package com.yorha.cnc.zonechat;

import com.yorha.common.actor.ZoneChatService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.utils.script.ScriptMgr;
import com.yorha.proto.SsZoneChat;

import java.time.Duration;
import java.time.Instant;

/**
 * zone 处理idip发来的请求
 *
 * <AUTHOR>
 */
public class ZoneChatServiceImpl implements ZoneChatService {
    private final ZoneChatActor zoneChatActor;

    public ZoneChatServiceImpl(ZoneChatActor zoneActor) {
        this.zoneChatActor = zoneActor;
    }

    @Override
    public void handleFetchChatMsgAsk(SsZoneChat.FetchChatMsgAsk ask) {
        ActorMsgEnvelope currentEnvelope = zoneChatActor.getCurrentEnvelope();
        zoneChatActor.getChatEntity().queryChatMsgs(ask.getFromId(), ask.getToId(), ask.getShieldListList(), currentEnvelope);
    }

    @Override
    public void handleSendChatMsgAsk(SsZoneChat.SendChatMsgAsk ask) {
        ActorMsgEnvelope currentEnvelope = zoneChatActor.getCurrentEnvelope();
        zoneChatActor.getChatEntity().chatRequest(ask.getChatMessage(), currentEnvelope);
    }

    @Override
    public void handleIdIpSendChatMsgAsk(SsZoneChat.IdIpSendChatMsgAsk ask) {
        ZoneChatEntity chatEntity = zoneChatActor.getChatEntity();
        chatEntity.loopSendZoneChat(ask.getLoopIntervalMiniute(), ask.getLoopTimes(), ask.getChatMessage());
        zoneChatActor.answer(SsZoneChat.IdIpSendChatMsgAns.getDefaultInstance());
    }

    /**
     * 处理来自IDIP的身份执行脚本消息请求。
     *
     * @param ask 包含执行脚本请求信息的对象，其中包含需要执行的Groovy脚本内容
     */
    @Override
    public void handleIdIpExecScriptMsgAsk(SsZoneChat.IdIpExecScriptMsgAsk ask) {
        Instant startTime = Instant.now();
        String result = ScriptMgr.getInstance().executeByGroovy(ask.getScript());
        Instant endTime = Instant.now();
        long interval = Duration.between(startTime, endTime).toMillis();
        SsZoneChat.IdIpExecScriptMsgAns.Builder ans = SsZoneChat.IdIpExecScriptMsgAns.newBuilder();
        ans.setResult(result);
        ans.setInterval(interval);
        zoneChatActor.answer(ans.build());
    }


}

package com.yorha.cnc.zonechat;

import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.chat.MegaGroupChatEntity;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsZoneChat;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 全服聊天manager
 *
 * <AUTHOR>
 */

public class ZoneChatEntity extends MegaGroupChatEntity {
    private static final Logger LOGGER = LogManager.getLogger(ZoneChatEntity.class);

    private final ZoneChatActor zoneActor;

    public ZoneChatEntity(ZoneChatActor actor) {
        super(actor);
        this.zoneActor = actor;
        // 初始化热数据，全服聊天直接初始化热数据
        this.loadChatMsgCacheFromDb();
    }

    @Override
    public CommonEnum.ChatChannel chatChannel() {
        return CommonEnum.ChatChannel.CC_SERVER;
    }

    @Override
    public String getChannelId() {
        return this.actor.getZoneIdStr();
    }

    @Override
    public String getIdFactoryKey() {
        return DbUtil.idFactoryTableKeyZoneChat + this.getChannelId();
    }

    @Override
    protected void broadcastMessage(CommonMsg.ChatMessage message) {
        // 如果是@消息，拉起被@的玩家设置firstAtIndex
        if (message.getType() == CommonEnum.MessageType.MT_AT) {
            this.handleAtMsg(message);
        }
        // 通知在线先不过滤被屏蔽了发送人的玩家，由客户端拦截
        BroadcastHelper.toCsOnlinePlayerInZone(actor.getZoneId(), MsgType.PLAYER_BORADCASTCHATMESSAGE_NTF,
                ChatHelper.formBroadCastChatNtf(message, this.getChannelId(), this.chatChannel()));
    }

    @Override
    protected void answerChatRequest(@Nullable ActorMsgEnvelope context, CommonMsg.ChatMessage message, Throwable throwable) {
        if (context == null) {
            return;
        }
        if (throwable != null) {
            actor.answerWithException(context, (Exception) throwable);
            return;
        }
        actor.answerWithContext(context, SsZoneChat.SendChatMsgAns.newBuilder().setMessageId(message.getMessageId()).build());
    }

    @Override
    public void queryChatMsgs(long fromId, long toId, List<Long> shieldList, ActorMsgEnvelope context) {
        if (fromId == 0) {
            fromId = this.getLastMsgId();
        }
        final List<CommonMsg.ChatMessage> chatMessages = ChatHelper.queryChatMsgFromCache(fromId, toId, shieldList, this.chatMsgCache);
        SsZoneChat.FetchChatMsgAns.Builder builder = SsZoneChat.FetchChatMsgAns.newBuilder();
        builder.addAllChatMsgs(chatMessages);
        actor.answerWithContext(context, builder.build());
    }

    private void loadChatMsgCacheFromDb() {
        final long fromId = this.getLastMsgId();
        final long toId = Math.max(0, this.getLastMsgId() - this.chatMsgCache.cacheCapacity() + 1);
        final List<CommonMsg.ChatMessage> chatMessages =
                ChatHelper.queryChatMsgSync(this.actor, this.chatChannel().getNumber(), this.actor.getZoneIdStr(), fromId, toId, Collections.emptySet());
        for (final CommonMsg.ChatMessage chatMessage : chatMessages) {
            this.chatMsgCache.putData(chatMessage.getMessageId(), chatMessage);
        }
    }

    private final Map<Integer, ActorTimer> actorTimerMap = new HashMap<>();
    private final Map<Integer, Integer> actorTimerLoopTimes = new HashMap<>();
    private Integer actorTimerId = 0;

    public void loopSendZoneChat(final int intervalMinute, final int loopTimes, final CommonMsg.ChatMessage chatMessage) {
        final int curTimerId = actorTimerId;
        LOGGER.info("loopSendZoneChat curTimerId={}, chatMessage={}", curTimerId, chatMessage);
        final Runnable task = () -> {
            chatRequest(chatMessage, null);
            this.actorTimerLoopTimes.put(curTimerId, this.actorTimerLoopTimes.getOrDefault(curTimerId, 0) + 1);
            LOGGER.info("loopSendZoneChat timerId={}, loopTimes={}, curLoopTimes={}", curTimerId, loopTimes, this.actorTimerLoopTimes.get(curTimerId));
            if (this.actorTimerLoopTimes.get(curTimerId) >= loopTimes) {
                LOGGER.info("loopSendZoneChat timerId={} reach loopTimes={}, cancel", curTimerId, loopTimes);
                this.actorTimerMap.get(curTimerId).cancel();
            }
        };

        this.addRepeatTimer("IdIpZoneChat " + curTimerId, TimerReasonType.IDIP_ZONE_CHAT, task, TimeUnit.MINUTES.toMillis(intervalMinute));
    }

    public void addRepeatTimer(final String prefix, final TimerReasonType reasonType, final Runnable task, final long intervalMs) {
        final ActorTimer actorTimer = zoneActor.addRepeatTimer(prefix, reasonType, task, 0L, intervalMs, TimeUnit.MILLISECONDS);
        actorTimerMap.put(actorTimerId, actorTimer);
        actorTimerId++;
    }
}

package com.yorha.cnc.zonechat;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.IActorWithTimer;
import com.yorha.common.actor.ZoneChatService;
import com.yorha.common.actor.ZoneChatServices;
import com.yorha.common.actor.node.IZoneActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import javax.annotation.Nullable;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ZoneChatActor extends GameActorWithCall implements ZoneChatServices, QlogServerFlowInterface, IZoneActor, IActorWithTimer {
    private static final Logger LOGGER = LogManager.getLogger(ZoneChatActor.class);

    private final ZoneChatService zoneChatService = new ZoneChatServiceImpl(this);
    private ZoneChatEntity chatEntity;
    private boolean isOk;

    public ZoneChatActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.isOk = false;
    }

    @Override
    @Nullable
    public ActorTimer addTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(String.valueOf(prefix), timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    @Nullable
    public ActorTimer addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Nullable
    public ActorTimer addRepeatTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, false);
    }

    @Nullable
    public ActorTimer addFixRepeatTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, true);
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    public boolean init() {
        LOGGER.info("gemini_system initZone {}", this);
        if (this.isOk) {
            LOGGER.info("initZone skip! already init ok! {}", this);
            return true;
        }
        try {
            // 初始化聊天
            chatEntity = new ZoneChatEntity(this);
            scheduleSystemMessage();
            this.isOk = true;
            return true;
        } catch (Exception e) {
            WechatLog.error(e);
        }
        return false;
    }

    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
    }

    private void scheduleSystemMessage() {
        final long SECONDS_IN_A_DAY = 24 * 60 * 60;
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        List<IntPairType> timeInterval = constTemplate.getEverydayTimeChatMsgId();
        for (IntPairType pairs : timeInterval) {
            long sendTime = pairs.getKey();
            int msgId = pairs.getValue();

            long nowOfSeconds = SystemClock.nowOfSeconds() % SECONDS_IN_A_DAY;
            long delayTime;

            if (sendTime < nowOfSeconds) {
                delayTime = sendTime + SECONDS_IN_A_DAY - nowOfSeconds;
            } else {
                delayTime = sendTime - nowOfSeconds;
            }

            this.addFixRepeatTimer(getZoneId() + "-" + sendTime + "-" + msgId, TimerReasonType.SCHEDULE_SYSTEM_MESSAGE,
                    () -> {
                        if (!ZoneContext.isServerOpen()) {
                            LOGGER.info("ZoneActor scheduleSystemMessage zone not open cancel {}", msgId);
                            return;
                        }
                        chatEntity.chatRequest(this.buildSystemMessage(msgId).build(), null);
                    },
                    delayTime, SECONDS_IN_A_DAY, TimeUnit.SECONDS);
        }
    }

    private CommonMsg.ChatMessage.Builder buildSystemMessage(int msgId) {
        CommonMsg.ChatMessage.Builder messageBuilder = CommonMsg.ChatMessage.newBuilder();
        CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
        messageData.setTemplateId(msgId);
        messageData.build();
        messageBuilder.setChatTimestamp(SystemClock.now())
                .setMessageData(messageData)
                .setType(CommonEnum.MessageType.MT_SYSTEM);
        return messageBuilder;
    }

    public ZoneChatEntity getChatEntity() {
        return chatEntity;
    }

    public QlogServerFlowInterface getQlogHead() {
        return this;
    }

    @Override
    public String getServerType() {
        return "";
    }

    @Override
    public int getIZoneAreaID() {
        return this.getZoneId();
    }

    @Override
    public String getServerOpenTime() {
        return TimeUtils.timeStampMs2String(ClusterConfigUtils.getZoneConfig(getZoneId()).getLongItem("open_zone_time"));
    }

    @Override
    public long getServerOpenTimeFar() {
        return TimeUtils.ms2Second((SystemClock.now() - ClusterConfigUtils.getZoneConfig(getZoneId()).getLongItem("open_zone_time")));
    }

    @Override
    public int getServerOnline() {
        return 0;
    }

    @Override
    public String getServerCondition() {
        return "xxx";
    }

    @Override
    public int getServerMilestoneStage() {
        return 0;
    }

    @Override
    public String getAccountId() {
        return "server_" + ServerContext.getServerInfo().getWorldId();
    }

    @Override
    public ZoneChatActor ownerActor() {
        return this;
    }

    @Override
    public ZoneChatService getZoneChatService() {
        return zoneChatService;
    }
}

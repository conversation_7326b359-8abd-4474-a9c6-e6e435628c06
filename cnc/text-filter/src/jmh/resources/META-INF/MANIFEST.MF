Manifest-Version: 1.0
Main-Class: safe.TextFilterBenchmark
Class-Path: opentracing-api-0.33.0.jar bcprov-jdk15on-1.66.jar simplecli
 ent_hotspot-0.12.0.jar client-java-proto-10.0.0.jar bcprov-ext-jdk15on-
 1.66.jar jackson-databind-2.13.2.jar netty-handler-4.1.78.Final.jar gso
 n-2.8.6.jar byte-buddy-1.12.10.jar byte-buddy-agent-1.12.10.jar jopt-si
 mple-4.6.jar kotlin-stdlib-common-1.4.10.jar netty-codec-dns-4.1.78.Fin
 al.jar gson-fire-1.8.4.jar sundr-codegen-0.22.0.jar simpleclient_common
 -0.12.0.jar vertx-grpc-4.3.2.jar swagger-annotations-1.6.2.jar opentrac
 ing-noop-0.33.0.jar fastutil-8.5.8.jar caffeine-2.8.5.jar failureaccess
 -1.0.1.jar jetcd-api-0.7.3.jar netty-handler-proxy-4.1.78.Final.jar jwk
 s-rsa-0.20.0.jar client-java-api-10.0.0.jar stax-api-1.0-2.jar pull-par
 ser-2.jar javax.annotation-api-1.3.2.jar zstd-jni-1.5.2-5.jar jctools-c
 ore-3.3.0.jar commons-logging-1.2.jar checker-qual-3.12.0.jar resourcec
 ify-annotations-0.22.0.jar error_prone_annotations-2.11.0.jar slf4j-api
 -1.7.36.jar dom4j-2.1.3.jar simpleclient_pushgateway-0.12.0.jar jaxb-ap
 i-2.3.0.jar jaeger-core-1.6.0.jar jetcd-core-0.7.3.jar grpc-grpclb-1.48
 .0.jar vertx-core-4.3.2.jar okhttp-4.9.0.jar commons-codec-1.15.jar log
 4j-core-2.17.0.jar jetcd-grpc-0.7.3.jar netty-common-4.1.78.Final.jar j
 ackson-core-2.13.2.jar jaeger-tracerresolver-1.6.0.jar netty-resolver-4
 .1.78.Final.jar jmh-core-1.24.jar grpc-stub-1.48.0.jar tomcat-annotatio
 ns-api-8.5.46.jar qqwry-java-0.9.0.jar protobuf-java-util-3.21.1.jar gu
 ava-31.1-jre.jar opentracing-util-0.33.0.jar annotations-23.0.0.jar ref
 lections-0.9.11.jar jaxen-1.1.6.jar bsh-2.0b6.jar xsdlib-2013.6.1.jar o
 pentracing-tracerresolver-0.1.8.jar netty-codec-http-4.1.78.Final.jar f
 luent-hc-4.5.13.jar jackson-annotations-2.13.2.jar java-jwt-3.18.2.jar 
 simpleclient-0.12.0.jar jsr305-3.0.2.jar grpc-context-1.48.0.jar netty-
 transport-native-unix-common-4.1.78.Final.jar eddsa-0.3.0.jar reflectas
 m-1.11.9.jar okio-jvm-2.8.0.jar libthrift-0.14.1.jar log4j-jcl-2.17.0.j
 ar log4j-web-2.17.0.jar disruptor-3.4.4.jar simpleclient_tracer_otel-0.
 12.0.jar grpc-core-1.48.0.jar relaxngDatatype-20020414.jar commons-coll
 ections4-4.4.jar asm-8.0.1.jar xpp3-1.1.4c.jar perfmark-api-0.25.0.jar 
 objenesis-3.2.jar httpclient-4.5.13.jar freemarker-2.3.31.jar commons-i
 o-2.8.0.jar builder-annotations-0.22.0.jar netty-resolver-dns-4.1.78.Fi
 nal.jar commons-math3-3.2.jar joda-time-2.10.6.jar simpleclient_httpser
 ver-0.12.0.jar logging-interceptor-3.14.9.jar protobuf-java-3.21.1.jar 
 tomcat-embed-core-8.5.46.jar httpcore-4.4.13.jar javassist-3.21.0-GA.ja
 r failsafe-2.4.4.jar j2objc-annotations-1.3.jar netty-all-4.1.63.Final.
 jar simpleclient_tracer_otel_agent-0.12.0.jar log4j-slf4j-impl-2.17.0.j
 ar grpc-netty-1.48.0.jar netty-codec-http2-4.1.78.Final.jar jnats-2.16.
 0.jar bcpkix-jdk15on-1.66.jar netty-transport-4.1.78.Final.jar gamioo-r
 edis-1.0.18.jar snakeyaml-1.27.jar grpc-api-1.48.0.jar netty-codec-4.1.
 78.Final.jar netty-codec-socks-4.1.78.Final.jar grpc-protobuf-lite-1.48
 .0.jar netty-buffer-4.1.78.Final.jar client-java-10.0.0.jar commons-lan
 g3-3.11.jar log4j-api-2.17.0.jar proto-google-common-protos-2.9.0.jar j
 aeger-client-1.6.0.jar commons-compress-1.20.jar sundr-core-0.22.0.jar 
 listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar joda-con
 vert-2.2.1.jar grpc-protobuf-1.48.0.jar simpleclient_tracer_common-0.12
 .0.jar jaeger-thrift-1.6.0.jar groovy-all-2.4.21.jar jetcd-common-0.7.3
 .jar kotlin-stdlib-1.4.10.jar jose4j-0.7.2.jar mockito-core-4.6.1.jar


package com.yorha.cnc.textFilter;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.TextFilterService;
import com.yorha.common.actor.TextFilterServices;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.TextFilterUtils;
import com.yorha.common.utils.id.SnowflakeIdGenerator;
import com.yorha.common.utils.id.ZoneSnowflakeFactory;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import safe.ITextFilter;
import safe.TextFilter;
import safe.TssSdkAgent;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

public class TextFilterActor extends BaseGameActor implements TextFilterServices {
    private static final Logger LOGGER = LogManager.getLogger(TextFilterActor.class);
    private final TextFilterService textFilterService;
    private final ITextFilter textFilter;
    private final SnowflakeIdGenerator idGenerator;

    public TextFilterActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.textFilterService = new TextFilterServiceImpl(this);
        this.idGenerator = ZoneSnowflakeFactory.newInstance();

        if (TextFilterUtils.isTssSdk()) {
            this.textFilter = new TssSdkAgent(this);
        } else {
            this.textFilter = new TextFilter(this);
        }
    }

    @Nullable
    public ActorTimer addRepeatTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(getId(), timerReasonType, runnable, initialDelay, period, unit, false);
    }

    @Override
    public TextFilterService getTextFilterService() {
        return textFilterService;
    }

    public ITextFilter getTextFilter() {
        return this.textFilter;
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
        LOGGER.info("TextFilterActor handleActorDestroyMsg");
        this.textFilter.destroy();
    }

    public long nextId() {
        return this.idGenerator.nextId();
    }
}

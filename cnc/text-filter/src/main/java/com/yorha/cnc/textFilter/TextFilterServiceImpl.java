package com.yorha.cnc.textFilter;

import com.google.common.collect.Lists;
import com.yorha.common.actor.TextFilterService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsTextFilter;
import safe.TextFilter;

import java.util.ArrayList;

public class TextFilterServiceImpl implements TextFilterService {

    private final TextFilterActor actor;


    public TextFilterServiceImpl(TextFilterActor httpActor) {
        this.actor = httpActor;
    }

    @Override
    public void handleCheckTextAsk(SsTextFilter.CheckTextAsk ask) {
        final CommonEnum.UgcSceneId sceneId = ask.getSceneId();
        final String text = ask.getText();
        CommonMsg.TsssdkJudgeUserInfo userInfo = ask.hasUserInfo() ? ask.getUserInfo() : null;
        final ActorMsgEnvelope actorMsgEnvelope = this.actor.getCurrentEnvelope();
        this.actor.getTextFilter().checkText(sceneId.getNumber(), text, userInfo,
                (checkResult -> actor.answerWithContext(actorMsgEnvelope, SsTextFilter.CheckTextAns.newBuilder()
                        .setIsLegal(checkResult.isLegal())
                        .setFilteredText(checkResult.filteredText())
                        .build())),
                (e -> actor.answerWithException(actorMsgEnvelope, e)));
    }

    @Override
    public void handleBatchCheckTextAsk(SsTextFilter.BatchCheckTextAsk ask) {
        ArrayList<TextFilter.CheckDataItem> items = Lists.newArrayList();
        for (SsTextFilter.CheckTextAsk single : ask.getTextsList()) {
            items.add(new TextFilter.CheckDataItem(single.getSceneId().getNumber(), single.getText()));
        }
        CommonMsg.TsssdkJudgeUserInfo userInfo = ask.hasUserInfo() ? ask.getUserInfo() : null;
        final ActorMsgEnvelope actorMsgEnvelope = this.actor.getCurrentEnvelope();
        this.actor.getTextFilter().batchCheckText(items, userInfo,
                (checkResults -> {
                    SsTextFilter.BatchCheckTextAns.Builder ans = SsTextFilter.BatchCheckTextAns.newBuilder();
                    for (TextFilter.CheckResult res : checkResults) {
                        ans.addResults(SsTextFilter.CheckTextAns.newBuilder()
                                .setIsLegal(res.isLegal())
                                .setFilteredText(res.filteredText())
                                .build());
                    }
                    actor.answerWithContext(actorMsgEnvelope, ans.build());
                }),
                (e -> actor.answerWithException(actorMsgEnvelope, e))
        );
    }
}

package tsssdk.jni;

public class TssSdkAccessInfo {
    public int network_type;                                // bind to TssSdkNetworkType
    public int backend_env_type;                            // bind to TssSdkBackendEnvironmentType
    public String data_proxy_domain;
    public String busi_proxy_domain;
    public String test_data_proxy_domain;
    public String test_busi_proxy_domain;

    public enum TssSdkNetworkType {
        TSS_SDK_TENCENT_IDC_NETWORK(0),                         // Tencent IDC (Internet Data Center)
        TSS_SDK_NON_TENCENT_IDC_NETWORK(1);                     // Other network except Tencent IDC

        private int network_type = 0;

        private TssSdkNetworkType(int value) {
            network_type = value;
        }

        public int GetNetworkType() {
            return network_type;
        }
    }

    public enum TssSdkBackendEnvironmentType {
        TSS_SDK_UNDEFINED_ENVIRONMENT(0),                       // Do not use this parameter to set backend environment type
        TSS_SDK_TEST_ENVIRONMENT(101),
        TSS_SDK_PRODUCT_ENVIRONMENT(1001);

        private int backend_env_type = 0;

        private TssSdkBackendEnvironmentType(int value) {
            backend_env_type = value;
        }

        public int GetBackendEnvironmentType() {
            return backend_env_type;
        }
    }
}



package tsssdk.jni;

import java.util.Arrays;

public class TssSdkAccountId {
    public byte[] account;                                 // The account of user
    public int account_type;                               // The account type of user, bind to TssAccountType

    @Override
    public String toString() {
        return "TssSdkAccountId{" +
                "account=" + Arrays.toString(account) +
                ", account_type=" + account_type +
                '}';
    }

    public enum TssAccountType {
        TSSACCOUNT_TYPE_QQ(1),                                 // QQ
        TSSACCOUNT_TYPE_WECHAT(2),                             // WeChat openid
        TSSACCOUNT_TYPE_BAIDU(3),                              // Baidu account
        TSSACCOUNT_TYPE_QQ_OPENID(4),                          // QQ openid
        TSSACCOUNT_TYPE_QQ_FAKE(5),                            // fake QQ
        TSSACCOUNT_TYPE_MB_OPENID(6),
        TSSACCOUNT_TYPE_VISITOR(7),                            // Visitor account
        TSSACCOUNT_TYPE_GOPENID(8),                            // gopenid
        TSSACCOUNT_TYPE_UNDEFINED(9),                          // ambiguous account
        TSSACCOUNT_TYPE_MAIL_START(100),                       // The beginning of email-like account
        TSSACCOUNT_TYPE_GOOGLE(101),                           // Google account
        TSSACCOUNT_TYPE_MAIL_END(199),                         // The end of email-like account
        TSSACCOUNT_TYPE_INT_START(200),                        // The beginning of 32-bit integer account
        TSSACCOUNT_TYPE_4399(201),                             // 4399 account
        TSSACCOUNT_TYPE_GARENA(202),                           // garena account
        TSSACCOUNT_TYPE_INT_END(299),                          // The end of 32-bit integer account
        TSSACCOUNT_TYPE_INT64_START(300),                      // The beginning of 64-bit integer account
        TSSACCOUNT_TYPE_WEGAME(301),                           // WeGame account
        TSSACCOUNT_TYPE_STEAM(302),                            // Steam account
        TSSACCOUNT_TYPE_WEGAME_COMMON_ID(308),                 // WeGame common ID
        TSSACCOUNT_TYPE_INT64_END(399),                        // The end of 64-bit integer account
        TSSACCOUNT_TYPE_OTHER_START(1000),                     // The beginning of other account
        TSSACCOUNT_TYPE_FACEBOOK(1001),                        // facebook account
        TSSACCOUNT_TYPE_SUPERCELL(1002);                       // supercell account

        private int account_type = 1;

        private TssAccountType(int value) {
            account_type = value;
        }

        public int GetAccountType() {
            return account_type;
        }
    }

    ;
}

package tsssdk.jni;

import com.yorha.common.utils.NativeUtils;

import java.io.IOException;

public class TssSdk {
    public TssSdk() {
    }

    static {
        try {
            NativeUtils.loadLibrary("tss_sdk");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
//        System.loadLibrary("tss_sdk");
    }

    public native int Init(TssSdkInitInfo init_info);

    public native int InitWithDomain(TssSdkInitWithDomainInfo init_info);

    public native int LoadAndInit(TssSdkInitInfo init_info, TssSdkAccessInfo access_info);

    public native int LoadAndInitWithProc(TssSdkInitInfo init_info, TssSdkAccessInfo access_info);

    public native void UnLoad();

    public native int AntiInterfInit();

    public native int AntiAddUser(TssSdkAntiAddUserInfo add_user_info);

    public native int AntiTickUser(TssSdkAntiTickUserInfo tick_user_info);

    public native int AntiDelUser(TssSdkAntiDelUserInfo del_user_info);

    public native int AntiRecvDataFromClient(TssSdkAntiRecvDataInfo recv_pkg_info);

    public int AntiSendDataToClient(TssSdkAntiSendDataInfo send_pkg_info)
    {
        String account = new String(send_pkg_info.account_info.account_id.account);
        System.out.println("account: " + account);
        System.out.println("account_type: " + send_pkg_info.account_info.account_id.account_type);
        System.out.println("plat_id: " + send_pkg_info.account_info.plat_id);
        System.out.println("game_id: " + send_pkg_info.account_info.game_id);
        System.out.println("world_id: " + send_pkg_info.account_info.world_id);
        System.out.println("channel_id: " + send_pkg_info.account_info.channel_id);
        System.out.println("role_id: " + send_pkg_info.account_info.role_id);
        String reserve_buf = new String(send_pkg_info.account_info.reserve_buf);
        System.out.println("reserve_buf: " + reserve_buf);
        System.out.println("anti_data_len " + send_pkg_info.anti_data.length);
        System.out.println("user_ext_data_len " + send_pkg_info.user_ext_data.length);
        return 0;
    }

    // initialize Uic interface, need called only once
    public native int UicInterfInit();

    // Synchronization interface to judge the user's NAME input
    public native int UicJudgeUserInputName(TssSdkUicNameUserInputInfo input_info);

    // Asynchronous interface to judge the user's CHAT input
    public native int UicJudgeUserInputChat(TssSdkUicChatUserInputInfo input_info);

    // Return to the CHAT type to determine the results of a callback function in the asynchronous mode
    public int UicOnChatJudgeResult(TssSdkUicChatJudgeResultInfo result_info)
    {
        String account = new String(result_info.account_info.account_id.account);
        System.out.println("account: " + account);
        System.out.println("flag: " + result_info.msg_result_flag + ", dirty level: " + result_info.dirty_level);
        System.out.println("output msg: " + result_info.msg);
        System.out.println("callback data len: " + result_info.callback_data.length);
        return 0;
    }

    // initialize Uic interface only for picture , need called only once
    public native int UicPicInterfInit();

    // Asynchronous interface to judge the user's picture info
    public native int UicJudgeUserInputPicture(TssSdkUicPictureUserInputInfo input_info);

    // Return to the picture type to determine the results of a callback function in the asynchronous mode
    public int UicOnPictureJudgeResult(TssSdkUicPictureJudgeResultInfo result_info)
    {
        String account = new String(result_info.account_info.account_id.account);
        System.out.println("account: " + account);
        System.out.println("lable: " + result_info.lable);
        System.out.println("err_code: " + result_info.error_code + ", error_msg: " + result_info.error_msg);
        System.out.println("check_desc: " + result_info.check_desc);
        System.out.println("callback data len: " + result_info.callback_data.length);
        return 0;
    }

    // initialize Uic interface only for text , need called only once
    public native int UicTextInterfInit();

    // Asynchronous interface to judge the user's text info
    public native int UicJudgeUserInputText(TssSdkUicTextUserInputInfo input_info);

    // Return to the text type to determine the results of a callback function in the asynchronous mode
    public int UicOnTextJudgeResult(TssSdkUicTextJudgeResultInfo result_info)
    {
        return 0;
    }

    public native int TlogInterfInit();

    public native int SendTlog(TssSdkTlogInfo tlog_info);

    //
    public native int LightFeatureInterfInit();

    public native int GetLightFeature(TssSdkLightFeatureUserInfo user_info, TssSdkLightFeatureData feature_data);

    public native int AceLicenseInterfInit();

    public native int GetAceLicense(TssSdkAceLicenseInfo license_info);

    public native void proc();
}


package tsssdk.jni;

import java.util.Arrays;

public class TssSdkUicTextJudgeResultInfo {
    public TssSdkAccountInfo account_info = new TssSdkAccountInfo();
    public int err_code;
    public int lable;
    public int check_result;
    public String err_msg;
    public String check_desc;
    public String filtered_text;
    public byte[] callback_data;

    @Override
    public String toString() {
        return "TssSdkUicTextJudgeResultInfo{" +
                "account_info=" + account_info +
                ", err_code=" + err_code +
                ", lable=" + lable +
                ", check_result=" + check_result +
                ", err_msg='" + err_msg + '\'' +
                ", check_desc='" + check_desc + '\'' +
                ", filtered_text='" + filtered_text + '\'' +
                ", callback_data=" + Arrays.toString(callback_data) +
                '}';
    }
}
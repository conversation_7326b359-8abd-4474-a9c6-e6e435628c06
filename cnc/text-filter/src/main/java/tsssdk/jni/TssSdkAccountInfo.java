package tsssdk.jni;

import java.util.Arrays;

public class TssSdkAccountInfo {
    public TssSdkAccountId account_id = new TssSdkAccountId();
    public int plat_id;                                   // bind to TssAccountPlatId
    public int game_id;
    public int world_id;
    public int channel_id;                                // can set to 0
    public long role_id;
    public byte[] reserve_buf;                            // not used

    @Override
    public String toString() {
        return "TssSdkAccountInfo{" +
                "account_id=" + account_id +
                ", plat_id=" + plat_id +
                ", game_id=" + game_id +
                ", world_id=" + world_id +
                ", channel_id=" + channel_id +
                ", role_id=" + role_id +
                ", reserve_buf=" + Arrays.toString(reserve_buf) +
                '}';
    }

    enum TssAccountPlatId {
        TSSPLAT_ID_IOS(0),                                     // iOS
        TSSPLAT_ID_ANDROID(1),                                 // Android
        TSSPLAT_ID_RESERVE(2),                                 // reserved
        TSSPLAT_ID_PC_CLIENT(3),                               // PC client
        TSSPLAT_ID_MICRO_WEB(4),                               // Micro web
        TSSPLAT_ID_MICRO_CLIENT(5),                            // Micro client
        TSSPLAT_ID_SWITCH(6),                                  // Switch client
        TSSPLAT_ID_PS_CLIENT(7),                               // PS client
        TSSPLAT_ID_XBOX_CLIENT(8),                             // XBOX client
        TSSPLAT_ID_UNKNOWN(9);

        private int plat_id = 0;

        private TssAccountPlatId(int value) {
            plat_id = value;
        }

        public int GetPlatId() {
            return plat_id;
        }
    }
}

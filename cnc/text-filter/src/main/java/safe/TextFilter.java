package safe;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.yorha.cnc.textFilter.TextFilterActor;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.PlatformAsyncClient;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.ThirdPartyErrLogLimiter;
import com.yorha.proto.CommonMsg;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.pool.PoolStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

import javax.crypto.Mac;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 文本过滤处理
 * <p>
 * 通过http访问腾讯内容安全检测服务 <a href="https://ace.qq.com/#/doc-center/bb276d7a22244a3f445ecf52847aaf336a0442ed">接口文档</a>
 */
public class TextFilter implements ITextFilter {
    private static final Logger LOGGER = LogManager.getLogger(TextFilter.class);

    final TextFilterActor actor;
    final PlatformAsyncClient httpClient;
    final Supplier<String> secretId;
    final Supplier<String> secretKey;
    final Supplier<String> domain;

    public TextFilter(TextFilterActor actor) {
        this(
                actor,
                () -> ClusterConfigUtils.getWorldConfig().getStringItem("safe_check_domain"),
                () -> ClusterConfigUtils.getWorldConfig().getStringItem("safe_check_secret_id"),
                () -> ClusterConfigUtils.getWorldConfig().getStringItem("safe_check_secret_key")
        );
    }

    public TextFilter(TextFilterActor actor, Supplier<String> safeCheckDomain, Supplier<String> secretId, Supplier<String> secretKey) {
        LOGGER.info("init TextFilter domain={}", safeCheckDomain);
        this.actor = actor;
        // 文档中建议设置的超时时间为1s，但是只设置1s容易网络超时
        this.httpClient = new PlatformAsyncClient(3000, 2);
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.domain = safeCheckDomain;
        this.actor.addRepeatTimer(TimerReasonType.TEXT_FILTER_CLEAR_CONNECTION, () -> {
            clearConnection(15);
        }, 15, 15, TimeUnit.SECONDS);
        this.actor.addRepeatTimer(TimerReasonType.TEXT_FILTER_CONNECTION_LOG, () -> {
            LOGGER.info("TextFilterActor poolStats={}", getPoolStats());
        }, 60, 60, TimeUnit.SECONDS);
        LOGGER.info("TextFilter start secretId={}, secretKey={}, domain={}", secretId, secretKey, safeCheckDomain);
    }

    // 海外公网接入地址
    static final String OVERSEA_PUBLIC_DOMAIN_AMERICA = "http://gateway-na.iegcom.com";
    // 文本校验地址后缀
    static final String BATCH_TEXT_URL = "/check/batch_text";

    static final DateTimeFormatter GMT_DATE_FORMATTER = DateTimeFormatter
            .ofPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US)
            .withZone(TimeZone.getTimeZone("GMT").toZoneId());

    public void clearConnection(final int idleSecondLimit) {
        this.httpClient.clearConnection(idleSecondLimit);
    }

    public PoolStats getPoolStats() {
        return this.httpClient.getPoolStats();
    }

    @Override
    public void destroy() {
        this.httpClient.destroy();
    }

    @Override
    public void checkText(int sceneId, String text, @Nullable CommonMsg.TsssdkJudgeUserInfo userInfo,
                          Consumer<CheckResult> onCheckSuccess, Consumer<Exception> onException) {
        asyncCheckText(new CheckDataItem(sceneId, text), onCheckSuccess, onException);
    }

    @Override
    public void batchCheckText(List<CheckDataItem> items, @Nullable CommonMsg.TsssdkJudgeUserInfo userInfo,
                               Consumer<List<CheckResult>> onCheckSuccess, Consumer<Exception> onException) {
        asyncBatchCheckText(items, onCheckSuccess, onException);
    }

    public void asyncBatchCheckText(List<CheckDataItem> items, Consumer<List<CheckResult>> onCheckSuccess, Consumer<Exception> onException) {
        Consumer<JsonArray> onSuccess = ret -> {
            if (ret == null) {
                throw new GeminiException(ErrorCode.TEXT_FILTER_NETWORK_ERROR, "text filter failed.");
            }
            onCheckSuccess.accept(parseRetData(ret));
        };
        asyncCheckText0(items, onSuccess, onException);
    }

    public void asyncCheckText(CheckDataItem checkItem, Consumer<CheckResult> onCheckSuccess, Consumer<Exception> onException) {
        Consumer<JsonArray> onSuccess = ret -> {
            if (ret == null) {
                throw new GeminiException(ErrorCode.TEXT_FILTER_NETWORK_ERROR, "text filter failed.");
            }
            onCheckSuccess.accept(parseRetData(ret).getFirst());
        };
        asyncCheckText0(ImmutableList.of(checkItem), onSuccess, onException);
    }

    private List<CheckResult> parseRetData(JsonArray retData) {
        ArrayList<CheckResult> results = Lists.newArrayList();
        for (JsonElement element : retData) {
            JsonObject r = element.getAsJsonObject().getAsJsonObject("text_check_result");
            results.add(
                    new CheckResult(
                            r.get("check_result").getAsInt() == 0,
                            r.get("filtered_text").getAsString()
                    )
            );
        }
        return results;
    }

    void asyncCheckText0(List<CheckDataItem> checkItems, Consumer<JsonArray> onCheckSuccess, Consumer<Exception> onException) {
        String secretId = this.secretId.get();
        String secretKey = this.secretKey.get();

        String gmtTime = getGmtTime();
        ImmutableMap<String, Object> entity = ImmutableMap.of(
                "tasks", checkItems
        );
        String payload = JsonUtils.toJsonString(entity);
        String signingString = getSigningString(secretId, secretKey, gmtTime, payload);
        ImmutableMap<String, String> headers = ImmutableMap.of(
                "X-HMAC-SIGNATURE", signingString,
                "X-HMAC-SECRET-ID", secretId,
                "X-HMAC-ALGORITHM", "hmac-sha256",
                "Date", gmtTime
        );
        final long t0 = System.currentTimeMillis();
        final HttpPost httpPost = httpClient.formPostRequest(domain.get() + BATCH_TEXT_URL, payload, headers);
        httpClient.execute2json(httpPost, ret -> {
            long costMs = System.currentTimeMillis() - t0;
            LOGGER.info("textFilter check items={} ret={} cost={}ms", checkItems, ret, costMs);

            if (costMs >= MonitorConstant.TEXT_FILTER_OVER_TIME) {
                MonitorUnit.TEXT_FILTER_COST_OVER_TIME_COUNTER.labels(ServerContext.getBusId()).inc();
            }
            MonitorUnit.TEXT_FILTER_COUNTER.labels(ServerContext.getBusId()).inc();

            if (ret == null || ret.get("err_code").getAsInt() != 0) {
                ThirdPartyErrLogLimiter.TEXT_FILTER_OUT_OF_TIME.tryError("TextFilter http request ret:{}", ret);
                MonitorUnit.TEXT_FILTER_FAIL_TOTAL.labels(ServerContext.getBusId()).inc();
                onCheckSuccess.accept(null);
                return;
            }
            onCheckSuccess.accept(ret.getAsJsonArray("data"));
        }, onException);
    }

    static String getSigningString(String secretId, String secretKey, String gmtDate, String payloadStr) {
        String canonicalQueryString = "";
        String httpMethod = "POST";
        String payloadSign = getPayloadSign(payloadStr);
        String signatureString = httpMethod + "\n" + TextFilter.BATCH_TEXT_URL + "\n" + canonicalQueryString + "\n" + secretId + "\n"
                + gmtDate + "\n" + payloadSign + "\n";

        Mac hmac = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, secretKey.getBytes(StandardCharsets.UTF_8));
        byte[] hashedBytes = hmac.doFinal(signatureString.getBytes(StandardCharsets.UTF_8));
        byte[] base64Bytes = Base64.getEncoder().encode(hashedBytes);
        return new String(base64Bytes);
    }

    static String getPayloadSign(String payloadStr) {
        return DatatypeConverter.printHexBinary(DigestUtils.sha256(payloadStr)).toLowerCase();
    }

    static String getGmtTime() {
        // 这里要用native时间，不然随便调一下时间这个接口就不能服务了
        return GMT_DATE_FORMATTER.format(Instant.ofEpochMilli(SystemClock.nowNative()));
    }
}

package safe;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import com.yorha.cnc.textFilter.TextFilterActor;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;
import tsssdk.jni.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
public class TssSdkAgent extends TssSdk implements ITextFilter {
    private static final Logger LOGGER = LogManager.getLogger(TssSdkAgent.class);

    private final ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;
    static final long PROC_SCHEDULE_PERIOD_MS = 5;

    private final Cache<Long, TssJob> tssJobCache;
    private final Supplier<Integer> fetchGameId;

    final TextFilterActor actor;

    public TssSdkAgent(TextFilterActor actor) {
        this(
                actor,
                () -> ClusterConfigUtils.getWorldConfig().getIntItem("tss_game_id"),
                ClusterConfigUtils.getWorldConfig().getStringItem("tss_auth_token"),
                ClusterConfigUtils.getWorldConfig().getStringItem("tss_env"),
                ClusterConfigUtils.getWorldConfig().getStringItem("tss_data_proxy_domain"),
                ClusterConfigUtils.getWorldConfig().getStringItem("tss_test_data_proxy_domain"),
                ClusterConfigUtils.getWorldConfig().getStringItem("tss_busi_proxy_domain"),
                ClusterConfigUtils.getWorldConfig().getStringItem("tss_test_busi_proxy_domain"),
                ClusterConfigUtils.getWorldConfig().getIntItem("tss_network_type")
        );
    }

    public TssSdkAgent(TextFilterActor actor, Supplier<Integer> fetchGameId, String authToken, String env,
                       String dataProxyDomain, String testDataProxyDomain,
                       String busiProxyDomain, String testBusiProxyDomain, int tssNetworkType) {
        this.actor = actor;
        this.fetchGameId = fetchGameId;
        int gameId = fetchGameId.get();
        TssSdkInitInfo initInfo = new TssSdkInitInfo();
        initInfo.game_id = gameId;
        initInfo.auth_token = authToken;
        TssSdkAccessInfo accessInfo = new TssSdkAccessInfo();
        accessInfo.network_type = (
                tssNetworkType == 0 ?
                        TssSdkAccessInfo.TssSdkNetworkType.TSS_SDK_TENCENT_IDC_NETWORK
                        : TssSdkAccessInfo.TssSdkNetworkType.TSS_SDK_NON_TENCENT_IDC_NETWORK
        ).GetNetworkType();
        accessInfo.backend_env_type = (
                "prod".equals(env) ?
                        TssSdkAccessInfo.TssSdkBackendEnvironmentType.TSS_SDK_PRODUCT_ENVIRONMENT
                        : TssSdkAccessInfo.TssSdkBackendEnvironmentType.TSS_SDK_TEST_ENVIRONMENT
        ).GetBackendEnvironmentType();
        accessInfo.data_proxy_domain = dataProxyDomain;
        accessInfo.test_data_proxy_domain = testDataProxyDomain;
        accessInfo.busi_proxy_domain = busiProxyDomain;
        accessInfo.test_busi_proxy_domain = testBusiProxyDomain;
        // 这个接口是不会自己起线程，需要自己起线程驱动proc的
        // 但是它接口叫withProc，真有意思啊
        int ret = LoadAndInitWithProc(initInfo, accessInfo);
        if (ret != 0) {
            WechatLog.error("tsssdk init failed, ret={} params:[{}]", ret, JsonUtils.toJsonString(initInfo));
        }
        int textInitRet = UicTextInterfInit();
        if (textInitRet != 0) {
            WechatLog.error("tsssdk init text failed, ret={} params:[{}]", ret, JsonUtils.toJsonString(initInfo));
        }
        LOGGER.info("tsssdk init ok. params:[{}]", JsonUtils.toJsonString(initInfo));
        tssJobCache = Caffeine.newBuilder()
                .scheduler(SystemScheduleMgr.getInstance().getScheduler())
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .maximumSize(100_000)
                .removalListener((k, v, cause) -> LOGGER.debug("tsssdk cache remove key={} reason={}", k, cause)).build();
        // 直接起一个线程跑 proc
        this.scheduledThreadPoolExecutor = ConcurrentHelper.newSingleThreadScheduledExecutor("tss-proc-scheduler");
        this.scheduledThreadPoolExecutor.scheduleWithFixedDelay(this::proc, PROC_SCHEDULE_PERIOD_MS, PROC_SCHEDULE_PERIOD_MS, TimeUnit.MILLISECONDS);
    }

    @Override
    public void destroy() {
        this.scheduledThreadPoolExecutor.shutdown();
        try {
            this.scheduledThreadPoolExecutor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.error("TssSdkAgent awaitTermination", e);
        }
    }

    @Override
    public void checkText(
            int sceneId,
            String text,
            @Nullable CommonMsg.TsssdkJudgeUserInfo userInfo,
            Consumer<CheckResult> onCheckSuccess,
            Consumer<Exception> onException
    ) {
        TssSdkUicTextUserInputInfo inputInfo = buildInputInfo(fetchGameId.get(), sceneId, text, userInfo);
        new SingleJob(inputInfo, onCheckSuccess, onException).judge();
    }

    @Override
    public void batchCheckText(
            List<CheckDataItem> items,
            @Nullable CommonMsg.TsssdkJudgeUserInfo userInfo,
            Consumer<List<CheckResult>> onCheckSuccess,
            Consumer<Exception> onException
    ) {
        List<TssSdkUicTextUserInputInfo> inputList = Lists.newArrayList();
        for (CheckDataItem item : items) {
            TssSdkUicTextUserInputInfo info = buildInputInfo(fetchGameId.get(), item.sceneId(), item.text(), userInfo);
            inputList.add(info);
        }
        new BatchJob(inputList, onCheckSuccess, onException).judge();
    }

    private TssSdkUicTextUserInputInfo buildInputInfo(int gameId, int sceneId, String text, CommonMsg.TsssdkJudgeUserInfo userInfo) {
        TssSdkUicTextUserInputInfo inputInfo = new TssSdkUicTextUserInputInfo();
        fillBaseTextInfo(gameId, inputInfo, userInfo);
        inputInfo.scene_id = sceneId;
        inputInfo.text = text;
        inputInfo.callback_data = Longs.toByteArray(spawnJobId());
        return inputInfo;
    }

    long spawnJobId() {
        return actor.nextId();
    }

    @Override
    public int UicOnTextJudgeResult(TssSdkUicTextJudgeResultInfo resultInfo) {
        LOGGER.info("tsssdk judgeTextResult: {}", resultInfo);
        long jobId = Longs.fromByteArray(resultInfo.callback_data);
        TssJob job = tssJobCache.getIfPresent(jobId);
        if (job != null) {
            job.complete(resultInfo);
        } else {
            LOGGER.error("tsssdk UicOnTextJudgeResult can not found job:{}", resultInfo);
        }
        return 0;
    }

    private static void fillBaseTextInfo(int gameId, TssSdkUicTextUserInputInfo info, @javax.annotation.Nullable CommonMsg.TsssdkJudgeUserInfo userInfo) {
        info.account_info.account_id.account_type = TssSdkAccountId.TssAccountType.TSSACCOUNT_TYPE_GOPENID.GetAccountType();

        if (userInfo == null) {
            // 试了一下，只要填充account_info.account_id.account就可以收到结果
            info.account_info.account_id.account = "12345".getBytes();
            return;
        }

        info.account_info.account_id.account = userInfo.getAccountId().getBytes();

        // 以下内容不填应该也可以，sdk黑盒，不清楚用这些字段干了些啥
        info.account_info.plat_id = userInfo.getPlatId();
        info.account_info.game_id = gameId;
        info.account_info.world_id = userInfo.getZoneId();
        info.account_info.channel_id = 0;
        info.account_info.role_id = userInfo.getRoleId();
        info.role_level = userInfo.getRoleLevel();
        info.role_name = userInfo.getRoleName();
        info.extra_data = "";
    }

    interface TssJob {
        long jobStartMs();

        void complete(TssSdkUicTextJudgeResultInfo resultInfo);
    }

    class SingleJob implements TssJob {

        private final TssSdkUicTextUserInputInfo info;
        private final long jobId;
        private long jobStartMs;
        private final Consumer<CheckResult> onCheckSuccess;
        private final Consumer<Exception> onException;

        SingleJob(TssSdkUicTextUserInputInfo info, Consumer<CheckResult> onCheckSuccess, Consumer<Exception> onException) {
            this.info = info;
            this.jobId = Longs.fromByteArray(info.callback_data);
            this.onCheckSuccess = onCheckSuccess;
            this.onException = onException;
        }

        public void judge() {
            tssJobCache.put(jobId, this);
            jobStartMs = SystemClock.nowNative();
            try {
                int ret = UicJudgeUserInputText(info);
                if (ret != 0) {
                    tssJobCache.invalidate(jobId);
                    onException.accept(new GeminiException("tsssdk judge failed: {}", ret));
                }
            } catch (Exception e) {
                tssJobCache.invalidate(jobId);
                onException.accept(new GeminiException("tsssdk judge failed: ", e));
            }
        }

        @Override
        public long jobStartMs() {
            return this.jobStartMs;
        }

        @Override
        public void complete(TssSdkUicTextJudgeResultInfo resultInfo) {
            tssJobCache.invalidate(jobId);
            if (resultInfo.err_code != 0) {
                onException.accept(new GeminiException("tsssdk job ret err: {}", resultInfo.err_code));
                return;
            }
            CheckResult checkResult = new CheckResult(resultInfo.check_result == 0, resultInfo.filtered_text);
            onCheckSuccess.accept(checkResult);
        }
    }

    class BatchJob {
        private final List<TssSdkUicTextUserInputInfo> inputInfoList;
        private final TssSdkUicTextJudgeResultInfo[] resultList;
        private final Consumer<List<CheckResult>> onCheckSuccess;
        private final Consumer<Exception> onException;

        BatchJob(List<TssSdkUicTextUserInputInfo> inputInfoList, Consumer<List<CheckResult>> onCheckSuccess, Consumer<Exception> onException) {
            this.inputInfoList = inputInfoList;
            this.resultList = new TssSdkUicTextJudgeResultInfo[inputInfoList.size()];
            this.onCheckSuccess = onCheckSuccess;
            this.onException = onException;
        }

        protected void judge() {
            for (int i = 0; i < inputInfoList.size(); i++) {
                TssSdkUicTextUserInputInfo info = inputInfoList.get(i);
                long jobId = Longs.fromByteArray(info.callback_data);
                final int finalIndex = i;
                final long startMs = SystemClock.nowNative();
                tssJobCache.put(jobId, new TssJob() {
                    @Override
                    public long jobStartMs() {
                        return startMs;
                    }

                    @Override
                    public void complete(TssSdkUicTextJudgeResultInfo resultInfo) {
                        if (Longs.fromByteArray(resultInfo.callback_data) == jobId) {
                            tssJobCache.invalidate(jobId);
                            resultList[finalIndex] = resultInfo;
                        } else {
                            WechatLog.error("this is weird.! cue damon.");
                        }
                        if (Arrays.stream(resultList).allMatch(Objects::nonNull)) {
                            onAllCompleted(Lists.newArrayList(resultList));
                        }
                    }
                });
                try {
                    int ret = UicJudgeUserInputText(info);
                    if (ret != 0) {
                        tssJobCache.invalidate(jobId);
                        onException.accept(new GeminiException("tsssdk judge failed: {}", ret));
                        return;
                    }
                } catch (Exception e) {
                    tssJobCache.invalidate(jobId);
                    onException.accept(new GeminiException("tsssdk judge failed: ", e));
                    return;
                }
            }
        }

        private void onAllCompleted(List<TssSdkUicTextJudgeResultInfo> resultList) {
            ArrayList<CheckResult> results = Lists.newArrayList();
            for (TssSdkUicTextJudgeResultInfo resultInfo : resultList) {
                if (resultInfo.err_code != 0) {
                    onException.accept(new GeminiException("tsssdk job ret err: {}", resultInfo.err_code));
                    return;
                }
                results.add(new CheckResult(resultInfo.check_result == 0, resultInfo.filtered_text));
            }
            onCheckSuccess.accept(results);
        }
    }

    public static void main(String[] args) throws InterruptedException {
        final String data_proxy_domain = "public-dataproxy.anticheatexpert.com";
        final String test_data_proxy_domain = "exp.public-dataproxy.anticheatexpert.com";
        final String busi_proxy_domain = "public-busiproxy.anticheatexpert.com";
        final String test_busi_proxy_domain = "exp.public-busiproxy.anticheatexpert.com";

        final int game_id = 520177630;
        final String auth_token = "DCDAFB77";
        TssSdkAgent sdk = new TssSdkAgent(
                null,
                () -> game_id, auth_token, "test",
                data_proxy_domain, test_data_proxy_domain, busi_proxy_domain, test_busi_proxy_domain, 1
        ) {
            @Override
            long spawnJobId() {
                return RandomUtils.nextLong(Long.MAX_VALUE);
            }
        };
        for (int i = 0; i < 3; i++) {
            Thread.startVirtualThread(() -> {
                sdk.checkText(101, "中国文本测试：方志敏，en test：fuck motherfucker bitch",
                        null,
                        (ret -> System.out.println("result=" + JsonUtils.toJsonString(ret))),
                        (ii -> {
                        })
                );
            });
        }
        Thread.startVirtualThread(() -> {
            sdk.batchCheckText(Lists.newArrayList(
                            new CheckDataItem(101, "mother"),
                            new CheckDataItem(101, "fucker"),
                            new CheckDataItem(101, "bitch")),
                    null,
                    (ret -> System.out.println("芜湖:" + JsonUtils.toJsonString(ret))),
                    (i -> {
                    })
            );
        });
        Thread.sleep(5000);
    }
}

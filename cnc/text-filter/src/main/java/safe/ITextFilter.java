package safe;

import com.google.gson.annotations.SerializedName;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.proto.CommonMsg;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface ITextFilter {
    void destroy();

    void checkText(int sceneId, String text, @Nullable CommonMsg.TsssdkJudgeUserInfo userInfo,
                   Consumer<CheckResult> onCheckSuccess, Consumer<Exception> onException);

    void batchCheckText(List<CheckDataItem> items, @Nullable CommonMsg.TsssdkJudgeUserInfo userInfo,
                        Consumer<List<CheckResult>> onCheckSuccess, Consumer<Exception> onException);

    record CheckResult(boolean isLegal, String filteredText) {
    }

    record CheckDataItem(@SerializedName("scene_id") int sceneId, String text) {

        @Override
        public @NotNull String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

}

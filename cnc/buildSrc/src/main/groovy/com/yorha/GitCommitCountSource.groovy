package com.yorha
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.provider.ValueSource
import org.gradle.api.provider.ValueSourceParameters
import org.gradle.api.tasks.Input
import org.gradle.process.ExecOperations

import javax.inject.Inject


abstract class GitCommitCountSource implements ValueSource<String, Params> {
    interface Params extends ValueSourceParameters {
        @Input
        DirectoryProperty getWorkDir()
    }
    @Inject
    abstract ExecOperations getExecOperations()


    @Override
    String obtain() {
        File workDir = parameters.workDir.get().asFile
        def output = new ByteArrayOutputStream()
        execOperations.exec {
            it.commandLine 'git', 'rev-list', '--count', 'HEAD'
            it.workingDir = workDir
            it.standardOutput = output
        }
        return output.toString().trim()
    }
}




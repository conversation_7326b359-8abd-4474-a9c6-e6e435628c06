package com.yorha

import org.gradle.api.Plugin
import org.gradle.api.Project

class GitInfoPlugin implements Plugin<Project> {
    void apply(Project project) {
        def gitInfo = project.extensions.findByType(GitInfoExtension)
        if (gitInfo == null) {
            def branchName = project.providers.of(GitBranchSource) {
                it.parameters.workDir = project.rootDir
            }
            def shortCommitHash = project.providers.of(GitShortCommitHashSource) {
                it.parameters.workDir = project.rootDir
            }
            def commitCount = project.providers.of(GitCommitCountSource) {
                it.parameters.workDir = project.rootDir
            }

            gitInfo = new GitInfoExtension(branchName, shortCommitHash, commitCount)
            project.extensions.add("gitInfo", gitInfo)
        }

        project.tasks.register('printGitInfo') {
            doLast {
                println "Git Info: ${gitInfo.version.get()}"
            }
        }
    }
}




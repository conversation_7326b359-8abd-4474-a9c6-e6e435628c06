package com.yorha

import org.gradle.api.file.DirectoryProperty
import org.gradle.api.provider.ValueSource
import org.gradle.api.provider.ValueSourceParameters
import org.gradle.api.tasks.Input
import org.gradle.process.ExecOperations

import javax.inject.Inject

abstract class GitBranchSource implements ValueSource<String, Params> {
    interface Params extends ValueSourceParameters {
        @Input
        DirectoryProperty getWorkDir()
    }

    @Inject
    abstract ExecOperations getExecOperations()

    @Override
    String obtain() {
        File workDir = parameters.workDir.get().asFile
        def output = new ByteArrayOutputStream()

        try {
            // 首先尝试获取分支名
            execOperations.exec {
                it.commandLine 'git', 'rev-parse', '--abbrev-ref', 'HEAD'
                it.workingDir = workDir
                it.standardOutput = output
                it.ignoreExitValue = true
            }

            def branch = output.toString().trim()

            // 如果返回 HEAD，说明处于 detached HEAD 状态
            if (branch == "HEAD" || branch.isEmpty()) {
                // 尝试获取关联的远程分支信息
                def remoteBranchOutput = new ByteArrayOutputStream()
                try {
                    execOperations.exec {
                        it.commandLine 'git', 'name-rev', '--name-only', 'HEAD'
                        it.workingDir = workDir
                        it.standardOutput = remoteBranchOutput
                        it.ignoreExitValue = true
                    }

                    def remoteBranch = remoteBranchOutput.toString().trim()
                    if (remoteBranch && !remoteBranch.contains("undefined")) {
                        // 提取分支名，例如从 "remotes/origin/dev" 提取 "dev"
                        if (remoteBranch.startsWith("remotes/origin/")) {
                            return remoteBranch.substring("remotes/origin/".length())
                        } else if (remoteBranch.startsWith("origin/")) {
                            return remoteBranch.substring("origin/".length())
                        }
                        return remoteBranch
                    }
                } catch (Exception ignored) {}

                // 如果无法获取远程分支信息，尝试获取提交哈希
                def commitOutput = new ByteArrayOutputStream()
                try {
                    execOperations.exec {
                        it.commandLine 'git', 'rev-parse', '--short', 'HEAD'
                        it.workingDir = workDir
                        it.standardOutput = commitOutput
                    }
                    return "detached-" + commitOutput.toString().trim()
                } catch (Exception ignored) {
                    return "detached-HEAD"
                }
            }

            return branch
        } catch (Exception e) {
            // 如果所有方法都失败，返回默认值
            return "unknown"
        }
    }
}

package com.yorha


import org.gradle.api.provider.Provider

class GitInfoExtension {
    private final Provider<String> version

    GitInfoExtension(Provider<String> branchName,
                     Provider<String> shortCommitHash,
                     Provider<String> commitCount) {

        this.version = branchName
                .zip(shortCommitHash) { br, hash ->
                    "$br-$hash"
                }
                .zip(commitCount) { brHash, count ->
                    def date = new Date().format("yyyyMMdd", TimeZone.getTimeZone("GMT+08:00"))
                    "${brHash}-$count-S${date}R1"
                } as Provider<String>

    }

    Provider<String> getVersion() {
        return version
    }
}

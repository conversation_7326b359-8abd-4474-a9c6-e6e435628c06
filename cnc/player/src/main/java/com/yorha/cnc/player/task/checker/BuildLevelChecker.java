package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildCreateEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 某个建筑升级达到多少级
 * param1: 建筑类型id
 * param2: 等级
 *
 * <AUTHOR>
 */
public class BuildLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerInnerBuildLevelUpEvent.class.getSimpleName(),
            PlayerInnerBuildCreateEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer buildType = taskParams.get(0);
        Integer level = taskParams.get(1);

        int maxLevel = entity.getInnerBuildRhComponent().getInnerBuildLevel(buildType);
        if (maxLevel != prop.getProcess()) {
            prop.setProcess(Math.min(level, maxLevel));
        }

        return prop.getProcess() >= level;
    }
}

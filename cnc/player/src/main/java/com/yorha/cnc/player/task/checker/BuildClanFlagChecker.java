package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.BuildClanFlagEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 建造联盟旗帜
 * param1: 个数
 * <AUTHOR>
 */
public class BuildClanFlagChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(BuildClanFlagChecker.class);

    public static List<String> attentionList = Lists.newArrayList(BuildClanFlagEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int countConfig = taskParams.get(0);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE:
                if (event instanceof BuildClanFlagEvent) {
                    prop.setProcess(Math.min(prop.getProcess() + 1, countConfig));
                    LOGGER.info("BuildClanFlagChecker updateProcess, trigger buildClanFlagEvent, playerId={}, add one time, process={}, config count={}", event.getPlayer().getPlayerId(), prop.getProcess(), countConfig);
                }
                break;
            default:
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }
}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerWeekRefreshEvent;
import com.yorha.cnc.player.event.task.PlayerHeroIntensiveEvent;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.qlog.json.item.QlogItemConfig;
import com.yorha.common.qlog.json.money.QlogMoneyConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.hero.HeroTemplateService;
import com.yorha.common.resource.resservice.player.VipResService;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerVipStore;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstVipTemplate;
import res.template.VipCommonTemplate;
import res.template.VipShopTemplate;

import java.util.*;

/**
 * 玩家身上的vip组件
 *
 * <AUTHOR>
 */

public class PlayerVipComponent extends PlayerComponent implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerVipComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerVipComponent::vipDailyRefresh);
        EntityEventHandlerHolder.register(PlayerWeekRefreshEvent.class, PlayerVipComponent::weeklyRefreshVipStoreInfo);
        EntityEventHandlerHolder.register(PlayerHeroIntensiveEvent.class, PlayerVipComponent::heroIntensive);
    }


    public PlayerVipComponent(PlayerEntity owner) {
        super(owner);
    }



    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            // 更新初始的VIP加成
            int vipLevel = getVipLevel();
            updateVipAdditions(vipLevel, vipLevel);
        }
    }


    // ------------------------------------- VIP 基础 -------------------------------- //

    /**
     * 1、刷新累计登录天数
     * 2、清空没有被领取的 vip 每日宝箱
     *
     * @param dayRefreshEvent 日刷事件
     */
    public static void vipDailyRefresh(PlayerDayRefreshEvent dayRefreshEvent) {
        PlayerEntity playerEntity = dayRefreshEvent.getPlayer();
        playerEntity.getVipComponent().refreshCumLoginDays();
        playerEntity.getVipComponent().clearVipDailyBox();
    }

    /**
     * 英雄觉醒时，如果玩家当前选择的是该英雄的奖励，则将玩家的选择清空
     *
     * @param intensiveEvent 觉醒事件
     */
    public static void heroIntensive(PlayerHeroIntensiveEvent intensiveEvent) {
        PlayerEntity playerEntity = intensiveEvent.getPlayer();
        playerEntity.getVipComponent().tryRemoveChosenHeroWhenIntensive(intensiveEvent.getHeroId());
    }

    /**
     * @return 返回玩家的vip等级
     */
    public int getVipLevel() {
        VipResService service = ResHolder.getResService(VipResService.class);
        return service.getLevelByExp(getVipExp());
    }

    /**
     * 增加vip经验，当vip等级变化时，会增删对应等级的vip加成
     *
     * @param exp vip经验
     */
    public void addVipExp(int exp, String action) {
        LOGGER.info("PlayerVipComponent addVipExp, exp={} reason={}", exp, action);
        if (exp <= 0) {
            LOGGER.error("exp {} should always bigger than 0", exp);
            return;
        }
        int beforeLevel = getVipLevel();
        int beforeExp = getVipExp();
        int afterExp = MathUtils.addExact(beforeExp, exp);
        if (afterExp - exp != beforeExp) {
            WechatLog.error("vip exp overflow, beforeExp: {}, exp: {}, afterExp: {}", beforeExp, exp, afterExp);
            return;
        }
        getVipModelProp().setVipExp(afterExp);
        int afterLevel = getVipLevel();
        if (afterLevel != beforeLevel) {
            onLvChanged(beforeLevel, afterLevel);
        }
        // qLog 打点
        getOwner().getQlogComponent().sendVipExpQLog(action, exp, afterLevel);
    }

    /**
     * 扣减vip经验
     *
     * @param decExp vip经验
     */
    public boolean decVipExp(int decExp, String action) {
        LOGGER.info("PlayerVipComponent decVipExp, decExp={} reason={}", decExp, action);
        if (decExp <= 0) {
            LOGGER.error("exp {} should always bigger than 0", decExp);
            return false;
        }
        int beforeExp = getVipExp();
        if (beforeExp <= 0) {
            return false;
        }
        int afterExp = Math.max(0, beforeExp - decExp);
        int beforeLevel = getVipLevel();
        getVipModelProp().setVipExp(afterExp);
        int afterLevel = getVipLevel();
        if (afterLevel != beforeLevel) {
            onLvChanged(beforeLevel, afterLevel);
        }
        // qLog 打点
        getOwner().getQlogComponent().sendVipExpQLog(action, decExp, afterLevel);
        return true;
    }


    /**
     * @return 获取玩家的vip经验
     */
    public int getVipExp() {
        return getVipModelProp().getVipExp();
    }

    /**
     * gm减少vip经验，正常不应该调用这个接口
     *
     * @param exp 要减少的vip经验
     */
    public void gmDecreaseVipExp(int exp) {
        if (exp >= 0) {
            LOGGER.info("exp {} should less than 0, nothing would do", exp);
            return;
        }
        int beforeLevel = getVipLevel();
        int beforeExp = getVipExp();
        int afterExp = MathUtils.addExact(beforeExp, exp);
        if (afterExp - exp != beforeExp) {
            LOGGER.warn("vip exp underflow, beforeExp: {}, exp: {}, afterExp: {}", beforeExp, exp, afterExp);
        }
        getVipModelProp().setVipExp(afterExp);
        int afterLevel = getVipLevel();
        if (afterLevel != beforeLevel) {
            // 因为减少经验可能导致等级降低，前面的等级不一定会刷新到后面的加成，因此最好全部去掉
            gmRemoveAllVipAdditions();
            updateVipAdditions(beforeLevel, afterLevel);
        }
    }


    /**
     * @return 获取玩家今天打开特权宝箱可以获得的经验值
     */
    public int getTodayExp() {
        VipResService service = ResHolder.getResService(VipResService.class);
        return service.getExpByCumLoginDays(getCumLoginDays());
    }

    /**
     * @return 获取玩家如果明天继续登录打开特权宝箱可以获得的经验值
     */
    public int getTomorrowExp() {
        VipResService service = ResHolder.getResService(VipResService.class);
        return service.getExpByCumLoginDays(getCumLoginDays() + 1);
    }

    /**
     * 购买前检查是否可以购买对应等级的VIP尊享宝箱
     *
     * @param goodsId 尊享礼包id
     */
    public void checkBuyExclusiveBoxBeforeBuyByGoodsId(int goodsId) throws GeminiException {
        LOGGER.info("vip exclusive box: try buy goodsId: {}", goodsId);
        VipResService resService = ResHolder.getResService(VipResService.class);
        int lv = resService.getLvByGoodsId(goodsId);
        if (lv < 0 && lv > resService.getMaxVipLevel()) {
            LOGGER.warn("vip level {} is not valid for goodsId {}", lv, goodsId);
            throw new GeminiException(ErrorCode.VIP_EXCLUSIVE_BOX_NOT_FOUND);
        }
        ExclusiveBoxInfoProp exclusiveBoxInfoProp = getVipModelProp().getLevelToExclusiveBoxInfoV(lv);
        if (exclusiveBoxInfoProp != null) {
            if (exclusiveBoxInfoProp.getStatus() == CommonEnum.ExclusiveBoxStatus.EBS_ALREADY_BOUGHT) {
                // 不是第一次购买
                throw new GeminiException(ErrorCode.VIP_EXCLUSIVE_HAVE_ALREADY_BOUGHT);
            } else {
                LOGGER.error("exclusive box status error, status: {}", exclusiveBoxInfoProp.getStatus());
            }
        }
    }

    /**
     * 尊享宝箱发货前的处理逻辑，不抛出异常
     *
     * @param goodsId 尊享礼包id
     */
    public void beforeDeliverExclusiveBoxNoThrow(int goodsId) {
        VipResService resService = ResHolder.getResService(VipResService.class);
        int lv = resService.getLvByGoodsId(goodsId);
        if (lv < 0 && lv > resService.getMaxVipLevel()) {
            LOGGER.error("vip level {} is not valid for goodsId {}", lv, goodsId);
        }
        ExclusiveBoxInfoProp exclusiveBoxInfoProp = getVipModelProp().getLevelToExclusiveBoxInfoV(lv);
        if (exclusiveBoxInfoProp != null) {
            LOGGER.error("exclusive box status error, status: {}, goodsId {}", exclusiveBoxInfoProp.getStatus(), goodsId);
        }
        // 设置为已购买
        getVipModelProp().addEmptyLevelToExclusiveBoxInfo(lv).setLevel(lv).setStatus(CommonEnum.ExclusiveBoxStatus.EBS_ALREADY_BOUGHT);
    }

    /**
     * 尊享宝箱发货后的处理逻辑， 不抛出异常
     *
     * @param goodsId 尊享礼包id
     */
    public void afterDeliverExclusiveBoxNoThrow(int goodsId) {
        // do nothing now
    }

    /**
     * @return 获取累计登录天数
     */
    private int getCumLoginDays() {
        return getVipModelProp().getVipCumLoginDays();
    }

    /**
     * 刷新vip模块使用的累计登录次数
     * NOTE(furson): 使用copilot补全
     */
    private void refreshCumLoginDays() {
        // 获取当前时间
        long now = SystemClock.now();
        // 获取上次刷新时间
        long lastRefreshTsMs = getVipModelProp().getLastUpdateCumLoginTsMs();
        // 如果上次刷新时间和当前时间是同一天，直接返回
        if (TimeUtils.isSameDay(lastRefreshTsMs, now)) {
            return;
        }
        ConstVipTemplate constTemplate = ResHolder.getConsts(ConstVipTemplate.class);
        int needResetDays = constTemplate.getCumLoginResetDay();
        if (TimeUtils.getAbsNatureDaysBetween(lastRefreshTsMs, now) >= needResetDays) {
            // 累计登录天数重置为1
            getVipModelProp().setVipCumLoginDays(1);
        } else {
            // 累计登录天数加1
            getVipModelProp().setVipCumLoginDays(getCumLoginDays() + 1);
        }
        // 更新累计登录天数刷新时间
        getVipModelProp().setLastUpdateCumLoginTsMs(now);
    }

    /**
     * 清空非当前等级未领取的每日宝箱的记录
     */
    private void clearVipDailyBox() {
        getVipModelProp().clearExtraCanGetDailyBoxLevel();
    }

    /**
     * 检查是否可以领取VIP每日礼包
     *
     * @param commonTemplate vip通用配置
     * @param checkTsMs      用于检查的时间戳
     * @throws GeminiException 可能返回的错误码为
     *                         {@link ErrorCode#VIP_DAILY_BOX_LEVEL_ALREADY_GOT}
     *                         {@link ErrorCode#VIP_DAILY_BOX_TODAY_ALREADY_GOT}
     */
    private void checkCanGetVipDailyBox(VipCommonTemplate commonTemplate, long checkTsMs) throws GeminiException {
        // 检查是否可以领取
        int vipLv = commonTemplate.getLevel();
        if (vipLv != getVipLevel()) {
            // 如果不是当前vip等级的宝箱，需要先检查是否在额外可领取的宝箱列表中
            if (!getVipModelProp().getExtraCanGetDailyBoxLevel().contains(vipLv)) {
                LOGGER.info("vip daily box: vipLv {} is not in extraCanGetDailyBoxLevel", vipLv);
                throw new GeminiException(ErrorCode.VIP_DAILY_BOX_LEVEL_ALREADY_GOT);
            }
            return;
        }
        // vip等级相等，上次领取时间戳跟检查时间戳是同一天
        if (TimeUtils.isSameDay(getVipModelProp().getLastGetDailyBoxTsMs(), checkTsMs)) {
            LOGGER.info("vip daily box: today already got");
            // 如果是当前vip等级的宝箱，需要检查是否已经领取过了
            throw new GeminiException(ErrorCode.VIP_DAILY_BOX_TODAY_ALREADY_GOT);
        }
        // vip等级相等，检查时间和下次刷新时间是同一天（跨了零点，但还没到刷新时间）
        if (TimeUtils.isSameDay(checkTsMs, getOwner().getRefreshComponent().getNextDayRefreshTs())) {
            // 检查时间戳小于下次刷新时间戳
            if (checkTsMs < getOwner().getRefreshComponent().getNextDayRefreshTs()) {
                LOGGER.info("vip daily box: not reach next day refresh time");
                // 如果是当前vip等级的宝箱，需要检查是否已经领取过了
                throw new GeminiException(ErrorCode.VIP_DAILY_BOX_TODAY_ALREADY_GOT);
            }
        }
        // 每日礼包是否包括自选商品
        if (isNeedChosenBox(commonTemplate)) {
            if (!thisQualityPlayerHasChosen(commonTemplate.getQuality())) {
                throw new GeminiException(ErrorCode.VIP_BOX_ITEM_NOT_CHOSEN);
            }
        }
    }

    /**
     * 检查是否可以领取VIP每日礼包
     *
     * @param checkTsMs 用于检查的时间戳
     * @throws GeminiException 可能返回的错误码为
     *                         {@link ErrorCode#VIP_PRIVILEGE_BOX_TODAY_ALREADY_GOT}
     */
    private void checkCanGetVipExpBox(long checkTsMs) throws GeminiException {
        // 上次领取时间戳跟检查时间戳是同一天
        if (TimeUtils.isSameDay(getVipModelProp().getLastGetPrivilegeBoxTsMs(), checkTsMs)) {
            LOGGER.info("vip exp box: today already got");
            throw new GeminiException(ErrorCode.VIP_PRIVILEGE_BOX_TODAY_ALREADY_GOT);
        }
        // 检查时间和下次刷新时间是同一天（跨了零点，但还没到刷新时间）
        if (TimeUtils.isSameDay(checkTsMs, getOwner().getRefreshComponent().getNextDayRefreshTs())) {
            // 检查时间戳小于下次刷新时间戳
            if (checkTsMs < getOwner().getRefreshComponent().getNextDayRefreshTs()) {
                LOGGER.info("vip exp box: not reach next day refresh time");
                throw new GeminiException(ErrorCode.VIP_PRIVILEGE_BOX_TODAY_ALREADY_GOT);
            }
        }
    }

    /**
     * 尝试添加额外可领取的每日宝箱
     * 2023年11月22日 追加回退等级机制，需求：回退等级后，高等级每日奖励保持原状，新等级每日奖励刷新
     *
     * @param beforeVipLv 之前的vip等级，可能需要添加到额外可领取的每日宝箱列表中
     * @param afterVipLv  获得经验后的vip等级，不需要添加到额外可领取列表中，不一定只比beforeVipLv大1
     */
    private void tryAddExtraCanGetVipDailyBox(int beforeVipLv, int afterVipLv) {
        if (afterVipLv != getVipLevel()) {
            LOGGER.error("rightVipLv {} is not equal to vipLv {}", afterVipLv, getVipLevel());
            return;
        }
        if (beforeVipLv < 0 || afterVipLv < 0) {
            LOGGER.error("vipLv {} cannot add to extra can get daily box level", beforeVipLv);
            return;
        }

        // 获取当前等级的每日宝箱信息
        long lastGetDailyBoxTsMs = getVipModelProp().getLastGetDailyBoxTsMs();
        // 只要出现升级，就一定要把今天的每日宝箱时间戳刷新为可领取状态
        getVipModelProp().setLastGetDailyBoxTsMs(0L);

        if (beforeVipLv >= afterVipLv) {
            // 现在允许回退了（回退之后高等级的礼包无额外处理需求）
            LOGGER.info("vipLv add to extra can get daily box level, before={} after={}", beforeVipLv, afterVipLv);
            return;
        }
        // 对[beforeVipLv, afterVipLv)之间的等级做处理
        for (int lv = beforeVipLv; lv < afterVipLv; lv++) {
            if (getVipModelProp().getExtraCanGetDailyBoxLevel().contains(lv)) {
                LOGGER.error("vipLv {} already in extra can get daily box level", lv);
                continue;
            }
            if (lv == beforeVipLv && TimeUtils.isSameDayWithNow(lastGetDailyBoxTsMs)) {
                // 即beforeVipLv等级的每日宝箱已经领取过了，不需要再添加到额外可领取的宝箱列表中
                LOGGER.info("today already get daily box for lv {}, cannot add extra can get daily box level", beforeVipLv);
            } else {
                // 其他中间等级或没有领取过beforeVipLv的情况下，将对应等级的宝箱添加到额外可领取的宝箱列表中
                getVipModelProp().getExtraCanGetDailyBoxLevel().add(lv);
                LOGGER.info("add extra can get daily box level {}", lv);
            }
        }
    }

    // ------------------------------------- VIP 商店 -------------------------------- //

    public void refreshVipStoreInfo() {
        getOwner().getProp().getVipModel().getBoughtGoods().clear();
    }

    public static void weeklyRefreshVipStoreInfo(PlayerWeekRefreshEvent weekRefreshEvent) {
        weekRefreshEvent.getPlayer().getVipComponent().refreshVipStoreInfo();
    }

    private void checkBuyVipShopItemLevel(int configGoodsId) throws GeminiException {
        VipShopTemplate vipShopTemplate = getVipShopTemplate(configGoodsId);
        int playerVipLevel = getVipLevel();
        int goodsVipLevelBottom = vipShopTemplate.getVipLevel();
        if (playerVipLevel < goodsVipLevelBottom) {
            throw new GeminiException(ErrorCode.VIP_STORE_VIP_LEVEL_TOO_LOW);
        }
    }

    /**
     * @param commonTemplate vip每日礼包配置
     * @return 根据配置返回该宝箱是否是需要选择的宝箱
     */
    private boolean isNeedChosenBox(VipCommonTemplate commonTemplate) {
        return commonTemplate.getOptional() > 0 && commonTemplate.getQuality() > 0;
    }

    private boolean thisQualityPlayerHasChosen(int qualityId) {
        return getVipModelProp().getVipBoxSelectedHero().containsKey(qualityId);
    }

    private void checkQualityId(int qualityId) throws GeminiException {
        VipResService resService = ResHolder.getResService(VipResService.class);
        resService.checkVipBoxQualityId(qualityId);
    }

    private void checkVipDailyBoxLevel(int qualityId) throws GeminiException {
        VipResService resService = ResHolder.getResService(VipResService.class);
        int minLv = resService.getVipBoxMinLv(qualityId);
        if (minLv == -1) {
            LOGGER.warn("checkVipDailyBoxLevel, vip box qualityId {} minLv not exist", qualityId);
            throw new GeminiException(ErrorCode.VIP_CONFIG_NOT_EXIST);
        }
        if (getVipLevel() < minLv) {
            throw new GeminiException(ErrorCode.VIP_DAILY_BOX_HERO_CHOSEN_LV_NOT_ENOUGH);
        }
    }

    private void checkVipShopItemLevel(int configGoodsId) throws GeminiException {
        VipShopTemplate vipShopTemplate = getVipShopTemplate(configGoodsId);
        int playerVipLevel = getVipLevel();
        int goodsVipLevelBottom = vipShopTemplate.getVipLevel();
        if (playerVipLevel < goodsVipLevelBottom) {
            throw new GeminiException(ErrorCode.VIP_SHOP_ITEM_HERO_CHOSEN_LV_NOT_ENOUGH);
        }
    }

    /**
     * @param vipShopTemplate vip商店配置
     * @return 根据配置返回该商品是否是需要选择的商品
     */
    private boolean isNeedChosenShopItem(VipShopTemplate vipShopTemplate) {
        return vipShopTemplate.getOptional() > 0 && vipShopTemplate.getQuality() > 0;
    }

    private boolean thisStoreItemPlayerHasChosen(int configGoodsId) {
        return getVipModelProp().getVipStoreSelectedHero().containsKey(configGoodsId);
    }

    /**
     * @param heroId    英雄id
     * @param qualityId 品质id
     * @throws GeminiException
     */
    private void checkSelectHeroId(int heroId, int qualityId) throws GeminiException {
        // 检查英雄是否存在
        getOwner().getHeroComponent().checkHeroProp(heroId);
        // 检查品质与英雄品质是否统一
        int configQualityId = HeroTemplateService.getHeroQualityByHeroId(heroId);
        if (configQualityId != qualityId) {
            LOGGER.warn("{} checkSelectHeroId heroId {} qualityId {} not match, config is {}", getOwner(), heroId, qualityId, configQualityId);
            throw new GeminiException(ErrorCode.VIP_HERO_CHOSEN_QUALITY_NOT_SAME);
        }

    }

    private void tryConsumeGoodCost(VipShopTemplate vipShopTemplate, long buyNum, String sPassword) {
        // 货币类型
        int currencyType = vipShopTemplate.getPricePair().getKey();
        // 货币要求数量
        int price = vipShopTemplate.getPricePair().getValue();
        // 钻石使用二级密码检测
        if (currencyType == CommonEnum.CurrencyType.DIAMOND_VALUE) {
            getOwner().getSettingComponent().checkSpassword(CommonEnum.SPassWordCheckType.SPWC_MANY_MONEY, price, sPassword);
        }
        // 尝试扣款
        AssetPackage cost = AssetPackage.builder().plusCurrency(currencyType, MathUtils.multiplyExact(buyNum, price)).build();
        getOwner().verifyThrow(cost);
        getOwner().consume(cost, CommonEnum.Reason.ICR_SHOPPING, "vip_market");
    }

    private void tryAddGoods(VipShopTemplate vipShopTemplate, int buyNum) {
        int itemId = 0;
        int addItemNum = 0;
        if (isNeedChosenShopItem(vipShopTemplate)) {
            int heroId = getVipModelProp().getVipStoreSelectedHero().get(vipShopTemplate.getId()).getHeroId();
            itemId = HeroTemplateService.getHeroChipItemIdByHeroId(heroId);
            addItemNum = MathUtils.multiplyExact(buyNum, vipShopTemplate.getOptional());
        } else {
            IntPairType itemPair = vipShopTemplate.getItemsPair();
            itemId = itemPair.getKey();
            addItemNum = MathUtils.multiplyExact(buyNum, itemPair.getValue());
        }
        AssetPackage itemPackage = AssetPackage.builder().plusItem(itemId, addItemNum).build();
        getOwner().getAssetComponent().give(itemPackage, CommonEnum.Reason.ICR_SHOPPING, "vip_market");
        int currencyType = vipShopTemplate.getPricePair().getKey();
        int usedMoney = MathUtils.multiplyExact(buyNum, vipShopTemplate.getPricePair().getValue());
        getOwner().getQlogComponent().sendShopQLog("vip_market", "shop_purchase",
                QlogMoneyConfig.getQlogMoneyConfigStr(currencyType, usedMoney),
                QlogItemConfig.getQlogItemConfigStr(itemId, addItemNum));
    }

    /**
     * @param configGoodsId 配置id
     * @return 返回对应的Vip商品配置，如果配置不存在，会抛出异常
     */
    private VipShopTemplate getVipShopTemplate(int configGoodsId) throws GeminiException {
        VipShopTemplate template = ResHolder.getInstance().findValueFromMap(VipShopTemplate.class, configGoodsId);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, StringUtils.format("buyVipItemByGoodsId configGoodsId={} not in VipShopTemplate", configGoodsId));
        }
        return template;
    }

    // ------------------------------------- VIP buff -------------------------------- //

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.VIP;
    }

    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        VipResService resService = ResHolder.getResService(VipResService.class);
        Map<CommonEnum.AdditionSourceType, Long> res = new HashMap<>();
        res.put(CommonEnum.AdditionSourceType.AST_VIP, resService.getAdditionValueByVipLevelAndId(getVipLevel(), additionId));
        return res;
    }

    /**
     * gm删除所有vip提供的加成
     */
    private void gmRemoveAllVipAdditions() {
        getOwner().getAddComponent().removeAdditionBySource(CommonEnum.AdditionSourceType.AST_VIP);
    }

    /**
     * 刷新vip等级的增益
     * P.S. 更高vip等级的增益是包含低等级的增益的（2023年11月22日进行修改，支持升级退级）
     *
     * @param beforeLevel 之前等级的加成
     * @param afterLevel  之后等级的加成
     */
    private void updateVipAdditions(int beforeLevel, int afterLevel) {
        // 加成要同时刷前后两个等级，防止策划删加成
        VipResService resService = ResHolder.getResService(VipResService.class);
        if (beforeLevel < 0 || beforeLevel > resService.getMaxVipLevel()) {
            LOGGER.error("add vip addition: vip level wrong, beforeLevel: {}", beforeLevel);
            return;
        }
        if (afterLevel < 0 || afterLevel > resService.getMaxVipLevel()) {
            LOGGER.error("add vip addition: vip level wrong, afterLevel: {}", afterLevel);
            return;
        }
        LOGGER.info("start to add vip lv {} lv {} 's addition", beforeLevel, afterLevel);
        Set<Integer> additionByBefore = resService.getAdditionListByVipLevel(beforeLevel, afterLevel);
        List<Integer> addition = new ArrayList<>(additionByBefore);
        getOwner().getAddComponent().updateAddition(AdditionProviderType.VIP, addition);
    }

    /**
     * 某个英雄觉醒后删除玩家自选的该英雄
     *
     * @param heroId 决定的英雄id
     */
    private void tryRemoveChosenHeroWhenIntensive(int heroId) {
        LOGGER.info("tryRemoveChosenHeroWhenIntensive: {} heroId: {}", getOwner(), heroId);
        getVipModelProp().getVipBoxSelectedHero().values().removeIf(selectedHero -> selectedHero.getHeroId() == heroId);
        getVipModelProp().getVipStoreSelectedHero().values().removeIf(selectedHero -> selectedHero.getHeroId() == heroId);
    }


    // ------------------------------------ CS 入口 --------------------------------------- //

    /**
     * 获取vip每日宝箱
     *
     * @param vipLv vip等级
     * @return 对应的S2C协议
     * @throws GeminiException 可能返回的异常见checkCanGetVipDailyBox
     */
    public PlayerCommon.Player_GetVipDailyBox_S2C getVipDailyBox(int vipLv) throws GeminiException {
        int nowVipLv = getVipLevel();

        if (nowVipLv < vipLv) {
            LOGGER.warn("getVipDailyBox: {} vip level not enough, nowVipLv: {}, vipLv: {}", getOwner(), nowVipLv, vipLv);
            throw new GeminiException(ErrorCode.VIP_LEVEL_NOT_ENOUGH);
        }
        VipCommonTemplate vipCommonTemplate = ResHolder.getResService(VipResService.class).getVipCommonTemplate(vipLv);
        long now = SystemClock.now();
        checkCanGetVipDailyBox(vipCommonTemplate, now);
        // 如果不是当前vip等级，先清掉之前的标志位
        if (vipLv != nowVipLv) {
            // 先remove掉相关的信息，Integer转换不能删除，不然就不是remove object了
            getVipModelProp().getExtraCanGetDailyBoxLevel().remove((Integer) vipLv);
        }
        // 仅在vip等级相同的时候设置领取时间
        if (vipLv == nowVipLv) {
            getVipModelProp().setLastGetDailyBoxTsMs(now);
        }
        PlayerCommon.Player_GetVipDailyBox_S2C.Builder builder = PlayerCommon.Player_GetVipDailyBox_S2C.newBuilder();
        List<IntPairType> itemPairList = vipCommonTemplate.getDailyBoxItemsPairList();
        // 发宝箱内固定的道具
        AssetPackage.Builder itemPackageBuilder = AssetPackage.builder();
        for (IntPairType itemPair : itemPairList) {
            itemPackageBuilder.plusItem(itemPair.getKey(), itemPair.getValue());
        }
        // 发宝箱内自选的道具，注意上面已经检查过这个部分了
        if (isNeedChosenBox(vipCommonTemplate)) {
            int qualityId = vipCommonTemplate.getQuality();
            VipSelectedHeroInfoProp selectedHeroInfoProp = getVipModelProp().getVipBoxSelectedHeroV(qualityId);
            if (null == selectedHeroInfoProp) {
                LOGGER.error("getVipDailyBox: {} vip box selected hero info not exist, qualityId: {}", getOwner(), qualityId);
                throw new GeminiException(ErrorCode.VIP_CONFIG_NOT_EXIST);
            }
            int heroId = selectedHeroInfoProp.getHeroId();
            int heroItemId = HeroTemplateService.getHeroChipItemIdByHeroId(heroId);
            itemPackageBuilder.plusItem(heroItemId, vipCommonTemplate.getOptional());
        }
        getOwner().getAssetComponent().give(itemPackageBuilder.build(), CommonEnum.Reason.ICR_VIP_DAILY_BOX, String.valueOf(vipLv));
        return builder.build();
    }

    public PlayerCommon.Player_GetVipPrivilegeBox_S2C getVipExpBox() throws GeminiException {
        // 检查是否可以领取
        long now = SystemClock.now();
        checkCanGetVipExpBox(now);
        int todayExp = getTodayExp();
        addVipExp(todayExp, "vip_reward_get_vip_exp");
        getVipModelProp().setLastGetPrivilegeBoxTsMs(SystemClock.now());
        // 构建回包
        PlayerCommon.Player_GetVipPrivilegeBox_S2C.Builder builder = PlayerCommon.Player_GetVipPrivilegeBox_S2C.newBuilder();
        builder.setTodayPoints(todayExp);
        builder.setCumLoginDays(getCumLoginDays());
        builder.setTomorrowPoints(getTomorrowExp());
        return builder.build();
    }

    /**
     * 获取Vip商店信息
     *
     * @return 返回Vip商店信息（玩家已经购买过的数量 & 商店库存刷新时间）
     */
    public PlayerVipStore.Player_GetVipStoreInfo_S2C.Builder getVipStoreInfo() {
        PlayerVipStore.Player_GetVipStoreInfo_S2C.Builder builder = PlayerVipStore.Player_GetVipStoreInfo_S2C.newBuilder();
        PlayerProp playerProp = getOwner().getProp();

        PlayerVipStore.VipStoreInfo.Builder vipStoreInfoBuilder = PlayerVipStore.VipStoreInfo.newBuilder();

        // 加载VipModelProp中已购买Vip商店道具数据至builder
        for (Map.Entry<Integer, BoughtGoodsInfoProp> goodsEntry : playerProp.getVipModel().getBoughtGoods().entrySet()) {
            vipStoreInfoBuilder.setBoughtNum(goodsEntry.getValue().getBoughtNum());
            vipStoreInfoBuilder.setConfigGoodsId(goodsEntry.getValue().getConfigGoodsId());
            builder.addInfo(vipStoreInfoBuilder.build());
        }

        // 告知客户端下次周刷时间
        builder.setNextRefreshTsMs(getOwner().getRefreshComponent().getNextWeekRefreshTs());
        LOGGER.debug("getInfo success");
        return builder;
    }

    /**
     * 根据Vip商店货品ID购买商品（钻石使用二级密码校验）
     *
     * @param configGoodsId 配置的商品ID
     * @param buyNum        购买数量
     * @param sPassword     二级密码
     * @return 玩家购买商品的信息
     */
    public PlayerVipStore.VipStoreInfo buyVipItemByGoodsId(int configGoodsId, int buyNum, String sPassword) {
        if (buyNum <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // 1. 确认configGoodsId有效
        VipShopTemplate vipShopTemplate = getVipShopTemplate(configGoodsId);

        // 2. 检查玩家Vip等级
        checkBuyVipShopItemLevel(configGoodsId);

        // 3. 自选商品检查
        if (isNeedChosenShopItem(vipShopTemplate)) {
            // 自选商品，玩家还未选择
            if (!thisStoreItemPlayerHasChosen(configGoodsId)) {
                throw new GeminiException(ErrorCode.VIP_SHOP_ITEM_NOT_CHOSEN);
            }
        }

        // 4. 检查购买数量是否超过上限
        BoughtGoodsInfoProp boughtGoodsInfoProp = getOwner().getProp().getVipModel().getBoughtGoods().get(configGoodsId);
        if (boughtGoodsInfoProp == null) {
            boughtGoodsInfoProp = getOwner().getProp().getVipModel().getBoughtGoods().addEmptyValue(configGoodsId);
        }
        // 购买前已购买数量
        int boughtNum = boughtGoodsInfoProp.getBoughtNum();
        // 购买上限
        int buyLimit = vipShopTemplate.getBuyLimit();
        if (buyLimit < buyNum + boughtNum) {
            throw new GeminiException(ErrorCode.STORE_EXCEED_BUY_LIMIT, StringUtils.format("buyVipItemByGoodsId configGoodsId={}, buyNum={}, boughtNum={}, buyLimit={}", configGoodsId, buyNum, buyLimit, boughtNum));
        }

        // 5. 检查玩家是否有足够资源支付
        tryConsumeGoodCost(vipShopTemplate, buyNum, sPassword);

        // 6. Vip商店购买记录
        boughtGoodsInfoProp.setBoughtNum(buyNum + boughtNum);
        PlayerVipStore.VipStoreInfo.Builder builder = PlayerVipStore.VipStoreInfo.newBuilder();
        builder.setConfigGoodsId(configGoodsId);
        builder.setBoughtNum(boughtGoodsInfoProp.getBoughtNum());

        // 7. 发货
        tryAddGoods(vipShopTemplate, buyNum);

        return builder.build();
    }


    public void selectVipDailyBoxHero(int qualityId, int heroId) {
        // 1. 确认qualityId有效
        checkQualityId(qualityId);

        // 2. 确认当前玩家vip等级是否达到可选的最低等级
        checkVipDailyBoxLevel(qualityId);

        // 3. 确认heroId有效
        checkSelectHeroId(heroId, qualityId);

        // 4. 设置到玩家身上
        Int32VipSelectedHeroInfoMapProp vipBoxSelectedHero = getVipModelProp().getVipBoxSelectedHero();
        if (vipBoxSelectedHero.containsKey(qualityId) && vipBoxSelectedHero.get(qualityId).getHeroId() == heroId) {
            LOGGER.warn("selectVipDailyBoxHero qualityId={}, heroId={} already selected", qualityId, heroId);
            return;
        }
        vipBoxSelectedHero.addEmptyValue(qualityId).setHeroId(heroId);
    }

    public void selectVipStoreHero(int configGoodsId, int heroId) {
        // 1. 确认configGoodsId有效
        VipShopTemplate shopTemplate = getVipShopTemplate(configGoodsId);

        // 2. 检查玩家Vip等级
        checkVipShopItemLevel(configGoodsId);

        // 3. 确认heroId有效
        checkSelectHeroId(heroId, shopTemplate.getQuality());

        // 4. 设置到玩家身上
        Int32VipSelectedHeroInfoMapProp vipStoreSelectedHero = getVipModelProp().getVipStoreSelectedHero();
        if (vipStoreSelectedHero.containsKey(configGoodsId) && vipStoreSelectedHero.get(configGoodsId).getHeroId() == heroId) {
            LOGGER.warn("selectVipStoreHero configGoodsId={}, heroId={} already selected", configGoodsId, heroId);
            return;
        }
        vipStoreSelectedHero.addEmptyValue(configGoodsId).setHeroId(heroId);
    }

    // ------------------------------------- 通用的prop方法 -------------------------------- //

    private PlayerVipModelProp getVipModelProp() {
        return getOwner().getProp().getVipModel();
    }

    private void onLvChanged(int beforeLevel, int afterLevel) {
        updateVipAdditions(beforeLevel, afterLevel);
        tryAddExtraCanGetVipDailyBox(beforeLevel, afterLevel);
    }
}

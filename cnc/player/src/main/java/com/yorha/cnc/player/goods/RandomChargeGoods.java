package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.unit.PlayerCycleChargeGoodsUnit;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemRewardBox;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.utils.Pair;
import com.yorha.game.gen.prop.ActNormalGoodsExtInfoProp;
import com.yorha.game.gen.prop.ActivitySelectGoodsRecordInfoProp;
import com.yorha.game.gen.prop.ActivitySelectGoodsRewardInfoProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.SsSceneMarquee;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;
import res.template.RadomGoodsMarqueeTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 随机礼包
 *
 * <AUTHOR>
 */
public class RandomChargeGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(RandomChargeGoods.class);

    @Override
    public void checkApply(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        final PlayerPayment.ActivityNormalGoodsParam goodsParam = msg.getOrderParam().getActGoodsParam();
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, goodsParam.getActId(), goodsParam.getUnitId());
        unit.checkApply(goodsTemplate);
        ItemResService itemResService = ResHolder.getResService(ItemResService.class);

        for (int selectRewardId : goodsTemplate.getSelectRewardsList()) {
            // 宝箱配表是存在的
            final List<ItemRewardBox> selectReward = itemResService.getSelectRewardOrNull(selectRewardId);
            if (selectReward == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectRewards without selectReward " + selectRewardId);
            }
            if (selectReward.isEmpty()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectRewards selectReward isEmpty" + selectRewardId);
            }
        }
    }

    @Override
    public void fillOrder(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        final PlayerPayment.ActivityNormalGoodsParam goodsParam = msg.getOrderParam().getActGoodsParam();
        final int actId = goodsParam.getActId();
        final int unitId = goodsParam.getUnitId();
        orderProp.getExtInfo()
                .getActNormal()
                .setActId(actId)
                .setUnitId(unitId);
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, actId, unitId);
        unit.recordOrderToken(orderProp.getToken());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
    }

    @Override
    public boolean needRecordGoodsHistory(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        boolean needRecord = true;
        try {
            ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
            PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, extInfo.getActId(), extInfo.getUnitId());
            if (!unit.isTokenSame(goodsOrder.getToken())) {
                // 订单不同，unit已经变化了，本次购买不应该记录次数
                needRecord = false;
                LOGGER.warn("RandomChargeGoods afterBaseDeliver, player {} cycle charge goods order token not same, order token {}, unit token {}",
                        player, goodsOrder.getToken(), unit.getCurOrderToken());
            }
            unit.clearOrderToken();
        } catch (Exception e) {
            LOGGER.error("RandomChargeGoods needRecordGoodsHistory, player {} cycle charge goods order token check failed", player, e);
        }
        return needRecord;
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        final ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
        final PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, extInfo.getActId(), extInfo.getUnitId());
        unit.onGoodsBought(goodsOrder.getGoodsId());
        final ItemResService itemResService = ResHolder.getResService(ItemResService.class);
        final List<ItemReward> rewardList = itemResService.randomRewardList(goodsTemplate.getSelectRewardsList());
        final List<Struct.ItemPair> items = new ArrayList<>();
        final AssetPackage.Builder asset = AssetPackage.builder();
        final ActivitySelectGoodsRecordInfoProp selectGoodsRecordInfo = new ActivitySelectGoodsRecordInfoProp();
        selectGoodsRecordInfo.setGoodsId(goodsTemplate.getId());
        for (ItemReward reward : rewardList) {
            asset.plusItem(reward.getItemTemplateId(), reward.getCount());
            final Struct.ItemPair.Builder itemPair = Struct.ItemPair.newBuilder();
            itemPair.setItemTemplateId(reward.getItemTemplateId())
                    .setCount(reward.getCount());
            items.add(itemPair.build());
            final ActivitySelectGoodsRewardInfoProp selectGoodsRewardInfo = new ActivitySelectGoodsRewardInfoProp();
            selectGoodsRewardInfo.setSelectRewardId(reward.getConfigId());
            selectGoodsRewardInfo.getItemPair()
                    .setItemTemplateId(reward.getItemTemplateId())
                    .setCount(reward.getCount());
            selectGoodsRecordInfo.getRewardInfos().put(reward.getConfigId(), selectGoodsRewardInfo);
        }
        player.getAssetComponent().give(asset.build(), CommonEnum.Reason.ICR_BUY_GOODS, String.valueOf(goodsTemplate.getId()));
        // 购买记录
        if (!selectGoodsRecordInfo.getRewardInfos().isEmpty()) {
            unit.recordSelectRecord(goodsTemplate.getId(), selectGoodsRecordInfo);
        }
        trySendMarquee(player, items, goodsTemplate.getId());
        LOGGER.info("RandomChargeGoods afterBaseDeliver {} {} {}", player, goodsTemplate.getId(), items);
        return items;
    }

    private void trySendMarquee(PlayerEntity player, List<Struct.ItemPair> items, int goodsId) {
        final Map<Integer, RadomGoodsMarqueeTemplate> configMap = ResHolder.getInstance().getMap(RadomGoodsMarqueeTemplate.class);
        final RadomGoodsMarqueeTemplate config = configMap.get(goodsId);
        if (config == null) {
            return;
        }
        final MarqueeResService marqueeResService = ResHolder.getResService(MarqueeResService.class);
        final Set<Pair<Integer, Integer>> spMap = marqueeResService.getSpItem(goodsId);
        if (spMap == null) {
            return;
        }
        final Struct.DisplayData.Builder dataBuilder = Struct.DisplayData.newBuilder();
        final Struct.DisplayParamList.Builder paramsBuilder = dataBuilder.getParamsBuilder();
        boolean needMarquee = false;
        paramsBuilder.addDatas(MsgHelper.buildDisPlayText(player.getName()));
        for (Struct.ItemPair itemPair : items) {
            final Pair<Integer, Integer> pair = new Pair<>(itemPair.getItemTemplateId(), itemPair.getCount());
            if (!spMap.contains(pair)) {
                continue;
            }
            paramsBuilder.addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_ITEM_ID_FOR_NAME, itemPair.getItemTemplateId()));
            paramsBuilder.addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, itemPair.getCount()));
            needMarquee = true;
        }
        if (!needMarquee) {
            return;
        }
        SsSceneMarquee.SendSceneMarqueeCmd cmd = SsSceneMarquee.SendSceneMarqueeCmd.newBuilder()
                .setMarqueeId(config.getMarquee())
                .setDisplayData(dataBuilder)
                .build();
        player.ownerActor().tellBigScene(player.getZoneId(), cmd);
        LOGGER.info("RandomChargeGoods trySendMarquee {} {} {} {}", player, goodsId, config.getMarquee(), items);
    }

    private PlayerCycleChargeGoodsUnit findCycleChargeGoodsUnit(PlayerEntity player, int actId, int unitId) throws GeminiException {
        BasePlayerActivityUnit unit = player.getActivityComponent().findActiveUnit(actId, unitId);
        if (!(unit instanceof PlayerCycleChargeGoodsUnit)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "actId: " + actId + ", unitId: " + unitId);
        }
        return (PlayerCycleChargeGoodsUnit) unit;
    }
}

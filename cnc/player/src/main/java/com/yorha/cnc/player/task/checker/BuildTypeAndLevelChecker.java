package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildCreateEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.HashSet;
import java.util.List;

/**
 * 任意x个y_y_y建筑升至z级
 * param1: 数量
 * param2-(N-1): 建筑类型
 * param-N : 等级
 *
 * <AUTHOR>
 */
public class BuildTypeAndLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerInnerBuildLevelUpEvent.class.getSimpleName(),
            PlayerInnerBuildCreateEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int num = taskParams.getFirst();
        int requireLv = taskParams.getLast();
        HashSet<Integer> buildTypeSet = new HashSet<>();
        for (int i = 1; i < taskParams.size() - 1; i++) {
            buildTypeSet.add(taskParams.get(i));
        }

        int fitNum = 0;
        for (var buildType : buildTypeSet) {
            if (event.getPlayer().getInnerBuildRhComponent().getInnerBuildLevel(buildType) >= requireLv) {
                fitNum++;
            }
        }
        prop.setProcess(Math.min(fitNum, num));

        return prop.getProcess() >= num;
    }
}

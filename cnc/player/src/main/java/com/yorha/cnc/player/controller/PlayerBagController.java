package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerItemComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.shop.ShopDataTemplateService;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.PlayerCommon.Player_BuyAndUseItem_C2S;
import com.yorha.proto.PlayerCommon.Player_BuyAndUseItem_S2C;
import com.yorha.proto.PlayerCommon.Player_UseItem_C2S;
import com.yorha.proto.PlayerCommon.Player_UseItem_S2C;
import res.template.ItemTemplate;
import res.template.ShopTemplate;

import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_SP_ITEM;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_BAG)
public class PlayerBagController {


    @CommandMapping(code = MsgType.PLAYER_BUYANDUSEITEM_C2S)
    public GeneratedMessageV3 buyAndUseItem(PlayerEntity playerEntity, Player_BuyAndUseItem_C2S msg) {
        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, (int) msg.getItemKey());
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        playerEntity.getSettingComponent().checkSpassword(SPWC_SP_ITEM, itemTemplate.getId(), msg.getParams().getSPassWord());
        ShopTemplate shopConf = ResHolder.getResService(ShopDataTemplateService.class).findItemShopConf((int) msg.getItemKey());
        if (shopConf == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        IntPairType pricePair = shopConf.getPricePair();
        if (pricePair.getValue() <= 0) {
            throw new GeminiException(ErrorCode.ITEM_PRICE_ILLEGAL);
        }
        CurrencyType currencyType = CurrencyType.forNumber(pricePair.getKey());
        if (currencyType == null) {
            throw new GeminiException("currencyType not recognized. {}", pricePair.getKey());
        }
        if (msg.getNum() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ItemUseParamsProp paramsProp = new ItemUseParamsProp();
        paramsProp.mergeFromCs(msg.getParams());

        int totalCost = pricePair.getValue() * msg.getNum();
        ErrorCode code = playerEntity.getPurseComponent().isEnough(currencyType, totalCost);
        if (!code.isOk()) {
            throw new GeminiException(code);
        }

        playerEntity.getPurseComponent().consume(currencyType, totalCost, CommonEnum.Reason.ICR_BUY, "");

        PlayerItemComponent itemComponent = playerEntity.getItemComponent();
        itemComponent.addItem((int) msg.getItemKey(), msg.getNum(), CommonEnum.Reason.ICR_BUY, "");

        Player_UseItem_S2C.Builder response = itemComponent.useItemByTemplateId(itemTemplate.getId(), msg.getNum(), paramsProp);

        Player_BuyAndUseItem_S2C.Builder returnMsg = Player_BuyAndUseItem_S2C.newBuilder();
        returnMsg.setItemKey(response.getItemKey()).setNum(response.getNum()).setResult(response.getResult());
        return returnMsg.build();
    }

    @CommandMapping(code = MsgType.PLAYER_USEITEM_C2S)
    public GeneratedMessageV3 useItem(PlayerEntity playerEntity, Player_UseItem_C2S msg) {
        ItemUseParamsProp paramsProp = new ItemUseParamsProp();
        paramsProp.mergeFromCs(msg.getParams());
        Player_UseItem_S2C.Builder response = playerEntity.getItemComponent().useItemByKey(msg.getItemKey(), msg.getNum(), paramsProp);
        return response.build();
    }
}

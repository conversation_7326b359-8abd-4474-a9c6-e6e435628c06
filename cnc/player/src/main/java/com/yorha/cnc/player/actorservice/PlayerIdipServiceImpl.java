package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.PlayerIdipService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerIdip;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBundlePurchase;

/**
 * @author: zpr
 * Date: 2023/8/9
 * Description:
 */
public class PlayerIdipServiceImpl implements PlayerIdipService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerIdipServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerIdipServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    @Override
    public void handleConsumeDiamondAsk(SsPlayerIdip.ConsumeDiamondAsk ask) {
        if (!StringUtils.equals(ask.getOpenId(), playerActor.getOrLoadEntity().getOpenId())) {
            throw new GeminiException("OpenID is wrong, openId={}", ask.getOpenId());
        }
        long before = playerActor.getEntity().getPurseComponent().getCurrencyAmount(CommonEnum.CurrencyType.DIAMOND);
        playerActor.getEntity().getPurseComponent().consume(CommonEnum.CurrencyType.DIAMOND, ask.getFixValue(),
                CommonEnum.Reason.ICR_IDIP, "IDIP_DO_DELETE_GOLD_REQ");
        long after = playerActor.getEntity().getPurseComponent().getCurrencyAmount(CommonEnum.CurrencyType.DIAMOND);
        SsPlayerIdip.ConsumeDiamondAns ans = SsPlayerIdip.ConsumeDiamondAns.newBuilder().setBeforeValue((int) before)
                .setAfterValue((int) after).build();
        LOGGER.info("PlayerIdipServiceImpl handleConsumeDiamondAsk consume diamond={}, before={}, after={}, playerId={}",
                ask.getFixValue(), before, after, playerActor.getPlayerId());
        playerActor.answer(ans);
    }

    @Override
    public void handleConsumeItemsAsk(SsPlayerIdip.ConsumeItemsAsk ask) {
        if (!StringUtils.equals(ask.getOpenId(), playerActor.getOrLoadEntity().getOpenId())) {
            throw new GeminiException("OpenID is wrong, openId={}", ask.getOpenId());
        }
        int before = playerActor.getEntity().getItemComponent().getItemNum(ask.getItemId());
        playerActor.getEntity().getItemComponent().consume(ask.getItemId(), ask.getItemNum(), CommonEnum.Reason.ICR_IDIP,
                "IDIP_DO_DELETE_ITEMS_REQ");
        int after = playerActor.getEntity().getItemComponent().getItemNum(ask.getItemId());
        SsPlayerIdip.ConsumeItemsAns ans = SsPlayerIdip.ConsumeItemsAns.newBuilder().setBeforeValue(before)
                .setAfterValue(after).build();
        LOGGER.info("PlayerIdipServiceImpl handleConsumeItemsAsk consume itemId={}, itemNum={}, before={}, after={}, playerId={}",
                ask.getItemId(), ask.getItemNum(), before, after, playerActor.getPlayerId());
        playerActor.answer(ans);
    }

    @Override
    public void handlePullMidasCmd(SsPlayerIdip.PullMidasCmd ask) {
        LOGGER.info("handlePullMidasCmd ask={}", ask);
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        playerEntity.getPaymentComponent().syncPull();
        try {
            QlogCncBundlePurchase.init(playerEntity.getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("purchase_gold_third_party")
                    .setBundleId(0)
                    .setPrice(ask.getChargeNum())
                    .setLimitedCount(-1)
                    .setRemainLimitedCount(-1)
                    .setIfDouble(0)
                    .setIfUseToken(0)
                    .setItemGet("")
                    .setActionNote("")
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("send QlogCncBundlePurchase error:", e);
        }
    }

    @Override
    public void handleResourceAsk(SsPlayerIdip.ResourceAsk ask) {
        LOGGER.info("PlayerIdipServiceImpl handleResourceAsk ask={}", ask);
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (!StringUtils.equals(ask.getOpenId(), playerEntity.getOpenId())) {
            playerActor.answer(SsPlayerIdip.ResourceAns.newBuilder().setExceptionId(ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()).build());
            return;
        }
        long beforeAmount = playerEntity.getPurseComponent().getCurrencyAmount(ask.getCurrencyType());
        if (ask.getValue() > 0) {
            playerEntity.getPurseComponent().give(ask.getCurrencyType(), ask.getValue(), CommonEnum.Reason.ICR_IDIP, "IDIP_DO_PLAYER_RESOURCE_REQ");
        } else {
            long realConsumeCount = Math.min(beforeAmount, -ask.getValue());
            if (realConsumeCount > 0) {
                playerEntity.getPurseComponent().consume(ask.getCurrencyType(), realConsumeCount, CommonEnum.Reason.ICR_IDIP, "IDIP_DO_PLAYER_RESOURCE_REQ");
            }
        }
        long afterAmount = playerActor.getEntity().getPurseComponent().getCurrencyAmount(ask.getCurrencyType());
        SsPlayerIdip.ResourceAns.Builder builder = SsPlayerIdip.ResourceAns.newBuilder()
                .setBeforeValue(beforeAmount)
                .setAfterValue(afterAmount);
        playerActor.answer(builder.build());
    }

    @Override
    public void handleModifyVipExpAsk(SsPlayerIdip.ModifyVipExpAsk ask) {
        if (ask.getValue() <= 0) {
            throw new GeminiException("value is wrong, value={}", ask.getValue());
        }
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (!StringUtils.equals(ask.getOpenId(), playerEntity.getOpenId())) {
            playerActor.answer(SsPlayerIdip.ModifyVipExpAns.newBuilder().setExceptionId(ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()).build());
            return;
        }
        int beforeLevel = playerEntity.getVipComponent().getVipLevel();
        int beforeExp = playerEntity.getVipComponent().getVipExp();

        playerEntity.getVipComponent().decVipExp(ask.getValue(), "idip_modify_dec");

        SsPlayerIdip.ModifyVipExpAns build = SsPlayerIdip.ModifyVipExpAns.newBuilder()
                .setBeforeExp(beforeExp)
                .setBeforeLevel(beforeLevel)
                .setAfterExp(playerEntity.getVipComponent().getVipExp())
                .setAfterLevel(playerEntity.getVipComponent().getVipLevel())
                .build();
        playerActor.answer(build);
    }

    @Override
    public void handleModifySoldierAsk(SsPlayerIdip.ModifySoldierAsk ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (ask.getPlayerId() != playerEntity.getPlayerId()) {
            LOGGER.error("PlayerIdipServiceImpl handleModifySoldierAsk, player error, player={}", ask.getPlayerId());
            playerActor.answer(SsPlayerIdip.ModifySoldierAns.newBuilder().setExceptionId(ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()).build());
            return;
        }
        if (!StringUtils.equals(ask.getOpenId(), playerEntity.getOpenId())) {
            playerActor.answer(SsPlayerIdip.ModifySoldierAns.newBuilder().setExceptionId(ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()).build());
            return;
        }
        SsScenePlayer.IdIpModifySoldierAsk builder = SsScenePlayer.IdIpModifySoldierAsk.newBuilder()
                .setSoldierId(ask.getSoldierId())
                .setPlayerId(ask.getPlayerId())
                .setValue(ask.getValue()).build();
        SsScenePlayer.IdIpModifySoldierAns ans = playerActor.callBigScene(builder);
        playerActor.answer(SsPlayerIdip.ModifySoldierAns.newBuilder().setBeforeValue(ans.getBeforeValue()).setAfterValue(ans.getAfterValue()).build());
    }
}

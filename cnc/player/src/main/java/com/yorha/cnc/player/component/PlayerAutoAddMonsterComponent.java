package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.enums.AutoAddMonsterType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ExecuteTimeProp;
import com.yorha.game.gen.prop.Int32ExecuteTimeMapProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * <p>
 * 自动补怪次数计算组件
 */
public class PlayerAutoAddMonsterComponent extends PlayerComponent {

    private static final Logger LOGGER = LogManager.getLogger(PlayerAutoAddMonsterComponent.class);


    public PlayerAutoAddMonsterComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        for (AutoAddMonsterType scheduleType : AutoAddMonsterType.values()) {
            Int32ExecuteTimeMapProp records = getOwner().getProp().getFragments().getScheduleExecuteRecords();
            if (!records.containsKey(scheduleType.getType())) {
                records.addEmptyValue(scheduleType.getType());
            }
        }
    }


    /**
     * 扣除使用过的自动补怪次数
     *
     */
    public int reduceSystemAddMonsterCount() {
        int addMonsterCount = getOwner().getProp().getFragments().getBigSceneRecords().getAutoAddMonsterCount();
        if (addMonsterCount <= 0) {
            return addMonsterCount;
        }
        int count = getCycle(getOwner(), SystemClock.now());
        if (count <= 0) {
            return addMonsterCount;
        }
        int afterCount = Math.max(addMonsterCount - count, 0);
        getOwner().getProp().getFragments().getBigSceneRecords().setAutoAddMonsterCount(afterCount);
        updateByCycle(getOwner(), afterCount == 0 ? 0 : count);

        LOGGER.info("Player {} reduce add monster count {}, current energy {}", getOwner(), count, afterCount);
        return afterCount;
    }

    private void updateByCycle(PlayerEntity player, int cycle) {
        ExecuteTimeProp timeProp = player.getProp().getFragments().getScheduleExecuteRecords().get(AutoAddMonsterType.REDUCE_ADD_MONSTER_COUNT.getType());
        if (timeProp == null || timeProp.getLastExecuteTime() <= 0) {
            return;
        }
        timeProp.setLastExecuteTime(cycle > 0 ? timeProp.getLastExecuteTime() + cycle * periodMs() : 0);
    }

    private long periodMs() {
        return TimeUnit.SECONDS.toMillis(ResHolder.getResService(ConstKVResService.class).getTemplate().getReduceAddMonsterPeriod());
    }

    public int getCycle(PlayerEntity player, long nowMs) {
        ExecuteTimeProp timeProp = player.getProp().getFragments().getScheduleExecuteRecords().get(AutoAddMonsterType.REDUCE_ADD_MONSTER_COUNT.getType());
        if (timeProp == null || timeProp.getLastExecuteTime() <= 0) {
            return 0;
        }
        long timeDiff = nowMs - timeProp.getLastExecuteTime();
        return (int) (timeDiff / periodMs());
    }

    public void updateByExecuteTime(long timeMs) {
        ExecuteTimeProp timeProp = getOwner().getProp().getFragments().getScheduleExecuteRecords().get(AutoAddMonsterType.REDUCE_ADD_MONSTER_COUNT.getType());
        if (timeProp.getLastExecuteTime() == 0) {
            timeProp.setLastExecuteTime(timeMs);
        } else {
            // 多余的时间
            long timeDiff = timeMs - timeProp.getLastExecuteTime();
            long timeReduce = timeDiff % periodMs();
            timeProp.setLastExecuteTime(timeMs - timeReduce);
        }
    }
}

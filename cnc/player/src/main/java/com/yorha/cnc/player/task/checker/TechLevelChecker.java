package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.TechResearchEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;


/**
 * 某个科技达到多少级
 * param1 科技id
 * param2 等级
 *
 * <AUTHOR>
 */
public class TechLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            TechResearchEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(
            PlayerTaskEvent event,
            TaskInfoProp prop,
            TaskPoolTemplate taskTemplate
    ) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer techId = taskParams.get(0);
        Integer techLevel = taskParams.get(1);

        int playerTechLevel = entity.getTechComponent().getTechLevelByTechId(techId);
        prop.setProcess(Math.min(playerTechLevel, techLevel));
        return prop.getProcess() >= techLevel;
    }
}

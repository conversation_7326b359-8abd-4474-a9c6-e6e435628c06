package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.TechFullOpenEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 科技战力提升
 * param1:战力值
 *
 * <AUTHOR>
 */
public class TechPowerIncreaseChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class),
            ClassNameCacheUtils.getSimpleName(TechFullOpenEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int powerConfig = taskParams.get(0);
        if (event.getPlayer().getTechComponent().isAllUnlock()) {
            prop.setProcess(powerConfig);
            return true;
        }

        if (event instanceof PlayerPowerIncreaseEvent) {
            PlayerPowerIncreaseEvent powerIncreaseEvent = (PlayerPowerIncreaseEvent) event;
            if (powerIncreaseEvent.getPowerReason() == CommonEnum.PowerType.PT_TECH) {
                long l = prop.getProcess() + powerIncreaseEvent.getPower();
                prop.setProcess(Math.min(powerConfig, (int) l));
            }
        }
        return prop.getProcess() >= powerConfig;
    }
}

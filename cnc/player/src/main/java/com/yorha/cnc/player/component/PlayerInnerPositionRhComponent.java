package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.newInnerBuild.NewInnerBuildTemplateService;
import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.game.gen.prop.InnerBuildRHProp;
import com.yorha.game.gen.prop.Int32InnerBuildRHMapProp;
import com.yorha.proto.CommonEnum.CityBuildType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.InnerBuildRhTemplate;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 内城位置相关
 */
public class PlayerInnerPositionRhComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerInnerPositionRhComponent.class);
    /**
     * ture表示格子被占用了
     */
    private BoolMap positionBoolMap;

    public PlayerInnerPositionRhComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        positionBoolMap = BoolMap.getInstance(130, 130);
    }

    @Override
    public void postLoad(boolean isRegister) {
        /*
        if (isRegister) {
            List<InnerBuildRHProp> innerBuildRHProps = getInnerBuildRHProp().values().stream().filter(it -> it.getBuildStateRH() == BuildStateRH.BuildStateRH_BUILD).collect(Collectors.toList());
            for (InnerBuildRHProp innerBuildRHProp : innerBuildRHProps) {
                InnerBuildRhTemplate innerBuildRhTemplate = ResHolder.getInstance().getValueFromMap(InnerBuildRhTemplate.class, innerBuildRHProp.getBuildType());
                IntPairType modelSize = innerBuildRhTemplate.getModelSizePair();
                PointProp point = innerBuildRHProp.getPoint();
                occupyPositionRH(point.getX(), point.getY(), modelSize.getKey(), modelSize.getValue());
            }
        }
        */
    }

    public BoolMap getRHPositionBoolMap() {
        return positionBoolMap;
    }

    /**
     * 检测是否在以建造中心为中心给定范围内进行建造
     */
    public void checkPositionBuild(int x, int y, int width, int height) {
        // 获取建造中心的坐标
        List<InnerBuildRHProp> innerBuildRHProps = getInnerBuildRHProp().values().stream()
                .filter(it -> it.getBuildType() == CityBuildType.CBT_FACT.getNumber())
                .collect(Collectors.toList());
        if (innerBuildRHProps.isEmpty()) {
            LOGGER.info(ErrorCode.BUILD_NO_FACTORY_BUILD.getCodeId());
        }
        InnerBuildRHProp buildRHProp = innerBuildRHProps.get(0);
        int buildX = buildRHProp.getPoint().getX();
        int buildY = buildRHProp.getPoint().getY();
        //建造中心的大小
        InnerBuildRhTemplate buildTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildTemplate(buildRHProp.getBuildType());
        IntPairType modelSizeBuild = buildTemplate.getModelSizePair();

        // 以建造中心为中心的获取的范围内
        IntPairType limitSize = ResHolder.getResService(NewInnerBuildTemplateService.class).getTemplate().getConstructionYardBaseSize();
        int limitWidth = limitSize.getKey();
        int limitHeight = limitSize.getValue();

        // 建造中心的范围
        int minX = buildX - (limitWidth - modelSizeBuild.getKey()) / 2;
        int minY = buildY - (limitHeight - modelSizeBuild.getValue()) / 2;
        int maxX = buildX + modelSizeBuild.getKey() - 1 + (limitWidth - modelSizeBuild.getKey()) / 2;
        int maxY = buildY + modelSizeBuild.getValue() - 1 + (limitHeight - modelSizeBuild.getValue()) / 2;

        // 检查给定坐标是否在范围内
        if (x < minX || x + width > maxX || y < minY || y + height > maxY) {
            throw new GeminiException(ErrorCode.BUILD_OUT_ON_RANGE.getCodeId());
        }
    }

    /**
     * 占位置
     */

    void occupyPositionRH(int x, int y, int width, int height) {
        getRHPositionBoolMap().setRHGridStatus(x, y, width, height, true);
    }

    private Int32InnerBuildRHMapProp getInnerBuildRHProp() {
        return getOwner().getProp().getPlayerInnerBuildRHModel().getInnerBuildRH();
    }

    /**
     * 检测位置是否被占用
     */
    public void checkPositionValidRH(int x, int y, int width, int height) {
        for (int i = 0; i < width; i++) {
            int xPoint = x + i;
            for (int j = 0; j < height; j++) {
                int yPoint = y + j;
                if (!getRHPositionBoolMap().checkPosStatus(xPoint, yPoint, false)) {
                    throw new GeminiException(ErrorCode.BUILD_BUILD_POSITION_OVERLAP.getCodeId());
                }
            }
        }
    }

    /**
     * 清除位置
     */
    public void clearPointByBuildId(int buildId) {
        InnerBuildRHProp innerBuildRHProp = getInnerBuildRHProp().get(buildId);
        if (innerBuildRHProp == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_INFOR.getCodeId());
        }
        InnerBuildRhTemplate rhBuildTemplate = ResHolder.getInstance().getValueFromMap(InnerBuildRhTemplate.class, innerBuildRHProp.getBuildType());
        if (rhBuildTemplate == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_INFOR.getCodeId());
        }
        IntPairType modelSize = rhBuildTemplate.getModelSizePair();
        getRHPositionBoolMap().setRHGridStatus(innerBuildRHProp.getPoint().getX(), innerBuildRHProp.getPoint().getY(), modelSize.getKey(), modelSize.getValue(), false);
    }
}

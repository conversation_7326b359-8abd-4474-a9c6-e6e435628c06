package com.yorha.cnc.player.controller;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerDiscountStore.*;

/**
 * 折扣商店相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_DISCOUNT_TORE)
public class PlayerDiscountStoreController {

    /**
     * 获取商店明细
     */
    @CommandMapping(code = MsgType.PLAYER_GETDISCOUNTSTOREINFO_C2S)
    public Player_GetDiscountStoreInfo_S2C handle(PlayerEntity playerEntity, Player_GetDiscountStoreInfo_C2S msg) {
        Player_GetDiscountStoreInfo_S2C.Builder ret = Player_GetDiscountStoreInfo_S2C.newBuilder();
        DiscountStoreDTO dto = playerEntity.getDiscountStoreComponent().getDiscountStoreInfo();
        ret.setDto(dto);
        return ret.build();
    }

    /**
     * 购买商品
     */
    @CommandMapping(code = MsgType.PLAYER_BUYDISCOUNTSTOREITEM_C2S)
    public Player_BuyDiscountStoreItem_S2C handle(PlayerEntity playerEntity, Player_BuyDiscountStoreItem_C2S msg) {
        Player_BuyDiscountStoreItem_S2C ret = playerEntity.getDiscountStoreComponent().buyDiscountStoreItem(msg.getItemId(), msg.getSPassWord());
        return ret;
    }

    /**
     * 刷新商店
     */
    @CommandMapping(code = MsgType.PLAYER_REFRESHDISCOUNTSTORE_C2S)
    public Player_RefreshDiscountStore_S2C handle(PlayerEntity playerEntity, Player_RefreshDiscountStore_C2S msg) {
        Player_RefreshDiscountStore_S2C.Builder ret = Player_RefreshDiscountStore_S2C.newBuilder();
        playerEntity.getDiscountStoreComponent().refreshDiscountStore();
        DiscountStoreDTO dto = playerEntity.getDiscountStoreComponent().getDiscountStoreInfo();
        ret.setDto(dto);
        return ret.build();
    }


}

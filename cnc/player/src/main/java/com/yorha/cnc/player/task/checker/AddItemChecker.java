package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.AddItemEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.HashSet;
import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.ADD_ITEM_TOTAL;

/**
 * 获得指定类型道具
 * param1: 次数
 * param2-X : 道具1 id、道具2 id、道具3 id........
 *
 * <AUTHOR>
 */
public class AddItemChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(AddItemEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer times = taskParams.get(0);
        HashSet<Integer> itemIdSet = new HashSet<>();
        for (int i = 1; i < taskParams.size(); i++) {
            itemIdSet.add(taskParams.get(i));
        }
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int addItemTotal = 0;
                for (Integer itemId : itemIdSet) {
                    addItemTotal += entity.getStatisticComponent().getSecondStatistic(ADD_ITEM_TOTAL, itemId);
                }
                prop.setProcess(Math.min(addItemTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof AddItemEvent) {
                    int itemId = ((AddItemEvent) event).getItemId();
                    if (itemIdSet.contains(itemId)) {
                        int addItemNum = ((AddItemEvent) event).getItemNum();
                        prop.setProcess(Math.min(times, prop.getProcess() + addItemNum));
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.RecruitLeagueHeroEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;
/**
 * 招募传说英雄
 * <AUTHOR>
 */
public class RecruitLeagueHeroChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(RecruitLeagueHeroEvent.class);

    public static List<String> attentionList = Lists.newArrayList(RecruitLeagueHeroEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configCount = taskParams.get(0);

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof RecruitLeagueHeroEvent) {
                prop.setProcess(Math.min(prop.getProcess() + ((RecruitLeagueHeroEvent) event).getRecruitTime(), configCount));
                LOGGER.info("RecruitLeagueHeroChecker updateProcess, playerId={}, add {} time, process={}, config count={}", event.getPlayer().getPlayerId(), ((RecruitLeagueHeroEvent) event).getRecruitTime(), prop.getProcess(), configCount);
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= configCount;
    }
}

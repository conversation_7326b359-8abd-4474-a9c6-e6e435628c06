package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 战力达到
 * param1: 战力值
 *
 * <AUTHOR>
 */
public class PowerIncreaseToChecker extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        long totalPower = event.getPlayer().getPowerComponent().getPowerInfoProp().getTotalPower();
        int power = taskParams.get(0);
        if (prop.getProcess() < totalPower) {
            prop.setProcess(Math.min((int) totalPower, power));
        }
        return prop.getProcess() >= power;
    }
}

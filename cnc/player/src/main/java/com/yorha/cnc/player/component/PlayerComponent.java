package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.resource.IResTemplate;
import com.yorha.proto.SsSceneDungeon;

import java.util.Set;

/**
 * 玩家组件基类
 *
 * <pre>
 * 注册
 *       -> com.init
 *       -> com.onLoad(isRegister) -> com.postLoad(isRegister)
 *       -> com.onLogin -> ntfPlayerFullProp -> com.postLogin
 *       -> registerSuccess
 *
 * 登录内存里没有entity
 *       -> com.init
 *       -> com.onLoad -> com.postLoad
 *       -> com.onLogin -> ntfPlayerFullProp -> com.postLogin
 *       -> loginSuccess
 *
 * 登录内存里有entity
 *       -> com.onLogin -> ntfPlayerFullProp -> com.postLogin
 *       -> loginSuccess
 *
 * 内部拉起没有entity
 *       -> com.init
 *       -> com.onLoad -> com.postLoad
 *       -> handleMsg
 *
 * 内部拉起有entity
 *       -> handleMsg
 * </pre>
 * <p> com指component
 * <p> com.onXxx: 处理本component的内部逻辑，同步调用，出现异常会中断整个xxx流程和其他com的onXxx，本次xxx以失败告终
 * <p> com.postXxx: 处理跨component依赖的逻辑，同步调用，出现异常不会影响其他com的postXxx,不会影响整体流程 且会企微告警
 * <p> 如果有逻辑不希望占用xxx的执行时间，使用 tell(Runnable) 来异步执行。如qlog
 * <p> 如果有逻辑不希望中断整个xxx流程，显式try-catch
 * <p> 如果两个component的onLogin之间有依赖关系，用postXxx二阶段处理
 * <p> registerSuccess和loginSuccess会给客户端回包等等
 * <p>
 * <p> 注意这里的register、load、login等逻辑都在component的构造函数和init()之后，与业务逻辑无关的一些操作可以放在这里（如：内部容器的初始化、prop和本地field的绑定等）
 *
 * <AUTHOR>
 */
public abstract class PlayerComponent extends AbstractComponent<PlayerEntity> {

    public PlayerComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public final void postInit() {
        // player禁止使用postInit接口
    }

    /**
     * 玩家数据加载/创建完毕后
     * 异常将会终止其他模块的onLoad，playerActor拉起异常
     * 注册/登录/内部拉起都会执行
     */
    public void onLoad(boolean isRegister) {
    }

    /**
     * 玩家数据加载/创建完毕后
     * 异常会终止其他模块的postLoad
     * 注册/登录/内部拉起都会执行
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     */
    public void postLoad(boolean isRegister) {
    }

    /**
     * 登录流程中entity load完毕之后
     * 异常将会终止其他模块的onLogin
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     */
    public void onLogin() {

    }

    /**
     * 异常不会终止其他模块的postLogin
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     *
     * @param playerLoginAns 登入大世界的回包
     */
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
    }

    /**
     * 玩家登出后
     */
    public void afterLogout() {
    }

    @Override
    public PlayerActor ownerActor() {
        return getOwner().ownerActor();
    }

    public long getPlayerId() {
        return getOwner().getEntityId();
    }

    public void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {

    }
}

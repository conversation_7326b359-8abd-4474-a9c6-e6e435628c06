package com.yorha.cnc.player.goods;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.event.PlayerPurchaseCardEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;

/**
 * 周卡月卡
 *
 * <AUTHOR>
 */
public class WeekMonthCardGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(WeekMonthCardGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        // 检查周卡月卡unit是否存在
        int actId = msg.getOrderParam().getActGoodsParam().getActId();
        int unitId = msg.getOrderParam().getActGoodsParam().getUnitId();
        BasePlayerActivityUnit unit = owner.getActivityComponent().findActiveUnit(actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        orderProp.getExtInfo()
                .getActNormal()
                .setActId(msg.getOrderParam().getActGoodsParam().getActId())
                .setUnitId(msg.getOrderParam().getActGoodsParam().getUnitId());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {

    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
        int actId = extInfo.getActId();
        int unitId = extInfo.getUnitId();
        int cardId = this.getCardId(goodsOrder.getGoodsId());
        ActivityWeekMonthCardUnitProp weekMonthCardInfoProp = this.getWeekMonthCardInfo(owner, actId, unitId);
        if (weekMonthCardInfoProp.getCardId() != cardId) {
            LOGGER.error("Activity cardId don't match goodOrder's cardId, ActivityId={}, unitId={}, cardId={}, goodOrder's  cardId={}",
                    actId, unitId, weekMonthCardInfoProp.getCardId(), cardId);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 续期
        long oldExpireTime = weekMonthCardInfoProp.getExpireTsMs();
        long nowTime = SystemClock.now();
        long startTime = Long.max(oldExpireTime, nowTime);
        // 持续天数
        int lastDay = goodsTemplate.getLastingTime();
        Instant instant = TimeUtils.getDayStartInstant(Instant.ofEpochMilli(startTime).plus(lastDay, ChronoUnit.DAYS));
        weekMonthCardInfoProp.setExpireTsMs(instant.toEpochMilli());
        LOGGER.info("player{} purchase card {}, oldExpireTime={}, nowTime={}, expireTime={}, lasDay={}",
                owner.getPlayerId(), cardId, oldExpireTime, nowTime, weekMonthCardInfoProp.getExpireTsMs(), lastDay);
        // 发送事件更新玩家周卡月卡失效定时器
        new PlayerPurchaseCardEvent(owner, cardId).dispatch();
        return Collections.emptyList();
    }

    protected int getCardId(final int goodsId) {
        return goodsId;
    }


    protected ActivityWeekMonthCardUnitProp getWeekMonthCardInfo(PlayerEntity owner, int actId, int unitId) {
        PlayerActivityModelProp prop = owner.getProp().getActivityModel();
        for (ActivityScheduleProp asProp : Lists.newArrayList(prop.getActSchedules().values())) {
            ActivityProp activityProp = asProp.getActivity();
            if (activityProp.getId() == actId) {
                ActivityUnitProp activityUnitProp = activityProp.getSpecUnit();
                if (activityUnitProp.getUnitId() != unitId) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
                }
                return activityUnitProp.getWeekMonthCardUnit();
            }
        }
        throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
    }
}

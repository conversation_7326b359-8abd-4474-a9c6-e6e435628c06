package com.yorha.cnc.player.gm.command.chat;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CreateChat implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        List<Long> members = ImmutableList.of(14808167L, 23131467L, 29430224L);
        ChatPlayerEntity chatPlayerEntity = actor.getOrLoadChatPlayerEntity();
        chatPlayerEntity.getHandleChatComponent().createGroupChat(0, members, "123");
    }

    @Override
    public String showHelp() {
        return "createGroupChat";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}
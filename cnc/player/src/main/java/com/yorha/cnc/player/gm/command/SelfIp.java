package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.FutureActorRunnable;
import com.yorha.common.actor.node.ISessionActor;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.helper.GmHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class SelfIp implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String ip = "unknown";
        IActorRef sessionRef = actor.getEntity().getSessionComponent().getSessionRef();
        if (sessionRef == null) {
            ip = "not online";
        } else {
            final FutureActorRunnable<ISessionActor, String> getSelfIp = new FutureActorRunnable<>("getSelfIp",
                    (sessionActor) -> sessionActor.getSession().getClientIp());
            ActorSendMsgUtils.send(sessionRef, getSelfIp);
            try {
                ip = getSelfIp.get(5, TimeUnit.SECONDS);
            } catch (Exception e) {
                ip = "exception";
            }
        }
        String res = "ip=" + ip + ", worldId=" + ServerContext.getWorldIdStr() + ", zoneId=" + ServerContext.getZoneId();
        GmHelper.sendGmNtfMail(actor.getZoneId(), playerId, "ip", res);
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

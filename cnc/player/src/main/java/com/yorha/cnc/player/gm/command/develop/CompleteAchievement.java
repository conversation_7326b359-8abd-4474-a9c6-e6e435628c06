package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.component.PlayerAchievementComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 完成玩家成就
 *
 * <AUTHOR>
 */
public class CompleteAchievement implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(CompleteAchievement.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int achievement = Integer.parseInt(args.get("achievementId"));
        final PlayerAchievementComponent achievementComponent = actor.getEntity().getAchievementComponent();
        if (achievementComponent.isAchievementComplete(achievement)) {
            return;
        }
        achievementComponent.onAchievementComplete(achievement);
        LOGGER.info("CompleteAchievement achievementId={}", achievement);
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "CompleteAchievement achievementId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

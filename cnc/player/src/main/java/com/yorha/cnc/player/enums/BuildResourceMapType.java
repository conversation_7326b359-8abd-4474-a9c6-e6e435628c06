package com.yorha.cnc.player.enums;

import com.yorha.proto.CommonEnum;

import java.util.*;

/**
 * 资源对应相关枚举映射枚举
 *
 * <AUTHOR>
 */
public enum BuildResourceMapType {

    /**
     * 石油相关枚举
     */
    OIL_TYPE(CommonEnum.CurrencyType.OIL,
            CommonEnum.CityBuildType.CBT_OIL_WELL,
            CommonEnum.BuffEffectType.ET_OIL_STORAGE_CAP_FIXED,
            CommonEnum.BuffEffectType.ET_OIL_RATE_BUFF,
            CommonEnum.BuffEffectType.ET_OIL_RATE_FIXED,
            CommonEnum.BuffEffectType.ET_PLAYER_OIL_PRODUCE_BUFF,
            CommonEnum.BuffEffectType.ET_OIL_PROTECT_LIMIT_FIXED,
            CommonEnum.BuffEffectType.ET_ALL_RATE_BUFF
    ),

    /**
     * 钢铁相关枚举
     */

    TUNGSTEN_GOLD_TYPE(CommonEnum.CurrencyType.STEEL,
            CommonEnum.CityBuildType.CBT_IRON_DEPOSIT,
            CommonEnum.BuffEffectType.ET_STEEL_STORAGE_CAP_FIXED,
            CommonEnum.BuffEffectType.ET_STEEL_RATE_BUFF,
            CommonEnum.BuffEffectType.ET_STEEL_RATE_FIXED,
            CommonEnum.BuffEffectType.ET_PLAYER_STEEL_PRODUCE_BUFF,
            CommonEnum.BuffEffectType.ET_STEEL_PROTECT_LIMIT_FIXED,
            CommonEnum.BuffEffectType.ET_ALL_RATE_BUFF
    );

    /**
     * 资源类型
     */
    private final CommonEnum.CurrencyType resourceType;

    /**
     * 资源建筑类型
     */
    private final CommonEnum.CityBuildType buildType;

    /**
     * 资源产量上限类型
     */
    private final CommonEnum.BuffEffectType capType;

    /**
     * 每小时基础资源产量
     */
    private final CommonEnum.BuffEffectType baseProduce;

    /**
     * 资源产量增幅万分比
     */
    private final CommonEnum.BuffEffectType rateProduce;

    /**
     * 固定资源产量
     */
    private final CommonEnum.BuffEffectType fixProduce;

    /**
     * 资源保护量buff
     */
    private final CommonEnum.BuffEffectType protectBuff;

    /**
     * 全资源产率增幅万分比
     */
    private final CommonEnum.BuffEffectType allRateProduce;

    BuildResourceMapType(CommonEnum.CurrencyType resourceType,
                         CommonEnum.CityBuildType buildType,
                         CommonEnum.BuffEffectType capType,
                         CommonEnum.BuffEffectType rateProduce,
                         CommonEnum.BuffEffectType baseProduce,
                         CommonEnum.BuffEffectType fixProduce,
                         CommonEnum.BuffEffectType protectBuff,
                         CommonEnum.BuffEffectType allRateProduce) {
        this.resourceType = resourceType;
        this.buildType = buildType;
        this.capType = capType;
        this.rateProduce = rateProduce;
        this.baseProduce = baseProduce;
        this.fixProduce = fixProduce;
        this.protectBuff = protectBuff;
        this.allRateProduce = allRateProduce;
    }

    public CommonEnum.CurrencyType getResourceType() {
        return resourceType;
    }

    public CommonEnum.CityBuildType getBuildType() {
        return buildType;
    }

    public CommonEnum.BuffEffectType getCapType() {
        return capType;
    }

    /**
     * ET_OIL_RATE_BUFF = 105 [(addNeedSync) = false];        // 石油增产buff（万分比）
     * ET_STEEL_RATE_BUFF = 106 [(addNeedSync) = false];        // 钢铁增产buff（万分比）
     * ET_RARE_EARTH_RATE_BUFF = 107 [(addNeedSync) = false];//稀土增产buff（万分比）
     * ET_TITANIUM_RATE_BUFF = 108 [(addNeedSync) = false];        //钛矿增产buff（万分比）
     */
    public CommonEnum.BuffEffectType getRateProduce() {
        return rateProduce;
    }

    /**
     * ET_PLAYER_OIL_PRODUCE_BUFF = 207 [(addNeedSync) = false];        // 个人石油产出buff（固定值）
     * ET_PLAYER_STEEL_PRODUCE_BUFF = 208 [(addNeedSync) = false];        // 个人钢铁产出buff（固定值）
     * ET_PLAYER_RARE_EARTH_PRODUCE_BUFF = 209 [(addNeedSync) = false];// 个人稀土产出buff（固定值）
     * ET_PLAYER_TITANIUM_PRODUCE_BUFF = 210 [(addNeedSync) = false];// 个人钛矿产出buff（固定值）
     */
    public CommonEnum.BuffEffectType getFixProduce() {
        return fixProduce;
    }

    /**
     * ET_OIL_RATE_FIXED = 144 [(addNeedSync) = false];        // 每小时石油产量
     * ET_STEEL_RATE_FIXED = 145 [(addNeedSync) = false];        // 每小时钢铁产量
     * ET_RARE_EARTH_RATE_FIXED = 146 [(addNeedSync) = false];// 每小时稀土产量
     * ET_TITANIUM_RATE_FIXED = 147 [(addNeedSync) = false];        // 每小时泰矿产量
     */
    public CommonEnum.BuffEffectType getBaseProduce() {
        return baseProduce;
    }

    /**
     * ET_OIL_PROTECT_LIMIT_FIXED = 157 [(addNeedSync) = false]; // 石油保护上限
     * ET_STEEL_PROTECT_LIMIT_FIXED = 158 [(addNeedSync) = false]; // 钢铁保护上限
     * ET_RARE_PROTECT_LIMIT_FIXED = 159 [(addNeedSync) = false]; // 稀土保护上限
     * ET_TITANIUM_PROTECT_LIMIT_FIXED = 160 [(addNeedSync) = false]; // 泰矿保护上限
     */
    public CommonEnum.BuffEffectType getProtectBuff() {
        return protectBuff;
    }

    /**
     * 全资源产率增加
     * ET_ALL_RATE_BUFF = 104
     */
    public CommonEnum.BuffEffectType getAllRateProduce() {
        return allRateProduce;
    }

    /**
     * 是否是资源产量相关效果
     */
    public boolean isProduceAttentionBuff(int addition) {
        if (getBaseProduce().getNumber() == addition) {
            return true;
        }
        if (getRateProduce().getNumber() == addition) {
            return true;
        }
        if (getFixProduce().getNumber() == addition) {
            return true;
        }
        if (getCapType().getNumber() == addition) {
            return true;
        }
        if (getAllRateProduce().getNumber() == addition) {
            return true;
        }
        return false;
    }

    public static BuildResourceMapType getBuildResourceMapTypeWithBuildType(CommonEnum.CityBuildType buildType) {
        Optional<BuildResourceMapType> first = Arrays.stream(BuildResourceMapType.values()).filter(it -> it.getBuildType().getNumber() == buildType.getNumber()).findFirst();
        return first.orElse(null);
    }

    public static Set<Integer> getProduceAttentionAddition() {
        Set<Integer> res = new HashSet<>();
        for (BuildResourceMapType value : values()) {
            res.add(value.getBaseProduce().getNumber());
            res.add(value.getRateProduce().getNumber());
            res.add(value.getFixProduce().getNumber());
            res.add(value.getCapType().getNumber());
            res.add(value.getAllRateProduce().getNumber());
        }
        return res;
    }

    /**
     * 通过资源类型获取BuildResourceMapType
     *
     * @param type 资源类型
     * @return BuildResourceMapType
     */
    public static BuildResourceMapType getBuildResourceMapTypeByCurrencyType(CommonEnum.CurrencyType type) {
        for (BuildResourceMapType buildResourceMapType : BuildResourceMapType.values()) {
            if (buildResourceMapType.resourceType == type) {
                return buildResourceMapType;
            }
        }
        return null;
    }

    public static List<Integer> getAllResourceBuildType() {
        List<Integer> list = new ArrayList<>();
        for (BuildResourceMapType res : BuildResourceMapType.values()) {
            list.add(res.getBuildType().getNumber());
        }
        return list;
    }
}

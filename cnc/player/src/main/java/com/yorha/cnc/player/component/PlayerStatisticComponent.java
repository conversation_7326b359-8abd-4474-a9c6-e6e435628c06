package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerWeekRefreshEvent;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.cnc.player.statistic.SecondStatisticHandler;
import com.yorha.cnc.player.statistic.SingleStatisticHandler;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.enums.statistic.StatisticRefreshType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.helper.CheckerHelper;
import com.yorha.common.qlog.json.statistic.SoldierKill;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.game.gen.prop.PlayerStatisticModelProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.values;

/**
 * 玩家统计项模块
 * 负责记录玩家行为数据
 * <p>
 * 统计项：
 * 纯后台且客户端完全不感知的，放统计项
 * 读多改少，就放属性系统自己维护（比如每天打守护者的次数，重置放在PlayerStatisticComponent）
 * 写多读少（放统计项功能里，并提供拉取cs协议）
 *
 * <AUTHOR>
 */
public class PlayerStatisticComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerStatisticComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerStatisticComponent::monitorDailyRefreshEvent);
        EntityEventHandlerHolder.register(PlayerWeekRefreshEvent.class, PlayerStatisticComponent::monitorWeeklyRefreshEvent);
        EntityEventHandlerHolder.register(PlayerSpeedUpEvent.class, PlayerStatisticComponent::onSpeedUpEvent);
    }

    private static void onSpeedUpEvent(PlayerSpeedUpEvent event) {
        if (QueueSpeedReason.isFreeSpeedUp(event.getReason())) {
            return;
        }
        int speedMinutes = (int) MathUtils.ceilLong((double) event.getTotalCostSec() / 60);
        event.getPlayer().getStatisticComponent().recordSingleStatistic(StatisticEnum.USE_ACCELERATE_ITEM_TOTAL, speedMinutes);
        if (event.getType() == CommonEnum.QueueTaskType.CITY_BUILD) {
            event.getPlayer().getStatisticComponent().recordSingleStatistic(StatisticEnum.USE_BUILD_ACCELERATE_ITEM_TOTAL, speedMinutes);
        } else if (event.getType() == CommonEnum.QueueTaskType.RESEARCH) {
            event.getPlayer().getStatisticComponent().recordSingleStatistic(StatisticEnum.USE_TECH_ACCELERATE_ITEM_TOTAL, speedMinutes);
        } else if (CheckerHelper.isTrain(event.getType())) {
            event.getPlayer().getStatisticComponent().recordSingleStatistic(StatisticEnum.USE_TRAIN_ACCELERATE_ITEM_TOTAL, speedMinutes);
        }
    }

    private final Map<StatisticEnum, SingleStatisticHandler> singleHandlerMap = Maps.newEnumMap(StatisticEnum.class);
    private final Map<StatisticEnum, SecondStatisticHandler> secondHandlerMap = Maps.newEnumMap(StatisticEnum.class);

    @Override
    public void init() {
        super.init();
        for (StatisticEnum statisticEnum : values()) {
            if (statisticEnum.getLevel() == 1) {
                singleHandlerMap.put(statisticEnum, new SingleStatisticHandler(getOwner(), statisticEnum));
            }
            if (statisticEnum.getLevel() == 2) {
                secondHandlerMap.put(statisticEnum, new SecondStatisticHandler(getOwner(), statisticEnum));
            }
        }
    }

    public PlayerStatisticComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerStatisticModelProp getProp() {
        return getOwner().getProp().getStatisticModel();
    }

    /**
     * 获取一维统计数据
     */
    public long getSingleStatistic(StatisticEnum type) {
        if (type.getLevel() != 1) {
            LOGGER.error("statistic level fail, need level 1, but type:{} no", type);
            throw new GeminiException(ErrorCode.STATISTIC_TYPE_ERROR);
        }
        SingleStatisticHandler singleStatisticHandler = singleHandlerMap.get(type);
        if (singleStatisticHandler == null) {
            return 0;
        }
        return singleStatisticHandler.getValue();
    }

    /**
     * 获取二维统计的全部数据
     */
    public Map<Integer, StructPB.SingleStatisticPB> getAllSecondStatisticPB(StatisticEnum type) {
        if (type.getLevel() != 2) {
            LOGGER.error("statistic level fail, need level 2, but type:{} no", type);
            throw new GeminiException(ErrorCode.STATISTIC_TYPE_ERROR);
        }
        SecondStatisticHandler secondStatisticHandler = secondHandlerMap.get(type);
        if (secondStatisticHandler == null) {
            return new HashMap<>();
        }
        return secondStatisticHandler.getAllPBValue().getDatasMap();
    }

    public Map<Integer, Struct.SingleStatistic> getAllSecondStatistic(StatisticEnum type) {
        if (type.getLevel() != 2) {
            LOGGER.error("statistic level fail, need level 2, but type:{} no", type);
            throw new GeminiException(ErrorCode.STATISTIC_TYPE_ERROR);
        }
        SecondStatisticHandler secondStatisticHandler = secondHandlerMap.get(type);
        if (secondStatisticHandler == null) {
            return new HashMap<>();
        }
        return secondStatisticHandler.getAllValue().getDatasMap();
    }

    /**
     * 获取二维统计数据
     */
    public long getSecondStatistic(StatisticEnum type, int id) {
        if (type.getLevel() != 2) {
            LOGGER.error("statistic level fail, need level 2, but type:{} id:{} no", type, id);
            throw new GeminiException(ErrorCode.STATISTIC_TYPE_ERROR);
        }
        SecondStatisticHandler secondStatisticHandler = secondHandlerMap.get(type);
        if (secondStatisticHandler == null) {
            return 0;
        }
        return secondStatisticHandler.getValue(id);
    }

    /**
     * 获取二维统计项数据之和
     */
    public long getSumValueOfSecondStatistic(StatisticEnum statisticEnum) {
        SecondStatisticHandler secondStatisticHandler = secondHandlerMap.get(statisticEnum);
        return secondStatisticHandler.sumValue();
    }

    /**
     * 重置统计数据
     */
    private void clearStatisticData(StatisticEnum type) {
        switch (type.getLevel()) {
            case 1: {
                singleHandlerMap.get(type).resetValue();
                break;
            }
            case 2: {
                secondHandlerMap.get(type).resetValue();
                break;
            }
            default: {
                LOGGER.error("un know statistic level, type:{}", type);
            }
        }
    }

    public void execRefreshEvent(StatisticRefreshType refreshType) {
        for (StatisticEnum type : values()) {
            if (type.getRefreshType() == refreshType) {
                clearStatisticData(type);
            }
        }
    }

    public void recordSingleStatistic(StatisticEnum type, int value) {
        singleHandlerMap.get(type).update(value);
    }

    public void recordSecondStatistic(StatisticEnum type, int id, long value) {
        secondHandlerMap.get(type).update(id, value);
    }

    public String getKillQLogInfo() {
        List<SoldierKill> result = new ArrayList<>();
        for (Map.Entry<Integer, Struct.SingleStatistic> entry : getAllSecondStatistic(StatisticEnum.SOLDIER_KILL_TOTAL).entrySet()) {
            result.add(new SoldierKill(entry.getKey(), entry.getValue().getValue()));
        }
        return JsonUtils.toJsonString(result);
    }

    /**
     * 周刷事件
     */
    private static void monitorWeeklyRefreshEvent(PlayerWeekRefreshEvent event) {
        event.getPlayer().getStatisticComponent().execRefreshEvent(StatisticRefreshType.WEEKLY);
    }

    /**
     * 日刷事件
     */
    private static void monitorDailyRefreshEvent(PlayerDayRefreshEvent event) {
        event.getPlayer().getStatisticComponent().execRefreshEvent(StatisticRefreshType.DAILY);
        // 清除每日击杀守护者数量记录
        event.getPlayer().getProp().getKillMonsterModel().setGuardKillNumDay(0);
    }

}
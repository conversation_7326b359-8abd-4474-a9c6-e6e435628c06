package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.component.PlayerTriggerBundleComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.payment.PaymentResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityLostTriggerBundleUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.PlayerResellTriggerBundleProp;
import com.yorha.game.gen.prop.PlayerTriggerBundleProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityResellTriggerGoodsTemplate;
import res.template.TriggerGoodsTemplate;

import java.util.List;

public class PlayerLostTriggerBundleUnit extends BasePlayerActivityUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerLostTriggerBundleUnit.class);

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_LOST_TRIGGER_BUNDLE, (owner, prop, template) ->
                new PlayerLostTriggerBundleUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerLostTriggerBundleUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    private void init() {
        final PlayerEntity player = player();
        final long now = SystemClock.now();
        if (this.unitProp.getLostTriggerBundleUnit().getLastInitTsMs() > 0 && this.unitProp.getLostTriggerBundleUnit().getLastInitTsMs() < now) {
            // 用来防重
            return;
        }
        this.unitProp.getLostTriggerBundleUnit().setLastInitTsMs(now);
        LOGGER.info("PlayerLostTriggerBundleUnit init {}", player);
        List<ActivityResellTriggerGoodsTemplate> conf = ResHolder.getResService(PaymentResService.class).getResellTriggerGoodsList(this.ownerActivity.getActivityId());
        List<PlayerTriggerBundleComponent.TriggerBundleDTO> popped = player.getTriggerBundleComponent().popExpiredBundles(conf);
        ActivityLostTriggerBundleUnitProp lostTriggerBundleUnit = this.unitProp.getLostTriggerBundleUnit();
        for (PlayerTriggerBundleComponent.TriggerBundleDTO lostBundleDTO : popped) {
            PlayerTriggerBundleProp lostBundle = lostBundleDTO.prop;
            lostTriggerBundleUnit.addEmptyResellBundles(lostBundle.getId())
                    .setId(lostBundle.getId())
                    .setTemplateId(lostBundle.getTemplateId())
                    .setBought(false)
                    .setGoodsShownTimes(lostBundleDTO.goodsShownTimes);
        }

    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        ActivityLostTriggerBundleUnitProp model = unitProp.getLostTriggerBundleUnit();
        PlayerTriggerBundleComponent triggerBundleComponent = player().getTriggerBundleComponent();
        for (PlayerResellTriggerBundleProp lostBundle : model.getResellBundles().values()) {
            if (!lostBundle.getBought()) {
                LOGGER.info("PlayerLostTriggerBundleUnit onExpire pushBack lostBundle {} {} {}", player(), lostBundle.getId(), lostBundle.getTemplateId());
                triggerBundleComponent.pushBackStillLostBundles(lostBundle.getId(), lostBundle.getTemplateId(), lostBundle.getGoodsShownTimes());
            }
        }
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return Lists.newArrayList(unitProp.getLostTriggerBundleUnit().getResellBundles().values())
                .stream()
                .allMatch(PlayerResellTriggerBundleProp::getBought);
    }


    public void checkApply(long triggerBundleId) {
        PlayerResellTriggerBundleProp resellBundle = unitProp.getLostTriggerBundleUnit().getResellBundlesV(triggerBundleId);
        if (resellBundle == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "bundle not found");
        }
        if (resellBundle.getBought()) {
            throw new GeminiException(ErrorCode.ALREADY_TAKEN);
        }
    }

    public void afterGoodsBought(long triggerBundleId) {
        PlayerResellTriggerBundleProp resellBundle = unitProp.getLostTriggerBundleUnit().getResellBundlesV(triggerBundleId);
        if (resellBundle == null) {
            WechatLog.error("PlayerLostTriggerBundleUnit afterGoodsBought bundle not found! {} {}", player(), triggerBundleId);
            return;
        }
        if (resellBundle.getBought()) {
            WechatLog.error("PlayerLostTriggerBundleUnit afterGoodsBought bundle already bought {} {}", player(), triggerBundleId);
            return;
        }
        resellBundle.setBought(true);
        TriggerGoodsTemplate template = ResHolder.getTemplate(TriggerGoodsTemplate.class, resellBundle.getTemplateId());
        player().getTriggerBundleComponent().sendPopupPurchaseQlog(template.getGoodsId(), "Purchase_bundle", "Lost_bundle", resellBundle.getGoodsShownTimes());
        if (this.isFinished()) {
            onUnitFinished();
        }
    }
}

package com.yorha.cnc.player.goods;


import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import res.template.ChargeGoodsTemplate;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * 充值礼包
 */
public interface Goods {

    /**
     * 客户端在发起礼包购买之前会向服务器申请一个订单token，这个token会通过midas的直购回调透传回服务器
     * <p>
     * 这个接口作一些校验逻辑
     */
    void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate);

    /**
     * 给订单prop填充一些额外信息
     */
    default void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
    }

    /**
     * 回调已经来了，这个时候再做一次检验，一般来说不用阻断，该做的校验在checkApply里已经做掉了
     */
    void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder);

    /**
     * 需要记录已购买次数，绝大部分情况下都应该返回true，目前仅在活动跨天刷新，且商品不变的情况下，可能返回false
     */
    default boolean needRecordGoodsHistory(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        return true;
    }

    /**
     * 配置表里的基本发货逻辑已经给了，如果有一些发货不能通过道具的形式给到，就在这里做一下
     */
    List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder);

    Map<CommonEnum.GoodsType, Goods> GOODS_MAP = new EnumMap<CommonEnum.GoodsType, Goods>(CommonEnum.GoodsType.class) {{
        this.put(CommonEnum.GoodsType.GT_TEST, new TestGoods());
        this.put(CommonEnum.GoodsType.GT_ACTIVITY, new ActivityNormalGoods());
        this.put(CommonEnum.GoodsType.GT_ACT_SECOND_QUEUE, new ActivitySecondQueueGoods());
        this.put(CommonEnum.GoodsType.GT_VIP, new VipExclusiveGoods());
        this.put(CommonEnum.GoodsType.GT_DAILY_ACCOUNT, new DailyDiscountGoods());
        this.put(CommonEnum.GoodsType.GT_GROWTH_FUND, new ActivityGrowthFundGoods());
        this.put(CommonEnum.GoodsType.GT_WEEK_MONTH_CARD, new WeekMonthCardGoods());
        this.put(CommonEnum.GoodsType.GT_TRIGGER_BUNDLE, new TriggerBundleGoods());
        this.put(CommonEnum.GoodsType.GT_CYCLE_CHARGE, new CycleChargeGoods());
        this.put(CommonEnum.GoodsType.GT_BATTLEPASS, new BattlePassGoods());
        this.put(CommonEnum.GoodsType.GT_SUPER_DAILY_ACCOUNT, new SuperDailyDiscountGoods());
        this.put(CommonEnum.GoodsType.GT_DISCOUNT_WEEK_MONTH_CARD, new DiscountWeekMonthCardGoods());
        this.put(CommonEnum.GoodsType.GT_SELECT, new SelectChargeGoods());
        this.put(CommonEnum.GoodsType.GT_RANDOM, new RandomChargeGoods());
        this.put(CommonEnum.GoodsType.GT_FESTIVAL_BP, new FestivalBpGoods());
        this.put(CommonEnum.GoodsType.GT_CACT_PAY_POOL, new ContinuesActPayPoolGoods());
        this.put(CommonEnum.GoodsType.GT_CONTINUES_GIFT, new ContinuesGiftGoods());
        this.put(CommonEnum.GoodsType.GT_SEASON_BATTLEPASS, new SeasonBattlePassGoods());
    }};


    static Goods goodsOf(CommonEnum.GoodsType type) {
        Goods goods = GOODS_MAP.get(type);
        if (goods == null) {
            throw new GeminiException("goods types not registered.{}", type);
        }
        return goods;
    }

}

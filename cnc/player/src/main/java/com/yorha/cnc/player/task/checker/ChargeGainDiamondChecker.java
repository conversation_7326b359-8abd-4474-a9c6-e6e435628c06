package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerBaseChargeEvent;
import com.yorha.cnc.player.event.task.PlayerBuyGoodsEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.BUY_GOODS_GAIN_DIAMOND;

/**
 * 累计充值获得X黄金
 */
public class ChargeGainDiamondChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerBaseChargeEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerBuyGoodsEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity player = event.getPlayer();
        final int requireDiamond = taskParams.get(0);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                long buyGoodsGainDiamond = player.getStatisticComponent().getSingleStatistic(BUY_GOODS_GAIN_DIAMOND);
                long saveAmt = player.getPaymentComponent().getSaveAmt();
                prop.setProcess((int) Math.min(requireDiamond, buyGoodsGainDiamond + saveAmt));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerBaseChargeEvent) {
                    PlayerBaseChargeEvent e = (PlayerBaseChargeEvent) event;
                    long saveAmtInc = Math.max(0, e.getSaveAmtNow() - e.getSaveAmtBefore());
                    prop.setProcess((int) Math.min(requireDiamond, prop.getProcess() + saveAmtInc));
                } else if (event instanceof PlayerBuyGoodsEvent) {
                    final int directDiamond = ((PlayerBuyGoodsEvent) event).getDirectDiamond();
                    prop.setProcess(Math.min(requireDiamond, prop.getProcess() + directDiamond));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= requireDiamond;
    }
}

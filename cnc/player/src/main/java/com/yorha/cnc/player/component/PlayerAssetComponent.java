package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.asset.*;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.shop.ShopDataTemplateService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 封装对道具、货币等玩家财产的操作
 * <AUTHOR>
 */
public class PlayerAssetComponent extends PlayerComponent {

    private static final Logger LOGGER = LogManager.getLogger(PlayerAssetComponent.class);

    public PlayerAssetComponent(PlayerEntity owner) {
        super(owner);
    }

    public ErrorCode verify(AssetPackage consume) {
        if (consume.hasStandaloneAsset()) {
            LOGGER.error("AssetPackage hasStandaloneAsset, cannot consume!  {} {}", getOwner(), consume);
            return ErrorCode.SEPARATE_ASSETS_ALREADY_EXIST;
        }

        if (!getOwner().getItemComponent().hasEnoughAll(consume)) {
            return ErrorCode.ITEM_NOT_ENOUGH;
        }
        ErrorCode code = getOwner().getPurseComponent().isEnoughAll(consume);
        if (code.isNotOk()) {
            return code;
        }
        return ErrorCode.OK;
    }

    public boolean hasEnough(AssetPackage consume) {
        return verify(consume).isOk();
    }

    public void verifyThrow(AssetPackage consume) {
        ErrorCode code = verify(consume);
        if (!code.isOk()) {
            LOGGER.warn("item not enough,consume:{}, {}", this.getEntityId(), consume);
            throw new GeminiException(code);
        }
    }

    public void consume(AssetPackage consume, CommonEnum.Reason reason) {
        consume(consume, reason, "");
    }

    public void consume(AssetPackage consume, CommonEnum.Reason reason, String subReason) {
        consume.forEach(new AssetConsumer() {
            @Override
            public void acceptItem(ItemDesc itemDesc) {
                getOwner().getItemComponent().consume(itemDesc, reason, subReason);
            }

            @Override
            public void acceptCurrency(CurrencyDesc currencyDesc) {
                getOwner().getPurseComponent().consume(currencyDesc.getCurrencyType(), currencyDesc.getAmount(), reason, subReason);
            }
        });
    }

    public void give(AssetPackage reward, CommonEnum.Reason reason) {
        give(reward, reason, "");
    }

    public void give(AssetPackage reward, CommonEnum.Reason reason, String subReason) {
        reward.forEach(new AssetConsumer() {
            @Override
            public void acceptItem(ItemDesc itemDesc) {
                getOwner().getItemComponent().addItem(itemDesc, reason, subReason);
            }

            @Override
            public void acceptCurrency(CurrencyDesc currencyDesc) {
                getOwner().getPurseComponent().give(currencyDesc.getCurrencyType(), currencyDesc.getAmount(), reason, subReason);
            }
        });
    }

    /**
     * 资源道具 to 金币
     */
    public Pair<AssetPackage, AssetPackage> calcAssetToGold(AssetPackage assetPackage) { // 计算钻石消耗
        AssetPackage.Builder cost = AssetPackage.builder();
        AssetPackage.Builder remain = AssetPackage.builder();
        for (AssetDesc desc : assetPackage.getImmutableAssets()) {
            if (desc instanceof CurrencyDesc) {
                Pair<AssetPackage, AssetPackage> cost2RemainAssetPackPair = calcCurrencyToGold((CurrencyDesc) desc);
                cost.plus(cost2RemainAssetPackPair.getFirst());
                remain.plus(cost2RemainAssetPackPair.getSecond());
            }
            if (desc instanceof ItemDesc) {
                LOGGER.error("cont cover item, asset={}", assetPackage);
            }
        }
        return Pair.of(cost.build(), remain.build());
    }

    /**
     * 时间 to 金币
     */
    public AssetPackage calcTimeMsToGold(long costTimeMs) {
        if (costTimeMs <= 0) {
            LOGGER.error("calcTimeMsToGold but time is 0");
            return AssetPackage.EMPTY;
        }
        Pair<AssetPackage, AssetPackage> assetPackageAssetPackagePair =
                ResHolder.getResService(ShopDataTemplateService.class).conversionGold(CommonEnum.ExchangeType.ET_TIME_SEC, (int) TimeUtils.ms2Second(costTimeMs));
        return assetPackageAssetPackagePair.getFirst();
    }

    /**
     * 资源 to 金币
     *
     * @return Pair<cost, remain> 消耗 to 多余归还资源
     */
    private Pair<AssetPackage, AssetPackage> calcCurrencyToGold(CurrencyDesc desc) {
        ShopDataTemplateService resService = ResHolder.getResService(ShopDataTemplateService.class);
        CommonEnum.ExchangeType exchangeType = resService.getExchangeByCurrencyType(desc.getCurrencyType());
        AssetPackage.Builder cost = AssetPackage.builder();
        AssetPackage.Builder remain = AssetPackage.builder();

        long has = getOwner().getPurseComponent().count(desc.getCurrencyType());
        long need2buy = desc.getAmount() - has;
        if (need2buy > 0) {
            Pair<AssetPackage, AssetPackage> assetPackagePair = resService.conversionGold(exchangeType, need2buy);
            cost.plus(assetPackagePair.getFirst());
            if (assetPackagePair.getSecond() != null) {
                remain.plus(assetPackagePair.getSecond());
            }
        }
        long costCurrency = Math.min(desc.getAmount(), has);
        if (costCurrency > 0) {
            cost.plusCurrency(desc.getCurrencyType(), costCurrency);
        }
        return Pair.of(cost.build(), remain.build());
    }
}

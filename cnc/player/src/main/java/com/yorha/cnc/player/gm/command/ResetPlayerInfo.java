package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 重置玩家信息（名字和昵称）
 *
 * <AUTHOR>
 */
public class ResetPlayerInfo implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int quietSecondTime = Integer.parseInt(args.get("quietSecondTime"));
        boolean resetName = Boolean.parseBoolean(args.get("resetName"));
        boolean resetPic = Boolean.parseBoolean(args.get("resetPic"));
        actor.getEntity().getProfileComponent().resetPlayerInfo(resetName, resetPic, quietSecondTime);
    }

    @Override
    public String showHelp() {
        return "ResetPlayerInfo resetName={}, resetPic={}, quietSecondTime={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

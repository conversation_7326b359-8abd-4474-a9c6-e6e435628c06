package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PositionMarkInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPositionMark;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.Struct;

/**
 * 坐标收藏与分享
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_SCENE_PLAYER)
public class PlayerPositionMarkController {

    /**
     * 坐标标记操作
     *
     * @param playerEntity
     * @param msg
     * @return
     */
    @CommandMapping(code = MsgType.PLAYER_POSITIONMARK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerPositionMark.Player_PositionMarck_C2S msg) {
        // 打包参数
        Struct.PositionMarkInfo.Builder positionMarkInfo = Struct.PositionMarkInfo.newBuilder();
        positionMarkInfo.setMarkId(msg.getMarkId()).setMarkPicId(msg.getMarkPic()).setMarkName(msg.getMarkName());
        if (msg.hasPoint()) {
            positionMarkInfo.getPointBuilder().setX(msg.getPoint().getX()).setY(msg.getPoint().getY());
        }
        positionMarkInfo.setZoneId(msg.getZoneId());
        // 校验操作合法性
        playerEntity.getPositionMarkComponent().checkAvalid(msg.getActionType(), msg.getPositionMarkType(), positionMarkInfo.build(), msg.getShareType());
        // 操作
        playerEntity.getPositionMarkComponent().markPosition(msg.getActionType(), msg.getPositionMarkType(), positionMarkInfo.build());
        // 回包
        return PlayerPositionMark.Player_PositionMarck_S2C.getDefaultInstance();
    }


    /**
     * 拉取联盟坐标
     *
     * @param playerEntity
     * @param msg
     * @return
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANPOSITIONMARK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerPositionMark.Player_FetchClanPositionMark_C2S msg) {
        PlayerPositionMark.Player_FetchClanPositionMark_S2C.Builder builder = PlayerPositionMark.Player_FetchClanPositionMark_S2C.newBuilder();
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            return PlayerPositionMark.Player_FetchClanPositionMark_S2C.getDefaultInstance();
        }
        SsClanAttr.FetchClanPositionMarkAsk.Builder ask = SsClanAttr.FetchClanPositionMarkAsk.newBuilder();
        SsClanAttr.FetchClanPositionMarkAns ans = playerEntity.ownerActor().callCurClan(ask.build());
        for (Struct.PositionMarkInfo info : ans.getInfosList()) {
            PositionMarkInfoProp prop = new PositionMarkInfoProp();
            prop.mergeFromSs(info);
            builder.addInfos(prop.getCopyCsBuilder().build());
        }
        return builder.build();
    }
}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerMoveCityRandomEvent;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon.Player_UseItem_S2C;
import com.yorha.proto.PlayerPB.ItemUseResultPB;
import com.yorha.proto.SsSceneCityArmy;
import com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns;
import com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk;
import com.yorha.proto.StructPB.PointPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

/**
 * <AUTHOR>
 * 随机迁城
 */
public class MoveCityRandomUsableItem extends AbstractUsableItem {

    private static final Logger LOGGER = LogManager.getLogger(MoveCityRandomUsableItem.class);

    private Point point;

    public MoveCityRandomUsableItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (num != 1) {
            throw new GeminiException(ErrorCode.ITEM_BATCH_USE_LIMIT);
        }
        SsSceneCityArmy.MoveCityVerifyAsk.Builder call = SsSceneCityArmy.MoveCityVerifyAsk.newBuilder();
        call.setPlayerId(playerEntity.getEntityId()).setMoveType(CommonEnum.MoveCityType.MCT_RANDOM);
        SsSceneCityArmy.MoveCityVerifyAns ans = playerEntity.ownerActor().callCurScene(call.build());
        if (!ErrorCode.isOK(ans.getErrorCode())) {
            throw new GeminiException(ans.getErrorCode());
        }
    }

    @Override
    public CommonEnum.Reason getItemReason(PlayerEntity playerEntity) {
        return CommonEnum.Reason.ICR_MOVE_CITY;
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        LOGGER.info("{} try use move city random", playerEntity);
        try {
            MoveCityRandomAsk.Builder call = MoveCityRandomAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
            MoveCityRandomAns ans = playerEntity.ownerActor().callCurScene(call.build());
            if (!ans.hasX()) {
                return false;
            }
            point = Point.valueOf(ans.getX(), ans.getY());
        } catch (Exception e) {
            if (e instanceof GeminiException) {
                // 多发个错误码
                playerEntity.sendMsgToClient(
                        MsgType.PLAYER_PLAYDIALOG_NTF,
                        MsgHelper.buildErrorMsg(((GeminiException) e).getCodeId())
                );
                return false;
            } else {
                // 算用了吧  也不知道其中发生了啥
                LOGGER.error("MoveCityRandomUsableItem use error", e);
                return true;
            }
        }
        LOGGER.info("player use random move city item {} reward to point {}", this, point);
        new PlayerMoveCityRandomEvent(playerEntity, point).dispatch();
        return true;
    }

    @Override
    public void responseMessage(Player_UseItem_S2C.Builder response) {
        ItemUseResultPB.Builder resultPB = ItemUseResultPB.newBuilder();
        if (point != null) {
            resultPB.setPoint(PointPB.newBuilder().setX(point.getX()).setY(point.getY()).build());
        }
        response.setResult(resultPB);
    }
}

package com.yorha.cnc.player.gm.command.rh;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 预览建筑信息
 */

public class ViewInfo implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int id = Integer.parseInt(args.get("id"));
        actor.getEntity().getInnerBuildRhComponent().buildInfoByViewId(id);

    }

    @Override
    public String showHelp() {
        return "ViewInfo id={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_INNER_BUILD;
    }
}

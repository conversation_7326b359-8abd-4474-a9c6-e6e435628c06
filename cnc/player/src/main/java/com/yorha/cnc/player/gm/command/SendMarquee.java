package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.DebugGroup;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.CommonEnum.MarqueeType;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.SsSceneMarquee;
import com.yorha.proto.Struct;

import java.util.Map;

/**
 * 跑马灯GM
 *
 * <AUTHOR>
 * @date 2022/02/09 16:30
 */
public class SendMarquee implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String type = args.get("type");
        String subType = args.get("id");
        Integer marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(MarqueeType.CREATE_CLAN);
        Struct.DisplayData displayData = buildDisplay();
        switch (type) {
            case "server": {
                SsSceneMarquee.SendSceneMarqueeCmd cmd = SsSceneMarquee.SendSceneMarqueeCmd.newBuilder().setMarqueeId(marqueeId)
                        .setDisplayData(displayData).build();
                actor.tellBigScene(cmd);
                break;
            }
            case "player": {
                PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, displayData);
                actor.getEntity().sendMsgToClient(MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
                break;
            }
            case "clan": {
                long clanId = 0L;
                if (StringUtils.isNotEmpty(subType)) {
                    clanId = Long.parseLong(subType);
                }
                if (clanId <= 0L) {
                    clanId = actor.getEntity().getProp().getClan().getClanId();
                }
                if (clanId <= 0L) {
                    throw new GeminiException(ErrorCode.CLAN_ID_NOT_EXIST);
                }
                PlayerScene.Player_MarqueeMessage_NTF.Builder marqueeBuilder = PlayerScene.Player_MarqueeMessage_NTF.newBuilder();
                marqueeBuilder.setDisplayData(MsgHelper.getDisplayDataPB(displayData)).build();
                BroadcastHelper.toCsOnlinePlayerInClan(actor.getZoneId(), clanId, MsgType.PLAYER_MARQUEEMESSAGE_NTF, marqueeBuilder.build());
                break;
            }
            default:
                break;
        }

    }

    public Struct.DisplayData buildDisplay() {
        Struct.DisplayData.Builder builder = Struct.DisplayData.newBuilder();
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText("测试"));
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_ITEM_ID_FOR_NAME, 0));
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText("测试"));
        return builder.build();
    }

    @Override
    public String showHelp() {
        return "SendMarquee type={server|player|clan} id={}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_COMMON;
    }
}

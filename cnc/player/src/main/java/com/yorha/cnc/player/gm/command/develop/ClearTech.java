package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * 玩家科技
 *
 * <AUTHOR>
 */
public class ClearTech implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        actor.getEntity().getTechComponent().clearAllTechByGm();
    }

    @Override
    public String showHelp() {
        return "ClearTech";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

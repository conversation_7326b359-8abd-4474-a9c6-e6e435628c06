package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.RecruitLeagueHeroEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * 招募英雄触发器
 *
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_RECRUIT_LEGEND_HERO)
public class RecruitLegendHero extends AbstractActivityTrigger {
    private static final Logger LOGGER = LogManager.getLogger(RecruitLegendHero.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            RecruitLeagueHeroEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        final RecruitLeagueHeroEvent recruitEvent = (RecruitLeagueHeroEvent) event;
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        if (params == null) {
            return false;
        }
        final int configTimes = params.get(CommonEnum.ActTriggerParamType.ATPT_RECRUIT_TIMES);
        long prevProcess = triggerInfoProp.getProcess();
        if (prevProcess == configTimes) {
            LOGGER.info("RecruitLegendHero triggerHappen, process already finish");
            return false;
        }
        triggerInfoProp.setProcess(Math.min(prevProcess + recruitEvent.getRecruitTime(), configTimes));
        LOGGER.info("RecruitLegendHero triggerHappen, prevProcess={}, target={}, addTimes={}, curProcess={}", prevProcess, configTimes, recruitEvent.getRecruitTime(), triggerInfoProp.getProcess());
        boolean trigger = (triggerInfoProp.getProcess() == configTimes);
        if (trigger) {
            triggerInfoProp.setTriggerTime(triggerInfoProp.getTriggerTime() + 1);
            LOGGER.info("RecruitLegendHero onTrigger, triggerTime={}", triggerInfoProp.getTriggerTime());
        }
        return trigger;
    }
}

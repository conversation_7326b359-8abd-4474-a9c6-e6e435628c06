package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerStarTrainingSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerStartLevelUpSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.START_LEVEL_UP_SOLDIER_NUM;
import static com.yorha.common.enums.statistic.StatisticEnum.START_TRAIN_SOLDIER_NUM;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_RECEIVE;

/**
 * 开始训练兵种X个
 * param1: 兵种类型 0代表任意兵种
 * param2: 个数
 *
 * <AUTHOR>
 */
public class StarTrainingSoldierChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerStarTrainingSoldierEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            PlayerStartLevelUpSoldierEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int soldierTypeConfig = taskParams.get(0);
        int countConfig = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            int trainNum;
            if (soldierTypeConfig == 0) {
                trainNum = (int) event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(START_TRAIN_SOLDIER_NUM);
                trainNum += event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(START_LEVEL_UP_SOLDIER_NUM);
            } else {
                trainNum = (int) event.getPlayer().getStatisticComponent().getSecondStatistic(START_TRAIN_SOLDIER_NUM, soldierTypeConfig);
                trainNum += event.getPlayer().getStatisticComponent().getSecondStatistic(START_LEVEL_UP_SOLDIER_NUM, soldierTypeConfig);
            }
            prop.setProcess(Math.min(trainNum, countConfig));
        } else if (taskTemplate.getTaskCalculationMethod() == TCT_RECEIVE) {
            if (event instanceof PlayerStarTrainingSoldierEvent) {
                PlayerStarTrainingSoldierEvent starTrainingSoldierEvent = (PlayerStarTrainingSoldierEvent) event;
                if (soldierTypeConfig == starTrainingSoldierEvent.getSoldier() || soldierTypeConfig == 0) {
                    prop.setProcess(Math.min(prop.getProcess() + starTrainingSoldierEvent.getCount(), countConfig));
                }
            } else if (event instanceof PlayerStartLevelUpSoldierEvent) {
                PlayerStartLevelUpSoldierEvent ee = (PlayerStartLevelUpSoldierEvent) event;
                if (soldierTypeConfig == 0 || soldierTypeConfig == ee.getSoldierType()) {
                    prop.setProcess(Math.min(prop.getProcess() + ee.getNum(), countConfig));
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }
}

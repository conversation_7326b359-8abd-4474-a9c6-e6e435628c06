package com.yorha.cnc.player.activity;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.activity.BaseActivityUnit;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import res.template.ActivityTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 活动中的复用单元
 */
public abstract class BasePlayerActivityUnit implements BaseActivityUnit {

    protected final PlayerActivity ownerActivity;
    protected final ActivityUnitProp unitProp;

    public BasePlayerActivityUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        this.ownerActivity = ownerActivity;
        this.unitProp = unitProp;
    }

    public void init(CommonEnum.ActivityUnitType unitType) {
        if (unitProp.getUnitId() > 0) {
            return;
        }
        unitProp.setUnitId(ownerActivity.allocateUnitId());
        unitProp.setUnitType(unitType);
    }

    public PlayerEntity player() {
        return ownerActivity.getPlayer();
    }

    public abstract void onMigrate();

    @Override
    public void expire() {
        onExpire();
        cancelListen();
    }

    private void cancelListen() {
        if (this instanceof PlayerEventListener) {
            PlayerEventListener asListener = (PlayerEventListener) this;
            for (Class<? extends PlayerEvent> eClazz : asListener.followList()) {
                ownerActivity.getPlayer().getActivityComponent().cancelListen(eClazz, asListener);
            }
        }
    }

    /**
     * 过期淘汰unit，注销自身的一些逻辑
     */
    public abstract void onExpire();

    /**
     * 强制下架时卸载一些内存中的容器、定时器什么的即可，不要做多余的业务处理，不要自作聪明
     */
    public abstract void forceOffImpl();

    /**
     * 标记本unit的活动内容是否被玩家消费完毕（比如所有任务都完成并领取了），用来处理一些活动的自动下线（在活动时间到期之前就下线）
     */
    public abstract boolean isFinished();

    /**
     * 处理领奖接口
     */
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        throw new GeminiException(ErrorCode.ACTIVITY_REWARD_TYPE_NOT_EXIST);
    }

    public int getUnitId() {
        return unitProp.getUnitId();
    }

    public void onUnitFinished() {
        ownerActivity.tryExpireOnUnitFinished();
    }

    @Override
    public void forceOff() {
        cancelListen();
        forceOffImpl();
    }

    public ActivityTemplate getTemplate() {
        return ownerActivity.getTemplate();
    }

    public ActorTimer addActTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return player().ownerActor().addTimer(player().getPlayerId() + "#" + ownerActivity.getActivityId(), timerReasonType, runnable, initialDelay, unit);
    }
}

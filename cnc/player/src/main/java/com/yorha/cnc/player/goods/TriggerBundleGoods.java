package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.unit.PlayerLostTriggerBundleUnit;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.game.gen.prop.ResellTriggerBundleOrderParamProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;


/**
 * 糊脸礼包
 */
public class TriggerBundleGoods implements Goods {

    private static final Logger LOGGER = LogManager.getLogger(TriggerBundleGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        if (msg.getOrderParam().hasTriggerBundleParam()) {
            final long bundleId = msg.getOrderParam().getTriggerBundleParam().getTriggerBundleId();
            owner.getTriggerBundleComponent().checkApplyOrder(bundleId, goodsTemplate.getId());
        } else if (msg.getOrderParam().hasResellTriggerBundleParam()) {
            StructPB.ResellTriggerBundleOrderParamPB param = msg.getOrderParam().getResellTriggerBundleParam();
            PlayerLostTriggerBundleUnit unit = findLostTriggerBundleUnit(owner, param.getActId(), param.getUnitId());
            unit.checkApply(param.getTriggerBundleId());
        } else {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    @NotNull
    private static PlayerLostTriggerBundleUnit findLostTriggerBundleUnit(PlayerEntity owner, int actId, int unitId) {
        BasePlayerActivityUnit unit = owner.getActivityComponent().findActiveUnit(actId, unitId);
        if (!(unit instanceof PlayerLostTriggerBundleUnit)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return (PlayerLostTriggerBundleUnit) unit;
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        if (msg.getOrderParam().hasTriggerBundleParam()) {
            orderProp.getExtInfo().getTriggerBundleOrderParam()
                    .setTriggerBundleId(msg.getOrderParam().getTriggerBundleParam().getTriggerBundleId());
        } else if (msg.getOrderParam().hasResellTriggerBundleParam()) {
            orderProp.getExtInfo().getResellTriggerBundleOrderParam()
                    .mergeFromCs(msg.getOrderParam().getResellTriggerBundleParam());
        }
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {

    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        if (goodsOrder.getExtInfo().getTriggerBundleOrderParam().getTriggerBundleId() > 0) {
            owner.getTriggerBundleComponent().afterGoodsBought(goodsOrder.getExtInfo().getTriggerBundleOrderParam().getTriggerBundleId());
        } else if (goodsOrder.getExtInfo().getResellTriggerBundleOrderParam().getTriggerBundleId() > 0) {
            ResellTriggerBundleOrderParamProp param = goodsOrder.getExtInfo().getResellTriggerBundleOrderParam();
            BasePlayerActivityUnit unit = owner.getActivityComponent().findActiveUnit(param.getActId(), param.getUnitId());
            long triggerBundleId = param.getTriggerBundleId();
            if (unit == null) {
                // 很可能是活动过期
                LOGGER.info("afterBaseDeliver lostBundleUnit not found. maybe act expired. {} {}", owner, triggerBundleId);
                owner.getTriggerBundleComponent().tryHandleBoughtLostBundle(triggerBundleId);
            } else if (unit instanceof PlayerLostTriggerBundleUnit) {
                ((PlayerLostTriggerBundleUnit) unit).afterGoodsBought(triggerBundleId);
            } else {
                WechatLog.error("afterBaseDeliver unit not null either PlayerLostTriggerBundleUnit! {} {}", owner, triggerBundleId);
            }
        }
        return Collections.emptyList();
    }
}

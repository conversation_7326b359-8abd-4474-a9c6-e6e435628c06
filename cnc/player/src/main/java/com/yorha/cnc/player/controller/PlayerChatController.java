package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.StructMailPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */

@Controller(module = CommonEnum.ModuleEnum.ME_CHAT)
public class PlayerChatController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerChatController.class);

    /**
     * 拉取聊天数据
     */
    @CommandMapping(code = MsgType.PLAYER_GETCHATMESSAGES_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_GetChatMessages_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        return chatPlayerEntity.getHandleChatComponent().getChatMessages(msg);
    }

    /**
     * 发送聊天
     */
    @CommandMapping(code = MsgType.PLAYER_CHATREQUEST_C2S)
    public void handle(PlayerEntity playerEntity, PlayerChat.Player_ChatRequest_C2S msg, int seqId) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        IActorRef session = playerEntity.ownerActor().sender();
        chatPlayerEntity.getHandleChatComponent().chatRequest(msg.getChatSession(), msg.getType(), msg.getMessageData(),
                (e) -> playerEntity.answerMsgToClient(session, seqId,
                        MsgType.PLAYER_CHATREQUEST_S2C,
                        e,
                        PlayerChat.Player_ChatRequest_S2C.getDefaultInstance()
                ));
    }

    /**
     * 已读聊天
     */
    @CommandMapping(code = MsgType.PLAYER_READCHATMESSAGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_ReadChatMessage_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        // cs协议的readMessage需要检查prop是否存在, 因为cs是异步发送消息，可能chatItem在服务端已经删除了，但是readMessage已经在客户端的异步请求队列中，又会重新创建出一个不完整的chatItem
        if (chatPlayerEntity.getHandleChatComponent().getChatItemOrNull(msg.getChatSession().getChannelType(), msg.getChatSession().getChatChannelId()) != null) {
            chatPlayerEntity.getHandleChatComponent().readMessage(msg.getChatSession(), msg.getIndex());
        } else {
            LOGGER.warn("try to read not exist chatItem's message, chatSession={}", msg.getChatSession());
        }
        return PlayerChat.Player_ReadChatMessage_S2C.getDefaultInstance();
    }

    /**
     * 申请翻译
     */
    @CommandMapping(code = MsgType.PLAYER_TRANSLATETEXT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_TranslateText_C2S msg) {
//        if (msg.getLanguage() == CommonEnum.Language.L_NONE) {
//            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
//        }
//        final String translatedText = TranslateHelper.translate(playerEntity.ownerActor(), msg.getText(), msg.getLanguage());
//        PlayerChat.Player_TranslateText_S2C.Builder builder = PlayerChat.Player_TranslateText_S2C.newBuilder();
//        builder.setText(translatedText).setLanguage(msg.getLanguage());
//        return builder.build();
        throw new GeminiException(ErrorCode.TRANSLATOR_NOT_OPEN);
    }

    /**
     * 创建群聊
     */
    @CommandMapping(code = MsgType.PLAYER_CREATEGROUPCHAT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_CreateGroupChat_C2S msg, int seqId) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getHandleChatComponent().createGroupChat(seqId, msg.getMembersList(), msg.getName());
        return null;
    }

    /**
     * 获取聊天成员id列表
     */
    @CommandMapping(code = MsgType.PLAYER_GETCHATMEMBERLIST_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_GetChatMember_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        return chatPlayerEntity.getMaintenanceComponent().getChatMember(msg);
    }

    /**
     * 批量获取玩家铭牌信息
     */
    @CommandMapping(code = MsgType.PLAYER_GETBATCHPLAYERCACHE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_GetBatchPlayerCache_C2S msg, int seqId) {
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        CardHelper.batchQueryPlayerCardWithClan(playerEntity.ownerActor(), msg.getPlayerIdsList(),
                (map) -> {
                    PlayerChat.Player_GetBatchPlayerCache_S2C.Builder builder = PlayerChat.Player_GetBatchPlayerCache_S2C.newBuilder();
                    builder.putAllPlayerCaches(map);
                    playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_GETBATCHPLAYERCACHE_S2C, null, builder.build());
                });
        return null;
    }

    /**
     * 检查玩家是否在群聊中
     */
    @CommandMapping(code = MsgType.PLAYER_CHECKINGROUPCHAT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_CheckInGroup_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        boolean isIn = chatPlayerEntity.getMaintenanceComponent().isInGroupChat(msg.getChatChannelId());
        PlayerChat.Player_CheckInGroup_S2C.Builder builder = PlayerChat.Player_CheckInGroup_S2C.newBuilder();
        builder.setIsIn(isIn);
        return builder.build();
    }

    /**
     * 邀请玩家加入群聊
     */
    @CommandMapping(code = MsgType.PLAYER_INVITEPLAYERJOINGROUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_InvitePlayer_C2S msg, int seqId) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getMaintenanceComponent().invitePlayer(msg, seqId);
        return null;
    }

    /**
     * 群主移除玩家
     */
    @CommandMapping(code = MsgType.PLAYER_REMOVEPLAYERFROMGROUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_OwnerRemoveMember_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getMaintenanceComponent().removeGroupMember(msg);
        return PlayerChat.Player_OwnerRemoveMember_S2C.getDefaultInstance();
    }

    /**
     * 玩家主动退群
     */
    @CommandMapping(code = MsgType.PLAYER_PLAYERQUITGROUPCHAT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_QuitGroupChat_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getMaintenanceComponent().playerQuitGroup(msg.getGroupChatId());
        return PlayerChat.Player_QuitGroupChat_S2C.getDefaultInstance();
    }

    /**
     * 转让群主
     */
    @CommandMapping(code = MsgType.PLAYER_PLAYERTRANSFERGROUPOWNER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_TransferGroupOwner_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getMaintenanceComponent().transferGroupOwner(msg);
        return PlayerChat.Player_TransferGroupOwner_S2C.getDefaultInstance();
    }

    /**
     * 解散群聊
     */
    @CommandMapping(code = MsgType.PLAYER_DISMISSGROUPCHAT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_DismissGroupChat_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getMaintenanceComponent().dismissChatGroup(msg.getPlayerId(), msg.getGroupChatId());
        return PlayerChat.Player_DismissGroupChat_S2C.getDefaultInstance();
    }

    /**
     * 更改群名
     */
    @CommandMapping(code = MsgType.PLAYER_PLAYERMODIFYGROUPNAME_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_ModifyGroupName_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getMaintenanceComponent().modifyGroupName(msg.getGroupChatId(), msg.getNewName());
        return PlayerChat.Player_ModifyGroupName_S2C.getDefaultInstance();
    }

    /**
     * 消息免打扰
     */
    @CommandMapping(code = MsgType.PLAYER_OPENCHATNOTDISTURB_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_ChatNotDisturb_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        ChatItemProp chatItemProp = chatPlayerEntity.getHandleChatComponent().getChatItemOrException(msg.getChatSession().getChannelType(), msg.getChatSession().getChatChannelId());
        chatItemProp.setNotDisturb(true);
        return PlayerChat.Player_ChatNotDisturb_S2C.getDefaultInstance();
    }

    /**
     * 关闭消息免打扰
     */
    @CommandMapping(code = MsgType.PLAYER_CLOSECHATNOTDISTURB_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_CloseChatNotDisturb_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        ChatItemProp chatItemProp = chatPlayerEntity.getHandleChatComponent().getChatItemOrException(msg.getChatSession().getChannelType(), msg.getChatSession().getChatChannelId());
        chatItemProp.setNotDisturb(false);
        return PlayerChat.Player_CloseChatNotDisturb_S2C.getDefaultInstance();
    }

    /**
     * 设置玩家的消息免打扰
     */
    @CommandMapping(code = MsgType.PLAYER_SETPLAYERNOTDISTURB_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_PlayerNotDisturb_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        ChatPlayerProp prop = chatPlayerEntity.getProp();
        if (prop.getNotDisturbList().contains(msg.getPlayerId())) {
            return PlayerChat.Player_PlayerNotDisturb_S2C.getDefaultInstance();
        }
        prop.getNotDisturbList().add(msg.getPlayerId());
        // 遍历玩家的私聊，检查是否有和该玩家的私聊频道，有设置该频道为免打扰
        ChannelInfoProp channelInfoProp = prop.getChannelInfo().get(CommonEnum.ChatChannel.CC_PRIVATE_VALUE);
        if (channelInfoProp == null) {
            return PlayerChat.Player_PlayerNotDisturb_S2C.getDefaultInstance();
        }
        for (String chatId : channelInfoProp.getItem().keySet()) {
            final long[] split = ChatHelper.getPrivateChatRelatedPlayerIds(chatId);
            if (split[0] == msg.getPlayerId() || split[1] == msg.getPlayerId()) {
                ChatItemProp chatItemProp = channelInfoProp.getItemV(chatId);
                chatItemProp.setNotDisturb(true);
                LOGGER.info("ChatLog : chat with player {} set to not disturb", msg.getPlayerId());
                break;
            }
        }
        return PlayerChat.Player_PlayerNotDisturb_S2C.getDefaultInstance();
    }

    /**
     * 解除玩家的消息免打扰
     */
    @CommandMapping(code = MsgType.PLAYER_UNSETPLAYERNOTDISTURB_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_ClosePlayerNotDisturb_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        ChatPlayerProp prop = chatPlayerEntity.getProp();
        if (!prop.getNotDisturbList().contains(msg.getPlayerId())) {
            return PlayerChat.Player_ClosePlayerNotDisturb_S2C.getDefaultInstance();
        }
        prop.getNotDisturbList().remove(msg.getPlayerId());
        // 遍历玩家的私聊，检查是否有和该玩家的私聊频道，存在则解除免打扰状态
        ChannelInfoProp channelInfoProp = prop.getChannelInfo().get(CommonEnum.ChatChannel.CC_PRIVATE_VALUE);
        if (channelInfoProp == null) {
            return PlayerChat.Player_ClosePlayerNotDisturb_S2C.getDefaultInstance();
        }
        for (String chatId : channelInfoProp.getItem().keySet()) {
            final long[] split = ChatHelper.getPrivateChatRelatedPlayerIds(chatId);
            if (split[0] == msg.getPlayerId() || split[1] == msg.getPlayerId()) {
                ChatItemProp chatItemProp = channelInfoProp.getItemV(chatId);
                chatItemProp.setNotDisturb(false);
                LOGGER.info("ChatLog : chat with player {} unset to not disturb", msg.getPlayerId());
                break;
            }
        }
        return PlayerChat.Player_PlayerNotDisturb_S2C.getDefaultInstance();
    }

    /**
     * 玩家主动删除聊天
     */
    @CommandMapping(code = MsgType.PLAYER_DELETECHATITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_DeleteChatItem_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        CommonEnum.ChatChannel chatChannel = msg.getChatSession().getChannelType();
        String channelId = msg.getChatSession().getChatChannelId();
        if (chatChannel == CommonEnum.ChatChannel.CC_SERVER || chatChannel == CommonEnum.ChatChannel.CC_CLAN) {
            throw new GeminiException(ErrorCode.CHAT_ITEM_CAN_NOT_DELETE);
        }
        // 判断chatItem是否存在
        ChatItemProp chatItemProp = chatPlayerEntity.getHandleChatComponent().getChatItemOrException(chatChannel, channelId);
        if (chatChannel == CommonEnum.ChatChannel.CC_GROUP) {
            chatItemProp.setHide(true);
        } else {
            chatPlayerEntity.getHandleChatComponent().clearChannelData(chatChannel, channelId);
        }
        return PlayerChat.Player_DeleteChatItem_S2C.getDefaultInstance();
    }

    /**
     * 显示隐藏的群聊
     */
    @CommandMapping(code = MsgType.PLAYER_SETGROUPCHATSHOW_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_ShowGroupChat_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        ChatItemProp chatItemProp = chatPlayerEntity.getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, msg.getGroupChatId());
        chatItemProp.setHide(false);
        return PlayerChat.Player_ShowGroupChat_S2C.getDefaultInstance();
    }

    /**
     * 获得分享邮件的内容
     */
    @CommandMapping(code = MsgType.PLAYER_GETSHAREDMAILCONTENT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerChat.Player_GetSharedMailContent_C2S msg) {
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        final StructMailPB.MailContentPB mailContent = chatPlayerEntity.getDbComponent().getChatMailContent(msg);
        return PlayerChat.Player_GetSharedMailContent_S2C.newBuilder()
                .setContent(mailContent)
                .build();
    }
}


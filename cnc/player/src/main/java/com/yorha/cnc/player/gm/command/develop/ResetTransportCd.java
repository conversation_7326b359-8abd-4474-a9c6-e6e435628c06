package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerPlaneComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * 重置运输机CD
 *
 * <AUTHOR>
 */
public class ResetTransportCd implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity entity = actor.getEntity();
        PlayerPlaneComponent planeComponent = entity.getPlaneComponent();

        while (true) {
            long planeId = planeComponent.getMaxCdTransport();
            if (planeId <= 0) {
                return;
            }
            planeComponent.resetTransportCd(planeId);
        }
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "ResetTransportCd";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_HERO_PLANE_SOLDIER;
    }
}

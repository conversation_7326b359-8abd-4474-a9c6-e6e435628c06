package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

/**
 * 加若干士兵
 */
public class AddSoldierItem extends AbstractUsableItem {

    private static final Logger LOGGER = LogManager.getLogger(AddSoldierItem.class);

    private final int soldierId;
    private final int addNum;

    public AddSoldierItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
        this.soldierId = itemTemplate.getEffectId();
        this.addNum = itemTemplate.getEffectValue() * num;
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        SsScenePlayer.PlayerAddSoldierAsk.Builder call = SsScenePlayer.PlayerAddSoldierAsk.newBuilder();
        call.setPlayerId(playerEntity.getPlayerId())
                .setSoldierId(this.soldierId)
                .setAddNum(this.addNum)
                .setReason(SoldierNumChangeReason.use_soldier_item.toString());
        playerEntity.ownerActor().callBigScene(call.build());
        LOGGER.debug("player use addSoldierItem {} {} {}", soldierId, num, addNum);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

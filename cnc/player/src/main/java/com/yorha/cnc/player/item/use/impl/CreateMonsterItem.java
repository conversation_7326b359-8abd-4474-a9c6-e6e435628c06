package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.*;
import res.template.ConstTemplate;
import res.template.ItemTemplate;

/**
 * 刷新野怪道具
 * <AUTHOR>
 */
public class CreateMonsterItem extends AbstractUsableItem {

    public CreateMonsterItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }
    private Point point;
    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        SsSceneObj.GetMonsterNumAsk.Builder ask = SsSceneObj.GetMonsterNumAsk.newBuilder();
        ask.setShapeType(CommonEnum.ShapeType.ST_CIRCLE);
        ItemTemplate template = getTemplate();
        ask.setParam(template.getEffectValue());
        ask.addTemplateIds(template.getEffectId());
        ask.setPlayerId(playerEntity.ownerActor().getPlayerId());
        SsSceneObj.GetMonsterNumAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        int num = ans.getNum();
        if (num > ResHolder.getConsts(ConstTemplate.class).getLuohaMonsterLimit()) {
            throw new GeminiException(ErrorCode.MONSTER_TOO_MANY);
        }
    }


    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        SsSceneObj.RefreshActMonsterAsk.Builder ask = SsSceneObj.RefreshActMonsterAsk.newBuilder();
        ItemTemplate template = getTemplate();
        ask.setLifeTime(template.getEffectValue2())
                .setRange(template.getEffectValue())
                .setPlayerId(playerEntity.ownerActor().getPlayerId())
                .setTemplateId(template.getEffectId());
        SsSceneObj.RefreshActMonsterAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        if (ans.getResult()) {
            point = Point.valueOf(ans.getPoint().getX(), ans.getPoint().getY());
        }
        return ans.getResult();
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        PlayerPB.ItemUseResultPB.Builder resultPb = PlayerPB.ItemUseResultPB.newBuilder();
        if (point != null) {
            resultPb.setPoint(StructPB.PointPB.newBuilder().setX(point.getX()).setY(point.getY()).build());
        }
        response.setResult(resultPb);
    }
}

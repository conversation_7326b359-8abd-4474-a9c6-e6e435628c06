package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.ClanStoreItemInfoProp;
import com.yorha.game.gen.prop.ClanStoreLogItemProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * <AUTHOR>
 */

@Controller(module = CommonEnum.ModuleEnum.ME_STORE)
public class PlayerStoreController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerStoreController.class);

    /**
     * 拉取军需处商店信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCOMMISSARIATSTOREINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerStore.Player_FetchCommissariatStoreInfo_C2S msg) {
        PlayerStore.Player_FetchCommissariatStoreInfo_S2C.Builder res = playerEntity.getCommStoreComponent().getStoreInfo();
        return res.build();
    }

    /**
     * 购买军需处商店道具
     */
    @CommandMapping(code = MsgType.PLAYER_BUYSTOREITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerStore.Player_BuyStoreItem_C2S msg) {
        if (msg.getBuyNum() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int leftCanBuyNum = playerEntity.getCommStoreComponent().buyItemById(msg.getConfigGoodsId(), msg.getBuyNum(), msg.getSPassWord());
        PlayerStore.Player_BuyStoreItem_S2C.Builder res = PlayerStore.Player_BuyStoreItem_S2C.newBuilder();
        res.setLeftCanBuyNum(leftCanBuyNum);
        return res.build();
    }

    /**
     * 购买并使用军需处商店道具，
     */
    @CommandMapping(code = MsgType.PLAYER_BUYANDUSESTOREITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerStore.Player_BuyAndUseStoreItem_C2S msg) {
        if (msg.getBuyNum() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        PlayerStore.Player_BuyAndUseStoreItem_S2C.Builder returnMsg = playerEntity.getCommStoreComponent()
                .buyAndUseItemById(msg.getConfigGoodsId(), msg.getBuyNum(), msg.getParams());
        return returnMsg.build();
    }

    /**
     * 拉取军团商店信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANSTORE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanStore.Player_FetchClanStore_C2S msg) {
        // 参数判断
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (!msg.hasInterfaceType()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (msg.getInterfaceType() == CommonEnum.ClanStoreInterfaceType.CSRT_NONE
                || msg.getInterfaceType() == CommonEnum.ClanStoreInterfaceType.CSRT_RECYCLE) {
            LOGGER.warn("interfaceType wrong");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // ss 协议构造请求
        CommonEnum.ClanStoreInterfaceType interfaceType = msg.getInterfaceType();
        SsClanStore.FetchClanStoreAsk.Builder ask = SsClanStore.FetchClanStoreAsk.newBuilder();
        ask.setInterfaceType(msg.getInterfaceType());
        SsClanStore.FetchClanStoreAns ans = playerEntity.ownerActor().callCurClan(ask.build());

        // 回包构造
        PlayerClanStore.Player_FetchClanStore_S2C.Builder retBuilder = PlayerClanStore.Player_FetchClanStore_S2C.newBuilder();
        // 拷贝进货信息
        if (interfaceType == CommonEnum.ClanStoreInterfaceType.CSRT_STOCK) {
            retBuilder.setClanScore(ans.getClanScore());
            retBuilder.setTerritoryLv(ans.getTerritoryLv());
        }
        // 拷贝购买信息
        for (int itemId : ans.getStoreInfoMap().keySet()) {
            Struct.ClanStoreItemInfo info = ans.getStoreInfoMap().get(itemId);
            ClanStoreItemInfoProp prop = new ClanStoreItemInfoProp();
            prop.mergeFromSs(info);
            retBuilder.putStoreInfo(itemId, prop.getCopyCsBuilder().build());
        }
        return retBuilder.build();
    }

    /**
     * 操作军团商店的道具（购买，进货，回收）
     */
    @CommandMapping(code = MsgType.PLAYER_OPERATECLANSTOREITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanStore.Player_OperateClanStoreItem_C2S msg) {
        // 参数判断
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (!msg.hasItemId() || !msg.hasItemNum() || !msg.hasInterfaceType()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (msg.getItemNum() <= 0) {
            LOGGER.warn("itemNum {} less or equal with 0", msg.getItemNum());
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // ss 协议构造请求或player侧处理
        CommonEnum.ClanStoreInterfaceType interfaceType = msg.getInterfaceType();
        PlayerClanStore.Player_OperateClanStoreItem_S2C.Builder retBuilder = PlayerClanStore.Player_OperateClanStoreItem_S2C.newBuilder();
        switch (interfaceType) {
            case CSRT_BUY:
            case CSRT_STOCK:
                retBuilder.setReturnType(playerEntity.getClanStoreComponent().clanStoreBuyOrStock(interfaceType, msg.getItemId(), msg.getItemNum()));
                break;
            case CSRT_RECYCLE:
                retBuilder.setReturnType(playerEntity.getClanStoreComponent().clanStoreRecycle(msg.getItemId(), msg.getItemNum()));
                break;
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return retBuilder.build();
    }

    /**
     * 拉取军团商店记录
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANSTORERECORD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanStore.Player_FetchClanStoreRecord_C2S msg) {
        // 参数判断
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (!msg.hasInterfaceType()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (msg.getInterfaceType() == CommonEnum.ClanStoreInterfaceType.CSRT_NONE
                || msg.getInterfaceType() == CommonEnum.ClanStoreInterfaceType.CSRT_RECYCLE) {
            LOGGER.warn("interfaceType wrong");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // ss 协议构造、请求
        SsClanStore.FetchClanStoreRecordAsk.Builder ask = SsClanStore.FetchClanStoreRecordAsk.newBuilder();
        ask.setInterfaceType(msg.getInterfaceType());
        SsClanStore.FetchClanStoreRecordAns ans = playerEntity.ownerActor().callCurClan(ask.build());

        // 构建S2C返回
        PlayerClanStore.Player_FetchClanStoreRecord_S2C.Builder retBuilder = PlayerClanStore.Player_FetchClanStoreRecord_S2C.newBuilder();
        for (Struct.ClanStoreLogItem logItem : ans.getRecordsList()) {
            ClanStoreLogItemProp prop = new ClanStoreLogItemProp();
            prop.mergeFromSs(logItem);
            retBuilder.addRecords(prop.getCopyCsBuilder());
        }

        return retBuilder.build();
    }
}

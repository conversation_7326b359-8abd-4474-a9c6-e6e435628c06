package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.PlayerManualCityFallEvent;
import com.yorha.cnc.player.event.PlayerMoveCityFixedEvent;
import com.yorha.cnc.player.event.PlayerMoveCityRandomEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.ClanTerritoryChangeEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneClan;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;


public class PlayerInClanTerritoryChecker extends AbstractTaskChecker {
    public static List<String> attentionList = Lists.newArrayList(
            PlayerJoinClanEvent.class.getSimpleName(),
            ClanTerritoryChangeEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            PlayerMoveCityFixedEvent.class.getSimpleName(),
            PlayerMoveCityRandomEvent.class.getSimpleName(),
            PlayerManualCityFallEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int countConfig = 1;
        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            // 剪枝：任务已完成
            if (prop.getProcess() >= countConfig) {
                return true;
            }

            // 查询是否在领土上
            long playerId = event.getPlayer().getPlayerId();
            SsSceneClan.CheckPlayerCityInTerritoryAsk.Builder ask = SsSceneClan.CheckPlayerCityInTerritoryAsk.newBuilder().setPlayerId(playerId);
            SsSceneClan.CheckPlayerCityInTerritoryAns ans = event.getPlayer().ownerActor().callBigScene(ask.build());

            int process = ans.getIsInTerritory() ? 1 : 0;
            prop.setProcess(Math.max(process, prop.getProcess()));
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }

        return prop.getProcess() >= countConfig;
    }
}
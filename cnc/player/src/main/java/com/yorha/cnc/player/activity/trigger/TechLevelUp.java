package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.TechResearchEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_TECH_LEVEL_UP)
public class TechLevelUp extends AbstractActivityTrigger {
    private static final Logger LOGGER = LogManager.getLogger(TechLevelUp.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            TechResearchEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        final PlayerEntity playerEntity = event.getPlayer();
        if (params == null) {
            return false;
        }
        final int techId = params.get(CommonEnum.ActTriggerParamType.ATPT_TECH_ID);
        final int techLevel = params.get(CommonEnum.ActTriggerParamType.ATPT_TECH_LEVEL);
        int playerTechLevel = playerEntity.getTechComponent().getTechLevelByTechId(techId);
        boolean trigger = (techLevel == playerTechLevel);
        if (trigger) {
            triggerInfoProp.setTriggerTime(triggerInfoProp.getTriggerTime() + 1);
            LOGGER.info("TchLevelUp onTrigger, params={}, playerTechLevel={}, triggerTime={}", params, playerTechLevel, triggerInfoProp.getTriggerTime());
        }
        return trigger;
    }
}

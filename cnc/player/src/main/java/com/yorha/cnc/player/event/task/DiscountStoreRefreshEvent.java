package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 黑市刷新事件
 *
 * <AUTHOR>
 */
public class DiscountStoreRefreshEvent extends PlayerTaskEvent {
    private final int refreshNum;

    public int getRefreshNum() {
        return refreshNum;
    }

    public DiscountStoreRefreshEvent(PlayerEntity entity, int refreshNum) {
        super(entity);
        this.refreshNum = refreshNum;
    }

}

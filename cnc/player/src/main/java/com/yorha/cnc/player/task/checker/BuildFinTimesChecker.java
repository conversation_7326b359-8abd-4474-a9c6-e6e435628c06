package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.BuildFullOpenEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 升级建筑x次
 * param1: 次数
 *
 * <AUTHOR>
 */
public class BuildFinTimes<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerInnerBuildLevelUpEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName(), BuildFullOpenEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer times = taskParams.get(0);
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int playerBuildFinTotal = (int) entity.getStatisticComponent().getSingleStatistic(StatisticEnum.INNER_BUILD_TOTAL);
                prop.setProcess(Math.min(playerBuildFinTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerInnerBuildLevelUpEvent) {
                    int levelUpNum = ((PlayerInnerBuildLevelUpEvent) event).getLevelUpNum();
                    prop.setProcess(Math.min(prop.getProcess() + levelUpNum, times));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

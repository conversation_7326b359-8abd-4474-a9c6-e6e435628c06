package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ActivityFestivalBpStatusProp;
import com.yorha.game.gen.prop.ActivityFestivalBpUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.FestivalBpPoolTemplate;
import res.template.FestivalBpTemplate;
import res.template.FpRewardPoolTemplate;

import java.util.List;
import java.util.Map;

/**
 * 节日bp
 *
 * <AUTHOR>
 */
public class PlayerFestivalBpUnit extends BasePlayerActivityUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerFestivalBpUnit.class);


    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_FEASTIVAL_BP, (owner, prop, template) ->
                new PlayerFestivalBpUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerFestivalBpUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    private void init() {
        // 解锁免费的通行证
        final FestivalBpTemplate bpTemplate = getBpTemplate();
        final Map<Integer, FestivalBpPoolTemplate> bpPoolTemplateMap = ResHolder.getInstance().getMap(FestivalBpPoolTemplate.class);
        for (int bpId : bpTemplate.getBpListList()) {
            final ActivityFestivalBpStatusProp bpStatusProp = getProp().getBpStatus().computeIfAbsent(bpId, key -> new ActivityFestivalBpStatusProp().setBpId(bpId));
            bpStatusProp.setHasTakeLevel(0)
                    .setCanTakeLevel(0)
                    .setOpen(false);
            final FestivalBpPoolTemplate bpPoolTemplate = bpPoolTemplateMap.get(bpId);
            if (bpPoolTemplate.getType() != CommonEnum.FestivalBpType.FBPT_FREE) {
                continue;
            }
            tryOpenBp(bpId, "init free");
        }
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        LOGGER.info("PlayerFestivalBpUnit onExpire {}", getActId());
        final StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        final Struct.ItemPairList.Builder itemBuilder = mailSendParams.getItemRewardBuilder();
        for (ActivityFestivalBpStatusProp bpStatusProp : getProp().getBpStatus().values()) {
            if (!bpStatusProp.getOpen()) {
                continue;
            }
            final int bpId = bpStatusProp.getBpId();
            final int canTakeLevel = bpStatusProp.getCanTakeLevel();
            final int hasTakeLevel = bpStatusProp.getHasTakeLevel();
            if (hasTakeLevel >= canTakeLevel) {
                LOGGER.info("PlayerFestivalBpUnit onExpire {} {} all taked {}/{}", getActId(), bpId, hasTakeLevel, canTakeLevel);
                continue;
            }
            // 算一下奖励
            final FestivalBpPoolTemplate bpPoolTemplate = ResHolder.getInstance().getValueFromMap(FestivalBpPoolTemplate.class, bpId);
            if (bpPoolTemplate == null) {
                LOGGER.error("PlayerFestivalBpUnit onExpire {} config not exist {}", getActId(), bpId);
                continue;
            }
            final List<Integer> rewardList = bpPoolTemplate.getLevelRewardList();
            for (int level = hasTakeLevel + 1; level <= canTakeLevel; level++) {
                if (rewardList.size() < level) {
                    LOGGER.error("PlayerFestivalBpUnit onExpire {} level without reward {} {}", getActId(), bpId, level);
                    continue;
                }
                // 标记已领取
                bpStatusProp.setHasTakeLevel(level);
                final Integer reward = bpPoolTemplate.getLevelRewardList().get(level - 1);
                final Struct.ItemPair.Builder itemPair = Struct.ItemPair.newBuilder();
                final FpRewardPoolTemplate template = ResHolder.getInstance().getValueFromMap(FpRewardPoolTemplate.class, reward);
                for (IntPairType item : template.getItemsPairList()) {
                    itemPair.setItemTemplateId(item.getKey())
                            .setCount(item.getValue());
                    itemBuilder.addDatas(itemPair.build());
                }
            }
            LOGGER.info("PlayerFestivalBpUnit onExpire {} {} send reward {}->{}", getActId(), bpId, hasTakeLevel, bpStatusProp.getHasTakeLevel());
        }
        // 发奖
        if (itemBuilder.getDatasCount() <= 0) {
            LOGGER.info("PlayerFestivalBpUnit onExpire {} send reward null", getActId());
            return;
        }
        final FestivalBpTemplate template = getBpTemplate();
        sendMailReward(template.getMailId(), mailSendParams);
    }

    private void sendMailReward(int mailId, StructMail.MailSendParams.Builder mailSendParams) {
        mailSendParams.setMailTemplateId(mailId);
        mailSendParams.getSenderBuilder().setSenderId(0);
        this.player().getMailComponent().sendPersonalMail(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(player().getPlayerId())
                        .setZoneId(player().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final int bpId = key.getBpId();
        final int level = key.getLevel();
        // 是否激活过通行证
        if (!isBpOpen(bpId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit handleTakeReward bp not open " + bpId);
        }
        final ActivityFestivalBpStatusProp status = getBpStatus(bpId);
        final int canTakeLevel = status.getCanTakeLevel();
        // 可领取校验
        if (canTakeLevel < level) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit handleTakeReward not can take " + bpId + "_" + level);
        }
        // 已领取校验
        final int hasTakeLevel = status.getHasTakeLevel();
        if (hasTakeLevel >= level) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit handleTakeReward has take " + bpId + "_" + level);
        }

        for (int rewardLevel = (hasTakeLevel + 1); rewardLevel <= level; rewardLevel++) {
            // 算一下奖励
            final FestivalBpPoolTemplate bpPoolTemplate = ResHolder.getInstance().getValueFromMap(FestivalBpPoolTemplate.class, bpId);
            if (bpPoolTemplate == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit handleTakeReward config not exist " + bpId);
            }
            final List<Integer> rewardList = bpPoolTemplate.getLevelRewardList();
            if (rewardList.size() < rewardLevel) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit handleTakeReward level without reward " + bpId + "_" + rewardLevel);
            }
            final int reward = bpPoolTemplate.getLevelRewardList().get(rewardLevel - 1);
            final AssetPackage.Builder rewardBuilder = AssetPackage.builder();
            final FpRewardPoolTemplate template = ResHolder.getInstance().getValueFromMap(FpRewardPoolTemplate.class, reward);
            for (IntPairType item : template.getItemsPairList()) {
                rewardBuilder.plusItem(item.getKey(), item.getValue());
            }
            // 标记已领取
            status.setHasTakeLevel(rewardLevel);
            // 发奖
            player().getAssetComponent().give(rewardBuilder.build(), CommonEnum.Reason.ICR_FESTIVAL_BP, bpId + "_" + rewardLevel);
            LOGGER.info("PlayerFestivalBpUnit handleTakeReward {} bp {} {}->{}", getActId(), bpId, hasTakeLevel, status.getHasTakeLevel());
        }

    }

    /**
     * 积分道具兑换
     */
    public int exchangeTicket(int num) {
        LOGGER.info("PlayerFestivalBpUnit exchangeTicket {} num {}", getActId(), num);
        if (num <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "exchangeTicket num " + num);
        }
        final FestivalBpTemplate template = getBpTemplate();
        final int ticketId = template.getScoreItem();
        // 积分道具消耗
        final AssetPackage.Builder costBuilder = AssetPackage.builder();
        costBuilder.plusItem(ticketId, num);
        AssetPackage cost = costBuilder.build();
        player().verifyThrow(cost);
        player().consume(cost, CommonEnum.Reason.ICR_FESTIVAL_BP, "fbp_" + ownerActivity.getActivityId());
        // 已经最大等级了，则兑换成奖励道具发给玩家
        final boolean isMaxLevel = isMaxLevel();
        LOGGER.info("PlayerFestivalBpUnit exchangeTicket {} num {} level {} max {}", getActId(), num, getProp().getCurLevel(), isMaxLevel);
        // 已经到最大等级了
        if (isMaxLevel) {
            final List<IntPairType> overExchanges = template.getOverExchangePairList();
            final AssetPackage.Builder rewards = AssetPackage.builder();
            for (IntPairType overExchange : overExchanges) {
                rewards.plusItem(overExchange.getKey(), MathUtils.multiplyExact(overExchange.getValue(), num));
            }
            player().getAssetComponent().give(rewards.build(), CommonEnum.Reason.ICR_FESTIVAL_BP, "fbp_" + ownerActivity.getActivityId());
            LOGGER.info("PlayerFestivalBpUnit exchangeTicket {} over exchange success num {}", getActId(), num);
            return 0;
        }
        // 积分暴击
        int boomRandom = RandomUtils.randomBetween(0, GameLogicConstants.IPPM);
        for (IntPairType suprise : template.getScoreSurprisePairList()) {
            if (boomRandom < suprise.getKey()) {
                final int score = MathUtils.multiplyExact(suprise.getValue(), num);
                addScore(score, "exchange boom");
                return score;
            }
            boomRandom = boomRandom - suprise.getKey();
        }
        // 普通兑换
        addScore(num, "exchange");
        return num;
    }

    public void gmAddScore(int num) {
        addScore(num, "gm");
    }

    /**
     * 加积分
     */
    private void addScore(int num, String reason) {
        LOGGER.info("PlayerFestivalBpUnit addScore {} num {} {}", getActId(), num, reason);
        final ActivityResService resService = ResHolder.getResService(ActivityResService.class);
        final Map<Integer, Integer> levelScoreMap = resService.getFbpLevelScore(getActId());
        if (levelScoreMap == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit addScore config not exist " + getActId());
        }
        final int curLevel = getProp().getCurLevel();
        final Integer score = levelScoreMap.get(curLevel);
        if (score == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit addScore " + getActId() + curLevel + "not exist");
        }
        final int curScore = getProp().getScore();
        if ((curScore + num) >= score) {
            // 升级
            getProp().setCurLevel(curLevel + 1);
            getProp().setScore(curScore + num - score);
            for (ActivityFestivalBpStatusProp bpStatusProp : getProp().getBpStatus().values()) {
                final int oldCanTakeLevel = bpStatusProp.getCanTakeLevel();
                bpStatusProp.setCanTakeLevel(getProp().getCurLevel());
                LOGGER.info("PlayerFestivalBpUnit addScore {} {} can take {}->{}", getActId(), bpStatusProp.getBpId(), oldCanTakeLevel, bpStatusProp.getCanTakeLevel());
            }
            LOGGER.info("PlayerFestivalBpUnit addScore {} level up {}->{} {} {}", getActId(), curLevel, getProp().getCurLevel(), getProp().getScore(), reason);
            return;
        }
        // 等级不变，加积分
        getProp().setScore(curScore + num);
        LOGGER.info("PlayerFestivalBpUnit addScore {} level {} score {}->{} {}", getActId(), curLevel, curScore, getProp().getScore(), reason);
    }

    /**
     * 已经到最大等级了
     */
    private boolean isMaxLevel() {
        final FestivalBpTemplate template = getBpTemplate();
        final int curLevel = getProp().getCurLevel();
        return curLevel >= template.getLevelScoreList().size();
    }

    /**
     * 开通通行证
     */
    public void tryOpenBp(int bpId, String reason) {
        // 校验是否已开启
        if (isBpOpen(bpId)) {
            LOGGER.info("PlayerFestivalBpUnit tryOpenBp repeat {} {} {}", getActId(), bpId, reason);
            return;
        }
        final FestivalBpPoolTemplate bpPoolTemplate = ResHolder.getInstance().getValueFromMap(FestivalBpPoolTemplate.class, bpId);
        // 金条购买扣钱
        if (bpPoolTemplate.getType() == CommonEnum.FestivalBpType.FBPT_GOLD) {
            final AssetPackage.Builder costBuilder = AssetPackage.builder();
            costBuilder.plusCurrency(CommonEnum.CurrencyType.DIAMOND, bpPoolTemplate.getPrice());
            AssetPackage cost = costBuilder.build();
            player().verifyThrow(cost);
            player().consume(cost, CommonEnum.Reason.ICR_FESTIVAL_BP, "consume_open_fbp");
        }
        // 设置通行证状态
        final ActivityFestivalBpStatusProp statusProp = getBpStatus(bpId);
        statusProp.setOpen(true);
        LOGGER.info("PlayerFestivalBpUnit tryOpenBp open {} {} {}", getActId(), bpId, reason);
    }

    public boolean isBpOpen(int bpId) {
        final ActivityFestivalBpStatusProp status = getBpStatus(bpId);
        if (status == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerFestivalBpUnit bp not exist " + bpId);
        }
        return status.getOpen();
    }


    private ActivityFestivalBpStatusProp getBpStatus(int bpId) {
        return getProp().getBpStatus().get(bpId);
    }

    private ActivityFestivalBpUnitProp getProp() {
        return unitProp.getBpUnit();
    }

    private int getActId() {
        return ownerActivity.getActivityId();
    }

    private FestivalBpTemplate getBpTemplate() {
        return ResHolder.getInstance().getValueFromMap(FestivalBpTemplate.class, getActId());
    }

    public FestivalBpPoolTemplate getBpPoolTemplate(int bpId) {
        if (getBpStatus(bpId) == null) {
            return null;
        }
        return ResHolder.getInstance().getValueFromMap(FestivalBpPoolTemplate.class, bpId);
    }

    @Override
    public boolean isFinished() {
        return false;
    }
}

package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskPoolTemplate;
import res.template.TaskPveTemplate;

import java.util.*;
import java.util.stream.Collectors;

public class PveTaskHandler extends AbstractTaskHandler {

    public PveTaskHandler(PlayerEntity entity, CommonEnum.TaskClass tcChapter) {
        super(entity, tcChapter.name());
    }

    @Override
    public void loadAllTask() {
        int chapter = getEntity().getProp().getTaskSystem().getPveChapter();
        int mainTaskId = ResHolder.getResService(TaskTemplateService.class).getPveChapterMainTask(chapter);

        //清理异常数据
        List<Integer> removes = new ArrayList<>();
        for (TaskInfoProp prop : getTaskProp().values()) {
            if (ResHolder.findTemplate(TaskPveTemplate.class, prop.getId()) == null) {
                removes.add(prop.getId());
            }
        }
        for (Integer id : removes) {
            getTaskProp().remove(id);
        }

        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            addTaskEventMonitor(value);
            if (value.getId() == mainTaskId) {
                getTaskProp().get(value.getId()).setIsMainTask(true);
            }
        }
        //补领章节任务或者进入下一章节
        takeChapterTask();
    }

    private boolean canAcceptTask(int taskId) {
        TaskPveTemplate template = ResHolder.findTemplate(TaskPveTemplate.class, taskId);
        return template != null;
    }

    private void takeChapterTask() {
        // 检测任务解锁
        int chapter = getEntity().getProp().getTaskSystem().getPveChapter();
        //获取当前章节任务列表
        Set<Integer> curTasks = ResHolder.getResService(TaskTemplateService.class).getPveChapterTasks(chapter);
        boolean finished = true;

        if (curTasks != null) {
            //检测章节是否有新增加的任务
            Set<Integer> ids = new HashSet<>();
            for (Integer configId : curTasks) {
                if (!getTaskProp().containsKey(configId) && canAcceptTask(configId)) {
                    ids.add(configId);
                }
            }
            if (!ids.isEmpty()) {
                addBatchTask(ids);
                finished = false;
            } else {
                //检测章节是否已经全部领取完奖励
                for (Integer configId : curTasks) {
                    TaskInfoProp taskProp = getTaskProp().get(configId);
                    if (!isFinishState(taskProp)) {
                        finished = false;
                        break;
                    }
                }
            }

            //有任务还没有接取
            if (getTaskProp().size() < curTasks.size()) {
                finished = false;
            }
        }

        //前一章节所有任务奖励都已领取完毕
        if (finished) {
            boolean gotoNext = (!getTaskProp().isEmpty()) || (chapter <= 0);
            //章节任务都完成后，清理所有数据
            getTaskProp().clear();
            //如果当前章节有数据，则进入下一章
            if (gotoNext) {
                int nextChapter = chapter + 1;
                Set<Integer> newTasks = ResHolder.getResService(TaskTemplateService.class).getPveChapterTasks(nextChapter);
                int nextMainId = ResHolder.getResService(TaskTemplateService.class).getPveChapterMainTask(nextChapter);
                if (nextMainId > 0 && canAcceptTask(nextMainId) && newTasks != null) {
                    Set<Integer> ids = new HashSet<>();
                    for (Integer configId : newTasks) {
                        if (canAcceptTask(configId)) {
                            ids.add(configId);
                        }
                    }
                    //加入下一章节的任务
                    addBatchTask(ids);
                    //设置章节主任务
                    TaskInfoProp prop = getTaskProp().get(nextMainId);
                    if (prop != null) {
                        prop.setIsMainTask(true);
                    }
                    //设置章节开始时间
                    getEntity().getProp().getTaskSystem().setPveTaskStartTsMs(SystemClock.now());
                }
                //无论任务是否建立完成，都进入下一章。有可能当前是最后一章，该情况下需要将章节+1.但要阻止它反复+1，所以只能是当前章节有任务的情况下才能+1
                getEntity().getProp().getTaskSystem().setPveChapter(nextChapter);
            }
        }
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> configIdList) {
        Int32TaskInfoMapProp taskChapter = getTaskProp();
        int chapter = getEntity().getProp().getTaskSystem().getPveChapter();
        int mainTaskId = ResHolder.getResService(TaskTemplateService.class).getPveChapterMainTask(chapter);
        //如果领取的任务包括章节任务，则将所有章节任务的奖励一起领取
        for (Integer configId : configIdList) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(taskChapter, configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }

            //如果是章节任务，则遍历所有未领取奖励的任务，将他们加入List
            if (mainTaskId == configId) {
                for (TaskInfoProp prop : taskChapter.values()) {
                    if (!isUnCompletedState(prop)) {
                        if (configIdList.stream().noneMatch(it -> it == prop.getId())) {
                            configIdList.add(prop.getId());
                        }
                    }
                }
                break;
            }
        }

        List<IntPairType> rewardList = new ArrayList<>();
        for (Integer configId : configIdList) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(taskChapter, configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }
            setNextTaskState(taskInfoProp);

            // 领奖
            TaskPveTemplate taskTemplate = ResHolder.getTemplate(TaskPveTemplate.class, configId);
            List<IntPairType> reward = taskTemplate.getRewardPairList();
            AssetPackage assetPackage = AssetPackage.builder().plusItems(reward).build();
            getEntity().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_TASK, formationSubReason(configId));

            rewardList.addAll(reward);
            takeRewardQLog(taskInfoProp);
        }

        //尝试进入下一章节
        takeChapterTask();
        return rewardList;
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return getEntity().getProp().getTaskSystem().getTaskPve();
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum gmType) {
        super.onTaskFinish(configId, gmType);
        // 章节任务完成
        //new ChapterTaskFinishEvent(getEntity(), configId).dispatch();
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(TaskPveTemplate.class, configId).getPVEtaskId();
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    @Override
    public void fullMemoryByGm() {
        Set<Integer> collect = ResHolder.getInstance().getListFromMap(TaskPveTemplate.class).stream().map(TaskPveTemplate::getId).collect(Collectors.toSet());
        addBatchTask(collect);
    }

    @Override
    public void completeTasksWithGm(int chapterId) {
        int chapter = getEntity().getProp().getTaskSystem().getPveChapter();
        while (chapterId <= chapter) {
            for (Map.Entry<Integer, TaskInfoProp> task : getTaskProp().entrySet()) {
                if (isUnCompleted(task.getValue())) {
                    super.completeTasksWithGm(task.getKey());
                }
            }
            checkOrTakeReward(new ArrayList<>(getTaskProp().keySet()));

            takeChapterTask();

            int curChapter = getEntity().getProp().getTaskSystem().getPveChapter();
            if (curChapter == chapter) {
                break;
            } else {
                chapter = curChapter;
            }
        }
    }

}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.DebugSwitchCenter;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 开关
 *
 * <AUTHOR>
 */
public class DebugSwitch implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(DebugSwitch.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String type = args.get("type");
        switch (type) {
            case "createArmyNoSoldierLimit": {
                DebugSwitchCenter.getInstance().setCreateArmyNoSoldierLimit(Boolean.parseBoolean(args.get("value")));
                break;
            }
            default:
                LOGGER.error("debug switch type not found:{}", type);
        }
    }

    @Override
    public String showHelp() {
        return "DebugSwitch type={} xxx=xxx";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }

}

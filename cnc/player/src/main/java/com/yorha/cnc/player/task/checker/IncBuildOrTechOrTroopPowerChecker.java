package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.helper.CheckerHelper;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskPoolTemplate;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 建造，科研，军队训练战力提升
 * param1: 战力值
 *
 * <AUTHOR>
 */
public class IncBuildOrTechOrTroopPowerChecker extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int powerConfig = taskParams.get(0);

        if (event instanceof PlayerPowerIncreaseEvent) {
            PlayerPowerIncreaseEvent powerIncreaseEvent = (PlayerPowerIncreaseEvent) event;
            if (!needCountReason(powerIncreaseEvent.getPowerReason(), powerIncreaseEvent.getSoldierNumChangeReason())) {
                // 如果是不需要计入进度的事件，则不更新进度
                return prop.getProcess() >= powerConfig;
            }
            long newProcess = prop.getProcess() + powerIncreaseEvent.getPower();
            prop.setProcess(Math.min(powerConfig, (int) newProcess));

        }
        return prop.getProcess() >= powerConfig;
    }

    private boolean needCountReason(CommonEnum.PowerType powerReason, @Nullable Integer soldierNumChangeReason) {
        switch (powerReason) {
            // 科技，内城直接返回true
            case PT_TECH:
            case PT_INNER_BUILDING:
                return true;
            case PT_SOLDIER: {
                // 军队看具体的原因
                return CheckerHelper.isUpdatePowerByTrainOrLevelUp(soldierNumChangeReason);
            }
            default:
                // 其余情况均不计入
                return false;
        }
    }
}

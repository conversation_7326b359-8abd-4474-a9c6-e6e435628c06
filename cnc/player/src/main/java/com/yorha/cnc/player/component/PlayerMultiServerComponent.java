package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsZoneCard;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PlayerMultiServerComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerMultiServerComponent.class);

    public PlayerMultiServerComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 获得指定赛季下的服务器列表
     *
     * @param season 服务器赛季（建设中也属于预备赛季）
     * @return Map<Integer, Integer>
     */
    public Map<Integer, Integer> getZonesUnderSeason(final CommonEnum.ZoneSeason season) {
        LOGGER.info("PlayerMultiServerComponent getZonesUnderSeason season={}", season);
        if (season == CommonEnum.ZoneSeason.ZS_BUILDING || season == CommonEnum.ZoneSeason.ZS_NONE) {
            throw new GeminiException("invalid type");
        }

        final SsZoneCard.GetZonesUnderSeasonAsk ask = SsZoneCard.GetZonesUnderSeasonAsk.newBuilder()
                .setZoneSeason(season)
                .build();
        final SsZoneCard.GetZonesUnderSeasonAns ans = this.ownerActor().callZoneCard(ask);

        final Map<Integer, Integer> zoneIds = ans.getZoneIdsMap();
        LOGGER.info("PlayerMultiServerComponent getZonesUnderSeason zoneIds={}", zoneIds);
        return zoneIds;
    }

    public Map<Integer, StructMsg.ZoneStatus> getZonesStatus(final List<Integer> zoneIds) {
        LOGGER.info("PlayerMultiServerComponent getZonesStatus zoneIds={}", zoneIds);

        final SsZoneCard.GetMultiZoneStatusAsk ask = SsZoneCard.GetMultiZoneStatusAsk.newBuilder()
                .addAllZoneIds(zoneIds)
                .build();
        final SsZoneCard.GetMultiZoneStatusAns ans = this.ownerActor().callZoneCard(ask);

        final Map<Integer, StructMsg.ZoneStatus> zoneStatusMap = ans.getServersStatusMap();
        LOGGER.info("PlayerMultiServerComponent getZonesStatus zoneStatusMap={}", zoneStatusMap);
        return zoneStatusMap;
    }
}


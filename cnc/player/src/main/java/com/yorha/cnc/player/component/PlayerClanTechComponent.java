package com.yorha.cnc.player.component;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.task.PlayerClanDonateEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanTechResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ClanTechModelProp;
import com.yorha.game.gen.prop.PlayerClanTechModelProp;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncGuildTechDonation;
import res.template.ClanTechDonateTemplate;
import res.template.ClanTechSubTemplate;
import res.template.ClanTechTemplate;
import res.template.ConstClanTechTemplate;

import static com.yorha.proto.CommonEnum.*;
import static com.yorha.proto.PlayerClanTech.*;
import static com.yorha.proto.SsClanTech.*;

/**
 * 军团科技
 */
public class PlayerClanTechComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanTechComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerClanTechComponent::onDailyRefresh);
    }

    public PlayerClanTechComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerClanTechModelProp prop() {
        return getOwner().getProp().getClan().getClanTechModel();
    }

    public Player_FetchClanTechInfo_S2C handleFetch() {
        ClanTechFetchInfoAsk ask = ClanTechFetchInfoAsk.newBuilder().setPlayerId(getPlayerId()).build();
        ClanTechFetchInfoAns ans = ownerActor().callCurClan(ask);
        ClanTechModelProp prop = new ClanTechModelProp();
        prop.mergeFromSs(ans.getTechModel());
        return Player_FetchClanTechInfo_S2C.newBuilder()
                .setTechModel(prop.getCopyCsBuilder())
                .setMyContributionDaily((int) ans.getContributionDaily())
                .build();
    }

    public GeneratedMessageV3 handleDonate(Player_ClanTechDonate_C2S msg) {
        final int clanTechId = msg.getClanTechId();
        final ClanTechDonateType donateType = msg.getDonateType();
        // 校验techId合法
        ResHolder.getTemplate(ClanTechTemplate.class, clanTechId);

        // 发到clan上check
        ClanTechDonateCheckAsk.Builder checkAsk = ClanTechDonateCheckAsk.newBuilder().setClanTechId(clanTechId);
        ClanTechDonateCheckAns checkAns = ownerActor().callCurClan(checkAsk.build());
        final int clanTechSubId = checkAns.getClanTechSubId();

        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        ClanTechSubTemplate subTemplate = ResHolder.getTemplate(ClanTechSubTemplate.class, clanTechSubId);
        ConstClanTechTemplate consts = ResHolder.getConsts(ConstClanTechTemplate.class);
        PlayerClanTechModelProp prop = prop();
        // 捐献次数限制
        if (donateType == ClanTechDonateType.CTDT_RES) {
            refreshResDonateTimes();
            if (prop.getCanResDonateTimes() <= 0) {
                throw new GeminiException(ErrorCode.CLAN_TECH_DONATE_TIMES_NOT_ENOUGH);
            }
        }
        // 钻石捐献有个加入联盟后的CD限制
        if (donateType == ClanTechDonateType.CTDT_DIAMOND) {
            long nowSec = SystemClock.nowOfSeconds();
            long enterClanSec = TimeUtils.ms2Second(getOwner().getProp().getClan().getEnterTime());
            if (nowSec - enterClanSec < consts.getDiamondDonateCdSec()) {
                throw new GeminiException(ErrorCode.CLAN_TECH_DIAMOND_DONATE_CD);
            }
        }

        // 捐献消耗
        AssetPackage.Builder costBuilder = AssetPackage.builder();
        if (donateType == ClanTechDonateType.CTDT_RES) {
            costBuilder.plusCurrency(subTemplate.getDonateCostResPairList());
        } else if (donateType == ClanTechDonateType.CTDT_DIAMOND) {
            final int dailyDiamondDonateTimes = prop().getDailyDiamondDonateTimes();
            int diamondCost = resService.calcDonateDiamondCost(dailyDiamondDonateTimes);
            costBuilder.plusCurrency(CurrencyType.DIAMOND, diamondCost);
        } else {
            throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
        }
        AssetPackage cost = costBuilder.build();
        getOwner().getAssetComponent().verifyThrow(cost);

        getOwner().getAssetComponent().consume(cost, Reason.ICR_CLAN_TECH_DONATE, String.valueOf(clanTechSubId));

        // 随机倍率
        ClanTechDonateTemplate donateTemplate = resService.randomDonateMagnification(donateType);

        // 捐献次数扣除/计数
        if (donateType == ClanTechDonateType.CTDT_RES) {
            prop.setCanResDonateTimes(prop.getCanResDonateTimes() - 1);
        }
        if (donateType == ClanTechDonateType.CTDT_DIAMOND) {
            prop.setDailyDiamondDonateTimes(prop.getDailyDiamondDonateTimes() + 1);
        }
        ClanTechDonateAsk ask = ClanTechDonateAsk.newBuilder()
                .setClanTechId(clanTechId)
                .setMagnification(donateTemplate.getMagnification())
                .setPlayerId(getPlayerId())
                .build();
        ClanTechDonateAns ans = ownerActor().callCurClan(ask);

        // 当日捐献点
        final int addTechPoint = subTemplate.getTechPoint() * donateTemplate.getMagnification();
        prop.setDailyDonatePoint(prop.getDailyDonatePoint() + addTechPoint);
        // 捐献获得个人积分
        ownerActor().getPlayerClanService().handleOnAddClanScoreCmd(SsPlayerClan.OnAddClanScoreCmd.newBuilder()
                .setScoreType(ClanScoreCategory.CSC_TECH_DONATE)
                .setAddValue(subTemplate.getPersonalPoint() * donateTemplate.getMagnification())
                .build());

        trySendQlog(clanTechId, donateType, clanTechSubId, cost, ans, addTechPoint);

        // 捐献单次仅记作1
        getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.CLAN_TECH_DONATE, 1);
        new PlayerClanDonateEvent(getOwner(), 1).dispatch();

        Player_ClanTechDonate_S2C.Builder response = Player_ClanTechDonate_S2C.newBuilder();
        response.setAfterPoint(ans.getAfterPoint());
        response.setMagnificationTemplateId(donateTemplate.getId());
        return response.build();
    }

    private void trySendQlog(int clanTechId, ClanTechDonateType donateType, int clanTechSubId, AssetPackage cost, ClanTechDonateAns ans, int addTechPoint) {
        try {
            QlogCncGuildTechDonation.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction(donateType == ClanTechDonateType.CTDT_RES ? "guild_donate_resource" : "guild_donate_gold")
                    .setDonateQuantity((int) cost.getImmutableAssets().get(0).getAmount())
                    .setGuildTechID(clanTechId)
                    .setGuildSubTechID(clanTechSubId)
                    .setGuildTechScore(ans.getAfterPoint())
                    .setGuildScoreDiff(addTechPoint)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendClanTechDonateQlogError", e);
        }
    }

    private void refreshResDonateTimes() {
        PlayerClanTechModelProp prop = prop();
        // 从未刷新过，时间按进入联盟的时间戳来算
        if (prop.getLastRefreshTsSec() <= 0) {
            prop.setLastRefreshTsSec((int) TimeUtils.ms2Second(getOwner().getProp().getClan().getEnterTime()));
        }
        long nowSec = SystemClock.nowOfSeconds();
        ConstClanTechTemplate consts = ResHolder.getConsts(ConstClanTechTemplate.class);
        int timesOld = prop.getCanResDonateTimes();
        int maxTimes = consts.getMaxResDonateTimes();
        if (timesOld >= maxTimes) {
            // 已达上限，标记一下时间戳，直接返回
            prop.setLastRefreshTsSec((int) nowSec);
            return;
        }
        int lastRefreshTsSec = prop.getLastRefreshTsSec();
        int recoverySec = consts.getResDonateRecoverySec();
        long deltaSec = nowSec - lastRefreshTsSec;
        long addTimes = deltaSec / recoverySec;
        long timesNew = Math.min(maxTimes, timesOld + addTimes);
        prop.setCanResDonateTimes((int) timesNew);
        if (timesNew < maxTimes) {
            prop.setLastRefreshTsSec((int) (lastRefreshTsSec + (recoverySec * addTimes)));
        } else {
            prop.setLastRefreshTsSec((int) nowSec);
        }
    }

    public void handleResearch(Player_ClanTechResearch_C2S msg) {
        final int clanTechId = msg.getClanTechId();
        // 校验techId合法
        ResHolder.getTemplate(ClanTechTemplate.class, clanTechId);

        // 发到clan上处理
        ClanTechResearchAsk.Builder researchAsk = ClanTechResearchAsk.newBuilder()
                .setClanTechId(clanTechId)
                .setPlayerId(getPlayerId());
        ownerActor().callCurClan(researchAsk.build());
    }

    public void handleRecommend(Player_ClanTechRecommend_C2S msg) {
        final int techId = msg.getTechId();
        if (techId > 0) {
            // 为0表示取消推荐
            ResHolder.getTemplate(ClanTechTemplate.class, techId);
        }
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_SET_TECH_RECOMMEND, getOwner().getPlayerClanComponent().getStaffId());
        ClanTechRecommendAsk ask = ClanTechRecommendAsk.newBuilder()
                .setClanTechId(techId)
                .setPlayerId(getPlayerId())
                .build();
        ownerActor().callCurClan(ask);
    }

    public void onJoinedClan(long enterTimeMs) {
        PlayerClanTechModelProp prop = prop();
        prop.setCanResDonateTimes(0);
        prop.setLastRefreshTsSec((int) TimeUtils.ms2Second(enterTimeMs));
        prop.setDailyDonatePoint(0);
    }

    private static void onDailyRefresh(PlayerDayRefreshEvent event) {
        PlayerClanTechModelProp techModel = event.getPlayer().getProp().getClan().getClanTechModel();
        techModel.setDailyDonatePoint(0);
        techModel.setDailyDiamondDonateTimes(0);
    }

    public Player_ClanTechDetail_S2C handleDetail(Player_ClanTechDetail_C2S msg) {
        int techId = msg.getClanTechId();
        // techId合法
        ResHolder.getTemplate(ClanTechTemplate.class, techId);

        ClanTechDetailAsk ask = ClanTechDetailAsk.newBuilder().setTechId(techId).build();
        ClanTechDetailAns ans = ownerActor().callCurClan(ask);

        return Player_ClanTechDetail_S2C.newBuilder()
                .setTechInfo(StructPB.ClanTechInfoPB.newBuilder()
                        .setTechId(ans.getTechInfo().getTechId())
                        .setTechSubId(ans.getTechInfo().getTechSubId())
                        .setPoint(ans.getTechInfo().getPoint())
                        .build())
                .build();
    }
}
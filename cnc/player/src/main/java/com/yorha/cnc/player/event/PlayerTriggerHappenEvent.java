package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;

import java.util.List;

public class PlayerTriggerHappenEvent extends PlayerEvent {
    private final List<Integer> happenedTriggers;

    public PlayerTriggerHappenEvent(PlayerEntity entity, List<Integer> happenedTriggers) {
        super(entity);
        this.happenedTriggers = happenedTriggers;
    }

    public List<Integer> getHappenedTriggers() {
        return happenedTriggers;
    }
}

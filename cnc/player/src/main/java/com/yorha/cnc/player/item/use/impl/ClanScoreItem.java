package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.SsClanAttr;
import res.template.ItemTemplate;

/**
 * 获得军团积分道具
 *
 * <AUTHOR>
 */
public class ClanScoreItem extends AbstractUsableItem {

    public ClanScoreItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        // 玩家不在军团内
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
    }


    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        // 进入方法时，认为玩家一定在军团内
        int count = getTemplate().getEffectValue();
        SsClanAttr.OnAddClanScoreForClanCmd.Builder cmdBuilder = SsClanAttr.OnAddClanScoreForClanCmd.newBuilder();
        cmdBuilder.setReason("use_item");
        cmdBuilder.setScore((long) count * num);
        cmdBuilder.setEntityId(0);
        playerEntity.ownerActor().tellCurClan(cmdBuilder.build());
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

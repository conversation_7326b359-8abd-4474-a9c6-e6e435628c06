package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.CommonEnum;

import javax.annotation.Nullable;

/**
 * 战力增长事件
 * 仅当战力增长的时候抛出，调用及抛出时请务必注意
 *
 * <AUTHOR>
 */
public class PlayerPowerIncreaseEvent extends PlayerTaskEvent {
    private final long power;
    private final CommonEnum.PowerType powerReason;
    @Nullable
    private final Integer soldierNumChangeReason;

    public PlayerPowerIncreaseEvent(PlayerEntity entity, long power, CommonEnum.PowerType powerReason, @Nullable Integer soldierNumChangeReason) {
        super(entity);
        this.power = power;
        this.powerReason = powerReason;
        this.soldierNumChangeReason = soldierNumChangeReason;
    }

    public long getPower() {
        return power;
    }

    public CommonEnum.PowerType getPowerReason() {
        return powerReason;
    }

    @Nullable
    public Integer getSoldierNumChangeReason() {
        return soldierNumChangeReason;
    }
}

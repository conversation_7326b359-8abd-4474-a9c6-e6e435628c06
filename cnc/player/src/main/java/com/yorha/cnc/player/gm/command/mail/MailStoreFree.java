package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class MailStore<PERSON>ree implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        ServerContext.getServerDebugOption().setMailStoreFree(true);
    }

    @Override
    public String showHelp() {
        return "MailStoreFree";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAIL;
    }
}


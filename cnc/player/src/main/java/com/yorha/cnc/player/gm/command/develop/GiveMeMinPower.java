package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;


/**
 * 一键变强(不加战斗力）
 *
 * <AUTHOR>
 */
public class GiveMeMinPower implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GiveMeMinPower.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        LOGGER.info("GiveMeMinPower execute {}", playerId);

        PlayerEntity playerEntity = actor.getEntity();
        try {
            resourcePower(playerEntity);
        } catch (Exception e) {
            LOGGER.error("GiveMeMinPower error", e);
        }


    }


    private void resourcePower(PlayerEntity playerEntity) {
        for (CommonEnum.CurrencyType type : CommonEnum.CurrencyType.values()) {
            playerEntity.getPurseComponent().give(type, 999999999, CommonEnum.Reason.ICR_GM, "");
        }
        LOGGER.debug("player:{} use give me power -> all resource", playerEntity.getEntityId());
    }


    @Override
    public String showHelp() {
        return "GiveMeMinPower";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

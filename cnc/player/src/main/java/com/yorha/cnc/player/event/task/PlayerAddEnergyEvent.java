package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家增加体力事件
 *
 * <AUTHOR>
 */
public class PlayerAddEnergyEvent extends PlayerTaskEvent {
    /**
     * 体力变化值
     */
    private final int energyNum;

    private final String energyCostReason;

    /**
     * 体力变化前的值
     */
    private final int beforeEnergy;

    /**
     * 添加体力的paramId
     * 道具添加时为道具id
     */
    private final int reasonTemplateId;


    public PlayerAddEnergyEvent(PlayerEntity entity, int energyNum, String energyCostReason, int beforeEnergy, int reasonTemplateId) {
        super(entity);
        this.energyNum = energyNum;
        this.energyCostReason = energyCostReason;
        this.beforeEnergy = beforeEnergy;
        this.reasonTemplateId = reasonTemplateId;
    }

    public int getEnergyNum() {
        return energyNum;
    }

    public String getEnergyCostReason() {
        return energyCostReason;
    }

    public int getBeforeEnergy() {
        return beforeEnergy;
    }

    public int getReasonTemplateId() {
        return reasonTemplateId;
    }
}

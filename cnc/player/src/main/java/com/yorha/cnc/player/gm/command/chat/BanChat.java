package com.yorha.cnc.player.gm.command.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 禁言
 *
 * <AUTHOR>
 */
public class BanChat implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int second = Integer.parseInt(args.get("second"));
        actor.getOrLoadChatPlayerEntity().getHandleChatComponent().banChat(SystemClock.now() + TimeUtils.second2Ms(second));
    }

    @Override
    public String showHelp() {
        return "BanChat second={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

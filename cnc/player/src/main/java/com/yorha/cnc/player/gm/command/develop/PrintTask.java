package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.helper.GmHelper;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.game.gen.prop.TaskSystemProp;
import com.yorha.proto.CommonEnum;

import java.util.Locale;
import java.util.Map;

/**
 * 打印当前接取任务信息，邮件发送
 *
 * <AUTHOR>
 */
public class PrintTask implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String key = "PrintTask";
        String type = args.get("type");
        TaskSystemProp taskSystem = actor.getEntity().getProp().getTaskSystem();

        StringBuilder print = new StringBuilder();
        if (type == null || type.toUpperCase(Locale.ROOT).equals("tc_main".toUpperCase(Locale.ROOT))) {
            print.append("主线任务：\n");
            for (TaskInfoProp prop : taskSystem.getTaskMain().values()) {
                extracted(print, prop);
            }
        }
        if (type == null || type.toUpperCase(Locale.ROOT).equals("tc_daily".toUpperCase(Locale.ROOT))) {
            print.append("每日任务：\n");
            for (TaskInfoProp prop : taskSystem.getTaskDaily().values()) {
                extracted(print, prop);
            }
        }
        if (type == null || type.toUpperCase(Locale.ROOT).equals("tc_branch".toUpperCase(Locale.ROOT))) {
            print.append("支线任务：\n");
            for (TaskInfoProp prop : taskSystem.getTaskBranch().values()) {
                extracted(print, prop);
            }
        }
        GmHelper.sendGmNtfMail(actor.getZoneId(), playerId, key, print.toString());
    }

    private void extracted(StringBuilder type, TaskInfoProp prop) {
        type.append("\t配置id: ").append(prop.getId()).append("\n");
        type.append("\t进度:  ").append(prop.getProcess()).append("\n");
        type.append("\t状态:  ");
        if (prop.getStatus() == CommonEnum.TaskStatus.AT_ACCEPT) {
            type.append("已接取");
        }
        if (prop.getStatus() == CommonEnum.TaskStatus.AT_NOT_REWARD) {
            type.append("待领奖");
        }
        if (prop.getStatus() == CommonEnum.TaskStatus.AT_REWARD) {
            type.append("已领奖");
        }
        type.append("\n");


    }

    @Override
    public String showHelp() {
        return PlayerGmCommand.super.showHelp();
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return PlayerGmCommand.super.getGroup();
    }
}

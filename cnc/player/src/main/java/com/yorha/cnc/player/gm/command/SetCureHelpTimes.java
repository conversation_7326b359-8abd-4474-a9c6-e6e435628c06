package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.Map;

public class SetCureHelpTimes implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(SetCureHelpTimes.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int num = Integer.parseInt(args.get("num"));
        if (num < 0) {
            LOGGER.error("num {} less than 0", num);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        LOGGER.debug("set today cure help num {}", num);
        actor.getEntity().getProp().getClan().getPlayerClanHelpModel().setTodayCureHelp(num);
        int dayLimit = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getCureHelpDailyLimit();
        if (dayLimit <= num) {
            LOGGER.debug("set today cannot create cure help");
            actor.getEntity().getProp().getClan().getPlayerClanHelpModel().setCannotCreateCureHelp(true);
        } else {
            LOGGER.debug("set today cannot create cure help");
            actor.getEntity().getProp().getClan().getPlayerClanHelpModel().setCannotCreateCureHelp(false);
        }
    }

    @Override
    public String showHelp() {
        return "SetCureHelpTimes num={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_CLAN;
    }
}

package com.yorha.cnc.player.gm.command.clan;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class SendRecommendNtfToPlayer implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        actor.getEntity().getPlayerClanComponent().gmRecommendOnce();
    }

    @Override
    public String showHelp() {
        return "SendRecommendNtfToPlayer";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_CLAN;
    }
}

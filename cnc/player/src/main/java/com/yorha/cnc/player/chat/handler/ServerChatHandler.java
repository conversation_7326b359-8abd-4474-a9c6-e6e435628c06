package com.yorha.cnc.player.chat.handler;

import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.chat.component.ChatPlayerHandleChatComponent;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.SsZoneChat;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstChatTemplate;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

/**
 * 全服聊天
 *
 * <AUTHOR>
 */

public class ServerChatHandler implements ChatHandler {
    private static final Logger LOGGER = LogManager.getLogger(ServerChatHandler.class);

    @Override
    public CommonEnum.ChatChannel chatChannel() {
        return CommonEnum.ChatChannel.CC_SERVER;
    }

    @Override
    public long chatRequestSync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage) {
        if (!channelId.equals(chatPlayerEntity.ownerActor().getZoneIdStr())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "ServerChatHandler chatRequestSync zoneId not equal");
        }
        SsZoneChat.SendChatMsgAsk.Builder ask = SsZoneChat.SendChatMsgAsk.newBuilder()
                .setChatMessage(chatMessage);
        SsZoneChat.SendChatMsgAns ans = chatPlayerEntity.ownerActor().callCurZoneChat(ask.build());
        CommonMsg.ChatSession.Builder chatSession = CommonMsg.ChatSession.newBuilder();
        chatSession.setChannelType(chatChannel())
                .setChatChannelId(channelId);
        chatPlayerEntity.getHandleChatComponent().readMessage(chatSession.build(), ans.getMessageId());
        return ans.getMessageId();
    }

    @Override
    public void chatRequestAsync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage, Consumer<Throwable> onComplete) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public List<CommonMsg.ChatMessage> queryChatMsgList(
            ChatPlayerEntity chatPlayerEntity,
            String channelId,
            Collection<Long> shieldList,
            PlayerChat.Player_GetChatMessages_C2S msg
    ) {
        final boolean isLogin = msg.getIsLogin();
        // 登录时不知道最新的id，由对端处理
        final long fromId = isLogin ? 0 : msg.getFromId();
        final long toId = msg.getToId();
        LOGGER.info("ServerChatHandler queryChatMsgList playerId={}, c2sFromId={}, c2sToId={}, isLogin={}",
                chatPlayerEntity.getEntityId(), msg.getFromId(), msg.getToId(), isLogin);
        SsZoneChat.FetchChatMsgAsk.Builder ask = SsZoneChat.FetchChatMsgAsk.newBuilder().setFromId(fromId).setToId(toId).addAllShieldList(shieldList);
        SsZoneChat.FetchChatMsgAns ans = chatPlayerEntity.ownerActor().callCurZoneChat(ask.build());
        // 去zone只可能拉到内存中最新的200条，其余消息player自己拉取
        final List<CommonMsg.ChatMessage> zoneCacheMsgList = ans.getChatMsgsList();
        if (isLogin) {
            return zoneCacheMsgList;
        }
        return ChatHelper.playerQueryChatMsgOnCacheMsg(chatPlayerEntity.ownerActor(), zoneCacheMsgList, msg.getChatSession(), fromId, toId, shieldList, 0);

    }


    @Override
    public long getCd(ChatPlayerEntity chatPlayerEntity) {
        ConstChatTemplate constTemplate = ChatPlayerHandleChatComponent.getConstTemplate();
        int cdTime = constTemplate.getChatCD();
        return TimeUtils.second2Ms(cdTime);
    }
}

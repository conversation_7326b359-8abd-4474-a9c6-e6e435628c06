package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTalentPointChangeEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 为英雄添加X个天赋点
 * param1: 个数
 *
 * <AUTHOR>
 */
public class AddHeroTalentPointChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerTalentPointChangeEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.getFirst();
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int talentAddNum = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(StatisticEnum.HERO_TALENT_NUM);
                prop.setProcess(Math.min(talentAddNum, param1));
                break;
            }
            case TCT_RECEIVE: {
                PlayerTalentPointChangeEvent addNumEvent = (PlayerTalentPointChangeEvent) event;
                prop.setProcess(Math.min(prop.getProcess() + addNumEvent.getAddPointNum(), param1));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= param1;
    }
}

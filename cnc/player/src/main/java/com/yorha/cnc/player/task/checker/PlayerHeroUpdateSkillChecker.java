package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerHeroUpdateSkillEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.HERO_UPDATE_SKILL_TIMES;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;

/**
 * 提升英雄技能x次
 * param1: 次数
 *
 * <AUTHOR>
 */
public class PlayerHeroUpdate<PERSON>kill<PERSON><PERSON><PERSON> extends AbstractTask<PERSON>hecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerHeroUpdateSkillEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.getFirst();

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            int updateSkillTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(HERO_UPDATE_SKILL_TIMES);
            prop.setProcess(Math.min(updateSkillTotal, param1));
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param1;
    }
}

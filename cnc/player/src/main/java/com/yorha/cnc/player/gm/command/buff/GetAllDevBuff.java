package com.yorha.cnc.player.gm.command.buff;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.helper.GmHelper;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.StructBattle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class GetAllDevBuff implements PlayerGmCommand {
    public static final Logger LOGGER = LogManager.getLogger(GetAllDevBuff.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String key = "GetAllDevBuff";
        int zoneId = actor.getZoneId();
        SsScenePlayer.GetSceneDevBuffAsk.Builder ask = SsScenePlayer.GetSceneDevBuffAsk.newBuilder().setPlayerId(playerId).setIsGm(true);
        SsScenePlayer.GetSceneDevBuffAns ans = actor.callCurScene(ask.build());

        DevBuffSysProp devBuffProp = new DevBuffSysProp();
        StructBattle.DevBuffSys sceneBuff = ans.getBuffSys();
        StructBattle.DevBuffSys zoneBuff = ans.getZoneBuffSys();
        StructBattle.DevBuffSys playerBuff = actor.getEntity().getPlayerDevBuffComponent().getAllDevBuffCopy(actor.getEntity().getProp().getDevBuffSysNew()).getCopySsBuilder().build();
        for (Map.Entry<Integer, StructBattle.DevBuff> entry : sceneBuff.getDevBuff().getDatasMap().entrySet()) {
            DevBuffProp prop = new DevBuffProp();
            prop.mergeFromSs(entry.getValue());
            devBuffProp.putDevBuffV(prop);
        }
        for (Map.Entry<Integer, StructBattle.DevBuff> entry : zoneBuff.getDevBuff().getDatasMap().entrySet()) {
            DevBuffProp prop = new DevBuffProp();
            prop.mergeFromSs(entry.getValue());
            devBuffProp.putDevBuffV(prop);
        }
        for (Map.Entry<Integer, StructBattle.DevBuff> entry : playerBuff.getDevBuff().getDatasMap().entrySet()) {
            DevBuffProp prop = new DevBuffProp();
            prop.mergeFromSs(entry.getValue());
            devBuffProp.putDevBuffV(prop);
        }
        GmHelper.sendGmNtfMail(zoneId, playerId, key, devBuffProp.toString());

        LOGGER.info("{} SceneDevBuff:{}", actor.getEntity(), sceneBuff);
        LOGGER.info("{} PlayerDevBuff:{}", actor.getEntity(), playerBuff);
        LOGGER.info("{} ZoneDevBuff:{}", actor.getEntity(), zoneBuff);
        LOGGER.info("{} TotalDevBuff:{}", actor.getEntity(), devBuffProp);
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;

import java.util.Map;

/**
 * 打印PlayerEntity内存占用
 */
public class PrintPlayerMemoryLayout implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        actor.getEntity().checkMemorySize(Integer.parseInt(args.get("layer")));
    }

    @Override
    public String showHelp() {
        return "PrintPlayerMemoryLayout layer={}";
    }
}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.KingdomHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.KingdomGiftInfoProp;
import com.yorha.game.gen.prop.PlayerKingdomModelProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.DevBuffSourceType;
import com.yorha.proto.CommonEnum.DevBuffType;
import com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C;
import com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C;
import com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstKingdomTemplate;
import res.template.KingdomBuffTemplate;
import res.template.KingdomSkillTemplate;
import res.template.KingdomTitleTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 玩家身上的王国信息管理组件
 *
 * <AUTHOR>
 */
public class PlayerKingdomComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerKingdomComponent.class);

    public PlayerKingdomComponent(PlayerEntity owner) {
        super(owner);
    }

    // ------------------------------------------------- cs 方法 ------------------------------------------ //

    public void appoint(int officeId, long toPlayerId) throws GeminiException {
        // 检查是否能任命
        checkCanAppoint(officeId);
        // 检查玩家合法性

        SsPlayerCard.QueryPlayerCardAns ans = CardHelper.queryPlayerCardSync(ownerActor(), toPlayerId);
        if (ans.getCardInfo().getZoneId() != getOwner().getZoneId()) {
            LOGGER.error("king appoint but not self zone {} {}", toPlayerId, ans.getCardInfo().getZoneId());
            throw new GeminiException(ErrorCode.CAN_NOT_APPOINT_NOT_SAME_ZONE);
        }

        SsSceneKingdom.KingAppointAsk.Builder builder = SsSceneKingdom.KingAppointAsk.newBuilder();
        builder.setOperatorId(getPlayerId());
        builder.setOperatorOfficeId(getKingdomModel().getOfficeId());
        builder.setKingdomOfficeId(officeId);
        builder.setToPlayerId(toPlayerId);
        ownerActor().callSelfBigScene(builder.build());
    }

    public void openBuff(int buffId) throws GeminiException {
        if (!isKing()) {
            // 不是国王你想开buff，先去篡位吧
            throw new GeminiException(ErrorCode.KINGDOM_NOT_KING);
        }
        // 当前增益还没结束
        if (SystemClock.now() < getKingdomModel().getBuffCdEndTsMs()) {
            throw new GeminiException(ErrorCode.KINGDOM_BUFF_IN_CD);
        }

        // 扣钱
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        KingdomBuffTemplate template = service.getBuffTemplate(buffId);
        AssetPackage consumeVerify = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND,
                template.getConsumePair().getValue()).build();
        getOwner().verifyThrow(consumeVerify);
        getOwner().consume(consumeVerify, CommonEnum.Reason.ICR_KINGDOM_BUFF);

        // 所有检查已结束，开buff
        SsSceneKingdom.KingOpenBuffAsk.Builder builder = SsSceneKingdom.KingOpenBuffAsk.newBuilder();
        builder.setKingdomBuffId(buffId);
        ownerActor().callSelfBigScene(builder.build());
    }

    public void sendGift(int giftId, long toPlayerId) throws GeminiException {
        // 检查是否能发礼物
        checkCanSendGift();
        // 检查玩家合法性
        StructPB.PlayerCardInfoPB pb = CardHelper.queryPlayerCardWithClanSync(ownerActor(), toPlayerId);
        if (pb.getZoneId() != getOwner().getZoneId()) {
            LOGGER.error("king sendGift but not self zone {} {}", toPlayerId, pb.getZoneId());
            throw new GeminiException(ErrorCode.CAN_NOT_SEND_GIFT_NOT_SAME_ZONE);
        }
        SsSceneKingdom.KingSendGiftAsk.Builder builder = SsSceneKingdom.KingSendGiftAsk.newBuilder();
        builder.setKingdomGiftId(giftId).setToPlayerId(toPlayerId)
                .setClanSimpleName(pb.getClanSimpleName());
        builder.getCardHeadBuilder()
                .setName(pb.getCardHead().getName())
                .setPic(pb.getCardHead().getPic())
                .setPicFrame(pb.getCardHead().getPicFrame())
                .setPicUrl(pb.getCardHead().getPicUrl());
        ownerActor().callSelfBigScene(builder.build());
    }

    public void useSkill(int skillId, long targetId, int targetZoneId) throws GeminiException {
        if (!isKing()) {
            // 不是国王你想用技能，先去篡位吧
            throw new GeminiException(ErrorCode.KINGDOM_NOT_KING);
        }

        // 策划说不能跨服放技能
        if (getOwner().getZoneId() != targetZoneId) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_CAN_NOT_USE);
        }

        // 去zone上check使用
        SsSceneKingdom.KingCheckCanUseSkillAsk.Builder checkBuilder = SsSceneKingdom.KingCheckCanUseSkillAsk.newBuilder()
                .setKingdomSkillId(skillId)
                .setToTargetId(targetId)
                .setZoneId(targetZoneId);
        ownerActor().callSelfBigScene(checkBuilder.build());

        // 扣钱
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        KingdomSkillTemplate template = service.getSkillTemplate(skillId);
        if (template.getConsumePair() != null) {
            AssetPackage consumeVerify = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND,
                    template.getConsumePair().getValue()).build();
            getOwner().verifyThrow(consumeVerify);
            getOwner().consume(consumeVerify, CommonEnum.Reason.ICR_KINGDOM_SKILL);
        }
        SsSceneKingdom.KingUseSkillAsk.Builder builder = SsSceneKingdom.KingUseSkillAsk.newBuilder();
        builder.setKingdomSkillId(skillId);
        builder.setToTargetId(targetId);
        builder.setZoneId(targetZoneId);
        ownerActor().callSelfBigScene(builder.build());
    }

    public Player_FetchHistoryKings_S2C.Builder fetchHistoryKings(int page) {
        SsSceneKingdom.FetchHistoryKingAsk.Builder askBuilder = SsSceneKingdom.FetchHistoryKingAsk.newBuilder();
        askBuilder.setPage(page);
        SsSceneKingdom.FetchHistoryKingAns zoneRetBuilder = ownerActor().callSelfBigScene(askBuilder.build());
        Player_FetchHistoryKings_S2C.Builder retClientBuilder = Player_FetchHistoryKings_S2C.newBuilder();
        retClientBuilder.setPage(zoneRetBuilder.getPage());
        retClientBuilder.setTotalPage(zoneRetBuilder.getTotalPage());
        retClientBuilder.setCurKingInfo(zoneRetBuilder.getCurKingInfo());
        retClientBuilder.addAllKingInfos(zoneRetBuilder.getKingInfosList());
        return retClientBuilder;
    }

    public Player_FetchKingdomGiftInfo_S2C.Builder fetchGiftInfo(boolean isFetchingLeftNum, int giftId, long checkPlayerId) {
        SsSceneKingdom.FetchKingdomGiftAsk.Builder askBuilder = SsSceneKingdom.FetchKingdomGiftAsk.newBuilder();
        askBuilder.setGiftId(giftId).setIsFetchingLeftNum(isFetchingLeftNum).setCheckPlayerId(checkPlayerId);
        SsSceneKingdom.FetchKingdomGiftAns zoneRetBuilder = ownerActor().callSelfBigScene(askBuilder.build());
        Player_FetchKingdomGiftInfo_S2C.Builder retClientBuilder = Player_FetchKingdomGiftInfo_S2C.newBuilder();
        if (isFetchingLeftNum) {
            retClientBuilder.putAllLeftNum(zoneRetBuilder.getLeftNumMap());
        } else {
            KingdomGiftInfoProp prop = new KingdomGiftInfoProp();
            prop.mergeFromSs(zoneRetBuilder.getGiftInfo());
            retClientBuilder.setGiftInfo(prop.getCopyCsBuilder());
        }
        retClientBuilder.setCanGiveGift(zoneRetBuilder.getCanGiveGift());
        return retClientBuilder;
    }

    public void fetchOfficeInfo(IActorRef sessionRef, int seqId) {
        SsSceneKingdom.FetchKingdomOfficeAsk.Builder askBuilder = SsSceneKingdom.FetchKingdomOfficeAsk.newBuilder();
        SsSceneKingdom.FetchKingdomOfficeAns zoneRetBuilder = ownerActor().callSelfBigScene(askBuilder.build());
        Player_FetchKingdomOfficeInfo_S2C.Builder retClientBuilder = Player_FetchKingdomOfficeInfo_S2C.newBuilder();
        List<Long> miss = new ArrayList<>();
        for (ZonePB.KingdomOfficeInfoPB value : zoneRetBuilder.getOfficeInfoMap().values()) {
            if (!value.hasPlayerCardHead()) {
                miss.add(value.getPlayerId());
            }
            retClientBuilder.putOfficeInfo(value.getOfficeId(), value);
        }
        if (miss.isEmpty()) {
            getOwner().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_FETCHKINGDOMOFFICEINFO_S2C, null, retClientBuilder.build());
            return;
        }
        CardHelper.batchQueryPlayerCardWithClan(ownerActor(), miss,
                (map) -> {
                    for (ZonePB.KingdomOfficeInfoPB value : zoneRetBuilder.getOfficeInfoMap().values()) {
                        if (value.hasPlayerCardHead()) {
                            continue;
                        }
                        StructPB.PlayerCardInfoPB pb = map.get(value.getPlayerId());
                        if (pb == null) {
                            continue;
                        }
                        ZonePB.KingdomOfficeInfoPB.Builder builder = ZonePB.KingdomOfficeInfoPB.newBuilder()
                                .setPlayerId(value.getPlayerId())
                                .setOfficeId(value.getOfficeId())
                                .setSname(pb.getClanSimpleName());
                        builder.getPlayerCardHeadBuilder().mergeFrom(pb.getCardHead());
                        retClientBuilder.putOfficeInfo(value.getOfficeId(), builder.build());
                    }
                    getOwner().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_FETCHKINGDOMOFFICEINFO_S2C, null, retClientBuilder.build());
                });
    }

    public int getKingdomMailId() {
        return ResHolder.getInstance().getConstTemplate(ConstKingdomTemplate.class).getKingdomMailID();
    }

    // ------------------------------------------------- ss 方法 ------------------------------------------ //

    public void applyKingdomOfficeChange(int oldOfficeId, int newOfficeId, int kingNum, String clanSimpleName) {
        LOGGER.info("applyKingdomOfficeChange oldOfficeId={} newOfficeId={}", oldOfficeId, newOfficeId);
        if (oldOfficeId != getKingdomModel().getOfficeId()) {
            LOGGER.error("maybe lost msg, old officeId is {}, where zone old officeId is {}", getKingdomModel().getOfficeId(), oldOfficeId);
        }
        oldOfficeId = getKingdomModel().getOfficeId();
        // 以玩家实际生效的官员buff为准，删除原有的buff
        if (oldOfficeId != 0) {
            removeOfficeBuff(oldOfficeId);
        }
        if (newOfficeId != 0) {
            addOfficeBuff(newOfficeId);
        }
        getKingdomModel().setOfficeId(newOfficeId);

        // 通知PlayerCard更新
        getOwner().getPlayerPropComponent().updatePlayerCardCache(true);
        if (newOfficeId == 0) {
            return;
        }
        if (clanSimpleName == null) {
            // 查一下了。。。。
            if (getOwner().getClanId() != 0) {
                SsClanCard.QueryClanCardAns ans = CardHelper.queryClanCardSync(ownerActor(), getOwner().getClanId());
                clanSimpleName = ans.getInfo().getBase().getSname();
            } else {
                clanSimpleName = "";
            }
        }
        // 是国王
        if (newOfficeId == ResHolder.getResService(KingdomTemplateResService.class).getKingOfficeId()) {
            // 存db
            StructMsg.SimpleKingInfo.Builder kingInfoBuilder = StructMsg.SimpleKingInfo.newBuilder();
            kingInfoBuilder.setNumber(kingNum)
                    .setCardHead(getOwner().getCardHead().getCopyCsBuilder())
                    .setBeKingTsMs(SystemClock.now())
                    .setSname(clanSimpleName);
            KingdomHelper.addKingInfo(ownerActor(), getOwner().getZoneId(), kingNum, kingInfoBuilder.build());
            // 跑马灯
            sendAppointMarquee(clanSimpleName, newOfficeId, 1);
            return;
        }
        KingdomTitleTemplate officeTemplate = ResHolder.getTemplate(KingdomTitleTemplate.class, newOfficeId);
        // 跑马灯
        sendAppointMarquee(clanSimpleName, newOfficeId, officeTemplate.getRank());

    }

    private void sendAppointMarquee(String clanSimpleName, int officeId, int rank) {
        ConstKingdomTemplate template = ResHolder.getConsts(ConstKingdomTemplate.class);
        int marqueeId = template.getKingdomMarqueeNegativeID();
        if (template.getPositiveTitleRanks().contains(rank)) {
            marqueeId = template.getKingdomMarqueePositiveID();
        }
        StructPB.DisplayDataPB.Builder builder = StructPB.DisplayDataPB.newBuilder();
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb("[" + clanSimpleName + "]"));
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(getOwner().getName()));
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(CommonEnum.DisplayParamType.DPT_KINGDOM_TITLE_ID, officeId));
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, builder.build());
        BroadcastHelper.toCsOnlinePlayerInZone(getOwner().getZoneId(), MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    /**
     * 结算赋税技能的资源给玩家
     *
     * @param resourceMap 资源map，可能为null
     */
    public void settleGainTaxResource(Map<Integer, Long> resourceMap) {
        if (null == resourceMap) {
            LOGGER.warn("PlayerKingdomComponent settleGainTaxResource: resourceMap is null");
            return;
        }
        for (Map.Entry<Integer, Long> entry : resourceMap.entrySet()) {
            long addCount = entry.getValue();
            getOwner().getPurseComponent().give(CommonEnum.CurrencyType.forNumber(entry.getKey()), addCount, CommonEnum.Reason.ICR_KINGDOM_SKILL, "");
        }
    }

    public int getOfficeId() {
        return getKingdomModel().getOfficeId();
    }

    public boolean isKing() {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        return service.isKing(getKingdomModel().getOfficeId());
    }

    private void checkCanAppoint(int targetOfficeId) throws GeminiException {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        if (!service.isCanAppointRank(getKingdomModel().getOfficeId(), targetOfficeId)) {
            LOGGER.info("checkCanAppoint: player {} cannot appoint office {}", getPlayerId(), targetOfficeId);
            throw new GeminiException(ErrorCode.KINGDOM_CANNOT_APPOINT);
        }
    }

    private void checkCanSendGift() throws GeminiException {
        if (!isKing()) {
            LOGGER.warn("checkCanSendGift: player {} isn't king", getPlayerId());
            throw new GeminiException(ErrorCode.KINGDOM_NOT_KING);
        }
    }

    private void removeOfficeBuff(int officeId) {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        service.getBuffByOfficeId(officeId).forEach(buffId -> {
            getOwner().getPlayerDevBuffComponent().removeDevBuff(buffId);
        });
    }

    private void addOfficeBuff(int officeId) {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        service.getBuffByOfficeId(officeId).forEach(buffId -> {
            getOwner().getPlayerDevBuffComponent().addDevBuff(buffId, null, null,
                    DevBuffType.DBT_SCENE_BUFF, DevBuffSourceType.DBST_KINGDOM_STAFF);
        });
    }

    private PlayerKingdomModelProp getKingdomModel() {
        return getOwner().getProp().getKingdomModel();
    }
    

}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.cnc.player.friend.component.FriendPlayerComponent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.Core;
import com.yorha.proto.PlayerFriend;
import com.yorha.proto.SsPlayerFriend;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstFriendTemplate;

/**
 * 玩家好友模块
 *
 * <AUTHOR>
 */
public class PlayerFriendComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerFriendComponent.class);

    public PlayerFriendComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void afterLogout() {
        // 执行friendPlayerEntity的下线逻辑
        FriendPlayerEntity friendPlayerEntity = getOwner().ownerActor().getFriendPlayerEntityOrNull();
        if (friendPlayerEntity == null) {
            return;
        }
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::afterLogout);
    }

    /**
     * 添加好友
     */
    public void applyNewFriend(IActorRef sessionRef, long playerId, int zoneId, int seqId, int tryNum) {
        // 检查是否可以添加该玩家
        if (tryNum <= 0) {
            LOGGER.error("PlayerFriendComponent applyNewFriend fail, migrate retry time use up, player1 {} apply add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_ADDFRIENDAPPLY_S2C, seqId, ErrorCode.FAILED.getCode());
            return;
        }
        ErrorCode errorCode = checkCanAdd(playerId);
        if (!ErrorCode.isOK(errorCode.getCodeId())) {
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_ADDFRIENDAPPLY_S2C, seqId, errorCode.getCode());
            LOGGER.info("PlayerFriendComponent applyNewFriend fail,  player1 {} apply add player2 {}, code = {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, errorCode, seqId, tryNum);
            return;
        }
        LOGGER.info("PlayerFriendComponent applyNewFriend, player1 {} in zone {} apply add player2 {} in zone {} start, seqId={}, tryNum={}",
                ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum);
        // 请求添加好友
        Struct.PlayerCardHead.Builder cardHead = getOwner().getCardHead().getCopySsBuilder();
        SsPlayerFriend.AddNewFriendAsk.Builder builder = SsPlayerFriend.AddNewFriendAsk.newBuilder();
        builder.setPlayerId(ownerActor().getPlayerId()).setZoneId(ownerActor().getZoneId()).setCardHead(cardHead);

        ownerActor().<SsPlayerFriend.AddNewFriendAns>askPlayer(zoneId, playerId, builder.build()).onComplete(
                (res, err) -> {
                    if (err instanceof MigrateException) {
                        LOGGER.info("PlayerFriendComponent applyNewFriend detect player {} migrate, old zone={}, new zone={}, seqId={}, tryNum={}", playerId, zoneId, ((MigrateException) err).getZoneId(), seqId, tryNum);
                        this.applyNewFriend(sessionRef, playerId, ((MigrateException) err).getZoneId(), seqId, tryNum - 1);
                        return;
                    }
                    if (err != null) {
                        LOGGER.error("PlayerFriendComponent applyNewFriend fail, player1 {} apply add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum, err);
                        this.sendFriendAskErrorCode(sessionRef, MsgType.PLAYER_ADDFRIENDAPPLY_C2S, seqId, err);
                        return;
                    }
                    if (res == null) {
                        WechatLog.error("PlayerFriendComponent applyNewFriend, res and err both null when player1 {} apply add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
                        SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_ADDFRIENDAPPLY_S2C, seqId, ErrorCode.FAILED.getCode());
                        return;
                    }
                    FriendPlayerEntity friendPlayerEntity = ownerActor().getFriendPlayerEntityOrNull();
                    if (friendPlayerEntity == null) {
                        // 不可能为null，如果真为null，就直接返回，让请求超时
                        WechatLog.error("PlayerFriendComponent applyNewFriend, friendPlayerEntity is null");
                        return;
                    }
                    // 将该请求添加到等待回应列表
                    WaitResponseInfoProp waitResponseInfoProp = friendPlayerEntity.getMaintenanceComponent().addWaitInfoToList(playerId);
                    // 如果玩家的申请列表里包含要添加对象的过期申请，则删除
                    if (friendPlayerEntity.hasApply(playerId)) {
                        FriendApplyInfoProp prop = friendPlayerEntity.getProp().getApplyList().get(playerId);
                        long expireTimeMs = TimeUtils.second2Ms(ResHolder.getConsts(ConstFriendTemplate.class).getApplyOverdue());
                        if ((prop.getApplyTsMs() + expireTimeMs) < SystemClock.now()) {
                            LOGGER.info("PlayerFriendComponent applyNewFriend, player1 {} remove player2 {} expire apply info, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
                            friendPlayerEntity.getMaintenanceComponent().removeApplyInfo(playerId);
                        }
                    }
                    getOwner().getQlogComponent().sendFriendQLog("friend_apply", playerId);
                    LOGGER.info("PlayerFriendComponent applyNewFriend, player1 {} in zone {} apply add player2 {} in zone {} end, seqId={}, tryNum={}, player1 waitList add {}",
                            ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum, waitResponseInfoProp);
                    ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_ADDFRIENDAPPLY_S2C, null, PlayerFriend.Player_AddFriend_S2C.getDefaultInstance());

                }
        );
    }

    /**
     * 添加好友检查
     */
    public ErrorCode checkCanAdd(long playerId) {
        FriendPlayerEntity friendPlayerEntity = ownerActor().getOrLoadFriendPlayerEntity();
        // 自己不能加自己
        if (playerId == friendPlayerEntity.getEntityId()) {
            return ErrorCode.FRIEND_CANNOT_OPERATE_YOURSELF;
        }
        // 玩家主堡是否达到7级
        int cityLevel = ownerActor().getOrLoadEntity().getCityLevel();
        ConstFriendTemplate template = ResHolder.getConsts(ConstFriendTemplate.class);
        if (cityLevel < template.getSendFriendLimit()) {
            return ErrorCode.FRIEND_ADD_FRIEND_NEED_SPECIAL_LEVEL;
        }
        // 是否屏蔽了该玩家
        if (friendPlayerEntity.hasShieldPlayer(playerId)) {
            return ErrorCode.FRIEND_HAVE_SHIELD_PLAYER;
        }
        if (friendPlayerEntity.hasFriend(playerId)) {
            return ErrorCode.FRIEND_ALREADY_HAVE_FRIENDSHIP;
        }
        if (friendPlayerEntity.isFriendFull()) {
            return ErrorCode.FRIEND_YOUR_FRIENDLIST_IS_FULL;
        }
        return ErrorCode.OK;
    }

    /**
     * 同意好友请求
     */
    public void agreeFriendApply(IActorRef sessionRef, long playerId, int zoneId, int seqId, int tryNum) {
        FriendPlayerEntity friendPlayerEntity = ownerActor().getOrLoadFriendPlayerEntity();
        // 好友列表已满，不能同意
        if (friendPlayerEntity.isFriendFull()) {
            LOGGER.info("PlayerFriendComponent agreeFriendApply, player {} try agree player {}'s apply fail, code={}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, ErrorCode.FRIEND_YOUR_FRIENDLIST_IS_FULL, seqId, tryNum);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_AGREEFRIENDAPPLY_S2C, seqId, ErrorCode.FRIEND_YOUR_FRIENDLIST_IS_FULL.getCode());
            return;
        }
        if (tryNum <= 0) {
            LOGGER.error("PlayerFriendComponent agreeFriendApply error, migrate retry time use up, player1 {} agree add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_AGREEFRIENDAPPLY_S2C, seqId, ErrorCode.FAILED.getCode());
            return;
        }

        LOGGER.info("PlayerFriendComponent agreeFriendApply, player1 {} in zone {} agree player2 {} in zone {} start, seqId={}, tryNum={}",
                ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum);
        // 通知对方已同意申请
        SsPlayerFriend.AgreeAddFriendAsk.Builder builder = SsPlayerFriend.AgreeAddFriendAsk.newBuilder();
        builder.setPlayerId(ownerActor().getPlayerId())
                .setZoneId(ownerActor().getZoneId())
                .setCardHead(getOwner().getCardHead().getCopySsBuilder());
        ownerActor().<SsPlayerFriend.AgreeAddFriendAns>askPlayer(zoneId, playerId, builder.build()).onComplete(
                (res, err) -> {
                    if (err instanceof MigrateException) {
                        LOGGER.info("PlayerFriendComponent agreeFriendApply detect player {} migrate, old zone={}, new zone={}, seqId={}, tryNum={}", playerId, zoneId, ((MigrateException) err).getZoneId(), seqId, tryNum);
                        this.agreeFriendApply(sessionRef, playerId, ((MigrateException) err).getZoneId(), seqId, tryNum - 1);
                        return;
                    }
                    if (err != null) {
                        LOGGER.error("PlayerFriendComponent agreeFriendApply fail, player1 {} agree add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum, err);
                        this.sendFriendAskErrorCode(sessionRef, MsgType.PLAYER_AGREEFRIENDAPPLY_C2S, seqId, err);
                        return;
                    }
                    if (res == null) {
                        WechatLog.error("PlayerFriendComponent agreeFriendApply, res and err both null when player1 {} agree add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
                        SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_AGREEFRIENDAPPLY_S2C, seqId, ErrorCode.FAILED.getCode());
                        return;
                    }
                    FriendApplyInfoProp applyInfoProp = friendPlayerEntity.getMaintenanceComponent().removeApplyInfo(playerId);
                    // 将该玩家添加到好友列表
                    SimpleFriendInfoProp friendInfoProp = friendPlayerEntity.getMaintenanceComponent().addPlayerToFriendList(playerId, zoneId, res.getCardHead());
                    // 对于AB互加好友的情况，A可能也申请了B，所以等待列表里有B，建立好友关系后需要移除
                    friendPlayerEntity.getMaintenanceComponent().removeWaitResponseInfo(playerId);
                    getOwner().getQlogComponent().sendFriendQLog("friend_accept", playerId);
                    LOGGER.info("PlayerFriendComponent agreeFriendApply, player1 {} in zone {} agree player2 {} in zone {} end, seqId={}, tryNum={}, player1 remove applyInfo={}, add friendInfo={}",
                            ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum, applyInfoProp, friendInfoProp);
                    ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_AGREEFRIENDAPPLY_S2C, null, PlayerFriend.Player_AgreeFriendApply_S2C.getDefaultInstance());
                }
        );
    }

    /**
     * 玩家拒绝加好友
     */
    public void refuseFriendApply(IActorRef sessionRef, long playerId, int zoneId, int seqId, int tryNum) {
        if (tryNum <= 0) {
            LOGGER.error("PlayerFriendComponent refuseFriendApply fail, migrate retry time use up, player1 {} refuse add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_REFUSEFRIENDAPPLY_S2C, seqId, ErrorCode.FAILED.getCode());
            return;
        }
        LOGGER.info("PlayerFriendComponent refuseFriendApply, player1 {} in zone {} refuse add player2 {} in zone {} start, seqId={}, tryNum={}",
                ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum);
        // 通知申请方拒绝加好友
        SsPlayerFriend.RefuseAddFriendAsk.Builder ask = SsPlayerFriend.RefuseAddFriendAsk.newBuilder();
        ask.setPlayerId(ownerActor().getPlayerId());
        ownerActor().<SsPlayerFriend.RefuseAddFriendAns>askPlayer(zoneId, playerId, ask.build()).onComplete(
                (res, err) -> {
                    if (err instanceof MigrateException) {
                        LOGGER.info("PlayerFriendComponent refuseFriendApply detect player {} migrate, old zone={}, new zone={}, seqId={}, tryNum={}", playerId, zoneId, ((MigrateException) err).getZoneId(), seqId, tryNum);
                        this.refuseFriendApply(sessionRef, playerId, ((MigrateException) err).getZoneId(), seqId, tryNum - 1);
                        return;
                    }
                    if (err != null) {
                        LOGGER.error("PlayerFriendComponent refuseFriendApply fail, player1 {} refuse add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum, err);
                        this.sendFriendAskErrorCode(sessionRef, MsgType.PLAYER_REFUSEFRIENDAPPLY_C2S, seqId, err);
                        return;
                    }
                    if (res == null) {
                        WechatLog.error("PlayerFriendComponent refuseFriendApply, res and err both null, when player1 {} refuse add player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
                        SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_REFUSEFRIENDAPPLY_S2C, seqId, ErrorCode.FAILED.getCode());
                        return;
                    }
                    // 将申请信息从申请列表中移除
                    FriendPlayerEntity friendPlayerEntity = ownerActor().getFriendPlayerEntityOrNull();
                    if (friendPlayerEntity == null) {
                        // 不可能为null，如果真为null，就直接返回，让请求超时
                        WechatLog.error("PlayerFriendComponent refuseFriendApply, friendPlayerEntity is null");
                        return;
                    }
                    FriendApplyInfoProp applyInfoProp = friendPlayerEntity.getMaintenanceComponent().removeApplyInfo(playerId);
                    getOwner().getQlogComponent().sendFriendQLog("friend_refuse", playerId);
                    LOGGER.info("PlayerFriendComponent refuseFriendApply, player1 {} in zone {} refuse add player2 {} in zone {} end, seqId={}, tryNum={}, player1 remove applyInfo={}",
                            ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum, applyInfoProp);
                    ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_REFUSEFRIENDAPPLY_S2C, null, PlayerFriend.Player_RefuseFriendApply_S2C.getDefaultInstance());
                }
        );
    }

    /**
     * 玩家删除好友
     */
    public void delFriend(IActorRef sessionRef, long playerId, int zoneId, int seqId, int tryNum) {
        if (tryNum <= 0) {
            LOGGER.error("PlayerFriendComponent delFriend fail, migrate retry time use up, player1 {} del player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_DELFRIEND_S2C, seqId, ErrorCode.FAILED.getCode());
            return;
        }
        LOGGER.info("PlayerFriendComponent delFriend, player1 {} in zone {} del player2 {} in zone {} start, seqId={}, tryNum={}",
                ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum);
        // 通知对方删除自己
        SsPlayerFriend.DelFriendAsk.Builder ask = SsPlayerFriend.DelFriendAsk.newBuilder();
        ask.setPlayerId(ownerActor().getPlayerId());
        ownerActor().<SsPlayerFriend.DelFriendAns>askPlayer(zoneId, playerId, ask.build()).onComplete(
                (res, err) -> {
                    if (err instanceof MigrateException) {
                        LOGGER.info("PlayerFriendComponent delFriend detect player {} migrate, old zone={}, new zone={}, seqId={}, tryNum={}", playerId, zoneId, ((MigrateException) err).getZoneId(), seqId, tryNum);
                        this.delFriend(sessionRef, playerId, ((MigrateException) err).getZoneId(), seqId, tryNum - 1);
                        return;
                    }
                    if (err != null) {
                        LOGGER.error("PlayerFriendComponent delFriend fail, player1 {} del player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum, err);
                        this.sendFriendAskErrorCode(sessionRef, MsgType.PLAYER_DELFRIEND_C2S, seqId, err);
                        return;
                    }
                    if (res == null) {
                        WechatLog.error("PlayerFriendComponent delFriend, res and err both null when player1 {} del player2 {}, seqId={}, tryNum={}", ownerActor().getPlayerId(), playerId, seqId, tryNum);
                        SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_DELFRIEND_S2C, seqId, ErrorCode.FAILED.getCode());
                        return;
                    }
                    // 将玩家从自己的好友列表删除
                    FriendPlayerEntity friendPlayerEntity = ownerActor().getFriendPlayerEntityOrNull();
                    if (friendPlayerEntity == null) {
                        // 不可能为null，如果真为null，就直接返回，让请求超时
                        WechatLog.error("PlayerFriendComponent delFriend, friendPlayerEntity is null");
                        return;
                    }
                    SimpleFriendInfoProp friendInfoProp = friendPlayerEntity.getMaintenanceComponent().removeFriendInfo(playerId);
                    getOwner().getQlogComponent().sendFriendQLog("friend_delete", playerId);
                    LOGGER.info("PlayerFriendComponent delFriend, player1 {} in zone {} del player2 {} in zone {} end, seqId=={}, tryNum={}, player1 remove friendInfo={}",
                            ownerActor().getPlayerId(), ownerActor().getZoneId(), playerId, zoneId, seqId, tryNum, friendInfoProp);
                    ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_DELFRIEND_S2C, null, PlayerFriend.Player_DelFriend_S2C.getDefaultInstance());
                }
        );
    }

    /**
     * 玩家屏蔽好友
     */
    public void shieldPlayer(IActorRef sessionRef, long playerId, int seqId) {
        FriendPlayerEntity friendPlayerEntity = ownerActor().getOrLoadFriendPlayerEntity();
        if (friendPlayerEntity.isShieldFull()) {
            LOGGER.error("PlayerFriendComponent shieldPlayer, player1 {} try shield player2 {}'s apply fail, seqId={}, errorCode={}", ownerActor().getPlayerId(), playerId, seqId, ErrorCode.FRIEND_REACH_SHIELD_LIMIT);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_SHIELDPLAYER_S2C, seqId, ErrorCode.FRIEND_REACH_SHIELD_LIMIT.getCode());
            return;
        }
        if (friendPlayerEntity.hasShieldPlayer(playerId)) {
            ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_SHIELDPLAYER_S2C, null, PlayerFriend.Player_ShieldPlayer_S2C.getDefaultInstance());
            return;
        }
        LOGGER.info("PlayerFriendComponent shieldPlayer, player1 {} shield player2 {}  start, seqId={}",
                ownerActor().getPlayerId(), playerId, seqId);
        CardHelper.queryPlayerCardAsync(ownerActor(), playerId, (cardInfo) -> {
            if (!cardInfo.hasCardHead()) {
                LOGGER.info("PlayerFriendComponent shieldPlayer, query player2 {} cardHead failed!", playerId);
                SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), MsgType.PLAYER_SHIELDPLAYER_S2C, seqId, ErrorCode.FAILED.getCode());
                return;
            }
            PlayerCardInfoProp cardInfoProp = new PlayerCardInfoProp();
            cardInfoProp.mergeFromCs(cardInfo);
            Struct.PlayerCardHead cardHead = cardInfoProp.getCardHead().getCopySsBuilder().build();
            ShieldPlayerInfoProp shieldPlayerInfoProp = friendPlayerEntity.getMaintenanceComponent().addShieldInfoToList(playerId, cardInfo.getZoneId(), cardHead);
            getOwner().getQlogComponent().sendFriendQLog("friend_shield", playerId);
            LOGGER.info("PlayerFriendComponent shieldPlayer, player1 {} shield player2 {} end, seqId={}, player1 add shieldInfo={}",
                    ownerActor().getPlayerId(), playerId, seqId, shieldPlayerInfoProp);
            ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_SHIELDPLAYER_S2C, null, PlayerFriend.Player_ShieldPlayer_S2C.getDefaultInstance());
        });
    }

    /**
     * 解除屏蔽
     */
    public void removeShield(long playerId) {
        LOGGER.info("PlayerFriendComponent removeShield, player1 {} remove shield player2 {} start", ownerActor().getPlayerId(), playerId);
        FriendPlayerEntity friendPlayerEntity = ownerActor().getOrLoadFriendPlayerEntity();
        ShieldPlayerInfoProp prop = friendPlayerEntity.getMaintenanceComponent().removeShieldInfo(playerId);
        getOwner().getQlogComponent().sendFriendQLog("friend_remove_shield", playerId);
        LOGGER.info("PlayerFriendComponent removeShield, player1 {} removeShield player2 {} end, player1 remove shieldInfo={}",
                ownerActor().getPlayerId(), playerId, prop);
    }

    /**
     * @param msgType c2s协议
     */
    private void sendFriendAskErrorCode(IActorRef sessionRef, int msgType, int seqId, Throwable error) {
        if (error != null) {
            if (!GeminiException.isLogicException(error)) {
                WechatLog.error("exception_perf handle cs msg fail! type:{} ", msgType, error);
            }
            final Core.Code retCode;
            if (!(error instanceof Exception)) {
                retCode = ErrorCode.FAILED.getCode();
            } else {
                retCode = ErrorCodeUtils.getCodeFromException((Exception) error);
            }
            int s2cMsgType = MsgType.getRetMsgId(msgType);
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), s2cMsgType, seqId, retCode);
        }
    }
}

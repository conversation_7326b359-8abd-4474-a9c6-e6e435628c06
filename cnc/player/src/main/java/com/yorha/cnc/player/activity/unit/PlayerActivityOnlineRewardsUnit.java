package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerLoginEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityOnlineRewardUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityOnlineTemplate;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 在线奖励
 */
public class PlayerActivityOnlineRewardsUnit extends BasePlayerActivityUnit implements PlayerEventListener {

    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityOnlineRewardsUnit.class);
    private int curTemplateId = 1;

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(PlayerLoginEvent.class);

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_ONLINE_REWARD, (owner, prop, template) ->
                new PlayerActivityOnlineRewardsUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerActivityOnlineRewardsUnit(PlayerActivity activity, ActivityUnitProp prop) {
        super(activity, prop);
    }

    private void init() {
        // 可重入保护 & 玩家在线时活动开启保护
        if (this.getProp().getCanReceiveTsMs() == 0) {
            ActivityOnlineTemplate curTemplate = ResHolder.getInstance().getValueFromMap(ActivityOnlineTemplate.class, curTemplateId);
            this.getProp().setCanReceiveTsMs(SystemClock.now() + TimeUtils.second2Ms(curTemplate.getOnlineSec()));
        }

    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
    }

    @Override
    public void onMigrate() {

    }

    /**
     * 判断是否可领取奖励，可领取则领取
     */
    private void checkReward() {
        if (curTemplateId > this.getMaxTemplateId()) {
            throw new GeminiException(ErrorCode.ONLINE_ACTIVITY_ALL_GOT);
        }

        // 未到领取时间，客户端没拦住
        if (this.getProp().getCanReceiveTsMs() > SystemClock.now()) {
            // 出现本日志的可能情况：客户端触发领取有误
            LOGGER.warn("PlayerActivityOnlineRewardsUnit checkReward player={}, canReceiveTsMs={}, curTsMs={}", player(), this.getProp().getCanReceiveTsMs(), SystemClock.now());
            throw new GeminiException(ErrorCode.ONLINE_ACTIVITY_CANT_TAKE_RWEARD);
        }

        takeReward();
    }

    /**
     * 记录并获取奖励
     */
    private void takeReward() {
        LOGGER.info("PlayerActivityOnlineRewardsUnit takeReward template:{}", curTemplateId);
        ActivityOnlineTemplate activityOnlineTemplate = ResHolder.getInstance().getValueFromMap(ActivityOnlineTemplate.class, curTemplateId);

        // 记录
        getProp().getRewardTakenIds().add(curTemplateId);

        // 展示用邮件
        StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder();
        mail.setMailTemplateId(activityOnlineTemplate.getMailId());
        mail.setIsOnlyForShow(true);

        // 发奖
        for (IntPairType awardPair : activityOnlineTemplate.getAwardPairList()) {
            int itemId = awardPair.getKey();
            int itemCnt = awardPair.getValue();
            mail.getItemRewardBuilder().addDatas(Struct.ItemPair.newBuilder()
                    .setItemTemplateId(itemId)
                    .setCount(itemCnt));

            player().getItemComponent().addItem(itemId, itemCnt, CommonEnum.Reason.ICR_ONLINE_ACTIVITY, String.valueOf(curTemplateId));
        }

        // 发邮件通知
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player().getPlayerId())
                .setZoneId(player().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, mail.build());

        curTemplateId++;
        if (curTemplateId > this.getMaxTemplateId()) {
            getProp().setCanReceiveTsMs(0);
        } else {
            ActivityOnlineTemplate newActivityOnlineTemplate = ResHolder.getInstance().getValueFromMap(ActivityOnlineTemplate.class, curTemplateId);
            // 根据领奖时间更新在线时间记录
            getProp().setCanReceiveTsMs(SystemClock.now() + TimeUtils.second2Ms(newActivityOnlineTemplate.getOnlineSec()));
        }
        LOGGER.info("PlayerActivityOnlineRewardsUnit takeReward done, curTemplateId={}, setCanReceiveTsMs={}", curTemplateId, getProp().getCanReceiveTsMs());
    }

    private ActivityOnlineRewardUnitProp getProp() {
        return unitProp.getOnlineRewardUnit();
    }


    /**
     * 获得上次登出时间戳(首次登录时返回可获取时间)
     *
     * @return long
     */
    private long getLastLogoutTsMs() {
        return player().getProp().getBasicInfo().getLastLogoutTsMs() != 0 ? player().getProp().getBasicInfo().getLastLogoutTsMs() : SystemClock.now();
    }

    private int getMaxTemplateId() {
        return Collections.max(ResHolder.getInstance().getListFromMap(ActivityOnlineTemplate.class), Comparator.comparing(ActivityOnlineTemplate::getId)).getId();
    }

    @Override
    public void onExpire() {
    }

    @Override
    public boolean isFinished() {
        return curTemplateId > this.getMaxTemplateId();
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        checkReward();
    }

    @Override
    public void forceOffImpl() {
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerLoginEvent) {
            curTemplateId = this.getProp().getRewardTakenIds().isEmpty() ? 1 : Collections.max(this.getProp().getRewardTakenIds()) + 1;
            final ActivityOnlineRewardUnitProp prop = getProp();
            if (curTemplateId <= this.getMaxTemplateId()) {
                final long lastCanReceiveTsMs = prop.getCanReceiveTsMs();
                ActivityOnlineTemplate curTemplate = ResHolder.getInstance().getValueFromMap(ActivityOnlineTemplate.class, curTemplateId);
                final long maxIntervalMs = TimeUtils.second2Ms(curTemplate.getOnlineSec());
                final long lastLogOutTsMs = this.getLastLogoutTsMs();
                long timeIntervalMs = lastCanReceiveTsMs - lastLogOutTsMs;
                // 防止异常数据&异常数据修复
                if (timeIntervalMs > maxIntervalMs) {
                    LOGGER.warn("PlayerActivityOnlineRewardsUnit onLogin new lastCanReceiveTsMs={}, lastLogoutTsMs={}, maxIntervalMs={}", lastCanReceiveTsMs, lastLogOutTsMs, maxIntervalMs);
                    timeIntervalMs = maxIntervalMs;
                }
                final long newCanReceiveTsMs = SystemClock.now() + timeIntervalMs;
                LOGGER.info("PlayerActivityOnlineRewardsUnit onLogin new timeIntervalMs={}", timeIntervalMs);
                prop.setCanReceiveTsMs(newCanReceiveTsMs);
            } else {
                prop.setCanReceiveTsMs(0);
            }
            LOGGER.info("PlayerActivityOnlineRewardsUnit onLogin new curTemplateId={}, setCanReceiveTsMs={}, canTake={}", curTemplateId, prop.getCanReceiveTsMs(), prop.getCanReceiveTsMs() <= SystemClock.now());
        }
    }

}




package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

/**
 * 体力道具，使用后增加体力
 */
public class AddEnergyItem extends AbstractUsableItem {

    public AddEnergyItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        final int addEnergy = calcAddEnergy();
        if (playerEntity.getEnergyComponent().willOverflowMaxValue(addEnergy)) {
            throw new GeminiException(ErrorCode.ENERGY_OVERFLOW_MAX_VALUE);
        }
    }

    private int calcAddEnergy() {
        return num * getTemplate().getEffectValue();
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        final int addEnergy = calcAddEnergy();
        String addEnergyReason = "energy_item";
        playerEntity.getEnergyComponent().addEnergy(addEnergy, true, addEnergyReason, getTemplate().getId());
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

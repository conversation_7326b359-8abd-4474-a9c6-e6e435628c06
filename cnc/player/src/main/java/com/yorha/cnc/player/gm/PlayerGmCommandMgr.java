package com.yorha.cnc.player.gm;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.gm.GmCommand;
import com.yorha.common.gm.GmCommandMgr;
import com.yorha.proto.SsClanBase;
import com.yorha.proto.SsSceneMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Locale;

/**
 * <AUTHOR>
 */
public class PlayerGmCommandMgr extends GmCommandMgr<PlayerActor> {
    private static final Logger LOGGER = LogManager.getLogger(PlayerGmCommandMgr.class);

    public static PlayerGmCommandMgr getInstance() {
        return PlayerGmCommandMgr.InstanceHolder.INSTANCE;
    }

    private static class InstanceHolder {
        private static final PlayerGmCommandMgr INSTANCE = new PlayerGmCommandMgr();
    }

    private PlayerGmCommandMgr() {
    }

    @Override
    protected String getCommandDir() {
        return "com.yorha.cnc.player.gm.command";
    }

    @Override
    public String handle(PlayerActor actor, long playerId, String command) {
        LOGGER.info("{} try execute gm:{}", actor, command);
        String commandName = StringUtils.substringBefore(command, " ").trim().toUpperCase(Locale.ROOT);
        if (StringUtils.isEmpty(commandName)) {
            return "commandName is empty";
        }
        GmCommand gmCommand = GmCommandMgr.COMMAND_STORE.get(commandName);
        if (gmCommand == null) {
            throw new GeminiException("find no gmCommand in COMMAND_STORE, name:{}", command);
        }
        if (StringUtils.contains(command, HELP) || StringUtils.contains(command, QUESTION_MARK)) {
            return gmCommand.showHelp();
        }
        String args = StringUtils.substringAfter(command, " ").trim();
        if (!checkValid(args)) {
            return "the command [" + command + "] is illegal";
        }
        if (gmCommand.isScene()) {
            SsSceneMap.ExecuteSceneGmAsk.Builder builder = SsSceneMap.ExecuteSceneGmAsk.newBuilder();
            SsSceneMap.ExecuteSceneGmAns ans = actor.callCurScene(builder.setCommand(command).setPlayerId(actor.getPlayerId()).build());
            return ans.hasResult() ? ans.getResult() : "OK";
        }
        if (gmCommand.isClan()) {
            SsClanBase.ExecuteClanGmAsk.Builder builder = SsClanBase.ExecuteClanGmAsk.newBuilder();
            SsClanBase.ExecuteClanGmAns ans = actor.callCurClan(builder.setCommand(command).setPlayerId(actor.getPlayerId()).build());
            return ans.hasResult() ? ans.getResult() : "OK";
        }
        // 执行命令逻辑
        gmCommand.execute(actor, playerId, buildArgs(args));
        return "ok";
    }
}

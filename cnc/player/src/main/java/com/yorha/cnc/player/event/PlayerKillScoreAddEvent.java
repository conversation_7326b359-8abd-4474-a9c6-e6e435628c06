package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家击杀积分增加事件
 *
 * <AUTHOR>
 */
public class PlayerKillScoreAddEvent extends PlayerEvent {
    private final long killScore;

    public PlayerKillScoreAddEvent(PlayerEntity entity, long killScore) {
        super(entity);
        this.killScore = killScore;
    }

    public long getKillScore() {
        return killScore;
    }
}

package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;

public class PlayerBestCommanderSubRankUnit extends PlayerScoreRankUnit {

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_BEST_COMMANDER_SUB_RANK, (owner, prop, template) ->
                new PlayerBestCommanderSubRankUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerBestCommanderSubRankUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void addPoints(int pointsTemplateId, long addPoints) {
        super.addPoints(pointsTemplateId, addPoints);
        // 找到父活动中的BestCommanderTotalRankUnit，加积分
        if (addPoints > 0) {
            PlayerBestCommanderTotalRankUnit totalRankUnit = ownerActivity.findFirstUnitInTopFatherActOf(PlayerBestCommanderTotalRankUnit.class);
            totalRankUnit.addPoints(addPoints);
        }
    }

    @Override
    public void onExpire() {
        super.onExpire();
        PlayerBestCommanderUnit bestCommanderUnit = ownerActivity.findFirstUnitInTopFatherActOf(PlayerBestCommanderUnit.class);
        if (bestCommanderUnit == null) {
            return;
        }
        bestCommanderUnit.onRankUnitExpire(ownerActivity.getActivityId());

    }
}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.game.gen.prop.PlayerUnitModelProp;
import com.yorha.proto.SsSceneDungeon;

/**
 * <AUTHOR>
 */
public class PlayerUnitComponent extends PlayerComponent {

    public PlayerUnitComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerUnitModelProp getProp() {
        return getOwner().getProp().getPlayerUnitModel();
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
//        //FIXME 暂时兵种解锁依赖英雄持有
//        Collection<PlayerHeroProp> heroes = getOwner().getHeroComponent().getAllHero();
//        for (PlayerHeroProp hero : heroes) {
//            int unitId = ResHolder.getTemplate(HeroRhTemplate.class, hero.getHeroId()).getRelatedUnit();
//            if (unitId > 0) {
//                if (!getProp().getPlayerUnitMap().containsKey(unitId)) {
//                    initUnit(unitId);
//                }
//            }
//        }
    }

//    public void initUnit(int unitId) {
//        if (getProp().getPlayerUnitMap().containsKey(unitId)) {
//            return;
//        }
//
//        UnitRhTemplate template = ResHolder.getTemplate(UnitRhTemplate.class, unitId);
//        if (template == null) {
//            return;
//        }
//
//        PlayerUnitProp prop = getProp().getPlayerUnitMap().addEmptyValue(unitId);
//        prop.setLevel(1).setExp(0).setUnitId(unitId);
//    }


    //兵种等级
//    public int getUnitLevel(int unitId) {
//        PlayerUnitProp prop = getProp().getPlayerUnitMap().get(unitId);
//        if (prop == null) {
//            return 0;
//        } else {
//            return prop.getLevel();
//        }
//    }

//    public int getMaxUnitLevel() {
//        int maxLevel = 0;
//        for (var prop : getProp().getPlayerUnitMap().entrySet()) {
//            if (prop.getValue().getLevel() > maxLevel) {
//                maxLevel = prop.getValue().getLevel();
//            }
//        }
//        return maxLevel;
//    }
//
//    /**
//     * 英雄升级接口
//     */
//    public void unitLevelUpHandler(int unitId, List<StructPB.ItemPB> itemPBList) {
//        if (unitId <= 0 || itemPBList == null || itemPBList.isEmpty()) {
//            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "invalid unitid or items");
//        }
//
//        PlayerUnitProp unitProp = getProp().getPlayerUnitMap().get(unitId);
//        if (unitProp == null) {
//            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unit is missing");
//        }
//
//        // 升级等级检测
//        unitMaxLevelCheck(unitProp);
//        // 经验书检测
//        totalExpCheck(itemPBList);
//
//        // 计算总经验量
//        long totalExp = calcTotalExp(unitProp, itemPBList);
//
//        // 道具检测
//        AssetPackage.Builder consumeBuilder = AssetPackage.builder();
//        itemPBList.forEach(it -> consumeBuilder.plusItem(it.getTemplateId(), it.getNum()));
//        AssetPackage consume = consumeBuilder.build();
//        getOwner().verifyThrow(consume);
//
//        // 适用道具
//        getOwner().consume(consume, CommonEnum.Reason.ICR_UNIT_LEVEL_UP, String.valueOf(unitId));
//
//        int upLevel = execAddUnitExp(unitId, totalExp);
//
//        //触发任务-兵种升级
//        new PlayerUnitLevelUpEvent(getOwner(), unitId, unitProp.getLevel(), upLevel).dispatch();
//    }

//    private int getMaxLevelLimit() {
//        // 等级限制
//        int buildType = ResHolder.getResService(TroopResService.class).getBuildTypeForUnitLevel();
//        int buildLevel = getOwner().getInnerBuildRhComponent().getInnerBuildLevel(buildType);
//        if (buildLevel <= 0) {
//            return 0;
//        }
//        BuildUpgradeRhTemplate upgradeTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(buildType, buildLevel);
//        if (upgradeTemplate == null) {
//            return 0;
//        }
//        return upgradeTemplate.getSoldierLevelMax();
//    }
//
//    // 兵种最大等级检测
//    private void unitMaxLevelCheck(PlayerUnitProp unitProp) {
//        int levelLimit = getMaxLevelLimit();
//        if (unitProp.getLevel() >= levelLimit) {
//            // 已抵达限制等级
//            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId(), "unit level is at limit");
//        }
//        Map<Integer, UnitsLevelTemplate> unitLevelMap = ResHolder.getInstance().getMap(UnitsLevelTemplate.class);
//        if (!unitLevelMap.containsKey(unitProp.getLevel() + 1)) {
//            // 已抵达最大等级
//            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId(), "no more unit level");
//        }
//    }
//
//    private void totalExpCheck(List<StructPB.ItemPB> itemPBList) {
//        int effectType = CommonEnum.ItemUseType.UNIT_EXP_ITEM.getNumber();
//        itemPBList.forEach(it -> {
//            ItemTemplate itemTemplate = ResHolder.getInstance().getValueFromMap(ItemTemplate.class, it.getTemplateId());
//            if (effectType != itemTemplate.getEffectType()) {
//                // 道具效果类型错误
//                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unit level item is invalid");
//            }
//        });
//    }
//
//    /**
//     * 根据道具计算总经验(道具效果类型检测)
//     * min(当前星级最大经验值, 道具提供的实际经验值)
//     */
//    private long calcTotalExp(PlayerUnitProp unitProp, List<StructPB.ItemPB> pbs) {
//        int itemTotalExp = pbs.stream()
//                .mapToInt(it -> {
//                            ItemTemplate itemTemplate = ResHolder.getInstance().getValueFromMap(ItemTemplate.class, it.getTemplateId());
//                            return itemTemplate.getEffectValue() * it.getNum();
//                        }
//                )
//                .sum();
//
//        int maxLevel = getMaxLevelLimit();
//        UnitsLevelTemplate template = ResHolder.getResService(TroopResService.class).getUnitsLevelTemplate(unitProp.getUnitId(), maxLevel);
//        if (template == null) {
//            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId(), "UnitsLevelTemplate for maxLevel is missing");
//        }
//        long levelExpLimit = template.getExpCost();
//        long maxLevelLaveExp = Math.max(levelExpLimit - unitProp.getExp(), 0);
//        long realExp = Math.min(itemTotalExp, maxLevelLaveExp);
//        LOGGER.info("build item exp. realExp:{}", realExp);
//        return realExp;
//    }
//
//
//    /**
//     * 添加经验
//     */
//    private int execAddUnitExp(int unitId, long totalExp) {
//        int upLevelNum = 0;
//        if (totalExp < 0) {
//            LOGGER.error("totalExp < 0");
//            return 0;
//        }
//        PlayerUnitProp unitProp = getProp().getPlayerUnitMap().get(unitId);
//        try {
//            // 升级所需经验
//            if (totalExp > 0) {
//                unitProp.setExp(Math.addExact(unitProp.getExp(), totalExp));
//            }
//            int oldLevel = unitProp.getLevel();
//            while (isCanLevelUp(unitProp)) {
//                execLevelUp(unitProp);
//            }
//
//            upLevelNum = unitProp.getLevel() - oldLevel;
//
//        } catch (ArithmeticException e) {
//            LOGGER.error("hero:{} out limit exp:{}, addExp:{}", unitId, unitProp.getExp(), totalExp);
//        }
//        return upLevelNum;
//    }

//    private boolean isCanLevelUp(PlayerUnitProp unitProp) {
//        if (unitProp.getLevel() >= getMaxLevelLimit()) {
//            return false;
//        }
//
//        UnitsLevelTemplate unitsLevelTemplate = ResHolder.getResService(TroopResService.class).getUnitsLevelTemplate(unitProp.getUnitId(), unitProp.getLevel() + 1);
//        if (unitsLevelTemplate == null) {
//            return false;
//        }
//        int needExp = unitsLevelTemplate.getExpCost();
//        return (unitProp.getExp() >= needExp);
//    }
//
//    /**
//     * 执行升级
//     */
//    private void execLevelUp(PlayerUnitProp unitProp) {
//        unitProp.setLevel(unitProp.getLevel() + 1);
//    }


}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ClanOperationType;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerClan;
import com.yorha.proto.PlayerRally.*;
import com.yorha.proto.SsSceneRallyAssist.*;
import com.yorha.proto.StructPlayer;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_RALLY)
public class PlayerRallyController {

    /**
     * 取消集结
     */
    @CommandMapping(code = MsgType.PLAYER_CANCELRALLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CancelRally_C2S msg) {
        PlayerCancelRallyAsk.Builder call = PlayerCancelRallyAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        playerEntity.ownerActor().callCurScene(call.setRallyId(msg.getRallyId()).build());
        return Player_CancelRally_S2C.newBuilder().setRallyId(msg.getRallyId()).build();
    }

    /**
     * 获取集结列表
     */
    @CommandMapping(code = MsgType.PLAYER_QUERYRALLYLIST_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryRallyList_C2S msg) {
        PlayerActor playerActor = playerEntity.ownerActor();
        FetchRallyListAsk.Builder call = FetchRallyListAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        FetchRallyListAns ans = playerActor.callCurScene(call.build());
        return MsgUtils.parseCsProto(MsgType.PLAYER_QUERYRALLYLIST_S2C, ans.getMsgBytes());
    }

    /**
     * 获取指定id的集结详细信息
     */
    @CommandMapping(code = MsgType.PLAYER_QUERYONERALLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryOneRally_C2S msg) {
        PlayerActor playerActor = playerEntity.ownerActor();
        // 客户端说拿不到指定集结信息不用返回error
        try {
            FetchOneRallyAsk.Builder call = FetchOneRallyAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
            FetchOneRallyAns ans = playerActor.callCurScene(call.setRallyId(msg.getRallyId()).build());
            return MsgUtils.parseCsProto(MsgType.PLAYER_QUERYONERALLY_S2C, ans.getMsgBytes());
        } catch (GeminiException e) {
            return Player_QueryOneRally_S2C.getDefaultInstance();
        }
    }

    /**
     * 遣返某个集结成员
     */
    @CommandMapping(code = MsgType.PLAYER_REPATRIATEONEARMYINRALLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RepatriateOneArmyInRally_C2S msg) {
        RepatriateRallyMemberAsk.Builder call = RepatriateRallyMemberAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        playerEntity.ownerActor().callCurScene(call.setArmyId(msg.getArmyId()).setRallyId(msg.getRallyId()).build());
        return Player_RepatriateOneArmyInRally_S2C.getDefaultInstance();
    }

    /**
     * 设置推荐兵种
     */
    @CommandMapping(code = MsgType.PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SetRallyRecommendSoldierType_C2S msg) {
        SetRallyRecommendSoldierAsk.Builder call = SetRallyRecommendSoldierAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        call.getSoldierTypeBuilder().addAllDatas(msg.getRecommendSoldierTypeList().getDatasList());
        call.setIsAssist(msg.getIsAssist());
        boolean isPermission = true;
        if (msg.getIsAssist()) {
            call.setEntityId(msg.getBuildingId());
        } else {
            call.setEntityId(msg.getRallyId());
        }
        call.setIsPermission(isPermission);
        playerEntity.ownerActor().callCurScene(call.build());
        Player_SetRallyRecommendSoldierType_S2C.Builder builder = Player_SetRallyRecommendSoldierType_S2C.newBuilder();
        builder.setRallyId(msg.getRallyId()).setIsAssist(msg.getIsAssist()).setBuildingId(msg.getBuildingId()).setRecommendSoldierTypeList(msg.getRecommendSoldierTypeList());
        return builder.build();
    }

    /**
     * 拉取预警界面
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHWARNINGLIST_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FetchWarningList_C2S msg) {
        FetchWarningAsk.Builder call = FetchWarningAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        FetchWarningAns ans = playerEntity.ownerActor().callCurScene(call.build());
        return MsgUtils.parseCsProto(MsgType.PLAYER_FETCHWARNINGLIST_S2C, ans.getMsgBytes());
    }

    /**
     * 设置预警忽略字段
     */
    @CommandMapping(code = MsgType.PLAYER_SETWARNINGITEMIGNORETAG_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SetWarningItemIgnoreTag_C2S msg) {
        SetWarningItemTagAsk.Builder call = SetWarningItemTagAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        playerEntity.ownerActor().callCurScene(call.setArmyId(msg.getArmyId()).setIgnore(msg.getIgnore()).build());
        return Player_SetWarningItemIgnoreTag_S2C.getDefaultInstance();
    }

    /**
     * 忽略预警
     */
    @CommandMapping(code = MsgType.PLAYER_IGNOREWARNINGALL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_IgnoreWarningAll_C2S msg) {
        IgnoreAllWarningAsk.Builder call = IgnoreAllWarningAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        playerEntity.ownerActor().callCurScene(call.build());
        return Player_IgnoreWarningAll_S2C.getDefaultInstance();
    }

    /**
     * 拉取某个建筑的援助界面
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCITYINNERARMY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FetchCityInnerArmy_C2S msg) {
        FetchInnerArmyAsk.Builder call = FetchInnerArmyAsk.newBuilder().setOperatorId(playerEntity.getEntityId());
        // 优先判定客户端使用targetId，后判定客户端使用playerId
        if (msg.hasTargetId()) {
            FetchInnerArmyAns ans = playerEntity.ownerActor().callCurScene(call.setTargetId(msg.getTargetId()).build());
            return MsgUtils.parseCsProto(MsgType.PLAYER_FETCHCITYINNERARMY_S2C, ans.getMsgBytes());
        } else if (msg.hasPlayerId()) {
            FetchInnerArmyAns ans = playerEntity.ownerActor().callCurScene(call.setTargetPlayerId(msg.getPlayerId()).build());
            return MsgUtils.parseCsProto(MsgType.PLAYER_FETCHCITYINNERARMY_S2C, ans.getMsgBytes());
        }
        throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
    }

    /**
     * 遣返某个援助军队
     */
    @CommandMapping(code = MsgType.PLAYER_REPATRIATEONEARMYINASSIST_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RepatriateOneArmyInAssist_C2S msg) {
        // 获取操作玩家是否有相应权限
        int staffId = playerEntity.getProp().getClan().getStaffId();
        boolean isPermission = ClanPermissionUtils.hasPermissionNoThrow(ClanOperationType.COT_REMOVE_FROM_RALLY, staffId);
        // 玩家可能遣返自己的军队，无权限下玩家也可以遣返自己的军队
        RepatriateAssistMemberAsk.Builder call = RepatriateAssistMemberAsk.newBuilder();
        call.setPlayerId(playerEntity.getEntityId()).setTargetId(msg.getTargetId()).setArmyId(msg.getArmyId()).setIsPermission(isPermission);
        playerEntity.ownerActor().callCurScene(call.build());
        Player_RepatriateOneArmyInAssist_S2C.Builder builder = Player_RepatriateOneArmyInAssist_S2C.newBuilder();
        builder.setArmyId(msg.getArmyId()).setTargetId(msg.getTargetId());
        return Player_RepatriateOneArmyInAssist_S2C.newBuilder().setArmyId(msg.getArmyId()).setTargetId(msg.getTargetId()).build();
    }

    /**
     * 更换联盟建筑援助防守队长
     */
    @CommandMapping(code = MsgType.PLAYER_CHANGEDEFENSIVELEADER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ChangeDefensiveLeader_C2S msg) {
        // 获取操作玩家是否有相应权限
        int staffId = playerEntity.getProp().getClan().getStaffId();
        boolean isPermission = ClanPermissionUtils.hasPermissionNoThrow(ClanOperationType.COT_CHANGE_RALLY_CAPTAIN, staffId);
        // 玩家可能自己就是防守队长，无权限下玩家也可以更换防守队长
        ChangeAssistLeaderAsk.Builder call = ChangeAssistLeaderAsk.newBuilder();
        call.setIsPermission(isPermission).setCityId(msg.getEntityId()).setTargetId(msg.getArmyId());
        playerEntity.ownerActor().callCurScene(call.setPlayerId(playerEntity.getEntityId()).build());
        return PlayerClan.Player_ChangeDefensiveLeader_S2C.getDefaultInstance();
    }

    /**
     * 拉取自己的援助历史
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCITYASSISTHISTORY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FetchCityAssistHistory_C2S msg) {
        Player_FetchCityAssistHistory_S2C.Builder builder = Player_FetchCityAssistHistory_S2C.newBuilder();
        playerEntity.getProp().getFragments().getAssistHistory().copyToCs(builder.getAssistHistoryBuilder());
        return builder.build();
    }

    /**
     * 发送集结邀请
     */
    @CommandMapping(code = MsgType.PLAYER_SENDRALLYINVITEMSG_C2S)
    public void handle(PlayerEntity playerEntity, Player_InviteJoinRally_C2S msg, int seqId) {
        FetchOneRallyAsk.Builder call = FetchOneRallyAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        FetchOneRallyAns ans = playerEntity.ownerActor().callCurScene(call.setRallyId(msg.getRallyId()).build());

        StructPlayer.RallyInfo rallyInfo = MsgUtils.parseProto(StructPlayer.RallyArmyInfo.getDefaultInstance(), ans.getMsgBytes());
        CommonEnum.MessageType messageType = CommonEnum.MessageType.MT_RALLY_INVITE;
        CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();

        messageData.getRallyInviteBuilder().setRallyId(msg.getRallyId());

        switch (rallyInfo.getTargetType()) {
            case RTT_CITY:
                messageData.getMsgParamBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(""))
                        .addDatas(MsgHelper.buildDisPlayTextPb(rallyInfo.getTargetCardHead().getName()));
                break;
            case RTT_RALLY_ARMY:
                messageData.getMsgParamBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(rallyInfo.getTargetClanShortName()))
                        .addDatas(MsgHelper.buildDisPlayMultiLangKeyPb("rally_target_2"));
                break;
            case RTT_MAP_BUILDING:
                if (rallyInfo.getTargetClanId() == 0) {
                    messageData.getMsgParamBuilder().getParamsBuilder()
                            .addDatas(MsgHelper.buildDisPlayMultiLangKeyPb("rally_target_1"));
                } else {
                    messageData.getMsgParamBuilder().getParamsBuilder()
                            .addDatas(MsgHelper.buildDisPlayTextPb(rallyInfo.getTargetClanShortName()));
                }
                messageData.getMsgParamBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayIdPb(CommonEnum.DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, rallyInfo.getTargetTemplateId()));
                break;
            case RTT_MONSTER:
                messageData.getMsgParamBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayTextPb(""))
                        .addDatas(MsgHelper.buildDisPlayIdPb(CommonEnum.DisplayParamType.DPT_MONSTER_ID, rallyInfo.getTargetTemplateId()));
                break;
            default:
                break;
        }
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        IActorRef session = playerEntity.ownerActor().sender();
        chatPlayerEntity.getHandleChatComponent().chatRequest(msg.getChatSession(), messageType, messageData.build(),
                (e) -> playerEntity.answerMsgToClient(session, seqId,
                        MsgType.PLAYER_SENDRALLYINVITEMSG_S2C,
                        e,
                        Player_InviteJoinRally_S2C.getDefaultInstance()
                ));
    }

    /**
     * 查看集结邀请
     */
    @CommandMapping(code = MsgType.PLAYER_CLICKRALLYINVITE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ClickRallyInvite_C2S msg) {
        PlayerActor actor = playerEntity.ownerActor();
        FetchOneRallyAsk.Builder call = FetchOneRallyAsk.newBuilder().setPlayerId(playerEntity.getEntityId()).setRallyId(msg.getRallyId());
        FetchOneRallyAns callAns = null;
        try {
            if (actor.getBigSceneId() == actor.getEntity().getCurSceneId()) {
                callAns = actor.callScene(actor.getEntity().getCurSceneId(), call.build());
            } else {
                callAns = actor.call(actor.getEntity().getSceneMgrComponent().getDungeonRef(), call.build());
            }
        } catch (Exception e) {
            if (GeminiException.isLogicException(e)) {
                GeminiException exception = (GeminiException) e;
                if (exception.getCodeId() == ErrorCode.CLAN_NOT_IN.getCodeId()) {
                    // 不在军团中，抛一个客户端能显示的错误码
                    throw new GeminiException(ErrorCode.CAN_NOT_OPEN_RALLY_INVITE);
                } else {
                    // 集结不存在
                    throw new GeminiException(ErrorCode.RALLY_NOT_EXIST);
                }
            }
            throw e;
        }
        return MsgUtils.parseCsProto(MsgType.PLAYER_CLICKRALLYINVITE_S2C, callAns.getMsgBytes());
    }
}

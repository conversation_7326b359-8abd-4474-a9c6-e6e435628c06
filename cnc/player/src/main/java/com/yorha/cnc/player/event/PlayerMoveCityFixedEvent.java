package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.shape.Point;

public class PlayerMoveCityFixedEvent extends PlayerTaskEvent {
    private final Point point;

    public PlayerMoveCityFixedEvent(PlayerEntity entity, Point point) {
        super(entity);
        this.point = point;
    }

    public Point getPoint() {
        return point;
    }
}

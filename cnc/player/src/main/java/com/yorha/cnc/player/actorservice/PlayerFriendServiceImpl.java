package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.PlayerFriendService;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.MigrateHelper;
import com.yorha.game.gen.prop.FriendApplyInfoProp;
import com.yorha.game.gen.prop.SimpleFriendInfoProp;
import com.yorha.game.gen.prop.WaitResponseInfoProp;
import com.yorha.proto.SsPlayerFriend;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PlayerFriendServiceImpl implements PlayerFriendService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerFriendServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerFriendServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    @Override
    public void handleAddNewFriendAsk(SsPlayerFriend.AddNewFriendAsk ask) {
        final SsPlayerFriend.AddNewFriendAns.Builder ans = SsPlayerFriend.AddNewFriendAns.newBuilder();
        FriendPlayerEntity friendPlayerEntity = playerActor.getOrLoadFriendPlayerEntity();
        long playerId = ask.getPlayerId();
        // 如果A在B的好友列表而B不在A的好友列表，则可能是之前B给A的同意回包丢失，这里给A补发一个同意回包; 或者是重复请求
        if (friendPlayerEntity.hasFriend(playerId)) {
            Struct.PlayerCardHead.Builder cardHead = friendPlayerEntity.getCardHead().getCopySsBuilder();
            SsPlayerFriend.AgreeAddFriendAsk.Builder builder = SsPlayerFriend.AgreeAddFriendAsk.newBuilder();
            builder.setPlayerId(playerActor.getPlayerId())
                    .setCardHead(cardHead);
            playerActor.answer(ans.build());
            int zoneId = ask.getZoneId();
            playerActor.tellPlayer(zoneId, playerId, builder.build());
            return;
        }
        // 玩家申请列表已满，则不允许申请加好友
        if (friendPlayerEntity.isApplyFull()) {
            throw new GeminiException(ErrorCode.FRIEND_REACH_APPLY_LIMIT);
        }
        // 将申请消息存入玩家的申请列表
        FriendApplyInfoProp prop = friendPlayerEntity.getMaintenanceComponent().addApplyInfoToList(playerId, ask.getZoneId(), ask.getCardHead());
        LOGGER.info("handleAddNewFriendAsk end, player {} add new friendApplyInfo {}", playerActor.getPlayerId(), prop);
        playerActor.answer(ans.build());
    }

    @Override
    public void handleAgreeAddFriendAsk(SsPlayerFriend.AgreeAddFriendAsk ask) {
        SsPlayerFriend.AgreeAddFriendAns.Builder ans = SsPlayerFriend.AgreeAddFriendAns.newBuilder();
        FriendPlayerEntity friendPlayerEntity;
        try {
            friendPlayerEntity = playerActor.getOrLoadFriendPlayerEntity();
        } catch (MigrateException e) {
            LOGGER.info("handleAgreeAddFriendAsk detect player {} migrate, curZone = {}, targetZone = {}", playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            if (playerActor.getCurrentEnvelope().isByTell()) {
                // 对于tell的方式，直接转发请求
                ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
                IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
                MigrateHelper.transferMsg(envelope, targetPlayer);
            } else {
                throw e;
            }
            return;
        }
        long playerId = ask.getPlayerId();
        int zoneId = ask.getZoneId();
        Struct.PlayerCardHead cardHead = ask.getCardHead();
        // 保证收到多个相同的ask，结果一样
        if (friendPlayerEntity.hasFriend(playerId)) {
            LOGGER.info("handleAgreeAddFriendAsk, you and player {} has been friend", playerId);
            ans.setCardHead(friendPlayerEntity.getCardHead().getCopySsBuilder());
            playerActor.answer(ans.build());
            return;
        }
        // 判断自身好友是否已满
        if (friendPlayerEntity.isFriendFull()) {
            throw new GeminiException(ErrorCode.FRIEND_PLAYER_FRIENDLIST_IS_FULL);
        }
        // 添加到好友列表,并移除相应的等待响应消息
        SimpleFriendInfoProp friendInfoProp = friendPlayerEntity.getMaintenanceComponent().addPlayerToFriendList(playerId, zoneId, cardHead);
        WaitResponseInfoProp waitResponseInfoProp = friendPlayerEntity.getMaintenanceComponent().removeWaitResponseInfo(playerId);

        // 对于AB互相申请加好友的情况，B的申请列表里可能也有A，也许移除申请列表里的数据
        if (friendPlayerEntity.hasApply(playerId)) {
            friendPlayerEntity.getMaintenanceComponent().removeApplyInfo(playerId);
        }
        LOGGER.info("handleAgreeAddFriendAsk end, player {} add new friendInfo {}, remove waitInfo {}", playerActor.getPlayerId(), friendInfoProp, waitResponseInfoProp);
        // AgreeNewFriend代码中使用了两种调用方式，call和tell
        if (playerActor.getCurrentEnvelope().isByTell()) {
            return;
        }
        ans.setCardHead(friendPlayerEntity.getCardHead().getCopySsBuilder());
        playerActor.answer(ans.build());
    }

    @Override
    public void handleRefuseAddFriendAsk(SsPlayerFriend.RefuseAddFriendAsk ask) {
        SsPlayerFriend.RefuseAddFriendAns.Builder ans = SsPlayerFriend.RefuseAddFriendAns.newBuilder();
        FriendPlayerEntity friendPlayerEntity = playerActor.getOrLoadFriendPlayerEntity();
        long playerId = ask.getPlayerId();
        // 删除等待回应信息
        WaitResponseInfoProp waitResponseInfoProp = friendPlayerEntity.getMaintenanceComponent().removeWaitResponseInfo(playerId);
        LOGGER.info("handleRefuseAddFriendAsk end, player {}  remove waitInfo {}", playerActor.getPlayerId(), waitResponseInfoProp);
        playerActor.answer(ans.build());
    }

    @Override
    public void handleDelFriendAsk(SsPlayerFriend.DelFriendAsk ask) {
        SsPlayerFriend.DelFriendAns.Builder ans = SsPlayerFriend.DelFriendAns.newBuilder();
        FriendPlayerEntity friendPlayerEntity = playerActor.getOrLoadFriendPlayerEntity();
        long playerId = ask.getPlayerId();
        if (friendPlayerEntity.hasFriend(playerId)) {
            SimpleFriendInfoProp prop = friendPlayerEntity.getMaintenanceComponent().removeFriendInfo(playerId);
            LOGGER.info("handleRefuseAddFriendAsk end, player {}  remove friendInfo {}", playerActor.getPlayerId(), prop);
        }
        playerActor.answer(ans.build());
    }

    @Override
    public void handleJudgeBeShieldAsk(SsPlayerFriend.JudgeBeShieldAsk ask) {
        final FriendPlayerEntity friendPlayerEntity = playerActor.getOrLoadFriendPlayerEntity();
        SsPlayerFriend.JudgeBeShieldAns.Builder ans = SsPlayerFriend.JudgeBeShieldAns.newBuilder();
        if (friendPlayerEntity.hasShieldPlayer(ask.getCheckId())) {
            ans.setResult(true);
        }
        playerActor.answer(ans.build());
    }
}

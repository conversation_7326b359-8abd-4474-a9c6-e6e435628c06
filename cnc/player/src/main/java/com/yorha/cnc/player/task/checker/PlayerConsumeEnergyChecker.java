package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerConsumeEnergyEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.CONSUME_ENERGY_TOTAL;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_RECEIVE;

/**
 * <AUTHOR>
 */
public class PlayerConsumeEnergy<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerConsumeEnergyEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.getFirst();

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            int energyTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(CONSUME_ENERGY_TOTAL);
            prop.setProcess(Math.min(energyTotal, param1));
        } else if (taskTemplate.getTaskCalculationMethod() == TCT_RECEIVE) {
            if (event instanceof PlayerConsumeEnergyEvent) {
                int energyNum = ((PlayerConsumeEnergyEvent) event).getEnergyNum();
                prop.setProcess(Math.min(prop.getProcess() + energyNum, param1));
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param1;
    }
}

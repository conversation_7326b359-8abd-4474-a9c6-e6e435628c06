package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.event.task.AddItemEvent;
import com.yorha.cnc.player.event.task.PlayerUseItemEvent;
import com.yorha.cnc.player.item.ItemUtils;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetDesc;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.ItemDesc;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.qlog.json.player.ItemInfo;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemWithdrawConf;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int64ItemMapProp;
import com.yorha.game.gen.prop.ItemProp;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.Struct;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogItemFlow;
import res.template.ItemTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.statistic.StatisticEnum.ADD_ITEM_TOTAL;
import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_SP_ITEM;

/**
 * 玩家道具模块
 * <p>
 * 每个Item有一个生成的唯一key和配置的templateId，道具在服务器侧不会堆叠，key和templateId是1:1对应的
 *
 * @Fix yuhy 2024年4月11日 唯一key就是templateId
 */
public class PlayerItemComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerItemComponent.class);

    private ActorTimer expireTimer;

    /**
     * 过期时间最近的时间（缓存，添加道具时用）
     */
    private long recentlyExpireTsMs;

    static {
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, PlayerItemComponent::onMainCityUpgradeEvent);
    }

    public PlayerItemComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            // 给初始道具
            List<IntPairType> initialItem = ResHolder.getResService(ConstKVResService.class).getTemplate().getInitialItem();
            if (CollectionUtils.isNotEmpty(initialItem)) {
                for (IntPairType item : initialItem) {
                    ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, item.getKey());
                    if (itemTemplate == null) {
                        LOGGER.error("{} init item error {}", getOwner(), item.getKey());
                    } else {
                        addItem(itemTemplate, item.getValue(), CommonEnum.Reason.ICR_INIT, "");
                    }
                }
            }
        }
        checkExpire();
    }

    private Int64ItemMapProp getProp() {
        return getOwner().getProp().getItems();
    }

    /**
     * 检测过期的道具
     */
    private void checkExpire() {
        long now = SystemClock.now();
        long triggerTsMs = Integer.MAX_VALUE;
        recentlyExpireTsMs = 0;
        // 创建一个列表，用于存放需要移除的过期道具，统一删除
        List<ItemProp> remove = new ArrayList<>();

        // 遍历所有道具，收集过期的
        for (ItemProp value : getProp().values()) {
            if (value.getExpiredTime() <= 0) {
                continue;
            }
            if (value.getExpiredTime() <= now) {
                remove.add(value);
                continue;
            }
            triggerTsMs = Math.min(triggerTsMs, value.getExpiredTime());
        }

        if (!remove.isEmpty()) {
            LOGGER.info("PlayerItemComponent checkExpire remove, removeSize={}", remove.size());
            for (ItemProp itemProp : remove) {
                reduceItem(itemProp, itemProp.getNum(), CommonEnum.Reason.ICR_ITEM_WITHDRAW, "ItemExpire");
            }
        }

        long delayMs = 0;
        if (triggerTsMs < Integer.MAX_VALUE) {
            delayMs = triggerTsMs - now;
            if (delayMs <= 0) {
                WechatLog.error("PlayerItemComponent checkExpire, triggerTsMs={} now={}", triggerTsMs, now);
                return;
            }
            recentlyExpireTsMs = triggerTsMs;
        }
        // 执行过期计时器操作，传入延迟时间
        refreshExpireTimer(delayMs);
    }

    /**
     * 维护定时器
     *
     * @param delayMs 延迟时间触发  0就return
     */
    private void refreshExpireTimer(long delayMs) {
        if (expireTimer != null && !expireTimer.isCanceled()) {
            expireTimer.cancel();
            expireTimer = null;
        }

        if (delayMs <= 0) {
            return;
        }
        expireTimer = ownerActor().addTimer(TimerReasonType.PLAYER_ITEM_EXPIRE, this::checkExpire, delayMs, TimeUnit.MILLISECONDS);
    }

    public boolean hasEnoughAll(AssetPackage assetPackage) {
        // 这里其实还是有一点风险
        // 如果AssetPackage构造的时候使用了plusStandalone方法，这里的判断其实就是有问题的
        for (AssetDesc asset : assetPackage.getImmutableAssets()) {
            if (asset instanceof ItemDesc
                    && !innerHasEnough(asset.getId(), (int) asset.getAmount())) {
                return false;
            }
        }
        return true;
    }

    public boolean hasEnough(int itemTemplateId, int num) {
        return innerHasEnough(itemTemplateId, num);
    }

    private boolean innerHasEnough(int itemTemplateId, int num) {
        ItemProp itemProp = getProp().get((long) itemTemplateId);
        if (itemProp == null) {
            return false;
        }
        return !isExpired(itemProp) && itemProp.getNum() >= num;
    }

    public int getItemNum(int itemTemplateId) {
        ItemProp itemProp = getProp().get((long) itemTemplateId);

        if (itemProp == null || isExpired(itemProp)) {
            return 0;
        }
        return itemProp.getNum();
    }

    public boolean notEnough(int itemTemplateId, int num) {
        return !hasEnough(itemTemplateId, num);
    }

    /**
     * cd中
     */
    private static boolean isCd(ItemProp itemProp) {
        return itemProp.getCd() > 0 && itemProp.getCd() >= SystemClock.now();
    }

    /**
     * 是否过期
     */
    private static boolean isExpired(ItemProp itemProp) {
        return itemProp.getExpiredTime() > 0 && itemProp.getExpiredTime() <= SystemClock.now();
    }

    public void addItem(ItemDesc itemDesc, CommonEnum.Reason reason, String subReason) {
        addItem(itemDesc.getTemplate(), (int) itemDesc.getAmount(), reason, subReason);
    }

    public void addItem(int itemTemplateId, int count, CommonEnum.Reason reason, String subReason) {
        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, itemTemplateId);
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        addItem(itemTemplate, count, reason, subReason);
    }

    /**
     * 添加道具的唯一入口，过期道具会被直接吞掉
     */
    public void addItem(ItemTemplate itemTemplate, int count, CommonEnum.Reason reason, String subReason) {
        if (count <= 0) {
            throw new GeminiException("addItem count={}", count);
        }

        final long itemTemplateId = itemTemplate.getId();


        // 过期检测，已过期的话直接return
        ItemWithdrawConf.List withdrawConf = ResHolder.getResService(ItemResService.class).findWithdrawConf(itemTemplate.getId());
        if (withdrawConf != null && ItemUtils.shouldWithdrawNow(getOwner(), withdrawConf)) {
            LOGGER.warn("addItem but expired. {} {}-{} {}-{}", getOwner(), itemTemplateId, count, reason, subReason);
            return;
        }
        ItemProp itemProp = getProp().get(itemTemplateId);

        // 如果不存在就创建Prop
        if (!getProp().containsKey(itemTemplateId)) {
            long expiredTime = ItemUtils.expireMillis(getOwner(), withdrawConf);
            itemProp = getProp().addEmptyValue(itemTemplateId);
            itemProp.setTemplateId(itemTemplate.getId()).setExpiredTime(expiredTime);
            LOGGER.info("player {} add new item {}", getOwner(), itemTemplate.getId());
            // 新增过期时间，需要重新设置定时器（和最近到期的时间对比一下，看有没有必要更新定时器）
            if (recentlyExpireTsMs > 0 && expiredTime < recentlyExpireTsMs) {
                checkExpire();
            }
        }

        // 最大持有检测
        final int beforeCount = itemProp.getNum();
        int afterCount = beforeCount + count;
        if (afterCount > itemTemplate.getOwnLimit()) {
            afterCount = itemTemplate.getOwnLimit();
            LOGGER.info("PlayerItemComponent addItem, shouldAdd={} ownCount={} limit={}", count, beforeCount, itemTemplate.getOwnLimit());
            sendQlog(CommonEnum.Reason.ICR_ITEM_WITHDRAW, "outLimit", itemTemplateId, beforeCount, afterCount, beforeCount + count - afterCount, 1);
        }
        final int realAddCount = afterCount - beforeCount;
        itemProp.setNum(afterCount);
        // 更新统计项
        getOwner().getStatisticComponent().recordSecondStatistic(ADD_ITEM_TOTAL, itemProp.getTemplateId(), realAddCount);
        // 任务事件触发
        new AddItemEvent(getOwner(), itemProp.getTemplateId(), realAddCount).dispatch();
        LOGGER.debug("player {} add item {}:{}->{}", getOwner(), itemTemplateId, beforeCount, afterCount);

        sendQlog(reason, subReason, itemTemplateId, beforeCount, afterCount, afterCount - beforeCount, 0);

        // 立即使用
        if (itemTemplate.getUseNow()) {
            try {
                useItemByTemplateId(itemTemplateId, count, null);
            } catch (GeminiException e) {
                throw e;
            }
        }
    }

    public String getBagQlogInfo() {
        List<ItemInfo> itemInfos = new ArrayList<>();
        for (ItemProp value : getProp().values()) {
            ItemInfo info = new ItemInfo();
            info.setItemId(value.getTemplateId());
            info.setIcount(value.getNum());
            itemInfos.add(info);
        }
        return JsonUtils.toJsonString(itemInfos);
    }

    private void sendQlog(CommonEnum.Reason reason, String subReason, long itemId, int beforeCount, int afterCount, int i, int i2) {
        QlogItemFlow.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setIGoodsId(Long.toString(itemId))
                .setBeforeCount(beforeCount)
                .setAfterCount(afterCount)
                .setCount(i)
                .setAddOrReduce(i2)
                .setReason(reason.toString())
                .setSubReason(subReason)
                .setIGoodsType(0)
                .setIMoney(0)
                .setIMoneyType(0).sendToQlog();
    }

    public void consume(ItemDesc itemDesc, CommonEnum.Reason reason, String subReason) {
        ItemProp itemProp = getProp().get((long) itemDesc.getId());
        if (itemProp == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        reduceItem(itemProp, (int) itemDesc.getAmount(), reason, subReason);
    }

    public void consume(int itemId, int num, CommonEnum.Reason reason, String subReason) {
        ItemProp itemProp = getProp().get((long) itemId);
        if (itemProp == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        reduceItem(itemProp, num, reason, subReason);
    }

    private void reduceItem(ItemProp itemProp, int count, CommonEnum.Reason reason, String subReason) {
        if (count <= 0) {
            // 在这里做兜底吧
            throw new GeminiException("try reduce item count={} id={}", count, itemProp.getTemplateId());
        }
        int before = itemProp.getNum();
        int remain = itemProp.getNum() - count;
        itemProp.setNum(remain);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} reduce item {} num {},before {},after {}, reason {}", getOwner(), itemProp.getTemplateId(), count, before, remain, reason);
        }

        // 删除道具
        if (remain <= 0) {
            removeItem(itemProp);
        }
        ItemTemplate itemTemplate = ResHolder.getResService(ItemResService.class).getItemTemplate(itemProp.getTemplateId());

        if (reason != CommonEnum.Reason.ICR_ITEM_WITHDRAW) {
            getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.USE_ITEM_TOTAL, itemProp.getTemplateId(), count);
            if (!itemTemplate.getUseNow()) {
                // 该统计项需要过滤自动使用道具
                getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.USE_ITEMTYPE_TOTAL, itemTemplate.getEffectType(), count);
            }
            new PlayerUseItemEvent(getOwner(), itemProp.getTemplateId(), itemTemplate.getEffectType(), count).dispatch();
        }

        sendQlog(reason, subReason, itemProp.getTemplateId(), before, remain, before - remain, 1);
    }

    /**
     * 唯一删除入口。数量扣为0时删除，如果该物品处于冷却(CD)，则暂时不移除，直到下次重登时timer触发
     */
    private void removeItem(ItemProp itemProp) {
        // 有CD的不删，直到CD过去
        if (itemProp == null || itemProp.getCd() > SystemClock.now()) {
            return;
        }
        LOGGER.info("PlayerItemComponent removeItem, removeKey={}", itemProp.getKey());
        getProp().remove(itemProp.getKey());
    }

    public PlayerCommon.Player_UseItem_S2C.Builder useItemByKey(long itemKey, int useNum, ItemUseParamsProp params) {
        ItemProp itemProp = getProp().get(itemKey);
        if (itemProp == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        if (isExpired(itemProp)) {
            throw new GeminiException(ErrorCode.ITEM_IS_EXPIRED);
        }
        getOwner().getSettingComponent().checkSpassword(SPWC_SP_ITEM, itemProp.getTemplateId(), params.getSPassWord());
        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, itemProp.getTemplateId());
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        if (itemTemplate.getBatchUseLimit() < useNum) {
            throw new GeminiException(ErrorCode.ITEM_BATCH_USE_LIMIT);
        }
        return useItem(itemProp, itemTemplate, useNum, params);
    }

    public PlayerCommon.Player_UseItem_S2C.Builder useItemByTemplateId(long itemTemplateId, int num, ItemUseParamsProp params) {
        // itemId就是templateId 要改就要客户端配合
        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, (int) itemTemplateId);
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        ItemProp itemProp = getProp().get(itemTemplateId);
        if (itemProp == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        if (isExpired(itemProp)) {
            throw new GeminiException(ErrorCode.ITEM_IS_EXPIRED);
        }
        return useItem(itemProp, itemTemplate, num, params);
    }

    private PlayerCommon.Player_UseItem_S2C.Builder useItem(ItemProp itemProp, ItemTemplate itemTemplate, int useNum, ItemUseParamsProp params) {
        if (useNum <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (itemProp.getNum() < useNum) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        if (isCd(itemProp)) {
            throw new GeminiException(ErrorCode.ITEM_IN_CD);
        }
        CommonEnum.ItemUseType itemUseType = CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType());
        if (itemUseType == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_USEABLE);
        }
        AbstractUsableItem usableItem = ItemUtils.newInstance(itemTemplate, useNum, itemUseType);
        if (usableItem == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_USEABLE);
        }
        // 条件验证
        usableItem.verifyThrow(getOwner(), params);
        CommonEnum.Reason reason = usableItem.getItemReason(getOwner());
        // 道具消耗
        reduceItem(itemProp, useNum, reason, "");
        // 道具使用
        boolean result = usableItem.use(getOwner(), params);
        if (!result) {
            // 使用失败后回滚道具
            LOGGER.info("PlayerItemComponent use item fail, return item, item={} num={}", itemProp.getTemplateId(), useNum);
            addItem(itemProp.getTemplateId(), useNum, CommonEnum.Reason.ICR_RETURN, "");
            throw new GeminiException(ErrorCode.ITEM_USE_FAIL);
        }
        PlayerCommon.Player_UseItem_S2C.Builder response = PlayerCommon.Player_UseItem_S2C.newBuilder();
        usableItem.responseMessage(response);

        // 设置cd
        if (itemTemplate.getCdSeconds() > 0) {
            itemProp.setCd(SystemClock.now() + TimeUnit.SECONDS.toMillis(itemTemplate.getCdSeconds()));
        }

        LOGGER.debug("player {} use item {} num {}", getOwner(), itemTemplate.getId(), useNum);
        response.setItemKey(itemProp.getTemplateId()).setNum(useNum);
        return response;
    }

    public void clearWithGm() {
        getProp().clear();
        checkExpire();
        LOGGER.info("player {} clear all items", getOwner());
    }

    public List<ItemReward> sendReward(int rewardId, CommonEnum.Reason reason, String subReason) {
        List<ItemReward> rewardList = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
        if (CollectionUtils.isEmpty(rewardList)) {
            return rewardList;
        }
        rewardList.forEach(reward -> addItem(reward.getItemTemplateId(), reward.getCount(), reason, subReason));
        return rewardList;
    }

    private static void onMainCityUpgradeEvent(MainCityUpgradeStaticEvent event) {

    }

    /**
     * 清空指定id的道具，没有任何补偿，会打info日志(此接口被定义为带有一定危险性，调用者并不明确要删掉多少道具)
     */
    public void clearItems(List<Integer> itemIds, CommonEnum.Reason reason, String subReason) {
        for (Integer itemId : itemIds) {
            int itemNum = getItemNum(itemId);
            if (itemNum > 0) {
                LOGGER.info("{} clearItems {} {}", getOwner(), itemId, itemNum);
                this.consume(itemId, itemNum, reason, subReason);
            }
        }
    }

    public void fixItemWithOldAccount() {
        List<ItemProp> targetList = new ArrayList<>();
        for (ItemProp value : getProp().values()) {
            if (value.getKey() == value.getTemplateId()) {
                continue;
            }
            targetList.add(value);
        }
        LOGGER.info("PlayerItemComponent fixItemWithOldAccount start. size={}", targetList.size());
        if (targetList.size() <= 0) {
            return;
        }
        for (ItemProp oldProp : targetList) {
            try {
                getProp().remove(oldProp.getKey());

                Struct.Item.Builder builder = Struct.Item.newBuilder();
                oldProp.copyToSs(builder);
                builder.setKey(oldProp.getTemplateId());
                Struct.Item newItem = builder.build();
                ItemProp newProp = getProp().addEmptyValue((long) oldProp.getTemplateId());
                newProp.mergeFromSs(newItem);
                LOGGER.info("PlayerItemComponent fixItemWithOldAccount success. oldKey={} templateId={} num={}", oldProp.getKey(), oldProp.getTemplateId(), oldProp.getNum());
            } catch (Exception e) {
                LOGGER.error("PlayerItemComponent fixItemWithOldAccount error oldProp={}", oldProp, e);
            }
        }

    }
}

package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.devbuff.PlayerDevBuffMgr;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;

import java.util.List;
import java.util.Map;

/**
 * 玩家buff在player，scenePlayer分别存储
 * <p>
 * buff更新流程：
 * player侧：player
 * scene侧: player -> scenePlayer
 *
 * <AUTHOR>
 */

public class PlayerDevBuffComponent extends PlayerComponent implements AdditionProviderInterface {

    private PlayerDevBuffMgr devBuffMgr;

    public PlayerDevBuffComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        devBuffMgr = new PlayerDevBuffMgr(getOwner());
    }

    @Override
    public void postLoad(boolean isRegister) {
        devBuffMgr.postInit();
    }

    public void refreshAdditionCache() {
        devBuffMgr.refreshAdditionCache();
    }

    /**
     * 添加buff
     *
     * @param buffId buffId
     */
    public void addDevBuff(int buffId, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                null,
                devBuffMgr.getBuffType(),
                sourceType,
                null);
    }

    /**
     * 添加buff 无视配表结束时间
     *
     * @param buffId  buffId
     * @param endTime 自定义的结束时间
     */
    public void addDevBuff(int buffId, Long endTime, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                endTime,
                devBuffMgr.getBuffType(),
                sourceType,
                null);
    }

    /**
     * 添加buff 无视配表结束时间
     *
     * @param buffId  buffId
     * @param endTime 自定义的结束时间
     */
    public void addDevBuff(int buffId,
                           Long startTime,
                           Long endTime,
                           CommonEnum.DevBuffType buffType,
                           CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                startTime,
                endTime,
                buffType,
                sourceType,
                null);
    }

    public void addDevBuff(int buffId,
                           Long startTime,
                           Long endTime,
                           CommonEnum.DevBuffType buffType,
                           CommonEnum.DevBuffSourceType sourceType,
                           int addLayer) {
        devBuffMgr.addDevBuff(buffId, startTime, endTime, buffType, sourceType, addLayer);
    }

    /**
     * 添加指定层数buff
     *
     * @param layer 层数
     */
    public void addDevBuffByLayer(int buffId, Long startTime,
                                  Long endTime,
                                  CommonEnum.DevBuffType buffType,
                                  CommonEnum.DevBuffSourceType sourceType,
                                  int layer) {
        devBuffMgr.addDevBuff(buffId,
                startTime,
                endTime,
                buffType,
                sourceType,
                layer);
    }

    /**
     * 移除buff
     *
     * @param buffId buffId
     */
    public DevBuffProp removeDevBuff(int buffId) {
        return devBuffMgr.removeDevBuff(buffId, null, false);
    }

    /**
     * 移除叠加buff
     *
     * @param buffId buffId
     * @param layer  层数
     */
    public void removeDevBuffByLayer(int buffId, int layer) {
        devBuffMgr.removeDevBuff(buffId, layer, false);
    }

    public boolean canAddDevBuff(int buffId) {
        return devBuffMgr.canAddDevBuff(buffId);
    }

    public List<DevBuffProp> getDevBuffByBuffType(CommonEnum.DevBuffType type) {
        return devBuffMgr.getDevBuffByBuffType(type);
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.BUFF;
    }

    /**
     * buff比较特殊，加在Player上的buff必然是提供player上用的加成。
     * 为了防止player和scenePlayer的加成相互覆盖，所以该接口只返回player上用的加成
     */
    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        if (AdditionUtil.isSceneAddition(additionId)) {
            return Maps.newHashMap();
        }
        return devBuffMgr.getAdditionValue(additionId);
    }

    public void addDevBuffFromScenePlayer(CommonMsg.DevBuffAddParam param) {
        devBuffMgr.addDevBuff(param.getDevBuffId(),
                param.hasStartTime() ? param.getStartTime() : null,
                param.hasEndTime() ? param.getEndTime() : null,
                param.getBuffType(),
                param.getSourceType(),
                param.hasLayer() ? param.getLayer() : null);
    }

    public void removeDevBuffFromScenePlayer(CommonMsg.DevBuffRemoveParam param) {
        devBuffMgr.removeDevBuff(param.getDevBuffId(),
                param.hasLayer() ? param.getLayer() : null,
                false);
    }

    public DevBuffSysProp getAllDevBuffCopy(DevBuffSysProp prop) {
        DevBuffSysProp sysProp = new DevBuffSysProp();
        for (Map.Entry<Integer, DevBuffProp> entry : prop.getDevBuff().entrySet()) {
            DevBuffProp p = new DevBuffProp();
            p.mergeFromSs(entry.getValue().getCopySsBuilder().build());
            sysProp.putDevBuffV(p);
        }
        return sysProp;
    }
}

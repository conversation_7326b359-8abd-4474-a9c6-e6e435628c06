package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.event.PlayerKillScoreAddEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.rank.RankHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.rank.RankDataTemplateService;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsRank.*;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RankTemplate;

import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.RESBUILDING_COLLECT_RESOURCE_NUM;
import static com.yorha.common.enums.statistic.StatisticEnum.RESOURCE_ASSIST_NUM;

/**
 * 排行榜相关
 *
 * <AUTHOR> Jiang
 */
public class PlayerRankComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerRankComponent.class);
    private static final int OUTSIDE_RANK = -1;

    static {
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, PlayerRankComponent::monitorMainCityUpgradeEvent);
        EntityEventHandlerHolder.register(PlayerKillScoreAddEvent.class, PlayerRankComponent::monitorPlayerKillScoreAddEvent);
    }

    public PlayerRankComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            return;
        }
        // 个人采集排行榜
        LOGGER.info("PlayerRankSnapshot rankId={} score={}", RankConstant.ZONE_PLAYER_COLLECT_RANK,
                getCollectScore());
        // 个人击杀排行榜
        getOwner().getDataRecordComponent().refreshKillScore();
        LOGGER.info("PlayerRankSnapshot rankId={} score={}", RankConstant.ZONE_PLAYER_KILL_RANK,
                getOwner().getDataRecordComponent().getRecordValue(CommonEnum.DataRecordType.DRT_KILL_SCORE));
        // 个人战力排行榜
        LOGGER.info("PlayerRankSnapshot rankId={} score={}", RankConstant.ZONE_PLAYER_POWER_RANK,
                getOwner().getProp().getPlayerPowerInfo().getTotalPower());
    }

    /**
     * 更新排行
     */
    public void updateZoneRanking(int rankId, long score) {
        if (!isNeedSyncToZoneRank(rankId)) {
            return;
        }
        UpdateRankingCmd.Builder build = UpdateRankingCmd.newBuilder();
        build.setMember(RankHelper.buildMember(getPlayerId(), score, getOwner().getZoneId()));
        UpdateRankingCmd msg = build.setRankId(rankId).build();
        // 本服的
        ownerActor().tellZoneRank(getOwner().getZoneId(), msg);
    }

    /**
     * 普通排行榜明细
     */
    public GetRankPageInfoAns getRankInfoList(int rankId, int page) {
        GetRankPageInfoAsk.Builder build = GetRankPageInfoAsk.newBuilder();
        build.setPage(page);
        build.setRankId(rankId);
        RankTemplate template = ResHolder.findTemplate(RankTemplate.class, rankId);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 设置自己的memberId
        switch (template.getMember()) {
            case RankConstant.MEMBER_PLAYER:
                build.setMemberId(getPlayerId());
                break;
            case RankConstant.MEMBER_CLAN:
                build.setMemberId(getOwner().getProp().getClan().getClanId());
                break;
            case RankConstant.MEMBER_ZONE:
                build.setMemberId(getOwner().getProp().getZoneModel().getZoneId());
                break;
            default:
                throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
        }
        return ownerActor().callZoneRank(build.build());
    }

    /**
     * 普通排行榜榜首
     */
    public GetTopRankInfoAns getTopRankInfo(int rankId) {
        GetTopRankInfoAsk.Builder build = GetTopRankInfoAsk.newBuilder();
        build.setRankId(rankId);
        return ownerActor().callZoneRank(build.build());
    }

    public long getCollectScore() {
        Map<Integer, Struct.SingleStatistic> alreadyCollectNum = getOwner().getStatisticComponent().getAllSecondStatistic(RESBUILDING_COLLECT_RESOURCE_NUM);
        long collectScore = 0L;
        RankDataTemplateService service = ResHolder.getResService(RankDataTemplateService.class);
        for (Integer currencyKey : alreadyCollectNum.keySet()) {
            CommonEnum.CurrencyType type = CommonEnum.CurrencyType.forNumber(currencyKey);
            if (null == type) {
                LOGGER.error("updateCollectRank error, currencyKey:{}", currencyKey);
                continue;
            }
            collectScore += alreadyCollectNum.get(currencyKey).getValue() * service.getCoefficientByCurrencyType(type);
        }
        return collectScore;
    }

    public long getAssistScore() {
        Map<Integer, Struct.SingleStatistic> alreadyCollectNum = getOwner().getStatisticComponent().getAllSecondStatistic(RESOURCE_ASSIST_NUM);
        long assistScore = 0L;
        RankDataTemplateService service = ResHolder.getResService(RankDataTemplateService.class);
        for (Integer currencyKey : alreadyCollectNum.keySet()) {
            CommonEnum.CurrencyType type = CommonEnum.CurrencyType.forNumber(currencyKey);
            if (null == type) {
                LOGGER.error("updateCollectRank error, currencyKey:{}", currencyKey);
                continue;
            }
            assistScore += alreadyCollectNum.get(currencyKey).getValue() * service.getAssistCoefficientByCurrencyType(type);
        }
        return assistScore;
    }

    /**
     * 更新采集排行榜
     */
    public void updateCollectRank() {
        updateZoneRanking(RankConstant.ZONE_PLAYER_COLLECT_RANK, getCollectScore());
    }

    public static void monitorMainCityUpgradeEvent(MainCityUpgradeStaticEvent event) {
        int mainCityLevel = event.getPlayer().getCityLevel();
        event.getPlayer().getPlayerRankComponent().updateZoneRanking(RankConstant.ZONE_PLAYER_CITY_LEVEL_RANK, mainCityLevel);
    }

    /**
     * @return 返回是否需要同步到全区排行榜上
     */
    public boolean isNeedSyncToZoneRank(int rankId) {
        final int mainCityLevel = getOwner().getCityLevel();
        final RankTemplate template = ResHolder.findTemplate(RankTemplate.class, rankId);
        if (mainCityLevel < template.getRankOpen()) {
            return false;
        }
        final boolean isMigratePlayer = getOwner().isMigratePlayer();
        return !template.getLimitMigrate() || !isMigratePlayer;
    }

    /**
     * 玩家击杀积分增加时的事件处理
     *
     * @param event 击杀积分事件
     */
    public static void monitorPlayerKillScoreAddEvent(PlayerKillScoreAddEvent event) {
        long killScore = event.getKillScore();
        event.getPlayer().getPlayerRankComponent().updateZoneRanking(RankConstant.ZONE_PLAYER_KILL_RANK, killScore);
    }

    /**
     * 获取当玩家处于榜外时，显示需要的信息
     * 统一风格，仿写其他排行榜相关接口
     *
     * @param rankId 排行榜id
     */
    public StructMsg.RankInfoDTO getOutSideRankInfoDTO(int rankId) {
        StructMsg.RankInfoDTO.Builder dtoBuilder = StructMsg.RankInfoDTO.newBuilder();
        RankTemplate template = ResHolder.findTemplate(RankTemplate.class, rankId);
        if (template == null) {
            LOGGER.error("rankId {} not exist", rankId);
            return dtoBuilder.build();
        }
        if (!template.getIsResident()) {
            LOGGER.info("rankId {} is not resident", rankId);
            return dtoBuilder.build();
        }
        if (getOwner().getPlayerClanComponent().isInClan()) {
            dtoBuilder.mergeFrom(getOwner().getPlayerClanComponent().getClanRankSimpleInfo(rankId));
            dtoBuilder.setRank(OUTSIDE_RANK);
        }
        switch (rankId) {
            case RankConstant.ZONE_PLAYER_POWER_RANK:
            case RankConstant.ZONE_PLAYER_KILL_RANK:
            case RankConstant.ZONE_PLAYER_CITY_LEVEL_RANK:
            case RankConstant.ZONE_PLAYER_COLLECT_RANK:
            case RankConstant.ZONE_PLAYER_EXPEDITION_STAR_RANK:
                dtoBuilder.setPlayerId(getPlayerId());
                dtoBuilder.setCardHead(getOwner().getProp().getAvatarModel().getCardHead().getCopyCsBuilder());
                dtoBuilder.setValue(getRankValueByTypeFromPlayer(rankId));
                dtoBuilder.setRank(OUTSIDE_RANK);
                dtoBuilder.setRankId(rankId);
                break;
            default:
                LOGGER.debug("rankId {} no need to set data from player", rankId);
                break;
        }
        return dtoBuilder.build();
    }

    /**
     * 根据排行榜id在player身上获取排行榜数据
     *
     * @param rankId 排行榜id
     * @return 对应类型的排行榜数据，类型数据不存在时返回0L
     */
    private long getRankValueByTypeFromPlayer(int rankId) {
        switch (rankId) {
            case RankConstant.ZONE_PLAYER_POWER_RANK:
                return getOwner().getProp().getPlayerPowerInfo().getTotalPower();
            case RankConstant.ZONE_PLAYER_KILL_RANK:
                return getOwner().getDataRecordComponent().getRecordValue(CommonEnum.DataRecordType.DRT_KILL_SCORE);
            case RankConstant.ZONE_PLAYER_CITY_LEVEL_RANK:
                return getOwner().getCityLevel();
            case RankConstant.ZONE_PLAYER_COLLECT_RANK:
                return getCollectScore();
            default:
                LOGGER.error("rankId {} cannot get data from player", rankId);
                break;
        }
        return 0L;
    }
}

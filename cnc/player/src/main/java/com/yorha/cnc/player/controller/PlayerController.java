package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.component.PlayerItemComponent;
import com.yorha.cnc.player.event.PlayerManualCityFallEvent;
import com.yorha.cnc.player.gm.PlayerGmCommandMgr;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.ItemDesc;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.player.AvatarResService;
import com.yorha.common.resource.resservice.shop.ShopDataTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.SingleStatisticProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.CommonEnum.MoveCityType;
import com.yorha.proto.PlayerCommon.*;
import com.yorha.proto.SsSceneCityArmy.*;
import com.yorha.proto.SsSceneObj.AddMonsterAns;
import com.yorha.proto.SsSceneObj.AddMonsterAsk;
import com.yorha.proto.SsSceneObj.PlayerSearchMonsterAns;
import com.yorha.proto.SsSceneObj.PlayerSearchMonsterAsk;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncClientInfo;
import qlog.flow.QlogCncSeparateDownload;
import res.template.CommanderAvatarFrameTemplate;
import res.template.CommanderAvatarTemplate;
import res.template.ConstTemplate;
import res.template.ShopTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.error.ErrorCode.PARAM_PARAMETER_EXCEPTION;
import static com.yorha.proto.CommonEnum.Reason.ICR_ACHIEVEMENT;
import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_CLOSE;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_USER)
public class PlayerController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerController.class);

    /**
     * 客户端debug指令
     */
    @CommandMapping(code = MsgType.PLAYER_DEBUGCOMMAND_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_DebugCommand_C2S msg) {
        Player_DebugCommand_S2C.Builder builder = Player_DebugCommand_S2C.newBuilder();
        String result;
        try {
            if (!ServerContext.getServerDebugOption().isGmSwitch()) {
                throw new GeminiException(ErrorCode.GM_GM_DISABLED.getCodeId());
            }
            WechatLog.getInstance().openScreenByGm();
            result = PlayerGmCommandMgr.getInstance().handle(playerEntity.ownerActor(), playerEntity.getPlayerId(), msg.getCommand());
        } catch (Exception e) {
            LOGGER.error("gm execute fail. msg:{}", msg, e);
            result = StringUtils.format("gm fail, cause:{}", e.getMessage());
        } finally {
            WechatLog.getInstance().closeScreenByGm();
        }
        // s2c
        builder.setResult(result);
        return builder.build();
    }

    /**
     * 客户端获取debug指令
     */
    @CommandMapping(code = MsgType.PLAYER_GETDEBUGCOMMANDLIST_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_GetDebugCommandList_C2S msg) {
        Collection<String> result = PlayerGmCommandMgr.getInstance().getDebugCommandList(msg.getGroup());
        if (!ServerContext.getServerDebugOption().isGmSwitch()) {
            throw new GeminiException(ErrorCode.GM_GM_DISABLED.getCodeId());
        }
        // s2c
        Player_GetDebugCommandList_S2C.Builder builder = Player_GetDebugCommandList_S2C.newBuilder();
        builder.setCommandList(JsonUtils.toJsonString(result));
        return builder.build();
    }

    /**
     * 按等级搜索范围内的野怪
     */
    @CommandMapping(code = MsgType.PLAYER_SEARCHMONSTER_C2S)
    public GeneratedMessageV3 searchMonster(PlayerEntity playerEntity, Player_SearchMonster_C2S msg) {
        if (!playerEntity.isInMainScene()) {
            throw new GeminiException(ErrorCode.MONSTER_SEARCH_MUST_BIG_SCENE.getCodeId());
        }
        final int level = msg.getLevel();
        if (level <= 0) {
            throw new GeminiException(ErrorCode.MONSTER_SEARCH_LEVEL_LIMT.getCodeId());
        }
        // 先寻找
        PlayerActor playerActor = playerEntity.ownerActor();
        PlayerSearchMonsterAsk.Builder call = PlayerSearchMonsterAsk.newBuilder();
        call.setPlayerId(playerEntity.getEntityId()).setLevel(msg.getLevel());
        PlayerSearchMonsterAns ans = playerActor.callCurScene(call.build());
        Player_SearchMonster_S2C.Builder response = Player_SearchMonster_S2C.newBuilder();
        if (ans.hasMonsterId()) {
            response.setMonsterId(ans.getMonsterId());
            response.getBornPointBuilder().setX(ans.getPos().getX()).setY(ans.getPos().getY());
            return response.build();
        }
        // 没有找到
        int autoAddMonsterCount = playerEntity.getScheduleComponent().reduceSystemAddMonsterCount();
        // 压测关闭检测
        if (!ServerContext.getServerDebugOption().isBattleTestServer()) {
            if (autoAddMonsterCount >= ResHolder.getResService(ConstKVResService.class).getTemplate().getAutoAddMonsterLimit()) {
                LOGGER.info("search failed MonsterSearch failed {} {}", playerEntity, ErrorCode.MONSTER_AUTO_ADD_LIMIT.getDesc());
                throw new GeminiException(ErrorCode.MONSTER_AUTO_ADD_LIMIT.getCodeId());
            }
        }
        long nowMs = SystemClock.now();
        // 自动补怪
        AddMonsterAsk.Builder addCall = SsSceneObj.AddMonsterAsk.newBuilder();
        addCall.setPlayerId(playerEntity.getEntityId()).setLevel(msg.getLevel());
        AddMonsterAns addAns = playerActor.callCurScene(addCall.build());
        if (!addAns.hasMonsterId()) {
            LOGGER.info("search failed MonsterSearch failed {} 补怪找点失败", playerEntity);
            throw new GeminiException(ErrorCode.MONSTER_SEARCH_FAIL.getCodeId());
        }
        // 记录补怪次数
        playerEntity.getProp().getFragments().getBigSceneRecords().setAutoAddMonsterCount(++autoAddMonsterCount);
        playerEntity.getScheduleComponent().updateByExecuteTime(nowMs);
        response.setMonsterId(addAns.getMonsterId());
        response.getBornPointBuilder().setX(addAns.getPos().getX()).setY(addAns.getPos().getY()).build();
        return response.build();
    }

    @CommandMapping(code = MsgType.PLAYER_VERIFYMOVECITY_C2S)
    public GeneratedMessageV3 checkMoveCity(PlayerEntity playerEntity, Player_VerifyMoveCity_C2S msg) {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        Player_VerifyMoveCity_S2C.Builder response = Player_VerifyMoveCity_S2C.newBuilder();
        MoveCityVerifyAsk.Builder call = MoveCityVerifyAsk.newBuilder();
        call.setPlayerId(playerEntity.getEntityId()).setX(msg.getX()).setY(msg.getY());
        // 总结
        // 能满足新手迁城的 就返回新手迁城道具（有道具）
        // 能满足领土迁城的 返回领土迁城道具 (有道具)
        // 能满足领土迁城的  没领土迁城道具，也没定点迁城道具 返回领土迁城道具
        // 能满足领土迁城的  没领土迁城道具，有定点迁城道具 返回定点迁城
        // 返回定点迁城 错误码及道具
        //新手迁城校验
        PlayerItemComponent itemComponent = playerEntity.getItemComponent();
        if (itemComponent.hasEnough(constTemplate.getItemIdmoveCitynewPlayer(), 1)) {
            MoveCityVerifyAns ans = playerEntity.ownerActor().callCurScene(call.setMoveType(MoveCityType.MCT_NEWPLAYER).build());
            if (ans.getErrorCode() == ErrorCode.OK.getCodeId()) {
                response.setErrorCode(ErrorCode.OK.getCodeId());
                response.setItemKey(constTemplate.getItemIdmoveCitynewPlayer())
                        .setMoveType(MoveCityType.MCT_NEWPLAYER);
                return response.build();
            }
        }
        // 怎么都要查一下领土迁城  优化：没联盟就不用查了
        if (playerEntity.getClanId() != 0) {
            MoveCityVerifyAns ans = playerEntity.ownerActor().callCurScene(call.setMoveType(MoveCityType.MCT_TERRITORY).build());
            if (ErrorCode.isOK(ans.getErrorCode())) {
                response.setErrorCode(ErrorCode.OK.getCodeId()).setMoveType(MoveCityType.MCT_TERRITORY);
                if (itemComponent.hasEnough(constTemplate.getItemIdmoveCityManor(), 1)) {
                    response.setItemKey(constTemplate.getItemIdmoveCityManor());
                    return response.build();
                }
                if (itemComponent.hasEnough(constTemplate.getItemIdmoveCityNormal(), 1)) {
                    response.setItemKey(constTemplate.getItemIdmoveCityNormal());
                    return response.build();
                }
                response.setItemKey(constTemplate.getItemIdmoveCityManor());
                return response.build();
            }
        }
        //定点迁城
        MoveCityVerifyAns ans = playerEntity.ownerActor().callCurScene(call.setMoveType(MoveCityType.MCT_NORMAL).build());
        response.setErrorCode(ans.getErrorCode());
        response.setItemKey(constTemplate.getItemIdmoveCityNormal())
                .setMoveType(MoveCityType.MCT_NORMAL);
        return response.build();
    }

    @CommandMapping(code = MsgType.PLAYER_CHANGEPLAYERNAME_C2S)
    public GeneratedMessageV3 changePlayerName(PlayerEntity playerEntity, Player_ChangePlayerName_C2S msg) {
        String newPlayerName = msg.getName();
        playerEntity.getProfileComponent().checkModifyName(msg.getType(), newPlayerName);
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        switch (msg.getType()) {
            case CNT_ITEM: {
                PlayerItemComponent itemComponent = playerEntity.getItemComponent();
                itemComponent.consume(new ItemDesc(constTemplate.getModifyNameItemId(), constTemplate.getModifyNameItemNum()), CommonEnum.Reason.ICR_CHANGE_NAME, "");
                break;
            }
            case CNT_MONEY: {
                ShopTemplate shopConf = ResHolder.getResService(ShopDataTemplateService.class).findItemShopConf(constTemplate.getModifyNameItemId());
                if (shopConf == null) {
                    throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
                }
                IntPairType pricePair = shopConf.getPricePair();
                CurrencyType currencyType = CurrencyType.forNumber(pricePair.getKey());
                if (currencyType == null) {
                    throw new GeminiException("currencyType not recognized. {}", pricePair.getKey());
                }
                playerEntity.getPurseComponent().consume(currencyType, pricePair.getValue(), CommonEnum.Reason.ICR_CHANGE_NAME, "");
                break;
            }

            default:
                throw new GeminiException(PARAM_PARAMETER_EXCEPTION);
        }
        playerEntity.getProfileComponent().modifyPlayerName(msg.getName());
        return Player_ChangePlayerName_S2C.newBuilder().setName(msg.getName()).build();
    }

    @CommandMapping(code = MsgType.PLAYER_CHANGEAVATAR_C2S)
    public GeneratedMessageV3 changeAvatar(PlayerEntity playerEntity, Player_ChangeAvatar_C2S msg) {
        switch (msg.getType()) {
            case CAT_PIC: {
                playerEntity.getProfileComponent().checkModifyPic();
                if (!ResHolder.getInstance().getMap(CommanderAvatarTemplate.class).containsKey(msg.getId())) {
                    throw new GeminiException(PARAM_PARAMETER_EXCEPTION);
                }
                List<Integer> initPic = ResHolder.getResService(AvatarResService.class).getInitPic();
                if (!initPic.contains(msg.getId()) && !playerEntity.hasUnlockPic(msg.getId())) {
                    throw new GeminiException(ErrorCode.AVATAR_PIC_LOCKED);
                }
                playerEntity.getProfileComponent().modifyPic(msg.getId());
                break;
            }
            case CAT_PICFRAME: {
                if (!ResHolder.getInstance().getMap(CommanderAvatarFrameTemplate.class).containsKey(msg.getId())) {
                    throw new GeminiException(PARAM_PARAMETER_EXCEPTION);
                }
                List<Integer> initPicFrame = ResHolder.getResService(AvatarResService.class).getInitPicFrame();
                if (!initPicFrame.contains(msg.getId()) && !playerEntity.hasUnlockPicFrame(msg.getId())) {
                    throw new GeminiException(ErrorCode.AVATAR_PIC_FRAME_LOCKED);
                }
                playerEntity.getProfileComponent().modifyPicFrame(msg.getId());
                break;
            }
            default:
                throw new GeminiException(PARAM_PARAMETER_EXCEPTION);
        }
        return Player_ChangeAvatar_S2C.newBuilder().build();
    }

    /**
     * 切换视野场景
     */
    @CommandMapping(code = MsgType.PLAYER_SWITCHVIEWSCENE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SwitchViewScene_C2S msg) {
        // 场景id
        int scene = msg.getScene();
        Player_SwitchViewScene_S2C.Builder builder = Player_SwitchViewScene_S2C.newBuilder();
        builder.setScene(scene);
        return builder.build();
    }

    /**
     * 查询玩家简单名片数据
     */
    @CommandMapping(code = MsgType.PLAYER_QUERYPLAYERCARDINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryPlayerCardInfo_C2S msg, int seqId) {
        if (msg.getPlayerId() <= 0) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        CardHelper.queryPlayerCardWithClanAsync(playerEntity.ownerActor(), msg.getPlayerId(),
                (pb) -> {
                    // 更新玩家好友数据
                    if (msg.getNeedUpdateFriend() && pb.getZoneId() != 0) {
                        playerEntity.ownerActor().getOrLoadFriendPlayerEntity().updateFriendData(msg.getPlayerId(), pb.getCardHead(), pb.getZoneId());
                    }
                    Player_QueryPlayerCardInfo_S2C.Builder builder = Player_QueryPlayerCardInfo_S2C.newBuilder();
                    builder.setPlayerCardInfo(pb);
                    playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_QUERYPLAYERCARDINFO_S2C, null, builder.build());
                }
        );
        return null;
    }

    /**
     * 查询玩家名片更多信息
     */
    @CommandMapping(code = MsgType.PLAYER_QUERYPLAYERCARDINFODETAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryPlayerCardInfoDetail_C2S msg, int seqId) {
        if (msg.getPlayerId() <= 0) {
            return Player_QueryPlayerCardInfoDetail_S2C.getDefaultInstance();
        }
        int zoneId = msg.getZoneId();
        PlayerActor playerActor = playerEntity.ownerActor();
        SsPlayerMisc.QueryPlayerCardInfoDetailAsk ask = SsPlayerMisc.QueryPlayerCardInfoDetailAsk.getDefaultInstance();
        IActorRef sessionRef = playerActor.sender();
        playerActor.<SsPlayerMisc.QueryPlayerCardInfoDetailAns>askPlayer(zoneId, msg.getPlayerId(), ask)
                .onComplete(
                        (ans, err) -> {
                            Player_QueryPlayerCardInfoDetail_S2C.Builder builder = Player_QueryPlayerCardInfoDetail_S2C.newBuilder();
                            if (ans != null) {
                                builder.setPlayerCardInfoDetail(ans.getDetail());
                            }
                            playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_QUERYPLAYERCARDINFODETAIL_S2C, err, builder.build());
                        });
        return null;
    }

    /**
     * 设置驻防
     */
    @CommandMapping(code = MsgType.PLAYER_SETWALLGARRISON_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SetWallGarrison_C2S msg) {
        playerEntity.getWallComponent().setHeroMechaHandler(msg);
        return Player_SetWallGarrison_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_REPAIRWALL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RepairWall_C2S msg) {
        RepairCityWallAsk.Builder call = RepairCityWallAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        playerEntity.ownerActor().callBigScene(call.build());
        return Player_RepairWall_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_OUTFIREWALL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_OutFireWall_C2S msg) {
        IntPairType cost = ResHolder.getResService(ConstKVResService.class).getTemplate().getCityWallOutfireCost();
        playerEntity.getPurseComponent().consume(CurrencyType.forNumber(cost.getKey()), cost.getValue(), CommonEnum.Reason.ICR_EXTINGUISH, "OutFireCityWallAsk");
        try {
            OutFireCityWallAsk.Builder call = OutFireCityWallAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
            playerEntity.ownerActor().callBigScene(call.build());
        } catch (GeminiException e) {
            // 回退资源
            playerEntity.getPurseComponent().give(CurrencyType.forNumber(cost.getKey()), cost.getValue(), CommonEnum.Reason.ICR_RETURN, "");
            throw e;
        }
        return Player_OutFireWall_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CITYFALL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CityFall_C2S msg) {
        SetCityFallAsk.Builder call = SetCityFallAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        SetCityFallAns ans = playerEntity.ownerActor().callBigScene(call.build());
        if (ans.hasNewPoint()) {
            new PlayerManualCityFallEvent(playerEntity, ans.getNewPoint()).dispatch();
        }
        return Player_CityFall_S2C.getDefaultInstance();
    }

    /**
     * 修改客户端相关信息
     */
    @CommandMapping(code = MsgType.PLAYER_CHANGECLIENTINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ChangeClientInfo_C2S msg) {
        CommonMsg.ClientInfo clientInfo = msg.getClientInfo();
        CommonMsg.ClientInfo.Builder playerClientInfo = playerEntity.getClientInfo().toBuilder();

        boolean isChanged = false;

        // 修改语言
        if (clientInfo.hasLanguage() && !playerClientInfo.getLanguage().equals(clientInfo.getLanguage())) {
            playerClientInfo.setLanguage(clientInfo.getLanguage());
            isChanged = true;
        }

        if (isChanged) {
            CommonMsg.ClientInfo allClientInfo = playerClientInfo.build();
            playerEntity.getSessionComponent().setClientInfo(allClientInfo, true);
        }
        return Player_ChangeClientInfo_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_FINISHGUIDANCE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FinishGuidance_C2S msg) {
        // NOTE(furson): 方法不应返回任何GeminiException，只使用日志进行记录
        if (!msg.hasGuidanceId() || !msg.hasStep()) {
            LOGGER.warn("no guidanceId or step upload when guidance finish");
            return Player_FinishGuidance_S2C.getDefaultInstance();
        }
        int guidanceId = msg.getGuidanceId();
        int stepId = msg.getStep();
        // guidanceID 或 stepId 错误
        if (guidanceId <= 0 || stepId < 0) {
            LOGGER.warn("wrong guidanceId {} stepId {} upload", guidanceId, stepId);
            return Player_FinishGuidance_S2C.getDefaultInstance();
        }
        if (playerEntity.getGuidanceComponent().isGuidanceNeedRecord(guidanceId, stepId)) {
            playerEntity.getGuidanceComponent().recordFinishedGuidance(guidanceId);
        }
        // 每次合法上报都需要打QLog
        playerEntity.getQlogComponent().sendGuidanceRecordQLog(guidanceId, stepId);
        return Player_FinishGuidance_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_MARKNEWBIESTEP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_MarkNewbieStep_C2S msg) {
        playerEntity.getNewbieComponent().markNewbieStep(msg);
        return Player_MarkNewbieStep_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_GIVENEWBIEHERO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_GiveNewbieHero_C2S msg) {
        return playerEntity.getNewbieComponent().giveNewbieHero();
    }

    @CommandMapping(code = MsgType.PLAYER_QUERYPLAYERKILLDETAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryPlayerKillDetail_C2S msg, int seqId) {
        if (msg.getPlayerId() <= 0) {
            return Player_QueryPlayerKillDetail_S2C.getDefaultInstance();
        }
        if (msg.getPlayerId() == playerEntity.getPlayerId()) {
            Map<Integer, StructPB.SingleStatisticPB> statisticMap = playerEntity.getStatisticComponent().getAllSecondStatisticPB(StatisticEnum.SOLDIER_KILL_TOTAL);
            return Player_QueryPlayerKillDetail_S2C.newBuilder().putAllSoldierKillStatistic(statisticMap).build();
        }
        int zoneId = msg.getZoneId();
        PlayerActor playerActor = playerEntity.ownerActor();
        IActorRef sessionRef = playerActor.sender();
        SsPlayerMisc.QueryPlayerKillDetailAsk ask = SsPlayerMisc.QueryPlayerKillDetailAsk.newBuilder().setPlayerId(msg.getPlayerId()).build();
        playerActor.<SsPlayerMisc.QueryPlayerKillDetailAns>askPlayer(zoneId, msg.getPlayerId(), ask)
                .onComplete(
                        (ans, err) -> {
                            Player_QueryPlayerKillDetail_S2C.Builder builder = Player_QueryPlayerKillDetail_S2C.newBuilder();
                            if (ans != null) {
                                for (Map.Entry<Integer, Struct.SingleStatistic> entry : ans.getSoldierKillStatisticMap().entrySet()) {
                                    SingleStatisticProp prop = new SingleStatisticProp();
                                    prop.mergeFromSs(entry.getValue());
                                    builder.putSoldierKillStatistic(entry.getKey(), prop.getCopyCsBuilder().build());
                                }
                            }
                            playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_QUERYPLAYERKILLDETAIL_S2C, err, builder.build());
                        });
        return null;
    }

    @CommandMapping(code = MsgType.PLAYER_TAKEDAILYFREEENERGY_C2S)
    public GeneratedMessageV3 takeDailyFreeEnergy(PlayerEntity playerEntity, Player_TakeDailyFreeEnergy_C2S msg) {
        playerEntity.getEnergyComponent().handleTakeDailyFreeEnergy();
        return Player_TakeDailyFreeEnergy_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_BUYENERGY_C2S)
    public GeneratedMessageV3 buyEnergy(PlayerEntity playerEntity, Player_BuyEnergy_C2S msg) {
        playerEntity.getEnergyComponent().handleBuyEnergy(msg);
        return Player_BuyEnergy_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_RECRUIT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_Recruit_C2S msg) {
        return playerEntity.getRecruitComponent().recruit(msg.getRecruitPoolType(), msg.getRecruitType());
    }

    @CommandMapping(code = MsgType.PLAYER_UGCCHECK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_UgcCheck_C2S msg) {
        Player_UgcCheck_S2C.Builder resp = Player_UgcCheck_S2C.newBuilder();
        String text = msg.getText();
        CommonEnum.UgcSceneId sceneId = msg.getSceneId();
        SsTextFilter.CheckTextAns ans = playerEntity.syncCheckText(text, sceneId);
        resp.setIsLegal(ans.getIsLegal())
                .setFilteredText(ans.getFilteredText());
        return resp.build();
    }

    @CommandMapping(code = MsgType.PLAYER_TRIGGERQLOG_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_TriggerQlog_C2S msg) {
        if (msg.getType() == CommonEnum.QlogType.QT_SEPARATE_DOWNLOAD) {
            QlogCncSeparateDownload.init(playerEntity.getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction(msg.getAction())
                    .sendToQlog();
        } else {
            QlogCncClientInfo.init(playerEntity.getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setType(msg.getType().getNumber())
                    .setClientInfo(msg.getAction())
                    .sendToQlog();
        }
        return Player_TriggerQlog_S2C.getDefaultInstance();
    }


    @CommandMapping(code = MsgType.PLAYER_QUERYPLAYERCARDHEAD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryPlayerCardHead_C2S msg, int seqId) {
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        CardHelper.batchQueryPlayerHead(playerEntity.ownerActor(), msg.getPlayerIdsList(),
                (map) -> {
                    Player_QueryPlayerCardHead_S2C.Builder builder = Player_QueryPlayerCardHead_S2C.newBuilder();
                    builder.putAllCardHeadList(map);
                    playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_QUERYPLAYERCARDHEAD_S2C, null, builder.build());
                }
        );
        return null;
    }

    @CommandMapping(code = MsgType.PLAYER_SETSPASSWORD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SetSPassWord_C2S msg) {
        playerEntity.getSettingComponent().setSpassword(msg.getSPassWord(), msg.getOldSpassWord());
        return Player_SetSPassWord_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CLOSESPASSWORD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CloseSPassWord_C2S msg) {
        playerEntity.getSettingComponent().closeSpassword();
        return Player_CloseSPassWord_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CANCELCLOSESPW_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CancelCloseSpw_C2S msg) {
        playerEntity.getSettingComponent().cancelCloseSpassword();
        return Player_CancelCloseSpw_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_USEEXPRESSION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_UseExpression_C2S msg) {
        playerEntity.getSettingComponent().useExpression(msg.getEntityId(), msg.getExpressionId());
        return Player_CancelCloseSpw_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SETSPASSWORDSTATUS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SetSPassWordStatus_C2S msg) {
        if (msg.getOpen()) {
            LOGGER.warn("player: {} setSpasswordStatus true is abandon", playerEntity);
            return Player_SetSPassWord_S2C.getDefaultInstance();
        }
        playerEntity.getSettingComponent().checkSpassword(SPWC_CLOSE, 0, msg.getSPassWord());
        playerEntity.getSettingComponent().closeSpasswordNoWait();
        playerEntity.getSettingComponent().setSpasswordStatus(msg.getOpen());
        return Player_SetSPassWord_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SETPFLAG_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SetPFlag_C2S msg) {
        playerEntity.getSettingComponent().setPFlag(msg.getPFlag());
        playerEntity.getSettingComponent().setPFlagStatus(msg.getShow());
        return Player_SetSPassWord_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SENDSYSASSISTMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SendSysAssistMail_C2S msg) {
        playerEntity.getPlaneComponent().consumeAllSysAssistTask();
        return Player_SendSysAssistMail_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SWITCHSETTING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SwitchSetting_C2S msg) {
        playerEntity.getSettingComponent().turnSwitch(msg.getOn(), msg.getSetting());
        return Player_SwitchSetting_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_AFTERBINDACCOUNT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_AfterBindAccount_C2S msg) {
        // 已领取
        if (playerEntity.getStatisticComponent().getSingleStatistic(StatisticEnum.FIRST_ACCOUNT_BOUND) > 0) {
            return Player_AfterBindAccount_S2C.getDefaultInstance();
        }
        // 记录
        playerEntity.getStatisticComponent().recordSingleStatistic(StatisticEnum.FIRST_ACCOUNT_BOUND, 1);
        // 发奖
        final int diamondCnt = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getFirstBindAccountAward();
        playerEntity.getPurseComponent().give(CurrencyType.DIAMOND, diamondCnt, CommonEnum.Reason.ICR_FIRST_BIND_ACCOUNT, "");
        StructPB.YoAssetPackagePB reward = AssetPackage.builder().plusCurrency(CurrencyType.DIAMOND, diamondCnt).build().toPb();
        return Player_AfterBindAccount_S2C.newBuilder().mergeReward(reward).build();
    }

    @CommandMapping(code = MsgType.PLAYER_MODIFYPUSHSETTING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ModifyPushSetting_C2S msg) {
        long playerId = playerEntity.getPlayerId();
        SsScenePlayer.SyncPlayerPushNtfInfoAsk.Builder ask = SsScenePlayer.SyncPlayerPushNtfInfoAsk.newBuilder();
        ask.setPlayerId(playerId)
                .getChangeNtfStatusInfoCmdBuilder()
                .addAllOpenSettingIdList(msg.getOpenSettingIdList())
                .addAllCloseSettingIdList(msg.getCloseSettingIdList());
        SsScenePlayer.SyncPlayerPushNtfInfoAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        final Player_ModifyPushSetting_S2C.Builder builder = Player_ModifyPushSetting_S2C.newBuilder();
        builder.addAllOpenSettingId(ans.getOpenSettingIdListList())
                .addAllCloseSettingId(ans.getCloseSettingIdListList());
        return builder.build();
    }

    @CommandMapping(code = MsgType.PLAYER_QUERYPUSHSETTING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueryPushSetting_C2S msg) {
        final SsScenePlayer.QueryPlayerPushNtfAsk.Builder ask = SsScenePlayer.QueryPlayerPushNtfAsk.newBuilder();
        ask.setPlayerId(playerEntity.getPlayerId());
        final SsScenePlayer.QueryPlayerPushNtfAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        final Player_QueryPushSetting_S2C.Builder builder = Player_QueryPushSetting_S2C.newBuilder();
        builder.addAllOpenSettingId(ans.getOpenSettingIdListList())
                .addAllCloseSettingId(ans.getCloseSettingIdListList());
        return builder.build();
    }

    /**
     * 成就分享
     */
    @CommandMapping(code = MsgType.PLAYER_SHAREACHIEVEMENT_C2S)
    public void handle(PlayerEntity playerEntity, PlayerAchievement.Player_ShareAchievement_C2S msg, int seqId) {
        final CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
        playerEntity.getAchievementComponent().fillAchievementShareData(msg.getAchievementId(), messageData);
        CommonEnum.MessageType messageType = CommonEnum.MessageType.MT_ACHIEVEMENT_SHARE;
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        IActorRef session = playerEntity.ownerActor().sender();
        chatPlayerEntity.getHandleChatComponent().chatRequest(msg.getChatSession(), messageType, messageData.build(),
                (e) -> playerEntity.answerMsgToClient(session, seqId,
                        MsgType.PLAYER_SHAREACHIEVEMENT_S2C,
                        e,
                        PlayerAchievement.Player_ShareAchievement_S2C.getDefaultInstance()
                ));
    }
    
    @CommandMapping(code = MsgType.PLAYER_FETCHACHIEVEMENT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAchievement.Player_FetchAchievement_C2S msg) {
        PlayerAchievement.Player_FetchAchievement_S2C.Builder rsp = PlayerAchievement.Player_FetchAchievement_S2C.newBuilder();
        switch (msg.getAchievementType()) {
            case ACHT_PLAYER:
                playerEntity.getAchievementComponent().fillFetchInfo(rsp);
                return rsp.build();
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unsupport AchievementType");
        }
    }

    @CommandMapping(code = MsgType.PLAYER_TAKEACHIEVEMENTREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAchievement.Player_TakeAchievementReward_C2S msg) {
        PlayerAchievement.Player_TakeAchievementReward_S2C.Builder rsp = PlayerAchievement.Player_TakeAchievementReward_S2C.newBuilder();
        switch (msg.getType()) {
            case ACHT_PLAYER:
                if (!playerEntity.getAchievementComponent().isAchievementCompleteNotReward(msg.getAchievementId())) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "not complete or repear take reward");
                }
                final Struct.YoAssetPackage yoAssetPackage = playerEntity.getAchievementComponent().takeReward(msg.getAchievementId());
                AssetPackage.Builder assetPackage = AssetPackage.builder();
                for (Struct.YoAssetDesc desc : yoAssetPackage.getAssets().getDatasList()) {
                    // 目前只支持道具奖励，配表有做校验
                    final ItemDesc itemDesc = new ItemDesc(desc.getId(), desc.getAmount());
                    assetPackage.plus(itemDesc);
                    rsp.getRewardBuilder().getAssetsBuilder().addDatas(itemDesc.toPb());
                }
                playerEntity.getAssetComponent().give(assetPackage.build(), ICR_ACHIEVEMENT);
                return rsp.build();
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unsupport AchievementType");
        }
    }
}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

/**
 * 永久解锁建造第二队列
 */
public class SecondBuildQueueForeverUnlockItem extends AbstractUsableItem {

    public SecondBuildQueueForeverUnlockItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {

    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        playerEntity.getPlayerQueueTaskComponent().unlockSecondBuildQueueForever();
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {

    }
}

package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.city.CitySkinAction;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.city.CitySkinService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CityDressProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.DressResTemplate;

import java.util.Iterator;
import java.util.List;
import java.util.Map;


public class PlayerCityDressComponent extends PlayerComponent implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCityDressComponent.class);

    public PlayerCityDressComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            // 设置默认皮肤
            setDefaultSkin();
        }
    }

    @Override
    public void onLogin() {
        clearExpiredCityDress();
    }

    private void setDefaultSkin() {
        // 含有默认永久皮肤
        this.setCityDressProp(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDefaultBaseDress(), 0);
        this.getOwner().getProp().getCityModel().setUsingSkin(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDefaultBaseDress());
    }

    /**
     * 清除过期基地皮肤
     */
    private void clearExpiredCityDress() {
        Iterator<Integer> it = this.getOwner().getProp().getCityModel().getUnlockedSkins().keySetIterator();
        while (it.hasNext()) {
            Integer key = it.next();
            CityDressProp cityDressProp = this.getOwner().getProp().getCityModel().getUnlockedSkins().get(key);
            long expireTsMs = cityDressProp.getTimeoutTsMs();
            // 临时 & 已过期
            if (!this.isEternalCityDressTimeoutTsMs(expireTsMs) && expireTsMs < SystemClock.now()) {
                it.remove();
                LOGGER.info("{} clear expire cityDress {}", getOwner().ownerActor().getPlayerId(), cityDressProp);
                this.sendCityDressExpireQLog(key, expireTsMs);
                // 过期后切换为默认皮肤，补发qlog
                if (key == this.getOwner().getProp().getCityModel().getUsingSkin()) {
                    this.sendUseCityDressQLog(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDefaultBaseDress(), expireTsMs);
                    this.getOwner().getProp().getCityModel().setUsingSkin(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDefaultBaseDress());
                    // 更新card
                    getOwner().getPlayerPropComponent().updatePlayerCardCache(false);
                }

            }
        }
    }


    /**
     * 更换主城皮肤
     *
     * @param cityDressTemplateId 主城皮肤配置id
     */
    public void changeCityDress(int cityDressTemplateId) {
        checkIsCityDress(cityDressTemplateId);

        checkHasValidCityDress(cityDressTemplateId);

        SsScenePlayer.ChangeCityDressAsk.Builder builder = SsScenePlayer.ChangeCityDressAsk.newBuilder();
        builder.setDressTemplateId(cityDressTemplateId)
                .setPlayerId(this.getPlayerId())
                .setDressTimeoutTsMs(this.getCityDressTimeoutTs(cityDressTemplateId));
        // 无返回值，但可能有exception
        this.getOwner().ownerActor().callBigScene(builder.build());

        // 清除原皮肤加成
        int usingSkin = this.getOwner().getProp().getCityModel().getUsingSkin();
        if (usingSkin > 0) {
            this.getOwner().getAddComponent().removeAdditionBySource(CommonEnum.AdditionSourceType.AST_CITY_DRESS);
        }
        this.sendUseCityDressQLog(cityDressTemplateId, SystemClock.now());
        this.getOwner().getProp().getCityModel().setUsingSkin(cityDressTemplateId);
        // 添加当前皮肤加成
        List<Integer> additionIds = Lists.newArrayList(this.getDressAdditon(cityDressTemplateId).keySet());
        this.getOwner().getAddComponent().updateAddition(AdditionProviderType.CITY_DRESS, additionIds);
        // 更新card
        getOwner().getPlayerPropComponent().updatePlayerCardCache(false);
        LOGGER.info("changeCityDress player: {}, curCityDresss={}", getOwner(), cityDressTemplateId);
    }

    /**
     * 解锁基地皮肤或延续有效时间
     *
     * @param citySubDressTemplateId 主城皮肤配置子id
     */
    public void extendOrUnlockCityDress(int citySubDressTemplateId) {
        int cityDressTemplateId = getDressId(citySubDressTemplateId);
        // 预防：已有永久
        if (hasEternalCityDress(cityDressTemplateId)) {
            return;
        }
        // 原过期时间，<0 未拥有过，==0 永久拥有， >0 过期时间
        long oldTimeoutTsMs = this.getCityDressTimeoutTs(cityDressTemplateId);
        // 在线时过期
        if (oldTimeoutTsMs > 0 && oldTimeoutTsMs < SystemClock.now()) {
            this.sendCityDressExpireQLog(cityDressTemplateId, oldTimeoutTsMs);
        }
        long continueTsMs = this.getContinueTimeMs(citySubDressTemplateId);
        // 永久设0， 否则计算到期时间
        long newTimeoutTsMs = this.isEternalCityDressTimeoutTsMs(continueTsMs) ? 0 : (Math.max(oldTimeoutTsMs, SystemClock.now()) + continueTsMs);
        this.setCityDressProp(cityDressTemplateId, newTimeoutTsMs);
        // 解锁皮肤（不区分是否首次）
        if (oldTimeoutTsMs < SystemClock.now()) {
            this.sendUnlockCityDressQLog(cityDressTemplateId);
        }
        LOGGER.info("extendOrUnlockCityDress player: {}, curCityDresss={}, curTimeOutTsMs={}", getOwner(), cityDressTemplateId, newTimeoutTsMs);

    }

    /**
     * 是否拥有永久基地皮肤
     *
     * @param cityDressTemplateId 基地皮肤配置id
     * @return true 已拥有
     */
    public boolean hasEternalCityDress(int cityDressTemplateId) {
        return this.isEternalCityDressTimeoutTsMs(this.getCityDressTimeoutTs(cityDressTemplateId));
    }


    /**
     * 检测是否拥有未过期的基地皮肤
     *
     * @param cityDressTemplateId 基地皮肤配置id
     */
    private void checkHasValidCityDress(int cityDressTemplateId) {
        long timeoutTsMs = this.getCityDressTimeoutTs(cityDressTemplateId);
        if (timeoutTsMs < 0) {
            throw new GeminiException(ErrorCode.DRESS_NOT_OWNED);
        }
        // 过期时间为0表示永久拥有
        if (!isEternalCityDressTimeoutTsMs(timeoutTsMs) && timeoutTsMs <= SystemClock.now()) {
            throw new GeminiException(ErrorCode.DRESS_NOT_OWNED);
        }
    }

    /**
     * 校验是否是基地装扮
     *
     * @param cityDressTemplateId 外观ID
     */
    private void checkIsCityDress(int cityDressTemplateId) {
        if (ResHolder.getInstance().getValueFromMap(DressResTemplate.class, cityDressTemplateId).getDressType() != CommonEnum.DressType.DRESS_CITY.getNumber()) {
            throw new GeminiException(ErrorCode.DRESS_TYPE_INVALID);
        }
    }

    /**
     * 是否是永久基地皮肤
     *
     * @param timeoutTsMs 过期时间
     * @return true 永久皮肤
     */
    private boolean isEternalCityDressTimeoutTsMs(long timeoutTsMs) {
        return timeoutTsMs == 0;
    }

    /**
     * 获取基地皮肤过期时间
     *
     * @param cityDressTemplateId 基地皮肤配置id
     * @return long <0 未拥有， ==0 永久， >0 过期时间戳
     */
    private long getCityDressTimeoutTs(int cityDressTemplateId) {
        CityDressProp dressProp = this.getOwner().getProp().getCityModel().getUnlockedSkins().get(cityDressTemplateId);
        if (dressProp == null) {
            return -1024;
        }
        return dressProp.getTimeoutTsMs();
    }

    /**
     * 设置已有主堡装扮（当前数据简单，全量覆盖）
     *
     * @param cityDressTemplateId 装扮id
     * @param timeoutTsMs         过期时间
     */
    private void setCityDressProp(int cityDressTemplateId, long timeoutTsMs) {
        this.getOwner().getProp().getCityModel().getUnlockedSkins().put(cityDressTemplateId,
                new CityDressProp()
                        .setDressTemplateId(cityDressTemplateId)
                        .setTimeoutTsMs(timeoutTsMs));

    }


    /**
     * 获取外观ID
     *
     * @param dressSubTemplateId 外观子ID
     * @return 外观ID
     */
    private int getDressId(int dressSubTemplateId) {
        return ResHolder.getResService(CitySkinService.class).getDressId(dressSubTemplateId);
    }

    /**
     * 获取持续时间ms
     *
     * @param dressSubTemplateId 外观子ID
     * @return 持续时间
     */
    private long getContinueTimeMs(int dressSubTemplateId) {
        int coutineTimeS = ResHolder.getResService(CitySkinService.class).getContinueTime(dressSubTemplateId);
        return TimeUtils.second2Ms(coutineTimeS);
    }

    /**
     * 获取装扮加成
     *
     * @param dressTemplateId 装扮id
     * @return 装扮加成
     */
    private Map<Integer, Long> getDressAdditon(int dressTemplateId) {
        return ResHolder.getResService(CitySkinService.class).getUseAttribute(dressTemplateId);
    }

    /**
     * 发送切换基地皮肤QLog
     *
     * @param dressTemplateId 基地皮肤配表id
     */
    private void sendUseCityDressQLog(int dressTemplateId, long useTsMs) {
        getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.USE_SKIN, dressTemplateId, CommonEnum.DressType.DRESS_CITY, useTsMs);
    }

    /**
     * 发送基地皮肤过期QLog
     *
     * @param dressTemplateId 基地皮肤配表id
     * @param expireTsMs      过期时间
     */
    private void sendCityDressExpireQLog(int dressTemplateId, long expireTsMs) {
        getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.SKIN_EXPIRE, dressTemplateId, CommonEnum.DressType.DRESS_CITY, expireTsMs);
    }

    /**
     * 发送解锁基地皮肤QLog
     *
     * @param dressTemplateId 基地皮肤配表id
     */
    private void sendUnlockCityDressQLog(int dressTemplateId) {
        getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.UNLOCK_SKIN, dressTemplateId, CommonEnum.DressType.DRESS_CITY, SystemClock.now());
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.CITY_DRESS;
    }

    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        long value = 0;
        int usingSkin = getOwner().getProp().getCityModel().getUsingSkin();
        if (usingSkin > 0) {
            value = this.getDressAdditon(usingSkin).getOrDefault(additionId, 0L);
        }
        Map<CommonEnum.AdditionSourceType, Long> res = Maps.newHashMap();
        res.put(CommonEnum.AdditionSourceType.AST_CITY_DRESS, value);
        return res;
    }
}

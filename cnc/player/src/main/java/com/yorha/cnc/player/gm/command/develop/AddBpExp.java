package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 加bp exp
 *
 * <AUTHOR>
 */
public class AddBpExp implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int count = Integer.parseInt(args.getOrDefault("count", "0"));
        actor.getEntity().getBattlePassComponent().addExp(count, "Gm", 0);
    }

    @Override
    public String showHelp() {
        return "AddBpExp count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

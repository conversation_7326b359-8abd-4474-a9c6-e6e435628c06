package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.enums.TaskCheckType;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.task.checker.TaskChecker;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncTaskFlow;
import res.template.TaskPoolTemplate;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 任务处理类基类
 * 提供关注列表功能
 *
 * <AUTHOR>
 */
public abstract class AbstractTaskHandler implements TaskHandler {
    private static final Logger LOGGER = LogManager.getLogger(AbstractTaskHandler.class);

    public AbstractTaskHandler(PlayerEntity entity, String typeClass) {
        this.entity = entity;
        this.typeClass = typeClass;
    }

    protected final PlayerEntity entity;
    protected final String typeClass;

    public String getTypeClass() {
        return typeClass;
    }

    public PlayerEntity getEntity() {
        return entity;
    }

    /**
     * 关注事件
     * Map<事件类型, List<taskConfigId>>
     * 关注列表根据玩家load进行载入，根据玩家内存数据自动淘汰进行剔除
     */
    public final Map<String, List<Integer>> attentionEventMap = Maps.newHashMap();

    public boolean openAttention = false;

    public abstract Int32TaskInfoMapProp getTaskProp();

    abstract int getTaskIdById(int configId);

    @Override
    public abstract void loadAllTask();

    @Override
    public void addBatchTask(Set<Integer> configIds) {
        LOGGER.info("task create. type:{} configIds:{}", typeClass, configIds);
        Int32TaskInfoMapProp taskPropMap = getTaskProp();
        for (int configId : Lists.newArrayList(configIds)) {
            TaskInfoProp taskInfoProp = taskPropMap.get(configId);
            if (taskInfoProp != null) {
                LOGGER.error("id is existed. taskInfoProp:{}, id:{}", taskInfoProp, configId);
                continue;
            }
            taskInfoProp = createTask(configId);
            addTaskEventMonitor(taskInfoProp);
        }
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum gmType) {
        LOGGER.info("task finish. type:{} configId:{}", typeClass, configId);
        // 移除监听事件
        removeAttention(configId);

        Int32TaskInfoMapProp taskMapProp = getTaskProp();
        TaskInfoProp taskInfoProp = getTaskInfoProp(taskMapProp, configId);
        if (!isAcceptedState(taskInfoProp)) {
            return;
        }
        setNextTaskState(taskInfoProp);
        finishTaskQLog(taskInfoProp);
    }


    @Override
    public void removeAttention(int id) {
        TaskChecker checker = getCheckerByTaskId(getTaskIdById(id));
        for (String eventName : checker.getAttentionEvent()) {
            List<Integer> attentionTaskIdList = attentionEventMap.get(eventName);
            if (attentionTaskIdList == null) {
                continue;
            }
            Iterator<Integer> iterator = attentionTaskIdList.iterator();
            while (iterator.hasNext()) {
                Integer attentionTaskId = iterator.next();
                if (attentionTaskId == id) {
                    iterator.remove();
                    break;
                }
            }
        }

    }

    @Override
    public void openAttention() {
        openAttention = true;
    }

    @Override
    public void updateProcess(Integer configId, PlayerTaskEvent event) {
        TaskInfoProp taskInfoProp = getTaskInfoProp(getTaskProp(), configId);
        if (isCompleted(taskInfoProp)) {
            LOGGER.error("updateProcess fail, task isCompleted, task={}, handler={} monitor={}", configId, getTypeClass(), takeAllTaskConfigIdWithEvent(event.getName()));
            return;
        }
        TaskPoolTemplate taskTemplate = getTaskTemplate(configId);
        CommonEnum.TaskType taskType = CommonEnum.TaskType.forNumber(taskTemplate.getTaskType().getNumber());
        TaskChecker checker = TaskCheckType.forNumber(taskType).getChecker();
        boolean taskFinished = checker.updateProcess(event, taskInfoProp, taskTemplate);
        if (taskFinished) {
            onTaskFinish(configId, TaskGmEnum.NO_GM);
        }
    }

    @Override
    public abstract List<IntPairType> checkOrTakeReward(List<Integer> taskId);

    @Override
    public void onInnerCityUpgrade(BuildFinEvent event) {
    }

    @Override
    public void onJoinClan(PlayerJoinClanEvent event) {
    }

    @Override
    public void clearAllAttention(String reason) {
        openAttention = false;
        removeAllAttention(reason);
    }

    @Override
    public void onNewbieOver() {
    }

    public TaskInfoProp createTask(int id) {
        TaskInfoProp taskInfoProp = buildTaskProp(id);

        Int32TaskInfoMapProp taskMap = getTaskProp();
        taskMap.put(id, taskInfoProp);
        LOGGER.debug("create task prop. taskHandler:{} taskProp:{}, totalTaskNum:{}", typeClass, taskInfoProp, taskMap.size());
        return taskInfoProp;
    }

    private TaskInfoProp buildTaskProp(int id) {
        TaskInfoProp taskInfoProp = new TaskInfoProp();
        taskInfoProp.setId(id);

        // 变更任务状态为可接取
        setNextTaskState(taskInfoProp);
        // 变更任务状态为已经接取
        setNextTaskState(taskInfoProp);
        return taskInfoProp;
    }

    public TaskChecker getCheckerByTaskId(int taskId) {
        TaskPoolTemplate taskTemplate = ResHolder.getTemplate(TaskPoolTemplate.class, taskId);
        CommonEnum.TaskType taskType = CommonEnum.TaskType.forNumber(taskTemplate.getTaskType().getNumber());
        return TaskCheckType.forNumber(taskType).getChecker();
    }

    /**
     * 添加任务到关注列表
     */
    protected void addAttentionList(List<String> eventList, int id) {
        if (!openAttention) {
            return;
        }
        for (String event : eventList) {
            List<Integer> idList = attentionEventMap.computeIfAbsent(event, (T) -> Lists.newArrayList());
            idList.add(id);
        }
        LOGGER.debug("addAttention.handler:{} event:{}", ClassNameCacheUtils.getSimpleName(this.getClass()), eventList);
    }

    /**
     * 根据事件获取关注的所有任务
     */
    public List<Integer> takeAllTaskConfigIdWithEvent(String event) {
        return attentionEventMap.getOrDefault(event, Lists.newArrayList());
    }

    /**
     * 检测并添加任务事件监听
     */
    protected void addTaskEventMonitor(TaskInfoProp taskInfoProp) {
        if (!openAttention) {
            return;
        }
        int configId = taskInfoProp.getId();
        if (isUnCompleted(taskInfoProp)) {
            TaskChecker checker = getCheckerByConfigId(configId);
            if (checker.getAttentionEvent().contains(CheckTaskProcessEvent.class.getSimpleName())) {
                try {
                    // 关注了检测任务进度事件的任务类型需要创建的时候手动触发下
                    boolean taskFinished = checker.updateProcess(new CheckTaskProcessEvent(getEntity()), taskInfoProp, getTaskTemplate(configId));
                    if (taskFinished) {
                        onTaskFinish(configId, TaskGmEnum.NO_GM);
                    }
                } catch (Exception e) {
                    // 防止阻断注册或者登录，这里捕捉异常
                    WechatLog.error("CheckTaskProcessEvent error.", e);
                }
            }
            if (isUnCompleted(taskInfoProp)) {
                // 未完成就添加关注列表(未完成且确实没完成的任务)
                addAttentionList(checker.getAttentionEvent(), configId);
            }
        }
    }

    protected TaskChecker getCheckerByConfigId(int configId) {
        int taskId = getTaskIdById(configId);
        TaskCheckType taskType = getTaskTypeWithTaskId(taskId);
        return taskType.getChecker();
    }

    protected TaskCheckType getTaskTypeWithTaskId(int taskId) {
        TaskPoolTemplate taskPoolTemplate = ResHolder.getInstance().findValueFromMap(TaskPoolTemplate.class, taskId);
        if (taskPoolTemplate == null) {
            throw new GeminiException("find taskPoolTemplate failed. taskId={}", taskId);
        }
        CommonEnum.TaskType newTaskType = CommonEnum.TaskType.forNumber(taskPoolTemplate.getTaskType().getNumber());
        if (newTaskType == null || newTaskType == CommonEnum.TaskType.TT_TASK_NONE) {
            throw new GeminiException("checkOrMonitorTask error taskId, taskId:{}", taskId);
        }
        return TaskCheckType.forNumber(newTaskType);
    }

    protected void removeAllAttention(String reason) {
        LOGGER.info("clear attention event. reason:{} handler={}", reason, getTypeClass());
        attentionEventMap.clear();
    }

    public static TaskInfoProp getTaskInfoProp(Int32TaskInfoMapProp map, Integer taskId) {
        TaskInfoProp taskInfoProp = map.get(taskId);
        if (taskInfoProp == null) {
            throw new GeminiException(ErrorCode.BUILD_TASKID_PARAM_ERROR);
        }
        return taskInfoProp;
    }

    /**
     * 任务已接取
     */
    public static boolean isAccepted(TaskInfoProp taskInfoProp) {
        if (taskInfoProp == null) {
            return false;
        }
        return switch (taskInfoProp.getStatus()) {
            case AT_NOT_REWARD, AT_ACCEPT -> true;
            default -> false;
        };
    }

    /**
     * 任务已完成
     */
    public static boolean isCompleted(TaskInfoProp taskInfoProp) {
        if (taskInfoProp == null) {
            return false;
        }
        return switch (taskInfoProp.getStatus()) {
            case AT_REWARD, AT_NOT_REWARD -> true;
            default -> false;
        };
    }

    /**
     * 任务未完成
     */
    public static boolean isUnCompleted(TaskInfoProp taskInfoProp) {
        return !isCompleted(taskInfoProp);
    }

    /**
     * 任务是已完成状态
     */
    public static boolean isFinishState(TaskInfoProp taskInfoProp) {
        if (taskInfoProp == null) {
            return false;
        }
        return taskInfoProp.getStatus() == CommonEnum.TaskStatus.AT_REWARD;
    }


    /**
     * 任务是已完成未领奖状态
     */
    public static boolean isUnCompletedState(TaskInfoProp taskInfoProp) {
        if (taskInfoProp == null) {
            return true;
        }
        return taskInfoProp.getStatus() != CommonEnum.TaskStatus.AT_NOT_REWARD;
    }

    /**
     * 任务是已接取状态
     */
    public static boolean isAcceptedState(TaskInfoProp taskInfoProp) {
        if (taskInfoProp == null) {
            return false;
        }
        return taskInfoProp.getStatus() == CommonEnum.TaskStatus.AT_ACCEPT;
    }

    public static void setNextTaskState(TaskInfoProp taskInfoProp) {
        CommonEnum.TaskStatus oldStatus = taskInfoProp.getStatus();
        long now = SystemClock.now();
        taskInfoProp.setLastUpdateMs(now);
        if (oldStatus == null) {
            taskInfoProp.setStatus(CommonEnum.TaskStatus.AT_NOT_ACCEPT);
            return;
        }
        switch (oldStatus) {
            case AT_NOT_ACCEPT: {
                taskInfoProp.setStatus(CommonEnum.TaskStatus.AT_CAN_ACCEPT);
                break;
            }
            case AT_CAN_ACCEPT: {
                taskInfoProp.setStatus(CommonEnum.TaskStatus.AT_ACCEPT);
                break;
            }
            case AT_ACCEPT: {
                taskInfoProp.setStatus(CommonEnum.TaskStatus.AT_NOT_REWARD);
                break;
            }
            case AT_NOT_REWARD: {
                taskInfoProp.setStatus(CommonEnum.TaskStatus.AT_REWARD);
                break;
            }
            default: {
                throw new GeminiException("unknown task status. status:{}", oldStatus);
            }
        }
        LOGGER.debug("task status update. taskProp:{}, oldStatus:{}", taskInfoProp, oldStatus);
    }

    /**
     * 完成任务QLog
     */
    public void finishTaskQLog(TaskInfoProp taskInfoProp) {
        QlogCncTaskFlow flow = QlogCncTaskFlow.init(getEntity().getQlogComponent());
        flow.setTaskClass(typeClass)
                .setDtEventTime(TimeUtils.now2String())
                .setITaskID(taskInfoProp.getId())
                .setAction("complete_task")
                .sendToQlog();
    }

    /**
     * 获取奖励QLog
     */
    public void takeRewardQLog(TaskInfoProp taskInfoProp) {
        QlogCncTaskFlow flow = QlogCncTaskFlow.init(getEntity().getQlogComponent());
        flow.setTaskClass(typeClass)
                .setDtEventTime(TimeUtils.now2String())
                .setITaskID(taskInfoProp.getId())
                .setAction("collect_task")
                .sendToQlog();
    }

    public String formationSubReason(int configId) {
        return typeClass + "_" + configId;
    }

    /**
     * 填满内存，GM使用
     */
    public void fullMemoryByGm() {
    }

    /**
     * GM完成所有任务
     *
     * @param id 指定id
     */
    public void completeTasksWithGm(int id) {
        for (TaskInfoProp taskProp : Lists.newArrayList(getTaskProp().values())) {
            if (id != 0 && taskProp.getId() != id) {
                continue;
            }
            if (isUnCompleted(taskProp)) {
                onTaskFinish(taskProp.getId(), TaskGmEnum.FINISH_GM);
            }
        }
    }

    public void addProcessByGm(int id, int value) {
        TaskInfoProp taskInfoProp = getTaskInfoProp(getTaskProp(), id);
        if (isCompleted(taskInfoProp)) {
            return;
        }
        taskInfoProp.setProcess(taskInfoProp.getProcess() + value);
        // GM重新检测一下完成
        updateProcess(id, new CheckTaskProcessEvent(getEntity()));
    }

}

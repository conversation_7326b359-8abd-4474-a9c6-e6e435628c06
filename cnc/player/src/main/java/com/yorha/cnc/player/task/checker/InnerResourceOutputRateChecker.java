package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.enums.BuildResourceMapType;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.InnerResourceOutputRateChangeEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 某种资源类型产量达到多少
 * param1: 资源类型
 * param2: 产量
 *
 * <AUTHOR>
 */
public class InnerResourceOutputRateChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            InnerResourceOutputRateChangeEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.get(0);
        int param2 = taskParams.get(1);

        BuildResourceMapType type = BuildResourceMapType.getBuildResourceMapTypeByCurrencyType(CommonEnum.CurrencyType.forNumber(param1));
        if (type != null) {
            int outputRate = (int) PlayerAddCalc.getProduceRatePerHour(event.getPlayer(), type, false);
            prop.setProcess(Math.min(outputRate, param2));
        }

        return prop.getProcess() >= param2;
    }
}

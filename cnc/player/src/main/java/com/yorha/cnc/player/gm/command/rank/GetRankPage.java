package com.yorha.cnc.player.gm.command.rank;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.helper.GmHelper;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsRank.GetRankPageInfoAns;

import java.util.Map;

/**
 * 分页获取
 *
 * <AUTHOR>
 */
public class GetRankPage implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int rankId = Integer.parseInt(args.get("type"));
        int page = Integer.parseInt(args.get("page"));
        GetRankPageInfoAns answer = actor.getEntity().getPlayerRankComponent().getRankInfoList(rankId, page);
        GmHelper.sendGmNtfMail(actor.getZoneId(), playerId, "GetRankPage", answer.toString());
    }

    @Override
    public String showHelp() {
        return "GetRankPage type={value} page={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_RANK;
    }
}

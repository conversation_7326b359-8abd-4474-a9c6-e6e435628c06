package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerInnerCollectResourceEvent;
import com.yorha.cnc.player.event.task.ResBuildingCollectResourceEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * 活动采集触发器
 *
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_COLLECT)
public class ActCollectTrigger extends AbstractActivityTrigger {
    private static final Logger LOGGER = LogManager.getLogger(ActCollectTrigger.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerInnerCollectResourceEvent.class,
            ResBuildingCollectResourceEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        boolean result = false;
        final long oldProcess = triggerInfoProp.getProcess();
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        long newProcess = oldProcess;
        if (event instanceof PlayerInnerCollectResourceEvent) {
            PlayerInnerCollectResourceEvent innerCollectResourceEvent = (PlayerInnerCollectResourceEvent) event;
            final Map<CommonEnum.CurrencyType, Long> resources = innerCollectResourceEvent.getResource();
            for (long resourceNum : resources.values()) {
                newProcess += resourceNum;
            }
        }

        if (event instanceof ResBuildingCollectResourceEvent) {
            ResBuildingCollectResourceEvent collectResourceEvent = (ResBuildingCollectResourceEvent) event;
            final Map<Integer, Long> resources = collectResourceEvent.getCollectResMap();
            for (long resourceNum : resources.values()) {
                newProcess += resourceNum;
            }
        }
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        if (params == null) {
            return false;
        }
        final int param = params.get(CommonEnum.ActTriggerParamType.ATPT_CURRENCY_NUM);
        if (newProcess >= param) {
            newProcess = newProcess - param;
            if (newProcess < 0) {
                newProcess = 0;
            }
            result = true;
        }
        if (result) {
            triggerInfoProp.setTriggerTime(triggerInfoProp.getTriggerTime() + 1);
            LOGGER.info("ActCollectTrigger onTrigger, triggerTime={}", triggerInfoProp.getTriggerTime());
        }
        triggerInfoProp.setProcess(newProcess);
        LOGGER.info("ActCollectTrigger onTrigger event {} triggerId {} isSatisfied {} process {}->{}", event, triggerId, result, oldProcess, newProcess);
        return result;
    }
}

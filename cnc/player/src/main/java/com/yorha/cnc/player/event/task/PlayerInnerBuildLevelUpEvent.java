package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 内城建筑升级（创建）事件
 *
 * <AUTHOR>
 */
public class PlayerInnerBuildLevelUpEvent extends PlayerTaskEvent {
    private final int buildType;
    private final int levelUpNum;
    private final int maxLevelBefore;
    private final int maxLevelAfter;

    public PlayerInnerBuildLevelUpEvent(PlayerEntity player, int buildType, int levelUpNum, int maxLevelBefore, int maxLevelAfter) {
        super(player);
        this.buildType = buildType;
        this.levelUpNum = levelUpNum;
        this.maxLevelBefore = maxLevelBefore;
        this.maxLevelAfter = maxLevelAfter;
    }

    public int getBuildType() {
        return buildType;
    }

    public int getLevelUpNum() {
        return levelUpNum;
    }

    public int getMaxLevelBefore() {
        return maxLevelBefore;
    }

    public int getMaxLevelAfter() {
        return maxLevelAfter;
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerGetClanGiftEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 联盟礼物领取次数
 *
 * <AUTHOR>
 */
public class ClanGiftChecker extends AbstractTaskChecker {
    // 仅接取，且仅读取事件内信息，故不关注CheckTaskProcessEvent
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerGetClanGiftEvent.class)
    );
    private static final Logger LOGGER = LogManager.getLogger(ClanGiftChecker.class);

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        int countConfig = taskParams.get(0);
        if (taskTemplate.getTaskCalculationMethod() != CommonEnum.TaskCalcType.TCT_RECEIVE) {
            LOGGER.error("not support task calc type. template:{}", taskTemplate);
            return prop.getProcess() >= countConfig;
        }
        if (event instanceof PlayerGetClanGiftEvent) {
            PlayerGetClanGiftEvent playerGetClanGiftEvent = (PlayerGetClanGiftEvent) event;
            prop.setProcess(Math.min(countConfig, prop.getProcess() + playerGetClanGiftEvent.getTimes()));
        }
        return prop.getProcess() >= countConfig;
    }
}

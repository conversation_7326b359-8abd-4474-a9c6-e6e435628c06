package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.pay.PayType;
import com.yorha.common.midas.MidasConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.SsPlayerPayment;
import res.template.ChargeGoodsTemplate;
import res.template.ChargeSdkTemplate;

import java.util.Map;

/**
 * 直充没法模拟的，这里只能直购
 */
public class Recharge implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        final int goodsId = Integer.parseInt(args.get("goodsId"));
        PlayerEntity player = actor.getEntity();
        ChargeGoodsTemplate goodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, goodsId);
        PlayerPayment.Player_ApplyGoodsOrder_C2S apply = PlayerPayment.Player_ApplyGoodsOrder_C2S.newBuilder().setGoodsId(goodsId).build();
        PlayerPayment.Player_ApplyGoodsOrder_S2C goodsOrderResp = player.getPaymentComponent().handleApplyGoodsOrder(apply);

        if (MidasConfig.fakeMidas()) {
            // fake_midas模式下在applyOrder的时候就自己发货了
            int chargeSdkId = goodsTemplate.getChargeSdkId();
            String midasProductId = ResHolder.getTemplate(ChargeSdkTemplate.class, chargeSdkId).getMidasProductId();
            player.getPaymentComponent().handleMidasCallback(SsPlayerPayment.MidasCallbackAsk.newBuilder()
                    .setAppMeta(goodsOrderResp.getOrderToken())
                    .setProductId(midasProductId)
                    .build(), false, PayType.PAY_BY_MONEY);
        }
    }

    @Override
    public String showHelp() {
        return "Recharge goodsId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }
}

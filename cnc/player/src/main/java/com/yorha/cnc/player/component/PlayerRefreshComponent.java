package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerWeekRefreshEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.RefreshInfoProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * 通用日刷周刷控制器
 *
 * <AUTHOR>
 */
public class PlayerRefreshComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerRefreshComponent.class);
    private ActorTimer dayTask;
    private ActorTimer weekTask;

    public PlayerRefreshComponent(PlayerEntity owner) {
        super(owner);
    }

    public RefreshInfoProp getProp() {
        return getOwner().getProp().getRefreshSys();
    }

    @Override
    public void onLogin() {
        if (TimeUtils.isSameDayWithNow(getProp().getDayRefreshTsMs())) {
            addDayRefreshTimer();
        } else {
            dispatchDayRefresh();
        }
        if (TimeUtils.isSameWeekWithNow(getProp().getWeekRefreshTsMs())) {
            addWeekRefreshTimer();
        } else {
            dispatchWeekRefresh();
        }
    }

    /**
     * 玩家登出后
     */
    @Override
    public void afterLogout() {
        if (dayTask != null) {
            dayTask.cancel();
            dayTask = null;
        }
        if (weekTask != null) {
            weekTask.cancel();
            weekTask = null;
        }
    }

    /**
     * 获取下日刷新时间戳（0为出错）
     *
     * @return long
     */
    public long getNextDayRefreshTs() {
        return dayTask == null ? 0 : dayTask.getNextExecuteTime();
    }

    /**
     * 获取下周刷新时间戳（0为出错）
     *
     * @return long
     */
    public long getNextWeekRefreshTs() {
        return weekTask == null ? 0 : weekTask.getNextExecuteTime();
    }

    private void addDayRefreshTimer() {
        if (dayTask != null) {
            // 上次登录过程 或 登出过程 出问题了 导致timer加了没取消
            LOGGER.error("{} addDayRefreshTimer repeat", getOwner());
            return;
        }
        long initialDelay = TimeUtils.getNextDayDurMs(SystemClock.now()) + RandomUtils.nextInt(5000);
        dayTask = ownerActor().addTimer(
                TimerReasonType.PLAYER_DAY_REFRESH,
                this::dispatchDayRefresh,
                initialDelay, TimeUnit.MILLISECONDS);

        // 更新下次日刷时间戳
        getProp().setNextDayRefreshTsMs(SystemClock.now() + initialDelay);
    }

    private void addWeekRefreshTimer() {
        if (weekTask != null) {
            // 上次登录过程 或 登出过程 出问题了 导致timer加了没取消
            LOGGER.error("{} addWeekRefreshTimer repeat", getOwner());
            return;
        }
        long initialDelay = TimeUtils.getNextWeekDurMs(SystemClock.now()) + RandomUtils.nextInt(5000);
        weekTask = ownerActor().addTimer(
                TimerReasonType.PLAYER_WEEK_REFRESH,
                this::dispatchWeekRefresh,
                initialDelay, TimeUnit.MILLISECONDS);

        // 更新下次周刷时间戳
        getProp().setNextWeekRefreshTsMs(SystemClock.now() + initialDelay);
    }

    private void dispatchDayRefresh() {
        dayTask = null;
        getProp().setDayRefreshTsMs(SystemClock.now());
        addDayRefreshTimer();

        // 这里不走event触发的原因是，累计登录任务需要等统计项更新了才能拿到正确的累计登录天数
        // 如果累计登录任务改成监听StatisticUpdateEvent这种形式，这里也可以改成event监听触发
        if (getOwner().isOnline()) {
            getOwner().getSessionComponent().tryAddLoginDays();
        }

        new PlayerDayRefreshEvent(getOwner()).dispatch();
    }

    private void dispatchWeekRefresh() {
        weekTask = null;
        getProp().setWeekRefreshTsMs(SystemClock.now());
        addWeekRefreshTimer();
        new PlayerWeekRefreshEvent(getOwner()).dispatch();
    }
}

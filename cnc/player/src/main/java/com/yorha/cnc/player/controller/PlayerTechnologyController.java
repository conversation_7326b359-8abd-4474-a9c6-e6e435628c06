package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerTech;

/**
 * 科技相关接口
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_TECH)
public class PlayerTechnologyController {

    /**
     * 研究科技
     */
    @CommandMapping(code = MsgType.PLAYER_RESEARCHTECH_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerTech.Player_ResearchTech_C2S msg) {
        // 建筑类型
        int techSubId = msg.getTechSubId();
        playerEntity.getTechComponent().researchTech(techSubId);
        return PlayerTech.Player_ResearchTech_S2C.getDefaultInstance();
    }


    /**
     * 立即完成研究科技
     */
    @CommandMapping(code = MsgType.PLAYER_IMMEDIATELYRESEARCHTECH_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerTech.Player_ImmediatelyResearchTech_C2S msg) {
        // 建筑类型
        int techSubId = msg.getTechSubId();
        playerEntity.getTechComponent().immediatelyResearchTech(techSubId, msg.getSPassWord(), false);
        return PlayerTech.Player_ResearchTech_S2C.getDefaultInstance();
    }

    /**
     * 领取科技
     */
    @CommandMapping(code = MsgType.PLAYER_RECEIVETECH_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerTech.Player_ReceiveTech_C2S msg) {
        // 建筑id
        int techSubId = msg.getTechSubId();
        playerEntity.getTechComponent().receiveTech(techSubId);
        return PlayerTech.Player_ReceiveTech_S2C.getDefaultInstance();
    }
}

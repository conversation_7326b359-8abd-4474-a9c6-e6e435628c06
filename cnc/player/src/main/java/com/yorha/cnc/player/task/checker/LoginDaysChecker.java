package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerLoginEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.LoginDaysExtraInfoProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 累计登录X天
 */
public class LoginDaysChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerLoginEvent.class.getSimpleName(),
            PlayerDayRefreshEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity player = event.getPlayer();
        final int requireDays = taskParams.getFirst();

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                final long loginDays = player.getStatisticComponent().getSingleStatistic(StatisticEnum.LOGIN_DAYS);
                prop.setProcess((int) Math.min(loginDays, requireDays));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof CheckTaskProcessEvent) {
                    if (player.isOnline()) {
                        // 接任务的时候可能不在线，要在线才能加一天
                        tryAddOneDay(prop, requireDays);
                    }
                } else if (event instanceof PlayerLoginEvent) {
                    // 登录直接尝试加一天
                    tryAddOneDay(prop, requireDays);
                } else if (event instanceof PlayerDayRefreshEvent) {
                    if (player.isOnline()) {
                        // 每日刷新的时候可能不在线的
                        tryAddOneDay(prop, requireDays);
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= requireDays;
    }

    private void tryAddOneDay(TaskInfoProp prop, int requireDays) {
        LoginDaysExtraInfoProp extraInfo = prop.getExtraInfo().getLoginDaysExtraInfo();
        long lastLoginTsMs = extraInfo.getLastUpdateTsMs();
        long nowMs = SystemClock.now();
        if (lastLoginTsMs <= 0) {
            // 任务刚创建，在线就加1
            addOneDay(prop, requireDays, extraInfo, nowMs);
        } else {
            // 判断一下今天是否已经记过了
            if (!TimeUtils.isSameDay(lastLoginTsMs, nowMs)) {
                addOneDay(prop, requireDays, extraInfo, nowMs);
            }
        }
    }

    private void addOneDay(TaskInfoProp prop, int requireDays, LoginDaysExtraInfoProp extraInfo, long nowMs) {
        extraInfo.setLastUpdateTsMs(nowMs);
        prop.setProcess(Math.min(requireDays, prop.getProcess() + 1));
    }
}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerKingdom.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 王国相关的CS协议
 *
 * <AUTHOR>
 */

@Controller(module = CommonEnum.ModuleEnum.ME_KINGDOM)
public class PlayerKingdomController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerKingdomController.class);

    /**
     * 国王任命
     */
    @CommandMapping(code = MsgType.PLAYER_KINGAPPOINT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_KingAppoint_C2S msg) {
        // 检查参数
        if (msg.getKingdomOfficeId() <= 0 || msg.getToPlayerId() <= 0) {
            LOGGER.warn("Player_KingAppoint_C2S param wrong {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // call到big scene上去
        playerEntity.getKingdomComponent().appoint(msg.getKingdomOfficeId(), msg.getToPlayerId());

        // 没出错认为成功
        return Player_KingSendGift_S2C.getDefaultInstance();
    }

    /**
     * 国王开启增益
     */
    @CommandMapping(code = MsgType.PLAYER_KINGOPENBUFF_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_KingOpenBuff_C2S msg) {
        // 检查参数
        if (msg.getKingdomBuffId() <= 0) {
            LOGGER.warn("Player_KingOpenBuff_C2S param wrong {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // call到big scene上去
        playerEntity.getKingdomComponent().openBuff(msg.getKingdomBuffId());

        // 没出错认为成功
        return Player_KingOpenBuff_S2C.getDefaultInstance();
    }


    /**
     * 国王赠送礼物
     */
    @CommandMapping(code = MsgType.PLAYER_KINGSENDGIFT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_KingSendGift_C2S msg) {
        // 检查参数
        if (msg.getKingdomGiftId() <= 0 || msg.getToPlayerId() <= 0) {
            LOGGER.warn("PLAYER_KINGSENDGIFT_C2S param lost {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // call到big scene上去
        playerEntity.getKingdomComponent().sendGift(msg.getKingdomGiftId(), msg.getToPlayerId());

        // 没出错认为成功
        return Player_KingSendGift_S2C.getDefaultInstance();
    }

    /**
     * 国王使用技能
     */
    @CommandMapping(code = MsgType.PLAYER_KINGUSESKILL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_KingUseSkill_C2S msg) {
        // 检查参数
        if (msg.getKingdomSkillId() <= 0) {
            LOGGER.warn("PLAYER_KINGUSESKILL_C2S param lost {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 检查参数
        if (msg.getZoneId() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // call到big scene上去
        playerEntity.getKingdomComponent().useSkill(msg.getKingdomSkillId(), msg.getToTargetId(), msg.getZoneId());

        // 没出错认为成功
        return Player_KingUseSkill_S2C.getDefaultInstance();
    }

    /**
     * 拉取历任国王
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHHISTORYKINGS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FetchHistoryKings_C2S msg) {
        // 检查参数
        if (msg.getPage() <= 0) {
            LOGGER.warn("PLAYER_FETCHHISTORYKINGS_C2S param lost {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // call到big scene上去
        Player_FetchHistoryKings_S2C.Builder builder =
                playerEntity.getKingdomComponent().fetchHistoryKings(msg.getPage());

        LOGGER.info("PLAYER_FETCHHISTORYKINGS_C2S result {}", builder);
        return builder.build();
    }

    /**
     * 拉取礼物信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHKINGDOMGIFTINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FetchKingdomGiftInfo_C2S msg) {
        if (msg.getIsFetchingLeftNum() && msg.hasGiftId()) {
            LOGGER.warn("PLAYER_FETCHKINGDOMGIFTINFO_C2S param cannot use both value {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (!msg.getIsFetchingLeftNum() && msg.getGiftId() == 0) {
            LOGGER.warn("PLAYER_FETCHKINGDOMGIFTINFO_C2S param giftId is zero {}", msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        Player_FetchKingdomGiftInfo_S2C.Builder builder =
                playerEntity.getKingdomComponent().fetchGiftInfo(msg.getIsFetchingLeftNum(), msg.getGiftId(), msg.getCheckPlayerId());
        return builder.build();
    }

    /**
     * 拉取王国官职信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHKINGDOMOFFICEINFO_C2S)
    public void handle(PlayerEntity playerEntity, Player_FetchKingdomOfficeInfo_C2S msg, int seqId) {
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getKingdomComponent().fetchOfficeInfo(sessionRef, seqId);
    }

}

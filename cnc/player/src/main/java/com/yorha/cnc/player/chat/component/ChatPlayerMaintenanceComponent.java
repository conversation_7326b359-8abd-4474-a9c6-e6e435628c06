package com.yorha.cnc.player.chat.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.constant.ChatConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 聊天数据和关系维护组件
 *
 * <AUTHOR>
 */
public class ChatPlayerMaintenanceComponent extends ChatPlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(ChatPlayerMaintenanceComponent.class);

    public ChatPlayerMaintenanceComponent(ChatPlayerEntity owner) {
        super(owner);
    }

    /**
     * 获取聊天成员
     */
    public PlayerChat.Player_GetChatMember_S2C getChatMember(PlayerChat.Player_GetChatMember_C2S msg) {
        PlayerChat.Player_GetChatMember_S2C.Builder builder = PlayerChat.Player_GetChatMember_S2C.newBuilder();
        CommonEnum.ChatChannel chatChannel = CommonEnum.ChatChannel.CC_GROUP;
        String channelId = msg.getChannelId();
        ChannelInfoProp prop = getOwner().getProp().getChannelInfoV(chatChannel.getNumber());
        if (prop == null) {
            return builder.build();
        }
        ChatItemProp itemProp = prop.getItemV(channelId);
        if (itemProp == null) {
            return builder.build();
        }
        int version = itemProp.getVersion();
        // version = 0 表示客户端本地无缓存
        if (msg.getVersion() == 0 || msg.getVersion() < version) {
            try {
                SsGroupChat.FetchChatMemberAns ans = getOwner().ownerActor().callSharedChat(chatChannel.getNumber(), channelId,
                        SsGroupChat.FetchChatMemberAsk.getDefaultInstance());
                builder.setChannelId(channelId)
                        .setVersion(ans.getDescription().getVersion())
                        .setOwner(ans.getDescription().getChatOwner())
                        .setMembers(ans.getDescription().getMembers());
                return builder.build();
            } catch (GeminiException e) {
                // 错误码为Item需要删除，则释放
                if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                    this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, channelId);
                }
                throw e;
            }
        }
        LOGGER.error("ChatPlayerMaintenanceComponent getChatMember, illegal chat member request, local group chat version={}, server version={}", msg.getVersion(), version);
        return builder.build();
    }

    /**
     * 检查玩家自身是否再某个群聊中。
     */
    public boolean isInGroupChat(final String channelId) {
        final SsGroupChat.JudgeMemberAsk ask = SsGroupChat.JudgeMemberAsk.newBuilder()
                .setPlayerId(this.ownerActor().getPlayerId())
                .build();
        try {
            final SsGroupChat.JudgeMemberAns ans = ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, channelId, ask);
            return ans.getIsIn();
        } catch (GeminiException e) {
            // 错误码为Item需要删除，则释放
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, channelId);
            }
            throw e;
        }
    }

    /**
     * 邀请玩家加入群聊
     */
    public void invitePlayer(PlayerChat.Player_InvitePlayer_C2S msg, int seqId) {
        if (msg.getInviteListList().isEmpty()) {
            throw new GeminiException(ErrorCode.AT_LEAST_INVITE_ONE_PLAYER);
        }
        IActorRef sessionRef = ownerActor().sender();
        CardHelper.batchQueryPlayerCard(ownerActor(), msg.getInviteListList(),
                (map) -> onInvitePlayerQueryEnd(sessionRef, msg, seqId, map));
    }

    private void onInvitePlayerQueryEnd(IActorRef sessionRef, PlayerChat.Player_InvitePlayer_C2S msg, int seqId, Map<Long, StructPB.PlayerCardInfoPB> data) {
        final SsGroupChat.InviteNewMemberAsk.Builder ask = SsGroupChat.InviteNewMemberAsk.newBuilder();
        final PlayerChat.Player_InvitePlayer_S2C.Builder builder = PlayerChat.Player_InvitePlayer_S2C.newBuilder();
        // 填充成员
        for (final long playerId : msg.getInviteListList()) {
            StructPB.PlayerCardInfoPB pb = data.get(playerId);
            if (pb == null) {
                continue;
            }
            CommonMsg.ChatMember.Builder memberBuilder = CommonMsg.ChatMember.newBuilder();
            ask.getMembersBuilder().addMembers(memberBuilder.setPlayerId(playerId).setZoneId(pb.getZoneId()).build());
        }
        ask.setInviter(ownerActor().getPlayerId());

        final SsGroupChat.InviteNewMemberAns ans;
        try {
            ans = ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, msg.getGroupChatId(), ask.build());
        } catch (GeminiException e) {
            if (e.isFailed()) {
                throw e;
            }
            // 错误码为Item需要删除，则释放
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, msg.getGroupChatId());
            }
            PlayerEntity entity = ownerActor().getEntityOrNull();
            if (entity != null) {
                entity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_INVITEPLAYERJOINGROUP_S2C, e, null);
            }
            return;
        }

        if (!ans.getFailedPlayerList().isEmpty()) {
            LOGGER.info("ChatPlayerMaintenanceComponent onInvitePlayerQueryEnd, invitePlayer fail, member {} up to group limit", ans.getFailedPlayerList());
            // 邀请人失败
            builder.addAllFailedPlayer(ans.getFailedPlayerList());
            PlayerEntity entity = ownerActor().getEntityOrNull();
            if (entity != null) {
                entity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_INVITEPLAYERJOINGROUP_S2C, null, builder.build());
            }
            return;
        }

        // 修改chatItem属性
        final ChatPlayerEntity chatPlayerEntity = ownerActor().getOrLoadChatPlayerEntity();
        final ChatItemProp chatItemProp = chatPlayerEntity.getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, msg.getGroupChatId());
        chatItemProp.setVersion(ans.getNewVersion());

        // 先回包
        builder.setInviteList(ans.getMembers()).setGroupChatId(msg.getGroupChatId());
        PlayerEntity entity = ownerActor().getEntityOrNull();
        if (entity != null) {
            entity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_INVITEPLAYERJOINGROUP_S2C, null, builder.build());
        }

        // 发送邀请成员系统消息
        final String inviterName = ownerActor().getOrLoadEntity().getName();
        final List<String> newMembers = new ArrayList<>();
        for (long playerId : msg.getInviteListList()) {
            StructPB.PlayerCardInfoPB pb = data.get(playerId);
            if (pb != null) {
                newMembers.add(pb.getCardHead().getName());
            }
        }

        final String memberName = String.join("、", newMembers);
        final CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
        messageData.setTemplateId(ChatConstants.JOIN_GROUP_MSG).getMsgParamBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayTextPb(inviterName))
                .addDatas(MsgHelper.buildDisPlayTextPb(memberName));
        chatPlayerEntity.getHandleChatComponent().sendSystemChat(CommonEnum.ChatChannel.CC_GROUP, msg.getGroupChatId(), messageData.build());
    }

    /**
     * 群主删除玩家
     */
    public void removeGroupMember(PlayerChat.Player_OwnerRemoveMember_C2S msg) {
        String channelId = msg.getGroupChatId();
        ChatItemProp chatItemProp = getOwner().getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, channelId);
        // 群主才有删除玩家的权限
        long owner = chatItemProp.getOwner();
        if (owner != ownerActor().getPlayerId()) {
            throw new GeminiException(ErrorCode.CHAT_PERMISSION_NOT_MET);
        }
        // 群主不能删除自己
        if (msg.getMembersList().contains(owner)) {
            throw new GeminiException(ErrorCode.CHAT_ILLEGAL_OPERATE);
        }
        // 获取玩家最新的名字
        final SsGroupChat.RemoveGroupMemberAsk.Builder ask = SsGroupChat.RemoveGroupMemberAsk.newBuilder();
        ask.setOperator(owner).addAllMemberIds(msg.getMembersList());
        final SsGroupChat.RemoveGroupMemberAns removeAns;
        try {
            removeAns = ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, channelId, ask.build());
        } catch (GeminiException e) {
            // 错误码为Item需要删除，则释放
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, channelId);
            }
            throw e;
        }

        // 如果删到只剩群主一个人，相当于解散群聊，直接清除chatItemProp
        if (removeAns.getDeleteGroup()) {
            getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, channelId);
            return;
        }
        // 修改chatItemProp
        if (removeAns.hasGroupOwner()) {
            chatItemProp.setOwner(removeAns.getGroupOwner());
        }
        if (removeAns.hasGroupVersion()) {
            chatItemProp.setVersion(removeAns.getGroupVersion());
        }
        List<String> names = new ArrayList<>();
        // 获取删除玩家的名字
        CardHelper.batchQueryPlayerHead(ownerActor(), msg.getMembersList(),
                (map) -> {
                    for (long playerId : msg.getMembersList()) {
                        StructPB.PlayerCardHeadPB pb = map.get(playerId);
                        if (pb != null) {
                            names.add(pb.getName());
                        }
                    }
                    // 发送删除玩家的系统消息
                    String ownerName = ownerActor().getOrLoadEntity().getName();
                    String memberName = String.join("、", names);
                    CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
                    messageData.setTemplateId(ChatConstants.REMOVE_MEMBER_MSG).getMsgParamBuilder().getParamsBuilder()
                            .addDatas(MsgHelper.buildDisPlayTextPb(ownerName))
                            .addDatas(MsgHelper.buildDisPlayTextPb(memberName));
                    getOwner().getHandleChatComponent().sendSystemChat(CommonEnum.ChatChannel.CC_GROUP, msg.getGroupChatId(), messageData.build());
                });
    }

    /**
     * 玩家主动退群
     */
    public void playerQuitGroup(String groupChatId) {
        // 检查玩家是否在该群聊中
        ownerActor().getOrLoadChatPlayerEntity().getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, groupChatId);

        String name = ownerActor().getOrLoadEntity().getCardHead().getName();
        final SsGroupChat.RemoveGroupMemberAns removeAns;
        try {
            SsGroupChat.RemoveGroupMemberAsk.Builder ask = SsGroupChat.RemoveGroupMemberAsk.newBuilder();
            ask.setOperator(ownerActor().getPlayerId())
                    .addMemberIds(ownerActor().getPlayerId())
                    .setName(name)
                    .setQuitGroup(true);
            removeAns = ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, groupChatId, ask.build());
        } catch (GeminiException e) {
            // 错误码为Item需要删除，则释放
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
            }
            throw e;
        }

        // 移除prop
        if (removeAns.getDeleteGroup()) {
            getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
        }
    }

    /**
     * 转让群主
     */
    public void transferGroupOwner(PlayerChat.Player_TransferGroupOwner_C2S msg) {
        String groupChatId = msg.getGroupChatId();
        ChatItemProp chatItemProp = getOwner().getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
        if (ownerActor().getPlayerId() != chatItemProp.getOwner()) {
            throw new GeminiException(ErrorCode.CHAT_ILLEGAL_OPERATE);
        }
        try {
            final SsGroupChat.TransferGroupOwnerAsk.Builder ask = SsGroupChat.TransferGroupOwnerAsk.newBuilder();
            ask.setOperator(ownerActor().getPlayerId()).setNewOwner(msg.getNewOwner());
            ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, groupChatId, ask.build());
        } catch (GeminiException e) {
            // 错误码为Item需要删除，则释放
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
            }
            throw e;
        }

        // 修改群聊属性
        chatItemProp.setOwner(msg.getNewOwner());

        // 获取新群主最新的名字
        final SsPlayerCard.QueryPlayerCardHeadAns ans;
        try {
            ans = CardHelper.queryPlayerCardHeadSync(ownerActor(), msg.getNewOwner());
        } catch (Exception e) {
            throw new GeminiException(ErrorCode.CHAT_PLAYER_NOT_EXIST);
        }
        if (ans == null || !ans.hasCardHead()) {
            throw new GeminiException(ErrorCode.CHAT_PLAYER_NOT_EXIST);
        }
        // 发送群主转移系统消息
        String oldOwnerName = ownerActor().getOrLoadEntity().getName();
        String newOwnerName = ans.getCardHead().getName();
        CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
        messageData.setTemplateId(ChatConstants.TRANSFER_GROUP_OWNER_MSG).getMsgParamBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayTextPb(oldOwnerName))
                .addDatas(MsgHelper.buildDisPlayTextPb(newOwnerName));
        getOwner().getHandleChatComponent().sendSystemChat(CommonEnum.ChatChannel.CC_GROUP, groupChatId, messageData.build());
    }

    /**
     * 解散群聊
     */
    public void dismissChatGroup(long operator, String groupChatId) {
        ChatItemProp chatItemProp = getOwner().getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
        long owner = chatItemProp.getOwner();
        if (owner != operator) {
            throw new GeminiException(ErrorCode.CHAT_PERMISSION_NOT_MET);
        }
        try {
            SsGroupChat.DismissChatGroupAsk.Builder builder = SsGroupChat.DismissChatGroupAsk.newBuilder();
            builder.setChatGroupId(groupChatId).setOperator(operator);
            ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, groupChatId, builder.build());
        } catch (GeminiException e) {
            if (e.getCodeId() != ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                throw e;
            }
        }

        // 删除群聊
        this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
    }

    /**
     * 修改群名
     */
    public void modifyGroupName(final String groupChatId, final String newName) {
        // 敏感词检查
        this.groupNameCheck(newName);

        final ChatItemProp chatItemProp = getOwner().getHandleChatComponent().getChatItemOrException(CommonEnum.ChatChannel.CC_GROUP, groupChatId);

        try {
            SsGroupChat.ModifyGroupNameAsk.Builder builder = SsGroupChat.ModifyGroupNameAsk.newBuilder();
            builder.setChatGroupId(groupChatId).setNewName(newName).setOperator(ownerActor().getPlayerId());
            ownerActor().callSharedChat(CommonEnum.ChatChannel.CC_GROUP_VALUE, groupChatId, builder.build());
        } catch (GeminiException e) {
            // 需要删除无效的数据
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                this.getOwner().getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, groupChatId);
            }
            throw e;
        }

        // 修改群聊属性
        chatItemProp.setGroupName(newName);

        // 发送修改群名系统消息
        final String operator = ownerActor().getOrLoadEntity().getName();
        CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
        messageData.setTemplateId(ChatConstants.MODIFY_GROUP_NAME_MSG).getMsgParamBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayTextPb(operator))
                .addDatas(MsgHelper.buildDisPlayTextPb(newName));
        getOwner().getHandleChatComponent().sendSystemChat(CommonEnum.ChatChannel.CC_GROUP, groupChatId, messageData.build());
    }

    /**
     * 命名检测
     */
    private void groupNameCheck(String name) throws GeminiException {
        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = ownerActor().getOrLoadEntity().syncCheckText(name, CommonEnum.UgcSceneId.USI_GROUP_CHAT_NAME);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }
    }
}

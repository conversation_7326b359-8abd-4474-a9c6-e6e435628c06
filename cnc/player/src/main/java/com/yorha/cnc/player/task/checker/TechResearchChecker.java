package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.TechFullOpenEvent;
import com.yorha.cnc.player.event.task.TechResearchEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 完成科技研究累计多少次
 * param1:次数
 *
 * <AUTHOR>
 */
public class TechResearch<PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            TechResearchEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            TechFullOpenEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(
            PlayerTaskEvent event,
            TaskInfoProp prop,
            TaskPoolTemplate taskTemplate
    ) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer times = taskParams.getFirst();
        if (event.getPlayer().getTechComponent().isAllUnlock()) {
            prop.setProcess(times);
            return true;
        }
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int playerTechResearchTotal = (int) entity.getStatisticComponent().getSingleStatistic(StatisticEnum.TECH_RESEARCH_TOTAL);
                prop.setProcess(Math.min(playerTechResearchTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof TechResearchEvent) {
                    int techResearchNum = ((TechResearchEvent) event).getResearchNum();
                    prop.setProcess(Math.min(prop.getProcess() + techResearchNum, times));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

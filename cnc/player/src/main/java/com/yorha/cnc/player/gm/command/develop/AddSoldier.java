package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerSoldier;
import com.yorha.proto.SsScenePlayer.PlayerAddSoldierAsk;

import java.util.Map;

/**
 * 增加士兵
 * <p>
 * 同步修改 AddSoldierFlow
 *
 * <AUTHOR>
 */
public class AddSoldier implements PlayerGmCommand {
    /**
     * 添加士兵 Soldier type=add soldierId=1001 num=10000
     * 添加士兵 Soldier type=addAll num=10000
     * 解散士兵 Soldier type=dismiss soldierId=1001 num=10000
     * 晋升士兵 Soldier type=levelup soldierId=1001 num=10000
     * 训练士兵 Soldier type=train soldierId=1001 num=10000 is_fast=true value2:是否一键训练
     */
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity playerEntity = actor.getEntity();
        String type = args.get("type");
        int num = Integer.parseInt(args.get("num"));
        PlayerAddSoldierAsk.Builder call = PlayerAddSoldierAsk.newBuilder();
        switch (type) {
            case "addAll": {
                for (Integer soldierId : ResHolder.getResService(SoldierResService.class).allSoldierIdsPlayerCanGetForGm()) {
                    actor.callCurScene(call.setPlayerId(playerId).setSoldierId(soldierId).setAddNum(num).setReason(SoldierNumChangeReason.gm.toString()).build());
                }
                break;
            }
            case "add": {
                int soldierId = Integer.parseInt(args.get("soldierId"));
                actor.callCurScene(call.setPlayerId(playerId).setSoldierId(soldierId).setAddNum(num).setReason(SoldierNumChangeReason.gm.toString()).build());
                break;
            }
            case "dismiss": {
                int soldierId = Integer.parseInt(args.get("soldierId"));
                PlayerSoldier.Player_SoldierDismiss_C2S.Builder builder = PlayerSoldier.Player_SoldierDismiss_C2S.newBuilder();
                builder.setSoldierId(soldierId).setNum(num);
                playerEntity.getSoldierComponent().handleDismiss(builder.build());
                break;
            }
            case "levelup": {
                int soldierId = Integer.parseInt(args.get("soldierId"));
                boolean isFast = Boolean.parseBoolean(args.get("isFast"));
                if (isFast) {
                    PlayerSoldier.Player_SoldierFastLevelUp_C2S.Builder builder = PlayerSoldier.Player_SoldierFastLevelUp_C2S.newBuilder();
                    builder.setSoldierId(soldierId).setNum(num);
                    playerEntity.getSoldierComponent().handleFastLevelUp(builder.build());
                } else {
                    PlayerSoldier.Player_SoldierLevelUp_C2S.Builder builder = PlayerSoldier.Player_SoldierLevelUp_C2S.newBuilder();
                    builder.setSoldierId(soldierId).setNum(num);
                    playerEntity.getSoldierComponent().handleLevelUp(builder.build());
                }
                break;
            }
            case "train": {
                int soldierId = Integer.parseInt(args.get("soldierId"));
                boolean isFast = Boolean.parseBoolean(args.get("isFast"));
                if (isFast) {
                    PlayerSoldier.Player_SoldierFastTrain_C2S.Builder builder = PlayerSoldier.Player_SoldierFastTrain_C2S.newBuilder();
                    builder.setSoldierId(soldierId).setNum(num);
                    playerEntity.getSoldierComponent().handleFastTrain(builder.build());
                } else {
                    playerEntity.getSoldierComponent().handleTrain(soldierId, num, false);
                }
                break;
            }
            default:
                break;
        }
    }

    @Override
    public String showHelp() {
        return "AddSoldier type={} soldierId={} num={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_HERO_PLANE_SOLDIER;
    }
}

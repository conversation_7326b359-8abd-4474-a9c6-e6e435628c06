package com.yorha.cnc.player.gm.command.campaign;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 设置战役关卡事件
 */
public class SetCampaignMissionEvent implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int missionIndex = Integer.parseInt(args.get("mission")) - 1;
        int eventId = Integer.parseInt(args.get("event"));
        // 更新战役关卡事件
        actor.getOrLoadEntity().getCampaignComponent().gmSetMissionEvent(missionIndex, eventId);
    }

    @Override
    public String showHelp() {
        return "SetCampaignMissionEvent mission={value} event={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

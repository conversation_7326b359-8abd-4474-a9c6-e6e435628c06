package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

/**
 * vip尊享礼包
 *
 * <AUTHOR>
 */
public class VipExclusiveGoods implements Goods {

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        owner.getVipComponent().checkBuyExclusiveBoxBeforeBuyByGoodsId(goodsTemplate.getId());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        // 一般来说不用阻断，该做的校验在checkApply里已经做掉了
        owner.getVipComponent().beforeDeliverExclusiveBoxNoThrow(goodsTemplate.getId());
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        owner.getVipComponent().afterDeliverExclusiveBoxNoThrow(goodsTemplate.getId());
        return Collections.emptyList();
    }
}

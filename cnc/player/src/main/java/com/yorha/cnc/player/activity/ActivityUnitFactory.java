package com.yorha.cnc.player.activity;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.activity.unit.PlayerActivityTaskUnit;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.game.gen.prop.ActivityProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityTemplate;

import java.util.Map;

public class ActivityUnitFactory {
    private static final Logger LOGGER = LogManager.getLogger(ActivityUnitFactory.class);

    /**
     * 不同类型unit作为activity一级unit的构造器
     */
    static final Map<CommonEnum.ActivityUnitType, UnitConstructor> CONSTRUCTOR_MAP = Maps.newConcurrentMap();

    @FunctionalInterface
    public interface UnitConstructor {
        BasePlayerActivityUnit create(PlayerActivity owner, ActivityProp prop, ActivityTemplate template);
    }

    public static void register(CommonEnum.ActivityUnitType type, UnitConstructor constructor) {
        if (CONSTRUCTOR_MAP.containsKey(type)) {
            throw new GeminiException("activity unit constructor duplicated. {}", type);
        }
        LOGGER.info("ActivityUnitFactory register {}", type);
        CONSTRUCTOR_MAP.put(type, constructor);
    }

    static {
        // 扫描所有Unit，触发静态注册
        final JavaClassScanner scanner = new JavaClassScanner();
        for (Class<? extends BasePlayerActivityUnit> eventClazz : scanner.getSubTypesOf(PlayerActivityTaskUnit.class.getPackage().getName(), BasePlayerActivityUnit.class)) {
            try {
                // 触发class的init，执行static block的register
                Class.forName(eventClazz.getName());
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        }
    }

    static UnitConstructor get(CommonEnum.ActivityUnitType type) {
        UnitConstructor constructor = CONSTRUCTOR_MAP.get(type);
        if (constructor == null) {
            throw new GeminiException("activity unit constructor not found. {}", type);
        }
        return constructor;
    }

}

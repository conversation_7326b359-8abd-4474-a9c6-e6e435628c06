package com.yorha.cnc.player.controller;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;
import com.yorha.proto.SsClanRank;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 军团排行模块
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_RANK)
public class PlayerClanRankController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanRankController.class);

    @CommandMapping(code = MsgType.PLAYER_GETCLANRANKPAGEINFO_C2S)
    public PlayerClan.Player_GetClanRankPageInfo_S2C handle(PlayerEntity playerEntity, PlayerClan.Player_GetClanRankPageInfo_C2S msg) {
        CommonEnum.RankType rankType = msg.getType();
        int page = msg.getPage();
        int rankId = msg.getRankId();
        SsClanRank.GetClanRankPageInfoAns answer = playerEntity.getPlayerClanComponent().getClanRankInfoList(rankId, page);
        PlayerClan.Player_GetClanRankPageInfo_S2C.Builder ret = PlayerClan.Player_GetClanRankPageInfo_S2C.newBuilder();
        ret.addAllDto(answer.getDtoList());
        if (answer.hasMyRankInfoDTO()) {
            // 自己的信息在榜内
            ret.setMyRankInfoDTO(answer.getMyRankInfoDTO());
        } else {
            LOGGER.error("get clan rank page info error: {}, no rank info for player {}", rankId, playerEntity.getPlayerId());
        }
        ret.setType(rankType);
        ret.setPage(page);
        ret.setTotal(answer.getTotal());
        ret.setRankId(rankId);
        return ret.build();

    }

    @CommandMapping(code = MsgType.PLAYER_GETTOPCLANRANKINFO_C2S)
    public PlayerClan.Player_GetTopClanRankInfo_S2C handle(PlayerEntity playerEntity, PlayerClan.Player_GetTopClanRankInfo_C2S msg) {
        SsClanRank.GetTopClanRankInfoAns answer = playerEntity.getPlayerClanComponent().getTopClanRankInfo();
        PlayerClan.Player_GetTopClanRankInfo_S2C.Builder ret = PlayerClan.Player_GetTopClanRankInfo_S2C.newBuilder();
        ret.addAllDto(answer.getDtoList());
        ret.setRankResetTsMs(answer.getRankResetTsMs());
        return ret.build();
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.KILL_RALLY_MONSTER_NUM_GROUPBY_LEVEL;
import static com.yorha.proto.CommonEnum.MonsterCategory.RALLY_MONSTER_VALUE;

/**
 * 摧毁特定等级集结城寨x次
 * param1: 最小等级
 * param2: 最大等级
 * param3: 次数
 *
 * <AUTHOR>
 */
public class PlayerFinishAttackRallyMonsterChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerKillBigSceneMonsterEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int minLevel = taskParams.get(0);
        int maxLevel = taskParams.get(1);
        int countConfig = taskParams.get(2);
        // 仅击杀集结城寨才会更新任务进度
        if (event instanceof PlayerKillBigSceneMonsterEvent) {
            int category = ((PlayerKillBigSceneMonsterEvent) event).getMonsterCategory();
            if (category != RALLY_MONSTER_VALUE) {
                return false;
            }
        }
        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int attackTotal = 0;
            for (int level = minLevel; level <= maxLevel; level++) {
                attackTotal += (int) event.getPlayer().getStatisticComponent().getSecondStatistic(KILL_RALLY_MONSTER_NUM_GROUPBY_LEVEL, level);
            }
            prop.setProcess(Math.min(attackTotal, countConfig));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof PlayerKillBigSceneMonsterEvent) {
                int monsterLevel = ((PlayerKillBigSceneMonsterEvent) event).getLevel();
                if (minLevel <= monsterLevel && monsterLevel <= maxLevel) {
                    prop.setProcess(Math.min(prop.getProcess() + 1, countConfig));
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }

}

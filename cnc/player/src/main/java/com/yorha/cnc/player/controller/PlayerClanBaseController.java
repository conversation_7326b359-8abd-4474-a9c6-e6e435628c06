package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 军团基础模块: 使用到ss_clan_base里ss协议的cs协议，可以放到这里~
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_BASE)
public class PlayerClanBaseController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanBaseController.class);

    /**
     * 创建联盟
     */
    @CommandMapping(code = MsgType.PLAYER_CREATECLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CreateClan_C2S msg) {
        if (!msg.hasParam()) {
            LOGGER.warn("Player_CreateClan_C2S failed, player {}, msg {}", playerEntity, msg);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        long clanId = playerEntity.getPlayerClanComponent().playerCreateClan(msg.getParam());
        return PlayerClan.Player_CreateClan_S2C.newBuilder().setId(clanId).build();
    }

    /**
     * 查询联盟列表 含关键字搜索
     */
    @CommandMapping(code = MsgType.PLAYER_SEARCHCLAN_C2S)
    public void handle(PlayerEntity playerEntity, PlayerClan.Player_SearchClan_C2S msg, int seqId) {
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getPlayerClanComponent().playerSearchClan(sessionRef, msg.getName(), seqId);
    }

    /**
     * 请求军团主页面数据
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANSIMPLEINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanSimpleInfo_C2S msg) {
        final long clanId = msg.getClanId();
        if (clanId <= 0L) {
            LOGGER.warn("clan id {} wrong", clanId);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CommonMsg.ClanCardInfo info = playerEntity.getPlayerClanComponent().getSingleClanCardInfo(clanId);
        if (info == null) {
            LOGGER.warn("clan {} not exist, null info here", clanId);
            return PlayerClan.Player_FetchClanSimpleInfo_S2C.newBuilder().build();
        }
        return PlayerClan.Player_FetchClanSimpleInfo_S2C.newBuilder().setClanInfo(info).build();
    }

    /**
     * 请求军团成员数据
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANMEMBERINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanMemberInfo_C2S msg) {
        final long clanId = msg.getClanId();
        if (clanId <= 0L) {
            LOGGER.warn("clan id {} wrong", clanId);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        StructClanPB.Int64ClanMemberMapPB memberMap = playerEntity.getPlayerClanComponent().getSingClanMemberInfo(clanId);
        if (memberMap == null) {
            LOGGER.warn("clan {} not exist, null member here", clanId);
            return PlayerClan.Player_FetchClanMemberInfo_S2C.newBuilder().build();
        }
        return PlayerClan.Player_FetchClanMemberInfo_S2C.newBuilder().setMember(memberMap).build();
    }

    /**
     * 解散军团
     */
    @CommandMapping(code = MsgType.PLAYER_DISSOLVECLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_DissolveClan_C2S msg) {
        // 是否在军团检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId <= 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        playerEntity.ownerActor().callCurClan(SsClanBase.PlayerDissolveClanAsk.newBuilder().setPlayerId(playerEntity.getPlayerId()).build());
        // 提前设置下次可以军团长的时间
        playerEntity.getPlayerClanComponent().setNextCanBeOwnerTsMsWhenDissolve();
        return PlayerClan.Player_DissolveClan_S2C.getDefaultInstance();
    }

    /**
     * 取消解散军团
     */
    @CommandMapping(code = MsgType.PLAYER_CANCELDISSOLVECLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CancelDissolveClan_C2S msg) {
        // 是否在军团检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId <= 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        playerEntity.ownerActor().callCurClan(SsClanBase.PlayerCancelDissolveClanAsk.newBuilder().setPlayerId(playerEntity.getPlayerId()).build());
        // 重置下次可以成为军团长的时间为当前时间
        playerEntity.getPlayerClanComponent().resetNextCanBeOwnerTsMs();
        return PlayerClan.Player_CancelDissolveClan_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_INVITETOCLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_InviteToClan_C2S msg) {
        if (!msg.hasInvitedPlayerId()) {
            // 没有邀请的人，邀请个鬼啊
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 检查一下
        playerEntity.getPlayerClanComponent().checkPlayerCanInvite();
        // 构建ss协议
        SsClanBase.InviteOtherToClanAsk.Builder ask = SsClanBase.InviteOtherToClanAsk.newBuilder();
        ask.setInvitedPlayerId(msg.getInvitedPlayerId()).setOpeartorId(playerEntity.getPlayerId());
        // call到自己军团上邀请
        playerEntity.ownerActor().callCurClan(ask.build());
        // 回程，狩猎成功了！
        return PlayerClan.Player_InviteToClan_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_HANDLECLANINVITATION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_HandleClanInvitation_C2S msg) {
        if (!msg.hasMailId() || !msg.hasOperateType() || !msg.hasClanId() || !msg.hasInvitePlayerName() || StringUtils.isEmpty(msg.getInvitePlayerName())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CommonEnum.ClanInvitationOpType operationType = CommonEnum.ClanInvitationOpType.forNumber(msg.getOperateType());
        if (operationType == null || operationType == CommonEnum.ClanInvitationOpType.CIOT_NONE) {
            LOGGER.warn("wrong operation type {}", msg.getOperateType());
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        final long clanId = msg.getClanId();
        if (clanId <= 0L) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 检查邀请还在不在，不在就不处理了
        playerEntity.getPlayerClanComponent().checkInvitationExist(clanId);
        switch (operationType) {
            case CIOT_AGREE:
                // 同意, 走加入军团逻辑
                playerEntity.getPlayerClanComponent().playerApplyJoinClan(msg.getClanId(), msg.getInvitePlayerName());
                break;
            case CIOT_REFUSE:
                // 拒绝, 告诉一下之前的军团
                SsClanBase.RefuseClanInviteCmd.Builder cmd = SsClanBase.RefuseClanInviteCmd.newBuilder();
                cmd.setInvitedPlayerId(playerEntity.getPlayerId());
                playerEntity.ownerActor().tellClan(clanId, cmd.build());
                break;
            default:
                LOGGER.error("wrong operation type {}", msg.getOperateType());
                return PlayerClan.Player_HandleClanInvitation_S2C.newBuilder().setMailId(msg.getMailId())
                        .setStatus(CommonEnum.MailInviteState.MAIL_INVITE_NONE).build();
        }
        long mailId = msg.getMailId();
        // 更改邮件信息, 如果上面抛出错误码了就不改邮件信息了
        CommonEnum.MailInviteState newStatus = playerEntity.getMailComponent().mailChangeInviteState(mailId, operationType);
        return PlayerClan.Player_HandleClanInvitation_S2C.newBuilder().setMailId(mailId).setStatus(newStatus).build();
    }
}

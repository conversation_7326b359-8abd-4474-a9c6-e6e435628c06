package com.yorha.cnc.player.gm.command.server;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 获取服务器时间
 */
public class GetServerTime implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String systemDate = TimeUtils.timeStampMs2String(SystemClock.nowNative());
        String offsetDate = TimeUtils.now2String();
        String zoneOpenTime = TimeUtils.timeStampMs2String(ZoneContext.getServerOpenTsMs());
        String result = StringUtils.format("当前系统物理时间:{}, 当前系统逻辑时间:{}, 当前服务器时区:{}, 开服时间:{}", systemDate, offsetDate, TimeUtils.getZoneId().toString(), zoneOpenTime);
        throw new GeminiException(ErrorCode.SYSTEM_WARNING, result);
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}
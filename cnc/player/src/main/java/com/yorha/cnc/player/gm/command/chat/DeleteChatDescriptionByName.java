package com.yorha.cnc.player.gm.command.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.DeleteByPartKeyAsk;
import com.yorha.common.db.tcaplus.result.DeleteByPartKeyResult;
import com.yorha.common.db.tcaplus.result.DeleteResult;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.TcaplusDb;

import java.util.Map;

/**
 * 直接删除聊天描述，用于测试聊天过期，仅支持私聊、群聊。
 *
 * <AUTHOR>
 */
public class DeleteChatDescriptionByName implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        final int channelType = Integer.parseInt(args.get("channelType"));
        final CommonEnum.ChatChannel chatChannel = CommonEnum.ChatChannel.forNumber(channelType);
        if (chatChannel == null) {
            throw new GeminiException("channelType={} is not right", args.get("channelType"));
        }

        final String channel = args.get("channel");
        if (StringUtils.isEmpty(channel)) {
            throw new GeminiException("channel is empty");
        }

        switch (chatChannel) {
            case CC_PRIVATE:
                this.deletePrivateChatByName(actor, channel);
                break;
            case CC_GROUP:
                this.deleteGroupChatByName(actor, channel);
                break;
            default:
                throw new GeminiException("channelType={} is not PRIVATE OR GROUP", chatChannel);
        }
    }

    /**
     * 通过名称删除群聊数据。
     *
     * @param actor       玩家。
     * @param channelName 聊天名称。
     */
    private void deleteGroupChatByName(final PlayerActor actor, final String channelName) {
        final ChannelInfoProp channelInfoProp = actor.getOrLoadChatPlayerEntity().getProp().getChannelInfoV(CommonEnum.ChatChannel.CC_GROUP_VALUE);
        for (final String chatId : channelInfoProp.getItem().keySet()) {
            final ChatItemProp itemV = channelInfoProp.getItemV(chatId);
            if (itemV.getGroupName().equals(channelName)) {
                this.deleteChatDescription(actor, CommonEnum.ChatChannel.CC_GROUP, chatId);
                return;
            }
        }
        throw new GeminiException("Group Chat, channelName={} not a useful chatName", channelName);
    }

    /**
     * 通过其他的玩家删除私聊数据。
     *
     * @param actor       玩家。
     * @param otherPlayer 另外的playerId。
     */
    private void deletePrivateChatByName(final PlayerActor actor, final String otherPlayer) {
        final long otherPlayerId = Long.parseLong(otherPlayer);
        final String chatId = ChatHelper.getPrivateChatIdFromPlayerIds(actor.getPlayerId(), otherPlayerId);
        final ChannelInfoProp channelInfoProp = actor.getOrLoadChatPlayerEntity().getProp().getChannelInfoV(CommonEnum.ChatChannel.CC_PRIVATE_VALUE);
        if (!channelInfoProp.getItem().containsKey(chatId)) {
            throw new GeminiException("Private Chat, data={} not a useful chatId", chatId);
        }
        try {
            final TcaplusDb.ChatTable.Builder deleteReq = TcaplusDb.ChatTable.newBuilder()
                    .setChannelType(CommonEnum.ChatChannel.CC_PRIVATE_VALUE)
                    .setChannelId(chatId);
            final DeleteByPartKeyAsk<TcaplusDb.ChatTable.Builder> deleteAsk = new DeleteByPartKeyAsk<>(deleteReq);
            final DeleteByPartKeyResult result = actor.callGameDb(deleteAsk);
            if (!result.isOk() && !result.isRecordNotExist()) {
                throw new GeminiException("delete chat fail code={}", result.getCode());
            }
        } catch (Exception e) {
            throw new GeminiException("delete chat description fail", e);
        }
        this.deleteChatDescription(actor, CommonEnum.ChatChannel.CC_PRIVATE, chatId);
    }

    /**
     * 删除聊天数据。
     *
     * @param actor       玩家。
     * @param chatChannel 聊天。
     * @param chatId      聊天id。
     */
    private void deleteChatDescription(final PlayerActor actor, final CommonEnum.ChatChannel chatChannel, final String chatId) {
        try {
            final TcaplusDb.ChatDescriptionTable.Builder deleteReq = TcaplusDb.ChatDescriptionTable.newBuilder()
                    .setChannelType(chatChannel.getNumber())
                    .setChannelId(chatId);
            final DeleteAsk<TcaplusDb.ChatDescriptionTable.Builder> deleteAsk = new DeleteAsk<>(deleteReq);
            final DeleteResult result = actor.callGameDb(deleteAsk);
            if (!result.isOk() && !result.isRecordNotExist()) {
                throw new GeminiException("delete chat fail code={}", result.getCode());
            }
        } catch (Exception e) {
            throw new GeminiException("delete chat description fail", e);
        }
    }

    @Override
    public String showHelp() {
        return "DeleteChatDescriptionByName channelType={} channel={}";
    }
}

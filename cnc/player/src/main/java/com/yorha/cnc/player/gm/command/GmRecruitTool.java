package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GmRecruitTool implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GmRecruitTool.class);
    Map<Integer, Integer> tempRecord = new HashMap<>();

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String param1 = args.get("recruitPoolType");
        String param2 = args.get("times");
        if (StringUtils.isEmpty(param1)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (StringUtils.isEmpty(param2)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int templateId = Integer.parseInt(param1);
        int times = Integer.parseInt(param2);
        if (times > 1000) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CommonEnum.RecruitPoolType recruitPoolType = CommonEnum.RecruitPoolType.forNumber(templateId);
        if (recruitPoolType == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        for (int i = 0; i < times; i++) {
            List<Integer> list = actor.getEntity().getRecruitComponent().recruit(recruitPoolType, CommonEnum.RecruitType.RT_SINGLE).getRewardConfigsList();
            for (int config : list) {
                int oldNum = tempRecord.getOrDefault(config, 0);
                tempRecord.put(config, oldNum + 1);
            }
        }
        LOGGER.info("ZeoRecruitTool player:{} record:{}", playerId, tempRecord);
        tempRecord.clear();
    }

    @Override
    public String showHelp() {
        return "GmRecruitTool recruitPoolType={value} times={value} ";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

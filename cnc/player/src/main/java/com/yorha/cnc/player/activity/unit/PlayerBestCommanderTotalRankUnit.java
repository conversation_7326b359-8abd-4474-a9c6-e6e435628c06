package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.rank.RankHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityScoreRankUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsRank;
import com.yorha.proto.SsSceneActivitySchedule;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityScoreRankTemplate;
import res.template.ActivityTemplate;
import res.template.RankTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 最强指挥官总榜
 *
 * <AUTHOR>
 */
public class PlayerBestCommanderTotalRankUnit extends BasePlayerActivityUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerBestCommanderTotalRankUnit.class);
    private boolean isInRankCache = false;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_BEST_COMMANDER_TOTAL_RANK, (owner, prop, template) ->
                new PlayerBestCommanderTotalRankUnit(owner, prop.getSpecUnit())
        );
    }

    private ActorTimer updateTimer = null;

    public PlayerBestCommanderTotalRankUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
        LOGGER.info("PlayerRankSnapshot rankId={} score={}",
                getRankId(),
                unitProp.getScoreRankUnit().getScore());
    }

    @Override
    public void onMigrate() {
        if (updateTimer != null) {
            updateTimer.cancel();
            updateTimer = null;
        }
    }

    @Override
    public void onExpire() {
        if (updateTimer != null) {
            updateTimer.cancel();
            updateTimer = null;
        }
        PlayerBestCommanderUnit bestCommanderUnit = ownerActivity.findFirstUnitInTopFatherActOf(PlayerBestCommanderUnit.class);
        if (bestCommanderUnit == null) {
            return;
        }
        bestCommanderUnit.onRankUnitExpire(ownerActivity.getActivityId());
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void forceOffImpl() {
    }


    public void addPoints(long addPoints) {
        if (addPoints > 0) {
            ActivityScoreRankUnitProp p = unitProp.getScoreRankUnit();
            p.setScore(p.getScore() + addPoints);
            updateScoreRank();
        }
    }

    protected void updateScoreRank() {
        if (updateTimer == null) {
            updateTimer = player().ownerActor().addTimer(
                    player().getPlayerId() + "-" + ownerActivity.getActivityId() + "-" + getUnitId(),
                    TimerReasonType.BEST_COMMANDER_TOTAL_RANK_UPDATE_SCORE, this::updateImpl,
                    RandomUtils.nextInt(3, 5),
                    TimeUnit.SECONDS
            );
        }
    }

    protected void updateImpl() {
        updateTimer = null;
        long score = unitProp.getScoreRankUnit().getScore();
        final int rankId = getRankId();
        if (rankId <= 0) {
            return;
        }
        if (!player().getPlayerRankComponent().isNeedSyncToZoneRank(rankId)) {
            return;
        }
        try {
            final long playerId = player().getPlayerId();
            final SsRank.MemberDto memberDto = RankHelper.buildMember(playerId, score, player().getZoneId());
            player().ownerActor().askZoneRank(player().getZoneId(), SsRank.UpdateBatchRankingAsk.newBuilder()
                    .setRankId(rankId)
                    .addMember(memberDto).build())
                    .onComplete((res, err) -> {
                                if (err != null) {
                                    // 活动过期了就不需要打错误日志了
                                    final long expireTsSec = ownerActivity.getProp().getEndTsSec();
                                    if (SystemClock.nowOfSeconds() >= expireTsSec) {
                                        return;
                                    }
                                    LOGGER.error("PlayerBestCommanderTotalRankUnit updateImpl failed", err);
                                    return;
                                }
                                SsRank.UpdateBatchRankingAns ans = (SsRank.UpdateBatchRankingAns) res;
                                SsRank.MemberRankDto memberRankDto = ans.getMemberMap().get(playerId);
                                if (memberRankDto == null) {
                                    return;
                                }
                                trySyncChooseItem2Zone(memberRankDto.getRank());
                            }
                    );
        } catch (Exception e) {
            WechatLog.error("PlayerBestCommanderTotalRankUnit updateImpl fail", e);
        }
    }

    private int getRankId() {
        ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId());
        final int scoreRankConfId = activityTemplate.getScoreRankId();
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, scoreRankConfId);
        return scoreRankTemplate.getRankId();
    }

    public void chooseItem(int itemId) {
        LOGGER.info("PlayerBestCommanderTotalRankUnit chooseItem item={}", itemId);
        unitProp.getBestCommanderTotalRankUnit().setChooseItem(itemId);
        if (isInRankCache) {
            syncChooseItem2Zone(itemId);
            return;
        }
        player().ownerActor().askZoneRank(player().getZoneId(), SsRank.RankInfoByPlayersAsk.newBuilder()
                .setRankId(getRankId())
                .addPlayers(player().getPlayerId()).build()).onComplete((res, err) -> {
            if (err != null) {
                LOGGER.error("PlayerBestCommanderTotalRankUnit chooseItem failed", err);
                return;
            }
            SsRank.RankInfoByPlayersAns ans = (SsRank.RankInfoByPlayersAns) res;
            StructMsg.RankInfoDTO rankInfoDTO = ans.getDtoMap().get(player().getPlayerId());
            if (rankInfoDTO == null) {
                return;
            }
            trySyncChooseItem2Zone(rankInfoDTO.getRank());
        });
    }

    private void trySyncChooseItem2Zone(int rank) {
        LOGGER.info("PlayerBestCommanderTotalRankUnit trySyncChooseItem2Zone rank={}", rank);
        final int rankId = getRankId();
        final Map<Integer, RankTemplate> rankTemplateMap = ResHolder.getInstance().getMap(RankTemplate.class);
        final RankTemplate rankTemplate = rankTemplateMap.get(rankId);
        if (rankTemplate == null) {
            return;
        }
        if (rank > rankTemplate.getMaxRank()) {
            return;
        }
        isInRankCache = true;
        final int chooseItem = unitProp.getBestCommanderTotalRankUnit().getChooseItem();
        syncChooseItem2Zone(chooseItem);
    }

    private void syncChooseItem2Zone(int chooseItem) {
        if (chooseItem <= 0) {
            return;
        }
        LOGGER.info("PlayerBestCommanderTotalRankUnit syncChooseItem2Zone item={}", chooseItem);
        final SsSceneActivitySchedule.BestCommanderChooseItemAsk.Builder ask = SsSceneActivitySchedule.BestCommanderChooseItemAsk.newBuilder();
        ask.setActId(ownerActivity.getActivityId())
                .setUnitId(getUnitId())
                .setPlayerId(player().getPlayerId())
                .setItemId(chooseItem);
        player().ownerActor().callSelfBigScene(ask.build());
    }
}

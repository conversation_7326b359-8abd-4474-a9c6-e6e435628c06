package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerHeroComponent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerHeroLevelUpEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;


/**
 * 英雄升级触发器
 *
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_HERO_LEVEL_UP)
public class HeroLevelUp extends AbstractActivityTrigger {
    private static final Logger LOGGER = LogManager.getLogger(HeroLevelUp.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerHeroLevelUpEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        final PlayerEntity playerEntity = event.getPlayer();
        final PlayerHeroComponent heroComponent = playerEntity.getHeroComponent();
        if (params == null) {
            return false;
        }
        final int heroId = params.get(CommonEnum.ActTriggerParamType.ATPT_HERO_ID);
        final int targetLevel = params.get(CommonEnum.ActTriggerParamType.ATPT_HERO_LEVEL);

        int heroLevel = 0;
        if (heroId == 0) {
            heroLevel = heroComponent.getMaxHeroLevel();
        } else {
            heroLevel = heroComponent.getHero(heroId).getLevel();
        }
        boolean trigger = (targetLevel == heroLevel);
        if (trigger) {
            triggerInfoProp.setTriggerTime(triggerInfoProp.getTriggerTime() + 1);
            LOGGER.info("HeroLevelUp onTrigger, params={}, heroLevel={}, triggerTime={}", params, heroLevel, triggerInfoProp.getTriggerTime());
        }
        return trigger;
    }
}

package com.yorha.cnc.player.gm.command.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.chat.ChatHelper;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class SendPrivateChatMsg implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        long targetPlayerId = Long.parseLong(args.get("playerId"));

        ChatPlayerEntity chatPlayerEntity = actor.getOrLoadChatPlayerEntity();

        final String channelId = ChatHelper.getPrivateChatIdFromPlayerIds(playerId, targetPlayerId);

        CommonMsg.ChatSession chatSession = CommonMsg.ChatSession.newBuilder()
                .setChatChannelId(channelId)
                .setChannelType(CommonEnum.ChatChannel.CC_PRIVATE)
                .build();
        CommonMsg.MessageData msg = CommonMsg.MessageData.newBuilder().setMsg("aaaaaaaa").build();
        chatPlayerEntity.getHandleChatComponent().chatRequest(chatSession, CommonEnum.MessageType.MT_PLAYER, msg, null);
    }

    @Override
    public String showHelp() {
        return "SendPrivateChatMsg playerId={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

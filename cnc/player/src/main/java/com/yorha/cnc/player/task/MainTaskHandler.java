package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainTaskFinishEvent;
import com.yorha.cnc.player.event.task.MechaFireEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskMainTemplate;
import res.template.TaskPoolTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 主线任务处理类
 *
 * <AUTHOR>
 */
public class MainTaskHandler extends AbstractTaskHandler {
    private static final Logger LOGGER = LogManager.getLogger(MainTaskHandler.class);

    public MainTaskHandler(PlayerEntity entity, CommonEnum.TaskClass tcMain) {
        super(entity, tcMain.name());
    }

    @Override
    public void onNewbieOver() {
        int firstMainTaskId = ResHolder.getResService(ConstKVResService.class).getTemplate().getFirstMainTaskId();
        TaskInfoProp taskInfoProp = getTaskProp().get(firstMainTaskId);
        if (taskInfoProp == null) {
            return;
        }
        if (isUnCompleted(taskInfoProp)) {
            LOGGER.info("mechaFire finish by newbieOver, taskId:{} status:{}", taskInfoProp.getId(), taskInfoProp.getStatus());
            new MechaFireEvent(getEntity()).dispatch();
        }
    }

    @Override
    public void loadAllTask() {
        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            addTaskEventMonitor(value);
        }
        // 检测任务解锁
        if (getTaskProp().values().size() <= 0) {
            int firstMainTaskId = ResHolder.getResService(ConstKVResService.class).getTemplate().getFirstMainTaskId();
            TaskMainTemplate taskMainTemplate = ResHolder.getTemplate(TaskMainTemplate.class, firstMainTaskId);
            addBatchTask(Sets.newHashSet(taskMainTemplate.getId()));
            //第一个主线任务不要自动完成
            //if (!getEntity().getNewbieComponent().isNewbie()) {
            //    onTaskFinish(firstMainTaskId, TaskGmEnum.NO_NEWBIE_ACCOUNT);
            //}
            getEntity().getNewbieComponent().checkNewbieGroup(taskMainTemplate, CommonEnum.TaskStatus.AT_ACCEPT);
        }
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> configIdList) {

        List<IntPairType> rewardList = new ArrayList<>();
        Int32TaskInfoMapProp taskDaily = getTaskProp();
        for (Integer configId : configIdList) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(taskDaily, configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }
            setNextTaskState(taskInfoProp);

            // 领奖
            TaskMainTemplate taskMainTemplate = ResHolder.getTemplate(TaskMainTemplate.class, configId);
            List<IntPairType> reward = taskMainTemplate.getRewardPairList();
            AssetPackage assetPackage = AssetPackage.builder().plusItems(reward).build();
            getEntity().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_TASK, formationSubReason(configId));

            rewardList.addAll(reward);
            takeRewardQLog(taskInfoProp);

            getEntity().getNewbieComponent().checkNewbieGroup(taskMainTemplate, CommonEnum.TaskStatus.AT_REWARD);
        }
        return rewardList;
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return getEntity().getProp().getTaskSystem().getTaskMain();
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum gmType) {
        super.onTaskFinish(configId, gmType);
        TaskMainTemplate template = ResHolder.getInstance().getValueFromMap(TaskMainTemplate.class, configId);
        getEntity().getNewbieComponent().checkNewbieGroup(template, CommonEnum.TaskStatus.AT_NOT_REWARD);
        // 主线任务完成
        new MainTaskFinishEvent(getEntity(), configId).dispatch();
        if (gmType == TaskGmEnum.CREATE_ALL_GM) {
            LOGGER.info("create all main task");
            return;
        }
        // 接取后置任务
        TaskMainTemplate mainPostTaskTemplate = ResHolder.getResService(TaskTemplateService.class).getMainPostTask(template);
        if (mainPostTaskTemplate != null) {
            addBatchTask(Sets.newHashSet(mainPostTaskTemplate.getId()));
            getEntity().getNewbieComponent().checkNewbieGroup(mainPostTaskTemplate, CommonEnum.TaskStatus.AT_ACCEPT);
        }
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(TaskMainTemplate.class, configId).getTaskId();
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    @Override
    public void fullMemoryByGm() {
        Set<Integer> collect = ResHolder.getInstance().getListFromMap(TaskMainTemplate.class).stream().map(TaskMainTemplate::getId).collect(Collectors.toSet());
        addBatchTask(collect);
        for (Integer id : collect) {
            TaskMainTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(TaskMainTemplate.class, id);
            getEntity().getNewbieComponent().checkNewbieGroup(valueFromMap, CommonEnum.TaskStatus.AT_ACCEPT);
        }
    }

    @Override
    public void completeTasksWithGm(int targetId) {
        // feat(2023年8月18日): 主线任务完成到指定id（并不完成指定的任务）
        if (ResHolder.getInstance().findValueFromMap(TaskMainTemplate.class, targetId) == null) {
            throw new GeminiException("id不存在");
        }
        TaskInfoProp targetProp = getTaskProp().get(targetId);
        if (isCompleted(targetProp)) {
            throw new GeminiException("任务已完成");
        }
        int max = ResHolder.getInstance().getListFromMap(TaskMainTemplate.class).size();
        int starIndex = 0;
        int firstMainTaskId = ResHolder.getResService(ConstKVResService.class).getTemplate().getFirstMainTaskId();
        TaskMainTemplate mainTemplate = ResHolder.getInstance().getValueFromMap(TaskMainTemplate.class, firstMainTaskId);
        TaskTemplateService resService = ResHolder.getResService(TaskTemplateService.class);

        while (starIndex++ < max) {
            TaskInfoProp curProp = getTaskProp().get(mainTemplate.getId());
            if (curProp == null) {
                addBatchTask(Sets.newHashSet(mainTemplate.getId()));
            }
            super.completeTasksWithGm(mainTemplate.getId());

            checkOrTakeReward(Lists.newArrayList(mainTemplate.getId()));

            mainTemplate = resService.getMainPostTask(mainTemplate);
            if (mainTemplate == null) {
                break;
            }
            if (mainTemplate.getId() == targetId) {
                break;
            }
        }
    }
}

package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 黑市使用金币购买事件
 *
 * <AUTHOR>
 */
public class DiscountStoreDiamondBuyEvent extends PlayerTaskEvent {
    private final int buyNum;

    public int getBuyNum() {
        return buyNum;
    }

    public DiscountStoreDiamondBuyEvent(PlayerEntity entity, int buyNum) {
        super(entity);
        this.buyNum = buyNum;
    }
}

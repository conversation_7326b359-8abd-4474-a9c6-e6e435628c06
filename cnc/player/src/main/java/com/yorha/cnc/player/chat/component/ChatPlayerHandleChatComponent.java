package com.yorha.cnc.player.chat.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.chat.handler.*;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.constant.ChatConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.game.gen.prop.PrepareChatItemProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstChatTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static java.lang.Math.max;

/**
 * 聊天请求处理component
 *
 * <AUTHOR>
 */
public class ChatPlayerHandleChatComponent extends ChatPlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(ChatPlayerHandleChatComponent.class);
    /**
     * 聊天channel处理handler
     */
    private static final Map<CommonEnum.ChatChannel, ChatHandler> CHAT_HANDLER_MAP = Maps.newEnumMap(CommonEnum.ChatChannel.class);

    static {
        ChatPlayerHandleChatComponent.register(new ClanChatHandler());
        ChatPlayerHandleChatComponent.register(new ServerChatHandler());
        ChatPlayerHandleChatComponent.register(new PrivateChatHandler());
        ChatPlayerHandleChatComponent.register(new GroupChatHandler());
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, ChatPlayerHandleChatComponent::onMainCityUpgradeEvent);
    }

    private final Map<CommonEnum.ChatChannel, Long> chatCd = new HashMap<>();

    public ChatPlayerHandleChatComponent(ChatPlayerEntity owner) {
        super(owner);
    }

    private static void register(ChatHandler handler) {
        CHAT_HANDLER_MAP.put(handler.chatChannel(), handler);
    }

    /**
     * 聊天常量表
     */
    public static ConstChatTemplate getConstTemplate() {
        return ResHolder.getInstance().getConstTemplate(ConstChatTemplate.class);
    }

    private static void onMainCityUpgradeEvent(MainCityUpgradeStaticEvent event) {
        ChatPlayerEntity chatPlayerEntity = event.getPlayer().ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getHandleChatComponent().reCalServerChannelCd();
    }

    private ChatHandler chatHandlerOf(CommonEnum.ChatChannel chatChannel) {
        if (!CHAT_HANDLER_MAP.containsKey(chatChannel)) {
            throw new GeminiException(ErrorCode.CHAT_HANDLER_NOT_EXIST);
        }
        return CHAT_HANDLER_MAP.get(chatChannel);
    }

    @Override
    public void onLoad() {
        // 加群群聊二阶段，二阶段检查，检查处于prepare状态的chat，是否其实已经进入提交状态
        final ChatPlayerProp prop = this.getChatProp();
        final ChannelInfoProp channelInfoProp = prop.getChannelInfoV(CommonEnum.ChatChannel.CC_GROUP_VALUE);

        if (channelInfoProp == null) {
            return;
        }

        final long nowTsMs = SystemClock.nowNative();
        for (final Map.Entry<String, PrepareChatItemProp> kv : channelInfoProp.getPrepareItem().entrySet()) {
            // 检查是否过期
            final long expireTsMs = kv.getValue().getCreateTsMs() + TimeUtils.second2Ms(GameLogicConstants.PREPARE_ITEM_EXPIRE_TIME);
            if (nowTsMs < expireTsMs) {
                continue;
            }

            // 延迟触发，ChatPlayer开始处理延时任务
            final SsPlayerChat.TryClearPrepareChatNtf.Builder ntf = SsPlayerChat.TryClearPrepareChatNtf.newBuilder();
            ntf.setChannelId(kv.getKey());
            this.ownerActor().tellPlayer(ownerActor().getZoneId(), ownerActor().getPlayerId(), ntf.build());
        }
    }

    /**
     * 记录聊天消息读到哪里，除cs协议或在chatRequest之后接一次readMessage外(表示读取自己发送的消息)，不得乱用
     */
    public void readMessage(CommonMsg.ChatSession chatSession, long index) {
        int channel = chatSession.getChannelType().getNumber();
        String id = chatSession.getChatChannelId();
        ChatPlayerProp chatProp = getChatProp();
        ChannelInfoProp prop = chatProp.getChannelInfo().get(channel);
        if (prop == null) {
            prop = chatProp.addEmptyChannelInfo(channel);
        }
        ChatItemProp itemProp = prop.getItem().get(id);
        if (itemProp == null) {
            itemProp = prop.addEmptyItem(id);
        }
        long oldIndex = itemProp.getReadIndex();
        if (index > itemProp.getReadIndex()) {
            itemProp.setReadIndex(index);
        }
        itemProp.setIgnoreCount(0);
        LOGGER.info("{} readMessage chatChannel:{}, id:{}, index:{} old:{}", ownerActor().getPlayerId(), chatSession.getChannelType(), id, index, oldIndex);
    }

    /**
     * 设置拉取的起始游标
     */
    public void setStartIndex(CommonEnum.ChatChannel chatChannel, String id, long index) {
        int channel = chatChannel.getNumber();
        ChatPlayerProp chatProp = getChatProp();
        ChannelInfoProp channelProp = chatProp.getChannelInfo().get(channel);
        if (channelProp == null) {
            channelProp = chatProp.addEmptyChannelInfo(channel);
        }
        ChatItemProp itemProp = channelProp.getItem().get(id);
        if (itemProp == null) {
            itemProp = channelProp.addEmptyItem(id);
        }
        itemProp.setStartIndex(index).setReadIndex(index);
        LOGGER.info("{} setStartIndex chatChannel:{}, id:{}, index:{}", ownerActor().getPlayerId(), chatChannel, id, index);
    }

    public long getQueryStartIndex(CommonEnum.ChatChannel chatChannel, String id) {
        ChannelInfoProp prop = getChatProp().getChannelInfo().get(chatChannel.getNumber());
        if (prop == null) {
            return 0;
        }
        ChatItemProp itemProp = prop.getItemV(id);
        if (itemProp == null) {
            return 0;
        }
        return itemProp.getStartIndex();
    }

    /**
     * 在确定应存在chatChannel的情况下使用，不存在则应该记录错误，表示逻辑出现了问题
     *
     * @param chatChannel 聊天频道。
     * @param chatId      聊天id。
     */
    public void clearChannelData(final CommonEnum.ChatChannel chatChannel, final String chatId) {
        if (chatChannel == null) {
            LOGGER.error("clearChannelData fail, param chatChannel is null, chatId={}", chatId);
            return;
        }
        if (StringUtils.isEmpty(chatId)) {
            LOGGER.error("clearChannelData fail, param chatId is null, chatChannel={}", chatChannel);
            return;
        }
        final ChannelInfoProp channelInfoProp = getChatProp().getChannelInfo().get(chatChannel.getNumber());
        if (channelInfoProp == null) {
            LOGGER.info("clearChannelData, chatChannel={}, chatId={}, already done", chatChannel, chatId);
            return;
        }

        channelInfoProp.getItem().remove(chatId);
        LOGGER.info("clearChannelData chatChannel={}, chatId={}", chatChannel, chatId);
    }

    /**
     * 获取chatItem，如果不存在返回null
     */
    public ChatItemProp getChatItemOrNull(CommonEnum.ChatChannel chatChannel, String id) {
        ChatPlayerProp prop = getChatProp();
        ChannelInfoProp channelInfoProp = prop.getChannelInfoV(chatChannel.getNumber());
        if (channelInfoProp == null) {
            return null;
        }
        return channelInfoProp.getItemV(id);
    }

    /**
     * 获取chatItem，如果不存在则创建
     */
    public ChatItemProp getChatItemOrCreate(CommonEnum.ChatChannel chatChannel, String id) {
        ChatPlayerProp prop = getChatProp();
        ChannelInfoProp channelInfoProp = prop.getChannelInfoV(chatChannel.getNumber());
        if (channelInfoProp == null) {
            channelInfoProp = prop.addEmptyChannelInfo(chatChannel.getNumber());
        }
        ChatItemProp chatItemProp = channelInfoProp.getItemV(id);
        if (chatItemProp == null) {
            chatItemProp = channelInfoProp.addEmptyItem(id);
            LOGGER.info("{} addChannelData chatChannel:{}, id:{}", ownerActor().getPlayerId(), chatChannel, id);
        }
        return chatItemProp;
    }

    /**
     * 获取chatItem，如果不存在则报错
     */
    public ChatItemProp getChatItemOrException(CommonEnum.ChatChannel chatChannel, String id) {
        ChatPlayerProp prop = getChatProp();
        ChannelInfoProp channelInfoProp = prop.getChannelInfoV(chatChannel.getNumber());
        if (channelInfoProp == null) {
            throw new GeminiException(ErrorCode.CHAT_CHANNEL_NOT_EXIST);
        }
        ChatItemProp chatItemProp = channelInfoProp.getItemV(id);
        if (chatItemProp == null) {
            throw new GeminiException(ErrorCode.CHAT_ID_NOT_EXIST);
        }
        return chatItemProp;
    }

    /**
     * 删除prepareItem
     */
    public PrepareChatItemProp removePrepareItem(String id) {
        ChatPlayerProp prop = getChatProp();
        ChannelInfoProp channelInfoProp = prop.getChannelInfoV(CommonEnum.ChatChannel.CC_GROUP_VALUE);
        if (channelInfoProp == null) {
            return null;
        }
        return channelInfoProp.getPrepareItem().remove(id);
    }

    /**
     * 获取聊天记录
     */
    public GeneratedMessageV3 getChatMessages(PlayerChat.Player_GetChatMessages_C2S msg) {
        final CommonEnum.ChatChannel chatChannel = msg.getChatSession().getChannelType();
        final String channelId = msg.getChatSession().getChatChannelId();

        var isInvalid = !msg.getIsLogin() && (msg.getFromId() < 0 || msg.getToId() < 0);
        if (isInvalid) {
            throw new GeminiException(ErrorCode.CHAT_MESSAGE_ID_INVALID, "fromId, toId negative");
        }
        if (msg.getFromId() < msg.getToId()) {
            throw new GeminiException(ErrorCode.CHAT_MESSAGE_ID_INVALID, "fromId < toId");
        }
        if (msg.getFromId() - msg.getToId() > getConstTemplate().getChatReqMessageNumber()) {
            throw new GeminiException(ErrorCode.CHAT_MESSAGE_ID_INVALID, "chat id too much");
        }

        final ChatPlayerProp prop = ownerActor().getOrLoadChatPlayerEntity().getProp();
        final ChannelInfoProp channelInfoProp = prop.getChannelInfoV(chatChannel.getNumber());
        if (channelInfoProp == null) {
            throw new GeminiException(ErrorCode.CHAT_CHANNEL_NOT_EXIST);
        }

        final ChatItemProp chatItemProp = channelInfoProp.getItemV(channelId);
        if (chatItemProp == null) {
            throw new GeminiException(ErrorCode.CHAT_ID_NOT_EXIST);
        }

        // 获取玩家的屏蔽列表，获取的聊天不能包含被屏蔽的人
        final FriendPlayerEntity friendPlayerEntity = ownerActor().getOrLoadFriendPlayerEntity();
        final Collection<Long> shieldList = friendPlayerEntity.getProp().getShiledList().keySet();
        final List<CommonMsg.ChatMessage> chatMsgs = this.chatHandlerOf(chatChannel).queryChatMsgList(getOwner(), channelId, shieldList, msg);

        LOGGER.debug("player={} query chat msg ans={}", getOwner(), chatMsgs);
        final PlayerChat.Player_GetChatMessages_S2C.Builder builder = PlayerChat.Player_GetChatMessages_S2C.newBuilder();
        builder.setChatSession(msg.getChatSession())
                .addAllMsgs(chatMsgs);

        final boolean isTargetChannel = chatChannel == CommonEnum.ChatChannel.CC_SERVER || chatChannel == CommonEnum.ChatChannel.CC_CLAN;
        if (isTargetChannel && msg.getIsLogin() && chatMsgs.size() < ChatConstants.PAGE_MSG_SIZE) {
            long maxIndex = 0;
            int unreadMsg = 0;
            for (CommonMsg.ChatMessage message : chatMsgs) {
                if (maxIndex < message.getMessageId()) {
                    maxIndex = message.getMessageId();
                }
                if (message.getMessageId() > chatItemProp.getReadIndex()) {
                    unreadMsg++;
                }
            }
            int ignoreNum = (int) (maxIndex - chatItemProp.getReadIndex() - unreadMsg);
            LOGGER.info("ChatPlayerHandleChatComponent getChatMessage, maxIndex={}, readIndex={}, unreadNUm={}, ignoreNum={}",
                    maxIndex, chatItemProp.getReadIndex(), unreadMsg, ignoreNum);
            chatItemProp.setIgnoreCount(max(ignoreNum, 0));
        }

        return builder.build();
    }

    /**
     * @return 聊天是否被封禁
     */
    public boolean isBanned() {
        return getChatProp().getBanTsMs() > SystemClock.now();
    }


    /**
     * 发聊天
     */
    public void chatRequest(
            final CommonMsg.ChatSession chatSession,
            final CommonEnum.MessageType messageType,
            final CommonMsg.MessageData messageData, Consumer<Throwable> onComplete
    ) {
        if (messageType == CommonEnum.MessageType.MT_PLAYER) {
            if (!isMsgValid(messageData.getMsg())) {
                throw new GeminiException(ErrorCode.CHAT_MESSAGE_INVALID);
            }
        }
        final CommonEnum.ChatChannel chatChannel = chatSession.getChannelType();
        final boolean cdSwitch = ServerContext.getServerDebugOption().isChatNoCd();
        // 是否无视cd
        if (!cdSwitch) {
            // 进来了，代表需要判cd
            if (chatCd.getOrDefault(chatChannel, 0L) > SystemClock.now()) {
                LOGGER.warn("{} next can send msg in channel{} is {}", getOwner().getEntityId(), chatSession.getChannelType(), chatCd.getOrDefault(chatChannel, 0L));
                throw new GeminiException(ErrorCode.CHAT_IN_CD);
            }
        }
        // 封禁应该直接在客户单拦截了 然后弹本地缓存系统消息 服务器不处理
        if (isBanned()) {
            LOGGER.warn("{} is in banChat", getOwner());
            throw new GeminiException(ErrorCode.IN_BAN_CHAT_STATE);
        }
        PlayerEntity playerEntity = ownerActor().getOrLoadEntity();
        CommonMsg.MessageData.Builder filteredMsgData = messageData.toBuilder();
        final boolean isTextFilter = ServerContext.getServerDebugOption().isTextFilter();
        if (isTextFilter && messageType == CommonEnum.MessageType.MT_PLAYER) {
            // 敏感词
            SsTextFilter.CheckTextAns checkTextAns = playerEntity.syncCheckText(filteredMsgData.getMsg(), CommonEnum.UgcSceneId.USI_CHAT);
            filteredMsgData.setMsg(checkTextAns.getFilteredText());
        }
        // 分享邮件，内容读db
        if (messageType == CommonEnum.MessageType.MT_MAIL_SHARE) {
            formShareMailMessageData(filteredMsgData);
        }
        // 构建消息
        CommonMsg.ChatMessage.Builder messageBuilder = playerEntity.getProfileComponent().buildChatSender();
        messageBuilder.setMessageData(filteredMsgData).setType(messageType);
        ChatHandler chatHandler = chatHandlerOf(chatChannel);
        chatCd.put(chatChannel, SystemClock.now() + chatHandler.getCd(getOwner()));
        LOGGER.debug("ChatLog-ChatRequest: player{} chatRequest with chatSession={}", playerEntity.getPlayerId(), chatSession);
        if (chatHandler.isSyncModel()) {
            chatHandler.chatRequestSync(getOwner(), chatSession.getChatChannelId(), messageBuilder.build());
            onComplete.accept(null);
        } else {
            chatHandler.chatRequestAsync(getOwner(), chatSession.getChatChannelId(), messageBuilder.build(), onComplete);
        }
    }

    /**
     * 构建分享邮件聊天消息
     *
     * @param msgBuilder 原聊天消息
     */
    private void formShareMailMessageData(final CommonMsg.MessageData.Builder msgBuilder) {
        final long mailId = msgBuilder.getSharedMail().getMailId();
        LOGGER.info("formShareMailMessageData load mailContent, mailId={}", mailId);
        final TcaplusDb.PlayerMailTable.Builder mailBuilder = TcaplusDb.PlayerMailTable.newBuilder().setPlayerId(this.getOwner().getEntityId()).setMailId(mailId);
        GetOption option = GetOption.newBuilder().withGetAllFields(false).withFieldNames(Lists.newArrayList("Content")).build();
        SelectUniqueAsk<TcaplusDb.PlayerMailTable.Builder> selectUniqueAsk = new SelectUniqueAsk<>(mailBuilder, option);
        final GetResult<TcaplusDb.PlayerMailTable.Builder> result;
        try {
            result = this.ownerActor().callGameDb(selectUniqueAsk);
        } catch (Exception e) {
            throw new GeminiException("formShareMailMessageData load mailContent fail", e, ErrorCode.CHAT_DB_OPERATION_FAILED.getCodeId());
        }
        if (!result.isOk()) {
            // 数据不存在不应打error
            if (result.isRecordNotExist()) {
                LOGGER.info("formShareMailMessageData load mailContent code={}, mailId={}", result.getCode(), mailId);
            } else {
                LOGGER.error("formShareMailMessageData load mailContent code={}, mailId={}", result.getCode(), mailId);
            }

            throw new GeminiException(ErrorCode.CHAT_DB_OPERATION_FAILED);
        }
        try {
            msgBuilder.getSharedMailBuilder().getContentBuilder()
                    .mergeFrom(result.value.getContent().toByteArray());
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("formShareMailMessageData mailContent db data {} to pb data fail", result.value.getContent(), e);
            msgBuilder.getSharedMailBuilder().setContent(StructMailPB.MailContentPB.getDefaultInstance());
        }
    }

    /**
     * 创建群聊
     */
    public void createGroupChat(int seqId, List<Long> players, String name) {
        GroupChatHandler handler = (GroupChatHandler) chatHandlerOf(CommonEnum.ChatChannel.CC_GROUP);
        handler.createChatGroup(ownerActor(), seqId, players, name);
    }

    /**
     * 发送系统消息
     * 注意 私聊是异步模式的 如果后续加私聊系统消息 要改
     */
    public long sendSystemChat(CommonEnum.ChatChannel chatChannel, String channelId, CommonMsg.MessageData data) {
        CommonMsg.ChatMessage.Builder messageBuilder = CommonMsg.ChatMessage.newBuilder();
        messageBuilder.setChatTimestamp(SystemClock.now()).setMessageData(data).setType(CommonEnum.MessageType.MT_SYSTEM);
        return chatHandlerOf(chatChannel).chatRequestSync(getOwner(), channelId, messageBuilder.build());
    }

    /**
     * 禁言
     */
    public void banChat(long banEndTsMs) {
        getChatProp().setBanTsMs(banEndTsMs);
        LOGGER.info("{} banChat banEndTsMs: {}", ownerActor().getPlayerId(), banEndTsMs);
    }

    /**
     * 解除禁言
     */
    public void cancelBanChat() {
        // 将禁言时间戳设为0
        getChatProp().setBanTsMs(0);
        LOGGER.info("{} cancelBanChat cancelBanChatTsMs: {}", ownerActor().getPlayerId(), SystemClock.now());
    }

    private ChatPlayerProp getChatProp() {
        return getOwner().getProp();
    }

    private boolean isMsgValid(String msg) {
        // 检查字符长度
        return StringUtils.isNotEmpty(msg)
                && msg.length() <= getConstTemplate().getCharacterLength();
    }

    public void reCalServerChannelCd() {
        int mainCityLevel = ownerActor().getOrLoadEntity().getCityLevel();
        if (mainCityLevel == getConstTemplate().getNewbieStandard()) {
            Long old = chatCd.getOrDefault(CommonEnum.ChatChannel.CC_SERVER, 0L);
            int delta = getConstTemplate().getNewbieChatCD() - getConstTemplate().getChatCD();
            chatCd.put(CommonEnum.ChatChannel.CC_SERVER, old - TimeUtils.second2Ms(delta));
        }
    }

    public long getMuteEndTsMs() {
        return getChatProp().getBanTsMs();
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 总战力提升（只积累增长值，不扣除降低值）
 * param1: 战力变更值
 *
 * <AUTHOR>
 */
public class TotalPowerIncreaseChecker extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int powerConfig = taskParams.get(0);

        if (event instanceof PlayerPowerIncreaseEvent) {
            PlayerPowerIncreaseEvent realEvent = (PlayerPowerIncreaseEvent) event;
            if (prop.getProcess() < powerConfig) {
                long l = prop.getProcess() + realEvent.getPower();
                prop.setProcess(Math.min(powerConfig, (int) l));
            }
        }
        return prop.getProcess() >= powerConfig;
    }
}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.MathUtils;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

/**
 * VIP经验道具
 *
 * <AUTHOR>
 */
public class VipExpItem extends AbstractUsableItem {

    public VipExpItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (num <= 0) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        int eachItemEffectValue = getTemplate().getEffectValue();
        if (eachItemEffectValue <= 0) {
            throw new GeminiException(ErrorCode.ITEM_USE_FAIL);
        }
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        // 可能为 INT_MAX 交给下层去处理了
        int count = MathUtils.multiplyExact(getTemplate().getEffectValue(), num);
        playerEntity.getVipComponent().addVipExp(count, "use_item_get_vip_exp");
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

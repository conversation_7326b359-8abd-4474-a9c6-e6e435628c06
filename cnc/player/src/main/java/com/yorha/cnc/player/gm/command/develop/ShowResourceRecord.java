package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerDataRecordComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 发送邮件告诉资源统计项
 * 包括：资源采集积分、资源援助积分、联盟帮助次数
 *
 * <AUTHOR>
 */
public class ShowResourceRecord implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(ShowResourceRecord.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity playerEntity = actor.getOrLoadEntity();
        PlayerDataRecordComponent component = playerEntity.getDataRecordComponent();
        Map<String, String> records = new HashMap<>();
        for (CommonEnum.DataRecordType type : CommonEnum.DataRecordType.values()) {
            records.put(type.name(), String.valueOf(component.getRecordValue(type)));
        }
        Map<Integer, Struct.SingleStatistic> resourceCollect = playerEntity.getStatisticComponent().getAllSecondStatistic(StatisticEnum.RESBUILDING_COLLECT_RESOURCE_NUM);
        records.put("resource_collect", resourceCollect.toString());

        Map<Integer, Struct.SingleStatistic> resourceAssist = playerEntity.getStatisticComponent().getAllSecondStatistic(StatisticEnum.RESOURCE_ASSIST_NUM);
        records.put("resource_assist", resourceAssist.toString());
        LOGGER.info("ShowResourceRecord records={}", records);

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getPersonalMailId());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(JsonUtils.toJsonString(records)));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("ShowResourceRecord")
                .setSubTitle("ShowResourceRecord");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(playerId)
                .setZoneId(actor.getZoneId())
                .build();
        actor.getEntity().getMailComponent().sendPersonalMail(receiver, builder.build());
    }
}

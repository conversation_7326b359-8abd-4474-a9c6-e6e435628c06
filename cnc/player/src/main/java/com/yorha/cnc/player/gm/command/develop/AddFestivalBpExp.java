package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.activity.unit.PlayerFestivalBpUnit;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 加bp exp
 *
 * <AUTHOR>
 */
public class AddFestivalBpExp implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int actId = Integer.parseInt(args.getOrDefault("actId", "0"));
        int count = Integer.parseInt(args.getOrDefault("count", "0"));
        PlayerFestivalBpUnit unit = actor.getEntity().getActivityComponent().checkedGetUnit(PlayerFestivalBpUnit.class, actId, 1);
        if (unit == null) {
            return;
        }
        unit.gmAddScore(count);
    }

    @Override
    public String showHelp() {
        return "AddFestivalBpExp actId={value} count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class GetDiscountStore implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GetDiscountStore.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        DiscountStoreDTO dto = actor.getEntity().getDiscountStoreComponent().getDiscountStoreInfo();
        LOGGER.info("{}", dto);
    }

    @Override
    public String showHelp() {
        return "GetDiscountStore";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

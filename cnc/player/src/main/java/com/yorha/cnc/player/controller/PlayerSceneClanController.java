package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.ClanOperationType;
import com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S;
import com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C;
import com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S;
import com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;

import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_CLAN;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_SCENE_CLAN)
public class PlayerSceneClanController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSceneClanController.class);

    /**
     * 拉取联盟领土页面数据
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANTERRITORYPAGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanTerritoryPage_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        PlayerClan.Player_FetchClanTerritoryPage_S2C.Builder retBuilder = PlayerClan.Player_FetchClanTerritoryPage_S2C.newBuilder();
        if (clanId == 0) {
            return retBuilder.build();
        }
        SsClanTerritory.FetchTerritoryPageAllAsk ask = SsClanTerritory.FetchTerritoryPageAllAsk.newBuilder().build();
        SsClanTerritory.FetchTerritoryPageAllAns ans = playerEntity.ownerActor().callCurClan(ask);
        if (!ans.hasPage()) {
            return retBuilder.build();
        }
        retBuilder.setClanTerritoryPage(ans.getPage());
        return retBuilder.build();
    }

    /**
     * 放弃联盟据点
     */
    @CommandMapping(code = MsgType.PLAYER_ABANDONMAPBUILDING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_AbandonMapBuilding_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        // 检查是否有放弃据点的权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_ABANDON_TERRITROY, staffId);
        // 去场景放弃
        SsSceneClan.AbandonClanMapBuildingAsk.Builder ask = SsSceneClan.AbandonClanMapBuildingAsk.newBuilder();
        playerEntity.ownerActor().callBigScene(ask.setClanId(clanId).setPlayerId(playerEntity.getEntityId()).setMapBuildingId(msg.getEntityId()).build());
        return PlayerClan.Player_AbandonMapBuilding_S2C.getDefaultInstance();
    }

    /**
     * 【点开地图建筑】时检查是否可以免费放置
     */
    @CommandMapping(code = MsgType.PLAYER_CHECKCANFREEREBUILD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CheckCanFreeRebuild_C2S msg) {
        // 目前客户端点开所有地图建筑都会发这个消息，这里不能返回给客户端错误码（不能阻断不在军团的玩家点击）
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            LOGGER.warn("shouldn't check can free rebuild when clicking map building while player {} not in clan", playerEntity.getPlayerId());
            return PlayerClan.Player_CheckCanFreeRebuild_S2C.newBuilder().setCanFreeRebuild(false).build();
        }
        SsSceneClan.CheckCanFreeRebuildAsk.Builder ask = SsSceneClan.CheckCanFreeRebuildAsk.newBuilder();
        ask.setClanId(playerEntity.getProp().getClan().getClanId());
        SsSceneClan.CheckCanFreeRebuildAns ans = playerEntity.ownerActor().callBigScene(ask.build());

        return PlayerClan.Player_CheckCanFreeRebuild_S2C.newBuilder().setCanFreeRebuild(ans.getCanFreeRebuild()).setMainBaseNum(ans.getMainBaseNum()).build();
    }

    /**
     * 【移动地图建筑】时检查是否可以放置（阻挡半径内军队，玩家主城，判定是否可以放置）
     */
    @CommandMapping(code = MsgType.PLAYER_VERIFYREBUILDMAPBUILDING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_VerifyRebuildMapBuilding_C2S msg) {
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            LOGGER.warn("shouldn't check can free rebuild when moving map building while player {} not in clan", playerEntity.getPlayerId());
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        playerEntity.getPlayerClanComponent().checkMapBuildingTypeParam(msg.getType());
        // 到scene上验证是否可重建
        SsSceneClan.VerifyCanRebuildAns ans =
                playerEntity.getPlayerClanComponent().verifyCanRebuild(playerEntity.getPlayerId(), msg.getMapBuildingId(), msg.getType(), false);

        PlayerClan.Player_VerifyRebuildMapBuilding_S2C.Builder retBuilder = PlayerClan.Player_VerifyRebuildMapBuilding_S2C.newBuilder();
        if (!ans.getCanRebuild()) {
            retBuilder.setReason(ans.getReason());
        }
        return retBuilder.setCanRebuild(ans.getCanRebuild()).build();
    }

    /**
     * 拉取联盟成员城池数据
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANMEMBERCITY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanMemberCity_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        PlayerClan.Player_FetchClanMemberCity_S2C.Builder ret = PlayerClan.Player_FetchClanMemberCity_S2C.newBuilder();
        if (clanId == 0) {
            ret.setVersion(0);
            return ret.build();
        }
        SsSceneMap.FetchClanCityPointListAsk.Builder call = SsSceneMap.FetchClanCityPointListAsk.newBuilder();
        call.setPlayerId(playerEntity.getEntityId()).setVersion(msg.getVersion());
        SsSceneMap.FetchClanCityPointListAns onCall = playerEntity.ownerActor().callBigScene(call.build());
        ret.setVersion(onCall.getVersion()).getOwnerPosBuilder().setX(onCall.getOwnerPos().getX()).setY(onCall.getOwnerPos().getY());
        for (Struct.Point p : onCall.getPointListList()) {
            ret.getPointListBuilder().addDatas(StructPB.PointPB.newBuilder().setX(p.getX()).setY(p.getY()).build());
        }
        return ret.build();
    }


    /**
     * 查看单个联盟成员的位置
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANSINGLEMEMBERCITY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanSingleMemberCity_C2S msg) {
        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0L) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        SsSceneMap.FetchSingleClanMemberCityPointAsk.Builder ask = SsSceneMap.FetchSingleClanMemberCityPointAsk.newBuilder();
        ask.setFetchPlayerId(msg.getMemberPlayerId()).setPlayerId(playerEntity.getPlayerId()).setOnlyFetchOwner(msg.getOnlyFetchOwner());
        SsSceneMap.FetchSingleClanMemberCityPointAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        PlayerClan.Player_FetchClanSingleMemberCity_S2C.Builder ret = PlayerClan.Player_FetchClanSingleMemberCity_S2C.newBuilder();
        Struct.Point p = ans.getMemberPos();
        ret.setMemberCityPos(StructPB.PointPB.newBuilder().setX(p.getX()).setY(p.getY()));
        return ret.build();
    }

    /**
     * 拉取势力图数据
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANTERRITORYMAP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanTerritoryMap_C2S msg) {
        if (msg.getZoneId() == 0) {
            msg = msg.toBuilder().setZoneId(playerEntity.getZoneId()).build();
        }
        SsSceneMap.FetchTerritoryMapAsk.Builder call = SsSceneMap.FetchTerritoryMapAsk.newBuilder().setVersion(msg.getVersion());
        SsSceneMap.FetchTerritoryMapAns ans = playerEntity.ownerActor().callTargetBigScene(msg.getZoneId(), call.build());
        PlayerClan.Player_FetchClanTerritoryMap_S2C.Builder ret = PlayerClan.Player_FetchClanTerritoryMap_S2C.newBuilder();
        ret.setVersion(ans.getVersion()).setZoneId(msg.getZoneId());
        if (!ans.getDelClanIdList().isEmpty()) {
            ret.getDelClanIdBuilder().addAllDatas(ans.getDelClanIdList());
        }
        ret.getMapItemBuilder().addAllDatas(ans.getMapItemList());
        return ret.build();
    }

    /**
     * 改建地缘建筑为联盟建筑
     */
    @CommandMapping(code = MsgType.PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ConstructClanStrongholdBuilding_C2S msg) {
        playerEntity.getPlayerClanComponent().tryRebuild(msg);
        return PlayerClan.Player_ConstructClanStrongholdBuilding_S2C.getDefaultInstance();
    }

    /**
     * 拆除联盟建筑
     */
    @CommandMapping(code = MsgType.PLAYER_DESTROYCLANSTRONGHOLDBUILDING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_DestroyClanStrongholdBuilding_C2S msg) {
        playerEntity.getSettingComponent().checkSpassword(SPWC_CLAN, 0, msg.getSPassWord());
        long clanId = playerEntity.getProp().getClan().getClanId();
        // 判断是否加入联盟
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 检查改建地图建筑类型
        playerEntity.getPlayerClanComponent().checkMapBuildingTypeParam(msg.getType());
        // 检查是否有拆除军团建筑的权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_DESTROY_CLAN_BUILDING, staffId);

        // 真正到scene执行拆除建筑的逻辑
        SsSceneClan.DestroyClanBuildingAsk.Builder ask = SsSceneClan.DestroyClanBuildingAsk.newBuilder();
        ask.setClanId(clanId).setMapBuildingId(msg.getMapBuildingId()).setPlayerName(playerEntity.getName());
        playerEntity.ownerActor().callBigScene(ask.build());

        // 构建回包
        PlayerClan.Player_DestroyClanStrongholdBuilding_S2C.Builder retBuilder = PlayerClan.Player_DestroyClanStrongholdBuilding_S2C.newBuilder();
        return retBuilder.build();
    }

    /**
     * 对军团建筑进行灭火操作
     * 灭火有两种方式：
     * 1、使用军团资源灭火，需要拥有灭火权限；
     * 2、使用个人资源灭火，只要有摩拉(原神货币)就行
     * 无论以何种方式灭火，均会提前到scene上做检查，这个检查可能会失效，导致出现资源多扣除的情况
     */
    @CommandMapping(code = MsgType.PLAYER_EXTINGUISHBUILDINGFIRE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ExtinguishBuildingFire_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        // 判断是否加入联盟
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 检查参数
        if (msg.getMethod() != CommonEnum.ExtinguishMethod.EXM_CLAN_MONEY && msg.getMethod() != CommonEnum.ExtinguishMethod.EXM_DIAMOND) {
            LOGGER.error("wrong method {} have been sent", msg.getMethod());
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // 先到scene上检查是否可以灭火
        SsSceneClan.VerifyCanExtinguishAsk.Builder ask = SsSceneClan.VerifyCanExtinguishAsk.newBuilder();
        ask.setClanId(playerEntity.getProp().getClan().getClanId())
                .setMapBuildingId(msg.getMapBuildingId());
        SsSceneClan.VerifyCanExtinguishAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        if (!ans.getCanExtinguish()) {
            throw new GeminiException(ErrorCode.MAP_BUILDING_IS_BEING_MANIPULATED);
        }

        if (msg.getMethod() == CommonEnum.ExtinguishMethod.EXM_CLAN_MONEY) {
            // 第一种方式：使用军团资源灭火
            int staffId = playerEntity.getPlayerClanComponent().getStaffId();
            // 检查玩家权限
            ClanPermissionUtils.checkPermission(ClanOperationType.COT_EXTINGUISH, staffId);
            // 到军团上检查资源并扣除资源
            SsClanTerritory.CheckExtinguishClanBuildingAsk.Builder checkCall = SsClanTerritory.CheckExtinguishClanBuildingAsk.newBuilder();
            checkCall.setPlayerId(playerEntity.getEntityId()).setRebuildingType(msg.getType())
                    .setCardHead(playerEntity.getCardHead().getCopySsBuilder())
                    .setBuildingTemplateId(ans.getBuildingTemplateId());
            checkCall.setCardHead(playerEntity.getCardHead().getCopySsBuilder());
            playerEntity.ownerActor().callCurClan(checkCall.build());
        } else {
            // 第二种方式：使用个人资源灭火
            ConstClanTerritoryTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
            int extinguishNeedDiamond = constTemplate.getExtinguishNeedDiamond();
            AssetPackage realConsume = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND, extinguishNeedDiamond).build();
            playerEntity.verifyThrow(realConsume);
            playerEntity.consume(realConsume, CommonEnum.Reason.ICR_EXTINGUISH);
        }
        try {
            // 执行真正的灭火逻辑
            SsSceneClan.ExtinguishBuildingFireAsk.Builder extinguishAsk = SsSceneClan.ExtinguishBuildingFireAsk.newBuilder();
            extinguishAsk.setClanId(clanId).setMapBuildingId(msg.getMapBuildingId());
            playerEntity.ownerActor().callBigScene(extinguishAsk.build());
        } catch (Exception e) {
            // 灭火失败，wechat告警，特别注意：此时资源已经扣除了
            WechatLog.error("extinguish fire failed, maybe need manual return: use method {}, exception is {}", msg.getMethod(), e);
            if (e instanceof GeminiException) {
                throw e;
            }
        }
        PlayerClan.Player_ExtinguishBuildingFire_S2C.Builder retBuilder = PlayerClan.Player_ExtinguishBuildingFire_S2C.newBuilder();
        return retBuilder.build();
    }


    /**
     * 耐久度恢复时拉取联盟建筑hp
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANBUILDINGHP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanBuildingHp_C2S msg) {
        SsSceneClan.FetchClanBuildingHpAsk.Builder ask = SsSceneClan.FetchClanBuildingHpAsk.newBuilder();
        ask.setMapBuildingId(msg.getMapBuildingId());
        SsSceneClan.FetchClanBuildingHpAns ans = playerEntity.ownerActor().callBigScene(ask.build());

        PlayerClan.Player_FetchClanBuildingHp_S2C.Builder builder = PlayerClan.Player_FetchClanBuildingHp_S2C.newBuilder();
        builder.setCurrentHp(ans.getCurHp()).setMaxHp(ans.getMaxHp());
        return builder.build();
    }

    /**
     * 放置军团资源中心
     */
    @CommandMapping(code = MsgType.PLAYER_CONSTRUCTCLANRESBUILDING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ConstructClanResBuilding_C2S msg) {
        playerEntity.getPlayerClanComponent().tryPlaceClanResBuild(msg);
        return Player_ConstructClanResBuilding_S2C.getDefaultInstance();
    }

    /**
     * 获取军团资源中心信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANRESBUILDINGINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FetchClanResBuildingInfo_C2S msg) {
        // 检查参数是否齐全
        if (!msg.hasTargetId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        SsSceneClan.FetchClanResBuildSimpleInfoAsk.Builder call = SsSceneClan.FetchClanResBuildSimpleInfoAsk.newBuilder();
        call.setTargetId(msg.getTargetId()).setPlayerId(playerEntity.getPlayerId());
        SsSceneClan.FetchClanResBuildSimpleInfoAns ans = playerEntity.ownerActor().callBigScene(call.build());

        // 到scene上获取资源中心信息
        Player_FetchClanResBuildingInfo_S2C.Builder retMsg = Player_FetchClanResBuildingInfo_S2C.newBuilder();
        if (ans.hasDisappearTsMs()) {
            retMsg.setDisappearTsMs(ans.getDisappearTsMs());
        }
        if (ans.hasProgressInfo()) {
            StructCommon.ProgressInfo progressInfo = ans.getProgressInfo();
            StructCommonPB.ProgressInfoPB.Builder progressInfoPb = StructCommonPB.ProgressInfoPB.newBuilder();
            progressInfoPb.setUid(progressInfo.getUid())
                    .setStateStartTsMs(progressInfo.getStateStartTsMs())
                    .setStateEndTsMs(progressInfo.getStateEndTsMs())
                    .setSpeed(progressInfo.getSpeed())
                    .setLastCalTsMs(progressInfo.getLastCalTsMs())
                    .setLastCalNum(progressInfo.getLastCalNum())
                    .setMaxNum(progressInfo.getMaxNum());
            retMsg.setCollectInfo(progressInfoPb);
        }
        return retMsg.build();
    }

    /**
     * 关闭军团推送界面
     */
    @CommandMapping(code = MsgType.PLAYER_CLOSECLANRECOMMEND_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerScene.Player_CloseClanRecommend_C2S msg) {
        // 关闭推送界面时才刷新推荐任务
        playerEntity.getPlayerClanComponent().refreshClanRecommendTask();
        PlayerScene.Player_CloseClanRecommend_S2C.Builder builder = PlayerScene.Player_CloseClanRecommend_S2C.newBuilder();
        return builder.build();
    }
}

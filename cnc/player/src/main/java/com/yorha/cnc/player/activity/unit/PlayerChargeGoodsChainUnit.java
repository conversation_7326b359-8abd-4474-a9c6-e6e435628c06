package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.goods.ActivityNormalGoods;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityChargeGoodsChainUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.GoodsChainInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.collections4.IterableUtils;
import res.template.ChargeChainTemplate;
import res.template.ChargeGoodsTemplate;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 礼包链活动
 * <p>
 * 由一个或多个礼包链组成，不同礼包链互不影响，单个礼包链内必须先购买上一个礼包才能购买下一个
 */
public class PlayerChargeGoodsChainUnit extends BasePlayerActivityUnit implements ActivityNormalGoods.ActUnitGoodsHandler {

    private final ImmutableList<Integer> goodsChainIds;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_CHARGE_GOODS_CHAIN, (owner, prop, template) ->
                new PlayerChargeGoodsChainUnit(
                        owner,
                        prop.getChargeGoodsChainUnit(),
                        ImmutableList.copyOf(template.getGoodsChainArrayList()))
        );
    }

    public PlayerChargeGoodsChainUnit(PlayerActivity activity, ActivityUnitProp unitProp, ImmutableList<Integer> goodsChainIds) {
        super(activity, unitProp);
        this.goodsChainIds = goodsChainIds;
    }

    private ActivityChargeGoodsChainUnitProp prop() {
        return unitProp.getChargeGoodsChain();
    }

    @Override
    public void load(boolean isInitial) {

    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public boolean isFinished() {
        return goodsChainIds.stream()
                .allMatch(chainId -> {
                    GoodsChainInfoProp chainInfo = prop().getChainsV(chainId);
                    if (chainInfo == null || chainInfo.getLastBoughtGoodsId() <= 0) {
                        return false;
                    }
                    ChargeChainTemplate chainTemplate = ResHolder.getTemplate(ChargeChainTemplate.class, chainId);
                    List<Integer> goodsIdList = chainTemplate.getChargeChainGoodsIdList();
                    Integer finalGoodsId = goodsIdList.getLast();
                    if (chainInfo.getLastBoughtGoodsId() == finalGoodsId) {
                        ChargeGoodsTemplate goodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, finalGoodsId);
                        return chainInfo.getLastGoodsBoughtTimes() >= goodsTemplate.getMaxPurchaseTimes();
                    }
                    return false;
                });
    }

    @Override
    public void forceOffImpl() {
    }

    @Override
    public void checkApply(ChargeGoodsTemplate goodsTemplate) {
        int goodsId = goodsTemplate.getId();

        Integer chainId = findChainId(goodsId);
        if (chainId == null) {
            throw new GeminiException(ErrorCode.ILLEGAL_GOODS_ID);
        }

        int shouldBuyGoodsId = curShouldBuyGoodsId(chainId);
        if (shouldBuyGoodsId <= 0 || goodsId != shouldBuyGoodsId) {
            throw new GeminiException(ErrorCode.ILLEGAL_GOODS_ID);
        }
    }

    @Nullable
    private Integer findChainId(int goodsId) {
        // 找到goodsId对应的chainId
        return IterableUtils.find(goodsChainIds, cid ->
                ResHolder.getTemplate(ChargeChainTemplate.class, cid).getChargeChainGoodsIdList().contains(goodsId)
        );
    }

    /**
     * @return -1 if no goods available
     */
    private int curShouldBuyGoodsId(int chainId) {
        GoodsChainInfoProp chainInfo = prop().getChainsV(chainId);
        ChargeChainTemplate chainTemplate = ResHolder.getTemplate(ChargeChainTemplate.class, chainId);
        List<Integer> chainGoodIds = chainTemplate.getChargeChainGoodsIdList();
        int shouldBuyGoodsId = chainGoodIds.getFirst();
        if (chainInfo != null && chainInfo.getLastBoughtGoodsId() > 0) {
            ChargeGoodsTemplate lastBoughtGoodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, chainInfo.getLastBoughtGoodsId());
            if (chainInfo.getLastGoodsBoughtTimes() >= lastBoughtGoodsTemplate.getMaxPurchaseTimes()) {
                // 当前礼包次数买尽才可以买下一个
                int index = chainGoodIds.indexOf(chainInfo.getLastBoughtGoodsId());
                if (index >= chainGoodIds.size() - 1) {
                    return -1;
                }
                shouldBuyGoodsId = chainGoodIds.get(index + 1);
            } else {
                shouldBuyGoodsId = chainInfo.getLastBoughtGoodsId();
            }
        }
        return shouldBuyGoodsId;
    }

    @Override
    public void onGoodsBought(int goodsId) {
        Integer chainId = findChainId(goodsId);
        if (chainId == null) {
            WechatLog.error("{} onGoodsBought chainId=null goodsId={}", player(), goodsId);
            return;
        }
        int shouldBuyGoodsId = curShouldBuyGoodsId(chainId);
        if (shouldBuyGoodsId != goodsId) {
            // should be impossible! unless config modified
            WechatLog.error("{} onGoodsBought shouldBuyGoodsId={} goodsId={}", player(), shouldBuyGoodsId, goodsId);
            // 不知道该怎么处理，别处理了
            return;
        }
        GoodsChainInfoProp chainInfo = prop().getChainsV(chainId);
        if (chainInfo == null) {
            chainInfo = prop().addEmptyChains(chainId).setChainId(chainId);
        }
        int times = chainInfo.getLastBoughtGoodsId() == goodsId ? chainInfo.getLastGoodsBoughtTimes() + 1 : 1;
        chainInfo.setLastBoughtGoodsId(goodsId)
                .setLastGoodsBoughtTimes(times);

        if (isFinished()) {
            onUnitFinished();
        }

    }
}

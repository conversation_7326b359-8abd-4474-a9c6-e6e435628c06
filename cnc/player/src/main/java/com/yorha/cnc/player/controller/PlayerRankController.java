package com.yorha.cnc.player.controller;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S;
import com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C;
import com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S;
import com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C;
import com.yorha.proto.SsRank.GetRankPageInfoAns;
import com.yorha.proto.SsRank.GetTopRankInfoAns;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RankTemplate;

/**
 * 排行榜相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_RANK)
public class PlayerRankController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerRankController.class);

    /**
     * 获取排行榜明细
     */
    @CommandMapping(code = MsgType.PLAYER_GETRANKPAGEINFO_C2S)
    public Player_GetRankPageInfo_S2C handle(PlayerEntity playerEntity, Player_GetRankPageInfo_C2S msg) {
        int page = msg.getPage();
        int rankId = msg.getRankId();
        GetRankPageInfoAns answer = playerEntity.getPlayerRankComponent().getRankInfoList(rankId, page);
        Player_GetRankPageInfo_S2C.Builder ret = Player_GetRankPageInfo_S2C.newBuilder();
        ret.addAllDto(answer.getDtoList());
        if (answer.hasMyRankInfoDTO()) {
            // 自己的信息在榜内
            ret.setMyRankInfoDTO(answer.getMyRankInfoDTO());
        } else {
            // 自己的信息在榜外
            ret.setMyRankInfoDTO(playerEntity.getPlayerRankComponent().getOutSideRankInfoDTO(rankId));
        }
        ret.setPage(page).setRankId(rankId).setTotal(answer.getTotal()).setMaxRank(answer.getMaxRank());
        return ret.build();
    }

    /**
     * 获取榜首信息 只有角色界面的常驻排行榜才用这个协议
     */
    @CommandMapping(code = MsgType.PLAYER_GETTOPRANKINFO_C2S)
    public Player_GetTopRankInfo_S2C handle(PlayerEntity playerEntity, Player_GetTopRankInfo_C2S msg) {
        Player_GetTopRankInfo_S2C.Builder ret = Player_GetTopRankInfo_S2C.newBuilder();
        for (int rankId : msg.getRankIdListList()) {
            RankTemplate template = ResHolder.getTemplate(RankTemplate.class, rankId);
            if (template == null) {
                LOGGER.error("rank id {} config is null, pls check the config", rankId);
                continue;
            }
            // 只有双常驻 才能拉
            if (!template.getIsResident()) {
                LOGGER.debug("rank id {} not need to send to client", rankId);
                continue;
            }
            GetTopRankInfoAns answer = playerEntity.getPlayerRankComponent().getTopRankInfo(rankId);
            if (answer.hasDto()) {
                ret.addDto(answer.getDto());
            }
        }
        return ret.build();
    }


}

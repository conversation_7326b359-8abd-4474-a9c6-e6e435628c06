package com.yorha.cnc.player.chat.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.io.MsgType;
import com.yorha.common.prop.PropNotifier;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.PlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ChatPlayerPropComponent extends ChatPlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(ChatPlayerPropComponent.class);

    private final ChatPlayerPropNotifier propNotifier;

    public ChatPlayerPropComponent(ChatPlayerEntity owner) {
        super(owner);
        this.propNotifier = new ChatPlayerPropNotifier();
    }

    @Override
    public void init() {
        getOwner().getProp().setListener(new CanStopPropertyChangeListener(this::flushProp, getOwner().ownerActor().self()));
    }

    @Override
    public void onLogin() {
        propNotifier.reset();
        // 登录的时候会全量下发一次prop, 之后增量下发
        flushProp();
    }

    public void immediateFlushProp() {
        // 主动下发属性同步
        this.flushProp();
    }

    public void onPlayerLogout() {
        this.propNotifier.reset();
    }

    private void flushProp() {
        if (getOwner().isDestroy()) {
            return;
        }
        if (getOwner().isStartZoneMigrate()) {
            // 移民中，拦截
            return;
        }
        // propNotifier自身是幂等的，对hasAnyMark有自己的控制
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());

        if (!getOwner().getProp().hasAnyMark()) {
            return;
        }
        this.getOwner().getDbComponent().saveChangeToDb();
        this.getOwner().getProp().unMarkAll();
    }

    class ChatPlayerPropNotifier extends PropNotifier<ChatPlayerProp, PlayerPB.PlayerEntityPB.Builder> {

        ChatPlayerPropNotifier() {
            super(PlayerPB.PlayerEntityPB.newBuilder());
        }

        @Override
        public boolean canNtfToClient() {
            if (getOwner().isDestroy()) {
                return false;
            }
            PlayerEntity playerEntity = ownerActor().getEntityOrNull();
            if (playerEntity == null) {
                return false;
            }
            if (!playerEntity.isOnline()) {
                return false;
            }
            return true;
        }

        @Override
        public boolean ntfToClient(Entity.EntityNtfMsg notify) {
            PlayerEntity playerEntity = ownerActor().getEntityOrNull();
            if (playerEntity == null) {
                return false;
            }
            LOGGER.debug("ChatLog : prop notify = {}", notify);
            playerEntity.sendMsgToClient(MsgType.ENTITYNTFMSG, notify);
            return true;
        }

        @Override
        public void fillEntityAttr(EntityAttrOuterClass.EntityAttr.Builder entityAttr, PlayerPB.PlayerEntityPB.Builder builder) {
            entityAttr.setEntityId(getEntityId())
                    .setEntityType(getEntityType())
                    .setPlayerAttr(builder.build());
        }

        @Override
        public int copyChange(ChatPlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            return prop.copyChangeToCs(changeCollector.getChatPlayerBuilder());
        }

        @Override
        public void copyFull(ChatPlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            prop.copyToCs(changeCollector.getChatPlayerBuilder());
        }

        @Override
        public void fillNewEntityNtf(Entity.EntityNtfMsg.Builder builder, EntityAttrOuterClass.EntityAttr.Builder entityAttr) {
            // chatPlayer以PlayerEntity的mod形式下发
            builder.addModEntities(entityAttr);
        }
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildCreateEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 拥有x个类型建筑
 * param1: 建筑类型
 * param2: 数量
 *
 * <AUTHOR>
 */
public class BuildTypeNumChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerInnerBuildLevelUpEvent.class.getSimpleName(),
            PlayerInnerBuildCreateEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer requireNum = taskParams.get(0);

        int curNum = event.getPlayer().getInnerBuildRhComponent().getInnerBuildTypeNum(true);
        prop.setProcess(Math.min(curNum, requireNum));

        return prop.getProcess() >= requireNum;
    }
}

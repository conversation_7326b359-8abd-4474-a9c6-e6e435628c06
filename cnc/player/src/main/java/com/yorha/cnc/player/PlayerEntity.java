package com.yorha.cnc.player;

import com.google.common.collect.Sets;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.MapEntry;
import com.yorha.cnc.player.component.*;
import com.yorha.cnc.player.enums.TextFilterMode;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.concurrent.IGeminiDispatcher;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.JolUtils;
import com.yorha.common.utils.TextFilterUtils;
import com.yorha.common.utils.jol.JolParser;
import com.yorha.common.utils.jol.TreeLayout;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.utils.time.schedule.ScheduleMgr;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerCardHeadProp;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.game.gen.prop.SingleStatisticProp;
import com.yorha.proto.*;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import io.netty.channel.Channel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openjdk.jol.info.GraphLayout;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class PlayerEntity extends AbstractEntity {
    private static final Logger LOGGER = LogManager.getLogger(PlayerEntity.class);
    private final PlayerActor actor;
    private final PlayerProp prop;
    /**
     * 是否正在注册
     */
    private boolean isRegister = false;

    /**
     * 理论上属于”公共“部分的对象其实都不需要计入Player占用内存
     */
    private static final Set<Class<?>> JOL_EXCLUDE = Sets.newHashSet(ScheduleMgr.class, Class.class, ThreadPoolExecutor.class,
            IGeminiDispatcher.class, Thread.class,
            // channel会引用到netty下层的一堆东西
            Channel.class,
            // 这个是Protobuf底层对结构体metadata的描述内容，有点大
            MapEntry.class,
            ActorSystem.class,
            ActorRunnable.class,
            ActorMsgEnvelope.class,
            Runnable.class
    );

    // -------------------------------- component --------------------------------
    private final PlayerPropComponent playerPropComponent = new PlayerPropComponent(this);
    private final PlayerSessionComponent sessionComponent = new PlayerSessionComponent(this);
    private final PlayerDbComponent dbComponent = new PlayerDbComponent(this);
    private final PlayerClanComponent clanComponent = new PlayerClanComponent(this);
    private final PlayerRankComponent rankComponent = new PlayerRankComponent(this);
    private final PlayerDiscountStoreComponent discountStoreComponent = new PlayerDiscountStoreComponent(this);
    private final PlayerClanStoreComponent clanStoreComponent = new PlayerClanStoreComponent(this);
    private final PlayerCommissariatStoreComponent commStoreComponent = new PlayerCommissariatStoreComponent(this);
    private final PlayerSettingComponent settingComponent = new PlayerSettingComponent(this);
    private final PlayerSceneMgrComponent sceneMgrComponent = new PlayerSceneMgrComponent(this);
    private final PlayerBattlePassComponent battlePassComponent = new PlayerBattlePassComponent(this);
    private final PlayerAntiAddictionComponent antiAddictionComponent = new PlayerAntiAddictionComponent(this);
    private final PlayerInnerBuildRhComponent innerBuildRhComponent = new PlayerInnerBuildRhComponent(this);
    private final PlayerCityCommandComponent cityCommandComponent = new PlayerCityCommandComponent(this);
    private final PlayerCampaignComponent campaignComponent = new PlayerCampaignComponent(this);
    // -------------------------------- 快捷方法 --------------------------------
    private final PlayerAutoAddMonsterComponent scheduleComponent = new PlayerAutoAddMonsterComponent(this);
    private final PlayerItemComponent itemComponent = new PlayerItemComponent(this);
    private final PlayerPurseComponent purseComponent = new PlayerPurseComponent(this);
    private final PlayerPlaneComponent planeComponent = new PlayerPlaneComponent(this);
    private final PlayerWallComponent wallComponent = new PlayerWallComponent(this);
    private final PlayerMailComponent mailComponent = new PlayerMailComponent(this);
    private final PlayerQueueTaskComponent playerQueueTaskComponent = new PlayerQueueTaskComponent(this);
    private final PlayerHospitalComponent playerHospitalComponent = new PlayerHospitalComponent(this);
    private final PlayerHeroComponent playerHeroComponent = new PlayerHeroComponent(this);
    private final PlayerSoldierComponent playerSoldierComponent = new PlayerSoldierComponent(this);
    private final PlayerPowerComponent playerPowerComponent = new PlayerPowerComponent(this);
    private final PlayerDataRecordComponent playerDataRecordComponent = new PlayerDataRecordComponent(this);
    private final PlayerProfileComponent profileComponent = new PlayerProfileComponent(this);
    private final PlayerActivityComponent activityComponent = new PlayerActivityComponent(this);
    private final PlayerQlogComponent qlogComponent = new PlayerQlogComponent(this);
    private final PlayerTroopFormationComponent troopFormationComponent = new PlayerTroopFormationComponent(this);
    private final PlayerMissionComponent missionComponent = new PlayerMissionComponent(this);

    // -------------------------------- 生命周期关键节点 --------------------------------
    //    private final PlayerChatComponent playerChatComponent = new PlayerChatComponent(this);
    private final PlayerBanTimeComponent playerBanTimeComponent = new PlayerBanTimeComponent(this);
    private final PlayerDevBuffComponent playerDevBuffComponent = new PlayerDevBuffComponent(this);
    private final PlayerAdditionComponent playerAdditionComponent = new PlayerAdditionComponent(this);
    private final PlayerResourceProduceComponent resourceProduceComponent = new PlayerResourceProduceComponent(this);
    private final PlayerResourceProtectComponent resourceProtectComponent = new PlayerResourceProtectComponent(this);
    private final PlayerPeaceShieldComponent peaceShieldComponent = new PlayerPeaceShieldComponent(this);
    private final PlayerBattleComponent battleComponent = new PlayerBattleComponent(this);
    private final PlayerRefreshComponent refreshComponent = new PlayerRefreshComponent(this);
    private final PlayerTaskComponent taskComponent = new PlayerTaskComponent(this);
    private final PlayerAssetComponent assetComponent = new PlayerAssetComponent(this);
    private final PlayerEnergyComponent energyComponent = new PlayerEnergyComponent(this);
    private final PlayerPositionMarkComponent positionMarkComponent = new PlayerPositionMarkComponent(this);
    private final PlayerGuidanceComponent playerGuidanceComponent = new PlayerGuidanceComponent(this);
    private final PlayerTechnologyComponent playerTechComponent = new PlayerTechnologyComponent(this);
    private final PlayerNewbieComponent newbieComponent = new PlayerNewbieComponent(this);
    private final PlayerStatisticComponent playerStatisticComponent = new PlayerStatisticComponent(this);
    private final PlayerRedDotComponent redDotComponent = new PlayerRedDotComponent(this);
    private final PlayerRecruitComponent recruitComponent = new PlayerRecruitComponent(this);
    private final PlayerClanTechComponent clanTechComponent = new PlayerClanTechComponent(this);
    private final PlayerMileStoneComponent mileStoneComponent = new PlayerMileStoneComponent(this);
    private final PlayerPointsComponent pointsComponent = new PlayerPointsComponent(this);
    private final PlayerPaymentComponent paymentComponent = new PlayerPaymentComponent(this);
    private final PlayerFriendComponent friendComponent = new PlayerFriendComponent(this);
    private final PlayerVipComponent vipComponent = new PlayerVipComponent(this);
    private final PlayerInnerQuestComponent innerQuestComponent = new PlayerInnerQuestComponent(this);
    private final PlayerDailyDiscountComponent dailyDiscountComponent = new PlayerDailyDiscountComponent(this);
    private final PlayerRandomDrawComponent randomDrawComponent = new PlayerRandomDrawComponent(this);
    private final PlayerKingdomComponent kingdomComponent = new PlayerKingdomComponent(this);
    private final PlayerClanGiftComponent clanGiftComponent = new PlayerClanGiftComponent(this);
    private final PlayerTriggerBundleComponent triggerBundleComponent = new PlayerTriggerBundleComponent(this);
    private final PlayerFeatureLockComponent featureLockComponent = new PlayerFeatureLockComponent(this);
    private final PlayerCityDressComponent cityDressComponent = new PlayerCityDressComponent(this);
    private final PlayerCharacterManagerComponent characterManagerComponent = new PlayerCharacterManagerComponent(this);
    private final PlayerContactsComponent contactsComponent = new PlayerContactsComponent(this);
    private final PlayerViewComponent viewComponent = new PlayerViewComponent(this);
    private final PlayerMultiServerComponent multiServerComponent = new PlayerMultiServerComponent(this);
    private final PlayerSkynetComponent skynetComponent = new PlayerSkynetComponent(this);
    private final PlayerCityNameplateComponent cityNameplateComponent = new PlayerCityNameplateComponent(this);
    private final PlayerAchievementComponent achievementComponent = new PlayerAchievementComponent(this);
    private final PlayerSeasonBattlePassComponent seasonBattlePassComponent = new PlayerSeasonBattlePassComponent(this);

    public PlayerEntity(final PlayerActor playerActor, final PlayerProp prop, final boolean isRegister, final Player.PlayerEntity changed) {
        super(playerActor.getPlayerId());
        this.prop = prop;
        this.actor = playerActor;
        this.getDbComponent().onCreate(changed);
        if (!isRegister) {
            // 提前设置，收集player初始化以及登录过程中的脏数据
            this.getPlayerPropComponent().setPropertyChangeListener();
        }
        this.applyDataPatch(prop, isRegister);
        this.initAllComponents();
    }


    /**
     * 生效补丁，仅用于玩家数据修正
     * 提醒：吃patch的时候各component还没有init过，严禁调用，直接取prop来操作
     * <p>
     * 新增patch代码需要自增version！！！
     */
    public void applyDataPatch(PlayerProp prop, boolean isRegister) {
        LOGGER.info("player applyDataPatch {} {}", this, getProp().getDataPatchVersion());
        // // 修复科技加速统计项
        int version1 = 1;
        // 修改道具唯一id为templateId
        int version3 = 3;
        // 远征奖励模式变更 兼容吸纳上老号数据
        int version5 = 5;

        int maxVersion = version5;

        if (isRegister) {
            // 新注册直接更新到最大version
            updateDataPatch(maxVersion);
            return;
        }

        // 修复科技加速统计项
        if (getProp().getDataPatchVersion() < version1) {
            updateDataPatch(version1);
            try {
                if (getProp().getCreateTime() <= TimeUtils.string2TimeStampMs("2023-09-04 00:00:00")) {
                    LOGGER.info("dataPatch fix tech statistic start");
                    // 重置科技累计加速
                    SingleStatisticProp techValue = getProp().getStatisticModel().getSingleStatisticMapV(StatisticEnum.USE_TECH_ACCELERATE_ITEM_TOTAL.ordinal());
                    if (techValue != null) {
                        long tV = techValue.getValue();
                        techValue.setValue(0);

                        // 去掉总累计加速中的科技加速
                        SingleStatisticProp totalValue = getProp().getStatisticModel().getSingleStatisticMapV(StatisticEnum.USE_ACCELERATE_ITEM_TOTAL.ordinal());
                        if (totalValue != null) {
                            totalValue.setValue(Math.max(0, totalValue.getValue() - tV));
                        }
                    }
                    LOGGER.info("dataPatch fix tech statistic end");
                }
            } catch (Exception e) {
                WechatLog.error("dataPatch fix tech failed version={} ", version1, e);
            }
        }

        // 修改道具唯一id为templateId
        if (getProp().getDataPatchVersion() < version3) {
            updateDataPatch(version3);
            try {
                LOGGER.info("dataPatch fix tech start version={} ", version3);
                getItemComponent().fixItemWithOldAccount();
                LOGGER.info("dataPatch fix tech end version={} ", version3);
            } catch (Exception e) {
                WechatLog.error("dataPatch fix tech failed version={} ", version3, e);
            }
        }

        if (getProp().getDataPatchVersion() < version5) {
            updateDataPatch(version5);
            try {
                LOGGER.info("dataPatch fix tech start version={} ", version5);

                LOGGER.info("dataPatch fix tech end version={} ", version5);
            } catch (Exception e) {
                WechatLog.error("dataPatch fix tech failed version={} ", version5, e);
            }
        }

        // 后续path在这里加，使用上面的范式，不能写return！！
    }

    private void updateDataPatch(int newVersion) {
        LOGGER.info("player updateDataPatch {} {}", this, newVersion);
        getProp().setDataPatchVersion(newVersion);
    }

    public boolean isRegister() {
        return isRegister;
    }

    public void setRegister(boolean register) {
        isRegister = register;
    }

    public PlayerProp getProp() {
        return prop;
    }

    public int getZoneId() {
        return ownerActor().getZoneId();
    }

    @Override
    public long getEntityId() {
        return getProp().getId();
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Player;
    }

    @Override
    public PlayerActor ownerActor() {
        return actor;
    }

    @Override
    protected void onPostInitFailed() {

    }

    public void permissionRoleCheck() {
        getPlayerBanTimeComponent().checkPlayerBanTime();
    }

    /**
     * 顶号会直接在player层调用
     * 普通断开连接则是gate层发来调用
     */
    public void onDisconnect(CommonEnum.SessionCloseReason closeReason) {
        LOGGER.info("[logout] sessionId={} openId={} playerId={} onDisconnect reason={}", getSessionComponent().getSessionId(), getOpenId(), getPlayerId(), closeReason);
        getSessionComponent().onLogout(closeReason);
        callAllComponentsSafe(PlayerComponent::afterLogout);
        // 检查是否所有的循环定时器已经取消
        boolean isAllCancel = ownerActor().isAllRepeatTimerCancel();
        if (!isAllCancel) {
            WechatLog.error("disconnect done, RepeatTimer not cancel! {}", this);
        }
    }

    public void checkMemorySize() {
        checkMemorySize(5);
    }

    public void checkMemorySize(int layer) {
        try {
            GeminiStopWatch stopWatch = new GeminiStopWatch("jol_parse_player");
            GraphLayout graphLayout = JolUtils.getGraphLayout(this, JOL_EXCLUDE);
            long totalSize = graphLayout.totalSize() / 1024;
            JolParser.toDisplay(TreeLayout.parse(graphLayout), layer, 4).newStringBuilder();
            stopWatch.mark("jol_parse_player_end");
            long costMs = stopWatch.getTotalCost();
            LOGGER.info("{} parse player size:{} count:{} cost:{}ms", this, totalSize, graphLayout.totalCount(), costMs);
        } catch (Throwable e) {
            LOGGER.error("player checkMemorySize error: ", e);
        }
    }

    /**
     * 获取玩家当前所在场景id
     */
    public long getCurSceneId() {
        return getProp().getScene().getCurSceneId();
    }

    public long getMainSceneId() {
        return getZoneId();
    }

    public boolean isOnline() {
        return getSessionComponent().isOnline();
    }

    /**
     * 只允许发ntf消息，禁止发送s2c类型
     */
    public void sendMsgToClient(int msgType, GeneratedMessageV3 msg) {
        if (!isOnline()) {
            return;
        }
        IActorRef sessionRef = getSessionComponent().getSessionRef();
        SessionHelper.sendMsgToSessionWithSeqId(sessionRef, ownerActor(), msgType, 0, msg);
    }

    /**
     * 异步回包给客户端，必须先取sessionRef，防止过程中断线重连
     * sessionRef必须是在回调前获取
     */
    public void answerMsgToClient(IActorRef sessionRef, int seqId, int s2cMsgId, Throwable e, GeneratedMessageV3 msg) {
        if (!isOnline()) {
            return;
        }
        answerMsgToClientForce(sessionRef, seqId, s2cMsgId, e, msg);
    }

    public void answerMsgToClientForce(IActorRef sessionRef, int seqId, int s2cMsgId, Throwable e, GeneratedMessageV3 msg) {
        if (e != null) {
            Core.Code retCode = ErrorCode.FAILED.getCode();
            if (e instanceof Exception) {
                retCode = ErrorCodeUtils.getCodeFromException((Exception) e);
            } else {
                LOGGER.error("answerMsgToClient throwable {} ", this, e);
            }
            LOGGER.info("answerMsgToClient {} to {}", retCode, ownerActor());
            SessionHelper.sendErrorCodeToSession(sessionRef, ownerActor(), s2cMsgId, seqId, retCode);
            return;
        }
        SessionHelper.sendMsgToSessionWithSeqId(sessionRef, ownerActor(), s2cMsgId, seqId, msg);
    }

    /**
     * @return 是否在副本
     */
    public boolean isInDungeon() {
        return getProp().getScene().getDungeonType() != CommonEnum.DungeonType.DT_NONE;
    }

    /**
     * 是否在主世界
     */
    public boolean isInMainScene() {
        return getProp().getScene().getDungeonType() == CommonEnum.DungeonType.DT_NONE;
    }

    public String getName() {
        return getProp().getAvatarModel().getCardHead().getName();
    }

    public PlayerCardHeadProp getCardHead() {
        return getProp().getAvatarModel().getCardHead();
    }

    public CommonMsg.ClientInfo getClientInfo() {
        return getSessionComponent().getClientInfo();
    }

    public CommonEnum.Language getClientLanguage() {
        return getClientInfo().getLanguage();
    }

    public String getOpenId() {
        return getProp().getOpenId();
    }

    public void verifyThrow(AssetPackage consume) {
        getAssetComponent().verifyThrow(consume);
    }

    /**
     * 包含来源的资产消耗
     */
    public void consume(AssetPackage consume, CommonEnum.Reason reason) {
        getAssetComponent().consume(consume, reason);
    }

    /**
     * 包含子来源的资产消耗
     */
    public void consume(AssetPackage consume, CommonEnum.Reason reason, Object subReason) {
        getAssetComponent().consume(consume, reason, String.valueOf(subReason));
    }

    /**
     * 调用所有组件的方法
     */
    private void callAllComponents(Consumer<? super PlayerComponent> action) {
        Collection<PlayerComponent> components = getAllComponents();
        for (PlayerComponent component : components) {
            action.accept(component);
        }
    }

    /**
     * 安全的调用所有组件的方法
     */
    public void callAllComponentsSafe(Consumer<? super PlayerComponent> action) {
        callAllComponents((component) -> {
            try {
                action.accept(component);
            } catch (Exception e) {
                WechatLog.error("player id={} callAllComponents error", getEntityId(), e);
            }
        });
    }

    @Override
    public void deleteObj() {
        super.deleteObj();
    }

    public PlayerEnergyComponent getEnergyComponent() {
        return energyComponent;
    }

    public PlayerClanStoreComponent getClanStoreComponent() {
        return clanStoreComponent;
    }

    public PlayerDiscountStoreComponent getDiscountStoreComponent() {
        return discountStoreComponent;
    }

    public PlayerCommissariatStoreComponent getCommStoreComponent() {
        return commStoreComponent;
    }

    public PlayerSettingComponent getSettingComponent() {
        return settingComponent;
    }

    public PlayerPositionMarkComponent getPositionMarkComponent() {
        return positionMarkComponent;
    }

    public PlayerPropComponent getPlayerPropComponent() {
        return playerPropComponent;
    }

    public PlayerSessionComponent getSessionComponent() {
        return sessionComponent;
    }

    public PlayerDbComponent getDbComponent() {
        return dbComponent;
    }

    public PlayerRankComponent getPlayerRankComponent() {
        return rankComponent;
    }

    public PlayerClanComponent getPlayerClanComponent() {
        return clanComponent;
    }

    public PlayerSceneMgrComponent getSceneMgrComponent() {
        return sceneMgrComponent;
    }

    public PlayerInnerBuildRhComponent getInnerBuildRhComponent() {
        return innerBuildRhComponent;
    }

    public PlayerCityCommandComponent getCityCommandComponent() {
        return cityCommandComponent;
    }

    public PlayerCampaignComponent getCampaignComponent() {
        return campaignComponent;
    }

    public PlayerQueueTaskComponent getPlayerQueueTaskComponent() {
        return playerQueueTaskComponent;
    }

    public PlayerAutoAddMonsterComponent getScheduleComponent() {
        return scheduleComponent;
    }

    public PlayerItemComponent getItemComponent() {
        return itemComponent;
    }

    public PlayerPurseComponent getPurseComponent() {
        return purseComponent;
    }

    public PlayerPlaneComponent getPlaneComponent() {
        return planeComponent;
    }

    public PlayerWallComponent getWallComponent() {
        return wallComponent;
    }

    public PlayerMailComponent getMailComponent() {
        return mailComponent;
    }

    public PlayerHospitalComponent getHospitalComponent() {
        return playerHospitalComponent;
    }

    public PlayerHeroComponent getHeroComponent() {
        return playerHeroComponent;
    }

    public PlayerSoldierComponent getSoldierComponent() {
        return playerSoldierComponent;
    }

    public PlayerPowerComponent getPowerComponent() {
        return playerPowerComponent;
    }

    public PlayerDataRecordComponent getDataRecordComponent() {
        return playerDataRecordComponent;
    }

    public PlayerProfileComponent getProfileComponent() {
        return profileComponent;
    }

    public PlayerActivityComponent getActivityComponent() {
        return activityComponent;
    }

    public PlayerQlogComponent getQlogComponent() {
        return qlogComponent;
    }

    public PlayerTroopFormationComponent getTroopFormationComponent() {
        return troopFormationComponent;
    }

    public PlayerBanTimeComponent getPlayerBanTimeComponent() {
        return playerBanTimeComponent;
    }

    public PlayerDevBuffComponent getPlayerDevBuffComponent() {
        return playerDevBuffComponent;
    }

    public PlayerAdditionComponent getAddComponent() {
        return playerAdditionComponent;
    }

    public PlayerResourceProduceComponent getResourceProduceComponent() {
        return resourceProduceComponent;
    }

    public PlayerResourceProtectComponent getResourceProtectComponent() {
        return resourceProtectComponent;
    }

    public PlayerPeaceShieldComponent getPeaceShieldComponent() {
        return peaceShieldComponent;
    }

    public PlayerBattleComponent getBattleComponent() {
        return battleComponent;
    }

    public PlayerTaskComponent getTaskComponent() {
        return taskComponent;
    }

    public PlayerAssetComponent getAssetComponent() {
        return assetComponent;
    }

    public PlayerGuidanceComponent getGuidanceComponent() {
        return playerGuidanceComponent;
    }

    public PlayerTechnologyComponent getTechComponent() {
        return playerTechComponent;
    }

    public PlayerStatisticComponent getStatisticComponent() {
        return playerStatisticComponent;
    }

    public PlayerNewbieComponent getNewbieComponent() {
        return newbieComponent;
    }

    public PlayerRedDotComponent getRedDotComponent() {
        return redDotComponent;
    }

    public PlayerRecruitComponent getRecruitComponent() {
        return recruitComponent;
    }

    public PlayerClanTechComponent getClanTechComponent() {
        return clanTechComponent;
    }

    public PlayerMileStoneComponent getMileStoneComponent() {
        return mileStoneComponent;
    }

    public PlayerPointsComponent getPointsComponent() {
        return pointsComponent;
    }

    public PlayerPaymentComponent getPaymentComponent() {
        return paymentComponent;
    }

    public PlayerFriendComponent getFriendComponent() {
        return friendComponent;
    }

    public PlayerVipComponent getVipComponent() {
        return vipComponent;
    }

    public PlayerRefreshComponent getRefreshComponent() {
        return refreshComponent;
    }

    public PlayerInnerQuestComponent getInnerQuestComponent() {
        return innerQuestComponent;
    }

    public PlayerDailyDiscountComponent getDailyDiscountComponent() {
        return dailyDiscountComponent;
    }

    public PlayerRandomDrawComponent getRandomDrawComponent() {
        return randomDrawComponent;
    }

    public PlayerKingdomComponent getKingdomComponent() {
        return kingdomComponent;
    }

    public PlayerClanGiftComponent getClanGiftComponent() {
        return clanGiftComponent;
    }

    public PlayerTriggerBundleComponent getTriggerBundleComponent() {
        return triggerBundleComponent;
    }

    public PlayerFeatureLockComponent getFeatureLockComponent() {
        return featureLockComponent;
    }

    public PlayerCityDressComponent getCityDressComponent() {
        return cityDressComponent;
    }

    public PlayerViewComponent getViewComponent() {
        return viewComponent;
    }

    public PlayerBattlePassComponent getBattlePassComponent() {
        return battlePassComponent;
    }

    public PlayerSeasonBattlePassComponent getSeasonBattlePassComponent() {
        return seasonBattlePassComponent;
    }

    public PlayerCharacterManagerComponent getCharacterManagerComponent() {
        return characterManagerComponent;
    }

    public PlayerContactsComponent getContactsComponent() {
        return contactsComponent;
    }

    public PlayerMultiServerComponent getMultiServerComponent() {
        return multiServerComponent;
    }

    public PlayerSkynetComponent getSkynetComponent() {
        return skynetComponent;
    }

    public PlayerCityNameplateComponent getCityNameplateComponent() {
        return cityNameplateComponent;
    }

    public PlayerAchievementComponent getAchievementComponent() {
        return achievementComponent;
    }

    public PlayerAntiAddictionComponent getAntiAddictionComponent() {
        return antiAddictionComponent;
    }

    public PlayerMissionComponent getMissionComponent() {
        return missionComponent;
    }

    // -------------------------------- 接口实现 --------------------------------
    public String getClientIp() {
        return getSessionComponent().getClientIp();
    }

    public long getPlayerId() {
        return getEntityId();
    }

    public long getClanId() {
        return getPlayerClanComponent().getClanId();
    }

    public String getAccountId() {
        return getProp().getOpenId();
    }

    // ------------------------- 容器重写 -------------------------

    @Override
    public int hashCode() {
        return Objects.hash(getEntityId());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        PlayerEntity entity = (PlayerEntity) obj;
        return getEntityId() == entity.getEntityId();
    }

    @Override
    public String toString() {
        return "Player{" + getEntityId() + ", " + getAccountId() + ", " + getName() + "}";
    }

    /**
     * 判断playerEntity是否属于内部玩家
     *
     * @return 是否是内部玩家的布尔值
     */
    public boolean isInternalPlayer() {
        return getProp().getBasicInfo().getInternalPlayerTag();
    }

    public boolean hasUnlockPic(int pic) {
        return getProp().getAvatarModel().getPicList().containsKey(pic);
    }

    public boolean hasUnlockPicFrame(int picFrame) {
        return getProp().getAvatarModel().getPicFrameList().containsKey(picFrame);
    }

    public long totalPower() {
        return getProp().getPlayerPowerInfo().getTotalPower();
    }

    public SsTextFilter.CheckTextAns syncCheckText(String text, CommonEnum.UgcSceneId sceneId) {
        SsTextFilter.CheckTextAsk.Builder builder = SsTextFilter.CheckTextAsk.newBuilder()
                .setSceneId(sceneId)
                .setText(text);
        String modeStr = ClusterConfigUtils.getWorldConfig().getStringItem("text_filter_mode");
        TextFilterMode mode = TextFilterMode.valueOf(modeStr);
        if (mode == TextFilterMode.ALL_PASS) {
            return SsTextFilter.CheckTextAns.newBuilder()
                    .setIsLegal(true)
                    .setFilteredText(text)
                    .build();
        } else if (mode == TextFilterMode.ALL_REJECT) {
            // 这个有点争议，这种模式的行为比较难以确定，不过这个模式一般也不会用到就是了
            return SsTextFilter.CheckTextAns.newBuilder()
                    .setIsLegal(false)
                    .setFilteredText("")
                    .build();
        }
        if (TextFilterUtils.isTssSdk()) {
            builder.setUserInfo(buildTsssdkJudgeUserInfo());
        }
        // 默认开启敏感词校验
        return ownerActor().callTextFilter(builder.build());
    }

    public SsTextFilter.BatchCheckTextAns syncBatchCheckText(String title, String subTitle, String
            senderTitle, String content) {
        String modeStr = ClusterConfigUtils.getWorldConfig().getStringItem("text_filter_mode");
        TextFilterMode mode = TextFilterMode.valueOf(modeStr);
        if (mode == TextFilterMode.ALL_PASS) {
            return SsTextFilter.BatchCheckTextAns.newBuilder()
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(true).setFilteredText(title).build())
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(true).setFilteredText(subTitle).build())
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(true).setFilteredText(senderTitle).build())
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(true).setFilteredText(content).build())
                    .build();
        } else if (mode == TextFilterMode.ALL_REJECT) {
            return SsTextFilter.BatchCheckTextAns.newBuilder()
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(false).setFilteredText("").build())
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(false).setFilteredText("").build())
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(false).setFilteredText("").build())
                    .addResults(SsTextFilter.CheckTextAns.newBuilder().setIsLegal(false).setFilteredText("").build())
                    .build();
        }
        SsTextFilter.BatchCheckTextAsk.Builder builder = SsTextFilter.BatchCheckTextAsk.newBuilder()
                .addTexts(SsTextFilter.CheckTextAsk.newBuilder().setText(title).setSceneId(CommonEnum.UgcSceneId.USI_PERSONAL_MAIL).build())
                .addTexts(SsTextFilter.CheckTextAsk.newBuilder().setText(subTitle).setSceneId(CommonEnum.UgcSceneId.USI_PERSONAL_MAIL).build())
                .addTexts(SsTextFilter.CheckTextAsk.newBuilder().setText(senderTitle).setSceneId(CommonEnum.UgcSceneId.USI_PERSONAL_MAIL).build())
                .addTexts(SsTextFilter.CheckTextAsk.newBuilder().setText(content).setSceneId(CommonEnum.UgcSceneId.USI_PERSONAL_MAIL).build());
        if (TextFilterUtils.isTssSdk()) {
            builder.setUserInfo(buildTsssdkJudgeUserInfo());
        }
        return ownerActor().callTextFilter(builder.build());
    }

    public CommonMsg.TsssdkJudgeUserInfo buildTsssdkJudgeUserInfo() {
        return CommonMsg.TsssdkJudgeUserInfo.newBuilder()
                .setAccountId(this.getOpenId())
                .setPlatId(this.getClientInfo().getPlatformId())
                .setZoneId(this.getZoneId())
                .setRoleId(this.getPlayerId())
                .setRoleName(this.getName())
                .build();
    }

    public int getCityLevel() {
        return 1;
    }

    /**
     * 获取在线毫秒数
     */
    public long getOnlineMs() {
        long curOnlineMs = 0;
        if (isOnline()) {
            curOnlineMs = SystemClock.now() - getProp().getBasicInfo().getLastLoginTsMs();
            if (curOnlineMs < 0) {
                LOGGER.error("online activity getOnlineSec fail. onlineMs:{}", curOnlineMs);
            }
        }
        return getProp().getBasicInfo().getOnlineTimeMs() + curOnlineMs;
    }

    public boolean isMigratePlayer() {
        // 移民玩家不上榜
        final int bornZoneId = getProp().getZoneModel().getBornZoneId();
        final int curZoneId = getZoneId();
        return bornZoneId != curZoneId;
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        getDbComponent().saveOnDestroy();
        getProp().unMarkAll();
    }

    /**
     * 主动踢自己，实际上是tell给自己的sessionRef来走踢人流程
     */
    public void kickOffMe(CommonEnum.SessionCloseReason reason) {
        LOGGER.info("[logout] sessionId={} openId={} playerId={} kickoffMe {} reason={}", getSessionComponent().getSessionId(), getOpenId(), getPlayerId(), this, reason);
        if (!isOnline()) {
            LOGGER.info("handle kickoffMe, but i already offline, {} reason={}", this, reason);
            return;
        }
        // 下线时会置空sessionRef
        IActorRef sessionRef = getSessionComponent().getSessionRef();
        // 原地执行下线逻辑
        onDisconnect(reason);

        // 告知session断开tcp
        if (sessionRef != null) {
            SsGateSession.KickOffSessionCmd cmd = SsGateSession.KickOffSessionCmd.newBuilder().setReason(reason).build();
            ActorSendMsgUtils.send(sessionRef, cmd);
        }
    }
}

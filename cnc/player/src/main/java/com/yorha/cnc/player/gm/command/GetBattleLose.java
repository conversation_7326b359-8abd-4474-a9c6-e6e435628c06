package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsScenePlayer;

import java.util.Map;

/**
 * 查看系统援助当前战损累计
 * <AUTHOR>
 */
public class GetBattleLose implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        SsScenePlayer.GetBattleLoseAsk.Builder builder = SsScenePlayer.GetBattleLoseAsk.newBuilder();
        builder.setPlayerId(playerId);
        SsScenePlayer.GetBattleLoseAns ans = actor.callBigScene(builder.build());
        throw new GeminiException(ErrorCode.SYSTEM_WARNING, "your battle lose: " + ans.getBattleLose());
    }

    @Override
    public String showHelp() {
        return "UnlockFog centerX={}, centerY={}, range={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityScoreRewardUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ActivityUnitType;
import com.yorha.proto.CommonEnum.DataRecordType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncActivityScore;
import res.template.ActivityScoreRewardTemplate;
import res.template.ActivityTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 历史最高战力活动
 *
 * <AUTHOR>
 */
public class PlayerHistoryHighestPowerUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerHistoryHighestPowerUnit.class);
    /**
     * 延时同步至联排行榜的task
     */
    private final String scoreId;
    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerPowerIncreaseEvent.class
    );

    static {
        ActivityUnitFactory.register(ActivityUnitType.AUT_HISTORY_POWER_MAX, (owner, prop, template) ->
                new PlayerHistoryHighestPowerUnit(owner, prop.getCommonScoreRewardUnit(), template.getScoreRewardUnitId())
        );
    }

    public PlayerHistoryHighestPowerUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp, String scoreId) {
        super(ownerActivity, unitProp);
        this.scoreId = scoreId;
    }

    private void init() {
        final ActivityScoreRewardUnitProp prop = unitProp.getScoreRewardUnit();
        final int level = ownerActivity.getPlayer().getCityLevel();
        final int cityLevel = (level <= 0) ? 1 : level;
        if (!ZoneContext.isServerOpen()) {
            throw new GeminiException("server is not open");
        }
        final long serverOpenTsMs = ZoneContext.getServerOpenTsMs();
        final long activityStartTsMs = TimeUnit.SECONDS.toMillis(ownerActivity.getProp().getStartTsSec());
        final int serverOpenDay;
        if (serverOpenTsMs > activityStartTsMs) {
            // 兼容移民的情况。移民后，自创角起的活动，开启时间是出生服的创角时间，比当前服的开服时间小，所以直接取0
            // 定期分服循环也会有这个问题，算出来的活动开启时间早于开服时间
            serverOpenDay = 0;
        } else {
            serverOpenDay = (int) TimeUtils.getAbsNatureDaysBetween(serverOpenTsMs, activityStartTsMs);
        }
        prop.setCityLevel(cityLevel).setServerOpenDay(serverOpenDay);
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final int scoreBoxId = key.getScoreBoxId();
        ActivityScoreRewardUnitProp prop = unitProp.getScoreRewardUnit();
        boolean alreadyTaken = prop.getTakenBoxIds().contains(scoreBoxId);
        if (alreadyTaken) {
            // 已经领取
            throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
        }
        ActivityResService asr = ResHolder.getResService(ActivityResService.class);
        ActivityScoreRewardTemplate template = asr.findScoreRewardTemplate(scoreId, scoreBoxId, prop.getCityLevel(), prop.getServerOpenDay());
        if (template == null) {
            throw new GeminiException(ErrorCode.REWARD_NOT_EXIST);
        }

        // 判断积分足够
        if (prop.getScore() < template.getScore()) {
            throw new GeminiException("score not enough.");
        }

        // 标记领取
        prop.addTakenBoxIds(scoreBoxId);

        // 给奖励
        final AssetPackage reward = AssetPackage.builder().plusItems(template.getRewardPairList()).build();
        final ActivityTemplate activityTemplate = ResHolder.getInstance().getValueFromMap(ActivityTemplate.class, this.ownerActivity.getActivityId());
        final String subReason = activityTemplate == null ? String.valueOf(this.ownerActivity.getActivityId()) : activityTemplate.getActivityRankType().name();
        ownerActivity.getPlayer().getAssetComponent().give(reward, CommonEnum.Reason.ICR_ACTIVITY_REWARD, subReason);

        rsp.setReward(reward.toPb());

        sendTakeRewardQlog(template);
    }

    private void sendTakeRewardQlog(ActivityScoreRewardTemplate template) {
        try {
            QlogCncActivityScore.init(ownerActivity.getPlayer().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("collect_act_reward")
                    .setSubjectID("")
                    .setIcount(0)
                    .setActivityScore(template.getScore())
                    .setActivityScoreID(scoreId)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendQlogCncActivityScore failed: ", e);
        }
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        updateScore();
    }

    private void updateScore() {
        long recordValue = player().getDataRecordComponent().getRecordValue(DataRecordType.DRT_MAX_POWER);
        if (recordValue <= unitProp.getScoreRewardUnit().getScore()) {
            return;
        }
        unitProp.getScoreRewardUnit().setScore((int) recordValue);
        try {
            QlogCncActivityScore.init(ownerActivity.getPlayer().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("powerMax")
                    .setSubjectID("")
                    .setIcount(0)
                    .setActivityScore((int) recordValue)
                    .setActivityScoreID(scoreId)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendQlogCncActivityScore addScore failed: ", e);
        }
    }
}

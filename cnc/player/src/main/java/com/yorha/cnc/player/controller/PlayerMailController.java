package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.*;
import res.template.CommanderAvatarFrameTemplate;
import res.template.CommanderAvatarTemplate;
import res.template.ConstTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.yorha.proto.CommonEnum.*;

/**
 * 邮件相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_MAIL)
public class PlayerMailController {

    private static StructMail.MailSender.Builder formMailSender(PlayerEntity playerEntity) {
        return StructMail.MailSender.newBuilder()
                .setSenderId(playerEntity.getPlayerId())
                .setZoneId(playerEntity.getZoneId())
                .setCardHead(playerEntity.getCardHead().getCopySsBuilder());

    }

    /**
     * 获取未读邮件数量
     */
    @CommandMapping(code = MsgType.PLAYER_MAILUNREADGET_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailUnreadGet_C2S msg) {
        return playerEntity.getMailComponent().mailUnReadGet();
    }

    /**
     * 批量已读邮件，只处理接收到的邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_MAILBATCHREAD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailBatchRead_C2S msg) {
        List<Long> res = playerEntity.getMailComponent().batchReadMail(msg.getMailListList());
        PlayerMail.Player_MailBatchRead_S2C.Builder builder = PlayerMail.Player_MailBatchRead_S2C.newBuilder();
        builder.addAllMailList(res);
        return builder.build();
    }

    /**
     * 领邮件奖励，只处理接收到的邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_MAILREWARDGET_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailRewardGet_C2S msg) {
        playerEntity.getMailComponent().mailRewardGet(msg.getMailId());
        PlayerMail.Player_MailRewardGet_S2C.Builder builder = PlayerMail.Player_MailRewardGet_S2C.newBuilder();
        builder.setMailId(msg.getMailId());
        builder.setMailTabsType(playerEntity.getMailComponent().getTemplateMailTabsType(builder.getMailId()));
        return builder.build();
    }

    /**
     * 删邮件。
     * 1. 删除已经发送邮件。
     * 2. 删除接收到的邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_MAILDEL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailDel_C2S msg) {
        return playerEntity.getMailComponent().mailDel(msg.getMailId());
    }

    /**
     * 批量删除邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_MAILBATCHDEL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailBatchDel_C2S msg) {
        final PlayerMail.Player_MailBatchDel_S2C.Builder builder = PlayerMail.Player_MailBatchDel_S2C.newBuilder();
        builder.addAllMailIdList(playerEntity.getMailComponent().mailBatchDel(msg.getMailIdListList()));
        return builder.build();
    }

    /**
     * 读战报
     */
    @CommandMapping(code = MsgType.PLAYER_READBATTLELOG_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_ReadBattleLog_C2S msg) {
        return playerEntity.getMailComponent().readBattleLog(msg);
    }

    private void checkPersonalMailC2S(PlayerMail.Player_SendPersonalMail_C2S msg) {
        if (msg.getTargetPlayerCount() == 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "Player_SendPersonalMail_C2S no target");
        }
        final int sendMailNumLimit = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getSendMailNumLimit();
        if (msg.getTargetPlayerCount() > sendMailNumLimit) {
            throw new GeminiException(ErrorCode.MAIL_REACH_MULTI_PERSONAL_LIMIT);
        }
        if (!msg.hasReceiverCard() && msg.getTargetPlayerCount() == 1) {
            throw new GeminiException(ErrorCode.MAIL_PLAYER_SEND_MAIL_NO_RECEIVER_CARD);
        }
        if (msg.hasReceiverCard() && msg.getTargetPlayerCount() > 1) {
            throw new GeminiException(ErrorCode.MAIL_PLAYER_SEND_MAIL_EXTRA_RECEIVER_CARD);
        }
        for (final CommonMsg.MailReceiver receiver : msg.getTargetPlayerList()) {
            if (receiver.getPlayerId() <= 0) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "Player_SendPersonalMail_C2S invalid playerId");
            }
            if (receiver.getZoneId() <= 0) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "Player_SendPersonalMail_C2S invalid zoneId");
            }
        }
    }

    /**
     * 发个人邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_SENDPERSONALMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_SendPersonalMail_C2S msg) {
        checkPersonalMailC2S(msg);
        final StructMailPB.PlayerMailReceiverCardPB receiverCard;
        if (msg.hasReceiverCard()) {
            if (!ResHolder.getInstance().getMap(CommanderAvatarTemplate.class).containsKey(msg.getReceiverCard().getPic()) && !msg.getReceiverCard().hasPicUrl()) {
                throw new GeminiException(ErrorCode.MAIL_PLAYER_SEND_MAIL_PLAYER_PIC_USELESS);
            }
            if (!ResHolder.getInstance().getMap(CommanderAvatarFrameTemplate.class).containsKey(msg.getReceiverCard().getPicFrame())) {
                throw new GeminiException(ErrorCode.MAIL_PLAYER_SEND_MAIL_PLAYER_PIC_FRAME_USELESS);
            }
            if (msg.getReceiverCard().getReceiverId() < 0) {
                throw new GeminiException(ErrorCode.MAIL_PLAYER_SEND_MAIL_PLAYER_RECEIVER_ID_USELESS);
            }
            receiverCard = msg.getReceiverCard();
        } else {
            receiverCard = null;
        }

        playerEntity.getMailComponent().checkBanPrivateMail();

        FilteredMailText filteredText = this.checkMailValid(playerEntity, msg.getTitle(), msg.getSubTitle(), msg.getSenderTitle(), msg.getContent());
        StructMail.MailSendParams mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(this.sendPersonMailTemplateId())
                .setSender(formMailSender(playerEntity))
                .setContent(StructMail.MailContent.newBuilder()
                        .setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                        .setDisplayData(Struct.DisplayData.newBuilder().setParams(
                                        Struct.DisplayParamList.newBuilder().addDatas(
                                                Struct.DisplayParam.newBuilder()
                                                        .setText(filteredText.content)
                                                        .setType(DisplayParamType.DPT_TEXT))
                                )
                        )
                )
                .setTitle(StructMail.MailShowTitle.newBuilder()
                        .setTitle(filteredText.title)
                        .setSubTitle(filteredText.subTitle)
                )
                .build();

        MailUtil.sendMailToPlayers(msg.getTargetPlayerList(), mailSendParams);
        playerEntity.getMailComponent().handleSendMail(this.sendMailTemplateId(), MailType.MAIL_TYPE_PERSONAL,
                filteredText.senderTitle, null, filteredText.subTitle, null, receiverCard, mailSendParams.getContent());

        return PlayerMail.Player_SendPersonalMail_S2C.getDefaultInstance();
    }

    /**
     * 发送联盟邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_SENDCLANMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_SendClanMail_C2S msg) {
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 检查是否有发送邮件的权限
        int staffId = playerEntity.getProp().getClan().getStaffId();
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_SEND_MAIL, staffId);

        // 检查邮件合法性
        FilteredMailText filteredText = this.checkMailValid(playerEntity, msg.getTitle(), msg.getSubTitle(), msg.getSenderTitle(), msg.getContent());
        StructMail.MailSendParams mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(this.sendClanMailTemplateId())
                .setSender(formMailSender(playerEntity))
                .setContent(StructMail.MailContent.newBuilder()
                        .setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                        .setDisplayData(Struct.DisplayData.newBuilder().setParams(
                                        Struct.DisplayParamList.newBuilder().addDatas(
                                                Struct.DisplayParam.newBuilder()
                                                        .setText(filteredText.content)
                                                        .setType(DisplayParamType.DPT_TEXT))
                                )
                        )
                )
                .setTitle(StructMail.MailShowTitle.newBuilder()
                        .setTitle(filteredText.title)
                        .setSubTitle(filteredText.subTitle)
                ).build();
        MailUtil.sendClanMail(playerEntity.getZoneId(), playerEntity.getClanId(), mailSendParams);
        playerEntity.getMailComponent().handleSendMail(this.sendMailTemplateId(), MailType.MAIL_TYPE_CLAN,
                filteredText.senderTitle, null, filteredText.subTitle, null, null, mailSendParams.getContent());
        return PlayerMail.Player_SendClanMail_S2C.newBuilder().build();
    }

    /**
     * 发送王国邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_SENDZONEMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_SendZoneMail_C2S msg) {
        // 仅国王可发送全服邮件
        if (!ServerContext.getServerDebugOption().isFreeSendZoneMail() && !playerEntity.getKingdomComponent().isKing()) {
            throw new GeminiException(ErrorCode.MAIL_PLAYER_SEND_FAIL);
        }
        FilteredMailText filteredText = this.checkMailValid(playerEntity, msg.getTitle(), msg.getSubTitle(), msg.getSenderTitle(), msg.getContent());
        Struct.DisplayParam.Builder subTitleDisPlayParam = Struct.DisplayParam.newBuilder()
                .setType(DisplayParamType.DPT_TEXT).setText(filteredText.subTitle);
        Struct.DisplayData.Builder subTitleDisPlayerData = Struct.DisplayData.newBuilder().setParams(
                Struct.DisplayParamList.newBuilder().addDatas(subTitleDisPlayParam)
        );

        StructMail.MailSendParams mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(playerEntity.getKingdomComponent().getKingdomMailId())
                .setSender(formMailSender(playerEntity))
                .setContent(StructMail.MailContent.newBuilder()
                        .setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                        .setDisplayData(Struct.DisplayData.newBuilder().setParams(
                                        Struct.DisplayParamList.newBuilder().addDatas(
                                                Struct.DisplayParam.newBuilder()
                                                        .setText(filteredText.content)
                                                        .setType(DisplayParamType.DPT_TEXT))
                                )
                        )
                )
                .setTitle(StructMail.MailShowTitle.newBuilder().setTitle(filteredText.title)
                        .setSubTitleData(subTitleDisPlayerData))
                .build();

        MailUtil.sendZoneMail(playerEntity.getZoneId(), mailSendParams);
        playerEntity.getMailComponent().handleSendMail(this.sendMailTemplateId(), MailType.MAIL_TYPE_ZONE,
                filteredText.senderTitle, null, null, subTitleDisPlayerData.build(), null, mailSendParams.getContent());

        return PlayerMail.Player_SendZoneMail_S2C.newBuilder().build();
    }

    /**
     * 屏蔽个人邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_SHIELDPERSONMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_ShieldPersonMail_C2S msg) {
        final int maxShieldPlayerCount = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getShieldMailLimit();
        final int defaultShieldPlayerDays = 365 * 100;
        if (msg.getPlayerIdListCount() > maxShieldPlayerCount) {
            throw new GeminiException(ErrorCode.MAIL_PLAYER_SHIELD_TOO_MUCH);
        }
        if (msg.getPlayerIdListList().stream().anyMatch(id -> id <= 0)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "Player_ShieldPersonMail_C2S contain non positive playerId");
        }
        for (final long playerId : msg.getPlayerIdListList()) {
            playerEntity.getMailComponent().shieldPersonMail(playerId, TimeUnit.DAYS.toSeconds(defaultShieldPlayerDays));
        }
        return PlayerMail.Player_ShieldPersonMail_S2C.newBuilder().addAllPlayerIdList(msg.getPlayerIdListList()).build();
    }

    /**
     * 解除个人邮件屏蔽。
     */
    @CommandMapping(code = MsgType.PLAYER_UNSHIELDPERSONMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_UnshieldPersonMail_C2S msg) {
        final int maxShieldPlayerCount = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getShieldMailLimit();
        if (msg.getPlayerIdListCount() > maxShieldPlayerCount) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "Player_UnshieldPersonMail_C2S playerIdListCount overSize");
        }
        for (final long playerId : msg.getPlayerIdListList()) {
            playerEntity.getMailComponent().unShieldPersonMail(playerId);
        }
        return PlayerMail.Player_UnshieldPersonMail_S2C.newBuilder().addAllPlayerIdList(msg.getPlayerIdListList()).build();
    }

    /**
     * 一键已读/领取指定邮件类型下所有邮件。
     */
    @CommandMapping(code = MsgType.PLAYER_READANDGETREWARDALLMAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_ReadAndGetRewardAllMail_C2S msg) {
        final MailTabsType mailTabsType = msg.getMailTabsType();
        if (mailTabsType == MailTabsType.MAIL_TABS_TYPE_UNKNOWN) {
            throw new GeminiException(ErrorCode.MAIL_TABS_NOT_EXIST);
        }
        final List<Long> mailIdList = playerEntity.getMailComponent().getMailIdsUnderTab(mailTabsType);
        // 1. 批量阅读的mailId
        playerEntity.getMailComponent().batchReadMail(mailIdList);
        final PlayerMail.Player_ReadAndGetRewardAllMail_S2C.Builder builder = PlayerMail.Player_ReadAndGetRewardAllMail_S2C.newBuilder().setMailTabsType(mailTabsType);
        // 2. 领取奖励
        final List<IntPairType> rewards = playerEntity.getMailComponent().mailRewardBatchGet(mailIdList, msg.getExcludeRewardMailIdListList());
        for (final IntPairType reward : rewards) {
            builder.putItems(reward.getKey(), builder.getItemsOrDefault(reward.getKey(), 0) + reward.getValue());
        }
        // 3. 返回结果
        return builder.build();
    }

    /**
     * 读战报详情
     */
    @CommandMapping(code = MsgType.PLAYER_READBATTLERECORDDETAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_ReadBattleRecordDetail_C2S msg) {
        return playerEntity.getMailComponent().readBattleRecordDetail(msg);
    }

    /**
     * 收藏邮件
     */
    @CommandMapping(code = MsgType.PLAYER_MAILSTORE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailStore_C2S msg) {
        playerEntity.getMailComponent().favoriteMail(msg.getMailId());
        return PlayerMail.Player_MailStore_S2C.getDefaultInstance();
    }

    /**
     * 批量拉取MailContent
     */
    @CommandMapping(code = MsgType.PLAYER_MAILBATCHGETCONTENT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMail.Player_MailBatchGetContent_C2S msg) {
        return playerEntity.getMailComponent().batchGetMailContent(msg.getMailIdsList());
    }

    private FilteredMailText checkMailValid(PlayerEntity playerEntity, String title, String subTitle, String senderTitle, String content) {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        if (content.length() > constTemplate.getMailContentLimit()) {
            throw new GeminiException(ErrorCode.WORD_EXCEEDED_LIMIT);
        }
        if (title.length() > constTemplate.getSendMailTitleLimit()) {
            throw new GeminiException(ErrorCode.WORD_EXCEEDED_LIMIT);
        }
        if (senderTitle.length() > constTemplate.getSendMailTitleLimit()) {
            throw new GeminiException(ErrorCode.WORD_EXCEEDED_LIMIT);
        }
        if (subTitle.length() > constTemplate.getMailTitleLimit()) {
            throw new GeminiException(ErrorCode.WORD_EXCEEDED_LIMIT);
        }
        // 敏感词
        SsTextFilter.BatchCheckTextAns checkTextAns = playerEntity.syncBatchCheckText(title, subTitle, senderTitle, content);
        return new FilteredMailText(
                checkTextAns.getResults(0).getFilteredText(),
                checkTextAns.getResults(1).getFilteredText(),
                checkTextAns.getResults(2).getFilteredText(),
                checkTextAns.getResults(3).getFilteredText()
        );
    }

    /**
     * @return 发送个人邮件模板id。
     */
    private int sendPersonMailTemplateId() {
        return ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getPersonalMailId();
    }

    /**
     * @return 发送联盟邮件模板id。
     */
    private int sendClanMailTemplateId() {
        return ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getClanMailId();
    }

    /**
     * @return 发送邮件的模板id。
     */
    private int sendMailTemplateId() {
        return ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getSendMailId();
    }

    private static class FilteredMailText {
        final String title;
        final String subTitle;
        final String senderTitle;
        final String content;

        private FilteredMailText(String title, String subTitle, String senderTitle, String content) {
            this.title = title;
            this.subTitle = subTitle;
            this.senderTitle = senderTitle;
            this.content = content;
        }
    }
}

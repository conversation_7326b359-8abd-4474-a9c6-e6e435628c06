package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.*;
import com.yorha.cnc.player.component.PlayerActivityComponent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.PlayerPurchaseCardEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.ActivityWeekMonthCardUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityWeekmonthCardTemplate;
import res.template.ChargeGoodsTemplate;
import res.template.ConstActivityTemplate;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 周卡月卡
 *
 * <AUTHOR>
 */
public class PlayerWeekMonthCardUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerWeekMonthCardUnit.class);

    private ActivityTimer expireTimer = null;

    private static final String TASK_FORMAT = "cardId_{%d}";

    public PlayerWeekMonthCardUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerPurchaseCardEvent.class
    );

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_WEEK_MONTH_CARD, (owner, prop, template) ->
                new PlayerWeekMonthCardUnit(owner, prop.getSpecUnit())
        );
    }

    private void init() {
        int actId = ownerActivity.getActivityId();
        for (ActivityWeekmonthCardTemplate template : ResHolder.getInstance().getListFromMap(ActivityWeekmonthCardTemplate.class)) {
            if (template.getActivityId() == actId) {
                unitProp.getWeekMonthCardUnit().setCardId(template.getGoodsId());
                return;
            }
        }
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
        PlayerActivityComponent activityComponent = ownerActivity.getPlayer().getActivityComponent();
        int scheduleId = ownerActivity.getActScheduleId();
        int actId = ownerActivity.getActivityId();
        // 添加周卡月卡过期timer
        int cardId = unitProp.getWeekMonthCardUnit().getCardId();
        // 老号的cardId可能没初始化，补充一下
        if (cardId == 0) {
            for (ActivityWeekmonthCardTemplate template : ResHolder.getInstance().getListFromMap(ActivityWeekmonthCardTemplate.class)) {
                if (template.getActivityId() == actId) {
                    unitProp.getWeekMonthCardUnit().setCardId(template.getGoodsId());
                    cardId = template.getGoodsId();
                    break;
                }
            }
        }
        int finalCardId = cardId;
        long expireTime = unitProp.getWeekMonthCardUnit().getExpireTsMs();
        long lastNtfExpireTime = unitProp.getWeekMonthCardUnit().getLastNtfExpireTsMs();
        if (expireTimer != null) {
            expireTimer.cancel();
            expireTimer = null;
        }
        if (expireTime >= SystemClock.now()) {
            Instant now = Instant.ofEpochMilli(SystemClock.now());
            Instant expire = Instant.ofEpochMilli(expireTime);
            Duration delay = Duration.between(now, expire);
            // 过期时间超过365天就不加了
            if (TimeUnit.SECONDS.toDays(delay.getSeconds()) < 365) {
                // 为防止定时器重复，格式化一下taskId
                String taskId = String.format(TASK_FORMAT, cardId);
                expireTimer = activityComponent.addTimer(scheduleId, actId, taskId, delay, () -> {
                    this.sendExpireMail(finalCardId);
                });
            }
            LOGGER.info("Create weekMonthCard expire timer, now={}, expire={}, delay={}", now, expire, delay);
        } else if (expireTime > lastNtfExpireTime) {
            sendExpireMail(cardId);
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {
        if (this.expireTimer != null) {
            this.expireTimer.cancel();
            this.expireTimer = null;
        }
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        int cardId = key.getWeekMonthCardId();
        if (cardId != unitProp.getWeekMonthCardUnit().getCardId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        long takeRewardTime = SystemClock.now();
        ActivityWeekMonthCardUnitProp prop = unitProp.getWeekMonthCardUnit();
        // 检查能够领奖
        if (takeRewardTime >= prop.getExpireTsMs()) {
            throw new GeminiException(ErrorCode.WEEK_MONTH_CARD_EXPIRE);
        }
        if (TimeUtils.isSameDay(takeRewardTime, prop.getLastDailyRewardTsMs())) {
            throw new GeminiException(ErrorCode.ALREADY_TAKE_REWARD);
        }
        prop.setLastDailyRewardTsMs(takeRewardTime);
        // 发奖
        ChargeGoodsTemplate goodsTemplate = ResHolder.findTemplate(ChargeGoodsTemplate.class, cardId);
        AssetPackage reward = AssetPackage.builder().plusItems(goodsTemplate.getDailyRewardPairList()).build();
        player().getAssetComponent().give(reward, CommonEnum.Reason.ICR_WEEK_MONTH_CARD_REWARD, String.valueOf(ownerActivity.getActivityId()));
        rsp.setReward(reward.toPb());
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    /**
     * 发送周卡月卡失效邮件
     */
    public void sendExpireMail(int cardId) {
        ConstActivityTemplate consts = ResHolder.getConsts(ConstActivityTemplate.class);
        Struct.DisplayData.Builder displayData = Struct.DisplayData.newBuilder()
                .setParams(Struct.DisplayParamList.newBuilder()
                        .addDatas(Struct.DisplayParam.newBuilder()
                                .setType(CommonEnum.DisplayParamType.DPT_CHARGE_GOODS_ID)
                                .setNumber(cardId)));
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(consts.getMonthlyCardExpiredMailId())
                .setTitle(StructMail.MailShowTitle.newBuilder()
                        .setTitleData(displayData)
                        .setSubTitleData(displayData))
                .setContent(StructMail.MailContent.newBuilder().setDisplayData(displayData));

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player().getPlayerId())
                .setZoneId(player().getZoneId())
                .build();

        LOGGER.info("{} send week month card {} expire mail", player().getPlayerId(), cardId);
        player().getMailComponent().sendPersonalMail(receiver, params.build());
        // 更新失效邮件发送时间
        ActivityWeekMonthCardUnitProp prop = unitProp.getWeekMonthCardUnit();
        prop.setLastNtfExpireTsMs(SystemClock.now());
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        PlayerActivityComponent activityComponent = player().getActivityComponent();
        int scheduleId = ownerActivity.getActScheduleId();
        int actId = ownerActivity.getActivityId();
        if (event instanceof PlayerPurchaseCardEvent) {
            int cardId = ((PlayerPurchaseCardEvent) event).getCardId();
            if (cardId != unitProp.getWeekMonthCardUnit().getCardId()) {
                return;
            }
            // 取消之前的定时器
            if (expireTimer != null) {
                expireTimer.cancel();
                expireTimer = null;
            }
            // 设置新的定时器
            long expireTime = unitProp.getWeekMonthCardUnit().getExpireTsMs();
            Instant now = Instant.ofEpochMilli(SystemClock.now());
            Instant expire = Instant.ofEpochMilli(expireTime);
            Duration delay = Duration.between(now, expire);
            // 过期时间超过365天就不加了
            if (TimeUnit.SECONDS.toDays(delay.getSeconds()) < 365) {
                String taskId = String.format(TASK_FORMAT, cardId);
                expireTimer = activityComponent.addTimer(scheduleId, actId, taskId, delay, () -> {
                    this.sendExpireMail(cardId);
                });
            }
            LOGGER.info("update weekMonthCard expire timer, now={}, expire={}, delay={}", now, expire, delay);
        }
    }
}

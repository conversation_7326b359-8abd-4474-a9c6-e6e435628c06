package com.yorha.cnc.player.controller;

import com.google.common.collect.Lists;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.task.DispatchTroopEvent;
import com.yorha.cnc.player.event.task.PlayerUseTransporterEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.game.gen.prop.CreateArmy_C2S_ParamProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.ArmyActionType;
import com.yorha.proto.CommonEnum.CheckZoneViewResult;
import com.yorha.proto.PlayerScene.*;
import com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns;
import com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk;
import com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns;
import com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk;
import com.yorha.proto.SsSceneMap.SearchPathAns;
import com.yorha.proto.SsSceneMap.SearchPathAsk;
import com.yorha.proto.SsSceneObj.CheckCanBeAttackAsk;
import com.yorha.proto.StructPB.PointListPB;
import com.yorha.proto.StructPB.PointPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.common.enums.statistic.StatisticEnum.DISPATCH_TROOP_TOTAL;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_SCENE_PLAYER)
public class PlayerSceneController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSceneController.class);

    @CommandMapping(code = MsgType.PLAYER_REPORTMAPVERSION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ReportMapVersion_C2S msg) {
        String version = ResHolder.getResService(SceneMapDataTemplateService.class).getNavmeshVersion(msg.getMapId());
        if (!"".equals(version) && !version.equals(msg.getVersion())) {
            LOGGER.warn("map version not same!!!!!!! mapId: {} server:{} client:{}", msg.getMapId(), version, msg.getVersion());
        }
        Player_ReportMapVersion_S2C.Builder builder = Player_ReportMapVersion_S2C.newBuilder();
        builder.setMapId(msg.getMapId());
        builder.setVersion(version);
        return builder.build();
    }

    /**
     * check 视野
     */
    @CommandMapping(code = MsgType.PLAYER_CHECKZONEVIEW_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CheckZoneView_C2S msg) {
        int targetZoneId = msg.getZoneId();
        CheckZoneViewResult result = playerEntity.getViewComponent().checkCanViewZone(targetZoneId);
        return Player_CheckZoneView_S2C.newBuilder().setCode(result).build();
    }

    /**
     * 拖动视野
     */
    @CommandMapping(code = MsgType.PLAYER_UPDATEVIEW_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_UpdateView_C2S msg) {
        int zoneId = playerEntity.getViewComponent().updateView(msg);
        return Player_UpdateView_S2C.newBuilder().setZoneId(zoneId).build();
    }

    private GeneratedMessageV3 checkAndCreateArmyInBigScene(PlayerEntity playerEntity, StructPlayer.CreateArmy_C2S_Param.Builder builder) {
        ArmyActionType armyActionType = builder.getArmyAction().getArmyActionType();
        boolean isTransportAction = armyActionType == ArmyActionType.AAT_Transport;

        int mainHeroId = builder.getTroopInfo().getMainHero().getHeroId();
        int deputyHeroId = builder.getTroopInfo().getDeputyHero().getHeroId();
        checkHeroState(playerEntity, builder);

        // 检测可以带兵的总数量，NOTE(furson): 这里的调用顺序很重要，有依赖关系，会刷新机甲身上的加成信息
        playerEntity.getBattleComponent().checkTroopMaxNum(builder, mainHeroId, deputyHeroId);

        long playerId = playerEntity.getEntityId();
        SsSceneCityArmy.CreateArmyCheckAsk.Builder checkAsk = SsSceneCityArmy.CreateArmyCheckAsk.newBuilder().setPlayerId(playerId).setParam(builder);
        SsSceneCityArmy.CreateArmyCheckAns checkAns = playerEntity.ownerActor().callCurScene(checkAsk.build());
        int monsterNeedEnergy = checkAns.getMonsterNeedEnergy();
        int realNeedEnergy = 0;
        String energyCostReason = null;
        int monsterTemplateId = 0;
        if (monsterNeedEnergy > 0) {
            if (checkAns.getMonsterId() > 0) {
                realNeedEnergy = (int) PlayerAddCalc.getKillMonsterEnergyCost(playerEntity, monsterNeedEnergy, mainHeroId, deputyHeroId, 0);
                monsterTemplateId = checkAns.getMonsterId();
            } else {
                realNeedEnergy = monsterNeedEnergy;
            }
            if (!playerEntity.getEnergyComponent().hasEnough(realNeedEnergy)) {
                throw new GeminiException(ErrorCode.MONSTER_ENERGY_NOT_ENOUGH);
            }
            energyCostReason = getEnergyCostReasonByAction(armyActionType);
        }
        // 重新构建英雄数据
        rebuildHeroData(playerEntity, builder);

        // 创建行军前置 各种占用
        int costEnergy = 0;
        CreatePlayerArmyAns ans;
        try {
            // 体力消耗
            if (realNeedEnergy > 0) {
                playerEntity.getEnergyComponent().consumeEnergy(realNeedEnergy, false, energyCostReason, monsterTemplateId);
                costEnergy = realNeedEnergy;
            }
            // 占用英雄
            playerEntity.getHeroComponent().occupyHero(Lists.newArrayList(mainHeroId, deputyHeroId));

            CreatePlayerArmyAsk.Builder call = CreatePlayerArmyAsk.newBuilder();
            call.setPlayerId(playerEntity.getPlayerId())
                    .setParam(builder)
                    .setCostEnergy(costEnergy);
            ans = playerEntity.ownerActor().callCurScene(call.build());
        } catch (Exception e) {
            if (costEnergy > 0) {
                playerEntity.getEnergyComponent().addEnergy(costEnergy, false, "attack_monster_return", 0);
            }
            playerEntity.getHeroComponent().releaseHeroState(Lists.newArrayList(mainHeroId, deputyHeroId));
            throw e;
        }

        // 更新出征次数统计项
        playerEntity.getStatisticComponent().recordSingleStatistic(DISPATCH_TROOP_TOTAL, 1);
        new DispatchTroopEvent(playerEntity, deputyHeroId).dispatch();

        // 运输机使用事件
        if (isTransportAction) {
            playerEntity.getStatisticComponent().recordSingleStatistic(StatisticEnum.AIR_FORCES_USED_TIMES, 1);
            new PlayerUseTransporterEvent(playerEntity).dispatch();
        }

        // 驻防检测
        playerEntity.getWallComponent().garrisonHeroOrMecha();
        Player_CreateArmy_S2C.Builder ret = Player_CreateArmy_S2C.newBuilder();
        if (ans.hasPoint()) {
            ret.getPointBuilder().setX(ans.getPoint().getX()).setY(ans.getPoint().getY());
        }

        return ret.setArmyId(ans.getArmyId()).build();
    }

    private void checkHeroState(PlayerEntity playerEntity, StructPlayer.CreateArmy_C2S_Param.Builder builder) {
        // 最大出兵量
        int mainHeroId = builder.getTroopInfo().getMainHero().getHeroId();
        playerEntity.getHeroComponent().checkHeroProp(mainHeroId);
        // 主将状态检测
        if (playerEntity.getHeroComponent().isBusyState(mainHeroId)) {
            throw new GeminiException(ErrorCode.HERO_HERO_IS_GOOUT);
        }
        int deputyHeroId = builder.getTroopInfo().getDeputyHero().getHeroId();
        // 副将状态检测
        if (deputyHeroId > 0) {
            if (deputyHeroId == mainHeroId) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
            if (playerEntity.getHeroComponent().isBusyState(deputyHeroId)) {
                throw new GeminiException(ErrorCode.HERO_HERO_IS_GOOUT);
            }
            // 主将三星才能配带副将的规则检查
            playerEntity.getHeroComponent().checkHeroProp(deputyHeroId);
            if (playerEntity.getHeroComponent().cantBringDeputy(mainHeroId)) {
                throw new GeminiException(ErrorCode.HERO_MAINHERO_STAR_LIMIT);
            }
        }
    }

    private GeneratedMessageV3 checkAndCreateArmyInDungeon(PlayerEntity playerEntity, StructPlayer.CreateArmy_C2S_Param.Builder builder) {
        int mainHeroId = builder.getTroopInfo().getMainHero().getHeroId();
        int deputyHeroId = builder.getTroopInfo().getDeputyHero().getHeroId();
        playerEntity.getHeroComponent().checkHeroProp(mainHeroId);
        // 副将状态检测
        if (deputyHeroId > 0) {
            // 主将三星才能配带副将的规则检查
            playerEntity.getHeroComponent().checkHeroProp(deputyHeroId);
            if (playerEntity.getHeroComponent().cantBringDeputy(mainHeroId)) {
                throw new GeminiException(ErrorCode.HERO_MAINHERO_STAR_LIMIT);
            }
        }
        // 最大出兵数量
        playerEntity.getBattleComponent().checkTroopMaxNum(builder, mainHeroId, deputyHeroId);

        // 重新构建英雄数据
        rebuildHeroData(playerEntity, builder);
        // 占用机甲，此处不再需要刷新加成，因为checkTroopMaxNum中刷新了加成
        CreatePlayerArmyAsk.Builder call = CreatePlayerArmyAsk.newBuilder();
        call.setPlayerId(playerEntity.getPlayerId())
                .setParam(builder);
        CreatePlayerArmyAns ans = playerEntity.ownerActor().callCurScene(call.build());
        Player_CreateArmy_S2C.Builder ret = Player_CreateArmy_S2C.newBuilder();
        if (ans.hasPoint()) {
            ret.getPointBuilder().setX(ans.getPoint().getX()).setY(ans.getPoint().getY());
        }
        return ret.setArmyId(ans.getArmyId()).build();
    }

    /**
     * 创建行军
     */
    @CommandMapping(code = MsgType.PLAYER_CREATEARMY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CreateArmy_C2S msg) {
        if (msg.getParam().getArmyAction().getArmyActionType() == ArmyActionType.AAT_CreateRally &&
                msg.getParam().getArmyAction().getWaitSecondsTime() == 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "wait second is 0");
        }
        CreateArmy_C2S_ParamProp askProp = new CreateArmy_C2S_ParamProp();
        askProp.mergeFromCs(msg.getParam());
        StructPlayer.CreateArmy_C2S_Param.Builder paramBuilder = askProp.getCopySsBuilder();
        if (playerEntity.isInMainScene()) {
            return checkAndCreateArmyInBigScene(playerEntity, paramBuilder);
        } else {
            return checkAndCreateArmyInDungeon(playerEntity, paramBuilder);
        }
    }

    /**
     * 重建一下英雄数据
     */
    public static void rebuildHeroData(PlayerEntity playerEntity, StructPlayer.CreateArmy_C2S_Param.Builder paramBuilder) {
        StructPlayer.Troop.Builder troopInfoBuilder = paramBuilder.getTroopInfoBuilder();
        if (troopInfoBuilder.getMainHero().getHeroId() > 0) {
            Struct.Hero hero = playerEntity.getHeroComponent().getHero(troopInfoBuilder.getMainHero().getHeroId());
            troopInfoBuilder.setMainHero(hero);
        }
        if (troopInfoBuilder.getDeputyHero().getHeroId() > 0) {
            Struct.Hero hero = playerEntity.getHeroComponent().getHero(troopInfoBuilder.getDeputyHero().getHeroId());
            troopInfoBuilder.setDeputyHero(hero);
        }
    }

    /**
     * 操控行军
     */
    @CommandMapping(code = MsgType.PLAYER_CHANGEARMYACTION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ChangeArmyAction_C2S msg) {
        PlayerActor playerActor = playerEntity.ownerActor();
        int realNeedEnergy = 0;
        if (playerEntity.isInMainScene()) {
            // 集结大厅检测
            StructPlayerPB.ArmyActionInfoPB actionInfo = msg.getArmyAction();

            SsSceneCityArmy.ChangeArmyActionCheckAsk.Builder checkAsk = SsSceneCityArmy.ChangeArmyActionCheckAsk.newBuilder()
                    .setPlayerId(playerEntity.getPlayerId())
                    .setMsgBytes(msg.toByteString());
            SsSceneCityArmy.ChangeArmyActionCheckAns checkAns = playerActor.callCurScene(checkAsk.build());

            // 体力消耗
            int monsterNeedEnergy = checkAns.getNeedEnergy();
            if (monsterNeedEnergy > 0) {
                realNeedEnergy = (int) PlayerAddCalc.getKillMonsterEnergyCost(playerEntity, monsterNeedEnergy, checkAns.getMainHeroId(), checkAns.getDeputyHeroId(), checkAns.getKillStreak());
                String energyCostReason = getEnergyCostReasonByAction(actionInfo.getArmyActionType());
                if (!playerEntity.getEnergyComponent().hasEnough(realNeedEnergy)) {
                    throw new GeminiException(ErrorCode.MONSTER_ENERGY_NOT_ENOUGH);
                }
                // 这里如果后面异常没有回滚体力
                playerEntity.getEnergyComponent().consumeEnergy(realNeedEnergy, false, energyCostReason, checkAns.getMonsterId());
            }
        }
        // 使用了byte
        ChangePlayerArmyActionAsk.Builder call = ChangePlayerArmyActionAsk.newBuilder();
        call.setPlayerId(playerEntity.getEntityId())
                .setMsgBytes(msg.toByteString())
                .setCostEnergy(realNeedEnergy);
        ChangePlayerArmyActionAns ans = playerActor.callCurScene(call.build());
        // s2c
        Player_ChangeArmyAction_S2C.Builder builder = Player_ChangeArmyAction_S2C.newBuilder();
        if (ans.hasPoint()) {
            builder.getPointBuilder().setX(ans.getPoint().getX()).setY(ans.getPoint().getY());
        }
        return builder.build();
    }

    /**
     * 根据行为确定体力扣减reason
     */
    private String getEnergyCostReasonByAction(ArmyActionType armyActionType) {
        if (armyActionType == ArmyActionType.AAT_PICK_UP) {
            return "pick_up";
        }
        return "attack_monster";
    }

    @CommandMapping(code = MsgType.PLAYER_FORCEDDEFEATARMY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ForcedDefeatArmy_C2S msg) {
        playerEntity.getTroopFormationComponent().forcedDefeatArmy(msg.getArmyId());
        return Player_ForcedDefeatArmy_S2C.getDefaultInstance();
    }

    /**
     * 查询行军路径
     */
    @CommandMapping(code = MsgType.PLAYER_SEARCHWALKPATH_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SearchWalkPath_C2S msg) {
        PointPB srcPoint = msg.getSrcPoint();
        PointPB endPoint = msg.getEndPoint();
        // 判断坐标合法性  只能做负值过滤  拿不到mapId, 无法做大于过滤
        if (srcPoint.getX() <= 0 || srcPoint.getY() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (msg.getRallyId() == 0 && msg.getTargetId() == 0) {
            if (endPoint.getX() <= 0 || endPoint.getY() <= 0) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }
        SearchPathAsk.Builder call = SearchPathAsk.newBuilder().setPlayerId(playerEntity.getPlayerId());
        call.getSrcBuilder().setX(srcPoint.getX()).setY(srcPoint.getY());
        if (msg.getRallyId() == 0) {
            call.setTargetId(msg.getTargetId()).getEndBuilder().setX(endPoint.getX()).setY(endPoint.getY());
        }
        call.setRallyId(msg.getRallyId());
        SearchPathAns ans = playerEntity.ownerActor().callCurScene(call.build());
        PointListPB.Builder pointList = PointListPB.newBuilder();
        for (Struct.Point p : ans.getPathList()) {
            pointList.addDatas(PointPB.newBuilder().setX(p.getX()).setY(p.getY()).build());
        }
        // s2c
        Player_SearchWalkPath_S2C.Builder builder = Player_SearchWalkPath_S2C.newBuilder();
        builder.setPointList(pointList);
        return builder.build();
    }

    /**
     * 是否可攻击目标
     */
    @CommandMapping(code = MsgType.PLAYER_CHECKCANATTACK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CheckCanAttack_C2S msg) {
        CheckCanBeAttackAsk.Builder call = CheckCanBeAttackAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        playerEntity.ownerActor().callCurScene(call.setTargetId(msg.getEntityId()).build());
        return Player_CheckCanAttack_S2C.newBuilder().setEntityId(msg.getEntityId()).build();
    }

    /**
     * 资源搜索
     */
    @CommandMapping(code = MsgType.PLAYER_SEARCHRESOURCE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerScene.Player_SearchResource_C2S msg) {
        PlayerScene.Player_SearchResource_S2C.Builder builder = PlayerScene.Player_SearchResource_S2C.newBuilder();
        if (!playerEntity.getTechComponent().checkResourceUnlock(msg.getCurrencyType().getNumber())) {
            builder.getErrorBuilder().setId(ErrorCode.COLLECT_RESOURCE_LOCK.getCodeId());
            return builder.build();
        }
        try {
            SsSceneCollect.SearchResourceAsk.Builder call = SsSceneCollect.SearchResourceAsk.newBuilder();
            call.setCurrencyType(msg.getCurrencyType());
            call.setLevel(msg.getLevel());
            call.setPlayerId(playerEntity.getEntityId());
            SsSceneCollect.SearchResourceAns ans = playerEntity.ownerActor().callCurScene(call.build());
            if (!ans.hasPoint()) {
                LOGGER.info("search failed SearchResource  {} {}", playerEntity, ErrorCode.COLLECT_SEARCH_RESOURCE_NULLTIPS.getDesc());
                builder.getErrorBuilder().setId(ErrorCode.COLLECT_SEARCH_RESOURCE_NULLTIPS.getCodeId());
                return builder.build();
            }
            builder.getPointBuilder().setX(ans.getPoint().getX())
                    .setY(ans.getPoint().getY()).build();
            builder.setEntityId(ans.getEntityId());
        } catch (GeminiException e) {
            builder.getErrorBuilder().setId(e.getCodeId());
            LOGGER.info("search failed SearchResource  {} {}", playerEntity, String.valueOf(builder.getErrorBuilder().getId()));
            return builder.build();
        }
        return builder.build();
    }

}

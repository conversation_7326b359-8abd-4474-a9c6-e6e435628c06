package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.SpyPlaneActionEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.fog.FogMapResService;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstConpensationTemplate;
import res.template.TransportPlaneTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.constant.Constants.CONVERT_TEN_THOUSAND_POINTS;
import static com.yorha.common.enums.error.ErrorCode.LOGISTICS_UNSUPPORT_ACTION;
import static com.yorha.common.enums.error.ErrorCode.PARAM_PARAMETER_EXCEPTION;
import static com.yorha.common.enums.statistic.StatisticEnum.SPY_PLANE_EXPLORE_NUM;
import static com.yorha.proto.CommonEnum.Reason.ICR_SPYPLANE;
import static com.yorha.proto.CommonEnum.SpyPlaneActionType.SPAT_EXPLORE;

/**
 * 飞机相关组件
 *
 * <AUTHOR>
 */
public class PlayerPlaneComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPlaneComponent.class);

    private final List<ActorTimer> scheduleTaskList = Lists.newArrayList();
    // 所有侦察机的模拟移动数据
    private final Map<Long, MoveData> spyMoveIndexMap = new HashMap<>();

    // 模拟飞行的移动数据(纯内存)
    private class MoveData {
        private final long planeId;
        private final Map<Integer, Long> arriveTimeMap;

        MoveData(long planeId, Map<Integer, Long> arriveTimeMap) {
            this.planeId = planeId;
            this.arriveTimeMap = arriveTimeMap;

        }

        private int nextIndex = 0;
        private ActorTimer timer;


        public void setNextIndex(int nextIndex) {
            this.nextIndex = nextIndex;
        }

        public int getNextIndex() {
            return this.nextIndex;
        }

        public Map<Integer, Long> getArriveTimeMap() {
            return arriveTimeMap;
        }

        public void startMove() {
            if (timer != null) {
                timer.cancel();
            }
            timer = ownerActor().addRepeatTimer(getPlayerId() + "-" + planeId, TimerReasonType.SIMULATE_SPY_FLY, () -> {
                simulateSpyFly(planeId);
            }, 1, 1000, TimeUnit.MILLISECONDS);
        }

        public void cancelTimer() {
            if (timer == null) {
                return;
            }
            timer.cancel();
            timer = null;
        }
    }

    public PlayerPlaneComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        super.postLogin(ans);
        checkTransportPlaneState();
        // 消费过期的系统援助任务
        consumeOverTimeSysAssistTask();
        final List<Long> scenePlaneList = ans.getPlaneListList();
        LOGGER.info("PlayerPlaneComponent postLogin scene planeList {}", scenePlaneList);
        // 处理离线时的侦察机迷雾探开
        for (PlayerSpyPlaneProp planeProp : getProp().getPlayerSpyPlane().values()) {
            final long spyPlaneId = planeProp.getId();
            if (planeProp.getStatus() != CommonEnum.PlaneStatus.PS_PLANE_OUT) {
                // 玩家身上如果飞机为空闲状态，但是地图上有飞机，记录日志
                if (scenePlaneList.contains(spyPlaneId)) {
                    planeProp.setStatus(CommonEnum.PlaneStatus.PS_PLANE_OUT);
                    LOGGER.error("PlayerPlaneComponent postLogin spy plane on scene status error {} {}", spyPlaneId, planeProp.getStatus());
                }
                continue;
            }
            // 释放地图上没有，但是玩家侧状态为出征中的飞机
            // 1. 在gvg副本期间，只回收大世界异常未回收的飞机
            // 2. 不在gvg时，回收所有异常未回收的飞机（包括副本内的飞机）
            if (planeProp.getSceneId() == getOwner().getMainSceneId()) {
                if (!scenePlaneList.contains(spyPlaneId)) {
                    releaseSpyPlane(spyPlaneId);
                    LOGGER.warn("PlayerPlaneComponent postLogin spy plane status error without scene plane {} {}", spyPlaneId, planeProp.getStatus());
                    continue;
                }
            }
            refreshMoveData(spyPlaneId, true, true);
        }
    }

    @Override
    public void afterLogout() {
        super.afterLogout();
        for (ActorTimer scheduleTask : scheduleTaskList) {
            if (scheduleTask == null) {
                continue;
            }
            scheduleTask.cancel();
        }

        for (MoveData moveData : spyMoveIndexMap.values()) {
            moveData.cancelTimer();
        }
    }

    private void checkTransportPlaneState() {
        for (PlayerTransportPlaneProp transportPlaneProp : getProp().getPlayerTransportPlane().values()) {
            // 占用中的运输机返航检测
            if (transportPlaneProp.getStatus() == CommonEnum.PlaneStatus.PS_PLANE_OUT && TimeUtils.isAfterNow(transportPlaneProp.getReturnOverMs())) {
                long delayMs = transportPlaneProp.getReturnOverMs() - SystemClock.now();
                long planeId = transportPlaneProp.getId();
                if (delayMs > 0) {
                    addTransportReturnSchedule(planeId, delayMs);
                } else {
                    releaseTransportPlane(planeId);
                }
            }
        }
    }


    /**
     * 占用侦察机
     */
    private void occupySpyPlane(PlayerSpyPlaneProp playerSpyPlane, long sceneId) {
        updateSpyPlaneStatus(playerSpyPlane, CommonEnum.PlaneStatus.PS_PLANE_OUT, sceneId);
    }

    public int getPlaneModel(long spyPlaneId) {
        PlayerSpyPlaneProp playerSpyPlaneV = getPlayerSpyPlaneProp(spyPlaneId);
        return playerSpyPlaneV.getPlaneModel();
    }

    /**
     * 创建地图侦察机对象
     */
    public void createSpyPlaneEntity(SsScenePlane.CreateSpyPlaneAsk ask) {
        PlayerSpyPlaneProp playerSpyPlaneV = getPlayerSpyPlaneProp(ask.getSpyInfo().getSpyPlaneId());
        if (isOccupyStatus(playerSpyPlaneV.getId(), playerSpyPlaneV.getStatus())) {
            // 侦察机已被使用
            throw new GeminiException(ErrorCode.PLANE_NO_SPY_PLANE.getCodeId());
        }
        //玩家侧校验
        checkPlayerSpyPlanePre(ask.getSpyInfo());

        //地图侧校验
        SsScenePlane.CheckMapCreateSpyPlaneAsk.Builder builder = SsScenePlane.CheckMapCreateSpyPlaneAsk.newBuilder();
        builder.setPlayerId(ask.getPlayerId())
                .setSpyInfo(ask.getSpyInfo().toBuilder()
                        .setSpyModel(playerSpyPlaneV.getPlaneModel()));

        SsScenePlane.CheckMapCreateSpyPlaneAns ans = ownerActor().callCurScene(builder.build());
        if (!ErrorCode.isOK(ans.getErrorCode())) {
            throw new GeminiException(ans.getErrorCode());
        }
        //玩家侧校验
        checkPlayerSpyPlaneAfter(ask.getSpyInfo(), ans);
        //cost
        if (ans.getCurrency().getCount() > 0) {
            CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(ans.getCurrency().getType());
            if (currencyType == null) {
                throw new GeminiException("player :{} createSpyPlane unKnown currency :{}", getPlayerId(), ans.getCurrency().getType());
            }
            getOwner().getPurseComponent().consume(currencyType, ans.getCurrency().getCount(), ICR_SPYPLANE, "");
        }

        // 锁定状态
        final long curSceneId = getOwner().getCurSceneId();
        occupySpyPlane(playerSpyPlaneV, curSceneId);

        // 通知大世界创建侦察机entity
        SsScenePlane.CreateSpyPlaneAns createSpyPlaneAns;
        try {
            createSpyPlaneAns = ownerActor().callCurScene(ask);
        } catch (Exception e) {
            // 释放飞机锁定
            releaseSpyPlane(ask.getSpyInfo().getSpyPlaneId());
            throw e;
        }
        if (createSpyPlaneAns.getPlaneEntityId() > 0) {
            if (ask.getSpyInfo().getActionType() == SPAT_EXPLORE) {
                getOwner().getStatisticComponent().recordSingleStatistic(SPY_PLANE_EXPLORE_NUM, 1);
            }
            new SpyPlaneActionEvent(getOwner(), ask.getSpyInfo().getActionType()).dispatch();
            if (ans.hasBuildType()) {
                getOwner().getTaskComponent().updateExploreMapBuildingTask(ans.getBuildType(), false);
            }
        }
    }

    public void releasePlane(SsPlayerMisc.ReleasePlaneCmd ask) {
        LOGGER.info("release plane. ask:{}", ask);
        if (ask.getTransportPlaneId() > 0) {
            getOwner().getPlaneComponent().releaseTransportPlane(ask.getTransportPlaneId());
        }
        if (ask.getSpyPlaneId() > 0) {
            getOwner().getPlaneComponent().releaseSpyPlane(ask.getSpyPlaneId());
        }
    }

    /**
     * 释放运输机
     */
    public void releaseTransportPlane(long planeId) {
        PlayerTransportPlaneProp transportPlaneProp = getPlayerTransportPlaneProp(planeId);
        TransportPlaneTemplate transportTemplate = ResHolder.getInstance().getValueFromMap(TransportPlaneTemplate.class, transportPlaneProp.getPlaneModel());
        long cdResetTimeMs = TimeUtils.second2Ms(transportTemplate.getCd()) + SystemClock.now();
        transportPlaneProp.setCdResetMs(cdResetTimeMs);
        LOGGER.info("transport enter cd time");
        updateTransportPlane(transportPlaneProp, CommonEnum.PlaneStatus.PS_PLANE_IDLE);
    }

    /**
     * 占用侦察机
     */
    public void releaseSpyPlane(long planeId) {
        refreshMoveData(planeId, true, false);
        PlayerSpyPlaneProp spyPlaneProp = getPlayerSpyPlaneProp(planeId);
        updateSpyPlaneStatus(spyPlaneProp, CommonEnum.PlaneStatus.PS_PLANE_IDLE, 0);
        if (spyMoveIndexMap.containsKey(planeId)) {
            spyMoveIndexMap.get(planeId).cancelTimer();
            spyMoveIndexMap.remove(planeId);
            resetProp(planeId);
        }
    }

    /**
     * 更新侦察机状态
     */
    private CommonEnum.PlaneStatus updateSpyPlaneStatus(PlayerSpyPlaneProp playerSpyPlaneV, CommonEnum.PlaneStatus newStatus, long sceneId) {
        final CommonEnum.PlaneStatus oldStatus = playerSpyPlaneV.getStatus();
        final long oldSceneId = playerSpyPlaneV.getSceneId();
        playerSpyPlaneV.setStatus(newStatus);
        playerSpyPlaneV.setSceneId(sceneId);
        LOGGER.info("PlayerPlaneComponent updateSpyPlaneStatus. Status:{} -> {} scene:{} -> {}", oldStatus, newStatus, oldSceneId, sceneId);
        return oldStatus;
    }

    /**
     * 更新运输机状态
     */
    private CommonEnum.PlaneStatus updateTransportPlane(PlayerTransportPlaneProp transportPlaneProp, CommonEnum.PlaneStatus newStatus) {
        CommonEnum.PlaneStatus oldStatus = transportPlaneProp.getStatus();
        transportPlaneProp.setStatus(newStatus);
        LOGGER.info("updateTransportPlane. oldStatus:{}", oldStatus);
        return oldStatus;
    }


    public int checkCreateSpyPlane(StructMsg.SpyInfo spyInfo) {
        PlayerSpyPlaneProp playerSpyPlaneV = getPlayerSpyPlaneProp(spyInfo.getSpyPlaneId());
        if (isOccupyStatus(playerSpyPlaneV.getId(), playerSpyPlaneV.getStatus())) {
            // 侦察机已被使用
            throw new GeminiException(ErrorCode.PLANE_NO_SPY_PLANE.getCodeId());
        }

        checkPlayerSpyPlanePre(spyInfo);

        //call scene check
        SsScenePlane.CheckMapCreateSpyPlaneAsk.Builder builder = SsScenePlane.CheckMapCreateSpyPlaneAsk.newBuilder();

        builder.setSpyInfo(spyInfo.toBuilder().setSpyModel(playerSpyPlaneV.getPlaneModel()));

        SsScenePlane.CheckMapCreateSpyPlaneAns ans = ownerActor().callCurScene(builder.build());
        if (!ErrorCode.isOK(ans.getErrorCode())) {
            return ans.getErrorCode();
        }
        //玩家侧校验
        checkPlayerSpyPlaneAfter(spyInfo, ans);
        if (ans.getCurrency().getCount() <= 0) {
            return ans.getErrorCode();
        }
        ErrorCode currencyErrorCode = getOwner().getPurseComponent().isEnough(CommonEnum.CurrencyType.forNumber(ans.getCurrency().getType()), ans.getCurrency().getCount());
        if (!ErrorCode.isOK(currencyErrorCode.getCode())) {
            return currencyErrorCode.getCodeId();
        }
        return ans.getErrorCode();
    }

    public void changeSpyPlaneAction(SsScenePlane.ChangeActionSpyPlaneAsk ask) {
        PlayerSpyPlaneProp playerSpyPlaneProp = getPlayerSpyPlaneProp(ask.getSpyInfo().getSpyPlaneId());
        StructMsg.SpyInfo.Builder spyInfoBuilder = ask.getSpyInfo().toBuilder().setSpyModel(playerSpyPlaneProp.getPlaneModel());
        if (playerSpyPlaneProp.getStatus() != CommonEnum.PlaneStatus.PS_PLANE_OUT) {
            throw new GeminiException(ErrorCode.PLANE_PLANE_DOES_NOT_EXIST);
        }
        //玩家侧校验
        checkPlayerSpyPlanePre(ask.getSpyInfo());
        //地图侧校验
        SsScenePlane.CheckMapCreateSpyPlaneAsk.Builder builder = SsScenePlane.CheckMapCreateSpyPlaneAsk.newBuilder();
        builder.setPlayerId(ask.getPlayerId())
                .setSpyInfo(spyInfoBuilder);
        SsScenePlane.CheckMapCreateSpyPlaneAns ans = ownerActor().callCurScene(builder.build());
        if (!ErrorCode.isOK(ans.getErrorCode())) {
            throw new GeminiException(ans.getErrorCode());
        }
        //玩家侧校验
        checkPlayerSpyPlaneAfter(ask.getSpyInfo(), ans);
        //cost
        if (ans.getCurrency().getCount() > 0) {
            CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(ans.getCurrency().getType());
            if (currencyType == null) {
                throw new GeminiException("player :{} createSpyPlane unKnown currency :{}", getPlayerId(), ans.getCurrency().getType());
            }
            getOwner().getPurseComponent().consume(currencyType, ans.getCurrency().getCount(), ICR_SPYPLANE, "");
        }
        //行为变更
        SsScenePlane.ChangeActionSpyPlaneAns changeActionSpyPlaneAns = ownerActor().callCurScene(ask);
        //飞机改变行为失败
        if (changeActionSpyPlaneAns.getPlaneEntityId() <= 0) {
            LOGGER.debug("player :{} spyPlane: {} changeSpyPlaneAction fail", getPlayerId(), changeActionSpyPlaneAns.getPlaneEntityId());
        } else {
            if (ask.getSpyInfo().getActionType() == SPAT_EXPLORE) {
                getOwner().getStatisticComponent().recordSingleStatistic(SPY_PLANE_EXPLORE_NUM, 1);
            }
            new SpyPlaneActionEvent(getOwner(), ask.getSpyInfo().getActionType()).dispatch();
            if (ans.hasBuildType()) {
                getOwner().getTaskComponent().updateExploreMapBuildingTask(ans.getBuildType(), false);
            }
        }
    }

    /**
     * 占用侦察机前置检测
     */
    private void checkPlayerSpyPlanePre(StructMsg.SpyInfo spyInfo) {
        //迷雾探索
        if (spyInfo.getActionType() == SPAT_EXPLORE) {
            if (!spyInfo.hasPoint()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "checkPlayerSpyPlane with error param point");
            }
            if (!spyInfo.hasSrcPoint()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "checkPlayerSpyPlane with error param spoint");
            }
            FogMapResService fogMapResService = ResHolder.getResService(FogMapResService.class);
            if (spyInfo.getPoint().getX() > fogMapResService.getWidth() || spyInfo.getPoint().getY() > fogMapResService.getHeight()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "checkPlayerSpyPlane with invaild point " + spyInfo.getPoint());
            }
            if (spyInfo.getSrcPoint().getX() > fogMapResService.getWidth() || spyInfo.getSrcPoint().getY() > fogMapResService.getHeight()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "checkPlayerSpyPlane with invaild spoint " + spyInfo.getSrcPoint());
            }
        }
    }

    private void checkPlayerSpyPlaneAfter(StructMsg.SpyInfo spyInfo, SsScenePlane.CheckMapCreateSpyPlaneAns ans) {
        
    }

    private boolean isOccupyStatus(long planeId, CommonEnum.PlaneStatus status) {
        if (CommonEnum.PlaneStatus.PS_PLANE_IDLE == status) {
            return false;
        }
        if (CommonEnum.PlaneStatus.PS_PLANE_OUT == status) {
            return true;
        }
        LOGGER.error("unknown status. planeId:{}, status:{}", planeId, status);
        return false;
    }

    /**
     * 解除战机弹框提醒
     */
    public void promptForPlaneUnlock(long planeId) {
        PlayerTransportPlaneProp transportPlaneProp = getProp().getPlayerTransportPlaneV(planeId);
        if (transportPlaneProp != null) {
            LOGGER.info("prompt for transport plane：{} unlock", planeId);
            transportPlaneProp.setIsNeedReminder(false);
            return;
        }
        PlayerSpyPlaneProp playerSpyPlaneProp = getProp().getPlayerSpyPlaneV(planeId);
        if (playerSpyPlaneProp != null) {
            LOGGER.info("prompt for spy plane：{} unlock", planeId);
            playerSpyPlaneProp.setIsNeedReminder(false);
        }
    }

    private PlayerSpyPlaneProp getPlayerSpyPlaneProp(long planeId) {
        PlayerSpyPlaneProp playerSpyPlane = getProp().getPlayerSpyPlaneV(planeId);
        if (playerSpyPlane == null) {
            throw new GeminiException(ErrorCode.PLANE_PLANE_DOES_NOT_EXIST);
        }
        return playerSpyPlane;
    }

    private PlayerTransportPlaneProp getPlayerTransportPlaneProp(long planeId) {
        PlayerTransportPlaneProp transportPlane = getProp().getPlayerTransportPlaneV(planeId);
        if (transportPlane == null) {
            throw new GeminiException(ErrorCode.PLANE_PLANE_DOES_NOT_EXIST);
        }
        return transportPlane;
    }

    private PlayerPlaneModelProp getProp() {
        return getOwner().getProp().getPlayerPlaneModel();
    }

    public long getMaxCdTransport() {
        long now = SystemClock.now();
        long maxCdMsPlaneId = 0;
        long maxCdMs = 0;
        for (PlayerTransportPlaneProp value : getProp().getPlayerTransportPlane().values()) {
            if (value.getCdResetMs() > now && value.getCdResetMs() > maxCdMs) {
                maxCdMs = value.getCdResetMs();
                maxCdMsPlaneId = value.getId();
            }
        }
        return maxCdMsPlaneId;
    }

    public void resetTransportCd(long maxCdTransport) {
        long now = SystemClock.now();
        PlayerTransportPlaneProp transportPlaneProp = getPlayerTransportPlaneProp(maxCdTransport);
        LOGGER.info("transport cd reset. cdMs:{}", transportPlaneProp.getCdResetMs() - now);
        transportPlaneProp.setCdResetMs(now);
    }

    public void returnTransportPlane(long planeId) {
        PlayerTransportPlaneProp transportPlaneProp = getPlayerTransportPlaneProp(planeId);
        int returnTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getReturnTime();
        long returnMs = TimeUtils.second2Ms(returnTime);
        if (returnMs > 0) {
            long now = SystemClock.now();
            transportPlaneProp.setReturnOverMs(now + returnMs);
            addTransportReturnSchedule(planeId, returnMs);
        } else {
            LOGGER.error("config error. returnTime:{}", returnTime);
        }
    }

    private void addTransportReturnSchedule(long planeId, long delayMs) {
        ActorTimer scheduleTask = ownerActor().addTimer(getPlayerId() + "-" + planeId, TimerReasonType.RELEASE_TRANSPORT_PLANE, () -> {
            releaseTransportPlane(planeId);
        }, delayMs, TimeUnit.MILLISECONDS);
        if (scheduleTask == null) {
            return;
        }
        scheduleTaskList.add(scheduleTask);
    }

    /**
     * 拥有cd中的指定运输机
     */
    public boolean hasCdTransportPlane(long transportId) {
        return getProp().getPlayerTransportPlaneV(transportId).getCdResetMs() > SystemClock.now();
    }

    public void checkLogisticsAction(StructMsg.LogisticsInfo info) {
        switch (info.getActionType()) {
            case LAT_RESOURCE_ASSIST: {
                // 目标id非法
                if (info.getTargetId() <= 0) {
                    throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "checkLogisticsAction type: " + info.getActionType() + "invaild targetId: " + info.getTargetId());
                }
                Map<Integer, Long> resource = getOwner().getResourceProtectComponent().getPlunderResource();
                // 可掠夺资源校验
                int totalCurrencyNum = 0;
                for (StructPB.CurrencyPB currency : info.getResourceList()) {
                    // 可掠夺资源库存中这个资源品项已经没有库存了
                    if (!resource.containsKey(currency.getType())) {
                        throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "checkLogisticsAction not have currency: " + currency.getType());
                    }
                    // 可掠夺资源库存中这个资源品项库存不足
                    long currencyNum = resource.getOrDefault(currency.getType(), 0L);
                    if (currencyNum < currency.getCount()) {
                        throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "checkLogisticsAction not have enough currency: " + currency.getType() + " have: " + currencyNum + " request: " + currency.getCount());
                    }
                    totalCurrencyNum += currency.getCount();
                }
                // scene上校验
                SsScenePlane.CheckLogisticsActionAsk.Builder ask = SsScenePlane.CheckLogisticsActionAsk.newBuilder();
                ask.setPlayerId(getPlayerId())
                        .setLogisticsInfo(info);
                SsScenePlane.CheckLogisticsActionAns ans = getOwner().ownerActor().callCurScene(ask.build());
                if (!ErrorCode.isOK(ans.getErrorCode())) {
                    throw new GeminiException(ans.getErrorCode());
                }
                // 运输容量检查
                long capacity = getLogisticsCapacity();
                if (totalCurrencyNum > capacity) {
                    throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "checkLogisticsAction over capacity: " + capacity + " request: " + totalCurrencyNum);
                }
                break;
            }
            default:
                throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "checkLogisticsAction unsupport action type: " + info.getActionType());
        }
    }

    /**
     * 获取运输容量
     */
    private long getLogisticsCapacity() {
        return getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_LOGISTICS_CAPACITY);
    }

    /**
     * 获取运输税率
     */
    private double getLogisticsTax(long count) {
        double result = getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_LOGISTICS_TAX);
        result = result / CONVERT_TEN_THOUSAND_POINTS;
        result = count * result / (1 - result);
        return result;
    }

    public void changeLogisticsAction(StructMsg.LogisticsInfo info) {
        switch (info.getActionType()) {
            case LAT_RESOURCE_ASSIST: {
                changeLogisticsAssist(info);
                break;
            }
            case LAT_RETURN: {
                SsScenePlane.ChangeLogisticActionAsk.Builder ask = SsScenePlane.ChangeLogisticActionAsk.newBuilder();
                ask.setPlayerId(getPlayerId())
                        .setLogisticsInfo(info);
                ownerActor().callCurScene(ask.build());
                break;
            }
            default:
                throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "handleAction unsupport action type: " + info.getActionType());
        }

    }

    /**
     * 增加一个系统援助的请求
     */
    public void addSysAssistTask(Struct.SysAssistInfo info) {
        Int64SysAssistInfoMapProp map = getProp().getSysAssistInfos();
        // 防重入
        if (map.containsKey(info.getExpiration())) {
            throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "addSysAssistTask already has this SysAssistTask id: " + info.getExpiration());
        }
        // 检测缓存是否已满
        if (map.size() >= ResHolder.getConsts(ConstConpensationTemplate.class).getRssAssistSystemSaveNum()) {
            throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "addSysAssistTask already full: " + map.size());
        }
        SysAssistInfoProp prop = new SysAssistInfoProp();
        prop.mergeFromSs(info);
        map.put(info.getExpiration(), prop);
    }

    public void consumeOverTimeSysAssistTask() {
        Int64SysAssistInfoMapProp map = getProp().getSysAssistInfos();
        Iterator<Long> keys = map.keySetIterator();
        long now = SystemClock.now();

        for (; keys.hasNext(); ) {
            long key = keys.next();
            if (now < key) {
                continue;
            }
            SysAssistInfoProp prop = map.get(key);
            keys.remove();
            consumeSysAssistTask(prop);
        }
    }

    public void consumeAllSysAssistTask() {
        Int64SysAssistInfoMapProp map = getProp().getSysAssistInfos();
        Iterator<Long> keys = map.keySetIterator();
        for (; keys.hasNext(); ) {
            long key = keys.next();
            SysAssistInfoProp prop = map.get(key);
            keys.remove();
            consumeSysAssistTask(prop);
        }
    }

    /**
     * 消费系统援助任务
     */
    public void consumeSysAssistTask(SysAssistInfoProp prop) {
        StringJoiner assistStr = new StringJoiner(",");
        for (String assist : prop.getAssistPlayerName()) {
            assistStr.add(assist);
        }
        String str = StringUtils.isBlank(prop.getTargetClanShortName()) ? "" : "[" + prop.getTargetClanShortName() + "]";
        str = str + prop.getTargetPlayerName();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(prop.getMailId());
        mailSendParams.getContentBuilder()
                .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder()
                .getParamsBuilder()
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(str))
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(assistStr.toString()));
        for (ItemPairProp itemPairProp : prop.getItemReward()) {
            mailSendParams.getItemRewardBuilder().addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(itemPairProp.getItemTemplateId()).setCount(itemPairProp.getCount()).build());
        }
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(getOwner().getPlayerId())
                .setZoneId(getOwner().getZoneId())
                .build();
        this.getOwner().getMailComponent().sendPersonalMail(receiver, mailSendParams.build());
    }

    /**
     * 主动援助
     *
     * @param info
     */
    private void changeLogisticsAssist(StructMsg.LogisticsInfo info) {
        SsScenePlane.CreateLogisticsPlaneAsk.Builder ask = SsScenePlane.CreateLogisticsPlaneAsk.newBuilder();
        ask.setPlayerId(getPlayerId())
                .setLogisticsInfo(info);
        // 计算运输数量与税率
        AssetPackage.Builder costBuilder = AssetPackage.builder();
        for (StructPB.CurrencyPB currency : info.getResourceList()) {
            if (currency.getCount() <= 0) {
                throw new GeminiException(PARAM_PARAMETER_EXCEPTION, "currency must > 0");
            }
            long taxNum = (long) getLogisticsTax(currency.getCount());
            costBuilder.plusCurrency(currency.getType(), currency.getCount() + taxNum);
            // 运输资源
            ask.addResources(Struct.Currency.newBuilder().setType(currency.getType()).setCount(currency.getCount()));
            if (taxNum <= 0) {
                continue;
            }
            // 税率
            ask.addTax(Struct.Currency.newBuilder().setType(currency.getType()).setCount(taxNum));
        }
        // 扣资源
        getOwner().consume(costBuilder.build(), CommonEnum.Reason.ICR_LOGISTICS);
        try {
            // 创建运输机
            ownerActor().callCurScene(ask.build());
        } catch (Exception e) {
            // 返还资源
            getOwner().getAssetComponent().give(costBuilder.build(), CommonEnum.Reason.ICR_LOGISTICS);
            throw e;
        }
    }

    public void handleSpyMove(SsPlayerMisc.SpyMoveCmd ask) {
        SimulateMoveProp prop = getPlayerSpyPlaneProp(ask.getPlaneId()).getSimulateMove();
        Point oldPoint = Point.valueOf(prop.getCurPoint().getX(), prop.getCurPoint().getY());
        Point newPoint = Point.valueOf(ask.getCurPoint().getX(), ask.getCurPoint().getY());
        LOGGER.info("PlayerPlaneComponent handleSpyMove {} {} {} {}", ask.getPlaneId(), ask.getPointListCount(), oldPoint, newPoint);

        if (prop.getCurPoint().getX() > 0) {
            if (oldPoint.equals(newPoint)) {
                return;
            }
            LOGGER.debug("handleSpyMove old: {} new: {}", oldPoint, newPoint);
        }
        resetProp(ask.getPlaneId());
        if (spyMoveIndexMap.containsKey(ask.getPlaneId())) {
            spyMoveIndexMap.get(ask.getPlaneId()).cancelTimer();
        }
        prop.getCurPoint().setX(ask.getCurPoint().getX()).setY(ask.getCurPoint().getY());
        prop.setStartTs(ask.getStartTs())
                .setEndTs(ask.getEndTs())
                .setMoveSpeed(ask.getMoveSpeed());
        for (Struct.Point point : ask.getPointListList()) {
            PointProp pointProp = new PointProp();
            pointProp.setX(point.getX())
                    .setY(point.getY());
            prop.addPointList(pointProp);
        }
        refreshMoveData(ask.getPlaneId(), false, true);
    }

    private void refreshMoveData(long planeId, boolean isRestore, boolean needTimerMove) {
        SimulateMoveProp prop = getPlayerSpyPlaneProp(planeId).getSimulateMove();
        int pointNum = prop.getPointListSize();
        if (pointNum <= 0) {
            return;
        }
        // 计算到达每个路点的时间
        long curTime = SystemClock.now();
        Map<Integer, Long> arriveTimeMap = new HashMap<>();
        PointProp curPointProp = prop.getCurPoint();
        PointProp lastPoint = prop.getPointListIndex(0);
        long lastTime = prop.getStartTs();
        for (int i = 0; i < pointNum; i++) {
            PointProp nextPoint = prop.getPointListIndex(i);
            double distance = Point.calDisBetweenTwoPoint(Point.valueOf(lastPoint.getX(), lastPoint.getY()), Point.valueOf(nextPoint.getX(), nextPoint.getY()));
            long addCostTime = (long) (distance / prop.getMoveSpeed() * 1000);
            arriveTimeMap.put(i, lastTime + addCostTime);
            lastTime += addCostTime;
            lastPoint = nextPoint;
        }
        MoveData data = new MoveData(planeId, arriveTimeMap);
        LOGGER.info("PlayerPlaneComponent refreshMoveData {} {} {} {} {} {} {}", planeId, isRestore, curPointProp, arriveTimeMap.keySet(), arriveTimeMap.values(), curTime, needTimerMove);
        if (spyMoveIndexMap.containsKey(planeId)) {
            spyMoveIndexMap.get(planeId).cancelTimer();
        }
        spyMoveIndexMap.put(planeId, data);
        // 计算当前所处路段index
        if (!isRestore) {
            // 更新当前所处路段的index
            int curIndex = 0;
            while (curIndex < pointNum && arriveTimeMap.get(curIndex) <= curTime) {
                curIndex++;
            }
            if (curIndex < pointNum) {
                prop.setCurIndex(curIndex);
            }
        } else {
            // 计算离线期间应当飞完的路段
            Point next = calCurPoint(SystemClock.now(), planeId, prop);
            LOGGER.info("PlayerPlaneComponent refreshMoveData isRestore {} {}", planeId, next);
            // 同一个路点段内
            int curIndex = prop.getCurIndex();
            int nextIndex = data.getNextIndex();
            if (nextIndex != curIndex) {
                // 跨路点了
                if ((curIndex < 0) || (curIndex >= pointNum) || (nextIndex < 0) || (nextIndex > pointNum)) {
                    LOGGER.warn("PlayerPlaneComponent refreshMoveData unknown error {} {}", curIndex, nextIndex);
                }
                Point curPoint;
                for (int i = curIndex; i < nextIndex; i++) {
                    PointProp nextPointProp = prop.getPointListIndex(i);
                    curPoint = Point.valueOf(nextPointProp.getX(), nextPointProp.getY());
                    curPointProp.setX(curPoint.getX()).setY(curPoint.getY());
                }
            }
            curPointProp.setX(next.getX()).setY(next.getY());
        }
        if (needTimerMove) {
            // 开始模拟飞行
            data.startMove();
        }
    }

    private void resetProp(long planeId) {
        SimulateMoveProp prop = getPlayerSpyPlaneProp(planeId).getSimulateMove();
        prop.clearPointList();
        prop.getCurPoint().setX(0).setY(0);
        prop.setStartTs(0)
                .setEndTs(0)
                .setMoveSpeed(0)
                .setCurIndex(0);
    }

    /**
     * 模拟侦察机的位移
     *
     * @param planeId 侦察机id
     */
    private void simulateSpyFly(long planeId) {
        SimulateMoveProp prop = getPlayerSpyPlaneProp(planeId).getSimulateMove();
        // 计算当前飞机坐标，解锁迷雾
        PointProp curPointProp = prop.getCurPoint();
        MoveData data = spyMoveIndexMap.get(planeId);
        Point next = calCurPoint(SystemClock.now(), planeId, prop);
        int nextIndex = data.getNextIndex();
        curPointProp.setX(next.getX()).setY(next.getY());
        prop.setCurIndex(nextIndex);
        if (SystemClock.now() >= prop.getEndTs()) {
            data.cancelTimer();
            spyMoveIndexMap.remove(planeId);
            resetProp(planeId);
        }
    }

    /**
     * 计算获取当前点
     */
    public Point calCurPoint(long curTime, long planeId, SimulateMoveProp prop) {
        PointProp curPoint = prop.getCurPoint();
        Point cur = Point.valueOf(curPoint.getX(), curPoint.getY());
        if (prop.getPointListSize() <= 0) {
            LOGGER.warn("planeId:{} pointListSize:{}", planeId, prop.getPointList());
            return cur;
        }
        int pointNum = prop.getPointListSize();
        MoveData data = spyMoveIndexMap.get(planeId);
        Map<Integer, Long> moveArriveTime = data.getArriveTimeMap();
        if (moveArriveTime.size() != pointNum) {
            LOGGER.info("planeId:{} moveArriveTime {} pointList {}", planeId, moveArriveTime.keySet(), prop.getPointList());
            return cur;
        }
        if (prop.getEndTs() <= curTime) {
            PointProp pointProp = prop.getPointListIndex(prop.getPointListSize() - 1);
            data.setNextIndex(prop.getPointListSize() - 1);
            return Point.valueOf(pointProp.getX(), pointProp.getY());
        }
        if (curTime <= prop.getStartTs()) {
            PointProp pointProp = prop.getPointListIndex(0);
            data.setNextIndex(0);
            return Point.valueOf(pointProp.getX(), pointProp.getY());
        }
        // 找到前往的下一个坐标点
        int curIndex = prop.getCurIndex();
        int nextMoveToPointIndex = curIndex;

        for (int i = curIndex; i < pointNum; i++) {
            nextMoveToPointIndex = i;
            if (moveArriveTime.get(nextMoveToPointIndex) > curTime) {
                data.setNextIndex(nextMoveToPointIndex);
                break;
            }
        }

        if (nextMoveToPointIndex <= 0) {
            LOGGER.info("planeId:{} curIndex {} nextMoveToPointIndex {} {} {}", planeId, curIndex, nextMoveToPointIndex, curTime, moveArriveTime.values());
            return cur;
        }

        if (nextMoveToPointIndex >= pointNum) {
            LOGGER.info("planeId:{} curIndex {} nextMoveToPointIndex {} {}", planeId, curIndex, nextMoveToPointIndex, pointNum);
            return cur;
        }

        PointProp lastPoint = prop.getPointListIndex(nextMoveToPointIndex - 1);
        PointProp nextPoint = prop.getPointListIndex(nextMoveToPointIndex);
        long lastArriveTime = moveArriveTime.get(nextMoveToPointIndex - 1);
        long nextArriveTime = moveArriveTime.get(nextMoveToPointIndex);
        float lerp = (curTime - lastArriveTime) * 1.0f / (nextArriveTime - lastArriveTime);
        return Point.lerpPoint(Point.valueOf(lastPoint.getX(), lastPoint.getY()), Point.valueOf(nextPoint.getX(), nextPoint.getY()), lerp);
    }
}

package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.proto.Struct;

/**
 * 落堡（非新角色落堡）事件
 */
public class PlayerManualCityFallEvent extends PlayerTaskEvent {
    private final Struct.Point newPoint;

    public PlayerManualCityFallEvent(PlayerEntity entity, Struct.Point newPoint) {
        super(entity);
        this.newPoint = newPoint;
    }

    public Struct.Point getNewPoint() {
        return newPoint;
    }
}

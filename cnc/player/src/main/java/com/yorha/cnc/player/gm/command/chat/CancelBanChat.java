package com.yorha.cnc.player.gm.command.chat;


import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 禁言
 *
 * <AUTHOR>
 */
public class CancelBanChat implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        actor.getOrLoadChatPlayerEntity().getHandleChatComponent().cancelBanChat();
    }

    @Override
    public String showHelp() {
        return "CancelBanChat";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

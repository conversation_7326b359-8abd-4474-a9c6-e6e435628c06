package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.PlayerAllBattleEndEvent;
import com.yorha.cnc.player.gm.DebugSwitchCenter;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.HeroHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PlayerBattleComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerBattleComponent.class);

    public PlayerBattleComponent(PlayerEntity owner) {
        super(owner);
    }

    public Map<Long, SsPlayerMisc.PlunderResult> handleBePlundered(Map<Long, SsPlayerMisc.PlunderAsk> plunderAskMap) {
        Map<Long, SsPlayerMisc.PlunderResult> res = Maps.newHashMap();
        for (Map.Entry<Long, SsPlayerMisc.PlunderAsk> entry : plunderAskMap.entrySet()) {
            SsPlayerMisc.PlunderResult.Builder builder = SsPlayerMisc.PlunderResult.newBuilder()
                    .setPlunderAsk(entry.getValue());
            Pair<Map<Integer, Struct.Currency>, Map<Integer, Struct.Currency>> plunderRes = bePlundered(entry.getValue().getPlunderWeightMap());
            builder.getPlunderResBuilder().putAllDatas(plunderRes.getFirst());
            builder.getLossResBuilder().putAllDatas(plunderRes.getSecond());
            res.put(entry.getKey(), builder.build());
        }
        return res;
    }

    /**
     * 守城战败资源被掠夺
     *
     * @return <进攻方获得的资源, 主堡损失的资源>
     */
    private Pair<Map<Integer, Struct.Currency>, Map<Integer, Struct.Currency>> bePlundered(Map<Long, SsPlayerMisc.PlunderWeight> enemyWeight) {
        Map<CommonEnum.CurrencyType, Long> canBePlundered = getOwner().getResourceProtectComponent().getPlunderAmountWithResource();
        // 实际可掠夺资源量 = 可掠夺资源量 * （1 - 掠夺资源损失比例）结果向下取整
        float lossRatio = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getPlunderRatio();
        Map<CommonEnum.CurrencyType, Long> fixedRes = new HashMap<>();
        for (Map.Entry<CommonEnum.CurrencyType, Long> entry : canBePlundered.entrySet()) {
            long res = MathUtils.floorLong(entry.getValue() * (1 - lossRatio));
            fixedRes.put(entry.getKey(), res);
        }

        // 部队负重
        long totalWeight = enemyWeight.values()
                .stream()
                .filter(it -> it.getPlunderWeight() > 0)
                .mapToLong(SsPlayerMisc.PlunderWeight::getPlunderWeight)
                .sum();

        // 被掠夺的资源
        Map<CommonEnum.CurrencyType, Long> plunderedRes = new HashMap<>();
        calcPlunderedRes(totalWeight, fixedRes, plunderedRes);

        // 扣除损失的资源
        Map<CommonEnum.CurrencyType, Long> lossRes = calcLossRes(plunderedRes, canBePlundered);

        LOGGER.debug("player:{}, be plundered. ori res:{}, fixed res:{}, totalWeight:{}, plunderedRes:{}, lossRes:{}, enemyWeight:{}",
                getOwner(), canBePlundered, fixedRes, totalWeight, plunderedRes, lossRes, enemyWeight.values());

        AssetPackage costPackage = AssetPackage.builder().plusCurrencyLong(lossRes).build();
        getOwner().consume(costPackage, CommonEnum.Reason.ICR_PLUNDER);

        Map<Integer, Struct.Currency> bePlunderRes = plunderedRes.entrySet()
                .stream()
                .collect(Collectors.toMap(it -> it.getKey().getNumber(),
                        it -> Struct.Currency.newBuilder().setType(it.getKey().getNumber()).setCount(it.getValue()).build()));
        Map<Integer, Struct.Currency> realLossRes = lossRes.entrySet()
                .stream()
                .collect(Collectors.toMap(it -> it.getKey().getNumber(),
                        it -> Struct.Currency.newBuilder().setType(it.getKey().getNumber()).setCount(it.getValue()).build()));
        return Pair.of(bePlunderRes, realLossRes);
    }

    /**
     * 计算被掠夺的资源
     */
    public static void calcPlunderedRes(long remainWeight, Map<CommonEnum.CurrencyType, Long> remainRes, Map<CommonEnum.CurrencyType, Long> finalRes) {
        Map<CommonEnum.CurrencyType, Long> remainResWithoutZero = remainRes.entrySet()
                .stream()
                .filter(it -> it.getValue() > 0)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 可掠夺的各类资源中，最少的资源量
        long minAmount;
        Optional<Long> min = remainResWithoutZero.values().stream().min(Long::compareTo);
        if (min.isPresent()) {
            minAmount = min.get();
        } else {
            return;
        }

        if (minAmount <= 0) {
            return;
        }

        if (minAmount * remainResWithoutZero.size() >= remainWeight) {
            long totalWeight = remainWeight;
            long resSize = remainResWithoutZero.size();

            // 剩余的资源抢不完，遍历抢每种资源
            for (CommonEnum.CurrencyType currencyType : remainResWithoutZero.keySet()) {
                // 当前资源可抢的量 = 剩余负重 / 剩余可抢资源种类个数，结果向上取整
                long perAmount = MathUtils.ceilLong((float) totalWeight / resSize--);
                if (perAmount <= 0) {
                    continue;
                }
                totalWeight = Math.max(totalWeight - perAmount, 0);
                finalRes.put(currencyType, finalRes.getOrDefault(currencyType, 0L) + perAmount);
            }
        } else {
            // 剩余的资源不能平均抢，先把最少的抢完，然后递归
            for (Map.Entry<CommonEnum.CurrencyType, Long> entry : remainResWithoutZero.entrySet()) {
                finalRes.put(entry.getKey(), finalRes.getOrDefault(entry.getKey(), 0L) + minAmount);
                // 扣除剩余的资源
                remainResWithoutZero.put(entry.getKey(), Math.max(entry.getValue() - minAmount, 0));
            }

            calcPlunderedRes(remainWeight - minAmount * remainResWithoutZero.size(), remainResWithoutZero, finalRes);
        }
    }

    /**
     * 扣除损失的资源
     */
    private Map<CommonEnum.CurrencyType, Long> calcLossRes(Map<CommonEnum.CurrencyType, Long> plunderedRes, Map<CommonEnum.CurrencyType, Long> canBePlundered) {
        Map<CommonEnum.CurrencyType, Long> lossRes = new HashMap<>();
        float lossRatio = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getPlunderRatio();
        // 实际损失资源量 = min(实际可掠夺量， 被掠夺资源量 / （1 - 掠夺资源损失比例），结果向上取整）
        for (Map.Entry<CommonEnum.CurrencyType, Long> entry : plunderedRes.entrySet()) {
            long lossAmount = Math.min(MathUtils.ceilLong(entry.getValue() / ((1 - lossRatio))), canBePlundered.getOrDefault(entry.getKey(), 0L));
            lossRes.put(entry.getKey(), lossAmount);
        }
        return lossRes;
    }

    /**
     * 检查客户端上传的军队出兵量是否合法，出兵量最大值计算涉及英雄、机甲、玩家加成
     *
     * @param needCheckBuilder 需要检查的客户端参数
     * @param mainHeroId       主英雄id
     * @param deputyHeroId     副英雄id
     */
    public void checkTroopMaxNum(StructPlayer.CreateArmy_C2S_Param.Builder needCheckBuilder, int mainHeroId, int deputyHeroId) throws GeminiException {
        // 使用debug命令可以暂时取消行军士兵上限的限制
        if (DebugSwitchCenter.getInstance().isCreateArmyNoSoldierLimit()) {
            return;
        }
        // 获取英雄的最大出兵量
        Struct.Hero mainHero = getOwner().getHeroComponent().getHero(mainHeroId);
        Struct.Hero deputyHero = getOwner().getHeroComponent().getHero(deputyHeroId);
        Map<CommonEnum.BuffEffectType, Long> playerHeroAddition = PlayerAddCalc.getPlayerTroopCapAddition(getOwner());
        long soldierMaxNum = HeroHelper.getSoldierMaxNum(0, mainHero, deputyHero, playerHeroAddition);

        // 客户端请求的出兵量
        long soldierRealNum = 0;
        for (Struct.Soldier soldierPB : needCheckBuilder.getTroopInfo().getTroop().getDatasMap().values()) {
            soldierRealNum += soldierPB.getNum();
        }
        if (soldierRealNum <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (soldierRealNum > soldierMaxNum) {
            LOGGER.info("create army fail, reason: soldierMaxNum = {}; soldierRealNum = {}", soldierMaxNum, soldierRealNum);
            throw new GeminiException(ErrorCode.TROOP_INSUFFICIENT_TROOPS.getCodeId());
        }
    }

    public void handleOnAllBattleEndCmd(SsPlayerMisc.OnAllBattleEndCmd ask) {
        LOGGER.info("PlayerBattleComponent OnAllBattleEndCmd ask={}", ask);
        new PlayerAllBattleEndEvent(getOwner(), ask.getTotalLoss()).dispatch();
    }
}

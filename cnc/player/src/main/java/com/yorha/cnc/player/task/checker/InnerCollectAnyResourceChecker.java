package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerCollectResourceEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.INNER_COLLECT_ANY_RESOURCE_NUM;

/**
 * 城内收集任意资源达到多少
 * param1: 数量
 *
 * <AUTHOR>
 */
public class InnerCollectAnyResourceChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerInnerCollectResourceEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int countConfig = taskParams.getFirst();
        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int countTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(INNER_COLLECT_ANY_RESOURCE_NUM);
            prop.setProcess(Math.min(countConfig, countTotal));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof PlayerInnerCollectResourceEvent) {
                PlayerInnerCollectResourceEvent collectResourceEvent = (PlayerInnerCollectResourceEvent) event;
                Map<CommonEnum.CurrencyType, Long> resourceMap = collectResourceEvent.getResource();
                long countTotal = 0;
                if (resourceMap != null) {
                    for (Long count : resourceMap.values()) {
                        countTotal += count;
                    }
                }
                prop.setProcess((int) Math.min(countConfig, prop.getProcess() + countTotal));
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }

        return prop.getProcess() >= countConfig;
    }
}

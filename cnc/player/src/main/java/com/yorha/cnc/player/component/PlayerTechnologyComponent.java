package com.yorha.cnc.player.component;


import com.google.common.collect.Sets;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.component.PlayerQueueTaskComponent.QueueTaskInterface;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.cnc.player.event.task.TechFullOpenEvent;
import com.yorha.cnc.player.event.task.TechResearchEvent;
import com.yorha.cnc.player.event.task.TechStartResearchEvent;
import com.yorha.cnc.player.task.AllUnlockInterface;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.asset.AssetDesc;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.AssetType;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.power.PowerInterface;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.game.gen.prop.PlayerTechnologyModelProp;
import com.yorha.game.gen.prop.QueueTaskProp;
import com.yorha.game.gen.prop.TechInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.StructPlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncTech;
import res.template.TechIdTemplate;
import res.template.TechSubidTemplate;

import java.util.*;

import static com.yorha.common.enums.statistic.StatisticEnum.START_LEVEL_UP_TECH_NUM;


/**
 * 玩家科技组件
 *
 * <AUTHOR>
 */
public class PlayerTechnologyComponent extends PlayerComponent implements QueueTaskInterface, PowerInterface, AdditionProviderInterface, AllUnlockInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerTechnologyComponent.class);
    private static final QueueTaskType QUEUE_TYPE = QueueTaskType.RESEARCH;
    /**
     * 已解锁兵种
     */
    private final Set<Integer> unlockSoldierList = new HashSet<>();

    /**
     * 已解锁可采集资源
     */
    private final Set<Integer> unlockResource = new HashSet<>();

    /**
     * 侦查等级
     */
    private final Set<Integer> unlockSpyData = new HashSet<>();

    /**
     * 需要同步
     */
    boolean needSyncFlag = false;

    /**
     * 仅标识科技已满
     */
    boolean isFullTech = false;

    public PlayerTechnologyComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            getOwner().getPlayerQueueTaskComponent().unLockQueue(true, QUEUE_TYPE, 0);
        }
    }

    @Override
    public void onLoad(boolean isRegister) {
        checkFullTech();
    }

    @Override
    public void onLogin() {
        List<Integer> techInitUnlockResource = ResHolder.getResService(ConstKVResService.class).getTemplate().getTechUnlockRareEarthTiberiumDiamond();
        unlockResource.addAll(techInitUnlockResource);
        List<Integer> techInitUnlockSoldier = ResHolder.getResService(ConstKVResService.class).getTemplate().getInitiallyunlockclasses();
        unlockSoldierList.addAll(techInitUnlockSoldier);
        // 目前的科技这里不需要同步
        for (TechInfoProp techInfoProp : getProp().getAllFinTech().values()) {
            TechSubidTemplate template = ResHolder.getTemplate(TechSubidTemplate.class, techInfoProp.getTechSubId());
            for (IntPairType intPairType : template.getParamPairList()) {
                loadTechAttr(intPairType, false);
            }
        }
    }

    public PlayerTechnologyModelProp getProp() {
        return getOwner().getProp().getPlayerTechModel();
    }

    /**
     * 解锁条件检测
     */
    private void unLockConditionCheck(TechSubidTemplate techSubTemplate) {
        for (IntPairType intPairType : techSubTemplate.getFrontTechPairList()) {
            BuildConditionType conditionType = BuildConditionType.forNumber(intPairType.getKey());
            if (conditionType == null) {
                LOGGER.error("unKnow conditionType, param:{}", intPairType);
                continue;
            }
            switch (conditionType) {
                case PRE_TECH: {
                    TechSubidTemplate conditionTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, intPairType.getValue());
                    // 获取前置科技目前等级
                    TechInfoProp techInfoProp = getProp().getAllFinTech().get(conditionTemplate.getParentId());
                    if (techInfoProp == null) {
                        // 前置科技不满足
                        throw new GeminiException(ErrorCode.TECH_PREPOSITION_TECH_DISSATISFACTION);
                    }
                    int playerSubTechLevel = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techInfoProp.getTechSubId()).getTechLevel();

                    // 科技目前等级 小于 前置等级
                    if (playerSubTechLevel < conditionTemplate.getTechLevel()) {
                        // 前置科技不满足
                        throw new GeminiException(ErrorCode.TECH_PREPOSITION_TECH_DISSATISFACTION);
                    }
                    break;
                }
                case PRE_BUILD: {
                    break;
                }
                case PRE_TASK:
                default:
                    break;
            }
        }
    }

    /**
     * 是否已存在该科技了
     *
     * @param techSubId
     * @return
     */
    public boolean hasResearchedTech(int techSubId) {
        Map<Integer, TechSubidTemplate> techMap = ResHolder.getInstance().getMap(TechSubidTemplate.class);
        if (!techMap.containsKey(techSubId)) {
            return false;
        }
        TechSubidTemplate techSubidTemplate = techMap.get(techSubId);
        TechInfoProp techInfoProp = getProp().getAllFinTech().get(techSubidTemplate.getParentId());
        if (techInfoProp == null) {
            return false;
        }
        if (!techMap.containsKey(techInfoProp.getTechSubId())) {
            return true;
        }
        TechSubidTemplate hasSubIdTemplate = techMap.get(techInfoProp.getTechSubId());
        if (hasSubIdTemplate.getTechLevel() < techSubidTemplate.getTechLevel()) {
            // 不存在更高级科技
            return false;
        }
        return true;
    }

    /**
     * 对外接口，是否满足解锁条件
     *
     * @param techSubId
     */
    public boolean unLockConditionCheck(int techSubId) {
        Map<Integer, TechSubidTemplate> techMap = ResHolder.getInstance().getMap(TechSubidTemplate.class);
        if (!techMap.containsKey(techSubId)) {
            return false;
        }
        try {
            unLockConditionCheck(techMap.get(techSubId));
        } catch (GeminiException e) {
            if (e.isFailed()) {
                LOGGER.error("player:{} tech unLockConditionCheck failed", getOwner(), e);
            }
            return false;
        }
        return true;
    }

    /**
     * 研究科技
     */
    public void researchTech(int techSubId) {
        // 科技中心状态检验
        checkTechnologyCenterState();
        // 1、数据校验
        // 基础科技配置校验
        TechSubidTemplate techSubidTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techSubId);
        if (getProp().getCanReceiveTech() > 0) {
            // 有未领取科技
            throw new GeminiException(ErrorCode.TECH_TECH_NOT_OBTAINED);
        }
        // 是否已存在该科技了
        if (hasResearchedTech(techSubId)) {
            throw new GeminiException(ErrorCode.TECH_MORE_ADVANCED_TECH);
        }

        // 研究条件检测
        unLockConditionCheck(techSubidTemplate);

        final long costTimeMs = PlayerAddCalc.getTechCostTimeMs(getOwner(), techSubidTemplate);
        final long taskFinTsMs = costTimeMs + SystemClock.now();

        // 空闲队列检测
        getOwner().getPlayerQueueTaskComponent().queueFreeCheck(techSubidTemplate.getId(), QUEUE_TYPE, taskFinTsMs);

        // 道具检测（解锁所需消耗）
        AssetPackage cost = AssetPackage.builder()
                .plusItems(techSubidTemplate.getCostItemPairList())
                .plusCurrency(PlayerAddCalc.getTechCostResourcesPairList(getOwner(), techSubidTemplate))
                .build();
        getOwner().verifyThrow(cost);

        // 2、资源消耗
        getOwner().consume(cost, Reason.ICR_TECH);

        // 3、科研
        buildLevelUpTask(techSubidTemplate, cost, costTimeMs);
    }

    public boolean isCanReceieve(int techSubId) {
        if (getProp().getCanReceiveTech() == techSubId) {
            return true;
        }
        return false;
    }

    /**
     * 立即完成科技
     */
    public boolean immediatelyResearchTech(int techSubId, String sPassWord, boolean isSystemOpera) {
        if (techSubId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
        }
        // 1、检验
        TechSubidTemplate techSubidTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techSubId);

        try {
            if (isSystemOpera && getProp().getCanReceiveTech() == techSubId) {
                return false;
            }
            if (!isSystemOpera && getProp().getCanReceiveTech() > 0) {
                // 有未领取科技
                throw new GeminiException(ErrorCode.TECH_TECH_NOT_OBTAINED);
            }

            // 是否已存在该科技了
            if (hasResearchedTech(techSubId)) {
                throw new GeminiException(ErrorCode.TECH_MORE_ADVANCED_TECH);
            }

            if (getOwner().getPlayerQueueTaskComponent().getQueueTaskProp(QueueTaskType.RESEARCH, techSubidTemplate.getId()) != null) {
                // 不允许立即完成队列中任务
                throw new GeminiException(ErrorCode.TECH_CAN_NOT_COMPLETED_IMMEDIATELY);
            }

            // 研究条件检测
            unLockConditionCheck(techSubidTemplate);
        } catch (GeminiException e) {
            // 系统升级时屏蔽异常，返回false
            if (isSystemOpera) {
                return false;
            }
            throw e;
        }


        if (!isSystemOpera) {
            // 计算资源消耗
            AssetPackage.Builder currCostAssetBuilder = AssetPackage.builder();
            List<IntPairType> techCostResourcesPairList = PlayerAddCalc.getTechCostResourcesPairList(getOwner(), techSubidTemplate);
            for (IntPairType intPairType : techCostResourcesPairList) {
                currCostAssetBuilder.plusCurrency(intPairType);
            }
            Pair<AssetPackage, AssetPackage> cost2RemainAssetPack = getOwner().getAssetComponent().calcAssetToGold(currCostAssetBuilder.build());

            // 计算时间消耗
            long totalCostMs = PlayerAddCalc.getTechCostTimeMs(getOwner(), techSubidTemplate);
            AssetPackage costTime = getOwner().getAssetComponent().calcTimeMsToGold(totalCostMs);

            AssetPackage cost = AssetPackage.builder()
                    .plus(costTime)
                    .plus(cost2RemainAssetPack.getFirst())
                    .build();

            getOwner().verifyThrow(cost);

            // 二级密码校验
            for (AssetDesc desc : cost.getImmutableAssets()) {
                if ((desc.getType() == AssetType.CURRENCY) && (desc.getId() == CommonEnum.CurrencyType.DIAMOND.getNumber())) {
                    getOwner().getSettingComponent().checkSpassword(CommonEnum.SPassWordCheckType.SPWC_MANY_MONEY, desc.getAmount(), sPassWord);
                }
            }
            // 资源消耗
            getOwner().consume(cost, Reason.ICR_TECH);
            // 归还多扣的
            getOwner().getAssetComponent().give(cost2RemainAssetPack.getSecond(), Reason.ICR_TECH_G2R_RETURN, "immediatelyResearchTech");
            // 执行研究操作
            buildLevelUpTask(techSubidTemplate, null, 0);
            new PlayerSpeedUpEvent(getOwner(), TimeUtils.ms2Second(totalCostMs), CommonEnum.QueueTaskType.RESEARCH, QueueSpeedReason.STRAIGHTLY_ACCELERATE).dispatch();
            // 直接领取
            receiveTech(techSubId);
            return true;
        }
        execTechResearch(techSubId);
        return true;
    }

    /**
     * 领取科技
     */
    public void receiveTech(int techSubId) {
        // 数据校验
        if (techSubId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (getProp().getCanReceiveTech() != techSubId) {
            // 无可领取科技
            throw new GeminiException(ErrorCode.TECH_NO_TECHNOLOGY_AVAILABLE);
        }
        getProp().setCanReceiveTech(0);
        execTechResearch(techSubId);
    }

    private void execTechResearch(int techSubId) {
        TechSubidTemplate subIdTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techSubId);

        // 初始化
        int techId = subIdTemplate.getParentId();
        TechInfoProp techInfoProp = getOrInitTechInfo(techId);
        techLevelUp(techInfoProp, subIdTemplate);

        // 无后置任务时检测一次
        if (subIdTemplate.getNextLevel() <= 0) {
            checkFullTech();
        }

        // 领取科技后置逻辑
        postReceiveTech(subIdTemplate.getParamPairList(), subIdTemplate.getBuffPairList());

        buildAndSendQlog(techSubId, 0, "tech_collect");
    }

    private void techLevelUp(TechInfoProp techInfoProp, TechSubidTemplate subIdTemplate) {
        LOGGER.debug("tech level up, tech:{}, oldSubId:{}, newSubId:{}", techInfoProp.getTechId(), techInfoProp.getTechSubId(), subIdTemplate.getId());
        techInfoProp.setTechSubId(subIdTemplate.getId());
    }


    /**
     * 领取科技后置逻辑
     */
    private void postReceiveTech(List<IntPairType> paramPairList, List<IntPairType> buffPairList) {
        // 更新战力
        getOwner().getPowerComponent().updatePower(PowerType.PT_TECH);
        // 更新加成
        getOwner().getAddComponent().updateAddition(AdditionProviderType.TECH, PlayerAdditionComponent.getAdditionIds(buffPairList));
        // 更新任务进展
        new TechResearchEvent(getOwner(), 1).dispatch();
        // 科技属性功能
        for (IntPairType intPairType : paramPairList) {
            loadTechAttr(intPairType, true);
        }
        syncTech2ScenePlayer();
    }

    private TechInfoProp getOrInitTechInfo(int techId) {
        TechInfoProp techInfoProp = getProp().getAllFinTech().get(techId);
        if (techInfoProp == null) {
            techInfoProp = getProp().addEmptyAllFinTech(techId);
        }
        return techInfoProp;
    }

    /**
     * 科技研究
     *
     * @param techSubIdTemplate 目标等级配置
     * @param costTimeMs        升级所消耗时间
     * @param cost              升级所实际消耗的资源
     */
    private void buildLevelUpTask(TechSubidTemplate techSubIdTemplate, AssetPackage cost, long costTimeMs) {
        buildAndSendQlog(techSubIdTemplate.getId(), SystemClock.now(), "tech_start");
        getOwner().getStatisticComponent().recordSecondStatistic(START_LEVEL_UP_TECH_NUM, techSubIdTemplate.getParentId(), 1);
        new TechStartResearchEvent(getOwner(), techSubIdTemplate.getParentId(), 1).dispatch();
        if (costTimeMs > 0) {
            // 添加任务队列
            getOwner().getPlayerQueueTaskComponent().addQueueTask(QUEUE_TYPE, techSubIdTemplate.getId(), costTimeMs, cost, StructPlayerPB.QueueExtraParamPB.getDefaultInstance());
            return;
        }

        // 立即完成
        QueueTaskProp queueTaskProp = getOwner().getPlayerQueueTaskComponent().getQueueTaskProp(QUEUE_TYPE, techSubIdTemplate.getId());
        if (queueTaskProp != null) {
            getOwner().getPlayerQueueTaskComponent().finishQueueTask(queueTaskProp);
            return;
        }
        onFinish(techSubIdTemplate.getId(), null);
    }

    /**
     * 载入科技解锁
     */
    private void loadTechAttr(IntPairType intPairType, boolean isNewTech) {
        LOGGER.debug("update tech param, param:{}", intPairType);
        TechAttrType techAttrType = TechAttrType.forNumber(intPairType.getKey());
        if (techAttrType == null) {
            LOGGER.error("techAttrType no config, param:{}", intPairType);
            return;
        }
        switch (techAttrType) {
            case UNLOCK_SOLDIER: {
                unlockSoldierList.add(intPairType.getValue());
                // 初始解锁兵种不需要配置成就，只在新增解锁里触发就行
                getOwner().getAchievementComponent().onTrigger(AchievementStatisticCheckerType.ASCT_UNLOCK_SOLDIER, intPairType.getValue(), 0, 1);
                if (isNewTech) {
                    needSyncFlag = true;
                }
                break;
            }
            case UNLOCK_RESOURCE_TYPE: {
                unlockResource.add(intPairType.getValue());
                if (isNewTech) {
                    needSyncFlag = true;
                }
                break;
            }
            case SPY_DATA_LEVEL_UP: {
                unlockSpyData.add(intPairType.getValue());
                if (isNewTech) {
                    needSyncFlag = true;
                }
                break;
            }
            default:
                break;
        }


    }

    /**
     * 更新科技解锁功能属性缓存
     */
    private void syncTech2ScenePlayer() {
        if (!needSyncFlag) {
            return;
        }
        needSyncFlag = false;
        SsScenePlayer.SyncTechDataCmd.Builder cmd = SsScenePlayer.SyncTechDataCmd.newBuilder().setPlayerId(getPlayerId());
        cmd.addAllUnlockSpyData(getSpyLevelSet());
        cmd.addAllUnlockResource(getResourceSet());
        ownerActor().tellBigScene(cmd.build());
    }

    /**
     * 完成事件
     *
     * @param techSubId 科技子id
     */
    @Override
    public void onFinish(long techSubId, QueueTaskProp taskProp) {
        int canReceiveTech = getProp().getCanReceiveTech();
        if (canReceiveTech > 0) {
            WechatLog.error("has noReceiveTech, queueTaskProp:{} finTechSubId:{} playerTechModel:{}", taskProp, techSubId, getProp());
            return;
        }

        int techId = (int) techSubId;
        getProp().setCanReceiveTech(techId);
        long startTime = taskProp == null ? SystemClock.now() : taskProp.getStarTime();

        buildAndSendQlog(techId, startTime, "tech_update");
        getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.TECH_RESEARCH_TOTAL, 1);
    }

    /**
     * 取消科研
     *
     * @param techSubId 科研子id
     */
    @Override
    public void onCancel(long techSubId, QueueTaskProp taskProp) {
        // 归还道具
        TechSubidTemplate techSubidTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, (int) techSubId);
        // 科技取消返还比率
        int returnRadio = ResHolder.getResService(ConstKVResService.class).getTemplate().getCancelresearch();
        AssetPackage.Builder assetPackageBuilder = AssetPackage.builder();

        // 归还资源
        for (CurrencyProp currencyProp : taskProp.getCostResource()) {
            int realNum = (int) (currencyProp.getCount() * returnRadio / Constants.CONVERT_TEN_THOUSAND_POINTS);
            if (realNum > 0) {
                assetPackageBuilder.plusCurrency(currencyProp.getType(), realNum);
            }
        }

        // 归还道具
        for (IntPairType itemPair : techSubidTemplate.getCostItemPairList()) {
            int realNum = (int) (itemPair.getValue() * returnRadio / Constants.CONVERT_TEN_THOUSAND_POINTS);
            assetPackageBuilder.plusItem(itemPair.getKey(), realNum);
        }

        getOwner().getAssetComponent().give(assetPackageBuilder.build(), Reason.ICR_TECH);
        String reasonAction = "tech_cancel";

        buildAndSendQlog((int) techSubId, taskProp.getStarTime(), reasonAction);
    }

    private void buildAndSendQlog(int techSubId, long startTsMs, String reasonAction) {
        TechSubidTemplate techSubTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techSubId);
        TechIdTemplate techTemplate = ResHolder.getInstance().getValueFromMap(TechIdTemplate.class, techSubTemplate.getParentId());
        QlogCncTech.init(getOwner().getQlogComponent())
                .setTechID(techTemplate.getId())
                .setTechType(String.valueOf(techTemplate.getTechType()))
                .setSubTechID(techSubTemplate.getId())
                .setTechLevel(techSubTemplate.getTechLevel())
                .setDtEventTime(TimeUtils.timeStampMs2String(SystemClock.now()))
                .setStartTime(TimeUtils.timeStampMs2String(startTsMs))
                .setAction(reasonAction)
                .sendToQlog();
    }


    @Override
    public boolean isSpeedUpItemAvailable(ItemUseType itemUseType) {
        return itemUseType == ItemUseType.COMMON_SPEED || itemUseType == ItemUseType.TECH_SPEED;
    }

    @Override
    public Reason speedUpUsingReason() {
        return Reason.ICR_TECH;
    }

    @Override
    public PowerType getPowerType() {
        return PowerType.PT_TECH;
    }

    @Override
    public long calcPower() {
        long sum = 0;
        for (TechInfoProp techInfoProp : getProp().getAllFinTech().values()) {
            TechSubidTemplate template = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techInfoProp.getTechSubId());
            sum += template.getPower();
        }
        return sum;
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.TECH;
    }

    @Override
    public Map<AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        long sum = 0;
        for (TechInfoProp techProp : getProp().getAllFinTech().values()) {
            TechSubidTemplate techSubIdTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techProp.getTechSubId());
            for (IntPairType intPairType : techSubIdTemplate.getBuffPairList()) {
                if (additionId == intPairType.getKey()) {
                    sum += intPairType.getValue();
                }
            }
        }
        Map<AdditionSourceType, Long> res = new HashMap<>();
        res.put(AdditionSourceType.AST_ACADEMY, sum);
        return res;
    }

    /**
     * 获取已解锁的士兵类型
     */
    public Set<Integer> getUnlockSoldierList() {
        return Sets.newHashSet(unlockSoldierList);
    }

    /**
     * 获取已解锁的侦察数据
     */
    public Set<Integer> getSpyLevelSet() {
        return Sets.newHashSet(unlockSpyData);
    }

    /**
     * 获取已解锁的资源数据
     */
    public Set<Integer> getResourceSet() {
        return Sets.newHashSet(unlockResource);
    }

    /**
     * 判断资源采集类型是否解锁
     */
    public boolean checkResourceUnlock(int resource) {
        return unlockResource.contains(resource);
    }

    public void clearAllTechByGm() {
        getProp().setCanReceiveTech(0);

        List<Integer> additionIds = new ArrayList<>();
        for (TechInfoProp value : getProp().getAllFinTech().values()) {
            List<IntPairType> buffPairList = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, value.getTechSubId()).getBuffPairList();
            additionIds.addAll(PlayerAdditionComponent.getAdditionIds(buffPairList));
        }
        getProp().getAllFinTech().clear();
        // 更新加成
        getOwner().getAddComponent().updateAddition(AdditionProviderType.TECH, additionIds);
        // 更新战力
        getOwner().getPowerComponent().updatePower(PowerType.PT_TECH);
    }

    public void unLockAllTechByGm() {
        List<IntPairType> paramList = new ArrayList<>();
        List<IntPairType> buffList = new ArrayList<>();

        for (TechSubidTemplate subIdTemplate : ResHolder.getInstance().getListFromMap(TechSubidTemplate.class)) {
            // 初始化
            int techId = subIdTemplate.getParentId();
            TechInfoProp techInfoProp = getOrInitTechInfo(techId);
            techLevelUp(techInfoProp, subIdTemplate);
            paramList.addAll(subIdTemplate.getParamPairList());
            buffList.addAll(subIdTemplate.getBuffPairList());
            buildAndSendQlog(subIdTemplate.getId(), SystemClock.now(), "gm_unlock_tech");
        }

        // 需要在战力更新前,战力更新触发活动，活动检测是否满
        checkFullTech();

        // 领取科技后置逻辑
        postReceiveTech(paramList, buffList);

        List<Integer> additionIds = PlayerAdditionComponent.getAdditionIds(buffList);
        // 更新加成
        getOwner().getAddComponent().updateAddition(AdditionProviderType.TECH, additionIds);
        // 更新战力
        getOwner().getPowerComponent().updatePower(PowerType.PT_TECH);
    }

    /**
     * 传入子科技id，返回对应的科技大类id，当子科技Id对应配置不存在时，返回0
     *
     * @param techSubId 子科技Id
     * @return 对应的科技大类id
     */
    public int getParentIdByTechSubId(int techSubId) {
        TechSubidTemplate techSubTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techSubId);
        if (techSubTemplate == null) {
            return 0;
        }
        return techSubTemplate.getParentId();
    }

    /**
     * 传入子科技id，返回对应的科技等级，当子科技Id对应配置不存在时，返回0
     *
     * @param techSubId 子科技Id
     * @return 科技等级
     */
    public int getTechLevelByTechSubId(int techSubId) {
        TechSubidTemplate techSubTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techSubId);
        if (techSubTemplate == null) {
            return 0;
        }
        return techSubTemplate.getTechLevel();
    }

    public int getTechLevelByTechId(int techId) {
        // 还未研发过
        if (!getProp().getAllFinTech().containsKey(techId)) {
            return 0;
        }
        int techSubId = getProp().getAllFinTech().get(techId).getTechSubId();
        return getTechLevelByTechSubId(techSubId);
    }

    /**
     * 判断科技中心是否因为正在升级而不能研究科技
     */
    private void checkTechnologyCenterState() {

    }

    public String getQLogTechInfo() {
        List<String> infos = new ArrayList<>();
        for (TechInfoProp prop : getProp().getAllFinTech().values()) {
            infos.add(String.valueOf(prop.getTechSubId()));
        }
        return QlogUtils.transCollection2ArrayString(infos);
    }

    private void checkFullTech() {
        for (TechIdTemplate techIdTemplate : ResHolder.getInstance().getListFromMap(TechIdTemplate.class)) {
            TechInfoProp techInfoProp = getProp().getAllFinTech().get(techIdTemplate.getId());
            if (techInfoProp == null) {
                return;
            }
            TechSubidTemplate techSubidTemplate = ResHolder.getInstance().getValueFromMap(TechSubidTemplate.class, techInfoProp.getTechSubId());
            if (techSubidTemplate.getNextLevel() > 0) {
                return;
            }
        }
        isFullTech = true;
        new TechFullOpenEvent(getOwner()).dispatch();
    }

    @Override
    public boolean isAllUnlock() {
        return isFullTech;
    }

    public void copyTech2DungeonPlayer(Player.PlayerTechnologyModel.Builder techBuilder) {
        getProp().copyToDb(techBuilder);
    }
}

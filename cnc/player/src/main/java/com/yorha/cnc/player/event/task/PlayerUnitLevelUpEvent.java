package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/*指定兵种达到X级*/
public class PlayerUnitLevelUpEvent extends PlayerTaskEvent {
    private final int unitId;
    private final int curLevel;
    private final int upLevelNum;

    public int getUpLevelNum() {
        return upLevelNum;
    }

    public PlayerUnitLevelUpEvent(PlayerEntity entity, int unitId, int curLevel, int upLevelNum) {
        super(entity);
        this.unitId = unitId;
        this.curLevel = curLevel;
        this.upLevelNum = upLevelNum;
    }

    public int getUnitId() {
        return unitId;
    }

    public int getCurLevel() {
        return curLevel;
    }
}

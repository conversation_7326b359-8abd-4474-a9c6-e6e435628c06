package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.utils.MathUtils;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

import static com.yorha.common.enums.reason.HeroUnlockReason.USE_HERO_ITEM;
import static com.yorha.proto.CommonEnum.Reason.*;

/**
 * 英雄道具，可招募英雄，如果英雄已存在则分解为碎片
 * <AUTHOR>
 */
public class RecruitHeroItem extends AbstractUsableItem {

    public RecruitHeroItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
    }

    @Override
    public CommonEnum.Reason getItemReason(PlayerEntity playerEntity) {
        int heroId = getTemplate().getEffectValue2();
        if (!playerEntity.getHeroComponent().hasHeroProp(heroId)) {
            if (num > 1) {
                return ICR_HERO_CARD_GET_HERO_AND_FRAG;
            }
            return ICR_HERO_CARD_GET_HERO;
        }
        return ICR_HERO_CARD_EXCHANGE_FRAG;
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        int heroId = template.getEffectValue2();
        int left = num;
        // 未拥有该英雄，招募英雄，已有则分解为碎片
        if (!playerEntity.getHeroComponent().hasHeroProp(heroId)) {
            playerEntity.getHeroComponent().initHero(heroId, USE_HERO_ITEM);
            left = num - 1;
            if (left <= 0) {
                return true;
            }
        }
        int heroTatterId = template.getEffectId();
        playerEntity.getItemComponent().addItem(heroTatterId, MathUtils.multiplyExact(left, template.getEffectValue()), getItemReason(playerEntity), "");
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

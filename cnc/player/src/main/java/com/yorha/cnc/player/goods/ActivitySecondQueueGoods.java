package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.Struct;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

public class ActivitySecondQueueGoods extends ActivityNormalGoods {

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        // 这个地方不能使用父类的逻辑，因为这个时候活动unit已经自己触发expire了，父类中的fineUnit会报错的（而且此时unit不需要处理什么逻辑了）
        return Collections.emptyList();
    }

}

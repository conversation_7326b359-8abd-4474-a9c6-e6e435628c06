package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.points.PointsByIncPowerConf;
import com.yorha.common.resource.resservice.points.PointsByKillMonsterConf;
import com.yorha.common.resource.resservice.points.PointsByUseSpeedUpConf;
import com.yorha.common.resource.resservice.points.PointsResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityPointsTemplate;
import res.template.MonsterTemplate;
import res.template.SoldierTypeTemplate;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 积分模块，玩家通过游戏中的行为获得对应的积分（每击杀一个叛军获得10点积分，每使用1个钻石获得100积分这种，不太适合用task那一套来实现）
 * <p>
 * 虽然大多用的都是活动内，但设计上并不局限于活动内
 * <p>
 * 获得的积分数据并不存储在一个集中的model中
 */
public class PlayerPointsComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPointsComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerKillBigSceneMonsterEvent.class, PlayerPointsComponent::onKillBigSceneMonster);
        EntityEventHandlerHolder.register(PlayerFinishTrainingSoldierEvent.class, PlayerPointsComponent::onFinishTrainingSoldier);
        EntityEventHandlerHolder.register(ResBuildingCollectResourceEvent.class, PlayerPointsComponent::onResBuildingCollectResource);
        EntityEventHandlerHolder.register(PlayerSpeedUpEvent.class, PlayerPointsComponent::onSpeedUp);
        EntityEventHandlerHolder.register(PlayerPowerIncreaseEvent.class, PlayerPointsComponent::onPowerIncrease);
        EntityEventHandlerHolder.register(PlayerKillSoldierEvent.class, PlayerPointsComponent::onKillSoldier);
        EntityEventHandlerHolder.register(PlayerSpeedUpEvent.class, PlayerPointsComponent::onUseSpeedUpItem);
    }

    private final List<Listener> listenerList = Lists.newLinkedList();

    public PlayerPointsComponent(PlayerEntity owner) {
        super(owner);
    }

    public void register(String module, Listener listener) {
        LOGGER.info("{} {} listen on points ", getOwner(), module);
        listenerList.add(listener);
    }

    public void remove(Listener listener) {
        listenerList.remove(listener);
    }

    public void gmGivePoints(int templateId, int count) {
        givePoints(templateId, count);
    }

    /**
     * PointsComponent 负责监听玩家各种行为，按配置换算为要增加的积分，分发给需要加积分的模块Listener
     */
    public interface Listener {

        // 关注的activity_points配置id
        Set<Integer> focusPointsIds();

        void addPoints(int pointsTemplateId, long addPoints);
    }

    private void givePoints(int pointsTemplateId, int addPoints) {
        for (Listener listener : listenerList) {
            if (listener.focusPointsIds().contains(pointsTemplateId)) {
                listener.addPoints(pointsTemplateId, addPoints);
            }
        }
    }

    private static void onKillBigSceneMonster(PlayerKillBigSceneMonsterEvent event) {
        PlayerPointsComponent component = event.getPlayer().getPointsComponent();
        PointsResService resService = ResHolder.getResService(PointsResService.class);
        MonsterTemplate monsterTemplate = ResHolder.getTemplate(MonsterTemplate.class, event.getMonsterId());
        for (PointsByKillMonsterConf conf : resService.getKillMonsterConfs(monsterTemplate)) {
            // 每杀一个野怪加配置的积分
            if (conf.matched(monsterTemplate)) {
                component.givePoints(conf.getTemplateId(), conf.getPoints());
            }
        }
    }

    private static void onFinishTrainingSoldier(PlayerFinishTrainingSoldierEvent event) {
        PlayerPointsComponent component = event.getPlayer().getPointsComponent();
        final int soldierId = event.getSoldierId();
        SoldierResService soldierResService = ResHolder.getResService(SoldierResService.class);
        final int soldierLevel = soldierResService.findSoldierTemplate(soldierId).getSoldierLevel();
        PointsResService pointsResService = ResHolder.getResService(PointsResService.class);

        int fromSoldierLv = 0;
        if (event.getPreSoldierId() > 0) {
            fromSoldierLv = soldierResService.findSoldierTemplate(event.getPreSoldierId()).getSoldierLevel();
        }
        List<ActivityPointsTemplate> templates = pointsResService.getTrainSoldierLvConfs(soldierLevel, fromSoldierLv);

        for (ActivityPointsTemplate template : templates) {
            component.givePoints(template.getId(), template.getPoints() * event.getCount());
        }
    }

    private static void onResBuildingCollectResource(ResBuildingCollectResourceEvent event) {
        PlayerPointsComponent component = event.getPlayer().getPointsComponent();
        Map<Integer, Long> collectResMap = event.getCollectResMap();
        PointsResService pointsResService = ResHolder.getResService(PointsResService.class);
        for (Map.Entry<Integer, Long> entry : collectResMap.entrySet()) {
            Integer currencyType = entry.getKey();
            Long count = entry.getValue();
            List<ActivityPointsTemplate> templates = pointsResService.getResCollectConfs(currencyType);
            for (ActivityPointsTemplate template : templates) {
                int addPoints = (int) Math.ceil((double) (count * template.getPoints()) / Integer.parseInt(template.getParam2()));
                component.givePoints(template.getId(), addPoints);
            }
        }
    }

    private static void onSpeedUp(PlayerSpeedUpEvent event) {
        if (event.getReason() == QueueSpeedReason.STRAIGHTLY_ACCELERATE || event.getReason() == QueueSpeedReason.ACCELERATE_CARD) {
            PlayerPointsComponent component = event.getPlayer().getPointsComponent();
            final int speedUpMinutes = (int) Math.ceil((double) event.getTotalCostSec() / 60);
            PointsResService pointsResService = ResHolder.getResService(PointsResService.class);
            List<ActivityPointsTemplate> templates = pointsResService.getPointsTemplates(CommonEnum.ActivityPointsWay.APW_SPEED_UP_ANY);
            for (ActivityPointsTemplate template : templates) {
                component.givePoints(template.getId(), template.getPoints() * speedUpMinutes);
            }
        }
    }

    /**
     * 使用加速道具
     */
    private static void onUseSpeedUpItem(PlayerSpeedUpEvent event) {
        if (event.getType() == null) {
            LOGGER.info("PlayerPointsComponent, onUseSpeedUpItem: PlayerSpeedUpEvent type is null, event:{}", event);
            return;
        }
        if (event.getReason() != QueueSpeedReason.STRAIGHTLY_ACCELERATE && event.getReason() != QueueSpeedReason.ACCELERATE_CARD) {
            // 不是使用加速道具
            return;
        }
        PlayerPointsComponent component = event.getPlayer().getPointsComponent();
        PointsResService pointsResService = ResHolder.getResService(PointsResService.class);
        List<PointsByUseSpeedUpConf> confs = pointsResService.getUseSpeedUpItemConfs(event.getType());
        for (PointsByUseSpeedUpConf conf : confs) {
            final int speedUpMinutes = (int) Math.ceil((double) event.getTotalCostSec() / (60 * conf.getMinutesToPoint()));
            component.givePoints(conf.getTemplateId(), conf.getPoints() * speedUpMinutes);
        }
    }

    private static void onPowerIncrease(PlayerPowerIncreaseEvent event) {
        if (event.getPowerReason() == CommonEnum.PowerType.PT_SOLDIER) {
            // 医院治疗引起的战力增加不计数，算是小小的潜规则吧
            if (TroopHelper.isUpdatePowerByRecover(event.getSoldierNumChangeReason())) {
                return;
            }
        }
        PlayerPointsComponent component = event.getPlayer().getPointsComponent();
        CommonEnum.PowerType powerType = event.getPowerReason();
        PointsResService pointsResService = ResHolder.getResService(PointsResService.class);
        List<PointsByIncPowerConf> confs = pointsResService.getPowerIncConfs(powerType);
        for (PointsByIncPowerConf conf : confs) {
            if (!conf.hasChangeReasonConfig()) {
                // 无修改原因的配置，配置无限制，直接加
                component.givePoints(conf.getTemplateId(), (int) (conf.getPoints() * event.getPower()));
                continue;
            }
            // 有修改原因的配置，看是否匹配
            if (conf.isChangeReasonMatched(event.getSoldierNumChangeReason())) {
                component.givePoints(conf.getTemplateId(), (int) (conf.getPoints() * event.getPower()));
            }
        }
    }

    private static void onKillSoldier(PlayerKillSoldierEvent event) {
        PlayerPointsComponent component = event.getPlayer().getPointsComponent();
        if (event.isNpcEnemy()) {
            // 击杀NPC不计入
            return;
        }
        PointsResService pointsResService = ResHolder.getResService(PointsResService.class);
        for (Map.Entry<Integer, Long> soldierId2count : event.getKillSoldierMap().entrySet()) {
            Integer soldierId = soldierId2count.getKey();
            Long count = soldierId2count.getValue();
            int soldierLevel = ResHolder.getTemplate(SoldierTypeTemplate.class, soldierId).getSoldierLevel();
            List<ActivityPointsTemplate> pointsTemplates = pointsResService.getKillPvpSoldierConfs(soldierLevel);
            for (ActivityPointsTemplate template : pointsTemplates) {
                int denominator = Integer.parseInt(template.getParam1());
                if (denominator <= 0) {
                    LOGGER.error("PlayerPointsComponent onKillSoldier param1<=0 templateId={} param1={}", template.getId(), template.getParam1());
                    continue;
                }
                final int countRatio = (int) Math.ceil((double) count / denominator);
                component.givePoints(template.getId(), countRatio * template.getPoints());
            }
        }
    }
}

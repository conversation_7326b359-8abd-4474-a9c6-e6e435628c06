package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ActivityTimerRewardUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ActivityTimerRewardTemplate;

import static com.yorha.proto.CommonEnum.ActivityUnitType;
import static com.yorha.proto.CommonEnum.Reason;

/**
 * 一段时间后可以领奖，再过一段时间后过期，并补发奖励邮件
 */
public class PlayerTimerRewardUnit extends BasePlayerActivityUnit {

    static {
        ActivityUnitFactory.register(ActivityUnitType.AUT_TIMER_REWARD, (owner, prop, template) ->
                new PlayerTimerRewardUnit(owner, prop.getTimerRewardUnit())
        );
    }

    public PlayerTimerRewardUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {

    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        ActivityTimerRewardTemplate template = ResHolder.getTemplate(ActivityTimerRewardTemplate.class, ownerActivity.getActivityId());
        if (template.getExpireMailId() > 0 && !unitProp.getTimerRewardUnit().getRewardTaken()) {
            StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
            params.setMailTemplateId(template.getExpireMailId());
            params.getSenderBuilder().setSenderId(0);

            final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                    .setPlayerId(player().getPlayerId())
                    .setZoneId(player().getZoneId())
                    .build();
            player().getMailComponent().sendPersonalMail(receiver, params.build());
        }
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        ActivityTimerRewardUnitProp prop = unitProp.getTimerRewardUnit();
        if (prop.getRewardTaken()) {
            throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
        }
        ActivityTimerRewardTemplate template = ResHolder.getTemplate(ActivityTimerRewardTemplate.class, ownerActivity.getActivityId());
        int rewardArriveSec = template.getRewardArriveSec();
        if (SystemClock.nowOfSeconds() < ownerActivity.getProp().getStartTsSec() + rewardArriveSec) {
            throw new GeminiException(ErrorCode.REWARD_NOT_EXIST);
        }
        prop.setRewardTaken(true);
        AssetPackage reward = AssetPackage.builder().plusItems(template.getRewardPairList()).build();
        player().getAssetComponent().give(reward, Reason.ICR_TIMER_REWARD, String.valueOf(ownerActivity.getActivityId()));
    }
}

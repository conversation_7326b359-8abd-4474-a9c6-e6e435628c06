package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.Map;

public class AddIdIpMail implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());


        final int itemId;
        if (args.get("itemId") != null) {
            itemId = Integer.parseInt(args.get("itemId"));
        } else {
            itemId = 10410500;
        }

        final int itemNum = 1;
        Struct.ItemPairList.Builder itemBuilder = Struct.ItemPairList.newBuilder();
        itemBuilder.addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(itemId).setCount(itemNum).build());
        builder.setItemReward(itemBuilder);

        actor.getOrLoadEntity().getMailComponent().sendPersonalMail(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(actor.getPlayerId())
                        .setZoneId(actor.getZoneId())
                        .build(),
                builder
                        .build()
        );
    }

    @Override
    public String showHelp() {
        return "AddIdIpMail itemId={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAIL;
    }
}


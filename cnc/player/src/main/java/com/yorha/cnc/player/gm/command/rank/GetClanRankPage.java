package com.yorha.cnc.player.gm.command.rank;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanRank.GetClanRankPageInfoAns;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 分页获取联盟排行
 *
 * <AUTHOR> Jiang
 */
public class GetClanRankPage implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GetClanRankPage.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int rankId = Integer.parseInt(args.get("rankId"));
        int page = Integer.parseInt(args.get("page"));
        GetClanRankPageInfoAns answer = actor.getEntity().getPlayerClanComponent().getClanRankInfoList(rankId, page);
        LOGGER.debug("{}", answer);
    }

    @Override
    public String showHelp() {
        return "GetClanRankPage rankId={value} page={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_RANK;
    }
}
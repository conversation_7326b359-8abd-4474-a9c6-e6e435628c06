package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * oom测试
 *
 * <AUTHOR>
 */
public class GmOomDump implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GmOomDump.class);

    private static final List<Byte[]> BYTES_LIST = new LinkedList<>();

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        LOGGER.info("try oom dump size:{}", args.get("size"));
        LOGGER.info("dump before: {}", getBytesListSize());
        while (true) {
            // 1MB = 1024KB = 1024*1024Byte
            Byte[] i = new Byte[1024 * 1024];
            BYTES_LIST.add(i);
            LOGGER.info("dump after: {}", getBytesListSize());
        }
    }

    private long getBytesListSize() {
        return BYTES_LIST.size();
    }

    @Override
    public String showHelp() {
        return "GmOomDump size={value MB}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}
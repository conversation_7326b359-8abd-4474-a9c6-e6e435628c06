package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.OpenTreasureChestsEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.OPEN_TREASURE_CHEST_TIME;

/**
 * 打开xx类型宝箱xx次(0代表任意宝箱)
 * param1：宝箱类型枚举->RecruitPoolType
 * param2: 次数
 *
 * <AUTHOR>
 */
public class OpenTreasureChestsTimes<PERSON>he<PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            OpenTreasureChestsEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer param1 = taskParams.get(0);
        Integer param2 = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int openTimesTotal;
            if (param1 == 0) {
                openTimesTotal = (int) event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(OPEN_TREASURE_CHEST_TIME);
            } else {
                openTimesTotal = (int) event.getPlayer().getStatisticComponent().getSecondStatistic(OPEN_TREASURE_CHEST_TIME, param1);
            }
            prop.setProcess(Math.min(openTimesTotal, param2));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof OpenTreasureChestsEvent) {
                if (param1 == 0 || param1 == ((OpenTreasureChestsEvent) event).getType()) {
                    int openTimes = ((OpenTreasureChestsEvent) event).getNum();
                    prop.setProcess(Math.min(prop.getProcess() + openTimes, param2));
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param2;
    }
}

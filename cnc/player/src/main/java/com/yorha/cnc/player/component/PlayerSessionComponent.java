package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerLoginDaysIncEvent;
import com.yorha.cnc.player.event.task.PlayerLoginEvent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.UpsertResult;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.PlayerUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.PlayerBasicInfoProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.SessionCloseReason;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 玩家通信
 *
 * <AUTHOR>
 */
public class PlayerSessionComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSessionComponent.class);

    private CommonMsg.ClientInfo clientInfo;
    private String clientIp = "";
    private IActorRef sessionRef;
    private IActorRef migrateSessionRef;

    public PlayerSessionComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        this.clientInfo = CommonMsg.ClientInfo.newBuilder().setPlatformId(-999).build();
    }

    public String getSessionId() {
        if (this.sessionRef == null) {
            return null;
        }
        return this.sessionRef.getActorId();
    }

    public IActorRef getSessionRef() {
        return sessionRef;
    }

    /**
     * 获取SessionRef的Data。
     *
     * @return sessionRef。
     */
    public CommonMsg.ActorRefData getSessionRefData() {
        return MsgHelper.buildRefMsg(sessionRef);
    }

    /**
     * 此时必须自身没有 this.sessionRef
     */
    public boolean checkSetSessionRef(IActorRef sessionRef) {
        if (sessionRef == null) {
            LOGGER.error("trySetSessionRef sessionRef is null");
            return false;
        }
        if (!StringUtils.equals(sessionRef.getActorRole(), ActorRole.GateSession.name())) {
            LOGGER.error("trySetSessionRef sessionRef is not session");
            return false;
        }
        if (this.sessionRef != null) {
            LOGGER.error("trySetSessionRef this.sessionRef is not null");
            return false;
        }
        return true;
    }

    /**
     * 外面的sessionRef必须等于自己的 this.sessionRef
     */
    public boolean checkDisconnect(IActorRef sessionRef) {
        if (sessionRef == null) {
            LOGGER.error("tryDisconnect sessionRef is null");
            return false;
        }
        if (!StringUtils.equals(sessionRef.getActorRole(), ActorRole.GateSession.name())) {
            LOGGER.error("tryDisconnect sessionRef is not session");
            return false;
        }
        if (this.sessionRef == null) {
            LOGGER.info("tryDisconnect this.sessionRef is null");
            return false;
        }
        if (this.sessionRef.equals(sessionRef)) {
            return true;
        } else {
            LOGGER.info("tryDisconnect this {} other {}", this.sessionRef, sessionRef);
            return false;
        }
    }

    public boolean isOnline() {
        return this.sessionRef != null;
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        tryAddLoginDays();
        new PlayerLoginEvent(getOwner()).dispatch();
        updateLastLoginPlayerAndKickOffRepeatLogin();
        this.getOwner().getDbComponent().updateSimplePlayerFull();
    }

    /**
     * 所有组件onLogin前调用
     */
    public void setSessionRef(IActorRef sessionRef) {
        // 绑定session
        if (!this.checkSetSessionRef(sessionRef)) {
            throw new GeminiException("session ref not right! sessionRef={}", sessionRef);
        }
        this.sessionRef = sessionRef;
    }

    public void tryAddLoginDays() {
        PlayerBasicInfoProp basicInfo = getOwner().getProp().getBasicInfo();
        final long lastUpdateLoginDaysTsMs = basicInfo.getLastUpdateLoginDaysTsMs();
        final long nowMs = SystemClock.now();
        basicInfo.setLastUpdateLoginDaysTsMs(nowMs);
        if (!TimeUtils.isSameDay(lastUpdateLoginDaysTsMs, nowMs)) {
            getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.LOGIN_DAYS, 1);
            new PlayerLoginDaysIncEvent(getOwner()).dispatch();
        }
    }

    /**
     * 只允许顶号的地方调用，其他要踢人请用kickOffMe
     */
    public void tryKickOutPreSession() {
        if (!isOnline()) {
            return;
        }
        // 有连接的情况下 再次有人登录 / 开服踢人
        LOGGER.info("{} atPlayer kickOut tryKickOutPreSession kick old session {} {}", LogKeyConstants.GAME_PLAYER_LOGIN, getSessionId(), getOwner());
        // 前一个连接走一遍离线流程
        getOwner().kickOffMe(SessionCloseReason.SCR_KICKOFF_SAME_ACCOUNT);
    }

    /**
     * 只能被onDisconnect调用
     */
    public void onLogout(SessionCloseReason closeReason) {
        if (!isOnline()) {
            // 无连接的情况下 断开
            LOGGER.error("{} logout but has no oldSession", getOwner());
            return;
        }

        // 下线后开启淘汰
        ownerActor().setReceiveTimeout(60);

        getOwner().getProp().getBasicInfo().setLastLogoutTsMs(SystemClock.now());
        updateOnlineMs();
        getOwner().getDbComponent().updateSimplePlayerFull();
        getOwner().getQlogComponent().sendLogoutQlog(closeReason);
        this.sessionRef = null;
        clientIp = "";
        // 通知地图
        LOGGER.info("{} atPlayer logout, call scene. sceneId={} playerId={} closeReason={}", LogKeyConstants.GAME_PLAYER_LOGIN, getOwner().getCurSceneId(), getPlayerId(), closeReason);
        try {
            getOwner().getViewComponent().clearView();
            getOwner().getSceneMgrComponent().playerLogout();
        } catch (Exception e) {
            // 防止打断登出过程
            LOGGER.error("onLogout failed ", e);
        }
    }

    /**
     * 更新在线时长
     */
    private void updateOnlineMs() {
        if (getOwner().getProp().getBasicInfo().getLastLoginTsMs() <= 0) {
            LOGGER.error("loginMs error, ms:{}", getOwner().getProp().getBasicInfo().getLastLoginTsMs());
            return;
        }
        long onlineMs = SystemClock.now() - getOwner().getProp().getBasicInfo().getLastLoginTsMs();
        if (onlineMs <= 0) {
            LOGGER.error("onlineMs error, ms:{}", getOwner().getProp().getBasicInfo().getLastLoginTsMs());
            return;
        }
        getOwner().getProp().getBasicInfo().setOnlineTimeMs(getOwner().getProp().getBasicInfo().getOnlineTimeMs() + onlineMs);
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public CommonMsg.ClientInfo getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(CommonMsg.ClientInfo clientInfo, boolean needNtfSession) {
        LOGGER.info("{} atPlayer bind clientInfo, {} client info changed. oldClientInfo={}, newClientInfo={}", LogKeyConstants.GAME_PLAYER_LOGIN, getOwner(), this.clientInfo, clientInfo);
        this.clientInfo = clientInfo;

        if (!needNtfSession) {
            return;
        }

        // 修改需要通知session
        final SsGateSession.UpdateClientInfoCmd cmd = SsGateSession.UpdateClientInfoCmd.newBuilder()
                .setNewClientInfo(clientInfo)
                .build();
        ActorSendMsgUtils.send(sessionRef, cmd);
        // 通知大世界修改推送语言设置
        ownerActor().tellBigScene(SsScenePlayer.SyncPlayerPushNtfInfoAsk.newBuilder()
                .setPlayerId(getPlayerId())
                .setLanguage(clientInfo.getLanguage())
                .build());
    }

    /**
     * 更新最近登录玩家&踢本账号下其它重复登录角色
     */
    private void updateLastLoginPlayerAndKickOffRepeatLogin() {
        Pair<Long, Integer> lastLoginPlayerIdWithVersion = PlayerUtils.getLastLoginPlayerIdWithVersion(this.getOwner().getOpenId(), this.ownerActor());
        // 是否有历史登录记录
        if (lastLoginPlayerIdWithVersion.getFirst() != 0) {
            // 踢人
            this.kickOtherPlayerInSameAccount(lastLoginPlayerIdWithVersion.getFirst());
        }

        this.updateLastLoginPlayerIdTillSuccess(lastLoginPlayerIdWithVersion.getSecond());
    }

    /**
     * 踢同账号下重复登录角色
     *
     * @param playerId 玩家id
     */
    private void kickOtherPlayerInSameAccount(long playerId) {
        final boolean accountCanRepeatLogin = ClusterConfigUtils.getWorldConfig().getBooleanItem("account_repeat_login");
        if (accountCanRepeatLogin) {
            LOGGER.info("kickOtherPlayerInSameAccount world config account_repeat_login={}, skip", accountCanRepeatLogin);
            return;
        }
        // 避免踢出自己（先前已尝试踢出自己身上的重复连接）
        if (playerId == this.getPlayerId()) {
            return;
        }
        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder();
        req.setPlayerId(playerId).setOpenId(this.getOwner().getProp().getOpenId());
        GetResult<TcaplusDb.AccountRoleTable.Builder> getResult = null;

        try {
            getResult = this.ownerActor().callGameDb(new SelectUniqueAsk<>(req, GetOption.newBuilder().build()));
        } catch (Exception e) {
            throw new GeminiException("AccountRoleTable fail", e);
        }

        // DB操作失败
        if (!getResult.isOk()) {
            throw new GeminiException("kickOtherPlayerInSameAccount tcaplus errorCode={}", getResult.getCode());
        }
        int zoneId = getResult.value.getRoleInfo().getZoneId();
        // 注意，踢掉后会导致被踢账号的三方推送失效
        this.ownerActor().tellPlayer(zoneId, playerId, SsPlayer.KickOffPlayerCmd.newBuilder().setCloseReason(SessionCloseReason.SCR_KICKOFF_ACCOUNT).build());
    }

    /**
     * 更新最近登录角色直至获取到乐观锁（乐观锁冲突时踢人）
     *
     * @param tcaplusVersion tcaplus数据版本
     */
    private void updateLastLoginPlayerIdTillSuccess(int tcaplusVersion) {
        final String openId = this.getOwner().getProp().getOpenId();
        TcaplusDb.KVStoreTable.Builder builder = TcaplusDb.KVStoreTable.newBuilder()
                .setKey(openId)
                .setZoneId(openId.hashCode());
        builder.getValueBuilder().setLongValue(this.getPlayerId());

        while (true) {
            UpsertOption.Builder updateOption = UpsertOption.newBuilder()
                    .setVersion(tcaplusVersion)
                    .setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL);
            UpsertResult<TcaplusDb.KVStoreTable.Builder> result;
            try {
                result = this.ownerActor().callGameDb(new UpsertAsk<>(builder, updateOption.build()));
            } catch (Exception e) {
                throw new GeminiException("updateLastLoginPlayerIdTillSuccess fail", e);
            }

            if (result.isOk()) {
                return;
            }

            if (!result.isInvalidVersion()) {
                throw new GeminiException("updateLastLoginPlayerIdTillSuccess fail tcaplus errorCode={}", result.getCode());
            }

            long repeatLoginPlayerId = result.value.getValue().getLongValue();
            LOGGER.info("updateLastLoginPlayerIdTillSuccess kick Repeat login player={}", repeatLoginPlayerId);
            this.kickOtherPlayerInSameAccount(repeatLoginPlayerId);
        }

    }

    public IActorRef getMigrateSessionRef() {
        return migrateSessionRef;
    }

    public void setMigrateSessionRef(IActorRef sessionRef) {
        this.migrateSessionRef = sessionRef;
    }
}

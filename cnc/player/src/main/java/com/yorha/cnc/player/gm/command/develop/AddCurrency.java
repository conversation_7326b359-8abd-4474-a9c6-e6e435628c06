package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AddCurrency implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int type = Integer.parseInt(args.get("currencyType"));
        CurrencyType currencyType = CurrencyType.forNumber(type);
        if (currencyType == null) {
            throw new GeminiException(ErrorCode.CURRENCY_NOT_EXIST.getCodeId());
        }
        int count = 1;
        if (args.contains<PERSON>ey("count")) {
            count = Integer.parseInt(args.get("count"));
        }
        if (currencyType == CurrencyType.CT_None) {
            for (int i = 1; i <= 5; i++) {
                actor.getEntity().getPurseComponent().give(CurrencyType.forNumber(i), count, CommonEnum.Reason.ICR_GM, "");
            }
        } else {
            actor.getEntity().getPurseComponent().give(currencyType, count, CommonEnum.Reason.ICR_GM, "");
        }
    }

    @Override
    public String showHelp() {
        return "AddCurrency currencyType={value} count={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

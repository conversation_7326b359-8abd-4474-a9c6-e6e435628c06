package com.yorha.cnc.player.gm.command.rh;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.InnerBuildRhTemplate;

import java.util.Map;

/**
 * RH升级建筑
 */
public class UpgradeInnerBuild implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int type = Integer.parseInt(args.get("type"));
        int level = Integer.parseInt(args.get("level"));
        CommonEnum.CityBuildType innerBuildTypeRH = CommonEnum.CityBuildType.forNumber(type);
        if (innerBuildTypeRH == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE.getCodeId());
        }
        InnerBuildRhTemplate buildRhTemplate = ResHolder.getInstance().findValueFromMap(InnerBuildRhTemplate.class, type);
        if (buildRhTemplate == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_INFOR.getCodeId());
        }

        if (level > 0) {
            actor.getEntity().getInnerBuildRhComponent().upgradeNewInnerBuildGM(type, level);
        }
    }

    @Override
    public String showHelp() {
        return "UpgradeInnerBuild type={value} level={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_INNER_BUILD;
    }
}

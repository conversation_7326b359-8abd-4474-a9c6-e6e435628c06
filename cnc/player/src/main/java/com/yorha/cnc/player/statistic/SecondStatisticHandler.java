package com.yorha.cnc.player.statistic;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.game.gen.prop.Int32SingleStatisticMapProp;
import com.yorha.game.gen.prop.SecondStatisticProp;
import com.yorha.game.gen.prop.SingleStatisticProp;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;

/**
 * 二维条件统计数据handler
 *
 * <AUTHOR>
 */
public class SecondStatisticHandler extends AbstractStatisticHandler {

    public SecondStatisticHandler(PlayerEntity entity, StatisticEnum type) {
        super(entity, type);
    }


    public void update(int id, long value) {
        SingleStatisticProp ssp = getProp().computeIfAbsent(id, k -> {
            SingleStatisticProp prop = new SingleStatisticProp();
            prop.setId(k);
            return prop;
        });
        recordCalc(ssp, value);
    }

    public long getValue(int id) {
        SingleStatisticProp singleStatisticProp = getProp().get(id);
        if (singleStatisticProp == null) {
            return 0;
        }
        return singleStatisticProp.getValue();
    }

    public StructPB.Int32SingleStatisticMapPB getAllPBValue() {
        return getProp().getCopyCsBuilder().build();
    }

    public Struct.Int32SingleStatisticMap getAllValue() {
        return getProp().getCopySsBuilder().build();
    }

    public long sumValue() {
        int sum = 0;
        for (SingleStatisticProp prop : getProp().values()) {
            sum += prop.getValue();
        }
        return sum;
    }

    private Int32SingleStatisticMapProp getProp() {
        SecondStatisticProp secondStatisticProp = getOwner().getProp().getStatisticModel().getSecondStatisticMap().computeIfAbsent(getStatisticEnum().ordinal(), (key) -> {
            SecondStatisticProp prop = new SecondStatisticProp();
            prop.setEnumId(key);
            return prop;
        });
        return secondStatisticProp.getSingleStatisticMap();
    }


    @Override
    public void resetValue() {
        for (SingleStatisticProp prop : getProp().values()) {
            prop.setValue(0);
        }
    }
}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon.Player_UseItem_S2C.Builder;
import res.template.ItemTemplate;

/**
 * 通用队列加速
 *
 * <AUTHOR>
 * 2021年11月19日 12:28:00
 */
public class CommonQueueSpeed extends AbstractUsableItem {

    public CommonQueueSpeed(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);

    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        throw new GeminiException(ErrorCode.ITEM_NOT_USEABLE);
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        return false;
    }

    @Override
    public void responseMessage(Builder response) {

    }
}

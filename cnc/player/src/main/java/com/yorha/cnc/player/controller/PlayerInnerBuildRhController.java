package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.enums.BuildResourceMapType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Assertions;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerInnerBuildLogic;
import com.yorha.proto.PlayerNewInnerBuild;
import com.yorha.proto.PlayerNewInnerBuild.*;
import com.yorha.proto.StructPB.PointPB;
import res.template.BaseExpandRhTemplate;

import java.util.Map;

/**
 * 新内城相关接口
 */
@Controller(module = CommonEnum.ModuleEnum.ME_NEW_INNER_BUILD)
public class PlayerInnerBuildRhController {

    /**
     * 放置建筑
     */
    @CommandMapping(code = MsgType.PLAYER_PRODUCTNEWINNERBUILD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ProductNewInnerBuild_C2S msg) {

        int buildId = msg.getBuildId();
        PointPB point = msg.getPoint();
        playerEntity.getInnerBuildRhComponent().productNewInnerBuild(buildId, point);
        return Player_ProductNewInnerBuild_S2C.getDefaultInstance();
    }

    /**
     * 收取内城矿点资源
     */
    @CommandMapping(code = MsgType.PLAYER_RECVINNERMINERES_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RecvInnerMineRes_C2S msg, int seqId) {
        Map<CommonEnum.CurrencyType, Long> currencyTypeLong = playerEntity.getResourceProduceComponent().handleReceiveResource(BuildResourceMapType.getAllResourceBuildType());
        PlayerNewInnerBuild.Player_RecvInnerMineRes_S2C.Builder builder = PlayerNewInnerBuild.Player_RecvInnerMineRes_S2C.newBuilder();

        for (Map.Entry<CommonEnum.CurrencyType, Long> entry : currencyTypeLong.entrySet()) {
            builder.addReceiveInfoList(PlayerInnerBuildLogic.ReceiveInfo.newBuilder().setType(entry.getKey()).setValue(entry.getValue()));
        }

        return builder.build();
    }

    /**
     * 移动建筑
     */
    @CommandMapping(code = MsgType.PLAYER_MOVEINNERBUILD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_MoveInnerBuild_C2S msg) {
        playerEntity.getInnerBuildRhComponent().moveNewInnerBuild(msg.getBuildId(), msg.getPoint());
        return Player_MoveInnerBuild_S2C.getDefaultInstance();
    }

    /**
     * 升级内城建筑
     */
    @CommandMapping(code = MsgType.PLAYER_UPGRADEINNERBUILD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_UpgradeInnerBuild_C2S msg) {
        playerEntity.getInnerBuildRhComponent().upgradeNewInnerBuild(msg.getBuildId());
        Player_UpgradeInnerBuild_S2C.Builder builder = Player_UpgradeInnerBuild_S2C.newBuilder();
        builder.setBuildId(msg.getBuildId());
        return builder.build();
    }

    /**
     * 解锁新区域
     */
    @CommandMapping(code = MsgType.PLAYER_UNLOCKNEWAREA_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_UnlockNewArea_C2S msg, int seqId) {
        var areaId = msg.getAreaId();
        var playerId = playerEntity.getPlayerId();

        var baseAreaProp = playerEntity.getInnerBuildRhComponent().getBaseAreaProp(areaId);
        // PVE关卡通关后会自动创建baseAreaProp
        Assertions.requireNotNull(baseAreaProp, () -> "Can't find area, areaId:" + areaId + ", playerId:" + playerId);

        //确保baseAreaProp处于未解锁状态
        var areaIsLocked = baseAreaProp.getState() == CommonEnum.BaseAreaStateRH.BaseAreaStateRH_LOCK;
        Assertions.require(areaIsLocked, () -> "area state isn't locked, areaId:" + areaId + ", playerId:" + playerId);

        baseAreaProp.setState(CommonEnum.BaseAreaStateRH.BaseAreaStateRH_UNLOCK);

        return Player_UnlockNewArea_S2C.getDefaultInstance();
    }

}

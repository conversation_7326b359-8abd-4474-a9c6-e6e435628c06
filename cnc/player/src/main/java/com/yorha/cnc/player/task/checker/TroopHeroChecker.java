package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.TroopHeroChangeEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

public class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            TroopHeroChangeEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int troopId = taskParams.get(0);
        int count = taskParams.get(1);
        var player = event.getPlayer();

        if (troopId == 0) {
            int maxCount = 0;
            for (var troop : player.getTroopFormationComponent().getFormationProp().getTroopRHMap().values()) {
                if (troop.getTroopSize() > maxCount) {
                    maxCount = troop.getTroopSize();
                }
            }
            prop.setProcess(Math.min(count, maxCount));
        } else {
            var troop = player.getTroopFormationComponent().getFormationProp().getTroopRHMap().get(troopId);
            if (troop != null) {
                prop.setProcess(Math.min(count, troop.getTroopSize()));
            }
        }

        return prop.getProcess() >= count;
    }
}
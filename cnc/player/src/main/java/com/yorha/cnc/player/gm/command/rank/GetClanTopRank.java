package com.yorha.cnc.player.gm.command.rank;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanRank.GetTopClanRankInfoAns;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 获取联盟榜首
 *
 * <AUTHOR> Jiang
 */
public class GetClanTopRank implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GetClanTopRank.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        GetTopClanRankInfoAns answer = actor.getEntity().getPlayerClanComponent().getTopClanRankInfo();
        LOGGER.debug("{}", answer);
    }

    @Override
    public String showHelp() {
        return "GetClanTopRank";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_RANK;
    }
}
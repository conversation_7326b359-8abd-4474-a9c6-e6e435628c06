package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerConsumeCurrencyEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

public class ConsumeCurrencyChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerConsumeCurrencyEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        final int type = taskParams.get(0);
        final int require = taskParams.get(1);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE: {
                if (event instanceof PlayerConsumeCurrencyEvent) {
                    PlayerConsumeCurrencyEvent e = (PlayerConsumeCurrencyEvent) event;
                    if (e.getCurrencyType().getNumber() == type) {
                        int consumeCount = (int) e.getConsumeCount();
                        prop.setProcess(Math.min(require, prop.getProcess() + consumeCount));
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= require;
    }
}
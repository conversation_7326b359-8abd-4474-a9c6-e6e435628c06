package com.yorha.cnc.player.gm.command.rh;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB.PointPB;

import java.util.Map;

public class BuildRH implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int id = Integer.parseInt(args.get("id"));
        int x = Integer.parseInt(args.get("pointX"));
        int y = Integer.parseInt(args.get("pointY"));

        PointPB point = PointPB.newBuilder().setX(x).setY(y).build();
        actor.getEntity().getInnerBuildRhComponent().productNewInnerBuild(id, point);
    }

    @Override
    public String showHelp() {
        return "BuildRH type";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_INNER_BUILD;
    }
}

package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.component.PlayerMissionComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

//完成关卡
public class CompleteMission implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(CompleteMission.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int missionId = Integer.parseInt(args.get("missionId"));
        final PlayerMissionComponent missionComponent = actor.getEntity().getMissionComponent();
        missionComponent.completeMissionGm(missionId);
        LOGGER.info("CompleteMission missionId={}", missionId);
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "CompleteMission missionId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

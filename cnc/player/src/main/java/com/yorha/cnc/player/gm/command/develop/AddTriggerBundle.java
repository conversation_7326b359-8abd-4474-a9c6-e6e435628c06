package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.TriggerGoodsTemplate;

import java.util.Map;

public class AddTriggerBundle implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("templateId"));
        int count = 1;
        if (args.size() > 1) {
            count = Integer.parseInt(args.get("count"));
        }
        TriggerGoodsTemplate template = ResHolder.findTemplate(TriggerGoodsTemplate.class, templateId);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        for (int i = 0; i < count; i++) {
            actor.getEntity().getTriggerBundleComponent().addBundle(template);
        }
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "AddTriggerBundle templateId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }
}

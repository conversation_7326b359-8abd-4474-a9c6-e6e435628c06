package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 用于还原patch包的版本号
 */
public class ResetPatchVersion  implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int version = Integer.parseInt(args.get("version"));
        actor.getEntity().getProp().setDataPatchVersion(version);
        actor.destroyAsync("GM");
    }

    @Override
    public String showHelp() {
        return "ResetPatchVersion version=2";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

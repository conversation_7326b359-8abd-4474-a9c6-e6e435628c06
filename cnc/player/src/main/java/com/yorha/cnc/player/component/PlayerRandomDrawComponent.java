package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.game.gen.prop.DrawInfoProp;
import com.yorha.game.gen.prop.Int32DrawInfoMapProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class PlayerRandomDrawComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerRandomDrawComponent.class);

    public PlayerRandomDrawComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 保底前抽取次数+1
     *
     * @param randomPoolId 随机道具池id
     */
    public void increaseRandomTimesBeforeGuarantee(int randomPoolId) {
        DrawInfoProp drawInfoProp = this.getDrawInfo().computeIfAbsent(randomPoolId, id -> new DrawInfoProp().setItemId(id));
        int newRandomTimesBeforeGuarantee = drawInfoProp.getDrawTimesBeforeGuarantee() + 1;
        drawInfoProp.setDrawTimesBeforeGuarantee(newRandomTimesBeforeGuarantee);
        LOGGER.info("{} randomPooId={} increase randomTimes, new randomTime={}", getOwner(), randomPoolId, newRandomTimesBeforeGuarantee);
    }

    /**
     * 获取保底前抽取次数
     *
     * @param randomPoolId 随机道具池id
     * @return 保底前抽取次数
     */
    public int getRandomTimesBeforeGuarantee(int randomPoolId) {
        DrawInfoProp drawInfo = this.getDrawInfo().get(randomPoolId);
        int randomTimes = drawInfo == null ? 0 : drawInfo.getDrawTimesBeforeGuarantee();
        LOGGER.info("{} randomPooId={} randomTimes={}", getOwner(), randomPoolId, randomTimes);
        return randomTimes;
    }

    /**
     * 清空抽取次数
     *
     * @param randomPoolId 随机道具池id
     */
    public void clearRandomTimes(int randomPoolId) {
        LOGGER.info("{} clear randomPooId={} randomTimes", getOwner(), randomPoolId);
        this.getDrawInfo().remove(randomPoolId);
    }

    private Int32DrawInfoMapProp getDrawInfo() {
        return this.getOwner().getProp().getDrawInfo();
    }
}

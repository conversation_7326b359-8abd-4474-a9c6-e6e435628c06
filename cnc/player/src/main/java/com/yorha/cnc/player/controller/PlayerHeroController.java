package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.reason.HeroSkillUpReason;
import com.yorha.common.enums.reason.HeroStarUpReason;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerHero.*;

/**
 * 英雄养成相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_HERO)
public class PlayerHeroController {
    /**
     * 解锁英雄
     */
    @CommandMapping(code = MsgType.PLAYER_UNLOCKHERO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_UnLockHero_C2S msg) {
        int heroId = msg.getHeroId();
        playerEntity.getHeroComponent().unLockHero(heroId);
        return Player_UnLockHero_S2C.getDefaultInstance();
    }


    /**
     * 英雄升级
     */
    @CommandMapping(code = MsgType.PLAYER_HEROLEVELUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroLevelUp_C2S msg) {
        int heroId = msg.getHeroId();
        playerEntity.getHeroComponent().handleHeroLevelUp(heroId);
        return Player_HeroLevelUp_S2C.getDefaultInstance();
    }

    /**
     * 英雄升星
     */
    @CommandMapping(code = MsgType.PLAYER_HEROSTAGEUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroStageUp_C2S msg) {
        int heroId = msg.getHeroId();
        boolean common = msg.getCommon();
        playerEntity.getHeroComponent().handleHeroStageUp(heroId, common, HeroStarUpReason.HERO_STAR_UP);
        return Player_HeroStageUp_S2C.newBuilder().build();
    }

    /**
     * 英雄技能提升
     */
    @CommandMapping(code = MsgType.PLAYER_HEROSKILLLEVELUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroSkillLevelUp_C2S msg) {
        int heroId = msg.getHeroId();
        int skillGroupId = msg.getSkillGroupId();
        playerEntity.getHeroComponent().handleSkillLevelUp(heroId, skillGroupId, HeroSkillUpReason.HERO_SKILL_UP);
        return Player_HeroSkillLevelUp_S2C.getDefaultInstance();
    }


    /// ////////////以下的暂时废弃///////////////
    /**
     * 英雄技能重置
     */
    @CommandMapping(code = MsgType.PLAYER_RESETSKILL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ResetSkill_C2S msg) {
        int heroId = msg.getHeroId();
        playerEntity.getHeroComponent().resetSkillHandler(heroId);
        return Player_ResetSkill_S2C.getDefaultInstance();
    }

    /**
     * 英雄设置技能槽位限制
     */
    @CommandMapping(code = MsgType.PLAYER_CHANGESKILLSLOTLIMIT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ChangeSkillSlotLimit_C2S msg) {
        int heroId = msg.getHeroId();
        int slot = msg.getSlotLimit();
        playerEntity.getHeroComponent().setSkillSlotLimitHandler(heroId, slot);
        return Player_ChangeSkillSlotLimit_S2C.getDefaultInstance();
    }

    /**
     * 英雄技能觉醒
     */
    @CommandMapping(code = MsgType.PLAYER_HEROINTENSIVESKILL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroInterensiveSkill_C2S msg) {
        int heroId = msg.getHeroId();
        playerEntity.getHeroComponent().heroIntensiveSkill(heroId);
        return Player_HeroInterensiveSkill_S2C.getDefaultInstance();
    }

    /**
     * 英雄天赋升级
     */
    @CommandMapping(code = MsgType.PLAYER_HEROTALENTLEVELUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroTalentLevelUp_C2S msg) {
//        int heroId = msg.getHeroId();
//        int pageId = msg.getPageSlot();
//        int groupId = msg.getTalentGroupId();
//        playerEntity.getHeroComponent().heroTalentLevelUpHandler(heroId, pageId, groupId);
        return Player_HeroTalentLevelUp_S2C.getDefaultInstance();
    }

    /**
     * 切换天赋页
     */
    @CommandMapping(code = MsgType.PLAYER_SWITCHTALENTPAGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SwitchTalentPage_C2S msg) {
        int heroId = msg.getHeroId();
        int pageSlot = msg.getPageSlot();
        playerEntity.getHeroComponent().switchTalentPageHandler(heroId, pageSlot, false);
        return Player_SwitchTalentPage_S2C.getDefaultInstance();
    }

    /**
     * 天赋页重命名
     */
    @CommandMapping(code = MsgType.PLAYER_RESETNAMETALENTPAGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ResetNameTalentPage_C2S msg) {
        int heroId = msg.getHeroId();
        String name = msg.getName();
        int pageSlot = msg.getPageSlot();
        playerEntity.getHeroComponent().resetNameTalentPageHandler(heroId, pageSlot, name);
        return Player_ResetNameTalentPage_S2C.getDefaultInstance();
    }


    /**
     * 天赋页重置
     */
    @CommandMapping(code = MsgType.PLAYER_HERORESETTALENT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroResetTalent_C2S msg) {
        int heroId = msg.getHeroId();
        int pageSlot = msg.getPageSlot();
        playerEntity.getHeroComponent().heroResetTalentHandler(heroId, pageSlot, false);
        return Player_ResetNameTalentPage_S2C.getDefaultInstance();
    }

    /**
     * 英雄碎片兑换
     */
    @CommandMapping(code = MsgType.PLAYER_HEROITEMEXCHANGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_HeroItemExchange_C2S msg) {
//        int heroId = msg.getHeroId();
//        int exChangeHeroChipNum = msg.getExChangeHeroChipNum();
//        playerEntity.getHeroComponent().exChangeHeroChip(heroId, exChangeHeroChipNum, msg.getSPassWord());
        return Player_HeroItemExchange_C2S.getDefaultInstance();
    }

}

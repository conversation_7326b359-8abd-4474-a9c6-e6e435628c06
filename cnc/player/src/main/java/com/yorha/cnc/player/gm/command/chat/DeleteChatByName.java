package com.yorha.cnc.player.gm.command.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 删除聊天名称对应的聊天。
 *
 * <AUTHOR>
 */
public class DeleteChatByName implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        final String channelName = args.get("channelName");
        final int channelType = Integer.parseInt(args.get("channelType"));
        final CommonEnum.ChatChannel chatChannel = CommonEnum.ChatChannel.forNumber(channelType);
        if (StringUtils.isEmpty(channelName)) {
            throw new GeminiException("channelName is empty");
        }
        if (chatChannel == null) {
            throw new GeminiException("channelType={} is not right", args.get("channelType"));
        }

        final ChannelInfoProp channelInfoProp = actor.getOrLoadChatPlayerEntity().getProp().getChannelInfoV(chatChannel.getNumber());
        for (final String chatId : channelInfoProp.getItem().keySet()) {
            final ChatItemProp itemV = channelInfoProp.getItemV(chatId);
            if (itemV.getGroupName().equals(channelName)) {
                actor.getOrLoadChatPlayerEntity().getHandleChatComponent().clearChannelData(chatChannel, chatId);
                break;
            }
        }
    }

    @Override
    public String showHelp() {
        return "DeleteChatByName channelType={} channelName={}";
    }
}

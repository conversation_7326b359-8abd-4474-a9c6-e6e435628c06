package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;

import java.util.Map;

/**
 * 添加个人邮件GM
 *
 * <AUTHOR>
 */

public class AddPersonalMail implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("mailTemplateId"));
        long targetId = Long.parseLong(args.get("targetId"));
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(templateId);
        if (targetId == 0) {
            targetId = actor.getPlayerId();
        }
        actor.getEntity().getMailComponent().sendPersonalMail(CommonMsg.MailReceiver.newBuilder().setPlayerId(playerId).setZoneId(actor.getZoneId()).build(), builder.build());
    }

    @Override
    public String showHelp() {
        return "AddPersonalMail mailTemplateId={value} targetId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAIL;
    }
}

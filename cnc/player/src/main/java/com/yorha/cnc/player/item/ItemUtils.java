package com.yorha.cnc.player.item;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.cnc.player.item.use.impl.*;
import com.yorha.common.resource.resservice.item.ItemWithdrawConf;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ItemUseType;
import res.template.ItemTemplate;

import java.time.Instant;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 */
public abstract class ItemUtils {

    public static AbstractUsableItem newInstance(ItemTemplate itemTemplate, int num, ItemUseType useType) {
        AbstractUsableItem item = null;
        switch (useType) {
            case CURRENCY:
                item = new CurrencyUsableItem(num, itemTemplate);
                break;
            case NEW_PLAYER_MOVE_CITY:
                item = new MoveCityUsableItem(num, itemTemplate, CommonEnum.MoveCityType.MCT_NEWPLAYER);
                break;
            case TERRITORY_MOVE_CITY:
                item = new MoveCityUsableItem(num, itemTemplate, CommonEnum.MoveCityType.MCT_TERRITORY);
                break;
            case MOVE_CITY:
                item = new MoveCityUsableItem(num, itemTemplate, CommonEnum.MoveCityType.MCT_NORMAL);
                break;
            case PICK_UP_BOX:
                item = new PickUpBoxUsableItem(num, itemTemplate);
                break;
            case COMPOUND_BOX:
                item = new CompoundBoxUsableItem(num, itemTemplate);
                break;
            case MOVE_CITY_RANDOM:
                item = new MoveCityRandomUsableItem(num, itemTemplate);
                break;
            case TEMP_BUILD_QUEUE:
                item = new UnLockCityBuildQueue(num, itemTemplate);
                break;
            case COMMON_SPEED:
                item = new CommonQueueSpeed(num, itemTemplate);
                break;
            case MODIFY_PLAYER_NAME:
                item = new ModifyPlayerNameUsableItem(num, itemTemplate);
                break;
            case ADD_BUFF:
                item = new BuffItem(num, itemTemplate);
                break;
            case ADD_SOLDIER:
                item = new AddSoldierItem(num, itemTemplate);
                break;
            case RESET_TRANSPORT_CD:
                item = new ResetTransportCd(num, itemTemplate);
                break;
            case PLAYER_HEAD_PIC_UNLOCK:
                item = new UnlockPlayerPicUsableItem(num, itemTemplate);
                break;
            case PLAYER_HEAD_PICFRAME_UNLOCK:
                item = new UnlockPlayerPicFrameUsableItem(num, itemTemplate);
                break;
            case ADD_ENERGY:
                item = new AddEnergyItem(num, itemTemplate);
                break;
            case RECRUIT_HERO:
                item = new RecruitHeroItem(num, itemTemplate);
                break;
            case CREATE_MONSTER:
                item = new CreateMonsterItem(num, itemTemplate);
                break;
            case CLAN_SCORE:
                item = new ClanScoreItem(num, itemTemplate);
                break;
            case PERSONAL_CLAN_SCORE:
                item = new PersonalClanScoreItem(num, itemTemplate);
                break;
            case EXPRESSION_UNLOCK:
                item = new UnlockExpressionItem(num, itemTemplate);
                break;
            case VIP_EXP:
                item = new VipExpItem(num, itemTemplate);
                break;
            case SECOND_QUEUE_FOREVER:
                item = new SecondBuildQueueForeverUnlockItem(num, itemTemplate);
                break;
            case CITY_DRESS:
                item = new UnlockCityDressItem(num, itemTemplate);
                break;
            case CITY_NAMEPLATE:
                item = new UnLockCityNameplate(num, itemTemplate);
                break;
            case RANDOM_WITH_GUARANTEE:
                item = new RandomWithGuaranteeItem(num, itemTemplate);
                break;
            case UP_TRAIN:
                item = new UpTrainItem(num, itemTemplate);
                break;
            case CONTINUES_GIFT_DESCOUNT:
                item = new ContinuesGiftDiscountItem(num, itemTemplate);
                break;
            default:
        }
        return item;
    }

    public static boolean shouldWithdrawNow(PlayerEntity player, ItemWithdrawConf.List confList) {
        return confList.getConfList().stream().anyMatch(conf -> {
            if (conf instanceof ItemWithdrawConf.PinnedDate) {
                return SystemClock.nowZonedDateTime().isAfter(((ItemWithdrawConf.PinnedDate) conf).getDate());

            } else if (conf instanceof ItemWithdrawConf.FromRoleCreateHours) {
                return SystemClock.nowZonedDateTime().isAfter(expireTimeOf((ItemWithdrawConf.FromRoleCreateHours) conf, player));

            } else if (conf instanceof ItemWithdrawConf.CityLevel) {
                return player.getCityLevel() >= ((ItemWithdrawConf.CityLevel) conf).getCityLevel();
            } else {
                return false;
            }
        });
    }

    private static ZonedDateTime expireTimeOf(ItemWithdrawConf.FromRoleCreateHours fromRoleCreateHours, PlayerEntity player) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(player.getProp().getCreateTime()), TimeUtils.getZoneId())
                .plusHours(fromRoleCreateHours.getHours());
    }

    public static long expireMillis(PlayerEntity player, ItemWithdrawConf.List confList) {
        if (confList == null) {
            return 0;
        }
        return confList.getConfList().stream().mapToLong(conf -> {
            if (conf instanceof ItemWithdrawConf.PinnedDate) {
                return ((ItemWithdrawConf.PinnedDate) conf).getDate().toInstant().toEpochMilli();

            } else if (conf instanceof ItemWithdrawConf.FromRoleCreateHours) {
                return expireTimeOf((ItemWithdrawConf.FromRoleCreateHours) conf, player).toInstant().toEpochMilli();
            } else {
                return 0L;
            }
        }).max().orElse(0);
    }

}

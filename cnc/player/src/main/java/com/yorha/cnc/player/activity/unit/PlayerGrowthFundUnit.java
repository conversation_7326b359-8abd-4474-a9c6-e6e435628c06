package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.goods.ActivityNormalGoods;
import com.yorha.cnc.player.task.AbstractTaskHandler;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.payment.PaymentResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityGrowthFundUnitProp;
import com.yorha.game.gen.prop.ActivityTaskUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import res.template.ChargeGoodsTemplate;
import res.template.ConstActivityTemplate;

import java.util.List;

/**
 * 成长基金(付费任务那部分的逻辑)
 */
public class PlayerGrowthFundUnit extends PlayerActivityTaskUnit implements ActivityNormalGoods.ActUnitGoodsHandler {

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_GROWTH_FUND, (owner, prop, template) -> new PlayerGrowthFundUnit(
                owner, prop.getSpecUnit(), template.getTaskPoolName(), template.getGrowthFundTaskIdsList()
        ));
    }

    public PlayerGrowthFundUnit(PlayerActivity activity, ActivityUnitProp prop, String taskPoolName, List<Integer> taskIds) {
        super(activity, prop, taskPoolName, taskIds);
    }

    @Override
    protected ActivityTaskUnitProp getTaskUnitProp() {
        return unitProp.getGrowthFundUnit().getTaskUnit();
    }

    private ActivityGrowthFundUnitProp growthFundUnitProp() {
        return unitProp.getGrowthFundUnit();
    }

    @Override
    public void load(boolean isInitial) {
        if (growthFundUnitProp().getPayTaskUnlocked()) {
            loadTasks();
        }
    }

    @Override
    public boolean isFinished() {
        ActivityGrowthFundUnitProp prop = unitProp.getGrowthFundUnit();
        return prop.getPayTaskUnlocked()
                && ImmutableList.copyOf(prop.getTaskUnit().getTasks().values()).stream()
                .allMatch(AbstractTaskHandler::isFinishState);
    }

    public void unlockPayTasks() {
        ActivityGrowthFundUnitProp prop = growthFundUnitProp();
        if (prop.getPayTaskUnlocked()) {
            WechatLog.error("{} PlayerGrowthFundUnit duplicated unlockPayTasks {}", player(), ownerActivity);
            return;
        }
        prop.setPayTaskUnlocked(true);
        loadTasks();
    }

    @Override
    public void checkApply(ChargeGoodsTemplate goodsTemplate) {
        if (goodsTemplate.getId() != ResHolder.getResService(PaymentResService.class).getGrowthFundGoodsId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "illegal goods id");
        }
        if (unitProp.getGrowthFundUnit().getPayTaskUnlocked()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "already unlocked");
        }
        if (player().getVipComponent().getVipLevel() < ResHolder.getConsts(ConstActivityTemplate.class).getGrowthFundGoodsReqVipLevel()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "vip level not enough");
        }

    }

    @Override
    public void onGoodsBought(int goodsId) {
        try {
            unlockPayTasks();
        } catch (Exception e) {
            WechatLog.error("{} PlayerGrowthFundUnit onGoodsBought error. {}", player(), goodsId, e);
        }
    }
}



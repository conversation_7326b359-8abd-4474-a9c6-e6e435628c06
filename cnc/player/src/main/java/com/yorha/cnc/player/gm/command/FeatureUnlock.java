package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 功能全解锁
 *
 * <AUTHOR>
 */
public class FeatureUnlock implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        for (CommonEnum.ModuleEnum moduleEnum : CommonEnum.ModuleEnum.values()) {
            actor.getOrLoadEntity().getFeatureLockComponent().unLockModule(moduleEnum);
        }
    }

    @Override
    public String showHelp() {
        return "FeatureUnlock";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

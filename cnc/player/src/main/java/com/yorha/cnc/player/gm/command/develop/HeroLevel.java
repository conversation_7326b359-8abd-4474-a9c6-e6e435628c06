package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerHeroComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.HeroAddLevelReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.proto.CommonEnum;
import res.template.HeroRhTemplate;

import java.util.Map;

/**
 * 英雄等级 通过Gm指令来增加当前等级+1
 * HeroLevel type=1411(英雄id)
 * HeroLevel type=0
 * <AUTHOR> Jiang
 */

public class HeroLevel implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity playerEntity = actor.getEntity();
        String type = args.get("type");
        int heroId = Integer.parseInt(type);

        if (heroId == 0) {
            for (PlayerHeroProp heroProp : playerEntity.getProp().getPlayerHeroModel().getPlayerHeroMap().values()) {
                updateLevel(playerEntity, heroProp);
            }
        } else {
            HeroRhTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroId);
            if (valueFromMap == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
            PlayerHeroProp heroProp = playerEntity.getProp().getPlayerHeroModel().getPlayerHeroMap().get(heroId);
            if (heroProp != null) {
                updateLevel(playerEntity, heroProp);
            }
        }
    }

    public static void updateLevel(PlayerEntity playerEntity, PlayerHeroProp heroProp) {
        PlayerHeroComponent heroComponent = playerEntity.getHeroComponent();
        heroComponent.execAddHeroLevel(heroProp, HeroAddLevelReason.GM_ADD_EXP);
    }


    @Override
    public String showHelp() {
        return "HeroLevel";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_HERO_PLANE_SOLDIER;
    }
}

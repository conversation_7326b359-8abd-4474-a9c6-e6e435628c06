package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.ChapterTaskFinishEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskChapterTemplate;
import res.template.TaskPoolTemplate;

import java.util.*;
import java.util.stream.Collectors;

public class ChapterTaskHandler extends AbstractTaskHandler {

    public ChapterTaskHandler(PlayerEntity entity, CommonEnum.TaskClass tcChapter) {
        super(entity, tcChapter.name());
    }

    @Override
    public void loadAllTask() {
        int chapter = getEntity().getProp().getTaskSystem().getChapter();
        int mainTaskId = ResHolder.getResService(TaskTemplateService.class).getChapterMainTask(chapter);

        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            addTaskEventMonitor(value);
            if (value.getId() == mainTaskId) {
                getTaskProp().get(value.getId()).setIsMainTask(true);
            }
        }
        //补领章节任务或者进入下一章节
        takeChapterTask();
    }

    private void takeChapterTask() {
        // 检测任务解锁
        int chapter = getEntity().getProp().getTaskSystem().getChapter();
        //获取当前章节任务列表
        Set<Integer> curTasks = ResHolder.getResService(TaskTemplateService.class).getChapterTasks(chapter);
        boolean finished = true;

        if (curTasks != null) {
            //检测章节是否有新增加的任务
            Set<Integer> ids = new HashSet<>();
            for (Integer configId : curTasks) {
                if (!getTaskProp().containsKey(configId)) {
                    ids.add(configId);
                }
            }
            if (!ids.isEmpty()) {
                addBatchTask(ids);
                finished = false;
            } else {
                //检测章节是否已经全部领取完奖励
                for (Integer configId : curTasks) {
                    TaskInfoProp taskProp = getTaskProp().get(configId);
                    if (!isFinishState(taskProp)) {
                        finished = false;
                        break;
                    }
                }
            }
        }

        //前一章节所有任务奖励都已领取完毕
        if (finished) {
            //章节任务都完成后，清理所有数据
            getTaskProp().clear();
            //如果当前章节有数据，则进入下一章
            boolean canGotoNext = (curTasks != null && !curTasks.isEmpty()) || chapter <= 0;
            if (canGotoNext) {
                int nextChapter = chapter + 1;
                Set<Integer> newTasks = ResHolder.getResService(TaskTemplateService.class).getChapterTasks(nextChapter);
                if (newTasks != null && !newTasks.isEmpty()) {
                    //加入下一章节的任务
                    addBatchTask(newTasks);
                    //设置章节主任务
                    int mainTaskId = ResHolder.getResService(TaskTemplateService.class).getChapterMainTask(nextChapter);
                    TaskInfoProp prop = getTaskProp().get(mainTaskId);
                    if (prop != null) {
                        prop.setIsMainTask(true);
                    }
                }
                //无论任务是否建立完成，都进入下一章。有可能当前是最后一章，该情况下需要将章节+1.但要阻止它反复+1，所以只能是当前章节有任务的情况下才能+1
                getEntity().getProp().getTaskSystem().setChapter(nextChapter);
            }
        }
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> configIdList) {
        Int32TaskInfoMapProp taskChapter = getTaskProp();
        int chapter = getEntity().getProp().getTaskSystem().getChapter();
        int mainTaskId = ResHolder.getResService(TaskTemplateService.class).getChapterMainTask(chapter);
        //如果领取的任务包括章节任务，则将所有章节任务的奖励一起领取
        for (Integer configId : configIdList) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(taskChapter, configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }

            //如果是章节任务，则遍历所有未领取奖励的任务，将他们加入List
            if (mainTaskId == configId) {
                for (TaskInfoProp prop : taskChapter.values()) {
                    if (!isUnCompletedState(prop)) {
                        if (configIdList.stream().noneMatch(it -> it == prop.getId())) {
                            configIdList.add(prop.getId());
                        }
                    }
                }
                break;
            }
        }

        List<IntPairType> rewardList = new ArrayList<>();
        for (Integer configId : configIdList) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(taskChapter, configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }
            setNextTaskState(taskInfoProp);

            // 领奖
            TaskChapterTemplate taskTemplate = ResHolder.getTemplate(TaskChapterTemplate.class, configId);
            List<IntPairType> reward = taskTemplate.getRewardPairList();
            AssetPackage assetPackage = AssetPackage.builder().plusItems(reward).build();
            getEntity().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_TASK, formationSubReason(configId));

            rewardList.addAll(reward);
            takeRewardQLog(taskInfoProp);
        }

        //尝试进入下一章节
        takeChapterTask();
        return rewardList;
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return getEntity().getProp().getTaskSystem().getTaskChapter();
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum gmType) {
        super.onTaskFinish(configId, gmType);
        // 章节任务完成
        new ChapterTaskFinishEvent(getEntity(), configId).dispatch();
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(TaskChapterTemplate.class, configId).getTaskId();
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    @Override
    public void fullMemoryByGm() {
        Set<Integer> collect = ResHolder.getInstance().getListFromMap(TaskChapterTemplate.class).stream().map(TaskChapterTemplate::getId).collect(Collectors.toSet());
        addBatchTask(collect);
    }

    @Override
    public void completeTasksWithGm(int chapterId) {
        int chapter = getEntity().getProp().getTaskSystem().getChapter();
        while (chapterId <= chapter) {
            for (Map.Entry<Integer, TaskInfoProp> task : getTaskProp().entrySet()) {
                if (isUnCompleted(task.getValue())) {
                    super.completeTasksWithGm(task.getKey());
                }
            }
            checkOrTakeReward(new ArrayList<>(getTaskProp().keySet()));

            takeChapterTask();

            int curChapter = getEntity().getProp().getTaskSystem().getChapter();
            if (curChapter == chapter) {
                break;
            } else {
                chapter = curChapter;
            }
        }
    }

}

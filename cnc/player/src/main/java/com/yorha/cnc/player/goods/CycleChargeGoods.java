package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.unit.PlayerCycleChargeGoodsUnit;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ActNormalGoodsExtInfoProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

public class CycleChargeGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(CycleChargeGoods.class);

    @Override
    public void checkApply(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        PlayerPayment.ActivityNormalGoodsParam goodsParam = msg.getOrderParam().getActGoodsParam();
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, goodsParam.getActId(), goodsParam.getUnitId());
        unit.checkApply(goodsTemplate);
    }

    @Override
    public void fillOrder(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        final int actId = msg.getOrderParam().getActGoodsParam().getActId();
        final int unitId = msg.getOrderParam().getActGoodsParam().getUnitId();
        orderProp.getExtInfo()
                .getActNormal()
                .setActId(actId)
                .setUnitId(unitId);
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, actId, unitId);

        unit.recordOrderToken(orderProp.getToken());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        // 遵照之前的逻辑，这里直接允许发货
    }

    @Override
    public boolean needRecordGoodsHistory(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        boolean needRecord = true;
        try {
            ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
            PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnitOrNull(player, extInfo.getActId(), extInfo.getUnitId());
            if (unit == null) {
                // unit过期了，找不到了，就别记了
                LOGGER.warn("CycleChargeGoods needRecordGoodsHistory unit not found maybe expired extInfo={}", extInfo);
                return false;
            }
            if (!unit.isTokenSame(goodsOrder.getToken())) {
                // 订单不同，unit已经变化了，本次购买不应该记录次数
                needRecord = false;
                LOGGER.warn("CycleChargeGoods afterBaseDeliver, player {} cycle charge goods order token not same, order token {}, unit token {}",
                        player, goodsOrder.getToken(), unit.getCurOrderToken());
            }
            unit.clearOrderToken();
        } catch (Exception e) {
            LOGGER.error("CycleChargeGoods needRecordGoodsHistory, player {} cycle charge goods order token check failed", player, e);
        }
        return needRecord;
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnitOrNull(player, extInfo.getActId(), extInfo.getUnitId());
        if (unit == null) {
            // unit过期了，找不到了，就别记了
            LOGGER.warn("CycleChargeGoods afterBaseDeliver unit not found maybe expired extInfo={}", extInfo);
            return Collections.emptyList();
        }
        unit.onGoodsBought(goodsOrder.getGoodsId());
        return Collections.emptyList();
    }

    private PlayerCycleChargeGoodsUnit findCycleChargeGoodsUnit(PlayerEntity player, int actId, int unitId) throws GeminiException {
        BasePlayerActivityUnit unit = player.getActivityComponent().findActiveUnit(actId, unitId);
        if (!(unit instanceof PlayerCycleChargeGoodsUnit)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return (PlayerCycleChargeGoodsUnit) unit;
    }

    private PlayerCycleChargeGoodsUnit findCycleChargeGoodsUnitOrNull(PlayerEntity player, int actId, int unitId) {
        BasePlayerActivityUnit unit = player.getActivityComponent().findActiveUnit(actId, unitId);
        if (!(unit instanceof PlayerCycleChargeGoodsUnit)) {
            return null;
        }
        return (PlayerCycleChargeGoodsUnit) unit;
    }
}

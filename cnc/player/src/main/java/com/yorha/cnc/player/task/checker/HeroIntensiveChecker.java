package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerHeroIntensiveEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 觉醒任意英雄X次
 *
 * <AUTHOR>
 */
public class HeroIntensiveChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(HeroIntensiveChecker.class);

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerHeroIntensiveEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configNum = taskParams.get(0);

        if (taskTemplate.getTaskCalculationMethod() != CommonEnum.TaskCalcType.TCT_RECEIVE) {
            LOGGER.error("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE));
            return prop.getProcess() >= configNum;
        }

        if (event instanceof PlayerHeroIntensiveEvent) {
            prop.setProcess(Math.min(prop.getProcess() + 1, configNum));
        }
        return prop.getProcess() >= configNum;
    }
}

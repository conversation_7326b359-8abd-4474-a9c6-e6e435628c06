package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerSkynetFinishEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;


/**
 * 完成X次天网任务
 * param1: X次
 *
 * <AUTHOR>
 */
public class SkynetFinishChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerSkynetFinishEvent.class),
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        final int count = taskParams.getFirst();

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE: {
                if (event instanceof PlayerSkynetFinishEvent) {
                    PlayerSkynetFinishEvent e = (PlayerSkynetFinishEvent) event;
                    if (e.getCount() > 0) {
                        prop.setProcess(Math.min(count, prop.getProcess() + e.getCount()));
                    }
                }
                break;
            }
            case TCT_CREATE: {
                int times = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(StatisticEnum.SKYNET_FINISH);
                prop.setProcess(Math.min(times, count));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= count;
    }
}
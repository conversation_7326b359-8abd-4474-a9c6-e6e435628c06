package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.UnlockUnitEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

public class UnlockUnitChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(UnlockUnitEvent.class),
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int times = 1;
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity player = event.getPlayer();
        final int unitId = taskParams.getFirst();

        if (player.getCityCommandComponent().isUnitUnlock(unitId)) {
            prop.setProcess(times);
        }
        return prop.getProcess() >= times;
    }
}
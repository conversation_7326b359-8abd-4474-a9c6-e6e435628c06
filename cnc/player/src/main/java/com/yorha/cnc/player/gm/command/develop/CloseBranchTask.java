package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 开启支线任务
 *
 * <AUTHOR>
 */
public class CloseBranchTask implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        actor.getOrLoadEntity().getTaskComponent().closeBranchTaskByGm();
    }

    @Override
    public String showHelp() {
        return "CloseBranchTask";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }

}

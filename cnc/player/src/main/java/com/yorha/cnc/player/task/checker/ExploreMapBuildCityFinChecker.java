package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.component.PlayerStatisticComponent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.ExploreMapBuildFinEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.proto.CommonEnum.MapBuildingType.*;

/**
 * 探索一次任意等级的城市
 *
 * <AUTHOR>
 */
public class ExploreMapBuildCityFinChecker extends AbstractTask<PERSON>hecker {

    public static List<String> attentionList = Lists.newArrayList(ExploreMapBuildFinEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int count = taskTemplate.getTypeValueList().get(0);
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                List<Integer> mapBuildingTypeList = Lists.newArrayList(MBT_CITY_ONT_VALUE, MBT_CITY_TWO_VALUE, MBT_CITY_THREE_VALUE, MBT_CITY_FOUR_VALUE);
                int exploreCityTotal = 0;
                PlayerStatisticComponent playerStatisticComponent = event.getPlayer().getStatisticComponent();
                for (Integer mapBuildingType : mapBuildingTypeList) {
                    exploreCityTotal += playerStatisticComponent.getSecondStatistic(StatisticEnum.EXPLORE_WORLD_BUILDING_NUM_END, mapBuildingType);
                }
                prop.setProcess(Math.min(exploreCityTotal, count));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= count;
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.GmHelper;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.resource.ResHolder;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsName;
import com.yorha.proto.SsRank;
import org.apache.commons.collections4.CollectionUtils;
import res.template.ConstTemplate;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryPlayer implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String name = args.get("name");
        // 空的 返回本服排行榜前100名
        if (StringUtils.isEmpty(name)) {
            int rankId = RankConstant.ZONE_PLAYER_POWER_RANK;
            SsRank.GetRankInfoByLimitAsk.Builder rankQuery = SsRank.GetRankInfoByLimitAsk.newBuilder().setRankId(rankId);
            rankQuery.getRangeBuilder().setRangeStart(0).setRangeEnd(99);
            try {
                SsRank.GetRankInfoByLimitAns res = actor.callZoneRank(actor.getZoneId(), rankQuery.build());
                List<Long> player = new ArrayList<>();
                for (CommonMsg.MemberAllDto member : res.getMembersList()) {
                    player.add(member.getMemberId());
                }
                onSearchAndQueryCard(actor, name, player);
            } catch (Exception e) {
                onSearchAndQueryCard(actor, name, null);
            }
            return;
        }
        ConstTemplate consts = ResHolder.getConsts(ConstTemplate.class);
        int utf8Length = StringUtils.getUTF8Length(name);
        if (utf8Length > consts.getSearchCommanderNameMax() || utf8Length < consts.getSearchCommanderNameMin()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int limit = consts.getSearchCommanderNum();
        long nameOwner = NameHelper.getNameOwner(actor, CommonEnum.NameType.PLAYER_NAME, name);
        SsName.SearchPlayerNameAsk query = SsName.SearchPlayerNameAsk.newBuilder().setName(name).setNumLimit(limit).build();
        try {
            SsName.SearchPlayerNameAns ans = actor.callName(actor.getZoneId(), query);
            LinkedList<Long> queryPlayer = new LinkedList<>(ans.getPlayerListList());
            if (nameOwner != 0) {
                queryPlayer.remove(nameOwner);
                queryPlayer.addFirst(nameOwner);
            }
            while (queryPlayer.size() > limit) {
                queryPlayer.removeLast();
            }
            onSearchAndQueryCard(actor, name, queryPlayer);
        } catch (Exception e) {
            onSearchAndQueryCard(actor, name, null);
        }
//        SsName.SearchPlayerNameAsk query = SsName.SearchPlayerNameAsk.newBuilder().setName(name).setNumLimit(100).build();
//        try {
//            int nodeNum = HashRefUtils.getNodeNum(ActorRole.Zone.name());
//            List<IActorRef> refList = new ArrayList<>();
//            for (int zoneId = 1; zoneId <= nodeNum; zoneId++) {
//                refList.add(RefFactory.ofName(zoneId));
//            }
//            List<Long> players = new ArrayList<>();
//            actor.<SsName.SearchPlayerNameAns>batchAsk(refList, query,
//                    (res) -> players.addAll(res.getPlayerListList()),
//                    () -> onSearchAndQueryCard(actor, name, players));
//        } catch (Exception e) {
//            onSearchAndQueryCard(actor, name, null);
//        }
    }

    private void onSearchAndQueryCard(PlayerActor actor, String name, List<Long> players) {
        if (CollectionUtils.isEmpty(players)) {
            GmHelper.sendGmNtfMail(actor.getZoneId(), actor.getPlayerId(), "QueryPlayer: " + name, "无");
            return;
        }
        CardHelper.batchQueryPlayerCardWithClan(actor, players,
                (map) -> {
                    List<String> out = new ArrayList<>();
                    for (Long playerId : players) {
                        if (!map.containsKey(playerId)) {
                            continue;
                        }
                        out.add(playerId + ": " + map.get(playerId).getCardHead().getName());
                    }
                    GmHelper.sendGmNtfMail(actor.getZoneId(), actor.getPlayerId(), "QueryPlayer: " + name, String.join("\n", out));
                });
    }

    @Override
    public String showHelp() {
        return "QueryPlayer name={value}";
    }
}


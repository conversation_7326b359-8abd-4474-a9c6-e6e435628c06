package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.PlayerUseTransporterEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.AIR_FORCES_USED_TIMES;

/**
 * 使用运输机
 * param1: 次数
 *
 * <AUTHOR>
 */
public class UseTransportChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerUseTransporterEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer times = taskParams.getFirst();

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int useAccelerateItemTotal = (int) entity.getStatisticComponent().getSingleStatistic(AIR_FORCES_USED_TIMES);
                prop.setProcess(Math.min(useAccelerateItemTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerUseTransporterEvent) {
                    prop.setProcess(Math.min(times, prop.getProcess() + 1));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

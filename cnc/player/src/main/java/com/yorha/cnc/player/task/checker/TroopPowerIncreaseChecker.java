package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 军队战力提升
 * param1: 战力值
 *
 * <AUTHOR>
 */
public class TroopPowerIncreaseChecker extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int powerConfig = taskParams.get(0);

        if (event instanceof PlayerPowerIncreaseEvent) {
            PlayerPowerIncreaseEvent powerIncreaseEvent = (PlayerPowerIncreaseEvent) event;
            if (powerIncreaseEvent.getPowerReason() != CommonEnum.PowerType.PT_SOLDIER) {
                return returnProcess(prop, powerConfig);
            }
            // 治疗的不计算战力提升
            if (TroopHelper.isUpdatePowerByRecover(powerIncreaseEvent.getSoldierNumChangeReason())) {
                return returnProcess(prop, powerConfig);
            }
            long l = prop.getProcess() + powerIncreaseEvent.getPower();
            prop.setProcess(Math.min(powerConfig, (int) l));
        }
        return returnProcess(prop, powerConfig);
    }

    private boolean returnProcess(TaskInfoProp prop, int powerConfig) {
        return prop.getProcess() >= powerConfig;
    }
}

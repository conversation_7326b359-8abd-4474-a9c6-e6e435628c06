package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ConstTemplate;
import res.template.ItemTemplate;

/**
 * 暴兵道具
 *
 * <AUTHOR>
 */
public class UpTrainItem extends AbstractUsableItem {
    public UpTrainItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        // 不知道应该怎么防止在配置错误的情况下，玩家使用了这个道具
        int upTrainSize = playerEntity.getSoldierComponent().getUpTrainsQueueSize();
        int expansionItemLimit = ResHolder.getConsts(ConstTemplate.class).getExpansionItemLimit();
        if ((upTrainSize + num) > expansionItemLimit) {
            throw new GeminiException(ErrorCode.UPTRAIN_LIMIT);
        }
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        playerEntity.getSoldierComponent().addUpTrainItem(getTemplate().getEffectValue(), num);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {

    }
}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBundlePurchase;
import res.template.ChargeBaseTemplate;
import res.template.ChargeSdkTemplate;

import static com.yorha.proto.PlayerPayment.*;

@Controller(module = CommonEnum.ModuleEnum.ME_USER)
public class PlayerPaymentController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPaymentController.class);

    @CommandMapping(code = MsgType.PLAYER_PAYMENTOVER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_PaymentOver_C2S msg) {
        playerEntity.getPaymentComponent().syncPull();
        try {
            ChargeBaseTemplate chargeBaseTemplate = ResHolder.getTemplate(ChargeBaseTemplate.class, msg.getChargeBaseId());
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, chargeBaseTemplate.getChargeSdkId());

            sendChargeBaseMail(playerEntity, msg.getChargeBaseId(), chargeBaseTemplate, msg.getIfDouble() == 1);

            QlogCncBundlePurchase.init(playerEntity.getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("purchase_gold")
                    .setBundleId(msg.getChargeBaseId())
                    .setPrice(sdkTemplate.getPrice())
                    .setLimitedCount(-1)
                    .setRemainLimitedCount(-1)
                    .setIfDouble(msg.getIfDouble())
                    .setIfUseToken(0)
                    .setItemGet("")
                    .setActionNote("")
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("send QlogCncBundlePurchase error:", e);
        }
        return Player_PaymentOver_S2C.getDefaultInstance();
    }

    private void sendChargeBaseMail(PlayerEntity player, int chargeBaseId, ChargeBaseTemplate template, boolean ifDouble) {
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(template.getTriggerMailId())
                .setTitle(StructMail.MailShowTitle.newBuilder()
                        .setSubTitleData(Struct.DisplayData.newBuilder().setParams(Struct.DisplayParamList.newBuilder().addDatas(Struct.DisplayParam.newBuilder()
                                .setType(CommonEnum.DisplayParamType.DPT_CHARGE_BASE_ID)
                                .setNumber(chargeBaseId)
                                .build()).build()).build()).build()
                )
                .setContent(StructMail.MailContent.newBuilder()
                        .setChargeBaseData(StructMail.MailChargeBaseData.newBuilder()
                                .setChargeBaseId(chargeBaseId)
                                .setIfDouble(ifDouble)
                                .build()).build()
                );
        LOGGER.info("{} payment send charge base mail: {}", player, chargeBaseId);
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player.getPlayerId())
                .setZoneId(player.getZoneId())
                .build();
        player.getMailComponent().sendPersonalMail(receiver, params.build());
    }

    @CommandMapping(code = MsgType.PLAYER_APPLYGOODSORDER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ApplyGoodsOrder_C2S msg) {
        return playerEntity.getPaymentComponent().handleApplyGoodsOrder(msg);
    }

    @CommandMapping(code = MsgType.PLAYER_FAKERECHARGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FakeRecharge_C2S msg) {
        return playerEntity.getPaymentComponent().handleFakeRecharge(msg);
    }

    @CommandMapping(code = MsgType.PLAYER_TRIGGERBUNDLESHOW_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_TriggerBundleShow_C2S msg) {
        playerEntity.getTriggerBundleComponent().handleShow(msg);
        return Player_TriggerBundleShow_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_JUDGERECHARGEPACKAGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_JudgeRecharge_C2S msg) {
        return playerEntity.getAntiAddictionComponent().judgePay(msg.getChargeSdkId());
    }
}

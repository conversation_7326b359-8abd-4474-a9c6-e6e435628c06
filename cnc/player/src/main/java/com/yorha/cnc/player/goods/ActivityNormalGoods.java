package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ActNormalGoodsExtInfoProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.jetbrains.annotations.NotNull;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

/**
 * 随活动投放的常规礼包，游戏中大部分礼包应该都是这个类型
 * <p>
 * 会随活动的开放而开放，处理常规的礼包链开放逻辑
 */
public class ActivityNormalGoods implements Goods {

    public interface ActUnitGoodsHandler {
        void checkApply(ChargeGoodsTemplate goodsTemplate);

        void onGoodsBought(int goodsId);
    }

    @Override
    public void checkApply(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        PlayerPayment.ActivityNormalGoodsParam goodsParam = msg.getOrderParam().getActGoodsParam();
        final int actId = goodsParam.getActId();
        final int unitId = goodsParam.getUnitId();
        ActUnitGoodsHandler actUnitGoodsHandler = findUnit(player, actId, unitId);
        actUnitGoodsHandler.checkApply(goodsTemplate);
    }

    @NotNull
    protected static ActUnitGoodsHandler findUnit(PlayerEntity player, int actId, int unitId) {
        BasePlayerActivityUnit unit = player.getActivityComponent().findActiveUnit(actId, unitId);
        if (!(unit instanceof ActUnitGoodsHandler)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return (ActUnitGoodsHandler) unit;
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        orderProp.getExtInfo()
                .getActNormal()
                .setActId(msg.getOrderParam().getActGoodsParam().getActId())
                .setUnitId(msg.getOrderParam().getActGoodsParam().getUnitId());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        // 这里应该可以不校验了，给他买
        // 一些特殊情况包括：
        // order生成了，但是付款回调回来的时候活动过期了
        // order是很早之前提前生成的，付款回调是很久以后的事情了，给他买也问题不大的（现在order上虽然有时间戳，但是没有过期逻辑）

        // 用老司机的话来说，人家要充钱，还能不给充？限购次数可以保证平衡性就好了
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
        ActUnitGoodsHandler actUnitGoodsHandler = findUnit(player, extInfo.getActId(), extInfo.getUnitId());
        actUnitGoodsHandler.onGoodsBought(goodsOrder.getGoodsId());
        return Collections.emptyList();
    }
}

package com.yorha.cnc.player.item.use;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.ItemTemplate;

/**
 * <AUTHOR>
 */
public abstract class AbstractUsableItem {

    protected final int num;
    protected final int itemTemplateId;

    public AbstractUsableItem(int num, ItemTemplate itemTemplate) {
        this.num = num;
        this.itemTemplateId = itemTemplate.getId();
    }

    public CommonEnum.Reason getItemReason(PlayerEntity playerEntity) {
        return CommonEnum.Reason.ICR_NONE;
    }

    public abstract void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params);

    /**
     * return false的时候会返还道具的，所以千万注意小心返回值
     */
    public abstract boolean use(PlayerEntity playerEntity, ItemUseParamsProp params);

    public abstract void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response);

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SIMPLE_STYLE).append(num).toString();
    }

    public ItemTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(ItemTemplate.class, itemTemplateId);
    }
}


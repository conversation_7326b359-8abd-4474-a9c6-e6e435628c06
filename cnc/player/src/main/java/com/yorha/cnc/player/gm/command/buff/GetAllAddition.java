package com.yorha.cnc.player.gm.command.buff;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.helper.GmHelper;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class GetAllAddition implements PlayerGmCommand {
    public static final Logger LOGGER = LogManager.getLogger(GetAllAddition.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String key = "GetAllAddition";
        int zoneId = actor.getZoneId();
        SsScenePlayer.GetSceneAdditionSysAsk.Builder ask = SsScenePlayer.GetSceneAdditionSysAsk.newBuilder().setPlayerId(playerId).setIsGm(true);
        SsScenePlayer.GetSceneAdditionSysAns ans = actor.callCurScene(ask.build());

        AdditionSysProp additionSysProp = new AdditionSysProp();
        Struct.AdditionSys scenePlayerAll = ans.getAdditionSys();
        AdditionSysProp playerAll = actor.getEntity().getAddComponent().getAllAdditionCopy(actor.getEntity().getProp().getAdditionSysNew());
        AdditionSysProp zoneAll = actor.getEntity().getAddComponent().getAllAdditionCopy(actor.getEntity().getProp().getZoneAdditionSys());

        additionSysProp.mergeFromSs(scenePlayerAll);
        AdditionUtil.mergeAdditionSys(additionSysProp, playerAll);
        AdditionUtil.mergeAdditionSys(additionSysProp, zoneAll);

        GmHelper.sendGmNtfMail(zoneId, playerId, key, additionSysProp.toString());

        LOGGER.info("{} SceneAddition:{}", actor.getEntity(), scenePlayerAll);
        LOGGER.info("{} PlayerAddition:{}", actor.getEntity(), playerAll);
        LOGGER.info("{} ZoneAddition:{}", actor.getEntity(), zoneAll);
        LOGGER.info("{} TotalAddition:{}", actor.getEntity(), additionSysProp);
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

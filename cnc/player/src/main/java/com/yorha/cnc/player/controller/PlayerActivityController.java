package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.unit.*;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.component.PlayerActivityComponent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerActivity.*;
import com.yorha.proto.SsClanActivity;
import com.yorha.proto.SsSceneActivitySchedule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;

@Controller(module = CommonEnum.ModuleEnum.ME_USER)
public class PlayerActivityController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityController.class);

    /**
     * 领取活动奖励
     */
    @CommandMapping(code = MsgType.PLAYER_ACTIVITYTAKEREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_C2S msg) {
        return playerEntity.getActivityComponent().handleTakeReward(msg);
    }

    @CommandMapping(code = MsgType.PLAYER_ACTIVITYBUYGOOD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_ActivityBuyGood_C2S msg) {
        playerEntity.getActivityComponent().handleBuyActivityGood(msg.getActivityId(), msg.getUnitId(), msg.getGoodId());
        return com.yorha.proto.PlayerActivity.Player_ActivityBuyGood_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_ACTIVITYBUYSECONDQUEUE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_ActivityBuySecondQueue_C2S msg) {
        playerEntity.getActivityComponent().handleBuySecondQueue(msg.getActId(), msg.getUnitId());
        return com.yorha.proto.PlayerActivity.Player_ActivityBuySecondQueue_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SENDACTIVITYINVITEMSG_C2S)
    public void handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_InviteJoinActivity_C2S msg, int seqId) {
        CommonEnum.MessageType messageType = CommonEnum.MessageType.MT_ACTIVITY_INVITE;
        CommonMsg.MessageData.Builder messageData = CommonMsg.MessageData.newBuilder();
        CommonMsg.ActivityInvite.Builder invite = CommonMsg.ActivityInvite.newBuilder();
        invite.setActivityId(msg.getActivityId());
        messageData.setActivityInvite(invite.build());
        ChatPlayerEntity chatPlayerEntity = playerEntity.ownerActor().getOrLoadChatPlayerEntity();
        IActorRef session = playerEntity.ownerActor().sender();
        chatPlayerEntity.getHandleChatComponent().chatRequest(msg.getChatSession(), messageType, messageData.build(),
                (e) -> playerEntity.answerMsgToClient(session, seqId,
                        MsgType.PLAYER_SENDACTIVITYINVITEMSG_S2C,
                        e,
                        com.yorha.proto.PlayerActivity.Player_InviteJoinActivity_S2C.getDefaultInstance()
                ));
    }

    @CommandMapping(code = MsgType.PLAYER_BESTCOMMANDERFETCH_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_BestCommanderFetch_C2S msg) {
        PlayerActivity activity = playerEntity.getActivityComponent().findActivity(msg.getActId());
        if (activity == null) {
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        PlayerBestCommanderUnit unit = activity.findFirstUnitInTopFatherActOf(PlayerBestCommanderUnit.class);
        if (unit == null) {
            throw new GeminiException(ErrorCode.ACTIVITY_UNIT_NOT_EXIST);
        }
        unit.handleFetch();
        return com.yorha.proto.PlayerActivity.Player_BestCommanderFetch_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_BESTCOMMANDERHISTORYRANK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_BestCommanderHistoryRank_C2S msg) {
        SsSceneActivitySchedule.BestCommanderHistoryRankAsk ask = SsSceneActivitySchedule.BestCommanderHistoryRankAsk.newBuilder()
                .setWant(msg.getWant())
                .setStartAge(msg.getStartAge())
                .build();
        SsSceneActivitySchedule.BestCommanderHistoryRankAns ans = playerEntity.ownerActor().callSelfBigScene(ask);
        return com.yorha.proto.PlayerActivity.Player_BestCommanderHistoryRank_S2C.newBuilder()
                .addAllHistoryRank(ans.getHistoryRankList())
                .build();
    }

    @CommandMapping(code = MsgType.PLAYER_REPORTSTORERATINGRESULT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_StoreRating_C2S msg) {
        PlayerStoreRatingUnit unit = playerEntity.getActivityComponent().findStoreRatingUnit(msg.getActId());
        unit.onReceiveReport(msg);
        return Player_StoreRating_S2C.getDefaultInstance();
    }
    
    @CommandMapping(code = MsgType.PLAYER_BUYBP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_BuyBp_C2S msg) {
        LOGGER.info("PlayerActivityController buy bp {} {} {}", msg.getActivityId(), msg.getUnitId(), msg.getBpId());
        final PlayerActor playerActor = playerEntity.ownerActor();
        final PlayerActivityComponent activityComponent = playerActor.getOrLoadEntity().getActivityComponent();
        final PlayerFestivalBpUnit unit = activityComponent.checkedGetUnit(PlayerFestivalBpUnit.class, msg.getActivityId(), msg.getUnitId());
        unit.tryOpenBp(msg.getBpId(), "gold buy");
        return Player_BuyBp_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_BPTICKETEXCHANGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_BpTicketExchange_C2S msg) {
        LOGGER.info("PlayerActivityController exchange bp ticket {} {} {}", msg.getActivityId(), msg.getUnitId(), msg.getNum());
        final PlayerActor playerActor = playerEntity.ownerActor();
        final PlayerActivityComponent activityComponent = playerActor.getOrLoadEntity().getActivityComponent();
        final PlayerFestivalBpUnit unit = activityComponent.checkedGetUnit(PlayerFestivalBpUnit.class, msg.getActivityId(), msg.getUnitId());
        final Player_BpTicketExchange_S2C.Builder builder = Player_BpTicketExchange_S2C.newBuilder();
        final int score = unit.exchangeTicket(msg.getNum());
        builder.setScore(score);
        return builder.build();
    }

    @CommandMapping(code = MsgType.PLAYER_FETCHACTIVITYCALENDAR_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_FetchActivityCalendar_C2S msg) {
        Set<ActivityResService.ActivityCalendar> activityCalendar = playerEntity.getActivityComponent().getActivityCalendars(SystemClock.nowInstant());
        Player_FetchActivityCalendar_S2C.Builder builder = Player_FetchActivityCalendar_S2C.newBuilder();
        for (ActivityResService.ActivityCalendar calendar : activityCalendar) {
            CommonMsg.ActivityCalendar.Builder calendarBuilder = CommonMsg.ActivityCalendar.newBuilder()
                    .setActId(calendar.cell.actId)
                    .setStartTsSec(calendar.cell.startTime.getEpochSecond())
                    .setEndTsSec(calendar.cell.expireTime.getEpochSecond());
            builder.addCalendar(calendarBuilder.build());
        }
        return builder.build();
    }

    @CommandMapping(code = MsgType.PLAYER_CHOOSEBESTCOMMANDERITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_ChooseBestCommanderItem_C2S msg) {
        final PlayerBestCommanderTotalRankUnit bestCommanderTotalRankUnit = playerEntity.getActivityComponent().findBestCommanderTotalRankUnit(msg.getActId(), msg.getUnitId());
        bestCommanderTotalRankUnit.chooseItem(msg.getItemId());
        return Player_ChooseBestCommanderItem_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_FETCHACTIVITYCLANDATA_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, com.yorha.proto.PlayerActivity.Player_FetchActivityClanData_C2S msg) {
        if (playerEntity.getClanId() == 0) {
            return Player_FetchActivityClanData_S2C.getDefaultInstance();
        }
        playerEntity.ownerActor().callCurClan(SsClanActivity.FetchActivityDataAsk.newBuilder().setType(msg.getType()).build());
        switch (msg.getType()) {
            default:
                throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
        }
    }

    /**
     * 购买翻盘活动的抽奖筹码
     */
    @CommandMapping(code = MsgType.PLAYER_BUYLOTTERYVOUCHER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_BuyLotteryVoucher_C2S msg) {
        LOGGER.info("PlayerActivityController buy lottery voucher {} {} {}", msg.getActivityId(), msg.getUnitId(), msg.getNum());
        PlayerActor playerActor = playerEntity.ownerActor();
        final PlayerActivityComponent activityComponent = playerActor.getOrLoadEntity().getActivityComponent();
        PlayerActivityLotteryUnit unit = activityComponent.checkedGetUnit(PlayerActivityLotteryUnit.class, msg.getActivityId(), msg.getUnitId());
        unit.buyVoucher(msg.getNum());
        return Player_BuyLotteryVoucher_S2C.getDefaultInstance();
    }

    /**
     * 娜塔莎转盘抽奖
     */
    @CommandMapping(code = MsgType.PLAYER_LOTTERYNATASHADRAW_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_Lottery_Natasha_Draw_C2S msg) {
        if (msg.getTimes() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "draw times wrong");
        }
        PlayerActor playerActor = playerEntity.ownerActor();
        final PlayerActivityComponent activityComponent = playerActor.getOrLoadEntity().getActivityComponent();
        PlayerLotteryNatashaUnit unit = activityComponent.checkedGetUnit(PlayerLotteryNatashaUnit.class, msg.getActivityId(), msg.getUnitId());
        AssetPackage assetPackage = unit.handleDraw(msg.getTimes());
        Player_Lottery_Natasha_Draw_S2C.Builder builder = Player_Lottery_Natasha_Draw_S2C.newBuilder()
                .setReward(assetPackage.toPb());
        return builder.build();
    }

    /**
     * 设置英雄
     */
    @CommandMapping(code = MsgType.PLAYER_SETTLELOTTERYHERO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SettleLotteryHero_C2S msg) {
        PlayerActor playerActor = playerEntity.ownerActor();
        final PlayerActivityComponent activityComponent = playerActor.getOrLoadEntity().getActivityComponent();
        PlayerActivityLotteryUnit unit = activityComponent.checkedGetUnit(PlayerActivityLotteryUnit.class, msg.getActivityId(), msg.getUnitId());
        unit.settleHero(msg.getHeroId());
        return Player_SettleLotteryHero_S2C.getDefaultInstance();
    }

    /**
     * 娜塔莎转盘兑换商店
     */
    @CommandMapping(code = MsgType.PLAYER_LOTTERYNATASHASHOPEXCHANGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_Lottery_Natasha_Shop_Exchange_C2S msg) {
        PlayerActor playerActor = playerEntity.ownerActor();
        playerActor.getOrLoadEntity().getActivityComponent().natashaShopExchange(msg.getShopId());
        return Player_Lottery_Natasha_Shop_Exchange_S2C.getDefaultInstance();
    }
}

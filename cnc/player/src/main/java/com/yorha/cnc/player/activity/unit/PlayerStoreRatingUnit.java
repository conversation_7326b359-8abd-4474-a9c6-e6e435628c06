package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerActivity.OpenStoreRatingNtf;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncShopScore;
import res.template.ActivityTriggerPoolTemplate;
import res.template.ConstActivityTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 商店评分
 *
 * <AUTHOR>
 */
public class PlayerStoreRatingUnit extends PlayerTriggerUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerStoreRatingUnit.class);

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_STORE_RATING, (owner, prop, template) ->
                new PlayerStoreRatingUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerStoreRatingUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void init() {
        super.init();
        unitProp.getStoreRatingUnit()
                .setUnratedTime(0)
                .setIsRated(false);
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }


    @Override
    public boolean isFinished() {
        int limit = ResHolder.getConsts(ConstActivityTemplate.class).getStoreRatingTriggerTime();
        return unitProp.getStoreRatingUnit().getIsRated() || unitProp.getStoreRatingUnit().getUnratedTime() > limit;
    }

    @Override
    protected void triggerSatisfiedImpl(int triggerId, PlayerEvent event) {
        if (isFinished()) {
            LOGGER.info("PlayerStoreRatingUnit onTrigger, activity finish!");
            return;
        }
        if (inCold()) {
            return;
        }
        if (ServerContext.isIosExamine()) {
            LOGGER.info("PlayerStoreRatingUnit onTrigger skip, isIosExamine");
            return;
        }
        // 通知客户端弹窗
        int triggerTime = unitProp.getStoreRatingUnit().getUnratedTime() + 1;
        LOGGER.info("PlayerStoreRating onTrigger, triggerTime={}", triggerTime);
        OpenStoreRatingNtf ntf = OpenStoreRatingNtf.newBuilder().setTriggerId(triggerId).build();
        ownerActivity.getPlayer().sendMsgToClient(MsgType.PLAYER_OPENSTORERATINGWINDOW_NTF, ntf);
        // 增加未评分计数，不再客户端上报的时候增加，因为客户端弹窗的时候可能直接杀进程，不上报，算作是未评分操作
        unitProp.getStoreRatingUnit().setUnratedTime(triggerTime)
                .setOnlineTimeMs(ownerActivity.getPlayer().getOnlineMs());
        final Map<Integer, ActivityTriggerPoolTemplate> triggerTemplateMap = ResHolder.getInstance().getMap(ActivityTriggerPoolTemplate.class);
        final ActivityTriggerPoolTemplate actTriggerTemplate = triggerTemplateMap.get(triggerId);
        QlogCncShopScore.init(player().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("Popup_Score")
                .setScore(0)
                .setNum(unitProp.getStoreRatingUnit().getUnratedTime())
                .setReason(actTriggerTemplate.getTriggerType().name())
                .setAfter_popup(0).sendToQlog();
    }


    /**
     * 是否处于弹窗冷却中
     */
    private boolean inCold() {
        ConstActivityTemplate template = ResHolder.getConsts(ConstActivityTemplate.class);
        long curOnlineTime = ownerActivity.getPlayer().getOnlineMs();
        // 判断是否在落堡禁止触发时间内
        long lastCityFallTsMs = ownerActivity.getPlayer().getProp().getRatingModel().getCityFallTsMs();
        int cityFallBanHours = template.getCityFallBanRatingInterval();
        if (TimeUnit.MILLISECONDS.toHours(curOnlineTime - lastCityFallTsMs) < (long) cityFallBanHours) {
            LOGGER.info("PlayerStoreRatingUnit inCold, in cityFallBanHours, curOnlineTime={}, lastCityFallOnlineTime={}, banHours={}",
                    curOnlineTime, lastCityFallTsMs, cityFallBanHours);
            return true;
        }
        // 判断是否在被攻击后禁止触发时间内
        long lastAttackTsMs = ownerActivity.getPlayer().getProp().getRatingModel().getBaseBeAttackTsMs();
        int attackBanHours = template.getBaseBeAttackBanRatingInterval();
        if (TimeUnit.MILLISECONDS.toHours(curOnlineTime - lastAttackTsMs) < attackBanHours) {
            LOGGER.info("PlayerStoreRatingUnit inCold, inAttackBanHours, curOnlineTime={}, lastBeAttackOnlineTime={}, banHours={}",
                    curOnlineTime, lastAttackTsMs, attackBanHours);
            return true;
        }
        // 判断是否在pvp失败禁止触发时间内
        long lastPvpFailTsMs = ownerActivity.getPlayer().getProp().getRatingModel().getPvpFailTsMs();
        int pvpBanHours = template.getPvpFailBanRatingInterval();
        if (TimeUnit.MILLISECONDS.toHours(curOnlineTime - lastPvpFailTsMs) < pvpBanHours) {
            LOGGER.info("PlayerStoreRatingUnit inCold, inBattleFailBanHours, curOnlineTime={}, lastBattleFailOnlineTime={}, banHours={}",
                    curOnlineTime, lastPvpFailTsMs, pvpBanHours);
            return true;
        }
        // 判断是否在上次弹窗之后的冷却期
        long oldOnlineTime = unitProp.getStoreRatingUnit().getOnlineTimeMs();
        int initialInterval = ResHolder.getConsts(ConstActivityTemplate.class).getInitialTriggerInterval();
        int extraInterval = ResHolder.getConsts(ConstActivityTemplate.class).getExtraTriggerInterval();
        int realInterval = initialInterval + extraInterval * unitProp.getStoreRatingUnit().getUnratedTime();
        if (TimeUnit.MILLISECONDS.toHours(curOnlineTime - oldOnlineTime) < realInterval) {
            LOGGER.info("PlayerStoreRating inCold, inCd, curOnlineTime={}, lastTriggerOnlineTime={}, intervalHours={}",
                    curOnlineTime, oldOnlineTime, realInterval);
            return true;
        }
        return false;
    }

    public void onReceiveReport(com.yorha.proto.PlayerActivity.Player_StoreRating_C2S msg) {
        final boolean finish = msg.getFinish();
        LOGGER.info("PlayerStoreRating onReceiveReport, finish={}", finish);
        if (finish) {
            // 结束活动
            unitProp.getStoreRatingUnit().setIsRated(true);
        }
        QlogCncShopScore.init(player().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("Click_Score")
                .setScore(msg.getScore())
                .setNum(unitProp.getStoreRatingUnit().getUnratedTime())
                .setReason("")
                .setAfter_popup(msg.getPopUpType()).sendToQlog();
    }
}

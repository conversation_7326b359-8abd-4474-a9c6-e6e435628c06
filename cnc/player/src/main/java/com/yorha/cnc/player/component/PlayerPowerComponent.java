package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.freqLimitCaller.FreqLimitCaller;
import com.yorha.common.power.PowerInterface;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerPowerInfoProp;
import com.yorha.game.gen.prop.PowerInfoUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncPowerFlow;

import java.util.Map;

/**
 * 处理PlayerEntity战斗力
 * <p>
 * 同步路径
 * 部队战力：scene -> player
 * 其余战力：player -> scene (只同步player侧的战力，因为scene那边player侧和scene侧的战力是分开记录的)
 *
 * <AUTHOR>
 */

public class PlayerPowerComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPowerComponent.class);

    private final Map<CommonEnum.PowerType, PowerInterface> powerInterfaceMap = Maps.newEnumMap(CommonEnum.PowerType.class);

    public PlayerPowerComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        // 注册会提供power的component
        register(getOwner().getHeroComponent());
        register(getOwner().getTechComponent());
        this.updateScheduleLimitCaller = new FreqLimitCaller(TimerReasonType.UPDATE_POWER_TO_OTHER,
                TimeUtils.second2Ms(getPowerUpdateInterval()),
                1,
                this::updatePowerToOther,
                this.ownerActor(),
                String.valueOf(this.getOwner().getEntityId()));
    }

    @Override
    public void onDestroy() {
        this.updateScheduleLimitCaller.stopTimer();
    }

    private void register(PowerInterface powerInterface) {
        powerInterfaceMap.put(powerInterface.getPowerType(), powerInterface);
    }

    /**
     * 延时同步至联盟和全服排行榜的task
     */
    private FreqLimitCaller updateScheduleLimitCaller;
    /**
     * 新号注册标签，用于优化新号战力更新频率
     */
    private boolean isNewbie;

    public void setNewbie(boolean flag) {
        isNewbie = flag;
    }

    public void updateNewbiePower() {
        if (!isNewbie) {
            return;
        }
        setNewbie(false);
        updateAllPower();
    }

    /**
     * 同步战力时间间隔
     */
    private int getPowerUpdateInterval() {
        return 4;
    }

    public PlayerPowerInfoProp getPowerInfoProp() {
        return getOwner().getProp().getPlayerPowerInfo();
    }

    /**
     * 更新所有战力
     */
    private void updateAllPower() {
        boolean powerChanged = false;
        for (CommonEnum.PowerType powerType : CommonEnum.PowerType.values()) {
            try {
                PowerInterface componentOrNull = getComponentOrNull(powerType);
                if (componentOrNull == null) {
                    continue;
                }
                long newPower = componentOrNull.calcPower();
                if (newPower < 0) {
                    LOGGER.error("player:{} calc power type:{} illegal.", getOwner(), powerType);
                    continue;
                }
                long powerDiff = doUpdatePower(powerType, newPower, null);
                if (powerDiff != 0) {
                    powerChanged = true;
                }
            } catch (Exception e) {
                LOGGER.error("PlayerPowerComponent updatePower error, ", e);
            }
        }

        if (powerChanged) {
            onPowerChanged();
        }
    }

    /**
     * 更新战力
     */
    public void updatePower(CommonEnum.PowerType powerType) {
        if (isNewbie) {
            return;
        }
        if (powerType == CommonEnum.PowerType.PT_SOLDIER) {
            // 士兵战力变更必须是scenePlayer侧来发起
            WechatLog.error("soldier power calc is unsupported in player side");
            throw new GeminiException(ErrorCode.POWER_CALC_ERROR, "soldier power calc is unsupported in player side");
        }
        try {
            long newPower = getComponent(powerType).calcPower();
            if (newPower < 0) {
                LOGGER.error("player:{} calc power type:{} illegal.", getOwner(), powerType);
                throw new GeminiException(ErrorCode.POWER_CALC_ERROR);
            }

            updatePowerWithPower(powerType, newPower, null);
        } catch (Exception e) {
            LOGGER.error("PlayerPowerComponent updatePower fail, ", e);
        }
    }

    /**
     * 更新战力
     *
     * @param newPower 指定战力
     */
    public void updatePowerWithPower(CommonEnum.PowerType powerType, long newPower, Integer soldierNumChangeReason) {
        if (isNewbie) {
            return;
        }
        try {
            if (powerType == CommonEnum.PowerType.PT_SOLDIER) {
                // 如果是士兵战力，需要把晋升的兵力也算上
                newPower += getOwner().getSoldierComponent().getLevelUpPower();
            }
            long powerDiff = doUpdatePower(powerType, newPower, soldierNumChangeReason);
            if (powerDiff != 0) {
                onPowerChanged();
            }
        } catch (Exception e) {
            LOGGER.error("PlayerPowerComponent updatePowerWithPower fail, ", e);
        }
    }

    private long doUpdatePower(CommonEnum.PowerType powerType, Long newPower, Integer soldierNumChangeReason) {
        PowerInfoUnitProp powerInfo = getPowerInfoProp().getPowerInfoV(powerType.getNumber());
        if (powerInfo == null) {
            powerInfo = getPowerInfoProp().addEmptyPowerInfo(powerType.getNumber());
        }
        long oldPower = powerInfo.getPower();
        long powerDiff = newPower - oldPower;
        if (powerDiff != 0) {
            // 更新战力
            long oldTotalPower = getPowerInfoProp().getTotalPower();
            long newTotalPower = oldTotalPower + powerDiff;
            getPowerInfoProp().setTotalPower(newTotalPower);
            powerInfo.setPower(newPower);
            LOGGER.debug("{} update power type:{} power:({})->({}) totalPower:({})->({}) powerDiff:{}", getOwner(), powerType, oldPower, newPower, oldTotalPower, newTotalPower, powerDiff);
            QlogCncPowerFlow.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setBeforeCount(oldTotalPower)
                    .setAfterCount(newTotalPower)
                    .setReason(powerType.toString())
                    .sendToQlog();
            // 通知PlayerCard更新
            getOwner().getPlayerPropComponent().updatePlayerCardCache(oldPower == 0);
        }
        if (powerDiff > 0) {
            onPowerIncreased(powerType, powerDiff, soldierNumChangeReason);
        }
        return powerDiff;
    }

    /**
     * 战力提升了
     */
    private void onPowerIncreased(CommonEnum.PowerType powerType, long powerDiff, Integer soldierNumChangeReason) {
        // 统计相关 统计最大战力
        getOwner().getDataRecordComponent().updateDataRecord(CommonEnum.DataRecordType.DRT_MAX_POWER, CommonEnum.DataRecordCalcType.DRCT_MAX, getPowerInfoProp().getTotalPower());
        new PlayerPowerIncreaseEvent(getOwner(), powerDiff, powerType, soldierNumChangeReason).dispatch();
    }

    public void onMigrateIn() {
        onPowerChanged();
    }

    /**
     * 战力变化了
     */
    private void onPowerChanged() {
        // 通知联盟、排行榜, 做延时处理，不实时同步
        int rankId = RankConstant.ZONE_PLAYER_POWER_RANK;
        if (!getOwner().getPlayerRankComponent().isNeedSyncToZoneRank(rankId)) {
            // 不需要同步到全区排行榜上
            if (!getOwner().getPlayerClanComponent().isInClan()) {
                // 并且不在军团内
                LOGGER.debug("no need to update power to clan or zone rank for {}", getOwner().getPlayerId());
                return;
            }
        }
        this.updateScheduleLimitCaller.run();
    }

    /**
     * 更新玩家的战力数据到其他actor
     */
    private void updatePowerToOther() {
        long totalPower = getPowerInfoProp().getTotalPower();
        // 延迟更新到rank或clan上，一定程度上合并
        int rankId = RankConstant.ZONE_PLAYER_POWER_RANK;
        // 有可能达不到条件，不需要同步到zone上
        if (getOwner().getPlayerRankComponent().isNeedSyncToZoneRank(rankId)) {
            getOwner().getPlayerRankComponent().updateZoneRanking(rankId, totalPower);
        }
        // 延迟启动时，玩家可能不在军团了
        if (getOwner().getPlayerClanComponent().isInClan()) {
            getOwner().getPlayerClanComponent().updateClanMemberInfo(StructClan.ClanMember.newBuilder().setComboat(totalPower).build());
        }
    }

    private PowerInterface getComponentOrNull(CommonEnum.PowerType powerType) {
        return powerInterfaceMap.get(powerType);
    }

    private PowerInterface getComponent(CommonEnum.PowerType powerType) {
        PowerInterface componentOrNull = this.getComponentOrNull(powerType);
        if (componentOrNull == null) {
            LOGGER.error("power type:{} not register", powerType);
            throw new GeminiException(ErrorCode.POWER_TYPE_NOT_EXIST);
        }
        return componentOrNull;
    }
}

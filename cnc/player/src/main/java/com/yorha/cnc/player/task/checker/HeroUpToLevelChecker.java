package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerHeroLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 指定英雄达到x级
 * param1: 英雄id
 * param2: 等级
 *
 * <AUTHOR>
 */
public class HeroUpToLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerHeroLevelUpEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int heroId = taskParams.get(0);
        int levelConfig = taskParams.get(1);
        int level = event.getPlayer().getHeroComponent().getHeroPropLevel(heroId);
        if (level > 0) {
            prop.setProcess(level);
        }
        return prop.getProcess() >= levelConfig;
    }
}

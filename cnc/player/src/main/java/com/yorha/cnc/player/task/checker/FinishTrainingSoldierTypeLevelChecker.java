package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerFinishTrainingSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.SoldierTypeTemplate;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.FINISH_TRAIN_SOLDIERID_NUM;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_RECEIVE;

public class FinishTrainingSoldierTypeLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerFinishTrainingSoldierEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int reqSoldierType = taskParams.get(0);
        int reqSoldierLevel = taskParams.get(1);
        int reqCount = taskParams.get(2);

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            long trainTotal = 0;
            if (reqSoldierType == 0) {
                trainTotal = event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(FINISH_TRAIN_SOLDIERID_NUM);
            } else {
                List<Integer> soldierIdList = ResHolder.getResService(SoldierResService.class).getIdListBySoldierType(reqSoldierType);
                for (Integer soldierId : soldierIdList) {
                    SoldierTypeTemplate t = ResHolder.getTemplate(SoldierTypeTemplate.class, soldierId);
                    if (t.getSoldierLevel() >= reqSoldierLevel) {
                        trainTotal += (int) event.getPlayer().getStatisticComponent().getSecondStatistic(FINISH_TRAIN_SOLDIERID_NUM, soldierId);
                    }
                }
            }
            prop.setProcess((int) Math.min(trainTotal, reqCount));
        } else if (taskTemplate.getTaskCalculationMethod() == TCT_RECEIVE) {
            if (event instanceof PlayerFinishTrainingSoldierEvent) {
                PlayerFinishTrainingSoldierEvent finishTrainingSoldierEvent = (PlayerFinishTrainingSoldierEvent) event;
                int soldierId = finishTrainingSoldierEvent.getSoldierId();
                SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
                int soldierLevel = soldierTemplate.getSoldierLevel();
                boolean shouldUpdateProcess = (reqSoldierType == 0 || soldierTemplate.getSoldierType() == reqSoldierType) && soldierLevel >= reqSoldierLevel;
                if (shouldUpdateProcess) {
                    prop.setProcess(Math.min(prop.getProcess() + finishTrainingSoldierEvent.getCount(), reqCount));
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= reqCount;
    }
}

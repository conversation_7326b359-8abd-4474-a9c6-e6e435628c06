package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.unit.PlayerContinuesUnit;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ActNormalGoodsExtInfoProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

/**
 * 连续活动解锁付费奖池
 *
 * <AUTHOR>
 */
public class ContinuesActPayPoolGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(ContinuesActPayPoolGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        // 检查是否已解锁
        int actId = msg.getOrderParam().getActGoodsParam().getActId();
        int unitId = msg.getOrderParam().getActGoodsParam().getUnitId();
        PlayerContinuesUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerContinuesUnit.class, actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (unit.getConfigTemplate().getPayGoodsId() != goodsTemplate.getId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (unit.isPayPoolUnlock()) {
            throw new GeminiException(ErrorCode.CONTINUE_ACT_PAY_UNLOCKED);
        }
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        orderProp.getExtInfo()
                .getActNormal()
                .setActId(msg.getOrderParam().getActGoodsParam().getActId())
                .setUnitId(msg.getOrderParam().getActGoodsParam().getUnitId());
    }

    /**
     * 发货前，不校验是否已解锁了了，倒霉蛋愿意连买两次，就解锁两次吧
     */
    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {

    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
        int actId = extInfo.getActId();
        int unitId = extInfo.getUnitId();
        PlayerContinuesUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerContinuesUnit.class, actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 解锁付费奖池
        unit.unlockPayPool();
        LOGGER.info("ContinuesActPayPoolGoods afterBaseDeliver {} {}", owner, goodsTemplate.getId());
        return Collections.emptyList();
    }
}

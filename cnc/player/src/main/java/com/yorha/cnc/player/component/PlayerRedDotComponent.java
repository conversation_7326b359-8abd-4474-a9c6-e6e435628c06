package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerRedDotModelProp;
import com.yorha.game.gen.prop.RedDotDataProp;
import com.yorha.proto.*;

import java.util.Collection;

/**
 * 红点（大多数红点由客户端通过实时数据判断是否显示，有些数据客户端无法实时获取到，所以由服务器统一管理）
 */
public class PlayerRedDotComponent extends PlayerComponent {

    public PlayerRedDotComponent(PlayerEntity owner) {
        super(owner);
    }

    private PlayerRedDotModelProp prop() {
        return getOwner().getProp().getRedDotModel();
    }

    public RedDotDataProp getOrCreateRedDot(CommonEnum.RedDotKey key) {
        RedDotDataProp v = prop().getRedDotMapV(key.getNumber());
        if (v != null) {
            return v;
        }
        return prop().addEmptyRedDotMap(key.getNumber());
    }

    public void clearRedDot(CommonEnum.RedDotKey key) {
        prop().removeRedDotMapV(key.getNumber());
    }

    public void handleCsRemoveRedDot(PlayerCommon.Player_RemoveRedDot_C2S msg) {
        final PlayerRedDotModelProp prop = prop();
        final CommonEnum.RedDotKey redDotKey = msg.getRedDotKey();
        if (msg.hasRemoveAll() && msg.getRemoveAll()) {
            prop.removeRedDotMapV(redDotKey.getNumber());
        } else {
            removeCellIds(redDotKey, msg.getRemoveCellIdsList());
        }
    }

    private void removeCellIds(CommonEnum.RedDotKey redDotKey, Collection<Long> removeCellIds) {
        CommonEnum.RedDotKey realKey = redDotKey;
        PlayerRedDotModelProp prop = prop();
        RedDotDataProp data = prop.getRedDotMapV(realKey.getNumber());
        if (data != null) {
            for (Long removeCellId : removeCellIds) {
                data.removeCellsV(removeCellId);
            }
            if (data.isCellsEmpty()) {
                prop.removeRedDotMapV(realKey.getNumber());
            }
        }
    }

    public void handleUpdateRedDotCmd(SsPlayerMisc.UpdateRedDotCmd cmd) {
        for (CommonMsg.UpdateRedDotData data : cmd.getRedDotsMap().values()) {
            updateRedDot(data);
        }
    }

    public void updateRedDot(CommonMsg.UpdateRedDotData data) {
        CommonEnum.RedDotKey redDotKey = CommonEnum.RedDotKey.forNumber(data.getRedDotKey());
        if (redDotKey == null) {
            WechatLog.error("redDotKey null! {}", data);
            return;
        }
        // 军团申请红点特殊处理，无论如何都要清除，因为实际上不需要保存两个
        if (data.getRedDotKey() == CommonEnum.RedDotKey.RDK_CLAN_APPLY_VALUE) {
            prop().removeRedDotMapV(redDotKey.getNumber());
        }
        if (data.getClearAll()) {
            prop().removeRedDotMapV(redDotKey.getNumber());
        } else {
            for (Struct.RedDotCell add : data.getAddCellsMap().values()) {
                updateRedDot(redDotKey, add);
            }
            removeCellIds(redDotKey, data.getRemoveCellIdsList());
        }
    }

    public void updateRedDot(CommonEnum.RedDotKey redDotKey, Struct.RedDotCell dotCell) {
        CommonEnum.RedDotKey realKey = redDotKey;
        RedDotDataProp dataProp = getOrCreateRedDot(realKey);
        dataProp.addEmptyCells(dotCell.getCellId());
    }
}

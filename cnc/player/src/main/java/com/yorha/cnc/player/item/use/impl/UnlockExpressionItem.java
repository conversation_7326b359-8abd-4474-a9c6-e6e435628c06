package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 解锁道具
 *
 * <AUTHOR>
 */
public class UnlockExpressionItem extends AbstractUsableItem {

    public UnlockExpressionItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
    }


    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        int expressionId = getTemplate().getEffectId();
        List<Integer> list = new ArrayList<>();
        list.add(expressionId);
        playerEntity.getSettingComponent().unlockExpression(list);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

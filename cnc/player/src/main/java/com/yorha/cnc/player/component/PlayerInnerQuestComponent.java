package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.ActivityOpenEvent;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.task.DispatchTroopEvent;
import com.yorha.cnc.player.event.task.PlayerOccupyPointEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.cityBuildLogic.TriggerQuestTemplateService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.InnerQuestProp;
import com.yorha.game.gen.prop.PlayerInnerQuestModelProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncQuestionnaire;
import res.template.TriggerQuestionTemplate;

import java.util.List;

/**
 * 内部问卷
 *
 * <AUTHOR>
 */
public class PlayerInnerQuestComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerInnerQuestComponent.class);

    static {
        EntityEventHandlerHolder.register(BuildFinEvent.class, PlayerInnerQuestComponent::monitorBuildLevelUp);
        EntityEventHandlerHolder.register(ActivityOpenEvent.class, PlayerInnerQuestComponent::monitorActivityOpen);
        EntityEventHandlerHolder.register(DispatchTroopEvent.class, PlayerInnerQuestComponent::monitorCreateTroop);
        EntityEventHandlerHolder.register(PlayerOccupyPointEvent.class, PlayerInnerQuestComponent::monitorClanOccupy);
    }

    private static void monitorActivityOpen(ActivityOpenEvent event) {
        event.getPlayer().getInnerQuestComponent().onActivityOpen(event.getId());
    }

    private static void monitorBuildLevelUp(BuildFinEvent event) {
        event.getPlayer().getInnerQuestComponent().onBuildLevelUp(event.getType(), event.getLevel());
    }

    /**
     * 占领据点
     */
    private static void monitorClanOccupy(PlayerOccupyPointEvent event) {
        event.getPlayer().getInnerQuestComponent().noMatchCheckInnerQuest(CommonEnum.QuestTriggerType.QTT_CAPTURE);
    }

    /**
     * 创建部队事件
     */
    private static void monitorCreateTroop(DispatchTroopEvent event) {
        if (event.getDeputyId() > 0) {
            event.getPlayer().getInnerQuestComponent().noMatchCheckInnerQuest(CommonEnum.QuestTriggerType.QTT_SUBHERO);
        }
    }

    /**
     * 指定建筑升级X级
     */
    private void onBuildLevelUp(CommonEnum.CityBuildType type, int level) {
        TriggerQuestTemplateService resService = ResHolder.getResService(TriggerQuestTemplateService.class);
        List<TriggerQuestionTemplate> configByType = resService.getConfigByType(CommonEnum.QuestTriggerType.QTT_BUILDING_LV);
        for (TriggerQuestionTemplate triggerQuestionTemplate : configByType) {
            Pair<Integer, Integer> paramPairByType = resService.getParamPairByType(CommonEnum.QuestTriggerType.QTT_BUILDING_LV, triggerQuestionTemplate);
            if (paramPairByType.getFirst() == type.getNumber() && level >= paramPairByType.getSecond()) {
                checkOrAddInnerQuest(triggerQuestionTemplate);
            }
        }
    }

    /**
     * 指定活动开启
     */
    private void onActivityOpen(int id) {
        TriggerQuestTemplateService resService = ResHolder.getResService(TriggerQuestTemplateService.class);
        List<TriggerQuestionTemplate> configByType = resService.getConfigByType(CommonEnum.QuestTriggerType.QTT_EVENT);
        for (TriggerQuestionTemplate triggerQuestionTemplate : configByType) {
            int paramByType = resService.getParamByType(CommonEnum.QuestTriggerType.QTT_EVENT, triggerQuestionTemplate);
            if (paramByType == id) {
                checkOrAddInnerQuest(triggerQuestionTemplate);
            }
        }
    }

    public void noMatchCheckInnerQuest(CommonEnum.QuestTriggerType type) {
        TriggerQuestTemplateService resService = ResHolder.getResService(TriggerQuestTemplateService.class);
        List<TriggerQuestionTemplate> configByType = resService.getConfigByType(type);
        for (TriggerQuestionTemplate triggerQuestionTemplate : configByType) {
            checkOrAddInnerQuest(triggerQuestionTemplate);
        }
    }

    public PlayerInnerQuestComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLogin() {
        TriggerQuestTemplateService resService = ResHolder.getResService(TriggerQuestTemplateService.class);
        List<TriggerQuestionTemplate> configByType = resService.getConfigByType(CommonEnum.QuestTriggerType.QTT_CREAT_ROLE_TIME);
        long onlineMs = getOwner().getOnlineMs();
        for (TriggerQuestionTemplate triggerQuestionTemplate : configByType) {
            int paramByType = resService.getParamByType(CommonEnum.QuestTriggerType.QTT_CREAT_ROLE_TIME, triggerQuestionTemplate);
            if (onlineMs >= TimeUtils.second2Ms(paramByType)) {
                checkOrAddInnerQuest(triggerQuestionTemplate);
            }
        }
    }

    public PlayerInnerQuestModelProp getProp() {
        return getOwner().getProp().getQuestModel();
    }

    public AssetPackage answerQuestHandler(int configId, int answerId) {
        if (configId <= 0 || answerId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        Pair<InnerQuestProp, TriggerQuestionTemplate> pair = checkOrGetProp(configId, getProp());

        InnerQuestProp innerQuestProp = pair.getFirst();
        TriggerQuestionTemplate configTemplate = pair.getSecond();

        long expireTsMs = innerQuestProp.getStatusSwitchTsMs() + TimeUtils.second2Ms(configTemplate.getOutOfOrderTime());

        // 已过期的需要手动过期掉
        if (TimeUtils.isBeforeNow(expireTsMs)) {
            expireQuest(configId, false, innerQuestProp);
            return AssetPackage.builder().build();
        }

        LOGGER.info("quest success, config:{}, answerId:{}", configId, answerId);
        innerQuestProp.setAnswerId(answerId).setStatus(CommonEnum.QuestStatus.QUESTS_ANSWER).setStatusSwitchTsMs(SystemClock.now());

        AssetPackage assetPackage = AssetPackage.builder().plusItems(configTemplate.getRewardPairList()).build();

        getOwner().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_QUESTIONNAIRE);

        sendQuestQLog(configId, answerId, "answer_questionnaire");
        return assetPackage;
    }

    public void expiredQuestHandler(int configId, boolean isActive) {
        if (configId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        Pair<InnerQuestProp, TriggerQuestionTemplate> pair = checkOrGetProp(configId, getProp());
        InnerQuestProp innerQuestProp = pair.getFirst();

        LOGGER.info("quest over, config:{}, isActive:{}", configId, isActive);
        expireQuest(configId, isActive, innerQuestProp);
    }

    private void expireQuest(int configId, boolean isActive, InnerQuestProp innerQuestProp) {
        innerQuestProp.setAnswerId(0).setStatus(CommonEnum.QuestStatus.QUESTS_ACTIVE_END).setStatusSwitchTsMs(SystemClock.now());
        String reason = isActive ? "close_questionnaire" : "time_runout_questionnaire";
        sendQuestQLog(configId, 0, reason);
    }

    /**
     * 添加问卷调查
     */
    private void checkOrAddInnerQuest(TriggerQuestionTemplate template) {
        if (getProp().getInnerQuest().containsKey(template.getId())) {
            return;
        }
        InnerQuestProp innerQuestProp = getProp().addEmptyInnerQuest(template.getId());
        if (template.getTriggerDelay() > 0) {
            innerQuestProp.setStatus(CommonEnum.QuestStatus.QUESTS_NONE).setStatusSwitchTsMs(SystemClock.now() + TimeUtils.second2Ms(template.getTriggerDelay()));
        } else {
            innerQuestProp.setStatus(CommonEnum.QuestStatus.QUESTS_START).setStatusSwitchTsMs(SystemClock.now());
        }
        LOGGER.info("add inner quest, template:{}", template.getId());
        sendQuestQLog(template.getId(), 0, "pop_questionnaire");
    }

    private void sendQuestQLog(int configId, int answer, String reason) {
        QlogCncQuestionnaire.init(getOwner().getQlogComponent())
                .setQuestionnaireId(configId)
                .setDtEventTime(TimeUtils.now2String())
                .setAnswer(answer)
                .setAction(reason)
                .sendToQlog();
    }

    /**
     * 检测并获取Prop
     */
    private static Pair<InnerQuestProp, TriggerQuestionTemplate> checkOrGetProp(int configId, PlayerInnerQuestModelProp prop) {
        TriggerQuestionTemplate configTemplate = ResHolder.getInstance().findValueFromMap(TriggerQuestionTemplate.class, configId);
        if (configTemplate == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        InnerQuestProp questProp = prop.getInnerQuestV(configId);
        if (questProp == null) {
            throw new GeminiException(ErrorCode.QUEST_NOT_EXIST);
        }
        if (isOver(questProp.getStatus())) {
            throw new GeminiException(ErrorCode.QUESTIONNAIRE_HAS_ENDED);
        }
        if (!isStart(questProp.getStatus()) && isDelayStart(questProp)) {
            throw new GeminiException(ErrorCode.QUESTIONNAIRE_NOT_OPEN);
        }
        return Pair.of(questProp, configTemplate);
    }

    private static boolean isDelayStart(InnerQuestProp prop) {
        return prop.getStatus() == CommonEnum.QuestStatus.QUESTS_NONE && prop.getStatusSwitchTsMs() > SystemClock.now();
    }

    /**
     * 问卷已初始化
     */
    private static boolean isStart(CommonEnum.QuestStatus status) {
        return status == CommonEnum.QuestStatus.QUESTS_START;
    }

    /**
     * 问卷已结束
     */
    private static boolean isOver(CommonEnum.QuestStatus status) {
        return status == CommonEnum.QuestStatus.QUESTS_ANSWER || status == CommonEnum.QuestStatus.QUESTS_ACTIVE_END;
    }


}

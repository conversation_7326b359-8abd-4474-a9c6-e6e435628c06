package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildCreateEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildLevelUpEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.Map;

/**
 * 内城建筑升级触发器
 *
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_INNER_BUILDING_LEVEL_UP)
public class InnerBuildingLevelUp extends AbstractActivityTrigger {

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerInnerBuildLevelUpEvent.class,
            PlayerInnerBuildCreateEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        if (params == null) {
            return false;
        }
        int eventBuildType = -1;
        if (event instanceof PlayerInnerBuildLevelUpEvent) {
            eventBuildType = ((PlayerInnerBuildLevelUpEvent) event).getBuildType();
        } else if (event instanceof PlayerInnerBuildCreateEvent) {
            eventBuildType = ((PlayerInnerBuildCreateEvent) event).getBuildType();
        }
        final int buildType = params.get(CommonEnum.ActTriggerParamType.ATPT_BUILD_TYPE);
        return eventBuildType == buildType;
    }
}

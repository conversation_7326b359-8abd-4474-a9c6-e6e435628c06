package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.BuildRhBuildingEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;


public class BuildRh<PERSON>um<PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            BuildRhBuildingEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer buildType = taskParams.get(0);
        Integer maxNum = taskParams.get(1);

        if (event instanceof BuildRhBuildingEvent) {
            if (buildType == ((BuildRhBuildingEvent) event).getBuildType()) {
                int num = ((BuildRhBuildingEvent) event).getPlaceNum();
                prop.setProcess(Math.min(num, maxNum));
            }
        } else {
            int num = event.getPlayer().getInnerBuildRhComponent().getPlacedBuildingNum(buildType);
            prop.setProcess(Math.min(num, maxNum));
        }

        return prop.getProcess() >= maxNum;
    }
}

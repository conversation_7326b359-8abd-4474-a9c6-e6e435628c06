package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerPB;
import res.template.CommanderAvatarFrameTemplate;
import res.template.ItemTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.yorha.proto.CommonEnum.Reason.ICR_EXCHANGE;

/**
 * 修改玩家头像框
 * zeo
 */
public class UnlockPlayerPicFrameUsableItem extends AbstractUsableItem {
    private final List<ItemReward> rewardList = new ArrayList<>();

    public UnlockPlayerPicFrameUsableItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (!ResHolder.getInstance().getMap(CommanderAvatarFrameTemplate.class).containsKey(getTemplate().getEffectId())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        boolean result = playerEntity.getProfileComponent().unLockPicFrame(template.getEffectId());
        if (!result) {
            rewardList.addAll(playerEntity.getItemComponent().sendReward(template.getEffectValue(), ICR_EXCHANGE, ""));
        }
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        PlayerPB.ItemUseResultPB.Builder resultPB = PlayerPB.ItemUseResultPB.newBuilder();
        resultPB.setItems(ItemResService.makeItemListPB(rewardList));
        response.setResult(resultPB);
    }
}

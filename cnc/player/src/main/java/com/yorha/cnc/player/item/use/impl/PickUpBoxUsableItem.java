package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemRewardBox;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerPB;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 自选宝箱
 */
public class PickUpBoxUsableItem extends AbstractUsableItem {

    private static final Logger LOGGER = LogManager.getLogger(PickUpBoxUsableItem.class);
    private final List<ItemReward> rewardList;
    private List<ItemRewardBox> rewardBox;
    private int rewardIndex;

    public PickUpBoxUsableItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
        this.rewardList = new ArrayList<>();
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (getTemplate() == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }

        List<ItemRewardBox> selectReward = ResHolder.getResService(ItemResService.class).getSelectReward(getTemplate().getEffectId());
        if (CollectionUtils.isEmpty(selectReward)) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }

        if (params.getIndex() < 0) {
            throw new GeminiException(ErrorCode.ITEM_NOT_USEABLE);
        }

        this.rewardBox = selectReward;
        this.rewardIndex = params.getIndex();
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (CollectionUtils.isEmpty(rewardBox)) {
            LOGGER.warn("player use pick up item {} select index {} empty reward", this, rewardIndex);
            return false;
        }
        ItemReward itemReward = RandomUtils.randomList(rewardBox).pickReward(rewardIndex);
        if (itemReward == null) {
            return false;
        }
        playerEntity.getItemComponent().addItem(itemReward.getItemTemplateId(), itemReward.getCount() * num, CommonEnum.Reason.ICR_BOX, "");
        rewardList.add(itemReward);
        // 是否应该直接使用自选宝箱里的道具
        boolean useDirectly = params.getUseDirectly();
        if (useDirectly) {
            int itemTemplateId = itemReward.getItemTemplateId();
            ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, itemTemplateId);
            CommonEnum.ItemUseType itemUseType = CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType());
            if (itemUseType == CommonEnum.ItemUseType.PICK_UP_BOX) {
                throw new GeminiException(ErrorCode.DOLLS_ARE_NOT_ALLOWED);
            }
            playerEntity.getItemComponent().useItemByTemplateId(itemTemplateId, itemReward.getCount() * num, new ItemUseParamsProp().setUseDirectly(false));
        }

        LOGGER.info("player use pick up item {} select index {} get reward {}", this, rewardIndex, rewardList);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        PlayerPB.ItemUseResultPB.Builder resultPB = PlayerPB.ItemUseResultPB.newBuilder();
        resultPB.setItems(ItemResService.makeItemListPB(rewardList));
        response.setResult(resultPB);
    }
}

package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家护送成功
 * <AUTHOR>
 */
public class PlayerEscortSuccessEvent extends PlayerTaskEvent {

    private int level;
    private long score;

    public PlayerEscortSuccessEvent(PlayerEntity entity, int level, long score) {
        super(entity);
        this.score = score;
        this.level = level;
    }

    public long getScore() {
        return score;
    }

    public int getLevel() {
        return level;
    }
}

package com.yorha.cnc.player.chat.handler;

import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.proto.CommonEnum.ChatChannel;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerChat.Player_GetChatMessages_C2S;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */

public interface ChatHandler {

    /**
     * 是否是同步模式 默认同步
     */
    default boolean isSyncModel() {
        return true;
    }

    /**
     * 聊天频道
     */
    ChatChannel chatChannel();

    /**
     * 同步请求聊天
     */
    long chatRequestSync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage);

    /**
     * 异步请求聊天
     */
    void chatRequestAsync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage, Consumer<Throwable> onComplete);

    /**
     * 请求获取聊天记录(元素的msgId呈降序)
     */
    List<CommonMsg.ChatMessage> queryChatMsgList(ChatPlayerEntity chatPlayerEntity, String channelId, Collection<Long> shieldList, Player_GetChatMessages_C2S msg);

    /**
     * 设置cd
     */
    long getCd(ChatPlayerEntity chatPlayerEntity);
}

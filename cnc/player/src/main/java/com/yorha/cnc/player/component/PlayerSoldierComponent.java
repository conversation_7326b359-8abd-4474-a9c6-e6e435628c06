package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.task.PlayerFinishTrainingSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.cnc.player.event.task.PlayerStarTrainingSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerStartLevelUpSoldierEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.props.MapPropKVQueue;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.PlayerSoldier;
import com.yorha.proto.SsScenePlayer.DismissInCitySoldiersAsk;
import com.yorha.proto.SsScenePlayer.PlayerAddSoldierAsk;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstNewbieSvrTemplate;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_SP_SOLDIER;


/**
 * 处理PlayerEntity身上兵营相关数据，注意玩家所拥有的总士兵数量是维护在ScenePlayer上的
 *
 * <AUTHOR>
 */
public class PlayerSoldierComponent extends PlayerComponent implements PlayerQueueTaskComponent.QueueTaskInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSoldierComponent.class);

    private PlayerSoldierInBuildingProp getProp() {
        return getOwner().getProp().getPlayerSoldierInBuilding();
    }

    /**
     * 兵种类型 -> 正在训练的soldierId
     */
    private final Map<SoldierType, Integer> trainingSoldierIdMap = Maps.newHashMap();
    /**
     * 兵种类型 -> 正在晋升的<fromSoldierId, toSoldierId>
     */
    private final Map<SoldierType, Pair<Integer, Integer>> levelUppingSoldierIdMap = Maps.newHashMap();

    private MapPropKVQueue<Integer, UpTrainInfoProp> upTrainsQueue;

    public PlayerSoldierComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        for (Integer trainingSoldierId : getProp().getTrainingSoldiers().keySet()) {
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(trainingSoldierId);
            if (soldierTemplate == null) {
                WechatLog.error("recover soldier template not found:{}", trainingSoldierId);
                return;
            }
            trainingSoldierIdMap.put(SoldierType.forNumber(soldierTemplate.getSoldierType()), trainingSoldierId);
        }
        for (Map.Entry<Integer, PlayerSoldierLevelUpUnitProp> entry : getProp().getLevelUppingSoldiers().entrySet()) {
            Integer fromSoldierId = entry.getValue().getToSoldierId();
            Integer toSoldierId = entry.getValue().getFromSoldierId();
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(toSoldierId);
            if (soldierTemplate == null) {
                WechatLog.error("recover lv upping soldier template not found:{}", toSoldierId);
                return;
            }
            levelUppingSoldierIdMap.put(SoldierType.forNumber(soldierTemplate.getSoldierType()), new Pair<>(fromSoldierId, toSoldierId));
        }

        upTrainsQueue = new MapPropKVQueue<>(ResHolder.getConsts(ConstTemplate.class).getExpansionItemLimit(),
                getProp().getUpTrains(),
                Comparator.comparingLong(UpTrainInfoProp::getId));
    }

    public void addUpTrainItem(int value, int num) {
        LOGGER.info("PlayerSoldierComponent addUpTrainItem {} {}", value, num);
        int nextId = 1;
        UpTrainInfoProp lastProp = upTrainsQueue.peekLast();
        if (lastProp != null) {
            nextId = lastProp.getId() + 1;
        }
        for (int i = 0; i < num; i++) {
            UpTrainInfoProp newProp = upTrainsQueue.offerLast(nextId + i);
            newProp.setId(nextId + i).setValue(value);
        }
        refreshUpTrainsProp();
    }

    private void refreshUpTrainsProp() {
        getProp().setUpleftTimes(upTrainsQueue.getSize());
        if (upTrainsQueue.getSize() <= 0) {
            getProp().setUpTrainAndLevelUpLimit(0);
            return;
        }
        getProp().setUpTrainAndLevelUpLimit(upTrainsQueue.peekFirst().getValue());
    }

    public int getUpTrainsQueueSize() {
        return upTrainsQueue.getSize();
    }

    public int getFirstUpTrain() {
        if (upTrainsQueue.getSize() <= 0) {
            return 0;
        }
        return upTrainsQueue.peekFirst().getValue();
    }

    public int pollFirstUpTrain() {
        if (upTrainsQueue.getSize() <= 0) {
            LOGGER.info("PlayerSoldierComponent pollFirstUpTrain {}", 0);
            return 0;
        }
        LOGGER.info("PlayerSoldierComponent pollFirstUpTrain {}", upTrainsQueue.peekFirst().getValue());
        int result = upTrainsQueue.pollFirst().getValue();
        refreshUpTrainsProp();
        return result;
    }

    private void addSoldierIntoTraining(SoldierType type, int soldierId, int num) {
        PlayerSoldierUnitProp p = new PlayerSoldierUnitProp()
                .setSoldierId(soldierId).setNum(num);
        getProp().putTrainingSoldiersV(p);
        trainingSoldierIdMap.put(type, soldierId);
        SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
        // 统计项
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.START_TRAIN_SOLDIER_NUM, soldierTemplate.getSoldierType(), num);
        new PlayerStarTrainingSoldierEvent(getOwner(), num, soldierTemplate.getSoldierType()).dispatch();
    }

    private PlayerSoldierUnitProp removeTrainingSoldier(SoldierType type, int soldierId) {
        PlayerSoldierUnitProp p = getProp().getTrainingSoldiersV(soldierId);
        getProp().removeTrainingSoldiersV(soldierId);
        trainingSoldierIdMap.remove(type);
        return p;
    }

    private void addSoldierIntoTrainingOver(Integer soldierId, Integer num, boolean isLevelUp, int fromSoldierId) {
        PlayerSoldierUnitProp existed = getProp().getTrainingOverSoldiersV(soldierId);
        if (existed != null && existed.getNum() > 0) {
            // 为什么要自动gather呢，因为trainingOverSoldier中新增了preSoldierId字段，不把上一次的trainingOver清掉的话这里就有问题了
            // 实际上游戏中不把前面的士兵领掉，也是不能开启下一次的训练的
            LOGGER.warn("addSoldierIntoTrainingOver auto gather!! {} {}", getOwner(), soldierId);
            handleGather(soldierId);
        }
        getProp().addEmptyTrainingOverSoldiers(soldierId)
                .setSoldierId(soldierId)
                .setNum(num)
                .setIsLevelUp(isLevelUp)
                .setPreSoldierId(fromSoldierId);
    }

    private void addSoldierIntoLevelUpping(SoldierType type, int fromSoldierId, int toSoldierId, int num) {
        PlayerSoldierLevelUpUnitProp p = new PlayerSoldierLevelUpUnitProp()
                .setFromSoldierId(fromSoldierId).setToSoldierId(toSoldierId).setNum(num);
        getProp().putLevelUppingSoldiersV(p);
        levelUppingSoldierIdMap.put(type, new Pair<>(fromSoldierId, toSoldierId));

        // 统计项
        SoldierTypeTemplate fromSoldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(fromSoldierId);
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.START_LEVEL_UP_SOLDIER_NUM, fromSoldierTemplate.getSoldierType(), num);
        new PlayerStartLevelUpSoldierEvent(getOwner(), fromSoldierTemplate.getSoldierType(), num).dispatch();
    }

    private PlayerSoldierLevelUpUnitProp removeLevelUppingSoldier(SoldierType type, int toSoldierId) {
        PlayerSoldierLevelUpUnitProp p = getProp().getLevelUppingSoldiersV(toSoldierId);
        getProp().removeLevelUppingSoldiersV(toSoldierId);
        levelUppingSoldierIdMap.remove(type);
        return p;
    }

    public void handleTrain(int soldierId, int num, boolean isRushTrain) {
        LOGGER.debug("{} train soldierId:{} num:{} isRushTrain:{}", getOwner(), soldierId, num, isRushTrain);
        // 检查客户端入参
        SoldierTypeTemplate soldierTemplate = checkMsgParamValid(soldierId, num, false);

        // 解锁兵种检测
        checkByPlayerTech(soldierId);

        SoldierType soldierType = SoldierType.forNumber(soldierTemplate.getSoldierType());
        QueueTaskType queueTaskType = GameLogicConstants.getQueueTaskTypeBySoldierType(soldierType);

        // 兵种对应的内城兵营建筑的状态检测
        canTrain(soldierType);


        // 计算消耗资源和时间
        Pair<AssetPackage, Long> costCurrency2Millis = calcCost(soldierTemplate, num);
        AssetPackage costPackage = costCurrency2Millis.getFirst();
        final long totalCostMs = costCurrency2Millis.getSecond();
        final long taskFinTsMs = costCurrency2Millis.getSecond() + SystemClock.now();

        // 队列检查并初始化
        checkAndInitQueue(soldierId, soldierType, queueTaskType, taskFinTsMs);

        // 检查资源
        getOwner().verifyThrow(costPackage);
        // 扣资源
        getOwner().consume(costPackage, Reason.ICR_ARMY);
        // 扣扩列道具
        pollFirstUpTrain();

        getProp().setFirstTrainTimeUsed(true);

        // 加入队列
        StructPlayerPB.QueueExtraParamPB.Builder extraParamPb = StructPlayerPB.QueueExtraParamPB.newBuilder();
        extraParamPb.setIsSoldierRush(isRushTrain);
        getOwner().getPlayerQueueTaskComponent().addQueueTask(queueTaskType, soldierId, totalCostMs, costPackage, extraParamPb.build());

        addSoldierIntoTraining(soldierType, soldierId, num);
    }

    /**
     * 暴兵
     */
    public void handleRushTrain(PlayerSoldier.Player_SoldierRushTrain_C2S msg) {
        LOGGER.debug("{} rush train msg:{}", getOwner(), msg);
        handleTrain(msg.getSoldierId(), msg.getNum(), true);
        // 加速队列
        List<StructPB.ItemPB> itemList = msg.getItemList().getDatasList();
        List<IntPairType> collect = itemList.stream().map(it -> IntPairType.makePair(it.getTemplateId(), it.getNum())).collect(Collectors.toList());
        SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(msg.getSoldierId());
        SoldierType soldierType = SoldierType.forNumber(soldierTemplate.getSoldierType());
        QueueTaskType queueTaskType = GameLogicConstants.getQueueTaskTypeBySoldierType(soldierType);

        getOwner().getPlayerQueueTaskComponent().speedUpQueueTask(queueTaskType, msg.getSoldierId(), collect);
    }

    public void handleFastTrain(PlayerSoldier.Player_SoldierFastTrain_C2S msg) {
        LOGGER.debug("{} train fast msg:{}", getOwner(), msg);

        int soldierId = msg.getSoldierId();
        int num = msg.getNum();

        // 检查客户端入参
        SoldierTypeTemplate soldierTemplate = checkMsgParamValid(soldierId, num, false);

        // 解锁士兵检测
        checkByPlayerTech(soldierId);

        // 计算原始消耗资源和时间
        Pair<AssetPackage, Long> costCurrency2Millis = calcCost(soldierTemplate, num);
        // 计算钻石消耗
        Pair<AssetPackage, AssetPackage> cost2RemainAssetPack = transformCost2FastCost(costCurrency2Millis);

        // 检查资源
        getOwner().verifyThrow(cost2RemainAssetPack.getFirst());
        // 扣资源
        getOwner().consume(cost2RemainAssetPack.getFirst(), Reason.ICR_ARMY);
        getProp().setFirstTrainTimeUsed(true);

        // 归还多扣的
        getOwner().getAssetComponent().give(cost2RemainAssetPack.getSecond(), Reason.ICR_TRAIN_G2R_RETURN, "handleFastTrain");
        // 扣扩列道具
        pollFirstUpTrain();
        // 直接发士兵
        PlayerAddSoldierAsk.Builder call = PlayerAddSoldierAsk.newBuilder();
        call.setPlayerId(getPlayerId())
                .setSoldierId(soldierId)
                .setAddNum(num)
                .setReason(SoldierNumChangeReason.train_army.toString());
        ownerActor().callBigScene(call.build());

        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.START_TRAIN_SOLDIER_NUM, soldierTemplate.getSoldierType(), num);
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.FINISH_TRAIN_SOLDIERID_NUM, soldierId, num);
        // 更新任务进度（动态事件）
        new PlayerStarTrainingSoldierEvent(getOwner(), num, soldierTemplate.getSoldierType()).dispatch();
        new PlayerFinishTrainingSoldierEvent(getOwner(), num, soldierId, 0).dispatch();

        // QLog
        QueueTaskType type = getQueueTypeWithSoldierId(soldierId);
        long costTimeMs = costCurrency2Millis.getSecond();
        long costTimeSec = TimeUtils.ms2Second(costTimeMs);
        new PlayerSpeedUpEvent(getOwner(), costTimeSec, type, QueueSpeedReason.STRAIGHTLY_ACCELERATE).dispatch();
        getOwner().getQlogComponent().sendQueueSpeedQLogWithQType(soldierId, costTimeSec, QueueSpeedReason.STRAIGHTLY_ACCELERATE, type);
    }

    /**
     * 玩家已解锁兵种检测
     */
    private void checkByPlayerTech(int soldierId) {
        if (!getOwner().getTechComponent().getUnlockSoldierList().contains(soldierId)) {
            // 兵种类型未解锁
            throw new GeminiException(ErrorCode.TECH_ARMS_TYPE_NOT_UNLOCKED);
        }
    }

    /**
     * 获取应该使用的队列类型
     */
    private QueueTaskType getQueueTypeWithSoldierId(int soldierId) {
        SoldierTypeTemplate soldierTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldierId);
        SoldierType soldierType = SoldierType.forNumber(soldierTemplate.getSoldierType());
        return GameLogicConstants.getQueueTaskTypeBySoldierType(soldierType);
    }

    public void handleGather(int soldierId) {
        PlayerSoldierUnitProp overSoldier = getProp().getTrainingOverSoldiersV(soldierId);
        if (overSoldier == null) {
            throw new GeminiException(ErrorCode.SOLDIER_NO_TRAIN_OVER_SOLDIER);
        }
        getProp().removeTrainingOverSoldiersV(soldierId);
        SoldierNumChangeReason reason = overSoldier.getIsLevelUp() ? SoldierNumChangeReason.update_army : SoldierNumChangeReason.train_army;

        PlayerAddSoldierAsk.Builder call = PlayerAddSoldierAsk.newBuilder();
        call.setPlayerId(getPlayerId())
                .setSoldierId(soldierId)
                .setAddNum(overSoldier.getNum())
                .setReason(reason.toString());
        ownerActor().callBigScene(call.build());
        // 士兵完成事件(动态事件)
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.FINISH_TRAIN_SOLDIERID_NUM, soldierId, overSoldier.getNum());
        new PlayerFinishTrainingSoldierEvent(getOwner(), overSoldier.getNum(), soldierId, overSoldier.getPreSoldierId()).dispatch();
    }

    public void handleDismiss(PlayerSoldier.Player_SoldierDismiss_C2S msg) {
        final int soldierId = msg.getSoldierId();
        SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
        if (soldierTemplate == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ID_NULL);
        }
        getOwner().getSettingComponent().checkSpassword(SPWC_SP_SOLDIER, soldierTemplate.getId(), msg.getSPassWord());
        final int num = msg.getNum();
        if (num <= 0) {
            throw new GeminiException(ErrorCode.PLAYER_SOLDIER_PRODUCE_PARAM);
        }
        // 扣除士兵
        DismissInCitySoldiersAsk.Builder call = DismissInCitySoldiersAsk.newBuilder()
                .setPlayerId(getEntityId())
                .setReason(SoldierNumChangeReason.manual_severance.toString())
                .putSoldierData(soldierId, num);
        ownerActor().callBigScene(call.build());
    }

    public void handleLevelUp(PlayerSoldier.Player_SoldierLevelUp_C2S msg) {
        LOGGER.debug("{} level up msg:{}", getOwner(), msg);

        int soldierId = msg.getSoldierId();
        int num = msg.getNum();

        // 检查客户端入参
        SoldierTypeTemplate soldierTemplate = checkMsgParamValid(soldierId, num, true);

        SoldierType soldierType = SoldierType.forNumber(soldierTemplate.getSoldierType());
        QueueTaskType queueTaskType = GameLogicConstants.getQueueTaskTypeBySoldierType(soldierType);

        // 兵种对应的内城兵营建筑的状态检测
        canLevelUp(soldierType);


        // 获取晋升后的士兵id
        SoldierTypeTemplate maxSoldier = getMaxSoldierId(soldierTemplate);
        int maxSoldierId = maxSoldier.getId();

        // 解锁士兵检测
        checkByPlayerTech(maxSoldierId);

        if (maxSoldierId <= soldierId) {
            throw new GeminiException(ErrorCode.SOLDIER_PROMOTE_ID_WRONG);
        }

        // 计算晋升消耗
        Pair<AssetPackage, Long> costCurrency2Millis = getRegularLevelUpCost(soldierTemplate, maxSoldierId, num);
        AssetPackage costPackage = costCurrency2Millis.getFirst();
        final long totalCostMs = costCurrency2Millis.getSecond();
        final long taskFinTsMs = totalCostMs + SystemClock.now();

        // 队列检查并初始化
        checkAndInitQueue(soldierId, soldierType, queueTaskType, taskFinTsMs);

        // 检查资源
        getOwner().verifyThrow(costPackage);
        // 扣资源
        getOwner().consume(costPackage, Reason.ICR_ARMY);
        // 扣除士兵
        DismissInCitySoldiersAsk.Builder call = DismissInCitySoldiersAsk.newBuilder().
                setPlayerId(getEntityId())
                .setReason(SoldierNumChangeReason.be_upgraded.toString())
                .putSoldierData(soldierId, num);
        ownerActor().callBigScene(call.build());
        // 扣除扩列道具
        pollFirstUpTrain();

        getOwner().getPlayerQueueTaskComponent().addQueueTask(queueTaskType, maxSoldierId, totalCostMs, costPackage, StructPlayerPB.QueueExtraParamPB.newBuilder().setIsSoldierLevelup(true).build());
        addSoldierIntoLevelUpping(soldierType, soldierId, maxSoldierId, num);
    }

    public void handleFastLevelUp(PlayerSoldier.Player_SoldierFastLevelUp_C2S msg) {
        LOGGER.debug("{} fast level up msg:{}", getOwner(), msg);

        int soldierId = msg.getSoldierId();
        int num = msg.getNum();

        // 检查客户端入参
        SoldierTypeTemplate soldierTemplate = checkMsgParamValid(soldierId, num, true);

        // 获取晋升后的士兵id
        SoldierTypeTemplate maxSoldier = getMaxSoldierId(soldierTemplate);
        int maxSoldierId = maxSoldier.getId();

        // 解锁士兵检测
        checkByPlayerTech(maxSoldierId);

        if (maxSoldierId <= soldierId) {
            throw new GeminiException(ErrorCode.SOLDIER_PROMOTE_ID_WRONG.getCodeId());
        }
        QueueTaskType queueType = GameLogicConstants.getQueueTaskTypeBySoldierType(SoldierType.forNumber(soldierTemplate.getSoldierType()));
        // 计算晋升消耗
        Pair<AssetPackage, Long> costCurrency2Millis = getRegularLevelUpCost(soldierTemplate, maxSoldierId, num);
        // 计算钻石消耗
        Pair<AssetPackage, AssetPackage> cost2RemainAssetPack = transformCost2FastCost(costCurrency2Millis);
        // 检查资源
        getOwner().verifyThrow(cost2RemainAssetPack.getFirst());

        // 扣除士兵
        DismissInCitySoldiersAsk.Builder dismissSoldierCall = DismissInCitySoldiersAsk.newBuilder()
                .setPlayerId(getEntityId())
                .setReason(SoldierNumChangeReason.be_upgraded.toString())
                .putSoldierData(soldierId, num);
        ownerActor().callBigScene(dismissSoldierCall.build());
        // 扣资源
        getOwner().consume(cost2RemainAssetPack.getFirst(), Reason.ICR_ARMY);

        // 归还多扣的
        getOwner().getAssetComponent().give(cost2RemainAssetPack.getSecond(), Reason.ICR_TRAIN_G2R_RETURN, "handleFastLevelUp");
        pollFirstUpTrain();
        // 直接发士兵
        PlayerAddSoldierAsk.Builder call = PlayerAddSoldierAsk.newBuilder();
        call.setPlayerId(getPlayerId())
                .setSoldierId(maxSoldierId)
                .setAddNum(num)
                .setReason(SoldierNumChangeReason.update_army.toString());
        ownerActor().callBigScene(call.build());

        QueueTaskType queueTaskType = getQueueTypeWithSoldierId(maxSoldierId);
        long costTimeMs = costCurrency2Millis.getSecond();
        long costTimeSec = TimeUtils.ms2Second(costTimeMs);

        // 累计完成训练
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.START_LEVEL_UP_SOLDIER_NUM, soldierTemplate.getSoldierType(), num);
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.FINISH_TRAIN_SOLDIERID_NUM, maxSoldierId, num);
        // 累计升级士兵
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.SOLDIER_LEVEL_UP, soldierId, num);
        new PlayerSpeedUpEvent(getOwner(), costTimeSec, queueType, QueueSpeedReason.STRAIGHTLY_ACCELERATE).dispatch();
        getOwner().getQlogComponent().sendQueueSpeedQLogWithQType(maxSoldierId, costTimeSec, QueueSpeedReason.STRAIGHTLY_ACCELERATE, queueTaskType);

        new PlayerStartLevelUpSoldierEvent(getOwner(), soldierTemplate.getSoldierType(), num).dispatch();
        new PlayerFinishTrainingSoldierEvent(getOwner(), num, maxSoldierId, soldierId).dispatch();
    }

    private SoldierTypeTemplate checkMsgParamValid(Integer soldierId, Integer num, boolean isLevelUp) {
        SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
        if (soldierTemplate == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ID_NULL);
        }

        if (num <= 0) {
            throw new GeminiException(ErrorCode.PLAYER_SOLDIER_PRODUCE_PARAM);
        }

        // 训练上限检查
        long upTrain = getFirstUpTrain();
        int numLimit = PlayerAddCalc.getTrainAndLevelUpLimit(getOwner(), soldierTemplate, upTrain);
        if (num > numLimit) {
            throw new GeminiException(ErrorCode.SOLDIER_TRAIN_LIMIT);
        }

        return soldierTemplate;
    }

    private void checkAndInitQueue(long soldierId, SoldierType soldierType, QueueTaskType queueTaskType, long taskFinTsMs) {
        if (trainingSoldierIdMap.containsKey(soldierType) || levelUppingSoldierIdMap.containsKey(soldierType)) {
            throw new GeminiException(ErrorCode.PLAYER_SOLDIER_PRODUCE_QUEUE_FULL);
        }
        unlockQueueBeforeUse(queueTaskType);
        getOwner().getPlayerQueueTaskComponent().queueFreeCheck(soldierId, queueTaskType, taskFinTsMs);
    }

    private void unlockQueueBeforeUse(QueueTaskType queueTaskType) {
        // 默认解锁训练队列
        QueueTasksProp queueTasks = getOwner().getPlayerQueueTaskComponent().getQueueTasks(queueTaskType);
        if (queueTasks == null) {
            getOwner().getPlayerQueueTaskComponent().unLockQueue(true, queueTaskType, 0);
        }
    }

    /**
     * 计算训练的消耗资源+时间
     */
    private Pair<AssetPackage, Long> calcCost(SoldierTypeTemplate soldierTemplate, Integer num) {
        long totalCostMillis = 0;
        Map<CurrencyType, Integer> costCurrency = Maps.newEnumMap(CurrencyType.class);
        List<IntPairType> trainRssCostPairList = soldierTemplate.getTrainRssCostPairList();

        for (IntPairType rssType2num : trainRssCostPairList) {
            CurrencyType currencyType = CurrencyType.forNumber(rssType2num.getKey());
            int costNum = (int) PlayerAddCalc.getSoldierTrainCostValue(getOwner(), rssType2num, num);
            costCurrency.merge(currencyType, costNum, Integer::sum);
        }

        boolean isNewbie = getOwner().getNewbieComponent().isNewbie();
        if (!getProp().getFirstTrainTimeUsed() && isNewbie) {
            // 新手期第一次训练时长固定
            int firstSoldierTrainSeconds = ResHolder.getConsts(ConstNewbieSvrTemplate.class).getFirstSoldierTrainSeconds();
            totalCostMillis += TimeUtils.second2Ms(firstSoldierTrainSeconds);
        } else {
            totalCostMillis += PlayerAddCalc.getSoldierTrainCostTime(getOwner(), soldierTemplate, num);
        }
        AssetPackage costPackage = AssetPackage.builder().plusCurrency(costCurrency).build();

        return new Pair<>(costPackage, totalCostMillis);
    }

    /**
     * 获取兵种已解锁的最大等级配置
     */
    private SoldierTypeTemplate getMaxSoldierId(SoldierTypeTemplate soldierTemplate) {
        for (Integer soldier : getOwner().getTechComponent().getUnlockSoldierList()) {
            SoldierTypeTemplate unLockSoldierTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldier);
            if (unLockSoldierTemplate.getSoldierType() == soldierTemplate.getSoldierType() && soldierTemplate.getSoldierLevel() < unLockSoldierTemplate.getSoldierLevel()) {
                soldierTemplate = unLockSoldierTemplate;
            }
        }
        return soldierTemplate;
    }

    /**
     * 晋升士兵资源消耗
     */
    private Pair<AssetPackage, Long> getRegularLevelUpCost(SoldierTypeTemplate soldierTemplate, Integer maxSoldierId, Integer num) {
        SoldierTypeTemplate highSoldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(maxSoldierId);
        if (highSoldierTemplate == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ID_NULL);
        }

        // 计算晋升消耗
        return calcLevelUpCost(soldierTemplate, highSoldierTemplate, num);
    }

    private Pair<AssetPackage, Long> calcLevelUpCost(SoldierTypeTemplate lowSoldierTemplate, SoldierTypeTemplate highSoldierTemplate, Integer num) {
        Map<CurrencyType, Integer> costCurrency = Maps.newEnumMap(CurrencyType.class);
        List<IntPairType> lowTrainRssCostPairList = lowSoldierTemplate.getTrainRssCostPairList();
        List<IntPairType> highTrainRssCostPairList = highSoldierTemplate.getTrainRssCostPairList();

        // 用高等级的士兵训练消耗减去低等级士兵训练消耗
        for (IntPairType highCost : highTrainRssCostPairList) {
            boolean containsFlag = false;

            for (IntPairType lowCost : lowTrainRssCostPairList) {
                if (lowCost.getKey() == highCost.getKey()) {
                    containsFlag = true;

                    CurrencyType currencyType = CurrencyType.forNumber(highCost.getKey());
                    int costNum = (int) PlayerAddCalc.getSoldierLevelUpCostValue(getOwner(), highCost, lowCost, num);
                    if (costNum > 0) {
                        costCurrency.merge(currencyType, costNum, Integer::sum);
                    }
                    break;
                }
            }

            // 高等级独有的消耗资源
            if (!containsFlag) {
                CurrencyType currencyType = CurrencyType.forNumber(highCost.getKey());
                int costNum = (int) PlayerAddCalc.getSoldierLevelUpCostValue(getOwner(), highCost, num);
                costCurrency.merge(currencyType, costNum, Integer::sum);
            }
        }
        AssetPackage costPackage = AssetPackage.builder().plusCurrency(costCurrency).build();

        // 计算耗时
        long totalCostMillis = 0;
        totalCostMillis += PlayerAddCalc.getSoldierLevelUpCostTime(getOwner(), lowSoldierTemplate, highSoldierTemplate, num);
        return new Pair<>(costPackage, totalCostMillis);
    }

    /**
     * 消耗换算成剩余消耗+钻石
     *
     * @return cost, remain 消耗和多扣返还
     */
    private Pair<AssetPackage, AssetPackage> transformCost2FastCost(Pair<AssetPackage, Long> costCurrency2Millis) {
        // 资源消耗
        Pair<AssetPackage, AssetPackage> cost2RemainAssetPack = getOwner().getAssetComponent().calcAssetToGold(costCurrency2Millis.getFirst());

        // 时间消耗
        AssetPackage costTime = getOwner().getAssetComponent().calcTimeMsToGold(costCurrency2Millis.getSecond());

        AssetPackage cost = AssetPackage.builder()
                .plus(costTime)
                .plus(cost2RemainAssetPack.getFirst())
                .build();


        getOwner().verifyThrow(cost);

        return Pair.of(cost, cost2RemainAssetPack.getSecond());
    }

    @Override
    public void onFinish(long taskId, QueueTaskProp taskProp) {
        LOGGER.debug("{} soldier queue finish:{}", getOwner(), taskProp);

        final int soldierId = (int) taskId;
        SoldierTypeTemplate template = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
        if (template == null) {
            WechatLog.error("training finish soldierTemplate not found:{}", taskId);
            return;
        }

        PlayerSoldierUnitProp p;
        boolean isLevelUpQueue = taskProp.getExtraParam().getIsSoldierLevelup();
        int fromSoldierId = 0;
        if (isLevelUpQueue) {
            PlayerSoldierLevelUpUnitProp lvUpP = removeLevelUppingSoldier(SoldierType.forNumber(template.getSoldierType()), soldierId);
            p = new PlayerSoldierUnitProp().setSoldierId(lvUpP.getToSoldierId()).setNum(lvUpP.getNum())
                    .setPreSoldierId(lvUpP.getFromSoldierId());
            fromSoldierId = lvUpP.getFromSoldierId();
        } else {
            p = removeTrainingSoldier(SoldierType.forNumber(template.getSoldierType()), soldierId);
        }

        // 挪到trainingOverSoldiers里面去
        addSoldierIntoTrainingOver(p.getSoldierId(), p.getNum(), isLevelUpQueue, fromSoldierId);

        if (isLevelUpQueue) {
            // 累计升级士兵
            getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.SOLDIER_LEVEL_UP, fromSoldierId, p.getNum());
        }
    }

    @Override
    public Map<Integer, Integer> immediatelyFinishPost(long taskId, QueueTaskProp taskProp) {
        LOGGER.debug("{} after soldier queue finish:{}", getOwner(), taskProp);
        HashMap<Integer, Integer> ans = new HashMap<>();
        final int soldierId = (int) taskId;
        PlayerSoldierUnitProp p = getProp().getTrainingOverSoldiersV(soldierId);
        if (p.getNum() <= 0) {
            LOGGER.error("soldierNum <= 0. prop:{}", getProp());
            return null;
        }
        ans.put(soldierId, p.getNum());
        handleGather(soldierId);
        LOGGER.info("立即完成自动领取士兵:{}", ans);
        return ans;
    }

    @Override
    public void onCancel(long taskId, QueueTaskProp taskProp) {
        final int soldierId = (int) taskId;
        SoldierTypeTemplate template = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
        // 训练、晋升取消返还比率
        int returnRadio = ResHolder.getResService(ConstKVResService.class).getTemplate().getCancelArmyReturnResource();
        if (template == null) {
            WechatLog.error("training cancel soldierTemplate not found:{}", taskId);
            return;
        }

        if (taskProp != null) {
            boolean isLevelUpQueue = taskProp.getExtraParam().getIsSoldierLevelup();
            if (isLevelUpQueue) {
                // 移除晋升中士兵
                PlayerSoldierLevelUpUnitProp lvUpP = removeLevelUppingSoldier(SoldierType.forNumber(template.getSoldierType()), soldierId);
                // 归还士兵
                PlayerAddSoldierAsk.Builder call = PlayerAddSoldierAsk.newBuilder();
                call.setPlayerId(getPlayerId())
                        .setSoldierId(lvUpP.getFromSoldierId())
                        .setAddNum(lvUpP.getNum())
                        .setReason(SoldierNumChangeReason.cancel_level_up.toString());
                ownerActor().callBigScene(call.build());
            } else {
                // 移除训练中士兵
                removeTrainingSoldier(SoldierType.forNumber(template.getSoldierType()), soldierId);
            }

            // 归还资源
            CurrencyListProp costResource = taskProp.getCostResource();
            costResource.forEach(it -> {
                CurrencyType currencyType = CurrencyType.forNumber(it.getType());
                if (currencyType != null) {
                    int realCount = (int) (it.getCount() * returnRadio / Constants.CONVERT_HUNDRED_POINTS);
                    getOwner().getPurseComponent().give(currencyType, realCount, Reason.ICR_ARMY, "");
                }
            });
        }
    }

    @Override
    public boolean isSpeedUpItemAvailable(ItemUseType itemUseType) {
        return itemUseType == ItemUseType.COMMON_SPEED || itemUseType == ItemUseType.SOLDIER_TRAINING_SPEED;
    }

    @Override
    public Reason speedUpUsingReason() {
        return Reason.ICR_ARMY;
    }

    /**
     * 判断兵营是否因为正在升级而不能训练士兵
     */
    private void canTrain(SoldierType soldierType) {

    }

    /**
     * 判断兵营是否因为正在升级而不能晋升士兵
     */
    private void canLevelUp(SoldierType soldierType) {

    }

    public long getLevelUpPower() {
        long power = 0;
        // 晋升中的
        for (Map.Entry<Integer, PlayerSoldierLevelUpUnitProp> entry : getProp().getLevelUppingSoldiers().entrySet()) {
            power += getSoldierPower(entry.getValue().getFromSoldierId(), entry.getValue().getNum());
        }
        // 晋升完待领取的
        for (Map.Entry<Integer, PlayerSoldierUnitProp> entry : getProp().getTrainingOverSoldiers().entrySet()) {
            if (!entry.getValue().getIsLevelUp()) {
                continue;
            }
            power += getSoldierPower(entry.getValue().getPreSoldierId(), entry.getValue().getNum());
        }
        return power;
    }

    private long getSoldierPower(int soldierId, int num) {
        SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
        if (soldierTemplate != null) {
            return (long) soldierTemplate.getPower() * num;
        }
        return 0;
    }
}



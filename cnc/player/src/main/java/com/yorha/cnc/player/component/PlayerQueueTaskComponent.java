package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerQueueUnlockEvent;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ItemUseType;
import com.yorha.proto.CommonEnum.QueueTaskType;
import com.yorha.proto.PlayerQueueTask;
import com.yorha.proto.SsClanHelp;
import com.yorha.proto.StructPlayerPB;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBuildCity;
import res.template.ConstClanTemplate;
import res.template.ConstTemplate;
import res.template.ItemTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 时间队列任务组件
 * 完成事件默认由客户端请求触发
 * 定时任务来保证城建到期自动完成
 *
 * <AUTHOR>
 * 2021年11月17日 11:56:00
 */
public class PlayerQueueTaskComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerQueueTaskComponent.class);

    private Int32QueueTasksMapProp queueTaskMap;

    private final Map<String, Map<Long, ActorTimer>> offlineCancelTask = new HashMap<>();

    public PlayerQueueTaskComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        queueTaskMap = getOwner().getProp().getQueueTaskMap();
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            unLockQueue(true, QueueTaskType.CITY_BUILD, 0);
        }
    }

    @Override
    public void postLoad(boolean isRegister) {
        // 需要放到postLoad
        refreshQueueTask();
    }

    /**
     * 立即完成接口
     */
    public PlayerQueueTask.Player_FinishQueueTask_S2C tryFinishQueueTask(QueueTaskType type, long taskId) {
        if (taskId <= 0) {
            throw new GeminiException(ErrorCode.BUILD_TASKID_PARAM_ERROR);
        }
        QueueTaskProp taskProp = getQueueTaskProp(type, taskId);
        if (taskProp == null) {
            throw new GeminiException(ErrorCode.BUILD_QUEUE_NOT_TASK);
        }

        getQueueTaskComponent(type).immediatelyFinishPreCost(taskProp);

        long now = SystemClock.now();
        long laveFinishTimeSec = TimeUtils.ms2Second(Math.max(0, taskProp.getEndTime() - now));

        taskProp.setSpeedTimeSec(taskProp.getSpeedTimeSec() + (int) laveFinishTimeSec);
        finishQueueTask(taskProp);
        QueueTaskInterface queueTaskComponent = getQueueTaskComponent(taskProp.getType());
        Map<Integer, Integer> soldierId2Num = queueTaskComponent.immediatelyFinishPost(taskId, taskProp);

        new PlayerSpeedUpEvent(getOwner(), laveFinishTimeSec, type, QueueSpeedReason.STRAIGHTLY_ACCELERATE).dispatch();
        getOwner().getQlogComponent().sendSpeedQLogWithTask(taskId, laveFinishTimeSec, QueueSpeedReason.STRAIGHTLY_ACCELERATE, taskProp);

        PlayerQueueTask.Player_FinishQueueTask_S2C.Builder builder = PlayerQueueTask.Player_FinishQueueTask_S2C.newBuilder();
        if (MapUtils.isNotEmpty(soldierId2Num)) {
            builder.putAllSoldierId2Num(soldierId2Num);
        }
        return builder.build();
    }

    /**
     * 免费的队列加速
     */
    public void queueFreeSpeed(long queueId) {
        if (queueId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        QueueTaskProp queueTaskProp = getQueueTaskWithQueueId(queueId);

        long now = SystemClock.now();
        if (!isValidQueue(now, queueTaskProp)) {
            throw new GeminiException(ErrorCode.QUQUE_QUEUE_HAS_EXPIRED);
        }
        if (!isCanFreeSpeed(queueTaskProp)) {
            throw new GeminiException(ErrorCode.QUEUE_QUEUE_CANNOT_BE_ACCELERATED);
        }

        long finishAfterTimeMs = queueTaskProp.getEndTime() - now;

        long freeSpeedTimeMs = getFreeSpeedTimeMs(queueTaskProp);

        long totalSpeedTimeMs = Math.min(finishAfterTimeMs, freeSpeedTimeMs);
        int totalSpeedTimeSec = (int) TimeUtils.ms2Second(totalSpeedTimeMs);

        queueTaskProp.setFreeSpeedCount(queueTaskProp.getFreeSpeedCount() + 1);
        queueTaskProp.setSpeedTimeSec(queueTaskProp.getSpeedTimeSec() + totalSpeedTimeSec);

        // 加速可能触发任务完成，任务完成会把taskId清空
        long taskId = queueTaskProp.getTaskId();

        long realSpeedTimeMs = taskSpeedExec(queueTaskProp, totalSpeedTimeMs);

        long realSpeedTimeSec = TimeUtils.ms2Second(realSpeedTimeMs);
        new PlayerSpeedUpEvent(getOwner(), realSpeedTimeSec, queueTaskProp.getType(), QueueSpeedReason.VIP_ACCELERATE).dispatch();
        getOwner().getQlogComponent().sendSpeedQLogWithTask(taskId, realSpeedTimeSec, QueueSpeedReason.VIP_ACCELERATE, queueTaskProp);
    }

    /**
     * 联盟帮助队列加速
     */
    public long clanHelpQueueSpeed(long queueId) {
        // 获取队列
        if (queueId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        QueueTaskProp queueTaskProp = getQueueTaskWithQueueId(queueId);

        if (null == queueTaskProp) {
            LOGGER.error("clanHelpQueueSpeed: queueTaskProp is null, queueId:{}", queueId);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // 队列有效期一定在任务有效期后，所以不需要判断队列有效期，仅判断队列内的任务是否有效
        long queueTaskRemainTimeMs = queueTaskProp.getEndTime() - SystemClock.now();
        if (queueTaskRemainTimeMs <= 0) {
            LOGGER.info("queue {} task {} have been finished", queueId, queueTaskProp.getTaskId());
            return 0L;
        }

        // 获取尝试减少的时间
        ConstClanTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        // max {a + b, c * d}
        long helpAdditionDecTimeMs = TimeUtils.second2Ms(getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_CLAN_HELP_ACC_TIME_FIXED));
        long helpBaseDecTimeMs = TimeUtils.second2Ms(constTemplate.getHelpBaseDecTime()) + helpAdditionDecTimeMs;
        int helpBaseDecTimePercent = constTemplate.getHelpBaseDecTimePercent();
        long helpBaseDecTimePercentMs = queueTaskRemainTimeMs / Constants.N_10_000L * helpBaseDecTimePercent;
        long helpTryDecMs = Math.max(helpBaseDecTimeMs, helpBaseDecTimePercentMs);

        // 执行加速，完成检查交给调用方法
        long realSpeedTimeSec = TimeUtils.ms2Second(taskSpeedExec(queueTaskProp, helpTryDecMs));
        LOGGER.debug("help try dec {}ms, real dec speed {}s", helpTryDecMs, realSpeedTimeSec);
        // 治疗队列需要增加今日已治疗次数
        if (queueTaskProp.getType() == QueueTaskType.MEDICAL_TREATMENT) {
            getOwner().getPlayerClanComponent().addClanHelpCureTimesAndCheck();
        }

        // qlog
        getOwner().getQlogComponent().sendSpeedQLogWithTask(queueTaskProp.getTaskId(), realSpeedTimeSec,
                QueueSpeedReason.GUILD_HELP, queueTaskProp);
        return realSpeedTimeSec;
    }

    /**
     * 取消任务接口
     */
    public void tryCancelQueueTask(QueueTaskType type, long taskId) {
        if (taskId <= 0) {
            throw new GeminiException(ErrorCode.BUILD_TASKID_PARAM_ERROR);
        }
        QueueTaskProp taskProp = getQueueTaskProp(type, taskId);
        if (taskProp == null) {
            throw new GeminiException(ErrorCode.BUILD_QUEUE_NOT_TASK);
        }
        if (!canCancel(type)) {
            throw new GeminiException(ErrorCode.QUEUE_TYPE_NOT_ALLOWED_CANCEL);
        }
        cancelQueueTask(taskProp);
    }

    /**
     * EVA技能加速
     */
    public void queueSpeedByEva(QueueTaskType queueTaskType, long second2Ms) {
        List<QueueTaskProp> queueTaskList = getQueueTaskListProp(queueTaskType);
        if (queueTaskList == null || queueTaskList.isEmpty()) {
            return;
        }
        for (QueueTaskProp queueTaskProp : queueTaskList) {
            taskSpeedExec(queueTaskProp, second2Ms);
        }
    }

    /**
     * 同步特定类型完成或取消的队列至当前联盟
     *
     * @param queueId 队列id
     * @param type    队列类型
     */
    private void syncQueueFinishedToClan(long queueId, QueueTaskType type) {
        if (!isQueueNeedToSynClan(type)) {
            return;
        }
        ownerActor().tellCurClan(SsClanHelp.OnQueueTaskFinishedCmd.newBuilder()
                .setQueueId(queueId).build());
    }

    /**
     * 队列是否需要同步到军团
     * 判断队列是否属于军团帮助可添加的类型，玩家是否在军团内
     *
     * @param type 队列类型
     */
    private boolean isQueueNeedToSynClan(QueueTaskType type) {
        switch (type) {
            case CITY_BUILD:
            case MEDICAL_TREATMENT:
            case RESEARCH: {
                break;
            }
            default: {
                return false;
            }
        }
        if (!getOwner().getPlayerClanComponent().isInClan()) {
            LOGGER.debug("player {} is not in clan, no need to sync clan help", getOwner().getPlayerId());
            return false;
        }
        return true;
    }

    /**
     * 清除队列任务
     */
    void tryClearQueueTask(QueueTaskType type, long taskId) {
        if (taskId <= 0) {
            return;
        }
        QueueTaskProp taskProp = getQueueTaskProp(type, taskId);
        if (taskProp == null) {
            return;
        }
        finishQueueTask(taskProp);
    }

    /**
     * 加速任务接口
     */
    public void speedUpQueueTask(QueueTaskType type, long taskId, List<IntPairType> itemList) {
        if (taskId <= 0) {
            throw new GeminiException(ErrorCode.BUILD_TASKID_PARAM_ERROR);
        }

        QueueTaskProp taskProp = getQueueTaskProp(type, taskId);
        if (taskProp == null) {
            throw new GeminiException(ErrorCode.BUILD_QUEUE_NOT_TASK);
        }

        if (CollectionUtils.isEmpty(itemList)) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }

        int speedUpSec = buildSpeedTime(type, itemList);

        if (speedUpSec <= 0) {
            throw new GeminiException(ErrorCode.BUILD_SPEED_TIME_PARAM_ERR);
        }

        // 校验道具充足
        AssetPackage cost = AssetPackage.builder().plusItems(itemList).build();
        getOwner().verifyThrow(cost);

        // 道具消耗
        CommonEnum.Reason reason = getQueueTaskComponent(type).speedUpUsingReason();
        getOwner().consume(cost, reason);

        long speedTimeMs = TimeUtils.second2Ms(speedUpSec);

        // 执行加速
        long realSpeedTimeMs = taskSpeedExec(taskProp, speedTimeMs);
        long realSpeedTimeSec = TimeUtils.ms2Second(realSpeedTimeMs);

        new PlayerSpeedUpEvent(getOwner(), speedUpSec, type, QueueSpeedReason.ACCELERATE_CARD).dispatch();

        getOwner().getQlogComponent().sendSpeedQLogWithTask(taskId, realSpeedTimeSec, QueueSpeedReason.ACCELERATE_CARD, taskProp);
    }


    /**
     * 执行队列任务加速
     *
     * @param speedTimeMs 加速时长 毫秒
     * @return 真实加速的时间
     */
    private long taskSpeedExec(QueueTaskProp taskProp, long speedTimeMs) {
        long realSpeedTimeMs = 0;
        if (speedTimeMs <= 0) {
            LOGGER.warn("QueueTaskProp speed up fail.speedTimeMs:{} task:{}", speedTimeMs, taskProp);
            return realSpeedTimeMs;
        }
        long laveFinishTimeMs = taskProp.getEndTime() - SystemClock.now();
        if (laveFinishTimeMs <= 0) {
            LOGGER.warn("QueueTaskProp speed up fail, task:{}", taskProp);
            return realSpeedTimeMs;
        }
        realSpeedTimeMs = Math.min(laveFinishTimeMs, speedTimeMs);
        LOGGER.info("exec inner city queue speed up, task:{}, speedTimeMs:{}", taskProp.getId(), speedTimeMs);
        taskProp.setEndTime(taskProp.getEndTime() - realSpeedTimeMs);
        finishCheck(taskProp.getType());
        return realSpeedTimeMs;
    }

    /**
     * 执行任务检测
     */
    private void finishCheck(QueueTaskType type) {
        pollCheckFinish(type);
    }

    private boolean isCanFreeSpeed(QueueTaskProp queueTaskProp) {
        // 没有加速次数
        if (queueTaskProp.getFreeSpeedCount() < 0) {
            return false;
        }
        ConstTemplate template = ResHolder.getResService(ConstKVResService.class).getTemplate();
        List<Integer> freeSpeedQueueType = template.getFreeSpeedQueueType();
        // 不允许免费加速
        if (!freeSpeedQueueType.contains(queueTaskProp.getType().getNumber())) {
            return false;
        }
        // 已超过加速次数
        return template.getQueueTaskFreeSpeedCount() > queueTaskProp.getFreeSpeedCount();
    }

    /**
     * 解锁队列
     *
     * @param unlockSec 0:无限期  other:解锁时长（Sec）
     */
    public void unLockQueue(boolean isDefaultFreeQueue, QueueTaskType type, long unlockSec) {
        if (unlockSec < 0) {
            return;
        }
        QueueTaskListProp queueTaskListProp = queueTaskMap.computeIfAbsent(type.getNumber(), k -> new QueueTasksProp().setId(type.getNumber())).getQueueTaskList();

        // 找到限时队列数据
        QueueTaskProp firstNonPermanentQueue = null;
        for (QueueTaskProp queueTaskProp : queueTaskListProp) {
            if (queueTaskProp.getQueueExpirTime() > 0) {
                firstNonPermanentQueue = queueTaskProp;
                break;
            }
        }

        // 添加永久队列
        if (unlockSec == 0) {
            if (firstNonPermanentQueue == null) {
                queueTaskListProp.add(
                        new QueueTaskProp()
                                .setId(queueTaskListProp.size() + 1)
                                .setQueueExpirTime(0)
                                .setType(type)
                );
            } else {
                firstNonPermanentQueue.setQueueExpirTime(0);
            }

            if (!isDefaultFreeQueue) {
                new PlayerQueueUnlockEvent(getOwner(), type, 0).dispatch();
            }
            return;
        }


        if (firstNonPermanentQueue != null) {
            // 过期时间 = 当前时间 + 剩余自由时间 + 解锁时间
            long queueExpireTime = Math.max(0, firstNonPermanentQueue.getQueueExpirTime() - SystemClock.now()) + TimeUtils.second2Ms(unlockSec) + SystemClock.now();
            firstNonPermanentQueue.setQueueExpirTime(queueExpireTime);
        } else {
            // 过期时间 = 当前时间 + 解锁时间
            long queueExpireTime = TimeUtils.second2Ms(unlockSec) + SystemClock.now();
            queueTaskListProp.add(
                    new QueueTaskProp()
                            .setId(queueTaskListProp.size() + 1)
                            .setQueueExpirTime(queueExpireTime)
                            .setType(type)
            );
        }
        if (!isDefaultFreeQueue) {
            new PlayerQueueUnlockEvent(getOwner(), type, unlockSec).dispatch();
        }
    }


    List<QueueTaskProp> getQueueTaskListProp(QueueTaskType type) {
        QueueTasksProp queueTasksProp = queueTaskMap.get(type.getNumber());
        if (queueTasksProp != null) {
            long now = SystemClock.now();
            List<QueueTaskProp> result = new ArrayList<>();
            for (QueueTaskProp queueTaskProp : queueTasksProp.getQueueTaskList()) {
                if (queueTaskProp.getQueueExpirTime() == 0 || queueTaskProp.getQueueExpirTime() > now) {
                    result.add(queueTaskProp);
                }
            }
            return result;
        }
        return null;
    }

    QueueTasksProp getQueueTasks(QueueTaskType type) {
        return queueTaskMap.get(type.getNumber());
    }

    public QueueTaskProp getQueueTaskProp(QueueTaskType type, long taskId) {
        List<QueueTaskProp> queueTaskListProp = getQueueTaskListProp(type);
        if (queueTaskListProp != null) {
            for (QueueTaskProp queueProp : queueTaskListProp) {
                if (queueProp.getTaskId() == taskId) {
                    return queueProp;
                }
            }
        }
        return null;
    }

    void queueFreeCheck(long taskId, QueueTaskType type, long taskFinTsMs) {
        List<QueueTaskProp> queueTaskListProp = getQueueTaskListProp(type);
        // 队列重复占用检测
        if (taskId > 0 && hasTaskQueue(queueTaskListProp, taskId)) {
            throw new GeminiException(ErrorCode.BUILD_TASKID_PARAM_ERROR);
        }
        // 空闲队列检测
        if (getFreeQueueWithType(queueTaskListProp, taskFinTsMs) == null) {
            throw new GeminiException(ErrorCode.BUILD_FREE_QUEUE_NO_ENOUGH);
        }
    }

    public boolean isBusyQueue(QueueTaskType type, long taskId) {
        List<QueueTaskProp> queueTaskListProp = getQueueTaskListProp(type);
        // 队列重复占用检测
        return hasTaskQueue(queueTaskListProp, taskId);
    }

    public static boolean hasTaskQueue(List<QueueTaskProp> queueTaskListProp, long taskId) {
        for (QueueTaskProp queueTaskProp : queueTaskListProp) {
            if (queueTaskProp.getTaskId() == taskId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取空闲队列
     * 空闲:队列未过期且未被任务占用
     */
    private QueueTaskProp getFreeQueueWithType(List<QueueTaskProp> queueTaskListProp, long taskFinTsMs) {
        if (CollectionUtils.isNotEmpty(queueTaskListProp)) {
            for (QueueTaskProp taskProp : queueTaskListProp) {
                if (isValidQueue(taskFinTsMs, taskProp) && isIdleQueue(taskProp)) {
                    return taskProp;
                }
            }
        }
        return null;
    }

    /**
     * 判断是否是有效队列
     * WARN：并不判断队列内的任务是否已经完成
     */
    private boolean isValidQueue(long taskFinTsMs, QueueTaskProp taskProp) {
        if (taskProp == null) {
            return false;
        }
        return taskProp.getQueueExpirTime() == 0 || taskProp.getQueueExpirTime() > taskFinTsMs;
    }

    /**
     * 是否是空闲队列
     */
    private boolean isIdleQueue(QueueTaskProp taskProp) {
        return taskProp.getTaskId() == 0;
    }

    private QueueTaskProp getQueueTaskWithQueueId(long queueId) {
        for (QueueTasksProp taskList : queueTaskMap.values()) {
            for (QueueTaskProp taskProp : taskList.getQueueTaskList()) {
                if (taskProp.getId() == queueId) {
                    return taskProp;
                }
            }
        }
        return null;
    }

    /**
     * 创建任务
     */
    long addQueueTask(QueueTaskType type, long taskId, long costTimeMs, AssetPackage cost, StructPlayerPB.QueueExtraParamPB extraParamPb) {
        List<QueueTaskProp> queueTaskListProp = getQueueTaskListProp(type);
        if (queueTaskListProp == null || queueTaskListProp.isEmpty()) {
            throw new GeminiException(ErrorCode.BUILD_QUEUE_NOT_TASK);
        }
        QueueTaskProp taskProp = queueTaskListProp.stream()
                .filter(it -> it.getTaskId() == 0)
                .findFirst()
                .orElseGet(() -> {
                    // 不存在则创建
                    QueueTaskProp newTaskProp = new QueueTaskProp();
                    queueTaskListProp.add(newTaskProp);
                    return newTaskProp;
                });

        // 更新数据（保证全部数据都要刷新到!!）
        long now = SystemClock.now();
        taskProp.setType(type)
                .setTaskId(taskId)
                .setStarTime(now)
                .setSpeedTimeSec(Constants.INT_INIT_VALUE)
                .setFreeSpeedCount(Constants.INT_INIT_VALUE)
                .setEndTime(now + costTimeMs)
                .getExtraParam().mergeFromCs(extraParamPb);

        taskProp.getCostResource().clear();
        // 已消耗资源记录（实际消耗资源不是固定值，所以队列暂存）
        if (cost != null) {
            List<CurrencyProp> resources = Lists.newArrayList();
            // 消耗道具不会受到玩家加成影响，所以无需记录在队列中
            cost.forEachCurrency(currencyDesc -> resources.add(currencyDesc.toCurrencyProp()));
            taskProp.getCostResource().addAll(resources);
        }

        LOGGER.debug("{} add queue task:{} type:{} costTimeMs:{}", getEntityId(), taskId, type, costTimeMs);

        if (taskProp.getEndTime() <= taskProp.getStarTime()) {
            finishQueueTask(taskProp);
            return taskProp.getId();
        }

        finishCheck(type);
        return taskProp.getId();
    }

    /**
     * 获取队列类型中最快完成的任务
     * 已过期的队列依然要检测任务是否完成
     */
    private QueueTaskProp getFastestQueueTask(QueueTaskType type) {
        QueueTasksProp queueTaskPropList = queueTaskMap.get(type.getNumber());
        if (queueTaskPropList == null) {
            return null;
        }
        List<QueueTaskProp> queueTasksProp = queueTaskPropList.getQueueTaskList();
        if (queueTasksProp == null || queueTasksProp.isEmpty()) {
            return null;
        }
        return queueTasksProp.stream()
                .filter(it -> it.getTaskId() > 0)
                .min(Comparator.comparing(QueueTaskProp::getEndTime))
                .orElse(null);
    }

    public void finishQueueTask(QueueTaskProp taskProp) {
        long taskId = taskProp.getTaskId();
        taskProp.setTaskId(0).setEndTime(SystemClock.now());
        finishCheck(taskProp.getType());
        QueueTaskInterface queueTaskComponent = getQueueTaskComponent(taskProp.getType());
        LOGGER.info("queue finish, buildId={} queueId={} queueType={} taskId={} star={} end={}", taskId, taskProp.getId(), taskProp.getType(), taskProp.getTaskId(), taskProp.getStarTime(), taskProp.getEndTime());
        queueTaskComponent.onFinish(taskId, taskProp);
        syncQueueFinishedToClan(taskProp.getId(), taskProp.getType());
        taskProp.setIsOpenClanHelp(false);
        PlayerQueueTask.Player_QueueFinish_NTF ntf = PlayerQueueTask.Player_QueueFinish_NTF.newBuilder().setTaskId(taskId).setQueueType(taskProp.getType()).build();
        getOwner().sendMsgToClient(MsgType.PLAYER_QUEUEFINISH_NTF, ntf);
    }

    private void cancelQueueTask(QueueTaskProp taskProp) {
        long taskId = taskProp.getTaskId();
        taskProp.setTaskId(0).setEndTime(SystemClock.now());
        LOGGER.info("queue cancel, buildId={} queueId={} queueType={} taskId={} star={} end={}", taskId, taskProp.getId(), taskProp.getType(), taskProp.getTaskId(), taskProp.getStarTime(), taskProp.getEndTime());
        finishCheck(taskProp.getType());
        getQueueTaskComponent(taskProp.getType()).onCancel(taskId, taskProp);
        syncQueueFinishedToClan(taskProp.getId(), taskProp.getType());
        taskProp.setIsOpenClanHelp(false);
    }

    /**
     * 获取玩家免费加速时长（后续VIP系统做好之后放过去）
     */
    public static long getFreeSpeedTimeMs(QueueTaskProp queueTaskProp) {
        if (queueTaskProp != null && queueTaskProp.getFreeSpeedCount() > 0) {
            return 0;
        }
        int queueTaskFreeSpeedTimeSec = ResHolder.getResService(ConstKVResService.class).getTemplate().getQueueTaskFreeSpeedTimeSec();
        return TimeUtils.second2Ms(queueTaskFreeSpeedTimeSec);
    }

    /**
     * 根据道具列表检测后拼装为实际加速时间（秒）
     */
    private int buildSpeedTime(QueueTaskType type, List<IntPairType> itemList) {
        int totalSpeedUpSec = 0;
        for (IntPairType item : itemList) {
            if (item.getKey() <= 0) {
                throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
            }
            // 道具类型判断
            ItemTemplate templateByItemKey = ResHolder.getInstance().getValueFromMap(ItemTemplate.class, item.getKey());
            ItemUseType itemUseType = ItemUseType.forNumber(templateByItemKey.getEffectType());
            if (!Objects.requireNonNull(getQueueTaskComponent(type)).isSpeedUpItemAvailable(itemUseType)) {
                // 错误的加速道具类型
                throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
            }
            if (item.getValue() < 0) {
                throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
            }
            // 更新任务进展
            int speedUpSec = templateByItemKey.getEffectValue() * item.getValue();
            totalSpeedUpSec += speedUpSec;
        }
        return totalSpeedUpSec;
    }

    /**
     * 检测队列任务
     */
    public void refreshQueueTask() {
        queueTaskMap.values().forEach(it -> {
            QueueTaskType type = QueueTaskType.forNumber(it.getId());
            finishCheck(type);
        });
    }

    /**
     * 判断是否为收费队列
     */
    public boolean isFeeQueue(long queueId, QueueTaskType type) {
        QueueTasksProp queueTasksProp = queueTaskMap.get(type.getNumber());
        if (queueTasksProp == null) {
            return false;
        }
        QueueTaskListProp queueTaskList = queueTasksProp.getQueueTaskList();
        return queueTaskList != null && !queueTaskList.isEmpty() && queueTaskList.getFirst().getId() != queueId;
    }

    /**
     * 按到期时间递归检测完成事件
     */
    private void pollCheckFinish(QueueTaskType taskType) {
        QueueTaskProp fastestQueueTask = getFastestQueueTask(taskType);
        if (fastestQueueTask == null) {
            // 终止递归
            clearSchedule(taskType);
            return;
        }

        long now = SystemClock.now();
        long delayTsMs = fastestQueueTask.getEndTime() - now;
        if (delayTsMs <= 0) {
            // 完成事件立即触发
            finishQueueTask(fastestQueueTask);
        } else {
            String entityId = getPlayerId() + "-" + fastestQueueTask.getTaskId() + "-" + taskType.name();
            // 已有更快的定时任务，不再添加
            Map<Long, ActorTimer> scheduleTasks = offlineCancelTask.computeIfAbsent(taskType.name(), k -> new HashMap<>());

            // 取消已添加的重复定时任务
            ActorTimer actorTimer = scheduleTasks.get(fastestQueueTask.getId());
            if (actorTimer != null && !actorTimer.isCanceled()) {
                actorTimer.cancel();
            }

            // 定时任务触发再次检测
            LOGGER.info("add queue task in schedule:{} scheduleTaskName:{} delayTsMs:{}", fastestQueueTask.getId(), entityId, delayTsMs);
            ActorTimer queue = ownerActor().addTimer(entityId, TimerReasonType.POLL_CHECK_FINISH_QUEUE, () -> pollCheckFinish(taskType), delayTsMs, TimeUnit.MILLISECONDS);
            if (queue != null) {
                scheduleTasks.put(fastestQueueTask.getId(), queue);
            }
        }
    }

    private void clearSchedule(QueueTaskType taskType) {
        Map<Long, ActorTimer> longActorTimerMap = offlineCancelTask.get(taskType.name());
        if (longActorTimerMap == null) {
            return;
        }
        for (ActorTimer value : longActorTimerMap.values()) {
            if (value != null && !value.isCanceled()) {
                value.cancel();
            }
        }
        offlineCancelTask.remove(taskType.name());
    }

    public void buildInnerBuildQLog(QlogCncBuildCity cncBuildQLog, long queueId) {
        if (queueId <= 0) {
            cncBuildQLog.setStartTime(TimeUtils.now2String()).setBuildingFormation(2);
            return;
        }
        QueueTaskProp queueTaskProp = getQueueTaskWithQueueId(queueId);
        if (queueTaskProp == null) {
            cncBuildQLog.setStartTime(TimeUtils.now2String()).setBuildingFormation(2);
            return;
        }
        int freeQueue = getOwner().getPlayerQueueTaskComponent().isFeeQueue(queueId, QueueTaskType.CITY_BUILD) ? 1 : 0;
        cncBuildQLog.setStartTime(TimeUtils.timeStampMs2String(queueTaskProp.getStarTime())).setBuildingFormation(freeQueue);
    }

    /**
     * 使用时间队列需要实现的组件接口
     */
    public interface QueueTaskInterface {

        /**
         * 立即完成所需消耗接口
         */
        default void immediatelyFinishPreCost(QueueTaskProp taskProp) {
            long remainTimeMillis = taskProp.getEndTime() - SystemClock.now();
            if (remainTimeMillis > 0) {
                // 时间消耗
                long costTimeMs = taskProp.getEndTime() - SystemClock.now();
                AssetPackage assetPackage = getOwner().getAssetComponent().calcTimeMsToGold(costTimeMs);

                getOwner().verifyThrow(assetPackage);
                getOwner().consume(assetPackage, speedUpUsingReason());
            }
        }

        /**
         * 完成事件
         */
        void onFinish(long taskId, QueueTaskProp taskProp);

        /**
         * 完成事件后
         */
        default Map<Integer, Integer> immediatelyFinishPost(long taskId, QueueTaskProp taskProp) {
            return null;
        }

        /**
         * 可以取消
         */
        default boolean canCancel() {
            return true;
        }

        void onCancel(long taskId, QueueTaskProp taskProp);

        PlayerEntity getOwner();

        /**
         * 是否是可用的加速道具类型判断
         */
        boolean isSpeedUpItemAvailable(ItemUseType itemUseType);

        CommonEnum.Reason speedUpUsingReason();
    }

    private boolean canCancel(QueueTaskType type) {
        return getQueueTaskComponent(type).canCancel();
    }

    private QueueTaskInterface getQueueTaskComponent(QueueTaskType type) {
        if (GameLogicConstants.isSoldierTrainingQueue(type)) {
            return getOwner().getSoldierComponent();
        }
        return switch (type) {
            case CITY_BUILD_UPGRADE -> getOwner().getInnerBuildRhComponent();
            case MEDICAL_TREATMENT -> getOwner().getHospitalComponent();
            case RESEARCH -> getOwner().getTechComponent();
            default -> {
                LOGGER.error("队列类型:{} 未注册组件", type);
                throw new GeminiException(ErrorCode.QUEUE_TYPE_ERROR);
            }
        };
    }

    public static List<CommonEnum.QueueTaskType> getAllSoldierQueueType() {
        ArrayList<QueueTaskType> queueTaskTypes = new ArrayList<>();
        queueTaskTypes.add(CommonEnum.QueueTaskType.SOLDIER_FOOT_TRAINING);
        queueTaskTypes.add(CommonEnum.QueueTaskType.SOLDIER_TANK_TRAINING);
        queueTaskTypes.add(CommonEnum.QueueTaskType.SOLDIER_ARTILLERY_TRAINING);
        queueTaskTypes.add(CommonEnum.QueueTaskType.SOLDIER_ARMORED_CAR_TRAINING);
        return queueTaskTypes;
    }

    /**
     * 根据内城建筑类型判断相关队列是否空闲
     *
     * @param innerCityBuildType 内城建筑类型
     */
    public void checkQueueTaskState(CommonEnum.InnerCityBuildType innerCityBuildType, long taskFinTsMs) {
        QueueTaskType queueTaskType = AdditionConstants.getQueueTaskTypeByInnerCityBuildType(innerCityBuildType);
        // 不在表中的建筑不需要检测，直接返回
        if (queueTaskType == null) {
            return;
        }
        // List为空表明队列还未创建，不需要判断
        List<QueueTaskProp> taskList = getQueueTaskListProp(queueTaskType);
        if (taskList == null) {
            return;
        }
        // 有空闲队列，直接返回
        if (getFreeQueueWithType(taskList, taskFinTsMs) != null) {
            return;
        }

        // 后面只是区分错误码！！！天坑

        // 科技中心正在研发科技，不能升级
        if (innerCityBuildType == CommonEnum.InnerCityBuildType.TECHNOLOGY_CENTER) {
            throw new GeminiException(ErrorCode.TECH_CAN_NOT_LEVEL_UP_IN_RESEARCH);
        }

        // 医院正在治疗伤兵，不能升级
        if (innerCityBuildType == CommonEnum.InnerCityBuildType.SERVICE_STATION) {
            throw new GeminiException(ErrorCode.HOSPITAL_CAN_NOT_LEVEL_UP_IN_TREAT);
        }
        boolean isLevelUp = false;
        for (QueueTaskProp prop : taskList) {
            isLevelUp = prop.getExtraParam().getIsSoldierLevelup();
        }
        // 兵营正在晋升士兵，不能升级
        if (isLevelUp) {
            throw new GeminiException(ErrorCode.ARMY_CAN_NOT_LEVEL_UP_IN_PROMOTION);
        }
        // 兵营正在训练士兵，不能升级
        throw new GeminiException(ErrorCode.ARMY_CAN_NOT_LEVEL_UP_IN_TRAINING);
    }


    /**
     * 设置队列已开启联盟帮助
     */
    public void setClanHelpFlag(long taskId, QueueTaskType type) {
        QueueTaskProp taskProp = getOwner().getPlayerQueueTaskComponent().getQueueTaskProp(type, taskId);
        if (taskProp == null) {
            LOGGER.info("task id {} type {} cannot get its prop", taskId, type);
            throw new GeminiException(ErrorCode.QUQUE_QUEUE_HAS_EXPIRED);
        }
        taskProp.setIsOpenClanHelp(true);
    }

    public boolean hasBusyQueue(QueueTaskType type) {
        QueueTasksProp queueTasks = getQueueTasks(type);
        if (queueTasks == null) {
            return false;
        }
        for (QueueTaskProp queueTaskProp : queueTasks.getQueueTaskList()) {
            if (TimeUtils.isBeforeNow(queueTaskProp.getStarTime()) && TimeUtils.isAfterNow(queueTaskProp.getEndTime())) {
                return true;
            }
        }
        return false;
    }

    public boolean isSecondBuildQueueForeverUnlocked() {
        QueueTasksProp queueTasksProp = this.getQueueTasks(QueueTaskType.CITY_BUILD);
        if (queueTasksProp == null || queueTasksProp.getQueueTaskListSize() < 2) {
            return false;
        }
        QueueTaskProp secondQueue = queueTasksProp.getQueueTaskListIndex(1);
        return secondQueue.getQueueExpirTime() == 0;
    }

    public void unlockSecondBuildQueueForever() {
        LOGGER.info("{} unlockSecondBuildQueueForever", getOwner());
        unLockQueue(false, QueueTaskType.CITY_BUILD, 0);
    }
}

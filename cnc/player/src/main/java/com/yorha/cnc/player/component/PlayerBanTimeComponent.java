package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.PlayerBanInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.SsSceneDungeon;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;

/**
 * 玩家封禁组件
 *
 * <AUTHOR>
 */
public class PlayerBanTimeComponent extends PlayerComponent {
    public static final Logger LOGGER = LogManager.getLogger(PlayerBanTimeComponent.class);

    public PlayerBanTimeComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
    }

    private PlayerBanInfoProp getBanInfo() {
        return getOwner().getProp().getBasicInfo().getBanInfo();
    }

    /**
     * 检测玩家封禁时间
     */
    public void checkPlayerBanTime() {
        final long banEndTime = getBanInfo().getBanEndTime();
        final int lastBanReasonId = getBanInfo().getLastBanReasonId();
        if (SystemClock.now() < banEndTime) {
            final String param = ErrorCodeUtils.buildBanParam(lastBanReasonId, banEndTime);
            LOGGER.info("player is be ban. player={}, banEndTime={}, reason={}", getPlayerId(), new Date(banEndTime), lastBanReasonId);
            throw new GeminiException(ErrorCode.PLAYER_PLAYER_IS_BANNED, param);
        }
    }

    /**
     * 封禁玩家固定时间
     */
    public void disablePlayer(SsPlayerMisc.BanPlayerFixMsAsk cmd) {
        long banFixMs = cmd.getBanFixMs();
        if (banFixMs <= 0) {
            return;
        }
        long banEndTsMs = banFixMs + SystemClock.now();

        getBanInfo().setBanEndTime(banEndTsMs).setLastBanReasonId(cmd.getLastBanReasonId());
        if (!getOwner().isOnline()) {
            return;
        }
        getOwner().kickOffMe(CommonEnum.SessionCloseReason.SCR_PLAYER_BAN);

        //销毁角色数据并且不保存到数据库
        if (cmd.getLastBanReasonId() == 999) {
            ownerActor().forceDestroy("disabled");
        }
    }

    /**
     * 取消封禁
     */
    public void cancelBan() {
        getBanInfo().setBanEndTime(SystemClock.now()).setLastBanReasonId(0);
    }

    public long getBanEndTsMs() {
        return getBanInfo().getBanEndTime();
    }
}

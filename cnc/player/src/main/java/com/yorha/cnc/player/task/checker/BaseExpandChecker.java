package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.BaseExpandEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

public class BaseExpand<PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(BaseExpandEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int requireId = taskParams.getFirst();
        var player = event.getPlayer();

        var areaProp = player.getInnerBuildRhComponent().getBaseAreaProp(requireId);
        if (areaProp == null) {
            prop.setProcess(0);
        } else {
            prop.setProcess(requireId);
        }

        return prop.getProcess() >= requireId;
    }
}

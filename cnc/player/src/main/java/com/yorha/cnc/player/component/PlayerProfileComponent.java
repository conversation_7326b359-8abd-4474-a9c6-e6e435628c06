package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PicChangeEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.player.AvatarResService;
import com.yorha.common.resource.resservice.shop.ShopDataTemplateService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncProfilePhotoFlow;
import qlog.flow.QlogCncRoleNameFlow;
import res.template.ConstTemplate;
import res.template.ShopTemplate;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * 玩家基本信息功能
 *
 * <AUTHOR>
 */

public class PlayerProfileComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerProfileComponent.class);

    public PlayerProfileComponent(PlayerEntity owner) {
        super(owner);
    }

    private PlayerCardHeadProp getHeadProp() {
        return getOwner().getProp().getAvatarModel().getCardHead();
    }

    public void checkModifyName(CommonEnum.ChangeNameType type, String newPlayerName) {
        // 检查重置静默期
        if (getOwner().getProp().getResetInfo().getBanResetNameTsMs() >= SystemClock.now()) {
            LOGGER.debug("change name fail because of quiet period");
            throw new GeminiException(ErrorCode.PLAYER_NAME_CHANGE_CD);
        }
        // 检查名字是否合法
        ConstTemplate bean = getResHolder().getConstTemplate(ConstTemplate.class);
        if (!StringUtils.checkLength(newPlayerName, bean.getCommanderNameMaxLen(), bean.getCommanderNameMinLen())) {
            throw new GeminiException(ErrorCode.SYSTEM_NAME_LENGTH_ILLEGAL);
        }
        // 含标点符号
        if (StringUtils.containsSpecialChar(newPlayerName)) {
            throw new GeminiException(ErrorCode.SYSTEM_NAME_ILLEGALITY);
        }
        // 首尾空格
        if (newPlayerName.startsWith(" ") || newPlayerName.endsWith(" ")) {
            throw new GeminiException(ErrorCode.SYSTEM_NAME_ILLEGALITY);
        }
        // 检查重名
        ErrorCode code = NameHelper.checkNameRepeat(ownerActor(), CommonEnum.NameType.PLAYER_NAME, newPlayerName);
        if (code.isNotOk()) {
            throw new GeminiException(code);
        }

        // 资源检查
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        switch (type) {
            case CNT_ITEM: {
                PlayerItemComponent itemComponent = getOwner().getItemComponent();
                if (itemComponent.notEnough(constTemplate.getModifyNameItemId(), constTemplate.getModifyNameItemNum())) {
                    throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
                }
                break;
            }
            case CNT_MONEY: {
                ShopTemplate shopConf = ResHolder.getResService(ShopDataTemplateService.class).findItemShopConf(constTemplate.getModifyNameItemId());
                if (shopConf == null) {
                    throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
                }
                IntPairType pricePair = shopConf.getPricePair();
                ErrorCode errorCode = getOwner().getPurseComponent().isEnough(pricePair.getKey(), pricePair.getValue());
                if (!errorCode.isOk()) {
                    throw new GeminiException(errorCode);
                }
                break;
            }
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(newPlayerName, CommonEnum.UgcSceneId.USI_PLAYER_NAME);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }

        //占用新名
        ErrorCode errorCode = NameHelper.occupyName(ownerActor(), CommonEnum.NameType.PLAYER_NAME, newPlayerName, getPlayerId());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
    }

    public void checkModifyPic() {
        // 检查重置静默期
        if (getOwner().getProp().getResetInfo().getBanResetPicTsMs() >= SystemClock.now()) {
            LOGGER.debug("change name fail because of quiet period");
            throw new GeminiException(ErrorCode.PLAYER_PIC_CHANGE_CD);
        }
    }

    public Core.Code modifyPicFrame(int picFrame) {
        int oldPicFrame = getOwner().getProp().getAvatarModel().getCardHead().getPicFrame();
        getHeadProp().setPicFrame(picFrame);
        onPicFrameChange(picFrame);
        LOGGER.debug("player:{} change picFrame:({})->({})", getOwner(), oldPicFrame, picFrame);
        return ErrorCode.OK.getCode();
    }

    public boolean unLockPic(int pic) {
        Int32PicInfoMapProp picList = getOwner().getProp().getAvatarModel().getPicList();
        // 解锁关联头像
        int movingPicId = ResHolder.getResService(AvatarResService.class).getMovingPicId(pic);
        if (movingPicId > 0) {
            unLockMovingPic(movingPicId);
        }

        if (picList.containsKey(pic)) {
            return false;
        }
        picList.put(pic, new PicInfoProp().setId(pic));
        LOGGER.info("player:{} unlock pic:{}", getOwner(), pic);
        return true;
    }

    private void unLockMovingPic(int movingPicId) {
        Int32PicInfoMapProp picList = getOwner().getProp().getAvatarModel().getPicList();
        if (picList.containsKey(movingPicId)) {
            return;
        }
        picList.put(movingPicId, new PicInfoProp().setId(movingPicId));
        LOGGER.info("player:{} unlock moving pic:{}", getOwner(), movingPicId);
    }

    public boolean unLockPicFrame(int picFrame) {
        Int32PicInfoMapProp picFrameList = getOwner().getProp().getAvatarModel().getPicFrameList();
        if (picFrameList.containsKey(picFrame)) {
            return false;
        }
        picFrameList.put(picFrame, new PicInfoProp().setId(picFrame));
        return true;
    }

    public Core.Code modifyPic(int pic) {
        int oldPic = getHeadProp().getPic();
        getHeadProp().setPic(pic);
        onPicChange(pic);
        LOGGER.debug("player:{} change pic:({})->({})", getOwner(), oldPic, pic);
        return ErrorCode.OK.getCode();
    }

    public Core.Code modifyPlayerName(String name) {
        String oldName = getOwner().getName();
        getHeadProp().setName(name);
        NameHelper.releaseName(ownerActor(), CommonEnum.NameType.PLAYER_NAME, oldName);
        LOGGER.debug("player:{} change name:({})->({})", getOwner(), oldName, name);
        onPlayerNameChange(name);
        return ErrorCode.OK.getCode();
    }

    /**
     * 重置玩家昵称，昵称随机生成, 仅限IdIp和GM使用
     */
    private void resetPlayerName(long quietPeriodTsMs) {
        try {
            PlayerZoneModelProp zoneModel = getOwner().getProp().getZoneModel();
            // 随机生成英文名称
            String name = NameHelper.newPlayerName(CommonEnum.Language.en, zoneModel.getBornZoneId(), zoneModel.getBornZoneInex());
            // 改名, 不检查静默期
            modifyPlayerName(name);
            // 重置静默期
            getOwner().getProp().getResetInfo().setBanResetNameTsMs(quietPeriodTsMs);
            // 告诉NameActor释放掉
            SsName.ReleaseNameCmd.Builder builder = SsName.ReleaseNameCmd.newBuilder();
            builder.setOwnerId(getEntityId()).setNameType(CommonEnum.NameType.PLAYER_NAME).build();
            ownerActor().tellName(builder.build());
        } catch (Exception e) {
            // 1、非GeminiException肯定需要提醒 2、GeminiException中的Failed也需要提醒
            if (GeminiException.isLogicException(e)) {
                LOGGER.info("exception_perf logic ResetPlayerName {} ", getOwner(), e);
            } else {
                LOGGER.error("exception_perf player ResetPlayerName {} ", getOwner(), e);
            }
        }
    }

    /**
     * 重置玩家头像，头像随机选择, 仅限IdIp和GM使用
     */
    private void resetPlayerPic(long quietPeriodTsMs) {
        int pic = RandomUtils.randomList(ResHolder.getResService(AvatarResService.class).getInitPic());
        getOwner().getProfileComponent().modifyPic(pic);
        //重置静默期
        getOwner().getProp().getResetInfo().setBanResetPicTsMs(quietPeriodTsMs);
    }

    /**
     * 重置玩家信息（昵称或头像）,仅限IdIp和GM使用
     */
    public void resetPlayerInfo(boolean resetName, boolean resetPic, int quietSecondTime) {
        long quietPeriodTsMs = SystemClock.now() + TimeUtils.second2Ms(quietSecondTime);
        if (resetName) {
            this.resetPlayerName(quietPeriodTsMs);
        }
        if (resetPic) {
            this.resetPlayerPic(quietPeriodTsMs);
        }
    }

    private void onPicChange(int picId) {
        try {
            new PicChangeEvent(getOwner()).dispatch();
            // 更改friendPlayerEntity的铭牌数据
            PlayerCardHeadProp prop = getOwner().ownerActor().getOrLoadFriendPlayerEntity().getProp().getCardHead();
            prop.setPic(picId);

            SsScenePlayer.SyncPlayerPicCmd cmd = SsScenePlayer.SyncPlayerPicCmd.newBuilder().setPlayerId(getEntityId()).setPic(picId).build();
            ownerActor().tellBigScene(cmd);
            // 通知clan
            StructClan.ClanMember ssClanMember = StructClan.ClanMember.newBuilder()
                    .setCardHead(Struct.PlayerCardHead.newBuilder().setPic(picId).build()).build();
            getOwner().getPlayerClanComponent().updateClanMemberInfo(ssClanMember);
            QlogCncProfilePhotoFlow flow = QlogCncProfilePhotoFlow.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("change_profile_photo")
                    .setSubjectId(picId);
            flow.sendToQlog();
            // 通知PlayerCard更新
            getOwner().getPlayerPropComponent().updatePlayerCardCache(false);
        } catch (Exception e) {
            LOGGER.error("PlayerProfileComponent onPicChange fail, ", e);
        }

    }

    private void onPicFrameChange(int picFrameId) {
        try {
            // 更改friendPlayerEntity的铭牌数据
            PlayerCardHeadProp prop = getOwner().ownerActor().getOrLoadFriendPlayerEntity().getProp().getCardHead();
            prop.setPicFrame(picFrameId);

            SsScenePlayer.SyncPlayerPicFrameCmd cmd = SsScenePlayer.SyncPlayerPicFrameCmd.newBuilder().setPlayerId(getEntityId()).setPicFrame(picFrameId).build();
            ownerActor().tellBigScene(cmd);
            // 通知clan
            StructClan.ClanMember ssClanMember = StructClan.ClanMember.newBuilder()
                    .setCardHead(Struct.PlayerCardHead.newBuilder().setPicFrame(picFrameId).build()).build();
            getOwner().getPlayerClanComponent().updateClanMemberInfo(ssClanMember);
            QlogCncProfilePhotoFlow flow = QlogCncProfilePhotoFlow.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("change_frame")
                    .setSubjectId(picFrameId);
            flow.sendToQlog();
            // 通知PlayerCard更新
            getOwner().getPlayerPropComponent().updatePlayerCardCache(false);
        } catch (Exception e) {
            LOGGER.error("PlayerProfileComponent onPicFrameChange fail, ", e);
        }
    }

    private void onPlayerNameChange(String newName) {
        try {
            // 更改friendPlayerEntity的铭牌数据
            PlayerCardHeadProp prop = getOwner().ownerActor().getOrLoadFriendPlayerEntity().getProp().getCardHead();
            QlogCncRoleNameFlow flow = QlogCncRoleNameFlow.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setNameBefore(prop.getName())
                    .setNameAfter(newName);
            flow.sendToQlog();
            prop.setName(newName);
            // 通知scene
            SsScenePlayer.SyncPlayerNameCmd cmd = SsScenePlayer.SyncPlayerNameCmd.newBuilder().setPlayerId(getEntityId()).setName(newName).build();
            ownerActor().tellBigScene(cmd);
            // 通知clan
            StructClan.ClanMember ssClanMember = StructClan.ClanMember.newBuilder()
                    .setCardHead(Struct.PlayerCardHead.newBuilder().setName(newName).build()).build();
            getOwner().getPlayerClanComponent().updateClanMemberInfo(ssClanMember);
            // 通知PlayerCard更新
            getOwner().getPlayerPropComponent().updatePlayerCardCache(true);
        } catch (Exception e) {
            LOGGER.error("PlayerProfileComponent onPlayerNameChange fail, ", e);
        }
    }

    public CommonMsg.ChatMessage.Builder buildChatSender() {
        CommonMsg.ChatMessage.Builder messageBuilder = CommonMsg.ChatMessage.newBuilder();
        messageBuilder.setChatTimestamp(SystemClock.now())
                .getChatMessageSenderBuilder()
                .setSenderId(getPlayerId())
                .setZoneId(ownerActor().getZoneId())
                .setCardHead(getHeadProp().getCopyCsBuilder());
        // 联盟信息
        if (getOwner().getPlayerClanComponent().isInClan()) {
            StructClan.ClanBaseInfo clanBaseInfo = getOwner().getPlayerClanComponent().getClanBaseInfo();
            if (clanBaseInfo != null) {
                messageBuilder.getChatMessageSenderBuilder().setClanSimpleName(clanBaseInfo.getSname());
            }
        }
        PlayerSettingModelProp settingModelProp = getOwner().getProp().getSettingModel();
        // 个人旗帜
        if (settingModelProp.getShowPFlag()) {
            messageBuilder.getChatMessageSenderBuilder().setPFlagId(settingModelProp.getCurPFlag());
        }
        return messageBuilder;
    }
}

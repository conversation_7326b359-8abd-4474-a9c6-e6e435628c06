package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 内城建造建筑完成事件
 *
 * <AUTHOR>
 */
public class PlayerInnerBuildCreateEvent extends PlayerTaskEvent {
    private final int buildType;
    private final int maxLevelBefore;
    private final int maxLevelAfter;

    public PlayerInnerBuildCreateEvent(PlayerEntity player, int buildType, int maxLevelBefore, int maxLevelAfter) {
        super(player);
        this.buildType = buildType;
        this.maxLevelBefore = maxLevelBefore;
        this.maxLevelAfter = maxLevelAfter;
    }

    public int getBuildType() {
        return buildType;
    }

    public int getMaxLevelBefore() {
        return maxLevelBefore;
    }
    public int getMaxLevelAfter() {
        return maxLevelAfter;
    }
}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.Core;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

import static com.yorha.proto.CommonEnum.ChangeNameType;


/**
 * 玩家改名
 *
 * <AUTHOR>
 */

public class ModifyPlayerNameUsableItem extends AbstractUsableItem {
    private static final Logger LOGGER = LogManager.getLogger(ModifyPlayerNameUsableItem.class);

    protected String newPlayerName;

    public ModifyPlayerNameUsableItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        newPlayerName = params.getNewPlayerName();
        if (newPlayerName.isEmpty()) {
            return;
        }
        playerEntity.getProfileComponent().checkModifyName(ChangeNameType.CNT_ITEM, newPlayerName);
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (newPlayerName.isEmpty()) {
            return true;
        }
        Core.Code code = playerEntity.getProfileComponent().modifyPlayerName(newPlayerName);
        if (!ErrorCode.isOK(code)) {
            LOGGER.error("player:{} change name failed. code:{}", playerEntity, code);
            return false;
        }
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        if (newPlayerName.isEmpty()) {
            return;
        }
        response.setResult(PlayerPB.ItemUseResultPB.newBuilder().setNewPlayerName(newPlayerName));
    }
}

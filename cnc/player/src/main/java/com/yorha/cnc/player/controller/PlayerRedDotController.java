package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;

@Controller(module = CommonEnum.ModuleEnum.ME_USER)
public class PlayerRedDotController {

    /**
     * 红点移除
     */
    @CommandMapping(code = MsgType.PLAYER_REMOVEREDDOT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCommon.Player_RemoveRedDot_C2S msg) {
        playerEntity.getRedDotComponent().handleCsRemoveRedDot(msg);
        return PlayerCommon.Player_RemoveRedDot_S2C.getDefaultInstance();
    }

}

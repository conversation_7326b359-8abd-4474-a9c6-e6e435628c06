package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 完成X次英雄升级
 *
 * <AUTHOR>
 */
public class PlayerHeroLevelUpEvent extends PlayerTaskEvent {
    private final int heroId;
    private final int curLevel;
    private final int upLevelNum;

    public int getUpLevelNum() {
        return upLevelNum;
    }

    public PlayerHeroLevelUpEvent(PlayerEntity entity, int heroId, int curLevel, int upLevelNum) {
        super(entity);
        this.heroId = heroId;
        this.curLevel = curLevel;
        this.upLevelNum = upLevelNum;
    }

    public int getHeroId() {
        return heroId;
    }

    public int getCurLevel() {
        return curLevel;
    }
}

package com.yorha.cnc.player.friend.component;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.framework.AbstractComponent;

/**
 * <AUTHOR>
 */
public class FriendPlayerComponent extends AbstractComponent<FriendPlayerEntity> {
    public FriendPlayerComponent(FriendPlayerEntity owner) {
        super(owner);
    }

    @Override
    public PlayerActor ownerActor() {
        return getOwner().ownerActor();
    }

    /**
     * 注册流程里，entity创建出来之后
     * 异常将会终止注册
     */
    public void onRegister() {

    }

    /**
     * 玩家注册后
     * 异常会终止注册
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     */
    public void postRegister() {
    }

    /**
     * 玩家数据加载/创建完毕后
     * 异常将会终止其他模块的onLoad，playerActor拉起异常
     * 注册/登录/内部拉起都会执行
     */
    public void onLoad() {

    }

    /**
     * 玩家数据加载/创建完毕后
     * 异常会终止其他模块的postLoad
     * 注册/登录/内部拉起都会执行
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     */
    public void postLoad() {
    }

    /**
     * 登录流程中entity load完毕之后
     * 异常将会终止其他模块的onLogin
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     */
    public void onLogin() {

    }

    /**
     * 异常会终止其他模块的postLogin
     * 此处依然是同步调用，如有需求可以显式使用PlayerActor.tell(Runnable)来异步调用
     */
    public void postLogin() {
    }

    /**
     * 玩家登出后
     */
    public void afterLogout() {
    }
}

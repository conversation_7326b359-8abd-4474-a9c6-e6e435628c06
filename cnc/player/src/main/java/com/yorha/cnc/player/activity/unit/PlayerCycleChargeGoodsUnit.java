package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityCycleChargeGoodsUnitProp;
import com.yorha.game.gen.prop.ActivitySelectGoodsRecordInfoProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.GoodsChainInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.collections4.IterableUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityTemplate;
import res.template.ChargeChainTemplate;
import res.template.ChargeGoodsTemplate;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 循环礼包活动
 * <p>
 * 由一个或多个礼包链组成，不同礼包链互不影响，单个礼包链内必须先购买上一个礼包才能购买下一个
 *
 * <AUTHOR>
 */
public class PlayerCycleChargeGoodsUnit extends BasePlayerActivityUnit implements PlayerEventListener {

    private static final Logger LOGGER = LogManager.getLogger(PlayerCycleChargeGoodsUnit.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerDayRefreshEvent.class
    );

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_CYCLE_CHARGE_GOODS, (owner, prop, template) ->
                new PlayerCycleChargeGoodsUnit(
                        owner,
                        prop.getChargeGoodsChainUnit(),
                        ImmutableList.copyOf(template.getGoodsChainArrayList()))
        );
    }

    private final ImmutableList<Integer> goodsChainIds;

    // 当前订单的token，同时只会有一个订单在购买
    private String curOrderToken = null;

    public PlayerCycleChargeGoodsUnit(PlayerActivity activity, ActivityUnitProp unitProp, ImmutableList<Integer> goodsChainIds) {
        super(activity, unitProp);
        this.goodsChainIds = goodsChainIds;
    }

    private ActivityCycleChargeGoodsUnitProp prop() {
        return unitProp.getCycleChargeGoods();
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            // 活动重新初始化的时候，可能会有上次购买的礼包卡点购买，那可能会存在礼包history没清掉的情况，需要上线的时候手动清一下
            clearGoodsHistoryInGoodsChain("init");
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        // 活动过期的时候，需要把礼包history清掉，正在购买的礼包可能还会产生新的礼包
        clearGoodsHistoryInGoodsChain("onExpire");
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerDayRefreshEvent) {
            ActivityTemplate activityTemplate = this.ownerActivity.getTemplate();
            if (activityTemplate.getCycleChargeDailyRefresh()) {
                clearGoodsHistoryInGoodsChain("dailyRefresh");
            }
        }
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    public void checkApply(ChargeGoodsTemplate goodsTemplate) {
        int goodsId = goodsTemplate.getId();

        Integer chainId = findChainId(goodsId);
        if (chainId == null) {
            throw new GeminiException(ErrorCode.ILLEGAL_GOODS_ID);
        }

        int shouldBuyGoodsId = curShouldBuyGoodsId(chainId);
        if (shouldBuyGoodsId <= 0 || goodsId != shouldBuyGoodsId) {
            throw new GeminiException(ErrorCode.ILLEGAL_GOODS_ID);
        }
    }

    public String getCurOrderToken() {
        return curOrderToken;
    }

    public void recordOrderToken(String orderToken) {
        if (curOrderToken != null) {
            LOGGER.warn("PlayerCycleChargeGoodsUnit recordOrderToken, curOrderToken is not null, curOrderToken={}, new orderToken={}", curOrderToken, orderToken);
        }
        // 无脑替换为新的订单token
        curOrderToken = orderToken;
    }

    public boolean isTokenSame(String orderToken) {
        if (curOrderToken == null) {
            // 在零点前下单，发货时跨零点，unit被重新new出来（走了过期新开活动的逻辑），此时curOrderToken就是空的，允许这个情况出现
            LOGGER.warn("PlayerCycleChargeGoodsUnit isTokenSame, curOrderToken is null, orderToken={}", orderToken);
            return false;
        }
        if (orderToken == null) {
            LOGGER.error("PlayerCycleChargeGoodsUnit isTokenSame, orderToken is null, curOrderToken={}", curOrderToken);
            return false;
        }
        return orderToken.equals(curOrderToken);
    }

    public void clearOrderToken() {
        curOrderToken = null;
    }

    @Nullable
    private Integer findChainId(int goodsId) {
        // 找到goodsId对应的chainId
        return IterableUtils.find(goodsChainIds, cid ->
                ResHolder.getTemplate(ChargeChainTemplate.class, cid).getChargeChainGoodsIdList().contains(goodsId)
        );
    }

    /**
     * @return -1 if no goods available
     */
    private int curShouldBuyGoodsId(int chainId) {
        GoodsChainInfoProp chainInfo = prop().getChainsV(chainId);
        ChargeChainTemplate chainTemplate = ResHolder.getTemplate(ChargeChainTemplate.class, chainId);
        List<Integer> chainGoodIds = chainTemplate.getChargeChainGoodsIdList();
        int shouldBuyGoodsId = chainGoodIds.get(0);
        if (chainInfo != null && chainInfo.getLastBoughtGoodsId() > 0) {
            ChargeGoodsTemplate lastBoughtGoodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, chainInfo.getLastBoughtGoodsId());
            if (chainInfo.getLastGoodsBoughtTimes() >= lastBoughtGoodsTemplate.getMaxPurchaseTimes()) {
                // 当前礼包次数买尽才可以买下一个
                int index = chainGoodIds.indexOf(chainInfo.getLastBoughtGoodsId());
                if (index >= chainGoodIds.size() - 1) {
                    return -1;
                }
                shouldBuyGoodsId = chainGoodIds.get(index + 1);
            } else {
                shouldBuyGoodsId = chainInfo.getLastBoughtGoodsId();
            }
        }
        return shouldBuyGoodsId;
    }

    public void onGoodsBought(int goodsId) {
        Integer chainId = findChainId(goodsId);
        if (chainId == null) {
            WechatLog.error("{} onGoodsBought chainId=null goodsId={}", player(), goodsId);
            return;
        }
        int shouldBuyGoodsId = curShouldBuyGoodsId(chainId);
        if (shouldBuyGoodsId != goodsId) {
            // 配置变了 or 回调跨零点礼包链重置了
            LOGGER.warn("{} PlayerCycleChargeGoodsUnit onGoodsBought shouldBuyGoodsId={} goodsId={}", player(), shouldBuyGoodsId, goodsId);
            return;
        }
        GoodsChainInfoProp chainInfo = prop().getChainsV(chainId);
        if (chainInfo == null) {
            chainInfo = prop().addEmptyChains(chainId).setChainId(chainId);
        }
        int times = chainInfo.getLastBoughtGoodsId() == goodsId ? chainInfo.getLastGoodsBoughtTimes() + 1 : 1;
        chainInfo.setLastBoughtGoodsId(goodsId)
                .setLastGoodsBoughtTimes(times);
    }

    public void recordSelectRecord(int goodsId, ActivitySelectGoodsRecordInfoProp record) {
        if (record == null) {
            LOGGER.error("PlayerCycleChargeGoodsUnit recordSelectRecord record is null unit {}", getUnitId());
            return;
        }
        if (record.getRewardInfos().isEmpty()) {
            LOGGER.error("PlayerCycleChargeGoodsUnit recordSelectRecord record is empty unit {}", getUnitId());
            return;
        }
        if (prop().getRecords().containsKey(goodsId)) {
            LOGGER.error("PlayerCycleChargeGoodsUnit recordSelectRecord record goodsId exist {} {}", getUnitId(), goodsId);
            return;
        }
        prop().getRecords().put(goodsId, record);
    }

    private void clearGoodsHistoryInGoodsChain(String reason) {
        // 把所有礼包链里的购买记录清空
        for (int chainId : goodsChainIds) {
            final ChargeChainTemplate chargeGoodsTemplate = ResHolder.getTemplate(ChargeChainTemplate.class, chainId);
            if (null == chargeGoodsTemplate) {
                LOGGER.error("ChargeChainTemplate not exist for chainId {}, when try clear buy record", chainId);
                continue;
            }
            for (int goodsId : chargeGoodsTemplate.getChargeChainGoodsIdList()) {
                player().getPaymentComponent().removeGoodsBoughtHistory(goodsId);
            }
            prop().clearChains();
        }
        prop().clearRecords();
        LOGGER.info("PlayerCycleChargeGoodsUnit clearGoodsHistoryInGoodsChain {} {} {} {} ", player(), ownerActivity.getActivityId(), getUnitId(), reason);
    }
}

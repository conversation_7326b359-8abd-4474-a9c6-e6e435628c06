package com.yorha.cnc.player.task.checker;

import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

public interface TaskChecker {

    /**
     * check关注的事件
     */
    List<String> getAttentionEvent();

    /**
     * 更新进度
     * 需要保证更新后检测任务是否完成
     *
     * @param event        任务事件，由系统触发的可以使用 NullTaskEvent
     * @param prop         任务prop载体
     * @param taskTemplate 任务配置
     * @return true:任务已经完成
     */
    boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate);
}

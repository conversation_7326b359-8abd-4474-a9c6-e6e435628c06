package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 * 增加vip经验
 */
public class AddVipExp implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(AddVipExp.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        if (!args.containsKey("count")) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int count = Integer.parseInt(args.get("count"));
        if (count == 0) {
            LOGGER.info("count is 0, do nothing");
            return;
        }
        if (count > 0) {
            actor.getEntity().getVipComponent().addVipExp(count, "gm");
        } else {
            actor.getEntity().getVipComponent().gmDecreaseVipExp(count);
        }
    }

    @Override
    public String showHelp() {
        return "addVipExp count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

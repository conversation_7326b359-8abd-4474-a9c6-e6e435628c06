package com.yorha.cnc.player.gm.command.server;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.resource.ResLoader;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 资源热加载
 *
 * <AUTHOR>
 */
public class Reload implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        ResLoader.reload();
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}


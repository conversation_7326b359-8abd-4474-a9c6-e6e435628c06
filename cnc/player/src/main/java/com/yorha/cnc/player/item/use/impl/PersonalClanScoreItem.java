package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

/**
 * 获得军团个人积分道具
 *
 * <AUTHOR>
 */
public class PersonalClanScoreItem extends AbstractUsableItem {

    public PersonalClanScoreItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
    }


    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        long count = getTemplate().getEffectValue();
        playerEntity.getPlayerClanComponent().addTotalClanScore(CommonEnum.ClanScoreCategory.CSC_ITEM, count * num);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

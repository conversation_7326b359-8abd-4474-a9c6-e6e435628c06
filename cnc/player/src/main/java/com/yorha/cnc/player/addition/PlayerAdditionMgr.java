package com.yorha.cnc.player.addition;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerEnergyComponent;
import com.yorha.cnc.player.component.PlayerResourceProduceComponent;
import com.yorha.cnc.player.enums.BuildResourceMapType;
import com.yorha.common.addition.AdditionMgrBase;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsScenePlayer;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;


/**
 * <AUTHOR>
 */
public class PlayerAdditionMgr extends AdditionMgrBase<PlayerEntity> {
    private static final Map<Integer, PlayerAdditionDispatcher> ADDITION_BEFORE_DISPATCHER = new HashMap<>();
    private static final Map<Integer, PlayerAdditionDispatcher> ADDITION_DISPATCHER = new HashMap<>();

    static {
        // 资源产出相关
        for (Integer id : BuildResourceMapType.getProduceAttentionAddition()) {
            ADDITION_BEFORE_DISPATCHER.put(id, PlayerResourceProduceComponent::onProduceRateAdditionChange);
            ADDITION_DISPATCHER.put(id, PlayerResourceProduceComponent::onNeedRefreshProduceProp);
        }
        ADDITION_BEFORE_DISPATCHER.put(CommonEnum.BuffEffectType.ET_ENERGY_RECOVER_SPEED_PERCENT_VALUE, PlayerEnergyComponent::beforeAdditionChange);

    }

    public static void dispatchBeforeAdditionChange(Map<Integer, Long> oldAdditions, PlayerEntity entity) {
        for (Map.Entry<Integer, Long> entry : oldAdditions.entrySet()) {
            int additionId = entry.getKey();
            long value = entry.getValue();
            try {
                if (ADDITION_BEFORE_DISPATCHER.containsKey(additionId)) {
                    ADDITION_BEFORE_DISPATCHER.get(additionId).dispatch(entity, additionId, value);
                }
            } catch (Exception e) {
                LOGGER.error("owner:{} dispatchBeforeAdditionChange failed. additionId: {}", entity, additionId, e);
            }
        }
    }

    public static void dispatchAdditionChange(Map<Integer, Long> newAdditions, PlayerEntity entity) {
        for (Map.Entry<Integer, Long> entry : newAdditions.entrySet()) {
            try {
                if (ADDITION_DISPATCHER.containsKey(entry.getKey())) {
                    ADDITION_DISPATCHER.get(entry.getKey()).dispatch(entity, entry.getKey(), entry.getValue());
                }
            } catch (Exception e) {
                LOGGER.error("owner:{} dispatchAdditionChange failed. additionId: {}", entity, entry.getKey(), e);
            }
        }
    }

    public PlayerAdditionMgr(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public AdditionSysProp getAdditionSys() {
        return getOwner().getProp().getAdditionSysNew();
    }

    // zone级别的加成
    public AdditionSysProp getZoneAdditionSys() {
        return getOwner().getProp().getZoneAdditionSys();
    }

    public Set<Integer> getZoneAdditionIdsBySource(CommonEnum.AdditionSourceType sourceType) {
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> map = AdditionUtil.additionSysToSourceMap(getZoneAdditionSys());
        return map.getOrDefault(sourceType, Maps.newHashMap()).keySet();
    }

    @Override
    protected void update(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            LOGGER.error("player:{}, update additions is empty. sourceType:{}", getOwner(), sourceType);
            return;
        }

        Map<Integer, Long> sceneAdditionMap = Maps.newHashMap();
        Map<Integer, Long> playerAdditionMap = Maps.newHashMap();
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            if (AdditionUtil.isSceneAddition(entry.getKey())) {
                sceneAdditionMap.put(entry.getKey(), entry.getValue());
            } else {
                playerAdditionMap.put(entry.getKey(), entry.getValue());
            }
        }
        updateAdditionToScene(sourceType, sceneAdditionMap);
        updateAdditionToPlayer(sourceType, playerAdditionMap, map -> updateAddition(sourceType, map));
    }

    private void updateAdditionToScene(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            return;
        }
        CommonMsg.UpdatePlayerAdditionCmd.Builder builder = CommonMsg.UpdatePlayerAdditionCmd.newBuilder();
        builder.setPlayerId(getOwner().getEntityId())
                .setSource(sourceType)
                .putAllAddition(additions);
        SsScenePlayer.UpdateAdditionFromPlayerCmd.Builder cmd = SsScenePlayer.UpdateAdditionFromPlayerCmd.newBuilder()
                .setCmd(builder);
        getOwner().ownerActor().tellBigScene(cmd.build());
    }

    public void updateAdditionToPlayer(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions, Consumer<Map<Integer, Long>> handle) {
        if (additions.isEmpty()) {
            return;
        }
        Map<Integer, Long> oldAdditions = new HashMap<>(additions.size());
        Map<Integer, Long> additionBySource = getAdditionBySource(sourceType);
        boolean needUpdate = false;

        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            int additionId = entry.getKey();
            oldAdditions.put(additionId, getAddition(additionId));

            long newValue = entry.getValue();
            long oldValue = additionBySource.getOrDefault(additionId, 0L);
            if (newValue != oldValue) {
                needUpdate = true;
            }
        }

        if (!needUpdate) {
            return;
        }
        doUpdate(sourceType, oldAdditions, additions, handle);
    }

    public void updateZoneAdditionToPlayer(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions, Consumer<Map<Integer, Long>> handle) {
        if (additions.isEmpty()) {
            return;
        }
        Map<Integer, Long> oldAdditions = new HashMap<>();
        for (int additionId : additions.keySet()) {
            oldAdditions.put(additionId, getAddition(additionId));
        }
        doUpdate(sourceType, oldAdditions, additions, handle);
    }

    private void doUpdate(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> oldAdditions, Map<Integer, Long> additions, Consumer<Map<Integer, Long>> handle) {
        // 加成变更之前，有些模块要先做处理。例：产量结算一下，体力恢复结算一下
        PlayerAdditionMgr.dispatchBeforeAdditionChange(oldAdditions, getOwner());

        // 更新加成
        handle.accept(additions);

        Map<Integer, Long> newAdditions = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : oldAdditions.entrySet()) {
            if (entry.getValue() != getAddition(entry.getKey())) {
                newAdditions.put(entry.getKey(), getAddition(entry.getKey()));
                LOGGER.info("{} update addition. additionId:{}, sourceType:{}, value:{} -> {}",
                        getOwner(), entry.getKey(), sourceType, entry.getValue(), getAddition(entry.getKey()));
            }
        }

        //加成变更之后，要以新的加成重新计算未来资源田产满的时间戳
        PlayerAdditionMgr.dispatchAdditionChange(newAdditions, getOwner());
    }

    @Override
    public long getAddition(int additionId) {
        long zoneAddition = getZoneAdditionSys().getAddition().getOrDefault(additionId, new AdditionProp()).getTotalValue();
        return super.getAddition(additionId) + zoneAddition;
    }
}

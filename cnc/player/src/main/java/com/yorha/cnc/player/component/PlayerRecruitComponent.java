package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.task.OpenTreasureChestsEvent;
import com.yorha.cnc.player.event.task.RecruitEpicHeroEvent;
import com.yorha.cnc.player.event.task.RecruitLeagueHeroEvent;
import com.yorha.common.asset.ItemDesc;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemRewardParam;
import com.yorha.common.resource.resservice.item.ItemRewardPool;
import com.yorha.common.resource.resservice.player.RecruitService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.PlayerRecruitModelProp;
import com.yorha.game.gen.prop.RecruitPoolProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncRecruit;
import res.template.HeroRhTemplate;
import res.template.ItemTemplate;
import res.template.RecruitRewardBoxTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.OPEN_TREASURE_CHEST_TIME;
import static com.yorha.proto.CommonEnum.HeroQuality.QUALITY_EPIC;
import static com.yorha.proto.CommonEnum.HeroQuality.QUALITY_LEGEND;
import static com.yorha.proto.CommonEnum.ItemUseType.RECRUIT_HERO;
import static com.yorha.proto.CommonEnum.Reason.ICR_RECRUIT;

/**
 * 玩家招募组件
 *
 * <AUTHOR>
 */
public class PlayerRecruitComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerRecruitComponent.class);

    public PlayerRecruitComponent(PlayerEntity owner) {
        super(owner);
    }

    private final Map<CommonEnum.RecruitPoolType, RecruitPoolProp> recruitPoolPropMap = new HashMap<>();

    @Override
    public void init() {
    }

    private PlayerRecruitModelProp prop() {
        return getOwner().getProp().getPlayerRecruitModel();
    }

    @Override
    public void onLoad(boolean isRegister) {
        recruitPoolPropMap.put(prop().getGoldRecruitPoolInfo().getRecruitPoolType(), prop().getGoldRecruitPoolInfo());
        recruitPoolPropMap.put(prop().getSilverRecruitPoolInfo().getRecruitPoolType(), prop().getSilverRecruitPoolInfo());
    }

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerRecruitComponent::dailyRefresh);
    }

    /**
     * 日刷事件
     */
    private static void dailyRefresh(PlayerDayRefreshEvent event) {
        event.getPlayer().getRecruitComponent().refreshRecord();
    }

    /**
     * 清空每日招募记录
     */
    public void refreshRecord() {
        for (RecruitPoolProp prop : recruitPoolPropMap.values()) {
            prop.setDailyRecruitCnt(0);
            prop.setFreeRecruitCnt(0);
        }
    }

    /**
     * 招募
     *
     * @param recruitPoolType
     * @param recruitType
     * @return
     */
    public PlayerCommon.Player_Recruit_S2C recruit(CommonEnum.RecruitPoolType recruitPoolType, CommonEnum.RecruitType recruitType) {
        PlayerCommon.Player_Recruit_S2C.Builder builder = PlayerCommon.Player_Recruit_S2C.newBuilder();
        switch (recruitType) {
            case RT_FREE: {
                builder.addAllRewardConfigs(freeRecruit(recruitPoolType));
                break;
            }
            case RT_SINGLE: {
                builder.addAllRewardConfigs(singleRecruit(recruitPoolType));
                break;
            }
            case RT_ALL: {
                builder.putAllItems(fullRecruit(recruitPoolType));
                break;
            }
            default: {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "recruit unsupport recruitType " + recruitType);
            }
        }
        return builder.build();
    }

    /**
     * 免费招募
     *
     * @param recruitPoolType
     * @return
     */
    private List<Integer> freeRecruit(CommonEnum.RecruitPoolType recruitPoolType) {
        RecruitPoolProp recruitPoolProp = getRecruitPoolProp(recruitPoolType);
        // 免费招募检测
        long limit = getFreeRecruitLimit(recruitPoolType);
        int recruitCnt = recruitPoolProp.getFreeRecruitCnt();
        if (recruitCnt >= limit) {
            throw new GeminiException(ErrorCode.RECRUIT_RECRUIT_FREE_LIMIT);
        }

        if (SystemClock.now() < recruitPoolProp.getNextFreeRecruitTime()) {
            throw new GeminiException(ErrorCode.RECRUIT_RECRUIT_FREE_LACK);
        }

        boolean needMustOut = needMustOut(recruitPoolProp);
        // 记录招募次数
        recruitPoolProp.setTotalRecruitCnt(recruitPoolProp.getTotalRecruitCnt() + 1);
        recruitPoolProp.setFreeRecruitCnt(recruitPoolProp.getFreeRecruitCnt() + 1);

        // 刷新下一次免费招募时间点
        recruitPoolProp.setNextFreeRecruitTime(SystemClock.now() + getFreeCd(recruitPoolType));

        // 抽取奖品
        Map<Integer, Integer> items = new HashMap<>();
        List<Integer> res = new ArrayList<>();
        final RecruitService recruitService = ResHolder.getResService(RecruitService.class);
        final ItemRewardPool guaranteePool = recruitService.getGuaranteePool(recruitPoolType, recruitPoolProp.getTotalRecruitCnt());
        final ItemRewardPool pool = recruitService.getRecruitPool(recruitPoolType);
        if (pool == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerRecruitComponent freeRecruit pool is null recruitPoolType=" + recruitPoolType + ", cnt=" + recruitPoolProp.getTotalRecruitCnt());
        }
        final boolean isGuarantee = guaranteePool != null;
        final List<ItemReward> list = new ArrayList<>();
        if (isGuarantee) {
            final List<ItemReward> guaranteeList = guaranteePool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 0));
            final List<ItemReward> normalList = pool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 1));
            list.addAll(guaranteeList);
            list.addAll(normalList);
        } else {
            final List<ItemReward> normalList = pool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 0));
            list.addAll(normalList);
        }
        int epicNum = 0;
        int leagueNum = 0;
        for (ItemReward reward : list) {
            res.add(reward.getConfigId());
            final int oldNum = items.getOrDefault(reward.getItemTemplateId(), 0);
            items.put(reward.getItemTemplateId(), oldNum + reward.getCount());
            final RecruitRewardBoxTemplate template = ResHolder.getTemplate(RecruitRewardBoxTemplate.class, reward.getConfigId());
            if (isHeroReward(QUALITY_EPIC, template)) {
                epicNum++;
            }
            if (isHeroReward(QUALITY_LEGEND, template)) {
                leagueNum++;
            }
        }
        // 发奖
        sendReward(items, recruitPoolType.toString());
        // 更新任务进度
        updateTaskProcess(recruitPoolType, 1);
        // 更新通行证任务进度
        updateBpTaskProcess(epicNum, leagueNum);
        QlogCncRecruit.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("recruit")
                .setRecruitPoolType(recruitPoolType.toString())
                .setFreeOrNot(0)
                .setParam(1)
                .setGuaranteeCnt(isGuarantee ? 1 : 0)
                .setTotalCnt(recruitPoolProp.getTotalRecruitCnt())
                .sendToQlog();
        LOGGER.debug("player:{} freeRecruit recruitPoolType:{} items:{}", getOwner(), recruitPoolType, items);
        return res;
    }

    private boolean isHeroReward(CommonEnum.HeroQuality quality, RecruitRewardBoxTemplate template) {
        final Map<Integer, ItemTemplate> itemTemplateMap = ResHolder.getInstance().getMap(ItemTemplate.class);
        final ItemTemplate itemTemplate = itemTemplateMap.get(template.getItemId());
        if (itemTemplate == null) {
            return false;
        }
        CommonEnum.ItemUseType itemUseType = CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType());
        if (itemUseType != RECRUIT_HERO) {
            return false;
        }
        int heroId = itemTemplate.getEffectValue2();
        final Map<Integer, HeroRhTemplate> heroTemplateMap = ResHolder.getInstance().getMap(HeroRhTemplate.class);
        final HeroRhTemplate heroTemplate = heroTemplateMap.get(heroId);
        if (heroTemplate == null) {
            return false;
        }
        if (heroTemplate.getRarity() != quality.getNumber()) {
            return false;
        }
        return true;
    }

    /**
     * 单次招募
     *
     * @param recruitPoolType
     * @return
     */
    private List<Integer> singleRecruit(CommonEnum.RecruitPoolType recruitPoolType) {
        RecruitPoolProp recruitPoolProp = getRecruitPoolProp(recruitPoolType);
        // 每日上限检测
        int limit = ResHolder.getResService(RecruitService.class).getDailyLimit(recruitPoolType);
        int recruitCnt = recruitPoolProp.getDailyRecruitCnt();
        if (recruitCnt >= limit) {
            throw new GeminiException(ErrorCode.RECRUIT_RECRUIT_DAILY_LIMIT);
        }
        // 道具消耗检测
        Struct.Item item = ResHolder.getResService(RecruitService.class).getCostItem(recruitPoolType);
        if (getOwner().getItemComponent().notEnough(item.getTemplateId(), item.getNum())) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        // 道具消耗
        getOwner().getItemComponent().consume(new ItemDesc(item.getTemplateId(), item.getNum()), ICR_RECRUIT, "单次招募");
        boolean needMustOut = needMustOut(recruitPoolProp);
        // 记录招募次数
        recruitPoolProp.setDailyRecruitCnt(recruitCnt + 1);
        recruitPoolProp.setTotalRecruitCnt(recruitPoolProp.getTotalRecruitCnt() + 1);
        // 抽奖
        Map<Integer, Integer> items = new HashMap<>();
        List<Integer> res = new ArrayList<>();
        final RecruitService recruitService = ResHolder.getResService(RecruitService.class);
        final ItemRewardPool guaranteePool = recruitService.getGuaranteePool(recruitPoolType, recruitPoolProp.getTotalRecruitCnt());
        final ItemRewardPool pool = recruitService.getRecruitPool(recruitPoolType);
        if (pool == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerRecruitComponent singleRecruit pool is null recruitPoolType=" + recruitPoolType + ", cnt=" + recruitPoolProp.getTotalRecruitCnt());
        }
        final boolean isGuarantee = guaranteePool != null;
        final List<ItemReward> list = new ArrayList<>();
        if (isGuarantee) {
            final List<ItemReward> guaranteeList = guaranteePool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 0));
            final List<ItemReward> normalList = pool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 1));
            list.addAll(guaranteeList);
            list.addAll(normalList);
        } else {
            final List<ItemReward> normalList = pool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 0));
            list.addAll(normalList);
        }
        int epicNum = 0;
        int leagueNum = 0;
        for (ItemReward reward : list) {
            res.add(reward.getConfigId());
            int oldNum = items.getOrDefault(reward.getItemTemplateId(), 0);
            items.put(reward.getItemTemplateId(), oldNum + reward.getCount());
            RecruitRewardBoxTemplate template = ResHolder.getTemplate(RecruitRewardBoxTemplate.class, reward.getConfigId());
            if (isHeroReward(QUALITY_EPIC, template)) {
                epicNum++;
            }
            if (isHeroReward(QUALITY_LEGEND, template)) {
                leagueNum++;
            }
        }
        // 发奖
        sendReward(items, recruitPoolType.toString());
        // 更新任务进度
        updateTaskProcess(recruitPoolType, 1);
        // 更新通行证任务进度
        updateBpTaskProcess(epicNum, leagueNum);
        QlogCncRecruit.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("recruit")
                .setRecruitPoolType(recruitPoolType.toString())
                .setFreeOrNot(1)
                .setParam(1)
                .setGuaranteeCnt(isGuarantee ? 1 : 0)
                .setTotalCnt(recruitPoolProp.getTotalRecruitCnt())
                .sendToQlog();
        LOGGER.debug("player:{} singleRecruit recruitPoolType:{} items:{}", getOwner(), recruitPoolType, items);
        return res;
    }

    /**
     * 全部招募
     *
     * @param recruitPoolType
     * @return
     */
    private Map<Integer, Integer> fullRecruit(CommonEnum.RecruitPoolType recruitPoolType) {
        RecruitPoolProp recruitPoolProp = getRecruitPoolProp(recruitPoolType);
        // 每日上限检测
        int limit = ResHolder.getResService(RecruitService.class).getDailyLimit(recruitPoolType);
        int recruitCnt = recruitPoolProp.getDailyRecruitCnt();
        if (recruitCnt >= limit) {
            throw new GeminiException(ErrorCode.RECRUIT_RECRUIT_DAILY_LIMIT);
        }

        // 计算本次全部招募 招募次数
        Struct.Item item = ResHolder.getResService(RecruitService.class).getCostItem(recruitPoolType);
        int itemNum = getOwner().getItemComponent().getItemNum(item.getTemplateId());
        if (itemNum <= 0) {
            throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
        }
        int cnt = Math.min((limit - recruitCnt), (itemNum / item.getNum()));
        if (cnt <= 0) {
            throw new GeminiException("player :{} fullRecruit unknown error", getOwner());
        }
        // 道具消耗
        getOwner().getItemComponent().consume(new ItemDesc(item.getTemplateId(), MathUtils.multiplyExact(item.getNum(), cnt)), ICR_RECRUIT, "全部招募");
        boolean needMustOut = needMustOut(recruitPoolProp);
        // 记录招募次数
        recruitPoolProp.setDailyRecruitCnt(recruitCnt + cnt);
        final int oldTotalRecruitCnt = recruitPoolProp.getTotalRecruitCnt();
        recruitPoolProp.setTotalRecruitCnt(oldTotalRecruitCnt + cnt);
        // 抽奖
        Map<Integer, Integer> res = new HashMap<>();
        int epicNum = 0;
        int leagueNum = 0;
        int guaranteeCnt = 0;
        for (int i = 1; i <= cnt; i++) {
            final RecruitService recruitService = ResHolder.getResService(RecruitService.class);
            final ItemRewardPool guaranteePool = recruitService.getGuaranteePool(recruitPoolType, oldTotalRecruitCnt + i);
            final ItemRewardPool pool = recruitService.getRecruitPool(recruitPoolType);
            if (pool == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerRecruitComponent fullRecruit pool is null recruitPoolType=" + recruitPoolType + ", cnt=" + recruitPoolProp.getTotalRecruitCnt());
            }
            final boolean isGuarantee = guaranteePool != null;
            final List<ItemReward> list = new ArrayList<>();
            if (isGuarantee) {
                final List<ItemReward> guaranteeList = guaranteePool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 0));
                final List<ItemReward> normalList = pool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 1));
                list.addAll(guaranteeList);
                list.addAll(normalList);
                guaranteeCnt++;
            } else {
                final List<ItemReward> normalList = pool.randomReward(new ItemRewardParam(needMustOut, getServerOpenDays(), 0));
                list.addAll(normalList);
            }
            for (ItemReward reward : list) {
                int oldNum = res.getOrDefault(reward.getItemTemplateId(), 0);
                res.put(reward.getItemTemplateId(), oldNum + reward.getCount());
                RecruitRewardBoxTemplate template = ResHolder.getTemplate(RecruitRewardBoxTemplate.class, reward.getConfigId());
                if (isHeroReward(QUALITY_EPIC, template)) {
                    epicNum++;
                }
                if (isHeroReward(QUALITY_LEGEND, template)) {
                    leagueNum++;
                }
            }
        }
        // 发奖
        sendReward(res, recruitPoolType.toString());
        // 更新任务进度
        updateTaskProcess(recruitPoolType, cnt);
        // 更新通行证任务进度
        updateBpTaskProcess(epicNum, leagueNum);
        QlogCncRecruit.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("recruit")
                .setRecruitPoolType(recruitPoolType.toString())
                .setFreeOrNot(1)
                .setParam(cnt)
                .setGuaranteeCnt(guaranteeCnt)
                .setTotalCnt(recruitPoolProp.getTotalRecruitCnt())
                .sendToQlog();
        recruitPoolProp.setFullRecruited(true);
        LOGGER.debug("player:{} fullRecruit cnt:{} recruitPoolType:{} items:{}", getOwner(), cnt, recruitPoolType, res);
        return res;
    }

    private long getFreeCd(CommonEnum.RecruitPoolType recruitPoolType) {
        int baseCdS = ResHolder.getResService(RecruitService.class).getFreeRefreshCdS(recruitPoolType);
        long res = 0;
        if (recruitPoolType == CommonEnum.RecruitPoolType.RPT_SILVER) {
            res = TimeUtils.second2Ms(getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_FREE_RECRUIT_SLIVER_CD_FIX));
        }
        if (recruitPoolType == CommonEnum.RecruitPoolType.RPT_GOLD) {
            res = TimeUtils.second2Ms(getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_FREE_RECRUIT_GOLD_CD_FIX));
        }
        if (res <= baseCdS) {
            throw new GeminiException("PlayerRecruitComponent getFreeCd invaild cd :{} ms", res);
        }
        return res;
    }

    private long getFreeRecruitLimit(CommonEnum.RecruitPoolType recruitPoolType) {
        long limit = ResHolder.getResService(RecruitService.class).getFreeDailyLimit(recruitPoolType);
        long res = getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_FREE_RECRUIT_LIMIT_FIX);
        if (res > limit) {
            throw new GeminiException("invaild recruitPoolType: {} RecruitLimit: {}", recruitPoolType.toString(), res);
        }
        return res;
    }

    private RecruitPoolProp getRecruitPoolProp(CommonEnum.RecruitPoolType recruitPoolType) {
        RecruitPoolProp recruitPoolProp = recruitPoolPropMap.getOrDefault(recruitPoolType, null);
        if (recruitPoolProp == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return recruitPoolProp;
    }

    private boolean needMustOut(RecruitPoolProp recruitPoolProp) {
        if (recruitPoolProp.getTotalRecruitCnt() <= 0) {
            return true;
        }
        return false;
    }

    /**
     * @return 没开服返回0
     */
    private long getServerOpenDays() {
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("PlayerRecruitComponent getServerOpenDays end server is not open={}", ZoneContext.getServerOpenTsMs());
            return 0;
        }
        return TimeUtils.getAbsNatureDaysBetween(ZoneContext.getServerOpenTsMs(), SystemClock.now());
    }

    /**
     * 发奖
     *
     * @param items
     */
    private void sendReward(Map<Integer, Integer> items, String subReason) {
        for (Map.Entry<Integer, Integer> entry : items.entrySet()) {
            getOwner().getItemComponent().addItem(entry.getKey(), entry.getValue(), ICR_RECRUIT, subReason);
        }
    }

    /**
     * 更新 打开xx宝箱xx次 任务进度
     */
    private void updateTaskProcess(CommonEnum.RecruitPoolType recruitPoolType, int num) {
        getOwner().getStatisticComponent().recordSecondStatistic(OPEN_TREASURE_CHEST_TIME, recruitPoolType.getNumber(), num);
        new OpenTreasureChestsEvent(getOwner(), recruitPoolType.getNumber(), num).dispatch();
    }

    /**
     * 更新通行证招募任务
     * 招募史诗英雄，招募传说英雄
     */
    private void updateBpTaskProcess(int epicNum, int leagueNum) {
        LOGGER.info("PlayerRecruitComponent UpdateBpTaskProcess, epicNum={}, legendNum={}", epicNum, leagueNum);
        if (epicNum > 0) {
            new RecruitEpicHeroEvent(getOwner(), epicNum).dispatch();
        }
        if (leagueNum > 0) {
            new RecruitLeagueHeroEvent(getOwner(), leagueNum).dispatch();
        }
    }

}

package com.yorha.cnc.player.activity;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actorservice.ActorTimer;

/**
 * 活动定时周期管理
 */
public class ActivityTimer {
    private final PlayerEntity player;
    private final ActorTimer timer;

    public ActivityTimer(PlayerEntity player, ActorTimer timer) {
        this.player = player;
        this.timer = timer;
    }

    public void cancel() {
        timer.cancel();
        player.getActivityComponent().removeTimer(timer);
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.ClanTerritoryLvChangeEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

public class PlayerInClanTerritoryLvChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(PlayerInClanTerritoryLvChecker.class);

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(ClanTerritoryLvChangeEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configLv = taskParams.get(0);
        // 只能是接取任务
        if (taskTemplate.getTaskCalculationMethod() != CommonEnum.TaskCalcType.TCT_RECEIVE) {
            LOGGER.error("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE));
            return prop.getProcess() >= configLv;
        }
        // 军团领地等级变化事件可能会更新进度
        if (event instanceof ClanTerritoryLvChangeEvent) {
            int lv = ((ClanTerritoryLvChangeEvent) event).getLv();
            // 当前任务进度小于变化事件进度时，更新进度
            if (prop.getProcess() < lv) {
                prop.setProcess(Math.min(lv, configLv));
            }
        }
        return prop.getProcess() >= configLv;
    }
}

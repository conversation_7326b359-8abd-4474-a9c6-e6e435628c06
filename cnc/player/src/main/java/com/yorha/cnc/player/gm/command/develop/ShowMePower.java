package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class ShowMePower implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(ShowMePower.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        // 建筑升级满级
        //actor.getEntity().getCityUpgradeComponent().GMCityUpgradeMax();
        // 指令满级
        //actor.getEntity().getCityCommandComponent().GMCityCommandMax();

        addResource(actor.getEntity());
    }

    @Override
    public String showHelp() {
        return "ShowMePower";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }

    private void addResource(PlayerEntity playerEntity) {
        playerEntity.getPurseComponent().give(CommonEnum.CurrencyType.OIL, 99999999, CommonEnum.Reason.ICR_GM, "");
        playerEntity.getPurseComponent().give(CommonEnum.CurrencyType.STEEL, 99999999, CommonEnum.Reason.ICR_GM, "");
        playerEntity.getPurseComponent().give(CommonEnum.CurrencyType.CONSUMABLES, 99999999, CommonEnum.Reason.ICR_GM, "");
        LOGGER.debug("player:{} use show me power -> all resource", playerEntity.getEntityId());
    }
}

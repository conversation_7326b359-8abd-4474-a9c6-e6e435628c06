package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.CommonEnum;

/**
 * 侦察机行动(迷雾探索、侦察、调查、回城)事件
 *
 * <AUTHOR>
 */
public class SpyPlaneActionEvent extends PlayerTaskEvent {
    private final CommonEnum.SpyPlaneActionType actionType;

    public CommonEnum.SpyPlaneActionType getActionType() {
        return actionType;
    }

    public SpyPlaneActionEvent(PlayerEntity entity, CommonEnum.SpyPlaneActionType actionType) {
        super(entity);
        this.actionType = actionType;
    }

}

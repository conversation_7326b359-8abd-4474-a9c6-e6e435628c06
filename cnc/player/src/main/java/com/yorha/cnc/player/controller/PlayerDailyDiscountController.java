package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerDailyDiscount;

@Controller(module = CommonEnum.ModuleEnum.ME_DAILY_DISCOUNT)
public class PlayerDailyDiscountController {

    /**
     * 获取每日特惠免费奖励
     */
    @CommandMapping(code = MsgType.PLAYER_GETDAILYDISCOUNTREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S msg) {
        PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.Builder response = PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.newBuilder();

        response.setRewardInfo(playerEntity.getDailyDiscountComponent().takeDailyDiscountReward().toPb());
        return response.build();
    }

    /**
     * 切换每日特惠当前选中英雄
     */
    @CommandMapping(code = MsgType.PLAYER_SWITCHDAILYDISCOUNTHERO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S msg) {
        playerEntity.getDailyDiscountComponent().switchHero(msg.getHeroId());
        return PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.getDefaultInstance();
    }

    /**
     * 获取超值每日特惠免费奖励
     */
    @CommandMapping(code = MsgType.PLAYER_GETSUPERDAILYDISCOUNTREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S msg) {
        PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.Builder response = PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.newBuilder();

        response.setRewardInfo(playerEntity.getDailyDiscountComponent().takeSuperDailyDiscountReward().toPb());
        return response.build();
    }
}


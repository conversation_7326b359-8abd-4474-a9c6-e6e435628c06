package com.yorha.cnc.player.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.battlePass.SeasonBattlePassResService;
import com.yorha.game.gen.prop.BattlePassTaskInfoProp;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.PlayerBattlePassModelProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BpTaskTemplate;
import res.template.ConstBpTemplate;
import res.template.SeasonBpScheduleTemplate;
import res.template.TaskPoolTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 赛季版通行证任务管理
 *
 * <AUTHOR>
 */
public class SeasonBattlePassTaskHandler extends AbstractTaskHandler {
    private static final Logger LOGGER = LogManager.getLogger(SeasonBattlePassTaskHandler.class);

    public SeasonBattlePassTaskHandler(PlayerEntity entity, CommonEnum.TaskClass typeClass) {
        super(entity, typeClass.name());
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return getEntity().getProp().getTaskSystem().getTaskBattlePass();
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(BpTaskTemplate.class, configId).getTaskId();
    }

    @Override
    public void loadAllTask() {
        PlayerBattlePassModelProp prop = getEntity().getProp().getSeasonBattlePassModel();
        int bpConfigId = prop.getBpConfigId();
        SeasonBpScheduleTemplate template = ResHolder.getTemplate(SeasonBpScheduleTemplate.class, bpConfigId);
        Set<Integer> reloadTasks = new HashSet<>();
        for (Map.Entry<Integer, BattlePassTaskInfoProp> entry : prop.getTasks().entrySet()) {
            List<Integer> bpTasks = ResHolder.getResService(SeasonBattlePassResService.class).getMissionGroup(template.getId(), entry.getKey());
            if (bpTasks.size() != entry.getValue().getGroupTasksSize()) {
                LOGGER.error("SeasonBattlePassTaskHandler loadAllTasks, config group task num invalid, groupId={}, old bpTasks={}, config bpTasks={}", entry.getKey(), entry.getValue().getGroupTasks(), bpTasks);
                continue;
            }
            for (int i = 0; i < bpTasks.size(); i++) {
                TaskInfoProp taskInfoProp = getTaskProp().get(entry.getValue().getGroupTasksIndex(i));
                if (taskInfoProp == null) {
                    LOGGER.error("SeasonBattlePassTaskHandler loadAllTask, taskInfoProp not exist, missionGroupId={}, index={}, taskPoolId={}", entry.getKey(), i, entry.getValue().getGroupTasksIndex(i));
                    continue;
                }
                if (!bpTasks.get(i).equals(taskInfoProp.getId())) {
                    // 发现任务变更，检查任务状态，已完成的任务忽略，未完成的任务替换
                    if (isCompleted(taskInfoProp)) {
                        continue;
                    }
                    // 移除旧任务，添加新任务id
                    getTaskProp().remove(taskInfoProp.getId());
                    reloadTasks.add(bpTasks.get(i));
                    entry.getValue().setGroupTasksIndex(i, bpTasks.get(i));
                } else {
                    if (isUnCompleted(taskInfoProp)) {
                        addTaskEventMonitor(taskInfoProp);
                    }
                }
            }
        }
        if (!reloadTasks.isEmpty()) {
            LOGGER.info("SeasonBattlePassTaskHandler loadAllTask, reloadTasks={}", reloadTasks);
            addBatchTask(reloadTasks);
        }
    }

    /**
     * 领取任务经验，无奖励
     */
    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> bpTaskIds) {
        LOGGER.info("SeasonBattlePassTaskHandler checkOrTakeReward, try take task exp, bpTaskIds={}", bpTaskIds);
        for (Integer configId : bpTaskIds) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(getTaskProp(), configId);
            if (isUnCompletedState(taskInfoProp)) {
                LOGGER.info("SeasonBattlePassTaskHandler checkOrTakeReward, task not completed, bpTaskId={}", configId);
                continue;
            }
            setNextTaskState(taskInfoProp);
            // 领取任务经验，进阶黄金bp周任务积分额外提升50%
            PlayerBattlePassModelProp prop = getEntity().getProp().getSeasonBattlePassModel();
            BpTaskTemplate template = ResHolder.getTemplate(BpTaskTemplate.class, configId);
            if (template == null) {
                LOGGER.error("SeasonBattlePassTaskHandler checkOrTakeReward, can not find bpTaskTemplate, bpTaskId={}", configId);
                continue;
            }
            int extraExpRate = ResHolder.getConsts(ConstBpTemplate.class).getBPGoldProExtraExpRate();
            int taskExp = template.getIntegral();
            if (prop.getBpType() == CommonEnum.BattltPassType.BPT_GOLD_PRO && !isSeasonTask(configId)) {
                // 进阶黄金bp获取经验值增加50%
                taskExp += taskExp * extraExpRate / GameLogicConstants.PERCENT_CONVERSION_UNITS;
            }
            LOGGER.info("SeasonBattlePassTaskHandler checkOrTakeReward, success take task exp, bpTaskId={}, bpType={}, taskExp={}", configId, prop.getBpType(), taskExp);
            getEntity().getSeasonBattlePassComponent().addExp(taskExp, "Complete_Task", configId);
            takeRewardQLog(taskInfoProp);
        }
        return null;
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    public void onBattlePassEnd() {
        this.getTaskProp().clear();
        this.clearAllAttention("battlePass end");
    }

    public boolean isSeasonTask(int bpTaskId) {
        PlayerBattlePassModelProp prop = getEntity().getProp().getSeasonBattlePassModel();
        if (!prop.getTasks().containsKey(0)) {
            return false;
        }
        return prop.getTasks().get(0).getGroupTasks().contains(bpTaskId);
    }
}

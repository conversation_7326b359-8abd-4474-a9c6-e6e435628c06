package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanLogRecordModelProp;
import com.yorha.game.gen.prop.ClanResourcesProp;
import com.yorha.game.gen.prop.ClanStageModelProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import static com.yorha.proto.CommonEnum.Reason.ICR_CLAN_CHANGE_NAME;

/**
 * 军团属性模块: 使用到ss_clan_attr里ss协议的cs协议，可以放到这里~
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_BASE)
public class PlayerClanAttrController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanAttrController.class);

    /**
     * 查询军团名字是否重复  含 简称、全称
     */
    @CommandMapping(code = MsgType.PLAYER_CHECKCLANNAME_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CheckClanName_C2S msg) {
        CommonEnum.NameType nameType = CommonEnum.NameType.forNumber(msg.getNameType());
        if (nameType == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ErrorCode code = NameHelper.checkNameRepeat(playerEntity.ownerActor(), nameType, msg.getName());
        PlayerClan.Player_CheckClanName_S2C.Builder builder = PlayerClan.Player_CheckClanName_S2C.newBuilder();
        builder.setIsRepeat(code.isNotOk());
        return builder.build();
    }

    /**
     * 修改军团名
     */
    @CommandMapping(code = MsgType.PLAYER_MODIFYCLANNAME_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ModifyClanName_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }

        // 检查联盟改名静默期
        playerEntity.ownerActor().callCurClan(SsClanAttr.CheckModifyNameQuietTimeAsk.getDefaultInstance());

        final String newName = msg.getNewName();
        // 合法，判重
        playerEntity.getPlayerClanComponent().checkClanName(newName);
        // 检查权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_EDIT_CONFIG, staffId);
        // 扣除资源
        ConstClanTemplate constClanTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        int moneyType = constClanTemplate.getClanCreateMoneyType();
        int cost = constClanTemplate.getChangeClanName();
        playerEntity.getPlayerClanComponent().consumeForCreateOrModifyClanInfo(moneyType, cost, ICR_CLAN_CHANGE_NAME);
        LOGGER.info("consume {} {} to modify clan name", moneyType, cost);

        // 占用新名字
        ErrorCode errorCode = NameHelper.occupyName(playerEntity.ownerActor(), CommonEnum.NameType.CLAN_NAME, newName, clanId);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }

        // call到clan上改名
        SsClanAttr.ModifyClanNameAsk.Builder ask = SsClanAttr.ModifyClanNameAsk.newBuilder().setCallerPlayerId(playerEntity.getPlayerId()).setName(newName);

        try {
            SsClanAttr.ModifyClanNameAns ans = playerEntity.ownerActor().callCurClan(ask.build());
            // 释放旧名字
            NameHelper.releaseName(playerEntity.ownerActor(), CommonEnum.NameType.CLAN_NAME, ans.getOldName());
            return PlayerClan.Player_ModifyClanName_S2C.newBuilder().build();
        } catch (Exception e) {
            LOGGER.debug("modify fail cause by logic");
            return PlayerClan.Player_ModifyClanName_S2C.getDefaultInstance();
        }
    }

    /**
     * 修改军团简称
     */
    @CommandMapping(code = MsgType.PLAYER_MODIFYCLANSNAME_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ModifyClanSName_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }

        // 检查联盟改名静默期
        playerEntity.ownerActor().callCurClan(SsClanAttr.CheckModifySNameQuietTimeAsk.getDefaultInstance());

        final String newSimpleName = msg.getNewSName();
        // 合法，判重
        playerEntity.getPlayerClanComponent().checkClanSimpleName(newSimpleName);
        // 检查权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_EDIT_CONFIG, staffId);
        // 扣除资源
        ConstClanTemplate constClanTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        int moneyType = constClanTemplate.getClanCreateMoneyType();
        int cost = constClanTemplate.getChangeClanSimpleName();
        playerEntity.getPlayerClanComponent().consumeForCreateOrModifyClanInfo(moneyType, cost, ICR_CLAN_CHANGE_NAME);
        LOGGER.info("consume {} {} to modify clan simple name", moneyType, cost);

        // 占用新简称名字
        ErrorCode errorCode = NameHelper.occupyName(playerEntity.ownerActor(), CommonEnum.NameType.CLAN_SIMPLE_NAME, newSimpleName, clanId);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        // call到clan上改名
        SsClanAttr.ModifyClanSimpleNameAsk ask = SsClanAttr.ModifyClanSimpleNameAsk.newBuilder()
                .setCallerPlayerId(playerEntity.getEntityId())
                .setSimpleName(newSimpleName)
                .build();
        SsClanAttr.ModifyClanSimpleNameAns ans = playerEntity.ownerActor().callCurClan(ask);

        // 释放旧名字
        NameHelper.releaseName(playerEntity.ownerActor(), CommonEnum.NameType.CLAN_SIMPLE_NAME, ans.getOldName());
        return PlayerClan.Player_ModifyClanSName_S2C.newBuilder().build();
    }

    /**
     * 修改军团旗帜
     */
    @CommandMapping(code = MsgType.PLAYER_MODIFYCLANFLAG_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ModifyClanFlag_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }
        final int flagSign = msg.getFlagSign();
        final int flagColor = msg.getFlagColor();
        final int flagShading = msg.getFlagShading();
        final int territoryColor = msg.getTerritoryColor();
        final int nationFlagId = msg.getNationFlagId();
        // 检查权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_EDIT_CONFIG, staffId);
        // 检查旗帜是否真的变化了，不变化不扣
        SsClanAttr.CheckClanFlagAndTerritoryColorAsk checkAsk = SsClanAttr.CheckClanFlagAndTerritoryColorAsk.newBuilder()
                .setFlagSign(flagSign)
                .setFlagColor(flagColor)
                .setFlagShading(flagShading)
                .setTerritoryColor(territoryColor)
                .setNationFlagId(nationFlagId)
                .build();
        // 平凡的check佐以平凡的call，最后成就不平凡的逻辑
        playerEntity.ownerActor().callCurClan(checkAsk);
        // 扣除资源
        ConstClanTemplate constClanTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        int moneyType = constClanTemplate.getClanCreateMoneyType();
        int cost = constClanTemplate.getChangeClanFlag();
        playerEntity.getPlayerClanComponent().consumeForCreateOrModifyClanInfo(moneyType, cost, CommonEnum.Reason.ICR_NONE);
        LOGGER.info("consume {} {} to modify clan flag", moneyType, cost);

        SsClanAttr.ModifyClanFlagAndTerritoryColorAsk ask = SsClanAttr.ModifyClanFlagAndTerritoryColorAsk.newBuilder()
                .setCallerPlayerId(playerEntity.getEntityId())
                .setFlagSign(flagSign)
                .setFlagColor(flagColor)
                .setFlagShading(flagShading)
                .setTerritoryColor(territoryColor)
                .setNationFlagId(nationFlagId)
                .build();
        try {
            // 返回什么对我都不重要，但过程对我有点重要
            playerEntity.ownerActor().callCurClan(ask);
        } catch (Exception e) {
            // 修改旗帜失败，wechat告警，特别注意：此时资源已经扣除了
            WechatLog.error("modify flag failed, maybe need manual resource return: msg is {}, exception is {}", msg, e);
            throw e;
        }
        return PlayerClan.Player_ModifyClanFlag_C2S.getDefaultInstance();
    }

    /**
     * 拉取欢迎邮件
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANWELCOMELETTER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanWelcomeLetter_C2S msg) {
        SsClanBase.FetchClanWelcomeLetterAsk ask = SsClanBase.FetchClanWelcomeLetterAsk.newBuilder().build();
        SsClanBase.FetchClanWelcomeLetterAns ans = playerEntity.ownerActor().callCurClan(ask);
        return PlayerClan.Player_FetchClanWelcomeLetter_S2C.newBuilder().setLetter(ans.getLetter()).build();

    }

    /**
     * 修改军团欢迎邮件
     */
    @CommandMapping(code = MsgType.PLAYER_MODIFYCLANWELCOMELETTER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ModifyClanWelcomeLetter_C2S msg) {
        final String newWelcomeLetter = msg.getLetter();
        SsClanAttr.ModifyClanWelcomeLetterAsk ask = SsClanAttr.ModifyClanWelcomeLetterAsk.newBuilder()
                .setCallerPlayerId(playerEntity.getEntityId())
                .setLetter(newWelcomeLetter)
                .build();
        playerEntity.ownerActor().callCurClan(ask);
        return PlayerClan.Player_ModifyClanWelcomeLetter_S2C.newBuilder().build();
    }

    /**
     * 修改军团设置杂项
     */
    @CommandMapping(code = MsgType.PLAYER_MODIFYCLANSETTINGMISC_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ModifyClanSettingMisc_C2S msg) {
        // 军团检查
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 参数检查
        if (!msg.hasLanguage() && !msg.hasNewRequire() && !msg.hasNewDescribe()) {
            // 可你却总是笑我，一无所有
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 权限检查
        if (!playerEntity.getPlayerClanComponent().isClanOwner()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        SsClanAttr.ModifyClanSettingMiscAsk.Builder ask = SsClanAttr.ModifyClanSettingMiscAsk.newBuilder();
        if (msg.hasNewDescribe()) {
            playerEntity.getPlayerClanComponent().checkClanDescribe(msg.getNewDescribe());
            ask.setDescribe(msg.getNewDescribe());
        }
        if (msg.hasLanguage()) {
            ask.setNewLanguage(msg.getLanguage());
        }
        if (msg.hasNewRequire()) {
            ask.setRequire(msg.getNewRequire());
        }
        // 渣男给你打电话是不关注你回什么的
        playerEntity.ownerActor().callCurClan(ask.build());
        return PlayerClan.Player_ModifyClanSettingMisc_S2C.getDefaultInstance();
    }

    /**
     * 领取军团势力奖励
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINCLANPOWERREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ObtainClanPowerReward_C2S msg) {
        PlayerClan.Player_ObtainClanPowerReward_S2C.Builder builder = playerEntity.getPlayerClanComponent().obtainClanPowerReward();
        return builder.build();
    }

    /**
     * 拉取军团仓库信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANWAREHOUSEINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanWareHouseInfo_C2S msg) {
        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }

        // call clan获取数据
        SsClanAttr.FetchClanWareHouseInfoAsk.Builder builder = SsClanAttr.FetchClanWareHouseInfoAsk.newBuilder();
        SsClanAttr.FetchClanWareHouseInfoAns ans = playerEntity.ownerActor().callCurClan(builder.build());

        // 设置回包
        ClanResourcesProp prop = new ClanResourcesProp();
        prop.mergeFromSs(ans.getResource());
        PlayerClan.Player_FetchClanWareHouseInfo_S2C.Builder retBuilder = PlayerClan.Player_FetchClanWareHouseInfo_S2C.newBuilder();
        retBuilder.setResources(prop.getCopyCsBuilder());
        return retBuilder.build();
    }

    /**
     * 拉取军团日志
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANEVENTLOG_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanEventLog_C2S msg) {
        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }

        // call clan获取数据
        SsClanAttr.FetchClanLogAsk.Builder builder = SsClanAttr.FetchClanLogAsk.newBuilder();
        SsClanAttr.FetchClanLogAns ans = playerEntity.ownerActor().callCurClan(builder.build());

        // 设置回包
        ClanLogRecordModelProp prop = new ClanLogRecordModelProp();
        prop.mergeFromSs(ans.getLogRecords());
        PlayerClan.Player_FetchClanEventLog_S2C.Builder retBuilder = PlayerClan.Player_FetchClanEventLog_S2C.newBuilder();
        retBuilder.setRecords(prop.getCopyCsBuilder());
        return retBuilder.build();
    }

    /**
     * 解散军团时拉取解散信息
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHDISSOLVINGINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchDissolvingInfo_C2S msg) {
        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId == 0L) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        SsClanAttr.FetchDissolvingInfoAsk.Builder builder = SsClanAttr.FetchDissolvingInfoAsk.newBuilder();
        SsClanAttr.FetchDissolvingInfoAns ans = playerEntity.ownerActor().callCurClan(builder.build());
        ClanStageModelProp prop = new ClanStageModelProp();
        prop.mergeFromSs(ans.getInfo());
        PlayerClan.Player_FetchDissolvingInfo_S2C.Builder retBuilder = PlayerClan.Player_FetchDissolvingInfo_S2C.newBuilder();
        retBuilder.setStageInfo(prop.getCopyCsBuilder());
        return retBuilder.build();
    }

    /**
     * 拉取军团资源快照
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANRESOURCESNAPSHOT_C2S)
    public PlayerClan.Player_FetchClanResourceSnapshot_S2C handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanResourceSnapshot_C2S msg) {
        SsClanAttr.FetchResourcesSnapshotAsk.Builder ask = SsClanAttr.FetchResourcesSnapshotAsk.newBuilder();
        PlayerActor playerActor = playerEntity.ownerActor();
        SsClanAttr.FetchResourcesSnapshotAns ans = playerActor.callCurClan(ask.build());

        PlayerClan.Player_FetchClanResourceSnapshot_S2C.Builder ret = PlayerClan.Player_FetchClanResourceSnapshot_S2C.newBuilder();
        ClanResourcesProp prop = new ClanResourcesProp();
        prop.mergeFromSs(ans.getResources());
        ret.setResources(prop.getCopyCsBuilder().build());
        return ret.build();
    }

    /**
     * 拉取军团最大人数上限tips
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANNUMMAXTIP_C2S)
    public PlayerClan.Player_FetchClanNumMaxTip_S2C handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanNumMaxTip_C2S msg) {
        // 是否在军团检查
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        SsClanAttr.FetchClanNumTipAns ans = playerEntity.ownerActor().callCurClan(SsClanAttr.FetchClanNumTipAsk.getDefaultInstance());
        PlayerClan.Player_FetchClanNumMaxTip_S2C.Builder ret = PlayerClan.Player_FetchClanNumMaxTip_S2C.newBuilder();
        ret.putAllNumMaxAdditionItem(ans.getNumMaxAdditionItemMap());
        return ret.build();
    }

    @CommandMapping(code = MsgType.PLAYER_FETCHCLANCOMMANDCENTERNUM_C2S)
    public PlayerClan.Player_ClanCommandCenterNum_S2C handle(PlayerEntity playerEntity, PlayerClan.Player_ClanCommandCenterNum_C2S msg) {
        // 是否在军团检查
        if (!playerEntity.getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        final SsSceneClan.GetClanCommandCenterNumAsk.Builder getClanCommandCenterNumAsk = SsSceneClan.GetClanCommandCenterNumAsk.newBuilder();
        getClanCommandCenterNumAsk.addClanIds(playerEntity.getClanId());
        SsSceneClan.GetClanCommandCenterNumAns ans = playerEntity.ownerActor().callBigScene(getClanCommandCenterNumAsk.build());
        PlayerClan.Player_ClanCommandCenterNum_S2C.Builder ret = PlayerClan.Player_ClanCommandCenterNum_S2C.newBuilder();
        ret.setNum(ans.getCenterNumsMap().getOrDefault(playerEntity.getClanId(), 0));
        return ret.build();
    }
}

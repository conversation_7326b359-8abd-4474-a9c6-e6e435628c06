package com.yorha.cnc.player.gm.command.server;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class SetIgnoreCrossingOwner implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        boolean isIgnoreCrossingOwner = true;
        if (args.containsKey("isIgnore")) {
            isIgnoreCrossingOwner = Integer.parseInt(args.get("isIgnore")) == 1;
        }
        ServerContext.getServerDebugOption().setIgnoreCrossingOwner(isIgnoreCrossingOwner);
    }

    @Override
    public String showHelp() {
        return "SetIgnoreCrossingOwner isIgnore={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }

}

package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityMonsterTemplate;

import java.util.List;

/**
 * 打野活动
 *
 * <AUTHOR>
 */
public class PlayerFightMonsterUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerFightMonsterUnit.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(PlayerKillBigSceneMonsterEvent.class);

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_FIGHT_MONSTER, (owner, prop, template) ->
                new PlayerFightMonsterUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerFightMonsterUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
    }

    @Override
    public void forceOffImpl() {
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    public void callBackKillMonster() {
        ActivityMonsterTemplate activityMonsterTemplate = ResHolder.getTemplate(ActivityMonsterTemplate.class, ownerActivity.getActivityId());
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(activityMonsterTemplate.getMailId());
        LOGGER.info("PlayerFightMonsterUnit playerId:{} send mail:{}", player().getPlayerId(), activityMonsterTemplate.getMailId());
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player().getPlayerId())
                .setZoneId(player().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, params.build());
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {

    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerKillBigSceneMonsterEvent) {
            if (((PlayerKillBigSceneMonsterEvent) event).getMonsterCategory() == CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE_VALUE) {
                callBackKillMonster();
            }
        }
    }
}

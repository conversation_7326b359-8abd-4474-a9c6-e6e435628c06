package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityTriggerPoolTemplate;
import res.template.FestivalBpRateTemplate;
import res.template.RateAttenuationCoefficientTemplate;

import java.util.Map;

/**
 * 节日活动unit
 *
 * <AUTHOR>
 */
public class PlayerFestivalTriggerUnit extends PlayerTriggerUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerFestivalTriggerUnit.class);

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_FESTIVAL_TRIGGER, (owner, prop, template) ->
                new PlayerFestivalTriggerUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerFestivalTriggerUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    protected void triggerSatisfiedImpl(int triggerId, PlayerEvent event) {
        final int activityId = ownerActivity.getActivityId();
        LOGGER.info("PlayerFestivalTriggerUnit triggerSatisfiedImpl {} {}", activityId, triggerId);
        final FestivalBpRateTemplate rateTemplate = ResHolder.getTemplate(FestivalBpRateTemplate.class, activityId);
        int rate = rateTemplate.getRate();
        ActivityTriggerPoolTemplate triggerPoolTemplate = ResHolder.getTemplate(ActivityTriggerPoolTemplate.class, triggerId);
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        // 击杀大世界活跃野怪有衰减系数
        if (triggerPoolTemplate.getTriggerType() == CommonEnum.ActivityUnitTriggerType.AUTT_KILL_MONSTER) {
            if (params.get(CommonEnum.ActTriggerParamType.ATPT_MONSTER_CATEGORY) == CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE_VALUE) {
                rate = rate * getCoefficient(unitProp.getFestivalUnit().getDropItemNum()) / GameLogicConstants.IPPM;
                LOGGER.info("PlayerFestivalTriggerUnit triggerSatisfiedImpl, activityId={}, original rate={}, calc rate={}", activityId, rateTemplate.getRate(), rate);
            }
        }
        if (RandomUtils.nextInt(GameLogicConstants.IPPM) < rate) {
            unitProp.getFestivalUnit().setDropItemNum(unitProp.getFestivalUnit().getDropItemNum() + 1);
            sendMailReward(rateTemplate.getRewardMail());
            LOGGER.info("PlayerFestivalTriggerUnit triggerSatisfiedImpl, send reward, {} {}", activityId, triggerId);
        }
    }

    private void sendMailReward(int mailId) {
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(mailId);
        params.getSenderBuilder().setSenderId(0);
        this.player().getMailComponent().sendPersonalMail(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(player().getPlayerId())
                        .setZoneId(player().getZoneId())
                        .build(),
                params.build());
    }

    private int getCoefficient(int num) {
        int coefficient = GameLogicConstants.IPPM;
        for (RateAttenuationCoefficientTemplate template : ResHolder.getInstance().getListFromMap(RateAttenuationCoefficientTemplate.class)) {
            if (num > template.getThreshold() && coefficient > template.getCoefficient()) {
                coefficient = template.getCoefficient();
            }
        }
        return coefficient;
    }
}


package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerGetClanGiftEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.clan.ClanGiftHelper;
import com.yorha.common.clan.ClanGiftUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.Nullable;
import res.template.ClanGiftTemplate;
import res.template.ClanTreasureTemplate;
import res.template.ConstClanTemplate;

import java.util.List;

public class PlayerClanGiftComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanGiftComponent.class);

    private PlayerClanGiftHelper clanGiftHelper = null;

    public PlayerClanGiftComponent(PlayerEntity owner) {
        super(owner);

    }

    @Override
    public void init() {
        if (this.clanGiftHelper == null) {
            this.clanGiftHelper = new PlayerClanGiftHelper(this.getProp().getGiftItems(), this.getOwner().getQlogComponent());
        }
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            this.setCurGiftIndex(-1);
        }
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns loginAns) {
        this.updateClanGiftStatus();
    }

    public ClanInfoProp getProp() {
        return getOwner().getProp().getClan();
    }


    /**
     * 添加军团礼物
     *
     * @param giftItem 礼物数据
     */
    public void addClanGift(StructClan.ClanGiftItem giftItem) {
        LOGGER.debug("PlayerClanGiftComponent addClanGift gift={}", giftItem);
        if (this.getOwner().getClanId() == 0) {
            LOGGER.warn("PlayerClanGiftComponent addClanGift player not in clan, skip");
            return;
        }
        final long giftIndex = giftItem.getGiftUniqueId();
        if (giftIndex < this.getCurGiftIndex()) {
            LOGGER.warn("PlayerClanGiftComponent addClanGift gift={}, curIndex={}", giftItem, this.getCurGiftIndex());
            return;
        }
        ClanGiftItemProp prop = new ClanGiftItemProp();
        prop.mergeChangeFromSs(giftItem);
        clanGiftHelper.addClanGift(prop);
        this.setCurGiftIndex(prop.getGiftUniqueId() + 1);
    }

    /**
     * 获取玩家军团礼物index
     *
     * @return 军团礼物index
     */
    public long getCurGiftIndex() {
        return this.getProp().getCurClanGiftIndex();
    }

    /**
     * 设置玩家军团礼物index
     *
     * @param giftIndex 军团礼物index
     */
    public void setCurGiftIndex(long giftIndex) {
        LOGGER.info("PlayerClanGiftComponent setCurGiftIndex origin={}, new={}", this.getCurGiftIndex(), giftIndex);
        this.getProp().setCurClanGiftIndex(giftIndex);
    }

    /**
     * 更新联盟礼物状态（是否过期，是否需要删除）
     */
    private void updateClanGiftStatus() {
        LOGGER.debug("PlayerClanGiftComponent updateClanGiftStatus");
        this.clanGiftHelper.removeNeedDeleteClanGifts();
    }

    /**
     * 添加珍藏礼物
     *
     * @param treasureGiftItem 珍藏礼物数据
     */
    public void addTreasureGift(StructClan.ClanTreasureGiftItem treasureGiftItem) {
        LOGGER.debug("PlayerClanGiftComponent addTreasureGift treasureGift={}", treasureGiftItem);
        if (this.getOwner().getClanId() == 0) {
            LOGGER.warn("PlayerClanGiftComponent addTreasureGift player not in clan, skip");
            return;
        }
        ClanTreasureGiftItemProp prop = new ClanTreasureGiftItemProp();
        prop.mergeChangeFromSs(treasureGiftItem);
        getProp().addTreasureGiftItems(prop);
    }

    /**
     * 领取所有普通军团礼物
     *
     * @return List<StructMsg.ClanGiftReward>
     */
    public PlayerClan.Player_GetClanGift_S2C.Builder takeAllNormalRarityGifts() {
        updateClanGiftStatus();
        int sumPoint = 0;
        int sumKey = 0;
        AssetPackage.Builder assetPackage = AssetPackage.builder();
        int successTakeCnt = 0;
        for (ClanGiftItemProp giftItem : this.getProp().getGiftItems()) {
            // 已领取跳过
            if (ClanGiftUtils.isGiftTaken(giftItem)) {
                continue;
            }
            // 已过期跳过
            if (ClanGiftUtils.isGiftExpired(giftItem)) {
                updateGiftExpiredStatus(giftItem);
                continue;
            }

            ClanGiftTemplate giftTemplate = ResHolder.getTemplate(ClanGiftTemplate.class, giftItem.getGiftId());
            LOGGER.debug("PlayerClanGiftComponent takeAllTargetRarityGifts giftTemplate={}", giftTemplate);
            if (giftTemplate.getType() != CommonEnum.ClanGiftRarityType.CLANGRT_NORMAML) {
                continue;
            }
            giftItem.setStatus(CommonEnum.ClanGiftStatus.CGS_TAKEN);
            List<ItemReward> rewards = getOwner().getItemComponent().sendReward(giftTemplate.getRewardId(), CommonEnum.Reason.ICR_CLAN_GIFT, String.valueOf(giftTemplate.getGiftID()));

            for (ItemReward reward : rewards) {
                ItemPairProp rewardRes = new ItemPairProp();
                rewardRes.setItemTemplateId(reward.getItemTemplateId())
                        .setCount(reward.getCount());
                giftItem.getGiftRecord().addItemReward(rewardRes);
                assetPackage.plusItem(reward.getItemTemplateId(), reward.getCount());
            }
            getOwner().getQlogComponent().sendTakeClanGiftQLog(giftTemplate.getGiftID());
            sumPoint += giftTemplate.getPoint();
            sumKey += giftTemplate.getKey();
            // 成功领取
            ++successTakeCnt;
        }
        if (assetPackage.isEmpty()) {
            throw new GeminiException(ErrorCode.CLAN_GIFT_NO_TAKEALL_GIFT);
        }
        PlayerClan.Player_GetClanGift_S2C.Builder response = PlayerClan.Player_GetClanGift_S2C.newBuilder();
        response.setClanGiftInfo(addClanGiftPointAndKey(sumPoint, sumKey).getClanGiftInfo());
        assetPackage.plusItem(ResHolder.getConsts(ConstClanTemplate.class).getKeyPointsID(), sumKey);
        assetPackage.plusItem(ResHolder.getConsts(ConstClanTemplate.class).getGiftPointsID(), sumPoint);
        response.setGiftReward(assetPackage.build().toPb());

        if (successTakeCnt > 0) {
            new PlayerGetClanGiftEvent(getOwner(), successTakeCnt).dispatch();
        }

        return response;
    }

    /**
     * 领取单个军团礼物
     *
     * @param giftUniqueId 军团礼物id
     * @return StructMsg.ClanGiftReward
     */
    public PlayerClan.Player_GetClanGift_S2C.Builder takeSingleGift(long giftUniqueId) {
        updateClanGiftStatus();
        ClanGiftItemProp giftItem = findGiftItem(giftUniqueId);
        if (giftItem == null) {
            throw new GeminiException(ErrorCode.CLAN_GIFT_ITEM_NOT_EXIST);
        }
        if (ClanGiftUtils.isGiftTaken(giftItem)) {
            throw new GeminiException(ErrorCode.CLAN_REWARD_ALREADY_TAKEN);
        }
        if (ClanGiftUtils.isGiftExpired(giftItem)) {
            updateGiftExpiredStatus(giftItem);
            throw new GeminiException(ErrorCode.CLAN_GIFT_EXPIRED);
        }

        ClanGiftTemplate giftTemplate = ResHolder.getTemplate(ClanGiftTemplate.class, giftItem.getGiftId());
        giftItem.setStatus(CommonEnum.ClanGiftStatus.CGS_TAKEN);
        List<ItemReward> rewards = getOwner().getItemComponent().sendReward(giftTemplate.getRewardId(), CommonEnum.Reason.ICR_CLAN_GIFT, String.valueOf(giftTemplate.getGiftID()));
        AssetPackage.Builder assetPackage = AssetPackage.builder();
        // 更新礼物对应产出
        for (ItemReward reward : rewards) {
            ItemPairProp rewardRes = new ItemPairProp();
            rewardRes.setItemTemplateId(reward.getItemTemplateId())
                    .setCount(reward.getCount());
            giftItem.getGiftRecord().addItemReward(rewardRes);
            assetPackage.plusItem(reward.getItemTemplateId(), reward.getCount());
        }
        getOwner().getQlogComponent().sendTakeClanGiftQLog(giftTemplate.getGiftID());

        PlayerClan.Player_GetClanGift_S2C.Builder response = PlayerClan.Player_GetClanGift_S2C.newBuilder();
        response.setClanGiftInfo(addClanGiftPointAndKey(giftTemplate.getPoint(), giftTemplate.getKey()).getClanGiftInfo());
        assetPackage.plusItem(ResHolder.getConsts(ConstClanTemplate.class).getKeyPointsID(), giftTemplate.getKey());
        assetPackage.plusItem(ResHolder.getConsts(ConstClanTemplate.class).getGiftPointsID(), giftTemplate.getPoint());

        response.setGiftReward(assetPackage.build().toPb());

        new PlayerGetClanGiftEvent(getOwner(), 1).dispatch();

        return response;
    }

    /**
     * 军团礼物更新过期状态（未领取&到过期时间）
     *
     * @param giftItem 军团礼物prop
     */
    private void updateGiftExpiredStatus(ClanGiftItemProp giftItem) {
        this.clanGiftHelper.updateGiftExpiredStatus(giftItem);
    }

    /**
     * 根据礼物id寻找礼物
     *
     * @param uniqueGiftId 礼物id
     * @return ClanGiftItemProp || null
     */
    @Nullable
    public ClanGiftItemProp findGiftItem(long uniqueGiftId) {
        for (ClanGiftItemProp giftItem : getProp().getGiftItems()) {
            if (giftItem.getGiftUniqueId() == uniqueGiftId) {
                return giftItem;
            }
        }
        return null;
    }

    /**
     * 清理已领取 or 已过期普通军团礼物
     */
    public void clearTakenOrExpiredNormalGifts(CommonEnum.ClanGiftRarityType rarityType) {
        this.clanGiftHelper.clearTakenOrExpiredGiftsImpl(rarityType);
    }

    /**
     * 根据珍藏礼物id寻找珍藏礼物
     *
     * @param treasureId 珍藏礼物id
     * @return ClanTreasureGiftItemProp || null
     */
    @Nullable
    public ClanTreasureGiftItemProp findTreasureGiftItem(long treasureId) {
        for (ClanTreasureGiftItemProp treasureGiftItem : getProp().getTreasureGiftItems()) {
            if (treasureGiftItem.getTreasureGiftUniqueId() == treasureId) {
                return treasureGiftItem;
            }
        }
        return null;
    }

    /**
     * 领取军团珍藏礼物
     *
     * @param treasureId 珍藏礼物id
     */
    public PlayerClan.Player_GetClanTreasureGift_S2C.Builder takeClanTreasureGift(long treasureId) {
        ClanTreasureGiftItemProp treasureGiftItem = findTreasureGiftItem(treasureId);
        if (treasureGiftItem == null) {
            throw new GeminiException(ErrorCode.CLAN_TREASURE_GIFT_NOT_EXIST);
        }
        // 先扣除
        getProp().removeTreasureGiftItems(treasureGiftItem);

        // 给奖励
        ClanTreasureTemplate treasureTemplate = ResHolder.getInstance().getValueFromMap(ClanTreasureTemplate.class, treasureGiftItem.getTemplateId());
        List<ItemReward> rewards = getOwner().getItemComponent().sendReward(treasureTemplate.getRewardId(), CommonEnum.Reason.ICR_CLAN_TREASURE_GIFT, String.valueOf(treasureTemplate.getId()));
        AssetPackage.Builder assetPackage = AssetPackage.builder();
        // 更新礼物对应产出
        for (ItemReward reward : rewards) {
            ItemPairProp rewardRes = new ItemPairProp();
            rewardRes.setItemTemplateId(reward.getItemTemplateId())
                    .setCount(reward.getCount());
            assetPackage.plusItem(reward.getItemTemplateId(), reward.getCount());
        }
        getOwner().getQlogComponent().sendTakeTreasureGiftQlog(treasureTemplate.getId());

        PlayerClan.Player_GetClanTreasureGift_S2C.Builder response = PlayerClan.Player_GetClanTreasureGift_S2C.newBuilder();
        // 复用接口获取最新数据
        response.setClanGiftInfo(addClanGiftPointAndKey(0, 0).getClanGiftInfo());
        response.setGiftReward(assetPackage.build().toPb());
        return response;
    }

    /**
     * 添加联盟礼物等级点数与珍藏礼物钥匙
     *
     * @param addPoint 联盟等级点数
     * @param addKey   珍藏礼物钥匙数
     */
    private SsClanGift.AddGiftPointAndKeyAns addClanGiftPointAndKey(int addPoint, int addKey) {
        SsClanGift.AddGiftPointAndKeyAsk ask = SsClanGift.AddGiftPointAndKeyAsk.newBuilder()
                .setAddKey(addKey)
                .setAddPoint(addPoint)
                .build();
        return ownerActor().callCurClan(ask);
    }


    static class PlayerClanGiftHelper extends ClanGiftHelper {
        private final PlayerQlogComponent qlogComponent;

        PlayerClanGiftHelper(ClanGiftItemListProp clanGiftItemProps, PlayerQlogComponent qlogComponent) {
            super(clanGiftItemProps);
            this.qlogComponent = qlogComponent;
        }


        @Override
        protected void onRemoveTraverse(ClanGiftItemProp giftItemProp) {
            this.updateGiftExpiredStatus(giftItemProp);
        }

        /**
         * 清理已领取or已过期军团礼物
         */
        public void clearTakenOrExpiredGiftsImpl(CommonEnum.ClanGiftRarityType rarityType) {
            List<ClanGiftItemProp> removeGiftItems = this.getClanGifts().stream()
                    .filter(giftItem -> ResHolder.getTemplate(ClanGiftTemplate.class, giftItem.getGiftId()).getType() == rarityType && (ClanGiftUtils.isGiftTaken(giftItem) || ClanGiftUtils.isGiftExpired(giftItem)))
                    .toList();
            if (removeGiftItems.isEmpty()) {
                return;
            }
            if (rarityType == CommonEnum.ClanGiftRarityType.CLANGRT_NORMAML) {
                normalGiftCnt -= removeGiftItems.size();
            } else {
                rareGiftCnt -= removeGiftItems.size();
            }

            this.getClanGifts().removeAll(removeGiftItems);
        }

        /**
         * 更新军团礼物过期状态
         *
         * @param giftItem 军团礼物
         */
        public void updateGiftExpiredStatus(ClanGiftItemProp giftItem) {
            boolean isExpired = ClanGiftUtils.isGiftExpired(giftItem);
            if (isExpired && giftItem.getStatus() == CommonEnum.ClanGiftStatus.CGS_CAN_TAKE) {
                giftItem.setStatus(CommonEnum.ClanGiftStatus.CGS_EXPIRED);
                ClanGiftTemplate giftTemplate = ResHolder.getTemplate(ClanGiftTemplate.class, giftItem.getGiftId());
                this.qlogComponent.sendExpireClanGiftQLog(giftTemplate.getGiftID());
            }
        }
    }

}

package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 英雄觉醒事件
 *
 * <AUTHOR>
 */
public class PlayerHeroIntensiveEvent extends PlayerTaskEvent {
    private final int heroId;

    public PlayerHeroIntensiveEvent(PlayerEntity player, int heroId) {
        super(player);
        this.heroId = heroId;
    }

    public int getHeroId() {
        return heroId;
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.PlayerUseItemEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.ItemTemplate;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.USE_ITEMTYPE_TOTAL;

/**
 * 使用道具类型
 * param1:道具类型
 * param2:次数
 *
 * <AUTHOR>
 */
public class UseItemTypeChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerUseItemEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(
            PlayerTaskEvent event,
            TaskInfoProp prop,
            TaskPoolTemplate taskTemplate
    ) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer itemType = taskParams.get(0);
        Integer times = taskParams.get(1);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int useItemTotal = (int) entity.getStatisticComponent().getSecondStatistic(USE_ITEMTYPE_TOTAL, itemType);
                prop.setProcess(Math.min(useItemTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerUseItemEvent) {
                    PlayerUseItemEvent useItemEvent = (PlayerUseItemEvent) event;
                    ItemTemplate itemTemplate = ResHolder.getInstance().getValueFromMap(ItemTemplate.class, useItemEvent.getItemId());
                    if (!itemTemplate.getUseNow() && itemType == useItemEvent.getItemType()) {
                        int useItemNum = useItemEvent.getItemNum();
                        prop.setProcess(Math.min(times, prop.getProcess() + useItemNum));
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("un know task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 添加天网经验
 *
 * <AUTHOR>
 */
public class AddSkynetExp implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        long value = Long.parseLong(args.get("value"));
        actor.getOrLoadEntity().getSkynetComponent().addExpByGm(value);
    }

    @Override
    public String showHelp() {
        return "AddSkynetExp value=99999";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

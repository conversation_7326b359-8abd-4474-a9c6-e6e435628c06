package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DebugGroup;
import res.template.ItemTemplate;

import java.util.Map;

/**
 * 加道具
 *
 * <AUTHOR>
 */
public class AddItem implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("templateId"));
        int count = 1;
        if (args.size() > 1) {
            count = Integer.parseInt(args.get("count"));
        }
        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, templateId);
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST.getCodeId());
        }

        actor.getEntity().getItemComponent().addItem(itemTemplate, count, CommonEnum.Reason.ICR_GM, "");
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "AddItem templateId={value} count={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_ITEM;
    }
}

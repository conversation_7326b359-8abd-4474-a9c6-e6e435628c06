package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.GatherTreatedSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.FINISH_TREAT_SOLDIER_NUM;

/**
 * 完成治疗士兵x个
 * param1: 治疗数量
 *
 * <AUTHOR>
 */
public class GatherTreatedSolider<PERSON>um<PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            GatherTreatedSoldierEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.getFirst();

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int treatTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(FINISH_TREAT_SOLDIER_NUM);
            prop.setProcess(Math.min(treatTotal, param1));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof GatherTreatedSoldierEvent) {
                int treatNum = ((GatherTreatedSoldierEvent) event).getTreatNum();
                prop.setProcess(Math.min(prop.getProcess() + treatNum, param1));
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param1;
    }
}

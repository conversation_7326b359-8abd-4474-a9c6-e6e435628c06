package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;

import java.util.Map;

/**
 * 清零某种或者全部资源
 */
public class ClearCurrency implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String param1 = args.get("type");
        if (StringUtils.isNotEmpty(param1)) {
            CurrencyType type = CurrencyType.forNumber(Integer.parseInt(param1));
            actor.getEntity().getPurseComponent().debugClear(type);
        } else {
            CurrencyType[] array = CurrencyType.values();
            for (CurrencyType type : array) {
                actor.getEntity().getPurseComponent().debugClear(type);
            }
        }
    }

    @Override
    public String showHelp() {
        return "ClearCurrency type={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}


package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerKillSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.SOLDIER_KILL_TOTAL;


public class PVPKillSoldierChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerKillSoldierEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.get(0);
        int param2 = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int killTotal;
            if (param1 == 0) {
                killTotal = (int) event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(SOLDIER_KILL_TOTAL);
            } else {
                killTotal = (int) event.getPlayer().getStatisticComponent().getSecondStatistic(SOLDIER_KILL_TOTAL, param1);
            }
            prop.setProcess(Math.min(killTotal, param2));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof PlayerKillSoldierEvent) {
                int killNum = 0;
                Map<Integer, Long> killSoldierId2Num = ((PlayerKillSoldierEvent) event).getKillSoldierMap();
                for (Map.Entry<Integer, Long> entry : killSoldierId2Num.entrySet()) {
                    if (entry.getKey() == param1 || param1 == 0) {
                        killNum += entry.getValue();
                    }
                }
                prop.setProcess(Math.min(prop.getProcess() + killNum, param2));
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param2;
    }
}

package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ActivityBestCommanderDailyDataProp;
import com.yorha.game.gen.prop.ActivityBestCommanderUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneActivitySchedule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;

public class PlayerBestCommanderUnit extends BasePlayerActivityUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerBestCommanderUnit.class);

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_BEST_COMMANDER_SEASON, (owner, prop, template) ->
                new PlayerBestCommanderUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerBestCommanderUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
        final Instant now = SystemClock.nowInstant();
        final Instant expireTime = Instant.ofEpochSecond(ownerActivity.getProp().getEndTsSec());
        if (now.isAfter(expireTime)) {
            LOGGER.info("PlayerBestCommanderUnit no need load act: {}", ownerActivity.getActivityId());
            return;
        }
        final SsSceneActivitySchedule.BestCommanderGetVolumeAsk.Builder ask = SsSceneActivitySchedule.BestCommanderGetVolumeAsk.newBuilder();
        ask.setActId(ownerActivity.getActivityId()).setPlayerId(player().getPlayerId());
        player().ownerActor().askSelfBigScene(player().getZoneId(), ask.build()).onComplete((res, err) -> {
            if (err != null) {
                LOGGER.warn("PlayerBestCommanderUnit load failed act: {}", ownerActivity.getActivityId(), err);
                return;
            }
            final SsSceneActivitySchedule.BestCommanderGetVolumeAns ans = (SsSceneActivitySchedule.BestCommanderGetVolumeAns) res;
            unitProp.getBestCommanderUnit().setActStage(ans.getActStage()).setVolumeCurSeason(ans.getVolume());
            LOGGER.info("PlayerBestCommanderUnit load {} {}", ans.getActStage(), ans.getVolume());
        });
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    public void handleFetch() {
        ActivityBestCommanderUnitProp unitProp = this.unitProp.getBestCommanderUnit();

        SsSceneActivitySchedule.BestCommanderFetchAsk.Builder ask = SsSceneActivitySchedule.BestCommanderFetchAsk.newBuilder();
        ask.setPlayerId(player().getPlayerId());
        ask.setActId(ownerActivity.getActivityId());

        for (ActivityBestCommanderDailyDataProp dailyDataProp : unitProp.getDailyDataMap().values()) {
            if (dailyDataProp.getMyRank() == 0) {
                ask.addChildActIds(dailyDataProp.getActId());
            }
        }
        if (ask.getChildActIdsCount() > 0) {
            SsSceneActivitySchedule.BestCommanderFetchAns ans = player().ownerActor().callSelfBigScene(ask.build());
            for (SsSceneActivitySchedule.BestCommanderFetchItem fetchItem : ans.getFetchItemsList()) {
                this.unitProp.getBestCommanderUnit()
                        .addEmptyDailyDataMap(fetchItem.getActId())
                        .setActId(fetchItem.getActId())
                        .setMyRank(fetchItem.getRank());
            }
        }

    }

    public void onRankUnitExpire(int activityId) {
        ActivityBestCommanderUnitProp prop = this.unitProp.getBestCommanderUnit();
        if (!prop.getDailyDataMap().containsKey(activityId)) {
            prop.addEmptyDailyDataMap(activityId)
                    .setActId(activityId);
        }
    }
}

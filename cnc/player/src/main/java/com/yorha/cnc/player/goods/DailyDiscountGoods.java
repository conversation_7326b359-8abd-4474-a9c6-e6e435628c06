package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

public class DailyDiscountGoods implements Goods {
    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        owner.getDailyDiscountComponent().checkBuyDailyDiscountBox(goodsTemplate.getId());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        owner.getDailyDiscountComponent().afterBuyDailyDiscountBox(goodsTemplate.getId());
        return Collections.emptyList();
    }
}

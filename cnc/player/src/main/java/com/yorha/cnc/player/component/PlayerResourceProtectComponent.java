package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.enums.BuildResourceMapType;
import com.yorha.proto.CommonEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 资源保护组件
 *
 * <AUTHOR>
 */
public class PlayerResourceProtectComponent extends PlayerComponent {

    public PlayerResourceProtectComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 获取可掠夺的资源
     */
    public Map<CommonEnum.CurrencyType, Long> getPlunderAmountWithResource() {
        Map<CommonEnum.CurrencyType, Long> result = new HashMap<>(5);
        for (BuildResourceMapType type : BuildResourceMapType.values()) {
            long resourceProtect = PlayerAddCalc.getResourceProtect(getOwner(), type);
            long currencyAmount = getOwner().getPurseComponent().getCurrencyAmount(type.getResourceType());
            // 可掠夺资源量 = 持有的资源量 - 资源保护量
            result.put(type.getResourceType(), Math.max((currencyAmount - resourceProtect), 0));
        }
        return result;
    }

    /**
     * 获取可掠夺的资源
     */
    public Map<Integer, Long> getPlunderResource() {
        Map<Integer, Long> result = new HashMap<>(5);
        for (BuildResourceMapType type : BuildResourceMapType.values()) {
            long resourceProtect = PlayerAddCalc.getResourceProtect(getOwner(), type);
            long currencyAmount = getOwner().getPurseComponent().getCurrencyAmount(type.getResourceType());
            // 可掠夺资源量 = 持有的资源量 - 资源保护量
            result.put(type.getResourceType().getNumber(), Math.max((currencyAmount - resourceProtect), 0));
        }
        return result;
    }

    /**
     * 获取受保护的资源
     */
    public Map<Integer, Long> getProtectWithResource() {
        Map<Integer, Long> result = new HashMap<>(5);
        for (BuildResourceMapType type : BuildResourceMapType.values()) {
            final long resourceProtect = PlayerAddCalc.getResourceProtect(getOwner(), type);
            final long currencyAmount = getOwner().getPurseComponent().getCurrencyAmount(type.getResourceType());
            result.put(type.getResourceType().getNumber(), Math.min(currencyAmount, resourceProtect));
        }
        return result;
    }
}

package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.task.DiscountStoreBuyEvent;
import com.yorha.cnc.player.event.task.DiscountStoreDiamondBuyEvent;
import com.yorha.cnc.player.event.task.DiscountStoreRefreshEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.TimeConstant;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.qlog.json.item.QlogItemConfig;
import com.yorha.common.qlog.json.money.QlogMoneyConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.DiscountInfoProp;
import com.yorha.game.gen.prop.GoodInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO;
import com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C;
import com.yorha.proto.SsSceneDungeon;
import com.yorha.proto.StructMsg.StoreItemDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstDiscountShopTemplate;
import res.template.DiscountShopTemplate;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_MANY_MONEY;

/**
 * 折扣商店相关
 *
 * <AUTHOR> Jiang
 */
public class PlayerDiscountStoreComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerDiscountStoreComponent.class);
    private ActorTimer refreshTask;

    static {
        EntityEventHandlerHolder.register(BuildFinEvent.class, PlayerDiscountStoreComponent::handle);
    }

    public PlayerDiscountStoreComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        long lastRefreshTsMs = getOwner().getProp().getDiscountInfo().getLastRefreshTime();
        long now = SystemClock.now();
        ConstDiscountShopTemplate template = ResHolder.getInstance().getConstTemplate(ConstDiscountShopTemplate.class);
        int interval = template.getShopRefreshInterval() * TimeConstant.ONE_HOUR;
        // 如果下线期间应该刷新一次，那么主动刷新并且设置下次的任务。否则只设置下次的任务
        if (now / interval != lastRefreshTsMs / interval) {
            handleReset();
        } else {
            setRefreshTask();
        }
    }

    /**
     * 玩家登出后
     */
    @Override
    public void afterLogout() {
        if (refreshTask != null) {
            refreshTask.cancel();
            refreshTask = null;
        }
    }

    /**
     * 建筑完成
     */
    public static void handle(BuildFinEvent event) {
        //if (event.getType() == InnerCityBuildType.MARKET) {
        //    event.getPlayer().getDiscountStoreComponent().resetStore(true);
        //}
    }


    public void handleReset() {
        if (refreshTask != null) {
            refreshTask.cancel();
        }
        refreshTask = null;
        resetStore(true);
        setRefreshTask();
    }

    /***重置刷新道具*/
    private void resetStore(boolean sys) {
        LOGGER.info("reset store start, playerId={}, sys={}", this.getPlayerId(), sys);
        ConstDiscountShopTemplate template = ResHolder.getInstance().getConstTemplate(ConstDiscountShopTemplate.class);
        DiscountInfoProp prop = getOwner().getProp().getDiscountInfo();
        long now = SystemClock.now();
        if (sys) {
            int interval = template.getShopRefreshInterval() * TimeConstant.ONE_HOUR;
            now -= now % interval;
            prop.setLastRefreshTime(now);
            prop.setRefreshTimes(template.getRefreshTimes());
            prop.setFreeTimes(template.getFreeRefreshTimes());
        }
        prop.getGoodStore().clear();
        LOGGER.info("reset store end, playerId={}, sys={}, now={}", this.getPlayerId(), sys, SystemClock.now());
    }

    /**
     * 折扣商店详情
     */
    public DiscountStoreDTO getDiscountStoreInfo() {
        DiscountStoreDTO.Builder ret = DiscountStoreDTO.newBuilder();
        DiscountInfoProp prop = getOwner().getProp().getDiscountInfo();
        ConstDiscountShopTemplate template = ResHolder.getInstance().getConstTemplate(ConstDiscountShopTemplate.class);
        ret.setFreeTimes(prop.getFreeTimes());
        ret.setMaxFreeTimes(template.getFreeRefreshTimes());
        ret.setRefreshTimes(prop.getRefreshTimes());
        ret.setMaxRefreshTimes(template.getRefreshTimes());
        int interval = template.getShopRefreshInterval() * TimeConstant.ONE_HOUR;
        long nextRefreshTime = prop.getLastRefreshTime() + interval;
        ret.setNextRefreshTime(nextRefreshTime);
        if (prop.getFreeTimes() > 0) {
            ret.setRefreshPrice(0);
        } else {
            int index = Math.min(template.getRefreshPrice().size() - 1, template.getRefreshTimes() - prop.getRefreshTimes());
            int price = template.getRefreshPrice().get(index);
            ret.setRefreshPrice(price);
        }

        Collection<GoodInfoProp> store = prop.getGoodStore().values();
        List<Integer> needDelIds = Lists.newLinkedList();
        for (GoodInfoProp e : store) {
            DiscountShopTemplate discountShopTemplate = ResHolder.getInstance().findValueFromMap(DiscountShopTemplate.class, e.getId());
            // 配置不存在，跳过
            if (null == discountShopTemplate) {
                needDelIds.add(e.getId());
                continue;
            }
            StoreItemDTO dto = this.buildStoreItemDTO(e, discountShopTemplate);
            ret.addList(dto);
        }
        // 删除配置不存在的商品id
        for (int needDelId : needDelIds) {
            prop.removeGoodStoreV(needDelId);
        }
        return ret.build();
    }

    /**
     * 购买道具
     */
    public Player_BuyDiscountStoreItem_S2C buyDiscountStoreItem(int itemId, String sPassWord) {
        Player_BuyDiscountStoreItem_S2C.Builder ret = Player_BuyDiscountStoreItem_S2C.newBuilder();
        DiscountInfoProp prop = getOwner().getProp().getDiscountInfo();
        GoodInfoProp goodInfoProp = prop.getGoodStore().get(itemId);
        //验证存在与否
        if (goodInfoProp == null) {
            throw new GeminiException(ErrorCode.DISCOUNT_STORE_GOOD_NOT_FOUND);
        }
        //验证数量
        if (goodInfoProp.getSellOut()) {
            throw new GeminiException(ErrorCode.DISCOUNT_STORE_GOOD_SELL_OUT);
        }
        DiscountShopTemplate discountShopTemplate = ResHolder.getInstance().getValueFromMap(DiscountShopTemplate.class, itemId);
        IntPairType money = discountShopTemplate.getPricePair();
        CurrencyType currencyType = CurrencyType.forNumber(money.getKey());
        if (currencyType == CurrencyType.DIAMOND) {
            getOwner().getSettingComponent().checkSpassword(SPWC_MANY_MONEY, money.getValue(), sPassWord);
        }
        AssetPackage cost = AssetPackage.builder().plusCurrency(currencyType, money.getValue()).build();
        getOwner().getAssetComponent().verifyThrow(cost);
        // 当前默认使用
        getOwner().getAssetComponent().consume(cost, CommonEnum.Reason.ICR_SHOPPING, "black_market");

        AssetPackage give = AssetPackage.builder().plusItem(discountShopTemplate.getItemId(), goodInfoProp.getCount()).build();
        getOwner().getAssetComponent().give(give, CommonEnum.Reason.ICR_SHOPPING, "black_market");

        // 更新任务进度
        if (currencyType == CurrencyType.DIAMOND) {
            getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.DISCOUNT_STORE_DIAMONDBUY_TOTAL, 1);
            new DiscountStoreDiamondBuyEvent(getOwner(), 1).dispatch();
        }
        getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.DISCOUNT_STORE_BUY_TOTAL, 1);
        new DiscountStoreBuyEvent(getOwner(), 1).dispatch();
        long now = SystemClock.now();
        goodInfoProp.setBuyTime(now);
        goodInfoProp.setSellOut(true);
        StoreItemDTO dto = this.buildStoreItemDTO(goodInfoProp, discountShopTemplate);
        ret.setDto(dto);
        // 黑市商店购买流水
        getOwner().getQlogComponent().sendShopQLog("black_market", "shop_purchase",
                QlogMoneyConfig.getQlogMoneyConfigStr(currencyType != null ? currencyType.getNumber() : 0, money.getValue()),
                QlogItemConfig.getQlogItemConfigStr(itemId, 1));
        return ret.build();
    }

    /**
     * 刷新商店
     */
    public void refreshDiscountStore() {
        DiscountInfoProp prop = getOwner().getProp().getDiscountInfo();
        //刷新次数不足
        if (prop.getFreeTimes() <= 0 && prop.getRefreshTimes() <= 0) {
            throw new GeminiException(ErrorCode.DISCOUNT_STORE_REFRESH_TIMES_NOT_ENOUGH);
        }
        boolean needPayForRefresh = prop.getFreeTimes() <= 0;
        int refreshMoneyType = 0;
        int refreshMoneyCount = 0;
        if (needPayForRefresh) {
            // 需要付费刷新
            ConstDiscountShopTemplate template = ResHolder.getInstance().getConstTemplate(ConstDiscountShopTemplate.class);
            int index = Math.min(template.getRefreshPrice().size() - 1, template.getRefreshTimes() - prop.getRefreshTimes());
            refreshMoneyType = CurrencyType.DIAMOND_VALUE;
            refreshMoneyCount = template.getRefreshPrice().get(index);
            // 扣费
            getOwner().getPurseComponent().consume(CurrencyType.forNumber(refreshMoneyType), refreshMoneyCount,
                    CommonEnum.Reason.ICR_SHOP_REFRESH, "black_market");
            prop.setRefreshTimes(prop.getRefreshTimes() - 1);
        } else {
            prop.setFreeTimes(prop.getFreeTimes() - 1);
        }
        this.resetStore(false);
        // 记录项更新
        getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.DISCOUNT_STORE_REFRESH_TOTAL, 1);
        // 抛出黑市刷新事件
        new DiscountStoreRefreshEvent(getOwner(), 1).dispatch();
        // qLog 打点
        if (needPayForRefresh) {
            getOwner().getQlogComponent().sendShopQLog("black_market", "shop_refresh",
                    QlogMoneyConfig.getQlogMoneyConfigStr(refreshMoneyType, refreshMoneyCount),
                    "");
        } else {
            getOwner().getQlogComponent().sendShopQLog("black_market", "shop_free_refresh",
                    "",
                    "");
        }
    }

    /**
     * 构建道具
     */
    private StoreItemDTO buildStoreItemDTO(GoodInfoProp e, DiscountShopTemplate discountShopTemplate) {
        StoreItemDTO.Builder builder = StoreItemDTO.newBuilder();
        builder.setId(e.getId());
        builder.setCount(e.getCount());
        builder.setSellOut(e.getSellOut());
        builder.setTemplateId(discountShopTemplate.getItemId());
        builder.setGroup(discountShopTemplate.getGroup());
        IntPairType money = discountShopTemplate.getPricePair();
        builder.setMoneyType(money.getKey());
        builder.setMoneyNum(money.getValue());
        builder.setDiscount(discountShopTemplate.getDiscount());
        builder.setSortWeight(discountShopTemplate.getSortingWeight());
        return builder.build();
    }


    /**
     * 设置下次刷新商店的任务
     */
    private void setRefreshTask() {
        if (refreshTask != null) {
            LOGGER.warn("refresh Task would be reset");
            refreshTask.cancel();
            refreshTask = null;
        }
        long delay = getNextTaskDelayTsMs();
        refreshTask = ownerActor().addTimer(TimerReasonType.REFRESH_DISCOUNT_STORE, this::handleReset, delay, TimeUnit.MILLISECONDS);
        LOGGER.info("set next refresh task success, delay {}, now {}", delay, SystemClock.now());
    }

    /**
     * @return 获取下次任务刷新商店的任务的启动延迟
     */
    private long getNextTaskDelayTsMs() {
        DiscountInfoProp prop = getOwner().getProp().getDiscountInfo();
        long now = SystemClock.now();
        ConstDiscountShopTemplate template = ResHolder.getInstance().getConstTemplate(ConstDiscountShopTemplate.class);
        int interval = template.getShopRefreshInterval() * TimeConstant.ONE_HOUR;
        // 无脑设置上次刷新时间为最近的整点刷新时间
        prop.setLastRefreshTime(now - now % interval);
        // now - now % interval + interval - now = interval - now % interval > 0 永远成立
        return Math.max(0, prop.getLastRefreshTime() + interval - now);
    }
}

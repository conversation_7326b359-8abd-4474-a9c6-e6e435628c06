package com.yorha.cnc.player.chat.handler;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.SsClanChat;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

/**
 * 联盟聊天
 *
 * <AUTHOR>
 */

public class ClanChatHandler implements ChatHandler {
    @Override
    public CommonEnum.ChatChannel chatChannel() {
        return CommonEnum.ChatChannel.CC_CLAN;
    }

    @Override
    public long chatRequestSync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage) {
        // 检查玩家是否在军团中
        PlayerEntity playerEntity = chatPlayerEntity.ownerActor().getOrLoadEntity();
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId != Long.parseLong(channelId)) {
            // 移除不存在的军团聊天
            ChannelInfoProp channelInfoProp = chatPlayerEntity.getProp().getChannelInfoV(CommonEnum.ChatChannel.CC_CLAN_VALUE);
            if (channelInfoProp != null) {
                channelInfoProp.removeItemV(channelId);
            }
            throw new GeminiException(ErrorCode.CHAT_NOT_CLAN_MEMBER);
        }
        SsClanChat.SendClanChatAsk.Builder ask = SsClanChat.SendClanChatAsk.newBuilder()
                .setChatMessage(chatMessage);
        SsClanChat.SendClanChatAns ans = chatPlayerEntity.ownerActor().callCurClan(ask.build());
        CommonMsg.ChatSession.Builder chatSession = CommonMsg.ChatSession.newBuilder();
        chatSession.setChannelType(chatChannel())
                .setChatChannelId(channelId);
        chatPlayerEntity.getHandleChatComponent().readMessage(chatSession.build(), ans.getMessageId());
        return ans.getMessageId();
    }

    @Override
    public void chatRequestAsync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage, Consumer<Throwable> onComplete) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public List<CommonMsg.ChatMessage> queryChatMsgList(
            ChatPlayerEntity chatPlayerEntity,
            String channelId,
            Collection<Long> shieldList,
            PlayerChat.Player_GetChatMessages_C2S msg
    ) {
        // 检查玩家是否在军团中
        PlayerEntity playerEntity = chatPlayerEntity.ownerActor().getOrLoadEntity();
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId != Long.parseLong(channelId)) {
            // 移除不存在的军团聊天
            ChannelInfoProp channelInfoProp = chatPlayerEntity.getProp().getChannelInfoV(CommonEnum.ChatChannel.CC_CLAN_VALUE);
            if (channelInfoProp != null) {
                channelInfoProp.removeItemV(channelId);
            }
            throw new GeminiException(ErrorCode.CHAT_NOT_CLAN_MEMBER);
        }
        long queryStartIndex = chatPlayerEntity.getHandleChatComponent().getQueryStartIndex(CommonEnum.ChatChannel.CC_CLAN, channelId);
        long toId = Math.max(queryStartIndex, msg.getToId());
        long fromId = Math.max(msg.getFromId(), toId);
        if (msg.getIsLogin()) {
            fromId = 0;
        }
        SsClanChat.FetchClanChatAsk.Builder ask = SsClanChat.FetchClanChatAsk.newBuilder().setFromId(fromId).setToId(toId).addAllShieldList(shieldList);
        SsClanChat.FetchClanChatAns ans = chatPlayerEntity.ownerActor().callCurClan(ask.build());
        if (msg.getIsLogin()) {
            return ans.getChatMsgsList();
        }
        return ChatHelper.playerQueryChatMsgOnCacheMsg(chatPlayerEntity.ownerActor(), ans.getChatMsgsList(), msg.getChatSession(), fromId, toId, shieldList, queryStartIndex);
    }

    @Override
    public long getCd(ChatPlayerEntity chatPlayerEntity) {
        return 0;
    }
}

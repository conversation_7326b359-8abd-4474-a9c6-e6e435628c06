package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家占领据点事件
 *
 * <AUTHOR>
 */
public class PlayerOccupyPointEvent extends PlayerTaskEvent {
    private final int occupyTime;

    public PlayerOccupyPointEvent(PlayerEntity entity, int occupyTime) {
        super(entity);
        this.occupyTime = occupyTime;
    }

    public int getOccupyTime() {
        return occupyTime;
    }
}

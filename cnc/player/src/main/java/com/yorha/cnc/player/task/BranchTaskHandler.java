package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.task.BranchTaskFinishEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskBranchTemplate;
import res.template.TaskPoolTemplate;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 普通任务处理类
 *
 * <AUTHOR>
 */
public class BranchTaskHandler extends AbstractTaskHandler {
    private static final Logger LOGGER = LogManager.getLogger(BranchTaskHandler.class);

    private final Set<Integer> allAcceptedGroupIds = new HashSet<>();

    boolean alreadyInit = false;

    public BranchTaskHandler(PlayerEntity entity, CommonEnum.TaskClass tcBranch) {
        super(entity, tcBranch.name());
    }

    /*
    @Override
    public void openAttention() {
        if (getEntity().getNewbieComponent().isNewbie()) {
            return;
        }
        openAttention = true;

    }
     */

    @Override
    public void loadAllTask() {
        /*
        if (getEntity().getNewbieComponent().isNewbie()) {
            return;
        }
         */
        // 防重入
        if (alreadyInit) {
            return;
        }
        // fix：主城升级触发在任务load之前，此时openAttention还未打开，这里手动打开下
        if (!openAttention) {
            openAttention();
        }
        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            TaskBranchTemplate branchConfig = ResHolder.getInstance().findValueFromMap(TaskBranchTemplate.class, value.getId());
            if (branchConfig == null) {
                //这里遍历时new了新的arraylist，可以直接从taskprop中移除对象
                getTaskProp().remove(value.getId());
                continue;
            }
            addTaskEventMonitor(value);
            allAcceptedGroupIds.add(branchConfig.getGroupId());
        }
        alreadyInit = true;
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> configIds) {
        List<IntPairType> rewardList = Lists.newArrayList();
        for (Integer configId : configIds) {

            TaskInfoProp taskInfoProp = getTaskInfoProp(getTaskProp(), configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }
            setNextTaskState(taskInfoProp);

            // 领奖
            List<IntPairType> reward = ResHolder.getTemplate(TaskBranchTemplate.class, configId).getRewardPairList();
            AssetPackage assetPackage = AssetPackage.builder().plusItems(reward).build();
            getEntity().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_TASK, formationSubReason(configId));

            rewardList.addAll(reward);

            takeRewardQLog(taskInfoProp);
        }
        return rewardList;
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum isGm) {
        super.onTaskFinish(configId, isGm);
        if (isGm == TaskGmEnum.CREATE_ALL_GM) {
            LOGGER.info("create all branch task");
            return;
        }
        TaskBranchTemplate branchConfig = ResHolder.getInstance().getValueFromMap(TaskBranchTemplate.class, configId);
        if (branchConfig.getFrontTask() > 0) {
            TaskBranchTemplate postConfig = ResHolder.getInstance().getValueFromMap(TaskBranchTemplate.class, branchConfig.getFrontTask());
            addBatchTask(Sets.newHashSet(postConfig.getId()));
            allAcceptedGroupIds.add(postConfig.getGroupId());
        }
        new BranchTaskFinishEvent(getEntity(), configId).dispatch();
    }

    @Override
    public void onInnerCityUpgrade(BuildFinEvent event) {
        /*
        if (getEntity().getNewbieComponent().isNewbie()) {
            return;
        }
         */
        if (!alreadyInit) {
            loadAllTask();
        }
        // 检测任务解锁
        checkOrAddTask(CommonEnum.BranchTaskCondition.BTC_INNER_CITY);
    }

    @Override
    public void onJoinClan(PlayerJoinClanEvent event) {
        /*
        if (getEntity().getNewbieComponent().isNewbie()) {
            return;
        }
        */
        if (!alreadyInit) {
            loadAllTask();
        }
        // 检测任务解锁
        checkOrAddTask(CommonEnum.BranchTaskCondition.BTC_JOIN_CLAN);
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    @Override
    public void onNewbieOver() {
        // 只有新手通过时才会接取任务
        openAttention();

        if (!alreadyInit) {
            // 新手需求：新手前不接支线任务，所以新手后要主动加载一次
            loadAllTask();
        }

        for (CommonEnum.BranchTaskCondition reason : CommonEnum.BranchTaskCondition.values()) {
            if (reason == CommonEnum.BranchTaskCondition.BTC_NONE) {
                continue;
            }
            checkOrAddTask(reason);
        }
    }

    /**
     * 检测任务是否需要被添加
     */
    private void checkOrAddTask(CommonEnum.BranchTaskCondition reason) {
        Set<TaskBranchTemplate> acceptedTask = ResHolder.getResService(TaskTemplateService.class).getUnacceptedTask(reason);
        // 过滤已接取的
        acceptedTask.removeIf(it -> allAcceptedGroupIds.contains(it.getGroupId()));

        if (acceptedTask.size() <= 0) {
            return;
        }

        // 过滤不符合条件的
        long clanId = getEntity().getClanId();
        int mainCityLevel = getEntity().getCityLevel();
        int techLevel = 0;
        acceptedTask.removeIf(next -> !checkCanReceive(next, clanId, mainCityLevel, techLevel));

        if (acceptedTask.size() <= 0) {
            return;
        }
        Set<Integer> addGroupId = new HashSet<>();
        Set<Integer> addId = new HashSet<>();
        for (TaskBranchTemplate branchTemplate : acceptedTask) {
            addGroupId.add(branchTemplate.getGroupId());
            addId.add(branchTemplate.getId());
        }

        LOGGER.info("branchTask add task start. allAcceptedGroupIds:{}", allAcceptedGroupIds);
        allAcceptedGroupIds.addAll(addGroupId);
        addBatchTask(addId);
    }

    private boolean checkCanReceive(TaskBranchTemplate branchTemplate, long clanId, int mainCityLevel, int techLevel) {
        for (IntPairType intPairType : branchTemplate.getUnlockConditionPairList()) {
            CommonEnum.BranchTaskCondition branchTaskCondition = CommonEnum.BranchTaskCondition.forNumber(intPairType.getKey());
            if (branchTaskCondition == null) {
                WechatLog.error("unKnow condition by branchTask id:{}", branchTemplate.getId());
                return false;
            }
            switch (branchTaskCondition) {
                case BTC_JOIN_CLAN: {
                    if (clanId <= 0) {
                        return false;
                    }
                    break;
                }
                case BTC_INNER_CITY: {
                    if (mainCityLevel < intPairType.getValue()) {
                        return false;
                    }
                    break;
                }
                case BTC_INNER_TECH: {
                    if (techLevel < intPairType.getValue()) {
                        return false;
                    }
                    break;
                }
                default: {
                    WechatLog.error("unKnow branch condition");
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return getEntity().getProp().getTaskSystem().getTaskBranch();
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(TaskBranchTemplate.class, configId).getTaskId();
    }

    @Override
    public void fullMemoryByGm() {
        Collection<TaskBranchTemplate> listFromMap = ResHolder.getInstance().getListFromMap(TaskBranchTemplate.class);
        Set<Integer> addGroupId = new HashSet<>();
        Set<Integer> addId = new HashSet<>();
        for (TaskBranchTemplate branchTemplate : listFromMap) {
            addGroupId.add(branchTemplate.getGroupId());
            addId.add(branchTemplate.getId());
        }
        addBatchTask(addId);
        allAcceptedGroupIds.addAll(addGroupId);
    }
}

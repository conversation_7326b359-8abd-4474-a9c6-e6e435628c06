package com.yorha.cnc.player.friend.component;


import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.io.MsgType;
import com.yorha.common.prop.PropNotifier;
import com.yorha.game.gen.prop.FriendPlayerProp;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.PlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 玩家好友属性组件
 *
 * <AUTHOR>
 */
public class FriendPlayerPropComponent extends FriendPlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(FriendPlayerPropComponent.class);

    private final FriendPlayerPropNotifier propNotifier;

    public FriendPlayerPropComponent(FriendPlayerEntity owner) {
        super(owner);
        propNotifier = new FriendPlayerPropNotifier();
    }

    @Override
    public void init() {
        getOwner().getProp().setListener(new CanStopPropertyChangeListener(this::flushProp, getOwner().ownerActor().self()));
    }

    @Override
    public void onLogin() {
        propNotifier.reset();
        // 登录的时候会全量下发一次prop，之后增量下发
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());
    }

    public void onPlayerLogout() {
        this.propNotifier.reset();
    }

    public void immediateFlushProp() {
        this.flushProp();
    }

    @Override
    public void onDestroy() {
        if (!getOwner().getProp().hasAnyMark()) {
            return;
        }
        LOGGER.error("FriendPlayerPropComponent onDestroy {} has dirty data!", getOwner());
    }

    private void flushProp() {
        if (!getOwner().getProp().hasAnyMark()) {
            return;
        }
        if (getOwner().isDestroy()) {
            LOGGER.error("FriendPlayerPropComponent flushProp {} has dirty data!", getOwner());
            return;
        }
        // propNotifier自身是幂等的，对hasAnyMark有自己的控制
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());
        this.getOwner().getDbComponent().saveChangeToDb();
        this.getOwner().getProp().unMarkAll();
    }

    class FriendPlayerPropNotifier extends PropNotifier<FriendPlayerProp, PlayerPB.PlayerEntityPB.Builder> {
        FriendPlayerPropNotifier() {
            super(PlayerPB.PlayerEntityPB.newBuilder());
        }

        @Override
        public boolean canNtfToClient() {
            if (getOwner().isDestroy()) {
                return false;
            }
            PlayerEntity playerEntity = ownerActor().getEntityOrNull();
            if (playerEntity == null) {
                return false;
            }
            if (!playerEntity.isOnline()) {
                return false;
            }
            return true;
        }

        @Override
        public boolean ntfToClient(Entity.EntityNtfMsg notify) {
            PlayerEntity playerEntity = ownerActor().getEntityOrNull();
            if (playerEntity == null) {
                return false;
            }
            LOGGER.debug("FriendLog : prop notify = {}", notify);
            playerEntity.sendMsgToClient(MsgType.ENTITYNTFMSG, notify);
            return true;
        }

        @Override
        public void fillEntityAttr(EntityAttrOuterClass.EntityAttr.Builder entityAttr, PlayerPB.PlayerEntityPB.Builder builder) {
            entityAttr.setEntityId(getEntityId())
                    .setEntityType(getEntityType())
                    .setPlayerAttr(builder.build());
        }

        @Override
        public int copyChange(FriendPlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            return prop.copyChangeToCs(changeCollector.getFriendPlayerBuilder());
        }

        @Override
        public void copyFull(FriendPlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            prop.copyToCs(changeCollector.getFriendPlayerBuilder());
        }

        @Override
        public void fillNewEntityNtf(Entity.EntityNtfMsg.Builder builder, EntityAttrOuterClass.EntityAttr.Builder entityAttr) {
            // friendPlayer以PlayerEntity的mod形式下发
            builder.addModEntities(entityAttr);
        }
    }
}

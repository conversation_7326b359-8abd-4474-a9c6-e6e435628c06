package com.yorha.cnc.player.chat.handler;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.constant.ChatConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.game.gen.prop.PrepareChatItemProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstChatTemplate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class GroupChatHandler implements ChatHandler {
    private static final Logger LOGGER = LogManager.getLogger(GroupChatHandler.class);

    @Override
    public CommonEnum.ChatChannel chatChannel() {
        return CommonEnum.ChatChannel.CC_GROUP;
    }

    @Override
    public long chatRequestSync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage) {
        final CommonMsg.ChatSession.Builder chatSession = CommonMsg.ChatSession.newBuilder();
        chatSession.setChannelType(chatChannel()).setChatChannelId(channelId);
        final SsGroupChat.SendChatMessageAsk.Builder ask = SsGroupChat.SendChatMessageAsk.newBuilder();
        ask.setChatMessage(chatMessage);
        final SsGroupChat.SendChatMessageAns ans;
        try {
            ans = chatPlayerEntity.ownerActor().callSharedChat(chatChannel().getNumber(), channelId, ask.build());
        } catch (GeminiException e) {
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                chatPlayerEntity.getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, channelId);
            }
            throw e;
        }
        chatPlayerEntity.getHandleChatComponent().readMessage(chatSession.build(), ans.getMessageId());
        return ans.getMessageId();
    }

    @Override
    public void chatRequestAsync(ChatPlayerEntity chatPlayerEntity, String channelId, CommonMsg.ChatMessage chatMessage, Consumer<Throwable> onComplete) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public List<CommonMsg.ChatMessage> queryChatMsgList(
            ChatPlayerEntity chatPlayerEntity,
            String channelId,
            Collection<Long> shieldList,
            PlayerChat.Player_GetChatMessages_C2S msg
    ) {
        final long queryStartIndex = chatPlayerEntity.getHandleChatComponent().getQueryStartIndex(CommonEnum.ChatChannel.CC_GROUP, channelId);
        final long toId = Math.max(queryStartIndex, msg.getToId());
        long fromId = Math.max(msg.getFromId(), toId);
        if (msg.getIsLogin()) {
            fromId = 0;
        }

        final SsGroupChat.QueryChatMessageAsk ask = SsGroupChat.QueryChatMessageAsk.newBuilder()
                .setFromId(fromId)
                .setToId(toId)
                .addAllShieldList(shieldList)
                .build();

        try {
            final SsGroupChat.QueryChatMessageAns ans = chatPlayerEntity.ownerActor().callSharedChat(chatChannel().getNumber(), channelId, ask);
            List<CommonMsg.ChatMessage> cacheMsgList = ans.getChatMsgsList();
            if (msg.getIsLogin()) {
                return cacheMsgList;
            }
            return ChatHelper.playerQueryChatMsgOnCacheMsg(chatPlayerEntity.ownerActor(), cacheMsgList, msg.getChatSession(), fromId, toId, shieldList, queryStartIndex);
        } catch (GeminiException e) {
            if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                chatPlayerEntity.getHandleChatComponent().clearChannelData(CommonEnum.ChatChannel.CC_GROUP, channelId);
            }
            throw e;
        }
    }

    @Override
    public long getCd(ChatPlayerEntity chatPlayerEntity) {
        return 0;
    }

    public void createChatGroup(final PlayerActor actor, final int seqId, final List<Long> players, final String name) {
        if (players.size() < ResHolder.getInstance().getConstTemplate(ConstChatTemplate.class).getGroupChatNumber()) {
            throw new GeminiException(ErrorCode.CHAT_NOT_ENOUGH_MEMBER);
        }
        // 检查自己的群聊数量是否超出上限
        final int groupChatLimit = ResHolder.getConsts(ConstChatTemplate.class).getGroupChatMax();
        final ChatPlayerProp prop = actor.getChatPlayerEntity().getProp();
        ChannelInfoProp channelInfoProp = prop.getChannelInfoV(CommonEnum.ChatChannel.CC_GROUP_VALUE);
        if (channelInfoProp == null) {
            channelInfoProp = prop.addEmptyChannelInfo(CommonEnum.ChatChannel.CC_GROUP_VALUE);
        }
        if (channelInfoProp.getItemSize() + channelInfoProp.getPrepareItemSize() >= groupChatLimit) {
            throw new GeminiException(ErrorCode.UP_GROUP_CHAT_LIMIT);
        }
        IActorRef sessionRef = actor.sender();
        CardHelper.batchQueryPlayerCard(actor, players,
                (map) -> onChatMemberQueryEnd(sessionRef, actor, seqId, players, name, map));
    }

    private void onChatMemberQueryEnd(IActorRef sessionRef, PlayerActor actor, int seqId, List<Long> players, String name, Map<Long, StructPB.PlayerCardInfoPB> data) {
        // 验证成员数据
        if (!validateChatMembers(sessionRef, actor, seqId, players, data)) {
            return;
        }

        // 构建聊天成员
        final SsGroupChat.CreateChatAsk.Builder ask = buildChatMembers(players, data);

        // 创建群聊
        createGroupChat(sessionRef, actor, seqId, players, name, data, ask);
    }

    /**
     * 验证聊天成员数据
     */
    private boolean validateChatMembers(IActorRef sessionRef, PlayerActor actor, int seqId, List<Long> players, Map<Long, StructPB.PlayerCardInfoPB> data) {
        // 少人
        if (data.size() != players.size()) {
            SessionHelper.sendErrorCodeToSession(sessionRef, actor, MsgType.PLAYER_CREATEGROUPCHAT_S2C, seqId, ErrorCode.CHAT_PLAYER_NOT_EXIST.getCode());
            return false;
        }

        // 检查每个玩家数据是否存在
        for (final long playerId : players) {
            final StructPB.PlayerCardInfoPB pb = data.get(playerId);
            if (pb == null) {
                SessionHelper.sendErrorCodeToSession(sessionRef, actor, MsgType.PLAYER_CREATEGROUPCHAT_S2C, seqId, ErrorCode.CHAT_PLAYER_NOT_EXIST.getCode());
                return false;
            }
        }
        return true;
    }

    /**
     * 构建聊天成员
     */
    private SsGroupChat.CreateChatAsk.Builder buildChatMembers(List<Long> players, Map<Long, StructPB.PlayerCardInfoPB> data) {
        final SsGroupChat.CreateChatAsk.Builder ask = SsGroupChat.CreateChatAsk.newBuilder();

        // 填充成员
        for (final long playerId : players) {
            final StructPB.PlayerCardInfoPB pb = data.get(playerId);
            ask.getChatMembersBuilder().addMembers(CommonMsg.ChatMember.newBuilder()
                    .setPlayerId(playerId)
                    .setZoneId(pb.getZoneId())
                    .build());
        }
        return ask;
    }

    /**
     * 创建群聊
     */
    private void createGroupChat(IActorRef sessionRef, PlayerActor actor, int seqId, List<Long> players, String name, Map<Long, StructPB.PlayerCardInfoPB> data, SsGroupChat.CreateChatAsk.Builder ask) {
        // 生成ChatChannelId
        final String chatChannelId = String.valueOf(IdFactory.nextId("create_group_chat"));
        ask.setChatOwner(actor.getPlayerId()).setName(name);

        final IActorRef chatRef = RefFactory.ofGroupChat(this.chatChannel().getNumber(), chatChannelId);
        actor.<SsGroupChat.CreateChatAns>ask(chatRef, ask.build()).onComplete(
                (res, err) -> handleChatCreationResponse(sessionRef, actor, seqId, players, name, data, chatChannelId, res, err)
        );
    }

    /**
     * 处理聊天创建响应
     */
    private void handleChatCreationResponse(IActorRef sessionRef, PlayerActor actor, int seqId, List<Long> players, String name, Map<Long, StructPB.PlayerCardInfoPB> data, String chatChannelId, SsGroupChat.CreateChatAns res, Throwable err) {
        PlayerEntity entity = actor.getEntityOrNull();

        // 处理错误情况
        if (err != null) {
            handleChatCreationError(sessionRef, actor, seqId, chatChannelId, err);
            return;
        }

        final PlayerChat.Player_CreateGroupChat_S2C.Builder s2c = PlayerChat.Player_CreateGroupChat_S2C.newBuilder();
        s2c.setChannelId(chatChannelId);

        // 部分玩家失败，返回特定的回包
        if (!res.getFailedPlayerList().isEmpty()) {
            handlePartialFailure(sessionRef, entity, seqId, chatChannelId, res, s2c);
            return;
        }

        // 处理成功情况
        processChatCreationSuccess(sessionRef, actor, entity, seqId, players, name, data, chatChannelId, s2c);
    }

    /**
     * 处理聊天创建错误
     */
    private void handleChatCreationError(IActorRef sessionRef, PlayerActor actor, int seqId, String chatChannelId, Throwable err) {
        final Core.Code retCode;
        if (!(err instanceof Exception)) {
            retCode = ErrorCode.FAILED.getCode();
        } else {
            retCode = ErrorCodeUtils.getCodeFromException((Exception) err);
        }
        LOGGER.error("GroupChatHandler onChatMemberQueryEnd create chat={}, retCode={}, t=", chatChannelId, retCode, err);
        SessionHelper.sendErrorCodeToSession(sessionRef, actor, MsgType.PLAYER_CREATEGROUPCHAT_S2C, seqId, retCode);
    }

    /**
     * 处理部分玩家失败的情况
     */
    private void handlePartialFailure(IActorRef sessionRef, PlayerEntity entity, int seqId, String chatChannelId, SsGroupChat.CreateChatAns res, PlayerChat.Player_CreateGroupChat_S2C.Builder s2c) {
        LOGGER.info("GroupChatHandler onChatMemberQueryEnd create fail, chat={} failedMemberList={}", chatChannelId, res.getFailedPlayerList());
        s2c.addAllFailedPlayer(res.getFailedPlayerList());
        if (entity != null) {
            entity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_CREATEGROUPCHAT_S2C, null, s2c.build());
        }
    }

    /**
     * 处理聊天创建成功的情况
     */
    private void processChatCreationSuccess(IActorRef sessionRef, PlayerActor actor, PlayerEntity entity, int seqId, List<Long> players, String name, Map<Long, StructPB.PlayerCardInfoPB> data, String chatChannelId, PlayerChat.Player_CreateGroupChat_S2C.Builder s2c) {
        // 添加群聊的ChatItem
        final ChatItemProp chatItemProp = actor.getChatPlayerEntity().getHandleChatComponent().getChatItemOrCreate(CommonEnum.ChatChannel.CC_GROUP, chatChannelId);
        chatItemProp.setOwner(actor.getPlayerId())
                .setVersion(1)
                .setMaxIndex(0)
                .setReadIndex(0)
                .setGroupName(name);

        // 删除prepareItem
        removePrepareItem(actor, chatChannelId);

        // 发送创建群聊的系统消息
        final long startIndex = sendGroupChatSystemMessage(actor, players, data, chatChannelId);
        chatItemProp.setStartIndex(startIndex);

        LOGGER.info("GroupChatHandler onChatMemberQueryEnd success, groupMember={}, groupName={}, chat={}", players, name, chatChannelId);
        actor.getChatPlayerEntity().getPropComponent().immediateFlushProp();

        if (entity != null) {
            entity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_CREATEGROUPCHAT_S2C, null, s2c.build());
        }
    }

    /**
     * 删除预备聊天项
     */
    private void removePrepareItem(PlayerActor actor, String chatChannelId) {
        final PrepareChatItemProp prepareItemProp = actor.getChatPlayerEntity().getHandleChatComponent().removePrepareItem(chatChannelId);
        if (prepareItemProp == null) {
            LOGGER.warn("GroupChatHandler onChatMemberQueryEnd, not exist prepareItem, chat={}", chatChannelId);
        } else {
            LOGGER.info("GroupChatHandler onChatMemberQueryEnd, success remove prepareItem, chat={}", chatChannelId);
        }
    }

    /**
     * 发送群聊系统消息
     */
    private long sendGroupChatSystemMessage(PlayerActor actor, List<Long> players, Map<Long, StructPB.PlayerCardInfoPB> data, String chatChannelId) {
        final String ownerName = data.get(actor.getPlayerId()).getCardHead().getName();
        List<String> memberNames = new ArrayList<>(players.size());
        for (long playerId : players) {
            memberNames.add(data.get(playerId).getCardHead().getName());
        }

        // 系统消息
        final String memberName = String.join("、", memberNames);
        final CommonMsg.MessageData.Builder builder = CommonMsg.MessageData.newBuilder();
        builder.setTemplateId(ChatConstants.CREATE_GROUP_CHAT).getMsgParamBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayTextPb(ownerName))
                .addDatas(MsgHelper.buildDisPlayTextPb(memberName));

        return actor.getChatPlayerEntity().getHandleChatComponent().sendSystemChat(CommonEnum.ChatChannel.CC_GROUP, chatChannelId, builder.build());
    }
}

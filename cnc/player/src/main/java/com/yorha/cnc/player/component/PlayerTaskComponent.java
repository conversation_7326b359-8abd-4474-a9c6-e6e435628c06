package com.yorha.cnc.player.component;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerNewbieOverEvent;
import com.yorha.cnc.player.event.task.*;
import com.yorha.cnc.player.task.*;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncActivityScore;
import res.template.MonsterTemplate;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import static com.yorha.common.enums.statistic.StatisticEnum.ATTACK_MONSTER_NUM_GROUPBY_LEVEL;
import static com.yorha.common.enums.statistic.StatisticEnum.ATTACK_POINT_NUM;
import static com.yorha.proto.CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE;
import static com.yorha.proto.CommonEnum.MonsterCategory.NEWBIE_MONSTER;


/**
 * 任务组件
 * 内部包括handler、checker、event子组件
 * handler：承载不同任务模块的处理逻辑，类如每日任务的添加，刷新，解锁等（提供抽象abstractHandler，承担任务事件事件的注册与卸载）
 * checker：承担不同任务类型的进度更新，完成检测
 * event：触发任务更新的消息通知（需要注意的是，taskEvent是player内部的动态事件，区分与静态事件，继承的是PlayerTaskEvent）
 * <p>
 * 新增一个任务模块需要关注的点
 * 1、新增handler
 * 新增一个任务类型需要关注的点
 * 1、新增任务类型对应的checker
 * 2、新增任务进度更新的触发事件
 * 3、checker中注册事件触发
 *
 * <AUTHOR>
 */
public class PlayerTaskComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerTaskComponent.class);

    /**
     * 基础任务模块handler（每日、支线、主线）
     * 任务提供的领奖接口只适用于上面三种
     */
    private final Map<CommonEnum.TaskClass, AbstractTaskHandler> taskHandlerMap = Maps.newEnumMap(CommonEnum.TaskClass.class);

    /**
     * 所有使用了任务框架的handler（每日、支线、主线、通行证、一系列活动）
     */
    private final List<AbstractTaskHandler> allTaskHandlers = Lists.newLinkedList();

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerTaskComponent::monitorDailyRefreshEvent);
        EntityEventHandlerHolder.register(BuildFinEvent.class, PlayerTaskComponent::monitorInnerCityUpgradeEvent);
        EntityEventHandlerHolder.register(PlayerNewbieOverEvent.class, PlayerTaskComponent::monitorNewbieOverEvent);
        EntityEventHandlerHolder.register(PlayerJoinClanEvent.class, PlayerTaskComponent::monitorJoinClanEvent);

        // 扫描所有PlayerTaskEvent，静态监听
        final JavaClassScanner scanner = new JavaClassScanner();
        for (Class<? extends PlayerTaskEvent> taskEventClazz : scanner.getSubTypesOf("com.yorha.cnc.player.event", PlayerTaskEvent.class)) {
            EntityEventHandlerHolder.register(taskEventClazz, PlayerTaskComponent::monitorTaskEvent);
        }
    }

    public PlayerTaskComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        taskHandlerMap.put(CommonEnum.TaskClass.TC_DAILY, new DailyTaskHandler(getOwner(), CommonEnum.TaskClass.TC_DAILY));
        taskHandlerMap.put(CommonEnum.TaskClass.TC_MAIN, new MainTaskHandler(getOwner(), CommonEnum.TaskClass.TC_MAIN));
        taskHandlerMap.put(CommonEnum.TaskClass.TC_BRANCH, new BranchTaskHandler(getOwner(), CommonEnum.TaskClass.TC_BRANCH));
        taskHandlerMap.put(CommonEnum.TaskClass.TC_CHAPTER, new ChapterTaskHandler(getOwner(), CommonEnum.TaskClass.TC_CHAPTER));
        taskHandlerMap.put(CommonEnum.TaskClass.TC_PVE, new PveTaskHandler(getOwner(), CommonEnum.TaskClass.TC_PVE));
    }

    @Override
    public void onLoad(boolean isRegister) {
        // 给自己来个消息，等各个模块都load完毕后统一发调check事件检测一下进度
        ownerActor().tell(ownerActor().self(), new ActorRunnable<PlayerActor>("taskCheckProcess", actor -> {
            try {
                new CheckTaskProcessEvent(getOwner()).dispatch();
            } catch (Exception e) {
                LOGGER.error("PlayerTaskComponent onLoad taskCheckProcess", e);
            }
        }));

        for (AbstractTaskHandler handler : taskHandlerMap.values()) {
            //if (handler instanceof BranchTaskHandler && getOwner().getNewbieComponent().isNewbie()) {
            //    continue;
            //}
            handler.openAttention();
            handler.loadAllTask();
        }
        allTaskHandlers.addAll(taskHandlerMap.values());
    }

    /**
     * 往任务监听系统里添加一个模块
     * <p>
     * 需要调用者手动openAttention、手动loadAllTask、手动触发checkTaskProcess
     */
    public void addTaskHandler(AbstractTaskHandler handler) {
        allTaskHandlers.add(handler);
    }

    public void removeTaskHandler(AbstractTaskHandler handler) {
        allTaskHandlers.remove(handler);
    }

    public int getHeatValue() {
        return getOwner().getProp().getTaskSystem().getHeatValue();
    }

    /**
     * 增加活跃度
     */
    public void addHeatValue(int addValue) {
        if (addValue <= 0) {
            LOGGER.error("add heat value fail, {} <= 0", addValue);
            return;
        }
        int finValue = getHeatValue() + addValue;
        LOGGER.info("add heatValue, oldValue:{}, newValue:{}", getHeatValue(), finValue);
        getOwner().getProp().getTaskSystem().setHeatValue(finValue);
        new HeatValueTaskEvent(getOwner()).dispatch();

        // 这里运营说要用这个qlog来记这个qlog，不合理但是没啥办法
        try {
            QlogCncActivityScore.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("complete_task_gain_score")
                    .setSubjectID("")
                    .setIcount(addValue)
                    .setActivityScore(finValue)
                    .setActivityScoreID("score_daily_task")
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendDailyTaskScoreQlog addScore failed: ", e);
        }
    }

    /**
     * 重置活跃度
     */
    public void resetHeatValue() {
        LOGGER.info("heat value reset, oldValue:{}", getHeatValue());
        getOwner().getProp().getTaskSystem().setHeatValue(0);
    }

    /**
     * 任务领奖的前置检测
     */
    private void checkTaskRewardState(CommonEnum.TaskClass taskClass, int configId) {
        AbstractTaskHandler handler = taskHandlerMap.get(taskClass);
        if (handler == null) {
            throw new GeminiException(ErrorCode.TASK_REWARD_NOT_EXIST, StringUtils.format("checkTaskRewardState but handler is null {} {}", taskClass, configId));
        }
        TaskInfoProp taskInfoProp = handler.getTaskProp().get(configId);
        if (taskInfoProp == null) {
            throw new GeminiException(ErrorCode.BUILD_TASKID_PARAM_ERROR, StringUtils.format("checkTaskRewardState but taskInfoProp is null {} {}", taskClass, configId));
        }
        if (AbstractTaskHandler.isUnCompletedState(taskInfoProp)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, StringUtils.format("checkTaskRewardState but not completed {} {}", taskClass, configId));
        }
    }

    /**
     * 检测并领取任务奖励
     * 如果参数有问题，会抛异常
     */
    public List<IntPairType> checkAndTakeTaskReward(CommonEnum.TaskClass taskClass, int configId) {
        checkTaskRewardState(taskClass, configId);
        AbstractTaskHandler handler = taskHandlerMap.get(taskClass);
        return handler.checkOrTakeReward(Lists.newArrayList(configId));
    }

    /**
     * 开战相关的任务更新
     */
    public void enterBattle(SsPlayerMisc.EnterBattleCmd ask) {
        switch (ask.getBattleRoleType()) {
            case SOT_CLAN_BUILDING_ARMY: {
                getOwner().getStatisticComponent().recordSingleStatistic(ATTACK_POINT_NUM, 1);
                new PlayerStarAttPointEvent(getOwner()).dispatch();
                break;
            }
            case SOT_MONSTER: {
                MonsterTemplate monsterTemplate = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, ask.getTemplateId());
                CommonEnum.MonsterCategory category = monsterTemplate.getCategory();
                // 仅击杀大世界活跃野怪和新手野怪才会更新统计项
                if (category == BIG_SCENE_ACTIVE || category == NEWBIE_MONSTER || getOwner().getSkynetComponent().isSkynetNormalMonster(ask.getTemplateId())) {
                    getOwner().getStatisticComponent().recordSecondStatistic(ATTACK_MONSTER_NUM_GROUPBY_LEVEL, monsterTemplate.getLevel(), 1);
                }
                new PlayerStarAttCreepsEvent(getOwner(), monsterTemplate.getId(), monsterTemplate.getCategory().getNumber(), monsterTemplate.getLevel()).dispatch();
                break;
            }
            default: {
                break;
            }
        }
    }

    /**
     * 大地图调查任务相关进度更新
     */
    public void updateExploreMapBuildingTask(CommonEnum.MapBuildingType type, boolean isFin) {
        if (type == null || type == CommonEnum.MapBuildingType.MBT_NONE) {
            return;
        }
        if (isFin) {
            getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.EXPLORE_WORLD_BUILDING_NUM_END, type.getNumber(), 1);
            new ExploreMapBuildFinEvent(getOwner(), type).dispatch();
            return;
        }
        getOwner().getStatisticComponent().recordSecondStatistic(StatisticEnum.EXPLORE_WORLD_BUILDING_NUM_START, type.getNumber(), 1);
        new ExploreMapBuildStartEvent(getOwner(), type).dispatch();
    }

    private void onNewbieOver() {
        for (AbstractTaskHandler allTaskHandler : allTaskHandlers) {
            allTaskHandler.onNewbieOver();
        }
    }

    /**
     * 监听零点刷新事件
     */
    public static void monitorDailyRefreshEvent(PlayerDayRefreshEvent event) {
        PlayerTaskComponent taskComponent = event.getPlayer().getTaskComponent();
        AbstractTaskHandler taskHandler = taskComponent.taskHandlerMap.get(CommonEnum.TaskClass.TC_DAILY);
        if (taskHandler instanceof DailyTaskHandler) {
            ((DailyTaskHandler) taskHandler).refreshDailyTask();
            return;
        }
        LOGGER.error("un know task handler class, className:{}", taskHandler.getClass().getName());
    }

    /**
     * 监听任务进度更新事件
     */
    public static <T extends PlayerTaskEvent> void monitorTaskEvent(T event) {
        LOGGER.debug("triggers quest event, event:{}", event);
        PlayerTaskComponent taskComponent = event.getPlayer().getTaskComponent();

        for (AbstractTaskHandler handler : taskComponent.allTaskHandlers) {
            for (Integer configId : Lists.newArrayList(handler.takeAllTaskConfigIdWithEvent(event.getName()))) {
                try {
                    handler.updateProcess(configId, event);
                } catch (Exception e) {
                    LOGGER.error("task process update fail, event:{} handler:{}, configId:{}", handler.getClass(), configId, event, e);
                }

            }
        }
    }

    /**
     * 建筑升级事件
     */
    public static void monitorInnerCityUpgradeEvent(BuildFinEvent event) {
        for (AbstractTaskHandler handler : event.getPlayer().getTaskComponent().allTaskHandlers) {
            handler.onInnerCityUpgrade(event);
        }
    }

    /**
     * 新手完成事件
     */
    private static void monitorNewbieOverEvent(PlayerNewbieOverEvent t) {
        LOGGER.debug("PlayerNewbieOverEvent start");
        t.getPlayer().getTaskComponent().onNewbieOver();
    }

    /**
     * 加入联盟事件
     */
    private static void monitorJoinClanEvent(PlayerJoinClanEvent event) {
        for (AbstractTaskHandler handler : event.getPlayer().getTaskComponent().allTaskHandlers) {
            handler.onJoinClan(event);
        }
    }

    public void completeTasksWithGm() {
        for (AbstractTaskHandler handler : allTaskHandlers) {
            handler.completeTasksWithGm(0);
        }
    }

    public void fullTaskByGm() {
        for (AbstractTaskHandler handler : taskHandlerMap.values()) {
            handler.fullMemoryByGm();
        }
    }

    public void openBranchTaskByGm() {
        taskHandlerMap.get(CommonEnum.TaskClass.TC_BRANCH).onNewbieOver();
    }

    public void closeBranchTaskByGm() {
        taskHandlerMap.get(CommonEnum.TaskClass.TC_BRANCH).clearAllAttention("GM");
        getOwner().getProp().getTaskSystem().getTaskBranch().clear();
    }

    public void completeTasksByTypeWithGm(String taskClass) {
        for (AbstractTaskHandler taskHandler : allTaskHandlers) {
            if (!taskHandler.getTypeClass().toUpperCase(Locale.ROOT).startsWith(taskClass.toUpperCase(Locale.ROOT))) {
                continue;
            }
            taskHandler.completeTasksWithGm(0);
        }
    }

    public void completeTasksByTypeWithGm(String taskClass, int id) {
        for (AbstractTaskHandler taskHandler : allTaskHandlers) {
            if (!taskHandler.getTypeClass().toUpperCase(Locale.ROOT).startsWith(taskClass.toUpperCase(Locale.ROOT))) {
                continue;
            }
            taskHandler.completeTasksWithGm(id);
        }
    }

    public void addProcessByGm(String taskClass, int id, int value) {
        for (AbstractTaskHandler taskHandler : allTaskHandlers) {
            // 活动任务的任务池名字是名字+活动id，需求方表示需要忽视任务id
            if (!taskHandler.getTypeClass().toUpperCase(Locale.ROOT).startsWith(taskClass.toUpperCase(Locale.ROOT))) {
                continue;
            }
            taskHandler.addProcessByGm(id, value);
        }
    }

    /**
     * 判断主线任务是否已完成
     *
     * @param task
     * @return
     */
    public boolean isMainTaskFinish(int task) {
        Int32TaskInfoMapProp taskInfoMapProp = getOwner().getProp().getTaskSystem().getTaskBranch();
        if (!taskInfoMapProp.containsKey(task)) {
            return false;
        }
        CommonEnum.TaskStatus status = taskInfoMapProp.get(task).getStatus();
        if ((status == CommonEnum.TaskStatus.AT_REWARD) || (status == CommonEnum.TaskStatus.AT_NOT_REWARD)) {
            return true;
        }
        return false;
    }

    //获取当前章节完成的任务数
    public int getChapterCompletedTaskNum(int chapter, int exceptId) {
        AbstractTaskHandler taskHandler = taskHandlerMap.get(CommonEnum.TaskClass.TC_CHAPTER);
        int num = 0;
        //获取当前章节任务列表
        Set<Integer> curTasks = ResHolder.getResService(TaskTemplateService.class).getChapterTasks(chapter);
        if (curTasks == null) {
            return num;
        }
        for (Integer configId : curTasks) {
            TaskInfoProp taskProp = taskHandler.getTaskProp().get(configId);
            if (exceptId == configId) {
                continue;
            }
            if (taskHandler.isCompleted(taskProp)) {
                num++;
            }
        }
        return num;
    }
}

package com.yorha.cnc.player.gm.command.clan;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ClanScoreItemProp;
import com.yorha.game.gen.prop.Int32ClanScoreItemMapProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanScoreTemplate;

import java.util.Map;

/**
 * 根据类型增加玩家的军团个人积分，部分来源的军团积分是有限制的
 *
 * <AUTHOR>
 */
public class AddPlayerClanScore implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(AddPlayerClanScore.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int type = Integer.parseInt(args.get("type"));
        int addScore = Integer.parseInt(args.get("addScore"));
        if (type < 0 || addScore <= 0) {
            LOGGER.error("type {} or num {} less than 0", type, addScore);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        LOGGER.debug("want to add type {} score {}", type, addScore);
        ClanScoreTemplate template = ResHolder.getInstance().findValueFromMap(ClanScoreTemplate.class, type);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int dayLimit = template.getDayLimit();
        Int32ClanScoreItemMapProp scoreInfo = actor.getEntity().getProp().getClan().getScoreInfo();
        if (!scoreInfo.containsKey(type)) {
            scoreInfo.addEmptyValue(type);
        }
        ClanScoreItemProp prop = scoreInfo.get(type);
        // 加爆了
        if (prop.getTodayAdd() + addScore <= 0) {
            LOGGER.error("addScore {} too large", addScore);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (prop.getTodayAdd() + addScore > dayLimit) {
            addScore = Math.toIntExact(dayLimit - prop.getTodayAdd());
        }
        if (addScore != 0) {
            prop.setScore(prop.getScore() + addScore).setTodayAdd(prop.getTodayAdd() + addScore);
        }
        actor.getEntity().getPlayerClanComponent().addTotalClanScore(CommonEnum.ClanScoreCategory.CSC_GM, addScore);
    }

    @Override
    public String showHelp() {
        return "AddPlayerClanScore type={} addScore={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_CLAN;
    }
}

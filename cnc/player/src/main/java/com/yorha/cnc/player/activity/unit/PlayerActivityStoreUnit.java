package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.PlayerWeekRefreshEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.qlog.json.money.QlogMoneyConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.ActivityStoreGoodRecordProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ActivityUnitType;
import com.yorha.proto.CommonEnum.Reason;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityStoreGoodsTemplate;
import res.template.ActivityStoreTemplate;

import java.util.List;

/**
 * 活动商店
 *
 * <AUTHOR>
 */
public class PlayerActivityStoreUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityStoreUnit.class);
    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerDayRefreshEvent.class,
            PlayerWeekRefreshEvent.class
    );

    static {
        ActivityUnitFactory.register(ActivityUnitType.AUT_STORE, (owner, prop, template) ->
                new PlayerActivityStoreUnit(owner, prop.getCommonStoreUnit(), template.getActivityMarketId())
        );
    }

    /**
     * 商店id
     */
    private final int storeId;

    public PlayerActivityStoreUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp, int storeId) {
        super(ownerActivity, unitProp);
        this.storeId = storeId;
    }

    @Override
    public void load(boolean isInitial) {

    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        unitProp.getStoreUnit().getGoodRecord().clear();
        LOGGER.info("PlayerActivityStoreUnit {} onExpire storeId: {}", player(), storeId);
    }

    public void bugGood(int goodId) {
        ActivityStoreTemplate storeTemplate = ResHolder.getInstance().getValueFromMap(ActivityStoreTemplate.class, storeId);
        if (!storeTemplate.getGoodsIdList().contains(goodId)) {
            throw new GeminiException(ErrorCode.DISCOUNT_STORE_GOOD_NOT_FOUND);
        }
        ActivityStoreGoodsTemplate template = ResHolder.getInstance().getValueFromMap(ActivityStoreGoodsTemplate.class, goodId);
        ActivityStoreGoodRecordProp goodRecordV = unitProp.getStoreUnit().getGoodRecordV(goodId);
        if (goodRecordV != null && template.getLimit() != 0 && goodRecordV.getBuyCount() >= template.getLimit()) {
            throw new GeminiException(ErrorCode.DISCOUNT_STORE_GOOD_SELL_OUT);
        }
        // 检查道具
        for (IntPairType pairType : template.getCostItemPairList()) {
            if (!player().getItemComponent().hasEnough(pairType.getKey(), pairType.getValue())) {
                throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
            }
        }
        // 增加购买次数
        if (goodRecordV == null) {
            goodRecordV = unitProp.getStoreUnit().addEmptyGoodRecord(goodId).setBuyCount(1);
        } else {
            goodRecordV.setBuyCount(goodRecordV.getBuyCount() + 1);
        }
        // 消耗道具
        for (IntPairType pairType : template.getCostItemPairList()) {
            player().getItemComponent().consume(pairType.getKey(), pairType.getValue(), Reason.ICR_ACTIVITY_EXCHANGE, "classified_mission");
        }
        // 获得道具
        for (IntPairType pairType : template.getGetItemPairList()) {
            player().getItemComponent().addItem(pairType.getKey(), pairType.getValue(), Reason.ICR_ACTIVITY_REWARD, "classified_mission");
        }
        // 购买流水
        player().getQlogComponent().sendShopQLog("classified_mission", "shop_exchange",
                QlogMoneyConfig.getQlogMoneyConfigStr(template.getCostItemPairList()),
                QlogMoneyConfig.getQlogMoneyConfigStr(template.getGetItemPairList()));
        LOGGER.info("{} PlayerActivityStoreUnit buy goodId: {}  nowBuyCount: {}", player(), goodId, goodRecordV.getBuyCount());
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void forceOffImpl() {
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerDayRefreshEvent) {
            // 看看限购是不是日刷的
            ActivityStoreTemplate template = ResHolder.getInstance().getValueFromMap(ActivityStoreTemplate.class, storeId);
            if (template.getRefreshType() != CommonEnum.RefreshType.RTYPE_DAY) {
                return;
            }
            unitProp.getStoreUnit().getGoodRecord().clear();
            LOGGER.info("PlayerActivityStoreUnit {} store record refresh storeId: {}", player(), storeId);
        } else if (event instanceof PlayerWeekRefreshEvent) {
            // 看看限购是不是日刷的
            ActivityStoreTemplate template = ResHolder.getInstance().getValueFromMap(ActivityStoreTemplate.class, storeId);
            if (template.getRefreshType() != CommonEnum.RefreshType.RTYPE_WEEK) {
                return;
            }
            unitProp.getStoreUnit().getGoodRecord().clear();
            LOGGER.info("PlayerActivityStoreUnit {} store record refresh storeId: {}", player(), storeId);
        }
    }

}

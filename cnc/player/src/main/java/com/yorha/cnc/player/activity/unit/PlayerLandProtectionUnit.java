package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.task.AbstractTaskHandler;
import com.yorha.cnc.player.task.ActivityTaskHandler;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivityTaskConf;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskProtectTemplate;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static com.yorha.cnc.player.task.AbstractTaskHandler.setNextTaskState;


public class PlayerLandProtectionUnit extends BasePlayerActivityUnit {

    private static final Logger LOGGER = LogManager.getLogger(PlayerLandProtectionUnit.class);

    private final String taskPoolName;
    private final List<Integer> confTaskIds;
    private AbstractTaskHandler taskHandler;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_LAND_PROTECTION, (owner, prop, template) -> new PlayerLandProtectionUnit(
                owner,
                prop.getCommonTaskUnit(),
                template.getTaskPoolName(),
                template.getActivityTaskIdsList())
        );
    }

    public PlayerLandProtectionUnit(PlayerActivity activity, ActivityUnitProp prop, String taskPoolName, List<Integer> activityTaskIdsList) {
        super(activity, prop);
        this.taskPoolName = taskPoolName;
        this.confTaskIds = activityTaskIdsList;
    }

    @Override
    public void load(boolean isInitial) {
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("activity clear attention");
        }
        taskHandler = new ActivityTaskHandler(ownerActivity.getPlayer(), unitProp.getTaskUnit(), taskPoolName, ownerActivity.getActivityId());
        taskHandler.openAttention();
        taskHandler.loadAllTask();
        // 选最小的任务添加
        LinkedList<Integer> candidateTaskIds = Lists.newLinkedList(confTaskIds);
        candidateTaskIds.removeAll(unitProp.getTaskUnit().getTasks().keySet());
        if (!candidateTaskIds.isEmpty()) {
            Integer newTaskId = Collections.min(candidateTaskIds);
            //上一个任务已领奖结束
            if (newTaskId == 1 || unitProp.getTaskUnit().getTasks().get(newTaskId - 1).getStatus() == CommonEnum.TaskStatus.AT_REWARD) {
                LOGGER.info("PlayerLandProtectionUnit player:{} load task id:{}", player(), newTaskId);
                taskHandler.addBatchTask(Sets.newHashSet(newTaskId));
            }
        }

        ownerActivity.getPlayer().getTaskComponent().addTaskHandler(taskHandler);
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        LOGGER.info("taskUnit expire {}", ownerActivity);
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("activityExpire");
        }
    }

    @Override
    public void forceOffImpl() {
        LOGGER.info("taskUnit forceOffImpl {}", ownerActivity);
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("forceOffImpl");
        }
    }

    @Override
    public boolean isFinished() {
        return ImmutableList.copyOf(unitProp.getTaskUnit().getTasks().values())
                .stream().allMatch(AbstractTaskHandler::isFinishState) && unitProp.getTaskUnit().getTasks().values().size() == ResHolder.getInstance().getListFromMap(TaskProtectTemplate.class).size();
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final int activityTaskId = key.getTaskId();
        Int32TaskInfoMapProp tasks = unitProp.getTaskUnit().getTasks();
        TaskInfoProp taskInfoProp = tasks.get(activityTaskId);
        if (taskInfoProp == null) {
            // 活动任务不存在
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_EXIST);
        }
        if (AbstractTaskHandler.isUnCompletedState(taskInfoProp)) {
            // 活动任务未完成
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_NOT_COMPLATE);
        }

        // 标记领奖
        setNextTaskState(taskInfoProp);

        // 领奖
        ActivityTaskConf taskConf = ResHolder.getResService(ActivityResService.class).getTaskConf(taskPoolName, activityTaskId);
        List<IntPairType> reward = taskConf.getReward();
        AssetPackage rewardPackage = AssetPackage.builder().plusItems(reward).build();
        ownerActivity.getPlayer().getAssetComponent().give(rewardPackage, CommonEnum.Reason.ICR_TASK, taskHandler.formationSubReason(activityTaskId));

        taskHandler.takeRewardQLog(taskInfoProp);
        rsp.setReward(rewardPackage.toPb());

        //添加下一个领土保护任务
        int newTaskId = this.getNextTaskId(activityTaskId);
        LOGGER.info("PlayerLandProtectionUnit handleTakeReward player:{} nextTaskId:{}", player(), newTaskId);
        if (newTaskId > 0) {
            taskHandler.addBatchTask(Sets.newHashSet(newTaskId));
        } else {
            // 所有任务已结束
            onUnitFinished();
        }

    }


    /**
     * 获取下一个领土保护任务的任务id
     *
     * @param activityTaskId 当前任务id
     * @return nextTaskId <0 失败
     */
    private int getNextTaskId(int activityTaskId) {
        TaskProtectTemplate taskProtectTemplate = ResHolder.getInstance().findValueFromMap(TaskProtectTemplate.class, activityTaskId);
        if (taskProtectTemplate == null) {
            return -1;
        }
        return taskProtectTemplate.getFrontTask() > 0 ? taskProtectTemplate.getFrontTask() : -1;
    }
}




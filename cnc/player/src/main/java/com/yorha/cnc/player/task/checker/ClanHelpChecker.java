package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerClanHelpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.CLAN_HELP_NUM_TOTAL;

/**
 * 联盟帮助
 * param1: 次数
 *
 * <AUTHOR>
 */
public class Clan<PERSON>elp<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerClanHelpEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int countConfig = taskParams.getFirst();

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int helpTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(CLAN_HELP_NUM_TOTAL);
            prop.setProcess(Math.min(helpTotal, countConfig));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof PlayerClanHelpEvent) {
                PlayerClanHelpEvent clanHelpEvent = (PlayerClanHelpEvent) event;
                if (clanHelpEvent.getCount() > 0) {
                    if (prop.getProcess() < countConfig) {
                        prop.setProcess(Math.min(prop.getProcess() + clanHelpEvent.getCount(), countConfig));
                    }
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }
}

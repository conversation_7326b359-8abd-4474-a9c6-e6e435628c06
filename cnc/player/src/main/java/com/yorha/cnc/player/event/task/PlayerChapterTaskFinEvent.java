package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 章节子任务完成事件
 *
 * <AUTHOR>
 */
public class PlayerChapterTaskFinEvent extends PlayerTaskEvent {
    private final int taskId;

    public PlayerChapterTaskFinEvent(PlayerEntity player, int taskId) {
        super(player);
        this.taskId = taskId;
    }

    public int getTaskId() {
        return taskId;
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerHeroComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.HeroAddLevelReason;
import com.yorha.common.enums.reason.HeroSkillUpReason;
import com.yorha.common.enums.reason.HeroStarUpReason;
import com.yorha.common.enums.reason.HeroUnlockReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.hero.HeroTemplateService;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.game.gen.prop.PlayerHeroSkillProp;
import com.yorha.proto.CommonEnum;
import res.template.HeroLevelRhTemplate;
import res.template.HeroRhTemplate;
import res.template.HeroStarRhTemplate;

import java.util.Map;


/**
 * <AUTHOR>
 */
public class Hero implements PlayerGmCommand {
    /**
     * 英雄解锁 【Hero type=hero subType=unlock id=英雄id】
     * Hero type=hero subType=unlock //结果所有的有配置技能的英雄
     * Hero type=hero subType=unlock id=200150015 //解锁英雄200150015

     * 英雄升级 【Hero type=hero subType=level  id=英雄id value=目标等级】
     * Hero type=hero subType=level //我的所有英雄只升1级
     * Hero type=hero subType=level id=200150015 //我的指定英雄200150015升1级
     * Hero type=hero subType=level value=8  //我的所有英雄升到8级
     * Hero type=hero subType=level id=200150015 value=8  //我的指定英雄200150015升到8级

     * 英雄升星 【Hero type=hero subType=star id=英雄id value=目标星级】
     * Hero type=hero subType=star //我的所有英雄只升1星级
     * Hero type=hero subType=star id=200150015 //我的指定英雄200150015升1星级
     * Hero type=hero subType=star value=4  //我的所有英雄升到4级
     * Hero type=hero subType=star id=200150015 value=8 //我的指定英雄200150015升到8星级

     *技能升级 【Hero type=skill subType=level id=英雄id skillId=技能id value=目标等级】
     *Hero type=skill subType=level id=200150013 我的英雄200150013的所有技能升一级
     *Hero type=skill subType=level id=200150013 skillId=500131  我的英雄200150013的技能500131升一级
     *Hero type=skill subType=level id=200150013 skillId=500132 value=5 我的英雄200150013的技能500131升到5级
     */
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity playerEntity = actor.getEntity();
        String type = args.get("type");
        String subType = args.get("subType");
        int id = 0;
        String value = args.get("id");
        if (value != null) {
            id = Integer.parseInt(value);
        }
        switch (type) {
            case "hero": {
                this.handleHero(playerEntity, args, id, subType);
                break;
            }
            case "skill": {
                int skillId = 0;
                {
                    String temp = args.get("skillId");
                    if (temp != null) {
                        skillId = Integer.parseInt(temp);
                    }
                }

                int level = 0;
                {
                    String temp = args.get("value");
                    if (temp != null) {
                        level = Integer.parseInt(temp);
                    }
                }
                PlayerHeroProp hero = playerEntity.getHeroComponent().getHeroProp(id);
                this.skillLevelUp(playerEntity, hero, skillId, level);
//
//                this.handleSkill(playerEntity, args, id, skillId, level);
                break;
            }
            default:
                break;
        }

    }

    private void handleHero(PlayerEntity playerEntity, Map<String, String> args, int id, String subType) {
        switch (subType) {
            case "unlock": {
                if (id == 0) {
                    ResHolder.getInstance().getListFromMap(HeroRhTemplate.class)
                            .stream()
                            .filter(it -> it.getRecruitShardAmount() > 0).filter(it -> it.getSkill1Pair() != null)
                            .filter(it -> !playerEntity.getHeroComponent().hasHeroProp(it.getId()))
                            .forEach(it -> playerEntity.getHeroComponent().initHero(it.getId(), HeroUnlockReason.GM_UNLOCK));
                } else {
                    HeroRhTemplate valueFromMap = ResHolder.getInstance().findValueFromMap(HeroRhTemplate.class, id);
                    if (valueFromMap == null) {
                        throw new GeminiException(ErrorCode.HERO_HERO_NULL);
                    }
                    if (!playerEntity.getHeroComponent().hasHeroProp(id)) {
                        playerEntity.getHeroComponent().initHero(id, HeroUnlockReason.GM_UNLOCK);
                    }
                }
                break;
            }
            case "level": {
                int level = 0;
                String temp = args.get("value");
                if (temp != null) {
                    level = Integer.parseInt(temp);
                }

                if (id == 0) {
                    for (PlayerHeroProp heroProp : playerEntity.getProp().getPlayerHeroModel().getPlayerHeroMap().values()) {
                        this.levelUp(playerEntity, heroProp, level);
                    }
                } else {

                    PlayerHeroProp heroProp = playerEntity.getProp().getPlayerHeroModel().getPlayerHeroMap().get(id);
                    if (heroProp != null) {
                        this.levelUp(playerEntity, heroProp, level);
                    }
                }
                break;
            }
            case "star": {
                int star = 0;
                String temp = args.get("value");
                if (temp != null) {
                    star = Integer.parseInt(temp);
                }
                if (id == 0) {
                    for (PlayerHeroProp heroProp : playerEntity.getProp().getPlayerHeroModel().getPlayerHeroMap().values()) {
                        this.starUp(playerEntity, heroProp, star);
                    }
                } else {
                    PlayerHeroProp heroProp = playerEntity.getProp().getPlayerHeroModel().getPlayerHeroMap().get(id);
                    if (heroProp != null) {
                        this.starUp(playerEntity, heroProp, star);
                    }
                }
                break;
            }
            default:
                break;
        }
    }


    private void levelUp(PlayerEntity playerEntity, PlayerHeroProp heroProp, int level) {
        if (level == 0) {
            this.tryLevelUpHero(playerEntity, heroProp);
        } else {
            if (heroProp.getLevel() < level) {
                int times = level - heroProp.getLevel();
                for (int i = 0; i < times; i++) {
                    this.tryLevelUpHero(playerEntity, heroProp);
                }
            }
        }
    }


    private void tryLevelUpHero(PlayerEntity playerEntity, PlayerHeroProp heroProp) {
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        HeroLevelRhTemplate template = resService.getHeroLevelTemplate(heroProp.getHeroId(), heroProp.getLevel() + 1);
        if (template != null) {
            PlayerHeroComponent heroComponent = playerEntity.getHeroComponent();
            heroComponent.execAddHeroLevel(heroProp, HeroAddLevelReason.GM_ADD_LEVEL);
        }
    }


    private void starUp(PlayerEntity playerEntity, PlayerHeroProp heroProp, int star) {
        if (star == 0) {
            this.tryStarUpHero(playerEntity, heroProp);
        } else {
            if (heroProp.getStar() < star) {
                int times = star - heroProp.getStar();
                for (int i = 0; i < times; i++) {
                    this.tryStarUpHero(playerEntity, heroProp);
                }
            }
        }
    }

    private void tryStarUpHero(PlayerEntity playerEntity, PlayerHeroProp heroProp) {
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        HeroStarRhTemplate template = resService.getHeroStarTemplate(heroProp.getStar() + 1);
        if (template != null) {
            PlayerHeroComponent heroComponent = playerEntity.getHeroComponent();
            heroComponent.handleHeroStageUp(heroProp.getHeroId(), false, HeroStarUpReason.GM_ADD_STAR);
        }
    }


    public static void upgrade(PlayerEntity playerEntity, PlayerHeroProp heroProp) {
        playerEntity.getHeroComponent().handleLevelUp2Max(heroProp, HeroAddLevelReason.GM_ADD_EXP);
        playerEntity.getHeroComponent().handleStageUp2Max(heroProp, HeroStarUpReason.GM_ADD_STAR);
        for (PlayerHeroSkillProp prop : heroProp.getSkillStore().values()) {
            while (ResHolder.getResService(HeroTemplateService.class).getHeroSkillLevelTemplate(prop.getSkillGroupId(), prop.getSkillLevel() + 1) != null) {
                playerEntity.getHeroComponent().handleSkillLevelUp(heroProp.getHeroId(), prop.getSkillGroupId(), HeroSkillUpReason.GM_ADD_SKILL);
            }
        }

        playerEntity.getHeroComponent().postHeroPropChange(heroProp.getHeroId(), false);
    }


    private void skillLevelUp(PlayerEntity playerEntity, PlayerHeroProp heroProp, int skillId, int level) {
        if (skillId == 0) {
            // 升级所有技能
            for (PlayerHeroSkillProp prop : heroProp.getSkillStore().values()) {
                levelUpSkill(playerEntity, heroProp, prop, level);
            }
        } else {
            // 升级指定技能
            PlayerHeroSkillProp prop = heroProp.getSkillStore().get(skillId);
            if (prop != null) {
                levelUpSkill(playerEntity, heroProp, prop, level);
            }
        }
    }

    private void levelUpSkill(PlayerEntity playerEntity, PlayerHeroProp heroProp, PlayerHeroSkillProp skillProp, int targetLevel) {
        int currentLevel = skillProp.getSkillLevel();
        int skillGroupId = skillProp.getSkillGroupId();
        int finalTargetLevel;
        if (targetLevel == 0) {
            // 只升一级
            finalTargetLevel = currentLevel + 1;
        } else {
            // 升到指定等级
            finalTargetLevel = targetLevel;
        }

        // 计算需要升级的次数
        int times = Math.max(0, finalTargetLevel - currentLevel);

        // 执行升级
        for (int i = 0; i < times; i++) {
            playerEntity.getHeroComponent().handleSkillLevelUp(heroProp.getHeroId(), skillGroupId, HeroSkillUpReason.GM_ADD_SKILL);
        }
    }


    @Override
    public String showHelp() {
        return "Hero type={hero skill} subType={unlock level star} id={} value={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_HERO_PLANE_SOLDIER;
    }
}

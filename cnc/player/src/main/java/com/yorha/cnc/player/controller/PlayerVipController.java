package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerVipStore;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * <AUTHOR>
 */

@Controller(module = CommonEnum.ModuleEnum.ME_VIP)
public class PlayerVipController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerVipController.class);

    /**
     * 拉取Vip每日宝箱
     */
    @CommandMapping(code = MsgType.PLAYER_GETVIPDAILYBOX_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCommon.Player_GetVipDailyBox_C2S msg) {
        if (!msg.hasVipLevel()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return playerEntity.getVipComponent().getVipDailyBox(msg.getVipLevel());
    }


    /**
     * 拉取Vip经验宝箱
     */
    @CommandMapping(code = MsgType.PLAYER_GETVIPPRIVILEGEBOX_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCommon.Player_GetVipPrivilegeBox_C2S msg) {
        return playerEntity.getVipComponent().getVipExpBox();
    }

    /**
     * 拉取Vip商店信息
     */
    @CommandMapping(code = MsgType.PLAYER_GETVIPSTOREINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerVipStore.Player_GetVipStoreInfo_C2S msg) {
        PlayerVipStore.Player_GetVipStoreInfo_S2C.Builder res = playerEntity.getVipComponent().getVipStoreInfo();
        return res.build();
    }

    /**
     * 购买Vip商店道具
     */
    @CommandMapping(code = MsgType.PLAYER_BUYVIPSTOREITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerVipStore.Player_BuyVipStoreItem_C2S msg) {
        PlayerVipStore.Player_BuyVipStoreItem_S2C.Builder res = PlayerVipStore.Player_BuyVipStoreItem_S2C.newBuilder();
        res.setInfo(playerEntity.getVipComponent().buyVipItemByGoodsId(msg.getConfigGoodsId(), msg.getBuyNum(), msg.getSPassWord()));
        return res.build();
    }

    /**
     * 选择Vip英雄道具
     */
    @CommandMapping(code = MsgType.PLAYER_SELECTVIPHEROITEM_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerVipStore.Player_SelectVipHeroItem_C2S msg) {
        if (!msg.hasSource() || !msg.hasHeroId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        switch (msg.getSource()) {
            case VHIS_VIP_DAILY_BOX:
                if (!msg.hasQualityId()) {
                    LOGGER.warn("Player_SelectVipHeroItem_C2S qualityId is null");
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
                }
                playerEntity.getVipComponent().selectVipDailyBoxHero(msg.getQualityId(), msg.getHeroId());
                break;
            case VHIS_VIP_STORE:
                if (!msg.hasStoreItemId()) {
                    LOGGER.warn("Player_SelectVipHeroItem_C2S storeItemId is null");
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
                }
                playerEntity.getVipComponent().selectVipStoreHero(msg.getStoreItemId(), msg.getHeroId());
                break;
            case VHIS_NONE:
                LOGGER.warn("Player_SelectVipHeroItem_C2S source is null");
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            default:
                LOGGER.warn("Player_SelectVipHeroItem_C2S source is wrong");
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return PlayerVipStore.Player_SelectVipHeroItem_S2C.getDefaultInstance();
    }
}

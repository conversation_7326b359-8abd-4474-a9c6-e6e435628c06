package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerMoveCityFixedEvent;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MoveCityType;
import com.yorha.proto.PlayerCommon.Player_UseItem_S2C;
import com.yorha.proto.PlayerPB.ItemUseResultPB;
import com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk;
import com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns;
import com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk;
import com.yorha.proto.StructPB.PointPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

/**
 * <AUTHOR>
 * <p>
 * 固定位置迁城
 */
public class MoveCityUsableItem extends AbstractUsableItem {

    private static final Logger LOGGER = LogManager.getLogger(MoveCityUsableItem.class);
    private final MoveCityType moveCityType;
    /**
     * 迁城坐标点
     */
    protected Point point;

    public MoveCityUsableItem(int num, ItemTemplate itemTemplate, MoveCityType moveCityType) {
        super(num, itemTemplate);
        this.moveCityType = moveCityType;
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        PointProp point = params.getPoint();
        if (point == null) {
            throw new GeminiException(ErrorCode.MAP_MOVE_CITY_INVAILD_POINT.getCodeId());
        }
        if (num != 1) {
            throw new GeminiException(ErrorCode.ITEM_BATCH_USE_LIMIT);
        }
        this.point = Point.valueOf(point.getX(), point.getY());
        MoveCityVerifyAsk.Builder call = MoveCityVerifyAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        call.setMoveType(moveCityType).setX(point.getX()).setY(point.getY());
        // 补充需求  在领土范围上没有领土迁城时，可以使用定点迁城代替领土迁城
        if (params.getIsReplaceTerritoryType()) {
            call.setMoveType(MoveCityType.MCT_TERRITORY);
        }
        MoveCityVerifyAns ans = playerEntity.ownerActor().callCurScene(call.build());
        if (!ErrorCode.isOK(ans.getErrorCode())) {
            throw new GeminiException(ans.getErrorCode());
        }
    }

    @Override
    public CommonEnum.Reason getItemReason(PlayerEntity playerEntity) {
        return CommonEnum.Reason.ICR_MOVE_CITY;
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        MoveCityFixedAsk.Builder call = MoveCityFixedAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        call.setMoveType(moveCityType).setX(point.getX()).setY(point.getY());
        // 补充需求  在领土范围上没有领土迁城时，可以使用定点迁城代替领土迁城
        if (params.getIsReplaceTerritoryType()) {
            call.setMoveType(MoveCityType.MCT_TERRITORY);
        }
        playerEntity.ownerActor().callCurScene(call.build());
        LOGGER.info("player use move city item {} reward to point {}", this, point);
        new PlayerMoveCityFixedEvent(playerEntity, point).dispatch();
        return true;
    }

    @Override
    public void responseMessage(Player_UseItem_S2C.Builder response) {
        ItemUseResultPB.Builder resultPB = ItemUseResultPB.newBuilder();
        resultPB.setPoint(PointPB.newBuilder().setX(point.getX()).setY(point.getY()).build());
        response.setResult(resultPB);
    }
}

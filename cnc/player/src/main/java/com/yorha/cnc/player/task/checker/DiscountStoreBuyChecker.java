package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.DiscountStoreBuyEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 黑市购买次数
 * param1: 次数
 *
 * <AUTHOR>
 */
public class DiscountStoreBuyChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(DiscountStoreBuyEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer times = taskParams.getFirst();

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int discountStoreBuyTotal = (int) entity.getStatisticComponent().getSingleStatistic(StatisticEnum.DISCOUNT_STORE_BUY_TOTAL);
                prop.setProcess(Math.min(discountStoreBuyTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                int discountStoreBuyNum = ((DiscountStoreBuyEvent) event).getBuyNum();
                prop.setProcess(Math.min(prop.getProcess() + discountStoreBuyNum, times));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

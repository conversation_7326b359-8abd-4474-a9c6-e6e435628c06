package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.ClanBuildingInfoEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanTerritory;
import com.yorha.proto.StructClan;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;


/**
 * 联盟是否建成主基地
 */
public class ClanHasMainBase<PERSON>hecker extends AbstractTaskChecker {
    public static List<String> attentionList = Lists.newArrayList(ClanBuildingInfoEvent.class.getSimpleName(), PlayerJoinClanEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int countConfig = 1;

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            // 剪枝：任务已完成
            if (prop.getProcess() >= countConfig) {
                return true;
            }
            final CommonEnum.MapBuildingType targetType = CommonEnum.MapBuildingType.MBT_MAIN_BASE;
            StructClan.ClanBuildingInfo mainBaseInfo = null;
            if (event instanceof ClanBuildingInfoEvent) {
                ClanBuildingInfoEvent clanBuildingInfoEvent = (ClanBuildingInfoEvent) event;
                mainBaseInfo = clanBuildingInfoEvent.getBuildingInfo().get(targetType.getNumber());
            } else {
                if (event.getPlayer().getClanId() == 0) {
                    return prop.getProcess() >= countConfig;
                }
                if (event instanceof PlayerJoinClanEvent) {
                    // 创建联盟时跳过callScene，避免时序问题
                    PlayerJoinClanEvent playerJoinClanEvent = (PlayerJoinClanEvent) event;
                    if (playerJoinClanEvent.isPlayerCreate) {
                        return prop.getProcess() >= countConfig;
                    }
                }

                SsClanTerritory.FetchClanBuildingInfoAns ans = event.getPlayer().ownerActor().callCurClan(
                        SsClanTerritory.FetchClanBuildingInfoAsk.newBuilder()
                                .setClanId(event.getPlayer().getProp().getClan().getClanId())
                                .build());

                mainBaseInfo = ans.getClanBuildingMap().get(targetType.getNumber());
            }

            int curMainBaseNum = mainBaseInfo == null ? 0 : mainBaseInfo.getBuiltNum();
            // 不回退进度
            prop.setProcess(Math.max(prop.getProcess(), curMainBaseNum));
            return prop.getProcess() >= countConfig;
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }

        return prop.getProcess() >= countConfig;
    }
}


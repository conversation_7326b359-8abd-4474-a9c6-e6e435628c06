package com.yorha.cnc.player;

import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.TextFormat;
import com.yorha.cnc.player.actorservice.*;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.chat.component.ChatPlayerComponent;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.cnc.player.friend.component.FriendPlayerComponent;
import com.yorha.cnc.player.offline.PlayerOfflineViewEntity;
import com.yorha.common.actor.*;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.actor.node.IPlayerActor;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.actorservice.IMsgContext;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actorservice.msg.GeminiCompletionStage;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteByPartKeyAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.Command;
import com.yorha.common.io.CommandMgr;
import com.yorha.common.io.MsgType;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.notification.INotificationBuilder;
import com.yorha.common.notification.PushNotificationManager;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.player.AvatarResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ChatChannel;
import com.yorha.proto.CommonEnum.SessionCloseReason;
import com.yorha.proto.Core;
import com.yorha.proto.System;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 玩家actor，持有玩家的状态(playerEntity)，分发玩家的行为
 * 纯策划业务内的逻辑在 playerEntity & component中实现
 *
 * <AUTHOR>
 */
public class PlayerActor extends GameActorWithCall implements PlayerServices, IPlayerActor, IActorWithTimer {
    private static final Logger LOGGER = LogManager.getLogger(PlayerActor.class);
    public static final String PLAYER_ENTITY_PLAYER = "PLAYER";
    public static final String PLAYER_ENTITY_CHAT = "CHAT";
    public static final String PLAYER_ENTITY_FRIEND = "FRIEND";
    public static final String PLAYER_ENTITY_OFFLINE_VIEW = "OFFLINE_VIEW";
    /**
     * 推送
     */
    private final PushNotificationManager notificationManager = new PushNotificationManager();

    private final long playerId;
    /**
     * 玩家下线或移民立即置为负数
     */
    private int curZoneId = -1;
    private PlayerEntity playerEntity;
    private FriendPlayerEntity friendPlayerEntity;
    private ChatPlayerEntity chatPlayerEntity;
    /**
     * 玩家离线时DB数据的内存映射，
     */
    private PlayerOfflineViewEntity offlineViewEntity;

    private final PlayerBaseServiceImpl baseService;
    private final PlayerMiscServiceImpl miscService;
    private final PlayerClanService clanService;
    private final PlayerDungeonService dungeonService;
    private final PlayerFriendService friendService;
    private final PlayerPaymentService paymentService;
    private final PlayerChatService chatService;
    private final PlayerKingdomService kingdomService;
    private final PlayerIdipService idipService;
    private final PlayerSceneService sceneService;

    public PlayerActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.playerId = Long.parseLong(this.getId());
        baseService = new PlayerBaseServiceImpl(this);
        miscService = new PlayerMiscServiceImpl(this);
        clanService = new PlayerClanServiceImpl(this);
        dungeonService = new PlayerDungeonServiceImpl(this);
        friendService = new PlayerFriendServiceImpl(this);
        paymentService = new PlayerPaymentServiceImpl(this);
        chatService = new PlayerChatServiceImpl(this);
        kingdomService = new PlayerKingdomServiceImpl(this);
        idipService = new PlayerIdipServiceImpl(this);
        sceneService = new PlayerSceneServiceImpl(this);
        // 60秒内没有消息则淘汰
        if (ServerContext.isProdSvr()) {
            setReceiveTimeout(60);
        } else {
            // 测试环境尽快淘汰，暴露问题
            setReceiveTimeout(6);
        }
    }

    public long getPlayerId() {
        return playerId;
    }

    /**
     * 如果为空，抛空指针异常
     */
    public PlayerEntity getEntity() {
        if (playerEntity == null) {
            throw new GeminiException("PlayerEntity is Null, {}", this);
        }
        return playerEntity;
    }

    /**
     * 不会判断是否为空
     */
    public PlayerEntity getEntityOrNull() {
        return playerEntity;
    }

    /**
     * 获取entity
     *
     * @return 如果内存中没有，会触发db捞取
     */
    public PlayerEntity getOrLoadEntity() {
        if (playerEntity == null) {
            // 主动在拉起时把离线展示数据置空，防止玩家登陆时离线数据未被淘汰
            setOfflineViewEntity(null);
            return loadPlayerEntity();
        }
        return playerEntity;
    }

    public void setEntity(PlayerEntity playerEntity) {
        if (this.playerEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_PLAYER).dec();
        }
        this.playerEntity = playerEntity;
        if (this.playerEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_PLAYER).inc();
        }
    }

    /**
     * 如果为空，抛空指针异常
     */
    public FriendPlayerEntity getFriendPlayerEntity() {
        if (friendPlayerEntity == null) {
            throw new GeminiException("FriendPlayerEntity is Null, {}", this);
        }
        return friendPlayerEntity;
    }

    /**
     * 不会判断是否为空
     */
    public FriendPlayerEntity getFriendPlayerEntityOrNull() {
        return friendPlayerEntity;
    }

    /**
     * 获取FriendPlayerEntity
     *
     * @return 如果内存中没有，会触发db捞取
     */
    public FriendPlayerEntity getOrLoadFriendPlayerEntity() {
        if (friendPlayerEntity != null) {
            return friendPlayerEntity;
        }
        long playerId = this.getPlayerId();
        TcaplusDb.FriendPlayerTable.Builder req = TcaplusDb.FriendPlayerTable.newBuilder();
        req.setPlayerId(playerId);
        GetResult<TcaplusDb.FriendPlayerTable.Builder> ans;
        ans = callGameDb(new SelectUniqueAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.warn("load friend player from db fail {} {}", this, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        TcaplusDb.FriendPlayerTable.Builder recordBuilder = ans.value;
        if (recordBuilder == null) {
            LOGGER.warn("load friend player from db fail recordBuilder is null, {} {}", this, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        FriendPlayerProp friendPlayerProp = FriendPlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
        int curZone = getEntityCurZoneId();
        if (curZone != this.getZoneId()) {
            throw new MigrateException(curZone, "PlayerActor getOrLoadFriendPlayerEntity, find player migrate, new zone={}", curZone);
        }
        LOGGER.debug("FriendLog : player {} load prop = {}", this.getPlayerId(), friendPlayerProp);
        // 创建FriendPlayerEntity
        final FriendPlayerEntity friendPlayerEntity = new FriendPlayerEntity(this, friendPlayerProp, recordBuilder.getChangedAttr());
        setFriendPlayerEntity(friendPlayerEntity);

        onFriendEntityLoad();
        return friendPlayerEntity;
    }

    /**
     * 创建FriendPlayerEntity
     */
    public FriendPlayerEntity createFriendPlayerEntity(long playerId) {
        LOGGER.info("{} atPlayer register, playerId={} create friend player entity,", LogKeyConstants.GAME_PLAYER_LOGIN, playerId);
        // 构建初始化数据
        FriendPlayerProp prop = new FriendPlayerProp();
        PlayerCardHeadProp cardHead = getEntity().getCardHead();
        prop.getCardHead().setName(cardHead.getName())
                .setPic(cardHead.getPic())
                .setPicFrame(cardHead.getPicFrame());
        prop.unMarkAll();
        final FriendPlayerEntity friendPlayer = new FriendPlayerEntity(this, prop, null);
        setFriendPlayerEntity(friendPlayer);
        return friendPlayer;
    }

    public void setFriendPlayerEntity(FriendPlayerEntity friendPlayerEntity) {
        if (this.friendPlayerEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_FRIEND).dec();
        }
        this.friendPlayerEntity = friendPlayerEntity;
        if (this.friendPlayerEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_FRIEND).inc();
        }
    }

    public ChatPlayerEntity getChatPlayerEntity() {
        if (chatPlayerEntity == null) {
            throw new GeminiException("ChatPlayerEntity is Null, {}", this);
        }
        return chatPlayerEntity;
    }

    public ChatPlayerEntity getChatPlayerEntityOrNull() {
        return chatPlayerEntity;
    }

    public ChatPlayerEntity getOrLoadChatPlayerEntity() {
        if (chatPlayerEntity != null) {
            return chatPlayerEntity;
        }
        final long playerId = this.getPlayerId();
        TcaplusDb.ChatPlayerTable.Builder req = TcaplusDb.ChatPlayerTable.newBuilder();
        req.setPlayerId(playerId);
        GetResult<TcaplusDb.ChatPlayerTable.Builder> ans;
        ans = callGameDb(new SelectUniqueAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.warn("load chat player from db fail {} {}", this, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        TcaplusDb.ChatPlayerTable.Builder recordBuilder = ans.value;
        if (recordBuilder == null) {
            LOGGER.warn("load chat player from db fail recordBuilder is null, {} {}", this, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        ChatPlayerProp chatPlayerProp = ChatPlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
        int curZone = getEntityCurZoneId();
        if (curZone != this.getZoneId()) {
            throw new MigrateException(curZone, "PlayerActor getOrLoadChatPlayerEntity, find player migrate, new zone={}", curZone);
        }
        LOGGER.debug("ChatLog : player {} load prop = {}", this.getPlayerId(), chatPlayerProp);
        // 创建FriendPlayerEntity
        ChatPlayerEntity entity = new ChatPlayerEntity(this, chatPlayerProp, recordBuilder.getChangedAttr());
        // PlayerActor绑定FriendPlayerEntity
        setChatPlayerEntity(entity);

        onChatEntityLoad();
        return entity;
    }

    /**
     * 创建ChatPlayerEntity
     */
    public ChatPlayerEntity createChatPlayerEntity(long playerId) {
        LOGGER.info("{} atPlayer register, playerId:{} create chat player entity.", LogKeyConstants.GAME_PLAYER_LOGIN, playerId);
        // 构建初始化数据
        ChatPlayerProp prop = new ChatPlayerProp();
        // 插入全服聊天频道
        int chatChannel = ChatChannel.CC_SERVER_VALUE;
        String channelId = String.valueOf(this.getZoneId());
        ChannelInfoProp channelInfoProp = prop.addEmptyChannelInfo(chatChannel);
        channelInfoProp.setChannel(chatChannel)
                .addEmptyItem(channelId).setReadIndex(0)
                .setMaxIndex(0).setVersion(1);
        prop.unMarkAll();
        ChatPlayerEntity chatPlayerEntity = new ChatPlayerEntity(this, prop, null);
        setChatPlayerEntity(chatPlayerEntity);
        return chatPlayerEntity;
    }

    public void setChatPlayerEntity(ChatPlayerEntity chatPlayerEntity) {
        if (this.chatPlayerEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_CHAT).dec();
        }
        this.chatPlayerEntity = chatPlayerEntity;
        if (this.chatPlayerEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_CHAT).inc();
        }
    }

    /**
     * 获取位置信息
     *
     * @return 如果内存中没有，会触发db捞取
     */
    public PlayerOfflineViewEntity getOrLoadOfflineViewEntity() {
        if (this.offlineViewEntity == null) {
            final PlayerOfflineViewEntity viewEntity = PlayerOfflineViewEntity.loadOfflineViewEntity(this, playerId);
            this.setOfflineViewEntity(viewEntity);
            LOGGER.info("{} stage 02. actor={} offlineView={}", LogKeyConstants.GAME_PLAYER_OFFLINE_VIEW, this, this.offlineViewEntity);
        }
        return offlineViewEntity;
    }


    private void setOfflineViewEntity(PlayerOfflineViewEntity entity) {
        if (this.offlineViewEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_OFFLINE_VIEW).dec();
        }
        this.offlineViewEntity = entity;
        if (this.offlineViewEntity != null) {
            MonitorUnit.PLAYER_ACTOR_ENTITY_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_OFFLINE_VIEW).inc();
        }
    }

    /**
     * @return Actor对应Entity的当前zoneId。
     */
    public int getEntityCurZoneId() {
        if (this.curZoneId > 0) {
            return this.curZoneId;
        }
        final TcaplusDb.PlayerTable.Builder req = TcaplusDb.PlayerTable.newBuilder();
        req.setPlayerId(this.getPlayerId());
        // 只抓取curZoneId部分
        final List<String> fieldList = Collections.singletonList("curZoneId");
        final GetOption build = GetOption.newBuilder().withFieldNames(fieldList).withGetAllFields(false).build();

        final GetResult<TcaplusDb.PlayerTable.Builder> ans;
        ans = this.callGameDb(new SelectUniqueAsk<>(req, build));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.warn("PlayerActor getCurZoneId, get curZoneId from db failed, ans={}", ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        final TcaplusDb.PlayerTable.Builder recordBuilder = ans.value;
        if (recordBuilder == null) {
            LOGGER.warn("PlayerActor getCurZoneId, get curZoneId from db failed, ans value is null, ans={}", ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        this.curZoneId = recordBuilder.getCurZoneId();
        return this.curZoneId;
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    public void onStopping() {
        super.onStopping();
        PlayerEntity player = getEntityOrNull();
        if (player != null) {
            player.kickOffMe(SessionCloseReason.SCR_SERVER_ALREADY_CLOSE);
        }
    }

    /**
     * 1. 取消propListener注册
     * 2. Entity各component onDestroy
     * 3. Prop检查数据标脏，同步落库
     * 4. Actor淘汰
     *
     * @param reason destroyReason
     */
    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
        // 避免各component onDestroy时触发propListener
        this.stopPropChangeListener();
        // 如果没下线要触发踢人
        try {
            PlayerEntity player = getEntityOrNull();
            if (player != null && player.isOnline()) {
                // 这里不用担心丢prop变化问题，因为entityDestroy中一定保证数据完整落库
                player.kickOffMe(SessionCloseReason.SCR_OPEN_ZONE);
            }
        } catch (Exception e) {
            // kickOffMe里有告知大世界离线，用的call，出现过timeout
            LOGGER.error("handleActorDestroyMsg reason={} ", reason, e);
        }
        // 销毁entity
        this.entityDestroy();
        // 设置所有entity为null
        this.setEntityAndSubEntityNull();
        // 重置curZoneId
        curZoneId = -1;
    }


    /**
     * destroy Entity
     */
    private void entityDestroy() {
        if (this.getEntityOrNull() != null) {
            this.getEntityOrNull().deleteObj();
        }

        if (this.getFriendPlayerEntityOrNull() != null) {
            this.getFriendPlayerEntityOrNull().deleteObj();
        }

        if (this.getChatPlayerEntityOrNull() != null) {
            this.getChatPlayerEntityOrNull().deleteObj();
        }
    }


    /**
     * 设置PlayerEntity以及相关子Entity为null。
     */
    private void setEntityAndSubEntityNull() {
        this.setEntity(null);
        this.setFriendPlayerEntity(null);
        this.setChatPlayerEntity(null);
        this.setOfflineViewEntity(null);
    }

    /**
     * 停用PropChangeListener（Player相关各PropertyChangeListener应为CanStopPropertyChangeListener）
     */
    private void stopPropChangeListener() {
        // Player
        PlayerEntity playerEntity = getEntityOrNull();
        if (playerEntity != null) {
            PropertyChangeListener playerPropListener = playerEntity.getProp().getListener();
            if (playerPropListener != null) {
                ((CanStopPropertyChangeListener) playerPropListener).stop();
            }
        }

        // FriendPlayer
        FriendPlayerEntity friendPlayerEntity = getFriendPlayerEntityOrNull();
        if (friendPlayerEntity != null) {
            PropertyChangeListener friendPropListener = friendPlayerEntity.getProp().getListener();
            if (friendPropListener != null) {
                ((CanStopPropertyChangeListener) friendPropListener).stop();
            }
        }

        // ChatPlayer
        ChatPlayerEntity chatPlayerEntity = getChatPlayerEntityOrNull();
        if (chatPlayerEntity != null) {
            PropertyChangeListener chatPropListener = chatPlayerEntity.getProp().getListener();
            if (chatPropListener != null) {
                ((CanStopPropertyChangeListener) chatPropListener).stop();
            }
        }
    }

    private boolean isPlayerStillSaveDb() {
        PlayerEntity playerEntity = getEntityOrNull();
        if (playerEntity == null) {
            return false;
        }
        PlayerProp prop = playerEntity.getProp();
        if (prop == null) {
            return false;
        }
        PropertyChangeListener listener = prop.getListener();
        if (listener != null) {
            // 判断prop是否还没落库完成
            if (listener.isInTaskQueue()) {
                return true;
            }
        }
        // 判断prop是否还没收脏完成
        return prop.hasAnyMark();
    }

    private boolean isFriendPlayerStillSaveDb() {
        FriendPlayerEntity friendPlayerEntity = getFriendPlayerEntityOrNull();
        if (friendPlayerEntity == null) {
            return false;
        }
        FriendPlayerProp prop = friendPlayerEntity.getProp();
        if (prop == null) {
            return false;
        }
        PropertyChangeListener listener = prop.getListener();
        if (listener != null) {
            // 判断prop是否还没落库完成
            if (listener.isInTaskQueue()) {
                return true;
            }
        }
        // 判断prop是否还没收脏完成
        return prop.hasAnyMark();
    }

    private boolean isChatPlayerStillSaveDb() {
        ChatPlayerEntity chatPlayerEntity = getChatPlayerEntityOrNull();
        if (chatPlayerEntity == null) {
            return false;
        }
        ChatPlayerProp prop = chatPlayerEntity.getProp();
        if (prop == null) {
            return false;
        }
        PropertyChangeListener listener = prop.getListener();
        if (listener != null) {
            // 判断prop是否还没落库完成
            if (listener.isInTaskQueue()) {
                return true;
            }
        }
        // 判断prop是否还没收脏完成
        return prop.hasAnyMark();
    }

    @Override
    protected boolean canDestroy() {
        if (isPlayerStillSaveDb()) {
            return false;
        }
        if (isFriendPlayerStillSaveDb()) {
            return false;
        }
        if (isChatPlayerStillSaveDb()) {
            return false;
        }
        return super.canDestroy();
    }

    /**
     * call玩家所属游戏服大世界
     */
    public <RESP> RESP callSelfBigScene(GeneratedMessageV3 msg) {
        return callScene(this.getBigSceneId(), msg);
    }

    /**
     * call玩家当前所在大世界
     */
    public <RESP> RESP callBigScene(GeneratedMessageV3 msg) {
        return callScene(this.getBigSceneId(), msg);
    }

    /**
     * call指定大世界（k服/游戏服）
     */
    public <RESP> RESP callTargetBigScene(int zoneId, GeneratedMessageV3 msg) {
        return callScene(zoneId, msg);
    }

    /**
     * call 当前场景 bigScene、dungeon
     */
    public <RESP> RESP callCurScene(GeneratedMessageV3 msg) {
        if (getBigSceneId() == getEntity().getCurSceneId()) {
            return callScene(getEntity().getCurSceneId(), msg);
        }
        return callTargetDungeon(getEntity().getSceneMgrComponent().getDungeonRef(), msg);
    }

    public <RESP> RESP callTargetDungeon(IActorRef ref, GeneratedMessageV3 msg) {
        try {
            return call(ref, msg);
        } catch (Exception e) {
            if (e instanceof GeminiException) {
                GeminiException e1 = (GeminiException) e;
                if (e1.getCodeId() == ErrorCode.ACTOR_NOT_EXISTS.getCodeId()) {
                    LOGGER.error("callTargetDungeon ref={}", ref);
                    throw new GeminiException(ErrorCode.DUNGEON_MAP_IS_NULL);
                }
            }
            throw e;
        }
    }

    public void tellBigScene(GeneratedMessageV3 msg) {
        tellBigScene(this.getBigSceneId(), msg);
    }

    /**
     * call 当前场景 bigScene、dungeon
     */
    public void tellCurScene(GeneratedMessageV3 msg) {
        if (getBigSceneId() == getEntity().getCurSceneId()) {
            tellBigScene(msg);
            return;
        }
        tell(getEntity().getSceneMgrComponent().getDungeonRef(), msg);
    }

    public <RESP> RESP callTargetAoiView(int zoneId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofAoiView(zoneId, getPlayerId()), msg);
    }

    public <RESP> RESP callCurClan(GeneratedMessageV3 msg) {
        long clanId = getEntity().getProp().getClan().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }
        return callClan(getEntity().getZoneId(), clanId, msg);
    }

    public void tellClan(long clanId, GeneratedMessageV3 msg) {
        tell(RefFactory.ofClan(getZoneId(), clanId), msg);
    }

    public <RESP> RESP callTargetClan(long clanId, GeneratedMessageV3 msg) {
        return callClan(getEntity().getZoneId(), clanId, msg);
    }

    public <RESP> RESP calZoneClan(int zoneId, long clanId, GeneratedMessageV3 msg) {
        return callClan(zoneId, clanId, msg);
    }

    public void tellCurClan(GeneratedMessageV3 msg) {
        long clanId = getEntity().getProp().getClan().getClanId();
        tellClan(getZoneId(), clanId, msg);
    }

    public <RESP> RESP callCurZoneChat(GeneratedMessageV3 msg) {
        return callZoneChat(getZoneId(), msg);
    }

    public <RESP> RESP callTextFilter(GeneratedMessageV3 msg) {
        try {
            return call(RefFactory.ofTextFilter(), msg);
        } catch (Throwable t) {
            if (t instanceof GeminiException) {
                GeminiException geminiException = (GeminiException) t;
                Throwable cause = geminiException.getCause();
                // 网络原因则不触发框架兜底告警
                if ((cause instanceof SocketTimeoutException) || (cause instanceof ConnectException)) {
                    LOGGER.warn("callTextFilter netWorkError msg={}, t=", msg, t);
                    throw new GeminiException(ErrorCode.TEXT_FILTER_NETWORK_FAIL);
                }
            }
            throw t;
        }

    }

    public <RESP> RESP callMidasAgent(GeneratedMessageV3 msg) {
        try {
            return call(RefFactory.ofMidasAgent(), msg);
        } catch (Throwable t) {
            if (t instanceof GeminiException) {
                GeminiException geminiException = (GeminiException) t;
                Throwable cause = geminiException.getCause();
                // 网络原因则不触发框架兜底告警
                if ((cause instanceof SocketTimeoutException) || (cause instanceof ConnectException)) {
                    LOGGER.warn("callMidasAgent netWorkError msg={}, t=", msg, t);
                    throw new GeminiException(ErrorCode.MIDAS_NETWORK_FAIL);
                }
            }
            throw t;
        }
    }

    public <RESP> GeminiCompletionStage<RESP> askMidasAgent(GeneratedMessageV3 msg) {
        return ask(RefFactory.ofMidasAgent(), msg);
    }

    public <RESP> RESP callSharedChat(int chatChannel, String channelId, GeneratedMessageV3 msg) {
        return callGroupChat(chatChannel, channelId, msg);
    }

    /**
     * 创建playerEntity
     */
    public PlayerEntity createPlayerEntity(String openId, int roleSeqOfAccount) {
        LOGGER.info("{} atPlayer register openId:{} playerId={} create player entity, {}", LogKeyConstants.GAME_PLAYER_LOGIN, openId, playerId, roleSeqOfAccount);
        int pic = RandomUtils.randomList(ResHolder.getResService(AvatarResService.class).getInitPic());
        int picFrame = RandomUtils.randomList(ResHolder.getResService(AvatarResService.class).getInitPicFrame());
        // 构建玩家属性
        PlayerProp prop = new PlayerProp()
                .setId(playerId)
                .setOpenId(openId)
                .setCreateTime(SystemClock.now());
        prop.getAvatarModel().getCardHead().setPic(pic).setPicFrame(picFrame);
        prop.getZoneModel().setZoneId(getZoneId());
        prop.getScene().setCurSceneId(this.getBigSceneId());
        // 自己是账号里第几个角色
        prop.getBasicInfo().setRoleSeqOfAccount(roleSeqOfAccount);
        prop.unMarkAll();
        // 创建playerEntity
        PlayerEntity playerEntity = new PlayerEntity(this, prop, true, null);
        playerEntity.setRegister(true);
        // PlayerActor绑定PlayerEntity
        setEntity(playerEntity);
        return playerEntity;
    }

    /**
     * 加载playerEntity
     */
    private PlayerEntity loadPlayerEntity() {
        LOGGER.info("{} atPlayer load, try load player entity {}", LogKeyConstants.GAME_PLAYER_LOGIN, this);
        if (playerEntity != null) {
            return playerEntity;
        }
        LOGGER.info("{} atPlayer real load, player entity {} cur msg {}", LogKeyConstants.GAME_PLAYER_LOGIN, this, getCurrentEnvelope());

        TcaplusDb.PlayerTable.Builder req = TcaplusDb.PlayerTable.newBuilder();
        req.setPlayerId(playerId);
        GetResult<TcaplusDb.PlayerTable.Builder> ans = callGameDb(new SelectUniqueAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.warn("load player from db fail {} {}", this, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        TcaplusDb.PlayerTable.Builder recordBuilder = ans.value;
        if (recordBuilder == null) {
            LOGGER.warn("load player from db fail recordBuilder is null, {} {}", this, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        // 通知大世界捞起entity
        PlayerEntity playerEntity = loadPlayerEntity(recordBuilder);
        this.curZoneId = recordBuilder.getCurZoneId();
        return playerEntity;
    }

    public PlayerEntity loadPlayerEntity(TcaplusDb.PlayerTable.Builder recordBuilder) {
        final PlayerProp playerProp = PlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
        final PlayerEntity playerEntity = new PlayerEntity(this, playerProp, false, recordBuilder.getChangedAttr());
        // PlayerActor绑定PlayerEntity
        setEntity(playerEntity);
        onLoad(false);
        return playerEntity;
    }

    public void onLoad(boolean isRegister) {
        // 调用玩家所有组件的 onLoad
        playerEntity.callAllComponentsSafe(playerComponent -> playerComponent.onLoad(isRegister));
        // 调用玩家所有组件的 postLoad
        playerEntity.callAllComponentsSafe(playerComponent -> playerComponent.postLoad(isRegister));
        // 消费所有离线RPC
        this.consumeAllOfflineRpcSafe();
    }

    public void onFriendEntityLoad() {
        // 调用好友所有组件的onLoad
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::onLoad);
        // 调用好友所有组件的postLoad
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::postLoad);
    }

    public void onChatEntityLoad() {
        // 调用所有聊天组件的onLoad
        chatPlayerEntity.callAllComponentsSafe(ChatPlayerComponent::onLoad);
        // 调用所有聊天组件的postLoad
        chatPlayerEntity.callAllComponentsSafe(ChatPlayerComponent::postLoad);
    }

    /**
     * 要么传第三个参数，要么传第四个参数
     */
    @Override
    public void onReceiveClientMsg(final int msgType, final int seqId, final byte[] content, final ByteString msgBytes) {
        int s2cMsgType = MsgType.getRetMsgId(msgType);
        if (s2cMsgType == 0) {
            LOGGER.info("cannot find s2cMsgId with c2sMsgId {}", msgType);
            return;
        }

        PlayerEntity playerEntity = this.getEntityOrNull();
        if (playerEntity == null) {
            // 可能是移民完成后，GateSessionActor还没意识到，继续传递消息拉起PlayerActor导致
            LOGGER.warn("onReceiveClientMsg msgType:{} player is null", msgType);
            return;
        }

        if (!shouldHandleMsg(playerEntity, msgType)) {
            return;
        }

        GeneratedMessageV3 msg;
        try {
            if (content != null) {
                // lambda传递，同进程，省去序列化
                msg = MsgUtils.parseCsProto(msgType, content);
            } else {
                // ss传递，gate与player不在同一进程
                msg = MsgUtils.parseCsProto(msgType, msgBytes);
            }
        } catch (Exception e) {
            LOGGER.warn("onReceiveClientMsg fail, msgType={} seqId={} player={}", msgType, seqId, getId());
            return;
        }

        if (msg == null) {
            LOGGER.error("onReceiveClientMsg but msg is null, msgType={} seqId={} player={}", msgType, seqId, getId());
            return;
        }

        final Command command = CommandMgr.getInstance().getCommand(msgType);
        // 功能封禁
        if (isModuleLocked(playerEntity, command)) {
            SessionHelper.sendErrorCodeToSession(sender(), this, s2cMsgType, seqId, ErrorCode.FEATURE_LOCK.getCode());
            LOGGER.info("feature_lock PlayerActor onReceiveClientMsg {} {} {}", playerEntity, command.getModule().name(), msgType);
            return;
        }

        logMsg(msgType, playerEntity, msg);

        Core.Code retCode = null;
        GeneratedMessageV3 ret = null;
        try {
            playerEntity.getPlayerPropComponent().beginHandleCsMsg();
            // 已经登录后，传递的第一个参数为PlayerEntity
            ret = (GeneratedMessageV3) command.executeFromPlayer(playerEntity, msg, seqId);
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                WechatLog.error("exception_perf handle cs msg fail! type:{} name:{}", msgType, command.getName(), e);
            }
            retCode = ErrorCodeUtils.getCodeFromException(e);
        }

        // 假如上述executeFromPlayer抛异常，则直到下次c2s前，属性都不会增量下发，观察是否需要修改
        // 处理客户端请求时，要在客户端回包之前flush属性变更，下发客户端
        this.finishHandleCsMsg();

        // 错误码下发
        if (retCode != null) {
            LOGGER.info("onReceiveClientMsg sendErrorCode={} c2sMsgType={} msg={}", retCode, msgType, msg);
            SessionHelper.sendErrorCodeToSession(sender(), this, s2cMsgType, seqId, retCode);
            return;
        }

        // 正常s2c下发
        if (ret == null) {
            // 有接口是使用ask模式在回调中下发S2C的，所以这里没有ret也是合法的（如ViewOtherPlayerBuildings接口）
            return;
        }
        IActorRef sessionRef = playerEntity.getSessionComponent().getSessionRef();
        SessionHelper.sendMsgToSessionWithSeqId(sessionRef, this, s2cMsgType, seqId, ret);
    }

    private boolean shouldHandleMsg(PlayerEntity playerEntity, int msgType) {
        int s2cMsgType = MsgType.getRetMsgId(msgType);
        if (s2cMsgType == 0) {
            LOGGER.info("cannot find s2cMsgId with c2sMsgId {}", msgType);
            return false;
        }

        if (playerEntity == null) {
            // 可能是移民完成后，GateSessionActor还没意识到，继续传递消息拉起PlayerActor导致
            LOGGER.warn("onReceiveClientMsg msgType:{} player is null", msgType);
            return false;
        }

        IActorRef sender = getCurrentEnvelope().getSender();
        IActorRef curSessionRef = playerEntity.getSessionComponent().getSessionRef();
        if (sender == null) {
            LOGGER.error("onReceiveClientMsg sender is null {}", msgType);
            return false;
        }
        if (curSessionRef == null) {
            LOGGER.error("onReceiveClientMsg curSessionRef is null {}", msgType);
            return false;
        }
        if (!sender.getActorId().equals(curSessionRef.getActorId())) {
            // 可能老的session虽然被顶号了，但还是把cs消息发过来了，我不应该处理
            LOGGER.info("onReceiveClientMsg sender={} not equal curSessionRef={} {}", sender, curSessionRef, msgType);
            return false;
        }

        final Command command = CommandMgr.getInstance().getCommand(msgType);
        if (command == null) {
            LOGGER.warn("this command not defined, type={}", msgType);
            return false;
        }
        return true;
    }

    private boolean isModuleLocked(PlayerEntity playerEntity, Command command) {
        Set<CommonEnum.ModuleEnum> globalLockedModule = ServerContext.getGlobalLockedFeatures();
        var moduleLockedGlobal = globalLockedModule != null && globalLockedModule.contains(command.getModule());

        var moduleLockedForPlayer = playerEntity.getFeatureLockComponent().isModuleLock(command.getModule()) && (!command.isModuleWhite());

        return moduleLockedGlobal || moduleLockedForPlayer;
    }

    private void logMsg(int msgType, PlayerEntity playerEntity, GeneratedMessageV3 msg) {
        if (msgType != 3203//64 Player_UpdateView_C2S
                && msgType != 1133//558 PLAYER_FETCHCLANMEMBERCITY_C2S
                && msgType != 1436//1653 PLAYER_TRIGGERQLOG_C2S
                && msgType != 2107//2016 PLAYER_UPDATECLIENTSTAGE_C2S
                && msgType != 2802//1993 PLAYER_FETCHCLANPOSITIONMARK_C2S
                && msgType != 1428//1312 PLAYER_MARKNEWBIESTEP_C2S
        ) {
            LOGGER.info("msg_perf sId={} oId={} pId={} dId={} msgType={} msg={}",
                    playerEntity.getSessionComponent().getSessionId(),
                    playerEntity.getOpenId(),
                    playerEntity.getPlayerId(),
                    playerEntity.getClientInfo().getDeviceId(),
                    msgType,
                    TextFormat.printer().shortDebugString(msg)
            );
        }
    }

    private void finishHandleCsMsg() {
        PlayerEntity playerEntity = this.getEntityOrNull();
        if (playerEntity == null) {
            LOGGER.warn("finishHandleCsMsg but player not load {}", this);
            return;
        }
        playerEntity.getPlayerPropComponent().finishHandleCsMsg();

        FriendPlayerEntity friendPlayerEntity = this.getFriendPlayerEntity();
        friendPlayerEntity.getPropComponent().immediateFlushProp();

        ChatPlayerEntity chatPlayerEntity = this.getChatPlayerEntity();
        chatPlayerEntity.getPropComponent().immediateFlushProp();
    }

    @Override
    public void receivePlayerQlog(AbstractPlayerQlogFlow flow) {
        PlayerEntity playerEntity = getOrLoadEntity();
        flow.fillHead(playerEntity.getQlogComponent());
        flow.sendToQlog();
    }

    @Override
    public PlayerBaseService getPlayerBaseService() {
        return baseService;
    }

    @Override
    public PlayerMiscService getPlayerMiscService() {
        return miscService;
    }

    @Override
    public PlayerPaymentService getPlayerPaymentService() {
        return paymentService;
    }

    @Override
    public PlayerSceneService getPlayerSceneService() {
        return sceneService;
    }

    @Override
    public PlayerClanService getPlayerClanService() {
        return clanService;
    }

    @Override
    public PlayerDungeonService getPlayerDungeonService() {
        return dungeonService;
    }

    @Override
    public PlayerFriendService getPlayerFriendService() {
        return friendService;
    }

    @Override
    public PlayerIdipService getPlayerIdipService() {
        return idipService;
    }

    @Override
    public PlayerChatService getPlayerChatService() {
        return chatService;
    }

    @Override
    public PlayerKingdomService getPlayerKingdomService() {
        return kingdomService;
    }

    /**
     * 设置单次定时器   以name为key 不准重复!!!!!
     * 如果命名中需要夹杂别的参数，请用addTimer(getPlayerId() + "-" + otherId, timerReasonType)的方式处理
     */
    @Nullable
    public ActorTimer addTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(String.valueOf(getPlayerId()), timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    @Nullable
    public ActorTimer addTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(String.valueOf(prefix), timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    @Nullable
    public ActorTimer addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Nullable
    public ActorTimer addRepeatTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(String.valueOf(getPlayerId()), timerReasonType, runnable, initialDelay, period, unit, false);
    }

    @Nullable
    public ActorTimer addRepeatTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, false);
    }

    /**
     * 添加离线RPC，30天过期
     * 消费保序，过期不消费
     */
    public void addOfflineRpc() {
        addOfflineRpc(TimeUtils.second2Ms(60 * 60 * 24 * 30));
    }

    /**
     * 添加离线RPC，自定过期时间
     * 消费保序，过期不消费
     *
     * @param expireDurationMs 过期前保留时间ms
     */
    public void addOfflineRpc(long expireDurationMs) {
        ActorMsgEnvelope offlineRpc = this.getCurrentEnvelope();
        IActorMsg payload = offlineRpc.getPayload();
        // 兜底：错用接口
        if (!(payload instanceof TypedMsg)) {
            WechatLog.error("addOfflineRpc invalid ActorMsgEnvelope! not RPC!");
            return;
        }
        // 兜底：过期时间设置错误
        if (expireDurationMs < 0) {
            WechatLog.error("addOfflineRpc invalid expireDurationMs={}", expireDurationMs);
            return;
        }
        LOGGER.info("addOfflineRpc expireDurationMs={}, msgType={}", expireDurationMs, ((TypedMsg) payload).getMsgType());
        TcaplusDb.OfflineRpcTable.Builder insertReq = TcaplusDb.OfflineRpcTable.newBuilder();
        insertReq.setPlayerId(this.getPlayerId())
                .setRpcId(UUID.randomUUID().toString())
                .setOfflineRpc(System.OffLineRpc.newBuilder()
                        .setExpireTsMs(SystemClock.now() + expireDurationMs)
                        .setMsg(offlineRpc.toProto()));
        InsertAsk<TcaplusDb.OfflineRpcTable.Builder> insertAsk = new InsertAsk<>(insertReq);
        InsertResult<TcaplusDb.OfflineRpcTable.Builder> ans = this.callGameDb(insertAsk);
        if (!DbUtil.isOk(ans.getCode())) {
            throw new GeminiException(ErrorCode.OFFLINE_RPC_INSERT_ERROR);
        }
    }

    /**
     * 消费所有离线RPC(出现异常wechat告警)
     * 按序消费，过期不消费
     */
    private void consumeAllOfflineRpcSafe() {
        TcaplusDb.OfflineRpcTable.Builder getReq = TcaplusDb.OfflineRpcTable.newBuilder();
        getReq.setPlayerId(this.getPlayerId());
        SelectAsk<TcaplusDb.OfflineRpcTable.Builder> selectAsk = new SelectAsk<>(getReq);

        try {
            // 拉取所有离线rpc
            GetByPartKeyResult<TcaplusDb.OfflineRpcTable.Builder> ans = this.callGameDb(selectAsk);

            if (DbUtil.isRecordNotExist(ans.getCode())) {
                return;
            }
            if (!DbUtil.isOk(ans.getCode())) {
                LOGGER.error("PlayerActor consumeAllOfflineRpcSafe, requestId={}, ret code={}", ans.requestId, ans.getCode());
                throw new GeminiException(ErrorCode.OFFLINE_RPC_GET_ERROR);
            }

            // 删除离线rpc（可丢消息，不可多消费）
            deleteAllOfflineRpc();

            // 剪枝
            if (ans.getValues().isEmpty()) {
                return;
            }

            // 链式调用无法获取类型，需显式声明
            Comparator<ValueWithVersion<TcaplusDb.OfflineRpcTable.Builder>> cmp = Comparator
                    .comparing((ValueWithVersion<TcaplusDb.OfflineRpcTable.Builder> i) -> i.value.getOfflineRpc().getMsg().getSendTsMs())
                    .thenComparing(i -> i.value.getOfflineRpc().getMsg().getMsgSeqId());
            // 按发送顺序（sendMs、SeqId）依次消费
            ans.getValues().sort(cmp);

            int consumeCnt = 0;
            for (ValueWithVersion<TcaplusDb.OfflineRpcTable.Builder> rpcInfoBuilder : ans.getValues()) {
                if (this.consumeSingleOfflineRpcSafe(rpcInfoBuilder.value)) {
                    consumeCnt++;
                }
                // 避免大量消费离线RPC导致线程长时间占用
                if (consumeCnt >= 10) {
                    LOGGER.warn("PlayerActor consumeAllOfflineRpcSafe consume offline Rpc over limit, consumeCnt={}", consumeCnt);
                    consumeCnt = 0;
                    // 线程不应yield，应尽快处理完
                    // !!危险操作，非框架勿用!!
                    if (Thread.currentThread().isVirtual()) {
                        Thread.yield();
                    }
                }
            }

        } catch (Exception e) {
            WechatLog.error("consumeAllOfflineRpcSafe error", e);
        }


    }

    /**
     * 消费单条离线RPC(消费出现异常也不阻断)
     *
     * @param rpcInfoBuilder 单条离线RPC DB数据
     * @return 是否满足消费条件
     */
    private boolean consumeSingleOfflineRpcSafe(TcaplusDb.OfflineRpcTable.Builder rpcInfoBuilder) {
        System.OffLineRpc offLineRpc = rpcInfoBuilder.getOfflineRpc();
        long expireTsMs = offLineRpc.getExpireTsMs();
        // 超过过期时间不消费
        if (expireTsMs < SystemClock.now()) {
            LOGGER.info("expired RPC={}, expireTsMs={}, curTsMs={}", offLineRpc.getMsg(), expireTsMs, SystemClock.now());
            return false;
        }
        final IMsgContext context = this.getContext();
        try {
            this.setMsgContext(null);
            ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromProto(offLineRpc.getMsg());
            LOGGER.info("consume RPC={}", offLineRpc.getMsg());
            this.handleActorMsgEnvelope(envelope);
        } catch (GeminiException e) {
            LOGGER.error("PlayerActor consumeSingleOfflineRpcSafe fail, msg={}", offLineRpc.getMsg(), e);
        } catch (Exception e) {
            WechatLog.error("consumeSingleOfflineRpc offLineRpc msg={}", offLineRpc.getMsg());
        } finally {
            this.setMsgContext(context);
        }
        return true;
    }

    /**
     * 删除所有玩家的离线RPC
     */
    private void deleteAllOfflineRpc() {
        TcaplusDb.OfflineRpcTable.Builder deleteReq = TcaplusDb.OfflineRpcTable.newBuilder();
        deleteReq.setPlayerId(this.getPlayerId());
        DeleteByPartKeyAsk<TcaplusDb.OfflineRpcTable.Builder> deleteByPartKeyAsk = new DeleteByPartKeyAsk<>(deleteReq);

        DeleteByPartKeyResult ans = this.callGameDb(deleteByPartKeyAsk);
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.error("PlayerActor deleteAllOfflineRpc, ret code={}", ans.getCode());
            throw new GeminiException(ErrorCode.OFFLINE_RPC_DELETE_ERROR);
        }
    }

    @Override
    protected void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        if (this.playerEntity != null) {
            playerEntity.callAllComponentsSafe(component -> component.onReloadRes(updatedTemplates));
        }
    }

    public void stopAllPropListener() {
        stopPropChangeListener();
    }

    public void notifyPlayer(final INotificationBuilder item) {
        try {
            List<IActorRef> ntfRefs = new ArrayList<>(2);
            // entity不一定存在，两端都推吧
            ntfRefs.add(RefFactory.ofLocalPushNotification());
            this.notificationManager.pushSingleNotification(item, this.playerId, ntfRefs);
        } catch (Exception e) {
            WechatLog.error("notifyPlayer, notificationBuilder={}, player={}, e=", item, this.playerId, e);
        }

    }
}

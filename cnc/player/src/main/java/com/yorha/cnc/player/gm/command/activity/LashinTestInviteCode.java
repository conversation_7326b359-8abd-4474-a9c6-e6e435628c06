package com.yorha.cnc.player.gm.command.activity;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.InviteCodeUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class LashinTestInviteCode implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int bornIndexBegin = Integer.parseInt(args.get("bornIndexBegin"));
        int bornIndexEnd = Integer.parseInt(args.get("bornIndexEnd"));
        int actIndex = Integer.parseInt(args.get("actIndex"));
        if (actIndex < 0 || actIndex >= 1024) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (bornIndexBegin >= bornIndexEnd) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (bornIndexBegin < 0 || bornIndexEnd >= 65536) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        InviteCodeUtils.gmTestInviteCode(actor.getZoneId(), bornIndexBegin, bornIndexEnd, actIndex);
    }

    @Override
    public String showHelp() {
        return "LashinTestInviteCode bornIndexBegin={value} bornIndexEnd={value} actIndex={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return PlayerGmCommand.super.getGroup();
    }
}

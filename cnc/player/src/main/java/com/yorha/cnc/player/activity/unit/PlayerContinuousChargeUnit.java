package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.task.AbstractTaskHandler;
import com.yorha.cnc.player.task.ActivityTaskHandler;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivityTaskConf;
import com.yorha.common.utils.MailUtil;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityTemplate;

import java.util.Collections;
import java.util.List;

import static com.yorha.cnc.player.task.AbstractTaskHandler.setNextTaskState;

/**
 * @author: zpr
 * Date: 2023/9/4
 * Description:
 */
public class PlayerContinuousChargeUnit extends BasePlayerActivityUnit implements PlayerEventListener {

    private static final Logger LOGGER = LogManager.getLogger(PlayerContinuousChargeUnit.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(PlayerDayRefreshEvent.class);
    private AbstractTaskHandler taskHandler;
    /**
     * 任务池的当前任务下标
     */
    private int taskIndex = 0;

    private String getTaskPoolName() {
        return ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId()).getTaskPoolName();
    }

    private List<Integer> getConfTaskIds() {
        List<Integer> confTaskIds = ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId()).getActivityTaskIdsList();
        if (confTaskIds == null) {
            LOGGER.error("confTaskIds is null, taskIndex={}, activityId={}", taskIndex, ownerActivity.getActivityId());
            return Collections.emptyList();
        }
        return ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId()).getActivityTaskIdsList();
    }

    public PlayerContinuousChargeUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_CONTINUOUS_CHARGE, (owner, prop, template) ->
                new PlayerContinuousChargeUnit(owner, prop.getSpecUnit()));
    }

    /**
     * 获取当前下标的任务id
     */
    private int getCurTaskId() {
        // 有可能出现异常，比如index超出上限
        try {
            return getConfTaskIds().get(taskIndex);
        } catch (IndexOutOfBoundsException e) {
            LOGGER.error("PlayerContinuousChargeUnit getCurTaskId taskIndex out of Bounds, taskIndex = {}, ConfTaskIds size = {}", taskIndex, getConfTaskIds().size());
            return getConfTaskIds().get(getConfTaskIds().size() - 1);
        }
    }

    /**
     * 判断是不是任务链最后一个
     */
    private boolean isMaxIndex() {
        return taskIndex >= (getConfTaskIds().size() - 1);
    }

    @Override
    public void load(boolean isInitial) {
        if (taskHandler != null) {
            clearTaskHandler("activity clear attention");
        }
        if (unitProp.getTaskUnit().getTasks().isEmpty()) {
            LOGGER.info("PlayerContinuousChargeUnit load and task map is empty");
            taskIndex = 0;
        } else {
            // 接下一个任务 但是下面loadAllTask已经接过了，所以这一段其实没什么用
            for (int i = 0; i < getConfTaskIds().size(); i++) {
                // 循环遍历任务池每个任务
                if (unitProp.getTaskUnit().getTasks().containsKey(getConfTaskIds().get(i))) {
                    // 身上有这个任务说明index可以加
                    taskIndex = i;
                } else {
                    // 提前出来
                    break;
                }
            }
        }
        LOGGER.info("PlayerContinuousChargeUnit load and taskIndex={}, curTaskId={}", taskIndex, this.getCurTaskId());
        taskHandler = new ActivityTaskHandler(ownerActivity.getPlayer(), unitProp.getTaskUnit(), getTaskPoolName(), ownerActivity.getActivityId());
        taskHandler.openAttention();
        taskHandler.loadAllTask();
        if (!taskHandler.getTaskProp().containsKey(getConfTaskIds().get(taskIndex))) {
            taskHandler.addBatchTask(Sets.newHashSet(getConfTaskIds().get(taskIndex)));
        }
        ownerActivity.getPlayer().getTaskComponent().addTaskHandler(taskHandler);
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        final int curTaskId = getCurTaskId();
        final TaskInfoProp taskInfoProp = unitProp.getTaskUnit().getTasks().get(curTaskId);
        if (taskInfoProp != null && taskInfoProp.getStatus() == CommonEnum.TaskStatus.AT_NOT_REWARD) {
            this.sendActivityReward(false);
        }
        LOGGER.info("PlayerContinuousChargeUnit onExpire, activity={}, taskIndex={}, curTaskId={}, isnull={}", ownerActivity, this.taskIndex, curTaskId, taskInfoProp != null);
        clearTaskHandler("activityExpire");
    }

    @Override
    public void forceOffImpl() {
        LOGGER.info("PlayerContinuousChargeUnit taskUnit forceOffImpl, activity={}", ownerActivity);
        clearTaskHandler("forceOffImpl");
    }

    private void clearTaskHandler(String reason) {
        LOGGER.info("PlayerContinuousChargeUnit clearTaskHandler reason={}, handler={}", reason, taskHandler);
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention(reason);
            taskHandler = null;
        }
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerDayRefreshEvent) {
            final int curTaskId = getCurTaskId();
            final TaskInfoProp taskInfoProp = unitProp.getTaskUnit().getTasks().get(curTaskId);
            if (taskInfoProp == null) {
                LOGGER.error("PlayerContinuousChargeUnit onEvent but taskInfoProp is null, taskIndex={}, curTaskId={}", this.taskIndex, curTaskId);
                return;
            }
            if (taskInfoProp.getStatus() == CommonEnum.TaskStatus.AT_NOT_REWARD) {
                this.sendActivityReward(false);
            }
            if (taskInfoProp.getStatus() == CommonEnum.TaskStatus.AT_REWARD) {
                if (isMaxIndex()) {
                    // 当前是最后一个了，没必要往下接取了
                    return;
                }
                taskIndex++;
                int newTaskId = getCurTaskId();
                LOGGER.info("PlayerContinuousChargeUnit PlayerDayRefreshEvent, newTask, curTaskIndex={}, curTaskId={}", taskIndex, newTaskId);
                // 接取下一个任务
                taskHandler.addBatchTask(Sets.newHashSet(newTaskId));
            } else {
                // 说明任务没完成，清空任务重新来
                taskInfoProp.setProcess(Constant.DEFAULT_INT_VALUE);
                LOGGER.info("PlayerContinuousChargeUnit PlayerDayRefreshEvent, reset task process, curTaskIndex={}, curTaskId={}", taskIndex, taskInfoProp.getId());
            }
        }
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final int activityTaskId = key.getTaskId();
        Int32TaskInfoMapProp tasks = unitProp.getTaskUnit().getTasks();
        TaskInfoProp taskInfoProp = tasks.get(activityTaskId);
        if (taskInfoProp == null) {
            // 活动任务不存在
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_EXIST);
        }
        if (AbstractTaskHandler.isUnCompletedState(taskInfoProp)) {
            // 活动任务未完成
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_NOT_COMPLATE);
        }
        sendActivityReward(true);
    }

    private void sendActivityReward(boolean isActiveTakeReward) {
        final int curTaskId = this.getCurTaskId();
        final String taskPoolName = getTaskPoolName();
        ActivityTaskConf taskConf = ResHolder.getResService(ActivityResService.class).getTaskConf(taskPoolName, curTaskId);
        if (taskConf == null) {
            LOGGER.error("PlayerContinuousChargeUnit sendActivityReward but taskConf is null pooName={}, curTaskId={}", taskPoolName, curTaskId);
            return;
        }
        final TaskInfoProp taskInfoProp = unitProp.getTaskUnit().getTasks().get(getCurTaskId());
        if (taskInfoProp.getStatus() != CommonEnum.TaskStatus.AT_NOT_REWARD) {
            LOGGER.error("PlayerContinuousChargeUnit Activity sendActivityReward, not reward, ownActivity={}", ownerActivity);
            return;
        }
        setNextTaskState(taskInfoProp);
        List<IntPairType> reward = taskConf.getReward();
        LOGGER.info("PlayerContinuousChargeUnit sendActivityReward, taskId={}, reward={}", this.getCurTaskId(), reward);
        AssetPackage rewardPackage = AssetPackage.builder().plusItems(reward).build();
        ownerActivity.getPlayer().getAssetComponent().give(rewardPackage, CommonEnum.Reason.ICR_ACTIVITY_REWARD, taskHandler.formationSubReason(taskConf.getTaskId()));
        if (isActiveTakeReward) {
            return;
        }
        StructMail.MailSendParams.Builder paramsBuilder = MailUtil.genMailParamsOnlyForShow(taskConf.getRewardTriggerMailId(), reward);
        // 给自己发邮件
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(ownerActivity.getPlayer().getPlayerId())
                .setZoneId(ownerActivity.getPlayer().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, paramsBuilder.build());
    }
}

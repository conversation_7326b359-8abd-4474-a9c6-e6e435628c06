package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.FinishDailyTaskEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskDailyTemplate;
import res.template.TaskPoolTemplate;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 每日任务处理类
 *
 * <AUTHOR>
 */
public class DailyTaskHandler extends AbstractTaskHandler {
    private static final Logger LOGGER = LogManager.getLogger(DailyTaskHandler.class);


    public DailyTaskHandler(PlayerEntity entity, CommonEnum.TaskClass tcDaily) {
        super(entity, tcDaily.name());
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return getEntity().getProp().getTaskSystem().getTaskDaily();
    }

    @Override
    public void loadAllTask() {
        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            addTaskEventMonitor(value);
        }
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> configIdList) {
        List<IntPairType> rewardList = new ArrayList<>();
        Int32TaskInfoMapProp taskDaily = getTaskProp();
        for (Integer configId : configIdList) {
            TaskInfoProp taskInfoProp = getTaskInfoProp(taskDaily, configId);

            if (isUnCompletedState(taskInfoProp)) {
                continue;
            }
            setNextTaskState(taskInfoProp);

            // 领奖
            List<IntPairType> reward = ResHolder.getTemplate(TaskDailyTemplate.class, configId).getRewardPairList();
            AssetPackage assetPackage = AssetPackage.builder().plusItems(reward).build();
            getEntity().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_TASK, formationSubReason(configId));

            takeRewardQLog(taskInfoProp);

            rewardList.addAll(reward);
        }
        int heatValueItemId = ResHolder.getResService(ConstKVResService.class).getTemplate().getHeatValueItemId();
        int heatValue = 0;
        for (IntPairType intPairType : rewardList) {
            if (intPairType.getKey() == heatValueItemId) {
                heatValue += intPairType.getValue();
            }
        }
        if (heatValue > 0) {
            getEntity().getTaskComponent().addHeatValue(heatValue);
        }
        return rewardList;
    }


    /**
     * 检测并创建每日任务
     */
    private void checkOrCreateAllDailyTask() {
        Set<Integer> addConfigIds = new HashSet<>();
        Int32TaskInfoMapProp taskDaily = getTaskProp();
        int mainCityLevel = getEntity().getCityLevel();
        List<TaskDailyTemplate> taskDailyList = ResHolder.getResService(TaskTemplateService.class).getDailyTaskByCityLevel(mainCityLevel);
        LOGGER.info("config daily taskSize:{}", taskDaily.size());
        for (TaskDailyTemplate taskDailyTemplate : taskDailyList) {
            TaskInfoProp taskInfoProp = taskDaily.get(taskDailyTemplate.getTaskId());
            if (taskInfoProp == null) {
                addConfigIds.add(taskDailyTemplate.getId());
            }
        }
        LOGGER.info("add daily addConfigIds:{}", addConfigIds.size());
        if (CollectionUtils.isNotEmpty(addConfigIds)) {
            openAttention = true;
            addBatchTask(addConfigIds);
        }
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(TaskDailyTemplate.class, configId).getTaskId();
    }

    public void refreshDailyTask() {
        LOGGER.info("refreshDailyTask. old taskSize:{}", getTaskProp().size());
        getTaskProp().clear();
        clearAllAttention("dailyTaskRefresh");
        checkOrCreateAllDailyTask();
        getEntity().getTaskComponent().resetHeatValue();
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum gmType) {
        super.onTaskFinish(configId, gmType);
        new FinishDailyTaskEvent(getEntity()).dispatch();
    }
}
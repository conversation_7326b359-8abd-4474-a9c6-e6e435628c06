package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class ResetFirstJoinClanFlag implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        boolean flag = true;
        if (args.get("flag") != null) {
            flag = Integer.parseInt(args.get("flag")) == 1;
        }
        actor.getOrLoadEntity().getPlayerClanComponent().gmSetHasGotFirstEnterClanReward(flag);
    }

    @Override
    public String showHelp() {
        return "ResetFirstJoinClanFlag flag={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClanTech;

/**
 * 军团科技模块: 使用到ss_clan_tech里ss协议的cs协议，可以放到这里~
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_TECH)
public class PlayerClanTechController {

    @CommandMapping(code = MsgType.PLAYER_FETCHCLANTECHINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanTech.Player_FetchClanTechInfo_C2S msg) {
        return playerEntity.getClanTechComponent().handleFetch();
    }

    @CommandMapping(code = MsgType.PLAYER_CLANTECHDONATE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanTech.Player_ClanTechDonate_C2S msg) {
        return playerEntity.getClanTechComponent().handleDonate(msg);
    }

    @CommandMapping(code = MsgType.PLAYER_CLANTECHRESEARCH_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanTech.Player_ClanTechResearch_C2S msg) {
        playerEntity.getClanTechComponent().handleResearch(msg);
        return PlayerClanTech.Player_ClanTechResearch_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CLANTECHRECOMMEND_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanTech.Player_ClanTechRecommend_C2S msg) {
        playerEntity.getClanTechComponent().handleRecommend(msg);
        return PlayerClanTech.Player_ClanTechRecommend_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CLANTECHDETAIL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClanTech.Player_ClanTechDetail_C2S msg) {
        return playerEntity.getClanTechComponent().handleDetail(msg);
    }
}

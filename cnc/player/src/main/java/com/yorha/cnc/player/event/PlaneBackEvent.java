package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;

import java.util.List;

/**
 * <AUTHOR>
 * 飞机回城事件
 */
public class PlaneBackEvent extends PlayerEvent {
    private final List<Long> planeIds;

    public PlaneBackEvent(PlayerEntity player, List<Long> planeIds) {
        super(player);
        this.planeIds = planeIds;
    }

    public List<Long> getPlaneIds() {
        return planeIds;
    }
}

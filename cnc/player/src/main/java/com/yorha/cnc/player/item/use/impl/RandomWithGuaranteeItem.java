package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.utils.Pair;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

import java.util.ArrayList;
import java.util.List;

public class RandomWithGuaranteeItem extends AbstractUsableItem {
    private static final Logger LOGGER = LogManager.getLogger(RandomWithGuaranteeItem.class);
    private final List<ItemReward> rewardList;

    public RandomWithGuaranteeItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
        rewardList = new ArrayList<>();
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        // 配置加载时已校验
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        int randomPoolId = getTemplate().getEffectId();
        for (int i = 0; i < this.num; i++) {
            int alreadyRandomTimes = playerEntity.getRandomDrawComponent().getRandomTimesBeforeGuarantee(randomPoolId);
            LOGGER.info("player: {} randomPoolId={}, alreadyRandomTimes={}", playerEntity, randomPoolId, alreadyRandomTimes);
            // 触发保底
            if (alreadyRandomTimes + 1 >= ResHolder.getResService(ItemResService.class).getTriggerTime(randomPoolId)) {
                onGuarantee(playerEntity, randomPoolId);
            } else {
                //正常随机
                onNormalRandom(playerEntity, randomPoolId);
            }

        }

        return true;
    }

    private boolean onNormalRandom(PlayerEntity playerEntity, int randomPoolId) {
        Pair<ItemReward, Boolean> randomRes = ResHolder.getResService(ItemResService.class).getRewardFromRandomPool(randomPoolId);
        playerEntity.getRandomDrawComponent().increaseRandomTimesBeforeGuarantee(randomPoolId);
        ItemReward itemReward = randomRes.getFirst();
        if (itemReward == null) {
            LOGGER.error("randomPoolId: {} itemReward == null!", randomPoolId);
            return false;
        }
        LOGGER.info("player: {} randomPoolId: {} randomRes: {}", playerEntity, randomPoolId, randomRes);
        boolean hitGuarantee = randomRes.getSecond();
        if (hitGuarantee) {
            playerEntity.getRandomDrawComponent().clearRandomTimes(randomPoolId);
        }
        playerEntity.getItemComponent().addItem(itemReward.getItemTemplateId(), itemReward.getCount(), CommonEnum.Reason.ICR_BOX, "");
        rewardList.add(itemReward);
        return true;
    }

    private boolean onGuarantee(PlayerEntity playerEntity, int randomPoolId) {
        List<ItemReward> guaranteeReward = ResHolder.getResService(ItemResService.class).getGuaranteeReward(randomPoolId);
        if (guaranteeReward == null) {
            return false;
        }
        LOGGER.info("player: {} randomPoolId: {} guaranteeRes: {}", playerEntity, randomPoolId, guaranteeReward);
        for (ItemReward reward : guaranteeReward) {
            playerEntity.getItemComponent().addItem(reward.getItemTemplateId(), reward.getCount(), CommonEnum.Reason.ICR_BOX, "guarantee");
        }
        playerEntity.getRandomDrawComponent().clearRandomTimes(randomPoolId);
        rewardList.addAll(guaranteeReward);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        PlayerPB.ItemUseResultPB.Builder resultPB = PlayerPB.ItemUseResultPB.newBuilder();
        resultPB.setItems(ItemResService.makeItemListPB(rewardList));
        response.setResult(resultPB);
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class BuyItemDiscountStore implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int itemId = Integer.parseInt(args.get("itemId"));
        actor.getEntity().getDiscountStoreComponent().buyDiscountStoreItem(itemId, "");
    }

    @Override
    public String showHelp() {
        return "BuyItemDiscountStore itemId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

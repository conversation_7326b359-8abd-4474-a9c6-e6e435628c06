package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;
import res.template.FestivalBpTemplate;

import java.util.Map;

public class ExchangeFestivalBpTicket implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int actId = Integer.parseInt(args.getOrDefault("actId", "0"));
        int count = Integer.parseInt(args.getOrDefault("count", "0"));
        final FestivalBpTemplate template = ResHolder.getInstance().getValueFromMap(FestivalBpTemplate.class, actId);
        StringBuilder sb = new StringBuilder();
        sb.append("总次数=" + count + '\n');
        long sum = 0;
        for (int i = 0; i < count; i++) {
            int score = 1;
            // 积分暴击
            int boomRandom = RandomUtils.randomBetween(0, GameLogicConstants.IPPM);
            for (IntPairType suprise : template.getScoreSurprisePairList()) {
                if (boomRandom < suprise.getKey()) {
                    score = MathUtils.multiplyExact(suprise.getValue(), 1);
                    break;
                }
                boomRandom = boomRandom - suprise.getKey();
            }

            sb.append(score).append(", ");
            sum += score;
        }
        sb.append("\n")
                .append("总分=" + sum);

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(sb.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("BP道具兑换gm")
                .setSubTitle("ExchangeFestivalBpTicket");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(playerId)
                .setZoneId(actor.getZoneId())
                .build();
        actor.getEntity().getMailComponent().sendPersonalMail(receiver, builder.build());

    }

    @Override
    public String showHelp() {
        return "ExchangeFestivalBpTicket actId={value} count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

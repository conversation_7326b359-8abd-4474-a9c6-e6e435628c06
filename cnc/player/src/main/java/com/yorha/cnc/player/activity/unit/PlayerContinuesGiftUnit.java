package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.MailUtil;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum.ActivityUnitType;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ContinuesGiftActTemplate;
import res.template.ContinuesGiftTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 连锁礼包
 *
 * <AUTHOR>
 */
public class PlayerContinuesGiftUnit extends BasePlayerActivityUnit {
    private static final Logger LOGGER = LogManager.getLogger(PlayerContinuesGiftUnit.class);


    static {
        ActivityUnitFactory.register(ActivityUnitType.AUT_CONTINUES_GIFT, (owner, prop, template) ->
                new PlayerContinuesGiftUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerContinuesGiftUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            unitProp.getContinuesGift().setRewardedLevel(-1);
            LOGGER.info("PlayerContinuesGiftUnit init {} {}", ownerActivity.getActivityId(), unitProp.getContinuesGift());
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        final int unlockedLevel = unitProp.getContinuesGift().getUnlockedLevel();
        final int rewardedLevel = unitProp.getContinuesGift().getRewardedLevel();
        if (rewardedLevel >= unlockedLevel) {
            LOGGER.info("PlayerContinuesGiftUnit onExpire no mail {} {}/{}", ownerActivity.getActivityId(), rewardedLevel, unlockedLevel);
            return;
        }
        final ContinuesGiftActTemplate template = getGiftActTemplate();
        if (unlockedLevel >= template.getGiftListList().size()) {
            LOGGER.error("PlayerContinuesGiftUnit onExpire illegal level {} {}", ownerActivity.getActivityId(), unlockedLevel);
            return;
        }
        final List<Struct.ItemPair> itemPairList = new ArrayList<>();
        // 这里发放，已领取后面几级的奖励
        for (int i = rewardedLevel + 1; i <= unlockedLevel; i++) {
            if (i < 0) {
                continue;
            }
            final int curGiftId = template.getGiftListList().get(i);
            final ContinuesGiftTemplate curGiftTemplate = ResHolder.getInstance().getValueFromMap(ContinuesGiftTemplate.class, curGiftId);
            // 运营指定特殊奖励项不补发
            if (!curGiftTemplate.getNeedReissue()) {
                continue;
            }
            final List<IntPairType> pairs = curGiftTemplate.getItemListPairList();
            for (IntPairType pair : pairs) {
                final Struct.ItemPair itemPair = Struct.ItemPair.newBuilder().setItemTemplateId(pair.getKey()).setCount(pair.getValue()).build();
                itemPairList.add(itemPair);
            }
        }
        if (itemPairList.isEmpty()) {
            LOGGER.info("PlayerContinuesGiftUnit onExpire reward is empty {} {}/{}", ownerActivity.getActivityId(), rewardedLevel, unlockedLevel);
            return;
        }
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        final int mailId = template.getExpireMailId();
        if (mailId <= 0) {
            LOGGER.error("PlayerContinuesGiftUnit onExpire error expire mail {} {}", ownerActivity.getActivityId(), unlockedLevel);
            return;
        }
        mailSendParams.getItemRewardBuilder().addAllDatas(itemPairList);
        mailSendParams.setMailTemplateId(mailId);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(player().getPlayerId())
                        .setZoneId(player().getZoneId())
                        .build(),
                mailSendParams.build());
        LOGGER.info("PlayerContinuesGiftUnit handleTakeReward onExpire {} mail {} {}", ownerActivity.getActivityId(), mailId, itemPairList);
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        // 未解锁
        if (key.getLevel() < unitProp.getContinuesGift().getUnlockedLevel()) {
            throw new GeminiException(ErrorCode.CONTINUES_GIFT_NOT_UNLOCK);
        }
        // 已领奖
        if (key.getLevel() <= unitProp.getContinuesGift().getRewardedLevel()) {
            throw new GeminiException(ErrorCode.CONTINUES_GIFT_HAS_REWARDED);
        }
        if (key.getLevel() > (unitProp.getContinuesGift().getRewardedLevel() + 1)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "skip reward");
        }
        final ContinuesGiftActTemplate template = getGiftActTemplate();
        if (key.getLevel() >= template.getGiftListList().size()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerContinuesGiftUnit error level " + key.getLevel());
        }
        unitProp.getContinuesGift().setRewardedLevel(key.getLevel());
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        final int mailId = template.getMailId();
        final int curGiftId = template.getGiftListList().get(key.getLevel());
        final ContinuesGiftTemplate curGiftTemplate = ResHolder.getInstance().getValueFromMap(ContinuesGiftTemplate.class, curGiftId);
        final List<IntPairType> pairs = curGiftTemplate.getItemListPairList();
        final List<Struct.ItemPair> itemPairList = new ArrayList<>();
        for (IntPairType pair : pairs) {
            final Struct.ItemPair itemPair = Struct.ItemPair.newBuilder().setItemTemplateId(pair.getKey()).setCount(pair.getValue()).build();
            itemPairList.add(itemPair);
        }
        mailSendParams.getItemRewardBuilder().addAllDatas(itemPairList);
        mailSendParams.setMailTemplateId(mailId);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(player().getPlayerId())
                        .setZoneId(player().getZoneId())
                        .build(),
                mailSendParams.build());
        LOGGER.info("PlayerContinuesGiftUnit handleTakeReward send reward {} mail {} {} {}", ownerActivity.getActivityId(), key.getLevel(), mailId, curGiftId);
        final int nextLevel = key.getLevel() + 1;
        if (nextLevel >= template.getGiftListList().size()) {
            return;
        }
        final int nextGiftId = template.getGiftListList().get(nextLevel);
        final ContinuesGiftTemplate nextGiftTemplate = ResHolder.getInstance().getValueFromMap(ContinuesGiftTemplate.class, nextGiftId);
        if (!nextGiftTemplate.getIsFree()) {
            return;
        }
        unitProp.getContinuesGift().setUnlockedLevel(nextLevel);
        LOGGER.info("PlayerContinuesGiftUnit handleTakeReward unLock next {}", nextLevel);
    }

    /**
     * 活动配置
     */
    private ContinuesGiftActTemplate getGiftActTemplate() {
        final ContinuesGiftActTemplate template = ResHolder.getTemplate(ContinuesGiftActTemplate.class, ownerActivity.getActivityId());
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerContinuesGiftUnit getDiscountItem null " + ownerActivity.getActivityId());
        }
        return template;
    }

    /**
     * 解锁下一级需要的礼包id 原价礼包_折扣礼包
     * 不需要礼包会返回null
     */
    public IntPairType getNeedGoodsPair() {
        final int nextLevel = unitProp.getContinuesGift().getUnlockedLevel() + 1;
        final ContinuesGiftActTemplate template = getGiftActTemplate();
        if (nextLevel >= template.getGiftListList().size()) {
            return null;
        }
        final int nextGiftId = template.getGiftListList().get(nextLevel);
        final ContinuesGiftTemplate nextGiftTemplate = ResHolder.getInstance().getValueFromMap(ContinuesGiftTemplate.class, nextGiftId);
        if (nextGiftTemplate.getIsFree()) {
            return null;
        }
        return IntPairType.makePair(nextGiftTemplate.getGoodsId(), nextGiftTemplate.getDiscountGoodsId());
    }

    /**
     * 折扣卡道具id
     */
    public int getDiscountItem() {
        return getGiftActTemplate().getDiscountItemId();
    }

    /**
     * 是否已经打折
     */
    public boolean alreadyDiscount() {
        return unitProp.getContinuesGift().getHasDiscount();
    }

    public void consumeDiscount(int goodsId) {
        unitProp.getContinuesGift().setHasDiscount(false);
        LOGGER.info("PlayerContinuesGiftUnit consumeDiscount {} {}", ownerActivity.getActivityId(), goodsId);
    }

    /**
     * 使用打折卡
     */
    public void useDiscountItem() {
        if (alreadyDiscount()) {
            throw new GeminiException(ErrorCode.CONTINUES_GIFT_HAS_DISCOUNT);
        }
        unitProp.getContinuesGift().setHasDiscount(true);
        LOGGER.info("PlayerContinuesGiftUnit useDiscountItem {}", ownerActivity.getActivityId());
    }

    /**
     * 购买礼包解锁下一级
     */
    public void unLockGoodsLevel(int goodsId) {
        LOGGER.info("PlayerContinuesGiftUnit unLockGoodsLevel {} goodsId {} curLevel {}", ownerActivity.getActivityId(), goodsId, unitProp.getContinuesGift().getUnlockedLevel());
        final IntPairType needGoodsPair = getNeedGoodsPair();
        if ((goodsId != needGoodsPair.getKey()) && (goodsId != needGoodsPair.getValue())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "illegal goodsId " + needGoodsPair + " but " + goodsId);
        }
        final int nextLevel = unitProp.getContinuesGift().getUnlockedLevel() + 1;
        final ContinuesGiftActTemplate template = getGiftActTemplate();
        if (nextLevel >= template.getGiftListList().size()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "without next level " + nextLevel);
        }
        final int nextGiftId = template.getGiftListList().get(nextLevel);
        final ContinuesGiftTemplate nextGiftTemplate = ResHolder.getInstance().getValueFromMap(ContinuesGiftTemplate.class, nextGiftId);
        if (nextGiftTemplate.getIsFree()) {
            return;
        }
        unitProp.getContinuesGift().setUnlockedLevel(nextLevel);
        LOGGER.info("PlayerContinuesGiftUnit unLockGoodsLevel unLock {} next {} goodsId {}", ownerActivity.getActivityId(), nextLevel, goodsId);
    }
}

package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 玩家任务事件（动态事件）
 * <p>
 * TaskComponent 会扫描所有 implements PlayerTaskEvent 的事件，对其监听
 * <p>
 * 其实作为class存在不太合理，作为annotation比较合适一些
 *
 * <AUTHOR>
 */
public abstract class PlayerTaskEvent extends PlayerEvent {

    public PlayerTaskEvent(PlayerEntity entity) {
        super(entity);
    }

    public String getName() {
        return ClassNameCacheUtils.getSimpleName(this.getClass());
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsMidasAgent;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

public class SwitchMidasStressTesting implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        boolean stressTesting = true;
        if (MapUtils.isNotEmpty(args)) {
            stressTesting = Boolean.parseBoolean(args.get("value"));
        }
        actor.tell(RefFactory.ofMidasAgent(), SsMidasAgent.MidasSwitchStressTestingModeCmd.newBuilder().setStressTesting(stressTesting).build());
    }

    @Override
    public String showHelp() {
        return "SwitchMidasStressTesting value={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }
}

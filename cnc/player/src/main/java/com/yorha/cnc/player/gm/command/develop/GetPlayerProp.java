package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.Map;

public class GetPlayerProp implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GetPlayerProp.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String player = args.get("player");
        if (StringUtils.isNotEmpty(player)) {
            playerId = Long.parseLong(player);
        }
        if (!args.containsKey("zoneId")) {
            throw new GeminiException("args without zoneId {}", args);
        }
        TcaplusDb.PlayerTable.Builder req = TcaplusDb.PlayerTable.newBuilder();
        req.setPlayerId(playerId);
        GetResult<TcaplusDb.PlayerTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            throw new GeminiException(StringUtils.format("load player from db fail {} {}", this, ans));
        }
        TcaplusDb.PlayerTable.Builder recordBuilder = ans.value;
        if (recordBuilder == null) {
            throw new GeminiException(StringUtils.format("load player from db fail {} {}", this, ans));
        }

        PlayerProp attr = PlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
        LOGGER.info("gm getPlayerProp: {}", attr);

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(attr.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("GetPlayerProp")
                .setSubTitle("GetPlayerProp");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(playerId)
                .setZoneId(actor.getZoneId())
                .build();
        actor.getEntity().getMailComponent().sendPersonalMail(receiver, builder.build());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

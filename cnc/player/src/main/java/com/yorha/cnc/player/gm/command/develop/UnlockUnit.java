package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 解锁兵种
 */
public class UnlockUnit implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int unitId = Integer.parseInt(args.get("unit"));
        // 解锁兵种
        actor.getOrLoadEntity().getCityCommandComponent().unlockUnit(unitId);
    }

    @Override
    public String showHelp() {
        return "UnlockUnit";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

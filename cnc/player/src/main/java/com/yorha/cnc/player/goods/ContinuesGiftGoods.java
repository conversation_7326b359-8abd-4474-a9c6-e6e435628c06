package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.unit.PlayerContinuesGiftUnit;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.ActNormalGoodsExtInfoProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;

import java.util.Collections;
import java.util.List;

/**
 * 连续活动解锁付费奖池
 *
 * <AUTHOR>
 */
public class ContinuesGiftGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(ContinuesGiftGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        // 检查是否已解锁
        int actId = msg.getOrderParam().getActGoodsParam().getActId();
        int unitId = msg.getOrderParam().getActGoodsParam().getUnitId();
        PlayerContinuesGiftUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerContinuesGiftUnit.class, actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        final IntPairType needGoodsPair = unit.getNeedGoodsPair();
        if (needGoodsPair == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "needGoodsPair is null");
        }
        if (unit.alreadyDiscount()) {
            if (needGoodsPair.getValue() != goodsTemplate.getId()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "not cur discount goods");
            }
            return;
        }

        if (needGoodsPair.getKey() != goodsTemplate.getId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "not cur goods");
        }
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        final int actId = msg.getOrderParam().getActGoodsParam().getActId();
        final int unitId = msg.getOrderParam().getActGoodsParam().getUnitId();
        orderProp.getExtInfo()
                .getActNormal()
                .setActId(msg.getOrderParam().getActGoodsParam().getActId())
                .setUnitId(msg.getOrderParam().getActGoodsParam().getUnitId());
        PlayerContinuesGiftUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerContinuesGiftUnit.class, actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (!unit.alreadyDiscount()) {
            return;
        }
        unit.consumeDiscount(goodsTemplate.getId());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {

    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ActNormalGoodsExtInfoProp extInfo = goodsOrder.getExtInfo().getActNormal();
        int actId = extInfo.getActId();
        int unitId = extInfo.getUnitId();
        PlayerContinuesGiftUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerContinuesGiftUnit.class, actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 下一级奖励
        unit.unLockGoodsLevel(goodsTemplate.getId());
        LOGGER.info("ContinuesGiftGoods afterBaseDeliver {} {}", owner, goodsTemplate.getId());
        return Collections.emptyList();
    }
}

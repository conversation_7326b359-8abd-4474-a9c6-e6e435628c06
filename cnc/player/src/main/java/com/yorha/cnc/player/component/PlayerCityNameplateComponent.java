package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.city.CitySkinAction;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.city.CitySkinService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.PlayerCityModelProp;
import com.yorha.game.gen.prop.PlayerNameplateProp;
import com.yorha.proto.CommonEnum.AdditionSourceType;
import com.yorha.proto.CommonEnum.DressType;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.DressResTemplate;
import res.template.DressTemplate;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 主堡铭牌
 *
 * <AUTHOR>
 */
public class PlayerCityNameplateComponent extends PlayerComponent implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCityNameplateComponent.class);

    public PlayerCityNameplateComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad(boolean isRegister) {
        // 运营期新加功能 初始化
        if (isRegister || getProp().getUnlockedNameplate().size() == 0) {
            // 设置默认的铭牌
            tryInitCityNameplate();
        }
        clearExpiredCityNameplate();
    }

    /**
     * 初始化铭牌数据
     */
    private void tryInitCityNameplate() {
        int defaultBaseDress = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDefaultBaseNameplate();
        getProp().addEmptyUnlockedNameplate(defaultBaseDress).setTimeoutTsMs(0);
        getProp().setUsingNameplate(defaultBaseDress);
    }

    /**
     * 清除过期基地铭牌
     */
    private void clearExpiredCityNameplate() {
        Iterator<Integer> it = getProp().getUnlockedNameplate().keySetIterator();
        while (it.hasNext()) {
            Integer key = it.next();
            PlayerNameplateProp prop = getProp().getUnlockedNameplate().get(key);
            long expireTsMs = prop.getTimeoutTsMs();
            // 临时 & 已过期
            if (expireTsMs == 0) {
                continue;
            }
            if (expireTsMs < SystemClock.now()) {
                it.remove();
                LOGGER.info("clearExpiredCityNameplate {}", prop);
                getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.SKIN_EXPIRE, key, DressType.DRESS_NAME_PLATE, expireTsMs);
                // 过期后切换为默认铭牌，补发qlog
                if (key == getProp().getUsingNameplate()) {
                    int defaultBaseDress = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDefaultBaseNameplate();
                    getProp().setUsingNameplate(defaultBaseDress);
                    getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.USE_SKIN, defaultBaseDress, DressType.DRESS_NAME_PLATE, expireTsMs);
                }

            }
        }
    }

    /**
     * 是否拥有永久基地铭牌
     */
    public boolean hasEternalCityNameplate(int templateId) {
        PlayerNameplateProp prop = getProp().getUnlockedNameplate().get(templateId);
        if (prop == null) {
            return false;
        }
        return prop.getTimeoutTsMs() == 0;
    }

    /**
     * 解锁基地铭牌或延续有效时间
     *
     * @param dressTemplateId 外观配置id
     * @param nameplateId     铭牌配置id
     */
    public void extendOrUnlockNameplate(int dressTemplateId, int nameplateId) {
        PlayerNameplateProp prop = getProp().getUnlockedNameplate().get(nameplateId);
        // 原过期时间，<0 未拥有过，==0 永久拥有， >0 过期时间
        long oldTimeoutTsMs = prop == null ? 0 : prop.getTimeoutTsMs();
        // 在线时过期
        if (oldTimeoutTsMs > 0 && oldTimeoutTsMs < SystemClock.now()) {
            getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.SKIN_EXPIRE, dressTemplateId, DressType.DRESS_NAME_PLATE, oldTimeoutTsMs);
        }
        long continueTsMs = TimeUtils.second2Ms(ResHolder.getTemplate(DressTemplate.class, dressTemplateId).getContinueTime());
        // 永久设0， 否则计算到期时间
        long newTimeoutTsMs = continueTsMs == 0 ? 0 : (Math.max(oldTimeoutTsMs, SystemClock.now()) + continueTsMs);
        getProp().getUnlockedNameplate().put(nameplateId, new PlayerNameplateProp().setTemplateId(nameplateId).setTimeoutTsMs(newTimeoutTsMs));
        // 解锁皮肤（不区分是否首次）
        if (oldTimeoutTsMs < SystemClock.now()) {
            getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.UNLOCK_SKIN, nameplateId, DressType.DRESS_NAME_PLATE, SystemClock.now());
        }
        LOGGER.info("extendOrUnlockNameplate nameplateId={}, curTimeOutTsMs={}", nameplateId, newTimeoutTsMs);

    }

    /**
     * 更换主城铭牌
     *
     * @param templateId 主城铭牌配置id
     */
    public void changeCityNameplate(int templateId) {
        //校验是否是铭牌装扮
        if (ResHolder.getInstance().getValueFromMap(DressResTemplate.class, templateId).getDressType() != DressType.DRESS_NAME_PLATE.getNumber()) {
            throw new GeminiException(ErrorCode.DRESS_TYPE_INVALID);
        }
        // 是否已经装上了
        if (getProp().getUsingNameplate() == templateId) {
            return;
        }
        PlayerNameplateProp prop = getProp().getUnlockedNameplate().get(templateId);
        // 是否过期
        if (prop == null) {
            throw new GeminiException(ErrorCode.DRESS_NOT_OWNED);
        }
        if (prop.getTimeoutTsMs() != 0 && prop.getTimeoutTsMs() <= SystemClock.now()) {
            throw new GeminiException(ErrorCode.DRESS_NOT_OWNED);
        }
        SsScenePlayer.ChangeCityDressAsk.Builder builder = SsScenePlayer.ChangeCityDressAsk.newBuilder();
        builder.setDressTemplateId(templateId)
                .setPlayerId(getPlayerId())
                .setDressTimeoutTsMs(prop.getTimeoutTsMs());
        // 无返回值，但可能有exception
        ownerActor().callBigScene(builder.build());
        // 清除原铭牌加成
        int usingNameplate = getProp().getUsingNameplate();
        if (usingNameplate > 0) {
            getOwner().getAddComponent().removeAdditionBySource(AdditionSourceType.AST_CITY_NAMEPLATE);
        }
        getProp().setUsingNameplate(templateId);
        getOwner().getQlogComponent().sendCitySkinQLog(CitySkinAction.USE_SKIN, templateId, DressType.DRESS_NAME_PLATE, SystemClock.now());
        // 添加当前皮肤加成
        List<Integer> additionIds = Lists.newArrayList(getNameplateAddition(templateId).keySet());
        getOwner().getAddComponent().updateAddition(type(), additionIds);
        LOGGER.info("changeCityNameplate {}", templateId);
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.CITY_NAMEPLATE;
    }

    @Override
    public Map<AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        long value = 0;
        int usingNameplate = getOwner().getProp().getCityModel().getUsingNameplate();
        if (usingNameplate > 0) {
            value = getNameplateAddition(usingNameplate).getOrDefault(additionId, 0L);
        }
        Map<AdditionSourceType, Long> res = Maps.newHashMap();
        res.put(AdditionSourceType.AST_CITY_NAMEPLATE, value);
        return res;
    }

    /**
     * 获取装扮加成
     *
     * @param templateId 装扮id
     * @return 装扮加成
     */
    private static Map<Integer, Long> getNameplateAddition(int templateId) {
        return ResHolder.getResService(CitySkinService.class).getUseAttribute(templateId);
    }

    private PlayerCityModelProp getProp() {
        return getOwner().getProp().getCityModel();
    }
}

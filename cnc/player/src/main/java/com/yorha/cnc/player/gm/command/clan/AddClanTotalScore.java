package com.yorha.cnc.player.gm.command.clan;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 无限制的增加军团个人积分
 *
 * <AUTHOR>
 */
public class AddClanTotalScore implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(AddClanTotalScore.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int addScore = Integer.parseInt(args.get("addScore"));
        long totalClanScore = actor.getEntity().getProp().getClan().getTotalClanScore();
        actor.getEntity().getProp().getClan().setTotalClanScore(totalClanScore + addScore);
        LOGGER.info("addScore from {} to {}", totalClanScore, totalClanScore + addScore);
    }

    @Override
    public String showHelp() {
        return "AddClanTotalScore addScore={addScore}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

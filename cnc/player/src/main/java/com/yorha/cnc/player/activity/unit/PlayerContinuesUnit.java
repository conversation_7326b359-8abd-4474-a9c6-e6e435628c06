package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.google.protobuf.TextFormat;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityContinuesUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.Int32SetProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ContinueActConfigTemplate;
import res.template.ContinueActPoolTemplate;

import java.util.*;

import static com.yorha.proto.CommonEnum.Reason.ICR_CONTINUES_ACT;

/**
 * 连续活动
 *
 * <AUTHOR>
 */
public class PlayerContinuesUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerContinuesUnit.class);


    public PlayerContinuesUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerDayRefreshEvent.class,
            PlayerSpeedUpEvent.class
    );

    private final List<Class<? extends PlayerEvent>> contentionEvents = new ArrayList<>();

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_CONTINUE, (owner, prop, template) ->
                new PlayerContinuesUnit(owner, prop.getSpecUnit())
        );
    }

    private void init() {
        final ContinueActConfigTemplate configTemplate = getConfigTemplate();
        if (configTemplate.getLevelUpType() == CommonEnum.ContinueActLevelUpType.CALUT_DAY_LOGIN) {
            addScore(calcScore(1));
        }
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
        contentionEvents.clear();
        final ContinueActConfigTemplate configTemplate = getConfigTemplate();
        switch (configTemplate.getLevelUpType()) {
            case CALUT_DAY_LOGIN: {
                contentionEvents.add(PlayerDayRefreshEvent.class);
                break;
            }
            case CALUT_CITY_BUILDING_SPEED: {
                contentionEvents.add(PlayerSpeedUpEvent.class);
            }
            default:
        }
        LOGGER.info("PlayerContinuesUnit load contentionEvents {}", contentionEvents);
    }

    public ContinueActConfigTemplate getConfigTemplate() {
        return ResHolder.getInstance().getValueFromMap(ContinueActConfigTemplate.class, ownerActivity.getActivityId());
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        final ContinueActConfigTemplate configTemplate = getConfigTemplate();
        if (!isPayPoolUnlock() && configTemplate.getExpireOnlyPay()) {
            return;
        }
        final ActivityContinuesUnitProp continuesUnitProp = getContinuesUnitProp();
        if (continuesUnitProp.getUnlockLevel() <= 0) {
            return;
        }
        final StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        final Map<Integer, IntPairType> reward = new HashMap<>();
        final Collection<Integer> oldPayReward = continuesUnitProp.getPayRewardedLevel().getValues();
        final Collection<Integer> freeReward = continuesUnitProp.getFreeRewardedLevel().getValues();

        if (isPayPoolUnlock()) {
            for (int level = 1; level <= continuesUnitProp.getUnlockLevel(); level++) {
                if (oldPayReward.contains(level)) {
                    continue;
                }
                final int index = level - 1;
                final int curTask = configTemplate.getPayPoolList().get(index);
                final IntPairType rewardPair = getPoolTemplate(curTask).getRewardPair();
                mailSendParams.getItemRewardBuilder().addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(rewardPair.getKey()).setCount(rewardPair.getValue()).build());
                reward.put(level, rewardPair);
            }
        }
        if (!configTemplate.getExpireOnlyPay()) {
            for (int level = 1; level <= continuesUnitProp.getUnlockLevel(); level++) {
                if (freeReward.contains(level)) {
                    continue;
                }
                final int index = level - 1;
                final int curTask = configTemplate.getFreePoolList().get(index);
                final IntPairType rewardPair = getPoolTemplate(curTask).getRewardPair();
                mailSendParams.getItemRewardBuilder().addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(rewardPair.getKey()).setCount(rewardPair.getValue()).build());
                reward.put(level, rewardPair);
            }
        }
        if (reward.size() <= 0) {
            return;
        }
        final int mailId = configTemplate.getMailId();
        mailSendParams.setMailTemplateId(mailId);
        mailSendParams.getSenderBuilder().setSenderId(0);
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player().getPlayerId())
                .setZoneId(player().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, mailSendParams.build());
        continuesUnitProp.getPayRewardedLevel().addAll(reward.keySet());
        final int actId = ownerActivity.getActivityId();
        LOGGER.info("PlayerContinuesUnit onExpire sendPersonalMail {} {} {} {} {} {} {}",
                actId, mailId, configTemplate.getExpireOnlyPay(), continuesUnitProp.getUnlockLevel(), oldPayReward, freeReward, reward);
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final ActivityContinuesUnitProp continuesUnitProp = getContinuesUnitProp();
        final int actId = ownerActivity.getActivityId();
        if (key.getIsPayPool() && (!isPayPoolUnlock())) {
            throw new GeminiException(ErrorCode.CONTINUE_ACT_NEED_PAY);
        }
        if ((key.getLevel() <= 0) && (!key.getTakeAll())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "error level " + key.getLevel());
        }
        if (key.getLevel() > continuesUnitProp.getUnlockLevel()) {
            throw new GeminiException(ErrorCode.CONTINUE_ACT_LEVEL_LOCK);
        }
        if (key.getTakeAll() && (continuesUnitProp.getUnlockLevel() <= 0)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "take all with " + continuesUnitProp.getUnlockLevel());
        }
        AssetPackage.Builder rewardPackage = AssetPackage.builder();
        if (!key.getTakeAll()) {
            takeReward(key.getLevel(), key.getIsPayPool(), rewardPackage);
        } else {
            for (int i = 1; i <= continuesUnitProp.getUnlockLevel(); i++) {
                final Int32SetProp hasFreeRewardLevel = continuesUnitProp.getFreeRewardedLevel();
                final Int32SetProp hasPayRewardLevel = continuesUnitProp.getPayRewardedLevel();
                if (!hasFreeRewardLevel.contains(i)) {
                    takeReward(i, false, rewardPackage);
                }
                if (isPayPoolUnlock()) {
                    if (!hasPayRewardLevel.contains(i)) {
                        takeReward(i, true, rewardPackage);
                    }
                }
            }
        }
        rsp.setReward(rewardPackage.build().toPb());
        LOGGER.info("PlayerContinuesUnit handleTakeReward {} {} {} {} {}",
                actId, key.getIsPayPool(), key.getLevel(), key.getTakeAll(), TextFormat.printer().printToString(rewardPackage.build().toPb()));
    }

    private void takeReward(int level, boolean isPayPool, AssetPackage.Builder rewardPackage) {
        final ActivityContinuesUnitProp continuesUnitProp = getContinuesUnitProp();
        final ContinueActConfigTemplate configTemplate = getConfigTemplate();
        Int32SetProp hasRewardLevel;
        int curTask;
        final int index = level - 1;
        if (isPayPool) {
            hasRewardLevel = continuesUnitProp.getPayRewardedLevel();
            curTask = configTemplate.getPayPoolList().get(index);
        } else {
            hasRewardLevel = continuesUnitProp.getFreeRewardedLevel();
            curTask = configTemplate.getFreePoolList().get(index);
        }
        if (hasRewardLevel.contains(level)) {
            throw new GeminiException(ErrorCode.CONTINUE_ACT_HAS_REWARD);
        }
        final IntPairType rewardPair = getPoolTemplate(curTask).getRewardPair();
        final int actId = ownerActivity.getActivityId();
        player().getItemComponent().addItem(rewardPair.getKey(), rewardPair.getValue(), ICR_CONTINUES_ACT, String.valueOf(actId));
        rewardPackage.plusItem(rewardPair.getKey(), rewardPair.getValue());
        hasRewardLevel.add(level);
        LOGGER.info("PlayerContinuesUnit takeReaward {} {} {}", actId, level, isPayPool);
    }

    @Override
    public boolean isFinished() {
        return false;
    }


    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        LOGGER.info("PlayerContinuesUnit followList {}", FOLLOW_EVENT_LIST);
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        final int actId = ownerActivity.getActivityId();
        LOGGER.info("PlayerContinuesUnit onEvent {} {}", actId, event);
        if (!contentionEvents.contains(event.getClass())) {
            LOGGER.info("PlayerContinuesUnit onEvent not contention {} {}", actId, event);
            return;
        }
        final ContinueActConfigTemplate configTemplate = getConfigTemplate();
        if (event instanceof PlayerDayRefreshEvent) {
            if (configTemplate.getLevelUpType() == CommonEnum.ContinueActLevelUpType.CALUT_DAY_LOGIN) {
                final long startTsSec = ownerActivity.getProp().getStartTsSec();
                if (TimeUtils.isSameDay(TimeUtils.second2Ms(startTsSec), player().getRefreshComponent().getProp().getDayRefreshTsMs())) {
                    return;
                }
                addScore(calcScore(1));
            }
            return;
        }

        if (event instanceof PlayerSpeedUpEvent) {
            PlayerSpeedUpEvent playerSpeedUpEvent = (PlayerSpeedUpEvent) event;
            if (configTemplate.getLevelUpType() == CommonEnum.ContinueActLevelUpType.CALUT_CITY_BUILDING_SPEED) {
                if (playerSpeedUpEvent.getType() != CommonEnum.QueueTaskType.CITY_BUILD) {
                    return;
                }
                addScore(calcScore((int) playerSpeedUpEvent.getTotalCostSec()));
            }
        }
    }

    private ActivityContinuesUnitProp getContinuesUnitProp() {
        return unitProp.getContinueUnit();
    }

    private ContinueActPoolTemplate getPoolTemplate(int id) {
        return ResHolder.getInstance().getValueFromMap(ContinueActPoolTemplate.class, id);
    }

    public boolean isPayPoolUnlock() {
        return getContinuesUnitProp().getUnlockPayPool();
    }

    public void unlockPayPool() {
        LOGGER.info("PlayerContinuesUnit unlockPayPool {}", ownerActivity.getActivityId());
        getContinuesUnitProp().setUnlockPayPool(true);
    }

    private int calcScore(int num) {
        return num / getConfigTemplate().getScoreParam();
    }

    public void gmAddScore(int score) {
        LOGGER.info("PlayerContinuesUnit gmAddScore {} {}", ownerActivity.getActivityId(), score);
        addScore(score);
    }

    private void addScore(int score) {
        final ActivityContinuesUnitProp continuesUnitProp = getContinuesUnitProp();
        final int oldLevel = continuesUnitProp.getUnlockLevel();
        final int oldScore = continuesUnitProp.getScore();
        final ContinueActConfigTemplate configTemplate = getConfigTemplate();
        final int maxLevel = configTemplate.getFreePoolList().size();
        if (oldLevel >= maxLevel) {
            LOGGER.info("PlayerContinuesUnit addScore full {} {}", ownerActivity.getActivityId(), score);
            return;
        }
        int newScore = oldScore + score;
        int newLevel = oldLevel;
        while (newLevel < maxLevel) {
            int curTask = configTemplate.getFreePoolList().get(newLevel);
            final int needScore = getPoolTemplate(curTask).getScore();
            if (newScore < needScore) {
                break;
            }
            newScore = newScore - needScore;
            newLevel++;
        }
        if (newLevel == maxLevel) {
            newScore = 0;
        }
        continuesUnitProp.setUnlockLevel(newLevel).setScore(newScore);
        LOGGER.info("PlayerContinuesUnit addScore {} {} {} {} -> {} {}", ownerActivity.getActivityId(), score, oldLevel, oldScore, newLevel, newScore);
    }
}

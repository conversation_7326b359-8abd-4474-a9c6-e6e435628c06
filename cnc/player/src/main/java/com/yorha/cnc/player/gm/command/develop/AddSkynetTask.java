package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.SkynetBossTemplate;
import res.template.SkynetTaskTemplate;

import java.util.Arrays;
import java.util.Map;

/**
 * 增加天网任务
 *
 * <AUTHOR>
 */
public class AddSkynetTask implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        CommonEnum.SkynetModelType type = CommonEnum.SkynetModelType.forNumber(Integer.parseInt(args.get("type")));
        if (type == null || type == CommonEnum.SkynetModelType.SMT_NONE) {
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, Arrays.toString(CommonEnum.SkynetModelType.values()));
        }
        int taskId = Integer.parseInt(args.get("id"));
        if (taskId <= 0) {
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, Arrays.toString(CommonEnum.SkynetModelType.values()));
        }
        if (type == CommonEnum.SkynetModelType.SMT_BOSS) {
            SkynetBossTemplate valueFromMap = ResHolder.getInstance().findValueFromMap(SkynetBossTemplate.class, taskId);
            if (valueFromMap == null) {
                throw new GeminiException(ErrorCode.SYSTEM_WARNING, "taskId not exist");
            }
        }
        if (type == CommonEnum.SkynetModelType.SMT_NORMAL || type == CommonEnum.SkynetModelType.SMT_GUARD) {
            SkynetTaskTemplate valueFromMap = ResHolder.getInstance().findValueFromMap(SkynetTaskTemplate.class, taskId);
            if (valueFromMap == null) {
                throw new GeminiException(ErrorCode.SYSTEM_WARNING, "taskId not exist");
            }
        }
        actor.getOrLoadEntity().getSkynetComponent().addTaskByGm(type, taskId);
    }

    @Override
    public String showHelp() {
        return "AddSkynetTask type={} id={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

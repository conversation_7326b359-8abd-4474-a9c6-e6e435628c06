package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructMail;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AddZoneMail implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("mailTemplateId"));
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(templateId);
        MailUtil.sendZoneMail(actor.getZoneId(), builder.build());
    }

    @Override
    public String showHelp() {
        return "AddZoneMail mailTemplateId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAIL;
    }
}

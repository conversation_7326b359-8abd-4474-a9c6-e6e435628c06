package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.proto.CommonEnum;

public class PlayerSpeedUpEvent extends PlayerTaskEvent {

    private final long totalCostSec;
    private final QueueSpeedReason reason;
    private final CommonEnum.QueueTaskType type;

    public PlayerSpeedUpEvent(PlayerEntity entity, long totalCostSec, CommonEnum.QueueTaskType type, QueueSpeedReason reason) {
        super(entity);
        this.totalCostSec = totalCostSec;
        this.reason = reason;
        this.type = type;
    }

    public long getTotalCostSec() {
        return totalCostSec;
    }

    public QueueSpeedReason getReason() {
        return reason;
    }

    public CommonEnum.QueueTaskType getType() {
        return type;
    }

}

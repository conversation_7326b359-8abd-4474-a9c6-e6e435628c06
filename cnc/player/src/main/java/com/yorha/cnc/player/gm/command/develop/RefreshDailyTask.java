package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.component.PlayerTaskComponent;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 刷新每日任务
 *
 * <AUTHOR>
 */
public class RefreshDailyTask implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerTaskComponent.monitorDailyRefreshEvent(new PlayerDayRefreshEvent(actor.getEntity()));
    }

    @Override
    public String showHelp() {
        return "RefreshDailyTask";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_TASK;
    }
}

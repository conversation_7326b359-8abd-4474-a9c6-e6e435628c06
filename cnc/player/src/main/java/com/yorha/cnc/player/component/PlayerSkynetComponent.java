package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.event.PlayerWeekRefreshEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.cnc.player.event.task.PlayerSkynetFinishEvent;
import com.yorha.cnc.player.event.task.PlayerSkynetRewardEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.skynet.SkynetTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncSkynetLevel;
import qlog.flow.QlogCncSkynetTask;
import res.template.*;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.statistic.StatisticEnum.SKYNET_FINISH;
import static com.yorha.common.enums.statistic.StatisticEnum.SKYNET_REWARD;

/**
 * 天网
 *
 * <AUTHOR>
 */
public class PlayerSkynetComponent extends PlayerComponent {
    public static final Logger LOGGER = LogManager.getLogger(PlayerSkynetComponent.class);

    private final Map<CommonEnum.SkynetModelType, Integer> maxIdWithModel = new EnumMap<>(CommonEnum.SkynetModelType.class);

    /**
     * 普通任务刷新时间
     */
    private ActorTimer normalRefreshTimer;
    /**
     * 守护任务池刷新时间
     */
    private ActorTimer guardRefreshTimer;
    /**
     * 普通任务过期时间
     */
    private ActorTimer normalExpireTimer;
    /**
     * 迷雾探索任务定时器
     */
    private List<ActorTimer> exploreTimers = new ArrayList<>();

    static {
        EntityEventHandlerHolder.register(PlayerWeekRefreshEvent.class, PlayerSkynetComponent::onWeekRefresh);
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, PlayerSkynetComponent::onMainCityLevelChange);
        EntityEventHandlerHolder.register(PlayerKillBigSceneMonsterEvent.class, PlayerSkynetComponent::onKillBigSceneMonster);
    }

    /**
     * 判断天网是否已解锁
     */
    private boolean isSkynetUnlock() {
        return getBaseProp().getCurLevel() > 0;
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (!isSkynetUnlock() && !checkOrUnlockSkynetSystem()) {
            // 未解锁且不需要解锁就return
            return;
        }
        // 维护一个模块内的id最大值
        maxIdWithModel.put(CommonEnum.SkynetModelType.SMT_GUARD, getGuardProp().getGuardTask().getId());
        int maxId = 0;
        for (SkynetTaskProp value : getNormalProp().getNormalTask().values()) {
            maxId = Math.max(value.getId(), maxId);
        }
        maxIdWithModel.put(CommonEnum.SkynetModelType.SMT_NORMAL, maxId);

    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        if (!isSkynetUnlock() && !checkOrUnlockSkynetSystem()) {
            // 未解锁且不需要解锁就return
            return;
        }
        // 检测野怪并回滚
        checkOrRollBack(playerLoginAns.getSkynetMonsterListList());
        // 普通任务
        normalCheckRoot(NormalRefreshModel.ALL);
        // 守护者任务
        guardCheckRoot();


        // 添加探索任务的定时器
        for (SkynetTaskProp taskProp : getNormalProp().getNormalTask().values()) {
            SkynetTaskTemplate taskTemplate = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, taskProp.getSkynetTaskId());
            // 检测探索任务是否完成,没完成就上倒计时
            if (taskTemplate.getTaskType() == CommonEnum.SkynetTaskType.SNTT_EXPLORE) {
                checkOrFinishExploreTask(taskProp);
            }
        }
    }

    /**
     * 玩家登出后
     */
    @Override
    public void afterLogout() {
        for (ActorTimer exploreTimer : exploreTimers) {
            if (exploreTimer == null) {
                continue;
            }
            if (!exploreTimer.isCanceled()) {
                exploreTimer.cancel();
            }
        }
    }


    private void guardCheckRoot() {
        // 检测守护者池子是否要刷新，刷新几次，开刷
        checkOrRefreshGuardPool();
        // 检测守护者任务是否要补，要补的话从池子里补
        checkOrUnlockGuardTask();
    }

    /**
     * 统一步骤
     * 1、先刷池子
     * 2、检测任务过期
     * 3、从池子补任务
     */
    private void normalCheckRoot(NormalRefreshModel model) {
        if (model == NormalRefreshModel.POOL || model == NormalRefreshModel.ALL) {
            // 检测池子是否要刷新，刷新几次，开刷
            checkOrRefreshNormalPool();
        }
        // 检测普通任务过期
        checkOrRemoveNormalTask();
        // 检测下任务是否补充
        checkOrUnlockNormalTask();
        // 重新设置过期倒计时
        checkOrInitExpireTimer();
    }

    /**
     * 检测天网野怪是否存在，不存在要回滚
     */
    private void checkOrRollBack(List<Long> skynetMonsterListList) {
        for (SkynetTaskProp prop : getNormalProp().getNormalTask().values()) {
            SkynetTaskTemplate taskTemplate = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, prop.getSkynetTaskId());
            if (!isDoingState(prop.getSkynetTaskStatus())) {
                continue;
            }
            if (taskTemplate.getTaskType() != CommonEnum.SkynetTaskType.SNTT_MONSTER) {
                continue;
            }
            if (!needRollBackWithMonsterTask(prop, skynetMonsterListList)) {
                continue;
            }
            rollBackTaskStatus(prop, CommonEnum.SkynetModelType.SMT_NORMAL, "CheckNormal");
        }

        if (needRollBackWithMonsterTask(getBossProp().getBossTask(), skynetMonsterListList)) {
            rollBackTaskStatus(getBossProp().getBossTask(), CommonEnum.SkynetModelType.SMT_BOSS, "CheckBoss");
        }
    }

    /**
     * 判断是否需要回滚
     * 进行中 且 无野怪id的
     * 进行中 且 大世界不存在对应野怪的
     */
    private boolean needRollBackWithMonsterTask(SkynetTaskProp bossTask, List<Long> sceneMonsterIds) {
        if (!isDoingState(bossTask.getSkynetTaskStatus())) {
            return false;
        }
        if (bossTask.getMonsterId() <= 0) {
            return true;
        }
        return !sceneMonsterIds.contains(bossTask.getMonsterId());
    }

    private void checkOrRemoveNormalTask() {
        Collection<SkynetTaskProp> values = getNormalProp().getNormalTask().values();
        if (values.size() <= 0) {
            return;
        }
        // 遍历删除过期的
        Iterator<SkynetTaskProp> iterator = values.iterator();
        List<Integer> deleteKeys = new ArrayList<>();
        while (iterator.hasNext()) {
            SkynetTaskProp next = iterator.next();
            if (isNormalTaskExpire(next)) {
                deleteKeys.add(next.getId());
            }
        }

        if (deleteKeys.size() > 0) {
            LOGGER.info("PlayerSkynetComponent checkOrUnlockNormalTask, normalTask expire, ids={}", deleteKeys);
            getNormalProp().getNormalTask().removeAll(deleteKeys);
        }
    }

    /**
     * 负责维护过期定时器
     */
    private void checkOrInitExpireTimer() {

        Collection<SkynetTaskProp> values = getNormalProp().getNormalTask().values();
        if (values.size() <= 0) {
            return;
        }
        long nextNormalTaskExpireDelayMs = 0;
        long now = SystemClock.now();

        for (SkynetTaskProp next : values) {
            if (isDoingState(next.getSkynetTaskStatus()) || isAwaitAwardsState(next.getSkynetTaskStatus())) {
                continue;
            }
            if (isNormalTaskExpire(next)) {
                continue;
            }
            SkynetTaskTemplate taskTemplate = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, next.getSkynetTaskId());
            long expireDelayMs = next.getReceiveTsMs() + TimeUtils.second2Ms(taskTemplate.getTime()) - now;
            nextNormalTaskExpireDelayMs = nextNormalTaskExpireDelayMs == 0 ? expireDelayMs : Math.min(expireDelayMs, nextNormalTaskExpireDelayMs);
        }

        if (nextNormalTaskExpireDelayMs > 0) {
            if (normalExpireTimer != null && !normalExpireTimer.isCanceled()) {
                long l = normalExpireTimer.getNextExecuteTime() - now;
                if (l > 0 && l < nextNormalTaskExpireDelayMs) {
                    return;
                }
                normalExpireTimer.cancel();
            }
            normalExpireTimer = ownerActor().addTimer(TimerReasonType.SKYNET_NORMAL_EXPIRE, () -> normalCheckRoot(NormalRefreshModel.EXPIRE_TASK), nextNormalTaskExpireDelayMs, TimeUnit.MILLISECONDS);
        }

    }

    /**
     * 检测并刷新守护任务池
     */
    private void checkOrRefreshGuardPool() {
        long now = SystemClock.now();
        SkynetGuardTaskProp guardProp = getGuardProp();
        long lastRefreshTsMs = guardProp.getLastRefreshTsMs();

        if (now - lastRefreshTsMs < 0) {
            LOGGER.error("PlayerSkynetComponent checkOrRefreshGuardPool, time callback, lastRefreshTsMs={}", lastRefreshTsMs);
            return;
        }

        guardProp.setLastRefreshTsMs(now);

        if (guardRefreshTimer != null && !guardRefreshTimer.isCanceled()) {
            guardRefreshTimer.cancel();
        }
        long delayMs = ResHolder.getResService(SkynetTemplateService.class).getNextRefreshLaveMs(false, now);
        guardRefreshTimer = ownerActor().addTimer(getRefreshTimerKey(CommonEnum.SkynetModelType.SMT_GUARD), TimerReasonType.SKYNET_GUARD_POOL, this::guardCheckRoot, delayMs, TimeUnit.MILLISECONDS);

        long count = ResHolder.getResService(SkynetTemplateService.class).calcNeedRefreshCount(false, lastRefreshTsMs, now);
        if (count > 0) {
            addGuardPool(count, lastRefreshTsMs);
        }
    }


    /**
     * 检测并刷新普通任务池
     */
    private void checkOrRefreshNormalPool() {
        long now = SystemClock.now();
        SkynetNormalTaskProp normalProp = getNormalProp();
        long lastRefreshTsMs = normalProp.getLastRefreshTsMs();
        if (now - lastRefreshTsMs < 0) {
            LOGGER.error("PlayerSkynetComponent checkOrRefreshNormalPool, time callback, lastRefreshTsMs={}", lastRefreshTsMs);
            return;
        }

        normalProp.setLastRefreshTsMs(now);

        if (normalRefreshTimer != null && !normalRefreshTimer.isCanceled()) {
            normalRefreshTimer.cancel();
        }
        long delayMs = ResHolder.getResService(SkynetTemplateService.class).getNextRefreshLaveMs(true, now);
        normalRefreshTimer = ownerActor().addTimer(getRefreshTimerKey(CommonEnum.SkynetModelType.SMT_NORMAL), TimerReasonType.SKYNET_NORMAL_POOL, () -> normalCheckRoot(NormalRefreshModel.POOL), delayMs, TimeUnit.MILLISECONDS);

        long refreshNum = ResHolder.getResService(SkynetTemplateService.class).calcNeedRefreshCount(true, lastRefreshTsMs, now);
        if (refreshNum > 0) {
            long totalNum = getCurLevelTemplate().getTaskNum() * refreshNum;
            addNormalPool(totalNum, lastRefreshTsMs);
        }
    }

    /**
     * 添加一批任务到池子中
     *
     * @param lastRefreshTsMs 刷新时才有值，升级补充时为0
     */
    private void addGuardPool(long count, long lastRefreshTsMs) {
        SkynetGuardTaskProp guardProp = getGuardProp();
        if (count == 0) {
            return;
        }
        if (count < 0) {
            LOGGER.error("PlayerSkynetComponent, addGuardPool prop={} count={}", guardProp, count);
            return;
        }
        // 未解锁直接return
        ConstSkynetTemplate skynetConstConfig = ResHolder.getConsts(ConstSkynetTemplate.class);
        if (getBaseProp().getCurLevel() < skynetConstConfig.getDefenderUnlockLevel()) {
            return;
        }
        int oneRefreshCount = skynetConstConfig.getDefenderTaskRefreshLimit();
        int saveTaskNumLimit = skynetConstConfig.getDefenderSaveLimit();
        int totalNum = (int) (oneRefreshCount * count);
        int realNum = Math.min(guardProp.getTaskPoolNum() + totalNum, saveTaskNumLimit);
        LOGGER.info("PlayerSkynetComponent checkOrRefreshGuardPool, lastTsMs={} count={} skynetLevel={} oldPool={} newPool={}", lastRefreshTsMs, count, getBaseProp().getCurLevel(), guardProp.getTaskPoolNum(), realNum);
        guardProp.setTaskPoolNum(realNum);
    }

    /**
     * 添加一批任务到池子中
     *
     * @param lastRefreshTsMs 刷新时才有值，升级补充时为0
     */
    private void addNormalPool(long count, long lastRefreshTsMs) {
        SkynetNormalTaskProp normalProp = getNormalProp();
        if (count <= 0) {
            LOGGER.error("PlayerSkynetComponent, addNormalPool prop={} count={}", normalProp, count);
            return;
        }
        int saveTaskNumLimit = ResHolder.getConsts(ConstSkynetTemplate.class).getSaveTaskNumLimit();
        long realNum = Math.min(normalProp.getTaskPoolNum() + count, saveTaskNumLimit);
        LOGGER.info("PlayerSkynetComponent checkOrRefreshNormalPool, lastTsMs={} count={} skynetLevel={} oldPool={} newPool={}", lastRefreshTsMs, count, getBaseProp().getCurLevel(), normalProp.getTaskPoolNum(), realNum);
        normalProp.setTaskPoolNum((int) realNum);
    }

    private static void onWeekRefresh(PlayerWeekRefreshEvent event) {
        LOGGER.info("PlayerSkynetComponent onWeekRefresh");
        for (ShopHistoryProp value : event.getPlayer().getSkynetComponent().getShopProp().getGoodsHistory().values()) {
            SkynetShopTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetShopTemplate.class, value.getShopId());
            if (valueFromMap.getRefresh() > 0 && value.getNum() > 0) {
                LOGGER.info("PlayerSkynetComponent onWeekRefresh, id={} oldNum={}", value.getShopId(), value.getNum());
                value.setNum(0);
            }
        }
    }

    private static void onMainCityLevelChange(MainCityUpgradeStaticEvent event) {
        // 3、检测一下BOSS任务
        event.getPlayer().getSkynetComponent().checkOrUnlockBossTask();
    }


    public PlayerSkynetComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 调用前保证已解锁
     */
    private SkynetLevelTemplate getCurLevelTemplate() {
        return ResHolder.getInstance().getValueFromMap(SkynetLevelTemplate.class, getBaseProp().getCurLevel());
    }

    private SkynetShopProp getShopProp() {
        return getOwner().getProp().getSkynetModel().getShopData();
    }

    private SkynetBaseDataProp getBaseProp() {
        return getOwner().getProp().getSkynetModel().getBaseData();
    }

    private SkynetBossTaskProp getBossProp() {
        return getOwner().getProp().getSkynetModel().getBossTask();
    }

    private SkynetGuardTaskProp getGuardProp() {
        return getOwner().getProp().getSkynetModel().getGuardTask();
    }

    private SkynetNormalTaskProp getNormalProp() {
        return getOwner().getProp().getSkynetModel().getNormalTask();
    }

    public void handleDoingTask(CommonEnum.SkynetModelType skynetModelType, int skynetTaskId, PlayerSkynet.Player_SkynetDoingTask_S2C.Builder builder) {
        if (!isSkynetUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_UNLOCK);
        }
        SkynetTaskProp receiveProp;
        int monsterTemplateId = 0;
        // 任务类型（boss模块不会赋值）
        CommonEnum.SkynetTaskType taskType = null;
        // 任务品质（boss模块不会赋值）
        int taskQuality = 0;
        // 体力扣除（boss模块不会赋值）
        int costEnergy = 0;
        switch (skynetModelType) {
            case SMT_BOSS: {
                SkynetTaskProp bossTask = getBossProp().getBossTask();
                if (bossTask.getId() != skynetTaskId) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                receiveProp = bossTask;
                SkynetBossTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetBossTemplate.class, bossTask.getSkynetTaskId());
                monsterTemplateId = valueFromMap.getBossID();
                break;
            }
            case SMT_NORMAL: {
                SkynetTaskProp taskProp = getNormalProp().getNormalTask().get(skynetTaskId);
                if (taskProp == null) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                receiveProp = taskProp;
                SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, taskProp.getSkynetTaskId());
                taskType = valueFromMap.getTaskType();
                costEnergy = valueFromMap.getNeedPower();
                monsterTemplateId = taskType == CommonEnum.SkynetTaskType.SNTT_MONSTER ? valueFromMap.getParam() : 0;
                taskQuality = valueFromMap.getQuality();
                break;
            }
            case SMT_GUARD: {
                throw new GeminiException(ErrorCode.SKYNET_FIB_GUARD_TASK);
            }
            default: {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }
        if (!isReceiveState(receiveProp.getSkynetTaskStatus())) {
            throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
        }
        if (costEnergy > 0) {
            if (!getOwner().getEnergyComponent().hasEnough(costEnergy)) {
                throw new GeminiException(ErrorCode.MONSTER_ENERGY_NOT_ENOUGH);
            }
            getOwner().getEnergyComponent().consumeEnergy(costEnergy, false, "skynet", receiveProp.getSkynetTaskId());
        }
        // 设置进行状态
        statusChange(receiveProp, CommonEnum.SkynetTaskStatus.SNTS_DOING);

        if (taskType != null) {
            if (taskType == CommonEnum.SkynetTaskType.SNTT_EXPLORE) {
                checkOrFinishExploreTask(receiveProp);
            }
            if (taskType == CommonEnum.SkynetTaskType.SNTT_TALK) {
                // 对话的直接完成
                execTaskFinish(receiveProp, skynetModelType);
            }
        }

        if (monsterTemplateId > 0) {
            SsSceneObj.SummonSkynetMonsterAns monsterAns = summonMonster(receiveProp, monsterTemplateId, skynetModelType, costEnergy);
            if (monsterAns != null) {
                builder.setBornPoint(StructPB.PointPB.newBuilder().setX(monsterAns.getPoint().getX()).setY(monsterAns.getPoint().getY()))
                        .setMonsterId(monsterAns.getMonsterId())
                        .setLifeEndTsMs(monsterAns.getExpireTsMs());
            }
        }
        sendTaskQLog(receiveProp.getSkynetTaskId(), taskQuality, skynetModelType, taskType, "task_start");
    }

    private SsSceneObj.SummonSkynetMonsterAns summonMonster(SkynetTaskProp receiveProp, int monsterTemplateId, CommonEnum.SkynetModelType modelType, int costEnergy) {
        MonsterTemplate valueFromMap = ResHolder.getInstance().findValueFromMap(MonsterTemplate.class, monsterTemplateId);
        if (valueFromMap == null) {
            WechatLog.error("PlayerSkynetComponent summonMonster fail, no config monsterTemplateId={}", monsterTemplateId);
            rollBackTaskStatus(receiveProp, modelType, "SummonFailWithConfig");
            return null;
        }
        SsSceneObj.SummonSkynetMonsterAsk build = SsSceneObj.SummonSkynetMonsterAsk.newBuilder()
                .setOwnerPlayerId(getPlayerId())
                .setMonsterTemplateId(monsterTemplateId).build();
        SsSceneObj.SummonSkynetMonsterAns ans;
        try {
            ans = ownerActor().callBigScene(build);
        } catch (Exception e) {
            rollBackTaskStatus(receiveProp, modelType, "SummonSkynetMonsterAskException");
            WechatLog.error("PlayerSkynetComponent SummonSkynetMonsterAsk fail, monsterTemplateId={}", monsterTemplateId);
            return null;
        }
        if (ans.getErrorCode() > 0) {
            String reason = "SceneSummonMonsterFail";
            getOwner().getEnergyComponent().addEnergy(costEnergy, false, reason, 0);
            rollBackTaskStatus(receiveProp, modelType, reason);
            throw new GeminiException(ans.getErrorCode());
        }
        if (ans.getMonsterId() > 0) {
            receiveProp.setMonsterId(ans.getMonsterId());
            return ans;
        } else {
            rollBackTaskStatus(receiveProp, modelType, "SummonFail");
            WechatLog.error("PlayerSkynetComponent SummonSkynetMonsterAsk fail, monsterId<=0");
            return null;
        }
    }

    private boolean rollBackTaskStatus(SkynetTaskProp receiveProp, CommonEnum.SkynetModelType modelType, String reason) {
        if (!isDoingState(receiveProp.getSkynetTaskStatus())) {
            LOGGER.error("PlayerSkynetComponent rollBackTaskStatus fail, id={} task={} oldStatus={}", receiveProp.getId(), receiveProp.getSkynetTaskId(), receiveProp.getSkynetTaskStatus());
            return false;
        }
        LOGGER.info("PlayerSkynetComponent rollBackTaskStatus, task rollBack, id={} task={} oldStatus={} reason={}", receiveProp.getId(), receiveProp.getSkynetTaskId(), receiveProp.getSkynetTaskStatus(), reason);
        receiveProp.setSkynetTaskStatus(CommonEnum.SkynetTaskStatus.SNTS_RECEIVED).setDoingTsMs(0).setMonsterId(0);
        switch (modelType) {
            case SMT_BOSS: {
                SkynetBossTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetBossTemplate.class, receiveProp.getSkynetTaskId());
                sendTaskQLog(valueFromMap.getId(), 0, modelType, CommonEnum.SkynetTaskType.SNTT_MONSTER, "task_failed");
                break;
            }
            case SMT_NORMAL:
            case SMT_GUARD: {
                SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, receiveProp.getSkynetTaskId());
                sendTaskQLog(valueFromMap.getId(), valueFromMap.getQuality(), modelType, valueFromMap.getTaskType(), "task_failed");
                return true;
            }
            default: {
                LOGGER.error("PlayerSkynetComponent rollBackTaskStatus fail, modelType={}", modelType);
            }
        }
        return false;
    }

    private void execTaskFinish(SkynetTaskProp receiveProp, CommonEnum.SkynetModelType moduleType) {
        statusChange(receiveProp, CommonEnum.SkynetTaskStatus.SNTS_AWAIT_AWARD);
        onTaskFinish();
        if (moduleType == CommonEnum.SkynetModelType.SMT_BOSS) {
            SkynetBossTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetBossTemplate.class, receiveProp.getSkynetTaskId());
            sendTaskQLog(valueFromMap.getId(), 0, moduleType, CommonEnum.SkynetTaskType.SNTT_MONSTER, "task_complete");
            return;
        }

        SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, receiveProp.getSkynetTaskId());

        sendTaskQLog(valueFromMap.getId(), valueFromMap.getQuality(), moduleType, valueFromMap.getTaskType(), "task_complete");

        if (valueFromMap.getExtraRewardPairList().size() <= 0) {
            return;
        }
        AssetPackage build = AssetPackage.builder().plusItems(valueFromMap.getExtraRewardPairList()).build();
        getOwner().getAssetComponent().give(build, CommonEnum.Reason.ICR_SKYNET, "extraReward");

        if (valueFromMap.getMailID() <= 0) {
            LOGGER.error("PlayerSkynetComponent execTaskFinish, extraReward={} mailId={}", valueFromMap.getExtraRewardPairList(), valueFromMap.getMailID());
            return;
        }
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder().setMailTemplateId(valueFromMap.getMailID()).setIsOnlyForShow(true);

        for (IntPairType intPairType : valueFromMap.getExtraRewardPairList()) {
            mailSendParams.getItemRewardBuilder().addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(intPairType.getKey()).setCount(intPairType.getValue()).build());
        }

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(getOwner().getPlayerId())
                .setZoneId(getOwner().getZoneId())
                .build();
        getOwner().getMailComponent().sendPersonalMail(receiver, mailSendParams.build());
    }

    private void onTaskFinish() {
        getOwner().getStatisticComponent().recordSingleStatistic(SKYNET_FINISH, 1);
        new PlayerSkynetFinishEvent(getOwner(), 1).dispatch();
    }

    private String getExploreTimerKey(int id) {
        return getPlayerId() + "-" + CommonEnum.SkynetTaskType.SNTT_EXPLORE_VALUE + "-" + id;
    }

    private String getRefreshTimerKey(CommonEnum.SkynetModelType skynetModelType) {
        return getPlayerId() + "-" + skynetModelType.getNumber();
    }

    private static void statusChange(SkynetTaskProp receiveProp, CommonEnum.SkynetTaskStatus newStatus) {
        LOGGER.info("PlayerSkynetComponent statusChange, id={} taskId={} oldStatus={} newStatus={}", receiveProp.getId(), receiveProp.getSkynetTaskId(), receiveProp.getSkynetTaskStatus(), newStatus);
        receiveProp.setSkynetTaskStatus(newStatus);
        if (newStatus == CommonEnum.SkynetTaskStatus.SNTS_DOING) {
            receiveProp.setDoingTsMs(SystemClock.now());
        }
        if (newStatus == CommonEnum.SkynetTaskStatus.SNTS_RECEIVED) {
            receiveProp.setReceiveTsMs(SystemClock.now());
        }
    }


    public void handleBuyStore(int shopId, int num) {
        if (shopId <= 0 || num <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (!isSkynetUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_UNLOCK);
        }
        if (!isSkynetShopUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_STORE_UNLOCK);
        }
        SkynetShopTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetShopTemplate.class, shopId);

        ShopHistoryProp shopHistoryProp;
        if (getShopProp().getGoodsHistory().containsKey(shopId)) {
            shopHistoryProp = getShopProp().getGoodsHistory().get(shopId);
        } else {
            shopHistoryProp = getShopProp().getGoodsHistory().addEmptyValue(shopId);
        }
        int afterNum = shopHistoryProp.getNum() + num;
        if (afterNum > valueFromMap.getLimitNum()) {
            throw new GeminiException(ErrorCode.SKYNET_STORE_GOODS_LIMIT);
        }

        AssetPackage cost = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.SKYNET_VALUE, (long) valueFromMap.getPrice() * num).build();
        getOwner().getAssetComponent().verify(cost);
        // 策划保证一个商品就是一个道具
        AssetPackage give = AssetPackage.builder().plusItem(valueFromMap.getItemIDPair().getKey(), num).build();

        LOGGER.info("PlayerSkynetComponent handleBuyStore, shopId={} num={} afterNum={} cost={} give={}", shopId, num, afterNum, cost, give);
        shopHistoryProp.setNum(afterNum);
        getOwner().getAssetComponent().consume(cost, CommonEnum.Reason.ICR_SKYNET);
        getOwner().getAssetComponent().give(give, CommonEnum.Reason.ICR_SKYNET);
    }

    public void handleTakeReward(CommonEnum.SkynetModelType skynetModelType, int skynetTaskId) {
        if (!isSkynetUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_UNLOCK);
        }
        SkynetTaskProp rewardProp;
        // 任务品质（BOSS不填充）
        int quality = 0;
        CommonEnum.SkynetTaskType type = null;
        int rewardId;
        switch (skynetModelType) {
            case SMT_BOSS: {
                SkynetTaskProp bossTask = getBossProp().getBossTask();
                if (bossTask.getId() != skynetTaskId) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                rewardProp = bossTask;
                SkynetBossTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetBossTemplate.class, bossTask.getSkynetTaskId());
                rewardId = valueFromMap.getTaskReward();
                break;
            }
            case SMT_GUARD: {
                SkynetTaskProp guardTask = getGuardProp().getGuardTask();
                if (guardTask.getId() != skynetTaskId) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                rewardProp = guardTask;
                SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, guardTask.getSkynetTaskId());
                rewardId = valueFromMap.getRewardID();
                quality = valueFromMap.getQuality();
                type = valueFromMap.getTaskType();
                break;
            }
            case SMT_NORMAL: {
                SkynetTaskProp taskProp = getNormalProp().getNormalTask().get(skynetTaskId);
                if (taskProp == null) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                rewardProp = taskProp;
                SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, taskProp.getSkynetTaskId());
                rewardId = valueFromMap.getRewardID();
                quality = valueFromMap.getQuality();
                type = valueFromMap.getTaskType();
                break;
            }
            default: {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }
        if (!isAwaitAwardsState(rewardProp.getSkynetTaskStatus())) {
            throw new GeminiException(ErrorCode.SKYNET_TASK_UN_COMPLETED);
        }
        // 标记任务完成
        statusChange(rewardProp, CommonEnum.SkynetTaskStatus.SNTS_OVER);
        onTaskOver();

        if (rewardId > 0) {
            AssetPackage assetPackage = ResHolder.getResService(ItemResService.class).randomReward2Pack(rewardId);
            getOwner().getAssetComponent().give(assetPackage, CommonEnum.Reason.ICR_SKYNET);
        }

        getOwner().getStatisticComponent().recordSingleStatistic(SKYNET_REWARD, 1);
        new PlayerSkynetRewardEvent(getOwner(), 1).dispatch();
        sendTaskQLog(rewardProp.getSkynetTaskId(), quality, skynetModelType, type, "task_collect_reward");

        if (skynetModelType == CommonEnum.SkynetModelType.SMT_BOSS) {
            checkOrUnlockBossTask();
        }
        if (skynetModelType == CommonEnum.SkynetModelType.SMT_GUARD) {
            guardCheckRoot();
        }
        if (skynetModelType == CommonEnum.SkynetModelType.SMT_NORMAL) {
            normalCheckRoot(NormalRefreshModel.ALL);
        }
    }

    /**
     * 任意任务领完奖，给1经验，策划没需求让写死
     */
    private void onTaskOver() {
        addExp(1);
        if (isSkynetExchangeUnlock()) {
            addPoint(1);
        }
    }

    private void addPoint(int value) {
        int configLimit = ResHolder.getInstance().getConstTemplate(ConstSkynetTemplate.class).getPointLimit();
        if (getBaseProp().getTotalInfoPoint() >= configLimit) {
            LOGGER.info("PlayerSkynetComponent addPoint outLimit, point={} config={}", getBaseProp().getTotalInfoPoint(), configLimit);
            return;
        }
        LOGGER.info("PlayerSkynetComponent addPoint, value={} oldValue={}", value, getBaseProp().getTotalInfoPoint());
        getBaseProp().setTotalInfoPoint(getBaseProp().getTotalInfoPoint() + value);
    }

    private void addExp(int value) {
        SkynetLevelTemplate curLevelTemplate = getCurLevelTemplate();
        if (isLevelMax(curLevelTemplate)) {
            LOGGER.info("PlayerSkynetComponent max level, level={} addExp={}", getBaseProp().getCurLevel(), value);
            return;
        }
        int skynetExp = getBaseProp().getSkynetExp() + value;
        LOGGER.info("PlayerSkynetComponent addExp, curLevel={} oldExp={} newExp={}", getBaseProp().getCurLevel(), getBaseProp().getSkynetExp(), skynetExp);
        getBaseProp().setSkynetExp(skynetExp);
        while (!isLevelMax(curLevelTemplate) && getBaseProp().getSkynetExp() >= curLevelTemplate.getNeedExp()) {
            execLevelUp();
            curLevelTemplate = getCurLevelTemplate();
        }
        sendLevelQLog(value, getBaseProp().getCurLevel(), getBaseProp().getSkynetExp());
    }

    private boolean isLevelMax(SkynetLevelTemplate curLevelTemplate) {
        return curLevelTemplate.getNeedExp() <= 0;
    }

    private void execLevelUp() {
        LOGGER.info("PlayerSkynetComponent execLevelUp, oldLevel={}", getBaseProp().getCurLevel());
        getBaseProp().setCurLevel(getBaseProp().getCurLevel() + 1);
        SkynetLevelTemplate curLevelTemplate = getCurLevelTemplate();

        // 1、补一批任务进来
        addNormalPool(curLevelTemplate.getLevelUpGiveTaskNum(), 0);
        // 2、补一批守卫者任务进来
        addGuardPool(curLevelTemplate.getLevelUpGiveDefenderTaskNum(), 0);

        normalCheckRoot(NormalRefreshModel.ALL);
        guardCheckRoot();
    }

    private boolean isSkynetShopUnlock() {
        int shopUnlockLevel = ResHolder.getConsts(ConstSkynetTemplate.class).getShopUnlockLevel();
        return getBaseProp().getCurLevel() >= shopUnlockLevel;
    }

    private boolean isSkynetExchangeUnlock() {
        int exchangeUnlockLevel = ResHolder.getConsts(ConstSkynetTemplate.class).getExchangeUnlocklevel();
        return getBaseProp().getCurLevel() >= exchangeUnlockLevel;
    }


    public void handleCharge() {
        if (!isSkynetUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_UNLOCK);
        }
        if (!isSkynetExchangeUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_INFORMATION_UNLOCK);
        }
        ConstSkynetTemplate constTemplate = ResHolder.getConsts(ConstSkynetTemplate.class);
        int needPoint = constTemplate.getNeedPoint();
        if (getBaseProp().getTotalInfoPoint() < needPoint) {
            throw new GeminiException(ErrorCode.SKYNET_INFORMATION_NOT_ENOUGH);
        }
        int hasItemLimit = constTemplate.getKeyLimit();
        int giveItemId = constTemplate.getKeyItemId();

        int hasItemNum = getOwner().getItemComponent().getItemNum(giveItemId);
        if (hasItemNum >= hasItemLimit) {
            throw new GeminiException(ErrorCode.SKYNET_INFORMATION_MAX_CHARGE);
        }
        LOGGER.info("PlayerSkynetComponent handleCharge success, costPointNum={} giveItem={}", needPoint, giveItemId);
        getBaseProp().setTotalInfoPoint(getBaseProp().getTotalInfoPoint() - needPoint);

        getOwner().getItemComponent().addItem(giveItemId, 1, CommonEnum.Reason.ICR_SKYNET, "charge");

    }

    public PlayerSkynet.Player_SkynetFindMonster_S2C handleFindMonster(int skynetTaskId, CommonEnum.SkynetModelType skynetModelType) {
        if (skynetModelType == CommonEnum.SkynetModelType.SMT_NONE || skynetTaskId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        SkynetTaskProp taskProp;
        switch (skynetModelType) {
            case SMT_NORMAL: {
                taskProp = getNormalProp().getNormalTask().get(skynetTaskId);
                if (taskProp == null) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                break;
            }
            case SMT_BOSS: {
                if (getBossProp().getBossTask().getId() != skynetTaskId) {
                    throw new GeminiException(ErrorCode.SKYNET_TASK_NOT_EXIST);
                }
                taskProp = getBossProp().getBossTask();
                break;
            }
            default: {
                LOGGER.error("PlayerSkynetComponent handleFindMonster modelType err, skynetTaskId={} skynetModelType={}", skynetTaskId, skynetModelType);
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }
        if (!isDoingState(taskProp.getSkynetTaskStatus())) {
            throw new GeminiException(ErrorCode.SKYNET_TASK_UN_COMPLETED);
        }
        if (taskProp.getMonsterId() <= 0) {
            throw new GeminiException(ErrorCode.MONSTER_NOT_EXIT);
        }
        SsScenePlayer.SkynetFindMonsterAsk.Builder builder = SsScenePlayer.SkynetFindMonsterAsk.newBuilder();
        builder.setPlayerId(getPlayerId()).setMonsterId(taskProp.getMonsterId());
        SsScenePlayer.SkynetFindMonsterAns ans = ownerActor().callBigScene(builder.build());
        return PlayerSkynet.Player_SkynetFindMonster_S2C.newBuilder()
                .setBornPoint(StructPB.PointPB.newBuilder()
                        .setX(ans.getBornPoint().getX())
                        .setY(ans.getBornPoint().getY()))
                .setLifeEndTsMs(ans.getLifeEndTsMs())
                .build();
    }

    public PlayerSkynet.Player_SkynetStoreInfo_S2C getSkynetStoreInfo() {
        if (!isSkynetUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_UNLOCK);
        }
        if (!isSkynetShopUnlock()) {
            throw new GeminiException(ErrorCode.SKYNET_STORE_UNLOCK);
        }
        PlayerSkynet.Player_SkynetStoreInfo_S2C.Builder builder = PlayerSkynet.Player_SkynetStoreInfo_S2C.newBuilder();
        for (SkynetShopTemplate skynetShopTemplate : ResHolder.getInstance().getListFromMap(SkynetShopTemplate.class)) {

            ShopHistoryProp shopHistoryProp = getShopProp().getGoodsHistory().get(skynetShopTemplate.getId());
            int laveShopCount = skynetShopTemplate.getLimitNum();
            if (shopHistoryProp != null) {
                laveShopCount = skynetShopTemplate.getLimitNum() - shopHistoryProp.getNum();
            }
            if (laveShopCount <= 0) {
                continue;
            }

            builder.addInfo(StructMsg.StoreInfo.newBuilder()
                    .setStoreType(CommonEnum.StoreType.STORE_TYPE_SKYNET)
                    .setConfigGoodsId(skynetShopTemplate.getId())
                    .setLeftCanBuyNum(laveShopCount)
                    .setMoneyNum(skynetShopTemplate.getPrice())
                    .setMoneyType(CommonEnum.CurrencyType.SKYNET_VALUE));
        }
        builder.setNextRefreshTsMs(getOwner().getRefreshComponent().getNextWeekRefreshTs());
        return builder.build();
    }

    private void checkOrFinishExploreTask(SkynetTaskProp taskProp) {
        if (!isDoingState(taskProp.getSkynetTaskStatus())) {
            return;
        }
        if (taskProp.getDoingTsMs() <= 0) {
            WechatLog.error("PlayerSkynetComponent explore task err, id={} taskId={} doingTsMs={}", taskProp.getId(), taskProp.getSkynetTaskId(), taskProp.getDoingTsMs());
            return;
        }
        CommonEnum.SkynetModelType moduleType = CommonEnum.SkynetModelType.SMT_NORMAL;
        long endTsMs = taskProp.getDoingTsMs() + TimeUtils.second2Ms(ResHolder.getConsts(ConstSkynetTemplate.class).getExploreTime());
        long laveFinishMs = endTsMs - SystemClock.now();
        if (laveFinishMs > 0) {
            ActorTimer actorTimer = ownerActor().addTimer(getExploreTimerKey(taskProp.getId()), TimerReasonType.SKYNET_EXPLORE_TASK, () -> execTaskFinish(taskProp, moduleType), laveFinishMs, TimeUnit.MILLISECONDS);
            if (actorTimer != null) {
                exploreTimers.add(actorTimer);
            }
        } else {
            execTaskFinish(taskProp, moduleType);
        }
    }

    private boolean checkOrUnlockSkynetSystem() {
        //FIXME 暂时屏蔽该功能
        return false;
    }

    /**
     * 外部保护好，调用前先判断是否已经解锁
     */
    private void unlockSkynet() {
        LOGGER.info("PlayerSkynetComponent unlockSkynet");
        long now = SystemClock.now();

        // 初始等级
        getBaseProp().setCurLevel(1);
        SkynetLevelTemplate curLevelTemplate = getCurLevelTemplate();

        // 初始刷普通任务
        getNormalProp().setLastRefreshTsMs(now).setTaskPoolNum(curLevelTemplate.getLevelUpGiveTaskNum());
        normalCheckRoot(NormalRefreshModel.ALL);

        // 初始刷守护者任务
        getGuardProp().setLastRefreshTsMs(now).setTaskPoolNum(curLevelTemplate.getLevelUpGiveDefenderTaskNum());
        guardCheckRoot();

        // 初始刷BOSS任务
        checkOrUnlockBossTask();
    }

    /**
     * 检测并自动补充守护者任务
     */
    private void checkOrUnlockGuardTask() {
        SkynetGuardTaskProp guardProp = getGuardProp();
        SkynetTaskProp guardTask = guardProp.getGuardTask();
        SkynetLevelTemplate curLevelTemplate = getCurLevelTemplate();
        int oldTaskPoolNum = guardProp.getTaskPoolNum();
        if (oldTaskPoolNum <= 0) {
            // 池子没了
            return;
        }
        // 有且只有一个进行中
        if (guardTask.getId() > 0 && isReceive(guardTask.getSkynetTaskStatus())) {
            return;
        }
        SkynetTaskTemplate skynetTaskTemplate = ResHolder.getResService(SkynetTemplateService.class).rollSkynetTaskTemplate(curLevelTemplate, true);
        if (skynetTaskTemplate == null) {
            return;
        }
        // 更新池子
        guardProp.setTaskPoolNum(oldTaskPoolNum - 1);
        LOGGER.info("PlayerSkynetComponent checkOrUnlockGuardTask oldPoolNum={} newPoolNum={}", oldTaskPoolNum, guardProp.getTaskPoolNum());

        CommonEnum.SkynetModelType smtGuard = CommonEnum.SkynetModelType.SMT_GUARD;
        int id = maxIdWithModel.getOrDefault(smtGuard, 0) + 1;
        maxIdWithModel.put(smtGuard, id);
        receiveGuardTask(guardTask, id, skynetTaskTemplate);
    }

    /**
     * 检测并自动补充普通任务
     */
    private void checkOrUnlockNormalTask() {
        SkynetNormalTaskProp normalProp = getNormalProp();
        SkynetLevelTemplate curLevelTemplate = getCurLevelTemplate();

        if (normalProp.getTaskPoolNum() <= 0) {
            // 池子没了
            return;
        }
        // 算出要补几个
        // 补几个 = Max(Min(最多拥有 - 目前有几个, 池子), 0)
        int hasCount = 0;
        for (SkynetTaskProp taskProp : getNormalProp().getNormalTask().values()) {
            if (isNormalTaskExpire(taskProp)) {
                continue;
            }
            hasCount++;
        }
        int oldTaskPoolNum = normalProp.getTaskPoolNum();
        int shouldAdd = Math.max(Math.min(curLevelTemplate.getDisplayMaxNum() - hasCount, oldTaskPoolNum), 0);

        if (shouldAdd > 0) {
            // 先更新池子数
            normalProp.setTaskPoolNum(oldTaskPoolNum - shouldAdd);
            LOGGER.info("PlayerSkynetComponent checkOrUnlockNormalTask oldPoolNum={} newPoolNum={}", oldTaskPoolNum, normalProp.getTaskPoolNum());

            // 挨个roll出来补
            SkynetTemplateService resService = ResHolder.getResService(SkynetTemplateService.class);
            for (int i = 0; i < shouldAdd; i++) {
                SkynetTaskTemplate skynetTaskTemplate = resService.rollSkynetTaskTemplate(curLevelTemplate, false);
                if (skynetTaskTemplate == null) {
                    continue;
                }
                CommonEnum.SkynetModelType smtNormal = CommonEnum.SkynetModelType.SMT_NORMAL;
                int id = maxIdWithModel.getOrDefault(smtNormal, 0) + 1;
                maxIdWithModel.put(smtNormal, id);
                SkynetTaskProp taskProp = getNormalProp().addEmptyNormalTask(id);
                receiveNormalTask(taskProp, skynetTaskTemplate);
            }
        }
    }

    /**
     * 检测并自动补充BOSS任务
     */
    public void checkOrUnlockBossTask() {
        SkynetTaskProp bossTask = getBossProp().getBossTask();
        // 没接过BOSS任务
        if (bossTask.getId() <= 0) {
            SkynetBossTemplate initBossTemplate = ResHolder.getResService(SkynetTemplateService.class).getInitBossTemplate();
            if (getOwner().getCityLevel() < initBossTemplate.getBaseLevel()) {
                return;
            }
            receiveBossTask(bossTask, initBossTemplate);
            return;
        }

        CommonEnum.SkynetTaskStatus skynetTaskStatus = bossTask.getSkynetTaskStatus();
        // BOSS任务只能接取一个
        if (isReceive(skynetTaskStatus)) {
            return;
        }
        SkynetBossTemplate nextBossTemplate = ResHolder.getResService(SkynetTemplateService.class).getNextBossTemplate(bossTask.getSkynetTaskId());
        if (nextBossTemplate == null) {
            // 没了
            return;
        }
        if (getOwner().getCityLevel() < nextBossTemplate.getBaseLevel()) {
            return;
        }
        receiveBossTask(bossTask, nextBossTemplate);
    }

    private void receiveGuardTask(SkynetTaskProp guardTask, int id, SkynetTaskTemplate taskTemplate) {
        LOGGER.info("PlayerSkynetComponent receiveGuardTask, oldId={} newId={} template={}", guardTask.getId(), id, taskTemplate.getId());
        guardTask.setId(id).setSkynetTaskId(taskTemplate.getId());
        statusChange(guardTask, CommonEnum.SkynetTaskStatus.SNTS_DOING);
        sendTaskQLog(taskTemplate.getId(), taskTemplate.getQuality(), CommonEnum.SkynetModelType.SMT_GUARD, taskTemplate.getTaskType(), "task_show");
    }

    private void receiveBossTask(SkynetTaskProp bossTask, SkynetBossTemplate bossTemplate) {
        LOGGER.info("PlayerSkynetComponent receiveBossTask, oldBossTask={} newBossTask={}", bossTask.getId(), bossTemplate.getId());
        // BOSS的id就是配置id
        int id = bossTemplate.getId();
        bossTask.setId(id).setSkynetTaskId(bossTemplate.getId());
        statusChange(bossTask, CommonEnum.SkynetTaskStatus.SNTS_RECEIVED);
        sendTaskQLog(bossTemplate.getId(), 0, CommonEnum.SkynetModelType.SMT_BOSS, CommonEnum.SkynetTaskType.SNTT_MONSTER, "task_show");
    }

    private void receiveNormalTask(SkynetTaskProp normalProp, SkynetTaskTemplate taskTemplate) {
        LOGGER.info("PlayerSkynetComponent receiveNormalTask, id={} taskTemplateId={}", normalProp.getId(), taskTemplate.getId());
        normalProp.setSkynetTaskId(taskTemplate.getId());
        statusChange(normalProp, CommonEnum.SkynetTaskStatus.SNTS_RECEIVED);
        sendTaskQLog(taskTemplate.getId(), taskTemplate.getQuality(), CommonEnum.SkynetModelType.SMT_NORMAL, taskTemplate.getTaskType(), "task_show");
    }


    /**
     * 判断普通任务是否过期（函数只判断时间相关逻辑）
     *
     * @return true:已过期
     */
    private static boolean isNormalTaskExpire(SkynetTaskProp taskProp) {
        if (!isReceive(taskProp.getSkynetTaskStatus())) {
            return true;
        }
        // 进行中的任务不过期
        if (isDoingState(taskProp.getSkynetTaskStatus())) {
            return false;
        }
        // 待领奖的任务不过期
        if (isAwaitAwardsState(taskProp.getSkynetTaskStatus())) {
            return false;
        }
        SkynetTaskTemplate skynetTaskTemplate = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, taskProp.getSkynetTaskId());
        long now = SystemClock.now();
        // 接取时间必不为0
        if (taskProp.getReceiveTsMs() <= 0) {
            WechatLog.error("PlayerSkynetComponent isNormalTaskExpire data exception, prop={}", taskProp);
            return false;
        }
        if (taskProp.getReceiveTsMs() + TimeUtils.second2Ms(skynetTaskTemplate.getTime()) > now) {
            return false;
        }
        return true;
    }


    private static boolean isReceive(CommonEnum.SkynetTaskStatus skynetTaskStatus) {
        return !(skynetTaskStatus == CommonEnum.SkynetTaskStatus.SNTS_OVER || skynetTaskStatus == CommonEnum.SkynetTaskStatus.SNTS_NONE);
    }

    private static boolean isDoingState(CommonEnum.SkynetTaskStatus skynetTaskStatus) {
        return skynetTaskStatus == CommonEnum.SkynetTaskStatus.SNTS_DOING;
    }

    private static boolean isReceiveState(CommonEnum.SkynetTaskStatus skynetTaskStatus) {
        return skynetTaskStatus == CommonEnum.SkynetTaskStatus.SNTS_RECEIVED;
    }

    private static boolean isAwaitAwardsState(CommonEnum.SkynetTaskStatus skynetTaskStatus) {
        return skynetTaskStatus == CommonEnum.SkynetTaskStatus.SNTS_AWAIT_AWARD;
    }

    private static void onKillBigSceneMonster(PlayerKillBigSceneMonsterEvent event) {
        if (event.getMonsterCategory() != CommonEnum.MonsterCategory.BUILDING_GUARD_VALUE) {
            return;
        }
        event.getPlayer().getSkynetComponent().killCableMonster();
    }

    /**
     * 城市守卫者任务触发完成
     */
    private void killCableMonster() {
        SkynetTaskProp guardTask = getGuardProp().getGuardTask();
        if (guardTask.getId() <= 0) {
            return;
        }
        if (!isDoingState(guardTask.getSkynetTaskStatus())) {
            return;
        }
        LOGGER.info("PlayerSkynetComponent killCableMonster, onPlayerKillCable");
        execTaskFinish(guardTask, CommonEnum.SkynetModelType.SMT_GUARD);
    }

    public List<Long> attentionMonsterList() {
        List<Long> result = new ArrayList<>();
        for (SkynetTaskProp value : getNormalProp().getNormalTask().values()) {
            if (value.getMonsterId() > 0) {
                result.add(value.getMonsterId());
            }
        }
        result.add(getBossProp().getBossTask().getMonsterId());
        return result;
    }

    public void onAttentionMonsterDead(long monsterId, int monsterTemplateId, boolean beKill) {
        LOGGER.info("PlayerSkynetComponent onAttentionMonsterDead, monsterId={} template={}", monsterId, monsterTemplateId);
        boolean operaSuccess = false;
        for (SkynetTaskProp normalProp : getNormalProp().getNormalTask().values()) {
            if (!isDoingState(normalProp.getSkynetTaskStatus())) {
                continue;
            }
            if (normalProp.getMonsterId() == monsterId) {
                operaSuccess = true;
                if (beKill) {
                    execTaskFinish(normalProp, CommonEnum.SkynetModelType.SMT_NORMAL);
                } else {
                    rollBackTaskStatus(normalProp, CommonEnum.SkynetModelType.SMT_NORMAL, "MonsterLeftOver");
                    normalCheckRoot(NormalRefreshModel.ALL);
                }
                // 野怪有且只有一个对应任务
                break;
            }
        }
        SkynetTaskProp bossTask = getBossProp().getBossTask();
        if (isDoingState(bossTask.getSkynetTaskStatus()) && bossTask.getMonsterId() == monsterId) {
            operaSuccess = true;
            if (beKill) {
                execTaskFinish(bossTask, CommonEnum.SkynetModelType.SMT_BOSS);
            } else {
                rollBackTaskStatus(bossTask, CommonEnum.SkynetModelType.SMT_BOSS, "MonsterLeftOver");
            }
        }
        if (!operaSuccess) {
            // 由scene通知过来，但是普通任务可能会过期，此时任务就不存在了
            LOGGER.warn("PlayerSkynetComponent onAttentionMonsterDead, task not exist, monsterId={} monsterTemplateId={} normal={} boss={}", monsterId, monsterTemplateId, getNormalProp().getNormalTask(), getBossProp().getBossTask());
        }
    }

    /**
     * 任务QLog
     *
     * @param taskId  任务id
     * @param quality 任务品质
     * @param reason  行为
     */
    public void sendTaskQLog(int taskId, int quality, CommonEnum.SkynetModelType module, CommonEnum.SkynetTaskType type, String reason) {
        QlogCncSkynetTask qlogCncSkynetTask = QlogCncSkynetTask.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setTaskId(taskId)
                .setAction(reason)
                .setTaskQuality(quality);
        if (module != null) {
            qlogCncSkynetTask.setTask_module(module.getNumber());
        } else {
            qlogCncSkynetTask.setTask_module(0);
        }
        if (type != null) {
            qlogCncSkynetTask.setTask_type(type.getNumber());
        } else {
            qlogCncSkynetTask.setTask_type(0);
        }
        qlogCncSkynetTask.sendToQlog();
    }

    /**
     * 等级QLog
     *
     * @param count      经验变化值
     * @param levelAfter 变化后等级
     * @param expAfter   变化后经验
     */
    public void sendLevelQLog(int count, int levelAfter, int expAfter) {
        QlogCncSkynetLevel.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setICount(count)
                .setLevelAfter(levelAfter)
                .setExpAfter(expAfter)
                .sendToQlog();
    }


    public void addExpByGm(long value) {
        addExp((int) value);
    }

    public void resetBaseByGm() {
        Player.PlayerSkynetModel build = Player.PlayerSkynetModel.newBuilder().build();
        getOwner().getProp().getSkynetModel().mergeFromDb(build);
        if (normalExpireTimer != null && !normalExpireTimer.isCanceled()) {
            normalExpireTimer.cancel();
        }
        if (normalRefreshTimer != null && !normalRefreshTimer.isCanceled()) {
            normalRefreshTimer.cancel();
        }

        if (guardRefreshTimer != null && !guardRefreshTimer.isCanceled()) {
            guardRefreshTimer.cancel();
        }
        unlockSkynet();
    }

    public void finishTaskByGm(CommonEnum.SkynetModelType type, int id) {
        switch (type) {
            case SMT_BOSS: {
                execTaskFinish(getBossProp().getBossTask(), type);
                break;
            }
            case SMT_GUARD: {
                execTaskFinish(getGuardProp().getGuardTask(), type);
                break;
            }
            case SMT_NORMAL: {
                if (id == 0) {
                    for (SkynetTaskProp value : getNormalProp().getNormalTask().values()) {
                        execTaskFinish(value, type);
                    }
                } else {
                    execTaskFinish(getNormalProp().getNormalTask().get(id), type);
                }
                break;
            }
            default:
                break;
        }
    }

    public void addTaskByGm(CommonEnum.SkynetModelType type, int id) {
        switch (type) {
            case SMT_BOSS: {
                SkynetBossTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetBossTemplate.class, id);
                receiveBossTask(getBossProp().getBossTask(), valueFromMap);
                break;
            }
            case SMT_GUARD: {
                SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, id);
                CommonEnum.SkynetModelType smtGuard = CommonEnum.SkynetModelType.SMT_GUARD;
                int onlyId = maxIdWithModel.getOrDefault(smtGuard, 0) + 1;
                maxIdWithModel.put(smtGuard, onlyId);
                receiveGuardTask(getGuardProp().getGuardTask(), onlyId, valueFromMap);
                break;
            }
            case SMT_NORMAL: {
                SkynetTaskTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(SkynetTaskTemplate.class, id);
                CommonEnum.SkynetModelType smtNormal = CommonEnum.SkynetModelType.SMT_NORMAL;
                int onlyId = maxIdWithModel.getOrDefault(smtNormal, 0) + 1;
                maxIdWithModel.put(smtNormal, onlyId);
                SkynetTaskProp taskProp = getNormalProp().addEmptyNormalTask(onlyId);
                receiveNormalTask(taskProp, valueFromMap);
                break;
            }
            default:
                break;
        }
    }

    public void addPointByGm(int value) {
        addPoint(value);
    }


    public boolean isSkynetNormalMonster(int monsterId) {
        MonsterTemplate valueFromMap = ResHolder.getInstance().findValueFromMap(MonsterTemplate.class, monsterId);
        if (valueFromMap != null && valueFromMap.getQuality() == CommonEnum.SceneObjQuality.NORMAL) {
            return true;
        }
        return false;
    }

    enum NormalRefreshModel {
        POOL,
        EXPIRE_TASK,
        ALL
    }
}

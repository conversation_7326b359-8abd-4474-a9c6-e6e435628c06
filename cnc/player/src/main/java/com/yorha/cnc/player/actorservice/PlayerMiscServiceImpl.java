package com.yorha.cnc.player.actorservice;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.component.PlayerStatisticComponent;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.actor.PlayerMiscService;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.notification.NotificationBuilder;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.rank.RankDataTemplateService;
import com.yorha.common.resource.resservice.scene.AchievementResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.AssistRecordProp;
import com.yorha.game.gen.prop.BigSceneRecordsProp;
import com.yorha.game.gen.prop.DataRecordUnitProp;
import com.yorha.game.gen.prop.PlayerInnerQuestModelProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.SsPlayerMisc.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.MonsterTemplate;
import res.template.SoldierTypeTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.statistic.StatisticEnum.*;
import static com.yorha.proto.CommonEnum.AchievementStatisticCheckerType.ASCT_KILL_MONSTER_GROUP;
import static com.yorha.proto.CommonEnum.DataRecordType.DRT_RESOURCE_COLLECT;
import static com.yorha.proto.CommonEnum.MonsterCategory.*;
import static com.yorha.proto.CommonEnum.Reason.ICR_NONE;
import static com.yorha.proto.CommonEnum.Reason.ICR_RES_COLLECT;

/**
 * 处理杂项逻辑
 * <AUTHOR>
 */
public class PlayerMiscServiceImpl implements PlayerMiscService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerMiscServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerMiscServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    @Override
    public void handleQueryPlayerCardInfoDetailAsk(QueryPlayerCardInfoDetailAsk ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        CommonMsg.PlayerCardInfoDetail.Builder builder = CommonMsg.PlayerCardInfoDetail.newBuilder();
        for (DataRecordUnitProp value : playerEntity.getProp().getPlayerDataRecord().getRecords().values()) {
            builder.putRecords(value.getPrivateKey(), value.getCopyCsBuilder().build());
        }
        QueryPlayerCardInfoDetailAns ans = QueryPlayerCardInfoDetailAns.newBuilder().setDetail(builder).build();
        playerActor.answer(ans);
    }

    @Override
    public void handleBanPlayerFixMsAsk(BanPlayerFixMsAsk ask) {
        playerActor.getOrLoadEntity().getPlayerBanTimeComponent().disablePlayer(ask);
        playerActor.answer(BanPlayerFixMsAns.getDefaultInstance());
    }

    @Override
    public void handleCancelBanPlayerAsk(CancelBanPlayerAsk ask) {
        playerActor.getOrLoadEntity().getPlayerBanTimeComponent().cancelBan();
        playerActor.answer(CancelBanPlayerAns.getDefaultInstance());
    }

    @Override
    public void handleGetSpyDataAsk(GetSpyDataAsk ask) {
        GetSpyDataAns.Builder ans = GetSpyDataAns.newBuilder();
        Map<Integer, Long> resource = playerActor.getOrLoadEntity().getResourceProtectComponent().getPlunderResource();
        ans.putAllResource(resource);
        playerActor.answer(ans.build());
    }

    @Override
    public void handleEnergyRollbackCmd(EnergyRollbackCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }

        if (ask.getEnergy() > 0) {
            String addEnergyReason = "attack_monster_return";
            int realAdd = playerEntity.getEnergyComponent().addEnergy(ask.getEnergy(), false, addEnergyReason, 0);
            int mailId;
            if (realAdd >= ask.getEnergy()) {
                mailId = ResHolder.getConsts(ConstTemplate.class).getEnergyRollBackMailId1();
            } else {
                // 体力超上限了，有浪费
                mailId = ResHolder.getConsts(ConstTemplate.class).getEnergyRollBackMailId2();
            }
            sendMail(playerEntity, realAdd, mailId);
        }
    }

    private void sendMail(PlayerEntity playerEntity, int realAdd, int mailId) {
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(mailId)
                .getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64, realAdd));

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(playerEntity.getPlayerId())
                .setZoneId(playerEntity.getZoneId())
                .build();
        playerEntity.getMailComponent().sendPersonalMail(receiver, mailSendParams.build());
    }

    @Override
    public void handleKillBigSceneMonsterCmd(KillBigSceneMonsterCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        MonsterCreateType createType = ask.getCreateType();
        BigSceneRecordsProp bigSceneRecords = playerEntity.getProp().getFragments().getBigSceneRecords();
        // 扣除自动补齐的次数
        if (createType == MonsterCreateType.SYSTEM_ADD) {
            int autoAddMonsterCount = bigSceneRecords.getAutoAddMonsterCount();
            bigSceneRecords.setAutoAddMonsterCount(Math.max(--autoAddMonsterCount, 0));
            playerEntity.getScheduleComponent().updateByExecuteTime(SystemClock.now());
        }
        MonsterTemplate monsterTemplate = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, ask.getMonsterId());

//        for (int heroId : ask.getHeroListList()) {
//            boolean isLevelUp = playerEntity.getHeroComponent().execAddHeroExp(heroId, ask.getExp(), HeroAddExpReason.ATTACK_MONSTER);
//            if (isLevelUp) {
//                playerEntity.getHeroComponent().postHeroPropChange(heroId, false);
//            }
//        }
        MonsterCategory category = monsterTemplate.getCategory();
        // 仅击杀大世界活跃野怪和新手野怪才会更新统计项
        if (category == BIG_SCENE_ACTIVE || category == NEWBIE_MONSTER || playerEntity.getSkynetComponent().isSkynetNormalMonster(ask.getMonsterId())) {
            playerEntity.getStatisticComponent().recordSecondStatistic(KILL_MONSTER_NUM_GROUPBY_LEVEL, monsterTemplate.getLevel(), 1);
            playerEntity.getStatisticComponent().recordSecondStatistic(KILL_SPECIAL_MONSTER, monsterTemplate.getId(), 1);
        }
        if (category == RALLY_MONSTER) {
            playerEntity.getStatisticComponent().recordSecondStatistic(KILL_RALLY_MONSTER_NUM_GROUPBY_LEVEL, monsterTemplate.getLevel(), 1);
        }
        // 统计每日击杀城市守护者数量
        if (category == BUILDING_GUARD) {
            int killNumToday = playerEntity.getProp().getKillMonsterModel().getGuardKillNumDay();
            killNumToday++;
            int limit = ResHolder.getConsts(ConstTemplate.class).getMonsterCityGuardEverydayRewardTimes();

            if (killNumToday <= limit) {
                playerEntity.getProp().getKillMonsterModel().setGuardKillNumDay(killNumToday);
                int mailId = monsterTemplate.getMailId();
                // 发放守护者每日邮件奖励
                if (mailId > 0) {
                    StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
                    params.setMailTemplateId(mailId);
                    params.getContentBuilder().getDisplayDataBuilder().getParamsBuilder()
                            .addDatas(Struct.DisplayParam.newBuilder().setType(DisplayParamType.DPT_INT64).setNumber(killNumToday).build())
                            .addDatas(Struct.DisplayParam.newBuilder().setType(DisplayParamType.DPT_INT64).setNumber(limit).build());

                    final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(playerEntity.getPlayerId())
                            .setZoneId(playerEntity.getZoneId())
                            .build();
                    playerEntity.getMailComponent().sendPersonalMail(receiver, params.build());
                }
            }

        }
        new PlayerKillBigSceneMonsterEvent(playerEntity,
                monsterTemplate.getId(),
                monsterTemplate.getCategory().getNumber(),
                monsterTemplate.getLevel(),
                ask.getSummonPlayerId()).dispatch();
        // 击杀野怪组成就
        final int groupId = ResHolder.getResService(AchievementResService.class).getGroupId(monsterTemplate.getId());
        if (groupId > 0) {
            final long oldValue = playerEntity.getStatisticComponent().getSecondStatistic(KILL_MONSTER_GROUP_NUM, groupId);
            playerEntity.getStatisticComponent().recordSecondStatistic(KILL_MONSTER_GROUP_NUM, groupId, 1);
            final long newValue = playerEntity.getStatisticComponent().getSecondStatistic(KILL_MONSTER_GROUP_NUM, groupId);
            playerEntity.getAchievementComponent().onTrigger(ASCT_KILL_MONSTER_GROUP, groupId, oldValue, newValue);
        }
    }

    @Override
    public void handleAddRewardCmd(AddRewardCmd ask) {
        PlayerEntity playerEntity = playerActor.getEntityOrNull();
        if (playerEntity == null) {
            playerActor.addOfflineRpc();
            return;
        }
        String subReason = "";
        if (ask.hasSubReason()) {
            subReason = ask.getSubReason();
        }
        // 拾取奖励
        if (CollectionUtils.isNotEmpty(ask.getItemRewards().getDatasList())) {
            for (Struct.ItemReward reward : ask.getItemRewards().getDatasList()) {
                playerEntity.getItemComponent().addItem(reward.getItemTemplateId(), reward.getCount(), ask.getReason(), subReason);
            }
        }
    }

    @Override
    public void handleMainCityDefendLoseCmd(MainCityDefendLoseCmd ask) {

    }

    @Override
    public void handleOnSettleRoundCmd(OnSettleRoundCmd ask) {
    }

    @Override
    public void handleAddAssistHistoryCmd(AddAssistHistoryCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        Struct.AssistRecord record = ask.getProp();
        AssistRecordProp prop = new AssistRecordProp()
                .setAssistTsMs(record.getAssistTsMs())
                .setSoldierNum(record.getSoldierNum())
                .setPlayerId(record.getPlayerId());
        prop.getCardHead().mergeFromSs(record.getCardHead());
        playerEntity.getProp().getFragments().getAssistHistory().add(0, prop);
        // 清理上限
        int assistRecordNumLimit = ResHolder.getResService(ConstKVResService.class).getTemplate().getAssistanceRecord();
        if (playerEntity.getProp().getFragments().getAssistHistorySize() > assistRecordNumLimit) {
            playerEntity.getProp().getFragments().removeAssistHistoryIndex(assistRecordNumLimit - 1);
        }
    }

    @Override
    public void handleUpdateScenePowerCmd(UpdateScenePowerCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }

        playerEntity.getPowerComponent().updatePowerWithPower(ask.getPowerType(), ask.getNewPower(), ask.getSoldierNumChangeReason());
    }

    @Override
    public void handleOnBattleRelationEndCmd(OnBattleRelationEndCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }

        // 战斗结果统计
        switch (ask.getBattleResult()) {
            case BRT_WIN: {
                playerEntity.getDataRecordComponent().updateDataRecord(DataRecordType.DRT_BATTLE_WIN_TIMES, DataRecordCalcType.DRCT_ADD, 1);
                break;
            }
            case BRT_LOSS: {
                playerEntity.getDataRecordComponent().updateDataRecord(DataRecordType.DRT_BATTLE_DEFEAT_TIMES, DataRecordCalcType.DRCT_ADD, 1);
                break;
            }
            case BRT_DRAW: {
                break;
            }
            default: {
                LOGGER.error("battleResult type error. ask:{}", ask);
            }
        }

        // 击杀统计
        if (!ask.getKillRecordMap().isEmpty()) {
            PlayerStatisticComponent statisticComponent = playerEntity.getStatisticComponent();
            ask.getKillRecordMap().forEach((k, v) -> {
                long oldNum = statisticComponent.getSecondStatistic(SOLDIER_KILL_TOTAL, k);
                statisticComponent.recordSecondStatistic(SOLDIER_KILL_TOTAL, k, v);
                LOGGER.debug("handleUpdateSceneDataRecordCmd player: {} update kill soldier: {}, oldNum:{} -> newNum:{}", playerEntity.getPlayerId(), k, oldNum, oldNum + v);
            });
            new PlayerKillSoldierEvent(playerEntity, ask.getKillRecordMap(), ask.getNpcEnemy(), ask.getOtherCurZoneId()).dispatch();
            playerEntity.getDataRecordComponent().refreshKillScore();

            // 主副将累计击杀记录
            playerEntity.getHeroComponent().recordPlayerSoldierKillData(ask.getHeroOrPlaneRecord(), ask.getKillRecordMap());
            // 历史杀敌数计数(pvp)
            statisticComponent.recordSingleStatistic(KILL_PVP_TOTAL, 1);
        }

        // 士兵统计，不算防御塔
        int deadSoldierCount = 0;
        for (Struct.Soldier soldier : ask.getSoldierResultList()) {
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldier.getSoldierId());
            if (soldierTemplate != null && soldierTemplate.getSoldierType() != SoldierType.ST_GuardTower_VALUE) {
                deadSoldierCount += soldier.getDeadNum();
            }
        }
        if (deadSoldierCount > 0) {
            playerEntity.getDataRecordComponent().updateDataRecord(DataRecordType.DRT_TOTAL_DEAD_SOLDIER_NUM, DataRecordCalcType.DRCT_ADD, deadSoldierCount);
        }
    }

    @Override
    public void handleRecordZoneSnapshotCmd(RecordZoneSnapshotCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        playerEntity.getQlogComponent().recordZoneSnapshot();
    }

    @Override
    public void handleOnReceiveMailCmd(OnReceiveMailCmd ask) {
        PlayerEntity playerEntity = playerActor.getEntityOrNull();
        final long senderId = ask.getNewMailCache().getSender().getSenderId();
        if (playerEntity == null) {
            playerActor.addOfflineRpc();
            notifyNewPrivateMail(senderId);
            return;
        }
        // 玩家离线，entity可能还未销毁
        if (!playerEntity.isOnline()) {
            notifyNewPrivateMail(senderId);
        }
        playerEntity.getMailComponent().handleReceiveMail(ask.getNewMailCache(), ask.getIdIpMailData());
    }

    /**
     * 推送：新私人邮件
     *
     * @param senderId 发送者id（私人邮件id>0）
     */
    private void notifyNewPrivateMail(final long senderId) {
        if (senderId <= 0) {
            return;
        }
        playerActor.notifyPlayer(NotificationBuilder.NEW_PRIVATE_MAIL);
    }

    @Override
    public void handleViewBuildingsAsk(ViewBuildingsAsk ask) {
        ViewBuildingsAns.Builder ansBuilder = ViewBuildingsAns.newBuilder();
        PlayerEntity playerEntity = playerActor.getEntityOrNull();

        // 发起方
        String aId = playerActor.getCurrentEnvelope().getSender().getActorId();
        // 被看方，其实就是本actor
        long bId = playerActor.getPlayerId();

        if (playerEntity == null) {
            LOGGER.info("handleViewBuildingsAsk a:{} view b:{} not in memory", aId, bId);
        }
        playerActor.answerWithContext(playerActor.getCurrentEnvelope(), ansBuilder.build());
    }

    @Override
    public void handleGetIdIpPlayerInfoAsk(GetIdIpPlayerInfoAsk ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        ChatPlayerEntity chatPlayerEntity = playerActor.getOrLoadChatPlayerEntity();
        GetIdIpPlayerInfoAns.Builder ans = GetIdIpPlayerInfoAns.newBuilder();
        if (playerEntity != null) {
            int platId = playerEntity.getClientInfo().getPlatformId();
            if ((platId != 0) && (platId != 1)) {
                platId = playerEntity.getProp().getBasicInfo().getBornPlatId();
            }
            ans.setId(playerEntity.getPlayerId())
                    .setBanEndTime(playerEntity.getPlayerBanTimeComponent().getBanEndTsMs())
                    .setCityLevel(playerEntity.getCityLevel())
                    .setCreateTime(playerEntity.getProp().getCreateTime())
                    .setLastLoginTime(playerEntity.getProp().getBasicInfo().getLastLoginTsMs())
                    .setLastLogoutTime(playerEntity.getProp().getBasicInfo().getLastLogoutTsMs())
                    .setMuteEndTime(chatPlayerEntity.getHandleChatComponent().getMuteEndTsMs())
                    .setName(playerEntity.getName())
                    .setOpenId(playerEntity.getOpenId())
                    .setPid(platId)
                    .setRemain(playerEntity.getPurseComponent().getCurrencyAmount(CurrencyType.DIAMOND))
                    .setTotal(0)  // 不接入充值！
                    .setVipLevel(playerEntity.getVipComponent().getVipLevel())
                    .setZoneId(playerEntity.getZoneId())
                    .setPower(playerEntity.totalPower())
                    .setClanId(playerEntity.getPlayerClanComponent().getClanId());
        }
        playerActor.answerWithContext(playerActor.getCurrentEnvelope(), ans.build());
    }

    @Override
    public void handleSyncPlaneStatusWithArmyBackCmd(SyncPlaneStatusWithArmyBackCmd ask) {
        // 现在飞机状态交由scenePlayer来维护
//        new PlaneBackEvent(playerActor.getEntity(), Lists.newArrayList(ask.getPlaneId())).dispatch();
    }

    @Override
    public void handleOnArmyReturnCmd(OnArmyReturnCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        // 加采集资源
        for (Map.Entry<Integer, Long> entry : ask.getCollectResMap().entrySet()) {
            CurrencyType currencyType = CurrencyType.forNumber(entry.getKey());
            if (currencyType != null) {
                String reason = ask.getIsCollectFromClanRes() ? "CLAN_RES_COLLECT" : "RES_COLLECT";
                playerEntity.getPurseComponent().give(currencyType, entry.getValue(), ICR_RES_COLLECT, reason);
                playerEntity.getStatisticComponent().recordSecondStatistic(RESBUILDING_COLLECT_RESOURCE_NUM, entry.getKey(), entry.getValue().intValue());
                playerEntity.getStatisticComponent().recordSingleStatistic(RESBUILDING_COLLECT_ANY_RESOURCE_NUM, entry.getValue().intValue());
            } else {
                LOGGER.error("player:{}, receive collect resource failed. currency:{}", playerEntity, entry.getKey());
            }
        }
        if (!ask.getCollectResMap().isEmpty()) {
            // 有采集，才增加排行榜数据
            playerEntity.getPlayerRankComponent().updateCollectRank();
            // 有采集，才能更新玩家数据统计模块
            long score = playerEntity.getPlayerRankComponent().getCollectScore();
            playerEntity.getDataRecordComponent().updateDataRecord(DRT_RESOURCE_COLLECT, DataRecordCalcType.DRCT_COVER, score);
            // 必须采集到资源才能更新”世界采集资源次数“的统计项
            playerEntity.getStatisticComponent().recordSingleStatistic(RESBUILDING_COLLECT_RESOURCE_TIME, 1);
            new ResBuildingCollectResourceEvent(playerEntity, ask.getCollectResMap(), ask.getOutTimes()).dispatch();
        }
        for (Map.Entry<Integer, Long> entry : ask.getPlunderResMap().entrySet()) {
            CurrencyType currencyType = CurrencyType.forNumber(entry.getKey());
            if (currencyType != null) {
                playerEntity.getPurseComponent().give(currencyType, entry.getValue(), ICR_RES_COLLECT, "RES_PLUNDER");
            } else {
                LOGGER.error("player:{}, receive plunder resource failed. currency:{}", playerEntity, entry.getKey());
            }
        }
        playerEntity.getHeroComponent().releaseHeroState(ask.getReleaseHeroCmd().getReleaseHeroIdListList());
        playerEntity.getPlaneComponent().releasePlane(ask.getReleasePlaneCmd());
        playerEntity.getWallComponent().garrisonHeroOrMecha();
    }

    @Override
    public void handleOnCityBattleEndAsk(OnCityBattleEndAsk ask) {
        OnCityBattleEndAns.Builder ans = OnCityBattleEndAns.newBuilder();
        // 掠夺资源
        Map<Long, PlunderResult> plunderResultMap = playerActor.getOrLoadEntity().getBattleComponent().handleBePlundered(ask.getPlunderAskMap());
        ans.putAllPlunderResult(plunderResultMap);
        playerActor.answer(ans.build());
    }

    @Override
    public void handleEnterBattleCmd(EnterBattleCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        playerEntity.getTaskComponent().enterBattle(ask);
    }

    @Override
    public void handleReleasePlaneCmd(ReleasePlaneCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        playerEntity.getPlaneComponent().releasePlane(ask);
    }

    @Override
    public void handleReturnTransportPlaneCmd(ReturnTransportPlaneCmd ask) {
        if (ask.getTransportPlaneId() > 0) {
            PlayerEntity playerEntity = playerActor.getOrLoadEntity();
            playerEntity.getPlaneComponent().returnTransportPlane(ask.getTransportPlaneId());
        }
    }


    @Override
    public void handleSendSurveyMailCmd(SendSurveyMailCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (ask.hasType()) {
            playerEntity.getTaskComponent().updateExploreMapBuildingTask(ask.getType(), true);
        }
        playerEntity.getStatisticComponent().recordSingleStatistic(SURVEY_REWARD_TIMES, 1);

    }

    @Override
    public void handleUpdateRedDotCmd(UpdateRedDotCmd cmd) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getRedDotComponent().handleUpdateRedDotCmd(cmd);
    }

    @Override
    public void handleSoldierNumCmd(SoldierNumCmd ask) {
        PlayerEntity entity = playerActor.getOrLoadEntity();
        new SoldierNumChangeEvent(entity, ask.getSoldierId2NumMap()).dispatch();
    }

    @Override
    public void handleClanBuildingInfoCmd(ClanBuildingInfoCmd ask) {
        PlayerEntity entity = playerActor.getOrLoadEntity();

        if (!ask.getClanBuildingMap().isEmpty()) {
            new ClanBuildingInfoEvent(entity, ask.getClanBuildingMap()).dispatch();
        }
        if (ask.hasPartNum()) {
            new ClanTerritoryChangeEvent(entity).dispatch();
        }
    }

    @Override
    public void handleQueryPlayerKillDetailAsk(QueryPlayerKillDetailAsk ask) {
        Map<Integer, Struct.SingleStatistic> statisticMap = playerActor.getOrLoadEntity().getStatisticComponent().getAllSecondStatistic(StatisticEnum.SOLDIER_KILL_TOTAL);
        Map<Integer, SoldierTypeTemplate> soldierTypeTemplateMap = ResHolder.getInstance().getMap(SoldierTypeTemplate.class);
        QueryPlayerKillDetailAns.Builder builder = QueryPlayerKillDetailAns.newBuilder();
        for (Map.Entry<Integer, Struct.SingleStatistic> entry : statisticMap.entrySet()) {
            if (soldierTypeTemplateMap.get(entry.getKey()).getKillScore() <= 0) {
                continue;
            }
            builder.putSoldierKillStatistic(entry.getKey(), entry.getValue());
        }
        playerActor.answer(builder.build());
    }

    @Override
    public void handleOnCollectEndCmd(OnCollectEndCmd ask) {
        if (ask.getRewardId() > 0) {
            PlayerEntity entity = playerActor.getOrLoadEntity();
            entity.getItemComponent().sendReward(ask.getRewardId(), ICR_NONE, "OnCollectEnd");
        }
    }

    @Override
    public void handleBroadcastMileStoneSwitchCmd(BroadcastMileStoneSwitchCmd ask) {
        PlayerEntity orLoadEntity = playerActor.getOrLoadEntity();
        orLoadEntity.getMileStoneComponent().updateMileStoneData(ask.getNewMileStoneId(), ask.getCurMileStoneEndTsMs(),
                Lists.newArrayList(ask.getRewardData()), false, ask.getMileStoneTemplateId(), "broadcast");
    }

    @Override
    public void handleBroadcastMileStoneResetCmd(BroadcastMileStoneResetCmd ask) {
        PlayerEntity orLoadEntity = playerActor.getOrLoadEntity();
        orLoadEntity.getMileStoneComponent().resetDataByGm();
    }

    @Override
    public void handleMutePlayerFixMsAsk(MutePlayerFixMsAsk ask) {
        long muteTime = ask.getMuteFixMs();
        long timeStamp;
        ChatPlayerEntity chatPlayerEntity = playerActor.getOrLoadChatPlayerEntity();
        long oldMuteTime = chatPlayerEntity.getProp().getBanTsMs();
        if (oldMuteTime < SystemClock.now()) {
            timeStamp = SystemClock.now() + muteTime;
        } else {
            timeStamp = oldMuteTime + muteTime;
        }
        chatPlayerEntity.getHandleChatComponent().banChat(timeStamp);
        playerActor.answer(MutePlayerFixMsAns.getDefaultInstance());
    }

    @Override
    public void handleCancelMutePlayerAsk(CancelMutePlayerAsk ask) {
        ChatPlayerEntity chatPlayerEntity = playerActor.getOrLoadChatPlayerEntity();
        chatPlayerEntity.getHandleChatComponent().cancelBanChat();
        playerActor.answer(CancelMutePlayerAns.getDefaultInstance());
    }

    @Override
    public void handleResetPlayerInfoAsk(ResetPlayerInfoAsk ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        boolean resetName = ask.getResetName();
        boolean resetPic = ask.getResetPic();
        int quietSecondTime = ask.getQuietSecondTime();
        player.getProfileComponent().resetPlayerInfo(resetName, resetPic, quietSecondTime);
        playerActor.answer(ResetPlayerInfoAns.getDefaultInstance());
    }

    @Override
    public void handleSyncServerOpenTsMsCmd(SyncServerOpenTsMsCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        //重置引用开服时间的模块
        player.getActivityComponent().resetActivity();
        player.getPlayerClanComponent().resetNextCanBeOwnerTsMs();
    }

    @Override
    public void handleGiveCurrencyCmd(GiveCurrencyCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        AssetPackage.Builder costBuilder = AssetPackage.builder();
        for (Struct.Currency currency : ask.getCurrencysList()) {
            costBuilder.plusCurrency(currency.getType(), currency.getCount());
        }
        player.getAssetComponent().give(costBuilder.build(), Reason.ICR_LOGISTICS);
    }

    @Override
    public void handleSendSysAssistCmd(SendSysAssistCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getPlaneComponent().addSysAssistTask(ask.getSysAssistInfo());
    }

    @Override
    public void handleCheckImurAsk(CheckImurAsk ask) {
        PlayerInnerQuestModelProp prop = playerActor.getOrLoadEntity().getProp().getQuestModel();
        CheckImurAns.Builder ans = CheckImurAns.newBuilder();
        if (!prop.getSids().contains(ask.getSid())) {
            prop.getSids().add(ask.getSid());
            ans.setResult(false);
        } else {
            ans.setResult(true);
        }
        playerActor.answer(ans.build());
    }

    @Override
    public void handleAddDevBuffFromSceneCmd(AddDevBuffFromSceneCmd cmd) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        CommonMsg.DevBuffAddParam param = cmd.getCmd().getParam();
        player.getPlayerDevBuffComponent().addDevBuffFromScenePlayer(param);
    }

    @Override
    public void handleRemoveDevBuffFromSceneCmd(RemoveDevBuffFromSceneCmd cmd) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getPlayerDevBuffComponent().removeDevBuffFromScenePlayer(cmd.getCmd().getParam());
    }

    @Override
    public void handleUpdateAdditionFromSceneCmd(UpdateAdditionFromSceneCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getAddComponent().updateAdditionFromScene(ask);
    }

    @Override
    public void handleUpdateAdditionFromZoneCmd(UpdateAdditionFromZoneCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getAddComponent().updateZoneAddition(ask.getSource(), ask.getAdditionMap());
    }

    @Override
    public void handleAddClanGiftForPlayerCmd(AddClanGiftForPlayerCmd ask) {
        PlayerEntity playerEntity = playerActor.getEntityOrNull();
        // 珍藏礼物走离线rpc，普通礼物仅广播在线玩家
        if (playerEntity == null) {
            playerActor.addOfflineRpc();
            return;
        }

        if (ask.hasGift()) {
            playerEntity.getClanGiftComponent().addClanGift(ask.getGift());
        }
        if (ask.hasTreasureGift()) {
            playerEntity.getClanGiftComponent().addTreasureGift(ask.getTreasureGift());
        }
    }

    @Override
    public void handleOnAllBattleEndCmd(OnAllBattleEndCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getBattleComponent().handleOnAllBattleEndCmd(ask);
        if (ask.getFail()) {
            // 设置最近一次战斗失败时的玩家在线时间
            long onlineTime = player.getOnlineMs();
            player.getProp().getRatingModel().setPvpFailTsMs(onlineTime);
            LOGGER.info("PlayerMiscServiceImpl handleOnAllBattleEndCmd, last battle fail onlineTsMs={}", onlineTime);
        }
    }

    @Override
    public void handleSpyMoveCmd(SpyMoveCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getPlaneComponent().handleSpyMove(ask);
    }

    @Override
    public void handleAddResourceAssistRecordCmd(AddResourceAssistRecordCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        // 计算资源援助排行榜的增加积分
        long resourceRankAddScore = 0;
        RankDataTemplateService service = ResHolder.getResService(RankDataTemplateService.class);
        for (Struct.Currency currency : ask.getCurrencysList()) {
            player.getStatisticComponent().recordSecondStatistic(RESOURCE_ASSIST_NUM, currency.getType(), currency.getCount());
            resourceRankAddScore += currency.getCount() * service.getCoefficientByCurrencyType(CurrencyType.forNumber(currency.getType()));
        }
        // 更新统计项
        long assistScore = player.getPlayerRankComponent().getAssistScore();
        player.getDataRecordComponent().updateDataRecord(CommonEnum.DataRecordType.DRT_RESOURCE_ASSIST, CommonEnum.DataRecordCalcType.DRCT_COVER, assistScore);

        // 更新联盟资源援助排行榜
        if (player.getPlayerClanComponent().isInClan()) {
            SsClanRank.UpdateClanRankingCmd.Builder updateRankingCmd = SsClanRank.UpdateClanRankingCmd.newBuilder();
            updateRankingCmd.setIncrease(true);
            updateRankingCmd.setScore(resourceRankAddScore);
            updateRankingCmd.setRankId(RankConstant.CLAN_PLAYER_RESOURCE_ASSIST_RANK);
            updateRankingCmd.setMemberId(player.getPlayerId());
            playerActor.tellCurClan(updateRankingCmd.build());
        }
    }

    @Override
    public void handleLogOffAccountAsk(LogOffAccountAsk ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        // 尝试踢人
        if (player.getSessionComponent().isOnline()) {
            player.kickOffMe(SessionCloseReason.SCR_ACCOUNT_LOGOFF);
        }
        // 先判断是否已经注销
        if (player.getProp().getBasicInfo().getLogOff()) {
            LOGGER.info("PlayerMiscServiceImpl handleLogOffAccountAsk, account has log off, playerId={}", playerActor.getPlayerId());
            playerActor.answer(LogOffAccountAns.getDefaultInstance());
            return;
        }
        // 静默期设置0天吧
        int quietSecondTime = (int) TimeUnit.DAYS.toSeconds(0L);
        // 设置账号注销
        player.getProp().getBasicInfo().setLogOff(true);
        // 重置名字和头像
        player.getProfileComponent().resetPlayerInfo(true, true, quietSecondTime);
        // 立刻更新名片
        player.getPlayerPropComponent().updatePlayerCardCache(true);
        LOGGER.info("PlayerMiscServiceImpl handleLogOffAccountAsk, player success logOff, playerId={}", player);
        playerActor.answer(LogOffAccountAns.getDefaultInstance());
    }

    @Override
    public void handleGetLastLoginTimeAsk(GetLastLoginTimeAsk ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        long lastLoginTsMs = player.getProp().getBasicInfo().getLastLoginTsMs();
        playerActor.answer(GetLastLoginTimeAns.newBuilder().setLastLoginTsMs(lastLoginTsMs).build());
    }

    @Override
    public void handleAddResourceAfterPlunderCmd(AddResourceAfterPlunderCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        for (Map.Entry<Integer, Long> entry : ask.getPlunderResMap().entrySet()) {
            CurrencyType currencyType = CurrencyType.forNumber(entry.getKey());
            if (currencyType != null) {
                playerEntity.getPurseComponent().give(currencyType, entry.getValue(), ICR_RES_COLLECT, "RES_PLUNDER");
            } else {
                LOGGER.error("player:{}, handleAddResourceAfterPlunderCmd failed currency:{}", playerEntity, entry.getKey());
            }
        }
    }

    @Override
    public void handleOnCityFallCmd(OnCityFallCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        long onlineTsMs = player.getOnlineMs();
        player.getProp().getRatingModel().setCityFallTsMs(onlineTsMs);
        LOGGER.info("PlayerMiscServiceImpl handleOnCityFallCmd, last cityFall onlineTsMs={}", onlineTsMs);
    }

    @Override
    public void handleOnBaseBeAttackCmd(OnBaseBeAttackCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        long onlineTsMs = player.getOnlineMs();
        player.getProp().getRatingModel().setBaseBeAttackTsMs(onlineTsMs);
        LOGGER.info("PlayerMiscServiceImpl handleOnBaseBeAttackCmd, last base be attack onlineTsMs={}", onlineTsMs);
    }

    @Override
    public void handleUpdateContactCmd(UpdateContactCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        player.getContactsComponent().tryUpdateContacts(ask.getPlayerId(), ask.getNewZoneId());
    }

    @Override
    public void handleMonsterDeadNtfOwnerCmd(MonsterDeadNtfOwnerCmd ask) {
        PlayerEntity player = playerActor.getOrLoadEntity();
        long monsterId = ask.getMonsterId();
        int monsterTemplateId = ask.getMonsterTemplateId();
        boolean beKill = ask.getBeKill();
        player.getSkynetComponent().onAttentionMonsterDead(monsterId, monsterTemplateId, beKill);
    }
}

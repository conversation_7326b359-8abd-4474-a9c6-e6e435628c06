package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.campaign.CampaignDifficultyLevelResService;
import com.yorha.common.resource.resservice.campaign.CampaignMissionLevelResService;
import com.yorha.common.resource.resservice.campaign.CampaignMissionRewardResService;
import com.yorha.common.resource.resservice.campaign.CampaignUnitLevelResService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.map.MapConfigResService;
import com.yorha.common.resource.resservice.troop.TroopResService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PlayerCampaignComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCampaignComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerCampaignComponent::campaignDailyRefresh);
    }

    static List<CommonEnum.BuffEffectType> campaignAdditionIds = Lists.newArrayList(
            CommonEnum.BuffEffectType.ET_PRISM_TOWER_ATTACK_FIXED, // 光棱塔攻击力
            CommonEnum.BuffEffectType.ET_BUNKER_ATTACK_FIXED,       // 碉堡攻击力
            CommonEnum.BuffEffectType.ET_PATRIOT_MISSLE_ATTACK_FIXED, // 导弹塔攻击力
            CommonEnum.BuffEffectType.ET_MISSION_UNIT_SIZE, // 出战数量增加
            CommonEnum.BuffEffectType.ET_POWER_PLANT_RECOVERY_FIXED // 电力恢复速度
    );

    private PlayerCampaignModelProp campaignModel;

    public PlayerCampaignComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        campaignModel = getOwner().getProp().getPlayerCampaignModel();
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            // 初始解锁事件
            campaignModel.getInfo().getEvents().addAll(ResHolder.getConsts(ConstCampaignTemplate.class).getDefaultUnlockEvent());
            campaignModel.setState(CommonEnum.CampaignState.CS_NONE);
            // 先刷新战役地图
            updateCampaign();
        }
    }

    public static void campaignDailyRefresh(PlayerDayRefreshEvent dayRefreshEvent) {
        PlayerEntity playerEntity = dayRefreshEvent.getPlayer();
        playerEntity.getCampaignComponent().updateCampaign();
    }

    // 更新战役
    public void updateCampaign() {
        // 战役进行中不更新
        if (campaignModel.getState() != CommonEnum.CampaignState.CS_NONE) {
            return;
        }
        // 刷新地图
        refreshMaps();
    }

    // 解锁事件
    public void unlockEvent(int eventId) {
        ResHolder.getTemplate(CampaignEventTemplate.class, eventId);
        if (campaignModel.getInfo().getEvents().contains(eventId)) {
            return;
        }
        campaignModel.getInfo().getEvents().add(eventId);
    }

    // 开始战役
    public void startCampaign(int mapIndex, List<IntPairType> items) {
        checkState(CommonEnum.CampaignState.CS_NONE); // 检查战役状态

        items = tidyItems(items); // 整理道具，数量合并
        checkItems(items); // 检查道具
        // 检查仓库
        int itemSize = ResHolder.getConsts(ConstCampaignTemplate.class).getStartStorage();
        checkItemSize(items, itemSize);
        // 检查体力
        int energyCost = ResHolder.getConsts(ConstCampaignTemplate.class).getPlayerInitEnergyCost();
        getOwner().getEnergyComponent().verifyThrow(energyCost);
        // 检查物品
        AssetPackage costItems = AssetPackage.builder().plusItems(items).build();
        getOwner().verifyThrow(costItems);

        // 战役信息
        CampaignInfoProp campaignInfo = campaignModel.getInfo();
        // 战役难度等级
        int campaignLevel = campaignInfo.getLevel();
        if (mapIndex < 0 || mapIndex >= campaignInfo.getMapsSize()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 任务奖励类型
        int campaignMapId = campaignInfo.getMapsIndex(mapIndex);
        // 生成任务关卡
        List<CampaignMissionProp> missions = getRandomMissionList(campaignLevel, campaignMapId);
        if (missions.isEmpty()) {
            throw new GeminiException(ErrorCode.INTERNAL_EXCEPTION); // 策划可能配错了
        }
        // 扣除体力
        getOwner().getEnergyComponent().consumeEnergy(energyCost, false, "campaign", campaignMapId);
        // 扣除道具
        getOwner().consume(costItems, CommonEnum.Reason.ICR_CAMPAIGN_START);

        // 清除数据
        campaignModel.clearMissions();
        campaignModel.clearBuildings();
        campaignModel.clearUnits();
        campaignModel.clearSkills();
        campaignModel.clearItems();
        campaignModel.clearAdditions();
        campaignModel.clearRewardItems();
        campaignModel.getEvent().setEventId(0).setEventTrainUnitId(0);
        campaignModel.getBattle().setMissionIndex(0).clearUnits();
        // 获取初始信息
        Map<Integer, CampaignBuildingProp> buildings = getInitBuildings(); // 建筑单位信息
        Map<Integer, CampaignUnitProp> units = getInitUnits();
        List<Integer> skills = getInitSkills();
        Map<Integer, CampaignAdditionProp> additions = getInitAdditions();
        // 初始化战役信息
        campaignModel.setCampaignLevel(campaignLevel);
        campaignModel.setMissionLevel(0);               // 重置难度
        campaignModel.getMissions().addAll(missions);   // 关卡
        campaignModel.getBuildings().putAll(buildings); // 建筑
        campaignModel.getUnits().putAll(units);         // 单位
        campaignModel.getSkills().addAll(skills);       // 技能
        campaignModel.setItemSize(itemSize);            // 仓库大小
        // 记录道具
        costItems.toItemReward().getDatasList().forEach(t -> campaignModel.putItemsV(new ItemPairProp().setItemTemplateId(t.getItemTemplateId()).setCount(t.getCount())));
        campaignModel.getAdditions().putAll(additions); // 加成

        LOGGER.info("Campaign start, campaign_level:{}, missions:{}", campaignModel.getCampaignLevel(), campaignModel.getMissions());
        startReadyState(); // 战役准备状态
    }

    // 开始关卡任务
    public CommonMsg.MissionMapInitInfo startMission(int missionIndex, List<Integer> unitIds, List<Integer> skills) {
        // 检查战役状态
        checkState(CommonEnum.CampaignState.CS_READY);

        if (missionIndex < 0 || missionIndex >= campaignModel.getMissionsSize()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CampaignMissionProp mission = campaignModel.getMissionsIndex(missionIndex);
        // 关卡已完成
        if (mission.getFinished()) {
            throw new GeminiException(ErrorCode.CAMPAIGN_MISSION_FINISHED);
        }
        // 去重
        unitIds = unitIds.stream().distinct().collect(Collectors.toList());
        skills = skills.stream().distinct().collect(Collectors.toList());
        List<CampaignUnitProp> units = getMissionUnits(unitIds);
        checkMissionSkills(skills);
        // 检查出战数量
        checkMissionUnitSize(units.size() + skills.size());
        // 任务关卡等级
        CampaignMissionLevelTemplate missionLevelCfg = ResHolder.getResService(CampaignMissionLevelResService.class).getMissionLevel(campaignModel.getCampaignLevel(), campaignModel.getMissionLevel());
        // CommonMsg.MissionMapInitInfo
        CommonMsg.MissionMapInitInfo.Builder builder = CommonMsg.MissionMapInitInfo.newBuilder();
        builder.setMapId(mission.getMapId());
        builder.setMissionLevelId(missionLevelCfg.getId());
        builder.putAllBuildings(campaignModel.getBuildings().getCopyCsBuilder().getDatasMap());
        units.forEach(t -> builder.putUnits(t.getUnitId(), t.getCopyCsBuilder().build()));
        builder.addAllSkills(skills);
        builder.addAllItems(campaignModel.getItems().getCopyCsBuilder().getDatasMap().values());
        builder.putAllAdditions(campaignModel.getAdditions().getCopyCsBuilder().getDatasMap());
        builder.addAllBuffs(campaignModel.getBuffs());

        Map<CommonEnum.BuffEffectType, Integer> additions = Maps.newHashMap();
        campaignModel.getAdditions().values().forEach(t -> additions.put(CommonEnum.BuffEffectType.forNumber(t.getAdditionId()), (int) t.getValue()));

        /**
         RtsBattle.RTSBattleInfo.Builder battleInfo = RtsBattle.RTSBattleInfo.newBuilder();
         battleInfo.setSeed(1); // FIXME: 随机数
         battleInfo.setMapId(mission.getMapId());
         List<RtsBattle.RTSUnit> battleUnits = units.stream().map(t -> RTSBattleHelper.InitCampaignUnit(t.getUnitId(), t.getStar(), t.getFatigued()).build()).collect(Collectors.toList());
         battleInfo.addAllUnits(battleUnits);
         List<RtsBattle.RTSBuilding> battleBuildings = campaignModel.getBuildings().values().stream().map(t -> RTSBattleHelper.InitCampaignBuilding(t.getId(), buildingId, additions).build()).collect(Collectors.toList());
         battleInfo.addAllBuildings(battleBuildings);
         battleInfo.addAllSkills(skills);
         // 道具技能
         campaignModel.getItems().forEach((k, v) -> {
         ItemTemplate itemCfg = ResHolder.getResService(ItemResService.class).getItemTemplate(k);
         if (itemCfg.getEffectType() == CommonEnum.ItemUseType.SUPER_WEAPON_VALUE) {
         battleInfo.putItemSkills(k, v.getCount());
         }
         });
         // 敌人的单位
         List<RtsBattle.RTSUnit> battleEnemyUnits = Lists.newArrayList();
         for (int i = 0; i < missionLevelCfg.getEnemyUnitList().size(); i++) {
         int id = battleEnemyUnits.size() + 1;
         int unitId = missionLevelCfg.getEnemyUnitList().get(i);
         RtsBattle.RTSUnit enemyUnit = RTSBattleHelper.InitEnemyUnit(id, unitId, missionLevelCfg.getUnitHpBonus(), missionLevelCfg.getUnitAttackBonus()).build();
         battleEnemyUnits.add(enemyUnit);
         }
         battleInfo.addAllEnemyUnits(battleEnemyUnits);
         // 敌人的建筑
         List<RtsBattle.RTSBuilding> battleBuildingUnits = Lists.newArrayList();
         for (int i = 0; i < missionLevelCfg.getEnemyArchitecturePairList().size(); i++) {
         int unitId = missionLevelCfg.getEnemyArchitecturePairList().get(i).getKey();
         int num = missionLevelCfg.getEnemyArchitecturePairList().get(i).getValue();
         for (int n = 0; n < num; n++) {
         int id = battleEnemyUnits.size() + 1;
         RtsBattle.RTSBuilding enemyBuilding = RTSBattleHelper.InitEnemyBuilding(id, unitId, missionLevelCfg.getArchitectureHpBonus(), missionLevelCfg.getArchitectureAttackBonus()).build();
         battleBuildingUnits.add(enemyBuilding);
         }
         }
         battleInfo.addAllEnemyBuildings(battleBuildingUnits);
         **/

        startMissionState(missionIndex, unitIds); // 开始作战任务
        return builder.build();
    }

    public void startMission2(int missionIndex, List<Integer> unitIds, List<Integer> skills) {
        //调用原接口
        startMission(missionIndex, unitIds, skills);

        //获取配置参数
//        CampaignMissionProp mission = campaignModel.getMissionsIndex(missionIndex);
//        List<CampaignUnitProp> units = getMissionUnits(unitIds);
        //加成
        Map<CommonEnum.BuffEffectType, Integer> additions = Maps.newHashMap();
        campaignModel.getAdditions().values().forEach(t -> additions.put(CommonEnum.BuffEffectType.forNumber(t.getAdditionId()), (int) t.getValue()));
        //关卡地方数据
        CampaignMissionLevelTemplate missionLevelCfg = ResHolder.getResService(CampaignMissionLevelResService.class).getMissionLevel(campaignModel.getCampaignLevel(), campaignModel.getMissionLevel());
        RTSBattleEnemyInfo info = new RTSBattleEnemyInfo();
        info.copyFromCampaignMissionLevel(missionLevelCfg);

//        RtsBattle.RTSBattleInfo battleInfo = packBattle(mission.getMapId(), units, skills, campaignModel.getBuildings(), campaignModel.getItems(), additions, info);
//        campaignModel.setBattleInfo(battleInfo.toByteString());
//        return battleInfo;
    }

    // 开始关卡任务-新接口
//    public RtsBattle.RTSBattleInfo packBattle(int mapId, List<CampaignUnitProp> units, List<Integer> skills, Map<Integer, CampaignBuildingProp> buildings, Map<Integer, ItemPairProp> items, Map<CommonEnum.BuffEffectType, Integer> additions, RTSBattleEnemyInfo enemyInfo) {
//        //新接口逻辑
//        RtsBattle.RTSBattleInfo.Builder battleInfo = RtsBattle.RTSBattleInfo.newBuilder();
//        battleInfo.setSeed(1); // FIXME: 随机数
//        battleInfo.setMapId(mapId);
//        //单位
//        List<RtsBattle.RTSUnit> battleUnits = units.stream().map(t -> RTSBattleHelper.initCampaignUnit(t.getUnitId(), t.getStar(), t.getFatigued(), t.getUnitLevel(), t.getHeroId(), t.getHeroLevel(), t.getBuildId(), t.getSlotId()).build()).collect(Collectors.toList());
//        battleInfo.addAllUnits(battleUnits);
//        //建筑
//        if (buildings != null) {
//            List<RtsBattle.RTSBuilding> battleBuildings = buildings.values().stream().map(t -> RTSBattleHelper.initCampaignBuilding(t.getId(), t.getUnitId(), 1, t.getPoint(), additions).build()).collect(Collectors.toList());
//            battleInfo.addAllBuildings(battleBuildings);
//        }
//        battleInfo.addAllSkills(skills);
//        // 道具技能
//        items.forEach((k, v) -> {
//            ItemTemplate itemCfg = ResHolder.getResService(ItemResService.class).getItemTemplate(k);
//            if (itemCfg.getEffectType() == CommonEnum.ItemUseType.SUPER_WEAPON_VALUE) {
//                battleInfo.putItemSkills(k, v.getCount());
//            }
//        });
//        // 敌人的单位
//        List<RtsBattle.RTSUnit> battleEnemyUnits = Lists.newArrayList();
//        for (int i = 0; i < enemyInfo.enemyUnits.size(); i++) {
//            int id = battleEnemyUnits.size() + 1;
//            int unitId = enemyInfo.enemyUnits.get(i);
//            RtsBattle.RTSUnit enemyUnit = RTSBattleHelper.initEnemyUnit(id, unitId, 1, enemyInfo.unitHpBonus, enemyInfo.unitAttackBonus).build();
//            battleEnemyUnits.add(enemyUnit);
//        }
//        battleInfo.addAllEnemyUnits(battleEnemyUnits);
//        // 敌人的建筑
//        List<RtsBattle.RTSBuilding> battleBuildingUnits = Lists.newArrayList();
//        for (int i = 0; i < enemyInfo.enemyArchitecture.size(); i++) {
//            int unitId = enemyInfo.enemyArchitecture.get(i).getKey();
//            int num = enemyInfo.enemyArchitecture.get(i).getValue();
//            for (int n = 0; n < num; n++) {
//                int id = battleEnemyUnits.size() + 1;
//                RtsBattle.RTSBuilding enemyBuilding = RTSBattleHelper.initEnemyBuilding(id, unitId, 1, enemyInfo.architectureHpBonus, enemyInfo.architectureAttackBonus).build();
//                battleBuildingUnits.add(enemyBuilding);
//            }
//        }
//        battleInfo.addAllEnemyBuildings(battleBuildingUnits);
//
//        return battleInfo.build();
//    }

    public ByteString getRtsBattleInfo() {
        return campaignModel.getBattleInfo();
    }

    public PlayerCampaign.Player_CampaignMissionFinish_S2C finishMission2(CommonMsg.MissionMapResultInfo result) {
        PlayerCampaign.Player_FinishMission_S2C oldAck = finishMission(result);
        PlayerCampaign.Player_CampaignMissionFinish_S2C.Builder builder = PlayerCampaign.Player_CampaignMissionFinish_S2C.newBuilder();
        builder.setResult(oldAck.getResult());
        builder.mergeBaseRewards(oldAck.getBaseRewards());
        builder.mergeExtraRewards(oldAck.getExtraRewards());

        campaignModel.setBattleInfo(ByteString.EMPTY);
        return builder.build();
    }

    // 撤退
    public void abortCampaign() {
        // 检查战役状态
        checkState(CommonEnum.CampaignState.CS_READY);
        // 结束战役
        startFinishState(CommonEnum.CampaignResult.CR_ABORT);
    }

    // 结束任务关卡
    public PlayerCampaign.Player_FinishMission_S2C finishMission(CommonMsg.MissionMapResultInfo result) {
        // 检查战役状态
        checkState(CommonEnum.CampaignState.CS_MISSION);
        // 记录结果
        LOGGER.info("Campaign mission, result:{}, buildings:{}, consumeItems:{}", result.getResult(), result.getBuildingsMap(), result.getConsumeItemsList());

        PlayerCampaign.Player_FinishMission_S2C.Builder builder = PlayerCampaign.Player_FinishMission_S2C.newBuilder();
        // 失败结算
        if (result.getResult() == 0) {
            // 引导战役不能失败
            if (ResHolder.getConsts(ConstCampaignTemplate.class).getGuideCampaignDifficultyId() == campaignModel.getCampaignLevel()) {
                campaignModel.getBattle().setMissionIndex(0).clearUnits();
                campaignModel.setState(CommonEnum.CampaignState.CS_READY); // 重置状态
            } else {
                // 更新战斗结果
                updateMissionFinishResult(result);
                startFinishState(CommonEnum.CampaignResult.CR_FAIL);
            }
            builder.setResult(result.getResult());
            return builder.build();
        }

        // 更新战斗结果
        updateMissionFinishResult(result);

        CampaignMissionProp mission = campaignModel.getMissionsIndex(campaignModel.getBattle().getMissionIndex());
        // 先设置关卡为完成
        mission.setFinished(true);
        // 更新关卡升级
        updateMissionLevel();

        // 设置事件状态
        if (mission.getEventId() > 0) {
            campaignModel.getEvent().setEventId(mission.getEventId()).setEventTrainUnitId(0);
        }
        // 计算奖励
        CampaignMissionRewardTemplate rewardTemplate = ResHolder.getResService(CampaignMissionRewardResService.class).getMissionReward(campaignModel.getCampaignLevel(), mission.getRewardType(), mission.getRewardLevel());
        // 基础奖励（资源和蓝图）
        AssetPackage.Builder baseBuilder = AssetPackage.builder();
        baseBuilder.plusCurrency(rewardTemplate.getGuaranteedRewardPairList());
        baseBuilder.build().forEachCurrency(t -> {
            for (CurrencyProp currency : campaignModel.getResources()) {
                if (currency.getType() == t.getId()) {
                    currency.setCount(currency.getCount() + t.getAmount());
                    return;
                }
            }
            campaignModel.addResources(t.toCurrencyProp());
        });
        AssetPackage baseRewards = baseBuilder.build();
        // 额外奖励
        List<ItemReward> rewardItems = ResHolder.getResService(ItemResService.class).randomReward(rewardTemplate.getDropPit());
        for (int i = 0; i < rewardTemplate.getLootDropCount(); i++) {
            rewardItems.addAll(ResHolder.getResService(ItemResService.class).randomReward(rewardTemplate.getLootGroupID()));
        }
        AssetPackage.Builder extraBuilder = AssetPackage.builder();
        rewardItems.forEach(t -> extraBuilder.plusItem(t.getItemTemplateId(), t.getCount()));
        AssetPackage extraRewards = extraBuilder.build();
        startMergeState(extraRewards.toItemReward().getDatasList());

        builder.setResult(result.getResult());
        builder.setBaseRewards(baseRewards.toPb());
        builder.setExtraRewards(extraRewards.toPb());
        return builder.build();
    }

    // 合并奖励道具
    public void mergeItems(List<IntPairType> items) {
        // 检查战役状态
        checkState(CommonEnum.CampaignState.CS_MERGE);

        // 整理道具，数量合并
        items = tidyItems(items);
        // 检查道具
        checkItemSize(items, campaignModel.getItemSize());

        if (items.stream().anyMatch(t -> {
            int count = 0;
            if (campaignModel.getItems().containsKey(t.getKey())) {
                count += campaignModel.getItems().get(t.getKey()).getCount();
            }
            if (campaignModel.getRewardItems().containsKey(t.getKey())) {
                count += campaignModel.getRewardItems().get(t.getKey()).getCount();
            }
            return t.getValue() > count;
        })) {
            throw new GeminiException(ErrorCode.CAMPAIGN_NOT_ENOUGH_SPACE);
        }

        // 重置物品
        campaignModel.clearItems();
        items.forEach(t -> campaignModel.putItemsV(new ItemPairProp().setItemTemplateId(t.getKey()).setCount(t.getValue())));
        // 清除合并的道具
        campaignModel.clearRewardItems();
        // 开始事件
        startEventState();
    }

    /**
     * 战役状态
     */
    // 开始作战
    private void startReadyState() {
        // 设置关卡难度
        int missionLevel = campaignModel.getMissionLevel() + 1;
        // 检查是否还有未完成的任务关卡
        if (missionLevel > campaignModel.getMissions().size()) {
            startFinishState(CommonEnum.CampaignResult.CR_FINISH);
            return;
        }
        campaignModel.setMissionLevel(missionLevel);
        campaignModel.setState(CommonEnum.CampaignState.CS_READY);
        LOGGER.info("Campaign state:{}, missionLevel:{}", campaignModel.getState(), campaignModel.getMissionLevel());
    }

    // 开始任务
    private void startMissionState(int missionIndex, List<Integer> unitIds) {
        // 设置战斗关卡索引
        campaignModel.getBattle().clearUnits();
        campaignModel.getBattle().setMissionIndex(missionIndex).getUnits().addAll(unitIds);
        campaignModel.setState(CommonEnum.CampaignState.CS_MISSION); // 战役关卡状态
        LOGGER.info("Campaign state:{}, mission:{}, units:{}", campaignModel.getState(), missionIndex, unitIds);
    }

    // 开始合并
    private void startMergeState(List<Struct.ItemPair> mergeItems) {
        // 没有合并的奖励
        if (mergeItems.isEmpty()) {
            startEventState();
            return;
        }
        ItemResService itemRes = ResHolder.getResService(ItemResService.class);
        int itemCost = campaignModel.getItems().values().stream().mapToInt(t -> itemRes.getItemCost(t.getItemTemplateId()) * t.getCount()).sum();
        int mergeItemCost = mergeItems.stream().mapToInt(t -> itemRes.getItemCost(t.getItemTemplateId()) * t.getCount()).sum();
        // 不超出仓库上限就自动合并
        if (itemCost + mergeItemCost <= ResHolder.getConsts(ConstCampaignTemplate.class).getStartStorage()) {
            for (Struct.ItemPair mergeItem : mergeItems) {
                ItemPairProp itemPair = campaignModel.getItems().computeIfAbsent(mergeItem.getItemTemplateId(), key -> new ItemPairProp().setItemTemplateId(key).setCount(0));
                itemPair.setCount(itemPair.getCount() + mergeItem.getCount());
            }
            startEventState();
            return;
        }
        // 超出仓库上限暂存奖励，设置合并状态，玩家手动合并
        campaignModel.clearRewardItems();
        for (Struct.ItemPair mergeItem : mergeItems) {
            campaignModel.putRewardItemsV(new ItemPairProp().setItemTemplateId(mergeItem.getItemTemplateId()).setCount(mergeItem.getCount()));
        }
        campaignModel.setState(CommonEnum.CampaignState.CS_MERGE);
        LOGGER.info("Campaign state:{}, items:{}, mergeItems:{}", campaignModel.getState(), campaignModel.getItems(), campaignModel.getRewardItems());
    }

    // 开始事件
    private void startEventState() {
        // 如果完成所有关卡，事件不执行，直接结束战役
        if (campaignModel.getMissionLevel() == campaignModel.getMissions().size()) {
            startFinishState(CommonEnum.CampaignResult.CR_FINISH);
            return;
        }
        // 如果没有事件就继续关卡
        if (campaignModel.getEvent().getEventId() == 0) {
            startReadyState();
            return;
        }
        // 事件自动触发
        triggerAutoEvent(campaignModel.getEvent().getEventId());
        // 开始事件状态
        campaignModel.setState(CommonEnum.CampaignState.CS_EVENT);
        LOGGER.info("Campaign state:{}, event:{}", campaignModel.getState(), campaignModel.getEvent());
    }

    // 完成战役状态
    private void startFinishState(CommonEnum.CampaignResult result) {
        // 结算
        finishCampaign(result);
        // 完成引导
        if (!campaignModel.getInfo().getFinishGuide() && ResHolder.getConsts(ConstCampaignTemplate.class).getGuideCampaignDifficultyId() == campaignModel.getCampaignLevel()) {
            campaignModel.getInfo().setFinishGuide(true);
        }
        // 清除战役数据
        campaignModel.setCampaignLevel(0);
        campaignModel.setMissionLevel(0);
        campaignModel.clearMissions();
        campaignModel.clearBuildings();
        campaignModel.clearUnits();
        campaignModel.clearSkills();
        campaignModel.setItemSize(0);
        campaignModel.clearItems();
        campaignModel.clearRewardItems();
        campaignModel.clearAdditions();
        campaignModel.getEvent().setEventId(0).setEventTrainUnitId(0);
        campaignModel.getBattle().setMissionIndex(0).clearUnits();
        campaignModel.clearResources();
        campaignModel.clearBuffs();
        // 设置战役状态
        campaignModel.setState(CommonEnum.CampaignState.CS_NONE);
        LOGGER.info("Campaign finished, result:{}", result);
        // 刷新战役信息
        campaignModel.getInfo().setLevel(0).clearMaps();
        updateCampaign();
    }

    /**
     * 事件
     */
    // 不用交互的事件自动触发效果
    private void triggerAutoEvent(int eventId) {
        switch (eventId) {
            case CommonEnum.CampaignEventType.CET_EVENT_HARD_VALUE:
                autoEventHard(); // 固守自动触发
                break;
            case CommonEnum.CampaignEventType.CET_EVENT_STRONG_VALUE:
                autoEventStrong(); // 突击自动触发
                break;
            default:
                break;
        }
    }

    // 检查事件类型
    public void checkEventType(CommonEnum.CampaignEventType eventType) {
        if (campaignModel.getEvent().getEventId() != eventType.getNumber()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    public boolean isEventType(CommonEnum.CampaignEventType eventType) {
        return campaignModel.getEvent().getEventId() == eventType.getNumber();
    }

    // 休整事件：使1个作战单位恢复1层疲劳
    public void eventRest(int unitId) {
        checkState(CommonEnum.CampaignState.CS_EVENT);
        checkEventType(CommonEnum.CampaignEventType.CET_EVENT_REST);
        CampaignUnitProp unit = getMissionUnit(unitId);
        // 恢复疲劳
        if (unit.getFatigued() > 0) {
            unit.setFatigued(unit.getFatigued() - 1);
        }
        LOGGER.info("Campaign event:{}, unit:{}", campaignModel.getEvent().getEventId(), unit);
        clearEvent(); // 清除事件
        startReadyState(); // 继续任务
    }

    // 酒吧事件：指定一个单位，恢复其1层疲劳，但有50%概率“喝断片：下一场战斗无法上场”或“撒酒疯：永久降低1星”
    public CommonEnum.CampaignEventBarEffectType eventBar(int unitId) {
        checkState(CommonEnum.CampaignState.CS_EVENT);
        checkEventType(CommonEnum.CampaignEventType.CET_EVENT_BAR);
        CampaignUnitProp unit = getMissionUnit(unitId);
        // 恢复疲劳
        if (unit.getFatigued() > 0) {
            unit.setFatigued(unit.getFatigued() - 1);
        }
        // 特殊效果触发概率
        int rate = ResHolder.getTemplate(CampaignEventTemplate.class, CommonEnum.CampaignEventType.CET_EVENT_BAR.getNumber()).getParameterList().getFirst();
        List<CommonEnum.CampaignEventBarEffectType> effectTypeList = Lists.newArrayList(CommonEnum.CampaignEventBarEffectType.CEBET_DISABLE, CommonEnum.CampaignEventBarEffectType.CEBET_DOWNGRADE);
        CommonEnum.CampaignEventBarEffectType effectType = RandomUtils.isSuccessByPercentage(rate) ? RandomUtils.randomList(effectTypeList) : CommonEnum.CampaignEventBarEffectType.CEBET_NONE;
        switch (effectType) {
            case CEBET_DISABLE:
                unit.setDisable(true);
                break;
            case CEBET_DOWNGRADE:
                int level = unit.getStar();
                if (level > 0) {
                    unit.setStar(level - 1);
                }
                break;
            default:
                break;
        }
        LOGGER.info("Campaign event:{}, unit:{}, effect:{}", campaignModel.getEvent().getEventId(), unit, effectType);
        clearEvent(); // 清除事件
        startReadyState(); // 继续任务
        return effectType;
    }

    // 集训事件：指定一个单位A（师傅），再选择单位B（徒弟）为其进行特训，每次集训不论是否成功，单位B都无法再继续参加后续任务。得到结果如下。B星级与A星级差值影响成功率。
    public boolean eventTrain(int unitId, int consumeUnitId) {
        checkState(CommonEnum.CampaignState.CS_EVENT);
        checkEventType(CommonEnum.CampaignEventType.CET_EVENT_TRAIN);
        // 单位不能训练自己
        if (unitId == consumeUnitId) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CampaignUnitProp unit = getMissionUnit(unitId);
        CampaignUnitProp consumeUnit = getMissionUnit(consumeUnitId);

        if (campaignModel.getEvent().getEventTrainUnitId() == 0) {
            campaignModel.getEvent().setEventTrainUnitId(unitId);
        } else if (campaignModel.getEvent().getEventTrainUnitId() != unitId) {
            throw new GeminiException(ErrorCode.CAMPAIGN_CANNOT_CHANGE_UNIT);
        }
        // 消耗的单位设为无效
        consumeUnit.setInvalid(true);
        int newLevel = unit.getStar() + 1;
        // 检查新等级
        ResHolder.getResService(CampaignUnitLevelResService.class).checkUnitLevel(unit.getUnitId(), newLevel);
        final List<Integer> rates = ResHolder.getConsts(ConstCampaignTemplate.class).getEventStarUp();
        int grade = consumeUnit.getStar() - unit.getStar();
        boolean success = grade >= 0 && grade < rates.size() && RandomUtils.isSuccessByPercentage(rates.get(grade));
        if (success) {
            unit.setStar(newLevel);
        }
        LOGGER.info("Campaign event:{}, unit:{}, consumeUnit:{}, success:{}", campaignModel.getEvent().getEventId(), unit, consumeUnit, success);
        return success; // 训练事件可以重复，直到客户端退出
    }

    // 恢复事件：所有建筑恢复10%
    public void eventRestore() {
        checkState(CommonEnum.CampaignState.CS_EVENT);
        checkEventType(CommonEnum.CampaignEventType.CET_EVENT_RESTORE);
        int hpPercentage = ResHolder.getTemplate(CampaignEventTemplate.class, CommonEnum.CampaignEventType.CET_EVENT_RESTORE.getNumber()).getParameterList().getFirst();
        for (CampaignBuildingProp building : campaignModel.getBuildings().values()) {
            if (building.getHp() == building.getHpMax()) {
                continue;
            }
            int newHp = building.getHp() + (building.getHpMax() * hpPercentage / 100);
            building.setHp(Math.min(newHp, building.getHpMax()));
        }
        LOGGER.info("Campaign event:{}, buildings:{}", campaignModel.getEvent().getEventId(), campaignModel.getBuildings());
        clearEvent(); // 清除事件
        startReadyState(); // 继续任务
    }

    // 修理事件：指定一个受伤建筑恢复50%耐久，但50%概率随机1个电厂失效，持续1场战斗
    public int eventRepair(int buildingId) {
        checkState(CommonEnum.CampaignState.CS_EVENT);
        checkEventType(CommonEnum.CampaignEventType.CET_EVENT_REPAIR);
        List<Integer> params = ResHolder.getTemplate(CampaignEventTemplate.class, CommonEnum.CampaignEventType.CET_EVENT_REPAIR.getNumber()).getParameterList();
        CampaignBuildingProp building = campaignModel.getBuildingsV(buildingId);
        if (building == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (building.getHp() < building.getHpMax()) {
            int hpPercentage = params.getFirst();
            int newHp = building.getHp() + (building.getHpMax() * hpPercentage / 100);
            building.setHp(Math.min(newHp, building.getHpMax()));
        }
        int effectRate = params.get(1);
        int unitPowerPlantId = ResHolder.getTemplate(InnerBuildRhTemplate.class, CommonEnum.CityBuildType.CBT_POWER_PLANT_VALUE).getUnitsConfigId();
        List<CampaignBuildingProp> buildingPowerPlans = campaignModel.getBuildings().values().stream().filter(t -> t.getUnitId() == unitPowerPlantId && t.getHp() > 0).collect(Collectors.toList());
        CampaignBuildingProp buildingPowerPlan = !buildingPowerPlans.isEmpty() && RandomUtils.isSuccessByPercentage(effectRate) ? RandomUtils.randomList(buildingPowerPlans) : null;
        if (buildingPowerPlan != null) {
            buildingPowerPlan.setDisable(true);
        }

        LOGGER.info("Campaign event:{}, building:{}, invalidBuilding:{}", campaignModel.getEvent().getEventId(), building, buildingPowerPlan);
        clearEvent(); // 清除事件
        startReadyState(); // 继续任务
        return buildingPowerPlan != null ? buildingPowerPlan.getId() : 0;
    }

    // 固守事件：在1次战斗中，所有建筑不会掉血，仅下一次战斗生效
    private void autoEventHard() {
        campaignModel.addBuffs(CommonEnum.CampaignBuff.CB_BUILDING_HARD_VALUE);
        // 不自动跳过事件，需要客户端点退出
    }

    // 突击事件：在1次战斗中，所有作战单位都以3星出战，仅下一次战斗生效
    private void autoEventStrong() {
        campaignModel.addBuffs(CommonEnum.CampaignBuff.CB_UNIT_STRONG_VALUE);
        // 不自动跳过事件，需要客户端点退出
    }

    // 退出事件
    public void eventExit() {
        checkState(CommonEnum.CampaignState.CS_EVENT);
        clearEvent(); // 清除事件
        startReadyState(); // 继续任务
    }

    // 清除事件
    private void clearEvent() {
        clearEventTrain(); // 清除集训事件
        campaignModel.getEvent().setEventId(CommonEnum.CampaignEventType.CET_NONE_VALUE);
    }

    // 清除集训事件数据
    private void clearEventTrain() {
        if (!isEventType(CommonEnum.CampaignEventType.CET_EVENT_TRAIN)) {
            return;
        }
        if (campaignModel.getEvent().getEventTrainUnitId() == 0) {
            return;
        }
        campaignModel.getEvent().setEventTrainUnitId(0);
    }

    /**
     * 战役基本信息刷新
     */
    // 刷新地图
    private void refreshMaps() {
        CampaignDifficultyLevelTemplate template;
        if (campaignModel.getInfo().getFinishGuide()) {
            int constructionPoint = getOwner().getProp().getPlayerInnerBuildRHModel().getConstructionPoint();
            template = ResHolder.getResService(CampaignDifficultyLevelResService.class).getMaxLevelByConstruction(constructionPoint);
        } else {
            int guideLevel = ResHolder.getConsts(ConstCampaignTemplate.class).getGuideCampaignDifficultyId();
            template = ResHolder.getTemplate(CampaignDifficultyLevelTemplate.class, guideLevel);
        }
        // 生成地图Id
        List<Integer> mapIdList = getInitMapIdList(template.getMapIDList(), template.getGeneratedMapCount());
        campaignModel.getInfo().setLevel(template.getId());
        campaignModel.getInfo().getMaps().clear();
        campaignModel.getInfo().getMaps().addAll(mapIdList);
        LOGGER.info("Campaign refresh, level:{}, mapCount:{}, maps:{}", template.getId(), template.getGeneratedMapCount(), campaignModel.getInfo().getMaps());
    }

    // 获取随机战役信息地图
    private List<Integer> getInitMapIdList(List<Integer> sourceMapIdList, int count) {
        List<Integer> mapIdList = Lists.newArrayList();
        // 检查地图数量
        if (sourceMapIdList.isEmpty() || count == 0) {
            return mapIdList;
        }
        // step1: Source重复填入
        while (count - mapIdList.size() >= sourceMapIdList.size()) {
            mapIdList.addAll(sourceMapIdList);
        }
        // step2: Source随机填剩余的数量
        int needMapCount = count - mapIdList.size();
        if (needMapCount > 0) {
            mapIdList.addAll(RandomUtils.randomList(sourceMapIdList, needMapCount));
        }
        // step3: 打乱顺序
        Collections.shuffle(mapIdList);
        return mapIdList;
    }

    /**
     * 战役初始化信息
     */
    // 获取初始的建筑
    private Map<Integer, CampaignBuildingProp> getInitBuildings() {
        Map<Integer, CampaignBuildingProp> buildings = Maps.newHashMap();
        for (int buildingId : ResHolder.getConsts(ConstCampaignTemplate.class).getDefaultBuildingPermanent()) {
            int unitId = ResHolder.getTemplate(InnerBuildRhTemplate.class, buildingId).getUnitsConfigId();
            int hp = ResHolder.getResService(TroopResService.class).getUnitsLevelTemplate(unitId, 1).getHp();
            int num = getOwner().getInnerBuildRhComponent().getInnerBuildNum(buildingId, true);
            // 永久建筑血量增加
            if (ResHolder.getTemplate(InnerBuildRhTemplate.class, buildingId).getBuildTag() == 1) {
                hp += (int) getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_BUILDING_HEALTH_FIXED);
            }
            // 发电厂血量增加
            if (buildingId == CommonEnum.CityBuildType.CBT_POWER_PLANT_VALUE) {
                hp += (int) getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_POWER_PLANT_HEALTH_FIXED);
            }
            for (int i = 0; i < num; i++) {
                int id = buildings.size() + 1;
                CampaignBuildingProp building = new CampaignBuildingProp().setId(id).setUnitId(unitId).setHp(hp).setHpMax(hp);
                buildings.put(id, building);
            }
        }
        return buildings;
    }

    // 获取初始的单位
    private Map<Integer, CampaignUnitProp> getInitUnits() {
        Map<Integer, CampaignUnitProp> units = Maps.newHashMap();
        for (int unitId : getOwner().getProp().getPlayerInnerBuildRHModel().getUnits()) {
            CampaignUnitProp unit = new CampaignUnitProp().setUnitId(unitId).setStar(0).setFatigued(0).setDisable(false).setInvalid(false);
            units.put(unitId, unit);
        }
        return units;
    }

    // 获取初始的技能
    private List<Integer> getInitSkills() {
        return getOwner().getProp().getPlayerInnerBuildRHModel().getSkills();
    }

    // 获取初始的属性
    public Map<Integer, CampaignAdditionProp> getInitAdditions() {
        Map<Integer, CampaignAdditionProp> additions = Maps.newHashMap();
        for (CommonEnum.BuffEffectType additionId : PlayerCampaignComponent.campaignAdditionIds) {
            long value = getOwner().getAddComponent().getAddition(additionId);
            if (value == 0) {
                continue;
            }
            CampaignAdditionProp addition = new CampaignAdditionProp().setAdditionId(additionId.getNumber()).setValue(value);
            additions.put(additionId.getNumber(), addition);
        }
        return additions;
    }

    /**
     * 战役任务生成
     */
    // 获取任务关卡列表
    private List<CampaignMissionProp> getRandomMissionList(int level, int campaignMapId) {
        // 获取地图列表
        List<Integer> mapIdList = getRandomMissionMap(level);
        List<CampaignMissionProp> missions = mapIdList.stream().map(t -> new CampaignMissionProp().setMapId(t).setFinished(false)).collect(Collectors.toList());
        // mission.setRewardType(0).setRewardLevel(1).setRewardId(0).setEventId(0);
        // 获得奖励类型
        List<Integer> rewardTypeList = getRandomRewardTypeList(campaignMapId, mapIdList.size());
        CampaignDifficultyLevelTemplate template = ResHolder.getTemplate(CampaignDifficultyLevelTemplate.class, level);
        for (int i = 0; i < mapIdList.size(); i++) {
            int rewardType = rewardTypeList.get(i);
            int rewardId = ResHolder.getResService(CampaignMissionRewardResService.class).getMissionReward(level, rewardType, 1).getId();
            missions.get(i).setRewardType(rewardType).setRewardLevel(1).setRewardId(rewardId);
        }
        // 生成事件
        List<CampaignMissionProp> eventMissions = missions.stream().filter(t -> RandomUtils.isSuccessByPercentage(template.getEventGenerationChance())).toList();
        eventMissions.forEach(t -> t.setEventId(getRandomEventId()));
        // 保底事件数量
        int minEventCount = Math.min(missions.size(), template.getMinEventGeneration());
        // 处理事件的保底
        if (eventMissions.size() < minEventCount) {
            List<CampaignMissionProp> extraEventMissions = missions.stream().filter(t -> t.getEventId() == 0).collect(Collectors.toList());
            int extraEventCount = minEventCount - eventMissions.size(); // 生成的事件数量
            RandomUtils.randomList(extraEventMissions, extraEventCount).forEach(t -> t.setEventId(getRandomEventId()));
        }
        return missions;
    }

    // 获取随机任务关卡地图
    private List<Integer> getRandomMissionMap(int level) {
        CampaignDifficultyLevelTemplate template = ResHolder.getTemplate(CampaignDifficultyLevelTemplate.class, level);
        // step1: 先添加保底
        List<Integer> mapTypeList = Lists.newArrayList();
        if (!template.getMapTypeGuaranteeList().isEmpty()) {
            if (template.getGeneratedMissionCount() < template.getMapTypeGuaranteeList().size()) {
                mapTypeList.addAll(template.getMapTypeGuaranteeList().subList(0, template.getGeneratedMissionCount()));
            } else {
                mapTypeList.addAll(template.getMapTypeGuaranteeList());
            }
        }
        // step2: 按权重随机
        if (!template.getMapTypeWeightingPairList().isEmpty()) {
            while (mapTypeList.size() < template.getGeneratedMissionCount()) {
                mapTypeList.add(RandomUtils.randomByWeight(template.getMapTypeWeightingPairList(), IntPairType::getValue).getKey());
            }
        }
        // step3: 生成地图Id
        Map<Integer, Integer> mapTypeCount = mapTypeList.stream().collect(Collectors.toMap(t -> t, p -> 1, Integer::sum));
        // Map<Integer, Integer> mapTypeCount = mapTypeList.stream().collect(Collectors.groupingBy(t -> t, Collectors.summingInt(p -> 1)));
        List<Integer> mapIdList = mapTypeCount.entrySet().stream().flatMap(t -> getRandomMapList(template.getMapLevelList(), t.getKey(), t.getValue()).stream()).collect(Collectors.toList());
        // 打乱顺序
        Collections.shuffle(mapIdList);
        return mapIdList;
    }

    // 生成给定数量随机地图Id
    private List<Integer> getRandomMapList(List<Integer> levelList, int mapType, int count) {
        List<Integer> mapIdList = Lists.newArrayList();
        if (count == 0) {
            return mapIdList;
        }
        List<Integer> sourceMapIdList = ResHolder.getResService(MapConfigResService.class).getMapList(mapType, levelList).stream().map(MapConfigRhTemplate::getId).collect(Collectors.toList());
        if (sourceMapIdList.isEmpty()) {
            return mapIdList;
        }
        while (count - mapIdList.size() >= sourceMapIdList.size()) {
            mapIdList.addAll(sourceMapIdList);
        }
        int needMapCount = count - mapIdList.size();
        if (needMapCount > 0) {
            mapIdList.addAll(RandomUtils.randomList(sourceMapIdList, needMapCount));
        }
        return mapIdList;
    }

    // 获取奖励类型列表
    private List<Integer> getRandomRewardTypeList(int campaignMapId, int count) {
        List<Integer> rewardTypeList = Lists.newArrayList();
        CampaignMapTemplate template = ResHolder.getTemplate(CampaignMapTemplate.class, campaignMapId);
        // step1: 保底关卡奖励类型按配置顺序
        if (!template.getMinimumGuaranteeList().isEmpty()) {
            if (count < template.getMinimumGuaranteeList().size()) {
                rewardTypeList.addAll(template.getMinimumGuaranteeList().subList(0, count));
            } else {
                rewardTypeList.addAll(template.getMinimumGuaranteeList());
            }
        }
        // step2: 超出保底数量的关卡按权重生成奖励类型
        if (!template.getMissionRewardTypeWeightingPairList().isEmpty()) {
            while (rewardTypeList.size() < count) {
                rewardTypeList.add(RandomUtils.randomByWeight(template.getMissionRewardTypeWeightingPairList(), IntPairType::getValue).getKey());
            }
        }
        // step3: 打乱顺序
        Collections.shuffle(rewardTypeList);
        return rewardTypeList;
    }

    // 获取随机的事件
    private int getRandomEventId() {
        List<Integer> unlockEventIdList = campaignModel.getInfo().getEvents();
        List<Integer> eventTypeWeights = ResHolder.getConsts(ConstCampaignTemplate.class).getEventTypeWeight();
        // step1: 先按type的权重随机
        List<Integer> typeList = unlockEventIdList.stream().map(t -> ResHolder.getTemplate(CampaignEventTemplate.class, t).getType()).distinct().collect(Collectors.toList());
        int eventType = RandomUtils.randomByWeight(typeList, t -> eventTypeWeights.get(t - 1));
        // step2: 从对应type的event权重随机
        List<Integer> eventIdList = unlockEventIdList.stream().filter(t -> ResHolder.getTemplate(CampaignEventTemplate.class, t).getType() == eventType).collect(Collectors.toList());
        return RandomUtils.randomByWeight(eventIdList, t -> ResHolder.getTemplate(CampaignEventTemplate.class, t).getEventWeight());
    }

    /**
     * 战役检查
     */
    private void checkState(CommonEnum.CampaignState state) {
        if (campaignModel.getState() != state) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    // 检查物品占用
    private void checkItemSize(List<IntPairType> items, int itemSize) {
        ItemResService itemRes = ResHolder.getResService(ItemResService.class);
        int itemCost = items.stream().mapToInt(t -> itemRes.getItemCost(t.getKey()) * t.getValue()).sum();
        if (itemCost > itemSize) {
            throw new GeminiException(ErrorCode.CAMPAIGN_NOT_ENOUGH_SPACE);
        }
    }

    // 检查道具类型
    private void checkItems(List<IntPairType> items) {
        ItemResService itemRes = ResHolder.getResService(ItemResService.class);
        for (IntPairType item : items) {
            if (itemRes.getItemTemplate(item.getKey()).getEffectType() == CommonEnum.ItemUseType.SUPER_WEAPON_VALUE) {
                continue;
            }
            throw new GeminiException(ErrorCode.CAMPAIGN_SUPER_WEAPONS_ONLY);
        }
    }

    private void checkMissionSkills(List<Integer> skills) {
        if (!campaignModel.getSkills().containsAll(skills)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    // 检查出战数量
    private void checkMissionUnitSize(int num) {
        if (num == 0 || num > getMissionUnitSize()) {
            throw new GeminiException(ErrorCode.CAMPAIGN_SELECT_UNITS_LIMIT);
        }
    }

    /**
     * 获取关卡数据
     */
    private CampaignMissionProp getMission(int missionIndex) {
        if (missionIndex < 0 || missionIndex >= campaignModel.getMissionsSize()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return campaignModel.getMissionsIndex(missionIndex);
    }

    // 获取单位
    private CampaignUnitProp getMissionUnit(int unitId) {
        CampaignUnitProp unit = campaignModel.getUnitsV(unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (unit.getDisable() || unit.getInvalid()) {
            throw new GeminiException(ErrorCode.CAMPAIGN_UNIT_DISABLED);
        }
        return unit;
    }

    public List<CampaignUnitProp> getMissionUnits(List<Integer> units) {
        return units.stream().map(this::getMissionUnit).collect(Collectors.toList());
    }

    // 获得属性值
    private long getAddition(CommonEnum.BuffEffectType effectType) {
        CampaignAdditionProp addition = campaignModel.getAdditionsV(effectType.getNumber());
        return addition == null ? 0 : addition.getValue();
    }

    // 获得关卡单位数量限制
    private int getMissionUnitSize() {
        return ResHolder.getConsts(ConstCampaignTemplate.class).getBattleCost() + (int) getAddition(CommonEnum.BuffEffectType.ET_MISSION_UNIT_SIZE);
    }

    private List<IntPairType> tidyItems(List<IntPairType> itemList) {
        return itemList.stream().collect(Collectors.groupingBy(IntPairType::getKey, Collectors.summingInt(IntPairType::getValue))).
                entrySet().stream().map(t -> IntPairType.makePair(t.getKey(), t.getValue())).collect(Collectors.toList());
    }

    /**
     * 更新战斗结果
     */
    private void updateMissionFinishResult(CommonMsg.MissionMapResultInfo result) {
        // 更新建筑血量
        result.getBuildingsMap().forEach((id, hp) -> {
            CampaignBuildingProp building = campaignModel.getBuildings().get(id);
            if (building != null) {
                building.setHp(hp);
            }
        });
        // 更新道具（扣除消耗的道具）
        for (StructPB.ItemPairPB consumeItem : result.getConsumeItemsList()) {
            ItemPairProp item = campaignModel.getItemsV(consumeItem.getItemTemplateId());
            if (item == null) {
                continue;
            }
            int newCount = item.getCount() - consumeItem.getCount();
            if (newCount > 0) {
                item.setCount(newCount);
            } else {
                campaignModel.removeItemsV(item.getItemTemplateId());
            }
        }
    }

    /**
     * 更新关卡进度
     */
    private void updateMissionLevel() {
        // 清除建筑回合禁用状态
        campaignModel.getBuildings().values().stream().filter(CampaignBuildingProp::getDisable).forEach(t -> t.setDisable(false));
        // 清除单位回合禁用状态
        campaignModel.getUnits().values().stream().filter(CampaignUnitProp::getDisable).forEach(t -> t.setDisable(false));
        // 清除回合buff
        campaignModel.clearBuffs();

        // 更新单位升星和疲劳
        List<CampaignUnitProp> units = campaignModel.getBattle().getUnits().stream().map(t -> campaignModel.getUnitsV(t)).collect(Collectors.toList());
        updateMissionUnits(units);
        // 清除关卡战斗状态
        campaignModel.getBattle().setMissionIndex(0);
        campaignModel.getBattle().clearUnits();
        // 更新任务奖励状态
        updateMissionReward();
    }

    // 更新关卡单位
    private void updateMissionUnits(List<CampaignUnitProp> units) {
        if (units.isEmpty()) {
            return;
        }
        // 任务关卡难度
        CampaignMissionLevelTemplate template = ResHolder.getResService(CampaignMissionLevelResService.class).getMissionLevel(campaignModel.getCampaignLevel(), campaignModel.getMissionLevel());
        // 计算疲劳数量
        int fatigueNum = units.stream().mapToInt(t -> RandomUtils.isSuccessByPercentage(template.getFatigueChance()) ? 1 : 0).sum();
        fatigueNum = Math.min(fatigueNum, template.getMaxFatigueGains());
        // 计算疲劳
        int fatigueMaxLevel = ResHolder.getConsts(ConstCampaignTemplate.class).getFatiguEeffectLeve().size() - 1;
        List<CampaignUnitProp> fatigueUnits = RandomUtils.randomList(units, fatigueNum).stream().filter(t -> t.getFatigued() < fatigueMaxLevel).toList();
        fatigueUnits.forEach(t -> t.setFatigued(t.getFatigued() + 1));
        // 计算升星
        int unitMaxStar = ResHolder.getConsts(ConstCampaignTemplate.class).getUnitMaxStar();
        List<CampaignUnitProp> upgradeUnits = units.stream().filter(t -> t.getStar() < unitMaxStar && RandomUtils.isSuccessByPercentage(template.getRankUpChance())).toList();
        upgradeUnits.forEach(t -> t.setStar(t.getStar() + 1));
    }

    // 更新关卡奖励
    private void updateMissionReward() {
        List<CampaignMissionProp> missions = campaignModel.getMissions().stream().filter(t -> !t.getFinished()).toList();
        for (CampaignMissionProp mission : missions) {
            int newRewardLevel = mission.getRewardLevel() + 1;
            if (!ResHolder.getResService(CampaignMissionRewardResService.class).randomUpgradeReward(campaignModel.getCampaignLevel(), mission.getRewardType(), newRewardLevel)) {
                continue;
            }
            int rewardId = ResHolder.getResService(CampaignMissionRewardResService.class).getMissionReward(campaignModel.getCampaignLevel(), mission.getRewardType(), newRewardLevel).getId();
            mission.setRewardLevel(newRewardLevel);
            mission.setRewardId(rewardId);
        }
    }

    /**
     * 战役结算
     */
    // 结束战役
    private void finishCampaign(CommonEnum.CampaignResult result) {
        // 获取最终物品列表（去除了战役临时物品）
        List<IntPairType> items = getFinalItemList();
        List<IntPairType> lossItems = getLossItemList(items, result);
        // 扣除损失的物品
        items = items.stream().map(t -> {
            for (IntPairType lossItem : lossItems) {
                if (t.getKey() == lossItem.getKey()) {
                    return IntPairType.makePair(t.getKey(), t.getValue() - lossItem.getValue());
                }
            }
            return t;
        }).filter(t -> t.getValue() > 0).collect(Collectors.toList());

        // 基础奖励（资源和蓝图）
        AssetPackage.Builder baseBuilder = AssetPackage.builder();
        campaignModel.getResources().forEach(t -> baseBuilder.plusCurrency(t.getType(), t.getCount()));
        AssetPackage baseRewards = baseBuilder.build();
        // 额外奖励（扣除损失）
        AssetPackage extraRewards = AssetPackage.builder().plusItems(items).build();
        // 损失列表
        AssetPackage lossRewards = AssetPackage.builder().plusItems(lossItems).build();
        // 奖励进入背包
        AssetPackage rewards = AssetPackage.builder().plus(baseRewards).plus(extraRewards).build();
        getOwner().getAssetComponent().give(rewards, CommonEnum.Reason.ICR_CAMPAIGN_FINISH);
        // 通知客户端显示结果
        notifyClientResult(result, baseRewards.toPb(), extraRewards.toPb(), lossRewards.toPb());
    }

    // 获取战役最终物品（删除战役临时物品）
    private List<IntPairType> getFinalItemList() {
        ItemResService itemResService = ResHolder.getResService(ItemResService.class);
        List<IntPairType> items = campaignModel.getItems().values().stream().map(t -> IntPairType.makePair(t.getItemTemplateId(), t.getCount())).toList();
        return items.stream().filter(t -> itemResService.getItemTemplate(t.getKey()).getEffectType() != CommonEnum.ItemUseType.CAMPAIGN_ITEM_VALUE).collect(Collectors.toList());
    }

    // 获取损失的物品列表
    private List<IntPairType> getLossItemList(List<IntPairType> items, CommonEnum.CampaignResult result) {
        ItemResService itemResService = ResHolder.getResService(ItemResService.class);
        switch (result) {
            case CR_ABORT:
                CampaignMissionLevelTemplate template = ResHolder.getResService(CampaignMissionLevelResService.class).getMissionLevel(campaignModel.getCampaignLevel(), campaignModel.getMissionLevel());
                // 奖励列表（去除了超级武器）
                List<IntPairType> rewardItems = items.stream().filter(t -> itemResService.getItemTemplate(t.getKey()).getEffectType() != CommonEnum.ItemUseType.SUPER_WEAPON_VALUE).toList();
                // 生成损失的物品
                List<Integer> itemList = Lists.newArrayList();
                for (IntPairType itemPair : rewardItems) {
                    for (int i = 0; i < itemPair.getValue(); i++) {
                        itemList.add(itemPair.getKey());
                    }
                }
                if (itemList.size() <= template.getRetreatLossGuarantee()) {
                    return Lists.newArrayList();
                }
                // 生成损失的数量
                int lossCount = RandomUtils.nextInt(template.getRetreatLossPair().getKey(), template.getRetreatLossPair().getValue() + 1);
                // 减去保底取最小
                lossCount = Math.min(itemList.size() - template.getRetreatLossGuarantee(), lossCount);

                Map<Integer, Integer> lossItems = RandomUtils.randomList(itemList, lossCount).stream().collect(Collectors.groupingBy(t -> t, Collectors.summingInt(p -> 1)));
                // 撤退损失部分奖励
                return lossItems.entrySet().stream().map(t -> IntPairType.makePair(t.getKey(), t.getValue())).collect(Collectors.toList());
            case CR_FAIL: // 失败损失全部奖励
                // 除了超级武器全部损失
                return items.stream().filter(t -> itemResService.getItemTemplate(t.getKey()).getEffectType() != CommonEnum.ItemUseType.SUPER_WEAPON_VALUE).collect(Collectors.toList());
            default:
                // 完成战役不损失奖励
                return Lists.newArrayList();
        }
    }

    // 通知客户端显示结果
    private void notifyClientResult(CommonEnum.CampaignResult result, StructPB.YoAssetPackagePB baseRewards, StructPB.YoAssetPackagePB extraRewards, StructPB.YoAssetPackagePB lossRewards) {
        PlayerCampaign.FinalCampaignResultNtf.Builder ntfBuilder = PlayerCampaign.FinalCampaignResultNtf.newBuilder();
        ntfBuilder.setResult(result);
        ntfBuilder.setBaseRewards(baseRewards);
        ntfBuilder.setExtraRewards(extraRewards);
        ntfBuilder.setLossRewards(lossRewards);
        getOwner().sendMsgToClient(MsgType.PLAYER_FINALCAMPAIGNRESULT_NTF, ntfBuilder.build());
    }

    /**
     * GM
     */
    public void gmSetMissionEvent(int missionIndex, int eventId) {
        // 检查战役状态
        checkState(CommonEnum.CampaignState.CS_READY);
        // 检查事件Id
        ResHolder.getTemplate(CampaignEventTemplate.class, eventId);
        // 获取设置关卡事件Id
        CampaignMissionProp mission = getMission(missionIndex);
        mission.setEventId(eventId);
    }
}

class RTSBattleEnemyInfo {
    List<Integer> enemyUnits = new ArrayList<>();
    List<IntPairType> enemyArchitecture = new ArrayList<>();
    int unitHpBonus = 0;
    int unitAttackBonus = 0;
    int architectureHpBonus = 0;
    int architectureAttackBonus = 0;

    public void copyFromCampaignMissionLevel(CampaignMissionLevelTemplate template) {
        this.enemyUnits.addAll(template.getEnemyUnitList());
        this.enemyArchitecture.addAll(template.getEnemyArchitecturePairList());
        this.unitHpBonus = template.getUnitHpBonus();
        this.unitAttackBonus = template.getUnitAttackBonus();
        this.architectureHpBonus = template.getArchitectureHpBonus();
        this.architectureAttackBonus = template.getArchitectureAttackBonus();
    }
}
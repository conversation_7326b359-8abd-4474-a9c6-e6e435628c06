package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.OccupyCityEvent;
import com.yorha.cnc.player.event.task.OccupyStrongHoldEvent;
import com.yorha.common.actor.PlayerSceneService;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.SsPlayerScene;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TerritoryBuildingTemplate;

public class PlayerSceneServiceImpl implements PlayerSceneService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSceneServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerSceneServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }


    @Override
    public void handleOnOccupySuccessCmd(SsPlayerScene.OnOccupySuccessCmd ask) {
        if (!ask.hasPartId()) {
            LOGGER.warn("handleOnOccupySuccessCmd ask has no partId, ask={}", ask);
            return;
        }
        if (!ask.hasTemplateId()) {
            LOGGER.warn("handleOnOccupySuccessCmd ask has no templateId, ask={}", ask);
            return;
        }
        if (!ask.hasAreaType()) {
            LOGGER.warn("handleOnOccupySuccessCmd ask has no areaType, ask={}", ask);
            return;
        }
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        TerritoryBuildingTemplate template = ResHolder.getInstance().findValueFromMap(TerritoryBuildingTemplate.class, ask.getTemplateId());
        if (template == null) {
            LOGGER.warn("handleOnOccupySuccessCmd ask has no template, ask={}", ask);
            return;
        }
        if (GameLogicConstants.isMapCity(ask.getAreaType())) {
            // 城市占领事件
            new OccupyCityEvent(playerEntity, template.getLevel()).dispatch();
        }
        if (GameLogicConstants.isMapStronghold(ask.getAreaType())) {
            // 据点占领事件
            new OccupyStrongHoldEvent(playerEntity, template.getLevel()).dispatch();
        }
    }

    @Override
    public void handleComsumeAssetAsk(SsPlayerScene.ComsumeAssetAsk ask) {
        AssetPackage.Builder builder = AssetPackage.builder();
        for (Struct.YoAssetDesc desc : ask.getPackage().getAssets().getDatasList()) {
            if (desc.getType() == 1) {
                builder.plusItem(desc.getId(), desc.getAmount());
                continue;
            }
            builder.plusCurrency(desc.getId(), desc.getAmount());
        }
        final PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        final ErrorCode errorCode = playerEntity.getAssetComponent().verify(builder.build());
        if (errorCode.isNotOk()) {
            playerActor.answer(SsPlayerScene.ComsumeAssetAns.newBuilder().setSuccess(false).build());
            return;
        }
        playerEntity.getAssetComponent().consume(builder.build(), ask.getReason());
        playerActor.answer(SsPlayerScene.ComsumeAssetAns.newBuilder().setSuccess(true).build());
    }
    
}

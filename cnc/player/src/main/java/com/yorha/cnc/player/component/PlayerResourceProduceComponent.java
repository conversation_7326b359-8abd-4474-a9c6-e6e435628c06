package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.enums.BuildResourceMapType;
import com.yorha.cnc.player.event.PlayerNewbieOverEvent;
import com.yorha.cnc.player.event.task.InnerResourceOutputRateChangeEvent;
import com.yorha.cnc.player.event.task.PlayerInnerCollectResourceEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.PlayerResourceProduceMapProp;
import com.yorha.game.gen.prop.ResourceProduceProp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.statistic.StatisticEnum.INNER_COLLECT_ANY_RESOURCE_NUM;
import static com.yorha.common.enums.statistic.StatisticEnum.INNER_COLLECT_RESOURCE_NUM;
import static com.yorha.proto.CommonEnum.*;

/**
 * 资源产出组件
 *
 * <AUTHOR>
 */
public class PlayerResourceProduceComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerResourceProduceComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerNewbieOverEvent.class, PlayerResourceProduceComponent::onNewbieOver);
    }

    public PlayerResourceProduceComponent(PlayerEntity owner) {
        super(owner);
    }

    public Map<CurrencyType, Long> handleReceiveResource(List<Integer> buildTypeList) {
        // 1、验证参数
        if (CollectionUtils.isEmpty(buildTypeList)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
        }

        // 2、验证建筑存在并找到建筑对应资源
        Set<BuildResourceMapType> resourceReduceSet = new HashSet<>();
        for (Integer buildTypeId : buildTypeList) {
            CityBuildType buildType = CityBuildType.forNumber(buildTypeId);
            if (buildType == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
            BuildResourceMapType type = BuildResourceMapType.getBuildResourceMapTypeWithBuildType(buildType);
            if (type == null) {
                throw new GeminiException(ErrorCode.RESOURCE_BUILD_ILLEGAL_RESOURCE_BUILDING_TYPES);
            }
            resourceReduceSet.add(type);
        }

        // 3、检查玩家资源数据是否可以收取，并统计
        Set<BuildResourceMapType> canReceiveResourceSet = new HashSet<>();
        for (BuildResourceMapType type : resourceReduceSet) {
            if (checkReceiveWithResource(type)) {
                canReceiveResourceSet.add(type);
            }
        }

        // 防止误差，这里统一时间
        long now = SystemClock.now();

        // 4、计算资源产出
        refreshProduceWithTypeSet(now, canReceiveResourceSet);

        // 收集资源产出数据
        Map<CurrencyType, Long> produceMap = Maps.newHashMap();

        // 新手/常规 统计资源产出
        /*
        if (getOwner().getNewbieComponent().isNewbie()) {
            // 新手期间不产资源，除了初始给的石油产出
            ConstNewbieSvrTemplate constNewbie = ResHolder.getConsts(ConstNewbieSvrTemplate.class);
            final int firstProduceCurrencyType = constNewbie.getFirstProduceCurrencyType();
            boolean containsNewbieResource = canReceiveResourceSet.stream().anyMatch(it -> it.getResourceType().getNumber() == firstProduceCurrencyType);
            if (containsNewbieResource && !getProp().getNewbieProduceTaken()) {
                getProp().setNewbieProduceTaken(true);
                produceMap.put(CurrencyType.forNumber(firstProduceCurrencyType), (long) constNewbie.getFirstProduceCurrencyNum());
            }
        } else {
        */
        for (BuildResourceMapType type : canReceiveResourceSet) {
            ResourceProduceProp rpp = getOrInitProduceProp(now, type);
            if (rpp.getResourceMargin() > 0) {
                // 清空资源产出，下一步就是给奖励了
                produceMap.put(type.getResourceType(), rpp.getResourceMargin());
                rpp.setLastClientReceiveMs(now);
                rpp.setResourceMargin(0);
            }
            // 计算资源田产满的时间戳
            refreshProduceFullTsMs(now, type);
        }
        //}

        if (MapUtils.isNotEmpty(produceMap)) {
            // 5、添加资源
            for (Map.Entry<CurrencyType, Long> entry : produceMap.entrySet()) {
                getOwner().getPurseComponent().give(entry.getKey(), entry.getValue(), Reason.ICR_RES_COLLECT, "RES_PRODUCE");
                // 更新统计项
                getOwner().getStatisticComponent().recordSecondStatistic(INNER_COLLECT_RESOURCE_NUM, entry.getKey().getNumber(), entry.getValue().intValue());
                getOwner().getStatisticComponent().recordSingleStatistic(INNER_COLLECT_ANY_RESOURCE_NUM, entry.getValue().intValue());
            }
            new PlayerInnerCollectResourceEvent(getOwner(), produceMap).dispatch();
        }
        return produceMap;
    }

    /**
     * 检测资源是否可以收取
     */
    public boolean checkReceiveWithResource(BuildResourceMapType type) {
        int resourceType = type.getResourceType().getNumber();
        ResourceProduceProp resourceProduceProp = getProp().getProduceMapV(resourceType);

        if (resourceProduceProp == null) {
            // 无资源产量
            //throw new GeminiException(ErrorCode.RESOURCE_BUILD_NO_RESOURCE_PRODUCTION.getCodeId());
            return false;
        }
        if (resourceProduceProp.getResourceId() != resourceType) {
            // 资源类错误
            //throw new GeminiException(ErrorCode.RESOURCE_BUILD_NO_RESOURCE_PRODUCTION.getCodeId());
            return false;
        }
        return true;
    }

    private PlayerResourceProduceMapProp getProp() {
        return getOwner().getProp().getResourceProduce();
    }

    /**
     * 更新玩家资源产率，来自加成系统事件
     */
    public static void onProduceRateAdditionChange(PlayerEntity player, int additionId, long oldValue) {
        long now = SystemClock.now();
        for (BuildResourceMapType buildResourceMapType : BuildResourceMapType.values()) {
            if (buildResourceMapType.isProduceAttentionBuff(additionId)) {
                player.getResourceProduceComponent().refreshProduceWithType(now, buildResourceMapType);
            }
        }
    }

    /**
     * 更新资源产出属性，例如资源产率提高时需要更新资源何时产满的时间戳
     */
    public static void onNeedRefreshProduceProp(PlayerEntity player, int additionId, long newValue) {
        long now = SystemClock.now();
        for (BuildResourceMapType buildResourceMapType : BuildResourceMapType.values()) {
            if (buildResourceMapType.isProduceAttentionBuff(additionId)) {
                player.getResourceProduceComponent().refreshProduceFullTsMs(now, buildResourceMapType);
            }
        }
        new InnerResourceOutputRateChangeEvent(player).dispatch();
    }

    private void refreshProduceWithTypeSet(long now, Set<BuildResourceMapType> canReceiveResourceSet) {
        for (BuildResourceMapType type : canReceiveResourceSet) {
            refreshProduceWithType(now, type);
        }
    }

    /**
     * 更新并获取玩家指定资源产量
     */
    private void refreshProduceWithType(long now, BuildResourceMapType resourceMapType) {
        ResourceProduceProp orInitProduceProp = getOrInitProduceProp(now, resourceMapType);

        //final boolean isNewbie = getOwner().getNewbieComponent().isNewbie();

        // 计算产出
        long totalValue = 0;
        long margeValue = orInitProduceProp.getResourceMargin();

        //if (!isNewbie) {
            // 新手期间不产资源
            // 计算时间
            long totalMs = now - orInitProduceProp.getLastReceiveMs();
            totalValue = getProduceTotalValue(totalMs, resourceMapType, false);
            long remainCap = getProduceMaxCap(resourceMapType) - orInitProduceProp.getResourceMargin();
            if (remainCap < totalValue) {
                margeValue = getProduceMaxCap(resourceMapType);
            } else {
                margeValue += totalValue;
            }
        //}
        if (totalValue < 0) {
            LOGGER.error("calcProduce fail, prop:{}, now:{}", orInitProduceProp, now);
            return;
        }
        // 无产量情况下
        if (totalValue == 0 && margeValue == 0) {
            orInitProduceProp.setLastClientReceiveMs(now);
        }
        // 服务器触发的更新需要暂存产出
        orInitProduceProp
                .setLastReceiveMs(now)
                .setResourceMargin(margeValue);
    }

    /**
     * 更新资源田何时产满的时间戳
     */
    public void refreshProduceFullTsMs(long now, BuildResourceMapType resourceMapType) {
        ResourceProduceProp orInitProduceProp = getOrInitProduceProp(now, resourceMapType);

        //final boolean isNewbie = getOwner().getNewbieComponent().isNewbie();
        //if (!isNewbie) {
            long remainCap = getProduceMaxCap(resourceMapType) - orInitProduceProp.getResourceMargin();
            if (remainCap > 0) {
                double produceRatePerHour = PlayerAddCalc.getProduceRatePerHour(getOwner(), resourceMapType, true);
                // 避免分母为0
                if (Math.abs(produceRatePerHour) <= 1e-6) {
                    orInitProduceProp.setReachLimitMs(Long.MAX_VALUE);
                } else {
                    long reachLimitMs = (long) (TimeUnit.HOURS.toMillis(remainCap) / produceRatePerHour);
                    orInitProduceProp.setReachLimitMs(now + reachLimitMs);
                }
            } else {
                orInitProduceProp.setReachLimitMs(now);
            }
        //}
    }

    public long getProduceTotalValue(long totalMs, BuildResourceMapType resourceMapType, boolean isSkillEffect) {
        long totalValue;
        long produceMaxCap = isSkillEffect ? Integer.MAX_VALUE : getProduceMaxCap(resourceMapType);
        double produceRatePerHour = PlayerAddCalc.getProduceRatePerHour(getOwner(), resourceMapType, true);
        totalValue = calcProduce(resourceMapType, produceMaxCap, produceRatePerHour, totalMs);
        return totalValue;
    }

    /**
     * 计算当前产量
     *
     * @param maxCap 建筑最大容量
     * @return 产量
     */
    public static long calcProduce(BuildResourceMapType type, long maxCap, double produceRatePerHour, long totalTimeMs) {
        if (type == null) {
            return 0;
        }
        // 资源产量 = 时间 * 产率/Ms
        double resourceNum = TimeUnit.MILLISECONDS.toHours((long) (totalTimeMs * produceRatePerHour));
        double realResourceNum = Math.min(maxCap, resourceNum);
        long totalProduce = (long) realResourceNum;
        LOGGER.info("calcProduce, totalTimeMs:{}, produceRatePerHour:{}, maxCap:{}, realResourceNum:{}, totalProduce:{}",
                totalTimeMs, produceRatePerHour, maxCap, realResourceNum, totalProduce);
        return totalProduce;
    }

    /**
     * 获取建筑最大容量
     */
    public long getProduceMaxCap(BuildResourceMapType type) {
        return getOwner().getAddComponent().getAddition(type.getCapType());
    }

    private ResourceProduceProp getOrInitProduceProp(long now, BuildResourceMapType buildResourceType) {
        int resourceType = buildResourceType.getResourceType().getNumber();
        ResourceProduceProp produceMapV = getProp().getProduceMapV(resourceType);
        if (produceMapV == null) {
            LOGGER.info("produceProp init, resourceType:{}", buildResourceType);
            ResourceProduceProp produceProp = getProp().addEmptyProduceMap(resourceType);
            produceProp.setLastReceiveMs(now).setLastClientReceiveMs(now);
            return produceProp;
        }
        return produceMapV;
    }

    private static void onNewbieOver(PlayerNewbieOverEvent event) {
        event.getPlayer().getResourceProduceComponent().onNewbieOverStartProduce();
    }

    private void onNewbieOverStartProduce() {
        // 新手结束了，可以开始正常产出资源了
        final long now = SystemClock.now();
        for (ResourceProduceProp rp : getProp().getProduceMap().values()) {
            // 从now开始计算资源产出
            rp.setLastReceiveMs(now);
            rp.setLastClientReceiveMs(now);
        }
    }
}

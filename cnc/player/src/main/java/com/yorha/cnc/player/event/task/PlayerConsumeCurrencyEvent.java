package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.CommonEnum;

public class PlayerConsumeCurrencyEvent extends PlayerTaskEvent {
    private final CommonEnum.CurrencyType currencyType;
    private final long consumeCount;

    public PlayerConsumeCurrencyEvent(PlayerEntity entity, CommonEnum.CurrencyType currencyType, long consumeCount) {
        super(entity);
        this.currencyType = currencyType;
        this.consumeCount = consumeCount;
    }

    public CommonEnum.CurrencyType getCurrencyType() {
        return currencyType;
    }

    public long getConsumeCount() {
        return consumeCount;
    }
}

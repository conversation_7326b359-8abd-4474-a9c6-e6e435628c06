package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

/**
 * <AUTHOR>
 * <p>
 * 货币道具
 */
public class CurrencyUsableItem extends AbstractUsableItem {

    private static final Logger LOGGER = LogManager.getLogger(CurrencyUsableItem.class);

    private CommonEnum.CurrencyType currencyType;
    private long count;

    public CurrencyUsableItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        if (template == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(template.getEffectId());
        if (currencyType == null) {
            throw new GeminiException(ErrorCode.CURRENCY_NOT_EXIST);
        }

        this.currencyType = currencyType;
        this.count = ((long) template.getEffectValue()) * num;
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        long add = playerEntity.getPurseComponent().give(currencyType, count, CommonEnum.Reason.ICR_ITEM_USE, "");

        LOGGER.info("player use currency item {} add count {}", this, add);
        return add > 0;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        StructPB.CurrencyListPB.Builder currencyList = StructPB.CurrencyListPB.newBuilder();
        StructPB.CurrencyPB.Builder currency = StructPB.CurrencyPB.newBuilder();
        currency.setType(currencyType.getNumber());
        currency.setCount(count);
        currencyList.addDatas(currency);

        PlayerPB.ItemUseResultPB.Builder resultPb = PlayerPB.ItemUseResultPB.newBuilder();
        resultPb.setCurrency(currencyList);
        response.setResult(resultPb);
    }
}

package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.PlayerNewbieStepEvent;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.resource.resservice.innermap.InnerMapService;
import com.yorha.common.resource.resservice.newInnerBuild.NewInnerBuildTemplateService;
import com.yorha.common.utils.Assertions;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.BuildStateRH;
import com.yorha.proto.CommonEnum.CityBuildType;
import com.yorha.proto.StructPB.PointPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.*;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.statistic.StatisticEnum.DEFEND_FINISH;

public class PlayerInnerBuildRhComponent extends PlayerComponent implements AdditionProviderInterface, PlayerQueueTaskComponent.QueueTaskInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerInnerBuildRhComponent.class);

    private PlayerInnerBuildRHModelProp innerBuildModel;

    private int innerMaxId = 0;

    private ActorTimer nextWaveTimer;

    public PlayerInnerBuildRhComponent(PlayerEntity owner) {
        super(owner);
    }

    static {
        EntityEventHandlerHolder.register(PlayerNewbieStepEvent.class, PlayerInnerBuildRhComponent::monitorNewbieStepEvent);
        EntityEventHandlerHolder.register(BranchTaskFinishEvent.class, PlayerInnerBuildRhComponent::monitorUnlockBuildingByTask);
        EntityEventHandlerHolder.register(MissionCompleteEvent.class, PlayerInnerBuildRhComponent::monitorUnlockBuildingByMission);
    }

    @Override
    public void init() {
        innerBuildModel = getOwner().getProp().getPlayerInnerBuildRHModel();
    }

    private Int32InnerBuildRHMapProp getInnerBuildRHProp() {
        return innerBuildModel.getInnerBuildRH();
    }

    public int getBaseExpandId() {
        return innerBuildModel.getBaseExpandId();
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            // 解锁升级队列
            getOwner().getPlayerQueueTaskComponent().unLockQueue(true, CommonEnum.QueueTaskType.CITY_BUILD_UPGRADE, 0);
        }
    }

    @Override
    public void postLoad(boolean isRegister) {
        getInnerBuildMaxId();
        checkBaseData();
        if (isRegister) {
            checkInitAllBuild();
        }
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        this.cancelNextWaveTimer();
        int baseLevel = getInnerBuildLevel(CityBuildType.CBT_FACT_VALUE);
        int delay = ResHolder.getResService(NewInnerBuildTemplateService.class).getNextWaveTimeMinute(baseLevel);
        ownerActor().addTimer(
                TimerReasonType.NEXT_DEFEND_CAMPAIGN,
                this::checkDefendCampaign,
                delay,
                TimeUnit.MINUTES);
    }

    private void cancelNextWaveTimer() {
        if (nextWaveTimer != null) {
            nextWaveTimer.cancel();
            nextWaveTimer = null;
        }
    }


    private void getInnerBuildMaxId() {
        for (InnerBuildRHProp innerBuildRHProp : getInnerBuildRHProp().values()) {
            innerMaxId = Math.max(this.innerMaxId, innerBuildRHProp.getBuildId());
        }
    }

    private int innerBuildRhId() {
        return ++innerMaxId;
    }

    public BaseAreaProp getBaseAreaProp(int areaId) {
        return innerBuildModel.getAreas().get(areaId);
    }

    /**
     * 初始化内城已解锁的建筑
     */
    private void checkInitAllBuild() {
        //解锁默认区域
        var defaultAreaId = ResHolder.getResService(NewInnerBuildTemplateService.class).getDefaultAreaId();
        if (defaultAreaId > 0) {
            addNewBaseArea(defaultAreaId).setState(CommonEnum.BaseAreaStateRH.BaseAreaStateRH_UNLOCK);
        }

        //矿点建筑未解锁的需要设置为解锁，但无法放置的状态，以给客户端展示矿田
        for (InnerBuildRhTemplate template : ResHolder.getInstance().getListFromMap(InnerBuildRhTemplate.class)) {
            if (isFixedCoordinateBuild(template)) {
                int curNum = getInnerBuildNum(template.getId(), false);
                int maxNum = template.getNumMax();
                int unlockNum = maxNum - curNum;
                if (unlockNum > 0) {
                    unlockInnerMine(template.getId(), unlockNum);
                }
            }
        }
        //解锁建筑
        for (InnerBuildRhTemplate template : ResHolder.getResService(NewInnerBuildTemplateService.class).getAllBuildInitTemplate()) {
            int unlockNum = template.getNewbieUnlockNum();
            if (unlockNum == 0) {
                continue;
            }
            if (getInnerBuildNum(template.getId(), false) >= unlockNum) {
                continue;
            }
            unLockInnerBuildRh(template.getId(), unlockNum);
        }
        //对于部分建筑需要在初始化时放到地图上
        for (IntTripleType buildPos : ResHolder.getConsts(ConstInnermapRhTemplate.class).getInitialInnerBuild()) {
            List<InnerBuildRHProp> builds = innerBuildModel.getInnerBuildRH().values().stream().filter(it -> it.getBuildType() == buildPos.getKey()).toList();
            if (!builds.isEmpty()) {
                InnerBuildRHProp build = builds.getFirst();
                PointPB.Builder builder = PointPB.newBuilder();
                builder.setX(buildPos.getValue1()).setY(buildPos.getValue2());
                productNewInnerBuild(build.getBuildId(), builder.build());
            }
        }

    }

    /**
     * GM使用建筑解锁
     */
    public void unLockInnerBuildRhGM(int innerBuildRhType, int num, PointPB factPoint, PointPB offsetPoint) {
        unLockInnerBuildRh(innerBuildRhType, num);
        if (factPoint != null && offsetPoint != null) {
            List<InnerBuildRHProp> unlockBuilds = innerBuildModel.getInnerBuildRH().values().stream().filter(
                    b -> b.getBuildType() == innerBuildRhType && b.getBuildStateRH() == BuildStateRH.BuildStateRH_UN_LOCK
            ).toList();
            if (!unlockBuilds.isEmpty()) {
                InnerBuildRHProp prop = unlockBuilds.getFirst();
                PointPB pos = PointPB.newBuilder().setX(factPoint.getX() + offsetPoint.getX()).setY(factPoint.getY() + offsetPoint.getY()).build();
                productNewInnerBuild(prop.getBuildId(), pos);
            }
        }
    }

    /**
     * GM使用预览信息
     */
    public void buildInfoByViewId(int id) {
        innerBuildModel.setViewId(id);
    }

    /**
     * 建造建筑
     */
    public void productNewInnerBuild(int buildId, PointPB point) {
        Assertions.require(buildId > 0, ErrorCode.PARAM_PARAMETER_EXCEPTION, "buildid or point is null");

        if (buildId < 0 || point == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "buildid or point is null");
        }

        InnerBuildRHProp build = getInnerBuildRHProp().get(buildId);
        if (build == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE);
        }
        InnerBuildRhTemplate buildTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildTemplate(build.getBuildType());
        if (buildTemplate == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE);
        }

        if (build.getBuildStateRH() != BuildStateRH.BuildStateRH_UN_LOCK) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "build has been placed");
        }
        //0级以上走升级流程
        if (build.getLevel() != 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "level is over 0, call levelup please");
        }
        //这里不要抛异常，以免打断初始化其他建筑的建造
        BuildUpgradeRhTemplate upgradeTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(build.getBuildType(), 1);
        if (upgradeTemplate == null) {
            return;
        }

        //阻挡点检测
        if (hasBlockInsidePosition(buildTemplate, point)) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_POSITION_OVERLAP.getCodeId());
        }

        //非固定坐标建筑只能在内城范围内建造
        if (!isFixedCoordinateBuild(buildTemplate) && !isPositionInsideCity(buildTemplate, point)) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_POSITION_OVERLAP.getCodeId());
        }

        //坐标是否被其他建筑占用
        if (!isBuildModelOccupied(buildTemplate, point, buildId)) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_POSITION_OVERLAP.getCodeId());
        }

        int beforeLv = getInnerBuildLevel(build.getBuildType());
        //创建
        build.setBuildStateRH(BuildStateRH.BuildStateRH_BUILD)
                .getPoint().setX(point.getX()).setY(point.getY());

        //0->1为立即完成
        finishUpgrade(build.getBuildId());

        new BuildRhBuildingEvent(getOwner(), build.getBuildType(), getPlacedBuildingNum(build.getBuildType())).dispatch();
        new PlayerInnerBuildCreateEvent(getOwner(), build.getBuildType(), beforeLv, getInnerBuildLevel(build.getBuildType())).dispatch();
    }

    /**
     * 建造矿点
     */
    private void productNewInnerMine(int buildId) {
        InnerBuildRHProp build = getInnerBuildRHProp().get(buildId);
        if (build == null) {
            return;
        }
        //创建
        build.setBuildStateRH(BuildStateRH.BuildStateRH_BUILD);
        //0->1为立即完成
        finishUpgrade(build.getBuildId());
    }

    /**
     * 移动建筑
     */
    public void moveNewInnerBuild(int buildId, PointPB point) {
        if (buildId < 0 || point == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "buildid or position is null");
        }

        InnerBuildRHProp build = getInnerBuildRHProp().get(buildId);
        if (build == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE);
        }
        //放下来的建筑才可以移动
        if (build.getBuildStateRH() != BuildStateRH.BuildStateRH_BUILD) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "building should be placed first");
        }

        var rewInnerBuildTemplateService = ResHolder.getResService(NewInnerBuildTemplateService.class);
        InnerBuildRhTemplate buildTemplate = rewInnerBuildTemplateService.getBuildTemplate(build.getBuildType());
        if (buildTemplate == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE);
        }
        //固定坐标建筑不可移动
        if (isFixedCoordinateBuild(buildTemplate)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "resource building can not be moved");
        }

        //阻挡点检测
        if (hasBlockInsidePosition(buildTemplate, point)) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_POSITION_OVERLAP.getCodeId());
        }

        //坐标是否被其他建筑占用
        if (!isBuildModelOccupied(buildTemplate, point, buildId)) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_POSITION_OVERLAP.getCodeId());
        }

        build.getPoint().setX(point.getX()).setY(point.getY());
    }

    public int getPlacedBuildingNum(int buildType) {
        return (int) getInnerBuildRHProp().values().stream().filter(
                it -> it.getBuildType() == buildType && it.getBuildStateRH() == BuildStateRH.BuildStateRH_BUILD
        ).count();
    }

    /**
     * 升级建筑
     */
    public void upgradeNewInnerBuild(int buildId) {
        if (buildId < 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "buildid is invalid");
        }

        InnerBuildRHProp build = getInnerBuildRHProp().get(buildId);
        if (build == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE);
        }
        //放下来的建筑才可以升级
        if (build.getBuildStateRH() != BuildStateRH.BuildStateRH_BUILD) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "product it first");
        }
        //升级中
        if (build.getInBuilding()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "in upgrading");
        }
        //0级走建造流程
        if (build.getLevel() == 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "only product building");
        }
        //获取升级数据 取下一级的数据
        int curLv = build.getLevel();
        BuildUpgradeRhTemplate template = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(build.getBuildType(), curLv);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "next level is missing");
        }

        //解锁条件
        List<IntPairType> conditions = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeCondition(build.getBuildType(), curLv);
        if (conditions.stream().anyMatch(it -> !isSatisfyCond(it))) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unlock condition fail");
        }

        //道具检测
        AssetPackage cost = AssetPackage.builder()
                .plusCurrency(template.getCostResourcesPairList())
                .build();
        getOwner().verifyThrow(cost);
        //检测是否有空闲队列
        long costTsMs = TimeUnit.SECONDS.toMillis(template.getCostTime());
        final long taskFinTsMs = costTsMs + SystemClock.now();
        getOwner().getPlayerQueueTaskComponent().queueFreeCheck(buildId, CommonEnum.QueueTaskType.CITY_BUILD_UPGRADE, taskFinTsMs);

        // 资源消耗
        getOwner().consume(cost, CommonEnum.Reason.ICR_CITY_BUILD);
        // 添加任务队列
        getOwner().getPlayerQueueTaskComponent().addQueueTask(CommonEnum.QueueTaskType.CITY_BUILD_UPGRADE, buildId, costTsMs, cost, StructPlayerPB.QueueExtraParamPB.getDefaultInstance());

        //设置建造中标志
        build.setInBuilding(true);
    }

    private boolean isSatisfyCond(IntPairType unlockCond) {
        switch (unlockCond.getKey()) {
            case CommonEnum.BuildConditionType.PRE_BUILD_VALUE: //建筑等级需求
                BuildUpgradeRhTemplate preTemplate = ResHolder.getTemplate(BuildUpgradeRhTemplate.class, unlockCond.getValue());
                if (preTemplate != null) {
                    int lv = getInnerBuildLevel(preTemplate.getBuildId());
                    if (lv < preTemplate.getBuildLevel()) {
                        return false;
                    }
                }
                break;
            case CommonEnum.BuildConditionType.PRE_TASK_VALUE: //前置任务需求
                if (!getOwner().getTaskComponent().isMainTaskFinish(unlockCond.getValue())) {
                    return false;
                }
                break;
            case CommonEnum.BuildConditionType.PRE_MISSION_VALUE: //前置关卡需求
                if (!getOwner().getMissionComponent().isMissionCompleted(unlockCond.getValue())) {
                    return false;
                }
                break;
            case CommonEnum.BuildConditionType.PRE_NONE_VALUE: //占位用需求，表示满足条件
                break;
            default:
                return false;
        }
        return true;
    }

    private void finishUpgrade(int buildId) {
        InnerBuildRHProp build = getInnerBuildRHProp().get(buildId);
        if (build == null) {
            return;
        }
        BuildUpgradeRhTemplate curTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(build.getBuildType(), build.getLevel());
        if (curTemplate == null) {
            return;
        }
        int beforeLv = getInnerBuildLevel(build.getBuildType());
        int nextlv = build.getLevel() + 1;
        //获取升级数据
        BuildUpgradeRhTemplate template = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(build.getBuildType(), nextlv);
        if (template == null) {
            return;
        }

        //移除建造中标志
        build.setInBuilding(false);
        //升级
        build.setLevel(nextlv);
        //设置加成效果
        List<Integer> ids = Lists.newArrayList();
        for (IntPairType intPairType : template.getBuffEffectPairList()) {
            CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(intPairType.getKey());
            if (buffEffectType != null) {
                ids.add(intPairType.getKey());
            }
        }
        // 更新加成
        getOwner().getAddComponent().updateAddition(AdditionProviderType.INNER_BUILDING_RH, ids);
        //尝试解锁建筑
        checkUnlockBuildingByBuilding(build.getBuildType(), build.getLevel());
        //尝试触发防守战
        //只在主堡升级时，触发防守战，或者初始化时
        if (build.getBuildType() == CityBuildType.CBT_FACT_VALUE) {
            this.checkDefendCampaign();
        }
        //尝试解锁编队
        checkUnlockTroop(build.getBuildType(), build.getLevel());

        //>1级说明是升级
        if (nextlv > 1) {
            new PlayerInnerBuildLevelUpEvent(getOwner(), build.getBuildType(), 1, beforeLv, getInnerBuildLevel(build.getBuildType())).dispatch();
        }
        new BuildFinEvent(getOwner(), CommonEnum.CityBuildType.forNumber(build.getBuildType()), nextlv).dispatch();
    }

    public void checkUnlockBuildingByBuilding(int buildRhType, int level) {
        checkUnlockBuilding(ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUnlockBuildings(buildRhType, level));
    }

    public static void monitorUnlockBuildingByTask(BranchTaskFinishEvent event) {
        event.getPlayer().getInnerBuildRhComponent().checkUnlockBuilding(ResHolder.getResService(NewInnerBuildTemplateService.class).getTaskUnlockBuildings(event.getTaskId()));
    }

    public static void monitorUnlockBuildingByMission(MissionCompleteEvent event) {
        PlayerEntity player = event.getPlayer();
        player.getInnerBuildRhComponent().checkUnlockBuilding(ResHolder.getResService(NewInnerBuildTemplateService.class).getMissionUnlockBuildings(event.getMissionId()));
        player.getInnerBuildRhComponent().checkExpandBase();
    }

    private void checkUnlockBuilding(List<IntPairType> unlocks) {
        if (unlocks == null) {
            return;
        }
        for (IntPairType id : unlocks) {
            //检测已拥有建筑数量
            int curNum = getUnlockBuildNum(id.getKey());
            if (curNum >= id.getValue()) {
                continue;
            }
            for (int i = curNum + 1; i <= id.getValue(); i++) {
                List<IntPairType> conditions = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildNumUnlockCondition(id.getKey(), i);
                if (conditions == null) {
                    break;
                }
                boolean satisy = true;
                for (IntPairType cond : conditions) {
                    if (!isSatisfyCond(cond)) {
                        satisy = false;
                        break;
                    }
                }
                if (satisy) {
                    unLockInnerBuildRh(id.getKey(), 1);
                } else {
                    break;
                }
            }
        }
    }

    public void checkExpandBase() {
        // 通知只允许有一个区域可解锁
        var hasLockArea = innerBuildModel.getAreas().values().stream().anyMatch(
                it -> it.getState() == CommonEnum.BaseAreaStateRH.BaseAreaStateRH_LOCK
        );
        if (hasLockArea) {
            return;
        }

        // 根据BaseExpandRhTemplate.getUnlockOrder()排序获得一个新的集合
        var templates = ResHolder.getInstance().getListFromMap(BaseExpandRhTemplate.class).stream().sorted(
                Comparator.comparingInt(BaseExpandRhTemplate::getUnlockOrder)
        ).toList();
        for (BaseExpandRhTemplate template : templates) {
            if (template.getUnlockMission() == 0) {
                continue;
            }
            if (getBaseAreaProp(template.getId()) == null && getOwner().getMissionComponent().isMissionCompleted(template.getUnlockMission())) {
                addNewBaseArea(template.getId());
                //触发任务
                new BaseExpandEvent(getOwner(), template.getId()).dispatch();
                break;
            }
        }
    }

    private void checkDefendCampaign() {
        PlayerDefendInfoProp defendInfo = innerBuildModel.getDefend();
        int baseLevel = getInnerBuildLevel(CityBuildType.CBT_FACT_VALUE);
        int campaignId = ResHolder.getResService(NewInnerBuildTemplateService.class).randomDefendCampaign(baseLevel);
        if (campaignId == 0) {
            return;
        }
        //已经有战役了,放到下一场中去
        if (defendInfo.getCampaignId() > 0) {
            defendInfo.setNextCampaignId(campaignId);
            return;
        }
        CampaignTdTemplate template = ResHolder.getTemplate(CampaignTdTemplate.class, campaignId);
        if (template == null) {
            return;
        }
        long now = SystemClock.now();
        defendInfo.setCampaignId(campaignId).setBaseLevel(baseLevel).setStartTsMs(now);
    }

    private void checkUnlockTroop(int buildRhType, int level) {
        BuildUpgradeRhTemplate template = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(buildRhType, level);
        if (template == null) {
            return;
        }
        for (IntPairType pair : template.getSquadUnlockPairList()) {
            getOwner().getTroopFormationComponent().unlockRHTroopSlot(pair.getKey(), pair.getValue());
        }
    }

    @Override
    public void onFinish(long taskId, QueueTaskProp taskProp) {
        int buildId = (int) taskId;
        finishUpgrade(buildId);
    }

    @Override
    public void onCancel(long taskId, QueueTaskProp taskProp) {

    }

    @Override
    public boolean isSpeedUpItemAvailable(CommonEnum.ItemUseType itemUseType) {
        return itemUseType == CommonEnum.ItemUseType.COMMON_SPEED;
    }

    @Override
    public CommonEnum.Reason speedUpUsingReason() {
        return CommonEnum.Reason.ICR_CITY_UPGRADE;
    }

    private BaseAreaProp addNewBaseArea(int areaId) {
        Assertions.check(getBaseAreaProp(areaId) == null, "area already exists, areaId:" + areaId);
        var area = new BaseAreaProp().setAreaId(areaId).setState(CommonEnum.BaseAreaStateRH.BaseAreaStateRH_LOCK);
        innerBuildModel.getAreas().put(areaId, area);
        return area;
    }

    /**
     * 解锁建筑
     */
    private void unLockInnerBuildRh(int innerBuildRhType, int num) {
        if (num <= 0) {
            return;
        }
        if (innerBuildRhType < 0) {
            return;
        }
        InnerBuildRhTemplate buildTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildTemplate(innerBuildRhType);
        if (buildTemplate == null) {
            return;
        }

        var buildTag = buildTemplate.getBuildTag();
        for (int i = 0; i < num; i++) {
            if (buildTag == CommonEnum.InnerBuildTag.IBT_FIX_POSITION_VALUE) {
                if (!unlockFixedPositionBuild(innerBuildRhType)) {
                    break;
                }
            } else if (buildTag == CommonEnum.InnerBuildTag.IBT_RESOURCE_VALUE) {
                if (!unlockResourceBuild(innerBuildRhType)) {
                    break;
                }
            } else {
                unlockGeneralBuild(innerBuildRhType);
            }
        }
    }

    /**
     * 解锁固定位置建筑
     *
     * @param innerBuildRhType 建筑类型
     * @return 是否成功解锁
     */
    private boolean unlockFixedPositionBuild(int innerBuildRhType) {
        //按照策划配置的顺序将建筑解锁，找出当前解锁第几个建筑
        List<InnerBuildRHProp> lockBuildings = innerBuildModel.getInnerBuildRH().values().stream().filter(
                it -> it.getBuildType() == innerBuildRhType && it.getBuildStateRH() == BuildStateRH.BuildStateRH_LOCK
        ).toList();

        var upgradeTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(innerBuildRhType, 0);
        if (upgradeTemplate == null) {
            LOGGER.error("Can't find upgrade template for build:{}", innerBuildRhType);
            return false;
        }
        var unlockIndex = upgradeTemplate.getFixCoordinatePairList().size() - lockBuildings.size();
        if (unlockIndex < 0) {
            LOGGER.error("Can't find unlock pair for build:{}", innerBuildRhType);
            return false;
        }
        var unlockPair = upgradeTemplate.getFixCoordinatePairList().get(unlockIndex);
        var unlockBuild = lockBuildings.stream().filter(
                it -> it.getPoint().getX() == unlockPair.getKey() && it.getPoint().getY() == unlockPair.getValue()
        ).findFirst();

        if (unlockBuild.isEmpty()) {
            LOGGER.error("Can't find unlock build for build:{}", innerBuildRhType);
            return false;
        }
        unlockBuild.get().setBuildStateRH(BuildStateRH.BuildStateRH_UN_LOCK);
        return true;
    }

    /**
     * 解锁资源建筑
     *
     * @param innerBuildRhType 建筑类型
     * @return 是否成功解锁
     */
    private boolean unlockResourceBuild(int innerBuildRhType) {
        List<InnerBuildRHProp> buildings = innerBuildModel.getInnerBuildRH().values().stream().filter(
                it -> it.getBuildType() == innerBuildRhType && it.getBuildStateRH() == BuildStateRH.BuildStateRH_UN_LOCK
        ).toList();
        int buildId = findMinBuildId(buildings);
        if (buildId == 0) {
            LOGGER.error("Can't find unlock build:{}", innerBuildRhType);
            return false;
        }
        productNewInnerMine(buildId);
        return true;
    }

    /**
     * 解锁通用建筑
     *
     * @param innerBuildRhType 建筑类型
     */
    private void unlockGeneralBuild(int innerBuildRhType) {
        int buildId = innerBuildRhId();
        InnerBuildRHProp unLockInnerBuildRHProp = new InnerBuildRHProp();
        unLockInnerBuildRHProp.setBuildId(buildId)
                .setBuildType(innerBuildRhType)
                .setBuildStateRH(BuildStateRH.BuildStateRH_UN_LOCK)
                .setLevel(0);
        LOGGER.info("unLockInnerBuildRh create success,  id:{}, type:{}", buildId, innerBuildRhType);
        getInnerBuildRHProp().put(buildId, unLockInnerBuildRHProp);
    }

    /**
     * 从建筑列表中找到最小的建筑ID
     *
     * @param buildings 建筑列表
     * @return 最小的建筑ID，如果列表为空则返回0
     */
    private int findMinBuildId(List<InnerBuildRHProp> buildings) {
        int buildId = 0;
        for (InnerBuildRHProp prop : buildings) {
            if (buildId == 0 || buildId > prop.getBuildId()) {
                buildId = prop.getBuildId();
            }
        }
        return buildId;
    }

    private boolean isFixedCoordinateBuild(InnerBuildRhTemplate buildTemplate) {
        var buildTag = buildTemplate.getBuildTag();
        return buildTag == CommonEnum.InnerBuildTag.IBT_RESOURCE_VALUE || buildTag == CommonEnum.InnerBuildTag.IBT_FIX_POSITION_VALUE;
    }

    private void unlockInnerMine(int innerBuildRhType, int num) {
        if (num <= 0 || innerBuildRhType < 0) {
            return;
        }

        InnerBuildRhTemplate buildTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildTemplate(innerBuildRhType);
        if (buildTemplate == null) {
            return;
        }
        if (!isFixedCoordinateBuild(buildTemplate)) {
            return;
        }

        for (int i = 0; i < num; i++) {
            //摆放位置
            BuildUpgradeRhTemplate upgradeTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(innerBuildRhType, 0);
            if (upgradeTemplate == null) {
                break;
            }
            int curNum = getInnerBuildNum(innerBuildRhType, false);
            if (upgradeTemplate.getFixCoordinatePairList().size() <= curNum) {
                break;
            }
            IntPairType pair = upgradeTemplate.getFixCoordinatePairList().get(curNum);

            //解锁
            int buildId = innerBuildRhId();
            InnerBuildRHProp unLockInnerBuildRHProp = new InnerBuildRHProp();
            unLockInnerBuildRHProp.setBuildId(buildId)
                    .setBuildType(innerBuildRhType)
                    .setLevel(0)
                    .getPoint().setX(pair.getKey()).setY(pair.getValue());
            if (buildTemplate.getBuildTag() == CommonEnum.InnerBuildTag.IBT_RESOURCE_VALUE) {
                unLockInnerBuildRHProp.setBuildStateRH(BuildStateRH.BuildStateRH_UN_LOCK);
            } else if (buildTemplate.getBuildTag() == CommonEnum.InnerBuildTag.IBT_FIX_POSITION_VALUE) {
                unLockInnerBuildRHProp.setBuildStateRH(BuildStateRH.BuildStateRH_LOCK);
            }
            LOGGER.info("unlockInnerMine create success,  id:{}, type:{}, position:{},{} ", buildId, innerBuildRhType, pair.getKey(), pair.getValue());
            getInnerBuildRHProp().put(buildId, unLockInnerBuildRHProp);
        }
    }

    private boolean hasBlockInsidePosition(@NotNull InnerBuildRhTemplate buildTemplate, PointPB point) {
        IntPairType newModelSize = buildTemplate.getModelSizePair();
        InnerMapService.InnerMapBlockInfo blockInfo = ResHolder.getResService(InnerMapService.class).getInnerMapBlock(innerBuildModel.getBaseId());
        if (blockInfo != null) {
            if (point.getX() < 0 || point.getY() < 0
                    || (point.getX() + newModelSize.getKey() >= blockInfo.size.getKey())
                    || (point.getY() + newModelSize.getValue() >= blockInfo.size.getValue())) {
                return true;
            }

            //计算是否在阻挡位上
            for (int i = point.getX(); i < point.getX() + newModelSize.getKey(); i++) {
                for (int j = point.getY(); j < point.getY() + newModelSize.getValue(); j++) {
                    IntPairType pos = IntPairType.makePair(i, j);
                    if (blockInfo.blocks.contains(pos)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private Set<IntPairType> getAllUnlockedPoints() {
        var unlockedPoints = new HashSet<IntPairType>();
        for (BaseAreaProp area : innerBuildModel.getAreas().values()) {
            if (area.getState() == CommonEnum.BaseAreaStateRH.BaseAreaStateRH_UNLOCK) {
                var areaTemplate = ResHolder.getTemplate(BaseExpandRhTemplate.class, area.getAreaId());
                var point = convertIntPairToPB(areaTemplate.getZoneCoordinatePair());
                unlockedPoints.addAll(getBuildOccupyPoints(point, areaTemplate.getZoneSizePair()));
            }
        }
        return unlockedPoints;
    }

    private Set<IntPairType> getBuildOccupyPoints(PointPB point, IntPairType modelSize) {
        var points = new HashSet<IntPairType>();
        for (int i = point.getX(); i < point.getX() + modelSize.getKey(); i++) {
            for (int j = point.getY(); j < point.getY() + modelSize.getValue(); j++) {
                points.add(IntPairType.makePair(i, j));
            }
        }
        return points;
    }

    private PointPB convertIntPairToPB(IntPairType pair) {
        return PointPB.newBuilder().setX(pair.getKey()).setY(pair.getValue()).build();
    }

    /**
     * 判断[point]是否在已解锁区域，内城已解锁区域是由多个矩形组成，组成后不一定是矩形，
     * <p>
     * <p>
     * 因此只能将已解锁区域转化成点，然后判断新建筑所占的点是否在已解锁区域内
     */
    private boolean isPositionInsideCity(@NotNull InnerBuildRhTemplate buildTemplate, PointPB point) {
        IntPairType modelSize = buildTemplate.getModelSizePair();
        var unlockedPoints = getAllUnlockedPoints();
        var buildPoints = getBuildOccupyPoints(point, modelSize);
        for (var buildPoint : buildPoints) {
            if (!unlockedPoints.contains(buildPoint)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断新的建筑坐标和模型范围是否与已有建筑坐标范围重叠
     *
     * @param buildTemplate 新的建筑模板
     * @param point         新的建筑坐标
     * @param buildId       新的建筑id
     * @return true: 不重叠，false: 重叠
     */
    private boolean isBuildModelOccupied(@NotNull InnerBuildRhTemplate buildTemplate, PointPB point, int buildId) {
        IntPairType newModelSize = buildTemplate.getModelSizePair();
        //第三步：检测其他建筑是否占用位置
        for (InnerBuildRHProp otherBuild : innerBuildModel.getInnerBuildRH().values()) {
            InnerBuildRhTemplate otherTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildTemplate(otherBuild.getBuildType());
            if (otherTemplate == null) {
                continue;
            }
            //自己不要检测重合
            if (buildId > 0 && otherBuild.getBuildId() == buildId) {
                continue;
            }
            IntPairType otherModelSize = otherTemplate.getModelSizePair();
            IntPairType otherPoint = IntPairType.makePair(otherBuild.getPoint().getX(), otherBuild.getPoint().getY());
            //检测新的建筑坐标范围不能与已有建筑坐标范围重叠
            if (ShapeUtils.isRectanglesIntersect(IntPairType.makePair(point.getX(), point.getY()), newModelSize, otherPoint, otherModelSize)) {
                return false;
            }
        }
        return true;
    }

    //获取内城建筑数量 包括仓库中和已放置的 (包括所有建筑 无论是否摆放)
    public int getInnerBuildNum(int innerBuildRhType, boolean onlyBuild) {
        if (onlyBuild) {
            return (int) getInnerBuildRHProp().values().stream().filter(
                    it -> it.getBuildType() == innerBuildRhType && it.getBuildStateRH() == BuildStateRH.BuildStateRH_BUILD
            ).count();
        } else {
            return (int) getInnerBuildRHProp().values().stream().filter(
                    it -> it.getBuildType() == innerBuildRhType
            ).count();
        }
    }

    //获取解锁的建筑数量，包含所有状态不等于BuildStateRH_LOCK的
    private int getUnlockBuildNum(int innerBuildRhType) {
        return (int) getInnerBuildRHProp().values().stream().filter(
                it -> it.getBuildType() == innerBuildRhType && it.getBuildStateRH() != BuildStateRH.BuildStateRH_LOCK
        ).count();
    }

    public int getInnerBuildTypeNum(boolean onlyBuild) {
        Set<Integer> typeNums = new HashSet<>();
        for (var build : getInnerBuildRHProp().values()) {
            if (!onlyBuild || build.getBuildStateRH() == BuildStateRH.BuildStateRH_BUILD) {
                typeNums.add(build.getBuildType());
            }
        }
        return typeNums.size();
    }

    //获取内城建筑的等级，取同类建筑的最高等级 (只计算摆放了的建筑)
    public int getInnerBuildLevel(int innerBuildRhType) {
        List<InnerBuildRHProp> builds = getInnerBuildRHProp().values().stream().filter(
                it -> it.getBuildType() == innerBuildRhType && it.getBuildStateRH() == BuildStateRH.BuildStateRH_BUILD
        ).toList();
        int maxLv = 0;
        for (InnerBuildRHProp build : builds) {
            if (build.getLevel() > maxLv) {
                maxLv = build.getLevel();
            }
        }
        return maxLv;
    }

    //获取内城建筑数量 包括仓库中和已放置的 (只计算摆放了的建筑)
    public int getInnerBuildNumOverLevel(int innerBuildRhType, int level) {
        return (int) getInnerBuildRHProp().values().stream().filter(
                it -> it.getBuildType() == innerBuildRhType && it.getLevel() >= level && it.getBuildStateRH() == BuildStateRH.BuildStateRH_BUILD
        ).count();
    }

    //是否自动引导触发采矿和搜索
    public static void monitorNewbieStepEvent(PlayerNewbieStepEvent event) {
    }

    //检测基地数据的合法性
    private void checkBaseData() {
        //解锁默认区域
        var defaultAreaId = ResHolder.getResService(NewInnerBuildTemplateService.class).getDefaultAreaId();
        if (defaultAreaId > 0 && getBaseAreaProp(defaultAreaId) == null) {
            addNewBaseArea(defaultAreaId);
        }
        //矿点建筑未解锁的需要设置为解锁，但无法放置的状态，以给客户端展示矿田
        for (InnerBuildRhTemplate template : ResHolder.getInstance().getListFromMap(InnerBuildRhTemplate.class)) {
            if (isFixedCoordinateBuild(template)) {
                int curNum = (int) getInnerBuildRHProp().values().stream().filter(it -> it.getBuildType() == template.getId()).count();
                int maxNum = template.getNumMax();
                int unlockNum = maxNum - curNum;
                if (unlockNum > 0) {
                    unlockInnerMine(template.getId(), unlockNum);
                }
            }
        }
        //遍历所有建筑 重新尝试触发建筑的解锁
        for (CityBuildType buildType : CityBuildType.values()) {
            //可能要连续触发多次
            while (true) {
                int curNum = getInnerBuildNum(buildType.getNumber(), false);
                List<IntPairType> conds = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildNumUnlockCondition(buildType.getNumber(), curNum + 1);
                if (conds == null) {
                    break;
                }
                boolean satisy = true;
                for (IntPairType pair : conds) {
                    if (!isSatisfyCond(pair)) {
                        satisy = false;
                        break;
                    }
                }
                if (satisy) {
                    unLockInnerBuildRh(buildType.getNumber(), 1);
                } else {
                    break;
                }
            }
        }

    }

    //GM升级建筑
    public void upgradeNewInnerBuildGM(int buildType, int level) {
        BuildUpgradeRhTemplate template = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(buildType, level);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "upgrade template for level is null");
        }
        List<InnerBuildRHProp> buildings = innerBuildModel.getInnerBuildRH().values().stream().filter(it -> it.getBuildType() == buildType).toList();
        for (InnerBuildRHProp prop : buildings) {
            while (prop.getLevel() < level) {
                finishUpgrade(prop.getBuildId());
            }
        }
    }

    public void startDefend() {
        if (innerBuildModel.getDefend().getCampaignId() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "campaign id for defend is 0");
        }
        //查询条件 TODO 这样的保护是否合理 by Allen
        CampaignTdTemplate template = ResHolder.getTemplate(CampaignTdTemplate.class, innerBuildModel.getDefend().getCampaignId());
        if (template == null) {
            //数据异常 重新刷新
            innerBuildModel.getDefend().setCampaignId(0);
            checkDefendCampaign();
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        for (IntTripleType triple : template.getParticipationRequirementsTripleList()) {
            if (getInnerBuildNumOverLevel(triple.getKey(), triple.getValue1()) < triple.getValue2()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "campaign condition is not satisfied");
            }
        }
    }

    //结算内城危机战斗
    public AssetPackage endDefendBattle(boolean won) {
        PlayerDefendInfoProp defend = innerBuildModel.getDefend();
        if (defend.getCampaignId() <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        //结算结果
        CampaignTdTemplate innerTemplate = ResHolder.findTemplate(CampaignTdTemplate.class, defend.getCampaignId());
        if (innerTemplate != null) {
            //获胜奖励
            if (won) {
                AssetPackage.Builder assetPackage = AssetPackage.builder();
                assetPackage.plusItemList(innerTemplate.getRewardType2PairList())
                        .plusCurrency(innerTemplate.getRewardType1PairList());

                //发奖励
                getOwner().getAssetComponent().give(assetPackage.build(), CommonEnum.Reason.ICR_CITY_DEFEND);

                //获胜后自动刷新 FIXME 临时逻辑
                defend.setCampaignId(0);
                if (defend.getNextCampaignId() > 0) {
                    defend.setCampaignId(defend.getNextCampaignId());
                    long now = SystemClock.now();
                    defend.setStartTsMs(now);
                    defend.setNextCampaignId(0);
                }

                getOwner().getStatisticComponent().recordSingleStatistic(DEFEND_FINISH, 1);
                new DefendBattleFireEvent(getOwner(), 1).dispatch();

                return assetPackage.build();
            } else {
                return AssetPackage.builder().build();
            }
        } else {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "campaign template is null");
        }
    }

    //防御战结算
    public PlayerCampaign.Player_CampaignMissionFinish_S2C endDefend(CommonMsg.MissionMapResultInfo result) {

        AssetPackage assetPackage = endDefendBattle(result.getResult() > 0);
        PlayerCampaign.Player_CampaignMissionFinish_S2C.Builder builder = PlayerCampaign.Player_CampaignMissionFinish_S2C.newBuilder();
        builder.setResult(result.getResult());
        builder.setBaseRewards(assetPackage.toPb());

        return builder.build();
    }

    //内城加成效果
    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.INNER_BUILDING_RH;
    }

    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        Map<CommonEnum.AdditionSourceType, Long> res = new HashMap<>();
        long sum = 0;
        List<IntPairType> initResProd = ResHolder.getConsts(ConstInnermapRhTemplate.class).getInitialResourcesProductivity();
        for (IntPairType pair : initResProd) {
            if (pair.getKey() == additionId) {
                sum += pair.getValue();
            }
        }
        List<IntPairType> initStorage = ResHolder.getConsts(ConstInnermapRhTemplate.class).getResourcesStorage();
        for (IntPairType pair : initStorage) {
            if (pair.getKey() == additionId) {
                sum += pair.getValue();
            }
        }
        for (InnerBuildRHProp build : getInnerBuildRHProp().values()) {
            if (build.getBuildStateRH() == BuildStateRH.BuildStateRH_UN_LOCK) {
                continue;
            }
            BuildUpgradeRhTemplate lvTemplate = ResHolder.getResService(NewInnerBuildTemplateService.class).getBuildUpgradeTempate(build.getBuildType(), build.getLevel());
            if (lvTemplate == null) {
                continue;
            }
            for (IntPairType pair : lvTemplate.getBuffEffectPairList()) {
                if (pair.getKey() == additionId) {
                    sum += pair.getValue();
                }
            }
        }
        res.put(CommonEnum.AdditionSourceType.AST_INNER_BUILDING_RH, sum);
        return res;
    }


}
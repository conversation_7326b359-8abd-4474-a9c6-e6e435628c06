package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 打开宝箱事件
 *
 * <AUTHOR>
 */
public class OpenTreasureChestsEvent extends PlayerTaskEvent {
    private final int type;
    private final int num;

    public OpenTreasureChestsEvent(PlayerEntity entity, int type, int num) {
        super(entity);
        this.type = type;
        this.num = num;
    }

    public int getNum() {
        return num;
    }

    public int getType() {
        return type;
    }
}

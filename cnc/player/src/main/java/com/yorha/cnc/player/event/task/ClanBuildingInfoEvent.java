package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.StructClan;

import java.util.Map;

public class ClanBuildingInfoEvent extends PlayerTaskEvent {
    private final Map<Integer, StructClan.ClanBuildingInfo> buildingInfo;

    public ClanBuildingInfoEvent(PlayerEntity entity, Map<Integer, StructClan.ClanBuildingInfo> buildingInfo) {
        super(entity);
        this.buildingInfo = buildingInfo;
    }

    public Map<Integer, StructClan.ClanBuildingInfo> getBuildingInfo() {
        return this.buildingInfo;
    }

}

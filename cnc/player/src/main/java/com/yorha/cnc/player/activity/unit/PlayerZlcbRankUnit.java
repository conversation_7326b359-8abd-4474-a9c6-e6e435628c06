package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerInnerCollectResourceEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.cnc.player.event.task.ResBuildingCollectResourceEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.rank.RankDataTemplateService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ActivityUnitType;
import com.yorha.proto.CommonEnum.RankType;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityReserveServerTemplate;
import res.template.ConstActivityTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 战略储备排行榜活动
 *
 * <AUTHOR>
 */
public class PlayerZlcbRankUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerZlcbRankUnit.class);
    /**
     * 延时同步至联排行榜的task
     */
    private ActorTimer updateSchedule = null;
    private final RankType rankType;
    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerInnerCollectResourceEvent.class,
            ResBuildingCollectResourceEvent.class,
            PlayerKillBigSceneMonsterEvent.class
    );

    static {
        ActivityUnitFactory.register(ActivityUnitType.AUT_ZLCB_RANK, (owner, prop, template) ->
                new PlayerZlcbRankUnit(owner, prop.getCommonScoreRewardUnit(), template.getActivityRankType())
        );
    }

    public PlayerZlcbRankUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp, RankType rankType) {
        super(ownerActivity, unitProp);
        this.rankType = rankType;
    }

    @Override
    public void load(boolean isInitial) {
        LOGGER.info("PlayerRankSnapshot rankId={} score={}",
                RankDataTemplateService.getRankIdByRankType(rankType),
                unitProp.getZlcbUnit().getScore());
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        // 内城资源收集
        if (event instanceof PlayerInnerCollectResourceEvent) {
            PlayerInnerCollectResourceEvent playerInnerCollectResourceEvent = (PlayerInnerCollectResourceEvent) event;
            for (long resourceNum : playerInnerCollectResourceEvent.getResource().values()) {
                int nowInnerResourceScore = unitProp.getZlcbUnit().getInnerResourceScore() + (int) resourceNum;
                unitProp.getZlcbUnit().setInnerResourceScore(nowInnerResourceScore);
                int limit = ResHolder.getConsts(ConstActivityTemplate.class).getEventReserveCityResource();
                if (limit <= 0) {
                    unitProp.getZlcbUnit().setInnerResourceScore(0);
                    // 发奖励
                    sendMailReward(ResHolder.getConsts(ConstActivityTemplate.class).getEventReserveMailId());
                    return;
                }
                while (nowInnerResourceScore >= limit) {
                    nowInnerResourceScore = nowInnerResourceScore - limit;
                    unitProp.getZlcbUnit().setInnerResourceScore(nowInnerResourceScore);
                    // 发奖励
                    sendMailReward(ResHolder.getConsts(ConstActivityTemplate.class).getEventReserveMailId());
                }
            }
            return;
        }

        // 大世界资源采集
        if (event instanceof ResBuildingCollectResourceEvent) {
            ResBuildingCollectResourceEvent resBuildingCollectResourceEvent = (ResBuildingCollectResourceEvent) event;
            for (long resourceNum : resBuildingCollectResourceEvent.getCollectResMap().values()) {
                int nowMapResource = unitProp.getZlcbUnit().getMapResourceScore() + (int) resourceNum;
                unitProp.getZlcbUnit().setMapResourceScore(nowMapResource);
                int limit = ResHolder.getConsts(ConstActivityTemplate.class).getEventReserveMapResource();
                if (limit <= 0) {
                    unitProp.getZlcbUnit().setMapResourceScore(0);
                    // 发奖励
                    sendMailReward(ResHolder.getConsts(ConstActivityTemplate.class).getEventReserveMailId());
                    return;
                }
                while (nowMapResource >= limit) {
                    nowMapResource = nowMapResource - limit;
                    unitProp.getZlcbUnit().setMapResourceScore(nowMapResource);
                    // 发奖励
                    sendMailReward(ResHolder.getConsts(ConstActivityTemplate.class).getEventreservemailid2());
                }
            }
            return;
        }

        // 打野
        if (event instanceof PlayerKillBigSceneMonsterEvent) {
            PlayerKillBigSceneMonsterEvent playerKillBigSceneMonsterEvent = (PlayerKillBigSceneMonsterEvent) event;
            if (playerKillBigSceneMonsterEvent.getMonsterCategory() != CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE.getNumber()) {
                return;
            }
            ActivityReserveServerTemplate template = ResHolder.getResService(ActivityResService.class).getZlcbRewardTemplate(ActivityUnitType.AUT_ZLCB_RANK, playerKillBigSceneMonsterEvent.getLevel());
            if (template == null) {
                return;
            }
            if (RandomUtils.nextInt(GameLogicConstants.IPPM) < template.getProb()) {
                sendMailReward(template.getMailId());
            }
        }
    }

    private void onUpdateScore() {
        unitProp.getZlcbUnit().setScore(unitProp.getZlcbUnit().getScore() + 1);
        if (updateSchedule == null) {
            updateSchedule = addActTimer(
                    TimerReasonType.PLAYER_ZLCB_RANK_UPDATE, this::updatePowerToRank,
                    RandomUtils.nextInt(3, 5),
                    TimeUnit.SECONDS
            );
        }
    }

    private void sendMailReward(int mailId) {
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(mailId);
        params.getSenderBuilder().setSenderId(0);
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player().getPlayerId())
                .setZoneId(player().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, params.build());
        onUpdateScore();
    }

    /**
     * 更新积分数据到rank
     */
    private void updatePowerToRank() {
        updateSchedule = null;
        long score = unitProp.getZlcbUnit().getScore();
        int rankId = RankDataTemplateService.getRankIdByRankType(rankType);
        player().getPlayerRankComponent().updateZoneRanking(rankId, score);
    }
}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.player.SettingService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ExpressionProp;
import com.yorha.game.gen.prop.Int32ExpressionMapProp;
import com.yorha.game.gen.prop.PlayerSettingModelProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncSet;
import res.template.ConstSettingTemplate;
import res.template.ExpressionTemplate;
import res.template.PrivateFlagTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.error.ErrorCode.SPASSWORD_CHECK_ERROR;
import static com.yorha.common.enums.error.ErrorCode.SPASSWORD_CHECK_OVERLIMIT;

/**
 * 玩家设置组件
 *
 * <AUTHOR>
 */
public class PlayerSettingComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSettingComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerSettingComponent::refreshSpwFailedTimes);
    }

    private ActorTimer closeSpwTimer;

    public PlayerSettingComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            unlockExpression(ResHolder.getConsts(ConstSettingTemplate.class).getDefaultExpress());
            //默认个人旗帜id
            setPFlag(ResHolder.getResService(SettingService.class).getRandomPflag());
        }
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        PlayerSettingModelProp settingModelProp = getOwner().getProp().getSettingModel();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        if (settingModelProp.getCloseSpwTime() > 0) {
            mailSendParams.setMailTemplateId(ResHolder.getConsts(ConstSettingTemplate.class).getSecondaryPasswordCloseMail())
                    .setIsOnlyForShow(true)
                    .getContentBuilder()
                    .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                    .getDisplayDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_TIME_STAMP, getOwner().getProp().getSettingModel().getCloseSpwTime()));
            final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                    .setPlayerId(getOwner().getPlayerId())
                    .setZoneId(getOwner().getZoneId())
                    .build();
            this.getOwner().getMailComponent().sendPersonalMail(receiver, mailSendParams.build());
        }
        getOwner().getProp().getSettingModel().setFailedTimesByDay(getOwner().getStatisticComponent().getSingleStatistic(StatisticEnum.FAILED_TIMES_TIME));
        long closeSpwTime = settingModelProp.getCloseSpwTime();
        if ((closeSpwTime > 0) && (closeSpwTimer == null)) {
            long delay = closeSpwTime - SystemClock.now();
            if (delay <= 0) {
                tryCloseSpassword();
                return;
            }
            closeSpwTimer = ownerActor().addTimer(TimerReasonType.CLOSE_PASSWORD, this::tryCloseSpassword, delay, TimeUnit.MILLISECONDS);
        }
    }

    public static void refreshSpwFailedTimes(PlayerDayRefreshEvent event) {
        event.getPlayer().getProp().getSettingModel().setFailedTimesByDay(0);
    }

    public int getPFlag() {
        return getOwner().getProp().getSettingModel().getCurPFlag();
    }

    /**
     * 设置个人旗帜
     *
     * @param flagId
     */
    public void setPFlag(int flagId) {
        if (getOwner().getProp().getSettingModel().getCurPFlag() == flagId) {
            return;
        }
        Map<Integer, PrivateFlagTemplate> map = ResHolder.getInstance().getMap(PrivateFlagTemplate.class);
        if (!map.containsKey(flagId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        getOwner().getProp().getSettingModel().setCurPFlag(flagId);
        if (getOwner().getProp().getSettingModel().getShowPFlag()) {
            SsScenePlayer.SetPFlagCmd.Builder cmd = SsScenePlayer.SetPFlagCmd.newBuilder().setPlayerId(getPlayerId()).setPFlagId(flagId);
            ownerActor().tellBigScene(cmd.build());
        }
    }

    /**
     * 设置个人旗帜展示状态
     *
     * @param show
     */
    public void setPFlagStatus(boolean show) {
        if (getOwner().getProp().getSettingModel().getShowPFlag() == show) {
            return;
        }
        getOwner().getProp().getSettingModel().setShowPFlag(show);
        int pFlagId = 0;
        if (show) {
            pFlagId = getOwner().getProp().getSettingModel().getCurPFlag();
        }
        SsScenePlayer.SetPFlagCmd.Builder cmd = SsScenePlayer.SetPFlagCmd.newBuilder().setPlayerId(getPlayerId()).setPFlagId(pFlagId);
        ownerActor().tellBigScene(cmd.build());
    }

    public void setRegisterClientIp(String clientIp) {
        getOwner().getProp().getSettingModel().setRegisterIp(clientIp);
    }

    /**
     * 解锁表情
     */
    public void unlockExpression(List<Integer> expressions) {
        Map<Integer, ExpressionTemplate> map = ResHolder.getInstance().getMap(ExpressionTemplate.class);
        for (int expression : expressions) {
            if (!map.containsKey(expression)) {
                LOGGER.warn("playerId: {} try unlock expression :{} invaild", getPlayerId(), expression);
                continue;
            }
            if (getOwner().getProp().getSettingModel().getExpressions().containsKey(expression)) {
                continue;
            }
            getOwner().getProp().getSettingModel().getExpressions().put(expression, new ExpressionProp().setExpressionId(expression));
        }
    }

    /**
     * 使用表情
     */
    public void useExpression(long entityId, int expressionId) {
        Int32ExpressionMapProp expressionMapProp = getOwner().getProp().getSettingModel().getExpressions();
        if (!expressionMapProp.containsKey(expressionId)) {
            // 弹个错误码
            return;
        }
        SsScenePlayer.SetExpressionCmd cmd = SsScenePlayer.SetExpressionCmd.newBuilder().setEntityId(entityId).setExpressionId(expressionId).setPlayerId(getPlayerId()).build();
        ownerActor().tellCurScene(cmd);
    }

    /**
     * 关闭二级密码
     */
    public void closeSpassword() {
        if (StringUtils.isEmpty(getOwner().getProp().getSettingModel().getSPassWord())) {
            return;
        }
        long now = SystemClock.now();
        long delay = TimeUnit.SECONDS.toMillis(ResHolder.getConsts(ConstSettingTemplate.class).getCloseSpwTime());
        getOwner().getProp().getSettingModel().setCloseSpwTime(now + delay);
        if (closeSpwTimer != null) {
            closeSpwTimer.cancel();
            closeSpwTimer = null;
        }
        closeSpwTimer = ownerActor().addTimer(TimerReasonType.CLOSE_PASSWORD_2, this::tryCloseSpassword, delay, TimeUnit.MILLISECONDS);
    }

    /**
     * 取消关闭二级密码
     */
    public void cancelCloseSpassword() {
        getOwner().getProp().getSettingModel().setCloseSpwTime(0);
    }

    /**
     * 设置二级密码
     *
     * @param password
     */
    public void setSpassword(String password, String oldPassWord) {
        if (getOwner().getProp().getSettingModel().getOpenSpw()) {
            innerCheckSpassword(oldPassWord);
        }
        getOwner().getProp().getSettingModel().setSPassWord(password);
        setSpasswordStatus(true);
        QlogCncSet.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("set_pin")
                .sendToQlog();
    }

    /**
     * 设置二级密码状态
     *
     * @param openSpw
     */
    public void setSpasswordStatus(boolean openSpw) {
        getOwner().getProp().getSettingModel().setOpenSpw(openSpw);
    }


    /**
     * 验证二级密码
     *
     * @param type
     * @param param
     * @param password
     */
    public void checkSpassword(CommonEnum.SPassWordCheckType type, long param, String password) {
        SettingService settingService = ResHolder.getResService(SettingService.class);
        if (!settingService.needCheck(type) || (StringUtils.isEmpty(getOwner().getProp().getSettingModel().getSPassWord()))) {
            return;
        }

        try {
            List<Integer> params;
            switch (type) {
                case SPWC_SP_ITEM:
                case SPWC_SP_SOLDIER:
                    params = settingService.getParam(type);
                    int paramToInt = (int) param;
                    if (!params.contains(paramToInt)) {
                        break;
                    }
                    innerCheckSpassword(password);
                    break;
                case SPWC_MANY_MONEY:
                    params = settingService.getParam(type);
                    for (int moneyLimit : params) {
                        if (param >= moneyLimit) {
                            innerCheckSpassword(password);
                            break;
                        }
                    }
                    break;
                case SPWC_CLAN:
                case SPWC_CLOSE:
                    innerCheckSpassword(password);
                    break;
                default:
                    throw new GeminiException("unknown spassword checktype:{}", type);
            }
        } catch (GeminiException e) {
            getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.FAILED_TIMES_TIME, 1);
            getOwner().getProp().getSettingModel().setFailedTimesByDay(getOwner().getStatisticComponent().getSingleStatistic(StatisticEnum.FAILED_TIMES_TIME));
            throw e;
        }
    }

    /**
     * 尝试关闭二级密码
     */
    private void tryCloseSpassword() {
        PlayerSettingModelProp settingModelProp = getOwner().getProp().getSettingModel();
        long closeSpwTime = settingModelProp.getCloseSpwTime();
        if (closeSpwTime <= 0) {
            return;
        }

        innerCloseSpassword();
    }

    public void closeSpasswordNoWait() {
        innerCloseSpassword();
    }

    private void innerCloseSpassword() {
        PlayerSettingModelProp settingModelProp = getOwner().getProp().getSettingModel();
        if (closeSpwTimer != null) {
            closeSpwTimer.cancel();
            closeSpwTimer = null;
        }
        settingModelProp.setSPassWord("");
        settingModelProp.setCloseSpwTime(0);
        settingModelProp.setOpenSpw(false);
        QlogCncSet.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("cancel_pin")
                .sendToQlog();
    }

    private void innerCheckSpassword(String password) {
        //二级密码超过每日上限
        if (getOwner().getStatisticComponent().getSingleStatistic(StatisticEnum.FAILED_TIMES_TIME) >= ResHolder.getConsts(ConstSettingTemplate.class).getSecondaryPasswordDailyWrong()) {
            throw new GeminiException(SPASSWORD_CHECK_OVERLIMIT);
        }
        if (!StringUtils.equals(password, getOwner().getProp().getSettingModel().getSPassWord())) {
            throw new GeminiException(SPASSWORD_CHECK_ERROR);
        }
    }

    /**
     * 设置开关
     *
     * @param on          true==打开，false==关闭
     * @param settingType 开关类型
     */
    public void turnSwitch(boolean on, CommonEnum.PlayerSwitchSettingType settingType) {
        int val = settingType.getNumber();
        if (on) {
            this.getOwner().getProp().getSettingModel().getSwitchSetting().add(val);
        } else {
            this.getOwner().getProp().getSettingModel().getSwitchSetting().remove(val);
        }
        LOGGER.info("PlayerSettingComponent turnSwitch switch={}, isOn={}", settingType, on);
    }

    /**
     * 获取开关设置状态
     *
     * @param settingType 开关类型
     * @return true==已打开
     */
    public boolean getSwitchStatus(CommonEnum.PlayerSwitchSettingType settingType) {
        return this.getOwner().getProp().getSettingModel().getSwitchSetting().contains(settingType.getNumber());
    }
}
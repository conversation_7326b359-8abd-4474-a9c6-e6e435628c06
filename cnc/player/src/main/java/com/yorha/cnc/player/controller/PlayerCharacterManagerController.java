package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.PlayerUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerCharacterManager;
import com.yorha.proto.SsSceneInfoMgr;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CHARACTER_MANAGER)
public class PlayerCharacterManagerController {

    /**
     * 获取角色列表
     */
    @CommandMapping(code = MsgType.PLAYER_GETCHARACTERLIST_C2S)
    public void handle(PlayerEntity playerEntity, PlayerCharacterManager.Player_GetCharacterList_C2S msg, int seqId) {
        playerEntity.getCharacterManagerComponent().handleGetCharacterList(seqId);
    }

    /**
     * 获取所有服务器状态
     */
    @CommandMapping(code = MsgType.PLAYER_GETZONESERVERLIST_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCharacterManager.Player_GetZoneServerList_C2S msg) {
        List<CommonMsg.ZoneServerInfo> allZoneServerInfo = playerEntity.getCharacterManagerComponent().getAllZoneServerInfo();
        return PlayerCharacterManager.Player_GetZoneServerList_S2C.newBuilder().addAllZoneServerList(allZoneServerInfo).build();
    }

    /**
     * 角色星标管理
     */
    @CommandMapping(code = MsgType.PLAYER_MANAGESTARFORCHARACTER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCharacterManager.Player_ManageStarForCharacter_C2S msg) {
        playerEntity.getCharacterManagerComponent().manageStarPlayer(msg.getSetStar(), msg.getPlayerId());
        return PlayerCharacterManager.Player_ManageStarForCharacter_S2C.getDefaultInstance();
    }

    /**
     * 检查创建新角色
     */
    @CommandMapping(code = MsgType.PLAYER_CHECKREGISTERNEWCHARACTER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S msg) {
        PlayerUtils.checkAccountRegisterLimit(playerEntity.getOpenId(), msg.getZoneId(), playerEntity.ownerActor());
        SsSceneInfoMgr.GetZoneIpPortAns ans = playerEntity.getCharacterManagerComponent().getZoneIpPort(msg.getZoneId(), playerEntity.getClientInfo().getChannelName());
        if (!ans.getCanRegister()) {
            throw new GeminiException(ErrorCode.MULTI_SERVER_TARGET_SERVER_CANNOT_REGISTER);
        }
        return PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.newBuilder()
                .setIp(ans.getIp())
                .setPort(ans.getPort())
                .setChannelName(ans.getChannelName())
                .build();
    }

    /**
     * 获取指定服ip port
     */
    @CommandMapping(code = MsgType.PLAYER_GETZONEIPPORT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerCharacterManager.Player_GetZoneIpPort_C2S msg) {
        SsSceneInfoMgr.GetZoneIpPortAns ans = playerEntity.getCharacterManagerComponent().getZoneIpPort(msg.getZoneId(), playerEntity.getClientInfo().getChannelName());
        return PlayerCharacterManager.Player_GetZoneIpPort_S2C.newBuilder()
                .setIp(ans.getIp())
                .setPort(ans.getPort())
                .setChannelName(ans.getChannelName())
                .build();
    }


}

package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class ConsumeCurrency implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int type = Integer.parseInt(args.get("currencyType"));
        CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(type);
        if (currencyType == null) {
            throw new GeminiException(ErrorCode.CURRENCY_NOT_EXIST.getCodeId());
        }
        int count = 1;
        if (args.containsKey("count")) {
            count = Integer.parseInt(args.get("count"));
        }
        if (currencyType == CommonEnum.CurrencyType.CT_None) {
            for (int i = 1; i <= 5; i++) {
                actor.getEntity().getPurseComponent().consume(CommonEnum.CurrencyType.forNumber(i), count, CommonEnum.Reason.ICR_GM, "");
            }
        } else {
            actor.getEntity().getPurseComponent().consume(currencyType, count, CommonEnum.Reason.ICR_GM, "");
        }
    }

    @Override
    public String showHelp() {
        return "ConsumeCurrency currencyType={value} count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

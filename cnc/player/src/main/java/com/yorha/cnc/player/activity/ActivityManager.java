package com.yorha.cnc.player.activity;

import com.yorha.cnc.player.activity.trigger.ActivityTriggerMgr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 活动管理器
 * <AUTHOR>
 */
public class ActivityManager {
    private static final Logger LOGGER = LogManager.getLogger(ActivityManager.class);
    private static class InstanceHolder {
        private static final ActivityManager INSTANCE = new ActivityManager();
    }

    public static ActivityManager getInstance() {
        return ActivityManager.InstanceHolder.INSTANCE;
    }
    /**
     *     活动模块中各单例管理类的初始化，任何报错终端zone的起服
     */
    public void init() {
        LOGGER.info("ActivityManager init");
        // 活动触发器初始化
        ActivityTriggerMgr.getInstance().init();
        LOGGER.info("ActivityManager init ok");
    }
}

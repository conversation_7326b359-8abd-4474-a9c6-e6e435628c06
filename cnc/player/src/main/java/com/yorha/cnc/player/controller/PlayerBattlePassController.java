package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerBattlePassModelProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerBattlepass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstBpTemplate;

/**
 * todo zeo: 赛季版本会和老版本同时开一段时间，老版本结束后，需要将老版本的协议，代码与配置删除，只保留赛季版本
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_BATTLEPASS)
public class PlayerBattlePassController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerBattlePassController.class);

    /**
     * 领取已完成任务的经验
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINBATTLEPASSTASKEXP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_ObtainTaskExp_C2S msg) {
        if (!playerEntity.getBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        int bpTaskId = msg.getBpTaskId();
        // 参数校验
        if (bpTaskId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 检查通行证活动是否有效
        if (!playerEntity.getBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        playerEntity.getBattlePassComponent().takeTaskReward(bpTaskId);
        LOGGER.info("PlayerBattlePassController obtain task exp, bpTaskId={}", bpTaskId);
        return PlayerBattlepass.Player_ObtainTaskExp_S2C.getDefaultInstance();
    }

    /**
     * 领取每日经验宝箱
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINDALIYEXPBOX_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_ObtainExpBox_C2S msg) {
        LOGGER.info("PlayerBattlePassController take dailyExpBox");
        if (!playerEntity.getBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        PlayerBattlePassModelProp prop = playerEntity.getProp().getBattlePassModel();
        // 今天已领过
        if (prop.getOpenDailyExpBox()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_HAS_GET_EXP_BOX);
        }
        // 不达领取线
        int heatValue = playerEntity.getProp().getTaskSystem().getHeatValue();
        if (heatValue < ResHolder.getConsts(ConstBpTemplate.class).getBPDailyTreasureThreshold()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_UNREACH_DAILY_EXP_BOX_THRESHOLD);
        }
        // 发奖
        int rewardExp = ResHolder.getConsts(ConstBpTemplate.class).getBPDailyTreasureReward();
        prop.setOpenDailyExpBox(true);
        playerEntity.getBattlePassComponent().addExp(rewardExp, "Active_Reward", 0);
        return PlayerBattlepass.Player_ObtainExpBox_S2C.getDefaultInstance();
    }

    /**
     * 购买等级
     */
    @CommandMapping(code = MsgType.PLAYER_PURCHASEBATTLEPASSLEVEL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_PurchaseBpLevel_C2S msg) {
        if (!playerEntity.getBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        int max = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        int level = msg.getLevel();
        int curBpLevel = playerEntity.getBattlePassComponent().getBpLevel();
        if ((level <= 0) || ((curBpLevel + level) > max)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int perLevelDiamond = ResHolder.getConsts(ConstBpTemplate.class).getBPGoldBuy();
        int costDiamond = level * perLevelDiamond;
        if (costDiamond <= 0) {
            WechatLog.error("PlayerBattlePassController purchase level, purchase level={}, costDiamond per level={}", level, perLevelDiamond);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        LOGGER.info("PlayerBattlePassController purchase level, purchase level={}, need costDiamond={}", level, costDiamond);
        int addExp = level * ResHolder.getConsts(ConstBpTemplate.class).getBpLevelUpExperience();
        AssetPackage asset = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND, costDiamond).build();
        playerEntity.verifyThrow(asset);
        playerEntity.consume(asset, CommonEnum.Reason.ICR_BP_PURCHASE_LEVEL, String.valueOf(level));
        playerEntity.getBattlePassComponent().addExp(addExp, "Gold_Purchase", 0);
        return PlayerBattlepass.Player_PurchaseBpLevel_S2C.getDefaultInstance();
    }

    /**
     * 领取等级奖励
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINBATTLEPASSLEVELREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_ObtainLevelReward_C2S msg) {
        LOGGER.info("PlayerBattlePassController obtain level reward, getAllLevel={}, special level={}", msg.getIsAll(), msg.getLevel());
        if (!playerEntity.getBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        AssetPackage asset;
        if (msg.getIsAll()) {
            asset = playerEntity.getBattlePassComponent().obtainAllLevelReward(CommonEnum.Reason.ICR_BP_TAKE_LEVEL_REWARD);
        } else {
            int level = msg.getLevel();
            if (msg.getExtraReward()) {
                level = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel() + 1;
            }
            asset = playerEntity.getBattlePassComponent().obtainSpecialLevelReward(level, msg.getExtraReward(), CommonEnum.Reason.ICR_BP_TAKE_LEVEL_REWARD);
        }
        if (asset.isEmpty()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_NO_LEVEL_REWARD_CAN_RECEIVE);
        }
        PlayerBattlepass.Player_ObtainLevelReward_S2C.Builder builder = PlayerBattlepass.Player_ObtainLevelReward_S2C.newBuilder();
        LOGGER.info("PlayerBattlePassController obtain level reward, reward={}", asset);
        builder.setReward(asset.toPb());
        return builder.build();
    }


    /**
     * 领取已完成任务的经验
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S msg) {
        if (!playerEntity.getSeasonBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        int bpTaskId = msg.getBpTaskId();
        // 参数校验
        if (bpTaskId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 检查通行证活动是否有效
        if (!playerEntity.getSeasonBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        playerEntity.getSeasonBattlePassComponent().takeTaskReward(bpTaskId);
        LOGGER.info("PlayerBattlePassController season obtain task exp, bpTaskId={}", bpTaskId);
        return PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.getDefaultInstance();
    }

    /**
     * 领取每日经验宝箱
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINSEASONDALIYEXPBOX_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_ObtainSeasonExpBox_C2S msg) {
        LOGGER.info("PlayerBattlePassController season take dailyExpBox");
        if (!playerEntity.getSeasonBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        PlayerBattlePassModelProp prop = playerEntity.getProp().getSeasonBattlePassModel();
        // 今天已领过
        if (prop.getOpenDailyExpBox()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_HAS_GET_EXP_BOX);
        }
        // 不达领取线
        int heatValue = playerEntity.getProp().getTaskSystem().getHeatValue();
        if (heatValue < ResHolder.getConsts(ConstBpTemplate.class).getBPDailyTreasureThreshold()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_UNREACH_DAILY_EXP_BOX_THRESHOLD);
        }
        // 发奖
        int rewardExp = ResHolder.getConsts(ConstBpTemplate.class).getBPDailyTreasureReward();
        prop.setOpenDailyExpBox(true);
        playerEntity.getSeasonBattlePassComponent().addExp(rewardExp, "Active_Reward", 0);
        return PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.getDefaultInstance();
    }

    /**
     * 购买等级
     */
    @CommandMapping(code = MsgType.PLAYER_PURCHASESEASONBATTLEPASSLEVEL_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S msg) {
        if (!playerEntity.getSeasonBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        int max = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        int level = msg.getLevel();
        int curBpLevel = playerEntity.getSeasonBattlePassComponent().getBpLevel();
        if ((level <= 0) || ((curBpLevel + level) > max)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int perLevelDiamond = ResHolder.getConsts(ConstBpTemplate.class).getBPGoldBuy();
        int costDiamond = level * perLevelDiamond;
        if (costDiamond <= 0) {
            WechatLog.error("PlayerBattlePassController season purchase level, purchase level={}, costDiamond per level={}", level, perLevelDiamond);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        LOGGER.info("PlayerBattlePassController season purchase level, purchase level={}, need costDiamond={}", level, costDiamond);
        int addExp = level * ResHolder.getConsts(ConstBpTemplate.class).getBpLevelUpExperience();
        AssetPackage asset = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND, costDiamond).build();
        playerEntity.verifyThrow(asset);
        playerEntity.consume(asset, CommonEnum.Reason.ICR_BP_PURCHASE_LEVEL, String.valueOf(level));
        playerEntity.getSeasonBattlePassComponent().addExp(addExp, "Gold_Purchase", 0);
        return PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.getDefaultInstance();
    }

    /**
     * 领取等级奖励
     */
    @CommandMapping(code = MsgType.PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S msg) {
        LOGGER.info("PlayerBattlePassController season obtain level reward, getAllLevel={}, special level={}", msg.getIsAll(), msg.getLevel());
        if (!playerEntity.getSeasonBattlePassComponent().hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        AssetPackage asset;
        if (msg.getIsAll()) {
            asset = playerEntity.getSeasonBattlePassComponent().obtainAllLevelReward(CommonEnum.Reason.ICR_BP_TAKE_LEVEL_REWARD);
        } else {
            int level = msg.getLevel();
            if (msg.getExtraReward()) {
                level = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel() + 1;
            }
            asset = playerEntity.getSeasonBattlePassComponent().obtainSpecialLevelReward(level, msg.getExtraReward(), CommonEnum.Reason.ICR_BP_TAKE_LEVEL_REWARD);
        }
        if (asset.isEmpty()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_NO_LEVEL_REWARD_CAN_RECEIVE);
        }
        PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.Builder builder = PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.newBuilder();
        LOGGER.info("PlayerBattlePassController season obtain level reward, reward={}", asset);
        builder.setReward(asset.toPb());
        return builder.build();
    }
}

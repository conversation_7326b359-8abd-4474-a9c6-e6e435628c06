package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家消耗体力事件
 *
 * <AUTHOR>
 */
public class PlayerConsumeEnergyEvent extends PlayerTaskEvent {
    /**
     * 体力变化值
     */
    private final int energyNum;

    private final String energyCostReason;

    /**
     * 体力变化前的值
     */
    private final int beforeEnergy;

    /**
     * 击杀野怪时是野怪配置id
     */
    private final int reasonTemplateId;


    public PlayerConsumeEnergyEvent(PlayerEntity entity, int energyNum, String energyCostReason, int beforeEnergy, int reasonTemplateId) {
        super(entity);
        this.energyNum = energyNum;
        this.energyCostReason = energyCostReason;
        this.beforeEnergy = beforeEnergy;
        this.reasonTemplateId = reasonTemplateId;
    }

    public int getEnergyNum() {
        return energyNum;
    }

    public String getEnergyCostReason() {
        return energyCostReason;
    }

    public int getBeforeEnergy() {
        return beforeEnergy;
    }

    public int getReasonTemplateId() {
        return reasonTemplateId;
    }
}

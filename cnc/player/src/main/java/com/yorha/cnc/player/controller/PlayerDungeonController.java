package com.yorha.cnc.player.controller;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DungeonType;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.SsScenePlayer;
import res.template.DungeonSkillTemplate;

/**
 * 副本相关
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_DUNGEON)
public class PlayerDungeonController {

    /**
     * 进入远征副本
     */
    @CommandMapping(code = MsgType.PLAYER_ENTEREXPEDITION_C2S)
    public PlayerScene.Player_EnterExpedition_S2C handle(PlayerEntity playerEntity, PlayerScene.Player_EnterExpedition_C2S msg) {
        createAndEnterExpedition(playerEntity, msg);
        return PlayerScene.Player_EnterExpedition_S2C.getDefaultInstance();
    }

    private void createAndEnterExpedition(PlayerEntity playerEntity, PlayerScene.Player_EnterExpedition_C2S msg) {
        /*
        if (!playerEntity.isInMainScene()) {
            throw new GeminiException(ErrorCode.DUNGEON_CANT_ENTER);
        }
        GeminiStopWatch watch = new GeminiStopWatch("create dungeon");
        // check
        playerEntity.getExpeditionComponent().checkCanEnter(msg.getDungeonId());
        LOGGER.info("{} try enter expedition id:{}  armySize:{}", playerEntity, msg.getDungeonId(), msg.getTroopMap().size());
        watch.mark("check");
        DungeonPlayerData.Builder dataBuilder = DungeonPlayerData.newBuilder();
        ExpeditionLevelConfigTemplate template = ResHolder.getInstance().getValueFromMap(ExpeditionLevelConfigTemplate.class, msg.getDungeonId());
        switch (template.getEnterMode()) {
            case DTM_ARMY:
                // 超出可用部队数
                if (msg.getTroopMap().size() > template.getTroopNumber() || msg.getTroopMap().isEmpty()) {
                    throw new GeminiException(ErrorCode.EXPEDITION_ARMY_ERROR);
                }
                checkExpeditionTroop(playerEntity, dataBuilder, msg.getDungeonId(), msg.getTroopMap());
                break;
            case DTM_CITY:
                copyExpeditionCity(playerEntity, dataBuilder, msg.getDungeonId());
                break;
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 设置阵营
        dataBuilder.getInfoBuilder().setCamp(Camp.C_DUNGEON_FRIENDLY);

        // copy下加成、增益
        SsScenePlayer.GetSceneAdditionSysAsk.Builder ask = SsScenePlayer.GetSceneAdditionSysAsk.newBuilder().setPlayerId(playerEntity.getPlayerId());
        SsScenePlayer.GetSceneAdditionSysAns ans = playerEntity.ownerActor().callBigScene(ask.build());
        dataBuilder.getInfoBuilder().getAdditionSysBuilder().setAddition(ans.getAdditionSys().getAddition());
        dataBuilder.getInfoBuilder().getDevBuffSysBuilder().setDevBuff(ans.getDevBuffSys().getDevBuff());

        // copy head
        dataBuilder.getInfoBuilder().setCardHead(playerEntity.getCardHead().getCopySsBuilder());
        watch.mark("build player");
        // 发送创建副本
        DungeonType type = DungeonType.DT_EXPEDITION;
        long id = IdFactory.nextId("CreateDungeon#" + type.name());
        IActorRef dungeonRef = ofDungeonActor(type, id);
        CreateDungeonAsk.Builder createBuilder = CreateDungeonAsk.newBuilder();
        createBuilder.setDungeonId(msg.getDungeonId()).setType(type);
        createBuilder.addAllowEnterPlayer(playerEntity.getPlayerId());
        playerEntity.ownerActor().createTargetDungeon(dungeonRef, createBuilder.build());
        watch.mark("create dungeon");
        // 切换场景
        switchScene2Dungeon(playerEntity, dataBuilder, dungeonRef, type);
        watch.mark("enter dungeon");
        playerEntity.getQlogComponent().sendExpeditionQLog(
                "start_expedition", template.getChapterId(), template.getId(),
                playerEntity.getExpeditionComponent().getLevelStar(template.getId()),
                0, 0);
        LOGGER.info("create dungeon cost time  {} {}", playerEntity, watch.stat());
         */
    }

    /*
    private void switchScene2Dungeon(PlayerEntity playerEntity, DungeonPlayerData.Builder dataBuilder, IActorRef dungeonRef, DungeonType type) {
        // 看看有没有视野需要清理
        playerEntity.getViewComponent().clearView();
        // 通知大世界切到副本去了
        EnterDungeonAsk.Builder builder = EnterDungeonAsk.newBuilder().setPlayerId(playerEntity.getEntityId());
        builder.setType(type);
        playerEntity.ownerActor().callCurScene(builder.build());
        // 正式进入远征副本
        try {
            builder.setSessionRef(playerEntity.getSessionComponent().getSessionRefData());
            builder.setData(dataBuilder);
            playerEntity.ownerActor().callTargetDungeon(dungeonRef, builder.build());
            long sceneId = Long.parseLong(dungeonRef.getActorId());
            playerEntity.getSceneMgrComponent().onPropChange2Dungeon(sceneId, type, dungeonRef, true);
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                LOGGER.info("exception_perf {} enterDungeon failed ", playerEntity, e);
            }
            playerEntity.ownerActor().callBigScene(LeaveDungeonAsk.newBuilder().setPlayerId(playerEntity.getPlayerId()).build());
            throw new GeminiException(ErrorCode.ENTER_DUNGEON_FAILED);
        }
    }
*/
    public static IActorRef ofDungeonActor(DungeonType type, long id, int instanceId) {
        //if (type == DungeonType.DT_EXPEDITION) {
        IActorRef actorRef = RefFactory.ofNewDungeon(id);
        if (actorRef.getBusId() == null) {
            throw new GeminiException(ErrorCode.FUNCTION_NOT_OPEN);
        }
        return actorRef;
        //}
        //throw new GeminiException(ErrorCode.FUNCTION_NOT_OPEN);
    }

    /**
     * 离开副本
     */
    @CommandMapping(code = MsgType.PLAYER_LEAVEDUNGEON_C2S)
    public PlayerScene.Player_LeaveDungeon_S2C handle(PlayerEntity playerEntity, PlayerScene.Player_LeaveDungeon_C2S msg) {
        playerEntity.getSceneMgrComponent().quitDungeon(msg.getType());
        return PlayerScene.Player_LeaveDungeon_S2C.getDefaultInstance();
    }

    /**
     * 重新进入副本
     */
    @CommandMapping(code = MsgType.PLAYER_REENTERDUNGEON_C2S)
    public PlayerScene.Player_ReEnterDungeon_S2C handle(PlayerEntity playerEntity, PlayerScene.Player_ReEnterDungeon_C2S msg) {
        if (!playerEntity.isInMainScene()) {
            throw new GeminiException(ErrorCode.DUNGEON_CANT_ENTER);
        }
        playerEntity.getSceneMgrComponent().reEnterDungeon(msg.getType());
        return PlayerScene.Player_ReEnterDungeon_S2C.getDefaultInstance();
    }

    /**
     * 副本切回大世界
     */
    @CommandMapping(code = MsgType.PLAYER_CHANGETOBIGSCENE_C2S)
    public PlayerScene.Player_ChangeToBigScene_S2C handle(PlayerEntity playerEntity, PlayerScene.Player_ChangeToBigScene_C2S msg) {
        if (!playerEntity.isInDungeon()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        playerEntity.getSceneMgrComponent().changeToBigScene();
        return PlayerScene.Player_ChangeToBigScene_S2C.getDefaultInstance();
    }


    /**
     * 使用副本技能
     */
    @CommandMapping(code = MsgType.PLAYER_USEDUNGEONSKILL_C2S)
    public PlayerScene.Player_UseDungeonSkill_S2C handle(PlayerEntity playerEntity, PlayerScene.Player_UseDungeonSkill_C2S msg) {
        DungeonSkillTemplate template = ResHolder.getInstance().findValueFromMap(DungeonSkillTemplate.class, msg.getSkillId());
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 区域技能必须指定坐标
        if (template.getType() == CommonEnum.DungeonSkillType.DST_AREA_SKILL && !msg.hasPoint()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        SsScenePlayer.UseDungeonSkillAsk.Builder ask = SsScenePlayer.UseDungeonSkillAsk.newBuilder();
        ask.setPlayerId(playerEntity.getEntityId()).setSkillId(msg.getSkillId());
        if (msg.hasPoint()) {
            ask.getPointBuilder().setX(msg.getPoint().getX()).setY(msg.getPoint().getY());
        }
        playerEntity.ownerActor().callCurScene(ask.build());
        return PlayerScene.Player_UseDungeonSkill_S2C.getDefaultInstance();
    }
}

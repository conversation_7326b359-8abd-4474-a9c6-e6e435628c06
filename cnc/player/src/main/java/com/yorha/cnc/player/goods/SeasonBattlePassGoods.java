package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.PlayerBattlePassModelProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;
import res.template.ConstBpTemplate;

import java.util.Collections;
import java.util.List;

/**
 * todo 常量表里几个礼包id还需要新增常量
 * 赛季版通行证
 *
 * <AUTHOR>
 */
public class SeasonBattlePassGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(SeasonBattlePassGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        this.checkCanApplyOrDeliver(owner, goodsTemplate, "checkApply");
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {

    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        this.checkCanApplyOrDeliver(owner, goodsTemplate, "checkBeforeDeliver");
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ConstBpTemplate template = ResHolder.getConsts(ConstBpTemplate.class);
        PlayerBattlePassModelProp prop = owner.getProp().getSeasonBattlePassModel();
        CommonEnum.BattltPassType oldType = prop.getBpType();
        LOGGER.info("SeasonBattlePassGoods afterBaseDeliver start, now bpType={}, level={}, exp={}, claimableGold={}", prop.getBpType(), prop.getLevel(), prop.getExp(), prop.getClaimableGold());
        // 购买基础黄金礼包
        if (goodsTemplate.getId() == template.getSeasonBPSmallGiftID()) {
            prop.setBpType(CommonEnum.BattltPassType.BPT_GOLD);
        } else if (goodsTemplate.getId() == template.getSeasonBPmidGiftID() || goodsTemplate.getId() == template.getSeasonBPBigGiftID()) {
            // 购买进阶黄金礼包
            prop.setBpType(CommonEnum.BattltPassType.BPT_GOLD_PRO);
            // 立即提升bp等级十级
            int addExp = template.getBPGoldProLevelUp() * template.getBpLevelUpExperience();
            owner.getSeasonBattlePassComponent().addExp(addExp, "Purchase_Bundle", goodsTemplate.getId());
        }
        // 标记 1 ~ level级的黄金奖励可领取
        if (oldType == CommonEnum.BattltPassType.BPT_SILVER) {
            for (int i = 1; i <= prop.getLevel(); i++) {
                owner.getSeasonBattlePassComponent().markGoldRewardClaimable(i);
            }
        }
        LOGGER.info("SeasonBattlePassGoods afterBaseDeliver end, deliver bpType={}, level={}, exp={}, claimableGold={}", prop.getBpType(), prop.getLevel(), prop.getExp(), prop.getClaimableGold());
        return Collections.emptyList();
    }

    private void checkCanApplyOrDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, String reason) {
        PlayerBattlePassModelProp prop = owner.getProp().getSeasonBattlePassModel();
        LOGGER.info("SeasonBattlePassGoods checkCanApplyOrDeliver start, check reason={}, goodId={}, player bpType={}", reason, goodsTemplate.getId(), prop.getBpType());
        if (prop.getBpType() == CommonEnum.BattltPassType.BPT_NONE) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        ConstBpTemplate template = ResHolder.getConsts(ConstBpTemplate.class);
        switch (prop.getBpType()) {
            case BPT_NONE:
                // 通行证活动未开启
                throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
            case BPT_SILVER:
                // 通行证活动已解锁，尝试购买通行证，只能购买基础黄金bp、基础+进阶黄金bp，不能直接购买进阶黄金bp
                if (goodsTemplate.getId() != template.getSeasonBPSmallGiftID() && goodsTemplate.getId() != template.getSeasonBPBigGiftID()) {
                    throw new GeminiException(ErrorCode.BATTLE_PASS_NOT_ALLOW_PURCHASE);
                }
                break;
            case BPT_GOLD:
                // 已购买基础黄金通行证，只能在购买进阶黄金bp
                if (goodsTemplate.getId() != template.getSeasonBPmidGiftID()) {
                    throw new GeminiException(ErrorCode.BATTLE_PASS_NOT_ALLOW_PURCHASE);
                }
                break;
            case BPT_GOLD_PRO:
                // 已经是进阶黄金礼包了，不能再买了
                throw new GeminiException(ErrorCode.BATTLE_PASS_ALREADY_BEST);
            default:
                throw new GeminiException(ErrorCode.BATTLE_PASS_UNDEFINED_TYPE);
        }
        LOGGER.info("SeasonBattlePassGoods checkCanApplyOrDeliver end, check reason={}, goodId={}, bpType={}", reason, goodsTemplate.getId(), prop.getBpType().getNumber());
    }
}

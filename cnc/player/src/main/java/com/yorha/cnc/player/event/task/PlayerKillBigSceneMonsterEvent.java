package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 完成击杀野怪(野蛮人城寨)事件
 *
 * <AUTHOR>
 */
public class PlayerKillBigSceneMonsterEvent extends PlayerTaskEvent {
    private final int monsterId;
    private final int monsterCategory;
    private final int level;
    private final long summonPlayerId;

    public int getMonsterId() {
        return monsterId;
    }

    public int getLevel() {
        return level;
    }

    public int getMonsterCategory() {
        return monsterCategory;
    }

    public long getSummonPlayerId() {
        return summonPlayerId;
    }

    public PlayerKillBigSceneMonsterEvent(PlayerEntity entity, int monsterId, int category, int level, long summonPlayerId) {
        super(entity);
        this.monsterId = monsterId;
        this.monsterCategory = category;
        this.level = level;
        this.summonPlayerId = summonPlayerId;
    }

}

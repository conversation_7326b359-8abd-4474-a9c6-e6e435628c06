package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerHospital;

import static com.yorha.proto.CommonMsg.HospitalTreatUnion;

/**
 * 医院（维修站）相关的协议处理
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_HOSPITAL)
public class PlayerHospitalController {

    /**
     * 医院治疗
     */
    @CommandMapping(code = MsgType.PLAYER_HOSPITALTREAT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerHospital.Player_HospitalTreat_C2S msg) {
        HospitalTreatUnion treatUnion = msg.getTreatUnion();
        playerEntity.getHospitalComponent().handleTreat(treatUnion);
        return PlayerHospital.Player_HospitalTreat_S2C.getDefaultInstance();
    }

    /**
     * 医院一键治疗
     */
    @CommandMapping(code = MsgType.PLAYER_HOSPITALFASTTREAT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerHospital.Player_HospitalFastTreat_C2S msg) {
        HospitalTreatUnion treatUnion = msg.getTreatUnion();
        playerEntity.getHospitalComponent().handleFastTreat(treatUnion, msg.getSPassWord());
        return PlayerHospital.Player_HospitalFastTreat_S2C.getDefaultInstance();
    }

    /**
     * 医院收取治疗完毕的士兵
     */
    @CommandMapping(code = MsgType.PLAYER_HOSPITALRETURNSOLDIERS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerHospital.Player_HospitalReturnSoldiers_C2S msg) {
        playerEntity.getHospitalComponent().handleReturnSoldiers();
        return PlayerHospital.Player_HospitalReturnSoldiers_S2C.getDefaultInstance();
    }
}

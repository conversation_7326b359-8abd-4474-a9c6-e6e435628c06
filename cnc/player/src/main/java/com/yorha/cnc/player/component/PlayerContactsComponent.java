package com.yorha.cnc.player.component;

import com.google.common.collect.Sets;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.CardHelper;
import com.yorha.game.gen.prop.PlayerContactsModelProp;
import com.yorha.game.gen.prop.PlayerContactsProp;
import com.yorha.proto.SsPlayerCard;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Set;

/**
 * 跨服联系别人，需要考虑迁服等场景
 * 缓存一些联系人的curZoneId
 * 使用的模块自驱删除对某个联系人的缓存
 * <p>
 * 可以考虑做成进程级、纯内存等形态
 * <p>
 * contact不提供同步接口，player和player之间不可以阻塞，会死锁
 * <p>
 * 不太适用就不要用
 */
public class PlayerContactsComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerContactsComponent.class);

    public PlayerContactsComponent(PlayerEntity owner) {
        super(owner);
    }

    public void saveContacts(long playerId, int zoneId, String reason) {
        PlayerContactsModelProp model = getOwner().getProp().getContactsModel();
        PlayerContactsProp contacts = model.getContactsBookV(playerId);
        if (contacts == null) {
            contacts = model.addEmptyContactsBook(playerId).setPlayerId(playerId);
        }
        contacts.setCachedZoneId(zoneId);
        contacts.addCacheReason(reason);
        LOGGER.info("PlayerContactsComponent saveContacts playerId={} zoneId={} reason={}", playerId, zoneId, reason);
    }

    public void removeAllContacts(String contactReason) {
        PlayerContactsModelProp model = getOwner().getProp().getContactsModel();
        Set<Long> toRemove = Sets.newHashSet();
        for (PlayerContactsProp contactsProp : model.getContactsBook().values()) {
            if (contactsProp.isCacheReasonContains(contactReason)) {
                toRemove.add(contactsProp.getPlayerId());
            }
        }
        for (Long pid : toRemove) {
            removeContacts(pid, contactReason);
        }
    }

    public void removeContacts(long playerId, String reason) {
        PlayerContactsModelProp model = getOwner().getProp().getContactsModel();
        PlayerContactsProp contacts = model.getContactsBookV(playerId);
        if (contacts != null) {
            contacts.removeCacheReason(reason);
            LOGGER.info("PlayerContactsComponent removeContacts playerId={} reason={}", playerId, reason);
            if (contacts.isCacheReasonEmpty()) {
                model.removeContactsBookV(playerId);
            }
        }
    }

    public void updateContacts(long pid, int newZoneId, String reason) {
        if (newZoneId <= 0) {
            return;
        }
        PlayerContactsProp contactsProp = getOwner().getProp().getContactsModel().getContactsBookV(pid);
        if (contactsProp == null) {
            saveContacts(pid, newZoneId, reason);
            return;
        }
        int cached = contactsProp.getCachedZoneId();
        if (cached != newZoneId) {
            LOGGER.info("PlayerContactsComponent updateContacts {} {}->{}", contactsProp.getPlayerId(), cached, newZoneId);
            contactsProp.setCachedZoneId(newZoneId);
        }
    }

    public void tryUpdateContacts(long pid, int newZoneId) {
        if (newZoneId <= 0) {
            return;
        }
        PlayerContactsProp contactsProp = getOwner().getProp().getContactsModel().getContactsBookV(pid);
        if (contactsProp == null) {
            LOGGER.warn("PlayerContactsComponent tryUpdateContacts failed contact not exists{} {}", pid, newZoneId);
            return;
        }
        int cached = contactsProp.getCachedZoneId();
        if (cached != newZoneId) {
            LOGGER.info("PlayerContactsComponent tryUpdateContacts {} {}->{}", contactsProp.getPlayerId(), cached, newZoneId);
            contactsProp.setCachedZoneId(newZoneId);
        }
    }

    public void tell(long playerId, GeneratedMessageV3 cmd) {
        int targetZoneId = getTargetZoneId(playerId);
        ownerActor().tellPlayer(targetZoneId, playerId, cmd);
    }

    public <ANS extends GeneratedMessageV3> void contactWith(long playerId, GeneratedMessageV3 ask, ContactConsumer<ANS, Throwable> callback) {
        final PlayerActor playerActor = ownerActor();
        int targetZoneId = getTargetZoneId(playerId);
        playerActor.<ANS>askPlayer(targetZoneId, playerId, ask).onComplete((ans, err) -> {
            if (err instanceof MigrateException) {
                // 只有移民异常需要处理一下，做一次重试
                int tarNewZoneId = ((MigrateException) err).getZoneId();
                playerActor.<ANS>askPlayer(tarNewZoneId, playerId, ask).onComplete((ans1, err1) ->
                        callback.accept(tarNewZoneId, ans1, err1)
                );
            } else {
                callback.accept(targetZoneId, ans, err);
            }
        });
    }

    public <ANS extends GeneratedMessageV3> void batchContactWith(Map<Long, ? extends GeneratedMessageV3> askMap, BatchContactConsumer<ANS, Throwable> oneComplete, Runnable allComplete) {
        final int[] counter = {askMap.size()};
        for (Map.Entry<Long, ? extends GeneratedMessageV3> askEntry : askMap.entrySet()) {
            final long pid = askEntry.getKey();
            this.<ANS>contactWith(pid, askEntry.getValue(), (targetCurZoneId, ans, err) -> {
                counter[0]--;
                oneComplete.accept(pid, targetCurZoneId, ans, err);
                if (counter[0] <= 0) {
                    allComplete.run();
                }
            });
        }
    }

    private int getTargetZoneId(long targetPlayerId) {
        final PlayerActor playerActor = ownerActor();
        PlayerContactsModelProp contactsModel = getOwner().getProp().getContactsModel();
        PlayerContactsProp contacts = contactsModel.getContactsBookV(targetPlayerId);
        int targetZoneId;
        if (contacts != null) {
            targetZoneId = contacts.getCachedZoneId();
        } else {
            SsPlayerCard.QueryPlayerCardAns cardAns = CardHelper.queryPlayerCardSync(playerActor, targetPlayerId);
            targetZoneId = cardAns.getCardInfo().getZoneId();
        }
        return targetZoneId;
    }

    @FunctionalInterface
    public interface ContactConsumer<T, U> {
        void accept(int targetCurZoneId, T t, U u);
    }

    @FunctionalInterface
    public interface BatchContactConsumer<T, U> {
        void accept(long pid, int targetCurZoneId, T t, U u);
    }

}

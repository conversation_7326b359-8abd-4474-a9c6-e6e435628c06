package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.OccupyCityEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 参与占领X级及更高等级的城市X次
 *
 * <AUTHOR>
 */
public class AttackCityChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(AttackCityChecker.class);

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(OccupyCityEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configLv = taskParams.get(0);
        int configTimes = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() != CommonEnum.TaskCalcType.TCT_RECEIVE) {
            LOGGER.error("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE));
            return prop.getProcess() >= configTimes;
        }

        // 仅接取类任务计数
        if (event instanceof OccupyCityEvent) {
            // 事件中的城市等级大于等于配置等级时，更新进度
            if (((OccupyCityEvent) event).getLv() >= configLv) {
                prop.setProcess(Math.min(prop.getProcess() + 1, configTimes));
            }
        }

        return prop.getProcess() >= configTimes;
    }
}

package com.yorha.cnc.player.component;

import com.google.protobuf.ByteString;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.MissionCompleteEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.ItemDesc;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.innermap.InnerMapService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.game.gen.prop.PlayerMissionModelProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerCampaign;
import com.yorha.proto.SsSceneDungeon;
import res.template.PveMissionRhTemplate;

import java.util.ArrayList;
import java.util.List;

public class PlayerMissionComponent extends PlayerComponent {

    public PlayerMissionComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerMissionModelProp getProp() {
        return getOwner().getProp().getPlayerMissionModel();
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        //设置当前关卡位置
        if (getProp().getCurMissionId() == 0) {
            getProp().setCurMissionId(ResHolder.getResService(InnerMapService.class).getFirstMainMissionId());
        }
        //如果主线关卡已经通关，则尝试往后移动一个主线关卡
        if (isMissionCompleted(getProp().getCurMissionId())) {
            gotoNextMission();
        }
    }

    public void startMission(int missionId, int troopId) {
        PveMissionRhTemplate template = ResHolder.getTemplate(PveMissionRhTemplate.class, missionId);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "pve mission template is missing");
        }
        //检测关卡类型
        if (template.getMissionType() != CommonEnum.MissionType.MIST_BATTLE) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "mission is not a battle");
        }
        //检测前置条件
        for (Integer id : template.getPrevMissionList()) {
            if (!isMissionCompleted(id)) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "pre mission is not completed");
            }
        }
        //禁止重复操作
        if (isMissionCompleted(missionId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "mission is already completed");
        }

//        //我方数据
//        List<CampaignUnitProp> units = new ArrayList<>();
//        //新手教学关卡不用检测我方队伍数据
//        if (!ResHolder.getConsts(ConstInnermapRhTemplate.class).getNewbieMissionId().contains(missionId)) {
//            TroopRHProp troop = getOwner().getTroopFormationComponent().getFormationProp().getTroopRHMap().get(troopId);
//            if (troop == null) {
//                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "troop id is invalid");
//            }
//            for (FormationRHProp formation : troop.getTroop().values()) {
//                CampaignUnitProp unitProp = new CampaignUnitProp();
//                unitProp.setUnitId(formation.getSoldier().getSoldierId())
//                        .setHeroId(formation.getHeroId())
//                        .setUnitLevel(getOwner().getUnitComponent().getUnitLevel(formation.getSoldier().getSoldierId()))
//                        .setHeroLevel(getOwner().getHeroComponent().getHeroPropLevel(formation.getHeroId()))
//                        .setSlotId(formation.getSlotId());
//                units.add(unitProp);
//            }
//        }

        //敌方数据

        //防守战模式下无需加成属性，对方建筑也不用获取
//        RtsBattle.RTSBattleInfo battleInfo = getOwner().getCampaignComponent().packBattle(getOwner().getInnerBuildRhComponent().getBaseId(), units, Collections.emptyList(), Collections.emptyMap(), new HashMap<>(), Collections.emptyMap(), new RTSBattleEnemyInfo());
//        getProp().setBattleInfo(battleInfo.toByteString());
        getProp().setBattleMissionId(missionId);

    }

    public ByteString getMissionRtsBattleInfo() {
        return getProp().getBattleInfo();
    }

    public PlayerCampaign.Player_CampaignMissionFinish_S2C endMission(CommonMsg.MissionMapResultInfo result) {
        if (getProp().getBattleMissionId() == 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "defend battle not began");
        }
        PveMissionRhTemplate template = ResHolder.getTemplate(PveMissionRhTemplate.class, getProp().getBattleMissionId());
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "pve mission template is missing");
        }

        List<ItemReward> rewards = new ArrayList<>();
        if (result.getResult() > 0) {
            AssetPackage.Builder assetPackage = AssetPackage.builder();
            for (Integer rewardId : template.getBattleRewardList()) {
                // roll奖励
                List<ItemReward> rwds = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
                //记录奖励
                for (ItemReward reward : rwds) {
                    assetPackage.plusItem(reward.getItemTemplateId(), reward.getCount());
                }
                rewards.addAll(rwds);
            }
            //发奖励
            getOwner().getAssetComponent().give(assetPackage.build(), CommonEnum.Reason.ICR_CITY_PVE);

            completeMission(getProp().getCurMissionId());
        }

        PlayerCampaign.Player_CampaignMissionFinish_S2C.Builder builder = PlayerCampaign.Player_CampaignMissionFinish_S2C.newBuilder();
        builder.setResult(result.getResult());
        for (ItemReward reward : rewards) {
            final ItemDesc itemDesc = new ItemDesc(reward.getItemTemplateId(), reward.getCount());
            builder.getBaseRewardsBuilder().getAssetsBuilder().addDatas(itemDesc.toPb());
        }

        //FIXME 暂时先不清理数据
        //getProp().setBattleInfo(ByteString.EMPTY);
        return builder.build();
    }

    public AssetPackage touchMission(int missionId) {
        PveMissionRhTemplate template = ResHolder.getTemplate(PveMissionRhTemplate.class, missionId);
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "pve mission template is missing");
        }
        //检测关卡类型
        if (template.getMissionType() != CommonEnum.MissionType.MIST_CHEST) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "mission is not a chest");
        }
        //检测前置关卡条件
        for (Integer id : template.getPrevMissionList()) {
            if (!isMissionCompleted(id)) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "pre mission is not completed");
            }
        }
        //禁止重复操作
        if (isMissionCompleted(missionId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "mission is already completed");
        }

        //奖励
        AssetPackage.Builder assetPackage = AssetPackage.builder();
        for (Integer rewardId : template.getCratesRewardList()) {
            // roll奖励
            List<ItemReward> rwds = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
            //记录奖励
            for (ItemReward reward : rwds) {
                assetPackage.plusItem(reward.getItemTemplateId(), reward.getCount());
            }
        }
        //发奖励
        getOwner().getAssetComponent().give(assetPackage.build(), CommonEnum.Reason.ICR_CITY_PVE);

        completeMission(getProp().getCurMissionId());

        return assetPackage.build();
    }

    private void completeMission(int missionId) {
        getProp().addFinishedMissions(missionId);
        //如果是当前的主线关卡，则向后推进
        if (missionId == getProp().getCurMissionId()) {
            //找到下一个关卡
            gotoNextMission();
        }

        new MissionCompleteEvent(getOwner(), missionId).dispatch();
    }

    public void completeMissionGm(int missionId) {
        PveMissionRhTemplate template = ResHolder.findTemplate(PveMissionRhTemplate.class, missionId);
        if (template == null || template.getMainMission() <= 0) {
            return;
        }
        //完成所有关卡
        if (missionId <= 0) {
            while (!isMissionCompleted(getProp().getCurMissionId())) {
                completeMission(getProp().getCurMissionId());
            }
        } else {
            while (!isMissionCompleted(missionId) && !isMissionCompleted(getProp().getCurMissionId())) {
                completeMission(getProp().getCurMissionId());
            }
        }
    }

    private void gotoNextMission() {
        List<Integer> missions = ResHolder.getResService(InnerMapService.class).getPostMissions(getProp().getCurMissionId());
        for (Integer id : missions) {
            PveMissionRhTemplate subTemplate = ResHolder.getTemplate(PveMissionRhTemplate.class, id);
            if (subTemplate == null) {
                continue;
            }
            if (subTemplate.getMainMission() <= 0) {
                continue;
            }
            boolean satisfy = true;
            for (Integer prevId : subTemplate.getPrevMissionList()) {
                if (!isMissionCompleted(prevId)) {
                    satisfy = false;
                    break;
                }
            }
            if (satisfy) {
                getProp().setCurMissionId(id);
                break;
            }
        }
    }

    public boolean isMissionCompleted(int missionId) {
        return getProp().getFinishedMissions().contains(missionId);
    }
}

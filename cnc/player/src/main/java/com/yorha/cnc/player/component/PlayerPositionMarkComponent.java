package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.SsTextFilter;
import com.yorha.proto.Struct;
import res.template.ConstPositionMarkTemplate;
import res.template.PositionMarkClanPicTemplate;
import res.template.PositionMarkPersonalPicTemplate;

import static com.yorha.proto.CommonEnum.*;

/**
 * 坐标收藏/分享组件
 *
 * <AUTHOR>
 */
public class PlayerPositionMarkComponent extends PlayerComponent {

    public PlayerPositionMarkComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 非法直接抛异常
     */
    public void checkAvalid(PositionMarkActionType actionType, PositionMarkType positionMarkType, Struct.PositionMarkInfo positionMarkInfo, PositionMarkShareType shareType) {
        //未知操作拒收
        if (actionType == PositionMarkActionType.PMAT_NONE) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (positionMarkType == PositionMarkType.PMT_NONE) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        //涉及标记名，都先确认长度合法
        if (positionMarkInfo.hasMarkName()) {
            //军团标记名长度限制
            if (positionMarkType == PositionMarkType.PMT_CLAN) {
                if (positionMarkInfo.getMarkName().length() > ResHolder.getInstance().getConstTemplate(ConstPositionMarkTemplate.class).getAlliMarkTextRange()) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
                }
            }
            //个人标记名长度限制
            if (positionMarkType == PositionMarkType.PMT_PERSIONAL) {
                if (positionMarkInfo.getMarkName().length() > ResHolder.getInstance().getConstTemplate(ConstPositionMarkTemplate.class).getPerMarkTextRange()) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
                }
            }
        }
        //新建或修改时需要校验图标id是否合法
        if ((actionType == PositionMarkActionType.PMAT_MARK) || (actionType == PositionMarkActionType.PMAT_EDIT)) {
            if (positionMarkType == PositionMarkType.PMT_CLAN) {
                ResHolder.getInstance().getValueFromMap(PositionMarkClanPicTemplate.class, positionMarkInfo.getMarkPicId());
            }
            if (positionMarkType == PositionMarkType.PMT_PERSIONAL) {
                ResHolder.getInstance().getValueFromMap(PositionMarkPersonalPicTemplate.class, positionMarkInfo.getMarkPicId());
            }
        }
        //坐标分享暂时不做限制
        //军团操作标记需要验证身份
        if (actionType != PositionMarkActionType.PMAT_SHARE && positionMarkType == PositionMarkType.PMT_CLAN) {
            if (!getOwner().getPlayerClanComponent().isInClan()) {
                throw new GeminiException(ErrorCode.MARK_NOT_IN_CLAN);
            }
            // 在player侧检查是否有军团标记权限
            int staffId = getOwner().getProp().getClan().getStaffId();
            ClanPermissionUtils.checkPermission(ClanOperationType.COT_MARK, staffId);
        }
        // 之所以放到最后校验敏感词，是因为这个最费时间
        if (positionMarkInfo.hasMarkName()) {
            UgcSceneId ugcSceneId = positionMarkType == PositionMarkType.PMT_PERSIONAL ? UgcSceneId.USI_POS_MARK : UgcSceneId.USI_CLAN_POS_MARK;
            SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(positionMarkInfo.getMarkName(), ugcSceneId);
            if (!checkTextAns.getIsLegal()) {
                throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
            }
        }
    }

    public void markPosition(PositionMarkActionType actionType, PositionMarkType positionMarkType, Struct.PositionMarkInfo positionMarkInfo) {
        SsScenePlayer.MarkPositionAsk.Builder ask = SsScenePlayer.MarkPositionAsk.newBuilder();
        ask.setPlayerId(getPlayerId())
                .setActionType(actionType)
                .setPositionMarkType(positionMarkType)
                .setPositionMarkInfo(positionMarkInfo);
        getOwner().ownerActor().callCurScene(ask.build());
    }
}

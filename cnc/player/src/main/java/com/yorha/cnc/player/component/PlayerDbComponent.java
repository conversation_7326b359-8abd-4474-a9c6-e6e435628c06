package com.yorha.cnc.player.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.db.tcaplus.result.UpsertResult;
import com.yorha.common.dbactor.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MonitorUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Player;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import static com.yorha.cnc.player.PlayerActor.PLAYER_ENTITY_PLAYER;

/**
 * <AUTHOR>
 */
public class PlayerDbComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerDbComponent.class);

    public PlayerDbComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLogin() {
        getOwner().getProp().getBasicInfo().setLastLoginTsMs(SystemClock.now());
    }

    public void insertSimplePlayerTable() {
        final String openId = getOwner().getOpenId();
        final long playerId = getPlayerId();
        final int zoneId = getOwner().getZoneId();

        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder();
        req.setOpenId(openId);
        req.setPlayerId(playerId);

        CommonMsg.AccountRole.Builder builder = CommonMsg.AccountRole.newBuilder();
        builder.setZoneId(zoneId)
                .setCreateTsMs(SystemClock.now())
                .setLastLogoutTsMs(SystemClock.now())
                .setLoginTsMs(SystemClock.now());
        req.setRoleInfo(builder);
        InsertAsk<TcaplusDb.AccountRoleTable.Builder> insertAsk = new InsertAsk<>(
                req,
                InsertOption.DEFAULT_VALUE
        );

        try {
            InsertResult<TcaplusDb.AccountRoleTable.Builder> insertResult = ownerActor().callGameDb(insertAsk);
            if (!insertResult.isOk()) {
                LOGGER.error("insertSimplePlayerTable fail! {}", insertResult);
            }
        } catch (Exception e) {
            LOGGER.error("insertSimplePlayerTable e fail!", e);
        }
    }

    public void updateSimplePlayerFull() {
        updateSimplePlayerFull(getOwner().getZoneId());
    }

    /**
     * 更新全量的AccountRole玩家信息
     * 调用时机： 登出调用
     */
    void updateSimplePlayerFull(int zoneId) {
        final String openId = getOwner().getOpenId();
        final long playerId = getPlayerId();
        final long logoutTsMs = getOwner().getProp().getBasicInfo().getLastLogoutTsMs();
        final long loginTsMs = getOwner().getProp().getBasicInfo().getLastLoginTsMs();
        final long createTsMs = getOwner().getProp().getCreateTime();

        CommonMsg.AccountRole.Builder builder = CommonMsg.AccountRole.newBuilder();
        builder.setZoneId(zoneId)
                .setCreateTsMs(createTsMs)
                .setLastLogoutTsMs(logoutTsMs)
                .setLoginTsMs(loginTsMs);

        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder();
        req.setPlayerId(playerId).setOpenId(openId).setRoleInfo(builder);

        UpsertAsk<TcaplusDb.AccountRoleTable.Builder> upsertAsk = new UpsertAsk<>(req, UpsertOption.newBuilder().withRetry().build());

        try {
            UpsertResult<TcaplusDb.AccountRoleTable.Builder> result = ownerActor().callGameDb(upsertAsk);
            if (!result.isOk()) {
                LOGGER.error("updateSimplePlayerFull fail! {}", result);
            }
        } catch (Exception e) {
            LOGGER.error("updateSimplePlayerFull e fail!", e);
        }
    }

    // ***********************************增量同步数据库**********************************
    /**
     * 操作增删改查的代理。
     */
    private DbTaskProxy dbTaskProxy;

    /**
     * 创建时候载入增量数据。
     *
     * @param changed 增量数据。
     */
    public void onCreate(Player.PlayerEntity changed) {
        final PlayerChangeAttrStrategy strategy = new PlayerChangeAttrStrategy(changed != null ? changed : Player.PlayerEntity.getDefaultInstance());
        this.dbTaskProxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString())
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .upsert(strategy)
                .delete(strategy)
                .owner(this.getOwner().ownerActor())
                .limitTimerOwner(this.getOwner().ownerActor())
                .entityId(String.valueOf(this.getEntityId()))
                .intervalMs(DbLimitConstants.PLAYER_DB_LIMIT_INTERVAL_MS)
                .build();
    }

    @Override
    public void onDestroy() {

    }

    /**
     * [异步落库]插入玩家数据
     */
    public void insertPlayerTable() {
        this.dbTaskProxy.insert();
    }

    /**
     * [异步落库]玩家数据落脏(玩家未移民，在线时调用)
     */
    public void saveDirty() {
        this.dbTaskProxy.update();
    }

    /**
     * [同步]销毁前落脏
     */
    public void saveOnDestroy() {
        LOGGER.info("PlayerDbComponent saveOnDestroy");
        if (!this.getOwner().isDestroy()) {
            WechatLog.error("PlayerDbComponent endDb {} is not destroy! skip!", this.getOwner());
        }
        // 注册流程未走完，未插入db
        if (this.getOwner().isRegister()) {
            LOGGER.warn("PlayerDbComponent endDb not register done, skip");
            return;
        }
        this.dbTaskProxy.saveDbSync();
        this.dbTaskProxy.stop();
    }

    /**
     * [同步]移民前落库
     */
    public void saveOnMigrate(final boolean stopProxy) {
        if (dbTaskProxy.isProxyStop()) {
            return;
        }
        // 先强制存盘一次
        this.dbTaskProxy.saveDbSync();
        if (stopProxy) {
            this.dbTaskProxy.stop();
        }

    }

    public void saveOfflineView(CommonMsg.PlayerOfflineView offlineView) {
        final long playerId = getPlayerId();
        final int zoneId = getOwner().getZoneId();
        TcaplusDb.PlayerTable.Builder req = TcaplusDb.PlayerTable.newBuilder();
        req.setCurZoneId(zoneId);
        req.setPlayerId(playerId);
        req.setOfflineView(offlineView);

        UpdateAsk<TcaplusDb.PlayerTable.Builder> updateAsk = new UpdateAsk<>(req);

        try {
            UpdateResult<TcaplusDb.PlayerTable.Builder> updateResult = ownerActor().callGameDb(updateAsk);
            if (!updateResult.isOk()) {
                LOGGER.error("update PlayerTable OfflineView fail! {}", updateResult);
            }
        } catch (Exception e) {
            LOGGER.error("update PlayerTable OfflineView e fail!", e);
        }
    }

    private static class PlayerChangeAttrStrategy extends ActorChangeAttrUpsertStrategy<Player.PlayerEntity> implements IDbChangeDeleteStrategy {
        PlayerChangeAttrStrategy(GeneratedMessageV3 msg) {
            super(msg);
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            final PlayerActor playerActor = (PlayerActor) actor;
            final Player.PlayerEntity.Builder builder = (Player.PlayerEntity.Builder) changeAttrSaveDataBuilder;
            return playerActor.getEntity().getProp().copyChangeToDb(builder) > 0;
        }

        @Override
        protected Player.PlayerEntity buildFullAttrSaveData(AbstractActor actor) {
            final PlayerActor playerActor = (PlayerActor) actor;
            return playerActor.getEntity().getProp().getCopyDbBuilder().build();
        }

        @Override
        protected Player.PlayerEntity buildFullAttrSaveData(final UpdateResult<Message.Builder> result) {
            final TcaplusDb.PlayerTable.Builder recordBuilder = (TcaplusDb.PlayerTable.Builder) result.value;
            final PlayerProp attr = PlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
            return attr.getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Player.PlayerEntity fullAttr, @NotNull Player.PlayerEntity changeAttr) {
            final PlayerActor playerActor = (PlayerActor) actor;
            TcaplusDb.PlayerTable.Builder request = TcaplusDb.PlayerTable.newBuilder()
                    .setPlayerId(playerActor.getPlayerId())
                    .setCurZoneId(playerActor.getZoneId());
            if (fullAttr != null) {
                request.setFullAttr(fullAttr);
                long dbSize = fullAttr.getSerializedSize() + changeAttr.getSerializedSize();
                MonitorUnit.DB_PLAYER_FULL_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_PLAYER).inc();
                MonitorUtils.monitorPlayerEntity(actor, fullAttr);
                DbMgrService.getInstance().getDbPerfLogger().onPlayerSaveDb(playerActor.getPlayerId(), dbSize);
            } else {
                MonitorUnit.DB_PLAYER_CHANGE_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_PLAYER).inc();
            }
            request.setChangedAttr(changeAttr);
            return request;
        }

        @Override
        public Message.Builder buildDeleteRequest(AbstractActor actor) {
            throw new GeminiException("try delete {}!", actor);
        }

    }

    @Override
    public String toString() {
        return "PlayerDbComponent{" +
                "playerId=" + this.getPlayerId() +
                '}';
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.TechFullOpenEvent;
import com.yorha.cnc.player.event.task.TechResearchEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 完成xxx科技研究
 * param1: 科技id
 *
 * <AUTHOR>
 */
public class TechFinishedChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(TechResearchEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName(), TechFullOpenEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int times = 1;
        if (event.getPlayer().getTechComponent().isAllUnlock()) {
            prop.setProcess(times);
            return prop.getProcess() >= times;
        }
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        // 遍历所有的科技id，只要有一个科技达到了1级，就表示该任务已经完成（科技之间是或的关系）
        for (int techId : taskParams) {
            int playerTechLevel = entity.getTechComponent().getTechLevelByTechId(techId);
            prop.setProcess(Math.min(times, playerTechLevel));
            if (playerTechLevel >= times) {
                // 已经完成了其中一项科技的研究，不需要再循环了
                break;
            }
        }
        // 若科技有多个等级，等级超过1级就表示 ”完成了对该科技的研究”
        return prop.getProcess() >= times;
    }
}

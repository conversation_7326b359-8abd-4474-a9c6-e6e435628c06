package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerHeroComponent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerHeroStarChangeEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * * 拥有x级x品x星的英雄x个
 * * param1：等级
 * * param2：品质
 * * param3：星级
 * * param4：个数
 * * 计算规则：等级>=param1,品质=param2,星级>=param3
 *
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_HERO_STAR_UP)
public class HeroLevelUpStar extends AbstractActivityTrigger {
    private static final Logger LOGGER = LogManager.getLogger(HeroLevelUpStar.class);
    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerHeroStarChangeEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        final PlayerEntity playerEntity = event.getPlayer();
        final PlayerHeroComponent heroComponent = playerEntity.getHeroComponent();
        if (params == null) {
            return false;
        }
        // 等级和品质全部置为0，不关注配表，只关注星级和个数
        final int level = 0;
        final int quality = 0;
        final int star = params.get(CommonEnum.ActTriggerParamType.ATPT_HERO_STAR);
        final int num = params.get(CommonEnum.ActTriggerParamType.ATPT_HERO_NUM);
        int exist = heroComponent.getPlayerHeroPropTaskProcess(level, quality, star);

        boolean trigger = (exist == num);
        if (trigger) {
            triggerInfoProp.setTriggerTime(triggerInfoProp.getTriggerTime() + 1);
            LOGGER.info("HeroLevelUpStar onTrigger, params={}, exist={}, triggerTime={}", params, exist, triggerInfoProp.getTriggerTime());
        }
        return trigger;
    }
}

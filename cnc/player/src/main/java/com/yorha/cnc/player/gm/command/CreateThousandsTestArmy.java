package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.controller.PlayerSceneController;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.*;
import com.yorha.proto.SsSceneObj.QueryMapBuildingIdAns;
import com.yorha.proto.SsSceneObj.QueryMapBuildingIdAsk;
import res.template.ConstThousandsTestTemplate;
import res.template.ThousandsTestArmyTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CreateThousandsTestArmy implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int num = Integer.parseInt(args.get("num"));
        int type = Integer.parseInt(args.get("type"));
        ConstThousandsTestTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstThousandsTestTemplate.class);
        // 攻击指定建筑
        StructPlayer.CreateArmy_C2S_Param.Builder paramBuilder = StructPlayer.CreateArmy_C2S_Param.newBuilder();
        if (type == 1) {
            // MapTemplateDataItem templateDataItem = ResHolder.getResService(MapSubdivisionDataService.class).getMapTemplateDataItem(1004);
            // RegionalAreaSettingTemplate template = templateDataItem.getValueFromMap(RegionalAreaSettingTemplate.class, constTemplate.getTargetPartId());
            // sendUpdateView(playerEntity, template.getPosX() * 1000, template.getPosY() * 1000);
            // 查询id
            QueryMapBuildingIdAns ans = actor.callBigScene(QueryMapBuildingIdAsk.newBuilder().setPartId(constTemplate.getTargetPartId()).build());
            paramBuilder.getArmyActionBuilder().setArmyActionType(CommonEnum.ArmyActionType.AAT_Battle).setTargetId(ans.getId());
        }
        // 联盟战争  先移动到指定地点
        if (type == 2) {
            List<Integer> battleArea = constTemplate.getBattleArea();
            Circle c = Circle.valueOf(battleArea.get(0), battleArea.get(1), battleArea.get(2));
            sendUpdateView(actor.getEntity(), battleArea.get(0), battleArea.get(1));
            Point point = c.getRandomPoint();
            paramBuilder.getArmyActionBuilder().setArmyActionType(CommonEnum.ArmyActionType.AAT_Move)
                    .getTargetPointBuilder().setX(point.getX()).setY(point.getY());
        }
        paramBuilder.getArmyActionBuilder().setDebugFastMove(true).setIsStayAfterBattle(true);
        Integer id = constTemplate.getArmyConfig().get(num % constTemplate.getArmyConfig().size());
        ThousandsTestArmyTemplate template = ResHolder.getInstance().getValueFromMap(ThousandsTestArmyTemplate.class, id);
        // 构建英雄
        StructPB.HeroPB.newBuilder();
        paramBuilder.getTroopInfoBuilder().setMainHero(Struct.Hero.newBuilder().setHeroId(template.getMainHero()).build());
        if (template.getDeputyHero() != 0) {
            paramBuilder.getTroopInfoBuilder().setDeputyHero(Struct.Hero.newBuilder().setHeroId(template.getDeputyHero()).build());
        }
        PlayerSceneController.rebuildHeroData(actor.getEntity(), paramBuilder);
        // 构建士兵
        for (IntPairType data : template.getSoldierPairList()) {
            Struct.Soldier.Builder soldier = Struct.Soldier.newBuilder();
            soldier.setNum(data.getValue()).setSoldierId(data.getKey());
            paramBuilder.getTroopInfoBuilder().getTroopBuilder().putDatas(data.getKey(), soldier.build());
        }
        // 发送到场景
        SsSceneCityArmy.CreatePlayerArmyAsk.Builder builder = SsSceneCityArmy.CreatePlayerArmyAsk.newBuilder();
        builder.setPlayerId(playerId).setParam(paramBuilder);
        actor.callBigScene(builder.build());
    }

    private void sendUpdateView(PlayerEntity player, int x, int y) {
        SsScenePlayer.UpdatePlayerViewAsk.Builder builder = SsScenePlayer.UpdatePlayerViewAsk.newBuilder();
        int l = 10000;
        int w = 10000;
        builder.setZoneId(player.getZoneId()).setEntityNumMax(200);
        builder.setLayerId(1).setPlayerId(player.getEntityId()).getP1Builder().setX(x - l).setY(y - w);
        builder.getP2Builder().setX(x - l).setY(y + w);
        builder.getP3Builder().setX(x + l).setY(y - w);
        builder.getP4Builder().setX(x + l).setY(y + w);
        player.ownerActor().callBigScene(builder.build());
    }

    @Override
    public String showHelp() {
        return "CreateThousandsTestArmy num={value} type={value}";
    }
}

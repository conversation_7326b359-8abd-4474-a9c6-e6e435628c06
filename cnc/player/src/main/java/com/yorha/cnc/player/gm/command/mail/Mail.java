package com.yorha.cnc.player.gm.command.mail;


import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;

import java.util.Map;

/**
 * 邮件GM
 *
 * <AUTHOR>
 */
public class Mail implements PlayerGmCommand {
    /**
     * 发邮件 Mail opType=send mailType=1 templateId=1001 targetId=10000
     * 读邮件 Mail opType=read tabType=1 mailId=10000
     * 删邮件 Mail opType=del tabType=1 mailId=10000
     */
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String opType = args.get("opType");
        switch (opType) {
            case "send": {
                String mailType = args.get("mailType");
                switch (mailType) {
                    case "1": {
                        int templateId = Integer.parseInt(args.get("templateId"));
                        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
                        builder.setMailTemplateId(templateId);
                        builder.getSenderBuilder().getCardHeadBuilder().setName("I'm Boss.");
                        builder.getTitleBuilder().setTitle("zone test title");
                        builder.getTitleBuilder().setSubTitle("zone test sub title");
                        builder.getContentBuilder().getDisplayDataBuilder().setParams(Struct.DisplayParamList.newBuilder().addDatas(MsgHelper.buildDisPlayText("zone test content")).build());
                        MailUtil.sendZoneMail(actor.getZoneId(), builder.build());
                        break;
                    }
                    case "2": {
                        int templateId = Integer.parseInt(args.get("templateId"));
                        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
                        builder.setMailTemplateId(templateId);
                        builder.getSenderBuilder().getCardHeadBuilder().setName("I'm Boss.");
                        builder.getTitleBuilder().setTitle("clan test title");
                        builder.getTitleBuilder().setSubTitle("clan test sub title");
                        builder.getContentBuilder().getDisplayDataBuilder().setParams(Struct.DisplayParamList.newBuilder().addDatas(MsgHelper.buildDisPlayText("clan test content")).build());
                        final long clanId = Long.parseLong(args.get("targetId"));
                        MailUtil.sendClanMail(actor.getZoneId(), clanId, builder.build());
                        break;
                    }
                    case "3": {
                        break;
                    }
                    default:
                        break;
                }
                break;
            }
            case "del": {
                actor.getEntity().getMailComponent().mailDel(Long.parseLong(args.get("mailId")));
                break;
            }
            default:
                break;
        }
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAIL;
    }

}

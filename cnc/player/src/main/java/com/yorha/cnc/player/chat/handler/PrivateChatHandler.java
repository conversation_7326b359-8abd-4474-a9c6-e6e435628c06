package com.yorha.cnc.player.chat.handler;

import com.yorha.cnc.player.actorservice.PlayerChatServiceImpl;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.FriendPlayerProp;
import com.yorha.proto.CommonEnum.ChatChannel;
import com.yorha.proto.CommonMsg.ChatMessage;
import com.yorha.proto.CommonMsg.ChatSession;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.SsPlayerCard;
import com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns;
import com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstChatTemplate;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

/**
 * 私人聊天
 *
 * <AUTHOR>
 */
public class PrivateChatHandler implements ChatHandler {
    private static final Logger LOGGER = LogManager.getLogger(PrivateChatHandler.class);

    @Override
    public boolean isSyncModel() {
        return false;
    }

    @Override
    public ChatChannel chatChannel() {
        return ChatChannel.CC_PRIVATE;
    }

    @Override
    public long chatRequestSync(ChatPlayerEntity chatPlayerEntity, String channelId, ChatMessage chatMessage) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void chatRequestAsync(ChatPlayerEntity chatPlayerEntity, String channelId, ChatMessage chatMessage, Consumer<Throwable> onComplete) {
        // 私聊需要达到私聊等级
        int cityLevel = chatPlayerEntity.ownerActor().getOrLoadEntity().getCityLevel();
        if (cityLevel < ResHolder.getConsts(ConstChatTemplate.class).getPrivateChatLevel()) {
            throw new GeminiException(ErrorCode.UNREACHED_PRIVATE_CHAT_LIMIT);
        }
        // 私聊chatChannelId的结构为"playerId1&playerId2"，其中id1为两者中较小的那个
        final long[] bothSizeId = ChatHelper.getPrivateChatRelatedPlayerIds(channelId);
        final long myId = chatPlayerEntity.getEntityId();

        if (bothSizeId[0] != myId && bothSizeId[1] != myId) {
            throw new GeminiException(ErrorCode.CHAT_CHAT_CHANNEL_ID_NOT_CORRECT);
        }
        // 取出对方id
        final long targetId = bothSizeId[0] == myId ? bothSizeId[1] : bothSizeId[0];
        // 检查是否屏蔽对方
        FriendPlayerProp friendPlayerProp = chatPlayerEntity.ownerActor().getOrLoadFriendPlayerEntity().getProp();
        if (friendPlayerProp.getShiledList().containsKey(targetId)) {
            throw new GeminiException(ErrorCode.CHAT_YOU_SHIELD_PLAYER);
        }
        // 查询下目标玩家存在性和zoneId
        SsPlayerCard.QueryPlayerCardAns queryAns;
        try {
            queryAns = CardHelper.queryPlayerCardSync(chatPlayerEntity.ownerActor(), targetId);
        } catch (Exception e) {
            throw new GeminiException(ErrorCode.CHAT_PLAYER_NOT_EXIST);
        }
        if (queryAns == null || !queryAns.hasCardInfo()) {
            throw new GeminiException(ErrorCode.CHAT_PLAYER_NOT_EXIST);
        }
        // ask对方收消息了
        ReceivePrivateMsgAsk.Builder receiveMsg = ReceivePrivateMsgAsk.newBuilder();
        receiveMsg.setChannelId(channelId).setChatMessage(chatMessage);
        chatPlayerEntity.ownerActor().<ReceivePrivateMsgAns>askPlayer(queryAns.getCardInfo().getZoneId(), targetId, receiveMsg.build()).onComplete(
                (res, err) -> {
                    if (err != null) {
                        if (onComplete != null) {
                            if (err instanceof MigrateException) {
                                onComplete.accept(new GeminiException(ErrorCode.PLAYER_MIGRATE_TRY_LATER));
                            } else {
                                onComplete.accept(err);
                            }
                        } else {
                            LOGGER.warn("chatRequestAsync failed {} {}", chatPlayerEntity, channelId, err);
                        }
                        return;
                    }

                    // 保持原意 先改已读  结束回调
                    ChatSession chatSession = ChatSession.newBuilder().setChannelType(chatChannel()).setChatChannelId(channelId).build();

                    // 给自己发一条
                    PlayerChatServiceImpl.handleNewMsg(chatPlayerEntity.ownerActor(), res.getChatMessage(), ChatChannel.CC_PRIVATE, channelId, res.getChatChannelCreateTsMs());

                    // 阅读消息
                    chatPlayerEntity.getHandleChatComponent().readMessage(chatSession, res.getChatMessage().getMessageId());

                    // 触发回调
                    onComplete.accept(null);
                }
        );
    }

    @Override
    public List<ChatMessage> queryChatMsgList(ChatPlayerEntity chatPlayerEntity, String channelId, Collection<Long> shieldList, PlayerChat.Player_GetChatMessages_C2S msg) {
        long queryStartIndex = chatPlayerEntity.getHandleChatComponent().getQueryStartIndex(ChatChannel.CC_PRIVATE, channelId);
        long toId = Math.max(queryStartIndex, msg.getToId());
        long fromId = Math.max(msg.getFromId(), toId);
        if (msg.getIsLogin()) {
            fromId = 0;
        }
        // 设置游标
        if (fromId == 0) {
            fromId = ChatHelper.getChatMaxIndex(chatPlayerEntity.ownerActor(), chatChannel().getNumber(), channelId);
        }
        return ChatHelper.playerQueryChatMsgOnCacheMsg(chatPlayerEntity.ownerActor(), null, msg.getChatSession(), fromId, toId, shieldList, queryStartIndex);
    }

    @Override
    public long getCd(ChatPlayerEntity chatPlayerEntity) {
        return 0;
    }
}

package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class MarkOneGuidance implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int guidanceId = 0;
        if (args.containsKey("guidanceId")) {
            guidanceId = Integer.parseInt(args.get("guidanceId"));
        }

        if (guidanceId == 0) {
            throw new GeminiException(ErrorCode.GUIDANCE_ID_WRONG);
        }

        boolean isMark = true;
        if (args.containsKey("isMark")) {
            isMark = Integer.parseInt(args.get("isOpen")) == 1;
        }
        if (isMark) {
            actor.getEntity().getGuidanceComponent().gmRecordFinishedGuidance(guidanceId);
        } else {
            actor.getEntity().getGuidanceComponent().gmResetGuidance(guidanceId);
        }
    }

    @Override
    public String showHelp() {
        return "MarkOneGuidance guidanceId={value} isMark={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

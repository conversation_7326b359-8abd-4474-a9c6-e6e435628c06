package com.yorha.cnc.player.gm.command.rh;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.InnerBuildRhTemplate;

import java.util.Map;

/**
 * RH解锁建筑
 */
public class UnLockInnerBuild implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int type = Integer.parseInt(args.get("type"));
        int count = Integer.parseInt(args.get("count"));
        CommonEnum.CityBuildType innerBuildTypeRH = CommonEnum.CityBuildType.forNumber(type);
        if (innerBuildTypeRH == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_TYPE.getCodeId());
        }
        InnerBuildRhTemplate buildRhTemplate = ResHolder.getInstance().findValueFromMap(InnerBuildRhTemplate.class, type);
        if (buildRhTemplate == null) {
            throw new GeminiException(ErrorCode.BUILD_BUILD_NO_INFOR.getCodeId());
        }

        if (count > 0) {
            actor.getEntity().getInnerBuildRhComponent().unLockInnerBuildRhGM(type, count, null, null);
        }
    }

    @Override
    public String showHelp() {
        return "UnLockInnerBuild type={value} count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_INNER_BUILD;
    }
}

package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.component.PlayerQueueTaskComponent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.PlayerQueueUnlockEvent;
import com.yorha.cnc.player.event.task.PlayerLoginEvent;
import com.yorha.cnc.player.goods.ActivityNormalGoods;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import res.template.ChargeGoodsTemplate;
import res.template.ConstActivityTemplate;

import java.util.List;

/**
 * 双队列礼包活动
 */
public class PlayerSecondQueueUnit extends BasePlayerActivityUnit implements PlayerEventListener, ActivityNormalGoods.ActUnitGoodsHandler {

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerQueueUnlockEvent.class,
            PlayerLoginEvent.class
    );

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_SECOND_QUEUE_GOODS, (owner, prop, template) ->
                new PlayerSecondQueueUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerSecondQueueUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
    }

    private boolean isSecondQueueForeverUnlocked() {
        PlayerQueueTaskComponent queueComponent = player().getPlayerQueueTaskComponent();
        return queueComponent.isSecondBuildQueueForeverUnlocked();
    }

    @Override
    public boolean isFinished() {
        return isSecondQueueForeverUnlocked();
    }

    @Override
    public void forceOffImpl() {
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (isSecondQueueForeverUnlocked()) {
            onUnitFinished();
        }
    }

    public void handleBuySecondQueue() {
        if (isSecondQueueForeverUnlocked()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "already unlocked");
        }
        ConstActivityTemplate consts = ResHolder.getConsts(ConstActivityTemplate.class);
        final int costDiamond = consts.getSecondQueueDiamond();
        ErrorCode enoughCode = player().getPurseComponent().isEnough(CommonEnum.CurrencyType.DIAMOND, costDiamond);
        if (enoughCode.isNotOk()) {
            throw new GeminiException(enoughCode);
        }

        player().getPurseComponent().consume(CommonEnum.CurrencyType.DIAMOND, costDiamond, CommonEnum.Reason.ICR_SECOND_QUEUE_UNLOCK_FOREVER, "direct");
        player().getPlayerQueueTaskComponent().unlockSecondBuildQueueForever();
    }

    @Override
    public void checkApply(ChargeGoodsTemplate goodsTemplate) {
        ConstActivityTemplate consts = ResHolder.getConsts(ConstActivityTemplate.class);
        if (goodsTemplate.getId() != consts.getSecondQueueGoodsId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (isSecondQueueForeverUnlocked()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "already unlocked");
        }
    }

    @Override
    public void onGoodsBought(int goodsId) {
        // 不用处理，礼包发放自动使用的队列道具，道具使用完毕自然会结束unit
    }
}

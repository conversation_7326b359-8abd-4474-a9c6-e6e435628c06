package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.component.PlayerAchievementComponent;
import com.yorha.cnc.player.component.PlayerStatisticComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.achievement.AchievementHelper;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum.DebugGroup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.DemandConfigTemplate;

import java.util.Map;

/**
 * 加玩家成就需求进度
 *
 * <AUTHOR>
 */
public class AddDemandProcess implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(AddDemandProcess.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        final PlayerStatisticComponent statisticComponent = actor.getEntity().getStatisticComponent();
        final PlayerAchievementComponent achievementComponent = actor.getEntity().getAchievementComponent();
        final int demandId = Integer.parseInt(args.get("demandId"));
        final int value = Integer.parseInt(args.get("value"));
        final Map<Integer, DemandConfigTemplate> demandConfigTemplateMap = ResHolder.getInstance().getMap(DemandConfigTemplate.class);
        final DemandConfigTemplate demandConfigTemplate = demandConfigTemplateMap.get(demandId);
        if (demandConfigTemplate == null) {
            throw new GeminiException("不存在的需求id");
        }
        final StatisticEnum statisticEnum = AchievementHelper.checkType2StatisticEnum(demandConfigTemplate.getCheckType());
        if (statisticEnum == null) {
            throw new GeminiException("这个需求暂时不支持用这个gm, 1");
        }
        if (!AchievementHelper.canRecord(statisticEnum)) {
            throw new GeminiException("这个需求暂时不支持用这个gm, 2");
        }
        long oldValue = 0;
        long newValue = 0;
        if (statisticEnum.isSingleStatistic()) {
            oldValue = statisticComponent.getSingleStatistic(statisticEnum);
            statisticComponent.recordSingleStatistic(statisticEnum, value);
            newValue = statisticComponent.getSingleStatistic(statisticEnum);
            achievementComponent.onTrigger(demandConfigTemplate.getCheckType(), oldValue, newValue);
        }
        if (statisticEnum.isSecondStatistic()) {
            final int id = demandConfigTemplate.getParamList().get(0);
            oldValue = statisticComponent.getSecondStatistic(statisticEnum, id);
            statisticComponent.recordSecondStatistic(statisticEnum, id, value);
            newValue = statisticComponent.getSecondStatistic(statisticEnum, id);
            achievementComponent.onTrigger(demandConfigTemplate.getCheckType(), id, oldValue, newValue);
        }
        LOGGER.info("AddKvkDemandProcess demandId={} value={}", demandId, value);
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "CompleteAchievement demandId={value} value={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.gm.command.buff;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.constant.DevBuffConstants;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import com.yorha.proto.CommonEnum.DebugGroup;
import res.template.BuffTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AddBuff implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int buffId = Integer.parseInt(args.get("buffId"));
        BuffTemplate buffTemplate = DevBuffMgrBase.getBuffTemplate(buffId);
        if (buffTemplate != null) {
            Long endTime = null;
            if (args.get("seconds") != null) {
                endTime = SystemClock.now() + Long.parseLong(args.get("seconds")) * 1000;
            }

            if (buffTemplate.getType() == BuffEffectType.ET_PEACE_SHIELD_VALUE) {
                actor.getEntity().getPeaceShieldComponent().openPeaceShield(buffId, endTime, CommonEnum.DevBuffSourceType.DBST_GM, DevBuffConstants.PeaceShieldReason.SYS);
            } else {
                actor.getEntity().getPlayerDevBuffComponent().addDevBuff(buffId, endTime, CommonEnum.DevBuffSourceType.DBST_GM);
            }
        }
    }

    @Override
    public String showHelp() {
        return "AddBuff buffId={value} seconds={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ClanStoreInterfaceType;
import com.yorha.proto.CommonEnum.ClanStoreOperateReturnType;
import com.yorha.proto.CommonEnum.Reason;
import com.yorha.proto.SsClanStore;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanRecycleTemplate;
import res.template.ClanStoreTemplate;

/**
 * <AUTHOR>
 */
public class PlayerClanStoreComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanStoreComponent.class);

    public PlayerClanStoreComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 军团商店回收入口
     *
     * @param itemId  道具id
     * @param itemNum 道具数量
     * @return 返回操作的枚举类型
     */
    public ClanStoreOperateReturnType clanStoreRecycle(int itemId, int itemNum) {
        ClanRecycleTemplate template = ResHolder.getInstance().getValueFromMap(ClanRecycleTemplate.class, itemId);
        if (null == template) {
            LOGGER.warn("item {} is not exist in recycle", itemId);
            return ClanStoreOperateReturnType.CSORT_NO_ITEM_CONFIG;
        }
        // 道具检测
        AssetPackage consume = AssetPackage.builder().plusItem(itemId, itemNum).build();
        getOwner().verifyThrow(consume);
        // 扣除道具
        getOwner().consume(consume, Reason.ICR_RECYCLE, "guild_shop");
        // 增加积分
        getOwner().getPlayerClanComponent().addTotalClanScore(CommonEnum.ClanScoreCategory.CSC_RECYCLE, (long) template.getScore() * itemNum);
        return ClanStoreOperateReturnType.CSORT_SUCCESS;
    }

    /**
     * 军团商店购买或进货入口
     *
     * @param interfaceType 界面类型
     * @param itemId        道具id
     * @param itemNum       道具数量
     */
    public ClanStoreOperateReturnType clanStoreBuyOrStock(ClanStoreInterfaceType interfaceType, int itemId, int itemNum) {
        ClanStoreTemplate template = ResHolder.getInstance().getValueFromMap(ClanStoreTemplate.class, itemId);
        if (interfaceType == ClanStoreInterfaceType.CSRT_BUY) {
            if (!getOwner().getPlayerClanComponent().checkTotalClanScore(template.getPrice() * itemNum)) {
                return ClanStoreOperateReturnType.CSORT_NO_ENOUGH_SCORE;
            }
        }
        SsClanStore.OperateClanStoreItemAsk.Builder ask = SsClanStore.OperateClanStoreItemAsk.newBuilder();
        ask.setInterfaceType(interfaceType);
        ask.setPlayerId(getPlayerId());
        ask.setItemId(itemId);
        ask.setItemNum(itemNum);
        SsClanStore.OperateClanStoreItemAns ans = getOwner().ownerActor().callCurClan(ask.build());
        if (ans.getReturnType() == ClanStoreOperateReturnType.CSORT_SUCCESS && interfaceType == ClanStoreInterfaceType.CSRT_BUY) {
            // 扣除积分
            getOwner().getPlayerClanComponent().decTotalClanScore(CommonEnum.ClanScoreCategory.CSC_SHOP, template.getPrice() * itemNum);
            // 道具构建
            AssetPackage assertPackage = AssetPackage.builder().plusItem(itemId, itemNum).build();
            // 增加道具
            getOwner().getAssetComponent().give(assertPackage, Reason.ICR_SHOPPING, "guild_shop");
        }
        return ans.getReturnType();
    }
}

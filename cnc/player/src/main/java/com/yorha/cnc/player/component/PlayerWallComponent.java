package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.Pair;
import com.yorha.game.gen.prop.PlayerWallModelProp;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 飞机相关组件
 *
 * <AUTHOR>
 */
public class PlayerWallComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerWallComponent.class);

    public PlayerWallComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerWallModelProp getProp() {
        return getOwner().getProp().getCityModel().getPlayerWallModel();
    }


    /**
     * 设置驻防英雄，机甲
     */
    public void setHeroMechaHandler(PlayerCommon.Player_SetWallGarrison_C2S msg) {
        int mainHeroId = msg.getMainHeroId();
        int deputyHeroId = msg.getDeputyHeroId();
        if (mainHeroId < 0 || deputyHeroId < 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (mainHeroId == 0 && deputyHeroId != 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (mainHeroId != 0) {
            getOwner().getHeroComponent().checkHeroProp(mainHeroId);
        }
        if (deputyHeroId != 0) {
            getOwner().getHeroComponent().checkHeroProp(deputyHeroId);
        }
        // 副本校验
        if (getOwner().isInDungeon()) {
            LOGGER.info("setHeroMechaHandler player is in Dungeon");

        } else {
            PlayerWallModelProp prop = getProp();
            // 现在客户端会全量传数据过来，所以需要全量检
            if (prop.getSetUpMainHeroId() == mainHeroId && prop.getSetUpDeputyHeroId() == deputyHeroId) {
                // 选中单位已驻防
                throw new GeminiException(ErrorCode.HERO_SELECTEDUNITDENFENSED);
            }
            trySetUpHeroOrMecha(mainHeroId, deputyHeroId);
        }


    }

    /**
     * 选出显示的主副将
     */
    private Pair<Integer, Integer> chooseShowGarrisonHero() {
        // 优先选玩家指定的驻防英雄
        List<Integer> priority = new ArrayList<>();
        priority.add(getProp().getSetUpMainHeroId());
        priority.add(getProp().getSetUpDeputyHeroId());
        Set<Integer> except = new HashSet<>();
        // 选主将
        int mainHeroId = getOwner().getHeroComponent().chooseGarrisonHero(priority, except);
        int deputyHeroId = 0;
        // 选副将
        if (mainHeroId != 0) {
            except.add(mainHeroId);
            deputyHeroId = getOwner().getHeroComponent().chooseGarrisonHero(priority, except);
        }
        return Pair.of(mainHeroId, deputyHeroId);
    }

    /**
     * 检测是否需要自动设置英雄（未设置过时）
     */
    public void checkOrSetDefaultHero(int heroId) {
        if (getProp().getSetUpMainHeroId() == 0) {
            trySetUpHeroOrMecha(heroId, 0);
            return;
        }
        if (getProp().getSetUpDeputyHeroId() == 0) {
            trySetUpHeroOrMecha(0, heroId);
            return;
        }
    }


    /**
     * 尝试设置英雄、机甲驻防逻辑
     */
    public void trySetUpHeroOrMecha(int mainHeroId, int deputyHeroId) {
        PlayerWallModelProp prop = getProp();
        if (mainHeroId > 0) {
            prop.setSetUpMainHeroId(mainHeroId);
        }
        if (deputyHeroId > 0) {
            prop.setSetUpDeputyHeroId(deputyHeroId);
        }
        garrisonHeroOrMecha();
    }

    /**
     * 选择最优的英雄和飞机
     * 第一优先：玩家设置
     * 第二优先：战力
     */
    public void garrisonHeroOrMecha() {
        PlayerWallModelProp prop = getProp();
        Pair<Integer, Integer> heroPair = chooseShowGarrisonHero();

        // 获取最优的驻防主副将
        final int mainHeroId = heroPair.getFirst();
        final int deputyHeroId = heroPair.getSecond();

        boolean needSync = false;


        if (prop.getUseMainHeroId() != mainHeroId) {
            prop.setUseMainHeroId(mainHeroId);
            needSync = true;
        }
        if (prop.getUseDeputyHeroId() != deputyHeroId) {
            prop.setUseDeputyHeroId(deputyHeroId);
            needSync = true;
        }

        if (needSync) {
            syncScenePlayerWall();
        }


    }


    /**
     * 同步玩家驻防信息
     */
    private void syncScenePlayerWall() {
        PlayerWallModelProp prop = getProp();
        SsScenePlayer.SyncPlayerWallHeroPlaneCmd.Builder callCmd = SsScenePlayer.SyncPlayerWallHeroPlaneCmd.newBuilder().setPlayerId(getPlayerId());
        if (prop.getUseMainHeroId() > 0) {
            Struct.Hero mainHero = getOwner().getHeroComponent().getHero(prop.getUseMainHeroId());
            callCmd.setMainHero(mainHero);
        } else {
            callCmd.setMainHero(Struct.Hero.getDefaultInstance());
        }

        if (prop.getUseDeputyHeroId() > 0) {
            Struct.Hero deputyHero = getOwner().getHeroComponent().getHero(prop.getUseDeputyHeroId());
            callCmd.setDeputyHero(deputyHero);
        } else {
            callCmd.setDeputyHero(Struct.Hero.getDefaultInstance());
        }
        LOGGER.info("syncScenePlayerWall, mainId:{} deputyId:{}", prop.getUseMainHeroId(), prop.getUseDeputyHeroId());
        ownerActor().tellBigScene(callCmd.build());
    }

    public boolean isWallHero(int heroId) {
        if (heroId <= 0) {
            LOGGER.error("why hero id <= 0");
            return false;
        }
        if (getProp().getUseMainHeroId() == heroId) {
            return true;
        }
        return getProp().getUseDeputyHeroId() == heroId;
    }

}

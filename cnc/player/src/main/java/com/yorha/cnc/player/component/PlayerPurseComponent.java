package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerConsumeCurrencyEvent;
import com.yorha.common.asset.AssetDesc;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.CurrencyDesc;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.qlog.json.player.CurrencyInfo;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 货币钱包
 */
public class PlayerPurseComponent extends PlayerComponent {

    private static final Logger LOGGER = LogManager.getLogger(PlayerPurseComponent.class);

    private static final EnumMap<CurrencyType, ErrorCode> CURRENCY_ERROR_CODE_MAP = new EnumMap<>(CurrencyType.class);

    static {
        CURRENCY_ERROR_CODE_MAP.put(CurrencyType.OIL, ErrorCode.CURRENCY_OIL_NOT_ENOUGH);
        CURRENCY_ERROR_CODE_MAP.put(CurrencyType.STEEL, ErrorCode.CURRENCY_TUNGSTEN_NOT_ENOUGH);
        CURRENCY_ERROR_CODE_MAP.put(CurrencyType.RARE_EARTH, ErrorCode.CURRENCY_RARE_EARTH_NOT_ENOUGH);
        CURRENCY_ERROR_CODE_MAP.put(CurrencyType.TIBERIUM, ErrorCode.CURRENCY_TIBERIUM_NOT_ENOUGH);
        CURRENCY_ERROR_CODE_MAP.put(CurrencyType.DIAMOND, ErrorCode.CURRENCY_DIAMOND_NOT_ENOUGH);
        CURRENCY_ERROR_CODE_MAP.put(CurrencyType.SKYNET, ErrorCode.CURRENCY_EXPEDITION_NOT_ENOUGH);
    }

    public PlayerPurseComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        for (CurrencyType currencyType : CurrencyType.values()) {
            CurrencyProp currencyProp = getOwner().getProp().getPurse().get(currencyType.getNumber());
            if (currencyProp == null) {
                getOwner().getProp().getPurse().addEmptyValue(currencyType.getNumber());
            }
        }
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            List<IntPairType> initialCurrency = ResHolder.getResService(ConstKVResService.class).getTemplate().getInitialCurrency();
            if (CollectionUtils.isNotEmpty(initialCurrency)) {
                initialCurrency.forEach(currency -> {
                    CurrencyType currencyType = CurrencyType.forNumber(currency.getKey());
                    if (currencyType == null) {
                        LOGGER.error("{} init currency {} error", getOwner(), currency.getKey());
                        return;
                    }
                    give(currencyType, currency.getValue(), CommonEnum.Reason.ICR_INIT, "");
                });
            }
        }
    }

    public long getCurrencyAmount(CurrencyType type) {
        CurrencyProp currencyProp = getOwner().getProp().getPurse().get(type.getNumber());
        if (currencyProp == null) {
            return 0L;
        }
        return currencyProp.getCount();
    }

    public long give(CurrencyType currencyType, long value, CommonEnum.Reason reason, String subReason) {
        if (value <= 0) {
            throw new GeminiException("Give currency must > 0, {}={}", currencyType, value);
        }
        CurrencyProp currencyProp = getOwner().getProp().getPurse().get(currencyType.getNumber());
        long before = currencyProp.getCount();
        long limit = Long.MAX_VALUE - currencyProp.getCount();
        long count = Math.min(limit, value);

        if (currencyType == CurrencyType.DIAMOND) {
            getOwner().getPaymentComponent().asyncPresent(count, ans -> {
                LOGGER.info("player {} add currency {} value {},before {},after {}", getOwner(), currencyType.name(), value, before, currencyProp.getCount());
                getOwner().getQlogComponent().sendMoneyQLog(currencyType.getNumber(), before, currencyProp.getCount(), value, true, reason, subReason);
            });
        } else {
            currencyProp.setCount(currencyProp.getCount() + count);
            LOGGER.info("player {} add currency {} value {},before {},after {}", getOwner(), currencyType.name(), value, before, currencyProp.getCount());
            // 通过资源类型区分货币QLog和资源QLog(权限开放原因)
            if (needMoneyWithType(currencyType)) {
                getOwner().getQlogComponent().sendMoneyQLog(currencyType.getNumber(), before, currencyProp.getCount(), value, true, reason, subReason);
            } else {
                getOwner().getQlogComponent().sendResQLog(currencyType.getNumber(), before, currencyProp.getCount(), value, true, reason, subReason);
            }
        }
        return count;
    }

    private boolean needMoneyWithType(CurrencyType currencyType) {
        if (currencyType == CurrencyType.DIAMOND) {
            return true;
        }
        if (currencyType == CurrencyType.SKYNET) {
            return true;
        }
        return false;
    }

    public void consume(CurrencyType currencyType, long count, CommonEnum.Reason reason, String subReason) {
        if (count <= 0) {
            throw new GeminiException("Consume currency must >0, {}={}", currencyType, count);
        }
        ErrorCode code = isEnough(currencyType, count);
        if (!code.isOk()) {
            throw new GeminiException(code);
        }
        CurrencyProp currencyProp = getOwner().getProp().getPurse().get(currencyType.getNumber());
        final long before = currencyProp.getCount();
        if (currencyType == CurrencyType.DIAMOND) {
            getOwner().getPaymentComponent().syncConsume(count);
        } else {
            currencyProp.setCount(before - count);
        }
        // 通过资源类型区分货币QLog和资源QLog(权限开放原因)
        final long after = currencyProp.getCount();
        if (needMoneyWithType(currencyType)) {
            getOwner().getQlogComponent().sendMoneyQLog(currencyType.getNumber(), before, after, count, false, reason, subReason);
        } else {
            getOwner().getQlogComponent().sendResQLog(currencyType.getNumber(), before, after, count, false, reason, subReason);
        }
        LOGGER.info("player {} cost currency {} num {},before {},after {}", getOwner(), currencyType, count, before, after);

        new PlayerConsumeCurrencyEvent(getOwner(), currencyType, count).dispatch();
    }

    public ErrorCode isEnoughAll(AssetPackage assetPackage) {
        for (AssetDesc asset : assetPackage.getImmutableAssets()) {
            if (asset instanceof CurrencyDesc) {
                CurrencyDesc cd = (CurrencyDesc) asset;
                ErrorCode code = isEnough(cd.getCurrencyType(), cd.getAmount());
                if (code.isNotOk()) {
                    return code;
                }
            }
        }
        return ErrorCode.OK;
    }

    public ErrorCode isEnough(int currencyType, long num) {
        CurrencyType type = CurrencyType.forNumber(currencyType);
        if (type == null) {
            throw new GeminiException("currencyType not recognized. {}", currencyType);
        }
        return isEnough(type, num);
    }

    public ErrorCode isEnough(CurrencyType currencyType, long num) {
        CurrencyProp currencyProp = getOwner().getProp().getPurse().get(currencyType.getNumber());
        if (currencyProp.getCount() >= num) {
            return ErrorCode.OK;
        }
        return CURRENCY_ERROR_CODE_MAP.get(currencyType);
    }

    /**
     * 清空某个资源
     */
    public void debugClear(CurrencyType currencyType) {
        long count = this.count(currencyType);
        if (count > 0) {
            this.consume(currencyType, count, CommonEnum.Reason.ICR_GM, "");
        }
    }

    public long count(CurrencyType currencyType) {
        return getOwner().getProp().getPurse().get(currencyType.getNumber()).getCount();
    }

    public long count(int currencyTypeNumber) {
        return getOwner().getProp().getPurse().get(currencyTypeNumber).getCount();
    }

    public String get4CurrencyQlogInfo() {
        List<CurrencyInfo> currencyInfos = new ArrayList<>();
        for (CurrencyType type : CurrencyType.values()) {
            if (type == CurrencyType.DIAMOND) {
                continue;
            }
            CurrencyInfo info = new CurrencyInfo();
            info.setResId(type.getNumber());
            info.setIcount(getCurrencyAmount(type));
            currencyInfos.add(info);
        }
        return JsonUtils.toJsonString(currencyInfos);
    }
}

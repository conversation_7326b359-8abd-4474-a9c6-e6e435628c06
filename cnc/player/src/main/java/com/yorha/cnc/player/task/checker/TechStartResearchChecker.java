package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.TechFullOpenEvent;
import com.yorha.cnc.player.event.task.TechStartResearchEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.START_LEVEL_UP_TECH_NUM;

/**
 * 开始技术研究
 * 参数1：科技id，0代表任意
 * 参数2：配置代表数值
 *
 * <AUTHOR>
 */
public class TechStartResearch<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            TechStartResearchEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            TechFullOpenEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer techId = taskParams.get(0);
        Integer value = taskParams.get(1);
        if (event.getPlayer().getTechComponent().isAllUnlock()) {
            prop.setProcess(value);
            return true;
        }
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                long sum;
                if (techId == 0) {
                    sum = event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(START_LEVEL_UP_TECH_NUM);
                } else {
                    sum = event.getPlayer().getStatisticComponent().getSecondStatistic(START_LEVEL_UP_TECH_NUM, techId);
                }
                prop.setProcess((int) Math.min(value, sum));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof TechStartResearchEvent) {
                    TechStartResearchEvent researchEvent = (TechStartResearchEvent) event;
                    if (techId != 0 && techId != researchEvent.getTechId()) {
                        break;
                    }
                    long l = prop.getProcess() + researchEvent.getCount();
                    prop.setProcess(Math.min(value, (int) l));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= 1;
    }
}

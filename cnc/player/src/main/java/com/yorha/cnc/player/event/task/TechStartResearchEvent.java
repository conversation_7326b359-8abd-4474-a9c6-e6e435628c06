package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 开始研究科技事件
 *
 * <AUTHOR>
 */
public class TechStartResearchEvent extends PlayerTaskEvent {

    private int techId;
    private int count;

    public TechStartResearchEvent(PlayerEntity entity, int techGroupId, int count) {
        super(entity);
        techId = techGroupId;
        this.count = count;
    }

    public int getTechId() {
        return techId;
    }

    public int getCount() {
        return count;
    }
}

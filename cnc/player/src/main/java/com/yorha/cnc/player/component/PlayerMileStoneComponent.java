package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.cnc.player.event.task.PlayerKillSoldierEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.milestone.MileStoneTemplateService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerMileStoneInfoProp;
import com.yorha.game.gen.prop.PlayerMileStoneModelProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.MilestoneChapterTemplate;
import res.template.MilestoneTaskTypeTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个人里程碑组件
 *
 * <AUTHOR>
 */
public class PlayerMileStoneComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerMileStoneComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerKillBigSceneMonsterEvent.class, PlayerMileStoneComponent::monitorMonsterKillEvent);
        EntityEventHandlerHolder.register(BuildFinEvent.class, PlayerMileStoneComponent::monitorBuildingLevelChangeEvent);
        EntityEventHandlerHolder.register(PlayerKillSoldierEvent.class, PlayerMileStoneComponent::monitorKillSoldierEvent);
    }

    private static void monitorKillSoldierEvent(PlayerKillSoldierEvent event) {
        event.getPlayer().getMileStoneComponent().updateScore(CommonEnum.MileStoneTaskType.MST_PLAYER_KILL_OTHER_SOLDIER, event);
    }

    private static void monitorMonsterKillEvent(PlayerKillBigSceneMonsterEvent event) {
        event.getPlayer().getMileStoneComponent().updateScore(CommonEnum.MileStoneTaskType.MST_KILL_REBEL_NUM, event);
        event.getPlayer().getMileStoneComponent().updateScore(CommonEnum.MileStoneTaskType.MST_PLAYER_KILL_MONSTER, event);
    }

    private static void monitorBuildingLevelChangeEvent(BuildFinEvent event) {
        event.getPlayer().getMileStoneComponent().updateScore(CommonEnum.MileStoneTaskType.MST_PLAYER_BUILD, event);
    }

    public PlayerMileStoneComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerMileStoneModelProp getProp() {
        return getOwner().getProp().getMileStoneModel();
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        try {
            onLoginSync(ans.getZoneMilestoneInfo());
        } catch (Exception e) {
            WechatLog.error("mileStone module post login error, ", e);
        }
        try {
            // 原服里程碑线上fix检测
            fixCheck();
        } catch (Exception e) {
            LOGGER.error("mileStone module post login error", e);
        }
    }

    private void fixCheck() {
        // fix start
    }

    /**
     * 登录时同步里程碑数据 三种case
     * 1、人在K，登录同步个人K里程碑数据
     * 2、人在原服，K服开了，登录同步原服数据，额外同步个人K里程碑数据
     * 3、人在原服，K服未开，登录同步原服数据
     */
    private void onLoginSync(StructMsg.ZoneMilestoneInfoByLogin zoneMilestoneInfo) {
        LOGGER.info("PlayerMileStoneComponent onLoginSync, curMilestoneId={} curMileStoneEndTsMs={}", zoneMilestoneInfo.getCurMileStoneId(), zoneMilestoneInfo.getCurMileStoneEndTsMs());
        PlayerMileStoneModelProp prop = getProp();
        if (zoneMilestoneInfo.getCurMileStoneId() >= 0 && prop.getCurMileStoneId() > zoneMilestoneInfo.getCurMileStoneId()) {
            prop.getMileStoneInfo().clear();
            updateCurMileStone(prop, 0, 0);
            LOGGER.warn("player milestone data, curMilestone={} sceneCurMilestone={}", prop.getCurMileStoneId(), zoneMilestoneInfo.getCurMileStoneId());
            return;
        }
        // 第一次登录且登出时间无值就认为是新号
        boolean isNewAccount = getOwner().getProp().getBasicInfo().getLastLogoutTsMs() == 0;
        updateMileStoneData(zoneMilestoneInfo.getCurMileStoneId(), zoneMilestoneInfo.getCurMileStoneEndTsMs(), zoneMilestoneInfo.getPlayerRewardDataList(), isNewAccount, zoneMilestoneInfo.getCurMileStoneTemplateId(), "login");
    }

    /**
     * 获取里程碑基础信息
     */
    public SsScenePlayer.GetMileStoneHistoryAns getBaseInfoHandler() {
        SsScenePlayer.GetMileStoneHistoryAsk.Builder askBuilder = SsScenePlayer.GetMileStoneHistoryAsk.newBuilder();
        return ownerActor().callBigScene(askBuilder.build());
    }

    /**
     * 获取里程碑排行信息
     */
    public SsScenePlayer.GetMileStoneRankInfoAns getRankInfoHandler(int mileStoneId) {
        SsScenePlayer.GetMileStoneRankInfoAsk ask = SsScenePlayer.GetMileStoneRankInfoAsk.newBuilder().setMileStoneId(mileStoneId).build();
        return ownerActor().callBigScene(ask);
    }

    /**
     * 获取里程碑奖励
     */
    public AssetPackage takeRewardHandler(int mileStoneId) {
        if (mileStoneId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        PlayerMileStoneInfoProp prop = getProp().getMileStoneInfoV(mileStoneId);

        if (prop == null) {
            throw new GeminiException(ErrorCode.MILESTONE_IS_NULL);
        }
        if (prop.getRewardStatus() == CommonEnum.MileStoneRewardStatus.MSR_AWARDED) {
            throw new GeminiException(ErrorCode.MILESTONE_AWARDED);
        }
        if (!isCanReward(prop.getRewardStatus())) {
            throw new GeminiException(ErrorCode.MILESTONE_CANT_TAKE_REWARD);
        }

        prop.setRewardStatus(CommonEnum.MileStoneRewardStatus.MSR_AWARDED);
        int rewardLevel = prop.getRewardLevel();
        int rewardId = ResHolder.getResService(MileStoneTemplateService.class).getRewardIdByLevel(mileStoneId, rewardLevel);
        if (rewardId == 0) {
            throw new GeminiException(ErrorCode.REWARD_NOT_EXIST);
        }
        AssetPackage rewardResult = ResHolder.getResService(ItemResService.class).randomReward2Pack(rewardId);
        getOwner().getAssetComponent().give(rewardResult, CommonEnum.Reason.ICR_MILESTONE, String.valueOf(mileStoneId));
        return rewardResult;
    }

    private static boolean isCanReward(CommonEnum.MileStoneRewardStatus rewardStatus) {
        return rewardStatus == CommonEnum.MileStoneRewardStatus.MSR_WAITING_AWARD;
    }

    /**
     * 同步大世界里程碑阶段
     *
     * @param isNewAccount 注册进来时需要屏蔽奖励
     */
    public void updateMileStoneData(int newMileStoneId, long endTsMs, Collection<
            StructMsg.MileStoneRewardData> mileStoneRewardData, boolean isNewAccount, int templateId, String reason) {
        LOGGER.info("sync mileStoneData start, mileStoneRewardData={} isNewAccount={} templateId={}", mileStoneRewardData, isNewAccount, templateId);
        setTemplateId(templateId, reason);
        PlayerMileStoneModelProp prop = getProp();
        if (prop.getCurMileStoneId() == newMileStoneId) {
            return;
        }
        for (StructMsg.MileStoneRewardData mileStoneReward : mileStoneRewardData) {
            PlayerMileStoneInfoProp mileStoneInfoProp = getOrInitProp(mileStoneReward.getMileStoneId());
            if (mileStoneInfoProp.getIsOver()) {
                continue;
            }
            mileStoneInfoProp.setIsOver(true);
            onMilestoneChapterOver(mileStoneInfoProp, mileStoneReward, templateId, isNewAccount);
        }
        updateCurMileStone(prop, newMileStoneId, endTsMs);
        if (newMileStoneId > 0) {
            updateCurMileStonePost(getOrInitProp(newMileStoneId), templateId);
        }
        LOGGER.info("sync mileStoneData end curMileStone={} endTsMs={}", newMileStoneId, endTsMs);
    }

    /**
     * @param isNewAccount 注册进来时需要屏蔽奖励
     */
    private void onMilestoneChapterOver(PlayerMileStoneInfoProp mileStoneInfoProp, StructMsg.MileStoneRewardData
            mileStoneReward, int template, boolean isNewAccount) {
        LOGGER.info("PlayerMileStoneComponent onMilestoneChapterOver mileStoneId={} isSuccess={} rewardRange={}",
                mileStoneReward.getMileStoneId(), mileStoneReward.getIsSuccess(), mileStoneReward.getRewardRange());

        // 计算奖励信息
        RewardCalculationResult result = calculateRewardInfo(mileStoneInfoProp, mileStoneReward, template, isNewAccount);

        // 更新里程碑信息
        LOGGER.info("PlayerMileStoneComponent onMilestoneChapterOver milestoneId={} rewardLevel={} rewardStatus={} reason={}",
                mileStoneInfoProp.getMileStoneId(), result.rewardLevel, result.rewardStatus, result.reason);
        mileStoneInfoProp.setRewardLevel(result.rewardLevel)
                .setRewardStatus(result.rewardStatus)
                .setDissatisfiedReason(result.reason);

        // 发送里程碑完成邮件
        sendMilestoneCompletionMail(mileStoneInfoProp);

        // 处理迷雾解锁逻辑
        handleFogUnlockLogic(mileStoneInfoProp, mileStoneReward);
    }

    /**
     * 奖励计算结果
     */
    private static class RewardCalculationResult {
        final int rewardLevel;
        final CommonEnum.MileStoneRewardStatus rewardStatus;
        final CommonEnum.MileStoneDissatisfiedReaSon reason;

        RewardCalculationResult(int rewardLevel, CommonEnum.MileStoneRewardStatus rewardStatus,
                                CommonEnum.MileStoneDissatisfiedReaSon reason) {
            this.rewardLevel = rewardLevel;
            this.rewardStatus = rewardStatus;
            this.reason = reason;
        }
    }

    /**
     * 计算奖励信息
     */
    private RewardCalculationResult calculateRewardInfo(PlayerMileStoneInfoProp mileStoneInfoProp,
                                                        StructMsg.MileStoneRewardData mileStoneReward, int template, boolean isNewAccount) {
        if (isNewAccount) {
            return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_SELF);
        }

        CommonEnum.MileStoneRewardRange rewardRange = mileStoneReward.getRewardRange();
        boolean isSuccess = mileStoneReward.getIsSuccess();

        switch (rewardRange) {
            case MSRR_CONDITION_PLAYER:
                return handleConditionPlayerReward(mileStoneInfoProp, mileStoneReward, template, isSuccess);
            case MSRR_CONDITION_ZONE:
                return handleConditionZoneReward(mileStoneReward, isSuccess);
            case MSRR_ALL_PLAYER:
                return handleAllPlayerReward(mileStoneReward, isSuccess);
            case MSRR_ALL_CLAN:
            case MSRR_CONDITION_CLAN:
                return handleClanRelatedReward(mileStoneReward, rewardRange, isSuccess);
            default:
                return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                        CommonEnum.MileStoneDissatisfiedReaSon.MDR_NONE);
        }
    }

    /**
     * 处理个人条件奖励
     */
    private RewardCalculationResult handleConditionPlayerReward(PlayerMileStoneInfoProp mileStoneInfoProp,
                                                                StructMsg.MileStoneRewardData mileStoneReward, int template, boolean isSuccess) {
        if (!isSuccess) {
            return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_SELF);
        }

        CommonEnum.MileStoneTaskType taskType = ResHolder.getResService(MileStoneTemplateService.class)
                .getTaskTypeById(mileStoneInfoProp.getMileStoneId());
        String[] taskParamById = ResHolder.getResService(MileStoneTemplateService.class)
                .getTaskParamById(mileStoneReward.getMileStoneId(), template);

        int requiredScore = getRequiredScoreForTaskType(taskType, taskParamById);
        if (mileStoneInfoProp.getSelfScore() >= requiredScore) {
            int rewardLevel = ResHolder.getResService(MileStoneTemplateService.class)
                    .getRewardLevelByRank(mileStoneReward.getMileStoneId(), 1);
            return new RewardCalculationResult(rewardLevel, CommonEnum.MileStoneRewardStatus.MSR_WAITING_AWARD,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_NONE);
        } else {
            return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_SELF);
        }
    }

    /**
     * 获取任务类型对应的所需分数
     */
    private int getRequiredScoreForTaskType(CommonEnum.MileStoneTaskType taskType, String[] taskParamById) {
        switch (taskType) {
            case MST_PLAYER_KILL_MONSTER:
                return Integer.parseInt(taskParamById[2]);
            case MST_PLAYER_BUILD:
                return Integer.parseInt(taskParamById[1]);
            case MST_PLAYER_KILL_OTHER_SOLDIER:
                return Integer.parseInt(taskParamById[0]);
            default:
                return 0;
        }
    }

    /**
     * 处理区域条件奖励
     */
    private RewardCalculationResult handleConditionZoneReward(StructMsg.MileStoneRewardData mileStoneReward, boolean isSuccess) {
        if (mileStoneReward.getRewardZoneIdList().contains(getOwner().getZoneId())) {
            int rewardLevel = ResHolder.getResService(MileStoneTemplateService.class)
                    .getRewardLevelByRank(mileStoneReward.getMileStoneId(), 1);
            return new RewardCalculationResult(rewardLevel, CommonEnum.MileStoneRewardStatus.MSR_WAITING_AWARD,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_NONE);
        } else {
            return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_SELF);
        }
    }

    /**
     * 处理全体玩家奖励
     */
    private RewardCalculationResult handleAllPlayerReward(StructMsg.MileStoneRewardData mileStoneReward, boolean isSuccess) {
        if (isSuccess) {
            int rewardLevel = ResHolder.getResService(MileStoneTemplateService.class)
                    .getRewardLevelByRank(mileStoneReward.getMileStoneId(), 1);
            return new RewardCalculationResult(rewardLevel, CommonEnum.MileStoneRewardStatus.MSR_WAITING_AWARD,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_NONE);
        } else {
            return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_SELF);
        }
    }

    /**
     * 处理军团相关奖励
     */
    private RewardCalculationResult handleClanRelatedReward(StructMsg.MileStoneRewardData mileStoneReward,
                                                            CommonEnum.MileStoneRewardRange rewardRange, boolean isSuccess) {
        long joinClanTsMs = getOwner().getPlayerClanComponent().getLastJoinClanTsMs();
        long quitClanTsMs = getOwner().getPlayerClanComponent().getLastQuitClanTsMs();
        long clanId = getOwner().getClanId();

        if (rewardRange == CommonEnum.MileStoneRewardRange.MSRR_ALL_CLAN) {
            if (isSuccess && isInClan(mileStoneReward, joinClanTsMs, quitClanTsMs, clanId)) {
                int rewardLevel = ResHolder.getResService(MileStoneTemplateService.class)
                        .getRewardLevelByRank(mileStoneReward.getMileStoneId(), 1);
                return new RewardCalculationResult(rewardLevel, CommonEnum.MileStoneRewardStatus.MSR_WAITING_AWARD,
                        CommonEnum.MileStoneDissatisfiedReaSon.MDR_NONE);
            }
            return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                    CommonEnum.MileStoneDissatisfiedReaSon.MDR_CLAN);
        } else { // MSRR_CONDITION_CLAN
            if (isInClan(mileStoneReward, joinClanTsMs, quitClanTsMs, clanId)) {
                Map<Long, Integer> clan2RewardMap = mileStoneReward.getClan2RewardMap();
                if (clan2RewardMap.containsKey(getOwner().getClanId())) {
                    int rewardLevel = clan2RewardMap.get(getOwner().getClanId());
                    return new RewardCalculationResult(rewardLevel, CommonEnum.MileStoneRewardStatus.MSR_WAITING_AWARD,
                            CommonEnum.MileStoneDissatisfiedReaSon.MDR_NONE);
                } else {
                    return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                            CommonEnum.MileStoneDissatisfiedReaSon.MDR_CLAN);
                }
            } else {
                return new RewardCalculationResult(0, CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED,
                        CommonEnum.MileStoneDissatisfiedReaSon.MDR_SELF);
            }
        }
    }

    /**
     * 发送里程碑完成邮件
     */
    private void sendMilestoneCompletionMail(PlayerMileStoneInfoProp mileStoneInfoProp) {
        MilestoneChapterTemplate mileStoneTemplate = ResHolder.getInstance()
                .getValueFromMap(MilestoneChapterTemplate.class, mileStoneInfoProp.getMileStoneId());
        if (mileStoneTemplate.getNextChapterID() <= 0) {
            return;
        }

        MilestoneChapterTemplate nextMilestoneTemplate = ResHolder.getInstance()
                .getValueFromMap(MilestoneChapterTemplate.class, mileStoneTemplate.getNextChapterID());
        for (Integer mailId : nextMilestoneTemplate.getMailIdList()) {
            StructMail.MailSendParams.Builder param = StructMail.MailSendParams.newBuilder()
                    .setMailTemplateId(mailId);
            int reasonZoneId = getOwner().getZoneId();
            param.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                    .getDisplayDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, reasonZoneId));
            final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                    .setPlayerId(getOwner().getPlayerId())
                    .setZoneId(getOwner().getZoneId())
                    .build();
            getOwner().getMailComponent().sendPersonalMail(receiver, param.build());
        }
    }

    /**
     * 处理迷雾解锁逻辑
     */
    private void handleFogUnlockLogic(PlayerMileStoneInfoProp mileStoneInfoProp, StructMsg.MileStoneRewardData mileStoneReward) {
        // 个人迷雾里程碑已完成且个人迷雾未全解锁时->不允许领奖、开启全部迷雾
        int mileStoneStarId = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getMileStoneStarId();
        int allFogUnlockMilestoneId = ResHolder.getResService(MileStoneTemplateService.class)
                .getAllFogUnlockMilestoneId(mileStoneStarId);
        if (allFogUnlockMilestoneId == mileStoneReward.getMileStoneId()) {
            mileStoneInfoProp.setRewardStatus(CommonEnum.MileStoneRewardStatus.MSR_DISSATISFIED);
        }
    }

    private boolean isInClan(StructMsg.MileStoneRewardData mileStoneReward, long joinClanTsMs, long quitClanTsMs,
                             long clanId) {
        return (clanId > 0 && joinClanTsMs <= mileStoneReward.getEndTsMs()) || (clanId <= 0 && quitClanTsMs >= mileStoneReward.getEndTsMs());
    }

    /**
     * 登录、切换里程碑、迁服
     */
    private void setTemplateId(int templateId, String reason) {
        if (getProp().getTemplateId() == templateId) {
            return;
        }
        LOGGER.info("PlayerMileStoneComponent setTemplateId success, reason={} oldTemplateId={} newTemplateId={}", reason, getProp().getTemplateId(), templateId);
        getProp().setTemplateId(templateId);
    }

    private void updateCurMileStone(PlayerMileStoneModelProp prop, int newMileStoneId, long endTsMs) {
        prop.setCurMileStoneId(newMileStoneId).setCurEndTsMs(endTsMs);
    }

    private void updateCurMileStonePost(PlayerMileStoneInfoProp newMileStone, int templateId) {
        // 任务类型相关
        CommonEnum.MileStoneTaskType taskTypeById = ResHolder.getResService(MileStoneTemplateService.class).getTaskTypeById(newMileStone.getMileStoneId());
        if (taskTypeById == CommonEnum.MileStoneTaskType.MST_PLAYER_BUILD) {
            String[] taskParamById = ResHolder.getResService(MileStoneTemplateService.class).getTaskParamById(newMileStone.getMileStoneId(), templateId);
            int level = Integer.parseInt(taskParamById[1]);
            newMileStone.setSelfScore(level);
        }
    }

    private PlayerMileStoneInfoProp getOrInitProp(int mileStoneId) {
        PlayerMileStoneModelProp prop = getProp();
        PlayerMileStoneInfoProp mileStoneInfoProp = prop.getMileStoneInfoV(mileStoneId);
        if (mileStoneInfoProp == null) {
            mileStoneInfoProp = prop.addEmptyMileStoneInfo(mileStoneId);
        }
        return mileStoneInfoProp;
    }

    public void resetDataByGm() {
        PlayerMileStoneModelProp prop = getProp();
        updateCurMileStone(prop, 0, 0);
        prop.getMileStoneInfo().clear();
    }

    public void updateScore(CommonEnum.MileStoneTaskType mileStoneTaskType, PlayerEvent event) {
        // 人在哪个服就记哪个服的数据
        PlayerMileStoneModelProp prop = getProp();

        // 验证参数和前置条件
        if (!validateUpdateScoreParams(prop, mileStoneTaskType, event)) {
            return;
        }

        // 处理符合条件的里程碑
        processEligibleMilestones(prop, mileStoneTaskType, event);
    }

    /**
     * 验证更新分数的参数和前置条件
     */
    private boolean validateUpdateScoreParams(PlayerMileStoneModelProp prop, CommonEnum.MileStoneTaskType mileStoneTaskType, PlayerEvent event) {
        int curMileStoneId = prop.getCurMileStoneId();
        if (curMileStoneId <= 0) {
            return false;
        }
        if (prop.getTemplateId() <= 0) {
            LOGGER.warn("PlayerMileStoneComponent updateScore fail no templateId, templateId={} mileStoneTaskType={} event={}",
                    prop.getTemplateId(), mileStoneTaskType, event);
            return false;
        }
        return true;
    }

    /**
     * 处理符合条件的里程碑
     */
    private void processEligibleMilestones(PlayerMileStoneModelProp prop, CommonEnum.MileStoneTaskType mileStoneTaskType, PlayerEvent event) {
        MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);
        Collection<MilestoneChapterTemplate> listFromMap = ResHolder.getInstance().getListFromMap(MilestoneChapterTemplate.class);

        for (MilestoneChapterTemplate milestoneChapterTemplate : listFromMap) {
            int mileStoneId = milestoneChapterTemplate.getId();
            CommonEnum.MileStoneTaskType taskType = resService.getTaskTypeById(mileStoneId);

            if (!shouldProcessMilestone(prop, mileStoneTaskType, taskType, mileStoneId)) {
                continue;
            }

            PlayerMileStoneInfoProp mileStoneInfoProp = getOrInitProp(milestoneChapterTemplate.getId());
            updateMilestoneScore(mileStoneTaskType, event, prop, resService, mileStoneInfoProp);

            if (milestoneChapterTemplate.getNextChapterID() <= 0) {
                break;
            }
        }
    }

    /**
     * 判断是否应该处理某个里程碑
     */
    private boolean shouldProcessMilestone(PlayerMileStoneModelProp prop, CommonEnum.MileStoneTaskType mileStoneTaskType,
                                           CommonEnum.MileStoneTaskType taskType, int mileStoneId) {
        if (taskType != mileStoneTaskType) {
            return false;
        }

        int curMileStoneId = prop.getCurMileStoneId();
        // 不统计已结束的里程碑
        if (curMileStoneId > mileStoneId) {
            return false;
        }

        // 非领取后统计的里程碑类型，只统计进行中的
        MilestoneTaskTypeTemplate taskTypeTemplate = ResHolder.getInstance()
                .getValueFromMap(MilestoneTaskTypeTemplate.class, taskType.getNumber());
        if (taskTypeTemplate.getStatisticType() != CommonEnum.MileStoneStatisticType.MSST_FINISH_OR_TIME_END
                && curMileStoneId != mileStoneId) {
            return false;
        }

        return true;
    }

    /**
     * 更新里程碑分数
     */
    private void updateMilestoneScore(CommonEnum.MileStoneTaskType mileStoneTaskType, PlayerEvent event,
                                      PlayerMileStoneModelProp prop, MileStoneTemplateService resService, PlayerMileStoneInfoProp mileStoneInfoProp) {
        switch (mileStoneTaskType) {
            case MST_PLAYER_KILL_MONSTER:
                handlePlayerKillMonsterScore(event, prop, resService, mileStoneInfoProp);
                break;
            case MST_KILL_REBEL_NUM:
                handleKillRebelScore(event, prop, resService, mileStoneInfoProp);
                break;
            case MST_PLAYER_KILL_OTHER_SOLDIER:
                handlePlayerKillSoldierScore(event, mileStoneInfoProp);
                break;
            case MST_PLAYER_BUILD:
                mileStoneInfoProp.setSelfScore(0);
                break;
            default:
                break;
        }
    }

    /**
     * 处理玩家击杀怪物分数
     */
    private void handlePlayerKillMonsterScore(PlayerEvent event, PlayerMileStoneModelProp prop,
                                              MileStoneTemplateService resService, PlayerMileStoneInfoProp mileStoneInfoProp) {
        PlayerKillBigSceneMonsterEvent monsterKillEvent = (PlayerKillBigSceneMonsterEvent) event;
        String[] taskParamById = resService.getTaskParamById(mileStoneInfoProp.getMileStoneId(), prop.getTemplateId());
        int monsterType = Integer.parseInt(taskParamById[0]);
        int monsterLevel = Integer.parseInt(taskParamById[1]);
        int configCount = Integer.parseInt(taskParamById[2]);

        if (matchWithMonster(monsterKillEvent, monsterType, monsterLevel)) {
            mileStoneInfoProp.setSelfScore(Math.min(configCount, mileStoneInfoProp.getSelfScore() + 1));
        }
    }

    /**
     * 处理击杀叛军分数
     */
    private void handleKillRebelScore(PlayerEvent event, PlayerMileStoneModelProp prop,
                                      MileStoneTemplateService resService, PlayerMileStoneInfoProp mileStoneInfoProp) {
        PlayerKillBigSceneMonsterEvent monsterKillEvent = (PlayerKillBigSceneMonsterEvent) event;
        String[] taskParamById = resService.getTaskParamById(mileStoneInfoProp.getMileStoneId(), prop.getTemplateId());
        int monsterType = Integer.parseInt(taskParamById[1]);
        int monsterLevel = Integer.parseInt(taskParamById[2]);

        if (matchWithMonster(monsterKillEvent, monsterType, monsterLevel)) {
            mileStoneInfoProp.setSelfScore(mileStoneInfoProp.getSelfScore() + 1);
        }
    }

    /**
     * 处理玩家击杀士兵分数
     */
    private void handlePlayerKillSoldierScore(PlayerEvent event, PlayerMileStoneInfoProp mileStoneInfoProp) {
        PlayerKillSoldierEvent killSoldierEvent = (PlayerKillSoldierEvent) event;
        int otherCurZoneId = killSoldierEvent.getOtherCurZoneId();

        if (otherCurZoneId == 0) {
            LOGGER.error("PlayerMileStoneComponent updateScore error, killSoldierEvent={}", killSoldierEvent);
            return;
        }
        if (otherCurZoneId == getOwner().getZoneId()) {
            return;
        }
        if (killSoldierEvent.isNpcEnemy()) {
            return;
        }

        // 杀玩家
        long sum = killSoldierEvent.getKillSoldierMap().values().stream().mapToLong(it -> it).sum();
        mileStoneInfoProp.setSelfScore(mileStoneInfoProp.getSelfScore() + sum);
    }

    private boolean matchWithMonster(PlayerKillBigSceneMonsterEvent event, int monsterConfigType,
                                     int monsterConfigLevel) {
        return event.getMonsterCategory() == monsterConfigType && (monsterConfigLevel == 0 || event.getLevel() >= monsterConfigLevel);
    }

    public List<Integer> getIsOverMileStoneId() {
        return getProp().getMileStoneInfo().values().stream().filter(PlayerMileStoneInfoProp::getIsOver).map(PlayerMileStoneInfoProp::getMileStoneId).collect(Collectors.toList());
    }
}

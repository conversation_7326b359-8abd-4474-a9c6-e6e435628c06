package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.BuildFullOpenEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.USE_BUILD_ACCELERATE_ITEM_TOTAL;

/**
 * 使用城建加速xx分钟
 * param1: 累计加速时间
 *
 * <AUTHOR>
 */
public class UseBuildAccelerateItemChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerSpeedUpEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName(), BuildFullOpenEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer time = taskParams.get(0);
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int useAccelerateItemTotal = (int) entity.getStatisticComponent().getSingleStatistic(USE_BUILD_ACCELERATE_ITEM_TOTAL);
                prop.setProcess(Math.min(useAccelerateItemTotal, time));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerSpeedUpEvent) {
                    PlayerSpeedUpEvent speedEvent = (PlayerSpeedUpEvent) event;
                    if (QueueSpeedReason.isFreeSpeedUp(speedEvent.getReason())) {
                        break;
                    }
                    if (speedEvent.getType() != CommonEnum.QueueTaskType.CITY_BUILD) {
                        break;
                    }
                    int speedTime = (int) MathUtils.ceilLong((double) speedEvent.getTotalCostSec() / 60);
                    prop.setProcess(Math.min(prop.getProcess() + speedTime, time));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= time;
    }
}

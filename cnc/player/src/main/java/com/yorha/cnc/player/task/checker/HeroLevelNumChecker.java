package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerHeroLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerHeroUnLockEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 拥有x个x等级的英雄
 * param1: 数量
 * param2: 等级
 *
 * <AUTHOR>
 */
public class HeroLevelNumChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerHeroLevelUpEvent.class.getSimpleName(),
            PlayerHeroUnLockEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer param1 = taskParams.get(0);
        Integer param2 = taskParams.get(1);

        int count = 0;
        for (PlayerHeroProp hero : event.getPlayer().getHeroComponent().getAllHero()) {
            if (hero.getLevel() >= param2) {
                count++;
            }
        }
        if (count != prop.getProcess()) {
            prop.setProcess(Math.min(param1, count));
        }

        return prop.getProcess() >= param1;
    }

}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerTask;

import java.util.List;

/**
 * 任务系统
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_TASK)
public class PlayerTaskController {
    @CommandMapping(code = MsgType.PLAYER_TASKAWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerTask.Player_TaskAward_C2S msg) {
        int configId = msg.getTaskId();
        CommonEnum.TaskClass taskClass = msg.getClass_();
        if (taskClass == CommonEnum.TaskClass.TC_NONE || configId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        List<IntPairType> intPairTypes = playerEntity.getTaskComponent().checkAndTakeTaskReward(taskClass, configId);
        if (intPairTypes == null || intPairTypes.size() <= 0) {
            throw new GeminiException(ErrorCode.TASK_REWARD_NOT_EXIST);
        }
        PlayerTask.Player_TaskAward_S2C.Builder builder = PlayerTask.Player_TaskAward_S2C.newBuilder();
        for (IntPairType intPairType : intPairTypes) {
            builder.addReward(PlayerTask.RewardPair.newBuilder().setItemId(intPairType.getKey()).setNum(intPairType.getValue()));
        }
        return builder.build();
    }

}

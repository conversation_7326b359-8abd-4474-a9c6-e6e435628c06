package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerHeroLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerHeroUnLockEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 指定英雄达到x级
 * param1:英雄id
 * param2: 等级
 *
 * <AUTHOR>
 */
public class PlayerHeroLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerHeroLevelUpEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            PlayerHeroUnLockEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.get(0);
        int param2 = taskParams.get(1);

        int heroLevel;
        if (param1 == 0) {
            heroLevel = event.getPlayer().getHeroComponent().getMaxHeroLevel();
        } else {
            heroLevel = event.getPlayer().getHeroComponent().getHero(param1).getLevel();
        }
        prop.setProcess(Math.min(heroLevel, param2));

        return prop.getProcess() >= param2;
    }
}

package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum.DebugGroup;
import com.yorha.proto.StructMail;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AddClanMail implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("mailTemplateId"));
        long clanId = Long.parseLong(args.get("targetId"));
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(templateId);
        MailUtil.sendClanMail(actor.getZoneId(), clanId, builder.build());
    }

    @Override
    public String showHelp() {
        return "AddClanMail mailTemplateId={value} targetId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAIL;
    }
}

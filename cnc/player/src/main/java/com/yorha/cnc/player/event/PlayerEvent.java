package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.framework.event.EntityEvent;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 */
public abstract class PlayerEvent extends EntityEvent {
    protected final PlayerEntity player;

    public PlayerEvent(PlayerEntity entity) {
        this.player = entity;
    }

    public PlayerEntity getPlayer() {
        return player;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

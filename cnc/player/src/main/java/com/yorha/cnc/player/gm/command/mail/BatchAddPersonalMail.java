package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;

import java.util.Map;

/**
 * 批量添加个人邮件
 *
 * <AUTHOR>
 */
public class BatchAddPersonalMail implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("mailTemplateId"));
        long targetId = Long.parseLong(args.get("targetId"));
        int num = Integer.parseInt(args.get("num"));
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(templateId);
        if (targetId == 0) {
            targetId = actor.getPlayerId();
        }

        for (int i = 0; i < num; i++) {
            MailUtil.sendMailToPlayer(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(targetId)
                            .setZoneId(actor.getZoneId())
                            .build(),
                    builder.build());
        }
    }

    @Override
    public String showHelp() {
        return "BatchAddPersonalMail mailTemplateId={value} targetId={value} num={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAIL;
    }
}

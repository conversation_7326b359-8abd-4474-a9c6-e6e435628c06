package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * <AUTHOR>
 * 增加体力
 */
public class AddPlayerEnergy implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int energy = Integer.parseInt(args.getOrDefault("energy", "0"));
        actor.getEntity().getEnergyComponent().addEnergy(energy, true, "gm", 0);
    }

    @Override
    public String showHelp() {
        return "AddPlayerEnergy energy={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.PlayerClanDonateEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 联盟捐献
 * param1: 次数
 *
 * <AUTHOR>
 */
public class ClanDonateChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(ClanDonateChecker.class);

    public static List<String> attentionList = Lists.newArrayList(PlayerClanDonateEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int countConfig = taskParams.getFirst();
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int count = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(StatisticEnum.CLAN_TECH_DONATE);
                prop.setProcess(Math.min(count, countConfig));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerClanDonateEvent) {
                    PlayerClanDonateEvent clanDonateEvent = (PlayerClanDonateEvent) event;
                    if (clanDonateEvent.getCount() > 0) {
                        prop.setProcess(Math.min(prop.getProcess() + clanDonateEvent.getCount(), countConfig));
                    } else {
                        LOGGER.error("ClanDonateChecker count err, event={}", event);
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}",
                        ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }

        return prop.getProcess() >= countConfig;
    }
}

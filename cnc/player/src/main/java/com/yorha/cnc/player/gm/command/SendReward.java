package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.proto.CommonEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 发随机奖励
 *
 * <AUTHOR> dieying
 */
public class SendReward implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int id = Integer.parseInt(args.get("id"));
        int count = Integer.parseInt(args.get("count"));
        if (count > 100) {
            throw new GeminiException("count>100 invalid");
        }
        PlayerEntity player = actor.getEntity();
        for (int i = 0; i < count; i++) {
            List<ItemReward> sendReward = player.getItemComponent().sendReward(id, CommonEnum.Reason.ICR_GM, "SendReward");
            if (CollectionUtils.isEmpty(sendReward)) {
                throw new GeminiException("reward is empty");
            }
        }
    }

    @Override
    public String showHelp() {
        return "SendReward id={value} count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_ITEM;
    }
}

package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.ItemTemplate;

import java.util.Map;

/**
 * 洛哈活动测试
 *
 * <AUTHOR>
 */
public class GmTestLuoHa implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int item = Integer.parseInt(args.get("item"));
        int num = Integer.parseInt(args.get("num"));
        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, item);
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST.getCodeId());
        }
        for (int i = 0; i < num; i++) {
            actor.getEntity().getItemComponent().addItem(itemTemplate, 1, CommonEnum.Reason.ICR_GM, "");
            actor.getEntity().getItemComponent().useItemByTemplateId(itemTemplate.getId(), 1, null);
        }
    }

    @Override
    public String showHelp() {
        return "GmTestLuoHa item={value} num={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

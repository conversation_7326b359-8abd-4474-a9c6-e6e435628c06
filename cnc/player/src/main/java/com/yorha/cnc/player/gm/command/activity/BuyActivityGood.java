package com.yorha.cnc.player.gm.command.activity;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class BuyActivityGood implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int activityId = Integer.parseInt(args.get("activityId"));
        int unitId = Integer.parseInt(args.get("unitId"));
        int goodId = Integer.parseInt(args.get("goodId"));
        actor.getEntity().getActivityComponent().handleBuyActivityGood(activityId, unitId, goodId);
    }

    @Override
    public String showHelp() {
        return "BuyActivityGood activityId={value} unitId={value} goodId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}


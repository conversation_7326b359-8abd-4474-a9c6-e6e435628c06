package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;
import com.yorha.proto.SsClanMember;
import com.yorha.proto.StructClanPB;

import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_CLAN;

/**
 * 军团成员模块: 使用到ss_clan_member里ss协议的cs协议，可以放到这里~
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_BASE)
public class PlayerClanMemberController {

    /**
     * 申请加入军团
     */
    @CommandMapping(code = MsgType.PLAYER_APPLYJOINCLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ApplyJoinClan_C2S msg) {
        if (msg.getClanId() <= 0L) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        long clanId = playerEntity.getPlayerClanComponent().playerApplyJoinClan(msg.getClanId(), "");
        PlayerClan.Player_ApplyJoinClan_S2C.Builder builder = PlayerClan.Player_ApplyJoinClan_S2C.newBuilder();
        builder.setClanId(clanId);
        return builder.build();
    }

    /**
     * 拉取军团申请玩家列表
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANAPPLYPLAYERS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanApplyPlayers_C2S msg) {
        PlayerClan.Player_FetchClanApplyPlayers_S2C.Builder builder = PlayerClan.Player_FetchClanApplyPlayers_S2C.newBuilder();
        StructClanPB.Int64ClanMemberMapPB applyMembers = playerEntity.getPlayerClanComponent().fetchClanApplyMembers();
        if (applyMembers != null) {
            builder.setApplyMembers(applyMembers);
        }
        return builder.build();
    }

    /**
     * 取消申请加入军团
     */
    @CommandMapping(code = MsgType.PLAYER_CANCELAPPLYJOINCLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CancelApplyJoinClan_C2S msg) {
        playerEntity.getPlayerClanComponent().playerCancelApplyJoinClan(msg.getClanId());
        return PlayerClan.Player_CancelApplyJoinClan_S2C.getDefaultInstance();
    }

    /**
     * 处理军团申请  接受/拒绝
     */
    @CommandMapping(code = MsgType.PLAYER_HANDLECLANAPPLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_HandleClanApply_C2S msg) {
        PlayerActor playerActor = playerEntity.ownerActor();
        // 检查是否有审批权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_AUDIT, staffId);
        SsClanMember.ProcessClanApplyAsk.Builder call = SsClanMember.ProcessClanApplyAsk.newBuilder();
        call.setOperatorId(playerEntity.getEntityId()).setTargetId(msg.getPlayerId()).setIsAllow(msg.getIsAllow());
        SsClanMember.ProcessClanApplyAns ans = playerActor.callCurClan(call.build());
        if (!ErrorCode.isOK(ans.getCode())) {
            throw new GeminiException(ans.getCode());
        }
        return PlayerClan.Player_HandleClanApply_S2C.getDefaultInstance();
    }

    /**
     * 退出军团
     */
    @CommandMapping(code = MsgType.PLAYER_QUITCLAN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_QuitClan_C2S msg) {
        // 检查是否有退出权限（NOTE(furson): 退出权限有什么好检查的呢？）
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_QUIT, staffId);
        playerEntity.ownerActor().callCurClan(SsClanMember.PlayerQuitClanAsk.newBuilder().setPlayerId(playerEntity.getEntityId()).build());
        playerEntity.getPlayerClanComponent().onLeaveClan(SystemClock.now());
        return PlayerClan.Player_QuitClan_S2C.getDefaultInstance();
    }

    /**
     * 踢人！
     */
    @CommandMapping(code = MsgType.PLAYER_KICKOFFCLANMEMBER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_KickOffClanMember_C2S msg) {
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId <= 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        playerEntity.getSettingComponent().checkSpassword(SPWC_CLAN, 0, msg.getSPassWord());
        // 检查是否有踢人权限
        int staffId = playerEntity.getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_KICK_OFF, staffId);

        PlayerClan.Player_KickOffClanMember_S2C.Builder builder = PlayerClan.Player_KickOffClanMember_S2C.newBuilder();
        final long operatorId = playerEntity.getEntityId();
        final long targetId = msg.getPlayerId();
        playerEntity.ownerActor().callCurClan(SsClanMember.KickOutClanMemberAsk.newBuilder()
                .setReason(msg.getReason()).setOperatorId(operatorId).setTargetId(targetId).build());
        return builder.build();
    }

    /**
     * 转让军团长
     */
    @CommandMapping(code = MsgType.PLAYER_TRANSFERCLANOWNER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_TransferClanOwner_C2S msg) {
        final int clanStaffId = ResHolder.getResService(ClanDataTemplateService.class).getOwnerStaffId();

        // 检查权限
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_ADJUST_STAFF, playerEntity.getPlayerClanComponent().getStaffId());

        // 到clan上改动职位
        SsClanMember.GrantClanStaffAsk.Builder call = SsClanMember.GrantClanStaffAsk.newBuilder();
        call.setStaffId(clanStaffId).setOperatorId(playerEntity.getEntityId()).setTargetId(msg.getPlayerId());
        SsClanMember.GrantClanStaffAns ans = playerEntity.ownerActor().callCurClan(call.build());

        if (ans.hasNextCanBeOwnerTsMs()) {
            playerEntity.getProp().getClan().setCanBeClanOwnerTsMs(ans.getNextCanBeOwnerTsMs());
        }
        return PlayerClan.Player_TransferClanOwner_S2C.getDefaultInstance();
    }

    /**
     * 修改玩家在军团内的官职
     */
    @CommandMapping(code = MsgType.PLAYER_MODIFYCLANMEMBERSTAFF_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ModifyClanMemberStaff_C2S msg) {
        // 检查密码
        playerEntity.getSettingComponent().checkSpassword(SPWC_CLAN, 0, msg.getSPassWord());
        // 检查权限
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_ADJUST_STAFF, playerEntity.getPlayerClanComponent().getStaffId());
        // 到clan上变动职位
        SsClanMember.GrantClanStaffAsk.Builder call = SsClanMember.GrantClanStaffAsk.newBuilder();
        call.setStaffId(msg.getStaffId()).setOperatorId(playerEntity.getEntityId()).setTargetId(msg.getPlayerId());
        SsClanMember.GrantClanStaffAns ans = playerEntity.ownerActor().callCurClan(call.build());

        PlayerClan.Player_ModifyClanMemberStaff_S2C.Builder builder = PlayerClan.Player_ModifyClanMemberStaff_S2C.newBuilder();
        if (ans.hasNextCanGrantTsMs()) {
            return builder.setNextCanGrantTsMs(ans.getNextCanGrantTsMs()).build();
        }
        return builder.build();
    }

    /**
     * 解散军团时玩家取消申请成为军团长
     */
    @CommandMapping(code = MsgType.PLAYER_CANCELAPPLYOWNERDISSOLVING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CancelApplyOwnerDissolving_C2S msg) {
        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId <= 0L) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        SsClanMember.CancelApplyOwnerDissolvingAsk.Builder builder = SsClanMember.CancelApplyOwnerDissolvingAsk.newBuilder()
                .setPlayerId(playerEntity.getPlayerId());
        playerEntity.ownerActor().callCurClan(builder.build());
        return PlayerClan.Player_CancelApplyOwnerDissolving_S2C.getDefaultInstance();
    }


    /**
     * 解散军团时申请成为军团长
     */
    @CommandMapping(code = MsgType.PLAYER_APPLYOWNERDISSOLVING_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ApplyOwnerDissolving_C2S msg) {
        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId <= 0L) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        SsClanMember.ApplyOwnerDissolvingAsk.Builder builder = SsClanMember.ApplyOwnerDissolvingAsk.newBuilder()
                .setPlayerId(playerEntity.getPlayerId());
        playerEntity.ownerActor().callCurClan(builder.build());
        return PlayerClan.Player_ApplyOwnerDissolving_S2C.getDefaultInstance();
    }

    /**
     * 拉取联盟势力奖励
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANPOWERREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanPowerReward_C2S msg) {
        SsClanMember.FetchClanPowerRewardAsk call = SsClanMember.FetchClanPowerRewardAsk.newBuilder().setPlayerId(playerEntity.getEntityId()).build();
        SsClanMember.FetchClanPowerRewardAns ans = playerEntity.ownerActor().callCurClan(call);
        PlayerClan.Player_FetchClanPowerReward_S2C.Builder builder = PlayerClan.Player_FetchClanPowerReward_S2C.newBuilder();
        builder.putAllResources(ans.getResourcesMap());
        return builder.build();
    }
}

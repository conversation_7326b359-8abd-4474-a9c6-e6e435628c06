package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum.DebugGroup;
import com.yorha.proto.Core.Code;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class GetPlayerEnergy implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        Code.Builder code = Code.newBuilder();
        code.setId(ErrorCode.PLAYER_ENERGY_VALUE.getCode().getId());
        code.addParam("" + actor.getEntity().getEnergyComponent().recoverEnergy());
        actor.getEntity().sendMsgToClient(
                MsgType.PLAYER_PLAYDIALOG_NTF,
                MsgHelper.buildErrorMsg(ErrorCode.PLAYER_ENERGY_VALUE.getCode())
        );
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}

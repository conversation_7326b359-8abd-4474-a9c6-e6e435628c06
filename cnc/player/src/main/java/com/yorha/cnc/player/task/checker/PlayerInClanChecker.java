package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 玩家正处在一个联盟中
 *
 * <AUTHOR>
 */
public class PlayerIn<PERSON><PERSON><PERSON>he<PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerJoinClanEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int config = 1;

        if (event instanceof PlayerJoinClanEvent) {
            prop.setProcess(1);
        } else {
            if (event.getPlayer().getPlayerClanComponent().isInClan()) {
                prop.setProcess(1);
            }
        }

        return prop.getProcess() >= config;
    }

}

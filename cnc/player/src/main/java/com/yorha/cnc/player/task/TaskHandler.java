package com.yorha.cnc.player.task;

import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.resource.datatype.IntPairType;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Set;

/**
 * 任务处理类
 *
 * <AUTHOR>
 */
public interface TaskHandler {

    /**
     * 加载任务
     * 添加关注事件
     */
    void loadAllTask();

    /**
     * 离线清除关注事件
     * @param reason
     */
    void clearAllAttention(String reason);

    /**
     * 移除关注事件
     */
    void removeAttention(int taskId);

    /**
     * 开启关注事件（防止真正开启关注事件之前有事件的加入，此时会导致重复添加）
     */
    void openAttention();


    /**
     * 批量添加任务
     */
    void addBatchTask(Set<Integer> taskIds);

    /**
     * 任务完成统一入口
     */
    void onTaskFinish(int configId, TaskGmEnum gmType);

    /**
     * 更新任务进度
     */
    void updateProcess(Integer taskId, PlayerTaskEvent event);

    /**
     * 任务领奖
     */
    List<IntPairType> checkOrTakeReward(List<Integer> taskIds);

    /**
     * 内城建筑等级变更事件
     */
    void onInnerCityUpgrade(BuildFinEvent event);

    TaskPoolTemplate getTaskTemplate(int configId);

    void onNewbieOver();

    void onJoinClan(PlayerJoinClanEvent event);
}

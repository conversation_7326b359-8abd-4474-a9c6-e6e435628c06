package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 英雄星级变更事件
 *
 * <AUTHOR>
 */
public class PlayerHeroStarChangeEvent extends PlayerTaskEvent {
    private final int heroId;
    private final int levelUpStar;

    public PlayerHeroStarChangeEvent(PlayerEntity player, int heroId, int levelUpStar) {
        super(player);
        this.heroId = heroId;
        this.levelUpStar = levelUpStar;
    }

    public int getHeroId() {
        return heroId;
    }

    public int getLevelUpStar() {
        return levelUpStar;
    }

}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.aoiView.AoiViewHelper;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum.CheckZoneViewResult;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.SsAoiView;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.SsZoneCard;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ServerAttributeTemplate;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class PlayerViewComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerViewComponent.class);

    /**
     * 跨服查看请求通过的zoneId们
     */
    private Set<Integer> viewCheckedZoneSet;

    /**
     * 当前正在查看的视野 zone和layer
     */
    private int curViewZoneId;
    private int curViewLayer;

    public PlayerViewComponent(PlayerEntity owner) {
        super(owner);
    }

    public CheckZoneViewResult checkCanViewZone(int targetZoneId) {
        if (targetZoneId == getOwner().getZoneId()) {
            LOGGER.warn("why check self zone view {} {}", getOwner(), targetZoneId);
            return CheckZoneViewResult.CZVR_OK;
        }
        if (viewCheckedZoneSet != null && viewCheckedZoneSet.contains(targetZoneId)) {
            return CheckZoneViewResult.CZVR_OK;
        }
        ServerAttributeTemplate template = ResHolder.getInstance().getValueFromMap(ServerAttributeTemplate.class, targetZoneId);
        if (template == null) {
            return CheckZoneViewResult.CZVR_ZONE_NOT_EXIST;
        }
        // 查询
        try {
            SsZoneCard.GetZoneMileStoneAsk msg = SsZoneCard.GetZoneMileStoneAsk.newBuilder().setZoneId(targetZoneId).build();
            this.getOwner().ownerActor().call(RefFactory.ofZoneCard(), msg);
        } catch (Exception e) {
            if (GeminiException.isLogicException(e)) {
                LOGGER.info("checkCanViewZone get mileStone result {} {} {}", getOwner(), targetZoneId, ((GeminiException) e).getCodeId());
            } else {
                LOGGER.error("checkCanViewZone get mileStone failed {} {} ", getOwner(), targetZoneId, e);
            }
            return CheckZoneViewResult.CZVR_ZONE_NOT_EXIST;
        }
        // 加入查询通过历史
        if (viewCheckedZoneSet == null) {
            viewCheckedZoneSet = new IntOpenHashSet();
        }
        viewCheckedZoneSet.add(targetZoneId);
        LOGGER.info("checkCanViewZone ok {} {}", getOwner(), targetZoneId);
        return CheckZoneViewResult.CZVR_OK;
    }

    public int updateView(PlayerScene.Player_UpdateView_C2S msg) {
        int zoneId = msg.getZoneId();
        // 跨服查看的前置check
        if (zoneId != getOwner().getZoneId()) {
            if ((viewCheckedZoneSet == null || !viewCheckedZoneSet.contains(zoneId))) {
                // 别的服  没检测过 或者检测过但是结果是不通过的  打error 无视此次协议
                LOGGER.warn("want to view other zone but check failed {} {}", getOwner(), zoneId);
                return 0;
            }
        }
        // 参数校验失败
        if (!AoiViewHelper.checkLayerValid(getEntityId(), msg.getLayer(), msg.getP1(), msg.getP2(), msg.getP3(), msg.getP4())) {
            return 0;
        }
        // 在副本 直接发
        if (getOwner().isInDungeon()) {
            SsScenePlayer.UpdatePlayerViewAsk.Builder call = SsScenePlayer.UpdatePlayerViewAsk.newBuilder();
            call.setPlayerId(getEntityId()).setLayerId(msg.getLayer());
            call.setP1(msg.getP1()).setP2(msg.getP2()).setP3(msg.getP3()).setP4(msg.getP4());
            ownerActor().callCurScene(call.build());
            return zoneId;
        }
        // 切服务器了 清理下原来的  直接给新的发
        if (zoneId != curViewZoneId) {
            clearOldZoneView(true);
            return sendUpdateZoneView(msg);
        }
        // 没切服务器
        boolean isOldNormal = BigSceneConstants.isNormalLayer(curViewLayer);
        boolean isNewNormal = BigSceneConstants.isNormalLayer(msg.getLayer());
        // 同一层
        if (isNewNormal == isOldNormal) {
            return sendUpdateZoneView(msg);
        }
        // 不同层 先清再发
        clearOldZoneView(false);
        return sendUpdateZoneView(msg);
    }

    /**
     * 视野模块认为自己的主堡所在的场景zoneId
     */
    private int getCurSceneZone() {
        return getOwner().getZoneId();
    }

    /**
     * logout or enter dungeon
     */
    public void clearView() {
        // 没有视野 不用发
        if (curViewZoneId == 0) {
            return;
        }
        // 是本服普通层 不用发  在ScenePlayer logout/enter dungeon时会自己清理的
        if (curViewZoneId == getCurSceneZone() && BigSceneConstants.isNormalLayer(curViewLayer)) {
            curViewLayer = 0;
            curViewZoneId = 0;
            return;
        }
        clearOldZoneView(true);
    }

    private void clearOldZoneView(boolean isChangeZone) {
        if (curViewZoneId == 0) {
            return;
        }
        try {
            boolean isNormal = BigSceneConstants.isNormalLayer(curViewLayer);
            if (isNormal) {
                SsScenePlayer.ClearPlayerViewAsk.Builder builder = SsScenePlayer.ClearPlayerViewAsk.newBuilder();
                int sceneZoneId = getCurSceneZone();
                boolean isBystander = curViewZoneId != sceneZoneId;
                // isChangeZone 决定普通层要不要发删除的ntf, 从本服的普通层切到缩略层 是需要发的
                builder.setPlayerId(getEntityId()).setIsByStander(isBystander).setIsChangeZone(isChangeZone);
                ownerActor().callTargetBigScene(curViewZoneId, builder.build());
            } else {
                SsAoiView.ClearViewAsk.Builder builder = SsAoiView.ClearViewAsk.newBuilder();
                builder.setPlayerId(getEntityId());
                ownerActor().callTargetAoiView(curViewZoneId, builder.build());
            }
        } catch (Exception e) {
            // 防止打断登出过程
            LOGGER.error("clearOldZoneView failed ", e);
        } finally {
            curViewLayer = 0;
            curViewZoneId = 0;
        }
    }

    private int sendUpdateZoneView(PlayerScene.Player_UpdateView_C2S msg) {
        int zoneId = msg.getZoneId();
        boolean isNormal = BigSceneConstants.isNormalLayer(msg.getLayer());
        int sceneZoneId = getCurSceneZone();
        if (isNormal) {
            SsScenePlayer.UpdatePlayerViewAsk.Builder builder = SsScenePlayer.UpdatePlayerViewAsk.newBuilder();
            builder.setP1(msg.getP1()).setP2(msg.getP2()).setP3(msg.getP3()).setP4(msg.getP4()).setLayerId(msg.getLayer());
            builder.setSessionRef(getOwner().getSessionComponent().getSessionRefData());
            builder.setPlayerId(getEntityId()).setZoneId(sceneZoneId).setEntityNumMax(msg.getEntityNumMax());
            SsScenePlayer.UpdatePlayerViewAns ans = ownerActor().callTargetBigScene(zoneId, builder.build());
            if (!ans.getIsOk()) {
                return 0;
            }
        } else {
            SsAoiView.UpdateViewAsk.Builder builder = SsAoiView.UpdateViewAsk.newBuilder();
            builder.setP1(msg.getP1()).setP2(msg.getP2()).setP3(msg.getP3()).setP4(msg.getP4()).setLayerId(msg.getLayer());
            builder.setSceneZoneId(sceneZoneId);
            // 附带session
            builder.setSessionRef(getOwner().getSessionComponent().getSessionRefData());
            SsAoiView.UpdateViewAns ans = ownerActor().callTargetAoiView(zoneId, builder.setPlayerId(getEntityId()).build());
            if (!ans.getIsOk()) {
                return 0;
            }
        }
        curViewLayer = msg.getLayer();
        curViewZoneId = zoneId;
        return zoneId;
    }
}

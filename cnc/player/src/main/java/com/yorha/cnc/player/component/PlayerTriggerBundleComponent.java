package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerTriggerHappenEvent;
import com.yorha.cnc.player.trigger.PlayerTriggerType;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.payment.PaymentResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerTriggerBundleModelProp;
import com.yorha.game.gen.prop.PlayerTriggerBundleProp;
import com.yorha.game.gen.prop.PlayerTriggerBundleRecordProp;
import com.yorha.game.gen.prop.PlayerTriggerBundleShownRecordProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.PlayerPayment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncPopupPurchase;
import res.template.*;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 糊脸礼包
 */
public class PlayerTriggerBundleComponent extends PlayerComponent {

    private static final Logger LOGGER = LogManager.getLogger(PlayerTriggerBundleComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerTriggerHappenEvent.class, PlayerTriggerBundleComponent::onTriggerHappen);
        // 这里是为了初始化clazz，勿删
        new PlayerTriggerType.Initialization();
    }

    private static void onTriggerHappen(PlayerTriggerHappenEvent event) {
        event.getPlayer().getTriggerBundleComponent().innerOnTriggerHappen(event.getHappenedTriggers());
    }

    private final Map<Long, ActorTimer> bundleExpireTimers = Maps.newHashMap();

    private long maxTriggerBundleId = 0L;

    public PlayerTriggerBundleComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad(boolean isRegister) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        calcMaxTriggerBundleWhenLoad(model);
        Instant now = SystemClock.nowInstant();
        for (PlayerTriggerBundleProp bundleProp : Lists.newArrayList(model.getBundles().values())) {
            if (bundleProp.getShowTsSec() > 0) {
                TriggerGoodsTemplate bundleTemplate = ResHolder.getTemplate(TriggerGoodsTemplate.class, bundleProp.getTemplateId());
                Instant expireTime = Instant.ofEpochSecond(bundleProp.getShowTsSec())
                        .plus(bundleTemplate.getLastingHours(), ChronoUnit.HOURS);
                if (expireTime.isAfter(now)) {
                    addBundleExpireTimer(Duration.between(now, expireTime), bundleProp.getId());
                } else {
                    tryExpireBundle(bundleProp.getId());
                }
            }
        }
    }

    private void calcMaxTriggerBundleWhenLoad(PlayerTriggerBundleModelProp model) {
        maxTriggerBundleId = model.getBundles().keySet().stream().max(Long::compareTo).orElse(0L);
        // checkNextTriggerBundleIdSafe(model);
    }

//    private void checkNextTriggerBundleIdSafe(PlayerTriggerBundleModelProp model) {
//        model.getBundles().keySet().forEach(bundleId -> {
//            if (bundleId >= maxTriggerBundleId) {
//                LOGGER.info("bundleId not safe bundleId={} nextTriggerBundleId={}", bundleId, maxTriggerBundleId);
//                needCheckNextTriggerBundleIdSafe = true;
//            }
//        });
//    }
//
//    private boolean containsNextTriggerBundleId(PlayerTriggerBundleModelProp model) {
//        if (!needCheckNextTriggerBundleIdSafe) {
//            // 没进入循环，一定是最大的
//            return false;
//        }
//        return model.getBundles().containsKey(maxTriggerBundleId);
//    }

    private void incMaxTriggerBundleId(PlayerTriggerBundleModelProp model) {
        ++maxTriggerBundleId;
        if (maxTriggerBundleId == Long.MAX_VALUE) {
            // 应该不可能吧？
            WechatLog.error("nextTriggerBundleId overflow! {}", getOwner());
            // 其实负数也没关系
            // needCheckNextTriggerBundleIdSafe = true;
            // maxTriggerBundleId = 1;
        }
    }

    private long getNextTriggerBundleId(PlayerTriggerBundleModelProp model) {
        // while (containsNextTriggerBundleId(model)) {
        //       incNextTriggerBundleId(model);
        // }
        incMaxTriggerBundleId(model);
        return maxTriggerBundleId;
    }

    private void innerOnTriggerHappen(List<Integer> happenedTriggers) {
        LOGGER.info("PlayerTriggerBundleComponent onTriggerHappen, event={}", happenedTriggers);
        for (Integer happenedTrigger : happenedTriggers) {
            TriggerTypeTemplate template = ResHolder.getTemplate(TriggerTypeTemplate.class, happenedTrigger);
            List<TriggerGoodsTemplate> bundleList = ResHolder.getResService(PaymentResService.class).getTriggerBundleList(template.getId());
            for (TriggerGoodsTemplate triggerGoodsTemplate : bundleList) {
                try {
                    if (tryAddBundle(triggerGoodsTemplate)) {
                        return;
                    }
                } catch (Exception e) {
                    WechatLog.error("tryAddBundle error:{} {}", getOwner(), happenedTriggers, e);
                }
            }
        }
    }

    private boolean tryAddBundle(TriggerGoodsTemplate template) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        int bundleTemplateId = template.getId();
        PlayerTriggerBundleRecordProp bundleRecord = model.getBundleRecordV(bundleTemplateId);
        int mainCityLevel = getOwner().getCityLevel();
        if (mainCityLevel < template.getMinTriggerCentreLevel() || mainCityLevel > template.getMaxTriggerCentreLevel()) {
            // 主堡等级限制
            LOGGER.debug("{} tryAddBundle but centreLevel limit {}", getOwner(), bundleTemplateId);
            return false;
        }
        if (template.getLimitTriggerTimes() > 0) {
            // 次数限制
            int historyTimes = bundleRecord == null ? 0 : bundleRecord.getTimes();
            if (historyTimes >= template.getLimitTriggerTimes()) {
                LOGGER.debug("{} tryAddBundle but times limit {}", getOwner(), bundleTemplateId);
                return false;
            }
        }
        if (template.getTriggerIntervalHours() > 0 && bundleRecord != null) {
            // 触发间隔限制
            if (bundleRecord.getLastBundleId() > 0) {
                PlayerTriggerBundleProp lastBundleProp = model.getBundlesV(bundleRecord.getLastBundleId());
                if (lastBundleProp != null && lastBundleProp.getShowTsSec() <= 0) {
                    // 上一个礼包触发了却还没呼出
                    LOGGER.debug("{} tryAddBundle but last bundle not yet show {}", getOwner(), bundleTemplateId);
                    return false;
                }
            }
            long lastHappenSec = bundleRecord.getLastHappenTsSec();
            long durationSec = SystemClock.nowOfSeconds() - lastHappenSec;
            if (durationSec < Duration.ofHours(template.getTriggerIntervalHours()).getSeconds()) {
                // 间隔限制
                LOGGER.debug("{} tryAddBundle but interval limit {} {}", getOwner(), bundleTemplateId, durationSec);
                return false;
            }
        }
        LOGGER.debug("{} tryAddBundle satisfied. {}", getOwner(), bundleTemplateId);
        addBundle(template);
        return true;
    }

    public void addBundle(TriggerGoodsTemplate template) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        final long bundleId = getNextTriggerBundleId(model);
        LOGGER.info("PlayerTriggerBundleComponent addBundle, configId={} bundleId={}", template.getId(), bundleId);
        final int bundleTemplateId = template.getId();
        PlayerTriggerBundleRecordProp bundleRecord = model.getBundleRecordV(bundleTemplateId);

        int goodsShownTimes = incAndGetShownTimes(template.getGoodsId());
        LOGGER.debug("{} addBundle. {} {}", getOwner(), bundleTemplateId, bundleId);
        PlayerTriggerBundleProp bundleProp = model.addEmptyBundles(bundleId).setId(bundleId);
        long nowSeconds = SystemClock.nowOfSeconds();
        bundleProp.setTriggerTsSec(nowSeconds)
                .setTemplateId(bundleTemplateId)
                .setGoodsShownTimes(goodsShownTimes);
        if (bundleRecord == null) {
            bundleRecord = model.addEmptyBundleRecord(bundleTemplateId)
                    .setTemplateId(bundleTemplateId);
        }
        bundleRecord.setLastHappenTsSec(nowSeconds)
                .setLastBundleId(bundleId)
                .setTimes(bundleRecord.getTimes() + 1);
    }

    public void handleShow(PlayerPayment.Player_TriggerBundleShow_C2S msg) {
        final long bundleId = msg.getBundleId();
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        PlayerTriggerBundleProp bundleProp = model.getBundlesV(bundleId);
        if (bundleProp == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "bundle not exist");
        }
        if (bundleProp.getShowTsSec() > 0) {
            LOGGER.warn("{} triggerBundle already show!. {}", getOwner(), bundleId);
            return;
        }
        final long nowSec = SystemClock.nowOfSeconds();
        bundleProp.setShowTsSec(nowSec);
        model.getBundleRecordV(bundleProp.getTemplateId())
                .setLastBundleId(nowSec);
        TriggerGoodsTemplate bundleTemplate = ResHolder.getTemplate(TriggerGoodsTemplate.class, bundleProp.getTemplateId());
        int lastingHours = bundleTemplate.getLastingHours();
        if (lastingHours > 0) {
            Duration delay = Duration.ofHours(lastingHours);
            addBundleExpireTimer(delay, bundleId);
        }
        int goodsId = bundleTemplate.getGoodsId();
        LOGGER.info("PlayerTriggerBundleComponent handleShow, configId={} bundleId={} starTsSec={}", bundleTemplate.getId(), bundleId, nowSec);
        sendPopupPurchaseQlog(goodsId, "Popup_bundle", "Popup", bundleProp.getGoodsShownTimes());
    }

    private int incAndGetShownTimes(int goodsId) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        PlayerTriggerBundleShownRecordProp shownRecord = model.getBundleShownRecordV(goodsId);
        if (shownRecord == null) {
            shownRecord = model.addEmptyBundleShownRecord(goodsId)
                    .setBundleId(goodsId);
        }
        final int shownTimes = shownRecord.getShownTimes() + 1;
        shownRecord.setShownTimes(shownTimes);
        return shownTimes;
    }

    public void sendPopupPurchaseQlog(int goodsId, String action, String position, int count) {
        try {
            ChargeGoodsTemplate chargeGoodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, goodsId);
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, chargeGoodsTemplate.getChargeSdkId());
            QlogCncPopupPurchase.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction(action)
                    .setPosition(position)
                    .setBundleId(goodsId)
                    .setPrice(String.valueOf(sdkTemplate.getPrice()))
                    .setCount(count)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendQlogCncPopupPurchase error:", e);
        }
    }

    private void addBundleExpireTimer(Duration delay, long bundleId) {
        ActorTimer timer = ownerActor().addTimer(getPlayerId() + "-" + bundleId, TimerReasonType.BUNDLE_EXPIRE, () -> tryExpireBundle(bundleId), delay.getSeconds(), TimeUnit.SECONDS);
        bundleExpireTimers.put(bundleId, timer);
    }

    private void tryExpireBundle(long bundleId) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        PlayerTriggerBundleProp bundleProp = model.getBundlesV(bundleId);
        if (bundleProp == null) {
            WechatLog.error("{} tryExpireBundle bundleProp not found. {}", getOwner(), bundleId);
            return;
        }
        LOGGER.info("{} bundleExpire {}", getOwner(), bundleId);
        model.removeBundlesV(bundleId);
        model.addEmptyExpiredBundles(bundleId)
                .setId(bundleId)
                .setTemplateId(bundleProp.getTemplateId())
                .setTriggerTsSec(bundleProp.getTriggerTsSec())
                .setShowTsSec(bundleProp.getShowTsSec())
                .setGoodsShownTimes(bundleProp.getGoodsShownTimes());
        cancelBundleTimer(bundleId);
    }

    private void cancelBundleTimer(long bundleId) {
        ActorTimer expireTimer = bundleExpireTimers.remove(bundleId);
        if (expireTimer != null) {
            expireTimer.cancel();
        }
    }

    public void checkApplyOrder(long bundleId, int goodsId) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        PlayerTriggerBundleProp bundleProp = model.getBundlesV(bundleId);
        if (bundleProp == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "bundle not exist");
        }
        TriggerGoodsTemplate template = ResHolder.getTemplate(TriggerGoodsTemplate.class, bundleProp.getTemplateId());
        if (template.getGoodsId() != goodsId) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, StringUtils.format("bundle goodsId not match! {} {}", bundleId, goodsId));
        }
    }

    public void afterGoodsBought(long bundleId) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        PlayerTriggerBundleProp bundleProp = model.getBundlesV(bundleId);
        int bundleTemplateId = 0;
        int goodsShownTimes = 0;
        if (bundleProp == null) {
            LOGGER.warn("{} TriggerBundle afterGoodsBought, prop=null! {}", getOwner(), bundleId);
            PlayerTriggerBundleProp expiredBundleProp = model.getExpiredBundlesV(bundleId);
            if (expiredBundleProp == null) {
                WechatLog.error("{} TriggerBundle maybe repeat buy! {}", getOwner(), bundleId);
            } else {
                LOGGER.info("{} TriggerBundle remove expired bundle! {}", getOwner(), bundleId);
                model.removeExpiredBundlesV(bundleId);
                bundleTemplateId = expiredBundleProp.getTemplateId();
                goodsShownTimes = expiredBundleProp.getGoodsShownTimes();
            }
        } else {
            bundleTemplateId = bundleProp.getTemplateId();
            goodsShownTimes = bundleProp.getGoodsShownTimes();
            LOGGER.info("{} buy TriggerBundle {} {}", getOwner(), bundleId, bundleProp.getTemplateId());
            model.removeBundlesV(bundleId);
            cancelBundleTimer(bundleId);
        }
        if (bundleTemplateId > 0) {
            TriggerGoodsTemplate bundleTemplate = ResHolder.getTemplate(TriggerGoodsTemplate.class, bundleTemplateId);
            final int goodsId = bundleTemplate.getGoodsId();
            sendPopupPurchaseQlog(goodsId, "Purchase_bundle", "Popup", goodsShownTimes);
        }
    }

    public void tryHandleBoughtLostBundle(long triggerBundleId) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        PlayerTriggerBundleProp expiredBundle = model.getExpiredBundlesV(triggerBundleId);
        if (expiredBundle != null) {
            LOGGER.info("tryHandleBoughtLostBundle remove expired bundle {} {}", getOwner(), triggerBundleId);
            model.removeExpiredBundlesV(triggerBundleId);
            TriggerGoodsTemplate template = ResHolder.getTemplate(TriggerGoodsTemplate.class, expiredBundle.getTemplateId());
            sendPopupPurchaseQlog(template.getGoodsId(), "Purchase_bundle", "Lost_bundle", expiredBundle.getGoodsShownTimes());
        } else {
            LOGGER.warn("tryHandleBoughtLostBundle expiredBundle not found {} {}", getOwner(), triggerBundleId);
        }
    }

    public static class TriggerBundleDTO {
        public PlayerTriggerBundleProp prop;
        public int goodsShownTimes = 0;
        public int templateId;

        public TriggerBundleDTO(PlayerTriggerBundleProp prop, int templateId) {
            this.prop = prop;
            this.templateId = templateId;
        }

        public TriggerGoodsTemplate getTemplate() {
            return ResHolder.getTemplate(TriggerGoodsTemplate.class, templateId);
        }

        @Override
        public String toString() {
            return "TriggerBundleDTO{" +
                    "prop=" + prop +
                    ", goodsShownTimes=" + goodsShownTimes +
                    ", templateId=" + templateId +
                    '}';
        }
    }

    public List<TriggerBundleDTO> popExpiredBundles(List<ActivityResellTriggerGoodsTemplate> conf) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();

        List<TriggerBundleDTO> expiredBundles = Lists.newLinkedList(model.getExpiredBundles().values()).stream()
                .map(prop -> new TriggerBundleDTO(prop, prop.getTemplateId()))
                .collect(Collectors.toList());
        // group同类型的礼包
        Map<Integer, List<TriggerBundleDTO>> typeMap = groupBy(expiredBundles, dto -> dto.getTemplate().getType());
        // 按等级从高到底排序
        for (List<TriggerBundleDTO> list : typeMap.values()) {
            list.sort(
                    Comparator.<TriggerBundleDTO>comparingInt(it -> it.getTemplate().getLevel())
                            .reversed()
            );
        }

        // pop出每个类型中前N个
        List<TriggerBundleDTO> popped = Lists.newLinkedList();
        for (ActivityResellTriggerGoodsTemplate confTemplate : conf) {
            List<TriggerBundleDTO> list = typeMap.getOrDefault(confTemplate.getBundleType(), Collections.emptyList());
            if (confTemplate.getCount() > 0 && !list.isEmpty()) {
                popped.addAll(list.subList(0, Math.min(list.size(), confTemplate.getCount())));
            }
        }
        Map<Integer, Integer> cachedShownTimes = Maps.newHashMap();
        for (TriggerBundleDTO popItem : popped) {
            model.removeExpiredBundlesV(popItem.prop.getId());
            int goodsId = popItem.getTemplate().getGoodsId();
            int shownTimes = incAndGetShownTimes(goodsId);
            if (cachedShownTimes.containsKey(goodsId)) {
                shownTimes = cachedShownTimes.get(goodsId);
            } else {
                cachedShownTimes.put(goodsId, shownTimes);
            }
            popItem.goodsShownTimes = shownTimes;
            sendPopupPurchaseQlog(goodsId, "Popup_bundle", "Lost_bundle", shownTimes);
        }
        LOGGER.info("PlayerTriggerBundleComponent popExpiredBundles {} : {}", getOwner(), popped);
        return popped;
    }


    private static <X, K> Map<K, List<X>> groupBy(Iterable<X> source, Function<X, K> selector) {
        HashMap<K, List<X>> map = Maps.newHashMap();
        for (X x : source) {
            K key = selector.apply(x);
            map.computeIfAbsent(key, i -> Lists.newLinkedList())
                    .add(x);
        }
        return map;
    }

    public void pushBackStillLostBundles(long bundleId, int templateId, int goodsShownTimes) {
        PlayerTriggerBundleModelProp model = getOwner().getProp().getTriggerBundleModel();
        model.addEmptyExpiredBundles(bundleId)
                .setId(bundleId)
                .setTemplateId(templateId)
                .setGoodsShownTimes(goodsShownTimes);
    }
}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerMultiServer;
import com.yorha.proto.StructMsg;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_MULTIR_SERVER)
public class PlayerMultiServerController {

    /**
     * 获取指定赛季下服务器列表
     */
    @CommandMapping(code = MsgType.PLAYER_GETZONESUNDERSEASON_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMultiServer.Player_GetZonesUnderSeason_C2S msg) {
        final Map<Integer, Integer> zoneIds = playerEntity.getMultiServerComponent().getZonesUnderSeason(msg.getZoneSeason());

        PlayerMultiServer.Player_GetZonesUnderSeason_S2C.Builder builder = PlayerMultiServer.Player_GetZonesUnderSeason_S2C.newBuilder();
        builder.putAllZoneIds(zoneIds);
        return builder.build();
    }

    /**
     * 获取指定赛季下服务器列表
     */
    @CommandMapping(code = MsgType.PLAYER_GETZONESSTATUS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMultiServer.Player_GetZonesStatus_C2S msg) {
        final Map<Integer, StructMsg.ZoneStatus> zoneStatusMap = playerEntity.getMultiServerComponent().getZonesStatus(msg.getZoneIdsList());

        PlayerMultiServer.Player_GetZonesStatus_S2C.Builder builder = PlayerMultiServer.Player_GetZonesStatus_S2C.newBuilder();
        builder.putAllServersStatus(zoneStatusMap);
        return builder.build();
    }
}

package com.yorha.cnc.player.gm;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * debug开关中心，有些debug指令希望开启神奇的功能
 *
 * <AUTHOR>
 */
public class DebugSwitchCenter {
    private static final Logger LOGGER = LogManager.getLogger(DebugSwitchCenter.class);

    private volatile boolean createArmyNoSoldierLimit = false;

    private DebugSwitchCenter() {
    }

    private static class InstanceHolder {
        private static final DebugSwitchCenter INSTANCE = new DebugSwitchCenter();
    }

    public static DebugSwitchCenter getInstance() {
        return DebugSwitchCenter.InstanceHolder.INSTANCE;
    }

    public boolean isCreateArmyNoSoldierLimit() {
        return createArmyNoSoldierLimit;
    }

    public void setCreateArmyNoSoldierLimit(boolean createArmyNoSoldierLimit) {
        LOGGER.warn("setCreateArmyNoSoldierLimit={}", createArmyNoSoldierLimit);
        this.createArmyNoSoldierLimit = createArmyNoSoldierLimit;
    }

}

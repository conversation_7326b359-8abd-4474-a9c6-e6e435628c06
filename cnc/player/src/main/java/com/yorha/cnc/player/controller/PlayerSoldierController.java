package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerSoldier;

/**
 * 处理兵营相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_SOLDIER)
public class PlayerSoldierController {

    @CommandMapping(code = MsgType.PLAYER_SOLDIERTRAIN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierTrain_C2S msg) {
        playerEntity.getSoldierComponent().handleTrain(msg.getSoldierId(), msg.getNum(), false);
        return PlayerSoldier.Player_SoldierTrain_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SOLDIERFASTTRAIN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierFastTrain_C2S msg) {
        playerEntity.getSoldierComponent().handleFastTrain(msg);
        return PlayerSoldier.Player_SoldierFastTrain_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SOLDIERGATHER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierGather_C2S msg) {
        playerEntity.getSoldierComponent().handleGather(msg.getSoldierId());
        return PlayerSoldier.Player_SoldierGather_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SOLDIERDISMISS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierDismiss_C2S msg) {
        playerEntity.getSoldierComponent().handleDismiss(msg);
        return PlayerSoldier.Player_SoldierDismiss_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SOLDIERLEVELUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierLevelUp_C2S msg) {
        playerEntity.getSoldierComponent().handleLevelUp(msg);
        return PlayerSoldier.Player_SoldierLevelUp_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_SOLDIERFASTLEVELUP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierFastLevelUp_C2S msg) {
        playerEntity.getSoldierComponent().handleFastLevelUp(msg);
        return PlayerSoldier.Player_SoldierFastLevelUp_S2C.getDefaultInstance();
    }

    /**
     * 暴兵
     */
    @CommandMapping(code = MsgType.PLAYER_SOLDIERRUSHTRAIN_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSoldier.Player_SoldierRushTrain_C2S msg) {
        playerEntity.getSoldierComponent().handleRushTrain(msg);
        return PlayerSoldier.Player_SoldierRushTrain_S2C.getDefaultInstance();
    }


}

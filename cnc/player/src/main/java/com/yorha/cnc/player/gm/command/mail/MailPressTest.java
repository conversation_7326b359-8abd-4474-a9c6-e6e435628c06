package com.yorha.cnc.player.gm.command.mail;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class MailPressTest implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int num = Integer.parseInt(args.get("num"));
        int saveContent = Integer.parseInt(args.get("saveContent"));
        actor.getEntity().getMailComponent().addBattleMail(num, saveContent > 0);

    }

    @Override
    public String showHelp() {
        return "MailPressTest num={value} saveContent={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAIL;
    }
}

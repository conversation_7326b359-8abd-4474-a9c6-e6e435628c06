package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.unit.PlayerCycleChargeGoodsUnit;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemRewardBox;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 自选礼包
 *
 * <AUTHOR>
 */
public class SelectChargeGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(SelectChargeGoods.class);

    @Override
    public void checkApply(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        final StructPB.ActivitySelectGoodsParamPB goodsParam = msg.getOrderParam().getSelectParam();
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, goodsParam.getActId(), goodsParam.getUnitId());
        unit.checkApply(goodsTemplate);
        ItemResService itemResService = ResHolder.getResService(ItemResService.class);

        final int configRewardSize = goodsTemplate.getSelectRewardsList().size();
        final int msgRewardSize = goodsParam.getSelectGoodsInfos().getDatasMap().size();
        // 配表宝箱数量 协议上传宝箱玩家奖励选择数量不一致
        if (configRewardSize != msgRewardSize) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectRewards not match " + configRewardSize + ":" + msgRewardSize);
        }

        for (int selectRewardId : goodsTemplate.getSelectRewardsList()) {
            // 挨个配表宝箱校验，协议都上传了正确的玩家选择
            if (!goodsParam.getSelectGoodsInfos().containsDatas(selectRewardId)) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectRewards without msg " + selectRewardId);
            }
            final StructPB.ActivitySelectGoodsInfoPB infoPb = goodsParam.getSelectGoodsInfos().getDatasMap().get(selectRewardId);
            // 自选宝箱配表是存在的
            final List<ItemRewardBox> selectReward = itemResService.getSelectRewardOrNull(selectRewardId);
            if (selectReward == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectRewards without selectReward " + selectRewardId);
            }
            // 自选宝箱框架设计上有隐含设计，为了兼容权重宝箱返回的居然是一个list，这里保险起见再校验一下
            if (selectReward.size() != 1) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectReward num invaild " + selectRewardId);
            }
            ItemRewardBox itemRewardBox = selectReward.get(0);
            if (itemRewardBox == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectReward itemRewardBox invaild " + selectRewardId);
            }
            // 确保玩家选择的index是一个合法的index,可以取出奖励的
            ItemReward reward = itemRewardBox.pickReward(infoPb.getIndex());
            if (reward == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "SelectReward index invaild " + selectRewardId);
            }
        }
    }

    @Override
    public void fillOrder(PlayerEntity player, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        final StructPB.ActivitySelectGoodsParamPB goodsParam = msg.getOrderParam().getSelectParam();
        final int actId = goodsParam.getActId();
        final int unitId = goodsParam.getUnitId();
        final ActivitySelectGoodsParamProp prop = orderProp.getExtInfo().getSelectGoodsParam();
        // 记录一下玩家的选择
        prop.mergeFromCs(msg.getOrderParam().getSelectParam());
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, actId, unitId);
        unit.recordOrderToken(orderProp.getToken());
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
    }

    @Override
    public boolean needRecordGoodsHistory(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        boolean needRecord = true;
        try {
            ActivitySelectGoodsParamProp extInfo = goodsOrder.getExtInfo().getSelectGoodsParam();
            PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, extInfo.getActId(), extInfo.getUnitId());
            if (!unit.isTokenSame(goodsOrder.getToken())) {
                // 订单不同，unit已经变化了，本次购买不应该记录次数
                needRecord = false;
                LOGGER.warn("SelectChargeGoods afterBaseDeliver, player {} cycle charge goods order token not same, order token {}, unit token {}",
                        player, goodsOrder.getToken(), unit.getCurOrderToken());
            }
            unit.clearOrderToken();
        } catch (Exception e) {
            LOGGER.error("SelectChargeGoods needRecordGoodsHistory, player {} cycle charge goods order token check failed", player, e);
        }
        return needRecord;
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity player, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        ActivitySelectGoodsParamProp extInfo = goodsOrder.getExtInfo().getSelectGoodsParam();
        PlayerCycleChargeGoodsUnit unit = findCycleChargeGoodsUnit(player, extInfo.getActId(), extInfo.getUnitId());
        unit.onGoodsBought(goodsOrder.getGoodsId());
        // 宝箱奖励
        ItemResService itemResService = ResHolder.getResService(ItemResService.class);
        Int32ActivitySelectGoodsInfoMapProp selectGoodsInfos = extInfo.getSelectGoodsInfos();
        AssetPackage.Builder asset = AssetPackage.builder();
        ActivitySelectGoodsRecordInfoProp selectGoodsRecordInfo = new ActivitySelectGoodsRecordInfoProp();
        selectGoodsRecordInfo.setGoodsId(goodsTemplate.getId());
        List<Struct.ItemPair> items = new ArrayList<>();
        for (int selectRewardId : goodsTemplate.getSelectRewardsList()) {
            int index = 1;
            if (selectGoodsInfos.containsKey(selectRewardId)) {
                index = selectGoodsInfos.get(selectRewardId).getIndex();
            } else {
                LOGGER.warn("SelectChargeGoods afterBaseDeliver not containIndex {} {} {}", player, goodsTemplate.getId(), selectRewardId);
            }
            List<ItemRewardBox> selectReward = itemResService.getSelectRewardOrNull(selectRewardId);
            if (selectReward == null) {
                WechatLog.error("SelectChargeGoods afterBaseDeliver selectReward is null {} {} {} ", player, goodsTemplate.getId(), selectRewardId);
                continue;
            }
            if (selectReward.isEmpty()) {
                WechatLog.error("SelectChargeGoods afterBaseDeliver selectReward is empty {} {} {} ", player, goodsTemplate.getId(), selectRewardId);
                continue;
            }
            ItemRewardBox itemRewardBox = selectReward.get(0);
            if (itemRewardBox == null) {
                WechatLog.error("SelectChargeGoods afterBaseDeliver itemRewardBox is null {} {} {} ", player, goodsTemplate.getId(), selectRewardId);
                continue;
            }
            ItemReward itemReward = itemRewardBox.pickReward(index);
            if (itemReward == null) {
                WechatLog.error("SelectChargeGoods afterBaseDeliver itemReward is null {} {} {} ", player, goodsTemplate.getId(), selectRewardId);
                continue;
            }
            asset.plusItem(itemReward.getItemTemplateId(), itemReward.getCount());
            ActivitySelectGoodsRewardInfoProp selectGoodsRewardInfo = new ActivitySelectGoodsRewardInfoProp();
            selectGoodsRewardInfo.setSelectRewardId(selectRewardId);
            selectGoodsRewardInfo.getItemPair()
                    .setItemTemplateId(itemReward.getItemTemplateId())
                    .setCount(itemReward.getCount());
            selectGoodsRecordInfo.getRewardInfos().put(selectRewardId, selectGoodsRewardInfo);
            items.add(Struct.ItemPair.newBuilder().setItemTemplateId(itemReward.getItemTemplateId()).setCount(itemReward.getCount()).build());
        }
        player.getAssetComponent().give(asset.build(), CommonEnum.Reason.ICR_BUY_GOODS, String.valueOf(goodsTemplate.getId()));
        // 购买记录
        if (!selectGoodsRecordInfo.getRewardInfos().isEmpty()) {
            unit.recordSelectRecord(goodsTemplate.getId(), selectGoodsRecordInfo);
        }
        LOGGER.info("SelectChargeGoods afterBaseDeliver {} {} {}", player, goodsTemplate.getId(), items);
        return items;
    }

    private PlayerCycleChargeGoodsUnit findCycleChargeGoodsUnit(PlayerEntity player, int actId, int unitId) throws GeminiException {
        BasePlayerActivityUnit unit = player.getActivityComponent().findActiveUnit(actId, unitId);
        if (!(unit instanceof PlayerCycleChargeGoodsUnit)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "actId: " + actId + ", unitId: " + unitId);
        }
        return (PlayerCycleChargeGoodsUnit) unit;
    }
}

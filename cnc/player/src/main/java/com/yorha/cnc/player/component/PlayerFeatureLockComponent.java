package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.BuildFinEvent;
import com.yorha.cnc.player.event.MainTaskFinishEvent;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.player.FeatureLockService;
import com.yorha.game.gen.prop.PlayerFeatureLockModelProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.FeatureUnlockTemplate;
import res.template.InitLockFeatureTemplate;

import java.util.Collection;
import java.util.List;

/**
 * 功能解锁模块
 *
 * <AUTHOR>
 */
public class PlayerFeatureLockComponent extends PlayerComponent {

    private static final Logger LOGGER = LogManager.getLogger(PlayerFeatureLockComponent.class);

    public PlayerFeatureLockComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerFeatureLockModelProp getProp() {
        return getOwner().getProp().getFeatureLockModel();
    }

    static {
        EntityEventHandlerHolder.register(MainTaskFinishEvent.class, PlayerFeatureLockComponent::refreshWithMainTask);
        EntityEventHandlerHolder.register(BuildFinEvent.class, PlayerFeatureLockComponent::refreshWithBuildFin);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            // 每个号初始上锁状态
            for (InitLockFeatureTemplate template : ResHolder.getInstance().getListFromMap(InitLockFeatureTemplate.class)) {
                lockModule(template.getLockedFeature());
            }
        }
    }

    @Override
    public void onLogin() {
        super.onLogin();
        // 遍历一边配置,尝试模块上锁，解锁状态
        refreshWithLogin();
    }

    private void refreshWithLogin() {
        boolean fix = false;
        for (FeatureUnlockTemplate template : ResHolder.getInstance().getListFromMap(FeatureUnlockTemplate.class)) {
            CommonEnum.ModuleEnum moduleEnum = template.getUnLock();
            // 任务解锁条件
            boolean taskSatisfied = getOwner().getTaskComponent().isMainTaskFinish(template.getTask());
            // 内城建筑解锁条件
            boolean buildStatisfied = !template.getBuildlevelPairList().isEmpty();
            boolean unlock = false;
            // 根据解锁条件判断该模块的解锁情况
            switch (template.getConditionrelation()) {
                case CE_OR: {
                    // 或的解锁条件
                    if (taskSatisfied || buildStatisfied) {
                        unlock = true;
                    }
                    break;
                }
                case CE_AND: {
                    // 与的解锁条件
                    if (taskSatisfied && buildStatisfied) {
                        unlock = true;
                    }
                    break;
                }
                default:
                    throw new GeminiException("PlayerFeatureLockComponent refreshWithLoad not support {}", template.getConditionrelation());
            }
            // 刷新解锁状态
            if (unlock) {
                if (!isModuleLock(moduleEnum)) {
                    continue;
                }
                unLockModule(moduleEnum);
                fix = true;
            } else {
                if (isModuleLock(moduleEnum)) {
                    continue;
                }
                lockModule(moduleEnum);
                fix = true;
            }
        }
        if (fix) {
            LOGGER.info("PlayerFeatureLockComponent refreshWithLogin fix {}", getOwner());
        }
    }

    /**
     * 根据任务刷新功能解锁
     *
     * @param event
     */
    private static void refreshWithMainTask(MainTaskFinishEvent event) {
        LOGGER.info("PlayerFeatureLockComponent refreshWithMainTask {} {}", event.getPlayer(), event);
        List<FeatureUnlockTemplate> templates = ResHolder.getResService(FeatureLockService.class).getTaskToTemplateMap(event.getTask());
        PlayerEntity playerEntity = event.getPlayer();
        tryUnlockFeature(templates, playerEntity);
    }

    /**
     * 根据建筑刷新功能解锁
     *
     * @param event
     */
    private static void refreshWithBuildFin(BuildFinEvent event) {
        LOGGER.info("PlayerFeatureLockComponent refreshWithBuildFin {} {}", event.getPlayer(), event);
        List<FeatureUnlockTemplate> templates = ResHolder.getResService(FeatureLockService.class).getBuildToTemplateMap(event.getType().getNumber());
        PlayerEntity playerEntity = event.getPlayer();
        tryUnlockFeature(templates, playerEntity);
    }

    private static void tryUnlockFeature(Collection<FeatureUnlockTemplate> templates, PlayerEntity playerEntity) {
        LOGGER.info("PlayerFeatureLockComponent tryUnlockFeature {} {}", playerEntity, templates);
        // 全解锁
        if (playerEntity.getFeatureLockComponent().isAllModuleUnlock()) {
            return;
        }
        for (FeatureUnlockTemplate template : templates) {
            CommonEnum.ModuleEnum moduleEnum = template.getUnLock();
            // 已解锁
            if (!playerEntity.getFeatureLockComponent().isModuleLock(moduleEnum)) {
                continue;
            }
            // 任务解锁条件
            boolean taskSatisfied = playerEntity.getTaskComponent().isMainTaskFinish(template.getTask());
            // 内城建筑解锁条件
            boolean buildStatisfied = !template.getBuildlevelPairList().isEmpty();
            // 或的解锁条件
            if (template.getConditionrelation() == CommonEnum.ConditionEnum.CE_OR) {
                if (taskSatisfied || buildStatisfied) {
                    playerEntity.getFeatureLockComponent().unLockModule(moduleEnum);
                }
                continue;
            }
            // 与的解锁条件
            if (template.getConditionrelation() == CommonEnum.ConditionEnum.CE_AND) {
                if (taskSatisfied && buildStatisfied) {
                    playerEntity.getFeatureLockComponent().unLockModule(moduleEnum);
                }
            }
        }
    }

    /**
     * 模块解锁
     *
     * @param moduleEnum 模块枚举
     */
    public void unLockModule(CommonEnum.ModuleEnum moduleEnum) {
        if (!isModuleLock(moduleEnum)) {
            return;
        }
        long oldFeatureLockInfo = getProp().getFeatureLockInfo();
        long newFeatureLockInfo = oldFeatureLockInfo & ~(1L << moduleEnum.getNumber());
        getProp().setFeatureLockInfo(newFeatureLockInfo);
        LOGGER.info("PlayerFeatureLockComponent unLockModule {} {}", getPlayerId(), moduleEnum);
    }

    /**
     * 模块上锁
     *
     * @param moduleEnum 模块枚举
     */
    public void lockModule(CommonEnum.ModuleEnum moduleEnum) {
        if (isModuleLock(moduleEnum)) {
            return;
        }
        long oldFeatureLockInfo = getProp().getFeatureLockInfo();
        long newFeatureLockInfo = oldFeatureLockInfo | (1L << moduleEnum.getNumber());
        getProp().setFeatureLockInfo(newFeatureLockInfo);
        LOGGER.info("PlayerFeatureLockComponent lockModule {} {}", getPlayerId(), moduleEnum);
    }

    /**
     * 模块是否上锁
     *
     * @param moduleEnum 模块枚举
     */
    public boolean isModuleLock(CommonEnum.ModuleEnum moduleEnum) {
        long result = getProp().getFeatureLockInfo() >> moduleEnum.getNumber();
        return (result & 1L) > 0;
    }

    /**
     * 模块全解锁
     *
     * @return
     */
    public boolean isAllModuleUnlock() {
        return getProp().getFeatureLockInfo() == 0;
    }
}

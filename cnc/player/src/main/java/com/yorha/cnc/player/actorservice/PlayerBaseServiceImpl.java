package com.yorha.cnc.player.actorservice;

import com.google.protobuf.ByteString;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.chat.component.ChatPlayerComponent;
import com.yorha.cnc.player.component.PlayerAntiAddictionComponent;
import com.yorha.cnc.player.component.PlayerComponent;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.cnc.player.friend.component.FriendPlayerComponent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.PlayerBaseService;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.enums.WhitePermissions;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.internalPlayerList.InternalPlayerListResService;
import com.yorha.common.resource.resservice.whiteList.WhiteListResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.PlayerUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.Language;
import com.yorha.proto.CommonEnum.SessionCloseReason;
import com.yorha.proto.Core.Code;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PlayerBaseServiceImpl implements PlayerBaseService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerBaseServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerBaseServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    /**
     * 登录（含注册）
     */
    @Override
    public void handleLoginPlayerCmd(SsPlayer.LoginPlayerCmd ask) {
        IActorRef sessionRef = playerActor.sender();
        String sessionId = null;
        if (sessionRef != null) {
            sessionId = sessionRef.getActorId();
        }
        String openId = ask.getOpenId();
        long playerId = playerActor.getPlayerId();

        LOGGER.info("[login] sessionId={} openId={} playerId={} handleLoginPlayerCmd start ask={} actor={}", sessionId, openId, playerId, ask, playerActor);
        int zoneId = playerActor.getZoneId();
        boolean isRegister = ask.getSessionParamInfo().getRegister();
        boolean isWhite = ask.getSessionParamInfo().getIsWhite();
        CommonMsg.SessionParamInfo sessionParamInfo = ask.getSessionParamInfo();
        CommonMsg.ClientInfo clientInfo = sessionParamInfo.getClientInfo();
        String clientIp = sessionParamInfo.getClientIp();
        String midasPf = ask.getMidasPf();
        final String token = ask.getIntlNtfToken();
        try {
            if (isRegister) {
                register(ask);
            }
            PlayerEntity playerEntity = playerActor.getOrLoadEntity();
            if (playerEntity == null) {
                throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
            }
            // 兼容线上玩家，对已创建的角色设置出生服开服时间戳
            if (ZoneContext.isServerOpen() && playerEntity.getProp().getZoneModel().getBornZoneOpenTsMs() <= 0) {
                playerEntity.getProp().getZoneModel().setBornZoneOpenTsMs(ZoneContext.getServerOpenTsMs());
            }

            FriendPlayerEntity friendPlayerEntity = playerActor.getOrLoadFriendPlayerEntity();
            if (friendPlayerEntity == null) {
                throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
            }

            ChatPlayerEntity chatPlayerEntity = playerActor.getOrLoadChatPlayerEntity();
            if (chatPlayerEntity == null) {
                throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
            }

            playerEntity.getProp().getBasicInfo().setMidasPf(midasPf);
            // 登录流程首先顶号判断
            playerEntity.getSessionComponent().tryKickOutPreSession();
            // 走完了顶号，设置自己的clientInfo和ip
            if (!isRegister) {
                playerEntity.getSessionComponent().setClientInfo(clientInfo, false);
                playerEntity.getSessionComponent().setClientIp(clientIp);
                // 在每次登陆时为playerEntity打上内部玩家的标签
                setInternalPlayerTag(playerEntity, clientInfo);
            }

            // 玩家各组件的login逻辑
            playerEntity.getProp().setId(playerId);
            onLogin(playerEntity, sessionRef, isWhite, token);

            SsMonitor.MonitorDailyPlayerNumCmd.Builder builder = SsMonitor.MonitorDailyPlayerNumCmd.newBuilder();
            builder.setPlayerId(playerId);
            playerActor.tell(RefFactory.ofLocalMonitor(), builder.build());

            ntfGatePlayerBound(sessionRef, openId, zoneId, playerId, playerEntity.getName(), null);
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                WechatLog.error("exception_perf player login handleLoginPlayerCmd fail! ", e);
            }
            Code codeFromException = ErrorCodeUtils.getCodeFromException(e);
            LOGGER.info("{} atPlayer login fail, sendErrorCode {} to {}", LogKeyConstants.GAME_PLAYER_LOGIN, codeFromException, playerActor);
            // 给客户端的只能是ServerIsFull 所以只能改写下了
            if (ErrorCode.isServerFullError(codeFromException.getId())) {
                codeFromException = ErrorCode.SERVER_IS_FULL.getCode();
            }
            ntfGatePlayerBound(sessionRef, openId, zoneId, playerId, "", codeFromException);
            playerActor.forceDestroy("player register failed" + codeFromException);
        }
    }

    private void register(SsPlayer.LoginPlayerCmd ask) {
        // 注册前置工作
        if (playerActor.getEntityOrNull() != null) {
            LOGGER.error("handleLoginPlayerCmd account_register_fail_total register but player entity still exists");
            MonitorUnit.ACCOUNT_REGISTER_FAIL_TOTAL.labels(ServerContext.getBusId(), ErrorCode.LOGIN_SIGN_WRONG.name()).inc();
            throw new GeminiException(ErrorCode.LOGIN_SIGN_WRONG);
        }
        String openId = ask.getOpenId();
        int zoneId = playerActor.getZoneId();
        // 每个服下创建角色数量限制
        PlayerUtils.checkAccountRegisterLimit(openId, zoneId, playerActor);
        // 账号下的第几个角色
        int roleSeqOfAccount = queryRoleSeqOfAccount(openId);

        CommonMsg.SessionParamInfo sessionParamInfo = ask.getSessionParamInfo();
        CommonMsg.ClientInfo clientInfo = sessionParamInfo.getClientInfo();
        String clientIp = sessionParamInfo.getClientIp();
        // 创建entity，此时entity还未落库，在DbComponent中落库的，考虑提前到create完立刻存库
        PlayerEntity playerEntity = playerActor.createPlayerEntity(openId, roleSeqOfAccount);
        playerEntity.getSessionComponent().setClientInfo(clientInfo, false);
        int platId = clientInfo.getPlatformId();
        if ((platId > 1) || (platId < 0)) {
            LOGGER.warn("PlayerSessionComponent setClientInfo platId unsupported {}", clientInfo.getPlatformId());
            platId = 1;
        }


        // ---------------------- 初始化一些prop

        PlayerProp playerEntityProp = playerEntity.getProp();
        playerEntityProp.getBasicInfo().setBornPlatId(platId).setRegChannel(clientInfo.getChannelId());
        playerEntity.getSessionComponent().setClientIp(clientIp);
        // 注册前先打tag，是新手
        boolean debugStartNewbie = ask.getDebugStartNewbie();
        playerEntity.getNewbieComponent().setNewbie(debugStartNewbie);
        playerEntity.getSettingComponent().setRegisterClientIp(clientIp);
        playerEntity.getPowerComponent().setNewbie(true);

        playerEntityProp.getPlayerRecruitModel().getSilverRecruitPoolInfo().setRecruitPoolType(CommonEnum.RecruitPoolType.RPT_SILVER);
        playerEntityProp.getPlayerRecruitModel().getGoldRecruitPoolInfo().setRecruitPoolType(CommonEnum.RecruitPoolType.RPT_GOLD);
        playerEntityProp.getResourceProduce().setNewbieProduceTaken(false);
        playerEntityProp.getPlayerSoldierInBuilding().setFirstTrainTimeUsed(false);

        // --------------------- 初始化完成


        // 账号注册时需要为playerEntity打上内部玩家的标签
        setInternalPlayerTag(playerEntity, clientInfo);
        // 通知大世界创建mapPlayer
        boolean ignoreRegisterLimit = ResHolder.getResService(WhiteListResService.class).hasWhitePermission(
                zoneId, openId, clientInfo.getDeviceId(), clientIp, WhitePermissions.WP_REGISTER_LIMIT);
        SsScenePlayer.FirstEnterBigSceneAns ans = syncFirstEnterBigScene(playerEntity,
                ask.getSessionParamInfo().getClientInfo().getHardwareLevel(), ask.getSessionParamInfo().getClientInfo().getLanguage(), ignoreRegisterLimit);
        //创建失败
        if (!ErrorCode.isOK(ans.getCode())) {
            LOGGER.error("handleLoginPlayerCmd account_register_fail_total first enter scene fail, code={}", ans.getCode());
            MonitorUnit.ACCOUNT_REGISTER_FAIL_TOTAL.labels(ServerContext.getBusId(), ErrorCode.getCodeName(ans.getCode().getId())).inc();
            throw new GeminiException(ans.getCode().getId());
        }
        playerEntity.getQlogComponent().setBornPoint(ans.getBornPoint());
        playerEntityProp.getZoneModel()
                .setBornZoneId(playerActor.getZoneId())
                .setBornZoneInex(ans.getBornIndex())
                .setBornZoneOpenTsMs(ZoneContext.getServerOpenTsMs());
        // 初始化名字
        playerEntityProp.getAvatarModel().getCardHead().setName(ans.getName());
        // 设置出生州
        playerEntity.getNewbieComponent().setMapId(ans.getBornRegionId());
        // 插入db
        playerEntity.getDbComponent().insertPlayerTable();
        playerEntity.getDbComponent().insertSimplePlayerTable();
        // 创建PlayerEntity的子entity
        createPlayerSubEntity(playerActor.getPlayerId());
        // 设置属性监听
        playerEntity.getPlayerPropComponent().setPropertyChangeListener();
        // 注册也会调起一次load逻辑
        playerActor.onLoad(true);
        // 导量Qlog
        playerEntity.getQlogComponent().sendDriveTrafficQlog(roleSeqOfAccount, clientInfo.getRegisterDriveTrafficReason(),
                clientInfo.getHardwareLevel(), ans.getBornRegionId(), ans.getBornPoint());
        // 注册完毕
        playerEntity.setRegister(false);
    }

    private int queryRoleSeqOfAccount(String openId) {
        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder();
        req.setOpenId(openId);
        try {
            GetByPartKeyResult<TcaplusDb.AccountRoleTable.Builder> ans = playerActor
                    .callGameDb(new SelectAsk<>(
                            req,
                            GetByPartKeyOption.newBuilder().withGetAllFields(false).build())
                    );
            if (!ans.isOk()) {
                if (ans.isRecordNotExist()) {
                    LOGGER.info("PlayerBaseServiceImpl queryRoleSeqOfAccount, requestId={}, valueSize={}, openId={}", ans.requestId, 0, openId);
                    return 1;
                }
                LOGGER.error("PlayerBaseServiceImpl queryRoleSeqOfAccount, db fail, requestId={}, code={}, openId={}", ans.requestId, ans.getCode(), openId);
                return -1;
            }
            final int size = ans.getValues().size();
            LOGGER.info("PlayerBaseServiceImpl queryRoleSeqOfAccount, requestId={}, valueSize={}, openId={}", ans.requestId, size, openId);
            // 已有角色数+1
            return size + 1;
        } catch (Exception e) {
            LOGGER.error("PlayerBaseServiceImpl queryRoleSeqOfAccount, openId={}, ", openId, e);
            return -1;
        }
    }

    public void onLogin(PlayerEntity playerEntity, IActorRef sessionRef, boolean isWhite, final String intlToken) {
        LOGGER.info("{} atPlayer onLogin start, entity={}", LogKeyConstants.GAME_PLAYER_LOGIN, playerEntity);
        // 这种check属于在期望内的失败，理应放在com.onLogin之前，onLogin中都是期望会成功的操作
        if (!isWhite) {
            playerEntity.permissionRoleCheck();
        }

        // 上线期间不淘汰
        playerActor.cancelReceiveTimeout();

        playerEntity.getSessionComponent().setSessionRef(sessionRef);

        // 防沉迷
        if (PlayerAntiAddictionComponent.isAntiAddictionOpen()) {
            if (!playerEntity.getAntiAddictionComponent().judgeTimingWhenLogin()) {
                throw new GeminiException(ErrorCode.ANTI_ADDICTION, "player trigger anti addiction");
            }
        }

        // 调用玩家所有组件的 onLogin
        playerEntity.callAllComponentsSafe(PlayerComponent::onLogin);
        // 下发属性系统newEntity,postLogin中会下发scenePlayer、sceneCity等等ntf，这些ntf在客户端依赖PlayerProp的初始化
        playerEntity.getPlayerPropComponent().ntfFullPlayerPropAtLogin();
        // 大世界登录失败 走服务器已满
        Language language = playerEntity.getClientLanguage();
        SsSceneDungeon.PlayerLoginAns playerLoginAns = playerEntity.getSceneMgrComponent().playerLogin(intlToken, language);
        // 调用FriendPlayerEntity的login
        FriendPlayerEntity friendPlayerEntity = playerActor.getFriendPlayerEntity();
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::onLogin);
        // 调用ChatPlayerEntity的login
        ChatPlayerEntity chatPlayerEntity = playerActor.getChatPlayerEntity();
        chatPlayerEntity.callAllComponentsSafe(ChatPlayerComponent::onLogin);
        // 调用玩家所有组件的 postLogin
        playerEntity.callAllComponentsSafe((it) -> it.postLogin(playerLoginAns));
        // 调用FriendPlayerEntity所有组件的postLogin
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::postLogin);
        // 调用ChatPlayerEntity所有组件的postLogin
        chatPlayerEntity.callAllComponentsSafe(ChatPlayerComponent::postLogin);
        playerEntity.getPowerComponent().updateNewbiePower();
        LOGGER.info("{} atPlayer onLogin end, entity={} loginAns={}", LogKeyConstants.GAME_PLAYER_LOGIN, playerEntity, playerLoginAns);
        // 统计登录
        // FIXME: 服务不可用 ServerStatisticLog.getInstance().record2StatisticServer(ServerStatisticLog.ServerStatisticType.LOGIN);
    }

    private void ntfGatePlayerBound(IActorRef receiver, String openId, int zoneId, long playerId, String playerName, Code code) {
        if (receiver == null) {
            throw new GeminiException("receiver is null");
        }
        if (receiver == IActorRef.NOBODY) {
            throw new GeminiException("receiver is nobody");
        }
        LOGGER.info("[login] sessionId={} openId={} playerId={} ntfGatePlayerBound code={} ", receiver.getActorId(), openId, playerId, code == null ? "null" : code.getId());
        SsGateSession.PlayerBoundCmd.Builder builder = SsGateSession.PlayerBoundCmd.newBuilder();
        builder.setPlayerId(playerId);
        builder.setOpenId(openId);
        builder.setZoneId(zoneId);
        builder.setPlayerName(playerName);
        if (code != null) {
            builder.setCode(code);
        }
        playerActor.tell(receiver, builder.build());
    }

    private SsScenePlayer.FirstEnterBigSceneAns syncFirstEnterBigScene(PlayerEntity playerEntity, final int hardwareLevel, Language clientLanguage, boolean isWhite) {
        SsScenePlayer.FirstEnterBigSceneAsk.Builder builder = SsScenePlayer.FirstEnterBigSceneAsk.newBuilder();
        builder.setPlayerId(playerEntity.getPlayerId())
                .setCardHead(playerEntity.getProp().getAvatarModel().getCardHead().getCopySsBuilder())
                .setCreateTime(playerEntity.getProp().getCreateTime())
                .setHardwareLevel(hardwareLevel)
//                .setActualLanguage(actualLanguage)
                .setClientLanguage(clientLanguage)
                .setIsInternalPlayer(playerEntity.isInternalPlayer())
                .setIsWhite(isWhite)
                .setOpenId(playerEntity.getOpenId());
        return playerActor.callBigScene(builder.build());
    }

    private void createPlayerSubEntity(long playerId) {
        // 创建FriendPlayerEntity
        FriendPlayerEntity friendPlayerEntity = playerActor.createFriendPlayerEntity(playerId);
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::onRegister);
        // 插入db
        friendPlayerEntity.getDbComponent().saveDb();
        friendPlayerEntity.callAllComponentsSafe(FriendPlayerComponent::postRegister);
        playerActor.onFriendEntityLoad();
        // 创建ChatPlayerEntity
        ChatPlayerEntity chatPlayerEntity = playerActor.createChatPlayerEntity(playerId);
        chatPlayerEntity.callAllComponentsSafe(ChatPlayerComponent::onRegister);
        // 插入db
        chatPlayerEntity.getDbComponent().saveDb();
        chatPlayerEntity.callAllComponentsSafe(ChatPlayerComponent::postRegister);
        playerActor.onChatEntityLoad();
    }

    /**
     * 正常断线
     */
    @Override
    public void handleOnPlayerDisconnectCmd(SsPlayer.OnPlayerDisconnectCmd ask) {
        PlayerEntity player = playerActor.getEntityOrNull();
        String sessionId = null;
        String openId = null;
        long playerId = playerActor.getPlayerId();
        if (player != null) {
            sessionId = player.getSessionComponent().getSessionId();
            openId = player.getOpenId();
        }
        LOGGER.info("[logout] sessionId={} openId={} playerId={} handleOnPlayerDisconnectCmd {} {}", sessionId, openId, playerId, ask, playerActor);

        CommonEnum.SessionCloseReason closeReason = ask.getCloseReason();

        if (player == null) {
            LOGGER.info("{} logout but has no player entity", playerActor);
            playerActor.forceDestroy("OnPlayerDisconnectCmd but player is null");
            return;
        }
        PlayerEntity playerEntity = playerActor.getEntity();
        if (!playerEntity.isOnline()) {
            // 因为session在tryLogin环节断联，也要发送disconnect给player，但player可能刚刚登陆失败，所以也没有online
            LOGGER.info("{} not online", playerActor);
            return;
        }
        // 保护逻辑，只有player当前持有的session可以断连，其余session不认可
        if (playerActor.getEntity().getSessionComponent().checkDisconnect(playerActor.sender())) {
            playerEntity.onDisconnect(closeReason);
            // 统计登出
            // FIXME: 服务不可用 ServerStatisticLog.getInstance().record2StatisticServer(ServerStatisticLog.ServerStatisticType.LOGOUT);
        } else {
            LOGGER.info("SessionId={} disconnect, Player={} Not Match, skip!", playerActor.sender(), playerId);
        }
    }

    @Override
    public void handleRecvClientMsgCmd(SsPlayer.RecvClientMsgCmd ask) {
        int msgType = ask.getMsgType();
        int seqId = ask.getSeqId();
        ByteString msgBytes = ask.getMsgBytes();

        playerActor.onReceiveClientMsg(msgType, seqId, null, msgBytes);
    }

    @Override
    public void handleKickOffPlayerCmd(SsPlayer.KickOffPlayerCmd ask) {
        final long playerId = playerActor.getPlayerId();

        if (this.playerActor.getEntityCurZoneId() != this.playerActor.getZoneId()) {
            LOGGER.error("handleKickOffPlayerCmd sender={} try kick player={}, but player entity is migrated, zoneId={}, entityCurZoneId={}",
                    playerActor.sender(), playerId, playerActor.getZoneId(), playerActor.getEntityCurZoneId());
            return;
        }

        final PlayerEntity playerEntity = playerActor.getEntityOrNull();

        // 如果由于顶号、封号、封角色被踢，则当前账号不允许在接收推送
        switch (ask.getCloseReason()) {
            case SCR_KICKOFF_ACCOUNT:
            case SCR_ACCOUNT_BAN:
            case SCR_PLAYER_BAN:
                this.playerActor.tellBigScene(SsScenePlayer.SyncPlayerPushNtfInfoAsk.newBuilder()
                        .setPlayerId(playerId)
                        .setIntlNtfToken("")
                        .build()
                );
                LOGGER.info("handleKickOffPlayerCmd sender={} try kick player={}, closeReason={}, and SyncPlayerPushNtfInfoAsk token empty",
                        playerActor.sender(), playerId, ask.getCloseReason());
                break;
            default:
                LOGGER.info("handleKickOffPlayerCmd sender={} try kick player={}, closeReason={}", playerActor.sender(), playerId, ask.getCloseReason());
        }

        // 兜底：entity已淘汰
        if (playerEntity == null) {
            LOGGER.warn("handleKickOffPlayerCmd sender={} try kick player={} whose entity is empty", playerActor.sender(), playerId);
            return;
        }

        // 兜底：entity已离线
        if (!playerEntity.isOnline()) {
            LOGGER.warn("handleKickOffPlayerCmd sender={} try kick player={} whose entity is not online", playerActor.sender(), playerId);
            return;
        }

        // 已有规则，踢人
        if (ask.hasCloseReason()) {
            playerEntity.kickOffMe(ask.getCloseReason());
            return;
        }

        // 默认规则
        playerEntity.kickOffMe(SessionCloseReason.SCR_KICKOFF_ACCOUNT);
    }

    private void setInternalPlayerTag(PlayerEntity playerEntity, CommonMsg.ClientInfo clientInfo) {
        long playerId = playerActor.getPlayerId();
        int zoneId = playerActor.getZoneId();
        if (!playerEntity.isInternalPlayer()) {
            InternalPlayerListResService internalPlayerService = ResHolder.getResService(InternalPlayerListResService.class);
            String deviceId = clientInfo.getDeviceId();
            boolean ans = internalPlayerService.isInternalDevice(deviceId);
            if (ans) {
                LOGGER.info("{} atPlayer internal tag, playerId={},player deviceId={}, zoneId={}", LogKeyConstants.GAME_PLAYER_LOGIN, playerId, deviceId, zoneId);
                playerEntity.getProp().getBasicInfo().setInternalPlayerTag(true);
            }
        }
    }

    @Override
    public void handleApplyDataPatchCmd(SsPlayer.ApplyDataPatchCmd ask) {
        PlayerEntity playerEntity = playerActor.getEntityOrNull();
        if (playerEntity == null) {
            LOGGER.warn("handleApplyDataPatchCmd try apply data patch playerEntity is empty={}", playerActor.getPlayerId());
            return;
        }
        LOGGER.info("handleApplyDataPatchCmd version={}", ask.getVersion());
        playerEntity.applyDataPatch(playerEntity.getProp(), false);
    }
}

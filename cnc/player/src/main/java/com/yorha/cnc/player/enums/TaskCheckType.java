package com.yorha.cnc.player.enums;


import com.yorha.cnc.player.task.checker.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;

import java.util.HashMap;
import java.util.Map;

import static com.yorha.proto.CommonEnum.TaskType;

/**
 * 任务checker映射
 *
 * <AUTHOR>
 */
public enum TaskCheckType {
    //	TASK_NONE(0,"默认类型"),
    BUILDING_LEVEL(TaskType.TT_BUILDING_LEVEL, "某个建筑升级达到多少级", new BuildLevelChecker()),
    CONSTRUCT_BUILD_XX_COUNT(TaskType.TT_CONSTRUCT_BUILD_XX_COUNT, "拥有x个类型建筑", new BuildTypeNumChecker()),
    CONSTRUCT_BUILDID_XX_COUNT(TaskType.TT_CONSTRUCT_BUILDID_XX_COUNT, "建造xxx建筑", new BuildNumChecker()),

    LEVEL_BUILD_XX_COUNT(TaskType.TT_LEVEL_BUILD_XX_COUNT, "升级建筑x次", new BuildFinTimesChecker()),
    CONSTRUCT_XX_NUM_XX_LEVEL_BUILD(TaskType.TT_CONSTRUCT_XX_NUM_XX_LEVEL_BUILD, "任意x个y_y_y建筑升至z级", new BuildTypeAndLevelChecker()),

    TECHNOLOGY_REACHES_LEVEL_X(TaskType.TT_TECHNOLOGY_REACHES_LEVEL_X, "某个科技达到多少级", new TechLevelChecker()),

    COMPLETE_XX_TECHNOLOGICAL_XX_TIMES(TaskType.TT_COMPLETE_XX_TECHNOLOGICAL_XX_TIMES, "完成科技研究累计多少次", new TechResearchChecker()),

    TECH_START_RESEARCH_XX(TaskType.TT_TECH_START_RESEARCH_XX, "开始技术研究", new TechStartResearchChecker()),

    FINISH_RESEARCH_XX_TECH(TaskType.TT_FINISH_RESEARCH_XX_TECH, "完成xx科技研究", new TechFinishedChecker()),

    //	SCIENTIFIC_RESEARCH_ACCELERATED_XX(20003,"研究科技消耗加速累计多少分钟"),
    HAVE_X_UNITS(TaskType.TT_HAVE_X_UNITS, "拥有某个兵种X个", new SoldierTypeNumChecker()),

    START_TRAINING_XX_ARMS_X_TIMES(TaskType.TT_START_TRAINING_XX_ARMS_X_TIMES, "开始训练某个兵种X个", new StarTrainingSoldierChecker()),
    FINISH_TRAINING_XX_ARMS_X_TIMES(TaskType.TT_FINISH_TRAINING_XX_ARMS_X_TIMES, "完成训练某个兵种X个", new FinishTrainingSoldierTypeChecker()),
    FINISH_TRAINING_XX_ARMS_X_LEVEL_X_TIMES(TaskType.TT_FINISH_TRAINING_XX_ARMS_X_LEVEL_X_TIMES, "完成训练某个等级兵种X个", new FinishTrainingSoldierIdChecker()),
    FINISH_TRAINING_XX_LEVEL_ARMS_XX_NUM(TaskType.TT_FINISH_TRAINING_XX_LEVEL_ARMS_XX_NUM, "完成训练x等级兵种x个", new FinishTrainingSoldierLevelChecker()),
    FINISH_TRAINING_X_TYPE_X_LEVEL_ARMS_X_NUM(TaskType.TT_FINISH_TRAINING_X_TYPE_X_LEVEL_ARMS_X_NUM, "完成训练X类型X等级以上兵种X个", new FinishTrainingSoldierTypeLevelChecker()),

    TREATMENT_SOLDIERS_X(TaskType.TT_TREATMENT_SOLDIERS_X, "完成治疗士兵x个", new GatherTreatedSoliderNumChecker()),

    //	MINING_XX_RESOURCES_REACH_XX(50001,"采某种资源达到多少"),
    MINING_XX_RESOURCES_REACH_XX(TaskType.TT_MINING_XX_RESOURCES_REACH_XX, "世界采集某种资源多少个(0表示任意资源)", new ResBuildingCollectResourceChecker()),

    COLLECT_XX_RESOURCES_IN_THE_CITY_REACH_XX(TaskType.TT_COLLECT_XX_RESOURCES_IN_THE_CITY_REACH_XX, "城内收集某种资源达到多少", new InnerCollectResourceChecker()),

    OUTPUT_RATE_OF_X_RESOURCES(TaskType.TT_OUTPUT_RATE_OF_X_RESOURCES, "某种资源产出率多少", new InnerResourceOutputRateChecker()),

    COLLECT_ANY_RESOURCES_IN_THE_CITY_REACH(TaskType.TT_COLLECT_ANY_RESOURCES_IN_THE_CITY_REACH, "城内收集任意资源达到多少", new InnerCollectAnyResourceChecker()),

    RESBUILDING_COLLECT_TIME(TaskType.TT_RESBUILDING_COLLECT_TIME, "世界采集资源x次", new ResBuildingCollectResourceTimeChecker()),
    COMPLETE_X_HERO_UPGRADES(TaskType.TT_COMPLETE_X_HERO_UPGRADES, "完成X次英雄升级", new HeroLevelUpChecker()),
    X_HERO_REACHES_X_STARS(TaskType.TT_X_HERO_REACHES_X_STARS, "指定英雄达到x星", new PlayerHeroStarChecker()),
    X_HERO_REACHES_X_LEVEL(TaskType.TT_X_HERO_REACHES_X_LEVEL, "指定英雄达到x级", new PlayerHeroLevelChecker()),
    X_HEROES_WITH_X_RANK_X_GRADE_X_STAR(TaskType.TT_X_HEROES_WITH_X_RANK_X_GRADE_X_STAR, "拥有x级x品x星的英雄x个", new PlayerHeroPropChecker()),
    UPDATE_HERO_SKILL_X_TIMES(TaskType.TT_UPDATE_HERO_SKILL_X_TIMES, "提升英雄技能x次", new PlayerHeroUpdateSkillChecker()),
    UPDATE_HERO_STAR_X_TIMES(TaskType.TT_UPDATE_HERO_STAR_X_TIMES, "提升英雄星级x次", new PlayerHeroUpdateStarChecker()),
    OPEN_XX_TREASURE_CHESTS_XX_TIMES(TaskType.TT_OPEN_XX_TREASURE_CHESTS_XX_TIMES, "打开xx类型宝箱xx次(0代表任意宝箱)", new OpenTreasureChestsTimesChecker()),
    ATTACK_A_CERTAIN_LEVEL_OF_MONSTERS_X_TIMES(TaskType.TT_ATTACK_A_CERTAIN_LEVEL_OF_MONSTERS_X_TIMES, "攻击特定等级野怪X次", new StarAttCreepsChecker()),
    KILL_MONSTERS_OF_A_CERTAIN_LEVEL_X_TIMES(TaskType.TT_KILL_MONSTERS_OF_A_CERTAIN_LEVEL_X_TIMES, "击杀特定等级野怪X次", new FinishAttCreepsChecker()),
    KILL_SPECIFIC_MONSTERS(TaskType.TT_KILL_SPECIFIC_MONSTERS, "击杀特定野怪", new KillSpecialCreepsChecker()),
    OCCUPY_STRONGHOLD_X_TIMES(TaskType.TT_OCCUPY_STRONGHOLD_X_TIMES, "占领据点x次", new PlayerOccupyPointChecker()),
    ATTACK_STRONGHOLD_X_TIMES(TaskType.TT_ATTACK_STRONGHOLD_X_TIMES, "攻打据点X次", new StarAttPointChecker()),
    OCCUPY_THE_STRONGHOLD_FOR_X_MINUTES(TaskType.TT_OCCUPY_THE_STRONGHOLD_FOR_X_MINUTES, "占领据点x分钟", new PlayerOccupyPointTimeChecker()),
    PHYSICAL_EXERTION(TaskType.TT_PHYSICAL_EXERTION, "体力消耗", new PlayerConsumeEnergyChecker()),
    KILL_RALLY_MONSTERS_OF_A_CERTAIN_LEVEL_X_TIMES(TaskType.TT_KILL_RALLY_MONSTERS_OF_A_CERTAIN_LEVEL_X_TIMES, "击杀特定等级集结城寨X次", new PlayerFinishAttackRallyMonsterChecker()),

    NUMBER_OF_SOLDIERS_KILLED_IN_PVP(TaskType.TT_NUMBER_OF_SOLDIERS_KILLED_IN_PVP, "PVP击杀士兵数量", new PVPKillSoldierChecker()),

    JOIN_THE_ALLIANCE(TaskType.TT_JOIN_THE_ALLIANCE, "加入联盟", new JoinClanChecker()),

    ALLIANCE_HELPED_X_TIMES(TaskType.TT_ALLIANCE_HELPED_X_TIMES, "联盟帮助X次", new ClanHelpChecker()),

    ALLIANCE_DONATED_X_TIMES(TaskType.TT_ALLIANCE_DONATED_X_TIMES, "联盟捐献X次", new ClanDonateChecker()),

    NUMBER_OF_BLACK_MARKET_PURCHASES(TaskType.TT_NUMBER_OF_BLACK_MARKET_PURCHASES, "黑市购买次数", new DiscountStoreBuyChecker()),

    BLACKMARKET_COST_GOLD_X_TIMES(TaskType.TT_BLACKMARKET_COST_GOLD_X_TIMES, "黑市使用金币购买次数", new DiscountStoreDiamondBuyChecker()),

    BLACK_MARKET_REFRESH_X_COUNT(TaskType.TT_BLACK_MARKET_REFRESH_X_COUNT, "黑市刷新次数", new DiscountStoreRefreshChecker()),

    POWER_REACHES_X(TaskType.TT_POWER_REACHES_X, "战力达到", new PowerIncreaseToChecker()),

    OVERALL_COMBAT_POWER_INCREASE(TaskType.TT_OVERALL_COMBAT_POWER_INCREASE, "总战力提升（只积累增长值，不扣除降低值）", new TotalPowerIncreaseChecker()),

    BUILDING_COMBAT_POWER(TaskType.TT_BUILDING_COMBAT_POWER, "建筑战力提升（只积累增长值，不扣除降低值）", new BuildPowerIncreaseChecker()),

    TECHNOLOGICAL_IMPROVEMENT(TaskType.TT_TECHNOLOGICAL_IMPROVEMENT, "科技战力提升（只积累增长值，不扣除降低值）", new TechPowerIncreaseChecker()),

    TROOP_STRENGTH_UPGRADE(TaskType.TT_TROOP_STRENGTH_UPGRADE, "部队战力提升（只积累增长值，不扣除降低值）", new TroopPowerIncreaseChecker()),

    INC_BUILD_OR_TECH_OR_TROOP_POWER(TaskType.TT_INC_BUILD_TECH_TROOP_POWER, "提升x点建筑或科技或部队战力（只积累增长值，不扣除降低值）", new IncBuildOrTechOrTroopPowerChecker()),

    HERO_POWER_INCREASE(TaskType.TT_HERO_POWER_INCREASE, "英雄战力提升（只积累增长值，不扣除降低值）", new HeroPowerIncreaseChecker()),

    //	NUMBER_OF_SYNTHETIC_EQUIPMENT_MATERIALS(120001,"合成装备材料次数"),
//	NUMBER_OF_EQUIPMENT_MADE(120002,"制作装备次数"),
//	NUMBER_OF_TIMES_TO_MAKE_MATERIALS(120003,"制作材料次数"),
//	HAVE_X_QUALITY_X_LEVEL_EQUIPMENT_X(120004,"拥有X品质X等级装备X个"),
//	MAKE_X_QUALITY_X_LEVEL_EQUIPMENT_X_PIECES(120005,"制作X品质X等级装备X个"),
//	DECOMPOSITION_X_QUALITY_X_LEVEL_EQUIPMENT_X_PIECES(120006,"分解X品质X等级装备X个"),
//	MECHA_UPGRADE(130001,"机甲升级（部位）"),
//	MECHA_AWAKENING(130002,"机甲觉醒"),
    AIR_FORCE_UPGRADE(TaskType.TT_AIR_FORCE_UPGRADE, "空军升级", new PlaneLevelUpChecker()),

    NUMBER_OF_AIR_FORCES_USED(TaskType.TT_NUMBER_OF_AIR_FORCES_USED, "使用空军次数（运输机）", new UseTransportChecker()),

    //	UPGRADE_SUPER_WEAPON(150001,"升级超武"),
//	USE_SUPER_WEAPON(150002,"使用超武"),

    HEAT_VALUE_TASK(TaskType.TT_HEAT_VALUE_TASK, "活跃度达到X", new HeatValueTaskChecker()),

    USE_ITEM_TASK(TaskType.TT_USE_ITEM_TASK, "使用n次道具", new UseItemChecker()),

    USE_XX_ITEMTYPE_XX_TIMES(TaskType.TT_USE_XX_ITEMTYPE_XX_TIMES, "消耗指定道具类型", new UseItemTypeChecker()),

    USE_ACCELERATE_ITEM_X_MIN(TaskType.TT_USE_ACCELERATE_ITEM_X_MIN, "使用任何加速道具累计x分钟", new UseAccelerateItemChecker()),

    USE_BUILD_ACCELERATE_ITEM_X_MIN(TaskType.TT_USE_BUILD_ACCELERATE_ITEM_X_MIN, "使用城建加速道具累计x分钟", new UseBuildAccelerateItemChecker()),

    USE_TRAIN_ACCELERATE_ITEM_X_MIN(TaskType.TT_USE_TRAIN_ACCELERATE_ITEM_X_MIN, "使用训练加速道具累计x分钟", new UseTrainAccelerateItemChecker()),

    USE_TECH_ACCELERATE_ITEM_X_MIN(TaskType.TT_USE_TECH_ACCELERATE_ITEM_X_MIN, "使用科技加速道具累计x分钟", new UseTechAccelerateItemChecker()),

    SPY_PLANE_SURVEY_NUM(TaskType.TT_SPY_PLANE_SURVEY_NUM, "侦察机成功调查一次", new SpyPlaneSurveyChecker()),

    ADD_XX_ITEM_XX_NUM(TaskType.TT_ADD_XX_ITEM_XX_NUM, "获得xx道具xx个", new AddItemChecker()),

    CHANGE_PIC(TaskType.TT_CHANGE_PIC, "设置头像", new PicChangeChecker()),

    DISPATCH_TROOP_XX_TIMES(TaskType.TT_DISPATCH_TROOP_XX_TIMES, "派遣部队x次", new DispatchTroopChecker()),

    PLAYER_IN_A_CLAN(TaskType.TT_PLAYER_IN_A_CLAN, "创建或加入一个联盟", new PlayerInClanChecker()),
    PLAYER_LOGIN_DAYS(TaskType.TT_LOGIN_DAYS, "累计登录x天", new LoginDaysChecker()),

    // -------------------- 机甲任务相关 ------------------------- //
    ADD_X_POINT_TO_HERO_TALENT(TaskType.ADD_X_POINT_TO_HERO_TALENT, "为英雄添加X个天赋点", new AddHeroTalentPointChecker()),

    CHARGE_TIMES(TaskType.TT_CHARGE_TIMES, "充值X次", new ChargeTimesChecker()),
    CHARGE_GAIN_DIAMOND(TaskType.TT_CHARGE_GAIN_DIAMOND, "累计充值获得X黄金", new ChargeGainDiamondChecker()),

    CLAN_HAS_MAIN_BASE(TaskType.TT_ALLIANCE_HAS_MAIN_BASE, "联盟拥有主基地", new ClanHasMainBaseChecker()),
    CLAN_HAS_X_COMMAND_CENTER(TaskType.TT_ALLIANCE_HAS_X_COMMAND_CENTER, "联盟拥有X个据点", new ClanCommandCenterChecker()),
    PLAYER_IN_TERRITORY(TaskType.TT_ALLIANCE_IN_TERRITORY, "玩家主城在联盟领土内", new PlayerInClanTerritoryChecker()),
    GET_CLAN_GIFT_X_TIMES(TaskType.TT_ALLIANCE_GET_GIFT_X_TIMES, "联盟礼物领取X次", new ClanGiftChecker()),

    KILL_BUILDING_GUARD(TaskType.TT_KILL_BUILDING_GUARD, "击杀X次守护者", new KillBuildingGuardChecker()),
    PICK_UP_BUFF(TaskType.TT_PICK_UP_BUFF, "采集X次符文", new PickUpBuffChecker()),
    CONSUME_CURRENCY(TaskType.TT_CONSUME_CURRENCY, "消耗X货币", new ConsumeCurrencyChecker()),
    INC_BUILD_OR_TROOP_POWER(TaskType.TT_INC_BUILD_OR_TROOP_POWER, "提升X点建筑或部队战斗力", new IncBuildOrTroopPowerChecker()),
    INC_TECH_OR_TROOP_POWER(TaskType.TT_INC_TECH_OR_TROOP_POWER, "提升X点科技或部队战斗力", new IncTechOrTroopPowerChecker()),

    TT_PROSPERITY_X_MAX(CommonEnum.TaskType.TT_PROSPERITY_X_MAX, "发展度达到X点", new ProsperityMaxChangeChecker()),
    TT_EXPLORE_CITY_ANY_LEVEL_ONCE_START(TaskType.TT_EXPLORE_CITY_ANY_LEVEL_ONCE_START, "开始调查一次任意等级的城市", new ExploreMapBuildCityStartChecker()),
    TT_EXPLORE_X_NUMBER_OF_MAP_POINTS_TYPE_X_START(TaskType.TT_EXPLORE_X_NUMBER_OF_MAP_POINTS_TYPE_X_START, "开始调查x次x类型的地图点", new ExploreMapBuildStartChecker()),


    TT_EXPLORE_CITY_ANY_LEVEL_ONCE_FIN(TaskType.TT_EXPLORE_CITY_ANY_LEVEL_ONCE_FIN, "完成调查一次任意等级的城市", new ExploreMapBuildCityFinChecker()),
    TT_EXPLORE_X_NUMBER_OF_MAP_POINTS_TYPE_X_FIN(TaskType.TT_EXPLORE_X_NUMBER_OF_MAP_POINTS_TYPE_X_FIN, "完成调查x次x类型的地图点", new ExploreMapBuildFinChecker()),

    TT_ATTACK_X_OR_HIGHER_LEVEL_STRONGHOLD_X_TIMES(TaskType.TT_ATTACK_X_OR_HIGHER_LEVEL_STRONGHOLD_X_TIMES, "参与攻占X级及更高等级的据点X次", new AttackStrongHoldChecker()),
    TT_ATTACK_X_OR_HIGHER_LEVEL_CITY_X_TIMES(TaskType.TT_ATTACK_X_OR_HIGHER_LEVEL_CITY_X_TIMES, "参与攻占X级及更高等级的城市X次", new AttackCityChecker()),
    TT_CLAN_TERRITORY_LV_REACH_X(TaskType.TT_CLAN_TERRITORY_LV_REACH_X, "联盟势力值等级达到X", new PlayerInClanTerritoryLvChecker()),
    TT_HERO_INTENSIVE(TaskType.TT_HERO_INTENSIVE, "觉醒任意英雄X次", new HeroIntensiveChecker()),
    TT_DESTROY_X_TIMES_RALLY_MONSTER(TaskType.TT_DESTROY_X_TIMES_RALLY_MONSTER, "摧毁X次集结城寨", new DestroyCableCampChecker()),

    TT_BUILD_X_CLAN_FLAG(TaskType.TT_BUILD_CLAN_FLAG, "参与完成建造X个联盟旗帜", new BuildClanFlagChecker()),
    TT_OBTAIN_CLAN_PRIVATE_SCORE(TaskType.TT_CLAN_OBTAINED_PRIVATE_SCORE, "联盟个人积分获取数", new AddClanPrivateScoreChecker()),
    TT_RECRUIT_X_EPIC_HERO(TaskType.TT_RECRUIT_EPIC_HERO, "招募X次史诗英雄", new RecruitEpicHeroChecker()),
    TT_RECRUIT_X_LEAGUE_HERO(TaskType.TT_RECRUIT_LEAGUE_HERO, "招募X次传说英雄", new RecruitLeagueHeroChecker()),
    TT_FINISH_X_DAILY_TASK(TaskType.TT_FINISH_DAILY_TASK, "完成X次每日任务", new FinishDailyTaskChecker()),

    RESBUILDING_COLLECT_TIME_OUT(TaskType.TT_RESBUILDING_COLLECT_TIME_OUT, "把x个采集点采空", new ResBuildingCollectResourceOutChecker()),
    USE_BUILD_TRAIN_ACCELERATE_ITEM_X_MIN(TaskType.TT_USE_BUILD_TRAIN_ACCELERATE_ITEM_X_MIN, "在建筑建造或部队训练中使用x分钟的加速道具", new UseBuildTrainAccelerateItemChecker()),
    USE_TECH_TRAIN_ACCELERATE_ITEM_X_MIN(TaskType.TT_USE_TECH_TRAIN_ACCELERATE_ITEM_X_MIN, "在科研或部队训练中使用x分钟的加速道具", new UseTechTrainAccelerateItemChecker()),
    INC_BUILD_OR_TRAIN_TROOP_POWER(TaskType.TT_INC_BUILD_OR_TRAIN_TROOP_POWER, "提升x点建筑或训练部队战力（仅计入建筑和兵营相关行为提升的战力，如“训练”、“升级”）", new IncBuildOrTrainTroopPowerChecker()),
    INC_TECH_OR_TRAIN_TROOP_POWER(TaskType.TT_INC_TECH_OR_TRAIN_TROOP_POWER, "提升x点科研或训练部队战力（仅计入科研和兵营相关行为提升的战力，如“训练”、“升级”）", new IncTechOrTrainTroopPowerChecker()),
    INC_TRAIN_TROOP_POWER(TaskType.TT_INC_TRAIN_TROOP_POWER, "训练提升x点部队战力（仅计入通过兵营进行的相关行为提升的战力，如“训练”、“升级”）", new IncTrainTroopPowerChecker()),

    TT_SKYNET_X_FINISH(TaskType.TT_SKYNET_X_FINISH, "完成X次天网任务", new SkynetFinishChecker()),
    TT_SKYNET_X_REWARD(TaskType.TT_SKYNET_X_REWARD, "领取X次天网任务奖励", new SkynetRewardChecker()),

    TT_CHAPTER_UNLOCK_UNIT(TaskType.TT_CHAPTER_UNLOCK_UNIT, "解锁指定单位", new UnlockUnitChecker()),
    TT_CHAPTER_COMPLETE_TASK(TaskType.TT_CHAPTER_COMPLETE_TASK, "完成章节所有任务", new ChapterTaskFinishChecker()),
    TT_CHAPTER_BUILD_RH_BUILDING(TaskType.TT_CHAPTER_BUILD_RH_BUILDING, "建造指定建筑", new BuildRhNumChecker()),
    TT_CHAPTER_COMPLETE_MISSION(TaskType.TT_CHAPTER_COMPLETE_MISSION, "通关指定关卡", new BuildCompleteMissionChecker()),
    TT_X_TROOP_X_HERO(TaskType.TT_X_TROOP_X_HERO, "编队X上阵x个英雄", new TroopHeroChecker()),
    TT_WIN_X_DEFEND_BATTLE(TaskType.TT_WIN_X_DEFEND_BATTLE, "塔防战获胜x次", new DefendBattleFireChecker()),
    TT_BASE_EXPAND_TO_X(TaskType.TT_BASE_EXPAND_TO_X, "扩张基地到X级", new BaseExpandChecker())
    ;

    private final String id;
    private final String desc;
    private final TaskChecker checker;


    private static final Map<String, TaskCheckType> NUMBERS;

    static {
        NUMBERS = new HashMap<>();
        for (TaskCheckType type : TaskCheckType.values()) {
            NUMBERS.put(type.id, type);
        }
    }

    public static TaskCheckType forNumber(TaskType value) {
        if (value == null) {
            throw new GeminiException(StringUtils.format("taskType not implemented"));
        }
        return NUMBERS.getOrDefault(value.name(), null);
//        TaskCheckType orDefault = NUMBERS.getOrDefault(value.name(), null);
//        if (orDefault == null) {
//            throw new GeminiException(StringUtils.format("taskType not implemented. type:{}", value));
//        }
//        return orDefault;
    }

    TaskCheckType(TaskType id, String desc, TaskChecker checker) {
        this.id = id.name();
        this.desc = desc;
        this.checker = checker;
    }

    public String getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }

    public TaskChecker getChecker() {
        return checker;
    }
}

package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.game.gen.prop.ItemProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class ClearItem implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String param1 = args.get("itemId");
        if (StringUtils.isNotEmpty(param1)) {
            int itemTemplateId = Integer.parseInt(param1);
            for (ItemProp value : actor.getEntity().getProp().getItems().values()) {
                if (value.getTemplateId() == itemTemplateId) {
                    value.setNum(0);
                }
            }
        } else {
            actor.getEntity().getItemComponent().clearWithGm();
        }
    }

    @Override
    public String showHelp() {
        return "ClearItem itemId={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_ITEM;
    }
}

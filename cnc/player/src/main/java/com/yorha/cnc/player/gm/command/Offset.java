package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.actor.cluster.ClusterHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.GmHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.utils.OffsetUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class Offset implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(Offset.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int addSeconds;
        if (args.get("time") != null) {
            final int tsSec = (int) TimeUnit.MILLISECONDS.toSeconds(TimeUtils.string2ZoneDate("yyyy-MM-dd-HH-mm-ss", args.get("time")));
            final int nowTsSec = (int) SystemClock.nowOfSeconds();
            if (tsSec <= SystemClock.nowOfSeconds()) {
                throw new GeminiException("tsSec={} should after now={}!", tsSec, SystemClock.nowOfSeconds());
            }
            addSeconds = tsSec - nowTsSec;
        } else {
            addSeconds = Integer.parseInt(args.get("seconds"));
            if (addSeconds <= 0) {
                throw new GeminiException("seconds should >0!");
            }
        }

        Pair<Long, Integer> offsetFromDb = OffsetUtils.getOffsetFromDb(actor);
        LOGGER.info("getOffsetFromDb {}", offsetFromDb);

        // 检测调整时间偏移量后，是否会超过定时器支持的最大时间
        var totalOffset = offsetFromDb.getFirst() + TimeUnit.SECONDS.toMillis(addSeconds);
        var protectedOffset = totalOffset - TimeUnit.DAYS.toMillis(90);
        if (protectedOffset > 0 && protectedOffset / 100 > Integer.MAX_VALUE) {
            throw new GeminiException("offset too large!");
        }

        long newOffset = OffsetUtils.changeOffsetToDb(offsetFromDb, addSeconds, actor);
        LOGGER.info("changeOffsetToDb {}", newOffset);
        // 通知所有进程
        ClusterHelper.broadcastStringToAllNodes(actor.system(), StringUtils.format("offset:{}", newOffset), actor.self());

        StringBuilder type = new StringBuilder();
        type.append("注意：")
                .append(actor.getEntity().getName())
                .append("(")
                .append(playerId)
                .append(")")
                .append(" 向后调整了时间偏移量 seconds=")
                .append(addSeconds).append("\t")
                .append("当前时间=")
                .append(TimeUtils.now2String());

        Integer marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(CommonEnum.MarqueeType.CREATE_CLAN);


        StructPB.DisplayDataPB.Builder builder = StructPB.DisplayDataPB.newBuilder();
        builder.getParamsBuilder().addDatas(StructPB.DisplayParamPB.newBuilder().setType(CommonEnum.DisplayParamType.DPT_TEXT).setText(type.toString()).build());
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(CommonEnum.DisplayParamType.DPT_ITEM_ID_FOR_NAME, 0));
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(type.toString()));
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, builder.build());
        BroadcastHelper.toCsOnlinePlayerInZone(actor.getZoneId(), MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
        // 只给gm执行者发邮件
        GmHelper.sendGmNtfMail(actor.getZoneId(), playerId, "Offset", type.toString());
    }

    @Override
    public String showHelp() {
        return "Offset seconds={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

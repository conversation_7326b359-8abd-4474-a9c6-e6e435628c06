package com.yorha.cnc.player.gm.command.server;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 获取业务版本号
 *
 * <AUTHOR>
 */
public class GetLogicTag implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        throw new GeminiException(ErrorCode.SYSTEM_WARNING, ServerContext.getLogicTag());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

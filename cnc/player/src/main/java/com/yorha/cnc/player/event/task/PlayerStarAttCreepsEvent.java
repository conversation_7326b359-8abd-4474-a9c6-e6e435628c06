package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 开始攻击野怪事件
 *
 * <AUTHOR>
 */
public class PlayerStarAttCreepsEvent extends PlayerTaskEvent {
    private final int creepId;
    private final int creepCategory;
    private final int level;

    public PlayerStarAttCreepsEvent(PlayerEntity entity, int creepId, int creepCategory, int level) {
        super(entity);
        this.creepId = creepId;
        this.creepCategory = creepCategory;
        this.level = level;
    }

    public int getCreepId() {
        return creepId;
    }

    public int getLevel() {
        return level;
    }

    public int getCreepCategory() {
        return creepCategory;
    }

}

package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.task.AbstractTaskHandler;
import com.yorha.cnc.player.task.SeasonBattlePassTaskHandler;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetDesc;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.battlePass.SeasonBattlePassResService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.server.config.ZoneSeasonInfo;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.BattlePassTaskInfoProp;
import com.yorha.game.gen.prop.PlayerBattlePassModelProp;
import com.yorha.game.gen.prop.PlayerPaymentModelProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBpExpFlow;
import res.template.BpRewardTemplate;
import res.template.BpTaskTemplate;
import res.template.ConstBpTemplate;
import res.template.SeasonBpScheduleTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 赛季版通行证模块，负责通行证活动的上下架
 *
 * <AUTHOR>
 */
public class PlayerSeasonBattlePassComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSeasonBattlePassComponent.class);

    private AbstractTaskHandler taskHandler;
    /**
     * 任务组的定时器
     */
    private final Map<Integer, ActorTimer> missionGroupTimerMap = new HashMap<>();
    /**
     * 本期battlePass，过期的定时器
     */
    private ActorTimer bpOverTimer;

    /**
     * taskHandler是否初始化，防止重复注册
     */
    private boolean alreadyInit;

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerSeasonBattlePassComponent::monitorDailyRefreshEvent);
    }

    public PlayerSeasonBattlePassComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (taskHandler != null) {
            ownerActor().getEntity().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("battlePass clear attention");
            LOGGER.error("PlayerSeasonBattlePassComponent onLoad, detect taskHandler not null");
        }
        taskHandler = new SeasonBattlePassTaskHandler(ownerActor().getEntity(), CommonEnum.TaskClass.TC_BATTLEPASS);

        refreshCurBattlePass();
        tryLoadNextBp("onLoad");
        // 跨天登录则执行日刷逻辑
        long lastDailyRefreshTsMs = getProp().getLastDailyRefreshTsMs();
        if (!TimeUtils.isSameDay(SystemClock.now(), lastDailyRefreshTsMs)) {
            onDailyRefresh();
        }
    }

    /**
     * 热更检查，包括更新的配置是否正确，不正确则不热更
     */
    private boolean onReloadCheck() {
        if (!hasJoinBp()) {
            return false;
        }
        PlayerBattlePassModelProp prop = getProp();
        SeasonBpScheduleTemplate template = ResHolder.getTemplate(SeasonBpScheduleTemplate.class, prop.getBpConfigId());
        final int openTime = ResHolder.getResService(SeasonBattlePassResService.class).getOpenTime(prop.getBpConfigId());
        int configLevel = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        // 检查通行证等级是否超过64
        if (configLevel > Long.SIZE) {
            LOGGER.error("PlayerSeasonBattlePassComponent onReloadCheck, config level exceed long size");
            return false;
        }
        // 检查当期bp开始时间是否修改
        if (openTime != prop.getOpenTime()) {
            LOGGER.error("PlayerSeasonBattlePassComponent onReloadCheck, bpScheduleTemplate change bp start time");
            return false;
        }
        // 检查当期bp持续时间是否修改
        if (template.getDuration() != prop.getDuration()) {
            LOGGER.error("PlayerSeasonBattlePassComponent onReloadCheck, bpScheduleTemplate change bp duration time");
            return false;
        }
        // bp任务组配置检查，只检查已解锁任务组的任务数量热更前后是否一致，任务是否变化不做判断
        for (Map.Entry<Integer, BattlePassTaskInfoProp> entry : prop.getTasks().entrySet()) {
            List<Integer> groupTasks = ResHolder.getResService(SeasonBattlePassResService.class).getMissionGroup(prop.getBpConfigId(), entry.getKey());
            if (groupTasks.size() != entry.getValue().getGroupTasksSize()) {
                LOGGER.error("PlayerSeasonBattlePassComponent onReloadCheck, missionGroup config wrong, bpConfigId={}, missionGroupId={}", prop.getBpConfigId(), entry.getKey());
                return false;
            }
        }
        return true;
    }

    /**
     * 热更
     */
    @Override
    public void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        // 通行证热更只需要热更任务，只需要关注任务相关表是否发生变化即可
        if (!(updatedTemplates.contains(SeasonBpScheduleTemplate.class) || updatedTemplates.contains(BpTaskTemplate.class))) {
            return;
        }
        // 没有通过热更检查也不热更
        if (!onReloadCheck()) {
            return;
        }
        PlayerBattlePassModelProp prop = getProp();
        Set<Integer> reloadTasks = new HashSet<>();
        // 只需要更新已解锁且未完成的任务
        LOGGER.info("PlayerSeasonBattlePassComponent onReloadRes, before reload tasks={}", prop.getTasks().values());
        for (Map.Entry<Integer, BattlePassTaskInfoProp> entry : prop.getTasks().entrySet()) {
            List<Integer> groupTasks = ResHolder.getResService(SeasonBattlePassResService.class).getMissionGroup(prop.getBpConfigId(), entry.getKey());
            if (groupTasks.size() != entry.getValue().getGroupTasksSize()) {
                LOGGER.error("PlayerSeasonBattlePassComponent onReloadRes, config group task num invalid, groupId={}, old bpTasks={}, config bpTasks={}", entry.getKey(), entry.getValue().getGroupTasks(), groupTasks);
                continue;
            }
            for (int i = 0; i < groupTasks.size(); i++) {
                int oldTask = entry.getValue().getGroupTasks().get(i);
                int newTask = groupTasks.get(i);
                TaskInfoProp taskInfoProp = this.taskHandler.getTaskProp().get(oldTask);
                if (taskInfoProp == null) {
                    LOGGER.error("PlayerSeasonBattlePassComponent onReloadRes, taskInfoProp not exist, missionGroupId={}, index={}, taskPoolId={}", entry.getKey(), i, entry.getValue().getGroupTasksIndex(i));
                    continue;
                }
                if (oldTask != newTask && AbstractTaskHandler.isUnCompleted(taskInfoProp)) {
                    this.taskHandler.getTaskProp().remove(oldTask);
                    entry.getValue().getGroupTasks().set(i, newTask);
                    reloadTasks.add(newTask);
                }
            }
        }
        LOGGER.info("PlayerSeasonBattlePassComponent onReloadRes, after reload tasks={}, reloadTasks={}", prop.getTasks().values(), reloadTasks);
        if (!reloadTasks.isEmpty()) {
            this.taskHandler.addBatchTask(reloadTasks);
        }
    }

    /**
     * 刷新当前battlePass
     */
    private void refreshCurBattlePass() {
        LOGGER.info("PlayerSeasonBattlePassComponent refreshCurBattlePass");
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("PlayerSeasonBattlePassComponent refreshCurBattlePass end server is not open={}", ZoneContext.getServerOpenTsMs());
            return;
        }
        long now = SystemClock.now();
        int bpId = getCurBpId();
        // 本期没有运行中的bp
        if (!hasJoinBp()) {
            LOGGER.info("PlayerSeasonBattlePassComponent refreshCurBattlePass skip cur {}", bpId);
            return;
        }
        PlayerBattlePassModelProp prop = getProp();
        long bpEndTs = getBpEndTs(bpId, prop.getTsMs());
        // 已过期bp
        if (bpEndTs <= now) {
            LOGGER.info("PlayerSeasonBattlePassComponent refreshCurBattlePass overBp {}", bpId);
            overBp(bpId);
            return;
        }
        // 未过期bp, 刷新
        LOGGER.info("PlayerSeasonBattlePassComponent refreshCurBattlePass refreshBp {}", bpId);
        refreshBp(bpId);
    }

    /**
     * 当期bpId
     */
    private int getCurBpId() {
        return getProp().getBpConfigId();
    }

    /**
     * 尝试加载下一期的bp(登录 + 跨天) checkValid得保证开服天数是顺着加下去的
     */
    private void tryLoadNextBp(String reason) {
        LOGGER.info("PlayerSeasonBattlePassComponent loadNextBp {}", reason);
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("PlayerSeasonBattlePassComponent tryLoadNextBp end server is not open {} openTs={}", reason, ZoneContext.getServerOpenTsMs());
            return;
        }
        if (hasJoinBp()) {
            LOGGER.info("PlayerSeasonBattlePassComponent hasJoinBp bp={}", getProp().getBpConfigId());
            return;
        }
        long now = SystemClock.now();
        long createTime = getOwner().getProp().getCreateTime();
        final ZoneSeasonInfo seasonInfo = ZoneContext.getZoneSeasonInfo();
        final CommonEnum.ZoneSeason season = seasonInfo.getSeason();
        LOGGER.info("PlayerSeasonBattlePassComponent tryLoadNextBp cur session={} stage={}", season, seasonInfo.getSeasonStage());
        for (SeasonBpScheduleTemplate template : ResHolder.getInstance().getListFromMap(SeasonBpScheduleTemplate.class)) {
            if (season != template.getSeason()) {
                continue;
            }
            final long bpStartTs = getBpStartTs(template.getId());
            final long bpEndTs = getBpEndTs(template.getId(), bpStartTs);
            // 创建角色前开的battlePass 不做处理
            if (bpStartTs < createTime) {
                LOGGER.info("PlayerSeasonBattlePassComponent loadNextBp skip {} {} {}", template.getId(), bpStartTs, createTime);
                continue;
            }
            // 开过的bp
            if (now >= bpEndTs) {
                LOGGER.info("PlayerSeasonBattlePassComponent loadNextBp overtime {}", template.getId());
                continue;
            }

            if (now >= bpStartTs) {
                LOGGER.info("PlayerSeasonBattlePassComponent loadNextBp openBp {}", template.getId());
                openBp(template.getId(), bpStartTs, bpEndTs);
            } else {
                LOGGER.info("PlayerSeasonBattlePassComponent next bp={} start={}", template.getId(), bpStartTs);
                return;
            }
        }
    }

    /**
     * 获取battlePass结束时间
     */
    private long getBpEndTs(int bpId, long startTsMs) {
        int duration;
        PlayerBattlePassModelProp prop = getProp();
        if (bpId == prop.getBpConfigId()) {
            duration = prop.getDuration();
        } else {
            SeasonBpScheduleTemplate template = ResHolder.getInstance().getValueFromMap(SeasonBpScheduleTemplate.class, bpId);
            duration = template.getDuration();
        }
        return startTsMs + TimeUnit.DAYS.toMillis(duration);
    }

    public PlayerBattlePassModelProp getProp() {
        return getOwner().getProp().getSeasonBattlePassModel();
    }


    /**
     * 获取battlePass开始时间
     */
    private long getBpStartTs(int bpId) {
        final ZoneSeasonInfo seasonInfo = ZoneContext.getZoneSeasonInfo();
        long tsMs = Long.MAX_VALUE;
        // 建设赛季，进入赛季的时间就是原服开服时间
        if (seasonInfo.getSeason() == CommonEnum.ZoneSeason.ZS_BUILDING) {
            tsMs = ZoneContext.getServerOpenTsMs();
        }
        final int startDay = ResHolder.getResService(SeasonBattlePassResService.class).getOpenTime(bpId);
        return TimeUtils.getNextDayStartMs(tsMs + TimeUnit.DAYS.toMillis(startDay - 1));
    }

    private int getStartMissionGroupNum(long startTs, long endTs) {
        long weeks = getWeeksBetween(startTs, endTs);
        int startMissionGroupNum = GameLogicConstants.BP_WEEK_MISSION_MAX - (int) weeks + 1;
        if (startMissionGroupNum <= 0) {
            startMissionGroupNum = 1;
        }
        return startMissionGroupNum;
    }

    /**
     * 开启一期 battlePass
     *
     * @param bpId battlePassId
     */
    private void openBp(int bpId, long bpStartTs, long bpEndTs) {
        LOGGER.info("PlayerSeasonBattlePassComponent openBp {}", bpId);
        if (!ZoneContext.isServerOpen()) {
            LOGGER.error("PlayerSeasonBattlePassComponent openBp end server is not open={} bpId={} time={}~{}", ZoneContext.getServerOpenTsMs(), bpId, bpStartTs, bpEndTs);
            return;
        }
        PlayerBattlePassModelProp prop = getProp();
        if (hasJoinBp()) {
            LOGGER.warn("PlayerSeasonBattlePassComponent openBp, currentBpId not equal 0, currentBpId={}, will open bpId={}", prop.getBpConfigId(), bpId);
        }
        resetBpTimer(bpId);
        long now = SystemClock.now();

        // 已到本期battlePass结束时间，结束本期
        if (now >= bpEndTs) {
            LOGGER.info("PlayerSeasonBattlePassComponent openBp then overBp{}", bpId);
            overBp(bpId);
            return;
        }
        tryParticipateInBp(bpId, bpStartTs);
        // 未到本期battlePass结束时间，编排定时器结束本期
        bpOverTimer = ownerActor().addTimer(getPlayerId() + "-season-" + bpId, TimerReasonType.BATTLE_PASS_OVER, () -> overBp(bpId), bpEndTs - now, TimeUnit.MILLISECONDS);
        int startMissionGroupNum = getStartMissionGroupNum(bpStartTs, bpEndTs);
        // 本周开启的任务组
        Set<Integer> startMissionGroups = new HashSet<>();
        for (int i = 0; i <= startMissionGroupNum; i++) {
            startMissionGroups.add(i);
        }
        openMissionGroup(bpId, startMissionGroups);
        // 编排后续开启的任务组
        long indexTs = bpStartTs;
        for (int j = (startMissionGroupNum + 1); j <= GameLogicConstants.BP_WEEK_MISSION_MAX; j++) {
            long nextStartTs = TimeUtils.getNextWeekStartMs(indexTs);
            indexTs = nextStartTs;
            scheduleNextMissionGroup(bpId, j, bpStartTs, nextStartTs, "openBp");
        }
    }

    /**
     * 刷新battlePass，继续定时开启还未开放的周任务
     *
     * @param bpId
     */
    private void refreshBp(int bpId) {
        LOGGER.info("PlayerSeasonBattlePassComponent refreshBp {}", bpId);
        resetBpTimer(bpId);
        PlayerBattlePassModelProp prop = getProp();
        long startTs = prop.getTsMs();
        long endTs = getBpEndTs(bpId, startTs);
        long now = SystemClock.now();
        // 已到本期battlePass结束时间，结束本期
        if (now >= endTs) {
            overBp(bpId);
            return;
        }
        // 未到本期battlePass结束时间，编排定时器结束本期
        bpOverTimer = ownerActor().addTimer(getPlayerId() + "-season-" + bpId, TimerReasonType.BATTLE_PASS_OVER_2, () -> overBp(bpId), endTs - now, TimeUnit.MILLISECONDS);
        // 先加载已经解锁的任务
        refreshTasks();
        // 刷新本期任务组状态
        int startMissionGroupNum = getStartMissionGroupNum(startTs, endTs);
        long indexTs = startTs;
        for (int j = (startMissionGroupNum + 1); j <= GameLogicConstants.BP_WEEK_MISSION_MAX; j++) {
            long nextStartTs = TimeUtils.getNextWeekStartMs(indexTs);
            indexTs = nextStartTs;
            // 对于已开的任务组不做处理
            if (isMissionGroupOpen(bpId, j)) {
                LOGGER.info("PlayerSeasonBattlePassComponent refreshBp skip MissionGroup {} {}", bpId, j);
                continue;
            }
            Set<Integer> missionGroupIds = new HashSet<>(1);
            missionGroupIds.add(j);
            // 对于离线期间开放的任务组，刷新开放状态
            if (nextStartTs <= now) {
                openMissionGroup(bpId, missionGroupIds);
                LOGGER.info("PlayerSeasonBattlePassComponent refreshBp now missionGroup {} {}", bpId, j);
                continue;
            }
            // 对于未到开放时间的任务组，编排定时器开放任务组
            scheduleNextMissionGroup(bpId, j, startTs, nextStartTs, "refreshBp");
        }
    }

    /**
     * 编排下一次周任务的开启
     *
     * @param bpId         battlePassid
     * @param missionGroup 任务组id
     * @param bpStartTs    bp 开始时间
     * @param mgstartTs    任务组开始时间
     * @param reason       原因
     */
    private void scheduleNextMissionGroup(int bpId, int missionGroup, long bpStartTs, long mgstartTs, String reason) {
        Set<Integer> missionGroupIds = new HashSet<>(1);
        missionGroupIds.add(missionGroup);
        long now = SystemClock.now();
        long dur = mgstartTs - now;
        if (dur <= 0) {
            openMissionGroup(bpId, missionGroupIds);
            LOGGER.info("PlayerSeasonBattlePassComponent scheduleNextMissionGroup open missionGroup {} {} {} {} {}", reason, bpId, missionGroup, bpStartTs, now);
            return;
        }
        // 对于未到开放时间的任务组，编排定时器开放任务组
        ActorTimer timer = ownerActor().addTimer(getPlayerId() + "-season-" + missionGroup, TimerReasonType.BATTLE_PASS_NEXT_MISSION_GROUP, () -> openMissionGroup(bpId, missionGroupIds), dur, TimeUnit.MILLISECONDS);
        if (timer != null) {
            missionGroupTimerMap.put(missionGroup, timer);
        }
        LOGGER.info("PlayerSeasonBattlePassComponent scheduleNextMissionGroup next missionGroup {} {} {} {} {} {}", reason, bpId, missionGroup, bpStartTs, dur, now);
    }

    /**
     * 任务组是否开放
     *
     * @param bpId    battlePassId
     * @param groupId 任务组id
     * @return
     */
    private boolean isMissionGroupOpen(int bpId, int groupId) {
        PlayerBattlePassModelProp prop = getProp();
        if (prop.getBpConfigId() != bpId) {
            return false;
        }
        return prop.getTasks().containsKey(groupId);
    }


    private long getWeeksBetween(long startTs, long endTs) {
        int beginWeekDay = TimeUtils.getZonedDateTime(startTs).getDayOfWeek().getValue();
        long daysBetween = TimeUtils.getAbsNatureDaysBetween(startTs, endTs);
        long weeksBetween = daysBetween / 7;
        int offset = (daysBetween % 7 + beginWeekDay) > 7 ? 1 : 0;
        return offset + weeksBetween;
    }

    /**
     * 参与条件检查，后面跨服可能用到，预留接口
     */
    private void checkCondition() {

    }

    /**
     * 尝试参与通行证活动
     */
    private void tryParticipateInBp(int bpId, long bpStartTs) {
        final int openTime = ResHolder.getResService(SeasonBattlePassResService.class).getOpenTime(bpId);

        checkCondition();
        // 设置属性
        SeasonBpScheduleTemplate template = ResHolder.getTemplate(SeasonBpScheduleTemplate.class, bpId);
        PlayerBattlePassModelProp prop = getProp();
        prop.setBpType(CommonEnum.BattltPassType.BPT_SILVER)
                .setBpConfigId(bpId)
                .setExp(0)
                .setLevel(1)
                .setOpenTime(openTime)
                .setDuration(template.getDuration())
                .setOpenDailyExpBox(false)
                .setLastDailyRefreshTsMs(SystemClock.now())
                .setTsMs(bpStartTs);
        prop.getClaimableSilver().clear();
        prop.getClaimableGold().clear();
        prop.getTasks().clear();
        markSilverRewardClaimable(1);
        LOGGER.info("PlayerSeasonBattlePassComponent tryParticipateInBp, bpType={}, bpId={}, bpLevel={} bpStartTs={}", prop.getBpType(), prop.getBpConfigId(), prop.getLevel(), bpStartTs);

        initTaskHandler();
    }

    public static void monitorDailyRefreshEvent(PlayerDayRefreshEvent event) {
        event.getPlayer().getSeasonBattlePassComponent().onDailyRefresh();
    }

    /**
     * 重置每日经验值
     */
    private void onDailyRefresh() {
        LOGGER.info("PlayerSeasonBattlePassComponent onDailyRefresh");
        tryLoadNextBp("onDailyRefresh");
        if (!hasJoinBp()) {
            return;
        }
        PlayerBattlePassModelProp prop = getProp();
        prop.setOpenDailyExpBox(false)
                .setLastDailyRefreshTsMs(SystemClock.now());
    }

    /**
     * 本期battlePass到期结束了
     */
    private void overBp(int bpId) {
        LOGGER.info("PlayerSeasonBattlePassComponent overBp {}", bpId);
        resetBpTimer(bpId);
        // 补发未领取的奖励，并重置bp属性
        onBattlePassFinish();
        // 上一期bp结束时再尝试开一次
        tryLoadNextBp("overBp:" + bpId);
    }

    /**
     * battlePass结束发奖并重置
     */
    private void onBattlePassFinish() {
        PlayerBattlePassModelProp prop = getProp();
        // 收集未领取的奖励，通过邮件发放
        AssetPackage assetPackage = null;
        try {
            assetPackage = this.obtainAllLevelReward(null);
        } catch (Exception e) {
            LOGGER.error("PlayerSeasonBattlePassComponent onBattlePassFinish, obtainAllLevelReward fail", e);
        }
        if (assetPackage != null && !assetPackage.isEmpty()) {
            sendNotTakeRewardMail(assetPackage);
        }
        // 重置属性
        prop.setBpType(CommonEnum.BattltPassType.BPT_NONE)
                .setBpConfigId(0)
                .setExp(0)
                .setLevel(0)
                .setOpenTime(0)
                .setDuration(0)
                .setOpenDailyExpBox(false)
                .setLastDailyRefreshTsMs(0)
                .setTsMs(0);
        prop.getClaimableSilver().clear();
        prop.getClaimableGold().clear();
        prop.getTasks().clear();
        // 终止所有任务
        ((SeasonBattlePassTaskHandler) taskHandler).onBattlePassEnd();
        ownerActor().getEntity().getTaskComponent().removeTaskHandler(taskHandler);
        alreadyInit = false;
        // 删除通行证购买历史,用作限购
        int silverGoodId = ResHolder.getConsts(ConstBpTemplate.class).getSeasonBPSmallGiftID();
        int goldGoodId = ResHolder.getConsts(ConstBpTemplate.class).getSeasonBPmidGiftID();
        int goldProGoodId = ResHolder.getConsts(ConstBpTemplate.class).getSeasonBPBigGiftID();
        PlayerPaymentModelProp paymentModelProp = getOwner().getProp().getPaymentModel();
        if (paymentModelProp.getGoodsHistory().containsKey(silverGoodId)) {
            paymentModelProp.removeGoodsHistoryV(silverGoodId);
            LOGGER.info("PlayerSeasonBattlePassComponent onBattlePassFinish, remove silver bp good history, goodsId={}", silverGoodId);
        }
        if (paymentModelProp.getGoodsHistory().containsKey(goldGoodId)) {
            paymentModelProp.removeGoodsHistoryV(goldGoodId);
            LOGGER.info("PlayerSeasonBattlePassComponent onBattlePassFinish, remove gold bp good history, goodsId={}", goldGoodId);
        }
        if (paymentModelProp.getGoodsHistory().containsKey(goldProGoodId)) {
            paymentModelProp.removeGoodsHistoryV(goldProGoodId);
            LOGGER.info("PlayerSeasonBattlePassComponent onBattlePassFinish, remove goldPro bp good history, goodsId={}", goldProGoodId);
        }
    }

    /**
     * 补发未领取的奖励
     */
    private void sendNotTakeRewardMail(AssetPackage reward) {
        int templateId = ResHolder.getConsts(ConstBpTemplate.class).getReissueOfRewardMail();
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(templateId);
        // 添加奖励
        for (AssetDesc desc : reward.getImmutableAssets()) {
            Struct.ItemPair itemPair = Struct.ItemPair.newBuilder()
                    .setItemTemplateId(desc.getId())
                    .setCount((int) desc.getAmount())
                    .build();
            params.getItemRewardBuilder()
                    .addDatas(itemPair);
        }

        LOGGER.info("PlayerSeasonBattlePassComponent sendNotTakeRewardMail, reward={}", reward);

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(getPlayerId())
                .setZoneId(this.getOwner().getZoneId())
                .build();
        this.getOwner().getMailComponent().sendPersonalMail(receiver, params.build());
    }

    /**
     * 开启任务组
     *
     * @param bpId            battlePassId
     * @param missionGroupIds 开启的任务组的id
     */
    private void openMissionGroup(int bpId, Set<Integer> missionGroupIds) {
        if (taskHandler == null) {
            LOGGER.error("PlayerSeasonBattlePassComponent openMissionGroup, taskHandler is null");
            return;
        }
        List<Integer> tasks;
        PlayerBattlePassModelProp prop = getProp();
        for (Integer id : missionGroupIds) {
            missionGroupTimerMap.remove(id);
            if (prop.getTasks().containsKey(id)) {
                LOGGER.error("PlayerSeasonBattlePassComponent openMissionGroup, missionGroup already open, groupId={}, all open groups={}", id, prop.getTasks().keySet());
                continue;
            }
            tasks = ResHolder.getResService(SeasonBattlePassResService.class).getMissionGroup(bpId, id);
            taskHandler.addBatchTask(new HashSet<>(tasks));
            prop.addEmptyTasks(id).getGroupTasks().addAll(tasks);
            LOGGER.info("PlayerSeasonBattlePassComponent openMissionGroup bpId={}, missionGroupId={}, add week tasks={}", bpId, id, tasks);
        }
    }

    /**
     * 重置battlePass
     */
    private void resetBpTimer(int bpId) {
        LOGGER.info("PlayerSeasonBattlePassComponent resetBp");
        // 清空任务组定时器
        for (ActorTimer timer : missionGroupTimerMap.values()) {
            if (timer == null) {
                continue;
            }
            timer.cancel();
        }
        missionGroupTimerMap.clear();
        if (bpOverTimer != null) {
            bpOverTimer.cancel();
            bpOverTimer = null;
        }
    }

    private void refreshTasks() {
        if (!hasJoinBp()) {
            return;
        }
        // 先开openAttention，才能添加任务事件
        initTaskHandler();
        taskHandler.loadAllTask();
    }

    private void initTaskHandler() {
        if (taskHandler == null) {
            LOGGER.error("PlayerSeasonBattlePassComponent initTaskHandler, taskHandler need create then init");
            return;
        }
        if (!alreadyInit) {
            taskHandler.openAttention();
            ownerActor().getEntity().getTaskComponent().addTaskHandler(taskHandler);
            alreadyInit = true;
            LOGGER.info("PlayerSeasonBattlePassComponent initTaskHandler, taskHandler init");
        } else {
            LOGGER.error("PlayerSeasonBattlePassComponent initTaskHandler, repeat init taskHandler, openAttention={}", taskHandler.openAttention);
        }
    }


    /**
     * 领取任务奖励
     *
     * @param configId bpTaskId
     */
    public void takeTaskReward(int configId) {
        taskHandler.checkOrTakeReward(Lists.newArrayList(configId));
    }


    /**
     * 加经验
     */
    public void addExp(int exp, String reason, int param) {
        LOGGER.info("PlayerSeasonBattlePassComponent addExp, reason={}, add exp={}", reason, exp);
        PlayerBattlePassModelProp prop = getProp();
        int oldExp = prop.getExp();
        int beforeLevel = prop.getLevel();
        int afterExp = oldExp + exp;
        prop.setExp(afterExp);
        tryUnLockBattlePassLevel();
        QlogCncBpExpFlow.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction(reason)
                .setGetExp(exp)
                .setBeforeLevel(beforeLevel)
                .setAfterLevel(prop.getLevel())
                .setAfterExp(prop.getLevel())
                .setAfterExp(afterExp)
                .setObjectId(param).sendToQlog();
    }

    private void tryUnLockBattlePassLevel() {
        PlayerBattlePassModelProp bpProp = getProp();
        int exp = bpProp.getExp();
        int nowLevel = bpProp.getLevel();
        ConstBpTemplate template = ResHolder.getConsts(ConstBpTemplate.class);
        int expPerLevel = template.getBpLevelUpExperience();
        // 默认从一级开始
        int newLevel = exp / expPerLevel + 1;
        if (newLevel == nowLevel) {
            return;
        }
        for (int i = nowLevel + 1; i <= newLevel; i++) {
            // 五十级以内，可领白银奖励
            if (i <= template.getBpBaseRewardLevel()) {
                markSilverRewardClaimable(i);
            }
            // 购买了基础或进阶通行证
            if (isGoldOrProBp()) {
                markGoldRewardClaimable(i);
            }
        }
        bpProp.setLevel(newLevel);
        LOGGER.info("PlayerSeasonBattlePassComponent tryUnlockBattlePassLevel, old level={}, new level={}, silverMarkByte={}, goldMarkByte={}", nowLevel, newLevel, bpProp.getClaimableSilver(), bpProp.getClaimableGold());
    }

    /**
     * 标记第level级的白银奖励可领取
     *
     * @param level 等级
     */
    public void markSilverRewardClaimable(int level) {
        if (level < 1) {
            throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
        }
        int configLevel = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        if (level > configLevel) {
            return;
        }
        // zeo: 标记一个等级可领取，抽一个方法出来
        int markByte = (level - 1) / Long.SIZE;
        PlayerBattlePassModelProp bpProp = getProp();
        while (bpProp.getClaimableSilver().size() < markByte + 1) {
            bpProp.getClaimableSilver().add(0L);
        }
        Long mask = 1L << ((level - 1) % Long.SIZE);
        Long nowByte = bpProp.getClaimableSilverIndex(markByte) | mask;
        bpProp.setClaimableSilverIndex(markByte, nowByte);
    }

    /**
     * 标记第level级的黄金奖励可领取
     *
     * @param level 等级
     */
    public void markGoldRewardClaimable(int level) {
        if (level < 1) {
            throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
        }
        int markByte = (level - 1) / Long.SIZE;
        PlayerBattlePassModelProp bpProp = getProp();
        while (bpProp.getClaimableGold().size() < markByte + 1) {
            bpProp.getClaimableGold().add(0L);
        }
        Long mask = 1L << ((level - 1) % Long.SIZE);
        Long nowByte = bpProp.getClaimableGoldIndex(markByte) | mask;
        bpProp.setClaimableGoldIndex(markByte, nowByte);
    }

    /**
     * 领取全部奖励
     * reason为null时，不发送下发奖励
     */
    public AssetPackage obtainAllLevelReward(CommonEnum.Reason reason) {
        if (!hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        AssetPackage.Builder asset = AssetPackage.builder();
        PlayerBattlePassModelProp prop = getProp();
        int configLevel = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        List<IntPairType> extraRewardId = ResHolder.getTemplate(SeasonBpScheduleTemplate.class, prop.getBpConfigId()).getExtraRewardIdPairList();
        // 领取白银奖励
        List<Integer> recordSilver = new ArrayList<>();
        for (int i = 0; i < prop.getClaimableSilverSize(); i++) {
            if (prop.getClaimableSilverIndex(i) == 0L) {
                // 没有可领的奖励
                continue;
            }
            //每个Long只使用configLevel个bit位
            for (int j = 0; j < Long.SIZE; j++) {
                int realLevel = i * Long.SIZE + (j + 1);
                // 白银礼包超出configLevel没有奖励
                if (realLevel > configLevel) {
                    break;
                }
                if (isClaimable(prop.getClaimableSilverIndex(i), j)) {
                    recordSilver.add(realLevel);
                    asset.plusItems(getBpReward(prop.getBpConfigId(), realLevel, 0));
                }
            }
            // 置为已全部领取
            prop.setClaimableSilverIndex(i, 0L);
        }
        if (!isGoldOrProBp()) {
            if (reason != null && !asset.isEmpty()) {
                getOwner().getAssetComponent().give(asset.build(), reason, recordSilver.toString());
            }
            LOGGER.info("PlayerSeasonBattlePassComponent obtainAllLevelReward, take silver reward levels={}", recordSilver);
            return asset.build();
        }
        // 领取黄金礼包
        List<Integer> recordGold = new ArrayList<>();
        for (int i = 0; i < prop.getClaimableGoldSize(); i++) {
            if (prop.getClaimableGoldIndex(i) == 0L) {
                continue;
            }
            for (int j = 0; j < Long.SIZE; j++) {
                int realLevel = i * Long.SIZE + (j + 1);
                if (realLevel > prop.getLevel()) {
                    break;
                }
                if (isClaimable(prop.getClaimableGoldIndex(i), j)) {
                    if (realLevel <= configLevel) {
                        asset.plusItems(getBpReward(prop.getBpConfigId(), realLevel, 1));
                    } else {
                        asset.plusItems(extraRewardId);
                    }
                    recordGold.add(realLevel);
                }
            }
            prop.setClaimableGoldIndex(i, 0L);
        }
        if (reason != null && !asset.isEmpty()) {
            Set<Integer> allRecordLevel = new HashSet<>();
            allRecordLevel.addAll(recordSilver);
            allRecordLevel.addAll(recordGold);
            getOwner().getAssetComponent().give(asset.build(), reason, new ArrayList<>(allRecordLevel).toString());
        }
        LOGGER.info("PlayerSeasonBattlePassComponent obtainAllLevelReward, take silver reward levels={}, gold reward levels={}", recordSilver, recordGold);
        return asset.build();
    }

    /**
     * 领取某一级奖励
     */

    public AssetPackage obtainSpecialLevelReward(int level, boolean extraReward, CommonEnum.Reason reason) {
        if (!hasJoinBp()) {
            throw new GeminiException(ErrorCode.BATTLE_PASS_ACTIVITY_NOT_OPEN);
        }
        if (level <= 0 || level > getBpLevel()) {
            throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
        }
        AssetPackage.Builder asset = AssetPackage.builder();
        int configLevel = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        Set<Integer> record = new HashSet<>();
        // 尝试领取白银礼包
        List<IntPairType> silverReward = takeSilverReward(level);
        if (!silverReward.isEmpty()) {
            record.add(level);
            asset.plusItems(silverReward);
        }
        if (isGoldOrProBp()) {
            // 尝试领取黄金奖励
            if (extraReward) {
                // 一次性领取50级之后的全部奖励
                for (int i = configLevel + 1; i <= getProp().getLevel(); i++) {
                    List<IntPairType> goldReward = takeGoldReward(i);
                    if (!goldReward.isEmpty()) {
                        record.add(i);
                        asset.plusItems(goldReward);
                    }
                }
            } else {
                List<IntPairType> goldReward = takeGoldReward(level);
                if (!goldReward.isEmpty()) {
                    record.add(level);
                    asset.plusItems(goldReward);
                }
            }
        }
        if (reason != null && !asset.isEmpty()) {
            getOwner().getAssetComponent().give(asset.build(), reason, new ArrayList<>(record).toString());
        }
        LOGGER.info("PlayerSeasonBattlePassComponent obtainSpecialLevelReward, level={}, extraReward={}, reason={}, record={}", level, extraReward, reason, record);
        return asset.build();
    }


    private boolean isClaimable(long markByte, int offset) {
        long maskByte = 1L << offset;
        return (maskByte & markByte) != 0;
    }

    /**
     * 领取某个等级的白银奖励
     */
    private List<IntPairType> takeSilverReward(int level) {
        int configLevel = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        if (level <= 0 || level > configLevel) {
            LOGGER.info("PlayerSeasonBattlePassComponent takeSilverReward, level out of silver reward bound, level={}", level);
            return Collections.emptyList();
        }
        List<IntPairType> reward = getBpReward(getProp().getBpConfigId(), level, 0);
        PlayerBattlePassModelProp prop = getProp();
        int markByteIndex = (level - 1) / Long.SIZE;
        int markByteOffset = (level - 1) % Long.SIZE;
        if (markByteIndex >= prop.getClaimableSilverSize()) {
            LOGGER.error("PlayerSeasonBattlePassComponent takeSilverReward, index out of bound, level={}, index={}, size={}", level, markByteIndex, prop.getClaimableSilverSize());
            throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
        }
        Long markByte = prop.getClaimableSilverIndex(markByteIndex);
        if (isClaimable(markByte, markByteOffset)) {
            long mask = 1L << markByteOffset;
            long result = markByte & (~mask);
            prop.setClaimableSilverIndex(markByteIndex, result);
        } else {
            LOGGER.info("PlayerSeasonBattlePassComponent takeSilverReward, silver level reward can not receive, level={}", level);
            return Collections.emptyList();
        }
        return reward;
    }

    private List<IntPairType> takeGoldReward(int level) {
        if (!isGoldOrProBp()) {
            LOGGER.info("PlayerSeasonBattlePassComponent takeGoldReward, invalid bpType, bpType={}", getProp().getBpType());
            return Collections.emptyList();
        }
        PlayerBattlePassModelProp prop = getProp();
        int configLevel = ResHolder.getConsts(ConstBpTemplate.class).getBpBaseRewardLevel();
        List<IntPairType> extraRewardId = ResHolder.getTemplate(SeasonBpScheduleTemplate.class, prop.getBpConfigId()).getExtraRewardIdPairList();
        List<IntPairType> reward;
        if (level <= configLevel) {
            reward = getBpReward(prop.getBpConfigId(), level, 1);
        } else {
            reward = extraRewardId;
        }
        int markByteIndex = (level - 1) / Long.SIZE;
        int markByteOffset = (level - 1) % Long.SIZE;
        if ((markByteIndex < 0) || (markByteIndex >= prop.getClaimableGoldSize())) {
            LOGGER.error("PlayerSeasonBattlePassComponent takeGoldReward, index out of bound, level={}, index={}, size={}", level, markByteIndex, prop.getClaimableGoldSize());
            throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
        }
        Long markByte = prop.getClaimableGoldIndex(markByteIndex);
        if (isClaimable(markByte, markByteOffset)) {
            long mask = 1L << markByteOffset;
            long result = markByte & (~mask);
            prop.setClaimableGoldIndex(markByteIndex, result);
        } else {
            LOGGER.info("PlayerSeasonBattlePassComponent takeGoldReward, gold level reward can not receive, level={}", level);
            return Collections.emptyList();
        }
        return reward;
    }


    public boolean isGoldBp() {
        PlayerBattlePassModelProp prop = getProp();
        return prop.getBpType() == CommonEnum.BattltPassType.BPT_GOLD;
    }

    public boolean isGoldProBp() {
        PlayerBattlePassModelProp prop = getProp();
        return prop.getBpType() == CommonEnum.BattltPassType.BPT_GOLD_PRO;
    }

    public boolean isGoldOrProBp() {
        return isGoldBp() || isGoldProBp();
    }

    /**
     * 是否已参与通行证
     */
    public boolean hasJoinBp() {
        return getProp().getBpConfigId() > 0;
    }

    /**
     * 获取bp奖励
     */
    public List<IntPairType> getBpReward(int bpId, int level, int type) {
        // 有性能问题，但是容易适配热更
        for (BpRewardTemplate template : ResHolder.getInstance().getListFromMap(BpRewardTemplate.class)) {
            if (template.getBpId() == bpId && template.getLevel() == level) {
                if (type == 0) {
                    return template.getSilverRewardPairList();
                } else {
                    return template.getGoldRewardPairList();
                }
            }
        }
        throw new GeminiException("PlayerSeasonBattlePassComponent getBpReward, not found bpRewardConfig, bpId={}, bpLevel={}, isSilverReward={} isGoldReward={}", bpId, level, type == 0, type == 1);
    }

    public int getBpLevel() {
        return getProp().getLevel();
    }

    public int getExp() {
        return getProp().getExp();
    }
}

package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 内城收集资源事件
 *
 * <AUTHOR>
 */
public class PlayerInnerCollectResourceEvent extends PlayerTaskEvent {
    private final Map<CommonEnum.CurrencyType, Long> resource;

    public PlayerInnerCollectResourceEvent(PlayerEntity entity, Map<CommonEnum.CurrencyType, Long> resource) {
        super(entity);
        this.resource = resource;
    }

    public Map<CommonEnum.CurrencyType, Long> getResource() {
        return resource;
    }
}

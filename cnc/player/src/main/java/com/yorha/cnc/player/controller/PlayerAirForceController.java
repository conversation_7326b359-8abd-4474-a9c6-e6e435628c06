package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerDataRecordComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.*;

import static com.yorha.common.enums.error.ErrorCode.LOGISTICS_UNSUPPORT_ACTION;
import static com.yorha.proto.CommonEnum.LogisticsActionType.LAT_RETURN;

/**
 * 空军相关的协议处理
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_AIR_FORCE)
public class PlayerAirForceController {

    @CommandMapping(code = MsgType.PLAYER_CREATESPYPLANE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAirForce.Player_CreateSpyPlane_C2S msg) {
        SsScenePlane.CreateSpyPlaneAsk.Builder builder = SsScenePlane.CreateSpyPlaneAsk.newBuilder();
        builder.setPlayerId(playerEntity.getPlayerId())
                .getSpyInfoBuilder()
                .setTargetId(msg.getSpyInfo().getTargetId())
                .setActionType(msg.getSpyInfo().getActionType())
                .setFinishNeedReturn(msg.getSpyInfo().getFinishNeedReturn())
                .setSpyPlaneId(msg.getSpyInfo().getSpyPlaneId())
                .setSpyModel(playerEntity.getPlaneComponent().getPlaneModel(msg.getSpyInfo().getSpyPlaneId()));
        builder.getSpyInfoBuilder().setPoint(StructPB.PointPB.newBuilder().setX(msg.getSpyInfo().getPoint().getX()).setY(msg.getSpyInfo().getPoint().getY()));
        builder.getSpyInfoBuilder().setSrcPoint(StructPB.PointPB.newBuilder().setX(msg.getSpyInfo().getSrcPoint().getX()).setY(msg.getSpyInfo().getSrcPoint().getY()));

        //构建侦查机
        playerEntity.getPlaneComponent().createSpyPlaneEntity(builder.build());

        if (builder.getSpyInfoBuilder().getActionType() == CommonEnum.SpyPlaneActionType.SPAT_SPY) {
            PlayerDataRecordComponent recordComponent = playerEntity.getDataRecordComponent();
            recordComponent.updateDataRecord(CommonEnum.DataRecordType.DRT_SPY_TIMES, CommonEnum.DataRecordCalcType.DRCT_ADD, 1);
        }
        return PlayerAirForce.Player_CreateSpyPlane_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CHECKSPY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAirForce.Player_CheckSpy_C2S msg) {
        //构建入参
        StructMsg.SpyInfo.Builder builder = StructMsg.SpyInfo.newBuilder().setTargetId(msg.getTargetId())
                .setActionType(CommonEnum.SpyPlaneActionType.SPAT_SPY);
        //侦查检测（包含玩家侧，大地图侧）
        int errorCodeId = playerEntity.getPlaneComponent().checkCreateSpyPlane(builder.build());
        PlayerAirForce.Player_CheckSpy_S2C.Builder res = PlayerAirForce.Player_CheckSpy_S2C.newBuilder();
        res.setErrorCode(errorCodeId);
        return res.build();
    }

    @CommandMapping(code = MsgType.PLAYER_CHANGEACTIONSPYPLANE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAirForce.Player_ChangeActionSpyPlane_C2S msg) {
        SsScenePlane.ChangeActionSpyPlaneAsk.Builder builder = SsScenePlane.ChangeActionSpyPlaneAsk.newBuilder();
        builder.setPlayerId(playerEntity.getPlayerId())
                .getSpyInfoBuilder()
                .setTargetId(msg.getSpyInfo().getTargetId())
                .setActionType(msg.getSpyInfo().getActionType())
                .setFinishNeedReturn(msg.getSpyInfo().getFinishNeedReturn())
                .setSpyPlaneId(msg.getSpyInfo().getSpyPlaneId());
        builder.getSpyInfoBuilder().setPoint(StructPB.PointPB.newBuilder().setX(msg.getSpyInfo().getPoint().getX()).setY(msg.getSpyInfo().getPoint().getY()));
        builder.getSpyInfoBuilder().setSrcPoint(StructPB.PointPB.newBuilder().setX(msg.getSpyInfo().getSrcPoint().getX()).setY(msg.getSpyInfo().getSrcPoint().getY()));
        //侦察机改变行为
        playerEntity.getPlaneComponent().changeSpyPlaneAction(builder.build());
        if (builder.getSpyInfoBuilder().getActionType() == CommonEnum.SpyPlaneActionType.SPAT_SPY) {
            PlayerDataRecordComponent recordComponent = playerEntity.getDataRecordComponent();
            recordComponent.updateDataRecord(CommonEnum.DataRecordType.DRT_SPY_TIMES, CommonEnum.DataRecordCalcType.DRCT_ADD, 1);
        }
        return PlayerAirForce.Player_ChangeActionSpyPlane_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CREATELOGISTICSPLANE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAirForce.Player_CreateLogisticsPlane_C2S msg) {
        try {
            playerEntity.getPlaneComponent().checkLogisticsAction(msg.getInfo());
        } catch (GeminiException e) {
            if (e.getCodeId() != LOGISTICS_UNSUPPORT_ACTION.getCodeId()) {
                throw e;
            }
            return PlayerAirForce.Player_CreateLogisticsPlane_S2C.getDefaultInstance();
        }

        playerEntity.getPlaneComponent().changeLogisticsAction(msg.getInfo());
        return PlayerAirForce.Player_CreateLogisticsPlane_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CHANGELOGISTICSPLANE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerAirForce.Player_ChangeLogisticsPlane_C2S msg) {
        if (msg.getInfo().getActionType() != LAT_RETURN) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        playerEntity.getPlaneComponent().changeLogisticsAction(msg.getInfo());
        return PlayerAirForce.Player_ChangeLogisticsPlane_S2C.getDefaultInstance();
    }
}

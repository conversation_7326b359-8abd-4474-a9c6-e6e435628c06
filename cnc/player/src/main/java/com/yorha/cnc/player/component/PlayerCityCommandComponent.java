package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.PlayerInnerBuildRHModelProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.SkillRhTemplate;

import java.util.HashMap;
import java.util.Map;

public class PlayerCityCommandComponent extends PlayerComponent implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCityCommandComponent.class);

    private PlayerInnerBuildRHModelProp innerBuildModel;

    public PlayerCityCommandComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        innerBuildModel = getOwner().getProp().getPlayerInnerBuildRHModel();
    }

    @Override
    public void onLoad(boolean isRegister) {
        if (isRegister) {
            // 解锁兵
            ResHolder.getConsts(ConstTemplate.class).getInitialUnit().forEach(this::unlockUnit);
            // 解锁初始技能
            ResHolder.getConsts(ConstTemplate.class).getDefaultBattleSkill().forEach(this::unlockSkill);
        }
    }

    public void unlockUnit(int unitId) {
        if (innerBuildModel.getUnits().contains(unitId)) {
            return;
        }
        if (CommonEnum.UnitType.forNumber(unitId) == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        innerBuildModel.getUnits().add(unitId);
    }

    public boolean isUnitUnlock(int unitId) {
        return innerBuildModel.getUnits().contains(unitId);
    }

    public void unlockSkill(int skillId) {
        if (innerBuildModel.getSkills().contains(skillId)) {
            return;
        }
        if (!ResHolder.getInstance().hasTemplate(SkillRhTemplate.class, skillId)) {
            LOGGER.info("unlock skill, skill:{} not exist", skillId);
            return;
        }
        innerBuildModel.getSkills().add(skillId);
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.CITY_COMMAND;
    }

    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        long sum = 0;
        Map<CommonEnum.AdditionSourceType, Long> res = new HashMap<>();
        res.put(CommonEnum.AdditionSourceType.AST_CITY_COMMAND, sum);
        return res;
    }

}
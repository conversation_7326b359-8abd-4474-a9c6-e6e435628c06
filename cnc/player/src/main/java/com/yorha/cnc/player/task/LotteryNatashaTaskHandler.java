package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.unit.PlayerLotteryNatashaUnit;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskDailyeventTemplate;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 娜塔莎转盘活动任务
 *
 * <AUTHOR>
 * @date 2024/5/6
 */
public class LotteryNatashaTaskHandler extends AbstractTaskHandler {

    private final PlayerLotteryNatashaUnit unit;
    private final Int32TaskInfoMapProp prop;
    /**
     * 防止重复监听任务事件（和TaskComponent之间有加载时序问题）
     */
    private boolean loaded = false;

    public LotteryNatashaTaskHandler(PlayerEntity entity, Int32TaskInfoMapProp prop, String taskPoolName, int activityId, PlayerLotteryNatashaUnit unit) {
        super(entity, taskPoolName + "_" + activityId);
        this.prop = prop;
        this.unit = unit;
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return prop;
    }

    @Override
    int getTaskIdById(int configId) {
        return ResHolder.getInstance().getValueFromMap(TaskDailyeventTemplate.class, configId).getTaskId();
    }

    @Override
    public void loadAllTask() {
        if (loaded) {
            return;
        }
        loaded = true;
        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            addTaskEventMonitor(value);
        }
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> taskId) {
        throw new GeminiException("illegal way.");
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }

    @Override
    public void onTaskFinish(int configId, TaskGmEnum gmType) {
        super.onTaskFinish(configId, gmType);
        unit.onTaskFinish(configId);
    }
}

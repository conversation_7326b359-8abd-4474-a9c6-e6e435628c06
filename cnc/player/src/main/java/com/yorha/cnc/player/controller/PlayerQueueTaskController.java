package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.QueueTaskType;
import com.yorha.proto.PlayerQueueTask.*;
import com.yorha.proto.StructPB.ItemPB;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 时间队列相关接口
 *
 * <AUTHOR>
 * 2021年11月17日 15:11:00
 */
@Controller(module = CommonEnum.ModuleEnum.ME_QUEUE_TASK)
public class PlayerQueueTaskController {

    /**
     * 道具加速队列任务
     */
    @CommandMapping(code = MsgType.PLAYER_SPEEDQUEUETASK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SpeedQueueTask_C2S msg) {
        // 建筑类型
        QueueTaskType queueType = msg.getQueueType();
        long taskId = msg.getTaskId();
        List<ItemPB> itemList = msg.getItemList().getDatasList();
        List<IntPairType> collect = itemList.stream().map(it -> IntPairType.makePair(it.getTemplateId(), it.getNum())).collect(Collectors.toList());
        playerEntity.getPlayerQueueTaskComponent().speedUpQueueTask(queueType, taskId, collect);
        return Player_SpeedQueueTask_S2C.getDefaultInstance();
    }

    /**
     * 立即完成队列任务
     */
    @CommandMapping(code = MsgType.PLAYER_FINISHQUEUETASK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_FinishQueueTask_C2S msg) {
        // 建筑id
        QueueTaskType queueType = msg.getQueueType();
        long taskId = msg.getTaskId();
        return playerEntity.getPlayerQueueTaskComponent().tryFinishQueueTask(queueType, taskId);
    }

    /**
     * 取消队列任务
     */
    @CommandMapping(code = MsgType.PLAYER_CANCELQUEUETASK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_CancelQueueTask_C2S msg) {
        QueueTaskType queueType = msg.getQueueType();
        long taskId = msg.getTaskId();
        playerEntity.getPlayerQueueTaskComponent().tryCancelQueueTask(queueType, taskId);
        return Player_CancelQueueTask_S2C.getDefaultInstance();
    }

    /**
     * 加速队列任务
     */
    @CommandMapping(code = MsgType.PLAYER_QUEUEFREESPEED_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_QueueFreeSpeed_C2S msg) {
        long queueId = msg.getQueueId();
        playerEntity.getPlayerQueueTaskComponent().queueFreeSpeed(queueId);
        return Player_QueueFreeSpeed_S2C.getDefaultInstance();
    }
}

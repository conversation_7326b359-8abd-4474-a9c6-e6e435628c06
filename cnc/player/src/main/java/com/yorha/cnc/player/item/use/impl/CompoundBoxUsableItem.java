package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerPB;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 奖池宝箱
 */
public class CompoundBoxUsableItem extends AbstractUsableItem {

    private static final Logger LOGGER = LogManager.getLogger(CompoundBoxUsableItem.class);
    private final List<ItemReward> rewardList;
    /**
     * 奖池宝箱开出的机甲配件id列表
     */
    private int rewardId;

    private List<List<ItemReward>> rolledReward = new ArrayList<>();

    public CompoundBoxUsableItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
        rewardList = new ArrayList<>();
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        if (template == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        this.rewardId = template.getEffectId();

        // 前置把奖励确定
        for (int i = 0; i < num; i++) {
            List<ItemReward> itemRewardList = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
            rolledReward.add(itemRewardList);
        }

    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        for (List<ItemReward> rewards : rolledReward) {
            if (CollectionUtils.isEmpty(rewards)) {
                continue;
            }
            rewards.forEach(reward -> {
                playerEntity.getItemComponent().addItem(reward.getItemTemplateId(), reward.getCount(), CommonEnum.Reason.ICR_BOX, "");
                // 如果生成奖励是自动使用的机甲配件道具，奖励列表中不需要添加
                rewardList.add(reward);
            });
        }

        LOGGER.info("player use compound box item {} reward get {}", this, rewardList);
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
        PlayerPB.ItemUseResultPB.Builder resultPB = PlayerPB.ItemUseResultPB.newBuilder();
        resultPB.setItems(ItemResService.makeItemListPB(rewardList));
        response.setResult(resultPB);
    }
}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.MechaFireEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.game.gen.prop.MileStoneZoneInfoProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.*;
import com.yorha.proto.PlayerInnerBuildLogic.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

/**
 * 内城杂项
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_INNER_BUILD_LOGIC)
public class PlayerInnerBuildLogicController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerInnerBuildLogicController.class);

    /**
     * 关闭战机解锁弹框提醒
     */
    @CommandMapping(code = MsgType.PLAYER_PROMPTFORPLANEUNLOCK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_PromptForPlaneUnlock_C2S msg) {
        long battlePlaneId = msg.getBattlePlaneId();
        playerEntity.getPlaneComponent().promptForPlaneUnlock(battlePlaneId);
        return Player_PromptForPlaneUnlock_S2C.getDefaultInstance();
    }

    /**
     * 查看公告板
     */
    @CommandMapping(code = MsgType.PLAYER_NOTICEBOARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_GetNoticeBoard_C2S msg) {
//        Player_GetNoticeBoard_S2C.Builder builder = Player_GetNoticeBoard_S2C.newBuilder();
//        List<NoticeInfo> noticeList = playerEntity.getInnerBuildComponent().getNoticeBoardInfoList();
//        if (CollectionUtils.isNotEmpty(noticeList)) {
//            builder.addAllInfo(noticeList);
//        }
//        return builder.build();
        return Player_GetNoticeBoard_S2C.getDefaultInstance();
    }

    /**
     * 查询里程碑基础
     */
    @CommandMapping(code = MsgType.PLAYER_GETMILESTONEHISTORY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMilestone.Player_GetMileStoneHistory_C2S msg) {
        PlayerMilestone.Player_GetMileStoneHistory_S2C.Builder builder = PlayerMilestone.Player_GetMileStoneHistory_S2C.newBuilder();
        SsScenePlayer.GetMileStoneHistoryAns baseInfoHandler = playerEntity.getMileStoneComponent().getBaseInfoHandler();
        builder.putAllMileStoneData(baseInfoHandler.getMileStoneDataMap());
        return builder.build();
    }


    /**
     * 查询里程碑详情
     */
    @CommandMapping(code = MsgType.PLAYER_GETMILESTONERANK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMilestone.Player_GetMileStoneRank_C2S msg) {
        int mileStoneId = msg.getMileStoneId();
        PlayerMilestone.Player_GetMileStoneRank_S2C.Builder builder = PlayerMilestone.Player_GetMileStoneRank_S2C.newBuilder();
        SsScenePlayer.GetMileStoneRankInfoAns rankInfoMap = playerEntity.getMileStoneComponent().getRankInfoHandler(mileStoneId);
        for (Map.Entry<Long, Struct.MileStoneClanInfo> entry : rankInfoMap.getMileStoneRankDataMap().entrySet()) {
            MileStoneClanInfoProp mileStoneClanInfoProp = new MileStoneClanInfoProp();
            mileStoneClanInfoProp.mergeFromSs(entry.getValue());
            builder.putRankInfo(entry.getKey(), mileStoneClanInfoProp.getCopyCsBuilder().build());
        }
        for (Map.Entry<Integer, Struct.MileStoneZoneInfo> entry : rankInfoMap.getMileStoneZoneRankDataMap().entrySet()) {
            MileStoneZoneInfoProp mileStoneZoneInfoProp = new MileStoneZoneInfoProp();
            mileStoneZoneInfoProp.mergeFromSs(entry.getValue());
            builder.putZoneRankInfo(entry.getKey(), mileStoneZoneInfoProp.getCopyCsBuilder().build());
        }
        return builder.build();
    }

    /**
     * 领取里程碑奖励
     */
    @CommandMapping(code = MsgType.PLAYER_TAKEMILESTONEREWARDC2S_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerMilestone.Player_TakeMileStoneReward_C2S msg) {
        int mileStoneId = msg.getMileStoneId();
        PlayerMilestone.Player_TakeMileStoneReward_S2C.Builder builder = PlayerMilestone.Player_TakeMileStoneReward_S2C.newBuilder();
        AssetPackage assetPackage = playerEntity.getMileStoneComponent().takeRewardHandler(mileStoneId);
        builder.setRewardInfo(assetPackage.toPb());
        return builder.build();
    }

    /**
     * 游戏内问卷调查
     */
    @CommandMapping(code = MsgType.PLAYER_COMPLETEQUESTIONNAIRE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerInnerBuildLogic.CompleteQuestionnaireC2S msg) {
        int configId = msg.getConfigId();
        int answerId = msg.getAnswerId();
        AssetPackage assetPackage = playerEntity.getInnerQuestComponent().answerQuestHandler(configId, answerId);
        PlayerInnerBuildLogic.CompleteQuestionnaireS2C.Builder builder = PlayerInnerBuildLogic.CompleteQuestionnaireS2C.newBuilder();
        builder.setReward(assetPackage.toPb());
        return builder.build();
    }

    /**
     * 游戏内问卷调查失效
     */
    @CommandMapping(code = MsgType.PLAYER_QUESTIONNAIREEXPIRED_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerInnerBuildLogic.QuestionnaireExpiredC2S msg) {
        int configId = msg.getConfigId();
        boolean isActive = msg.getIsActive();
        playerEntity.getInnerQuestComponent().expiredQuestHandler(configId, isActive);
        return PlayerInnerBuildLogic.QuestionnaireExpiredS2C.getDefaultInstance();
    }

    /**
     * 客户端新手阶段表
     */
    @CommandMapping(code = MsgType.PLAYER_UPDATECLIENTSTAGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerInnerBuildLogic.Player_UpdateClientStage_C2S msg) {
        LOGGER.debug("client stage update. msg={}", msg);
        int groupId = msg.getGroupId();
        int stage = msg.getStage();
        boolean flag = msg.getFlag();
        playerEntity.getNewbieComponent().updateClientStage(groupId, stage, flag);
        return Player_UpdateClientStage_S2C.getDefaultInstance();
    }


    /**
     * 机甲堡垒灭火（完成第一个主线引导任务）
     * 良性接口，不阻断，只做定向逻辑--把状态改向目标状态
     */
    @CommandMapping(code = MsgType.PLAYER_COMPLETEMAINTASK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerInnerBuildLogic.Player_CompleteMainTask_C2S msg) {
        // 防止客户端乱来，这里要传id并校验
        int mainTask = msg.getTaskId();
        int firstMainTaskId = ResHolder.getResService(ConstKVResService.class).getTemplate().getFirstMainTaskId();
        if (firstMainTaskId != mainTask) {
            // 不做业务阻断，防止客户端卡死（能走到这那问题就大了）
            LOGGER.error("mechaFire player:{} serious error. taskId:{}", playerEntity.getPlayerId(), mainTask);
            return buildFailMechaFireMsg(false);
        }
        TaskInfoProp taskInfoProp = playerEntity.getProp().getTaskSystem().getTaskMain().get(mainTask);
        if (taskInfoProp == null) {
            WechatLog.error("mechaFire player:{} error. taskId:{}", playerEntity.getPlayerId(), mainTask);
            return buildFailMechaFireMsg(false);
        }
        LOGGER.info("mechaFire client start.");
        CommonEnum.TaskStatus oldStatus = taskInfoProp.getStatus();
        new MechaFireEvent(playerEntity).dispatch();
        CommonEnum.TaskStatus newStatus = taskInfoProp.getStatus();

        LOGGER.info("mechaFire client end. taskId:{} oldStatus:{} newStatus:{}", taskInfoProp.getId(), oldStatus, newStatus);
        return buildFailMechaFireMsg(true);
    }

    @NotNull
    private Player_CompleteMainTask_S2C buildFailMechaFireMsg(boolean taskComplete) {
        return Player_CompleteMainTask_S2C.newBuilder().setTaskComplete(taskComplete).build();
    }

   
}

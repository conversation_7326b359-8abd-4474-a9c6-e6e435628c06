package com.yorha.cnc.player.component;

import chinaQlog.flow.QlogChinaPlayerLogin;
import chinaQlog.flow.QlogChinaPlayerLogout;
import chinaQlog.flow.QlogChinaPlayerRegister;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerAddEnergyEvent;
import com.yorha.cnc.player.event.task.PlayerConsumeEnergyEvent;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.qlog.city.CitySkinAction;
import com.yorha.common.enums.qlog.clan.ClanGiftAction;
import com.yorha.common.enums.qlog.clan.ClanGiftType;
import com.yorha.common.enums.qlog.mail.MailActionType;
import com.yorha.common.enums.qlog.mail.MailTabType;
import com.yorha.common.enums.reason.GuildPersonalScoreActionType;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.QlogUtil;
import com.yorha.common.qlog.json.player.BpInfo;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerBasicInfoProp;
import com.yorha.game.gen.prop.PlayerZoneModelProp;
import com.yorha.game.gen.prop.PowerInfoUnitProp;
import com.yorha.game.gen.prop.QueueTaskProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsSceneDungeon;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.*;
import res.template.MailTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class PlayerQlogComponent extends PlayerComponent implements QlogPlayerFlowInterface, QlogClanFlowInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerQlogComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerConsumeEnergyEvent.class, PlayerQlogComponent::monitorConsumeEnergyEvent);
        EntityEventHandlerHolder.register(PlayerAddEnergyEvent.class, PlayerQlogComponent::monitorAddEnergyEvent);
    }

    private String accountName = "";
    private Struct.Point bornPoint = null;

    public PlayerQlogComponent(PlayerEntity owner) {
        super(owner);
    }

    private static void monitorConsumeEnergyEvent(PlayerConsumeEnergyEvent event) {
        event.getPlayer().getQlogComponent().sendEnergyQLog(event.getEnergyNum(), event.getEnergyCostReason(), event.getBeforeEnergy(), event.getReasonTemplateId(), 1);
    }

    private static void monitorAddEnergyEvent(PlayerAddEnergyEvent event) {
        event.getPlayer().getQlogComponent().sendEnergyQLog(event.getEnergyNum(), event.getEnergyCostReason(), event.getBeforeEnergy(), event.getReasonTemplateId(), 0);
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        sendLoginQlog();
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            // 考虑改成异步的
            afterRegisterQLog();
            // 玩家快照QLog
            // FIXME： 删了功能之后QLog不完整，先屏蔽
            // playerSnapshotQLog();
        }
    }

    public void afterRegisterQLog() {
        CommonMsg.ClientInfo clientInfo = getOwner().getSessionComponent().getClientInfo();
        if (ServerContext.isChinaVersion()) {
            QlogChinaPlayerRegister chinaCncPlayerRegister = QlogChinaPlayerRegister.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String());
            chinaCncPlayerRegister.setSystemSoftware(clientInfo.getSystemSoftware())
                    .setSystemHardware(clientInfo.getSystemHardware())
                    .setGlVersion(clientInfo.getGLVersion())
                    .setDeviceId(clientInfo.getDeviceId())
                    .setMemory(clientInfo.getMemory())
                    .setAdjustId(clientInfo.getAdjustId())
                    .setCountry("")
                    .setLoginChannel(clientInfo.getChannelId())
                    .setXwid("")
                    .setRegChannel(getOwner().getProp().getBasicInfo().getRegChannel());
            if (bornPoint != null) {
                chinaCncPlayerRegister.setBornPartId(MapGridDataManager.getPartId(BigSceneConstants.BIG_SCENE_MAP_ID, bornPoint.getX(), bornPoint.getY()));
            }
            chinaCncPlayerRegister.sendToQlog();
            return;
        }
        QlogPlayerRegister qlogCncPlayerRegister = QlogPlayerRegister.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String());
        qlogCncPlayerRegister.setSystemSoftware(clientInfo.getSystemSoftware())
                .setSystemHardware(clientInfo.getSystemHardware())
                .setGlVersion(clientInfo.getGLVersion())
                .setDeviceId(clientInfo.getDeviceId())
                .setMemory(clientInfo.getMemory())
                .setAdjustId(clientInfo.getAdjustId())
                .setCountry("")
                .setLoginChannel(clientInfo.getChannelId())
                .setXwid("");
        if (bornPoint != null) {
            qlogCncPlayerRegister.setBornPartId(MapGridDataManager.getPartId(BigSceneConstants.BIG_SCENE_MAP_ID, bornPoint.getX(), bornPoint.getY()));
        }
        qlogCncPlayerRegister.sendToQlog();
    }

    public void setBornPoint(Struct.Point point) {
        bornPoint = point;
    }

    @Override
    public void afterLogout() {
        // FIXME： 删了功能之后QLog不完整，先屏蔽
        // playerSnapshotQLog();
    }

    @Override
    public String getGameappid() {
        if (ServerContext.isChinaVersion()) {
            CommonMsg.ClientInfo clientInfo = getOwner().getClientInfo();
            if (clientInfo == null) {
                return "";
            }
            return QlogUtil.getAppId(clientInfo.getChannelId());
        }
        return GameLogicConstants.GAME_APPID;
    }

    @Override
    public String getVOpenID() {
        return getOwner().getProp().getOpenId();
    }

    @Override
    public String getAccountCreate_time() {
        return "";
    }

    @Override
    public String getAccountReg_time() {
        return "";
    }

    @Override
    public String getServer_type() {
        return "";
    }

    //这里应该是玩家身上记录的创角时候的服务器id
    @Override
    public String getIZoneAreaID() {
        PlayerZoneModelProp zoneModelProp = getOwner().getProp().getZoneModel();
        return String.valueOf(zoneModelProp.getBornZoneId());
    }

    @Override
    public String getGameSvrId() {
        return String.valueOf(ownerActor().getZoneId());
    }

    @Override
    public String getWorldId() {
        return String.valueOf(ServerContext.getServerInfo().getWorldId());
    }

    @Override
    public String getNow_coordinate() {
        return "";
    }

    @Override
    public String getVRoleID() {
        return Long.toString(getOwner().getPlayerId());
    }

    @Override
    public String getVRoleName() {
        return getOwner().getName();
    }

    @Override
    public int getRole_num() {
        return getOwner().getProp().getBasicInfo().getRoleSeqOfAccount();
    }

    @Override
    public String getRole_type() {
        if (getOwner().isInternalPlayer()) {
            return "2";
        }
        return "1";
    }

    @Override
    public String getRoleCreate_time() {
        return TimeUtils.timeStampMs2String(getOwner().getProp().getCreateTime());
    }

    @Override
    public int getILevel() {
        return 1;
    }

    @Override
    public int getIVipLevel() {
        return getOwner().getVipComponent().getVipLevel();
    }

    @Override
    public long getIRoleCE() {
        return (int) getOwner().getProp().getPlayerPowerInfo().getTotalPower();
    }

    @Override
    public long getHighestRole_power() {
        return (int) getOwner().getDataRecordComponent().getRecordValue(CommonEnum.DataRecordType.DRT_MAX_POWER);
    }

    @Override
    public int getPlayerFriendsNum() {
        if (getOwner().isRegister()) {
            return 0;
        }
        FriendPlayerEntity friendPlayerEntity = ownerActor().getOrLoadFriendPlayerEntity();
        if (friendPlayerEntity == null) {
            return 0;
        }
        return friendPlayerEntity.getFriendNum();
    }

    @Override
    public float getRecharge_sum() {
        return getOwner().getPaymentComponent().getSaveAmt();
    }

    @Override
    public long getIGuildID() {
        return getOwner().getClanId();
    }

    @Override
    public String getVGuildName() {
        return "";
    }

    @Override
    public String getGuildGrade() {
        return String.valueOf(getOwner().getPlayerClanComponent().getStaffId());
    }

    @Override
    public String getGuildClass() {
        return "";
    }

    @Override
    public int getPlatID() {
        CommonMsg.ClientInfo clientInfo = getOwner().getClientInfo();
        if (clientInfo == null) {
            return -999;
        }
        return clientInfo.getPlatformId();
    }

    @Override
    public String getGame_language() {
        return String.valueOf(getOwner().getClientInfo().getLanguage().getNumber());
    }

    @Override
    public String getVClientIP() {
        return getOwner().getClientIp();
    }

    @Override
    public String getVClientIPV6() {
        return "";
    }

    @Override
    public long getKill_num() {
        return getOwner().getStatisticComponent().getSingleStatistic(StatisticEnum.KILL_PVP_TOTAL);
    }

    @Override
    public String getClientVersion() {
        return getOwner().getClientInfo().getVersion();
    }

    @Override
    public int getMoney_config() {
        return (int) getOwner().getPurseComponent().getCurrencyAmount(CommonEnum.CurrencyType.DIAMOND);
    }

    @Override
    public int getTransferOrNot() {
        return getOwner().isMigratePlayer() ? 1 : 0;
    }

    // 联盟头部
    @Override
    public String getGuildCreate_time() {
        return "";
    }

    @Override
    public String getGuild_id() {
        return String.valueOf(this.getOwner().getProp().getClan().getClanId());
    }

    @Override
    public String getGuild_name() {
        return "";
    }

    @Override
    public String getGuildShortName() {
        return "";
    }

    @Override
    public int getGuild_level() {
        return 0;
    }

    @Override
    public int getGuild_population() {
        return 0;
    }

    @Override
    public int getGuild_online() {
        return 0;
    }

    @Override
    public int getGuildGiftLevel() {
        return 0;
    }

    @Override
    public long getGuild_power() {
        return 0;
    }

    @Override
    public long getGuild_influence() {
        return 0;
    }

    @Override
    public String getGuildLeader_id() {
        return "";
    }

    @Override
    public String getAccountId() {
        return "guild_" + ServerContext.getServerInfo().getWorldId() + "_" + getIZoneAreaID();
    }

    public void sendLoginQlog() {
        try {
            CommonMsg.ClientInfo clientInfo = getOwner().getClientInfo();
            if (ServerContext.isChinaVersion()) {
                QlogChinaPlayerLogin.init(this)
                        .setDtEventTime(TimeUtils.now2String())
                        .setSystemSoftware(clientInfo.getSystemSoftware())
                        .setSystemHardware(clientInfo.getSystemHardware())
                        .setMemory(clientInfo.getMemory())
                        .setGlVersion(clientInfo.getGLVersion())
                        .setDeviceId(clientInfo.getDeviceId())
                        .setAccountName(accountName)
                        .setCountry("")
                        .setAdjustId(clientInfo.getAdjustId())
                        .setLoginChannel(clientInfo.getChannelId())
                        .setXwid("")
                        .setDeviceLevel(clientInfo.getHardwareLevel())
                        .setOaId(clientInfo.getOaid())
                        .setRegChannel(getOwner().getProp().getBasicInfo().getRegChannel())
                        .setMsdkLoginChannel(clientInfo.getMsdkLoginChannel())
                        .setMsdkRegChannel(clientInfo.getMsdkRegChannel())
                        .sendToQlog();
                return;
            }
            QlogPlayerLogin.init(this)
                    .setDtEventTime(TimeUtils.now2String())
                    .setSystemSoftware(clientInfo.getSystemSoftware())
                    .setSystemHardware(clientInfo.getSystemHardware())
                    .setMemory(clientInfo.getMemory())
                    .setGlVersion(clientInfo.getGLVersion())
                    .setDeviceId(clientInfo.getDeviceId())
                    .setAccountName(accountName)
                    .setCountry("")
                    .setAdjustId(clientInfo.getAdjustId())
                    .setLoginChannel(clientInfo.getChannelId())
                    .setXwid("")
                    .setDeviceLevel(clientInfo.getHardwareLevel())
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendLoginQlog error: playerId={}, ", getPlayerId(), e);
        }
    }

    public void sendLogoutQlog(CommonEnum.SessionCloseReason reason) {
        try {
            CommonMsg.ClientInfo clientInfo = getOwner().getClientInfo();
            long onlineMillis = Math.max(0, getOwner().getProp().getBasicInfo().getLastLogoutTsMs() - getOwner().getProp().getBasicInfo().getLastLoginTsMs());
            if (ServerContext.isChinaVersion()) {
                QlogChinaPlayerLogout.init(this)
                        .setDtEventTime(TimeUtils.now2String())
                        .setSystemSoftware(clientInfo.getSystemSoftware())
                        .setSystemHardware(clientInfo.getSystemHardware())
                        .setMemory(clientInfo.getMemory())
                        .setGlVersion(clientInfo.getGLVersion())
                        .setDeviceId(clientInfo.getDeviceId())
                        .setOnlineTime(TimeUnit.MILLISECONDS.toSeconds(onlineMillis))
                        .setReason(reason.toString())
                        .setAccountName(accountName)
                        .setCountry("")
                        .setAdjustId(clientInfo.getAdjustId())
                        .setLoginChannel(clientInfo.getChannelId())
                        .setXwid("")
                        .setRegChannel(getOwner().getProp().getBasicInfo().getRegChannel())
                        .sendToQlog();
                return;
            }
            QlogPlayerLogout.init(this)
                    .setDtEventTime(TimeUtils.now2String())
                    .setSystemSoftware(clientInfo.getSystemSoftware())
                    .setSystemHardware(clientInfo.getSystemHardware())
                    .setMemory(clientInfo.getMemory())
                    .setGlVersion(clientInfo.getGLVersion())
                    .setDeviceId(clientInfo.getDeviceId())
                    .setOnlineTime(TimeUnit.MILLISECONDS.toSeconds(onlineMillis))
                    .setReason(reason.toString())
                    .setAccountName(accountName)
                    .setCountry("")
                    .setAdjustId(clientInfo.getAdjustId())
                    .setLoginChannel(clientInfo.getChannelId())
                    .setXwid("")
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendLogoutQlog error: playerId={}, ", getPlayerId(), e);
        }
    }

    /**
     * 记录玩家零点快照数据
     */
    public void recordZoneSnapshot() {
        playerSnapshotQLog();
    }

    /**
     * 记录玩家快照QLog
     */
    private void playerSnapshotQLog() {
        PlayerBasicInfoProp basicInfo = getOwner().getProp().getBasicInfo();
        long lastLoginTsMs = basicInfo.getLastLoginTsMs();
        long lastLogoutTsMs = basicInfo.getLastLogoutTsMs();
        long now = SystemClock.now();
        if (lastLoginTsMs <= 0) {
            lastLoginTsMs = now;
        }

        if (lastLogoutTsMs <= 0) {
            lastLogoutTsMs = now;
        }
        QlogCncPlayerSnapshot qlogCncPlayerSnapshot = QlogCncPlayerSnapshot.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setHeroLevelStar("")
                .setHeroSkill("")
                .setBuildingConfig("")
                .setMechaInfo("")
                .setRoleLastLoginTime(TimeUtils.timeStampMs2String(lastLoginTsMs))
                .setRoleLastLogoutTime(TimeUtils.timeStampMs2String(lastLogoutTsMs))
                .setBattlePlaneSkill("")// 战斗机已废弃
                .setBattlePlanePower(0)
                .setKillInfo(getOwner().getStatisticComponent().getKillQLogInfo())
                .setBagInfo(getOwner().getItemComponent().getBagQlogInfo())
                .setGuidePosition(getOwner().getNewbieComponent().getStepIndex())
                .setEnergyInfo(getOwner().getEnergyComponent().getEnergy())
                .setMoneyInfo(getOwner().getPurseComponent().getCurrencyAmount(CommonEnum.CurrencyType.DIAMOND))
                .setResInfo(getOwner().getPurseComponent().get4CurrencyQlogInfo())
                .setTechInfo(getOwner().getTechComponent().getQLogTechInfo())
                .setFlagInfo(getOwner().getSettingComponent().getPFlag())
                .setWeekMonthCardInfo(getOwner().getActivityComponent().getWeekMonthCardInfo());

        for (CommonEnum.PowerType powerType : CommonEnum.PowerType.values()) {
            PowerInfoUnitProp powerProp = getOwner().getPowerComponent().getPowerInfoProp().getPowerInfo().get(powerType.getNumber());
            long power = powerProp != null ? powerProp.getPower() : 0;
            switch (powerType) {
                case PT_SOLDIER: {
                    qlogCncPlayerSnapshot.setArmyPower(power);
                    break;
                }
                case PT_INNER_BUILDING: {
                    qlogCncPlayerSnapshot.setBuildingPower(power);
                    break;
                }
                case PT_HERO: {
                    qlogCncPlayerSnapshot.setHeroPower(power);
                    break;
                }
                case PT_TECH: {
                    qlogCncPlayerSnapshot.setTechPower(power);
                    break;
                }
                default:
                    break;
            }
        }
        BpInfo bpInfo = new BpInfo();
        bpInfo.setLevel(getOwner().getBattlePassComponent().getBpLevel());
        bpInfo.setExp(getOwner().getBattlePassComponent().getExp());
        qlogCncPlayerSnapshot.setBpInfo(JsonUtils.toJsonString(bpInfo));
        qlogCncPlayerSnapshot.sendToQlog();

        QlogCncPlayerHeroSnapshot playerHeroSnapshot = QlogCncPlayerHeroSnapshot.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setHeroLevelStar(getOwner().getHeroComponent().getQLogHeroInfo())
                .setHeroSkill(getOwner().getHeroComponent().getQLogHeroSkillInfo())
                .setHeroTalent(getOwner().getHeroComponent().getQLogHeroTalentInfo());
        playerHeroSnapshot.sendToQlog();
    }

    /**
     * 发送加速队列QLog
     */
    public void sendQueueSpeedQLogWithQType(long taskId, long speedTimeSec, QueueSpeedReason reason, CommonEnum.QueueTaskType queueTaskType) {
        QlogCncAccelerateFlow.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAccelerateFormation("")
                .setObjectID(String.valueOf(taskId))
                .setStartTime(TimeUtils.now2String())
                .setEndTimeActual(TimeUtils.now2String())
                .setAction(queueTaskType.name().toLowerCase())
                .setAccelerateType(reason.name().toLowerCase())
                .setGuildHelpOrNot(0)
                .setAccelerateTime(speedTimeSec)
                .sendToQlog();
    }

    /**
     * 发送加速QLog
     */
    public void sendSpeedQLogWithTask(long taskId, long speedTimeSec, QueueSpeedReason reason, QueueTaskProp taskProp) {
        QlogCncAccelerateFlow.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAccelerateFormation(String.valueOf(taskProp.getId()))
                .setObjectID(String.valueOf(taskId))
                .setStartTime(TimeUtils.timeStampMs2String(taskProp.getStarTime()))
                .setEndTimeActual(TimeUtils.timeStampMs2String(taskProp.getEndTime()))
                .setAction(taskProp.getType().name().toLowerCase())
                .setAccelerateType(reason.name().toLowerCase())
                .setGuildHelpOrNot(0)
                .setAccelerateTime(speedTimeSec)
                .sendToQlog();
    }

    /**
     * 发送触发式引导QLog
     */
    public void sendGuidanceRecordQLog(int guidanceId, int step) {
        QlogCncGuidanceRecordFlow.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setJobID(guidanceId)
                .setStep(step)
                .setState(step == 0 ? 0 : 1)
                .sendToQlog();
    }

    /**
     * 获取整合后的占领与建设积分，实际用在qlog里的军团积分类型
     * 如果后续要扩展，建议不再依赖这种方式获取积分类型，而是直接从链路顶层向下传递积分类型
     *
     * @param isOccupy 是否是占领
     * @return 返回占领或建设的积分类型
     */
    public CommonEnum.ClanScoreCategory getClanScoreTypeWhenOccupyOrBuild(boolean isOccupy) {
        if (isOccupy) {
            return CommonEnum.ClanScoreCategory.CSC_OCCUPY;
        } else {
            return CommonEnum.ClanScoreCategory.CSC_BUILD;
        }
    }

    /**
     * 将联盟分数类型转换为记录所需的动作字符串
     */
    private GuildPersonalScoreActionType transClanScoreTypeToActionStr(CommonEnum.ClanScoreCategory scoreType) {
        switch (scoreType) {
            case CSC_HELP:
                return GuildPersonalScoreActionType.GUILD_HELP;
            case CSC_BUILD:
                return GuildPersonalScoreActionType.PARTICIPATE_BUILD;
            case CSC_OCCUPY:
                return GuildPersonalScoreActionType.OCCUPY;
            case CSC_TECH_DONATE:
                return GuildPersonalScoreActionType.TECH_DONATE;
            case CSC_SHOP:
                return GuildPersonalScoreActionType.GUILD_SHOP;
            case CSC_RECYCLE:
                return GuildPersonalScoreActionType.RECYCLE;
            case CSC_GIFT:
                return GuildPersonalScoreActionType.GUILD_GIFT;
            case CSC_ITEM:
                return GuildPersonalScoreActionType.USE_ITEM;
            case CSC_GM:
                return GuildPersonalScoreActionType.GM;
            default:
                return null;
        }
    }

    /**
     * 发送联盟个人积分流水Qlog
     */
    public void sendGuildPersonalScoreQLog(CommonEnum.ClanScoreCategory scoreType, long beforeScore, long afterScore, long iCount, int addOrReduce) {
        if (scoreType == null) {
            LOGGER.error("null scoreType have been sent");
            return;
        }
        GuildPersonalScoreActionType actionType = transClanScoreTypeToActionStr(scoreType);
        if (actionType == null) {
            LOGGER.error("wrong scoreType {} have been sent", scoreType);
            return;
        }
        QlogCncGuildPersonalScore.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction(actionType.name().toLowerCase())
                .setBeforeCount(beforeScore)
                .setAfterCount(afterScore)
                .setICount(iCount)
                .setAddOrReduce(addOrReduce)
                .sendToQlog();
    }

    /**
     * 资源流水
     */
    public void sendResQLog(int resId, long oldCount, long newCount, long changeCount, boolean isAdd, CommonEnum.Reason reason, String subReason) {
        QlogCncRes.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setResId(resId)
                .setBeforeCount(oldCount)
                .setAfterCount(newCount)
                .setICount(changeCount)
                .setAddOrReduce(isAdd ? 0 : 1)
                .setReason(reason.toString())
                .setSubReason(subReason)
                .sendToQlog();
    }

    /**
     * 资源流水
     */
    public void sendMoneyQLog(int iMoneyType, long oldCount, long newCount, long changeCount, boolean isAdd, CommonEnum.Reason reason, String subReason) {
        QlogMoneyFlow.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setIMoneyType(iMoneyType)
                .setBeforeMoney(oldCount)
                .setAfterMoney(newCount)
                .setIMoney(changeCount)
                .setAddOrReduce(isAdd ? 0 : 1)
                .setReason(reason.toString())
                .setSubReason(subReason)
                .sendToQlog();
    }

    public void sendEnergyQLog(int changeNum, String energyCostReason, int beforeEnergy, int templateId, int addOrReduce) {
        QlogCncEnergyFlow.init(getOwner().getQlogComponent())
                .setAction(energyCostReason)
                .setBeforeCount(beforeEnergy)
                .setAfterCount(getOwner().getEnergyComponent().getEnergy())
                .setICount(changeNum)
                .setDtEventTime(TimeUtils.now2String())
                .setAddOrReduce(addOrReduce) // 0:增加 1:减少
                .setParamEnergy(String.valueOf(templateId))
                .sendToQlog();
    }

    public void sendNewbieQlog(int stepIndex) {
        try {
            QlogCncGuideFlow.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setIGuideID(String.valueOf(stepIndex))
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendNewbieQlog error:", e);
        }
    }

    /**
     * 发送商店流水（包括商店购买和刷新行为）
     *
     * @param shopType    商店类型
     *                    "arms_dealer" 军需处
     *                    "black_market" 黑市商店
     *                    "expedition_shop" 远征商店
     *                    "vip_shop" vip商店
     * @param action      操作类型
     *                    "shop_purchase" 购买
     *                    "shop_refresh" 手动付费刷新
     *                    "shop_free_refresh" 手动免费刷新
     * @param moneyUseStr 货币消耗字符串
     * @param goodsGetStr 物品获得字符串
     */

    public void sendShopQLog(String shopType, String action, String moneyUseStr, String goodsGetStr) {
        try {
            QlogCncShop.init(this)
                    .setDtEventTime(TimeUtils.now2String())
                    .setShopType(shopType)
                    .setAction(action)
                    .setIMoneyUse(moneyUseStr)
                    .setIGoodsGet(goodsGetStr)
                    .sendToQlog();
        } catch (Exception e) {
            WechatLog.error("sendShopQlog error:", e);
        }
    }

    public void sendExpeditionQLog(String action, int chapterId, int levelId, int beforeStar, int timeCost, int result) {
        QlogCncExpedition.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setChapterId(chapterId)
                .setLevelId(levelId)
                .setBeforeStar(beforeStar)
                .setTimeCost(timeCost)
                .setResult(result)
                .sendToQlog();
    }

    public void sendFriendQLog(String action, long playerId) {
        QlogCncFriends.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setFriendId(playerId)
                .sendToQlog();
    }

    /**
     * 发送vip经验变动流水
     *
     * @param action        动作
     *                      "use_item_get_vip_exp" 使用道具获取vip经验
     *                      "vip_reward_get_vip_exp" vip特权宝箱获得点数
     * @param expCount      变动的经验值
     * @param afterVipLevel 经验值变动后的vip等级
     */
    public void sendVipExpQLog(String action, int expCount, int afterVipLevel) {
        QlogCncVipExp.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setExpCount(expCount)
                .setAfterVipLevel(afterVipLevel)
                .sendToQlog();
    }


    /**
     * 发送邮件流水
     *
     * @param mailActionType    邮件行为
     * @param mail              邮件
     * @param mailTemplate      邮件模板
     * @param rewards           邮件奖励
     * @param receiveMailObject 收件方
     * @param sendMailObject    发件方
     */
    public void sendMailQLog(final MailActionType mailActionType, final MailMinInfo mail, final MailTemplate mailTemplate, final List<IntPairType> rewards, final String receiveMailObject, final String sendMailObject) {
        QlogCncMailFlow.init(this)
                .setAction(mailActionType.getType())
                .setDtEventTime(TimeUtils.now2String())
                .setMailTabsType(MailTabType.getTypeByMailTabsType(mailTemplate.getMailTabsType()))
                .setMailId(String.valueOf(mail.getMailId()))
                .setMailTemplateId(mailTemplate.getId())
                .setRewardConfig(QlogUtils.transReward2String(rewards))
                .setReceiveMailObject(receiveMailObject)
                .setSendMailObject(sendMailObject)
                .sendToQlog();
    }

    /**
     * 发送基地皮肤流水
     *
     * @param action         行为
     * @param skinTemplateId 皮肤ID
     * @param skinType       皮肤类型， 1：基地皮肤 2：铭牌皮肤
     * @param timeMs         行为发生时间
     */
    public void sendCitySkinQLog(final CitySkinAction action, int skinTemplateId, final CommonEnum.DressType skinType, long timeMs) {
        QlogCncBaseSkin.init(this)
                .setAction(action.getType())
                .setSkinId(skinTemplateId)
                .setSkinType(skinType.getNumber())
                .setDtEventTime(TimeUtils.timeStampMs2String(timeMs));
    }

    /**
     * 发送领取军团礼物流水
     *
     * @param giftId 礼物id
     */
    public void sendTakeClanGiftQLog(long giftId) {
        QlogCncGuildGift.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(ClanGiftAction.COLLECT_REWARD.getType())
                .setRoleId(String.valueOf(this.getPlayerId()))
                .setGiftType(ClanGiftType.NORMAL_GIFT.getType())
                .setTargetId(String.valueOf(giftId))
                .sendToQlog();
    }

    /**
     * 发送领取珍藏军团礼物流水
     *
     * @param treasureGiftLevel 珍藏礼物等级
     */
    public void sendTakeTreasureGiftQlog(int treasureGiftLevel) {
        QlogCncGuildGift.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(ClanGiftAction.COLLECT_REWARD.getType())
                .setRoleId(String.valueOf(this.getPlayerId()))
                .setGiftType(ClanGiftType.TREASURE_GIFT.getType())
                .setTargetId(String.valueOf(treasureGiftLevel))
                .sendToQlog();
    }

    /**
     * 发送领取军团礼物流水
     *
     * @param giftId 礼物id
     */
    public void sendExpireClanGiftQLog(long giftId) {
        QlogCncGuildGift.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(ClanGiftAction.REWARD_EXPIRE.getType())
                .setRoleId(String.valueOf(this.getPlayerId()))
                .setGiftType(ClanGiftType.NORMAL_GIFT.getType())
                .setTargetId(String.valueOf(giftId))
                .sendToQlog();
    }

    public void sendActivityScoreQlog(String action, String subjectId, int iCount, int score, String scoreId) {
        try {
            QlogCncActivityScore.init(getOwner().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction(action)
                    .setSubjectID(subjectId)
                    .setIcount(iCount)
                    .setActivityScore(score)
                    .setActivityScoreID(scoreId)
                    .sendToQlog();
        } catch (Exception e) {
            WechatLog.error("sendActivityScoreQlog error: ", e);
        }
    }

    /**
     * 发送导量流水
     *
     * @param roleSeqOfAccount 第n个角色
     * @param driveTrafficType 导量策略
     * @param hardwareLevel    机型档位（1-4）
     * @param continentId      出生大洲id
     * @param bornPoint        出生坐标
     */
    public void sendDriveTrafficQlog(final int roleSeqOfAccount, final int driveTrafficType, final int hardwareLevel, final int continentId, final Struct.Point bornPoint) {
        CommonEnum.DriveTrafficType driveTrafficReason = CommonEnum.DriveTrafficType.forNumber(driveTrafficType);

        if (driveTrafficReason == null || driveTrafficType == 0) {
            // 枚举中不含的数值(包含0)，采用服务器满策略
            driveTrafficReason = CommonEnum.DriveTrafficType.DTAT_SERVER_FULLFILLED;
            LOGGER.info("sendDriveTrafficQlog unknown driveTrafficType={}, use default driveTrafficReason={}", driveTrafficType, driveTrafficReason.toString());
        }
        if (roleSeqOfAccount == 1) {
            LOGGER.info("zone_drive_success_total hardwareLevel={}, driveTrafficReason={}", hardwareLevel, driveTrafficReason);
        }
        QlogCncUserDistribution.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setUserFeature(QlogUtils.hardwareLevelToJsonString(hardwareLevel))
                .setDistributionStrategy(QlogUtils.driveTrafficTypeToString(driveTrafficReason))
                .setContinentlD(continentId)
                .setCoordinates(StringUtils.format("[{}, {}]", bornPoint.getX(), bornPoint.getY()))
                .sendToQlog();

    }

    public void sendZoneMigrateQLog(String action, String type, int targetZone) {
        QlogCncImmigrant.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setType(type)
                .setTargetZone(targetZone)
                .sendToQlog();
    }
}

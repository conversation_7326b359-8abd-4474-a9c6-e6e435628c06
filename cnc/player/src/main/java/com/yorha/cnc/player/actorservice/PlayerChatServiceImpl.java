package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.PlayerChatService;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.MigrateHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.notification.NotificationBuilder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.game.gen.prop.ChatItemProp;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.game.gen.prop.Int64ShieldPlayerInfoMapProp;
import com.yorha.proto.CommonEnum.ChatChannel;
import com.yorha.proto.CommonEnum.MessageType;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CommonMsg.ChatMember;
import com.yorha.proto.PlayerChat.Player_BoradcastChatMessage_NTF;
import com.yorha.proto.SsGroupChat.JudgeMemberAns;
import com.yorha.proto.SsGroupChat.JudgeMemberAsk;
import com.yorha.proto.SsPlayerChat.*;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstChatTemplate;

import java.util.Map;

public class PlayerChatServiceImpl implements PlayerChatService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerChatServiceImpl.class);

    private static ChannelInfoProp getChannelInfoProp(final ChatPlayerProp prop, final ChatChannel channel) {
        ChannelInfoProp channelInfoProp = prop.getChannelInfo().get(channel.getNumber());
        if (channelInfoProp == null) {
            channelInfoProp = prop.addEmptyChannelInfo(channel.getNumber());
        }
        return channelInfoProp;
    }

    private final PlayerActor playerActor;

    public PlayerChatServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    /**
     * 加入群聊两阶段的相关协议，属于准备阶段，创建ChatItem，并标记为准备状态。
     * 注意：处于移民状态会抛出MigrateException，群聊Actor将会处理。
     * 此接口幂等。
     *
     * @param ask ChatGroup的请求。
     */
    @Override
    public void handlePrepareJoinGroupChatAsk(PrepareJoinGroupChatAsk ask) {
        final ChatPlayerProp prop = this.playerActor.getOrLoadChatPlayerEntity().getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_GROUP);
        final PrepareJoinGroupChatAns.Builder ans = PrepareJoinGroupChatAns.newBuilder();

        // 当前已经进入commit状态，拒绝进入prepare状态
        if (channelInfoProp.getItem().containsKey(ask.getChannelId())) {
            LOGGER.error("Join2PC, prepare, handlePrepareJoinGroupChatAsk, chat={} already into commit, chatItem={}",
                    ask.getChannelId(), channelInfoProp.getItemV(ask.getChannelId()));
            this.playerActor.answer(ans.build());
            return;
        }

        // 当前已经进入prepare状态，回复成功
        if (channelInfoProp.getPrepareItem().containsKey(ask.getChannelId())) {
            ans.setPrepare(true);
            this.playerActor.answer(ans.build());
            return;
        }

        // 检查群聊数量是否达到上限
        final int groupChatLimit = ResHolder.getConsts(ConstChatTemplate.class).getGroupChatMax();
        if ((channelInfoProp.getItemSize() + channelInfoProp.getPrepareItemSize()) >= groupChatLimit) {
            LOGGER.info("Join2PC, prepare, handlePrepareJoinGroupChatAsk, try create chat={}, but up to group chat limit, chatItem num={}, prepareChatItem num={}",
                    ask.getChannelId(), channelInfoProp.getItemSize(), channelInfoProp.getPrepareItemSize());
            ans.setPrepare(false);
            this.playerActor.answer(ans.build());
            return;
        }

        // 进入准备状态
        LOGGER.info("Join2PC, prepare, handlePrepareJoinGroupChatAsk, chat={} enter prepare", ask.getChannelId());
        channelInfoProp.addEmptyPrepareItem(ask.getChannelId()).setCreateTsMs(SystemClock.nowNative());

        // 回包
        ans.setPrepare(true);
        this.playerActor.answer(ans.build());
    }

    /**
     * 加入群聊两阶段的相关协议，属于提交阶段，出现执行问题的回滚操作，回滚。
     * 此接口幂等。
     *
     * @param ask 请求。
     */
    @Override
    public void handleClearPrepareGroupChatCmd(ClearPrepareGroupChatCmd ask) {
        try {
            final ChatPlayerEntity chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
            final ChatPlayerProp prop = chatPlayerEntity.getProp();
            final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_GROUP);
            channelInfoProp.getPrepareItem().remove(ask.getChannelId());
            if (channelInfoProp.getItem().containsKey(ask.getChannelId())) {
                LOGGER.error("Join2PC, commit, handleClearPrepareGroupChatCmd, chat={} already commit", ask.getChannelId());
            }
            LOGGER.info("Join2PC, commit, handleClearPrepareGroupChatCmd, rollback chat={}", ask.getChannelId());
        } catch (MigrateException e) {
            // 移民了，需要转发请求
            LOGGER.info("Join2PC, commit, handleClearPrepareGroupChatCmd, chat={} player={} in zone={}, migrate, transfer to curZone={}",
                    ask.getChannelId(), playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            final ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
            final IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
            MigrateHelper.transferMsg(envelope, targetPlayer);
        }
    }

    /**
     * 加入群聊两阶段的相关协议，属于提交阶段，加入群聊成功，提交prepare的数据到ChatItem。
     * 此接口幂等。
     *
     * @param ask 请求。
     */
    @Override
    public void handleJoinGroupChatCmd(JoinGroupChatCmd ask) {
        final ChatPlayerEntity chatPlayerEntity;
        try {
            chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        } catch (MigrateException e) {
            // 移民转发
            LOGGER.info("Join2PC, commit, handleJoinGroupChatCmd, chat={} player={} in zone={}, migrate, transfer to curZone={}",
                    ask.getChannelId(), playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            final ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
            final IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
            MigrateHelper.transferMsg(envelope, targetPlayer);
            return;
        }

        // 检查是否已经处于提交状态
        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_GROUP);
        if (channelInfoProp.getItem().containsKey(ask.getChannelId())) {
            LOGGER.info("Join2PC, commit, handleJoinGroupChatCmd, chat={} already commit", ask.getChannelId());
            return;
        }

        // 删除prepare
        final boolean isDeletePrepare = channelInfoProp.getPrepareItem().remove(ask.getChannelId()) != null;

        // 检查群聊数量是否达到上限
        final int groupChatLimit = ResHolder.getConsts(ConstChatTemplate.class).getGroupChatMax();
        if (!isDeletePrepare && ((channelInfoProp.getItemSize() + channelInfoProp.getPrepareItemSize()) >= groupChatLimit)) {
            LOGGER.error("Join2PC, commit, handleJoinGroupChatCmd, chat={} up to limit, chatItem num={}, prepareChatItem num={}",
                    ask.getChannelId(), channelInfoProp.getItemSize(), channelInfoProp.getPrepareItemSize());
            return;
        }

        // 新增聊天
        channelInfoProp.addEmptyItem(ask.getChannelId())
                .setOwner(ask.getOwner())
                .setMaxIndex(ask.getMaxIndex())
                .setReadIndex(ask.getMaxIndex())
                .setVersion(1)
                .setGroupName(ask.getGroupName());

        LOGGER.info("Join2PC, commit, handleJoinGroupChatCmd, commit chat={} isDeletePrepare={} into commit",
                ask.getChannelId(), isDeletePrepare);
    }

    /**
     * 加入群聊二阶段，当玩家身上的prepare超时的时候，需要确定是否需要提交prepare。
     * 注意：此消息的触发者是player自己，此函数幂等。
     *
     * @param ask 请求。
     */
    @Override
    public void handleTryClearPrepareChatNtf(TryClearPrepareChatNtf ask) {
        final ChatPlayerEntity chatPlayerEntity;
        try {
            chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        } catch (MigrateException e) {
            LOGGER.info("Join2PC, commit, handleTryClearPrepareChatNtf, find player migrate, playerId={}, old zone={}, curZone={}",
                    playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            return;
        }

        // 准备数据
        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_GROUP);

        if (channelInfoProp.getItem().containsKey(ask.getChannelId())) {
            LOGGER.info("Join2PC, commit, handleTryClearPrepareChatNtf, already commit, channelId={}", ask.getChannelId());
            return;
        }

        final TcaplusDb.ChatDescriptionTable.Builder req = TcaplusDb.ChatDescriptionTable.newBuilder()
                .setChannelType(ChatChannel.CC_GROUP_VALUE)
                .setChannelId(ask.getChannelId());

        try {
            // 发送请求
            final GetOption getOption = GetOption.newBuilder().withGetAllFields(true).build();
            final SelectUniqueAsk<TcaplusDb.ChatDescriptionTable.Builder> selectUniqueAsk = new SelectUniqueAsk<>(req, getOption);
            final GetResult<TcaplusDb.ChatDescriptionTable.Builder> ans = playerActor.callGameDb(selectUniqueAsk);

            if (ans.isRecordNotExist()) {
                LOGGER.info("Join2PC, commit, handleTryClearPrepareChatNtf, chat={} not commit, clear prepareItem", ask.getChannelId());
                // 群聊不存在，则清除
                channelInfoProp.removePrepareItemV(ask.getChannelId());
                return;
            }
            if (!ans.isOk()) {
                LOGGER.error("Join2PC, prepare, handleTryClearPrepareChatNtf, get chat={} fail, code={}", ask.getChannelId(), ans.getCode());
                return;
            }

            // 判断群成员有没有这个人
            boolean exist = false;
            for (final ChatMember chatMember : ans.value.getDescription().getMembers().getMembersList()) {
                if (chatMember.getPlayerId() == playerActor.getPlayerId()) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                LOGGER.info("Join2PC, commit, handleTryClearPrepareChatNtf, chat={} has no member={}, clear prepareItem",
                        ask.getChannelId(), playerActor.getPlayerId());
                // 群聊不存在，则清除
                channelInfoProp.removePrepareItemV(ask.getChannelId());
                return;
            }

            // 存在则加入群聊
            channelInfoProp.addEmptyItem(ask.getChannelId())
                    .setMaxIndex(ans.value.getMaxMsgIndex())
                    .setGroupName(ans.value.getDescription().getChatName())
                    .setOwner(ans.value.getDescription().getChatOwner());
            LOGGER.info("Join2PC, commit, handleTryClearPrepareChatNtf, chat={} commit", ask.getChannelId());
        } catch (Exception e) {
            LOGGER.error("Join2PC, prepare, handleTryClearPrepareChatNtf, get chat={} fail, e=", ask.getChannelId(), e);
        }
    }

    /**
     * 离开群聊。
     *
     * @param ask 请求。
     */
    @Override
    public void handleLeaveGroupChatCmd(LeaveGroupChatCmd ask) {
        final ChatPlayerEntity chatPlayerEntity;
        try {
            chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        } catch (MigrateException e) {
            LOGGER.info("PlayerChatServiceImpl handleLeaveGroupChatCmd, chat={} player={} in zone={} migrate, transfer to curZone={}",
                    ask.getChannelId(), playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            final ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
            final IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
            MigrateHelper.transferMsg(envelope, targetPlayer);
            return;
        }
        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_GROUP);
        final ChatItemProp chatItemProp = channelInfoProp.getItem().remove(ask.getChannelId());
        if (chatItemProp != null) {
            return;
        }
        LOGGER.info("PlayerChatServiceImpl handleLeaveGroupChatCmd, player={} not in chat={}", playerActor.getPlayerId(), ask.getChannelId());
    }

    @Override
    public void handleHandleNewMessageAsk(HandleNewMessageAsk ask) {
        handleNewMsg(this.playerActor, ask.getNewMessage(), ask.getChatSession().getChannelType(), ask.getChatSession().getChatChannelId(), 0);
        playerActor.answer(HandleNewMessageAns.getDefaultInstance());
    }


    @Override
    public void handleGroupInfoChangeNtf(GroupInfoChangeNtf ask) {
        ChatPlayerEntity chatPlayerEntity;
        try {
            chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        } catch (MigrateException e) {
            LOGGER.info("PlayerChatServiceImpl handleGroupInfoChangeNtf, chat={} player={} in zone={} migrate, transfer to zone={}",
                    ask.getGroupChatId(), playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            final ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
            final IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
            MigrateHelper.transferMsg(envelope, targetPlayer);
            return;
        }

        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_GROUP);
        final ChatItemProp chatItemProp = channelInfoProp.getItem().get(ask.getGroupChatId());
        if (chatItemProp == null) {
            return;
        }

        // ntf消息可能错序，确保version不会回退
        if (ask.hasVersion() && ask.getVersion() > chatItemProp.getVersion()) {
            chatItemProp.setVersion(ask.getVersion());
        }
        if (ask.hasOwner()) {
            chatItemProp.setOwner(ask.getOwner());
        }
        if (ask.hasName()) {
            chatItemProp.setGroupName(ask.getName());
        }
    }

    @Override
    public void handleGroupDismissNtf(GroupDismissNtf ntf) {
        final ChatPlayerEntity chatPlayerEntity;
        try {
            chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        } catch (MigrateException e) {
            LOGGER.info("PlayerChatServiceImpl handleGroupDismissNtf, chat={} player={} in zone={} migrate, transfer to zone={}",
                    ntf.getGroupChatId(), playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            final ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
            final IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
            MigrateHelper.transferMsg(envelope, targetPlayer);
            return;
        }
        ChatPlayerProp prop = chatPlayerEntity.getProp();
        ChannelInfoProp channelInfoProp = prop.getChannelInfo().get(ChatChannel.CC_GROUP_VALUE);
        if (channelInfoProp == null) {
            return;
        }
        channelInfoProp.removeItemV(ntf.getGroupChatId());
    }

    @Override
    public void handleIgnoreMsgNtf(IgnoreMsgNtf ask) {
        final ChatPlayerEntity chatPlayerEntity;
        try {
            chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        } catch (MigrateException e) {
            LOGGER.info("PlayerChatServiceImpl handleIgnoreMsgNtf, chat={} player={} in zone={} migrate, transfer to zone={}",
                    ask.getChatSession(), playerActor.getPlayerId(), playerActor.getZoneId(), e.getZoneId());
            final ActorMsgEnvelope envelope = this.playerActor.getCurrentEnvelope();
            final IActorRef targetPlayer = RefFactory.ofPlayer(e.getZoneId(), playerActor.getPlayerId());
            MigrateHelper.transferMsg(envelope, targetPlayer);
            return;
        }
        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ask.getChatSession().getChannelType());
        final ChatItemProp chatItemProp = channelInfoProp.getItem().get(ask.getChatSession().getChatChannelId());
        if (chatItemProp == null) {
            return;
        }

        chatItemProp.setIgnoreCount(chatItemProp.getIgnoreCount() + 1);
    }


    @Override
    public void handleReceivePrivateMsgAsk(ReceivePrivateMsgAsk ask) {
        final FriendPlayerEntity friendPlayerEntity = this.playerActor.getOrLoadFriendPlayerEntity();
        final ChatPlayerEntity chatPlayerEntity = this.playerActor.getOrLoadChatPlayerEntity();
        if (friendPlayerEntity.hasShieldPlayer(ask.getChatMessage().getChatMessageSender().getSenderId())) {
            throw new GeminiException(ErrorCode.CHAT_PLAYER_SHIELD_YOU);
        }

        // 新建群聊描述
        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, ChatChannel.CC_PRIVATE);
        if (channelInfoProp.getItemV(ask.getChannelId()) == null) {
            ChatHelper.insertChatDescription(playerActor, ChatChannel.CC_PRIVATE_VALUE, ask.getChannelId());
        }

        // 插入消息
        final ActorMsgEnvelope envelope = playerActor.getCurrentEnvelope();
        final Pair<CommonMsg.ChatMessage, Long> pair = ChatHelper.onChatMsgRequest(playerActor, ask.getChannelId(), ChatChannel.CC_PRIVATE_VALUE, ask.getChatMessage());

        // 给自己发
        handleNewMsg(this.playerActor, pair.getFirst(), ChatChannel.CC_PRIVATE, ask.getChannelId(), pair.getSecond());

        // 回消息
        playerActor.answerWithContext(envelope, ReceivePrivateMsgAns.newBuilder()
                .setChatMessage(pair.getFirst()).setChatChannelCreateTsMs(pair.getSecond()).build());
    }

    @Override
    public void handleGroupChatExpireCmd(GroupChatExpireCmd ask) {
        throw new NotImplementedException("");
    }

    /**
     * 接收到消息，当接收不存在的频道信息的时候，会尝试再玩家身上创建。
     *
     * @param playerActor           玩家Actor。
     * @param chatMessage           消息。
     * @param channel               频道。
     * @param channelId             频道 id。
     * @param chatChannelCreateTsMs 频道创建时间戳，为0代表不需要刷新ChatItem，否则当玩家身上记录的chatItem创建时间不同时，
     */
    public static void handleNewMsg(final PlayerActor playerActor, final CommonMsg.ChatMessage chatMessage, final ChatChannel channel, final String channelId, final long chatChannelCreateTsMs) {
        final ChatPlayerEntity chatPlayerEntity = playerActor.getOrLoadChatPlayerEntity();
        final long sender = chatMessage.getChatMessageSender().getSenderId();
        final long maxIndex = chatMessage.getMessageId();
        final ChatPlayerProp prop = chatPlayerEntity.getProp();
        final ChannelInfoProp channelInfoProp = getChannelInfoProp(prop, channel);

        ChatItemProp chatItemProp = channelInfoProp.getItem().get(channelId);
        var channelCreateTimeChanged = chatItemProp != null && (chatChannelCreateTsMs != 0 && chatChannelCreateTsMs != chatItemProp.getCreateTsMs());
        if (channelCreateTimeChanged) {
            // 时间戳变了，可能是本地过期，后来被替代。chat的版本变了。类似私聊，淘汰后又创建了，需要刷到最新
            LOGGER.info("channel={} expire and replace, elderCreateTsMs={}, newCreateTsMs={}, elderMaxIndex={}, newMaxIndex={}",
                    channelId, chatItemProp.getCreateTsMs(), chatChannelCreateTsMs, chatItemProp.getMaxIndex(), maxIndex);
            channelInfoProp.removeItemV(channelId);
            chatItemProp = null;
        }

        // 聊天不存在，尝试创建聊天
        if (chatItemProp == null) {
            chatItemProp = checkAndAddChatItem(chatPlayerEntity, channel, channelId, maxIndex, chatChannelCreateTsMs);
        }

        // 聊天无法创建，跳过
        if (chatItemProp == null) {
            return;
        }

        // @消息修改firstAtIndex，私聊没有@
        if (chatMessage.getType() == MessageType.MT_AT) {
            // 只保留第一个未读的
            if (chatItemProp.getReadIndex() >= chatItemProp.getFirstAtIndex()) {
                chatItemProp.setFirstAtIndex(maxIndex);
            }
        }

        // 全服聊天和军团聊天通知在线客户端会走其它逻辑，这是只是用来处理全服和军团的@消息
        if (channel == ChatChannel.CC_SERVER || channel == ChatChannel.CC_CLAN) {
            return;
        }

        // 因为消息接受可能是错序的，很可能后一条消息比前一条消息先收到
        if (chatItemProp.getMaxIndex() < maxIndex) {
            chatItemProp.setMaxIndex(maxIndex);
        }

        chatItemProp.setLastChatTsMs(SystemClock.now());

        // 如果屏蔽了当前玩家，忽略此条消息，不通知客户端
        final Int64ShieldPlayerInfoMapProp shiledList = playerActor.getOrLoadFriendPlayerEntity().getProp().getShiledList();
        if (shiledList.containsKey(sender)) {
            chatItemProp.setIgnoreCount(chatItemProp.getIgnoreCount() + 1);
            return;
        }

        // 隐藏的群聊收到消息后要取消隐藏状态，但要在判断屏蔽之后
        chatItemProp.setHide(false);

        // 如果客户端在线，通知客户端
        final PlayerEntity playerEntity = chatPlayerEntity.ownerActor().getEntityOrNull();
        if (playerEntity == null || !playerEntity.isOnline()) {
            if (channel == ChatChannel.CC_PRIVATE) {
                playerActor.notifyPlayer(NotificationBuilder.NEW_PRIVATE_MESSAGE);
            }
            return;
        }

        // 主动下发一次属性同步，防止chatItem的同步在消息下发之后
        chatPlayerEntity.getPropComponent().immediateFlushProp();

        final Player_BoradcastChatMessage_NTF.Builder broadcast = Player_BoradcastChatMessage_NTF.newBuilder();
        broadcast.setChatMessage(chatMessage).getChatSessionBuilder()
                .setChatChannelId(channelId).setChannelType(channel);
        playerEntity.sendMsgToClient(MsgType.PLAYER_BORADCASTCHATMESSAGE_NTF, ChatHelper.formBroadCastChatNtf(chatMessage, channelId, channel));
    }

    private static ChatItemProp checkAndAddChatItem(
            ChatPlayerEntity entity, ChatChannel chatChannel, final String channelId,
            final long maxIndex, final long chatChannelCreateTsMs) {

        final ChannelInfoProp channelInfoProp = getChannelInfoProp(entity.getProp(), chatChannel);

        if (chatChannel == ChatChannel.CC_GROUP) {
            // 检查是否爆破了
            if (channelInfoProp.getItemSize() >= ResHolder.getInstance().getConstTemplate(ConstChatTemplate.class).getGroupChatMax()) {
                LOGGER.info("PlayerChatServiceImpl checkAndAddChatItem, player={} in groupChat={}, chatItem too much",
                        entity.ownerActor().getPlayerId(), channelId);
                return null;
            }
            final JudgeMemberAns ans;
            try {
                // 检查是否再群里面
                final JudgeMemberAsk ask = JudgeMemberAsk.newBuilder().setPlayerId(entity.ownerActor().getPlayerId()).build();
                ans = entity.ownerActor().callSharedChat(chatChannel.getNumber(), channelId, ask);
                if (!ans.getIsIn()) {
                    LOGGER.error("PlayerChatServiceImpl checkAndAddChatItem, player={} not in groupChat={}, but receive msg",
                            entity.ownerActor().getPlayerId(), channelId);
                    return null;
                }
            } catch (GeminiException e) {
                if (e.getCodeId() == ErrorCode.CHAT_ILLEGAL_ITEM_NEED_DEL.getCodeId()) {
                    LOGGER.error("PlayerChatServiceImpl checkAndAddChatItem, player={} not in groupChat={}, but receive msg, e=",
                            entity.ownerActor().getPlayerId(), channelId, e);
                    return null;
                }
                throw e;
            }

            // 可能是两阶段提交的第二阶段协议丢了，导致prepareItem没清
            final boolean isPrepareRemove = channelInfoProp.getPrepareItem().remove(channelId) != null;
            LOGGER.info("PlayerChatServiceImpl checkAndAddChatItem, player={} in groupChat={}, and create chatItem, isPrepareRemove={}",
                    entity.ownerActor().getPlayerId(), channelId, isPrepareRemove);
            channelInfoProp.addEmptyItem(channelId)
                    .setReadIndex(maxIndex - 1)
                    .setStartIndex(maxIndex - 1)
                    .setCreateTsMs(chatChannelCreateTsMs)
                    .setOwner(ans.getOwner())
                    .setVersion(ans.getVersion())
                    .setGroupName(ans.getChatName());
        }

        // 检查聊天上限，私聊超出上限需要淘汰一个最不活跃的聊天item再添加新的聊天item
        if (chatChannel == ChatChannel.CC_PRIVATE) {
            final int privateChatLimit = ResHolder.getInstance().getConstTemplate(ConstChatTemplate.class).getPrivateChatMax();
            if (channelInfoProp.getItemSize() >= privateChatLimit) {
                long earlyTsMs = Long.MAX_VALUE;
                String earlyChannel = null;
                for (final Map.Entry<String, ChatItemProp> kv : channelInfoProp.getItem().entrySet()) {
                    if (kv.getValue().getLastChatTsMs() < earlyTsMs) {
                        earlyChannel = kv.getKey();
                        earlyTsMs = kv.getValue().getLastChatTsMs();
                    }
                }
                channelInfoProp.removeItemV(earlyChannel);
                LOGGER.info("PlayerChatServiceImpl checkAndAddChatItem, player={} reach privateChat item limit, remove chatItem {}, add chat={}",
                        chatChannel, earlyChannel, channelId);
            }
            // 对于新添加的chatItem, readIndex和startIndex设置为maxIndex-1，表示只有一条消息可读和可拉取
            channelInfoProp.addEmptyItem(channelId)
                    .setReadIndex(maxIndex - 1)
                    .setStartIndex(maxIndex - 1)
                    .setCreateTsMs(chatChannelCreateTsMs);
        }

        // 新添群聊的chatItem要设置群聊相关信息
        return channelInfoProp.getItemV(channelId);
    }
}

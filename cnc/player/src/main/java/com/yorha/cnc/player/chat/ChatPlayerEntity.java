package com.yorha.cnc.player.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.chat.component.*;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.Player;

import java.util.Collection;
import java.util.function.Consumer;

/**
 * 聊天Entity
 *
 * <AUTHOR>
 */
public class ChatPlayerEntity extends AbstractEntity {

    private final PlayerActor actor;
    private final ChatPlayerProp prop;
    private boolean isStartZoneMigrate;

    public ChatPlayerEntity(PlayerActor playerActor, ChatPlayerProp prop, Player.ChatPlayer changed) {
        super(playerActor.getPlayerId());
        this.prop = prop;
        this.actor = playerActor;
        this.chatPlayerDbComponent = new ChatPlayerDbComponent(this, changed);
        // 所有组件的init和postInit
        initAllComponents();
    }

    public ChatPlayerProp getProp() {
        return prop;
    }

    @Override
    public EntityAttrOuterClass.EntityType getEntityType() {
        return EntityAttrOuterClass.EntityType.ET_Player;
    }

    @Override
    public PlayerActor ownerActor() {
        return actor;
    }

    @Override
    protected void onPostInitFailed() {

    }
    // --------------------------------------生命周期关键节点-----------------------------------------------

    /**
     * 调用所有组件的方法
     */
    private void callAllComponents(Consumer<? super ChatPlayerComponent> action) {
        Collection<ChatPlayerComponent> components = getAllComponents();
        for (ChatPlayerComponent component : components) {
            action.accept(component);
        }
    }

    /**
     * 安全调用所有组件的方法
     */
    public void callAllComponentsSafe(Consumer<? super ChatPlayerComponent> action) {
        callAllComponents((component) -> {
            try {
                action.accept(component);
            } catch (Exception e) {
                WechatLog.error("chat id={} callAllComponents error", getEntityId(), e);
            }
        });
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        if (isStartZoneMigrate()) {
            // 如果处于移民中，前面已经全量落库过了
            return;
        }
        // 阻塞式落脏
        getDbComponent().saveOnDestroy();
        getProp().unMarkAll();
    }

    public boolean isStartZoneMigrate() {
        return isStartZoneMigrate;
    }

    public void startZoneMigrate() {
        isStartZoneMigrate = true;
        // 强制落库
        getDbComponent().saveAndStop();
    }

    // --------------------------------------component--------------------------------
    private final ChatPlayerPropComponent chatPlayerPropComponent = new ChatPlayerPropComponent(this);
    private final ChatPlayerDbComponent chatPlayerDbComponent;
    private final ChatPlayerHandleChatComponent handleChatComponent = new ChatPlayerHandleChatComponent(this);
    private final ChatPlayerMaintenanceComponent maintenanceComponent = new ChatPlayerMaintenanceComponent(this);

    public ChatPlayerPropComponent getPropComponent() {
        return chatPlayerPropComponent;
    }

    public ChatPlayerDbComponent getDbComponent() {
        return chatPlayerDbComponent;
    }

    public ChatPlayerHandleChatComponent getHandleChatComponent() {
        return handleChatComponent;
    }

    public ChatPlayerMaintenanceComponent getMaintenanceComponent() {
        return maintenanceComponent;
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.AddClanPrivateScoreEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;
/**
 * 添加联盟个人积分
 * <AUTHOR>
 */
public class AddClanPrivateScoreChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(AddClanPrivateScoreChecker.class);

    public static List<String> attentionList = Lists.newArrayList(AddClanPrivateScoreEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configScore = taskParams.get(0);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE:
                if (event instanceof AddClanPrivateScoreEvent) {
                    AddClanPrivateScoreEvent subEvent = (AddClanPrivateScoreEvent) event;
                    prop.setProcess(Math.min(prop.getProcess() + subEvent.getScore(), configScore));
                    LOGGER.info("AddClanPrivateScoreChecker updateProcess, trigger addClanPrivateScoreEvent, playerId={}, add score={}, process={}, config score={}", event.getPlayer().getPlayerId(), ((AddClanPrivateScoreEvent) event).getScore(), prop.getProcess(), configScore);
                }
                break;
            default:
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }

        return prop.getProcess() >= configScore;
    }
}

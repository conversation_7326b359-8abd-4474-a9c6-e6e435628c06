package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.CommonEnum;

/**
 * 队列解锁事件，解锁默认队列不会抛出来
 * <p>
 * 可能是新开队列、已开队列时间延长、解锁永久队列都有可能哦
 */
public class PlayerQueueUnlockEvent extends PlayerEvent {

    private final CommonEnum.QueueTaskType queueType;
    private final long unlockSec;

    public PlayerQueueUnlockEvent(PlayerEntity entity, CommonEnum.QueueTaskType queueType, long unlockSec) {
        super(entity);
        this.queueType = queueType;
        this.unlockSec = unlockSec;
    }

    public CommonEnum.QueueTaskType getQueueType() {
        return queueType;
    }

    public long getUnlockSec() {
        return unlockSec;
    }
}

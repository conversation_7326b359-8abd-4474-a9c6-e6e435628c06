package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.constant.ChatConstants;
import com.yorha.common.db.tcaplus.msg.BatchSelectAsk;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.result.BatchGetResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.helper.ZoneFindHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.id.ZoneSnowflakeFactory;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.SsClanBase.*;
import com.yorha.proto.SsClanMember.*;
import com.yorha.proto.SsClanRank.GetClanRankPageInfoAns;
import com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk;
import com.yorha.proto.SsClanRank.GetTopClanRankInfoAns;
import com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk;
import com.yorha.proto.SsClanTerritory.*;
import com.yorha.proto.Struct.ClanRecord;
import com.yorha.proto.Struct.DisplayData;
import com.yorha.proto.StructClan.Int64ClanMemberMap;
import com.yorha.proto.StructClanPB.Int64ClanMemberMapPB;
import com.yorha.proto.StructMsg.RankInfoDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yorha.common.addition.AdditionUtil.CLAN_ADDITION_SOURCES;
import static com.yorha.common.enums.statistic.StatisticEnum.JOIN_CLAN_NUM;
import static com.yorha.proto.CommonEnum.Reason.ICR_CREATE_CLAN;

/**
 * <AUTHOR>
 */
public class PlayerClanComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanComponent.class);
    private static final int OWNER_STAFF = 1;


    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerClanComponent::refreshClanScore);
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerClanComponent::refreshClanHelpCureLimit);
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, PlayerClanComponent::startClanRecommendTask);
    }

    private ActorTimer clanRecommendTask;

    public PlayerClanComponent(PlayerEntity owner) {
        super(owner);
    }

    private static void refreshClanScore(PlayerDayRefreshEvent event) {
        for (ClanScoreItemProp prop : event.getPlayer().getProp().getClan().getScoreInfo().values()) {
            prop.setTodayAdd(0);
        }
        LOGGER.info("{} refreshClanScore end", event.getPlayer());
    }

    private static void refreshClanHelpCureLimit(PlayerDayRefreshEvent event) {
        event.getPlayer().getProp().getClan().getPlayerClanHelpModel().setTodayCureHelp(0).setCannotCreateCureHelp(false);
        LOGGER.info("{} refreshClanHelpCureLimit end", event.getPlayer());
    }

    /**
     * 开始军团推荐task
     */
    private static void startClanRecommendTask(MainCityUpgradeStaticEvent event) {
        event.getPlayer().getPlayerClanComponent().startClanRecommendTask();
    }

    @Override
    public void onLogin() {
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns loginAns) {
        long oldClanId = getClanId();
        CheckInClanAns ans = checkInClanAndRefreshClanInfo(SystemClock.nowOfSeconds());
        if (ans == null) {
            startClanRecommendTask();
            if (isInClan()) {
                // 此时玩家身上不应该有军团信息了，因为正常情况下前面已经执行了退军团的逻辑
                WechatLog.error("player {} shouldn't in clan now", getOwner());
            }
        }
        // 必须确保玩家拥有sessionId
        tellNeedClanHelpItemIdsNtf();

        // 同步clanId
        if (loginAns.getClanId() != getClanId() && oldClanId == getClanId()) {
            // loginAns.getClanId() != getClanId() scenePlayer和player的数据不一致了
            // oldClanId == getClanId() 是否在checkInPlayer的时候退盟, 如果退了在退盟逻辑中已经同步，就不用同步scene了
            SsScenePlayer.SyncPlayerClanIdNameCmd.Builder builder = SsScenePlayer.SyncPlayerClanIdNameCmd.newBuilder();
            builder.setPlayerId(getEntityId()).setClanId(getClanId());
            if (isInClan()) {
                builder.setName(getClanBaseInfo().getSname());
            }
            ownerActor().tellBigScene(builder.build());
            // 通知PlayerCard更新
            getOwner().getPlayerPropComponent().updatePlayerCardCache(true);
        }
    }

    @Override
    public void afterLogout() {
        this.checkOutPlayer();
        cancelClanRecommendTask();
    }

    public ClanInfoProp getProp() {
        return getOwner().getProp().getClan();
    }

    /**
     * 是否在联盟中
     */
    public boolean isInClan() {
        return getClanId() != 0;
    }

    /**
     * @return 联盟id
     */
    public long getClanId() {
        return getOwner().getProp().getClan().getClanId();
    }

    /**
     * @return 进入联盟时间。
     */
    public long getEnterClanTsMs() {
        return getOwner().getProp().getClan().getEnterTime();
    }

    /**
     * 检查名名称是否有效
     *
     * @param name 全称
     * @throws GeminiException 异常
     */
    public void checkClanName(String name) throws GeminiException {
        ClanDataTemplateService clanDataTemplateService = ResHolder.getResService(ClanDataTemplateService.class);
        // 全称合法性
        if (!clanDataTemplateService.checkClanNameLegal(name)) {
            throw new GeminiException(ErrorCode.CLAN_CLAN_NAME_ERROR);
        }
        // 名字唯一性
        ErrorCode code = NameHelper.checkNameRepeat(ownerActor(), NameType.CLAN_NAME, name);
        if (code.isNotOk()) {
            throw new GeminiException(code);
        }
        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(name, UgcSceneId.USI_CLAN_NAME);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }
    }

    /**
     * 检查简称合法性
     *
     * @param sName 简称
     */
    public void checkClanSimpleName(String sName) {
        ClanDataTemplateService clanDataTemplateService = ResHolder.getResService(ClanDataTemplateService.class);
        if (!clanDataTemplateService.checkClanSimpleNameLegal(sName)) {
            throw new GeminiException(ErrorCode.CLAN_CLAN_SIMPLE_NAME_ERROR);
        }
        ErrorCode code = NameHelper.checkNameRepeat(ownerActor(), NameType.CLAN_SIMPLE_NAME, sName);
        if (code.isNotOk()) {
            throw new GeminiException(code);
        }
        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(sName, UgcSceneId.USI_CLAN_SNAME);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }
    }

    /**
     * 检查联盟宣言是否有效
     *
     * @param describe 联盟宣言
     */
    public void checkClanDescribe(String describe) {
        ClanDataTemplateService clanDataTemplateService = ResHolder.getResService(ClanDataTemplateService.class);
        // 描述合法性
        if (!clanDataTemplateService.checkClanDescribe(describe)) {
            throw new GeminiException(ErrorCode.CLAN_DESCRIBE_ERROR);
        }

        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(describe, UgcSceneId.USI_CLAN_DESCRIBE);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }
    }

    public long playerCreateClan(StructClanPB.ClanBaseInfoPB createClanInfo) {
        ClanInfoProp clanInfo = getOwner().getProp().getClan();
        if (clanInfo.getClanId() > 0) {
            throw new GeminiException(ErrorCode.CLAN_ALREADY_IN_ONE);
        }
        // 是否仍处于不能成为军团长的冷却期
        if (SystemClock.now() < getProp().getCanBeClanOwnerTsMs()) {
            LOGGER.warn("can not be clan owner until {}", getProp().getCanBeClanOwnerTsMs());
            throw new GeminiException(ErrorCode.CLAN_QUIT_CD);
        }

        // 名字检查
        String simpleName = createClanInfo.getSname();
        String name = createClanInfo.getName();
        String describe = createClanInfo.getDescribe();
        this.checkClanSimpleName(simpleName);
        this.checkClanName(name);
        this.checkClanDescribe(describe);

        // 扣资源
        ConstClanTemplate constClanTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        int moneyType = constClanTemplate.getClanCreateMoneyType();
        int cost = constClanTemplate.getClanCreateMoney();
        consumeForCreateOrModifyClanInfo(moneyType, cost, ICR_CREATE_CLAN);
        LOGGER.info("consume {} {} to create clan name={}, sname={}", moneyType, cost, name, simpleName);

        // 名字占用
        long clanId = ZoneSnowflakeFactory.nextId();
        List<Pair<NameType, String>> list = new ArrayList<>();
        list.add(Pair.of(NameType.CLAN_NAME, name));
        list.add(Pair.of(NameType.CLAN_SIMPLE_NAME, simpleName));
        ErrorCode errorCode = NameHelper.occupyNames(ownerActor(), list, clanId);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }

        // 构建联盟属性
        Clan.ClanEntity.Builder builder = Clan.ClanEntity.newBuilder();
        StructClan.ClanMember member = genClanMember(true);
        builder.setId(clanId).setOwnerId(getEntityId()).setCombat(getOwner().getProp().getPlayerPowerInfo().getTotalPower());
        builder.setCreateTime(member.getEnterTime()).setNum(1).setNumMax(constClanTemplate.getClanMaxNum()).getMemberBuilder().putDatas(member.getId(), member);
        builder.getMiscModelBuilder().setKillScore(getOwner().getDataRecordComponent().getRecordValue(CommonEnum.DataRecordType.DRT_KILL_SCORE));
        builder.getMiscModelBuilder().setOwnerBecomeTsMs(SystemClock.now());
        StructClan.ClanBaseInfo.Builder base = builder.getBaseBuilder();
        base.setSname(simpleName).setName(name).setLanguage(createClanInfo.getLanguage()).setRequire(createClanInfo.getRequire()).setDescribe(describe);
        base.setTerritoryColor(createClanInfo.getTerritoryColor()).setFlagColor(createClanInfo.getFlagColor()).setFlagShading(createClanInfo.getFlagShading())
                .setFlagSign(createClanInfo.getFlagSign()).setNationFlagId(createClanInfo.getNationFlagId());
        // create
        ownerActor().callTargetClan(clanId, CreateClanEntityAsk.newBuilder().setProp(builder.build()).build());
        // on create
        joinClan(clanId, member.getEnterTime());
        afterJoinedClan(clanId, simpleName, member.getEnterTime(), true);
        // 发送大世界跑马灯
        StructClan.ClanFlagInfo.Builder clanFlagBuilder = StructClan.ClanFlagInfo.newBuilder();
        clanFlagBuilder.setFlagColor(createClanInfo.getFlagColor()).setFlagShading(createClanInfo.getFlagShading())
                .setFlagSign(createClanInfo.getFlagSign()).setNationFlagId(createClanInfo.getNationFlagId()).setTerritoryColor(createClanInfo.getTerritoryColor());
        sendCreateClanMarquee(name, clanFlagBuilder.build());
        LOGGER.info("createClan succeed. PlayerId: {}  ClanId:{}", getEntityId(), clanId);
        return clanId;
    }

    public CommonMsg.ClanCardInfo getSingleClanCardInfo(long clanId) {
        try {
            if (clanId == getClanId()) {
                FetchClanCardAns ans = ownerActor().callCurClan(FetchClanCardAsk.getDefaultInstance());
                if (ans.hasInfo()) {
                    return ans.getInfo();
                }
                return null;
            }
            SsClanCard.QueryClanCardAns ans = CardHelper.queryClanCardSync(ownerActor(), clanId);
            if (ans.hasInfo()) {
                return ans.getInfo();
            }
            // call了错误的clan返回的ans是空的
        } catch (Exception e) {
            LOGGER.error("getSingleClanCardInfo {} error", clanId, e);
        }
        return null;
    }

    public void playerSearchClan(IActorRef sessionRef, String name, int seqId) {
        LOGGER.info("{} search clan name:{}", getOwner(), name);
        // 如果不使用名字搜索，则到scene上根据玩家信息获取特殊默认列表
        if (StringUtils.isEmpty(name)) {
            SsSceneClan.FetchDefaultClanListAsk.Builder ask = SsSceneClan.FetchDefaultClanListAsk.newBuilder();
            ask.setPlayerId(getPlayerId());
            // 发到原服
            SsSceneClan.FetchDefaultClanListAns ans = ownerActor().callSelfBigScene(ask.build());
            getClanListInfo(sessionRef, ans.getClanListList(), seqId);
            return;
        }
        // 如果使用名字搜索，则到name上根据玩家输入名字获取列表
        int limit = ResHolder.getResService(ConstClanKVResService.class).getTemplate().getClanListNum();
        List<TcaplusDb.NameTable.Builder> listReq = new ArrayList<>();
        listReq.add(TcaplusDb.NameTable.newBuilder().setNameType(NameType.CLAN_NAME_VALUE).setName(name));
        listReq.add(TcaplusDb.NameTable.newBuilder().setNameType(NameType.CLAN_SIMPLE_NAME_VALUE).setName(name));
        SsName.SearchClanNameAsk.Builder ask = SsName.SearchClanNameAsk.newBuilder();
        BatchGetResult<TcaplusDb.NameTable.Builder> result = ownerActor().callGameDb(new BatchSelectAsk<>(listReq, BatchGetOption.newBuilder().build()));
        ask.setNum(limit).setName(name);
        SsName.SearchClanNameAns ans = ownerActor().callName(ask.build());
        LinkedList<Long> queryClan = new LinkedList<>(ans.getClanListList());
        for (ValueWithVersion<TcaplusDb.NameTable.Builder> value : result.values) {
            queryClan.remove(value.value.getOwnerId());
            queryClan.addFirst(value.value.getOwnerId());
        }
        while (queryClan.size() > limit) {
            queryClan.removeLast();
        }
        getClanListInfo(sessionRef, queryClan, seqId);
    }

    private void getClanListInfo(IActorRef sessionRef, List<Long> clanIds, int seqId) {
        LOGGER.info("try get clan list {}", clanIds);
        if (clanIds.isEmpty()) {
            ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_SEARCHCLAN_S2C, null, PlayerClan.Player_SearchClan_S2C.getDefaultInstance());
            return;
        }
        CardHelper.batchQueryClanCard(ownerActor(), clanIds,
                (map) -> {
                    PlayerClan.Player_SearchClan_S2C.Builder builder = PlayerClan.Player_SearchClan_S2C.newBuilder();
                    long now = SystemClock.now();
                    for (CommonMsg.ClanCardInfo info : map.values()) {
                        if (info.getStageInfo().getDissolutionTsMs() > 0 && info.getStageInfo().getDissolutionTsMs() <= now) {
                            // 解散了，别显示了
                            continue;
                        }
                        builder.addClanList(info);
                    }
                    // 可能数量不匹配，有些军团解散了
                    ownerActor().getEntity().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_SEARCHCLAN_S2C, null, builder.build());
                });
    }

    public RankInfoDTO getClanRankSimpleInfo(int rankId) {
        try {
            FetchClanRankSimpleInfoAsk.Builder ask = FetchClanRankSimpleInfoAsk.newBuilder().setRankId(rankId);
            FetchClanRankSimpleInfoAns ans = ownerActor().callCurClan(ask.build());
            return ans.getDto();
        } catch (Exception e) {
            LOGGER.error("getClanRankSimpleInfo {} error", getOwner().getClanId(), e);
        }
        return null;
    }

    public Int64ClanMemberMapPB fetchClanApplyMembers() {
        FetchClanApplyMembersAns ans = ownerActor().callCurClan(FetchClanApplyMembersAsk.getDefaultInstance());
        // call了错误的clan返回的ans是空的
        if (ans.getApplyMembers().getDatasCount() != 0) {
            Int64ClanMemberMap memberMap = ans.getApplyMembers();
            Int64ClanMemberMapProp prop = new Int64ClanMemberMapProp(null, 0);
            prop.mergeFromSs(memberMap);
            return prop.getCopyCsBuilder().build();
        }
        return null;
    }

    public Int64ClanMemberMapPB getSingClanMemberInfo(long clanId) {
        try {
            int zoneId = ZoneFindHelper.queryClanZoneId(clanId);
            FetchClanMemberInfoAns ans = ownerActor().calZoneClan(zoneId, clanId, FetchClanMemberInfoAsk.getDefaultInstance());
            // call了错误的clan返回的ans是空的
            if (!ans.getMsg().isEmpty()) {
                Int64ClanMemberMap memberMap = MsgUtils.parseProto(Int64ClanMemberMap.getDefaultInstance(), ans.getMsg());
                if (memberMap != null) {
                    Int64ClanMemberMapProp prop = new Int64ClanMemberMapProp(null, 0);
                    prop.mergeFromSs(memberMap);
                    return prop.getCopyCsBuilder().build();
                }
            }
        } catch (Exception e) {
            LOGGER.error("getSingClanMemberInfo {} error", clanId, e);
        }
        return null;
    }

    public long playerApplyJoinClan(long clanId, String invitePlayerName) {
        ClanInfoProp clanInfo = getOwner().getProp().getClan();
        // 已经加入别的联盟
        if (clanInfo.getClanId() > 0) {
            throw new GeminiException(ErrorCode.CLAN_ALREADY_IN_ONE);
        }
        boolean isByInvite = StringUtils.isNotEmpty(invitePlayerName);
        // 已经申请过, 主动申请才需要检查。受邀加入就不检查了
        if (clanInfo.getApplyClanList().contains(clanId) && !isByInvite) {
            throw new GeminiException(ErrorCode.CLAN_ALREADY_APPLY);
        }
        // 构造申请者prop
        PlayerApplyClanAsk.Builder builder = PlayerApplyClanAsk.newBuilder().setMember(genClanMember(false));
        PlayerApplyClanAns ans = ownerActor().callTargetClan(clanId, builder.setInvitePlayerName(invitePlayerName).build());
        if (ErrorCode.isOK(ans.getCode())) {
            // 不再直接加入，加入联盟逻辑由联盟发起，走另外一套逻辑
//            onJoinedClan(clanId, ans.getClanSimpleName(), builder.getMember().getEnterTime(), false);
            return clanId;
        }
        if (ans.getCode() == ErrorCode.CLAN_APPLY_SUCCESS.getCodeId()) {
            // 申请加入军团成功
            clanInfo.addApplyClanList(clanId);
            LOGGER.info("{} apply join clan {}", getOwner(), clanId);
            return 0;
        }
        if (ans.getCode() == ErrorCode.CLAN_FULL.getCodeId()) {
            // 军团已满，常见情况不打日志
            throw new GeminiException(ErrorCode.CLAN_FULL);
        }
        if (ans.getCode() == ErrorCode.CLAN_NOT_EXIST.getCodeId()) {
            // 军团不存在，可能是军团已解散或clanId不存在
            LOGGER.info("{} apply join clan {} not exist", getOwner(), clanId);
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        if (ans.getCode() == ErrorCode.CLAN_ALREADY_IN_ONE.getCodeId()) {
            // 申请人已经有军团，打条日志，不做处理
            LOGGER.info("{} apply join clan {}, but already in one", getOwner(), clanId);
            return clanId;
        }
        if (ErrorCode.CLAN_ALREADY_APPLY.isSame(ans.getCode())) {
            // 已在联盟申请列表，理论不会有，认为是玩家侧没有数据导致未阻拦
            clanInfo.addApplyClanList(clanId);
            LOGGER.error("{} apply join clan {} data repeated", getOwner(), clanId);
            throw new GeminiException(ErrorCode.CLAN_ALREADY_APPLY);
        }
        LOGGER.error("{} apply join clan {} failed {}", getOwner(), clanId, ans.getCode());
        return 0;
    }

    /**
     * 被通知加入联盟的审批结果
     */
    public void onNotifyClanApplyResult(long clanId, boolean isAllow, String clanSName, long operatorId) {
        // 移除申请记录
        getOwner().getProp().getClan().removeApplyClanList(clanId);
        SsPlayerClan.ClanApplyResultAns.Builder ans = SsPlayerClan.ClanApplyResultAns.newBuilder();
        ans.setPlayerId(getPlayerId());
        if (isAllow) {
            long nowId = getClanId();
            // 未加入任何联盟 或者已经加入了该联盟
            if (nowId == 0 || nowId == clanId) {
                if (nowId == clanId) {
                    LOGGER.error("onNotifyClanApplyResult already joined {} {} {} {} ", getOwner(), clanId, clanSName, operatorId);
                }
                StructClan.ClanMember genClanMember = genClanMember(true);
                ans.setIsSuccess(true).setMember(genClanMember);
                joinClan(clanId, genClanMember.getEnterTime());
                // 在这里就answer回去，保证在afterJoinedClan中call clan的时候，clan中已经处理完了入盟流程
                ownerActor().answer(ans.build());
                afterJoinedClan(clanId, clanSName, genClanMember.getEnterTime(), false);
            } else {
                // 已经加入别的联盟
                ans.setIsSuccess(false);
                ownerActor().answer(ans.build());
            }
        } else {
            LOGGER.info("onNotifyClanApplyResult {} {} refused ", getOwner(), clanId);
        }
    }

    /**
     * 取消申请加入联盟
     */
    public void playerCancelApplyJoinClan(long clanId) {
        ClanInfoProp clanInfo = getOwner().getProp().getClan();
        // 通知对应联盟
        if (clanInfo.getApplyClanList().contains(clanId)) {
            clanInfo.removeApplyClanList(clanId);
            ownerActor().tellClan(clanId, PlayerCancelApplyCmd.newBuilder().setPlayerId(getEntityId()).build());
        }
    }

    /**
     * 向clan报备，刷新联盟相关信息
     * 1. 上交ActorRef
     * 2. 设置组播Tag
     *
     * @param tsMs 当前时间戳，毫秒级别
     */
    public CheckInClanAns checkInClanAndRefreshClanInfo(long tsMs) {
        CheckInClanAns ans = checkInPlayer(tsMs);
        refreshCheckInClanInfo(ans);
        return ans;
    }

    /**
     * 刷新联盟信息
     * 职位改变信息，主动向clan报备。
     * 1. 上交ActorRef
     * 2. 设置组播Tag。
     *
     * @param tsMs 时间戳，毫秒级别
     */
    public CheckInClanAns checkInPlayer(long tsMs) {
        ClanInfoProp clanInfoProp = this.getOwner().getProp().getClan();
        if (clanInfoProp.getClanId() == 0) {
            // 不在军团，返回null，期望返回后玩家身上仍然没有军团信息
            return null;
        }
        if (!this.getOwner().isOnline()) {
            LOGGER.error("try check in player not online! playerId={}", this.getEntityId());
            return null;
        }
        try {
            final CommonMsg.ActorRefData sessionRefData = this.getOwner().getSessionComponent().getSessionRefData();
            final CheckInClanAsk ask = CheckInClanAsk.newBuilder().setPlayerId(getEntityId())
                    .setSessionRefData(sessionRefData)
                    .setClanGiftIndex(this.getOwner().getClanGiftComponent().getCurGiftIndex())
                    .setClanMailIndex(getOwner().getMailComponent().getClanMailIndex())
                    .setEnterClanTsMs(getOwner().getPlayerClanComponent().getEnterClanTsMs())
                    .build();
            final CheckInClanAns ans = ownerActor().callCurClan(ask);
            if (ans.getCode() == ErrorCode.CLAN_NOT_IN.getCodeId() || ans.getCode() == ErrorCode.CLAN_NOT_EXIST.getCodeId()) {
                if (ans.getCode() == ErrorCode.CLAN_NOT_EXIST.getCodeId()) {
                    LOGGER.warn("check in fail! clan is not exist. clanId={}, playerId={}", clanInfoProp.getClanId(), this.getEntityId());
                } else {
                    LOGGER.error("check in fail! leave clan. clanId={}, playerId={}, code={}", clanInfoProp.getClanId(), this.getEntityId(), ans.getCode());
                }
                this.onLeaveClan(tsMs);
                return null;
            }
            return ans;
        } catch (Exception e) {
            LOGGER.error("checkInClan Exception clanId={}, ", clanInfoProp.getClanId(), e);
        }
        return null;
    }

    /**
     * 登出联盟
     */
    public void checkOutPlayer() {
        if (!isInClan()) {
            return;
        }
        final CheckOutClanAsk ask = CheckOutClanAsk.newBuilder().setPlayerId(getPlayerId()).build();
        try {
            final CheckOutClanAns ans = ownerActor().callCurClan(ask);
            if (ans.getCode() == ErrorCode.CLAN_NOT_IN.getCodeId() || ans.getCode() == ErrorCode.CLAN_NOT_EXIST.getCodeId()) {
                LOGGER.error("check out fail! clanId: {}, playerId: {}, code:{}", getClanId(), getPlayerId(), ans.getCode());
                this.onLeaveClan(SystemClock.now());
            }
        } catch (Exception e) {
            LOGGER.error("checkOutClan Exception clanId:{}, ", getClanId(), e);
        }
    }

    /**
     * 离开军团处理
     */
    public void onLeaveClan(long leaveTsMs) {
        // 清理军团频道
        final long clanId = getProp().getClanId();
        String channelId = String.valueOf(clanId);
        ChatPlayerEntity chatPlayerEntity = ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getHandleChatComponent().clearChannelData(ChatChannel.CC_CLAN, channelId);
        // 改变属性
        ClanInfoProp clanInfoProp = getOwner().getProp().getClan();
        clanInfoProp.setClanId(0).setQuitTime(leaveTsMs);
        // 清空军团礼物
        clanInfoProp.clearGiftItems();
        clanInfoProp.clearTreasureGiftItems();
        clanInfoProp.setCurClanGiftIndex(-1);

        ownerActor().tellBigScene(SsScenePlayer.SyncPlayerClanIdNameCmd.newBuilder().setPlayerId(getEntityId()).setClanId(0).setName("").build());
        // 重置军团游标
        getOwner().getMailComponent().resetClanMailIndex();
        // 删除所有军团相关的加成、buff、红点和其他信息
        removeAllInfoFromClan();

        // 清除联盟相关红点
        getOwner().getRedDotComponent().clearRedDot(RedDotKey.RDK_CLAN_APPLY);
        // 增加军团推荐
        refreshClanRecommendTask();
        // 通知PlayerCard更新
        getOwner().getPlayerPropComponent().updatePlayerCardCache(true);
        new PlayerLeaveClanEvent(getOwner(), clanId).dispatch();
    }

    public void updateClanMemberInfo(StructClan.ClanMember ss) {
        if (isInClan()) {
            UpdateClanMemberInfoCmd.Builder builder = UpdateClanMemberInfoCmd.newBuilder().setPlayerId(getEntityId()).setChange(ss.toByteString());
            ownerActor().tellClan(getOwner().getPlayerClanComponent().getClanId(), builder.build());
        }
    }

    public StructClan.ClanBaseInfo getClanBaseInfo() {
        GetClanBaseInfoAns ans = ownerActor().callCurClan(GetClanBaseInfoAsk.getDefaultInstance());
        return ans.getBase();
    }

    /**
     * 刷新军团登录相关信息
     */
    public void refreshCheckInClanInfo(CheckInClanAns ans) {
        if (ans == null) {
            LOGGER.info("PlayerClanComponent refreshCheckInClanInfo {} ans is null, do nothing", getOwner());
            return;
        }
        refreshClanBuff(ans.getDevBuffInfo());
        refreshClanAddition(ans.getAdditionInfo());
        refreshClanBasicInfo(ans.getBasicInfo());
        refreshClanRedDotInfo(ans.getRedDotInfo());
        refreshClanGiftInfo(ans.getGiftInfo());
        refreshClanBuildingInfo(ans.getBuildingInfo());
        refreshClanMailInfo(ans.getMailInfo());
    }

    private void triggerClanEvent(CommonMsg.CheckInBuildingInfo checkInBuildingInfo) {
        new ClanBuildingInfoEvent(this.getOwner(), checkInBuildingInfo.getClanBuildingMap()).dispatch();
        new ClanTerritoryLvChangeEvent(this.getOwner(), checkInBuildingInfo.getTerritoryLv()).dispatch();
    }

    private void refreshClanGiftInfo(CommonMsg.CheckInGiftInfo checkInGiftInfo) {
        // 首次拉取，去除不可见礼物
        if (this.getOwner().getClanGiftComponent().getCurGiftIndex() <= 0) {
            for (StructClan.ClanGiftItem clanGiftItem : checkInGiftInfo.getClanGiftsList()) {
                if (TimeUtils.second2Ms(clanGiftItem.getCreateTsSec()) < this.getEnterClanTsMs()) {
                    continue;
                }
                this.getOwner().getClanGiftComponent().addClanGift(clanGiftItem);
            }
        } else {
            for (StructClan.ClanGiftItem clanGiftItem : checkInGiftInfo.getClanGiftsList()) {
                this.getOwner().getClanGiftComponent().addClanGift(clanGiftItem);
            }
        }

        PlayerClan.Player_ClanGiftInfo_NTF.Builder ntf = PlayerClan.Player_ClanGiftInfo_NTF.newBuilder();
        ntf.setClanGiftInfo(checkInGiftInfo.getClanGiftInfo());
        // 通知客户端军团最新信息
        this.getOwner().sendMsgToClient(MsgType.PLAYER_NOTIFYCLANGIFTINFOONLINE_NTF, ntf.build());
    }

    private void refreshClanBuildingInfo(CommonMsg.CheckInBuildingInfo checkInBuildingInfo) {
        triggerClanEvent(checkInBuildingInfo);
    }

    private void refreshClanMailInfo(CommonMsg.CheckInMailInfo checkInMailInfo) {
        List<StructMail.NewMailCache> mailsToAdd = new ArrayList<>(checkInMailInfo.getMailsList());
        // 根据index排序，避免游标错序
        mailsToAdd.sort(Comparator.comparing(StructMail.NewMailCache::getMailIndex));
        List<PlayerMailProp> addedMail = Lists.newLinkedList();

        LOGGER.info("PlayerClanComponent refreshClanMailInfo clan mail num={}, self clan index={}", mailsToAdd.size(), this.getOwner().getMailComponent().getClanMailIndex());
        // 添加离线邮件
        for (StructMail.NewMailCache mailToAdd : mailsToAdd) {
            PlayerMailProp mail = getOwner().getMailComponent().onMailAdd(mailToAdd, false);
            if (mail == null) {
                continue;
            }
            addedMail.add(mail);
        }
        // 更新游标（未更新邮件也需更新）
        final MailReadIndexProp mailReadIndex = getOwner().getProp().getMailModel().getMailreadIndex();
        mailReadIndex.getClanMailIndex().setClanMailIndex(checkInMailInfo.getClanMailIndex()).setClanId(getOwner().getClanId());
        LOGGER.info("PlayerClanComponent refreshClanMailInfo after refresh clan index={}", this.getOwner().getMailComponent().getClanMailIndex());
        // 添加的邮件为空，不通知客户端
        if (addedMail.isEmpty()) {
            return;
        }
        // 通知客户端收到新邮件
        getOwner().getMailComponent().notifyReceiveNewMails(addedMail.stream().filter((mail) -> getOwner().getMailComponent().getPlayerMail().containsKey(mail.getMailId())).collect(Collectors.toList()));
    }

    /**
     * 刷新联盟加成
     */
    public void refreshClanAddition(CommonMsg.CheckInAdditionInfo ans) {
        getOwner().getAddComponent().updateAddition(ans.getAddition());
    }

    /**
     * 联盟buff更新
     */
    public void onClanDevBuffUpdate(SsPlayerClan.OnClanDevBuffUpdateCmd ask) {
        CommonMsg.DevBuffAddParam buff = ask.getDevBuffProp();
        if (ask.getIsAdd()) {
            getOwner().getPlayerDevBuffComponent().addDevBuff(buff.getDevBuffId(),
                    buff.getStartTime(),
                    buff.getEndTime(),
                    buff.getBuffType(),
                    buff.getSourceType(),
                    buff.getLayer());
        } else {
            getOwner().getPlayerDevBuffComponent().removeDevBuffByLayer(buff.getDevBuffId(), buff.getLayer());
        }
    }

    /**
     * 检查是否是第一次进入军团，如果是给玩家发送第一次进入军团的奖励
     */
    public void checkAndSendFirstEnterClanReward() {
        if (getOwner().getProp().getClan().getHasGotFirstEnterClanReward()) {
            return;
        }
        ConstClanTemplate constClanTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        int mailId = constClanTemplate.getFirstJoinAllianceMail();

        // 设置邮件参数(奖励)
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(mailId);
        for (IntPairType itemPair : constClanTemplate.getFirstJoinClanReward()) {
            params.getItemRewardBuilder().addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(itemPair.getKey()).setCount(itemPair.getValue()).build());
        }

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(getOwner().getPlayerId())
                .setZoneId(getOwner().getZoneId())
                .build();
        this.getOwner().getMailComponent().sendPersonalMail(receiver, params.build());

        getOwner().getProp().getClan().setHasGotFirstEnterClanReward(true);
    }

    /**
     * gm设置第一次加入军团奖励
     *
     * @param flag 标志位
     */
    public void gmSetHasGotFirstEnterClanReward(boolean flag) {
        getOwner().getProp().getClan().setHasGotFirstEnterClanReward(flag);
    }

    /**
     * 删除所有来自联盟的信息
     */
    public void removeAllInfoFromClan() {
        removeClanBuff();
        removeClanAddition();
        removeClanRedDotInfo();
        removeClanBasicInfo();
    }

    /**
     * 刷新联盟buff
     */
    public void refreshClanBuff(CommonMsg.CheckInDevBuffInfo ans) {
        List<DevBuffProp> curClanBuff = getOwner().getPlayerDevBuffComponent().getDevBuffByBuffType(DevBuffType.DBT_CLAN_BUFF);
        DevBuffSysProp clanDevBuff = new DevBuffSysProp();
        clanDevBuff.mergeFromSs(ans.getDevBuff());
        // 删除已经不存在的联盟buff
        for (DevBuffProp devBuffProp : curClanBuff) {
            if (clanDevBuff.getDevBuffV(devBuffProp.getDevBuffId()) == null) {
                getOwner().getPlayerDevBuffComponent().removeDevBuff(devBuffProp.getDevBuffId());
            }
        }
        // 添加新增的联盟buff
        for (Map.Entry<Integer, DevBuffProp> entry : clanDevBuff.getDevBuff().entrySet()) {
            DevBuffProp existDevBuff = getOwner().getProp().getDevBuffSysNew().getDevBuffV(entry.getKey());
            DevBuffProp buff = entry.getValue();
            // 船新的buff
            if (existDevBuff == null) {
                getOwner().getPlayerDevBuffComponent().addDevBuffByLayer(
                        buff.getDevBuffId(),
                        buff.getStartTime(),
                        buff.getEndTime(),
                        buff.getDevBuffType(),
                        buff.getSourceType(),
                        buff.getLayer());
            } else {
                if (existDevBuff.getLayer() > buff.getLayer()) {
                    // 要删除层数
                    getOwner().getPlayerDevBuffComponent().removeDevBuffByLayer(buff.getDevBuffId(), existDevBuff.getLayer() - buff.getLayer());
                } else if (existDevBuff.getLayer() < buff.getLayer()) {
                    // 要添加层数
                    getOwner().getPlayerDevBuffComponent().addDevBuffByLayer(buff.getDevBuffId(),
                            buff.getStartTime(),
                            buff.getEndTime(),
                            buff.getDevBuffType(),
                            buff.getSourceType(),
                            buff.getLayer() - existDevBuff.getLayer());
                }
            }
        }
    }

    /**
     * 领取联盟势力宝箱奖励
     */
    public PlayerClan.Player_ObtainClanPowerReward_S2C.Builder obtainClanPowerReward() {
        long clanId = getOwner().getPlayerClanComponent().getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (TimeUtils.isSameDayWithNow(getOwner().getProp().getClan().getObtainPowerRewardTsMs())) {
            throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
        }
        GetClanPowerRewardAsk.Builder call = GetClanPowerRewardAsk.newBuilder().setPlayerId(getEntityId());
        GetClanPowerRewardAns ans = ownerActor().callCurClan(call.build());

        // 构建返回包
        PlayerClan.Player_ObtainClanPowerReward_S2C.Builder retBuilder = PlayerClan.Player_ObtainClanPowerReward_S2C.newBuilder();
        int clanPowerRewardId = ans.getRewardId();
        getOwner().getProp().getClan().setObtainPowerRewardTsMs(SystemClock.now());
        // 发道具奖励
        getOwner().getItemComponent().sendReward(clanPowerRewardId, Reason.ICR_CPR, "");
        retBuilder.setRewardId(clanPowerRewardId);
        // 发资源奖励
        for (Map.Entry<Integer, Integer> entry : ans.getResourceMap().entrySet()) {
            long addCount = entry.getValue();
            getOwner().getPurseComponent().give(CurrencyType.forNumber(entry.getKey()), addCount, Reason.ICR_CPR, "");
            retBuilder.putResource(entry.getKey(), entry.getValue());
        }
        getOwner().getProp().getClan().getResources().clear();
        LOGGER.info("{} obtain clanPowerReward", getOwner());
        return retBuilder;
    }

    /**
     * 退出联盟时缓存的没领取的势力宝箱的资源奖励
     */
    public void onAddClanPowerResources(Map<Integer, Integer> resources) {
        Int32CurrencyMapProp prop = getOwner().getProp().getClan().getResources();
        for (Map.Entry<Integer, Integer> entry : resources.entrySet()) {
            if (!prop.containsKey(entry.getKey())) {
                prop.addEmptyValue(entry.getKey()).setCount(entry.getValue());
                LOGGER.info("{} onAddClanPowerResource. type:{} old:0 add:{}", getOwner(), entry.getKey(), entry.getValue());
                continue;
            }
            long count = prop.get(entry.getKey()).getCount();
            prop.get(entry.getKey()).setCount(count + entry.getValue());
            LOGGER.info("{} onAddClanPowerResource. type:{} old:{} add:{}", getOwner(), entry.getKey(), count, entry.getValue());
        }
    }

    /**
     * 军团排行榜明细
     */
    public GetClanRankPageInfoAns getClanRankInfoList(int rankId, int page) {
        GetClanRankPageInfoAsk.Builder build = GetClanRankPageInfoAsk.newBuilder();
        build.setPage(page);
        build.setRankId(rankId);
        build.setMemberId(this.getPlayerId());
        return ownerActor().callCurClan(build.build());
    }

    /**
     * 军团排行榜榜首
     */
    public GetTopClanRankInfoAns getTopClanRankInfo() {
        GetTopClanRankInfoAsk.Builder build = GetTopClanRankInfoAsk.newBuilder();
        return ownerActor().callCurClan(build.build());
    }

    /**
     * 检查军团建筑参数
     *
     * @param type 建筑参数类型
     * @throws GeminiException 会抛出参数错误的GeminiException
     */
    public void checkMapBuildingTypeParam(CommonEnum.MapBuildingType type) throws GeminiException {
        switch (type) {
            case MBT_MAIN_BASE:
            case MBT_COMMAND_CENTER:
            case MBT_CLAN_FORTRESS:
                break;
            default:
                LOGGER.warn("client send wrong building type {}", type);
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    /**
     * 验证是否可以重建建筑
     */
    public SsSceneClan.VerifyCanRebuildAns verifyCanRebuild(long playerId, long mapBuildingId,
                                                            CommonEnum.MapBuildingType mapBuildingType,
                                                            boolean checkFromConstruct) {
        SsSceneClan.VerifyCanRebuildAsk.Builder ask = SsSceneClan.VerifyCanRebuildAsk.newBuilder();
        ask.setPlayerId(playerId).setClanId(getOwner().getClanId())
                .setType(mapBuildingType)
                .setMapBuildingId(mapBuildingId)
                .setIsConstructCheck(checkFromConstruct);
        return getOwner().ownerActor().callBigScene(ask.build());
    }

    /**
     * 联盟加成更新
     */
    public void onClanAdditionUpdate(SsPlayerClan.OnClanAdditionUpdateCmd ask) {
        if (isInClan()) {
            getOwner().getAddComponent().updateAdditionFromClan(ask);
        }
    }

    /**
     * 增加玩家身上的联盟积分
     *
     * @param scoreType 分数类型
     * @param addScore  增加的分数
     */
    public void addTotalClanScore(ClanScoreCategory scoreType, long addScore) {
        long beforeTotalClanScore = getOwner().getProp().getClan().getTotalClanScore();
        getOwner().getProp().getClan().setTotalClanScore(MathUtils.addExact(beforeTotalClanScore, addScore));
        long nowTotalClanScore = getOwner().getProp().getClan().getTotalClanScore();
        LOGGER.info("add clan score. type: {}, beforeScore: {} addScore: {}, nowScore: {}", scoreType, beforeTotalClanScore, addScore, nowTotalClanScore);
        // 通行证任务：加积分
        new AddClanPrivateScoreEvent(getOwner(), (int) addScore).dispatch();
        // 记录qlog
        getOwner().getQlogComponent().sendGuildPersonalScoreQLog(scoreType, beforeTotalClanScore, nowTotalClanScore, addScore, 0);
    }

    /**
     * 判断联盟积分是否足够
     *
     * @param needScore 期望拥有的积分
     */
    public boolean checkTotalClanScore(int needScore) {
        long totalClanScore = getOwner().getProp().getClan().getTotalClanScore();
        return needScore <= totalClanScore;
    }

    /**
     * 减少玩家身上的联盟积分
     *
     * @param scoreType 分数类型
     * @param decScore  要减少的分数
     */
    public void decTotalClanScore(ClanScoreCategory scoreType, long decScore) {
        long beforeTotalClanScore = getOwner().getProp().getClan().getTotalClanScore();
        if (decScore > beforeTotalClanScore) {
            LOGGER.info("dec score {} bigger than totalClanScore {}, would use totalClanScore", decScore, beforeTotalClanScore);
            decScore = beforeTotalClanScore;
        }
        getOwner().getProp().getClan().setTotalClanScore(beforeTotalClanScore - decScore);
        long nowTotalClanScore = getOwner().getProp().getClan().getTotalClanScore();
        LOGGER.info("dec clan score. type: {}, beforeScore: {} decScore: {}, nowScore: {}", scoreType, beforeTotalClanScore, decScore, nowTotalClanScore);

        // 记录qlog
        getOwner().getQlogComponent().sendGuildPersonalScoreQLog(scoreType, beforeTotalClanScore, nowTotalClanScore, decScore, 1);
    }

    /**
     * 构建军团帮助记录
     *
     * @param type   帮助的类型
     * @param taskId 帮助对应的队列id
     */
    public ClanRecord buildClanHelpRecord(long taskId, QueueTaskType type) {
        ClanRecord.Builder record = ClanRecord.newBuilder();
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();

        // 判断是哪种帮助
        switch (type) {
            case CITY_BUILD: {
                // 建造队列
                record.setRecordType(ClanRecordType.CRT_HELP_BUILD);
                break;
            }
            case MEDICAL_TREATMENT: {
                // 医疗队列
                record.setRecordType(ClanRecordType.CRT_HELP_CURE);
                break;
            }
            case RESEARCH: {
                // 科技队列
                record.setRecordType(ClanRecordType.CRT_HELP_RESEARCH);
                // NOTE(furson): 科技id需要强转int，正确性限制在调用方法内
                paramBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_RESEARCH_ID_FOR_NAME,
                        getOwner().getTechComponent().getParentIdByTechSubId((int) taskId)));
                paramBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64,
                        getOwner().getTechComponent().getTechLevelByTechSubId((int) taskId)));
                break;
            }
            default: {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }
        record.setParams(paramBuilder.build());
        return record.build();
    }

    /**
     * 增加军团帮助治疗次数，判断并设置增加后是否可以再发起治疗帮助
     */
    public void addClanHelpCureTimesAndCheck() {
        int nowCureHelpTimes = getOwner().getProp().getClan().getPlayerClanHelpModel().getTodayCureHelp();
        getOwner().getProp().getClan().getPlayerClanHelpModel().setTodayCureHelp(nowCureHelpTimes + 1);

        // 判断今日是否还可以发起治疗队列
        if (nowCureHelpTimes + 1 >= ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getCureHelpDailyLimit()) {
            getOwner().getProp().getClan().getPlayerClanHelpModel().setCannotCreateCureHelp(true);
        }
    }

    /**
     * 获取最大军团帮助治疗次数
     *
     * @param buildOfferTimes 建筑提供的最大军团治疗次数
     */
    public int compAndGetHelpCureMaxTimes(int buildOfferTimes) {
        int todayCureHelpTimes = getOwner().getProp().getClan().getPlayerClanHelpModel().getTodayCureHelp();
        int dayLimit = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getCureHelpDailyLimit();
        return Math.min(dayLimit - todayCureHelpTimes, buildOfferTimes);
    }

    /**
     * 玩家职位变动时，player属性的变更
     *
     * @param newStaffId 新的职位id
     */
    public void onStaffIdChange(int newStaffId) {
        int beforeStaffId = getOwner().getProp().getClan().getStaffId();
        if (beforeStaffId == newStaffId) {
            return;
        }
        LOGGER.info("onStaffIdChange, staffId changed: from {} to {}", beforeStaffId, newStaffId);
        getOwner().getProp().getClan().setStaffId(newStaffId);

        ClanDataTemplateService clanDataTemplateService = ResHolder.getResService(ClanDataTemplateService.class);
        if (clanDataTemplateService == null) {
            LOGGER.error("clanDataTemplateService is null, cannot change staff's buff");
            return;
        }
        if (clanDataTemplateService.isSpecialStaff(beforeStaffId)) {
            // 删除buff
            for (int buffId : clanDataTemplateService.getBuffIdsByClanStaffId(beforeStaffId)) {
                getOwner().getPlayerDevBuffComponent().removeDevBuff(buffId);
            }
        }
        if (clanDataTemplateService.isSpecialStaff(newStaffId)) {
            // 增加buff
            for (int buffId : clanDataTemplateService.getBuffIdsByClanStaffId(newStaffId)) {
                getOwner().getPlayerDevBuffComponent().addDevBuff(buffId, null, null, DevBuffType.DBT_CLAN_BUFF, DevBuffSourceType.DBST_CLAN_STAFF);
            }
        }
    }

    /**
     * 设置下次可以成为盟主的时间戳(ms)
     */
    public void setNextCanBeOwnerTsMsWhenDissolve() {
        if (!isInEarlyKingdom()) {
            return;
        }
        if (!isClanOwner()) {
            return;
        }
        getProp().setCanBeClanOwnerTsMs(getNextCanBeOwnerTsMsWhenDissolving());
    }

    /**
     * 重置下次可以成为盟主的时间戳，重置时间使用当前的时间戳
     */
    public void resetNextCanBeOwnerTsMs() {
        getProp().setCanBeClanOwnerTsMs(SystemClock.now());
    }

    /**
     * 创建军团、修改军团基本信息时扣除资源
     */
    public void consumeForCreateOrModifyClanInfo(int moneyType, int cost, Reason reason) {
        final CurrencyType currencyType = CurrencyType.forNumber(moneyType);
        if (currencyType == null) {
            throw new GeminiException(ErrorCode.CURRENCY_NOT_EXIST);
        }
        final ErrorCode code = this.getOwner().getPurseComponent().isEnough(currencyType, cost);
        if (code.isNotOk()) {
            throw new GeminiException(code);
        }
        this.getOwner().getPurseComponent().consume(currencyType, cost, reason, "");
    }

    /**
     * 获取上次加入联盟的时间戳
     */
    public long getLastJoinClanTsMs() {
        return getProp().getEnterTime();
    }

    /**
     * 获取上次离开联盟的时间戳
     */
    public long getLastQuitClanTsMs() {
        return getProp().getQuitTime();
    }

    /**
     * gm 军团推荐一次
     */
    public void gmRecommendOnce() {
        doClanRecommendOnce();
    }

    /**
     * 发送创建军团的跑马灯
     *
     * @param name 军团名字
     */
    private void sendCreateClanMarquee(String name, StructClan.ClanFlagInfo clanFlagInfo) {
        final int marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(MarqueeType.CREATE_CLAN);
        Struct.DisplayData.Builder builder = Struct.DisplayData.newBuilder();
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(getOwner().getName()));
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(name));
        SsSceneMarquee.SendSceneMarqueeCmd cmd = SsSceneMarquee.SendSceneMarqueeCmd.newBuilder()
                .setMarqueeId(marqueeId)
                .setDisplayData(builder)
                .setClanFlagInfo(clanFlagInfo)
                .build();
        ownerActor().tellBigScene(getOwner().getZoneId(), cmd);
    }

    private StructClan.ClanMember genClanMember(boolean needAddRes) {
        StructClan.ClanMember.Builder builder = StructClan.ClanMember.newBuilder();
        long now = SystemClock.now();
        builder.setComboat(getOwner().getProp().getPlayerPowerInfo().getTotalPower())
                .setId(getEntityId())
                .setEnterTime(now)
                .setIsOnline(getOwner().isOnline())
                .setCardHead(getOwner().getCardHead().getCopySsBuilder())
                .setNextCanBeOwnerTsMs(getOwner().getProp().getClan().getCanBeClanOwnerTsMs())
                .setKillScore(getOwner().getDataRecordComponent().getRecordValue(DataRecordType.DRT_KILL_SCORE));
        if (needAddRes) {
            builder.setResLastCalTsMs(now)
                    .setResources(getOwner().getProp().getClan().getResources().getCopySsBuilder());
        }
        return builder.build();
    }

    /**
     * 加入联盟后的系列操作
     */
    private void joinClan(long clanId, long enterTime) {
        // 取消军团推荐
        clearRecommendDataAndCancelTask();
        // set prop
        ClanInfoProp prop = getOwner().getProp().getClan();
        prop.setClanId(clanId).setEnterTime(enterTime);
    }

    private void afterJoinedClan(long clanId, String sname, long enterTime, boolean isPlayerCreated) {
        ClanInfoProp prop = getOwner().getProp().getClan();
        getOwner().getClanTechComponent().onJoinedClan(enterTime);
        for (long applyId : prop.getApplyClanList()) {
            ownerActor().tellClan(applyId, PlayerCancelApplyCmd.newBuilder().setPlayerId(getEntityId()).build());
        }
        // cancel apply
        prop.clearApplyClanList();
        ownerActor().tellBigScene(SsScenePlayer.SyncPlayerClanIdNameCmd.newBuilder().setPlayerId(getEntityId()).setClanId(clanId).setName(sname).build());
        // 重置联盟游标
        getOwner().getMailComponent().resetClanMailIndex();
        // 玩家在线才刷新联盟信息
        if (getOwner().isOnline()) {
            checkInClanAndRefreshClanInfo(enterTime);
        }
        // 判断是否首次加入军团，是发送奖励
        if (!isPlayerCreated) {
            // 自己创建军团不算加入
            checkAndSendFirstEnterClanReward();
        }
        // 创建军团的聊天item，并立即下发属性
        ChatPlayerEntity chatPlayerEntity = ownerActor().getOrLoadChatPlayerEntity();
        chatPlayerEntity.getHandleChatComponent().getChatItemOrCreate(ChatChannel.CC_CLAN, String.valueOf(clanId));
        chatPlayerEntity.getPropComponent().immediateFlushProp();
        // 发个联盟系统消息
        CommonMsg.MessageData.Builder builder = CommonMsg.MessageData.newBuilder();
        builder.setTemplateId(ChatConstants.ENTER_CLAN_MSG).getMsgParamBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(getOwner().getName()));
        long startIndex = chatPlayerEntity.getHandleChatComponent().sendSystemChat(ChatChannel.CC_CLAN, String.valueOf(clanId), builder.build());
        chatPlayerEntity.getHandleChatComponent().setStartIndex(ChatChannel.CC_CLAN, String.valueOf(clanId), startIndex);
        // 统计项更新
        getOwner().getStatisticComponent().recordSingleStatistic(JOIN_CLAN_NUM, 1);
        // 通知PlayerCard更新
        getOwner().getPlayerPropComponent().updatePlayerCardCache(true);
        getOwner().getHeroComponent().syncSimpleHeroProp2Clan();
        new PlayerJoinClanEvent(getOwner(), isPlayerCreated).dispatch();
    }

    /**
     * 移除军团加成
     */
    private void removeClanAddition() {
        // 不在联盟了，移除联盟相关加成
        for (AdditionSourceType additionSourceType : CLAN_ADDITION_SOURCES) {
            getOwner().getAddComponent().removeAdditionBySource(additionSourceType);
        }
    }

    /**
     * 移除军团buff
     */
    private void removeClanBuff() {
        List<DevBuffProp> curClanBuff = getOwner().getPlayerDevBuffComponent().getDevBuffByBuffType(DevBuffType.DBT_CLAN_BUFF);
        // 没有军团了,删掉所有军团buff
        for (DevBuffProp devBuffProp : curClanBuff) {
            getOwner().getPlayerDevBuffComponent().removeDevBuff(devBuffProp.getDevBuffId());
        }
    }

    /**
     * 告知联盟需要发送帮助id的ntf
     */
    private void tellNeedClanHelpItemIdsNtf() {
        if (!isInClan()) {
            return;
        }
        SsClanHelp.NeedClanHelpItemIdsNtfCmd.Builder cmdBuilder = SsClanHelp.NeedClanHelpItemIdsNtfCmd.newBuilder();
        cmdBuilder.setPlayerId(getPlayerId());
        getOwner().ownerActor().tellCurClan(cmdBuilder.build());
    }

    private void refreshClanRedDotInfo(CommonMsg.CheckInRedDotInfo ans) {
        // 军团红点直接删除玩家身上的信息拷贝
        for (Struct.RedDotData redDotData : ans.getRedDotMap().getDatasMap().values()) {
            CommonEnum.RedDotKey redDotKey = CommonEnum.RedDotKey.forNumber(redDotData.getKey());
            if (redDotKey == null) {
                WechatLog.error("redDotKey null! {}", ans);
                continue;
            }
            // 申请红点，如果没有权限直接忽略，注意此时staffId必须已经更新到player身上了
            if (redDotKey == RedDotKey.RDK_CLAN_APPLY &&
                    !ClanPermissionUtils.hasPermissionNoThrow(ClanOperationType.COT_AUDIT, getProp().getStaffId())) {
                continue;
            }
            // 先清除玩家身上的红点信息
            getOwner().getRedDotComponent().clearRedDot(redDotKey);
            // 逐个添加
            for (Struct.RedDotCell add : redDotData.getCells().getDatasMap().values()) {
                getOwner().getRedDotComponent().updateRedDot(redDotKey, add);
            }
        }
    }

    private void removeClanRedDotInfo() {
        // 目前仅有军团申请、战争、商店三种红点
        getOwner().getRedDotComponent().clearRedDot(RedDotKey.RDK_CLAN_APPLY);
        getOwner().getRedDotComponent().clearRedDot(RedDotKey.RDK_CLAN_WAR);
        getOwner().getRedDotComponent().clearRedDot(RedDotKey.RDK_CLAN_STORE);
        getOwner().getRedDotComponent().clearRedDot(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD);
        getOwner().getRedDotComponent().clearRedDot(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD);
    }

    private void refreshClanBasicInfo(CommonMsg.CheckInBasicInfo ans) {
        if (ans.getStaffId() == 0) {
            LOGGER.error("shouldn't get staffId 0 when checkIn refresh, not set to player");
            return;
        }
        if (ans.getStaffId() != getProp().getStaffId()) {
            LOGGER.info("refreshClanBasicInfo, staffId changed: from {} to {}", getProp().getStaffId(), ans.getStaffId());
            getProp().setStaffId(ans.getStaffId());
        }
    }

    /**
     * 开始军团推荐task
     */
    private void startClanRecommendTask() {

    }

    /**
     * 刷新军团推荐task
     */
    public void refreshClanRecommendTask() {
    }

    /**
     * 清理军团推荐的数据并取消军团推荐的task
     */
    private void clearRecommendDataAndCancelTask() {
        getProp().clearAlreadyRecommendClanIds();
        cancelClanRecommendTask();
    }

    /**
     * 因为加入军团或下线等情况，取消军团推荐task
     */
    private void cancelClanRecommendTask() {
        if (clanRecommendTask != null) {
            clanRecommendTask.cancel();
        }
        clanRecommendTask = null;
    }

    /**
     * 开始军团推荐
     */
    private void doClanRecommendOnce() {
        if (clanRecommendTask != null) {
            clanRecommendTask.cancel();
        }
        clanRecommendTask = null;
        // 将已推荐的clan带上scene，向scene请求发送推荐ntf给对应玩家
        SsSceneClan.AskForClanRecommendAsk.Builder ask = SsSceneClan.AskForClanRecommendAsk.newBuilder();
        ask.setPlayerId(getPlayerId());
        ask.addAllBetterNotRecomClanIds(getProp().getAlreadyRecommendClanIds());
        // 发给当前所在的场景，因为那边要拿到scenePlayer
        SsSceneClan.AskForClanRecommendAns ans = ownerActor().callBigScene(ask.build());
        LOGGER.info("ans is {}", ans);
        if (ans.getRecommendClanId() <= 0) {
            return;
        }
        ConstClanTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        Int64ListProp listProp = getProp().getAlreadyRecommendClanIds();
        long thisTimeRecommendClanId = ans.getRecommendClanId();
        if (listProp.contains(thisTimeRecommendClanId)) {
            return;
        } else if (listProp.size() > constTemplate.getRecommendClanBufferLength()) {
            listProp.remove(listProp.getFirst());
        }
        listProp.add(thisTimeRecommendClanId);
    }

    /**
     * 获取下次可以成为军团长的时间戳(毫秒)
     */
    private long getNextCanBeOwnerTsMsWhenDissolving() {
        ConstClanTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        long canBeLeaderCdMs = TimeUnit.DAYS.toMillis(constTemplate.getBeClanLeaderCD());
        long countDownTsMs = TimeUnit.MINUTES.toMillis(constTemplate.getDissolutionCountdown());
        return SystemClock.now() + countDownTsMs + canBeLeaderCdMs;
    }

    /**
     * 玩家在军团内是否是军团长
     *
     * @return 是军团长时返回true
     */
    public boolean isClanOwner() {
        return getProp().getStaffId() == OWNER_STAFF;
    }

    /**
     * 玩家在军团的职位id
     *
     * @return 职位id，不在军团内应该为0
     */
    public int getStaffId() {
        return getProp().getStaffId();
    }

    public void tryRebuild(PlayerClan.Player_ConstructClanStrongholdBuilding_C2S msg) throws GeminiException {
        long clanId = getOwner().getProp().getClan().getClanId();
        // 判断是否加入联盟
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 检查改建地图建筑类型
        checkMapBuildingTypeParam(msg.getType());
        // 检查是否有建设军团建筑的权限
        int staffId = getOwner().getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_REBUILD_CLAN_BUILDING, staffId);
        // 先到scene上检查是否可以重建
        SsSceneClan.VerifyCanRebuildAns ans = verifyCanRebuild(getOwner().getPlayerId(), msg.getMapBuildingId(), msg.getType(), true);
        if (!ans.getCanRebuild()) {
            // scene上检查无法建设
            throw new GeminiException(ErrorCode.MAP_BUILDING_IS_BEING_MANIPULATED);
        }
        // 检查是否有对应科技，检查是否有足够资源，检查是否仍然可以建设，扣除资源，
        SsClanTerritory.CheckRebuildClanBuildingAsk.Builder checkCall = SsClanTerritory.CheckRebuildClanBuildingAsk.newBuilder();
        checkCall.setPlayerId(getOwner().getEntityId())
                .setRebuildingType(msg.getType())
                .setEntityId(msg.getMapBuildingId())
                .setStoryId(ans.getStoryId());
        checkCall.setCardHead(getOwner().getCardHead().getCopySsBuilder());
        CheckRebuildClanBuildingAns checkRebuildClanBuildingAns = getOwner().ownerActor().callCurClan(checkCall.build());

        try {
            SsSceneClan.ConstructClanBuildingAsk.Builder buildCall = SsSceneClan.ConstructClanBuildingAsk.newBuilder();
            buildCall.setBuildingType(msg.getType()).setClanId(clanId).setPlayerId(getOwner().getEntityId())
                    .setMapBuildingId(msg.getMapBuildingId()).setStaffId(checkRebuildClanBuildingAns.getStaffId());
            getOwner().ownerActor().callBigScene(buildCall.build());
        } catch (Exception e) {
            // 建设失败，wechat告警，特别注意：此时资源已经扣除了
            WechatLog.error("construct failed, maybe need manual resource return: msg is {}, exception is {}", msg, e);
            if (e instanceof GeminiException) {
                throw e;
            }
        }
    }

    /**
     * 放置军团资源中心，协议入口
     */
    public void tryPlaceClanResBuild(PlayerSceneClan.Player_ConstructClanResBuilding_C2S msg) throws GeminiException {
        LOGGER.info("Player_ConstructClanResBuilding_C2S msg is {}", msg);
        // 检查参数是否齐全
        if (!msg.hasType() || !msg.hasP()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 是否在军团
        if (!getOwner().getPlayerClanComponent().isInClan()) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 检查type是否正确
        checkClanResBuildingParam(msg.getType());
        // 检查权限
        int staffId = getOwner().getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_REBUILD_CLAN_BUILDING, staffId);
        // 基础数据准备
        Struct.Point.Builder p = Struct.Point.newBuilder();
        p.setX(msg.getP().getX()).setY(msg.getP().getY());
        // 先到scene上检查是否可以放置（包括是否已经有资源中心，静、动态阻挡等）
        SsSceneClan.VerifyCanPlaceClanResAsk.Builder verifyCall = SsSceneClan.VerifyCanPlaceClanResAsk.newBuilder();
        verifyCall.setClanId(getOwner().getClanId()).setType(msg.getType()).setP(p);
        SsSceneClan.VerifyCanPlaceClanResAsk verifyMsg = verifyCall.build();
        getOwner().ownerActor().callSelfBigScene(verifyMsg);
        // 到clan上资源、科技检查并扣除资源
        PlaceClanResBuildAsk.Builder clanPlaceCall = PlaceClanResBuildAsk.newBuilder();
        clanPlaceCall.setType(msg.getType()).setPlayerId(getOwner().getPlayerId())
                .setCardHead(getOwner().getCardHead().getCopySsBuilder());
        PlaceClanResBuildAns ans = getOwner().ownerActor().callCurClan(clanPlaceCall.build());

        // 到scene上真正的放置建筑
        SsSceneClan.PlaceClanResBuildInSceneAsk.Builder realPlaceCall = SsSceneClan.PlaceClanResBuildInSceneAsk.newBuilder();
        realPlaceCall.setClanId(getOwner().getClanId()).setType(msg.getType()).setP(p).setEntityId(ans.getEntityId());
        getOwner().ownerActor().callBigScene(realPlaceCall.build());
    }

    /**
     * 检查军团资源建筑参数
     *
     * @param type 建筑参数类型
     * @throws GeminiException 业务异常 {@link ErrorCode#PARAM_PARAMETER_EXCEPTION}
     */
    private void checkClanResBuildingParam(MapBuildingType type) throws GeminiException {
        switch (type) {
            case MBT_CLAN_RES_OIL:
            case MBT_CLAN_RES_STEEL:
            case MBT_CLAN_RES_RARE_EARTH:
            case MBT_CLAN_RES_TIBERIUM:
                break;
            default:
                LOGGER.warn("client send wrong building type {}", type);
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    /**
     * 是否是王国建立初期
     * v
     *
     * @return 是王国建立初期返回true
     */
    private boolean isInEarlyKingdom() {
        int earlyKingdomDays = ResHolder.getResService(ConstClanKVResService.class).getTemplate().getEarlyKingdomTime();
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("isInEarlyKingdom end server is not open={}", ZoneContext.getServerOpenTsMs());
            return true;
        }
        return SystemClock.now() <= ZoneContext.getServerOpenTsMs() + TimeUnit.DAYS.toMillis(earlyKingdomDays);
    }

    private void removeClanBasicInfo() {
        getProp().setStaffId(0);
    }

    public void checkPlayerCanInvite() throws GeminiException {
        if (!getOwner().getPlayerClanComponent().isInClan()) {
            // 自己都不在军团邀请谁呢？
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        // 看看有没有邀请权限
        int staffId = getOwner().getPlayerClanComponent().getStaffId();
        ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_INVITE, staffId);
    }

    public void checkInvitationExist(long clanId) throws GeminiException {
        SsClanBase.CheckInviteExistAsk.Builder ask = SsClanBase.CheckInviteExistAsk.newBuilder();
        ask.setInvitedPlayerId(getOwner().getPlayerId());
        ownerActor().callTargetClan(clanId, ask.build());
    }
}

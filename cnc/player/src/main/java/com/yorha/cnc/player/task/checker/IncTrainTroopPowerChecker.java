package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.helper.CheckerHelper;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;


/**
 * 训练提升x点部队战力（仅计入通过兵营进行的相关行为提升的战力，如“训练”、“升级”）
 * param1: 累计提升战力
 *
 * <AUTHOR>
 */
public class IncTrainTroopPowerChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        final int require = taskParams.get(0);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE: {
                if (event instanceof PlayerPowerIncreaseEvent) {
                    PlayerPowerIncreaseEvent e = (PlayerPowerIncreaseEvent) event;
                    if (e.getPowerReason() == CommonEnum.PowerType.PT_SOLDIER) {
                        if (CheckerHelper.isUpdatePowerByTrainOrLevelUp(e.getSoldierNumChangeReason())) {
                            prop.setProcess((int) Math.min(require, prop.getProcess() + e.getPower()));
                            break;
                        }
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= require;
    }
}
package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * 加战略排行榜积分
 *
 * <AUTHOR>
 */
public class AddZlcbScore implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        if (!args.containsKey("rankType")) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (!args.containsKey("score")) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int score = Integer.parseInt(args.get("score"));
        int rankId = Integer.parseInt(args.get("rankType"));
        actor.getOrLoadEntity().getPlayerRankComponent().updateZoneRanking(rankId, score);
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "AddZlcbScore rankType={value} score={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_RANK;
    }
}

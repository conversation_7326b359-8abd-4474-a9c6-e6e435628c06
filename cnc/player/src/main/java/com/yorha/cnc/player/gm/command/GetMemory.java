package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.utils.MonitorUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
public class GetMemory implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GetMemory.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int minShowSize = MonitorConstant.ENTITY_MIN_SHOW_BYTE_SIZE;
        if (args.containsKey("minShowSize")) {
            minShowSize = Integer.parseInt(args.get("minShowSize"));
        }
        LOGGER.error(MonitorUtils.printPropMemory(actor.getEntity().getProp().getCopyDbBuilder().build(), minShowSize));
    }
}

package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.task.BuildClanFlagEvent;
import com.yorha.cnc.player.event.task.ClanTerritoryLvChangeEvent;
import com.yorha.cnc.player.event.task.PlayerOccupyPointEvent;
import com.yorha.common.actor.PlayerClanService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.rank.RankConstant;
import com.yorha.game.gen.prop.ClanScoreItemProp;
import com.yorha.game.gen.prop.Int32ClanScoreItemMapProp;
import com.yorha.proto.CommonEnum.ClanScoreCategory;
import com.yorha.proto.SsClanRank.UpdateClanRankingCmd;
import com.yorha.proto.SsPlayerClan.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.common.enums.statistic.StatisticEnum.OCCUPY_POINT_NUM;
import static com.yorha.common.enums.statistic.StatisticEnum.OCCUPY_POINT_TIME;

/**
 * <AUTHOR>
 */
public class PlayerClanServiceImpl implements PlayerClanService {

    private static final Logger LOGGER = LogManager.getLogger(PlayerClanServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerClanServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    @Override
    public void handleClanApplyResultAsk(ClanApplyResultAsk ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        playerEntity.getPlayerClanComponent().onNotifyClanApplyResult(
                ask.getClanId(),
                ask.getIsAllow(),
                ask.getClanSname(),
                ask.getOperatorId()
        );
    }

    @Override
    public void handleOnNtfClanKickOffResultCmd(OnNtfClanKickOffResultCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        final long clanId = ask.getClanId();
        if (clanId != playerEntity.getProp().getClan().getClanId()) {
            LOGGER.warn("onNtfClanKickOffResult player clan={}, kickMsgFromClanId={}", playerEntity.getProp().getClan(), clanId);
            return;
        }
        playerEntity.getPlayerClanComponent().onLeaveClan(ask.getKickOffTsMs());
    }

    @Override
    public void handleOnClanAdditionUpdateCmd(OnClanAdditionUpdateCmd ask) {
        PlayerEntity playerEntity = playerActor.getEntityOrNull();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        final long clanId = ask.getClanId();
        if (clanId != playerEntity.getProp().getClan().getClanId()) {
            LOGGER.warn("onClanAdditionUpdate player clan={}, req clanId={}", playerEntity.getProp().getClan(), clanId);
            return;
        }
        playerEntity.getPlayerClanComponent().onClanAdditionUpdate(ask);
    }

    @Override
    public void handleOnClanDevBuffUpdateCmd(OnClanDevBuffUpdateCmd ask) {
        PlayerEntity playerEntity = playerActor.getEntityOrNull();
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        final long clanId = ask.getClanId();
        if (clanId != playerEntity.getProp().getClan().getClanId()) {
            LOGGER.warn("onClanDevBuffUpdate player clan={}, req clanId={}", playerEntity.getProp().getClan(), clanId);
            return;
        }
        playerEntity.getPlayerClanComponent().onClanDevBuffUpdate(ask);
    }

    @Override
    public void handleOnAddClanScoreCmd(OnAddClanScoreCmd cmd) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            return;
        }
        // 只做基本的参数检查
        if (!cmd.hasScoreType() || !cmd.hasAddValue()) {
            LOGGER.error("OnAddClanScoreCmd param error, scoreType={}, addValue={}", cmd.getScoreType(), cmd.getAddValue());
            return;
        }
        // 参数检查，参数错误不进行
        if (cmd.getIsOccupyAddScore() && cmd.getIsBuildAddScore()) {
            LOGGER.error("OnAddClanScoreCmd param error, isOccupyAddScore and isBuildAddScore all true");
            return;
        }
        // 通行证事件：建造完成联盟旗帜
        if (cmd.getIsFinishBuild()) {
            new BuildClanFlagEvent(playerEntity).dispatch();
        }
        ClanScoreCategory scoreType = cmd.getScoreType();
        int scoreTypeId = scoreType.getNumber();
        Int32ClanScoreItemMapProp scoreInfo = playerEntity.getProp().getClan().getScoreInfo();
        if (!scoreInfo.containsKey(scoreTypeId)) {
            scoreInfo.addEmptyValue(scoreTypeId);
        }
        ClanScoreItemProp prop = scoreInfo.get(scoreTypeId);
        int dayLimit = PlayerAddCalc.getClanScoreDailyLimit(playerEntity, scoreTypeId);
        long scoreAdd = cmd.getAddValue();
        if (prop.getTodayAdd() + scoreAdd > dayLimit) {
            scoreAdd = dayLimit - prop.getTodayAdd();
        }
        boolean isOccupyOrBuild = cmd.getIsOccupyAddScore() || cmd.getIsBuildAddScore();
        if (scoreAdd > 0) {
            LOGGER.info("reach daily limit for {} add {}, set real add {}", scoreType, cmd.getAddValue(), scoreAdd);
            prop.setScore(prop.getScore() + scoreAdd).setTodayAdd(prop.getTodayAdd() + scoreAdd);
            // 增加军团积分，由底层方法打日志和qlog
            if (isOccupyOrBuild) {
                // 占领或建设，需要转换为qlog需要的军团积分类型
                playerEntity.getPlayerClanComponent().addTotalClanScore(
                        playerEntity.getQlogComponent().getClanScoreTypeWhenOccupyOrBuild(cmd.getIsOccupyAddScore()),
                        scoreAdd);
            } else {
                playerEntity.getPlayerClanComponent().addTotalClanScore(scoreType, scoreAdd);
            }
        }
        // 占领或建设需要后处理
        if (isOccupyOrBuild) {
            postProcessOnAddClanScoreCmd(playerEntity, cmd);
        }
    }

    private void postProcessOnAddClanScoreCmd(PlayerEntity playerEntity, OnAddClanScoreCmd cmd) {
        int scoreTypeId = cmd.getScoreType().getNumber();
        // 后处理逻辑仅在占领或建设时触发
        if (scoreTypeId != ClanScoreCategory.CSC_OCCUPY_AND_BUILD.getNumber()) {
            return;
        }
        // isBuildAddScore 和 isOccupyAddScore 不能同时为true
        if (cmd.getIsBuildAddScore()) {
            if (!playerEntity.getPlayerClanComponent().isInClan()) {
                LOGGER.info("OnAddClanScoreCmd player not in clan");
                return;
            }
            if (cmd.getScoreUseInterval() <= 0) {
                LOGGER.warn("OnAddClanScoreCmd scoreUseInterval={}, no need to update", cmd.getScoreUseInterval());
                return;
            }
            // 联盟建设个人时长排行榜数据同步
            UpdateClanRankingCmd.Builder updateRankingCmd = UpdateClanRankingCmd.newBuilder();
            updateRankingCmd.setIncrease(true);
            updateRankingCmd.setScore(cmd.getScoreUseInterval());
            updateRankingCmd.setRankId(RankConstant.CLAN_PLAYER_BUILD_RANK);
            updateRankingCmd.setMemberId(playerEntity.getPlayerId());
            playerActor.tellCurClan(updateRankingCmd.build());
        } else if (cmd.getIsOccupyAddScore()) {
            // 联盟加分类型为占领并且占领完成时玩家在据点中视为成功占领据点一次
            if (cmd.getInBuildingWhenOccupy()) {
                playerEntity.getStatisticComponent().recordSingleStatistic(OCCUPY_POINT_NUM, 1);
            }
            playerEntity.getStatisticComponent().recordSingleStatistic(OCCUPY_POINT_TIME, cmd.getScoreUseInterval());
            new PlayerOccupyPointEvent(playerEntity, cmd.getScoreUseInterval()).dispatch();
        }
    }

    @Override
    public void handleOnAddClanPowerResourceCmd(OnAddClanPowerResourceCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            return;
        }
        playerEntity.getPlayerClanComponent().onAddClanPowerResources(ask.getResourceMap());
    }

    @Override
    public void handleOnClanHelpHappenAsk(OnClanHelpHappenAsk ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            return;
        }
        long reduceTime = playerEntity.getPlayerQueueTaskComponent().clanHelpQueueSpeed(ask.getQueueId());
        OnClanHelpHappenAns.Builder ans = OnClanHelpHappenAns.newBuilder();
        ans.setReduceTime(reduceTime).setIsSuccessReduce(reduceTime != 0);
        playerActor.answer(ans.build());
    }

    @Override
    public void handleOnPlayerNeedClanInfoChangeCmd(OnPlayerNeedClanInfoChangeCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            LOGGER.error("sync clanInfo change failed");
            return;
        }
        playerEntity.getPlayerClanComponent().onStaffIdChange(ask.getStaffId());
        if (ask.hasRedDotData()) {
            playerEntity.getRedDotComponent().updateRedDot(ask.getRedDotData());
        }
    }

    @Override
    public void handleOnClanTerritoryLvChangeCmd(OnClanTerritoryLvChangeCmd ask) {
        PlayerEntity playerEntity = playerActor.getOrLoadEntity();
        if (playerEntity == null) {
            LOGGER.error("sync clan territory lv change failed");
            return;
        }
        new ClanTerritoryLvChangeEvent(playerEntity, ask.getNewTerritoryLv()).dispatch();
    }

}

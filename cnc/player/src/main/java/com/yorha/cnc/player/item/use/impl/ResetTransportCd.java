package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

/**
 * 重置运输机CD
 *
 * <AUTHOR>
 */
public class ResetTransportCd extends AbstractUsableItem {

    public ResetTransportCd(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (params.getTransportId() == 0 && playerEntity.getPlaneComponent().getMaxCdTransport() <= 0) {
            // 无CD中运输机
            throw new GeminiException(ErrorCode.PLANE_NO_CD_TRANSPORT_PLANE);
        }
        if (params.getTransportId() > 0 && !playerEntity.getPlaneComponent().hasCdTransportPlane(params.getTransportId())) {
            // 该运输机无需重置CD
            throw new GeminiException(ErrorCode.PLANE_CURRENT_TRANSPORT_PLANE_NO_CD);
        }

    }


    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (params.getTransportId() == 0) {
            playerEntity.getPlaneComponent().resetTransportCd(playerEntity.getPlaneComponent().getMaxCdTransport());
            return true;
        }
        if (playerEntity.getPlaneComponent().hasCdTransportPlane(params.getTransportId())) {
            playerEntity.getPlaneComponent().resetTransportCd(params.getTransportId());
            return true;
        }
        return false;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

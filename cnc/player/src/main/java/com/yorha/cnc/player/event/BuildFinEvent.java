package com.yorha.cnc.player.event;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.proto.CommonEnum;

/**
 * 建筑完成事件
 */
public class BuildFinEvent extends PlayerEvent {

    private final CommonEnum.CityBuildType type;

    private final int level;

    public BuildFinEvent(PlayerEntity entity, CommonEnum.CityBuildType type, int level) {
        super(entity);
        this.type = type;
        this.level = level;
    }

    public CommonEnum.CityBuildType getType() {
        return type;
    }

    public int getLevel() {
        return level;
    }
}

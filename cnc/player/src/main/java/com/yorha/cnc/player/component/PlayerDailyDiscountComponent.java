package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.dailyDiscount.DailyDiscountService;
import com.yorha.common.server.ZoneContext;
import com.yorha.game.gen.prop.Int32SetProp;
import com.yorha.game.gen.prop.PlayerDailyGoodsModelProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.HeroRhTemplate;

import java.util.List;

public class PlayerDailyDiscountComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerDailyDiscountComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerDailyDiscountComponent::dailyRefresh);
    }

    public PlayerDailyDiscountComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            // 创号后设置默认英雄
            this.getDailyGoodsProp().setHeroId(ResHolder.getResService(DailyDiscountService.class).getPrimaryHeroId());
        }
    }

    /**
     * 获取每日特惠免费奖励
     *
     * @return AssetPackage 道具包
     */
    public AssetPackage takeDailyDiscountReward() {
        if (hasGotDailyDiscountReward()) {
            LOGGER.info("takeDailyDiscountReward {} already taken dailyDiscount reward", getOwner());
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_REWARD_ALREADY_TAKEN);
        }

        List<IntPairType> freeRewardList = ResHolder.getResService(DailyDiscountService.class).getDailyFreeReward();
        AssetPackage reward = AssetPackage.builder().plusItems(freeRewardList).build();
        this.ownerActor().getEntity().getAssetComponent().give(reward, CommonEnum.Reason.ICR_DAILY_DISCOUNT_FREE_REWARD);
        // 标记已领取
        this.getDailyGoodsProp().setRewardTaken(true);
        return reward;
    }

    /**
     * 获取超值每日特惠免费奖励
     *
     * @return AssetPackage 道具包
     */
    public AssetPackage takeSuperDailyDiscountReward() {
        if (hasGotSuperDailyDiscountReward()) {
            LOGGER.info("takeSuperDailyDiscountReward {} already taken superDailyDiscount reward", getOwner());
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_REWARD_ALREADY_TAKEN);
        }

        List<IntPairType> freeRewardList = ResHolder.getResService(DailyDiscountService.class).getDailyFreeReward();
        AssetPackage reward = AssetPackage.builder().plusItems(freeRewardList).build();
        this.ownerActor().getEntity().getAssetComponent().give(reward, CommonEnum.Reason.ICR_DAILY_DISCOUNT_PREMIUM_FREE_REWARD);
        // 标记已领取
        this.getDailyGoodsProp().setSuperRewardTaken(true);
        return reward;
    }


    /**
     * 切换每日特惠中选中的英雄
     *
     * @param heroId 英雄id
     */
    public void switchHero(int heroId) {
        // 不可购买，返回错误码
        if (!this.isBuyAbleHero(heroId)) {
            LOGGER.info("{} try to switch an unlocked hero {}", getOwner(), heroId);
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_HERO_UNLOCKED);
        }
        LOGGER.info("switchHero {} switch to heroId={}", getOwner(), heroId);
        this.getDailyGoodsProp().setHeroId(heroId);
    }

    /**
     * 礼包购买前校验（供DailyDiscountGoods使用）
     *
     * @param goodsId 礼包id
     */
    public void checkBuyDailyDiscountBox(int goodsId) {
        int heroId = this.getDailyDiscountHeroId(goodsId);
        if (!this.isBuyAbleHero(heroId)) {
            LOGGER.info("checkBuyDailyDiscountBox try to buy an unlocked hero {} ", heroId);
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_HERO_CANT_BUY);
        }

        int goodsLevel = this.getGoodsLevel(goodsId);
        if (this.hasBoughtGoodLevel(goodsLevel)) {
            LOGGER.info("checkBuyDailyDiscountBox already bought level={} dailyDiscount reward", goodsLevel);
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_LEVEL_BOUGHT);
        }

        if (goodsLevel == ResHolder.getResService(DailyDiscountService.class).getCollectionLevel()) {
            checkBuyCollection();
        } else {
            checkBuySinglePack();
        }
        LOGGER.info("checkBuyDailyDiscountBox {} can buy dailyDiscount goodsId={}, level={}", getOwner(), goodsId, goodsLevel);
    }


    /**
     * 礼包购买前校验（供SuperDailyDiscountGoods使用）
     *
     * @param goodsId 礼包id
     */
    public void checkBuySuperDailyDiscountBox(int goodsId) {
        // 买完普通每日特惠才可以买超值每日特惠
        checkDailyDiscountBoxAllBought();

        int heroId = this.getSuperDailyDiscountHeroId(goodsId);
        if (!this.isBuyAbleHero(heroId)) {
            LOGGER.info("checkBuySuperDailyDiscountBox try to buy an unlocked hero {} ", heroId);
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_HERO_CANT_BUY);
        }

        int superGoodsLevel = this.getSuperGoodsLevel(goodsId);
        if (this.hasBoughtSuperGoodLevel(superGoodsLevel)) {
            LOGGER.info("checkBuySuperDailyDiscountBox already bought level={} superDailyDiscount reward", superGoodsLevel);
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_LEVEL_BOUGHT);
        }

        if (superGoodsLevel == ResHolder.getResService(DailyDiscountService.class).getSuperCollectionLevel()) {
            checkBuySuperCollection();
        } else {
            checkBuySuperSinglePack();
        }
        LOGGER.info("checkBuySuperDailyDiscountBox {} can buy dailyDiscount goodsId={}, level={}", getOwner(), goodsId, superGoodsLevel);
    }

    /**
     * 超值每日特惠购买前校验：所有每日特惠礼包均购买完毕
     */
    private void checkDailyDiscountBoxAllBought() throws GeminiException {
        if (this.hasBoughtGoodLevel(ResHolder.getResService(DailyDiscountService.class).getCollectionLevel())) {
            return;
        }
        if (this.getDailyGoodsProp().getBoughtGoodLevelSize() != ResHolder.getResService(DailyDiscountService.class).getPackLevelNum()) {
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_PACK_NOT_BOUGHT_DONE);
        }
    }

    /**
     * 校验礼包合集是否可买
     */
    private void checkBuyCollection() {
        Int32SetProp boughtGoodLevel = this.getDailyGoodsProp().getBoughtGoodLevel();
        // 购买合集条件：未买过合集&未买过其他礼包 == 都没买过
        if (boughtGoodLevel.size() > 0) {
            LOGGER.info("checkBuyCollection already bought single level, can't buy collection");
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_CANT_BUY_COLLECTION);
        }
    }

    /**
     * 校验超级礼包合集是否可买
     */
    private void checkBuySuperCollection() {
        Int32SetProp boughtSuperGoodLevel = this.getDailyGoodsProp().getBoughtSuperGoodLevel();
        // 购买合集条件：未买过合集&未买过其他礼包 == 都没买过
        if (boughtSuperGoodLevel.size() > 0) {
            LOGGER.info("checkBuySuperCollection already bought single level, can't buy collection");
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_CANT_BUY_COLLECTION);
        }
    }

    /**
     * 校验礼包单级是否可买
     */
    private void checkBuySinglePack() {
        Int32SetProp boughtGoodLevel = this.getDailyGoodsProp().getBoughtGoodLevel();
        int collectionLevel = ResHolder.getResService(DailyDiscountService.class).getCollectionLevel();
        if (boughtGoodLevel.contains(collectionLevel)) {
            LOGGER.info("checkBuySinglePack {} already bought collection, can't buy single level", getOwner());
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_CANT_BUT_LEVEL);
        }
    }

    /**
     * 校验超级礼包单级是否可买
     */
    private void checkBuySuperSinglePack() {
        Int32SetProp boughtSuperGoodLevel = this.getDailyGoodsProp().getBoughtSuperGoodLevel();
        int superCollectionLevel = ResHolder.getResService(DailyDiscountService.class).getSuperCollectionLevel();
        if (boughtSuperGoodLevel.contains(superCollectionLevel)) {
            LOGGER.info("checkBuySuperSinglePack {} already bought collection, can't buy single level", getOwner());
            throw new GeminiException(ErrorCode.DAILY_DISCOUNT_CANT_BUT_LEVEL);
        }
    }

    /**
     * 礼包购买后逻辑（供DailyDiscountGoods使用）
     *
     * @param goodsId 礼包id
     */
    public void afterBuyDailyDiscountBox(int goodsId) {
        int goodsLevel = getGoodsLevel(goodsId);
        this.getDailyGoodsProp().getBoughtGoodLevel().add(goodsLevel);
        LOGGER.info("afterBuyDailyDiscountBox {} just bought goodsId={}, level = {}", getOwner(), goodsId, goodsLevel);
    }

    /**
     * 超值每日特惠礼包购买后逻辑（供SuperDailyDiscountGoods使用）
     *
     * @param goodsId 礼包id
     */
    public void afterBuySuperDailyDiscountBox(int goodsId) {
        int superGoodsLevel = getSuperGoodsLevel(goodsId);
        this.getDailyGoodsProp().getBoughtSuperGoodLevel().add(superGoodsLevel);
        LOGGER.info("afterBuySuperDailyDiscountBox {} just bought goodsId={}, level = {}", getOwner(), goodsId, superGoodsLevel);
    }

    /**
     * 获取每日折扣prop
     *
     * @return PlayerDailyGoodsModelProp
     */
    private PlayerDailyGoodsModelProp getDailyGoodsProp() {
        return this.ownerActor().getEntity().getProp().getDailyGoodsModel();
    }

    /**
     * @param heroId 英雄id
     * @return boolean true==英雄可购买
     */
    private boolean isBuyAbleHero(int heroId) {
        if (ResHolder.getInstance().findValueFromMap(HeroRhTemplate.class, heroId) == null) {
            throw new GeminiException(ErrorCode.HERO_HERO_NULL);
        }
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("PlayerDailyDiscountComponent isBuyAbleHero, server is not open={} heroId={}", ZoneContext.getServerOpenTsMs(), heroId);
            return false;
        }
        return ResHolder.getResService(DailyDiscountService.class).isBuyAbleHero(heroId, ZoneContext.getServerOpenTsMs());
    }

    /**
     * 根据礼包id获取对应礼包等级
     *
     * @param goodsId 礼包id
     * @return 每日折扣礼包对应等级
     */
    private int getGoodsLevel(int goodsId) {
        int goodLevel = ResHolder.getResService(DailyDiscountService.class).tryGetGoodsLevel(goodsId);
        LOGGER.info("getGoodsLevel goodsId={}, level={}", goodsId, goodLevel);
        if (goodLevel < 0) {
            throw new GeminiException(ErrorCode.ILLEGAL_GOODS_ID);
        }
        return goodLevel;
    }

    /**
     * 根据礼包id获取对应超级礼包等级
     *
     * @param goodsId 礼包id
     * @return 每日折扣礼包对应等级
     */
    private int getSuperGoodsLevel(int goodsId) {
        int goodLevel = ResHolder.getResService(DailyDiscountService.class).tryGetSuperGoodsLevel(goodsId);
        LOGGER.info("getSuperGoodsLevel goodsId={}, level={}", goodsId, goodLevel);
        if (goodLevel < 0) {
            throw new GeminiException(ErrorCode.ILLEGAL_GOODS_ID);
        }
        return goodLevel;
    }

    /**
     * 根据每日特惠礼包id获取对应英雄id
     *
     * @param goodsId 礼包id
     * @return 每日折扣礼包对应英雄
     */
    private int getDailyDiscountHeroId(int goodsId) {
        int heroId = ResHolder.getResService(DailyDiscountService.class).tryGetHeroId(goodsId);
        LOGGER.info("getHeroId goodsId={}, heroId={}", goodsId, heroId);
        if (heroId < 0) {
            throw new GeminiException(ErrorCode.HERO_HERO_NULL);
        }
        return heroId;
    }

    /**
     * 根据超值每日特惠礼包id获取对应英雄id
     *
     * @param goodsId 礼包id
     * @return 每日折扣礼包对应英雄
     */
    private int getSuperDailyDiscountHeroId(int goodsId) {
        int heroId = ResHolder.getResService(DailyDiscountService.class).tryGetSuperHeroId(goodsId);
        LOGGER.info("getSuperDailyDiscountHeroId goodsId={}, heroId={}", goodsId, heroId);
        if (heroId < 0) {
            throw new GeminiException(ErrorCode.HERO_HERO_NULL);
        }
        return heroId;
    }

    /**
     * 已购买对应等级礼包
     *
     * @param goodsLevel 礼包等级
     * @return boolean
     */
    private boolean hasBoughtGoodLevel(int goodsLevel) {
        return this.getDailyGoodsProp().getBoughtGoodLevel().contains(goodsLevel);
    }

    /**
     * 已购买对应等级超级礼包
     *
     * @param goodsLevel 礼包等级
     * @return boolean
     */
    private boolean hasBoughtSuperGoodLevel(int goodsLevel) {
        return this.getDailyGoodsProp().getBoughtSuperGoodLevel().contains(goodsLevel);
    }

    /**
     * 是否已领取过每日特惠免费奖励
     *
     * @return boolean
     */
    private boolean hasGotDailyDiscountReward() {
        return getDailyGoodsProp().getRewardTaken();
    }

    /**
     * 是否已领取过超值每日特惠免费奖励
     *
     * @return boolean
     */
    private boolean hasGotSuperDailyDiscountReward() {
        return getDailyGoodsProp().getSuperRewardTaken();
    }

    /**
     * 日刷，清空是否已领过免费奖励&每日购买记录
     *
     * @param dayRefreshEvent 日刷事件
     */
    private static void dailyRefresh(PlayerDayRefreshEvent dayRefreshEvent) {
        LOGGER.info("dailyRefresh refresh dailyDiscount info");
        PlayerEntity playerEntity = dayRefreshEvent.getPlayer();
        PlayerDailyGoodsModelProp playerDailyGoodsProp = playerEntity.getProp().getDailyGoodsModel();
        // 普通每日特惠数据清除
        playerDailyGoodsProp.setRewardTaken(false);
        playerDailyGoodsProp.clearBoughtGoodLevel();

        // 超值每日特惠数据清除
        playerDailyGoodsProp.setSuperRewardTaken(false);
        playerDailyGoodsProp.clearBoughtSuperGoodLevel();
    }

}

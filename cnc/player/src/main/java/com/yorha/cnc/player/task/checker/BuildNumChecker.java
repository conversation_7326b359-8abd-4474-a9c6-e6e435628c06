package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.BuildFullOpenEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerBuildCreateEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.CONSTRUCT_INNER_BUILD_NUM_TOTAL;

/**
 * 建造xxx建筑
 * param1: buildId
 * param2: num
 *
 * <AUTHOR>
 */
public class BuildNum<PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerInnerBuildCreateEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName(), BuildFullOpenEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer buildType = taskParams.get(0);
        Integer maxNum = taskParams.get(1);
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int num = (int) entity.getStatisticComponent().getSecondStatistic(CONSTRUCT_INNER_BUILD_NUM_TOTAL, buildType);
                prop.setProcess(Math.min(num, maxNum));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= maxNum;
    }
}

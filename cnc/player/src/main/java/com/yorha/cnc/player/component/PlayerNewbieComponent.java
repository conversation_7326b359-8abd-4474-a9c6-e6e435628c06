package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerNewbieOverEvent;
import com.yorha.cnc.player.event.PlayerNewbieStepEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.HeroUnlockReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.newbie.NewBieTemplateService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClientStageInfoProp;
import com.yorha.game.gen.prop.PlayerNewbieModelProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 新手啦
 */
public class PlayerNewbieComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerNewbieComponent.class);

    public PlayerNewbieComponent(PlayerEntity owner) {
        super(owner);
    }

    public boolean isNewbie() {
        return getOwner().getProp().getNewbieModel().getIsNewbie();
    }

    /**
     * 因为时序问题不能使用注册接口，直接显式调用
     */
    public void setNewbie(boolean debugStartNewbie) {
        PlayerNewbieModelProp prop = getOwner().getProp().getNewbieModel();
        prop.setIsNewbie(debugStartNewbie);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (!isRegister) {
            return;
        }
        if (!getProp().getIsNewbie()) {
            // 注册完就不是新手了，说明玩家没有开启新手模式，这个时候抛一个新手结束事件，激活关联模块
            onNewbieOver(CommonEnum.NewbieOverReason.NSR_CS_GM);
        }
    }

    public void markNewbieStep(PlayerCommon.Player_MarkNewbieStep_C2S msg) {
        PlayerNewbieModelProp newbieModel = getOwner().getProp().getNewbieModel();
        if (newbieModel.getIsNewbie()) {
            // 非新手不处理了

            newbieModel.setStepIndex(msg.getStepIndex());
            if (msg.hasStepNickname()) {
                newbieModel.setStepNickname(msg.getStepNickname());
            }
            // 标记新手期结束
            updateNewbieMark(msg.getMarkNewbieOver(), msg.getOverReason());
            //触发引导附带的内城事件
            new PlayerNewbieStepEvent(getOwner(), msg.getStepIndex()).dispatch();
            //getOwner().getInnerBuildRhComponent().checkTryTutorial(msg.getStepIndex());
            
            getOwner().getQlogComponent().sendNewbieQlog(msg.getStepIndex());
        } else {
            LOGGER.warn("{} newbieOver but recv cs mark {}", getOwner(), msg);
        }
    }

    public void gmSkipNewbie() {
        updateNewbieMark(true, CommonEnum.NewbieOverReason.NSR_SVR_GM);
    }

    private void updateNewbieMark(boolean isNewbieOver, CommonEnum.NewbieOverReason reason) {
        PlayerNewbieModelProp newbieModel = getOwner().getProp().getNewbieModel();
        final boolean isNewbieBefore = newbieModel.getIsNewbie();
        newbieModel.setIsNewbie(!isNewbieOver);
        if (isNewbieBefore && isNewbieOver) {
            onNewbieOver(reason);
        }
    }

    private void onNewbieOver(CommonEnum.NewbieOverReason reason) {
        LOGGER.info("{} newbieOver reason={}", getOwner(), reason);
        new PlayerNewbieOverEvent(getOwner()).dispatch();
        SsScenePlayer.MarkNewbieOverAsk.Builder builder = SsScenePlayer.MarkNewbieOverAsk.newBuilder().setPlayerId(getPlayerId());
        boolean needCheck = needCheck(reason);
        if (needCheck) {
            checkNewbieData();
            builder.setNewbieCheckFlag(true);
        }
        ownerActor().callBigScene(builder.build());
    }

    private void checkNewbieData() {
        List<String> errorStr = new ArrayList<>();
        for (NewbieCheckTemplate newbieCheckTemplate : ResHolder.getResService(NewBieTemplateService.class).getAllNewbieCheck()) {
            String error = newbieDataCheckByConfig(newbieCheckTemplate);
            if (error != null && error.length() > 0) {
                errorStr.add(error);
            }
        }
        if (!errorStr.isEmpty()) {
            WechatLog.error(new ResourceException("新手数据异常. 异常信息：{}", errorStr));
        }
    }


    /**
     * 返回检验结果
     *
     * @return null代表通过 任何信息都代表失败
     */
    private String newbieDataCheckByConfig(NewbieCheckTemplate newbieCheckTemplate) {
        switch (newbieCheckTemplate.getType()) {
            case NCT_RESOURCE: {
                CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(newbieCheckTemplate.getTypeId());
                //
                if (currencyType == null) {
                    LOGGER.error("newbie check error, resource:{}", newbieCheckTemplate.getTypeId());
                    return null;
                }
                long count = getOwner().getPurseComponent().count(currencyType);
                if (count != newbieCheckTemplate.getValue()) {
                    return StringUtils.format("资源校验失败.资源={} 期望={} 实际={}", currencyType, newbieCheckTemplate.getValue(), count);
                }
                return null;
            }

            case NCT_BUILD: {
                CommonEnum.InnerCityBuildType innerCityBuildType = CommonEnum.InnerCityBuildType.forNumber(newbieCheckTemplate.getTypeId());
                if (innerCityBuildType == null) {
                    LOGGER.error("newbie check error, build:{}", newbieCheckTemplate.getTypeId());
                    return null;
                }
                return null;
            }
            case NCT_SOLDIER:// 士兵在scene上校验，这里直接过
            default: {
                return null;
            }
        }
    }


    public int getStepIndex() {
        return getOwner().getProp().getNewbieModel().getStepIndex();
    }

    public PlayerCommon.Player_GiveNewbieHero_S2C giveNewbieHero() {
        final int freeHeroId = ResHolder.getConsts(ConstNewbieSvrTemplate.class).getFreeHeroId();
        PlayerCommon.Player_GiveNewbieHero_S2C response = PlayerCommon.Player_GiveNewbieHero_S2C.newBuilder().setHeroId(freeHeroId).build();
        HeroRhTemplate freeHeroTemplate = ResHolder.findTemplate(HeroRhTemplate.class, freeHeroId);
        // freeHeroId可能会改成0
        if (freeHeroTemplate == null || getOwner().getHeroComponent().hasHeroProp(freeHeroId)) {
            return response;
        }
        getOwner().getHeroComponent().initHero(freeHeroId, HeroUnlockReason.NEWBIE_FREE_GET);
        return response;
    }

    public void setMapId(int bornRegionId) {
        getOwner().getProp().getNewbieModel().setMapId(bornRegionId);
    }

    public PlayerNewbieModelProp getProp() {
        return getOwner().getProp().getNewbieModel();
    }

    public void updateClientStage(int groupId, int stage, boolean flag) {
        if (groupId <= 0 || stage <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (!ResHolder.getResService(NewBieTemplateService.class).hasTemplate(groupId, stage)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        addClientStage(groupId, stage, flag, "client");
    }

    public void checkNewbieGroup(TaskMainTemplate taskMainTemplate, CommonEnum.TaskStatus taskStatus) {
        int addId;
        int removeId;
        switch (taskStatus) {
            case AT_ACCEPT: {
                addId = taskMainTemplate.getTaskGetGuide();
                removeId = taskMainTemplate.getTaskGetDeleteGuide();
                break;
            }
            case AT_NOT_REWARD: {
                addId = taskMainTemplate.getTaskCompleteGuide();
                removeId = taskMainTemplate.getTaskCompleteDeleteGuide();
                break;
            }
            case AT_REWARD: {
                addId = taskMainTemplate.getTaskDoneGuide();
                removeId = taskMainTemplate.getTaskDoneDeleteGuide();
                break;
            }
            default: {
                return;
            }
        }
        if (addId > 0) {
            addClientStage(addId, 0, false, "serverTaskReceive");
        }
        if (removeId > 0) {
            addClientStage(removeId, 0, true, "serverTaskRemove");
        }
    }

    public void addClientStage(int groupId, int stage, boolean flag, String reason) {
        LOGGER.info("newbie client stage update start, groupId={}, stage={}, flag={} reason={}", groupId, stage, flag, reason);
        ClientStageInfoProp clientStageInfoProp = getProp().getStageInfo().get(groupId);
        if (clientStageInfoProp == null) {
            clientStageInfoProp = getProp().getStageInfo().addEmptyValue(groupId);
        }
        if (!clientStageInfoProp.getFlag()) {
            clientStageInfoProp.setStageId(stage).setFlag(flag);
        } else {
            LOGGER.info("newbie client stage is over, groupId={} reason={}", groupId, reason);
        }
        LOGGER.info("newbie client stage update end, groupId={}, stage={}, flag={} reason={}", clientStageInfoProp.getGroupId(), clientStageInfoProp.getStageId(), clientStageInfoProp.getFlag(), reason);
    }

    private static boolean needCheck(CommonEnum.NewbieOverReason reason) {
        switch (reason) {
            case NSR_CS_GM:
            case NSR_SVR_GM: {
                return false;
            }
            case NSR_NATURAL:
            case NSR_FORCE_SKIP: {
                return true;
            }
            default: {
                WechatLog.error("unknow reason:{}, newbie over", reason);
                return false;
            }
        }
    }
}

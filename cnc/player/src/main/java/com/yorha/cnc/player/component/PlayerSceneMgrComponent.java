package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.notification.NotificationTokenHelper;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DungeonSceneRecordProp;
import com.yorha.game.gen.prop.PlayerSceneProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DungeonType;
import com.yorha.proto.SsSceneDungeon;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PlayerSceneMgrComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSceneMgrComponent.class);
    private IActorRef dungeonRef;

    public PlayerSceneMgrComponent(PlayerEntity owner) {
        super(owner);
    }

    public IActorRef getDungeonRef() {
        return dungeonRef;
    }

    /**
     * 玩家视野是否在副本中
     */
    public boolean isInDungeon() {
        return getProp().getDungeonType() != DungeonType.DT_NONE && getProp().getCurSceneId() != ownerActor().getBigSceneId();
    }

    /**
     * 重进副本 在视野切回大世界之后
     */
    public void reEnterDungeon(DungeonType type) {
        // 获取暂存的副本信息
        DungeonSceneRecordProp recordV = getProp().getDungeonRecordV(type.getNumber());
        if (recordV == null) {
            throw new GeminiException(ErrorCode.ENTER_DUNGEON_FAILED);
        }
        // 看看有没有视野需要清理
        getOwner().getViewComponent().clearView();
        // 通知大世界切到副本去了
        SsSceneDungeon.EnterDungeonAsk.Builder builder = SsSceneDungeon.EnterDungeonAsk.newBuilder();
        ownerActor().callCurScene(builder.setPlayerId(getEntityId()).setType(type).build());
        enterSavedDungeon(recordV);
    }

    /**
     * 重新进入副本  视野切回大世界后 或者 登录就进副本
     */
    private void enterSavedDungeon(DungeonSceneRecordProp recordV) {
        long targetSceneId = recordV.getDungeonSceneId();
        // 构建ref
        dungeonRef = RefFactory.ofFixedDungeon(recordV.getDungeonRefBus(), String.valueOf(targetSceneId));
        final String token = NotificationTokenHelper.getToken(this.getPlayerId());
        final CommonEnum.Language language = NotificationTokenHelper.getLanguage(this.getPlayerId());
        SsSceneDungeon.PlayerLoginAsk.Builder longinAsk = buildLoginAsk(false, token, language);
        // 走登入进入副本
        DungeonType dungeonType = DungeonType.forNumber(recordV.getDungeonType());
        try {
            ownerActor().callTargetDungeon(dungeonRef, longinAsk.build());
            onPropChange2Dungeon(targetSceneId, dungeonType, dungeonRef, false);
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                LOGGER.info("exception_perf {} reEnterDungeon failed ", getOwner(), e);
            }
            tryRemoveDungeonRecord(dungeonType);
            // 进不去 回大世界
            quitDungeonToMain();
            throw new GeminiException(ErrorCode.ENTER_DUNGEON_FAILED);
        }
    }

    /**
     * 主动退出副本 不能再进来了!!!
     */
    public void quitDungeon(DungeonType type) {
        if (getOwner().isInMainScene()) {
            return;
        }
        if (type != getProp().getDungeonType()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 尝试删存档
        tryRemoveDungeonRecord(type);
        // 通知当前副本
        SsSceneDungeon.LeaveDungeonAsk msg = SsSceneDungeon.LeaveDungeonAsk.newBuilder().setPlayerId(getPlayerId()).build();
        try {
            ownerActor().callCurScene(msg);
        } catch (Exception e) {
            LOGGER.error("{} quitDungeon failed ", getOwner(), e);
        }
        // 通知大世界 离开副本了
        onPropChange2MainScene();
        ownerActor().callBigScene(msg);
    }

    /**
     * 副本销毁通知
     */
    public void onDungeonDestroy(DungeonType type, long sceneId) {
        DungeonSceneRecordProp dungeonRecordV = getProp().getDungeonRecordV(type.getNumber());
        if (dungeonRecordV != null) {
            if (dungeonRecordV.getDungeonSceneId() == sceneId) {
                tryRemoveDungeonRecord(type);
            } else {
                LOGGER.error("handleOnDungeonDestroyCmd but record data not match {} {} {} {}", getOwner(), type, sceneId, dungeonRecordV.getDungeonSceneId());
            }
        }
        if (sceneId != getProp().getCurSceneId()) {
            LOGGER.warn("handleOnDungeonDestroyCmd curSceneId={}, askSceneId={}, not equal", getProp().getCurSceneId(), sceneId);
            return;
        }
        // 改prop
        onPropChange2MainScene();
        // 直接进大世界
        if (getOwner().isOnline()) {
            ownerActor().callBigScene(SsSceneDungeon.LeaveDungeonAsk.newBuilder().setPlayerId(getPlayerId()).build());
        }
    }

    /**
     * 登录前的 副本check
     */
    private DungeonSceneRecordProp checkSavedDungeonBeforeLogin() {
        // 当前场景是副本  但是没有记录 切回去
        if (getProp().getDungeonType() != DungeonType.DT_NONE) {
            LOGGER.warn("checkSavedDungeonBeforeLogin but dungeon record not exist type={}", getProp().getDungeonType());
            onPropChange2MainScene();
        }
        // 移民没切场景 兜底
        if (getProp().getCurSceneId() != getOwner().getZoneId()) {
            LOGGER.warn("checkSavedDungeonBeforeLogin but scene id not match(maybe migrate) oldSceneId={}", getProp().getCurSceneId());
            onPropChange2MainScene();
        }
        return null;
    }

    /**
     * @param intlToken intel token。
     * @return answer。
     */
    public SsSceneDungeon.PlayerLoginAns playerLogin(final String intlToken, final CommonEnum.Language language) {
        DungeonSceneRecordProp dungeonRecord = null;
        try {
            // 登录前暂存副本校验 看看要不要直接进副本
            dungeonRecord = checkSavedDungeonBeforeLogin();
        } catch (Exception e) {
            LOGGER.error("checkSavedDungeonBeforeLogin failed {} {}", getOwner(), e);
        }
        long curSceneId = getProp().getCurSceneId();
        LOGGER.info("{} atPlayer login scene, playerId={}, sceneId={} type={}", LogKeyConstants.GAME_PLAYER_LOGIN, getEntityId(), curSceneId, getProp().getDungeonType());

        // 不管之前在不在副本  都要先进大世界一波
        SsSceneDungeon.PlayerLoginAsk.Builder builder = buildLoginAsk(true, intlToken, language);
        SsSceneDungeon.PlayerLoginAns ans = ownerActor().callBigScene(builder.build());
        // 有副本 需要直接进
        if (dungeonRecord != null) {
            try {
                enterSavedDungeon(dungeonRecord);
            } catch (Exception e) {
                LOGGER.error("login in try enter dungeon but failed {} {}", getOwner(), e);
            }
        }
        return ans;
    }

    /**
     * 正常登出  如果在副本中  就暂存
     */
    public void playerLogout() {
        // 跟大世界说登出了
        SsSceneDungeon.PlayerLogoutAsk ask = SsSceneDungeon.PlayerLogoutAsk.newBuilder().setPlayerId(getEntityId()).build();
        ownerActor().callBigScene(ask);
        // 当前是在副本中
        if (isInDungeon()) {
            onLogoutDungeon();
        }
    }

    private void onLogoutDungeon() {
        SsSceneDungeon.PlayerLogoutAsk ask = SsSceneDungeon.PlayerLogoutAsk.newBuilder().setPlayerId(getEntityId()).build();
        try {
            SsSceneDungeon.PlayerLogoutAns ans = ownerActor().callTargetDungeon(dungeonRef, ask);
            int type = getProp().getDungeonType().getNumber();
            // 副本侧需要判断是否需要保存下这个副本
            if (ans.getNeedSaveScene()) {
                if (getProp().getDungeonRecordV(type) == null) {
                    tryAddDungeonRecord();
                } else {
                    getProp().getDungeonRecordV(type).setLogoutTsMs(SystemClock.now());
                }
            } else {
                // 如果不暂存  就尝试移除
                tryRemoveDungeonRecord(getProp().getDungeonType());
            }
            LOGGER.info("logout when in dungeon {} {} {} {}", getOwner(), getProp().getCurSceneId(), getProp().getDungeonType(), ans.getNeedSaveScene());
        } catch (Exception e) {
            LOGGER.error("onLogoutDungeon failed {} {}", getOwner(), e);
            onPropChange2MainScene();
            tryRemoveDungeonRecord(getProp().getDungeonType());
        }
    }

    /**
     * 视野切回大世界操作
     */
    public void changeToBigScene() {
        if (getOwner().isInMainScene()) {
            return;
        }
        // 跟副本说登出了
        onLogoutDungeon();
        // 告诉主世界退出副本
        quitDungeonToMain();
    }

    /**
     * 回到大世界
     */
    public void onPropChange2MainScene() {
        this.dungeonRef = null;
        getProp().setCurSceneId(getOwner().getZoneId()).setDungeonType(DungeonType.DT_NONE);
    }

    /**
     * 进入副本 改属性
     *
     * @param sceneId
     * @param type       副本类型
     * @param dungeonRef 副本ref
     * @param isEnter    是否是进入（true==进入副本，false==视野切换至副本）
     */
    public void onPropChange2Dungeon(long sceneId, DungeonType type, IActorRef dungeonRef, boolean isEnter) {
        this.dungeonRef = dungeonRef;
        getProp().setCurSceneId(sceneId).setDungeonType(type);
        tryAddDungeonRecord();
        LOGGER.info("{} onPropChange2Dungeon sceneId: {} type:{}", getOwner(), sceneId, type);
        if (isEnter) {
            onEnterDungeon(type);
        }
    }

    /**
     * 进入副本时
     *
     * @param type 副本类型
     */
    private void onEnterDungeon(DungeonType type) {

    }

    /**
     * 尝试存盘副本记录
     */
    private void tryAddDungeonRecord() {
        int type = getProp().getDungeonType().getNumber();
        if (getProp().getDungeonRecord().containsKey(type)) {
            return;
        }
        getProp().addEmptyDungeonRecord(type)
                .setDungeonSceneId(getProp().getCurSceneId())
                .setDungeonRefBus(dungeonRef.getBusId())
                .setLogoutTsMs(SystemClock.now());
        getOwner().getAddComponent().removeAdditionBySource(CommonEnum.AdditionSourceType.AST_DUNGEON);
        LOGGER.info("tryAddDungeonRecord {} {} {}", getOwner(), type, getProp().getCurSceneId());
    }

    /**
     * 尝试清理副本
     */
    private void tryRemoveDungeonRecord(DungeonType type) {
        if (!getProp().getDungeonRecord().containsKey(type.getNumber())) {
            return;
        }
        getProp().removeDungeonRecordV(type.getNumber());
        List<DevBuffProp> dungeonBuffs = getOwner().getPlayerDevBuffComponent().getDevBuffByBuffType(CommonEnum.DevBuffType.DBT_DUNGEON_BUFF);
        // 离开副本，删掉所有副本buff
        for (DevBuffProp devBuffProp : dungeonBuffs) {
            getOwner().getPlayerDevBuffComponent().removeDevBuff(devBuffProp.getDevBuffId());
        }
        getOwner().getAddComponent().removeAdditionBySource(CommonEnum.AdditionSourceType.AST_DUNGEON);
        LOGGER.info("tryRemoveDungeonRecord dungeonType={}", type);
    }

    /**
     * 退出副本切到主世界
     */
    private void quitDungeonToMain() {
        // prop改一下
        onPropChange2MainScene();
        // 告诉大世界退出副本
        ownerActor().callBigScene(SsSceneDungeon.LeaveDungeonAsk.newBuilder().setPlayerId(getPlayerId()).build());
    }

    private PlayerSceneProp getProp() {
        return getOwner().getProp().getScene();
    }

    private SsSceneDungeon.PlayerLoginAsk.Builder buildLoginAsk(final boolean isJoinMainScene, final String intlToken, final CommonEnum.Language language) {
        SsSceneDungeon.PlayerLoginAsk.Builder builder = SsSceneDungeon.PlayerLoginAsk.newBuilder();
        builder.setPlayerId(getEntityId())
                .setSessionRef(getOwner().getSessionComponent().getSessionRefData())
                .setIsNewbie(getOwner().getProp().getNewbieModel().getIsNewbie())
                .setLastLoginOutTsMs(getOwner().getProp().getBasicInfo().getLastLogoutTsMs())
                .setLanguage(language);
        if (intlToken != null) {
            builder.setIntlNtfToken(intlToken);
        }
        // 登录数据check 要check多模块的数据
        if (isJoinMainScene) {
            builder.setOpenId(getOwner().getOpenId())
                    .addAllSkynetMonsterList(getOwner().getSkynetComponent().attentionMonsterList())
                    .setDungeonType(getProp().getDungeonType());
            builder.getPlayerMilestoneInfoBuilder().addAllPlayerMileStone(getOwner().getMileStoneComponent().getIsOverMileStoneId());
        }
        return builder;
    }
}

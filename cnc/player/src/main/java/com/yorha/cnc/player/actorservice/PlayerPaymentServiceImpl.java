package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.common.actor.PlayerPaymentService;
import com.yorha.common.enums.pay.PayType;
import com.yorha.proto.SsPlayerPayment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class PlayerPaymentServiceImpl implements PlayerPaymentService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPaymentServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerPaymentServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    @Override
    public void handleMidasCallbackAsk(SsPlayerPayment.MidasCallbackAsk ask) {
        LOGGER.info("{} handle midas callback, msg={}", playerActor, ask);
        playerActor.getOrLoadEntity().getPaymentComponent().handleMidasCallback(ask, true, PayType.PAY_BY_MONEY);
    }
}

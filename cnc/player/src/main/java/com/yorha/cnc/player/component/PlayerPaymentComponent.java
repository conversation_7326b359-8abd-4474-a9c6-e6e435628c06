package com.yorha.cnc.player.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.TextFormat;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerBaseChargeEvent;
import com.yorha.cnc.player.event.task.PlayerBuyGoodsEvent;
import com.yorha.cnc.player.goods.Goods;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.clan.ClanGiftRecordUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.pay.PayType;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.midas.MidasConfig;
import com.yorha.common.midas.MidasUtils;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.qlog.json.item.QlogItemConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.ThirdPartyErrLogLimiter;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.http.client.HttpResponseException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBundlePurchase;
import res.template.ChargeBaseTemplate;
import res.template.ChargeGoodsTemplate;
import res.template.ChargeSdkTemplate;
import res.template.ConstTemplate;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.yorha.proto.CommonEnum.CurrencyType;
import static com.yorha.proto.PlayerPayment.*;
import static com.yorha.proto.SsMidasAgent.*;

/**
 * 支付（目前全走midas/米大师）
 * 只有一级货币，都托管在midas侧
 * 游戏侧接口主要就是query、consume、present
 * 阻塞式接口、异步式接口
 * 失败重试机制（现在的重试机制只是简单的重试一下而已，不提供存库等保证手段）
 */
public class PlayerPaymentComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPaymentComponent.class);
    // 如果客户端没有正常上报pf的话，用这个缺省的也可以正常使用
    private static final String DEFAULT_MIDAS_PF = "wechat_abroad_wx-2001-android-2011";
    // 和midas的交互重试次数，不要设置太大，现在ask模式的重试是递归的，有爆栈风险的哦
    private static final int RETRY_TIMES = 2;
    private static final int MAX_DONE_GOODS_ORDER_SIZE = 100;

    static {
        EntityEventHandlerHolder.register(PlayerBaseChargeEvent.class, PlayerPaymentComponent::checkMidasUrlShow);
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, PlayerPaymentComponent::checkMidasUrlShow);
        EntityEventHandlerHolder.register(PlayerBuyGoodsEvent.class, PlayerPaymentComponent::checkMidasUrlShow);
    }

    // 按时序排序,用于淘汰
    private List<PlayerGoodsInfoProp> doneGoodOrderList;

    public PlayerPaymentComponent(PlayerEntity owner) {
        super(owner);
    }

    private static boolean isAnsOk(MidasRespHeader respHeader) {
        return respHeader.getRet() == 0;
    }

    private static boolean isNoBalance(MidasRespHeader respHeader) {
        return respHeader.getRet() == 1004;
    }

    private static String ansToString(GeneratedMessageV3 msg) {
        if (msg == null) {
            return "null";
        }
        return TextFormat.printer().escapingNonAscii(false).printToString(msg);
    }

    private static String formMidasDoneGoodsOrderKey(final String token, final String billno, final String openId) {
        return billno + openId + token;
    }

    /**
     * 检测是否需要米大师url糊脸（客户端根据player prop判断是否需要糊脸）
     */
    private static void checkMidasUrlShow(final PlayerEvent event) {
        if (ServerContext.isIosExamine()) {
            LOGGER.info("checkMidasUrlShow skip, isIosExamine");
            return;
        }
        final PlayerEntity player = event.getPlayer();
        // 主城升级事件 & 非第一次糊脸

        // 后续糊脸只会因为充值显示
        if (event instanceof MainCityUpgradeStaticEvent && player.getProp().getPaymentModel().getLastShowMidasUrlTsMs() != 0) {
            LOGGER.info("checkMidasUrlShow skip, ignore mainCityUpgradeEvent");
            return;
        }


        // 累充没到金额不显示
        {
            // 直冲累计黄金
            final long saveAmt = player.getProp().getPaymentModel().getSaveAmt();
            // 直购赠送黄金
            final long goodsSaveAmt = player.getStatisticComponent().getSingleStatistic(StatisticEnum.BUY_GOODS_GAIN_DIAMOND);
            final long targetSaveAmt = ResHolder.getConsts(ConstTemplate.class).getMidasNotifyMinSaveAmt();
            if (saveAmt + goodsSaveAmt < targetSaveAmt) {
                LOGGER.info("checkMidasUrlShow skip, saveAmt={}, goodsSaveAmt={}, targetSaveAmt={}", saveAmt, goodsSaveAmt, targetSaveAmt);
                return;
            }
        }

        final long now = SystemClock.now();

        // 绝对时间内不再显示
        {
            final long absDays = TimeUtils.getAbsDaysBetween(player.getProp().getPaymentModel().getLastShowMidasUrlTsMs(), now);
            final int minGapDays = ResHolder.getConsts(ConstTemplate.class).getMidasNotifyRenotifyDay();
            if (absDays < minGapDays) {
                LOGGER.info("checkMidasUrlShow skip, absDays={}, minGapDays={}", absDays, minGapDays);
                return;
            }
        }

        LOGGER.info("checkMidasUrlShow new tsMs={}", now);
        player.getProp().getPaymentModel().setLastShowMidasUrlTsMs(now);
    }

    @Override
    public void onLoad(boolean isRegister) {
        final PlayerPaymentModelProp paymentModel = getOwner().getProp().getPaymentModel();
        doneGoodOrderList = new ArrayList<>(paymentModel.getDoneGoodsOrders().values());
        doneGoodOrderList.sort(Comparator.comparing(PlayerGoodsInfoProp::getTsMs));
        shrinkDoneGoodOrderList();
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        // 非阻塞地拉取货币信息，不影响注册登录基本流程
        asyncPull();
    }

    private String getPf() {
        String pf = getOwner().getProp().getBasicInfo().getMidasPf();
        return StringUtils.isEmpty(pf) ? DEFAULT_MIDAS_PF : pf;
    }

    // 本来要整一个withRetryAsync的，但是，还是收敛到ask吧

    private MidasReqHeader buildHeader() {
        return MidasReqHeader.newBuilder()
                .setZoneId(ServerContext.getWorldId())
                .setOpenId(getOwner().getOpenId())
                .setPlayerId(getPlayerId())
                .setPf(getPf())
                .build();
    }

    @Nullable
    private <RESP> RESP withRetry(String what, Supplier<RESP> func, final int tryTimes) {
        for (int times = 1; times <= tryTimes; times++) {
            try {
                RESP resp = func.get();
                if (resp == null) {
                    LOGGER.error("midas_perf {} payment try {} failed. times={}. cause:resp=null", getPlayerId(), what, times);
                } else {
                    return resp;
                }
            } catch (Exception e) {
                if (isMidasError(e)) {
                    LOGGER.warn("midas_perf {} payment try {} failed. times={}. cause:", getPlayerId(), what, times, e);
                } else {
                    LOGGER.error("midas_perf {} payment try {} failed. times={}. cause:", getPlayerId(), what, times, e);
                }

            }
        }
        return null;
    }

    private boolean isMidasError(Exception e) {
        if (e instanceof GeminiException) {
            return ((GeminiException) e).getCodeId() == ErrorCode.MIDAS_NETWORK_FAIL.getCodeId();
        } else if (e instanceof HttpResponseException) {
            // 5XX为服务器错误
            return ((HttpResponseException) e).getStatusCode() >= 500;
        } else {
            return e instanceof SocketTimeoutException;
        }
    }

    /**
     * 有retry能力的ask
     *
     * @param what      行为(打日志)
     * @param ask       ask
     * @param tryTimes  总执行次数(成功了就结束)
     * @param onSuccess 成功时回调
     * @param onFailure 失败时回调(超过总执行次数)
     */
    private <RESP> void withRetryAsk(String what, GeneratedMessageV3 ask, final int tryTimes, Consumer<RESP> onSuccess, Consumer<Throwable> onFailure) {
        loop(1, what, ask, onSuccess, onFailure, tryTimes);
    }

    private <RESP> void loop(int times, String what, GeneratedMessageV3 ask, Consumer<RESP> onSuccess, Consumer<Throwable> onFailure, final int tryTimes) {
        ownerActor().<RESP>askMidasAgent(ask)
                .onComplete((resp, err) -> {
                    if (resp == null || err != null) {
                        if (times < tryTimes) {
                            LOGGER.error("midas_perf {} payment try {} failed. times={}. cause:", getPlayerId(), what, times, err);
                            loop(times + 1, what, ask, onSuccess, onFailure, tryTimes);
                        } else {
                            onFailure.accept(err);
                        }
                    } else {
                        onSuccess.accept(resp);
                    }
                });
    }

    private boolean refreshCurrency(MidasRespHeader respHeader) {
        if (respHeader == null) {
            WechatLog.error("refreshCurrency but respHeader is null");
            return false;
        }
        Int32CurrencyMapProp purse = getOwner().getProp().getPurse();
        CurrencyProp diamondProp = purse.get(CurrencyType.DIAMOND.getNumber());
        if (diamondProp == null) {
            WechatLog.error("refreshCurrency but diamondProp is null {}", respHeader);
            return false;
        }
        LOGGER.info("midas_perf {} payment diamond count change {}->{}", getPlayerId(), diamondProp.getCount(), respHeader.getBalance());
        boolean changed = diamondProp.getCount() != respHeader.getBalance();
        if (changed) {
            diamondProp.setCount(respHeader.getBalance());
        }
        PlayerPaymentModelProp paymentModel = getOwner().getProp().getPaymentModel();
        paymentModel.setGenBalance(respHeader.getGenBalance());
        return changed;
    }

    private void refreshProp(MidasQueryAns queryAns) {
        PlayerPaymentModelProp model = getOwner().getProp().getPaymentModel();
        final long saveAmtBefore = model.getSaveAmt();
        // saveAmt是玩家直充获得的游戏币数量
        if (saveAmtBefore != queryAns.getSaveAmt()) {
            LOGGER.info("midas_perf charge={} {} payment save_amt change {}->{}", queryAns.getSaveAmt() - saveAmtBefore, getPlayerId(), saveAmtBefore, queryAns.getSaveAmt());
            model.setSaveAmt(queryAns.getSaveAmt());
            // 统计计数
            getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.CHARGE_BASE_TIMES, 1);
            new PlayerBaseChargeEvent(getOwner(), saveAmtBefore, queryAns.getSaveAmt()).dispatch();
        }
        model.setFirstSave(queryAns.getFirstSave());
        model.setSaveSum(queryAns.getSaveSum());
        model.setCostSum(queryAns.getCostSum());
        model.setPresentSum(queryAns.getPresentSum());
    }

    private void monitorInvoke(final String method) {
        LOGGER.info("midas_perf {} monitorInvoke reason={}", getPlayerId(), method);
        MonitorUnit.PLAYER_MIDAS_REQUEST_TOTAL.labels(ServerContext.getBusId(), method).inc();
    }

    private void monitorInvokeFailed(final String reason) {
        LOGGER.warn("midas_perf {} monitorInvokeFailed reason={}", getPlayerId(), reason);
        MonitorUnit.PLAYER_MIDAS_REQUEST_FAIL_TOTAL.labels(ServerContext.getBusId(), reason).inc();
    }

    public void syncPull() {
        if (MidasConfig.fakeMidas()) {
            return;
        }
        MidasReqHeader header = buildHeader();
        MidasQueryAsk ask = MidasQueryAsk.newBuilder().setHeader(header).build();
        monitorInvoke("syncPull");
        MidasQueryAns ans = withRetry("midas_query", () -> ownerActor().callMidasAgent(ask), RETRY_TIMES);

        if (ans == null || !isAnsOk(ans.getHeader())) {
            final String failReason;
            if (ans == null) {
                failReason = "syncPull outOfTime";
                ThirdPartyErrLogLimiter.MIDAS_OUT_OF_TIME.tryError("midas_perf {} payment pull failed reason=outOfTime", getPlayerId());
            } else {
                failReason = "syncPull midas error";
                WechatLog.error("midas_perf {} payment pull failed reason=midas error, ans={}", getPlayerId(), ansToString(ans));
            }
            monitorInvokeFailed(failReason);
        } else {
            final long before = getOwner().getPurseComponent().count(CurrencyType.DIAMOND);
            LOGGER.info("midas_perf {} syncPull before={}, midas header={}", getPlayerId(), before, ans.getHeader());
            if (refreshCurrency(ans.getHeader())) {
                final long after = getOwner().getPurseComponent().count(CurrencyType.DIAMOND);
                getOwner().getQlogComponent().sendMoneyQLog(CurrencyType.DIAMOND.getNumber(), before, after, after - before, true, CommonEnum.Reason.ICR_CHARGE_BASE, "");
            }
            refreshProp(ans);
        }
    }

    public void asyncPull() {
        if (MidasConfig.fakeMidas()) {
            return;
        }
        MidasReqHeader header = buildHeader();
        MidasQueryAsk ask = MidasQueryAsk.newBuilder().setHeader(header).build();
        monitorInvoke("asyncPull");
        this.<MidasQueryAns>withRetryAsk("midas_async_query", ask, RETRY_TIMES,
                ans -> {
                    MidasRespHeader ansHeader = ans.getHeader();
                    if (isAnsOk(ansHeader)) {
                        final long before = getOwner().getPurseComponent().count(CurrencyType.DIAMOND);
                        LOGGER.info("midas_perf {} asyncPull before={}, midas header={}", getPlayerId(), before, ans.getHeader());
                        if (refreshCurrency(ansHeader)) {
                            final long after = getOwner().getPurseComponent().count(CurrencyType.DIAMOND);
                            getOwner().getQlogComponent().sendMoneyQLog(CurrencyType.DIAMOND.getNumber(), before, after, after - before, true, CommonEnum.Reason.ICR_CHARGE_BASE, "");
                        }
                        refreshProp(ans);
                    } else {
                        monitorInvokeFailed("asyncPull midas error");
                        WechatLog.error("midas_perf {} payment pull failed reason=midas error, ans={}", getPlayerId(), ansToString(ans));
                    }
                },
                err -> {
                    monitorInvokeFailed("asyncPull outOfTime");
                    ThirdPartyErrLogLimiter.MIDAS_OUT_OF_TIME.tryError("midas_perf {} payment pull failed reason=outOfTime, err=", getPlayerId(), err);
                });
    }

    private String genBillNo() {
        return ownerActor().getPlayerId() + "_" + SystemClock.now() + "_" + RandomUtils.nextInt(1, 999);
    }

    public void syncConsume(long amount) {
        if (amount <= 0) {
            throw new GeminiException("diamond consume amount should >0. {}", amount);
        }
        if (MidasConfig.fakeMidas()) {
            fakeConsume(amount);
            return;
        }
        final String billno = genBillNo();
        MidasReqHeader header = buildHeader();
        MidasConsumeAsk ask = MidasConsumeAsk.newBuilder()
                .setHeader(header)
                .setBillno(billno)
                .setAmount(amount)
                .build();

        monitorInvoke("syncConsume");
        MidasConsumeAns ans;


        ans = withRetry("midas_consume", () -> ownerActor().callMidasAgent(ask), 1);
        LOGGER.info("midas_perf {} syncConsume ask={}, ans={}", getPlayerId(), ask, ans);
        // 正常返回
        if (ans != null && isAnsOk(ans.getHeader())) {
            refreshCurrency(ans.getHeader());
            return;
        }
        final String failReason;
        if (ans == null) {
            failReason = "syncConsume outOfTime";
            ThirdPartyErrLogLimiter.MIDAS_OUT_OF_TIME.tryError("midas_perf {} payment consume failed reason={}, amount={} billno={}", getPlayerId(), failReason, amount, billno);
        } else {
            // 余额不足却尝试消费金条
            if (isNoBalance(ans.getHeader())) {
                failReason = "user no balance";
                LOGGER.warn("midas_perf {} payment consume failed reason={}, amount={} billno={} {}", getPlayerId(), failReason, amount, billno, ansToString(ans));
            } else {
                failReason = "syncConsume midas error";
                WechatLog.error("midas_perf {} payment consume failed reason={}, amount={} billno={} {}", getPlayerId(), failReason, amount, billno, ansToString(ans));
            }

        }
        monitorInvokeFailed(failReason);
        throw new GeminiException(ErrorCode.MIDAS_NETWORK_FAIL, failReason);
    }

    public void asyncPresent(long amount, Consumer<MidasPresentAns> callback) {
        if (amount <= 0) {
            throw new GeminiException("diamond present amount should >0. {}", amount);
        }
        if (MidasConfig.fakeMidas()) {
            fakePresent(amount);
            callback.accept(MidasPresentAns.newBuilder().build());
            return;
        }
        final String billno = genBillNo();
        MidasReqHeader header = buildHeader();
        MidasPresentAsk ask = MidasPresentAsk.newBuilder()
                .setHeader(header)
                .setBillno(billno)
                .setAmount(amount)
                .build();
        monitorInvoke("asyncPresent");


        this.<MidasPresentAns>withRetryAsk("async present", ask, 1,
                ans -> {
                    LOGGER.info("midas_perf {} payment async present, ask={}, ans={}", getPlayerId(), ask, ans);
                    // 成功
                    if (isAnsOk(ans.getHeader())) {
                        LOGGER.info("midas_perf {} payment async present, success", getPlayerId());
                        refreshCurrency(ans.getHeader());
                        callback.accept(ans);
                        return;
                    }
                    // 失败
                    monitorInvokeFailed("asyncPresent midas error");
                    WechatLog.error("midas_perf {} payment async present failed reason=midas error, amount={} billno={} ans={}", getPlayerId(), amount, billno, ansToString(ans));
                },
                (err) -> {
                    monitorInvokeFailed("asyncPresent outOfTime");
                    ThirdPartyErrLogLimiter.MIDAS_OUT_OF_TIME.tryError("midas_perf {} payment async present failed reason=outOfTime, amount={}, billno={} err=", getPlayerId(), amount, billno, err);
                });
    }

    private void fakeConsume(long amount) {
        refreshCurrency(MidasRespHeader.newBuilder()
                .setBalance(getOwner().getPurseComponent().getCurrencyAmount(CurrencyType.DIAMOND) - amount)
                .setGenBalance(getOwner().getProp().getPaymentModel().getGenBalance() - amount)
                .build());
    }

    private void fakePresent(long amount) {
        refreshCurrency(MidasRespHeader.newBuilder()
                .setBalance(getOwner().getPurseComponent().getCurrencyAmount(CurrencyType.DIAMOND) + amount)
                .setGenBalance(getOwner().getProp().getPaymentModel().getGenBalance() + amount)
                .build());
    }

    public Player_ApplyGoodsOrder_S2C handleApplyGoodsOrder(Player_ApplyGoodsOrder_C2S msg) {
        final int goodsId = msg.getGoodsId();
        LOGGER.info("midas_perf midas_buy {} handleApplyGoodsOrder start, msg={}", getPlayerId(), msg);
        ChargeGoodsTemplate goodsTemplate = ResHolder.findTemplate(ChargeGoodsTemplate.class, goodsId);
        if (goodsTemplate == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "illegal goods id");
        }
        PlayerPaymentModelProp paymentModel = getOwner().getProp().getPaymentModel();
        // 基本的限购次数
        final int maxPurchaseTimes = goodsTemplate.getMaxPurchaseTimes();
        PlayerGoodsHistoryProp goodsHistory = paymentModel.getGoodsHistoryV(goodsId);
        int boughtTimes = goodsHistory == null ? 0 : goodsHistory.getBoughtTimes();
        if (maxPurchaseTimes >= 0 && boughtTimes >= maxPurchaseTimes) {
            // 配置为-1表明不限购
            throw new GeminiException(ErrorCode.PURCHASE_TIMES_LIMIT);
        }

        // 购买礼包的预校验
        Goods.goodsOf(goodsTemplate.getGoodsType()).checkApply(getOwner(), msg, goodsTemplate);

        // 同一goodsId只有一个token
        PlayerGoodsOrderProp orderProp = null;
        for (PlayerGoodsOrderProp exist : paymentModel.getGoodsOrders().values()) {
            if (exist.getGoodsId() == goodsId) {
                orderProp = exist;
                // 订单已经存在，则清空原来的信息。因为一个goodsId同时只能存在一个订单
                LOGGER.info("midas_perf midas_buy handleApplyGoodsOrder extInfo is cleared {}", goodsId);
                orderProp.getExtInfo().mergeFromSs(Player.GoodsOrderExtInfo.getDefaultInstance());
                break;
            }
        }

        if (orderProp == null) {
            // 为这笔订单生成token并落库
            String token = genBillNo();

            orderProp = paymentModel.addEmptyGoodsOrders(token)
                    .setGoodsId(goodsId);
        }
        orderProp.setTsSec((int) TimeUtils.ms2Second(SystemClock.nowNative()));

        // 填充订单额外信息
        Goods.goodsOf(goodsTemplate.getGoodsType()).fillOrder(getOwner(), msg, goodsTemplate, orderProp);
        // token 为唯一ID
        final String token = orderProp.getToken();
        // appMeta = {token}_{zoneId}
        final String appMeta = formAppMeta(token);
        Player_ApplyGoodsOrder_S2C.Builder resp = Player_ApplyGoodsOrder_S2C.newBuilder()
                .setOrderToken(appMeta)
                .setOrderFinished(false);

        LOGGER.info("midas_perf midas_buy {} handleApplyGoodsOrder done, msg={}, token={}, appMeta={}", getPlayerId(), msg, token, appMeta);
        if (MidasConfig.fakeMidas() && !ServerContext.isProdSvr()) {
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, goodsTemplate.getChargeSdkId());
            resp.setOrderFinished(true);
            // fake_midas模式下直接发货了
            handleMidasCallback(SsPlayerPayment.MidasCallbackAsk.newBuilder()
                    .setProductId(sdkTemplate.getMidasProductId())
                    .setAppMeta(appMeta)
                    .build(), false, PayType.PAY_BY_MONEY);
        } else {
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, goodsTemplate.getChargeSdkId());
            // 代金券id
            final int voucherItemId = ResHolder.getConsts(ConstTemplate.class).getVoucherItemId();
            // 代金券数量
            final int needVoucher = sdkTemplate.getGoodsNeedVoucher();
            // 荣耀金券id
            final int honourVouchItemId = sdkTemplate.getHonourVoucherItemId();
            PayType payType = null;
            // 先判断代金券
            if (tryConsumeVoucher(voucherItemId, needVoucher, goodsId)) {
                payType = PayType.PAY_BY_VOUCHER;
            } else if (honourVouchItemId > 0 && tryConsumeVoucher(honourVouchItemId, 1, goodsId)) {
                // 再判断荣耀金券，(允许荣耀金券购买且有对应道具)
                payType = PayType.PAY_BY_HONOUR_VOUCHER;
            }
            if (payType != null) {
                resp.setOrderFinished(true);
                // 发货
                handleMidasCallback(SsPlayerPayment.MidasCallbackAsk.newBuilder()
                        .setProductId(sdkTemplate.getMidasProductId())
                        .setAppMeta(appMeta)
                        .build(), false, payType);
            }

        }
        return resp.build();
    }

    /**
     * 尝试扣除代金券
     *
     * @param voucherItemId 代金券道具id
     * @param num           数量
     * @param goodsId       礼包id
     * @return true==扣除成功
     */
    private boolean tryConsumeVoucher(final int voucherItemId, final int num, final int goodsId) {
        if (!getOwner().getItemComponent().hasEnough(voucherItemId, num)) {
            return false;
        }
        getOwner().getItemComponent().consume(voucherItemId, num, CommonEnum.Reason.ICR_BUY_GOODS, String.valueOf(goodsId));
        return true;
    }

    /**
     * 发货逻辑（含子礼包发货）
     *
     * @param goodsId    礼包id
     * @param sendReason 道具理由
     * @return directDiamond 返回的钻石数
     */
    private int sendGoods(final int goodsId, final CommonEnum.Reason sendReason) {
        // 给东西
        ChargeGoodsTemplate goodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, goodsId);
        int directDiamond = goodsTemplate.getDirectDiamond();
        if (directDiamond > 0) {
            getOwner().getPurseComponent().give(CurrencyType.DIAMOND, directDiamond, sendReason, String.valueOf(goodsId));
        } else {
            LOGGER.debug("midas_perf {} direct diamond no need to give. directDiamond: {}, goodsId: {}", getPlayerId(), directDiamond, goodsId);
        }
        getOwner().getAssetComponent().give(AssetPackage.builder().plusItems(goodsTemplate.getGoodsRewardPairList()).build(), sendReason, String.valueOf(goodsId));
        // 有配置军团礼物
        if (goodsTemplate.getClanGiftId() != 0) {
            this.sendClanGift(goodsTemplate);
        }
        // 子礼包发货
        for (IntPairType subGoods : goodsTemplate.getSubGoodsIdPairList()) {
            int subGoodsId = subGoods.getKey();
            directDiamond += this.sendGoods(subGoodsId, sendReason);
        }
        return directDiamond;
    }

    /**
     * 处理midas回调
     *
     * @param ask     ask
     * @param needAns 是否需要回包
     * @param payType 购买类型
     */
    public void handleMidasCallback(final SsPlayerPayment.MidasCallbackAsk ask, final boolean needAns, final PayType payType) {
        MonitorUnit.PLAYER_MIDAS_CALLBACK_TOTAL.labels(ServerContext.getBusId()).inc();
        LOGGER.info("midas_perf midas_buy {} midasCallBack ask={}", getPlayerId(), ask);
        SsPlayerPayment.MidasCallbackAns.Builder ans = SsPlayerPayment.MidasCallbackAns.newBuilder();
        boolean alreadyAnswer = false;
        String failReason = null;
        int goodsId = 0;
        try {
            // 米大师网页购买，直接发货
            if (isMidasBuy(ask)) {
                // token为空，仅根据billNo与openId防重
                final String key = formMidasDoneGoodsOrderKey(ask.getToken(), ask.getBillno(), ask.getOpenId());
                // 订单已发货过(完全一样的midas订单)
                if (getOwner().getProp().getPaymentModel().getDoneGoodsOrdersV(key) != null) {
                    LOGGER.info("midas_perf midas_buy {} alreadySendGoods midasweb same request,  billno={}, openId={}, appMeta={}",
                            getPlayerId(), ask.getBillno(), ask.getOpenId(), ask.getAppMeta());
                    ans.setOk(true);
                    ans.setExtraMsg(MidasUtils.ExtraMsg.SAME_REQUEST.getMsg());
                    ownerActor().answer(ans.build());
                    return;
                }
                try {
                    MonitorUnit.MIDAS_WEB_BUY_TOTAL.labels(ServerContext.getBusId()).inc();
                    goodsId = midasBuySendGoods(ask.getPayItem());
                } catch (Exception e) {
                    // Integer.parseInt失败
                    if (e instanceof NumberFormatException) {
                        failReason = MidasUtils.ExtraMsg.WEB_PARSE_FAIL.getMsg();
                    } else {
                        failReason = MidasUtils.ExtraMsg.WEB_FAIL.getMsg();
                    }
                    MonitorUnit.MIDAS_WEB_BUY_FAIL_TOTAL.labels(ServerContext.getBusId()).inc();
                    throw e;
                }

                ans.setOk(true);
                ownerActor().answer(ans.build());
                this.addDoneOrder("", ask.getBillno(), ask.getOpenId(), "", goodsId);
                return;
            }

            final String appMeta = getTokenFromAppMeta(ask.getAppMeta());
            LOGGER.info("midas_perf {} handleMidasCallback appMeta={}, parsed token={}", getPlayerId(), ask.getAppMeta(), appMeta);
            PlayerPaymentModelProp paymentModel = getOwner().getProp().getPaymentModel();

            // 凭token发货，一是合法，二可以防重，已经发货的会从这里移除的
            PlayerGoodsOrderProp goodsOrder = paymentModel.getGoodsOrdersV(getIdFromToken(appMeta));
            if (goodsOrder == null) {
                if (needAns) {
                    final String key = formMidasDoneGoodsOrderKey(ask.getToken(), ask.getBillno(), ask.getOpenId());
                    // 订单已发货过(完全一样的midas订单)
                    if (paymentModel.getDoneGoodsOrdersV(key) != null) {
                        LOGGER.info("midas_perf midas_buy {} alreadySendGoods same request, token={}, billno={}, openId={}, appMeta={}",
                                getPlayerId(), ask.getToken(), ask.getBillno(), ask.getOpenId(), ask.getAppMeta());
                        ans.setOk(true);
                        ans.setExtraMsg(MidasUtils.ExtraMsg.SAME_REQUEST.getMsg());
                        ownerActor().answer(ans.build());
                        return;
                    }
                    // 订单已发货过(appMeta相同，为业务唯一ID)
                    for (final PlayerGoodsInfoProp doneGoodsOrder : paymentModel.getDoneGoodsOrders().values()) {
                        if (doneGoodsOrder.getAppMeta().equals(appMeta)) {
                            LOGGER.info("midas_perf midas_buy {} alreadySendGoods same appMeta, token={}, billno={}, openId={}, appMeta={}, doneOrder={}",
                                    getPlayerId(), ask.getToken(), ask.getBillno(), ask.getOpenId(), ask.getAppMeta(), doneGoodsOrder);
                            ans.setOk(true);

                            ans.setExtraMsg(MidasUtils.ExtraMsg.SAME_REQUEST.getMsg());
                            ownerActor().answer(ans.build());
                            return;
                        }
                    }
                    LOGGER.warn("midas_perf midas_buy {} midasCallBack, order not found, token={}, billno={}, openId={}, appMeta={}",
                            getPlayerId(), ask.getToken(), ask.getBillno(), ask.getOpenId(), ask.getAppMeta());
                } else {
                    LOGGER.warn("midas_perf midas_buy {} order not found, appMeta={}",
                            getPlayerId(), ask.getAppMeta());
                }

                failReason = MidasUtils.ExtraMsg.ORDER_NOT_FOUND.getMsg();
                throw new GeminiException(ErrorCode.MIDAS_ORDER_NOT_FOUND);
            }
            goodsId = goodsOrder.getGoodsId();
            ChargeGoodsTemplate goodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, goodsId);
            int productId = goodsTemplate.getChargeSdkId();
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, productId);
            // 比对productId，appMeta是客户端透传的，不可靠，productId是midas侧和实际付款金额对应的东西，以此作为发货的真实保障
            if (!sdkTemplate.getMidasProductId().equals(ask.getProductId())) {
                failReason = MidasUtils.ExtraMsg.ILLEGAL_PRODUCT_ID.getMsg();
                throw new GeminiException("midas callback product not illegal! expected:{} recv:{}", sdkTemplate.getMidasProductId(), ask.getProductId());
            }

            Goods goods = Goods.goodsOf(goodsTemplate.getGoodsType());
            try {
                goods.checkBeforeDeliver(getOwner(), goodsTemplate, goodsOrder);
            } catch (Exception e) {
                LOGGER.warn("midas_perf {} checkBeforeDeliver fail, goodsId={}", getPlayerId(), goodsTemplate.getId());
                failReason = MidasUtils.ExtraMsg.CHECK_BEFORE_DELIVER_FAIL.getMsg();
                throw e;
            }

            LOGGER.info("midas_perf money goods={} {} midas callback. z:{} {} {}", sdkTemplate.getPrice(), getPlayerId(), needAns, payType, goodsOrder);
            // 还不知道该怎么处理，先warning一下
            if (SystemClock.nowOfSeconds() - goodsOrder.getTsSec() > TimeUnit.HOURS.toSeconds(2)) {
                LOGGER.warn("midas_perf {} midas callback. order ts too old. need check. {}", getPlayerId(), goodsOrder);
            }
            // 移除订单
            paymentModel.removeGoodsOrdersV(goodsOrder.getToken());
            if (needAns) {
                // try catch 保护
                try {
                    // 添加历史订单
                    this.addDoneOrder(ask.getToken(), ask.getBillno(), ask.getOpenId(), appMeta, goodsId);
                } catch (Exception e) {
                    LOGGER.error("handleMidasCallback addDoneOrder, ", e);
                }
            }

            // 获取购买次数记录
            PlayerGoodsHistoryProp goodsHistory = paymentModel.getGoodsHistoryV(goodsId);
            if (goodsHistory == null) {
                goodsHistory = paymentModel.addEmptyGoodsHistory(goodsId).setGoodsId(goodsId); // 不要问我为什么再set一遍
            }

            // 判断是否记录购买次数
            boolean needRecord = true;
            try {
                needRecord = goods.needRecordGoodsHistory(getOwner(), goodsTemplate, goodsOrder);
            } catch (Exception e) {
                WechatLog.error("midas_perf {} needRecordGoodsHistory err, order:{}, ", getPlayerId(), goodsOrder, e);
            }
            if (needRecord) {
                // 记录、增加购买次数
                goodsHistory.setBoughtTimes(goodsHistory.getBoughtTimes() + 1);
            }

            final int directDiamond = this.sendGoods(goodsId, CommonEnum.Reason.ICR_BUY_GOODS);
            ans.setOk(true);

            if (needAns) {
                LOGGER.info("midas_perf midas_buy {} sendGoods , goodsId={}, token={}, billno={}, openId={}, appMeta={}",
                        getPlayerId(), goodsId, ask.getToken(), ask.getBillno(), ask.getOpenId(), ask.getAppMeta());
                ownerActor().answer(ans.build());
                alreadyAnswer = true;
            } else {
                LOGGER.info("midas_perf midas_buy {} sendGoods, goodsId={}, appMeta={}", getPlayerId(), goodsId, ask.getAppMeta());
            }
            List<Struct.ItemPair> unfixedItems = Collections.emptyList();
            try {
                // 基础的钻石和道具已经给了，这个时候不能因为afterBaseDeliver出了异常导致退款什么的
                unfixedItems = goods.afterBaseDeliver(getOwner(), goodsTemplate, goodsOrder);
            } catch (Exception e) {
                WechatLog.error("midas_perf {} afterBaseDeliver err, order:{}, ", getPlayerId(), goodsOrder, e);
            }
            getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.BUY_GOODS_GAIN_DIAMOND, directDiamond);
            getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.BUY_GOODS_TIMES, 1);
            // 购买直购礼包事件，先answer再抛事件，不要卡住回调处理
            new PlayerBuyGoodsEvent(getOwner(), goodsId, directDiamond).dispatch();
            if (goodsTemplate.getTriggerMailId() > 0) {
                sendBuyGoodsMail(goodsId, goodsTemplate, unfixedItems);
            }
            try {
                String itemGet = "";
                if ((unfixedItems != null) && (!unfixedItems.isEmpty())) {
                    List<QlogItemConfig> qlogItemConfigs = new ArrayList<>(unfixedItems.size());
                    for (Struct.ItemPair itemPair : unfixedItems) {
                        QlogItemConfig itemConfig = new QlogItemConfig(itemPair.getItemTemplateId(), itemPair.getCount());
                        qlogItemConfigs.add(itemConfig);
                    }
                    itemGet = JsonUtils.toJsonString(qlogItemConfigs);
                }
                QlogCncBundlePurchase.init(getOwner().getQlogComponent())
                        .setDtEventTime(TimeUtils.now2String())
                        .setAction("purchase_bundle")
                        .setBundleId(goodsId)
                        .setPrice(sdkTemplate.getPrice())
                        .setLimitedCount(goodsTemplate.getMaxPurchaseTimes())
                        .setRemainLimitedCount(goodsTemplate.getMaxPurchaseTimes() >= 0 ? goodsTemplate.getMaxPurchaseTimes() - goodsHistory.getBoughtTimes() : -1)
                        .setIfDouble(0)
                        .setIfUseToken(payType.getType())
                        .setItemGet(itemGet)
                        .setActionNote("")
                        .sendToQlog();
                getOwner().getAntiAddictionComponent().reportPay(sdkTemplate);
            } catch (Exception e) {
                LOGGER.error("midas_perf {} send QlogCncBundlePurchase error:", getPlayerId(), e);
            }

        } catch (Exception e) {
            WechatLog.error("midas_perf {} handle midas callback error! ", getPlayerId(), e);
            MonitorUnit.PLAYER_MIDAS_CALLBACK_FAIL_TOTAL.labels(ServerContext.getBusId()).inc();
            deliverGoodsFailQlog(failReason != null ? failReason : "unknown reason", goodsId, payType);
            if (!alreadyAnswer && needAns) {
                ans.setOk(false);
                if (failReason != null) {
                    ans.setExtraMsg(failReason);
                    // check before deliver失败为业务问题，midas再重试也没用，故返回ok
                    // ILLEGAL_PRODUCT_ID 付款与发货的productId对不上，可能是客户端bug或玩家hack，再重试也没用，故返回ok
                    if (failReason.equals(MidasUtils.ExtraMsg.CHECK_BEFORE_DELIVER_FAIL.getMsg()) || failReason.equals(MidasUtils.ExtraMsg.ILLEGAL_PRODUCT_ID.getMsg())) {
                        ans.setOk(true);
                    }
                }
                ownerActor().answer(ans.build());
            }
        }
    }

    /**
     * 发货失败qlog
     *
     * @param failReason 失败原因
     * @param goodsId    礼包id
     * @param payType    购买方式
     */
    private void deliverGoodsFailQlog(@Nonnull final String failReason, final int goodsId, final PayType payType) {
        final QlogCncBundlePurchase qlogCncBundlePurchase = QlogCncBundlePurchase.init(getOwner().getQlogComponent());
        // 兜底保护：goodsId可能为空
        if (goodsId > 0) {
            ChargeGoodsTemplate goodsTemplate = ResHolder.getTemplate(ChargeGoodsTemplate.class, goodsId);
            int productId = goodsTemplate.getChargeSdkId();
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, productId);
            qlogCncBundlePurchase
                    .setBundleId(goodsId)
                    .setPrice(sdkTemplate.getPrice())
                    .setLimitedCount(goodsTemplate.getMaxPurchaseTimes())
                    .setIfUseToken(payType.getType());
        }
        qlogCncBundlePurchase
                .setRemainLimitedCount(0)
                .setIfDouble(0)
                .setItemGet("")
                .setAction("deliver_fail")
                .setDtEventTime(TimeUtils.now2String())
                .setActionNote(failReason)
                .sendToQlog();
    }

    /**
     * 请求是否来自midasbuy
     */
    private boolean isMidasBuy(final SsPlayerPayment.MidasCallbackAsk ask) {
        if (!StringUtils.isEmpty(ask.getAppMeta())) {
            return false;
        }
        return MidasUtils.isMidasBuy(ask.getChannelId());
    }

    private int midasBuySendGoods(final String payItem) {
        final int goodsId;
        {
            final String goodsIdString = payItem.substring(0, payItem.indexOf("*"));
            LOGGER.info("midas_perf midas_buy {} midasBuySendGoods goodsId={}, payItem={}", getPlayerId(), goodsIdString, payItem);
            goodsId = Integer.parseInt(goodsIdString);

        }
        sendGoods(goodsId, CommonEnum.Reason.ICR_MIDASBUY_PLATFORM);
        sendBuyGoodsMail(goodsId, ResHolder.getTemplate(ChargeGoodsTemplate.class, goodsId), null);
        return goodsId;
    }

    /**
     * 为军团添加军团礼物
     *
     * @param goodsTemplate 礼包表
     */
    private void sendClanGift(ChargeGoodsTemplate goodsTemplate) {
        if (this.getOwner().getClanId() == 0) {
            LOGGER.debug("midas_perf {} sendClanGift player has no clan, skip ClanGift", getPlayerId());
            return;
        }
        if (goodsTemplate.getClanGiftId() == 0) {
            LOGGER.error("midas_perf {} sendClanGift goods id={} has no clanGift, skip ClanGift", getPlayerId(), goodsTemplate.getId());
            return;
        }
        SsClanGift.AddClanGiftCmd.Builder addGiftCmd = SsClanGift.AddClanGiftCmd.newBuilder();
        addGiftCmd.setGiftItemId(goodsTemplate.getClanGiftId());
        addGiftCmd.setGiftRecord(
                ClanGiftRecordUtils.formBuyGoodsRecord(
                        this.getOwner().getSettingComponent().getSwitchStatus(CommonEnum.PlayerSwitchSettingType.PSST_HIDE_PAYINFO_FOR_CLANGIFT),
                        this.getOwner().getName(),
                        this.getPlayerId(),
                        goodsTemplate.getId())
        );

        this.ownerActor().tellCurClan(addGiftCmd.build());
    }

    private void sendBuyGoodsMail(int goodsId, ChargeGoodsTemplate goodsTemplate, List<Struct.ItemPair> unfixedItems) {
        StructMail.MailBuyGoodsData.Builder mailBuyGoodsData = StructMail.MailBuyGoodsData.newBuilder();
        mailBuyGoodsData.setChargeGoodsId(goodsId);
        // 填充非固定奖励
        if ((unfixedItems != null) && (!unfixedItems.isEmpty())) {
            mailBuyGoodsData.getSelectItemsBuilder().addAllDatas(unfixedItems);
        }
        // 填充固定奖励
        List<IntPairType> goodsReward = goodsTemplate.getGoodsRewardPairList();
        for (IntPairType reward : goodsReward) {
            Struct.ItemPair.Builder itemPairBuilder = Struct.ItemPair.newBuilder();
            itemPairBuilder.setItemTemplateId(reward.getKey()).setCount(reward.getValue());
            mailBuyGoodsData.getSelectItemsBuilder().addDatas(itemPairBuilder.build());
        }
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(goodsTemplate.getTriggerMailId())
                .setTitle(StructMail.MailShowTitle.newBuilder()
                        .setSubTitleData(Struct.DisplayData.newBuilder().setParams(Struct.DisplayParamList.newBuilder().addDatas(Struct.DisplayParam.newBuilder()
                                .setType(CommonEnum.DisplayParamType.DPT_CHARGE_GOODS_ID)
                                .setNumber(goodsId)
                                .build()).build()).build()).build()
                )
                .setContent(StructMail.MailContent.newBuilder()
                        .setBuyGoodsData(mailBuyGoodsData.build()).build()
                );
        LOGGER.info("midas_perf {} payment send goods mail: {}", getPlayerId(), goodsId);
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(getOwner().getPlayerId())
                .setZoneId(getOwner().getZoneId())
                .build();
        this.getOwner().getMailComponent().sendPersonalMail(receiver, params.build());
    }

    private void addDoneOrder(final String token, final String billno, final String openId, final String appMeta, final int goodsId) {
        final PlayerPaymentModelProp paymentModel = getOwner().getProp().getPaymentModel();
        // 避免重复装箱
        final long curTsMs = SystemClock.now();
        final String key = formMidasDoneGoodsOrderKey(token, billno, openId);
        paymentModel.addEmptyDoneGoodsOrders(key)
                .setBillno(billno)
                .setTsMs(curTsMs)
                .setToken(token)
                .setAppMeta(appMeta)
                .setGoodsId(goodsId);
        this.doneGoodOrderList.add(paymentModel.getDoneGoodsOrdersV(key));
        LOGGER.info("midas_perf {} addDoneOrder token={}, billno={}, openId={}, appMeta={}, goodsId={}", getPlayerId(), token, billno, openId, appMeta, goodsId);
        shrinkDoneGoodOrderList();
    }

    private void shrinkDoneGoodOrderList() {
        final PlayerPaymentModelProp paymentModel = getOwner().getProp().getPaymentModel();
        while (doneGoodOrderList.size() > MAX_DONE_GOODS_ORDER_SIZE) {
            final PlayerGoodsInfoProp oldestPlayerGoodsInfo = doneGoodOrderList.removeFirst();
            paymentModel.removeDoneGoodsOrdersV(oldestPlayerGoodsInfo.getKey());
        }
    }

    public long getSaveAmt() {
        return getOwner().getProp().getPaymentModel().getSaveAmt();
    }

    public Player_FakeRecharge_S2C handleFakeRecharge(Player_FakeRecharge_C2S msg) {
        if (ServerContext.isProdSvr()) {
            WechatLog.error("midas_perf {} prodSvr handleFakeRecharge !", getPlayerId());
            return Player_FakeRecharge_S2C.getDefaultInstance();
        }
        if (MidasConfig.fakeMidas()) {
            // 仅在模拟环境下处理
            final int chargeBaseId = msg.getChargeBaseId();
            ChargeBaseTemplate baseTemplate = ResHolder.findTemplate(ChargeBaseTemplate.class, chargeBaseId);
            if (baseTemplate == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
            if (ServerContext.isBanShuSvr()) {
                Player_JudgeRecharge_S2C res = getOwner().getAntiAddictionComponent().judgePay(baseTemplate.getChargeSdkId());
                if (res.getChargeSdkId() <= 0) {
                    throw new GeminiException(ErrorCode.ANTI_ADDICTION);
                }
            }
            LOGGER.warn("midas_perf {} payment fake recharge {}", getPlayerId(), msg);
            int diamond = baseTemplate.getDirectDiamond();
            PlayerPaymentModelProp model = getOwner().getProp().getPaymentModel();
            final long before = getOwner().getPurseComponent().count(CurrencyType.DIAMOND);
            final long after = before + diamond;
            refreshCurrency(MidasRespHeader.newBuilder()
                    .setBalance(after)
                    .setGenBalance(model.getGenBalance())
                    .build());
            getOwner().getQlogComponent().sendMoneyQLog(CurrencyType.DIAMOND.getNumber(), before, after, after - before, true, CommonEnum.Reason.ICR_CHARGE_BASE, "");
            refreshProp(MidasQueryAns.newBuilder()
                    .setSaveAmt(model.getSaveAmt() + diamond)
                    .setFirstSave(model.getFirstSave())
                    .setSaveSum(model.getSaveSum())
                    .setCostSum(model.getCostSum())
                    .setPresentSum(model.getPresentSum())
                    .build());
            ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, baseTemplate.getChargeSdkId());
            if (ServerContext.isBanShuSvr()) {
                getOwner().getAntiAddictionComponent().reportPay(sdkTemplate);
            }
        }
        return Player_FakeRecharge_S2C.getDefaultInstance();
    }

    /**
     * 清理礼包购买的历史记录
     * 十分危险的接口，调用前请确保一定有清理的必要
     *
     * @param goodsId 礼包id
     */
    public void removeGoodsBoughtHistory(int goodsId) {
        if (null == getOwner().getProp().getPaymentModel().getGoodsHistoryV(goodsId)) {
            LOGGER.info("midas_perf {} try clean goods {} bought history not exist, no need to remove", getPlayerId(), goodsId);
            return;
        }
        getOwner().getProp().getPaymentModel().removeGoodsHistoryV(goodsId);
        LOGGER.info("midas_perf {} success remove goods bought history for goods {}", getPlayerId(), goodsId);
    }

    /**
     * 从midas回调中的appMeta解析出token(安卓与IOS的appMeta不同)
     * appMeta: 服务器透传给客户端,客户端发给midas,midas再返回给服务端
     * 安卓: token
     * IOS: token*支付方式*平台渠道
     */
    String getTokenFromAppMeta(final String appMeta) {
        final int index = appMeta.indexOf("*");
        // 不存在*
        if (index < 0) {
            return appMeta;
        }
        return appMeta.substring(0, index);
    }

    String formAppMeta(final String token) {
        return StringUtils.format("{}_{}", token, this.getOwner().getZoneId());
    }

    /**
     * 从token中解析出id(前后兼容)
     * 旧token: {唯一id}
     * 新token: {唯一id}_{zoneId}
     */
    String getIdFromToken(final String token) {
        final int index = token.indexOf("_");
        // 不存在_
        if (index < 0) {
            return token;
        }
        return token.substring(0, index);
    }
}

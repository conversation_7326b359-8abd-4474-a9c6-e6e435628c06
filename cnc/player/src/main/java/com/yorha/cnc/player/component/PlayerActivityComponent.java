package com.yorha.cnc.player.component;

import com.google.common.collect.*;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.ActivityTimer;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.activity.unit.*;
import com.yorha.cnc.player.event.ActivityOpenEvent;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.activity.TickLikeTimer;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivitySchedule;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import javax.annotation.Nullable;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 玩家活动组件
 *
 * <AUTHOR>
 */
public class PlayerActivityComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityComponent.class);

    static {
        // 扫描所有PlayerEvent，静态监听
        final JavaClassScanner scanner = new JavaClassScanner();
        for (Class<? extends PlayerEvent> eventClazz : scanner.getSubTypesOf(PlayerEvent.class.getPackage().getName(), PlayerEvent.class)) {
            EntityEventHandlerHolder.register(eventClazz, PlayerActivityComponent::onEvent);
        }
    }

    /**
     * 所有活跃的活动都在这里
     */
    private final Map<Integer, PlayerActivity> activities = Maps.newHashMap();

    private final TickLikeTimer reloadTimer = new TickLikeTimer();
    private final Map<String, ActorTimer> timerMap = Maps.newHashMap();

    /**
     * event class name -> 关注的unit
     */
    private final Multimap<String, PlayerEventListener> eventListenerMap = HashMultimap.create();

    /**
     * 积分单元管理
     * 使用ArrayList替代LinkedList以减少内存分配开销
     */
    private final List<PlayerPointsComponent.Listener> pointsListener = Lists.newArrayList();
    /**
     * 登录流程中是否已经刷新过排期
     */
    private boolean reloaded = false;

    public PlayerActivityComponent(PlayerEntity owner) {
        super(owner);
    }

    private PlayerActivityModelProp prop() {
        return getOwner().getProp().getActivityModel();
    }

    @Override
    public void onLoad(boolean isRegister) {
        reloaded = true;
        reloadActivitySchedule();
    }

    @Override
    public void onLogin() {
        if (!reloaded) {
            // 完整登录时，在onload里面已经刷新过了
            reloadActivitySchedule();
            reloaded = true;
        }
    }

    @Override
    public void afterLogout() {
        // 下次小登录（不走onLoad）要刷一下排期
        reloaded = false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelAll();
    }

    /**
     * 取消所有定时器
     * 会在Player淘汰的时候调用
     */
    public void cancelAll() {
        this.reloadTimer.cancel();
        for (ActorTimer scheduleTask : timerMap.values()) {
            scheduleTask.cancel();
        }
        timerMap.clear();
    }

    /**
     * 重置活动,已接取的就改变不了了
     */
    public void resetActivity() {
        reloadActivitySchedule();
    }

    /**
     * 加载活动排期，为玩家添加触发器、启动定时器等，应实现为幂等操作
     */
    private void reloadActivitySchedule() {
        if (ServerContext.getServerStopStep() > 1) {
            // 停服中不再执行reload了，timer也不让加的
            return;
        }
        if (!ZoneContext.isServerOpen()) {
            // 没开服跳过
            LOGGER.info("reloadActivitySchedule end server is not open={}", ZoneContext.getServerOpenTsMs());
            return;
        }
        if (ServerContext.isDevEnv()) {
            // 没开服跳过
            LOGGER.info("reloadActivitySchedule ignore in dev env. {}", ServerContext.isDevEnv());
            return;
        }
        final long t0 = SystemClock.nowNative();
        final PlayerActivityModelProp model = prop();
        final Set<Integer> forceOffScheduleIds = ResHolder.getResService(ActivityResService.class).getForceOffScheduleIds();
        // 最优先处理活动强制下架
        for (int forceOffScheduleId : forceOffScheduleIds) {
            ActivityScheduleProp forceOffProp = model.getActSchedulesV(forceOffScheduleId);
            if (forceOffProp != null) {
                final int forceOffActId = forceOffProp.getActivity().getId();
                if (forceOffActId > 0) {
                    LOGGER.info("reloadActivitySchedule force close activity. {} {} {}", getOwner(), forceOffScheduleId, forceOffActId);
                    PlayerActivity loadedActivity = activities.get(forceOffActId);
                    if (loadedActivity != null) {
                        // 活动已经加载到内存中了，需要强制卸载，清理相关容器，这步做在卸载prop之前吧
                        loadedActivity.forceOff();
                    }
                    ActivityScheduleProp cloneProp = new ActivityScheduleProp();
                    cloneProp.mergeFromDb(forceOffProp.getCopyDbBuilder().build());
                    // 强制卸载prop，转移到另外一个容器中去，等待脚本处理问题活动的数据
                    // 因为无法判定活动到底是什么问题，所以每一个强制下架的活动都需要手动的脚本来处理活动数据
                    model.removeActSchedulesV(forceOffScheduleId);
                    ActivityScheduleProp existForceOffProp = model.getForceOffScheduleDataV(forceOffScheduleId);
                    if (existForceOffProp != null) {
                        LOGGER.error("reloadActivitySchedule force close activity, prop exist! {} {} {} existProp={} thisProp={}",
                                getOwner(), forceOffScheduleId, forceOffActId, existForceOffProp, cloneProp);
                    } else {
                        model.putForceOffScheduleDataV(cloneProp);
                    }
                }
            }
        }

        // load身上所有已经开启的排期的活动
        ArrayList<ActivityScheduleProp> asProps = Lists.newArrayList(model.getActSchedules().values());
        for (ActivityScheduleProp asProp : asProps) {
            try {
                ActivityProp activityProp = asProp.getActivity();
                PlayerActivity playerActivity = activities.get(activityProp.getId());
                if (playerActivity == null) {
                    if (activityProp.getId() <= 0) {
                        continue;
                    }
                    // prop有，但是内存没有，需要把活动new出来
                    ActivityTemplate template = ResHolder.getTemplate(ActivityTemplate.class, activityProp.getId());
                    loadActivity(asProp.getScheduleId(), null, template, activityProp);
                } else {
                    playerActivity.reload();
                }
            } catch (Exception e) {
                LOGGER.error("reloadActivitySchedule load scheduleId={} activityId={} failed ", asProp.getScheduleId(), asProp.getActivity().getId(), e);
            }
        }

        // 遍历排期配置，加载所有可以激活的活动
        tryOpenNewActivity(ResHolder.getResService(ActivityResService.class).allActivitySchedule());
        final long costMs = SystemClock.nowNative() - t0;
        LOGGER.info("{} reloadActivitySchedule over. cost:{}ms ", getOwner(), costMs);
    }

    private void tryOpenNewActivity(List<ActivitySchedule> schedules) {
        final Set<Integer> forceOffScheduleIds = ResHolder.getResService(ActivityResService.class).getForceOffScheduleIds();
        final PlayerActivityModelProp model = prop();
        final PlayerEntity player = getOwner();
        final long nowMs = SystemClock.now();
        final long nowSec = TimeUtils.ms2Second(nowMs);
        final Instant now = Instant.ofEpochMilli(nowMs);
        final long serverOpenTsMs = ZoneContext.getServerOpenTsMs();
        // 开服第一天算0
        final long zoneOpenDaysUntilNow = TimeUtils.getAbsNatureDaysBetween(serverOpenTsMs, nowMs);
        final int playerCityLevel = player.getCityLevel();
        final int serverZoneId = ServerContext.getZoneId();
        final int playerBornZoneId = player.getProp().getZoneModel().getBornZoneId();

        for (ActivitySchedule as : schedules) {
            try {
                ActivityScheduleTemplate scheduleTemplate = as.template;
                final int actScheduleId = scheduleTemplate.getId();
                if (forceOffScheduleIds.contains(actScheduleId)) {
                    continue;
                }
                if (model.getForceOffScheduleDataV(actScheduleId) != null) {
                    WechatLog.error("reloadActivitySchedule forceOffScheduleData exist! must be config reverted {}", actScheduleId);
                    continue;
                }
                if (model.getExpiredActSchedulesV(actScheduleId) != null) {
                    // 这次活动玩家已经接过并且已经玩完了
                    continue;
                }
                ActivityScheduleProp asProp = model.getActSchedulesV(actScheduleId);
                if (asProp != null && asProp.getActivity().getId() > 0) {
                    // 这个排期在玩家身上已经有活动了，不用再继续处理
                    continue;
                }
                // 开服天数检查
                if (!ActivityResService.isServerOpenLimitOk(scheduleTemplate, zoneOpenDaysUntilNow)) {
                    continue;
                }
                // 区服限制检查
                if (!ActivityResService.isZoneIdOk(scheduleTemplate, player.getZoneId(), now)) {
                    continue;
                }
                // 排期开启出生服检查，出生服!=当前服，跳过
                if (scheduleTemplate.getOriginalServerOpen() && serverZoneId != playerBornZoneId) {
                    continue;
                }

                // 虽然叫curOrNext，但是非循环活动只有一次cell，还是会返回这个cell的
                final ActivitySchedule.Cell curOrNext = curOrNext(player, now, as);
                if (curOrNext != null) {
                    if (curOrNext.expireTime.isBefore(now)) {
                        // 过期了不用管，已经接到身上的话上面会处理，没接到身上的话就是没参与过了
                        LOGGER.debug("reloadActivitySchedule activitySchedule expired will ignore {}", curOrNext.actId);
                    } else if (curOrNext.startTime.isAfter(now)) {
                        // 活动尚未开始
                        tryAddNextReloadTimer(curOrNext.startTime, actScheduleId, curOrNext.actId);
                    } else {
                        // 活动开始了
                        if (asProp == null) {
                            asProp = prop().addEmptyActSchedules(actScheduleId).setScheduleId(actScheduleId);
                        }
                        if (isPlayerLvReq(scheduleTemplate)) {
                            // 如果开启有玩家等级要求，需要记录一下上一次加载排期的时间，用来做到达等级要求后也不能中途开启活动
                            int lastLoadTsSec = asProp.getLastLoadTsSec();
                            asProp.setLastLoadTsSec((int) nowSec);
                            // 玩家等级限制检查
                            if (!ActivityResService.isPlayerLevelOk(scheduleTemplate, playerCityLevel)) {
                                continue;
                            }
                            // 活动开启的时候玩家等级不符合条件，中途就不能再开了
                            if (!canHalfOpenForPlayerLv(scheduleTemplate, curOrNext, lastLoadTsSec)) {
                                continue;
                            }
                        }
                        final long curOrNextStartSec = curOrNext.startTime.getEpochSecond();
                        // 上一期的结束时间
                        final long lastActEndSec = asProp.getLoopData().getLastAct().getEndTsSec();
                        final boolean isNewCell = curOrNextStartSec >= lastActEndSec;
                        if (isNewCell) {
                            // 活动正在周期内
                            openActivity(actScheduleId, curOrNext);
                        } else {
                            LOGGER.error("PlayerActivityComponent reloadActivitySchedule open activity failed. schId={} lastActEndSec={} curOrNextStartSec={}",
                                    scheduleTemplate.getId(), lastActEndSec, curOrNextStartSec);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("PlayerActivityComponent reloadActivitySchedule scheduleId={} error, ", as.template.getId(), e);
            }
        }
    }

    private boolean isPlayerLvReq(ActivityScheduleTemplate scheduleTemplate) {
        final List<IntPairType> lvReq = scheduleTemplate.getRequiredPlayerLevelPairList();
        return lvReq != null && !lvReq.isEmpty();
    }

    private boolean canHalfOpenForPlayerLv(ActivityScheduleTemplate scheduleTemplate, ActivitySchedule.Cell curOrNext, int lastLoadTsSec) {
        final long curOrNextStartSec = curOrNext.startTime.getEpochSecond();
        final boolean loaded = lastLoadTsSec >= curOrNextStartSec;
        // 活动开始后已经加载过了，且不能中途加入，活动不开启
        return !loaded || scheduleTemplate.getHalfwayOpen();
    }

    @Nullable
    private static ActivitySchedule.Cell curOrNext(PlayerEntity player, Instant now, ActivitySchedule as) {
        final long serverOpenTsMs = ZoneContext.getServerOpenTsMs();
        final Instant zoneOpenTime = Instant.ofEpochMilli(serverOpenTsMs);
        final long playerCreateTsMs = player.getProp().getCreateTime();
        final ActivityScheduleTemplate template = as.template;
        CommonEnum.ActivityOpenType openType = template.getOpenType();

        ActivitySchedule.Cell curOrNext = null;
        switch (openType) {
            case AOT_FROM_ZONE_OPEN:
                curOrNext = ActivitySchedule.FromZoneOpen.cell(as, zoneOpenTime);
                break;
            case AOT_FROM_PLAYER_CREATE:
                curOrNext = ActivitySchedule.FromPlayerCreate.cell(as, playerCreateTsMs);
                break;
            case AOT_ON_DATE:
                curOrNext = ActivitySchedule.OnDate.cell(as);
                break;
            case AOT_ON_DATE_LOOP:
                curOrNext = ActivitySchedule.OnDateLoop.curOrNext(as, now);
                break;
            case AOT_ON_DATE_ZONE_LOOP:
                curOrNext = ActivitySchedule.OnDateZoneLoop.curOrNext(as, zoneOpenTime, now);
                break;
            case AOT_ZONE_LOOP:
                curOrNext = ActivitySchedule.ZoneLoop.curOrNext(as, zoneOpenTime, now);
                break;
            default:
                WechatLog.error("activity openType no conf: {}", template.getId());
        }
        return curOrNext;
    }

    public void openActivity(int actScheduleId, ActivitySchedule.Cell cell) throws Exception {
        final int activityId = cell.actId;
        PlayerActivity exist = activities.get(activityId);
        if (exist != null) {
            WechatLog.error("{} openActivity already exist! {}", getOwner(), activityId);
            return;
        }
        ActivityTemplate activityTemplate = cell.template;

        // 初始化Activity的Prop
        ActivityScheduleProp scheduleProp = initActScheduleProp(actScheduleId, cell);
        loadActivity(actScheduleId, null, activityTemplate, scheduleProp.getActivity());
        new ActivityOpenEvent(getOwner(), activityTemplate.getId()).dispatch();
    }

    private ActivityScheduleProp initActScheduleProp(int actScheduleId, ActivitySchedule.Cell cell) {
        ActivityScheduleProp scheduleProp = prop().getActSchedulesV(actScheduleId);
        if (scheduleProp == null) {
            LOGGER.warn("{} newActScheduleProp not exist! {}", getOwner(), actScheduleId);
            scheduleProp = prop().addEmptyActSchedules(actScheduleId).setScheduleId(actScheduleId);
        }
        PlayerActivity.initProp(scheduleProp.getActivity(), cell.actId, cell.startTime, cell.expireTime);
        return scheduleProp;
    }

    public PlayerActivity loadActivity(int actScheduleId, PlayerActivity father, ActivityTemplate activityTemplate, ActivityProp prop) throws Exception {
        final int activityId = activityTemplate.getId();
        if (activities.containsKey(activityId)) {
            WechatLog.error("{} loadActivity already exist activityId={}", getOwner(), activityId);
            return null;
        }
        PlayerActivity activity = new PlayerActivity(getOwner(), father, actScheduleId, activityTemplate, prop);
        activity.reload();
        // reload可能会过期，只有还开启的活动才需要加入到activitiesMap
        if (activity.isActive()) {
            activities.put(activityId, activity);
        }
        return activity;
    }

    /**
     * 需要保证传入的 actScheduleId+activityId+taskId 是唯一的
     */
    public ActivityTimer addTimer(int actScheduleId, int activityId, String taskId, Duration delay, Runnable runnable) {
        // 每个task做一个10s的随机偏移
        final long initDelayMs = delay.toMillis() + RandomUtils.nextInt(0, Constants.N_10_000);

        final String scheduleTaskId = actScheduleId + "." + activityId + "." + taskId;
        ActorTimer task = ownerActor().addTimer(
                scheduleTaskId,
                TimerReasonType.PLAYER_ACTIVITY,
                () -> {
                    runnable.run();
                    timerMap.remove(scheduleTaskId);
                },
                initDelayMs, TimeUnit.MILLISECONDS);
        if (task == null) {
            return null;
        }
        if (timerMap.containsKey(task.getName())) {
            LOGGER.error("put activity schedule task duplicated. {}", task.getName());
        }
        timerMap.put(task.getName(), task);
        return new ActivityTimer(getOwner(), task);
    }

    public void forceRemoveActivity(PlayerActivity activity) {
        activities.remove(activity.getActivityId());
    }

    public void expireActivity(PlayerActivity activity) {
        activities.remove(activity.getActivityId());
        if (activity.getFather() == null) {
            // 整个活动排期过期
            expireActSchedule(activity.getActScheduleId());
        }
    }

    public void expireActSchedule(int actScheduleId) {
        LOGGER.info("expired activity schedule scheduleId={}", actScheduleId);
        ActivityScheduleTemplate scheduleTemplate = ResHolder.getTemplate(ActivityScheduleTemplate.class, actScheduleId);
        if (ActivityResService.isNotLoopSchedule(scheduleTemplate.getOpenType())) {
            // 一次性活动，开启后过期，清空活动数据，加入过期容器
            prop().removeActSchedulesV(actScheduleId);
            prop().putExpiredActSchedulesV(new ActivityScheduleProp().setScheduleId(actScheduleId));
        } else {
            // 循环活动，开启后过期，清空活动数据，记录开启关闭时间，用于后续循环判断用
            ActivityScheduleProp old = prop().getActSchedulesV(actScheduleId);
            prop().removeActSchedulesV(actScheduleId);
            // 现在没有clearActivity这样的接口
            ActivityScheduleProp newAsProp = prop().addEmptyActSchedules(actScheduleId)
                    .setScheduleId(actScheduleId)
                    .setLastLoadTsSec(old.getLastLoadTsSec());
            newAsProp.getLoopData().getLastAct()
                    .setStartTsSec(old.getActivity().getStartTsSec())
                    .setEndTsSec(old.getActivity().getEndTsSec());
        }

    }

    public void watchScore(PlayerPointsComponent.Listener listener) {
        this.pointsListener.add(listener);
        getOwner().getPointsComponent().register("activity", listener);
    }

    public void unwatchScore(PlayerPointsComponent.Listener listener) {
        this.pointsListener.remove(listener);
        getOwner().getPointsComponent().remove(listener);
    }

    public GeneratedMessageV3 handleTakeReward(com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_C2S msg) {
        final int activityId = msg.getActivityId();
        BasePlayerActivityUnit activityUnit = checkedGetUnit(activityId, msg.getUnitId());

        Player_ActivityTakeReward_S2C.Builder response = Player_ActivityTakeReward_S2C.newBuilder();
        activityUnit.handleTakeReward(msg.getRewardKey(), response);
        return response.build();
    }

    public void handleBuyActivityGood(int activityId, int unitId, int goodId) {
        PlayerActivityStoreUnit unit = checkedGetUnit(PlayerActivityStoreUnit.class, activityId, unitId);
        unit.bugGood(goodId);
    }

    public void addScore(String scoreId, int num, String action, String subjectId) {
        for (PlayerPointsComponent.Listener listener : pointsListener) {
            if (listener instanceof PlayerActivityScoreRewardUnit) {
                PlayerActivityScoreRewardUnit unit = (PlayerActivityScoreRewardUnit) listener;
                if (unit.getScoreId().equals(scoreId)) {
                    unit.addScore(num, action, subjectId);
                }
            }
        }
    }

    public void fullActivityByGm() {
    }

    private static <T extends PlayerEvent> void onEvent(T event) {
        PlayerActivityComponent component = event.getPlayer().getActivityComponent();
        if (event instanceof PlayerDayRefreshEvent || event instanceof MainCityUpgradeStaticEvent) {
            // 活动排期开放逻辑加入循环活动机制之后，计算每个活动的schedule变得肥肠复杂和易于出错
            // 引入每日和升级后重新reload的机制，一是简化逻辑，二是方便reload
            component.reloadActivitySchedule();
        }
        Collection<PlayerEventListener> listeners = component.eventListenerMap.get(event.getClass().getName());
        for (PlayerEventListener listener : listeners) {
            try {
                listener.onEvent(event);
            } catch (Exception e) {
                WechatLog.error("{} activity onEvent error {}", event.getPlayer(), event, e);
            }
        }
    }

    public void onMigrate() {
        for (PlayerActivity activity : Lists.newArrayList(activities.values())) {
            activity.onMigrate();
        }
    }

    public void listenOn(Class<? extends PlayerEvent> eClazz, PlayerEventListener listener) {
        eventListenerMap.put(eClazz.getName(), listener);
    }

    public void cancelListen(Class<? extends PlayerEvent> eClazz, PlayerEventListener listener) {
        eventListenerMap.remove(eClazz.getName(), listener);
    }

    @Nullable
    public PlayerActivity findActivity(int actId) {
        return activities.get(actId);
    }

    @Nullable
    public BasePlayerActivityUnit findActiveUnit(int actId, int unitId) {
        PlayerActivity activity = activities.get(actId);
        if (activity == null) {
            return null;
        }
        return activity.findUnit(unitId);
    }

    public BasePlayerActivityUnit checkedGetUnit(int actId, int unitId) {
        PlayerActivity activity = activities.get(actId);
        if (activity == null) {
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        BasePlayerActivityUnit activityUnit = activity.findUnit(unitId);
        if (activityUnit == null) {
            throw new GeminiException(ErrorCode.ACTIVITY_UNIT_NOT_EXIST);
        }
        return activityUnit;
    }

    public <T> T checkedGetUnit(Class<T> clazz, int actId, int unitId) {
        BasePlayerActivityUnit unit = checkedGetUnit(actId, unitId);
        if (!clazz.isAssignableFrom(unit.getClass())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        //noinspection unchecked
        return (T) unit;
    }

    public void handleBuySecondQueue(int actId, int unitId) {
        PlayerSecondQueueUnit secondQueueUnit = checkedGetUnit(PlayerSecondQueueUnit.class, actId, unitId);
        secondQueueUnit.handleBuySecondQueue();
    }

    public void removeTimer(ActorTimer timer) {
        // 这里remove的时候不存在也是正常的
        timerMap.remove(timer.getName());
    }

    public void tryAddNextReloadTimer(Instant next, int scheduleId, int actId) {
        long delay = Duration.between(SystemClock.nowInstant(), next).plusSeconds(RandomUtils.nextInt(1, 5)).toMillis();
        if (delay >= TimeUnit.DAYS.toMillis(365)) {
            // 大于1年就不定时了
            LOGGER.warn("tryAddNextReloadTimer add too long timer scheduleId={} actId={} next={}", scheduleId, actId, next);
            return;
        }
        this.reloadTimer.tryAddTickTimer(next, () -> ownerActor().addTimer(
                TimerReasonType.PLAYER_ACTIVITY_RELOAD,
                this::reloadActivitySchedule,
                delay,
                TimeUnit.MILLISECONDS
        ));
    }

    public String getWeekMonthCardInfo() {
        Map<Integer, Long> weekMonthCardInfo = new HashMap<>();
        PlayerActivityModelProp prop = getOwner().getProp().getActivityModel();
        for (ActivityScheduleProp asProp : Lists.newArrayList(prop.getActSchedules().values())) {
            ActivityProp activityProp = asProp.getActivity();
            ActivityUnitProp activityUnitProp = activityProp.getSpecUnit();
            if (activityUnitProp != null && activityUnitProp.getUnitType() == CommonEnum.ActivityUnitType.AUT_WEEK_MONTH_CARD && activityUnitProp.getUnitId() > 0) {
                ActivityWeekMonthCardUnitProp weekMonthCardUnitProp = activityUnitProp.getWeekMonthCardUnit();
                if (weekMonthCardUnitProp.getCardId() != 0) {
                    long expireTime = weekMonthCardUnitProp.getExpireTsMs();
                    long now = SystemClock.now();
                    long remainTime = 0;
                    if (expireTime > now) {
                        remainTime = TimeUtils.ms2Second(expireTime - now);
                    }
                    weekMonthCardInfo.put(weekMonthCardUnitProp.getCardId(), remainTime);
                }
            }
        }
        return JsonUtils.toJsonString(weekMonthCardInfo);
    }

    @Override
    public void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        if (updatedTemplates.contains(ActivityScheduleTemplate.class)
                || updatedTemplates.contains(ActivityTemplate.class)
                || updatedTemplates.contains(ActivityForceOffScheduleTemplate.class)) {
            this.reloadActivitySchedule();
        }
    }

    public void gmReload() {
        this.reloadActivitySchedule();
    }

    /**
     * 危险接口，根据类型查找玩家身上有的unit，如果有且仅有这个一个unit，返回这个unit，否则返回null
     *
     * @param unitType 单元类型
     */
    @Nullable
    public BasePlayerActivityUnit gmFindUniqueActiveUnitByType(CommonEnum.ActivityUnitType unitType) {
        for (PlayerActivity activity : activities.values()) {
            if (activity.getTemplate().getSpecUnitType() != unitType) {
                continue;
            }
            if (activity.onlyOneUnit()) {
                return activity.getOnlyOneUnit();
            }
        }
        return null;
    }

    public PlayerStoreRatingUnit findStoreRatingUnit(int actId) {
        PlayerActivity activity = findActivity(actId);
        if (activity == null) {
            LOGGER.error("PlayerActivityComponent findStoreRatingUnit, activity not exist, actId={}", actId);
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        PlayerStoreRatingUnit storeRatingUnit = activity.findFirstUnitInTopFatherActOf(PlayerStoreRatingUnit.class);
        if (storeRatingUnit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "storeRating unit not found");
        }
        return storeRatingUnit;
    }

    public PlayerBestCommanderTotalRankUnit findBestCommanderTotalRankUnit(int actId, int unitId) {
        BasePlayerActivityUnit unit = findActiveUnit(actId, unitId);
        if (unit == null) {
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        if (!(unit instanceof PlayerBestCommanderTotalRankUnit)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "PlayerBestCommanderTotalRankUnit unit not found");
        }
        return (PlayerBestCommanderTotalRankUnit) unit;
    }


    public Set<ActivityResService.ActivityCalendar> getActivityCalendars(Instant now) {
        final int serverZoneId = ServerContext.getZoneId();
        final long serverOpenTsMs = ZoneContext.getServerOpenTsMs();
        final long playerCreateTsMs = getOwner().getProp().getCreateTime();
        Set<ActivityResService.ActivityCalendar> ret = Sets.newHashSet();
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("getActivityCalendars server is not open={}", serverOpenTsMs);
            return ret;
        }
        Instant twoDaysBeforeNow = now.minus(2, ChronoUnit.DAYS);
        // 找出近7天内的所有活动
        for (int i = 0; i < 7; i++) {
            Instant today = TimeUtils.getDayStartInstant(twoDaysBeforeNow.plus(i, ChronoUnit.DAYS));
            Set<ActivityResService.ActivityCalendar> todayActivities = ActivityResService.getTodayActivityCalendar(serverOpenTsMs, today, serverZoneId, playerCreateTsMs);
            for (ActivityResService.ActivityCalendar calendar : todayActivities) {
                if (!calendar.canShowInCalendar(now)) {
                    continue;
                }
                boolean canOpen = filterByPlayer(calendar);
                if (!canOpen) {
                    continue;
                }
                ret.add(calendar);
            }
        }
        return ret;
    }

    private boolean filterByPlayer(ActivityResService.ActivityCalendar calendar) {
        final int serverZoneId = ServerContext.getZoneId();
        final int playerBornZoneId = this.getOwner().getProp().getZoneModel().getBornZoneId();
        final int playerCityLevel = getOwner().getCityLevel();

        ActivityScheduleTemplate scheduleTemplate = ResHolder.getResService(ActivityResService.class).getScheduleTemplate(calendar.scheduleId);
        // 排期开启出生服检查，出生服!=当前服，跳过
        if (scheduleTemplate.getOriginalServerOpen() && serverZoneId != playerBornZoneId) {
            return false;
        }
        if (isPlayerLvReq(scheduleTemplate)) {
            // 玩家等级限制检查
            if (!ActivityResService.isPlayerLevelOk(scheduleTemplate, playerCityLevel)) {
                return false;
            }
            // 如果开启有玩家等级要求，需要记录一下上一次加载排期的时间，用来做到达等级要求后也不能中途开启活动
            ActivityScheduleProp asProp = prop().getActSchedulesV(scheduleTemplate.getId());
            if (asProp == null) {
                return true;
            }
            int lastLoadTsSec = asProp.getLastLoadTsSec();
            // 活动开启的时候玩家等级不符合条件，中途就不能再开了
            return canHalfOpenForPlayerLv(scheduleTemplate, calendar.cell, lastLoadTsSec);
        }
        return true;
    }

    public boolean isReloaded() {
        return reloaded;
    }

    public void natashaShopExchange(int shopId) {
        NatashaShopTemplate shopTemplate = ResHolder.getTemplate(NatashaShopTemplate.class, shopId);
        // 道具够不够
        if (shopTemplate.getPricePairList() != null && !shopTemplate.getPricePairList().isEmpty()) {
            for (IntPairType pair : shopTemplate.getPricePairList()) {
                if (!getOwner().getItemComponent().hasEnough(pair.getKey(), pair.getValue())) {
                    throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
                }
            }
        }
        // 检查兑换条件，如果奖励是永久装扮，如果已经拥有了不允许兑换
        if (shopTemplate.getItemIdPairList() != null && !shopTemplate.getItemIdPairList().isEmpty()) {
            for (IntPairType intPairType : shopTemplate.getItemIdPairList()) {
                ItemTemplate itemTemplate = ResHolder.getTemplate(ItemTemplate.class, intPairType.getKey());
                if (itemTemplate.getEffectType() == CommonEnum.ItemUseType.CITY_DRESS_VALUE || itemTemplate.getEffectType() == CommonEnum.ItemUseType.CITY_NAMEPLATE_VALUE) {
                    // 只判断永久装扮
                    DressTemplate dressTemplate = ResHolder.getTemplate(DressTemplate.class, itemTemplate.getEffectValue());
                    if (dressTemplate.getContinueTime() == 0) {
                        // 背包里有永久装扮道具，不给换
                        boolean hasDress = getOwner().getItemComponent().hasEnough(itemTemplate.getId(), 1);
                        if (hasDress) {
                            throw new GeminiException(ErrorCode.DRESS_ALREADY_HAVE);
                        }
                        // 皮肤系统里有永久装扮道具，不给换
                        if (itemTemplate.getEffectType() == CommonEnum.ItemUseType.CITY_DRESS_VALUE) {
                            if (getOwner().getCityDressComponent().hasEternalCityDress(dressTemplate.getDressId())) {
                                throw new GeminiException(ErrorCode.DRESS_ALREADY_HAVE);
                            }

                        }
                        // 铭牌系统里有永久装扮道具，不给换
                        if (itemTemplate.getEffectType() == CommonEnum.ItemUseType.CITY_NAMEPLATE_VALUE) {
                            if (getOwner().getCityNameplateComponent().hasEternalCityNameplate(dressTemplate.getDressId())) {
                                throw new GeminiException(ErrorCode.DRESS_ALREADY_HAVE);
                            }
                        }
                    }
                }
            }
        }
        // 扣道具
        if (shopTemplate.getPricePairList() != null && !shopTemplate.getPricePairList().isEmpty()) {
            for (IntPairType pair : shopTemplate.getPricePairList()) {
                getOwner().getItemComponent().consume(pair.getKey(), pair.getValue(), CommonEnum.Reason.ICR_LOTTERY_NATASHA_SHOP_EXCHANGE, String.valueOf(shopId));
            }
        }
        // 发奖
        if (shopTemplate.getItemIdPairList() != null && !shopTemplate.getItemIdPairList().isEmpty()) {
            AssetPackage.Builder rewardBuilder = AssetPackage.builder();
            rewardBuilder.plusItems(shopTemplate.getItemIdPairList());
            getOwner().getAssetComponent().give(rewardBuilder.build(), CommonEnum.Reason.ICR_LOTTERY_NATASHA_SHOP_EXCHANGE, String.valueOf(shopId));
        }
    }
}

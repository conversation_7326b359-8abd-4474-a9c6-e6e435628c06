package com.yorha.cnc.player.component;


import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerKillScoreAddEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.KillScoreHelper;
import com.yorha.game.gen.prop.DataRecordUnitProp;
import com.yorha.game.gen.prop.PlayerDataRecordProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 处理PlayerEntity数据统计
 *
 * <AUTHOR>
 */

public class PlayerDataRecordComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerDataRecordComponent.class);

    private PlayerDataRecordProp dataRecordProp;

    public PlayerDataRecordComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        this.dataRecordProp = getOwner().getProp().getPlayerDataRecord();
    }

    /**
     * 更新非场景相关数据统计
     */
    public void updateDataRecord(CommonEnum.DataRecordType recordType, CommonEnum.DataRecordCalcType recordCalcType, long value) {
        try {
            DataRecordUnitProp recordProp = dataRecordProp.getRecordsV(recordType.getNumber());
            if (recordProp == null) {
                recordProp = dataRecordProp.addEmptyRecords(recordType.getNumber());
            }
            doUpdate(recordType, recordCalcType, recordProp, value);
        } catch (Exception e) {
            LOGGER.error("PlayerDataRecordComponent updateDataRecord fail", e);
        }
    }

    public long getRecordValue(CommonEnum.DataRecordType recordType) {
        DataRecordUnitProp record = dataRecordProp.getRecordsV(recordType.getNumber());
        return record == null ? 0 : record.getValue();
    }

    private void doUpdate(CommonEnum.DataRecordType recordType, CommonEnum.DataRecordCalcType recordCalcType, DataRecordUnitProp recordProp, long value) {
        long oldValue = recordProp.getValue();

        switch (recordCalcType) {
            case DRCT_ADD: {
                recordProp.setValue(oldValue + value);
                break;
            }
            case DRCT_SUB: {
                recordProp.setValue(oldValue - value);
                break;
            }
            case DRCT_COVER: {
                recordProp.setValue(value);
                break;
            }
            case DRCT_MAX: {
                if (oldValue < value) {
                    recordProp.setValue(value);
                }
                break;
            }
            default:
                LOGGER.error("recordType:{} recordCalcType:{} is unsupported", recordType, recordCalcType);
                throw new GeminiException(ErrorCode.CALC_TYPE_ERROR);
        }

        LOGGER.debug("player:{} collection type:{} value:({}) -> ({})", getOwner(), recordType, oldValue, recordProp.getValue());
    }

    /**
     * 刷新击杀积分
     * 根据player身上的数据统计项，计算出新的玩家击杀积分，替换击杀积分，不是增量
     */
    public void refreshKillScore() {
        long recordSum = KillScoreHelper.getKillScoreByStatistic(getOwner().getStatisticComponent().getAllSecondStatisticPB(StatisticEnum.SOLDIER_KILL_TOTAL));
        long beforeRefreshSum = getOwner().getDataRecordComponent().getRecordValue(CommonEnum.DataRecordType.DRT_KILL_SCORE);
        updateDataRecord(CommonEnum.DataRecordType.DRT_KILL_SCORE, CommonEnum.DataRecordCalcType.DRCT_MAX, recordSum);
        long afterRefreshSum = getOwner().getDataRecordComponent().getRecordValue(CommonEnum.DataRecordType.DRT_KILL_SCORE);

        // 击杀积分仅能增加，减少的情况是调用接口bug，相等的情况下不需要更新到clan和scene
        if (afterRefreshSum < beforeRefreshSum) {
            LOGGER.error("afterRefreshSum {} less than beforeRefreshSum {}, maybe need check the logic", afterRefreshSum, beforeRefreshSum);
            return;
        }
        if (afterRefreshSum == beforeRefreshSum) {
            LOGGER.debug("afterRefreshSum {} equal with beforeRefreshSum, no need to sync", afterRefreshSum);
            return;
        }
        // 抛出玩家击杀积分增加的事件
        new PlayerKillScoreAddEvent(getOwner(), afterRefreshSum).dispatch();
        // 玩家有军团时，同步给军团
        if (getOwner().getPlayerClanComponent().isInClan()) {
            StructClan.ClanMember.Builder ssBuilder = StructClan.ClanMember.newBuilder().setKillScore(afterRefreshSum);
            getOwner().getPlayerClanComponent().updateClanMemberInfo(ssBuilder.build());
        }
        // 通知PlayerCard更新
        getOwner().getPlayerPropComponent().updatePlayerCardCache(false);
    }
}

package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.common.actor.PlayerDungeonService;
import com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PlayerDungeonServiceImpl implements PlayerDungeonService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerDungeonServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerDungeonServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }


    @Override
    public void handleOnDungeonDestroyCmd(OnDungeonDestroyCmd ask) {
        LOGGER.info("handleOnDungeonDestroyCmd {} {} {}", playerActor, ask.getType(), ask.getSceneId());
        playerActor.getOrLoadEntity().getSceneMgrComponent().onDungeonDestroy(ask.getType(), ask.getSceneId());
    }
}

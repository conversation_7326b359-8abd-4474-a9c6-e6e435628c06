package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 英雄战力提升
 * param1: 战力值
 *
 * <AUTHOR>
 */
public class HeroPowerIncreaseChecker extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int powerConfig = taskParams.get(0);
        if (event instanceof PlayerPowerIncreaseEvent) {
            PlayerPowerIncreaseEvent powerIncreaseEvent = (PlayerPowerIncreaseEvent) event;
            if (powerIncreaseEvent.getPowerReason() == CommonEnum.PowerType.PT_HERO) {
                if (prop.getProcess() < powerConfig) {
                    long l = prop.getProcess() + powerIncreaseEvent.getPower();
                    prop.setProcess(Math.min(powerConfig, (int) l));
                }
            }
        }
        return prop.getProcess() >= powerConfig;
    }
}

package com.yorha.cnc.player.gm.command;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.DebugGroup;
import com.yorha.proto.CommonEnum.QueueTaskType;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class OperationQueue implements PlayerGmCommand {

    /**
     * operationQueue taskId=3519399281569482 operationType=speed type=1 itemId=1001010 value=10   加速某个任务value秒
     * operationQueue taskId=3519399281569482 operationType=cancel type=1  取消某个任务
     * operationQueue taskId=3519399281569482 operationType=finish type=1  立刻完成某个任务
     * operationQueue taskId=3519399281569482 operationType=unlock type=1 value=500 解锁第二队列value秒
     */
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        long taskId = Long.parseLong(args.get("taskId"));
        String operationType = args.get("operationType");
        QueueTaskType type = QueueTaskType.forNumber(Integer.parseInt(args.get("type")));

        int itemId = 0;
        if (args.containsKey("itemId")) {
            itemId = Integer.parseInt(args.get("itemId"));
        }

        int value = 0;
        if (args.containsKey("value")) {
            value = Integer.parseInt(args.get("value"));
        }
        PlayerEntity playerEntity = actor.getEntity();
        switch (operationType) {
            case "speed": {
                playerEntity.getPlayerQueueTaskComponent().speedUpQueueTask(type, taskId, Lists.newArrayList(IntPairType.makePair(itemId, value)));
                break;
            }
            case "cancel": {
                playerEntity.getPlayerQueueTaskComponent().tryCancelQueueTask(type, taskId);
                break;
            }
            case "unlock": {
                playerEntity.getPlayerQueueTaskComponent().unLockQueue(false, type, SystemClock.now() + value);
                break;
            }
            case "finish": {
                playerEntity.getPlayerQueueTaskComponent().tryFinishQueueTask(type, taskId);
                break;
            }
            default:
        }
    }

    @Override
    public String showHelp() {
        return "operationQueue";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_INNER_BUILD;
    }
}

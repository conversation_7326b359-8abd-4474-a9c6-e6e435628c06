package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerBaseChargeEvent;
import com.yorha.cnc.player.event.task.PlayerBuyGoodsEvent;
import com.yorha.cnc.player.task.AbstractTaskHandler;
import com.yorha.cnc.player.task.LotteryNatashaTaskHandler;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityLotteryNatashaResService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityLotteryNatashaUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncNatashabulletRecruit;
import qlog.flow.QlogCncNatashabulletTaskFlow;
import res.template.ActivityLotteryTemplate;
import res.template.LotteryDrawTemplate;
import res.template.LotteryNatashaTemplate;
import res.template.TaskDailyeventTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.yorha.cnc.player.task.AbstractTaskHandler.setNextTaskState;

/**
 * 娜塔莎武器库
 * 任务获取道具，用道具转转盘
 *
 * <AUTHOR>
 * @date 2024/5/6
 */
public class PlayerLotteryNatashaUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerLotteryNatashaUnit.class);

    private static final String DAILY_TASK_POOL_NAME = "LotteryNatashaDailyTask";
    private static final String RECHARGE_TASK_POOL_NAME = "LotteryNatashaRechargeTask";
    private LotteryNatashaTaskHandler dailyTaskHandler;
    private LotteryNatashaTaskHandler rechargeTaskHandler;

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerDayRefreshEvent.class,
            PlayerBaseChargeEvent.class,
            PlayerBuyGoodsEvent.class
    );

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_LOTTERY_NATASHA, (owner, prop, template) ->
                new PlayerLotteryNatashaUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerLotteryNatashaUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
        LotteryNatashaTemplate template = ResHolder.findTemplate(LotteryNatashaTemplate.class, ownerActivity.getActivityId());
        if (template == null) {
            LOGGER.error("PlayerLotteryNatashaUnit load LotteryNatashaTemplate is null activityId={}", ownerActivity.getActivityId());
            return;
        }
        // 初始化日刷任务
        if (dailyTaskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(dailyTaskHandler);
            dailyTaskHandler.clearAllAttention(StringUtils.format("PlayerLotteryNatashaUnit load clear attention activityId={} taskPoolName={}",
                    ownerActivity.getActivityId(), DAILY_TASK_POOL_NAME));
        }
        dailyTaskHandler = initTaskHandler(DAILY_TASK_POOL_NAME, unitProp.getLotteryNatasha().getDailyTasks(), Lists.newArrayList(template.getTaskPoolId()));

        // 初始化充值任务
        if (rechargeTaskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(rechargeTaskHandler);
            rechargeTaskHandler.clearAllAttention(StringUtils.format("PlayerLotteryNatashaUnit load clear attention activityId={} taskPoolName={}",
                    ownerActivity.getActivityId(), RECHARGE_TASK_POOL_NAME));
        }
        rechargeTaskHandler = initTaskHandler(RECHARGE_TASK_POOL_NAME, unitProp.getLotteryNatasha().getRechargeTasks(), template.getTaskChargeidList());
        LOGGER.info("PlayerLotteryNatashaUnit dailyTask={} rechargeTask={}",
                unitProp.getLotteryNatasha().getDailyTasks().keySet(), unitProp.getLotteryNatasha().getRechargeTasks().keySet());
    }

    private LotteryNatashaTaskHandler initTaskHandler(String taskPoolName, Int32TaskInfoMapProp taskProp, List<Integer> taskIds) {
        LotteryNatashaTaskHandler taskHandler = new LotteryNatashaTaskHandler(ownerActivity.getPlayer(), taskProp, taskPoolName, ownerActivity.getActivityId(), this);
        taskHandler.openAttention();
        // 先把prop里的load进来
        taskHandler.loadAllTask();
        // 再找表里的新增的task
        HashSet<Integer> newTaskIds = new HashSet<>(taskIds);
        newTaskIds.removeAll(taskProp.keySet());
        taskHandler.addBatchTask(newTaskIds);
        ownerActivity.getPlayer().getTaskComponent().addTaskHandler(taskHandler);
        return taskHandler;
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        sendExpiredReward();
        clearTask(StringUtils.format("PlayerLotteryNatashaUnit activityExpire activityId={}", ownerActivity.getActivityId()));
    }

    @Override
    public void forceOffImpl() {
        clearTask(StringUtils.format("PlayerLotteryNatashaUnit activityForceOff activityId={}", ownerActivity.getActivityId()));
    }

    private void clearTask(String reason) {
        if (dailyTaskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(dailyTaskHandler);
            dailyTaskHandler.clearAllAttention(reason);
        }
        if (rechargeTaskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(rechargeTaskHandler);
            rechargeTaskHandler.clearAllAttention(reason);
        }
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerDayRefreshEvent) {
            // 日刷任务
            refreshDailyTask();
        } else if (event instanceof PlayerBaseChargeEvent || event instanceof PlayerBuyGoodsEvent) {
            // 标记活动期间发生充值
            markRecharge(event);
        }
    }

    private void refreshDailyTask() {
        LOGGER.info("PlayerLotteryNatashaUnit refreshDailyTask activityId={}", ownerActivity.getActivityId());
        LotteryNatashaTemplate template = ResHolder.findTemplate(LotteryNatashaTemplate.class, ownerActivity.getActivityId());
        if (template == null) {
            LOGGER.error("PlayerLotteryNatashaUnit refreshDailyTask LotteryNatashaTemplate is null activityId={}", ownerActivity.getActivityId());
            return;
        }
        unitProp.getLotteryNatasha().getDailyTasks().clear();
        dailyTaskHandler.clearAllAttention("PlayerLotteryNatashaUnit dailyTaskRefresh");
        dailyTaskHandler.openAttention();
        dailyTaskHandler.addBatchTask(Sets.newHashSet(template.getTaskPoolId()));
    }

    private void markRecharge(PlayerEvent event) {
        if (unitProp.getLotteryNatasha().getIsRecharged()) {
            return;
        }
        // 必须带有黄金才能算充值，策划说的
        if (event instanceof PlayerBuyGoodsEvent) {
            if (((PlayerBuyGoodsEvent) event).getDirectDiamond() <= 0) {
                return;
            }
        }
        LOGGER.info("PlayerLotteryNatashaUnit markRecharged activityId={} mark recharge", ownerActivity.getActivityId());
        unitProp.getLotteryNatasha().setIsRecharged(true);
    }

    /**
     * 抽奖
     *
     * @param times 抽几次
     * @return 奖励
     */
    public AssetPackage handleDraw(int times) {
        PlayerEntity player = player();
        int activityId = this.ownerActivity.getActivityId();
        LotteryNatashaTemplate natashaTemplate = ResHolder.getTemplate(LotteryNatashaTemplate.class, activityId);
        ActivityLotteryTemplate lotteryTemplate = ResHolder.getTemplate(ActivityLotteryTemplate.class, natashaTemplate.getActivityLotteryid());
        LotteryDrawTemplate normalDrawTemplate = ResHolder.getResService(ActivityLotteryNatashaResService.class).getNormalDrawTemplate(lotteryTemplate);
        // 道具够不够，用默认奖池的消耗来判断，策划说三个奖池消耗一样，配置校验也做了
        if (normalDrawTemplate.getCostItemsPairList() != null && !normalDrawTemplate.getCostItemsPairList().isEmpty()) {
            for (IntPairType pair : normalDrawTemplate.getCostItemsPairList()) {
                if (!player.getItemComponent().hasEnough(pair.getKey(), Math.multiplyExact(pair.getValue(), times))) {
                    throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
                }
            }
        }
        AssetPackage.Builder rewardDisplayBuilder = AssetPackage.builder();
        for (int i = 0; i < times; i++) {
            // 抽一次奖
            AssetPackage assetPackage = handleDrawOnce();
            rewardDisplayBuilder.plusStandalone(assetPackage);
        }
        return rewardDisplayBuilder.build();
    }

    private AssetPackage handleDrawOnce() {
        PlayerEntity player = player();
        int activityId = this.ownerActivity.getActivityId();
        ActivityLotteryNatashaUnitProp lotteryNatashaProp = unitProp.getLotteryNatasha();
        LotteryNatashaTemplate natashaTemplate = ResHolder.getTemplate(LotteryNatashaTemplate.class, activityId);
        ActivityLotteryTemplate lotteryTemplate = ResHolder.getTemplate(ActivityLotteryTemplate.class, natashaTemplate.getActivityLotteryid());

        int oldDrawNumSmall = lotteryNatashaProp.getDrawNumSmall();
        int oldDrawNumLarge = lotteryNatashaProp.getDrawNumLarge();
        // 算奖池,默认,小保底,大保底
        LotteryDrawTemplate drawTemplate = ResHolder.getResService(ActivityLotteryNatashaResService.class).getDrawTemplate(lotteryTemplate, oldDrawNumSmall, oldDrawNumLarge);
        // roll奖励
        List<ItemReward> rewards = ResHolder.getResService(ItemResService.class).randomReward(drawTemplate.getRewardId());

        // 扣道具
        if (drawTemplate.getCostItemsPairList() != null && !drawTemplate.getCostItemsPairList().isEmpty()) {
            for (IntPairType pair : drawTemplate.getCostItemsPairList()) {
                player.getItemComponent().consume(pair.getKey(), pair.getValue(), CommonEnum.Reason.ICR_LOTTERY_NATASHA, String.valueOf(activityId));
            }
        }

        // 重置保底次数
        AssetPackage.Builder rewardBuilder = AssetPackage.builder();
        boolean smallPoolHit = false;
        boolean largePoolHit = false;
        for (ItemReward itemReward : rewards) {
            if (hitGuarantee(drawTemplate.getSmallGuaranteePairList(), itemReward.getItemTemplateId(), itemReward.getCount())) {
                // 小保底奖励命中,重置小保底次数
                lotteryNatashaProp.setDrawNumSmall(0);
                smallPoolHit = true;
            }
            if (hitGuarantee(drawTemplate.getBigGuaranteePairList(), itemReward.getItemTemplateId(), itemReward.getCount())) {
                // 大保底命中,重置大保底次数
                lotteryNatashaProp.setDrawNumLarge(0);
                largePoolHit = true;
            }
            rewardBuilder.plusItem(itemReward.getItemTemplateId(), itemReward.getCount());
        }

        lotteryNatashaProp.setTotalDrawTimes(lotteryNatashaProp.getTotalDrawTimes() + 1);
        // 没中,计数
        if (!smallPoolHit) {
            lotteryNatashaProp.setDrawNumSmall(oldDrawNumSmall + 1);
        }
        if (!largePoolHit) {
            lotteryNatashaProp.setDrawNumLarge(oldDrawNumLarge + 1);
        }
        // 发奖
        AssetPackage rewardPack = rewardBuilder.build();
        player.getAssetComponent().give(rewardPack, CommonEnum.Reason.ICR_LOTTERY_NATASHA, String.valueOf(activityId));

        // qlog
        QlogCncNatashabulletRecruit.init(player().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setActivityid(ownerActivity.getActivityId())
                .setTotalCount(unitProp.getLotteryNatasha().getTotalDrawTimes())
                .sendToQlog();

        LOGGER.info("handleDrawOnce end activityId={} drawPool={} drawNumSmall={} drawNumLarge={} totalTimes={} smallPoolHit={} largePoolHit={}",
                activityId, drawTemplate.getId(), lotteryNatashaProp.getDrawNumSmall(), lotteryNatashaProp.getDrawNumLarge(),
                lotteryNatashaProp.getTotalDrawTimes(), smallPoolHit, largePoolHit);
        return rewardPack;
    }

    private static boolean hitGuarantee(List<IntPairType> guarantee, int itemId, int count) {
        for (IntPairType pair : guarantee) {
            if (pair.getKey() == itemId && pair.getValue() == count) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        int dailyTaskId = key.getNatashaRewardKey().getDailyTaskId();
        if (dailyTaskId > 0) {
            // 日刷奖励
            handleTaskReward(dailyTaskId, unitProp.getLotteryNatasha().getDailyTasks(), rsp);
            return;
        }
        int rechargeTaskId = key.getNatashaRewardKey().getRechargeTaskId();
        if (rechargeTaskId > 0) {
            // 充值奖励
            handleTaskReward(rechargeTaskId, unitProp.getLotteryNatasha().getRechargeTasks(), rsp);
            return;
        }
        int boxId = key.getNatashaRewardKey().getBoxId();
        if (boxId > 0) {
            // 宝箱奖励
            handleBoxReward(boxId, rsp);
            return;
        }
        throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "natasha lottery take reward param wrong!");
    }

    private void handleTaskReward(int taskId, Int32TaskInfoMapProp tasks, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        TaskInfoProp taskInfoProp = tasks.get(taskId);
        if (taskInfoProp == null) {
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_EXIST);
        }
        if (AbstractTaskHandler.isUnCompletedState(taskInfoProp)) {
            // 活动任务未完成
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_NOT_COMPLATE);
        }
        TaskDailyeventTemplate taskTemplate = ResHolder.getTemplate(TaskDailyeventTemplate.class, taskId);
        // 标记领奖
        setNextTaskState(taskInfoProp);
        List<IntPairType> rewardPairList = taskTemplate.getRewardPairList();
        if (rewardPairList == null || rewardPairList.isEmpty()) {
            LOGGER.warn("PlayerLotteryNatashaUnit handleTaskReward activityId={} taskId={} reward is empty", ownerActivity.getActivityId(), taskId);
            return;
        }
        // 领奖
        AssetPackage rewardPackage = AssetPackage.builder().plusItems(rewardPairList).build();
        ownerActivity.getPlayer().getAssetComponent().give(rewardPackage, CommonEnum.Reason.ICR_LOTTERY_NATASHA, ownerActivity.getActivityId() + "_" + taskId);
        rsp.setReward(rewardPackage.toPb());

        // qlog
        QlogCncNatashabulletTaskFlow.init(player().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setActivityid(ownerActivity.getActivityId())
                .setAction("collect_task")
                .setITaskID(taskId)
                .sendToQlog();
    }

    private void handleBoxReward(int boxId, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        if (!unitProp.getLotteryNatasha().getIsRecharged()) {
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_NOT_COMPLATE);
        }
        if (unitProp.getLotteryNatasha().isReceivedBoxIdContains(boxId)) {
            throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
        }
        // 时间校验
        int boxIndex = boxId - 1;
        LotteryNatashaTemplate lotteryNatashaTemplate = ResHolder.getTemplate(LotteryNatashaTemplate.class, ownerActivity.getActivityId());
        Set<Integer> activeBoxIndex = getActiveBoxIndex(lotteryNatashaTemplate, ownerActivity.getProp().getStartTsSec());
        if (!activeBoxIndex.contains(boxIndex)) {
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_NOT_COMPLATE);
        }
        // 标记
        unitProp.getLotteryNatasha().addReceivedBoxId(boxId);
        // 领奖
        AssetPackage rewardPackage = ResHolder.getResService(ItemResService.class).randomReward2Pack(lotteryNatashaTemplate.getTimeRewardTripleList().get(boxIndex).getValue2());
        ownerActivity.getPlayer().getAssetComponent().give(rewardPackage, CommonEnum.Reason.ICR_LOTTERY_NATASHA, ownerActivity.getActivityId() + "_" + boxId);
        rsp.setReward(rewardPackage.toPb());
    }

    /**
     * 过期补发没领的奖励
     */
    private void sendExpiredReward() {
        LotteryNatashaTemplate lotteryNatashaTemplate = ResHolder.findTemplate(LotteryNatashaTemplate.class, ownerActivity.getActivityId());
        if (lotteryNatashaTemplate == null) {
            LOGGER.error("PlayerLotteryNatashaUnit sendExpiredReward activityId={} LotteryNatashaTemplate is null", ownerActivity.getActivityId());
            return;
        }

        List<IntPairType> reward = Lists.newArrayList();
        List<Integer> unReceivedTask = Lists.newArrayList();
        // 找出没领奖的充值任务
        for (Map.Entry<Integer, TaskInfoProp> entry : unitProp.getLotteryNatasha().getRechargeTasks().entrySet()) {
            if (entry.getValue().getStatus() != CommonEnum.TaskStatus.AT_NOT_REWARD) {
                continue;
            }
            unReceivedTask.add(entry.getKey());
            TaskDailyeventTemplate taskTemplate = ResHolder.findTemplate(TaskDailyeventTemplate.class, entry.getKey());
            if (taskTemplate == null) {
                LOGGER.error("PlayerLotteryNatashaUnit sendExpiredReward activityId={} taskId={} TaskDailyeventTemplate is null",
                        ownerActivity.getActivityId(), entry.getKey());
                continue;
            }
            reward.addAll(taskTemplate.getRewardPairList());
        }
        // 找出没领的宝箱
        List<Integer> unReceivedBoxIds = Lists.newArrayList();
        if (unitProp.getLotteryNatasha().getIsRecharged()) {
            Set<Integer> activeBoxIndex = getActiveBoxIndex(lotteryNatashaTemplate, ownerActivity.getProp().getStartTsSec());
            for (Integer boxIndex : activeBoxIndex) {
                // ！！！这里要 +1，因为外面是从1开始的
                int boxId = boxIndex + 1;
                if (unitProp.getLotteryNatasha().getReceivedBoxId().contains(boxId)) {
                    continue;
                }
                unReceivedBoxIds.add(boxId);
                List<ItemReward> rewardList = ResHolder.getResService(ItemResService.class).randomReward(lotteryNatashaTemplate.getTimeRewardTripleList().get(boxIndex).getValue2());
                for (ItemReward itemReward : rewardList) {
                    reward.add(IntPairType.makePair(itemReward.getItemTemplateId(), itemReward.getCount()));
                }
            }
        }

        if (reward.isEmpty()) {
            return;
        }
        LOGGER.info("PlayerLotteryNatashaUnit sendExpiredReward activityId={} unReceivedTask={} unReceivedBox={}",
                ownerActivity.getActivityId(), unReceivedTask, unReceivedBoxIds);

        // 发邮件
        ActivityLotteryTemplate template = ResHolder.findTemplate(ActivityLotteryTemplate.class, lotteryNatashaTemplate.getActivityLotteryid());
        if (template == null) {
            LOGGER.error("PlayerLotteryNatashaUnit sendExpiredReward activityId={} lotteryId={} ActivityLotteryTemplate is null",
                    ownerActivity.getActivityId(), lotteryNatashaTemplate.getActivityLotteryid());
            return;
        }
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(template.getStageRewardMailId());
        Struct.ItemPairList.Builder itemBuilder = Struct.ItemPairList.newBuilder();
        for (IntPairType pair : reward) {
            itemBuilder.addDatas(Struct.ItemPair.newBuilder()
                    .setItemTemplateId(pair.getKey())
                    .setCount(pair.getValue())
                    .build());
        }
        params.setItemReward(itemBuilder);
        CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(ownerActivity.getPlayer().getPlayerId())
                .setZoneId(ownerActivity.getPlayer().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, params.build());
    }

    /**
     * 返回活动开始到此刻的可开启的宝箱下标
     * 注意是下标!! 从0开始
     */
    private static Set<Integer> getActiveBoxIndex(LotteryNatashaTemplate template, long activityStartTsSec) {
        Set<Integer> ret = Sets.newHashSet();
        // 活动开始到现在过了多少小时
        int hours = TimeUtils.subDateToHour(TimeUnit.SECONDS.toMillis(activityStartTsSec), SystemClock.now());
        for (int i = 0; i < template.getTimeRewardTripleList().size(); i++) {
            if (hours < template.getTimeRewardTripleList().get(i).getKey()) {
                continue;
            }
            ret.add(i);
        }
        return ret;
    }

    public void onTaskFinish(int configId) {
        QlogCncNatashabulletTaskFlow.init(player().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setActivityid(ownerActivity.getActivityId())
                .setAction("complete_task")
                .setITaskID(configId)
                .sendToQlog();
    }
}

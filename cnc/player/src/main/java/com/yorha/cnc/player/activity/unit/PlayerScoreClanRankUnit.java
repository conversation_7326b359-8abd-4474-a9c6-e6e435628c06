package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.Sets;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.component.PlayerPointsComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanActivity;
import res.template.ActivityScoreRankTemplate;
import res.template.ActivityTemplate;

import java.util.Set;

/**
 * 积分联盟排名
 *
 * <AUTHOR>
 */
public class PlayerScoreClanRankUnit extends BasePlayerActivityUnit implements PlayerPointsComponent.Listener {

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_SCORE_CLAN_RANK, (owner, prop, template) ->
                new PlayerScoreClanRankUnit(owner, prop.getScoreRankUnit())
        );
    }

    public PlayerScoreClanRankUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void onMigrate() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public void load(boolean isInitial) {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
        ownerActivity.getPlayer().getActivityComponent().watchScore(this);
    }

    @Override
    public void onExpire() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public void forceOffImpl() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public Set<Integer> focusPointsIds() {
        ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId());
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, activityTemplate.getScoreRankId());
        return Sets.newHashSet(scoreRankTemplate.getScoreIdsList());
    }

    @Override
    public void addPoints(int pointsTemplateId, long addPoints) {
        if (player().getClanId() == 0) {
            return;
        }
        long id = buildActIdWithStartTsSec(ownerActivity.getActivityId(), ownerActivity.getProp().getStartTsSec());
        // 直接发给联盟加分
        SsClanActivity.AddClanActScoreCmd.Builder builder = SsClanActivity.AddClanActScoreCmd.newBuilder();
        builder.setAddScore((int) addPoints).setCurActId(id).setType(CommonEnum.ActivityUnitType.AUT_SCORE_CLAN_RANK);
        player().ownerActor().tellCurClan(builder.build());
    }

    public long buildActIdWithStartTsSec(int actId, int startTsSec) {
        return (((long) actId) << 32) | startTsSec;
    }
}

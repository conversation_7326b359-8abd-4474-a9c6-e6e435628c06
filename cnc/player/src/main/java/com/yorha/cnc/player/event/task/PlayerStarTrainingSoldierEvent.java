package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 造兵开始事件
 *
 * <AUTHOR>
 */
public class PlayerStarTrainingSoldierEvent extends PlayerTaskEvent {
    private final int count;
    private final int soldier;

    public int getCount() {
        return count;
    }

    public int getSoldier() {
        return soldier;
    }

    public PlayerStarTrainingSoldierEvent(PlayerEntity entity, int count, int soldier) {
        super(entity);
        this.count = count;
        this.soldier = soldier;
    }

}

package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 完成当前所有任务
 *
 * <AUTHOR>
 */
public class CompleteTasks implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String type = args.get("type");
        String idStr = args.get("id");

        if (StringUtils.isEmpty(type)) {
            actor.getEntity().getTaskComponent().completeTasksWithGm();
            return;
        }
        if (idStr == null) {
            actor.getEntity().getTaskComponent().completeTasksByTypeWithGm(type);
            return;
        }
        int id = Integer.parseInt(idStr);
        if (id <= 0) {
            throw new GeminiException("未指定id");
        }
        actor.getEntity().getTaskComponent().completeTasksByTypeWithGm(type, id);
    }

    @Override
    public String showHelp() {
        return "CompleteTasks type=task_dailyevent id=211030001";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_TASK;
    }
}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.unit.PlayerContinuesGiftUnit;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;

/**
 * 连续礼包折扣卡
 *
 * <AUTHOR>
 */
public class ContinuesGiftDiscountItem extends AbstractUsableItem {
    public ContinuesGiftDiscountItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        final int actId = getTemplate().getEffectId();
        final int unitId = getTemplate().getEffectValue();
        final PlayerContinuesGiftUnit unit = playerEntity.getActivityComponent().checkedGetUnit(PlayerContinuesGiftUnit.class, actId, unitId);
        if (unit.getDiscountItem() != itemTemplateId) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "discount item not equal");
        }
        if (unit.alreadyDiscount()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "already discount");
        }
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        final int actId = getTemplate().getEffectId();
        final int unitId = getTemplate().getEffectValue();
        final PlayerContinuesGiftUnit unit = playerEntity.getActivityComponent().checkedGetUnit(PlayerContinuesGiftUnit.class, actId, unitId);
        if (unit.getDiscountItem() != itemTemplateId) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "discount item not equal");
        }
        unit.useDiscountItem();
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {

    }
}

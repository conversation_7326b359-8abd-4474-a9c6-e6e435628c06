package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.activity.trigger.AbstractActivityTrigger;
import com.yorha.cnc.player.activity.trigger.ActivityTriggerMgr;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ActivityTriggerUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.Int32TriggerInfoMapProp;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActTriggerListTemplate;
import res.template.ActivityTriggerPoolTemplate;

import java.util.*;

/**
 * 触发器活动
 *
 * <AUTHOR>
 */
public abstract class PlayerTriggerUnit extends BasePlayerActivityUnit implements PlayerEventListener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerTriggerUnit.class);
    private final Map<Class<? extends PlayerEvent>, Set<Integer>> attentionMap = new HashMap<>();

    public PlayerTriggerUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    protected void init() {
    }


    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
        final int activityId = ownerActivity.getActivityId();
        LOGGER.info("PlayerTriggerUnit load start {}", activityId);
        final Map<Integer, ActivityTriggerPoolTemplate> triggerMap = ResHolder.getInstance().getMap(ActivityTriggerPoolTemplate.class);
        final List<Integer> triggers = getTriggerList();
        attentionMap.clear();
        for (int triggerId : triggers) {
            if (!triggerMap.containsKey(triggerId)) {
                LOGGER.error("PlayerTriggerUnit load not exist trigger {} {}", activityId, triggerId);
                continue;
            }
            final ActivityTriggerPoolTemplate triggerTemplate = triggerMap.get(triggerId);
            final AbstractActivityTrigger trigger = ActivityTriggerMgr.getInstance().get(triggerTemplate.getTriggerType());
            final List<Class<? extends PlayerEvent>> attentionEvents = trigger.getAttentionEvent();
            for (Class<? extends PlayerEvent> event : attentionEvents) {
                attentionMap.computeIfAbsent(event, k -> new HashSet<>()).add(triggerId);
            }
        }
        LOGGER.info("PlayerTriggerUnit load end {} {}", activityId, triggers);
    }

    @Override
    public void onExpire() {
        LOGGER.info("PlayerTriggerUnit onExpire");
    }


    @Override
    public boolean isFinished() {
        return false;
    }


    private ActivityTriggerUnitProp getTriggerUnitProp() {
        return unitProp.getTriggerunit();
    }

    private List<Integer> getTriggerList() {
        final int activityId = ownerActivity.getActivityId();
        final Map<Integer, ActTriggerListTemplate> triggerlistMap = ResHolder.getInstance().getMap(ActTriggerListTemplate.class);
        if (!triggerlistMap.containsKey(activityId)) {
            return Collections.emptyList();
        }
        return triggerlistMap.get(activityId).getTriggerListList();
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return new ArrayList<>(attentionMap.keySet());
    }

    @Override
    public void onEvent(PlayerEvent event) {
        final int activityId = ownerActivity.getActivityId();
        LOGGER.info("PlayerTriggerUnit onEvent {} event={}", activityId, event);
        Set<Integer> triggers = attentionMap.get(event.getClass());
        if (triggers == null) {
            return;
        }
        if (triggers.isEmpty()) {
            return;
        }
        final Map<Integer, ActivityTriggerPoolTemplate> triggerTemplateMap = ResHolder.getInstance().getMap(ActivityTriggerPoolTemplate.class);
        final Int32TriggerInfoMapProp triggerInfoMapProp = getTriggerUnitProp().getTriggers();
        for (int triggerId : triggers) {
            try {
                final ActivityTriggerPoolTemplate actTriggerTemplate = triggerTemplateMap.get(triggerId);
                final CommonEnum.ActivityUnitTriggerType triggerType = actTriggerTemplate.getTriggerType();
                final AbstractActivityTrigger trigger = ActivityTriggerMgr.getInstance().get(triggerType);
                final TriggerInfoProp triggerInfoProp = triggerInfoMapProp.computeIfAbsent(triggerId, k -> new TriggerInfoProp().setTriggerId(triggerId));
                if ((actTriggerTemplate.getTriggerTimes() != -1) && (triggerInfoProp.getTriggerTime() >= actTriggerTemplate.getTriggerTimes())) {
                    LOGGER.info("PlayerTriggerUnit onEvent, reach trigger times limit, can not trigger, triggerId={}, limit={}, triggerTimes={}",
                            triggerId, actTriggerTemplate.getTriggerTimes(), triggerInfoProp.getTriggerTime());
                    continue;
                }
                final boolean isSatisfied = trigger.onTrigger(event, triggerId, triggerInfoProp);
                LOGGER.info("PlayerTriggerUnit onEvent {} {} triggerId {} isSatisfied {}", activityId, event, triggerId, isSatisfied);
                if (!isSatisfied) {
                    continue;
                }
                triggerSatisfiedImpl(triggerId, event);
            } catch (Exception e) {
                LOGGER.error("PlayerTriggerUnit onEvent {} {} failed triggerId {} ", activityId, event, triggerId, e);
            }
        }
    }

    protected abstract void triggerSatisfiedImpl(int triggerId, PlayerEvent event);
}

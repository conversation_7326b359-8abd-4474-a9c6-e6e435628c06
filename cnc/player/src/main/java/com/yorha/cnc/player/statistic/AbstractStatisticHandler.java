package com.yorha.cnc.player.statistic;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.SingleStatisticProp;

/**
 * 玩家统计项抽象处理类
 * <p>
 * 提供checkKey来辅助筛选自己专属 PlayerStatisticData
 * 子类需要找到并管理自己的 PlayerStatisticData
 *
 * <AUTHOR>
 */
public abstract class AbstractStatisticHandler {

    private final PlayerEntity entity;

    private final StatisticEnum type;


    public AbstractStatisticHandler(PlayerEntity entity, StatisticEnum type) {
        this.entity = entity;
        this.type = type;
    }

    public PlayerEntity getOwner() {
        return entity;
    }

    /**
     * 重置数据
     */
    abstract void resetValue();

    /**
     * 获取统计枚举
     */
    StatisticEnum getStatisticEnum() {
        return type;
    }

    /**
     * 根据计算方式、原始值、修正值得到结果值
     *
     * @param prop  基础数据
     * @param value 修正值
     */
    protected void recordCalc(SingleStatisticProp prop, long value) {
        long finValue = 0;
        long base = prop.getValue();
        switch (getStatisticEnum().getCalcType()) {
            case DRCT_ADD: {
                finValue = base + value;
                break;
            }
            case DRCT_MAX: {
                finValue = Math.max(base, value);
                break;
            }
            case DRCT_COVER: {
                finValue = value;
                break;
            }
            case DRCT_SUB: {
                finValue = Math.max(0, base - value);
                break;
            }
            default: {
                throw new GeminiException(ErrorCode.CALC_TYPE_ERROR);
            }
        }
        prop.setValue(finValue);
    }

}

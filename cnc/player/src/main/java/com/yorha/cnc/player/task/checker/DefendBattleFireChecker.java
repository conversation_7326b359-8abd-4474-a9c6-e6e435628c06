package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.DefendBattleFireEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.DEFEND_FINISH;

public class DefendBattleFire<PERSON><PERSON><PERSON> extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(DefendBattleFireEvent.class),
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        final int require = taskParams.getFirst();

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE: {
                if (event instanceof DefendBattleFireEvent) {
                    DefendBattleFireEvent e = (DefendBattleFireEvent) event;
                    prop.setProcess(Math.min(require, prop.getProcess() + e.getCount()));
                }
                break;
            }
            case TCT_CREATE: {
                long num = event.getPlayer().getStatisticComponent().getSingleStatistic(DEFEND_FINISH);
                prop.setProcess(Math.min(require, (int) num));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= require;
    }
}
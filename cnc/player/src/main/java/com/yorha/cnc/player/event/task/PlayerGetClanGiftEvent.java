package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家领取军团礼物事件
 * 军团礼物 包括 普通 和 稀有，不包括珍藏礼物
 * 一键领取仅会抛出一次事件
 *
 * <AUTHOR>
 */
public class PlayerGetClanGiftEvent extends PlayerTaskEvent {
    private final int times;

    public PlayerGetClanGiftEvent(PlayerEntity entity, final int times) {
        super(entity);
        this.times = times;
    }

    public int getTimes() {
        return times;
    }
}

package com.yorha.cnc.player.activity;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.common.activity.ActivityPropBase;
import com.yorha.common.activity.BaseActivity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.helper.ActivityHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.Pair;
import com.yorha.game.gen.prop.ActivityProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;
import res.template.ActivityRecycleTemplate;
import res.template.ActivityTemplate;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import static com.yorha.proto.CommonEnum.ActivityStatus;
import static com.yorha.proto.CommonEnum.ActivityUnitType;

/**
 * 玩家身上的活动，统管本活动的生命周期、属性等
 * <p>
 * 活动加载顺序为：
 * 状态更改 -> 加载unit -> 加载子活动
 * expire顺序：
 * 状态更改 -> expire子活动 -> expire unit
 */
public class PlayerActivity extends BaseActivity<PlayerActivity, BasePlayerActivityUnit> {
    private static final Logger LOGGER = LogManager.getLogger(PlayerActivity.class);

    private final PlayerEntity player;
    private final ActivityProp prop;
    private final ActivityPropBase.ActivityPropWrapper propDelegate;

    public PlayerActivity(PlayerEntity player, PlayerActivity father, int actScheduleId, ActivityTemplate template, ActivityProp prop) {
        super(father, actScheduleId, template);
        this.player = player;
        this.prop = prop;
        this.propDelegate = new ActivityPropBase.ActivityPropWrapper(prop);
    }

    @Override
    protected ActivityPropBase baseProp() {
        return this.propDelegate;
    }

    @Override
    protected Object owner() {
        return player;
    }

    @Override
    protected void tryAddReloadTimer(Instant reloadTime) {
        player.getActivityComponent().tryAddNextReloadTimer(reloadTime, actScheduleId, activityId);
    }

    public static void initProp(ActivityProp prop, int activityId, Instant startTime, Instant endTime) {
        prop.setId(activityId)
                .setStartTsSec((int) startTime.getEpochSecond())
                .setStatus(ActivityStatus.ACS_NONE)
                .setStatusEnterTsSec((int) startTime.getEpochSecond())
                .setEndTsSec((int) endTime.getEpochSecond());
    }

    public static ActivityProp newProp(int activityId, Instant startTime, Instant expireTime) {
        ActivityProp prop = new ActivityProp();
        initProp(prop, activityId, startTime, expireTime);
        return prop;
    }

    @Override
    protected void loadUnits(ActivityTemplate activityTemplate, boolean isInitial) {
        for (ActivityUnitType unitType : getAllUnitTypes(activityTemplate)) {
            BasePlayerActivityUnit unit = ActivityUnitFactory.get(unitType).create(this, prop, activityTemplate);
            unit.init(unitType);
            unit.load(isInitial);
            registerUnit(unit.getUnitId(), unit);
        }
    }

    @Override
    protected void reloadUnits() {
        // player在内存中，不反复reload了。但是每次登录要reload
        if (player.getActivityComponent().isReloaded()) {
            return;
        }
        for (Map.Entry<Integer, BasePlayerActivityUnit> entry : unitMap.entrySet()) {
            BasePlayerActivityUnit unit = entry.getValue();
            unit.load(false);
        }
    }

    private List<ActivityUnitType> getAllUnitTypes(ActivityTemplate activityTemplate) {
        List<ActivityUnitType> units = Lists.newArrayList();
        units.addAll(ActivityResService.parseCommonUnitTypes(activityTemplate));
        ActivityUnitType specUnitType = activityTemplate.getSpecUnitType();
        if (specUnitType != null && specUnitType != ActivityUnitType.AUT_NONE) {
            units.add(specUnitType);
        }
        return units;
    }

    @Override
    protected void registerUnit(int unitId, BasePlayerActivityUnit unit) {
        super.registerUnit(unitId, unit);

        if (unit instanceof PlayerEventListener) {
            PlayerEventListener asListener = (PlayerEventListener) unit;
            List<Class<? extends PlayerEvent>> followList = asListener.followList();
            for (Class<? extends PlayerEvent> eClazz : followList) {
                player.getActivityComponent().listenOn(eClazz, asListener);
            }
        }
    }

    @Nullable
    @Override
    protected PlayerActivity tryLoadChildAct(ActivityTemplate childTemplate, Instant childStartTime, Instant childExpireTime) throws Exception {
        ActivityProp childActProp = getOrCreateChildProp(childTemplate.getId(), childStartTime, childExpireTime);
        if (childActProp.getStatus() == ActivityStatus.ACS_EXPIRED) {
            // 已经执行过expire的不再加载
            return null;
        }
        return player.getActivityComponent().loadActivity(actScheduleId, this, childTemplate, childActProp);
    }

    private ActivityProp getOrCreateChildProp(int childActivityId, Instant childStartTime, Instant childExpireTime) {
        ActivityProp childActProp = prop.getChildActivitiesV(childActivityId);
        if (childActProp == null) {
            childActProp = newProp(childActivityId, childStartTime, childExpireTime);
            prop.putChildActivitiesV(childActProp);
        }
        return childActProp;
    }

    @Override
    protected void expireImpl() {
        recycleOrRecoup("expire");
        player.getActivityComponent().expireActivity(this);
    }

    /**
     * 活动过期时回收并补偿道具
     * 仅支持个人活动
     */
    protected void recycleOrRecoup(String reason) {
        final ActivityResService ars = ResHolder.getResService(ActivityResService.class);
        List<ActivityRecycleTemplate> recycleRule = ars.getRecycleRule(getActivityId());
        if (recycleRule == null) {
            return;
        }
        for (ActivityRecycleTemplate recycleTemplate : recycleRule) {
            Pair<AssetPackage, List<Struct.ItemPair>> assetPackageListPair =
                    ActivityHelper.buildRecycleOrRecoup((key) -> player.getItemComponent().getItemNum(key), recycleTemplate);
            AssetPackage cost = assetPackageListPair.getFirst();
            List<Struct.ItemPair> giver = assetPackageListPair.getSecond();
            LOGGER.info("PlayerActivity recycleOrRecoup actId={} recycleId={} cost={} giver={} reason={}",
                    recycleTemplate.getActivityId(), recycleTemplate.getId(), cost, giver, reason);
            player.getAssetComponent().consume(cost, CommonEnum.Reason.ICR_RECYCLE, "act_expire" + activityId);

            if (giver.isEmpty()) {
                continue;
            }
            if (recycleTemplate.getRecycleMail() <= 0) {
                continue;
            }
            StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
            params.setMailTemplateId(recycleTemplate.getRecycleMail());
            params.getItemRewardBuilder().addAllDatas(giver);
            final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                    .setPlayerId(player.getPlayerId())
                    .setZoneId(player.getZoneId())
                    .build();
            player.getMailComponent().sendPersonalMail(receiver, params.build());
        }
    }

    public void onMigrate() {
        recycleOrRecoup("migrate");
        for (BasePlayerActivityUnit unit : unitMap.values()) {
            unit.onMigrate();
        }
    }

    public PlayerEntity getPlayer() {
        return player;
    }

    @Override
    public String toString() {
        return "PlayerActivity{" +
                "player=" + player +
                ", actScheduleId='" + actScheduleId + '\'' +
                ", activityId='" + activityId + '\'' +
                '}';
    }

    public int allocateUnitId() {
        prop.setMaxUnitId(prop.getMaxUnitId() + 1);
        return prop.getMaxUnitId();
    }

    public ActivityProp getProp() {
        return prop;
    }

    @Override
    public boolean allFinished() {
        return children.values().stream().allMatch(PlayerActivity::allFinished)
                && unitMap.values().stream().allMatch(BasePlayerActivityUnit::isFinished);
    }

    @Override
    public void forceOffImpl() {
        player.getActivityComponent().forceRemoveActivity(this);
    }

    public void tryExpireOnUnitFinished() {
        ActivityTemplate template = getTemplate();
        if (template.getAutoExpire() && allFinished()) {
            expire();
        }
    }

    public <TYPE extends BasePlayerActivityUnit> TYPE findUnitOf(Class<TYPE> clazz) {
        for (BasePlayerActivityUnit unit : this.unitMap.values()) {
            if (clazz.isAssignableFrom(unit.getClass())) {
                //noinspection unchecked
                return (TYPE) unit;
            }
        }
        return null;
    }

    @Override
    protected void afterChildExpired(int activityId) {
        tryExpireOnUnitFinished();
    }
}
package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerHeroStarChangeEvent;
import com.yorha.cnc.player.event.task.PlayerHeroUnLockEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 指定英雄达到x星
 * param1: 英雄id
 * param2: 星级
 *
 * <AUTHOR>
 */
public class PlayerHeroStarChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerHeroStarChangeEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            PlayerHeroUnLockEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.get(0);
        int param2 = taskParams.get(1);

        int heroStar = event.getPlayer().getHeroComponent().getHero(param1).getStar();
        prop.setProcess(Math.min(heroStar, param2));

        return prop.getProcess() >= param2;
    }


}

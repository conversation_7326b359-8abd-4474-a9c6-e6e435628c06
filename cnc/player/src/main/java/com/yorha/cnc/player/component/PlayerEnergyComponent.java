package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.cnc.player.event.task.PlayerAddEnergyEvent;
import com.yorha.cnc.player.event.task.PlayerConsumeEnergyEvent;
import com.yorha.cnc.player.item.use.impl.AddEnergyItem;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.commander.CommanderResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.PlayerEnergyModelProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.SsSceneDungeon;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.ItemTemplate;

import static com.yorha.common.enums.statistic.StatisticEnum.CONSUME_ENERGY_TOTAL;

/**
 * <AUTHOR>
 */
public class PlayerEnergyComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerEnergyComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerEnergyComponent::onDailyRefresh);
    }

    public PlayerEnergyComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            final int initEnergy = ResHolder.getConsts(ConstTemplate.class).getPlayerInitEnergy();
            PlayerEnergyModelProp energyModel = getOwner().getProp().getEnergyModel();
            // 给定初始体力
            energyModel.setEnergy(initEnergy);
            energyModel.setLastRecoverSec((int) SystemClock.nowOfSeconds());
        }
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        recoverEnergy();
    }

    public static void beforeAdditionChange(PlayerEntity player, int additionId, long value) {
        player.getEnergyComponent().recoverEnergy();
    }

    /**
     * 恢复体力
     */
    public int recoverEnergy() {
        PlayerEntity player = getOwner();
        PlayerEnergyModelProp energyModel = player.getProp().getEnergyModel();
        final int before = energyModel.getEnergy();
        final int recoverMax = (int) PlayerAddCalc.getEnergyRecoverMax(player);
        final int nowSec = (int) SystemClock.nowOfSeconds();

        if (before >= recoverMax) {
            // 当前已经恢复满了，以确切的当前时间作为下一次恢复的”起始时间戳“
            energyModel.setLastRecoverSec(nowSec);
        } else {
            // 每隔N秒恢复一点体力
            final long recoverIntervalMs = PlayerAddCalc.getEnergyRecoverIntervalMs(player);
            final int intervalSec = (int) TimeUtils.ms2Second(recoverIntervalMs);
            final int lastRecoverSec = energyModel.getLastRecoverSec();
            int canRecover = Math.max(nowSec - lastRecoverSec, 0) / intervalSec;

            int after = Math.min(recoverMax, before + canRecover);
            energyModel.setEnergy(after);
            if (after >= recoverMax) {
                // 恢复满的情况下，以确切的当前时间作为下一次恢复的”起始时间戳“
                energyModel.setLastRecoverSec(nowSec);
            } else {
                // 需要考虑恢复时间周期的余数
                energyModel.setLastRecoverSec(lastRecoverSec + (canRecover * intervalSec));
            }
            LOGGER.debug("{} recover energy {}->{}", player, before, after);
        }
        return energyModel.getEnergy();
    }

    public boolean hasEnough(int needEnergy) {
        recoverEnergy();
        return getOwner().getProp().getEnergyModel().getEnergy() >= needEnergy;
    }

    public void verifyThrow(int needEnergy) {
        if (!hasEnough(needEnergy)) {
            throw new GeminiException("energy not enough. {}-{}", getOwner().getProp().getEnergyModel().getEnergy(), needEnergy);
        }
    }

    /**
     * 一般来说调用consumeEnergy之前必然要先调用hasEnough，调了hasEnough的话，needRecover设为false即可
     *
     * @param energyCostReason 体力消耗reason
     */
    public void consumeEnergy(int cost, boolean needRecover, String energyCostReason, int reasonTemplateId) {
        if (cost <= 0) {
            throw new GeminiException("cost energy non positive: {}", cost);
        }
        if (needRecover) {
            recoverEnergy();
        }
        PlayerEnergyModelProp energyModel = getOwner().getProp().getEnergyModel();
        final int before = energyModel.getEnergy();
        if (before < cost) {
            throw new GeminiException("energy not enough. {}", cost);
        }
        energyModel.setEnergy(before - cost);
        LOGGER.debug("{} consume energy {}->{}", getOwner(), before, energyModel.getEnergy());

        postConsumeEnergy(cost, energyCostReason, reasonTemplateId, before);
    }


    public int addEnergy(int num, boolean canOverflow, String energyAddReason, int reasonTemplateId) {
        // 需要先recover一下，不然不知道当前体力已经恢复到的确切值
        recoverEnergy();

        PlayerEnergyModelProp energyModel = getOwner().getProp().getEnergyModel();
        final int before = energyModel.getEnergy();

        int after = before + num;
        ConstTemplate consts = ResHolder.getConsts(ConstTemplate.class);
        if (canOverflow) {
            if (after > consts.getPlayerEnergyValueMax()) {
                // 如果超出了绝对上限的话，打个日志保险一点
                LOGGER.info("{} addEnergy overflow maxValue {}->{}", getOwner(), before, after);
            }
            after = Math.min(consts.getPlayerEnergyValueMax(), after);
        } else {
            // before有可能已经超过recoverMax了，不能倒扣体力
            after = Math.max(before, Math.min((int) PlayerAddCalc.getEnergyRecoverMax(getOwner()), after));
        }
        energyModel.setEnergy(after);
        LOGGER.debug("{} add energy {}->{} {}", getOwner(), before, after, canOverflow);
        // 返回真实增加的
        int realAddEnergy = after - before;
        postAddEnergy(realAddEnergy, energyAddReason, reasonTemplateId, before);
        return realAddEnergy;
    }


    /**
     * 扣减体力后续逻辑
     */
    private void postConsumeEnergy(int cost, String energyCostReason, int monsterId, int before) {
        getOwner().getStatisticComponent().recordSingleStatistic(CONSUME_ENERGY_TOTAL, cost);
        new PlayerConsumeEnergyEvent(getOwner(), cost, energyCostReason, before, monsterId).dispatch();
    }

    /**
     * 添加体力后续逻辑
     */
    private void postAddEnergy(int cost, String energyCostReason, int reasonTemplateId, int before) {
        new PlayerAddEnergyEvent(getOwner(), cost, energyCostReason, before, reasonTemplateId).dispatch();
    }

    private static void onDailyRefresh(PlayerDayRefreshEvent event) {
        event.getPlayer().getProp().getEnergyModel()
                .setDailyFreeEnergyTaken(false)
                .setDailyBuyTimes(0);
    }

    public void handleTakeDailyFreeEnergy() {
        final PlayerEnergyModelProp energyModel = getOwner().getProp().getEnergyModel();
        if (energyModel.getDailyFreeEnergyTaken()) {
            throw new GeminiException(ErrorCode.ALREADY_TAKEN);
        }

        energyModel.setDailyFreeEnergyTaken(true);

        final int dailyFreeEnergy = ResHolder.getConsts(ConstTemplate.class).getDailyFreeEnergy();
        String addEnergyReason = "energy_recover";
        addEnergy(dailyFreeEnergy, false, addEnergyReason, 0);
    }

    public void handleBuyEnergy(PlayerCommon.Player_BuyEnergy_C2S msg) {
        PlayerEnergyModelProp energyModel = getOwner().getProp().getEnergyModel();
        final int times = energyModel.getDailyBuyTimes() + 1;

        int costDiamond = ResHolder.getResService(CommanderResService.class).getEnergyBuyCostDiamondByTimes(times);
        AssetPackage cost = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND, costDiamond).build();
        getOwner().getAssetComponent().verifyThrow(cost);
        int rewardItemId = ResHolder.getConsts(ConstTemplate.class).getEnergyBuyItemId();

        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, rewardItemId);
        if (itemTemplate == null) {
            throw new GeminiException(ErrorCode.ITEM_NOT_EXIST);
        }
        if (msg.getDirectUse()) {
            // 超过体力绝对上限的话，不让购买
            new AddEnergyItem(1, itemTemplate).verifyThrow(getOwner(), null);
        }

        getOwner().getAssetComponent().consume(cost, CommonEnum.Reason.ICR_BUY_ENERGY);
        energyModel.setDailyBuyTimes(times);

        getOwner().getItemComponent().addItem(rewardItemId, 1, CommonEnum.Reason.ICR_BUY_ENERGY, String.valueOf(times));

        if (msg.getDirectUse()) {
            getOwner().getItemComponent().useItemByTemplateId(rewardItemId, 1, null);
        }
    }

    public boolean willOverflowMaxValue(int addEnergy) {
        recoverEnergy();
        int playerEnergyValueMax = ResHolder.getConsts(ConstTemplate.class).getPlayerEnergyValueMax();
        return getOwner().getProp().getEnergyModel().getEnergy() + addEnergy > playerEnergyValueMax;
    }

    public int getEnergy() {
        return getOwner().getProp().getEnergyModel().getEnergy();
    }
}

package com.yorha.cnc.player.controller;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerCampaign;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 战役
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CAMPAIGN)
public class PlayerCampaignController {

    @CommandMapping(code = MsgType.PLAYER_STARTCAMPAIGN_C2S)
    public PlayerCampaign.Player_StartCampaign_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_StartCampaign_C2S msg) {
        int campaignMapIndex = msg.getCampaignMapIndex() - 1;
        List<IntPairType> items = msg.getItemsList().stream().map(t -> IntPairType.makePair(t.getItemTemplateId(), t.getCount())).collect(Collectors.toList());
        playerEntity.getCampaignComponent().startCampaign(campaignMapIndex, items);
        return PlayerCampaign.Player_StartCampaign_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_ABORTCAMPAIGN_C2S)
    public PlayerCampaign.Player_AbortCampaign_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_AbortCampaign_C2S msg) {
        playerEntity.getCampaignComponent().abortCampaign();
        return PlayerCampaign.Player_AbortCampaign_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_STARTMISSION_C2S)
    public PlayerCampaign.Player_StartMission_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_StartMission_C2S msg) {
        CommonMsg.MissionMapInitInfo mapInitInfo = playerEntity.getCampaignComponent().startMission(msg.getMissionIndex() - 1, msg.getUnitsList(), msg.getSkillsList());
        PlayerCampaign.Player_StartMission_S2C.Builder builder = PlayerCampaign.Player_StartMission_S2C.newBuilder();
        builder.setMissionIndex(msg.getMissionIndex());
        builder.setMapInitInfo(mapInitInfo);
        return builder.build();
    }

    @CommandMapping(code = MsgType.PLAYER_FINISHMISSION_C2S)
    public PlayerCampaign.Player_FinishMission_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_FinishMission_C2S msg) {
        return playerEntity.getCampaignComponent().finishMission(msg.getMapResultInfo());
    }

    @CommandMapping(code = MsgType.PLAYER_MERGECAMPAIGNITEM_C2S)
    public PlayerCampaign.Player_MergeCampaignItem_C2S handle(PlayerEntity playerEntity, PlayerCampaign.Player_MergeCampaignItem_C2S msg) {
        List<IntPairType> items = msg.getItemsList().stream().map(t -> IntPairType.makePair(t.getItemTemplateId(), t.getCount())).collect(Collectors.toList());
        playerEntity.getCampaignComponent().mergeItems(items);
        return PlayerCampaign.Player_MergeCampaignItem_C2S.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNEVENTREST_C2S)
    public PlayerCampaign.Player_CampaignEventRest_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignEventRest_C2S msg) {
        playerEntity.getCampaignComponent().eventRest(msg.getUnitId());
        return PlayerCampaign.Player_CampaignEventRest_S2C.newBuilder().setUnitId(msg.getUnitId()).build();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNEVENTBAR_C2S)
    public PlayerCampaign.Player_CampaignEventBar_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignEventBar_C2S msg) {
        CommonEnum.CampaignEventBarEffectType effectType = playerEntity.getCampaignComponent().eventBar(msg.getUnitId());
        return PlayerCampaign.Player_CampaignEventBar_S2C.newBuilder().setUnitId(msg.getUnitId()).setEffectType(effectType).build();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNEVENTTRAIN_C2S)
    public PlayerCampaign.Player_CampaignEventTrain_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignEventTrain_C2S msg) {
        boolean success = playerEntity.getCampaignComponent().eventTrain(msg.getUnitId(), msg.getConsumeUnitId());
        return PlayerCampaign.Player_CampaignEventTrain_S2C.newBuilder().setUnitId(msg.getUnitId()).setSuccess(success).build();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNEVENTRESTORE_C2S)
    public PlayerCampaign.Player_CampaignEventRestore_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignEventRestore_C2S msg) {
        playerEntity.getCampaignComponent().eventRestore();
        return PlayerCampaign.Player_CampaignEventRestore_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNEVENTREPAIR_C2S)
    public PlayerCampaign.Player_CampaignEventRepair_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignEventRepair_C2S msg) {
        int invalidBuildingId = playerEntity.getCampaignComponent().eventRepair(msg.getBuildingId());
        return PlayerCampaign.Player_CampaignEventRepair_S2C.newBuilder().setBuildingId(msg.getBuildingId()).setInvalidBuildingId(invalidBuildingId).build();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNEVENTEXIT_C2S)
    public PlayerCampaign.Player_CampaignEventExit_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignEventExit_C2S msg) {
        playerEntity.getCampaignComponent().eventExit();
        return PlayerCampaign.Player_CampaignEventExit_S2C.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNMISSIONSTART_C2S)
    public PlayerCampaign.Player_CampaignMissionStart_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignMissionStart_C2S msg) {
        PlayerCampaign.Player_CampaignMissionStart_S2C.Builder builder = PlayerCampaign.Player_CampaignMissionStart_S2C.newBuilder();
        builder.setMissionIndex(msg.getMissionIndex());
        var battleType = CommonEnum.BattleType.forNumber(msg.getBattleType());
        if (battleType == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        switch (battleType) {
            case BT_CAMPAIGN: {
                playerEntity.getCampaignComponent().startMission2(msg.getMissionIndex() - 1, msg.getUnitsList(), msg.getSkillsList());
                break;
            }
            case BT_INNER_DEF: {
                playerEntity.getInnerBuildRhComponent().startDefend();
                break;
            }
            case BT_INNER_PVE: {
                playerEntity.getMissionComponent().startMission(msg.getMissionIndex(), msg.getTroopId());
                break;
            }
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return builder.build();
    }

    @CommandMapping(code = MsgType.PLAYER_CAMPAIGNMISSIONFINISH_C2S)
    public PlayerCampaign.Player_CampaignMissionFinish_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_CampaignMissionFinish_C2S msg) {
        CommonEnum.BattleType battleType = CommonEnum.BattleType.forNumber(msg.getBattleType());
        return switch (battleType) {
            case BT_CAMPAIGN -> playerEntity.getCampaignComponent().finishMission2(msg.getMapResultInfo());
//            case BT_CRISIS -> playerEntity.getInnerBuildRhComponent().endCrisis2(msg.getMapResultInfo());
            case BT_INNER_DEF -> playerEntity.getInnerBuildRhComponent().endDefend(msg.getMapResultInfo());
            case BT_INNER_PVE -> playerEntity.getMissionComponent().endMission(msg.getMapResultInfo());
            default -> throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        };
    }

    @CommandMapping(code = MsgType.PLAYER_TOUCHMISSIONCHEST_C2S)
    public PlayerCampaign.Player_TouchMissionChest_S2C handle(PlayerEntity playerEntity, PlayerCampaign.Player_TouchMissionChest_C2S msg) {
        AssetPackage assetPackage = playerEntity.getMissionComponent().touchMission(msg.getMissionIndex());

        PlayerCampaign.Player_TouchMissionChest_S2C.Builder builder = PlayerCampaign.Player_TouchMissionChest_S2C.newBuilder();
        builder.setBaseRewards(assetPackage.toPb());

        return builder.build();
    }
}

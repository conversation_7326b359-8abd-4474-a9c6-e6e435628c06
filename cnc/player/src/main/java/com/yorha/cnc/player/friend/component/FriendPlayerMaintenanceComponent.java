package com.yorha.cnc.player.friend.component;

import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.FriendApplyInfoProp;
import com.yorha.game.gen.prop.ShieldPlayerInfoProp;
import com.yorha.game.gen.prop.SimpleFriendInfoProp;
import com.yorha.game.gen.prop.WaitResponseInfoProp;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstFriendTemplate;

import java.util.Iterator;
import java.util.concurrent.TimeUnit;

/**
 * 好友数据维护组件
 *
 * <AUTHOR>
 */
public class FriendPlayerMaintenanceComponent extends FriendPlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(FriendPlayerMaintenanceComponent.class);

    private long nextExpireApply = 0;
    private long nextExpireWait = 0;
    private ActorTimer clearExpireApply = null;
    private ActorTimer clearExpireWait = null;

    @Override
    public void postInit() {
        // 清理过期申请和等待回应信息
        clearExpireApply();
        clearExpireWaitInfo();
    }

    @Override
    public void onDestroy() {
        resetExpireApply();
        resetExpireWait();
    }

    public FriendPlayerMaintenanceComponent(FriendPlayerEntity owner) {
        super(owner);
    }

    public SimpleFriendInfoProp addPlayerToFriendList(long playerId, int zoneId, Struct.PlayerCardHead cardHead) {
        Player.SimpleFriendInfo.Builder info = Player.SimpleFriendInfo.newBuilder();
        info.setPlayerId(playerId)
                .setZoneId(zoneId)
                .setCardHead(cardHead);
        SimpleFriendInfoProp prop = new SimpleFriendInfoProp();
        prop.mergeChangeFromSs(info.build());
        getOwner().getProp().getFriendList().put(playerId, prop);
        return prop;
    }

    public FriendApplyInfoProp addApplyInfoToList(long playerId, int zoneId, Struct.PlayerCardHead cardHead) {
        Player.FriendApplyInfo.Builder info = Player.FriendApplyInfo.newBuilder();
        info.setPlayerId(playerId)
                .setZoneId(zoneId)
                .setCardHead(cardHead)
                .setApplyTsMs(SystemClock.now());
        FriendApplyInfoProp prop = new FriendApplyInfoProp();
        prop.mergeChangeFromSs(info.build());
        getOwner().getProp().getApplyList().put(playerId, prop);
        if (nextExpireApply == 0 && clearExpireApply == null) {
            nextExpireApply = playerId;
            long delay = ResHolder.getConsts(ConstFriendTemplate.class).getApplyDelete();
            clearExpireApply = ownerActor().addTimer(TimerReasonType.FRIEND_CLEAR_EXPIRE_APPLY, this::clearExpireApply, delay, TimeUnit.MILLISECONDS);
        }
        return prop;
    }

    public ShieldPlayerInfoProp addShieldInfoToList(long playerId, int zoneId, Struct.PlayerCardHead cardHead) {
        ShieldPlayerInfoProp prop = new ShieldPlayerInfoProp();
        prop.setPlayerId(playerId).setZoneId(zoneId).getCardHead().mergeFromSs(cardHead);
        getOwner().getProp().getShiledList().put(playerId, prop);
        return prop;
    }

    public WaitResponseInfoProp addWaitInfoToList(long playerId) {
        WaitResponseInfoProp newProp = getOwner().getProp().getWaitList()
                .addEmptyValue(playerId)
                .setApplyTsMs(SystemClock.now());
        if (nextExpireWait == 0 && clearExpireWait == null) {
            nextExpireWait = playerId;
            long delay = ResHolder.getConsts(ConstFriendTemplate.class).getApplyDelete();
            clearExpireApply = ownerActor().addTimer(TimerReasonType.FRIEND_CLEAR_EXPIRE_WAIT, this::clearExpireWaitInfo, delay, TimeUnit.MILLISECONDS);
        }
        return newProp;
    }

    public FriendApplyInfoProp removeApplyInfo(long playerId) {
        return getOwner().getProp().getApplyList().remove(playerId);
    }

    public WaitResponseInfoProp removeWaitResponseInfo(long playerId) {
        return getOwner().getProp().getWaitList().remove(playerId);
    }

    public SimpleFriendInfoProp removeFriendInfo(long playerId) {
        return getOwner().getProp().getFriendList().remove(playerId);
    }

    public ShieldPlayerInfoProp removeShieldInfo(long playerId) {
        return getOwner().getProp().getShiledList().remove(playerId);
    }

    public void clearExpireApply() {
        Long nextTsMs = Long.MAX_VALUE;
        long playerId = 0;
        Iterator<Long> it = getOwner().getProp().getApplyList().keySetIterator();
        while (it.hasNext()) {
            Long key = it.next();
            FriendApplyInfoProp entry = getOwner().getProp().getApplyList().get(key);
            long expireTime = entry.getApplyTsMs() + TimeUtils.second2Ms(ResHolder.getConsts(ConstFriendTemplate.class).getApplyDelete());
            long now = SystemClock.now();
            if (expireTime < now) {
                it.remove();
                LOGGER.debug("FriendLog : {} clear expire apply {}, prop = {}", getOwner().ownerActor().getPlayerId(), entry, getOwner().getProp());
            } else {
                if (nextTsMs > (expireTime - now)) {
                    playerId = key;
                    nextTsMs = expireTime - now;
                }
            }
        }
        if (playerId != 0) {
            setNextExpireApply(playerId, nextTsMs);
        } else {
            resetExpireApply();
        }
    }

    public void clearExpireWaitInfo() {
        Long nextTsMs = Long.MAX_VALUE;
        long playerId = 0;
        Iterator<Long> it = getOwner().getProp().getWaitList().keySetIterator();
        while (it.hasNext()) {
            Long key = it.next();
            WaitResponseInfoProp entry = getOwner().getProp().getWaitList().get(key);
            long expireTime = entry.getApplyTsMs() + TimeUtils.second2Ms(ResHolder.getConsts(ConstFriendTemplate.class).getApplyOverdue());
            long now = SystemClock.now();
            if (expireTime < now) {
                it.remove();
                LOGGER.debug("FriendLog : {} clear expire wait {}, prop = {}", getOwner().ownerActor().getPlayerId(), entry, getOwner().getProp());
            } else {
                if (nextTsMs > (expireTime - now)) {
                    playerId = key;
                    nextTsMs = expireTime - now;
                }
            }
        }
        if (playerId != 0) {
            setNextExpireWait(playerId, nextTsMs);
        } else {
            resetExpireWait();
        }
    }

    public void setNextExpireApply(long playerId, long delay) {
        nextExpireApply = playerId;
        if (clearExpireApply != null) {
            clearExpireApply.cancel();
        }
        clearExpireApply = ownerActor().addTimer(TimerReasonType.FRIEND_CLEAR_EXPIRE_APPLY_2, this::clearExpireApply, delay, TimeUnit.MILLISECONDS);
    }

    public void resetExpireApply() {
        nextExpireApply = 0;
        if (clearExpireApply != null) {
            clearExpireApply.cancel();
        }
        clearExpireApply = null;
    }

    public void setNextExpireWait(long playerId, long delay) {
        nextExpireWait = playerId;
        if (clearExpireWait != null) {
            clearExpireWait.cancel();
        }
        clearExpireWait = ownerActor().addTimer(TimerReasonType.FRIEND_CLEAR_EXPIRE_WAIT_2, this::clearExpireWaitInfo, delay, TimeUnit.MILLISECONDS);
    }

    public void resetExpireWait() {
        nextExpireWait = 0;
        if (clearExpireWait != null) {
            clearExpireWait.cancel();
        }
        clearExpireWait = null;
    }
}

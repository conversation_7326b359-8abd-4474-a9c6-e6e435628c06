package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

public class DestroyCableCampChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(DestroyCableCampChecker.class);

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(PlayerKillBigSceneMonsterEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configTimes = taskParams.get(0);
        // 只能是接取任务
        if (taskTemplate.getTaskCalculationMethod() != CommonEnum.TaskCalcType.TCT_RECEIVE) {
            LOGGER.error("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE));
            return prop.getProcess() >= configTimes;
        }
        // 击杀野怪事件可能会更新进度
        if (event instanceof PlayerKillBigSceneMonsterEvent) {
            if (((PlayerKillBigSceneMonsterEvent) event).getMonsterCategory() == CommonEnum.MonsterCategory.RALLY_MONSTER_VALUE) {
                prop.setProcess(Math.min(prop.getProcess() + 1, configTimes));
            }
        }
        return prop.getProcess() >= configTimes;
    }
}

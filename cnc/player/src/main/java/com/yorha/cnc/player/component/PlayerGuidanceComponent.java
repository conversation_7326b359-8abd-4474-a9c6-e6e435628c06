package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.guidance.GuidanceTemplateService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class PlayerGuidanceComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerGuidanceComponent.class);

    public PlayerGuidanceComponent(PlayerEntity owner) {
        super(owner);
    }

    public void recordFinishedGuidance(int guidanceId) {
        getOwner().getProp().getPlayerGuidanceModel().addEmptyAlreadyFinishGuidances(guidanceId);
    }

    public boolean isGuidanceNeedRecord(int guidanceId, int stepId) {
        // 已经被记录过了
        if (getOwner().getProp().getPlayerGuidanceModel().getAlreadyFinishGuidancesV(guidanceId) != null) {
            LOGGER.debug("try to record guidance {} again", guidanceId);
            return false;
        }
        GuidanceTemplateService service = ResHolder.getResService(GuidanceTemplateService.class);
        // 配置不存在
        if (!service.isGuidanceConfigExist(guidanceId)) {
            LOGGER.warn("config id {} not exist", guidanceId);
            return false;
        }
        // 当前引导是否首次出现即需记录
        if (service.isGuidanceNeedRecordWhenStart(guidanceId)) {
            LOGGER.debug("guidance {} need record when start", guidanceId);
            return stepId == 0;
        } else {
            return service.isGuidanceEndStep(guidanceId, stepId);
        }
    }

    public boolean isGuidanceFinished(int guidanceId) {
        return getOwner().getProp().getPlayerGuidanceModel().getAlreadyFinishGuidancesV(guidanceId) != null;
    }

    public void gmRecordFinishedGuidance(int guidanceId) {
        getOwner().getProp().getPlayerGuidanceModel().addEmptyAlreadyFinishGuidances(guidanceId);
    }

    public void gmResetGuidance(int guidanceId) {
        if (getOwner().getProp().getPlayerGuidanceModel().getAlreadyFinishGuidancesV(guidanceId) == null) {
            LOGGER.error("try to reset guidance {} while not set", guidanceId);
            return;
        }
        getOwner().getProp().getPlayerGuidanceModel().removeAlreadyFinishGuidancesV(guidanceId);
        LOGGER.debug("success remove guidance {}", guidanceId);
    }

    public void gmResetAllGuidance() {
        getOwner().getProp().getPlayerGuidanceModel().clearAlreadyFinishGuidances();
        LOGGER.debug("success remove all guidance");
    }
}

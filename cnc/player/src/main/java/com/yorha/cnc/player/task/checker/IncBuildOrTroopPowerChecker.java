package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerPowerIncreaseEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

public class IncBuildOrTroopPower<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerPowerIncreaseEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        final int require = taskParams.get(0);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_RECEIVE: {
                if (event instanceof PlayerPowerIncreaseEvent) {
                    PlayerPowerIncreaseEvent e = (PlayerPowerIncreaseEvent) event;

                    if (e.getPowerReason() == CommonEnum.PowerType.PT_INNER_BUILDING) {
                        prop.setProcess((int) Math.min(require, prop.getProcess() + e.getPower()));
                        break;
                    }
                    if (e.getPowerReason() == CommonEnum.PowerType.PT_SOLDIER) {
                        // 治疗的不计算战力提升
                        if (TroopHelper.isUpdatePowerByRecover(e.getSoldierNumChangeReason())) {
                            break;
                        }
                        prop.setProcess((int) Math.min(require, prop.getProcess() + e.getPower()));
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= require;
    }
}
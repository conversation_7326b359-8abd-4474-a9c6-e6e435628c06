package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.ChapterTaskFinishEvent;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.task.TaskTemplateService;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Set;

public class ChapterTaskFinishChecker  extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(ChapterTaskFinishChecker.class);

    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(ChapterTaskFinishEvent.class),
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {

        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity player = event.getPlayer();
        final int chapter = taskParams.get(0);
        Set<Integer> tasks = ResHolder.getResService(TaskTemplateService.class).getChapterTasks(chapter);
        if (tasks == null) {
            LOGGER.error("Chapter={} has none tasks, should not be task and taskId={}", chapter, prop.getId());
            return false;
        }
        //-1是为了排除自身
        int configCount = tasks.size() - 1;
        //排除自身后的任务完成数量
        int completedNum = player.getTaskComponent().getChapterCompletedTaskNum(chapter, taskTemplate.getId());
        prop.setProcess(completedNum);

        return prop.getProcess() >= configCount;
    }
}

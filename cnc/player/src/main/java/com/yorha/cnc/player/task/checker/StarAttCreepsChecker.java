package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerStarAttCreepsEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.ATTACK_MONSTER_NUM_GROUPBY_LEVEL;

/**
 * 攻击特定等级野怪X次
 * param1: 最小等级
 * param2: 最大等级
 * param3: 次数
 *
 * <AUTHOR>
 */
public class StarAttCreeps<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerStarAttCreepsEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(
            PlayerTaskEvent event,
            TaskInfoProp prop,
            TaskPoolTemplate taskTemplate
    ) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer minLevel = taskParams.get(0);
        Integer maxLevel = taskParams.get(1);
        Integer countConfig = taskParams.get(2);
        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int attackTotal = 0;
            for (int level = minLevel; level <= maxLevel; level++) {
                attackTotal += (int) event.getPlayer().getStatisticComponent().getSecondStatistic(ATTACK_MONSTER_NUM_GROUPBY_LEVEL, level);
            }
            prop.setProcess(Math.min(attackTotal, countConfig));
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }
}

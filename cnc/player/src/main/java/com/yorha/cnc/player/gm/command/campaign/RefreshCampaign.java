package com.yorha.cnc.player.gm.command.campaign;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 刷新战役
 */
public class RefreshCampaign implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        // 更新战役
        actor.getOrLoadEntity().getCampaignComponent().updateCampaign();
    }

    @Override
    public String showHelp() {
        return "RefreshCampaign";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

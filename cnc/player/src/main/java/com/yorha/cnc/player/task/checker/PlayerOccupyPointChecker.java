package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerOccupyPointEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.OCCUPY_POINT_NUM;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;
/**
 * 占领据点x次
 * param1： 次数
 *
 * <AUTHOR>
 */
public class PlayerOccupy<PERSON>oint<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerOccupyPointEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.getFirst();

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            int occupyTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(OCCUPY_POINT_NUM);
            prop.setProcess(Math.min(occupyTotal, param1));
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param1;
    }
}

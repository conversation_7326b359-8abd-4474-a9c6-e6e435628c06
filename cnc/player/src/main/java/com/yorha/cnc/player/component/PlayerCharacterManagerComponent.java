package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.WhitePermissions;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.whiteList.WhiteListResService;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PlayerCharacterManagerComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCharacterManagerComponent.class);

    public PlayerCharacterManagerComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 处理获取角色列表c2s请求
     *
     * @param seqId
     */
    public void handleGetCharacterList(int seqId) {
        final Map<Long, Boolean> playerIdsWithStar = this.getPlayerIdsWithStar(this.getOwner().getOpenId());
        IActorRef sessionRef = ownerActor().sender();
        CardHelper.batchQueryPlayerCard(this.ownerActor(), playerIdsWithStar.keySet(), (map) -> {
            PlayerCharacterManager.Player_GetCharacterList_S2C.Builder response = PlayerCharacterManager.Player_GetCharacterList_S2C.newBuilder();
            for (Map.Entry<Long, StructPB.PlayerCardInfoPB> entry : map.entrySet()) {
                final Long playerId = entry.getKey();
                final StructPB.PlayerCardInfoPB playerCardInfo = entry.getValue();
                response.addCharacterList(CommonMsg.CharacterInfo.newBuilder()
                        .setPlayerId(playerId)
                        .setCardHead(playerCardInfo.getCardHead())
                        .setZoneId(playerCardInfo.getZoneId())
                        .setCombat(playerCardInfo.getCombat())
                        .setIsStar(playerIdsWithStar.getOrDefault(playerId, false))
                        .build());
            }
            LOGGER.info("handleGetCharacterList playerIdSize={}, playerCardSize={}", playerIdsWithStar.size(), map.size());
            getOwner().answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_GETCHARACTERLIST_S2C, null, response.build());
        });
    }

    /**
     * 管理星标角色
     *
     * @param isStar   是否是星标
     * @param playerId 角色id
     */
    public void manageStarPlayer(final boolean isStar, final long playerId) {
        if (this.getPlayerIdsWithStar(this.getOwner().getOpenId()).get(playerId) == null) {
            LOGGER.info("manageStarPlayer playerId={}, not owned by openId={}", playerId, this.getOwner().getOpenId());
            throw new GeminiException(ErrorCode.MULTI_SERVER_NOT_OWNED_CHARACTER);
        }
        TcaplusDb.AccountRoleTable.Builder updateReq = TcaplusDb.AccountRoleTable.newBuilder()
                .setPlayerId(playerId)
                .setOpenId(this.getOwner().getOpenId())
                .setIsStar(isStar ? 1 : 0);
        UpdateAsk<TcaplusDb.AccountRoleTable.Builder> ask = new UpdateAsk<>(updateReq);
        try {
            UpdateResult<TcaplusDb.AccountRoleTable.Builder> ans = this.ownerActor().callGameDb(ask);
            if (!ans.isOk()) {
                LOGGER.error("manageStarPlayer update playerId={}, isStar={} fail, code={}", playerId, isStar, ans.getCode());
                throw new GeminiException(ErrorCode.MULTI_SERVER_GET_CHARACTER_INFO_FAIL);
            }
        } catch (Exception e) {
            LOGGER.error("manageStarPlayer update playerId={}, isStar={} fail, ", playerId, isStar, e);
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_CHARACTER_INFO_FAIL);
        }
    }

    /**
     * 拉取所有已对外开服服务器信息
     *
     * @return List<CommonMsg.ZoneServerInfo>
     */
    public List<CommonMsg.ZoneServerInfo> getAllZoneServerInfo() {
        final SsZoneCard.GetAllZoneInfoAns ans;
        try {
            ans = this.getOwner().ownerActor().call(RefFactory.ofZoneCard(), SsZoneCard.GetAllZoneInfoAsk.newBuilder().setIsSuperWhite(this.isSuperWhite()).build());
        } catch (Exception e) {
            LOGGER.error("getAllZoneServerInfo fail, ", e);
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_SERVERS_INFO_FAIL);
        }
        return ans.getZoneServerListList();
    }

    public SsSceneInfoMgr.GetZoneIpPortAns getZoneIpPort(final int zoneId, final String channelName) {
        final SsSceneInfoMgr.GetZoneIpPortAns ans;
        try {
            ans = this.getOwner().ownerActor().callScene(zoneId, SsSceneInfoMgr.GetZoneIpPortAsk.newBuilder().setChannelName(channelName).setIsSuperWhite(this.isSuperWhite()).build());
        } catch (Exception e) {
            LOGGER.error("getZoneIpPort zoneId={}, channelName={} fail, ", zoneId, channelName, e);
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_TARGET_SERVER_INFO_FAIL);
        }
        LOGGER.info("getZoneIpPort zoneId={}, ip={}, port={}", zoneId, ans.getIp(), ans.getPort());
        return ans;
    }

    /**
     * 拉取玩家名下所有角色id&星标信息
     *
     * @param openId 玩家openId
     * @return Map<Long, Boolean> key=playerId, val=isStar
     * @throws GeminiException
     */
    private Map<Long, Boolean> getPlayerIdsWithStar(final String openId) throws GeminiException {
        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder();
        req.setOpenId(openId);
        GetByPartKeyResult<TcaplusDb.AccountRoleTable.Builder> result;
        try {
            result = this.ownerActor().callGameDb(new SelectAsk<>(req, GetByPartKeyOption.newBuilder().build()));
        } catch (Exception e) {
            LOGGER.error("getPlayerIdWithStar fail openId={}", openId, e);
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_CHARACTER_INFO_FAIL);

        }
        // 无角色
        if (result.isRecordNotExist()) {
            return Collections.emptyMap();
        }
        // DB操作失败
        if (!result.isOk()) {
            LOGGER.error("getPlayerIdWithStar db fail, requestId={}, code={}", result.requestId, result.getCode());
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_CHARACTER_INFO_FAIL);
        }
        Map<Long, Boolean> playerIdsWithStar = new HashMap<>(result.values.size());
        for (ValueWithVersion<TcaplusDb.AccountRoleTable.Builder> value : result.values) {
            final long playerId = value.value.getPlayerId();
            final boolean isStar = value.value.getIsStar() > 0;
            playerIdsWithStar.put(playerId, isStar);
        }
        return playerIdsWithStar;
    }

    /**
     * 玩家是否是超级白名单
     *
     * @return true==是超级白名单
     */
    boolean isSuperWhite() {
        final boolean isSuperWhite = ResHolder.getResService(WhiteListResService.class)
                .hasWhitePermission(
                        0,
                        this.getOwner().getOpenId(),
                        this.getOwner().getClientInfo().getDeviceId(),
                        this.getOwner().getClientIp(),
                        WhitePermissions.WP_POWER);
        LOGGER.info("PlayerCharacterManagerComponent isSuperWhite={}", isSuperWhite);
        return isSuperWhite;
    }
}

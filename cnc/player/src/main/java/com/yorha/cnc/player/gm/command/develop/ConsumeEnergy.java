package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class ConsumeEnergy implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int energy = Integer.parseInt(args.getOrDefault("energy", "0"));
        if (energy > 0) {
            actor.getEntity().getEnergyComponent().consumeEnergy(energy, true, "gm", 0);
        }
    }

    @Override
    public String showHelp() {
        return "ConsumeEnergy energy={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

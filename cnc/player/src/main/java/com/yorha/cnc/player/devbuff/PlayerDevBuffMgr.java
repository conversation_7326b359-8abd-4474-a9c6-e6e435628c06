package com.yorha.cnc.player.devbuff;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsScenePlayer;
import res.template.BuffTemplate;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class PlayerDevBuffMgr extends DevBuffMgrBase<PlayerEntity> {
    public PlayerDevBuffMgr(PlayerEntity owner) {
        super(owner);
    }

    @Override
    protected LOGLEVEL logLevel() {
        return LOGLEVEL.INFO;
    }

    @Override
    public DevBuffSysProp getDevBuffSys() {
        return getOwner().getProp().getDevBuffSysNew();
    }

    @Override
    public CommonEnum.DevBuffType getBuffType() {
        return CommonEnum.DevBuffType.DBT_PLAYER_BUFF;
    }

    @Override
    public DevBuffProp addDevBuff(int buffId,
                                  Long startTime,
                                  Long forceEndTime,
                                  CommonEnum.DevBuffType buffType,
                                  CommonEnum.DevBuffSourceType sourceType,
                                  Integer layer) {
        IActorRef targetActorRef = RefFactory.ofBigScene((int) getOwner().getMainSceneId());
        return addDevBuff(buffId, startTime, forceEndTime, buffType, sourceType, layer, targetActorRef);
    }

    public DevBuffProp addDevBuff(int buffId,
                                  Long startTime,
                                  Long forceEndTime,
                                  CommonEnum.DevBuffType buffType,
                                  CommonEnum.DevBuffSourceType sourceType,
                                  Integer layer, IActorRef targetActorRef) {
        if (DevBuffUtil.isSceneBuff(buffId)) {
            // scene buff，call到scene上去加
            CommonMsg.DevBuffAddParam.Builder paramBuilder = CommonMsg.DevBuffAddParam.newBuilder()
                    .setDevBuffId(buffId)
                    .setBuffType(buffType)
                    .setSourceType(sourceType);
            if (startTime != null) {
                paramBuilder.setStartTime(startTime);
            }
            if (forceEndTime != null) {
                paramBuilder.setEndTime(forceEndTime);
            }
            if (layer != null) {
                paramBuilder.setLayer(layer);
            }
            CommonMsg.AddDevBuffAsk.Builder builder = CommonMsg.AddDevBuffAsk.newBuilder()
                    .setPlayerId(getOwner().getEntityId())
                    .setParam(paramBuilder.build());
            SsScenePlayer.AddDevBuffFromPlayerAsk.Builder call = SsScenePlayer.AddDevBuffFromPlayerAsk.newBuilder()
                    .setAsk(builder);
            SsScenePlayer.AddDevBuffFromPlayerAns ans = getOwner().ownerActor().call(targetActorRef, call.build());
            if (ans.getAns().hasBuff()) {
                DevBuffProp devBuffProp = new DevBuffProp();
                devBuffProp.mergeFromSs(ans.getAns().getBuff());
                return devBuffProp;
            } else {
                return null;
            }
        } else {
            return super.addDevBuff(buffId, startTime, forceEndTime, buffType, sourceType, layer);
        }
    }

    @Override
    public DevBuffProp removeDevBuff(int buffId, Integer layer, boolean isExpired) {
        if (DevBuffUtil.isSceneBuff(buffId)) {
            // scene buff，call到scene上去删除
            CommonMsg.DevBuffRemoveParam.Builder paramBuilder = CommonMsg.DevBuffRemoveParam.newBuilder()
                    .setDevBuffId(buffId);
            if (layer != null) {
                paramBuilder.setLayer(layer);
            }
            CommonMsg.RemoveDevBuffAsk.Builder builder = CommonMsg.RemoveDevBuffAsk.newBuilder()
                    .setPlayerId(getOwner().getEntityId())
                    .setParam(paramBuilder.build());
            SsScenePlayer.RemoveDevBuffFromPlayerAsk.Builder call = SsScenePlayer.RemoveDevBuffFromPlayerAsk.newBuilder()
                    .setAsk(builder);
            SsScenePlayer.RemoveDevBuffFromPlayerAns ans = getOwner().ownerActor().callBigScene(call.build());
            if (ans.getAns().getIsRemoved()) {
                if (ans.getAns().hasBuff()) {
                    DevBuffProp devBuffProp = new DevBuffProp();
                    devBuffProp.mergeFromSs(ans.getAns().getBuff());
                    return devBuffProp;
                } else {
                    return null;
                }
            } else {
                DevBuffProp devBuffProp = new DevBuffProp();
                devBuffProp.mergeFromSs(ans.getAns().getBuff());
                return devBuffProp;
            }
        } else {
            return super.removeDevBuff(buffId, layer, isExpired);
        }
    }

    @Override
    protected void afterAddDevBuff(DevBuffProp devBuff, int addLayer) {
        // 更新加成
        BuffTemplate buffTemplate = getBuffTemplate(devBuff.getDevBuffId());
        getOwner().getAddComponent().updateAddition(AdditionProviderType.BUFF, buffTemplate.getType());
    }

    @Override
    protected void afterRemoveDevBuff(DevBuffProp devBuffToRemove, boolean isExpired, int decLayer) {
        // 更新加成
        BuffTemplate buffTemplate = getBuffTemplate(devBuffToRemove.getDevBuffId());
        getOwner().getAddComponent().updateAddition(AdditionProviderType.BUFF, buffTemplate.getType());
    }

    @Override
    public boolean canAddDevBuff(int buffId) {
        if (DevBuffUtil.isSceneBuff(buffId)) {
            // scene buff，call到scene上去check
            SsScenePlayer.CheckCanAddDevBuffAsk.Builder call = SsScenePlayer.CheckCanAddDevBuffAsk.newBuilder()
                    .setPlayerId(getOwner().getEntityId())
                    .setBuffId(buffId);
            SsScenePlayer.CheckCanAddDevBuffAns ans = getOwner().ownerActor().callBigScene(call.build());
            return ans.getCan();
        } else {
            return super.canAddDevBuff(buffId);
        }
    }

    @Override
    protected void addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        getOwner().ownerActor().addTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    protected void cancelTimer(String prefix, TimerReasonType timerReasonType) {
        ActorTimer timer = getOwner().ownerActor().getTimer(prefix, timerReasonType);
        if (timer != null) {
            timer.cancel();
        }
    }
}

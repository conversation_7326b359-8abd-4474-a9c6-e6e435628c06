package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerFinishTrainingSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.FINISH_TRAIN_SOLDIERID_NUM;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_RECEIVE;

/**
 * 完成训练兵种X个
 * param1: 兵种类型 0代表任意兵种
 * param2: 个数
 *
 * <AUTHOR>
 */
public class FinishTrainingSoldierTypeChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerFinishTrainingSoldierEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());


    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int soldierTypeConfig = taskParams.get(0);
        int countConfig = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            int trainNum = 0;
            if (soldierTypeConfig == 0) {
                trainNum = (int) event.getPlayer().getStatisticComponent().getSumValueOfSecondStatistic(FINISH_TRAIN_SOLDIERID_NUM);
            } else {
                List<Integer> soldierIdList = ResHolder.getResService(SoldierResService.class).getIdListBySoldierType(soldierTypeConfig);
                for (Integer soldierId : soldierIdList) {
                    trainNum += (int) event.getPlayer().getStatisticComponent().getSecondStatistic(FINISH_TRAIN_SOLDIERID_NUM, soldierId);
                }
            }
            prop.setProcess(Math.min(trainNum, countConfig));
        } else if (taskTemplate.getTaskCalculationMethod() == TCT_RECEIVE) {
            if (event instanceof PlayerFinishTrainingSoldierEvent) {
                PlayerFinishTrainingSoldierEvent finishTrainingSoldierEvent = (PlayerFinishTrainingSoldierEvent) event;
                int soldierId = finishTrainingSoldierEvent.getSoldierId();
                int soldierType = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId).getSoldierType();
                if (soldierTypeConfig == soldierType || soldierTypeConfig == 0) {
                    prop.setProcess(Math.min(prop.getProcess() + finishTrainingSoldierEvent.getCount(), countConfig));
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }
}

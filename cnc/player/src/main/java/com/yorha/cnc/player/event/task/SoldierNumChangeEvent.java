package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

import java.util.Map;

/**
 * 士兵数量变化事件
 *
 * <AUTHOR>
 */
public class SoldierNumChangeEvent extends PlayerTaskEvent {
    Map<Integer, Integer> soldierType2Num;

    public SoldierNumChangeEvent(PlayerEntity entity, Map<Integer, Integer> soldierType2Num) {
        super(entity);
        this.soldierType2Num = soldierType2Num;
    }

    public Map<Integer, Integer> getSoldierType2Num() {
        return soldierType2Num;
    }

}

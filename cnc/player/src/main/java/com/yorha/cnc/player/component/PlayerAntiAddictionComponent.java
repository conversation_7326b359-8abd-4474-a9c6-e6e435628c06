package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.hope.FactType;
import com.yorha.common.enums.hope.InstructionType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeSdkTemplate;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.TimerReasonType.ANTI_ADDICTION_FORCE_LOGOUT;
import static com.yorha.common.enums.TimerReasonType.ANTI_ADDICTION_TASK;

/**
 * 防沉迷
 * <p>
 * 只有国服版本会使用这个组件！！！
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
public class PlayerAntiAddictionComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerAntiAddictionComponent.class);

    private long lastJudgeTimingTsMs;
    private ActorTimer judgeTimingTimer;
    private static final int JUDGE_TIMER_PERIOD = 300;
    private ForceLogoutTask forceLogoutTask;

    public PlayerAntiAddictionComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void afterLogout() {
        if (!isAntiAddictionOpen()) {
            return;
        }
        judgeTimingWhenLogout();
    }

    public static boolean isAntiAddictionOpen() {
        try {
            return ClusterConfigUtils.getWorldConfig().getBooleanItem("open_anti_addiction");
        } catch (Exception e) {
            LOGGER.warn("open_anti_addiction no config use false");
        }
        return false;
    }

    /**
     * 登录时执行judgeTiming
     */
    public boolean judgeTimingWhenLogin() {
        if (!isAntiAddictionOpen()) {
            return true;
        }
        Pair<Boolean, Boolean> result = this.judgeTiming(FactType.GAME_START);
        if (!result.getFirst()) {
            // 登陆决策没通过，登陆失败
            return false;
        }

        // 开启心跳
        cancelJudgeTimingTimer();
        judgeTimingTimer = ownerActor().addRepeatTimer(
                ANTI_ADDICTION_TASK,
                this::judgeTimingPeriodically,
                JUDGE_TIMER_PERIOD,
                JUDGE_TIMER_PERIOD,
                TimeUnit.SECONDS);

        return true;
    }

    /**
     * 防沉迷周期性执行judgeTiming
     */
    private void judgeTimingPeriodically() {
        if (!isAntiAddictionOpen()) {
            return;
        }
        Pair<Boolean, Boolean> result = this.judgeTiming(FactType.GAMING);
        if (result.getSecond()) {
            // 强制下线
            forceLogout();
        }
    }

    /**
     * 下线时执行judgeTiming
     */
    private void judgeTimingWhenLogout() {
        cancelJudgeTimingTimer();
        cancelForceLogoutTimer();
        this.judgeTiming(FactType.GAME_END);
        // judgeTiming会设置定时器
        cancelForceLogoutTimer();
    }

    private void cancelJudgeTimingTimer() {
        if (judgeTimingTimer != null) {
            judgeTimingTimer.cancel();
            judgeTimingTimer = null;
        }
    }

    private void cancelForceLogoutTimer() {
        if (forceLogoutTask != null) {
            forceLogoutTask.cancel();
        }
    }

    /**
     * 计时决策
     *
     * @return <是否通过决策，是否立即下线>
     */
    private Pair<Boolean, Boolean> judgeTiming(FactType factType) {
        if (!isAntiAddictionOpen()) {
            return Pair.of(true, false);
        }
        CommonMsg.ClientInfo clientInfo = getOwner().getSessionComponent().getClientInfo();
        String clientIp = getOwner().getSessionComponent().getClientIp();
        long now = SystemClock.now();
        int duration = (int) TimeUtils.ms2Second(now - this.lastJudgeTimingTsMs);
        this.lastJudgeTimingTsMs = now;
        try {
            LOGGER.info("PlayerAntiAddictionComponent judgeTiming start {} factType={}", getOwner(), factType);
            SsAntiAddiction.JudgeTimingAsk.Builder ask = SsAntiAddiction.JudgeTimingAsk.newBuilder()
                    .setUserId(getOwner().getOpenId())
                    .setFactType(factType.getFactType())
                    .setUserId(getOwner().getOpenId())
                    .setDuration(duration)
                    .setDeviceInfo(SsAntiAddiction.DeviceInfo.newBuilder()
                            .setOuterIp(clientIp)
                            .setDeviceId(clientInfo.getDeviceId())
                            .setQimei36("")
                            .build());
            SsAntiAddiction.JudgeTimingAns ans = getOwner().ownerActor().callAndCreate(RefFactory.ofAntiAddiction(), ask.build());
            LOGGER.info("PlayerAntiAddictionComponent judgeTiming {} factType={} ans={}", getOwner(), factType, ans);

            SsAntiAddiction.JudgeTimingResult result = ans.getResult();
            if (result.getRet() != 0) {
                // sdk返回错误码，不能登陆
                LOGGER.error("PlayerAntiAddictionComponent judgeTimingWhenLogin fail with error code {}", result.getRet());
                return Pair.of(false, false);
            }
            // 有提示
            if (result.getInstructionsCount() > 0) {
                User.HopeInstruction instruction = result.getInstructions(0);
                // 通知客户端弹框
                notifyClient(instruction, result.getTraceId());

                // 强制下线
                if (instruction.getType() == InstructionType.LOGOUT.getInstructionType()) {
                    // 执行上报
                    reportExecute(instruction.getRuleName(), result.getTraceId(), SystemClock.nowOfSeconds());
                    return Pair.of(false, true);
                }

                // 预踢人
                if (instruction.getType() == InstructionType.PRELOGOUT.getInstructionType()) {
                    boolean kickOffNow = setForceLogoutTimer(instruction.getLogoutTime(), instruction, result.getTraceId());
                    if (kickOffNow) {
                        // 预踢人返回的下线时间戳过期了，强制下线
                        return Pair.of(false, true);
                    }
                }
            }
            // 决策通过，正常游戏
            return Pair.of(true, false);
        } catch (Exception e) {
            LOGGER.error("PlayerAntiAddictionComponent judgeTiming failed. factType={} ", factType, e);
            return Pair.of(false, false);
        }
    }

    private void forceLogout() {
        cancelJudgeTimingTimer();
        cancelForceLogoutTimer();

        if (!getOwner().isOnline()) {
            return;
        }

        if (forceLogoutTask != null) {
            User.HopeInstruction instruction = forceLogoutTask.instruction;
            String traceId = forceLogoutTask.traceId;
            // 通知客户端弹框
            notifyClient(instruction, traceId);
            // 执行上报
            reportExecute(instruction.getRuleName(), traceId, SystemClock.nowOfSeconds());
        }
        // 踢人
        getOwner().kickOffMe(CommonEnum.SessionCloseReason.SCR_ANTI_ADDICTION);
    }

    private boolean setForceLogoutTimer(long logoutTime, User.HopeInstruction instruction, String traceId) {
        if (logoutTime < 0) {
            return false;
        }
        if (forceLogoutTask != null && logoutTime == forceLogoutTask.forceLogoutTsSec) {
            return false;
        }

        LOGGER.info("PlayerAntiAddictionComponent setForceLogoutTimer logoutTime={} traceId={}", logoutTime, traceId);
        cancelForceLogoutTimer();
        forceLogoutTask = new ForceLogoutTask(logoutTime, instruction, traceId);
        long delay = logoutTime - SystemClock.nowOfSeconds();
        if (delay <= 0) {
            // 预踢人返回的下线时间戳过期了，下线
            return true;
        } else {
            // 开启强制下线定时器
            forceLogoutTask.forceLogoutTimer = ownerActor().addTimer(
                    ANTI_ADDICTION_FORCE_LOGOUT,
                    this::forceLogout,
                    delay,
                    TimeUnit.SECONDS);
        }
        return false;
    }

    private void notifyClient(User.HopeInstruction instruction, String traceId) {
        User.HopeCommandNtfMsg.Builder ntfMsgBuilder = User.HopeCommandNtfMsg.newBuilder()
                .setTraceId(traceId)
                .mergeInstruction(instruction);
        getOwner().sendMsgToClient(MsgType.HOPECOMMANDNTFMSG, ntfMsgBuilder.build());
    }

    /**
     * 充值决策
     * <p>
     * 只给版署版本使用！！！
     */
    public PlayerPayment.Player_JudgeRecharge_S2C judgePay(int chargeSdkId) {
        if (!ServerContext.isBanShuSvr()) {
            WechatLog.error("midas_perf {} not banshuSvr but judgePay !", getPlayerId());
            throw new GeminiException(ErrorCode.SYSTEM_MAINTAIN);
        }
        if (ServerContext.isProdSvr()) {
            WechatLog.error("midas_perf {} prodSvr judgePay !", getPlayerId());
            throw new GeminiException(ErrorCode.SYSTEM_MAINTAIN);
        }
        if (!isAntiAddictionOpen()) {
            return PlayerPayment.Player_JudgeRecharge_S2C.newBuilder().setChargeSdkId(chargeSdkId).build();
        }
        LOGGER.info("PlayerAntiAddictionComponent judgePay chargeSdkId={}", chargeSdkId);
        ChargeSdkTemplate sdkTemplate = ResHolder.getTemplate(ChargeSdkTemplate.class, chargeSdkId);
        int price = (int) (sdkTemplate.getPrice() * 100);

        PlayerPayment.Player_JudgeRecharge_S2C.Builder builder = PlayerPayment.Player_JudgeRecharge_S2C.newBuilder();
        try {
            SsAntiAddiction.JudgePayAsk.Builder ask = SsAntiAddiction.JudgePayAsk.newBuilder()
                    .setUserId(getOwner().getOpenId())
                    .setPayAmount(price);
            SsAntiAddiction.JudgePayAns ans = getOwner().ownerActor().callAndCreate(RefFactory.ofAntiAddiction(), ask.build());
            LOGGER.info("PlayerAntiAddictionComponent judgePay {} ans={}", getOwner(), ans);

            SsAntiAddiction.JudgePayResult result = ans.getResult();
            if (result.getRet() != 0) {
                LOGGER.error("PlayerAntiAddictionComponent judgeTiming fail with error code {}", result.getRet());
                builder.setChargeSdkId(-1);
            } else if (result.getInstructionsCount() <= 0) {
                builder.setChargeSdkId(chargeSdkId);
            } else {
                // 有提示
                User.HopeInstruction instruction = result.getInstructions(0);
                if (instruction.getType() == InstructionType.STOP.getInstructionType()) {
                    // 不能充值
                    builder.setChargeSdkId(-1);
                } else {
                    builder.setChargeSdkId(chargeSdkId);
                }
                builder.setTraceId(result.getTraceId());
                builder.mergeInstruction(instruction);
            }
            return builder.build();
        } catch (Exception e) {
            LOGGER.error("PlayerAntiAddictionComponent judgePay failed. ", e);
            // wiki说的，https://hope.qq.com/wiki/htdocs/document/view.html?file=7/245
            // 没有返回指令或系统出错时，请允许用户充值
            builder.setChargeSdkId(chargeSdkId);
            return builder.build();
        }
    }

    /**
     * 充值上报
     * <p>
     * 只给版署版本使用！！！
     */
    public void reportPay(ChargeSdkTemplate sdkTemplate) {
        if (!ServerContext.isBanShuSvr()) {
            return;
        }
        if (ServerContext.isProdSvr()) {
            return;
        }
        if (!isAntiAddictionOpen()) {
            return;
        }
        int price = (int) (sdkTemplate.getPrice() * 100);
        int payTimestamp = (int) TimeUtils.ms2Second(SystemClock.now());
        String payOrderId = UUID.randomUUID().toString().replace("-", "");

        SsAntiAddiction.ReportPayCmd.Builder cmd = SsAntiAddiction.ReportPayCmd.newBuilder()
                .setPayInfo(SsAntiAddiction.PayInfo.newBuilder()
                        .setPayAmount(price)
                        .setUserId(getOwner().getOpenId())
                        .setPayTimestamp(payTimestamp)
                        .setPayOrderId(payOrderId).build());
        LOGGER.info("PlayerAntiAddictionComponent reportPay {} cmd={}", getOwner(), cmd.build());
        getOwner().ownerActor().tell(RefFactory.ofAntiAddiction(), cmd.build());
    }

    /**
     * 执行上报
     */
    public void reportExecute(String ruleName, String instrTraceId, long execTime) {
        if (!isAntiAddictionOpen()) {
            return;
        }
        SsAntiAddiction.ReportExecuteCmd.Builder cmd = SsAntiAddiction.ReportExecuteCmd.newBuilder()
                .setUserId(getOwner().getOpenId())
                .setRuleName(ruleName)
                .setInstrTraceId(instrTraceId)
                .setExecTime(execTime);
        LOGGER.info("PlayerAntiAddictionComponent reportExecute {} cmd={}", getOwner(), cmd.build());
        getOwner().ownerActor().tell(RefFactory.ofAntiAddiction(), cmd.build());
    }

    static class ForceLogoutTask {
        /**
         * 强制下线定时器
         */
        public ActorTimer forceLogoutTimer;
        /**
         * 强制下线时间戳
         */
        public final long forceLogoutTsSec;
        public final User.HopeInstruction instruction;
        public final String traceId;

        ForceLogoutTask(long forceLogoutTsSec, User.HopeInstruction instruction, String traceId) {
            this.forceLogoutTsSec = forceLogoutTsSec;
            this.instruction = instruction;
            this.traceId = traceId;
        }

        public void cancel() {
            if (forceLogoutTimer != null) {
                forceLogoutTimer.cancel();
                forceLogoutTimer = null;
            }
        }
    }
}

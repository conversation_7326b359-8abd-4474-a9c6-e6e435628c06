package com.yorha.cnc.player.task.checker;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerBaseChargeEvent;
import com.yorha.cnc.player.event.task.PlayerBuyGoodsEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.BUY_GOODS_TIMES;
import static com.yorha.common.enums.statistic.StatisticEnum.CHARGE_BASE_TIMES;

/**
 * 充值X次
 */
public class ChargeTimesChecker extends AbstractTaskChecker {
    public static List<String> attentionList = ImmutableList.of(
            ClassNameCacheUtils.getSimpleName(CheckTaskProcessEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerBaseChargeEvent.class),
            ClassNameCacheUtils.getSimpleName(PlayerBuyGoodsEvent.class)
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        final List<Integer> taskParams = taskTemplate.getTypeValueList();
        final int requireTimes = taskParams.get(0);
        final PlayerEntity player = event.getPlayer();

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                long chargeBaseTimes = player.getStatisticComponent().getSingleStatistic(CHARGE_BASE_TIMES);
                long buyGoodsTimes = player.getStatisticComponent().getSingleStatistic(BUY_GOODS_TIMES);
                prop.setProcess(Math.min(requireTimes, (int) (chargeBaseTimes + buyGoodsTimes)));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerBaseChargeEvent || event instanceof PlayerBuyGoodsEvent) {
                    prop.setProcess(Math.min(requireTimes, prop.getProcess() + 1));
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= requireTimes;
    }
}

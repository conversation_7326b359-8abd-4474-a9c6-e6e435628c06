package com.yorha.cnc.player.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.task.GatherTreatedSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerSpeedUpEvent;
import com.yorha.common.asset.AssetDesc;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.AssetType;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.QueueSpeedReason;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.QueueTaskProp;
import com.yorha.game.gen.prop.QueueTasksProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg.HospitalTreatUnion;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;

import java.util.List;
import java.util.Map;

import static com.yorha.common.constant.GameLogicConstants.HOSPITAL_OCCUPY_QUEUE_TASK_ID;

/**
 * 医院（维修站），真实的医院数据在 ScenePlayer上，这里仅作为医院操作的壳
 *
 * <AUTHOR>
 */
public class PlayerHospitalComponent extends PlayerComponent implements PlayerQueueTaskComponent.QueueTaskInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerHospitalComponent.class);

    public PlayerHospitalComponent(PlayerEntity owner) {
        super(owner);
    }

    private void tryUnlockTreatmentQueue() {
        // 解锁治疗队列
        QueueTasksProp queueTasks = getOwner().getPlayerQueueTaskComponent().getQueueTasks(CommonEnum.QueueTaskType.MEDICAL_TREATMENT);
        if (queueTasks == null) {
            getOwner().getPlayerQueueTaskComponent().unLockQueue(true, CommonEnum.QueueTaskType.MEDICAL_TREATMENT, 0);
        }
    }

    public void handleTreat(HospitalTreatUnion treatUnion) {
        assertParam(treatUnion);

        tryUnlockTreatmentQueue();

        // 消耗资源构建
        Pair<AssetPackage, Long> costCurrency2Millis = calcCost(treatUnion);
        AssetPackage costPackage = costCurrency2Millis.getFirst();

        final long totalCostMillis = costCurrency2Millis.getSecond();
        final long taskFinTsMs = totalCostMillis + SystemClock.now();

        PlayerQueueTaskComponent queueTaskComponent = getOwner().getPlayerQueueTaskComponent();
        // 队列相关检测
        queueTaskComponent.queueFreeCheck(HOSPITAL_OCCUPY_QUEUE_TASK_ID, CommonEnum.QueueTaskType.MEDICAL_TREATMENT, taskFinTsMs);

        // 资源是否足够
        getOwner().verifyThrow(costPackage);

        SsScenePlayer.HospitalTreatCheckAsk.Builder checkAsk = SsScenePlayer.HospitalTreatCheckAsk.newBuilder()
                .setPlayerId(getPlayerId())
                .setTreatUnion(treatUnion);
        // 先检查治疗的士兵能不能行
        ownerActor().callBigScene(checkAsk.build());

        // 扣资源
        getOwner().consume(costPackage, CommonEnum.Reason.ICR_MEDICAL_TREATMENT);
        tryOccupyTreatmentQueue(queueTaskComponent, totalCostMillis);
        // 将等待的士兵挪到治疗中去
        SsScenePlayer.HospitalTreatAsk.Builder treatAsk = SsScenePlayer.HospitalTreatAsk.newBuilder()
                .setPlayerId(getPlayerId())
                .setTreatUnion(treatUnion);
        ownerActor().callBigScene(treatAsk.build());
    }

    private void assertParam(HospitalTreatUnion treatUnion) {
        for (StructPB.PlayerHospitalSoldierPB item : treatUnion.getToTreatSoldiers().getDatasMap().values()) {
            if (item.getSoldierId() <= 0 || item.getSevereNum() <= 0) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }

        if (treatUnion.hasToTreatExclusive() && treatUnion.getToTreatExclusive().getSoldierId() > 0) {
            int exclusiveSoldierId = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getHospitalExclusiveSoldierId();
            if (treatUnion.getToTreatExclusive().getSoldierId() != exclusiveSoldierId) {
                throw new GeminiException(ErrorCode.TREAT_SOLDIERID_ILLEGAL);
            }
            if (treatUnion.getToTreatExclusive().getSevereNum() <= 0) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }
    }

    /**
     * 计算治疗一坨兵的消耗资源+时间
     */
    private Pair<AssetPackage, Long> calcCost(HospitalTreatUnion treatUnion) {
        long[] totalCostMillis = {0};
        Map<CommonEnum.CurrencyType, Integer> costCurrency = Maps.newEnumMap(CommonEnum.CurrencyType.class);
        // 普通区治疗的消耗
        for (StructPB.PlayerHospitalSoldierPB toTreatItem : treatUnion.getToTreatSoldiers().getDatasMap().values()) {
            calcCost4Single(toTreatItem, totalCostMillis, costCurrency);
        }
        // 专区治疗消耗
        if (treatUnion.hasToTreatExclusive()) {
            calcCost4Single(treatUnion.getToTreatExclusive(), totalCostMillis, costCurrency);
        }
        // 治疗有个最小时间
        final int hospitalTreatMinSeconds = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getHospitalTreatMinSeconds();
        totalCostMillis[0] = Math.max(totalCostMillis[0], TimeUtils.second2Ms(hospitalTreatMinSeconds));

        // 治疗的资源消耗需要从训练消耗 * 系数
        final int costPercent = ResHolder.getConsts(ConstTemplate.class).getHospitalCostPercent();
        AssetPackage.Builder finalCost = AssetPackage.builder();
        costCurrency.forEach((type, num) -> {
            final long finalNum = ((long) num * costPercent) / Constants.N_100;
            if (finalNum > 0) {
                finalCost.plusCurrency(type, finalNum);
            }
        });
        return new Pair<>(finalCost.build(), totalCostMillis[0]);
    }

    /**
     * 这里算出来的时间是最终时间
     * <p>
     * 但是算出来的资源消耗是训练的消耗，累加之后需要再乘以一个系数
     */
    private void calcCost4Single(StructPB.PlayerHospitalSoldierPB toTreatItem, long[] totalCostMillis, Map<CommonEnum.CurrencyType, Integer> costCurrency) {
        if (toTreatItem.getSevereNum() <= 0) {
            return;
        }
        SoldierTypeTemplate soldierTypeTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, toTreatItem.getSoldierId());
        // 治疗的资源消耗现在改成用训练的消耗乘以一个系数
        List<IntPairType> trainRssCostPairList = soldierTypeTemplate.getTrainRssCostPairList();
        for (IntPairType rssType2num : trainRssCostPairList) {
            CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(rssType2num.getKey());
            int cost = (int) PlayerAddCalc.getSoldierTreatCostValue(getOwner(), rssType2num, toTreatItem.getSevereNum());
            costCurrency.merge(currencyType, cost, Integer::sum);
        }
        totalCostMillis[0] += PlayerAddCalc.getSoldierTreatCostTime(getOwner(), soldierTypeTemplate, toTreatItem.getSevereNum());
    }

    private void tryOccupyTreatmentQueue(PlayerQueueTaskComponent queueTaskComponent, long totalCostMillis) {
        // 懒式解锁医疗队列，并不需要在注册时就做这个事，第一次治疗时被动解锁就好了
        tryUnlockTreatmentQueue();
        // 占队列
        queueTaskComponent.addQueueTask(CommonEnum.QueueTaskType.MEDICAL_TREATMENT, HOSPITAL_OCCUPY_QUEUE_TASK_ID, totalCostMillis, null, StructPlayerPB.QueueExtraParamPB.getDefaultInstance());
    }

    public void handleFastTreat(HospitalTreatUnion treatUnion, String sPassWord) {
        assertParam(treatUnion);

        // 计算原始消耗
        Pair<AssetPackage, Long> costCurrency2Millis = calcCost(treatUnion);
        Pair<AssetPackage, AssetPackage> cost2RemainAssetPack = getOwner().getAssetComponent().calcAssetToGold(costCurrency2Millis.getFirst());

        // 时间消耗
        Long totalCostMs = costCurrency2Millis.getSecond();
        AssetPackage costTime = getOwner().getAssetComponent().calcTimeMsToGold(totalCostMs);

        AssetPackage cost = AssetPackage.builder()
                .plus(costTime)
                .plus(cost2RemainAssetPack.getFirst())
                .build();
        // 二级密码校验
        for (AssetDesc desc : cost.getImmutableAssets()) {
            if ((desc.getType() == AssetType.CURRENCY) && (desc.getId() == CommonEnum.CurrencyType.DIAMOND.getNumber())) {
                getOwner().getSettingComponent().checkSpassword(CommonEnum.SPassWordCheckType.SPWC_MANY_MONEY, desc.getAmount(), sPassWord);
            }
        }
        getOwner().verifyThrow(cost);


        SsScenePlayer.HospitalTreatCheckAsk checkAsk = SsScenePlayer.HospitalTreatCheckAsk.newBuilder()
                .setPlayerId(getPlayerId())
                .setTreatUnion(treatUnion)
                .build();
        ownerActor().callBigScene(checkAsk);

        // 2、资源消耗
        getOwner().consume(cost, CommonEnum.Reason.ICR_MEDICAL_TREATMENT);
        // 3、归还多扣的
        getOwner().getAssetComponent().give(cost2RemainAssetPack.getSecond(), CommonEnum.Reason.ICR_HOSPITAL_G2R_RETURN);

        SsScenePlayer.HospitalFastTreatAsk fastTreatAsk = SsScenePlayer.HospitalFastTreatAsk.newBuilder()
                .setPlayerId(getPlayerId())
                .setTreatUnion(treatUnion)
                .build();
        SsScenePlayer.HospitalFastTreatAns ans = ownerActor().callBigScene(fastTreatAsk);
        Map<Integer, Integer> soldierId2Num = ans.getSoldierId2NumMap();
        int treatNum = 0;
        for (Integer num : soldierId2Num.values()) {
            treatNum += num;
        }
        // 更新任务进度
        getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.FINISH_TREAT_SOLDIER_NUM, treatNum);
        new GatherTreatedSoldierEvent(getOwner(), treatNum).dispatch();

        long totalCostSec = TimeUtils.ms2Second(totalCostMs);
        new PlayerSpeedUpEvent(getOwner(), totalCostSec, CommonEnum.QueueTaskType.MEDICAL_TREATMENT, QueueSpeedReason.STRAIGHTLY_ACCELERATE).dispatch();
        getOwner().getQlogComponent().sendQueueSpeedQLogWithQType(HOSPITAL_OCCUPY_QUEUE_TASK_ID, totalCostSec, QueueSpeedReason.STRAIGHTLY_ACCELERATE, CommonEnum.QueueTaskType.MEDICAL_TREATMENT);
    }

    public Map<Integer, Integer> handleReturnSoldiers() {
        SsScenePlayer.ReturnTreatOverSoldiersAsk.Builder call = SsScenePlayer.ReturnTreatOverSoldiersAsk.newBuilder()
                .setPlayerId(getPlayerId());
        // 还到scene上面去
        final SsScenePlayer.ReturnTreatOverSoldiersAns ans = ownerActor().callCurScene(call.build());


        Map<Integer, Integer> soldierId2Num = ans.getSoldierId2NumMap();
        int treatNum = 0;
        for (Integer num : soldierId2Num.values()) {
            treatNum += num;
        }
        getOwner().getStatisticComponent().recordSingleStatistic(StatisticEnum.FINISH_TREAT_SOLDIER_NUM, treatNum);
        new GatherTreatedSoldierEvent(getOwner(), treatNum).dispatch();
        return soldierId2Num;
    }

    @Override
    public void onFinish(long taskId, QueueTaskProp taskProp) {
        if (taskId == HOSPITAL_OCCUPY_QUEUE_TASK_ID) {
            SsScenePlayer.HospitalTreatFinishCmd treatFinishAsk = SsScenePlayer.HospitalTreatFinishCmd.newBuilder()
                    .setPlayerId(getPlayerId())
                    .build();
            ownerActor().tellBigScene(treatFinishAsk);
        } else {
            LOGGER.error("PlayerHospitalComponent onFinish taskId={}, no handle", taskId);
        }

    }

    @Override
    public Map<Integer, Integer> immediatelyFinishPost(long taskId, QueueTaskProp taskProp) {
        Map<Integer, Integer> map = handleReturnSoldiers();
        LOGGER.info("立即完成自动领取治疗完成的士兵:{}, isInBigScene={}", map, this.getOwner().isInMainScene());
        return map;
    }

    @Override
    public boolean canCancel() {
        // 治疗不让取消滴
        return false;
    }

    @Override
    public void onCancel(long taskId, QueueTaskProp taskProp) {
    }

    @Override
    public boolean isSpeedUpItemAvailable(CommonEnum.ItemUseType itemUseType) {
        return itemUseType == CommonEnum.ItemUseType.COMMON_SPEED || itemUseType == CommonEnum.ItemUseType.HOSPITAL_TREAT_SPEED;
    }

    @Override
    public CommonEnum.Reason speedUpUsingReason() {
        return CommonEnum.Reason.ICR_MEDICAL_TREATMENT;
    }

}

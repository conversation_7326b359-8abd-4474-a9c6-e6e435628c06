package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

import java.util.Map;

/**
 * 世界采集资源事件
 *
 * <AUTHOR>
 */
public class ResBuildingCollectResourceEvent extends PlayerTaskEvent {
    private final Map<Integer, Long> collectResMap;
    private int outTimes;

    public Map<Integer, Long> getCollectResMap() {
        return collectResMap;
    }

    public int getOutTimes() {
        return outTimes;
    }

    public ResBuildingCollectResourceEvent(PlayerEntity entity, Map<Integer, Long> collectResMap, int outTimes) {
        super(entity);
        this.collectResMap = collectResMap;
        this.outTimes = outTimes;
    }

}

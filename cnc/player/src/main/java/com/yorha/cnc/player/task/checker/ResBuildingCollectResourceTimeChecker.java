package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.ResBuildingCollectResourceEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.RESBUILDING_COLLECT_RESOURCE_TIME;

/**
 * 世界采集资源x次
 * param1: 次数
 *
 * <AUTHOR>
 */
public class ResBuildingCollectResourceTimeChecker extends AbstractTask<PERSON>hecker {

    public static List<String> attentionList = Lists.newArrayList(
            ResBuildingCollectResourceEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(
            PlayerTaskEvent event,
            TaskInfoProp prop,
            TaskPoolTemplate taskTemplate
    ) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.getFirst();

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int collectTime = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(RESBUILDING_COLLECT_RESOURCE_TIME);
            prop.setProcess(Math.min(collectTime, param1));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof ResBuildingCollectResourceEvent) {
                prop.setProcess(Math.min(prop.getProcess() + 1, param1));
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param1;
    }
}

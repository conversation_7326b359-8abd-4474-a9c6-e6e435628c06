package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 玩家直购礼包的事件，可以明确购买的是哪个goodsId
 */
public class PlayerBuyGoodsEvent extends PlayerTaskEvent {
    private final int goodsId;
    private final int directDiamond;

    public PlayerBuyGoodsEvent(PlayerEntity entity, int goodsId, int directDiamond) {
        super(entity);
        this.goodsId = goodsId;
        this.directDiamond = directDiamond;
    }

    public int getGoodsId() {
        return goodsId;
    }

    public int getDirectDiamond() {
        return directDiamond;
    }
}

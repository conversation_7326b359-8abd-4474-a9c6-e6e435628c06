package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.ExploreMapBuildFinEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 探索x次x类型的地图点
 *
 * <AUTHOR>
 */
public class ExploreMapBuildFinChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(ExploreMapBuildFinEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int type = taskTemplate.getTypeValueList().get(0);
        int count = taskTemplate.getTypeValueList().get(1);
        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                long exploreCityTotal = event.getPlayer().getStatisticComponent().getSecondStatistic(StatisticEnum.EXPLORE_WORLD_BUILDING_NUM_END, type);
                prop.setProcess((int) Math.min(exploreCityTotal, count));
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= count;
    }
}

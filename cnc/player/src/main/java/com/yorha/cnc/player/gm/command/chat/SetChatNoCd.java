package com.yorha.cnc.player.gm.command.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class SetChatNoCd implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        boolean isNoCd = true;
        if (args.containsKey("isNoCd")) {
            isNoCd = Integer.parseInt(args.get("isNoCd")) == 1;
        }
        ServerContext.getServerDebugOption().setChatNoCd(isNoCd);
    }

    @Override
    public String showHelp() {
        return "SetChatNoCd isNoCd={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.PlayerClanHelpEvent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ClanHelpItemProp;
import com.yorha.game.gen.prop.QueueTaskProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;
import com.yorha.proto.SsClanHelp;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.common.enums.statistic.StatisticEnum.CLAN_HELP_NUM_TOTAL;

/**
 * 军团帮助模块: 使用到ss_clan_help里ss协议的cs协议，可以放到这里~
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_HELP)
public class PlayerClanHelpController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerClanHelpController.class);

    /**
     * 发起联盟帮助请求
     */
    @CommandMapping(code = MsgType.PLAYER_CREATECLANHELP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_CreateClanHelp_C2S msg) {
        // 参数检查
        if (!msg.hasTaskId() || !msg.hasTaskType()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 参数 type 类型检查
        switch (msg.getTaskType()) {
            case CITY_BUILD:
            case MEDICAL_TREATMENT:
            case RESEARCH: {
                break;
            }
            default: {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
        }

        // 是否在联盟检查
        long clanId = playerEntity.getProp().getClan().getClanId();
        if (clanId <= 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }

        // 获取具体task
        CommonEnum.QueueTaskType type = msg.getTaskType();
        long taskId = msg.getTaskId();
        QueueTaskProp taskProp = playerEntity.getPlayerQueueTaskComponent().getQueueTaskProp(type, taskId);
        if (taskProp == null) {
            LOGGER.info("task id {} type {} cannot get its prop", taskId, type);
            throw new GeminiException(ErrorCode.QUQUE_QUEUE_HAS_EXPIRED);
        }

        // task同步给联盟
        SsClanHelp.SyncPlayerQueueTaskCmd.Builder ask = SsClanHelp.SyncPlayerQueueTaskCmd.newBuilder();
        ask.setTask(taskProp.getCopySsBuilder()).setPlayerId(playerEntity.getPlayerId());
        // 设置帮助记录
        ask.setHelpRecord(playerEntity.getPlayerClanComponent().buildClanHelpRecord(taskId, type));
        playerEntity.ownerActor().tellCurClan(ask.build());
        playerEntity.getPlayerQueueTaskComponent().setClanHelpFlag(taskId, type);
        return PlayerClan.Player_CreateClanHelp_S2C.getDefaultInstance();
    }

    /**
     * 拉取所有联盟帮助
     */
    @CommandMapping(code = MsgType.PLAYER_FETCHCLANHELPS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FetchClanHelps_C2S msg) {
        SsClanHelp.FetchClanHelpsAsk.Builder ask = SsClanHelp.FetchClanHelpsAsk.newBuilder();
        PlayerActor playerActor = playerEntity.ownerActor();
        ask.setPlayerId(playerEntity.getPlayerId());
        SsClanHelp.FetchClanHelpsAns ans = playerActor.callCurClan(ask.build());
        PlayerClan.Player_FetchClanHelps_S2C.Builder builder = PlayerClan.Player_FetchClanHelps_S2C.newBuilder();
        for (Struct.ClanHelpItem item : ans.getItemsMap().values()) {
            ClanHelpItemProp prop = new ClanHelpItemProp();
            // 玩家已帮助过该帮助直接跳过
            if (item.getAlreadyHelpPlayerIds().getDatasList().contains(playerEntity.getPlayerId())) {
                continue;
            }
            prop.mergeChangeFromSs(item);
            builder.addHelpItems(prop.getCopyCsBuilder().build());
        }
        builder.setRefreshTime(TimeUtils.getNextDayStartMs(SystemClock.now()));
        return builder.build();
    }

    /**
     * 完成所有联盟帮助
     */
    @CommandMapping(code = MsgType.PLAYER_FINISHALLCLANHELPS_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_FinishAllClanHelps_C2S msg) {
        SsClanHelp.FinishAllClanHelpsAsk.Builder ask = SsClanHelp.FinishAllClanHelpsAsk.newBuilder();
        ask.setPlayerId(playerEntity.getPlayerId());
        ask.setPlayerName(playerEntity.getName());
        PlayerActor playerActor = playerEntity.ownerActor();
        SsClanHelp.FinishAllClanHelpsAns ret = playerActor.callCurClan(ask.build());

        // 原来登科的意思估计是想把帮助和任务进度处理解耦吧，不过实际压力并没有那么大，完全可以顺序来做
        playerEntity.getStatisticComponent().recordSingleStatistic(CLAN_HELP_NUM_TOTAL, ret.getSuccessHelpTimes());
        new PlayerClanHelpEvent(playerEntity, ret.getSuccessHelpTimes()).dispatch();
        // 增加联盟援助次数
        playerEntity.getDataRecordComponent().updateDataRecord(CommonEnum.DataRecordType.DRT_ASSIST_ALLY_TIME, CommonEnum.DataRecordCalcType.DRCT_ADD, ret.getSuccessHelpTimes());
        return PlayerClan.Player_FinishAllClanHelps_S2C.getDefaultInstance();
    }
}

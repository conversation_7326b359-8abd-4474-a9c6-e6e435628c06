package com.yorha.cnc.player.friend;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.friend.component.FriendPlayerComponent;
import com.yorha.cnc.player.friend.component.FriendPlayerDbComponent;
import com.yorha.cnc.player.friend.component.FriendPlayerMaintenanceComponent;
import com.yorha.cnc.player.friend.component.FriendPlayerPropComponent;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.FriendPlayerProp;
import com.yorha.game.gen.prop.PlayerCardHeadProp;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.Player;
import com.yorha.proto.StructPB;
import res.template.ConstFriendTemplate;

import java.util.Collection;
import java.util.function.Consumer;

/**
 * 好友Entity
 *
 * <AUTHOR>
 */
public class FriendPlayerEntity extends AbstractEntity {

    private final PlayerActor actor;
    private final FriendPlayerProp prop;
    private boolean isStartZoneMigrate;

    public FriendPlayerEntity(PlayerActor playerActor, FriendPlayerProp prop, Player.FriendPlayer changed) {
        super(playerActor.getPlayerId());
        this.prop = prop;
        this.actor = playerActor;
        this.friendPlayerDbComponent = new FriendPlayerDbComponent(this, changed);
        // 所有组件的init和postInit
        initAllComponents();
    }

    public FriendPlayerProp getProp() {
        return prop;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Player;
    }

    @Override
    public PlayerActor ownerActor() {
        return actor;
    }

    @Override
    protected void onPostInitFailed() {

    }

    // ------------------------------------生命周期关键节点----------------------------------------

    /**
     * 调用所有组件的方法
     */
    private void callAllComponents(Consumer<? super FriendPlayerComponent> action) {
        Collection<FriendPlayerComponent> components = getAllComponents();
        for (FriendPlayerComponent component : components) {
            action.accept(component);
        }
    }

    /**
     * 安全的调用所有组件的方法
     */
    public void callAllComponentsSafe(Consumer<? super FriendPlayerComponent> action) {
        callAllComponents((component) -> {
            try {
                action.accept(component);
            } catch (Exception e) {
                WechatLog.error("friend id={} callAllComponents error", getEntityId(), e);
            }
        });
    }

    // ------------------------------------component-------------------------------------------
    private final FriendPlayerPropComponent friendPlayerPropComponent = new FriendPlayerPropComponent(this);
    private final FriendPlayerDbComponent friendPlayerDbComponent;
    private final FriendPlayerMaintenanceComponent friendPlayerMaintenanceComponent = new FriendPlayerMaintenanceComponent(this);

    public FriendPlayerPropComponent getPropComponent() {
        return friendPlayerPropComponent;
    }

    public FriendPlayerDbComponent getDbComponent() {
        return friendPlayerDbComponent;
    }

    public FriendPlayerMaintenanceComponent getMaintenanceComponent() {
        return friendPlayerMaintenanceComponent;
    }

    public boolean hasFriend(long playerId) {
        return getProp().getFriendList().containsKey(playerId);
    }

    public boolean hasShieldPlayer(long playerId) {
        return getProp().getShiledList().containsKey(playerId);
    }

    public boolean hasApply(long playerId) {
        return getProp().getApplyList().containsKey(playerId);
    }

    /**
     * 玩家好友是否达到上限
     */
    public boolean isFriendFull() {
        ConstFriendTemplate template = ResHolder.getConsts(ConstFriendTemplate.class);
        return getProp().getFriendList().size() >= template.getFriendMax();
    }

    /**
     * 玩家申请是否达到上限
     */
    public boolean isApplyFull() {
        ConstFriendTemplate template = ResHolder.getConsts(ConstFriendTemplate.class);
        return getProp().getApplyList().size() >= template.getApplyDMax();
    }

    /**
     * 玩家是否达到屏蔽上限
     */
    public boolean isShieldFull() {
        ConstFriendTemplate template = ResHolder.getConsts(ConstFriendTemplate.class);
        return getProp().getShiledList().size() >= template.getShieldMax();
    }

    public PlayerCardHeadProp getCardHead() {
        return getProp().getCardHead();
    }

    /**
     * 更新玩家好友数据，好友/申请/屏蔽列表中的某一项数据，由在好友界面点击某个玩家头像的事件触发
     */
    public void updateFriendData(long playerId, StructPB.PlayerCardHeadPB cardHead, int zoneId) {
        if (getProp().getFriendList().containsKey(playerId)) {
            getProp().getFriendList().get(playerId).setZoneId(zoneId)
                    .getCardHead().mergeChangeFromCs(cardHead);
        }
        if (getProp().getApplyList().containsKey(playerId)) {
            getProp().getApplyList().get(playerId).setZoneId(zoneId)
                    .getCardHead().mergeChangeFromCs(cardHead);
        }
        if (getProp().getShiledList().containsKey(playerId)) {
            getProp().getShiledList().get(playerId).setZoneId(zoneId)
                    .getCardHead().mergeChangeFromCs(cardHead);
        }
    }

    public int getFriendNum() {
        return getProp().getFriendList().size();
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        if (isStartZoneMigrate()) {
            // 如果处于移民中，前面已经全量落库过了
            return;
        }
        // 阻塞式落脏
        getDbComponent().saveOnDestroy();
        getProp().unMarkAll();
    }

    public boolean isStartZoneMigrate() {
        return isStartZoneMigrate;
    }

    public void startZoneMigrate() {
        isStartZoneMigrate = true;
        // 强制落库
        getDbComponent().saveAndDestroy();
    }
}

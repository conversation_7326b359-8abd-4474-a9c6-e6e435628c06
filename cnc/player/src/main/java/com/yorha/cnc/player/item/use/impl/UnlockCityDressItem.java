package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.city.CitySkinService;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import res.template.ItemTemplate;


public class UnlockCityDressItem extends AbstractUsableItem {
    public UnlockCityDressItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        int citySubDressTemplateId = template.getEffectValue();
        int cityDressTemplateId = ResHolder.getResService(CitySkinService.class).getDressId(citySubDressTemplateId);
        // 已永久解锁基地皮肤则补偿
        if (playerEntity.getCityDressComponent().hasEternalCityDress(cityDressTemplateId)) {
            for (IntPairType itemPair : template.getExchangeItemIdPairList()) {
                playerEntity.getItemComponent().addItem(itemPair.getKey(), itemPair.getValue(), CommonEnum.Reason.ICR_DRESS_EXCHANGE, "");
            }
        } else {
            playerEntity.getCityDressComponent().extendOrUnlockCityDress(citySubDressTemplateId);
        }
        // 直接切换至皮肤
        if (params.getUseDirectly()) {
            playerEntity.getCityDressComponent().changeCityDress(cityDressTemplateId);
        }
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }
}

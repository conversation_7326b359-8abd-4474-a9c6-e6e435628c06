package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerPlaneLevelUpEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.PLANE_LEVEL_UP_TOTAL;

/**
 * 空军升级
 * param1: 空军id
 * param2: 次数
 *
 * <AUTHOR>
 */
public class PlaneLevel<PERSON><PERSON><PERSON><PERSON><PERSON> extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerPlaneLevelUpEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        PlayerEntity entity = event.getPlayer();
        Integer planeId = taskParams.get(0);
        Integer times = taskParams.get(1);

        switch (taskTemplate.getTaskCalculationMethod()) {
            case TCT_CREATE: {
                int planeLevelUpTotal = (int) entity.getStatisticComponent().getSecondStatistic(PLANE_LEVEL_UP_TOTAL, planeId);
                prop.setProcess(Math.min(planeLevelUpTotal, times));
                break;
            }
            case TCT_RECEIVE: {
                if (event instanceof PlayerPlaneLevelUpEvent) {
                    if (planeId == ((PlayerPlaneLevelUpEvent) event).getPlaneType()) {
                        prop.setProcess(Math.min(times, prop.getProcess() + 1));
                    }
                }
                break;
            }
            default: {
                WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
            }
        }
        return prop.getProcess() >= times;
    }
}

package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.SeasonActivityHelper;
import com.yorha.common.qlog.json.item.QlogItemConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityLotterySeasonService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityLotteryUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.ArrayList;
import java.util.List;

public class PlayerActivityLotteryUnit extends BasePlayerActivityUnit {

    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityLotteryUnit.class);

    private boolean isInitOk = false;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_LOTTERY, (owner, prop, template) ->
                new PlayerActivityLotteryUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerActivityLotteryUnit(PlayerActivity activity, ActivityUnitProp prop) {
        super(activity, prop);
    }

    @Override
    public void load(boolean isInitial) {
        SsSceneActivitySchedule.GetLotteryInfoAsk ask = SsSceneActivitySchedule.GetLotteryInfoAsk.newBuilder().setActivityId(ownerActivity.getActivityId()).build();
        player().ownerActor().<SsSceneActivitySchedule.GetLotteryInfoAns>askSelfBigScene(player().getZoneId(), ask).onComplete((res, err) -> {
            if (err != null) {
                LOGGER.error("PlayerActivityLotteryUnit ask get lottery info error", err);
                return;
            }
            isInitOk = true;
            if (!res.hasSeason() || res.getVolume() <= 0) {
                LOGGER.info("PlayerActivityLotteryUnit get lottery info error, no season or stage or volume res={} activityId={}",
                        res, ownerActivity.getActivityId());
                return;
            }
            CommonEnum.CommanderActivtyStage actStage = SeasonActivityHelper.getActStage(res.getSeason(), res.getStage());
            LOGGER.info("PlayerActivityLotteryUnit get lottery info, stage={}, season={}, actStage={} volume={} activityId={}",
                    res.getStage(), res.getSeason(), actStage, res.getVolume(), ownerActivity.getActivityId());
            getProp().setCurActivityStage(actStage).setCurVolume(res.getVolume());
            if (getProp().getSettleHero() == 0) {
                LotterySeasonTemplate seasonConfig = ResHolder.getResService(ActivityLotterySeasonService.class).getSeasonConfig(actStage, getProp().getCurVolume());
                if (seasonConfig == null) {
                    WechatLog.error("PlayerActivityLotteryUnit get lottery info error, no season or stage or volume res={} activityId={}",
                            res, ownerActivity.getActivityId());
                    return;
                }
                int heroRewardId = seasonConfig.getHeroRewardIdsList().getFirst();
                getProp().setSettleHero(heroRewardId);
            }
            LOGGER.info("PlayerActivityLotteryUnit get lottery info, stage={}, season={}, actStage={} volume={}, heroRewardId={} activityId={}",
                    res.getStage(), res.getSeason(), actStage, res.getVolume(), getProp().getSettleHero(), ownerActivity.getActivityId());
        });
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
        trySendStageRewardMail();
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        checkUnitLoadOk();
        final int drawId = key.getLotteryDrawId();
        if (drawId > 0) {
            // 抽奖
            handleDraw(drawId, rsp);
        } else {
            final int stageRewardId = key.getLotteryStageRewardId();
            // 领取阶段奖励
            if (stageRewardId > 0) {
                handleTakeStageReward(stageRewardId);
            }
        }
    }

    private void checkUnitLoadOk() {
        if (!isInitOk) {
            throw new GeminiException(ErrorCode.LOTTERY_NOT_INIT_PLEASE_LOGIN);
        }
    }

    private void handleDraw(int drawId, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        checkUnitLoadOk();
        int activityId = this.ownerActivity.getActivityId();
        ActivityLotteryUnitProp prop = getProp();
        LotteryHeroRewardTemplate lotteryHeroTemplate = ResHolder.getInstance().getValueFromMap(LotteryHeroRewardTemplate.class, getProp().getSettleHero());

        if (lotteryHeroTemplate == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "reward is not exist");
        }
        List<Integer> drawIdsList = lotteryHeroTemplate.getDrawIdsList();
        if (!drawIdsList.contains(drawId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "illegal drawId");
        }
        LotteryDrawTemplate drawTemplate = ResHolder.getTemplate(LotteryDrawTemplate.class, drawId);
        // 第一次免费
        boolean isFree = drawTemplate.getCostTimes() <= 1 && getProp().getFreeTimes() <= 0;

        int finalTotalTimes = getProp().getTotalTimes() + drawTemplate.getCostTimes();
        if (finalTotalTimes > ResHolder.getConsts(ConstActivityTemplate.class).getTotalTimesLimit()) {
            throw new GeminiException(ErrorCode.LOTTERY_TOTAL_TIMES_LIMITED);
        }
        if (!isFree) {
            if (drawTemplate.getCostItemsPairList() != null && !drawTemplate.getCostItemsPairList().isEmpty()) {
                for (IntPairType pair : drawTemplate.getCostItemsPairList()) {
                    // 看看幸运券够不够
                    if (!player().getItemComponent().hasEnough(pair.getKey(), pair.getValue())) {
                        throw new GeminiException(ErrorCode.ITEM_NOT_ENOUGH);
                    }
                }
            } else {
                // 抽奖id非法
                throw new GeminiException(ErrorCode.LOTTERY_DRAW_NOT_EXIST);
            }
        }

        // 基本的校验都过了
        // 随机奖励
        AssetPackage.Builder rewardBuilder = AssetPackage.builder();
        ItemResService itemResService = ResHolder.getResService(ItemResService.class);
        for (int i = 0; i < drawTemplate.getCostTimes(); i++) {
            rewardBuilder.plusStandalone(itemResService.randomReward2Pack(drawTemplate.getRewardId()));
        }
        LOGGER.info("PlayerActivityLotteryUnit handleDraw, drawId={}, rewardBuilder={} isFree={}", drawId, rewardBuilder, isFree);
        List<QlogItemConfig> qlogItemConfigs = null;
        // 扣
        if (!isFree) {
            if (drawTemplate.getCostItemsPairList() != null && !drawTemplate.getCostItemsPairList().isEmpty()) {
                qlogItemConfigs = new ArrayList<>(drawTemplate.getCostItemsPairList().size());
                for (IntPairType pair : drawTemplate.getCostItemsPairList()) {
                    player().getItemComponent().consume(pair.getKey(), pair.getValue(), CommonEnum.Reason.ICR_LOTTERY_ACT_DRAW, String.valueOf(activityId));
                    QlogItemConfig itemConfig = new QlogItemConfig(pair.getKey(), pair.getValue());
                    qlogItemConfigs.add(itemConfig);
                }
            }
        } else {
            getProp().setFreeTimes(1);
        }
        // 记录
        prop.setTotalTimes(finalTotalTimes);

        // 奖
        AssetPackage rewardPack = rewardBuilder.build();
        player().getAssetComponent().give(rewardPack, CommonEnum.Reason.ICR_LOTTERY_ACT_DRAW, String.valueOf(activityId));

        rsp.setReward(rewardPack.toPb());
        player().getQlogComponent().sendShopQLog("lucky_wheels", "shop_exchange",
                qlogItemConfigs == null ? "" : JsonUtils.toJsonString(qlogItemConfigs),
                QlogItemConfig.fromAssetPackage(rewardPack));
        player().getQlogComponent().sendActivityScoreQlog("lucky_wheels", "", drawTemplate.getCostTimes(), prop.getTotalTimes(), "score_lucky_wheels");
    }

    private void handleTakeStageReward(int stageRewardId) {
        checkUnitLoadOk();
        int activityId = this.ownerActivity.getActivityId();
        LotteryHeroRewardTemplate lotteryHeroTemplate = ResHolder.getInstance().getValueFromMap(LotteryHeroRewardTemplate.class, getProp().getSettleHero());

        if (!lotteryHeroTemplate.getStageRewardIdsList().contains(stageRewardId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "illegal stageRewardId");
        }

        LotteryStageRewardTemplate stageRewardTemplate = ResHolder.getTemplate(LotteryStageRewardTemplate.class, stageRewardId);
        ActivityLotteryUnitProp prop = unitProp.getLotteryUnit();
        if (!prop.getTakenStageRewardIds().isEmpty()) {
            // 遍历身上已领过的奖励，有相同stageGroup的要报错
            for (Integer takenStageRewardId : prop.getTakenStageRewardIds()) {
                LotteryStageRewardTemplate template = ResHolder.getTemplate(LotteryStageRewardTemplate.class, takenStageRewardId);
                if (template.getStageGroup() == stageRewardTemplate.getStageGroup()) {
                    throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
                }
            }
        }
        if (prop.getTakenStageRewardIds().contains(stageRewardId)) {
            throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
        }
        if (prop.getTotalTimes() < stageRewardTemplate.getNeedTimes()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "times not enough");
        }

        prop.addTakenStageRewardIds(stageRewardId);

        AssetPackage reward = AssetPackage.builder().plusItems(stageRewardTemplate.getRewardPairList()).build();
        player().getAssetComponent().give(reward, CommonEnum.Reason.ICR_LOTTERY_ACT_STAGE, String.valueOf(activityId));

        player().getQlogComponent().sendActivityScoreQlog("collect_act_reward", "", 0, stageRewardTemplate.getNeedTimes(), "score_lucky_wheels");

    }

    private void trySendStageRewardMail() {
        int settleHero = getProp().getSettleHero();
        if (settleHero <= 0) {
            LOGGER.info("PlayerActivityLotteryUnit trySendStageRewardMail, settleHero={} is empty", settleHero);
            return;
        }
        LotteryHeroRewardTemplate lotteryHeroTemplate = ResHolder.getInstance().getValueFromMap(LotteryHeroRewardTemplate.class, settleHero);

        int mailId = lotteryHeroTemplate.getStageRewardMailId();
        if (mailId <= 0) {
            return;
        }
        ActivityLotteryUnitProp prop = unitProp.getLotteryUnit();
        List<IntPairType> reward = Lists.newArrayList();
        for (Integer stageRewardId : lotteryHeroTemplate.getStageRewardIdsList()) {
            if (!prop.getTakenStageRewardIds().contains(stageRewardId)) {
                LotteryStageRewardTemplate stageRewardTemplate = ResHolder.getTemplate(LotteryStageRewardTemplate.class, stageRewardId);
                if (prop.getTotalTimes() >= stageRewardTemplate.getNeedTimes()) {
                    reward.addAll(stageRewardTemplate.getRewardPairList());
                }
            }
        }
        if (!reward.isEmpty()) {
            StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder()
                    .setMailTemplateId(mailId);
            Struct.ItemPairList.Builder b = Struct.ItemPairList.newBuilder();
            for (IntPairType pair : reward) {
                b.addDatas(Struct.ItemPair.newBuilder()
                        .setItemTemplateId(pair.getKey())
                        .setCount(pair.getValue())
                        .build());
            }
            params.setItemReward(b);
            final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                    .setPlayerId(ownerActivity.getPlayer().getPlayerId())
                    .setZoneId(ownerActivity.getPlayer().getZoneId())
                    .build();

            player().getMailComponent().sendPersonalMail(receiver, params.build());
        }
    }

    /**
     * 购买抽奖代币券
     */
    public void buyVoucher(int num) {
        checkUnitLoadOk();
        int activityId = this.ownerActivity.getActivityId();
        int afterTimes = getProp().getVoucherBuyTimes() + num;
        ConstActivityTemplate consts = ResHolder.getConsts(ConstActivityTemplate.class);
        if (afterTimes > consts.getGoldBuyNum()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "Lottery Voucher But Times Limit");
        }
        // 扣钱
        int price = num * consts.getLuckyBuyNum();
        player().getPurseComponent().consume(CommonEnum.CurrencyType.DIAMOND, price, CommonEnum.Reason.ICR_LOTTERY_ACT_DRAW, String.valueOf(activityId));
        // 更新属性
        unitProp.getLotteryUnit().setVoucherBuyTimes(afterTimes);
        // 加道具
        player().getItemComponent().addItem(consts.getItemNumid(), num, CommonEnum.Reason.ICR_LOTTERY_ACT_DRAW, String.valueOf(activityId));
        LOGGER.info("Buy Lottery Voucher num={}", num);
    }

    private ActivityLotteryUnitProp getProp() {
        return unitProp.getLotteryUnit();
    }

    public void settleHero(int id) {
        checkUnitLoadOk();
        CommonEnum.CommanderActivtyStage actStage = getProp().getCurActivityStage();
        LotterySeasonTemplate seasonConfig = ResHolder.getResService(ActivityLotterySeasonService.class).getSeasonConfig(actStage, getProp().getCurVolume());
        if (seasonConfig == null) {
            throw new GeminiException(ErrorCode.LOTTERY_NOT_INIT_PLEASE_LOGIN);
        }
        if (!seasonConfig.getHeroRewardIdsList().contains(id)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ActivityLotteryUnitProp lotteryUnit = getProp();
        if (lotteryUnit.getSettleHero() == id) {
            throw new GeminiException(ErrorCode.LOTTERY_HERO_SETTLED);
        }
        LOGGER.info("PlayerActivityLotteryUnit settleHero id={}", id);
        lotteryUnit.setSettleHero(id);
    }
}




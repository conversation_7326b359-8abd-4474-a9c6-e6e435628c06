package com.yorha.cnc.player.component;


import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAddCalc;
import com.yorha.cnc.player.event.task.TroopHeroChangeEvent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.HeroHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.hero.HeroTemplateService;
import com.yorha.common.resource.resservice.troop.TroopResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.SsScenePlayer.GetAllSoldierAns;
import com.yorha.proto.SsScenePlayer.GetAllSoldierAsk;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.HeroLevelRhTemplate;
import res.template.HeroRhTemplate;
import res.template.UnitRhTemplate;

import java.util.Map;

/**
 * 部队编队组件
 *
 * <AUTHOR>
 * 2022年1月13日
 */
public class PlayerTroopFormationComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerTroopFormationComponent.class);

    private PlayerFormationProp playerFormationProp = null;

    public PlayerTroopFormationComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerFormationProp getFormationProp() {
        return playerFormationProp;
    }

    @Override
    public void init() {
        playerFormationProp = getOwner().getProp().getFormation();
    }

    /**
     * 增加新的编队
     */
    public void addNewFormation(StructPlayerPB.FormationPB formationPB) {
        LOGGER.debug("{} add new formation:{}", getOwner(), formationPB);

        int mainHeroId = formationPB.getMainHeroId();
        int deputyHeroId = formationPB.getDeputyHeroId();
        // 英雄解锁检测
        PlayerHeroComponent heroComponent = getOwner().getHeroComponent();

        heroComponent.checkHeroProp(mainHeroId);
        if (deputyHeroId > 0) {
            if (deputyHeroId == mainHeroId) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
            }
            heroComponent.checkHeroProp(deputyHeroId);
            // 主将三星才能配带副将的规则检查
            if (getOwner().getHeroComponent().cantBringDeputy(mainHeroId)) {
                throw new GeminiException(ErrorCode.HERO_MAINHERO_STAR_LIMIT);
            }
        }

        int formationId = formationPB.getFormationId();
        if (formationId <= 0 || formationId > ResHolder.getResService(ConstKVResService.class).getTemplate().getFormationNumLimit()) {
            throw new GeminiException(ErrorCode.FORMATION_ID_NULL.getCodeId());
        }

        // 远程获取ScenePlayer在内城的空闲士兵
        GetAllSoldierAns ans = ownerActor().callBigScene(GetAllSoldierAsk.newBuilder().setPlayerId(getPlayerId()).build());
        Map<Integer, Integer> inCitySoldierMap = ans.getSoldierMap();
        long realTotalSoldierNum = 0;
        // 检测兵力
        for (StructPB.SoldierPB soldierPb : formationPB.getSoldiers().getDatasMap().values()) {
            int soldierNum = soldierPb.getNum();
            int playerSoldierId = soldierPb.getSoldierId();
            int playerSoldierNum = inCitySoldierMap.getOrDefault(playerSoldierId, 0);
            if (soldierNum <= 0 || soldierNum > playerSoldierNum) {
                throw new GeminiException(ErrorCode.SOLDIER_NUM_SHORTAGE.getCodeId());
            }
            realTotalSoldierNum += soldierNum;
        }

        if (realTotalSoldierNum <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // 最大出兵量检测
        Struct.Hero mainHero = getOwner().getHeroComponent().getHero(mainHeroId);
        Struct.Hero deputyHero = getOwner().getHeroComponent().getHero(deputyHeroId);
        Map<CommonEnum.BuffEffectType, Long> playerHeroAddition = PlayerAddCalc.getPlayerTroopCapAddition(getOwner());
        long soldierMaxNum = HeroHelper.getSoldierMaxNum(0, mainHero, deputyHero, playerHeroAddition);
        if (realTotalSoldierNum > soldierMaxNum) {
            throw new GeminiException(ErrorCode.TROOP_INSUFFICIENT_TROOPS.getCodeId());
        }

        // 创建编队
        createFormation(formationPB);
    }

    private void createFormation(StructPlayerPB.FormationPB formationPB) {
        int mainHeroId = formationPB.getMainHeroId();
        int deputyHeroId = formationPB.getDeputyHeroId();
        int formationId = formationPB.getFormationId();
        String formationName = formationPB.getName();
        FormationProp localFormationAttrData = new FormationProp();
        localFormationAttrData.setFormationId(formationId);
        localFormationAttrData.setMainHeroId(mainHeroId);
        if (formationPB.getDeputyHeroId() > 0) {
            localFormationAttrData.setDeputyHeroId(deputyHeroId);
        }
        if (StringUtils.isNotEmpty(formationName)) {
            localFormationAttrData.setName(formationName);
        }
        // 本地获取ScenePlayer在内城的空闲士兵
        for (StructPB.SoldierPB soldierPB : formationPB.getSoldiers().getDatasMap().values()) {
            SoldierProp newSoldierProp = localFormationAttrData.getSoldiers().addEmptyValue(soldierPB.getSoldierId());
            newSoldierProp.setNum(soldierPB.getNum());
        }
        getFormationProp().putFormationMapV(localFormationAttrData);
        LOGGER.info("add new formation.formation:{}", localFormationAttrData.getFormationId());
    }


    /**
     * 解散旧的编队
     */
    public void dismissFormation(int formationId) {
        FormationProp formationProp = getFormationProp().getFormationMap().remove(formationId);
        if (formationProp == null) {
            throw new GeminiException(ErrorCode.FORMATION_ID_NULL);
        }
        LOGGER.info("remove formation. formation:{}", formationProp.getFormationId());
    }


    /**
     * 更改一个编队的名字
     *
     * @param formationId   编队Id
     * @param formationName 编队名字
     */
    public void changeFormationName(int formationId, String formationName) {
        FormationProp formationAttrData = getFormationProp().getFormationMap().get(formationId);
        if (formationAttrData == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        GeminiException geminiException = formationNameCheck(formationName);
        if (geminiException != null) {
            throw geminiException;
        }
        formationAttrData.setName(formationName);
    }

    /**
     * 解锁队伍槽位
     */
    public void unlockRHTroopSlot(int troopId, int slotId) {
        TroopRHProp troop = getFormationProp().getTroopRHMap().get(troopId);
        if (troop == null) {
            getFormationProp().getTroopRHMap().addEmptyValue(troopId);
        }
        troop = getFormationProp().getTroopRHMap().get(troopId);
        if (troop.getTroop().containsKey(slotId)) {
            return;
        }
        FormationRHProp prop = new FormationRHProp();
        prop.setSlotId(slotId).setHeroId(0);
        troop.getTroop().put(slotId, prop);
    }


    /**
     * 虚拟编队
     */
    public void editVirtualFormation(Map<Integer, Integer> map) {
        if (map == null || map.isEmpty()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        //合法性检测
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            Integer positionKey = entry.getKey();
            Integer heroId = entry.getValue();
            Struct.Hero hero = getOwner().getHeroComponent().getHero(heroId);
            if (hero == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "hero is missing");
            }
            HeroRhTemplate heroTemplate = ResHolder.getTemplate(HeroRhTemplate.class, heroId);
            if (heroTemplate == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "heroId {} is not existed", heroId);
            }
            if (positionKey < 0 || positionKey >= GameLogicConstants.FORMATION_MAX_SLOT) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "position {} is invalid", positionKey);
            }
        }

        playerFormationProp.clearPositionMap();
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            Integer positionKey = entry.getKey();
            Integer heroId = entry.getValue();
            VirtualFormationProp virtualFormationProp = playerFormationProp.addEmptyPositionMap(positionKey);
            virtualFormationProp.setPosition(positionKey);
            virtualFormationProp.setHeroId(heroId);
        }
    }


    /**
     * 编队
     */
    public void editRHTroop(int troopId, int slotId, int heroId) {
        TroopRHProp troop = getFormationProp().getTroopRHMap().get(troopId);
        if (troop == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "troop id is invalid");
        }

        int num = 0;
        int unitId = 0;
        if (heroId > 0) {
            //检测英雄是否解锁
            Struct.Hero hero = getOwner().getHeroComponent().getHero(heroId);
            if (hero == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "hero is missing");
            }
            //检测槽位是否解锁
            IntPairType buildPair = ResHolder.getResService(TroopResService.class).getTroopSlotRelateBuilding(troopId, slotId);
            if (buildPair == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "building for troop is missing");
            }
            //建筑槽位未解锁
            if (getOwner().getInnerBuildRhComponent().getInnerBuildLevel(buildPair.getKey()) < buildPair.getValue()) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "troop slot is locked");
            }
            //检测是否重复上阵
            for (TroopRHProp troopProp : getOwner().getProp().getFormation().getTroopRHMap().values()) {
                for (FormationRHProp formation : troopProp.getTroop().values()) {
                    if (formation.getSlotId() == slotId && troopProp.getTroopId() == troopId) {
                        continue;
                    }
                    if (formation.getHeroId() == heroId) {
                        throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "hero is used");
                    }
                }
            }

            HeroRhTemplate heroTemplate = ResHolder.getTemplate(HeroRhTemplate.class, heroId);
            if (heroTemplate == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "hero level template is missing");
            }
            //自动设置兵种
            HeroLevelRhTemplate lvTemplate = ResHolder.getTemplate(HeroLevelRhTemplate.class, hero.getLevel());
            if (lvTemplate == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "hero level template is missing");
            }
            //获取士兵的Cost
            UnitRhTemplate unitTemplate = ResHolder.getTemplate(UnitRhTemplate.class, heroTemplate.getRelatedUnit());
            if (unitTemplate == null) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unit template is missing");
            }
            unitId = heroTemplate.getRelatedUnit();
            HeroTemplateService heroTemplateService = ResHolder.getResService(HeroTemplateService.class);
            HeroLevelRhTemplate heroLevelTemplate = heroTemplateService.getHeroLevelTemplate(heroId, hero.getLevel());
            num = heroLevelTemplate.getRtsTroopCapacity();
        }

        FormationRHProp prop = troop.addEmptyTroop(slotId);
        prop.setSlotId(slotId).setHeroId(heroId);
        SoldierProp sProp = prop.getSoldier();
        sProp.setSoldierId(unitId).setNum(num);

        //触发任务
        new TroopHeroChangeEvent(getOwner(), troopId, troop.getTroopSize()).dispatch();
    }


    /**
     * 编队名检查（策划：无需重复命名的检测）
     *
     * @param formationName 编队名
     */
    private GeminiException formationNameCheck(String formationName) {
        // 长度检测
        ConstTemplate template = ResHolder.getResService(ConstKVResService.class).getTemplate();
        if (!StringUtils.checkLength(formationName, template.getFormationNameLengthLimit(), template.getFormationNumMinLimit())) {
            return new GeminiException(ErrorCode.FORMATION_FORMATION_NAME_LENGTH_LIMIT);
        }
        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(formationName, CommonEnum.UgcSceneId.USI_FORMATION_NAME);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }
        return null;
    }

    public void forcedDefeatArmy(long armyId) {
        long now = SystemClock.now();
        // check cd
        if (getFormationProp().getForcedDefeatArmyTsMs() > now) {
            throw new GeminiException(ErrorCode.FORCED_DEFEAT_ARMY_FAILED);
        }
        long deltaTs = TimeUtils.second2Ms(ResHolder.getConsts(ConstTemplate.class).getTroopDefeatedCD());
        getFormationProp().setForcedDefeatArmyTsMs(now + deltaTs);
        SsSceneCityArmy.ForcedDefeatArmyAsk.Builder builder = SsSceneCityArmy.ForcedDefeatArmyAsk.newBuilder();
        builder.setPlayerId(getEntityId()).setArmyId(armyId);
        try {
            ownerActor().callCurScene(builder.build());
        } catch (Exception e) {
            // 回滚时间戳
            getFormationProp().setForcedDefeatArmyTsMs(0);
            throw e;
        }
    }
}

package com.yorha.cnc.player.statistic;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.game.gen.prop.SingleStatisticProp;

/**
 * 科技统计handler
 *
 * <AUTHOR>
 */
public class SingleStatisticHandler extends AbstractStatisticHandler {

    public SingleStatisticHandler(PlayerEntity entity, StatisticEnum type) {
        super(entity, type);
    }

    public void update(int num) {
        recordCalc(getProp(), num);
    }

    public long getValue() {
        return getProp().getValue();
    }


    SingleStatisticProp getProp() {
        return getOwner().getProp().getStatisticModel().getSingleStatisticMap().computeIfAbsent(getStatisticEnum().ordinal(), (key) -> new SingleStatisticProp().setId(key));
    }


    @Override
    public void resetValue() {
        getProp().setValue(0);
    }

}

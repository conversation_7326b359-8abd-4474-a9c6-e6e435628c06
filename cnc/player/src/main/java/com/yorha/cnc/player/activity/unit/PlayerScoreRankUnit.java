package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.Sets;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.component.PlayerPointsComponent;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ActivityScoreRankUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityScoreRankTemplate;
import res.template.ActivityTemplate;

import java.util.Set;
import java.util.concurrent.TimeUnit;

public class PlayerScoreRankUnit extends BasePlayerActivityUnit implements PlayerPointsComponent.Listener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerScoreRankUnit.class);

    ActorTimer updateTimer = null;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_SCORE_RANK, (owner, prop, template) ->
                new PlayerScoreRankUnit(owner, prop.getScoreRankUnit())
        );
    }

    public PlayerScoreRankUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
        ownerActivity.getPlayer().getActivityComponent().watchScore(this);
        LOGGER.info("PlayerRankSnapshot rankId={} score={}",
                ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId()).getScoreRankId(),
                unitProp.getScoreRankUnit().getScore());
    }

    @Override
    public void onMigrate() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
        if (updateTimer != null) {
            updateTimer.cancel();
            updateTimer = null;
        }
    }

    @Override
    public void onExpire() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public void forceOffImpl() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public Set<Integer> focusPointsIds() {
        ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId());
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, activityTemplate.getScoreRankId());
        return Sets.newHashSet(scoreRankTemplate.getScoreIdsList());
    }

    @Override
    public void addPoints(int pointsTemplateId, long addPoints) {
        if (addPoints > 0) {
            ActivityScoreRankUnitProp p = unitProp.getScoreRankUnit();
            p.setScore(p.getScore() + addPoints);
            updateScoreRank();
            LOGGER.info("PlayerScoreRankUnit addPoints activityId={} templateId={} addPoints={} newPoints={}", ownerActivity.getActivityId(), pointsTemplateId, addPoints, p.getScore());
        }
    }

    protected void updateScoreRank() {
        if (updateTimer == null) {
            updateTimer = player().ownerActor().addTimer(
                    player().getPlayerId() + "-" + ownerActivity.getActivityId() + "-" + getUnitId(), TimerReasonType.PLAYER_SCORE_RANK_UPDATE, this::updateImpl,
                    RandomUtils.nextInt(3, 5),
                    TimeUnit.SECONDS
            );
        }
    }

    protected void updateImpl() {
        updateTimer = null;
        long score = unitProp.getScoreRankUnit().getScore();
        ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, ownerActivity.getActivityId());
        final int scoreRankConfId = activityTemplate.getScoreRankId();
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, scoreRankConfId);
        final int rankId = scoreRankTemplate.getRankId();
        if (rankId > 0) {
            player().getPlayerRankComponent().updateZoneRanking(rankId, score);
        }
    }
}

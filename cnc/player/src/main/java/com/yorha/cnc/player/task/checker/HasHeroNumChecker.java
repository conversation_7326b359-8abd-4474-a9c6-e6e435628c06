package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.HeroRhTemplate;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 拥有X级X品X星的英雄X个
 * param1: 英雄等级
 * param2: 等级品级
 * param3: 等级星级
 * param4: 等级个数
 *
 * <AUTHOR>
 */
public class HasHeroNumChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerHeroStarChangeEvent.class.getSimpleName(),
            PlayerHeroLevelChangeEvent.class.getSimpleName(),
            PlayerHeroUnLockEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer param1 = taskParams.get(0);
        Integer param2 = taskParams.get(1);
        Integer param3 = taskParams.get(2);
        Integer param4 = taskParams.get(3);
        int count = 0;
        for (PlayerHeroProp hero : event.getPlayer().getHeroComponent().getAllHero()) {
            HeroRhTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, hero.getHeroId());
            if (hero.getLevel() >= param1
                    && valueFromMap.getRarity() >= param2
                    && hero.getStar() >= param3) {
                count++;
            }
        }
        if (count != prop.getProcess()) {
            prop.setProcess(Math.min(param4, count));
        }
        return prop.getProcess() >= param4;
    }
}

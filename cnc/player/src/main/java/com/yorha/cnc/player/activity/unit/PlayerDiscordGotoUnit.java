package com.yorha.cnc.player.activity.unit;

import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ActivityDiscordGotoTemplate;

/**
 * discord社区跳转活动，直接领奖
 */
public class PlayerDiscordGotoUnit extends BasePlayerActivityUnit {

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_DISCORD_GOTO_EVENT, (owner, prop, template) ->
                new PlayerDiscordGotoUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerDiscordGotoUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    @Override
    public void load(boolean isInitial) {

    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        if (unitProp.getDiscordGotoUnit().getRewardTaken()) {
            throw new GeminiException("reward already taken.");
        }

        ActivityDiscordGotoTemplate gotoTemplate = ResHolder.getTemplate(ActivityDiscordGotoTemplate.class, ownerActivity.getActivityId());

        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(gotoTemplate.getMailId());
        params.getSenderBuilder().setSenderId(0);

        unitProp.getDiscordGotoUnit().setRewardTaken(true);

        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(player().getPlayerId())
                .setZoneId(player().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, params.build());
    }
}

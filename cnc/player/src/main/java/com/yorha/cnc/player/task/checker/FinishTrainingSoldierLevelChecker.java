package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerFinishTrainingSoldierEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.FINISH_TRAIN_SOLDIERID_NUM;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_CREATE;
import static com.yorha.proto.CommonEnum.TaskCalcType.TCT_RECEIVE;

/**
 * 完成训练x等级兵种x个
 * param1:等级
 * param2:数量
 *
 * <AUTHOR>
 */
public class FinishTrainingSoldierLevelChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerFinishTrainingSoldierEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int soldierLevelConfig = taskParams.get(0);
        int countConfig = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() == TCT_CREATE) {
            int trainTotal = 0;
            List<Integer> soldierIdList = ResHolder.getResService(SoldierResService.class).getIdListBySoldierLevel(soldierLevelConfig);
            for (Integer soldierId : soldierIdList) {
                trainTotal += (int) event.getPlayer().getStatisticComponent().getSecondStatistic(FINISH_TRAIN_SOLDIERID_NUM, soldierId);
            }
            prop.setProcess(Math.min(trainTotal, countConfig));
        } else if (taskTemplate.getTaskCalculationMethod() == TCT_RECEIVE) {
            if (event instanceof PlayerFinishTrainingSoldierEvent) {
                PlayerFinishTrainingSoldierEvent finishTrainingSoldierEvent = (PlayerFinishTrainingSoldierEvent) event;
                int soldierId = finishTrainingSoldierEvent.getSoldierId();
                int soldierLevel = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId).getSoldierLevel();
                if (soldierLevelConfig == soldierLevel) {
                    prop.setProcess(Math.min(prop.getProcess() + finishTrainingSoldierEvent.getCount(), countConfig));
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }
}

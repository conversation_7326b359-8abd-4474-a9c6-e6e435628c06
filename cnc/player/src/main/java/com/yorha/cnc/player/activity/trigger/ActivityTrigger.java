package com.yorha.cnc.player.activity.trigger;

import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.game.gen.prop.TriggerInfoProp;

import java.util.List;

/**
 * 活动触发器接口
 *
 * <AUTHOR>
 */
public interface ActivityTrigger {
    /**
     * 关注的事件列表
     *
     * @return 关注的事件
     */
    List<Class<? extends PlayerEvent>> getAttentionEvent();

    /**
     * 触发回调
     *
     * @param event     任务事件
     * @param triggerId 触发器id
     * @param triggerInfoProp 触发器相关落库属性
     * @return true: 满足触发条件
     */
    boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp);
}

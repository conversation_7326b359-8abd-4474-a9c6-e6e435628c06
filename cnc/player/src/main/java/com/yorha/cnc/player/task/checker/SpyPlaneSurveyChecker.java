package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.SpyPlaneSurveyFinishedEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.SPY_PLANE_SURVEY_TOTAL;

/**
 * 侦察机成功调查一次
 *
 * <AUTHOR>
 */
public class SpyPlaneSurveyChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            SpyPlaneSurveyFinishedEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName()
    );

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int config = 1;
        int spyPlaneSurveyTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(SPY_PLANE_SURVEY_TOTAL);
        prop.setProcess(Math.min(config, spyPlaneSurveyTotal));

        return prop.getProcess() >= config;
    }
}

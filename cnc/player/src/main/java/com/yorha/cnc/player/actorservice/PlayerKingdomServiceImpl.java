package com.yorha.cnc.player.actorservice;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.common.actor.PlayerKingdomService;
import com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd;
import com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 玩家的王国服务
 *
 * <AUTHOR>
 */
public class PlayerKingdomServiceImpl implements PlayerKingdomService {
    private static final Logger LOGGER = LogManager.getLogger(PlayerKingdomServiceImpl.class);
    private final PlayerActor playerActor;

    public PlayerKingdomServiceImpl(PlayerActor playerActor) {
        this.playerActor = playerActor;
    }

    @Override
    public void handleKingdomOfficeChangeCmd(KingdomOfficeChangeCmd ask) {
        if (!ask.hasOldOfficeId() || !ask.hasNewOfficeId()) {
            LOGGER.warn("handleKingdomOfficeChangeCmd: invalid ask, ask={}", ask);
            return;
        }
        playerActor.getOrLoadEntity().getKingdomComponent().applyKingdomOfficeChange(
                ask.getOldOfficeId(), ask.getNewOfficeId(),
                ask.getKingNum(),
                ask.hasClanSimpleName() ? ask.getClanSimpleName() : null);
    }

    @Override
    public void handleKingdomSettleGainTaxCmd(KingdomSettleGainTaxCmd ask) {
        playerActor.getOrLoadEntity().getKingdomComponent().settleGainTaxResource(ask.getResourceMap());
    }
}

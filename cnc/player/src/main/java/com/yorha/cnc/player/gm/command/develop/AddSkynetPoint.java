package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 添加天网情报点
 *
 * <AUTHOR>
 */
public class AddSkynetPoint implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int value = Integer.parseInt(args.get("value"));
        actor.getOrLoadEntity().getSkynetComponent().addPointByGm(value);
    }

    @Override
    public String showHelp() {
        return "AddSkynetPoint value=99999";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

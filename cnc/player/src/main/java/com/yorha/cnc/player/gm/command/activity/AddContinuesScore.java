package com.yorha.cnc.player.gm.command.activity;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.unit.PlayerContinuesUnit;
import com.yorha.cnc.player.component.PlayerActivityComponent;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 增加连续活动积分
 *
 * <AUTHOR>
 */
public class AddContinuesScore implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        final int activityId = Integer.parseInt(args.get("activityId"));
        final int score = Integer.parseInt(args.get("score"));
        final PlayerActivityComponent activityComponent = actor.getEntity().getActivityComponent();
        BasePlayerActivityUnit unit = activityComponent.findActiveUnit(activityId, 1);
        if (unit instanceof PlayerContinuesUnit) {
            ((PlayerContinuesUnit) unit).gmAddScore(score);
        }
    }

    @Override
    public String showHelp() {
        return "AddContinuesScore activityId={value} score={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

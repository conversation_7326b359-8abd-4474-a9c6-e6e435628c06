package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.cache.AutoLoadSyncCache;
import com.yorha.common.cache.CacheMgr;
import com.yorha.common.cache.action.CacheAddAction;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.*;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.enums.CacheUsageEnum;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.mail.MailActionType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.helper.CommonIdIpHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.mail.MailIdType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.constant.ConstSpyService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.mail.MailTemplateService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import qlog.flow.QlogIdipReq;
import res.template.ConstCollectTemplate;
import res.template.ConstTemplate;
import res.template.MailExpireTemplate;
import res.template.MailTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class PlayerMailComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerMailComponent.class);
    /**
     * 一轮批量删除最多邮件数
     */
    private static final int MAX_DELETE_NUM = 10;
    /**
     * 全服战报缓存
     * 200条
     * LFU 1s过期
     */
    private static final AutoLoadSyncCache<PlayerMail.Player_ReadBattleLog_C2S, PlayerMail.Player_ReadBattleLog_S2C> BATTLE_LOG_CACHE = CacheMgr.buildAutoLoadCache(100, 0,
            new CacheAddAction<PlayerMail.Player_ReadBattleLog_C2S, PlayerMail.Player_ReadBattleLog_S2C>() {
                @Override
                public PlayerMail.Player_ReadBattleLog_S2C execute(PlayerMail.Player_ReadBattleLog_C2S msg, GameActorWithCall actor) {
                    return loadBattleLog(actor, msg.getPageId(), msg.getLogId());
                }

                private PlayerMail.Player_ReadBattleLog_S2C loadBattleLog(GameActorWithCall actor, int pageId, long logId) {
                    LOGGER.info("PlayerMailComponent BATTLE_LOG_CACHE loadBattleLog, pageId={}, logId={}", pageId, logId);
                    if (logId <= 0) {
                        throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                    }
                    if (pageId <= 0) {
                        throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                    }

                    PlayerMail.Player_ReadBattleLog_S2C.Builder builder = PlayerMail.Player_ReadBattleLog_S2C.newBuilder();

                    if (pageId == 1) {
                        // 请求第一个页，也要把日志概要带上
                        TcaplusDb.BattleLogTable.Builder req = TcaplusDb.BattleLogTable.newBuilder().setLogId(logId);
                        GetResult<TcaplusDb.BattleLogTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
                        if (DbUtil.isRecordNotExist(ans.getCode())) {
                            throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                        }
                        if (!DbUtil.isOk(ans.getCode())) {
                            LOGGER.error("PlayerMailComponent BATTLE_LOG_CACHE loadBattleLog, BattleLogTable db fail, cod={}", ans.getCode());
                            throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                        }
                        TcaplusDb.BattleLogTable.Builder log = ans.value;
                        if (log == null) {
                            LOGGER.error("PlayerMailComponent BATTLE_LOG_CACHE loadBattleLog, BattleLogTable db fail, ans=null");
                            throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                        }
                        builder.setLog(log.getLog());
                    }

                    // 请求日志页
                    TcaplusDb.BattleLogPageTable.Builder req = TcaplusDb.BattleLogPageTable.newBuilder()
                            .setLogId(logId)
                            .setPageId(pageId);
                    try {
                        GetResult<TcaplusDb.BattleLogPageTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
                        if (!DbUtil.isOk(ans.getCode())) {
                            LOGGER.error("PlayerMailComponent BATTLE_LOG_CACHE loadBattleLog, BattleLogPageTable db fail, cod={}", ans.getCode());
                            throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                        }
                        TcaplusDb.BattleLogPageTable.Builder page = ans.value;
                        if (page == null) {
                            LOGGER.error("PlayerMailComponent BATTLE_LOG_CACHE loadBattleLog, BattleLogPageTable db fail, ans=null");
                            throw new GeminiException(ErrorCode.MAIL_BATTLE_LOG_NOT_FOUND);
                        }
                        builder.setLogPage(page.getLogPage());
                    } catch (Exception e) {
                        LOGGER.error("PlayerMailComponent BATTLE_LOG_CACHE loadBattleLog, exception=", e);
                        throw e;
                    }

                    return builder.build();
                }
            }, CacheUsageEnum.BATTLE_LOG_CACHE);
    /**
     * 全服战报详情缓存
     * 200条
     * LFU 1s过期
     */
    private static final AutoLoadSyncCache<PlayerMail.Player_ReadBattleRecordDetail_C2S, PlayerMail.Player_ReadBattleRecordDetail_S2C> BATTLE_RECORD_DETAIL_CACHE = CacheMgr.buildAutoLoadCache(100, 0,
            new CacheAddAction<PlayerMail.Player_ReadBattleRecordDetail_C2S, PlayerMail.Player_ReadBattleRecordDetail_S2C>() {
                @Override
                public PlayerMail.Player_ReadBattleRecordDetail_S2C execute(PlayerMail.Player_ReadBattleRecordDetail_C2S msg, GameActorWithCall actor) {
                    return loadBattleRecordDetail(actor, msg);
                }

                private PlayerMail.Player_ReadBattleRecordDetail_S2C loadBattleRecordDetail(GameActorWithCall actor, PlayerMail.Player_ReadBattleRecordDetail_C2S msg) {
                    LOGGER.info("PlayerMailComponent BATTLE_RECORD_DETAIL_CACHE loadBattleRecordDetail, cs msg={}", msg);
                    PlayerMail.Player_ReadBattleRecordDetail_S2C.Builder builder = PlayerMail.Player_ReadBattleRecordDetail_S2C.newBuilder()
                            .setRecordId(msg.getRecordId())
                            .setBattleId(msg.getBattleId())
                            .setOpType(msg.getOpType());

                    if (msg.getRecordId() <= 0) {
                        throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
                    }
                    if (msg.getBattleId() <= 0) {
                        throw new GeminiException(ErrorCode.SYSTEM_INVALID_PARAM);
                    }

                    // 请求详情
                    TcaplusDb.BattleRecordDetailTable.Builder req = TcaplusDb.BattleRecordDetailTable.newBuilder()
                            .setRecordId(msg.getRecordId())
                            .setBattleId(msg.getBattleId());
                    try {
                        GetResult<TcaplusDb.BattleRecordDetailTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
                        if (!DbUtil.isOk(ans.getCode())) {
                            if (DbUtil.isRecordNotExist(ans.getCode())) {
                                LOGGER.warn("PlayerMailComponent BATTLE_RECORD_DETAIL_CACHE loadBattleRecordDetail, BattleRecordDetailTable db not exists, code={}", ans.getCode());
                            } else {
                                LOGGER.error("PlayerMailComponent BATTLE_RECORD_DETAIL_CACHE loadBattleRecordDetail, BattleRecordDetailTable db fail, code={}", ans.getCode());
                            }
                            return builder.build();
                        }
                        TcaplusDb.BattleRecordDetailTable.Builder detail = ans.value;
                        if (detail != null) {
                            PlayerMail.BattleRecordDetail pbFromDb = PlayerMail.BattleRecordDetail.parseFrom(detail.getDetail());
                            PlayerMail.BattleRecordDetail.Builder detailBuilder = PlayerMail.BattleRecordDetail.newBuilder();
                            switch (msg.getOpType()) {
                                case RMDOT_ADDITION: {
                                    if (pbFromDb.hasMyAddition() && pbFromDb.hasEnemyAddition()) {
                                        detailBuilder
                                                .setMyAddition(pbFromDb.getMyAddition())
                                                .setEnemyAddition(pbFromDb.getEnemyAddition());
                                    }
                                    break;
                                }
                                case RMDOT_MY_MEMBERS: {
                                    detailBuilder.addAllMyMembers(pbFromDb.getMyMembersList());
                                    break;
                                }
                                case RMDOT_ENEMY_MEMBERS: {
                                    detailBuilder.addAllEnemyMembers(pbFromDb.getEnemyMembersList());
                                    break;
                                }
                                default: {
                                    break;
                                }
                            }
                            builder.setDetail(detailBuilder);
                        } else {
                            LOGGER.error("PlayerMailComponent BATTLE_RECORD_DETAIL_CACHE loadBattleRecordDetail, BattleRecordDetailTable db fail, detail is null");
                            return builder.build();
                        }
                    } catch (InvalidProtocolBufferException e) {
                        LOGGER.error("PlayerMailComponent BATTLE_RECORD_DETAIL_CACHE loadBattleRecordDetail, exception=", e);
                        return builder.build();
                    } catch (Exception e) {
                        LOGGER.error("PlayerMailComponent BATTLE_RECORD_DETAIL_CACHE loadBattleRecordDetail, exception=", e);
                        throw e;
                    }
                    return builder.build();
                }
            }, CacheUsageEnum.BATTLE_RECORD_DETAIL_CACHE);

    static {
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, PlayerMailComponent::onMainCityUpgradeEvent);
    }

    private final MailStoreHelper mailStoreHelper = new MailStoreHelper();
    private final Map<CommonEnum.MailTabsType, List<MailMinInfo>> mailsInfoMap = Maps.newHashMap();
    private final Map<Long, MailMinInfo> playerMails = Maps.newHashMap();
    private ActorTimer deleteMailsTimer = null;

    public PlayerMailComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * 主城升级事件监听
     *
     * @param event 事件
     */
    private static void onMainCityUpgradeEvent(MainCityUpgradeStaticEvent event) {

    }

    @Override
    public void onLogin() {
        this.playerMails.clear();
        this.mailsInfoMap.clear();
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns ans) {
        GeminiStopWatch watch = new GeminiStopWatch("start mail header push");

        List<StructMailPB.PlayerMailPB> allMailHeaders = this.loadAllHeaderFromDb();
        List<PlayerMailProp> newMails = this.syncOfflineMails();
        this.handleExpiredMail();
        this.tryAddDeleteMailsTimer();
        this.notifyClientMailHeaders(true, allMailHeaders.stream().filter((mail) -> this.playerMails.containsKey(mail.getMailId())).collect(Collectors.toList()));
        this.notifyReceiveNewMails(newMails.stream().filter((mail) -> this.playerMails.containsKey(mail.getMailId())).collect(Collectors.toList()));

        this.mailStoreHelper.init(this.playerMails);
        watch.mark("push all mail header");
        LOGGER.info("PlayerMailComponent postLogin mail_push_headers_over_cost_total totalCost={}", watch.getTotalCost());
    }

    /**
     * 校验是否被禁止私聊邮件
     */
    public void checkBanPrivateMail() throws GeminiException {
        // josefren: IDIP后来补的禁言私聊邮件，不像聊天禁言有前端拦截
        if (this.ownerActor().getOrLoadChatPlayerEntity().getHandleChatComponent().isBanned()) {
            LOGGER.warn("PlayerMailComponent checkBanPrivateMail is banned");
            // 复用聊天错误码
            throw new GeminiException(ErrorCode.IN_BAN_CHAT_STATE);
        }
    }


    /**
     * 添加删除邮件timer
     */
    public void tryAddDeleteMailsTimer() {
        // 定时器已存在
        if (deleteMailsTimer != null) {
            return;
        }
        // 没需要删除的邮件
        if (!this.hasDeleteMails()) {
            return;
        }
        deleteMailsTimer = this.ownerActor().addRepeatTimer(TimerReasonType.DELETE_MAIL, () -> {
            if (!this.hasDeleteMails()) {
                this.removeDeleteMailsTimer();
                return;
            }
            this.deleteMails();
        }, 1, 1, TimeUnit.SECONDS);
    }

    /**
     * 删除待删除邮件
     */
    private void deleteMails() {
        int loopSize = Math.min(this.getOwner().getProp().getMailModel().getDeleteMailsSize(), MAX_DELETE_NUM);
        final Int64SetProp mailsToDelete = this.getOwner().getProp().getMailModel().getDeleteMails();
        for (final Long mailId : mailsToDelete.getValues()) {
            if (loopSize <= 0) {
                break;
            }
            loopSize--;
            this.deleteMail(mailId);
        }
    }

    /**
     * 删除指定邮件
     *
     * @param mailId 邮件id
     */
    private void deleteMail(final Long mailId) {
        TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
        req.setPlayerId(this.getPlayerId()).setMailId(mailId);
        this.ownerActor().askGameDb(new DeleteAsk<>(req)).onComplete((result, throwable) -> {
            if (throwable != null) {
                LOGGER.error("PlayerMailComponent deleteMail mailId={}, e=", mailId, throwable);
                return;
            }
            if (!(result.isOk() || result.isRecordNotExist())) {
                LOGGER.error("PlayerMailComponent deleteMail mailId={} fail, errorCode={}", mailId, result.getCode());
                return;
            }
            LOGGER.info("PlayerMailComponent deleteMail, mailId={} deleted", mailId);
            this.getOwner().getProp().getMailModel().getDeleteMails().remove(mailId);
            this.getOwner().getProp().getMailModel().getReadedMails().remove(mailId);
        });
    }

    /**
     * 玩家是否有待删除邮件
     *
     * @return
     */
    private boolean hasDeleteMails() {
        return !this.getOwner().getProp().getMailModel().isDeleteMailsEmpty();
    }

    /**
     * 移除定时删除timer
     */
    private void removeDeleteMailsTimer() {
        LOGGER.info("removeDeleteMailsTimer");
        if (deleteMailsTimer == null) {
            return;
        }
        deleteMailsTimer.cancel();
        deleteMailsTimer = null;
    }

    @Override
    public void afterLogout() {
        this.removeDeleteMailsTimer();
    }

    /**
     * 从DB读取玩家所有Header
     */
    private List<StructMailPB.PlayerMailPB> loadAllHeaderFromDb() {
        TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
        req.setPlayerId(getPlayerId());

        GetByPartKeyResult<TcaplusDb.PlayerMailTable.Builder> res;
        try {
            res = this.getOwner().ownerActor().callGameDb(new SelectAsk<>(req, GetByPartKeyOption.newBuilder()
                    .withGetAllFields(false)
                    .withFieldNames(Lists.newArrayList("Status", "RewardStatus", "Header", "InviteStatus"))
                    .build()
            ));
            if (DbUtil.isRecordNotExist(res.getCode())) {
                return Collections.emptyList();
            }
            if (!res.isOk()) {
                LOGGER.error("PlayerMailComponent loadAllHeaderFromDb fail, requestId={}, code={}", res.requestId, res.getCode());
                throw new GeminiException("loadAllHeaderFromDb get All Header Fail");
            }
        } catch (Exception e) {
            LOGGER.error("PlayerMailComponent loadAllHeaderFromDb fail, ", e);
            throw new GeminiException("loadAllHeaderFromDb get All Header Fail");
        }
        List<StructMailPB.PlayerMailPB> mailHeaders = new ArrayList<>(res.getValues().size());
        for (ValueWithVersion<TcaplusDb.PlayerMailTable.Builder> dbMailHeader : res.getValues()) {
            // 待删除邮件，不加载到内存中了
            if (this.getOwner().getProp().getMailModel().isDeleteMailsContains(dbMailHeader.value.getMailId())) {
                continue;
            }
            final boolean readed = this.getOwner().getProp().getMailModel().isReadedMailsContains(dbMailHeader.value.getMailId());
            if (!this.addMailMinInfo(new MailMinInfo(dbMailHeader.value, readed))) {
                continue;
            }
            mailHeaders.add(this.dbDataToMailHeader(dbMailHeader.value, readed));
        }
        for (List<MailMinInfo> mailsInfoList : this.mailsInfoMap.values()) {
            mailsInfoList.sort(Comparator.comparingLong(MailMinInfo::getCreateTsMs));
        }

        return mailHeaders;
    }

    /**
     * 删除mailMinInfo
     *
     * @param mailMinInfo 邮件内存信息
     */
    private void delMailMinInfo(final MailMinInfo mailMinInfo) {
        List<MailMinInfo> mailsUnderTab = this.getMailsUnderTab(this.getMailTabsTypeByMailInfo(mailMinInfo));
        boolean removeTabs = false;
        for (int i = 0; i < mailsUnderTab.size(); i++) {
            if (mailsUnderTab.get(i).getMailId() == mailMinInfo.getMailId()) {
                mailsUnderTab.remove(i);
                removeTabs = true;
                break;
            }
        }
        mailStoreHelper.deleteStore(mailMinInfo);
        boolean removePlayerMail = this.playerMails.remove(mailMinInfo.getMailId()) != null;
        // 日志确认是否两个都删除
        LOGGER.info("delMailMinInfo removeMailUnderTabs={}, removePlayerMail={}, mailMinInfo={}", removeTabs, removePlayerMail, mailMinInfo);
    }

    /**
     * 添加新mailMinInfo
     *
     * @param mailMinInfo 邮件内存信息
     */
    private boolean addMailMinInfo(final MailMinInfo mailMinInfo) {
        // 防重复
        if (this.playerMails.containsKey(mailMinInfo.getMailId())) {
            LOGGER.warn("PlayerMailComponent addMailMinInfo already has mailId={}", mailMinInfo.getMailId());
            return false;
        }
        final CommonEnum.MailTabsType mailTabsType = this.getMailTabsTypeByMailInfo(mailMinInfo);
        this.mailsInfoMap.computeIfAbsent(mailTabsType, (type) -> new LinkedList<>()).add(mailMinInfo);
        this.playerMails.put(mailMinInfo.getMailId(), mailMinInfo);
        return true;
    }

    public CommonEnum.MailTabsType getMailTabsTypeByMailInfo(final MailMinInfo mailMinInfo) {
        final CommonEnum.MailTabsType mailTabsType;
        // 收藏不另开页签
        if (mailMinInfo.getIsSender()) {
            mailTabsType = CommonEnum.MailTabsType.MAIL_TABS_TYPE_SEND;
        } else {
            mailTabsType = this.getMailTemplate(mailMinInfo.getMailTemplateId()).getMailTabsType();
        }
        return mailTabsType;
    }

    /**
     * 向客户端推送邮件头
     *
     * @param isDone      是否推送结束
     * @param mailHeaders 邮件头
     */
    private void notifyClientMailHeaders(boolean isDone, final List<StructMailPB.PlayerMailPB> mailHeaders) {
        PlayerMail.Player_Push_All_Mail_Header_NTF.Builder ntf = PlayerMail.Player_Push_All_Mail_Header_NTF.newBuilder().addAllMails(mailHeaders).setIsDone(isDone);
        this.getOwner().sendMsgToClient(MsgType.PLAYER_PUSHALLMAILHEADER_NTF, ntf.build());
    }

    /**
     * 转换db数据为客户端所需邮件头
     *
     * @param dbData db邮件数据
     * @return 邮件头
     */
    private StructMailPB.PlayerMailPB dbDataToMailHeader(TcaplusDb.PlayerMailTable.Builder dbData, boolean readed) {
        StructMailPB.PlayerMailPB.Builder mailHeader = this.mailHeaderToPlayerMail(dbData.getHeader());

        mailHeader.setMailId(dbData.getMailId());
        DbTransFormer.fromMailStatus(dbData.getStatus(), readed, mailHeader);
        DbTransFormer.fromMailRewardStatus(dbData.getRewardStatus(), mailHeader);
        DbTransFormer.fromMailInviteStatus(dbData.getInviteStatus(), mailHeader);
        return mailHeader.build();
    }

    /**
     * 用db中的 MailBrief构建PlayerMail
     *
     * @param brief 邮件固定摘要信息
     * @return 邮件
     */
    private StructMailPB.PlayerMailPB.Builder mailHeaderToPlayerMail(CommonMsg.MailBrief brief) {
        PlayerMailProp mailHeader = new PlayerMailProp();
        mailHeader.setMailTemplateId(brief.getMailTemplateId());
        mailHeader.getSender().mergeFromSs(brief.getSender());
        mailHeader.setCreateTimestamp(brief.getCreateTimestamp());
        mailHeader.getTitle().mergeFromSs(brief.getTitle());
        for (Struct.ItemPair itemPairDB : brief.getItemRewardList()) {
            ItemPairProp itemPair = new ItemPairProp();
            itemPair.mergeChangeFromDb(itemPairDB);
            mailHeader.getItemReward().add(itemPair);
        }
        mailHeader.setLanguage(brief.getLanguage());
        mailHeader.setMailType(brief.getMailType());
        mailHeader.setOfflineExpiredTime(brief.getOfflineExpiredTime());
        mailHeader.setCanReceive(brief.getCanReceive());
        mailHeader.getReceiverCard().mergeChangeFromSs(brief.getReceiverCard());
        mailHeader.setIsIdIpMail(brief.getIsIdIpMail());

        return mailHeader.getCopyCsBuilder();
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            this.resetClanMailIndex();
        }
    }

    /**
     * 获取mailId对应邮件的MailTabsType。
     *
     * @param mailId 邮件id
     * @return 邮件页签
     */
    public CommonEnum.MailTabsType getTemplateMailTabsType(final long mailId) {
        final MailMinInfo mail = this.getMail(mailId);
        if (mail == null) {
            throw new GeminiException("PlayerMailComponent getTemplateMailTabsType mail {} not exists", mailId);
        }
        return this.getMailTabsTypeByMailInfo(mail);
    }


    /**
     * 屏蔽指定玩家
     *
     * @param playerId      玩家id
     * @param expireTimeSec 持续时间
     */
    public void shieldPersonMail(long playerId, long expireTimeSec) {
        long curTsSec = SystemClock.nowOfSeconds();
        long expireTsSec = curTsSec + expireTimeSec;
        if (expireTsSec < curTsSec) {
            throw new GeminiException("PlayerMailComponent shieldPersonMail expireTimeSec={} useless", expireTimeSec);
        }
        this.getOwner().getProp().getMailModel().addEmptyShieldPersons(playerId).setShieldExpireTsSec(expireTsSec);
        LOGGER.info("PlayerMailComponent shieldPersonMail player {} at {}s, expire after {}s", playerId, curTsSec, expireTimeSec);
    }

    /**
     * 解除屏蔽对象
     *
     * @param playerId 玩家id
     */
    public void unShieldPersonMail(long playerId) {
        this.getOwner().getProp().getMailModel().removeShieldPersonsV(playerId);
        LOGGER.info("PlayerMailComponent unShieldPersonMail player {}", playerId);
    }

    /**
     * 收藏邮件
     *
     * @param mailId 邮件id
     */
    public void favoriteMail(final long mailId) {
        this.mailStoreHelper.addStore(mailId);
        this.sendFavoriteMailQlog(getMail(mailId));
    }

    /**
     * 批量从db加载MailContent并多语言翻译
     *
     * @param mailIds 邮件ids
     * @return Player_MailBatchGetContent_S2C
     */
    public PlayerMail.Player_MailBatchGetContent_S2C batchGetMailContent(List<Long> mailIds) {
        GeminiStopWatch watch = new GeminiStopWatch("batch get mail content");
        PlayerMail.Player_MailBatchGetContent_S2C.Builder response = PlayerMail.Player_MailBatchGetContent_S2C.newBuilder();
        if (mailIds.isEmpty()) {
            return response.build();
        }
        if (mailIds.size() > 50) {
            mailIds = mailIds.subList(0, 50);
        }
        BatchSelectAsk<TcaplusDb.PlayerMailTable.Builder> batchSelectAsk = this.getBatchSelectAsk(mailIds);
        // 所有id都无效，直接返回
        if (batchSelectAsk.getReqList().isEmpty()) {
            return response.build();
        }
        try {
            BatchGetResult<TcaplusDb.PlayerMailTable.Builder> ans = this.ownerActor().callGameDb(batchSelectAsk);
            if (!DbUtil.isOk(ans.getCode())) {
                LOGGER.error("PlayerMailComponent batchGetMailContent, mailIds={}, requestId={}, code={}", mailIds, ans.requestId, ans.getCode());
                throw new GeminiException(ErrorCode.MAIL_CONTENT_NOT_FOUND.getCodeId());
            }
            if (ans.values.isEmpty()) {
                LOGGER.error("PlayerMailComponent batchGetMailContent, data is empty, mailIds={}, requestId={}", mailIds, ans.requestId);
                throw new GeminiException(ErrorCode.MAIL_CONTENT_NOT_FOUND.getCodeId());
            }
            LOGGER.info("PlayerMailComponent batchGetMailContent, mailIds={}, requestId={}", mailIds, ans.requestId);
            for (ValueWithVersion<TcaplusDb.PlayerMailTable.Builder> dbValue : ans.values) {
                final StructMail.MailContent.Builder builder = dbValue.value.getContentBuilder();
                if (builder == null) {
                    throw new GeminiException(ErrorCode.MAIL_CONTENT_NOT_FOUND.getCodeId());
                }
                // 多语言
                LOGGER.info("PlayerMailComponent batchGetMailContent try transMultiLangTxt for mailContent mailId={}", dbValue.value.getMailId());
                transMultiLangTxt(builder.getDisplayDataBuilder().getParamsBuilder(), getOwner().getClientLanguage());
                StructMailPB.MailContentPB mailContentPB;
                try {
                    mailContentPB = StructMailPB.MailContentPB.newBuilder().mergeFrom(builder.build().toByteArray()).build();
                } catch (InvalidProtocolBufferException e) {
                    LOGGER.error("PlayerMailComponent batchGetMailContent parse mail, db data {} to pb data fail", builder, e);
                    mailContentPB = StructMailPB.MailContentPB.getDefaultInstance();
                }
                response.putMailContents(dbValue.value.getMailId(), mailContentPB);
            }

        } catch (Exception e) {
            LOGGER.error("PlayerMailComponent batchGetMailContent fail,", e);
            throw e;
        }
        response.setValidContentsNum(response.getMailContentsCount());
        watch.mark("batch get mail content end");
        LOGGER.info("PlayerMailComponent batchGetMailContent mail_batch_get_content_over_cost_total totalCost={}", watch.getTotalCost());
        return response.build();
    }

    /**
     * 根据mailIds构建Tcaplus批量拉取请求
     *
     * @param mailIds 邮件ids
     * @return BatchSelectAsk<TcaplusDb.PlayerMailTable.Builder>
     */
    private BatchSelectAsk<TcaplusDb.PlayerMailTable.Builder> getBatchSelectAsk(List<Long> mailIds) {
        BatchGetOption.Builder batchOption = BatchGetOption.newBuilder().withGetAllFields(false).withFieldNames(Lists.newArrayList("Content"));
        List<TcaplusDb.PlayerMailTable.Builder> listReq = new ArrayList<>();
        for (Long mailId : mailIds) {
            // 避免客户端传错id
            if (!playerMails.containsKey(mailId)) {
                LOGGER.warn("PlayerMailComponent getBatchSelectAsk mailId={}, no content in db", mailId);
                continue;
            }
            TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
            req.setPlayerId(this.getPlayerId()).setMailId(mailId);
            listReq.add(req);
        }
        return new BatchSelectAsk<>(listReq, batchOption.build());
    }

    /**
     * 被动删除邮件（邮件数达到页签上限）
     *
     * @param mail 邮件
     */
    private void onPassiveMailDel(final MailMinInfo mail) {
        if (mail.getCanReceive() && mail.getRewardState() == CommonEnum.MailRewardState.MAIL_REWARD_STATE_UNTAKEN) {
            this.onMailRewardGet(mail);
        }
        this.markDeleteMail(mail);
        LOGGER.info("PlayerMailComponent onPassiveMail delete mailId={}", mail.getMailId());
    }

    /**
     * 收到邮件的回调
     *
     * @param newMail    收到的新邮件
     * @param isIdIpMail 是否是idIp邮件
     * @return 新邮件。
     */
    @Nullable
    public PlayerMailProp onMailAdd(StructMail.NewMailCache newMail, boolean isIdIpMail) {
        final StructMail.PlayerMail mailPB = toPlayerMailHeader(newMail);
        // 设置游标
        this.updateMailReadIndex(newMail);

        // 添加邮件
        final PlayerMailProp mail = new PlayerMailProp();
        mail.mergeFromSs(mailPB);
        mail.setIsIdIpMail(isIdIpMail);

        MailMinInfo mailMinInfo = this.dbInsertMail(mail, newMail.getContent());
        // 邮件添加失败
        if (mailMinInfo == null) {
            return null;
        }
        if (mail.getCanReceive()) {
            LOGGER.debug("PlayerMailComponent onMailAdd receive new mail=\n{}", mail);
            // qLog
            this.sendAddMailQlog(mailMinInfo);
        } else {
            LOGGER.debug("PlayerMailComponent onMailAdd receive offline expire new mail=\n{}", mail);
        }
        // 添加邮件
        CommonEnum.MailTabsType mailTabsType = this.getMailTabsTypeByMailInfo(mailMinInfo);
        for (final MailMinInfo removeMail : this.getOverLoadMails(mailTabsType)) {
            // 通知邮件删除
            this.notifyClientMailDelete(removeMail.getMailId(), mailTabsType);
            this.onPassiveMailDel(removeMail);
            this.sendPassiveDeleteQlog(removeMail, MailActionType.PASSIVE_DELETE_OVERLIMIT);
        }
        // 邮件仅作奖励展示 且 奖励需通过邮件入背包
        if (mail.getRewardState() == CommonEnum.MailRewardState.MAIL_REWARD_STATE_ONLY_SHOW && newMail.getAddItemByMail().getAddItemWhenOnlyForShow()) {
            this.addOnlyForShowItem(mail, newMail.getAddItemByMail());
        }
        return mail;
    }

    /**
     * 将仅展示的奖励发给玩家
     *
     * @param mail          邮件
     * @param addItemByMail 发奖参数
     */
    private void addOnlyForShowItem(PlayerMailProp mail, StructMail.AddItemByMail addItemByMail) {
        LOGGER.info("PlayerMailComponent addOnlyForShowItem mailId={} addItemByMail={}", mail.getMailId(), addItemByMail);
        CommonEnum.Reason reason = addItemByMail.getAddItemReason();
        String subReason = addItemByMail.getSubReason();
        // qlog理由没填就告警
        if (reason == CommonEnum.Reason.ICR_NONE || !addItemByMail.hasAddItemReason()) {
            WechatLog.error("PlayerMailComponent addOnlyForShowItem mailId={}, invalid qlog reason", mail.getMailId());
        }

        AssetPackage.Builder addItemBuilder = AssetPackage.builder();
        // 动态奖励
        for (ItemPairProp itemPair : mail.getItemReward()) {
            addItemBuilder.plusItem(itemPair.getItemTemplateId(), itemPair.getCount());
        }
        // 静态奖励
        addItemBuilder.plusItemList(ResHolder.getTemplate(MailTemplate.class, mail.getMailTemplateId()).getAwardPairList());

        this.getOwner().getAssetComponent().give(addItemBuilder.build(), reason, subReason);
    }

    /**
     * 处理离线时过期的个人邮件
     */
    private void handleExpiredMail() {
        final long curNowTs = SystemClock.now();
        // 1. 设置离线过期日志为可接受状态
        for (CommonEnum.MailTabsType tabsType : CommonEnum.MailTabsType.values()) {
            for (final MailMinInfo mail : this.getMailsUnderTab(tabsType)) {
                // 1. 邮件可接受
                if (mail.getCanReceive()) {
                    continue;
                }
                // 2. 已经过期
                if (mail.getOfflineExpireTsMs() != 0 && mail.getOfflineExpireTsMs() <= curNowTs) {
                    continue;
                }
                mail.setCanReceive(true);
                this.sendAddMailQlog(mail);
            }
        }

        for (CommonEnum.MailTabsType mailTabsType : this.mailsInfoMap.keySet()) {
            final MailExpireTemplate mailExpireTemplate = this.getMailExpireTemplate(mailTabsType);
            final long expireTimeMs = mailExpireTemplate != null ? TimeUnit.DAYS.toMillis(mailExpireTemplate.getExpire()) : TimeUnit.DAYS.toMillis(30);

            List<MailMinInfo> toDeleteMails = Lists.newLinkedList();

            // 2. 删除过期邮件
            for (final MailMinInfo mailUnderTabsType : this.getMailsUnderTab(mailTabsType)) {
                // 收藏的邮件
                if (mailUnderTabsType.getIsStored()) {
                    continue;
                }
                final long expireTsMs = mailUnderTabsType.getCreateTsMs() + expireTimeMs;
                // 尚未接收的邮件 or 已过期邮件
                if (!mailUnderTabsType.getCanReceive() || expireTsMs < curNowTs) {
                    toDeleteMails.add(mailUnderTabsType);
                }
            }

            for (final MailMinInfo toDeleteMail : toDeleteMails) {
                this.onPassiveMailDel(toDeleteMail);
                this.sendPassiveDeleteQlog(toDeleteMail, MailActionType.PASSIVE_DELETE_OVERTIME);
            }

            // 3. 调整邮件容量，删除过量邮件。
            for (final MailMinInfo overLoadMail : this.getOverLoadMails(mailTabsType)) {
                this.onPassiveMailDel(overLoadMail);
                this.sendPassiveDeleteQlog(overLoadMail, MailActionType.PASSIVE_DELETE_OVERLIMIT);
            }
        }

    }

    /**
     * 向MailActor拉取离线邮件
     */
    private List<PlayerMailProp> syncOfflineMails() {
        List<PlayerMailProp> result = Lists.newLinkedList();
        final MailReadIndexProp mailReadIndex = getOwner().getProp().getMailModel().getMailreadIndex();
        LOGGER.info("PlayerMailComponent syncOfflineMails, zoneMailIndex={}, clanMailIndex={}-{}", mailReadIndex.getZoneMailIndex(), mailReadIndex.getClanMailIndex().getClanId(), mailReadIndex.getClanMailIndex().getClanMailIndex());
        // 拉离线邮件
        SsSceneMail.SyncPlayerOfflineMailsAsk.Builder ask = SsSceneMail.SyncPlayerOfflineMailsAsk.newBuilder()
                .setCreateTime(getOwner().getProp().getCreateTime())
                .setZoneMailIndex(mailReadIndex.getZoneMailIndex());
        SsSceneMail.SyncPlayerOfflineMailsAns ans = getOwner().ownerActor().callSelfBigScene(ask.build());

        if (!ans.getNewMailCacheList().getDatasList().isEmpty()) {
            LOGGER.info("PlayerMailComponent syncOfflineMails, offline mails:{}", ans.getNewMailCacheList().getDatasList().stream().map(StructMail.NewMailCache::getMailId).collect(Collectors.toList()));
        }

        List<StructMail.NewMailCache> mailsToAdd = new ArrayList<>(ans.getNewMailCacheList().getDatasList());
        // 根据index排序，避免游标错序
        mailsToAdd.sort(Comparator.comparing(StructMail.NewMailCache::getMailIndex));
        // 添加离线邮件
        for (StructMail.NewMailCache mailToAdd : mailsToAdd) {
            PlayerMailProp mail = onMailAdd(mailToAdd, false);
            if (mail == null) {
                continue;
            }
            result.add(mail);
        }

        // 更新游标（未更新邮件也需更新）
        mailReadIndex.setZoneMailIndex(ans.getZoneMailIndex());

        return result;
    }

    /**
     * 获取各邮件页签的未读邮件数量
     */
    public PlayerMail.Player_MailUnreadGet_S2C mailUnReadGet() {
        final List<MailUnreadCountProp> unreadCount = new ArrayList<>();
        for (Map.Entry<CommonEnum.MailTabsType, List<MailMinInfo>> entry : this.mailsInfoMap.entrySet()) {
            final int unReadCount = (int) entry.getValue().stream()
                    .filter(it -> it.getReadState() == CommonEnum.MailReadState.MAIL_READ_STATE_UNREAD)
                    .count();
            unreadCount.add(new MailUnreadCountProp().setType(entry.getKey()).setCount(unReadCount));
        }

        PlayerMail.Player_MailUnreadGet_S2C.Builder builder = PlayerMail.Player_MailUnreadGet_S2C.newBuilder();
        List<StructMailPB.MailUnreadCountPB> collect = unreadCount.stream()
                .map(it -> it.getCopyCsBuilder().build())
                .collect(Collectors.toList());
        builder.getCountBuilder().addAllDatas(collect);
        return builder.build();
    }

    private MailMinInfo getMail(long mailId) {
        return this.playerMails.getOrDefault(mailId, null);
    }

    /**
     * 批量已读邮件
     *
     * @param mailIdList 邮件id列表
     * @return 已读邮件id
     */
    public List<Long> batchReadMail(final List<Long> mailIdList) {
        List<Long> res = new ArrayList<>();
        for (long mailId : mailIdList) {
            MailMinInfo mail = this.getMail(mailId);
            if (mail == null) {
                LOGGER.info("PlayerMailComponent batchReadMail mailId={} not found", mailId);
                continue;
            }
            this.markMailReadedState(mail);
            res.add(mailId);
        }
        return res;
    }

    /**
     * 领邮件奖励
     *
     * @param mailId 邮件id
     */
    public void mailRewardGet(final long mailId) {
        final MailMinInfo mail = this.getMail(mailId);
        if (mail == null) {
            throw new GeminiException(ErrorCode.MAIL_NOT_FOUND.getCodeId());
        }
        this.markMailReadedState(mail);
        List<IntPairType> intPairTypes = this.onMailRewardGet(mail);
        if (intPairTypes.isEmpty()) {
            LOGGER.warn("PlayerMailComponent mailRewardGet mailId={} templateId={} reward is empty", mail.getMailId(), mail.getMailTemplateId());
        }
    }

    /**
     * 批量领取邮件奖励
     *
     * @param mailIdList        邮件ids
     * @param excludeMailIdList 排除得邮件ids
     * @return 奖励列表
     */
    public List<IntPairType> mailRewardBatchGet(List<Long> mailIdList, List<Long> excludeMailIdList) {
        final Set<Long> excludeMailIdSet = new HashSet<>(excludeMailIdList);
        final List<IntPairType> rewards = new ArrayList<>(mailIdList.size());
        for (long mailId : mailIdList) {
            if (excludeMailIdSet.contains(mailId)) {
                continue;
            }
            final MailMinInfo mail = this.getMail(mailId);
            if (mail == null) {
                continue;
            }
            if (mail.getRewardState() != CommonEnum.MailRewardState.MAIL_REWARD_STATE_UNTAKEN) {
                continue;
            }
            List<IntPairType> c = this.onMailRewardGet(mail);
            if (c.isEmpty()) {
                LOGGER.warn("PlayerMailComponent mailRewardBatchGet player={} mailId={} templateId={} reward is empty", getOwner(), mail.getMailId(), mail.getMailTemplateId());
            }
            rewards.addAll(c);
        }
        return rewards;
    }

    /**
     * 获取邮件奖励（调用时就触发发奖）
     *
     * @param mail 奖励邮件。
     * @return 已领取得奖励
     */
    private List<IntPairType> onMailRewardGet(final MailMinInfo mail) {
        if (mail.getRewardState() == CommonEnum.MailRewardState.MAIL_REWARD_STATE_TAKEN) {
            LOGGER.warn("PlayerMailComponent onMailRewardGet already get mailId={} reward", mail.getMailId());
            throw new GeminiException(ErrorCode.MAIL_REWARD_ALREADY_RECEIVED.getCodeId());
        }
        if (mail.getRewardState() == CommonEnum.MailRewardState.MAIL_REWARD_STATE_ONLY_SHOW) {
            LOGGER.warn("PlayerMailComponent onMailRewardGet try get only show mailId={} reward", mail.getMailId());
            throw new GeminiException(ErrorCode.MAIL_MAIL_REWARD_FAILED.getCodeId());
        }
        // 保底逻辑，防止程序漏加上面判断导致的额外领取奖励
        if (mail.getRewardState() != CommonEnum.MailRewardState.MAIL_REWARD_STATE_UNTAKEN) {
            LOGGER.warn("PlayerMailComponent onMailRewardGet get not takeable mailId={} reward", mail.getMailId());
            throw new GeminiException(ErrorCode.MAIL_MAIL_REWARD_FAILED.getCodeId());
        }

        final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
        if (mailTemplate == null) {
            LOGGER.warn("PlayerMailComponent onMailRewardGet mailId={} templateId={} not found", mail.getMailId(), mail.getMailTemplateId());
            throw new GeminiException(ErrorCode.MAIL_NO_CONFIG.getCodeId());
        }

        List<IntPairType> rewardList = getMailReward(mail, mailTemplate);
        if (!rewardList.isEmpty()) {
            mail.setRewardState(CommonEnum.MailRewardState.MAIL_REWARD_STATE_TAKEN);
            // 改数据
            this.dbUpdateRewardStatus(mail);

            // 发奖励
            for (IntPairType reward : rewardList) {
                this.getOwner().getItemComponent().addItem(reward.getKey(), reward.getValue(), CommonEnum.Reason.ICR_MAIL, String.valueOf(mailTemplate.getId()));
            }

            // qLog
            this.getOwner().getQlogComponent().sendMailQLog(MailActionType.COLLECTION_MAIL_REWARD, mail, mailTemplate, rewardList, "", "");

            LOGGER.debug("PlayerMailComponent onMailRewardGet get mail reward mailId={}", mail.getMailId());
        }
        return rewardList;
    }

    /**
     * 更改邮件邀请状态
     *
     * @param mailId        邮件id
     * @param operationType 处理联盟邀请行为
     * @return 邮件邀请状态
     */
    public CommonEnum.MailInviteState mailChangeInviteState(final long mailId, CommonEnum.ClanInvitationOpType operationType) {
        final MailMinInfo mail = this.getMail(mailId);
        if (mail == null) {
            throw new GeminiException(ErrorCode.MAIL_NOT_FOUND.getCodeId());
        }
        return changeMailInviteState(mail, operationType);
    }

    /**
     * 删除邮件
     *
     * @param mailId 邮件id
     * @return 删除邮件S2C
     */
    public PlayerMail.Player_MailDel_S2C mailDel(final long mailId) {
        final MailMinInfo mail = this.getMail(mailId);
        if (mail == null) {
            throw new GeminiException(ErrorCode.MAIL_NOT_FOUND.getCodeId());
        }
        final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());

        if (hasUnTakenRewards(mail)) {
            throw new GeminiException(ErrorCode.MAIL_GET_REWARD_FIRST.getCodeId());
        }
        final CommonEnum.MailTabsType tabsType = this.getMailTabsTypeByMailInfo(mail);
        LOGGER.debug("PlayerMailComponent mailDel player delete mailId={}", mailId);
        this.markDeleteMail(mail);
        PlayerMail.Player_MailDel_S2C.Builder builder = PlayerMail.Player_MailDel_S2C.newBuilder();
        builder.setMailTabsType(mail.getIsStored() ? CommonEnum.MailTabsType.MAIL_TABS_TYPE_STORE : tabsType);
        builder.setMailId(mailId);

        // qLog
        this.getOwner().getQlogComponent().sendMailQLog(MailActionType.DELETE_MAIL, mail, mailTemplate, getMailReward(mail, mailTemplate), "", "");
        return builder.build();

    }

    /**
     * 删除已收到邮件列表。
     *
     * @param mailIdList 批量删除的邮件id列表。
     * @return 删除邮件id列表。
     */
    public List<Long> mailBatchDel(final List<Long> mailIdList) {
        if (mailIdList.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        final List<Long> delMailIdList = new ArrayList<>(mailIdList.size());
        for (final long mailId : mailIdList) {
            final MailMinInfo mail = this.getMail(mailId);
            if (mail == null) {
                LOGGER.debug("PlayerMailComponent mailBatchDel mailId={} not found", mailId);
                continue;
            }
            final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
            if (mailTemplate == null) {
                LOGGER.debug("PlayerMailComponent mailBatchDel mailId={} templateId={} not found", mailId, mail.getMailTemplateId());
                continue;
            }

            if (hasUnTakenRewards(mail)) {
                LOGGER.debug("PlayerMailComponent mailBatchDel mailId={} get reward first", mailId);
                continue;
            }

            this.markDeleteMail(mail);

            // qLog
            this.getOwner().getQlogComponent().sendMailQLog(MailActionType.DELETE_MAIL, mail, mailTemplate, getMailReward(mail, mailTemplate), "", "");
            delMailIdList.add(mailId);
        }
        return delMailIdList;
    }

    /**
     * 获取页签下所有邮件信息
     *
     * @param tabsType 页签
     * @return 页签下所有邮件信息 | 空列表
     */
    private List<MailMinInfo> getMailsUnderTab(CommonEnum.MailTabsType tabsType) {
        return this.mailsInfoMap.getOrDefault(tabsType, Collections.emptyList());
    }

    /**
     * 页签下所有邮件id
     *
     * @param tabsType 页签
     * @return 页签下所有邮件id | 空列表
     */
    public List<Long> getMailIdsUnderTab(CommonEnum.MailTabsType tabsType) {
        return this.mailsInfoMap.getOrDefault(tabsType, Collections.emptyList()).stream().map(MailMinInfo::getMailId).collect(Collectors.toList());
    }


    /**
     * 玩家收到邮件
     *
     * @param newMailProto 新邮件cache
     * @param idIpMailData idIp邮件数据
     */
    public void handleReceiveMail(StructMail.NewMailCache newMailProto, StructMsg.IdIpMailData idIpMailData) {
        if (newMailProto.getMailType() == CommonEnum.MailType.MAIL_TYPE_PERSONAL && newMailProto.getSender().getSenderId() > 0) {
            final long senderId = newMailProto.getSender().getSenderId();
            final PlayerShieldMailPersonProp shieldPerson = this.getOwner().getProp().getMailModel().getShieldPersonsV(senderId);

            if (shieldPerson != null) {
                // 屏蔽仍生效
                if (shieldPerson.getShieldExpireTsSec() >= SystemClock.nowOfSeconds()) {
                    LOGGER.info("PlayerMailComponent handleReceiveMail {} shield {}", newMailProto, shieldPerson);
                    return;
                }
                // 屏蔽已过期
                this.getOwner().getProp().getMailModel().removeShieldPersonsV(senderId);
            }
        }
        boolean idIpMail = CommonIdIpHelper.isIdIpMail(idIpMailData);
        PlayerMailProp newMail = onMailAdd(newMailProto, idIpMail);
        // 邮件添加失败
        if (newMail == null) {
            return;
        }
        // 通知客户端
        List<PlayerMailProp> toNotifyMails = new ArrayList<>();
        toNotifyMails.add(newMail);
        notifyReceiveNewMails(toNotifyMails);

        if (idIpMail) {
            LOGGER.info("PlayerMailComponent handleReceiveMail mailId={} idIpMailData={}", newMail.getMailId(), idIpMail);
            for (ItemPairProp itemPairProp : newMail.getItemReward()) {
                sendIdIpMailRewardQLog(itemPairProp, idIpMailData);
            }
        }
    }

    /**
     * 给自己添加一封个人邮件（同步写入）
     *
     * @param mailSendParams 发送邮件所需参数
     */
    private void sendSelfMail(StructMail.MailSendParams mailSendParams) {
        LOGGER.info("PlayerMailComponent sendSelfMail templateId={}", mailSendParams.getMailTemplateId());
        final long mailId = MailUtil.genMailId(MailIdType.PLAYER);
        StructMail.NewMailCache.Builder newMailCache = MailUtil.buildNewMailCachePb(mailSendParams, CommonEnum.MailType.MAIL_TYPE_PERSONAL, 0, mailId, 0);
        this.handleReceiveMail(newMailCache.build(), null);
    }

    /**
     * 获取邮件配置
     *
     * @param mailTemplateId 邮件配置id
     * @return 邮件配置
     */
    private MailTemplate getMailTemplate(int mailTemplateId) {
        return ResHolder.getResService(MailTemplateService.class).getMailTemplate(mailTemplateId);
    }

    /**
     * 获取页签过期配置
     *
     * @param mailTabsType 邮件页签
     * @return 页签过期配置
     */
    private MailExpireTemplate getMailExpireTemplate(final CommonEnum.MailTabsType mailTabsType) {
        return ResHolder.getResService(MailTemplateService.class).getMailExpireTemplate(mailTabsType);
    }

    /**
     * IDIP邮件奖励log
     *
     * @param itemPairProp 奖励内容
     * @param idIpMailData IDIP邮件内容
     */
    private void sendIdIpMailRewardQLog(ItemPairProp itemPairProp, StructMsg.IdIpMailData idIpMailData) {
        QlogIdipReq.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setICount(itemPairProp.getCount())
                .setIGoodsId(String.valueOf(itemPairProp.getItemTemplateId()))
                .setChannId(idIpMailData.getSource())
                .setSeqId(idIpMailData.getSerial())
                .setIdipCommand(idIpMailData.getCmdId())
                .sendToQlog();
    }

    /**
     * 接收邮件Qlog。
     *
     * @param mail 邮件。
     */
    private void sendAddMailQlog(final MailMinInfo mail) {
        // 1. 发送方id
        final String senderId;
        if (this.getMailTabsTypeByMailInfo(mail) == CommonEnum.MailTabsType.MAIL_TABS_TYPE_PERSONAL) {
            senderId = mail.getSenderId() != 0 ? String.valueOf(mail.getSenderId()) : "";
        } else {
            senderId = "";
        }
        // 2. qlog
        final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
        this.getOwner().getQlogComponent().sendMailQLog(MailActionType.RECEIVE_MAIL, mail, mailTemplate, getMailReward(mail, mailTemplate), "", senderId);
    }

    /**
     * 被动删除Qlog。
     *
     * @param mail       目标邮件。
     * @param actionType PASSIVE_DELETE_OVERTIME or PASSIVE_DELETE_OVERLIMIT。
     */
    private void sendPassiveDeleteQlog(final MailMinInfo mail, MailActionType actionType) {
        if (actionType != MailActionType.PASSIVE_DELETE_OVERTIME && actionType != MailActionType.PASSIVE_DELETE_OVERLIMIT) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 1. 发送方id
        final String senderId;
        if (this.getMailTabsTypeByMailInfo(mail) == CommonEnum.MailTabsType.MAIL_TABS_TYPE_PERSONAL) {
            senderId = mail.getSenderId() != 0 ? String.valueOf(mail.getSenderId()) : "";
        } else {
            senderId = "";
        }
        // 2. qlog
        final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
        this.getOwner().getQlogComponent().sendMailQLog(actionType, mail, mailTemplate, getMailReward(mail, mailTemplate), "", senderId);
    }

    /**
     * 收藏邮件Qlog
     *
     * @param mail 目标邮件
     */
    private void sendFavoriteMailQlog(final MailMinInfo mail) {
        final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
        this.getOwner().getQlogComponent().sendMailQLog(MailActionType.ADD_TO_FAVORITE, mail, mailTemplate, getMailReward(mail, mailTemplate), "", "");
    }

    /**
     * 更新全局邮件相关下标
     *
     * @param newMailPb 全局邮件（联盟、全服）
     */
    private void updateMailReadIndex(StructMail.NewMailCache newMailPb) {
        MailReadIndexProp readIndex = getOwner().getProp().getMailModel().getMailreadIndex();
        // 不存在邮件编号
        if (newMailPb.getMailIndex() <= 0) {
            return;
        }

        if (newMailPb.getMailType() == CommonEnum.MailType.MAIL_TYPE_ZONE) {
            if (newMailPb.getMailIndex() > readIndex.getZoneMailIndex()) {
                LOGGER.info("PlayerMailComponent updateMailReadIndex set zoneMailIndex from={}, to={}", readIndex.getZoneMailIndex(), newMailPb.getMailIndex());
                readIndex.setZoneMailIndex(newMailPb.getMailIndex());
                return;
            }
            LOGGER.warn("PlayerMailComponent updateMailReadIndex filter zoneMail player zoneMailIndex={}, zoneMailIndex={}", readIndex.getZoneMailIndex(), newMailPb.getMailIndex());
        } else if (newMailPb.getMailType() == CommonEnum.MailType.MAIL_TYPE_CLAN) {
            if (newMailPb.getMailIndex() > readIndex.getClanMailIndex().getClanMailIndex()) {
                LOGGER.info("PlayerMailComponent updateMailReadIndex set clanMailIndex from={}, to={}, clanId={}", readIndex.getClanMailIndex().getClanMailIndex(), newMailPb.getMailIndex(), readIndex.getClanMailIndex().getClanId());
                readIndex.getClanMailIndex().setClanMailIndex(newMailPb.getMailIndex());
                return;
            }
            LOGGER.warn("PlayerMailComponent updateMailReadIndex filter clanMail player clanMailIndex={}, clanMailIndex={}", readIndex.getClanMailIndex().getClanMailIndex(), newMailPb.getMailIndex());
        }

    }

    /**
     * 通知客户端收到新邮件
     *
     * @param mails 新邮件
     */
    public void notifyReceiveNewMails(List<PlayerMailProp> mails) {
        Map<CommonEnum.MailTabsType, List<PlayerMailProp>> newMails = new HashMap<>();
        for (PlayerMailProp mailProp : mails) {
            MailTemplate mailTemplate = this.getMailTemplate(mailProp.getMailTemplateId());
            CommonEnum.MailTabsType mailTabsType = mailTemplate.getMailTabsType();

            if (!newMails.containsKey(mailTabsType)) {
                newMails.put(mailTabsType, new ArrayList<>());
            }
            newMails.get(mailTabsType).add(mailProp);
        }

        for (Map.Entry<CommonEnum.MailTabsType, List<PlayerMailProp>> entry : newMails.entrySet()) {
            this.notifyClientNewMail(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 生成新邮件ntf builder
     *
     * @param mailTabsType 页签
     * @param mailProps    邮件
     * @return Player_Get_New_Mail_NTF.Builder
     */
    private PlayerMail.Player_Get_New_Mail_NTF.Builder genGetNewMailNtfBuilder(CommonEnum.MailTabsType
                                                                                       mailTabsType, List<PlayerMailProp> mailProps) {
        PlayerMail.Player_Get_New_Mail_NTF.Builder builder = PlayerMail.Player_Get_New_Mail_NTF.newBuilder();
        builder.setMailTabsType(mailTabsType);
        List<StructMailPB.PlayerMailPB> collect = mailProps.stream()
                .map(it -> it.getCopyCsBuilder().build())
                .collect(Collectors.toList());
        builder.getMailsBuilder().addAllDatas(collect);
        return builder;
    }

    /**
     * 添加一封发送邮件
     *
     * @param mailTemplateId 邮件模板id
     * @param mailType       邮件类型
     * @param title          标题
     * @param titleData      标题数据
     * @param subTitle       副标题
     * @param subTitleData   副标题数据
     * @param receiverCard   收件人卡片
     * @param mailContent    邮件内容
     */
    public void handleSendMail(final int mailTemplateId, final CommonEnum.MailType mailType,
                               String title, Struct.DisplayData titleData, String subTitle, Struct.DisplayData subTitleData,
                               StructMailPB.PlayerMailReceiverCardPB receiverCard, StructMail.MailContent mailContent) {
        // 已发送邮件的id需要和接收不同，因此额外生成。
        for (final MailMinInfo removeMail : this.getOverLoadMails(CommonEnum.MailTabsType.MAIL_TABS_TYPE_SEND)) {
            // 通知邮件删除
            this.notifyClientMailDelete(removeMail.getMailId(), CommonEnum.MailTabsType.MAIL_TABS_TYPE_SEND);
        }
        final PlayerMailProp mailProp = new PlayerMailProp();
        mailProp.setMailId(IdFactory.nextId("send_mail"));
        mailProp.setMailType(mailType);
        mailProp.setMailTemplateId(mailTemplateId);
        mailProp.setCreateTimestamp(SystemClock.now());
        mailProp.setRewardState(CommonEnum.MailRewardState.MAIL_REWARD_STATE_ONLY_SHOW);
        mailProp.setReadState(CommonEnum.MailReadState.MAIL_READ_STATE_READED);
        mailProp.setCanReceive(true);
        mailProp.getSender().setSenderId(this.getPlayerId());
        mailProp.setIsSender(true);
        mailProp.getContent().mergeFromSs(mailContent);
        if (titleData == null) {
            mailProp.getTitle().setTitle(title);
        } else {
            mailProp.getTitle().getTitleData().mergeFromSs(titleData);
        }
        if (subTitleData == null) {
            mailProp.getTitle().setSubTitle(subTitle);
        } else {
            mailProp.getTitle().getSubTitleData().mergeFromSs(subTitleData);
        }
        if (receiverCard != null) {
            mailProp.getReceiverCard().mergeFromCs(receiverCard);
        }
        final List<PlayerMailProp> mails = new ArrayList<>(1);
        mails.add(mailProp);
        this.dbInsertMail(mailProp, mailContent);
        this.notifyClientNewMail(CommonEnum.MailTabsType.MAIL_TABS_TYPE_SEND, mails);
    }

    public void sendPersonalMail(final CommonMsg.MailReceiver receiver, final StructMail.MailSendParams mailSendParams) {
        LOGGER.info("PlayerMailComponent sendPersonalMail receiver={}, mailTemplateId={}", receiver, mailSendParams.getMailTemplateId());
        if (receiver.getPlayerId() == 0) {
            throw new GeminiException("sendPersonalMail receiver invalid playerId");
        }
        if (receiver.getZoneId() == 0) {
            throw new GeminiException("sendPersonalMail recerver invalid zoneId");
        }
        if (receiver.getPlayerId() == this.getPlayerId()) {
            this.ownerActor().tell(this.ownerActor().self(), new ActorRunnable<PlayerActor>("sendSelfMail", (actor) -> {
                sendSelfMail(mailSendParams);
            }));
            return;
        }
        MailUtil.sendMailToPlayer(receiver, mailSendParams);
    }


    /**
     * 转为发给客户端的数据
     *
     * @param newMail 新邮件
     * @return 客户端邮件
     */
    private StructMail.PlayerMail toPlayerMailHeader(StructMail.NewMailCache newMail) {
        if (!newMail.hasMailId()) {
            throw new GeminiException("no mail id in mai {}", newMail);
        }
        StructMail.PlayerMail.Builder builder = StructMail.PlayerMail.newBuilder();
        // 个人邮件，在当前PlayerEntity上生成MailId
        builder.setMailId(newMail.getMailId());
        builder.setMailTemplateId(newMail.getMailTemplateId());
        builder.setReadState(CommonEnum.MailReadState.MAIL_READ_STATE_UNREAD);
        builder.setSender(newMail.getSender());
        builder.setCreateTimestamp(newMail.getCreateTimestamp());
        builder.setStore(false);

        MailTemplate mailTemplate = this.getMailTemplate(newMail.getMailTemplateId());
        if (newMail.getIsOnlyForShow()) {
            builder.setRewardState(CommonEnum.MailRewardState.MAIL_REWARD_STATE_ONLY_SHOW);
        } else if (!mailTemplate.getAwardPairList().isEmpty() || newMail.getItemReward().getDatasCount() > 0) {
            builder.setRewardState(CommonEnum.MailRewardState.MAIL_REWARD_STATE_UNTAKEN);
        } else {
            builder.setRewardState(CommonEnum.MailRewardState.MAIL_REWARD_STATE_TAKEN);
        }
        if (newMail.getHasInvitedStatus()) {
            builder.setInviteState(CommonEnum.MailInviteState.MAIL_INVITE_NOT_HANDLE);
        }
        // 多语言
        final StructMail.MailShowTitle.Builder titleBuilder = newMail.getTitle().toBuilder();
        LOGGER.info("PlayerMailComponent toPlayerMailHeader try transMultiLangTxt for titleData mailId={}", newMail.getMailId());
        transMultiLangTxt(titleBuilder.getTitleDataBuilder().getParamsBuilder(), getOwner().getClientLanguage());
        LOGGER.info("PlayerMailComponent toPlayerMailHeader try transMultiLangTxt for subTitleData mailId={}", newMail.getMailId());
        transMultiLangTxt(titleBuilder.getSubTitleDataBuilder().getParamsBuilder(), getOwner().getClientLanguage());
        builder.setTitle(titleBuilder.build());
        builder.setItemReward(newMail.getItemReward());
        builder.setLanguage(getOwner().getClientLanguage());
        builder.setMailType(newMail.getMailType());
        builder.setOfflineExpiredTime(newMail.getExpireTimestamp());
        builder.setCanReceive(true);
        // 离线邮件过期时间，离线玩家在这个时间之后登录，收不到这封邮件
        if (newMail.getExpireTimestamp() > 0) {
            builder.setCanReceive(getOwner().isOnline());
        }
        final StructMail.MailContent.Builder contentBuilder = newMail.getContent().toBuilder();
        LOGGER.info("PlayerMailComponent toPlayerMailHeader try transMultiLangTxt for mailContent mailId={}", newMail.getMailId());
        transMultiLangTxt(contentBuilder.getDisplayDataBuilder().getParamsBuilder(), getOwner().getClientLanguage());
        builder.mergeContent(contentBuilder.build());

        return builder.build();
    }

    /**
     * 把多语言的内容转换成玩家语言。
     * 注意：
     * 1. 本函数主要作用于DPT_MULTI_LANG_TXT对应的内容，此类型由运营使用。
     * 2. 本函需要配置多种（至少包含英文）文本，如未配置对应文本，会强制使用英文文本，如英文未配置，则导致文本丢失。
     */
    private void transMultiLangTxt(Struct.DisplayParamList.Builder builder, CommonEnum.Language lang) {
        Integer multiLangTxtIndex = null;
        for (int i = 0; i < builder.getDatasCount(); i++) {
            if (builder.getDatas(i).getType() == CommonEnum.DisplayParamType.DPT_MULTI_LANG_TXT) {
                multiLangTxtIndex = i;
                break;
            }
        }
        // 无可翻译多语言数据，日志告警
        if (multiLangTxtIndex == null) {
            LOGGER.warn("PlayerMailComponent transMultiLangTxt no multiLangTxt found");
            return;
        }

        Struct.MultiLangTxt multiLangTxtPb = builder.getDatas(multiLangTxtIndex).getMultiLangTxt().getDatasMap().get(lang.getNumber());
        if (multiLangTxtPb == null) {
            // 如果语言不存在，默认英语
            multiLangTxtPb = builder.getDatas(multiLangTxtIndex).getMultiLangTxt().getDatasMap().get(CommonEnum.Language.en_VALUE);
            LOGGER.warn("PlayerMailComponent transMultiLangTxt no support language found, language={}, use English", lang);
        } else if (multiLangTxtPb.hasLanguage() && multiLangTxtPb.getLanguage() == CommonEnum.Language.L_NONE_VALUE) {
            // 如果语言传了默认枚举，那能怎么办，改成英语呗
            multiLangTxtPb = builder.getDatas(multiLangTxtIndex).getMultiLangTxt().getDatasMap().get(CommonEnum.Language.en_VALUE);
            LOGGER.warn("PlayerMailComponent transMultiLangTxt initial language [NONE], use English");
        } else if (multiLangTxtPb.hasLanguage() && multiLangTxtPb.getLanguage() == CommonEnum.Language.other_VALUE) {
            // 如果语言为other，默认英语
            multiLangTxtPb = builder.getDatas(multiLangTxtIndex).getMultiLangTxt().getDatasMap().get(CommonEnum.Language.en_VALUE);
            LOGGER.warn("PlayerMailComponent transMultiLangTxt initial language [other], use English");
        }
        // 删除多语言data
        builder.removeDatas(multiLangTxtIndex);
        // 插入当前语言data
        if (multiLangTxtPb != null) {
            builder.addDatas(Struct.DisplayParam.newBuilder().setType(CommonEnum.DisplayParamType.DPT_TEXT).setText(multiLangTxtPb.getText()));
        }

    }

    public int getClanMailIndex() {
        MailReadIndexProp readIndex = getOwner().getProp().getMailModel().getMailreadIndex();
        return readIndex.getClanMailIndex().getClanMailIndex();
    }

    /**
     * 重置联盟邮件游标
     */
    public void resetClanMailIndex() {
        MailReadIndexProp readIndex = getOwner().getProp().getMailModel().getMailreadIndex();
        // -1表示拉取时候跳过当前所有index。
        readIndex.getClanMailIndex().setClanMailIndex(-1);
        readIndex.getClanMailIndex().setClanId(getOwner().getClanId());
        LOGGER.info("resetClanMailIndex clanId={} ", getOwner().getClanId());
    }

    /**
     * 获取邮件奖励
     *
     * @param mail         邮件
     * @param mailTemplate 邮件模板
     * @return 奖励列表
     */
    public List<IntPairType> getMailReward(final MailMinInfo mail, MailTemplate mailTemplate) {
        List<IntPairType> rewardList = new ArrayList<>();
        // 自定义奖励
        for (Struct.ItemPair extraItemReward : mail.getExtraItemRewards()) {
            if (ResHolder.getResService(ItemResService.class).getItemTemplate(extraItemReward.getItemTemplateId()) == null || extraItemReward.getCount() <= 0) {
                throw new GeminiException("PlayerMailComponent getMailReward fail, mailTemplateId={}, mailItemReward={}", mail.getMailTemplateId(), mail.getExtraItemRewards());
            }
            rewardList.add(IntPairType.makePair(extraItemReward.getItemTemplateId(), extraItemReward.getCount()));
        }
        // 配表奖励
        if (!mailTemplate.getAwardPairList().isEmpty()) {
            rewardList.addAll(mailTemplate.getAwardPairList());
        }
        return rewardList;
    }


    /**
     * 读战报详情（使用自动缓存，未命中则访问DB）
     *
     * @param msg 战报详情c2s
     * @return 战报详情s2c
     */
    public PlayerMail.Player_ReadBattleRecordDetail_S2C readBattleRecordDetail(PlayerMail.Player_ReadBattleRecordDetail_C2S msg) {
        try {
            return BATTLE_RECORD_DETAIL_CACHE.getData(msg, this.getOwner().ownerActor());
        } catch (GeminiException e) {
            throw e;
        } catch (Throwable throwable) {
            throw new GeminiException("PlayerMailComponent readBattleRecordDetail fail", throwable);
        }
    }

    /**
     * 读战报（使用自动缓存，未命中则访问DB）
     *
     * @param msg 战报c2s
     * @return 战报s2c
     */
    public PlayerMail.Player_ReadBattleLog_S2C readBattleLog(PlayerMail.Player_ReadBattleLog_C2S msg) {
        try {
            return BATTLE_LOG_CACHE.getData(msg, this.getOwner().ownerActor());
        } catch (GeminiException e) {
            throw e;
        } catch (Throwable throwable) {
            throw new GeminiException("PlayerMailComponent readBattleLog", throwable);
        }
    }

    /**
     * 压测用，添加战斗邮件
     *
     * @param count       数量
     * @param withContent 是否有MailContent
     */
    public void addBattleMail(int count, boolean withContent) {
        for (int i = 0; i < count; i++) {
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                    .setMailTemplateId(ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getBattleReportMailId());
            {

                // 构建战斗数据
                long now = SystemClock.now();

                StructBattle.BattleRecordAll.Builder recordAll = StructBattle.BattleRecordAll.newBuilder()
                        .setRecordId(123456)
                        .setStartMillis(now)
                        .setEndMillis(now);

                for (int j = 0; j < 5; j++) {
                    StructBattle.BattleRecordOne.Builder one = StructBattle.BattleRecordOne.newBuilder()
                            .setStartMillis(now)
                            .setEndMillis(now)
                            .setIsLeftRole(true)
                            .setIsAllLoss(false)
                            .setBattleId(123456);
                    one.getLocationBuilder().setX(10000).setY(10000);
                    one.getBattleLogIdListBuilder().addDatas(123456L);
                    fillRole(one.getLeftRoleBuilder());
                    fillRole(one.getRightRoleBuilder());

                    recordAll.getSingleRecordListBuilder().addDatas(one);
                }

                recordAll.getSelfSummaryBuilder().setClanName("测试联盟").setTotal(123456).setLeftAlive(123456);
                recordAll.getSelfSummaryBuilder().getCardHeadBuilder().setName("我是测试的玩家名").setPic(15010).setPicFrame(1);
                for (int j = 1; j <= 60; j++) {
                    recordAll.getRoundAliveListBuilder().addDatas(StructBattle.BattleRecordRoundAlive.newBuilder().setRound(j).setAlive(10000 + j));
                }
                for (int j = 1; j <= 60; j++) {
                    StructBattle.BattleRecordRoundEvent.Builder battleRecordRoundEvent = StructBattle.BattleRecordRoundEvent.newBuilder().setRound(j);
                    battleRecordRoundEvent.getTroopEventListBuilder().addDatas(StructBattle.BattleRecordTroopEvent.newBuilder().setClanName("测试联盟").setType(CommonEnum.BattleLogTroopChangeType.BLTCT_HOSPITAL).setSoldierNum(123456));
                    recordAll.getRoundEventListBuilder().addDatas(battleRecordRoundEvent);
                }


                // 构建邮件内容
                mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_BATTLE_RECORD_ALL);
                mailSendParams.getContentBuilder().setBattleRecord(recordAll);

                // title
                StructMail.MailShowTitle.Builder title = StructMail.MailShowTitle.newBuilder();
                ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                title.setTitleKey(constTemplate.getBattleRecord6());
                String subTitleKey = constTemplate.getBattleRecordSub13();
                String clanName = "测试联盟";
                title.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                title.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText("我是测试的玩家名"));
                title.setSubTitleKey(subTitleKey);
                mailSendParams.setTitle(title);

                // 填充title额外参数
                if (!recordAll.getSingleRecordList().getDatasList().isEmpty()) {
                    // 主将信息
                    Struct.DisplayParam.Builder heroParam = Struct.DisplayParam.newBuilder().setType(CommonEnum.DisplayParamType.DPT_HERO_ID).setNumber(1408);
                    mailSendParams.getTitleBuilder().getExtraNotifyDataBuilder().getParamsBuilder().addDatas(heroParam);
                }
            }
            int mailId = i + 1;
            final StructMail.NewMailCache.Builder builder = MailUtil.buildNewMailCachePb(mailSendParams.build(), CommonEnum.MailType.MAIL_TYPE_PERSONAL, 0, mailId, 0);
            //折叠邮件，需要把邮件内容也投递到player侧
            if (withContent) {
                builder.setContent(mailSendParams.getContent());
            }
            this.sendPersonalMail(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(getPlayerId())
                            .setZoneId(this.getOwner().getZoneId())
                            .build(),
                    mailSendParams.build());

        }
    }

    /**
     * GM指令填满邮箱
     */
    public void fullMailByGm() {
        getOwner().ownerActor().addTimer(TimerReasonType.FILL_BATTLE_NAIL_GM, () -> fillBattleMailByGm(200), 1, TimeUnit.MILLISECONDS);
        getOwner().ownerActor().addTimer(TimerReasonType.FILL_SPY_MAIL_GM, () -> fillSpyMailByGm(150), 2000, TimeUnit.MILLISECONDS);
        getOwner().ownerActor().addTimer(TimerReasonType.FILL_COLLECTION_MAIL_GM, () -> fillCollectionMailByGm(150), 4000, TimeUnit.MILLISECONDS);
        getOwner().ownerActor().addTimer(TimerReasonType.FILL_EXPLORE_MAIL_GM, () -> fillExploreMailByGm(100), 6000, TimeUnit.MILLISECONDS);
    }

    public final Map<Long, MailMinInfo> getPlayerMail() {
        return playerMails;
    }

    public void gmDeleteLatestReportMail() {
        final List<MailMinInfo> mailMinInfoList = this.getMailsUnderTab(CommonEnum.MailTabsType.MAIL_TABS_TYPE_REPORT);
        MailMinInfo deleteMail = mailMinInfoList.get(mailMinInfoList.size() - 1);

        this.notifyClientMailDelete(deleteMail.getMailId(), this.getMailTabsTypeByMailInfo(deleteMail));
        this.onPassiveMailDel(deleteMail);
    }

    public void gmDeleteOldestReportMail() {
        final List<MailMinInfo> mailMinInfoList = this.getMailsUnderTab(CommonEnum.MailTabsType.MAIL_TABS_TYPE_REPORT);
        MailMinInfo deleteMail = mailMinInfoList.get(0);
        this.notifyClientMailDelete(deleteMail.getMailId(), this.getMailTabsTypeByMailInfo(deleteMail));
        this.onPassiveMailDel(deleteMail);
    }

    /**
     * 获取特定页签下超容量邮件
     *
     * @param mailTabsType 目标页签
     * @return 需删除的超容量邮件列表
     */
    private List<MailMinInfo> getOverLoadMails(CommonEnum.MailTabsType mailTabsType) {
        final List<MailMinInfo> mailsUnderTab = this.getMailsUnderTab(mailTabsType);
        int maxSize = this.getMailExpireTemplate(mailTabsType).getMax();
        if (mailsUnderTab.size() < maxSize) {
            return Collections.emptyList();
        }
        ListIterator<MailMinInfo> it = mailsUnderTab.listIterator();
        List<MailMinInfo> res = Lists.newLinkedList();
        int needDeleteSize = mailsUnderTab.size() - maxSize;
        while (needDeleteSize > 0 && it.hasNext()) {
            final MailMinInfo curKey = it.next();
            if (curKey.getIsStored()) {
                continue;
            }
            res.add(curKey);
            needDeleteSize--;
        }
        return res;
    }

    /**
     * 阅读邮件后标记为已读
     *
     * @param mail 邮件
     */
    private void markMailReadedState(MailMinInfo mail) {
        final CommonEnum.MailReadState oldState = mail.getReadState();
        mail.setReadState(CommonEnum.MailReadState.MAIL_READ_STATE_READED);
        this.getOwner().getProp().getMailModel().addReadedMails(mail.getMailId());
        if (oldState == CommonEnum.MailReadState.MAIL_READ_STATE_UNREAD) {
            MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
            // qLog
            this.getOwner().getQlogComponent().sendMailQLog(MailActionType.READ_MAIL, mail, mailTemplate, getMailReward(mail, mailTemplate), "", "");
        }
    }

    /**
     * 生成db内Status字段
     *
     * @param mail 邮件
     * @return int
     */
    private int formMailStatus(final PlayerMailProp mail) {
        return DbTransFormer.toMailStatus(mail.getReadState() == CommonEnum.MailReadState.MAIL_READ_STATE_READED, mail.getStore());
    }

    /**
     * 邀请邮件更改状态
     */
    private CommonEnum.MailInviteState changeMailInviteState(MailMinInfo mail, CommonEnum.ClanInvitationOpType operationType) {
        final CommonEnum.MailInviteState oldInviteState = mail.getInviteState();
        CommonEnum.MailInviteState newInviteState;
        switch (operationType) {
            case CIOT_AGREE:
                newInviteState = CommonEnum.MailInviteState.MAIL_INVITE_AGREE;
                mail.setInviteState(newInviteState);
                break;
            case CIOT_REFUSE:
                newInviteState = CommonEnum.MailInviteState.MAIL_INVITE_REFUSE;
                mail.setInviteState(newInviteState);
                break;
            default:
                LOGGER.error("changeMailInviteState error, operationType:{}", operationType);
                return oldInviteState;
        }
        // 原来是未处理状态，更改为已处理状态(接受或拒绝)
        if (oldInviteState == CommonEnum.MailInviteState.MAIL_INVITE_NOT_HANDLE) {
            this.doUpdateInviteStatus(mail);
        }
        return newInviteState;
    }

    /**
     * 标记待删除邮件
     *
     * @param mail 邮件
     */
    private void markDeleteMail(final MailMinInfo mail) {
        this.delMailMinInfo(mail);
        this.getOwner().getProp().getMailModel().getDeleteMails().add(mail.getMailId());
        this.tryAddDeleteMailsTimer();
        LOGGER.info("PlayerMailComponent markDeleteMail mailId={}", mail.getMailId());
    }

    /**
     * db插入邮件
     *
     * @param mail        邮件header
     * @param mailContent 邮件content
     */
    @Nullable
    private MailMinInfo dbInsertMail(PlayerMailProp mail, StructMail.MailContent mailContent) {
        final MailMinInfo mailMinInfo = new MailMinInfo(mail, false);
        if (!this.addMailMinInfo(mailMinInfo)) {
            LOGGER.warn("PlayerMailComponent dbInsertMail mailId={} already exists, skip", mailMinInfo.getMailId());
            return null;
        }

        TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
        req.setPlayerId(this.getPlayerId());
        req.setMailId(mail.getMailId());
        req.setStatus(this.formMailStatus(mail));
        req.setHeader(DbTransFormer.toMailBrief(mail));
        req.setContent(mailContent);
        req.setInviteStatus(mail.getInviteState().getNumber());
        req.setRewardStatus(mail.getRewardState().getNumber());
        LOGGER.info("PlayerMailComponent dbInsertMail mail={}", mail);
        try {
            InsertResult<TcaplusDb.PlayerMailTable.Builder> insertResult = this.ownerActor().callGameDb(new InsertAsk<>(req));
            if (!insertResult.isOk()) {
                // 全服邮件&联盟邮件 由于db乱序可能重复插入，已插入的不算出错
                if (DbUtil.isRecordAlreadyExist(insertResult.getCode())) {
                    LOGGER.info("PlayerMailComponent dbInsertMail isRecordAlreadyExist, mailId={}", mail.getMailId());
                } else {
                    LOGGER.error("PlayerMailComponent dbInsertMail failed, code={}, req={}", insertResult.getCode(), req);
                }
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("PlayerMailComponent dbInsertMail failed", e);
            return null;
        }

        return mailMinInfo;
    }

    /**
     * 更新邮件DB中状态字段(仅收藏会触发，其余情况不触发)
     *
     * @param mail 内存态邮件头
     */
    private void dbUpdateStatus(final MailMinInfo mail) {
        TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
        req.setStatus(mail.formDbStatus())
                .setMailId(mail.getMailId())
                .setPlayerId(this.getPlayerId());
        UpdateResult<TcaplusDb.PlayerMailTable.Builder> ans = this.ownerActor().callGameDb(new UpdateAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.error("PlayerMailComponent dbUpdateStatus fail, req={} code={}", req, ans.getCode());
            throw new GeminiException("PlayerMailComponent dbUpdateStatus fail");
        }
    }

    /**
     * 更新邮件DB中领奖状态字段
     *
     * @param mail 内存态邮件头
     */
    private void dbUpdateRewardStatus(MailMinInfo mail) {
        TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
        req.setRewardStatus(mail.getRewardState().getNumber())
                .setMailId(mail.getMailId())
                .setPlayerId(this.getPlayerId());

        UpdateResult<TcaplusDb.PlayerMailTable.Builder> ans = this.ownerActor().callGameDb(new UpdateAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.error("PlayerMailComponent dbUpdateRewardStatus fail, req={} code={}", req, ans.getCode());
            throw new GeminiException("PlayerMailComponent dbUpdateRewardStatus fail");
        }
    }

    /**
     * 更新邮件DB中邀请状态字段
     *
     * @param mail 内存态邮件头
     */
    private void doUpdateInviteStatus(MailMinInfo mail) {
        TcaplusDb.PlayerMailTable.Builder req = TcaplusDb.PlayerMailTable.newBuilder();
        req.setInviteStatus(mail.getInviteState().getNumber())
                .setMailId(mail.getMailId())
                .setPlayerId(this.getPlayerId());

        UpdateResult<TcaplusDb.PlayerMailTable.Builder> ans = this.ownerActor().callGameDb(new UpdateAsk<>(req));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.error("PlayerMailComponent dbUpdateInviteStatus, req={} code={}", req, ans.getCode());
            throw new GeminiException("PlayerMailComponent dbUpdateInviteStatus fail");
        }
    }

    /**
     * 通知客户端邮件删除
     *
     * @param mailId       邮件id
     * @param mailTabsType 邮件页签
     */
    private void notifyClientMailDelete(long mailId, CommonEnum.MailTabsType mailTabsType) {
        final PlayerMail.Player_Del_Mail_NTF delMsg = PlayerMail.Player_Del_Mail_NTF.newBuilder()
                .setMailId(mailId)
                .setMailTabsType(mailTabsType)
                .build();
        this.getOwner().sendMsgToClient(MsgType.PLAYER_DELMAIL_NTF, delMsg);
    }

    /**
     * 通知客户端收到新邮件
     *
     * @param mailTabsType 邮件页签
     * @param mails        新邮件s
     */
    private void notifyClientNewMail(CommonEnum.MailTabsType mailTabsType, final List<PlayerMailProp> mails) {
        PlayerMail.Player_Get_New_Mail_NTF.Builder builder = genGetNewMailNtfBuilder(mailTabsType, mails);
        this.getOwner().sendMsgToClient(MsgType.PLAYER_GETNEWMAIL_NTF, builder.build());
    }

    /**
     * 邮件是否不可删除
     *
     * @param mail 邮件
     * @return true=可删除
     */
    private boolean hasUnTakenRewards(MailMinInfo mail) {
        final MailTemplate mailTemplate = this.getMailTemplate(mail.getMailTemplateId());
        if (mailTemplate == null) {
            return false;
        }
        // 有奖励没领不能删
        return mail.getRewardState() == CommonEnum.MailRewardState.MAIL_REWARD_STATE_UNTAKEN && (!mail.getExtraItemRewards().isEmpty() || !mailTemplate.getAwardPairList().isEmpty());
    }

    private void fillExploreMailByGm(int count) {
        for (int i = 0; i < count; i++) {
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder().setMailTemplateId(80001);
            mailSendParams.getContentBuilder().getExploreDataBuilder().setConfigId(10000);
            mailSendParams.getTitleBuilder().getExtraNotifyDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_EXPLORE_REWARD, 10000));
            this.sendPersonalMail(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(getPlayerId())
                            .setZoneId(this.getOwner().getZoneId())
                            .build(),
                    mailSendParams.build());
        }
    }

    private void fillCollectionMailByGm(int count) {
        for (int i = 0; i < count; i++) {
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                    .setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstCollectTemplate.class).getCollectMailId());
            mailSendParams.getContentBuilder().getCollectDataBuilder()
                    .setLevel(1)
                    .setPoint(Struct.Point.newBuilder().setX(10000).setY(10000).build())
                    .setCurrencyType(CommonEnum.CurrencyType.OIL)
                    .setLastRefreshCollectStamp(SystemClock.now())
                    .setCount(123456)
                    .setExtraCount(123456);
            mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_RESOURCE, 100002));
            this.sendPersonalMail(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(getPlayerId())
                            .setZoneId(this.getOwner().getZoneId())
                            .build(),
                    mailSendParams.build());
        }
    }

    private void fillSpyMailByGm(int count) {
        for (int i = 0; i < count; i++) {
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                    .setMailTemplateId(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseSpySuccess());

            mailSendParams.getContentBuilder().getSpyDataBuilder().getCardHeadBuilder().setName("我是测试的玩家名").setPic(15010).setPicFrame(1);

            for (CommonEnum.SpyContentType techId : CommonEnum.SpyContentType.values()) {
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyTechBuilder().addDatas(techId.getNumber());
            }

            for (int j = 1; j <= 5; j++) {
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyCityDataBuilder().getResourcesBuilder().putDatas(i, Struct.Currency.newBuilder().setType(i).setCount(123456).build());
            }

            mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                    .getSpyDataBuilder().setClanBriefName("测试联盟")
                    .setPoint(Struct.Point.newBuilder().setX(10000).setY(10000))
                    .setCardHead(Struct.PlayerCardHead.newBuilder().setName("我是测试的玩家名").setPic(15010).setPicFrame(1).build())
                    .getSpyCityDataBuilder()
                    // 城墙等级、当前及最大hp，防御塔等级，当前及最大hp
                    .setWallLevel(1)
                    .setCurWallHp(123456)
                    .setMaxWallHp(123456);

            mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                    .getSpyDataBuilder().getSpyCityDataBuilder()
                    .setTowerLevel(1)
                    .setCurTowerHp(123456)
                    .setMaxTowerHp(123456);

            // 城防部队信息
            Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
            mainHeroBuilder.setHeroId(1408)
                    .setLevel(1)
                    .setStar(1);
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getHeroListBuilder().addDatas(mainHeroBuilder);
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getMainHeroIdListBuilder().addDatas(1408);

            Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
            deputyHeroBuilder.setHeroId(1408)
                    .setLevel(1)
                    .setStar(1);
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getHeroListBuilder().addDatas(deputyHeroBuilder);

            //士兵
            for (Integer soldierId : ResHolder.getResService(SoldierResService.class).allSoldierIdsPlayerCanGetForGm()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierId);
                soldierBuilder.setNum(123456);
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getSoldierListBuilder().addDatas(soldierBuilder);
            }

            mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayText("测试联盟"))
                    .addDatas(MsgHelper.buildDisPlayText("我是测试的玩家名"));

            this.sendPersonalMail(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(getPlayerId())
                            .setZoneId(this.getOwner().getZoneId())
                            .build(),
                    mailSendParams.build());
        }
    }

    private void fillBattleMailByGm(int count) {
        for (int i = 0; i < count; i++) {
            // 构建战斗数据
            long now = SystemClock.now();

            StructBattle.BattleRecordAll.Builder recordAll = StructBattle.BattleRecordAll.newBuilder()
                    .setRecordId(123456)
                    .setStartMillis(now)
                    .setEndMillis(now);

            for (int j = 0; j < 5; j++) {
                StructBattle.BattleRecordOne.Builder one = StructBattle.BattleRecordOne.newBuilder()
                        .setStartMillis(now)
                        .setEndMillis(now)
                        .setIsLeftRole(true)
                        .setIsAllLoss(false)
                        .setBattleId(123456);
                one.getLocationBuilder().setX(10000).setY(10000);
                one.getBattleLogIdListBuilder().addDatas(123456L);
                fillRole(one.getLeftRoleBuilder());
                fillRole(one.getRightRoleBuilder());

                recordAll.getSingleRecordListBuilder().addDatas(one);
            }

            recordAll.getSelfSummaryBuilder().setClanName("测试联盟").setTotal(123456).setLeftAlive(123456);
            recordAll.getSelfSummaryBuilder().getCardHeadBuilder().setName("我是测试的玩家名").setPic(15010).setPicFrame(1);
            for (int j = 1; j <= 60; j++) {
                recordAll.getRoundAliveListBuilder().addDatas(StructBattle.BattleRecordRoundAlive.newBuilder().setRound(j).setAlive(10000 + j));
            }
            for (int j = 1; j <= 60; j++) {
                StructBattle.BattleRecordRoundEvent.Builder battleRecordRoundEvent = StructBattle.BattleRecordRoundEvent.newBuilder().setRound(j);
                battleRecordRoundEvent.getTroopEventListBuilder().addDatas(StructBattle.BattleRecordTroopEvent.newBuilder().setClanName("测试联盟").setType(CommonEnum.BattleLogTroopChangeType.BLTCT_HOSPITAL).setSoldierNum(123456));
                recordAll.getRoundEventListBuilder().addDatas(battleRecordRoundEvent);
            }

            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                    .setMailTemplateId(ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getBattleReportMailId());

            // 构建邮件内容
            mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_BATTLE_RECORD_ALL);
            mailSendParams.getContentBuilder().setBattleRecord(recordAll);

            // title
            StructMail.MailShowTitle.Builder title = StructMail.MailShowTitle.newBuilder();
            ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
            title.setTitleKey(constTemplate.getBattleRecord6());
            String subTitleKey = constTemplate.getBattleRecordSub13();
            String clanName = "测试联盟";
            title.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
            title.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText("我是测试的玩家名"));
            title.setSubTitleKey(subTitleKey);
            mailSendParams.setTitle(title);

            // 填充title额外参数
            if (!recordAll.getSingleRecordList().getDatasList().isEmpty()) {
                // 主将信息
                Struct.DisplayParam.Builder heroParam = Struct.DisplayParam.newBuilder().setType(CommonEnum.DisplayParamType.DPT_HERO_ID).setNumber(1408);
                mailSendParams.getTitleBuilder().getExtraNotifyDataBuilder().getParamsBuilder().addDatas(heroParam);
            }

            this.sendPersonalMail(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(getPlayerId())
                            .setZoneId(this.getOwner().getZoneId())
                            .build(),
                    mailSendParams.build());
        }
    }

    private void fillRole(StructBattle.BattleRecordRole.Builder role) {
        role.setRoleId(getPlayerId())
                .setClanName("测试联盟")
                .setLostPower(123456)
                .setRoleType(CommonEnum.SceneObjType.SOT_ARMY)
                .setRoleId(getPlayerId());
        role.getLocationBuilder().setX(10000).setY(10000);
        role.getMainHeroBuilder().setHeroId(1408).setLevel(1).setAddExp(1).setStar(1);
        role.getDeputyHeroBuilder().setHeroId(1408).setLevel(1).setAddExp(1).setStar(1);
        role.getRewardBuilder().getRewardsBuilder().addDatas(StructBattle.DisplayReward.newBuilder().setId(11300001).setNum(1));
        role.getSoldierReportBuilder().setTotal(123456).setTreatment(123456).setDead(123456).setSevereWound(123456).setSlightWound(123456).setLeftAlive(123456);
        role.getCardHeadBuilder().setName("我是测试的玩家名").setPic(15010).setPicFrame(1);
        role.getPlunderBuilder().setResult(CommonEnum.PlunderResultEnum.PRE_PLUNDER);
        for (int i = 1; i <= 5; i++) {
            role.getPlunderBuilder().getResourcesBuilder().putDatas(i, Struct.Currency.newBuilder().setType(i).setCount(123456).build());
        }
    }

    /**
     * DB与内存数据转换封装
     */
    static class DbTransFormer {
        private static final int READ_BITS = 0;
        private static final int STORE_BITS = 1;
        /**
         * 64
         */
        private static final int READ_SIZE = (1 << READ_BITS);
        /**
         * 256
         */
        private static final int STORE_SIZE = (1 << STORE_BITS);

        static void fromMailStatus(int status, boolean readed, StructMailPB.PlayerMailPB.Builder mail) {
            mail.setStore(getIsStore(status));
            if (readed) {
                mail.setReadState(CommonEnum.MailReadState.MAIL_READ_STATE_READED);
            } else {
                mail.setReadState(getMailReadState(status));
            }

        }

        static void fromMailRewardStatus(int rewardStatus, StructMailPB.PlayerMailPB.Builder mail) {
            mail.setRewardState(CommonEnum.MailRewardState.forNumber(rewardStatus));
        }

        static void fromMailInviteStatus(int inviteStatus, StructMailPB.PlayerMailPB.Builder mail) {
            mail.setInviteState(CommonEnum.MailInviteState.forNumber(inviteStatus));
        }

        public static CommonMsg.MailBrief toMailBrief(final PlayerMailProp mail) {
            CommonMsg.MailBrief.Builder builder = CommonMsg.MailBrief.newBuilder();
            builder.setMailTemplateId(mail.getMailTemplateId());
            builder.setSender(mail.getSender().getCopySsBuilder());
            builder.setCreateTimestamp(mail.getCreateTimestamp());
            builder.setTitle(mail.getTitle().getCopySsBuilder());
            builder.addAllItemReward(mail.getItemReward().getCopyDbBuilder().getDatasList());
            builder.setLanguage(mail.getLanguage());
            builder.setMailType(mail.getMailType());
            builder.setOfflineExpiredTime(mail.getOfflineExpiredTime());
            builder.setCanReceive(mail.getCanReceive());
            builder.setReceiverCard(mail.getReceiverCard().getCopySsBuilder());
            builder.setIsIdIpMail(mail.getIsIdIpMail());

            return builder.build();
        }

        public static boolean getIsStore(int dbStatus) {
            return (dbStatus & STORE_SIZE) > 0;
        }

        public static CommonEnum.MailReadState getMailReadState(int dbStatus) {
            return (dbStatus & READ_SIZE) > 0 ? CommonEnum.MailReadState.MAIL_READ_STATE_READED : CommonEnum.MailReadState.MAIL_READ_STATE_UNREAD;
        }

        public static int toMailStatus(boolean isRead, boolean isStore) {
            int res = 0;
            if (isRead) {
                res |= DbTransFormer.READ_SIZE;
            }
            if (isStore) {
                res |= DbTransFormer.STORE_SIZE;
            }
            return res;
        }
    }

    /**
     * 邮件收藏逻辑封装
     * 收藏不存在页签变动，邮件仍属于原页签，玩家登录后构造并维护收藏数据
     */
    private class MailStoreHelper {
        private final Map<CommonEnum.MailTabsType, Integer> mailTabsStoredCnt = Maps.newHashMap();

        /**
         * 初始化构造，建立收藏邮件信息与各页签下收藏计数
         *
         * @param playerMails 玩家所有邮件
         */
        public void init(Map<Long, MailMinInfo> playerMails) {
            // 统计各页签下收藏邮件的数量
            for (MailMinInfo mail : playerMails.values()) {
                if (!mail.getIsStored()) {
                    continue;
                }
                mailTabsStoredCnt.put(getTemplateMailTabsType(mail.getMailId()), 1 + mailTabsStoredCnt.getOrDefault(getTemplateMailTabsType(mail.getMailId()), 0));
            }
        }

        /**
         * 添加收藏
         *
         * @param mailId 邮件id
         */
        public void addStore(long mailId) {
            MailMinInfo mail = getMail(mailId);
            checkStoreAble(mail);
            mail.setIsStored(true);
            mailTabsStoredCnt.put(getTemplateMailTabsType(mail.getMailId()), 1 + mailTabsStoredCnt.getOrDefault(getTemplateMailTabsType(mail.getMailId()), 0));
            PlayerMailComponent.this.dbUpdateStatus(mail);
        }

        public void deleteStore(MailMinInfo mail) {
            if (!mail.getIsStored()) {
                return;
            }
            mailTabsStoredCnt.put(getTemplateMailTabsType(mail.getMailId()), mailTabsStoredCnt.getOrDefault(getTemplateMailTabsType(mail.getMailId()), 1) - 1);
        }

        /**
         * 判断是否可收藏
         *
         * @param mail 邮件
         */
        private void checkStoreAble(final MailMinInfo mail) {
            // 邮件不存在
            if (mail == null) {
                throw new GeminiException(ErrorCode.MAIL_NOT_FOUND);
            }
            // 奖励未领取不允许收藏
            if (mail.getRewardState() == CommonEnum.MailRewardState.MAIL_REWARD_STATE_UNTAKEN) {
                throw new GeminiException(ErrorCode.MAIL_GET_REWARD_FIRST);
            }
            // 已经收藏
            if (mail.getIsStored()) {
                throw new GeminiException(ErrorCode.MAIL_ALREADY_STORE);
            }
            CommonEnum.MailTabsType mailTabsType = getTemplateMailTabsType(mail.getMailId());

            // 个人发送邮件不收藏(防止错误调用)
            if (mail.getIsSender()) {
                throw new GeminiException(ErrorCode.MAIL_NOT_STOREABLE);
            }
            MailExpireTemplate expireTemplate = getMailExpireTemplate(mailTabsType);
            LOGGER.info("PlayerMailComponent checkStoreAble mailTabsType={} limit={} curStore={}", mailTabsType, expireTemplate.getCollectMax(), mailTabsStoredCnt.getOrDefault(mailTabsType, 0));
            if (ServerContext.getServerDebugOption().isMailStoreFree()) {
                return;
            }
            // 已达到当前页签收藏上限
            if (mailTabsStoredCnt.getOrDefault(mailTabsType, 0) >= expireTemplate.getCollectMax()) {
                throw new GeminiException(ErrorCode.MAIL_OVER_STORE);
            }
        }
    }

}


class MailMinInfo {
    private final long mailId;
    private final long createTsMs;
    private final int templateId;
    private final List<Struct.ItemPair> extraItemRewards;
    private final long senderId;
    private final long offlineExpireTsMs;
    private final boolean isSender;
    private boolean isStored; //是否收藏
    private CommonEnum.MailReadState readState;
    private CommonEnum.MailRewardState rewardState;
    private CommonEnum.MailInviteState inviteState;
    private boolean canReceive;

    MailMinInfo(final TcaplusDb.PlayerMailTable.Builder dbData, final boolean readed) {
        this.mailId = dbData.getMailId();
        this.createTsMs = dbData.getHeader().getCreateTimestamp();
        this.templateId = dbData.getHeader().getMailTemplateId();
        this.senderId = dbData.getHeader().getSender().getSenderId();
        this.offlineExpireTsMs = dbData.getHeader().getOfflineExpiredTime();
        this.isSender = dbData.getHeader().getIsSender();

        this.readState = readed ? CommonEnum.MailReadState.MAIL_READ_STATE_READED : PlayerMailComponent.DbTransFormer.getMailReadState(dbData.getStatus());
        this.isStored = PlayerMailComponent.DbTransFormer.getIsStore(dbData.getStatus());
        this.rewardState = CommonEnum.MailRewardState.forNumber(dbData.getRewardStatus());
        this.inviteState = CommonEnum.MailInviteState.forNumber(dbData.getInviteStatus());


        this.extraItemRewards = new ArrayList<>(dbData.getHeader().getItemRewardList());
    }

    MailMinInfo(final PlayerMailProp mailProp, final boolean readed) {
        this.mailId = mailProp.getMailId();
        this.createTsMs = mailProp.getCreateTimestamp();
        this.templateId = mailProp.getMailTemplateId();
        this.senderId = mailProp.getSender().getSenderId();
        this.offlineExpireTsMs = mailProp.getOfflineExpiredTime();
        this.isSender = mailProp.getIsSender();
        this.readState = readed ? CommonEnum.MailReadState.MAIL_READ_STATE_READED : mailProp.getReadState();
        this.isStored = mailProp.getStore();
        this.rewardState = mailProp.getRewardState();
        this.inviteState = mailProp.getInviteState();


        this.extraItemRewards = new ArrayList<>(mailProp.getItemReward().getCopyDbBuilder().getDatasList());
    }

    @Override
    public String toString() {
        return "MailMinInfo{" +
                "mailId=" + mailId +
                ", createTsMs=" + createTsMs +
                ", templateId=" + templateId +
                ", senderId=" + senderId +
                ", offlineExpireTsMs=" + offlineExpireTsMs +
                ", isStored=" + isStored +
                ", readState=" + readState +
                ", rewardState=" + rewardState +
                ", inviteState=" + inviteState +
                ", canReceive=" + canReceive +
                ", isSender=" + isSender +
                '}';
    }

    public long getMailId() {
        return this.mailId;
    }

    public long getCreateTsMs() {
        return this.createTsMs;
    }

    public int getMailTemplateId() {
        return this.templateId;
    }

    public CommonEnum.MailReadState getReadState() {
        return this.readState;
    }

    public void setReadState(CommonEnum.MailReadState newReadState) {
        this.readState = newReadState;
    }

    public CommonEnum.MailRewardState getRewardState() {
        return this.rewardState;
    }

    public void setRewardState(CommonEnum.MailRewardState newRewardState) {
        this.rewardState = newRewardState;
    }

    public CommonEnum.MailInviteState getInviteState() {
        return this.inviteState;
    }

    public void setInviteState(CommonEnum.MailInviteState newInviteState) {
        this.inviteState = newInviteState;
    }

    public boolean getIsStored() {
        return this.isStored;
    }

    public void setIsStored(boolean isStored) {
        this.isStored = isStored;
    }

    public int formDbStatus() {
        return PlayerMailComponent.DbTransFormer.toMailStatus(this.readState == CommonEnum.MailReadState.MAIL_READ_STATE_READED, this.getIsStored());
    }

    public List<Struct.ItemPair> getExtraItemRewards() {
        return this.extraItemRewards;
    }

    public boolean getCanReceive() {
        return this.canReceive;
    }

    public void setCanReceive(boolean canReceive) {
        this.canReceive = canReceive;
    }

    public long getSenderId() {
        return this.senderId;
    }

    public long getOfflineExpireTsMs() {
        return this.offlineExpireTsMs;
    }

    public boolean getIsSender() {
        return this.isSender;
    }
}




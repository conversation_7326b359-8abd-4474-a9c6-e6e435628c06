package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;

import static com.yorha.common.enums.statistic.StatisticEnum.KILL_SPECIAL_MONSTER;

/**
 * 击杀特殊野怪
 * param1: 野怪id
 *
 * <AUTHOR>
 */
public class KillSpecialCreepsChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerKillBigSceneMonsterEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer param1 = taskParams.getFirst();
        int processConfig = 1;
        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int killProcess = (int) event.getPlayer().getStatisticComponent().getSecondStatistic(KILL_SPECIAL_MONSTER, param1);
            prop.setProcess(Math.min(killProcess, processConfig));
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= processConfig;
    }


}

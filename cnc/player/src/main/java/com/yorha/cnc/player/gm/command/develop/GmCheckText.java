package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsTextFilter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class GmCheckText implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GmCheckText.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int sceneId = Integer.parseInt(args.get("sceneId"));
        String text = args.get("text");

        SsTextFilter.CheckTextAns checkResult = actor.getEntity().syncCheckText(text, CommonEnum.UgcSceneId.forNumber(sceneId));
        LOGGER.info("GmCheckText [{}-{}] result: {} {}", sceneId, text, checkResult.getIsLegal(), checkResult.getFilteredText());
    }

    @Override
    public String showHelp() {
        return "GmCheckText text={value} sceneId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_NONE;
    }
}

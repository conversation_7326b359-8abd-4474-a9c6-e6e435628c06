package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.SoldierNumChangeEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.SsScenePlayer;
import res.template.SoldierTypeTemplate;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Map;

/**
 * 拥有某个兵种x个
 * param1:兵种类型（0代表任意兵种）
 * param2:数量
 *
 * <AUTHOR>
 */
public class SoldierTypeNumChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            SoldierNumChangeEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        Integer param1 = taskParams.get(0);
        Integer param2 = taskParams.get(1);

        int soldierNum = 0;
        Map<Integer, Integer> soldierId2Num;
        if (event instanceof SoldierNumChangeEvent) {
            soldierId2Num = ((SoldierNumChangeEvent) event).getSoldierType2Num();
        } else {
            PlayerEntity entity = event.getPlayer();
            // event为CheckTaskProcessEvent,需要向scene询问每个士兵类型的数量
            SsScenePlayer.GetAllSoldierAsk ask = SsScenePlayer.GetAllSoldierAsk.newBuilder().setPlayerId(entity.getPlayerId()).build();
            SsScenePlayer.GetAllSoldierAns ans = entity.ownerActor().callBigScene(ask);
            soldierId2Num = ans.getSoldierMap();
        }
        if (param1 == 0) {
            for (Integer num : soldierId2Num.values()) {
                soldierNum += num;
            }
        } else {
            for (Integer soldierId : soldierId2Num.keySet()) {
                SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
                if (soldierTemplate.getSoldierType() == param1) {
                    soldierNum += soldierId2Num.get(soldierId);
                }
            }
        }
        // 进度只增不减，若当前数量比历史最高值低，进度仍为历史最高值
        int process = Math.max(prop.getProcess(), soldierNum);
        prop.setProcess(Math.min(param2, process));

        return prop.getProcess() >= param2;
    }


}

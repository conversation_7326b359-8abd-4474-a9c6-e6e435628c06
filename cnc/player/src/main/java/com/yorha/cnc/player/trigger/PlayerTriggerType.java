package com.yorha.cnc.player.trigger;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.event.PlayerAllBattleEndEvent;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.PlayerTriggerHappenEvent;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.constant.Constants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.payment.PaymentResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.HeroRhTemplate;
import res.template.TriggerTypeTemplate;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 玩家触发机制
 * <p>
 * 现在只有糊脸礼包用
 */
public interface PlayerTriggerType {

    List<Class<? extends PlayerEvent>> followEvents();

    boolean triggerHappen(PlayerEvent event, TriggerTypeTemplate template);

    /**
     * 如果一次可以同时触发多个trigger，按照trigger的优先级从高到低排个序
     * <p>
     * 目前只有回天改名这个礼包用到，因为一个战报战损过多可以同时触发多个礼包，但需求的期望是只弹出一个，所以需要在触发的礼包中选一个优先级最高的
     * <p>
     * 但是现在这样处理，属于默认了一个event只会触发一个糊脸礼包，算是一个小小的潜规则
     */
    default void sort(List<TriggerTypeTemplate> happened) {
    }

    Map<Integer, PlayerTriggerType> TRIGGER_TYPE_MAP = ImmutableMap.of(
            1, new BuildLevelUp(),
            2, new SpecificQualityHeroLevelUp(),
            3, new SpecificQualityHeroUnlock(),
            4, new BattleLostTooMuch(),
            5, new SpecificQualityHeroAwaken()
    );

    class Initialization {
        private static final Logger LOGGER = LogManager.getLogger(PlayerTriggerType.class);

        static {
            TRIGGER_TYPE_MAP.forEach((typeIndex, tt) -> {
                for (Class<? extends PlayerEvent> followEventClazz : tt.followEvents()) {
                    if (followEventClazz.isAssignableFrom(PlayerTriggerHappenEvent.class)) {
                        throw new GeminiException("TriggerType may cause infinite loop! {}", tt.getClass().getName());
                    }
                    LOGGER.info("PlayerTriggerType init {} {} {}", typeIndex, tt.getClass().getSimpleName(), followEventClazz.getSimpleName());
                    EntityEventHandlerHolder.register(followEventClazz, event -> onEvent(tt, typeIndex, event));
                }
            });
        }
    }

    static PlayerTriggerType of(int index) {
        PlayerTriggerType tt = TRIGGER_TYPE_MAP.get(index);
        if (tt == null) {
            throw new GeminiException("TriggerType not found:{}", index);
        }
        return tt;
    }

    static <T extends PlayerEvent> void onEvent(PlayerTriggerType tt, int typeIndex, T event) {
        PaymentResService prs = ResHolder.getResService(PaymentResService.class);
        List<TriggerTypeTemplate> triggerTemplates = prs.getTriggerTemplates(typeIndex);
        List<TriggerTypeTemplate> happened = Lists.newLinkedList();
        for (TriggerTypeTemplate template : triggerTemplates) {
            try {
                if (tt.triggerHappen(event, template)) {
                    happened.add(template);
                }
            } catch (Exception e) {
                WechatLog.error("TriggerType onEvent error, event:{}", event, e);
            }
        }
        if (!happened.isEmpty()) {
            tt.sort(happened);
            // 事件转事件真有意思
            new PlayerTriggerHappenEvent(event.getPlayer(), happened.stream().map(TriggerTypeTemplate::getId).collect(Collectors.toList())).dispatch();
        }
    }

    /**
     * XX建筑达到XX等级
     */
    class BuildLevelUp implements PlayerTriggerType {

        @Override
        public List<Class<? extends PlayerEvent>> followEvents() {
            return ImmutableList.of(
                    PlayerInnerBuildLevelUpEvent.class,
                    PlayerInnerBuildCreateEvent.class
            );
        }

        @Override
        public boolean triggerHappen(PlayerEvent event, TriggerTypeTemplate template) {
            String[] array = template.getTriggerParam().split(Constants.XIA_HUA_XIAN);
            int confBuildId = Integer.parseInt(array[0]);
            int confLevel = Integer.parseInt(array[1]);
            int buildId = 0;
            int maxLevelBefore = 0;
            int maxLevelAfter = 1;
            if (event instanceof PlayerInnerBuildLevelUpEvent) {
                PlayerInnerBuildLevelUpEvent e = (PlayerInnerBuildLevelUpEvent) event;
                maxLevelBefore = e.getMaxLevelBefore();
                maxLevelAfter = e.getMaxLevelAfter();
                buildId = e.getBuildType();
            } else if (event instanceof PlayerInnerBuildCreateEvent) {
                PlayerInnerBuildCreateEvent e = (PlayerInnerBuildCreateEvent) event;
                maxLevelBefore = e.getMaxLevelBefore();
                maxLevelAfter = e.getMaxLevelAfter();
                buildId = e.getBuildType();
            }
            return buildId == confBuildId && maxLevelBefore < confLevel && maxLevelAfter >= confLevel;
        }

    }

    /**
     * 指定XX品质的任意英雄提升至XX等级
     */
    class SpecificQualityHeroLevelUp implements PlayerTriggerType {
        @Override
        public List<Class<? extends PlayerEvent>> followEvents() {
            return ImmutableList.of(
                    PlayerHeroLevelUpEvent.class
            );
        }

        @Override
        public boolean triggerHappen(PlayerEvent event, TriggerTypeTemplate template) {
            if (event instanceof PlayerHeroLevelUpEvent) {
                PlayerHeroLevelUpEvent theEvent = (PlayerHeroLevelUpEvent) event;
                HeroRhTemplate heroTemplate = ResHolder.getTemplate(HeroRhTemplate.class, theEvent.getHeroId());
                String[] array = template.getTriggerParam().split(Constants.XIA_HUA_XIAN);
                int confQuality = Integer.parseInt(array[0]);
                int confLevel = Integer.parseInt(array[1]);
                return heroTemplate.getRarity() == confQuality
                        && theEvent.getCurLevel() >= confLevel
                        && theEvent.getCurLevel() - theEvent.getUpLevelNum() < confLevel;
            }
            return false;
        }
    }

    /**
     * 指定XX品质的任意英雄解锁
     */
    class SpecificQualityHeroUnlock implements PlayerTriggerType {
        @Override
        public List<Class<? extends PlayerEvent>> followEvents() {
            return ImmutableList.of(
                    PlayerHeroUnLockEvent.class
            );
        }

        @Override
        public boolean triggerHappen(PlayerEvent event, TriggerTypeTemplate template) {
            if (event instanceof PlayerHeroUnLockEvent) {
                PlayerHeroUnLockEvent theEvent = (PlayerHeroUnLockEvent) event;
                HeroRhTemplate heroTemplate = ResHolder.getTemplate(HeroRhTemplate.class, theEvent.getHeroId());
                int confQuality = Integer.parseInt(template.getTriggerParam());
                String[] array = new String[]{};
                if (template.getTriggerSpecialParam() != null) {
                    array = template.getTriggerSpecialParam().trim().split(Constants.DOU_HAO);
                }

                Set<Integer> excludeHeroIds = Sets.newHashSet();
                for (String s : array) {
                    if (StringUtils.isNotEmpty(s)) {
                        excludeHeroIds.add(Integer.parseInt(s));
                    }
                }
                return heroTemplate.getRarity() == confQuality
                        && !excludeHeroIds.contains(theEvent.getHeroId());
            }
            return false;
        }
    }

    /**
     * 战报中士兵重伤数目达到一定数量
     */
    class BattleLostTooMuch implements PlayerTriggerType {
        @Override
        public List<Class<? extends PlayerEvent>> followEvents() {
            return ImmutableList.of(
                    PlayerAllBattleEndEvent.class
            );
        }

        @Override
        public boolean triggerHappen(PlayerEvent event, TriggerTypeTemplate template) {
            if (event instanceof PlayerAllBattleEndEvent) {
                return ((PlayerAllBattleEndEvent) event).getTotalLossPower() >= Integer.parseInt(template.getTriggerParam());
            }
            return false;
        }

        @Override
        public void sort(List<TriggerTypeTemplate> happened) {
            happened.sort(Comparator.<TriggerTypeTemplate>comparingInt(it -> Integer.parseInt(it.getTriggerParam()))
                    .reversed());
        }
    }


    /**
     * 指定XX品质的任意英雄觉醒
     */
    class SpecificQualityHeroAwaken implements PlayerTriggerType {
        @Override
        public List<Class<? extends PlayerEvent>> followEvents() {
            return ImmutableList.of(
                    PlayerHeroIntensiveEvent.class
            );
        }

        @Override
        public boolean triggerHappen(PlayerEvent event, TriggerTypeTemplate template) {
            if (event instanceof PlayerHeroIntensiveEvent) {
                PlayerHeroIntensiveEvent theEvent = (PlayerHeroIntensiveEvent) event;
                HeroRhTemplate heroTemplate = ResHolder.getTemplate(HeroRhTemplate.class, theEvent.getHeroId());
                int confQuality = Integer.parseInt(template.getTriggerParam());
                if (confQuality != heroTemplate.getRarity()) {
                    return false;
                }
                String[] array = new String[]{};
                if (template.getTriggerSpecialParam() != null) {
                    array = template.getTriggerSpecialParam().trim().split(Constants.DOU_HAO);
                }

                Set<Integer> excludeHeroIds = Sets.newHashSet();
                for (String s : array) {
                    if (StringUtils.isNotEmpty(s)) {
                        excludeHeroIds.add(Integer.parseInt(s));
                    }
                }
                return !excludeHeroIds.contains(theEvent.getHeroId());
            }
            return false;
        }
    }


}

package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.task.AbstractTaskHandler;
import com.yorha.cnc.player.task.ActivityTaskHandler;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivityTaskConf;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityTaskUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskDailyeventTemplate;

import java.util.HashSet;
import java.util.List;

import static com.yorha.cnc.player.task.AbstractTaskHandler.setNextTaskState;

/**
 * 由一组任务组成的活动单元
 * <p>
 * 想复用但是又有点逻辑不一样的，优先考虑另写，而不是在这里堆shit
 */
public class PlayerActivityTaskUnit extends BasePlayerActivityUnit {

    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityTaskUnit.class);

    protected final String taskPoolName;
    protected final List<Integer> confTaskIds;
    protected AbstractTaskHandler taskHandler;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_TASK, (owner, prop, template) -> new PlayerActivityTaskUnit(
                owner,
                prop.getCommonTaskUnit(),
                template.getTaskPoolName(),
                template.getActivityTaskIdsList())
        );
    }

    public PlayerActivityTaskUnit(PlayerActivity activity, ActivityUnitProp prop, String taskPoolName, List<Integer> activityTaskIdsList) {
        super(activity, prop);
        this.taskPoolName = taskPoolName;
        this.confTaskIds = activityTaskIdsList;
    }

    protected ActivityTaskUnitProp getTaskUnitProp() {
        return unitProp.getTaskUnit();
    }


    @Override
    public void load(boolean isInitial) {
        loadTasks();
    }

    @Override
    public void onMigrate() {

    }

    protected void loadTasks() {
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("activity clear attention");
        }
        ActivityTaskUnitProp taskUnitProp = getTaskUnitProp();
        taskHandler = new ActivityTaskHandler(ownerActivity.getPlayer(), taskUnitProp, taskPoolName, ownerActivity.getActivityId());
        taskHandler.openAttention();
        taskHandler.loadAllTask();
        // 这是一段特殊逻辑，针对task_dailyevent，如果任务已经接取就不管了。
        if ("task_dailyevent".equals(taskPoolName)) {
            specialForTaskDailyEvent();
        } else {
            // 新出现的task
            HashSet<Integer> newTaskIds = new HashSet<>(confTaskIds);
            newTaskIds.removeAll(taskUnitProp.getTasks().keySet());
            taskHandler.addBatchTask(newTaskIds);
            ownerActivity.getPlayer().getTaskComponent().addTaskHandler(taskHandler);
        }
    }

    private void specialForTaskDailyEvent() {
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("PlayerActivityTaskUnit specialForTaskDailyEvent failed server is not open");
            return;
        }
        if (!getTaskUnitProp().getTasks().isEmpty()) {
            // 已经接过任务了
            ownerActivity.getPlayer().getTaskComponent().addTaskHandler(taskHandler);
            return;
        }
        HashSet<Integer> newTaskIds = Sets.newHashSet();
        for (Integer confTaskId : confTaskIds) {
            TaskDailyeventTemplate template = ResHolder.findTemplate(TaskDailyeventTemplate.class, confTaskId);
            if (template == null) {
                LOGGER.error("PlayerActivityTaskUnit specialForTaskDailyEvent failed id not found in task_dailyevent id={}", confTaskId);
                continue;
            }
            final int playerCityLevel = ownerActivity.getPlayer().getCityLevel();
            if (!ActivityResService.isPlayerLevelOk(template.getRequiredPlayerLevelPairList(), playerCityLevel)) {
                continue;
            }
            final long zoneOpenDaysUntilNow = TimeUtils.getAbsNatureDaysBetween(ZoneContext.getServerOpenTsMs(), SystemClock.now());
            if (!ActivityResService.isServerOpenLimitOk(template.getServerOpenLimitPairList(), zoneOpenDaysUntilNow)) {
                continue;
            }
            newTaskIds.add(confTaskId);
        }
        if (!newTaskIds.isEmpty()) {
            taskHandler.addBatchTask(newTaskIds);
            ownerActivity.getPlayer().getTaskComponent().addTaskHandler(taskHandler);
            LOGGER.info("PlayerActivityTaskUnit specialForTaskDailyEvent activityId={} newTaskIds={}", ownerActivity.getActivityId(), newTaskIds);
        }
    }

    @Override
    public void onExpire() {
        LOGGER.info("taskUnit expire {}", ownerActivity);
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("activityExpire");
        }
    }

    @Override
    public void forceOffImpl() {
        LOGGER.info("taskUnit forceOffImpl {}", ownerActivity);
        if (taskHandler != null) {
            ownerActivity.getPlayer().getTaskComponent().removeTaskHandler(taskHandler);
            taskHandler.clearAllAttention("forceOffImpl");
        }
    }

    @Override
    public boolean isFinished() {
        return ImmutableList.copyOf(getTaskUnitProp().getTasks().values())
                .stream().allMatch(AbstractTaskHandler::isFinishState);
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final int taskId = key.getTaskId();
        Int32TaskInfoMapProp tasks = getTaskUnitProp().getTasks();
        TaskInfoProp taskInfoProp = tasks.get(taskId);
        if (taskInfoProp == null) {
            // 活动任务不存在
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_EXIST);
        }
        if (AbstractTaskHandler.isUnCompletedState(taskInfoProp)) {
            // 活动任务未完成
            throw new GeminiException(ErrorCode.ACTIVITY_TASK_NOT_COMPLATE);
        }

        // 标记领奖
        setNextTaskState(taskInfoProp);

        // 领奖
        ActivityTaskConf taskConf = ResHolder.getResService(ActivityResService.class).getTaskConf(taskPoolName, taskId);
        List<IntPairType> reward = taskConf.getReward();
        AssetPackage rewardPackage = AssetPackage.builder().plusItems(reward).build();
        ownerActivity.getPlayer().getAssetComponent().give(rewardPackage, CommonEnum.Reason.ICR_TASK, taskHandler.formationSubReason(taskId));
        Pair<String, Integer> rewardScore = taskConf.getRewardScore();
        if (rewardScore != null) {
            ownerActivity.getPlayer().getActivityComponent().addScore(
                    rewardScore.getFirst(),
                    rewardScore.getSecond(),
                    "complete_task_gain_score",
                    String.valueOf(taskId)
            );
        }
        if (taskConf.getRewardTriggerMailId() > 0) {
            sendTaskRewardMail(taskConf);
        }
        taskHandler.takeRewardQLog(taskInfoProp);
        rsp.setReward(rewardPackage.toPb());
        if (isFinished()) {
            onUnitFinished();
        }
    }

    private void sendTaskRewardMail(ActivityTaskConf taskConf) {
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(taskConf.getRewardTriggerMailId());
        final CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder()
                .setPlayerId(ownerActivity.getPlayer().getPlayerId())
                .setZoneId(ownerActivity.getPlayer().getZoneId())
                .build();
        player().getMailComponent().sendPersonalMail(receiver, params.build());
    }
}




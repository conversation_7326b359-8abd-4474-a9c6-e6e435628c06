package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.constant.DevBuffConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import res.template.BuffTemplate;
import res.template.ItemTemplate;


/**
 * 增益道具
 *
 * <AUTHOR>
 */

public class BuffItem extends AbstractUsableItem {

    public BuffItem(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        int buffId = getTemplate().getEffectId();
        BuffTemplate buffTemplate = DevBuffMgrBase.getBuffTemplate(buffId);
        if (buffTemplate == null) {
            throw new GeminiException(ErrorCode.BUFF_NO_CONFIG);
        }

        if (!playerEntity.getPlayerDevBuffComponent().canAddDevBuff(buffId)) {
            if (isPeaceShieldItem()) {
                throw new GeminiException(ErrorCode.BUFF_OPEN_PEACE_SHIELD_FAILED);
            } else {
                throw new GeminiException(ErrorCode.ITEM_USE_FAIL);
            }
        }
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        int buffId = template.getEffectId();
        if (isPeaceShieldItem()) {
            // 和平护盾
            playerEntity.getPeaceShieldComponent().openPeaceShield(buffId, null, CommonEnum.DevBuffSourceType.DBST_ITEM, DevBuffConstants.PeaceShieldReason.ITEM);
        } else {
            playerEntity.getPlayerDevBuffComponent().addDevBuff(buffId, CommonEnum.DevBuffSourceType.DBST_ITEM);
        }
        return true;
    }

    @Override
    public void responseMessage(PlayerCommon.Player_UseItem_S2C.Builder response) {
    }

    private boolean isPeaceShieldItem() {
        return DevBuffUtil.isPeaceShieldBuff(getTemplate().getEffectId());
    }
}

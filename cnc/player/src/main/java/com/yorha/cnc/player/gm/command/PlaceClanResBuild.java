package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerSceneClan;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 模拟客户端发来放置军团资源中心的消息
 *
 * <AUTHOR>
 */
public class PlaceClanResBuild implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(PlaceClanResBuild.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int x = Integer.MAX_VALUE;
        int y = Integer.MAX_VALUE;
        if (args.get("x") != null) {
            x = Integer.parseInt(args.get("x"));
        }
        if (args.get("y") != null) {
            y = Integer.parseInt(args.get("y"));
        }
        if (x == Integer.MAX_VALUE || y == Integer.MAX_VALUE) {
            LOGGER.info("x or y not exist");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CommonEnum.MapBuildingType type = CommonEnum.MapBuildingType.forNumber(Integer.parseInt(args.get("type")));
        if (type == null) {
            LOGGER.info("type not exist");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        switch (type) {
            case MBT_CLAN_RES_OIL:
            case MBT_CLAN_RES_STEEL:
            case MBT_CLAN_RES_RARE_EARTH:
            case MBT_CLAN_RES_TIBERIUM:
                break;
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        StructPB.PointPB.Builder pointPB = StructPB.PointPB.newBuilder().setX(x).setY(y);
        PlayerSceneClan.Player_ConstructClanResBuilding_C2S c2sMsg =
                PlayerSceneClan.Player_ConstructClanResBuilding_C2S.newBuilder()
                        .setP(pointPB).setType(type).build();
        // 为null就抛错下去
        PlayerEntity playerEntity = actor.getEntity();
        playerEntity.getPlayerClanComponent().tryPlaceClanResBuild(c2sMsg);
    }

    @Override
    public String showHelp() {
        return "PlaceClanResBuild x={} y={} type={}";
    }
}

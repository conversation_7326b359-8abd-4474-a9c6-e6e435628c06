package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 一键塞满内存
 *
 * <AUTHOR>
 */
public class FullMemory implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(FullMemory.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity playerEntity = actor.getEntity();

        try {
            // 塞满活动
            playerEntity.getActivityComponent().fullActivityByGm();
        } catch (Exception e) {
            LOGGER.error("FullMemory error", e);
        }

        try {
            // 塞满加成
            playerEntity.getAddComponent().fullAdditionByGm();
        } catch (Exception e) {
            LOGGER.error("FullMemory error", e);
        }

        try {
            // 塞满任务
            playerEntity.getTaskComponent().fullTaskByGm();
        } catch (Exception e) {
            LOGGER.error("FullMemory error", e);
        }

        try {
            // 塞满邮件
            playerEntity.getMailComponent().fullMailByGm();
        } catch (Exception e) {
            LOGGER.error("FullMemory error", e);
        }

    }

    @Override
    public String showHelp() {
        return "FullMemory";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

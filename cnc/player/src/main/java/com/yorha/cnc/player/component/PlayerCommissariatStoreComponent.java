package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.PlayerDayRefreshEvent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.qlog.json.item.QlogItemConfig;
import com.yorha.common.qlog.json.money.QlogMoneyConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.game.gen.prop.PlayerStoreModelProp;
import com.yorha.game.gen.prop.ShopBuyInfoProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ShopTemplate;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yorha.proto.CommonEnum.SPassWordCheckType.SPWC_MANY_MONEY;

/**
 * <AUTHOR>
 * <p>
 * 军需处商店管理组件
 */
public class PlayerCommissariatStoreComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCommissariatStoreComponent.class);

    static {
        EntityEventHandlerHolder.register(PlayerDayRefreshEvent.class, PlayerCommissariatStoreComponent::refreshLimitWithServer);
    }

    public PlayerCommissariatStoreComponent(PlayerEntity owner) {
        super(owner);
    }

    /**
     * @return 返回客户端需要的商店信息
     */
    public PlayerStore.Player_FetchCommissariatStoreInfo_S2C.Builder getStoreInfo() {
        PlayerStore.Player_FetchCommissariatStoreInfo_S2C.Builder retBuilder = PlayerStore.Player_FetchCommissariatStoreInfo_S2C.newBuilder();
        Collection<ShopTemplate> itemLists = ResHolder.getInstance().getListFromMap(ShopTemplate.class);
        long now = SystemClock.now();
        for (ShopTemplate itemTemplate : itemLists) {
            long startTime = 0L;
            long endTime = 0L;
            // 获取上架时间
            if (null != itemTemplate.getStartTime()) {
                startTime = TimeUtils.string2TimeStampMs(itemTemplate.getStartTime());
            }
            // 获取下架时间
            if (null != itemTemplate.getOffTime()) {
                endTime = TimeUtils.string2TimeStampMs(itemTemplate.getOffTime());
            }
            // 不在架商品忽略
            if (!isOnShelf(startTime, endTime, now)) {
                continue;
            }
            retBuilder.addInfo(transConfigItemToClientItem(itemTemplate, startTime, endTime));
        }
        return retBuilder;
    }

    /**
     * 通过配置id和数目购买并使用道具
     *
     * @param id     配置id
     * @param buyNum 数目
     * @return 返回客户端需要的商店信息
     */
    public PlayerStore.Player_BuyAndUseStoreItem_S2C.Builder buyAndUseItemById(long id, int buyNum, PlayerPB.ItemUseParamsPB paramsPB) {
        int leftCanBuyNum = buyItemById(id, buyNum, paramsPB.getSPassWord());
        PlayerStore.Player_BuyAndUseStoreItem_S2C.Builder retBuilder = PlayerStore.Player_BuyAndUseStoreItem_S2C.newBuilder();
        retBuilder.setLeftCanBuyNum(leftCanBuyNum);

        ShopTemplate template = getShopItemTemplate((int) id);
        if (null == template) {
            LOGGER.error("buy success while use failed for {}, {}", id, buyNum);
        } else {
            if (paramsPB.getQueueType() != CommonEnum.QueueTaskType.DEFAULT_NONE) {
                // 加速逻辑，
                List<IntPairType> collect = Stream.of(IntPairType.makePair(template.getItemID(), buyNum)).collect(Collectors.toList());
                getOwner().getPlayerQueueTaskComponent().speedUpQueueTask(paramsPB.getQueueType(), paramsPB.getTaskId(), collect);
            } else {
                // 正常使用逻辑
                ItemUseParamsProp paramsProp = new ItemUseParamsProp();
                paramsProp.mergeFromCs(paramsPB);
                PlayerCommon.Player_UseItem_S2C.Builder response = getOwner().getItemComponent()
                        .useItemByTemplateId(template.getItemID(), buyNum, paramsProp);
                retBuilder.setResult(response.getResult());
            }
        }
        return retBuilder;
    }

    /**
     * 通过配置id和数目购买道具
     *
     * @param id     配置id
     * @param buyNum 数目
     * @return 返回剩余可购买的道具数目，如果没有配置限购将返回0
     */
    public int buyItemById(long id, int buyNum, String sPassWord) {
        int configItemId = (int) id;
        ShopTemplate template = getShopItemTemplate(configItemId);
        if (null == template) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        long moneyNum = MathUtils.multiplyExact(template.getPricePair().getValue(), buyNum);
        getOwner().getSettingComponent().checkSpassword(SPWC_MANY_MONEY, moneyNum, sPassWord);

        // 检查资源是否足够并扣除
        AssetPackage cost = AssetPackage.builder().plusCurrency(CommonEnum.CurrencyType.DIAMOND_VALUE, moneyNum).build();
        getOwner().verifyThrow(cost);


        getOwner().consume(cost, CommonEnum.Reason.ICR_SHOPPING, "arms_dealer");
        
        // 发购买的道具
        AssetPackage itemPackage = AssetPackage.builder().plusItem(template.getItemID(), buyNum).build();
        getOwner().getAssetComponent().give(itemPackage, CommonEnum.Reason.ICR_SHOPPING, "arms_dealer");

        // 如果有赠送道具，发赠送的道具
        IntPairType extraItemPair = template.getExtraItemPair();
        if (extraItemPair != null) {
            AssetPackage extraItemPackage = AssetPackage.builder().plusItem(extraItemPair.getKey(), (long) extraItemPair.getValue() * buyNum).build();
            getOwner().getAssetComponent().give(extraItemPackage, CommonEnum.Reason.ICR_SHOPPING, "arms_dealer");
        }
        getOwner().getQlogComponent().sendShopQLog("arms_dealer", "shop_purchase",
                QlogMoneyConfig.getQlogMoneyConfigStr(CommonEnum.CurrencyType.DIAMOND_VALUE, (int) moneyNum),
                QlogItemConfig.getQlogItemConfigStr(template.getItemID(), buyNum));

        // 客户端无限购表现需求，直接返回0不限购
        return 0;
    }

    /**
     * 将配置的商品信息转换成发给客户端的商品信息
     *
     * @param template    单个商品的配置信息
     * @param startTimeMs 商品上架的时间戳，可能为0
     * @param endTimeMs   商品下架的时间戳，可能为0
     * @return 返回发给客户端的商品信息builder
     */
    private StructMsg.StoreInfo.Builder transConfigItemToClientItem(ShopTemplate template, long startTimeMs, long endTimeMs) {
        StructMsg.StoreInfo.Builder builder = StructMsg.StoreInfo.newBuilder();
        // 直接从template中获取
        builder.setStoreType(CommonEnum.StoreType.STORE_TYPE_COMMISSARIAT)
                .setConfigGoodsId(template.getId())
                .setRefreshTime(template.getRefreshTime())
                .setOnShelfTime(startTimeMs)
                .setOffShelfTime(endTimeMs)
                .setMoneyType(template.getPricePair().getKey())
                .setMoneyNum(template.getPricePair().getValue());

        // 根据玩家身上已买过的限购商品信息设置可买数目
        ShopBuyInfoProp shopBuyInfoProp = getProp().getCommissariatShopBuyInfoV(template.getId());
        if (null != shopBuyInfoProp) {
            refreshLimitBuy(template.getRefreshTime(), template.getId(), startTimeMs);
            builder.setLeftCanBuyNum(template.getBuyLimit() - shopBuyInfoProp.getBuyCount());
        } else {
            builder.setLeftCanBuyNum(template.getBuyLimit());
        }
        // 如果有额外赠送的商品设置到builder上
        if (template.getExtraItemPair() != null) {
            builder.setExtraItemId(template.getExtraItemPair().getKey())
                    .setExtraNum(template.getExtraItemPair().getValue());
        }
        return builder;
    }

    /**
     * 被动刷新商品的限购次数
     *
     * @param refreshInterval 刷新间隔（s)
     * @param configItemId    配置的itemId
     * @param onShelfTimeMs   商品配置的上架时间
     */
    public void refreshLimitBuy(int refreshInterval, int configItemId, long onShelfTimeMs) {
        // 刷新间隔未配置，认为不需要刷新
        if (refreshInterval == 0) {
            return;
        }
        // 有购买记录才刷新
        ShopBuyInfoProp shopBuyInfoProp = getProp().getCommissariatShopBuyInfoV(configItemId);
        if (null != shopBuyInfoProp) {
            long lastRefreshTimeMs = shopBuyInfoProp.getLastRefreshTime();
            long startRefreshTimeMs = shopBuyInfoProp.getStartTime();
            long now = SystemClock.now();
            if (startRefreshTimeMs != onShelfTimeMs) {
                // 上架时间不同认为是不一样的商品
                startRefreshTimeMs = onShelfTimeMs;
                shopBuyInfoProp.setBuyCount(0);
            } else {
                // 已过刷新时间（除法分区），需要刷新
                long refreshIntervalMs = TimeUtils.second2Ms(refreshInterval);
                if ((lastRefreshTimeMs - startRefreshTimeMs) / refreshIntervalMs != (now - startRefreshTimeMs) / refreshIntervalMs) {
                    shopBuyInfoProp.setBuyCount(0);
                }
            }
            shopBuyInfoProp.setStartTime(startRefreshTimeMs);
            shopBuyInfoProp.setLastRefreshTime(now);
        }
    }

    /**
     * 返回商品此时是否应该在架上
     * 上下架时间戳均可以配置0，代表是否需要在意上下架时间。
     *
     * @param startTimeMs 上架的时间戳
     * @param offTimeMs   下架的时间戳
     * @param nowMs       当前的时间戳
     * @return 商品在架返回true，商品不在架返回false
     */
    private boolean isOnShelf(long startTimeMs, long offTimeMs, long nowMs) {
        if (startTimeMs == 0 && nowMs < offTimeMs) {
            // 上架时间未配置，当前时间未到下架时间
            return true;
        } else if (startTimeMs <= nowMs && offTimeMs == 0) {
            // 下架时间未配置，当前时间已过上架时间；注意，隐含了上下架时间都未配置的情况
            return true;
        } else {
            // 上下架时间均配置，判断当前时间是否在[上架时间，下架时间)区间内
            return startTimeMs != 0 && offTimeMs != 0 && nowMs >= startTimeMs && nowMs < offTimeMs;
        }
    }

//    /**
//     * 刷新玩家身上的限购信息，检查玩家期望购买数量是否符合限购信息，将本次购买记录在限购信息内
//     *
//     * @param template 道具配置
//     * @param buyNum   玩家期望购买的数目
//     * @return 返回扣除本次购买后，仍可以购买的数目
//     * @throws GeminiException 限购数量小于要购买数量时抛出
//     */
//    private int refreshAndCheckBuyLimitInfo(ShopTemplate template, int buyNum) throws GeminiException {
//        long onShelfTimeMs = TimeUtils.string2TimeStampMs(template.getStartTime());
//        // 被动刷新玩家限购信息
//        getOwner().getCommStoreComponent().refreshLimitBuy(template.getRefreshTime(), template.getId(), onShelfTimeMs);
//
//        // 查询玩家身上的限购信息
//        int totalCanBuyNum = template.getBuyLimit();
//        int leftCanBuyNum = totalCanBuyNum, alreadyBuyCount = 0;
//        ShopBuyInfoProp prop = getShopBuyInfoByConfigId(template.getId());
//        if (null != prop) {
//            alreadyBuyCount = prop.getBuyCount();
//            leftCanBuyNum = totalCanBuyNum - alreadyBuyCount;
//        }
//        if (leftCanBuyNum < buyNum) {
//            throw new GeminiException(ErrorCode.STORE_EXCEED_BUY_LIMIT.getCodeId());
//        }
//        alreadyBuyCount += buyNum;
//        leftCanBuyNum -= buyNum;
//        // 重新设置玩家身上的限购信息
//        putNewShopBuyInfo(template.getId(), alreadyBuyCount, onShelfTimeMs);
//        return leftCanBuyNum;
//    }

    /**
     * @param configId 配置id
     * @return 返回对应的商品配置，如果商品配置不存在，会返回null，需要上层自行处理
     */
    @Nullable
    private ShopTemplate getShopItemTemplate(int configId) {
        return ResHolder.getInstance().findValueFromMap(ShopTemplate.class, configId);
    }

//    /**
//     * @param configId 配置id
//     * @return 返回对应的商品的prop，可能为null，需要上层自行处理
//     */
//    @Nullable
//    private ShopBuyInfoProp getShopBuyInfoByConfigId(int configId) {
//        return getProp().getCommissariatShopBuyInfoV(configId);
//    }

//    /**
//     * 将新的prop设置到玩家身上
//     *  @param configId        配置id
//     * @param alreadyBuyCount 已经购买的商品数量
//     * @param onShelfTimeMs   商品上架的时间戳
//     */
//    private void putNewShopBuyInfo(int configId, int alreadyBuyCount, long onShelfTimeMs) {
//        ShopBuyInfoProp newProp = new ShopBuyInfoProp();
//        newProp.setId(configId);
//        newProp.setBuyCount(alreadyBuyCount);
//        newProp.setLastRefreshTime(SystemClock.now());
//        newProp.setStartTime(onShelfTimeMs);
//        getProp().putCommissariatShopBuyInfoV(newProp);
//    }

    private PlayerStoreModelProp getProp() {
        return getOwner().getProp().getPlayerStoreModel();
    }


    /**
     * 日刷清理服务器限购
     */
    public void clearLimitWithServer() {
        LOGGER.info("clearLimitWithServer on dayRefresh");
        for (ShopBuyInfoProp value : getProp().getCommissariatShopBuyInfo().values()) {
            value.setBuyCountWithDay(0);
        }
    }

    /**
     * 纯服务器的限购数量（根据主城等级，日刷）
     */
    private static void refreshLimitWithServer(PlayerDayRefreshEvent event) {
        event.getPlayer().getCommStoreComponent().clearLimitWithServer();
    }
}

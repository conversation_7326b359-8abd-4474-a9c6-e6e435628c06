package com.yorha.cnc.player.gm.command.chat;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 设置聊天是否开启敏感词过滤，默认为true
 * <AUTHOR>
 */
public class SetTextNoFilter implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        boolean textFilter = true;
        if (args.containsKey("textFilter")) {
            textFilter = Boolean.parseBoolean(args.get("textFilter"));
        }
        ServerContext.getServerDebugOption().setIsTextFilter(textFilter);
    }

    @Override
    public String showHelp() {
        return "SetTextNoFilter textFilter={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

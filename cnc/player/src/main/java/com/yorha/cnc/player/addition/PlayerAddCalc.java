package com.yorha.cnc.player.addition;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerHeroComponent;
import com.yorha.cnc.player.enums.BuildResourceMapType;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.FormulaUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import res.template.ClanScoreTemplate;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;
import res.template.TechSubidTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.yorha.proto.CommonEnum.BuffEffectType;
import static com.yorha.proto.CommonEnum.SoldierType;

/**
 * 加成公式计算器
 * <p>
 * player所有加成相关计算公式
 *
 * <AUTHOR>
 */
public class PlayerAddCalc {

    /**
     * 获取单次X兵种造兵/晋升数量上限
     * TIPS:单次X兵种训练/晋升数量 = X兵种造兵数量固定值 + ∑全兵种训练数量增加固定值
     *
     * @param playerEntity    玩家类型
     * @param soldierTemplate 兵种配置
     * @return 单次X兵种造兵/晋升数量上限
     */
    public static int getTrainAndLevelUpLimit(PlayerEntity playerEntity, SoldierTypeTemplate soldierTemplate, long upTrain) {
        BuffEffectType fixedType = AdditionConstants.SOLDIER_TYPE_TRAIN_EFFECT_LIMIT_TYPE_MAP.get(SoldierType.forNumber(soldierTemplate.getSoldierType()));
        long baseLimit = playerEntity.getAddComponent().getAddition(fixedType);
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TRAINING_OR_LEVELUP_LIMIT_UP_FIXED);
        return (int) FormulaUtils.f1(baseLimit, addition + upTrain, 0, 1);
    }

    /**
     * 军队容量加成可能来自于机甲或者玩家本身的加成
     *
     * @param playerEntity 玩家实体
     * @return 获取玩家军队容量加成
     */
    public static Map<CommonEnum.BuffEffectType, Long> getPlayerTroopCapAddition(PlayerEntity playerEntity) {
        HashMap<CommonEnum.BuffEffectType, Long> result = Maps.newHashMap();
        result.put(CommonEnum.BuffEffectType.ET_TROOP_CAP_FIXED, playerEntity.getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_TROOP_CAP_FIXED));
        result.put(CommonEnum.BuffEffectType.ET_TROOP_CAPACITY_PERCENT, playerEntity.getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_TROOP_CAPACITY_PERCENT));
        return result;
    }

    /**
     * 获取加成后的真实科研消耗时间
     * TIPS:科研时间 = 配表时间 / (1 + ∑科研速度增加百分比)
     *
     * @param playerEntity      玩家实体
     * @param techSubIdTemplate 科研配置
     * @return 真实科研消耗时间（毫秒）
     */
    public static long getTechCostTimeMs(PlayerEntity playerEntity, TechSubidTemplate techSubIdTemplate) {
        // 基础科研时间
        long configTimeMs = TimeUnit.SECONDS.toMillis(techSubIdTemplate.getTiming());
        // 科研耗时减少增幅万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TECH_SPEED);
        return FormulaUtils.f2(configTimeMs, 0, addition, 1);
    }


    /**
     * 获取加成后的真实科研消耗资源列表
     * TIPS:资源消耗 = 配表资源消耗 * （ 1 - ∑科研资源消耗降低百分比）
     *
     * @param playerEntity      玩家实体
     * @param techSubIdTemplate 科技配置
     * @return 真实城建消耗资源列表
     */
    public static List<IntPairType> getTechCostResourcesPairList(PlayerEntity playerEntity, TechSubidTemplate techSubIdTemplate) {
        // 科技资源消耗降低万分比
        BuffEffectType etInnerBuildingCostDecPer = BuffEffectType.ET_TECH_COST_DEC_PER;
        long addition = playerEntity.getAddComponent().getAddition(etInnerBuildingCostDecPer);
        // 真实资源消耗列表
        List<IntPairType> realTechCostResourcesPairList = Lists.newArrayList();
        // 对原始列表中每个Pair的value使用f3函数
        for (IntPairType intPairType : techSubIdTemplate.getCostResourcesPairList()) {
            realTechCostResourcesPairList.add(IntPairType.makePair(intPairType.getKey(), (int) FormulaUtils.f3(intPairType.getValue(), 0, addition, 1)));
        }
        return realTechCostResourcesPairList;
    }

    /**
     * 获取加成后的真实兵种晋级消耗时间
     * TIPS：单个晋级时间 = （配表目标T级单个造兵时间 - 配表当前T级单个造兵时间） / (1 + ∑训练速度增加百分比)
     *
     * @param playerEntity        玩家实体
     * @param lowSoldierTemplate  低等级士兵配置
     * @param highSoldierTemplate 高等级士兵配置
     * @param num                 晋升数量
     * @return 真实兵种晋级消耗时间（毫秒）
     */
    public static long getSoldierLevelUpCostTime(PlayerEntity playerEntity, SoldierTypeTemplate lowSoldierTemplate, SoldierTypeTemplate highSoldierTemplate, Integer num) {
        // 单个高级兵种基础造兵时间
        long highConfigTimeMs = highSoldierTemplate.getTrainTimeMs();
        // 单个低级兵种基础造兵时间
        long lowConfigTimeMs = lowSoldierTemplate.getTrainTimeMs();
        // 晋升增幅万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TRAINING_SOLIDER_SPEED);
        if (highConfigTimeMs >= lowConfigTimeMs) {
            return FormulaUtils.f2(highConfigTimeMs, lowConfigTimeMs, addition, num);
        } else {
            return 0L;
        }
    }

    /**
     * 获取加成后的真实兵种晋级消耗资源
     *
     * @param playerEntity    玩家实体
     * @param highIntPairType 高等级士兵资源种类和消耗数量
     * @param lowIntPairType  低等级士兵资源种类和消耗数量
     * @param num             晋升数量
     * @return 真实兵种晋级消耗资源
     */
    public static long getSoldierLevelUpCostValue(PlayerEntity playerEntity, IntPairType highIntPairType, IntPairType lowIntPairType, Integer num) {
        // 单个高级兵种基础造兵资源
        long highConfigValue = highIntPairType.getValue();
        // 单个低级兵种基础造兵资源
        long lowConfigValue = lowIntPairType.getValue();
        // 晋升资源消耗降低万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TRAINING_SOLIDER_COST_DEC_PER);
        if (highConfigValue >= lowConfigValue) {
            return FormulaUtils.f4(highConfigValue, lowConfigValue, addition, num);
        } else {
            return 0L;
        }
    }

    /**
     * 获取加成后的真实兵种晋级消耗资源
     *
     * @param playerEntity    玩家实体
     * @param highIntPairType 高等级士兵资源种类和消耗数量
     * @param num             晋升数量
     * @return 真实兵种晋级消耗资源
     */
    public static long getSoldierLevelUpCostValue(PlayerEntity playerEntity, IntPairType highIntPairType, Integer num) {
        // 单个高级兵种基础造兵资源
        long highConfigValue = highIntPairType.getValue();
        // 晋升资源消耗降低万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TRAINING_SOLIDER_COST_DEC_PER);
        return FormulaUtils.f4(highConfigValue, 0, addition, num);

    }

    /**
     * 获取加成后的真实造兵消耗时间
     * TIPS:单兵种单个造兵时间 = 配表Tx单兵种单个造兵时间 / (1 + ∑训练速度增加百分比)
     *
     * @param playerEntity    玩家实体
     * @param soldierTemplate 士兵配置
     * @param num             建造士兵数量
     * @return 真实造兵消耗时间(毫秒)
     */
    public static long getSoldierTrainCostTime(PlayerEntity playerEntity, SoldierTypeTemplate soldierTemplate, Integer num) {
        // 单兵种单个基础造兵时间
        long configTimeMs = soldierTemplate.getTrainTimeMs();
        // 造兵增幅万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TRAINING_SOLIDER_SPEED);
        return FormulaUtils.f2(configTimeMs, 0, addition, num);
    }

    /**
     * 获取加成后的真实造兵消耗资源
     * TIPS: 单兵种单个造兵资源消耗 = 配表单兵种单个造兵资源消耗* （ 1 - ∑造兵资源消耗降低百分比）
     * 总造兵资源消耗 = 单兵种单个造兵资源消耗 * 总造兵数量
     *
     * @param playerEntity 玩家实体
     * @param rssType2num  IntPairType类型,存储资源种类和该资源的消耗数量
     * @param num          造兵数量
     * @return 真实造兵消耗资源
     */
    public static long getSoldierTrainCostValue(PlayerEntity playerEntity, IntPairType rssType2num, Integer num) {
        // 单兵基础建造资源消耗
        long baseTrainCostValue = rssType2num.getValue();
        // 资源消耗降低万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TRAINING_SOLIDER_COST_DEC_PER);
        return FormulaUtils.f3(baseTrainCostValue, 0, addition, num);
    }

    /**
     * 获取加成后的真实治疗单兵种的消耗时间
     * TIPS:单兵种单个维修时间 =配表Tx单兵种单个维修时间/ (1 + ∑维修速度增加百分比)
     *
     * @param playerEntity    玩家实体
     * @param soldierTemplate 兵种配置
     * @param num             治疗士兵数量
     * @return 真实治疗单兵种的消耗时间（毫秒）
     */
    public static long getSoldierTreatCostTime(PlayerEntity playerEntity, SoldierTypeTemplate soldierTemplate, Integer num) {
        // 单兵种单个基础治疗时间
        long configTimeMs = soldierTemplate.getTreatTimeMillis();
        // 治疗增幅万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TREATMENT_SPEED);
        return FormulaUtils.f2(configTimeMs, 0, addition, num);
    }

    /**
     * 获取加成后的真实治疗单兵种的消耗资源
     * TIPS:单兵种单个造兵资源消耗 = 配表Tx单兵种单个造兵资源消耗* （ 1 - ∑造兵资源消耗降低百分比）
     *
     * @param playerEntity 玩家实体
     * @param rssType2num  IntPairType类型,存储资源种类和该资源的消耗数量
     * @param num          治疗士兵数量
     * @return 真实治疗单兵种的消耗资源
     */
    public static long getSoldierTreatCostValue(PlayerEntity playerEntity, IntPairType rssType2num, Integer num) {
        // 单兵基础治疗资源消耗
        long baseTreatCostValue = rssType2num.getValue();
        // 资源消耗减少万分比
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_TREATMENT_COST_DEC);
        return FormulaUtils.f3(baseTreatCostValue, 0, addition, num);
    }

    /**
     * 获取每秒产量
     */
    public static double getProduceRatePerHour(PlayerEntity playerEntity, BuildResourceMapType type, boolean calcAllResourceAddition) {
        // 每小时基础产量
        long baseProduce = playerEntity.getAddComponent().getAddition(type.getBaseProduce());
        // 产量增幅万分比
        long singleAddition = playerEntity.getAddComponent().getAddition(type.getRateProduce());
        // 全资源产量增幅万分比
        long allAddition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_ALL_RATE_BUFF);

        long totalAddition;

        if (calcAllResourceAddition) {
            totalAddition = singleAddition + allAddition;
        } else {
            totalAddition = singleAddition;
        }
        // 每小时产量 = ∑产量固定值 * （1 + ∑增产万分比）
        // 公式需要更新
        return FormulaUtils.f1(0, baseProduce, totalAddition, 1);
    }

    /**
     * 获取资源保护量
     */
    public static long getResourceProtect(PlayerEntity playerEntity, BuildResourceMapType type) {
        // 全资源保护增幅
        long additionValue = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_RESOURCE_PROTECT_PERCENT);
        long baseProtect = playerEntity.getAddComponent().getAddition(type.getProtectBuff());
        // 资源保护量 = 仓库等级对应配表资源保护量 * (1 + ∑资源保护量增加万分比)
        return FormulaUtils.f1(0, baseProtect, additionValue, 1);
    }

    /**
     * 体力自然恢复上限
     */
    public static long getEnergyRecoverMax(PlayerEntity playerEntity) {
        int energyRecoverMax = ResHolder.getResService(ConstKVResService.class).getTemplate().getPlayerEnergyRecoverMax();
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_ENERGY_MAX_FIXED);
        // 体力上限 = 基础体力上限 + ∑体力上限提升
        return FormulaUtils.f1(energyRecoverMax, addition, 0, 1);
    }

    /**
     * 体力恢复速度(恢复一点体力的间隔毫秒数)
     */
    public static long getEnergyRecoverIntervalMs(PlayerEntity playerEntity) {
        int baseRecover = ResHolder.getResService(ConstKVResService.class).getTemplate().getPlayerEneryRecoverTimePeriod();
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_ENERGY_RECOVER_SPEED_PERCENT);
        // 恢复1点体力的时间 = 体力基础恢复时间 / ( 1 + ∑体力恢复速度提升百分比）
        return FormulaUtils.f2(TimeUtils.second2Ms(baseRecover), 0, addition, 1);
    }

    /**
     * 打野体力消耗
     */
    public static long getKillMonsterEnergyCost(PlayerEntity playerEntity, int baseCost, int mainHeroId, int deputyHeroId, int killStreak) {
        long costReduceFixed = 0;
        long costReducePercent = 0;


        ConstTemplate consts = ResHolder.getConsts(ConstTemplate.class);
        int continuityMonsterEnergy = consts.getContinuityMonsterEnergy();
        int continuityMonsterEnergyMaxTimes = consts.getContinuityMonsterEnergyMaxTimes();
        costReduceFixed += (long) Math.min(killStreak, continuityMonsterEnergyMaxTimes) * continuityMonsterEnergy;

        costReduceFixed += playerEntity.getAddComponent().getAddition(BuffEffectType.ET_ENERGY_COST_REDUCE_FIXED);
        costReducePercent += playerEntity.getAddComponent().getAddition(BuffEffectType.ET_ENERGY_COST_REDUCE_PERCENT);

        PlayerHeroComponent heroComponent = playerEntity.getHeroComponent();
        if (mainHeroId > 0) {
            costReduceFixed += heroComponent.getHeroAddition(mainHeroId, BuffEffectType.ET_ENERGY_COST_REDUCE_FIXED, false);
            costReducePercent += heroComponent.getHeroAddition(mainHeroId, BuffEffectType.ET_ENERGY_COST_REDUCE_PERCENT, false);
        }
        if (deputyHeroId > 0) {
            costReduceFixed += heroComponent.getHeroAddition(deputyHeroId, BuffEffectType.ET_ENERGY_COST_REDUCE_FIXED, true);
            costReducePercent += heroComponent.getHeroAddition(deputyHeroId, BuffEffectType.ET_ENERGY_COST_REDUCE_PERCENT, true);
        }
        // 打野体力消耗 = （打野配表体力消耗 – 打野体力消耗降低固定值）*（ 1 – 打野体力消耗降低百分比）
        return FormulaUtils.f4(baseCost, costReduceFixed, costReducePercent, 1);
    }

    /**
     * 联盟积分每日上限
     */
    public static int getClanScoreDailyLimit(PlayerEntity playerEntity, int scoreTypeId) {
        int dayLimit = ResHolder.getInstance().getValueFromMap(ClanScoreTemplate.class, scoreTypeId).getDayLimit();
        long addition = playerEntity.getAddComponent().getAddition(BuffEffectType.ET_BUILD_CLAN_BUILDING_DAILY_SCORE_LIMIT);
        return (int) FormulaUtils.f1(dayLimit, addition, 0, 1);
    }

}


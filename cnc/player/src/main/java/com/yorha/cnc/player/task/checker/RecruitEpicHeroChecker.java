package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.RecruitEpicHeroEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 招募史诗英雄
 * <AUTHOR>
 */
public class RecruitEpicHeroChecker extends AbstractTaskChecker {
    private static final Logger LOGGER = LogManager.getLogger(RecruitEpicHeroChecker.class);

    public static List<String> attentionList = Lists.newArrayList(RecruitEpicHeroEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int configCount = taskParams.get(0);

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof RecruitEpicHeroEvent) {
                prop.setProcess(Math.min(prop.getProcess() + ((RecruitEpicHeroEvent) event).getRecruitTime(), configCount));
                LOGGER.info("RecruitEpicHeroChecker updateProcess, playerId={}, add {} time, process={}, config count={}", event.getPlayer().getPlayerId(), ((RecruitEpicHeroEvent) event).getRecruitTime(), prop.getProcess(), configCount);
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= configCount;
    }
}

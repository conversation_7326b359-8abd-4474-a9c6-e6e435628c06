package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.User;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_USER)
public class PlayerAccountController {

    @CommandMapping(code = MsgType.ACCOUNTLOGIN_C2S_MSG)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, User.AccountLogin_C2S_Msg msg) {
        // 不使用了，但不删，也不报错，防止客户端还在请求
        return User.AccountLogin_S2C_Msg.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.ACCOUNTBIND_C2S_MSG)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, User.AccountBind_C2S_Msg msg) {
        // 不使用了，但不删，也不报错，防止客户端还在请求
        return User.AccountBind_S2C_Msg.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.ACCOUNTUNBIND_C2S_MSG)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, User.AccountUnbind_C2S_Msg msg) {
        // 不使用了，但不删，也不报错，防止客户端还在请求
        return User.AccountUnbind_S2C_Msg.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.ACCOUNTBINDINFO_C2S_MSG)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, User.AccountBindInfo_C2S_Msg msg) {
        // 不使用了，但不删，也不报错，防止客户端还在请求
        return User.AccountBindInfo_S2C_Msg.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.REPORTEXECUTE_C2S_MSG)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, User.ReportExecute_C2S_Msg msg) {
        playerEntity.getAntiAddictionComponent().reportExecute(msg.getRuleName(), msg.getInstrTraceId(), msg.getExecTime());
        return User.ReportExecute_S2C_Msg.getDefaultInstance();
    }
}

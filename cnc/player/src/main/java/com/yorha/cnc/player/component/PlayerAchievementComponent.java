package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.achievement.AchievementHelper;
import com.yorha.common.achievement.AchievementInterface;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.scene.AchievementResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.AchievementConfigTemplate;
import res.template.DemandConfigTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yorha.proto.CommonEnum.AchievementDemandType.ACHST_PLAYER;
import static com.yorha.proto.CommonEnum.AchievementState.*;
import static com.yorha.proto.CommonEnum.AchievementType.ACHT_PLAYER;

/**
 * 玩家原服成就
 *
 * <AUTHOR>
 */
public class PlayerAchievementComponent extends PlayerComponent implements AchievementInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerAchievementComponent.class);

    public PlayerAchievementComponent(PlayerEntity owner) {
        super(owner);
    }

    private PlayerAchievementModelProp getProp() {
        return getOwner().getProp().getAchievementModel();
    }

    @Override
    public void onLoad(boolean isRegister) {
        super.onLoad(isRegister);
        // 自动接取任务
        final Map<Integer, AchievementConfigTemplate> achievementConfigTemplateMap = ResHolder.getInstance().getMap(AchievementConfigTemplate.class);
        for (AchievementConfigTemplate achievementConfigTemplate : achievementConfigTemplateMap.values()) {
            if (achievementConfigTemplate.getType() == ACHT_PLAYER) {
                final int achievementId = achievementConfigTemplate.getId();
                final AchievementProp achievementProp = getProp().getAchievements().computeIfAbsent(achievementId, key -> new AchievementProp().setAchievementId(achievementId));
                if (achievementProp.getState() != ACHS_NONE) {
                    continue;
                }
                achievementProp.setState(ACHS_ACCEPT);
                if (checkAchievementAllDemandComplete(achievementId)) {
                    onAchievementComplete(achievementId);
                }
            }
        }
        LOGGER.info("PlayerAchievementComponent onLoad player={}", getOwner().getPlayerId());
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        // 原服个人成就完成但是没有领取的红点
        final PlayerAchievement.AchievementCompleteNtf.Builder msgBuilder = PlayerAchievement.AchievementCompleteNtf.newBuilder();
        for (AchievementProp achievementProp : getProp().getAchievements().values()) {
            final int achievementId = achievementProp.getAchievementId();
            if (achievementProp.getState() == ACHS_COMPLETE) {
                msgBuilder.addAchievements(achievementId);
            }
        }
        if (msgBuilder.getAchievementsCount() > 0) {
            getOwner().sendMsgToClient(MsgType.PLAYER_ACHIEVEMENTCOMPLETENTF_NTF, msgBuilder.build());
        }
        LOGGER.info("PlayerAchievementComponent postLogin player={}", getOwner().getPlayerId());
    }

    /**
     * 成就分享数据
     */
    public void fillAchievementShareData(int achievementId, CommonMsg.MessageData.Builder messageData) throws GeminiException {
        final Map<Integer, AchievementConfigTemplate> achievementConfigTemplateMap = ResHolder.getInstance().getMap(AchievementConfigTemplate.class);
        final AchievementConfigTemplate achievementConfigTemplate = achievementConfigTemplateMap.get(achievementId);
        if (achievementConfigTemplate == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unknown achievement " + achievementId);
        }
        if (achievementConfigTemplate.getType() != ACHT_PLAYER) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "unsupport type " + achievementConfigTemplate.getType());
        }
        final AchievementProp prop = getProp().getAchievementsV(achievementId);
        if (prop == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "prop is null " + achievementId);
        }
        final Map<Integer, Long> demandsProgress = getDemandsProgress(achievementConfigTemplate.getDemandsList());
        messageData.getAchievementDataBuilder()
                .setAchievementId(prop.getAchievementId())
                .setCompleteTsMs(prop.getCompleteTsMs())
                .setState(prop.getState())
                .putAllDemands(demandsProgress);
    }

    /**
     * 获取成就需求进度
     * 返回值 成就需求id->进度
     */
    private Map<Integer, Long> getDemandsProgress(List<Integer> demands) {
        final Map<Integer, DemandConfigTemplate> demandConfigTemplateMap = ResHolder.getInstance().getMap(DemandConfigTemplate.class);
        final Map<Integer, Long> demandsProgress = new HashMap<>();
        for (int demandId : demands) {
            final DemandConfigTemplate demandConfigTemplate = demandConfigTemplateMap.get(demandId);
            if (demandConfigTemplate == null) {
                continue;
            }
            final List<Integer> params = demandConfigTemplate.getParamList();
            final int firstParam = params.getFirst();
            final StatisticEnum statisticEnum = AchievementHelper.checkType2StatisticEnum(demandConfigTemplate.getCheckType());
            if (statisticEnum == null) {
                continue;
            }
            // 特殊统计项
            if (!AchievementHelper.canRecord(statisticEnum)) {
                final long spProgress = getCantRecordDemandProcess(demandConfigTemplate.getCheckType(), demandConfigTemplate.getType(), firstParam);
                demandsProgress.put(demandId, spProgress);
                continue;
            }
            // 获取统计项
            final Int32SecondStatisticMapProp secondStatisticMapProp = getOwner().getStatisticComponent().getProp().getSecondStatisticMap();
            final Int32SingleStatisticMapProp singleStatisticMapProp = getOwner().getStatisticComponent().getProp().getSingleStatisticMap();
            // 二维统计项
            if (statisticEnum.isSecondStatistic()) {
                if (secondStatisticMapProp == null) {
                    continue;
                }
                final SecondStatisticProp secondStatisticProp = secondStatisticMapProp.get(statisticEnum.ordinal());
                if (secondStatisticProp == null) {
                    continue;
                }

                final SingleStatisticProp singleStatisticProp = secondStatisticProp.getSingleStatisticMap().get(firstParam);
                if (singleStatisticProp == null) {
                    continue;
                }
                // 需求未达成
                demandsProgress.put(demandId, singleStatisticProp.getValue());
                continue;
            }
            // 一维统计项
            if (statisticEnum.isSingleStatistic()) {
                if (singleStatisticMapProp == null) {
                    continue;
                }
                final SingleStatisticProp singleStatisticProp = singleStatisticMapProp.get(statisticEnum.ordinal());
                if (singleStatisticProp == null) {
                    continue;
                }
                demandsProgress.put(demandId, singleStatisticProp.getValue());
            }
        }
        return demandsProgress;
    }

    /**
     * 拉取成就页面时的展示数据
     */
    public void fillFetchInfo(PlayerAchievement.Player_FetchAchievement_S2C.Builder rsp) {
        final Map<Integer, AchievementConfigTemplate> achievementConfigTemplateMap = ResHolder.getInstance().getMap(AchievementConfigTemplate.class);
        rsp.putAllAchievements(getProp().getAchievements().getCopyCsBuilder().getDatasMap());
        for (int achievementId : getProp().getAchievements().keySet()) {
            final AchievementConfigTemplate template = achievementConfigTemplateMap.get(achievementId);
            if (template == null) {
                continue;
            }
            final Map<Integer, Long> demandsProgress = getDemandsProgress(template.getDemandsList());
            rsp.putAllDemands(demandsProgress);
        }
    }


    /**
     * 根据校验类型，获取特殊，不记录在统计项中的成就进度
     */
    private long getCantRecordDemandProcess(CommonEnum.AchievementStatisticCheckerType checkerType, CommonEnum.AchievementDemandType demandType, int id) {
        if (demandType != ACHST_PLAYER) {
            LOGGER.error("PlayerAchievementComponent getCantRecordDemandProcess checkerType={} unsupport demandType={} id={}", checkerType, demandType, id);
            return 0;
        }

        switch (checkerType) {
            case ASCT_SURVEY_PART: {
                return 0;
            }
            case ASCT_UNLOCK_SOLDIER: {
                if (getOwner().getTechComponent().getUnlockSoldierList().contains(id)) {
                    return 1;
                }
                return 0;
            }
            case ASCT_UNLOCK_HERO: {
                if (getOwner().getHeroComponent().isUnLockByHeroId(id)) {
                    return 1;
                }
                return 0;
            }
            default:
                LOGGER.error("PlayerAchievementComponent getCantRecordDemandProcess not support checkerType={} id={}", checkerType, id);
                return 0;
        }
    }

    public void onTrigger(CommonEnum.AchievementStatisticCheckerType checkerType, long oldValue, long newValue) {
        onTrigger(checkerType, 0, oldValue, newValue);
    }

    @Override
    public void onTrigger(CommonEnum.AchievementStatisticCheckerType checkerType, int id, long oldValue, long newValue) {
        try {
            LOGGER.info("PlayerAchievementComponent onTrigger checkerType={} id={} value:{}->{}", checkerType, id, oldValue, newValue);
            final AchievementResService achievementService = ResHolder.getResService(AchievementResService.class);
            final List<DemandConfigTemplate> demands = achievementService.getDemands(checkerType);
            final StatisticEnum statisticEnum = AchievementHelper.checkType2StatisticEnum(checkerType);
            if (statisticEnum == null) {
                LOGGER.error("PlayerAchievementComponent onTrigger fail checkerType={} statisticEnum not bind", checkerType);
                return;
            }
            for (DemandConfigTemplate demand : demands) {
                final AchievementConfigTemplate achievementConfigTemplate = achievementService.getAchievementConfigTemplate(demand.getId());
                if (achievementConfigTemplate == null) {
                    LOGGER.error("PlayerAchievementComponent onTrigger fail checkerType={} achievementConfigTemplate is null demand={}", checkerType, demand.getId());
                    continue;
                }
                final int achievementId = achievementConfigTemplate.getId();
                // 成就已达成
                if (isAchievementComplete(achievementId)) {
                    continue;
                }
                // 配表做过校验了，这里可以无脑根据索引list参数
                final List<Integer> params = demand.getParamList();
                final int firstParam = params.get(0);
                int param = firstParam;
                if (statisticEnum.isSecondStatistic()) {
                    // 二维统计项的第一个参数,与配置表配置的不同
                    if (id != firstParam) {
                        continue;
                    }
                    param = params.get(1);
                }
                // 只支持原服个人成就需求
                if (demand.getType() != ACHST_PLAYER) {
                    LOGGER.error("PlayerAchievementComponent onTrigger unknown type demand={} type={}", demand.getId(), demand.getType());
                    continue;
                }
                // 需求已达成
                if (AchievementHelper.isDemandComplete(oldValue, param)) {
                    continue;
                }
                // 本次未达成需求
                if (!AchievementHelper.isDemandComplete(newValue, param)) {
                    continue;
                }
                // 本需求对应的成就的所有需求均达成了
                if (checkAchievementAllDemandComplete(achievementId)) {
                    onAchievementComplete(achievementId);
                }
            }
        } catch (Exception e) {
            LOGGER.error("PlayerAchievementComponent onTrigger failed checkerType={} id={} value:{}->{}", checkerType, id, oldValue, newValue, e);
        }
    }

    @Override
    public boolean isAchievementComplete(int achievementId) {
        final Int32AchievementMapProp achievementMapProp = getProp().getAchievements();
        if (!achievementMapProp.containsKey(achievementId)) {
            return false;
        }
        final CommonEnum.AchievementState state = achievementMapProp.get(achievementId).getState();
        if (state == ACHS_COMPLETE) {
            return true;
        }
        return state == CommonEnum.AchievementState.ACHS_REWARD;
    }

    public boolean isAchievementCompleteNotReward(int achievementId) {
        final Int32AchievementMapProp achievementMapProp = getProp().getAchievements();
        if (!achievementMapProp.containsKey(achievementId)) {
            return false;
        }
        final CommonEnum.AchievementState state = achievementMapProp.get(achievementId).getState();
        return state == ACHS_COMPLETE;
    }

    @Override
    public boolean checkAchievementAllDemandComplete(int achievementId) {
        final Map<Integer, AchievementConfigTemplate> achievementConfigTemplateMap = ResHolder.getInstance().getMap(AchievementConfigTemplate.class);
        final Map<Integer, DemandConfigTemplate> demandConfigTemplateMap = ResHolder.getInstance().getMap(DemandConfigTemplate.class);
        final AchievementConfigTemplate achievementConfigTemplate = achievementConfigTemplateMap.get(achievementId);
        if (achievementConfigTemplate == null) {
            LOGGER.error("PlayerAchievementComponent checkAchievementAllDemandComplete no achievement template achievementId={}", achievementId);
            return false;
        }
        // 遍历每个需求是否都达成
        final Map<Integer, Long> demandsProgress = getDemandsProgress(achievementConfigTemplate.getDemandsList());
        for (int demandId : achievementConfigTemplate.getDemandsList()) {
            final DemandConfigTemplate demandConfigTemplate = demandConfigTemplateMap.get(demandId);
            if (demandConfigTemplate == null) {
                LOGGER.error("PlayerAchievementComponent checkAchievementAllDemandComplete no demand template achievementId={} , demand={}",
                        achievementId, demandId);
                return false;
            }
            final CommonEnum.AchievementStatisticCheckerType checkerType = demandConfigTemplate.getCheckType();
            final StatisticEnum statisticEnum = AchievementHelper.checkType2StatisticEnum(checkerType);
            if (statisticEnum == null) {
                LOGGER.error("PlayerAchievementComponent checkAchievementAllDemandComplete fail checkerType={} statisticEnum not bind demand={}",
                        checkerType, demandId);
                return false;
            }
            if (demandConfigTemplate.getType() != ACHST_PLAYER) {
                LOGGER.error("PlayerAchievementComponent checkAchievementAllDemandComplete not support ={} achievementId={} , demand={}",
                        demandConfigTemplate.getType(), achievementId, demandId);
                return false;
            }
            final long progress = demandsProgress.getOrDefault(demandId, 0L);
            // 二维统计项
            if (statisticEnum.isSecondStatistic()) {
                final List<Integer> params = demandConfigTemplate.getParamList();
                final int secondParam = params.get(1);
                // 需求未达成
                if (!AchievementHelper.isDemandComplete(progress, secondParam)) {
                    return false;
                }
                continue;
            }
            // 一维统计项
            if (statisticEnum.isSingleStatistic()) {
                final List<Integer> params = demandConfigTemplate.getParamList();
                final int firstParam = params.getFirst();
                // 需求未达成
                if (!AchievementHelper.isDemandComplete(progress, firstParam)) {
                    return false;
                }
            }
        }
        // 本成就所有需求已达成
        return true;
    }

    @Override
    public void onAchievementComplete(int achievementId) {
        final AchievementProp achievementProp = getProp().getAchievementsV(achievementId);
        if (achievementProp == null) {
            LOGGER.error("PlayerAchievementComponent onAchievementComplete achievementProp is null achievementId={}", achievementId);
            return;
        }
        if (achievementProp.getState() != ACHS_ACCEPT) {
            return;
        }
        achievementProp.setCompleteTsMs(SystemClock.now());
        final CommonEnum.AchievementState oldState = achievementProp.getState();
        achievementProp.setState(ACHS_COMPLETE);
        // 红点通知
        final PlayerAchievement.AchievementCompleteNtf.Builder msgBuilder = PlayerAchievement.AchievementCompleteNtf.newBuilder();
        msgBuilder.addAchievements(achievementId);
        getOwner().sendMsgToClient(MsgType.PLAYER_ACHIEVEMENTCOMPLETENTF_NTF, msgBuilder.build());
        // 通知PlayerCard更新
        getOwner().getPlayerPropComponent().updatePlayerCardCache(false);
        LOGGER.info("PlayerAchievementComponent onAchievementComplete achievementId={} state: {}->{}", achievementId, oldState, achievementProp.getState());
    }

    @Override
    public Struct.YoAssetPackage takeReward(int achievementId) {
        final AchievementProp achievementProp = getProp().getAchievementsV(achievementId);
        if (achievementProp == null) {
            LOGGER.error("PlayerAchievementComponent takeReward achievementProp is null achievementId={}", achievementId);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "AchievementProp is null id=" + achievementId);
        }
        final CommonEnum.AchievementState oldState = achievementProp.getState();
        //发奖励
        final Map<Integer, AchievementConfigTemplate> achievementConfigTemplateMap = ResHolder.getInstance().getMap(AchievementConfigTemplate.class);
        final AchievementConfigTemplate achievementConfigTemplate = achievementConfigTemplateMap.get(achievementId);
        if (achievementConfigTemplate == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "achievementConfigTemplate is null id=" + achievementId);
        }
        final Struct.YoAssetPackage.Builder builder = Struct.YoAssetPackage.newBuilder();
        for (IntPairType reward : achievementConfigTemplate.getRewardPairList()) {
            builder.getAssetsBuilder().addDatas(
                    Struct.YoAssetDesc.newBuilder()
                            .setId(reward.getKey())
                            .setAmount(reward.getValue())
                            .setType(1).build()
            );
        }
        Struct.YoAssetPackage assetPackage = builder.build();
        achievementProp.setState(CommonEnum.AchievementState.ACHS_REWARD);
        LOGGER.info("PlayerAchievementComponent takeReward achievementId={} state: {}->{}", achievementId, oldState, achievementProp.getState());
        return assetPackage;
    }
}

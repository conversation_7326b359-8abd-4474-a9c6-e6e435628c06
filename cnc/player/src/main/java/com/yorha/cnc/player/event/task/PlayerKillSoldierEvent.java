package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

import java.util.Map;

/**
 * PVP击杀士兵事件
 *
 * <AUTHOR>
 */
public class PlayerKillSoldierEvent extends PlayerTaskEvent {
    private final Map<Integer, Long> killSoldierMap;
    private final boolean npcEnemy;
    private final int otherCurZoneId;

    public Map<Integer, Long> getKillSoldierMap() {
        return killSoldierMap;
    }

    public PlayerKillSoldierEvent(PlayerEntity entity, Map<Integer, Long> killSoldierMap, boolean npcEnemy, int otherCurZoneId) {
        super(entity);
        this.killSoldierMap = killSoldierMap;
        this.npcEnemy = npcEnemy;
        this.otherCurZoneId = otherCurZoneId;
    }

    public int getOtherCurZoneId() {
        return otherCurZoneId;
    }

    public boolean isNpcEnemy() {
        return npcEnemy;
    }
}

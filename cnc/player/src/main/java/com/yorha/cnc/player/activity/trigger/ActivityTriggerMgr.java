package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.Maps;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.proto.CommonEnum;
import javassist.Modifier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 活动触发器工厂
 *
 * <AUTHOR>
 */
public class ActivityTriggerMgr {
    private static final Logger LOGGER = LogManager.getLogger(ActivityTriggerMgr.class);

    private final Map<CommonEnum.ActivityUnitTriggerType, AbstractActivityTrigger> triggersMap = Maps.newHashMap();

    private static class InstanceHolder {
        private static final ActivityTriggerMgr INSTANCE = new ActivityTriggerMgr();
    }

    public static ActivityTriggerMgr getInstance() {
        return ActivityTriggerMgr.InstanceHolder.INSTANCE;
    }

    public void init() {
        final JavaClassScanner scanner = new JavaClassScanner();
        for (Class<? extends AbstractActivityTrigger> triggerClazz : scanner.getSubTypesOf(AbstractActivityTrigger.class.getPackage().getName(), AbstractActivityTrigger.class)) {
            try {
                if (Modifier.isAbstract(triggerClazz.getModifiers())) {
                    continue;
                }
                final TriggerController triggerController = triggerClazz.getAnnotation(TriggerController.class);
                if (triggerController == null) {
                    throw new GeminiException("activity trigger {} without TriggerController", triggerClazz.getName());
                }
                final CommonEnum.ActivityUnitTriggerType type = triggerController.type();
                if (triggersMap.containsKey(type)) {
                    throw new GeminiException("activity trigger duplicated. {}", type);
                }
                triggersMap.put(type, triggerClazz.getDeclaredConstructor().newInstance());
                LOGGER.info("ActivityTriggerFactory register {}", type);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    public AbstractActivityTrigger get(CommonEnum.ActivityUnitTriggerType type) {
        AbstractActivityTrigger trigger = triggersMap.get(type);
        if (trigger == null) {
            throw new GeminiException("activity trigger not found. {}", type);
        }
        return trigger;
    }

}

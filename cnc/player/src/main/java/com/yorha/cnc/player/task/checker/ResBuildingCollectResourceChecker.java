package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.cnc.player.event.task.ResBuildingCollectResourceEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.RESBUILDING_COLLECT_ANY_RESOURCE_NUM;
import static com.yorha.common.enums.statistic.StatisticEnum.RESBUILDING_COLLECT_RESOURCE_NUM;
/**
 * 世界采集资源数量达到多少(param1 = 0表示任意资源)
 * param1: 资源类型
 * param2: 数量
 *
 * <AUTHOR>
 */
public class ResBuildingCollectResourceChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            ResBuildingCollectResourceEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(
            PlayerTaskEvent event,
            TaskInfoProp prop,
            TaskPoolTemplate taskTemplate
    ) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.get(0);
        int param2 = taskParams.get(1);

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int collectTotal;
            if (param1 == 0) {
                collectTotal = (int) event.getPlayer().getStatisticComponent().getSingleStatistic(RESBUILDING_COLLECT_ANY_RESOURCE_NUM);
            } else {
                collectTotal = (int) event.getPlayer().getStatisticComponent().getSecondStatistic(RESBUILDING_COLLECT_RESOURCE_NUM, param1);
            }
            prop.setProcess(Math.min(collectTotal, param2));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof ResBuildingCollectResourceEvent) {
                Map<Integer, Long> resMap = ((ResBuildingCollectResourceEvent) event).getCollectResMap();
                for (Map.Entry<Integer, Long> entry : resMap.entrySet()) {
                    if (entry.getKey() == param1 || param1 == 0) {
                        prop.setProcess(Math.min(prop.getProcess() + entry.getValue().intValue(), param2));
                    }
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= param2;
    }
}

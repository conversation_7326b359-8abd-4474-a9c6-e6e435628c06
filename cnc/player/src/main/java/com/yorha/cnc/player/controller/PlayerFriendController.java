package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.cluster.toplogy.Node;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.NodeRole;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.FriendApplyInfoProp;
import com.yorha.game.gen.prop.ShieldPlayerInfoProp;
import com.yorha.game.gen.prop.SimpleFriendInfoProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.PlayerFriend.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 好友相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_FRIEND)
public class PlayerFriendController {
    private static final Logger LOGGER = LogManager.getLogger(PlayerFriendController.class);

    /**
     * 搜索玩家
     */
    @CommandMapping(code = MsgType.PLAYER_SEARCHPLAYER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_SearchPlayer_C2S msg) {
        long playerId = msg.getPlayerId();
        PlayerFriend.Player_SearchPlayer_S2C.Builder result = PlayerFriend.Player_SearchPlayer_S2C.newBuilder();
        SsPlayerCard.QueryPlayerCardAns ans = CardHelper.queryPlayerCardSync(playerEntity.ownerActor(), playerId);
        if (!ans.hasCardInfo()) {
            result.setExist(false);
            return result.build();
        }
        result.setExist(true).setPlayerId(playerId).setZoneId(ans.getCardInfo().getZoneId())
                .setCardHead(ans.getCardInfo().getCardHead());
        return result.build();
    }

    /**
     * 搜索玩家
     */
    @CommandMapping(code = MsgType.PLAYER_SEARCHPLAYERBYNAME_C2S)
    public void handle(PlayerEntity playerEntity, Player_SearchPlayerByName_C2S msg, int seqId) {
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        String name = msg.getName();
        // 空的 返回本服排行榜前100名
        if (StringUtils.isEmpty(name)) {
            int rankId = RankConstant.ZONE_PLAYER_POWER_RANK;
            SsRank.GetRankInfoByLimitAsk.Builder rankQuery = SsRank.GetRankInfoByLimitAsk.newBuilder().setRankId(rankId);
            rankQuery.getRangeBuilder().setRangeStart(0).setRangeEnd(99);
            try {
                SsRank.GetRankInfoByLimitAns res = playerEntity.ownerActor().callZoneRank(playerEntity.getZoneId(), rankQuery.build());
                List<Long> player = new ArrayList<>();
                for (CommonMsg.MemberAllDto member : res.getMembersList()) {
                    player.add(member.getMemberId());
                }
                onSearchAndQueryCard(sessionRef, playerEntity, player, name, seqId);
            } catch (Exception e) {
                onSearchAndQueryCard(sessionRef, playerEntity, null, name, seqId);
                LOGGER.error("SearchPlayer failed name={} ", name, e);
            }
            return;
        }
        ConstTemplate consts = ResHolder.getConsts(ConstTemplate.class);
        int utf8Length = StringUtils.getUTF8Length(name);
        if (utf8Length > consts.getSearchCommanderNameMax() || utf8Length < consts.getSearchCommanderNameMin()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        int limit = consts.getSearchCommanderNum();
        long nameOwner = NameHelper.getNameOwner(playerEntity.ownerActor(), CommonEnum.NameType.PLAYER_NAME, name);
        try {
            List<IActorRef> refList = buildNameSearchZoneRef();
            SsName.SearchPlayerNameAsk query = SsName.SearchPlayerNameAsk.newBuilder().setName(name).setNumLimit(limit).build();
            LinkedList<Long> players = new LinkedList<>();
            playerEntity.ownerActor().<SsName.SearchPlayerNameAns>batchAsk(refList, query,
                    (res) -> players.addAll(res.getPlayerListList()),
                    () -> {
                        if (nameOwner != 0) {
                            players.remove(nameOwner);
                            players.addFirst(nameOwner);
                        }
                        if (players.size() > limit) {
                            List<Long> subList = players.subList(0, limit);
                            onSearchAndQueryCard(sessionRef, playerEntity, subList, name, seqId);
                        } else {
                            onSearchAndQueryCard(sessionRef, playerEntity, players, name, seqId);
                        }

                    });
        } catch (Exception e) {
            onSearchAndQueryCard(sessionRef, playerEntity, null, name, seqId);
            LOGGER.error("SearchPlayer failed name={} ", name, e);
        }
    }

    /**
     * 构建名字搜索的目标zone ref list
     */
    private List<IActorRef> buildNameSearchZoneRef() {
        List<IActorRef> refList = new ArrayList<>();
        List<Node> nodeList = ServerContext.getActorSystem().getRegistryValue().getServerTypeNodeList(NodeRole.Zone.getTypeId());
        if (nodeList.size() <= GameLogicConstants.NAME_SEARCH_ZONE_NUM) {
            for (Node node : nodeList) {
                refList.add(RefFactory.ofName(node.getZoneId()));
            }
            return refList;
        }
        int zoneId = ServerContext.getZoneId();
        // 上下占一半， 剩下随机
        int start = zoneId - GameLogicConstants.NAME_SEARCH_ZONE_NUM / 4;
        int end = zoneId + GameLogicConstants.NAME_SEARCH_ZONE_NUM / 4;
        // 随机池
        List<Integer> rest = new ArrayList<>();
        for (Node node : nodeList) {
            int i = node.getZoneId();
            if (i >= start && i <= end) {
                refList.add(RefFactory.ofName(i));
                continue;
            }
            rest.add(i);
        }
        List<Integer> selected = RandomUtils.randomList(rest, GameLogicConstants.NAME_SEARCH_ZONE_NUM - refList.size());
        for (Integer i : selected) {
            refList.add(RefFactory.ofName(i));
        }
        return refList;
    }

    private void onSearchAndQueryCard(IActorRef sessionRef, PlayerEntity playerEntity, List<Long> players, String name, int seqId) {
        if (CollectionUtils.isEmpty(players)) {
            playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_SEARCHPLAYERBYNAME_S2C, null, Player_SearchPlayerByName_S2C.getDefaultInstance());
            LOGGER.info("onSearchAndQueryCard result name={} size=0", name);
            return;
        }
        CardHelper.batchQueryPlayerCardWithClan(playerEntity.ownerActor(), players,
                (map) -> {
                    Player_SearchPlayerByName_S2C.Builder builder = Player_SearchPlayerByName_S2C.newBuilder();
                    for (Long playerId : players) {
                        if (!map.containsKey(playerId)) {
                            continue;
                        }
                        builder.addCardList(map.get(playerId));
                    }
                    playerEntity.answerMsgToClient(sessionRef, seqId, MsgType.PLAYER_SEARCHPLAYERBYNAME_S2C, null, builder.build());
                    LOGGER.info("onSearchAndQueryCard result name={} size={}", name, builder.getCardListCount());
                });
    }

    /**
     * 玩家申请加好友
     */
    @CommandMapping(code = MsgType.PLAYER_ADDFRIENDAPPLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_AddFriend_C2S msg, int seqId) {
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getFriendComponent().applyNewFriend(sessionRef, msg.getPlayerId(), msg.getZoneId(), seqId, GameLogicConstants.MIGRATE_RETRY_NUM);
        return null;
    }

    /**
     * 玩家同意加好友
     */
    @CommandMapping(code = MsgType.PLAYER_AGREEFRIENDAPPLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_AgreeFriendApply_C2S msg, int seqId) {
        // 检查玩家申请列表中是否有该玩家
        FriendPlayerEntity friendPlayerEntity = playerEntity.ownerActor().getOrLoadFriendPlayerEntity();
        FriendApplyInfoProp prop = friendPlayerEntity.getProp().getApplyListV(msg.getPlayerId());
        if (prop == null) {
            LOGGER.info("PlayerFriendController playerAgreeFriendApply, applyInfo is null, applierId={}", msg.getPlayerId());
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
        }
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getFriendComponent().agreeFriendApply(sessionRef, msg.getPlayerId(), prop.getZoneId(), seqId, GameLogicConstants.MIGRATE_RETRY_NUM);
        return null;
    }

    /**
     * 玩家拒绝加好友
     */
    @CommandMapping(code = MsgType.PLAYER_REFUSEFRIENDAPPLY_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RefuseFriendApply_C2S msg, int seqId) {
        // 检查玩家申请列表中是否有该玩家
        FriendPlayerEntity friendPlayerEntity = playerEntity.ownerActor().getOrLoadFriendPlayerEntity();
        FriendApplyInfoProp prop = friendPlayerEntity.getProp().getApplyListV(msg.getPlayerId());
        if (prop == null) {
            LOGGER.info("PlayerFriendController playerRefuseFriendApply, applyInfo is null, applierId={}", msg.getPlayerId());
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
        }
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getFriendComponent().refuseFriendApply(sessionRef, msg.getPlayerId(), prop.getZoneId(), seqId, GameLogicConstants.MIGRATE_RETRY_NUM);
        return null;
    }

    /**
     * 玩家删除好友
     */
    @CommandMapping(code = MsgType.PLAYER_DELFRIEND_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_DelFriend_C2S msg, int seqId) {
        // 检查玩家好友列表中是否有该好友
        FriendPlayerEntity friendPlayerEntity = playerEntity.ownerActor().getOrLoadFriendPlayerEntity();
        SimpleFriendInfoProp prop = friendPlayerEntity.getProp().getFriendList().get(msg.getPlayerId());
        if (prop == null) {
            LOGGER.info("PlayerFriendController playerDelFriend, friendInfo is null, friendId={}", msg.getPlayerId());
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
        }
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getFriendComponent().delFriend(sessionRef, msg.getPlayerId(), prop.getZoneId(), seqId, GameLogicConstants.MIGRATE_RETRY_NUM);
        return null;
    }

    /**
     * 玩家屏蔽好友
     */
    @CommandMapping(code = MsgType.PLAYER_SHIELDPLAYER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_ShieldPlayer_C2S msg, int seqId) {
        long playerId = msg.getPlayerId();
        if (playerId == playerEntity.getPlayerId()) {
            LOGGER.info("shieldPlayer fail player {} is self", playerId);
            return Player_ShieldPlayer_S2C.getDefaultInstance();
        }
        FriendPlayerEntity friendPlayerEntity = playerEntity.ownerActor().getOrLoadFriendPlayerEntity();
        if (friendPlayerEntity.hasShieldPlayer(playerId)) {
            LOGGER.info("shieldPlayer: player {} has been shield", playerId);
            return Player_ShieldPlayer_S2C.getDefaultInstance();
        }
        IActorRef sessionRef = playerEntity.ownerActor().sender();
        playerEntity.getFriendComponent().shieldPlayer(sessionRef, playerId, seqId);
        return null;
    }

    /**
     * 解除玩家屏蔽
     */
    @CommandMapping(code = MsgType.PLAYER_REMOVESHIELDPLAYER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RemoveShield_C2S msg) {
        // 检查是否屏蔽了该玩家
        FriendPlayerEntity friendPlayerEntity = playerEntity.ownerActor().getOrLoadFriendPlayerEntity();
        ShieldPlayerInfoProp prop = friendPlayerEntity.getProp().getShiledList().get(msg.getPlayerId());
        if (prop == null) {
            throw new GeminiException(ErrorCode.FRIEND_PARAM_ILLEGAL);
        }
        playerEntity.getFriendComponent().removeShield(msg.getPlayerId());
        return Player_RemoveShield_S2C.getDefaultInstance();
    }
}

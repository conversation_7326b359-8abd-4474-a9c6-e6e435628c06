package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;
import res.template.ConstActivityTemplate;

import java.util.Collections;
import java.util.List;

public class DiscountWeekMonthCardGoods extends WeekMonthCardGoods {
    private static final Logger LOGGER = LogManager.getLogger(DiscountWeekMonthCardGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        if (!hasDiscountCoupon(owner)) {
            throw new GeminiException(ErrorCode.NO_COUPON);
        }
        super.checkApply(owner, msg, goodsTemplate);
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        if (!hasDiscountCoupon(owner)) {
            throw new GeminiException(ErrorCode.NO_COUPON);
        }
        LOGGER.info("DiscountWeekMonthCardGoods afterBaseDeliver goodsOrder={}", goodsOrder);
        super.afterBaseDeliver(owner, goodsTemplate, goodsOrder);
        // 发货后扣除折扣券道具(qlog subReason 用礼包Id)
        owner.getItemComponent().consume(getCounponId(), 1, CommonEnum.Reason.ICR_CHARGE_BASE, String.valueOf(goodsTemplate.getId()));
        return Collections.emptyList();
    }

    @Override
    protected int getCardId(final int goodsId) {
        return ResHolder.getResService(ActivityResService.class).getWeekMonthGoodsId(goodsId);
    }

    private boolean hasDiscountCoupon(final PlayerEntity owner) {
        return owner.getItemComponent().hasEnough(getCounponId(), 1);
    }

    static int getCounponId() {
        return ResHolder.getConsts(ConstActivityTemplate.class).getWeekMonthCardCouponID();
    }
}

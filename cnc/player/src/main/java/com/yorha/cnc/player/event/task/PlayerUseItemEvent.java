package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;


/**
 * 使用道具事件
 *
 * <AUTHOR>
 */
public class PlayerUseItemEvent extends PlayerTaskEvent {
    private final int itemId;
    private final int itemType;
    private final int itemNum;

    public int getItemId() {
        return itemId;
    }

    public int getItemNum() {
        return itemNum;
    }

    public int getItemType() {
        return itemType;
    }

    public PlayerUseItemEvent(PlayerEntity entity, int itemId, int itemType, int itemNum) {
        super(entity);
        this.itemId = itemId;
        this.itemType = itemType;
        this.itemNum = itemNum;
    }

}

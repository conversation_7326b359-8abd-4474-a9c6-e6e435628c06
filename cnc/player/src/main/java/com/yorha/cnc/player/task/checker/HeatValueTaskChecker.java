package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.HeatValueTaskEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 活跃度任务
 * param1 活跃度值
 *
 * <AUTHOR>
 */
public class HeatValueTaskChecker extends AbstractTaskChecker {
    public static List<String> attentionList = Lists.newArrayList(HeatValueTaskEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int heatValue = event.getPlayer().getTaskComponent().getHeatValue();
        Integer param = taskParams.getFirst();

        if (heatValue != prop.getProcess()) {
            prop.setProcess(heatValue);
        }

        return prop.getProcess() >= param;
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.ClanBuildingInfoEvent;
import com.yorha.cnc.player.event.task.PlayerJoinClanEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanTerritory;
import com.yorha.proto.StructClan;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;


/**
 * 联盟指挥中心建成数量是否达到
 * param1: 指挥中心数量
 */
public class ClanCommandCenterChecker extends AbstractTaskChecker {
    public static List<String> attentionList = Lists.newArrayList(ClanBuildingInfoEvent.class.getSimpleName(), PlayerJoinClanEvent.class.getSimpleName(), CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        int countConfig = taskTemplate.getTypeValueList().get(0);

        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            // 剪枝：任务已完成
            if (prop.getProcess() >= countConfig) {
                return true;
            }
            final CommonEnum.MapBuildingType targetType = CommonEnum.MapBuildingType.MBT_COMMAND_CENTER;
            StructClan.ClanBuildingInfo commmandCenterInfo = null;
            if (event instanceof ClanBuildingInfoEvent) {
                ClanBuildingInfoEvent clanBuildingInfoEvent = (ClanBuildingInfoEvent) event;
                commmandCenterInfo = clanBuildingInfoEvent.getBuildingInfo().get(targetType.getNumber());
            } else {
                if (event.getPlayer().getProp().getClan().getClanId() == 0) {
                    return prop.getProcess() >= countConfig;
                }
                if (event instanceof PlayerJoinClanEvent) {
                    // 创建联盟时跳过callScene，避免时序问题
                    PlayerJoinClanEvent playerJoinClanEvent = (PlayerJoinClanEvent) event;
                    if (playerJoinClanEvent.isPlayerCreate) {
                        return prop.getProcess() >= countConfig;
                    }
                }

                SsClanTerritory.FetchClanBuildingInfoAns ans = event.getPlayer().ownerActor().callCurClan(
                        SsClanTerritory.FetchClanBuildingInfoAsk.newBuilder()
                                .setClanId(event.getPlayer().getProp().getClan().getClanId())
                                .build());

                commmandCenterInfo = ans.getClanBuildingMap().get(targetType.getNumber());
            }
            // 任务未完成时进度可回退
            int curCommnadCenterNum = commmandCenterInfo == null ? prop.getProcess() : commmandCenterInfo.getBuiltNum();
            prop.setProcess(curCommnadCenterNum);
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}", ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;

    }

}

package com.yorha.cnc.player.item.use.impl;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.component.PlayerQueueTaskComponent;
import com.yorha.cnc.player.item.use.AbstractUsableItem;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.ItemUseParamsProp;
import com.yorha.proto.CommonEnum.QueueTaskType;
import com.yorha.proto.PlayerCommon.Player_UseItem_S2C.Builder;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;
import res.template.ItemTemplate;

import java.util.List;

import static com.yorha.proto.CommonEnum.Reason.ICR_EXCHANGE;

/**
 * 解锁队列
 *
 * <AUTHOR>
 * 2021年11月19日 12:28:00
 */
public class UnLockCityBuildQueue extends AbstractUsableItem {

    private AssetPackage withdrawReward = null;

    public UnLockCityBuildQueue(int num, ItemTemplate itemTemplate) {
        super(num, itemTemplate);
    }

    @Override
    public void verifyThrow(PlayerEntity playerEntity, ItemUseParamsProp params) {
        if (num != 1) {
            throw new GeminiException(ErrorCode.ITEM_BATCH_USE_LIMIT);
        }
    }

    @Override
    public boolean use(PlayerEntity playerEntity, ItemUseParamsProp params) {
        ItemTemplate template = getTemplate();
        int unlockSec = template.getEffectValue();
        PlayerQueueTaskComponent queueComponent = playerEntity.getPlayerQueueTaskComponent();
        if (queueComponent.isSecondBuildQueueForeverUnlocked()) {
            // 第二队列已经永久解锁了，现在就兑换成别的
            List<IntPairType> withdrawConf = template.getWithdrawItemIdPairList();
            AssetPackage.Builder reward = AssetPackage.builder();
            for (IntPairType pair : withdrawConf) {
                reward.plusItem(pair.getKey(), (long) pair.getValue() * num);
            }

            withdrawReward = reward.build();
            playerEntity.getAssetComponent().give(withdrawReward, ICR_EXCHANGE, String.valueOf(template.getId()));
        } else {
            queueComponent.unLockQueue(false, QueueTaskType.CITY_BUILD, unlockSec);
        }
        return true;
    }

    @Override
    public void responseMessage(Builder response) {
        if (withdrawReward != null) {
            PlayerPB.ItemUseResultPB.Builder resultPB = PlayerPB.ItemUseResultPB.newBuilder();
            withdrawReward.forEachItems(itemDesc ->
                    resultPB.getItemsBuilder().addDatas(StructPB.ItemPB.newBuilder()
                            .setTemplateId(itemDesc.getId())
                            .setNum((int) itemDesc.getAmount())
                            .build()));
            response.setResult(resultPB);
        }
    }
}

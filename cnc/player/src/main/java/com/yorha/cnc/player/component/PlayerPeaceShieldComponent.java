package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.constant.DevBuffConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;


/**
 * <AUTHOR>
 */
public class PlayerPeaceShieldComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPeaceShieldComponent.class);

    public PlayerPeaceShieldComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postLoad(boolean isRegister) {
        if (isRegister) {
            // 新手护盾
            openRookiePeaceShield();
        }
    }

    /**
     * 开启和平护盾
     *
     * @param shieldBuffId 护盾id
     * @param endTime      强制结束时间
     */
    public void openPeaceShield(int shieldBuffId, Long endTime, CommonEnum.DevBuffSourceType sourceType, DevBuffConstants.PeaceShieldReason reason) {
        if (endTime != null && endTime < SystemClock.now()) {
            LOGGER.error("{} buffId:{}, add peace shield failed. cause endTime:{} invalid.", getOwner(), shieldBuffId, endTime);
            throw new GeminiException(ErrorCode.BUFF_OPEN_PEACE_SHIELD_FAILED);
        }
        getOwner().getPlayerDevBuffComponent().addDevBuff(shieldBuffId, endTime, sourceType);
    }

    /**
     * 开启新手和平护盾
     */
    public void openRookiePeaceShield() {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        int shieldId = constTemplate.getNewPlayerProtectBuff();
        getOwner().getPlayerDevBuffComponent().addDevBuff(shieldId, null, CommonEnum.DevBuffSourceType.DBST_SYS);
    }

    /**
     * 关闭新手和平护盾
     */
    public void closeRookiePeaceShield() {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        int shieldId = constTemplate.getNewPlayerProtectBuff();
        // 新手护盾还在，移除新手护盾
        if (getOwner().getPlayerDevBuffComponent().removeDevBuff(shieldId) != null) {
            LOGGER.info("{} peace shield off id:{} reason:{}.", getOwner(), shieldId, DevBuffConstants.PeaceShieldReason.ROOKIE);
        }
    }
}

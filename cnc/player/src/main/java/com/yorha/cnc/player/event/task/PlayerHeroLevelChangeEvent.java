package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 英雄等级变化事件
 *
 * <AUTHOR>
 */
public class PlayerHeroLevelChangeEvent extends PlayerTaskEvent {
    private int heroId;
    private int toLevel;

    public int getHeroId() {
        return heroId;
    }

    public int getToLevel() {
        return toLevel;
    }

    public PlayerHeroLevelChangeEvent(PlayerEntity player, int heroId, int toLevel) {
        super(player);
        this.heroId = heroId;
        this.toLevel = toLevel;
    }

}

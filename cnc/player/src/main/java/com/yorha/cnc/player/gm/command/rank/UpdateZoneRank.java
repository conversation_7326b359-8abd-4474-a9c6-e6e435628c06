package com.yorha.cnc.player.gm.command.rank;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class UpdateZoneRank implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int rankId = Integer.parseInt(args.get("rankId"));
        int score = Integer.parseInt(args.get("score"));
        actor.getOrLoadEntity().getPlayerRankComponent().updateZoneRanking(rankId, score);
    }

    @Override
    public String showHelp() {
        return "UpdateZoneRank rankId={value} score={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_RANK;
    }
}


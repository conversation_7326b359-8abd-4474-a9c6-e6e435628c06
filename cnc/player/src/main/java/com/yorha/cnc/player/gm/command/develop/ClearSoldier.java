package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerSoldier;
import com.yorha.proto.SsScenePlayer;

import java.util.Map;

/**
 * 清空士兵
 */
public class ClearSoldier implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        PlayerEntity entity = actor.getEntity();
        SsScenePlayer.GetAllSoldierAsk getAsk = SsScenePlayer.GetAllSoldierAsk.newBuilder().setPlayerId(entity.getPlayerId()).build();
        SsScenePlayer.GetAllSoldierAns getAns = entity.ownerActor().callBigScene(getAsk);
        for (Map.Entry<Integer, Integer> entry : getAns.getSoldierMap().entrySet()) {
            if (entry.getValue() <= 0) {
                continue;
            }
            PlayerSoldier.Player_SoldierDismiss_C2S.Builder builder = PlayerSoldier.Player_SoldierDismiss_C2S.newBuilder();
            builder.setSoldierId(entry.getKey()).setNum(entry.getValue());
            entity.getSoldierComponent().handleDismiss(builder.build());
        }
    }

    @Override
    public String showHelp() {
        return "ClearSoldier";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_HERO_PLANE_SOLDIER;
    }
}

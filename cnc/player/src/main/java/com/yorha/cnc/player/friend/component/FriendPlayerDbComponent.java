package com.yorha.cnc.player.friend.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.friend.FriendPlayerEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.ActorChangeAttrUpsertStrategy;
import com.yorha.common.dbactor.DbTaskProxy;
import com.yorha.common.dbactor.DefaultDbOperationStrategyImpl;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.FriendPlayerProp;
import com.yorha.proto.Player;
import com.yorha.proto.TcaplusDb;
import org.jetbrains.annotations.NotNull;

import static com.yorha.cnc.player.PlayerActor.PLAYER_ENTITY_FRIEND;

/**
 * <AUTHOR>
 */
public class FriendPlayerDbComponent extends FriendPlayerComponent {
    /**
     * 操作增删改查的代理。
     */
    private final DbTaskProxy dbTaskProxy;

    public FriendPlayerDbComponent(FriendPlayerEntity owner, Player.FriendPlayer changedProp) {
        super(owner);
        final FriendPlayerChangeAttrInsertStrategy strategy = new FriendPlayerChangeAttrInsertStrategy(changedProp != null ? changedProp : Player.FriendPlayer.getDefaultInstance());
        this.dbTaskProxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString())
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .upsert(strategy)
                .owner(ownerActor())
                .limitTimerOwner(ownerActor())
                .entityId("Friend" + this.getEntityId())// chatPlayer、friendPlayer、player 共用entityId且同属于PlayerActor，避免定时器重复
                .intervalMs(DbLimitConstants.FRIEND_PLAYER_DB_LIMIT_INTERVAL_MS)
                .build();
    }

    @Override
    public void onRegister() {
        super.onRegister();
    }

    @Override
    public void onLoad() {
        super.onLoad();
    }

    /**
     * insert
     */
    public void saveDb() {
        this.dbTaskProxy.insert();
    }

    /**
     * 落脏
     */
    public void saveChangeToDb() {
        // 更新
        this.dbTaskProxy.update();
    }

    /**
     * [同步]销毁前落脏
     */
    public void saveOnDestroy() {
        if (!getOwner().isDestroy()) {
            WechatLog.error("FriendPlayerDbComponent endDb {} is not destroy!", getOwner());
        }
        this.saveAndDestroy();
    }

    /**
     * [同步]完整落库后停止db操作托管
     */
    public void saveAndDestroy() {
        if (dbTaskProxy.isProxyStop()) {
            return;
        }
        // 先强制存盘一次
        this.dbTaskProxy.saveDbSync();
        this.dbTaskProxy.stop();
    }

    private static class FriendPlayerChangeAttrInsertStrategy extends ActorChangeAttrUpsertStrategy<Player.FriendPlayer> {

        FriendPlayerChangeAttrInsertStrategy(GeneratedMessageV3 msg) {
            super(msg);
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            return ((PlayerActor) actor).getFriendPlayerEntity().getProp().copyChangeToDb((Player.FriendPlayer.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected Player.FriendPlayer buildFullAttrSaveData(AbstractActor actor) {
            final PlayerActor playerActor = (PlayerActor) actor;
            return playerActor.getFriendPlayerEntity().getProp().getCopyDbBuilder().build();
        }

        @Override
        protected Player.FriendPlayer buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            final TcaplusDb.FriendPlayerTable.Builder recordBuilder = (TcaplusDb.FriendPlayerTable.Builder) result.value;
            final FriendPlayerProp prop = FriendPlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
            return prop.getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Player.FriendPlayer fullAttr, @NotNull Player.FriendPlayer changeAttr) {
            final PlayerActor playerActor = (PlayerActor) actor;
            TcaplusDb.FriendPlayerTable.Builder request = TcaplusDb.FriendPlayerTable.newBuilder()
                    .setPlayerId(playerActor.getPlayerId());
            if (fullAttr != null) {
                request.setFullAttr(fullAttr);
                MonitorUnit.DB_PLAYER_FULL_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_FRIEND).inc();
            } else {
                MonitorUnit.DB_PLAYER_CHANGE_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_FRIEND).inc();
            }
            request.setChangedAttr(changeAttr);
            return request;
        }

    }

    @Override
    public String toString() {
        return "FriendPlayerDbComponent{" +
                "playerId=" + this.getEntityId() +
                '}';
    }
}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.MainCityUpgradeStaticEvent;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.event.EntityEventHandlerHolder;
import com.yorha.common.freqLimitCaller.FreqLimitCaller;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.AchievementProp;
import com.yorha.game.gen.prop.DataRecordUnitProp;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.proto.*;
import com.yorha.proto.Entity.EntityNtfMsg;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.AchievementConfigTemplate;

import java.util.Map;

import static com.yorha.proto.CommonEnum.AchievementState.ACHS_COMPLETE;
import static com.yorha.proto.CommonEnum.AchievementState.ACHS_REWARD;

/**
 * 玩家属性组件
 *
 * <AUTHOR>
 */
public class PlayerPropComponent extends PlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPropComponent.class);

    private PlayerProp playerProp;
    /**
     * cs缓存builder，用于存储变化的cs属性信息
     */
    private PlayerPB.PlayerEntityPB.Builder changedCsBuilder;
    /**
     * cs属性变化的条目
     */
    private int changedCsFieldCount = 0;
    /**
     * 是否正在处理cs消息，是的话这段时间的属性变更不会下发，而是在s2c前一次下发
     */
    private boolean isHandleCsMsg = false;
    /**
     * newEntity 是否已经下发给客户端，为严格控制在new之前不可以下发mod，下线后重置
     */
    private boolean isFullPropNotified;

    /**
     * 延时同步至PlayerCardCache 和 db
     */
    private FreqLimitCaller updatePlayerCardSchedule;

    public PlayerPropComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        playerProp = getOwner().getProp();
        updatePlayerCardSchedule = new FreqLimitCaller(TimerReasonType.UPDATE_PLAYER_CARD_SCHEDULE,
                300000,
                1,
                () -> updatePlayerCard(ownerActor(), getOwner().getProp().getZoneModel().getZoneId(), getPlayerId(), getOwner().getProp(), getOwner().getCityLevel()),
                this.ownerActor(),
                String.valueOf(getEntityId()));
    }

    /**
     * 调用时机
     * 1、所有组件的onRegister结束后，插入db并设置监听
     * 2、创建PlayerEntity中，【initAllComponents】所有组件调用init、postInit前
     * <p>
     * onPropChangeListener可以保证在插入db后调用，所以db可以只关注update
     * 因为Player的特殊性，其会在s2c前主动搜脏下发，所以onPropChangeListener中可能没有脏属性要下发了
     */
    public void setPropertyChangeListener() {
        getOwner().getProp().setListener(new CanStopPropertyChangeListener(this::onPropChangeListener, ownerActor().self()));
    }

    /**
     * 能否下发属性dirty变更给客户端
     */
    private boolean canSendDirtyToClient() {
        if (!isFullPropNotified) {
            return false;
        }
        if (getOwner().isDestroy()) {
            return false;
        }
        return getOwner().isOnline();
    }

    /**
     * 登录最后调用，会下发全量 player prop
     */
    public void ntfFullPlayerPropAtLogin() {
        if (!getOwner().isOnline()) {
            LOGGER.error("player not online,but ntfFullPlayerPropAtLogin {}", getOwner());
            return;
        }
        // 获取全量cs属性数据
        PlayerPB.PlayerEntityPB.Builder fullCsBuilder = playerProp.getCopyCsBuilder();

        // 填充到EntityAttr
        EntityAttr.Builder playerAttrBuilder = EntityAttr.newBuilder();
        playerAttrBuilder.setPlayerAttr(fullCsBuilder)
                .setEntityType(EntityType.ET_Player)
                .setEntityId(getEntityId());

        // 填充到真正下发的EntityNtf
        EntityNtfMsg.Builder ntfBuilder = EntityNtfMsg.newBuilder();
        ntfBuilder.addNewEntities(playerAttrBuilder);

        // 真正发送
        getOwner().sendMsgToClient(MsgType.ENTITYNTFMSG, ntfBuilder.build());

        isFullPropNotified = true;

        clearCsDirty();
        this.getOwner().getDbComponent().saveDirty();
        this.playerProp.unMarkAll();
    }

    private void collectCsDirty() {
        if (!isFullPropNotified) {
            // 还没完整推送过，就没必要采集脏数据了
            return;
        }
        if (changedCsBuilder == null) {
            changedCsBuilder = PlayerPB.PlayerEntityPB.newBuilder();
        }
        changedCsFieldCount += playerProp.copyChangeToCs(changedCsBuilder);
    }

    private void sendCsDirty() {
        if (!canSendDirtyToClient()) {
            return;
        }
        if (changedCsBuilder == null) {
            return;
        }
        if (changedCsFieldCount <= 0) {
            return;
        }
        if (isHandleCsMsg) {
            // 处理cs消息中，则只搜脏，在s2c前一次性推送(finishHandleCsMsg)
            return;
        }
        // 不是cs导致的属性变化，需要主动推送

        // 填充到EntityAttr
        EntityAttr.Builder playerAttrBuilder = EntityAttr.newBuilder();
        playerAttrBuilder.setPlayerAttr(changedCsBuilder)
                .setEntityType(EntityType.ET_Player)
                .setEntityId(getEntityId());
        // 填充到真正下发的EntityNtf
        EntityNtfMsg.Builder ntfBuilder = EntityNtfMsg.newBuilder();
        ntfBuilder.addModEntities(playerAttrBuilder);
        // 真正发送
        getOwner().sendMsgToClient(MsgType.ENTITYNTFMSG, ntfBuilder.build());
        // 完事，清空所有内存中的缓存
        clearCsDirty();

    }

    private void clearCsDirty() {
        changedCsBuilder = null;
        changedCsFieldCount = 0;
    }

    @Override
    public void afterLogout() {
        // 下线后，重置
        isFullPropNotified = false;
    }

    public void beginHandleCsMsg() {
        if (isHandleCsMsg) {
            // 确保没有漏置
            WechatLog.error("beginHandleCsMsg {}", getPlayerId());
            return;
        }
        isHandleCsMsg = true;
    }

    /**
     * 在发送s2c消息前，将c2s到s2c这段时间的属性变更一次下发
     */
    public void finishHandleCsMsg() {
        if (!isHandleCsMsg) {
            // 有可能是cs序列化解析抛了异常
            LOGGER.warn("finishHandleCsMsg fail, isHandleCsMsg is false {}", getPlayerId());
            return;
        }
        isHandleCsMsg = false;
        onPropChangeListener();
    }

    /**
     * 属性系统变更的回调信息
     */
    public void onPropChangeListener() {
        // 一次c2s导致了属性变化，触发了propListener，但可能在s2c下发前finishHandleCsMsg已经主动搜脏下发了，所以这里可能没脏了
        if (!playerProp.hasAnyMark()) {
            return;
        }
        if (getOwner().isDestroy()) {
            LOGGER.error("PlayerPropComponent onPropChangeListener onDestroy {} has dirty data!", getOwner());
            return;
        }
        this.collectCsDirty();
        this.getOwner().getDbComponent().saveDirty();
        this.playerProp.unMarkAll();
        this.sendCsDirty();
    }

    static {
        EntityEventHandlerHolder.register(MainCityUpgradeStaticEvent.class, (e) -> e.getPlayer().getPlayerPropComponent().updatePlayerCardCache(false));
    }

    public static void updatePlayerCard(PlayerActor actor, int zoneId, long playerId, PlayerProp prop, int cityLevel) {
        StructPB.PlayerCardInfoPB.Builder pb = StructPB.PlayerCardInfoPB.newBuilder();
        Struct.PlayerCardInfo.Builder db = Struct.PlayerCardInfo.newBuilder();
        DataRecordUnitProp recordsV = prop.getPlayerDataRecord().getRecordsV(CommonEnum.DataRecordType.DRT_KILL_SCORE_VALUE);
        if (recordsV != null) {
            pb.setKillScore(recordsV.getValue());
            db.setKillScore(recordsV.getValue());
        }
        int completeGoldAch = 0;
        int completeSilverAch = 0;
        int completeCopperAch = 0;
        final Map<Integer, AchievementConfigTemplate> achievementConfigTemplateMap = ResHolder.getInstance().getMap(AchievementConfigTemplate.class);
        for (AchievementProp achievementProp : prop.getAchievementModel().getAchievements().values()) {
            if ((achievementProp.getState() != ACHS_COMPLETE) && (achievementProp.getState() != ACHS_REWARD)) {
                continue;
            }
            final AchievementConfigTemplate achievementConfigTemplate = achievementConfigTemplateMap.get(achievementProp.getAchievementId());
            if (achievementConfigTemplate == null) {
                continue;
            }
            final int level = achievementConfigTemplate.getLevel();
            switch (level) {
                case 1: {
                    completeGoldAch++;
                    break;
                }
                case 2: {
                    completeSilverAch++;
                    break;
                }
                case 3: {
                    completeCopperAch++;
                    break;
                }
                default:
                    LOGGER.error("PlayerPropComponent updatePlayerCard unknown level={}", level);
            }
        }
        pb.setAchievementGoldNum(completeGoldAch);
        db.setAchievementGoldNum(completeGoldAch);
        pb.setAchievementSilverNum(completeSilverAch);
        db.setAchievementSilverNum(completeSilverAch);
        pb.setAchievementCopperNum(completeCopperAch);
        db.setAchievementCopperNum(completeCopperAch);
        pb.setCityLevel(cityLevel);
        db.setCityLevel(cityLevel);
        db.setPlayerId(prop.getId())
                .setZoneId(zoneId)
                .setCombat(prop.getPlayerPowerInfo().getTotalPower())
                .setClanId(prop.getClan().getClanId())
                .setCitySkinId(prop.getCityModel().getUsingSkin())
                .setKingdomOffice(prop.getKingdomModel().getOfficeId())
                .setLogOff(prop.getBasicInfo().getLogOff());
        pb.setPlayerId(prop.getId())
                .setZoneId(zoneId)
                .setCombat(prop.getPlayerPowerInfo().getTotalPower())
                .setClanId(prop.getClan().getClanId())
                .setCitySkinId(prop.getCityModel().getUsingSkin())
                .setKingdomOffice(prop.getKingdomModel().getOfficeId())
                .setLogOff(prop.getBasicInfo().getLogOff());
        pb.setCardHead(prop.getAvatarModel().getCardHead().getCopyCsBuilder());
        db.setCardHead(prop.getAvatarModel().getCardHead().getCopySsBuilder());
        // 向db
        TcaplusDb.PlayerCardTable.Builder req = TcaplusDb.PlayerCardTable.newBuilder();
        req.setPlayerId(playerId).setCardInfo(db.build());
        actor.tellGameDb(new UpsertAsk<>(req, UpsertOption.getDefaultInstance()));
        // 向cache
        // 如果在停服中 拦截
        if (ServerContext.getServerStopStep() > 1) {
            LOGGER.info("want updatePlayerCard but server is stopping");
            return;
        }
        SsPlayerCard.UpdatePlayerCardCmd msg = SsPlayerCard.UpdatePlayerCardCmd.newBuilder().setCardInfo(pb.build()).build();
        actor.tell(CardHelper.genPlayerCardRef(playerId), msg);
    }

    public void updatePlayerCardCache(boolean isImmediately) {
        if (updatePlayerCardSchedule == null) {
            return;
        }
        // 需要马上执行的
        if (isImmediately) {
            if (updatePlayerCardSchedule.isRunFuture()) {
                updatePlayerCardSchedule.stopTimer();
            }
            updatePlayerCard(ownerActor(), getOwner().getProp().getZoneModel().getZoneId(), getPlayerId(), getOwner().getProp(), getOwner().getCityLevel());
            return;
        }
        updatePlayerCardSchedule.run();
    }

    public void stopUpdatePlayerCard() {
        if (updatePlayerCardSchedule == null) {
            return;
        }
        if (updatePlayerCardSchedule.isRunFuture()) {
            updatePlayerCardSchedule.stopTimer();
        }
        updatePlayerCardSchedule = null;
    }

    @Override
    public void onDestroy() {
        if (updatePlayerCardSchedule == null) {
            return;
        }
        if (updatePlayerCardSchedule.isRunFuture()) {
            updatePlayerCard(ownerActor(), getOwner().getProp().getZoneModel().getZoneId(), getPlayerId(), getOwner().getProp(), getOwner().getCityLevel());
            updatePlayerCardSchedule.stopTimer();
        }
    }
}

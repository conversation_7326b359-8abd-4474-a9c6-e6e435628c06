package com.yorha.cnc.player.gm.command.server;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class SetIsBattleTestServer implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        boolean isBattleServer = true;
        if (args.containsKey("isOpen")) {
            isBattleServer = Integer.parseInt(args.get("isOpen")) == 1;
        }
        ServerContext.getServerDebugOption().setBattleTestServer(isBattleServer);
    }

    @Override
    public String showHelp() {
        return "SetIsBattleTestServer isOpen={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

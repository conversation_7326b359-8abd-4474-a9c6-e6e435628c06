package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerSkynet;


/**
 * 天网
 *
 * <AUTHOR>
 */

@Controller(module = CommonEnum.ModuleEnum.ME_SKYNET)
public class PlayerSkynetController {

    /**
     * 选择进行任务
     */
    @CommandMapping(code = MsgType.PLAYER_SKYNETDOINGTASK_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSkynet.Player_SkynetDoingTask_C2S msg) {
        CommonEnum.SkynetModelType skynetModelType = msg.getSkynetModelType();
        int skynetTaskId = msg.getSkynetTaskId();
        PlayerSkynet.Player_SkynetDoingTask_S2C.Builder builder = PlayerSkynet.Player_SkynetDoingTask_S2C.newBuilder();
        playerEntity.getSkynetComponent().handleDoingTask(skynetModelType, skynetTaskId, builder);
        return builder.build();
    }

    /**
     * 领奖
     */
    @CommandMapping(code = MsgType.PLAYER_SKYNETTAKETASKREWARD_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSkynet.Player_SkynetTakeTaskReward_C2S msg) {
        CommonEnum.SkynetModelType skynetModelType = msg.getSkynetModelType();
        int skynetTaskId = msg.getSkynetTaskId();
        playerEntity.getSkynetComponent().handleTakeReward(skynetModelType, skynetTaskId);
        return PlayerSkynet.Player_SkynetTakeTaskReward_S2C.getDefaultInstance();
    }

    /**
     * 情报点兑换
     */
    @CommandMapping(code = MsgType.PLAYER_SKYNETCHARGE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSkynet.Player_SkynetCharge_C2S msg) {
        playerEntity.getSkynetComponent().handleCharge();
        return PlayerSkynet.Player_SkynetCharge_S2C.getDefaultInstance();
    }

    /**
     * 查看商店
     */
    @CommandMapping(code = MsgType.PLAYER_SKYNETSTOREINFO_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSkynet.Player_SkynetStoreInfo_C2S msg) {
        return playerEntity.getSkynetComponent().getSkynetStoreInfo();
    }


    /**
     * 购买商品
     */
    @CommandMapping(code = MsgType.PLAYER_SKYNETBUYSTORE_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSkynet.Player_SkynetBuyStore_C2S msg) {
        playerEntity.getSkynetComponent().handleBuyStore(msg.getShopId(), msg.getNum());
        return PlayerSkynet.Player_SkynetBuyStore_C2S.getDefaultInstance();
    }

    /**
     * 查询天网野怪坐标
     */
    @CommandMapping(code = MsgType.PLAYER_SKYNETFINDMONSTER_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerSkynet.Player_SkynetFindMonster_C2S msg) {
        return playerEntity.getSkynetComponent().handleFindMonster(msg.getSkynetTaskId(), msg.getSkynetModelType());
    }
}

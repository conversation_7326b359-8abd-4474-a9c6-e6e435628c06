package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;

/**
 * 军团礼物模块: 使用到ss_clan_gift里ss协议的cs协议，可以放到这里~
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_CLAN_GIFT)
public class PlayerClanGiftController {

    /**
     * 领取联盟礼物
     */
    @CommandMapping(code = MsgType.PLAYER_GETCLANGIFT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_GetClanGift_C2S msg) {
        final long giftId = msg.getGiftUniqueId();
        final boolean isGetAll = msg.getIsGetAll();


        PlayerClan.Player_GetClanGift_S2C.Builder response;
        if (isGetAll) {
            response = playerEntity.getClanGiftComponent().takeAllNormalRarityGifts();
        } else {
            response = playerEntity.getClanGiftComponent().takeSingleGift(giftId);
        }
        return response.build();
    }

    /**
     * 清理已领取过的联盟礼物
     */
    @CommandMapping(code = MsgType.PLAYER_CLEARCLANGIFT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_ClearClanGift_C2S msg) {
        playerEntity.getClanGiftComponent().clearTakenOrExpiredNormalGifts(msg.getRarity());
        return PlayerClan.Player_ClearClanGift_S2C.getDefaultInstance();
    }

    /**
     * 领取联盟珍藏礼物
     */
    @CommandMapping(code = MsgType.PLAYER_GETCLANTREASUREGIFT_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, PlayerClan.Player_GetClanTreasureGift_C2S msg) {
        final long treasureId = msg.getTreasureId();
        PlayerClan.Player_GetClanTreasureGift_S2C.Builder response = playerEntity.getClanGiftComponent().takeClanTreasureGift(treasureId);
        return response.build();
    }
}

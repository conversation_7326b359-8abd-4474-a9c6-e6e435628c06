package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 造兵完成事件
 *
 * <AUTHOR>
 */
public class PlayerFinishTrainingSoldierEvent extends PlayerTaskEvent {
    private final int count;
    private final int soldierId;
    private final int preSoldierId;

    public int getCount() {
        return count;
    }

    public int getSoldierId() {
        return soldierId;
    }
    public int getPreSoldierId() {
        return preSoldierId;
    }
    public PlayerFinishTrainingSoldierEvent(PlayerEntity entity, int count, int soldierId, int preSoldierId) {
        super(entity);
        this.count = count;
        this.soldierId = soldierId;
        this.preSoldierId = preSoldierId;
    }

}

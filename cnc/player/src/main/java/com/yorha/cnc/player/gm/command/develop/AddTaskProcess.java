package com.yorha.cnc.player.gm.command.develop;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 添加指定任务进度
 *
 * <AUTHOR>
 */
public class AddTaskProcess implements PlayerGmCommand {
    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {

        String type = args.get("type");
        int id = Integer.parseInt(args.get("id"));
        int value = Integer.parseInt(args.get("value"));
        if (StringUtils.isEmpty(type)) {
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, "任务类型错误，目前支持 1每日任务、2主线任务、3支线任务、4活动任务");
        }
        if (id <= 0 || value <= 0) {
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, "需要指定id和value");
        }
        actor.getEntity().getTaskComponent().addProcessByGm(type, id, value);
    }

    @Override
    public String showHelp() {
        return "AddTaskProcess type={} id={} value={}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_TASK;
    }
}

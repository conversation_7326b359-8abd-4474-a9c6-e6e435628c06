package com.yorha.cnc.player.offline;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PlayerOfflineViewEntity {
    private static final Logger LOGGER = LogManager.getLogger(PlayerOfflineViewEntity.class);

    private Map<Integer, CommonMsg.InnerBuildMsg> innerPositionMap;

    public PlayerOfflineViewEntity(CommonMsg.PlayerOfflineView viewMsg) {
        innerPositionMap = viewMsg.getPoint2BuildMap();
    }

    /**
     * 加载playerEntity
     */
    public static PlayerOfflineViewEntity loadOfflineViewEntity(PlayerActor actor, long playerId) {
        LOGGER.info("{} stage 01. actor={} cur msg={}", LogKeyConstants.GAME_PLAYER_OFFLINE_VIEW, actor, actor.getCurrentEnvelope());

        TcaplusDb.PlayerTable.Builder req = TcaplusDb.PlayerTable.newBuilder();
        req.setPlayerId(playerId);
        // 只抓取部分field
        List<String> fieldList = new ArrayList<>();

        fieldList.add("curZoneId");
        fieldList.add("OfflineView");
        GetOption build = GetOption.newBuilder().withFieldNames(fieldList).withGetAllFields(false).build();

        GetResult<TcaplusDb.PlayerTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req, build));
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.warn("{} stage 11. from db fail {} {}", LogKeyConstants.GAME_PLAYER_OFFLINE_VIEW, actor, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        TcaplusDb.PlayerTable.Builder recordBuilder = ans.value;
        if (recordBuilder == null) {
            LOGGER.warn("{} stage 12. from db fail {} {}", LogKeyConstants.GAME_PLAYER_OFFLINE_VIEW, actor, ans);
            throw new GeminiException(ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
        }
        if (recordBuilder.getCurZoneId() != actor.getZoneId()) {
            throw new MigrateException(recordBuilder.getCurZoneId(), "PlayerOfflineViewEntity loadOfflineViewEntity, find player migrate, playerId={}, old zone={}, cur zone={}",
                    playerId, actor.getZoneId(), recordBuilder.getCurZoneId());
        }
        CommonMsg.PlayerOfflineView offlineView = recordBuilder.getOfflineView();
        return new PlayerOfflineViewEntity(offlineView);
    }

    @Override
    public String toString() {
        return "PlayerOfflineViewEntity{" +
                "innerPositionMap=" + innerPositionMap.size() +
                '}';
    }
}

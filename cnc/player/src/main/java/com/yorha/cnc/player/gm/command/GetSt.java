package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.helper.GmHelper;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class GetSt implements PlayerGmCommand {
    public static final Logger LOGGER = LogManager.getLogger(GetSt.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String key = "统计项";
        int zoneId = actor.getZoneId();
        GmHelper.sendGmNtfMail(zoneId, playerId, key, actor.getOrLoadEntity().getProp().getStatisticModel().toString());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

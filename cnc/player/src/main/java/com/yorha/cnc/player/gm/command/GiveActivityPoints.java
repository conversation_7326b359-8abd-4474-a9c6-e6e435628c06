package com.yorha.cnc.player.gm.command;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.ActivityPointsTemplate;

import java.util.Map;

public class GiveActivityPoints implements PlayerGmCommand {

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        int templateId = Integer.parseInt(args.get("templateId"));
        int count = 1;
        if (args.containsKey("count")) {
            count = Integer.parseInt(args.get("count"));
        }
        ActivityPointsTemplate template = ResHolder.findTemplate(ActivityPointsTemplate.class, templateId);
        if (template == null) {
            throw new GeminiException(ErrorCode.TEMPLATE_NOT_EXIST);
        }

        actor.getEntity().getPointsComponent().gmGivePoints(templateId, count);
    }

    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    @Override
    public String showHelp() {
        return "GiveActivityPoints templateId={value} count={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }
}

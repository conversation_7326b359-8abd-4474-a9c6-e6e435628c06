package com.yorha.cnc.player.task;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivityTaskConf;
import com.yorha.game.gen.prop.ActivityTaskUnitProp;
import com.yorha.game.gen.prop.Int32TaskInfoMapProp;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 活动任务管理，每一个任务活动都会持有一个 ActivityTaskHandler
 */
public class ActivityTaskHandler extends AbstractTaskHandler {

    private final ActivityTaskUnitProp prop;
    /**
     * 这个活动任务配置的地址
     */
    private final String taskPoolName;
    /**
     * 防止重复监听任务事件（和TaskComponent之间有加载时序问题）
     */
    private boolean loaded = false;

    public ActivityTaskHandler(PlayerEntity entity, ActivityTaskUnitProp prop, String taskPoolName, int activityId) {
        super(entity, taskPoolName + "_" + activityId);
        this.prop = prop;
        this.taskPoolName = taskPoolName;
    }

    @Override
    public Int32TaskInfoMapProp getTaskProp() {
        return prop.getTasks();
    }

    @Override
    int getTaskIdById(int configId) {
        ActivityTaskConf taskConf = ResHolder.getResService(ActivityResService.class).getTaskConf(taskPoolName, configId);
        if (taskConf == null) {
            throw new GeminiException("activityTaskConf not found, pool={} id={}", taskPoolName, configId);
        }
        return taskConf.getTaskId();
    }

    @Override
    public void loadAllTask() {
        if (loaded) {
            return;
        }
        loaded = true;
        for (TaskInfoProp value : Lists.newArrayList(getTaskProp().values())) {
            addTaskEventMonitor(value);
        }
    }

    @Override
    public List<IntPairType> checkOrTakeReward(List<Integer> taskId) {
        throw new GeminiException("illegal way.");
    }

    @Override
    public TaskPoolTemplate getTaskTemplate(int configId) {
        return ResHolder.getTemplate(TaskPoolTemplate.class, getTaskIdById(configId));
    }
}

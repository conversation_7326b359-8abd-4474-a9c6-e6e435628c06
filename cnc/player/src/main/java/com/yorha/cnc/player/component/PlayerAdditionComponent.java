package com.yorha.cnc.player.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.addition.PlayerAdditionMgr;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.AdditionItemProp;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.SsSceneDungeon;
import com.yorha.proto.Struct.AdditionSys;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.BuffEffectTemplate;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 玩家加成在player，scenePlayer分别存储
 * 加成更新流程：
 * player侧: player
 * scene侧: player -> scenePlayer
 *
 * <AUTHOR>
 */
public class PlayerAdditionComponent extends PlayerComponent {
    public static final Logger LOGGER = LogManager.getLogger(PlayerAdditionComponent.class);

    private PlayerAdditionMgr additionMgr;

    public PlayerAdditionComponent(PlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        additionMgr = new PlayerAdditionMgr(getOwner());

        // 注册会提供addition的component
        additionMgr.register(getOwner().getPlayerDevBuffComponent());
        additionMgr.register(getOwner().getTechComponent());
        additionMgr.register(getOwner().getVipComponent());
        additionMgr.register(getOwner().getCityDressComponent());
        additionMgr.register(getOwner().getCityNameplateComponent());
        additionMgr.register(getOwner().getCityCommandComponent()); // 指令
        additionMgr.register(getOwner().getInnerBuildRhComponent()); // 内城RH
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        // 同步zone上的加成
//        syncZoneAddition(playerLoginAns.getZoneAdditionSys());
//        refreshAllAddition();
    }

    public void refreshAllAddition() {
        getOwner().getPlayerDevBuffComponent().refreshAdditionCache();
        additionMgr.refreshAllAddition();
    }

    private void syncZoneAddition(AdditionSys newZoneAdditionSys) {
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> changedSourceMap =
                AdditionUtil.getChangedAdditionMapBySource(additionMgr.getZoneAdditionSys(), newZoneAdditionSys);
        LOGGER.info("{} syncZoneAddition changedSourceMap:{}", getOwner(), changedSourceMap);
        // 触发zone加成变更
        for (Map.Entry<CommonEnum.AdditionSourceType, Map<Integer, Long>> entry : changedSourceMap.entrySet()) {
            updateZoneAddition(entry.getKey(), entry.getValue());
        }
    }

    public void removeZoneAdditionBySource(CommonEnum.AdditionSourceType sourceType) {
        Set<Integer> additionIdsBySource = additionMgr.getZoneAdditionIdsBySource(sourceType);
        Map<Integer, Long> additionToRemove = Maps.newHashMap();
        for (Integer id : additionIdsBySource) {
            additionToRemove.put(id, 0L);
        }
        updateZoneAddition(sourceType, additionToRemove);
    }

    /**
     * 获取加成值
     *
     * @param type 加成
     * @return 值
     */
    public long getAddition(BuffEffectType type) {
        if (AdditionUtil.isSceneAddition(type.getNumber())) {
            WechatLog.error("{} getAddition failed in player. id:{}", getOwner(), type.getNumber());
        }
        return additionMgr.getAddition(type.getNumber());
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionId   加成id
     */
    public void updateAddition(AdditionProviderType providerType, int additionId) {
        if (additionId > 0) {
            additionMgr.updateAdditionByProvider(providerType, Lists.newArrayList(additionId));
        } else {
            LOGGER.error("player:{}, updateAddition, providerType:{}, additionId:{} invalid", getOwner(), providerType, additionId);
        }
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionIds  加成id list
     */
    public void updateAddition(AdditionProviderType providerType, @NotNull List<Integer> additionIds) {
        additionMgr.updateAdditionByProvider(providerType, additionIds);
    }

    public void updateAdditionFromScene(SsPlayerMisc.UpdateAdditionFromSceneCmd ask) {
        additionMgr.updateAdditionBySource(ask.getCmd().getSource(), ask.getCmd().getAdditionMap());
    }

    public void updateAdditionFromClan(SsPlayerClan.OnClanAdditionUpdateCmd ask) {
        additionMgr.updateAdditionBySource(ask.getSource(), ask.getAdditionsMap());
    }

    /**
     * 更新加成
     *
     * @param additionSys 加成
     */
    public void updateAddition(AdditionSys additionSys) {
        additionMgr.updateAdditionBySys(additionSys);
    }

    public void removeAdditionBySource(CommonEnum.AdditionSourceType sourceType) {
        Set<Integer> additionIdsBySource = additionMgr.getAdditionIdsBySource(sourceType);
        Map<Integer, Long> additionToRemove = Maps.newHashMap();
        for (Integer id : additionIdsBySource) {
            additionToRemove.put(id, 0L);
        }
        additionMgr.updateAdditionBySource(sourceType, additionToRemove);
    }

    public static List<Integer> getAdditionIds(List<IntPairType> buffPairList) {
        List<Integer> ids = Lists.newArrayList();
        for (IntPairType intPairType : buffPairList) {
            CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(intPairType.getKey());
            if (buffEffectType != null) {
                ids.add(intPairType.getKey());
            }
        }
        return ids;
    }

    public void fullAdditionByGm() {
        Map<Integer, Long> additionMap = Maps.newHashMap();
        for (BuffEffectTemplate template : ResHolder.getInstance().getListFromMap(BuffEffectTemplate.class)) {
            additionMap.put(template.getId(), 100L);
        }
        additionMgr.updateAdditionBySource(CommonEnum.AdditionSourceType.AST_SYS, additionMap);
        additionMgr.updateAdditionBySource(CommonEnum.AdditionSourceType.AST_BUFF, additionMap);
    }

    public void updateZoneAddition(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        additionMgr.updateZoneAdditionToPlayer(sourceType, additions, map -> AdditionUtil.updateAdditionSys(additionMgr.getZoneAdditionSys(), sourceType, map));
    }

    public AdditionSysProp getAllAdditionCopy(AdditionSysProp prop) {
        AdditionSysProp sysProp = new AdditionSysProp();
        for (Map.Entry<Integer, AdditionProp> entry : prop.getAddition().entrySet()) {
            AdditionProp p = new AdditionProp();
            long total = 0;
            for (Map.Entry<Integer, AdditionItemProp> itemPropEntry : entry.getValue().getAdditionItems().entrySet()) {
                long value = itemPropEntry.getValue().getValue();
                AdditionItemProp i = new AdditionItemProp();
                i.mergeFromSs(itemPropEntry.getValue().getCopySsBuilder().build());
                p.putAdditionItemsV(i);
                total += value;
            }
            if (total > 0) {
                p.setTotalValue(total);
                p.setAdditionId(entry.getKey());
                sysProp.putAdditionV(p);
            }
        }
        return sysProp;
    }
}

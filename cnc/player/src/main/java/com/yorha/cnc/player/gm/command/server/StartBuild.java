package com.yorha.cnc.player.gm.command.server;

import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;


/**
 * 对着某个partId上的建筑，改建成我们需要的
 *
 * <AUTHOR>
 */
public class StartBuild implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(StartBuild.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        if (args.get("type") == null) {
            LOGGER.info("type not exist");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (args.get("mapBuildingId") == null) {
            LOGGER.info("mapBuildingId not exist");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        CommonEnum.MapBuildingType type = CommonEnum.MapBuildingType.forNumber(Integer.parseInt(args.get("type")));
        if (type == null) {
            LOGGER.info("type not exist");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        long mapBuildingId = Long.parseLong(args.get("mapBuildingId"));
        switch (type) {
            case MBT_MAIN_BASE:
            case MBT_COMMAND_CENTER:
            case MBT_CLAN_FORTRESS:
                break;
            default:
                LOGGER.info("type not exist");
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        PlayerClan.Player_ConstructClanStrongholdBuilding_C2S c2sMsg =
                PlayerClan.Player_ConstructClanStrongholdBuilding_C2S.newBuilder()
                        .setMapBuildingId(mapBuildingId)
                        .setType(type)
                        .build();
        actor.getEntity().getPlayerClanComponent().tryRebuild(c2sMsg);
    }
}

package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.*;
import com.yorha.game.gen.prop.TaskInfoProp;
import res.template.TaskPoolTemplate;

import java.util.List;

/**
 * 拥有x级x品x星的英雄x个
 * param1：等级
 * param2：品质
 * param3：星级
 * param4：个数
 * 计算规则：等级>=param1,品质=param2,星级>=param3
 *
 * <AUTHOR>
 */
public class PlayerHeroPropChecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(PlayerHeroStarChangeEvent.class.getSimpleName(),
            PlayerHeroLevelUpEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName(),
            PlayerHeroUnLockEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int param1 = taskParams.get(0);
        int param2 = taskParams.get(1);
        int param3 = taskParams.get(2);
        int param4 = taskParams.get(3);

        int prefixHeroNum = event.getPlayer().getHeroComponent().getPlayerHeroPropTaskProcess(param1, param2, param3);
        prop.setProcess(Math.min(prefixHeroNum, param4));

        return prop.getProcess() >= param4;
    }
}

package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.activity.PlayerEventListener;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.PlayerManualCityFallEvent;
import com.yorha.cnc.player.event.PlayerMoveCityFixedEvent;
import com.yorha.cnc.player.event.PlayerMoveCityRandomEvent;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.Struct;

import java.util.List;

/**
 * 次日送英雄的专属unit
 */
public class PlayerHeroComeUnit extends BasePlayerActivityUnit implements PlayerEventListener {

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerManualCityFallEvent.class,
            PlayerMoveCityFixedEvent.class,
            PlayerMoveCityRandomEvent.class
    );

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_HERO_COME, (owner, prop, template) ->
                new PlayerHeroComeUnit(owner, prop.getSpecUnit())
        );
    }

    public PlayerHeroComeUnit(PlayerActivity ownerActivity, ActivityUnitProp unitProp) {
        super(ownerActivity, unitProp);
    }

    /**
     * 以落城点为中心的圆上面，至少有90度的扇面是在地图内的，所以转4个点就肯定会落在地图内了
     */
    private static final List<Integer> ANGLE_OFFSETS = ImmutableList.of(0, 180, 90, 270);

    private void init() {
        SsScenePlayer.FetchMainCityInfoAsk ask = SsScenePlayer.FetchMainCityInfoAsk.newBuilder().setPlayerId(player().getPlayerId()).build();
        SsScenePlayer.FetchMainCityInfoAns ans = player().ownerActor().callBigScene(ask);

        if (ans.hasPosition()) {
            randomSetBornPoint(ans.getPosition());
        } else {
            WechatLog.error("HeroComeUnit init but no mainCity Position: {}", player());
        }
    }

    private void randomSetBornPoint(Struct.Point center) {
        randomSetBornPoint(center.getX(), center.getY());
    }

    private void randomSetBornPoint(int centerX, int centerY) {
        int cityViewEdge = 1000; //ResHolder.getResService(FogMapResService.class).getCityViewEdge();
        int radius = cityViewEdge / 2;

        Point born = randomAvailablePoint(centerX, centerY, radius);
        unitProp.getHeroComeUnit().getBornPoint()
                .setX(born.getX())
                .setY(born.getY());
    }

    private static Point randomAvailablePoint(int centerX, int centerY, int radius) {
        int angle = RandomUtils.nextInt(0, 360);

        for (Integer offset : ANGLE_OFFSETS) {
            int angleWithOffset = angle + offset;
            if (angleWithOffset >= 360) {
                angleWithOffset -= 360;
            }
            Point born = calcPoint(centerX, centerY, radius, angleWithOffset);
            // 不在地图内要切换位置
            if (SceneMapDataTemplateService.isPointInsideBigScene(born)) {
                return born;
            }
        }
        throw new GeminiException("HeroComeActivity randomAvailablePoint failed..");
    }

    private static Point calcPoint(int centerX, int centerY, int radius, int angle) {
        double radians = Math.toRadians(angle);
        double sin = Math.sin(radians);
        double cos = Math.cos(radians);
        return Point.valueOf((float) (radius * cos + centerX), (float) (radius * sin + centerY));
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
    }

    @Override
    public void onMigrate() {

    }

    @Override
    public void onExpire() {
    }

    @Override
    public void forceOffImpl() {

    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public List<Class<? extends PlayerEvent>> followList() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public void onEvent(PlayerEvent event) {
        if (event instanceof PlayerManualCityFallEvent) {
            PlayerManualCityFallEvent e = (PlayerManualCityFallEvent) event;
            randomSetBornPoint(e.getNewPoint().getX(), e.getNewPoint().getY());
        } else if (event instanceof PlayerMoveCityFixedEvent) {
            PlayerMoveCityFixedEvent e = (PlayerMoveCityFixedEvent) event;
            randomSetBornPoint(e.getPoint().getX(), e.getPoint().getY());
        } else if (event instanceof PlayerMoveCityRandomEvent) {
            PlayerMoveCityRandomEvent e = (PlayerMoveCityRandomEvent) event;
            randomSetBornPoint(e.getPoint().getX(), e.getPoint().getY());
        }
    }
}

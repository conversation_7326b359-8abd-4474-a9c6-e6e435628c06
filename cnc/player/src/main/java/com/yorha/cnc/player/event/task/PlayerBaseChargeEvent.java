package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 直充完成的事件
 * <p>
 * 直充没有明确的回调，所以并不能100%明确本event抛出的时候，具体完成了几次充值，充值了多少数额
 * <p>
 * 用本event来做充值次数计数可行但不完美
 */
public class PlayerBaseChargeEvent extends PlayerTaskEvent {

    private final long saveAmtBefore;
    private final long saveAmtNow;

    public PlayerBaseChargeEvent(PlayerEntity entity, long saveAmtBefore, long saveAmtNow) {
        super(entity);
        this.saveAmtBefore = saveAmtBefore;
        this.saveAmtNow = saveAmtNow;
    }

    public long getSaveAmtBefore() {
        return saveAmtBefore;
    }

    public long getSaveAmtNow() {
        return saveAmtNow;
    }

    public long incSaveAmt() {
        return Math.max(0, saveAmtNow - saveAmtBefore);
    }
}

package com.yorha.cnc.player.gm.command.develop;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.gm.PlayerGmCommand;
import com.yorha.cnc.player.gm.command.Hero;
import com.yorha.common.enums.reason.HeroAddLevelReason;
import com.yorha.common.enums.reason.HeroStarUpReason;
import com.yorha.common.enums.reason.HeroUnlockReason;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.HeroRhTemplate;
import res.template.ItemTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * 一键变强
 *
 * <AUTHOR>
 */
public class GiveMePower implements PlayerGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(GiveMePower.class);

    @Override
    public void execute(PlayerActor actor, long playerId, Map<String, String> args) {
        String type = args.get("type");

        PlayerEntity playerEntity = actor.getEntity();
        try {
            resourcePower(playerEntity);
        } catch (Exception e) {
            LOGGER.error("GiveMePower error", e);
        }

        try {
            itemPower(playerEntity);
        } catch (Exception e) {
            LOGGER.error("GiveMePower error", e);
        }

        try {
            soldierPower(playerEntity);
        } catch (Exception e) {
            LOGGER.error("GiveMePower error", e);
        }

        try {
            heroPower(playerEntity);
        } catch (Exception e) {
            LOGGER.error("GiveMePower error", e);
        }

        try {
            unlockAllTech(playerEntity);
        } catch (Exception e) {
            LOGGER.error("GiveMePower error", e);
        }

        if ("memory".equals(type)) {

            try {
                // 塞满活动
                playerEntity.getActivityComponent().fullActivityByGm();
            } catch (Exception e) {
                LOGGER.error("FullMemory error", e);
            }

            try {
                // 塞满加成
                playerEntity.getAddComponent().fullAdditionByGm();
            } catch (Exception e) {
                LOGGER.error("FullMemory error", e);
            }

            try {
                // 塞满任务
                playerEntity.getTaskComponent().fullTaskByGm();
            } catch (Exception e) {
                LOGGER.error("FullMemory error", e);
            }

            try {
                // 塞满邮件
                playerEntity.getMailComponent().fullMailByGm();
            } catch (Exception e) {
                LOGGER.error("FullMemory error", e);
            }

        }
    }

    private void unlockAllTech(PlayerEntity playerEntity) {
        playerEntity.getTechComponent().unLockAllTechByGm();
    }

    private void itemPower(PlayerEntity playerEntity) {
        // 添加需要跳过的effect类型
        List<Integer> needSkipEffectType = Lists.newArrayList();
        needSkipEffectType.add(CommonEnum.ItemUseType.CLAN_GIFT_VALUE);
        needSkipEffectType.add(CommonEnum.ItemUseType.CLAN_SCORE_VALUE);
        needSkipEffectType.add(CommonEnum.ItemUseType.PERSONAL_CLAN_SCORE_VALUE);

        ResHolder.getInstance().getListFromMap(ItemTemplate.class).forEach(it -> {
            // 特殊英雄不要给道具
            if (it.getEffectType() == CommonEnum.ItemUseType.RECRUIT_HERO_VALUE) {
                int heroId = it.getEffectValue();
                HeroRhTemplate valueFromMap = ResHolder.getInstance().findValueFromMap(HeroRhTemplate.class, heroId);
                if (valueFromMap == null) {
                    return;
                }

            }
            if (needSkipEffectType.contains(it.getEffectType())) {
                return;
            }
            playerEntity.getItemComponent().addItem(it.getId(), 999, CommonEnum.Reason.ICR_GM, "GiveMePower");
        });
        LOGGER.debug("player:{} use give me power -> all item", playerEntity.getEntityId());
    }

    private void resourcePower(PlayerEntity playerEntity) {
        for (CommonEnum.CurrencyType type : CommonEnum.CurrencyType.values()) {
            playerEntity.getPurseComponent().give(type, 999999999, CommonEnum.Reason.ICR_GM, "");
        }
        LOGGER.debug("player:{} use give me power -> all resource", playerEntity.getEntityId());
    }

    private void soldierPower(PlayerEntity playerEntity) {
        LOGGER.debug("{} try rich", playerEntity);
        SsScenePlayer.PlayerAddSoldierAsk.Builder builder = SsScenePlayer.PlayerAddSoldierAsk.newBuilder();
        builder.setPlayerId(playerEntity.getEntityId()).setAddNum(200000).setReason(SoldierNumChangeReason.gm.toString());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(1001).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(1002).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(1003).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(1004).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(1005).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(2001).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(2002).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(2003).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(2004).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(2005).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(3001).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(3002).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(3003).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(3004).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(3005).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(4001).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(4002).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(4003).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(4004).build());
        playerEntity.ownerActor().callBigScene(builder.setSoldierId(4005).build());
        LOGGER.debug("player:{} use give me power -> all soldier", playerEntity.getEntityId());
    }


    private void heroPower(PlayerEntity playerEntity) {
        ResHolder.getInstance().getListFromMap(HeroRhTemplate.class).forEach(it -> {
            if (!playerEntity.getHeroComponent().hasHeroProp(it.getId())) {
                playerEntity.getHeroComponent().initHero(it.getId(), HeroUnlockReason.GM_UNLOCK);
            }
        });
        Collection<PlayerHeroProp> list = playerEntity.getHeroComponent().getAllHero();
        for (PlayerHeroProp heroProp : list) {
            playerEntity.getHeroComponent().handleLevelUp2Max(heroProp, HeroAddLevelReason.GM_ADD_EXP);
            playerEntity.getHeroComponent().handleStageUp2Max(heroProp, HeroStarUpReason.GM_ADD_STAR);
            Hero.upgrade(playerEntity, heroProp);
            playerEntity.getHeroComponent().postHeroPropChange(heroProp.getHeroId(), false);
        }

        LOGGER.debug("player:{} use give me power -> all hero", playerEntity.getEntityId());
    }

    @Override
    public String showHelp() {
        return "GiveMePower";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

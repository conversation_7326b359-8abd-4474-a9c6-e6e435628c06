package com.yorha.cnc.player.activity.unit;

import com.google.common.collect.ImmutableSet;
import com.yorha.cnc.player.activity.ActivityUnitFactory;
import com.yorha.cnc.player.activity.BasePlayerActivityUnit;
import com.yorha.cnc.player.activity.PlayerActivity;
import com.yorha.cnc.player.component.PlayerPointsComponent;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ActivityScoreRewardUnitProp;
import com.yorha.game.gen.prop.ActivityUnitProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncActivityScore;
import res.template.ActivityScoreRewardTemplate;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 很常见的活动单元：获得积分，满足一定积分领取一个奖励
 * <p>
 * 想复用但是又有点逻辑不一样的，优先考虑另写，而不是在这里堆shit
 */
public class PlayerActivityScoreRewardUnit extends BasePlayerActivityUnit implements PlayerPointsComponent.Listener {
    private static final Logger LOGGER = LogManager.getLogger(PlayerActivityScoreRewardUnit.class);

    private final String scoreId;

    static {
        ActivityUnitFactory.register(CommonEnum.ActivityUnitType.AUT_SCORE_REWARD, (owner, prop, template) ->
                new PlayerActivityScoreRewardUnit(owner, prop.getCommonScoreRewardUnit(), template.getScoreRewardUnitId())
        );
    }

    public PlayerActivityScoreRewardUnit(PlayerActivity activity, ActivityUnitProp prop, String scoreId) {
        super(activity, prop);
        this.scoreId = scoreId;
    }

    private void init() {
        final ActivityScoreRewardUnitProp prop = unitProp.getScoreRewardUnit();
        final int level = ownerActivity.getPlayer().getCityLevel();
        final int cityLevel = (level <= 0) ? 1 : level;
        if (!ZoneContext.isServerOpen()) {
            throw new GeminiException("server is not open");
        }
        final long serverOpenTsMs = ZoneContext.getServerOpenTsMs();
        final long activityStartTsMs = TimeUnit.SECONDS.toMillis(ownerActivity.getProp().getStartTsSec());
        final int serverOpenDay;
        if (serverOpenTsMs > activityStartTsMs) {
            // 兼容移民的情况。移民后，自创角起的活动，开启时间是出生服的创角时间，比当前服的开服时间小，所以直接取0
            // 定期分服循环也会有这个问题，算出来的活动开启时间早于开服时间
            serverOpenDay = 0;
        } else {
            serverOpenDay = (int) TimeUtils.getAbsNatureDaysBetween(serverOpenTsMs, activityStartTsMs);
        }
        prop.setCityLevel(cityLevel).setServerOpenDay(serverOpenDay);
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
        ownerActivity.getPlayer().getActivityComponent().watchScore(this);
    }

    @Override
    public void onMigrate() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public void handleTakeReward(com.yorha.proto.PlayerActivity.ActivityUnitRewardKey key, com.yorha.proto.PlayerActivity.Player_ActivityTakeReward_S2C.Builder rsp) {
        final int scoreBoxId = key.getScoreBoxId();
        ActivityScoreRewardUnitProp prop = unitProp.getScoreRewardUnit();
        boolean alreadyTaken = prop.getTakenBoxIds().contains(scoreBoxId);
        if (alreadyTaken) {
            // 已经领取
            throw new GeminiException(ErrorCode.REWARD_ALREADY_OBTAIN);
        }
        ActivityResService asr = ResHolder.getResService(ActivityResService.class);
        ActivityScoreRewardTemplate template = asr.findScoreRewardTemplate(scoreId, scoreBoxId, prop.getCityLevel(), prop.getServerOpenDay());
        if (template == null) {
            throw new GeminiException(ErrorCode.REWARD_NOT_EXIST);
        }

        // 判断积分足够
        if (prop.getScore() < template.getScore()) {
            throw new GeminiException("score not enough.");
        }

        // 标记领取
        prop.addTakenBoxIds(scoreBoxId);

        // 给奖励
        AssetPackage reward = AssetPackage.builder().plusItems(template.getRewardPairList()).build();
        ownerActivity.getPlayer().getAssetComponent().give(reward, CommonEnum.Reason.ICR_ACTIVITY_SCORE_REWARD, String.valueOf((ownerActivity.getActivityId())));

        rsp.setReward(reward.toPb());

        sendTakeRewardQlog(template);
    }

    @Override
    public void forceOffImpl() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    private void sendTakeRewardQlog(ActivityScoreRewardTemplate template) {
        try {
            QlogCncActivityScore.init(ownerActivity.getPlayer().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("collect_act_reward")
                    .setSubjectID("")
                    .setIcount(0)
                    .setActivityScore(template.getScore())
                    .setActivityScoreID(scoreId)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendQlogCncActivityScore failed: ", e);
        }
    }

    @Override
    public void onExpire() {
        ownerActivity.getPlayer().getActivityComponent().unwatchScore(this);
    }

    @Override
    public boolean isFinished() {
        ImmutableSet<Integer> allBoxIds = ResHolder.getResService(ActivityResService.class).findAllBoxIds(scoreId);
        return new HashSet<>(unitProp.getScoreRewardUnit().getTakenBoxIds()).containsAll(allBoxIds);
    }

    public String getScoreId() {
        return scoreId;
    }

    public void addScore(int num, String action, String subjectId) {
        if (num <= 0) {
            LOGGER.error("addScore num={} action={} subjectId={} activityId={}", num, action, subjectId, ownerActivity.getActivityId());
            return;
        }
        int scoreAfter = unitProp.getScoreRewardUnit().getScore() + num;
        unitProp.getScoreRewardUnit().setScore(scoreAfter);

        try {
            QlogCncActivityScore.init(ownerActivity.getPlayer().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction(action)
                    .setSubjectID(subjectId)
                    .setIcount(num)
                    .setActivityScore(scoreAfter)
                    .setActivityScoreID(scoreId)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendQlogCncActivityScore addScore failed: ", e);
        }

    }

    @Override
    public Set<Integer> focusPointsIds() {
        ActivityResService asr = ResHolder.getResService(ActivityResService.class);
        return asr.getScoreRewardPointsIds(scoreId);
    }

    @Override
    public void addPoints(int pointsTemplateId, long addPoints) {
        addScore((int) addPoints, "complete_event_gain_score", String.valueOf(pointsTemplateId));
    }
}

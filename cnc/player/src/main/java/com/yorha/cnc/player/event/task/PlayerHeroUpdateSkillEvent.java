package com.yorha.cnc.player.event.task;

import com.yorha.cnc.player.PlayerEntity;

/**
 * 英雄升级
 *
 * <AUTHOR>
 */
public class PlayerHeroUpdateSkillEvent extends PlayerTaskEvent {
    private final int heroId;
    private final int skillId;

    public PlayerHeroUpdateSkillEvent(PlayerEntity player, int heroId, int skillId) {
        super(player);
        this.heroId = heroId;
        this.skillId = skillId;
    }

    public int getHeroId() {
        return heroId;
    }

    public int getSkillId() {
        return skillId;
    }
}

package com.yorha.cnc.player.chat.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.player.PlayerActor;
import com.yorha.cnc.player.chat.ChatPlayerEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.cache.AutoLoadSyncCache;
import com.yorha.common.cache.CacheMgr;
import com.yorha.common.cache.action.CacheAddAction;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.ActorChangeAttrUpsertStrategy;
import com.yorha.common.dbactor.DbTaskProxy;
import com.yorha.common.dbactor.DefaultDbOperationStrategyImpl;
import com.yorha.common.enums.CacheUsageEnum;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ChatPlayerProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import static com.yorha.cnc.player.PlayerActor.PLAYER_ENTITY_CHAT;

/**
 * <AUTHOR>
 */
public class ChatPlayerDbComponent extends ChatPlayerComponent {
    private static final Logger LOGGER = LogManager.getLogger(ChatPlayerDbComponent.class);
    /**
     * 操作增删改查的代理
     */
    private final DbTaskProxy dbTaskProxy;

    /**
     * 全服聊天分享邮件缓存
     * 100条 1s
     * LFU
     */
    private static final AutoLoadSyncCache<PlayerChat.Player_GetSharedMailContent_C2S, StructMailPB.MailContentPB> CHAT_MAIL_CONTENT_CACHE = CacheMgr.buildAutoLoadCache(
            100,
            1000,
            new CacheAddAction<>() {
                @Override
                public StructMailPB.MailContentPB execute(PlayerChat.Player_GetSharedMailContent_C2S msg, GameActorWithCall actor) {
                    return loadChatMailContent(actor, msg.getChatSession().getChatChannelId(), msg.getChatSession().getChannelType(), msg.getMessageId());
                }

                private StructMailPB.MailContentPB loadChatMailContent(GameActorWithCall actor, final String channelId, final CommonEnum.ChatChannel channel, final long messageId) {
                    LOGGER.info("ChatPlayerDbComponent CHAT_MAIL_CONTENT_CACHE loadChatMailContent, channelId={}, channel={}, messageId={}", channelId, channel, messageId);
                    if (messageId <= 0) {
                        throw new GeminiException(ErrorCode.CHAT_DB_OPERATION_FAILED);
                    }
                    // 请求日志页
                    TcaplusDb.ChatTable.Builder req = TcaplusDb.ChatTable.newBuilder()
                            .setMsgIndex(messageId)
                            .setChannelType(channel.getNumber())
                            .setChannelId(channelId);
                    try {
                        GetResult<TcaplusDb.ChatTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
                        if (!DbUtil.isOk(ans.getCode())) {
                            LOGGER.error("ChatPlayerDbComponent CHAT_MAIL_CONTENT_CACHE loadChatMailContent, db fail, code={}", ans.getCode());
                            throw new GeminiException(ErrorCode.CHAT_DB_OPERATION_FAILED);
                        }
                        TcaplusDb.ChatTable.Builder chatMessage = ans.value;
                        if (chatMessage.getMsg().getType() != CommonEnum.MessageType.MT_MAIL_SHARE) {
                            throw new GeminiException(ErrorCode.CHAT_DB_OPERATION_FAILED, "chatMsg type not sharedMail");
                        }
                        return chatMessage.getMsg().getMessageData().getSharedMail().getContent();
                    } catch (Exception e) {
                        LOGGER.error("ChatPlayerDbComponent CHAT_MAIL_CONTENT_CACHE loadChatMailContent, exception=", e);
                        throw e;
                    }
                }
            }, CacheUsageEnum.CHAT);

    public ChatPlayerDbComponent(ChatPlayerEntity owner, Player.ChatPlayer changedProp) {
        super(owner);
        final ChatPlayerActorUpsertStrategy strategy = new ChatPlayerActorUpsertStrategy(changedProp != null ? changedProp : Player.ChatPlayer.getDefaultInstance());
        this.dbTaskProxy = DbTaskProxy.newBuilder()
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .name(this.getOwner().toString())
                .upsert(strategy)
                .owner(ownerActor())
                .limitTimerOwner(ownerActor())
                .entityId("Chat" + this.getEntityId()) // chatPlayer、friendPlayer、player 共用entityId且同属于PlayerActor，避免定时器重复
                .intervalMs(DbLimitConstants.CHAT_PLAYER_DB_LIMIT_INTERVAL_MS)
                .build();
    }

    public StructMailPB.MailContentPB getChatMailContent(PlayerChat.Player_GetSharedMailContent_C2S msg) {
        try {
            return CHAT_MAIL_CONTENT_CACHE.getData(msg, this.ownerActor());
        } catch (GeminiException e) {
            throw e;
        } catch (Throwable throwable) {
            throw new GeminiException("ChatPlayerDbComponent getChatMailContent fail", throwable);
        }
    }

    /**
     * insert
     */
    public void saveDb() {
        this.dbTaskProxy.insert();
    }

    /**
     * update
     */
    public void saveChangeToDb() {
        // 更新
        this.dbTaskProxy.update();
    }

    /**
     * [同步]销毁前落脏
     */
    public void saveOnDestroy() {
        if (!getOwner().isDestroy()) {
            WechatLog.error("ChatPlayerDbComponent endDb {} is not destroy!", getOwner());
        }
        this.saveAndStop();
    }

    /**
     * [同步]完整落库后停止db操作托管
     */
    public void saveAndStop() {
        if (dbTaskProxy.isProxyStop()) {
            return;
        }
        // 先强制存盘一次
        this.dbTaskProxy.saveDbSync();
        this.dbTaskProxy.stop();
    }

    private static class ChatPlayerActorUpsertStrategy extends ActorChangeAttrUpsertStrategy<Player.ChatPlayer> {

        ChatPlayerActorUpsertStrategy(GeneratedMessageV3 msg) {
            super(msg);
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            final PlayerActor playerActor = (PlayerActor) actor;
            return playerActor.getChatPlayerEntity().getProp().copyChangeToDb((Player.ChatPlayer.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected Player.ChatPlayer buildFullAttrSaveData(AbstractActor actor) {
            final PlayerActor playerActor = (PlayerActor) actor;
            return playerActor.getChatPlayerEntity().getProp().getCopyDbBuilder().build();
        }

        @Override
        protected Player.ChatPlayer buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            final TcaplusDb.ChatPlayerTable.Builder recordBuilder = (TcaplusDb.ChatPlayerTable.Builder) result.value;
            final ChatPlayerProp prop = ChatPlayerProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
            return prop.getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Player.ChatPlayer fullAttr, @NotNull Player.ChatPlayer changeAttr) {
            final PlayerActor playerActor = (PlayerActor) actor;
            TcaplusDb.ChatPlayerTable.Builder request = TcaplusDb.ChatPlayerTable.newBuilder()
                    .setPlayerId(playerActor.getPlayerId());
            if (fullAttr != null) {
                request.setFullAttr(fullAttr);
                MonitorUnit.DB_PLAYER_FULL_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_CHAT).inc();
            } else {
                MonitorUnit.DB_PLAYER_CHANGE_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId(), PLAYER_ENTITY_CHAT).inc();
            }
            request.setChangedAttr(changeAttr);
            return request;
        }

    }

    @Override
    public String toString() {
        return "ChatPlayerDbComponent{" +
                "playerId=" + this.getEntityId() +
                '}';
    }
}

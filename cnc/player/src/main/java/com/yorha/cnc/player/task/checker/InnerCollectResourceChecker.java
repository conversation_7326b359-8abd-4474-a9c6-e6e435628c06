package com.yorha.cnc.player.task.checker;

import com.google.common.collect.Lists;
import com.yorha.cnc.player.event.task.CheckTaskProcessEvent;
import com.yorha.cnc.player.event.task.PlayerInnerCollectResourceEvent;
import com.yorha.cnc.player.event.task.PlayerTaskEvent;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.TaskInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import res.template.TaskPoolTemplate;

import java.util.List;
import java.util.Map;

import static com.yorha.common.enums.statistic.StatisticEnum.INNER_COLLECT_RESOURCE_NUM;

/**
 * 城内收集某种资源达到多少
 * param1: 资源类型
 * param2: 数量
 *
 * <AUTHOR>
 */
public class InnerCollectResource<PERSON>hecker extends AbstractTaskChecker {

    public static List<String> attentionList = Lists.newArrayList(
            PlayerInnerCollectResourceEvent.class.getSimpleName(),
            CheckTaskProcessEvent.class.getSimpleName());

    @Override
    public List<String> getAttentionEvent() {
        return attentionList;
    }

    @Override
    public boolean updateProcess(PlayerTaskEvent event, TaskInfoProp prop, TaskPoolTemplate taskTemplate) {
        List<Integer> taskParams = taskTemplate.getTypeValueList();
        int resourceConfig = taskParams.get(0);
        int countConfig = taskParams.get(1);
        if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_CREATE) {
            int countTotal = (int) event.getPlayer().getStatisticComponent().getSecondStatistic(INNER_COLLECT_RESOURCE_NUM, resourceConfig);
            prop.setProcess(Math.min(countConfig, countTotal));
        } else if (taskTemplate.getTaskCalculationMethod() == CommonEnum.TaskCalcType.TCT_RECEIVE) {
            if (event instanceof PlayerInnerCollectResourceEvent) {
                PlayerInnerCollectResourceEvent collectResourceEvent = (PlayerInnerCollectResourceEvent) event;
                Map<CommonEnum.CurrencyType, Long> resourceMap = collectResourceEvent.getResource();
                if (resourceMap != null) {
                    long count = getCount(taskTemplate, resourceConfig, resourceMap);
                    if (count > 0) {
                        prop.setProcess((int) Math.min(countConfig, prop.getProcess() + count));
                    }
                }
            }
        } else {
            WechatLog.error(new ResourceException("not support task calc type. template:{}",
                    ToStringBuilder.reflectionToString(taskTemplate, ToStringStyle.SHORT_PREFIX_STYLE)));
        }
        return prop.getProcess() >= countConfig;
    }

    private Long getCount(TaskPoolTemplate taskTemplate, int resourceConfig, Map<CommonEnum.CurrencyType, Long> resourceMap) {
        Long count;
        // 策划要求的特殊逻辑，0代表所有
        if (resourceConfig == 0) {
            return resourceMap.values().stream().mapToLong(it -> it).sum();
        }
        CommonEnum.CurrencyType key = CommonEnum.CurrencyType.forNumber(resourceConfig);
        if (key == null) {
            WechatLog.error(new ResourceException("task={} resourceConfig={} type error", taskTemplate.getId(), resourceConfig));
            return 0L;
        }
        count = resourceMap.get(key);
        if (count == null) {
            return 0L;
        }
        return count;
    }
}

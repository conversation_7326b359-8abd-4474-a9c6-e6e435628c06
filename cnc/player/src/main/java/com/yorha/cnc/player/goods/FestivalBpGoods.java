package com.yorha.cnc.player.goods;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.activity.unit.PlayerFestivalBpUnit;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.FestivalBpParamProp;
import com.yorha.game.gen.prop.PlayerGoodsOrderProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPayment;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ChargeGoodsTemplate;
import res.template.FestivalBpPoolTemplate;

import java.util.Collections;
import java.util.List;

/**
 * 节日通行礼包
 *
 * <AUTHOR>
 */
public class FestivalBpGoods implements Goods {
    private static final Logger LOGGER = LogManager.getLogger(FestivalBpGoods.class);

    @Override
    public void checkApply(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate) {
        final StructPB.FestivalBpParamPB param = msg.getOrderParam().getFbpParam();
        final int actId = param.getActId();
        final int unitId = param.getUnitId();
        final PlayerFestivalBpUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerFestivalBpUnit.class, actId, unitId);
        final FestivalBpPoolTemplate template = unit.getBpPoolTemplate(param.getBpId());
        if (template == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "error bp");
        }
        if (template.getType() != CommonEnum.FestivalBpType.FBPT_GOODS) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "error type");
        }
        if (template.getGoodsId() != goodsTemplate.getId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "error goods");
        }
        if (unit.isBpOpen(param.getBpId())) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "repeat open");
        }
    }

    @Override
    public void fillOrder(PlayerEntity owner, PlayerPayment.Player_ApplyGoodsOrder_C2S msg, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp orderProp) {
        final int actId = msg.getOrderParam().getFbpParam().getActId();
        final int unitId = msg.getOrderParam().getFbpParam().getUnitId();
        final int bpId = msg.getOrderParam().getFbpParam().getBpId();
        orderProp.getExtInfo()
                .getBpParam()
                .setActId(actId)
                .setUnitId(unitId)
                .setBpId(bpId);
        LOGGER.info("FestivalBpGoods fillOrder {} {}-{}-{}", owner, actId, unitId, bpId);
    }

    @Override
    public void checkBeforeDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
    }

    @Override
    public List<Struct.ItemPair> afterBaseDeliver(PlayerEntity owner, ChargeGoodsTemplate goodsTemplate, PlayerGoodsOrderProp goodsOrder) {
        final FestivalBpParamProp param = goodsOrder.getExtInfo().getBpParam();
        final int actId = param.getActId();
        final int unitId = param.getUnitId();
        final PlayerFestivalBpUnit unit = owner.getActivityComponent().checkedGetUnit(PlayerFestivalBpUnit.class, actId, unitId);
        unit.tryOpenBp(param.getBpId(), "goods_" + goodsTemplate.getId());
        return Collections.emptyList();
    }

}

package com.yorha.cnc.player.component;

import com.yorha.cnc.player.PlayerEntity;
import com.yorha.cnc.player.event.task.*;
import com.yorha.common.asset.AssetDesc;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.asset.AssetType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.HeroHelper;
import com.yorha.common.power.PowerInterface;
import com.yorha.common.qlog.json.hero.HeroLevelStar;
import com.yorha.common.qlog.json.hero.HeroSkill;
import com.yorha.common.qlog.json.hero.HeroTalent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.hero.HeroTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.HeroState;
import com.yorha.proto.CommonEnum.talentPateState;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncHeroGet;
import qlog.flow.QlogCncHeroLevel;
import qlog.flow.QlogCncHeroSkill;
import qlog.flow.QlogCncHeroStar;
import res.template.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.yorha.common.enums.statistic.StatisticEnum.*;

/**
 * 英雄相关组件
 *
 * <AUTHOR> Jiang
 */
public class PlayerHeroComponent extends PlayerComponent implements PowerInterface {
    private static final Logger LOGGER = LogManager.getLogger(PlayerHeroComponent.class);

    public PlayerHeroComponent(PlayerEntity owner) {
        super(owner);
    }

    public PlayerHeroModelProp getProp() {
        return getOwner().getProp().getPlayerHeroModel();
    }

    @Override
    public void postLogin(SsSceneDungeon.PlayerLoginAns playerLoginAns) {
        try {
            this.initHeroProp();
            this.checkAndFixHeroStatus(playerLoginAns.getHeroListList());
            this.syncSimpleHeroProp2Clan();
        } catch (Exception e) {
            WechatLog.error("hero module init error. ", e);
        }
    }

    /**
     * 在行军中的所有英雄
     */
    private void checkAndFixHeroStatus(List<Integer> occupyHeroList) {
        for (PlayerHeroProp heroProp : getProp().getPlayerHeroMap().values()) {
            if (occupyHeroList.contains(heroProp.getHeroId()) && heroProp.getState() == HeroState.HERO_IDLE) {
                this.updateHeroState(heroProp, HeroState.HERO_SET_OFF);
                LOGGER.info("PlayerHeroComponent checkAndFixHeroStatus try lock HERO_IDLE to HERO_SET_OFF {} {}", getEntityId(), heroProp.getHeroId());
                // 正式环境线上不应该出现
                WechatLog.error("checkAndFixHeroStatus fail lock need check {} {}", getEntityId(), heroProp.getHeroId());
            }
            if (!occupyHeroList.contains(heroProp.getHeroId()) && heroProp.getState() == HeroState.HERO_SET_OFF) {
                updateHeroState(heroProp, HeroState.HERO_IDLE);
                // 玩家登录call大世界登录的时候，大世界正好行军回城，导致两边的影响不一致，所以不能企微告警
                // 但一定是 玩家认为英雄上锁，大世界认为英雄没锁
                LOGGER.info("PlayerHeroComponent checkAndFixHeroStatus try unlock HERO_SET_OFF to HERO_IDLE {} {}", getEntityId(), heroProp.getHeroId());
                if (!ServerContext.isProdSvr()) {
                    // 非正式环境需要告警
                    WechatLog.error("checkAndFixHeroStatus fail unlock need check {} {}", getEntityId(), heroProp.getHeroId());
                }
            }
        }
    }

    private void initHeroProp() {
        // 初始化解锁配置中的英雄
        List<Integer> initialHero = ResHolder.getResService(ConstKVResService.class).getTemplate().getInitialHero();
        for (Integer heroId : initialHero) {
            Int32PlayerHeroMapProp playerHeroMap = getProp().getPlayerHeroMap();
            if (!playerHeroMap.containsKey(heroId)) {
                initHero(heroId, HeroUnlockReason.INITIALIZE_UNLOCK);
            }
        }
    }


    /**
     * 删除所有英雄
     */
    public void removeAllHero() {
        Int32PlayerHeroMapProp playerHeroMap = getProp().getPlayerHeroMap();
        playerHeroMap.clear();
    }

    /**
     * 英雄解锁接口
     */
    public void unLockHero(int heroId) {
        if (heroId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
        }

//        if (!isCanUnLockWithConfig(heroId)) {
//            throw new GeminiException(ErrorCode.HERO_HERO_NOT_OPEN.getCodeId());
//        }
        HeroRhTemplate heroTemplate = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroId);

        // 英雄未解锁检测
        if (isUnLockByHeroId(heroId)) {
            //英雄已解锁
            throw new GeminiException(ErrorCode.HERO_HERO_IS_UNLOCK.getCodeId());
        }

//        // 英雄开放招募时间检测
//        if (heroTemplate.getOpenTimeDt().getTime() > SystemClock.now()) {
//            throw new GeminiException(ErrorCode.HERO_UN_OPEN_RECRUIT.getCodeId());
//        }

        // 道具检测（解锁所需消耗）
        AssetPackage consume = AssetPackage.builder().plusItem(heroTemplate.getRelatedShardItem(), heroTemplate.getRecruitShardAmount()).build();
        getOwner().verifyThrow(consume);

        // 扣除道具
        getOwner().consume(consume, CommonEnum.Reason.ICR_HERO_FRAGMENT_COMPOSE, String.valueOf(heroId));

        // 解锁英雄
        initHero(heroId, HeroUnlockReason.HERO_FRAGMENT_COMPOSE);
    }

    /**
     * 英雄已解锁
     */
    public boolean isUnLockByHeroId(int heroId) {
        if (!hasHeroProp(heroId)) {
            return false;
        }
        return getHeroProp(heroId).getState() != HeroState.HERO_NONE;
    }

    /**
     * 英雄升级接口
     */
    public void handleHeroLevelUp(int heroId) {
        if (heroId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        PlayerHeroProp heroProp = this.getHeroProp(heroId);
        // 英雄解锁检测
        this.heroUnLockCheck(heroId);

        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        HeroLevelRhTemplate template = resService.getHeroLevelTemplate(heroId, heroProp.getLevel() + 1);
        if (template == null) {
            // 英雄已抵达最大等级
            throw new GeminiException(ErrorCode.HERO_HERO_LEVEL_MAX);
        }
        // 英雄升级建筑等级检测
        this.checkBuilding(template.getRequireBuildingLevelPair());
        //资源检测
        AssetPackage consume = AssetPackage.builder().plusCurrency(template.getCostPair()).build();
        this.getOwner().verifyThrow(consume);
        // 适用道具
        this.getOwner().consume(consume, CommonEnum.Reason.ICR_HERO_LEVEL_UP, String.valueOf(heroId));
        this.execAddHeroLevel(heroProp, HeroAddLevelReason.USE_EXPRIENCE_ITEM);
        this.checkAndUnlockHeroSkill(heroProp, HeroSkillUnlockReason.HERO_LEVEL_UP);
        this.postHeroPropChange(heroId, false);

    }

    /**
     * 英雄升星接口
     */
    public void handleHeroStageUp(int heroId, boolean common, HeroStarUpReason reason) {
        // 英雄解锁检测
        this.heroUnLockCheck(heroId);
        PlayerHeroProp heroProp = getHeroProp(heroId);
        int next = heroProp.getStar() + 1;
        HeroTemplateService heroTemplateService = ResHolder.getResService(HeroTemplateService.class);
        //满级检测
        boolean flag = heroTemplateService.hasStar(next);
        if (!flag) {
            // 星已满级
            throw new GeminiException(ErrorCode.HERO_STAR_LEVEL_MAX);
        }
        if (HeroStarUpReason.GM_ADD_STAR != reason) {
            HeroRhTemplate heroTemplate = ResHolder.getTemplate(HeroRhTemplate.class, heroId);
            // 道具消耗组装
            HeroStarRhTemplate heroStarTemplate = heroTemplateService.getHeroStarTemplate(next);
            AssetPackage consume = this.buildStarUpCost(heroTemplate.getRarity(), heroTemplate.getRelatedShardItem(), heroStarTemplate.getCostShard(), common);
            // 道具消耗检测
            getOwner().verifyThrow(consume);

            getOwner().consume(consume, CommonEnum.Reason.ICR_HERO_STAR_UP, heroId);

            this.recordConsume(heroProp.getHeroStatistics().getStarHistoryCost(), consume);
        }

        heroProp.setStar(next);
        //技能星级修改
        heroProp.getSkillStore().values().forEach(prop -> {
            HeroSkillLevelRhTemplate heroSkillLevelTemplate = heroTemplateService.getHeroSkillLevelTemplate4Star(prop.getSkillGroupId(), next);
            if (heroSkillLevelTemplate != null) {
                prop.setSkillStar(heroSkillLevelTemplate.getStar());
            }
        });
        getOwner().getStatisticComponent().recordSingleStatistic(HERO_STARUP_TIMES, 1);
        new PlayerHeroStarChangeEvent(getOwner(), heroProp.getHeroId(), 1).dispatch();
        // 记录QLog
        sendHeroStarQLog(heroProp, reason);
        this.checkAndUnlockHeroSkill(heroProp, HeroSkillUnlockReason.HERO_STAR_UP);
        this.postHeroPropChange(heroId, false);

    }


    /**
     * 英雄技能升级
     *
     * @param heroId       heroId
     * @param skillGroupId skillGroupId
     * @param reason       原因，只有HeroSkillUpReason.HERO_SKILL_UP 才需要检测
     */
    public void handleSkillLevelUp(int heroId, int skillGroupId, HeroSkillUpReason reason) {
        PlayerHeroProp heroProp = this.getHeroProp(heroId);
        // 英雄解锁检测
        this.heroUnLockCheck(heroId);
        PlayerHeroSkillProp skillProp = heroProp.getSkillStoreV(skillGroupId);
        if (skillProp == null) {
            throw new GeminiException(ErrorCode.HERO_SKILL_LOCK);
        }
        int next = skillProp.getSkillLevel() + 1;
        HeroTemplateService heroTemplateService = ResHolder.getResService(HeroTemplateService.class);
        HeroSkillLevelRhTemplate skillLevelTemplate = heroTemplateService.getHeroSkillLevelTemplate(skillProp.getSkillGroupId(), next);
        if (skillLevelTemplate == null) {
            // 技能已满级
            throw new GeminiException(ErrorCode.HERO_SKILL_LEVEL_MAX);
        }
        if (HeroSkillUpReason.GM_ADD_SKILL != reason) {
            int requireHeroLevel = skillLevelTemplate.getRequireHeroLevel();
            if (requireHeroLevel > heroProp.getLevel()) {
                // 技能升级英雄等级不足
                throw new GeminiException(ErrorCode.HERO_SKILL_HERO_LEVEL_NOT_ENOUGH);
            }
            int requireStar = skillLevelTemplate.getRequireStar();
            if (requireStar > heroProp.getStar()) {
                // 技能升级英雄星级不足
                throw new GeminiException(ErrorCode.HERO_SKILL_HERO_STAR_NOT_ENOUGH);
            }
            //资源检测
            AssetPackage consume = AssetPackage.builder().plusCurrency(skillLevelTemplate.getCostPair()).build();
            getOwner().verifyThrow(consume);
            // 消耗道具
            getOwner().consume(consume, CommonEnum.Reason.ICR_HERO_SKILL_UP, heroId);
            recordConsume(heroProp.getHeroStatistics().getSkillHistoryCost(), consume);

        }
        //技能升级
        SkillConfigTemplate skillTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillTemplate(skillProp.getSkillGroupId(), next);
        skillProp.setSkillLevel(next);
        getOwner().getStatisticComponent().recordSingleStatistic(HERO_UPDATE_SKILL_TIMES, 1);
        new PlayerHeroUpdateSkillEvent(getOwner(), heroId, next).dispatch();
        sendSkillUpQLog(heroId, skillTemplate.getSkillId(), skillTemplate.getLevel(), reason.name());
        LOGGER.debug("skill level up,heroId:{} skillGroupId:{},skillLevel:{}", heroId, skillProp.getSkillGroupId(), skillProp.getSkillLevel());
        postHeroPropChange(heroId, false);
    }


    public void resetSkillHandler(int heroId) {
        if (heroId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
        }
        PlayerHeroProp heroProp = getHeroProp(heroId);

//        // 技能重置检测
//        resetSkillCheck(heroProp);

        // 道具检测
        int heroSkillResetItemId = ResHolder.getResService(ConstKVResService.class).getTemplate().getHeroSkillResetItemId();
        // 特殊道具，一次用一个
        AssetPackage assetPackage = AssetPackage.builder().plusItem(heroSkillResetItemId, 1).build();
        getOwner().verifyThrow(assetPackage);

        // 道具消耗
        getOwner().consume(assetPackage, CommonEnum.Reason.ICR_HERO_SKILL_RESET, String.valueOf(heroId));

        // 重置英雄技能
        execResetHeroSkill(heroProp);

        // 同步英雄数据
        postHeroPropChange(heroId, false);
    }


    /**
     * 重置英雄天赋页接口
     */
    public void heroResetTalentHandler(int heroId, int slot, boolean isGm) {
        if (heroId <= 0 || slot <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        // 英雄解锁检测
        PlayerHeroProp heroProp = getHeroProp(heroId);
        heroUnLockCheck(heroId);

        // 天赋页检测
        PlayerHeroTalentPageProp talentPageProp = getTalentPageProp(slot, heroProp);

        // 天赋页解锁检测
        talentPageUnLockCheck(talentPageProp);

        // 配置检测
        TalentPageTemplate talentPageTemplate = ResHolder.getInstance().getValueFromMap(TalentPageTemplate.class, slot);

        boolean isInUsePage = HeroHelper.isUsedStatus(talentPageProp);
        if (isInUsePage && !isGm) {
            // 道具消耗检测
            IntPairType resetCostPair = talentPageTemplate.getResetCostPair();
            AssetPackage.Builder costItem = AssetPackage.builder().plusItem(resetCostPair.getKey(), resetCostPair.getValue());
            // 道具消耗
            getOwner().consume(costItem.build(), CommonEnum.Reason.ICR_NONE, "heroResetTalentHandler");
        }
        resetTalentPage(talentPageProp);

        // 正在使用中的天赋升级需要通知Scene
        if (isInUsePage) {
            postHeroPropChange(heroId, false);
        }
    }

    /**
     * 英雄切换天赋页接口
     */
    public void switchTalentPageHandler(int heroId, int usePageSlot, boolean isGm) {
        if (heroId <= 0 || usePageSlot <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
        }
        // 英雄解锁检测
        heroUnLockCheck(heroId);

        PlayerHeroProp heroProp = getHeroProp(heroId);
        PlayerHeroTalentPageProp talentPageProp = getUsedTalentPagePropById(usePageSlot, heroProp);

        TalentPageTemplate switchTargetPageTemplate = ResHolder.getInstance().getValueFromMap(TalentPageTemplate.class, usePageSlot);

        if (!isGm) {
            // 天赋页正在使用检测
            talentPageUseCheck(talentPageProp);
            // 天赋页解锁检测
            talentPageUnLockCheck(talentPageProp);
            // 道具消耗检测
            IntPairType switchCostPair = switchTargetPageTemplate.getSwitchCostPair();
            AssetPackage.Builder costItem = AssetPackage.builder().plusItem(switchCostPair.getKey(), switchCostPair.getValue());
            // 道具消耗
            getOwner().consume(costItem.build(), CommonEnum.Reason.ICR_NONE, "switchTalentPageHandler");
        }

        execSwitchTalentPage(heroProp, talentPageProp);

        postHeroPropChange(heroId, false);
    }


    /**
     * 英雄天赋页重命名接口
     */
    public void resetNameTalentPageHandler(int heroId, int pageSlot, String newName) {
        if (heroId <= 0 || pageSlot <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 英雄解锁检测
        heroUnLockCheck(heroId);

        PlayerHeroProp heroProp = getHeroProp(heroId);

        // 名称检测
        talentPageNameCheck(newName);

        PlayerHeroTalentPageProp talentPageProp = getUsedTalentPagePropById(pageSlot, heroProp);

        talentPageUnLockCheck(talentPageProp);

        talentPageProp.setTalentPageName(newName);
    }


    public void setSkillSlotLimitHandler(int heroId, int slotLimit) {
        if (heroId <= 0 || slotLimit <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
        }

        PlayerHeroProp heroProp = getHeroProp(heroId);
        if (slotLimit > heroProp.getSkillStoreSize()) {
            // 没有足够技能
            throw new GeminiException(ErrorCode.HERO_NOENOUGHSKILL);
        }
        heroProp.setSkillSlotLimit(slotLimit);
    }


    public void heroIntensiveSkill(int heroId) {

//        PlayerHeroProp heroProp = getHeroProp(heroId);
//        HeroRhTemplate heroTemplate = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroId);


//
//        // 觉醒技能
//        intensiveSkill(heroTemplate, heroProp);
        postHeroPropChange(heroId, false);

        new PlayerHeroIntensiveEvent(getOwner(), heroId).dispatch();
    }


    /**
     * 技能重置
     */
    private void execResetHeroSkill(PlayerHeroProp heroProp) {
//        final int initLevel = 1;
    //    int sumLevel = 0;
//        SkillDataTemplateService resService = ResHolder.getResService(SkillDataTemplateService.class);
//        for (PlayerHeroSkillProp skillProp : heroProp.getSkillStore().values()) {
//            SkillTemplate skillTemplate = ResHolder.getInstance().getValueFromMap(SkillTemplate.class, skillProp.getSkillId());
//            if (skillTemplate.getLevel() <= initLevel) {
//                continue;
//            }
//            SkillTemplate initSkillTemplate = resService.getSkillTemplate(skillProp.getSkillGroupId(), initLevel);
//            skillProp.setSkillId(initSkillTemplate.getId());
//            sumLevel += (skillTemplate.getLevel() - initLevel);
//        }
//        if (sumLevel == 0) {
//            LOGGER.error("reset skill error. no skill need reset. heroProp:{}", heroProp);
//        }

    }

    public PlayerHeroTalentPageProp getTalentPageProp(int slotNum, PlayerHeroProp heroProp) {
        return heroProp.getTalentPageStore().get(slotNum);
    }


    /**
     * 记录星级/技能历史消耗
     */
    private void recordConsume(Int32ItemPairMapProp starHistoryCost, AssetPackage consume) {
        for (AssetDesc desc : consume.getImmutableAssets()) {
            if (desc.getType() == AssetType.ITEM) {
                ItemPairProp itemPairProp = starHistoryCost.computeIfAbsent(desc.getId(), (key) -> {
                    ItemPairProp prop = new ItemPairProp();
                    prop.setItemTemplateId(key);
                    return prop;
                });
                itemPairProp.setCount((int) (itemPairProp.getCount() + desc.getAmount()));
            }
        }
    }

    /**
     * 记录英雄玩家士兵击杀数
     */
    public void recordPlayerSoldierKillData(SsPlayerMisc.BattleHeroOrPlaneRecord heroOrPlane, Map<Integer, Long> killRecordMap) {
        boolean hasMainHero = heroOrPlane.getMainHeroId() > 0;
        boolean hasDeputyHero = heroOrPlane.getDeputyHeroId() > 0;
        if (!hasMainHero) {
            return;
        }

        long totalKillPlayerSoldier = 0;
        long totalKillPlayerMonster = 0;
        for (Map.Entry<Integer, Long> entry : killRecordMap.entrySet()) {
            SoldierTypeTemplate soldierTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, entry.getKey());
            if (isPlayerSoldier(soldierTemplate)) {
                totalKillPlayerSoldier += entry.getValue();
            }
            if (isMonsterSoldier(soldierTemplate)) {
                totalKillPlayerMonster += entry.getValue();
            }
        }
        if (hasDeputyHero) {
            totalKillPlayerSoldier = totalKillPlayerSoldier >> 1;
            totalKillPlayerMonster = totalKillPlayerMonster >> 1;
            // 记录副将击杀
            execRecordKill(heroOrPlane.getDeputyHeroId(), totalKillPlayerSoldier, totalKillPlayerMonster);
        }
        // 记录主将击杀
        execRecordKill(heroOrPlane.getMainHeroId(), totalKillPlayerSoldier, totalKillPlayerMonster);

    }

    private void execRecordKill(int heroId, long totalKillSoldier, long totalKillMonster) {
        if (heroId <= 0) {
            return;
        }
        PlayerHeroProp heroProp = getHeroProp(heroId);
        LOGGER.debug("hero exec record kill info. totalSoldier:{} totalMonster:{}", totalKillMonster, totalKillSoldier);
        heroProp.getHeroStatistics().setTotalKillSoldier(heroProp.getHeroStatistics().getTotalKillSoldier() + totalKillSoldier);
        heroProp.getHeroStatistics().setTotalKillMonster(heroProp.getHeroStatistics().getTotalKillMonster() + totalKillMonster);
    }

    private boolean isPlayerSoldier(SoldierTypeTemplate soldierTemplate) {
        CommonEnum.SoldierBelong soldierBelong = soldierTemplate.getSoldierBelong();
        return soldierBelong == CommonEnum.SoldierBelong.SB_PLAYER;
    }

    private boolean isMonsterSoldier(SoldierTypeTemplate soldierTemplate) {
        CommonEnum.SoldierBelong soldierBelong = soldierTemplate.getSoldierBelong();
        return soldierBelong == CommonEnum.SoldierBelong.SB_MONSTER;
    }

    /**
     * 构建升星消耗
     */
    private AssetPackage buildStarUpCost(int quality, int itemTemplateId, int targetCount, boolean common) {
        AssetPackage.Builder builder = AssetPackage.builder();
        if (common) {
            int remain = getOwner().getItemComponent().getItemNum(itemTemplateId);
            if (remain < targetCount) {
                // 使用已有道具数量
                if (remain > 0) {
                    builder.plusItem(itemTemplateId, remain);
                }
                // 替换ID
                ConstKVResService constKvResService = ResHolder.getResService(ConstKVResService.class);
                int replaceItemId = constKvResService.getHeroStarClipItemId(quality);
                int replaceCount = targetCount - remain;
                if (replaceCount > 0) {
                    builder.plusItem(replaceItemId, replaceCount);
                }
            } else {
                // 如果剩余数量 >= 目标数量,直接使用原道具
                builder.plusItem(itemTemplateId, targetCount);
            }
        } else {
            builder.plusItem(itemTemplateId, targetCount);
        }

        return builder.build();
    }


    /**
     * 英雄已解锁检测
     */
    public void heroUnLockCheck(int heroId) {
        if (isUnLockByHeroId(heroId)) {
            return;
        }
        throw new GeminiException(ErrorCode.HERO_LOCK.getCodeId());
    }

    /**
     * 检测是否能升级
     */
    private void checkBuilding(IntPairType condition) {
        //CommonEnum.CityBuildType.CBT_FACT_VALUE
        int buildLevel = getOwner().getInnerBuildRhComponent().getInnerBuildLevel(condition.getKey());
        if (buildLevel < condition.getValue()) {
            // 英雄已抵达建筑限制等级
            throw new GeminiException(ErrorCode.HERO_REACH_BUILDING_LEVEL_LIMIT);
        }
    }

    /**
     * 根据英雄id获取数据
     */
    public PlayerHeroProp getHeroProp(int heroId) {
        return getProp().getPlayerHeroMap().get(heroId);
    }

    /**
     * 根据英雄id获取数据
     */
    public Struct.Hero getHero(int heroId) {
        if (heroId <= 0) {
            return null;
        }
        PlayerHeroProp playerHeroProp = getProp().getPlayerHeroMap().get(heroId);
        if (playerHeroProp == null) {
            return null;
        }
        return buildHeroByPlayerHero(playerHeroProp);
    }

    /**
     * 获取当前拥有英雄的最高等级
     *
     * @return 最高等级
     */
    public int getMaxHeroLevel() {
        Collection<PlayerHeroProp> playerHeroPropList = getProp().getPlayerHeroMap().values();
        int maxLevel = 0;
        for (PlayerHeroProp prop : playerHeroPropList) {
            maxLevel = Math.max(maxLevel, prop.getLevel());
        }
        return maxLevel;
    }

    /**
     * 获取 拥有x级x品x星的英雄x个 任务的进度
     *
     * @return 符合要求的英雄个数
     */
    public int getPlayerHeroPropTaskProcess(int level, int quality, int star) {
        Collection<PlayerHeroProp> playerHeroPropList = getProp().getPlayerHeroMap().values();
        int prefixNum = 0;
        for (PlayerHeroProp prop : playerHeroPropList) {
            int heroId = prop.getHeroId();
            HeroRhTemplate heroTemplate = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroId);
            if (heroTemplate.getRarity() == quality || quality == 0) {
                if (prop.getLevel() >= level && prop.getStar() >= star) {
                    prefixNum++;
                }
            }
        }
        return prefixNum;
    }

    public boolean hasHeroProp(int heroId) {
        if (heroId <= 0) {
            return false;
        }
        return getProp().getPlayerHeroMap().containsKey(heroId);
    }


    public void checkHeroProp(int heroId) {
        if (heroId <= 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (!hasHeroProp(heroId)) {
            throw new GeminiException(ErrorCode.HERO_HERO_NOT_OPEN);
        }
    }

    /**
     * 获取使用中的天赋页
     */
    public PlayerHeroTalentPageProp getUsedTalentPagePropById(int slotNum, PlayerHeroProp heroProp) {
        PlayerHeroTalentPageProp talentPageProp = heroProp.getTalentPageStoreV(slotNum);

        if (talentPageProp == null) {
            // 失效天赋页
            throw new GeminiException(ErrorCode.HERO_TALENT_PAGE_FAIL.getCodeId());
        }
        return talentPageProp;
    }

    public void initHero(int heroId, HeroUnlockReason reason) {
        if (hasHeroProp(heroId)) {
            LOGGER.error("try unlock an exist hero. playerId:{}, heroId:{} reason:{}", getEntityId(), heroId, reason);
            return;
        }
        handleHeroUnlock(heroId, reason);
    }

    /**
     * 初始化解锁英雄
     */
    private void handleHeroUnlock(int heroId, HeroUnlockReason reason) {
        if (heroId <= 0) {
            WechatLog.error("PlayerHeroComponent handleHeroUnlock error, heroId={} reason={}", heroId, reason);
            return;
        }
        PlayerHeroProp heroProp = getProp().getPlayerHeroMap().addEmptyValue(heroId);
        heroProp.setHeroId(heroId);
        // 初始化头像
//        unlockHeroAvatar(heroId);
        // 初始化天赋页
//        initUnLockTalentPage(heroProp);
        // 初始化等级
        heroProp.setLevel(1);
//        initHeroLevel(heroProp);
        // 初始化星级
        heroProp.setStar(1);

        // initStarStage(heroId, this.getStarReason(reason));
        // 初始化技能
        this.checkAndUnlockHeroSkill(heroProp, HeroSkillUnlockReason.HERO_UNLOCK);
        // 初始化状态
        updateHeroState(heroProp, HeroState.HERO_IDLE);
        heroProp.getHeroStatistics().setUnlockTSMs(SystemClock.now());
//        getOwner().getWallComponent().checkOrSetDefaultHero(heroId);
        new PlayerHeroUnLockEvent(getOwner(), heroId).dispatch();
        sendHeroUnlockQLog(heroProp, reason);

        postHeroPropChange(heroId, true);
        // 成就需求目前不关心初始化解锁的那些英雄，所以只需要这里触发
        //   getOwner().getAchievementComponent().onTrigger(ASCT_UNLOCK_HERO, heroId, 0, 1);
//        //解锁对应兵种 FIXME
//        getOwner().getUnitComponent().initUnit(ResHolder.getTemplate(HeroTemplate.class, heroId).getRelatedUnit());
    }


    /**
     * 获取已使用的天赋点
     */
    public int getUsedTalentPoint(PlayerHeroTalentPageProp talentPageProp) {
        return talentPageProp.getTalentMap().values().stream().mapToInt(PlayerHeroTalentProp::getUsedTalentPoint).sum();
    }

    /**
     * 英雄解锁QLog
     */
    private void sendHeroUnlockQLog(PlayerHeroProp heroProp, HeroUnlockReason reason) {
        QlogCncHeroGet.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setHeroID(String.valueOf(heroProp.getHeroId()))
                .setAction(reason.name().toLowerCase())
                .sendToQlog();
    }


    /**
     * 技能解锁检测
     */

    private void checkAndUnlockHeroSkill(PlayerHeroProp heroProp, HeroSkillUnlockReason reason) {
        HeroRhTemplate heroTemplate = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroProp.getHeroId());
        IntPairType firstSkill = heroTemplate.getSkill1Pair();
        if (firstSkill != null) {
            this.addSkill(heroProp, firstSkill.getKey(), firstSkill.getValue(), CommonEnum.SkillSlot.SLOT_ONE, reason);
        }
        IntPairType secondSkill = heroTemplate.getSkill2Pair();
        if (secondSkill != null) {
            this.addSkill(heroProp, secondSkill.getKey(), secondSkill.getValue(), CommonEnum.SkillSlot.SLOT_TWO, reason);
        }
        IntPairType thirdSkill = heroTemplate.getSkill3Pair();
        if (thirdSkill != null) {
            this.addSkill(heroProp, thirdSkill.getKey(), thirdSkill.getValue(), CommonEnum.SkillSlot.SLOT_THREE, reason);
        }
        IntPairType fourthSkill = heroTemplate.getSkill4Pair();
        if (fourthSkill != null) {
            this.addSkill(heroProp, fourthSkill.getKey(), fourthSkill.getValue(), CommonEnum.SkillSlot.SLOT_FOUR, reason);
        }
    }


    /**
     * 发送升星QLog
     */
    private void sendHeroStarQLog(PlayerHeroProp heroProp, HeroStarUpReason reason) {
        QlogCncHeroStar.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setHeroID(String.valueOf(heroProp.getHeroId()))
                .setAction(reason.name().toLowerCase())
                .setAfterHeroStar(heroProp.getStar())
                .setAfterHeroStarExp(0)
                .setHeroStarExpCount(0)
                .sendToQlog();
    }

    /**
     * 添加1级等级
     */
    public void execAddHeroLevel(PlayerHeroProp heroProp, HeroAddLevelReason reason) {
        heroProp.setLevel(heroProp.getLevel() + 1);
        getOwner().getStatisticComponent().recordSingleStatistic(HERO_LEVELUP_TOTAL, 1);
        new PlayerHeroLevelUpEvent(getOwner(), heroProp.getHeroId(), heroProp.getLevel(), 1).dispatch();
        sendHeroAddExpQLog(heroProp, reason);
    }


    /**
     * 只会给gm使用，加到满级
     */
    public void handleStageUp2Max(PlayerHeroProp heroProp, HeroStarUpReason reason) {
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        while (true) {
            HeroStarRhTemplate template = resService.getHeroStarTemplate(heroProp.getStar() + 1);
            if (template == null) {
                break;
            }
            this.handleHeroStageUp(heroProp.getHeroId(), false, reason);
        }
    }


    /**
     * 星级加到满星
     */
    public void handleLevelUp2Max(PlayerHeroProp heroProp, HeroAddLevelReason reason) {
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        while (true) {
            HeroLevelRhTemplate template = resService.getHeroLevelTemplate(heroProp.getHeroId(), heroProp.getLevel() + 1);
            if (template == null) {
                break;
            }
            this.execAddHeroLevel(heroProp, reason);
        }
    }


    /**
     * 发送经验增加QLog
     */
    private void sendHeroAddExpQLog(PlayerHeroProp heroProp, HeroAddLevelReason reason) {
        QlogCncHeroLevel.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction("hero_exp_add")
                .setSubAction(reason.name().toLowerCase())
                .setHeroID(String.valueOf(heroProp.getHeroId()))
                .setAfterHeroLevel(heroProp.getLevel() + 1)
                .setAfterHeroExp(0)
                .setHeroExpCount(0)
                .sendToQlog();
    }


    private void addSkill(PlayerHeroProp heroProp, int skillGroupId, int skillLevel, CommonEnum.SkillSlot slot, HeroSkillUnlockReason reason) {
        Int32PlayerHeroSkillMapProp skillStore = heroProp.getSkillStore();
        if (skillStore.containsKey(skillGroupId)) {
            return;
        }
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        HeroSkillLevelRhTemplate skillTemplate = resService.getHeroSkillLevelTemplate(skillGroupId, skillLevel);
        if (heroProp.getStar() < skillTemplate.getRequireStar()) {
            return;
        }
        if (heroProp.getLevel() < skillTemplate.getRequireHeroLevel()) {
            return;
        }
        PlayerHeroSkillProp skillProp = new PlayerHeroSkillProp().setSkillGroupId(skillGroupId).setSkillLevel(skillLevel).setSkillSlot(slot.getNumber());
        skillStore.put(skillGroupId, skillProp);
        sendSkillUpQLog(heroProp.getHeroId(), skillGroupId, skillLevel, reason.name());
    }

    /**
     * 切换天赋页
     */
    public static void execSwitchTalentPage(PlayerHeroProp heroProp, PlayerHeroTalentPageProp talentPageProp) {
        heroProp.getTalentPageStore().values()
                .stream()
                .filter(it -> it.getTalentPageState() == talentPateState.TPS_USED)
                .forEach(it -> it.setTalentPageState(talentPateState.TPS_IDLE));
        talentPageProp.setTalentPageState(talentPateState.TPS_USED);
    }

    /**
     * 重置天赋页
     */
    public void resetTalentPage(PlayerHeroTalentPageProp talentPageProp) {
        int leftTalentPoint = talentPageProp.getLeftTalentPoint();
        int usedTalentPoint = getUsedTalentPoint(talentPageProp);
        talentPageProp.getTalentMap().clear();
        talentPageProp.setLeftTalentPoint(leftTalentPoint + usedTalentPoint);
    }

    /**
     * 命名检测
     */
    private void talentPageNameCheck(String name) throws GeminiException {
        int nameLength = ResHolder.getResService(ConstKVResService.class).getTemplate().getTalentPageNameLimit();
        if (name.length() > nameLength) {
            // 名称过长
            throw new GeminiException(ErrorCode.HERO_NAME_TOO_LONG.getCodeId());
        }
        if (StringUtils.isEmpty(name)) {
            // 名字为空
            throw new GeminiException(ErrorCode.HERO_NAME_EMPTY.getCodeId());
        }
        // 含标点符号
        if (StringUtils.containsSpecialChar(name)) {
            throw new GeminiException(ErrorCode.SYSTEM_NAME_ILLEGALITY);
        }
        // 首尾空格
        if (name.startsWith(" ") || name.endsWith(" ")) {
            throw new GeminiException(ErrorCode.SYSTEM_NAME_ILLEGALITY);
        }
        // 敏感词
        SsTextFilter.CheckTextAns checkTextAns = getOwner().syncCheckText(name, CommonEnum.UgcSceneId.USI_TALENT_PAGE_NAME);
        if (!checkTextAns.getIsLegal()) {
            throw new GeminiException(ErrorCode.TEXT_CONTAINS_ILLEGAL_CONTENT);
        }
    }



    /**
     * 天赋页解锁状态检测
     */
    public void talentPageUnLockCheck(PlayerHeroTalentPageProp talentPageProp) {
        if (talentPageProp == null || talentPageProp.getTalentPageState() == talentPateState.TPS_NONE) {
            // 天赋页未解锁
            throw new GeminiException(ErrorCode.HERO_TALENT_PAGE_LOCK.getCodeId());
        }
    }


    public static void talentPageUseCheck(PlayerHeroTalentPageProp talentPageProp) {
        if (talentPageProp.getTalentPageState() == talentPateState.TPS_USED) {
            // 天赋使用中
            throw new GeminiException(ErrorCode.HERO_TALENT_PAGE_USED.getCodeId());
        }
    }


    /**
     * 英雄属性变更的后续逻辑
     * 战力更新细则：天赋只有使用中的会影响战力，星级等级技能任意变动都会引起战力变更
     * 属性更新细则：上墙和出征英雄才需要同步到大世界
     *
     * @param heroId:英雄id
     */
    public void postHeroPropChange(int heroId, boolean isUnlock) {
        // 更新战力
        getOwner().getPowerComponent().updatePower(CommonEnum.PowerType.PT_HERO);
        // 同步英雄属性到scene
        // 刚解锁的英雄不需要同步到scene，自动上墙逻辑在syncScenePlayerWall这里已经同步过去了
        // 2024.03.04出现一个error：解锁英雄，自动上墙，battle侧到下一回合才会构建副将，syncHeroProp2Scene过去后就会出现error
        if (!isUnlock) {
            syncHeroProp2Scene(heroId);
        }
    }

    /**
     * 玩家登陆,加联盟时 topN英雄同步clan
     */
    public void syncSimpleHeroProp2Clan() {
        if (!getOwner().getPlayerClanComponent().isInClan()) {
            return;
        }
        List<Integer> heroIdSortByPower = getOwner().getHeroComponent().getHeroIdSortByPower();
        SsClanMember.SyncPlayerTopNHeroCmd.Builder cmd = SsClanMember.SyncPlayerTopNHeroCmd.newBuilder();

        for (int heroId : heroIdSortByPower) {
            PlayerHeroProp heroProp = getHeroProp(heroId);
            cmd.addHeroList(buildSimpleHeroByPlayerHero(heroProp));
        }
        LOGGER.info("PlayerHeroComponent syncSimpleHeroProp2Clan onlogin {} with hero: {}", getOwner().getClanId(), heroIdSortByPower);
        ownerActor().tellClan(getOwner().getClanId(), cmd.setPlayerId(getPlayerId()).build());
    }


    public void syncHeroProp2Scene(int heroId) {
        PlayerHeroProp heroProp = getHeroProp(heroId);

        boolean isWallHero = getOwner().getWallComponent().isWallHero(heroId);
        if (heroProp.getState() == HeroState.HERO_SET_OFF || isWallHero) {

            SsScenePlayer.SyncPlayerHeroCmd.Builder cmd = SsScenePlayer.SyncPlayerHeroCmd.newBuilder();
            cmd.setPlayerId(getPlayerId());

            LOGGER.info("hero sync scene with heroId:{}", heroId);
            cmd.addHeroList(buildHeroByPlayerHero(heroProp));
            ownerActor().tellBigScene(cmd.setPlayerId(getEntityId()).build());
        }
    }


    public Struct.SimpleSkill buildSimpleSkill(PlayerHeroSkillProp skillProp) {
        return Struct.SimpleSkill.newBuilder()
                .setSkillGroupId(skillProp.getSkillGroupId())
                .setLevel(skillProp.getSkillLevel())
                .setStar(skillProp.getSkillStar())
                .build();

    }

    private Struct.Hero buildHeroByPlayerHero(PlayerHeroProp heroProp) {
        Struct.SimpleSkillList.Builder builder = Struct.SimpleSkillList.newBuilder();
        heroProp.getSkillStore().values().stream().map(this::buildSimpleSkill).forEach(builder::addDatas);

        Set<Integer> talentIds = new HashSet<>();
        for (PlayerHeroTalentPageProp talentPageProp : heroProp.getTalentPageStore().values()) {
            if (talentPageProp.getTalentPageState() == talentPateState.TPS_USED) {
                for (PlayerHeroTalentProp talentProp : talentPageProp.getTalentMap().values()) {
                    talentIds.add(talentProp.getTalentId());
                }
                break;
            }
        }
        boolean isAwake = heroProp.getHeroStatistics().getAwakenTSMs() > 0 && heroProp.getHeroStatistics().getAwakenTSMs() <= SystemClock.now();
        return Struct.Hero.newBuilder()
                .setHeroId(heroProp.getHeroId())
                .setLevel(heroProp.getLevel())
                .setStar(heroProp.getStar())
                .setSkills(builder.build())
                .setIsAwake(isAwake)
                .setTalentIds(Basic.Int32List.newBuilder().addAllDatas(talentIds).build()).build();
    }

    private Struct.SimpleHero buildSimpleHeroByPlayerHero(PlayerHeroProp heroProp) {
        boolean isAwake = heroProp.getHeroStatistics().getAwakenTSMs() > 0 && heroProp.getHeroStatistics().getAwakenTSMs() <= SystemClock.now();
        return Struct.SimpleHero.newBuilder()
                .setHeroId(heroProp.getHeroId())
                .setLevel(heroProp.getLevel())
                .setStar(heroProp.getStar())
                .setIsAwake(isAwake)
                .build();
    }

    @Override
    public CommonEnum.PowerType getPowerType() {
        return CommonEnum.PowerType.PT_HERO;
    }

    @Override
    public long calcPower() {
        return getProp().getPlayerHeroMap().values().stream().mapToLong(HeroHelper::getHeroPower).sum();
    }

    public void releaseHeroState(List<Integer> heroIds) {
        LOGGER.info("release hero. heroList:{}", heroIds);
        if (getOwner() == null || CollectionUtils.isEmpty(heroIds)) {
            return;
        }
        for (Integer heroId : heroIds) {
            if (heroId <= 0) {
                continue;
            }
            PlayerHeroProp heroProp = getHeroProp(heroId);
            if (isSetOffState(heroProp)) {
                updateHeroState(heroProp, HeroState.HERO_IDLE);
            } else {
                // 在极端情况，玩家登录同时大世界的行军回城，导致checkAndFixHeroStatus先跑了
                LOGGER.warn("release hero fail. no need to be released, heroId={} state={}", heroProp.getHeroId(), heroProp.getState());
            }
        }
    }

    public void occupyHero(List<Integer> heroList) {
        LOGGER.info("occupy hero. heroList:{}", heroList);
        if (getOwner() == null || CollectionUtils.isEmpty(heroList)) {
            return;
        }
        for (Integer heroId : heroList) {
            if (heroId <= 0) {
                continue;
            }
            PlayerHeroProp heroProp = getHeroProp(heroId);
            if (isIdleState(heroProp)) {
                updateHeroState(heroProp, HeroState.HERO_SET_OFF);
            } else {
                LOGGER.error("occupy hero fail. no need to be occupied, heroId={} state={}", heroProp.getHeroId(), heroProp.getState());
            }
        }
    }

    private void updateHeroState(PlayerHeroProp heroProp, HeroState heroState) {
        LOGGER.info("update hero state. oldHero:{} newState:{}", heroProp.getHeroId(), heroState);
        heroProp.setState(heroState);
    }

    /**
     * 是外出英雄
     */
    private boolean isSetOffState(PlayerHeroProp heroProp) {
        if (heroProp == null) {
            return false;
        }
        return heroProp.getState() == HeroState.HERO_SET_OFF;
    }

    /**
     * 是忙碌英雄
     */
    public boolean isBusyState(int heroId) {
        return !isIdleState(heroId);
    }

    /**
     * 是空闲英雄
     */
    public boolean isIdleState(int heroId) {
        PlayerHeroProp heroProp = getHeroProp(heroId);
        return isIdleState(heroProp);
    }

    /**
     * 是空闲英雄
     */
    private boolean isIdleState(PlayerHeroProp heroProp) {
        if (heroProp == null) {
            return false;
        }
        return heroProp.getState() == HeroState.HERO_IDLE;
    }

    public Collection<PlayerHeroProp> getAllHero() {
        return getProp().getPlayerHeroMap().values();
    }

    /**
     * 判断主将星级是否能够携带副将
     *
     * @param mainHeroId 主将Id
     * @return true表示可以不携带，false表示能携带
     */
    public boolean cantBringDeputy(int mainHeroId) {
        PlayerHeroProp heroProp = getHeroProp(mainHeroId);
        int mainHeroStar = heroProp.getStar();
        int limitStar = ResHolder.getResService(ConstKVResService.class).getTemplate().getUnlockDeputyHeroStarLimit();
        return limitStar > mainHeroStar;
    }


    /**
     * 按照优先级选择合适英雄
     */
    public int chooseGarrisonHero(List<Integer> priority, Set<Integer> except) {
        for (Integer heroId : priority) {
            if (heroId == 0 || except.contains(heroId)) {
                continue;
            }
            if (getOwner().getHeroComponent().isIdleHero(heroId)) {
                return heroId;
            }
        }
        List<Integer> heroIdSortByPower = getOwner().getHeroComponent().getHeroIdSortByPower();
        var selectedHero = heroIdSortByPower.stream().filter(heroId -> !except.contains(heroId)).findFirst();
        return selectedHero.orElse(0);
    }

    public long getHeroAddition(int heroId, CommonEnum.BuffEffectType type, boolean ignoreTalent) {
        return HeroHelper.getHeroAddition(getHero(heroId), type, ignoreTalent);
    }

    public String getQLogHeroInfo() {
        List<HeroLevelStar> list = new ArrayList<>();
        for (PlayerHeroProp heroProp : getProp().getPlayerHeroMap().values()) {
            HeroLevelStar heroLevelStar = new HeroLevelStar();
            heroLevelStar.setHeroId(heroProp.getHeroId());
            heroLevelStar.setHeroLevel(heroProp.getLevel());
            heroLevelStar.setStarLevel(heroProp.getStar());
            list.add(heroLevelStar);
        }
        return JsonUtils.toJsonString(list);
    }

    public String getQLogHeroSkillInfo() {
        List<HeroSkill> heroSkillList = new ArrayList<>();
        for (PlayerHeroProp heroProp : getProp().getPlayerHeroMap().values()) {
            HeroSkill heroSkill = new HeroSkill();
            heroSkill.setHeroId(heroProp.getHeroId());
            heroSkill.setSkillList(heroProp.getSkillStore().keySet());
            heroSkillList.add(heroSkill);
        }
        return JsonUtils.toJsonString(heroSkillList);
    }

    public String getQLogHeroTalentInfo() {
        List<HeroTalent> heroTalentList = new ArrayList<>();
        for (PlayerHeroProp heroProp : getProp().getPlayerHeroMap().values()) {
            HeroTalent heroTalent = new HeroTalent();
            heroTalent.setHeroId(heroProp.getHeroId());
            List<String> list = new ArrayList<>();
            for (PlayerHeroTalentPageProp talentProp : heroProp.getTalentPageStore().values()) {
                for (int talent : talentProp.getTalentMap().keySet()) {
                    list.add(String.valueOf(talent));
                }
            }
            heroTalent.setTalentList(list);
            heroTalentList.add(heroTalent);
        }
        return JsonUtils.toJsonString(heroTalentList);
    }

    /**
     * 技能升级QLog
     */
    private void sendSkillUpQLog(int heroId, int skillGroupId, int skillLevel, String reason) {
        QlogCncHeroSkill.init(getOwner().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setHeroID(String.valueOf(heroId))
                .setHeroSkillGroupId(String.valueOf(skillGroupId))
                .setAction(reason.toLowerCase())
                .setAfterSkillLevel(skillLevel)
                .sendToQlog();
    }

    public boolean isIdleHero(int setUpMainHeroId) {
        return getHeroProp(setUpMainHeroId).getState().equals(HeroState.HERO_IDLE);
    }


    /**
     * 获取按照战力排序的的城内英雄列表
     *
     * @return 英雄属性列表
     */
    public List<Integer> getHeroIdSortByPower() {
        List<PlayerHeroProp> heroList = new ArrayList<>();
        for (PlayerHeroProp hero : getProp().getPlayerHeroMap().values()) {
            if (hero.getState() == CommonEnum.HeroState.HERO_IDLE) {
                heroList.add(hero);
            }
        }
        return heroList.stream().sorted(Comparator.comparingLong(o -> -HeroHelper.getHeroPower(o))).map(PlayerHeroProp::getHeroId).collect(Collectors.toList());
    }


    public int getHeroPropLevel(int heroId) {
        return getHeroProp(heroId).getLevel();
    }

}

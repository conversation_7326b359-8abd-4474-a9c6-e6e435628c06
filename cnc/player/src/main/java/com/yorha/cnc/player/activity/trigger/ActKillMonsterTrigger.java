package com.yorha.cnc.player.activity.trigger;

import com.google.common.collect.ImmutableList;
import com.yorha.cnc.player.event.PlayerEvent;
import com.yorha.cnc.player.event.task.PlayerKillBigSceneMonsterEvent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.game.gen.prop.TriggerInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * 活动杀怪触发器
 *
 * <AUTHOR>
 */
@TriggerController(type = CommonEnum.ActivityUnitTriggerType.AUTT_KILL_MONSTER)
public class ActKillMonsterTrigger extends AbstractActivityTrigger {
    private static final Logger LOGGER = LogManager.getLogger(ActKillMonsterTrigger.class);

    private static final List<Class<? extends PlayerEvent>> FOLLOW_EVENT_LIST = ImmutableList.of(
            PlayerKillBigSceneMonsterEvent.class
    );

    @Override
    public List<Class<? extends PlayerEvent>> getAttentionEvent() {
        return FOLLOW_EVENT_LIST;
    }

    @Override
    public boolean onTrigger(PlayerEvent event, int triggerId, TriggerInfoProp triggerInfoProp) {
        boolean result = false;
        final ActivityResService activityResService = ResHolder.getResService(ActivityResService.class);
        final PlayerKillBigSceneMonsterEvent playerKillBigSceneMonsterEvent = (PlayerKillBigSceneMonsterEvent) event;
        final Map<CommonEnum.ActTriggerParamType, Integer> params = activityResService.getActTriggerParams(triggerId);
        if (params == null) {
            return false;
        }
        final int param = params.get(CommonEnum.ActTriggerParamType.ATPT_MONSTER_CATEGORY);
        if (playerKillBigSceneMonsterEvent.getMonsterCategory() == param) {
            result = true;
        }
        LOGGER.info("ActKillMonsterTrigger onTrigger event {} triggerId {} isSatisfied {}", event, triggerId, result);
        if (result) {
            triggerInfoProp.setTriggerTime(triggerInfoProp.getTriggerTime() + 1);
            LOGGER.info("ActKillMonsterTrigger onTrigger, triggerTime={}", triggerInfoProp.getTriggerTime());
        }
        return result;
    }
}

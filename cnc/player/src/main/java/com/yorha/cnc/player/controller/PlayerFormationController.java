package com.yorha.cnc.player.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.player.PlayerEntity;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerFormation.*;
import com.yorha.proto.StructPlayerPB;

import java.util.Map;

/**
 * 编队相关协议
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_FORMATION)
public class PlayerFormationController {

    /**
     * 编队
     */
    @CommandMapping(code = MsgType.PLAYER_EDITFORMATION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_EditFormation_C2S msg) {
        StructPlayerPB.EditFormation_C2S_ParamPB param = msg.getParam();
        playerEntity.getTroopFormationComponent().addNewFormation(param.getFormation());
        // s2c
        return Player_EditFormation_S2C.getDefaultInstance();
    }

    /**
     * 编队更名
     */
    @CommandMapping(code = MsgType.PLAYER_RENAMEFORMATION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RenameFormation_C2S msg) {
        int formationId = msg.getId();
        String formationName = msg.getName();
        playerEntity.getTroopFormationComponent().changeFormationName(formationId, formationName);
        // s2c
        return Player_RenameFormation_S2C.getDefaultInstance();
    }

    /**
     * 删除编队
     */
    @CommandMapping(code = MsgType.PLAYER_REMOVEFORMATION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_RemoveFormation_C2S msg) {
        int formationId = msg.getId();
        playerEntity.getTroopFormationComponent().dismissFormation(formationId);
        // s2c
        return Player_RemoveFormation_S2C.getDefaultInstance();
    }

    /**
     * 编辑PVE编队
     */
    @CommandMapping(code = MsgType.PLAYER_EDITRHTROOP_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_EditRHTroop_C2S msg) {
        playerEntity.getTroopFormationComponent().editRHTroop(msg.getTroopId(), msg.getSlotId(), msg.getHeroId());
        // s2c
        return Player_EditRHTroop_S2C.getDefaultInstance();
    }


    /**
     * 编辑虚拟编队
     */
    @CommandMapping(code = MsgType.PLAYER_EDITVIRTUALFORMATION_C2S)
    public GeneratedMessageV3 handle(PlayerEntity playerEntity, Player_EditVirtualFormation_C2S msg) {
        Map<Integer, Integer> positionMap = msg.getPositionMap();
        playerEntity.getTroopFormationComponent().editVirtualFormation(positionMap);
        // s2c
        return Player_EditVirtualFormation_S2C.getDefaultInstance();
    }

}

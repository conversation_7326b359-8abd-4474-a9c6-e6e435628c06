package com.yorha.game;

import io.xjar.XCryptos;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 加密函数主体。
 *
 * <AUTHOR>
 */
public class EncryptionMain {
    private static final int ARGS_ARGV = 3;
    private static final int ARGS_ORIGIN_JAR = 0;
    private static final int ARGS_ENCRYPTED_JAR = 1;
    private static final int ARGS_PASSWORD = 2;
    private static final int ARGS_PASSWORD_LENGTH = 1;

    public static void main(String[] args) throws Exception {
        System.out.println(Arrays.stream(args).collect(Collectors.toList()));
        if (args.length != ARGS_ARGV) {
            throw new IllegalArgumentException("Input Args Not Right! You Need (Jar, TargetJar, Password)!");
        }
        final File jarFile = new File(args[ARGS_ORIGIN_JAR]);
        if (!jarFile.exists()) {
            throw new FileNotFoundException("jar " + args[0] + " not exit's");
        }
        final File encryptedJar = new File(args[ARGS_ENCRYPTED_JAR]);
        if (!(new File(encryptedJar.getParent())).isDirectory()) {
            throw new FileNotFoundException("target jar " + args[1] + " directory not exit's");
        }
        if (args[ARGS_PASSWORD].length() < ARGS_PASSWORD_LENGTH) {
            throw new IllegalArgumentException("password " + args[2] + " need more than "
                    + ARGS_PASSWORD_LENGTH + " chars");
        }
        XCryptos.encryption()
                .from(args[0])
                .use(args[2])
                // 加密业务文件
                .include("/com/yorha/**/*.class")
                // 加密mock文件
                .include("/org/apache/logging/log4j/core/async/AsyncLoggerDisruptor.class")
                .include("/io/nats/client/impl/MessageQueue.class")
                .include("/com/tencent/**/*.class")
                .include("/res/template/*.class")
                .include("/qlog/flow/*.class")
                .to(args[1]);
    }
}

package main

import (
	"bytes"
	"crypto/md5"
	"crypto/sha1"
	"errors"
	"fmt"
	"hash"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
)

const (
	CODE_OK = iota
	CODE_ERR_NO_JAR
	CODE_ERR_JAR_PATH
	CODE_ERR_JAR_NO_MD5
	CODE_ERR_JAR_MD5_WRONG
	CODE_ERR_JAR_PATH_NO_SHA1
	CODE_ERR_JAR_PATH_SHA1_WRONG
	CODE_ERR_JAVA_AGENT_FORBID
	CODE_ERR_NOT_CMD_JAVA
	CODE_ERR_JAVA_NOT_RIGHT
	CODE_ERR_JAVA_BUFFER_SMALL
	CODE_ERR_JAVA_ITEM_1_ERR
	CODE_ERR_JAVA_ITEM_2_ERR
	CODE_ERR_JAVA_ITEM_3_ERR
	CODE_ERR_JAVA_PROGRAM
)

var xJar = XJar{
	md5:  []byte{#{xJar.md5}},
	sha1: []byte{#{xJar.sha1}},
}

var xKey = XKey{
	algorithm: []byte{#{xKey.algorithm}},
	keysize:   []byte{#{xKey.keysize}},
	ivsize:    []byte{#{xKey.ivsize}},
	password:  []byte{#{xKey.password}},
}

type JavaVersionOutput struct {
	buffer []byte
}

func NewBuffer() io.ReadWriter {
	return &JavaVersionOutput{
		buffer: make([]byte, 100),
	}
}

func (ou *JavaVersionOutput) Write(p []byte) (n int, err error) {
	for _, b := range p {
		ou.buffer = append(ou.buffer, b)
	}
	return len(p), nil
}

func (ou *JavaVersionOutput) Read(p []byte) (n int, err error) {
	if len(p) < len(ou.buffer) {
		return 0, fmt.Errorf("Buffer Too Small")
	}
	for index, b := range ou.buffer {
		p[index] = b
	}
	n = len(ou.buffer)
	ou.buffer = ou.buffer[:0]
	return n, nil
}


func main() {
	// 从参数中读取jar包地址
	jar, err := JAR(os.Args)
	if err != nil {
		fmt.Println("error: ", CODE_ERR_NO_JAR)
		return
	}
	// 校验Jar包 MD5
	path, err := filepath.Abs(jar)
	if err != nil {
		fmt.Println("error: ", CODE_ERR_JAR_PATH)
		return
	}
	MD5, err := MD5(path)
	if err != nil {
		fmt.Println("error: ", CODE_ERR_JAR_NO_MD5)
		return
	}
	if bytes.Compare(MD5, xJar.md5) != 0 {
		fmt.Println("error: ", CODE_ERR_JAR_MD5_WRONG)
		return
	}
	// 校验jar包 with SHA-1
	SHA1, err := SHA1(path)
	if err != nil {
		fmt.Println("error: ", CODE_ERR_JAR_PATH_NO_SHA1)
		return
	}
	if bytes.Compare(SHA1, xJar.sha1) != 0 {
		fmt.Println("error: ", CODE_ERR_JAR_PATH_SHA1_WRONG)
		return
	}
	// 静止agent模式
	{
		args := os.Args
		l := len(args)
		for i := 0; i < l; i++ {
			arg := args[i]
			if strings.HasPrefix(arg, "-javaagent:") {
				fmt.Println("error: ", CODE_ERR_JAVA_AGENT_FORBID)
				return
			}
		}
	}
	// 校验java版本
	java := os.Args[1]
	args := os.Args[2:]
	if java != "java" {
		fmt.Println("error: ", CODE_ERR_NOT_CMD_JAVA)
		return
	}
	javaCode := JAVA(java)
	if javaCode != CODE_OK {
		fmt.Println("error: ", javaCode)
		return
	}
    // 正式启动
	key := bytes.Join([][]byte{
		xKey.algorithm, {13, 10},
		xKey.keysize, {13, 10},
		xKey.ivsize, {13, 10},
		xKey.password, {13, 10},
	}, []byte{})
	cmd := exec.Command(java, args...)
	cmd.Stdin = bytes.NewReader(key)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	err = cmd.Run()
	if err != nil {
		fmt.Println(err)
		fmt.Println("error: ", CODE_ERR_JAVA_PROGRAM)
	}
}

func JAVA(java string) int {
	cmd := exec.Command(java, "-version")
	errBuffer := NewBuffer()
	outBuffer := NewBuffer()
	cmd.Stdout = outBuffer
	cmd.Stderr = errBuffer
	err := cmd.Run()
	if err != nil {
		return CODE_ERR_JAVA_NOT_RIGHT
	}
	bb := make([]byte, 4096)
	if _, err := errBuffer.Read(bb); err != nil {
		return CODE_ERR_JAVA_BUFFER_SMALL
	}
	items := strings.Split(string(bb), "\n")
	re1, err := regexp.Compile("openjdk version \"1\\.8\\.0_[0-9]+_fiber\"")
	if err != nil {
		return CODE_ERR_JAVA_ITEM_1_ERR
	}
	if !re1.Match([]byte(strings.TrimSpace(items[0]))) {
		return CODE_ERR_JAVA_ITEM_1_ERR
	}
	re2, err := regexp.Compile("OpenJDK Runtime Environment \\(Tencent Kona 8\\.[0-9]+\\.[0-9]+\\) \\(build 1\\.8\\.0_[0-9]+_fiber.*\\)")
	if err != nil {
		return CODE_ERR_JAVA_ITEM_2_ERR
	}
	if !re2.Match([]byte(strings.TrimSpace(items[1]))) {
		return CODE_ERR_JAVA_ITEM_2_ERR
	}
	re3, err := regexp.Compile("OpenJDK 64-Bit Server VM \\(Tencent Kona 8\\.[0-9]+\\.[0-9]+\\) \\(build .*\\)")
	if err != nil {
		return CODE_ERR_JAVA_ITEM_3_ERR
	}
	if !re3.Match([]byte(strings.TrimSpace(items[2]))) {
		return CODE_ERR_JAVA_ITEM_3_ERR
	}
	return CODE_OK
}

// find jar name from args
func JAR(args []string) (string, error) {
	var jar string

	l := len(args)
	for i := 1; i < l-1; i++ {
		arg := args[i]
		if arg == "-jar" {
			jar = args[i+1]
		}
	}
	if jar == "" {
		return "", errors.New("unspecified jar name")
	}

	return jar, nil
}

// calculate file's MD5
func MD5(path string) ([]byte, error) {
	return HASH(path, md5.New())
}

// calculate file's SHA-1
func SHA1(path string) ([]byte, error) {
	return HASH(path, sha1.New())
}

// calculate file's HASH value with specified HASH Algorithm
func HASH(path string, hash hash.Hash) ([]byte, error) {
	file, err := os.Open(path)

	if err != nil {
		return nil, err
	}

	_, _err := io.Copy(hash, file)
	if _err != nil {
		return nil, _err
	}

	sum := hash.Sum(nil)

	return sum, nil
}

type XJar struct {
	md5  []byte
	sha1 []byte
}

type XKey struct {
	algorithm []byte
	keysize   []byte
	ivsize    []byte
	password  []byte
}

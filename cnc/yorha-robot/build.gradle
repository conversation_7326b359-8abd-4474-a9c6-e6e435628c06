dependencies {
    implementation project(':common')
    implementation("commons-cli:commons-cli:1.9.0")
}

ext {
    destination = "$releaseRoot/robotcli"
}


jar {
    manifest {
        attributes 'Main-Class': "com.yorha.robot.RobotApp"
        attributes 'Class-Path': ' . ' + configurations.runtimeClasspath.collect { "lib/${it.name}" }.join(' ')
    }
    destinationDirectory = file("$destination")
}


tasks.register('clearJar', Delete) {
    delete "$destination/lib"
}

tasks.register('copyJar', Sync) {
    from configurations.runtimeClasspath;
    into "$destination/lib"
}

tasks.register('copyResources', Copy) {
    sourceSets.main.resources.srcDirs.each {
        from it
        into "$destination"
    }
}


tasks.register('release', Copy) {
    dependsOn build, copyJar, copyResources
}


processResources {
    exclude { "**/*.*" };
}


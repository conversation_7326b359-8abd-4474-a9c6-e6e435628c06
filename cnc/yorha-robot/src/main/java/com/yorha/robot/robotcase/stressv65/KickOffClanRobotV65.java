package com.yorha.robot.robotcase.stressv65;

import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.clan.ApplyJoinClanFlow;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.flow.clan.KickOffClanMemberFlow;
import com.yorha.robot.flow.player.FeatureUnlockFlow;
import com.yorha.robot.flow.player.SendGmFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class KickOffClanRobotV65 extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(KickOffClanRobotV65.class);

    public KickOffClanRobotV65(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        final int clanNum = getRobotId() / Constant.CLAN_MEMBER_MAX;
        // 解锁功能
        runFlow(new FeatureUnlockFlow());
        // 获取资源
        runFlow(new SendGmFlow(), "AddCurrency currencyType=5 count=1000");
        // 创建或等待军团
        runFlow(new CreateOrApplyClanFlow());

        // 数据准备
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 联盟总个数
        final int totalClanCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - 1) / Constant.CLAN_MEMBER_MAX + 1;
        waitState("waitAllClanReady", () -> scene.getClanCount() >= totalClanCount);
        boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;

        scene.addAlreadyJoinClanPlayer();
        waitState("waitAllPlayerJoinClan", () -> scene.getAlreadyJoinClanPlayer() >= RobotMgr.getInstance().getAllRobot(getUser()).size());


        int runTimes = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int totalCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - totalClanCount) * runTimes;
        // 循环入口
        for (int i = 0; i < runTimes; ++i) {
            onceRun(scene, totalCount, isClanOwner, clanNum);
        }
    }

    private void onceRun(ClanScene scene, int totalCount, boolean isClanOwner, int clanNum) {
        LoginScene countScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        // 循环入口
        if (isClanOwner) {
            while (true) {
                // 除了自己全部踢出去
                runFlow(new KickOffClanMemberFlow(), getBoard().getPlayerProp().getClan().getClanId(), getBoard().getPlayerId());
                // 看看测完没
                if (countScene.checkCountFinished(totalCount)) {
                    break;
                }
            }
        } else {
            // 等待退出军团
            waitState("waitQuitClanFinish", () -> getBoard().getPlayerProp().getClan().getClanId() == 0);
            // 申请加入军团
            runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
            // 确保加入军团
            waitState("waitJoinClanFinish", () -> getBoard().getPlayerProp().getClan().getClanId() != 0);
            // 成员做一轮自增一次计数
            countScene.addCount();
        }
    }
}

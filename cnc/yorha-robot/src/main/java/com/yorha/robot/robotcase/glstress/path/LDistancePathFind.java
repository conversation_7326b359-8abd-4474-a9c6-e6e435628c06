package com.yorha.robot.robotcase.glstress.path;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.SearchWalkPathFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 远距离寻路（随机寻路）
 */
public class LDistancePathFind extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(LDistancePathFind.class);

    public LDistancePathFind(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "FinMileStone count=26");
            runFlow(new DebugCmdFlow(), "Offset seconds=3600");
        }

        // 1、登录
        // 2、延迟1~20秒，错峰
        this.realSleep(RandomUtils.nextInt(1000, 20000));
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        PointProp point = getBoard().getCityProp().getPoint();
        while (i >= 0) {
            i++;
            Point targetPoint = Point.valueOf(point.getX() + RandomUtils.nextInt(-5000, 5000), point.getY() + RandomUtils.nextInt(-5000, 5000));
            try {
                runFlow(new SearchWalkPathFlow(), targetPoint);
            } catch (RuntimeException e) {
                LOGGER.warn("SearchWalkPathFlow no path. start={} end={}", point, targetPoint);
            }
            realSleep(RandomUtils.nextInt(1000, 2000));
        }
    }

}

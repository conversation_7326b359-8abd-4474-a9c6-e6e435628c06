package com.yorha.robot.scene;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.AbstractRobotScene;

import java.util.HashSet;

/**
 * 战斗压测用例场景共享数据
 *
 * <AUTHOR>
 */
public class BeautifulBattleScene extends AbstractRobotScene {

    private final HashSet<Integer> cityPrepared = Sets.newHashSet();

    private final HashSet<Long> armyArrived = Sets.newHashSet();

    public void markCityPrepared(int robotId) {
        cityPrepared.add(robotId);
    }

    public void unMarkCityPrepared(int robotId) {
        cityPrepared.remove(robotId);
    }

    public void markArmyArrived(long armyId) {
        armyArrived.add(armyId);
    }

    public HashSet<Long> getArmyArrived() {
        return armyArrived;
    }

    public boolean isArmyArrived(long armyId) {
        return armyArrived.contains(armyId);
    }

    public Integer getRandomCity() {
        return RandomUtils.randomList(Lists.newArrayList(cityPrepared));
    }

    public boolean isCityPrepared(int robotId) {
        return cityPrepared.contains(robotId);
    }
}

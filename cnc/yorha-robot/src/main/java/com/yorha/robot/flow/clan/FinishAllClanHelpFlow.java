package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class FinishAllClanHelpFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(FinishAllClanHelpFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_FinishAllClanHelps_C2S.Builder builder = PlayerClan.Player_FinishAllClanHelps_C2S.newBuilder();
        robot.sendMsgToServerSync(MsgType.PLAYER_FINISHALLCLANHELPS_C2S, builder.build());
    }
}

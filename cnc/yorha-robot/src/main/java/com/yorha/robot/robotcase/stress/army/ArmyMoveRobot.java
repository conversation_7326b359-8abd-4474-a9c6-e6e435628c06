package com.yorha.robot.robotcase.stress.army;

import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.ChangeArmyFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ArmyScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ArmyMoveRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ArmyMoveRobot.class);

    public ArmyMoveRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        doOnce();
        finished();
    }

    private void doOnce() {
        // 行军不限制兵力上限
        CncFlowUtil.sendDebugCommand(this, "DebugSwitch type=createArmyNoSoldierLimit value=true");
        waitAllRobotLoginSuccess();
        
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }

        // 编队
        PointProp pointProp = getBoard().cityProp.getPoint();
        // 判断随机点能否出发
        Point point = Point.valueOf(pointProp.getX() + 710, pointProp.getY() + 710);
        //runFlow(new SearchWalkPathFlow(), point);
        // 出发
        runFlow(new CreateArmyToAnyWhereFlow(), true, point, buildArmyTroop(this, false, 1000));
        // 记录
        waitState("waitArmyCreated", () -> getBoard().getMyArmyIds().size() > 0);
        ArmyScene scene = RobotMgr.getInstance().getScene(getUser(), ArmyScene.class);
        long myArmyId = getBoard().getMyArmies().get(0).getFirst();
        scene.addArmy(getRobotId(), myArmyId);

        // 找敌军id
        int enemyArmyId = (getRobotId() + 1) % ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum;
        waitState("waitAllArmyCreated", () -> scene.getArmyId(enemyArmyId) > 0);
        // 向目标行军
        runFlow(new ChangeArmyFlow(), myArmyId, CommonEnum.ArmyActionType.AAT_Battle, scene.getArmyId(enemyArmyId), 0, 0);

        // 等待20秒后，撤军
        realSleep(20000);
        runFlow(new FastReturnAllMyArmyFlow());
    }

}

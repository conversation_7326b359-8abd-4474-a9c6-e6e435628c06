package com.yorha.robot.simple;

import com.google.common.collect.Lists;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.SimpleMockClient;

import java.util.ArrayList;
import java.util.concurrent.CountDownLatch;

//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                            O\ = /O
//                        ____/`---'\____
//                      .   ' \\| |// `.
//                       / \\||| : |||// \
//                     / _||||| -:- |||||- \
//                       | | \\\ - /// | |
//                     | \_| ''\---/'' | |
//                      \ .-\__ `-` ___/-. /
//                   ___`. .' /--.--\ `. . __
//                ."" '< `.___\_<|>_/___.' >'"".
//               | | : `- \`.;`\ _ /`;.`/ - ` : | |
//                 \ \ `-. \_ __\ /__ _/ .-` / /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//
//         .............................................
public class DamonMockClient extends SimpleMockClient {

    public static void main(String[] args) throws InterruptedException {
        int svrPort = 8928;
        initAll(svrPort);
        String openId = "c42c03b9b3a7484d8b276a6d2c16600f";
//        long playerId = 8062528400L;
        long playerId = 0;
        int zoneId = 1;
        int roleCount = 100;
        CountDownLatch countDownLatch = new CountDownLatch(roleCount);
        for (int i = 1; i <= roleCount; i++) {
            final int finalI = i;
            Thread.startVirtualThread(() -> {
                try {
                    Thread.sleep(RandomUtils.nextInt(10000));
                    Single single = new Single();

                    single.init(svrPort);

                    single.login(openId, 0, zoneId);

                    PlayerCommon.Player_ChangePlayerName_C2S.Builder changeNameReq = PlayerCommon.Player_ChangePlayerName_C2S.newBuilder();
                    changeNameReq.setType(CommonEnum.ChangeNameType.CNT_ITEM)
                            .setName("damontest" + finalI);
                    single.sendAndWait(changeNameReq.build());

                    ArrayList<Integer> pointsTemplateIds = Lists.newArrayList(
                            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 30, 31, 32, 33, 34, 35);
                    for (int pointsTemplateId : pointsTemplateIds) {
                        int count = RandomUtils.nextInt(10000);
                        String command = "GiveActivityPoints templateId=" + pointsTemplateId + " count=" + count;
                        single.sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand(command).build());
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                countDownLatch.countDown();
            });
        }
        countDownLatch.await();

    }

}

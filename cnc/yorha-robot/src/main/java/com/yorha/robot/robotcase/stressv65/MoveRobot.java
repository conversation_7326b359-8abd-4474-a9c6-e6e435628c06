package com.yorha.robot.robotcase.stressv65;

import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 移动测试
 * <AUTHOR>
 * @date 2023/7/11
 */
public class MoveRobot extends EnterWorldRobot {
    public MoveRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        // 行军不限制兵力上限
        CncFlowUtil.sendDebugCommand(this, "DebugSwitch type=createArmyNoSoldierLimit value=true");
        waitAllRobotLoginSuccess();
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int i = 0;
        while (i < executeCount) {
            if (getBoard().getMyArmyIds().size() <= 0) {
                // 编队
                PointProp pointProp = getBoard().cityProp.getPoint();
                // 创建一只部队
                Point point = Point.valueOf(pointProp.getX() + 1000, pointProp.getY() + 1000);
                // 出发
                runFlow(new CreateArmyToAnyWhereFlow(), true, point, buildArmyTroop(this, false, 500));
                // 记录
                waitState("waitArmyCreated", () -> getBoard().getMyArmyIds().size() > 0);
            }
            runFlow(new DebugCmdFlow(), "RandomFastMove");
            this.realSleep(5000);
            i++;
        }
        end();
    }
}
package com.yorha.robot.robotcase.stressv6;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.robotcase.stress.spy.CreateSpyPlaneStressRobot;

/**
 * <AUTHOR>
 */
public class SpyExploreRobot extends CreateSpyPlaneStressRobot {

    public SpyExploreRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            CncFlowUtil.sendDebugCommand(this, "GiveMePowerSimple");
        }
        waitAllRobotLoginSuccess();
        realSleep(5000 + RandomUtils.nextInt(5000));
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        returnHome();

        while (i > 0) {
            i--;

            try {
                int random = i % 25;
                realSleep(RandomUtils.nextInt(15000) + 3000);
                if (random == 0) {
                    changeSpy();
                    continue;
                }

                if (random % 3 == 0) {
                    createExploreFog();
                    continue;
                }

                if (random % 3 == 1) {
                    changeExploreFog();
                    continue;
                }
                returnHome();
            } catch (Exception e) {

            }
        }
        try {
            returnHome();
        } catch (Exception e) {

        } finally {
            end();
        }
    }

}

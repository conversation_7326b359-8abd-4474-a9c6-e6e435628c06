package com.yorha.robot.robotcase.glstress.troop;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 视野
 */
public class CreateTroop extends EnterWorldRobot {
    public CreateTroop(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "FinMileStone count=26");
            runFlow(new DebugCmdFlow(), "Offset seconds=3600");
        }
        runFlow(new DebugCmdFlow(), "GiveMePower");
        this.realSleep(1000);

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i >= 0) {
            i++;
            randomCreateArmyOrCancel();
            realSleep(RandomUtils.nextInt(1000, 2000));
        }

    }

    private void randomCreateArmyOrCancel() {
        // 编队
        PointProp pointProp = getBoard().cityProp.getPoint();
        // 创建一只部队
        Point point = Point.valueOf(pointProp.getX() + 1000, pointProp.getY() + 1000);
        // 出发
        StructPlayerPB.TroopPB troopPB = buildArmyTroop(this, false, 500);
        if (troopPB.getMainHero().getHeroId() == 0) {
            return;
        }
        runFlow(new CreateArmyToAnyWhereFlow(), true, point, troopPB);
        // 记录
        waitState("waitArmyCreated", () -> getBoard().getMyArmyIds().size() > 0);
        realSleep(10_000);
        // 回家
        runFlow(new FastReturnAllMyArmyFlow());
        waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
    }
}

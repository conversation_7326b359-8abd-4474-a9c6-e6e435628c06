package com.yorha.robot.scene;

import com.yorha.robot.core.AbstractRobotScene;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class CityScene extends AbstractRobotScene {
    /**
     * 主城id
     */
    private final Map<Integer, Long> cityMap = new ConcurrentHashMap<>();


    public void addCity(int robotId, long cityId) {
        cityMap.put(robotId, cityId);
    }


    public long getRandomCityId(int robotId) {
        return cityMap.getOrDefault(robotId, 0L);
    }
}

package com.yorha.robot.flow.user;

import com.yorha.common.io.MsgType;
import com.yorha.proto.User;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class LoginOrCreatePlayerFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(LoginFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {

        long playerId = robot.getBoard().getPlayerId();

        User.Login_C2S_Msg.Builder builder = User.Login_C2S_Msg.newBuilder();
        builder.setAccountToken(robot.getAccountToken())
                .setPlayerId(playerId)
                .setIsRegister(playerId == 0)
                .setDebugStartNewbie((Boolean) args[0])
                .setZoneId(robot.getBoard().getZoneId());
        robot.sendMsgToServerSync(MsgType.LOGIN_C2S_MSG, builder.build());
    }
}
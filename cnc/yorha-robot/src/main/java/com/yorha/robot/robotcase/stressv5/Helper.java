package com.yorha.robot.robotcase.stressv5;

import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableList;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface Helper {
    Map<Integer, List<Point>> REGION_POINT = ImmutableBiMap.of(
            0, ImmutableList.of(
                    Point.valueOf(212000, 299000),
                    Point.valueOf(28000, 114000),
                    Point.valueOf(356000, 131000),
                    Point.valueOf(340000, 76000),
                    Point.valueOf(222000, 456000),
                    Point.valueOf(134000, 192000)),
            5, ImmutableList.of(
                    Point.valueOf(51000, 1039000),
                    Point.valueOf(340000, 1202000),
                    Point.valueOf(114000, 833000),
                    Point.valueOf(303000, 987000),
                    Point.valueOf(148000, 982000),
                    Point.valueOf(222000, 813000)),
            6, ImmutableList.of(
                    Point.valueOf(387000, 585000),
                    Point.valueOf(390000, 834000),
                    Point.valueOf(375000, 784000),
                    Point.valueOf(372000, 715000),
                    Point.valueOf(334000, 630000),
                    Point.valueOf(324000, 560000))
    );

    Map<Integer, List<Integer>> CROSS_LIST = ImmutableBiMap.of(
            0, ImmutableList.of(10032, 10033, 10034, 10035, 10036),
            5, ImmutableList.of(60030, 60031, 60032, 60033, 60034)
    );

    static int getRegionId(Point point) {
        if (point.getY() > 625000) {
            return 5;
        }
        return 0;
    }

    static int getRegionId(PointProp point) {
        if (point.getY() > 625000) {
            return 5;
        }
        return 0;
    }

    static Point getRegionPoint(int regionId, int index) {
        List<Point> list = REGION_POINT.get(regionId);
        return list.get(index % list.size());
    }

    static int getRegionCross(int regionId, int index) {
        return CROSS_LIST.get(regionId).get(index);
    }
}

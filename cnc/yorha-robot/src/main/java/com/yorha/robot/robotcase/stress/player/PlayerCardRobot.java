package com.yorha.robot.robotcase.stress.player;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.player.QueryPlayerCardInfoFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 查询玩家名片
 *
 * <AUTHOR>
 */
public class PlayerCardRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(PlayerCardRobot.class);

    public PlayerCardRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        if (getRobotId() % 2 == 0) {
            // 一半的人不在线
            end();
            return;
        }

        realSleep(5000 + RandomUtils.nextInt(1000));

        int robotNum = ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum;
        LoginScene loginScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i > 0) {
            i--;
            int robotId = RandomUtils.nextInt(robotNum);
            if (robotId == getRobotId()) {
                robotId = (robotId + 1) % robotNum;
            }
            long targetId = loginScene.getRobotPlayerId(robotId);
            runFlow(new QueryPlayerCardInfoFlow(), targetId);

            int random = RandomUtils.nextInt(10);
            if (random == 0) {
                // 十分之一的概率
                PlayerCommon.Player_QueryPlayerCardInfoDetail_C2S.Builder b2 = PlayerCommon.Player_QueryPlayerCardInfoDetail_C2S.newBuilder();
                b2.setPlayerId(targetId);
                sendMsgToServerSync(MsgType.PLAYER_QUERYPLAYERCARDINFODETAIL_C2S, b2.build());
            } else if (random == 1) {
                // 十分之一的概率
                PlayerCommon.Player_QueryPlayerKillDetail_C2S.Builder b3 = PlayerCommon.Player_QueryPlayerKillDetail_C2S.newBuilder();
                b3.setPlayerId(targetId);
                sendMsgToServerSync(MsgType.PLAYER_QUERYPLAYERKILLDETAIL_C2S, b3.build());
            }

            realSleep(1000);
        }
        end();
    }
}

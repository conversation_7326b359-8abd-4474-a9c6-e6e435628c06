package com.yorha.robot.flow.rally;

import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.proto.*;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 创建或者加入集结集结
 *
 * <AUTHOR>
 */
public class CreateOrJoinRallyFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateOrJoinRallyFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        // 联盟编号
        int clanNum = robot.getRobotId() / Constant.CLAN_MEMBER_MAX;
        boolean isClanOwner = (robot.getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;
        ClanScene scene = RobotMgr.getInstance().getScene(robot.getUser(), ClanScene.class);

        if (isClanOwner) {
            robot.waitState("waitCreateRally", () -> scene.getBattleTargetCity(clanNum) > 0);
            LOGGER.info("{} try create rally success. targetId :{}", robot, scene.getBattleTargetCity(clanNum));
            StructPlayerPB.ArmyActionInfoPB.Builder info = StructPlayerPB.ArmyActionInfoPB.newBuilder();
            info.setArmyActionType(CommonEnum.ArmyActionType.AAT_CreateRally)
                    .setTargetId(scene.getBattleTargetCity(clanNum)).setWaitSecondsTime(600).setDebugFastMove(true);
            PlayerScene.Player_CreateArmy_C2S.Builder builder = PlayerScene.Player_CreateArmy_C2S.newBuilder();
            builder.getParamBuilder().setArmyAction(info).setTroopInfo(buildArmyTroop(robot));
            PlayerScene.Player_CreateArmy_S2C rspMsg = (PlayerScene.Player_CreateArmy_S2C)
                    robot.sendMsgToServerSync(MsgType.PLAYER_CREATEARMY_C2S, builder.build());

            LOGGER.debug("{} create army success. armyId :{}", robot, rspMsg.getArmyId());
            PlayerRally.Player_QueryRallyList_C2S.Builder builder2 = PlayerRally.Player_QueryRallyList_C2S.newBuilder();
            PlayerRally.Player_QueryRallyList_S2C rspMsg2 = (PlayerRally.Player_QueryRallyList_S2C)
                    robot.sendMsgToServerSync(MsgType.PLAYER_QUERYRALLYLIST_C2S, builder2.build());
            scene.addRallyId(clanNum, rspMsg2.getRallyList().getDatasList().getFirst().getRallyId());
            LOGGER.debug("{} create rally success. armyId :{}", robot, rspMsg2.getRallyList().getDatasList().getFirst().getRallyId());
        } else {
            // 等待自己盟的集结创建好
            robot.waitState("waitCreateRally", () -> scene.getRallyId(clanNum) > 0);
            StructPlayerPB.ArmyActionInfoPB.Builder info = StructPlayerPB.ArmyActionInfoPB.newBuilder();
            info.setArmyActionType(CommonEnum.ArmyActionType.AAT_JoinRally)
                    .setTargetId(scene.getRallyId(clanNum)).setDebugFastMove(true);
            PlayerScene.Player_CreateArmy_C2S.Builder builder = PlayerScene.Player_CreateArmy_C2S.newBuilder();
            builder.getParamBuilder().setArmyAction(info).setTroopInfo(buildArmyTroop(robot));
            PlayerScene.Player_CreateArmy_S2C rspMsg = (PlayerScene.Player_CreateArmy_S2C)
                    robot.sendMsgToServerSync(MsgType.PLAYER_CREATEARMY_C2S, builder.build());
            LOGGER.debug("{} create army join rally success. armyId :{}", robot, rspMsg.getArmyId());
        }
    }

    private StructPlayerPB.TroopPB buildArmyTroop(CncRobot me) {
        StructPlayerPB.TroopPB.Builder builder = StructPlayerPB.TroopPB.newBuilder();
        PlayerProp playerProp = me.getBoard().getPlayerProp();

        // 英雄设置
        Collection<PlayerHeroProp> myHeroes = playerProp.getPlayerHeroModel().getPlayerHeroMap().values();
        List<PlayerHeroProp> idleHeroes = myHeroes.stream()
                .filter(p -> p.getState() == CommonEnum.HeroState.HERO_IDLE)
                .collect(Collectors.toList());
        Collections.shuffle(idleHeroes);
        if (!idleHeroes.isEmpty()) {
            builder.setMainHero(StructPB.HeroPB.newBuilder().setHeroId(idleHeroes.getFirst().getHeroId()).build());
        }
        // 兵力设置
        playerProp.getScenePlayer().getSoldier().getSoldierMap().forEach((soldierId, mySoldier) -> {
            StructPB.SoldierPB soldierPb = StructPB.SoldierPB.newBuilder()
                    .setSoldierId(soldierId)
                    .setNum(10)
                    .build();
            builder.getTroopBuilder().putDatas(soldierId, soldierPb);
        });
        return builder.build();
    }
}

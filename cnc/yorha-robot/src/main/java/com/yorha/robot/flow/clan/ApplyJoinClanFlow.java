package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan.Player_ApplyJoinClan_C2S;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2021/08/20 15:41
 */
public class ApplyJoinClanFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(ApplyJoinClanFlow.class);

    /**
     * long clanId
     */
    @Override
    public void run(Cnc<PERSON><PERSON><PERSON> robot, Object[] args) {
        long clanId = (long) args[0];
        Player_ApplyJoinClan_C2S.Builder builder = Player_ApplyJoinClan_C2S.newBuilder();
        builder.setClanId(clanId);
//        LOGGER.info("ApplyJoinClanFlow robotId={}, clanId={}", robot.getRobotId(), clanId);
        robot.sendMsgToServerSync(MsgType.PLAYER_APPLYJOINCLAN_C2S, builder.build());
    }
}

package com.yorha.robot.robotcase.stress.army;

import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
@RobotCase(
        name = "创建行军",
        desc = ""
)
public class CreateArmyRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ArmyMoveRobot.class);

    public CreateArmyRobot(String robotName, Integer robotId, User user) {
        super(robotN<PERSON>, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }
        waitAllRobotLoginSuccess();
        PointProp pointProp = getBoard().cityProp.getPoint();
        // 随机点
        Point point = Point.valueOf(pointProp.getX() + 710, pointProp.getY() + 710);

        // 出发
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        try {
            while (i > 0) {
                runFlow(new CreateArmyToAnyWhereFlow(), true, point, buildArmyTroop(this, false, 1000));
                waitState("army create", () -> !getBoard().getMyArmies().isEmpty());

                this.realSleep(3000);

                runFlow(new FastReturnAllMyArmyFlow());
                waitState("army return", () -> getBoard().getMyArmies().isEmpty());

                this.realSleep(500);
                i--;
            }
        } catch (Exception e) {
            // 可能是找的点不能去，舍弃这些机器人
            finished();
        }
        finished();
    }
}

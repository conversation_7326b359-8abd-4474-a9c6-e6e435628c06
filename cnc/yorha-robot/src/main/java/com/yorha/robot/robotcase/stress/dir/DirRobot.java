package com.yorha.robot.robotcase.stress.dir;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.dir.DirPingFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DirRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(DirRobot.class);

    public DirRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void firstStart() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        connectServerSync(config.host, config.port);
        run();
    }

    @Override
    public void run() {
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        try {
            for (long i = 0; i < executeCount; i++) {
                runFlow(new DirPingFlow());
                runFlow(new DirPingFlow());
                runFlow(new DirPingFlow());
                runFlow(new DirPingFlow());

                realSleep(500);

                // runFlow(new GetZoneListFlow(), 1, "origin");
            }
        } catch (Exception e) {
            LOGGER.error("DirRobot {}", this, e);
        } finally {
            end();
        }

    }
}
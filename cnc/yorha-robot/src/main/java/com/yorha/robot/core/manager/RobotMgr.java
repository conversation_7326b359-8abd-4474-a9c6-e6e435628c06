package com.yorha.robot.core.manager;

import com.google.common.collect.Sets;
import com.google.common.collect.Sets.SetView;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.yorha.common.concurrent.IGeminiDispatcher;
import com.yorha.common.concurrent.NamedRunnable;
import com.yorha.common.concurrent.NamedRunnableWithId;
import com.yorha.common.concurrent.dispatcher.GeminiDispatcher;
import com.yorha.common.concurrent.dispatcher.GeminiDispatcherFactory;
import com.yorha.common.concurrent.dispatcher.GeminiFiberDispatcher;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.constant.LogConstant;
import com.yorha.common.resource.ResLoader;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.robot.core.AbstractRobotScene;
import com.yorha.robot.core.RobotStats;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.base.Robot;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.base.RobotExitCode;
import com.yorha.robot.core.handler.RobotChannelHandler;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.LengthFieldPrepender;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class RobotMgr {
    private static final int THREAD_NUM = 30;
    private static final Logger LOGGER = LogManager.getLogger(RobotMgr.class);
    private static final Logger LOGGER_PERF = LogManager.getLogger(LogConstant.LOG_TYPE_PERF_STAT);
    private static final RobotMgr INSTANCE = new RobotMgr();

    public static RobotMgr getInstance() {
        return RobotMgr.INSTANCE;
    }

    private static final Map<String, User> USERS = new ConcurrentHashMap<>();

    public static RobotStats getFlowStats(User user) {
        return user.getRobotStats();
    }

    public void onRobotStart(User user) {
        user.getCurLimit().incrementAndGet();
    }

    public void onRobotEnd(User user) {
        user.getCurLimit().decrementAndGet();
    }

    private final GeminiThreadPoolExecutor threadPoolExecutor;

    public RobotMgr() {
        this.threadPoolExecutor = ConcurrentHelper.newFixedThreadExecutor("RobotWorker", THREAD_NUM, 0, false);
        Runtime.getRuntime().addShutdownHook(ConcurrentHelper.newThread("ShutdownHook", false, () -> {
            for (User user : USERS.values()) {
                LOGGER.info("initAndRunRobot ShutDownHook Trigger user:{}", user.getUserName());
                allOver(user);
            }
            LogManager.shutdown();
        }));
    }

    /**
     * 压力测试场景下，退出进程
     */
    public void exitWithPress(String userName, RobotExitCode exitCode) {
        if ("press".equals(userName)) {
            System.exit(exitCode.ordinal());
        }
    }

    private void runRobot(User user) {
        USERS.put(user.getUserName(), user);
        if ("press".equals(user.getUserName())) {
            initAndRunRobot(user);
            return;
        }
        threadPoolExecutor.execute(user);
    }

    /**
     * 初始化机器人模拟
     */
    public void init(String userName, String[] args, Class<? extends Config> type, Map<String, Object> robotArgsMap) {
        LOGGER.info("init robot mgr");
        try {
            ConfigMgr.getInstance().parseCmdArgs(userName, args, type);
        } catch (Exception e) {
            LOGGER.error("parse config file fail: ", e);
            exitWithPress(userName, RobotExitCode.CONFIG_WRONG);
            return;
        }
        // 用户发起新的请求，先终止上一个请求
        if (USERS.containsKey(userName)) {
            LOGGER.warn("{} already exist countDown last robots", userName);
            countDown(userName);
        }
        ServerContext.initServerInfo("1.1.4.1");
        ServerContext.loadServerCfg();
        ResLoader.load("../../../cnc/client/game_data/server");
        // 构造配置个数的机器人
        Config config = ConfigMgr.getInstance().getConfig(userName);
        LOGGER.info("create robot num:{}", config.robotNum);
        GeminiFiberDispatcher geminiDispatcherWithFiber = GeminiDispatcherFactory.buildFiberDispatcher("robot-logic", config.robotLogicThreadNum, 256);
        GeminiDispatcher robotMgrDispatcher = GeminiDispatcherFactory.buildDispatcher("robot-mgr", 1, 25600);
        long startTsMs = SystemClock.now();
        int configLimit = config.robotNumInOneBatch;
        Bootstrap bootstrap = initNettyClientEnv(userName);
        Object obj = injectParam(robotArgsMap, userName);
        User user = new User.Builder()
                .setUserName(userName)
                .setBootstrap(bootstrap)
                .setGeminiDispatcherWithFiber(geminiDispatcherWithFiber)
                .setRobotMgrDispatcher(robotMgrDispatcher)
                .setStartTsMs(startTsMs)
                .setConfigLimit(configLimit)
                .setArg(obj)
                .build();
        // 超时终止机器人
        SystemScheduleMgr.getInstance().schedule(() -> countDown(userName), config.timeout, TimeUnit.SECONDS);
        StringCacheMgr.initDisplayDataCache();
        // 启动机器人
        runRobot(user);
    }

    public Object injectParam(Map<String, Object> objectMap, String userName) {
        try {
            String className = RobotMgr.getInstance().getRobotClassName(userName);
            Class demoClass = Class.forName(className).getDeclaredAnnotation(RobotCase.class).args();
            Object object = demoClass.newInstance();
            for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
                Field field = demoClass.getField(entry.getKey());
                field.set(object, entry.getValue());
            }
            return object;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 可能重入，需要注意安全。在一批robot结束后会再次调用
     */
    public void initAndRunRobot(User user) {
        Config config = ConfigMgr.getInstance().getConfig(user.getUserName());
        LOGGER.info("initAndRunRobot {} {}", config.robotNumInOneBatch, config.cycleTimes);
        // 启动1s定时器输出flow perf
        SystemScheduleMgr.getInstance().scheduleWithFixedDelay(new NamedRunnable("flow perf print timer", () -> {
            if (user.isStop()) {
                return;
            }
            getFlowStats(user).flush();
        }), 0, 1, TimeUnit.SECONDS);

        String robotName = "robot-" + config.robotName + "-";
        // 创建所有robot
        createAllRobot(robotName, config, user.getRobotMap(), user);
        // 依次投递任务
        int cycleTimes = config.cycleTimes;
        for (int i = 0; i < cycleTimes; i++) {
            batchRobotRun(config, user);
        }
        if ("press".equals(user.getUserName())) {
            // 全部完成，提前结束
            SystemScheduleMgr.getInstance().schedule(() -> {
                allOver(user);
                exitWithPress(user.getUserName(), RobotExitCode.SUCCESS);
            }, 1, TimeUnit.SECONDS);
        }
    }

    private void allOver(User user) {
        Config config = ConfigMgr.getInstance().getConfig(user.getUserName());
        getGeminiDispatcher(user).getTaskStats().printAndClear();
        getRobotMgrDispatcher(user).getTaskStats().printAndClear();
        getFlowStats(user).flush();
        getFlowStats(user).end();
        getFlowStats(user).stopWithSpecificTime(60);
        long costTime = SystemClock.nowNative() - user.getStartTsMs();
        user.stop();
        USERS.remove(user.getUserName());
        LOGGER_PERF.info("user {} robot {} all finished! num:{} cost:{}s",
                user.getUserName(), ConfigMgr.getInstance().getConfig(user.getUserName()).robotName, config.robotNum, costTime / 1000.0f);
    }

    public String getRobotClassName(String userName) {
        return "com.yorha.robot.robotcase." + ConfigMgr.getInstance().getConfig(userName).robotType;
    }

    /**
     * 创建好所有robot
     */
    private void createAllRobot(String robotName, Config config, Map<Integer, Robot> robotMap, User user) {
        String robotClassName = getRobotClassName(user.getUserName());
        Class<?> robotClass;
        Constructor<?> constructor;
        try {
            robotClass = Class.forName(robotClassName);
            constructor = robotClass.getConstructor(String.class, Integer.class, User.class);
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            LOGGER.error("user {} robot {} not found. {}", user.getUserName(), robotClassName, e);
            exitWithPress(user.getUserName(), RobotExitCode.ROBOT_LOGIC_WRONG);
            return;
        }
        try {
            for (int i = 0; i < config.robotNum; i++) {
                final int robotIndex = i + config.robotStartIndex;
                if (config.randomAccount) {
                    robotName = StringUtils.randomString(10);
                }
                Robot robot = (Robot) constructor.newInstance(robotName, robotIndex, user);
                robotMap.put(robotIndex, robot);
            }
        } catch (IllegalAccessException | InstantiationException | InvocationTargetException e) {
            LOGGER.error("user {} create robot fail ", user.getUserName(), e);
            exitWithPress(user.getUserName(), RobotExitCode.ROBOT_LOGIC_WRONG);
            return;
        }
        LOGGER.info("user:{} create all robot success num:{}", user.getUserName(), config.robotNum);
    }

    private long lastLogTsMs = 0;

    private void showWaitLog(User user, int limit) {
        if (lastLogTsMs == 0) {
            lastLogTsMs = SystemClock.nowNative();
            LOGGER.info("user:{} curLimit:{}", user.getUserName(), limit);
            return;
        }

        long now = SystemClock.nowNative();
        if (now - lastLogTsMs >= 5_000) {
            lastLogTsMs = now;
            LOGGER.info("user:{} curLimit:{}", user.getUserName(), limit);
        }
    }

    private void batchRobotRun(Config config, User user) {
        config.robotStartIndex = 0;
        for (int i = 0; i < config.robotNum; i++) {
            final int robotIndex = i + config.robotStartIndex;
            Robot robot = user.getRobotMap().get(robotIndex);
            RobotMgr.getInstance().onRobotStart(user);
            robot.setStart();
            getGeminiDispatcher(user).execute(NamedRunnableWithId.valueOf(robotIndex, "robot-start-run", robot::firstStart));

            if ((i + 1) % config.robotNumInOneBatch == 0) {
                int interval = config.createIntervalTime;
                if (interval != 0) {
                    try {
                        Thread.sleep(interval);
                    } catch (InterruptedException e) {
                        LOGGER.error("batchRobotRun", e);
                    }
                }
            }
        }
        while (true) {
            int l = user.getCurLimit().get();
            if (user.isStop()) {
                LOGGER.info("user {} wait robot finish isStop left {}", user.getUserName(), l);
                return;
            }
            if (l == 0) {
                break;
            }
            // 自旋等待所有robot完成
            showWaitLog(user, l);
        }
    }

    /**
     * 机器人时间到，没有全部完成，打印失败的机器人和卡住的flow
     */
    private void countDown(String userName) {
        if (!USERS.containsKey(userName)) {
            return;
        }
        User user = USERS.get(userName);
        Config config = ConfigMgr.getInstance().getConfig(userName);
        HashSet<Integer> allRobotIdSet = Sets.newHashSet(user.getFinishedRobotIdSet());
        int unFinishedRobotNum = config.robotNum - user.getFinishedRobotIdSet().size();

        long costTime = SystemClock.nowNative() - user.getStartTsMs();
        LOGGER.error("user:{} robot not finished timeout total:{} unFinishedRobot:{} cost:{}s",
                userName, config.robotNum, unFinishedRobotNum, costTime / 1000.0);

        SetView<Integer> unFinishedRobotIdSet = Sets.difference(allRobotIdSet, user.getFinishedRobotIdSet());
        int minFlowCount = 0;
        for (int robotId : unFinishedRobotIdSet) {
            Robot robot = user.getRobotMap().get(robotId);
            robot.dumpState();
            if (robot.flowCount() < minFlowCount || minFlowCount <= 0) {
                minFlowCount = robot.flowCount();
            }
        }
        LOGGER.info("user:{} min flow count={}", userName, minFlowCount);
        allOver(user);
        exitWithPress(userName, RobotExitCode.ROBOT_LOGIC_WRONG);
    }

    public IGeminiDispatcher getGeminiDispatcher(User user) {
        return user.getGeminiDispatcherWithFiber();
    }

    /**
     * 初始化客户端连接环境
     */
    public Bootstrap initNettyClientEnv(String userName) {
        EventLoopGroup group = new NioEventLoopGroup(ConfigMgr.getInstance().getConfig(userName).robotIOThreadNum,
                new ThreadFactoryBuilder().setNameFormat("robot-io-%d").build());
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(group);
        bootstrap.channel(NioSocketChannel.class);
        bootstrap.option(ChannelOption.SO_REUSEADDR, true);
        // 这是为了客户端主动断开连接后，不进入time_wait而直接释放端口，在反复建立大量连接的场景中需要用到
        bootstrap.option(ChannelOption.SO_LINGER, 0);
        bootstrap.handler(new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) {
                ChannelPipeline p = ch.pipeline();
                // 发消息处理器
                p.addLast(new LengthFieldPrepender(4));
                // 收消息处理器
                p.addLast(new LengthFieldBasedFrameDecoder(Integer.MAX_VALUE, 0, 4, 0, 4));
                p.addLast(new RobotChannelHandler());
            }
        });
        return bootstrap;
    }

    public Bootstrap getBootstrap(User user) {
        return user.getBootstrap();
    }

    public IGeminiDispatcher getRobotMgrDispatcher(User user) {
        return user.getRobotMgrDispatcher();
    }

    public Robot findRobot(User user, int robotId) {
        return user.getRobotMap().get(robotId);
    }

    public <T extends AbstractRobotScene> T getScene(User user, Class<T> clazz) {
        //noinspection unchecked
        return (T) user.getSceneMap().computeIfAbsent(clazz, c -> {
            try {
                return clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                throw new RuntimeException("scene.newInstance failed" + clazz.getName(), e);
            }
        });
    }

    public Collection<Robot> getAllRobot(User user) {
        return user.getRobotMap().values();
    }

    public List<Robot> getOtherAllRobot(User user, int robotId) {
        return user.getRobotMap().values().stream().filter(it -> it.getRobotId() != robotId).collect(Collectors.toList());
    }

    public Map<Integer, Robot> getAllRobotMap(User user) {
        return user.getRobotMap();
    }

    /**
     * 最大同时使用用户数量 不可超过 线程池线程数量
     *
     * @param userName
     * @return
     */
    public boolean isOverLimit(String userName) {
        if (USERS.containsKey(userName)) {
            return false;
        }
        if (USERS.size() >= THREAD_NUM) {
            return true;
        }
        return false;
    }
}


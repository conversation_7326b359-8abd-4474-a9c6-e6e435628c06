package com.yorha.robot.simple;

import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.SimpleMockClient;

public class FursonMockClient extends SimpleMockClient {
    public static void main(String[] args) throws InterruptedException {
        initAll(8889);
        String openId = "cb270491a036480e900c0f02f25437c2";
        long playerId = 1482724410;
        int zoneId = 1;
        login(openId, playerId, zoneId);

        StringBuilder sb = new StringBuilder();
        sb.append("CheckArtifactProbability suitNo=abc loc=efg times=100000");
        for (int suitNo = 1; suitNo <= 14; ++suitNo) {
            int loc = RandomUtils.nextInt(6) + 1;
            String command = sb.toString().replace("abc", String.valueOf(suitNo)).replace("efg", String.valueOf(loc));
            sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand(command).build());
        }
    }
}

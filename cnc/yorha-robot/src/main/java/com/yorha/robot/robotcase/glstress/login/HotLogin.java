package com.yorha.robot.robotcase.glstress.login;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.*;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 热登录
 * 48000注册，跑8000个每次登陆后维持心跳20秒，然后断开再次登录
 */
public class HotLogin extends EnterWorldRobot {

    public HotLogin(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }


    @Override
    public void run() {
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i >= 0) {
            i++;
            Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
            connectServerSync(config.host, config.port);
            runFlow(new GetPlayerListFlow());
            runFlow(new LoginOrCreatePlayerFlow(), isSkipNewbie());
            // 每秒发个心跳包
            for (int j = 0; j < 20; j++) {
                runFlow(new KeepAliveFlow());
                this.realSleep(1000);
            }
            disconnectServerSync();
            this.realSleep(RandomUtils.nextInt(1000, 2000));
        }
        finished();
    }

}

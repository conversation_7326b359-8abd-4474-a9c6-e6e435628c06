package com.yorha.robot.robotcase.stress;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.monster.SearchMonsterFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 压力测试刷怪
 *
 * <AUTHOR>
 */
public class StressSearchMonsterRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(StressSearchMonsterRobot.class);

    public StressSearchMonsterRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i >= 0) {
            int monsterLevel = (i % 5) + 1;
            runFlow(new SearchMonsterFlow(), monsterLevel);
            i--;
        }
        finished();
    }
}

package com.yorha.robot.robotcase;

import com.yorha.common.io.MsgType;
import com.yorha.proto.*;
import com.yorha.robot.core.User;
import com.yorha.robot.flow.scene.UpdateViewFlow;
import com.yorha.robot.flow.user.KeepAliveFlow;

/**
 * 完整的客户端登录，包含拉取各种协议
 */
public class FullLoginRobot extends EnterWorldRobot {

    public FullLoginRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        // Player_ReportMapVersion_C2S
        PlayerScene.Player_ReportMapVersion_C2S.Builder b1 = PlayerScene.Player_ReportMapVersion_C2S.newBuilder();
        b1.setMapId(1004).setVersion("1.0.36");
        sendMsgToServerSync(MsgType.PLAYER_REPORTMAPVERSION_C2S, b1.build());

        // Player_MailUnreadGet_C2S
        PlayerMail.Player_MailUnreadGet_C2S.Builder b2 = PlayerMail.Player_MailUnreadGet_C2S.newBuilder();
        sendMsgToServerSync(MsgType.PLAYER_MAILUNREADGET_C2S, b2.build());

        // Player_GiveNewbieHero_C2S
        PlayerCommon.Player_GiveNewbieHero_C2S.Builder b3 = PlayerCommon.Player_GiveNewbieHero_C2S.newBuilder();
        sendMsgToServerSync(MsgType.PLAYER_GIVENEWBIEHERO_C2S, b3.build());

        // Player_GetChatMessages_C2S
        PlayerChat.Player_GetChatMessages_C2S.Builder b4 = PlayerChat.Player_GetChatMessages_C2S.newBuilder();
        b4.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(CommonEnum.ChatChannel.CC_SERVER).setChatChannelId("1").build())
                .setFromId(0)
                .setToId(0)
                .setIsLogin(true);
        sendMsgToServerSync(MsgType.PLAYER_GETCHATMESSAGES_C2S, b4.build());

        // Player_FetchClanTerritoryMap_C2S
        PlayerClan.Player_FetchClanTerritoryMap_C2S.Builder b7 = PlayerClan.Player_FetchClanTerritoryMap_C2S.newBuilder();
        b7.setVersion(0);
        sendMsgToServerSync(MsgType.PLAYER_FETCHCLANTERRITORYMAP_C2S, b7.build());

        // Player_UpdateView_C2S
        runFlow(new UpdateViewFlow());

        // KeepAlive_C2S_Msg
        runFlow(new KeepAliveFlow());

        end();
    }
}

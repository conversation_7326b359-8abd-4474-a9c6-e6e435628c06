package com.yorha.robot.flow.clan;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 创建或加入联盟
 *
 * <AUTHOR>
 */

public class CreateOrApplyClanFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateOrApplyClanFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        // 联盟编号
        int clanNum = robot.getRobotId() / Constant.CLAN_MEMBER_MAX;
        boolean isClanOwner = (robot.getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;
        ClanScene scene = RobotMgr.getInstance().getScene(robot.getUser(), ClanScene.class);
        // 已经有联盟了
        if (robot.getBoard().playerProp.getClan().getClanId() != 0) {
            if (isClanOwner) {
                scene.putClan(clanNum, robot.getBoard().playerProp.getClan().getClanId(), robot.getBoard().getPlayerProp().getScenePlayer().getMainCityId());
            }
            return;
        }

        String clanNamePrefix = robot.getRobotName();
        if (clanNum >= 10000) {
            throw new GeminiException(ErrorCode.CLAN_SNAME_EXCEEDED_LIMIT);
        }
        if (isClanOwner) {
            String clanSName = String.valueOf(clanNum + 10000);
            clanSName = clanSName.substring(1, 5);
            String clanName = clanNamePrefix + clanNum;
            LOGGER.info("CreateOrApplyClanFlow sName:{} name:{}", clanSName, clanName);
            robot.runFlow(new CreateClanFlow(), clanSName, clanName);
            robot.waitState("waitClanProp", () -> robot.getBoard().playerProp.getClan().getClanId() != 0);
            scene.putClan(clanNum, robot.getBoard().playerProp.getClan().getClanId(), robot.getBoard().getPlayerProp().getScenePlayer().getMainCityId());
        } else {
            robot.waitState("waitJoinClan", () -> scene.getClanId(clanNum) > 0, 10000);
            robot.runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
            robot.waitState("waitJoinClanProp", () -> robot.getBoard().playerProp.getClan().getClanId() != 0);
        }
    }
}

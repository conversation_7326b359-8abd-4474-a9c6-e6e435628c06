package com.yorha.robot.base;

/**
 * <AUTHOR>
 */
public interface Constant {
    /**
     * 联盟成员上限  测试用例分组用
     */
    int CLAN_MEMBER_MAX = 20;
    /**
     * 发送邮件的次数
     */
    int SEND_MAIL_NUM = 1;
    /**
     * 测试全服邮件id
     */
    int TEST_ZONE_MAIL_TEMPLATE_ID = 10003;
    /**
     * 测试联盟邮件id
     */
    int TEST_CLAN_MAIL_TEMPLATE_ID = 10001;

    /**
     * 邮件批量拉取content上限
     */
    int MAIL_CONTENT_MAX = 50;
}

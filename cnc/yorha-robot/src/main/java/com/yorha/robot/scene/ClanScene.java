package com.yorha.robot.scene;

import com.yorha.robot.core.AbstractRobotScene;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 联盟用例共享数据
 *
 * <AUTHOR>
 */

public class ClanScene extends AbstractRobotScene {
    /**
     * 联盟id
     */
    private final Map<Integer, Long> clanMap = new ConcurrentHashMap<>();
    /**
     * 联盟盟主城池id
     */
    private final Map<Integer, Long> clanOwnerCityId = new ConcurrentHashMap<>();

    /**
     * 已经加入军团的机器人个数
     */
    private final AtomicInteger alreadyJoinClanRobots = new AtomicInteger(0);

    /**
     * 所有机器人已发起帮助的个数
     */
    private final AtomicInteger alreadyCreateHelpRobots = new AtomicInteger(0);

    /**
     * 所有机器人已完成帮助的个数
     */
    private final AtomicInteger alreadyFinishHelpRobots = new AtomicInteger(0);

    public void putClan(int clanNum, long clanId, long ownerCityId) {
        clanMap.put(clanNum, clanId);
        clanOwnerCityId.put(clanNum, ownerCityId);
    }

    public long getClanId(int clanNum) {
        return clanMap.getOrDefault(clanNum, 0L);
    }

    public int getClanCount() {
        return clanMap.size();
    }

    public Collection<Long> getAllClanId() {
        return clanMap.values();
    }

    /**
     * 联盟集结id
     */
    private final Map<Integer, Long> clanRallyId = new ConcurrentHashMap<>();

    public void addRallyId(int clanNum, long rallyId) {
        clanRallyId.put(clanNum, rallyId);
    }

    public long getRallyId(int clanNum) {
        return clanRallyId.getOrDefault(clanNum, 0L);
    }

    public int getRallySize() {
        return clanRallyId.size();
    }

    public long getBattleTargetCity(int clanNum) {
        if (clanOwnerCityId.containsKey(clanNum + 1)) {
            return clanOwnerCityId.get(clanNum + 1);
        }
        return clanOwnerCityId.getOrDefault(clanNum - 1, 0L);
    }

    public long getClanOwnerCityId(int clanNum) {
        return clanOwnerCityId.getOrDefault(clanNum, 0L);
    }

    /**
     * 集结对应的owner
     */
    public final Map<Integer, Map<Integer, Long>> rallyMap = new ConcurrentHashMap<>();
    /**
     * 联盟index 集结id
     */
    public final Map<Integer, Long> rallyHashMap = new ConcurrentHashMap<>();

    /**
     * 已经发起帮助的玩家
     */
    public int getAlreadyCreateHelpNumber() {
        return alreadyCreateHelpRobots.get();
    }

    /**
     * 增加已经发起帮助的玩家
     */
    public void addAlreadyCreateHelpPlayer() {
        alreadyCreateHelpRobots.incrementAndGet();
    }

    /**
     * 增加已经发起帮助的玩家
     */
    public void clearAlreadyCreateHelpPlayer(int expect) {
        alreadyCreateHelpRobots.compareAndSet(expect, 0);
    }

    /**
     * 已经完成帮助的玩家
     */
    public int getAlreadyFinishHelpNumber() {
        return alreadyFinishHelpRobots.get();
    }

    /**
     * 增加已经完成帮助的玩家
     */
    public void addAlreadyFinishHelpPlayer() {
        alreadyFinishHelpRobots.incrementAndGet();
    }

    /**
     * 增加已经发起帮助的玩家
     */
    public void clearAlreadyFinishHelpPlayer(int expect) {
        alreadyFinishHelpRobots.compareAndSet(expect, 0);
    }

    /**
     * 增加加入军团的玩家
     */
    public void addAlreadyJoinClanPlayer() {
        alreadyJoinClanRobots.incrementAndGet();
    }

    /**
     * 减少加入军团的玩家
     */
    public void decAlreadyJoinClanPlayer() {
        alreadyJoinClanRobots.decrementAndGet();
    }

    /**
     * 获取加入军团的玩家
     */
    public int getAlreadyJoinClanPlayer() {
        return alreadyJoinClanRobots.get();
    }
}

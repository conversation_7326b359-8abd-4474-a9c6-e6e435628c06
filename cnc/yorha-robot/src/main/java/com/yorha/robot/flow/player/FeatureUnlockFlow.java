package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;


public class FeatureUnlockFlow implements Cnc<PERSON>low {

    @Override
    public void run(Cnc<PERSON><PERSON>ot robot, Object[] args) {

        PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();

        builder.setCommand("FeatureUnlock");
        robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.build());

    }
}

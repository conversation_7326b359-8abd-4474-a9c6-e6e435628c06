package com.yorha.robot.flow.monster;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 清理大地图上所有野怪
 *
 * <AUTHOR>
 */
public class ClearMonsterFlow implements Cnc<PERSON>low {

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();
        String cmd = "ClearMonster playerId=" + robot.getRobotId();
        builder.setCommand(cmd);
        robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.build());
    }
}

package com.yorha.robot.robotcase.stress.player;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 修改指挥官头像用例
 * zeo
 */
public class PlayerChangePlayerNameRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(PlayerChangePlayerNameRobot.class);

    public PlayerChangePlayerNameRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        CncFlowUtil.sendDebugCommand(this, "GiveMePower");
        waitAllRobotLoginSuccess();
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i > 0) {
            i--;
            realSleep(RandomUtils.nextLong(3000));
            sendMsg();
        }
    }

    private void sendMsg() {
        PlayerCommon.Player_ChangePlayerName_C2S.Builder builder = PlayerCommon.Player_ChangePlayerName_C2S.newBuilder();
        builder.setType(CommonEnum.ChangeNameType.CNT_ITEM)
                .setName("xxxx:" + RandomUtils.nextLong(10000));
        sendMsgToServerSync(MsgType.PLAYER_CHANGEPLAYERNAME_C2S, builder.build());
        LOGGER.debug("PlayerId:{} changeNmae", getBoard().getPlayerId());
    }
}

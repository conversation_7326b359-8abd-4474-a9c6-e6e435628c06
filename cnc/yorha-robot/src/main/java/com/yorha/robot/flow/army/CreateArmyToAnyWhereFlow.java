package com.yorha.robot.flow.army;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum.ArmyActionType;
import com.yorha.proto.PlayerScene.Player_CreateArmy_C2S;
import com.yorha.proto.PlayerScene.Player_CreateArmy_S2C;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 创建行军随机去往地图上的点
 *
 * <AUTHOR>
 */
public class CreateArmyToAnyWhereFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateArmyToAnyWhereFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        // 是否极速行军
        boolean fastMove = (boolean) args[0];
        Point point = (Point) args[1];
        // 部队详情
        StructPlayerPB.TroopPB troopInfo = StructPlayerPB.TroopPB.getDefaultInstance();
        if (args.length > 2) {
            troopInfo = (StructPlayerPB.TroopPB) args[2];
        }

        StructPlayerPB.ArmyActionInfoPB.Builder info = StructPlayerPB.ArmyActionInfoPB.newBuilder();
        info.setArmyActionType(ArmyActionType.AAT_Move)
                .setTargetPoint(StructPB.PointPB.newBuilder().setX(point.getX()).setY(point.getY()))
                .setDebugFastMove(fastMove);

        StructPlayerPB.CreateArmy_C2S_ParamPB.Builder pb = StructPlayerPB.CreateArmy_C2S_ParamPB.newBuilder();
        pb.setArmyAction(info);
        pb.setTroopInfo(troopInfo);
        Player_CreateArmy_C2S.Builder builder = Player_CreateArmy_C2S.newBuilder();
        builder.setParam(pb);

        Player_CreateArmy_S2C rspMsg = (Player_CreateArmy_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_CREATEARMY_C2S, builder.build());
        // 通过entityNtf下发来addArmy
        LOGGER.debug("{} create army success. armyId :{}", robot, rspMsg.getArmyId());
    }

}

package com.yorha.robot.robotcase.stress.chat;

import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.StringCacheMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.chat.ChatFlow;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */

public class StressClanChatRobot extends EnterWorldRobot {
    public StressClanChatRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=5 count=9999999");

        runFlow(new CreateOrApplyClanFlow());
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            stressFixSendChat();
        }
        finished();
    }

    private void stressFixSendChat() {
        String randomChatMsg = StringCacheMgr.getMaxLenChatMsg();
        runFlow(new ChatFlow(), CommonEnum.ChatChannel.CC_CLAN, randomChatMsg);
    }
}

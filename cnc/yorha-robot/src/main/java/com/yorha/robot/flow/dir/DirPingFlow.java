package com.yorha.robot.flow.dir;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CsAccount;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

public class Dir<PERSON>ing<PERSON><PERSON> implements Cnc<PERSON>low {
    @Override
    public void run(CncRobot robot, Object[] args) {
        CsAccount.DirPing_C2S_Msg msg = CsAccount.DirPing_C2S_Msg.getDefaultInstance();
        GeneratedMessageV3 ret = robot.sendMsgToServerSync(MsgType.DIRPING_C2S_MSG, msg);
    }
}

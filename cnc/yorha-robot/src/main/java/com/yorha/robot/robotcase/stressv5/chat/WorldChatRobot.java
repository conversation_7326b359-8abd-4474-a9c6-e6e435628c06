package com.yorha.robot.robotcase.stressv5.chat;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerChat;
import com.yorha.robot.bean.ChatArgs;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.StringCacheMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.chat.ChatFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
@RobotCase(
        name = "全服聊天",
        desc = "机器人批量登陆后发送全服聊天",
        args = ChatArgs.class
)
public class WorldChatRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(WorldChatRobot.class);

    public WorldChatRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());

        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "SetChatNoCd isNoCd=1");
            runFlow(new DebugCmdFlow(), "SetTextNoFilter textFilter=false");
        }
        waitAllRobotLoginSuccess();

        // 控制每秒大概200个
        int interval = config.robotNum / 200;
        // 错开
        realSleep(5000 + RandomUtils.nextInt(interval) * 1000L);

        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            int random = RandomUtils.nextInt(20);
            if (random == 0) {
                // 1/20概率触发下线再上线拉取
                disconnectServerSync();
                realSleep(3_000);
                connectZone1();
                runFlow(new GetPlayerListFlow());
                runFlow(new LoginFlow());

                // 拉取全服聊天最新1页，大概20条
                PlayerChat.Player_GetChatMessages_C2S.Builder b3 = PlayerChat.Player_GetChatMessages_C2S.newBuilder();
                b3.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(CommonEnum.ChatChannel.CC_SERVER).setChatChannelId("1").build())
                        .setFromId(0)
                        .setToId(0)
                        .setIsLogin(true);
                sendMsgToServerSync(MsgType.PLAYER_GETCHATMESSAGES_C2S, b3.build());
                LOGGER.debug("WorldChatPress, execute getChatMessage, playerId={}", getBoard().getPlayerId());
            } else {

                // 19/20概率在线发送一些胡言乱语
                stressFixMaxSendChat();
                LOGGER.debug("WorldChatPress, execute sendMessage, playerId={}", getBoard().getPlayerId());
                realSleep(10_000 + RandomUtils.nextInt(1000));
            }

        }
        disconnectAndEnd();
    }

    private void stressFixMaxSendChat() {
        String randomChatMsg = getUser().getArg(ChatArgs.class).content;
        if (StringUtils.isEmpty(randomChatMsg)) {
            randomChatMsg = StringCacheMgr.getMaxLenChatMsgWithSomething();
        }

        runFlow(new ChatFlow(), CommonEnum.ChatChannel.CC_SERVER, 1, randomChatMsg);
    }
}


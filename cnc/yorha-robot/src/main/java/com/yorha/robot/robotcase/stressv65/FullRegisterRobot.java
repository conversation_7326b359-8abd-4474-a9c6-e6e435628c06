package com.yorha.robot.robotcase.stressv65;

import com.yorha.robot.core.User;
import com.yorha.robot.flow.dir.DirRegisterFlow;
import com.yorha.robot.flow.user.CreatePlayerFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class FullRegisterRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(FullRegisterRobot.class);

    public FullRegisterRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void firstStart() {
        try {

            // 连接dir
            connectDir();
            runFlow(new DirRegisterFlow());
            disconnectServerSync();

            // 连接zone
            connectZone1();
            runFlow(new GetPlayerListFlow());
            runFlow(new CreatePlayerFlow(), isSkipNewbie());
            disconnectServerSync();

            // 连接dir
            connectDir();
            runFlow(new DirRegisterFlow());
            disconnectServerSync();

            // 连接zone
            connectZone1();
            runFlow(new GetPlayerListFlow());
            runFlow(new LoginFlow(), isSkipNewbie());
            disconnectServerSync();

            // 完成
        } catch (Exception e) {
            LOGGER.error("LoginRobot firstStart fail, ", e);
        } finally {
            disconnectAndEnd();
        }

    }
}

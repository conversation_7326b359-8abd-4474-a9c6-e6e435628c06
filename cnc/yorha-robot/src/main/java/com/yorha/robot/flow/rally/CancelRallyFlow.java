package com.yorha.robot.flow.rally;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerRally;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class CancelRallyFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CancelRallyFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerRally.Player_CancelRally_C2S.Builder builder = PlayerRally.Player_CancelRally_C2S.newBuilder();
        builder.setRallyId((long) args[0]);
        robot.sendMsgToServerSync(MsgType.PLAYER_CANCELRALLY_C2S, builder.build());
        LOGGER.debug("{} cancel rally first rally. rallyId :{}", robot, args[0]);
    }
}

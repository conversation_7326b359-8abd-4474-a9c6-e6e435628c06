package com.yorha.robot.robotcase;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.Pair;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import com.yorha.proto.Entity.EntityNtfMsg;
import com.yorha.proto.Entity.SceneObjBriefAttr;
import com.yorha.proto.Entity.SceneObjBriefNtfMsg;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.Board;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.scene.UpdateViewFlow;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RobotCase(
        name = "进入大世界",
        desc = "登录进入大世界"
)
public class EnterWorldRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(EnterWorldRobot.class);

    public EnterWorldRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
        s2cOnCall.put(MsgType.ENTITYNTFMSG, this::onEntitySync);
        s2cOnCall.put(MsgType.SCENEOBJBRIEFNTFMSG, this::onBriefSync);
        s2cOnCall.put(MsgType.PLAYER_CHANGEMAP_NTF, this::onMapChanged);
        s2cOnCall.put(MsgType.PLAYER_CHANGEMAPDONE_NTF, this::onMapChangedDone);
    }

    @Override
    public void firstStart() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        connectServerSync(config.host, config.port);
        run();
    }

    @Override
    public void run() {
        super.run();
        LOGGER.debug("{} afterEnterBigScene", this);
        LoginScene loginScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        loginScene.markRobotLogin(getRobotId(), getBoard().getPlayerId());
//        if (getBoard().getCityProp().getIsAscend()) {
//            runFlow(new CityFallFlow());
//        }
        try {
            afterEnterBigScene();
        } finally {
            end();
        }
    }

    protected void afterEnterBigScene() {
        runFlow(new UpdateViewFlow());
        end();
    }

    /**
     * 等待所有玩家登录成功
     */
    protected void waitAllRobotLoginSuccess() {
        LoginScene loginScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        waitState("waitAllRobotLogin", () -> loginScene.allRobotLogin(getUser().getUserName()), 6000_000);
        if (getRobotId() == 0) {
            LOGGER.info("all robot login");
        }
    }

    public void onEntitySync(GeneratedMessageV3 originMsg) {
        EntityNtfMsg msg = (EntityNtfMsg) originMsg;
        onEntityNew(msg.getNewEntitiesList());
        onEntityMod(msg.getModEntitiesList());
        onEntityDel(msg.getDelEntitiesList());
    }

    public void onBriefSync(GeneratedMessageV3 originMsg) {
        SceneObjBriefNtfMsg msg = (SceneObjBriefNtfMsg) originMsg;
        for (SceneObjBriefAttr attr : msg.getNewEntitiesList()) {
            switch (attr.getEntityType()) {
                case ET_DropObject:
                    LOGGER.debug("onBriefSync new drop: {}", attr.getDropObjAttr());
                    break;
                case ET_MapBuilding:
                    LOGGER.debug("onBriefSync new mapBuilding: {}", attr.getMapBuildingAttr());
                    break;
                case ET_Monster:
                    LOGGER.debug("onBriefSync new monster: {}", attr.getMonsterAttr());
                    break;
                default:
                    break;
            }
        }
        LOGGER.debug("onBriefSync del: {}", msg.getDelEntitiesList());
    }

    public void onMapChanged(GeneratedMessageV3 originMsg) {
        PlayerScene.Player_ChangeMap_NTF msg = (PlayerScene.Player_ChangeMap_NTF) originMsg;
        long curMap = msg.getMapId();
        long curScene = msg.getSceneId();
        Board board = getBoard();
        board.setSceneId(curScene);
        board.setMapId(curMap);
        getBoard().getMyArmies().clear();
        LOGGER.debug("{} onMapChanged {} {}", this, curMap, curScene);
    }

    public void onMapChangedDone(GeneratedMessageV3 originMsg) {
        PlayerScene.Player_ChangeMapDone_NTF msg = (PlayerScene.Player_ChangeMapDone_NTF) originMsg;
        LOGGER.debug("{} onMapChangedDone {} {}", this, msg.getMapId(), msg.getSceneId());
    }

    public void onEntityNew(List<EntityAttrOuterClass.EntityAttr> entityList) {
        Board board = getBoard();
        for (EntityAttrOuterClass.EntityAttr entity : entityList) {
            switch (entity.getEntityType()) {
                case ET_Player:
                    board.playerProp = new PlayerProp();
                    board.playerProp.mergeFromCs(entity.getPlayerAttr());
                    board.setPlayerId(entity.getPlayerAttr().getId());
                    break;
                case ET_City:
                    if (entity.getEntityId() == board.playerProp.getScenePlayer().getMainCityId()) {
                        board.cityProp = new CityProp();
                        board.cityProp.mergeFromCs(entity.getCityAttr());
                    }
                    break;
                case ET_Army:
                    if (entity.getArmyAttr().getOwnerId() == board.getPlayerId()) {
                        ArmyProp armyProp = new ArmyProp();
                        armyProp.mergeFromCs(entity.getArmyAttr());
                        board.addMyArmy(entity.getEntityId(), armyProp);
                        LOGGER.debug("my army: {}", board.getMyArmyIds());
                    }
                    break;
                case ET_Clan:
                    if (entity.getClanAttr().getId() == board.getPlayerProp().getClan().getClanId()) {
                        ClanProp clanProp = new ClanProp();
                        clanProp.mergeFromCs(entity.getClanAttr());
                        board.setClanProp(clanProp);
                        LOGGER.debug("clan: {}  {}", entity.getEntityId(), entity.getClanAttr());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private void onEntityMod(List<EntityAttrOuterClass.EntityAttr> entityList) {
        Board board = getBoard();
        for (EntityAttrOuterClass.EntityAttr entity : entityList) {
            switch (entity.getEntityType()) {
                case ET_Player:
                    board.playerProp.mergeChangeFromCs(entity.getPlayerAttr());
                    break;
                case ET_City:
                    if (board.playerProp != null && entity.getEntityId() == board.playerProp.getScenePlayer().getMainCityId()) {
                        board.cityProp.mergeChangeFromCs(entity.getCityAttr());
                    }
                    break;
                case ET_Army:
                    ArmyProp myArmy = board.findMyArmy(entity.getEntityId());
                    if (myArmy != null) {
                        myArmy.mergeChangeFromCs(entity.getArmyAttr());
                    }
                    break;
                default:
                    return;
            }
        }
    }

    protected void onEntityDel(List<Long> entityIdList) {
        Board board = getBoard();
        for (long entityId : entityIdList) {
            board.tryDeleteMyArmy(entityId);
        }
    }

    public StructPlayerPB.TroopPB buildArmyTroop(CncRobot me, boolean addDeputy, int soldierNum) {
        StructPlayerPB.TroopPB.Builder builder = StructPlayerPB.TroopPB.newBuilder();
        PlayerProp playerProp = me.getBoard().getPlayerProp();

        // 英雄设置
        Collection<PlayerHeroProp> myHeroes = playerProp.getPlayerHeroModel().getPlayerHeroMap().values();
        List<PlayerHeroProp> idleHeroes = myHeroes.stream()
                .filter(p -> p.getState() == CommonEnum.HeroState.HERO_IDLE)
                .filter(p -> !isHeroAlreadyInArmy(p.getHeroId()))
                .collect(Collectors.toList());
        Collections.shuffle(idleHeroes);
        if (!idleHeroes.isEmpty()) {
            builder.setMainHero(StructPB.HeroPB.newBuilder().setHeroId(idleHeroes.get(0).getHeroId()).build());
        }
        if (addDeputy && idleHeroes.size() > 1) {
            builder.setDeputyHero(StructPB.HeroPB.newBuilder().setHeroId(idleHeroes.get(1).getHeroId()).build());
        }
        // 兵力设置
        playerProp.getScenePlayer().getSoldier().getSoldierMap().forEach((soldierId, mySoldier) -> {
            StructPB.SoldierPB soldierPb = StructPB.SoldierPB.newBuilder()
                    .setSoldierId(soldierId)
                    .setNum(Math.min(soldierNum, mySoldier.getNum()))
                    .build();
            builder.getTroopBuilder().putDatas(soldierId, soldierPb);
        });
        return builder.build();
    }

    public StructPlayerPB.TroopPB buildArmyTroop(CncRobot me) {
        return buildArmyTroop(me, true, 100000);
    }

    private boolean isHeroAlreadyInArmy(int heroId) {
        for (Pair<Long, ArmyProp> pair : getBoard().getMyArmies()) {
            TroopProp troop = pair.getSecond().getTroop();
            if (troop.getMainHero().getHeroId() == heroId) {
                return true;
            }
            if (troop.getDeputyHero().getHeroId() == heroId) {
                return true;
            }
        }
        return false;
    }
}

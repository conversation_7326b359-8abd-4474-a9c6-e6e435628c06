package com.yorha.robot.robotcase;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.user.KeepAliveFlow;

/**
 * <AUTHOR>
 */
public class OffsetRobot extends EnterWorldRobot {

    public OffsetRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        realSleep(5000);

        if (getRobotId() == 0) {

            // 大哥，负责调整时间
            int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
            while (i-- > 0) {
                // 6个小时
                runFlow(new DebugCmdFlow(), "Offset seconds=21600");
                this.realSleep(5000);
            }

        } else {

            // 小弟们，发心跳保持在线
            // KeepAlive_C2S_Msg
            int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
            while (i-- > 0) {
                runFlow(new KeepAliveFlow());
                this.realSleep(5000);
            }
        }

        finished();
    }
}

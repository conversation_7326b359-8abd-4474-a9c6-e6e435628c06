package com.yorha.robot.robotcase.stress.player;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.player.SearchResBuildingFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 搜索资源田
 *
 * <AUTHOR>
 */
public class SearchResBuildingRobot extends EnterWorldRobot {
    public SearchResBuildingRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }
        waitAllRobotLoginSuccess();

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i > 0) {
            int type = RandomUtils.nextInt(4) + 1;
            int level = RandomUtils.nextInt(6) + 1;
            runFlow(new SearchResBuildingFlow(), type, level);
            i--;
        }
        finished();
    }
}

package com.yorha.robot.flow.army;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum.ArmyActionType;
import com.yorha.proto.PlayerScene.Player_ChangeArmyAction_C2S;
import com.yorha.proto.StructPlayerPB.ArmyActionInfoPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class FastReturnAllMyArmyFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        boolean isFast = true;
        if (args.length >= 1) {
            isFast = (boolean) args[0];
        }
        Set<Long> myArmy = robot.getBoard().getMyArmyIds();
        for (long armyId : myArmy) {
            ArmyActionInfoPB.Builder actionBuilder = ArmyActionInfoPB.newBuilder();
            actionBuilder.setArmyActionType(ArmyActionType.AAT_Return).setDebugFastMove(isFast);
            Player_ChangeArmyAction_C2S.Builder builder = Player_ChangeArmyAction_C2S.newBuilder();
            builder.setArmyAction(actionBuilder).setArmyId(armyId);
            robot.sendMsgToServerSync(MsgType.PLAYER_CHANGEARMYACTION_C2S, builder.build());
        }
    }

}

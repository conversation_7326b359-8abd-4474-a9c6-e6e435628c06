package com.yorha.robot.core.base;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.proto.Core.Code;

/**
 * <AUTHOR>
 * @date 2021/07/28 10:52
 */
public class Message {
    private final int msgType;
    private final int seqId;
    private final Code code;
    private final GeneratedMessageV3 realMsg;

    public Message(int msgType, int seqId, Code code, GeneratedMessageV3 realMsg) {
        this.msgType = msgType;
        this.seqId = seqId;
        this.code = code;
        this.realMsg = realMsg;
    }

    public int getSeqId() {
        return seqId;
    }

    @SuppressWarnings("unchecked")
    public <T extends GeneratedMessageV3> T getRealMsg() {
        return (T) realMsg;
    }

    public int getMsgType() {
        return msgType;
    }

    public Code getCode() {
        return code;
    }

    @Override
    public String toString() {
        return "Message{" +
                "msgType=" + msgType +
                "seqId=" + seqId +
                '}';
    }
}


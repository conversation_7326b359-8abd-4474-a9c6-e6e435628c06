package com.yorha.robot.robotcase.stress.player;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.flow.player.FetchClanMemberCityFlow;
import com.yorha.robot.flow.player.FetchClanTerritoryMapFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;

/**
 * 模拟客户端拉取城池位置、势力图
 *
 * <AUTHOR>
 */
public class FetchCityAndMapRobot extends EnterWorldRobot {
    public FetchCityAndMapRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        // 盟主去建盟 剩下的人等待
        runFlow(new CreateOrApplyClanFlow());
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        scene.addAlreadyJoinClanPlayer();
        waitState("waitAllPlayerJoinClan", () -> scene.getAlreadyJoinClanPlayer() >= RobotMgr.getInstance().getAllRobot(getUser()).size());

        int runTimes = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < runTimes; ++i) {
            runFlow(new FetchClanMemberCityFlow());
            runFlow(new FetchClanTerritoryMapFlow());
            realSleep(5000);
        }

        finished();
    }
}

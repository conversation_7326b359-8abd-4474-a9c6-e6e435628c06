package com.yorha.robot.flow.battle;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerRally;
import com.yorha.proto.PlayerRally.Player_QueryRallyList_S2C;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.scene.ClanScene;

import static com.yorha.robot.robotcase.stress.battle.ArmyBattleRobot.buildBattleArmyTroop;


public class CreateRallyFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        CncRobot rallyTarget = getRallyTarget(robot);
        if (rallyTarget == null) {
            return;
        }
        long targetCityId = rallyTarget.getBoard().getMainCityId();

        StructPlayerPB.ArmyActionInfoPB.Builder info = StructPlayerPB.ArmyActionInfoPB.newBuilder();
        info.setArmyActionType(CommonEnum.ArmyActionType.AAT_CreateRally)
                .setTargetId(targetCityId).setWaitSecondsTime(600).setDebugFastMove(true);
        PlayerScene.Player_CreateArmy_C2S.Builder builder = PlayerScene.Player_CreateArmy_C2S.newBuilder();
        builder.getParamBuilder().setArmyAction(info).setTroopInfo(buildBattleArmyTroop(robot, 1000, null));

        PlayerRally.Player_QueryRallyList_C2S.Builder builder2 = PlayerRally.Player_QueryRallyList_C2S.newBuilder();
        Player_QueryRallyList_S2C rspMsg2 = (Player_QueryRallyList_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_QUERYRALLYLIST_C2S, builder2.build());
        for (StructPlayerPB.RallyInfoPB rallyInfoPB : rspMsg2.getRallyList().getDatasList()) {
            if (rallyInfoPB.getOrganizerId() == robot.getBoard().getPlayerId()) {
                ClanScene scene = RobotMgr.getInstance().getScene(robot.getUser(), ClanScene.class);
                int clanIndex = robot.getRobotId() / Constant.CLAN_MEMBER_MAX;
                scene.rallyHashMap.put(clanIndex, rallyInfoPB.getRallyId());
                break;
            }
        }
    }

    private CncRobot getRallyTarget(CncRobot robot) {
        int clanNum = robot.getRobotId() / Constant.CLAN_MEMBER_MAX;
        ClanScene scene = RobotMgr.getInstance().getScene(robot.getUser(), ClanScene.class);
        int nextClanNum = clanNum + 1;
        long clanId = scene.getClanId(nextClanNum);
        if (clanId == 0) {
            // 不存在联盟, 找第一个联盟
            nextClanNum = 0;
        }
        // 找下一个联盟中和我对位的目标
        int targetRobotId = robot.getRobotId() + (nextClanNum - clanNum) * Constant.CLAN_MEMBER_MAX;
        return (CncRobot) RobotMgr.getInstance().findRobot(robot.getUser(), targetRobotId);
    }
}

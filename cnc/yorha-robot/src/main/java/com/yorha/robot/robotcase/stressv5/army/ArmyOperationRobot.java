package com.yorha.robot.robotcase.stressv5.army;

import com.yorha.common.ratelimiter.GeminiThreadRateLimiter;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.flow.city.CityFallFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.robotcase.stressv5.Helper;

/**
 * <AUTHOR>
 */
public class ArmyOperationRobot extends EnterWorldRobot {
    public ArmyOperationRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    // 控制每秒大概300个
    private static final GeminiThreadRateLimiter LIMITER = new GeminiThreadRateLimiter(300);

    @Override
    protected void afterEnterBigScene() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());

        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePowerSimple");
        }
        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "SetIgnoreCrossingOwner isIgnore=1");
        }

        if (getBoard().getCityProp().getAscendReason() != CommonEnum.CityAscendReason.CAR_NONE) {
            runFlow(new CityFallFlow());
        }

        runFlow(new FastReturnAllMyArmyFlow());
        if (!getBoard().getMyArmies().isEmpty()) {
            waitState("army return", () -> getBoard().getMyArmies().size() == 0, 10_000);
        }

        waitAllRobotLoginSuccess();

        PointProp pointProp = getBoard().cityProp.getPoint();
        int regionId = Helper.getRegionId(pointProp);

        int executeCount = config.executeRound;
        for (int i = 0; i < executeCount; i++) {
            while (!LIMITER.tryAcquire()) {
                realSleep(300);
            }
            Point regionPoint = Helper.getRegionPoint(regionId, i);
            // 出发
            runFlow(new CreateArmyToAnyWhereFlow(), false, regionPoint, buildArmyTroop(this, false, 1000));
            waitState("army created", () -> getBoard().getMyArmies().size() > 0, 10_000);
            realSleep(3000);
            // 回家
            runFlow(new FastReturnAllMyArmyFlow());
            waitState("army return", () -> getBoard().getMyArmies().size() == 0, 10_000);
        }
        disconnectAndEnd();
    }


}

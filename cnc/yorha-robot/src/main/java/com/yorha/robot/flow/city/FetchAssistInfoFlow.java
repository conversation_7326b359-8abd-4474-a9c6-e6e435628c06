package com.yorha.robot.flow.city;

import com.yorha.proto.PlayerRally;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class FetchAssistInfoFlow implements CncFlow {
    @Override
    public void run(CncR<PERSON>ot robot, Object[] args) {
        PlayerRally.Player_FetchCityInnerArmy_C2S.Builder builder = PlayerRally.Player_FetchCityInnerArmy_C2S.newBuilder();

        long targetId = (long) args[0];
        builder.setTargetId(targetId);
    }
}

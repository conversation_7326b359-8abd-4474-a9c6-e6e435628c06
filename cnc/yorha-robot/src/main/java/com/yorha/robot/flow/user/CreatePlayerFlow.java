package com.yorha.robot.flow.user;

import com.yorha.common.io.MsgType;
import com.yorha.proto.User;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 创建新角色
 */
public class CreatePlayerFlow implements CncFlow {

    @Override
    public void run(Cnc<PERSON><PERSON>ot robot, Object[] args) {
        int zoneId = robot.getBoard().getZoneId();
        User.Login_C2S_Msg.Builder builder = User.Login_C2S_Msg.newBuilder();

        long playerId = robot.getBoard().getPlayerId();
        if (playerId > 0) {
            builder.setAccountToken(robot.getAccountToken())
                    .setPlayerId(playerId)
                    .setDebugStartNewbie(false)
                    .setZoneId(1);
        } else {
            builder.setAccountToken(robot.getAccountToken())
                    .setPlayerId(0)
                    .setDebugStartNewbie((<PERSON><PERSON><PERSON>) args[0])
                    .setIsRegister(true)
                    .setZoneId(zoneId);
        }
        robot.sendMsgToServerSync(MsgType.LOGIN_C2S_MSG, builder.build());
    }
}
package com.yorha.robot.flow.chat;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerChat;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;


/**
 * <AUTHOR>
 */

public class Chat<PERSON>low implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        CommonEnum.ChatChannel chatChannel = (CommonEnum.ChatChannel) args[0];
        PlayerChat.Player_ChatRequest_C2S.Builder builder = PlayerChat.Player_ChatRequest_C2S.newBuilder();
        if (chatChannel == CommonEnum.ChatChannel.CC_SERVER) {
            // 1服
            builder.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(chatChannel).setChatChannelId("1").build());
        } else {
            builder.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(chatChannel).
                    setChatChannelId(String.valueOf(args[1])).build());
        }
        builder.setMessageData(CommonMsg.MessageData.newBuilder().setMsg((String) args[2]));
        robot.sendMsgToServerSync(MsgType.PLAYER_CHATREQUEST_C2S, builder.build());
    }
}

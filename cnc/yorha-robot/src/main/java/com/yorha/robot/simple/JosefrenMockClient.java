package com.yorha.robot.simple;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.robot.SimpleMockClient;


public class JosefrenMockClient extends SimpleMockClient {

    public static void main(String[] args) throws InterruptedException {
        initAll(10042);
        String openId = "sdf";
        long playerId = 0;
        int zoneId = 1;
        login(openId, playerId, zoneId);
//        sendAndWait(PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.newBuilder().setHeroId(1517).build());
        sendAndWait(PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.newBuilder().setHeroId(1508).build());
    }

    private static void testReload() {
        String openId = "sdf";
        long playerId = 0;
        int zoneId = 1;
        login(openId, playerId, zoneId);
        sendGm("ReloadAll");
    }

    private static void checkRegisterNewCharacter(final int zoneId) {
        sendAndWait(PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.newBuilder().setZoneId(zoneId).build());
    }

    private static void getRoleList() {
        sendAndWait(PlayerCharacterManager.Player_GetCharacterList_C2S.newBuilder().build());
    }

    private static void starPlayer(final long playerId) {
        sendAndWait(PlayerCharacterManager.Player_ManageStarForCharacter_C2S.newBuilder().setPlayerId(playerId).setSetStar(true).build());
    }

    private static void clearClanGifts() {
        sendAndWait(PlayerClan.Player_ClearClanGift_C2S.newBuilder().build());
    }

    private static void takeAllClanGift(CommonEnum.ClanGiftRarityType rarityType) {
//        sendAndWait(PlayerClan.Player_GetClanGift_C2S.newBuilder().setIsGetAll(true).setRarity(rarityType).build());
    }

    private static void takeClanGift(long id) {
        sendAndWait(PlayerClan.Player_GetClanGift_C2S.newBuilder().setGiftUniqueId(id).build());
    }

    private static void takeTreasureGift(long id) {
        sendAndWait(PlayerClan.Player_GetClanTreasureGift_C2S.newBuilder().setTreasureId(id).build());
    }

    private static void addClanGift() {
        sendGm("DebugClanGift command=addGift param1=1001");
        sendGm("DebugClanGift command=addGift param1=1002");
    }

    private static void addClanGiftKey(int keys) {
        sendGm(StringUtils.format("DebugClanGift command=addKey  param1={}", keys));
    }

    private static void addClanGiftPoints(int points) {
        sendGm(StringUtils.format("DebugClanGift command=addPoint   param1={}", points));
    }
//    private static void testDailyDiscount() {
//        testGetFreeRewards();
//        testBuyDailyGoods();
//    }
//
//    private static void testBuyDailyGoods() {
//        //合集：    10025
//        //各档位：   10022,10023,10024
//        //
//        int collectionId = 10025;
//        List<Integer> allLevelIds = Lists.newArrayList(10022, 10023, 10024);
//        testBuyGoods(10022);
//        testBuyGoods(collectionId);
//    }
//
//    private static void testGetFreeRewards() {
//        sendAndWait(PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.newBuilder().build());
//    }
//
//    private static void testBuySameGoods(int goodsId) {
//        testBuyGoods(goodsId);
//        testBuyGoods(goodsId);
//    }
//
//    private static void testBuyGoods(int goodsId) {
//        sendAndWait(PlayerPayment.Player_ApplyGoodsOrder_C2S.newBuilder().setGoodsId(goodsId).build());
//    }
//
//    private static void testVipStore() {
//        // 测试购买
//        sendAndWait(PlayerVipStore.Player_GetVipStoreInfo_C2S.newBuilder().build());
//        sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("AddCurrency currencyType=5 count=88888888").build());
//        sendAndWait(PlayerVipStore.Player_BuyVipStoreItem_C2S.newBuilder().setBuyNum(1).setConfigGoodsId(1001).build());
//        System.out.println("testVipStore done");
//        sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("ClearCurrency").build());
//    }
//
//    private static void timePass(Integer seconds) {
//        sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("Offset seconds=" + seconds.toString()).build());
//    }
//
//    private static void testCitySkin() {
//        // 永久皮肤道具 & 1天皮肤道具 各10个
//        //sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("AddItem templateId=11304001 count=10").build());
//        //sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("AddItem templateId=11304002 count=10").build());
//        PlayerPB.ItemUseParamsPB.Builder params = PlayerPB.ItemUseParamsPB.newBuilder().setUseDirectly(true);
//        //sendAndWait(PlayerCommon.Player_UseItem_C2S.newBuilder().setItemKey(309408840).setNum(1).setParams(params).build());
//
//        sendAndWait(PlayerInnerBuild.Player_ChangeCityDress_C2S.newBuilder().setDressTemplateId(101001).build());
//    }

    /**
     * ex: AddCurrency currencyType=5 count=88888888
     *
     * @param cmd gm指令
     */
    private static void sendGm(final String cmd) {
        sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand(cmd).build());
    }
}

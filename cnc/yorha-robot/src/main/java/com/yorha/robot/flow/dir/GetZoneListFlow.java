package com.yorha.robot.flow.dir;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CsAccount;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

public class GetZoneListFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        CommonMsg.YoAccountToken yoAccountToken = robot.getAccountToken();

        int hardwareLevel = robot.getBoard().getHardwareLevel();
        final String channelName = robot.getBoard().getChannel();
        CsAccount.DirGetZone_C2S_Msg dirGetZone = CsAccount.DirGetZone_C2S_Msg.newBuilder()
                .setAccountToken(yoAccountToken)
                .setHardwareLevel(hardwareLevel)
                .setChannelName(channelName)
                .build();
        CsAccount.DirGetZone_S2C_Msg result = (CsAccount.DirGetZone_S2C_Msg) robot.sendMsgToServerSync(MsgType.DIRGETZONE_C2S_MSG, dirGetZone);

        // 记录dir分配的zone：ip、port、zoneId
        robot.getBoard().setIp(result.getTargetZoneServer().getIp());
        robot.getBoard().setPort(result.getTargetZoneServer().getPort());
        robot.getBoard().setZoneId(result.getTargetZoneServer().getZoneId());
        robot.getBoard().setDriveTrafficReason(result.getRegisterDriveTrafficReason());

//        {
//            User.GetRoleList_C2S_Msg.Builder builder = User.GetRoleList_C2S_Msg.newBuilder();
//            builder.setAccountToken(robot.getAccountToken()))
//                    .getClientInfoBuilder()
//                    .setLanguage(CommonEnum.Language.en)
//                    .setVersion(ConfigMgr.getInstance().getConfig(robot.getUser().getUserName()).clientVersion);
//            builder.setZoneId(1);
//
//            User.GetRoleList_S2C_Msg rsp = (User.GetRoleList_S2C_Msg) robot.sendMsgToServerSync(MsgType.GETROLELIST_C2S_MSG, builder.build());
//            List<CommonMsg.SimplePlayerInfo> playerInfoListList = rsp.getPlayerInfoListList();
//            if (!ConfigMgr.getInstance().getConfig(robot.getUser().getUserName()).loginExistedPlayer) {
//                // 外面的配置可以控制是否强制注册新角色，注册就不选中某一个登录角色了
//                return;
//            }
//
//            for (CommonMsg.SimplePlayerInfo playerInfo : playerInfoListList) {
//                // 账号下有角色, 登录已有的角色
//                robot.getBoard().setPlayerId(playerInfo.getPlayerId());
//                break;
//            }
//        }
//
////        if (result.hasTargetZoneServer()) {
//        User.Login_C2S_Msg.Builder builder = User.Login_C2S_Msg.newBuilder();
//        builder.setAccountToken(robot.getAccountToken()))
//                .setPlayerId(0)
//                .setDebugStartNewbie(true)
//                .setZoneId(1);
//        GeneratedMessageV3 ans = robot.sendMsgToServerSync(MsgType.LOGIN_C2S_MSG, builder.build());
//        }

    }
}

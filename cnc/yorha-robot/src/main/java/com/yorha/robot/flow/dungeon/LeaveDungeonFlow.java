package com.yorha.robot.flow.dungeon;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerScene;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class LeaveDungeonFlow implements CncFlow {
    @Override
    public void run(Cnc<PERSON><PERSON><PERSON> robot, Object[] args) {
        robot.sendMsgToServerSync(MsgType.PLAYER_LEAVEDUNGEON_C2S, PlayerScene.Player_LeaveDungeon_C2S.getDefaultInstance());
       
    }
}

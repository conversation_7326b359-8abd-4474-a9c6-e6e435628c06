package com.yorha.robot.robotcase.glstress.view;

import com.google.common.collect.Maps;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.clan.CreateOrWaitClanFlow;
import com.yorha.robot.flow.player.FetchClanMemberCityFlow;
import com.yorha.robot.flow.player.FetchClanTerritoryMapFlow;
import com.yorha.robot.flow.scene.UpdateNormalViewFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;

import java.util.HashMap;

/**
 * 视野
 */
public class RandomSceneView extends EnterWorldRobot {
    public RandomSceneView(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {

        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "FinMileStone count=26");
            runFlow(new DebugCmdFlow(), "Offset seconds=3600");
        }
        runFlow(new DebugCmdFlow(), "GiveMePower");
        waitAllRobotLoginSuccess();

        // 盟主去建盟 剩下的人等待
        runFlow(new CreateOrWaitClanFlow());

        // 等所有的盟建好
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 联盟总个数
        int totalClanCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - 1) / Constant.CLAN_MEMBER_MAX + 1;
        waitState("waitAllClanReady", () -> scene.getClanCount() >= totalClanCount);

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        HashMap<Integer, Pair<Integer, Integer>> pointMap = Maps.newHashMap();
        pointMap.put(1, Pair.of(37000, 17000));
        pointMap.put(2, Pair.of(57000, 26000));
        pointMap.put(3, Pair.of(57000, 26000));
        pointMap.put(4, Pair.of(180000, 96500));
        pointMap.put(5, Pair.of(213000, 96000));
        pointMap.put(6, Pair.of(213000, 150000));
        pointMap.put(7, Pair.of(513000, 960000));

        while (i >= 0) {
            i++;
            int randomIndex = RandomUtils.nextInt(1, 8);
            Pair<Integer, Integer> pointPair = pointMap.get(randomIndex);
            runFlow(new UpdateNormalViewFlow(), i, randomIndex, pointPair.getFirst(), pointPair.getSecond());
            runFlow(new FetchClanMemberCityFlow());
            runFlow(new FetchClanTerritoryMapFlow());
            realSleep(RandomUtils.nextInt(1000, 2000));
        }

    }
}

package com.yorha.robot.robotcase.stress.player;

import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.ItemProp;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.StructPB;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.robotcase.EnterWorldRobot;

import java.util.Map;

/**
 * 使用道具解锁迷雾用例
 *
 * <AUTHOR>
 */
public class PlayerFogRobot extends EnterWorldRobot {
    private int indexX = 1;
    private int indexY = 1;

    public PlayerFogRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    private void sendMsg(long itemUniqId) {
        PlayerCommon.Player_UseItem_C2S.Builder builder = PlayerCommon.Player_UseItem_C2S.newBuilder();
        builder.setItemKey(itemUniqId);
        int num = RandomUtils.randomBetween(1, 10);
        builder.setNum(num);
        for (int i = 0; i < num; i++) {
            builder.getParamsBuilder().getPointListBuilder().addDatas(StructPB.PointPB.newBuilder().setX(indexX * 5000).setY(indexY * 5000).build());
            indexX++;
            if (indexX > 10) {
                indexY++;
                indexX = 1;
            }
        }
        sendMsgToServerSync(MsgType.PLAYER_USEITEM_C2S, builder.build());
    }

    @Override
    protected void afterEnterBigScene() {
        CncFlowUtil.sendDebugCommand(this, "AddItem templateId=21000027 count=5000");

        long itemUniqId = -1;
        for (Map.Entry<Long, ItemProp> itemProp : getBoard().playerProp.getItems().entrySet()) {
            if (itemProp.getValue().getTemplateId() == 21000027) {
                itemUniqId = itemProp.getKey();
                break;
            }
        }

        if (itemUniqId <= 0) {
            return;
        }

        waitAllRobotLoginSuccess();

        realSleep(5000);
        realSleep(RandomUtils.nextInt(500));

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i-- >= 0) {
            realSleep(1000);
            sendMsg(itemUniqId);
        }
        end();
    }

}

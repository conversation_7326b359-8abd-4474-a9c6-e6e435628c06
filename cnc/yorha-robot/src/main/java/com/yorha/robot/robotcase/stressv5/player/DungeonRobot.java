package com.yorha.robot.robotcase.stressv5.player;

import com.google.common.collect.ImmutableList;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.FastTrainFlow;
import com.yorha.robot.flow.dungeon.EnterDungeonFlow;
import com.yorha.robot.flow.dungeon.LeaveDungeonFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DungeonRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(DungeonRobot.class);

    public DungeonRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    public static List<Point> pointList = ImmutableList.of(
            Point.valueOf(23000, 21000),
            Point.valueOf(28000, 25000),
            Point.valueOf(31000, 22000),
            Point.valueOf(28000, 27000)
    );

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePowerSimple");
        }
        runFlow(new FastTrainFlow(), 1001, 2000);
        waitAllRobotLoginSuccess();

        realSleep(5000);
        realSleep(RandomUtils.nextInt(500));

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i-- >= 0) {
            runFlow(new EnterDungeonFlow(), 1409, 1408, 1001, 2000, 10001);
            realSleep(1000);
//            waitState("army exist", () -> getBoard().getMyArmies().size() > 0);
//            Point movePoint = getMovePoint();
//            runFlow(new ChangeArmyFlow(), getBoard().getFirstArmy().getFirst(), CommonEnum.ArmyActionType.AAT_Move, 0L, movePoint.getX(), movePoint.getY());
//            realSleep(1000);
            runFlow(new LeaveDungeonFlow());
            realSleep(1000);
        }

        end();
    }

    private Point getMovePoint() {
        return pointList.get(getRobotId() % 4);
    }

}

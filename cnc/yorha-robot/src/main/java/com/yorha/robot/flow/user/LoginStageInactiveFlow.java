package com.yorha.robot.flow.user;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.proto.User;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 登陆过程中断线
 *
 * <AUTHOR>
 */
public class LoginStageInactiveFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(LoginStageInactiveFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {

        // 必须是已有角色
        long playerId = robot.getBoard().getPlayerId();
        if (playerId <= 0) {
            throw new GeminiException("must has playerId");
        }

        User.Login_C2S_Msg.Builder builder = User.Login_C2S_Msg.newBuilder();
        builder.setAccountToken(robot.getAccountToken())
                .setPlayerId(playerId)
                .setDebugStartNewbie(false)
                .setZoneId(1);
        robot.sendMsgToServerAsync(MsgType.LOGIN_C2S_MSG, builder.build());
    }
}

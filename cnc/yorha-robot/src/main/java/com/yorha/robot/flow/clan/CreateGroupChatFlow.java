package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.ChannelInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerChat;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 拉起群聊
 *
 * <AUTHOR>
 */
public class CreateGroupChatFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateGroupChatFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        int groupMember = (int) args[0];
        boolean isGroupOwner = (robot.getRobotId() % groupMember) == 0;
        ChannelInfoProp c = robot.getBoard().playerProp.getChatPlayer().getChannelInfo()
                .get(CommonEnum.ChatChannel.CC_GROUP_VALUE);

        if (c != null) {
            return;
        }

        if (isGroupOwner) {
            PlayerChat.Player_CreateGroupChat_C2S.Builder b = PlayerChat.Player_CreateGroupChat_C2S.newBuilder();
            int membersCnt = 0;
            LoginScene loginScene = RobotMgr.getInstance().getScene(robot.getUser(), LoginScene.class);

            while (membersCnt < groupMember) {
                long robotMemberId = loginScene.getRobotPlayerId(robot.getRobotId() + membersCnt);
                b.addMembers(robotMemberId);
                membersCnt++;
            }

            b.setName(String.valueOf(robot.getRobotId()));
            robot.sendMsgToServerSync(MsgType.PLAYER_CREATEGROUPCHAT_C2S, b.build());
        }
    }
}

package com.yorha.robot.robotcase.stressv65;

import com.yorha.game.gen.prop.ItemProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.city.RandomMoveCityFlow;
import com.yorha.robot.flow.user.CreatePlayerFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.robotcase.stress.login.RegisterPlayerRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class RegisterAndMoveCityRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(RegisterPlayerRobot.class);

    public RegisterAndMoveCityRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void run() {
        runFlow(new GetPlayerListFlow());
        runFlow(new CreatePlayerFlow(), isSkipNewbie());
        waitState("waitLoginFinish", () -> {
            if (getBoard().getSceneId() == 0) {
                return false;
            }
            if (getBoard().playerProp == null) {
                return false;
            }
            if (getBoard().cityProp == null) {
                return false;
            }
            return true;
        });

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        // 获取足够的迁城道具
        runFlow(new DebugCmdFlow(), StringUtils.format("AddItem templateId=10610001 count={}", i));
        final int needItemNum = i;
        waitState("waitItemProp", () -> {
            for (ItemProp itemProp : getBoard().playerProp.getItems().values()) {
                if (itemProp.getTemplateId() == 10610001 && itemProp.getNum() >= needItemNum) {
                    return true;
                }
            }
            return false;
        });

        // 通过判断templateId获取迁城道具唯一id
        long itemUniqId = -1;
        for (Map.Entry<Long, ItemProp> itemProp : getBoard().playerProp.getItems().entrySet()) {
            if (itemProp.getValue().getTemplateId() == 10610001 || itemProp.getValue().getTemplateId() == 0) {
                itemUniqId = itemProp.getKey();
                break;
            }
        }
        if (itemUniqId == -1) {
            throw new RuntimeException(StringUtils.format("{} has no random move item", this));
        }
        
        // 深拷贝
        Point lastCityPoint = Point.valueOf(getBoard().cityProp.getPoint().getX(), getBoard().cityProp.getPoint().getY());
        while (i > 0) {
            // 使用随机迁城
            runFlow(new RandomMoveCityFlow(), itemUniqId);
            Point curCityPoint = Point.valueOf(getBoard().cityProp.getPoint().getX(), getBoard().cityProp.getPoint().getY());
            LOGGER.debug("last:{} cur:{}", lastCityPoint, curCityPoint);
            lastCityPoint = curCityPoint;
            this.realSleep(1000);
            i--;
        }
        end();
    }
}

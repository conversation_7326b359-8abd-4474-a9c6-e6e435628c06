package com.yorha.robot.robotcase.stressv65;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.flow.dir.GetZoneListFlow;
import com.yorha.robot.flow.user.CreatePlayerFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DriveTrafficRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(FullRegisterRobot.class);

    public DriveTrafficRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void firstStart() {
        try {
            int hardwareLevel = RandomUtils.nextInt(5);
            this.getBoard().setHardwareLevel(hardwareLevel);
            connectRandDir();
            runFlow(new GetZoneListFlow());
            disconnectServerSync();
            while (true) {
                boolean reconnect = RandomUtils.nextBoolean();
                if (!reconnect) {
                    break;
                }
                int sleepSec = 4 + RandomUtils.nextInt(3);  //[4, 6]
                realSleep(TimeUtils.second2Ms(sleepSec));
                connectRandDir();
                runFlow(new GetZoneListFlow());
                disconnectServerSync();
            }
            // 连接dir指定zone
            connectServerSync(this.getBoard().getIp(), this.getBoard().getPort());
            runFlow(new GetPlayerListFlow());
            runFlow(new CreatePlayerFlow(), isSkipNewbie());
            disconnectServerSync();
            // 完成
        } catch (Exception e) {
            LOGGER.error("LoginRobot firstStart fail, ", e);
        } finally {
            disconnectAndEnd();
        }

    }

    private void connectRandDir() {
        int i = RandomUtils.nextInt(3);
        int port = -1;
        if (i == 0) {
            port = 8888;
        } else if (i == 1) {
            port = 8889;
        } else {
            port = 8890;
        }
        connectServerSync("***********", port);
    }

    private void setHardwareLevel() {
        // int[][] rule = {{1, 6}, {4, 1}, {1, 2}}; // 州落堡qa测试
        int[][] rule = {{3, 5999}, {1, 4000}, {1, 1}, {3, 1}, {3, 6000}, {1, 4000}, {1, 27999}, {1, 1}}; // 州落堡策划案压测
//        int[][] rule = {{3, 10000}, {1, 1}, {3, 1000}, {3, 100}, {1, 100}, {1, 3799}, {1, 1}, {1, 100}};    // 导量热更

        // 从1开始
        int robotId = this.getRobotId();

        for (int[] curRule : rule) {
            int hardwareLevel = curRule[0];
            int numLimit = curRule[1];
            //0-5998 5999个机器人
            if (robotId < numLimit) {
                this.getBoard().setHardwareLevel(hardwareLevel);
                return;
            }
            robotId -= numLimit;
        }
        LOGGER.error("robot num over hardwareLevel rule");
        disconnectAndEnd();
    }

//    private void connectRandomDir() {
//        int i = RandomUtils.nextInt(0, 3);
//        switch (i) {
//            case 0:
//                connectServerSync("10.100.0.10", 8888);
//                break;
//            case 1:
//                connectServerSync("10.100.0.136", 8888);
//                break;
//            case 2:
//                connectServerSync("10.100.0.136", 8888);
//                break;
//        }
//    }
}

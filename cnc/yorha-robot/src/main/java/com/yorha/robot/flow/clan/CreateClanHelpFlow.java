package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class CreateClanHelpFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateClanHelpFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_CreateClanHelp_C2S.Builder builder = PlayerClan.Player_CreateClanHelp_C2S.newBuilder();
        CommonEnum.QueueTaskType taskType = CommonEnum.QueueTaskType.forNumber((int) args[0]);
        long taskId = (long) args[1];
        builder.setTaskType(taskType).setTaskId(taskId);
        robot.sendMsgToServerSync(MsgType.PLAYER_CREATECLANHELP_C2S, builder.build());
    }
}

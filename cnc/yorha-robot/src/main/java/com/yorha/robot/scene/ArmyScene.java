package com.yorha.robot.scene;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.AbstractRobotScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 行军相关压测用例场景共享数据
 *
 * <AUTHOR>
 */

public class ArmyScene extends AbstractRobotScene {
    private static final Logger LOGGER = LogManager.getLogger(ArmyScene.class);
    /**
     * 行军id
     */
    private final Map<Integer, Long> armyMap = new ConcurrentHashMap<>();
    private final Map<Long, List<Long>> clanArmy = new ConcurrentHashMap<>();
    private final Map<Long, Point> armyPoint = new ConcurrentHashMap();

    public void addArmy(int robotId, long armyId) {
        armyMap.put(robotId, armyId);
    }

    public long getArmyId(int robotId) {
        return armyMap.getOrDefault(robotId, 0L);
    }

    public synchronized void addClanArmy(long clanId, long armyId, Point point) {
        if (!clanArmy.containsKey(clanId)) {
            clanArmy.put(clanId, new ArrayList<>());
        }
        clanArmy.get(clanId).add(armyId);

        if (armyPoint.size() < 1000) {
            armyPoint.put(armyId, point);
        }
    }

    public synchronized void removeClanArmy(long clanId, Long armyId) {
        if (!clanArmy.containsKey(clanId)) {
            return;
        }
        clanArmy.get(clanId).remove(armyId);
    }

    public synchronized long getClanRandomArmyId(long clanId) {
        if (!clanArmy.containsKey(clanId) || clanArmy.get(clanId).isEmpty()) {
            return 0;
        }
        return RandomUtils.randomList(clanArmy.get(clanId));
    }

    public synchronized Point getPoint() {
        return RandomUtils.randomList(new ArrayList<>(armyPoint.values()));
    }

    public synchronized void removeArmy(int robotId) {
        armyMap.remove(robotId);
    }

    public Map<Integer, Long> getArmyMap() {
        return armyMap;
    }
}

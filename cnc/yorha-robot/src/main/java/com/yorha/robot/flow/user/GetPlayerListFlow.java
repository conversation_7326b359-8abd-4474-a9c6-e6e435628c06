package com.yorha.robot.flow.user;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum.Language;
import com.yorha.proto.CommonMsg.SimplePlayerInfo;
import com.yorha.proto.User;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/27 11:21
 */
public class GetPlayerListFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(GetPlayerListFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        int hardwareLevel = robot.getBoard().getHardwareLevel();
        int targetZoneId = robot.getBoard().getZoneId();
        User.GetRoleList_C2S_Msg.Builder builder = User.GetRoleList_C2S_Msg.newBuilder();
        builder.setAccountToken(robot.getAccountToken())
                .getClientInfoBuilder()
                .setLanguage(Language.en)
                .setHardwareLevel(hardwareLevel)
                .setRegisterDriveTrafficReason(robot.getBoard().getDriveTrafficReason())
                .setVersion(ConfigMgr.getInstance().getConfig(robot.getUser().getUserName()).clientVersion);
        builder.setZoneId(targetZoneId);

        User.GetRoleList_S2C_Msg rsp = (User.GetRoleList_S2C_Msg) robot.sendMsgToServerSync(MsgType.GETROLELIST_C2S_MSG, builder.build());
        if (rsp == null) {
            return;
        }
        List<SimplePlayerInfo> playerInfoListList = rsp.getPlayerInfoListList();
        if (!ConfigMgr.getInstance().getConfig(robot.getUser().getUserName()).loginExistedPlayer) {
            // 外面的配置可以控制是否强制注册新角色，注册就不选中某一个登录角色了
            return;
        }

        for (SimplePlayerInfo playerInfo : playerInfoListList) {
            // 账号下有角色, 登录已有的角色
            robot.getBoard().setPlayerId(playerInfo.getPlayerId());
            break;
        }
    }
}

package com.yorha.robot.robotcase.stress.mail;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 填满邮件
 */
public class FullMailRobot extends EnterWorldRobot {

    public FullMailRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        int executeRound = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        String gm = StringUtils.format("MailPressTest num={} saveContent=0", executeRound);
        runFlow(new DebugCmdFlow(), gm);
        end();
    }
}
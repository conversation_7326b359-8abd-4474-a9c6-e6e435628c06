package com.yorha.robot.robotcase.stress.clan;

import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.clan.AcceptClanApplyFlow;
import com.yorha.robot.flow.clan.ApplyJoinClanFlow;
import com.yorha.robot.flow.clan.CreateOrWaitClanFlow;
import com.yorha.robot.flow.clan.QuitClanFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class JoinClanRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(JoinClanRobot.class);

    public JoinClanRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }
        waitAllRobotLoginSuccess();

        // 盟主去建盟 剩下的人等待
        runFlow(new CreateOrWaitClanFlow());

        // 等所有的盟建好
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 联盟总个数
        int totalClanCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - 1) / Constant.CLAN_MEMBER_MAX + 1;
        waitState("waitAllClanReady", () -> scene.getClanCount() >= totalClanCount);

        // 联盟编号
        int clanNum = getRobotId() / Constant.CLAN_MEMBER_MAX;
        boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int totalCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - totalClanCount) * i;

        if (!isClanOwner && getBoard().playerProp.getClan().getClanId() != 0) {
            // 退盟
            runFlow(new QuitClanFlow());
            waitState("waitJoinClanProp", () -> getBoard().playerProp.getClan().getClanId() == 0);
        }

        if (isClanOwner) {
            LoginScene countScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
            while (true) {
                // 拉申请列表 同意
                runFlow(new AcceptClanApplyFlow());
                // 看看测完没
                if (countScene.checkCountFinished(totalCount)) {
                    break;
                }
            }
        } else {
            while (i-- > 0) {
                //申请
                runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
                waitState("waitJoinClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
                // 退盟
                runFlow(new QuitClanFlow());
                waitState("waitJoinClanProp", () -> getBoard().playerProp.getClan().getClanId() == 0);
                RobotMgr.getInstance().getScene(getUser(), LoginScene.class).addCount();
            }
        }
        finished();
    }
}

package com.yorha.robot.robotcase.stress.player;

import com.yorha.robot.core.User;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.translator.TranslateTextFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

@RobotCase(
        name = "翻译机器人",
        desc = "疯狂翻译"
)
public class TranslateTextRobot extends EnterWorldRobot {
    public TranslateTextRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i > 0) {
            runFlow(new TranslateTextFlow(), "hello timi a! one two three");
            runFlow(new TranslateTextFlow(), "see damon a! two one three");
            runFlow(new TranslateTextFlow(), "play with zeo a! three one three");
            runFlow(new TranslateTextFlow(), "i love you a! four one three");
            runFlow(new TranslateTextFlow(), "best hudie s! five one three");
            runFlow(new TranslateTextFlow(), "lala heha one!You make my heart smile. In such a soft and warm season, " +
                    "please accept my sincere blessing and deep concern for you. For our ever-lasting friendship, " +
                    "send sincere blessings and warm greetings to my friends whom I miss so much. " +
                    "Wish you a happy new year and a good fortune in the coming year when we will share our happiness, " +
                    "think of our good friends, and our dreams come true!"
                    + "lala heha!You make my heart smile. In such a soft and warm season, please accept my sincere blessing and deep concern for you. " +
                    "For our ever-lasting friendship, send sincere blessings and warm greetings to my friends whom I miss so much. " +
                    "Wish you a happy new year and a good fortune in the coming year when we will share our happiness, " +
                    "think of our good friends, and our dreams come true!");
            i--;
        }
        finished();
    }
}

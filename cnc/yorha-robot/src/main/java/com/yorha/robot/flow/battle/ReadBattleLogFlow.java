package com.yorha.robot.flow.battle;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerMail;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 获取战斗日志
 *
 * <AUTHOR>
 */
public class ReadBattleLogFlow implements Cnc<PERSON>low {
    @Override
    public void run(CncRobot robot, Object[] args) {
        long logId = (long) args[0];

        PlayerMail.Player_ReadBattleLog_C2S.Builder builder = PlayerMail.Player_ReadBattleLog_C2S.newBuilder();
        builder.setLogId(logId)
                .setPageId(1);
        robot.sendMsgToServerSync(MsgType.PLAYER_READBATTLELOG_C2S, builder.build());
    }
}

package com.yorha.robot.robotcase.stressv5.city;

import com.yorha.game.gen.prop.ItemProp;
import com.yorha.common.utils.RandomUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.city.RandomMoveCityFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 随机迁城
 *
 * <AUTHOR>
 */
public class CityRandomMoveRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(CityRandomMoveRobot.class);

    public CityRandomMoveRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "Offset seconds=3600");
        }

        // 获取足够的迁城道具
        runFlow(new DebugCmdFlow(), StringUtils.format("AddItem templateId=10610001 count={}", 99999));
        waitState("waitItemProp", () -> {
            for (ItemProp itemProp : getBoard().playerProp.getItems().values()) {
                if (itemProp.getTemplateId() == 10610001 && itemProp.getNum() >= 99999) {
                    return true;
                }
            }
            return false;
        });
        // 通过判断templateId获取迁城道具唯一id
        long itemUniqId = -1;
        for (Map.Entry<Long, ItemProp> itemProp : getBoard().playerProp.getItems().entrySet()) {
            if (itemProp.getValue().getTemplateId() == 10610001 || itemProp.getValue().getTemplateId() == 0) {
                itemUniqId = itemProp.getKey();
                break;
            }
        }
        if (itemUniqId == -1) {
            throw new RuntimeException(StringUtils.format("{} has no random move item", this));
        }

        Point lastCityPoint = null;
        // 深拷贝
        if (LOGGER.isDebugEnabled()) {
            lastCityPoint = Point.valueOf(getBoard().cityProp.getPoint().getX(), getBoard().cityProp.getPoint().getY());
        }

        waitAllRobotLoginSuccess();

        realSleep(5000);
        realSleep(RandomUtils.nextInt(500));

        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            this.realSleep(1000);
            // 使用随机迁城
            runFlow(new RandomMoveCityFlow(), itemUniqId);
            if (LOGGER.isDebugEnabled()) {
                Point curCityPoint = Point.valueOf(getBoard().cityProp.getPoint().getX(), getBoard().cityProp.getPoint().getY());
                LOGGER.debug("last:{} cur:{}", lastCityPoint, curCityPoint);
                lastCityPoint = curCityPoint;
            }
        }
        end();
    }
}

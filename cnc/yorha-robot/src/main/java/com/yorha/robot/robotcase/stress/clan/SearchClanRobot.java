package com.yorha.robot.robotcase.stress.clan;

import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.clan.ApplyJoinClanFlow;
import com.yorha.robot.flow.clan.CreateClanFlow;
import com.yorha.robot.flow.clan.SearchClanAndGetClanMemberFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 搜索军团的机器人
 *
 * <AUTHOR>
 */
public class SearchClanRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(SearchClanRobot.class);

    public SearchClanRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        final int clanNum = getRobotId() / Constant.CLAN_MEMBER_MAX;
        final int totalClanCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - 1) / Constant.CLAN_MEMBER_MAX + 1;
        final boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;

        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        if (isClanOwner) {
            // 军团长创建军团
            if (getBoard().getPlayerProp().getClan().getClanId() == 0) {
                CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=5 count=1000");
                String clanSName = String.valueOf(clanNum + 10000);
                clanSName = clanSName.substring(1, 5);
                String clanNamePrefix = getRobotName();
                String clanName = clanNamePrefix + clanNum;
                LOGGER.info("CreateClanFlow sName:{} name:{}", clanSName, clanName);
                runFlow(new CreateClanFlow(), clanSName, clanName);
                waitState("waitClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
            }
            scene.putClan(clanNum, getBoard().playerProp.getClan().getClanId(), getBoard().getPlayerProp().getScenePlayer().getMainCityId());
        }

        waitState("waitAllClanReady", () -> scene.getClanCount() >= totalClanCount);
        if (!isClanOwner) {
            // 军团成员加入军团
            if (getBoard().getPlayerProp().getClan().getClanId() == 0) {
                // 未进入军团玩家进入一下
                runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
                // 确认自己进入军团
                waitState("waiJoinClanFinish", () -> getBoard().getPlayerProp().getClan().getClanId() != 0);
            }
        }

        final int runTimes = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < runTimes; ++i) {
            if (getRobotId() % 2 == 0) {
                runFlow(new SearchClanAndGetClanMemberFlow());
            } else {
                runFlow(new SearchClanAndGetClanMemberFlow(), getRobotName().substring(0, 5));
            }
            realSleep(1000);
        }
        finished();
    }
}

package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 搜索联盟
 * 链路调用：client -> player -> name(one call) -> player -> clan(multiple calls) -> player(整合) -> client
 * <p>
 * 搜索联盟有两种方式：
 * 1. 不带名字的搜索
 * 客户端入口：(1) 未加入军团时点击加入军团 (2) 加入军团后点击军团列表
 * 当小区内军团数小于或等于策划配置个数，所有玩家返回同样的名字列表
 * 当小区内军团数大于策划配置个数，随机返回等于策划配置个数的名字列表（当前没有特殊选择逻辑）
 * 通过名字列表【逐个】去拉起clan获取信息
 * 2. 带名字的搜索
 * 客户端入口：(1) 未加入军团时点击加入军团后输入关键词点击搜索 (2) 加入军团后点击军团列表输入关键词点击搜索
 * 逐个比较名字，最终只返回小于等于策划配置个数的名字列表
 * 通过名字列表【逐个】去拉起clan获取信息
 *
 * <AUTHOR>
 */
public class SearchClanFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(SearchClanFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_SearchClan_C2S.Builder builder = PlayerClan.Player_SearchClan_C2S.newBuilder();
        if (args.length > 0) {
            String searchName = String.valueOf(args[0]);
            builder.setName(searchName);
        }
        robot.sendMsgToServerSync(MsgType.PLAYER_SEARCHCLAN_C2S, builder.build());
    }
}

package com.yorha.robot.robotcase.stressv65;

import com.google.common.collect.ImmutableList;
import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */
public class BuildNameRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(BuildNameRobot.class);

    public BuildNameRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    List<String> nameList = ImmutableList.of(
            "可乐",
            "大可乐",
            "可乐123",
            "可乐1111",
            "可乐cc",
            "可乐cola",
            "可口可乐",
            "百事可乐",
            "可乐饼",
            "可乐鸡翅",
            "可乐虾",
            "可乐qq糖",
            "可乐排骨",
            "香酥排骨",
            "蒜香排骨",
            "排骨汤",
            "排骨莲藕汤",
            "cola",
            "Cola",
            "COLA",
            "CocaCola",
            "Coco",
            "coco",
            "Coca",
            "kele",
            "chocolate",
            "chocomilk",
            "soda",
            "Soda",
            "SodaWater",
            "乐可",
            "可乐可",
            "可乐Su123",
            "可kou乐",
            "可 乐",
            "可乐 sU 123",
            "可   乐",
            "可 乐 shui",
            "可乐 suda",
            "1  23",
            "12 3",
            "c ola",
            "col a"
    );

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePowerSimple");

        }
//        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
//        if (getBoard().getPlayerProp().getAvatarModel().getCardHead().getName().contains("_")) {
////            String s = genName();
//            String s = NumToNameHelper.randomGenName(getRobotId());
//            // 改个名
//            runFlow(new ChangeNameFlow(), s);
//            LOGGER.info("{} {}", getRobotId(), s);
//            // 加点兵 构造不同战力
//            runFlow(new FastTrainFlow(), 1001, RandomUtils.nextInt(1000));
//        }
        end();
    }

    private String genName() {
        if (getRobotId() < nameList.size()) {
            return nameList.get(getRobotId());
        }
        List<String> name = new ArrayList<>();
        //2中文+2小写+2大写+2数字
        int s1 = RandomUtils.nextInt(0x4E00, 0x9FBF);
        name.add(String.valueOf((char) s1));
        int s2 = RandomUtils.nextInt(97, 123);
        name.add(String.valueOf((char) s2));
        int s3 = RandomUtils.nextInt(65, 91);
        name.add(String.valueOf((char) s3));
        int s4 = RandomUtils.nextInt(0, 10);
        name.add(String.valueOf(s4));
        int s5 = RandomUtils.nextInt(0x4E00, 0x9FBF);
        name.add(String.valueOf((char) s5));
        int s6 = RandomUtils.nextInt(97, 123);
        name.add(String.valueOf((char) s6));
        int s7 = RandomUtils.nextInt(65, 91);
        name.add(String.valueOf((char) s7));
        int s8 = RandomUtils.nextInt(0, 10);
        name.add(String.valueOf(s8));
        Collections.shuffle(name);
        return String.join("", name);
    }
}

package com.yorha.robot.robotcase.stress.marquee;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.flow.marquee.SendMarqueeFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 联盟跑马灯
 *
 * <AUTHOR>
 */
public class ClanMarqueeRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ClanMarqueeRobot.class);

    public ClanMarqueeRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        CncFlowUtil.sendDebugCommand(this, "GiveMePower");

        waitAllRobotLoginSuccess();


        runFlow(new CreateOrApplyClanFlow());

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i > 0) {
            runFlow(new SendMarqueeFlow(), "clan");
            i--;
        }
        finished();
    }
}


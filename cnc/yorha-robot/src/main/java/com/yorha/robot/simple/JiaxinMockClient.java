package com.yorha.robot.simple;

import com.yorha.proto.*;
import com.yorha.robot.SimpleMockClient;

import static com.yorha.proto.CommonEnum.*;
import static com.yorha.proto.PlayerClanTech.*;

/**
 * 夹心功能测试机器人
 */
public class JiaxinMockClient extends SimpleMockClient {
    public static void main(String[] args) throws InterruptedException {
        initAll(8889);
        String openId = "f75de02ffced47d7a4580b113787f377";
        long playerId = 12641423;
        int zoneId = 1;
        login(openId, playerId, zoneId);
        // note: add by jiaxin, for test tech addition

        sendAndWait(PlayerClan.Player_ConstructClanStrongholdBuilding_C2S.newBuilder().setType(MapBuildingType.MBT_COMMAND_CENTER).setMapBuildingId(9).build());
    }
}

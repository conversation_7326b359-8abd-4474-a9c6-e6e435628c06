package com.yorha.robot.robotcase.stress.city;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.city.MoveCityCheckFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class MoveCityCheckRobot extends EnterWorldRobot {
    public MoveCityCheckRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int baseX = getBoard().cityProp.getPoint().getX();
        int baseY = getBoard().cityProp.getPoint().getY();
        while (i > 0) {
            // 随机点
            int x = RandomUtils.nextInt(20000) - 10000;
            int y = RandomUtils.nextInt(20000) - 10000;

            runFlow(new MoveCityCheckFlow(), x + baseX, y + baseY);
            i--;
        }
        end();
    }
}

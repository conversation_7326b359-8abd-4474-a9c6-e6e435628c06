package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 拉取军团势力图
 *
 * <AUTHOR>
 */

public class FetchClanTerritoryMapFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(FetchClanTerritoryMapFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_FetchClanTerritoryMap_C2S.Builder builder = PlayerClan.Player_FetchClanTerritoryMap_C2S.newBuilder();
        robot.sendMsgToServerSync(MsgType.PLAYER_FETCHCLANTERRITORYMAP_C2S, builder.build());
    }
}

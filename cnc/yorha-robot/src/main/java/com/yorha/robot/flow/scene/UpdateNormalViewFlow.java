package com.yorha.robot.flow.scene;

import com.yorha.common.enums.DirectionEnum;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class UpdateNormalViewFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(UpdateNormalViewFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        final PointProp cityPointProp = robot.getBoard().cityProp.getPoint();
        Point centerPoint = Point.valueOf(cityPointProp.getX(), cityPointProp.getY());

        // 第几次视野
        int i = (int) args[0];
        DirectionEnum cur = DirectionEnum.values()[i % DirectionEnum.values().length];

        int layer = (int) args[1];
        int width = (int) args[2];
        int height = (int) args[3];

        List<StructPB.PointPB> pointList = genViewPointByCenter(centerPoint.getX(), centerPoint.getY(), cur, width, height);

        PlayerScene.Player_UpdateView_C2S.Builder msg = PlayerScene.Player_UpdateView_C2S.newBuilder();
        msg.setZoneId(robot.getBoard().getZoneId());
        msg.setType(1);
        msg.setP1(pointList.get(0));
        msg.setP2(pointList.get(1));
        msg.setP3(pointList.get(2));
        msg.setP4(pointList.get(3));
        msg.setLayer(layer);
        LOGGER.debug("UpdateNormalViewFlow {}", msg);
        PlayerScene.Player_UpdateView_S2C rsp = (PlayerScene.Player_UpdateView_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_UPDATEVIEW_C2S, msg.build());
    }

    /**
     * 通过中心点和视野边长，生成视野的四个范围点
     */
    private List<StructPB.PointPB> genViewPointByCenter(int x, int y, DirectionEnum dir, int width, int height) {
        int deltaX = dir.getAddX();
        int deltaY = dir.getAddY();

        // 偏移中心点
        x += deltaX * width * 2;
        y += deltaY * height * 2;

        List<StructPB.PointPB> pointList = new ArrayList<>();
        pointList.add(StructPB.PointPB.newBuilder().setX(x - width / 2).setY(y + height / 2).build());
        pointList.add(StructPB.PointPB.newBuilder().setX(x + width / 2).setY(y + height / 2).build());
        pointList.add(StructPB.PointPB.newBuilder().setX(x - width / 2).setY(y - height / 2).build());
        pointList.add(StructPB.PointPB.newBuilder().setX(x + width / 2).setY(y - height / 2).build());
        return pointList;
    }
}

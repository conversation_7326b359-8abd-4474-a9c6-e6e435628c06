package com.yorha.robot.flow.army;

import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.PlayerScene.Player_SearchWalkPath_C2S;
import com.yorha.proto.PlayerScene.Player_SearchWalkPath_S2C;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class SearchWalkPathFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        Point point = (Point) args[0];

        PointProp mainCityPoint = robot.getBoard().cityProp.getPoint();

        Player_SearchWalkPath_C2S.Builder builder = Player_SearchWalkPath_C2S.newBuilder();
        builder.getSrcPointBuilder().setX(mainCityPoint.getX()).setY(mainCityPoint.getY());
        builder.getEndPointBuilder().setX(Math.max(point.getX(), 1)).setY(Math.max(point.getY(), 1));

        Player_SearchWalkPath_S2C rspMsg = (Player_SearchWalkPath_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_SEARCHWALKPATH_C2S, builder.build());
        if (rspMsg.getPointList().getDatasCount() <= 1) {
            throw new RuntimeException(StringUtils.format("{} wrong end point:{}, cannot find path", robot, point));
        }
    }

}

package com.yorha.robot.flow.scene;

import com.yorha.common.enums.DirectionEnum;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.PlayerScene.Player_UpdateView_C2S;
import com.yorha.proto.PlayerScene.Player_UpdateView_S2C;
import com.yorha.proto.StructPB.PointPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/28 17:47
 */
public class UpdateViewFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(UpdateViewFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        DirectionEnum dir = null;
        int layer = 1;
        if (args.length != 0) {
            // 第几次视野
            int i = (int) args[0];
            dir = DirectionEnum.values()[i % DirectionEnum.values().length];
            layer = i % 8 + 1;
        }

        final PointProp cityPointProp = robot.getBoard().cityProp.getPoint();
        Point centerPoint = Point.valueOf(cityPointProp.getX(), cityPointProp.getY());

        if (args.length > 1) {
            // 自己指定视野中心
            centerPoint = (Point) args[1];
            layer = (int) args[2];
        }
        List<PointPB> pointList = genViewPointByCenter(Point.valueOf(centerPoint.getX(), centerPoint.getY()), dir, layer);

        Player_UpdateView_C2S.Builder msg = Player_UpdateView_C2S.newBuilder();
        msg.setType(1);
        msg.setP1(pointList.get(0));
        msg.setP2(pointList.get(1));
        msg.setP3(pointList.get(2));
        msg.setP4(pointList.get(3));
        msg.setLayer(layer);
        Player_UpdateView_S2C rsp = (Player_UpdateView_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_UPDATEVIEW_C2S, msg.build());
    }

    /**
     * 通过中心点和视野边长，生成视野的四个范围点
     */
    private List<PointPB> genViewPointByCenter(Point point, DirectionEnum dir, int layer) {
        int pointX = point.getX();
        int pointY = point.getY();

        // 偏移中心点
        if (dir != null) {
            pointX = pointX + dir.getAddX() * 4000;
            pointY = pointY + dir.getAddY() * 4000;
        }

        List<PointPB> pointList = new ArrayList<>();
        pointList.add(PointPB.newBuilder().setX(pointX - 2000 * layer).setY(pointY + 2000 * layer).build());
        pointList.add(PointPB.newBuilder().setX(pointX + 2000 * layer).setY(pointY + 2000 * layer).build());
        pointList.add(PointPB.newBuilder().setX(pointX - 2000 * layer).setY(pointY - 2000 * layer).build());
        pointList.add(PointPB.newBuilder().setX(pointX + 2000 * layer).setY(pointY - 2000 * layer).build());
        return pointList;
    }

}

package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerRally;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class FetchWarningInfoFlow implements CncFlow {
    @Override
    public void run(Cnc<PERSON><PERSON>ot robot, Object[] args) {
        PlayerRally.Player_FetchWarningList_C2S.Builder builder = PlayerRally.Player_FetchWarningList_C2S.newBuilder();
        robot.sendMsgToServerSync(MsgType.PLAYER_FETCHWARNINGLIST_C2S, builder.build());
    }
}

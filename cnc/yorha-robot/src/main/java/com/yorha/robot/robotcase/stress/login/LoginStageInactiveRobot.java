package com.yorha.robot.robotcase.stress.login;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginStageInactiveFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 游戏服
 * 登录角色
 *
 * <AUTHOR>
 */
public class LoginStageInactiveRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(LoginStageInactiveRobot.class);

    public LoginStageInactiveRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void firstStart() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        connectServerSync(config.host, config.port);
        runFlow(new GetPlayerListFlow());
        runFlow(new LoginStageInactiveFlow(), isSkipNewbie());
        realSleep(RandomUtils.nextInt(1, 5));
        end();
    }

    @Override
    public void run() {
//        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
//        connectServerSync(config.host, config.port);
//        runFlow(new GetPlayerListFlow());
//        runFlow(new CheckResourceFlow());
//        runFlow(new LoginStageInactiveFlow(), isSkipNewbie());
//        realSleep(RandomUtils.nextInt(50, 1000));
//        end();
////        disconnectServerSync();
//        finished();
    }
}
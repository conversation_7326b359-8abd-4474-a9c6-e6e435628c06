package com.yorha.robot.core;

import com.yorha.common.concurrent.IGeminiDispatcher;
import com.yorha.robot.bean.BaseArgs;
import com.yorha.robot.core.base.Robot;
import com.yorha.robot.core.manager.RobotMgr;
import io.netty.bootstrap.Bootstrap;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 操作者
 *
 * <AUTHOR>
 */
public class User implements Runnable {
    private final String userName;
    private final Bootstrap bootstrap;
    private final Map<Integer, Robot> robotMap;
    private final Set<Integer> finishedRobotIdSet;
    private final IGeminiDispatcher geminiDispatcherWithFiber;
    private final IGeminiDispatcher robotMgrDispatcher;
    private final Map<Class<? extends AbstractRobotScene>, AbstractRobotScene> sceneMap;
    private final Object arg;
    /**
     * 配置的limit等待
     */
    private final int configLimit;
    /**
     * 当前跑的数量
     */
    private final AtomicInteger curLimit;

    private final RobotStats robotStats;
    /**
     * 机器人启动时间戳
     */
    private final long startTsMs;
    private AtomicBoolean isStop = new AtomicBoolean(false);

    public Set<Integer> getFinishedRobotIdSet() {
        return this.finishedRobotIdSet;
    }

    public long getStartTsMs() {
        return this.startTsMs;
    }

    public Map<Integer, Robot> getRobotMap() {
        return this.robotMap;
    }

    public String getUserName() {
        return this.userName;
    }

    public Bootstrap getBootstrap() {
        return this.bootstrap;
    }

    public IGeminiDispatcher getGeminiDispatcherWithFiber() {
        return this.geminiDispatcherWithFiber;
    }

    public IGeminiDispatcher getRobotMgrDispatcher() {
        return this.robotMgrDispatcher;
    }

    public RobotStats getRobotStats() {
        return this.robotStats;
    }

    public int getConfigLimit() {
        return this.configLimit;
    }

    public AtomicInteger getCurLimit() {
        return this.curLimit;
    }

    public Map<Class<? extends AbstractRobotScene>, AbstractRobotScene> getSceneMap() {
        return this.sceneMap;
    }

    public void stop() {
        isStop.set(true);
    }

    public boolean isStop() {
        return isStop.get();
    }

    public <T extends BaseArgs> T getArg(Class<T> clazz) {
        if (!clazz.isInstance(arg)) {
            throw new RuntimeException("not have arg class " + clazz.getName());
        }
        return (T) arg;
    }

    @Override
    public void run() {
        RobotMgr.getInstance().initAndRunRobot(this);
    }

    public User(Builder builder) {
        this.bootstrap = builder.bootstrap;
        this.robotMap = builder.robotMap;
        this.finishedRobotIdSet = builder.finishedRobotIdSet;
        this.geminiDispatcherWithFiber = builder.geminiDispatcherWithFiber;
        this.robotMgrDispatcher = builder.robotMgrDispatcher;
        this.configLimit = builder.configLimit;
        this.curLimit = builder.curLimit;
        this.robotStats = builder.robotStats;
        this.startTsMs = builder.startTsMs;
        this.userName = builder.userName;
        this.sceneMap = builder.sceneMap;
        this.arg = builder.arg;
    }

    public static final class Builder {
        private String userName;
        private Bootstrap bootstrap;
        private Map<Integer, Robot> robotMap;
        private Set<Integer> finishedRobotIdSet;
        private IGeminiDispatcher geminiDispatcherWithFiber;
        private IGeminiDispatcher robotMgrDispatcher;
        private int configLimit;
        private AtomicInteger curLimit;
        private RobotStats robotStats;
        private long startTsMs;
        private Map<Class<? extends AbstractRobotScene>, AbstractRobotScene> sceneMap;
        private Object arg;

        public Builder setUserName(String userName) {
            this.userName = userName;
            return this;
        }

        public Builder setGeminiDispatcherWithFiber(IGeminiDispatcher geminiDispatcherWithFiber) {
            this.geminiDispatcherWithFiber = geminiDispatcherWithFiber;
            return this;
        }

        public Builder setRobotMgrDispatcher(IGeminiDispatcher robotMgrDispatcher) {
            this.robotMgrDispatcher = robotMgrDispatcher;
            return this;
        }

        public Builder setStartTsMs(long startTsMs) {
            this.startTsMs = startTsMs;
            return this;
        }

        public Builder setConfigLimit(int configLimit) {
            this.configLimit = configLimit;
            return this;
        }

        public Builder setBootstrap(Bootstrap bootstrap) {
            this.bootstrap = bootstrap;
            return this;
        }

        public Builder setArg(Object arg) {
            this.arg = arg;
            return this;
        }

        private void initDefaultValue(Builder builder) {
            builder.robotMap = new ConcurrentHashMap<>();
            builder.finishedRobotIdSet = new HashSet<>();
            builder.curLimit = new AtomicInteger();
            builder.robotStats = new RobotStats();
            builder.sceneMap = new ConcurrentHashMap<>();
        }

        public User build() {
            initDefaultValue(this);
            return new User(this);
        }
    }
}

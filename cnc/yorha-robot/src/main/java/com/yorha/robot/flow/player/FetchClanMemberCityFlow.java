package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 拉取军团盟友的城池位置
 *
 * <AUTHOR>
 */
public class FetchClanMemberCityFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(FetchClanMemberCityFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_FetchClanMemberCity_C2S.Builder builder = PlayerClan.Player_FetchClanMemberCity_C2S.newBuilder();
        robot.sendMsgToServerSync(MsgType.PLAYER_FETCHCLANMEMBERCITY_C2S, builder.build());
    }
}

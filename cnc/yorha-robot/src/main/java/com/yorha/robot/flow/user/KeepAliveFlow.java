package com.yorha.robot.flow.user;

import com.yorha.common.io.MsgType;
import com.yorha.proto.Core;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

public class KeepAliveFlow implements CncFlow {

    @Override
    public void run(Cnc<PERSON><PERSON><PERSON> robot, Object[] args) {
        robot.sendMsgToServerSync(MsgType.KEEPALIVE_C2S_MSG, Core.KeepAlive_C2S_Msg.getDefaultInstance());
    }
}

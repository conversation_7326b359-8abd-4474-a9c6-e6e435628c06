package com.yorha.robot.flow.mail;

import com.yorha.common.io.MsgType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.flow.CncFlow;

/**
 * 发邮件flow
 *
 * <AUTHOR>
 */

public class SendGmMailFlow implements Cnc<PERSON>low {

    @Override
    public void run(CncRobot robot, Object[] args) {
        for (int i = 0; i < Constant.SEND_MAIL_NUM; i++) {
            PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();
            String command = "";
            if (args[0] == CommonEnum.MailType.MAIL_TYPE_ZONE) {
                command = StringUtils.format("AddZoneMail mailTemplateId={}", Constant.TEST_ZONE_MAIL_TEMPLATE_ID);
            } else if (args[0] == CommonEnum.MailType.MAIL_TYPE_CLAN && args.length == 2) {
                command = StringUtils.format("AddClanMail mailTemplateId={} targetId={}", Constant.TEST_CLAN_MAIL_TEMPLATE_ID, args[1]);
            }
            builder.setCommand(command);
            robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.build());
        }
    }
}

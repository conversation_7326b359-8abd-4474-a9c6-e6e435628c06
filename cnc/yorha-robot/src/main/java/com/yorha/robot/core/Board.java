package com.yorha.robot.core;

import com.google.common.collect.Lists;
import com.yorha.game.gen.prop.ArmyProp;
import com.yorha.game.gen.prop.CityProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.common.utils.Pair;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/07/28 10:48
 */
public class Board {
    public PlayerProp playerProp;
    public CityProp cityProp;
    private ClanProp clanProp;

    private long playerId;
    private long sceneId;
    private long mapId;
    private String ip;
    private int port;
    private String channel = "origin";
    private int hardwareLevel = 1;
    private int zoneId = 1;
    private int driveTrafficReason = 3;
    private final List<Pair<Long, ArmyProp>> myArmies = Lists.newArrayListWithExpectedSize(3);

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getSceneId() {
        return sceneId;
    }

    public long getMainCityId() {
        return playerProp.getScenePlayer().getMainCityId();
    }

    public void setSceneId(long sceneId) {
        this.sceneId = sceneId;
    }

    public void setMapId(long mapId) {
        this.mapId = mapId;
    }

    public long getMapId() {
        return mapId;
    }

    public void addMyArmy(long armyId, ArmyProp armyProp) {
        myArmies.add(Pair.of(armyId, armyProp));
    }

    public Set<Long> getMyArmyIds() {
        return myArmies.stream().mapToLong(Pair::getFirst).boxed().collect(Collectors.toSet());
    }

    public ClanProp getClanProp() {
        return clanProp;
    }

    public void setClanProp(ClanProp clanProp) {
        this.clanProp = clanProp;
    }

    public ArmyProp findMyArmy(long armyId) {
        for (Pair<Long, ArmyProp> id2army : myArmies) {
            if (id2army.getFirst() == armyId) {
                return id2army.getSecond();
            }
        }
        return null;
    }

    public ArmyProp getArmy() {
        return myArmies.get(0).getSecond();
    }

    public Pair<Long, ArmyProp> getFirstArmy() {
        if (myArmies.size() > 0) {
            return myArmies.get(0);
        }
        return null;
    }

    public List<Pair<Long, ArmyProp>> getMyArmies() {
        return myArmies;
    }

    public void tryDeleteMyArmy(long armyId) {
        myArmies.removeIf(p -> p.getFirst() == armyId);
    }

    public CityProp getCityProp() {
        return cityProp;
    }

    public PlayerProp getPlayerProp() {
        return playerProp;
    }

    public long searchResBuildingId;

    public long getSearchResBuildingId() {
        return searchResBuildingId;
    }

    public void setSearchResBuildingId(long searchResBuildingId) {
        this.searchResBuildingId = searchResBuildingId;
    }

    public long searchClanId;

    public long getSearchClanId() {
        return searchClanId;
    }

    public void setSearchClanId(long searchClanId) {
        this.searchClanId = searchClanId;
    }

    public void setIp(final String ip) {
        this.ip = ip;
    }

    public String getIp() {
        return this.ip;
    }

    public void setPort(final int port) {
        this.port = port;
    }

    public int getPort() {
        return this.port;
    }

    public int getHardwareLevel() {
        return this.hardwareLevel;
    }

    public void setHardwareLevel(int hardwareLevel) {
        this.hardwareLevel = hardwareLevel;
    }

    public String getChannel() {
        return this.channel;
    }

    public void setChannel(final String channel) {
        this.channel = channel;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    public int getZoneId() {
        return this.zoneId;
    }

    public void setDriveTrafficReason(int driveTrafficReason) {
        this.driveTrafficReason = driveTrafficReason;
    }

    public int getDriveTrafficReason() {
        return this.driveTrafficReason;
    }
}

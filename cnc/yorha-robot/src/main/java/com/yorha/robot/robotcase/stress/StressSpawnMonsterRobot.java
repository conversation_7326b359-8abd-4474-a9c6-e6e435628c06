package com.yorha.robot.robotcase.stress;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.monster.SpawnMonsterFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 压力测试刷怪
 *
 * <AUTHOR>
 */
public class StressSpawnMonsterRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(StressSpawnMonsterRobot.class);

    public StressSpawnMonsterRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i >= 0) {
            doOnce();
            i--;
        }
        finished();
    }

    private void doOnce() {
        runFlow(new SpawnMonsterFlow());
        realSleep(10 * 1000);
//        runFlow(new ClearMonsterFlow());
//        realSleep(2 * 1000);
    }
}

package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerClan;
import com.yorha.proto.PlayerClan.Player_SearchClan_S2C;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 搜索特定联盟，并在返回的联盟中拉取成员列表
 * 搜索联盟的相关说明请见SearchClanFlow
 *
 * <AUTHOR>
 */
public class SearchClanAndGetClanMemberFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(SearchClanAndGetClanMemberFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_SearchClan_C2S.Builder searchClanBuilder = PlayerClan.Player_SearchClan_C2S.newBuilder();
        if (args.length > 0) {
            String searchName = String.valueOf(args[0]);
            searchClanBuilder.setName(searchName);
        }
        Player_SearchClan_S2C ret = (Player_SearchClan_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_SEARCHCLAN_C2S, searchClanBuilder.build());

        CommonMsg.ClanCardInfo simpleInfo = RandomUtils.randomList(ret.getClanListList());
        // 当名字对应的军团不存在时随机为null
        if (null == simpleInfo) {
            if (args.length > 0) {
                LOGGER.error("check search name {}, this clan not found", String.valueOf(args[0]));
            } else {
                LOGGER.error("there is no clan can search");
            }
            return;
        }
        PlayerClan.Player_FetchClanMemberInfo_C2S.Builder fetchClanBuilder = PlayerClan.Player_FetchClanMemberInfo_C2S.newBuilder();
        fetchClanBuilder.setClanId(simpleInfo.getId());
        robot.sendMsgToServerSync(MsgType.PLAYER_FETCHCLANMEMBERINFO_C2S, fetchClanBuilder.build());
    }
}

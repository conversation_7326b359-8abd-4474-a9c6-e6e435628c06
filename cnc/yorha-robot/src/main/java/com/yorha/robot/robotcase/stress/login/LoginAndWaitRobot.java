package com.yorha.robot.robotcase.stress.login;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.KeepAliveFlow;
import com.yorha.robot.flow.user.LoginFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 游戏服
 * 等待指定时间
 *
 * <AUTHOR>
 */
public class LoginAndWaitRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(LoginAndWaitRobot.class);

    public LoginAndWaitRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void run() {
        runFlow(new GetPlayerListFlow());
        runFlow(new LoginFlow(), isSkipNewbie());
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i >= 0) {
            runFlow(new KeepAliveFlow());
            this.realSleep(5000);
            i--;
        }
        end();
    }

}

package com.yorha.robot.flow.mail;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.flow.CncFlowUtil;

/**
 * 发邮件flow
 *
 * <AUTHOR>
 */

public class SendZoneMailFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        CncFlowUtil.sendDebugCommand(robot, StringUtils.format("AddZoneMail mailTemplateId={}", Constant.TEST_ZONE_MAIL_TEMPLATE_ID));
    }
}

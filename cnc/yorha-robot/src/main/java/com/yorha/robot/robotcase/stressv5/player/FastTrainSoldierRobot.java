package com.yorha.robot.robotcase.stressv5.player;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.FastTrainFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

public class FastTrainSoldierRobot extends EnterWorldRobot {

    public FastTrainSoldierRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        runFlow(new DebugCmdFlow(), "AddCurrency currencyType=5 count=99999999");
        realSleep(5000);

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i-- >= 0) {
            runFlow(new FastTrainFlow(), 1004, 1000);
            realSleep(1000);
        }

        finished();

    }
}

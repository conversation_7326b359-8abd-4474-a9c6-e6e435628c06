package com.yorha.robot.robotcase.stressv65;

import com.yorha.common.helper.NumToNameHelper;
import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.player.ChangeNameFlow;
import com.yorha.robot.flow.player.SearchPlayerFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class SearchPlayerRobot extends EnterWorldRobot {
    public SearchPlayerRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        if (getBoard().getPlayerProp().getAvatarModel().getCardHead().getName().contains("_")) {
            // 改个名
            runFlow(new ChangeNameFlow(), "是" + getRobotId() + config.robotName);
        }
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePowerSimple");
        }

        waitAllRobotLoginSuccess();

        int executeCount = config.executeRound;
        for (int i = 0; i < executeCount; i++) {
            for (int j = 0; j < 5; j++) {
                int searchId = getRobotId() + RandomUtils.nextInt(1000);
                String searchName = NumToNameHelper.randomGenName(searchId);
                String subName = searchName.substring(0, RandomUtils.nextInt(3, 15));
                this.realSleep(500 + RandomUtils.nextInt(1000));
                runFlow(new SearchPlayerFlow(), subName);
            }
        }
        end();
    }
}

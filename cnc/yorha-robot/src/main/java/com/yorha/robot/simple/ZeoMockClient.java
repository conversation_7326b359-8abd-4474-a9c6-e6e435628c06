package com.yorha.robot.simple;

import com.yorha.proto.PlayerCommon;
import com.yorha.robot.SimpleMockClient;

/**
 * <AUTHOR>
 */
public class ZeoMockClient extends SimpleMockClient {

    public static void main(String[] args) throws InterruptedException {
        initAll(8930);
        String openId = "ad8b36ed52c147cfa3f5b7b1c7fc5829";
        long playerId = 33679637L;
        int zoneId = 1;
        login(openId, playerId, zoneId);
        sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("AddItem 8004001=10620002 count=10").build());
    }

    //============================================== 移民相关测试用例 ==================================================
    

}

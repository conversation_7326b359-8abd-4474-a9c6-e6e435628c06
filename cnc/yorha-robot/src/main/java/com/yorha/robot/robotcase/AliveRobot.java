package com.yorha.robot.robotcase;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.KeepAliveFlow;

public class AliveRobot extends EnterWorldRobot {
    public AliveRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            runFlow(new KeepAliveFlow());
            realSleep(5000);
        }
        end();
    }
}

package com.yorha.robot.core;

import com.yorha.common.perf.AbstractPerfLogger;
import com.yorha.common.utils.TimeUnitUtils;
import com.yorha.common.utils.TextTable;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.*;

@ThreadSafe
public class RobotStats extends AbstractPerfLogger {

    private Map<String, RobotMsgRecord> robotMsgRecordMap = new HashMap<>();
    private final List<Map<String, RobotMsgRecord>> totalRobotMsgRecord = new LinkedList<>();

    private static class RobotMsgRecord {
        public long sendNum = 0;

        public long doneNum = 0;
        public long totalCost = 0;
        public long maxCost = 0;

        public List<Long> costList = new LinkedList<>();

        public void onC2S() {
            sendNum++;
        }

        public void onS2C(long cost) {
            doneNum++;
            costList.add(cost);
            totalCost += cost;
            maxCost = Math.max(maxCost, cost);
        }
    }

    public RobotStats() {
        super("robot-msg");
    }

    public void onC2S(String msgName) {
        run(() -> {
            RobotMsgRecord record = robotMsgRecordMap.computeIfAbsent(msgName, k -> new RobotMsgRecord());
            record.onC2S();
        });
    }

    public void onS2C(String msgName, long cost) {
        run(() -> {
            RobotMsgRecord record = robotMsgRecordMap.computeIfAbsent(msgName, k -> new RobotMsgRecord());
            record.onS2C(cost);
        });
    }

    public void onNtf() {

    }

    private void show() {
        realShow("robot-send", robotMsgRecordMap, getLoggerPerf(), 1);
        totalRobotMsgRecord.add(robotMsgRecordMap);
        robotMsgRecordMap = new HashMap<>();
    }

    private static void realShow(String textName, Map<String, RobotMsgRecord> target, Logger logger, int costTime) {
        TextTable textTable = new TextTable(textName);
        textTable.addColumn("msgName", "c2sCount", "s2cCount", "avgCostMs", "p90CostMs", "p99CostMs", "maxCostMs", "TPS");
        for (Map.Entry<String, RobotMsgRecord> entry : target.entrySet()) {
            RobotMsgRecord record = entry.getValue();
            long avgCost = -1;
            long p90cost = -1;
            long p99cost = -1;
            if (record.doneNum != 0) {
                avgCost = record.totalCost / record.doneNum;
                Collections.sort(record.costList);
                int size = record.costList.size();
                int p90Index = (int) (0.9f * size);
                p90Index = Math.min(p90Index, size - 1);

                int p99Index = (int) (0.99f * size);
                p99Index = Math.min(p99Index, size - 1);

                p90cost = record.costList.get(p90Index);
                p99cost = record.costList.get(p99Index);
            }

            textTable.addRow().add(
                    entry.getKey(),
                    record.sendNum,
                    record.doneNum,
                    TimeUnitUtils.getStringFromNs(avgCost),
                    TimeUnitUtils.getStringFromNs(p90cost),
                    TimeUnitUtils.getStringFromNs(p99cost),
                    TimeUnitUtils.getStringFromNs(record.maxCost),
                    record.doneNum * 1.0f / costTime
            );
        }
        textTable.info(logger, false);
    }

    public void end() {
        run(() -> {
            // 遍历total，分3段输出
            int size = totalRobotMsgRecord.size();
            if (size < 10) {
                // 10秒以内的没啥意义
                return;
            }
            int gap = size / 3;
            Map<String, RobotMsgRecord> first = new HashMap<>();
            Map<String, RobotMsgRecord> second = new HashMap<>();
            Map<String, RobotMsgRecord> third = new HashMap<>();
            for (int i = 0; i < size; i++) {
                if (i < gap) {
                    merge(first, totalRobotMsgRecord.get(i));
                } else if (i < gap * 2) {
                    merge(second, totalRobotMsgRecord.get(i));
                } else {
                    merge(third, totalRobotMsgRecord.get(i));
                }
            }
            realShow("1-" + gap, first, getLoggerPerf(), gap);
            realShow("" + (gap + 1) + "-" + (gap * 2), second, getLoggerPerf(), gap);
            realShow("" + (gap * 2 + 1) + "-" + size, third, getLoggerPerf(), size - gap * 2);
        });
    }

    private void merge(Map<String, RobotMsgRecord> left, Map<String, RobotMsgRecord> right) {
        for (Map.Entry<String, RobotMsgRecord> entry : right.entrySet()) {
            String key = entry.getKey();
            RobotMsgRecord record = left.computeIfAbsent(key, k -> new RobotMsgRecord());
            mergeUnit(record, entry.getValue());
            left.put(key, record);
        }
    }

    private static void mergeUnit(RobotMsgRecord left, RobotMsgRecord right) {
        left.doneNum += right.doneNum;
        left.maxCost = Math.max(left.maxCost, right.maxCost);
        left.costList.addAll(right.costList);
        left.totalCost += right.totalCost;
        left.sendNum += right.sendNum;
    }

    public void flush() {
        run(() -> {
            show();
        });
    }

}

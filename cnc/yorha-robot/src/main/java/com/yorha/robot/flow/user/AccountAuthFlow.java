package com.yorha.robot.flow.user;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CsAccount;
import com.yorha.proto.CsAccount.DirRegisterAccount_S2C_Msg;
import com.yorha.proto.CsAccount.DirGetServerList_S2C_Msg;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 目录服压测
 *
 * <AUTHOR>
 */
public class AccountAuthFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(AccountAuthFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {

        CsAccount.DirRegisterAccount_C2S_Msg accountAuth = CsAccount.DirRegisterAccount_C2S_Msg
                .newBuilder()
                .setIdentity(
                        CommonMsg.AccountIdentity.newBuilder()
                                .setAccount(robot.getRobotName())
                                .setAccountType(CommonEnum.AccountType.AT_DEVICE_ID)
                                .build()
                )
                .build();

        DirRegisterAccount_S2C_Msg accountAuthResult = (DirRegisterAccount_S2C_Msg) robot.sendMsgToServerSync(MsgType.DIRREGISTERACCOUNT_C2S_MSG, accountAuth);
        robot.setOpenId(accountAuthResult.getToken().getOpenId());
        LOGGER.debug("robot: {} account login success. {}", robot.getRobotName(), accountAuthResult.toString());


        CsAccount.DirGetServerList_C2S_Msg findServerList = CsAccount.DirGetServerList_C2S_Msg.newBuilder()
                .setAccountToken(accountAuthResult.getToken())
                .build();
        DirGetServerList_S2C_Msg findServerListResult = (DirGetServerList_S2C_Msg) robot.sendMsgToServerSync(MsgType.DIRGETSERVERLIST_C2S_MSG, findServerList);
        LOGGER.debug("robot: {} get server list success. {}", robot.getRobotName(), findServerListResult.toString());

    }
}

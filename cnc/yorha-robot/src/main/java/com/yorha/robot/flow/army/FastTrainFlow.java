package com.yorha.robot.flow.army;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerSoldier;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class FastTrainFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        final int soldierId = (int) args[0];
        final int num = (int) args[1];
        PlayerSoldier.Player_SoldierFastTrain_C2S.Builder builder = PlayerSoldier.Player_SoldierFastTrain_C2S.newBuilder();
        builder.setNum(num).setSoldierId(soldierId);
        robot.sendMsgToServerSync(MsgType.PLAYER_SOLDIERFASTTRAIN_C2S, builder.build());
    }
}

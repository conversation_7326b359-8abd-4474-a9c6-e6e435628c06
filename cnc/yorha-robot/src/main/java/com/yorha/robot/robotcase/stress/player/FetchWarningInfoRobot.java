package com.yorha.robot.robotcase.stress.player;

import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToTargetFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.flow.player.FetchWarningInfoFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.CityScene;

/**
 * <AUTHOR>
 */
public class FetchWarningInfoRobot extends EnterWorldRobot {
    public FetchWarningInfoRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        // 加兵 加英雄
//        CncFlowUtil.sendDebugCommand(this, "Hero type=hero subType=unlock value=1401");
//        CncFlowUtil.sendDebugCommand(this, "Hero type=hero subType=unlock value=1402");
//        CncFlowUtil.sendDebugCommand(this, "AddSoldier type=addAll num=" + 1000);
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }
        // 关罩子
        CncFlowUtil.sendDebugCommand(this, "CloseProtect isSysShield=1");
        // 等罩子关完 再把自己放进被攻打的待选城池里
        CityScene scene = RobotMgr.getInstance().getScene(getUser(), CityScene.class);
        scene.addCity(getRobotId(), getBoard().getMainCityId());

        // 先派个行军过去
        if (getBoard().getMyArmies().size() != 0) {
            runFlow(new FastReturnAllMyArmyFlow());
            waitState("hero ready", () -> getBoard().getMyArmies().isEmpty());
        }
        waitState("enemy city ready", () -> getEnemyCityId() != 0);
        runFlow(new CreateArmyToTargetFlow(), false, CommonEnum.ArmyActionType.AAT_Battle, getEnemyCityId(), buildArmyTroop(this, false, 1));
        // 开始拉了
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i > 0) {
            runFlow(new FetchWarningInfoFlow());
            i--;
        }
        runFlow(new FastReturnAllMyArmyFlow());
        finished();
    }


    private long getEnemyCityId() {
        int i = getRobotId() + 1;
        return RobotMgr.getInstance().getScene(getUser(), CityScene.class).getRandomCityId(i % 10);
    }
}

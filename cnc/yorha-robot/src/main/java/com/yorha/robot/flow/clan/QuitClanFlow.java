package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class QuitClan<PERSON>low implements CncFlow {
    @Override
    public void run(Cnc<PERSON><PERSON><PERSON> robot, Object[] args) {
        robot.sendMsgToServerSync(MsgType.PLAYER_QUITCLAN_C2S, PlayerClan.Player_QuitClan_C2S.getDefaultInstance());
    }
}

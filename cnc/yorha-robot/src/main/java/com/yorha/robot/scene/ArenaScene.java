package com.yorha.robot.scene;

import com.yorha.robot.core.AbstractRobotScene;
import com.yorha.robot.core.manager.ConfigMgr;

import java.util.concurrent.atomic.AtomicInteger;

public class ArenaScene extends AbstractRobotScene {
    private final AtomicInteger count = new AtomicInteger();

    public boolean singleRobotSave(String userName) {
        return count.get() >= 1;
    }

    public boolean allRobotSave(String userName) {
        return count.get() >= ConfigMgr.getInstance().getConfig(userName).robotNum;
    }


    public void markRobotSave(int robotId, long playerId) {
        count.incrementAndGet();
    }
}

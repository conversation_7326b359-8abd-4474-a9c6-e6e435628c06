package com.yorha.robot.robotcase;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.flow.user.AccountAuthFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
@RobotCase(
        name = "鉴权账户",
        desc = "登录账户"
)
public class AccountRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(AccountRobot.class);

    public AccountRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void run() {
        // 重写CncRobot::run方法，不再需要player数据
        LOGGER.debug("{} accountLogin", this);
        runFlow(new AccountAuthFlow());
        finished();
    }


}

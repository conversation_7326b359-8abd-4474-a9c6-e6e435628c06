package com.yorha.robot.base;

import com.yorha.proto.CommonMsg;
import com.yorha.robot.core.Board;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Robot;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginOrCreatePlayerFlow;

/**
 * <AUTHOR>
 * @date 2021/07/28 11:03
 */
public class CncRobot extends Robot {

    private final Board board;

    public CncRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
        this.board = new Board();
        this.board.setZoneId(ConfigMgr.getInstance().getConfig(user.getUserName()).zoneId);

    }

    public Board getBoard() {
        return board;
    }

    public CommonMsg.YoAccountToken getAccountToken() {
        CommonMsg.YoAccountToken.Builder accessToken = CommonMsg.YoAccountToken.newBuilder().setOpenId(getOpenId()).setAccessToken("robot");
        return accessToken.build();
    }

    protected boolean isSkipNewbie() {
        return false;
    }

    @Override
    public void run() {
        super.run();
        runFlow(new GetPlayerListFlow());
        runFlow(new LoginOrCreatePlayerFlow(), isSkipNewbie());
        waitState("waitLoginFinish", () -> {
            if (getBoard().getSceneId() == 0) {
                return false;
            }
            if (getBoard().playerProp == null) {
                return false;
            }
            return getBoard().cityProp != null;
        });
    }
}

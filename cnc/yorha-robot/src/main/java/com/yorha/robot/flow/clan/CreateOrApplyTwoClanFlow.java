package com.yorha.robot.flow.clan;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.scene.ClanScene;

/**
 * 平分到两个联盟里  135  246
 *
 * <AUTHOR>
 */
public class CreateOrApplyTwoClanFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        String clanNamePrefix = robot.getRobotName();
        // 联盟编号
        int clanNum = robot.getRobotId() % 2;
        boolean isClanOwner = robot.getRobotId() == 0 || robot.getRobotId() == 1;
        ClanScene scene = RobotMgr.getInstance().getScene(robot.getUser(), ClanScene.class);
        // 已经有联盟了
        if (robot.getBoard().playerProp.getClan().getClanId() != 0) {
            if (isClanOwner) {
                scene.putClan(clanNum, robot.getBoard().playerProp.getClan().getClanId(), robot.getBoard().getPlayerProp().getScenePlayer().getMainCityId());
            }
            return;
        }
        if (isClanOwner) {
            String clanSName = ConfigMgr.getInstance().getConfig(robot.getUser().getUserName()).robotName.substring(0, 3) + String.valueOf(clanNum);
            robot.runFlow(new CreateClanFlow(), clanSName.substring(1), clanNamePrefix + clanNum);
            robot.waitState("waitClanProp", () -> robot.getBoard().playerProp.getClan().getClanId() != 0);
            scene.putClan(clanNum, robot.getBoard().playerProp.getClan().getClanId(), robot.getBoard().getPlayerProp().getScenePlayer().getMainCityId());
        } else {
            robot.waitState("waitJoinClan", () -> scene.getClanId(clanNum) > 0);
            robot.runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
            robot.waitState("waitJoinClanProp", () -> robot.getBoard().playerProp.getClan().getClanId() != 0);
        }
    }
}

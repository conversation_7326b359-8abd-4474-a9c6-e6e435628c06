package com.yorha.robot.flow.city;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.io.MsgType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerCommon.Player_VerifyMoveCity_S2C;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class MoveCityCheckFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerCommon.Player_VerifyMoveCity_C2S.Builder builder = PlayerCommon.Player_VerifyMoveCity_C2S.newBuilder();

        int x = (int) args[0];
        int y = (int) args[1];
        builder.setX(Math.max(x, 1)).setY(Math.max(y, 1));
        Player_VerifyMoveCity_S2C msg = (Player_VerifyMoveCity_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_VERIFYMOVECITY_C2S, builder.build());
        if (!ErrorCode.isOK(msg.getErrorCode())) {
            throw new RuntimeException(StringUtils.format("{} wrong end point:{}, cannot move city", robot, Point.valueOf(x, y)));
        }
    }
}

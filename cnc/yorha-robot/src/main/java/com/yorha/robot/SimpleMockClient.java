package com.yorha.robot;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.io.MsgType;
import com.yorha.common.io.ParseEngine;
import com.yorha.common.utils.ChannelHelper;
import com.yorha.common.utils.Pair;
import com.yorha.proto.*;
import com.yorha.robot.core.base.Robot;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.LengthFieldPrepender;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 后端开发期间自测协议的工具，不为复杂测试场景而生
 * <p>
 * 不会维护属性数据，通过查看client日志和server日志观察协议行为
 */
public class SimpleMockClient {
    protected static final Logger LOGGER = LogManager.getLogger(SimpleMockClient.class);

    public static class Single {
        protected Bootstrap bootstrap;
        protected Channel channel;
        protected int seqId;
        protected Pair<Integer, CompletableFuture<GeneratedMessageV3>> waiting = null;

        public void init(int svrPort) throws InterruptedException {
            initNettyClientEnv();
            ChannelFuture channelFuture = bootstrap.connect("localhost", svrPort).await();
            channel = channelFuture.channel();
        }

        public void initNettyClientEnv() {
            EventLoopGroup group = new NioEventLoopGroup(3, new ThreadFactoryBuilder().setNameFormat("io-%d").build());
            bootstrap = new Bootstrap();
            bootstrap.group(group);
            bootstrap.channel(NioSocketChannel.class);
            bootstrap.option(ChannelOption.SO_REUSEADDR, true);
            // 这是为了客户端主动断开连接后，不进入time_wait而直接释放端口，在反复建立大量连接的场景中需要用到
            bootstrap.option(ChannelOption.SO_LINGER, 0);
            bootstrap.handler(new ChannelInitializer<Channel>() {
                @Override
                protected void initChannel(Channel ch) {
                    ChannelPipeline p = ch.pipeline();
                    // 发消息处理器
                    p.addLast(new LengthFieldPrepender(4));
                    // 收消息处理器
                    p.addLast(new LengthFieldBasedFrameDecoder(Integer.MAX_VALUE, 0, 4, 0, 4));
                    p.addLast(new RecvHandler());
                }
            });
        }

        public void send(GeneratedMessageV3 req) {
            seqId++;
            int msgType = PROTO_MSG_2_MSG_ID.get(req.getClass());
            ByteBuf msgData = ParseEngine.getInstance().encodeMsg(msgType, seqId, null, req);
            LOGGER.info("send msg: {} seqId={} req:{}", req.getClass(), seqId, req);
            channel.writeAndFlush(msgData);
        }

        public void sendAndWait(GeneratedMessageV3 req) {
            send(req);
            int msgType = PROTO_MSG_2_MSG_ID.get(req.getClass());
            int retMsgId = MsgType.getRetMsgId(msgType);
            waiting = new Pair<>(retMsgId, new CompletableFuture<>());
            try {
                waiting.getSecond().get(5000, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                e.printStackTrace();
            } finally {
                waiting = null;
            }
        }

        public void login(String openId, long playerId, int zoneId) {
            CommonMsg.YoAccountToken accountToken = CommonMsg.YoAccountToken.newBuilder()
                    .setOpenId(openId)
                    .setAccessToken("robot")
                    .build();
            sendAndWait(User.GetRoleList_C2S_Msg.newBuilder()
                    .setAccountToken(accountToken)
                    .setClientInfo(CommonMsg.ClientInfo.newBuilder().setLanguage(CommonEnum.Language.zh).build())
                    .setZoneId(zoneId)
                    .build());
            sendAndWait(User.Login_C2S_Msg.newBuilder()
                    .setAccountToken(accountToken)
                    .setPlayerId(playerId)
                    .setZoneId(zoneId)
                    .setIsRegister(playerId == 0)
                    .build());
        }

        protected class RecvHandler extends SimpleChannelInboundHandler<ByteBuf> {
            @Override
            public void channelActive(ChannelHandlerContext ctx) {
                LOGGER.info("{} active", ctx.channel());
            }

            @Override
            public void channelInactive(ChannelHandlerContext ctx) {
                LOGGER.info("{} inactive.", ctx.channel());
            }

            @Override
            protected void channelRead0(ChannelHandlerContext ctx, ByteBuf msg) {
                int headerLength = msg.readShort();
                byte[] headerData = new byte[headerLength];
                msg.readBytes(headerData);

                try {
                    Core.CSHeader csHeader = Core.CSHeader.parseFrom(headerData);
                    byte[] bodyData = new byte[msg.readableBytes()];
                    msg.readBytes(bodyData);
                    byte[] msgData = ParseEngine.decodeMsg(csHeader.getFlag(), bodyData);

                    int msgType = csHeader.getType();
                    int code = csHeader.getCode().getId();

                    GeneratedMessageV3 response = Robot.parseMsg(msgType, msgData);
                    LOGGER.info("recv msg: {} {} code=[{}-{}] {}", msgType,
                            response == null ? null : response.getClass(), code, csHeader.getCode().getParamList(), response);
                    if (waiting != null && waiting.getFirst() == msgType) {
                        waiting.getSecond().complete(response);
                    }
                    ctx.flush();
                } catch (Exception e) {
                    LOGGER.error("recv msg handle error: ", e);
                }
            }

            @Override
            public void exceptionCaught(ChannelHandlerContext ctx, Throwable e) {
                LOGGER.error("exception caught. {} {}", channel, e);
                ChannelHelper.closeChannel(channel, "RobotChannelHandler exceptionCaught");
            }
        }

    }

    protected static Single single = new Single();
    protected static final Map<Class<? extends GeneratedMessageV3>, Integer> PROTO_MSG_2_MSG_ID = new HashMap<>();

    protected static void initAll() throws InterruptedException {
        initAll(8889);
    }

    protected static void initAll(int svrPort) throws InterruptedException {
        buildMsgMapping();
        single.init(svrPort);
    }

    protected static void buildMsgMapping() {
        MsgType.getMsgId2ProtoMsgMap().forEach((msgId, defaultInstance) -> PROTO_MSG_2_MSG_ID.put(defaultInstance.getClass(), msgId));
    }

    protected static void send(GeneratedMessageV3 req) {
        single.send(req);
    }

    protected static void sendAndWait(GeneratedMessageV3 req) {
        single.sendAndWait(req);
    }

    protected static void login(String openId, long playerId, int zoneId) {
        single.login(openId, playerId, zoneId);
    }
}

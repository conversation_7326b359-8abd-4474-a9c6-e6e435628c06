package com.yorha.robot.robotcase.stress.login;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 游戏服
 * 登录角色
 *
 * <AUTHOR>
 */
public class LoginPlayerRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(LoginPlayerRobot.class);

    public LoginPlayerRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void run() {
        runFlow(new GetPlayerListFlow());
        runFlow(new LoginFlow(), isSkipNewbie());
        end();
    }
}
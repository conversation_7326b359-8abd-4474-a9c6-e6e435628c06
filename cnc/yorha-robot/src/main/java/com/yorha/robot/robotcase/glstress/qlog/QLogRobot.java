package com.yorha.robot.robotcase.glstress.qlog;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.*;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 完整新手引导流程
 *
 * <AUTHOR>
 */
public class QLogRobot extends EnterWorldRobot {
    public QLogRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected boolean isSkipNewbie() {
        return true;
    }

    @Override
    protected void afterEnterBigScene() {
        // 延迟1~20秒，错峰
        this.realSleep(RandomUtils.nextInt(1000, 20000));
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i >= 0) {
            i++;
            sendQlogMsg(i);
            realSleep(RandomUtils.nextInt(1000, 2000));
        }
    }

    private void sendQlogMsg(int type) {
        if (type == 0) {
            PlayerCommon.Player_TriggerQlog_C2S.Builder b1 = PlayerCommon.Player_TriggerQlog_C2S.newBuilder();
            CommonEnum.QlogType q1 = CommonEnum.QlogType.valueOf("QT_NEWBIE");
            b1.setAction("{module:\"tutorials\", button:\"\", param_1:\"700002\", param_2:\"1\", param_3:\"true\" }")
                    .setType(q1);
            sendMsgToServerSync(MsgType.PLAYER_TRIGGERQLOG_C2S, b1.build());
        } else {
            PlayerCommon.Player_TriggerQlog_C2S.Builder b2 = PlayerCommon.Player_TriggerQlog_C2S.newBuilder();
            CommonEnum.QlogType q2 = CommonEnum.QlogType.valueOf("QT_NEWBIE");
            b2.setAction("{module:\"tutorials\", button:\"\", param_1:\"700038\", param_2:\"1\", param_3:\"true\" }")
                    .setType(q2);
            sendMsgToServerSync(MsgType.PLAYER_TRIGGERQLOG_C2S, b2.build());
        }
    }
}

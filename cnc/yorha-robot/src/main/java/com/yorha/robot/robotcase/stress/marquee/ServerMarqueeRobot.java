package com.yorha.robot.robotcase.stress.marquee;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.marquee.SendMarqueeFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 全服跑马灯
 *
 * <AUTHOR>
 */
public class ServerMarqueeRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ServerMarqueeRobot.class);

    public ServerMarqueeRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i > 0) {
            runFlow(new SendMarqueeFlow(), "server");
            i--;
        }
        finished();
    }
}

package com.yorha.robot.robotcase.stress.view;

import com.yorha.common.enums.DirectionEnum;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 专门看某个区域的视野，来回切换
 *
 * <AUTHOR>
 */
public class SpecViewRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(SpecViewRobot.class);

    public SpecViewRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "Offset seconds=3600");
        }
        waitAllRobotLoginSuccess();

        realSleep(5000);

        realSleep(RandomUtils.nextInt(1000));

        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int i = 0;
        while (i < executeCount) {

            final PointProp cityPointProp = getBoard().cityProp.getPoint();
            Point centerPoint = Point.valueOf(cityPointProp.getX(), cityPointProp.getY());

            // 第几次视野
            DirectionEnum cur = DirectionEnum.values()[i % DirectionEnum.values().length];

            int layer = 2;
            int width = 30000;
            int height = 30000;

            List<StructPB.PointPB> pointList = genViewPointByCenter(centerPoint.getX(), centerPoint.getY(), cur, width, height);

            PlayerScene.Player_UpdateView_C2S.Builder msg = PlayerScene.Player_UpdateView_C2S.newBuilder();
            msg.setType(1);

            if (i % 2 == 0) {
                msg.setP1(pointList.get(0));
                msg.setP2(pointList.get(1));
                msg.setP3(pointList.get(2));
                msg.setP4(pointList.get(3));
            } else {
                msg.setP1(StructPB.PointPB.newBuilder().setX(48257).setY(64000).build());
                msg.setP2(StructPB.PointPB.newBuilder().setX(44000).setY(53000).build());
                msg.setP3(StructPB.PointPB.newBuilder().setX(62000).setY(53000).build());
                msg.setP4(StructPB.PointPB.newBuilder().setX(59000).setY(64000).build());
            }
            msg.setLayer(layer);
            LOGGER.debug("UpdateNormalViewFlow {}", msg);
            PlayerScene.Player_UpdateView_S2C rsp = (PlayerScene.Player_UpdateView_S2C) sendMsgToServerSync(MsgType.PLAYER_UPDATEVIEW_C2S, msg.build());

            this.realSleep(1000);
            i++;
        }
        end();
    }

    /**
     * 通过中心点和视野边长，生成视野的四个范围点
     */
    private List<StructPB.PointPB> genViewPointByCenter(int x, int y, DirectionEnum dir, int width, int height) {
        int deltaX = dir.getAddX();
        int deltaY = dir.getAddY();

        // 偏移中心点
        x += deltaX * width * 2;
        y += deltaY * height * 2;

        List<StructPB.PointPB> pointList = new ArrayList<>();
        pointList.add(StructPB.PointPB.newBuilder().setX(x - width / 2).setY(y + height / 2).build());
        pointList.add(StructPB.PointPB.newBuilder().setX(x + width / 2).setY(y + height / 2).build());
        pointList.add(StructPB.PointPB.newBuilder().setX(x - width / 2).setY(y - height / 2).build());
        pointList.add(StructPB.PointPB.newBuilder().setX(x + width / 2).setY(y - height / 2).build());
        return pointList;
    }
}

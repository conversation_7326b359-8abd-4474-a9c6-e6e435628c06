package com.yorha.robot.robotcase.stress.clan;

import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.clan.CreateClanFlow;
import com.yorha.robot.flow.clan.ModifyClanFlagFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ModifyClanNameAndFlagRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(SearchClanRobot.class);

    public ModifyClanNameAndFlagRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=5 count=9999999");
        // 每个玩家都创建一个军团
        if (getBoard().getPlayerProp().getClan().getClanId() == 0) {
            int clanNum = getRobotId();
            String clanSName = String.valueOf(clanNum + 10000);
            clanSName = clanSName.substring(1, 5);
            String clanNamePrefix = getRobotName();
            String clanName = clanNamePrefix + clanNum;
            LOGGER.info("CreateClanFlow sName:{} name:{}", clanSName, clanName);
            ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
            runFlow(new CreateClanFlow(), clanSName, clanName);
            waitState("waitClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
            scene.putClan(clanNum, getBoard().playerProp.getClan().getClanId(), getBoard().getPlayerProp().getScenePlayer().getMainCityId());
            waitState("waitAllClanReady", () -> scene.getClanCount() >= RobotMgr.getInstance().getAllRobot(getUser()).size());
        }

        int runTimes = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (runTimes-- > 0) {
            // 改旗帜
            runFlow(new ModifyClanFlagFlow(), runTimes % 7 + 1);
        }
        finished();
    }
}

package com.yorha.robot.robotcase.stress.clan;

import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.QueueTasksProp;
import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * 快捷军团帮助压力测试
 */
public class ClanHelpRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ClanHelpRobot.class);

    public ClanHelpRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    private long getTaskIdByTaskType(CommonEnum.QueueTaskType type) {
        QueueTasksProp queueTasksProp = getBoard().playerProp.getQueueTaskMap().get(type.getNumber());
        if (queueTasksProp != null && queueTasksProp.getQueueTaskList().size() != 0) {
            return queueTasksProp.getQueueTaskList().get(0).getTaskId();
        }
        return 0L;
    }


    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        // 除了建造中心都升级到最高
        CncFlowUtil.sendDebugCommand(this, "SpecificInnerBuildToMax buildIdMin=102 buildIdMax=132");
        // 建造中心随机升级到2 - 20级
        int targetMainBuildingLevel = RandomUtils.nextInt(20) + 1;
        if (targetMainBuildingLevel != 1) {
            CncFlowUtil.sendDebugCommand(this, "LevelUpBuilding type=101 level=" + targetMainBuildingLevel);
        }
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 发放升级建筑需要用的资源
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=1 count=999999999");
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=2 count=999999999");
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=3 count=999999999");
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=4 count=999999999");
        CncFlowUtil.sendDebugCommand(this, "AddCurrency currencyType=5 count=100000");

        runFlow(new CreateOrApplyClanFlow());
        scene.addAlreadyJoinClanPlayer();
        waitState("waitAllPlayerJoinClan", () -> scene.getAlreadyJoinClanPlayer() >= RobotMgr.getInstance().getAllRobot(getUser()).size());

        final int mainBuildingType = 101;
        int runTimes = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        boolean needCancelTask = true;
        
        scene.addAlreadyFinishHelpPlayer();
        finished();
    }
}

package com.yorha.robot.core.base;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.io.MsgType;
import com.yorha.common.io.ParseEngine;
import com.yorha.common.concurrent.NamedRunnableWithId;
import com.yorha.common.exception.GeminiErrorCodeManager;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.RandomUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.robot.core.SendAndGetMsg;
import com.yorha.robot.core.User;
import com.yorha.robot.core.handler.MessageHandler;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.BooleanSupplier;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * <p>
 * firstStart --> 发起连接session
 * secondStart --> 连接成功回调
 * end --> robot业务搞定，发起断开
 * over --> 断开回调
 */

public class Robot {
    private static final Logger LOGGER = LogManager.getLogger(Robot.class);
    protected Map<Integer, Consumer<GeneratedMessageV3>> s2cOnCall = new HashMap<>();
    private final LongAdder number = new LongAdder();

    private volatile Channel channel;

    private final int robotId;
    private String openId;
    private final String robotName;

    private SendAndGetMsg syncCall;

    private final Queue<Message> queue = new ConcurrentLinkedQueue<>();
    private final List<String> flowNameList = new LinkedList<>();
    private String curFlowName;
    private final User user;

    public Robot(String robotName, Integer robotId, User user) {
        this.robotId = robotId;
        this.user = user;
        this.robotName = robotName;
    }

    public String getDeviceId() {
        return robotName + robotId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public int getRobotId() {
        return robotId;
    }

    /**
     * @return 第几个机器人
     */
    public int getRobotIndex() {
        return robotId - ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotStartIndex;
    }

    public String getOpenId() {
        if (StringUtils.isEmpty(openId)) {
            return getDeviceId();
        }
        return openId;
    }

    public String getRobotName() {
        return robotName;
    }

    @Override
    public String toString() {
        return "user: " + user.getUserName() + " " + getOpenId();
    }

    public void firstStart() {

        try {
            connectZone1();
            run();
        } catch (Exception e) {
            end();
        }
    }

    public void setStart() {
        this.isEnd = false;
    }

    public void connectDir() {
        boolean b = RandomUtils.nextBoolean();
        if (b) {
            connectServerSync("***********", 8888);
        } else {
            connectServerSync("************", 8889);
        }
    }

    public void connectZone1() {
        Config config = ConfigMgr.getInstance().getConfig(this.user.getUserName());
        String host = "127.0.0.1";
        int port = 8930;
        if (config != null) {
            host = config.host;
            port = config.port;
        }
        connectServerSync(host, port);
    }

    /**
     * 同步连接服务器
     */
    public void connectServerSync(String host, int port) {
        connectTsNs = SystemClock.nanoTimeNative();
        RobotMgr.getFlowStats(user).onC2S("connect");
        Bootstrap bootstrap = RobotMgr.getInstance().getBootstrap(user);
        Config config = ConfigMgr.getInstance().getConfig(user.getUserName());
        // 连接服务器
        ChannelFuture future = bootstrap.connect(host, port);
        try {
            future.get(30, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            LOGGER.error("connectServerSync fail, ", e);
            throw new GeminiException("Robot connectServerSync ", e);
        }
        LOGGER.debug("{} connect success", this);
        long cost = SystemClock.nanoTimeNative() - connectTsNs;
        RobotMgr.getFlowStats(getUser()).onS2C("connect", cost);
        channel = future.channel();
        if (channel == null) {
            throw new GeminiException("ciao channel is null");
        }
        MessageHandler.getInstance().onRobotBindChannel(channel, this);
    }

    /**
     * 同步断开服务器
     */
    public void disconnectServerSync() {
        LOGGER.debug("{} disconnectServerSync", this);
        disconnectTsNs = SystemClock.nanoTimeNative();
        RobotMgr.getFlowStats(getUser()).onC2S("disconnect");
        if (channel == null) {
            return;
        }
        ChannelFuture future = channel.disconnect();
        try {
            future.get(5, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            LOGGER.error("disconnectServerSync fail, ", e);
            throw new GeminiException("Robot disconnectServerSync ", e);
        }
        LOGGER.debug("{} disconnect success", this);
        long cost = SystemClock.nanoTimeNative() - disconnectTsNs;
        User user = getUser();
        RobotMgr.getFlowStats(user).onS2C("disconnect", cost);
        MessageHandler.getInstance().unbindChannel(channel, this);
        channel = null;
    }

    public User getUser() {
        if (user == null) {
            throw new GeminiException("robot: {} user not exist", robotName);
        }
        return user;
    }

    private long connectTsNs = 0;

    private long disconnectTsNs = 0;

    public void run() {
        LOGGER.debug("{} run", this);
    }

    /**
     * 成功完成
     */
    public void finished() {
        RobotMgr.getInstance().getRobotMgrDispatcher(getUser()).execute(NamedRunnableWithId.valueOf(0, "robot-finish", () -> {
            // RobotMgr.getInstance().onRobotFinished(getRobotId());
        }));
    }

    public void end() {
        disconnectAndEnd();
    }

    private boolean isEnd = false;

    public void disconnectAndEnd() {
        if (channel != null) {
            disconnectServerSync();
        }
        realEnd();
    }

    public void realEnd() {
        if (isEnd) {
            return;
        }
        RobotMgr.getInstance().onRobotEnd(user);
        isEnd = true;
    }

    public void waitState(String waitName, BooleanSupplier func) {
        waitState(waitName, func, ConfigMgr.getInstance().getConfig(getUser().getUserName()).defaultWaitTime);
    }

    public void waitState(String waitName, BooleanSupplier func, long waitMillis) {
        User user = getUser();
        RobotMgr.getFlowStats(user).onC2S(waitName);
        final long startTs = SystemClock.nanoTimeNative();
        LOGGER.debug("wait [{}] start", waitName);
        boolean ret = waitState(func, waitMillis);
        LOGGER.debug("wait [{}] finish, ret={}", waitName, ret);
        if (!ret) {
            // 说明没等到指定的消息
            throw new RuntimeException(StringUtils.format("{} wait {} fail", this, waitName));
        }
        RobotMgr.getFlowStats(user).onS2C(waitName, SystemClock.nanoTimeNative() - startTs);
    }

    private boolean waitState(BooleanSupplier func, long waitMillis) {
        int count = (int) (waitMillis / 100);
        while (count > 0) {
            // 每次停止0.1秒
            realSleep(100);
            handleMsgInQueue();
            if (func.getAsBoolean()) {
                return true;
            }
            count--;
        }
        return false;
    }

    public void realSleep(long t) {
        try {
            Thread.sleep(t);
        } catch (InterruptedException e) {
            LOGGER.error("", e);
        }
        handleMsgInQueue();
    }

    public void runFlow(BaseFlow baseFlow, Object... args) {
        String flowName = baseFlow.getClass().getSimpleName();
        setNewFlow(flowName);
        baseFlow.run(this, args);
        setNewFlow(null);
        LOGGER.debug("{} {} ok", this, baseFlow.getClass().getSimpleName());
    }

    public static GeneratedMessageV3 parseMsg(int msgType, byte[] msgData) {
        try {
            GeneratedMessageV3 msgInstance = MsgType.getMsgFromType(msgType);
            return msgInstance.getParserForType().parseFrom(msgData);
        } catch (Exception e) {
            LOGGER.error("parseMsg failed, type={}, e={}", msgType, e);
        }
        return null;
    }

    /**
     * 跑在io线程
     */
    public void onRecvMsg(Message msg) {
        LOGGER.debug("{} recv msg {} call:{}", this, msg, syncCall);
        if (syncCall != null) {
            if (syncCall.checkFinish(msg)) {
                return;
            }
        }
        LOGGER.debug("{} add {} into queue", this, msg.getMsgType());
        // 如果超出队列上限，抛异常
        queue.add(msg);
    }

    /**
     * 异步发送消息
     *
     * @return 返回等待的消息seqId
     */
    public int sendMsgToServerAsync(int msgType, GeneratedMessageV3 msg) {
        LOGGER.debug("{} send msg type:{}", this, MsgType.getMsgFromType(msgType).getClass().getSimpleName());
        number.increment();
        int seqId = number.intValue();
        if (seqId == 0) {
            LOGGER.error("sendMsgToServerAsync seqId is 0  long:{}", number.longValue());
        }
        ByteBuf msgData = ParseEngine.getInstance().encodeMsg(msgType, seqId, null, msg);
        if (channel.isActive()) {
            channel.writeAndFlush(msgData);
        }
        return seqId;
    }

    /**
     * 同步发送消息
     * 对异步的封装
     *
     * @return 返回的消息
     */
    public GeneratedMessageV3 sendMsgToServerSync(int msgType, GeneratedMessageV3 msg) {
        return sendMsgToServerSync(msgType, msg, ConfigMgr.getInstance().getConfig(getUser().getUserName()).sendMsgMaxWaitTime * 1000);
    }

    public GeneratedMessageV3 sendMsgToServerSync(int msgType, GeneratedMessageV3 msg, long timeout) {
        String flowName = "send msg " + MsgUtils.getMsgNameFromCsMsgType(msgType);
        User user = getUser();
        AtomicReference<Long> startTs = new AtomicReference<>();
        syncCall = new SendAndGetMsg(() -> {
            startTs.set(SystemClock.nanoTimeNative());
            RobotMgr.getFlowStats(user).onC2S(flowName);
            return sendMsgToServerAsync(msgType, msg);
        });
        Message rspMsg = null;
        int seqId = -1;
        try {
            rspMsg = syncCall.run(timeout, TimeUnit.MILLISECONDS);
            seqId = syncCall.getWaitMsgSeqId();
        } catch (Exception e) {

            LOGGER.error("{} send {} timeout {} {} {}", this, msgType, startTs.get(),   SystemClock.nowZonedDateTime(), timeout, e);
        } finally {
            String ret = flowName;
            if (rspMsg == null) {
                ret = flowName + "-fail";
            }
            RobotMgr.getFlowStats(user).onS2C(ret, SystemClock.nanoTimeNative() - startTs.get());
            syncCall = null;
        }
        if (rspMsg == null) {
            LOGGER.error("{} handle {} timeout", this, curFlowName);
            return null;
        }
        if (!ErrorCode.isOK(rspMsg.getCode())) {
            LOGGER.error("{} msg:{} errorcode:{} fail", this, rspMsg.getMsgType(), GeminiErrorCodeManager.getGeminiErrorCode(rspMsg.getCode().getId()));
            throw new GeminiException(rspMsg.getCode().getId());
        }
        if (msgType != rspMsg.getMsgType() - 10000) {
            LOGGER.error("response msg error. sendType:{} sendSeqId:{} rspType: {} rspSeqId: {}", msgType, seqId, rspMsg.getMsgType(), rspMsg.getSeqId());
        }
        handleMsgInQueue();
        return rspMsg.getRealMsg();
    }

    private void handleMsgInQueue() {
        // 处理这段时间收到的ntf
        while (!queue.isEmpty()) {
            Message ntfMsg = queue.poll();
            if (s2cOnCall.containsKey(ntfMsg.getMsgType())) {
                s2cOnCall.get(ntfMsg.getMsgType()).accept(ntfMsg.getRealMsg());
            }
        }
    }

    private void setNewFlow(String newFlowName) {
        this.curFlowName = newFlowName;
        if (newFlowName == null) {
            return;
        }
        this.flowNameList.add(newFlowName);
    }

    private String getCurFlowName() {
        return this.curFlowName;
    }

    /**
     * dump出当前状态
     */
    public void dumpState() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(this).append("\n");
        stringBuilder.append("flow list:").append(flowNameList).append("\n");
        stringBuilder.append("cur flow:").append(getCurFlowName()).append("\n");
        stringBuilder.append("queue:").append(Arrays.toString(queue.toArray())).append("\n");
        LOGGER.error(stringBuilder);
    }

    public int flowCount() {
        return flowNameList.size();
    }
}

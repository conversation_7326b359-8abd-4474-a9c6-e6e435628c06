package com.yorha.robot;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.robot.config.RobotConfig;
import com.yorha.robot.core.manager.RobotMgr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RobotApp {
    private static final Logger LOGGER = LogManager.getLogger(RobotApp.class);

    /**
     * 压力测试，机器人跑完结束进程
     */
    public static void main(String[] args) throws InterruptedException {
        LOGGER.info("start robot app");
        ErrorCode.justLoadCode();
        // 为了触发static代码块
        //LOGGER.info("MAX_MSG_TYPE:{}", MsgType.MAX_MSG_TYPE);

        // 初始化机器人
        RobotMgr.getInstance().init("press", args, RobotConfig.class, new HashMap<>());
    }

    /**
     * 辅助测试机器人，进程常驻
     */
    public static String launchWebRobot(String userName, String[] args, Map<String, Object> robotArgsMap) {
        LOGGER.info("launchWebRobot");
        ErrorCode.justLoadCode();
        // 为了触发static代码块
        //LOGGER.info("MAX_MSG_TYPE:{}", MsgType.MAX_MSG_TYPE);
        RobotMgr myRobotMgr = RobotMgr.getInstance();
        if (myRobotMgr.isOverLimit(userName)) {
            return "too many user, wait for a moment";
        }
        // 初始化机器人
        myRobotMgr.init(userName, args, RobotConfig.class, robotArgsMap);
        return "ok";
    }
}

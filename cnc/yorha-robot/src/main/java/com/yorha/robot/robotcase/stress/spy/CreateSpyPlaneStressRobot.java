package com.yorha.robot.robotcase.stress.spy;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.PlayerSpyPlaneProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerAirForce;
import com.yorha.proto.StructPB;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.robotcase.EnterWorldRobot;

import java.util.Map;

/**
 * 创建侦察机探索迷雾用例
 * zeo
 */
public class CreateSpyPlaneStressRobot extends EnterWorldRobot {
    public CreateSpyPlaneStressRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    private int indexX = 1;
    private int indexY = 1;

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        realSleep(5000);
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        returnHome();
        while (i > 0) {
            i--;
            int random = i % 4;
            realSleep(RandomUtils.nextInt(30000) + 3000);
            if (random == 0) {
                createExploreFog();
                continue;
            }

            if (random == 3) {
                changeExploreFog();
                continue;
            }

            if (random == 2) {
                changeSpy();
                continue;
            }

            returnHome();
        }
        finished();
    }

    protected void createExploreFog() {
        PlayerAirForce.Player_CreateSpyPlane_C2S.Builder builder = PlayerAirForce.Player_CreateSpyPlane_C2S.newBuilder();
        int count = 0;
        boolean finishNeedReturn = false;
        if (indexX % 2 == 0) {
            finishNeedReturn = true;
        }
        for (Map.Entry<Long, PlayerSpyPlaneProp> entry : getBoard().getPlayerProp().getPlayerPlaneModel().getPlayerSpyPlane().entrySet()) {
            if (entry.getValue().getStatus() == CommonEnum.PlaneStatus.PS_PLANE_IDLE) {
                builder.getSpyInfoBuilder()
                        .setActionType(CommonEnum.SpyPlaneActionType.SPAT_EXPLORE)
                        .setFinishNeedReturn(finishNeedReturn)
                        .setSpyPlaneId(entry.getKey())
                        .setPoint(StructPB.PointPB.newBuilder().setX(indexX * 50).setY(indexY * 50).build())
                        .setSrcPoint(StructPB.PointPB.newBuilder().setX(indexX * 50).setY(indexY * 50).build())
                        .build();
                sendMsgToServerSync(MsgType.PLAYER_CREATESPYPLANE_C2S, builder.build());
                count++;
            }
        }
        //LOGGER.info("PlayerId:{} exploreFog:{}, num:{}", getBoard().getPlayerId(), indexX, count);
        indexX++;
        if (indexX > 10) {
            indexY++;
            indexX = 1;
        }
    }

    protected void returnHome() {
        int count = 0;
        PlayerAirForce.Player_ChangeActionSpyPlane_C2S.Builder builder = PlayerAirForce.Player_ChangeActionSpyPlane_C2S.newBuilder();
        for (Map.Entry<Long, PlayerSpyPlaneProp> entry : getBoard().getPlayerProp().getPlayerPlaneModel().getPlayerSpyPlane().entrySet()) {
            if (entry.getValue().getStatus() != CommonEnum.PlaneStatus.PS_PLANE_IDLE) {
                builder.getSpyInfoBuilder()
                        .setActionType(CommonEnum.SpyPlaneActionType.SPAT_RETURN)
                        .setSpyPlaneId(entry.getKey())
                        .build();
                sendMsgToServerSync(MsgType.PLAYER_CHANGEACTIONSPYPLANE_C2S, builder.build());
                count++;
            }
        }
        //LOGGER.info("PlayerId:{} returnHome num:{}", getBoard().getPlayerId(), count);
    }

    protected void changeExploreFog() {
        PlayerAirForce.Player_ChangeActionSpyPlane_C2S.Builder builder = PlayerAirForce.Player_ChangeActionSpyPlane_C2S.newBuilder();
        int count = 0;
        boolean finishNeedReturn = false;
        if (indexX % 2 == 0) {
            finishNeedReturn = true;
        }
        for (Map.Entry<Long, PlayerSpyPlaneProp> entry : getBoard().getPlayerProp().getPlayerPlaneModel().getPlayerSpyPlane().entrySet()) {
            if (entry.getValue().getStatus() == CommonEnum.PlaneStatus.PS_PLANE_OUT) {
                builder.getSpyInfoBuilder()
                        .setActionType(CommonEnum.SpyPlaneActionType.SPAT_EXPLORE)
                        .setFinishNeedReturn(finishNeedReturn)
                        .setSpyPlaneId(entry.getKey())
                        .setPoint(StructPB.PointPB.newBuilder().setX(indexX * 50).setY(indexY * 50).build())
                        .setSrcPoint(StructPB.PointPB.newBuilder().setX(indexX * 50).setY(indexY * 50).build())
                        .build();
                sendMsgToServerSync(MsgType.PLAYER_CHANGEACTIONSPYPLANE_C2S, builder.build());
                count++;
            }
        }
        //LOGGER.info("PlayerId:{} changeExploreFog:{}, num:{}", getBoard().getPlayerId(), indexX, count);
        indexX++;
        if (indexX > 10) {
            indexY++;
            indexX = 1;
        }
    }

    protected void changeSpy() {
        PlayerAirForce.Player_ChangeActionSpyPlane_C2S.Builder builder = PlayerAirForce.Player_ChangeActionSpyPlane_C2S.newBuilder();
        int count = 0;

        EnterWorldRobot robot = (EnterWorldRobot) RobotMgr.getInstance().getAllRobot(getUser()).stream().findAny().get();

        long cityId = robot.getBoard().getMainCityId();
        for (Map.Entry<Long, PlayerSpyPlaneProp> entry : getBoard().getPlayerProp().getPlayerPlaneModel().getPlayerSpyPlane().entrySet()) {
            if (entry.getValue().getStatus() != CommonEnum.PlaneStatus.PS_PLANE_IDLE) {
                builder.getSpyInfoBuilder()
                        .setActionType(CommonEnum.SpyPlaneActionType.SPAT_SPY)
                        .setSpyPlaneId(entry.getKey())
                        .setTargetId(cityId)
                        .build();
                sendMsgToServerSync(MsgType.PLAYER_CHANGEACTIONSPYPLANE_C2S, builder.build());
                count++;
            }
        }
    }


}

package com.yorha.robot.core.base;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.Options;

/**
 * <AUTHOR>
 */
public class Config {
    public String host;
    public int port;
    /**
     * 是否分析flow耗时
     */
    public boolean statistics;
    public int robotNum;
    public String robotName;
    public boolean randomAccount;
    public String robotType;
    public int timeout;
    public int defaultWaitTime;
    /**
     * 客户端版本号
     */
    public String clientVersion;
    /**
     * 一批创建N个机器人后停顿 createIntervalTime ,再继续下一批次的机器人
     */
    public int robotNumInOneBatch;
    /**
     * 创建机器人的间隔时间
     */
    public int createIntervalTime;
    /**
     * 机器人的逻辑线程数量
     */
    public int robotLogicThreadNum;
    /**
     * 机器人的io线程数量
     */
    public int robotIOThreadNum;
    /**
     * 是否登录账号下已有的角色
     */
    public boolean loginExistedPlayer;
    /**
     * 发送消息，最长容忍时间 秒
     */
    public long sendMsgMaxWaitTime;
    /**
     * 执行轮数
     */
    public int executeRound;
    /**
     * 无视协议异常
     */
    public boolean ignoreWhenException;

    public int cycleTimes;

    /**
     * 机器人开始index
     */
    public int robotStartIndex = 0;

    public int zoneId = 1;

    public static Options options = new Options();

    static {
        options.addOption("c", "config", true, "config path");
        options.addOption("h", "host", true, "server host");
        options.addOption("p", "port", true, "server port");
        options.addOption("n", "num", true, "robot num");
        options.addOption("u", "user", true, "robot user name");
        options.addOption("t", "type", true, "robot case path");
        options.addOption("s", "statistics", true, "is open statistics");
        options.addOption("o", "timeout", true, "close time");
        options.addOption("rsi", "robotStartIndex", true, "robot start index");
        options.addOption("l", "loginExistedPlayer", true, "loginExistedPlayer");
    }

    public void resetByCmd(CommandLine commandLine) {
        if (commandLine.hasOption('h')) {
            host = commandLine.getOptionValue('h');
        }
        if (commandLine.hasOption('p')) {
            port = Integer.parseInt(commandLine.getOptionValue('p'));
        }
        if (commandLine.hasOption('n')) {
            robotNum = Integer.parseInt(commandLine.getOptionValue('n'));
        }

        if (commandLine.hasOption('u')) {
            robotName = commandLine.getOptionValue('u');
        }
        if (commandLine.hasOption('t')) {
            robotType = commandLine.getOptionValue('t');
        }
        if (commandLine.hasOption('s')) {
            statistics = Boolean.parseBoolean(commandLine.getOptionValue('s'));
        }
        if (commandLine.hasOption('o')) {
            timeout = Integer.parseInt(commandLine.getOptionValue('o'));
        }
        if (commandLine.hasOption("rsi")) {
            robotStartIndex = Integer.parseInt(commandLine.getOptionValue("rsi"));
        }
        if (commandLine.hasOption("l")) {
            loginExistedPlayer = Boolean.parseBoolean(commandLine.getOptionValue("l"));
        }
    }
}

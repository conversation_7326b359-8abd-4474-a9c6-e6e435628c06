package com.yorha.robot.flow.friend;

import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.FriendPlayerProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.PlayerFriend;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class RandomAddFriendFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(RandomAddFriendFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        boolean random = (boolean) args[0];
        long playerId;
        // 随机加好友
        if (random) {
            Set<Integer> keySet = new HashSet<>(robot.getUser().getRobotMap().keySet());
            keySet.remove(robot.getRobotId());
            int robotId = RandomUtils.randomSet(keySet);
            CncRobot targetRobot = (CncRobot) robot.getUser().getRobotMap().get(robotId);
            playerId = targetRobot.getBoard().getPlayerId();
            while (!keySet.isEmpty() && this.notValid(robot, playerId)) {
                keySet.remove(robotId);
                if (keySet.isEmpty()) {
                    break;
                }
                robotId = RandomUtils.randomSet(keySet);
                targetRobot = (CncRobot) robot.getUser().getRobotMap().get(robotId);
                playerId = targetRobot.getBoard().getPlayerId();
            }
            if (this.notValid(robot, playerId)) {
                // 所有人都是该robot的好友
                LOGGER.info("FriendPress randomAddFriendFlow, player {} can not find legal player", robot.getBoard().getPlayerId());
                return;
            }
        } else {
            playerId = (long) args[1];
        }

        LOGGER.debug("FriendedPress randomAddFriendFlow, random={}, player1 {} apply friend player2 {}", random, robot.getBoard().getPlayerId(), playerId);
        final int zoneId = robot.getBoard().getZoneId();
        PlayerFriend.Player_AddFriend_C2S.Builder friendApply = PlayerFriend.Player_AddFriend_C2S.newBuilder();
        friendApply.setPlayerId(playerId).setZoneId(zoneId);
        try {
            robot.sendMsgToServerSync(MsgType.PLAYER_ADDFRIENDAPPLY_C2S, friendApply.build());
        } catch (GeminiException e) {
            int code = e.getCodeId();
            if (code >= 10000) {
                LOGGER.info("FriendPress randomAddFriendFlow occur expected exception, playerId={}, e={}", playerId, e.getMessage());
            } else {
                LOGGER.error("FriendPress randomAddFriendFlow occur unexpected exception, playerId={}, e={}", playerId, e.getMessage());
            }
        }
    }

    private boolean notValid(CncRobot robot, long playerId) {
        FriendPlayerProp friendPlayerProp = robot.getBoard().getPlayerProp().getFriendPlayer();
        boolean isFriend = friendPlayerProp.getFriendList().containsKey(playerId);
        boolean hasShield = friendPlayerProp.getShiledList().containsKey(playerId);
        boolean hasApplied = friendPlayerProp.getWaitList().containsKey(playerId);
        LOGGER.debug("FriendPress checkValid, playerId={}, isFriend={}, hasShield={}, hasApplied={}", playerId, isFriend, hasShield, hasApplied);
        return isFriend || hasShield || hasApplied;
    }
}

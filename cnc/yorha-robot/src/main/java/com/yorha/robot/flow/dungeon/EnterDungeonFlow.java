package com.yorha.robot.flow.dungeon;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructMsg;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class EnterDungeonFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        final int hero1 = (int) args[0];
        final int hero2 = (int) args[1];
        final int soldierId = (int) args[2];
        final int num = (int) args[3];
        final int dungeonId = (int) args[4];
        PlayerScene.Player_EnterExpedition_C2S.Builder builder = PlayerScene.Player_EnterExpedition_C2S.newBuilder();
        StructMsg.BriefTroop.Builder troop = StructMsg.BriefTroop.newBuilder();
        troop.setMainHeroId(hero1).setDeputyHeroId(hero2).putTroop(soldierId, num);

        builder.setDungeonId(dungeonId).putTroop(1, troop.build());
        robot.sendMsgToServerSync(MsgType.PLAYER_ENTEREXPEDITION_C2S, builder.build());
    }
}

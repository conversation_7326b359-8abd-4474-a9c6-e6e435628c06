package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.PlayerScene.Player_SearchResource_S2C;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class SearchResBuildingFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerScene.Player_SearchResource_C2S.Builder builder = PlayerScene.Player_SearchResource_C2S.newBuilder();
        builder.setCurrencyType(CommonEnum.CurrencyType.forNumber((int) args[0])).setLevel((int) args[1]);
        Player_SearchResource_S2C ans = (Player_SearchResource_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_SEARCHRESOURCE_C2S, builder.build());
        robot.getBoard().setSearchResBuildingId(ans.getEntityId());
    }
}

package com.yorha.robot.scene;

import com.yorha.robot.core.AbstractRobotScene;
import com.yorha.robot.core.manager.ConfigMgr;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 登陆用例共享数据
 *
 * <AUTHOR>
 */

public class LoginScene extends AbstractRobotScene {
    private final AtomicInteger loginCount = new AtomicInteger();
    /**
     * 联盟id
     */
    private final Map<Integer, Long> idMap = new ConcurrentHashMap<>();

    public boolean allRobotLogin(String userName) {
        return loginCount.get() >= ConfigMgr.getInstance().getConfig(userName).robotNum;
    }

    public boolean allRobotLogout() {
        return loginCount.get() == 1;
    }

    public void markRobotLogin(int robotId, long playerId) {
        loginCount.incrementAndGet();
        idMap.put(robotId, playerId);
    }

    public void markRobotLogout() {
        loginCount.decrementAndGet();
    }

    /**
     * 计数器
     */
    private final AtomicInteger count = new AtomicInteger();

    public boolean checkCountFinished(int num) {
        return count.get() >= num;
    }

    public void addCount() {
        count.incrementAndGet();
    }

    public int getCount() {
        return count.get();
    }

    public long getRobotPlayerId(int robotId) {
        return idMap.getOrDefault(robotId, 0L);
    }
}

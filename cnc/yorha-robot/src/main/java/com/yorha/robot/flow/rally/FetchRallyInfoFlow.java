package com.yorha.robot.flow.rally;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerRally;
import com.yorha.proto.PlayerRally.Player_QueryRallyList_S2C;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 拉取集结列表 并拉取第一个集结的详情
 *
 * <AUTHOR>
 */
public class FetchRallyInfoFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(FetchRallyInfoFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerRally.Player_QueryRallyList_C2S.Builder builder = PlayerRally.Player_QueryRallyList_C2S.newBuilder();
        Player_QueryRallyList_S2C rspMsg = (Player_QueryRallyList_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_QUERYRALLYLIST_C2S, builder.build());

        List<StructPlayerPB.RallyInfoPB> dataList = rspMsg.getRallyList().getDatasList();
        if (!dataList.isEmpty()) {
            long rallyId = dataList.getFirst().getRallyId();
            PlayerRally.Player_QueryOneRally_C2S.Builder builder2 = PlayerRally.Player_QueryOneRally_C2S.newBuilder();
            builder2.setRallyId(rallyId);
            robot.sendMsgToServerSync(MsgType.PLAYER_QUERYONERALLY_C2S, builder2.build());
            LOGGER.debug("{} fetch first rally. rallyId :{}", robot, rallyId);
        }
    }
}

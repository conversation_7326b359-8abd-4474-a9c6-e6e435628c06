package com.yorha.robot.robotcase.stress.mail;

import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.mail.SendZoneMailFlow;
import com.yorha.robot.flow.user.KeepAliveFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;


/**
 * 在线接收全服邮件用例
 *
 * <AUTHOR>
 */

public class StressZoneMailRobot extends EnterWorldRobot {
    public StressZoneMailRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        realSleep(5000);
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        int executeCount = config.executeRound;
        for (int i = 0; i < executeCount; i++) {
            realSleep(1000);
            int index = i % config.robotNum;
            if (getRobotId() == index) {
                runFlow(new SendZoneMailFlow());
            } else {
                runFlow(new KeepAliveFlow());
            }
        }

        end();
    }
}

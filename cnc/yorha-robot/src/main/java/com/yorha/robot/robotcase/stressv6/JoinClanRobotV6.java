package com.yorha.robot.robotcase.stressv6;

import com.yorha.robot.core.User;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.clan.*;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class JoinClanRobotV6 extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(com.yorha.robot.robotcase.stress.clan.JoinClanRobot.class);

    public JoinClanRobotV6(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePowerSimple");
        }
//        waitAllRobotLoginSuccess();

        // 盟主去建盟 剩下的人加入
        runFlow(new CreateOrApplyClanFlow());
        end();
    }
}

package com.yorha.robot.robotcase.glstress.dir;

import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Robot;
import com.yorha.robot.flow.dir.GetZoneListFlow;
import com.yorha.robot.flow.user.CreatePlayerFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 导量推荐
 *
 * <AUTHOR>
 */
public class DriveTrafficRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(DriveTrafficRobot.class);

    public DriveTrafficRobot(String robotName, Integer robotId, User user) {
        super(robotN<PERSON>, robotId, user);
    }

    /**
     * 重试可能性
     */
    static final int RETRY_PERCENTAGE = 20;
    /**
     * 最少重试次数
     */
    static final int MIN_RETRY_TIME = 2;
    /**
     * 最多重试次数
     */
    static final int MAX_RETRY_TIME = 3;

    /**
     * 最少等待秒
     */
    static final int MIN_SLEEP_SEC = 4;

    /**
     * 最多等待秒
     */
    static final int MAX_SLEEP_SEC = 6;

    @Override
    public void firstStart() {
        try {
            int hardwareLevel = RandomUtils.nextInt(5);
            this.getBoard().setHardwareLevel(hardwareLevel);
            connectRandDir(this);
            runFlow(new GetZoneListFlow());
            disconnectServerSync();
            // 20%的可能重试
            if (RandomUtils.isSuccessByPercentage(RETRY_PERCENTAGE)) {
                // 重试的有50%可能重试3次
                final int retryTime = RandomUtils.nextInt(MIN_RETRY_TIME, MAX_RETRY_TIME + 1);
                for (int i = 0; i < retryTime; i++) {
                    int sleepSec = RandomUtils.nextInt(MIN_SLEEP_SEC, MAX_SLEEP_SEC + 1);  // 等一会儿(4-6s)
                    realSleep(TimeUtils.second2Ms(sleepSec));
                    connectRandDir(this);
                    runFlow(new GetZoneListFlow());
                    disconnectServerSync();
                }
            }
            // 连接dir指定zone
            connectServerSync(this.getBoard().getIp(), this.getBoard().getPort());
            runFlow(new GetPlayerListFlow());
            runFlow(new CreatePlayerFlow(), isSkipNewbie());
            disconnectServerSync();
            // 完成
        } catch (Exception e) {
            LOGGER.error("DriveTrafficRobot firstStart fail, ", e);
        } finally {
            disconnectAndEnd();
        }

    }

    // josefren: 目前都是硬编码，出机器人包前需考虑修改此处
    public static void connectRandDir(Robot robot) {
        Pair<String, Integer> address = RandomUtils.randomList(getAllDirAddress());
        robot.connectServerSync(address.getFirst(), address.getSecond());
    }


    private static final List<Pair<String, Integer>> DIR_ADDRESS_LIST = new ArrayList<>();

    public static List<Pair<String, Integer>> getAllDirAddress() {
        if (!DIR_ADDRESS_LIST.isEmpty()) {
            return DIR_ADDRESS_LIST;
        }
        DIR_ADDRESS_LIST.add(Pair.of("***********", 8888));
        DIR_ADDRESS_LIST.add(Pair.of("***********", 8889));
        DIR_ADDRESS_LIST.add(Pair.of("***********", 8890));
        return DIR_ADDRESS_LIST;
    }
}


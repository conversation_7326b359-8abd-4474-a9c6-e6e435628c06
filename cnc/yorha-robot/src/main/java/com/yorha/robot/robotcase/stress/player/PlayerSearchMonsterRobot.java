package com.yorha.robot.robotcase.stress.player;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 搜索野怪
 *
 * <AUTHOR>
 */
@RobotCase(
        name = "搜索野怪",
        desc = "执行一次搜索野怪"
)
public class PlayerSearchMonsterRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(PlayerSearchMonsterRobot.class);

    public PlayerSearchMonsterRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        waitAllRobotLoginSuccess();
        // 控制每秒大概1000个
        int interval = config.robotNum / 200;

        // 错开
        realSleep(5000 + RandomUtils.nextInt(interval) * 1000L);
        sendMsg();
        finished();
    }

    private void sendMsg() {
        PlayerCommon.Player_SearchMonster_C2S.Builder builder = PlayerCommon.Player_SearchMonster_C2S.newBuilder();
        builder.setLevel(1);
        sendMsgToServerSync(MsgType.PLAYER_SEARCHMONSTER_C2S, builder.build());
        LOGGER.debug("PlayerId:{} PlayerSearchMonsterRobot", getBoard().getPlayerId());
    }
}

package com.yorha.robot.robotcase.stressv6;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerAirForce;
import com.yorha.proto.StructPB;
import com.yorha.robot.core.User;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class ExploreNuclearRobot extends EnterWorldRobot {
    public ExploreNuclearRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        realSleep(5000 + RandomUtils.nextInt(30_000));

        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePowerSimple");
        }

        realSleep(30_000);

        PlayerAirForce.Player_CreateSpyPlane_C2S.Builder b1 = PlayerAirForce.Player_CreateSpyPlane_C2S.newBuilder();
        StructPB.PointPB.Builder p = StructPB.PointPB.newBuilder();
        p.setX(355660).setY(724600);
        b1.getSpyInfoBuilder()
                .setTargetId(70007)
                .setActionType(CommonEnum.SpyPlaneActionType.SPAT_SURVEY)
                .setPoint(p.build())
                .setFinishNeedReturn(true)
                .setSpyPlaneId(getBoard().getPlayerProp().getPlayerPlaneModel().getPlayerSpyPlane().keySet().iterator().next())
                .build();
        sendMsgToServerSync(MsgType.PLAYER_CREATESPYPLANE_C2S, b1.build());

        end();
    }
}

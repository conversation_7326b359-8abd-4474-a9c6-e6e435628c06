package com.yorha.robot.robotcase.stress.rank;

import com.yorha.robot.core.User;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.rank.FetchRankFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 拉取排行榜的机器人
 *
 * <AUTHOR>
 */
public class FetchRankRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(FetchRankRobot.class);

    public FetchRankRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        // 建造中心升至8级
        CncFlowUtil.sendDebugCommand(this, "LevelUpBuilding type=101 level=8");
        // 拉取排行榜
        runFlow(new FetchRankFlow());
    }
}

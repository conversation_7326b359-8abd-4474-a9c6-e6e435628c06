package com.yorha.robot.core.handler;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.common.io.ParseEngine;
import com.yorha.proto.Core;
import com.yorha.proto.Core.Code;
import com.yorha.robot.core.base.Message;
import com.yorha.robot.core.base.Robot;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class MessageHandler {
    private static final Logger LOGGER = LogManager.getLogger(MessageHandler.class);

    private static class SingletonClassInstance {
        private static final MessageHandler INSTANCE = new MessageHandler();
    }

    public static MessageHandler getInstance() {
        return MessageHandler.SingletonClassInstance.INSTANCE;
    }

    private final Map<Channel, Robot> channel2RobotMap = new ConcurrentHashMap<>();

    public void onRobotBindChannel(Channel channel, Robot robot) {
        channel2RobotMap.put(channel, robot);
    }

    public void unbindChannel(Channel channel, Robot robot) {
        if (channel == null) {
            return;
        }
        channel2RobotMap.remove(channel);
    }

    public void dispatchMsg(Channel channel, ByteBuf msg) {
        Robot robot = channel2RobotMap.get(channel);
        if (robot == null) {
            LOGGER.error("robot = null");
            return;
        }

        int headerLength = msg.readShort();
        byte[] headerData = new byte[headerLength];
        msg.readBytes(headerData);

        try {
            Core.CSHeader csHeader = Core.CSHeader.parseFrom(headerData);
            byte[] bodyData = new byte[msg.readableBytes()];
            msg.readBytes(bodyData);
            byte[] msgData = ParseEngine.decodeMsg(csHeader.getFlag(), bodyData);

            int msgType = csHeader.getType();
            Code code = csHeader.getCode();
            int seqId = csHeader.getSeqId();

            GeneratedMessageV3 generatedMessageV3 = Robot.parseMsg(msgType, msgData);
            Message newMsg = new Message(msgType, seqId, code, generatedMessageV3);

            robot.onRecvMsg(newMsg);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("decode msg header error:", e);
        } catch (Exception e) {
            LOGGER.error("robot:{}", robot, e);
        }

    }
}

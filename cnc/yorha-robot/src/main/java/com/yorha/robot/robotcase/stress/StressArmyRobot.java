package com.yorha.robot.robotcase.stress;

import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.flow.army.SearchWalkPathFlow;
import com.yorha.robot.flow.debug.AddManySoldierFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;

/**
 * 压力测试行军
 *
 * <AUTHOR>
 */
public class StressArmyRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(StressArmyRobot.class);

    public StressArmyRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i > 0) {
            doOnce();
            i--;
        }
        finished();
    }

    private void doOnce() {
        if (getRobotId() == 1) {
            CncFlowUtil.sendDebugCommand(this, "DebugSwitch type=createArmyNoSoldierLimit value=true");
        }
        waitAllRobotLoginSuccess();
        runFlow(new AddManySoldierFlow());

        int centerX = 12;
        int centerY = 248;
        int deltaX = getRobotId() * 0;
        int deltaY = getRobotId() * 0;
        int p = BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        Point moveArmyToPoint = Point.valueOf(centerX * p + deltaX, centerY * p + deltaY);

        runFlow(new SearchWalkPathFlow(), moveArmyToPoint);

        runFlow(new CreateArmyToAnyWhereFlow(), false, moveArmyToPoint, buildArmyTroop(this));
        waitState("armyCreateSuccess", () -> !getBoard().getMyArmies().isEmpty(), 30000);

        realSleep(1000);
        runFlow(new FastReturnAllMyArmyFlow());

        realSleep(1000);

        waitState("waitReturn", () -> {
            Set<Long> newMyArmy = getBoard().getMyArmyIds();
            if (newMyArmy.size() == 0) {
                return true;
            }
            return false;
        });
    }
}

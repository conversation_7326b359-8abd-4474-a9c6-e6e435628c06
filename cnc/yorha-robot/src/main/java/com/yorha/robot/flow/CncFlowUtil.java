package com.yorha.robot.flow;

import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;

/**
 * 总有你不想反复写的代码
 *
 * <AUTHOR>
 */
public class CncFlowUtil {

    public static void sendDebugCommand(CncRobot robot, String command) {
        PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();
        builder.setCommand(command);
        robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.build());
    }

    public static Point transPoint(PointProp pointProp) {
        return Point.valueOf(pointProp.getX(), pointProp.getY());
    }

}

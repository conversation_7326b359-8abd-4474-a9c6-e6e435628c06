package com.yorha.robot.robotcase.stressv5.player;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.player.SearchResBuildingFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class RandomSearchRobot extends EnterWorldRobot {

    public RandomSearchRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }
        waitAllRobotLoginSuccess();

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i > 0) {
//            int monsterLevel = (i % 8) + 1;
//            runFlow(new SearchMonsterFlow(), monsterLevel);
            realSleep(1000);
            int type = RandomUtils.nextInt(4) + 1;
            int level = RandomUtils.nextInt(6) + 1;
            runFlow(new SearchResBuildingFlow(), type, level);
            i--;
        }
        finished();
    }
}

package com.yorha.robot.robotcase.stressv5.player;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.scene.UpdateNormalViewFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class UpdateViewRobot extends EnterWorldRobot {
    public UpdateViewRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        this.realSleep(5000);

        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            this.realSleep(1000);
            int cnt = RandomUtils.nextInt(3) + 1;
            while (cnt-- > 0) {
                if (RandomUtils.nextBoolean()) {
                    runFlow(new UpdateNormalViewFlow(), i, 3, 57000, 27000);
                } else {
                    runFlow(new UpdateNormalViewFlow(), i, 5, 213000, 96000);
                }
            }
        }
        finished();
    }
}

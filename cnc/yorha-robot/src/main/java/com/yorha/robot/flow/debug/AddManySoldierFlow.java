package com.yorha.robot.flow.debug;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.flow.CncFlowUtil;

/**
 * 充足的兵力可以带来安全感
 *
 * <AUTHOR>
 */
public class AddManySoldierFlow implements Cnc<PERSON>low {
    @Override
    public void run(CncRobot robot, Object[] args) {
        CncFlowUtil.sendDebugCommand(robot, "AddSoldier type=addAll num=" + 10000000);
        CncFlowUtil.sendDebugCommand(robot, "GiveMePower");
    }
}

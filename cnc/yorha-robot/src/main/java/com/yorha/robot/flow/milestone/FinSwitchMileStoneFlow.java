package com.yorha.robot.flow.milestone;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.PlayerCommon.Player_DebugCommand_C2S;
import com.yorha.proto.PlayerMilestone;
import com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 完成并切换到下一里程碑
 *
 * <AUTHOR>
 */
public class FinSwitchMileStoneFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(FinSwitchMileStoneFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerMilestone.Player_GetMileStoneHistory_C2S.Builder ask = PlayerMilestone.Player_GetMileStoneHistory_C2S.newBuilder();
        Player_GetMileStoneHistory_S2C ans = (Player_GetMileStoneHistory_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_GETMILESTONEHISTORY_C2S, ask.build());
        int preMileStoneId = getCurMileStoneId(ans);

        Player_DebugCommand_C2S.Builder addProcess = Player_DebugCommand_C2S.newBuilder().setCommand("AddMileStoneProcess value=99999999");
        robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, addProcess.build());

        int curMileStoneId = getCurMileStoneId(ans);

        if (curMileStoneId == preMileStoneId) {
            PlayerCommon.Player_DebugCommand_C2S.Builder finMilestone = PlayerCommon.Player_DebugCommand_C2S.newBuilder().setCommand("FinMilestone");
            robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, finMilestone.build());
        }
    }

    /**
     * 获取进行中里程碑id
     */
    private int getCurMileStoneId(PlayerMilestone.Player_GetMileStoneHistory_S2C ans) {
        for (CommonMsg.MileStoneData mileStoneData : ans.getMileStoneDataMap().values()) {
            if (mileStoneData.getStatus() == CommonEnum.MileStoneStatus.MSS_PROCESSING) {
                return mileStoneData.getMileStoneId();
            }
        }
        LOGGER.error("getCurMileStoneId fail. ans:{}", ans);
        return 0;
    }
}
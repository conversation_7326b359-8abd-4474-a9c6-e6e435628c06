package com.yorha.robot.robotcase.glstress.login;

import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.KeepAliveFlow;
import com.yorha.robot.flow.user.LoginOrCreatePlayerFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 冷登录
 * 48000人，每次登陆后过维持心跳20秒，然后下线2分钟，再次登录
 */
public class ColdLogin extends EnterWorldRobot {

    public ColdLogin(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }


    @Override
    public void run() {
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i >= 0) {
            i++;
            Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
            connectServerSync(config.host, config.port);
            runFlow(new GetPlayerListFlow());
            runFlow(new LoginOrCreatePlayerFlow(), isSkipNewbie());
            // 每秒发个心跳包
            for (int j = 0; j < 20; j++) {
                runFlow(new KeepAliveFlow());
                this.realSleep(1000);
            }
            disconnectServerSync(); // 2、延迟1~20秒，错峰
            this.realSleep(120000);
        }
        finished();
    }

}

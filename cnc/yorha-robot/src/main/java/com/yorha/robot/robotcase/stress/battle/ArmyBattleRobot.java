package com.yorha.robot.robotcase.stress.battle;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Ring;
import com.yorha.game.gen.prop.ArmyProp;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.game.gen.prop.PlayerProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.CreateArmyToTargetFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.flow.army.SearchWalkPathFlow;
import com.yorha.robot.flow.scene.UpdateViewFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.BeautifulBattleScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 部队战斗压测
 *
 * <AUTHOR>
 */
public class ArmyBattleRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ArmyBattleRobot.class);

    public ArmyBattleRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    final boolean withView = true;
    // 0,1  2,3  4,5分别一组
    final int groupId = getRobotId() / 2;
    final int centerX = (224 + groupId * 10) * 1000;
    final int centerY = 204 * 1000;
    final int soldierNum = 7000;

    @Override
    protected void afterEnterBigScene() {
        if (getRobotId() == 0) {
            // 第一个开启下
            CncFlowUtil.sendDebugCommand(this, "SetIsBattleTestServer isOpen=1");
            runFlow(new DebugCmdFlow(), "SetIgnoreCrossingOwner isIgnore=1");
        }
        waitAllRobotLoginSuccess();

        try {
            if (getBoard().getCityProp().getLevel() == 1) {
                runFlow(new DebugCmdFlow(), "LevelUpBuilding type=101 level=25");
                runFlow(new DebugCmdFlow(), "LevelUpBuilding type=115 level=25");
                runFlow(new DebugCmdFlow(), "Hero type=hero subType=unlock value=0");
                runFlow(new DebugCmdFlow(), "Hero type=hero subType=upgrade value=0");
            }
            runFlow(new DebugCmdFlow(), StringUtils.format("AddSoldier type=addAll num={}", 1000000));
            runFlow(new DebugCmdFlow(), "ChangeMoveRatio ratio=1000");

            runFlow(new FastReturnAllMyArmyFlow());
            waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
        } catch (Exception e) {
            LOGGER.error("ciao", e);
            end();
            return;
        }

        realSleep(10000 + RandomUtils.nextInt(1000));

        // 循环创建行军
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        try {
            while (i > 0) {
                i--;

                if (getRobotId() % 2 == 0) {
                    // 进攻方
                    a();
                } else {
                    // 挨打方
                    b();
                }

            }
        } catch (Exception e) {
            LOGGER.error("ciao", e);
        }

        end();
    }

    private void a() {
        long enemyArmyId = 0;
        int count = 5;
        while (count > 0) {
            count--;
            // 找个敌人
            enemyArmyId = findEnemyId(this);
            if (enemyArmyId == 0) {
                realSleep(5_000);
            } else {
                // 找到啦
                break;
            }
        }

        if (enemyArmyId == 0) {
            // 还是没找到
            throw new GeminiException("886");
        }
        // 向目标战斗
        runFlow(new CreateArmyToTargetFlow(), true, CommonEnum.ArmyActionType.AAT_Battle, enemyArmyId, buildBattleArmyTroop(this, soldierNum, null));
        realSleep(20_000);

        try {
            // 可能已经失败回家中，可能已经到家
            runFlow(new FastReturnAllMyArmyFlow());
            waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
        } catch (Exception e) {
            // 无所谓
        }
    }

    /**
     * 在附近创建部队
     */
    private void b() {
        PointProp pointProp = getBoard().cityProp.getPoint();
        Ring circle = Ring.valueOf(pointProp.getX(), pointProp.getY(), 5000, 1000);
        Point point = null;
        for (int i = 0; i < 30; i++) {
            Point p = circle.getRandomPoint();
            try {
                runFlow(new SearchWalkPathFlow(), p);
            } catch (Exception e) {
                continue;
            }
            point = p;
            break;
        }
        if (point == null) {
            realSleep(2_000);
            return;
        }
        if (withView) {
            runFlow(new UpdateViewFlow(), 4, point, 1);
        }
        // 创建行军
        runFlow(new CreateArmyToAnyWhereFlow(), true, point, buildBattleArmyTroop(this, soldierNum, null));
        // 等行军到达
        waitState("waitArmyArrived", () -> isArrived(this), 20_000);
        BeautifulBattleScene scene = RobotMgr.getInstance().getScene(getUser(), BeautifulBattleScene.class);
        scene.markArmyArrived(getBoard().getFirstArmy().getFirst());
        // 等行军死回家
        waitState("b_wait_army_return", () -> getBoard().getMyArmyIds().isEmpty(), 9999_000);
    }

    public static long findEnemyId(CncRobot robot) {
        int targetRobotId = (robot.getRobotId() + 1) % ConfigMgr.getInstance().getConfig(robot.getUser().getUserName()).robotNum;
        CncRobot targetRobot = (CncRobot) RobotMgr.getInstance().findRobot(robot.getUser(), targetRobotId);
        if (targetRobot == null) {
            return 0;
        }
        Pair<Long, ArmyProp> firstArmy = targetRobot.getBoard().getFirstArmy();
        if (firstArmy != null) {
            return firstArmy.getFirst();
        }
        return 0;
    }

    public static boolean isArrived(CncRobot robot) {
        if (robot.getBoard().getMyArmyIds().isEmpty()) {
            return false;
        }
        CommonEnum.ArmyState armyState = robot.getBoard().getFirstArmy().getSecond().getArmyState();
        if (armyState == CommonEnum.ArmyState.AS_Staying) {
            return true;
        } else if (armyState == CommonEnum.ArmyState.AS_InBattle) {
            // 可能已经被别人打了！
            return true;
        }
        return true;
    }

    public static Point getMovePoint(int centerX, int centerY, int r) {
        Circle c = Circle.valueOf(centerX, centerY, r);
        return c.getRandomPoint();
    }

    public static StructPlayerPB.TroopPB buildBattleArmyTroop(CncRobot me, int num, List<Integer> specifySoldier) {
        StructPlayerPB.TroopPB.Builder builder = StructPlayerPB.TroopPB.newBuilder();
        PlayerProp playerProp = me.getBoard().getPlayerProp();

        Collection<PlayerHeroProp> myHeroes = playerProp.getPlayerHeroModel().getPlayerHeroMap().values();
        List<PlayerHeroProp> idleHeroes = myHeroes.stream()
                .filter(p -> p.getStar() > 1)
                .filter(p -> p.getState() == CommonEnum.HeroState.HERO_IDLE)
                .filter(p -> !isHeroAlreadyInArmy(me, p.getHeroId()))
                .collect(Collectors.toList());
        // Collections.shuffle(idleHeroes);
        // 英雄设置
        if (!idleHeroes.isEmpty()) {
            builder.setMainHero(StructPB.HeroPB.newBuilder().setHeroId(idleHeroes.get(0).getHeroId()).build());
        }
        if (idleHeroes.size() > 1) {
            builder.setDeputyHero(StructPB.HeroPB.newBuilder().setHeroId(idleHeroes.get(1).getHeroId()).build());
        }
        // 兵力设置
        if (specifySoldier != null) {
            int eachNum = Math.max(1, num / specifySoldier.size());
            playerProp.getScenePlayer().getSoldier().getSoldierMap().forEach((soldierId, mySoldier) -> {
                if (specifySoldier.contains(soldierId)) {
                    StructPB.SoldierPB soldierPb = StructPB.SoldierPB.newBuilder()
                            .setSoldierId(soldierId)
                            .setNum(Math.min(eachNum, mySoldier.getNum()))
                            .build();
                    builder.getTroopBuilder().putDatas(soldierId, soldierPb);
                }
            });
        } else {
            int eachNum = Math.max(1, num / playerProp.getScenePlayer().getSoldier().getSoldierMap().size());
            playerProp.getScenePlayer().getSoldier().getSoldierMap().forEach((soldierId, mySoldier) -> {
                StructPB.SoldierPB soldierPb = StructPB.SoldierPB.newBuilder()
                        .setSoldierId(soldierId)
                        .setNum(Math.min(eachNum, mySoldier.getNum()))
                        .build();
                builder.getTroopBuilder().putDatas(soldierId, soldierPb);
            });
        }

        return builder.build();
    }

    private static boolean isHeroAlreadyInArmy(CncRobot me, int heroId) {
        return me.getBoard().getMyArmies()
                .stream()
                .anyMatch(it -> it.getSecond().getTroop().getMainHero().getHeroId() == heroId
                        || it.getSecond().getTroop().getDeputyHero().getHeroId() == heroId);
    }

}

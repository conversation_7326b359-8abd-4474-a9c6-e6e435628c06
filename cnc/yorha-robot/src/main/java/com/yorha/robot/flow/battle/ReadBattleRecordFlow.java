package com.yorha.robot.flow.battle;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerMail;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 获取战斗报告邮件
 *
 * <AUTHOR>
 */
public class ReadBattleRecordFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(ReadBattleRecordFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        long mailId = (long) args[0];
        PlayerMail.Player_MailBatchRead_C2S.Builder builder1 = PlayerMail.Player_MailBatchRead_C2S.newBuilder();
        builder1.addMailList(mailId);
        robot.sendMsgToServerSync(MsgType.PLAYER_MAILBATCHREAD_C2S, builder1.build());
    }
}

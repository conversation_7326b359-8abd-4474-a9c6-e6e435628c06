package com.yorha.robot.simple;

import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerHospital;
import com.yorha.proto.PlayerSoldier;
import com.yorha.proto.PlayerTech;
import com.yorha.robot.SimpleMockClient;


/**
 * <AUTHOR>
 */
public class DengkeMockClient extends SimpleMockClient {
    public static void main(String[] args) throws InterruptedException {
        initAll(8889);
        String openId = "1dc2d779c2a445d5950e29344849969b";
        long playerId = 608180009L;
        int zoneId = 1;
        login(openId, playerId, zoneId);
        //barracksTest1();
        //artilleryTest1();
        //vehicleTest1();
        //tankTest1();
        //technologyCenterTest1();
        //serviceStationTest1();

        //barracksTest2();
        //artilleryTest2();
        //vehicleTest2();
        //tankTest2();
        //technologyCenterTest2();
        //serviceStationTest2();
    }

    private static void barracksTest1() {
        barracksUpgrade();
        trainFoot();
    }

    private static void barracksTest2() {
        trainFoot();
        barracksUpgrade();
    }

    private static void artilleryTest1() {
        artilleryFactoryUpgrade();
        trainArtillery();
    }

    private static void artilleryTest2() {
        trainArtillery();
        artilleryFactoryUpgrade();
    }

    private static void vehicleTest1() {
        vehicleFactoryUpgrade();
        trainVehicle();
    }

    private static void vehicleTest2() {
        trainVehicle();
        vehicleFactoryUpgrade();
    }

    private static void tankTest1() {
        tankFactoryUpgrade();
        trainTank();
    }

    private static void tankTest2() {
        trainTank();
        tankFactoryUpgrade();
    }

    private static void technologyCenterTest1() {
        technologyCenterUpgrade();
        research();
    }

    private static void technologyCenterTest2() {
        research();
        technologyCenterUpgrade();
    }

    private static void serviceStationTest1() {
        serviceStationUpgrade();
        treat();
    }

    private static void serviceStationTest2() {
        treat();
        serviceStationUpgrade();
    }

    // 兵营升级
    private static void barracksUpgrade() {
        //sendAndWait(PlayerInnerBuild.Player_UpLevelInnerBuild_C2S.newBuilder().setId(608180088).build());
    }

    // 火炮厂升级
    private static void artilleryFactoryUpgrade() {
        //sendAndWait(PlayerInnerBuild.Player_UpLevelInnerBuild_C2S.newBuilder().setId(608180092).build());
    }

    // 载具厂升级
    private static void vehicleFactoryUpgrade() {
        //sendAndWait(PlayerInnerBuild.Player_UpLevelInnerBuild_C2S.newBuilder().setId(608180100).build());
    }

    // 坦克厂升级
    private static void tankFactoryUpgrade() {
        //sendAndWait(PlayerInnerBuild.Player_UpLevelInnerBuild_C2S.newBuilder().setId(608180096).build());
    }

    // 医院升级
    private static void serviceStationUpgrade() {
        //sendAndWait(PlayerInnerBuild.Player_UpLevelInnerBuild_C2S.newBuilder().setId(608180278).build());
    }

    // 科技中心升级
    private static void technologyCenterUpgrade() {
        //sendAndWait(PlayerInnerBuild.Player_UpLevelInnerBuild_C2S.newBuilder().setId(608180085).build());
    }

    // 训练步兵
    private static void trainFoot() {
        sendAndWait(PlayerSoldier.Player_SoldierTrain_C2S.newBuilder().setSoldierId(1001).setNum(10).build());
    }

    // 训练火炮
    private static void trainArtillery() {
        sendAndWait(PlayerSoldier.Player_SoldierTrain_C2S.newBuilder().setSoldierId(3001).setNum(5).build());
    }

    // 训练载具
    private static void trainVehicle() {
        sendAndWait(PlayerSoldier.Player_SoldierTrain_C2S.newBuilder().setSoldierId(4001).setNum(2).build());
    }

    // 训练坦克
    private static void trainTank() {
        sendAndWait(PlayerSoldier.Player_SoldierTrain_C2S.newBuilder().setSoldierId(2001).setNum(2).build());
    }

    // 研发科技
    private static void research() {
        sendAndWait(PlayerTech.Player_ResearchTech_C2S.newBuilder().setTechSubId(1).build());
    }

    // 治疗伤兵
    private static void treat() {
        CommonMsg.HospitalTreatUnion hospitalTreatUnion = CommonMsg.HospitalTreatUnion.newBuilder().getDefaultInstanceForType();
        sendAndWait(PlayerHospital.Player_HospitalTreat_C2S.newBuilder().setTreatUnion(hospitalTreatUnion).build());
    }
}

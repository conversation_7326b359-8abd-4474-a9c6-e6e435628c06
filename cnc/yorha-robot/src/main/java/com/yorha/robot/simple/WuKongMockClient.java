package com.yorha.robot.simple;

import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.SimpleMockClient;

/**
 * <AUTHOR>
 */
public class WuKongMockClient extends SimpleMockClient {
    private static final String WUKONG_OPEN_ID = "713f19cf105f48dcb82854306bbb65ad";
    private static final long WUKONG_PLAYER_ID = 3883925513L;
    private static final int WUKONG_ZONE_ID = 1;

    public static void main(String[] args) throws InterruptedException {
        initAll(8894);
        int zoneId = 1;
        login(WUKONG_OPEN_ID, WUKONG_PLAYER_ID, WUKONG_ZONE_ID);
//        translate();
//        takeGiftCodeReward();
    }


    public static void gmMainCityUp() {
        sendAndWait(PlayerCommon.Player_DebugCommand_C2S.newBuilder()
                .setCommand("LevelUpBuilding type=101 level=2").build()
        );
    }

    public static void translate() {
        sendAndWait(PlayerChat.Player_TranslateText_C2S.newBuilder().setLanguage(CommonEnum.Language.en).setText("  我现在就是这样的人").build());
    }

//    public static void takeGiftCodeReward() {
//        sendAndWait(PlayerActivity.Player_GiftCodeTakeReward_C2S.newBuilder().setGiftCode("yorha1249").build());
//    }
}

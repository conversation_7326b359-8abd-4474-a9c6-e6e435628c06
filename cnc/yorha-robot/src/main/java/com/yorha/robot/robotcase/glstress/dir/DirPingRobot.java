package com.yorha.robot.robotcase.glstress.dir;

import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.dir.DirPingFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * ping dir测速
 *
 * <AUTHOR>
 */
public class DirPingRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(DirPingRobot.class);

    public DirPingRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    /**
     * 重试可能性
     */
    static final int WORK_PERCENTAGE = 20;


    @Override
    public void firstStart() {
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        if (i <= 0 && this.getRobotIndex() == 0) {
            LOGGER.error("DirPingRobot robotConfig error, executeRound <= 0");
            return;
        }
        while (i-- > 0) {
            if (RandomUtils.isSuccessByPercentage(WORK_PERCENTAGE)) {
                for (Pair<String, Integer> address : DriveTrafficRobot.getAllDirAddress()) {
                    this.connectServerSync(address.getFirst(), address.getSecond());
                    runFlow(new DirPingFlow());
                }
            }
            this.realSleep(TimeUtils.second2Ms(1));
        }
    }
}


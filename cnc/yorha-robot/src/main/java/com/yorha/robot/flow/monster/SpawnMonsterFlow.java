package com.yorha.robot.flow.monster;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 按照策划配置整个大地图刷新一波怪
 *
 * <AUTHOR>
 */
public class SpawnMonsterFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();
        String cmd = "GmSpawnMonster playerId=" + robot.getRobotId();
        builder.setCommand(cmd);
        robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.build());
    }
}

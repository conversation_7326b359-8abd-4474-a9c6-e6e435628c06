package com.yorha.robot.flow.player;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerFriend;
import com.yorha.proto.StructPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SearchPlayerFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(SearchPlayerFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        String name = (String) args[0];
        PlayerFriend.Player_SearchPlayerByName_C2S.Builder builder = PlayerFriend.Player_SearchPlayerByName_C2S.newBuilder();
        builder.setName(name);
        GeneratedMessageV3 res = robot.sendMsgToServerSync(MsgType.PLAYER_SEARCHPLAYERBYNAME_C2S, builder.build());
        PlayerFriend.Player_SearchPlayerByName_S2C ans = (PlayerFriend.Player_SearchPlayerByName_S2C) res;
        List<String> nameRet = new ArrayList<>();
        for (StructPB.PlayerCardInfoPB pb : ans.getCardListList()) {
            nameRet.add(pb.getCardHead().getName());
        }
        LOGGER.info("SearchPlayerFlow search={} {} result={}", name, nameRet.size(), String.join(" ", nameRet));
    }
}

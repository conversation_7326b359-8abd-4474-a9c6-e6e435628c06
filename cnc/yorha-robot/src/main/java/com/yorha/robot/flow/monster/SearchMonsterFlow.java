package com.yorha.robot.flow.monster;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 搜怪
 *
 * <AUTHOR>
 */
public class SearchMonsterFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        int monsterLevel = 1;
        if (args.length > 0) {
            monsterLevel = (int) args[0];
        }
        PlayerCommon.Player_SearchMonster_C2S.Builder builder = PlayerCommon.Player_SearchMonster_C2S.newBuilder();
        builder.setLevel(monsterLevel);
        robot.sendMsgToServerSync(MsgType.PLAYER_SEARCHMONSTER_C2S, builder.build());
    }
}

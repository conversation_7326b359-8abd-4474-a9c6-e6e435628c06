package com.yorha.robot.flow.city;

import com.yorha.common.io.MsgType;
import com.yorha.proto.*;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * @author: zpr
 * Date: 2023/9/11
 * Description:
 */
public class TargetMoveCityFlow implements Cnc<PERSON>low {

    @Override
    public void run(CncRobot robot, Object[] args) {
        int x = (int) args[0];
        int y = (int) args[1];

        robot.runFlow(new MoveCityCheckFlow(), x, y);
        PlayerCommon.Player_UseItem_C2S.Builder builder = PlayerCommon.Player_UseItem_C2S.newBuilder();
        builder.setItemKey(Long.parseLong(args[2].toString()));
        builder.setNum(1);
        builder.setParams(PlayerPB.ItemUseParamsPB.newBuilder().setPoint(StructPB.PointPB.newBuilder().setX(x).setY(y).build()).build());
        robot.sendMsgToServerSync(MsgType.PLAYER_USEITEM_C2S, builder.build());
    }
}

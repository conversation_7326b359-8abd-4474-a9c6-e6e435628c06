package com.yorha.robot.robotcase.stressv65;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.player.FeatureUnlockFlow;
import com.yorha.robot.flow.player.SendGmFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class MidasConsumeRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(MidasConsumeRobot.class);

    public MidasConsumeRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        runFlow(new FeatureUnlockFlow());

//        MidasScene scene = RobotMgr.getInstance().getScene(getUser(), MidasScene.class);
        // 不太重要，多发几次gm也没关系
        runFlow(new SendGmFlow(), "SwitchMidasStressTesting value=true");

        // 等一等
        realSleep(3000);

        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i-- > 0) {
            realSleep(RandomUtils.randomBetween(51, 100));
            runFlow(new SendGmFlow(), "ConsumeCurrency currencyType=5 count=100");
        }
        end();
    }

}

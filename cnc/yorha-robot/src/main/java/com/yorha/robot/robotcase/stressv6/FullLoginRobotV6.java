package com.yorha.robot.robotcase.stressv6;

import com.yorha.common.io.MsgType;
import com.yorha.proto.*;
import com.yorha.robot.core.User;
import com.yorha.robot.flow.scene.UpdateViewFlow;
import com.yorha.robot.flow.user.KeepAliveFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 完整的客户端登录，包含拉取各种协议
 */
public class FullLoginRobotV6 extends EnterWorldRobot {

    public FullLoginRobotV6(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        // Player_ReportMapVersion_C2S
        PlayerScene.Player_ReportMapVersion_C2S.Builder b1 = PlayerScene.Player_ReportMapVersion_C2S.newBuilder();
        b1.setMapId(1004).setVersion("1.0.81");
        sendMsgToServerSync(MsgType.PLAYER_REPORTMAPVERSION_C2S, b1.build());

        // Player_MailUnreadGet_C2S
        PlayerMail.Player_MailUnreadGet_C2S.Builder b2 = PlayerMail.Player_MailUnreadGet_C2S.newBuilder();
        sendMsgToServerSync(MsgType.PLAYER_MAILUNREADGET_C2S, b2.build());

        // Player_GetChatMessages_C2S
        PlayerChat.Player_GetChatMessages_C2S.Builder b3 = PlayerChat.Player_GetChatMessages_C2S.newBuilder();
        b3.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(CommonEnum.ChatChannel.CC_SERVER).setChatChannelId("1").build())
                .setFromId(0)
                .setToId(0)
                .setIsLogin(true);
        sendMsgToServerSync(MsgType.PLAYER_GETCHATMESSAGES_C2S, b3.build());

        // Player_TriggerQlog_C2S
        PlayerCommon.Player_TriggerQlog_C2S.Builder b9 = PlayerCommon.Player_TriggerQlog_C2S.newBuilder();
        CommonEnum.QlogType q = CommonEnum.QlogType.valueOf("QT_SEPARATE_DOWNLOAD");
        b9.setAction("download_completed").setType(q);
        sendMsgToServerSync(MsgType.PLAYER_TRIGGERQLOG_C2S, b9.build());

        if (getBoard().playerProp.getClan().getClanId() != 0) {
            String clanId = String.valueOf(getBoard().playerProp.getClan().getClanId());
            // Player_FetchClanPositionMark_C2S
            PlayerPositionMark.Player_FetchClanPositionMark_C2S.Builder b10 = PlayerPositionMark.Player_FetchClanPositionMark_C2S.newBuilder();
            sendMsgToServerSync(MsgType.PLAYER_FETCHCLANPOSITIONMARK_C2S, b10.build());
            // Player_FetchClanMemberCity_C2S
            PlayerClan.Player_FetchClanMemberCity_C2S.Builder b11 = PlayerClan.Player_FetchClanMemberCity_C2S.newBuilder();
            b11.setVersion(-1);
            sendMsgToServerSync(MsgType.PLAYER_FETCHCLANMEMBERCITY_C2S, b11.build());
            // Player_GetChatMessages_C2S
            PlayerChat.Player_GetChatMessages_C2S.Builder b12 = PlayerChat.Player_GetChatMessages_C2S.newBuilder();
            b12.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(CommonEnum.ChatChannel.CC_CLAN).setChatChannelId(clanId).build())
                    .setFromId(0)
                    .setToId(0)
                    .setIsLogin(true);
            sendMsgToServerSync(MsgType.PLAYER_GETCHATMESSAGES_C2S, b12.build());
        }
        

        // Player_TriggerQlog_C2S
        PlayerCommon.Player_TriggerQlog_C2S.Builder b14 = PlayerCommon.Player_TriggerQlog_C2S.newBuilder();
        CommonEnum.QlogType q1 = CommonEnum.QlogType.valueOf("QT_BSYSTEM");
        b14.setAction("{module:task_entrance, button:arrowButton_open, param_1:, param_2:}").setType(q1);
        sendMsgToServerSync(MsgType.PLAYER_TRIGGERQLOG_C2S, b14.build());


        // Player_UpdateView_C2S
        runFlow(new UpdateViewFlow());

        // KeepAlive_C2S_Msg
        runFlow(new KeepAliveFlow());

        end();
    }
}

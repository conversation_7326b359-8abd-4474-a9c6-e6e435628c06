package com.yorha.robot.robotcase.stressv83;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginOrCreatePlayerFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 吃patch包机器人
 * 登录，重置version，登出后登录
 */
public class PatchRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(PatchRobot.class);

    public PatchRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        LOGGER.info("PatchRobot PatchRobot start");
        // 获取随机的机甲配件
        // 给经验
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int limit = executeCount / 2;

        for (int i = 0; i < executeCount; i++) {
            realSleep(RandomUtils.nextInt(5000, 10000));
            LOGGER.info("PatchRobot PatchRobot begin");

            if (i > limit) {
                LOGGER.info("PatchRobot PatchRobot apply patch2");
                runFlow(new DebugCmdFlow(), "ResetPatchVersion version=1");
            } else {
                runFlow(new DebugCmdFlow(), "ResetPatchVersion version=2");
            }

            // 下线上线
            disconnectServerSync();

            Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
            connectServerSync(config.host, config.port);

            runFlow(new GetPlayerListFlow());
            runFlow(new LoginOrCreatePlayerFlow(), isSkipNewbie());
            LOGGER.info("PatchRobot PatchRobot over");
        }
        end();
    }


}

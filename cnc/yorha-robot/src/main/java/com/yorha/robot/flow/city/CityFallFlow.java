package com.yorha.robot.flow.city;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class CityFallFlow implements CncFlow {
    @Override
    public void run(Cnc<PERSON><PERSON>ot robot, Object[] args) {

        robot.sendMsgToServerSync(MsgType.PLAYER_CITYFALL_C2S, PlayerCommon.Player_CityFall_C2S.getDefaultInstance());
    }
}

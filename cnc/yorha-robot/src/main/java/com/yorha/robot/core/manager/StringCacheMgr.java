package com.yorha.robot.core.manager;

import com.yorha.common.utils.RandomUtils;

import java.util.HashMap;

public class StringCacheMgr {


    private static HashMap<Integer, String> randomStringMsg;

    public static void initDisplayDataCache() {
        randomStringMsg = new HashMap<>();
        StringBuilder display = new StringBuilder("YORHA");
        for (int i = 5; i <= 50; i++) {
            display.append("天");
            randomStringMsg.put(i, display.toString());
        }
    }

    public static String getChatMsg(int len) {
        if (len < 5 || len > 50) {
            return "error";
        }
        return randomStringMsg.get(len);
    }

    public static String getMaxLenChatMsg() {
        return randomStringMsg.get(50);
    }

    public static String getMaxLenChatMsgWithSomething() {
        return randomStringMsg.get(50) + RandomUtils.nextInt(1000000);
    }
}

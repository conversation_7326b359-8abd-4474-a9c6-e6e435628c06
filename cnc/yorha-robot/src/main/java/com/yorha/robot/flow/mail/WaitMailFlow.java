package com.yorha.robot.flow.mail;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerMail;
import com.yorha.proto.PlayerMail.Player_MailUnreadGet_S2C;
import com.yorha.proto.StructMailPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.flow.CncFlow;


/**
 * 等待接收邮件flow
 *
 * <AUTHOR>
 */


public class WaitMailFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        // 等所有邮件都收到
        robot.waitState("waitAllMailReceived", () -> {
            PlayerMail.Player_MailUnreadGet_C2S.Builder builder = PlayerMail.Player_MailUnreadGet_C2S.newBuilder();
            Player_MailUnreadGet_S2C rspMsg = (Player_MailUnreadGet_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_MAILUNREADGET_C2S, builder.build());
            return rspMsg.getCount().getDatasList().stream().mapToInt(StructMailPB.MailUnreadCountPB::getCount).sum() >= Constant.SEND_MAIL_NUM;
        });
    }
}

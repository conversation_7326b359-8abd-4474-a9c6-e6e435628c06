package com.yorha.robot.flow.marquee;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 发送跑马灯
 *
 * <AUTHOR>
 */
public class SendMarquee<PERSON>low implements Cnc<PERSON>low {

    @Override
    public void run(CncRobot robot, Object[] args) {
        String type = "clan";
        if (args.length > 0) {
            type = args[0].toString();
        }

        PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();
        String cmd = null;
        if ("player".equals(type)) {
            robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.setCommand("SendMarquee type=player").build());
        } else if ("clan".equals(type)) {
            // 军团长承载联盟跑马灯的发送任务
            if (robot.getBoard().getPlayerProp().getClan().getStaffId() == 1) {
                robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.setCommand("SendMarquee type=clan id=" + 0).build());
            }
        } else if ("server".equals(type)) {
            robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.setCommand("SendMarquee type=server").build());
        }
    }
}
package com.yorha.robot.robotcase.stress.battle;

import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.ChangeArmyFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.BeautifulBattleScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.robot.robotcase.stress.battle.ArmyBattleRobot.*;

/**
 * 部队战斗压测，无伤版本，配合enable_battle_no_cost_soldier一起食用
 *
 * <AUTHOR>
 */
public class SimpleArmyBattleRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(SimpleArmyBattleRobot.class);

    public SimpleArmyBattleRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        runFlow(new DebugCmdFlow(), "GiveMePower");
        runFlow(new DebugCmdFlow(), "ChangeMoveRatio ratio=1000");

        realSleep(1000);

        // 创建行军
        int centerX = 226 * 1000;
        int centerY = 194 * 1000;
        int soldierNum = 5000;

        // 创建行军
        Point movePoint = getMovePoint(centerX, centerY, 600);
        runFlow(new CreateArmyToAnyWhereFlow(), true, movePoint, buildBattleArmyTroop(this, soldierNum, null));
        // 等行军到达
        waitState("waitArmyArrived", () -> isArrived(this), 60000);
        BeautifulBattleScene scene = RobotMgr.getInstance().getScene(getUser(), BeautifulBattleScene.class);
        scene.markArmyArrived(getBoard().getFirstArmy().getFirst());

        waitState("waitAllArmyArrived", () -> scene.getArmyArrived().size() == RobotMgr.getInstance().getAllRobot(getUser()).size(), 60000);
        long enemyArmyId = findEnemyId(this);
        // 向目标战斗
        runFlow(new ChangeArmyFlow(), getBoard().getFirstArmy().getFirst(), CommonEnum.ArmyActionType.AAT_Battle, enemyArmyId, 0, 0);
    }
}

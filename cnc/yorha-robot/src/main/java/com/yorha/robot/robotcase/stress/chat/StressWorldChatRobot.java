package com.yorha.robot.robotcase.stress.chat;

import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.StringCacheMgr;
import com.yorha.robot.flow.chat.ChatFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 *
 * <AUTHOR>
 */

public class StressWorldChatRobot extends EnterWorldRobot {
    public StressWorldChatRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            stressFixMaxSendChat();
        }
        finished();
    }

    private void stressFixMaxSendChat() {
        String randomChatMsg = StringCacheMgr.getMaxLenChatMsg();
        runFlow(new ChatFlow(), CommonEnum.ChatChannel.CC_SERVER, randomChatMsg);
    }
}

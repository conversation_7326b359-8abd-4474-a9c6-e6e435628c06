package com.yorha.robot.scene;

import com.yorha.robot.core.AbstractRobotScene;

import java.util.concurrent.atomic.AtomicBoolean;


/**
 * <AUTHOR>
 */
public class MailScene extends AbstractRobotScene {

    private final AtomicBoolean mailSendDone = new AtomicBoolean(false);

    public void setMailSendDone(final boolean isDone) {
        mailSendDone.set(isDone);
    }


    public boolean isMailSendDone() {
        return mailSendDone.get();
    }
}
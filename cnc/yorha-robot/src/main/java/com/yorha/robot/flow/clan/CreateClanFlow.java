package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.Language;
import com.yorha.proto.PlayerClan.Player_CreateClan_C2S;
import com.yorha.proto.StructClanPB.ClanBaseInfoPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2021/08/17 16:55
 */
public class CreateClanFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateClanFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        ClanBaseInfoPB.Builder paramPb = ClanBaseInfoPB.newBuilder();
        paramPb.setSname(String.valueOf(args[0]))
                .setName(String.valueOf(args[1]))
                .setTerritoryColor(3)
                .setFlagColor(21)
                .setFlagSign(1)
                .setFlagShading(0)
                .setLanguage(Language.zh);
        if (args.length > 2) {
            paramPb.setRequire((CommonEnum.ClanEnterRequire) args[2]);
        }
        Player_CreateClan_C2S.Builder builder = Player_CreateClan_C2S.newBuilder();
        builder.setParam(paramPb);
        robot.sendMsgToServerSync(MsgType.PLAYER_CREATECLAN_C2S, builder.build());
    }
}

package com.yorha.robot.robotcase.multifunction;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ItemProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.robot.base.Constant;
import com.yorha.robot.bean.ChatArgs;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.core.manager.StringCacheMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.army.CreateArmyToAnyWhereFlow;
import com.yorha.robot.flow.army.CreateArmyToTargetFlow;
import com.yorha.robot.flow.army.FastReturnAllMyArmyFlow;
import com.yorha.robot.flow.chat.ChatFlow;
import com.yorha.robot.flow.city.RandomMoveCityFlow;
import com.yorha.robot.flow.clan.ApplyJoinClanFlow;
import com.yorha.robot.flow.clan.CreateClanFlow;
import com.yorha.robot.flow.clan.QuitClanFlow;
import com.yorha.robot.flow.player.FeatureUnlockFlow;
import com.yorha.robot.flow.player.SendGmFlow;
import com.yorha.robot.flow.scene.UpdateViewFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginOrCreatePlayerFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

import static com.yorha.common.enums.error.ErrorCode.*;
import static com.yorha.robot.robotcase.stress.battle.ArmyBattleRobot.buildBattleArmyTroop;

/**
 * 多功能机器人
 *
 * @author: yue
 */

@RobotCase(
        args = ChatArgs.class
)
public class MultifunctionRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(MultifunctionRobot.class);
    private long startTime;
    private long duration = 23 * 60 * 60 * 1000L;
    private int viewCount = 0;

    public MultifunctionRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        try {
            if (getRobotId() == 0) {
                runFlow(new DebugCmdFlow(), "FinMileStone");
                runFlow(new DebugCmdFlow(), "FinMileStone");
                runFlow(new DebugCmdFlow(), "FinMileStone");
                runFlow(new DebugCmdFlow(), "Offset seconds=3600");

                runFlow(new DebugCmdFlow(), "SetChatNoCd isNoCd=1");
                runFlow(new DebugCmdFlow(), "SetTextNoFilter textFilter=false");
                CncFlowUtil.sendDebugCommand(this, "SetIsBattleTestServer isOpen=1");
            }
            //完整登录
            fullLogin();
//            waitAllRobotLoginSuccess();
            realSleep(RandomUtils.nextInt(20_000));

            // 获取足够的迁城道具
            runFlow(new DebugCmdFlow(), StringUtils.format("AddItem templateId=10610001 count={}", 99999));
            realSleep(3000 + RandomUtils.nextInt(2000));
            // 解锁功能
            runFlow(new FeatureUnlockFlow());
            realSleep(3000 + RandomUtils.nextInt(2000));
            // 获取资源
            runFlow(new SendGmFlow(), "AddCurrency currencyType=5 count=10000");

            if (getBoard().getCityProp().getLevel() == 1) {
                runFlow(new DebugCmdFlow(), "LevelUpBuilding type=101 level=25");
                runFlow(new DebugCmdFlow(), "LevelUpBuilding type=115 level=25");
                runFlow(new DebugCmdFlow(), "Hero type=hero subType=unlock value=0");
                runFlow(new DebugCmdFlow(), "Hero type=hero subType=upgrade value=0");
                runFlow(new DebugCmdFlow(), StringUtils.format("AddSoldier type=addAll num={}", 1000000));
            }
            realSleep(3000 + RandomUtils.nextInt(2000));
            runFlow(new DebugCmdFlow(), "ChangeMoveRatio ratio=1000");

            realSleep(3000 + RandomUtils.nextInt(2000));

            runFlow(new FastReturnAllMyArmyFlow());
            waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
        } catch (Exception e) {
            LOGGER.error("ciao", e);
            end();
            return;
        }

        //开始记录
        this.startTime = SystemClock.now();
        int errorCount = 0;

        while (true) {
            try {
                /**
                 * 稳定压测机器人操作
                 * 5%申请/退出军团
                 * 5%登出后重新登录
                 * 5%迁城
                 * 10%聊天
                 * 15%创建部队乱跑
                 * 25%乱看
                 * 35%打野怪
                 */
                int r = RandomUtils.nextInt(100);
                if (r < 5) {
                    applyOrExitClan();
                } else if (r < 10) {
                    boolean flag = logout();
                    if (!flag) {
                        LOGGER.error("Multifunction Robot logout false kill----{}", getRobotId());
                        break;
                    }
                } else if (r < 15) {
                    randomMoveCity();
                } else if (r < 25) {
                    worldChat();
                } else if (r < 40) {
                    randomCreateArmyOrCancel();
                } else if (r < 100) {
                    monsterBattle();
                }
                errorCount = 0;
            } catch (Exception e) {
                LOGGER.error(e, e);
                errorCount++;
            }

            //连续错误50次,则终止机器人
            if (errorCount > 50) {
                LOGGER.info("error count > 50 robot exit robotId = {}", getRobotId());
                break;
            }

            //机器人压测时长
            if (SystemClock.now() - this.startTime > duration) {
                break;
            }

            realSleep(10000 + RandomUtils.nextInt(5000));
        }

        //测试结束
        end();
    }

    /**
     * 完整登录（FullLoginRobot）
     */
    private void fullLogin() {
        // Player_ReportMapVersion_C2S
        PlayerScene.Player_ReportMapVersion_C2S.Builder b1 = PlayerScene.Player_ReportMapVersion_C2S.newBuilder();
        b1.setMapId(1004).setVersion("1.1.28");
        sendMsgToServerSync(MsgType.PLAYER_REPORTMAPVERSION_C2S, b1.build());

        // Player_MailUnreadGet_C2S
        PlayerMail.Player_MailUnreadGet_C2S.Builder b2 = PlayerMail.Player_MailUnreadGet_C2S.newBuilder();
        sendMsgToServerSync(MsgType.PLAYER_MAILUNREADGET_C2S, b2.build());

        // Player_GiveNewbieHero_C2S
        PlayerCommon.Player_GiveNewbieHero_C2S.Builder b3 = PlayerCommon.Player_GiveNewbieHero_C2S.newBuilder();
        sendMsgToServerSync(MsgType.PLAYER_GIVENEWBIEHERO_C2S, b3.build());

        // Player_GetChatMessages_C2S
        PlayerChat.Player_GetChatMessages_C2S.Builder b4 = PlayerChat.Player_GetChatMessages_C2S.newBuilder();
        b4.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(CommonEnum.ChatChannel.CC_SERVER).setChatChannelId("1").build())
                .setFromId(0)
                .setToId(0)
                .setIsLogin(true);
        sendMsgToServerSync(MsgType.PLAYER_GETCHATMESSAGES_C2S, b4.build());

//        // AccountBindInfo_C2S_Msg
//        com.yorha.proto.User.AccountBindInfo_C2S_Msg.Builder b6 = com.yorha.proto.User.AccountBindInfo_C2S_Msg.newBuilder();
//        b6.setAccountToken(AuthUtils.buildYoAccountToken(getOpenId()));
//        sendMsgToServerSync(MsgType.ACCOUNTBINDINFO_C2S_MSG, b6.build());

        // Player_FetchClanTerritoryMap_C2S
        PlayerClan.Player_FetchClanTerritoryMap_C2S.Builder b7 = PlayerClan.Player_FetchClanTerritoryMap_C2S.newBuilder();
        b7.setVersion(0);
        sendMsgToServerSync(MsgType.PLAYER_FETCHCLANTERRITORYMAP_C2S, b7.build());
    }

    /**
     * 登出后重新登录（stressv65.FriendRobot）
     */
    private boolean logout() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        try {
            realSleep(10_000);
            disconnectServerSync();
            realSleep(20_000);
            connectServerSync(config.host, config.port);
        } catch (Exception e) {
            LOGGER.error("multifunctionRobot logout or login error", e);
            return false;
        }
        runFlow(new GetPlayerListFlow());
        runFlow(new LoginOrCreatePlayerFlow(), isSkipNewbie());
        waitState("waitLoginFinish", () -> {
            if (getBoard().getSceneId() == 0) {
                return false;
            }
            if (getBoard().playerProp == null) {
                return false;
            }
            if (getBoard().cityProp == null) {
                return false;
            }
            return true;
        });
        realSleep(2_000);
        //完整登录
        fullLogin();
        return true;
    }

    /**
     * 随机迁城（stressv65.RegisterAndMoveCityRobot）
     */
    private void randomMoveCity() {
        // 通过判断templateId获取迁城道具唯一id
        long itemUniqId = -1;
        for (Map.Entry<Long, ItemProp> itemProp : getBoard().playerProp.getItems().entrySet()) {
            if (itemProp.getValue().getTemplateId() == 10610001 || itemProp.getValue().getTemplateId() == 0) {
                itemUniqId = itemProp.getKey();
                break;
            }
        }
        if (itemUniqId == -1) {
            throw new RuntimeException(StringUtils.format("{} has no random move item", this));
        }
        // 深拷贝
        Point lastCityPoint = Point.valueOf(getBoard().cityProp.getPoint().getX(), getBoard().cityProp.getPoint().getY());
        // 使用随机迁城
        runFlow(new RandomMoveCityFlow(), itemUniqId);
        Point curCityPoint = Point.valueOf(getBoard().cityProp.getPoint().getX(), getBoard().cityProp.getPoint().getY());
        LOGGER.debug("last:{} cur:{}", lastCityPoint, curCityPoint);
    }

    /**
     * 随机派出行军移动和撤回（stressv5.army.ArmyOperationRobot）
     */
    private void randomCreateArmyOrCancel() {
        // 编队
        PointProp pointProp = getBoard().cityProp.getPoint();
        // 创建一只部队
        Point point = Point.valueOf(pointProp.getX() + 1000, pointProp.getY() + 1000);
        // 出发
        StructPlayerPB.TroopPB troopPB = buildArmyTroop(this, false, 500);
        if (troopPB.getMainHero().getHeroId() == 0) {
            return;
        }
        runFlow(new CreateArmyToAnyWhereFlow(), true, point, troopPB);
        // 记录
        waitState("waitArmyCreated", () -> getBoard().getMyArmyIds().size() > 0);
        realSleep(10_000);
        // 回家
        runFlow(new FastReturnAllMyArmyFlow());
        waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
    }

    /**
     * 全服聊天（worldChatRobot）
     */
    private void worldChat() {
        int r = RandomUtils.nextInt(20);
        if (r == 0) {
            // 拉取全服聊天最新1页，大概20条
            PlayerChat.Player_GetChatMessages_C2S.Builder b3 = PlayerChat.Player_GetChatMessages_C2S.newBuilder();
            b3.setChatSession(CommonMsg.ChatSession.newBuilder().setChannelType(CommonEnum.ChatChannel.CC_SERVER).setChatChannelId("1").build())
                    .setFromId(0)
                    .setToId(0)
                    .setIsLogin(true);
            sendMsgToServerSync(MsgType.PLAYER_GETCHATMESSAGES_C2S, b3.build());
        } else {
            String randomChatMsg = getUser().getArg(ChatArgs.class).content;
            if (StringUtils.isEmpty(randomChatMsg)) {
                randomChatMsg = StringCacheMgr.getMaxLenChatMsgWithSomething();
            }

            runFlow(new ChatFlow(), CommonEnum.ChatChannel.CC_SERVER, 1, randomChatMsg);
        }

    }

    /**
     * 申请或者退出军团（ApplyClanRobotV65）
     */
    private void applyOrExitClan() {
        // 联盟编号
        int clanNum = getRobotId() / Constant.CLAN_MEMBER_MAX;
        boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 已经有联盟了
        if (getBoard().playerProp.getClan().getClanId() != 0) {
            if (isClanOwner) {
                scene.putClan(clanNum, getBoard().playerProp.getClan().getClanId(), getBoard().getPlayerProp().getScenePlayer().getMainCityId());
                // 军团长处理所有申请
                PlayerClan.Player_FetchClanApplyPlayers_S2C ans = (PlayerClan.Player_FetchClanApplyPlayers_S2C)
                        sendMsgToServerSync(MsgType.PLAYER_FETCHCLANAPPLYPLAYERS_C2S, PlayerClan.Player_FetchClanApplyPlayers_C2S.getDefaultInstance());
                if (ans.getApplyMembers().getDatasCount() == 0) {
                    return;
                }
                for (Long id : ans.getApplyMembers().getDatasMap().keySet()) {
                    realSleep(2_000);
                    PlayerClan.Player_HandleClanApply_C2S.Builder builder = PlayerClan.Player_HandleClanApply_C2S.newBuilder();
                    builder.setIsAllow(true).setPlayerId(id);
                    sendMsgToServerSync(MsgType.PLAYER_HANDLECLANAPPLY_C2S, builder.build());
                    // 增加加入军团的玩家
                    scene.addAlreadyJoinClanPlayer();
                }
            } else {
                // 已进入军团成员退出军团
                runFlow(new QuitClanFlow());
                // 确认自己已退出军团
                waitState("waitQuitClanFinish", () -> getBoard().getPlayerProp().getClan().getClanId() == 0);
                // 记录自己已退出军团
                scene.decAlreadyJoinClanPlayer();
                realSleep(5_000);
                if (scene.getClanId(clanNum) != 0) {
                    // 开始申请
                    runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
                }
            }
        } else {
            String clanNamePrefix = getRobotName();
            if (isClanOwner) {
                String clanSName = String.valueOf(clanNum + 10000);
                clanSName = clanSName.substring(1, 5);
                String clanName = clanNamePrefix + clanNum;
                LOGGER.info("CreateOrWaitClanFlow sName:{} name:{}", clanSName, clanName);
                try {
                    runFlow(new CreateClanFlow(), clanSName, clanName, CommonEnum.ClanEnterRequire.VERIFY);
                    waitState("waitClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
                } catch (Exception e) {
                    //重名了
                    if (((GeminiException) e).getCodeId() == CLAN_DUPLICATE_SIMPLE_NAME.getCodeId()) {
                        String name = "a" + String.valueOf(RandomUtils.nextInt(500) + 500);
                        runFlow(new CreateClanFlow(), name, "clanNamePrefix" + name, CommonEnum.ClanEnterRequire.VERIFY);
                        waitState("waitClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
                    }
                    throw e;
                }
                scene.putClan(clanNum, getBoard().playerProp.getClan().getClanId(), getBoard().getPlayerProp().getScenePlayer().getMainCityId());
                // 记录军团长已加入军团
                scene.addAlreadyJoinClanPlayer();
            }
        }
    }

    /**
     * 打野（MonsterBattleRobot）
     */
    private void monsterBattle() {
        try {
            PlayerCommon.Player_SearchMonster_C2S.Builder builder = PlayerCommon.Player_SearchMonster_C2S.newBuilder();
            int monsterLv = Math.min(10, getBoard().getPlayerProp().getScenePlayer().getKillMonsterMaxLevel() + 1);
            builder.setLevel(monsterLv);
            //
            PlayerCommon.Player_SearchMonster_S2C sc = (PlayerCommon.Player_SearchMonster_S2C)
                    sendMsgToServerSync(MsgType.PLAYER_SEARCHMONSTER_C2S, builder.build());
            if (sc.hasMonsterId()) {
                try {
                    if (!getBoard().getMyArmyIds().isEmpty()) {
                        // 此时可能部队刚好回城不存在了，可能败退中，出现了就不再参与吧
                        runFlow(new FastReturnAllMyArmyFlow());
                        waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
                    }
                } catch (Exception e2) {
                    if (((GeminiException) e2).getCodeId() != ARMY_NOT_EXIT.getCodeId()) {
                        throw e2;
                    }
                }
                realSleep(2_000);
                long monsterId = sc.getMonsterId();
                runFlow(new UpdateViewFlow(), 4, Point.valueOf(sc.getBornPoint().getX(), sc.getBornPoint().getY()), 1);
                realSleep(2_000);
                // 打怪
                try {
                    StructPlayerPB.TroopPB troopPB = buildBattleArmyTroop(this, 2000, null);
                    if (troopPB.getMainHero().getHeroId() == 0) {
                        return;
                    }
                    runFlow(new CreateArmyToTargetFlow(), true, CommonEnum.ArmyActionType.AAT_Battle, monsterId, troopPB);
                    waitState("waitBattleEnd", () -> getBoard().getMyArmyIds().isEmpty(), 10_000);
                } catch (Exception e) {
                    try {
                        if (!getBoard().getMyArmyIds().isEmpty()) {
                            // 此时可能部队刚好回城不存在了，可能败退中，出现了就不再参与吧
                            runFlow(new FastReturnAllMyArmyFlow());
                            waitState("waitAllArmyReturn", () -> getBoard().getMyArmyIds().isEmpty());
                        }
                    } catch (Exception e2) {
                        if (((GeminiException) e2).getCodeId() != ARMY_NOT_EXIT.getCodeId()) {
                            throw e2;
                        }
                    }
                }

                if (getBoard().getPlayerProp().getEnergyModel().getEnergy() <= 500) {
                    runFlow(new DebugCmdFlow(), "AddPlayerEnergy energy=10000");
                    runFlow(new DebugCmdFlow(), StringUtils.format("AddSoldier type=addAll num={}", 200));
                }
            }

            realSleep(5_000);
        } catch (Exception e) {
            if (e instanceof GeminiException) {
                if (((GeminiException) e).getCodeId() == MONSTER_ENERGY_NOT_ENOUGH.getCodeId()) {
                    runFlow(new DebugCmdFlow(), "AddPlayerEnergy energy=10000");
                }
                if (((GeminiException) e).getCodeId() == SOLDIER_NUM_SHORTAGE.getCodeId()) {
                    runFlow(new DebugCmdFlow(), StringUtils.format("AddSoldier type=addAll num={}", 1000000));
                }
            }
            throw e;
        }
    }


}

package com.yorha.robot.flow.rank;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerRank;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 拉取排行榜
 * <p>
 * 目前游戏内已有的排行榜
 * RT_PLAYER_COLLECT_LEVEL
 * RT_CLAN_KILL
 * RT_CLAN_FLAG
 * RT_CLAN_POWER
 * RT_PLAYER_CITY_LEVEL
 * RT_PLAYER_KILL
 * RT_PLAYER_POWER
 *
 * <AUTHOR>
 */
public class FetchRankFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(FetchRankFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerRank.Player_GetTopRankInfo_C2S.Builder topRankBuilder = PlayerRank.Player_GetTopRankInfo_C2S.newBuilder();
        topRankBuilder.addRankIdList(1);
        topRankBuilder.addRankIdList(2);
        topRankBuilder.addRankIdList(3);
        topRankBuilder.addRankIdList(4);
        topRankBuilder.addRankIdList(5);
        topRankBuilder.addRankIdList(6);
        topRankBuilder.addRankIdList(7);
        robot.sendMsgToServerSync(MsgType.PLAYER_GETTOPRANKINFO_C2S, topRankBuilder.build());

        final int MAX_PAGE = 5;
        for (int page = 1; page <= MAX_PAGE; ++page) {
            PlayerRank.Player_GetRankPageInfo_C2S.Builder rankPageBuilder = PlayerRank.Player_GetRankPageInfo_C2S.newBuilder();
            rankPageBuilder.setRankId(4);
            rankPageBuilder.setPage(page);
            robot.sendMsgToServerSync(MsgType.PLAYER_GETRANKPAGEINFO_C2S, rankPageBuilder.build());
        }
    }
}

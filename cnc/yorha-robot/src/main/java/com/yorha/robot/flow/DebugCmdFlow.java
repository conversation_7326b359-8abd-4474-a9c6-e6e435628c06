package com.yorha.robot.flow;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2021/07/28 14:20
 */
public class DebugCmdFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(DebugCmdFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerCommon.Player_DebugCommand_C2S.Builder builder = PlayerCommon.Player_DebugCommand_C2S.newBuilder();
        String cmd = (String) args[0];
        builder.setCommand(cmd);
        robot.sendMsgToServerSync(MsgType.PLAYER_DEBUGCOMMAND_C2S, builder.build());
    }
}

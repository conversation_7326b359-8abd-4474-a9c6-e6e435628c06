package com.yorha.robot.robotcase.stress.player;

import com.yorha.robot.core.User;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.robotcase.stress.marquee.PlayerMarqueeRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 测试玩家内存
 *
 * <AUTHOR>
 */
public class PlayerMemoryRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(PlayerMarqueeRobot.class);

    public PlayerMemoryRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        // 使用gm命令
        CncFlowUtil.sendDebugCommand(this, "GiveMePower type=memory");
        LOGGER.info("PlayerId:{}", getBoard().getPlayerId());
        finished();
    }
}

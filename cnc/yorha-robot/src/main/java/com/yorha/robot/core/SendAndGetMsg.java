package com.yorha.robot.core;

import com.yorha.common.concurrent.FiberAsync;
import com.yorha.robot.core.base.Message;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class SendAndGetMsg extends FiberAsync<Message, Exception> {
    private static final Logger LOGGER = LogManager.getLogger(SendAndGetMsg.class);

    private final Supplier<Integer> runnable;
    private int waitMsgSeqId = -111;

    public SendAndGetMsg(Supplier<Integer> runnable) {
        super();
        this.runnable = runnable;
    }

    @Override
    protected void requestAsync() {
        this.waitMsgSeqId = runnable.get();
    }

    public boolean checkFinish(Message msg) {
        if (msg.getSeqId() == waitMsgSeqId) {
            if (msg.getSeqId() == 0) {
                LOGGER.error("checkFinish {} waitMsgSeqId: {}", msg, waitMsgSeqId);
            }
            asyncCompleted(msg);
            return true;
        }
        return false;
    }

    public int getWaitMsgSeqId() {
        return waitMsgSeqId;
    }
}

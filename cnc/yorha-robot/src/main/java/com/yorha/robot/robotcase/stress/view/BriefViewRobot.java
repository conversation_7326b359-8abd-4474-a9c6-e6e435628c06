package com.yorha.robot.robotcase.stress.view;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.scene.UpdateNormalViewFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class BriefViewRobot extends EnterWorldRobot {
    public BriefViewRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "FinMileStone");
            runFlow(new DebugCmdFlow(), "FinMileStone");
        }
        waitAllRobotLoginSuccess();

        if (getRobotId() == 0) {
            runFlow(new DebugCmdFlow(), "Offset seconds=3600");
        }

        realSleep(5000);

        realSleep(RandomUtils.nextInt(500));

        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int i = 0;
        while (i < executeCount) {
            runFlow(new UpdateNormalViewFlow(), i, 4, 212000, 96500);
            this.realSleep(1000);
            i++;
        }
        end();
    }
}

package com.yorha.robot.robotcase.stress;

import com.yorha.robot.core.User;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * oom测试
 *
 * <AUTHOR>
 */
public class OomTestRobot extends EnterWorldRobot {

    public OomTestRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        CncFlowUtil.sendDebugCommand(this, "GmOomDump size=10000");
        finished();
    }
}

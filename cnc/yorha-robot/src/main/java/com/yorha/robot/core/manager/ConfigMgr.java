package com.yorha.robot.core.manager;


import com.yorha.common.utils.FileUtils;
import com.yorha.robot.core.base.Config;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.ParseException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2021/08/03 14:10
 */
public class ConfigMgr {
    private static final Logger LOGGER = LogManager.getLogger(ConfigMgr.class);

    public static ConfigMgr getInstance() {
        return ConfigMgr.InstanceHolder.INSTANCE;
    }

    private static class InstanceHolder {
        private static final ConfigMgr INSTANCE = new ConfigMgr();
    }

    private final Map<String, Config> robotConfigMap = new ConcurrentHashMap<>();

    public Config getConfig(String userName) {
        return robotConfigMap.getOrDefault(userName, null);
    }

    public void createNewConfig(String userName, String fileName, CommandLine commandLine, Class<? extends Config> type) throws FileNotFoundException {
        File file = FileUtils.getFile(fileName);
        if (null == file || !file.exists()) {
            LOGGER.error("config file {} not found", fileName);
            return;
        }
        LOGGER.info("try parse config file: {}", fileName);
        Yaml yaml = new Yaml();
        Config config = yaml.loadAs(new FileInputStream(file), type);
        config.resetByCmd(commandLine);
        if (config.defaultWaitTime == 0) {
            config.defaultWaitTime = 5000;
        }
        robotConfigMap.put(userName, config);
        LOGGER.info("parse config success");
    }

    public void parseCmdArgs(String userName, String[] args, Class<? extends Config> type) throws ParseException, FileNotFoundException {
        CommandLineParser parser = new DefaultParser();
        CommandLine commandLine = parser.parse(Config.options, args);
        String configFileName = "config/robot-config.yml";
        if (commandLine.hasOption('c')) {
            // 可以指定读取别的目录的yml
            configFileName = commandLine.getOptionValue('c');
        }
        createNewConfig(userName, configFileName, commandLine, type);
    }
}

package com.yorha.robot.flow.army;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 创建行军去目标那   干架、援助
 *
 * <AUTHOR>
 */
public class CreateArmyToTargetFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        // 是否极速行军
        final boolean fastMove = (boolean) args[0];
        // 行军类型
        CommonEnum.ArmyActionType type = (CommonEnum.ArmyActionType) args[1];
        // 目标id
        final long targetId = (long) args[2];
        // 部队详情
        StructPlayerPB.TroopPB troopInfo = StructPlayerPB.TroopPB.getDefaultInstance();
        if (args.length > 3) {
            troopInfo = (StructPlayerPB.TroopPB) args[3];
        }

        StructPlayerPB.ArmyActionInfoPB.Builder info = StructPlayerPB.ArmyActionInfoPB.newBuilder();
        info.setArmyActionType(type)
                .setDebugFastMove(fastMove)
                .setTargetId(targetId)
                .setIsStayAfterBattle(false);

        StructPlayerPB.CreateArmy_C2S_ParamPB.Builder pb = StructPlayerPB.CreateArmy_C2S_ParamPB.newBuilder();
        pb.setArmyAction(info);
        pb.setTroopInfo(troopInfo);
        PlayerScene.Player_CreateArmy_C2S.Builder builder = PlayerScene.Player_CreateArmy_C2S.newBuilder();
        builder.setParam(pb);
        robot.sendMsgToServerSync(MsgType.PLAYER_CREATEARMY_C2S, builder.build());
    }
}

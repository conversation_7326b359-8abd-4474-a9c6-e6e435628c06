package com.yorha.robot.flow.city;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon.Player_UseItem_C2S;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class RandomMoveCityFlow implements Cnc<PERSON>low {

    @Override
    public void run(CncRobot robot, Object[] args) {
        long randomMoveCityItemUniqId = (long) args[0];
        Player_UseItem_C2S.Builder builder = Player_UseItem_C2S.newBuilder();
        builder.setItemKey(randomMoveCityItemUniqId);
        builder.setNum(1);
        robot.sendMsgToServerSync(MsgType.PLAYER_USEITEM_C2S, builder.build());
    }

}

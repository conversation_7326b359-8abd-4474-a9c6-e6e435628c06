package com.yorha.robot.flow.mail;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerMail;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 获取邮件红点flow
 *
 * <AUTHOR>
 */

public class GetMailUnReadCountFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerMail.Player_MailUnreadGet_C2S.Builder builder = PlayerMail.Player_MailUnreadGet_C2S.newBuilder();
        robot.sendMsgToServerSync(MsgType.PLAYER_MAILUNREADGET_C2S, builder.build());
    }
}

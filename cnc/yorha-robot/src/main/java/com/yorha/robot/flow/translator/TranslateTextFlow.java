package com.yorha.robot.flow.translator;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerChat;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 测试翻译flow。
 *
 * <AUTHOR>
 */
public class TranslateTextFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(TranslateTextFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerChat.Player_TranslateText_C2S msg = PlayerChat.Player_TranslateText_C2S.newBuilder()
                .setLanguage(CommonEnum.Language.zh)
                .setText((String) args[0])
                .build();
        robot.sendMsgToServerSync(MsgType.PLAYER_TRANSLATETEXT_C2S, msg);
    }
}

package com.yorha.robot.robotcase.stress.clan;

import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.DebugCmdFlow;
import com.yorha.robot.flow.clan.AcceptClanApplyFlow;
import com.yorha.robot.flow.clan.ApplyJoinClanFlow;
import com.yorha.robot.flow.clan.CreateOrWaitClanFlow;
import com.yorha.robot.flow.clan.QuitClanFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class FetchApplyClanRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(FetchApplyClanRobot.class);

    public FetchApplyClanRobot(String robotName, Integer robotId, User user) {
        super(robotN<PERSON>, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        if (getBoard().getCityProp().getLevel() == 1) {
            runFlow(new DebugCmdFlow(), "GiveMePower");
        }
        waitAllRobotLoginSuccess();

        // 盟主去建盟 剩下的人等待
        runFlow(new CreateOrWaitClanFlow());

        // 等所有的盟建好
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 联盟总个数
        int totalClanCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - 1) / Constant.CLAN_MEMBER_MAX + 1;
        waitState("waitAllClanReady", () -> scene.getClanCount() >= totalClanCount);

        // 联盟编号
        int clanNum = getRobotId() / Constant.CLAN_MEMBER_MAX;
        boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;

        // 保证非军团长的成员测试开始前不在军团内
        if (!isClanOwner && getBoard().playerProp.getClan().getClanId() != 0) {
            // 退盟
            runFlow(new QuitClanFlow());
            waitState("waitJoinClanProp", () -> getBoard().playerProp.getClan().getClanId() == 0);
        }

        // 不是军团长，选一半人进入军团，另一半人等待
        if (isClanOwner) {
            runFlow(new AcceptClanApplyFlow());
            realSleep(100);
        } else {
            runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
            waitState("waitJoinClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
        }


        if (!isClanOwner && getBoard().playerProp.getClan().getClanId() != 0) {
            runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNum));
            waitState("waitJoinClanProp", () -> getBoard().playerProp.getClan().getClanId() != 0);
        }
        finished();
    }
}

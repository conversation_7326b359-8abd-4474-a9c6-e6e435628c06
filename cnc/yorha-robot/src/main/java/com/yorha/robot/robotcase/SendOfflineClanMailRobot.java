package com.yorha.robot.robotcase;

import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.flow.mail.SendGmMailFlow;
import com.yorha.robot.flow.mail.WaitMailFlow;
import com.yorha.robot.scene.ClanScene;
import com.yorha.robot.scene.LoginScene;

/**
 * 发送离线联盟邮件robot
 *
 * <AUTHOR>
 */

public class SendOfflineClanMailRobot extends EnterWorldRobot {
    public SendOfflineClanMailRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        LoginScene loginScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        runFlow(new CreateOrApplyClanFlow());
        // 0号robot，等所有玩家下线后，给所有联盟发联盟邮件
        if (this.getRobotId() == 0) {
            ClanScene clanScene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
            waitState("waitAllRobotLogout", loginScene::allRobotLogout);
            clanScene.getAllClanId().forEach(it -> runFlow(new SendGmMailFlow(), CommonEnum.MailType.MAIL_TYPE_CLAN, it));
            runFlow(new WaitMailFlow());
        }
        loginScene.markRobotLogout();
        finished();
    }
}

package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.proto.PlayerClan.Player_FetchClanApplyPlayers_S2C;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * 拉申请列表 同意
 *
 * <AUTHOR>
 */
public class AcceptClanApplyFlow implements CncFlow {
    @Override
    public void run(CncRobot robot, Object[] args) {
        Player_FetchClanApplyPlayers_S2C ans = (Player_FetchClanApplyPlayers_S2C) robot.sendMsgToServerSync(
                MsgType.PLAYER_FETCHCLANAPPLYPLAYERS_C2S, PlayerClan.Player_FetchClanApplyPlayers_C2S.getDefaultInstance());

        if (ans.getApplyMembers().getDatasCount() == 0) {
            return;
        }
        for (Long id : ans.getApplyMembers().getDatasMap().keySet()) {
            robot.realSleep(100);
            PlayerClan.Player_HandleClanApply_C2S.Builder builder = PlayerClan.Player_HandleClanApply_C2S.newBuilder();
            builder.setIsAllow(true).setPlayerId(id);
            robot.sendMsgToServerSync(MsgType.PLAYER_HANDLECLANAPPLY_C2S, builder.build());
        }
    }
}

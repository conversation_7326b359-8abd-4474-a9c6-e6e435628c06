package com.yorha.robot.robotcase.stress.army;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.army.SearchWalkPathFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * <AUTHOR>
 */
public class SearchPathRobot extends EnterWorldRobot {
    public SearchPathRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;

        while (i > 0) {
            Point point = Point.valueOf(RandomUtils.nextInt(300000), RandomUtils.nextInt(400000));
            runFlow(new SearchWalkPathFlow(), point);
            i--;
        }
        finished();
    }
}

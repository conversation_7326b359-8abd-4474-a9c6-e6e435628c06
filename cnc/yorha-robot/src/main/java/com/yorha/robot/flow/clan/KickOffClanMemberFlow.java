package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

public class KickOffClanMemberFlow implements Cnc<PERSON>low {
    @Override
    public void run(CncRobot robot, Object[] args) {
        long clanId = (long) args[0];
        long operatorId = (long) args[1];
        robot.realSleep(100);
        PlayerClan.Player_FetchClanMemberInfo_S2C ans =
                (PlayerClan.Player_FetchClanMemberInfo_S2C) robot.sendMsgToServerSync(MsgType.PLAYER_FETCHCLANMEMBERINFO_C2S,
                        PlayerClan.Player_FetchClanMemberInfo_C2S.newBuilder().setClanId(clanId).build());

        for (Long targetId : ans.getMember().getDatasMap().keySet()) {
            if (targetId == operatorId) {
                continue;
            }
            // 1s 最多发 10 次
            robot.realSleep(100);
            PlayerClan.Player_KickOffClanMember_C2S.Builder builder = PlayerClan.Player_KickOffClanMember_C2S.newBuilder();
            builder.setPlayerId(targetId);
            robot.sendMsgToServerSync(MsgType.PLAYER_KICKOFFCLANMEMBER_C2S, builder.build());
        }
    }
}

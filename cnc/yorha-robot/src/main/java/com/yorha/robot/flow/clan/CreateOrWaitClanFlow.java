package com.yorha.robot.flow.clan;

import com.yorha.proto.CommonEnum;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.CncFlow;
import com.yorha.robot.scene.ClanScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 创建或等待创建
 *
 * <AUTHOR>
 */
public class CreateOrWaitClanFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CreateOrWaitClanFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        // 联盟编号
        int clanNum = robot.getRobotId() / Constant.CLAN_MEMBER_MAX;
        boolean isClanOwner = (robot.getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;
        ClanScene scene = RobotMgr.getInstance().getScene(robot.getUser(), ClanScene.class);
        // 已经有联盟了
        if (robot.getBoard().playerProp.getClan().getClanId() != 0) {
            if (isClanOwner) {
                scene.putClan(clanNum, robot.getBoard().playerProp.getClan().getClanId(), robot.getBoard().getPlayerProp().getScenePlayer().getMainCityId());
            }
            return;
        }
        String clanNamePrefix = robot.getRobotName();
        if (isClanOwner) {
            String clanSName = String.valueOf(clanNum + 10000);
            clanSName = clanSName.substring(1, 5);
            String clanName = clanNamePrefix + clanNum;
            LOGGER.info("CreateOrWaitClanFlow sName:{} name:{}", clanSName, clanName);
            robot.runFlow(new CreateClanFlow(), clanSName, clanName, CommonEnum.ClanEnterRequire.VERIFY);
            robot.waitState("waitClanProp", () -> robot.getBoard().playerProp.getClan().getClanId() != 0);
            scene.putClan(clanNum, robot.getBoard().playerProp.getClan().getClanId(), robot.getBoard().getPlayerProp().getScenePlayer().getMainCityId());
        } else {
            robot.waitState("waitCreateClan", () -> scene.getClanId(clanNum) > 0);
        }
    }
}


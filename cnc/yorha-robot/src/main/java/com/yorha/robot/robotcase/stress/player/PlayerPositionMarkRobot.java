package com.yorha.robot.robotcase.stress.player;

import com.yorha.common.io.MsgType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerPositionMark;
import com.yorha.proto.StructPB;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.CncFlowUtil;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 联盟坐标收藏
 * zeo
 */
public class PlayerPositionMarkRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPositionMarkRobot.class);


    boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;

    public PlayerPositionMarkRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        CncFlowUtil.sendDebugCommand(this, "GiveMePower");
        waitAllRobotLoginSuccess();
        runFlow(new CreateOrApplyClanFlow());
        if (isClanOwner) {
            int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
            while (i > 0) {
                i--;
                realSleep(RandomUtils.nextLong(3000));
                sendMsg();
            }
        }
    }

    private void sendMsg() {
        PlayerPositionMark.Player_PositionMarck_C2S.Builder builder = PlayerPositionMark.Player_PositionMarck_C2S.newBuilder();
        builder.setActionType(CommonEnum.PositionMarkActionType.PMAT_MARK)
                .setPositionMarkType(CommonEnum.PositionMarkType.PMT_CLAN)
                .setPoint(StructPB.PointPB.newBuilder().setX(RandomUtils.nextInt(200)).setY(100).build())
                .setMarkName("name" + RandomUtils.nextInt(100))
                .setMarkPic(1);
        sendMsgToServerSync(MsgType.PLAYER_POSITIONMARK_C2S, builder.build());
        for (long markId : getBoard().getClanProp().getClanPosMarkMap().keySet()) {
            PlayerPositionMark.Player_PositionMarck_C2S.Builder deleteBuilder = PlayerPositionMark.Player_PositionMarck_C2S.newBuilder();
            deleteBuilder.setActionType(CommonEnum.PositionMarkActionType.PMAT_DELETE)
                    .setPositionMarkType(CommonEnum.PositionMarkType.PMT_CLAN);
            sendMsgToServerSync(MsgType.PLAYER_POSITIONMARK_C2S, deleteBuilder.build());
        }
        LOGGER.debug("PlayerId:{} position mark clan:{}", getBoard().getPlayerId(), getBoard().getPlayerProp().getClan().getClanId());
    }
}

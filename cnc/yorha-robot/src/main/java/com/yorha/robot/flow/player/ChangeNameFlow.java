package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ChangeNameFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(ChangeNameFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        String name = (String) args[0];
        PlayerCommon.Player_ChangePlayerName_C2S.Builder builder = PlayerCommon.Player_ChangePlayerName_C2S.newBuilder();
        builder.setType(CommonEnum.ChangeNameType.CNT_ITEM)
                .setName(name);
        robot.sendMsgToServerSync(MsgType.PLAYER_CHANGEPLAYERNAME_C2S, builder.build());
    }
}

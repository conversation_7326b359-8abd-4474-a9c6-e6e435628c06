package com.yorha.robot.flow.clan;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.io.MsgType;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ModifyClanNameFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(SearchClanFlow.class);

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerClan.Player_ModifyClanName_C2S.Builder builder = PlayerClan.Player_ModifyClanName_C2S.newBuilder();
        if (args.length <= 0) {
            LOGGER.error("need new flag for modify clan name");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        String name = String.valueOf(args[0]);
        builder.setNewName(name);
        robot.sendMsgToServerSync(MsgType.PLAYER_MODIFYCLANNAME_C2S, builder.build());
    }
}

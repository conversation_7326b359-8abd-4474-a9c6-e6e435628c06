package com.yorha.robot.flow.player;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerCommon;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 获取玩家名片更多信息
 *
 * <AUTHOR>
 */

public class QueryPlayerCardDetailFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(QueryPlayerCardDetailFlow.class);

    /**
     * @param robot 单个robot对象
     * @param args  只支持单个参数，参数为playerId，不传参数时默认调用robot对应的playerId
     */

    @Override
    public void run(CncRobot robot, Object[] args) {
        PlayerCommon.Player_QueryPlayerCardInfoDetail_C2S.Builder builder = PlayerCommon.Player_QueryPlayerCardInfoDetail_C2S.newBuilder();
        if (args.length > 0) {
            builder.setPlayerId((long) args[0]);
        } else {
            builder.setPlayerId(robot.getBoard().getPlayerId());
        }
        robot.sendMsgToServerSync(MsgType.PLAYER_QUERYPLAYERCARDINFODETAIL_C2S, builder.build());
    }
}

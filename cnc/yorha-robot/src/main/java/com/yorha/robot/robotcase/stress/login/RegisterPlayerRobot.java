package com.yorha.robot.robotcase.stress.login;

import com.yorha.robot.base.CncRobot;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.CreatePlayerFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 游戏服
 * 注册角色
 *
 * <AUTHOR>
 */
public class RegisterPlayerRobot extends CncRobot {
    private static final Logger LOGGER = LogManager.getLogger(RegisterPlayerRobot.class);

    public RegisterPlayerRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void firstStart() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        connectServerSync(config.host, config.port);
        runFlow(new GetPlayerListFlow());
        runFlow(new CreatePlayerFlow(), isSkipNewbie());
        end();
    }
}
package com.yorha.robot.flow.clan;

import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerClan;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class FetchClanSimpleInfoFlow implements Cnc<PERSON>low {
    @Override
    public void run(Cnc<PERSON><PERSON>ot robot, Object[] args) {
        long clanId = (long) args[0];
        PlayerClan.Player_FetchClanSimpleInfo_C2S.Builder builder = PlayerClan.Player_FetchClanSimpleInfo_C2S.newBuilder();
        builder.setClanId(clanId);
        robot.sendMsgToServerSync(MsgType.PLAYER_FETCHCLANSIMPLEINFO_C2S, builder.build());
    }
}
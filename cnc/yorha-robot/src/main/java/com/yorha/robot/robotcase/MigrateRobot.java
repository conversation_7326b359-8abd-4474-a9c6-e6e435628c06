package com.yorha.robot.robotcase;

import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.dir.DirRegisterFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginOrCreatePlayerFlow;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2024/1/9
 */
public class MigrateRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(MigrateRobot.class);

    public MigrateRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    public void firstStart() {
        try {
            int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
            for (int i = 0; i < executeCount; i++) {
                // 连dir
                connectDirSvr();

                // 连zone
                connectGameSvr();

                // 登陆
                login();

                realSleep(RandomUtils.nextInt(5000, 10000));
            }
        } catch (Exception e) {
            LOGGER.error("MigrateRobot fail robotId={} ", getRobotId(), e);
        }

        end();
    }

    private void connectDirSvr() {
        // 连dir
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        connectServerSync(config.host, config.port);
        runFlow(new DirRegisterFlow());
        disconnectServerSync();
    }

    private void connectGameSvr() {
        LOGGER.info("robot={} connect to gamesvr={}:{} zoneId={}", getRobotId(), getBoard().getIp(), getBoard().getPort(), getBoard().getZoneId());
        connectServerSync(getBoard().getIp(), getBoard().getPort());
    }

    private void login() {
        runFlow(new GetPlayerListFlow());
        runFlow(new LoginOrCreatePlayerFlow(), isSkipNewbie());
        waitState("waitLoginFinish", () -> {
            if (getBoard().getSceneId() == 0) {
                return false;
            }
            if (getBoard().playerProp == null) {
                return false;
            }
            return getBoard().cityProp != null;
        });
        LoginScene loginScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        loginScene.markRobotLogin(getRobotId(), getBoard().getPlayerId());
    }
}

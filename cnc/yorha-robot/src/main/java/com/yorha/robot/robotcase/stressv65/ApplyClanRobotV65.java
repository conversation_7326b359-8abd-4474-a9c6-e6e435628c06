package com.yorha.robot.robotcase.stressv65;

import com.yorha.common.ratelimiter.GeminiThreadRateLimiter;
import com.yorha.robot.base.Constant;
import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.clan.AcceptClanApplyFlow;
import com.yorha.robot.flow.clan.ApplyJoinClanFlow;
import com.yorha.robot.flow.clan.CreateOrWaitClanFlow;
import com.yorha.robot.flow.clan.QuitClanFlow;
import com.yorha.robot.flow.player.FeatureUnlockFlow;
import com.yorha.robot.flow.player.SendGmFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.flow.user.LoginFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.ClanScene;
import com.yorha.robot.scene.LoginScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ApplyClanRobotV65 extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(ApplyClanRobotV65.class);

    public ApplyClanRobotV65(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    private static final GeminiThreadRateLimiter LIMITER = new GeminiThreadRateLimiter(250);

    @Override
    protected void afterEnterBigScene() {
        // 解锁功能
        runFlow(new FeatureUnlockFlow());
        // 获取资源
        runFlow(new SendGmFlow(), "AddCurrency currencyType=5 count=1000");
        // 创建或等待军团
        runFlow(new CreateOrWaitClanFlow());


        // 数据准备
        ClanScene scene = RobotMgr.getInstance().getScene(getUser(), ClanScene.class);
        // 联盟总个数
        int totalClanCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - 1) / Constant.CLAN_MEMBER_MAX + 1;
        waitState("waitAllClanReady", () -> scene.getClanCount() >= totalClanCount);

        boolean isClanOwner = (getRobotId() % Constant.CLAN_MEMBER_MAX) == 0;

        if (isClanOwner) {
            // 进入循环前，记录军团长已加入军团
            scene.addAlreadyJoinClanPlayer();
        } else {
            // 进入循环前，如果军团成员已经加入军团，退出一下
            if (getBoard().getPlayerProp().getClan().getClanId() != 0) {
                // 已进入军团成员退出军团
                runFlow(new QuitClanFlow());
                // 确认自己已退出军团
                waitState("waitQuitClanFinish", () -> getBoard().getPlayerProp().getClan().getClanId() == 0);
            }
        }

        int runTimes = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        int totalCount = (ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum - totalClanCount) * runTimes;

        LoginScene countScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        for (int i = 0; i < runTimes; ++i) {
            // 循环入口
            onceRun(scene, totalCount, isClanOwner);

            if (countScene.checkCountFinished(totalCount)) {
                break;
            }
        }
    }

    private void onceRun(ClanScene scene, int totalCount, boolean isClanOwner) {
        LoginScene countScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        if (isClanOwner) {
            while (true) {
                // 军团长休眠
                realSleep(1000);
                // 看看测完没
                if (countScene.checkCountFinished(totalCount)) {
                    break;
                }
                // 军团长处理所有申请
                runFlow(new AcceptClanApplyFlow());
            }
        } else {
            int clanNo = getRobotId() / Constant.CLAN_MEMBER_MAX;
            // 开始申请
            runFlow(new ApplyJoinClanFlow(), scene.getClanId(clanNo));
            // 已申请成员下线或休眠
            if (getRobotId() % 2 == 0) {
                // 一半随机成员下线再上线
                disconnectServerSync();
                while (!LIMITER.tryAcquire()) {
                    realSleep(1000);
                }
                Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
                connectServerSync(config.host, config.port);
                runFlow(new GetPlayerListFlow());
                runFlow(new LoginFlow());
            }
            // 确认自己已进入军团
            waitState("waitJoinClanProp", () -> getBoard().getPlayerProp().getClan().getClanId() != 0);
            // 记录自己已加入军团
            scene.addAlreadyJoinClanPlayer();
            // 已进入军团成员退出军团
            runFlow(new QuitClanFlow());
            // 确认自己已退出军团
            waitState("waitQuitClanFinish", () -> getBoard().getPlayerProp().getClan().getClanId() == 0);
            // 记录自己已退出军团
            scene.decAlreadyJoinClanPlayer();
            // 成员做一轮自增一次计数
            countScene.addCount();
        }
    }
}

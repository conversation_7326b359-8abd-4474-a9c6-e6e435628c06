package com.yorha.robot.robotcase.stress.mail;

import com.yorha.proto.CommonEnum;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.mail.SendGmMailFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;

/**
 * 在线接收联盟邮件用例
 *
 * <AUTHOR>
 */

public class StressClanMailRobot extends EnterWorldRobot {
    public StressClanMailRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();
        int executeCount = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        for (int i = 0; i < executeCount; i++) {
            runFlow(new SendGmMailFlow(), CommonEnum.MailType.MAIL_TYPE_CLAN, getBoard().getPlayerProp().getClan().getClanId());
        }
        finished();
    }
}

package com.yorha.robot.flow.army;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;


/**
 * <AUTHOR>
 */
public class ChangeArmyFlow implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        long armyId = (long) args[0];
        CommonEnum.ArmyActionType actionType = (CommonEnum.ArmyActionType) args[1];
        long targetId = (long) args[2];
        int targetX = (int) args[3];
        int targetY = (int) args[4];

        StructPlayerPB.ArmyActionInfoPB.Builder actionPb = StructPlayerPB.ArmyActionInfoPB.newBuilder();
        actionPb.setTargetId(targetId).setArmyActionType(actionType).setTargetPoint(StructPB.PointPB.newBuilder().setX(targetX).setY(targetY));
        if (actionType == CommonEnum.ArmyActionType.AAT_Battle) {
            actionPb.setIsStayAfterBattle(true);
        }
        PlayerScene.Player_ChangeArmyAction_C2S.Builder builder = PlayerScene.Player_ChangeArmyAction_C2S.newBuilder();
        builder.setArmyAction(actionPb).setArmyId(armyId);
        robot.sendMsgToServerSync(MsgType.PLAYER_CHANGEARMYACTION_C2S, builder.build());
    }
}

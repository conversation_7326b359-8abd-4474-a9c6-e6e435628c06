package com.yorha.robot.robotcase.stressv65;

import com.yorha.common.ratelimiter.GeminiThreadRateLimiter;
import com.yorha.common.utils.RandomUtils;
import com.yorha.robot.core.User;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.core.manager.RobotMgr;
import com.yorha.robot.flow.clan.CreateOrApplyClanFlow;
import com.yorha.robot.flow.clan.FetchClanSimpleInfoFlow;
import com.yorha.robot.flow.player.QueryPlayerCardInfoFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import com.yorha.robot.scene.LoginScene;

/**
 * <AUTHOR>
 */
public class QueryCardRobot extends EnterWorldRobot {

    public QueryCardRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }

    // 控制每秒大概1000个
    private static final GeminiThreadRateLimiter LIMITER = new GeminiThreadRateLimiter(1000);

    @Override
    protected void afterEnterBigScene() {
        waitAllRobotLoginSuccess();

        runFlow(new CreateOrApplyClanFlow());

        LoginScene loginScene = RobotMgr.getInstance().getScene(getUser(), LoginScene.class);
        int robotNum = ConfigMgr.getInstance().getConfig(getUser().getUserName()).robotNum;
        int i = ConfigMgr.getInstance().getConfig(getUser().getUserName()).executeRound;
        while (i-- > 0) {
            while (!LIMITER.tryAcquire()) {
                realSleep(100);
            }
            int robotId = RandomUtils.nextInt(robotNum);
            if (robotId == getRobotId()) {
                robotId = (robotId + 1) % robotNum;
            }
            runFlow(new QueryPlayerCardInfoFlow(), loginScene.getRobotPlayerId(robotId));
            runFlow(new FetchClanSimpleInfoFlow(), getBoard().getPlayerProp().getClan().getClanId());
            realSleep(1000);
        }
    }
}

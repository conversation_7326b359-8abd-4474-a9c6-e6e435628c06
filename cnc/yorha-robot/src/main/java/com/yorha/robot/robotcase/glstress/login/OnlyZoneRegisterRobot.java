package com.yorha.robot.robotcase.glstress.login;

import com.yorha.robot.core.User;
import com.yorha.robot.core.base.Config;
import com.yorha.robot.core.manager.ConfigMgr;
import com.yorha.robot.flow.user.CreatePlayerFlow;
import com.yorha.robot.flow.user.GetPlayerListFlow;
import com.yorha.robot.robotcase.EnterWorldRobot;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 注册机器人（不含dir）
 *
 * <AUTHOR>
 */
public class OnlyZoneRegisterRobot extends EnterWorldRobot {
    private static final Logger LOGGER = LogManager.getLogger(OnlyZoneRegisterRobot.class);

    public OnlyZoneRegisterRobot(String robotName, Integer robotId, User user) {
        super(robotName, robotId, user);
    }


    @Override
    public void firstStart() {
        Config config = ConfigMgr.getInstance().getConfig(getUser().getUserName());
        connectServerSync(config.host, config.port);
        runFlow(new GetPlayerListFlow());
        runFlow(new CreatePlayerFlow(), isSkipNewbie());

    }
}



package com.yorha.robot.flow.player;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.io.MsgType;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerQueueTask;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class CancelQueueTaskFlow implements CncFlow {
    private static final Logger LOGGER = LogManager.getLogger(CancelQueueTaskFlow.class);

    @Override
    public void run(Cnc<PERSON>obot robot, Object[] args) {
        PlayerQueueTask.Player_CancelQueueTask_C2S.Builder builder = PlayerQueueTask.Player_CancelQueueTask_C2S.newBuilder();
        CommonEnum.QueueTaskType queueTaskType = CommonEnum.QueueTaskType.forNumber((int) args[0]);
        long taskId = (long) args[1];
        builder.setQueueType(queueTaskType).setTaskId(taskId);
        try {
            robot.sendMsgToServerSync(MsgType.PLAYER_CANCELQUEUETASK_C2S, builder.build());
        } catch (GeminiException ge) {
            if (ge.getCodeId() == ErrorCode.BUILD_QUEUE_NOT_TASK.getCodeId()) {
                LOGGER.error("task {} already finished", taskId);
            }
        }
    }
}

package com.yorha.robot.flow.dir;

import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CsAccount;
import com.yorha.proto.CsAccount.DirRegisterAccount_S2C_Msg;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.flow.CncFlow;

/**
 * <AUTHOR>
 */
public class DirRegister<PERSON>low implements CncFlow {

    @Override
    public void run(CncRobot robot, Object[] args) {
        // 1.鉴权注册
        CsAccount.DirRegisterAccount_C2S_Msg accountAuth = CsAccount.DirRegisterAccount_C2S_Msg
                .newBuilder()
                .setIdentity(
                        CommonMsg.AccountIdentity.newBuilder()
                                // 设备id鉴权
                                .setAccountType(CommonEnum.AccountType.AT_DEVICE_ID)
                                // account代表设备id，同一个设备id会在服务器等于登录
                                .setDeviceId(CommonMsg.AccountIdentityDeviceId.newBuilder().setDeviceId(robot.getDeviceId()).build())
                                .build()
                )
                .build();
        DirRegisterAccount_S2C_Msg accountAuthResult = (DirRegisterAccount_S2C_Msg) robot.sendMsgToServerSync(MsgType.DIRREGISTERACCOUNT_C2S_MSG, accountAuth);
        robot.setOpenId(accountAuthResult.getToken().getOpenId());
        // 2.获取小服列表
        int hardwareLevel = robot.getBoard().getHardwareLevel();
        final String channelName = robot.getBoard().getChannel();
        CsAccount.DirGetZone_C2S_Msg dirGetZone = CsAccount.DirGetZone_C2S_Msg.newBuilder()
                .setAccountToken(accountAuthResult.getToken())
                .setHardwareLevel(hardwareLevel)
                .setChannelName(channelName)
                .build();
        CsAccount.DirGetZone_S2C_Msg result = (CsAccount.DirGetZone_S2C_Msg) robot.sendMsgToServerSync(MsgType.DIRGETZONE_C2S_MSG, dirGetZone);

        // 记录dir分配的zone：ip、port、zoneId
        robot.getBoard().setIp(result.getTargetZoneServer().getIp());
        robot.getBoard().setPort(result.getTargetZoneServer().getPort());
        robot.getBoard().setZoneId(result.getTargetZoneServer().getZoneId());
    }
}

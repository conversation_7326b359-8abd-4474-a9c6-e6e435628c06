<?xml version="1.0" encoding="UTF-8"?>
<configuration status="warn" shutdownHook="disable">
    <properties>
        <property name="LOG_HOME">/log/robot</property>
    </properties>
    <appenders>
        <!-- 控制台输出-->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
        </Console>
        <!-- 运行日志 -->
        <RollingRandomAccessFile name="DevLog" fileName="${LOG_HOME}/robotcli"
                                 filePattern="${LOG_HOME}/robotcli.%d{yyyy-MM-dd-HH}.log" immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
                <DefaultRolloverStrategy max="100"/>
            </Policies>
        </RollingRandomAccessFile>
    </appenders>
    <loggers>
        <!-- 第三方日志等级 -->
        <Logger name="io.netty" level="warn"/>
        <Logger name="org.apache.commons" level="warn"/>
        <Logger name="org.reflections" level="warn"/>
        <!-- Root Logger -->
        <Root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="DevLog"/>
        </Root>
    </loggers>
</configuration>
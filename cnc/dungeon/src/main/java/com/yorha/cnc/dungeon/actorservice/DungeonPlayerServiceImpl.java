package com.yorha.cnc.dungeon.actorservice;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.component.AoiMgrComponent;
import com.yorha.cnc.scene.entity.component.GridAoiMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.actor.ScenePlayerMgrService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsScenePlayer.*;

/**
 * <AUTHOR>
 */
public class DungeonPlayerServiceImpl implements ScenePlayerMgrService {
    private final DungeonActor dungeonActor;

    public DungeonPlayerServiceImpl(DungeonActor dungeonActor) {
        this.dungeonActor = dungeonActor;
    }

    public DungeonSceneEntity getScene() {
        return dungeonActor.getSceneWithException();
    }

    @Override
    public void handleFirstEnterBigSceneAsk(FirstEnterBigSceneAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleUpdatePlayerViewAsk(UpdatePlayerViewAsk ask) {
        AoiMgrComponent aoiMgrComponent = getScene().getAoiMgrComponent();
        if (aoiMgrComponent instanceof GridAoiMgrComponent) {
            boolean ret = ((GridAoiMgrComponent) aoiMgrComponent).onPlayerUpdateView(ask);
            dungeonActor.answer(UpdatePlayerViewAns.newBuilder().setIsOk(ret).build());
            return;
        }
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleClearPlayerViewAsk(ClearPlayerViewAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handlePlayerAddSoldierAsk(PlayerAddSoldierAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleDismissInCitySoldiersAsk(DismissInCitySoldiersAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleReturnTreatOverSoldiersAsk(ReturnTreatOverSoldiersAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetAllSoldierAsk(GetAllSoldierAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleHospitalTreatCheckAsk(HospitalTreatCheckAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleHospitalTreatAsk(HospitalTreatAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleHospitalFastTreatAsk(HospitalFastTreatAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleHospitalTreatFinishCmd(HospitalTreatFinishCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleAddDevBuffFromPlayerAsk(AddDevBuffFromPlayerAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getAsk().getPlayerId());
        AddDevBuffFromPlayerAns.Builder ans = AddDevBuffFromPlayerAns.newBuilder();
        DevBuffProp prop = scenePlayer.getDevBuffComponent().addDevBuffByParam(ask.getAsk().getParam());
        if (prop != null) {
            CommonMsg.AddDevBuffAns.Builder builder = CommonMsg.AddDevBuffAns.newBuilder();
            builder.setBuff(prop.getCopySsBuilder());
            ans.setAns(builder.build());
        }
        dungeonActor.answer(ans.build());
    }

    @Override
    public void handleRemoveDevBuffFromPlayerAsk(RemoveDevBuffFromPlayerAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleUpdateAdditionFromPlayerCmd(UpdateAdditionFromPlayerCmd ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getCmd().getPlayerId());
        scenePlayer.getAdditionComponent().updateAdditionFromPlayer(ask.getCmd().getSource(), ask.getCmd().getAdditionMap());
    }

    @Override
    public void handleGetSceneAdditionSysAsk(GetSceneAdditionSysAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetSceneDevBuffAsk(GetSceneDevBuffAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleMarkPositionAsk(MarkPositionAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSetMarkReadedCmd(SetMarkReadedCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerClanIdNameCmd(SyncPlayerClanIdNameCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerNameCmd(SyncPlayerNameCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerSoldierCmd(SyncPlayerSoldierCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerCityBuildLevelCmd(SyncPlayerCityBuildLevelCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlaneCmd(SyncPlaneCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerHeroCmd(SyncPlayerHeroCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncTechDataCmd(SyncTechDataCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerPicCmd(SyncPlayerPicCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerPicFrameCmd(SyncPlayerPicFrameCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerWallHeroPlaneCmd(SyncPlayerWallHeroPlaneCmd cmd) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerEraCmd(SyncPlayerEraCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleMarkNewbieOverAsk(MarkNewbieOverAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleBroadcastOnlinePlayerCsCmd(BroadcastOnlinePlayerCsCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleBroadcastOnlinePlayerCsWithMultiLanguageCmd(BroadcastOnlinePlayerCsWithMultiLanguageCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetMileStoneHistoryAsk(GetMileStoneHistoryAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetMileStoneRankInfoAsk(GetMileStoneRankInfoAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }


    @Override
    public void handleFetchMainCityInfoAsk(FetchMainCityInfoAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSetExpressionCmd(SetExpressionCmd ask) {
        SceneObjEntity entity = getScene().getObjMgrComponent().getSceneObjEntity(ask.getEntityId());
        if (entity == null) {
            return;
        }
        if (entity.getPlayerId() != ask.getPlayerId()) {
            return;
        }
        entity.setExpression(ask.getExpressionId());
    }

    @Override
    public void handleSetPFlagCmd(SetPFlagCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetBattleLoseAsk(GetBattleLoseAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleUseDungeonSkillAsk(UseDungeonSkillAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        DungeonPlayerEntity player = (DungeonPlayerEntity) scenePlayer;
        Point point = null;
        if (ask.hasPoint()) {
            point = Point.valueOf(ask.getPoint().getX(), ask.getPoint().getY());
        }
        player.getSkillComponent().checkAndUseSkill(ask.getSkillId(), point, ask.getEntityId());
        dungeonActor.answer(UseDungeonSkillAns.getDefaultInstance());
    }

    @Override
    public void handleChangeCityDressAsk(ChangeCityDressAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleIdIpReturnAllArmyAsk(IdIpReturnAllArmyAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleIdIpGlobalPeaceShieldAsk(IdIpGlobalPeaceShieldAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSyncPlayerPushNtfInfoAsk(SyncPlayerPushNtfInfoAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleQueryPlayerPushNtfAsk(QueryPlayerPushNtfAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleIdIpModifySoldierAsk(IdIpModifySoldierAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSkynetFindMonsterAsk(SkynetFindMonsterAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetZoneSeasonAsk(GetZoneSeasonAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleBroadOnlinePlayerSsCmd(BroadOnlinePlayerSsCmd ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleCheckCanAddDevBuffAsk(CheckCanAddDevBuffAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

}

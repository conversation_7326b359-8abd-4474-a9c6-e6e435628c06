package com.yorha.cnc.dungeon.dungeonBuilding.common;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.game.gen.prop.DungeonBuildingProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class DungeonBuildingBuilder extends SceneObjBuilder<DungeonBuildingEntity, DungeonBuildingProp> {
    public DungeonBuildingBuilder(SceneEntity sceneEntity, long eid, DungeonBuildingProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public BuildingTransformComponent transformComponent(DungeonBuildingEntity owner) {
        return new BuildingTransformComponent(owner, owner, getPointProp());
    }
}

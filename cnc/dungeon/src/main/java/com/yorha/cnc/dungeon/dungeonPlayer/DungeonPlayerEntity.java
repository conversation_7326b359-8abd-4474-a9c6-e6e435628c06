package com.yorha.cnc.dungeon.dungeonPlayer;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.component.DungeonPlayerAdditionComponent;
import com.yorha.cnc.dungeon.dungeonPlayer.component.DungeonPlayerArmyMgrComponent;
import com.yorha.cnc.dungeon.dungeonPlayer.component.DungeonPlayerPropComponent;
import com.yorha.cnc.dungeon.dungeonPlayer.component.DungeonPlayerSkillComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.component.ScenePlayerPositionMarkComponent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.DungeonPlayerProp;
import com.yorha.game.gen.prop.PlayerCardHeadProp;
import com.yorha.game.gen.prop.WarningItemListProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass.EntityType;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DungeonPlayerEntity extends AbstractScenePlayerEntity {
    private final DungeonPlayerProp prop;
    private final DungeonPlayerSkillComponent skillComponent = new DungeonPlayerSkillComponent(this);
    private final DungeonPlayerPropComponent propComponent;

    private IActorRef playerRef;

    public DungeonPlayerEntity(DungeonSceneEntity sceneEntity, long playerId, DungeonPlayerProp prop, DungeonPlayerBuilder builder) {
        super(sceneEntity, playerId, builder);
        this.prop = prop;
        this.propComponent = new DungeonPlayerPropComponent(this);
        initAllComponents();
    }

    @Override
    public void onLogin(IActorRef sessionRef) {
        bindSession(sessionRef);
        getPropComponent().onLogin();
        // dungeonScene
        getScene().getPropComponent().onPlayerLogin(sessionRef);
        // dungeonSceneObj
        getScene().getObjMgrComponent().onPlayerLogin(this, sessionRef);
    }

    @Override
    public void onLogout() {
        bindSession(null);
        getPropComponent().onPlayerLogout();
        getAoiObserverComponent().onPlayerLogout();
    }

    public IActorRef getPlayerRef() {
        return playerRef;
    }

    public void setPlayerRef(IActorRef playerRef) {
        this.playerRef = playerRef;
    }

    public DungeonPlayerProp getProp() {
        return prop;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Player;
    }

    @Override
    public DungeonActor ownerActor() {
        return (DungeonActor) super.ownerActor();
    }

    @Override
    public DungeonSceneEntity getScene() {
        return (DungeonSceneEntity) super.getScene();
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return getProp().getCamp();
    }

    @Override
    public PlayerCardHeadProp getCardHead() {
        return getProp().getCardHead();
    }

    @Override
    public void setMainCityId(long cityId) {
        getProp().setMainCityId(cityId);
    }

    @Override
    public DungeonPlayerArmyMgrComponent getArmyMgrComponent() {
        return (DungeonPlayerArmyMgrComponent) super.getArmyMgrComponent();
    }

    @Override
    public DungeonPlayerAdditionComponent getAdditionComponent() {
        return (DungeonPlayerAdditionComponent) super.getAdditionComponent();
    }

    @Override
    public ScenePlayerPositionMarkComponent getPositionMarkComponent() {
        throw new GeminiException(getEntityType() + "not support positionMarkComponent");
    }

    @Override
    public String getClanName() {
        return super.getClanName();
    }

    public DungeonPlayerSkillComponent getSkillComponent() {
        return skillComponent;
    }

    public DungeonPlayerPropComponent getPropComponent() {
        return propComponent;
    }

    @Override
    public int getZoneId() {
        if (getPlayerRef() != null) {
            return getPlayerRef().getZoneId();
        }
        return 0;
    }

    @Override
    public IActorRef genPlayerRef() {
        return getPlayerRef();
    }

    @Override
    public long getClanId() {
        return super.getClanId();
    }

    @Override
    public WarningItemListProp getWarningProp() {
        return getProp().getWarning();
    }

    @Override
    public List<Integer> getUnlockSpyData() {
        return getProp().getTechModel().getUnlockSpyLevel();
    }
}

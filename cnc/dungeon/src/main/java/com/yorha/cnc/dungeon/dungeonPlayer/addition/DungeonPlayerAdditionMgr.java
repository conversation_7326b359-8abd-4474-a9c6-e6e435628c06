package com.yorha.cnc.dungeon.dungeonPlayer.addition;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.AbstractScenePlayerAdditionMgr;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerMisc;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class DungeonPlayerAdditionMgr extends AbstractScenePlayerAdditionMgr<DungeonPlayerEntity> {
    public DungeonPlayerAdditionMgr(DungeonPlayerEntity owner) {
        super(owner);
    }

    @Override
    public AdditionSysProp getAdditionSys() {
        return getOwner().getProp().getAdditionSys();
    }

    @Override
    protected void updateAdditionToPlayer(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            return;
        }
        tellPlayerUpdateAdditionFromScene(sourceType, additions);
    }

    private void tellPlayerUpdateAdditionFromScene(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        SsPlayerMisc.UpdateAdditionFromSceneCmd.Builder cmd = SsPlayerMisc.UpdateAdditionFromSceneCmd.newBuilder()
                .setCmd(CommonMsg.UpdatePlayerAdditionCmd.newBuilder()
                        .setPlayerId(getOwner().getEntityId())
                        .setSource(sourceType)
                        .putAllAddition(additions));
        getOwner().ownerActor().tellPlayer(getOwner().getZoneId(), getOwner().getEntityId(), cmd.build());
    }
}

package com.yorha.cnc.dungeon.dungeonBuilding;

import com.yorha.cnc.dungeon.dungeonBuilding.common.DungeonBuildingBuilder;
import com.yorha.cnc.dungeon.dungeonBuilding.common.DungeonBuildingEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.game.gen.prop.DungeonBuildingProp;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class DungeonBuildingFactory {

    /**
     * 创建地图建筑
     */
    public static DungeonBuildingEntity createDungeonBuilding(SceneEntity scene, int uniqueId, int buildTemplateId, int x, int y, CommonEnum.Camp camp) {
        DungeonBuildingProp prop = makeProp(buildTemplateId, x, y, camp);
        DungeonBuildingBuilder builder = new DungeonBuildingBuilder(scene, scene.ownerActor().nextId(), prop);
        // 构建entity
        DungeonBuildingEntity buildingEntity = new DungeonBuildingEntity(builder);
        buildingEntity.setUniqueId(uniqueId);
        buildingEntity.addIntoScene();
        return buildingEntity;
    }

    private static DungeonBuildingProp makeProp(int buildTemplateId, int x, int y, CommonEnum.Camp camp) {
        // 构建prop
        DungeonBuildingProp prop = new DungeonBuildingProp();
        prop.setCamp(camp).setTemplateId(buildTemplateId).getPoint().setX(x).setY(y);
        prop.unMarkAll();
        return prop;
    }
}

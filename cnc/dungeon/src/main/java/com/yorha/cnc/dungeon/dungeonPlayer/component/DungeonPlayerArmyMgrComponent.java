package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerArmyMgrComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.dungeon.PlayerArmyAllDieEvent;
import com.yorha.game.gen.prop.ScenePlayerArmyModelProp;

/**
 * <AUTHOR>
 */
public class DungeonPlayerArmyMgrComponent extends AbstractScenePlayerArmyMgrComponent {

    public DungeonPlayerArmyMgrComponent(DungeonPlayerEntity owner) {
        super(owner);
    }

    @Override
    public DungeonPlayerEntity getOwner() {
        return (DungeonPlayerEntity) super.getOwner();
    }

    private ScenePlayerArmyModelProp getArmyModelProp() {
        return getOwner().getProp().getArmyModel();
    }

    @Override
    public void onNewArmy(ArmyEntity newArmyEntity) {
        super.onNewArmy(newArmyEntity);
        if (newArmyEntity.isRallyArmy()) {
            return;
        }
        getArmyModelProp().putArmyV(newArmyEntity.getStatusComponent().getProp());
    }

    @Override
    protected void onNormalArmyDelete(ArmyEntity army) {
        super.onNormalArmyDelete(army);
    }

    @Override
    public void removeArmy(ArmyEntity army) {
        super.removeArmy(army);
        if (myArmyList.isEmpty()) {
            getOwner().getScene().getDispatcher().dispatch(new PlayerArmyAllDieEvent(getEntityId()));
        }

        getArmyModelProp().removeArmyV(army.getEntityId());
    }

}

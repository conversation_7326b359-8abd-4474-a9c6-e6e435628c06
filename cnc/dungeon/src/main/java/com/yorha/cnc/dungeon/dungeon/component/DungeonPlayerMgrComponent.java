package com.yorha.cnc.dungeon.dungeon.component;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerBuilder;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.component.ScenePlayerMgrComponent;
import com.yorha.cnc.scene.event.dungeon.PlayerEnterEvent;
import com.yorha.cnc.scene.event.dungeon.PlayerLeaveEvent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.game.gen.prop.DungeonPlayerProp;
import com.yorha.proto.SsPlayerDungeon;
import com.yorha.proto.SsSceneDungeon;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class DungeonPlayerMgrComponent extends ScenePlayerMgrComponent<DungeonPlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(DungeonPlayerMgrComponent.class);
    /**
     * 准入的playerId
     */
    private Set<Long> allowPlayers;
    /**
     * 已经发了离开的player们
     */
    private final Set<Long> leavePlayers = new HashSet<>();

    public DungeonPlayerMgrComponent(DungeonSceneEntity owner) {
        super(owner);
    }

    public void setDungeonAllowPlayer(List<Long> allow) {
        if (allow.isEmpty()) {
            return;
        }
        this.allowPlayers = new HashSet<>(allow);
    }

    public void onPlayerLogin(long playerId, IActorRef sessionRef, IActorRef playerRef) {
        if (!scenePlayerMap.containsKey(playerId)) {
            throw new GeminiException(ErrorCode.NOT_ALLOW_ENTER_DUNGEON);
        }
        DungeonPlayerEntity dungeonPlayer = scenePlayerMap.get(playerId);
        dungeonPlayer.setPlayerRef(playerRef);
        super.onPlayerLogin(playerId, sessionRef);
        LOGGER.info("{} atScene dungeon login successful playerId={}, sessionRef={}", LogKeyConstants.GAME_PLAYER_LOGIN, playerId, sessionRef);
    }

    public boolean onDungeonPlayerLogout(long playerId) {
        super.onPlayerLogout(playerId);
        boolean ret = getOwner().getDungeonControl().needSaveSceneWhenLogout();
        // 不需要保存
        if (!ret) {
            // 就当走了 不会暂存副本 也不用发场景销毁给player
            leavePlayers.add(playerId);
        }
        return ret;
    }

    public void playerEnterDungeon(long playerId, IActorRef sessionRef, SsSceneDungeon.DungeonPlayerData data, IActorRef playerRef) {
        if (allowPlayers != null && !allowPlayers.contains(playerId)) {
            throw new GeminiException(ErrorCode.NOT_ALLOW_ENTER_DUNGEON);
        }
        getOwner().getDungeonControl().checkPlayerEnter(playerId, data);
        if (!scenePlayerMap.containsKey(playerId)) {
            DungeonPlayerProp prop = new DungeonPlayerProp();
            prop.mergeFromSs(data.getInfo());
            DungeonPlayerEntity player = new DungeonPlayerEntity(getOwner(), playerId, prop, new DungeonPlayerBuilder());
            addScenePlayer(player);
            // 具体的副本可能需要初始化一些数据
            getOwner().getDungeonControl().afterInitDungeonPlayer(player, data);
        } else {
            // 已经有了？？
            LOGGER.warn("try enter dungeon but data is already init {} {}", getOwner(), playerId);
        }
        // 走登录流程
        onPlayerLogin(playerId, sessionRef, playerRef);
        getOwner().getDispatcher().dispatch(new PlayerEnterEvent(getScenePlayer(playerId)));
    }

    @Override
    public void playerLeaveDungeon(long playerId) {
        // 先抛事件免得结算的时候都设置成没在线状态了发不了协议
        getOwner().getDispatcher().dispatch(new PlayerLeaveEvent(getScenePlayer(playerId)));
        super.onPlayerLogout(playerId);
        leavePlayers.add(playerId);
    }

    public void broadcastSceneDestroy() {
        // 场景销毁 通知下没离开的player
        SsPlayerDungeon.OnDungeonDestroyCmd.Builder cmd = SsPlayerDungeon.OnDungeonDestroyCmd.newBuilder()
                .setSceneId(getEntityId())
                .setType(this.getOwner().getDungeonType());
        broadcastPlayerExceptLeave(cmd.build());
        // 把在线的清理了
        onlinePlayer.clear();
        onlineSessionRefMap.clear();
    }

    /**
     * 广播给所有的player 除了已经发过离开副本的
     */
    public void broadcastPlayerExceptLeave(GeneratedMessageV3 ssMsg) {
        List<IActorRef> playerRefList = new LinkedList<>();
        for (AbstractScenePlayerEntity scenePlayer : scenePlayerMap.values()) {
            DungeonPlayerEntity dungeonPlayer = (DungeonPlayerEntity) scenePlayer;
            if (leavePlayers.contains(scenePlayer.getPlayerId()) && dungeonPlayer.getPlayerRef() != null) {
                continue;
            }
            playerRefList.add(dungeonPlayer.getPlayerRef());
        }
        if (playerRefList.isEmpty()) {
            return;
        }
        BroadcastHelper.toAllTargets(playerRefList, this.ownerActor().self(), ssMsg);
    }

    @Override
    protected List<IActorRef> getOnlinePlayerActorRef() {
        List<IActorRef> playerRefList = new LinkedList<>();
        for (AbstractScenePlayerEntity scenePlayer : onlinePlayer.values()) {
            DungeonPlayerEntity dungeonPlayer = (DungeonPlayerEntity) scenePlayer;
            playerRefList.add(dungeonPlayer.getPlayerRef());
        }
        return playerRefList;
    }

    @Override
    public DungeonSceneEntity getOwner() {
        return (DungeonSceneEntity) super.getOwner();
    }
}

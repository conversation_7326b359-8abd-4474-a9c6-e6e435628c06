package com.yorha.cnc.dungeon.gm;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.common.gm.GmCommandMgr;

/**
 * <AUTHOR>
 */
public class DungeonGmCommandMgr extends GmCommandMgr<DungeonActor> {
    public static DungeonGmCommandMgr getInstance() {
        return InstanceHolder.INSTANCE;
    }

    private static class InstanceHolder {
        private static final DungeonGmCommandMgr INSTANCE = new DungeonGmCommandMgr();
    }

    private DungeonGmCommandMgr() {
    }

    @Override
    protected String getCommandDir() {
        return "com.yorha.cnc.dungeon.gm.command";
    }
}

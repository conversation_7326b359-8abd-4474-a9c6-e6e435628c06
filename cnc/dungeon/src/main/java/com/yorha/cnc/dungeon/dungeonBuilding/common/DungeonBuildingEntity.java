package com.yorha.cnc.dungeon.dungeonBuilding.common;

import com.yorha.cnc.dungeon.dungeonBuilding.common.component.DungeonBuildingBattleComponent;
import com.yorha.cnc.dungeon.dungeonBuilding.common.component.DungeonBuildingBuffComponent;
import com.yorha.cnc.dungeon.dungeonBuilding.common.component.DungeonBuildingInnerArmyComponent;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.DungeonBuildingProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.TcaplusDb;
import res.template.MapBuildingTemplate;

/**
 * <AUTHOR>
 */
public class DungeonBuildingEntity extends SceneObjEntity implements BuildingEntityType {
    private final DungeonBuildingProp prop;
    private final DungeonBuildingBattleComponent battleComponent;
    private final DungeonBuildingInnerArmyComponent innerArmyComponent = new DungeonBuildingInnerArmyComponent(this);
    private final SceneObjAdditionComponent additionComponent = new SceneObjAdditionComponent(this);
    private final DungeonBuildingBuffComponent buffComponent = new DungeonBuildingBuffComponent(this);
    private int uniqueId = 0;

    public DungeonBuildingEntity(DungeonBuildingBuilder builder) {
        super(builder);
        prop = builder.getProp();
        battleComponent = new DungeonBuildingBattleComponent(this);
        initAllComponents();
        getPropComponent().initPropListener(false);
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_DungeonBuilding;
    }

    @Override
    public DungeonBuildingProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getDungeonBuildingAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getDungeonBuildingAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getDungeonBuildingAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {

    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return 0;
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        return null;
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public DungeonBuildingInnerArmyComponent getInnerArmyComponent() {
        return innerArmyComponent;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    @Override
    public DungeonBuildingBuffComponent getBuffComponent() {
        return buffComponent;
    }

    @Override
    public CommonEnum.SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public long getPlayerId() {
        return 0;
    }

    @Override
    public long getClanId() {
        return 0;
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return getProp().getCamp();
    }

    public void setCampEnum(CommonEnum.Camp camp) {
        getProp().setCamp(camp);
    }

    public int getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(int uniqueId) {
        this.uniqueId = uniqueId;
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, getProp().getTemplateId());
    }
}

package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.io.MsgType;
import com.yorha.common.prop.PropNotifier;
import com.yorha.game.gen.prop.DungeonPlayerProp;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.PlayerPB;

/**
 * <AUTHOR>
 */
public class DungeonPlayerPropComponent extends AbstractComponent<DungeonPlayerEntity> {

    private final DungeonPlayerPropComponent.DungeonPlayerNotifier propNotifier;

    public DungeonPlayerPropComponent(DungeonPlayerEntity owner) {
        super(owner);
        propNotifier = new DungeonPlayerNotifier();
    }

    @Override
    public void init() {
        getOwner().getProp().setListener(new PropertyChangeListener(this::flushProp, getOwner().ownerActor().self()));
    }

    /**
     * 下发变体属性
     * 所以是mod而不是new
     */
    public void onLogin() {
        propNotifier.reset();
        // 会下发全量prop
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());
        getOwner().getProp().unMarkAll();
    }

    public void onPlayerLogout() {
        this.propNotifier.reset();
    }

    private void flushProp() {
        // 没脏数据，返回
        if (!getOwner().getProp().hasAnyMark()) {
            return;
        }
        // 自己destroy了，返回
        if (getOwner().isDestroy()) {
            return;
        }
        // propNotifier 自身是幂等的，对hasAnyMark有自己的控制
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());
        getOwner().getProp().unMarkAll();
    }

    class DungeonPlayerNotifier extends PropNotifier<DungeonPlayerProp, PlayerPB.PlayerEntityPB.Builder> {
        DungeonPlayerNotifier() {
            super(PlayerPB.PlayerEntityPB.newBuilder());
        }

        @Override
        public boolean canNtfToClient() {
            return getOwner().isOnline();
        }

        @Override
        public boolean ntfToClient(Entity.EntityNtfMsg notify) {
            return getOwner().sendMsgToClient(MsgType.ENTITYNTFMSG, notify);
        }

        @Override
        public void fillEntityAttr(EntityAttrOuterClass.EntityAttr.Builder entityAttr, PlayerPB.PlayerEntityPB.Builder builder) {
            entityAttr.setEntityId(getEntityId())
                    .setEntityType(getEntityType())
                    .setPlayerAttr(builder.build());
        }

        @Override
        public int copyChange(DungeonPlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            return prop.copyChangeToCs(changeCollector.getDungeonPlayerBuilder());
        }

        @Override
        public void copyFull(DungeonPlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            prop.copyToCs(changeCollector.getDungeonPlayerBuilder());
        }

        @Override
        protected void fillNewEntityNtf(Entity.EntityNtfMsg.Builder builder, EntityAttrOuterClass.EntityAttr.Builder entityAttr) {
            // DungeonPlayer都是以PlayerEntity的mod形式下发的
            builder.addModEntities(entityAttr);
        }
    }
}

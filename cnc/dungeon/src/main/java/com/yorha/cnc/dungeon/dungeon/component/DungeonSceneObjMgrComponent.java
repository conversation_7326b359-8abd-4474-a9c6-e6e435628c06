package com.yorha.cnc.dungeon.dungeon.component;

import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.dungeon.dungeonBuilding.common.DungeonBuildingEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.dungeon.*;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.EntityAttrOuterClass.EntityType;

import java.util.*;

/**
 * <AUTHOR>
 */
public class DungeonSceneObjMgrComponent extends ObjMgrComponent {
    /**
     * 野怪  组id->list
     */
    private final Map<Integer, List<MonsterEntity>> groupMonster = new HashMap<>();
    private final Map<Integer, CityEntity> npcCityMap = new HashMap<>();
    private final Map<Integer, SceneObjEntity> dungeonBuildingEntityMap = new HashMap<>();

    public DungeonSceneObjMgrComponent(DungeonSceneEntity owner) {
        super(owner);
    }

    public void onPlayerLogin(DungeonPlayerEntity player, IActorRef sessionRef) {
        int n = 0;
        Entity.EntityNtfMsg.Builder msgBuilder = Entity.EntityNtfMsg.newBuilder().setZoneId(getOwner().getZoneId());
        for (SceneObjEntity entity : entityMap.values()) {
            EntityAttrOuterClass.EntityAttr.Builder fullAttr = entity.getPropComponent().getFullAttr();
            msgBuilder.addNewEntities(fullAttr);
            if (++n >= GameLogicConstants.AOI_SYNC_PACKAGE_ENTITY_NUM) {
                SessionHelper.sendMsgToSession(sessionRef, ownerActor(), MsgType.ENTITYNTFMSG, msgBuilder.build());
                msgBuilder = Entity.EntityNtfMsg.newBuilder().setZoneId(getOwner().getZoneId());
                n = 0;
            }
        }
        if (n != 0) {
            SessionHelper.sendMsgToSession(sessionRef, ownerActor(), MsgType.ENTITYNTFMSG, msgBuilder.build());
        }
    }

    @Override
    public void addSceneObjEntity(SceneObjEntity entity) {
        super.addSceneObjEntity(entity);
        if (entity.getEntityType() == EntityType.ET_City) {
            CityEntity city = (CityEntity) entity;
            if (city.getUniqueId() != 0) {
                npcCityMap.put(city.getUniqueId(), city);
                getOwner().getDispatcher().dispatch(new NpcCityBornEvent(city));
            }
        }
        if (entity.getEntityType() == EntityType.ET_DungeonBuilding) {
            final int uniqueId;
            DungeonBuildingEntity buildingEntity = (DungeonBuildingEntity) entity;
            uniqueId = buildingEntity.getUniqueId();
            dungeonBuildingEntityMap.put(uniqueId, entity);
        }
        if (entity.getEntityType() == EntityType.ET_Monster) {
            MonsterEntity monster = (MonsterEntity) entity;
            if (monster.getProp().getGroupId() == 0) {
                return;
            }
            List<MonsterEntity> list = groupMonster.computeIfAbsent(monster.getProp().getGroupId(), (key) -> new ArrayList<>());
            list.add(monster);
            getOwner().getDispatcher().dispatch(new MonsterBornEvent(monster));
        }
    }

    @Override
    public void removeSceneObjEntity(SceneObjEntity entity) {
        super.removeSceneObjEntity(entity);
        if (entity.getEntityType() == EntityType.ET_Monster) {
            onMonsterDied((MonsterEntity) entity);
        }
    }

    private void onMonsterDied(MonsterEntity entity) {
        int groupId = entity.getProp().getGroupId();
        boolean removed = groupMonster.getOrDefault(groupId, Collections.emptyList()).remove(entity);
        if (!removed) {
            return;
        }
        getOwner().getDispatcher().dispatch(new MonsterDiedEvent(entity.getTemplate().getId()));
        if (!groupMonster.get(groupId).isEmpty()) {
            return;
        }
        groupMonster.remove(groupId);
        getOwner().getDispatcher().dispatch(new GroupMonsterDiedEvent(groupId, entity.getBattleComponent().hasAnyAlive()));
        if (!groupMonster.isEmpty()) {
            return;
        }
        getOwner().getDispatcher().dispatch(new AllMonsterDiedEvent());
    }

    public List<MonsterEntity> getGroupMonster(int groupId) {
        return groupMonster.getOrDefault(groupId, Collections.emptyList());
    }

    public CityEntity getNpcCity(int id) {
        return npcCityMap.get(id);
    }

    public SceneObjEntity getDungeonBuilding(int id) {
        return dungeonBuildingEntityMap.get(id);
    }

    @Override
    public void onDestroy() {
        for (List<MonsterEntity> entities : new ArrayList<>(groupMonster.values())) {
            for (MonsterEntity entity : new ArrayList<>(entities)) {
                entity.forceRecycle();
            }
        }
        groupMonster.clear();
        npcCityMap.clear();
    }

    @Override
    public DungeonSceneEntity getOwner() {
        return (DungeonSceneEntity) super.getOwner();
    }
}

package com.yorha.cnc.dungeon.control;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.DungeonStage;
import com.yorha.proto.SsSceneDungeon;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public abstract class AbstractDungeonControl {
    private static final Logger LOGGER = LogManager.getLogger(AbstractDungeonControl.class);

    private final DungeonSceneEntity owner;
    private boolean isEnd;
    private long startTsMs;

    public AbstractDungeonControl(DungeonSceneEntity owner) {
        this.owner = owner;
    }

    /**
     * 检查玩家能不能进副本
     */
    public void checkPlayerEnter(final long playerId, SsSceneDungeon.DungeonPlayerData data) {

    }

    public void afterInitDungeonPlayer(DungeonPlayerEntity player, SsSceneDungeon.DungeonPlayerData data) {

    }

    /**
     * @return true==需要保存
     */
    public boolean needSaveSceneWhenLogout() {
        return false;
    }

    public int getCostSecond() {
        return startTsMs == 0 ? 0 : (int) TimeUtils.ms2Second(SystemClock.now() - startTsMs);
    }

    /**
     * 进入准备阶段   具体control控制
     */
    protected void enterPrepare() {
        if (getOwner().getProp().getStage() != DungeonStage.DS_INIT) {
            LOGGER.error("{} error  try enter prepare stage", getOwner());
            return;
        }
        getOwner().getProp().setStage(DungeonStage.DS_PREPARE).setEnterStageTsMs(SystemClock.now());
        onPrepare();
        LOGGER.info("{} enterPrepare", getOwner());
        if (getPrepareTime() != 0) {
            owner.getTimerComponent().addTimerWithPrefix(String.valueOf(getOwner().getDungeonId()),
                    TimerReasonType.DUNGEON_PREPARE, this::enterRunning, getPrepareTime(), TimeUnit.SECONDS);
        } else {
            enterRunning();
        }
    }

    /**
     * 进入运行阶段  timer控制
     */
    private void enterRunning() {
        if (getOwner().getProp().getStage() != DungeonStage.DS_PREPARE) {
            LOGGER.error("{} error  try enter running stage", getOwner());
            return;
        }
        getOwner().getProp().setStage(DungeonStage.DS_RUNNING).setEnterStageTsMs(SystemClock.now());
        startTsMs = SystemClock.now();
        onStart();
        LOGGER.info("{} enterRunning", getOwner());
        if (getRunningTime() != 0) {
            owner.getTimerComponent().addTimerWithPrefix(String.valueOf(getOwner().getDungeonId()), TimerReasonType.DUNGEON_RUNNING,
                    () -> enterEnding(true, false), getRunningTime(), TimeUnit.SECONDS);
        } else {
            enterRunning();
        }
    }


    /**
     * 进入结束阶段  timer/具体control 控制
     */
    protected void enterEnding(boolean isTimer, boolean isSuccess) {
        if (isEnd) {
            return;
        }
        isEnd = true;
        if (!isTimer) {
            getOwner().getTimerComponent().cancelTimer(TimerReasonType.DUNGEON_RUNNING, String.valueOf(getOwner().getDungeonId()));
        }
        LOGGER.info("{} enterEnding isTimer: {} isSuccess: {}", getOwner(), isTimer, isSuccess);
        getOwner().getProp().setStage(DungeonStage.DS_ENDING).setEnterStageTsMs(SystemClock.now());
        // 保护onEnd
        safeOnEnd(isSuccess);
        // 通知场景结束  移除所有timer和eventListener
        getOwner().onEnd();
        if (getEndingTime() != 0) {
            owner.getTimerComponent().addTimerWithPrefix(String.valueOf(getOwner().getDungeonId()),
                    TimerReasonType.DUNGEON_ENDING, this::destroy, getEndingTime(), TimeUnit.SECONDS);
        } else {
            destroy();
        }
    }

    /**
     * 强制结束并销毁 不管在什么阶段
     */
    protected void forceDestroy() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.DUNGEON_RUNNING, String.valueOf(getOwner().getDungeonId()));
        if (!isEnd) {
            isEnd = true;
            getOwner().getProp().setStage(DungeonStage.DS_ENDING).setEnterStageTsMs(SystemClock.now());
            // 保护onEnd不要出错
            safeOnEnd(false);
            // 通知场景结束  移除所有timer和eventListener
            getOwner().onEnd();
        }
        owner.getTimerComponent().addTimerWithPrefix(String.valueOf(getOwner().getDungeonId()),
                TimerReasonType.DUNGEON_DESTROY, this::destroy, 1, TimeUnit.SECONDS);
        LOGGER.info("{} forceDestroy", getOwner());
    }

    private void safeOnEnd(final boolean isSuccess) {
        try {
            // 调用具体控制器的end
            onEnd(isSuccess);
        } catch (Exception e) {
            LOGGER.error("{} safeOnEnd fail, e=", getOwner(), e);
        }
    }

    public void destroy() {
        getOwner().deleteObj();
        ownerActor().forceDestroy("player all leave");
    }

    public abstract int getMapId();

    public void onPrepare() {

    }

    public void onStart() {

    }

    public void onEnd(boolean isSuccess) {

    }

    public void onTick() {

    }

    public void afterTick() {

    }

    /**
     * 准备阶段时间
     */
    protected abstract int getPrepareTime();

    /**
     * 运行阶段时间
     */
    protected abstract int getRunningTime();

    /**
     * 结束阶段时间
     */
    protected abstract int getEndingTime();

    public DungeonSceneEntity getOwner() {
        return owner;
    }

    public DungeonActor ownerActor() {
        return getOwner().ownerActor();
    }
}

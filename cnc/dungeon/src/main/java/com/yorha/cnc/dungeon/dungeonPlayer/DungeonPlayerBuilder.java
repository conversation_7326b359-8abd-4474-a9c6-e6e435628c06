package com.yorha.cnc.dungeon.dungeonPlayer;

import com.yorha.cnc.dungeon.dungeonPlayer.component.*;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerBuilder;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerBattleComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerSoldierMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;

/**
 * <AUTHOR>
 */
public class DungeonPlayerBuilder extends AbstractScenePlayerBuilder<DungeonPlayerEntity> {

    @Override
    public DungeonPlayerRallyComponent rallyComponent(DungeonPlayerEntity owner) {
        return new DungeonPlayerRallyComponent(owner);
    }

    @Override
    public AbstractScenePlayerSoldierMgrComponent soldierMgrComponent(DungeonPlayerEntity owner) {
        return null;
    }

    @Override
    public DungeonPlayerDevBuffComponent devBuffComponent(DungeonPlayerEntity owner) {
        return new DungeonPlayerDevBuffComponent(owner);
    }

    @Override
    public DungeonPlayerAdditionComponent additionComponent(DungeonPlayerEntity owner) {
        return new DungeonPlayerAdditionComponent(owner);
    }

    @Override
    public DungeonPlayerArmyMgrComponent armyMgrComponent(DungeonPlayerEntity owner) {
        return new DungeonPlayerArmyMgrComponent(owner);
    }

    @Override
    public DungeonPlayerCityComponent cityComponent(DungeonPlayerEntity owner) {
        return new DungeonPlayerCityComponent(owner);
    }

    @Override
    public AbstractScenePlayerWarningComponent warningComponent(DungeonPlayerEntity owner) {
        return super.warningComponent(owner);
    }

    @Override
    public AbstractScenePlayerBattleComponent battleComponent(DungeonPlayerEntity owner) {
        return super.battleComponent(owner);
    }
}

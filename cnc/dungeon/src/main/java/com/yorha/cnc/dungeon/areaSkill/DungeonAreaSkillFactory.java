package com.yorha.cnc.dungeon.areaSkill;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.areaSkill.AreaSkillBuilder;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Ring;
import com.yorha.game.gen.prop.AreaSkillProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RangeSkillTemplate;

public class DungeonAreaSkillFactory {
    private static final Logger LOGGER = LogManager.getLogger(DungeonAreaSkillFactory.class);

    public static DungeonAreaSkillEntity createAreaSkill(
            SceneEntity scene,
            AbstractScenePlayerEntity ownerScenePlayer,
            BattleRole owner,
            int areaSkillId,
            Point point,
            Integer lifeTime
    ) {
        // 找出生点
        Point bornPoint;
        RangeSkillTemplate template = ResHolder.getTemplate(RangeSkillTemplate.class, areaSkillId);
        bornPoint = getConfigBornPoint(scene, point, template);
        if (bornPoint == null) {
            LOGGER.error("DungeonAreaSkillErrLog {} {} {}, createAreaSkill failed. bornPoint is null areaSkillId:{}, basePoint:{}",
                    scene, ownerScenePlayer, owner, areaSkillId, point);
            return null;
        }

        if (lifeTime != null && lifeTime <= 0) {
            LOGGER.error("DungeonAreaSkillErrLog {} {} {}, createAreaSkill failed. lifeTime invalid areaSkillId:{}, lifeTime:{}",
                    scene, ownerScenePlayer, owner, areaSkillId, lifeTime);
            return null;
        }

        if (ownerScenePlayer == null && owner == null) {
            LOGGER.error("DungeonAreaSkillErrLog createAreaSkill failed. scenePlayer and owner is null");
            return null;
        }

        int troopId = template.getTroopIndex();
        AreaSkillProp prop = new AreaSkillProp();
        prop.getTroop().mergeFromSs(TroopHelper.getTroopBuilder(ResHolder.getInstance(), troopId));
        prop.setTemplateId(troopId);
        prop.getPoint().setX(bornPoint.getX()).setY(bornPoint.getY());

        prop.unMarkAll();

        long areaSkillEntityId = scene.ownerActor().nextId();
        AreaSkillBuilder builder = new AreaSkillBuilder(scene, areaSkillEntityId, prop);
        DungeonAreaSkillEntity areaSkillEntity = new DungeonAreaSkillEntity(builder, ownerScenePlayer, owner,
                lifeTime == null ? template.getLifetime() : lifeTime, areaSkillId);
        areaSkillEntity.addIntoScene();
        areaSkillEntity.getBattleComponent().onAreaSkillCreated();
        return areaSkillEntity;
    }

    /**
     * 根据范围配置选点
     */
    private static Point getConfigBornPoint(SceneEntity scene, Point point, RangeSkillTemplate template) {
        if (template.getCreateRange() == CommonEnum.AreaSkillBornPointType.ASBPT_RING_RANDOM) {
            int innerR = template.getRangeList().get(0);
            int outerR = template.getRangeList().get(1);
            return selectBornPointByRing(scene, point, innerR, outerR);
        }
        if (template.getCreateRange() == CommonEnum.AreaSkillBornPointType.ASBPT_SPECIFIC_POINT) {
            return point;
        }
        return null;
    }

    /**
     * 环形选点
     *
     * @param point  基础点
     * @param innerR 内圆半径
     * @param outerR 外圆半径
     */
    private static Point selectBornPointByRing(SceneEntity scene, Point point, int innerR, int outerR) {
        if (scene == null || point == null) {
            LOGGER.error("DungeonAreaSkillErrLog selectBornPointByRing scene={} point={}", innerR, outerR);
            return null;
        }
        if (!Ring.checkInnerOuterData(innerR, outerR)) {
            LOGGER.error("DungeonAreaSkillErrLog selectBornPointByRing innerR={} outerR={}", innerR, outerR);
            return null;
        }
        Ring shape = Ring.valueOf(point, outerR, innerR);
        return BornPointHelper.randomRingBornPoint(scene, shape, 0, true);
    }
}

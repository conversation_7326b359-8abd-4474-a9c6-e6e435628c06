package com.yorha.cnc.dungeon.dungeon.component;

import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.entity.component.RallyMgrComponent;
import com.yorha.cnc.scene.event.dungeon.DungeonRallyEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.StructPlayerPB;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DungeonRallyMgrComponent extends RallyMgrComponent {
    private final Map<Long, Map<Long, RallyEntity>> targetRallyMap = new Long2ObjectOpenHashMap<>();
    private final Map<Long, RallyEntity> rallyMap = new Long2ObjectOpenHashMap<>();

    public DungeonRallyMgrComponent(DungeonSceneEntity owner) {
        super(owner);
    }

    @Override
    public DungeonSceneEntity getOwner() {
        return (DungeonSceneEntity) super.getOwner();
    }

    protected void addRallyInfo(RallyEntity rally, SceneObjEntity sceneObj) {
        targetRallyMap.computeIfAbsent(sceneObj.getEntityId(), (k) -> new HashMap<>()).put(rally.getEntityId(), rally);
        rallyMap.put(rally.getEntityId(), rally);
        getOwner().getDispatcher().dispatch(new DungeonRallyEvent(sceneObj));
    }

    protected void removeRallyInfo(RallyEntity rally) {
        targetRallyMap.get(rally.getProp().getTargetId()).remove(rally.getEntityId());
        rallyMap.remove(rally.getEntityId());
    }

    public void addRally(RallyEntity rally, SceneObjEntity sceneObj) {
        addRallyInfo(rally, sceneObj);
    }

    public void removeRally(RallyEntity rally) {
        removeRallyInfo(rally);
    }

    @Override
    public RallyEntity getRally(long rallyId) {
        return rallyMap.get(rallyId);
    }

    public RallyEntity getRallyEntityWithException(long rallyId) {
        RallyEntity rallyEntity = rallyMap.get(rallyId);
        if (rallyEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        return rallyEntity;
    }

    public StructPlayerPB.RallyInfoListPB queryRallyInfoList(final long playerId) {
        StructPlayerPB.RallyInfoListPB.Builder listBuilder = StructPlayerPB.RallyInfoListPB.newBuilder();
        for (RallyEntity rally : rallyMap.values()) {
            listBuilder.addDatas(rally.getRallySimpleInfoPb());
        }
        return listBuilder.build();
    }

    public StructPlayerPB.RallyInfoPB getOneRallyInfo(long rallyId, long playerId) {
        RallyEntity rallyEntity = rallyMap.get(rallyId);
        if (rallyEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        return rallyEntity.getRallyInfoPb();


    }

    @Override
    public void onDestroy() {
        for (RallyEntity value : new ArrayList<>(rallyMap.values())) {
            value.dismiss(RallyDismissReason.RDR_CANCEL);
        }
        rallyMap.clear();
        targetRallyMap.clear();
    }
}

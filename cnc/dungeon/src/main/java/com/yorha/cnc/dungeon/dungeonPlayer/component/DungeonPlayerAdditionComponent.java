package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.addition.DungeonPlayerAdditionMgr;
import com.yorha.cnc.scene.abstractsceneplayer.addition.AbstractScenePlayerAdditionMgr;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerAdditionComponent;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DungeonPlayerAdditionComponent extends AbstractScenePlayerAdditionComponent {
    public static final Logger LOGGER = LogManager.getLogger(DungeonPlayerAdditionComponent.class);

    public DungeonPlayerAdditionComponent(DungeonPlayerEntity owner) {
        super(owner);
    }

    @Override
    public DungeonPlayerEntity getOwner() {
        return (DungeonPlayerEntity) super.getOwner();
    }

    @Override
    public void init() {
        additionMgr = new DungeonPlayerAdditionMgr(getOwner());
        additionMgr.register(getOwner().getDevBuffComponent());
    }

    @Override
    public void postInit() {
        Map<Integer, Long> newAdditions = new HashMap<>(getOwner().getProp().getAdditionSys().getAdditionSize());
        for (Map.Entry<Integer, AdditionProp> addition : getOwner().getProp().getAdditionSys().getAddition().entrySet()) {
            newAdditions.put(addition.getKey(), addition.getValue().getTotalValue());
        }
        AbstractScenePlayerAdditionMgr.dispatchAdditionChange(newAdditions, getOwner());
    }

    @Override
    protected AdditionSysProp getAdditionProp() {
        return getOwner().getProp().getAdditionSys();
    }
}

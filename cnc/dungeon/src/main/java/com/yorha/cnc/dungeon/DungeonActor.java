package com.yorha.cnc.dungeon;

import com.yorha.cnc.dungeon.actorservice.*;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneBuilder;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.actorservice.SceneMapServiceImpl;
import com.yorha.common.actor.*;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DungeonActor extends SceneActor implements SceneServices {
    private static final Logger LOGGER = LogManager.getLogger(DungeonActor.class);
    private DungeonPlayerServiceImpl playerService;
    private DungeonObjServiceImpl objService;
    private SceneMapServiceImpl mapService;
    private DungeonCityArmyServiceImpl cityArmyService;
    private DungeonRallyAssistServiceImpl rallyAssistService;
    private DungeonServiceImpl dungeonService;
    private DungeonPlaneServiceImpl scenePlaneService;
    private DungeonSceneEntity dungeonScene;

    public DungeonActor(ActorSystem system, IActorRef self) {
        super(system, self);
    }

    @Override
    public void initService() {
        playerService = new DungeonPlayerServiceImpl(this);
        mapService = new SceneMapServiceImpl(this);
        cityArmyService = new DungeonCityArmyServiceImpl(this);
        rallyAssistService = new DungeonRallyAssistServiceImpl(this);
        objService = new DungeonObjServiceImpl(this);
        dungeonService = new DungeonServiceImpl(this);
        scenePlaneService = new DungeonPlaneServiceImpl(this);
        // 副本最大时间10m， 保底保证12m副本消失
        this.setReceiveTimeout(720);
    }

    public void createDungeonScene(long sceneId, CommonEnum.DungeonType type, int dungeonId) {
        DungeonSceneBuilder builder = new DungeonSceneBuilder(type);
        try {
            dungeonScene = new DungeonSceneEntity(this, sceneId, builder, type, dungeonId);
        } catch (Exception e) {
            LOGGER.error("createDungeonScene error", e);
            forceDestroy("crate dungeon fail");
            throw e;
        }
        LOGGER.info("createDungeonScene {}", dungeonScene);
    }

    @Override
    public DungeonSceneEntity getScene() {
        return dungeonScene;
    }

    public DungeonSceneEntity getSceneWithException() {
        if (dungeonScene == null) {
            forceDestroy("scene not exist");
            throw new GeminiException(ErrorCode.SCENE_NOT_EXIT);
        }
        return dungeonScene;
    }


    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    public SceneCityArmyService getSceneCityArmyService() {
        return cityArmyService;
    }

    @Override
    public SceneDungeonService getSceneDungeonService() {
        return dungeonService;
    }

    @Override
    public SceneMapService getSceneMapService() {
        return mapService;
    }

    @Override
    public SceneObjectService getSceneObjectService() {
        return objService;
    }

    @Override
    public ScenePlaneService getScenePlaneService() {
        return scenePlaneService;
    }

    @Override
    public ScenePlayerMgrService getScenePlayerMgrService() {
        return playerService;
    }

    @Override
    public SceneRallyAssistService getSceneRallyAssistService() {
        return rallyAssistService;
    }

    @Override
    public SceneClanMgrService getSceneClanMgrService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public SceneCollectService getSceneCollectService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public SceneInformationMgrService getSceneInformationMgrService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public SceneKingdomService getSceneKingdomService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public SceneMarqueeService getSceneMarqueeService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public SceneActivityService getSceneActivityService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public SceneIdipService getSceneIdipService() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }
}

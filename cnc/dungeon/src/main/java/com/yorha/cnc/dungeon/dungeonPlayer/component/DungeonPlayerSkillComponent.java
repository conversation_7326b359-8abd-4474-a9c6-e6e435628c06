package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.areaSkill.DungeonAreaSkillFactory;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.DungeonSkillItemProp;
import com.yorha.game.gen.prop.DungeonSkillSysProp;
import com.yorha.proto.CommonEnum.DungeonSkillType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.DungeonSkillTemplate;

/**
 * <AUTHOR>
 */
public class DungeonPlayerSkillComponent extends AbstractComponent<DungeonPlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(DungeonPlayerSkillComponent.class);

    public DungeonPlayerSkillComponent(DungeonPlayerEntity owner) {
        super(owner);
    }

    private DungeonSkillSysProp getProp() {
        return getOwner().getProp().getDungeonSkill();
    }

    public void addSkill(int skillId, int num) {
        DungeonSkillTemplate template = ResHolder.getInstance().findValueFromMap(DungeonSkillTemplate.class, skillId);
        if (template == null) {
            LOGGER.error("{} addSkill but skillId {} not exist config", getOwner(), skillId);
            return;
        }
        if (!getProp().getSkill().containsKey(skillId)) {
            getProp().getSkill().addEmptyValue(skillId);
        }
        DungeonSkillItemProp skillV = getProp().getSkillV(skillId);
        skillV.setCanUseNum(skillV.getCanUseNum() + num);
        LOGGER.info("{} addDungeonSkill id: {} num: {}", getOwner(), skillId, num);
    }

    /**
     * 校验技能能否使用
     */
    public void checkSkill(int skillId) {
        DungeonSkillItemProp skillV = getProp().getSkillV(skillId);
        if (skillV == null || skillV.getCanUseNum() == 0) {
            throw new GeminiException(ErrorCode.SKILL_COMMANDER_SKILL_UNLOCK);
        }
        long now = SystemClock.now();
        if (skillV.getCanUseTsMs() > now) {
            throw new GeminiException(ErrorCode.SKILL_COMMANDER_SKILL_CD);
        }
    }

    /**
     * 使用技能 必须前置校验已经过了 否则会出错！
     */
    public void useSkill(int skillId, Point point, long entityId) {
        DungeonSkillItemProp skillV = getProp().getSkillV(skillId);
        if (skillV == null || skillV.getCanUseNum() == 0) {
            return;
        }
        useSkill(skillV, point, entityId);
    }

    /**
     * 检验并使用技能 不能使用会抛异常
     */
    public void checkAndUseSkill(int skillId, Point point, long entityId) {
        final DungeonSkillItemProp skillV;

        skillV = getProp().getSkillV(skillId);
        if (skillV == null) {
            throw new GeminiException(ErrorCode.SKILL_COMMANDER_SKILL_UNLOCK);
        }

        checkDungeonSKill(skillV);

        useSkill(skillV, point, entityId);
    }

    private void checkDungeonSKill(final DungeonSkillItemProp skillV) {
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            LOGGER.info("checkDungeonSKill isBattleTestServer skip");
            return;
        }
        if (skillV.getCanUseNum() == 0) {
            throw new GeminiException(ErrorCode.SKILL_COMMANDER_SKILL_UNLOCK);
        }
        long now = SystemClock.now();
        if (skillV.getCanUseTsMs() > now) {
            throw new GeminiException(ErrorCode.SKILL_COMMANDER_SKILL_CD);
        }
    }

    private void useSkill(DungeonSkillItemProp prop, Point point, long entityId) {
        final int skillId = prop.getSkillId();
        LOGGER.info("DungeonPlayerSkillComponent useSkill owner={} skillId={}", getOwner(), skillId);
        DungeonSkillTemplate template = ResHolder.getInstance().getValueFromMap(DungeonSkillTemplate.class, skillId);
        long now = SystemClock.now();
        // 加cd 减次数
        if (template.getCd() != 0) {
            prop.setCanUseTsMs(now + TimeUtils.second2Ms(template.getCd()));
        }
        prop.setCanUseNum(prop.getCanUseNum() - 1);
        DungeonSkillType type = template.getType();
        // 处理效果
        switch (type) {
            case DST_AREA_SKILL:
                DungeonAreaSkillFactory.createAreaSkill(getOwner().getScene(), getOwner(), null, template.getAreaSkillId(), point, null);
                break;
            default:
                break;
        }
    }
}

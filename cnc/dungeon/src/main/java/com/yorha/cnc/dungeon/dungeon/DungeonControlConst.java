package com.yorha.cnc.dungeon.dungeon;

import com.yorha.cnc.dungeon.control.AbstractDungeonControl;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum.DungeonType;

/**
 * <AUTHOR>
 */
public interface DungeonControlConst {
    /**
     * 构造控制器
     *
     * @param owner     副本场景
     * @param type      副本类型
     * @param dungeonId 副本id
     * @return 控制器
     */
    static AbstractDungeonControl genControl(DungeonSceneEntity owner, DungeonType type, int dungeonId) {
        switch (type) {
            default:
                throw new GeminiException(ErrorCode.DUNGEON_TYPE_NOT_HAVE_CONTROL);
        }
    }
}
package com.yorha.cnc.dungeon.areaSkill;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.areaSkill.AreaSkillBuilder;
import com.yorha.cnc.scene.areaSkill.AreaSkillEntity;

public class DungeonAreaSkillEntity extends AreaSkillEntity {
    public DungeonAreaSkillEntity(AreaSkillBuilder builder, AbstractScenePlayerEntity scenePlayer, BattleRole master, int lifeTime, int templateId) {
        super(builder, scenePlayer, master, lifeTime, templateId);
    }

    @Override
    public String getClanName() {
        return "";
    }

    @Override
    public String getClanSName() {
        return "";
    }
}

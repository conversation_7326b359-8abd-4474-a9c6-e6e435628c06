package com.yorha.cnc.dungeon.city.component;

import com.yorha.cnc.battle.event.GarrisonChangeEvent;
import com.yorha.cnc.battle.event.TroopEvent;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.*;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.component.CityBattleComponent;
import com.yorha.cnc.scene.event.army.BattleUnitPropChangeEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.helper.GuardTowerHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;
import res.template.TroopTemplate;

import javax.annotation.Nullable;
import java.util.*;

public class DungeonCityBattleComponent extends CityBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(DungeonCityBattleComponent.class);

    public DungeonCityBattleComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected DungeonPlayerEntity getScenePlayer() {
        return (DungeonPlayerEntity) getOwner().getScenePlayer();
    }


    @Override
    public void init() {
        super.init();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleUnitPropChange, BattleUnitPropChangeEvent.class);
    }

    @Override
    public void postInit() {
        if (getOwner().getProp().getWallState() != CommonEnum.CityWallState.CS_NORMAL && getScenePlayer().getWallComponent() != null) {
            getScenePlayer().getWallComponent().onRestore();
        }
    }

    @Override
    public boolean beAttacked(ArmyEntity armyEntity, Long attackerPlayerId) {
        // 添加预警信息
        CommonEnum.WarningType type = armyEntity.isRallyArmy() ? CommonEnum.WarningType.WT_RallyAttack : CommonEnum.WarningType.WT_Attack;
        getOwner().getInnerArmyComponent().addWarningItem(armyEntity, type);
        return super.beAttacked(armyEntity, attackerPlayerId);
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }


    @Override
    protected boolean buildGarrisonHeroOrMecha() {
        boolean flag = false;
        WallInfoProp sceneWallProp = getScenePlayer().getProp().getWall();
        LOGGER.info("{} refreshGarrisonInfo. {} {}", getOwner(), sceneWallProp.getMainHero(), sceneWallProp.getDeputyHero());
        // 主将
        final int mainHeroIdBefore = getTroop().getMainHero().getHeroId();
        if (replaceTroopHero(getTroop().getMainHero(), sceneWallProp.getMainHero())) {
            flag = true;
        }
        // 副将
        final int deputyHeroIdBefore = getTroop().getDeputyHero().getHeroId();
        if (replaceTroopHero(getTroop().getDeputyHero(), sceneWallProp.getDeputyHero())) {
            flag = true;
        }

        LOGGER.debug("{} refreshGarrisonInfo mainHero:{}->{} deputyHero:{}->{}",
                getOwner(),
                mainHeroIdBefore,
                getTroop().getMainHero().getHeroId(),
                deputyHeroIdBefore,
                getTroop().getDeputyHero().getHeroId());

        return flag;
    }

    @Override
    protected void buildGarrisonSoldier() {

    }

    /**
     * @return true:确实替换为了新的
     */
    private boolean replaceTroopHero(HeroProp troopHero, @Nullable HeroProp chosenHeroProp) {
        boolean replaced = false;
        if (chosenHeroProp == null) {
            if (troopHero.getHeroId() > 0) {
                replaced = true;
                troopHero.mergeFromDb(Struct.Hero.getDefaultInstance());
            }
        } else {
            if (troopHero.getHeroId() != chosenHeroProp.getHeroId()) {
                replaced = true;
                Struct.Hero.Builder builder = Struct.Hero.newBuilder();
                chosenHeroProp.copyToSs(builder);
                troopHero.mergeFromSs(builder.build());
            }
        }
        return replaced;
    }

    @Override
    protected void buildGuardTower() {
        if (getScenePlayer().getWallComponent() == null) {
            return;
        }
        int defenseTowerId = getScenePlayer().getWallComponent().getDefenseTowerId();
        int towerTroopId = GuardTowerHelper.getGuardTowerTroopId(defenseTowerId);
        TroopTemplate troopTemplate = ResHolder.getInstance().findValueFromMap(TroopTemplate.class, towerTroopId);
        if (troopTemplate != null) {
            IntPairType soldierData = troopTemplate.getSoldierPairList().get(0);
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierData.getKey());
            if (soldierTemplate != null) {
                int towerMaxHp = getScenePlayer().getWallComponent().getDefenseTowerMaxHp();
                int towerCurHp = getScenePlayer().getWallComponent().getDefenseTowerCurHp();
                SoldierUnit.OwnerInfo ownerInfo = new SoldierUnit.OwnerInfo(getEntityId(), CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF);
                SoldierUnit towerUnit = new SoldierUnit(ownerInfo, SoldierData.fromProp(new SoldierProp().setSoldierId(soldierTemplate.getId()).setNum(towerCurHp)));
                getBattleRole().setGuardTower(new GuardTower(getBattleRole(), towerUnit, towerMaxHp, GuardTowerHelper.getGuardTowerLv(defenseTowerId)));
            }
        }
        getScenePlayer().getWallComponent().onCityEnterBattle();
    }

    /**
     * 行军拉出后  check守城队伍
     * 检查出去的士兵有没有占用战斗部队名额，重设num
     */
    @Override
    public void onArmyOut(Collection<Struct.Soldier> outSoldiers) {
        if (!isInBattle()) {
            return;
        }
        // recalculate soldier
        int totalOutNum = 0;
        final Int32SoldierMapProp garrisonSoldierMap = getOwner().getProp().getGarrison().getSoldierMap();
        for (Struct.Soldier outSoldier : outSoldiers) {
            SoldierProp garrisonSoldier = garrisonSoldierMap.get(outSoldier.getSoldierId());
            if (garrisonSoldier != null) {
                int oldNum = garrisonSoldier.getNum();
                garrisonSoldier.setNum(Math.max(0, oldNum - outSoldier.getNum()));
                totalOutNum += Math.min(oldNum, outSoldier.getNum());
            }
        }
        getOwner().getEventDispatcher().dispatch(new TroopEvent(getEntityId(),
                SoldierNumChangeReason.army_out,
                totalOutNum,
                getScenePlayer().getName(),
                getScenePlayer().getClanName(),
                getScenePlayer().getCardHeadSS()));
        LOGGER.debug("city: {} rebuildGarrisonSoldier: {}, aliveRate:{}", getOwner(), garrisonSoldierMap, getBattleRole().aliveSoldierRate());
    }

    /**
     * 行军回城后  check守城队伍
     * 当前战斗部队未满需要补充部队
     */
    @Override
    public void onArmyReturn(Collection<SoldierProp> soldierPropList) {
        if (!isInBattle()) {
            return;
        }
        onSoldierAdd(soldierPropList, SoldierNumChangeReason.army_return);
    }

    @Override
    public void onGarrisonChange() {
        if (!isInBattle()) {
            return;
        }
        boolean realChange = buildGarrisonHeroOrMecha();
        if (realChange) {
            getOwner().getEventDispatcher().dispatch(new GarrisonChangeEvent());
        }
    }

    @Override
    public void onSoldierAdd(Collection<SoldierProp> soldierPropList, SoldierNumChangeReason reason) {
        if (!isInBattle()) {
            return;
        }
        int maxSoldierNum = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getCityBattleMaxSoldier();
        int total = cityOwnerGarrisonSoldierTotal();
        int maxCanAdd = maxSoldierNum - total;
        if (maxCanAdd <= 0) {
            return;
        }
        List<SoldierProp> soldierProps = new ArrayList<>(soldierPropList);
        soldierProps.sort(Comparator.comparing(SoldierProp::getSoldierId).reversed());

        CityGarrisonProp garrisonProp = getOwner().getProp().getGarrison();
        int totalAddNum = 0;
        // 遍历回城的士兵list
        for (SoldierProp prop : soldierProps) {
            if (maxCanAdd <= 0) {
                return;
            }
            int addNum = Math.min(Soldier.aliveCountOf(prop), maxCanAdd);
            SoldierProp existSp = garrisonProp.getSoldierMapV(prop.getSoldierId());
            if (existSp == null) {
                existSp = garrisonProp.addEmptySoldierMap(prop.getSoldierId());
                existSp.setNum(addNum);
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF), existSp);
            } else {
                getBattleRole().addSoldier4ExistUnit(this.getBattleRole(), prop.getSoldierId(), addNum);
            }
            maxCanAdd -= addNum;
            totalAddNum += addNum;
        }
        // 构建总troop
        getBattleRole().refreshTroop();
        getOwner().getEventDispatcher().dispatch(new TroopEvent(getEntityId(),
                reason,
                totalAddNum,
                getScenePlayer().getName(),
                getScenePlayer().getClanName(),
                getScenePlayer().getCardHeadSS()));
        LOGGER.debug("city: {} rebuildGarrisonSoldier: {} {}, aliveRate:{}", getEntityId(), getTroop().getTroop(), maxCanAdd, getBattleRole().aliveSoldierRate());
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {
        BattleRecordRoleSummaryProp selfSummary = recordAllProp.getSelfSummary();
        BattleRecordRoleProp selfRoleProp = getSelfOrEnemyRole(recordAllProp, false);
        if (selfRoleProp != null) {
            selfSummary.setClanName(selfRoleProp.getClanName());
            selfSummary.getCardHead().mergeFromSs(selfRoleProp.getCardHead().getCopySsBuilder().build());
        } else {
            selfSummary.setClanName(getOwner().getProp().getClanSname());
            selfSummary.getCardHead().mergeFromSs(getOwner().getProp().getCardHead().getCopySsBuilder().build());
        }
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        // 自己
        roleRecord.addMember(buildRoleMemberRecord());
        // 增援部队
        for (ArmyEntity inRallyArmy : getOwner().getInnerArmyComponent().getInnerArmyList()) {
            roleRecord.addMember(inRallyArmy.getBattleComponent().buildRoleMemberRecord());
        }
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getEntityId())
                .setPlayerId(getOwner().getPlayerId())
                .setClanName(getOwner().getProp().getClanSname())
                .setCardHead(getOwner().getProp().getCardHead().getCopySsBuilder().build());
        HeroProp mainHeroProp = getBattleRole().getMainHero() != null ? getBattleRole().getMainHero().getHeroProp() : new HeroProp();
        HeroProp deputyHeroProp = getBattleRole().getDeputyHero() != null ? getBattleRole().getDeputyHero().getHeroProp() : new HeroProp();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    @Override
    protected StructMail.MailShowTitle getMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        StructMail.MailShowTitle.Builder builder = StructMail.MailShowTitle.newBuilder();
        BattleRecordRoleProp enemyRole = getSelfOrEnemyRole(recordAllProp, true);
        // title
        builder.setTitleKey(getBattleRecordMailTitle(alive, anyEnemyAlive, enemyRole));

        // sub title
        if (enemyRole != null) {
            boolean isSuperWeaponAtk = enemyRole.getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill.getNumber();
            if (isSuperWeaponAtk) {
                ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                // 超武战报
                String subTitleKey = temple.getBattleRecordSub16();
                builder.setSubTitleKey(subTitleKey);

                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayHero(enemyRole.getMainHero().getHeroId()));
            } else {
                // 防守基地
                ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                String subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub2() : temple.getBattleRecordSub1();
                builder.setSubTitleKey(subTitleKey);

                // 您的基地遭到了%s的攻击
                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(enemyRole.getCardHead().getName()));
            }
        }
        return builder.build();
    }

    @Override
    public void afterGarrisonReplaced(BattleRecordAllProp recordAllProp) {
        super.afterGarrisonReplaced(recordAllProp);
        trySendRecordMail(getBattleRecordPlayerIds(), recordAllProp, getBattleRole().hasAnyAlive(), true);
    }

    @Override
    public void handleSoldierLoss(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {

    }

    @Override
    public boolean isMustNtfType() {
        return true;
    }

    private int cityOwnerGarrisonSoldierTotal() {
        Collection<SoldierProp> garrisonSoldiers = getOwner().getProp().getGarrison().getSoldierMap().values();
        return garrisonSoldiers.stream().mapToInt(SoldierProp::getNum).sum();
    }

    private void onBattleUnitPropChange(BattleUnitPropChangeEvent event) {
        LOGGER.debug("onBattleUnitChange. event:{}", event);
        if (!isInBattle()) {
            return;
        }
        if (event.getHeroList() != null && event.getHeroList().size() > 0) {
            for (Struct.Hero hero : event.getHeroList()) {
                LOGGER.debug("{} onHeroPropChange heroId={}", this, hero);
                if (getTroop().getMainHero().getHeroId() == hero.getHeroId()) {
                    getTroop().getMainHero().mergeChangeFromSs(hero);
                    BattleHero mainHero = getMainHero();
                    mainHero.refreshHeroByProp();
                } else if (getTroop().getDeputyHero().getHeroId() == hero.getHeroId()) {
                    getTroop().getDeputyHero().mergeChangeFromSs(hero);
                    BattleHero deputyHero = getBattleRole().getDeputyHero();
                    if (deputyHero != null) {
                        deputyHero.refreshHeroByProp();
                    } else {
                        // 副将为空的时候不应该有数据同步过来
                        LOGGER.error("onBattleUnitPropChange. deputy:{} battleRole:{}", getTroop().getDeputyHero(), getBattleRole());
                    }
                }
            }
        }
    }
}

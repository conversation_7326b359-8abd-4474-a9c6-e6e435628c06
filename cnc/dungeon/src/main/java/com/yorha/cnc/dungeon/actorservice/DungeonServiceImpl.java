package com.yorha.cnc.dungeon.actorservice;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.SceneDungeonService;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.proto.SsSceneDungeon.*;


/**
 * <AUTHOR>
 */
public class DungeonServiceImpl implements SceneDungeonService {
    private final DungeonActor dungeonActor;

    public DungeonServiceImpl(DungeonActor dungeonActor) {
        this.dungeonActor = dungeonActor;
    }

    public long getSceneId() {
        return dungeonActor.getSceneId();
    }

    public DungeonSceneEntity getSceneWithException() {
        return dungeonActor.getSceneWithException();
    }

    @Override
    public void handleCreateDungeonAsk(CreateDungeonAsk ask) {
        dungeonActor.createDungeonScene(getSceneId(), ask.getType(), ask.getDungeonId());
        // 初始化dungeonPlayer的数据
        getSceneWithException().getPlayerMgrComponent().setDungeonAllowPlayer(ask.getAllowEnterPlayerList());
        dungeonActor.answer(CreateDungeonAns.newBuilder().build());
    }

    /**
     * 进入副本
     * 1. 先call大世界  设置成进入副本状态，清理aoi等
     * 2. 再call副本    初始化数据等
     */
    @Override
    public void handleEnterDungeonAsk(EnterDungeonAsk ask) {
        final IActorRef sessionRef = RefFactory.fromPb(ask.getSessionRef());
        getSceneWithException().getPlayerMgrComponent().playerEnterDungeon(ask.getPlayerId(), sessionRef, ask.getData(), dungeonActor.getCurrentEnvelope().getSender());

        dungeonActor.answer(EnterDungeonAns.newBuilder().build());
    }

    /**
     * 离开副本
     * 1. 先call副本  离开
     * 2. 再call大世界  重置状态，下发数据
     */
    @Override
    public void handleLeaveDungeonAsk(LeaveDungeonAsk ask) {
        // 大世界 接到进入副本的通知
        getSceneWithException().getPlayerMgrComponent().playerLeaveDungeon(ask.getPlayerId());
        dungeonActor.answer(LeaveDungeonAns.newBuilder().build());
    }

    /**
     * 肯定是重连进来的
     */
    @Override
    public void handlePlayerLoginAsk(PlayerLoginAsk ask) {
        final IActorRef sessionRef = RefFactory.fromPb(ask.getSessionRef());
        getSceneWithException().getPlayerMgrComponent().onPlayerLogin(ask.getPlayerId(), sessionRef, dungeonActor.getCurrentEnvelope().getSender());
        dungeonActor.answer(PlayerLoginAns.newBuilder().setIsOk(true).build());
    }

    @Override
    public void handlePlayerLogoutAsk(PlayerLogoutAsk ask) {
        boolean needSaveScene = getSceneWithException().getPlayerMgrComponent().onDungeonPlayerLogout(ask.getPlayerId());
        PlayerLogoutAns.Builder builder = PlayerLogoutAns.newBuilder().setNeedSaveScene(needSaveScene);
        dungeonActor.answer(builder.build());
    }

    @Override
    public void handlePerformActionAsk(PerformActionAsk ask) {
        dungeonActor.answer(PerformActionAns.getDefaultInstance());
    }
}

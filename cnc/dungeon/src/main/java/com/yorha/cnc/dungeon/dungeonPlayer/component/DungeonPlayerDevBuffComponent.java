package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.dungeon.dungeonPlayer.buff.DungeonDevBuffMgr;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerDevBuffComponent;
import com.yorha.game.gen.prop.DevBuffSysProp;

/**
 * <AUTHOR>
 */
public class DungeonPlayerDevBuffComponent extends AbstractScenePlayerDevBuffComponent {

    public DungeonPlayerDevBuffComponent(DungeonPlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        devBuffMgr = new DungeonDevBuffMgr(getOwner());
    }

    @Override
    public void postInit() {
        getMgr().postInit();
    }

    @Override
    public DungeonPlayerEntity getOwner() {
        return (DungeonPlayerEntity) super.getOwner();
    }

    @Override
    public DevBuffSysProp getBuffProp() {
        return getOwner().getProp().getDevBuffSys();
    }
}

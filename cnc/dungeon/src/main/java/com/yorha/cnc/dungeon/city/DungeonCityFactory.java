package com.yorha.cnc.dungeon.city;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityBuilder;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.CityProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DungeonCityFactory {
    private static final Logger LOGGER = LogManager.getLogger(DungeonCityFactory.class);

    public static void createDungeonPlayerCity(AbstractScenePlayerEntity player, int level, Point bornPos) {
        // 构建city属性
        CityProp prop = new CityProp();
        prop.setOwnerId(player.getEntityId())
                .setLevel(level)
                .setClanId(player.getClanId())
                .setWallState(CommonEnum.CityWallState.CS_NORMAL)
                .setCamp(player.getCampEnum())
                .getPoint().setX(bornPos.getX()).setY(bornPos.getY());

        prop.getCardHead().mergeFromSs(player.getCardHead().getCopySsBuilder().build());
        // 创建city
        prop.unMarkAll();
        CityBuilder builder = new DungeonCityBuilder(player.getScene(), player.getScene().ownerActor().nextId(), prop);
        CityEntity cityEntity = new CityEntity(builder);
        cityEntity.addIntoScene();
        LOGGER.info("{} create success", cityEntity);
    }
}

package com.yorha.cnc.dungeon.actorservice;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.actor.SceneRallyAssistService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.PlayerRally;
import com.yorha.proto.SsSceneRallyAssist;
import com.yorha.proto.StructPlayerPB;

/**
 * <AUTHOR>
 */
public class DungeonRallyAssistServiceImpl implements SceneRallyAssistService {
    private final DungeonActor dungeonActor;

    public DungeonRallyAssistServiceImpl(DungeonActor dungeonActor) {
        this.dungeonActor = dungeonActor;
    }

    public DungeonSceneEntity getScene() {
        return dungeonActor.getSceneWithException();
    }

    @Override
    public void handleFetchRallyListAsk(SsSceneRallyAssist.FetchRallyListAsk ask) {
        StructPlayerPB.RallyInfoListPB list = getScene().getRallyMgrComponent().queryRallyInfoList(ask.getPlayerId());
        PlayerRally.Player_QueryRallyList_S2C build = PlayerRally.Player_QueryRallyList_S2C.newBuilder().setRallyList(list).build();
        dungeonActor.answer(SsSceneRallyAssist.FetchRallyListAns.newBuilder().setMsgBytes(build.toByteString()).build());
    }

    @Override
    public void handleFetchOneRallyAsk(SsSceneRallyAssist.FetchOneRallyAsk ask) {
        StructPlayerPB.RallyInfoPB info = getScene().getRallyMgrComponent().getOneRallyInfo(ask.getRallyId(), ask.getPlayerId());
        PlayerRally.Player_QueryOneRally_S2C build = PlayerRally.Player_QueryOneRally_S2C.newBuilder().setRallyInfo(info).build();
        dungeonActor.answer(SsSceneRallyAssist.FetchOneRallyAns.newBuilder().setMsgBytes(build.toByteString()).build());
    }

    @Override
    public void handlePlayerCancelRallyAsk(SsSceneRallyAssist.PlayerCancelRallyAsk ask) {
        RallyEntity rallyEntity = getScene().getRallyMgrComponent().getRallyEntityWithException(ask.getRallyId());
        rallyEntity.cancel(ask.getPlayerId());
        dungeonActor.answer(SsSceneRallyAssist.PlayerCancelRallyAns.getDefaultInstance());
    }

    @Override
    public void handleRepatriateRallyMemberAsk(SsSceneRallyAssist.RepatriateRallyMemberAsk ask) {
        RallyEntity rallyEntity = getScene().getRallyMgrComponent().getRallyEntityWithException(ask.getRallyId());
        rallyEntity.getArmyMgrComponent().repatriateMember(ask.getPlayerId(), ask.getArmyId());
        dungeonActor.answer(SsSceneRallyAssist.RepatriateRallyMemberAns.getDefaultInstance());
    }

    @Override
    public void handleSetRallyRecommendSoldierAsk(SsSceneRallyAssist.SetRallyRecommendSoldierAsk ask) {
        RallyEntity rallyEntity = getScene().getRallyMgrComponent().getRallyEntityWithException(ask.getEntityId());
        rallyEntity.setRecommendSoldierType(ask.getPlayerId(), ask.getSoldierType().getDatasList());
        dungeonActor.answer(SsSceneRallyAssist.SetRallyRecommendSoldierAns.getDefaultInstance());
    }

    @Override
    public void handleFetchWarningAsk(SsSceneRallyAssist.FetchWarningAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer.getWarningComponent() == null) {
            throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED, "not gvg");
        }
        StructPlayerPB.WarningInfoListPB list = scenePlayer.getWarningComponent().getWarningList();
        PlayerRally.Player_FetchWarningList_S2C.Builder builder = PlayerRally.Player_FetchWarningList_S2C.newBuilder();
        builder.setWarningList(list);
        PlayerRally.Player_FetchWarningList_S2C build = builder.build();
        dungeonActor.answer(SsSceneRallyAssist.FetchWarningAns.newBuilder().setMsgBytes(build.toByteString()).build());
    }

    @Override
    public void handleSetWarningItemTagAsk(SsSceneRallyAssist.SetWarningItemTagAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer.getWarningComponent() == null) {
            throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED, "not gvg");
        }
        scenePlayer.getWarningComponent().setWarningItemIgnoreTag(ask.getArmyId(), ask.getIgnore());
        dungeonActor.answer(SsSceneRallyAssist.SetWarningItemTagAns.getDefaultInstance());
    }

    @Override
    public void handleIgnoreAllWarningAsk(SsSceneRallyAssist.IgnoreAllWarningAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer.getWarningComponent() == null) {
            throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED, "not gvg");
        }
        scenePlayer.getWarningComponent().ignoreWarningAll();
        dungeonActor.answer(SsSceneRallyAssist.IgnoreAllWarningAns.getDefaultInstance());
    }

    @Override
    public void handleFetchInnerArmyAsk(SsSceneRallyAssist.FetchInnerArmyAsk ask) {
        AbstractScenePlayerEntity operatorScenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getOperatorId());
        SsSceneRallyAssist.FetchInnerArmyAns.Builder ans = SsSceneRallyAssist.FetchInnerArmyAns.newBuilder();
        PlayerRally.Player_FetchCityInnerArmy_S2C.Builder csRetBuilder = PlayerRally.Player_FetchCityInnerArmy_S2C.newBuilder();

        // 盟友的城 或者 地图建筑
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntityWithException(ask.getTargetId());
        // 非同阵营不可拉取
        if (target.getCampEnum() != operatorScenePlayer.getCampEnum()) {
            dungeonActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
            return;
        }
        SceneObjInnerArmyComponent innerArmyComponent = target.getInnerArmyComponent();
        if (innerArmyComponent == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        csRetBuilder.setCityAssistInfo(innerArmyComponent.getAssistInfoPb(ask.getOperatorId()));
        dungeonActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
    }

    @Override
    public void handleRepatriateAssistMemberAsk(SsSceneRallyAssist.RepatriateAssistMemberAsk ask) {
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntityWithException(ask.getTargetId());
        SceneObjInnerArmyComponent innerArmyComponent = target.getInnerArmyComponent();
        if (innerArmyComponent == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        innerArmyComponent.repatriateArmy(ask.getPlayerId(), ask.getArmyId(), ask.getIsPermission());
        dungeonActor.answer(SsSceneRallyAssist.RepatriateAssistMemberAns.getDefaultInstance());
    }

    @Override
    public void handleChangeAssistLeaderAsk(SsSceneRallyAssist.ChangeAssistLeaderAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }
}

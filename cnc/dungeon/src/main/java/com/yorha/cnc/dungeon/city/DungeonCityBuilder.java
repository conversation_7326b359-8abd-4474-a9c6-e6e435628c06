package com.yorha.cnc.dungeon.city;

import com.yorha.cnc.dungeon.city.component.DungeonCityBattleComponent;
import com.yorha.cnc.scene.city.CityBuilder;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.city.component.CityBattleComponent;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.game.gen.prop.CityProp;

public class DungeonCityBuilder extends CityBuilder {
    public DungeonCityBuilder(SceneEntity sceneEntity, long eid, CityProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public CityBattleComponent battleComponent(CityEntity owner) {
        return new DungeonCityBattleComponent(owner);
    }
}

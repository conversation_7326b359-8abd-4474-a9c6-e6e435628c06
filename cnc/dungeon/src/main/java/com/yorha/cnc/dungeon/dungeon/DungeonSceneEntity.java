package com.yorha.cnc.dungeon.dungeon;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.control.AbstractDungeonControl;
import com.yorha.cnc.dungeon.dungeon.component.DungeonPlayerMgrComponent;
import com.yorha.cnc.dungeon.dungeon.component.DungeonRallyMgrComponent;
import com.yorha.cnc.dungeon.dungeon.component.DungeonSceneObjMgrComponent;
import com.yorha.cnc.dungeon.dungeon.component.DungeonScenePropComponent;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.GridAoiMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.eventdispatcher.EventDispatcher;
import com.yorha.game.gen.prop.DungeonSceneProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DungeonStage;
import com.yorha.proto.CommonEnum.DungeonType;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DungeonSceneEntity extends SceneEntity {
    private static final Logger LOGGER = LogManager.getLogger(DungeonSceneEntity.class);
    private final AbstractDungeonControl dungeonControl;
    private final DungeonSceneProp prop;

    private final EventDispatcher dispatcher = new EventDispatcher();
    private final DungeonScenePropComponent propComponent = new DungeonScenePropComponent(this);
    private final DungeonRallyMgrComponent rallyMgrComponent;

    public DungeonSceneEntity(DungeonActor dungeonActor, long entityId, DungeonSceneBuilder builder, DungeonType type, int dungeonId) {
        super(dungeonActor, entityId, builder);
        rallyMgrComponent = builder.dungeonRallyMgrComponent(this);
        prop = new DungeonSceneProp();
        prop.setType(type).setDungeonId(dungeonId).setStage(DungeonStage.DS_INIT);
        dungeonControl = DungeonControlConst.genControl(this, type, dungeonId);
        prop.unMarkAll();
        getPathFindMgrComponent().initPathFind();
        if ((getAoiMgrComponent()) instanceof GridAoiMgrComponent) {
            ((GridAoiMgrComponent) getAoiMgrComponent()).initAoi();
        }
        initAllComponents();
        startTick();
    }

    @Override
    public void deleteObj() {
        // 场景销毁 通知下没离开的player
        getPlayerMgrComponent().broadcastSceneDestroy();
        super.deleteObj();
    }

    public void onEnd() {
        getRallyMgrComponent().onDestroy();
        // 取消触发器
        getDispatcher().clear();
        // 取消timer
        getTimerComponent().cancelAllTimer();
        this.isInTick = false;
    }

    @Override
    public int getZoneId() {
        return 0;
    }

    @Override
    public int getDungeonId() {
        return prop.getDungeonId();
    }

    public DungeonType getDungeonType() {
        return prop.getType();
    }

    @Override
    public void onTick() {
        try {
            getDungeonControl().onTick();
        } catch (Exception e) {
            LOGGER.error("onTick failed {} ", this, e);
        }
        super.onTick();
    }

    @Override
    protected void afterTick() {
        super.afterTick();
        try {
            this.getDungeonControl().afterTick();
        } catch (Exception e) {
            LOGGER.error("afterTick failed {} ", this, e);
        }
        if (getAoiMgrComponent() instanceof GridAoiMgrComponent) {
            ((GridAoiMgrComponent) getAoiMgrComponent()).afterTick();
        }
    }

    public DungeonScenePropComponent getPropComponent() {
        return propComponent;
    }

    public DungeonSceneProp getProp() {
        return prop;
    }

    public AbstractDungeonControl getDungeonControl() {
        return dungeonControl;
    }

    /**
     * 获取所在场景层级的最高层
     */
    @Override
    public int getMaxSceneLayer(SceneObjEntity sceneObjEntity) {
        return super.getMaxSceneLayer(sceneObjEntity);
    }


    @Override
    public EntityAttrOuterClass.EntityType getEntityType() {
        return EntityAttrOuterClass.EntityType.ET_Dungeon;
    }

    @Override
    public DungeonPlayerMgrComponent getPlayerMgrComponent() {
        return (DungeonPlayerMgrComponent) super.getPlayerMgrComponent();
    }

    @Override
    public DungeonSceneObjMgrComponent getObjMgrComponent() {
        return (DungeonSceneObjMgrComponent) super.getObjMgrComponent();
    }

    @Override
    public DungeonRallyMgrComponent getRallyMgrComponent() {
        return rallyMgrComponent;
    }

    @Override
    public EventDispatcher getDispatcher() {
        return dispatcher;
    }

    @Override
    public DungeonActor ownerActor() {
        return (DungeonActor) super.ownerActor();
    }

    @Override
    public int getMapId() {
        return dungeonControl.getMapId();
    }

    @Override
    public CommonEnum.MapType getMapType() {
        DungeonType dungeonType = getDungeonType();
        switch (dungeonType) {
            default:
                throw new GeminiException("DungeonSceneEntity getMapType unsupported dungeonType={}", dungeonType);
        }

    }

    @Override
    public long getMapIdForPoint() {
        return 0;
    }

    @Override
    public String toString() {
        return "DungeonSceneEntity{" + getEntityId() +
                ", stage=" + prop.getStage() +
                ", " + dungeonControl +
                '}';
    }
}

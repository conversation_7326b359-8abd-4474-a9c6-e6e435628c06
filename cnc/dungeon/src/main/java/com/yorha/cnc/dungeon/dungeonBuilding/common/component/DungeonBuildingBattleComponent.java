package com.yorha.cnc.dungeon.dungeonBuilding.common.component;

import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.SoldierUnit;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.dungeon.dungeonBuilding.common.DungeonBuildingEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.BattleProp;
import com.yorha.game.gen.prop.BattleRecordAllProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SceneObjType;
import com.yorha.proto.StructPlayer;
import qlog.flow.QlogCncBattle;
import res.template.MapBuildingTemplate;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DungeonBuildingBattleComponent extends SceneObjBattleComponent {
    public DungeonBuildingBattleComponent(DungeonBuildingEntity owner) {
        super(owner, SceneObjType.SOT_CITY_ARMY_SELF, null);
    }

    @Override
    public boolean ready(IBattleRoleAdapter other) {
        onAttackerArrived();
        return super.ready(other);
    }

    /**
     * 攻城队伍到达 初始化驻防队伍数据
     */
    public void onAttackerArrived() {
        // 战斗中无需构建数据
        if (isInBattle()) {
            return;
        }
        SceneObjInnerArmyComponent innerArmyComponent = getOwner().getInnerArmyComponent();
        // 没人驻守 使用配置部队
        if (innerArmyComponent.getInnerArmyList().isEmpty()) {
            MapBuildingTemplate template = getOwner().getBuildingTemplate();
            StructPlayer.Troop troop = TroopHelper.getTroopBuilder(ResHolder.getInstance(), template.getTroopId());
            getTroop().mergeFromSs(troop);
            getTroop().setTroopId(template.getTroopId());
            getTroop().markAll();
            if (getTroop().getMainHero().getHeroId() != 0) {
                getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
            }
            if (getTroop().getDeputyHero().getHeroId() != 0) {
                getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
            }
            for (SoldierProp selfSoldier : getTroop().getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_CITY_ARMY_SELF), selfSoldier);
            }
            return;
        }
        innerArmyComponent.enterBattle();
        // 使用城内驻守部队
        long leaderArmyId = innerArmyComponent.getLeaderArmyId();
        for (ArmyEntity innerArmy : innerArmyComponent.getInnerArmyList()) {
            TroopProp troop = innerArmy.getProp().getTroop();
            if (innerArmy.getEntityId() == leaderArmyId) {
                setLeaderHero(innerArmy);
            }
            for (SoldierProp innerArmySoldierProp : troop.getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(innerArmy.getEntityId(), SceneObjType.SOT_CITY_ARMY_OTHER), innerArmySoldierProp);
            }
            getBattleRole().refreshTroop();
        }
    }

    private void setLeaderHero(ArmyEntity armyEntity) {
        if (armyEntity == null) {
            getTroop().getMainHero().setHeroId(0);
            getTroop().getDeputyHero().setHeroId(0);
            getBattleRole().setMainHero(null);
            getBattleRole().setDeputyHero(null);
            return;
        }
        TroopProp troop = armyEntity.getProp().getTroop();
        if (troop.getMainHero().getHeroId() != 0) {
            getTroop().getMainHero().mergeFromSs(troop.getMainHero().getCopySsBuilder().build());
            getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
        } else {
            getTroop().getMainHero().setHeroId(0);
            getBattleRole().setMainHero(null);
        }
        if (troop.getDeputyHero().getHeroId() != 0) {
            getTroop().getDeputyHero().mergeFromSs(troop.getDeputyHero().getCopySsBuilder().build());
            getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
        } else {
            getTroop().getDeputyHero().setHeroId(0);
            getBattleRole().setDeputyHero(null);
        }
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
        if (!battleResult.alive) {
            // 切阵营
            getOwner().getProp().setCamp(CommonEnum.Camp.C_DUNGEON_FRIENDLY);
            // 加技能
            getOwner().getEventDispatcher().dispatch(new DieEvent(getEntityId(), getOwner().getBuildingTemplate().getId()));
        }
    }

    @Override
    public void clearAfterSettle() {
        super.clearAfterSettle();
        if (!getBattleRole().hasActiveRelation()) {
            clearBattleData();
        }
    }

    protected void clearBattleData() {
        getBattleRole().clearAllProperty();
        clearTroop();
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {

    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {

    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {

    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        return null;
    }

    @Override
    public DungeonBuildingEntity getOwner() {
        return (DungeonBuildingEntity) super.getOwner();
    }

    @Override
    public List<BattleRole> getGvgMapBuildInnerArmy() {
        return getOwner().getInnerArmyComponent().getInnerArmyList().stream().map(it -> it.getBattleComponent().getBattleRole()).collect(Collectors.toList());
    }
}

package com.yorha.cnc.dungeon.dungeon;

import com.yorha.cnc.dungeon.dungeon.component.*;
import com.yorha.cnc.scene.entity.SceneBuilder;
import com.yorha.cnc.scene.entity.component.*;
import com.yorha.common.constant.BattleConstants;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class DungeonSceneBuilder extends SceneBuilder<DungeonSceneEntity> {
    private final CommonEnum.DungeonType type;

    public DungeonSceneBuilder(CommonEnum.DungeonType type) {
        this.type = type;
    }

    @Override
    public BattleGroundComponent battleGroundComponent(DungeonSceneEntity owner) {
        return new BattleGroundComponent(owner, BattleConstants.BattleGroundType.DUNGEON, this.type);
    }

    @Override
    public DungeonPlayerMgrComponent playerMgrComponent(DungeonSceneEntity owner) {
        return new DungeonPlayerMgrComponent(owner);
    }

    @Override
    public DungeonSceneObjMgrComponent objMgrComponent(DungeonSceneEntity owner) {
        return new DungeonSceneObjMgrComponent(owner);
    }

    @Override
    public AoiMgrComponent aoiMgrComponent(DungeonSceneEntity owner) {
        return new AoiMgrComponent(owner);
    }

    @Override
    public PathFindMgrComponent pathFindMgrComponent(DungeonSceneEntity owner) {
        return super.pathFindMgrComponent(owner);
    }

    public DungeonRallyMgrComponent dungeonRallyMgrComponent(DungeonSceneEntity owner) {
        return new DungeonRallyMgrComponent(owner);
    }

    @Override
    public MarqueeComponent marqueeComponent(DungeonSceneEntity owner) {
        return new DungeonSceneMarqueeComponent(owner);
    }

    @Override
    public ClanMgrComponent clanMgrComponent(DungeonSceneEntity owner) {
        return new DungeonClanMgrComponent(owner);
    }
}

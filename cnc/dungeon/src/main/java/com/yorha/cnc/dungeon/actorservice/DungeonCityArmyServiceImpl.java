package com.yorha.cnc.dungeon.actorservice;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.actorservice.SceneCityArmyServiceImpl;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsSceneCityArmy.*;

/**
 * <AUTHOR>
 */
public class DungeonCityArmyServiceImpl extends SceneCityArmyServiceImpl {
    public DungeonCityArmyServiceImpl(DungeonActor dungeonActor) {
        super(dungeonActor);
    }

    public DungeonActor getDungeonActor() {
        return (DungeonActor) super.getSceneActor();
    }

    @Override
    public DungeonSceneEntity getScene() {
        return getDungeonActor().getSceneWithException();
    }

    @Override
    public long getSceneId() {
        return this.getDungeonActor().getSceneId();
    }

    @Override
    public void handleRepairCityWallAsk(RepairCityWallAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleOutFireCityWallAsk(OutFireCityWallAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleSetCityFallAsk(SetCityFallAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleMoveCityFixedAsk(MoveCityFixedAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleMoveCityRandomAsk(MoveCityRandomAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleMoveCityVerifyAsk(MoveCityVerifyAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleCreateArmyCheckAsk(CreateArmyCheckAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleChangeArmyActionCheckAsk(ChangeArmyActionCheckAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleForcedDefeatArmyAsk(ForcedDefeatArmyAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleCreatePlayerArmyAsk(CreatePlayerArmyAsk ask) {
        /*
        ActorMsgEnvelope context = getDungeonActor().getCurrentEnvelope();
        // 如果是空运  要先check技能的

        if (ask.getParam().getArmyAction().getArmyActionType() == CommonEnum.ArmyActionType.AAT_Transport) {
            AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
            DungeonPlayerEntity player = (DungeonPlayerEntity) scenePlayer;
            DungeonSkillTemplate template = ResHolder.getResService(ExpeditionService.class).getDungeonTransportSkillTemplate();
            player.getSkillComponent().checkSkill(template.getId());
            CreatePlayerArmyAsk.Builder builder = ask.toBuilder();
            ask = builder.build();
            StructPlayer.CreateArmy_C2S_Param param = ask.getParam();
            ArmyFactory.createArmy(getScene(), ask.getPlayerId(), param, ask.getCostEnergy()
                    , (e, army) -> {
                        afterArmyCreated(context, param, e, army);
                        // 先这么着吧，不重要 如果是成功创建 因为其他异常了 不扣就不扣吧   ArmyFactory拿不到DungeonPlayer没办法
                        if (army != null) {
                            player.getSkillComponent().useSkill(template.getId(), null, 0);
                        }
                    }
            );
            return;
        }
         */

        super.handleCreatePlayerArmyAsk(ask);
    }
}

package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerRallyComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.game.gen.prop.ScenePlayerRallyBaseProp;
import com.yorha.proto.Core;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DungeonPlayerRallyComponent extends AbstractScenePlayerRallyComponent {
    private static final Logger LOGGER = LogManager.getLogger(DungeonPlayerRallyComponent.class);

    public DungeonPlayerRallyComponent(DungeonPlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        setCurRallyId(0);
    }

    @Override
    protected ScenePlayerRallyBaseProp getRallyProp() {
        return getOwner().getProp().getRallyModel();
    }

    @Override
    public DungeonPlayerEntity getOwner() {
        return (DungeonPlayerEntity) super.getOwner();
    }

    @Override
    public void postInit() {
        onRallyMaxCapChange(getOwner());
    }

    @Override
    public Core.Code checkJoinRally(long rallyId, long soldierNum) {
        return ErrorCode.FAILED.getCode();
    }

    @Override
    public int getRallyMaxCap(long targetId) {
        return super.getRallyMaxCap(targetId);
    }

    @Override
    public RallyEntity createRally(ArmyEntity armyEntity, long targetId, int waitTime, int costEnergy) {
        LOGGER.info("{} try create Rally for {}, target:{}", getOwner(), armyEntity, targetId);
        // 获取组装好组织者属性的集结prop
        RallyInfoProp rallyInfoProp = buildOrganizerRallyProp();
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);

        // 建立目标数据
        switch (targetEntity.getEntityType()) {
            // 目前只支持到 city
            case ET_City:
                CityEntity city = (CityEntity) targetEntity;
                city.copyToRallyInfo(rallyInfoProp);
                break;
            case ET_Army:
                ArmyEntity army = (ArmyEntity) targetEntity;
                army.copyToRallyInfo(rallyInfoProp);
                break;
            case ET_Monster:
                MonsterEntity monster = (MonsterEntity) targetEntity;
                monster.copyToRallyInfo(rallyInfoProp);
                break;
            default:
                throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }

        rallyInfoProp.setMaxSoldierNum(SceneAddCalc.getRallyCap(armyEntity.getBattleComponent().getBattleRole(), targetEntity));

        RallyEntity rallyEntity = new RallyEntity(getOwner().getScene(), null, rallyInfoProp, armyEntity, targetEntity, costEnergy);
        rallyEntity.start(waitTime);
        setCurRallyId(rallyEntity.getEntityId());
        getOwner().getScene().getRallyMgrComponent().addRally(rallyEntity, targetEntity);
        return rallyEntity;
    }

    @Override
    public void onRallyDelete(RallyEntity rallyEntity) {
        super.onRallyDelete(rallyEntity);
        getOwner().getScene().getRallyMgrComponent().removeRally(rallyEntity);
    }

    @Override
    public RallyEntity getRallyEntity(long clanId, long rallyId) {
        return getRallyEntity(rallyId);
    }

    @Override
    public RallyEntity getRallyEntity(long rallyId) {
        return getOwner().getScene().getRallyMgrComponent().getRally(rallyId);
    }

    /**
     * 检测能否发起集结
     *
     * @param targetId   目标id
     * @param waitTime   准备时间
     * @param soldierNum 士兵数目
     * @return ErrorCode
     */
    @Override
    public Core.Code checkCreateRally(long targetId, int waitTime, long soldierNum) {
        Core.Code code = super.checkCreateRally(targetId, waitTime, soldierNum);
        // 不对直接返回
        if (code != ErrorCode.OK.getCode()) {
            return code;
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 是否能援助
     */
    @Override
    public Core.Code checkAssist(long targetId, long soldierNum, long armyId) {
        return super.checkAssist(targetId, soldierNum, armyId);
    }

}

package com.yorha.cnc.dungeon.dungeonPlayer.component;

import com.yorha.cnc.dungeon.dungeonPlayer.DungeonPlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerCityMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWallComponent;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;

/**
 * <AUTHOR>
 */
public class DungeonPlayerCityComponent extends AbstractScenePlayerCityMgrComponent {

    public DungeonPlayerCityComponent(DungeonPlayerEntity owner) {
        super(owner);
    }

    @Override
    public DungeonPlayerEntity getOwner() {
        return (DungeonPlayerEntity) super.getOwner();
    }

    @Override
    public void onNewMainCity(CityEntity city) {
        super.onNewMainCity(city);
        AbstractScenePlayerWallComponent wallComponent = getOwner().getWallComponent();
        if (wallComponent != null) {
            city.getEventDispatcher().addEventListenerRepeat(wallComponent::onCityBattleEnd, EndAllBattleEvent.class);
        }

    }
}

package com.yorha.cnc.dungeon.dungeonBuilding.common.component;

import com.yorha.cnc.dungeon.dungeonBuilding.common.DungeonBuildingEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.game.gen.prop.CityInnerArmyProp;
import com.yorha.common.exception.GeminiException;

/**
 * <AUTHOR>
 */
public class DungeonBuildingInnerArmyComponent extends SceneObjInnerArmyComponent {
    public DungeonBuildingInnerArmyComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
        // 所有战斗结束 改城内军队状态
        getOwner().getEventDispatcher().addEventListenerRepeat(this::endAllBattle, EndAllBattleEvent.class);
    }

    @Override
    public int getBeAidedMaxNum() {
        return getOwner().getBuildingTemplate().getMaxSoldierNum();
    }

    @Override
    public CityInnerArmyProp getProp() {
        return getOwner().getProp().getInnerArmy();
    }

    @Override
    public void checkCanRepatriateArmy(long playerId, ArmyEntity army, boolean isPermission) {
        // 非城主 也非自己的部队
        if (getOwner().getPlayerId() != playerId && playerId != army.getPlayerId()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
    }

    @Override
    public DungeonBuildingEntity getOwner() {
        return (DungeonBuildingEntity) super.getOwner();
    }
}

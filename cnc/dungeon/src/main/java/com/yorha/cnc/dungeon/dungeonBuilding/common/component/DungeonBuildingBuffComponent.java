package com.yorha.cnc.dungeon.dungeonBuilding.common.component;

import com.yorha.cnc.dungeon.dungeonBuilding.common.DungeonBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.game.gen.prop.Int32BuffMapProp;

/**
 * <AUTHOR>
 */
public class DungeonBuildingBuffComponent extends SceneObjBuffComponent {
    public DungeonBuildingBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuffSys().getBuff();
    }

    @Override
    public DungeonBuildingEntity getOwner() {
        return (DungeonBuildingEntity) super.getOwner();
    }
}

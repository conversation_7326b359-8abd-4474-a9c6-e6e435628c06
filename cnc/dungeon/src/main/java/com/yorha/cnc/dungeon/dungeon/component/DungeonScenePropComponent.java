package com.yorha.cnc.dungeon.dungeon.component;

import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.DungeonSceneProp;
import com.yorha.common.actor.IActorRef;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;

import static com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Dungeon;

/**
 * <AUTHOR>
 */
public class DungeonScenePropComponent extends AbstractComponent<DungeonSceneEntity> {

    public DungeonScenePropComponent(DungeonSceneEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
        initPropListener();
    }

    private DungeonSceneProp getProp() {
        return getOwner().getProp();
    }

    private void initPropListener() {
        getProp().setListener(new PropertyChangeListener(() -> {
            if (!getProp().hasAnyMark()) {
                return;
            }
            ntfChangeToClient();
            getProp().unMarkAll();
        }, getOwner().ownerActor().self()));
        if (getProp().hasAnyMark()) {
            getProp().getListener().trigger(getProp().getClass().getSimpleName());
        }
    }

    /**
     * 属性增量更新通知客户端
     */
    private void ntfChangeToClient() {
        final EntityAttr.Builder entityAttrBuilder = EntityAttr.newBuilder();
        if (getProp().copyChangeToCs(entityAttrBuilder.getDungeonSceneAttrBuilder()) <= 0) {
            return;
        }
        entityAttrBuilder.setEntityType(ET_Dungeon).setEntityId(getEntityId());
        final Entity.EntityNtfMsg.Builder builder = Entity.EntityNtfMsg.newBuilder().setZoneId(getOwner().getZoneId());
        builder.addModEntities(entityAttrBuilder);
        getOwner().getPlayerMgrComponent().broadcastOnlineClientMsg(MsgType.ENTITYNTFMSG, builder.build());
    }

    public void onPlayerLogin(IActorRef sessionRef) {
        Entity.EntityNtfMsg.Builder fullEntityAttrMsgBuilder = Entity.EntityNtfMsg.newBuilder().setZoneId(getOwner().getZoneId());
        EntityAttr.Builder entityAttrBuilder = EntityAttr.newBuilder();
        entityAttrBuilder
                .setEntityType(ET_Dungeon)
                .setEntityId(getEntityId());
        getProp().copyToCs(entityAttrBuilder.getDungeonSceneAttrBuilder());
        fullEntityAttrMsgBuilder.addNewEntities(entityAttrBuilder.build()).setReason(SceneObjectNtfReason.SONR_NONE);
        SessionHelper.sendMsgToSession(sessionRef, ownerActor(), MsgType.ENTITYNTFMSG, fullEntityAttrMsgBuilder.build());
    }
}

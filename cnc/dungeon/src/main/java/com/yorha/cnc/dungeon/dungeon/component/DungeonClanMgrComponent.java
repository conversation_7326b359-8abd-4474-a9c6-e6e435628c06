package com.yorha.cnc.dungeon.dungeon.component;

import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.ClanMgrComponent;
import com.yorha.game.gen.prop.ClanFlagInfoProp;

/**
 * 副本联盟管理
 *
 * <AUTHOR>
 */
public class DungeonClanMgrComponent extends ClanMgrComponent {

    public DungeonClanMgrComponent(SceneEntity owner) {
        super(owner);
    }

    @Override
    public DungeonSceneEntity getOwner() {
        return (DungeonSceneEntity) super.getOwner();
    }

    @Override
    public void formClanFlag(ClanFlagInfoProp clanFlagInfoProp, AbstractScenePlayerEntity player) {
        super.formClanFlag(clanFlagInfoProp, player);
    }
}

package com.yorha.cnc.dungeon.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.actorservice.ScenePlaneServiceImpl;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsScenePlane;

/**
 * 副本飞机
 *
 * <AUTHOR>
 */
public class DungeonPlaneServiceImpl extends ScenePlaneServiceImpl {
    public DungeonPlaneServiceImpl(SceneActor sceneActor) {
        super(sceneActor);
    }

    @Override
    public void handleCreateLogisticsPlaneAsk(SsScenePlane.CreateLogisticsPlaneAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleCheckLogisticsActionAsk(SsScenePlane.CheckLogisticsActionAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleChangeLogisticActionAsk(SsScenePlane.ChangeLogisticActionAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }
}

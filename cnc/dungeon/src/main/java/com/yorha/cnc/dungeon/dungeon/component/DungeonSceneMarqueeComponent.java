package com.yorha.cnc.dungeon.dungeon.component;

import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.entity.component.MarqueeComponent;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.StructPB;
import org.apache.commons.lang3.NotImplementedException;

import javax.annotation.Nullable;

public class DungeonSceneMarqueeComponent extends MarqueeComponent {

    public DungeonSceneMarqueeComponent(DungeonSceneEntity owner) {
        super(owner);
    }

    @Override
    public void sendClanMarquee(final long clanId, final int marqueeId, final StructPB.DisplayDataPB param, @Nullable final StructClanPB.ClanFlagInfoPB flagInfo) {
        throw new NotImplementedException("sendClanMarquee");
    }

    @Override
    public void sendFullServerMarquee(final int zoneId, final int marqueeId, StructPB.DisplayDataPB displayData, StructClanPB.ClanFlagInfoPB flagInfo) {
        throw new NotImplementedException("sendFullServerMarquee");
    }

    @Override
    public void sendWorldMarquee(int marqueeId, StructPB.DisplayDataPB displayData) {
        throw new NotImplementedException("sendWorldMarquee");
    }
}

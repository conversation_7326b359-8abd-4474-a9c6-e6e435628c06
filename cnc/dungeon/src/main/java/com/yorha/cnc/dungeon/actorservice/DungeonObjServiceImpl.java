package com.yorha.cnc.dungeon.actorservice;

import com.yorha.cnc.dungeon.DungeonActor;
import com.yorha.cnc.dungeon.dungeon.DungeonSceneEntity;
import com.yorha.cnc.scene.actorservice.SceneObjServiceImpl;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsSceneObj.*;

/**
 * <AUTHOR>
 */
public class DungeonObjServiceImpl extends SceneObjServiceImpl {
    private final DungeonActor dungeonActor;

    public DungeonObjServiceImpl(DungeonActor dungeonActor) {
        super(dungeonActor);
        this.dungeonActor = dungeonActor;
    }

    @Override
    public DungeonSceneEntity getScene() {
        return dungeonActor.getSceneWithException();
    }

    @Override
    public void handlePlayerSearchMonsterAsk(PlayerSearchMonsterAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleAddMonsterAsk(AddMonsterAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleCheckCanBeAttackAsk(CheckCanBeAttackAsk ask) {
        super.handleCheckCanBeAttackAsk(ask);
    }

    @Override
    public void handleQueryMapBuildingIdAsk(QueryMapBuildingIdAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleGetMonsterNumAsk(GetMonsterNumAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleRefreshActMonsterAsk(RefreshActMonsterAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }
    
    @Override
    public void handleSummonSkynetMonsterAsk(SummonSkynetMonsterAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }
}

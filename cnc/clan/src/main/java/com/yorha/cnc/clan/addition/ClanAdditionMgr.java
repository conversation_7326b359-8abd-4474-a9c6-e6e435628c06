package com.yorha.cnc.clan.addition;

import com.google.common.collect.Maps;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.component.ClanAdditionComponent;
import com.yorha.cnc.clan.component.ClanWareHouseComponent;
import com.yorha.common.addition.AdditionMgrBase;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.SsSceneClan;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ClanAdditionMgr extends AdditionMgrBase<ClanEntity> {
    private static final Map<Integer, ClanAdditionDispatcher> ADDITION_DISPATCH = new HashMap<>();

    static {
        ADDITION_DISPATCH.put(BuffEffectType.ET_ADD_CLAN_MEMBER_NUM_VALUE, ClanAdditionComponent::onClanMemberAdditionChange);
        ADDITION_DISPATCH.put(BuffEffectType.ET_CLAN_OIL_PRODUCE_BUFF_VALUE, ClanAdditionComponent::onClanCurrencyAdditionChange);
        ADDITION_DISPATCH.put(BuffEffectType.ET_CLAN_STEEL_PRODUCE_BUFF_VALUE, ClanAdditionComponent::onClanCurrencyAdditionChange);
        ADDITION_DISPATCH.put(BuffEffectType.ET_CLAN_RARE_EARTH_PRODUCE_BUFF_VALUE, ClanAdditionComponent::onClanCurrencyAdditionChange);
        ADDITION_DISPATCH.put(BuffEffectType.ET_CLAN_TITANIUM_PRODUCE_BUFF_VALUE, ClanAdditionComponent::onClanCurrencyAdditionChange);
        ADDITION_DISPATCH.put(BuffEffectType.ET_CLAN_CRYSTAL_PRODUCE_BUFF_VALUE, ClanAdditionComponent::onClanCurrencyAdditionChange);
        ADDITION_DISPATCH.put(BuffEffectType.ET_CLAN_WAREHOUSE_CAPACITY_PERCENT_VALUE, ClanWareHouseComponent::refreshMaxCapacity);
    }

    public static void dispatchAdditionChange(Map<Integer, Long> newAdditions, ClanEntity entity) {
        for (int additionId : newAdditions.keySet()) {
            if (ADDITION_DISPATCH.containsKey(additionId)) {
                ADDITION_DISPATCH.get(additionId).dispatch(entity, additionId, newAdditions.get(additionId));
            }
        }
    }

    public ClanAdditionMgr(ClanEntity owner) {
        super(owner);
    }

    @Override
    public AdditionSysProp getAdditionSys() {
        return getOwner().getProp().getAdditionSys();
    }

    @Override
    protected void update(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            LOGGER.error("clan:{}, update additions is empty. sourceType:{}", getOwner(), sourceType);
            return;
        }

        Map<Integer, Long> oldAdditions = new HashMap<>(additions.size());
        for (int additionId : additions.keySet()) {
            oldAdditions.put(additionId, getAddition(additionId));
        }
        // 更新加成
        updateAddition(sourceType, additions);

        Map<Integer, Long> newAdditions = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : oldAdditions.entrySet()) {
            long newAdditionValue = getAddition(entry.getKey());
            if (entry.getValue() != newAdditionValue) {
                newAdditions.put(entry.getKey(), newAdditionValue);
                LOGGER.info("clan:{} update addition. additionId:{}, sourceType:{}, value:{} -> {}",
                        getOwner(), entry.getKey(), sourceType, entry.getValue(), getAddition(entry.getKey()));
            }
        }

        // 同步给sceneClan
        Map<Integer, Long> addToSceneClan = additionFilterBySceneClan(additions);
        if (!addToSceneClan.isEmpty()) {
            SsSceneClan.SyncClanAdditionCmd.Builder cmd = SsSceneClan.SyncClanAdditionCmd.newBuilder()
                    .setClanId(getOwner().getEntityId())
                    .setSource(sourceType)
                    .putAllAddition(addToSceneClan);
            getOwner().ownerActor().tellBigScene(cmd.build());
        }

        // 同步给scenePlayer
        Map<Integer, Long> addToScenePlayer = additionFilterByPlayer(additions, true);
        if (!addToScenePlayer.isEmpty()) {
            SsSceneClan.UpdateAdditionFromClanCmd.Builder cmd = SsSceneClan.UpdateAdditionFromClanCmd.newBuilder()
                    .setClanId(getOwner().getEntityId())
                    .setSource(sourceType)
                    .putAllAdditions(addToScenePlayer);
            getOwner().ownerActor().tellBigScene(cmd.build());
        }

        // 通知在线成员player
        Map<Integer, Long> addToPlayer = additionFilterByPlayer(additions, false);
        if (!addToPlayer.isEmpty()) {
            for (Long playerId : getOwner().getMemberComponent().getAllClanOnlinePlayerIds()) {
                SsPlayerClan.OnClanAdditionUpdateCmd.Builder cmd = SsPlayerClan.OnClanAdditionUpdateCmd.newBuilder()
                        .setClanId(getOwner().getEntityId())
                        .setSource(sourceType)
                        .putAllAdditions(addToPlayer);
                getOwner().ownerActor().tellPlayer(playerId, cmd.build());
            }
        }
        // 加成更新后处理
        ClanAdditionMgr.dispatchAdditionChange(newAdditions, getOwner());
    }

    /**
     * 获取需要同步到scene的clan加成
     */
    public static Map<Integer, Long> additionFilterBySceneClan(Map<Integer, Long> additionMap) {
        Map<Integer, Long> res = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : additionMap.entrySet()) {
            // if (AdditionUtil.isClanAddition(entry.getKey()) && AdditionUtil.isSceneAddition(entry.getKey())) {
            //  部分加在军团的加成（如军团建筑建造速度），被配置成了player加成，客户端需要加成值做展示计算，
            //  但同时需要同步给 scene clan，因为 scene clan 那边用到了这些数据来计算，所以这里暂时先同步给 scene clan
            if (AdditionUtil.isSceneAddition(entry.getKey())) {
                res.put(entry.getKey(), entry.getValue());
            }
        }
        return res;
    }

    private static Map<Integer, Long> additionFilterByPlayer(Map<Integer, Long> additions, boolean isScene) {
        Map<Integer, Long> ret = Maps.newHashMap();
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            if (AdditionUtil.isClanAddition(entry.getKey())) {
                continue;
            }
            // isScene=true 返回scene上的加成；isScene=false 返回player上的加成
            var sceneAddition = isScene && AdditionUtil.isSceneAddition(entry.getKey());
            var playerAddition = !isScene && !AdditionUtil.isSceneAddition(entry.getKey());
            if (sceneAddition || playerAddition) {
                ret.put(entry.getKey(), entry.getValue());
            }
        }
        return ret;
    }
}

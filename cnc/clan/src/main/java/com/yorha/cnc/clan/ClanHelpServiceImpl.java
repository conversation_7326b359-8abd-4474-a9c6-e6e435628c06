package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanHelpService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsClanHelp.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ClanHelpServiceImpl implements ClanHelpService {
    private static final Logger LOGGER = LogManager.getLogger(ClanHelpServiceImpl.class);

    private final ClanActor clanActor;

    public ClanHelpServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }

    public long getClanId() {
        return clanActor.getClanId();
    }

    @Override
    public void handleSyncPlayerQueueTaskCmd(SyncPlayerQueueTaskCmd cmd) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleSyncPlayerQueueTaskCmd clan is null={}", getClanId());
            return;
        }
        clanEntity.getHelpComponent().addClanHelpItems(cmd.getTask(), cmd.getPlayerId(), cmd.getHelpRecord(),
                cmd.getTargetLevel(), cmd.getBuildId(), cmd.getMaxHelpTimes());
    }

    @Override
    public void handleFetchClanHelpsAsk(FetchClanHelpsAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        FetchClanHelpsAns.Builder ans = FetchClanHelpsAns.newBuilder();
        ans.putAllItems(clanEntity.getHelpComponent().getAllCanHelpItems(ask.getPlayerId()));
        clanActor.answer(ans.build());
    }

    @Override
    public void handleFinishAllClanHelpsAsk(FinishAllClanHelpsAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        int successHelpTimes = clanEntity.getHelpComponent().finishAllClanHelps(ask.getPlayerId(), ask.getPlayerName());
        FinishAllClanHelpsAns.Builder ans = FinishAllClanHelpsAns.newBuilder().setSuccessHelpTimes(successHelpTimes);
        clanActor.answer(ans.build());
    }

    @Override
    public void handleOnQueueTaskFinishedCmd(OnQueueTaskFinishedCmd ask) {
        if (!ask.hasQueueId()) {
            LOGGER.error("queueId is needed in queue task finished cmd");
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleOnQueueTaskFinishedCmd clan is null={}", getClanId());
            return;
        }
        clanEntity.getHelpComponent().delClanHelpItem(ask.getQueueId());
    }

    @Override
    public void handleNeedClanHelpItemIdsNtfCmd(NeedClanHelpItemIdsNtfCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleNeedClanHelpItemIdsNtfCmd clan is null={}", getClanId());
            return;
        }
        clanEntity.getHelpComponent().trySyncCanHelpItemIds(ask.getPlayerId());
    }
}

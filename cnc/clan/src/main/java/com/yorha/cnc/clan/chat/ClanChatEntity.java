package com.yorha.cnc.clan.chat;

import com.yorha.cnc.clan.ClanActor;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.chat.ChatHelper;
import com.yorha.common.chat.MegaGroupChatEntity;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.io.MsgType;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.SsClanChat;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;

/**
 * 联盟聊天manager
 *
 * <AUTHOR>
 */

public class ClanChatEntity extends MegaGroupChatEntity {
    /**
     * 懒加载，只有get时才会将最新的200条消息取出
     */
    private boolean isLoaded;

    public ClanChatEntity(ClanActor actor) {
        super(actor);
        this.isLoaded = false;
    }

    @Override
    public String getIdFactoryKey() {
        return DbUtil.idFactoryTableKeyClanChat + this.getChannelId();
    }

    @Override
    public CommonEnum.ChatChannel chatChannel() {
        return CommonEnum.ChatChannel.CC_CLAN;
    }

    @Override
    public String getChannelId() {
        return String.valueOf(((ClanActor) this.actor).getClanId());
    }

    @Override
    protected void broadcastMessage(CommonMsg.ChatMessage message) {
        // 如果是@消息，拉起被@的玩家设置firstAtIndex
        if (message.getType() == CommonEnum.MessageType.MT_AT) {
            this.handleAtMsg(message);
        }
        // 通知在线先不过滤被屏蔽了发送人的玩家，由客户端拦截
        final PlayerChat.Player_BoradcastChatMessage_NTF.Builder builder = PlayerChat.Player_BoradcastChatMessage_NTF.newBuilder();
        builder.getChatSessionBuilder().setChannelType(chatChannel()).setChatChannelId(this.getChannelId());
        builder.setChatMessage(message);
        BroadcastHelper.toCsOnlinePlayerInClan(this.actor.getZoneId(), this.getActor().getClanId(), MsgType.PLAYER_BORADCASTCHATMESSAGE_NTF, builder.build());
    }

    @Override
    protected void answerChatRequest(@Nullable ActorMsgEnvelope context, CommonMsg.ChatMessage message, Throwable throwable) {
        if (context == null) {
            return;
        }
        if (throwable != null) {
            this.actor.answerWithException(context, (Exception) throwable);
            return;
        }
        this.actor.answerWithContext(context, SsClanChat.SendClanChatAns.newBuilder().setMessageId(message.getMessageId()).build());
    }

    @Override
    public void queryChatMsgs(long fromId, final long toId, final List<Long> shieldList, final ActorMsgEnvelope context) {
        this.loadCacheWhenUnload();
        if (fromId == 0) {
            fromId = this.getLastMsgId();
        }
        final List<CommonMsg.ChatMessage> chatMessages = ChatHelper.queryChatMsgFromCache(fromId, toId, shieldList, this.chatMsgCache);
        SsClanChat.FetchClanChatAns.Builder builder = SsClanChat.FetchClanChatAns.newBuilder();
        builder.addAllChatMsgs(chatMessages);
        actor.answerWithContext(context, builder.build());
    }

    private ClanActor getActor() {
        return (ClanActor) this.actor;
    }

    private void loadCacheWhenUnload() {
        if (this.isLoaded) {
            return;
        }
        final long fromId = this.getLastMsgId();
        final long toId = Math.max(0, this.getLastMsgId() - this.chatMsgCache.cacheCapacity() + 1);
        final List<CommonMsg.ChatMessage> chatMessages = ChatHelper.queryChatMsgSync(
                this.actor, this.chatChannel().getNumber(), String.valueOf(this.getActor().getClanId()), fromId, toId, Collections.emptySet());
        for (final CommonMsg.ChatMessage chatMessage : chatMessages) {
            this.chatMsgCache.putData(chatMessage.getMessageId(), chatMessage);
        }
        this.isLoaded = true;
    }
}

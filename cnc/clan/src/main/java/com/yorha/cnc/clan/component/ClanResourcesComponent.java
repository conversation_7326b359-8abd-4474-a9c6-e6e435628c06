package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.ClanResourcesProp;
import com.yorha.game.gen.prop.SingleClanResourceInfoProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.ClanResourceType;
import com.yorha.proto.PlayerClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 联盟资源
 *
 * <AUTHOR>
 */
public class ClanResourcesComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanResourcesComponent.class);

    public ClanResourcesComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 结算下联盟资源产出
     */
    public void settleClanResources() {
        if (TimeUtils.isAfterNow(getProp().getLastCalTsMs())) {
            LOGGER.debug("last cal time {} is after now", getProp().getLastCalTsMs());
            return;
        }
        long deltaMs = TimeUtils.getDeltaMs(getProp().getLastCalTsMs());
        getProp().setLastCalTsMs(SystemClock.now());
        for (Map.Entry<Integer, ClanResourceType> entry : AdditionConstants.CLAN_RESOURCES_EFFECT.entrySet()) {
            long addition = getOwner().getAddComponent().getAddition(entry.getKey());
            long addValue = addition * deltaMs / Duration.ofHours(1).toMillis();
            if (addValue < 0) {
                LOGGER.error("{} add addClanResources error. type: {} addition:{} deltaMs:{}", getOwner(), entry.getValue(), addition, deltaMs);
                continue;
            }
            addResources(entry.getValue(), addValue);
        }
        LOGGER.info("{} settleClanResources time:{} {}", getOwner(), deltaMs, getProp().getResourceInfos().toString());
        updateClanResourcesToClanMembers();
    }

    /**
     * 设置资源容量最大值，不会将超出最大值的资源设置为最大值
     *
     * @param type        资源类型
     * @param newCapacity 新的资源容量最大值
     */
    public void setMaxCapacityWithType(int type, long newCapacity) {
        if (getProp().getWarehouseMapV(type) == null) {
            LOGGER.error("type {} not exist, check the call", type);
            return;
        }
        long nowCapacity = getProp().getWarehouseMapV(type).getMaxCount();
        if (nowCapacity != newCapacity) {
            getProp().getWarehouseMapV(type).setMaxCount(newCapacity);
            LOGGER.info("set max capacity {} for type {}, before is {}", newCapacity, type, nowCapacity);
        }
    }

    /**
     * 同步联盟资源变更
     */
    public void updateClanResourcesToClanMembers() {
        PlayerClan.Player_UpdateClanInfo_NTF.Builder builder = PlayerClan.Player_UpdateClanInfo_NTF.newBuilder();
        builder.setResources(getProp().getCopyCsBuilder());
        getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(MsgType.PLAYER_UPDATECLANINFO_NTF, builder.build());
    }

    /**
     * 增加资源
     * NOTE(furson): 这个接口目前不能增加军团积分，请使用单独接口增加军团积分
     */
    public void addResources(ClanResourceType type, long addV) {
        if (addV == 0) {
            LOGGER.info("{} addClanResources. type:{} add:{}", getOwner(), type, addV);
            return;
        }
        if (type == ClanResourceType.CNRT_CLAN_SCORE) {
            LOGGER.error("cannot use this func to add clan score, check the call logic or config");
            return;
        }
        long nowCount = 0;
        if (getProp().getResourceInfos().containsKey(type.getNumber())) {
            nowCount = getProp().getResourceInfos().get(type.getNumber()).getCount();
        }
        long maxStoreNum = 0;
        if (getProp().getWarehouseMap().containsKey(type.getNumber())) {
            maxStoreNum = getProp().getWarehouseMapV(type.getNumber()).getMaxCount();
        }
        long realAdd = Math.min(addV, maxStoreNum - nowCount);
        if (!getProp().getResourceInfos().containsKey(type.getNumber())) {
            getProp().getResourceInfos().addEmptyValue(type.getNumber()).setCount(realAdd);
            LOGGER.info("{} addClanResources. type:{} old:0 add:{}", getOwner(), type, realAdd);
            return;
        }
        long beforeCount = getProp().getResourceInfos().get(type.getNumber()).getCount();
        long afterCount = beforeCount + realAdd;
        getProp().getResourceInfos().get(type.getNumber()).setCount(afterCount);
        LOGGER.info("{} addClanResources. type:{} old:{} add:{}", getOwner(), type, beforeCount, realAdd);
        // 不要打开，不要打开，不要打开
        // getOwner().getClanQlogComponent().qlogClanRes(type.getNumber(), beforeCount, afterCount, realAdd, 1, action, "");
    }


    /**
     * 检查资源，可以检查资源；用于特殊情况下的先检查后扣除，扣除没有单独的接口，请再调用先检查再扣除
     *
     * @param cost 资源类型 -> 资源量 键值对
     */
    public void checkClanResources(List<IntPairType> cost) {
        for (IntPairType pair : cost) {
            checkSingleResource(pair.getKey(), pair.getValue());
        }
    }

    /**
     * 检查资源并扣除对应资源，可以扣除军团积分
     *
     * @param action   资源消耗的qlog流水字段
     * @param cost     资源类型 -> 资源量 键值对
     * @param targetId 检查扣除资源的目标id，仅用于记录qlog
     */
    public void checkAndDecClanResources(String action, List<IntPairType> cost, long targetId) {
        for (IntPairType pair : cost) {
            checkSingleResource(pair.getKey(), pair.getValue());
        }
        for (IntPairType pair : cost) {
            if (pair.getKey() != ClanResourceType.CNRT_CLAN_SCORE_VALUE) {
                decSingleResource(action, pair.getKey(), pair.getValue(), targetId);
            } else {
                decClanScore(action, pair.getValue(), targetId);
            }
        }
        updateClanResourcesToClanMembers();
    }

    /**
     * 检查某种资源够不够  不够直接抛异常
     */
    private void checkSingleResource(int type, long value) {
        if (!getProp().getResourceInfos().containsKey(type)) {
            throw new GeminiException(ErrorCode.CLAN_RESOURCE_NOT_ENOUGH);
        }
        if (getProp().getResourceInfos().get(type).getCount() < value) {
            throw new GeminiException(ErrorCode.CLAN_RESOURCE_NOT_ENOUGH);
        }
    }

    /**
     * 扣除资源通用接口
     * 不能用来扣除军团积分，军团积分目前单独处理
     *
     * @param type 扣除的资源类型
     * @param decV 减少的资源数量
     */
    private void decSingleResource(String action, int type, long decV, long targetId) {
        if (type == ClanResourceType.CNRT_CLAN_SCORE_VALUE) {
            LOGGER.error("type {} shouldn't dec use this func", type);
            return;
        }
        long beforeCount = getProp().getResourceInfos().get(type).getCount();
        if (beforeCount < decV) {
            return;
        }
        long afterCount = beforeCount - decV;
        getProp().getResourceInfos().get(type).setCount(afterCount);
        LOGGER.info("{} decResources. type:{} old:{} dec:{}", getOwner(), type, beforeCount, decV);
        getOwner().getClanQlogComponent().qlogClanRes(type, beforeCount, afterCount, decV, 1, action, String.valueOf(targetId));
    }

    /**
     * 增加军团积分
     *
     * @param addScore 要增加的军团分数
     */
    public void addClanScore(String action, long addScore, long targetId) {
        long beforeScore = getClanScore();
        setClanScore(beforeScore + addScore);
        long nowScore = getClanScore();
        LOGGER.info("add clan score, now {}, addScore {}, after {}", beforeScore, addScore, nowScore);
        getOwner().getClanQlogComponent().qLogClanScore(action, beforeScore, nowScore, addScore, 0, targetId);
    }

    /**
     * 减少军团积分
     *
     * @param action   行为
     * @param decScore 要减少的军团分数
     * @param targetId 减少积分行为的目标id，仅用于记录qlog
     */
    public void decClanScore(String action, long decScore, long targetId) {
        long beforeScore = getClanScore();
        setClanScore(beforeScore - decScore);
        long nowScore = getClanScore();
        LOGGER.info("dec clan score, beforeScore {}, decScore {}, after {}", beforeScore, decScore, nowScore);
        getOwner().getClanQlogComponent().qLogClanScore(action, beforeScore, nowScore, decScore, 1, targetId);
    }

    /**
     * @return 快捷获取军团积分
     */
    public long getClanScore() {
        SingleClanResourceInfoProp prop = getProp().getResourceInfosV(ClanResourceType.CNRT_CLAN_SCORE_VALUE);
        return prop == null ? 0L : prop.getCount();
    }

    /**
     * 设置军团积分
     *
     * @param clanScore 要设置的军团积分
     */
    private void setClanScore(long clanScore) {
        if (!getProp().getResourceInfos().containsKey(ClanResourceType.CNRT_CLAN_SCORE_VALUE)) {
            getProp().getResourceInfos()
                    .addEmptyValue(ClanResourceType.CNRT_CLAN_SCORE_VALUE)
                    .setType(ClanResourceType.CNRT_CLAN_SCORE_VALUE);
        }
        getProp().getResourceInfos().get(ClanResourceType.CNRT_CLAN_SCORE_VALUE).setCount(clanScore);
    }

    /**
     * 清理水晶资源
     */
    public void clearCrystalResource() {
        SingleClanResourceInfoProp v = getProp().getResourceInfosV(ClanResourceType.CNRT_CRYSTAL_VALUE);
        if (v == null) {
            return;
        }
        getProp().removeResourceInfosV(ClanResourceType.CNRT_CRYSTAL_VALUE);
        LOGGER.info("clearCrystalResource num={}", v.getCount());
    }

    public ClanResourcesProp getProp() {
        return getOwner().getProp().getResources();
    }
}

package com.yorha.cnc.clan.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.io.MsgType;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ClanHelpItemProp;
import com.yorha.game.gen.prop.Int64ClanHelpItemMapProp;
import com.yorha.game.gen.prop.PlayerCardHeadProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan.Player_ClanHelpInfoUpdate_NTF;
import com.yorha.proto.PlayerClan.Player_SyncCanHelpItemIds_NTF;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk;
import com.yorha.proto.Struct.ClanHelpItem;
import com.yorha.proto.Struct.ClanRecord;
import com.yorha.proto.StructPB.ClanHelpItemPB;
import com.yorha.proto.StructPlayer.QueueTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ClanHelpComponent extends ClanComponent {

    private static final Logger LOGGER = LogManager.getLogger(ClanHelpComponent.class);

    private final Map<Long, Set<Long>> playerIdToHelpItemIds = new HashMap<>();
    private final Set<Long> allCanHelpItemIds = new HashSet<>();

    public ClanHelpComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 联盟加载后
     */
    @Override
    public void onLoad() {
        refreshInnerMap();
    }

    /**
     * 创建联盟时
     */
    @Override
    public void onCreate() {
        // do nothing
    }

    /**
     * 联盟解散时
     */
    @Override
    public void onDissolution() {
        playerIdToHelpItemIds.clear();
        allCanHelpItemIds.clear();
    }

    private void refreshInnerMap() {
        Int64ClanHelpItemMapProp helpItemMap = getOwner().getProp().getHelps();
        allCanHelpItemIds.addAll(helpItemMap.keySet());
        for (long id : helpItemMap.keySet()) {
            long playerId = helpItemMap.get(id).getPlayerId();
            if (playerIdToHelpItemIds.containsKey(playerId)) {
                playerIdToHelpItemIds.get(playerId).add(id);
            } else {
                playerIdToHelpItemIds.put(playerId, Sets.newHashSet(id));
            }
            // 恢复ClanHelpItem里的头像信息
            ClanHelpItemProp helpItem = helpItemMap.get(id);
            helpItem.getCardHead().mergeFromSs(getOwner().getMemberComponent().getMemberCardHead(playerId));
        }
    }

    /**
     * 添加一个联盟帮助请求
     */
    public void addClanHelpItems(QueueTask task, long playerId, ClanRecord record, int targetLevel, long buildId, int maxHelpTimes) {
        // 重复添加或id重复，大概率来自客户端重复发送协议
        if (getOwner().getProp().getHelpsV(task.getId()) != null) {
            LOGGER.warn("task have already been set {}", task.getId());
            return;
        }
        ClanHelpItemProp itemProp = new ClanHelpItemProp();
        // 设置默认属性
        itemProp.setId(task.getId()).setHelpType(task.getType()).setAlreadyHelpTimes(0)
                .setTotalReductionTime(0L).setPlayerId(playerId).setTargetLevel(targetLevel)
                .setBuildId(buildId).setTotalHelpTimes(maxHelpTimes)
                .setCreateTsMs(SystemClock.now());
        // 设置铭牌。NOTE(furson): 能省一点是一点，简单的就不要prop->ss->prop了
        PlayerCardHeadProp cardHeadProp = getOwner().getMemberComponent().getCardHeadPropById(playerId);
        if (cardHeadProp == null) {
            LOGGER.error("addClanHelpItems: player card head prop is null in clan {}, playerId {}", getOwner(), playerId);
            return;
        }
        itemProp.getCardHead().setName(cardHeadProp.getName())
                .setPic(cardHeadProp.getPic())
                .setPicFrame(cardHeadProp.getPicFrame())
                .setPicUrl(cardHeadProp.getPicUrl());

        // 设置帮助记录
        itemProp.getHelpRecord().mergeFromSs(record);
        getOwner().getProp().putHelpsV(itemProp);
        LOGGER.info("add help: itemId {}, playerId {}", task.getId(), playerId);

        // 加入到内存映射中
        if (playerIdToHelpItemIds.containsKey(playerId)) {
            playerIdToHelpItemIds.get(playerId).add(task.getId());
        } else {
            playerIdToHelpItemIds.put(playerId, Sets.newHashSet(task.getId()));
        }
        allCanHelpItemIds.add(task.getId());
        // 同步新增消息给军团所有在线玩家
        Player_ClanHelpInfoUpdate_NTF.Builder addHelpNtfBuilder = Player_ClanHelpInfoUpdate_NTF.newBuilder();
        addHelpNtfBuilder.addHelpItems(itemProp.getCopyCsBuilder().build());
        sendHelpUpdateNtfToClan(addHelpNtfBuilder);
    }

    /**
     * 仅当队列完成时，删除内存及军团上的信息，并同步删除消息给玩家
     */
    public void delClanHelpItem(long taskId) {
        // 删除clan prop中的信息
        if (getOwner().getProp().getHelpsV(taskId) == null) {
            // 对应队列未发起帮助或玩家更换联盟后此处可能为空
            LOGGER.debug("try del not exist clan help items, taskId is {}", taskId);
            return;
        }
        ClanHelpItemProp needDelProp = getOwner().getProp().getHelpsV(taskId);
        long playerId = needDelProp.getPlayerId();
        getOwner().getProp().removeHelpsV(taskId);

        // 删除内存映射信息
        if (!playerIdToHelpItemIds.containsKey(playerId)) {
            // 若帮助信息存在，内存映射不存在，则数据有问题
            LOGGER.error("no memory map with playerId {} when delete task {}", playerId, taskId);
            return;
        }
        playerIdToHelpItemIds.get(playerId).remove(taskId);
        allCanHelpItemIds.remove(taskId);

        LOGGER.debug("del {} when queue finished", needDelProp.getId());
        // TODO(furson): 可以只发给玩家自己，不需要广播
        // 同步删除消息给军团所有在线玩家
        Player_ClanHelpInfoUpdate_NTF.Builder delHelpNtfBuilder = Player_ClanHelpInfoUpdate_NTF.newBuilder();
        delHelpNtfBuilder.addHelpItemsBuilder().setId(needDelProp.getId()).setIsCanceled(true);
        sendHelpUpdateNtfToClan(delHelpNtfBuilder);
    }


    /**
     * 退盟时，删除已发起的帮助项
     */
    public void delAllHelpsWhenQuitClan(long playerId) {
        // 无帮助情况
        if (!playerIdToHelpItemIds.containsKey(playerId)) {
            return;
        }
        // 有帮助未完成情况
        Player_ClanHelpInfoUpdate_NTF.Builder delHelpNtfBuilder = Player_ClanHelpInfoUpdate_NTF.newBuilder();
        for (long taskId : playerIdToHelpItemIds.get(playerId)) {
            LOGGER.debug("task {} have been removed when player {} quit clan", taskId, playerId);
            getOwner().getProp().removeHelpsV(taskId);
            delHelpNtfBuilder.addHelpItemsBuilder().setId(taskId).setIsCanceled(true);
            allCanHelpItemIds.remove(taskId);
        }
        playerIdToHelpItemIds.remove(playerId);
        // 同步删除消息给军团所有在线玩家
        sendHelpUpdateNtfToClan(delHelpNtfBuilder);
    }

    /**
     * 尝试使用playerId发送同步所有当前联盟可帮助的帮助项的ntf
     *
     * @param playerId 玩家id
     */
    public void trySyncCanHelpItemIds(long playerId) {
        final GeneratedMessageV3 msg = buildSyncHelpItemIdsNtf(playerId).build();
        getOwner().getMemberComponent().sendCsMsgToMemberClient(
                playerId,
                MsgType.PLAYER_SYNCCANHELPITEMIDS_NTF,
                msg
        );
    }

    private Player_SyncCanHelpItemIds_NTF.Builder buildSyncHelpItemIdsNtf(long playerId) {
        Player_SyncCanHelpItemIds_NTF.Builder builder = Player_SyncCanHelpItemIds_NTF.newBuilder();
        for (Long itemId : allCanHelpItemIds) {
            ClanHelpItemProp prop = getPlayerCanHelpItem(playerId, itemId);
            if (prop == null) {
                continue;
            }
            builder.addHelpItemIds(prop.getId());
        }
        return builder;
    }

    /**
     * 判断帮助项是否是玩家自己的发起的
     */
    private boolean isHelpCreatedByPlayer(long playerId, long itemId) {
        Set<Long> myHelpItemIds = playerIdToHelpItemIds.get(playerId);
        if (null == myHelpItemIds) {
            return false;
        }
        // 过滤掉自己的任务
        return myHelpItemIds.contains(itemId);
    }


    /**
     * 判断帮助项玩家是否已帮助过
     */
    private boolean isThisPlayerAlreadyHelped(ClanHelpItemProp prop, long playerId) {
        return prop.getAlreadyHelpPlayerIds().contains(playerId);
    }


    /**
     * 过滤获取联盟帮助项
     *
     * @param playerId 发起完成请求的玩家id，用于过滤当前玩家自己的请求与已经帮助过的请求
     * @param itemId   用于判断的联盟帮助项id
     */
    private ClanHelpItemProp getPlayerCanHelpItem(long playerId, long itemId) {
        // 过滤掉玩家自己发起的帮助
        if (isHelpCreatedByPlayer(playerId, itemId)) {
            LOGGER.debug("item {} is {}'s own item", itemId, playerId);
            return null;
        }
        // 帮助项是否存在
        ClanHelpItemProp prop = getOwner().getProp().getHelpsV(itemId);
        if (prop == null) {
            LOGGER.error("get help item {} null while fetch all clan help items using cache", itemId);
            return null;
        }
        // 过滤掉玩家已经帮助过的帮助
        if (isThisPlayerAlreadyHelped(prop, playerId)) {
            LOGGER.debug("item {} already helped", itemId);
            return null;
        }
        // 已经达到最大帮助次数，跳过
        if (prop.getAlreadyHelpTimes() >= prop.getTotalHelpTimes()) {
            LOGGER.debug("item {} meets max help times", itemId);
            return null;
        }
        return prop;
    }

    /**
     * 获取联盟帮助
     */
    private Map<Long, ClanHelpItem> getPlayerOwnHelpItems(long playerId) {
        Map<Long, ClanHelpItem> res = new HashMap<>();
        if (playerIdToHelpItemIds.get(playerId) == null) {
            return res;
        }
        for (Long itemId : playerIdToHelpItemIds.get(playerId)) {
            // 帮助项是否存在
            ClanHelpItemProp prop = getOwner().getProp().getHelpsV(itemId);
            if (prop == null) {
                LOGGER.error("get help item {} null while fetch all clan help items using cache", itemId);
                continue;
            }
            res.put(itemId, prop.getCopySsBuilder().build());
        }
        return res;
    }

    /**
     * 获取所有可以帮助的帮助项
     *
     * @param playerId 发起完成请求的玩家id，用于过滤当前玩家自己的请求与已经帮助过的请求
     */
    public Map<Long, ClanHelpItem> getAllCanHelpItems(long playerId) {
        Map<Long, ClanHelpItem> res = getPlayerOwnHelpItems(playerId);
        // 添加非自己其他的可帮助项
        for (Long itemId : allCanHelpItemIds) {
            ClanHelpItemProp prop = getPlayerCanHelpItem(playerId, itemId);
            if (null != prop) {
                res.put(itemId, prop.getCopySsBuilder().build());
            }
        }
        return res;
    }


    /**
     * 完成所有联盟帮助
     *
     * @param playerId 发起完成请求的玩家id，用于过滤当前玩家自己的请求
     * @return 返回成功帮助的次数
     */
    public int finishAllClanHelps(long playerId, String playerName) {
        List<Long> successHelpIds = new ArrayList<>();
        for (Long helpItemId : getAllCanHelpItems(playerId).keySet()) {
            ClanHelpItemProp prop = getPlayerCanHelpItem(playerId, helpItemId);
            if (null == prop) {
                continue;
            }

            // 玩家可帮助的情况认为玩家一定能完成该项帮助
            LOGGER.debug("player {} success help {}'s help", playerId, prop.getPlayerId());
            prop.getAlreadyHelpPlayerIds().add(playerId);
            prop.setAlreadyHelpTimes(prop.getAlreadyHelpTimes() + 1);
            boolean thisHelpNeedCancel = false;
            if (prop.getAlreadyHelpTimes() >= prop.getTotalHelpTimes()) {
                thisHelpNeedCancel = true;
            }
            successHelpIds.add(prop.getId());
            // 执行真正的帮助逻辑
            finishSingleHelp(playerId, playerName, prop, thisHelpNeedCancel);
        }
        // 有成功帮助次数时
        if (successHelpIds.size() > 0) {
            // 增加个人军团积分
            addHelpPersonalClanScore(playerId, successHelpIds.size());
            // 增加军团积分
            addHelpClanScore(successHelpIds.size());
            // 发送帮助更新ntf给发起请求的玩家
            sendHelpCanceledNtfToPlayer(playerId, successHelpIds);
        }
        return successHelpIds.size();
    }

    public void gmAddHelpTimes(long playerId, int times) {
        Set<Long> myHelpItemSet = playerIdToHelpItemIds.get(playerId);
        for (Long itemId : myHelpItemSet) {
            ClanHelpItemProp prop = getOwner().getProp().getHelpsV(itemId);
            if (prop == null) {
                LOGGER.error("get help item {} null while fetch all clan help items using cache", itemId);
                continue;
            }
            prop.setAlreadyHelpTimes(prop.getAlreadyHelpTimes() + times);
        }
    }

    /**
     * 增加个人通过成功帮助获得的联盟积分
     *
     * @param playerId           需要添加积分的玩家id
     * @param successFinishCount 成功完成的帮助数量
     */
    private void addHelpPersonalClanScore(long playerId, int successFinishCount) {
        SsPlayerClan.OnAddClanScoreCmd.Builder cmd = SsPlayerClan.OnAddClanScoreCmd.newBuilder();
        cmd.setScoreType(CommonEnum.ClanScoreCategory.CSC_HELP)
                .setAddValue(geSingleHelpScore() * successFinishCount);
        ownerActor().tellPlayer(playerId, cmd.build());
        syncHelpTimesToRank(playerId, successFinishCount);
    }

    /**
     * 增加联盟通过帮助行为获得的积分
     *
     * @param successFinishCount 成功完成的帮助数量
     */
    private void addHelpClanScore(int successFinishCount) {
        getOwner().getResourcesComponent().addClanScore("guild_help", (long) successFinishCount * geSingleHelpScore(), 0L);
    }


    /**
     * 联盟帮助次数排行榜同步
     *
     * @param playerId     玩家id
     * @param addHelpTimes 本次增加的帮助次数
     */
    private void syncHelpTimesToRank(long playerId, int addHelpTimes) {
        getOwner().getRankComponent().updateClanRanking(RankConstant.CLAN_PLAYER_HELP_RANK, true, playerId, addHelpTimes);
    }

    /**
     * 完成单个联盟帮助
     *
     * @param helpPlayerId       点击完成帮助的玩家id
     * @param helpPlayerName     点击完成帮助的玩家名字
     * @param prop               被完成的联盟帮助项
     * @param thisHelpIsCanceled 是否需要取消这个帮助
     */
    private void finishSingleHelp(long helpPlayerId, String helpPlayerName, ClanHelpItemProp prop, boolean thisHelpIsCanceled) {
        LOGGER.debug("try finish Single help {}", prop.getId());
        long targetPlayerId = prop.getPlayerId();
        OnClanHelpHappenAsk.Builder ask = OnClanHelpHappenAsk.newBuilder();
        ask.setQueueId(prop.getId());
        ownerActor().<SsPlayerClan.OnClanHelpHappenAns>askPlayer(ownerActor().getZoneId(), targetPlayerId, ask.build())
                .onComplete((ans, err) -> {
                            if (err != null) {
                                LOGGER.error("player {} help id {} failed", helpPlayerId, prop.getId(), err);
                                return;
                            }
                            if (!ans.getIsSuccessReduce()) {
                                LOGGER.info("player {} help id {} failed, but add score", helpPlayerId, prop.getId());
                                return;
                            }
                            LOGGER.info("player {} help id {} success", helpPlayerId, prop.getId());
                            long realSpeedTimeSec = ans.getReduceTime();
                            prop.setTotalReductionTime(prop.getTotalReductionTime() + realSpeedTimeSec);
                            // 如果回调时，clan已经不再包含帮助项，则认为帮助项已经被完成或取消，需要把取消的标志位设置在更新的ntf中
                            if (!allCanHelpItemIds.contains(prop.getId())) {
                                sendUpdateNtfOnClanHelpFinished(helpPlayerId, helpPlayerName, prop, true);
                            } else {
                                sendUpdateNtfOnClanHelpFinished(helpPlayerId, helpPlayerName, prop, thisHelpIsCanceled);
                            }
                        }
                );
    }

    /**
     * 获取单次帮助分数
     *
     * @return 返回单次帮助分数
     */
    private int geSingleHelpScore() {
        ConstClanTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        return constTemplate.getEachHelpPoint();
    }

    /**
     * 发送取消帮助的信息给点击"完成所有帮助"的玩家
     *
     * @param playerId          玩家id
     * @param needCancelHelpIds 需要取消的帮助列表
     */
    private void sendHelpCanceledNtfToPlayer(long playerId, List<Long> needCancelHelpIds) {
        Player_ClanHelpInfoUpdate_NTF.Builder toHelpPlayerNtfBuilder = Player_ClanHelpInfoUpdate_NTF.newBuilder();

        // 构建给发起完成帮助的玩家取消对应帮助的ntf
        for (long successHelpId : needCancelHelpIds) {
            ClanHelpItemPB.Builder toHelpPlayerUpdateItemBuilder = ClanHelpItemPB.newBuilder();
            toHelpPlayerUpdateItemBuilder.setId(successHelpId).setIsCanceled(true);
            toHelpPlayerNtfBuilder.addHelpItems(toHelpPlayerUpdateItemBuilder.build());
        }

        // 单独给触发帮助的玩家发送帮助取消ntf
        getOwner().getMemberComponent().sendCsMsgToMemberClient(playerId, MsgType.PLAYER_CLANHELPINFOUPDATE_NTF, toHelpPlayerNtfBuilder.build());
    }

    /**
     * 某个帮助实际被帮助后同步给军团内除发起者之外的其他玩家
     * <p>
     * 不能同步给发起者，此时发起者身上的帮助项应该已经被删除了
     *
     * @param helpPlayerId   帮助其他人的玩家id
     * @param helpPlayerName 帮助其他人的玩家名字
     * @param prop           帮助项的详细信息，玩家此时已经完成了帮助，会出现在帮助项的
     */
    private void sendUpdateNtfOnClanHelpFinished(Long helpPlayerId, String helpPlayerName, ClanHelpItemProp prop, boolean isCanceled) {
        Player_ClanHelpInfoUpdate_NTF.Builder broadcastNtfBuilder = Player_ClanHelpInfoUpdate_NTF.newBuilder();

        // 单个帮助项发生更改且未被取消
        ClanHelpItemPB.Builder broadcastUpdateItemBuilder = ClanHelpItemPB.newBuilder();
        broadcastUpdateItemBuilder.setId(prop.getId()).setAlreadyHelpTimes(prop.getAlreadyHelpTimes())
                .setTotalHelpTimes(prop.getTotalHelpTimes())
                .setTotalReductionTime(prop.getTotalReductionTime())
                .setHelpPlayerName(helpPlayerName)
                .setAlreadyHelpPlayerIds(prop.getAlreadyHelpPlayerIds().getCopyCsBuilder())
                .setIsCanceled(isCanceled);
        broadcastNtfBuilder.addHelpItems(broadcastUpdateItemBuilder.build());

        if (isCanceled) {
            // 被帮助完成的帮助项不一定完成了，玩家仍然需要看到这个帮助项，所以cancel的ntf不需要发给发起帮助的玩家
            sendHelpUpdateNtfToClanWithoutExcludeMembers(broadcastNtfBuilder, Lists.newArrayList(helpPlayerId, prop.getPlayerId()));
            // 清空ntf中的item信息，重新设置信息的标志位为false，构建ntf并发给发起帮助的玩家
            broadcastNtfBuilder.clearHelpItems();
            broadcastUpdateItemBuilder.setIsCanceled(false);
            broadcastNtfBuilder.addHelpItems(broadcastUpdateItemBuilder.build());
            getOwner().getMemberComponent().sendCsMsgToMemberClient(prop.getPlayerId(),
                    MsgType.PLAYER_CLANHELPINFOUPDATE_NTF, broadcastNtfBuilder.build());
        } else {
            sendHelpUpdateNtfToClanWithoutExcludeMembers(broadcastNtfBuilder, Lists.newArrayList(helpPlayerId));
        }
    }

    /**
     * 发送帮助更新的消息给所有联盟在线玩家
     *
     * @param ntfBuilder 需要发送的消息builder
     */
    private void sendHelpUpdateNtfToClan(Player_ClanHelpInfoUpdate_NTF.Builder ntfBuilder) {
        getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(MsgType.PLAYER_CLANHELPINFOUPDATE_NTF, ntfBuilder.build());
        // 军团帮助消息数量监控
        int size = getOwner().getMemberComponent().getAllClanOnlinePlayerIds().size();
        if (size > 0) {
            LOGGER.info("sendHelpUpdateNtfToClan id:{} size:{}", getOwnerId(), size);
        }
    }

    /**
     * 发送帮助更新的消息给除去特定玩家的联盟在线玩家
     *
     * @param ntfBuilder       需要发送的消息builder
     * @param excludeMemberIds 不需要发送ntf的玩家
     */
    private void sendHelpUpdateNtfToClanWithoutExcludeMembers(Player_ClanHelpInfoUpdate_NTF.Builder ntfBuilder,
                                                              List<Long> excludeMemberIds) {
        getOwner().getMemberComponent().sendMsgToOnlineClanMembersExcludeSpecificMembers(
                MsgType.PLAYER_CLANHELPINFOUPDATE_NTF,
                ntfBuilder.build(),
                excludeMemberIds);
        // 军团帮助消息数量监控
        int size = getOwner().getMemberComponent().getAllClanOnlinePlayerIds().size() - 1;
        if (size > 0) {
            LOGGER.info("sendHelpUpdateNtfToClanWithoutExcludeMembers id:{} size:{}", getOwnerId(), size);
        }
    }
}

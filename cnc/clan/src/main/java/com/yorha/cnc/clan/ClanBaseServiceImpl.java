package com.yorha.cnc.clan;

import com.google.protobuf.ByteString;
import com.yorha.cnc.clan.component.ClanResourcesComponent;
import com.yorha.cnc.clan.gm.ClanGmCommandMgr;
import com.yorha.common.actor.ClanBaseService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsClanBase.*;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMsg.RankInfoDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ClanBaseServiceImpl implements ClanBaseService {
    private static final Logger LOGGER = LogManager.getLogger(ClanBaseServiceImpl.class);

    private final ClanActor clanActor;

    public ClanBaseServiceImpl(ClanActor clan) {
        clanActor = clan;
    }

    @Override
    public void handleCreateClanEntityAsk(CreateClanEntityAsk ask) {
        ClanProp prop = new ClanProp();
        prop.mergeFromSs(ask.getProp());
        prop.unMarkAll();
        clanActor.createClan(prop);
        clanActor.answer(CreateClanEntityAns.getDefaultInstance());
    }

    @Override
    public void handleFetchClanCardAsk(FetchClanCardAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        CommonMsg.ClanCardInfo clanCardInfo = clanEntity.getPropComponent().genCardPb();
        if (clanCardInfo != null) {
            clanActor.answer(FetchClanCardAns.newBuilder().setInfo(clanCardInfo).build());
            return;
        }
        clanActor.answer(FetchClanCardAns.getDefaultInstance());
        LOGGER.error("handleFetchClanCardAsk failed prop is null {}", clanEntity);
    }

    @Override
    public void handleFetchClanRankSimpleInfoAsk(FetchClanRankSimpleInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        FetchClanRankSimpleInfoAns.Builder ans = FetchClanRankSimpleInfoAns.newBuilder();
        if (clanEntity == null) {
            LOGGER.error("fetch clan rank simple info:{} fail", clanActor.getClanId());
            clanActor.answer(ans.build());
            return;
        }
        if (!ask.hasRankId()) {
            LOGGER.error("fetch clan rank simple info without id");
            clanActor.answer(ans.build());
            return;
        }
        RankInfoDTO.Builder infoDTO = RankInfoDTO.newBuilder();
        clanEntity.getRankComponent().getRankSimpleInfo(ask.getRankId(), infoDTO);
        clanActor.answer(ans.setDto(infoDTO).build());
    }

    @Override
    public void handleFetchClanMemberInfoAsk(FetchClanMemberInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        FetchClanMemberInfoAns.Builder ans = FetchClanMemberInfoAns.newBuilder();
        // 解散军团提前拦住
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.warn("clan is null or destroy, clanId:{}", clanActor.getClanId());
            clanActor.answer(ans.build());
            return;
        }
        clanActor.answer(ans.setClanId(clanEntity.getEntityId()).setMsg(clanEntity.getMemberComponent().getAllMembers().toByteString()).build());
    }

    @Override
    public void handleFetchClanApplyMembersAsk(FetchClanApplyMembersAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        FetchClanApplyMembersAns.Builder ans = FetchClanApplyMembersAns.newBuilder();
        if (clanEntity == null) {
            LOGGER.error("search clan:{} fail", clanActor.getClanId());
            clanActor.answer(ans.build());
            return;
        }
        clanActor.answer(ans.setApplyMembers(clanEntity.getMemberComponent().getAllAppliers()).build());
    }

    @Override
    public void handleFetchClanWelcomeLetterAsk(FetchClanWelcomeLetterAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        FetchClanWelcomeLetterAns.Builder ans = FetchClanWelcomeLetterAns.newBuilder();
        if (clanEntity == null) {
            LOGGER.error("search clan:{} fail", clanActor.getClanId());
            clanActor.answer(ans.build());
            return;
        }
        ans.setLetter(clanEntity.getProp().getBase().getWelcomeLetter());
        clanActor.answer(ans.build());
    }

    @Override
    public void handleGetClanBaseInfoAsk(GetClanBaseInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        GetClanBaseInfoAns.Builder ans = GetClanBaseInfoAns.newBuilder();
        if (clanEntity == null) {
            LOGGER.error("getClanBaseInfo cant find clan: {}", clanActor);
            clanActor.answer(ans.build());
            return;
        }
        clanEntity.getProp().getBase().copyToSs(ans.getBaseBuilder());
        clanActor.answer(ans.build());
    }

    @Override
    public void handleSendClanMailAndSaveAsk(SendClanMailAndSaveAsk ask) {
        if (!ask.hasMailSendParams() || !ask.hasMailId()) {
            LOGGER.error("SendClanMailAndSaveAsk has no mailSendParams or mailId, {}", ask);
            return;
        }
        // 目前都是tell过来的，可能会拉起军团导致解散，tell不关心错误码
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.warn("SendClanMailAndSaveAsk clanEntity null clanActor={}, mailId={}", clanActor, ask.getMailId());
            return;
        }
        clanEntity.getMailComponent().sendClanMail(ask.getMailId(), ask.getMailSendParams());
    }

    @Override
    public void handleExecuteClanGmAsk(ExecuteClanGmAsk ask) {
        try {
            ClanGmCommandMgr.getInstance().handle(clanActor, ask.getPlayerId(), ask.getCommand());
        } catch (Exception e) {
            LOGGER.error("clan gm fail. msg:{}", ask, e);
            clanActor.answer(ExecuteClanGmAns.getDefaultInstance());
            return;
        }
        clanActor.answer(ExecuteClanGmAns.getDefaultInstance());
    }

    @Override
    public void handlePlayerDissolveClanAsk(PlayerDissolveClanAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getStageComponent().playerStartDissolveClan(ask.getPlayerId());
        long dissolveTsMs = clanEntity.getStageComponent().getDissolveTsMs();
        clanActor.answer(PlayerDissolveClanAns.newBuilder().setDissolutionTsMs(dissolveTsMs).build());
    }

    @Override
    public void handlePlayerCancelDissolveClanAsk(PlayerCancelDissolveClanAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getStageComponent().playerCancelDissolveClan(ask.getPlayerId());
        clanActor.answer(PlayerCancelDissolveClanAns.newBuilder().build());
    }

    @Override
    public void handleRecordClanSnapshotCmd(RecordClanSnapshotCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            return;
        }
        clanEntity.getClanQlogComponent().qlogSnapShot(1);
    }

    @Override
    public void handleBroadcastClanOnlineMemberCsCmd(BroadcastClanOnlineMemberCsCmd ask) {
        int msgType = ask.getMsgType();
        ByteString msgBytes = ask.getMsgBytes();
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleBroadcastClanOnlineMemberCsCmd clan is null={}", clanActor.getClanId());
            return;
        }
        clanEntity.getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(msgType, msgBytes);
    }

    @Override
    public void handleAddClanResourcesCmd(AddClanResourcesCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleAddClanResourcesCmd clan is null={}", clanActor.getClanId());
            return;
        }
        ClanResourcesComponent resourcesComponent = clanEntity.getResourcesComponent();
        for (Struct.Currency currency : ask.getResourcesList()) {
            CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(currency.getType());
            if (currencyType == null) {
                continue;
            }
            CommonEnum.ClanResourceType clanResourceType = getClanResourceType(currencyType);
            if (clanResourceType == null) {
                continue;
            }
            resourcesComponent.addResources(clanResourceType, currency.getCount());
        }
    }

    @Override
    public void handleInviteOtherToClanAsk(InviteOtherToClanAsk ask) {
        if (!ask.hasInvitedPlayerId() || !ask.hasOpeartorId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getInviteComponent().inviteToClan(ask.getOpeartorId(), ask.getInvitedPlayerId());
        clanActor.answer(InviteOtherToClanAns.newBuilder().build());
    }

    @Override
    public void handleCheckInviteExistAsk(CheckInviteExistAsk ask) {
        if (!ask.hasInvitedPlayerId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getInviteComponent().hasInvitePlayer(ask.getInvitedPlayerId());
        clanActor.answer(CheckInviteExistAns.getDefaultInstance());
    }

    @Override
    public void handleRefuseClanInviteCmd(RefuseClanInviteCmd ask) {
        if (!ask.hasInvitedPlayerId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getInviteComponent().removeClanInvite(ask.getInvitedPlayerId());
    }

    @Override
    public void handleRefreshClanThingsAsk(RefreshClanThingsAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        RefreshClanThingsAns.Builder ans = RefreshClanThingsAns.newBuilder();
        clanEntity.getMemberComponent().fillRefreshAns(ans, ask.getPlayerId());
        clanActor.answer(ans.build());
    }

    @Override
    public void handlePushNotifyCmd(PushNotifyCmd cmd) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            LOGGER.warn("handlePushNotifyCmd null entity, clan={} cmd={}", clanActor.getClanId(), cmd);
            return;
        }
        clanEntity.getNotificationComponent().onPushNotifyCmd(cmd.getNotifyId());
    }

    private CommonEnum.ClanResourceType getClanResourceType(CommonEnum.CurrencyType type) {
        switch (type) {
            case OIL: {
                return CommonEnum.ClanResourceType.CNRT_OIL;
            }
            case STEEL: {
                return CommonEnum.ClanResourceType.CNRT_STEEL;
            }
            case RARE_EARTH: {
                return CommonEnum.ClanResourceType.CNRT_RARE_EARTH;
            }
            case TIBERIUM: {
                return CommonEnum.ClanResourceType.CNRT_TIBERIUM;
            }
            default: {
                return null;
            }
        }
    }
}

package com.yorha.cnc.clan;

import com.yorha.cnc.clan.component.ClanGiftComponent;
import com.yorha.common.actor.ClanGiftService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsClanGift;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 联盟礼物
 */
public class ClanGiftServiceImpl implements ClanGiftService {
    private static final Logger LOGGER = LogManager.getLogger(ClanGiftServiceImpl.class);

    private final ClanActor clanActor;

    public ClanGiftServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }


    @Override
    public void handleAddGiftPointAndKeyAsk(SsClanGift.AddGiftPointAndKeyAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        ClanGiftComponent giftComponent = clanEntity.getGiftComponent();

        giftComponent.addGiftPointAndKey(ask.getAddPoint(), ask.getAddKey());

        SsClanGift.AddGiftPointAndKeyAns.Builder ans = SsClanGift.AddGiftPointAndKeyAns.newBuilder();
        ans.setClanGiftInfo(giftComponent.formClanGiftSharedInfo());
        clanActor.answer(ans.build());
    }

    @Override
    public void handleAddClanGiftCmd(SsClanGift.AddClanGiftCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleAddClanGiftCmd clan is null={}", clanActor.getClanId());
            return;
        }
        ClanGiftComponent giftComponent = clanEntity.getGiftComponent();
        int itemId = ask.getGiftItemId();
        int giftId = giftComponent.getGiftId(itemId);
        giftComponent.addGift(giftId, ask.getGiftRecord());
    }

}

package com.yorha.cnc.clan.rank;

import com.yorha.cnc.clan.ClanActor;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actor.ClanRankMgrService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.rank.AbstractRankEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsClanRank.*;
import com.yorha.proto.StructMsg;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RankTemplate;

import java.util.*;

/**
 * some description
 *
 * <AUTHOR>
 */
public class ClanRankMgrServiceImpl implements ClanRankMgrService {
    private static final Logger LOGGER = LogManager.getLogger(ClanRankMgrServiceImpl.class);
    private final ClanActor clanActor;

    public ClanRankMgrServiceImpl(ClanActor clan) {
        this.clanActor = clan;
    }

    @Override
    public void handleGetClanRankPageInfoAsk(GetClanRankPageInfoAsk ask) {
        final ActorMsgEnvelope context = clanActor.getCurrentEnvelope();
        GetClanRankPageInfoAns.Builder message = GetClanRankPageInfoAns.newBuilder();
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        AbstractRankEntity rankEntity = clanEntity.getRankComponent().getRankEntity(ask.getRankId());
        message.setRankId(ask.getRankId());
        message.setPage(ask.getPage());
        if (rankEntity == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "not exist rank");
        }
        rankEntity.getRankPageInfo(ask.getMemberId(), ask.getPage(),
                (size, page) -> {
                    message.addAllDto(page.getList());
                    if (page.getDto() != null) {
                        message.setMyRankInfoDTO(page.getDto());
                    }
                    message.setTotal(size);
                    this.clanActor.answerWithContext(context, message.build());
                });
    }

    @Override
    public void handleGetTopClanRankInfoAsk(GetTopClanRankInfoAsk ask) {
        final ActorMsgEnvelope context = clanActor.getCurrentEnvelope();
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        Map<Integer, Long> rankToPlayer = new HashMap<>();
        Set<Long> player = new HashSet<>();

        final Collection<ClanRankEntity> array = clanEntity.getRankComponent().getRankEntityList();
        for (ClanRankEntity entity : array) {
            RankTemplate template = ResHolder.findTemplate(RankTemplate.class, entity.getRankId());
            if (template == null) {
                LOGGER.error("rank id {} config is null, pls check the config", entity.getRankId());
                continue;
            }
            if (!template.getIsResident()) {
                LOGGER.debug("rank id {} not need to send to client", entity.getRankId());
                continue;
            }
            Long playerId = entity.getTopMemberId();
            if (playerId != 0) {
                rankToPlayer.put(entity.getRankId(), playerId);
                player.add(playerId);
            }
        }
        CardHelper.batchQueryPlayerHead(clanActor, player,
                (map) -> {
                    GetTopClanRankInfoAns.Builder message = GetTopClanRankInfoAns.newBuilder();
                    for (Map.Entry<Integer, Long> entry : rankToPlayer.entrySet()) {
                        if (!map.containsKey(entry.getValue())) {
                            continue;
                        }
                        message.addDto(buildRankDto(clanEntity, entry.getKey(), entry.getValue(), map.get(entry.getValue())));
                    }
                    message.setRankResetTsMs(clanEntity.getRefreshComponent().getNextWeeklyRefreshTsMs());
                    clanActor.answerWithContext(context, message.build());
                });
    }


    public static StructMsg.RankInfoDTO buildRankDto(ClanEntity clanEntity, int rankId, long memberId, StructPB.PlayerCardHeadPB pb) {
        StructMsg.RankInfoDTO.Builder dto = StructMsg.RankInfoDTO.newBuilder();
        dto.setRankId(rankId)
                .setPlayerId(memberId)
                .setStaffId(clanEntity.getStaffComponent().getPlayerStaffId(memberId))
                .setCardHead(pb);
        return dto.build();
    }


    @Override
    public void handleUpdateClanRankingCmd(UpdateClanRankingCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        AbstractRankEntity rankEntity = clanEntity.getRankComponent().getRankEntity(ask.getRankId());
        if (rankEntity != null) {
            rankEntity.updateRanking(ask.getIncrease(), ask.getMemberId(), ask.getScore(), clanActor.getZoneId());
        } else {
            LOGGER.warn("rankEntity not exist id:{}", ask.getRankId());
        }
    }

}

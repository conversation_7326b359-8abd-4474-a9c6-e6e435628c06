package com.yorha.cnc.clan.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanTechResService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ClanContributionProp;
import com.yorha.game.gen.prop.ClanMemberHistoryModelProp;
import com.yorha.game.gen.prop.ClanMemberHistoryProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import qlog.flow.QlogCncPlayerRankings;
import res.template.ConstClanTechTemplate;

import java.util.Comparator;
import java.util.List;

import static com.yorha.proto.CommonEnum.ClanContributionRankingType;
import static com.yorha.proto.CommonEnum.MailContentType;


/**
 * 联盟里面贡献相关的
 */
public class ClanContributionComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanContributionComponent.class);

    public ClanContributionComponent(ClanEntity owner) {
        super(owner);
    }

    public void addContribution(long playerId, int addScore) {
        ClanMemberHistoryModelProp historyModel = getOwner().getProp().getClanMemberHistoryModel();
        ClanMemberHistoryProp memberHistory = historyModel.getMembersV(playerId);
        if (memberHistory == null) {
            memberHistory = historyModel.addEmptyMembers(playerId);
        }

        ClanContributionProp p = memberHistory.getContribution();
        p.setContribution(p.getContribution() + addScore);
        p.setContributionWeekly(p.getContributionWeekly() + addScore);
        p.setContributionSeason(p.getContributionSeason() + addScore);
        p.setContributionDaily(p.getContributionDaily() + addScore);
        p.setUpdateTsMs(SystemClock.now());
        LOGGER.debug("clanMember addContribution: {} {} {} {} {} {}",
                playerId, addScore, p.getContribution(), p.getContributionWeekly(), p.getContributionSeason(), p.getContributionDaily());
        // 上排行榜
        getOwner().getRankComponent().updateClanRanking(RankConstant.CLAN_PLAYER_TECHNOLOGY_DONATION_RANK, playerId, p.getContributionWeekly());
    }

    public void onNewPlayerJoined(long memberId) {
        ClanMemberHistoryModelProp historyModel = getOwner().getProp().getClanMemberHistoryModel();
        ClanMemberHistoryProp historyProp = historyModel.getMembersV(memberId);
        if (historyProp != null && historyProp.getContribution().getContributionWeekly() > 0) {
            getOwner().getRankComponent().updateClanRanking(RankConstant.CLAN_PLAYER_TECHNOLOGY_DONATION_RANK,
                    memberId, historyProp.getContribution().getContributionWeekly());
        }
    }

    private static class SettleItem {
        public long playerId;
        public String name;
        public int rank;
        public long contribution;
        public long tsMs;

        SettleItem(long playerId, String name, long contribution, long tsMs) {
            this.playerId = playerId;
            this.name = name;
            this.contribution = contribution;
            this.tsMs = tsMs;
        }

        @Override
        public String toString() {
            return StringUtils.reflectionToString(this);
        }
    }

    public void settleDailyRanking() {
        LOGGER.info("{} settleDailyContributionRanking...", getOwner());
        // 收集当日贡献数据
        List<SettleItem> rankList = Lists.newLinkedList();
        ConstClanTechTemplate consts = ResHolder.getConsts(ConstClanTechTemplate.class);
        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        int conLimit = consts.getDailyContributionPointsRankingLimit();
        // 每日贡献清零
        for (ClanMemberHistoryProp historyProp : getOwner().getProp().getClanMemberHistoryModel().getMembers().values()) {
            ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(historyProp.getId());
            ClanContributionProp contributionProp = historyProp.getContribution();
            long contributionDaily = contributionProp.getContributionDaily();
            long contributionTsMs = contributionProp.getUpdateTsMs();
            if (contributionDaily >= conLimit) {
                if (memberProp == null) {
                    LOGGER.info("settleClanDailyContributionRanking member not in clan: {} {} {}", getOwner(), historyProp.getId(), contributionDaily);
                } else {
                    rankList.add(new SettleItem(historyProp.getId(), memberProp.getCardHead().getName(), contributionDaily, contributionTsMs));
                }
            }

            contributionProp.setContributionDaily(0);
        }

        int maxRankNo = resService.maxRankNoOf(ClanContributionRankingType.CTRT_DAILY);
        rankList = processRankList(rankList, maxRankNo);

        if (!rankList.isEmpty()) {
            // 发奖励
            ItemResService itemResService = ResHolder.getResService(ItemResService.class);
            for (SettleItem settleItem : rankList) {
                int rewardId = resService.findRewardIdByRanking(ClanContributionRankingType.CTRT_DAILY, settleItem.rank);
                sendReward(itemResService, settleItem, rewardId);
            }
            // 发邮件
            int mailId = ResHolder.getConsts(ConstClanTechTemplate.class).getDailyContributionRankMailId();
            sendContributionRankingMail(mailId, rankList);

            // 发送qlog
            trySendRankingQlog(rankList, "clan_player_tech_dona_daily");
        }
    }

    private void trySendRankingQlog(List<SettleItem> rankList, String rankingName) {
        // rankingName 不要超过32个字符，qlog上rankingName只定义了size32
        for (SettleItem settleItem : rankList) {
            QlogCncPlayerRankings.init(getOwner().getClanQlogComponent().asServerHead())
                    .setRankingName(rankingName)
                    .setDtEventTime(TimeUtils.now2String())
                    .setRanking(settleItem.rank)
                    .setIGuildId(ownerActor().getClanId())
                    .setVRoleId(String.valueOf(settleItem.playerId))
                    .setRankingScore(settleItem.contribution)
                    .setOptionalReward(0)
                    .sendToQlog();
        }
    }

    public void settleWeeklyRanking() {
        LOGGER.info("{} settleWeeklyContributionRanking...", getOwner());
        // 收集当周贡献数据
        List<SettleItem> rankList = Lists.newLinkedList();
        ConstClanTechTemplate consts = ResHolder.getConsts(ConstClanTechTemplate.class);
        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        int conLimit = consts.getWeeklyContributionPointsRankingLimit();
        // 每周贡献清零
        for (ClanMemberHistoryProp historyProp : getOwner().getProp().getClanMemberHistoryModel().getMembers().values()) {
            ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(historyProp.getId());
            ClanContributionProp contributionProp = historyProp.getContribution();
            long contributionWeekly = contributionProp.getContributionWeekly();
            long contributionTsMs = contributionProp.getUpdateTsMs();
            if (contributionWeekly >= conLimit) {
                if (memberProp == null) {
                    LOGGER.info("settleClanWeeklyContributionRanking member not in clan: {} {} {}", getOwner(), historyProp.getId(), contributionWeekly);
                } else {
                    rankList.add(new SettleItem(historyProp.getId(), memberProp.getCardHead().getName(), contributionWeekly, contributionTsMs));
                }
            }

            contributionProp.setContributionWeekly(0);
        }

        int maxRankNo = resService.maxRankNoOf(ClanContributionRankingType.CTRT_WEEKLY);
        rankList = processRankList(rankList, maxRankNo);

        if (!rankList.isEmpty()) {
            // 发奖励
            ItemResService itemResService = ResHolder.getResService(ItemResService.class);
            for (SettleItem settleItem : rankList) {
                int rewardId = resService.findRewardIdByRanking(ClanContributionRankingType.CTRT_WEEKLY, settleItem.rank);
                sendReward(itemResService, settleItem, rewardId);
            }
            // 发邮件
            int mailId = ResHolder.getConsts(ConstClanTechTemplate.class).getWeeklyContributionRankMailId();
            sendContributionRankingMail(mailId, rankList);

            // qlog
            trySendRankingQlog(rankList, "clan_player_tech_dona");
        }
    }

    @NotNull
    private static List<SettleItem> processRankList(List<SettleItem> rankList, int maxRankNo) {
        // 按照分数倒序，分数相同，按照时间顺序
        rankList.sort(
                Comparator.<SettleItem>comparingLong(i -> i.contribution)
                        .reversed()
                        .thenComparingLong(i -> i.tsMs)
        );
        if (rankList.size() > maxRankNo) {
            rankList = Lists.newLinkedList(rankList.subList(0, maxRankNo));
        }
        for (int i = 0; i < rankList.size(); i++) {
            rankList.get(i).rank = i + 1;
        }
        return rankList;
    }

    private void sendReward(ItemResService itemResService, SettleItem settleItem, int rewardId) {
        if (rewardId > 0) {
            List<ItemReward> rewards = itemResService.randomReward(rewardId);
            SsPlayerMisc.AddRewardCmd.Builder sendRewardCmd = SsPlayerMisc.AddRewardCmd.newBuilder();
            for (ItemReward itemReward : rewards) {
                sendRewardCmd.getItemRewardsBuilder()
                        .addDatas(Struct.ItemReward.newBuilder()
                                .setItemTemplateId(itemReward.getItemTemplateId())
                                .setCount(itemReward.getCount()).build()
                        );
            }
            ownerActor().tellPlayer(settleItem.playerId, sendRewardCmd.build());
        } else {
            LOGGER.error("{} send contribution reward, non reward {}", getOwner(), settleItem);
        }
    }

    private void sendContributionRankingMail(int mailId, List<SettleItem> rankList) {
        try {
            StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
            params.setMailTemplateId(mailId);
            params.getSenderBuilder().setSenderId(0);
            params.setContent(buildMailContent(rankList));
            MailUtil.sendClanMail(this.getOwner().getZoneId(), getEntityId(), params.build());
        } catch (Exception e) {
            LOGGER.error("{} sendContributionRankingMail failed:{} {}", getOwner(), mailId, rankList);
        }
    }

    private StructMail.MailContent buildMailContent(List<SettleItem> rankList) {
        StructMail.MailClanContributionRankingItemList.Builder rankingItemList = StructMail.MailClanContributionRankingItemList.newBuilder();
        for (SettleItem settleItem : rankList) {
            rankingItemList.addDatas(StructMail.MailClanContributionRankingItem.newBuilder()
                    .setRank(settleItem.rank)
                    .setPlayerId(settleItem.playerId)
                    .setPlayerName(settleItem.name)
                    .setContribution(settleItem.contribution)
                    .build());
        }
        return StructMail.MailContent.newBuilder()
                .setContentType(MailContentType.MAIL_CONTENT_TYPE_CLAN_CONTRIBUTION_RANKING)
                .setClanContributionRankingData(StructMail.MailClanContributionRankingData.newBuilder()
                        .setItems(rankingItemList.build())
                        .build())
                .build();
    }


}

package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanActor;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.resource.resservice.clan.StaffHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.game.gen.prop.ClanStaffInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanOfficeTemplate;
import res.template.ConstClanTemplate;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 官员职位
 *
 * <AUTHOR>
 */
public class ClanStaffComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanStaffComponent.class);
    /**
     * 官职剩余人数
     */
    private final Map<Integer, Integer> notOfferedStaffCnt = new HashMap<>();
    private final Map<Integer, Set<Long>> offeredStaffPlayerIds = new HashMap<>();

    // 盟主不在线且正在间隔期内才会赋值
    private ActorTimer autoTransferOwnerTimer = null;

    public ClanStaffComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        resetClanStaffCnt();
        resetClanStaffPlayIds();
    }

    @Override
    public void onCreate() {
        // 授予联盟所有者
        grantClanOwnerImpl(getOwnerId(), 0, false);
    }

    /**
     * @param memberIds 角色id
     * @return 返回对应权限角色在线的玩家数目
     */
    public int getOnlineStaffCnt(Set<Long> memberIds) {
        Set<Long> allClanOnlinePlayerIds = this.getOwner().getMemberComponent().getAllClanOnlinePlayerIds();
        int staffCnt = 0;
        for (long memberId : memberIds) {
            if (allClanOnlinePlayerIds.contains(memberId)) {
                ++staffCnt;
            }
        }
        return staffCnt;
    }

    /**
     * 转让军团长
     *
     * @param operatorId 操作玩家id
     * @param targetId   目标玩家id
     */
    public long transferClanOwner(long operatorId, long targetId) throws GeminiException {
        return transferClanOwner(operatorId, targetId, false);
    }

    /**
     * 转移军团长
     *
     * @param operatorId 原军团长
     * @param targetId   新军团长
     * @return 返回原军团长下次可以成为军团长的时间
     */
    public long transferClanOwner(long operatorId, long targetId, boolean ignoreOwnerCd) throws GeminiException {
        ClanProp prop = this.getOwner().getProp();
        if (prop.getOwnerId() != operatorId) {
            LOGGER.error("transferClanOwner operator not owner! clan={}, operatorId={}, targetId={}", getOwner(), operatorId, targetId);
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        if (operatorId == targetId) {
            LOGGER.error("transferClanOwner operator, target same! clan={}, operatorId={}", getOwner(), operatorId);
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        ClanMemberProp operatorMemberProp = getOwner().getMemberComponent().getMemberNoThrow(operatorId);
        ClanMemberProp targetMemberProp = getOwner().getMemberComponent().getMemberNoThrow(targetId);
        if (null == operatorMemberProp) {
            LOGGER.error("transferClanOwner operator not in! clan={}, operatorId={}", getOwner(), operatorId);
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (null == targetMemberProp) {
            LOGGER.error("transferClanOwner target not in! clan={}, targetId={}", getOwner(), targetId);
            throw new GeminiException(ErrorCode.CLAN_TARGET_PLAYER_NOT_IN_CLAN);
        }
        // NOTE(furson): 康乐说这是保底逻辑，低频操作可以不删除
        if (operatorMemberProp.getStaffId() != this.getOwner().getDataTemplateService().getOwnerStaffId()) {
            LOGGER.error("transferClanOwner caller not the clan owner! need check ownerId! clan={}, callerId={}, clan ownerId={}",
                    getOwner(), operatorId, prop.getOwnerId());
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        if (!ignoreOwnerCd && targetMemberProp.getNextCanBeOwnerTsMs() > SystemClock.now()) {
            LOGGER.info("target {} cannot be owner until {}", targetId, targetMemberProp.getNextCanBeOwnerTsMs());
            throw new GeminiException(ErrorCode.CLAN_IN_BE_OWNER_CD);
        }
        // 先移除目标的职位，再移除自己的职位
        this.removePlayerHighStaff(targetId, false, operatorId);
        Integer staffId = this.removePlayerHighStaff(operatorId, false, operatorId);
        if (staffId == null) {
            LOGGER.error("transferClanOwner operator has no staff! clan={}, operatorId={}, targetId={}", getOwner(), operatorId, targetId);
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        // 目标变为军团长
        grantClanOwnerImpl(targetId, operatorId, true);
        // 设置原盟主可以下次可以成为军团长的时间
        long nextCanBeOwnerTsMs = 0L;
        if (isInEarlyKingdom()) {
            nextCanBeOwnerTsMs = getOwner().getStaffComponent().getNextCanBeOwnerTsMs();
            operatorMemberProp.setNextCanBeOwnerTsMs(nextCanBeOwnerTsMs);
        }
        LOGGER.info("transferClanOwner Successful! clan={}, operatorId={}, targetId={}", getOwner(), operatorId, targetId);
        // 记录转让军团长日志
        getOwner().getLogComponent().logTransferOwner(operatorId, targetId);
        // 同步给场景
        getOwner().getMemberComponent().syncClanMemberToScene(true, false);

        // 军团长变更需要广播所有在线玩家
        PlayerClan.ClanOwnerChangedNtf ownerChangedNtf = PlayerClan.ClanOwnerChangedNtf.newBuilder()
                .setClanId(this.getEntityId())
                .setOwnerId(targetId).build();
        getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(MsgType.PLAYER_CLANOWNERCHANGED_NTF, ownerChangedNtf);
        getOwner().getStaffComponent().onOwnerChange();
        getOwner().getStageComponent().cancelApplyOwnerWhenDissolving(targetId);

        return nextCanBeOwnerTsMs;
    }

    /**
     * 获取下次可以授予职位的毫秒
     *
     * @param staffId 职位id
     * @return 返回下次可以授予职位的毫秒数，0代表未设置
     */
    public long getNextCanGrantStaffTsMs(int staffId) {
        ClanProp prop = this.getOwner().getProp();
        if (prop.getStaffModel().getStaffInfoMapV(staffId) == null) {
            return 0L;
        }
        ClanStaffInfoProp info = prop.getStaffModel().getStaffInfoMapV(staffId);
        long specialStaffGrantedCd = ResHolder.getResService(ConstClanKVResService.class).getTemplate().getSpecialStaffGrantedCD();
        return info.getLastGrantTsMs() + TimeUnit.MINUTES.toMillis(specialStaffGrantedCd);
    }

    /**
     * 获取下次可以成为军团长的时间戳(毫秒)
     */
    private long getNextCanBeOwnerTsMs() {
        int canBeLeaderDays = ResHolder.getResService(ConstClanKVResService.class).getTemplate().getBeClanLeaderCD();
        return SystemClock.now() + TimeUnit.DAYS.toMillis(canBeLeaderDays);
    }

    /**
     * 是否是王国建立初期
     *
     * @return 是王国建立初期返回true
     */
    private boolean isInEarlyKingdom() {
        int earlyKingdomDays = ResHolder.getResService(ConstClanKVResService.class).getTemplate().getEarlyKingdomTime();
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("isInEarlyKingdom end server is not open={}", ZoneContext.getServerOpenTsMs());
            return true;
        }
        return SystemClock.now() <= ZoneContext.getServerOpenTsMs() + TimeUnit.DAYS.toMillis(earlyKingdomDays);
    }

    /**
     * 授予官职
     *
     * @param operatorId 授予人
     * @param targetId   被授予人
     * @param staffId    官职id
     */
    public void grantStaff(long operatorId, long targetId, int staffId) {
        ClanMemberProp operatorMemberProp = getOwner().getMemberComponent().getMemberNoThrow(operatorId);
        ClanMemberProp targetMemberProp = getOwner().getMemberComponent().getMemberNoThrow(targetId);
        if (operatorMemberProp == null) {
            LOGGER.info("grantStaff operator or target not in! clan={}, operator={}, target={}, staffId={}", getOwner(), operatorId, targetId, staffId);
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (targetMemberProp == null) {
            LOGGER.info("grantStaff operator or target not in! clan={}, operator={}, target={}, staffId={}", getOwner(), operatorId, targetId, staffId);
            throw new GeminiException(ErrorCode.CLAN_TARGET_PLAYER_NOT_IN_CLAN);
        }
        ClanDataTemplateService clanDataTemplateService = this.getOwner().getDataTemplateService();
        if (!clanDataTemplateService.canStaffAdjustOtherStaff(operatorMemberProp.getStaffId(), targetMemberProp.getStaffId(), staffId)) {
            LOGGER.info("grantStaff operator can't adjust target! clan={}, operator={}, target={}, staffId={}", getOwner(), operatorId, targetId, staffId);
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        // 不是特殊职位可以使用小于1进行判断
        if (this.getNotOfferedStaffCnt(staffId) < 1 && !isHasBuffStaff(staffId)) {
            LOGGER.info("grantStaff staff not enough! clan={}, operator={}, target={}, staffId={}", getOwner(), operatorId, targetId, staffId);
            throw new GeminiException(ErrorCode.CLAN_R4_FULL);
        }
        // 有特殊buff的staff有替换逻辑
        checkSpecialStaffOrRemove(staffId, false, operatorId);
        if (clanDataTemplateService.isHighStaff(targetMemberProp.getStaffId())) {
            this.removePlayerHighStaff(targetId, false, operatorId);
        }
        this.doGrantStaff(targetMemberProp, staffId, true, false, operatorId);
    }

    /**
     * 回收玩家的特殊职位
     *
     * @param playerId    玩家id
     * @param needClanLog 是否需要军团日志
     * @return 玩家老的职位id；如不存在特殊职位，返回null；否则返回职位id
     */
    public Integer removePlayerHighStaff(long playerId, boolean needClanLog, long operationId) {
        final ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(playerId);
        if (memberProp == null) {
            return null;
        }
        final ClanDataTemplateService clanDataTemplateService = this.getOwner().getDataTemplateService();
        final int staffIdAfterRemovingStaff = clanDataTemplateService.getStaffIdAfterRemovingStaff();
        if (this.getNotOfferedStaffCnt(staffIdAfterRemovingStaff) < 1) {
            return null;
        }
        final Integer oldStaffId = memberProp.getStaffId();
        if (clanDataTemplateService.isHighStaff(oldStaffId)) {
            this.doGrantStaff(memberProp, staffIdAfterRemovingStaff, true, needClanLog, operationId);
            return oldStaffId;
        }
        return null;
    }


    /**
     * 根据联盟成员重置各个职位的剩余数量
     */
    private void resetClanStaffCnt() {
        this.getOwner().getDataTemplateService().getStaffIds().forEach(staffId -> {
            StaffHolder staffHolder = this.getOwner().getDataTemplateService().getStaffHolder(staffId);
            this.notOfferedStaffCnt.put(staffId, staffHolder.getMaxCnt());
        });
        this.getOwner().getProp().getMember().forEach((key, value) -> this.decStaffCnt(value.getStaffId()));
    }

    /**
     * 根据联盟成员重置各个职位对应的玩家id
     */
    private void resetClanStaffPlayIds() {
        this.getOwner().getProp().getMember().forEach((key, value) -> this.recordOfferedStaffPlayerId(value.getStaffId(), value.getId()));
    }

    /**
     * 授予军团长
     *
     * @param targetId   目标玩家id
     * @param operatorId 操作玩家id, 可能为0, 代表系统处理
     * @param needQLog   是否需要打qlog
     * @throws GeminiException 可能会抛出业务异常，需要上层处理或抛给客户端
     */
    private void grantClanOwnerImpl(long targetId, long operatorId, boolean needQLog) throws GeminiException {
        // memberProp必须存在，前序逻辑需要保证，否则抛异常
        ClanMemberProp targetMemberProp = getOwner().getMemberComponent().getMember(targetId);
        // 先设置军团长id，NOTE(furson):OwnerId的设置在外面一定是二段的，如果流程中断，可能出现部分数据有误
        ClanProp prop = getOwner().getProp();
        prop.setOwnerId(targetId);
        // 将目标玩家设置为军团长
        doGrantStaff(targetMemberProp, getOwner().getDataTemplateService().getOwnerStaffId(), needQLog, false, operatorId);
        // 军团转让相关，军团长成功设置后再设置时间
        prop.getMiscModel().setOwnerBecomeTsMs(SystemClock.now());
        LOGGER.info("grantClanOwnerImpl clan={}, target={}, operator={}", getOwner(), targetId, operatorId);
    }


    /**
     * 获取职位数量
     *
     * @param staffId 职位id
     * @return 职位数量
     */
    private int getNotOfferedStaffCnt(int staffId) {
        final ClanDataTemplateService clanDataTemplateService = this.getOwner().getDataTemplateService();
        // 没有buff的高官的计数需要加上有buff的高官
        if (staffId == clanDataTemplateService.getClanWithoutBuffHighStaff()) {
            int staffCnt = 0;
            for (int highStaffId : clanDataTemplateService.getMiddleStaffIdSet()) {
                if (offeredStaffPlayerIds.get(highStaffId) != null) {
                    staffCnt += offeredStaffPlayerIds.get(highStaffId).size();
                }
            }
            int staffMaxNum = clanDataTemplateService.getClanWithoutBuffHighStaffMaxNum();
            if (staffCnt > staffMaxNum) {
                LOGGER.error("staffCnt {} bigger than staffMaxNum {}, check your logic", staffCnt, staffMaxNum);
            }
            return staffCnt >= staffMaxNum ? 0 : staffMaxNum - staffCnt;
        }
        return notOfferedStaffCnt.getOrDefault(staffId, 0);
    }

    /**
     * 直接赋予成员职位
     * 注意：不要直接使用此接口（存在未检查直接赋予的情况），使用grantStaff替代
     *
     * @param playerMemberProp 成员属性
     * @param staffId          职位id
     * @param needQLog         是否需要打qlog
     * @param needClanLog      是否需要打军团日志
     * @param operationId      操作id
     */
    public void doGrantStaff(ClanMemberProp playerMemberProp, int staffId, boolean needQLog, boolean needClanLog, long operationId) {
        if (playerMemberProp == null) {
            LOGGER.error("doGrantStaff {} playerMemberProp is null, staffId {}, operationId {}", getOwner(), staffId, operationId);
            return;
        }
        int oldStaffId = playerMemberProp.getStaffId();
        this.incStaffCnt(oldStaffId);
        this.deleteOfferedStaffPlayerId(oldStaffId, playerMemberProp.getId());

        playerMemberProp.setStaffId(staffId);
        this.decStaffCnt(playerMemberProp.getStaffId());
        this.recordOfferedStaffPlayerId(staffId, playerMemberProp.getId());

        // 设置授予职位的时间
        if (isHasBuffStaff(staffId)) {
            getOwner().getProp().getStaffModel().addEmptyStaffInfoMap(staffId).setLastGrantTsMs(SystemClock.now());
        }

        // 设置授予职位相关的军团日志
        if (needClanLog) {
            getOwner().getLogComponent().logStaffChange(oldStaffId, staffId, operationId, playerMemberProp.getCardHead().getName());
        }

        if (needQLog) {
            getOwner().getMemberComponent().sendGuildStaffManageQLog(playerMemberProp.getId(), "grade_change", operationId, 0, 0);
        }

        getOwner().getMemberComponent().syncClanInfoToPlayer(playerMemberProp.getId(), oldStaffId);
    }


    /**
     * 获取成员职位id
     */
    public int getPlayerStaffId(long playerId) {
        ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(playerId);
        if (memberProp == null) {
            return 0;
        }
        return memberProp.getStaffId();
    }

    /**
     * 授予前检查之前是否有玩家在该staff上，若有将该玩家职位剥夺
     *
     * @param staffId          准备授予的staffId
     * @param needClanLog      是否需要打军团日志
     * @param operatorPlayerId 操作的玩家id
     */
    public void checkSpecialStaffOrRemove(int staffId, boolean needClanLog, long operatorPlayerId) {
        if (!isHasBuffStaff(staffId)) {
            LOGGER.debug("{} not special buff staff", staffId);
            return;
        }
        List<Long> playerIds = getPlayerIdsByStaffId(staffId);
        if (playerIds.isEmpty()) {
            LOGGER.debug("{} haven't grant to other people before", staffId);
            return;
        }
        if (playerIds.size() != 1) {
            LOGGER.error("{}'s number is not 1", staffId);
            return;
        }
        // 当且仅当该职位有一个玩家时，将该玩家撤职，并授予R4
        Integer oldStaffId = this.removePlayerHighStaff(playerIds.getFirst(), needClanLog, operatorPlayerId);
        if (oldStaffId == null) {
            LOGGER.error("checkSpecialStaffOrRemove remove old staff failed, clan={}, operatorId={}, targetId={}",
                    getOwner(), operatorPlayerId, playerIds.getFirst());
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        final ClanDataTemplateService clanDataTemplateService = this.getOwner().getDataTemplateService();
        final int clanWithoutBuffHighStaff = clanDataTemplateService.getClanWithoutBuffHighStaff();
        this.doGrantStaff(getOwner().getMemberComponent().getMemberNoThrow(
                playerIds.getFirst()), clanWithoutBuffHighStaff, true, needClanLog, operatorPlayerId);
        LOGGER.debug("finish remove special staff {}, grant {}", staffId, clanWithoutBuffHighStaff);
    }


    /**
     * 获取在某个职位上的所有玩家
     *
     * @param staffId 职位id
     * @return 返回玩家id list，若不存在则返回空list，不返回null
     */
    private List<Long> getPlayerIdsByStaffId(int staffId) {
        List<Long> playerIds = new ArrayList<>();
        if (this.offeredStaffPlayerIds.get(staffId) != null) {
            playerIds.addAll(this.offeredStaffPlayerIds.get(staffId));
        }
        return playerIds;
    }

    /**
     * 新增职位数量
     *
     * @param staffId 职位id
     */
    private void incStaffCnt(int staffId) {
        if (!this.notOfferedStaffCnt.containsKey(staffId)) {
            LOGGER.debug("inc staff={} cnt", staffId);
            return;
        }
        this.notOfferedStaffCnt.put(staffId, this.notOfferedStaffCnt.get(staffId) + 1);
    }

    /**
     * 减少职位数量
     *
     * @param staffId 职位id
     */
    private void decStaffCnt(int staffId) {
        if (!this.notOfferedStaffCnt.containsKey(staffId)) {
            LOGGER.debug("dec staff={} cnt", staffId);
            return;
        }
        this.notOfferedStaffCnt.put(staffId, this.notOfferedStaffCnt.get(staffId) - 1);
    }

    /**
     * 记录被授予职位的玩家的playerId
     *
     * @param staffId 职位id playerId 玩家id
     */
    private void recordOfferedStaffPlayerId(int staffId, long playerId) {
        if (!this.notOfferedStaffCnt.containsKey(staffId)) {
            LOGGER.debug("dec staff={} cnt", staffId);
            return;
        }

        if (!this.offeredStaffPlayerIds.containsKey(staffId)) {
            this.offeredStaffPlayerIds.put(staffId, new HashSet<>());
        }
        this.offeredStaffPlayerIds.get(staffId).add(playerId);
    }

    /**
     * 删除被解除职位的玩家的playerId
     *
     * @param staffId 职位id playerId 玩家id
     */
    private void deleteOfferedStaffPlayerId(int staffId, long playerId) {
        if (!this.notOfferedStaffCnt.containsKey(staffId)) {
            LOGGER.debug("dec staff={} cnt", staffId);
            return;
        }
        if (!this.offeredStaffPlayerIds.containsKey(staffId)) {
            LOGGER.debug("no such staffId {} have been record with playerId {}", staffId, playerId);
            return;
        }
        if (this.offeredStaffPlayerIds.get(staffId).contains(playerId)) {
            this.offeredStaffPlayerIds.get(staffId).remove(playerId);
        } else {
            LOGGER.debug("no such playerId {} and staffId {}", playerId, staffId);
        }
    }

    public boolean isHasBuffStaff(int staffId) {
        return ResHolder.getResService(ConstClanKVResService.class).getTemplate().getSpecialStaffIDs().contains(staffId);
    }

    /**
     * 返回军团内拥有特定权限的玩家id
     *
     * @param operationType 权限类型
     * @return 返回拥有权限的玩家id，若没有这类玩家返回一个空的List，不返回null
     */
    public Set<Long> getHasSpecificPrivilegePlayerIds(CommonEnum.ClanOperationType operationType) {
        Set<Long> playerIds = new HashSet<>();
        for (long memberId : getOwner().getMemberComponent().getAllMemberIds()) {
            ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(memberId);
            if (memberProp == null) {
                continue;
            }
            final int staffId = memberProp.getStaffId();
            if (ClanPermissionUtils.hasPermissionNoThrow(operationType, staffId)) {
                playerIds.add(memberId);
            }
        }
        return playerIds;
    }

    public void onOwnerChange() {
        cancelAutoTransferOwnerTimer();
        if (!getOwner().getMemberComponent().isOnline(getOwnerId())) {
            tryAddAutoTransferOwnerTimer();
        }
    }

    public void tryAddAutoTransferOwnerTimer() {
        cancelAutoTransferOwnerTimer();
        ConstClanTemplate consts = ResHolder.getConsts(ConstClanTemplate.class);
        int intervalHours = consts.getClanOwnerTransferTimeOld();
        Instant divideTime = Instant.ofEpochMilli(ZoneContext.getServerOpenTsMs())
                .plus(consts.getClanOwnerTransferDivideOpenTime(), ChronoUnit.DAYS);
        if (SystemClock.nowInstant().isBefore(divideTime)) {
            intervalHours = consts.getClanOwnerTransferTimeNew();
        }
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("tryAddAutoTransferOwnerTimer server is not open={}", ZoneContext.getServerOpenTsMs());
            intervalHours = consts.getClanOwnerTransferTimeNew();
        }
        autoTransferOwnerTimer = ownerActor().addTimer(getEntityId(), TimerReasonType.CLAN_STAFF_AUTO_TRANSFER_OWNER,
                this::tryAutoTransferOwner, intervalHours, TimeUnit.HOURS);
    }

    public ClanMemberProp getMember(long playerId) {
        return getOwner().getMemberComponent().getMember(playerId);
    }

    private void tryAutoTransferOwner() {
        cancelAutoTransferOwnerTimer();
        // 这里是try，所以得先确认一下owner真的离线太久了
        final long curOwnerId = getOwnerId();
        if (getOwner().getMemberComponent().isOnline(curOwnerId)) {
            LOGGER.info("tryAutoTransferOwner owner online");
            return;
        }
        long curOwnerLastOnlineMs = getMember(curOwnerId).getLastOnlineTsMs();
        long ownerBecomeTsMs = getOwner().getProp().getMiscModel().getOwnerBecomeTsMs();
        Instant countingDownStart = Instant.ofEpochMilli(Math.max(curOwnerLastOnlineMs, ownerBecomeTsMs));

        ConstClanTemplate consts = ResHolder.getConsts(ConstClanTemplate.class);
        int intervalHours = consts.getClanOwnerTransferTimeOld();
        Instant divideTime = Instant.ofEpochMilli(ZoneContext.getServerOpenTsMs())
                .plus(consts.getClanOwnerTransferDivideOpenTime(), ChronoUnit.DAYS);
        if (countingDownStart.isBefore(divideTime)) {
            intervalHours = consts.getClanOwnerTransferTimeNew();
        }
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("tryAutoTransferOwner server is not open={}", ZoneContext.getServerOpenTsMs());
            intervalHours = consts.getClanOwnerTransferTimeNew();
        }
        if (SystemClock.nowInstant().isBefore(countingDownStart.plus(intervalHours, ChronoUnit.HOURS))) {
            LOGGER.info("tryAutoTransferOwner curOwner lastOnlineTs too close");
            return;
        }

        // 找一下最适合的下一任
        final long nowMs = SystemClock.now();
        List<ClanMemberProp> onlineMembers = getOwner().getMemberComponent().onlinePlayerIds()
                .stream()
                .map(this::getMember)
                .toList();
        Optional<ClanMemberProp> foundNextOwner = onlineMembers.stream()
                .max(Comparator.<ClanMemberProp>comparingInt(member -> member.getNextCanBeOwnerTsMs() < nowMs ? 2 : 1)
                        .thenComparingInt(member -> {
                            int staffId = member.getStaffId();
                            return ResHolder.getTemplate(ClanOfficeTemplate.class, staffId).getStaffLevel();
                        })
                        .thenComparingLong(ClanMemberProp::getComboat)
                );
        if (foundNextOwner.isEmpty()) {
            LOGGER.info("tryAutoTransferOwner nextOwner not found!");
            return;
        }
        ClanMemberProp nextOwnerProp = foundNextOwner.get();
        final long newOwnerId = nextOwnerProp.getId();
        LOGGER.info("tryAutoTransferOwner trigger! transfer owner={} {}->{}", getOwner(), curOwnerId, newOwnerId);
        for (ClanMemberProp memberProp : onlineMembers) {
            LOGGER.info("tryAutoTransferOwner detail={} {} {} {}",
                    memberProp.getId(), memberProp.getNextCanBeOwnerTsMs(), memberProp.getStaffId(), memberProp.getComboat());
        }

        transferClanOwner(curOwnerId, newOwnerId, true);

        // 转让成功，发个邮件
        getOwner().getMsgComponent().sendAutoTransferOwnerMail(curOwnerId, newOwnerId);

    }

    private void cancelAutoTransferOwnerTimer() {
        if (this.autoTransferOwnerTimer != null) {
            this.autoTransferOwnerTimer.cancel();
            this.autoTransferOwnerTimer = null;
        }
    }

    public void onMemberCheckIn(long playerId) {
        if (getOwner().getMemberComponent().isClanOwner(playerId)) {
            cancelAutoTransferOwnerTimer();
        } else if (this.autoTransferOwnerTimer == null && !getOwner().getMemberComponent().isOnline(getOwnerId())) {
            // 不影响玩家checkIn，异步处理
            ActorSendMsgUtils.send(ownerActor().self(), new ActorRunnable<ClanActor>("tryAutoTransferOwner",
                    clanActor -> clanActor.getClanEntity().getStaffComponent().tryAutoTransferOwner()));
        }
    }
}

package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.freqLimitCaller.FreqLimitCaller;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanBaseInfoProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * <AUTHOR>
 */

public class ClanPropComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanPropComponent.class);

    /**
     * 延时同步至PlayerCardCache 和 db
     */
    private FreqLimitCaller updateClanCardSchedule;

    public ClanPropComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onLoad() {
        ClanProp prop = getOwner().getProp();
        prop.setNum(prop.getMember().size());
        int addition = (int) getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_ADD_CLAN_MEMBER_NUM);
        prop.setNumMax(ResHolder.getResService(ConstClanKVResService.class).getTemplate().getClanMaxNum() + addition);
        refreshClanPower();
        refreshClanKillScore();
    }


    @Override
    public void init() {
        updateClanCardSchedule = new FreqLimitCaller(TimerReasonType.UPDATE_CLAN_CARD_SCHEDULE,
                300000,
                1,
                this::updateClanCard,
                this.ownerActor(),
                String.valueOf(getEntityId()));
        getOwner().getProp().setListener(new CanStopPropertyChangeListener(() -> {
            if (!getOwner().isInitOk()) {
                // 未初始化完全，actor还未注册，此时投递消息会出错
                return;
            }
            if (!getOwner().getProp().hasAnyMark()) {
                return;
            }
            final ClanProp clanProp = this.getOwner().getProp();
            clanProp.setVersion(clanProp.getVersion() + 1L);
            getOwner().getDbComponent().saveChangeToDb();
            clanProp.unMarkAll();
        }, getOwner().ownerActor().self()));

        this.getOwner().getProp().setVersion(GameLogicConstants.CLAN_VERSION_INIT);
    }

    /**
     * 填充玩家登录军团后获取的基本信息
     *
     * @param basicInfoBuilder 基础信息的构造器
     */
    public void fillCheckInBasicInfo(long memberId, CommonMsg.CheckInBasicInfo.Builder basicInfoBuilder) {
        // 一定非空
        basicInfoBuilder.setStaffId(getOwner().getMemberComponent().getMemberNoThrow(memberId).getStaffId());
    }

    // ---------------------------------------------------- 战力 ----------------------------------------------------

    /**
     * 增加联盟战力
     */
    public void addClanPower(long combat) {
        ClanProp prop = getOwner().getProp();
        prop.setCombat(prop.getCombat() + combat);
    }

    /**
     * 更新联盟战力
     */
    public void refreshClanPower() {
        ClanProp prop = getOwner().getProp();
        long combat = 0L;

        for (ClanMemberProp member : prop.getMember().values()) {
            combat += member.getComboat();
        }
        prop.setCombat(combat);
        // 军团card更新：军团战力变化，延迟同步
        updateClanCardCache(false);
    }

    public void decClanPower(long combat) {
        ClanProp prop = getOwner().getProp();
        prop.setCombat(prop.getCombat() - combat);
    }

    // ---------------------------------------------------- 击杀积分 ----------------------------------------------------

    public void addClanKillScore(long killScore) {
        ClanProp prop = getOwner().getProp();
        prop.getMiscModel().setKillScore(prop.getMiscModel().getKillScore() + killScore);
    }

    /**
     * 更新联盟击杀积分
     */
    public void refreshClanKillScore() {
        ClanProp prop = getOwner().getProp();
        long killScore = 0L;
        for (ClanMemberProp member : prop.getMember().values()) {
            killScore += member.getKillScore();
        }
        prop.getMiscModel().setKillScore(killScore);
    }

    public void decClanKillScore(long killScore) {
        ClanProp prop = getOwner().getProp();
        prop.getMiscModel().setKillScore(prop.getMiscModel().getKillScore() - killScore);
    }

    /**
     * 获取军团的击杀积分
     */
    public long getClanKillScore() {
        long killScore = 0L;
        for (long memberId : getOwner().getMemberComponent().getAllMemberIds()) {
            killScore += getOwner().getMemberComponent().getMemberNoThrow(memberId).getKillScore();
        }
        return killScore;
    }


    // ---------------------------------------------- 进出对prop的操作 ----------------------------------------------------
    public void onPlayerJoin(ClanMemberProp memberProp) {
        ClanProp prop = getOwner().getProp();
        memberProp.setResLastCalTsMs(memberProp.getEnterTime());
        prop.putMemberV(memberProp);
        prop.setNum(prop.getMember().size());
        addClanPower(memberProp.getComboat());
        addClanKillScore(memberProp.getKillScore());
        // 军团card更新：有人加入时实时同步
        updateClanCardCache(true);
    }

    public void onPlayerLeave(long playerId) {
        ClanProp prop = getOwner().getProp();
        // 未领取资源同步给退盟玩家
        ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(playerId);
        getOwner().getMemberComponent().addResourcesToPlayer(memberProp);
        // 处理clan prop
        prop.removeMemberV(playerId);
        prop.setNum(prop.getMember().size());
        decClanPower(memberProp.getComboat());
        decClanKillScore(memberProp.getKillScore());
        // 同步到scene上，NEED_MORE_THINK(furson): 军团剩余人数为0时，会执行解散逻辑，此时同步无意义，对端也不做任何处理
        if (prop.getNum() > 0) {
            getOwner().getMemberComponent().syncClanMemberToScene(false, true);
            // 军团card更新：军团还有人时退出实时同步
            updateClanCardCache(true);
        }
    }

    /**
     * 同步给场景相关属性变更
     */
    public void syncToSceneClan(SsSceneClan.SyncSceneClanCmd.Builder cmd) {
        cmd.setClanId(getEntityId());
        ownerActor().tellBigScene(cmd.build());
    }

    /**
     * 获取军团的简单信息
     */
    public CommonMsg.ClanCardInfo genCardPb() {
        ClanProp prop = getOwner().getProp();
        CommonMsg.ClanCardInfo.Builder info = CommonMsg.ClanCardInfo.newBuilder();
        // 按需手动设置，不使用copyTo，避免不需要的信息发送
        info.setZoneId(ownerActor().getZoneId())
                .setId(getEntityId())
                .setNum(prop.getNum())
                .setNumMax(prop.getNumMax())
                .setCombat(prop.getCombat())
                .setTerritoryPower(getOwner().getTerritoryComponent().getTerritoryPower())
                .setGiftLevel(getOwner().getGiftComponent().getGiftLevel());
        // 军团基本信息设置
        ClanBaseInfoProp baseInfoProp = prop.getBase();
        info.getBaseBuilder().setName(baseInfoProp.getName())
                .setSname(baseInfoProp.getSname())
                .setTerritoryColor(baseInfoProp.getTerritoryColor())
                .setFlagColor(baseInfoProp.getFlagColor())
                .setFlagShading(baseInfoProp.getFlagShading())
                .setFlagSign(baseInfoProp.getFlagSign())
                .setNationFlagId(baseInfoProp.getNationFlagId())
                .setDescribe(baseInfoProp.getDescribe())
                .setRequire(baseInfoProp.getRequire())
                .setLanguage(baseInfoProp.getLanguage());
        // 盟主信息设置
        if (prop.getOwnerId() != 0) {
            ClanMemberProp ownerProp = getOwner().getMemberComponent().getClanOwner();
            if (ownerProp == null) {
                return null;
            }
            info.getOwnerBuilder().setId(ownerProp.getId()).setComboat(ownerProp.getComboat());
            info.getOwnerBuilder().getCardHeadBuilder().mergeFrom(ownerProp.getCardHead().getCopyCsBuilder().build());
        }
        info.getStageInfoBuilder().setStage(prop.getStageModel().getStage())
                .setDissolutionTsMs(prop.getStageModel().getDissolutionTsMs());
        return info.build();
    }

    /**
     * 将数据更新到cache
     */
    private void updateClanCard() {
        if (getOwner().getStageComponent().isStartDissolve()) {
            // 之前的任务解散时才被启动，不再需要更新了
            LOGGER.info("updateClanCard startDissolve {}, no need to update", getOwner());
            return;
        }
        LOGGER.info("updateClanCard start {}", getOwner());
        CommonMsg.ClanCardInfo info = genCardPb();
        if (info == null) {
            LOGGER.error("updateClanCard failed prop is null {}", getOwner());
            return;
        }
        // 向db
        TcaplusDb.ClanCardTable.Builder req = TcaplusDb.ClanCardTable.newBuilder();
        req.setClanId(getEntityId()).setInfo(info);
        ownerActor().tellGameDb(new UpsertAsk<>(req, UpsertOption.getDefaultInstance()));
        // 向cache
        // 如果在停服中 拦截
        if (ServerContext.getServerStopStep() > 1) {
            LOGGER.info("want updateClanCard but server is stopping");
            return;
        }
        SsClanCard.UpdateClanCardCmd msg = SsClanCard.UpdateClanCardCmd.newBuilder().setCardInfo(info).build();
        ownerActor().tell(CardHelper.genClanCardRef(getEntityId()), msg);
    }

    /**
     * 删除clan card的数据，仅在解散时调用
     */
    private void deleteClanCard() {
        if (!getOwner().getStageComponent().isAlreadyDisband()) {
            // 没解散就调用，居心叵测23333333
            LOGGER.error("deleteClanCard failed not disband {}", getOwner());
            return;
        }
        LOGGER.info("deleteClanCard start {}", getOwner());

        // 删除db里的数据
        try {
            TcaplusDb.ClanCardTable.Builder req = TcaplusDb.ClanCardTable.newBuilder();
            req.setClanId(getEntityId());
            ownerActor().callGameDb(new DeleteAsk<>(req));
        } catch (Exception e) {
            WechatLog.error("deleteClanCard failed {}, ", getOwner(), e);
        }
        // 注意：cache里面的依然存在
    }

    public void updateClanCardCache(boolean isImmediately) {
        if (updateClanCardSchedule == null) {
            return;
        }
        if (getOwner().getStageComponent().isStartDissolve()) {
            // 解散时调入外层接口，不再需要更新了
            LOGGER.warn("updateClanCardCache startDissolve {}, no need to update", getOwner());
            return;
        }
        // 需要马上执行的
        if (isImmediately) {
            if (updateClanCardSchedule.isRunFuture()) {
                updateClanCardSchedule.stopTimer();
            }
            updateClanCard();
            return;
        }
        updateClanCardSchedule.run();
    }

    @Override
    public void onDestroy() {
        if (updateClanCardSchedule == null) {
            return;
        }
        if (getOwner().getStageComponent().isAlreadyDisband()) {
            // 已经解散了，主动destroy，直接删除clan card
            if (updateClanCardSchedule.isRunFuture()) {
                updateClanCardSchedule.stopTimer();
            }
            deleteClanCard();
            return;
        }
        if (updateClanCardSchedule.isRunFuture()) {
            // 销毁entity时，仍有还未执行的更新任务
            updateClanCard();
            updateClanCardSchedule.stopTimer();
        }
    }
}

package com.yorha.cnc.clan;

import com.yorha.cnc.clan.component.*;
import com.yorha.cnc.clan.component.territory.ClanTerritoryComponent;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.proto.Clan;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import res.template.ConstClanTemplate;

import java.util.Collection;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class ClanEntity extends AbstractEntity {
    private final ClanActor actor;
    private final ClanProp prop;
    private final boolean isCreate;

    protected ClanEntity(ClanActor clanActor, ClanProp prop, Clan.ClanEntity changedProp, boolean isCreate) {
        super(prop.getId());
        this.prop = prop;
        this.actor = clanActor;
        this.isCreate = isCreate;
        this.dbComponent = new ClanDbComponent(this, changedProp);
        initAllComponents();
    }

    public ClanProp getProp() {
        return prop;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Clan;
    }

    @Override
    public ClanActor ownerActor() {
        return actor;
    }

    @Override
    protected void onPostInitFailed() {

    }

    public int getZoneId() {
        return this.actor.getZoneId();
    }

    public boolean isCreate() {
        return this.isCreate;
    }

    /**
     * 调用所有组件的方法
     */
    private void callAllComponents(Consumer<? super ClanComponent> action) {
        Collection<ClanComponent> components = getAllComponents();
        for (ClanComponent component : components) {
            // 加stopwatch统计
            action.accept(component);
        }
    }

    /**
     * 安全的调用所有组件的方法
     */
    public void callAllComponentsSafe(Consumer<? super ClanComponent> action) {
        callAllComponents((clanComponent -> {
            try {
                // 加stopwatch统计
                action.accept(clanComponent);
            } catch (Exception e) {
                WechatLog.error("clan id={} callAllComponent error", getEntityId(), e);
            }
        }));
    }
    // -------------------------------- component --------------------------------

    private final ClanPropComponent propComponent = new ClanPropComponent(this);
    private final ClanDbComponent dbComponent;
    private final ClanMemberComponent memberComponent = new ClanMemberComponent(this);
    private final ClanMsgComponent msgComponent = new ClanMsgComponent(this);
    private final ClanSettingComponent settingComponent = new ClanSettingComponent(this);
    private final ClanStageComponent stageComponent = new ClanStageComponent(this);
    private final ClanQlogComponent clanQlogComponent = new ClanQlogComponent(this);
    private final ClanStaffComponent staffComponent = new ClanStaffComponent(this);
    private final ClanDevBuffComponent devBuffComponent = new ClanDevBuffComponent(this);
    private final ClanAdditionComponent additionComponent = new ClanAdditionComponent(this);
    private final ClanResourcesComponent resourcesComponent = new ClanResourcesComponent(this);
    private final ClanRankComponent rankComponent = new ClanRankComponent(this);
    private final ClanHelpComponent helpComponent = new ClanHelpComponent(this);
    private final ClanGiftComponent giftComponent = new ClanGiftComponent(this);
    private final ClanPositionMarkComponent positionMarkComponent = new ClanPositionMarkComponent(this);
    private final ClanChatComponent chatComponent = new ClanChatComponent(this);
    private final ClanWareHouseComponent wareHouseComponent = new ClanWareHouseComponent(this);
    private final ClanLogComponent logComponent = new ClanLogComponent(this);
    private final ClanRedDotComponent redDotComponent = new ClanRedDotComponent(this);
    private final ClanTechComponent techComponent = new ClanTechComponent(this);
    private final ClanContributionComponent contributionComponent = new ClanContributionComponent(this);
    private final ClanRefreshComponent refreshComponent = new ClanRefreshComponent(this);
    private final ClanStoreComponent storeComponent = new ClanStoreComponent(this);
    private final ClanInviteComponent inviteComponent = new ClanInviteComponent(this);
    private final ClanTerritoryComponent territoryComponent = new ClanTerritoryComponent(this);
    private final ClanMailComponent mailComponent = new ClanMailComponent(this);
    private final ClanPushNotificationComponent notificationComponent = new ClanPushNotificationComponent(this);
    private final ClanActivityComponent activityComponent = new ClanActivityComponent(this);
    private final ClanMarqueeComponent marqueeComponent = new ClanMarqueeComponent(this);

    public ClanPositionMarkComponent getPositionMarkComponent() {
        return positionMarkComponent;
    }

    public ClanPropComponent getPropComponent() {
        return propComponent;
    }

    public ClanDbComponent getDbComponent() {
        return dbComponent;
    }

    public ClanMemberComponent getMemberComponent() {
        return memberComponent;
    }

    public ClanMsgComponent getMsgComponent() {
        return msgComponent;
    }

    public ClanSettingComponent getSettingComponent() {
        return this.settingComponent;
    }

    public ClanStageComponent getStageComponent() {
        return this.stageComponent;
    }

    public ClanQlogComponent getClanQlogComponent() {
        return this.clanQlogComponent;
    }

    public ClanStaffComponent getStaffComponent() {
        return staffComponent;
    }

    public ClanDevBuffComponent getDevBuffComponent() {
        return devBuffComponent;
    }

    public ClanAdditionComponent getAddComponent() {
        return additionComponent;
    }

    public ClanResourcesComponent getResourcesComponent() {
        return resourcesComponent;
    }

    public ClanRankComponent getRankComponent() {
        return rankComponent;
    }

    public ClanHelpComponent getHelpComponent() {
        return helpComponent;
    }

    public ClanGiftComponent getGiftComponent() {
        return giftComponent;
    }

    public ClanChatComponent getChatComponent() {
        return chatComponent;
    }

    public ClanWareHouseComponent getWareHouseComponent() {
        return wareHouseComponent;
    }

    public ClanLogComponent getLogComponent() {
        return logComponent;
    }

    public ClanRedDotComponent getRedDotComponent() {
        return redDotComponent;
    }

    public ClanTechComponent getTechComponent() {
        return techComponent;
    }

    public ClanContributionComponent getContributionComponent() {
        return contributionComponent;
    }

    public ClanRefreshComponent getRefreshComponent() {
        return refreshComponent;
    }

    public ClanStoreComponent getStoreComponent() {
        return storeComponent;
    }

    public ClanInviteComponent getInviteComponent() {
        return inviteComponent;
    }

    public ClanTerritoryComponent getTerritoryComponent() {
        return territoryComponent;
    }

    public ClanMailComponent getMailComponent() {
        return mailComponent;
    }

    public ClanPushNotificationComponent getNotificationComponent() {
        return notificationComponent;
    }

    public ClanMarqueeComponent getMarqueeComponent() {
        return marqueeComponent;
    }

    public ClanActivityComponent getActivityComponent() {
        return activityComponent;
    }

    public ClanDataTemplateService getDataTemplateService() {
        return ResHolder.getResService(ClanDataTemplateService.class);
    }

    public ConstClanTemplate getConstTemplate() {
        return ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        getDbComponent().saveOnDestroy();
        getProp().unMarkAll();
    }
}

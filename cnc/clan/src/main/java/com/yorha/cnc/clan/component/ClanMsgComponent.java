package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.game.gen.prop.ClanBuildCostResourceProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.ClanKickOffReason;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.CommonEnum.MailContentType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ClanMsgComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanMsgComponent.class);

    public ClanMsgComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 发送高级职称变更邮件
     */
    public void sendClanHighStaffAlterMail(long operatorId, long targetId) {
        final int mailId = this.getOwner().getConstTemplate().getClanOfficialArrangemetMail();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        final ClanMemberProp operatorMember = getOwner().getMemberComponent().getMemberNoThrow(operatorId);
        final ClanMemberProp targetMember = getOwner().getMemberComponent().getMemberNoThrow(targetId);
        if (operatorMember == null) {
            LOGGER.error("sendClanHighStaffAlterMail operator prop is null, clan={}, operatorId={}, targetId={}", getOwner(), operatorId, targetId);
            return;
        }
        if (targetMember == null) {
            LOGGER.error("sendClanHighStaffAlterMail target prop is null, clan={}, operatorId={}, targetId={}", getOwner(), operatorId, targetId);
            return;
        }
        Struct.DisplayData.Builder titleData = mailSendParams.getTitleBuilder().getSubTitleDataBuilder();
        titleData.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(targetMember.getCardHead().getName()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, targetMember.getStaffId()));
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);

        mailSendParams.getContentBuilder().getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(targetMember.getCardHead().getName()))
                .addDatas(MsgHelper.buildDisPlayText(operatorMember.getCardHead().getName()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, targetMember.getStaffId()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, targetMember.getStaffId()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_DESCRIBE, targetMember.getStaffId()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_BUFF_DESCRIBE, targetMember.getStaffId()));
        sendClanMail(mailSendParams.build());
    }

    /**
     * 发送低级职称变更邮件
     *
     * @param playerId  职位变更人
     * @param isLevelUp 是否升值
     */
    public void sendClanLowStaffAlterMail(long playerId, boolean isLevelUp) {
        if (!getOwner().getMemberComponent().isClanMember(playerId)) {
            LOGGER.error("sendClanLowStaffAlterMail failed! clan={}, playerId={}, isLevelUp={}", getOwner(), playerId, isLevelUp);
            return;
        }
        final int mailId;
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        if (isLevelUp) {
            mailId = this.getOwner().getConstTemplate().getClanMemberLevelUpMail();
        } else {
            mailId = this.getOwner().getConstTemplate().getClanMemberLevelDownMail();
        }
        final int staffId = getOwner().getMemberComponent().getMemberNoThrow(playerId).getStaffId();
        // 副标题参数设置
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, staffId));

        // 正文参数设置
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, staffId));

        mailSendParams.setMailTemplateId(mailId);
        sendPersonalMail(playerId, mailSendParams.build());
    }

    /**
     * 发送军团长申请邮件
     *
     * @param playerId 职位变更人
     */
    public void sendClanOwnerApplyMail(long playerId) {
        final int mailId = this.getOwner().getConstTemplate().getClanOwnerApplyMail();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        String memberNameById = getOwner().getMemberComponent().getMemberNameById(playerId);
        // 副标题参数设置
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(memberNameById));

        // 正文参数设置
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(memberNameById));

        mailSendParams.setMailTemplateId(mailId);
        sendClanMail(mailSendParams.build());
    }

    /**
     * 发送盟主变更邮件
     *
     * @param callerPlayerId 主动方
     * @param calleePlayerId 被动方
     */
    public void sendClanOwnerTransferMail(long callerPlayerId, long calleePlayerId) {
        final int mailId = this.getOwner().getConstTemplate().getClanLeaderChangeMail();
        final StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        final ClanMemberProp oldOwner = getOwner().getMemberComponent().getMemberNoThrow(callerPlayerId);
        final ClanMemberProp newOwner = getOwner().getMemberComponent().getMemberNoThrow(calleePlayerId);
        if (oldOwner == null || newOwner == null) {
            LOGGER.warn("skip sendClanOwnerTransferMail! clan={}, oldOwnerId={}, newOwnerId={}",
                    this.getOwner().getProp().getBase(), callerPlayerId, calleePlayerId);
            return;
        }
        String newOwnerName = newOwner.getCardHead().getName();
        // 1. 设置内容
        StructMail.MailContent.Builder mailContent = mailSendParams.getContentBuilder();
        mailContent.setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        mailContent.getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(oldOwner.getCardHead().getName()))
                .addDatas(MsgHelper.buildDisPlayText(newOwnerName))
                .addDatas(MsgHelper.buildDisPlayText(newOwnerName));
        // 2. 设置sub title
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(newOwnerName));
        // 3. 发送邮件
        sendClanMail(mailSendParams.build());
    }

    public void sendAutoTransferOwnerMail(long oldOwnerId, long newOwnerId) {
        // 虽然这个接口和上面这个 sendClanOwnerTransferMail 几乎一毛一样，但是其实是不同的功能，谨慎做合并

        final int mailId = this.getOwner().getConstTemplate().getClanOwnerTransferMail();
        final StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        final ClanMemberProp oldOwner = getOwner().getMemberComponent().getMemberNoThrow(oldOwnerId);
        final ClanMemberProp newOwner = getOwner().getMemberComponent().getMemberNoThrow(newOwnerId);
        if (oldOwner == null || newOwner == null) {
            LOGGER.warn("skip sendAutoTransferOwnerMail! clan={}, oldOwnerId={}, newOwnerId={}", this.getOwner().getProp().getBase(), oldOwnerId, newOwnerId);
            return;
        }
        String newOwnerName = newOwner.getCardHead().getName();
        // 1. 设置内容
        StructMail.MailContent.Builder mailContent = mailSendParams.getContentBuilder();
        mailContent.setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        mailContent.getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(oldOwner.getCardHead().getName()))
                .addDatas(MsgHelper.buildDisPlayText(newOwnerName))
                .addDatas(MsgHelper.buildDisPlayText(newOwnerName));
        // 2. 设置sub title
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(newOwnerName));
        // 3. 发送邮件
        sendClanMail(mailSendParams.build());
    }

    /**
     * 发送审核邮件
     *
     * @param memberProp 申请的玩家信息
     */
    public void sendReviewApplyMail(ClanMemberProp memberProp) {
        final int mailId = this.getOwner().getConstTemplate().getClanReviewApplyMail();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        // 正文参数设置
        // 使用折叠邮件
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_CLAN_APPLY);
        mailSendParams.getContentBuilder().setClanApplyData(
                StructClan.ClanApplyData.newBuilder()
                        .setPlayerId(memberProp.getId())
                        .setComboat(memberProp.getComboat())
                        .setCardHead(memberProp.getCardHead().getCopySsBuilder())
                        .setClanSimpleName(getOwner().getProp().getBase().getSname()));


        // 获取所有有审核权限的军团成员id
        Set<Long> playerIds = getOwner().getStaffComponent().getHasSpecificPrivilegePlayerIds(CommonEnum.ClanOperationType.COT_AUDIT);
        mailSendParams.setMailTemplateId(mailId);
        sendBatchPersonalMail(playerIds, mailSendParams.build());
    }

    /**
     * 发送踢人邮件
     *
     * @param callerPlayerId 踢人者
     * @param calleePlayerId 被踢者
     * @param reason         被踢的原因
     */
    public void sendKickOffMemberMsg(long callerPlayerId, long calleePlayerId, ClanKickOffReason reason) {
        final int mailId = this.getOwner().getConstTemplate().getClanMemberRemoveMail();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        // 副标题参数设置
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getBase().getName()));

        // 正文参数设置
        mailSendParams.getContentBuilder()
                .setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_KICK_OFF_CLAN_REASON, reason.getNumber()))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(callerPlayerId)));

        mailSendParams.setMailTemplateId(mailId);
        sendPersonalMail(calleePlayerId, mailSendParams.build());
    }

    /**
     * 发送取消预解散的邮件
     *
     * @param playerId 执行取消预解散的玩家id，该玩家一定在军团内
     */
    public void sendCancelDissolveMail(long playerId) {
        final int mailId = this.getOwner().getConstTemplate().getClanCancelDissolveMail();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(playerId)));
        mailSendParams.setMailTemplateId(mailId);
        sendClanMail(mailSendParams.build());
    }

    /**
     * 发送欢迎邮件
     *
     * @param playerId 玩家id
     */
    public void sendNewbieMail(long playerId) {
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(ResHolder.getResService(ConstClanKVResService.class).getTemplate().getClanJoinMailId());

        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getBase().getSname()))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getBase().getName()));

        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA)
                .getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getSettingComponent().getWelcomeLetter()));

        sendPersonalMail(playerId, mailSendParams.build());
    }

    /**
     * 发送加入联盟被拒绝的邮件
     *
     * @param operatorId 操作的玩家id
     * @param targetId   目标玩家id
     */
    public void sendClanUnionApplicationDeniedMail(long operatorId, long targetId) {
        final int mailId = this.getOwner().getConstTemplate().getClanUnionApplicationDeniedMailId();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        // 副标题参数设置
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorId)));


        // 正文参数设置
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        mailSendParams.getContentBuilder().getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorId)))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getBase().getSname()));

        mailSendParams.setMailTemplateId(mailId);
        sendPersonalMail(targetId, mailSendParams.build());
    }

    /**
     * 发送取消申请成为军团长的邮件
     */
    public void sendCancelApplyOwnerMail(long playerId) {
        final int mailId = getOwner().getConstTemplate().getClanMemberCancelApplyOwner();
        final StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        // 正文参数设置
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        mailSendParams.getContentBuilder().getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(playerId)));

        mailSendParams.setMailTemplateId(mailId);
        getOwner().getMsgComponent().sendClanMail(mailSendParams.build());
    }

    /**
     * 发送邀请邮件
     *
     * @param operatorId     操作者id
     * @param targetPlayerId 目标玩家id
     */
    public void sendInviteMail(long operatorId, long targetPlayerId) {
        final int mailId = getOwner().getConstTemplate().getInviteJoinClanMail();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();

        // 副标题参数设置：操作者玩家名字，军团简称，军团名字
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorId)))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getBase().getSname()))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getBase().getName()));

        // 正文参数设置
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_CLAN_INVITE);
        mailSendParams.getContentBuilder().getClanInviteDataBuilder()
                .setInviteClanId(getOwner().getEntityId())
                .setInvitePlayerName(getOwner().getMemberComponent().getMemberNameById(operatorId))
                .setInviteClanSimpleName(getOwner().getProp().getBase().getSname())
                .setInviteClanName(getOwner().getProp().getBase().getName())
                .setInviteFlagInfo(getClanFlagInfoBuilder());

        // 设置为有邀请状态
        mailSendParams.setHasInvitedStatus(true);
        mailSendParams.setMailTemplateId(mailId);
        sendPersonalMail(targetPlayerId, mailSendParams.build());
    }

    /**
     * 发送建筑建设完成邮件
     *
     * @param mailId           邮件id
     * @param templateId       建筑模板id
     * @param point            建筑坐标
     * @param costResourceProp 资源消耗prop
     */
    public void sendBuildFinishMail(int mailId, int templateId, Struct.Point point, ClanBuildCostResourceProp costResourceProp) {
        if (mailId <= 0) {
            LOGGER.error("sendBuildFinishMail, {} mailId {} wrong", getOwner(), mailId);
            return;
        }
        StructMail.MailSendParams.Builder mailBuilder = StructMail.MailSendParams.newBuilder();
        mailBuilder.setMailTemplateId(mailId);
        mailBuilder.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_CLAN_BUILDING);
        // 正文内容；设置建筑模板id和坐标
        mailBuilder.getContentBuilder().getClanBuildingDataBuilder().setTemplateId(templateId)
                .setP(point);
        // 设置玩家id
        mailBuilder.getContentBuilder().getClanBuildingDataBuilder().setPlayerId(costResourceProp.getPlayerId());
        // 设置玩家头像
        mailBuilder.getContentBuilder().getClanBuildingDataBuilder().setCardHead(costResourceProp.getCardHead().getCopySsBuilder().build());
        // 设置消耗资源
        mailBuilder.getContentBuilder().getClanBuildingDataBuilder().setCostResource(costResourceProp.getCostResource().getCopySsBuilder());
        // 发送邮件
        MailUtil.sendClanMail(this.getOwner().getZoneId(), getEntityId(), mailBuilder.build());
    }

    public void sendPersonalMail(long targetId, StructMail.MailSendParams mailSendParams) {
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(targetId)
                        .setZoneId(this.getOwner().getZoneId())
                        .build(),
                mailSendParams);
    }

    public void sendBatchPersonalMail(Set<Long> targetIdList, StructMail.MailSendParams mailSendParamsPb) {
        List<CommonMsg.MailReceiver> receivers = targetIdList.stream()
                .map(playerId -> CommonMsg.MailReceiver.newBuilder().setPlayerId(playerId).setZoneId(this.getOwner().getZoneId()).build())
                .collect(Collectors.toList());
        MailUtil.sendMailToPlayers(receivers, mailSendParamsPb);
    }

    public void sendClanMail(StructMail.MailSendParams mailSendParams) {
        MailUtil.sendClanMail(this.getOwner().getZoneId(), getEntityId(), mailSendParams);
    }

    public void sendNewbieMarquee(String name) {
        final ConstClanTemplate constClanTemplate = this.getOwner().getConstTemplate();
        final int marqueeId = constClanTemplate.getJoinClanMessage();
        StructPB.DisplayDataPB.Builder builder = StructPB.DisplayDataPB.newBuilder();
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(name));
        getOwner().getMarqueeComponent().sendClanMarquee(marqueeId, builder.build());
    }

    public void sendKickOffMarquee(String name) {
        final ConstClanTemplate constClanTemplate = this.getOwner().getConstTemplate();
        final int marqueeId = constClanTemplate.getLeaveClanMessage();
        StructPB.DisplayDataPB.Builder builder = StructPB.DisplayDataPB.newBuilder();
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(name));
        getOwner().getMarqueeComponent().sendClanMarquee(marqueeId, builder.build());
    }

    public void sendPositionMarkMarquee(final int marqueeId, final int staffId, final String name, final int markPicId) {
        StructPB.DisplayDataPB.Builder builder = StructPB.DisplayDataPB.newBuilder();
        builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_STAFF_ID_FOR_NAME, staffId))
                .addDatas(MsgHelper.buildDisPlayTextPb(name))
                .addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_MARK_PIC_ID, markPicId));
        getOwner().getMarqueeComponent().sendClanMarquee(marqueeId, builder.build(), getClanFlagInfoPbBuilder().build());
    }

    public StructClan.ClanFlagInfo.Builder getClanFlagInfoBuilder() {
        StructClan.ClanFlagInfo.Builder builder = StructClan.ClanFlagInfo.newBuilder();
        builder.setFlagColor(getOwner().getProp().getBase().getFlagColor())
                .setFlagShading(getOwner().getProp().getBase().getFlagShading())
                .setFlagSign(getOwner().getProp().getBase().getFlagSign())
                .setNationFlagId(getOwner().getProp().getBase().getNationFlagId())
                .setTerritoryColor(getOwner().getProp().getBase().getTerritoryColor());
        return builder;
    }

    private StructClanPB.ClanFlagInfoPB.Builder getClanFlagInfoPbBuilder() {
        StructClanPB.ClanFlagInfoPB.Builder builder = StructClanPB.ClanFlagInfoPB.newBuilder();
        builder.setFlagColor(getOwner().getProp().getBase().getFlagColor())
                .setFlagShading(getOwner().getProp().getBase().getFlagShading())
                .setFlagSign(getOwner().getProp().getBase().getFlagSign())
                .setNationFlagId(getOwner().getProp().getBase().getNationFlagId())
                .setTerritoryColor(getOwner().getProp().getBase().getTerritoryColor());
        return builder;
    }
}


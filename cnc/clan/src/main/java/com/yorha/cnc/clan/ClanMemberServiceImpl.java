package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanMemberMgrService;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.SimpleHeroProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.Core;
import com.yorha.proto.SsClanMember.*;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.Struct;
import com.yorha.proto.StructClan;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ClanMemberServiceImpl implements ClanMemberMgrService {
    private static final Logger LOGGER = LogManager.getLogger(ClanMemberServiceImpl.class);
    private final ClanActor clanActor;

    public ClanMemberServiceImpl(ClanActor clan) {
        this.clanActor = clan;
    }

    @Override
    public void handlePlayerApplyClanAsk(PlayerApplyClanAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        PlayerApplyClanAns.Builder ans = PlayerApplyClanAns.newBuilder();
        if (clanEntity == null || clanEntity.isDestroy()) {
            clanActor.answer(ans.setCode(ErrorCode.CLAN_NOT_EXIST.getCodeId()).build());
            return;
        }
        ClanMemberProp clanMemberProp = new ClanMemberProp();
        clanMemberProp.mergeFromSs(ask.getMember());
        Core.Code code = clanEntity.getMemberComponent().applyJoin(clanMemberProp, ask.getInvitePlayerName());
        clanActor.answer(ans.setCode(code.getId()).build());
    }

    @Override
    public void handleProcessClanApplyAsk(ProcessClanApplyAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            clanActor.answer(ProcessClanApplyAns.newBuilder().setCode(ErrorCode.CLAN_NOT_EXIST.getCodeId()).build());
            return;
        }
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        clanEntity.getMemberComponent().handleProcessClanApply(clanActor.getCurrentEnvelope(), ask.getOperatorId(), ask.getTargetId(), ask.getIsAllow());
    }

    @Override
    public void handlePlayerQuitClanAsk(PlayerQuitClanAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.info("handlePlayerQuitClanAsk clanEntity is null or destroy, ask={}", ask);
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        clanEntity.getMemberComponent().playerTryQuit(ask.getPlayerId(), ask.getPlayerId());
        clanActor.answer(PlayerQuitClanAns.getDefaultInstance());
    }

    @Override
    public void handleKickOutClanMemberAsk(KickOutClanMemberAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.info("handleKickOutClanMemberAsk clanEntity is null or destroy, ask={}", ask);
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        long operatorId = ask.getOperatorId();
        long targetId = ask.getTargetId();
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        clanEntity.getMemberComponent().kickOffClanMember(operatorId, targetId);
        clanEntity.ownerActor().tellPlayer(targetId, SsPlayerClan.OnNtfClanKickOffResultCmd.newBuilder()
                .setClanId(clanActor.getClanId())
                .setCallerId(operatorId)
                .setKickOffTsMs(SystemClock.now()).build());
        clanEntity.getMsgComponent().sendKickOffMemberMsg(operatorId, targetId, ask.getReason());
        clanActor.answer(KickOutClanMemberAns.getDefaultInstance());
    }

    @Override
    public void handleCheckInClanAsk(CheckInClanAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            clanActor.answer(CheckInClanAns.newBuilder().setCode(ErrorCode.CLAN_NOT_EXIST.getCodeId()).build());
            return;
        }
        if (!StringUtils.equals(ask.getSessionRefData().getActorRole(), ActorRole.GateSession.name())) {
            throw new GeminiException(ErrorCode.CHECK_NOT_SESSION_REF);
        }
        final IActorRef sessionRef = RefFactory.fromPb(ask.getSessionRefData());
        if (!clanEntity.getMemberComponent().checkInMember(ask.getPlayerId(), sessionRef)) {
            WechatLog.error("handleCheckInClanAsk not in clan {} {}", clanEntity, ask.getPlayerId());
            clanActor.answer(CheckInClanAns.newBuilder().setCode(ErrorCode.CLAN_NOT_IN.getCodeId()).build());
            return;
        }
        // 构建checkIn返回
        CheckInClanAns.Builder ansBuilder = CheckInClanAns.newBuilder();
        clanEntity.getMemberComponent().fillCheckInAns(ask, ansBuilder);
        // 填充需要返回的信息
        ansBuilder.setCode(ErrorCode.OK.getCodeId());
        clanActor.answer(ansBuilder.build());
    }

    @Override
    public void handleCheckOutClanAsk(CheckOutClanAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            clanActor.answer(CheckOutClanAns.newBuilder().setCode(ErrorCode.CLAN_NOT_EXIST.getCodeId()).build());
            return;
        }
        if (!clanEntity.getMemberComponent().checkOutMember(ask.getPlayerId())) {
            clanActor.answer(CheckOutClanAns.newBuilder().setCode(ErrorCode.CLAN_NOT_IN.getCodeId()).build());
            return;
        }
        clanActor.answer(CheckOutClanAns.newBuilder().setCode(ErrorCode.OK.getCodeId()).build());
    }

    @Override
    public void handleGrantClanStaffAsk(GrantClanStaffAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.info("handleGrantClanStaffAsk clanEntity is null or destroy, ask={}", ask);
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        int staffId = ask.getStaffId();
        long operatorId = ask.getOperatorId();
        long targetId = ask.getTargetId();
        if (clanEntity.getDataTemplateService().getOwnerStaffId() == staffId) {
            // 官员为盟主
            long nextCanBeOwnerTsMs = clanEntity.getStaffComponent().transferClanOwner(operatorId, targetId);
            clanEntity.getMsgComponent().sendClanOwnerTransferMail(operatorId, targetId);
            LOGGER.info("grantClanStaff successful[OWNER]! clan={}, operatorId={}, targetId={}, staffId={}", clanEntity, operatorId, targetId, staffId);
            GrantClanStaffAns.Builder builder = GrantClanStaffAns.newBuilder();
            if (nextCanBeOwnerTsMs != 0L) {
                builder.setNextCanBeOwnerTsMs(nextCanBeOwnerTsMs);
            }
            clanActor.answer(builder.build());
            return;
        }
        final int calleeOldStaffId = clanEntity.getStaffComponent().getPlayerStaffId(targetId);
        final int cmpResult = clanEntity.getDataTemplateService().compareStaff(staffId, calleeOldStaffId);
        if (staffId == calleeOldStaffId) {
            LOGGER.warn("grantClanStaff same staff privilege! clan={}, operatorId={}, targetId={}, staffId={}", clanEntity, operatorId, targetId, staffId);
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        long nextCanGrantStaffTs = clanEntity.getStaffComponent().getNextCanGrantStaffTsMs(staffId);
        // 若职位授予仍处在冷却时间内
        if (nextCanGrantStaffTs != 0 && nextCanGrantStaffTs > SystemClock.now()) {
            clanActor.answer(GrantClanStaffAns.newBuilder().setNextCanGrantTsMs(nextCanGrantStaffTs).build());
            return;
        }
        clanEntity.getStaffComponent().grantStaff(operatorId, targetId, staffId);
        clanEntity.getLogComponent().logStaffChange(calleeOldStaffId, staffId, operatorId, clanEntity.getMemberComponent().getMemberNameById(targetId));
        if (clanEntity.getStaffComponent().isHasBuffStaff(staffId)) {
            clanEntity.getMsgComponent().sendClanHighStaffAlterMail(operatorId, targetId);
        } else {
            clanEntity.getMsgComponent().sendClanLowStaffAlterMail(targetId, cmpResult >= 0);
        }
        LOGGER.info("grantClanStaff successful[STAFF]! clan={}, operatorId={}, targetId={}, staffId={}", clanEntity, operatorId, targetId, staffId);
        clanActor.answer(GrantClanStaffAns.getDefaultInstance());
    }

    @Override
    public void handleFetchClanPowerRewardAsk(FetchClanPowerRewardAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.info("handleFetchClanPowerRewardAsk clanEntity is null or destroy, ask={}", ask);
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        final List<Pair<Integer, Integer>> result = clanEntity.getMemberComponent().settlePlayerResources(ask.getPlayerId());
        if (result == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }
        final FetchClanPowerRewardAns.Builder ans = FetchClanPowerRewardAns.newBuilder();
        for (final Pair<Integer, Integer> item : result) {
            ans.putResources(item.getKey(), item.getValue());
        }
        clanActor.answer(ans.build());
    }

    @Override
    public void handleUpdateClanMemberInfoCmd(UpdateClanMemberInfoCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleUpdateClanMemberInfoCmd clan is null={}", clanActor.getClanId());
            return;
        }
        StructClan.ClanMember pb = MsgUtils.parseProto(StructClan.ClanMember.getDefaultInstance(), ask.getChange());
        clanEntity.getMemberComponent().updateClanMemberInfo(ask.getPlayerId(), pb);
    }

    @Override
    public void handlePlayerCancelApplyCmd(PlayerCancelApplyCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            LOGGER.warn("handlePlayerCancelApplyCmd clan is null={}", clanActor.getClanId());
            return;
        }
        clanEntity.getMemberComponent().removeApply(ask.getPlayerId());
    }

    @Override
    public void handleApplyOwnerDissolvingAsk(ApplyOwnerDissolvingAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST.getCodeId());
        }
        clanEntity.getStageComponent().applyOwnerWhenDissolving(ask.getPlayerId());
        ApplyOwnerDissolvingAns.Builder ans = ApplyOwnerDissolvingAns.newBuilder();
        clanActor.answer(ans.build());
    }

    @Override
    public void handleCancelApplyOwnerDissolvingAsk(CancelApplyOwnerDissolvingAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST.getCodeId());
        }
        clanEntity.getStageComponent().cancelApplyOwnerWhenDissolving(ask.getPlayerId());
        clanActor.answer(CancelApplyOwnerDissolvingAns.getDefaultInstance());
    }

    @Override
    public void handleSyncPlayerTopNHeroCmd(SyncPlayerTopNHeroCmd ask) {
        final ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST.getCodeId());
        }
        final ClanMemberProp clanMemberProp = clanEntity.getMemberComponent().getMember(ask.getPlayerId());
        clanMemberProp.getTopHero().clear();
        for (Struct.SimpleHero simpleHero : ask.getHeroListList()) {
            final SimpleHeroProp simpleHeroProp = new SimpleHeroProp();
            simpleHeroProp.mergeFromSs(simpleHero);
            clanMemberProp.getTopHero().add(simpleHeroProp);
        }
    }
}

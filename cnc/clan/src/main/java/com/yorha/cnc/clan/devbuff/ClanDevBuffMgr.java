package com.yorha.cnc.clan.devbuff;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.SsSceneClan;
import res.template.BuffTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ClanDevBuffMgr extends DevBuffMgrBase<ClanEntity> {
    public ClanDevBuffMgr(ClanEntity owner) {
        super(owner);
    }

    @Override
    public DevBuffSysProp getDevBuffSys() {
        return getOwner().getProp().getDevBuffSys();
    }

    @Override
    public CommonEnum.DevBuffType getBuffType() {
        return CommonEnum.DevBuffType.DBT_CLAN_BUFF;
    }

    @Override
    protected void addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        getOwner().ownerActor().addTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    protected void cancelTimer(String prefix, TimerReasonType timerReasonType) {
        ActorTimer timer = getOwner().ownerActor().getTimer(prefix, timerReasonType);
        if (timer != null) {
            timer.cancel();
        }
    }

    @Override
    public DevBuffMgrBase.LOGLEVEL logLevel() {
        return LOGLEVEL.INFO;
    }

    @Override
    protected void afterAddDevBuff(DevBuffProp devBuff, int addLayer) {
        // 不管是player侧用的还是clan用的，都update到clan的加成中
        BuffTemplate buffTemplate = getBuffTemplate(devBuff.getDevBuffId());
        getOwner().getAddComponent().updateAddition(AdditionProviderType.BUFF, buffTemplate.getType());

        if (DevBuffUtil.isClanSelfBuff(devBuff.getDevBuffId())) {
            // 只作用于联盟自己的buff不同步给player
            return;
        }
        CommonMsg.DevBuffAddParam param = CommonMsg.DevBuffAddParam.newBuilder()
                .setDevBuffId(devBuff.getDevBuffId())
                .setBuffType(devBuff.getDevBuffType())
                .setStartTime(devBuff.getStartTime())
                .setEndTime(devBuff.getEndTime())
                .setSourceType(devBuff.getSourceType())
                .setLayer(addLayer)
                .build();
        // scene要用的buff同步给scenePlayer
        if (DevBuffUtil.isSceneBuff(devBuff.getDevBuffId())) {
            SsSceneClan.AddDevBuffFromClanCmd.Builder cmd = SsSceneClan.AddDevBuffFromClanCmd.newBuilder()
                    .setClanId(getOwner().getEntityId())
                    .setParam(param);
            getOwner().ownerActor().tellBigScene(cmd.build());
        } else {
            // player侧要用的，通知在线成员
            for (Long playerId : getOwner().getMemberComponent().getAllClanOnlinePlayerIds()) {
                getOwner().ownerActor().tellPlayer(playerId, SsPlayerClan.OnClanDevBuffUpdateCmd.newBuilder()
                        .setClanId(getOwner().getEntityId())
                        .setIsAdd(true)
                        .setDevBuffProp(param).build());
            }
        }
    }

    @Override
    protected void afterRemoveDevBuff(DevBuffProp devBuffToRemove, boolean isExpired, int decLayer) {
        // 不管是player侧用的还是clan用的，都update到clan的加成中
        BuffTemplate buffTemplate = getBuffTemplate(devBuffToRemove.getDevBuffId());
        getOwner().getAddComponent().updateAddition(AdditionProviderType.BUFF, buffTemplate.getType());

        if (DevBuffUtil.isClanSelfBuff(devBuffToRemove.getDevBuffId())) {
            // 只作用于联盟自己的buff不同步给player
            return;
        }
        // scene要用的buff同步给scenePlayer
        if (DevBuffUtil.isSceneBuff(devBuffToRemove.getDevBuffId())) {
            CommonMsg.DevBuffRemoveParam param = CommonMsg.DevBuffRemoveParam.newBuilder()
                    .setDevBuffId(devBuffToRemove.getDevBuffId())
                    .setLayer(decLayer)
                    .build();
            SsSceneClan.RemoveDevBuffFromClanCmd.Builder cmd = SsSceneClan.RemoveDevBuffFromClanCmd.newBuilder()
                    .setClanId(getOwner().getEntityId())
                    .setParam(param);
            getOwner().ownerActor().tellBigScene(cmd.build());
        } else {
            // player侧要用的，通知在线成员
            CommonMsg.DevBuffAddParam param = CommonMsg.DevBuffAddParam.newBuilder()
                    .setDevBuffId(devBuffToRemove.getDevBuffId())
                    .setLayer(decLayer)
                    .build();
            for (Long playerId : getOwner().getMemberComponent().getAllClanOnlinePlayerIds()) {
                getOwner().ownerActor().tellPlayer(playerId, SsPlayerClan.OnClanDevBuffUpdateCmd.newBuilder()
                        .setClanId(getOwner().getEntityId())
                        .setIsAdd(false)
                        .setDevBuffProp(param)
                        .build());
            }
        }
    }
}

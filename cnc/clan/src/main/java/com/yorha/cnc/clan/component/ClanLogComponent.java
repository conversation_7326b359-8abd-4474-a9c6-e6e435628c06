package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanPrivilegeRole;
import com.yorha.common.resource.resservice.clan.StaffHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.game.gen.prop.ClanLogModelProp;
import com.yorha.game.gen.prop.ClanLogRecordModelProp;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ClanRecordType;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.Struct;
import com.yorha.proto.Struct.ClanLogItem;
import com.yorha.proto.Struct.ClanRecord;
import com.yorha.proto.Struct.DisplayData;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

/**
 * <AUTHOR>
 */
public class ClanLogComponent extends ClanComponent {

    private static final Logger LOGGER = LogManager.getLogger(ClanLogComponent.class);

    public ClanLogComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 联盟加载后
     */
    @Override
    public void onLoad() {
        ConstClanTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        long configMaxLogCnt = template.getMaxNumOfAllianceLogs();
        long nowMaxLogCnt = getClanLogProp().getLogBasicInfo().getMaxRecordNum();
        if (configMaxLogCnt == nowMaxLogCnt) {
            return;
        }
        if (configMaxLogCnt < nowMaxLogCnt) {
            onMaxRecordNumReduced(configMaxLogCnt);
        }
        getClanLogProp().getLogBasicInfo().setMaxRecordNum(configMaxLogCnt);
    }

    /**
     * 创建联盟时
     */
    @Override
    public void onCreate() {
        ConstClanTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        getClanLogProp().getLogBasicInfo().setMaxRecordNum(template.getMaxNumOfAllianceLogs())
                .setNextRecordId(0).setBeginRecordId(0);
    }


    /**
     * 当最大记录数变小时
     *
     * @param configMaxCnt 配置最大记录数
     */
    private void onMaxRecordNumReduced(long configMaxCnt) {
        long curRecordNum = getClanLogProp().getRecord().getLogRecordSize();
        if (curRecordNum <= configMaxCnt) {
            // 当前记录数量小于新更改的配置最大值，无需任何操作
            return;
        }
        long needDelLastLoc = getClanLogProp().getLogBasicInfo().getNextRecordId() - configMaxCnt;
        long beginLoc = getClanLogProp().getLogBasicInfo().getBeginRecordId();
        for (long loc = beginLoc; loc < needDelLastLoc; ++loc) {
            getClanLogProp().getRecord().removeLogRecordV(loc);
        }
    }

    /**
     * 职位更改日志通用入口
     *
     * @param oldStaffId 被更改职位的玩家原来的的staffId
     * @param newStaffId 被更改职位的玩家新的staffId
     * @param operatorId 操作的玩家id
     * @param playerName 被更改职位的玩家名称
     */
    public void logStaffChange(int oldStaffId, int newStaffId, long operatorId, String playerName) {
        // operatorId为0时，不存在玩家主动调整职位，一般为系统调整，无需记录
        if (operatorId <= 0L) {
            return;
        }
        StaffHolder oldStaffHolder = this.getOwner().getDataTemplateService().getStaffHolder(oldStaffId);
        StaffHolder newStaffHolder = this.getOwner().getDataTemplateService().getStaffHolder(newStaffId);
        int oldRole = oldStaffHolder.getRole();
        int newRole = newStaffHolder.getRole();
        if (ClanPrivilegeRole.isLowRole(newStaffHolder.getRole())) {
            logLowLevelChange(operatorId, playerName, oldRole, newRole);
        } else if (ResHolder.getResService(ConstClanKVResService.class).getTemplate().getSpecialStaffIDs().contains(newStaffId)) {
            // NOTE(furson): 来自阳了的furson，这边把逻辑往上移了，原来这个逻辑从来没走到过，因为会先remove掉职位变为一般人再任命，所以永远会被现在下面的if拦掉，有些危险，回头得看看
            logGrantSpecialStaff(operatorId, playerName, newStaffId);
        } else if (ClanPrivilegeRole.isLowRole(oldStaffHolder.getRole()) && ClanPrivilegeRole.isHighRole(newStaffHolder.getRole())) {
            logLevelMember(operatorId, playerName);
        }
    }

    /**
     * 玩家更改为高职位的日志
     *
     * @param operatorPlayerId 操作的玩家id
     * @param playerName       被更改职位的玩家名称
     */
    private void logLevelMember(long operatorPlayerId, String playerName) {
        final long aimLevel = 4L;
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_UP_LEVEL_MEMBER);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorPlayerId)))
                .addDatas(MsgHelper.buildDisPlayText(playerName))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64, aimLevel));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 玩家低职位内调整的日志
     *
     * @param operatorPlayerId 操作的玩家id
     * @param playerName       被更改职位的玩家名称
     * @param oldLevel         调整前的玩家职位等级
     * @param newLevel         调整后的玩家职位等级
     */
    private void logLowLevelChange(long operatorPlayerId, String playerName, int oldLevel, int newLevel) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_LEVEL_CHANGE);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorPlayerId)))
                .addDatas(MsgHelper.buildDisPlayText(playerName))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64, oldLevel))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64, newLevel));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 授予特殊职位日志
     *
     * @param operatorPlayerId       操作玩家id
     * @param grantedStaffPlayerName 被授予职位的玩家名字
     * @param staffId                职位id
     */
    private void logGrantSpecialStaff(long operatorPlayerId, String grantedStaffPlayerName, int staffId) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_GRANT_SPECIAL_STAFF);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorPlayerId)))
                .addDatas(MsgHelper.buildDisPlayText(grantedStaffPlayerName))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, staffId));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 转让军团长日志
     *
     * @param callerPlayerId 主动转让军团长的玩家id
     * @param calleePlayerId 获得军团长职位的玩家id
     */
    public void logTransferOwner(long callerPlayerId, long calleePlayerId) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_TRANSFER_OWNER);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(callerPlayerId)))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(calleePlayerId)));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 改建建筑日志
     *
     * @param msg 改建建筑日志结构
     */
    public void logRebuild(SsClanAttr.RebuildLogMsg msg) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_REBUILD);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(msg.getRebuildPlayerName()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, msg.getTemplateId()));
        addCanJumpPointParam(paramBuilder, msg.getPoint());
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 拆毁建筑日志
     *
     * @param msg 拆毁建筑日志结构
     */
    public void logDestroy(SsClanAttr.DestroyLogMsg msg) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_DESTROY);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(msg.getDestroyPlayerName()))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, msg.getTemplateId()));
        addCanJumpPointParam(paramBuilder, msg.getPoint());
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 军团建筑被攻击日志
     *
     * @param msg 被攻击日志记录所需结构
     */
    public void logBeAttacked(SsClanAttr.BeAttackedLogMsg msg) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_BUILDING_ATTACKED);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, msg.getTemplateId()))
                .addDatas(MsgHelper.buildDisPlayText(msg.getClanSName()))
                .addDatas(MsgHelper.buildDisPlayText(msg.getAttackerPlayerName()));
        addCanJumpPointParam(paramBuilder, msg.getPoint());
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 开始攻击日志
     *
     * @param msg 开始攻击日志所需结构
     */
    public void logStartAttack(SsClanAttr.StartAttackLogMsg msg) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_START_ATTACK);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, msg.getTemplateId()));
        addCanJumpPointParam(paramBuilder, msg.getPoint());
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 占领日志
     *
     * @param msg 占领日志所需结构
     */
    public void logOccupy(SsClanAttr.OccupyLogMsg msg) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_OCCUPY);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, msg.getTemplateId()))
                .addDatas(MsgHelper.buildDisPlayText(msg.getLastHitPlayerName()));
        addCanJumpPointParam(paramBuilder, msg.getPoint());
        builder.setParams(paramBuilder);
        recordLog(builder.build());

    }

    private void addCanJumpPointParam(DisplayData.Builder paramBuilder, Struct.Point point) {
        CommonEnum.MapType mapType = CommonEnum.MapType.forNumber(point.getMapType());
        if (mapType == null) {
            LOGGER.error("addCanJumpPointParam mapType is null! point={}", point);
            mapType = CommonEnum.MapType.MAT_NONE;
        }
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildGotoDisplayPoint(point.getX(), point.getY(), mapType, point.getMapId()));
    }

    /**
     * 踢人军团日志
     *
     * @param operatorPlayerId 操作玩家id
     * @param quitPlayerName   退出玩家的名字
     */
    public void logKickOff(long operatorPlayerId, String quitPlayerName) {
        if (getOwner().getStageComponent().isStartDissolve()) {
            // 开始解散了还打什么军团日志；解散状态下可能会出现kickAll，这个时候因为是系统踢人，operatorPlayerId为0
            return;
        }
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_KICK_OFF);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorPlayerId)))
                .addDatas(MsgHelper.buildDisPlayText(quitPlayerName));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }


    /**
     * 主动退出军团日志
     *
     * @param playerName 退出军团的玩家名字
     */
    public void logQuitClan(String playerName) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_QUIT_CLAN);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(playerName));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 审批进入军团日志
     *
     * @param operatorPlayerId 操作的玩家id
     * @param enterPlayerName  进入军团的玩家名字
     */
    public void logApproveApply(long operatorPlayerId, String enterPlayerName) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_APPROVE_APPLY);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getMemberComponent().getMemberNameById(operatorPlayerId)))
                .addDatas(MsgHelper.buildDisPlayText(enterPlayerName))
                .addDatas(MsgHelper.buildDisPlayText(enterPlayerName));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 进入军团日志
     *
     * @param playerName 进入军团的玩家名字
     */
    public void logEnterClan(String playerName) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_ENTER_CLAN);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(playerName));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 邀请进入军团日志
     *
     * @param joinPlayerName   进入军团的玩家名字
     * @param invitePlayerName 邀请进入军团的玩家名字
     */
    public void logInviteToClan(String joinPlayerName, String invitePlayerName) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_INVITE);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(joinPlayerName))
                .addDatas(MsgHelper.buildDisPlayText(invitePlayerName));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 记录礼物升级日志
     *
     * @param giftLevel 礼物等级
     */
    public void logGiftLevelUp(int giftLevel) {
        ClanRecord.Builder builder = getBuilderByType(ClanRecordType.CRT_LOG_GIFT_LEVEL);
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64, giftLevel));
        builder.setParams(paramBuilder);
        recordLog(builder.build());
    }

    /**
     * 获取日志记录
     *
     * @return
     */
    public ClanLogRecordModelProp getRecords() {
        return getClanLogProp().getRecord();
    }

    /**
     * 记录日志
     */
    private void recordLog(ClanRecord record) {
        if (record == null) {
            LOGGER.error("record is null");
            return;
        }
        ClanLogItem.Builder logItemBuilder = ClanLogItem.newBuilder();
        // 设置id，id设置出去后马上更新
        long nextId = getClanLogProp().getLogBasicInfo().getNextRecordId();
        logItemBuilder.setId(nextId);
        getClanLogProp().getLogBasicInfo().setNextRecordId(nextId + 1L);

        logItemBuilder.setLogRecord(record);
        logItemBuilder.setCreateTsMs(SystemClock.now());

        getClanLogProp().getRecord().addEmptyLogRecord(logItemBuilder.getId()).mergeFromSs(logItemBuilder.build());
        afterLogAdd();
    }

    /**
     * log增加后处理，判断是否超过最大可记录log，若超过删除最早记录的一条日志
     */
    private void afterLogAdd() {
        long beginId = getClanLogProp().getLogBasicInfo().getBeginRecordId();
        long curRecordNum = getClanLogProp().getRecord().getLogRecordSize();
        long maxRecordNum = getClanLogProp().getLogBasicInfo().getMaxRecordNum();
        if (curRecordNum > maxRecordNum + 1L) {
            LOGGER.error("curRecordNum {} cannot bigger than maxRecordNum {} + 1L when only add one log",
                    curRecordNum, maxRecordNum);
        }
        if (curRecordNum == maxRecordNum + 1L) {
            getClanLogProp().getRecord().removeLogRecordV(beginId);
            getClanLogProp().getLogBasicInfo().setBeginRecordId(beginId + 1L);
        }
    }

    private ClanRecord.Builder getBuilderByType(ClanRecordType type) {
        ClanRecord.Builder recordBuilder = ClanRecord.newBuilder();
        recordBuilder.setRecordType(type);
        return recordBuilder;
    }

    private ClanLogModelProp getClanLogProp() {
        return getOwner().getProp().getLogModel();
    }
}

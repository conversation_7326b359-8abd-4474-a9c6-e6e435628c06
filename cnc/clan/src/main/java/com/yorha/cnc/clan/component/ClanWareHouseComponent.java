package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.ClanResourcesProp;
import com.yorha.common.utils.FormulaUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.WarehouseInfoProp;
import com.yorha.proto.Clan.ClanResources;
import com.yorha.proto.CommonEnum.BuffEffectType;
import com.yorha.proto.CommonEnum.ClanRecordType;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.CommonEnum.MapBuildingType;
import com.yorha.proto.Struct.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;
import res.template.MapBuildingTemplate;

import java.util.List;

public class ClanWareHouseComponent extends ClanComponent {

    private static final Logger LOGGER = LogManager.getLogger(ClanWareHouseComponent.class);

    public ClanWareHouseComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 联盟加载后
     */
    @Override
    public void onLoad() {
        ConstClanTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        long configMaxLogCnt = template.getAllianceWarehouseLog();
        long nowMaxLogCnt = getClanResourcesProp().getWareHouseLogBasicInfo().getMaxRecordNum();
        if (configMaxLogCnt != nowMaxLogCnt) {
            if (configMaxLogCnt < nowMaxLogCnt) {
                onMaxRecordNumReduced(configMaxLogCnt);
            }
            getClanResourcesProp().getWareHouseLogBasicInfo().setMaxRecordNum(configMaxLogCnt);
        }
        // 根据加成刷新最大存储量
        refreshMaxCapacityWhenLoad();
    }

    /**
     * 创建联盟时
     */
    @Override
    public void onCreate() {
        ConstClanTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        int initValue = template.getAllianceInitialPoints();
        if (template.getAllianceInitialTopLimitResource() == null) {
            return;
        }
        for (IntPairType pair : template.getAllianceInitialTopLimitResource()) {
            getClanResourcesProp().addEmptyWarehouseMap(pair.getKey()).setMaxCount(pair.getValue());
            getClanResourcesProp().addEmptyResourceInfos(pair.getKey()).setCount(initValue);
        }
        getClanResourcesProp().setLastCalTsMs(SystemClock.now());
        getClanResourcesProp().getWareHouseLogBasicInfo().setMaxRecordNum(template.getAllianceWarehouseLog())
                .setNextRecordId(0).setBeginRecordId(0);
    }


    /**
     * 当最大记录数变小时
     *
     * @param configMaxCnt 配置最大记录数
     */
    private void onMaxRecordNumReduced(long configMaxCnt) {
        long curRecordNum = getClanResourcesProp().getUsageRecordSize();
        if (curRecordNum <= configMaxCnt) {
            // 当前记录数量小于新更改的配置最大值，无需任何操作
            return;
        }
        long needDelLastLoc = getClanResourcesProp().getWareHouseLogBasicInfo().getNextRecordId() - configMaxCnt;
        long beginLoc = getClanResourcesProp().getWareHouseLogBasicInfo().getBeginRecordId();
        for (long loc = beginLoc; loc < needDelLastLoc; ++loc) {
            getClanResourcesProp().removeUsageRecordV(loc);
        }
    }

    /**
     * 构建建设军团建筑相关的资源消耗记录
     */
    public ClanRecord buildClanBuildingRecord(ClanRecordType type, String playerName, int buildingId) {
        if (type != ClanRecordType.CRT_WAREHOUSE_BUILD && type != ClanRecordType.CRT_WAREHOUSE_EXTINGUISH) {
            LOGGER.error("wrong type {} have been sent", type);
            return null;
        }
        ClanRecord.Builder record = ClanRecord.newBuilder();
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        record.setRecordType(type);
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(playerName))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, buildingId));
        record.setParams(paramBuilder.build());
        return record.build();
    }

    /**
     * 构建建筑军团资源中心相关的资源消耗记录
     */
    public ClanRecord buildClanResBuildingRecord(ClanRecordType type, String playerName, MapBuildingType buildingType) {
        if (type != ClanRecordType.CRT_WAREHOUSE_BUILD) {
            LOGGER.error("wrong type {} have been sent", type);
            return null;
        }
        ClanRecord.Builder record = ClanRecord.newBuilder();
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        record.setRecordType(type);

        for (MapBuildingTemplate template : ResHolder.getInstance().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getType() == buildingType) {
                paramBuilder.getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayText(playerName))
                        .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, template.getId()));
                record.setParams(paramBuilder.build());
                return record.build();
            }
        }
        LOGGER.warn("cannot find template for {} where try build clan res building", buildingType);
        return null;
    }

    public ClanRecord buildTechDonateRecord(String playerName, int techSubId) {
        ClanRecord.Builder record = ClanRecord.newBuilder();
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        record.setRecordType(ClanRecordType.CRT_WAREHOUSE_RESEARCH);
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(playerName))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_CLAN_TECH_SUB_ID, techSubId));
        record.setParams(paramBuilder.build());
        return record.build();
    }

    /**
     * 记录
     */
    public void recordWareHouseLog(long playerId, PlayerCardHead cardHead, ClanRecord record,
                                   List<IntPairType> cost) {
        if (record == null) {
            LOGGER.error("record is null");
            return;
        }
        WarehouseUsageItem.Builder builder = WarehouseUsageItem.newBuilder();
        // 设置id，id设置出去后马上更新
        long nextId = getClanResourcesProp().getWareHouseLogBasicInfo().getNextRecordId();
        builder.setId(nextId);
        getClanResourcesProp().getWareHouseLogBasicInfo().setNextRecordId(nextId + 1L);

        // 设置使用的资源数据
        Int32SingleClanResourceInfoMap.Builder mapBuilder = Int32SingleClanResourceInfoMap.newBuilder();
        for (IntPairType pair : cost) {
            SingleClanResourceInfo.Builder resourceInfoBuilder = SingleClanResourceInfo.newBuilder();
            mapBuilder.putDatas(pair.getKey(), resourceInfoBuilder.setType(pair.getKey()).setCount(pair.getValue()).build());
        }
        builder.setUseResourceInfos(mapBuilder.build());

        // 设置其他显示数据
        builder.setPlayerId(playerId).setCreateTsMs(SystemClock.now()).setUsageRecord(record);
        builder.setCardHead(cardHead);

        getClanResourcesProp().addEmptyUsageRecord(builder.getId()).mergeFromSs(builder.build());
        // 增加log后特殊逻辑判断
        afterWareHouseLogAdd();
    }

    /**
     * 获取仓库界面所需信息
     */
    public ClanResources getAllWarehouseInfo() {
        return getOwner().getProp().getResources().getCopySsBuilder().build();
    }

    /**
     * 根据加成刷新仓库资源的最大容量，只设置最大容量，若已经超过最大容量，这个方法不会将超出部分去掉
     * 存在事件丢失（如停机）的可能性，将在重新加载军团时主动刷新
     *
     * @param entity     军团实体
     * @param additionId 加成id
     * @param newValue   加成id对应的新的加成值
     */
    public static void refreshMaxCapacity(ClanEntity entity, int additionId, Long newValue) {
        // 不是仓库最大容量的加成不管
        if (BuffEffectType.ET_CLAN_WAREHOUSE_CAPACITY_PERCENT != BuffEffectType.forNumber(additionId)) {
            return;
        }
        ConstClanTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        for (IntPairType pair : template.getAllianceInitialTopLimitResource()) {
            long maxCapacity = FormulaUtils.f1(pair.getValue(), 0, newValue, 1);
            entity.getResourcesComponent().setMaxCapacityWithType(pair.getKey(), maxCapacity);
        }
    }

    /**
     * 加载时刷新仓库资源的最大容量
     */
    private void refreshMaxCapacityWhenLoad() {
        ConstClanTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        long addition = getOwner().getAddComponent().getAddition(BuffEffectType.ET_CLAN_WAREHOUSE_CAPACITY_PERCENT);
        for (IntPairType pair : template.getAllianceInitialTopLimitResource()) {
            long maxCapacity = FormulaUtils.f1(pair.getValue(), 0, addition, 1);
            ClanResourcesProp resources = getOwner().getProp().getResources();
            WarehouseInfoProp warehouseMapV = resources.getWarehouseMapV(pair.getKey());
            // 新的资源类型，需要put一下
            if (warehouseMapV == null) {
                warehouseMapV = resources.addEmptyWarehouseMap(pair.getKey());
            }
            long oldValue = warehouseMapV.getMaxCount();
            if (oldValue == maxCapacity) {
                continue;
            }
            // 设置新的最大容量
            warehouseMapV.setMaxCount(maxCapacity);
            LOGGER.info("refreshMaxCapacityWhenLoad type={} old={} new={} ", pair.getKey(), oldValue, warehouseMapV.getMaxCount());
        }
    }

    /**
     * log增加后处理，判断是否超过最大可记录log，若超过删除最早记录的一条日志
     */
    private void afterWareHouseLogAdd() {
        long beginId = getClanResourcesProp().getWareHouseLogBasicInfo().getBeginRecordId();
        long curRecordNum = getClanResourcesProp().getUsageRecordSize();
        long maxRecordNum = getClanResourcesProp().getWareHouseLogBasicInfo().getMaxRecordNum();
        if (curRecordNum > maxRecordNum + 1L) {
            LOGGER.error("curRecordNum {} cannot bigger than maxRecordNum {} + 1L when only add one log",
                    curRecordNum, maxRecordNum);
        }
        if (curRecordNum == maxRecordNum + 1L) {
            getClanResourcesProp().removeUsageRecordV(beginId);
            getClanResourcesProp().getWareHouseLogBasicInfo().setBeginRecordId(beginId + 1L);
        }
    }

    private ClanResourcesProp getClanResourcesProp() {
        return getOwner().getProp().getResources();
    }
}

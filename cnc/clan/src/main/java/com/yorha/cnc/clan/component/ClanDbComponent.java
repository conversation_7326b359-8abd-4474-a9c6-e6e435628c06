package com.yorha.cnc.clan.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.clan.ClanActor;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.*;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MonitorUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanBaseInfoProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.proto.Clan;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneClan;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ClanDbComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanDbComponent.class);
    private boolean isDissolution = false;
    /**
     * 操作增删改查的代理。
     */
    private final DbTaskProxy dbTaskProxy;

    public ClanDbComponent(ClanEntity owner, Clan.ClanEntity changedProp) {
        super(owner);
        final ClanDbActorUpsertStrategy strategy = new ClanDbActorUpsertStrategy(changedProp != null ? changedProp : Clan.ClanEntity.getDefaultInstance());
        this.dbTaskProxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString())
                .owner(this.ownerActor())
                .limitTimerOwner(ownerActor())
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .upsert(strategy)
                .delete(strategy)
                .intervalMs(DbLimitConstants.CLAN_DB_LIMIT_INTERVAL_MS)
                .entityId(String.valueOf(this.getEntityId()))
                .build();
    }

    @Override
    public void onCreate() {
        this.dbTaskProxy.insert();
    }

    @Override
    public void onDissolution() {
        isDissolution = true;
        ClanBaseInfoProp prop = getOwner().getProp().getBase();
        List<Pair<CommonEnum.NameType, String>> list = new ArrayList<>();
        list.add(Pair.of(CommonEnum.NameType.CLAN_NAME, prop.getName()));
        list.add(Pair.of(CommonEnum.NameType.CLAN_SIMPLE_NAME, prop.getSname()));
        NameHelper.releaseNames(ownerActor(), list, getEntityId(), true);
        dbTaskProxy.delete();
        LOGGER.info("clan: db proxy delete, {} dissolution.", getEntityId());
        // 通知大世界地图联盟解散
        ownerActor().tellBigScene(SsSceneClan.OnClanDissolutionCmd.newBuilder().setClanId(getEntityId()).build());
    }

    /**
     * 属性增量更新同步db
     */
    public void saveChangeToDb() {
        this.dbTaskProxy.update();
    }

    /**
     * 属性增量更新同步db
     */
    public void saveOnDestroy() {
        if (!this.getOwner().isDestroy()) {
            WechatLog.error("ClanDbComponent endDb {} is not destroy!", getOwner());
        }
        if (!isDissolution) {
            this.dbTaskProxy.saveDbSync();
        }
        this.dbTaskProxy.stop();
    }

    private static class ClanDbActorUpsertStrategy extends ActorChangeAttrUpsertStrategy<Clan.ClanEntity> implements IDbChangeDeleteStrategy {

        ClanDbActorUpsertStrategy(GeneratedMessageV3 msg) {
            super(msg);
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            final ClanActor clanActor = (ClanActor) actor;
            return clanActor.getClanEntity().getProp().copyChangeToDb((Clan.ClanEntity.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected Clan.ClanEntity buildFullAttrSaveData(AbstractActor actor) {
            final ClanActor clanActor = (ClanActor) actor;
            return clanActor.getClanEntity().getProp().getCopyDbBuilder().build();
        }

        @Override
        protected Clan.ClanEntity buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            final TcaplusDb.ClanTable.Builder recordBuilder = (TcaplusDb.ClanTable.Builder) result.value;
            final ClanProp prop = ClanProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
            return prop.getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Clan.ClanEntity fullAttr, @NotNull Clan.ClanEntity changeAttr) {
            final ClanActor clanActor = (ClanActor) actor;
            final TcaplusDb.ClanTable.Builder request = TcaplusDb.ClanTable.newBuilder();
            request.setZoneId(clanActor.getZoneId()).setClanId(clanActor.getClanId());
            if (fullAttr != null) {
                request.setFullAttr(fullAttr);
                long dbSize = fullAttr.getSerializedSize() + changeAttr.getSerializedSize();
                MonitorUnit.DB_CLAN_FULL_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId()).inc();
                MonitorUtils.monitorClanEntity(actor, fullAttr);
                DbMgrService.getInstance().getDbPerfLogger().onClanSaveDb(clanActor.getClanId(), dbSize);
            } else {
                MonitorUnit.DB_CLAN_CHANGE_ATTR_SAVE_TOTAL.labels(ServerContext.getBusId()).inc();
            }
            // 就是为了全量生成的时候要清空db中的changedAttr
            request.setChangedAttr(changeAttr);
            return request;
        }

        @Override
        public Message.Builder buildDeleteRequest(AbstractActor actor) {
            final ClanActor clanActor = (ClanActor) actor;
            TcaplusDb.ClanTable.Builder req = TcaplusDb.ClanTable.newBuilder();
            req.setZoneId(clanActor.getZoneId()).setClanId(clanActor.getClanId());
            return req;
        }

    }

    @Override
    public String toString() {
        return "ClanDbComponent{" +
                "clanId=" + this.getEntityId() +
                '}';
    }
}

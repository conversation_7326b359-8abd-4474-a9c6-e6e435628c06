package com.yorha.cnc.clan.rank;

import com.yorha.cnc.clan.ClanActor;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.rank.AbstractRankEntity;
import com.yorha.common.rank.RankMember;
import com.yorha.common.rank.RankPageDTO;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.StructMsg.RankInfoDTO;
import com.yorha.proto.StructPB;
import io.gamioo.redis.zset.long2object.Long2ObjectEntry;
import org.apache.commons.collections4.CollectionUtils;
import res.template.ConstRankTemplate;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 联盟排行
 *
 * <AUTHOR>
 */
public class ClanRankEntity extends AbstractRankEntity {

    public ClanRankEntity(int rankId, long clanId, ClanActor clanActor, boolean isCreate) {
        super(clanActor, clanActor.getZoneId(), rankId, clanId + ":" + rankId, isCreate);
    }

    @Override
    public ClanActor ownerActor() {
        return (ClanActor) super.ownerActor();
    }

    /**
     * 注: 结果：排名从1开始
     */
    @Override
    public void getRankPageInfo(long memberId, int page, BiConsumer<Integer, RankPageDTO> onComplete) {
        int pageSize = ResHolder.getInstance().getConstTemplate(ConstRankTemplate.class).getPageSize();

        int start = (page - 1) * pageSize;
        int end = page * pageSize - 1;
        int rankShow = getRankShow();
        if (rankShow > 0) {
            end = Math.min(end, rankShow - 1);
        }
        List<Long2ObjectEntry<RankMember>> array = Collections.emptyList();
        if (page > 0) {
            array = list.zrangeByRank(start, end);
        }
        Set<Long> playerIdSet = new HashSet<>();
        if (memberId > 0) {
            playerIdSet.add(memberId);
        }
        for (Long2ObjectEntry<RankMember> e : array) {
            if (e.getLongMember() != memberId) {
                playerIdSet.add(e.getLongMember());
            }
        }
        if (playerIdSet.isEmpty()) {
            onComplete.accept(getTotalSize(), new RankPageDTO());
            return;
        }
        List<Long2ObjectEntry<RankMember>> finalArray = array;
        CardHelper.batchQueryPlayerHead(ownerActor(), playerIdSet,
                (players) -> {
                    RankPageDTO ret = new RankPageDTO();
                    for (int i = 0; i < finalArray.size(); i++) {
                        Long2ObjectEntry<RankMember> member = finalArray.get(i);
                        RankMember rankMember = member.getScore();
                        StructPB.PlayerCardHeadPB pb = players.get(rankMember.getId());
                        if (pb != null) {
                            RankInfoDTO rankInfoDTO = buildRankInfoDTO(start + i + 1, rankMember, pb);
                            ret.add2List(rankInfoDTO);
                        }
                    }
                    //加上我自己的
                    if (list.contain(memberId)) {
                        int rank = list.zrank(memberId);
                        RankMember rankMember = list.zscore(memberId);
                        StructPB.PlayerCardHeadPB pb = players.get(memberId);
                        RankInfoDTO rankInfoDTO = buildRankInfoDTO(rank + 1, rankMember, pb);
                        ret.setDto(rankInfoDTO);
                    }
                    onComplete.accept(getTotalSize(), ret);
                });
    }

    @Override
    public void getRankByPlayerList(Collection<Long> ranksList, Consumer<Map<Long, RankInfoDTO>> onComplete) {
        throw new GeminiException(ErrorCode.FAILED, "禁用的查询类型");
    }

    @Override
    public void getRankByRankList(Set<Integer> ranksList, Consumer<Map<Long, RankInfoDTO>> onComplete) {
        throw new GeminiException(ErrorCode.FAILED, "禁用的查询类型");
    }

    @Override
    public int getTargetRanks(long memberId, long score) {
        throw new GeminiException(ErrorCode.FAILED, "禁用的查询类型");
    }

    @Override
    public void getTopRankInfo(Consumer<RankInfoDTO> onComplete) {
        throw new GeminiException(ErrorCode.FAILED, "禁用的查询类型");
    }

    public Long getTopMemberId() {
        List<Long2ObjectEntry<RankMember>> array = list.zrangeByRank(0, 1);
        if (!CollectionUtils.isEmpty(array)) {
            RankMember rankMember = array.get(0).getScore();
            return rankMember.getId();
        }
        return 0L;
    }

    /**
     * 注: 结果：排名从1开始
     */
    private RankInfoDTO buildRankInfoDTO(int rank, RankMember rankMember, StructPB.PlayerCardHeadPB pb) {
        RankInfoDTO.Builder dto = RankInfoDTO.newBuilder();
        dto.setRank(rank)
                .setPlayerId(rankMember.getId())
                .setValue(rankMember.getScore())
                .setCardHead(pb)
                .setStaffId(ownerActor().getOrLoadClanEntity().getStaffComponent().getPlayerStaffId(rankMember.getId()));
        dto.setRankId(rankId);
        return dto.build();
    }
}

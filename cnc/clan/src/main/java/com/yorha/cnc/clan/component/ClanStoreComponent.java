package com.yorha.cnc.clan.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ClanStoreItemInfoProp;
import com.yorha.game.gen.prop.ClanStoreLogItemProp;
import com.yorha.game.gen.prop.ClanStoreModelProp;
import com.yorha.game.gen.prop.PlayerCardHeadProp;
import com.yorha.gemini.props.MapPropKVQueue;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.ClanRecordType;
import com.yorha.proto.CommonEnum.ClanStoreInterfaceType;
import com.yorha.proto.CommonEnum.ClanStoreOperateReturnType;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf;
import com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf;
import com.yorha.proto.SsClanStore.FetchClanStoreAns;
import com.yorha.proto.Struct.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanStoreTemplate;
import res.template.ConstClanTemplate;

import javax.annotation.Nullable;
import java.util.*;

/**
 * 军团商店组件
 *
 * <AUTHOR>
 */
public class ClanStoreComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanStoreComponent.class);

    private final Map<ClanRecordType, MapPropKVQueue<Long, ClanStoreLogItemProp>> storeLogsMap = Maps.newHashMap();

    public ClanStoreComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void onCreate() {
        initClanStoreLog();
    }

    @Override
    public void onLoad() {
        initClanStoreLog();
    }

    /**
     * 根据界面拉取军团商店信息
     *
     * @param interfaceType 界面枚举
     * @return 返回ss回包
     */
    public FetchClanStoreAns fetchClanStore(ClanStoreInterfaceType interfaceType) {
        FetchClanStoreAns.Builder retBuilder = FetchClanStoreAns.newBuilder();
        retBuilder.putAllStoreInfo(getServerClanStoreInfoMap());
        if (interfaceType == ClanStoreInterfaceType.CSRT_BUY) {
            return retBuilder.build();
        } else if (interfaceType == ClanStoreInterfaceType.CSRT_STOCK) {
            retBuilder.setClanScore(getClanScore());
            retBuilder.setTerritoryLv(getOwner().getTerritoryComponent().getTerritoryLevel());
            return retBuilder.build();
        } else {
            LOGGER.error("error interface have been sent");
            return FetchClanStoreAns.getDefaultInstance();
        }
    }

    /**
     * 购买商店内商品，只要这个商品被进货了，哪怕配置消失了，也应可以买到
     *
     * @param operatorId 进行操作的玩家id
     * @param itemId     商品id
     * @param itemNum    商品数目
     * @return 购买结果
     */
    public ClanStoreOperateReturnType buy(long operatorId, int itemId, int itemNum) {
        // 数量不够或已经删除的情况下返回
        ClanStoreItemInfoProp itemInfoProp = getClanStoreItemInfoByItemId(itemId);
        if (itemInfoProp == null || itemInfoProp.getItemNum() < itemNum) {
            return ClanStoreOperateReturnType.CSORT_NO_ENOUGH_ITEM;
        }
        // 数量相等，意味着下架，发送下架的ntf给在线玩家
        if (itemInfoProp.getItemNum() == itemNum) {
            sendStoreItemChangeNtf(itemId, itemNum);
            // 删除商品
            removeClanItemInfoByItemId(itemId);
            if (getClanStoreRemainItemNum() <= 0) {
                // 移除所有商店红点
                getOwner().getRedDotComponent().tryRemoveAllStoreRedDots();
            } else {
                // 移除特定红点
                getOwner().getRedDotComponent().tryRemoveStoreRedDot(itemId);
            }
        } else {
            // 扣除数量，此时库存数量一定是大于玩家购买数量的
            itemInfoProp.setItemNum(itemInfoProp.getItemNum() - itemNum);
        }
        logStoreBuy(operatorId, itemId, itemNum);
        return ClanStoreOperateReturnType.CSORT_SUCCESS;
    }

    /**
     * 从策划配置进货，需要当前商品是否存在
     *
     * @param operatorId 进行操作的玩家id
     * @param itemId     商品id
     * @param itemNum    商品数目
     * @return 进货结果
     */
    public ClanStoreOperateReturnType stock(long operatorId, int itemId, int itemNum) {
        // 配置不存在的情况下返回
        ClanStoreTemplate template = getClanStoreTemplateByItemId(itemId);
        if (null == template) {
            return ClanStoreOperateReturnType.CSORT_NO_ITEM_CONFIG;
        }
        // 积分不足的情况下返回
        int singleStockScore = template.getClanScore();
        long needClanScore = (long) singleStockScore * itemNum;
        if (needClanScore > getClanScore()) {
            return ClanStoreOperateReturnType.CSORT_NO_ENOUGH_SCORE;
        }
        // 势力值等级不足的情况下返回
        int needTerritoryLv = template.getNeedPowerLv();
        if (getOwner().getTerritoryComponent().getTerritoryLevel() < needTerritoryLv) {
            return ClanStoreOperateReturnType.CSORT_NO_ENOUGH_TERRITORY_LV;
        }
        // 扣除积分
        getOwner().getResourcesComponent().decClanScore("guild_restock", needClanScore, 0L);
        // 增加商店内道具
        ClanStoreItemInfoProp prop = getClanStoreItemInfoByItemId(itemId);
        if (null == prop) {
            // 新增道具，意味着新道具上架，发送上架的ntf给在线玩家
            sendStoreItemChangeNtf(itemId, itemNum);
            // 新增红点
            getOwner().getRedDotComponent().tryAddStoreRedDot(itemId);
        }
        addClanStoreItemInfo(itemId, itemNum);
        // 主动同步积分
        sendClanScoreChangeNtf();
        logStoreStock(operatorId, itemId, itemNum);
        // 记录qlog
        getOwner().getClanQlogComponent().qLogClanRestock(operatorId,
                getOwner().getStaffComponent().getPlayerStaffId(operatorId),
                template.getItemtype(),
                String.valueOf(itemId),
                itemNum);
        return ClanStoreOperateReturnType.CSORT_SUCCESS;
    }

    /**
     * 根据面板类型获取军团商店的记录
     *
     * @param interfaceType 面板类型
     * @return 返回对应的军团商店的记录
     */
    public List<ClanStoreLogItem> getClanStoreRecord(ClanStoreInterfaceType interfaceType) {
        if (interfaceType == ClanStoreInterfaceType.CSRT_BUY) {
            List<ClanStoreLogItem> retList = new ArrayList<>();
            for (ClanStoreLogItemProp itemProp : storeLogsMap.get(ClanRecordType.CRT_STORE_BUY).values()) {
                retList.add(itemProp.getCopySsBuilder().build());
            }
            // 给客户端的需要从时间戳大的排到时间戳小的
            Collections.reverse(retList);
            return retList;
        } else if (interfaceType == ClanStoreInterfaceType.CSRT_STOCK) {
            List<ClanStoreLogItem> retList = new ArrayList<>();
            for (ClanStoreLogItemProp itemProp : storeLogsMap.get(ClanRecordType.CRT_STORE_STOCK).values()) {
                retList.add(itemProp.getCopySsBuilder().build());
            }
            // 给客户端的需要从时间戳大的排到时间戳小的
            Collections.reverse(retList);
            return retList;
        } else {
            LOGGER.error("error interface have been sent");
            return null;
        }
    }

    /**
     * @return 快捷获取军团商店中剩余的商品个数，用于校验和下架所有商品时的判定
     */
    public int getClanStoreRemainItemNum() {
        return getStoreModelProp().getStoreInfoSize();
    }

    // ---------------------------------------- private methods ---------------------------------- //

    /**
     * 记录军团商店购买记录
     */
    private void logStoreBuy(long playerId, int itemId, int itemNum) {
        PlayerCardHeadProp memberProp = getOwner().getMemberComponent().getCardHeadPropById(playerId);
        if (null == memberProp) {
            LOGGER.warn("try load player {} not in clan, buy record would be ignore", playerId);
            return;
        }
        ClanRecordType recordType = ClanRecordType.CRT_STORE_BUY;
        ClanRecord record = buildClanRecord(recordType, memberProp.getName(), itemId, itemNum);
        addClanStoreLog(recordType, memberProp.getCopySsBuilder().build(), record, playerId, itemId, itemNum);
    }

    /**
     * 记录军团商店进货记录
     */
    private void logStoreStock(long playerId, int itemId, int itemNum) {
        PlayerCardHeadProp memberProp = getOwner().getMemberComponent().getCardHeadPropById(playerId);
        if (null == memberProp) {
            LOGGER.warn("try load player {} not in clan, stock record would be ignore", playerId);
            return;
        }
        ClanRecordType recordType = ClanRecordType.CRT_STORE_STOCK;
        ClanRecord record = buildClanRecord(ClanRecordType.CRT_STORE_STOCK, memberProp.getName(), itemId, itemNum);
        addClanStoreLog(recordType, memberProp.getCopySsBuilder().build(), record, playerId, itemId, itemNum);
    }

    /**
     * 构建ClanRecord用于记录
     *
     * @param type       记录类型
     * @param playerName 玩家名字
     * @param itemId     道具id
     * @param itemNum    道具数目
     * @return 返回ClanRecord
     */
    private ClanRecord buildClanRecord(ClanRecordType type, String playerName, int itemId, int itemNum) {
        ClanRecord.Builder record = ClanRecord.newBuilder();
        DisplayData.Builder paramBuilder = DisplayData.newBuilder();
        record.setRecordType(type);
        paramBuilder.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(playerName))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_ITEM_ID_FOR_NAME, itemId))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_INT64, itemNum));
        return record.build();
    }

    /**
     * @return 获取配置的军团商店日志容量
     */
    private int getStoreLogConfigCapacity() {
        return ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getAllianceWarehouseLog();
    }

    /**
     * 初始化军团商店日志，将属性系统的map委托给MapPropKVQueue管理
     */
    private void initClanStoreLog() {
        final int capacity = getStoreLogConfigCapacity();
        // 商店购买记录
        storeLogsMap.put(ClanRecordType.CRT_STORE_BUY, new MapPropKVQueue<>(capacity,
                getStoreModelProp().getBuyRecords(),
                Comparator.comparingLong(ClanStoreLogItemProp::getCreateTsMs)));
        // 商店进货记录
        storeLogsMap.put(ClanRecordType.CRT_STORE_STOCK, new MapPropKVQueue<>(capacity,
                getStoreModelProp().getStockRecords(),
                Comparator.comparingLong(ClanStoreLogItemProp::getCreateTsMs)));

    }

    /**
     * 发送商品下架的信息给所有军团在线玩家
     *
     * @param itemId  道具id
     * @param itemNum 道具数量
     */
    private void sendStoreItemChangeNtf(int itemId, int itemNum) {
        OnClanStoreItemChangeNtf.Builder ntfBuilder = OnClanStoreItemChangeNtf.newBuilder();
        ntfBuilder.setItemId(itemId);
        if (itemNum != 0) {
            ntfBuilder.setItemNum(itemNum);
        }
        getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(
                MsgType.PLAYER_ONCLANSTOREITEMCHANGENTF_NTF,
                ntfBuilder.build()
        );
    }

    /**
     * 发送军团积分变更的信息给所有军团在线玩家
     */
    private void sendClanScoreChangeNtf() {
        OnClanStoreStockNtf.Builder ntfBuilder = OnClanStoreStockNtf.newBuilder();
        ntfBuilder.setNewClanScore(getClanScore());
        getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(
                MsgType.PLAYER_ONCLANSTORESTOCKNTF_NTF,
                ntfBuilder.build()
        );
    }


    /**
     * @return 快捷返回ss军团商店信息map
     */
    private Map<Integer, ClanStoreItemInfo> getServerClanStoreInfoMap() {
        Map<Integer, ClanStoreItemInfo> storeItemInfoMap = new HashMap<>();
        for (int itemId : getStoreModelProp().getStoreInfo().keySet()) {
            ClanStoreItemInfoProp itemInfoProp = getStoreModelProp().getStoreInfo().get(itemId);
            storeItemInfoMap.put(itemId, itemInfoProp.getCopySsBuilder().build());
        }
        return storeItemInfoMap;
    }

    /**
     * @param itemId 道具id
     * @return 快捷获取军团商店内的某个道具
     */
    @Nullable
    private ClanStoreItemInfoProp getClanStoreItemInfoByItemId(int itemId) {
        return getStoreModelProp().getStoreInfoV(itemId);
    }

    /**
     * @param itemId 道具id
     * @return 快捷删除军团商店内某个道具的prop
     */
    private void removeClanItemInfoByItemId(int itemId) {
        getStoreModelProp().removeStoreInfoV(itemId);
    }

    /**
     * 增加军团商店道具信息
     *
     * @param itemId  道具id
     * @param itemNum 道具数量
     */
    private void addClanStoreItemInfo(int itemId, int itemNum) {
        ClanStoreTemplate template = getClanStoreTemplateByItemId(itemId);
        if (template == null) {
            LOGGER.error("try add not exist itemId {} to store", itemId);
            return;
        }
        ClanStoreItemInfoProp prop = getClanStoreItemInfoByItemId(itemId);
        if (prop == null) {
            // 新增
            getStoreModelProp().addEmptyStoreInfo(itemId).setItemNum(itemNum);
        } else {
            // 设置
            prop.setItemNum(prop.getItemNum() + itemNum);
        }
    }

    /**
     * @return 快捷获取军团商店prop
     */
    private ClanStoreModelProp getStoreModelProp() {
        return getOwner().getProp().getStoreModel();
    }

    /**
     * @return 快捷获取军团积分
     */
    private long getClanScore() {
        return getOwner().getResourcesComponent().getClanScore();
    }

    /**
     * @param itemId 道具id
     * @return 快捷获取id对应的军团商店配置，如果配置不存在，会返回空
     */
    @Nullable
    private ClanStoreTemplate getClanStoreTemplateByItemId(int itemId) {
        return ResHolder.getInstance().getValueFromMap(ClanStoreTemplate.class, itemId);
    }

    /**
     * 添加军团商店日志
     *
     * @param type           记录类型
     * @param playerCardHead 玩家铭牌
     * @param record         记录参数信息
     * @param playerId       玩家id
     * @param itemId         道具id
     * @param itemNum        道具数目
     */
    private void addClanStoreLog(ClanRecordType type, PlayerCardHead playerCardHead, ClanRecord record,
                                 long playerId, int itemId, int itemNum) {
        MapPropKVQueue<Long, ClanStoreLogItemProp> queue = storeLogsMap.get(type);
        if (queue == null) {
            LOGGER.error("try load type {} not exist, check init or params, store log would be skip", type);
            return;
        }

        // 若队列满，需要缩容到策划配置。策划配置可能突然缩小。
        if (queue.isFull()) {
            queue.shrinkSize(getStoreLogConfigCapacity() - 1);
        }

        // 获取下一个id
        long nextId = 1L;
        // 获取排序队列中头日志的id，因为插入时是逆序的，第一个的id才是最大的
        ClanStoreLogItemProp lastProp = queue.peekLast();
        if (lastProp != null) {
            nextId = lastProp.getId() + 1;
        }

        // 构建prop，并加入到队列中
        ClanStoreLogItemProp prop = queue.offerLast(nextId);
        prop.setId(nextId).setPlayerId(playerId).setItemId(itemId).setItemCount(itemNum).setCreateTsMs(SystemClock.now());
        prop.getCardHead().mergeFromSs(playerCardHead);
        prop.getUsageRecord().mergeFromSs(record);
    }
}

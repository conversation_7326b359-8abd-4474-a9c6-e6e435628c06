package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.chat.ClanChatEntity;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.constant.ChatConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.result.DeleteResult;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ClanChatComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanChatComponent.class);
    private ClanChatEntity chat;

    public ClanChatComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        chat = new ClanChatEntity(ownerActor());
    }

    public void fetchChatMsg(long fromId, long toId, List<Long> shieldList) {
        final ActorMsgEnvelope context = ownerActor().getCurrentEnvelope();
        chat.queryChatMsgs(fromId, toId, shieldList, context);
    }

    /**
     * 由其它actor发起的chatRequest，需要chatRequest回包
     */
    public void sendChatMsg(CommonMsg.ChatMessage msg) {
        final ActorMsgEnvelope context = ownerActor().getCurrentEnvelope();
        chat.chatRequest(msg, context);
    }

    /**
     * 由clanActor直接发起的chatRequest，不需要chatRequest回包
     */
    public void sendChatMsgWithoutContext(CommonMsg.ChatMessage msg) {
        chat.chatRequest(msg, null);
    }

    public void sendLeaveClanMsg(String playerName) {
        if (getOwner().getStageComponent().isStartDissolve()) {
            // 都解散了，就别发消息了
            return;
        }
        CommonMsg.MessageData.Builder messageDataBuilder = CommonMsg.MessageData.newBuilder();
        messageDataBuilder.setTemplateId(ChatConstants.LEAVE_CLAN_MSG)
                .getMsgParamBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(playerName));
        CommonMsg.ChatMessage.Builder messageBuilder = CommonMsg.ChatMessage.newBuilder();
        messageBuilder.setChatTimestamp(SystemClock.now()).setMessageData(messageDataBuilder).setType(CommonEnum.MessageType.MT_SYSTEM);
        sendChatMsgWithoutContext(messageBuilder.build());
    }

    @Override
    public void onDissolution() {
        TcaplusDb.IdFactoryTable.Builder req = TcaplusDb.IdFactoryTable.newBuilder();
        req.setZoneId(0).setKey(this.chat.getIdFactoryKey());
        try {
            DeleteResult ans = ownerActor().callGameDb(new DeleteAsk<>(req));
            if (!DbUtil.isOk(ans.getCode())) {
                LOGGER.error("delete clan chat index fail {} {}", getOwner(), ans.getCode());
                return;
            }
        } catch (Exception e) {
            LOGGER.error("delete clan chat index fail {}", getOwner(), e);
            return;
        }
        LOGGER.info("delete clan chat index end {}", getOwner());
    }
}

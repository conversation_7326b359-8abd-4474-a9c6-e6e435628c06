package com.yorha.cnc.clan.component.territory;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.component.ClanComponent;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.utils.MailUtil;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.TerritoryInfoProp;
import com.yorha.proto.*;
import com.yorha.proto.SsClanTerritory.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanPowerRewardTemplate;
import res.template.ConstClanTerritoryTemplate;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class ClanTerritoryComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanTerritoryComponent.class);

    private final ClanTerritoryItem item;

    public ClanTerritoryComponent(ClanEntity owner) {
        super(owner);
        item = new ClanTerritoryItem(this, this::getTerritoryInfo);
    }

    /**
     * checkin时填写数据  玩家任务只算本服的计数，不算kvk里的
     *
     * @param ansBuilder builder
     */
    public void fillCheckInBuildingInfo(CommonMsg.CheckInBuildingInfo.Builder ansBuilder) {
        Map<Integer, StructClan.ClanBuildingInfo> clanBuildingInfoMap = getTerritoryInfo().getClanBuilding().getCopySsBuilder().getDatasMap();
        if (clanBuildingInfoMap != null) {
            ansBuilder.putAllClanBuilding(clanBuildingInfoMap);
        }
        ansBuilder.setTerritoryLv(getTerritoryLevel());
    }

    /**
     * 玩家任务拉取使用   只算本服的计数，不算kvk里的
     */
    public FetchClanBuildingInfoAns.Builder fetchClanBuildingInfo() {
        FetchClanBuildingInfoAns.Builder ans = FetchClanBuildingInfoAns.newBuilder();
        if (getTerritoryInfo().getClanBuilding().getCopySsBuilder().getDatasMap() != null) {
            ans.putAllClanBuilding(getTerritoryInfo().getClanBuilding().getCopySsBuilder().getDatasMap());
        }
        return ans;
    }

    /**
     * 更新军团的领土建筑信息
     */
    public SsPlayerMisc.ClanBuildingInfoCmd syncTerritoryInfo(SyncTerritoryInfoCmd ask) {
        LOGGER.debug("syncTerritoryPowerInfo ask={}", ask);
        SsPlayerMisc.ClanBuildingInfoCmd.Builder ret = item.onSyncTerritoryInfo(ask);
        return ret.build();
    }

    /**
     * 刷新势力等级
     */
    public void refreshTerritoryPowerLv(long beforePower, int beforeLv) {
        long territoryPower = getTerritoryPower();
        if (beforePower == territoryPower) {
            return;
        }
        // 军团card更新：势力值变更实时同步，中低频
        getOwner().getPropComponent().updateClanCardCache(true);
        int newLv = ResHolder.getResService(ClanDataTemplateService.class).getClanRewardLevel(territoryPower);
        if (beforeLv < newLv) {
            // 势力值等级增加通知所有在线玩家
            SsPlayerClan.OnClanTerritoryLvChangeCmd cmd = SsPlayerClan.OnClanTerritoryLvChangeCmd.newBuilder()
                    .setNewTerritoryLv(newLv).build();
            getOwner().getMemberComponent().batchTellOnlinePlayer(cmd);
        }
        // 势力值变更更新排行榜
        getOwner().getRankComponent().updateClanTerritoryValueZoneRank();
        // 势力等级最大值是记在本服的
        if (getTerritoryInfo().getPowerLevelMax() >= newLv) {
            // 历史最高势力值等级未刷新，直接返回
            return;
        }
        getTerritoryInfo().setPowerLevelMax(newLv);
        sendTerritoryLvRewardMail(newLv);
    }

    /**
     * 更新军团的建筑信息
     */
    public void onSyncClanBuildStatus(SyncClanBuildStatusCmd ask) {
        item.onSyncClanBuildStatus(ask);
    }


    /**
     * 拉取联盟领地页面
     */
    public void getTerritoryPageAll(ActorMsgEnvelope envelope) {
        SsSceneClan.FetchTerritoryPageAsk.Builder ask = SsSceneClan.FetchTerritoryPageAsk.newBuilder();
        SsSceneClan.FetchTerritoryPageAsk msg = ask.setClanId(getEntityId()).build();
        ownerActor().<SsSceneClan.FetchTerritoryPageAns>askSelfBigScene(getOwner().getZoneId(), msg).onComplete(
                (res, t) -> {
                    if (res == null || t != null) {
                        ownerActor().answerWithContext(envelope, FetchTerritoryPageAllAns.getDefaultInstance());
                        return;
                    }
                    onSceneTerritoryPageAnswer(res.getPage(), null, envelope);
                }
        );
    }

    private void onSceneTerritoryPageAnswer(CommonMsg.ClanTerritoryPage p1, CommonMsg.ClanTerritoryPage p2, ActorMsgEnvelope envelope) {
        FetchTerritoryPageAllAns.Builder ret = FetchTerritoryPageAllAns.newBuilder();
        // 需要本服数据
        ret.setPage(p1);
        // 累加k服数据
        if (p2 != null) {
            addTerritoryPageTotal(ret.getPageBuilder(), p2);
        }
        CommonMsg.ClanTerritoryPage.Builder pageBuilder = ret.getPageBuilder();
        // 补充buff
        for (DevBuffProp prop : getOwner().getProp().getDevBuffSys().getDevBuff().values()) {
            if (!DevBuffUtil.TERRITORY_BUFF_SOURCE_TYPE.contains(prop.getSourceType())) {
                continue;
            }
            if (prop.getLayer() == 0) {
                continue;
            }
            pageBuilder.putBuffs(prop.getDevBuffId(), prop.getLayer() + pageBuilder.getBuffsOrDefault(prop.getDevBuffId(), 0));
        }
        ownerActor().answerWithContext(envelope, ret.build());
    }

    private void addTerritoryPageTotal(CommonMsg.ClanTerritoryPage.Builder builder, CommonMsg.ClanTerritoryPage p) {
        builder.setStrongholdNum(builder.getStrongholdNum() + p.getStrongholdNum());
        builder.setCityNum(builder.getCityNum() + p.getCityNum());
        builder.setStrongholdPower(builder.getStrongholdPower() + p.getStrongholdPower());
        builder.setCityPower(builder.getCityPower() + p.getCityPower());
        builder.setClanBuildingPower(builder.getClanBuildingPower() + p.getClanBuildingPower());
        // 军团资源田
        if (p.hasClanResBuldingInfo()) {
            builder.setClanResBuldingInfo(p.getClanResBuldingInfo());
        }
    }

    /**
     * 军团建筑改建检测
     */
    public void checkRebuildClanBuilding(CheckRebuildClanBuildingAsk ask) {
        item.checkRebuildClanBuilding(ask);
    }

    /**
     * ss协议处理入口：放置军团资源中心
     *
     * @param ask ss协议请求
     * @return 返回军团资源中心的唯一id
     */
    public long placeClanResBuild(PlaceClanResBuildAsk ask) {
        return item.placeClanResBuild(ask);
    }


    /**
     * ss协议处理入口：拉取玩家势力值奖励
     */
    public GetClanPowerRewardAns getClanPowerReward(long playerId) {
        ClanMemberProp prop = getOwner().getMemberComponent().getMember(playerId);
        getOwner().getMemberComponent().settlePlayerResources(playerId);
        int rewardId = ResHolder.getResService(ClanDataTemplateService.class).getClanPowerRewardId(getOwner().getTerritoryComponent().getTerritoryPower());
        GetClanPowerRewardAns.Builder ans = GetClanPowerRewardAns.newBuilder().setRewardId(rewardId);
        for (CurrencyProp p : prop.getResources().values()) {
            ans.putResource(p.getType(), (int) p.getCount());
        }
        prop.clearResources();
        return ans.build();
    }

    /**
     * 军团建筑灭火
     */
    public void checkExtinguishClanBuildingAsk(CheckExtinguishClanBuildingAsk ask) {
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        ConstClanTerritoryTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
        List<IntPairType> cost = constTemplate.getExtinguishNeedMoney();
        getOwner().getResourcesComponent().checkAndDecClanResources("extinguish", cost, 0);
        // 记录仓库日志
        Struct.ClanRecord record = getOwner().getWareHouseComponent().buildClanBuildingRecord(
                CommonEnum.ClanRecordType.CRT_WAREHOUSE_EXTINGUISH,
                ask.getCardHead().getName(),
                ask.getBuildingTemplateId());
        getOwner().getWareHouseComponent().recordWareHouseLog(ask.getPlayerId(), ask.getCardHead(), record, cost);
    }

    /**
     * @return 快捷获取军团势力值等级
     */
    public int getTerritoryLevel() {
        long territoryPower = getTerritoryPower();
        return ResHolder.getResService(ClanDataTemplateService.class).getClanRewardLevel(territoryPower);
    }

    /**
     * @return 快捷获取军团势力值
     */
    public long getTerritoryPower() {
        return item.getTerritoryPower();
    }


    /**
     * @return 返回一个不变的地块id集合
     */
    public Collection<Integer> getOwnPartIdCollection() {
        return Collections.unmodifiableCollection(getTerritoryInfo().getOwnPartIdSet().getValues());
    }

    /**
     * 发送达到新的势力值等级的奖励邮件
     *
     * @param territoryLv 势力值等级
     */
    private void sendTerritoryLvRewardMail(int territoryLv) {
        ClanPowerRewardTemplate template = ResHolder.getInstance().getValueFromMap(ClanPowerRewardTemplate.class, territoryLv);
        if (template.getMailId() != 0) {
            // 发个奖励邮件
            StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder();
            mail.setMailTemplateId(template.getMailId());
            MailUtil.sendClanMail(this.getOwner().getZoneId(), this.getEntityId(), mail.build());
        }
    }


    private TerritoryInfoProp getTerritoryInfo() {
        return getOwner().getProp().getTerritoryInfo();
    }
}

package com.yorha.cnc.clan.component.territory;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.resource.resservice.map.BigMapBuildingTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.FormulaUtils;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.game.gen.prop.ClanBuildCostResourceProp;
import com.yorha.game.gen.prop.ClanBuildingInfoProp;
import com.yorha.game.gen.prop.TerritoryInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk;
import com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk;
import com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd;
import com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import com.yorha.proto.StructClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * 场景建筑数据单元
 *
 * <AUTHOR>
 */
public class ClanTerritoryItem {
    private static final Logger LOGGER = LogManager.getLogger(ClanTerritoryComponent.class);
    private final ClanTerritoryComponent owner;
    private final Supplier<TerritoryInfoProp> propSupplier;

    public ClanTerritoryItem(ClanTerritoryComponent owner, Supplier<TerritoryInfoProp> propSupplier) {
        this.owner = owner;
        this.propSupplier = propSupplier;
    }

    private TerritoryInfoProp getProp() {
        return propSupplier.get();
    }

    public long getTerritoryPower() {
        return getProp().getTerritoryPower();
    }

    private ClanEntity getOwner() {
        return owner.getOwner();
    }

    /**
     * 收到领土情况同步
     */
    public SsPlayerMisc.ClanBuildingInfoCmd.Builder onSyncTerritoryInfo(SyncTerritoryInfoCmd ask) {
        SsPlayerMisc.ClanBuildingInfoCmd.Builder ret = null;
        // 更新建筑信息
        if (ask.hasBuildingType() && ask.hasTotalNum() && ask.hasAlreadyBuiltNum()) {
            updateClanBuildingInfo(ask);
            ret = SsPlayerMisc.ClanBuildingInfoCmd.newBuilder();
            int clanBuildingNum = getClanBuildingNum(ask.getBuildingType());
            int clanBuildingBuiltNum = getClanBuildingBuiltNum(ask.getBuildingType());
            ret.putClanBuilding(ask.getBuildingType(),
                    StructClan.ClanBuildingInfo.newBuilder()
                            .setId(ask.getBuildingType())
                            .setNum(clanBuildingNum)
                            .setBuiltNum(clanBuildingBuiltNum)
                            .build());
        }
        // 更新地块数
        if (ask.hasPartNum()) {
            updatePartNum(ask.getPartNum());
            if (ret == null) {
                ret = SsPlayerMisc.ClanBuildingInfoCmd.newBuilder();
            }
            ret.setPartNum(ask.getPartNum());
        }
        // 更新势力值
        if (ask.hasTerritoryPower()) {
            updateTerritoryPower(ask.getTerritoryPower());
        }
        // 更新地块id
        if (ask.hasAddPartOrReduce() && ask.hasPartId()) {
            if (ask.getAddPartOrReduce()) {
                addPartId(ask.getPartId());
            } else {
                removePartId(ask.getPartId());
            }
        }
        return ret;
    }

    /**
     * 更新军团的建筑信息
     */
    public void onSyncClanBuildStatus(SyncClanBuildStatusCmd ask) {
        if (ask.getStatus() == CommonEnum.ClanBuildStatus.CBS_FINISH) {
            // 如果是完成建筑，要发邮件
            sendBuildFinishMail(ask.getId(), ask.getTemplateId(), ask.getPoint());
        }
        clearBuildingCostResource(ask.getId(), ask.getIsClanResBuilding());
    }

    /**
     * 发送建设完成的邮件
     */
    private void sendBuildFinishMail(long entityId, int templateId, Struct.Point point) {
        if (getProp().getBuildCostResourcesV(entityId) == null) {
            LOGGER.error("build finish cannot get record, entityId {}", entityId);
            return;
        }
        // 发送军团建筑修建成功邮件
        int mailId = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class).getFinishBuildMail();
        owner.getOwner().getMsgComponent().sendBuildFinishMail(mailId, templateId, point, getProp().getBuildCostResourcesV(entityId));
    }

    /**
     * 清除建筑消耗的资源记录
     *
     * @param entityId          实体id
     * @param isClanResBuilding 是否是军团资源中心
     */
    private void clearBuildingCostResource(long entityId, boolean isClanResBuilding) {
        if (getProp().getBuildCostResourcesV(entityId) == null) {
            // get确实可能是null的，建筑有可能已经建设完成了，记录已经被清除了。然后再被玩家拆除，所以这里可能是正常的情况。
            LOGGER.info("try clear empty record, entityId {}, isClanResBuilding {}", entityId, isClanResBuilding);
            return;
        }
        getProp().removeBuildCostResourcesV(entityId);
        // 红点
        // TODO(furson): 加个事件解耦
        if (isClanResBuilding) {
            owner.getOwner().getRedDotComponent().tryRemoveResBuildingRedDot();
        } else {
            owner.getOwner().getRedDotComponent().tryRemoveConstructRedDot(entityId);
        }
    }

    public int getClanBuildingNum(int buildingType) {
        ClanBuildingInfoProp prop = getProp().getClanBuilding().get(buildingType);
        if (prop == null) {
            return 0;
        }
        return prop.getNum();
    }

    public int getClanBuildingBuiltNum(int buildingType) {
        ClanBuildingInfoProp prop = getProp().getClanBuilding().get(buildingType);
        if (prop == null) {
            return 0;
        }
        return prop.getBuiltNum();
    }

    /**
     * 更新势力值，如果新势力值对应的势力值最大等级也有变化，发奖励邮件
     *
     * @param territoryPower 势力值
     */
    private void updateTerritoryPower(int territoryPower) {
        long beforePower = owner.getTerritoryPower();
        int beforePowerLevel = ResHolder.getResService(ClanDataTemplateService.class).getClanRewardLevel(beforePower);
        // 更新势力值
        getProp().setTerritoryPower(territoryPower);
        owner.refreshTerritoryPowerLv(beforePower, beforePowerLevel);
    }

    /**
     * 更新军团拥有的地块数量
     *
     * @param partNum 地块数量
     */
    private void updatePartNum(int partNum) {
        getProp().setPartNum(partNum);
    }

    /**
     * 更新军团的建筑信息
     *
     * @param ask 更新请求
     */
    private void updateClanBuildingInfo(SyncTerritoryInfoCmd ask) {
        LOGGER.info("syncTerritoryPowerInfo: set type {} and num {} {}", ask.getBuildingType(), ask.getTotalNum(), ask.getAlreadyBuiltNum());
        // 直接替换对应结构数据
        TerritoryInfoProp territoryInfoProp = propSupplier.get();
        ClanBuildingInfoProp clanBuildingInfoProp = territoryInfoProp.getClanBuilding()
                .computeIfAbsent(ask.getBuildingType(), (id) -> new ClanBuildingInfoProp().setId(id));
        clanBuildingInfoProp.setBuiltNum(ask.getAlreadyBuiltNum());
        clanBuildingInfoProp.setNum(ask.getTotalNum());
    }

    /**
     * 添加地块id
     *
     * @param partId 地块id
     */
    private void addPartId(int partId) {
        if (getProp().getOwnPartIdSet().contains(partId)) {
            LOGGER.warn("ClanBuildingComponent addPartId partId {} already exist", partId);
            return;
        }
        getProp().getOwnPartIdSet().add(partId);
    }

    /**
     * 删除地块id的记录
     *
     * @param partId 地块id
     */
    private void removePartId(int partId) {
        if (getProp().getOwnPartIdSet().contains(partId)) {
            LOGGER.info("ClanBuildingComponent removePartId partId {} not exist", partId);
            return;
        }
        getProp().getOwnPartIdSet().remove(partId);
    }

    /**
     * @return 返回建造军团建筑时是否只需要检查资源
     */
    public static boolean isOnlyCheckResourceWhenBuild() {
        // gm命令未打开情况下一定需要检查军团建筑建设
        if (!ServerContext.getServerDebugOption().isGmSwitch()) {
            return false;
        }
        return ServerContext.getServerDebugOption().isOnlyCheckResourceWhenBuild();
    }

    /**
     * 军团建筑改建检测
     */
    public void checkRebuildClanBuilding(CheckRebuildClanBuildingAsk ask) throws GeminiException {
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        CommonEnum.MapBuildingType rebuildType = ask.getRebuildingType();
        // gm命令设置为只检查资源时，跳过科技检查
        if (!isOnlyCheckResourceWhenBuild()) {
            // 建造科技未解锁
            CommonEnum.BuffEffectType buffEffectType = AdditionConstants.CLAN_BUILDING_BUILD_EFFECT_TYPE_MAP.get(rebuildType);
            if (buffEffectType != null) {
                long addition = getOwner().getAddComponent().getAddition(buffEffectType);
                if (addition <= 0) {
                    throw new GeminiException(ErrorCode.CLAN_BUILDING_BUILD_LOCK);
                }
            }
        }
        int rebuildNum = 1;
        // 获取当前数量
        ClanBuildingInfoProp clanBuildingInfoProp = getProp().getClanBuildingV(ask.getRebuildingType().getNumber());
        if (null != clanBuildingInfoProp) {
            rebuildNum = clanBuildingInfoProp.getNum() + 1;
        }
        // 检查当前数量是否超出该类建筑建造上限
        checkExceedBuildingUpperLimit(rebuildType, rebuildNum);

        // 获取建设配置
        ClanBuildingResourceTemplate template = ResHolder.getResService(BigMapBuildingTemplateService.class)
                .getClanBuildingTemplate(rebuildType, rebuildNum, ask.getStoryId());

        // 主基地扣除资源前额外检查是否满足建设条件。不满足不扣资源
        if (rebuildType == CommonEnum.MapBuildingType.MBT_MAIN_BASE && !isOnlyCheckResourceWhenBuild()) {
            int memberNum = getOwner().getMemberComponent().getMemberNum();
            long combat = getOwner().getProp().getCombat();
            if (memberNum < template.getNeedMemberNums() || combat < template.getNeedCombat()) {
                throw new GeminiException(ErrorCode.CLAN_BUILDING_NO_ENOUGH_MEMBER_OR_COMBAT);
            }
        }

        // 获取加成
        long addition = getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_CLAN_USE_BUILD_CLAN_BUILDING_COST_DEC_PER);
        List<IntPairType> cost = getRebuildClanBuildingCost(template, addition);
        getOwner().getResourcesComponent().checkAndDecClanResources("start_build_guildbuilding", cost, ask.getEntityId());

        // 打出实际消耗的资源，如果真正出错补偿可以追溯
        StringBuilder sb = new StringBuilder();
        for (IntPairType pair : cost) {
            sb.append("type: ").append(pair.getKey()).append(", cost: ").append(pair.getValue()).append("\n");
        }
        LOGGER.info("build cost {}", sb.toString());

        // 记录仓库日志
        Struct.ClanRecord record = getOwner().getWareHouseComponent().buildClanBuildingRecord(
                CommonEnum.ClanRecordType.CRT_WAREHOUSE_BUILD, ask.getCardHead().getName(), template.getBuildingId());
        getOwner().getWareHouseComponent().recordWareHouseLog(ask.getPlayerId(), ask.getCardHead(), record, cost);

        // 将建筑加到建设中
        recordBuildingCostResource(ask.getEntityId(), ask.getPlayerId(), cost, rebuildType, false);
    }

    /**
     * 军团基地、堡垒、指挥中心的修建数量上限检查
     *
     * @param rebuildType 建筑类型
     * @param rebuildNum  建设后的数量
     */
    private void checkExceedBuildingUpperLimit(CommonEnum.MapBuildingType rebuildType, int rebuildNum) throws GeminiException {
        long numLimit = 0;
        // 获取基础数量上限
        for (TerritoryBuildingTemplate template : ResHolder.getInstance().getListFromMap(TerritoryBuildingTemplate.class)) {
            if (template.getType() == rebuildType) {
                numLimit = template.getLimit();
                break;
            }
        }
        // 获取军团科技加成的上限增量
        CommonEnum.BuffEffectType buffEffectType = AdditionConstants.CLAN_BUILDING_UPPER_LIMIT_EFFECT_MAP.get(rebuildType);
        long addition = this.getOwner().getAddComponent().getAddition(buffEffectType);
        if (addition > 0) {
            numLimit += addition;
        }
        if (numLimit < rebuildNum) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_EXCEED_LIMIT_NUM);
        }
    }

    private List<IntPairType> getRebuildClanBuildingCost(ClanBuildingResourceTemplate template, long addition) {
        if (addition == 0L) {
            return template.getResourcesPairList();
        }
        // 根据军团资源消耗加成减少对应资源消耗
        List<IntPairType> afterReduceCost = new ArrayList<>();
        for (IntPairType pair : template.getResourcesPairList()) {
            afterReduceCost.add(IntPairType.makePair(pair.getKey(), (int) FormulaUtils.f3(pair.getValue(), 0, addition, 1)));
        }
        return afterReduceCost;
    }

    /**
     * 军团资源田建设判定
     *
     * @return 返回军团资源中心的唯一id
     */
    public long placeClanResBuild(PlaceClanResBuildAsk ask) {
        // 检查建设条件
        checkCanBuildResBuilding(ask.getType());

        // 先检查资源是否足够
        long addition = getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_CLAN_USE_BUILD_CLAN_BUILDING_COST_DEC_PER);
        List<IntPairType> cost = getRebuildClanResBuildingCost(ask.getType(), addition);
        getOwner().getResourcesComponent().checkClanResources(cost);

        // 资源不够会在上面抛异常，走到这里说明万事俱备，可以准备生成这个建筑了
        long entityId = IdFactory.nextId("clan_res_building");
        getOwner().getResourcesComponent().checkAndDecClanResources("start_build_guildbuilding", cost, entityId);

        // 记录仓库日志
        Struct.ClanRecord record = getOwner().getWareHouseComponent()
                .buildClanResBuildingRecord(CommonEnum.ClanRecordType.CRT_WAREHOUSE_BUILD, ask.getCardHead().getName(), ask.getType());
        getOwner().getWareHouseComponent().recordWareHouseLog(ask.getPlayerId(), ask.getCardHead(), record, cost);

        // 将建筑加到建设中
        recordBuildingCostResource(entityId, ask.getPlayerId(), cost, ask.getType(), true);
        return entityId;
    }

    /**
     * 检查军团建筑资源中心的前置条件
     */
    private void checkCanBuildResBuilding(CommonEnum.MapBuildingType wantBuildType) {
        if (isOnlyCheckResourceWhenBuild()) {
            return;
        }
        ClanResourceBuildingTemplate checkTemplate = null;
        for (MapBuildingTemplate template : ResHolder.getInstance().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getType() == wantBuildType) {
                checkTemplate = ResHolder.getInstance().getValueFromMap(ClanResourceBuildingTemplate.class, template.getId());
                int memberNum = getOwner().getMemberComponent().getMemberNum();
                long territoryPower = getOwner().getTerritoryComponent().getTerritoryPower();
                if (memberNum < checkTemplate.getNeedMemberNums() || territoryPower < checkTemplate.getNeedpower()) {
                    throw new GeminiException(ErrorCode.CLAN_BUILDING_NO_ENOUGH_MEMBER_OR_COMBAT);
                }
                return;
            }
        }
        // 没有找到对应配置
        LOGGER.error("no rebuilding config have been found");
        throw new GeminiException(ErrorCode.CLAN_BUILD_COST_CONFIG_NOT_FOUND);
    }

    private List<IntPairType> getRebuildClanResBuildingCost(CommonEnum.MapBuildingType wantBuildType, long addition) {
        List<IntPairType> cost = null;
        ClanResourceBuildingTemplate checkTemplate = null;
        for (MapBuildingTemplate template : ResHolder.getInstance().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getType() == wantBuildType) {
                checkTemplate = ResHolder.getInstance().getValueFromMap(ClanResourceBuildingTemplate.class, template.getId());
                cost = checkTemplate.getResourcesPairList();
            }
        }
        if (cost == null) {
            LOGGER.error("build Type {} get cost failed", wantBuildType);
            throw new GeminiException(ErrorCode.CLAN_RES_BUILDING_CONFIG_NOT_FOUND);
        }
        if (addition == 0L) {
            return cost;
        }
        // 根据军团资源消耗加成减少对应资源消耗
        List<IntPairType> afterReduceCost = new ArrayList<>();
        for (IntPairType pair : cost) {
            afterReduceCost.add(IntPairType.makePair(pair.getKey(), (int) FormulaUtils.f3(pair.getValue(), 0, addition, 1)));
        }
        return afterReduceCost;
    }

    /**
     * 记录建筑消耗的资源
     *
     * @param entityId          实体id
     * @param playerId          玩家id
     * @param cost              消耗的资源
     * @param isClanResBuilding 是否是军团资源建筑
     */
    private void recordBuildingCostResource(long entityId, long playerId, List<IntPairType> cost, CommonEnum.MapBuildingType type, boolean isClanResBuilding) {
        if (getProp().getBuildCostResourcesV(entityId) != null) {
            LOGGER.warn("{} has already record cost, do nothing", entityId);
            return;
        }
        ClanBuildCostResourceProp buildCostResourceProp = getProp().addEmptyBuildCostResources(entityId);
        // 设置玩家相关信息
        buildCostResourceProp.setPlayerId(playerId).setType(type);
        buildCostResourceProp.getCardHead().mergeFromSs(getOwner().getMemberComponent().getMemberCardHead(playerId));
        // 遍历资源并加入
        for (IntPairType pair : cost) {
            buildCostResourceProp.addEmptyCostResource(pair.getKey()).setCount(pair.getValue());
        }
        // 红点
        // TODO(furson): 加个事件解耦
        if (isClanResBuilding) {
            getOwner().getRedDotComponent().tryAddResBuildingRedDot();
        } else {
            getOwner().getRedDotComponent().tryAddConstructRedDot(entityId);
        }
    }

    public void clearData() {
        getProp().setTerritoryPower(0).setPartNum(0).setPowerLevelMax(0);
        getProp().clearOwnPartIdSet();
        getProp().clearClanBuilding();
        for (ClanBuildCostResourceProp prop : getProp().getBuildCostResources().values()) {
            if (GameLogicConstants.isRebuildBuilding(prop.getType())) {
                owner.getOwner().getRedDotComponent().tryRemoveConstructRedDot(prop.getPrivateKey());
                continue;
            }
            getOwner().getRedDotComponent().tryRemoveResBuildingRedDot();
        }
        getProp().clearBuildCostResources();
    }
}

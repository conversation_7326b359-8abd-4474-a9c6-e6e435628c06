package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.rank.ClanRankEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.rank.RankHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ClanBaseInfoProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.proto.SsRank;
import com.yorha.proto.SsRank.UpdateRankingCmd;
import com.yorha.proto.SsSceneClan;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.RankTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class ClanRankComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanRankComponent.class);
    private ActorTimer powerUpdateTask = null;
    private ActorTimer killScoreUpdateTask = null;
    private final Map<Integer, ClanRankEntity> store = new HashMap<>();

    public ClanRankComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
        initResidentRank(getOwner().isCreate());
    }


    /**
     * 联盟创建后, 创建公会时初始化排行榜
     */
    @Override
    public void onCreate() {
        updateClanZoneRank();
        for (ClanMemberProp member : getOwner().getProp().getMember().values()) {
            addMemberToClanRank(member);
        }
    }

    @Override
    public void onDestroy() {
        for (ClanRankEntity rankEntity : store.values()) {
            rankEntity.destroy();
        }
    }

    /**
     * 初始化军团所有常驻的排行榜
     */
    private void initResidentRank(boolean isCreate) {
        for (RankTemplate template : ResHolder.getInstance().getListFromMap(RankTemplate.class)) {
            if (!template.getIsResident()) {
                continue;
            }
            if (RankConstant.SCOPE_CLAN == template.getScope()) {
                ClanRankEntity entity = new ClanRankEntity(template.getId(), getEntityId(), ownerActor(), isCreate);
                store.put(template.getId(), entity);
                // 需要重置的 初始化下
                if (entity.getTotalSize() == 0 && template.getReset()) {
                    onNewClanRankAdd(entity);
                }
            }
        }
    }

    public void dispatchWeekRefresh() {
        // 每周重置
        Collection<ClanRankEntity> list = store.values();
        for (ClanRankEntity e : list) {
            RankTemplate template = ResHolder.findTemplate(RankTemplate.class, e.getRankId());
            if (template.getReset()) {
                e.reset();
            }
        }
    }

    public ClanRankEntity getRankEntity(int rankId) {
        return store.get(rankId);
    }

    public ClanRankEntity getRankEntityOrNull(int rankId) {
        return store.getOrDefault(rankId, null);
    }

    public Collection<ClanRankEntity> getRankEntityList() {
        return store.values();
    }

    /**
     * 新玩家加入军团时排行榜的处理
     *
     * @param playerProp 玩家信息
     */
    public void onNewPlayerJoinedClan(ClanMemberProp playerProp) {
        // 更新单个玩家在联盟内的排行
        addMemberToClanRank(playerProp);
        // 更新军团在单区的排行
        updateClanZoneRank();
    }

    /**
     * 玩家退出军团时排行榜的处理
     *
     * @param playerId 退出军团的玩家id
     */
    public void onMemberLeaveClan(long playerId) {
        deleteClanRanking(playerId);
        updateClanZoneRank();
    }

    public void onCombatChangeUpdateRank(long memberId, long combat) {
        // 军团个人战力排行榜更新需要即时
        updateClanRanking(RankConstant.CLAN_PLAYER_POWER_RANK, memberId, combat);
        // 军团战力全区排行榜和军团战力在scene上的记录可以打包变更
        if (powerUpdateTask != null) {
            return;
        }
        powerUpdateTask = ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_RANK_COMBAT_CHANGE_UPDATE,
                () -> {
                    updateClanPowerZoneRank();
                    syncSceneCombatChange();
                    powerUpdateTask = null;
                },
                getPowerUpdateInterval(),
                TimeUnit.SECONDS);
    }

    private void syncSceneCombatChange() {
        long combat = getOwner().getProp().getCombat();
        SsSceneClan.SyncSceneClanCmd.Builder cmd = SsSceneClan.SyncSceneClanCmd.newBuilder();
        cmd.getSceneClanBuilder().setPower(combat);
        getOwner().getPropComponent().syncToSceneClan(cmd);
    }

    public void onKillChangeUpdateRank(long memberId, long killScore) {
        // 军团个人击杀排行榜更新需要即时
        updateClanRanking(RankConstant.CLAN_PLAYER_KILL_RANK, memberId, killScore);
        // 军团击杀全区排行榜scene上的记录可以打包变更
        if (killScoreUpdateTask != null) {
            return;
        }
        killScoreUpdateTask = ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_KILL_CHANGE_UPDATE,
                () -> {
                    updateClanKillZoneRank();
                    killScoreUpdateTask = null;
                },
                getKillScoreUpdateInterval(),
                TimeUnit.SECONDS);
    }

    private void updateClanZoneRank() {
        updateClanPowerZoneRank();
        updateClanKillZoneRank();
    }

    private void updateClanPowerZoneRank() {
        this.updateZoneRanking(RankConstant.ZONE_CLAN_POWER_RANK, getOwner().getProp().getCombat());
    }

    private void updateClanKillZoneRank() {
        this.updateZoneRanking(RankConstant.ZONE_CLAN_KILL_RANK, getOwner().getProp().getMiscModel().getKillScore());
    }

    public void updateClanTerritoryValueZoneRank() {
        this.updateZoneRanking(RankConstant.ZONE_CLAN_FLAG_RANK, getOwner().getTerritoryComponent().getTerritoryPower());
    }

    public void updateZoneRanking(int rankId, long score) {
        UpdateRankingCmd.Builder build = UpdateRankingCmd.newBuilder();
        build.setMember(RankHelper.buildMember(getEntityId(), score, ownerActor().getZoneId()));
        UpdateRankingCmd msg = build.setRankId(rankId).build();
        // 本服的
        ownerActor().tellZoneRank(getOwner().getZoneId(), msg);
    }

    /**
     * 玩家加入军团时有些排行榜默认加入
     */
    public void addMemberToClanRank(ClanMemberProp memberProp) {
        // 创建时初始化所有clan相关的排行榜，store中保存的就是所有clan相关的排行榜
        Collection<ClanRankEntity> list = store.values();
        ConstTemplate constTemplate = ResHolder.getConsts(ConstTemplate.class);

        for (ClanRankEntity e : list) {
            if (constTemplate.getSpecialClanRankListId().contains(e.getRankId())) {
                continue;
            }
            // 战力和击杀数值不需要先置0，直接设置为玩家带上来的值即可
            if (e.getRankId() == RankConstant.CLAN_PLAYER_POWER_RANK) {
                e.updateRanking(memberProp.getId(), memberProp.getComboat(), getOwner().getZoneId());
            } else if (e.getRankId() == RankConstant.CLAN_PLAYER_KILL_RANK) {
                e.updateRanking(memberProp.getId(), memberProp.getKillScore(), getOwner().getZoneId());
            } else {
                e.updateRanking(memberProp.getId(), 0, getOwner().getZoneId());
            }
        }
    }

    /**
     * 新的排行榜添加 需要把成员都插入一下
     */
    public void onNewClanRankAdd(ClanRankEntity rankEntity) {
        ConstTemplate constTemplate = ResHolder.getConsts(ConstTemplate.class);
        if (constTemplate.getSpecialClanRankListId().contains(rankEntity.getRankId())) {
            return;
        }
        for (ClanMemberProp member : getOwner().getProp().getMember().values()) {
            // 战力和击杀数值不需要先置0，直接设置为玩家带上来的值即可
            if (rankEntity.getRankId() == RankConstant.CLAN_PLAYER_POWER_RANK) {
                rankEntity.updateRanking(member.getId(), member.getComboat(), getOwner().getZoneId());
            } else if (rankEntity.getRankId() == RankConstant.CLAN_PLAYER_KILL_RANK) {
                rankEntity.updateRanking(member.getId(), member.getKillScore(), getOwner().getZoneId());
            } else {
                rankEntity.updateRanking(member.getId(), 0, getOwner().getZoneId());
            }
        }
    }

    public void updateClanRanking(int rankId, long memberId, long score) {
        updateClanRanking(rankId, false, memberId, score);
    }

    /**
     * 联盟侧数据变更联盟排行榜接口
     *
     * @param rankId   排行榜id
     * @param increase 是否增加
     * @param memberId 更改数值的联盟成员玩家id
     * @param score    更改的分数
     */
    public void updateClanRanking(int rankId, boolean increase, long memberId, long score) {
        ClanRankEntity clanRankEntity = this.getRankEntity(rankId);
        if (null == clanRankEntity) {
            LOGGER.error("clanRankEntity for rankId {} is null", rankId);
            return;
        }
        clanRankEntity.updateRanking(increase, memberId, score, getOwner().getZoneId());
    }

    /**
     * 玩家离开军团时删除排行
     */
    public void deleteClanRanking(long playerId) {
        Collection<ClanRankEntity> list = store.values();
        for (ClanRankEntity clanRankEntity : list) {
            clanRankEntity.deleteRanking(playerId);
        }
    }

    /**
     * 联盟解散
     */
    @Override
    public void onDissolution() {
        // 删除我的所有的排行榜
        Collection<ClanRankEntity> list = store.values();
        for (ClanRankEntity clanRankEntity : list) {
            try {
                clanRankEntity.delete();
            } catch (Exception e) {
                LOGGER.error("deleteAllClanRanking delete rank failed {} ", clanRankEntity.getRankId(), e);
            }
        }
        store.clear();
        // 删除全服排行榜中的我
        final SsRank.DeleteAllRankAboutMeCmd cmd = SsRank.DeleteAllRankAboutMeCmd.newBuilder().setMemberId(getEntityId()).build();
        ownerActor().tellZoneRank(getOwner().getZoneId(), cmd);
    }

    /**
     * 获取军团排行榜的简要信息
     */
    public void getRankSimpleInfo(int rankId, StructMsg.RankInfoDTO.Builder dto) {
        ClanProp prop = getOwner().getProp();
        RankTemplate template = ResHolder.findTemplate(RankTemplate.class, rankId);
        ClanBaseInfoProp baseInfoProp = prop.getBase();
        dto.setRankId(rankId);
        dto.setClanId(prop.getId());
        dto.setClanName(baseInfoProp.getName());
        dto.setClansimpleName(baseInfoProp.getSname());
        if (template.getMember() == RankConstant.MEMBER_CLAN) {
            dto.setFlagSign(baseInfoProp.getFlagSign());
            dto.setFlagColor(baseInfoProp.getFlagColor());
            dto.setFlagShading(baseInfoProp.getFlagShading());
            dto.setTerritoryColor(baseInfoProp.getTerritoryColor());
            dto.setNationFlagId(baseInfoProp.getNationFlagId());
            if (prop.getOwnerId() != 0) {
                ClanMemberProp ownerProp = getOwner().getMemberComponent().getClanOwner();
                dto.setPlayerId(ownerProp.getId());
                dto.getCardHeadBuilder().setName(ownerProp.getCardHead().getName());
            }
        }
        // 某些type需要从clan获取数据
        switch (rankId) {
            case RankConstant.ZONE_CLAN_POWER_RANK:
                dto.setValue(prop.getCombat());
                break;
            case RankConstant.ZONE_CLAN_KILL_RANK:
                dto.setValue(getOwner().getPropComponent().getClanKillScore());
                break;
            case RankConstant.ZONE_CLAN_FLAG_RANK:
                dto.setValue(getOwner().getTerritoryComponent().getTerritoryPower());
                break;
            default:
                LOGGER.debug("value would not set for rankId {}", rankId);
                break;
        }
        LOGGER.debug("value would not set for rankId {}", rankId);
    }


    /**
     * 战力更新时间间隔
     */
    private long getPowerUpdateInterval() {
        return RandomUtils.nextLong(60, 65);
    }

    /**
     * 击杀更新时间间隔
     */
    private long getKillScoreUpdateInterval() {
        return RandomUtils.nextLong(60, 65);
    }
}

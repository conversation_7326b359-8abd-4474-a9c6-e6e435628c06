package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.clan.ClanGiftHelper;
import com.yorha.common.clan.ClanGiftRecordUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.clan.ClanGiftAction;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanGiftResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ClanGiftItemProp;
import com.yorha.game.gen.prop.ClanGiftModelProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanGiftLvTemplate;
import res.template.ClanTreasureTemplate;
import res.template.ConstTemplate;
import res.template.ItemTemplate;

import java.util.stream.Collectors;

/**
 * 联盟礼物
 */
public class ClanGiftComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanGiftComponent.class);
    private ClanGiftHelper clanGiftHelper;

    public ClanGiftComponent(ClanEntity owner) {
        super(owner);

    }

    @Override
    public void init() {
        ClanGiftModelProp giftModel = getProp();
        // 初始礼物等级1级
        if (giftModel.getGiftLevel() <= 0) {
            giftModel.setGiftLevel(1);
            ClanGiftLvTemplate lvTemplate = ResHolder.getInstance().getValueFromMap(ClanGiftLvTemplate.class, 1);
            giftModel.setCurTreasureTemplateId(lvTemplate.getTreasureId());
        }
        clanGiftHelper = new ClanGiftHelper(giftModel.getClanGifts());
    }

    public ClanGiftModelProp getProp() {
        return getOwner().getProp().getGiftModel();
    }

    /**
     * 获取联盟当前礼物等级
     *
     * @return 当前礼物等级
     */
    public int getGiftLevel() {
        return getProp().getGiftLevel();
    }

    /**
     * 获取联盟当前礼物等级点数
     *
     * @return 当前礼物等级点数
     */
    public int getGiftPoints() {
        return getProp().getCurGiftPoints();
    }

    /**
     * 获取联盟当前珍藏礼物钥匙数
     *
     * @return 当前珍藏礼物钥匙数
     */
    public int getGiftKeys() {
        return getProp().getCurKeyNum();
    }

    /**
     * 获取联盟当前珍藏礼物配表id
     *
     * @return 当前珍藏礼物配表id
     */
    public int getCurTreasureTemplateId() {
        return getProp().getCurTreasureTemplateId();
    }


    /**
     * checkin时填写数据
     *
     * @param playerCurGiftIndex 玩家当前军团礼物index
     * @param ansBuilder         builder
     */
    public void fillCheckInGiftInfo(long playerCurGiftIndex, CommonMsg.CheckInGiftInfo.Builder ansBuilder) {
        ansBuilder.setClanGiftInfo(this.formClanGiftSharedInfo());
        clanGiftHelper.removeNeedDeleteClanGifts();
        for (int i = 0; i < this.getProp().getClanGiftsSize(); i++) {
            ClanGiftItemProp clanGiftItemProp = this.getProp().getClanGiftsIndex(i);
            // 剪枝优化
            if (clanGiftItemProp.getGiftUniqueId() >= playerCurGiftIndex) {
                ansBuilder.addAllClanGifts(this.getProp().getClanGifts().subList(i,
                        this.getProp().getClanGiftsSize()).stream().map(clanGift -> clanGift.getCopySsBuilder().build()).collect(Collectors.toList()));
                break;
            }
        }
        ansBuilder.setCurClanGiftIndex(this.getCurGiftIndex());
    }

    /**
     * 为所有军团内玩家添加军团礼物
     *
     * @param giftId           礼物id
     * @param originGiftRecord 礼物来源记录
     */
    public void addGift(int giftId, final Struct.ClanGiftRecord originGiftRecord) {
        //兜底： giftId 与 giftLevel是否存在对应配置
        int giftTemplateId = ResHolder.getResService(ClanGiftResService.class).getClanGiftTemplate(this.getGiftLevel(), giftId).getId();
        final int nowSeconds = (int) SystemClock.nowOfSeconds();
        // 设置屏蔽则不下发理由
        final Struct.ClanGiftRecord giftRecord = originGiftRecord.getIsHidden()
                ? ClanGiftRecordUtils.formHiddenBuyGoodsRecord(ClanGiftRecordUtils.getGoodsId(originGiftRecord)) : originGiftRecord;
        StructClan.ClanGiftItem clanGiftItem = StructClan.ClanGiftItem.newBuilder()
                .setGiftUniqueId(this.allocateGiftIndex())
                .setGiftId(giftTemplateId)
                .setGiftLevel(this.getGiftLevel())
                .setStatus(CommonEnum.ClanGiftStatus.CGS_CAN_TAKE)
                .setCreateTsSec(nowSeconds)
                .setGiftRecord(giftRecord)
                .build();
        LOGGER.debug("ClanGiftComponent addGift gift={}", clanGiftItem);
        ClanGiftItemProp clanGiftItemProp = new ClanGiftItemProp();
        clanGiftItemProp.mergeFromSs(clanGiftItem);
        clanGiftHelper.addClanGift(clanGiftItemProp);

        SsPlayerMisc.AddClanGiftForPlayerCmd tell = SsPlayerMisc.AddClanGiftForPlayerCmd.newBuilder()
                .setGift(clanGiftItem)
                .build();
        // 广播所有在线联盟玩家，离线登录再拉取
        this.getOwner().getMemberComponent().batchTellOnlinePlayer(tell);

        // 设置屏蔽不影响qlog
        this.sendAddClanGiftQlog(originGiftRecord);

        // 黄金宝箱则发送推送
        if (giftId == ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getClanGoldGiftId()) {
            getOwner().getNotificationComponent().pushGiveGoldBoxNotification();
        }
    }

    /**
     * 获取军团礼物道具对应的礼物id
     *
     * @param itemId 道具id
     * @return 礼物id
     */
    public int getGiftId(int itemId) {
        ItemTemplate itemTemplate = ResHolder.getInstance().getValueFromMap(ItemTemplate.class, itemId);
        if (CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType()) != CommonEnum.ItemUseType.CLAN_GIFT) {
            throw new GeminiException(ErrorCode.CLAN_GIFT_ITEM_TYPE_ERROR);
        }
        int giftId = itemTemplate.getEffectId();
        if (!ResHolder.getResService(ClanGiftResService.class).isValidGiftId(giftId)) {
            throw new GeminiException(ErrorCode.CLAN_GIFT_INVALID_GIFT_ID);
        }
        return giftId;
    }

    /**
     * 分配一个可用军团礼物index
     *
     * @return 可用军团礼物index
     */
    private long allocateGiftIndex() {
        long result = this.getCurGiftIndex();
        this.getProp().setGiftIndex(result + 1);
        return result;
    }

    /**
     * 获取当前可用且未分配的军团礼物index
     *
     * @return 可用且未分配的军团礼物index
     */
    public long getCurGiftIndex() {
        return this.getProp().getGiftIndex();
    }

    /**
     * 分配一个可用珍藏礼物index
     *
     * @return 可用珍藏礼物index
     */
    private long allocateNewTreasureGiftIndex() {
        long result = this.getCurTreasureGiftIndex();
        this.getProp().setTreasureGiftIndex(result + 1);
        return result;
    }

    /**
     * 获取当前可用且未分配的珍藏礼物index
     *
     * @return 可用且未分配的珍藏礼物index
     */
    private long getCurTreasureGiftIndex() {
        return this.getProp().getTreasureGiftIndex();
    }

    /**
     * 添加军团礼物等级点数与珍藏礼物钥匙
     *
     * @param addPoint 添加的等级点数
     * @param addKey   添加的珍藏礼物钥匙
     */
    public void addGiftPointAndKey(int addPoint, int addKey) {
        ClanGiftModelProp giftModel = getProp();
        long originTreasureGiftIndex = this.getCurTreasureGiftIndex();
        int leftPoint = addPoint;
        while (leftPoint > 0) {
            leftPoint = tryAddPoint(leftPoint, giftModel);

        }

        int leftKey = addKey;
        while (leftKey > 0) {
            leftKey = tryAddKey(leftKey, giftModel);
        }

        // 添加钥匙时触发过发送珍藏礼物
        if (originTreasureGiftIndex != this.getCurTreasureGiftIndex()) {
            // 广播在线玩家钥匙与礼物数据更新
            PlayerClan.Player_ClanGiftInfo_NTF.Builder ntf = PlayerClan.Player_ClanGiftInfo_NTF.newBuilder();
            ntf.setClanGiftInfo(this.formClanGiftSharedInfo());
            this.getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(MsgType.PLAYER_NOTIFYCLANGIFTINFOONLINE_NTF, ntf.build());
        }
    }

    /**
     * 加礼物点数，返回剩余点数（点数满足升级条件则升级）
     *
     * @param addPoint  加的点数
     * @param giftModel prop数据
     * @return 当前点数
     */
    private int tryAddPoint(int addPoint, ClanGiftModelProp giftModel) {
        // 加礼物点数，尝试升一级，返回升一级后剩余点数
        final int curLevel = this.getGiftLevel();
        if (curLevel < ResHolder.getResService(ClanGiftResService.class).getMaxGiftLevel()) {
            ClanGiftLvTemplate lvTemplate = ResHolder.getInstance().getValueFromMap(ClanGiftLvTemplate.class, curLevel + 1);
            final int lvUpNeedPoint = lvTemplate.getUpgradePoint();
            final int curPoints = giftModel.getCurGiftPoints();
            if (curPoints + addPoint >= lvUpNeedPoint) {
                giftModel.setCurGiftPoints(0);
                giftModel.setGiftLevel(curLevel + 1);
                LOGGER.info("clan giftLevel up: {} ->{}", getOwner(), giftModel.getGiftLevel());
                getOwner().getLogComponent().logGiftLevelUp(giftModel.getGiftLevel());
                // 设定下一个宝箱
                ClanGiftLvTemplate giftLvTemplate = ResHolder.getInstance().getValueFromMap(ClanGiftLvTemplate.class, giftModel.getGiftLevel());
                giftModel.setCurTreasureTemplateId(giftLvTemplate.getTreasureId());
                // 军团card更新：礼物等级变化，中低频
                getOwner().getPropComponent().updateClanCardCache(true);
                return curPoints + addPoint - lvUpNeedPoint;
            } else {
                giftModel.setCurGiftPoints(curPoints + addPoint);
            }
        } else {
            // 已经达到最高礼物等级
            LOGGER.debug("clan gift add point overflow. {} {}", getOwner(), addPoint);
        }
        return 0;
    }

    /**
     * 加钥匙，返回剩余钥匙（点数满足宝箱条件则发放宝箱）
     *
     * @param addKey    加的钥匙
     * @param giftModel prop数据
     * @return 当前点数
     */
    private int tryAddKey(int addKey, ClanGiftModelProp giftModel) {
        // 加钥匙，尝试开一个珍藏宝箱，开一个之后返回剩余钥匙数
        final int curTreasureTemplateId = this.getCurTreasureTemplateId();
        ClanTreasureTemplate treasureTemplate = ResHolder.getInstance().getValueFromMap(ClanTreasureTemplate.class, curTreasureTemplateId);
        int sumKey = giftModel.getCurKeyNum() + addKey;
        if (sumKey >= treasureTemplate.getKey()) {
            // 先清空钥匙
            giftModel.setCurKeyNum(0);
            // 发送珍藏军团礼物
            this.addTreasureGift(curTreasureTemplateId);
            return sumKey - treasureTemplate.getKey();

        } else {
            giftModel.setCurKeyNum(sumKey);
        }
        return 0;
    }

    /**
     * 发送珍藏军团礼物
     *
     * @param curTreasureTemplateId 军团礼物配表id
     */
    private void addTreasureGift(int curTreasureTemplateId) {
        final long uniqueId = this.allocateNewTreasureGiftIndex();
        LOGGER.debug("clan {} add treasureGift templateId={} uniqueId={}", getOwner(), curTreasureTemplateId, uniqueId);

        SsPlayerMisc.AddClanGiftForPlayerCmd tell = SsPlayerMisc.AddClanGiftForPlayerCmd.newBuilder()
                .setTreasureGift(
                        StructClan.ClanTreasureGiftItem.newBuilder()
                                .setTemplateId(curTreasureTemplateId)
                                .setTreasureGiftUniqueId(uniqueId)
                ).build();
        // 广播所有联盟玩家，通过离线rpc处理
        for (ClanMemberProp member : getOwner().getProp().getMember().values()) {
            long playerId = member.getId();
            ownerActor().tellPlayer(playerId, tell);
        }

    }

    /**
     * 生成军团礼物全军团成员共享信息
     *
     * @return 军团礼物共享信息
     */
    public StructMsg.ClanGiftSharedInfo formClanGiftSharedInfo() {
        return StructMsg.ClanGiftSharedInfo.newBuilder()
                .setGiftLevel(this.getGiftLevel())
                .setCurGiftPoints(this.getGiftPoints())
                .setCurKeyNum(this.getGiftKeys())
                .setCurTreasureTemplateId(this.getCurTreasureTemplateId())
                .build();
    }

    /**
     * 发送添加军团礼物的Qlog
     *
     * @param giftRecord 礼物来源记录
     */
    private void sendAddClanGiftQlog(final Struct.ClanGiftRecord giftRecord) {
        switch (giftRecord.getType()) {
            case CGRT_MEMBER_KILL_MONSTER:
            case CGRT_TOGETHER_KILL_MONSTER:
                long monsterId = ClanGiftRecordUtils.getMonsterId(giftRecord);
                this.getOwner().getClanQlogComponent().qlogClanGift(ClanGiftAction.KILL_MONSTER, giftRecord.getPlayerId(), monsterId);
                break;
            case CGRT_BUY_GOODS:
                long goodsId = ClanGiftRecordUtils.getGoodsId(giftRecord);
                this.getOwner().getClanQlogComponent().qlogClanGift(ClanGiftAction.BUY_BUNDLE, giftRecord.getPlayerId(), goodsId);
                break;
            default:
                LOGGER.error("ClanGiftComponent sendAddClanGiftQlog unimplemented ClanGiftRecordType={}", giftRecord.getType());
        }
    }

}

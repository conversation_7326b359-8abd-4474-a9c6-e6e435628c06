package com.yorha.cnc.clan.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.clan.ClanTechResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.ClanPB;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructPB;
import org.apache.commons.collections4.IterableUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncGuildTechUpgrade;
import res.template.ClanTechSubTemplate;
import res.template.ConstClanTechTemplate;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.proto.CommonEnum.*;
import static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg;
import static com.yorha.proto.SsClanTech.*;

/**
 * 军团科技
 */
public class ClanTechComponent extends ClanComponent implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(ClanTechComponent.class);

    /**
     * 军团科技读多写少，科技的addition算好放在这里
     */
    private final Map<Integer, Integer> additionMap = Maps.newHashMap();

    /**
     * 研发中的科技subId -> 定时器任务
     */
    private final Map<Integer, ActorTimer> timerMap = Maps.newHashMapWithExpectedSize(1);

    public ClanTechComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad() {
        ClanTechModelProp prop = prop();
        ArrayList<ClanTechResearchInfoProp> researchingList = Lists.newArrayList(prop.getResearchingTech().values());
        // 刷一下研发中现在已经完成的科技
        for (ClanTechResearchInfoProp researchingProp : researchingList) {
            int startTsSec = researchingProp.getStartTsSec();
            ClanTechSubTemplate subTemplate = ResHolder.getTemplate(ClanTechSubTemplate.class, researchingProp.getTechSubId());
            int costSec = subTemplate.getCostSec();
            int finishSec = startTsSec + costSec;
            long nowSec = SystemClock.nowOfSeconds();
            if (nowSec < finishSec) {
                LOGGER.info("{} onLoad schedule tech finish job: {}", getOwner(), researchingProp.getTechSubId());
                scheduleResearchFinishTimer(researchingProp, subTemplate);
            } else {
                LOGGER.info("{} onLoad tryFinishTecResearch {}", getOwner(), researchingProp.getTechSubId());
                tryFinishResearch(researchingProp.getTechSubId(), true);
            }
        }
        // 刷一下additionMap
        refreshAdditionMap();
    }

    public void refreshAdditionMap() {
        additionMap.clear();
        for (ClanTechInfoProp techProp : prop().getFinishedTech().values()) {
            int techSubId = techProp.getTechSubId();
            if (techSubId > 0) {
                ClanTechSubTemplate subTemplate = ResHolder.getTemplate(ClanTechSubTemplate.class, techSubId);
                for (IntPairType buffPair : subTemplate.getBuffPairList()) {
                    additionMap.merge(buffPair.getKey(), buffPair.getValue(), Integer::sum);
                }
            }
        }
        LOGGER.info("{} refresh tech addition: {}", getOwner(), additionMap);
    }

    public ClanTechModelProp prop() {
        return getOwner().getProp().getTechModel();
    }

    public ClanTechFetchInfoAns handleFetch(ClanTechFetchInfoAsk ask) {
        ClanMemberHistoryProp memberHistory = getOwner().getProp().getClanMemberHistoryModel().getMembersV(ask.getPlayerId());
        long contributionDaily = memberHistory == null ? 0 : memberHistory.getContribution().getContributionDaily();
        return ClanTechFetchInfoAns.newBuilder()
                .setTechModel(prop().getCopySsBuilder())
                .setContributionDaily(contributionDaily)
                .build();
    }

    public ClanTechDonateCheckAns handleDonateCheck(int clanTechId) {
        final ClanTechModelProp prop = prop();
        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        List<ClanTechSubTemplate> subTechs = resService.getSubTechs(clanTechId);

        // 研发中不可以捐献哦
        HashSet<Integer> researchingSubIds = Sets.newHashSet(prop.getResearchingTech().keySet());
        if (!researchingSubIds.isEmpty() && subTechs.stream().anyMatch(t -> researchingSubIds.contains(t.getId()))) {
            throw new GeminiException(ErrorCode.CLAN_TECH_RESEARCHING_CANNOT_DONATE);
        }
        ClanTechInfoProp techProp = prop.getFinishedTech().get(clanTechId);
        // 找一下当前要捐献的是哪个subId
        ClanTechSubTemplate nextSubTech = findNextSubTech(techProp, subTechs);
        if (nextSubTech == null) {
            throw new GeminiException(ErrorCode.CLAN_TECH_MAX_LEVEL);
        }
        int needPoint = getResearchPoint(techProp, nextSubTech, clanTechId);
        if (techProp != null && techProp.getPoint() >= needPoint) {
            throw new GeminiException(ErrorCode.CLAN_TECH_POINT_ENOUGH);
        }
        // 前置条件不满足的话，不可以研究也不可以捐献的
        checkPreCondition(nextSubTech);

        ClanTechDonateCheckAns.Builder ans = ClanTechDonateCheckAns.newBuilder();
        ans.setClanTechSubId(nextSubTech.getId());
        return ans.build();
    }

    private int getResearchPoint(ClanTechInfoProp techProp, ClanTechSubTemplate nextSubTech, int clanTechId) {
        int needPoint = nextSubTech.getCostPoint();
        return needPoint;
    }

    @Nullable
    private static ClanTechSubTemplate findNextSubTech(@Nullable ClanTechInfoProp techProp, List<ClanTechSubTemplate> subTechs) {
        if (techProp == null || techProp.getTechSubId() == 0) {
            return subTechs.get(0);
        } else {
            ClanTechSubTemplate curSubTech = IterableUtils.find(subTechs, it -> it.getId() == techProp.getTechSubId());
            if (curSubTech == null) {
                throw new GeminiException("clanSubTech template not exist!{}", techProp.getTechSubId());
            }
            int curSubTechLevel = curSubTech.getTechLevel();
            return IterableUtils.find(subTechs, it -> it.getTechLevel() == curSubTechLevel + 1);
        }
    }

    private void checkPreCondition(ClanTechSubTemplate nextSubTech) {
        List<Integer> frontTechSubIds = nextSubTech.getFrontTechList();
        ClanTechModelProp prop = prop();
        for (Integer frontTechSubId : frontTechSubIds) {
            ClanTechSubTemplate frontTechSub = ResHolder.getTemplate(ClanTechSubTemplate.class, frontTechSubId);
            int frontTechId = frontTechSub.getTechId();
            int frontTechLv = frontTechSub.getTechLevel();
            ClanTechInfoProp frontTechProp = prop.getFinishedTechV(frontTechId);
            if (frontTechProp == null || frontTechProp.getTechSubId() <= 0) {
                throw new GeminiException(ErrorCode.CLAN_TECH_PRE_TECH_NOT_FINISH);
            } else {
                int frontTechCurSubId = frontTechProp.getTechSubId();
                ClanTechSubTemplate frontTechCurSub = ResHolder.getTemplate(ClanTechSubTemplate.class, frontTechCurSubId);
                if (frontTechCurSub.getTechLevel() < frontTechLv) {
                    throw new GeminiException(ErrorCode.CLAN_TECH_PRE_TECH_NOT_FINISH);
                }
            }
        }
    }

    public ClanTechDonateAns handleDonate(ClanTechDonateAsk ask) {
        int clanTechId = ask.getClanTechId();
        int magnification = ask.getMagnification();
        ClanTechModelProp prop = prop();
        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        List<ClanTechSubTemplate> subTechs = resService.getSubTechs(clanTechId);
        ClanTechDonateAns.Builder ans = ClanTechDonateAns.newBuilder();

        // 其实这里可能状态全都变了，可能已经在研发中了，也可能已经不是那个subId了，但是不重要了，player那边该扣的已经扣了，直接给吧
        ClanTechInfoProp techProp = prop.getFinishedTech().get(clanTechId);
        if (techProp == null) {
            techProp = prop.addEmptyFinishedTech(clanTechId);
        }
        ClanTechSubTemplate nextSubTech = findNextSubTech(techProp, subTechs);
        if (nextSubTech == null) {
            WechatLog.error("clanTech boom. what now? {} {}", techProp, ask);
            ans.setAfterPoint(techProp.getPoint());
            return ans.build();
        }
        int clanTechCostPoint = getResearchPoint(techProp, nextSubTech, clanTechId);
        // 科技点
        techProp.setPoint(Math.min(clanTechCostPoint, techProp.getPoint() + (nextSubTech.getTechPoint() * magnification)));
        // 联盟积分
        getOwner().getResourcesComponent().addClanScore("guild_tech_donate", (long) nextSubTech.getClanPoint() * magnification, clanTechId);
        // 个人积分在player上加
        // 个人贡献
        getOwner().getContributionComponent().addContribution(ask.getPlayerId(), nextSubTech.getContributionPoint() * magnification);

        // 给客户端同步变更
        if (techProp.getPoint() >= clanTechCostPoint) {
            // 点数变化只在到达可升级的点的时候同步
            notifyUpdate(ClanTechInfoUpdateMsg.newBuilder()
                    .setModData(ClanPB.ClanTechModelPB.newBuilder()
                            .setFinishedTech(StructPB.Int32ClanTechInfoMapPB.newBuilder()
                                    .putDatas(techProp.getTechId(), techProp.getCopyCsBuilder().build())
                                    .build())
                            .setRecommendTechId(prop.getRecommendTechId())
                            .build())
                    .build()
            );
        }
        ans.setAfterPoint(techProp.getPoint());
        return ans.build();
    }

    public void gmSetPoint(int techId, int count) {
        ClanTechInfoProp techProp = prop().getFinishedTech().get(techId);
        if (techProp == null) {
            techProp = prop().addEmptyFinishedTech(techId);
        }
        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        List<ClanTechSubTemplate> subTechs = resService.getSubTechs(techId);
        ClanTechSubTemplate nextSubTech = findNextSubTech(techProp, subTechs);
        if (nextSubTech != null) {
            int clanTechCostPoint = getResearchPoint(techProp, nextSubTech, techId);
            // 科技点
            techProp.setPoint(Math.min(clanTechCostPoint, count));
        }
    }

    public void handleRecommend(ClanTechRecommendAsk ask) {
        final int recommendTechId = ask.getClanTechId();
        final long playerId = ask.getPlayerId();

        // 设置推荐
        int recommendBefore = prop().getRecommendTechId();
        prop().setRecommendTechId(recommendTechId);
        LOGGER.info("{} setClanTechRecommend {}->{}", playerId, recommendBefore, recommendTechId);
        // 给客户端同步变更
        notifyUpdate(ClanTechInfoUpdateMsg.newBuilder()
                .setModData(ClanPB.ClanTechModelPB.newBuilder()
                        .setRecommendTechId(recommendTechId)
                        .build()).build());
    }

    public void handleResearch(ClanTechResearchAsk ask) {
        final int clanTechId = ask.getClanTechId();
        final long playerId = ask.getPlayerId();

        final ClanTechModelProp prop = prop();
        ClanTechResService resService = ResHolder.getResService(ClanTechResService.class);
        List<ClanTechSubTemplate> subTechs = resService.getSubTechs(clanTechId);
        // 已经在研发中
        if (!prop.getResearchingTech().isEmpty()) {
            throw new GeminiException(ErrorCode.CLAN_TECH_RESEARCHING);
        }
        ClanTechInfoProp techProp = prop.getFinishedTech().get(clanTechId);
        // 找一下当前要捐献的是哪个subId
        ClanTechSubTemplate nextSubTech = findNextSubTech(techProp, subTechs);
        if (nextSubTech == null) {
            throw new GeminiException(ErrorCode.CLAN_TECH_MAX_LEVEL);
        }
        int needPoint = nextSubTech.getCostPoint();
        if (techProp == null || techProp.getPoint() < needPoint) {
            throw new GeminiException(ErrorCode.CLAN_TECH_POINT_NOT_ENOUGH);
        }
        // 前置条件不满足的话，不可以研究也不可以捐献的
        checkPreCondition(nextSubTech);

        // 权限
        ClanPermissionUtils.checkPermission(ClanOperationType.COT_TECH_RESEARCH, getOwner().getStaffComponent().getPlayerStaffId(playerId));

        // 联盟资源
        List<IntPairType> cost = nextSubTech.getCostResPairList();
        getOwner().getResourcesComponent().checkAndDecClanResources("start_guild_tech", cost, clanTechId);

        // 记录仓库日志
        ClanMemberProp operator = getOwner().getMemberComponent().getMember(playerId);
        Struct.ClanRecord record = getOwner().getWareHouseComponent()
                .buildTechDonateRecord(operator.getCardHead().getName(), nextSubTech.getId());
        getOwner().getWareHouseComponent().recordWareHouseLog(playerId, operator.getCardHead().getCopySsBuilder().build(), record, cost);

        int nextSubTechId = nextSubTech.getId();
        LOGGER.info("clanTech begin research. {} tech={} operator={}", getOwner(), nextSubTechId, playerId);
        techProp.setPoint(0);

        trySendQlog("tech_start", clanTechId, playerId, nextSubTechId);

        ClanTechResearchInfoProp rp = prop.addEmptyResearchingTech(nextSubTechId)
                .setStartTsSec((int) SystemClock.nowOfSeconds());
        scheduleResearchFinishTimer(rp, nextSubTech);

        // 给客户端同步变更
        notifyUpdate(ClanTechInfoUpdateMsg.newBuilder()
                .setModData(ClanPB.ClanTechModelPB.newBuilder()
                        .setResearchingTech(StructPB.Int32ClanTechResearchInfoMapPB.newBuilder()
                                .putDatas(nextSubTechId, rp.getCopyCsBuilder().build())
                                .build())
                        .setFinishedTech(StructPB.Int32ClanTechInfoMapPB.newBuilder()
                                .putDatas(techProp.getTechId(), techProp.getCopyCsBuilder().build())
                                .build())
                        .setRecommendTechId(prop.getRecommendTechId())
                        .build())
                .build()
        );
    }

    private void trySendQlog(String action, int clanTechId, long playerId, int nextSubTechId) {
        try {
            QlogCncGuildTechUpgrade.init(getOwner().getClanQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setOptionRoleId(String.valueOf(playerId))
                    .setAction(action)
                    .setGuildTechID(clanTechId)
                    .setGuildSubTechID(nextSubTechId)
                    .sendToQlog();
        } catch (Exception e) {
            LOGGER.error("sendClanTechUpgradeQlog error:", e);
        }
    }

    private void scheduleResearchFinishTimer(ClanTechResearchInfoProp rp, ClanTechSubTemplate subTemplate) {
        int costSec = subTemplate.getCostSec();
        int finishSec = rp.getStartTsSec() + costSec;
        long nowSec = SystemClock.nowOfSeconds();
        ActorTimer timer = ownerActor().addTimer(
                getOwner().getEntityId() + "-" + subTemplate.getId(),
                TimerReasonType.CLAN_TECH_RESEARCH_FINISH,
                () -> this.tryFinishResearch(subTemplate.getId(), false),
                finishSec - nowSec,
                TimeUnit.SECONDS
        );
        if (timer == null) {
            return;
        }
        timerMap.put(subTemplate.getId(), timer);
    }

    public void gmFinishResearch() {
        List<Integer> researchingTechSubIds = Lists.newArrayList(prop().getResearchingTech().keySet());
        for (Integer techSubId : researchingTechSubIds) {
            tryFinishResearch(techSubId, false);
        }
    }

    private void tryFinishResearch(int techSubId, boolean onLoad) {
        ActorTimer timer = timerMap.remove(techSubId);
        if (timer != null) {
            timer.cancel();
        }
        ClanTechModelProp prop = prop();
        ClanTechResearchInfoProp researchingProp = prop.getResearchingTechV(techSubId);
        ClanTechSubTemplate subTemplate = ResHolder.getTemplate(ClanTechSubTemplate.class, techSubId);
        if (subTemplate == null) {
            WechatLog.error("clanTechSubTemplate not found!! {} {} ", techSubId, getOwner());
            return;
        }
        if (researchingProp == null) {
            WechatLog.error("tryFinishResearch prop not exist! {}", techSubId);
        } else {
            ClanTechInfoProp techProp = prop.getFinishedTechV(subTemplate.getTechId());
            if (techProp == null) {
                WechatLog.error("tech finish but prop not exist! {} {}", getOwner(), techSubId);
                return;
            }
            int techSubIdBefore = techProp.getTechSubId();
            LOGGER.info("{} techFinishResearch {}:{}->{}", getOwner(), subTemplate.getTechId(), techSubIdBefore, techSubId);
            prop.removeResearchingTechV(techSubId);

            techProp.setTechSubId(techSubId);

            // 科技效果刷新
            // 先加成
            refreshAdditionMap();
            Set<Integer> affectedAdditionIds = Sets.newHashSet();
            if (techSubIdBefore > 0) {
                ClanTechSubTemplate techSubBefore = ResHolder.getTemplate(ClanTechSubTemplate.class, techSubIdBefore);
                techSubBefore.getBuffPairList().forEach(pair -> affectedAdditionIds.add(pair.getKey()));
            }
            subTemplate.getBuffPairList().forEach(pair -> affectedAdditionIds.add(pair.getKey()));
            getOwner().getAddComponent().updateAddition(AdditionProviderType.CLAN_TECH, Lists.newArrayList(affectedAdditionIds));

            // 给客户端同步变更
            if (!onLoad) {
                notifyUpdate(ClanTechInfoUpdateMsg.newBuilder()
                        .setModData(ClanPB.ClanTechModelPB.newBuilder()
                                .setResearchingTech(StructPB.Int32ClanTechResearchInfoMapPB.newBuilder()
                                        .addDeleteKeys(techSubId)
                                        .build())
                                .setFinishedTech(StructPB.Int32ClanTechInfoMapPB.newBuilder()
                                        .putDatas(techProp.getTechId(), techProp.getCopyCsBuilder().build())
                                        .build())
                                .setRecommendTechId(prop.getRecommendTechId())
                                .build()).build()
                );
            }

            // 研发完成邮件
            try {
                int finishMailId = ResHolder.getConsts(ConstClanTechTemplate.class).getResearchFinishMailId();
                StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
                params.setMailTemplateId(finishMailId);
                params.getSenderBuilder().setSenderId(0);
                params.setContent(StructMail.MailContent.newBuilder()
                        .setContentType(MailContentType.MAIL_CONTENT_TYPE_CLAN_TECH_RESEARCH_FINISH)
                        .setClanTechResearchFinishData(StructMail.MailClanTechResearchFinishData.newBuilder()
                                .setTechSubId(techSubId).build()));
                MailUtil.sendClanMail(this.getOwner().getZoneId(), this.getEntityId(), params.build());
            } catch (Exception e) {
                LOGGER.error("{} clanTechResearchFinish mail send failed:{}", getOwner(), techSubId);
            }
            trySendQlog("tech_update", techProp.getTechId(), 0, techSubId);
        }
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.CLAN_TECH;
    }

    @Override
    public Map<AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        Map<AdditionSourceType, Long> result = Maps.newEnumMap(AdditionSourceType.class);
        Integer value = additionMap.getOrDefault(additionId, 0);
        result.put(AdditionSourceType.AST_CLAN_ACADEMY, (long) value);
        return result;
    }

    /**
     * 联盟科技特殊效果只有一个解锁堡垒，唉
     */
    public boolean isFortressUnlocked() {
        for (ClanTechInfoProp techProp : prop().getFinishedTech().values()) {
            int techSubId = techProp.getTechSubId();
            if (techSubId > 0) {
                ClanTechSubTemplate subTemplate = ResHolder.getTemplate(ClanTechSubTemplate.class, techSubId);
                List<IntPairType> paramPairList = subTemplate.getParamPairList();
                if (paramPairList != null) {
                    for (IntPairType paramPair : paramPairList) {
                        if (paramPair.getKey() == ClanTechAttrType.CTAT_UNLOCK_MAP_BUILDING_VALUE
                                && paramPair.getValue() == MapBuildingType.MBT_CLAN_FORTRESS_VALUE) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private void notifyUpdate(ClanTechInfoUpdateMsg msg) {
        BroadcastHelper.toCsOnlinePlayerInClan(ownerActor().getZoneId(),
                getOwner().getEntityId(),
                MsgType.PLAYER_NOTIFYCLANTECHINFO_NTF,
                msg
        );
    }

    public ClanTechDetailAns handleDetail(ClanTechDetailAsk ask) {
        int techId = ask.getTechId();
        ClanTechDetailAns.Builder ans = ClanTechDetailAns.newBuilder();
        ClanTechInfoProp techProp = prop().getFinishedTech().get(techId);
        if (techProp != null) {
            ans.setTechInfo(techProp.getCopySsBuilder());
        } else {
            ans.setTechInfo(Struct.ClanTechInfo.newBuilder().setTechId(techId));
        }
        return ans.build();
    }

    /**
     * 解锁所有军团科技
     * 不能改成一级级升的优雅方式，因为消息太多了
     * 超武一定得一级级升，不然会有企微报错
     * 其他科技可以只升最高级
     * 目前会有92次军团科技升级，会有addition同步消息和邮件发给player，比较爆炸
     * （打黑工的下场）
     */
    public void gmUnlockAllClanTech() {
        // 已经在研究的科技完成一下呢~
        gmFinishResearch();
        int lastTechId = 0;
        int lastSubTechId = 0;
        for (ClanTechSubTemplate techTemplate : ResHolder.getInstance().getListFromMap(ClanTechSubTemplate.class)) {
            if (techTemplate.getParamPairList().isEmpty() && techTemplate.getBuffPairList().isEmpty()) {
                // 如果没有加成也没有超武配置，认为是没用、没完成或者特殊用途的科技，直接跳过就好
                continue;
            }
            // 科技id不同嘞！
            if (lastTechId != techTemplate.getTechId() && lastTechId != 0) {
                ClanTechInfoProp techInfoProp = prop().getFinishedTechV(lastTechId);
                if (techInfoProp == null || techInfoProp.getTechSubId() < lastSubTechId) {
                    // 没研发的科技或者科技的子科技还没研发到顶级，就暴力研发最高级
                    gmUnlockSpecificClanTech(lastSubTechId);
                }
            }
            // 记录下当前的科技id和本次的科技子id
            lastTechId = techTemplate.getTechId();
            lastSubTechId = techTemplate.getId();
        }
        ClanTechSubTemplate techTemplate = ResHolder.getInstance().getValueFromMap(ClanTechSubTemplate.class, lastSubTechId);
        if (techTemplate == null) {
            LOGGER.info("gmUnlockAllClanTech lastSubTechId {} not found", lastSubTechId);
            return;
        }
        if (techTemplate.getParamPairList().isEmpty() && techTemplate.getBuffPairList().isEmpty()) {
            // 最后一个科技是特殊科技，不需要解锁
            return;
        }
        if (!techTemplate.getParamPairList().isEmpty()) {
            // 最后一个科技是超武，不知道怎么处理，不管了
            return;
        }
        // 最后的一般科技的解锁
        ClanTechInfoProp techInfoProp = prop().getFinishedTechV(lastTechId);
        if (techInfoProp == null || techInfoProp.getTechSubId() < lastSubTechId) {
            // 没研发的科技或者科技的子科技还没研发到顶级，就暴力研发最高级
            gmUnlockSpecificClanTech(lastSubTechId);
        }
    }

    /**
     * 解锁特定军团科技
     *
     * @param targetTechSubId 目标科技子id
     */
    public boolean gmUnlockSpecificClanTech(int targetTechSubId) {
        // 先做配置检查
        ClanTechSubTemplate template = ResHolder.getInstance().findValueFromMap(ClanTechSubTemplate.class, targetTechSubId);
        if (template.getParamPairList().isEmpty() && template.getBuffPairList().isEmpty()) {
            // 如果没有加成也没有超武配置，认为是没用、没完成或者特殊用途的科技，直接返回false
            return false;
        }
        final ClanTechModelProp prop = prop();
        prop.addEmptyResearchingTech(targetTechSubId)
                .setStartTsSec((int) SystemClock.nowOfSeconds());
        int techId = template.getTechId();
        ClanTechInfoProp techProp = prop.getFinishedTech().get(techId);
        if (techProp == null) {
            prop.addEmptyFinishedTech(techId);
        }
        tryFinishResearch(targetTechSubId, false);
        return true;
    }
}

package com.yorha.cnc.clan.enums;

import com.yorha.proto.CommonEnum;

/**
 * 资源对应相关枚举映射枚举
 *
 * <AUTHOR>
 */
public enum ClanResourceMapType {

    /**
     * 石油相关枚举
     */
    OIL_TYPE(CommonEnum.ClanResourceType.CNRT_OIL,
            CommonEnum.BuffEffectType.ET_CLAN_OIL_PRODUCE_BUFF),

    /**
     * 钢铁相关枚举
     */
    TUNGSTEN_GOLD_TYPE(CommonEnum.ClanResourceType.CNRT_STEEL,
            CommonEnum.BuffEffectType.ET_CLAN_STEEL_PRODUCE_BUFF),

    /**
     * 稀土相关枚举
     */
    RARE_EARTH_TYPE(CommonEnum.ClanResourceType.CNRT_RARE_EARTH,
            CommonEnum.BuffEffectType.ET_CLAN_RARE_EARTH_PRODUCE_BUFF),

    /**
     * 钛矿相关枚举
     */
    TIBERIUM_TYPE(CommonEnum.ClanResourceType.CNRT_TIBERIUM,
            CommonEnum.BuffEffectType.ET_CLAN_TITANIUM_PRODUCE_BUFF),
    /**
     * 水晶相关枚举
     */
    CRYSTAL_TYPE(CommonEnum.ClanResourceType.CNRT_CRYSTAL,
            CommonEnum.BuffEffectType.ET_CLAN_CRYSTAL_PRODUCE_BUFF);

    /**
     * 资源类型
     */
    private final CommonEnum.ClanResourceType resourceType;

    /**
     * 资源产量的buff加成数值
     */
    private final CommonEnum.BuffEffectType produceBuffId;

    ClanResourceMapType(CommonEnum.ClanResourceType resourceType, CommonEnum.BuffEffectType produceBuffId) {
        this.resourceType = resourceType;
        this.produceBuffId = produceBuffId;
    }

    public CommonEnum.ClanResourceType getResourceType() {
        return resourceType;
    }

    public CommonEnum.BuffEffectType getProduceBuffId() {
        return produceBuffId;
    }
}

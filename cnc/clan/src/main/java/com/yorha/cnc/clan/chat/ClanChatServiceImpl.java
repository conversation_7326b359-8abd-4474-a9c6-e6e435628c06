package com.yorha.cnc.clan.chat;

import com.yorha.cnc.clan.ClanActor;
import com.yorha.cnc.clan.component.ClanChatComponent;
import com.yorha.common.actor.ClanChatService;
import com.yorha.proto.SsClanChat.FetchClanChatAsk;
import com.yorha.proto.SsClanChat.SendClanChatAsk;

/**
 * <AUTHOR>
 */
public class ClanChatServiceImpl implements ClanChatService {
    private final ClanActor clanActor;

    public ClanChatServiceImpl(ClanActor actor) {
        this.clanActor = actor;
    }

    private ClanChatComponent getChatComponent() {
        return clanActor.getOrLoadClanEntity().getChatComponent();
    }

    @Override
    public void handleFetchClanChatAsk(FetchClanChatAsk ask) {
        getChatComponent().fetchChatMsg(ask.getFromId(), ask.getToId(), ask.getShieldListList());
    }

    @Override
    public void handleSendClanChatAsk(SendClanChatAsk ask) {
        getChatComponent().sendChatMsg(ask.getChatMessage());
    }
}

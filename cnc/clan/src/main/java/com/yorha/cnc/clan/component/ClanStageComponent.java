package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ClanApplyOwnerInfoProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.ClanStageModelProp;
import com.yorha.proto.CommonEnum.ClanEnterRequire;
import com.yorha.proto.CommonEnum.ClanStageType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class ClanStageComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanStageComponent.class);

    private ActorTimer dissolutionTask = null;

    /**
     * 是否开始解散
     */
    private boolean startDissolve = false;

    /**
     * 是否已经解散了
     */
    private boolean alreadyDisband = false;

    public ClanStageComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void onCreate() {
        getProp().setStage(ClanStageType.CST_NORMAL);
    }

    @Override
    public void onLoad() {
        // do nothing
    }

    @Override
    public void postLoad() {
        // do nothing
    }

    /**
     * @return 返回是否解散
     */
    public void tryDissolution() {
        long dissolveTsMs = getDissolveTsMs();
        if (dissolveTsMs <= 0L) {
            return;
        }
        long now = SystemClock.now();
        if (dissolveTsMs > now) {
            startOnceDissolveTask(dissolveTsMs - now);
            return;
        }
        dissolveOrTransferOwner();
    }

    /**
     * 真正的解散逻辑
     */
    public void realDissolve() {
        try {
            setStartDissolve();
            getOwner().getMemberComponent().kickAllMembers();
            getOwner().callAllComponentsSafe(ClanComponent::onDissolution);
            LOGGER.info("clan {} realDissolve success", getOwner());
        } catch (Exception e) {
            LOGGER.error("clan {} realDissolve error, ", getOwner(), e);
        }
        // 尽可能保证走到destroy，删除db里的数据
        afterClanDissolution();
        LOGGER.info("clan realDissolve finish");
    }

    /**
     * NOTE(furson): 高危接口，调用时请小心
     * 军团解散后处理
     * 目前会有两种情况调用：
     * 1. 军团被拉起后，clanActor上已经有clanEntity，timer到期解散或者执行gm命令解散
     * 2. 军团被拉起过程中，clanActor上还没有clanEntity，发现军团需要解散
     */
    private void afterClanDissolution() {
        setAlreadyDisband();
        getOwner().getClanQlogComponent().qLogDismiss();
        getOwner().deleteObj();
        // 统一将entity设置为null
        ownerActor().setClanEntityNull();
        // Clan Actor 再见了！我们不会永远记得你的！
        // 设置clanactor已解散，延时销毁actor，防止在解散过程中有人尝试再次load
        ownerActor().setDissolved();
        ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_ACTOR_DESTROY,
                () -> ownerActor().forceDestroy("ClanDissolution"),
                10,
                TimeUnit.SECONDS);
    }

    public void playerStartDissolveClan(long playerId) {
        // 玩家并非军团长
        if (!getOwner().getMemberComponent().isClanOwner(playerId)) {
            LOGGER.warn("player {} not the owner of clan {}, try dissolve clan", playerId, getOwner().getEntityId());
            throw new GeminiException(ErrorCode.CLAN_NOT_CLAN_OWNER);
        }
        // 军团剩余人数不等于1
        if (getOwner().getProp().getMemberSize() != 1) {
            LOGGER.warn("player {} try dissolve clan when clan member not 1", playerId);
            throw new GeminiException(ErrorCode.CLAN_MEMBER_MORE_THAN_ONE);
        }
        // 军团当前并非任意人可以加入的
        if (getOwner().getSettingComponent().getClanEnterRequire() != ClanEnterRequire.NONE) {
            LOGGER.warn("clan {} isn't anybody can enter", getOwner().getEntityId());
            throw new GeminiException(ErrorCode.CLAN_WRONG_ENTER_REQUIRE);
        }

        ConstClanTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class);
        long countDownTsMs = TimeUnit.MINUTES.toMillis(constTemplate.getDissolutionCountdown());
        getProp().setStage(ClanStageType.CST_ON_DISSOLUTION).setDissolutionTsMs(SystemClock.now() + countDownTsMs);
        startOnceDissolveTask(countDownTsMs);
        // 军团card更新：军团开始解散，实时同步
        getOwner().getPropComponent().updateClanCardCache(true);
    }

    public void playerCancelDissolveClan(long playerId) {
        // 玩家并非军团长
        if (!getOwner().getMemberComponent().isClanOwner(playerId)) {
            LOGGER.warn("player {} not the owner of clan {}, try dissolve clan", playerId, getOwner().getEntityId());
            throw new GeminiException(ErrorCode.CLAN_NOT_CLAN_OWNER);
        }
        // 军团当前并未处于解散状态
        if (getProp().getStage() != ClanStageType.CST_ON_DISSOLUTION) {
            LOGGER.warn("clan {} isn't on dissolution stage but trying to cancel dissolve", getOwner().getEntityId());
            throw new GeminiException(ErrorCode.CLAN_STAGE_WRONG);
        }
        getProp().setStage(ClanStageType.CST_NORMAL).setDissolutionTsMs(0L).clearApplyOwnerMemberInfo();
        cancelDissolveTask();
        getOwner().getMsgComponent().sendCancelDissolveMail(playerId);
        // 军团card更新：军团取消解散，实时同步
        getOwner().getPropComponent().updateClanCardCache(true);
    }

    /**
     * 获取预解散相关的信息信息
     */
    public ClanStageModelProp fetchDissolveClanInfo() {
        ClanStageModelProp retProp = getOwner().getProp().getStageModel();
        for (long memberId : retProp.getApplyOwnerMemberInfo().keySet()) {
            ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(memberId);
            if (memberProp == null) {
                LOGGER.error("fetchDissolveClanInfo member {} not found in clan {}", memberId, getOwner());
                continue;
            }
            // 需要是可变的，所以必须实时更新
            ClanApplyOwnerInfoProp applyOwnerMemberInfoV = retProp.getApplyOwnerMemberInfoV(memberId);
            applyOwnerMemberInfoV.getCardHead().setName(memberProp.getCardHead().getName())
                    .setPic(memberProp.getCardHead().getPic())
                    .setPicFrame(memberProp.getCardHead().getPicFrame())
                    .setPicUrl(memberProp.getCardHead().getPicUrl());
            applyOwnerMemberInfoV.setCombat(memberProp.getComboat());
        }
        return retProp;
    }

    public long getDissolveTsMs() {
        return getProp().getDissolutionTsMs();
    }

    /**
     * @return 返回是否执行了解散逻辑
     */
    public void dissolveOrTransferOwner() {
        LOGGER.info("dissolving time end: dissolve or transferOwner");
        if (getProp().isApplyOwnerMemberInfoEmpty()) {
            // 无人申请，军团打咩
            LOGGER.info("clan {} success dissolution", getOwner());
            realDissolve();
        } else {
            long nowOwnerId = getOwner().getMemberComponent().getOwnerId();
            Long nextOwnerId = getNextOwnerPlayerId();
            if (nextOwnerId == null) {
                // 找不到下一个军团长
                LOGGER.info("clan {} try dissolution with no next owner found", getOwner());
                realDissolve();
            } else {
                // 新军团长
                try {
                    getOwner().getStaffComponent().transferClanOwner(nowOwnerId, nextOwnerId);
                } catch (Exception e) {
                    LOGGER.info("dissolveOrTransferOwner transferClanOwner failed ", e);
                    realDissolve();
                    return;
                }
                getProp().setStage(ClanStageType.CST_NORMAL).setDissolutionTsMs(0L).clearApplyOwnerMemberInfo();
            }
        }
    }

    public void applyOwnerWhenDissolving(long playerId) {
        if (getProp().getStage() != ClanStageType.CST_ON_DISSOLUTION) {
            LOGGER.debug("{} not in dissolution", getOwner().getEntityId());
            return;
        }
        if (!getOwner().getMemberComponent().isClanMember(playerId)) {
            LOGGER.error("{} not in clan {} but try apply owner when dissolving", playerId, getOwner().getEntityId());
            return;
        }
        if (getProp().getApplyOwnerMemberInfoV(playerId) != null) {
            LOGGER.debug("{} try apply owner again when dissolving", playerId);
            return;
        }
        getProp().getApplyOwnerMemberInfo().put(playerId, new ClanApplyOwnerInfoProp().setId(playerId));
        getOwner().getMsgComponent().sendClanOwnerApplyMail(playerId);
    }

    public void cancelApplyOwnerWhenDissolving(long playerId) {
        if (getProp().getStage() != ClanStageType.CST_ON_DISSOLUTION) {
            LOGGER.debug("{} not in dissolution", getOwner().getEntityId());
            return;
        }
        if (!getOwner().getMemberComponent().isClanMember(playerId)) {
            LOGGER.error("{} not in clan {} but try cancel apply owner when dissolving", playerId, getOwner().getEntityId());
            return;
        }
        if (getProp().getApplyOwnerMemberInfoV(playerId) == null) {
            LOGGER.info("{} try cancel apply owner when not applying", playerId);
            return;
        }
        getProp().removeApplyOwnerMemberInfoV(playerId);
        getOwner().getMsgComponent().sendCancelApplyOwnerMail(playerId);
    }

    /**
     * @return 返回军团是否处于解散状态
     */
    public boolean isDissolving() {
        return getProp().getStage() == ClanStageType.CST_ON_DISSOLUTION;
    }

    public boolean isStartDissolve() {
        return startDissolve;
    }

    public void setStartDissolve() {
        startDissolve = true;
    }

    public boolean isAlreadyDisband() {
        return alreadyDisband;
    }

    private void setAlreadyDisband() {
        alreadyDisband = true;
    }

    private Long getNextOwnerPlayerId() {
        Long nextOwnerId = null;
        long maxCombat = -1L;
        long now = SystemClock.now();
        for (Long memberId : getProp().getApplyOwnerMemberInfo().keySet()) {
            ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(memberId);
            if (memberProp == null) {
                LOGGER.error("getNextOwnerPlayerId member {} not found in clan {}", memberId, getOwner());
                continue;
            }
            if (memberProp.getNextCanBeOwnerTsMs() >= now) {
                continue;
            }
            // 就是盟主，不要
            if (getOwnerId() == memberId) {
                continue;
            }
            if (memberProp.getComboat() > maxCombat) {
                maxCombat = memberProp.getComboat();
                nextOwnerId = memberId;
            }
        }
        return nextOwnerId;
    }

    private void startOnceDissolveTask(long delayMs) {
        dissolutionTask = ownerActor().addTimer(getEntityId(), TimerReasonType.CLAN_DISSOLVE_OR_TRANSFER_OWNER,
                this::dissolveOrTransferOwner, delayMs, TimeUnit.MILLISECONDS);
    }

    private void cancelDissolveTask() {
        if (dissolutionTask == null) {
            return;
        }
        dissolutionTask.cancel();
        dissolutionTask = null;
    }

    private ClanStageModelProp getProp() {
        return getOwner().getProp().getStageModel();
    }
}

package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanTechService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;

import static com.yorha.proto.SsClanTech.*;

/**
 * 军团科技
 */
public class ClanTechServiceImpl implements ClanTechService {
    private final ClanActor clanActor;

    public ClanTechServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }

    @Override
    public void handleClanTechFetchInfoAsk(ClanTechFetchInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        ClanTechFetchInfoAns ans = clanEntity.getTechComponent().handleFetch(ask);
        clanActor.answer(ans);
    }

    @Override
    public void handleClanTechDonateCheckAsk(ClanTechDonateCheckAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        final int clanTechId = ask.getClanTechId();
        ClanTechDonateCheckAns ans = clanEntity.getTechComponent().handleDonateCheck(clanTechId);
        clanActor.answer(ans);
    }

    @Override
    public void handleClanTechDonateAsk(ClanTechDonateAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        ClanTechDonateAns ans = clanEntity.getTechComponent().handleDonate(ask);
        clanActor.answer(ans);
    }

    @Override
    public void handleClanTechResearchAsk(ClanTechResearchAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getTechComponent().handleResearch(ask);
        clanActor.answer(ClanTechResearchAns.getDefaultInstance());
    }

    @Override
    public void handleClanTechRecommendAsk(ClanTechRecommendAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getTechComponent().handleRecommend(ask);
        clanActor.answer(ClanTechRecommendAns.getDefaultInstance());
    }

    @Override
    public void handleClanTechDetailAsk(ClanTechDetailAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        ClanTechDetailAns ans = clanEntity.getTechComponent().handleDetail(ask);
        clanActor.answer(ans);
    }
}

package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanActor;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.framework.AbstractComponent;

/**
 * clan组件基类
 *
 * <AUTHOR>
 */
public abstract class ClanComponent extends AbstractComponent<ClanEntity> {

    public ClanComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 联盟创建后
     */
    public void onCreate() {
    }

    /**
     * 联盟加载后
     */
    public void onLoad() {
    }

    /**
     * onLoad后
     */
    public void postLoad() {
    }

    /**
     * 联盟解散
     */
    public void onDissolution() {
    }

    @Override
    public ClanActor ownerActor() {
        return getOwner().ownerActor();
    }

    public long getOwnerId() {
        return this.getOwner().getProp().getOwnerId();
    }
}

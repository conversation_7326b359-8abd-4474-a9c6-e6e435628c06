package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.notification.NotificationBuilder;
import com.yorha.common.notification.PushNotificationManager;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ClanPushNotificationComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanPushNotificationComponent.class);
    private final PushNotificationManager pushNotificationManager = new PushNotificationManager();

    public ClanPushNotificationComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 获得军团礼物黄金宝箱
     */
    public void pushGiveGoldBoxNotification() {
        this.pushOfflineNotification(NotificationBuilder.GIVE_GOLD_BOX);
    }

    public void onPushNotifyCmd(final int notifyId) {
        NotificationBuilder notificationBuilder = NotificationBuilder.getNotificationBuilder(notifyId);
        if (notificationBuilder == null) {
            LOGGER.error("onPushNotifyCmd unknown notifyId={}", notifyId);
            return;
        }
        this.pushOfflineNotification(notificationBuilder);
    }

    public void pushClanMailNotification() {
        this.pushOfflineNotification(NotificationBuilder.NEW_CLAN_MAIL);
    }

    private void pushOfflineNotification(NotificationBuilder notificationBuilder) {
        try {
            List<Long> offlineMembers = new ArrayList<>(getOwner().getMemberComponent().getAllClanOfflinePlayerIds());
            List<IActorRef> ntfRefs = new ArrayList<>();
            ntfRefs.add(RefFactory.ofLocalPushNotification());
            this.pushNotificationManager.pushMultipleNotification(notificationBuilder, offlineMembers, ntfRefs);
        } catch (Exception e) {
            WechatLog.error("ClanPushNotificationComponent pushOfflineNotification, notificationBuilder={}, e=", notificationBuilder, e);
        }
    }
}

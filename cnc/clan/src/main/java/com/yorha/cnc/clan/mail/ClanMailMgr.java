package com.yorha.cnc.clan.mail;

import com.google.protobuf.Message;
import com.yorha.cnc.clan.ClanActor;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.db.tcaplus.msg.DeleteByPartKeyAsk;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.option.DeleteByPartKeyOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.mail.MailMgrBase;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.mail.MailTemplateService;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.StructMail;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.collections4.CollectionUtils;
import res.template.MailExpireTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public class ClanMailMgr extends MailMgrBase {
    public ClanMailMgr(ClanEntity owner, ClanActor actor) {
        super(actor);
        this.owner = owner;
    }

    /**
     * 军团实体
     */
    private final ClanEntity owner;

    /**
     * 军团邮件
     */
    private final ClanMailData clanMailData = new ClanMailData();

    /**
     * 加载邮件
     */
    public void loadMails() {
        TcaplusDb.ClanMailStorageTable.Builder req = TcaplusDb.ClanMailStorageTable.newBuilder();
        req.setZoneId(getOwner().getZoneId());
        req.setClanId(getOwner().getEntityId());
        final GetByPartKeyResult<TcaplusDb.ClanMailStorageTable.Builder> ans = getActor().call(RefFactory.dbActorRef(), new SelectAsk<>(req));
        if (!ans.isOk() && !ans.isRecordNotExist()) {
            throw new GeminiException("loadMails load from db fail, code:{}", ans.getCode());
        }
        if (CollectionUtils.isEmpty(ans.values)) {
            return;
        }
        for (ValueWithVersion<TcaplusDb.ClanMailStorageTable.Builder> vv : ans.getValues()) {
            final TcaplusDb.ClanMailStorageTable.Builder mail = vv.value;
            final StructMail.NewMailCache cache = mail.getFullAttr();
            // cache 联盟邮件
            clanMailData.addMailCache(cache);
        }
        LOGGER.info("ClanMailMgr loadMails mail cache init finish!");
        clearClanMails();
    }

    @Override
    public int getMailIndex() {
        return clanMailData.getMaxMailIndex();
    }

    @Override
    protected Message.Builder getInsertReqBuilder(StructMail.NewMailCache mailCache) {
        TcaplusDb.ClanMailStorageTable.Builder req = TcaplusDb.ClanMailStorageTable.newBuilder();
        req.setZoneId(getActor().getZoneId());
        req.setClanId(getOwner().getEntityId());
        req.setMailId(mailCache.getMailId());
        req.setFullAttr(mailCache);
        return req;
    }

    @Override
    protected Message.Builder getRemoveReqBuilder(long mailId) {
        TcaplusDb.ClanMailStorageTable.Builder req = TcaplusDb.ClanMailStorageTable.newBuilder();
        req.setZoneId(getActor().getZoneId());
        req.setClanId(getOwner().getEntityId());
        req.setMailId(mailId);
        return req;
    }

    @Override
    protected void afterRemoveMail(long mailId) {
        final boolean isRemoved = clanMailData.removeMailCache(mailId);
        LOGGER.info("ClanMailMgr afterRemoveMail mailId={}, isRemoved={}, totalNum={}", mailId, isRemoved, clanMailData.getMailCacheMap().size());
    }

    /**
     * 发送联盟邮件
     *
     * @param mailId         邮件id
     * @param mailSendParams 邮件发送参数
     */
    public void sendClanMail(long mailId, StructMail.MailSendParams mailSendParams) {
        final int nextMailIndex = clanMailData.getNextMailIndex();

        Consumer<StructMail.NewMailCache> afterDbInsert = (newMailCache) -> {
            // 获取所有在线的军团成员
            List<Long> onlinePlayerIds = getOwner().getMemberComponent().onlinePlayerIds();
            final SsPlayerMisc.OnReceiveMailCmd cmd = SsPlayerMisc.OnReceiveMailCmd.newBuilder()
                    .setNewMailCache(newMailCache)
                    .build();
            BroadcastHelper.toAllTargets(toActorRef(onlinePlayerIds), getOwner().ownerActor().self(), cmd);
            LOGGER.info("ClanMailMgr sendClanMail, clan {} send clan mail success. mailId={}, templateId={}, nextMailIndex={}",
                    getOwner(), newMailCache.getMailId(), newMailCache.getMailTemplateId(), nextMailIndex);
            // 发送后尝试清理邮件
            clearClanMails();
            // 不在线的成员推送
            getOwner().getNotificationComponent().pushClanMailNotification();
        };

        addNewMailCacheToDb(mailSendParams, CommonEnum.MailType.MAIL_TYPE_CLAN, clanMailData, getOwner().getEntityId(), mailId, nextMailIndex, afterDbInsert);
    }

    public List<StructMail.NewMailCache> getOfflineMails(int mailIndex, long enterClanTsMs) {
        List<StructMail.NewMailCache> offlineMails = new ArrayList<>();
        if (clanMailData.getMailCacheMap().isEmpty()) {
            return offlineMails;
        }
        if (mailIndex < 0) {
            // 下标负数表示首次拉取，没有下标凭证，只需要依据enterClanTsMs过滤邮件
            for (StructMail.NewMailCache mail : clanMailData.getMailCacheMap().values()) {
                if (MailUtil.mailCreateTimeChecker(mail.getCreateTimestamp(), enterClanTsMs)
                        && MailUtil.mailExpireTimeChecker(mail.getExpireTimestamp())) {
                    offlineMails.add(mail);
                }
            }
        } else {
            // 下标非负，依据下标过滤邮件
            for (StructMail.NewMailCache mail : clanMailData.getMailCacheMap().values()) {
                if (MailUtil.mailIndexChecker(mail.getMailIndex(), mailIndex)
                        && MailUtil.mailExpireTimeChecker(mail.getExpireTimestamp())) {
                    offlineMails.add(mail);
                }
            }
        }
        return offlineMails;
    }

    public void deleteAllMails() {
        if (clanMailData.getMailCacheMap().isEmpty()) {
            // 内存里都没东西，直接返回
            return;
        }
        TcaplusDb.ClanMailStorageTable.Builder req = TcaplusDb.ClanMailStorageTable.newBuilder();
        req.setZoneId(getOwner().getZoneId());
        req.setClanId(getOwner().getEntityId());
        // 交由tcaplusMgrService托管
        owner.ownerActor().tell(RefFactory.dbActorRef(), new DeleteByPartKeyAsk<>(req, DeleteByPartKeyOption.newBuilder().withRetry().build()));
        LOGGER.info("ClanMailMgr deleteAllMails {} size:{}", getOwner(), getClanMailTotalNum());
    }

    public int getClanMailTotalNum() {
        return clanMailData.getMailCacheMap().size();
    }

    /**
     * 清理联盟邮件
     */
    private void clearClanMails() {
        final MailExpireTemplate clanMailExpireTemplate = ResHolder.getResService(MailTemplateService.class)
                .getMailExpireTemplate(CommonEnum.MailTabsType.MAIL_TABS_TYPE_CLAN);
        final int expireDays = clanMailExpireTemplate.getExpire();
        final int maxClanMailNum = clanMailExpireTemplate.getMax();
        LOGGER.info("ClanMailMgr clearClanMails {} start clear size:{}", getOwner(), getClanMailTotalNum());
        clearMails(clanMailData, expireDays, maxClanMailNum);
        LOGGER.info("ClanMailMgr clearClanMails {} after clear size:{}", getOwner(), getClanMailTotalNum());
    }

    private ClanEntity getOwner() {
        return owner;
    }
}

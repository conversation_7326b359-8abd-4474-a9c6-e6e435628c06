package com.yorha.cnc.clan.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.ClanEnterRequire;
import com.yorha.proto.CommonEnum.ClanOperationType;
import com.yorha.proto.Core.Code;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncGuildStaffManage;

import javax.annotation.Nullable;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.yorha.proto.CommonEnum.ClanResourceType;

/**
 * 联盟成员进出
 *
 * <AUTHOR>
 */
public class ClanMemberComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanMemberComponent.class);

    /**
     * 等待加入联盟超时时长
     */
    private static final int JOIN_PENDING_TIME_OUT_SEC = 12;

    /**
     * 正在加入联盟的玩家
     * <playerId, expire time>
     */
    private final Map<Long, Long> joinPendingMembers = new HashMap<>();

    /**
     * 系统操作id
     */
    private final long systemOperatorId = 0L;

    /**
     * 联盟在线玩家
     */
    private final Map<Long, IActorRef> checkInClients = new HashMap<>();

    public ClanMemberComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 广播所有联盟在线玩家cs消息
     */
    public void broadcastCsMsgToAllOnlineMemberInClan(int msgType, GeneratedMessageV3 msg) {
        SessionHelper.broadcastMsgToSessions(checkInClients.values(), this.ownerActor().self(), msgType, msg);
    }

    /**
     * 广播所有联盟在线玩家cs消息
     */
    public void broadcastCsMsgToAllOnlineMemberInClan(int msgType, ByteString msgBytes) {
        SessionHelper.broadcastMsgToSessions(checkInClients.values(), this.ownerActor().self(), msgType, msgBytes);
    }

    /**
     * 广播联盟中除了特定玩家之外的所有在线玩家
     */
    public void sendMsgToOnlineClanMembersExcludeSpecificMembers(int msgType, GeneratedMessageV3 msg, List<Long> excludeMemberIds) {
        List<IActorRef> targetRefList = new LinkedList<>();
        for (long memberId : checkInClients.keySet()) {
            if (excludeMemberIds.contains(memberId)) {
                continue;
            }
            IActorRef sessionRef = checkInClients.get(memberId);
            targetRefList.add(sessionRef);
        }
        SessionHelper.broadcastMsgToSessions(targetRefList, this.ownerActor().self(), msgType, msg);
    }

    /**
     * 广播联盟中特定的在线玩家
     */
    public void sendMsgToOnlineClanMembersSpecificMembers(int msgType, GeneratedMessageV3 msg, Set<Long> specificMemberIds) {
        List<IActorRef> targetRefList = new LinkedList<>();
        for (Long memberId : specificMemberIds) {
            if (!isOnline(memberId)) {
                continue;
            }
            IActorRef sessionRef = checkInClients.get(memberId);
            targetRefList.add(sessionRef);
        }
        SessionHelper.broadcastMsgToSessions(targetRefList, this.ownerActor().self(), msgType, msg);
    }

    /**
     * 登录player。
     *
     * @param playerId   玩家Id
     * @param sessionRef 玩家Ref。
     * @return 是否登录成功。当且仅当玩家不再联盟，登入失败。
     */
    public boolean checkInMember(final long playerId, final IActorRef sessionRef) {
        final ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(playerId);
        if (memberProp == null) {
            return false;
        }
        if (sessionRef == null) {
            return false;
        }
        if (isOnline(playerId)) {
            LOGGER.error("check in playerId={}, when session={} already check in!", playerId, this.checkInClients.get(playerId));
        }
        memberProp.setIsOnline(true);
        memberProp.setLastOnlineTsMs(SystemClock.now());
        this.checkInClients.put(playerId, sessionRef);
        // 有玩家在线期间不淘汰
        ownerActor().cancelReceiveTimeout();

        getOwner().getStaffComponent().onMemberCheckIn(playerId);
        // 尝试发送帮助气泡ntf
        getOwner().getHelpComponent().trySyncCanHelpItemIds(playerId);
        return true;
    }

    /**
     * 填充在线登入的玩家所需的信息
     *
     * @param ans 要给在线的玩家返回的信息的builder
     */
    public void fillCheckInAns(SsClanMember.CheckInClanAsk ask, SsClanMember.CheckInClanAns.Builder ans) {
        LOGGER.info("ClanMemberComponent fillCheckInAns, start fill info for player={}, enterClanTsMs={} clanMailIndex={} clanGiftIndex={}",
                ask.getPlayerId(), ask.getEnterClanTsMs(), ask.getClanMailIndex(), ask.getClanGiftIndex());
        long playerId = ask.getPlayerId();
        // 填充加成信息
        getOwner().getAddComponent().fillCheckInAdditionInfo(ans.getAdditionInfoBuilder());
        // 填充军团buff信息
        getOwner().getDevBuffComponent().fillCheckInBuffInfo(ans.getDevBuffInfoBuilder(), playerId);
        // 填充军团红点信息
        getOwner().getRedDotComponent().fillCheckInClanRedDotInfo(ans.getRedDotInfoBuilder());
        // 填充军团成员红点信息
        fillCheckInClanMemberRedDotInfo(playerId, ans.getRedDotInfoBuilder());
        // 填充军团基本信息
        getOwner().getPropComponent().fillCheckInBasicInfo(playerId, ans.getBasicInfoBuilder());
        // 填充军团礼物信息
        getOwner().getGiftComponent().fillCheckInGiftInfo(ask.getClanGiftIndex(), ans.getGiftInfoBuilder());
        // 填充军团建筑信息
        getOwner().getTerritoryComponent().fillCheckInBuildingInfo(ans.getBuildingInfoBuilder());
        // 填充军团邮件信息
        getOwner().getMailComponent().fillCheckInMailInfo(ask.getClanMailIndex(), ask.getEnterClanTsMs(), ans.getMailInfoBuilder());

        LOGGER.info("ClanMemberComponent fillCheckInAns, end fill info for player={}", ask.getPlayerId());
    }

    public void fillRefreshAns(SsClanBase.RefreshClanThingsAns.Builder ans, long playerId) {
        // scenePlayer登录时，来刷新数据
        getOwner().getDevBuffComponent().fillSceneBuffInfo(ans.getBuffSysBuilder(), playerId);
        getOwner().getAddComponent().fillSceneAdditionInfo(ans.getAdditionBuilder());
    }

    /**
     * 登出player。
     *
     * @param playerId 玩家id。
     * @return 是否登出成功。当且仅当玩家不再联盟，登出失败。
     */
    public boolean checkOutMember(final long playerId) {
        final ClanMemberProp memberProp = getOwner().getMemberComponent().getMemberNoThrow(playerId);
        if (memberProp == null) {
            LOGGER.error("check out playerId={} not in clan", playerId);
            return false;
        }
        IActorRef sessionRef = this.checkInClients.remove(playerId);
        if (sessionRef == null) {
            LOGGER.error("check out playerId={} when not check in!", playerId);
        }
        memberProp.setLastOnlineTsMs(SystemClock.now());
        memberProp.setIsOnline(false);
        // 没有人在线开启淘汰
        if (checkInClients.isEmpty()) {
            // 300秒淘汰，NOTE(furson): 300秒只是个经验值，没有数据支撑
            ownerActor().setReceiveTimeout(300);
        }
        if (isClanOwner(playerId)) {
            getOwner().getStaffComponent().tryAddAutoTransferOwnerTimer();
        }
        return true;
    }

    /**
     * 发送消息给成员client。
     *
     * @param playerId 成员id。
     * @param msgType  消息类型。
     * @param msg      消息体。
     */
    public void sendCsMsgToMemberClient(final long playerId, int msgType, GeneratedMessageV3 msg) {
        final ClanMemberProp memberProp = getMemberNoThrow(playerId);
        if (memberProp == null) {
            return;
        }
        final IActorRef sessionRef = this.checkInClients.get(playerId);
        if (sessionRef == null) {
            if (!memberProp.getIsOnline()) {
                return;
            }
            memberProp.setIsOnline(false);
            return;
        }
        SessionHelper.sendMsgToSession(sessionRef, ownerActor(), msgType, msg);
    }

    /**
     * 发送消息给在线的成员对应的player
     *
     * @param msg 消息体。
     */
    public void batchTellOnlinePlayer(GeneratedMessageV3 msg) {
        try {
            Set<Long> allClanOnlinePlayerIds = getAllClanOnlinePlayerIds();
            for (long onlinePlayerId : allClanOnlinePlayerIds) {
                ownerActor().tellPlayer(onlinePlayerId, msg);
            }

            LOGGER.info("success batch tells, tells num {}", allClanOnlinePlayerIds.size());
        } catch (Exception e) {
            LOGGER.error("batchTellOnlinePlayer error:", e);
        }
    }

    /**
     * 发送消息给在线的成员对应的player
     *
     * @param msg 消息体。
     */
    public void tellOnlinePlayerWithSpecPerm(GeneratedMessageV3 msg, ClanOperationType operationType) {
        try {
            Set<Long> specPermPlayerIds = getOnlinePlayerIdsWithPermCheck(operationType);
            for (long onlinePlayerId : specPermPlayerIds) {
                ownerActor().tellPlayer(onlinePlayerId, msg);
            }
            LOGGER.warn("success batch tells, tells num {}", specPermPlayerIds.size());
        } catch (Exception e) {
            LOGGER.error("batchTellOnlinePlayer error:", e);
        }
    }

    private int getJoinPendingSize() {
        // 移除加入超时的玩家
        List<Long> expireIds = joinPendingMembers.entrySet()
                .stream()
                .filter(it -> it.getValue() <= SystemClock.nowOfSeconds())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (!expireIds.isEmpty()) {
            for (long id : expireIds) {
                joinPendingMembers.remove(id);
            }
            LOGGER.error("player join clan time out! clanId:{}, players:{}", this.getEntityId(), expireIds);
        }


        return joinPendingMembers.size();
    }

    private void addJoinPendingMember(long playerId, long timestampSec) {
        joinPendingMembers.put(playerId, timestampSec + JOIN_PENDING_TIME_OUT_SEC);
    }

    private void removeJoinPendingMember(long playerId) {
        joinPendingMembers.remove(playerId);
    }

    /**
     * 是否是联盟所有者
     *
     * @param playerId 玩家id
     * @return 是或者否
     */
    public boolean isClanOwner(long playerId) {
        return getOwnerId() == playerId;
    }

    /**
     * 踢掉联盟成员
     *
     * @param operatorId 发起方
     * @param targetId   被踢方
     */
    public void kickOffClanMember(long operatorId, long targetId) {
        ClanMemberProp operatorMemberProp = getMemberNoThrow(operatorId);
        ClanMemberProp targetMemberProp = getMemberNoThrow(targetId);
        if (operatorMemberProp == null) {
            LOGGER.warn("kickOffClanMember operator not in! clan={}, operatorId={}", getOwner(), operatorId);
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (targetMemberProp == null) {
            LOGGER.warn("kickOffClanMember target not in! clan={}, targetId={}", getOwner(), targetId);
            throw new GeminiException(ErrorCode.CLAN_TARGET_PLAYER_NOT_IN_CLAN);
        }
        final ClanDataTemplateService dataTemplateService = this.getOwner().getDataTemplateService();
        if (!dataTemplateService.canStaffKickOffOtherStaff(operatorMemberProp.getStaffId(), targetMemberProp.getStaffId())) {
            LOGGER.warn("kickOffClanMember caller can't kickoff callee! clanId={}, operatorId={}, targetId={}", this.getEntityId(), operatorId, targetId);
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        this.kickPlayerOut(targetId, operatorId);
    }

    public void kickAllMembers() {
        LOGGER.info("clan {} start kick all members", this.getEntityId());
        long ownerId = this.getOwnerId();

        Set<Long> allMemberIds = new HashSet<>(getOwner().getMemberComponent().getAllMemberIds());
        for (long memberId : allMemberIds) {
            if (memberId == ownerId) {
                LOGGER.debug("member {} shouldn't be kick", ownerId);
                continue;
            }
            final Core.Code code = kickPlayerOut(memberId, systemOperatorId);
            if (!ErrorCode.isOK(code)) {
                LOGGER.warn("kickAllMembers clanId: {}, callee: {}, code: {}", this.getEntityId(), memberId, code);
            }
            getOwner().ownerActor().tellPlayer(memberId, SsPlayerClan.OnNtfClanKickOffResultCmd.newBuilder()
                    .setClanId(getEntityId())
                    .setCallerId(ownerId)
                    .setKickOffTsMs(SystemClock.now()).build());
        }

        if (!getOwner().getMemberComponent().getAllMemberIds().isEmpty()) {
            final Core.Code code = this.kickPlayerOut(ownerId, systemOperatorId);
            if (!ErrorCode.isOK(code)) {
                LOGGER.warn("forceKickOffClanMember clanId: {}, callee: {}, code: {}", this.getEntityId(), ownerId, code);
            }
            if (ownerId != 0) {
                getOwner().ownerActor().tellPlayer(ownerId, SsPlayerClan.OnNtfClanKickOffResultCmd.newBuilder()
                        .setClanId(getEntityId())
                        .setCallerId(0)
                        .setKickOffTsMs(SystemClock.now()).build());
            }
        }
    }


    /**
     * 成员请求加入联盟
     *
     * @param memberProp 成员信息
     * @return 加入结论
     */
    public Core.Code applyJoin(ClanMemberProp memberProp, String invitePlayerName) {
        ClanProp prop = getOwner().getProp();
        long applyPlayerId = memberProp.getId();
        // 是否是玩家主动加入
        boolean playerActiveJoin = "".equals(invitePlayerName);
        // 申请不存在 已经被处理
        if (isClanApplier(applyPlayerId) && playerActiveJoin) {
            return ErrorCode.CLAN_ALREADY_APPLY.getCode();
        }
        // 已经加入本联盟 刚好被审批
        if (isClanMember(applyPlayerId)) {
            LOGGER.warn("applyJoin but already in clan! {} {}", getOwner(), applyPlayerId);
            return ErrorCode.CLAN_ALREADY_IN_ONE.getCode();
        }
        final long operatorId = applyPlayerId;
        Set<Long> playerIds = getOwner().getStaffComponent().getHasSpecificPrivilegePlayerIds(ClanOperationType.COT_AUDIT);
        sendGuildStaffManageQLog(applyPlayerId, "apply_guild", operatorId,
                playerIds.size(),
                getOwner().getStaffComponent().getOnlineStaffCnt(playerIds));
        ClanEnterRequire clanEnterRequire = prop.getBase().getRequire();
        if (checkCanDirectJoin(clanEnterRequire, applyPlayerId, playerActiveJoin)) {
            if (isClanApplier(applyPlayerId)) {
                removeApply(applyPlayerId);
            }
            addJoinPendingMember(memberProp.getId(), SystemClock.nowOfSeconds());
            SsPlayerClan.ClanApplyResultAsk ask = buildClanApplyResultAsk(operatorId, true);

            ownerActor().<SsPlayerClan.ClanApplyResultAns>askPlayer(ownerActor().getZoneId(), applyPlayerId, ask)
                    .onComplete((ans, err) -> {
                        // 先移除这个pending
                        this.removeJoinPendingMember(applyPlayerId);
                        if (err != null) {
                            WechatLog.error("applyJoin allowed member join error: {} {}", operatorId, applyPlayerId, err);
                        } else {
                            if (!ans.getIsSuccess()) {
                                LOGGER.warn("player applyJoin but failed joinAsk! {} {}", getOwner(), applyPlayerId);
                            } else {
                                LOGGER.info("clan={} player={} joined clan success", getOwner(), ans.getPlayerId());
                                ClanMemberProp memberPropInAns = new ClanMemberProp();
                                memberPropInAns.mergeFromSs(ans.getMember());
                                playerJoined(memberPropInAns, operatorId);

                                if (playerActiveJoin) {
                                    // 记录主动加入日志
                                    getOwner().getLogComponent().logEnterClan(memberPropInAns.getCardHead().getName());
                                } else {
                                    // 记录受邀加入日志
                                    getOwner().getLogComponent().logInviteToClan(memberPropInAns.getCardHead().getName(), invitePlayerName);
                                    // 删除邀请
                                    getOwner().getInviteComponent().removeClanInvite(applyPlayerId);
                                }
                            }
                        }
                    });
            return ErrorCode.OK.getCode();
        }
        if (clanEnterRequire == ClanEnterRequire.NONE) {
            return ErrorCode.CLAN_FULL.getCode();
        } else if (clanEnterRequire == ClanEnterRequire.VERIFY) {
            // 审核制 邀请 人满 依然返回人满
            if (!playerActiveJoin) {
                return ErrorCode.CLAN_FULL.getCode();
            }
        }
        // 需要审核
        prop.putApplysV(memberProp);
        LOGGER.info("player={} apply join clan={} success", applyPlayerId, getOwner());
        getOwner().getRedDotComponent().tryAddApplyRedDot();
        getOwner().getMsgComponent().sendReviewApplyMail(memberProp);
        return ErrorCode.CLAN_APPLY_SUCCESS.getCode();
    }

    private SsPlayerClan.ClanApplyResultAsk buildClanApplyResultAsk(long operatorId, boolean isAllow) {
        return SsPlayerClan.ClanApplyResultAsk.newBuilder()
                .setClanId(getEntityId())
                .setClanSname(getOwner().getProp().getBase().getSname())
                .setOperatorId(operatorId)
                .setIsAllow(isAllow)
                .build();
    }

    private boolean checkCanDirectJoin(ClanEnterRequire clanEnterRequire, long applyPlayerId, boolean playerActiveJoin) {
        if (this.isClanMemberFull()) {
            // 先把军团人满的情况排除掉
            return false;
        }
        if (clanEnterRequire == ClanEnterRequire.NONE) {
            // 无需审核 人数未满 直接加入
            return true;
        }
        // NOTE(furson): 如果ClanEnterRequire的数量变多了，这里就会有问题
        if (playerActiveJoin) {
            // 军团人不满, 军团要求申请但玩家是自己申请进来的, 不能加入
            return false;
        }
        // NOTE(furson): 邀请本身是可能过期的，所以这里不是无脑返回true
        // 军团人不满, 军团要求申请但玩家是被邀请的, 也一定可以加入
        return getOwner().getInviteComponent().hasInvitePlayerNoThrow(applyPlayerId);
    }

    private boolean isClanMemberFull() {
        // 压测可以突破人数上限
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            return false;
        }
        ClanProp prop = this.getOwner().getProp();
        return prop.getNum() + getJoinPendingSize() >= prop.getNumMax();
    }

    private void playerJoined(ClanMemberProp memberProp, final long operationId) {
        // clan内部相关变动
        getOwner().getPropComponent().onPlayerJoin(memberProp);
        getOwner().getStaffComponent().doGrantStaff(memberProp, this.getOwner().getDataTemplateService().getClanNewbieStaff(), false, false, 0);
        LOGGER.info("clan: {} player: {} joined done, staff: {}", getEntityId(), memberProp.getId(), memberProp.getStaffId());
        // 加入完成，加入行为相关QLog
        Set<Long> hasPrivilegePlayerIds = getOwner().getStaffComponent().getHasSpecificPrivilegePlayerIds(ClanOperationType.COT_AUDIT);
        sendGuildStaffManageQLog(memberProp.getId(), "join_guild", operationId,
                hasPrivilegePlayerIds.size(),
                getOwner().getStaffComponent().getOnlineStaffCnt(hasPrivilegePlayerIds));

        // 新成员信息同步到scene上
        syncClanMemberToScene(false, true);
        // 新成员加入军团排行榜变化
        getOwner().getRankComponent().onNewPlayerJoinedClan(memberProp);
        // 发送欢迎邮件
        getOwner().getMsgComponent().sendNewbieMail(memberProp.getId());
        // 发送入盟通知
        getOwner().getMsgComponent().sendNewbieMarquee(memberProp.getCardHead().getName());
        // 重返联盟的玩家或许可以上贡献排行榜
        getOwner().getContributionComponent().onNewPlayerJoined(memberProp.getId());
    }


    /**
     * 对于玩家的入盟申请进行处理
     *
     * @param operatorId 操作玩家id
     * @param targetId   目标玩家id
     * @param isAllow    是否同意
     */
    public void handleProcessClanApply(ActorMsgEnvelope context, long operatorId, long targetId, boolean isAllow) {
        // 已经处理过，不在申请列表中
        if (!isClanApplier(targetId)) {
            PlayerClan.Player_NotifyClanApplyPlayers_NTF.Builder ntfBuilder = PlayerClan.Player_NotifyClanApplyPlayers_NTF.newBuilder();
            ntfBuilder.setApplyMembers(getOwner().getProp().getApplys().getCopyCsBuilder());
            sendCsMsgToMemberClient(operatorId, MsgType.PLAYER_NOTIFYCLANAPPLYPLAYERS_NTF, ntfBuilder.build());
            answerProcessClanApplyResult(context, ErrorCode.CLAN_OPERATION_INVALID);
            return;
        }
        if (isAllow && isClanMemberFull()) {
            answerProcessClanApplyResult(context, ErrorCode.CLAN_FULL);
            return;
        }

        removeApply(targetId);

        SsPlayerClan.ClanApplyResultAsk ask = buildClanApplyResultAsk(operatorId, isAllow);

        if (isAllow) {
            // 同意玩家加入
            // 先加入到pending队列中，防止并发，待player回onPlayerTryJoinedResult后从pending中移除
            addJoinPendingMember(targetId, SystemClock.nowOfSeconds());
            ownerActor().<SsPlayerClan.ClanApplyResultAns>askPlayer(ownerActor().getZoneId(), targetId, ask)
                    .onComplete((ans, err) -> {
                        // 先移除这个pending
                        this.removeJoinPendingMember(targetId);

                        if (err != null) {
                            if (err instanceof MigrateException) {
                                LOGGER.info("handleProcessClanApply allowed member join failed target is migrated to new zone={}. operatorId={} targetId={}",
                                        ((MigrateException) err).getZoneId(), operatorId, targetId);
                                answerProcessClanApplyResult(context, ErrorCode.CITY_PLAYER_DOES_NOT_EXIST);
                            } else {
                                WechatLog.error("handleProcessClanApply allowed member join error: {} {}", operatorId, targetId, err);
                                answerProcessClanApplyResult(context, ErrorCode.FAILED);
                            }
                        } else {
                            if (!ans.getIsSuccess()) {
                                answerProcessClanApplyResult(context, ErrorCode.CLAN_ALREADY_JOIN_OTHER);
                            } else {
                                answerProcessClanApplyResult(context, ErrorCode.OK);
                                LOGGER.info("clan: {} player: {} joined", getEntityId(), ans.getPlayerId());
                                ClanMemberProp memberProp = new ClanMemberProp();
                                memberProp.mergeFromSs(ans.getMember());
                                playerJoined(memberProp, operatorId);
                                // 记录申请加入的日志
                                getOwner().getLogComponent().logApproveApply(operatorId, memberProp.getCardHead().getName());
                            }
                        }
                    });
        } else {
            // 拒绝玩家加入
            getOwner().getMsgComponent().sendClanUnionApplicationDeniedMail(operatorId, targetId);
            sendGuildStaffManageQLog(targetId, "refuse_entry_guild", operatorId, 0, 0);
            ownerActor().tellPlayer(targetId, ask);
            answerProcessClanApplyResult(context, ErrorCode.OK);
        }
    }

    private void answerProcessClanApplyResult(ActorMsgEnvelope context, ErrorCode code) {
        ownerActor().answerWithContext(context, SsClanMember.ProcessClanApplyAns.newBuilder().setCode(code.getCodeId()).build());
    }

    public Code kickPlayerOut(long targetId, long operatorId) {
        Code preQuitCheck = preQuit(targetId);
        if (!ErrorCode.isOK(preQuitCheck)) {
            return preQuitCheck;
        }
        // 拿到被踢的玩家的名字
        String targetName = onMemberQuit(targetId, operatorId);
        postQuit(targetId, operatorId, targetName, false);
        return ErrorCode.OK.getCode();
    }

    public void playerTryQuit(long playerId, long operatorId) {
        Code preQuitCheck = preQuit(playerId);
        if (!ErrorCode.isOK(preQuitCheck)) {
            return;
        }
        // 拿到退出玩家的名字
        String playerName = onMemberQuit(playerId, operatorId);
        postQuit(playerId, operatorId, playerName, true);
    }

    public Code preQuit(long playerId) {
        ClanProp prop = getOwner().getProp();
        // 已经不在联盟中
        if (!isClanMember(playerId)) {
            return ErrorCode.CLAN_NOT_IN.getCode();
        }
        // 盟主在还有其他人的情况下无法退盟
        if (playerId == prop.getOwnerId() && prop.getNum() > 1) {
            return ErrorCode.CLAN_OWNER_CANT_QUIT.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * @param quitPlayerId 离开的玩家id
     * @param operatorId   操作者id，可能为0
     * @return 返回离开的玩家的名字
     */
    public String onMemberQuit(long quitPlayerId, long operatorId) {
        // 回收职位
        getOwner().getStaffComponent().doGrantStaff(getMemberNoThrow(quitPlayerId), getOwner().getDataTemplateService().getClanNewbieStaff(), false, false, 0);
        // 取消军团长申请
        getOwner().getStageComponent().cancelApplyOwnerWhenDissolving(quitPlayerId);
        // 军团prop清理
        String playerName = getMemberNoThrow(quitPlayerId).getCardHead().getName();
        getOwner().getPropComponent().onPlayerLeave(quitPlayerId);
        // 删除当前checkIn的Player
        this.checkInClients.remove(quitPlayerId);
        // 军团排行榜处理
        getOwner().getRankComponent().onMemberLeaveClan(quitPlayerId);
        // 删除所有军团帮助
        getOwner().getHelpComponent().delAllHelpsWhenQuitClan(quitPlayerId);
        // 发离开的消息
        getOwner().getChatComponent().sendLeaveClanMsg(playerName);
        // QLog
        sendGuildStaffManageQLog(quitPlayerId, "leave_guild", operatorId, 0, 0);
        return playerName;
    }

    public void postQuit(long targetId, long operatorId, String playerName, boolean isPlayerTryQuit) {
        if (isPlayerTryQuit) {
            // 玩家主动离开联盟，联盟人数不为0，发送离开消息
            if (getOwner().getMemberComponent().getMemberNum() != 0) {
                // 退出军团日志
                getOwner().getLogComponent().logQuitClan(playerName);
            }
        } else {
            LOGGER.info("kickOffClanMember clan={}, operatorId={}, targetId={}", getOwner(), operatorId, targetId);
            if (operatorId == systemOperatorId) {
                // 系统操作，不发送跑马灯且不记录日志
                return;
            }
            // 发送踢人跑马灯
            getOwner().getMsgComponent().sendKickOffMarquee(playerName);
            // 记录踢人日志
            getOwner().getLogComponent().logKickOff(operatorId, playerName);
        }
    }

    public void removeApply(long playerId) {
        // 删除申请
        getOwner().getProp().removeApplysV(playerId);
        // 尝试删除申请红点
        getOwner().getRedDotComponent().tryRemoveApplyRedDot();
    }

    public void updateClanMemberInfo(long playerId, StructClan.ClanMember pb) {
        LOGGER.debug("update clan member prop! clanId: {}, playerId: {}, pb:{}", this.getEntityId(), playerId, pb);

        ClanMemberProp clanMemberProp = getMemberNoThrow(playerId);
        if (clanMemberProp == null) {
            // 联盟踢人，会拉起player。在player的load过程会tell来updateClanMemberInfo，所以这里允许被调用
            LOGGER.warn("player not in clan! clanId: {}, playerId: {}", this.getEntityId(), playerId);
            return;
        }

        if (pb.hasComboat()) {
            long powerChange = pb.getComboat() - clanMemberProp.getComboat();
            if (powerChange != 0) {
                clanMemberProp.setComboat(pb.getComboat());
                getOwner().getPropComponent().refreshClanPower();
                // 战力发生变更、排行榜才发生变更
                getOwner().getRankComponent().onCombatChangeUpdateRank(playerId, pb.getComboat());
            }
        }
        if (pb.hasKillScore()) {
            long killScoreChange = pb.getKillScore() - clanMemberProp.getKillScore();
            if (killScoreChange != 0) {
                clanMemberProp.setKillScore(pb.getKillScore());
                getOwner().getPropComponent().refreshClanKillScore();
                // 击杀积分发生变更、排行榜才发生变更
                getOwner().getRankComponent().onKillChangeUpdateRank(playerId, pb.getKillScore());
            }
        }
        if (playerId == getOwner().getProp().getOwnerId()) {
            if (pb.hasCardHead()) {
                // 军团card更新：军团长铭牌发生变化实时同步
                getOwner().getPropComponent().updateClanCardCache(true);
            }
            if (pb.getCardHead().hasName()) {
                // 军团长名字变化了，同步给场景
                SsSceneClan.SyncSceneClanCmd.Builder cmd = SsSceneClan.SyncSceneClanCmd.newBuilder();
                cmd.getSceneClanBuilder().setOwnerName(pb.getCardHead().getName());
                getOwner().getPropComponent().syncToSceneClan(cmd);
            }
        }
        clanMemberProp.mergeChangeFromSs(pb);
    }

    /**
     * @return 获取所有军团内在线的玩家id集合
     */
    public Set<Long> getAllClanOnlinePlayerIds() {
        return getOwner().getProp().getMember().values().stream()
                .filter(ClanMemberProp::getIsOnline)
                .map(ClanMemberProp::getId)
                .collect(Collectors.toSet());
    }

    /**
     * @return 获取所有军团内在线且拥有指定权限的玩家集合
     */
    public Set<Long> getOnlinePlayerIdsWithPermCheck(ClanOperationType operation) {
        Set<Long> retPlayerIdsSet = new HashSet<>();
        for (ClanMemberProp prop : getOwner().getProp().getMember().values()) {
            if (prop.getIsOnline() && ClanPermissionUtils.hasPermissionNoThrow(operation, prop.getStaffId())) {
                retPlayerIdsSet.add(prop.getId());
            }
        }
        return retPlayerIdsSet;
    }

    /**
     * @return 获取所有军团内不在线的玩家id集合
     */
    public Set<Long> getAllClanOfflinePlayerIds() {
        Set<Long> offlinePlayerIds = Sets.newHashSet();
        for (ClanMemberProp prop : getOwner().getProp().getMember().values()) {
            if (!prop.getIsOnline()) {
                offlinePlayerIds.add(prop.getId());
            }
        }
        return offlinePlayerIds;
    }

    // ------------------------------------- 红点 ----------------------------------- //

    /**
     * 为军团成员添加红点
     *
     * @param playerId  玩家id
     * @param redDotKey 军团红点类型
     * @param cellId    区分红点来源的id
     */
    public void addRedDot(long playerId, CommonEnum.RedDotKey redDotKey, long cellId) {
        ClanMemberProp prop = getMember(playerId);
        RedDotDataProp dataProp = getOrCreateRedDot(prop, redDotKey);
        dataProp.addEmptyCells(cellId);
    }

    /**
     * 移除军团成员的某一类红点中的某一个
     *
     * @param playerId  玩家id
     * @param redDotKey 军团红点类型
     * @param cellId    区分红点来源的id
     */
    public void removeRedDot(long playerId, CommonEnum.RedDotKey redDotKey, long cellId) {
        ClanMemberProp prop = getMember(playerId);
        RedDotDataProp dataProp = prop.getRedDotMapV(redDotKey.getNumber());
        if (dataProp == null) {
            return;
        }
        dataProp.removeCellsV(cellId);
    }

    /**
     * 移除某一类军团红点
     *
     * @param playerId  玩家id
     * @param redDotKey 军团红点类型
     */
    public void removeAllRedDotByKey(long playerId, CommonEnum.RedDotKey redDotKey) {
        ClanMemberProp prop = getMember(playerId);
        prop.removeRedDotMapV(redDotKey.getNumber());
    }

    /**
     * 在军团成员prop上创建红点
     *
     * @param prop 军团成员prop数据
     * @param key  红点类型
     * @return 红点prop
     */
    private RedDotDataProp getOrCreateRedDot(ClanMemberProp prop, CommonEnum.RedDotKey key) {
        RedDotDataProp v = prop.getRedDotMapV(key.getNumber());
        if (v != null) {
            return v;
        }
        return prop.addEmptyRedDotMap(key.getNumber());
    }

    // ------------------------------------- 红点 ----------------------------------- //

    /**
     * 发送成员管理QLog
     */
    public void sendGuildStaffManageQLog(long playerId, String action, long operationId, int count,
                                         int onlineCount) {
        String reason = "ARTIFICIAL_GUILD_ACT";
        if (operationId == 0) {
            reason = "AUTO_GUILD_ACT";
        }
        QlogCncGuildStaffManage flow = new QlogCncGuildStaffManage();
        flow.setGameappid("");
        flow.setVOpenID("");
        flow.setAccountCreate_time("");
        flow.setAccountReg_time("");
        flow.setServer_type("");
        flow.setIZoneAreaID("");
        flow.setWorldId(String.valueOf(ServerContext.getWorldId()));
        flow.setGameSvrId(String.valueOf(getOwner().getZoneId()));
        flow.setNow_coordinate("");
        flow.setVRoleID(String.valueOf(playerId));
        flow.setVRoleName("");
        flow.setRole_num(0);
        flow.setRole_type("");
        flow.setRoleCreate_time("");
        flow.setILevel(0);
        flow.setIVipLevel(0);
        flow.setIRoleCE(0);
        flow.setHighestRole_power(0);
        flow.setPlayerFriendsNum(0);
        flow.setRecharge_sum(0);
        flow.setIGuildID(0);
        flow.setVGuildName("");
        flow.setGuildGrade("");
        flow.setGuildClass("");
        flow.setPlatID(0);
        flow.setGame_language("");
        flow.setVClientIP("");
        flow.setVClientIPV6("");
        flow.setKill_num(0);
        flow.setClientVersion("");
        flow.setMoney_config(0);
        flow.setTransferOrNot(0);

        flow.setDtEventTime(TimeUtils.now2String())
                .setIActType(action)
                .setReason(reason)
                .setOptionRoleId(String.valueOf(operationId))
                .setOptionRoleGrade(getOwner().getStaffComponent().getPlayerStaffId(operationId))
                .setGuildPowerPersonCount(count)
                .setAfterGuildGrade(getOwner().getStaffComponent().getPlayerStaffId(playerId))
                .setObjectGuildId(String.valueOf(getEntityId()))
                .setGuildPowerPersonCountOnline(onlineCount)
                .sendToQlog();
    }

    /**
     * 结算下联盟个人资源产出
     */
    public void settleAllPlayerResources() {
        for (ClanMemberProp prop : getOwner().getProp().getMember().values()) {
            settlePlayerResources(prop);
        }
    }

    /**
     * 结算个人资源产出
     *
     * @param playerId playerId
     * @return 如果player不存在，返回null；存在则返回结算结果。
     */
    public List<Pair<Integer, Integer>> settlePlayerResources(final long playerId) {
        ClanMemberProp memberV = getMemberNoThrow(playerId);
        if (memberV == null) {
            return null;
        }
        this.settlePlayerResources(memberV);
        List<Pair<Integer, Integer>> resources = new ArrayList<>(memberV.getResourcesSize());
        for (CurrencyProp prop : memberV.getResources().values()) {
            resources.add(Pair.of(prop.getType(), (int) prop.getCount()));
        }
        return resources;
    }

    private void settlePlayerResources(ClanMemberProp prop) {
        long now = SystemClock.now();
        long refreshMs = prop.getResLastCalTsMs();
        if (refreshMs > now) {
            return;
        }
        prop.setResLastCalTsMs(now);
        long deltaMs = now - refreshMs;
        for (Map.Entry<Integer, ClanResourceType> entry : AdditionConstants.PLAYER_RESOURCES_EFFECT.entrySet()) {
            long addition = getOwner().getAddComponent().getAddition(entry.getKey());
            long addValue = (addition * deltaMs / Duration.ofHours(1).toMillis());
            if (addValue < 0) {
                LOGGER.error("{} add addPlayerResources: {} error. type: {} addition:{} deltaMs:{}",
                        getOwner(), prop.getId(), entry.getValue(), addition, deltaMs);
                continue;
            }
            addResources(prop, entry.getValue(), addValue);
        }
    }

    /**
     * 增加个人资源
     */
    private void addResources(ClanMemberProp prop, ClanResourceType type, long addV) {
        if (addV == 0) {
            LOGGER.info("{} addPlayerResources :{}. type:{} add:{}", getOwner(), prop.getId(), type, addV);
            return;
        }
        if (!prop.getResources().containsKey(type.getNumber())) {
            prop.getResources().addEmptyValue(type.getNumber()).setCount(addV);
            LOGGER.info("{} addPlayerResources :{}. type:{} old:0 add:{}", getOwner(), prop.getId(), type, addV);
            return;
        }
        long count = prop.getResources().get(type.getNumber()).getCount();
        int limit = ResHolder.getResService(ClanDataTemplateService.class).getClanPlayerResourceLimit(type.getNumber());
        long newCount = count + addV;
        if (limit != 0) {
            newCount = Math.min(newCount, limit);
        }
        prop.getResources().get(type.getNumber()).setCount(newCount);
        LOGGER.info("{} addPlayerResources :{}. type:{} old:{} add:{} new: {}", getOwner(), prop.getId(), type, count, addV, newCount);
    }

    /**
     * 退盟的时候 把没领取的资源发到player存储
     */
    public void addResourcesToPlayer(ClanMemberProp prop) {
        settlePlayerResources(prop);
        SsPlayerClan.OnAddClanPowerResourceCmd.Builder tell = SsPlayerClan.OnAddClanPowerResourceCmd.newBuilder();
        for (CurrencyProp p : prop.getResources().values()) {
            tell.putResource(p.getType(), (int) p.getCount());
        }
        ownerActor().tellPlayer(prop.getId(), tell.build());
    }

    public void syncClanMemberToScene(boolean isOwnerChange, boolean isPowerChange) {
        // 通知大世界成员变更
        ClanProp prop = getOwner().getProp();
        SsSceneClan.SyncSceneClanCmd.Builder tell = SsSceneClan.SyncSceneClanCmd.newBuilder().setClanId(getEntityId());
        tell.setMemberNum(getOwner().getMemberComponent().getMemberNum());
        if (isOwnerChange) {
            tell.getSceneClanBuilder().setOwnerId(getOwnerId())
                    .setOwnerName(getMemberNameById(getOwnerId()));
            // 军团card更新：军团长换人，实时同步
            getOwner().getPropComponent().updateClanCardCache(true);
        }
        if (isPowerChange) {
            tell.getSceneClanBuilder().setPower(prop.getCombat());
        }
        ownerActor().tellBigScene(tell.build());
    }

    public void syncClanInfoToPlayer(long playerId, int oldStaffId) {
        // 通知player变更
        ClanMemberProp memberProp = getMemberNoThrow(playerId);
        if (memberProp == null) {
            LOGGER.error("{} not in clan {} member but try sync to player", playerId, getOwner());
            return;
        }
        SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.Builder tell = SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.newBuilder();
        int newStaffId = memberProp.getStaffId();
        // 申请红点同步
        boolean canAuditBefore = ClanPermissionUtils.hasPermissionNoThrow(ClanOperationType.COT_AUDIT, oldStaffId);
        boolean canAuditAfter = ClanPermissionUtils.hasPermissionNoThrow(ClanOperationType.COT_AUDIT, newStaffId);
        // 仅当前后权限有变化，并且军团申请列表中有人才发
        if ((canAuditBefore != canAuditAfter) && !getOwner().getProp().getApplys().isEmpty()) {
            tell.setRedDotData(getOwner().getRedDotComponent().getApplyRedDotData(canAuditAfter).build());
        }
        ownerActor().tellPlayer(playerId, tell.setStaffId(newStaffId).build());
        LOGGER.info("syncClanInfoToPlayer {} {}", playerId, tell);
    }

    /**
     * 填充玩家登录军团后获取的红点信息
     *
     * @param redDotInfoBuilder 红点信息的构造器
     */
    public void fillCheckInClanMemberRedDotInfo(long playerId, CommonMsg.CheckInRedDotInfo.Builder redDotInfoBuilder) {
        // 军团成员红点设置，目前仅有军团商店红点
        ClanMemberProp prop = getMember(playerId);
        for (RedDotDataProp redDotDataProp : prop.getRedDotMap().values()) {
            redDotInfoBuilder.getRedDotMapBuilder().putDatas(redDotDataProp.getKey(), redDotDataProp.getCopySsBuilder().build());
        }
        // 校验红点数据是否正确
        if (getOwner().getStoreComponent().getClanStoreRemainItemNum() != prop.getRedDotMapSize()) {
            LOGGER.warn("fetch check failed: clan store remain num is {}, where member prop has {}",
                    getOwner().getStoreComponent().getClanStoreRemainItemNum(), prop.getRedDotMapSize());
        }
        // 玩家获取后删除所有军团成员红点信息
        prop.clearRedDotMap();
    }

    /**
     * @return 返回是否有军团成员在线
     */
    public boolean hasClanMemberOnline() {
        return !checkInClients.isEmpty();
    }


    public List<Long> onlinePlayerIds() {
        return Lists.newArrayList(checkInClients.keySet());
    }

    public boolean isOnline(long playerId) {
        return checkInClients.containsKey(playerId);
    }

    public boolean isClanApplier(long playerId) {
        return getOwner().getProp().getApplys().containsKey(playerId);
    }

    public boolean isClanMember(long playerId) {
        return getOwner().getProp().getMember().containsKey(playerId);
    }

    public ClanMemberProp getMember(long playerId) {
        ClanMemberProp member = getOwner().getProp().getMemberV(playerId);
        if (member == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        return member;
    }

    @Nullable
    public ClanMemberProp getMemberNoThrow(long playerId) {
        return getOwner().getProp().getMemberV(playerId);
    }

    public ClanMemberProp getClanOwner() {
        return getMemberNoThrow(getOwnerId());
    }

    /**
     * 返回军团所有者id
     *
     * @return 是或者否
     */
    @Override
    public long getOwnerId() {
        return this.getOwner().getProp().getOwnerId();
    }

    /**
     * @param playerId 玩家id
     * @return 根据玩家id返回玩家铭牌
     */
    @Nullable
    public PlayerCardHeadProp getCardHeadPropById(long playerId) {
        ClanMemberProp memberProp = getMemberNoThrow(playerId);
        if (memberProp != null) {
            return memberProp.getCardHead();
        } else {
            LOGGER.error("try load {}'s card head when player isn't in clan", playerId);
            return null;
        }
    }

    public Struct.PlayerCardHead getMemberCardHead(long playerId) {
        return getMember(playerId).getCardHead().getCopySsBuilder().build();
    }

    /**
     * @param playerId 玩家id
     * @return 根据玩家id返回玩家名字
     */
    public String getMemberNameById(long playerId) {
        ClanMemberProp memberProp = getMemberNoThrow(playerId);
        if (memberProp != null) {
            return memberProp.getCardHead().getName();
        } else {
            LOGGER.error("try load {}'s name when player isn't in clan", playerId);
            return "";
        }
    }

    /**
     * @return 返回军团长名字
     */
    public String getClanOwnerName() {
        return getMemberNameById(getOwnerId());
    }

    /**
     * 获取当前人数
     *
     * @return 当前人数
     */
    public int getMemberNum() {
        return this.getOwner().getProp().getNum();
    }

    /**
     * 获取所有成员id
     *
     * @return 返回成员id列表
     */
    public Set<Long> getAllMemberIds() {
        return this.getOwner().getProp().getMember().keySet();
    }

    /**
     * 获取所有军团的成员信息
     */
    public StructClan.Int64ClanMemberMap getAllMembers() {
        return getOwner().getProp().getMember().getCopySsBuilder().build();
    }

    /**
     * 获取所有军团申请成员信息
     */
    public StructClan.Int64ClanMemberMap getAllAppliers() {
        return getOwner().getProp().getApplys().getCopySsBuilder().build();
    }

    public SsClanAttr.FetchClanNumTipAns.Builder getNumTip() {
        SsClanAttr.FetchClanNumTipAns.Builder builder = SsClanAttr.FetchClanNumTipAns.newBuilder();
        builder.putAllNumMaxAdditionItem(getOwner().getAddComponent()
                .getAdditionSourceMapByAdditionId(CommonEnum.BuffEffectType.ET_ADD_CLAN_MEMBER_NUM.getNumber()));
        return builder;
    }
}

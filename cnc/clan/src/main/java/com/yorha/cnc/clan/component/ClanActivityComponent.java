package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ClanActivityModelProp;
import com.yorha.game.gen.prop.ClanScoreRankProp;
import com.yorha.proto.CommonEnum.ActivityUnitType;
import com.yorha.proto.SsClanActivity;
import res.template.ActivityScoreRankTemplate;
import res.template.ActivityTemplate;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 活动  比较复杂的请另起component
 *
 * <AUTHOR>
 */
public class ClanActivityComponent extends ClanComponent {

    private final Map<Integer, ActorTimer> updateScheduleRank = new HashMap<>();

    public ClanActivityComponent(ClanEntity owner) {
        super(owner);
    }


    private ClanActivityModelProp getProp() {
        return getOwner().getProp().getActModel();
    }

    public void onAddActScore(ActivityUnitType type, long actId, int addScore) {
        final int activityId = (int) (actId >> 32);
        switch (type) {
            case AUT_SCORE_CLAN_RANK:
                ClanScoreRankProp scoreRankV = getProp().getScoreRankV(activityId);
                if (scoreRankV == null) {
                    getProp().getScoreRank().addEmptyValue(activityId).setScore(0).setCurActId(actId);
                    scoreRankV = getProp().getScoreRankV(activityId);
                } else if (scoreRankV.getCurActId() != actId) {
                    scoreRankV.setScore(0).setCurActId(actId);
                }
                if (addScore == 0) {
                    return;
                }
                scoreRankV.setScore(scoreRankV.getScore() + addScore);
                // 更新到rank去
                if (!updateScheduleRank.containsKey(activityId)) {
                    updateScheduleRank.put(activityId, ownerActor().addTimer(actId,
                            TimerReasonType.CLAN_SCORE_RANK_UPDATE, () -> updateToScoreRank(activityId),
                            RandomUtils.nextInt(3, 5),
                            TimeUnit.SECONDS
                    ));
                }
                return;
            default:
                throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
        }
    }


    public void updateToScoreRank(int activityId) {
        updateScheduleRank.remove(activityId);
        ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, activityId);
        final int scoreRankConfId = activityTemplate.getScoreRankId();
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, scoreRankConfId);
        final int rankId = scoreRankTemplate.getRankId();

        getOwner().getRankComponent().updateZoneRanking(rankId, getProp().getScoreRankV(activityId).getScore());

    }

    public SsClanActivity.FetchActivityDataAns fetchActivityData(ActivityUnitType type) {
        switch (type) {
            default:
                throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
        }
    }

    @Override
    public void onDestroy() {
        if (!updateScheduleRank.isEmpty()) {
            for (int activityId : new HashSet<>(updateScheduleRank.keySet())) {
                updateToScoreRank(activityId);
            }
        }
    }
}

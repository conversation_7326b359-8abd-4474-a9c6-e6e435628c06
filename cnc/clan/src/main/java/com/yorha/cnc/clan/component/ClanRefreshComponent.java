package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ClanRefreshModelProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * 日刷周刷
 */
public class ClanRefreshComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanRefreshComponent.class);
    private long nextWeeklyRefreshTsMs;
    private ActorTimer dayTask;
    private ActorTimer weekTask;

    public ClanRefreshComponent(ClanEntity owner) {
        super(owner);
    }

    public ClanRefreshModelProp prop() {
        return getOwner().getProp().getRefreshModel();
    }

    @Override
    public void postInit() {
        ClanRefreshModelProp prop = prop();
        if (TimeUtils.isSameDayWithNow(prop.getDayRefreshTsMs())) {
            addDayRefreshTimer();
        } else {
            dispatchDayRefresh();
        }
        if (TimeUtils.isSameWeekWithNow(prop.getWeekRefreshTsMs())) {
            addWeekRefreshTimer();
        } else {
            dispatchWeekRefresh();
        }
    }

    @Override
    public void onDestroy() {
        // 保底清理
        if (dayTask != null) {
            dayTask.cancel();
            dayTask = null;
        }
        if (weekTask != null) {
            weekTask.cancel();
            weekTask = null;
        }
    }

    private void addDayRefreshTimer() {
        if (dayTask != null) {
            // timer 没取消！
            LOGGER.error("{} addDayRefreshTimer error, timer not cancel", getOwner());
            return;
        }
        long initialDelay = TimeUtils.getNextDayDurMs(SystemClock.now()) + RandomUtils.nextInt(5000);
        dayTask = ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_DAY_REFRESH,
                this::dispatchDayRefresh,
                initialDelay, TimeUnit.MILLISECONDS);
        LOGGER.info("{} addDayRefreshTimer, now={}, initialDelay={}", getOwner(), SystemClock.now(), initialDelay);
    }

    private void addWeekRefreshTimer() {
        if (weekTask != null) {
            // timer 没取消！
            LOGGER.error("{} addWeekRefreshTimer error, timer not cancel", getOwner());
            return;
        }
        long now = SystemClock.now();
        long nextWeekDurMs = TimeUtils.getNextWeekDurMs(SystemClock.now()) + RandomUtils.nextInt(5000);
        weekTask = ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_WEEK_REFRESH,
                this::dispatchWeekRefresh,
                nextWeekDurMs, TimeUnit.MILLISECONDS);
        this.nextWeeklyRefreshTsMs = now + nextWeekDurMs;
        LOGGER.info("{} addWeekRefreshTimer, now={}, nextWeekDurMs={}", getOwner(), now, nextWeekDurMs);
    }

    private void dispatchDayRefresh() {
        dayTask = null;
        prop().setDayRefreshTsMs(SystemClock.now());
        addDayRefreshTimer();

        getOwner().getContributionComponent().settleDailyRanking();
    }

    private void dispatchWeekRefresh() {
        weekTask = null;
        prop().setWeekRefreshTsMs(SystemClock.now());
        addWeekRefreshTimer();

        getOwner().getContributionComponent().settleWeeklyRanking();
        getOwner().getRankComponent().dispatchWeekRefresh();
    }

    public long getNextWeeklyRefreshTsMs() {
        return nextWeeklyRefreshTsMs;
    }
}

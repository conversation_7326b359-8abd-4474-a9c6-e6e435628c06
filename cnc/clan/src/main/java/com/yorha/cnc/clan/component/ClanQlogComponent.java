package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.enums.qlog.clan.ClanGiftAction;
import com.yorha.common.enums.qlog.clan.ClanGiftType;
import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.json.clan.ClanCurrency;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.SingleClanResourceInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneClan;
import qlog.flow.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ClanQlogComponent extends ClanComponent implements QlogClanFlowInterface {

    public ClanQlogComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void onLoad() {
        // 给scene上发消息，告诉scene，clan被拉起了，需要被标记为活跃
        syncEntityStatusToScene(false);
    }

    @Override
    public void onDestroy() {
        if (!getOwner().getStageComponent().isAlreadyDisband()) {
            //  只有不解散的军团需要告知scene，需要被标记为不活跃，解散的军团都删除了
            syncEntityStatusToScene(true);
            // 只有不解散的军团会需要打不活跃的qlog
            qlogSnapShot(0);
        }
    }

    @Override
    public void onDissolution() {
        qlogSnapShot(2);
    }

    @Override
    public String getServer_type() {
        return "";
    }

    @Override
    public String getIZoneAreaID() {
        return String.valueOf(this.getOwner().ownerActor().getZoneId());
    }

    @Override
    public String getGuildCreate_time() {
        return TimeUtils.timeStampMs2String(getOwner().getProp().getCreateTime());
    }

    @Override
    public String getGuild_id() {
        return Long.toString(getOwner().getProp().getId());
    }

    @Override
    public String getGuild_name() {
        return getOwner().getProp().getBase().getName();
    }

    @Override
    public String getGuildShortName() {
        return getOwner().getProp().getBase().getSname();
    }

    @Override
    public int getGuild_level() {
        return 0;
    }

    @Override
    public int getGuild_population() {
        return getOwner().getProp().getNum();
    }

    @Override
    public int getGuild_online() {
        return 0;
    }

    @Override
    public int getGuildGiftLevel() {
        return getOwner().getProp().getGiftModel().getGiftLevel();
    }

    @Override
    public long getGuild_power() {
        return getOwner().getProp().getCombat();
    }

    @Override
    public long getGuild_influence() {
        return getOwner().getTerritoryComponent().getTerritoryPower();
    }

    @Override
    public String getGuildLeader_id() {
        return Long.toString(getOwner().getProp().getOwnerId());
    }

    @Override
    public String getAccountId() {
        return "guild_" + ServerContext.getServerInfo().getWorldId() + "_" + getIZoneAreaID();
    }

    /**
     * 在player上拼出一个ServerHead，小部分内容缺失
     */
    public QlogServerFlowInterface asServerHead() {
        return new QlogServerFlowInterface() {
            @Override
            public String getServerType() {
                return "";
            }

            @Override
            public int getIZoneAreaID() {
                return getOwner().getZoneId();
            }

            @Override
            public String getServerOpenTime() {
                return TimeUtils.timeStampMs2String(ZoneContext.getServerOpenTsMs());
            }

            @Override
            public long getServerOpenTimeFar() {
                return TimeUtils.ms2Second((SystemClock.now() - ZoneContext.getServerOpenTsMs()));
            }

            @Override
            public int getServerOnline() {
                return 0;
            }

            @Override
            public String getServerCondition() {
                return "";
            }

            @Override
            public int getServerMilestoneStage() {
                return 0;
            }

            @Override
            public String getAccountId() {
                return "server_" + ServerContext.getServerInfo().getWorldId();
            }
        };
    }

    public void qLogCreate() {
        QlogCncGuildManage.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setIActType("create_guild")
                .setAfterContent("")
                .setAfterEntryStandard(0)
                .sendToQlog();
    }

    public void qLogDismiss() {
        QlogCncGuildManage.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setIActType("dismiss_guild")
                .setAfterContent("")
                .setAfterEntryStandard(0)
                .sendToQlog();
    }

    public void qLogChangeName() {
        final String name = this.getOwner().getProp().getBase().getName();
        QlogCncGuildManage.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setIActType("change_guild_name")
                .setAfterContent(name)
                .setAfterEntryStandard(0)
                .sendToQlog();
    }

    public void qLogChangeSName() {
        final String sName = this.getOwner().getProp().getBase().getSname();
        QlogCncGuildManage.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setIActType("change_guild_short_name")
                .setAfterContent(sName)
                .setAfterEntryStandard(0)
                .sendToQlog();
    }


    public void qLogClanEntry() {
        final CommonEnum.ClanEnterRequire require = this.getOwner().getProp().getBase().getRequire();
        QlogCncGuildManage.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setIActType("change_guild_entry")
                .setAfterContent("")
                .setAfterEntryStandard(require == CommonEnum.ClanEnterRequire.VERIFY ? 1 : 0)
                .sendToQlog();

    }

    /**
     * 记录军团进货的流水
     *
     * @param operatorId    操作人的playerId
     * @param operatorGrade 操作人的职级
     * @param iGoodsType    货物类型，见配置表
     * @param iGoodsId      进货的道具id
     * @param iCount        进货的道具数量
     */
    public void qLogClanRestock(long operatorId, int operatorGrade, int iGoodsType, String iGoodsId, int iCount) {
        QlogCncGuildRestock.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction("guild_restock")
                .setOptionRoleId(String.valueOf(operatorId))
                .setOptionRoleGrade(operatorGrade)
                .setIGoodsType(iGoodsType)
                .setIGoodsId(iGoodsId)
                .setICount(iCount)
                .sendToQlog();
    }

    /**
     * 记录军团积分的流水
     *
     * @param action      行为类型，包括:
     *                    guild_restock 军团商店进货
     *                    start_build_guildbuilding 点击建造军团建筑
     *                    start_guild_tech 研究军团科技
     *                    guild_tech_donate 科技捐献
     *                    guild_help 军团帮助
     *                    participate_build 军团成员参与建造军团建筑
     *                    use_item 使用道具
     *                    gm gm命令
     * @param beforeCount 行为前积分数量
     * @param afterCount  行为后积分数量
     * @param iCount      变化的积分数量
     * @param addOrReduce 变化方向：0，积分增加；1，积分减少
     */
    public void qLogClanScore(String action, long beforeCount, long afterCount, long iCount, int addOrReduce, long targetId) {
        QlogCncGuildScore.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setBeforeCount(beforeCount)
                .setAfterCount(afterCount)
                .setICount(iCount)
                .setAddOrReduce(addOrReduce)
                .setTargetId(targetId)
                .sendToQlog();
    }

    /**
     * 记录军团资源的流水
     *
     * @param resId       资源id
     * @param beforeCount 行为前资源数量
     * @param iCount      变化的资源数量
     * @param afterCount  行为后资源数量
     * @param addOrReduce 变化方向：0，资源增加；1，资源减少
     * @param reason      变化原因，包括：
     *                    start_build_guildbuilding 建造军团建筑
     *                    start_guild_tech 军团科技研究
     * @param subReason   变化子原因，包括：
     *                    start_build_guildbuilding -> 建筑id
     *                    start_guild_tech -> 科技id
     */
    public void qlogClanRes(int resId, long beforeCount, long afterCount, long iCount, int addOrReduce, String reason,
                            String subReason) {
        QlogCncGuildRes.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setResId(resId)
                .setBeforeCount(beforeCount)
                .setAfterCount(afterCount)
                .setICount(iCount)
                .setAddOrReduce(addOrReduce)
                .setReason(reason)
                .setSubReason(subReason)
                .sendToQlog();
    }

    /**
     * 发送军团礼物流水
     *
     * @param action   行为（购买礼包、击杀野怪、领取奖励、奖励过期）
     * @param playerId 玩家id
     * @param targetId 目标id（根据行为：购买礼包=礼包id；击杀野怪=野怪id；领取奖励or奖励过期=礼物id）
     */
    public void qlogClanGift(final ClanGiftAction action, long playerId, long targetId) {
        QlogCncGuildGift.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action.getType())
                .setRoleId(String.valueOf(playerId))
                .setGiftType(ClanGiftType.NORMAL_GIFT.getType())
                .setTargetId(String.valueOf(targetId))
                .sendToQlog();
    }

    /**
     * 军团快照
     *
     * @param clanStatus 0 淘汰， 1 正常， 2 解散
     */
    public void qlogSnapShot(int clanStatus) {
        List<ClanCurrency> currencyList = new ArrayList<>();
        for (Map.Entry<Integer, SingleClanResourceInfoProp> entry : getOwner().getResourcesComponent().getProp().getResourceInfos().entrySet()) {
            ClanCurrency currency = new ClanCurrency();
            currency.setGuildresType(entry.getKey());
            currency.setIcount(entry.getValue().getCount());
            currencyList.add(currency);
        }
        QlogCncGuildSnapshot.init(getOwner().getClanQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setExpireOrNot(clanStatus)
                .setGuildScore(getOwner().getResourcesComponent().getClanScore())
                .setGuildResConfig(JsonUtils.toJsonString(currencyList))
                .setGuildCityList(QlogUtils.transCollection2ArrayString(getOwner().getTerritoryComponent().getOwnPartIdCollection()))
                .setGuildFlag(getOwner().getProp().getBase().getNationFlagId())
                .sendToQlog();
    }

    /**
     * 同步军团实体状态到scene
     *
     * @param isActorDestroy 军团实体是否销毁
     */
    private void syncEntityStatusToScene(boolean isActorDestroy) {
        SsSceneClan.SyncClanActorStatusCmd.Builder builder = SsSceneClan.SyncClanActorStatusCmd.newBuilder();
        builder.setClanId(getEntityId()).setIsActorDestroy(isActorDestroy);
        ownerActor().tellBigScene(builder.build());
    }
}

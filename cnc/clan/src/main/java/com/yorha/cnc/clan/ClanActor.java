package com.yorha.cnc.clan;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.clan.chat.ClanChatServiceImpl;
import com.yorha.cnc.clan.component.ClanComponent;
import com.yorha.cnc.clan.rank.ClanRankMgrServiceImpl;
import com.yorha.common.actor.*;
import com.yorha.common.actor.node.IClanActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.utils.RandomUtils;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.Clan;
import com.yorha.proto.SsSceneClan;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 联盟actor
 *
 * <AUTHOR>
 */
public class ClanActor extends GameActorWithCall implements ClanServices, IClanActor, IActorWithTimer {
    private static final Logger LOGGER = LogManager.getLogger(ClanActor.class);

    private final long clanId;
    private ClanEntity clanEntity;
    private final ClanAttrService attrService;
    private final ClanBaseServiceImpl baseService;
    private final ClanMemberServiceImpl memberService;
    private final ClanRankMgrService rankMgrService;
    private final ClanGiftService giftService;
    private final ClanChatService chatService;
    private final ClanHelpService helpService;
    private final ClanTechService techService;
    private final ClanStoreService storeService;
    private final ClanTerritoryService territoryService;
    private final ClanActivityService clanActivityService;
    private boolean isDissolved;

    public ClanActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.clanId = Long.parseLong(self.getActorId());
        this.attrService = new ClanAttrServiceImpl(this);
        this.baseService = new ClanBaseServiceImpl(this);
        this.memberService = new ClanMemberServiceImpl(this);
        this.rankMgrService = new ClanRankMgrServiceImpl(this);
        this.giftService = new ClanGiftServiceImpl(this);
        this.chatService = new ClanChatServiceImpl(this);
        this.helpService = new ClanHelpServiceImpl(this);
        this.techService = new ClanTechServiceImpl(this);
        this.storeService = new ClanStoreServiceImpl(this);
        this.territoryService = new ClanTerritoryServiceImpl(this);
        this.clanActivityService = new ClanActivityServiceImpl(this);
        // 300秒淘汰，NOTE(furson): 300只是个经验值，没有数据支撑
        // 做个偏移量，防止烂代码导致的批量拉起所有clan后，所有clan集体销毁的二次波峰
        int time = 300 + RandomUtils.nextInt(60);
        LOGGER.info("ClanActor setReceiveTimeout {} {}", clanId, time);
        setReceiveTimeout(time);
    }

    @Nullable
    @Override
    public ActorTimer addTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(String.valueOf(prefix), timerReasonType, runnable, initialDelay, unit);
    }

    @Nullable
    @Override
    public ActorTimer addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    /**
     * 创建联盟
     */
    public void createClan(ClanProp prop) {
        LOGGER.info("createClan id: {}, owner: {}", prop.getId(), prop.getOwnerId());
        // 创建ClanEntity
        ClanEntity clanEntity = new ClanEntity(this, prop, null, true);
        this.clanEntity = clanEntity;
        clanEntity.callAllComponentsSafe(ClanComponent::onCreate);
        // 通知大世界地图联盟成立
        SsSceneClan.OnClanCreatedCmd.Builder tell = SsSceneClan.OnClanCreatedCmd.newBuilder()
                .setClanId(prop.getId());
        // 创建军团时sceneclan上增加clanId的同步
        tell.getSceneClanBuilder().setClanId(prop.getId())
                .setClanName(prop.getBase().getName())
                .setClanSimpleName(prop.getBase().getSname())
                .setTerritoryColor(prop.getBase().getTerritoryColor())
                .setFlagShading(prop.getBase().getFlagShading())
                .setNationFlagId(prop.getBase().getNationFlagId())
                .setFlagSign(prop.getBase().getFlagSign())
                .setFlagColor(prop.getBase().getFlagColor())
                .setOwnerId(prop.getOwnerId())
                .setPower(prop.getCombat())
                .setOwnerName(clanEntity.getMemberComponent().getClanOwnerName());

        clanEntity.ownerActor().tellBigScene(tell.build());
        clanEntity.getClanQlogComponent().qLogCreate();
        // 军团card更新：创建时实时同步
        clanEntity.getPropComponent().updateClanCardCache(true);
    }

    public ClanEntity getClanEntity() {
        if (clanEntity == null) {
            LOGGER.error("getClanEntity failed, clanId is {}", getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        return clanEntity;
    }

    /**
     * clanEntity仍可能为空（解散），注意处理
     */
    @Nullable
    public ClanEntity getOrLoadClanEntity() {
        if (clanEntity == null) {
            clanEntity = tryLoadClanEntity(false);
        }
        // 此时clanEntity也有可能是null
        return clanEntity;
    }

    public ClanEntity getOrLoadClanEntityDissAsNil() {
        if (clanEntity == null) {
            clanEntity = tryLoadClanEntity(true);
        }
        // 此时clanEntity也有可能是null
        return clanEntity;
    }

    /**
     * 获取军团实体
     * 加入淘汰以后，ss协议不应该再使用这个方法，而应该使用getOrLoadClanEntity或getOrLoadClanEntityDissAsNil
     */
    public ClanEntity getClanEntityOrNull() {
        return clanEntity;
    }

    private ClanEntity tryLoadClanEntity(boolean dissolutionAsNull) {
        if (isDissolved) {
            LOGGER.warn("tryLoadClanEntity but clan is dissolved");
            return null;
        }
        final TcaplusDb.ClanTable.Builder req = TcaplusDb.ClanTable.newBuilder()
                .setZoneId(this.getZoneId())
                .setClanId(clanId);
        try {
            GetResult<TcaplusDb.ClanTable.Builder> ans = this.callGameDb(new SelectUniqueAsk<>(req));
            if (DbUtil.isRecordNotExist(ans.getCode())) {
                throw new GeminiException(ErrorCode.CLAN_NOT_EXIST, StringUtils.format("tryLoadClanEntity fail! code={}", ans.getCode()));
            } else if (!DbUtil.isOk(ans.getCode())) {
                throw new GeminiException("tryLoadClanEntity fail! code={}", ans.getCode());
            }
            TcaplusDb.ClanTable.Builder res = ans.value;
            ClanEntity clanEntity = afterLoadClanFromDb(Pair.of(res.getFullAttr(), res.getChangedAttr()));
            // 完整拉起后尝试解散军团
            tryDissolution(clanEntity);
            if (clanEntity.isDestroy()) {
                if (dissolutionAsNull) {
                    LOGGER.info("success destroy after dissolution, would return null");
                    return null;
                } else {
                    LOGGER.info("success destroy after dissolution, would throw exception");
                    throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
                }
            }
            return clanEntity;
        } catch (Exception e) {
            if (GeminiException.isLogicException(e)) {
                LOGGER.warn("tryLoadClanEntity req={} fail!", req, e);
            } else {
                LOGGER.error("tryLoadClanEntity req={} fail!", req, e);
            }
        }
        return null;
    }

    public void gmTryDissoluteClanEntity() {
        if (clanEntity == null) {
            LOGGER.info("ClanActor gmTryDissoluteClanEntity clanEntity is null, wrong use of gm command");
            return;
        }
        // 强制执行解散逻辑
        clanEntity.getStageComponent().realDissolve();
    }

    public void setClanEntityNull() {
        this.clanEntity = null;
    }

    private ClanEntity afterLoadClanFromDb(Pair<Clan.ClanEntity, Clan.ClanEntity> clanDb) {
        if (clanDb == null) {
            return null;
        }
        ClanProp prop = ClanProp.of(clanDb.getLeft(), clanDb.getRight());
        ClanEntity clanEntity = new ClanEntity(this, prop, clanDb.getRight(), false);
        clanEntity.callAllComponentsSafe(ClanComponent::onLoad);
        clanEntity.callAllComponentsSafe(ClanComponent::postLoad);
        return clanEntity;
    }

    /**
     * 尝试解散军团
     *
     * @param clanEntity 军团实体
     */
    private void tryDissolution(ClanEntity clanEntity) {
        clanEntity.getStageComponent().tryDissolution();
    }

    private boolean isClanStillSaveDb() {
        ClanEntity clanEntity = getClanEntityOrNull();
        if (clanEntity == null) {
            return false;
        }
        ClanProp prop = clanEntity.getProp();
        if (prop == null) {
            return false;
        }
        PropertyChangeListener listener = prop.getListener();
        if (listener != null) {
            // 判断prop是否还没落库完成
            if (listener.isInTaskQueue()) {
                return true;
            }
        }
        // 判断prop是否还没收脏完成
        return prop.hasAnyMark();
    }

    private boolean hasClanMemberOnline() {
        ClanEntity clanEntity = getClanEntityOrNull();
        if (clanEntity == null) {
            return false;
        }
        return clanEntity.getMemberComponent().hasClanMemberOnline();
    }

    private void stopPropChangeListener() {
        if (getClanEntityOrNull() != null && getClanEntityOrNull().getProp() != null) {
            PropertyChangeListener clanPropListener = getClanEntity().getProp().getListener();
            if (clanPropListener != null) {
                ((CanStopPropertyChangeListener) clanPropListener).stop();
            }
        }
    }

    private void entityDestroy() {
        if (getClanEntityOrNull() != null) {
            getClanEntity().deleteObj();
        }
    }

    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
        // 避免各component onDestroy时触发propListener
        stopPropChangeListener();

        entityDestroy();
    }

    @Override
    protected boolean canDestroy() {
        if (hasClanMemberOnline()) {
            return false;
        }
        if (isClanStillSaveDb()) {
            return false;
        }
        return super.canDestroy();
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    public long getClanId() {
        return clanId;
    }

    public void tellBigScene(GeneratedMessageV3 msg) {
        tellBigScene(this.getBigSceneId(), msg);
    }

    @Override
    public ClanAttrService getClanAttrService() {
        return attrService;
    }

    @Override
    public ClanBaseService getClanBaseService() {
        return baseService;
    }

    @Override
    public ClanChatService getClanChatService() {
        return chatService;
    }

    @Override
    public ClanGiftService getClanGiftService() {
        return giftService;
    }

    @Override
    public ClanHelpService getClanHelpService() {
        return helpService;
    }

    @Override
    public ClanMemberMgrService getClanMemberMgrService() {
        return memberService;
    }

    @Override
    public ClanRankMgrService getClanRankMgrService() {
        return rankMgrService;
    }

    @Override
    public ClanTechService getClanTechService() {
        return techService;
    }

    @Override
    public ClanTerritoryService getClanTerritoryService() {
        return territoryService;
    }


    @Override
    public ClanStoreService getClanStoreService() {
        return storeService;
    }

    @Override
    public ClanActivityService getClanActivityService() {
        return clanActivityService;
    }

    @Override
    protected void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        if (this.clanEntity != null) {
            this.clanEntity.getAddComponent().onReloadRes(updatedTemplates);
        }
    }

    public void setDissolved() {
        isDissolved = true;
    }

    public void tellPlayer(long targetPlayerId, GeneratedMessageV3 msg) {
        this.tellPlayer(this.getZoneId(), targetPlayerId, msg);
    }
}

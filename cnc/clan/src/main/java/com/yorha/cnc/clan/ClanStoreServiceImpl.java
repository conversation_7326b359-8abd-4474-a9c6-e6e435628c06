package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanStoreService;
import com.yorha.common.clan.ClanPermissionUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ClanStoreInterfaceType;
import com.yorha.proto.CommonEnum.ClanStoreOperateReturnType;
import com.yorha.proto.SsClanStore.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ClanStoreServiceImpl implements ClanStoreService {
    private static final Logger LOGGER = LogManager.getLogger(ClanStoreServiceImpl.class);
    private final ClanActor clanActor;

    public ClanStoreServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }

    @Override
    public void handleFetchClanStoreAsk(FetchClanStoreAsk ask) {
        ClanStoreInterfaceType type = ask.getInterfaceType();
        if (type == ClanStoreInterfaceType.CSRT_NONE || type == ClanStoreInterfaceType.CSRT_RECYCLE) {
            LOGGER.warn("wrong type {} to get clan store", type);
            clanActor.answer(FetchClanStoreAns.getDefaultInstance());
            return;
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.error("handleFetchClanStoreAsk, clan {} is not exist", clanActor.getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        FetchClanStoreAns ans = clanEntity.getStoreComponent().fetchClanStore(ask.getInterfaceType());
        clanActor.answer(ans);
    }

    @Override
    public void handleFetchClanStoreRecordAsk(FetchClanStoreRecordAsk ask) {
        ClanStoreInterfaceType type = ask.getInterfaceType();
        if (type == ClanStoreInterfaceType.CSRT_NONE || type == ClanStoreInterfaceType.CSRT_RECYCLE) {
            LOGGER.warn("wrong type {} to fetch clan store record", type);
            clanActor.answer(FetchClanStoreRecordAns.getDefaultInstance());
            return;
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.error("handleFetchClanStoreRecordAsk, clan {} is not exist", clanActor.getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        FetchClanStoreRecordAns.Builder builder = FetchClanStoreRecordAns.newBuilder();
        builder.addAllRecords(clanEntity.getStoreComponent().getClanStoreRecord(ask.getInterfaceType()));
        clanActor.answer(builder.build());
    }

    @Override
    public void handleOperateClanStoreItemAsk(OperateClanStoreItemAsk ask) {
        ClanStoreInterfaceType type = ask.getInterfaceType();
        if (type == ClanStoreInterfaceType.CSRT_NONE || type == ClanStoreInterfaceType.CSRT_RECYCLE) {
            LOGGER.warn("wrong type {} to fetch clan store record", type);
            clanActor.answer(FetchClanStoreAns.getDefaultInstance());
            return;
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.error("handleOperateClanStoreItemAsk, clan {} is not exist", clanActor.getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        if (type == ClanStoreInterfaceType.CSRT_BUY) {
            ClanStoreOperateReturnType returnType = clanEntity.getStoreComponent()
                    .buy(ask.getPlayerId(), ask.getItemId(), ask.getItemNum());
            clanActor.answer(OperateClanStoreItemAns.newBuilder().setReturnType(returnType).build());
            return;
        }
        if (type == ClanStoreInterfaceType.CSRT_STOCK) {
            ClanMemberProp memberProp = clanEntity.getMemberComponent().getMemberNoThrow(ask.getPlayerId());
            if (memberProp == null) {
                LOGGER.info("handleOperateClanStoreItemAsk player {} is not in clan {}", ask.getPlayerId(), clanEntity);
                throw new GeminiException(ErrorCode.CLAN_NOT_IN);
            }
            ClanPermissionUtils.checkPermission(CommonEnum.ClanOperationType.COT_STORE_STOCK, memberProp.getStaffId());
            ClanStoreOperateReturnType returnType = clanEntity.getStoreComponent()
                    .stock(ask.getPlayerId(), ask.getItemId(), ask.getItemNum());
            clanActor.answer(OperateClanStoreItemAns.newBuilder().setReturnType(returnType).build());
            return;
        }
    }
}

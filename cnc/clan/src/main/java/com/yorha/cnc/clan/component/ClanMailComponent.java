package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.mail.ClanMailMgr;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 联盟邮件组件
 *
 * <AUTHOR>
 */
public class ClanMailComponent extends ClanComponent {

    private static final Logger LOGGER = LogManager.getLogger(ClanMailComponent.class);

    public ClanMailComponent(ClanEntity owner) {
        super(owner);
    }

    private ClanMailMgr mailMgr;

    /**
     * 联盟创建后
     */
    @Override
    public void onCreate() {
        mailMgr = new ClanMailMgr(getOwner(), ownerActor());
        mailMgr.loadMails();
    }


    /**
     * 联盟加载后
     */
    @Override
    public void onLoad() {
        if (mailMgr == null) {
            mailMgr = new ClanMailMgr(getOwner(), ownerActor());
            mailMgr.loadMails();
        }
    }

    @Override
    public void onDestroy() {
        if (getOwner().getStageComponent().isAlreadyDisband()) {
            return;
        }
        LOGGER.info("ClanMailComponent size:{}", mailMgr.getClanMailTotalNum());
        mailMgr = null;
    }

    /**
     * 联盟解散
     */
    @Override
    public void onDissolution() {
        // 解散删除所有邮件
        mailMgr.deleteAllMails();
        mailMgr = null;
    }

    /**
     * 发送军团邮件
     *
     * @param mailId         邮件id
     * @param mailSendParams 邮件发送参数
     */
    public void sendClanMail(long mailId, StructMail.MailSendParams mailSendParams) {
        mailMgr.sendClanMail(mailId, mailSendParams);
    }

    /**
     * 填充登录时所需的军团邮件信息
     *
     * @param clanMailIndex 军团邮件下标
     * @param enterClanTsMs 登录时进入军团的时间戳
     * @param ansBuilder    回包builder
     */
    public void fillCheckInMailInfo(int clanMailIndex, long enterClanTsMs, CommonMsg.CheckInMailInfo.Builder ansBuilder) {
        ansBuilder.addAllMails(mailMgr.getOfflineMails(clanMailIndex, enterClanTsMs));
        ansBuilder.setClanMailIndex(mailMgr.getMailIndex());
    }
}

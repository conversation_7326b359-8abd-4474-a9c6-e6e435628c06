package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.game.gen.prop.Int64PositionMarkInfoMapProp;
import com.yorha.game.gen.prop.PositionMarkInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MarqueeType;
import com.yorha.proto.CommonEnum.PositionMarkActionType;
import com.yorha.proto.CommonEnum.PositionMarkType;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.SsSceneClan;
import res.template.ConstPositionMarkTemplate;

/**
 * 联盟坐标收藏
 *
 * <AUTHOR>
 */
public class ClanPositionMarkComponent extends ClanComponent {
    public ClanPositionMarkComponent(ClanEntity owner) {
        super(owner);
    }

    public void markPosition(SsClanAttr.ClanMarkPositionCmd ask) {
        if (ask.getPositionMarkType() != PositionMarkType.PMT_CLAN) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, ask.getPositionMarkType().toString());
        }
        ClanMemberProp clanMemberProp = getOwner().getMemberComponent().getMember(ask.getPlayerId());
        // 跑马灯参数
        int marqueeId = 0;
        int markPicId = 0;
        //删除标记
        if (ask.getActionType() == PositionMarkActionType.PMAT_DELETE) {
            if (!getPosMarkMap().containsKey(ask.getPositionMarkInfo().getMarkId())) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "not exist mark");
            }
            // 获取跑马灯参数
            marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(MarqueeType.MARK_CLAN_DELETE);
            markPicId = getPosMarkModel().getClanPosMarkMapV(ask.getPositionMarkInfo().getMarkId()).getMarkPicId();
            getPosMarkMap().remove(ask.getPositionMarkInfo().getMarkId());
        }

        //编辑标记
        if (ask.getActionType() == CommonEnum.PositionMarkActionType.PMAT_EDIT) {
            if (!getPosMarkMap().containsKey(ask.getPositionMarkInfo().getMarkId())) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "clan mark not exist");
            }
            PositionMarkInfoProp positionMarkInfoProp = getPosMarkMap().get(ask.getPositionMarkInfo().getMarkId());
            if (ask.getPositionMarkInfo().getMarkPicId() > 0) {
                positionMarkInfoProp.setMarkPicId(ask.getPositionMarkInfo().getMarkPicId());
            }
            if (ask.getPositionMarkInfo().hasMarkName()) {
                positionMarkInfoProp.setMarkName(ask.getPositionMarkInfo().getMarkName());
            }
            if (ask.getPositionMarkInfo().hasPoint()) {
                positionMarkInfoProp.getPoint().setX(ask.getPositionMarkInfo().getPoint().getX()).setY(ask.getPositionMarkInfo().getPoint().getY());
            }
            if (ask.getPositionMarkInfo().hasOperatorName()) {
                positionMarkInfoProp.setOperatorName(ask.getPositionMarkInfo().getOperatorName());
            }
            if (ask.getPositionMarkInfo().hasZoneId()) {
                positionMarkInfoProp.setZoneId(ask.getPositionMarkInfo().getZoneId());
            }
            //移除之前的标记
            getPosMarkMap().remove(ask.getPositionMarkInfo().getMarkId());
            //加入新的标记
            long markId = IdFactory.nextId("new mark");
            positionMarkInfoProp.setMarkId(markId);
            getPosMarkMap().put(markId, positionMarkInfoProp);
            // 获取跑马灯参数
            marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(MarqueeType.MARK_CLAN_ADD);
            markPicId = getPosMarkModel().getClanPosMarkMapV(markId).getMarkPicId();
        }

        //新增标记
        if (ask.getActionType() == CommonEnum.PositionMarkActionType.PMAT_MARK) {
            if (getPosMarkMap().containsKey(ask.getPositionMarkInfo().getMarkId())) {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "repeat mark");
            }
            if (getPosMarkMap().size() >= ResHolder.getInstance().getConstTemplate(ConstPositionMarkTemplate.class).getAlliMarkRange()) {
                throw new GeminiException(ErrorCode.MARK_CLAN_OVER_LIMIT);
            }
            PositionMarkInfoProp positionMarkInfoProp = new PositionMarkInfoProp();
            positionMarkInfoProp.setMarkId(ask.getPositionMarkInfo().getMarkId())
                    .setZoneId(ask.getPositionMarkInfo().getZoneId())
                    .setMarkPicId(ask.getPositionMarkInfo().getMarkPicId())
                    .setMarkName(ask.getPositionMarkInfo().getMarkName())
                    .setOperatorName(ask.getPositionMarkInfo().getOperatorName()).getPoint()
                    .setX(ask.getPositionMarkInfo().getPoint().getX())
                    .setY(ask.getPositionMarkInfo().getPoint().getY());
            getPosMarkMap().put(ask.getPositionMarkInfo().getMarkId(), positionMarkInfoProp);
            // 获取跑马灯参数
            marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(MarqueeType.MARK_CLAN_ADD);
            markPicId = getPosMarkModel().getClanPosMarkMapV(ask.getPositionMarkInfo().getMarkId()).getMarkPicId();
        }
        //同步到scene上
        SsSceneClan.SyncSceneClanCmd.Builder builder = SsSceneClan.SyncSceneClanCmd.newBuilder();
        builder.setHasPositionMarkChange(true);
        getOwner().getPropComponent().syncToSceneClan(builder);
        // 发跑马灯
        getOwner().getMsgComponent().sendPositionMarkMarquee(marqueeId, clanMemberProp.getStaffId(), clanMemberProp.getCardHead().getName(), markPicId);
    }

    private ClanProp getPosMarkModel() {
        return getOwner().getProp();
    }

    private Int64PositionMarkInfoMapProp getPosMarkMap() {
        return getOwner().getProp().getClanPosMarkMap();
    }
}

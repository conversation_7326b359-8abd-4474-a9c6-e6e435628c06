package com.yorha.cnc.clan.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.devbuff.ClanDevBuffMgr;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanDataTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructBattle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 联盟DevBuff功能对外接口组件
 * <p>
 * ClanDevBuffMgr 真实处理逻辑类
 *
 * <AUTHOR>
 */

public class ClanDevBuffComponent extends ClanComponent implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(ClanDevBuffComponent.class);

    private ClanDevBuffMgr devBuffMgr;

    public ClanDevBuffComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        devBuffMgr = new ClanDevBuffMgr(getOwner());
    }

    @Override
    public void postLoad() {
        devBuffMgr.postInit();
    }

    /**
     * 添加buff
     *
     * @param buffId buffId
     */
    public void addDevBuff(int buffId, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                null,
                devBuffMgr.getBuffType(),
                sourceType,
                null);
    }

    /**
     * 添加buff
     *
     * @param buffId buffId
     */
    public void addDevBuffByLayer(int buffId, int layer, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                null,
                devBuffMgr.getBuffType(),
                sourceType,
                layer);
    }

    /**
     * 添加buff 无视配表结束时间
     *
     * @param buffId  buffId
     * @param endTime 自定义的结束时间
     */
    public void addDevBuff(int buffId, Long endTime, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                endTime,
                devBuffMgr.getBuffType(),
                sourceType,
                null);
    }

    /**
     * 移除buff
     *
     * @param buffId buffId
     */
    public void removeDevBuff(int buffId) {
        devBuffMgr.removeDevBuff(buffId, null, false);
    }

    /**
     * 移除叠加buff
     *
     * @param buffId buffId
     * @param layer  层数
     */
    public void removeDevBuffByLayer(int buffId, int layer) {
        devBuffMgr.removeDevBuff(buffId, layer, false);
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.BUFF;
    }

    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        return devBuffMgr.getAdditionValue(additionId);
    }

    /**
     * 填充玩家登录军团后获取的player侧加成信息
     *
     * @param devBuffInfoBuilder 加成信息的构造器
     */
    public void fillCheckInBuffInfo(CommonMsg.CheckInDevBuffInfo.Builder devBuffInfoBuilder, long playerId) {
        StructBattle.Int32DevBuffMap.Builder devBuffBuilder = devBuffInfoBuilder.getDevBuffBuilder().getDevBuffBuilder();
        // 联盟上的buff
        devBuffBuilder.putAllDatas(getBuffInfo(false));
        // 玩家头衔buff
        devBuffBuilder.putAllDatas(getStaffBuffs(playerId, false));
    }

    /**
     * 填充玩家登录军团后获取的scene侧加成信息
     */
    public void fillSceneBuffInfo(StructBattle.DevBuffSys.Builder buffSysBuilder, long playerId) {
        StructBattle.Int32DevBuffMap.Builder devBuffBuilder = buffSysBuilder.getDevBuffBuilder();
        // 联盟上的buff
        devBuffBuilder.putAllDatas(getBuffInfo(true));
        // 玩家头衔buff
        devBuffBuilder.putAllDatas(getStaffBuffs(playerId, true));
    }

    private Map<Integer, StructBattle.DevBuff> getBuffInfo(boolean isScene) {
        Map<Integer, StructBattle.DevBuff> map = Maps.newHashMap();
        for (DevBuffProp prop : getOwner().getProp().getDevBuffSys().getDevBuff().values()) {
            if (DevBuffUtil.isClanSelfBuff(prop.getDevBuffId())) {
                continue;
            }
            // isScene=true 返回scene上的buff；isScene=false 返回player上的buff
            var sceneBuff = isScene && DevBuffUtil.isSceneBuff(prop.getDevBuffId());
            var playerBuff = !isScene && !DevBuffUtil.isSceneBuff(prop.getDevBuffId());
            if (sceneBuff || playerBuff) {
                StructBattle.DevBuff.Builder data = prop.getCopySsBuilder();
                map.put(data.getDevBuffId(), data.build());
            }
        }
        return map;
    }

    private Map<Integer, StructBattle.DevBuff> getStaffBuffs(long playerId, boolean isScene) {
        Map<Integer, StructBattle.DevBuff> ret = Maps.newHashMap();
        int playerStaffId = getOwner().getStaffComponent().getPlayerStaffId(playerId);
        List<Integer> staffBuffs = ResHolder.getResService(ClanDataTemplateService.class).getBuffIdsByClanStaffId(playerStaffId);
        for (Integer staffBuff : staffBuffs) {
            var sceneBuff = isScene && DevBuffUtil.isSceneBuff(staffBuff);
            var playerBuff = !isScene && !DevBuffUtil.isSceneBuff(staffBuff);
            if (sceneBuff || playerBuff) {
                StructBattle.DevBuff.Builder buffBuilder = StructBattle.DevBuff.newBuilder()
                        .setDevBuffId(staffBuff)
                        .setStartTime(SystemClock.now())
                        .setEndTime(0)
                        .setDevBuffType(CommonEnum.DevBuffType.DBT_CLAN_BUFF)
                        .setSourceType(CommonEnum.DevBuffSourceType.DBST_CLAN_STAFF)
                        .setLayer(1);
                ret.put(staffBuff, buffBuilder.build());
            }
        }
        return ret;
    }

    public void refreshAdditionCache() {
        devBuffMgr.refreshAdditionCache();
    }

    public void refreshAllMapBuildingBuff(Map<Integer, CommonMsg.TerritoryBuffItem> buffs) {
        LOGGER.info("refreshAllMapBuildingBuff buffs={}", buffs);
        // 先结算下资源的增长
        getOwner().getResourcesComponent().settleClanResources();
        getOwner().getMemberComponent().settleAllPlayerResources();

        Set<CommonEnum.DevBuffSourceType> sourceTypeSet = DevBuffUtil.CLAN_BUILDING_SOURCE_TYPE;
        List<Integer> toRemove = Lists.newArrayList();
        // 删除已经不存在的buff
        for (Map.Entry<Integer, DevBuffProp> entry : devBuffMgr.getDevBuffSys().getDevBuff().entrySet()) {
            if (!sourceTypeSet.contains(entry.getValue().getSourceType())) {
                continue;
            }

            if (!buffs.containsKey(entry.getKey())) {
                toRemove.add(entry.getKey());
            }
        }

        for (Integer buffId : toRemove) {
            removeDevBuff(buffId);
        }

        // 添加新增的buff
        for (Map.Entry<Integer, CommonMsg.TerritoryBuffItem> entry : buffs.entrySet()) {
            int buffId = entry.getKey();
            int layers = entry.getValue().getLayers();
            CommonEnum.DevBuffSourceType type = entry.getValue().getType();

            // 新的buff
            DevBuffProp devBuffV = devBuffMgr.getDevBuffSys().getDevBuffV(buffId);
            if (devBuffV == null) {
                if (layers > 0) {
                    addDevBuffByLayer(buffId, layers, type);
                }
                continue;
            }
            // buff层数有变
            int layerDiff = devBuffV.getLayer() - layers;
            if (layerDiff > 0) {
                removeDevBuffByLayer(buffId, layerDiff);
            } else if (layerDiff < 0) {
                addDevBuffByLayer(buffId, -layerDiff, type);
            }
        }

        Set<CommonMsg.TerritoryBuffItem> finalBuff = Sets.newHashSet();
        for (Map.Entry<Integer, DevBuffProp> entry : devBuffMgr.getDevBuffSys().getDevBuff().entrySet()) {
            if (!sourceTypeSet.contains(entry.getValue().getSourceType())) {
                continue;
            }
            CommonMsg.TerritoryBuffItem.Builder build = CommonMsg.TerritoryBuffItem.newBuilder()
                    .setBuffId(entry.getKey())
                    .setLayers(entry.getValue().getLayer())
                    .setType(entry.getValue().getSourceType());
            finalBuff.add(build.build());
        }
        LOGGER.info("refreshAllMapBuildingBuff final buffs={}", finalBuff);
    }
}

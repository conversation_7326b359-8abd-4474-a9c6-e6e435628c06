package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.game.gen.prop.ClanRedDotModelProp;
import com.yorha.game.gen.prop.RedDotDataProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.RedDotKey;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * <p>
 * 军团红点模块
 * 接入军团红点需要确认军团红点的形式，目前有两种形式
 * 1、【军团红点】：军团所有人看到的红点都是相同的，并且这个红点无法通过客户端协议主动消除。此时红点数据保存在军团上，无需写扩散到军团成员。
 * 2、【军团成员红点】：军团内每个人看到的红点是不同的，这个红点可以通过客户端协议主动消除。此时红点数据保存在军团成员上，红点产生时会写扩散到所有军团成员。
 * <p>
 * 无论军团红点还是军团成员红点，当玩家checkIn到军团获取到两类红点数据时，都需要直接覆盖player身上的对应红点数据。
 */
public class ClanRedDotComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanRedDotComponent.class);

    public ClanRedDotComponent(ClanEntity owner) {
        super(owner);
    }

    /**
     * 联盟加载后
     */
    @Override
    public void onLoad() {
        // 军团加载后主动清空所有战争红点，因为集结本身nodb，删除战争红点的cmd在停机或故障时无法发送到clan上
        // 宁可少显示红点也不多显示消不掉的红点
        clearBattleRedDot();
    }

    // ----------------------------------------- 军团红点：战争红点 ---------------------------------- //

    /**
     * 新增rallyId战争红点
     *
     * @param rallyId 集结id，用于唯一区分红点
     */
    public void tryAddWarRedDot(RedDotKey keyType, long rallyId) {
        // 增加军团的红点信息
        RedDotDataProp dataProp = getOrCreateRedDot(keyType);
        dataProp.addEmptyCells(rallyId);
        // 发送增加红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(keyType.getNumber(), CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(keyType.getNumber())
                        .putAddCells(rallyId, Struct.RedDotCell.newBuilder().setCellId(rallyId).build())
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }

    /**
     * 移除rallyId相关的战争红点
     *
     * @param rallyId 集结id，用于唯一区分红点
     */
    public void tryRemoveWarRedDot(RedDotKey keyType, long rallyId) {
        // 删除军团的红点信息
        removeRedDot(keyType, rallyId);
        // 发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(keyType.getNumber(), CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(keyType.getNumber())
                        .addRemoveCellIds(rallyId)
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }

    /**
     * 清理所有的军团战争红点
     */
    private void clearBattleRedDot() {
        removeOneTypeRedDot(RedDotKey.RDK_CLAN_WAR);
    }

    // ----------------------------------------- 军团红点：申请红点 ---------------------------------- //

    /**
     * 新增申请红点
     */
    public void tryAddApplyRedDot() {
        // 增加军团的红点信息
        RedDotDataProp dataProp = getOrCreateRedDot(RedDotKey.RDK_CLAN_APPLY);
        // 获取当前在申请列表中的玩家数
        long applyPlayersNum = getOwner().getProp().getApplys().size();
        if (applyPlayersNum <= 0) {
            LOGGER.error("try add apply red dot where applyPlayersNum {} less or equal with 0", applyPlayersNum);
            return;
        }
        // 清空所有的军团申请红点
        dataProp.clearCells();
        // 传递给玩家当前的申请人数，不传递具体的申请玩家id，因为申请红点是阅后即焚，客户端，服务端均不关心红点产生原因，仅关心产生新红点的次数
        dataProp.addEmptyCells(applyPlayersNum);
        // 发送增加红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_APPLY_VALUE, getApplyRedDotData(true).build()
                ).build();
        getOwner().getMemberComponent().tellOnlinePlayerWithSpecPerm(updateRedDotCmd, CommonEnum.ClanOperationType.COT_AUDIT);
    }

    /**
     * 删除申请红点
     */
    public void tryRemoveApplyRedDot() {
        if (getOwner().getProp().getApplys().size() != 0) {
            LOGGER.info("only remove apply red dot when size is 0");
            return;
        }
        // 获取删除前在申请列表中的玩家数
        long beforeRemoveApplyPlayersNum = getOwner().getProp().getApplys().size() + 1L;
        // 删除军团的红点信息
        removeRedDot(RedDotKey.RDK_CLAN_APPLY, beforeRemoveApplyPlayersNum);
        // 当申请者个数为0时，发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_APPLY_VALUE, getApplyRedDotData(false).build())
                .build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }


    /**
     * @param isAdd 是否是增加
     * @return 返回申请红点的Builder
     */
    public CommonMsg.UpdateRedDotData.Builder getApplyRedDotData(boolean isAdd) {
        if (isAdd) {
            return CommonMsg.UpdateRedDotData.newBuilder()
                    .setRedDotKey(RedDotKey.RDK_CLAN_APPLY_VALUE)
                    .putAddCells(0, Struct.RedDotCell.newBuilder().setCellId(0).build());
        } else {
            return CommonMsg.UpdateRedDotData.newBuilder()
                    .setRedDotKey(RedDotKey.RDK_CLAN_APPLY_VALUE)
                    .setClearAll(true);
        }
    }

    /**
     * 删除多个申请红点，目前仅用于军团准入条件从申请加入变为任何人可加入
     *
     * @param applyPlayerIds 申请的玩家id列表，用于唯一区分红点
     */
    public void tryRemoveApplyRedDot(Set<Long> applyPlayerIds) {
        // 删除军团的红点信息
        removeRedDots(RedDotKey.RDK_CLAN_APPLY, applyPlayerIds);
        // 发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_APPLY_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_APPLY_VALUE)
                        .setClearAll(true)
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }

    // ----------------------------------------- 军团成员红点：军团商店红点 ---------------------------------- //

    /**
     * 添加军团商店上架商品红点
     *
     * @param itemId 上架的商品id
     */
    public void tryAddStoreRedDot(int itemId) {
        // 发送增加红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_STORE_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_STORE_VALUE)
                        .putAddCells(itemId, Struct.RedDotCell.newBuilder().setCellId(itemId).build())
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
        // 获取所有不在线的军团玩家，将红点添加到clanMember身上
        for (long playerId : getOwner().getMemberComponent().getAllClanOfflinePlayerIds()) {
            getOwner().getMemberComponent().addRedDot(playerId, RedDotKey.RDK_CLAN_STORE, itemId);
        }
    }

    /**
     * 移除军团商店某商品红点
     *
     * @param itemId 下架的商品id
     */
    public void tryRemoveStoreRedDot(int itemId) {
        // 发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_STORE_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_STORE_VALUE)
                        .addRemoveCellIds(itemId)
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
        // 获取所有不在线的军团玩家，将红点从clanMember身上移除
        for (long playerId : getOwner().getMemberComponent().getAllClanOfflinePlayerIds()) {
            getOwner().getMemberComponent().removeRedDot(playerId, RedDotKey.RDK_CLAN_STORE, itemId);
        }
    }

    /**
     * 移除军团商店所有红点，仅在确定商店没有商品后调用
     */
    public void tryRemoveAllStoreRedDots() {
        // 发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_STORE_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_STORE_VALUE)
                        .setClearAll(true)
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
        // 额外清除可能的军团红点
        getProp().removeRedDotMapV(RedDotKey.RDK_CLAN_STORE_VALUE);
        // 获取所有不在线的军团玩家，将红点从clanMember身上移除
        for (long playerId : getOwner().getMemberComponent().getAllClanOfflinePlayerIds()) {
            getOwner().getMemberComponent().removeAllRedDotByKey(playerId, RedDotKey.RDK_CLAN_STORE);
        }
    }

    // ----------------------------------------- 军团红点：建设id ---------------------------------- //

    /**
     * 新增军团资源建设红点
     */
    public void tryAddResBuildingRedDot() {
        // 增加军团的红点信息
        RedDotDataProp dataProp = getOrCreateRedDot(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD);
        dataProp.addEmptyCells(0L);
        // 发送增加红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD_VALUE)
                        .putAddCells(0L, Struct.RedDotCell.newBuilder().setCellId(0L).build())
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }

    /**
     * 移除军团资源建设红点
     */

    public void tryRemoveResBuildingRedDot() {
        // 删除军团的红点信息
        removeRedDot(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD, 0L);
        // 发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_RES_BUILDING_BUILD_VALUE)
                        .addRemoveCellIds(0L)
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }


    // ----------------------------------------- 军团红点：改建id ---------------------------------- //

    /**
     * 新增军团改建红点
     */
    public void tryAddConstructRedDot(long entityId) {
        // 增加军团的红点信息
        RedDotDataProp dataProp = getOrCreateRedDot(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD);
        dataProp.addEmptyCells(entityId);
        // 发送增加红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD_VALUE)
                        .putAddCells(0L, Struct.RedDotCell.newBuilder().setCellId(entityId).build())
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }

    /**
     * 移除军团资源建设红点
     */

    public void tryRemoveConstructRedDot(long entityId) {
        // 删除军团的红点信息
        removeRedDot(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD, entityId);
        // 发送删除红点的信息给军团内在线的玩家
        SsPlayerMisc.UpdateRedDotCmd updateRedDotCmd = SsPlayerMisc.UpdateRedDotCmd.newBuilder()
                .putRedDots(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD_VALUE, CommonMsg.UpdateRedDotData.newBuilder()
                        .setRedDotKey(RedDotKey.RDK_CLAN_CONSTRUCT_BUILD_VALUE)
                        .addRemoveCellIds(entityId)
                        .build()
                ).build();
        getOwner().getMemberComponent().batchTellOnlinePlayer(updateRedDotCmd);
    }
    

    // ----------------------------------------- 其他 ---------------------------------- //

    /**
     * 填充玩家登录军团后获取的红点信息
     *
     * @param redDotInfoBuilder 红点信息的构造器
     */
    public void fillCheckInClanRedDotInfo(CommonMsg.CheckInRedDotInfo.Builder redDotInfoBuilder) {
        // 军团红点设置，目前有申请、战争红点
        for (RedDotDataProp redDotDataProp : getProp().getRedDotMap().values()) {
            redDotInfoBuilder.getRedDotMapBuilder().putDatas(redDotDataProp.getKey(), redDotDataProp.getCopySsBuilder().build());
        }
    }

    private ClanRedDotModelProp getProp() {
        return getOwner().getProp().getRedDotModel();
    }

    private RedDotDataProp getOrCreateRedDot(CommonEnum.RedDotKey key) {
        RedDotDataProp v = getProp().getRedDotMapV(key.getNumber());
        if (v != null) {
            return v;
        }
        return getProp().addEmptyRedDotMap(key.getNumber());
    }

    private void removeOneTypeRedDot(CommonEnum.RedDotKey key) {
        getProp().removeRedDotMapV(key.getNumber());
    }

    private void removeRedDot(CommonEnum.RedDotKey key, long removeCellId) {
        RedDotDataProp dataProp = getProp().getRedDotMapV(key.getNumber());
        if (dataProp == null) {
            return;
        }
        dataProp.removeCellsV(removeCellId);
    }

    private void removeRedDots(CommonEnum.RedDotKey key, Collection<Long> removeCellIds) {
        RedDotDataProp dataProp = getProp().getRedDotMapV(key.getNumber());
        if (dataProp == null) {
            return;
        }
        for (Long removeCellId : removeCellIds) {
            dataProp.removeCellsV(removeCellId);
        }
    }
}

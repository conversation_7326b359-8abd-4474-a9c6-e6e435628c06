package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanTerritoryService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanTerritory.*;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 领土相关协议
 *
 * <AUTHOR>
 */
public class ClanTerritoryServiceImpl implements ClanTerritoryService {
    private static final Logger LOGGER = LogManager.getLogger(ClanTerritoryServiceImpl.class);

    private final ClanActor clanActor;

    public ClanTerritoryServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }

    public long getClanId() {
        return clanActor.getClanId();
    }

    @Override
    public void handleGetClanPowerRewardAsk(GetClanPowerRewardAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        GetClanPowerRewardAns ans = clanEntity.getTerritoryComponent().getClanPowerReward(ask.getPlayerId());
        clanActor.answer(ans);
    }

    @Override
    public void handleCheckRebuildClanBuildingAsk(CheckRebuildClanBuildingAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (null == clanEntity) {
            LOGGER.error("CheckRebuildClanBuildingAsk cant find clan: {}", getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getTerritoryComponent().checkRebuildClanBuilding(ask);
        CheckRebuildClanBuildingAns.Builder ans = CheckRebuildClanBuildingAns.newBuilder();
        ans.setStaffId(clanEntity.getStaffComponent().getPlayerStaffId(ask.getPlayerId()));
        clanActor.answer(ans.build());
    }

    @Override
    public void handlePlaceClanResBuildAsk(PlaceClanResBuildAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (null == clanEntity) {
            LOGGER.error("placeClanResBuildAsk cant find clan: {}", getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        long entityId = clanEntity.getTerritoryComponent().placeClanResBuild(ask);
        clanActor.answer(PlaceClanResBuildAns.newBuilder().setEntityId(entityId).build());
    }

    @Override
    public void handleCheckExtinguishClanBuildingAsk(CheckExtinguishClanBuildingAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (null == clanEntity) {
            LOGGER.error("CheckExtinguishClanBuildingAsk cant find clan: {}", getClanId());
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getTerritoryComponent().checkExtinguishClanBuildingAsk(ask);
        clanActor.answer(CheckExtinguishClanBuildingAns.getDefaultInstance());
    }

    @Override
    public void handleFetchClanBuildingInfoAsk(FetchClanBuildingInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (null == clanEntity) {
            LOGGER.info("FetchClanBuildingInfoAsk cant find clan={}", getClanId());
            clanActor.answer(FetchClanBuildingInfoAns.getDefaultInstance());
            return;
        }
        FetchClanBuildingInfoAns.Builder ans = clanEntity.getTerritoryComponent().fetchClanBuildingInfo();
        clanActor.answer(ans.build());
    }

    @Override
    public void handleFetchTerritoryPageAllAsk(FetchTerritoryPageAllAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.warn("FetchTerritoryPageAll, getOrLoadClanFailed");
            clanActor.answer(FetchTerritoryPageAllAns.getDefaultInstance());
            return;
        }
        clanEntity.getTerritoryComponent().getTerritoryPageAll(clanActor.getCurrentEnvelope());
    }

    @Override
    public void handleSyncTerritoryBuffSnapshotCmd(SyncTerritoryBuffSnapshotCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            LOGGER.error("handleSyncTerritoryBuffSnapshotCmd cant find clan: {}", getClanId());
            return;
        }
        clanEntity.getDevBuffComponent().refreshAllMapBuildingBuff(ask.getBuffMap());
    }

    @Override
    public void handleSyncTerritoryInfoCmd(SyncTerritoryInfoCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            LOGGER.error("syncClanPower cant find clan: {}", getClanId());
            return;
        }

        SsPlayerMisc.ClanBuildingInfoCmd playerTell = clanEntity.getTerritoryComponent().syncTerritoryInfo(ask);
        // 有设置值才发送通知
        if (playerTell != null) {
            LOGGER.info("tell clanMember ClanBuildingInfoCmd= {}", playerTell);
            clanEntity.getMemberComponent().batchTellOnlinePlayer(playerTell);
        }
    }

    @Override
    public void handleSyncClanBuildStatusCmd(SyncClanBuildStatusCmd ask) {
        if (!ask.hasId() || !ask.hasStatus()) {
            LOGGER.error("SyncClanBuildStatusCmd error: {}", ask);
            return;
        }
        if (ask.getStatus() == CommonEnum.ClanBuildStatus.CBS_FINISH) {
            // 如果是完成建筑，需要额外检查参数
            if (!ask.hasPoint() || !ask.hasTemplateId()) {
                LOGGER.error("SyncClanBuildStatusCmd loss point or templateId, error: {}", ask);
                return;
            }
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            LOGGER.error("SyncClanBuildStatusCmd cant find clan: {}", getClanId());
            return;
        }
        clanEntity.getTerritoryComponent().onSyncClanBuildStatus(ask);
    }
}

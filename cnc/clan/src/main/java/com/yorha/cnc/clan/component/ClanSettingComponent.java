package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.NameHelper;
import com.yorha.game.gen.prop.ClanBaseInfoProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ClanEnterRequire;
import com.yorha.proto.SsPlayerClan;
import com.yorha.proto.SsSceneClan;
import com.yorha.proto.SsSceneClan.SyncSceneClanCmd;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ClanSettingComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanSettingComponent.class);

    public ClanSettingComponent(ClanEntity owner) {
        super(owner);
    }

    private ClanBaseInfoProp getBase() {
        return getOwner().getProp().getBase();
    }

    public String getName() {
        return getBase().getName();
    }

    public void setName(String name) {
        getBase().setName(name);
        // 同步给场景
        SyncSceneClanCmd.Builder cmd = SyncSceneClanCmd.newBuilder();
        cmd.getSceneClanBuilder().setClanName(name);
        getOwner().getPropComponent().syncToSceneClan(cmd);
        // 军团card更新：军团名变化，实时同步
        getOwner().getPropComponent().updateClanCardCache(true);
    }

    public String getSimpleName() {
        return getBase().getSname();
    }

    public void setSimpleName(String name) {
        getBase().setSname(name);
        // 同步给场景
        SyncSceneClanCmd.Builder cmd = SyncSceneClanCmd.newBuilder();
        cmd.getSceneClanBuilder().setClanSimpleName(name);
        getOwner().getPropComponent().syncToSceneClan(cmd);
        // 军团card更新：军团简称变化实时同步
        getOwner().getPropComponent().updateClanCardCache(true);
    }

    public void setDescribe(String describe) {
        getBase().setDescribe(describe).setDescribeModFlag(true);
        // 军团card更新：军团描述变化延迟同步
        getOwner().getPropComponent().updateClanCardCache(false);
    }

    /**
     * 检查重置静默期，IdIp和GM不需要检查静默期，但是正常游戏逻辑需要检查静默期再修改名称
     */
    public void checkModifyClanName() {
        if (getOwner().getProp().getResetQuietPeriod().getResetNameQuietTsMs() >= SystemClock.now()) {
            LOGGER.error("change clan name fail because of quiet period");
            throw new GeminiException(ErrorCode.CLAN_NAME_CHANG_CD);
        }
    }

    /**
     * 检查重置静默期，IdIp和GM不需要检查静默期，但是正常游戏逻辑需要检查静默期再修改名称
     */
    public void checkModifyClanSName() {
        if (getOwner().getProp().getResetQuietPeriod().getResetSNameQuietTsMs() >= SystemClock.now()) {
            LOGGER.error("change clan simple name fail because of quiet period");
            throw new GeminiException(ErrorCode.CLAN_SIMPLE_NAME_CHANG_CD);
        }
    }

    /**
     * 检查重置静默期，IdIp和GM不需要检查静默期，但是正常游戏逻辑需要检查静默期再修改宣言
     */
    public void checkModifyClanDescribe() {
        if (getOwner().getProp().getResetQuietPeriod().getResetDescribeQuietTsMs() >= SystemClock.now()) {
            LOGGER.warn("change clan describe fail because of quiet period");
            throw new GeminiException(ErrorCode.CLAN_DESC_CHANG_CD);
        }
    }

    /**
     * 设置默认宣言
     */
    private void setDefaultDescribe() {
        //目前还没有默认宣言的设计，暂时将宣言设置为空
        this.setDescribe("");
    }

    /**
     * 重置联盟名称，仅供IdIp和GM使用
     */
    private void resetClanName(long quietPeriodTsMs) {
        String oldName = this.getName();
        String name = NameHelper.newClanName(ownerActor(), getEntityId());
        // 改名
        this.setName(name);
        // 重置静默期
        getOwner().getProp().getResetQuietPeriod().setResetNameQuietTsMs(quietPeriodTsMs);
        // 回收旧名字
        NameHelper.releaseName(ownerActor(), CommonEnum.NameType.CLAN_NAME, oldName);
    }

    /**
     * 重置联盟宣言，仅供IDIP和GM使用
     */
    private void resetClanDescribe(long quietPeriodTsMs) {
        // 设置为默认宣言
        this.setDefaultDescribe();
        // 重置静默期
        getOwner().getProp().getResetQuietPeriod().setResetDescribeQuietTsMs(quietPeriodTsMs);
    }

    private void resetClanShortName(long quietPeriodTsMs) {
        String oldSName = this.getSimpleName();
        String name = NameHelper.newClanSName(ownerActor(), getEntityId());
        // 改名
        this.setSimpleName(name);
        // 重置静默期
        getOwner().getProp().getResetQuietPeriod().setResetSNameQuietTsMs(quietPeriodTsMs);
        // 回收旧名字
        NameHelper.releaseName(ownerActor(), CommonEnum.NameType.CLAN_SIMPLE_NAME, oldSName);
    }

    /**
     * 重置联盟信息（名称和宣言）
     */
    public void resetClanInfo(boolean resetName, boolean resetDescribe, boolean resetClanShortName, int quietSecondTime) {
        long quietPeriodTsMs = SystemClock.now() + TimeUtils.second2Ms(quietSecondTime);
        if (resetName) {
            this.resetClanName(quietPeriodTsMs);
        }
        if (resetDescribe) {
            this.resetClanDescribe(quietPeriodTsMs);
        }
        if (resetClanShortName) {
            this.resetClanShortName(quietPeriodTsMs);
        }
    }


    public ClanEnterRequire getClanEnterRequire() {
        return getBase().getRequire();
    }

    public void setClanEnterRequire(ClanEnterRequire newRequire) {
        ClanEnterRequire oldRequire = getBase().getRequire();
        getBase().setRequire(newRequire);
        if (oldRequire == ClanEnterRequire.VERIFY && newRequire == ClanEnterRequire.NONE) {
            // 从需要审核改成无需审核，需要清空所有申请列表，拒绝所有申请列表中的玩家加入军团
            for (long applyPlayerIds : getOwner().getProp().getApplys().keySet()) {
                ownerActor().tellPlayer(applyPlayerIds, SsPlayerClan.ClanApplyResultAsk.newBuilder()
                        .setClanId(getEntityId())
                        .setIsAllow(false)
                        .setClanSname(getOwner().getProp().getBase().getSname())
                        .setOperatorId(0L)
                        .build());
                getOwner().getMemberComponent().sendGuildStaffManageQLog(applyPlayerIds, "refuse_entry_guild", 0L, 0, 0);
            }
            getOwner().getRedDotComponent().tryRemoveApplyRedDot(getOwner().getProp().getApplys().keySet());
            getOwner().getProp().getApplys().clear();
        }
        // 同步给场景，军团推荐需要相关信息
        SsSceneClan.SyncSceneClanCmd.Builder tell = SsSceneClan.SyncSceneClanCmd.newBuilder();
        tell.getSceneClanBuilder().setRequire(newRequire);
        getOwner().getPropComponent().syncToSceneClan(tell);
        // 军团card更新：军团准入变化实时同步
        getOwner().getPropComponent().updateClanCardCache(true);
    }

    /**
     * 确保军团旗帜或领土颜色变化
     *
     * @param flagColor      旗帜颜色
     * @param flagShading    旗帜背景
     * @param flagSign       旗帜符号
     * @param territoryColor 领土颜色
     * @throws GeminiException 如果军团旗帜或领土颜色未发生变化，抛出
     */
    public void sureClanFlagOrTerritoryColorChanged(int flagColor, int flagShading, int flagSign, int territoryColor, int nationFlagId) throws GeminiException {
        if (getBase().getFlagColor() != flagColor) {
            return;
        }
        if (getBase().getFlagShading() != flagShading) {
            return;
        }
        if (getBase().getFlagSign() != flagSign) {
            return;
        }
        if (getBase().getTerritoryColor() != territoryColor) {
            return;
        }
        if (getBase().getNationFlagId() != nationFlagId) {
            return;
        }
        throw new GeminiException(ErrorCode.CLAN_FLAG_AND_TERRITORY_COLOR_NO_DIFF);
    }

    public boolean setClanFlag(int flagColor, int flagShading, int flagSign, int nationFlag) {
        boolean isChanged = false;
        if (getBase().getFlagColor() != flagColor) {
            getBase().setFlagColor(flagColor);
            isChanged = true;
        }
        if (getBase().getFlagShading() != flagShading) {
            getBase().setFlagShading(flagShading);
            isChanged = true;
        }
        if (getBase().getFlagSign() != flagSign) {
            getBase().setFlagSign(flagSign);
            isChanged = true;
        }
        if (getBase().getNationFlagId() != nationFlag) {
            getBase().setNationFlagId(nationFlag);
            isChanged = true;
        }
        return isChanged;
    }

    public boolean setTerritoryColor(int territoryColor) {
        boolean isChanged = false;
        if (getBase().getTerritoryColor() != territoryColor) {
            getBase().setTerritoryColor(territoryColor);
            isChanged = true;
        }
        return isChanged;
    }

    public int getTerritoryColor() {
        return getBase().getTerritoryColor();
    }

    public String getWelcomeLetter() {
        return getBase().getWelcomeLetter();
    }

    public void setWelcomeLetter(String letter) {
        getBase().setWelcomeLetter(letter);
    }

    public void setLanguage(CommonEnum.Language language) {
        getBase().setLanguage(language);
        // 军团card更新：军团语言变化延迟同步
        getOwner().getPropComponent().updateClanCardCache(false);
    }

}

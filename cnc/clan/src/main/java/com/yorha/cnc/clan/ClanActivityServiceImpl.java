package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanActivityService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsClanActivity.AddClanActScoreCmd;
import com.yorha.proto.SsClanActivity.FetchActivityDataAns;
import com.yorha.proto.SsClanActivity.FetchActivityDataAsk;

/**
 * <AUTHOR>
 */
public class ClanActivityServiceImpl implements ClanActivityService {

    private final ClanActor clanActor;

    public ClanActivityServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }

    public long getClanId() {
        return clanActor.getClanId();
    }

    @Override
    public void handleAddClanActScoreCmd(AddClanActScoreCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getActivityComponent().onAddActScore(ask.getType(), ask.getCurActId(), ask.getAddScore());
    }

    @Override
    public void handleFetchActivityDataAsk(FetchActivityDataAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        FetchActivityDataAns ans = clanEntity.getActivityComponent().fetchActivityData(ask.getType());
        clanActor.answer(ans);
    }
}
package com.yorha.cnc.clan.component;

import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ClanInviteModelProp;
import com.yorha.game.gen.prop.InvitePlayerRecordProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class ClanInviteComponent extends ClanComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanInviteComponent.class);

    /**
     * 下一个过期的邀请记录的定时器
     */
    private ActorTimer nextExpireInviteTask = null;

    /**
     * 最早过期的邀请记录的时间戳
     */
    private long earliestInviteTsMs = Long.MAX_VALUE;

    /**
     * 最早过期的邀请记录的玩家id
     */
    private long earliestPlayerId = 0L;

    public ClanInviteComponent(ClanEntity entity) {
        super(entity);
    }

    /**
     * 联盟加载后
     */
    @Override
    public void onLoad() {
        long inviteExpireTsMs = TimeUnit.HOURS.toMillis(getOwner().getConstTemplate().getInviteExpireHours());
        Set<Long> needRemoveRecordPlayerIds = new HashSet<>();
        // 检查邀请列表里面是否有过期的，并加入优先队列
        for (InvitePlayerRecordProp record : getProp().getInviteRecords().values()) {
            if (record.getInviteTsMs() + inviteExpireTsMs < SystemClock.now()) {
                needRemoveRecordPlayerIds.add(record.getPlayerId());
                continue;
            }
            if (record.getInviteTsMs() < earliestInviteTsMs) {
                earliestInviteTsMs = record.getInviteTsMs();
                earliestPlayerId = record.getPlayerId();
            }
        }
        // 移除过期的邀请记录
        for (long playerId : needRemoveRecordPlayerIds) {
            getProp().getInviteRecords().remove(playerId);
        }
        // 设置下一个过期的邀请记录的定时器
        setNextExpireInviteTask();
    }

    /**
     * 创建联盟时
     */
    @Override
    public void onCreate() {
        // do nothing
    }

    /**
     * 联盟解散时
     */
    @Override
    public void onDissolution() {
    }

    /**
     * 邀请玩家加入联盟
     *
     * @param operatorId     操作者id
     * @param targetPlayerId 目标玩家id
     */
    public void inviteToClan(long operatorId, long targetPlayerId) {
        long now = SystemClock.now();
        // 检查邀请列表里面是否已经有了
        if (isInInviteRecord(targetPlayerId)) {
            // 有，刷新邀请时间
            getInvitePlayerRecordProp(targetPlayerId).setInviteTsMs(now);
            if (targetPlayerId == earliestPlayerId) {
                // 如果是最早的邀请，刷新最早的邀请记录信息
                refreshEarliestInvite();
                setNextExpireInviteTask();
            }
        } else {
            // 没有，加入邀请列表
            if (isInviteRecordFull()) {
                // 邀请列表已满，移除最早的邀请
                inviteExpire();
            }
            addInviteRecord(targetPlayerId, now);
        }
        // 发邮件
        getOwner().getMsgComponent().sendInviteMail(operatorId, targetPlayerId);
        // 打qlog，待需求
    }

    /**
     * 删除玩家在本军团的邀请信息，如果没有邀请信息会打日志
     *
     * @param playerId 玩家id
     */
    public void removeClanInvite(long playerId) {
        if (!isInInviteRecord(playerId)) {
            LOGGER.info("try refuse clan invite, but not in invite record, playerId={}", playerId);
            return;
        }
        getProp().removeInviteRecordsV(playerId);
        if (playerId == earliestPlayerId) {
            // 如果是最早的邀请，刷新最早的邀请记录信息
            refreshEarliestInvite();
            setNextExpireInviteTask();
        }
        // qlog
    }

    public void hasInvitePlayer(long playerId) throws GeminiException {
        if (!isInInviteRecord(playerId)) {
            throw new GeminiException(ErrorCode.CLAN_INVITE_NOT_EXIST);
        }
    }

    public boolean hasInvitePlayerNoThrow(long playerId) {
        return isInInviteRecord(playerId);
    }

    private void setNextExpireInviteTask() {
        if (nextExpireInviteTask != null) {
            nextExpireInviteTask.cancel();
            nextExpireInviteTask = null;
        }
        if (earliestInviteTsMs == Long.MAX_VALUE) {
            // 没有邀请记录，直接return
            return;
        }
        long nextExpireTsMs = earliestInviteTsMs + TimeUnit.HOURS.toMillis(getOwner().getConstTemplate().getInviteExpireHours());
        long delay = nextExpireTsMs - SystemClock.now();
        if (delay <= 0) {
            inviteExpire();
            return;
        }
        nextExpireInviteTask = ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_INVITE_EXPIRE,
                this::inviteExpire,
                delay, TimeUnit.MILLISECONDS);
    }

    private void inviteExpire() {
        if (earliestInviteTsMs == Long.MAX_VALUE) {
            // 没有邀请记录，直接return
            return;
        }
        getProp().getInviteRecords().remove(earliestPlayerId);
        refreshEarliestInvite();
        setNextExpireInviteTask();
    }

    /**
     * 刷新最早的邀请记录 O(n)
     */
    private void refreshEarliestInvite() {
        earliestInviteTsMs = Long.MAX_VALUE;
        earliestPlayerId = 0L;
        for (InvitePlayerRecordProp record : getProp().getInviteRecords().values()) {
            if (record.getInviteTsMs() < earliestInviteTsMs) {
                earliestInviteTsMs = record.getInviteTsMs();
                earliestPlayerId = record.getPlayerId();
            }
        }
    }

    private boolean isInviteRecordFull() {
        return getProp().getInviteRecordsSize() >= getOwner().getConstTemplate().getInviteLimit();
    }

    private void addInviteRecord(long playerId, long inviteTsMs) {
        InvitePlayerRecordProp newProp = getProp().addEmptyInviteRecords(playerId);
        newProp.setPlayerId(playerId);
        newProp.setInviteTsMs(inviteTsMs);
    }

    private InvitePlayerRecordProp getInvitePlayerRecordProp(long playerId) {
        return getProp().getInviteRecordsV(playerId);
    }

    /**
     * 查看玩家是否已经在邀请列表中
     *
     * @param playerId 玩家id
     * @return 是否在邀请列表中
     */
    private boolean isInInviteRecord(long playerId) {
        return getProp().getInviteRecordsV(playerId) != null;
    }

    private ClanInviteModelProp getProp() {
        return getOwner().getProp().getInviteModel();
    }
}

package com.yorha.cnc.clan.component;

import com.google.protobuf.ByteString;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.StructPB;

import javax.annotation.Nullable;

public class ClanMarqueeComponent extends ClanComponent {

    public ClanMarqueeComponent(ClanEntity owner) {
        super(owner);
    }

    public void sendClanMarquee(final int marqueeId, final StructPB.DisplayDataPB param) {
        sendClanMarquee(marqueeId, param, null);
    }

    public void sendClanMarquee(final int marqueeId, final StructPB.DisplayDataPB param, @Nullable final StructClanPB.ClanFlagInfoPB flagInfo) {
        if (flagInfo == null) {
            PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, param);
            final int msgType = MsgType.PLAYER_MARQUEEMESSAGE_NTF;
            ByteString msgBytes = msg.toByteString();
            getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(msgType, msgBytes);
        } else {
            PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, param, flagInfo);
            final int msgType = MsgType.PLAYER_MARQUEEMESSAGE_NTF;
            ByteString msgBytes = msg.toByteString();
            getOwner().getMemberComponent().broadcastCsMsgToAllOnlineMemberInClan(msgType, msgBytes);
        }
    }

    public void sendFullServerMarquee(final int marqueeId, StructPB.DisplayDataPB displayData, StructClanPB.ClanFlagInfoPB flagInfo) {
        PlayerScene.Player_MarqueeMessage_NTF msg;
        if (flagInfo == null) {
            msg = MsgHelper.buildMarqueeMsg(marqueeId, displayData);
        } else {
            msg = MsgHelper.buildMarqueeMsg(marqueeId, displayData, flagInfo);
        }
        BroadcastHelper.toCsOnlinePlayerInZone(ownerActor().getZoneId(), MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }
}

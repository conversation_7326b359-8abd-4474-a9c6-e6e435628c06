package com.yorha.cnc.clan;

import com.yorha.common.actor.ClanAttrService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.game.gen.prop.ClanLogRecordModelProp;
import com.yorha.game.gen.prop.ClanStageModelProp;
import com.yorha.game.gen.prop.Int64PositionMarkInfoMapProp;
import com.yorha.game.gen.prop.PositionMarkInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.SsClanAttr.*;
import com.yorha.proto.SsSceneClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ClanAttrServiceImpl implements ClanAttrService {
    private static final Logger LOGGER = LogManager.getLogger(ClanAttrServiceImpl.class);

    private final ClanActor clanActor;

    public ClanAttrServiceImpl(ClanActor clanActor) {
        this.clanActor = clanActor;
    }

    public long getClanId() {
        return clanActor.getClanId();
    }

    @Override
    public void handleModifyClanPrivilegeAsk(ModifyClanPrivilegeAsk ask) {
        clanActor.answer(ModifyClanPrivilegeAns.getDefaultInstance());
    }

    @Override
    public void handleCheckModifyNameQuietTimeAsk(CheckModifyNameQuietTimeAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        //检查重置静默期
        clanEntity.getSettingComponent().checkModifyClanName();
        clanActor.answer(CheckModifyNameQuietTimeAns.getDefaultInstance());
    }

    @Override
    public void handleCheckModifySNameQuietTimeAsk(CheckModifySNameQuietTimeAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        //检查重置静默期
        clanEntity.getSettingComponent().checkModifyClanSName();
        clanActor.answer(CheckModifySNameQuietTimeAns.getDefaultInstance());
    }

    @Override
    public void handleFetchClanPositionMarkAsk(FetchClanPositionMarkAsk ask) {
        FetchClanPositionMarkAns.Builder ans = FetchClanPositionMarkAns.newBuilder();
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        Int64PositionMarkInfoMapProp mapProp = clanEntity.getProp().getClanPosMarkMap();
        for (PositionMarkInfoProp infoProp : mapProp.values()) {
            ans.addInfos(infoProp.getCopySsBuilder().build());
        }
        clanActor.answer(ans.build());
    }

    @Override
    public void handleModifyClanNameAsk(ModifyClanNameAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        final String oldName = clanEntity.getSettingComponent().getName();
        clanEntity.getSettingComponent().setName(ask.getName());
        clanEntity.getClanQlogComponent().qLogChangeName();
        clanActor.answer(ModifyClanNameAns.newBuilder().setOldName(oldName).build());
    }

    @Override
    public void handleModifyClanSimpleNameAsk(ModifyClanSimpleNameAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        // NOTE(furson): 权限检查在player侧完成，clan不再检查权限
        final String oldSimpleName = clanEntity.getSettingComponent().getSimpleName();
        clanEntity.getSettingComponent().setSimpleName(ask.getSimpleName());
        clanEntity.getClanQlogComponent().qLogChangeSName();
        clanActor.answer(ModifyClanSimpleNameAns.newBuilder().setOldName(oldSimpleName).build());
    }

    @Override
    public void handleCheckClanFlagAndTerritoryColorAsk(CheckClanFlagAndTerritoryColorAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getSettingComponent().sureClanFlagOrTerritoryColorChanged(ask.getFlagColor(),
                ask.getFlagShading(), ask.getFlagSign(), ask.getTerritoryColor(), ask.getNationFlagId());
        clanActor.answer(CheckClanFlagAndTerritoryColorAns.getDefaultInstance());
    }

    @Override
    public void handleModifyClanFlagAndTerritoryColorAsk(ModifyClanFlagAndTerritoryColorAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        boolean isChanged = clanEntity.getSettingComponent().setClanFlag(ask.getFlagColor(), ask.getFlagShading(), ask.getFlagSign(), ask.getNationFlagId());
        isChanged = clanEntity.getSettingComponent().setTerritoryColor(ask.getTerritoryColor()) || isChanged;
        // 仅当旗帜更改时，同步给场景
        if (isChanged) {
            SsSceneClan.SyncSceneClanCmd.Builder cmd = SsSceneClan.SyncSceneClanCmd.newBuilder();
            cmd.getSceneClanBuilder().setFlagColor(ask.getFlagColor()).setTerritoryColor(ask.getTerritoryColor())
                    .setFlagShading(ask.getFlagShading()).setFlagSign(ask.getFlagSign()).setNationFlagId(ask.getNationFlagId());
            clanEntity.getPropComponent().syncToSceneClan(cmd);
            // 军团card更新：低频，旗帜变化需要实时更新
            clanEntity.getPropComponent().updateClanCardCache(false);
        }
        clanActor.answer(ModifyClanFlagAndTerritoryColorAns.getDefaultInstance());
    }

    @Override
    public void handleModifyClanWelcomeLetterAsk(ModifyClanWelcomeLetterAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        if (!clanEntity.getMemberComponent().isClanOwner(ask.getCallerPlayerId())) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        if (ask.getLetter().length() >= ResHolder.getResService(ConstClanKVResService.class).getTemplate().getWelcomeAllianceWord()) {
            throw new GeminiException(ErrorCode.CLAN_WELCOME_LETTER_LIMIT);
        }
        clanEntity.getSettingComponent().setWelcomeLetter(ask.getLetter());
        clanActor.answer(ModifyClanWelcomeLetterAns.newBuilder().build());
    }

    @Override
    public void handleModifyClanSettingMiscAsk(ModifyClanSettingMiscAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        if (ask.hasRequire()) {
            if (clanEntity.getStageComponent().isDissolving()) {
                throw new GeminiException(ErrorCode.CLAN_TRY_CHANGE_ENTER_REQUIRE_IN_DISSOLVING);
            }
        }
        if (ask.hasDescribe()) {
            // 宣言修改需要额外检查静默期
            clanEntity.getSettingComponent().checkModifyClanDescribe();
            clanEntity.getSettingComponent().setDescribe(ask.getDescribe());
        }
        if (ask.hasNewLanguage()) {
            clanEntity.getSettingComponent().setLanguage(ask.getNewLanguage());
        }
        if (ask.hasRequire()) {
            clanEntity.getSettingComponent().setClanEnterRequire(ask.getRequire());
            clanEntity.getClanQlogComponent().qLogClanEntry();
        }
        clanActor.answer(ModifyClanSettingMiscAns.getDefaultInstance());
    }

    @Override
    public void handleClanMarkPositionCmd(ClanMarkPositionCmd ask) {
        ClanEntity clanEntity = clanActor.getClanEntityOrNull();
        // 联盟解散保护
        if (clanEntity == null) {
            LOGGER.info("handleClanMarkPositionCmd clan is null {}", clanActor.getClanId());
            return;
        }
        clanEntity.getPositionMarkComponent().markPosition(ask);
    }

    @Override
    public void handleSyncRedDotToClanCmd(SyncRedDotToClanCmd ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null) {
            LOGGER.warn("SyncRedDotToClanCmd cant find clan: {}", getClanId());
            return;
        }
        if (ask.getType() == CommonEnum.RedDotKey.RDK_CLAN_WAR) {
            if (ask.getIsNewRally()) {
                clanEntity.getRedDotComponent().tryAddWarRedDot(ask.getType(), ask.getRallyId());
            } else {
                clanEntity.getRedDotComponent().tryRemoveWarRedDot(ask.getType(), ask.getRallyId());
            }
        } else {
            LOGGER.error("wrong type {} have been sent", ask.getType());
        }
    }

    @Override
    public void handleFetchResourcesSnapshotAsk(FetchResourcesSnapshotAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        // 刷新资源
        clanEntity.getResourcesComponent().settleClanResources();
        // 构造返回包
        FetchResourcesSnapshotAns.Builder ans = FetchResourcesSnapshotAns.newBuilder();
        ans.setResources(clanEntity.getProp().getResources().getCopySsBuilder());
        clanActor.answer(ans.build());
    }

    @Override
    public void handleFetchClanWareHouseInfoAsk(FetchClanWareHouseInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        clanEntity.getResourcesComponent().settleClanResources();
        SsClanAttr.FetchClanWareHouseInfoAns.Builder builder = SsClanAttr.FetchClanWareHouseInfoAns.newBuilder();
        builder.setResource(clanEntity.getWareHouseComponent().getAllWarehouseInfo());
        clanActor.answer(builder.build());
    }

    @Override
    public void handleOnAddClanScoreForClanCmd(OnAddClanScoreForClanCmd cmd) {
        if (!cmd.hasReason() || !cmd.hasScore()) {
            LOGGER.warn("reason {} or score {} is null", cmd.getReason(), cmd.getScore());
            return;
        }
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleOnAddClanScoreForClanCmd clan is null={}", clanActor.getClanId());
            return;
        }
        clanEntity.getResourcesComponent().addClanScore(cmd.getReason(), cmd.getScore(), cmd.getEntityId());
    }

    @Override
    public void handleCreateClanLogCmd(CreateClanLogCmd cmd) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            LOGGER.warn("handleCreateClanLogCmd clan is null={}", clanActor.getClanId());
            return;
        }
        switch (cmd.getRecordType()) {
            case CRT_LOG_REBUILD:
                clanEntity.getLogComponent().logRebuild(cmd.getRebuildLogMsg());
                break;
            case CRT_LOG_DESTROY:
                clanEntity.getLogComponent().logDestroy(cmd.getDestroyLogMsg());
                break;
            case CRT_LOG_BUILDING_ATTACKED:
                clanEntity.getLogComponent().logBeAttacked(cmd.getBeAttackedLogMsg());
                break;
            case CRT_LOG_START_ATTACK:
                clanEntity.getLogComponent().logStartAttack(cmd.getStartAttackLogMsg());
                break;
            case CRT_LOG_OCCUPY:
                clanEntity.getLogComponent().logOccupy(cmd.getOccupyLogMsg());
                break;
            default:
                LOGGER.error("wrong record type {} have been sent", cmd.getRecordType());
                break;
        }
    }

    @Override
    public void handleFetchClanLogAsk(FetchClanLogAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        ClanLogRecordModelProp prop = clanEntity.getLogComponent().getRecords();
        clanActor.answer(FetchClanLogAns.newBuilder().setLogRecords(prop.getCopySsBuilder()).build());
    }

    @Override
    public void handleFetchDissolvingInfoAsk(FetchDissolvingInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        ClanStageModelProp prop = clanEntity.getStageComponent().fetchDissolveClanInfo();
        clanActor.answer(FetchDissolvingInfoAns.newBuilder().setInfo(prop.getCopySsBuilder()).build());
    }

    @Override
    public void handleIDIPQueryClanInfoAsk(IDIPQueryClanInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        IDIPQueryClanInfoAns.Builder builder = IDIPQueryClanInfoAns.newBuilder()
                .setClanId(clanEntity.getEntityId())
                .setCreateTimeMs(clanEntity.getProp().getCreateTime())
                .setOwnerId(clanEntity.getProp().getOwnerId())
                .setClanName(clanEntity.getProp().getBase().getName())
                .setClanSName(clanEntity.getProp().getBase().getSname())
                .setGiftLv(clanEntity.getGiftComponent().getGiftLevel())
                .setPower(clanEntity.getProp().getCombat())
                .setMemberNum(clanEntity.getMemberComponent().getMemberNum())
                .setOwnerName(clanEntity.getMemberComponent().getClanOwnerName())
                .setDescribe(clanEntity.getProp().getBase().getDescribe());
        clanActor.answer(builder.build());
    }

    @Override
    public void handleIDIPResetClanInfoAsk(IDIPResetClanInfoAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntityDissAsNil();
        if (clanEntity == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        boolean resetClanName = ask.getResetClanName();
        boolean resetClanDescribe = ask.getResetClanDescribe();
        boolean resetClanShortName = ask.getResetClanShortName();
        int quietSecondTime = ask.getQuietSecondTime();
        clanEntity.getSettingComponent().resetClanInfo(resetClanName, resetClanDescribe, resetClanShortName, quietSecondTime);
        clanActor.answer(IDIPResetClanInfoAns.getDefaultInstance());
    }

    @Override
    public void handleFetchClanNumTipAsk(FetchClanNumTipAsk ask) {
        ClanEntity clanEntity = clanActor.getOrLoadClanEntity();
        if (clanEntity == null || clanEntity.isDestroy()) {
            LOGGER.warn("fetchClanNumTip, getOrLoadClanFailed");
            clanActor.answer(FetchClanNumTipAns.getDefaultInstance());
            return;
        }
        FetchClanNumTipAns.Builder ret = clanEntity.getMemberComponent().getNumTip();
        clanActor.answer(ret.build());
    }
}

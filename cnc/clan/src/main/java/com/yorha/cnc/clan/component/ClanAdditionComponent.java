package com.yorha.cnc.clan.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.clan.ClanEntity;
import com.yorha.cnc.clan.addition.ClanAdditionMgr;
import com.yorha.cnc.clan.enums.ClanResourceMapType;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.game.gen.prop.WarehouseInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class ClanAdditionComponent extends ClanComponent {
    public static final Logger LOGGER = LogManager.getLogger(ClanAdditionComponent.class);

    private ClanAdditionMgr additionMgr;

    private ActorTimer reloadResTask;

    public ClanAdditionComponent(ClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        additionMgr = new ClanAdditionMgr(getOwner());
        // 注册会提供addition的component
        additionMgr.register(getOwner().getDevBuffComponent());
        additionMgr.register(getOwner().getTechComponent());
    }

    @Override
    public void onLoad() {
        refreshAllAddition();
    }

    public void refreshAllAddition() {
        getOwner().getDevBuffComponent().refreshAdditionCache();
        getOwner().getTechComponent().refreshAdditionMap();
        additionMgr.refreshAllAddition();
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionId   加成id
     */
    public void updateAddition(AdditionProviderType providerType, int additionId) {
        if (additionId > 0) {
            additionMgr.updateAdditionByProvider(providerType, Lists.newArrayList(additionId));
        } else {
            LOGGER.error("clan:{}, updateAddition, additionId:{} invalid", getOwner(), additionId);
        }
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionIds  加成id list
     */
    public void updateAddition(AdditionProviderType providerType, @NotNull List<Integer> additionIds) {
        additionMgr.updateAdditionByProvider(providerType, additionIds);
    }

    public long getAddition(int additionId) {
        return additionMgr.getAddition(additionId);
    }

    public long getAddition(CommonEnum.BuffEffectType type) {
        return getAddition(type.getNumber());
    }

    /**
     * 填充玩家登录军团后获取的player侧加成信息
     *
     * @param additionInfoBuilder 加成信息的构造器
     */
    public void fillCheckInAdditionInfo(CommonMsg.CheckInAdditionInfo.Builder additionInfoBuilder) {
        additionInfoBuilder.getAdditionBuilder().getAdditionBuilder().putAllDatas(getAdditionInfo(false));
    }

    /**
     * 填充玩家登录军团后获取的scene侧加成信息
     */
    public void fillSceneAdditionInfo(Struct.AdditionSys.Builder additionBuilder) {
        additionBuilder.getAdditionBuilder().putAllDatas(getAdditionInfo(true));
    }

    private Map<Integer, Struct.Addition> getAdditionInfo(boolean isScene) {
        Map<Integer, Struct.Addition> map = Maps.newHashMap();
        AdditionSysProp additionSys = getOwner().getProp().getAdditionSys();
        for (Integer id : additionSys.getAddition().keySet()) {
            if (AdditionUtil.isClanAddition(id)) {
                continue;
            }
            // isScene=true 返回scene上的加成；isScene=false 返回player上的加成
            var sceneAddition = isScene && AdditionUtil.isSceneAddition(id);
            var playerAddition = !isScene && !AdditionUtil.isSceneAddition(id);
            if (sceneAddition || playerAddition) {
                Struct.Addition.Builder builder = Struct.Addition.newBuilder();
                additionSys.getAdditionV(id).copyToSs(builder);
                map.put(id, builder.build());
            }
        }
        return map;
    }

    /**
     * @param additionId 加成id
     * @return 根据加成id返回加成来源
     */
    public Map<Integer, StructPB.AdditionItemPB> getAdditionSourceMapByAdditionId(int additionId) {
        Map<Integer, StructPB.AdditionItemPB> retMap = new HashMap<>();
        AdditionProp additionProp = getOwner().getProp().getAdditionSys().getAdditionV(additionId);
        if (null == additionProp) {
            return retMap;
        }
        for (int sourceId : additionProp.getAdditionItems().keySet()) {
            retMap.put(sourceId, additionProp.getAdditionItemsV(sourceId).getCopyCsBuilder().build());
        }
        return retMap;
    }

    public static void onClanMemberAdditionChange(ClanEntity entity, int additionId, Long newValue) {
        int newNum = (int) (ResHolder.getResService(ConstClanKVResService.class).getTemplate().getClanMaxNum() + newValue);
        LOGGER.info("{} onClanMemberAdditionChange old: {} new: {} ", entity, entity.getProp().getNumMax(), newNum);
        entity.getProp().setNumMax(newNum);
        // 军团card更新：低频，最大人数变更实时更新
        entity.getPropComponent().updateClanCardCache(true);
    }

    public static void onClanCurrencyAdditionChange(ClanEntity entity, int additionId, Long newValue) {
        entity.getResourcesComponent().settleClanResources();
        for (ClanResourceMapType clanResourceMapType : ClanResourceMapType.values()) {
            if (clanResourceMapType.getProduceBuffId().getNumber() == additionId) {
                WarehouseInfoProp wareHouseInfoProp = entity.getProp().getResources().getWarehouseMapV(clanResourceMapType.getResourceType().getNumber());
                if (null == wareHouseInfoProp) {
                    LOGGER.info("wareHouseInfoProp {} is null, add new prop", clanResourceMapType.getResourceType());
                    WarehouseInfoProp newProp = new WarehouseInfoProp();
                    newProp.setType(clanResourceMapType.getResourceType().getNumber()).setBaseProducePerHour(newValue);
                    entity.getProp().getResources().putWarehouseMapV(newProp);
                } else {
                    LOGGER.info("{} onClanCurrencyAdditionChange old: {} new: {}", entity, wareHouseInfoProp.getBaseProducePerHour(), newValue);
                    wareHouseInfoProp.setBaseProducePerHour(newValue);

                }
                return;
            }
        }
    }

    public void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        //预防上一个task未执行，再次更新了表格
        cancelReloadResTask();

        long initialDelay = RandomUtils.nextInt(60000);
        reloadResTask = ownerActor().addTimer(
                getEntityId(),
                TimerReasonType.CLAN_RELOAD_RES_TASK,
                this::dispatchReloadResTask,
                initialDelay, TimeUnit.MILLISECONDS);
        LOGGER.info("{} addReloadResTask, now={}, initialDelay={}", getOwner(), SystemClock.now(), initialDelay);
    }

    @Override
    public void onDestroy() {
        cancelReloadResTask();
    }

    @Override
    public void onDissolution() {
        cancelReloadResTask();
    }

    private void dispatchReloadResTask() {
        final long start = SystemClock.now();
        reloadResTask = null;
        refreshAllAddition();
        LOGGER.info("ClanAdditionComponent dispatchReloadResTask cost:{} ", SystemClock.now() - start);
    }

    private void cancelReloadResTask() {
        if (reloadResTask != null) {
            reloadResTask.cancel();
            reloadResTask = null;
        }
    }
}

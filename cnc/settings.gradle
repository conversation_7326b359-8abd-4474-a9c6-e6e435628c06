if (JavaVersion.current() < JavaVersion.VERSION_21) {
    throw new GradleException("JDK 21+ required. Current: ${JavaVersion.current()}")
}

rootProject.name = 'cnc'


// cnc
include 'common'

// actor
include 'idip'
include 'gate'
include 'name'
include 'rank'
include 'zone-chat'
include 'player'
include 'clan'
include 'scene'
include 'translator'
include 'dungeon'
include 'text-filter'
include 'midas-agent'
include 'midas-callback-session'
include 'group-chat'
include 'battle'
include 'zoneCard'
include 'auth'
include 'playercard'
include 'clancard'
include 'push-notification'
include 'anti-addiction'

// proj
include 'game'
include 'ping'
include 'yorha-directory'
include 'yorha-robot'

// tools
include 'websvr'
include 'encryption' // 加密工具
include 'codereview'
include 'monitor'


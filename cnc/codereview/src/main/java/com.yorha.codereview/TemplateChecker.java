package com.yorha.codereview;


import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.IResTemplate;
import net.sourceforge.pmd.lang.java.ast.ASTFieldDeclaration;
import net.sourceforge.pmd.lang.java.ast.ASTType;
import net.sourceforge.pmd.lang.java.rule.AbstractJavaRule;
import net.sourceforge.pmd.lang.java.typeresolution.typedefinition.JavaTypeDefinition;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class Template<PERSON>hecker extends AbstractJavaRule {

    @Override
    public Object visit(ASTFieldDeclaration node, Object data) {
        if (node.getTypeDefinition() == null) {
            return super.visit(node, data);
        }
        Class<?> nodeType = node.getTypeDefinition().getType();
        // 过滤下。。
        if (nodeType.getPackage() != null && nodeType.getPackage().getName().contains("com.yorha.game.gen")) {
            return super.visit(node, data);
        }
        if (Map.class.isAssignableFrom(nodeType) || Collection.class.isAssignableFrom(nodeType)) {
            if (checkMap(node.getTypeDefinition())) {
                asCtx(data).addViolation(node);
            }
            return super.visit(node, data);
        }
        for (ASTType type : node.findChildrenOfType(ASTType.class)) {
            Class<?> clazz = type.getType();
            if (clazz == null) {
                continue;
            }
            if (checkTemplate(clazz)) {
                asCtx(data).addViolation(node);
                break;
            }
        }
        return super.visit(node, data);
    }

    private boolean checkMap(JavaTypeDefinition definition) {
        for (int i = 0; i < definition.getTypeParameterCount(); i++) {
            Class clazz = definition.getGenericType(i).getType();
            if (Map.class.isAssignableFrom(clazz) || Collection.class.isAssignableFrom(clazz)) {
                if (checkMap(definition.getGenericType(i))) {
                    return true;
                }
                continue;
            }
            if (checkTemplate(clazz)) {
                return true;
            }
        }
        return false;
    }

    private boolean checkTemplate(Class clazz) {
        if (IResTemplate.class.isAssignableFrom(clazz)) {
            return true;
        }
        if (AbstractResService.class.isAssignableFrom(clazz)) {
            return true;
        }
        return false;
    }
}

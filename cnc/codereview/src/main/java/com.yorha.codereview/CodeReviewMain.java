package com.yorha.codereview;


import net.sourceforge.pmd.PMD;
import org.dom4j.DocumentException;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class CodeReviewMain {

    public static void main(String[] args) throws IOException, DocumentException {
//        // 将 RuleSet 写入 XML 文件
//        Rule rule = new TemplateChecker();
//        RuleSet ruleSet = RuleSet.forSingleRule(rule);
//        OutputStream outputStream = new FileOutputStream("template_rule");
//        RuleSetWriter writer = new RuleSetWriter(outputStream);
//        writer.write(ruleSet);
//        outputStream.close();
        long startTsMs = System.currentTimeMillis();
        //String path = ".\\";
        String path = ".\\battle\\src\\main\\java\\com\\yorha\\cnc\\battle\\buf";
        if (args.length > 0) {
            path = args[0];
        }
        String[] pmdArgs = {
                "-d", path,
                "--ignore-list", "ignore.txt",
                "-R", "rulesets/template_rule.xml",
                "-f", "text",
                "--report-file", "CodeReviewResult.txt"
        };
        PMD.runPmd(pmdArgs);
        long endTsMs = System.currentTimeMillis();
        StringBuilder builder = new StringBuilder();
        builder.append("cost: ").append(endTsMs - startTsMs).append("ms").append("\n");
        try (BufferedReader reader = new BufferedReader(new FileReader("CodeReviewResult.txt"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                int index = line.indexOf(":");
                if (index < 0) {
                    continue;
                }
                String substring = line.substring(0, index);
                index = substring.lastIndexOf("\\");
                if (index < 0) {
                    continue;
                }
                builder.append(substring.substring(index + 1)).append("\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        FileOutputStream steamOut = new FileOutputStream("CodeReviewResult");
        steamOut.write(builder.toString().getBytes(StandardCharsets.UTF_8));
        steamOut.close();
    }

}

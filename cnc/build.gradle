import com.github.spotbugs.snom.SpotBugsTask
import org.codehaus.groovy.runtime.GStringImpl

// 插件配置
plugins {
    id 'com.github.spotbugs' version '6.0.26' apply false
    id 'com.yorha.git-version'
}


ext.sharedGitInfo = gitInfo.version.get() as GStringImpl
// 全项目配置
allprojects {
    // 包仓库
    repositories {
        // 腾讯云的国内镜像
        maven {
            url = uri('https://mirrors.cloud.tencent.com/nexus/repository/maven-public')
        }
        // 阿里云镜像作为备用
        maven {
            url = uri('https://maven.aliyun.com/repository/public')
        }
        // Maven中央仓库
        mavenCentral()
    }
}

// 子项目配置
subprojects {
    /**
     * 相比java插件增加了api依赖关键词
     */
    apply plugin: "java-library"
    apply plugin: 'maven-publish'
    apply plugin: 'signing'
    apply plugin: 'pmd'
    apply plugin: 'checkstyle'
    apply plugin: 'com.github.spotbugs'

    // 统一依赖版本管理
    configurations.all {
        resolutionStrategy {
            // 强制使用统一的protobuf版本
            force 'com.google.protobuf:protobuf-java:3.12.0'
            force 'com.google.protobuf:protobuf-java-util:3.12.0'
        }
    }

    // 定义代码group id
    group = "com.yorha"

    ext {
        releaseRoot = '../build'
    }

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21) // 保持项目编译目标为 JDK 21
        }
    }

    tasks.withType(JavaCompile).configureEach {
        options.encoding = 'UTF-8'
        options.compilerArgs.add('-XDignore.symbol.file')
    }

    // 定义资源集合
    sourceSets {
        // main包
        main
        // test包
        test
        // jmh包
        jmh
    }


    tasks.register('createDirs') {
        sourceSets*.java.srcDirs*.each { it.mkdirs() }
        sourceSets*.resources.srcDirs*.each { it.mkdirs() }

    }


    jar {
        archiveFileName.set("${project.name}.jar")
        manifest {
            attributes(
                    "Provider": "Ruyi Jingxiu Game Company",
                    "Created-By": "${System.getProperty('java.version')} (${System.getProperty('java.specification.vendor')})",
                    "Implementation-Title": project.name,
                    "Implementation-Version":  rootProject.ext.sharedGitInfo,
                    "Build-Time": new Date().format('yyyy-MM-dd HH:mm:ss')
            )
        }
    }


    tasks.withType(Javadoc).configureEach {
        options.encoding = 'UTF-8'
        options.charSet = 'UTF-8'
        options.author = true
        options.version = true
        options.header = project.name
        options.links = ["https://docs.oracle.com/javase/21/docs/api/"]
        options.addBooleanOption('html5', true)
    }

    tasks.register('javadocJar', Jar) {
        dependsOn javadoc
        from tasks.named('javadoc').get().outputs.files
        archiveClassifier = 'javadoc'
    }

    tasks.register('sourcesJar', Jar) {
        archiveClassifier = 'sources'
        from sourceSets.main.allJava
    }


    // 代码质量工具配置
    pmd {
        toolVersion = '7.15.0'
        consoleOutput = true
        ruleSetFiles = files("${rootProject.projectDir}/config/pmd/alibaba-pmd-ruleset.xml")
        ruleSets = [] // 清空默认规则集，使用自定义规则集
        ignoreFailures = false
        incrementalAnalysis = false // 禁用增量分析以避免重复输出
        threads = Runtime.runtime.availableProcessors() // 使用多线程提高性能
    }

    checkstyle {
        toolVersion = '10.25.0'
        configFile = file("${rootProject.projectDir}/config/checkstyle/alibaba-checkstyle.xml")
        ignoreFailures = false
        showViolations = true
        maxWarnings = 0
        maxErrors = 0
    }

    spotbugs {
        toolVersion = '4.9.3'
        ignoreFailures = false
        showStackTraces = true
        showProgress = true
        excludeFilter = file("${rootProject.projectDir}/config/spotbugs/exclude.xml")
    }

    // 排除生成的代码
    tasks.withType(Pmd).configureEach {
        // 确保PMD在编译后运行，并且能访问编译后的类信息
        dependsOn compileJava
        // 设置类路径包含编译后的类和依赖
        classpath = sourceSets.main.compileClasspath// + sourceSets.main.output
        reports {
            xml.required = false
            html.required = true
        }
        // 确保只输出一次结果
        doFirst {
            logger.lifecycle("Running PMD analysis...")
        }
    }

    // 专门配置PMD源码集，排除生成的代码
    tasks.named('pmdMain') {
        source = sourceSets.main.allJava.matching {
            exclude '**common/src/main/gen/**'  // 排除任何位置的gen目录
            exclude 'com/yorha/game/gen/**'  // 排除proto生成的代码
            exclude 'com/yorha/proto/**'  // 排除proto生成的代码
            exclude 'com/yorha/robot/**' // 排除robot模块
            exclude 'com/yorha/cnc/battle/**' // 排除battle模块
            exclude 'com/yorha/cnc/idip/**' // 排除idip模块
            exclude 'com/yorha/cnc/scene/**' // scene

            exclude 'com/yorha/common/Easy3dNav/**'  // 排除easy3dnav的代码
            exclude 'com/yorha/common/actor/ClanServices.java'
            exclude 'com/yorha/common/actor/PlayerServices.java'
            exclude 'com/yorha/common/actor/SceneServices.java'
            exclude 'com/yorha/common/io/MsgType.java'
            exclude 'com/yorha/common/utils/ip/IpSeekerUtils.java'
            exclude 'com/yorha/common/utils/time/SystemClock.java'
            exclude 'com/yorha/common/db/tcaplus/TcaplusPerformanceTest.java'
            exclude 'com/yorha/common/concurrent/DaemonThreadFactory.java'
            exclude 'com/yorha/common/concurrent/NameableThreadFactory.java'
            exclude 'com/yorha/common/dbactor/DbTaskProxy.java'
            exclude 'com/yorha/common/etcd/EtcdKeyValueKeeper.java'
            exclude 'com/yorha/common/etcd/EtcdKeyValueWatcher.java'
            exclude 'com/yorha/common/exports/GeminiMemoryPoolsExports.java'
            exclude 'com/yorha/common/exports/GeminiStandardExports.java'
            exclude 'com/yorha/common/lock/SpinLock.java'
            exclude 'com/yorha/common/msgcounter/GeminiMsgCounter.java'
            exclude 'com/yorha/common/resource/resservice/task/TaskTemplateService.java'
            exclude 'com/yorha/common/utils/shape/Rectangle.java'
            exclude 'com/yorha/common/utils/FormulaUtils.java'
            exclude 'com/yorha/common/utils/ServerStatisticLog.java'
            exclude 'com/yorha/common/utils/jol/JolParser.java'
            exclude 'com/yorha/common/rank/RankPageDTO.java'
            exclude 'com/yorha/common/msgcounter/MsgCounter.java'
            exclude 'com/yorha/common/chat/MegaGroupChatEntity.java'

            exclude 'com/yorha/directory/utils/DriveTrafficHelper.java'

            exclude 'com/yorha/gemini/props/AbstractListNode.java'
            exclude 'com/yorha/gemini/props/AbstractPropNode.java'

            exclude 'com/yorha/cnc/player/chat/component/ChatPlayerHandleChatComponent.java'
            exclude 'com/yorha/cnc/player/component/PlayerPaymentComponent.java'
            exclude 'com/yorha/cnc/player/item/ItemUtils.java'

            exclude 'com/yorha/game/GameServer.java'
            exclude 'io/nats/client/impl/MessageQueue.java'

            exclude 'com/yorha/cnc/zone/component/MileStoneMgrComponent.java'
            exclude 'com/yorha/common/resource/resservice/shop/ShopDataTemplateService.java'
            exclude 'com/yorha/common/resource/resservice/language/ServerLanguageTemplateService.java'
        }
    }

    tasks.withType(Checkstyle).configureEach {
        reports {
            xml.required = false
            html.required = true
        }
    }

    tasks.withType(SpotBugsTask).configureEach {
        reports {
            xml.required = false
            html.required = true
        }
    }

    dependencies {
        // jetbrain
        implementation group: 'org.jetbrains', name: 'annotations', version: '26.0.2'
        //Junit5
        testImplementation 'org.junit.platform:junit-platform-launcher:1.13.0-M3'
        testImplementation 'org.junit.jupiter:junit-jupiter-api:5.13.0-M3'
        testImplementation 'org.junit.jupiter:junit-jupiter-params:5.13.0-M3'
        testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.13.0-M3'
        testImplementation 'org.mockito:mockito-junit-jupiter:5.17.0'
        testImplementation 'org.mockito:mockito-core:5.17.0'

        // 保证jmh目录下java代码能顺利import其他main目录下等代码
        jmhImplementation project
        jmhImplementation 'org.openjdk.jmh:jmh-core:1.37'
        jmhAnnotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess:1.37'
        compileOnly group: 'org.apache.skywalking', name: 'apm-agent-core', version: '8.11.0'

        // 阿里巴巴 P3C PMD 规则
        pmd 'com.alibaba.p3c:p3c-pmd:2.1.1'
    }


    test {
        useJUnitPlatform()
        jvmArgs '-Dfile.encoding=UTF-8'
        maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
        ignoreFailures = false
        failFast = true
        testLogging {
            showStandardStreams = true
            events = ["passed", "failed", "skipped"]
            exceptionFormat = "full"
        }

        afterSuite { desc, result ->
            if (!desc.parent) {
                println """
               Tests Summary:
               Total: ${result.testCount}
               Passed: ${result.successfulTestCount}
               Failed: ${result.failedTestCount}
               Skipped: ${result.skippedTestCount}
               -------------------------
               """.stripIndent()
            }
        }


        reports.html.required = true
        reports.junitXml.required = false
    }
}


// 汇总 Gradle 多模块测试结果
tasks.register('testReport', TestReport) {
    // 设置输出目录
    destinationDirectory.set(layout.buildDirectory.dir("reports/allTests"))
    // 添加要聚合的测试结果（并设置 ignoreFailures）
    testResults.from(
            subprojects.collect {
                def testTask = it.tasks.test
                testTask.ignoreFailures = true
                testTask.binaryResultsDirectory
            }
    )
}

// 禁用所有代码质量检查任务的自动执行（仅在git runner中执行）
subprojects {
    tasks.withType(Checkstyle).configureEach {
        def runCodeQuality = project.hasProperty('runCodeQuality')
        onlyIf { runCodeQuality }
    }

    tasks.withType(Pmd).configureEach {
        def runCodeQuality = project.hasProperty('runCodeQuality')
        onlyIf { runCodeQuality }
    }

    tasks.withType(SpotBugsTask).configureEach {
        def runCodeQuality = project.hasProperty('runCodeQuality')
        onlyIf { runCodeQuality }
    }
}

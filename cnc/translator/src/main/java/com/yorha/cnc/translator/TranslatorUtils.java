package com.yorha.cnc.translator;

import com.google.api.gax.retrying.RetrySettings;
import com.google.cloud.translate.Translate;
import com.google.cloud.translate.TranslateOptions;
import com.google.cloud.translate.Translation;
import com.google.protobuf.Message;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.codec.binary.Hex;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * Tranlator的工具类。
 *
 * <AUTHOR>
 */
public final class TranslatorUtils {
    public static final int SHORT_LONG_DIVIDING_RUNE_LENGTH = 64;
    private static final Logger LOGGER = LogManager.getLogger(TranslatorUtils.class);
    private static volatile Translate googleTranslate;

    /**
         * 用于翻译的Key。
         */
        record TranslatedKey(int hashId, String payloadHash, String language) {

        @Override
            public int hashCode() {
                int result = hashId;
                result = 31 * result + (language != null ? language.hashCode() : 0);
                return result;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {
                    return true;
                }
                if (o == null || getClass() != o.getClass()) {
                    return false;
                }
                TranslatedKey that = (TranslatedKey) o;
                if (hashId != that.hashId) {
                    return false;
                }
                if (!StringUtils.equals(this.language, that.language)) {
                    return false;
                }
                return StringUtils.equals(this.payloadHash, that.payloadHash);
            }

            @Override
            public @NotNull String toString() {
                return "TranslatedKey{" +
                        "hashId=" + hashId +
                        ", payloadHash='" + payloadHash + '\'' +
                        ", language='" + language + '\'' +
                        '}';
            }
        }

    /**
     * 翻译的结果。
     */
    static class TranslatedContent {
        public final String text;
        public final String translatedText;
        public final long timeStampMs;

        TranslatedContent(String text, String translatedText) {
            this.text = text;
            this.translatedText = translatedText;
            this.timeStampMs = SystemClock.nowNative();
        }

        /**
         * 是否需要更新LRU。
         *
         * @return 更新LRU。
         */
        public boolean isNeedRefreshLru() {
            return TimeUnit.MILLISECONDS.toSeconds(SystemClock.nowNative() - this.timeStampMs) > 600;
        }
    }

    /**
     * 翻译一段语言。
     *
     * @param actor 发送方Actor。
     * @param key   翻译key。
     * @param text  待翻译的text。
     * @param watch 秒表
     * @return 翻译结果。
     */
    public static TranslatedContent translateTextGoogle(final AbstractActor actor, final TranslatedKey key, final String text, final GeminiStopWatch watch) {
        // 1. 从数据库 cache中读取数据
        watch.mark("ts-cache-load");
        final Message.Builder ans = getSentence(actor, key, text);
        // 2. 读取成功
        if (ans != null) {
            if (isLongSentence(text)) {
                TcaplusDb.TranslateLongSentenceTable.Builder res = (TcaplusDb.TranslateLongSentenceTable.Builder) ans;
                if (res.getText().equals(text)) {
                    // 刷新缓存
                    TranslatorUtils.TranslatedContent value = new TranslatorUtils.TranslatedContent(text, res.getTranslatedText());
                    watch.mark("ts-cache-update");
                    updateSentence(actor, key, value);
                    return value;
                }
            } else {
                // 刷新缓存
                TcaplusDb.TranslateShortSentenceTable.Builder res = (TcaplusDb.TranslateShortSentenceTable.Builder) ans;
                TranslatorUtils.TranslatedContent value = new TranslatorUtils.TranslatedContent(text, res.getTranslatedText());
                watch.mark("ts-cache-update");
                updateSentence(actor, key, value);
                return value;
            }
        }
        // 3. google 翻译
        watch.mark("ts-google-translate");
        final Translation translation = getGoogleTranslate()
                .translate(text,
                        Translate.TranslateOption.targetLanguage(key.language),
                        Translate.TranslateOption.format("text")
                );
        TranslatorUtils.TranslatedContent value = new TranslatedContent(text, translation.getTranslatedText());
        watch.mark("ts-cache-insert");
        insertSentence(actor, key, value);
        return value;
    }


    /**
     * 是否长段落。(注意性能：会被常常调到)
     *
     * @param text 信息。
     * @return true 是 or false 否。
     */
    public static boolean isLongSentence(final String text) {
        return text.length() > TranslatorUtils.SHORT_LONG_DIVIDING_RUNE_LENGTH;
    }

    /**
     * 计算信息的摘要。
     *
     * @param body 待计算信息摘要体。
     * @return 信息摘要。
     */
    public static String calcPayloadHash(String body) {
        try {
            final MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(body.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(md.digest());
        } catch (NoSuchAlgorithmException e) {
            return body.substring(TranslatorUtils.SHORT_LONG_DIVIDING_RUNE_LENGTH);
        }
    }

    /**
     * 读取sentence。
     *
     * @param actor 发送actor。
     * @param key   翻译Key。
     * @return 获取消息。读取不了则返回null。
     */
    @Nullable
    public static Message.Builder getSentence(final AbstractActor actor, final TranslatedKey key, final String text) {
        final Message.Builder msg = isLongSentence(text) ?
                TcaplusDb.TranslateLongSentenceTable.newBuilder()
                        .setHashId(key.hashId)
                        .setPayloadHash(key.payloadHash)
                        .setLanguage(key.language) :
                TcaplusDb.TranslateShortSentenceTable.newBuilder()
                        .setHashId(key.hashId)
                        .setLanguage(key.language)
                        .setText(text);
        SelectUniqueAsk<Message.Builder> ask = new SelectUniqueAsk<>(msg);
        try {
            final GetResult<Message.Builder> ans = actor.call(RefFactory.dbActorRef(), ask);
            if (DbUtil.isOk(ans.getCode())) {
                return ans.value;
            }
            if (!DbUtil.isRecordNotExist(ans.getCode())) {
                LOGGER.error("translate get sentence fail! msg={}, code={}", msg, ans.getCode());
            }
        } catch (Exception exception) {
            LOGGER.error("translate from cache msg={}, language={} fail!", msg, exception);
        }
        return null;
    }

    /**
     * 插入段。
     *
     * @param actor 承载Actor。
     * @param key   翻译的Key。
     * @param value 翻译的结果。
     * @return 结果。
     */
    public static void insertSentence(final AbstractActor actor, final TranslatedKey key, final TranslatedContent value) {
        final Message.Builder msg = TranslatorUtils.isLongSentence(value.text) ?
                TcaplusDb.TranslateLongSentenceTable.newBuilder()
                        .setHashId(key.hashId)
                        .setPayloadHash(key.payloadHash)
                        .setLanguage(key.language)
                        .setText(value.text)
                        .setTranslatedText(value.translatedText)
                :
                TcaplusDb.TranslateShortSentenceTable.newBuilder()
                        .setHashId(key.hashId)
                        .setLanguage(key.language)
                        .setText(value.text)
                        .setTranslatedText(value.translatedText);
        InsertAsk<Message.Builder> insertAsk = new InsertAsk<>(msg);
        try {
            InsertResult<Message.Builder> ans = actor.call(RefFactory.dbActorRef(), insertAsk);
            if (DbUtil.isOk(ans.getCode())) {
                return;
            }
            if (!DbUtil.isRecordAlreadyExist(ans.getCode())) {
                LOGGER.error("translate insert sentence fail! msg={}, code={}", msg, ans.getCode());
            }
        } catch (Exception exception) {
            LOGGER.error("insert fail! ask={}", insertAsk, exception);
        }
    }

    /**
     * 更新段，当段不存在，会尝试插入，不允许再insertSentence中调用。
     *
     * @param actor 承载Actor。
     * @param key   翻译的key。
     * @param value 翻译的结果。
     * @return false 表示记录不存在；true表示记录存在
     */
    public static void updateSentence(AbstractActor actor, final TranslatedKey key, final TranslatedContent value) {
        // 更新主要是用来刷新过期时间的，因此只需要使用primary key进行刷新。
        final Message.Builder msg = TranslatorUtils.isLongSentence(value.text) ?
                TcaplusDb.TranslateLongSentenceTable.newBuilder()
                        .setHashId(key.hashId)
                        .setPayloadHash(key.payloadHash)
                        .setLanguage(key.language) :
                TcaplusDb.TranslateShortSentenceTable.newBuilder()
                        .setHashId(key.hashId)
                        .setLanguage(key.language)
                        .setText(value.text);
        UpdateAsk<Message.Builder> updateAsk = new UpdateAsk<>(msg);
        try {
            final UpdateResult<Message.Builder> ans = actor.call(RefFactory.dbActorRef(), updateAsk);
            if (DbUtil.isOk(ans.getCode())) {
                return;
            }
            if (!DbUtil.isRecordNotExist(ans.getCode())) {
                LOGGER.error("translate update sentence fail! msg={}, code={}", msg, ans.getCode());
                return;
            }
            insertSentence(actor, key, value);
        } catch (Exception exception) {
            LOGGER.error("update fail! ask={}", updateAsk, exception);
        }
    }

    /**
     * 获取一个翻译的客户端。
     *
     * @return 翻译客户端。
     */
    public static Translate getGoogleTranslate() {
        Translate translate = TranslatorUtils.googleTranslate;
        if (translate == null) {
            synchronized (TranslatorUtils.class) {
                translate = TranslatorUtils.googleTranslate;
                if (translate == null) {
                    final long timeOutMs = ServerContext.getRpcTimeout();
                    final TranslateOptions.Builder builder = TranslateOptions.newBuilder()
                            /*
                             * 重试配置前提：
                             * 1. 总尝试时间最大rpc_timeout秒。
                             * 2. 单次rpc超时时间1s。
                             * 3. 首次失败重试间隔10ms，再次执行则是上次delay时间*2，之后以此类推，最大达到200ms。
                             * 4. 重试次数上限10次，最大重试等待时长100ms。
                             */
                            .setRetrySettings(RetrySettings.newBuilder()
                                    .setTotalTimeout(org.threeten.bp.Duration.ofMillis(timeOutMs))
                                    .setInitialRpcTimeout(org.threeten.bp.Duration.ofMillis(1000))
                                    .setRpcTimeoutMultiplier(1.0)
                                    .setMaxRpcTimeout(org.threeten.bp.Duration.ofMillis(1000))
                                    .setInitialRetryDelay(org.threeten.bp.Duration.ofMillis(10))
                                    .setRetryDelayMultiplier(2.0)
                                    .setMaxAttempts(10)
                                    .setMaxRetryDelay(org.threeten.bp.Duration.ofMillis(200))
                                    .build());
                    TranslatorUtils.googleTranslate = translate = builder.build().getService();
                }
            }
        }
        return translate;
    }
}

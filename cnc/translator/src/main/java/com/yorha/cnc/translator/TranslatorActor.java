package com.yorha.cnc.translator;

import com.yorha.common.actor.TranslatorService;
import com.yorha.common.actor.TranslatorServices;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.actor.IActorRef;
import com.yorha.gemini.actor.msg.TypedMsg;

/**
 * 翻译Actor。
 *
 * <AUTHOR>
 */

public class TranslatorActor extends BaseGameActor implements TranslatorServices {
    private final TranslatorService translatorService;

    public TranslatorActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.translatorService = new TranslatorGoogleServiceImpl(this);
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    public TranslatorService getTranslatorService() {
        return this.translatorService;
    }
}

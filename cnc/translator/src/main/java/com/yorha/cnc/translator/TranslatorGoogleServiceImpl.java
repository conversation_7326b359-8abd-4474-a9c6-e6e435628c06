package com.yorha.cnc.translator;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.cloud.translate.Language;
import com.yorha.common.actor.TranslatorService;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.concurrent.singleflight.SingleFlightGroup;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.proto.SsTranslator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 基于GoogleApiV2（基础版）的翻译Service。
 *
 * <AUTHOR>
 */
public class TranslatorGoogleServiceImpl implements TranslatorService {
    private static final Logger LOGGER = LogManager.getLogger(TranslatorGoogleServiceImpl.class);
    private static final long SHORT_SENTENCE_CACHE_SIZE = 500000;
    private static final long LONG_SENTENCE_CACHE_SIZE = 15000;
    private static final long SHORT_SENTENCE_CACHE_MAX_MINUTES = 10;
    private static final long LONG_SENTENCE_CACHE_MAX_MINUTES = 100;

    /**
     * actor
     */
    private final TranslatorActor actor;
    /**
     * 短语翻译sf, key: (text, language), value: 翻译结果
     */
    private final SingleFlightGroup<TranslatorUtils.TranslatedKey, TranslatorUtils.TranslatedContent> shortSentenceSingleFlight = new SingleFlightGroup<>();
    /**
     * 长语翻译sf, key: (payload, language), value: 翻译结果，payload是用算法所处的信息摘要。
     */
    private final SingleFlightGroup<TranslatorUtils.TranslatedKey, TranslatorUtils.TranslatedContent> longSentenceSingleFlight = new SingleFlightGroup<>();
    /**
     * 短语翻译缓存, key: (text, language), value: 翻译结果
     */
    private final Cache<TranslatorUtils.TranslatedKey, TranslatorUtils.TranslatedContent> shortSentenceCache;
    /**
     * 长语言翻译缓存, key: (payload, language), value: 翻译结果，payload是用算法所处的信息摘要。
     */
    private final Cache<TranslatorUtils.TranslatedKey, TranslatorUtils.TranslatedContent> longSentenceCache;
    /**
     * 当前支持的语言信息列表。
     */
    private Set<String> supportLanguages;


    public TranslatorGoogleServiceImpl(TranslatorActor actor) {
        this.actor = actor;
        this.shortSentenceCache = Caffeine.newBuilder()
                .scheduler(SystemScheduleMgr.getInstance().getScheduler())
                .expireAfterAccess(Duration.ofMinutes(TranslatorGoogleServiceImpl.SHORT_SENTENCE_CACHE_MAX_MINUTES))
                .maximumSize(TranslatorGoogleServiceImpl.SHORT_SENTENCE_CACHE_SIZE)
                .removalListener((key, value, cause) -> LOGGER.debug("short sentence! key={} remove because cause={}", key, cause)).build();
        this.longSentenceCache = Caffeine.newBuilder()
                .scheduler(SystemScheduleMgr.getInstance().getScheduler())
                .expireAfterAccess(Duration.ofMinutes(TranslatorGoogleServiceImpl.LONG_SENTENCE_CACHE_MAX_MINUTES))
                .maximumSize(TranslatorGoogleServiceImpl.LONG_SENTENCE_CACHE_SIZE)
                .removalListener((key, value, cause) -> LOGGER.debug("long sentence! key={} remove because cause={}", key, cause)).build();
    }

    @Override
    public void handleTranslateTextAsk(SsTranslator.TranslateTextAsk ask) {
        final String text = ask.getText();
        final String targetLanguage = ask.getTargetLanguage();
        if (StringUtils.isEmpty(text)) {
            this.actor.answer(SsTranslator.TranslateTextAns.newBuilder().setText("").build());
            return;
        }
        if (!this.supportLanguage(targetLanguage)) {
            throw new GeminiException("translate illegal target language={}", targetLanguage);
        }
        this.translateText(text, targetLanguage);
    }

    /**
     * 检查是否支持语言
     *
     * @param language 目标语言。
     * @return yes 支持 or no 不支持
     */
    private boolean supportLanguage(String language) {
        if (this.supportLanguages != null) {
            return this.supportLanguages.contains(language);
        }
        GeminiStopWatch watch = new GeminiStopWatch("list support language");
        watch.mark("ts-list-language");
        List<Language> languageList = TranslatorUtils.getGoogleTranslate().listSupportedLanguages();
        this.supportLanguages = languageList.stream().map(Language::getCode).collect(Collectors.toSet());
        LOGGER.info("gemini_perf supportLanguage list support languages={}, clock={}", this.supportLanguages, watch.stat());
        return this.supportLanguages.contains(language);
    }

    /**
     * 执行翻译逻辑。
     *
     * @param text           待翻译语言。
     * @param targetLanguage 目标翻译语言。
     */
    private void translateText(final String text, final String targetLanguage) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        if (context == null) {
            throw new GeminiException("Envelop null!");
        }
        // 1. 准备执行上下文
        SingleFlightGroup<TranslatorUtils.TranslatedKey, TranslatorUtils.TranslatedContent> sf;
        String payloadHash;
        Cache<TranslatorUtils.TranslatedKey, TranslatorUtils.TranslatedContent> cache;
        if (TranslatorUtils.isLongSentence(text)) {
            sf = this.longSentenceSingleFlight;
            payloadHash = TranslatorUtils.calcPayloadHash(text);
            cache = this.longSentenceCache;
        } else {
            sf = this.shortSentenceSingleFlight;
            payloadHash = text;
            cache = this.shortSentenceCache;
        }
        final AbstractActor actor = this.actor;
        final TranslatorUtils.TranslatedKey key = new TranslatorUtils.TranslatedKey(text.hashCode(), payloadHash, targetLanguage);
        final GeminiStopWatch watch = new GeminiStopWatch(key);
        // 2. sf聚合访问流量
        sf.invokeAsync(
                key,
                () -> {
                    // 1. 已经存在，不翻译
                    watch.mark("ts-work-vt");
                    final TranslatorUtils.TranslatedContent value = cache.getIfPresent(key);
                    final boolean isRightText = value != null && value.text.equals(text);
                    if (isRightText && !value.isNeedRefreshLru()) {
                        return value;
                    }
                    // 续期
                    TranslatorUtils.TranslatedContent result;
                    if (isRightText) {
                        watch.mark("ts-cache-update");
                        TranslatorUtils.updateSentence(actor, key, value);
                        // 更新refresh时间戳
                        result = new TranslatorUtils.TranslatedContent(value.text, value.translatedText);
                    } else {
                        result = TranslatorUtils.translateTextGoogle(actor, key, text, watch);
                    }
                    cache.put(key, result);
                    return result;
                },
                (v, e) -> {
                    // 3. 翻译的异步回调
                    watch.mark("ts-work-res");
                    LOGGER.info("gemini_perf google translate key:{} {}", key, watch.stat());
                    if (e != null) {
                        LOGGER.error("translate text fail! text={}, language={}", text, targetLanguage, e);
                        actor.answerWithException(context, (Exception) e);
                        return;
                    }
                    if (v == null) {
                        LOGGER.error("translate text empty! text={}, language={}", text, targetLanguage);
                        actor.answerWithException(context, new GeminiException("translate text fail!"));
                        return;
                    }
                    if (!v.text.equals(text)) {
                        LOGGER.error("translate text hash conflict! text={}, language={}, value={}", text, targetLanguage, v.text);
                        actor.answerWithException(context, new GeminiException("translate text fail!"));
                        return;
                    }
                    actor.answerWithContext(context, SsTranslator.TranslateTextAns.newBuilder().setText(v.translatedText).build());
                });
    }
}

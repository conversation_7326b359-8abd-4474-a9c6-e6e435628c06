<?xml version="1.0" encoding="UTF-8"?>
<configuration status="warn" shutdownHook="disable">
    <properties>
        <property name="MY_NAME">websvr</property>
        <property name="LOG_HOME">${sys:LOG_ROOT}</property>
    </properties>
    <appenders>
        <!-- 控制台输出-->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
        </Console>
        <!-- 运行日志 -->
        <RollingRandomAccessFile name="DevLog" fileName="${LOG_HOME}/websvr"
                                 filePattern="${LOG_HOME}/websvr.%d{yyyy-MM-dd}" immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
        <!-- 性能统计日志 -->
        <RollingRandomAccessFile name="PerfStatAppender" fileName="${LOG_HOME}/perf_stat_log"
                                 filePattern="${LOG_HOME}/perf_stat_log.%d{yyyy-MM-dd}" immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
    </appenders>
    <loggers>
        <!-- 第三方日志等级 -->
        <Logger name="io.netty" level="warn"/>
        <Logger name="org.apache.commons" level="warn"/>
        <Logger name="org.reflections" level="warn"/>
        <Logger name="PerfStatLog" level="info" additivity="false">
            <appender-ref ref="PerfStatAppender"/>
        </Logger>
        <!-- Root Logger -->
        <Root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="DevLog"/>
        </Root>
    </loggers>
</configuration>
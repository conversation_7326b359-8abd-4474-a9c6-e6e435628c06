<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<html lang="en">
<body id="page-top">
<style>
    #about {
        left: 50%;
        top: 50%;
        transform: translate(-50%, 10%);
        font-family: Agenda-Light, Agenda Light, Agenda, <PERSON><PERSON>, sans-serif;
        font-weight: 100;
        background: rgba(0, 0, 0, 0.3);
        color: white;
        padding: 2rem;
        width: 33%;
        margin: 2rem;
        position: absolute;
        z-index: 2;
        font-size: 1.2rem;
    }
    #about {
        position: absolute;
        z-index: 3;
    }

    #about button:hover {
        background: rgba(0, 0, 0, .5)
    }

    #about {
        position: relative;
    }

</style>
<!-- About -->
 <div id="about">
     <br/>
     <br/>
     <div class="container">
         <h1>辅助测试工具</h1>
         <p>这是一段说明,介绍本工具的用法。
         </p>
     </div>
     <div class="container">
         <form id="robotForm" th:action="@{/zeoTools/test}"
               th:object="${robotForm}" method="post">
             <div class="row">
                 <div class="col-md-3 mt-5">
                     <div class="form-group">
                         <input type="text" value="" placeholder="用户名" name="user" th:field="*{user}" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = 'User name';}"/>
                         <br/>
                         <br/>
                         <select id="server" th:field="*{server}" class="form-control selectpicker">
                             <option value="">请选择目标服务器</option>
                             <option th:each="name : ${serverList}"
                                     th:value="${name}"
                                     th:text="${name}">
                             </option>
                         </select>
                         <br/>
                         <br/>
                         <select id="robot" th:field="*{robot}" class="form-control selectpicker">
                             <option value="">请选择测试机器人</option>
                             <option th:each="name : ${robotNameList}"
                                     th:value="${name}"
                                     th:text="${name}">
                             </option>
                         </select>
                         <br/>
                         <br/>
                         <label>请输入机器人参数: </label>
                         <input type="text" value="" placeholder="参数1" name="arg1" th:field="*{arg1}" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                         <input type="text" value="" placeholder="参数2" name="arg2" th:field="*{arg2}" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                         <br/>
                         <br/>
                         <label>请输入机器人数量: </label>
                         <input type="text" th:field="*{num}"/>
                         <br/>
                         <br/>
                         <input type="submit" value="运行"/>
                         <br/>
                         <br/>
                     </div>
                 </div>
                 <div class="col-md-1 mt-5"></div>
                 <div class="table-responsive col-md-8 mt-5">
                     <table id="robotConfTable" class="table table-striped text-nowrap">
                         <thead class="thead-dark">
                         <tr>
                             <td>机器人参数说明表</td>
                         </tr>
                         </thead>
                         <tr th:each="arg:${argsMap}">
                             <td><p th:text="${arg}"></p></td>
                         </tr>
                     </table>
                 </div>
             </div>
         </form>
     </div>
 </div>
</body>
</html>
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<html lang="en">
<body id="page-top">
<style>
    #about {
        left: 50%;
        top: 50%;
        transform: translate(-50%, 10%);
        font-family: Agenda-Light, Agenda Light, Agenda, <PERSON><PERSON>, sans-serif;
        font-weight: 100;
        background: rgba(0, 0, 0, 0.3);
        color: white;
        padding: 2rem;
        width: 33%;
        margin: 2rem;
        position: absolute;
        z-index: 2;
        font-size: 1.2rem;
    }
    #about {
        position: absolute;
        z-index: 3;
    }

    #about button:hover {
        background: rgba(0, 0, 0, .5)
    }

    #about {
        position: relative;
    }

</style>
<!-- About -->
 <div id="about">
     <br/>
     <br/>
     <div class="container">
         <h1>每日压测报告</h1>
         <p>这是一段说明,介绍本工具的用法。
         </p>
     </div>
     <div class="container">
         <form id="reportForm" th:action="@{/zeoTools/report}"
               th:object="${reportForm}" method="post">
             <div class="row">
                 <div class="col-md-3 mt-5">
                     <div class="form-group">
                         <label>请输入报告参数: </label>
                         <br/>
                         <br/>
                         <input type="text" value="" placeholder="报告名" name="reportName" th:field="*{reportName}" onfocus="this.value = 'perf_stat_log';" onblur="if (this.value == '') {this.value = 'perf_stat_log';}"/>
                         <br/>
                         <br/>
                         <input type="text" value="" placeholder="日期" name="date" th:field="*{date}" onfocus="this.value = '';" onblur="if (this.value == '') {this.value = '';}"/>
                         <br/>
                         <br/>
                         <input type="submit" value="拉取"/>
                         <br/>
                         <br/>
                     </div>
                 </div>
             </div>
         </form>
     </div>
 </div>
</body>
</html>
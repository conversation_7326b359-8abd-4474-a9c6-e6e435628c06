package com.yorha.robot.service;

import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.utils.YamlUtils;
import com.yorha.robot.RobotApp;
import com.yorha.robot.base.CncRobot;
import com.yorha.robot.bean.NoneArgs;
import com.yorha.robot.core.base.ArgDesc;
import com.yorha.robot.core.base.RobotCase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模拟机器人的service
 *
 * <AUTHOR>
 */
@Service
public class WebRobotService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WebRobotService.class);
    private static final Map<String, Class<? extends CncRobot>> ROBOT_TYPE_MAP = new ConcurrentHashMap<>();
    private static final Map<String, List<String>> ROBOT_ARGS_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Object> SERVER_TO_IP = new ConcurrentHashMap<>();
    private static final String YAML = "cfg/server.yaml";

    static {
        final JavaClassScanner scanner = new JavaClassScanner();
        for (Class<? extends CncRobot> robot : scanner.getSubTypesOf("com.yorha.robot.robotcase", CncRobot.class)) {
            RobotCase robotCase = robot.getAnnotation(RobotCase.class);
            if (robotCase == null) {
                continue;
            }
            ROBOT_TYPE_MAP.put(robotCase.name(), robot);
            if (robotCase.args() == NoneArgs.class) {
                continue;
            }
            List<String> args = new ArrayList<>();
            for (Field field : robotCase.args().getFields()) {
                args.add(field.getDeclaredAnnotation(ArgDesc.class).value());
            }
            ROBOT_ARGS_MAP.put(robotCase.name(), args);
        }
        //loadServer();
    }

    public Set<String> getRobotNameList() {
        return ROBOT_TYPE_MAP.keySet();
    }

    public Map<String, List<String>> getRobotArgsMap() {
        return ROBOT_ARGS_MAP;
    }

    public Class<? extends CncRobot> getRobot(String name) {
        return ROBOT_TYPE_MAP.getOrDefault(name, null);
    }

    public String launch(String userName, String[] args, Map<String, Object> robotArgsMap) throws InterruptedException {
        LOGGER.info("launch");
        return RobotApp.launchWebRobot(userName, args, robotArgsMap);
    }

    private static void loadServer() {
        File yamlFile = new File(YAML);
        StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader fileReader = new BufferedReader(new FileReader(yamlFile));
            String temp;
            while ((temp = fileReader.readLine()) != null) {
                if (temp.trim().startsWith("#")) {
                    continue;
                }
                stringBuilder.append(temp).append("\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        String result = stringBuilder.toString();
        Map<String, Object> channelInfo = YamlUtils.newInstance(result);
        SERVER_TO_IP.putAll(channelInfo);
    }

    public Set<String> getServerList() {
        return SERVER_TO_IP.keySet();
    }

    public String getIp(String server) {
        Object ip = SERVER_TO_IP.getOrDefault(server, null);
        if (ip == null) {
            return "";
        }
        return (String) ip;
    }
}

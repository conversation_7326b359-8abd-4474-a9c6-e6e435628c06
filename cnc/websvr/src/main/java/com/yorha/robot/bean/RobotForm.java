package com.yorha.robot.bean;

/**
 * 机器人请求bean
 *
 * <AUTHOR>
 */
public class RobotForm {
    private String user;
    private String server;
    private String robot;
    private int num;
    private String arg1;
    private String arg2;

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public void setRobot(String robot) {
        this.robot = robot;
    }

    public String getRobot() {
        return robot;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getNum() {
        return num;
    }

    public void setArg1(String arg1) {
        this.arg1 = arg1;
    }

    public String getArg1() {
        return arg1;
    }

    public void setArg2(String arg2) {
        this.arg2 = arg2;
    }

    public String getArg2() {
        return arg2;
    }


    @Override
    public String toString() {
        return user + " " + server + " " + robot + " " + num + " " + arg1 + arg2 + "\n";
    }
}

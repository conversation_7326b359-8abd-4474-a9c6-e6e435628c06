package com.yorha.robot.controller;


import com.yorha.robot.base.CncRobot;
import com.yorha.robot.bean.ReportForm;
import com.yorha.robot.bean.RobotForm;
import com.yorha.robot.core.base.RobotCase;
import com.yorha.robot.service.WebRobotService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(path = "zeoTools")
public class TestController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TestController.class);
    @Autowired
    private WebRobotService webRobotService;

    @GetMapping(path = "/test")
    public String test(Model model) {
        LOGGER.info("test accept.已接收");
        model.addAttribute("serverList", webRobotService.getServerList());
        model.addAttribute("robotNameList", webRobotService.getRobotNameList());
        model.addAttribute("argsMap", webRobotService.getRobotArgsMap());
        model.addAttribute("robotForm", new RobotForm());
        return "test";
    }

    @PostMapping(path = "/test")
    @ResponseBody
    public String simulate(@RequestParam Map<String, Object> params, HttpServletRequest request, @ModelAttribute RobotForm robotForm) {
        try {
            LOGGER.info("{} post {}", request, params);
            String server = robotForm.getServer();
            if (server.isEmpty()) {
                LOGGER.warn("{} post server is null", request);
                return "unknown server";
            }
            String robotName = robotForm.getRobot();
            if (robotName.isEmpty()) {
                LOGGER.warn("{} post robot is null", request);
                return "unknown robot";
            }
            String user = robotForm.getUser();
            if (user == null) {
                LOGGER.warn("{} post user is null", request);
                return "unknown user";
            }
            int robotNum = robotForm.getNum();
            if (robotNum <= 0) {
                LOGGER.warn("{} post num invailed", request);
                return "invailed num";
            }
            server = webRobotService.getIp(server);
            if (server.isEmpty()) {
                LOGGER.warn("{} server not support ", server);
                return "server not support";
            }

            Class<? extends CncRobot> robot = webRobotService.getRobot(robotName);
            if (robot == null) {
                LOGGER.warn("{} post cont find this robot {}", request, robotName);
                return "cant find robot";
            }
            Field[] fields = robot.getDeclaredAnnotation(RobotCase.class).args().getFields();
            Map<String, Object> robotArgsMap = new HashMap<>();
            int argsSize = fields.length;
            if (argsSize > 2) {
                return "now unsupported this robot";
            }
            if (argsSize > 0) {
                robotArgsMap.put(fields[0].getName(), robotForm.getArg1());
                if (argsSize > 1) {
                    robotArgsMap.put(fields[1].getName(), robotForm.getArg2());
                }
            }
            String startArgs = "";
            startArgs += "-h," + server + ",";
            startArgs += "-t," + robot.getName().replace("com.yorha.robot.robotcase.", "") + ",";
            startArgs += "-n," + robotNum + ",";
            startArgs += "-u," + user;
            return webRobotService.launch(user, startArgs.split(","), robotArgsMap);
        } catch (Exception e) {
            LOGGER.error("", e);
            return e.toString();
        }
    }

    @GetMapping(path = "/report")
    public String report(Model model) {
        LOGGER.info("report accept.已接收");
        model.addAttribute("reportForm", new ReportForm());
        return "report";
    }

    @PostMapping(path = "/report")
    @ResponseBody
    public String getReport(@RequestParam Map<String, Object> params, @ModelAttribute ReportForm reportForm) {
        LOGGER.info("getReport {}", params);
        String fileName = "../log/websvr/";
        fileName += reportForm.getReportName();
        String date = reportForm.getDate();
        if (!date.isEmpty()) {
            fileName = fileName + "." + date;
        }
        File file = new File(fileName);
        StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader fileReader = new BufferedReader(new FileReader(file));
            String temp;
            stringBuilder.append("<h1 style=\"text-align:center;\">每日压测报告</h1>").append("<br/>");
            while ((temp = fileReader.readLine()) != null) {
                stringBuilder.append("<pre>").append(temp).append("</pre>");
            }
        } catch (IOException e) {
            e.printStackTrace();
            return e.toString();
        }
        return stringBuilder.toString();
    }
}

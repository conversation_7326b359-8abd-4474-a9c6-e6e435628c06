server:
  envNamespace: dev
  worldId: 1                   #微信大区
  id: 1                        # 服务器ID
  name: 'dir-1'               # 服务器名
  externalIp: '***********'    # 外网IP
  tcpPort: 9527                # 对外端口
  webPort: 10086               # 后台端口

local: 'game-data/server' #策划数据路径
debug: true                #调试模式

heartBeat: 120 # 网关心跳检测时间(0 不进行心跳检测)
compress: false #网关数据包压缩
compressThreshold: 512 #压缩阀值
crypto: false # 网关数据包加密

tcaplus:
  appId: 2    # 业务ID
  zoneId: 3   # 游戏区ID
  password: '71BA75F6C4E3B49B'
  addressList:
    - 'tcp://************:9999'

cacheList:
  - type: 1              #global库
    ip: '**********'
    port: 6789
    index: 0
    password: 'VZ#hKuR%i3fTNh&9'

  - type: 2              #local库
    ip: '**********'
    port: 6790
    index: 0
    password: 'VZ#hKuR%i3fTNh&9'

wechatLog:
  enable: true
  defaultLogger: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=141a8f09-5654-477e-904b-c39c9986a2ca
  loggers:
    opsLogger: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=141a8f09-5654-477e-904b-c39c9986a2ca
<?xml version="1.0" encoding="UTF-8"?>
<!-- status=debug 可 以查看log4j的装配过程级别有8个  ALL,TRACE, DEBUG, INFO, WARN, ERROR ,FATAL,OFF,如果monitorInterval设置为0或负数,不会对配置变更进行监测-->
<configuration status="warn" monitorInterval="1800" shutdownHook="disable">
    <properties>
        <property name="LOG_HOME">${sys:LOG_ROOT}</property>
    </properties>
    <appenders>
        <!-- 定义控制台输出-->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n" />
        </Console>
        <!-- 游戏日志 -->
        <RollingRandomAccessFile name="FileAppender" fileName="${LOG_HOME}/dirsvr"
                                 filePattern="${LOG_HOME}/dirsvr.%d{yyyy-MM-dd-HH}.%i.log"
                                 immediateFlush="true">
            <PatternLayout
                    pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
    </appenders>
    <loggers>
        <!-- 3party Loggers -->
        <Logger name="org.springframework" level="warn">
        </Logger>
        <Logger name="io.netty" level="warn">
        </Logger>
        <Logger name="org.apache.http" level="warn">
        </Logger>
        <Logger name="org.apache.commons" level="warn">
        </Logger>
        <Logger name="com.mchange.v2" level="warn">
        </Logger>
        <Logger name="org.reflections" level="warn">
        </Logger>
        <Logger name="com.tencent.tcaplus" level="warn">
        </Logger>
        <Logger name="java.sql" level="warn">
        </Logger>
        <Logger name="io.grpc" level="warn">
        </Logger>
        <Logger name="org.mongodb" level="warn">
        </Logger>
        <!-- Root Logger -->
        <Root level="info" includeLocation="true">
            <appender-ref ref="FileAppender"/>
            <appender-ref ref="Console"/>
        </Root>
    </loggers>
</configuration>
package com.yorha.directory;

import com.google.protobuf.Empty;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.*;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.ErrorCodeUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.Core;
import com.yorha.proto.CsAccount;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.net.InetSocketAddress;
import java.util.concurrent.Executor;
import java.util.concurrent.locks.ReentrantLock;

/**
 * dir session object。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class DirSession implements ISession {
    private static final Logger LOGGER = LogManager.getLogger(DirSession.class);

    private final long sessionId;
    private final Channel channel;
    private final String ip;
    private final Executor executor;
    private final ReentrantLock reentrantLock;

    public DirSession(long sessionId, Channel channel, Executor executor) {
        this.sessionId = sessionId;
        this.channel = channel;
        this.ip = this.channel.remoteAddress() != null ? ((InetSocketAddress) channel.remoteAddress()).getAddress().getHostAddress() : null;
        this.executor = executor;
        this.reentrantLock = new ReentrantLock();
    }

    public void receiveMsg(final Core.CSHeader header, final byte[] body) {
        final int msgType = header.getType();
        final int seqId = header.getSeqId();
        final int s2cMsgId = MsgType.getRetMsgId(msgType);
        final Command command = CommandMgr.getInstance().getCommand(msgType);
        if (command == null) {
            LOGGER.warn("receiveMsg msgType:{} useless! no command!", msgType);
            this.answerErrorCode(s2cMsgId, seqId, ErrorCode.COMMAND_MSG_C2S_NOT_EXIST.getCode());
            return;
        }

        if (msgType == MsgType.DIRPING_C2S_MSG) {
            // ping直接当场处理吧
            answerMsg(s2cMsgId, seqId, CsAccount.DirPing_S2C_Msg.getDefaultInstance());
            MonitorUnit.DIR_RESPONSE_TOTAL.labels(ServerContext.getBusId(), MsgUtils.getMsgNameFromCsMsgType(msgType)).inc();
            return;
        }

        this.executor.execute(() -> {
            reentrantLock.lock();
            try {
                final byte[] message = ParseEngine.decodeMsg(header.getFlag(), body);
                long time = SystemClock.nowNative();
                command.execute(this, msgType, seqId, message);
                long executeTimeMs = SystemClock.nowNative() - time;
                if (executeTimeMs >= MonitorConstant.DIR_RESPONSE_COST_OVER_TIME) {
                    LOGGER.info("execute overtime, cost={}ms more than threshold={}ms", executeTimeMs, MonitorConstant.DIR_RESPONSE_COST_OVER_TIME);
                }
                MonitorUnit.DIR_RESPONSE_TOTAL.labels(ServerContext.getBusId(), MsgUtils.getMsgNameFromCsMsgType(msgType)).inc();
            } catch (Exception e) {
                if (!GeminiException.isLogicException(e)) {
                    WechatLog.error("exception_perf DirSession processMsg type:{} session:{}", msgType, this, e);
                }
                final Core.Code retCode = ErrorCodeUtils.getCodeFromException(e);
                LOGGER.info("DirSession processMsg sendErrorCode {} to {}", retCode, this);
                this.answerErrorCode(s2cMsgId, seqId, retCode);
            } finally {
                reentrantLock.unlock();
            }
        });
    }

    @Override
    public void answerMsg(int msgType, int seqId, GeneratedMessageV3 msg) {
        this.sendMsg(msgType, seqId, null, msg);
    }

    @Override
    public void answerErrorCode(int msgType, int seqId, Core.Code code) {
        this.sendMsg(msgType, seqId, code, null);
    }

    private void sendMsg(int msgType, int seqId, Core.Code code, GeneratedMessageV3 msg) {
        if (!this.channel.isActive()) {
            return;
        }
        if (msg == null) {
            msg = Empty.getDefaultInstance();
        }
        String msgName = MsgUtils.getMsgNameFromCsMsgType(msgType);
        LOGGER.info("DirSession sendMsg msgType:{} {} {} seqId={} code={} msg={}", msgType, msgName, this, seqId, code, msg);
        ByteBuf byteBuf = ParseEngine.getInstance().encodeMsg(msgType, seqId, code, msg);
        this.channel.writeAndFlush(byteBuf);
        // 下行流量统计
        MonitorUnit.WRITE_MSG_BYTE_COUNTER.labels(ServerContext.getBusId()).inc(byteBuf.readableBytes());
        MonitorUnit.WRITE_MSG_COUNTER.labels(ServerContext.getBusId()).inc();
    }

    public long getSessionId() {
        return sessionId;
    }

    public String getClientIp() {
        return this.ip;
    }

    @Override
    public String toString() {
        return "DirSession{" +
                "id=" + sessionId +
                ", ip='" + ip + '\'' +
                '}';
    }
}

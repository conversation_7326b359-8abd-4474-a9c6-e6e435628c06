package com.yorha.directory;

import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.AbstractServer;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.ChannelHelper;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.Core.CSHeader;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.TooLongFrameException;
import io.netty.handler.timeout.IdleStateEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class DirectoryChannelInboundHandler extends SimpleChannelInboundHandler<ByteBuf> {
    private static final Logger LOGGER = LogManager.getLogger(DirectoryChannelInboundHandler.class);
    private static final int MSG_FIXED_HEAD_BYTE_SIZE = 2;

    private DirSession session;

    public DirectoryChannelInboundHandler() {
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object e) {
        if (e instanceof IdleStateEvent) {
            final Channel channel = ctx.channel();
            LOGGER.warn("DirectoryChannelInboundHandler userEventTriggered {} {} {}", e, channel, this.session);
            ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler idle too long");
        }
    }

    /**
     * 建立连接 channelConnected
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        if (!ServerContext.isDirServer()) {
            LOGGER.error("DirectoryChannelInboundHandler channelActive but server is not dir!");
            return;
        }

        final Channel channel = ctx.channel();
        if (!ServerContext.getServer().isRunning()) {
            LOGGER.info("channelActive but server not in running {}", session);
            ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler server not running");
            return;
        }

        AbstractServer server = ServerContext.getServer();
        DirServer dirServer = (DirServer) server;
        this.session = new DirSession(IdFactory.nextIdForActorMsg("new dir session"), channel, dirServer.getExecutor());
        LOGGER.info("DirectoryChannelInboundHandler channelActive {} {}", channel, this.session);
        DirSessionManager.getInstance().addSession(session.getSessionId());
    }

    /**
     * 断开连接 原先的channelClosed
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        final Channel channel = ctx.channel();
        LOGGER.info("DirectoryChannelInboundHandler channelInactive {} {}", channel, this.session);
        if (session != null) {
            DirSessionManager.getInstance().removeSession(session.getSessionId());
        }
    }

    /**
     * messageReceived
     */
    @Override
    public void channelRead0(final ChannelHandlerContext ctx, final ByteBuf msg) {
        if (!ServerContext.getServer().isRunning()) {
            LOGGER.info("channelRead0 but server not in running {}", session);
            ChannelHelper.closeChannel(ctx.channel(), "DirectoryChannelInboundHandler channelRead0 server not running");
            return;
        }
        if (session == null) {
            LOGGER.info("channelRead0 but session is null");
            ChannelHelper.closeChannel(ctx.channel(), "DirectoryChannelInboundHandler channelRead0 null session");
            return;
        }
        final Channel channel = ctx.channel();
        final int msgTotalByteSize = msg.readableBytes();
        if (msgTotalByteSize <= MSG_FIXED_HEAD_BYTE_SIZE) {
            LOGGER.warn("DirectoryChannelInboundHandler channelRead0 headerLength <= {}! session {}", MSG_FIXED_HEAD_BYTE_SIZE, this.session);
            ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler channelRead0 totalSize < fixedSize");
            return;
        }
        final int headerByteSize = msg.readShort();
        final int bodyByteSize = msgTotalByteSize - headerByteSize - MSG_FIXED_HEAD_BYTE_SIZE;
        if (headerByteSize <= 0 || bodyByteSize < 0) {
            LOGGER.info("DirectoryChannelInboundHandler channelRead0 headerByteSize {} bodyByteSize {} session {}", headerByteSize, bodyByteSize, this.session);
            ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler channelRead0 invalid header or body size");
            return;
        }
        // 挪下来，不希望外界探活等流量影响判断
        // 上行流量统计
        MonitorUnit.READ_MSG_COUNTER.labels(ServerContext.getBusId()).inc();
        MonitorUnit.READ_MSG_BYTE_COUNTER.labels(ServerContext.getBusId()).inc(msgTotalByteSize);
        // 读取header
        final byte[] headerData = new byte[headerByteSize];
        msg.readBytes(headerData);
        // 读取body
        final byte[] bodyData = new byte[bodyByteSize];
        msg.readBytes(bodyData);
        // 解析数据
        final CSHeader csHeader;
        try {
            csHeader = CSHeader.parseFrom(headerData);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.info("DirectoryChannelInboundHandler channelRead0 decodeMsg header fail! ch {} session {}", ctx.channel(), this.session, e);
            ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler channelRead0 decodeMsg header fail");
            return;
        }
        this.session.receiveMsg(csHeader, bodyData);
    }

    @Override
    public void channelWritabilityChanged(ChannelHandlerContext ctx) {
        final Channel channel = ctx.channel();
        LOGGER.error("channel {}, isWritable {}, isActive {} session {}", channel, channel.isWritable(), channel.isActive(), this.session);
        if (channel.isWritable()) {
            return;
        }
        ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler channelWritabilityChanged reach high water");
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable err) {
        final Channel channel = ctx.channel();
        try {
            if (err instanceof IOException) {
                LOGGER.info("DirectoryChannelInboundHandler exceptionCaught {} {} {}", channel, this.session, err.getMessage());
            } else if (err instanceof TooLongFrameException) {
                // 发送的前四个字节代表长度
                LOGGER.warn("DirectoryChannelInboundHandler exceptionCaught {} {} {}", channel, this.session, err.getMessage());
            } else {
                WechatLog.error("DirectoryChannelInboundHandler exceptionCaught {} {}", channel, this.session, err);
            }
        } finally {
            ChannelHelper.closeChannel(channel, "DirectoryChannelInboundHandler exceptionCaught");
        }
    }

}
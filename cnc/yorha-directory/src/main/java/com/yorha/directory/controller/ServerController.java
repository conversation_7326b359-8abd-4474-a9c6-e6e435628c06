package com.yorha.directory.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.auth.server.AuthChannel;
import com.yorha.common.auth.server.AuthChannelDispatcher;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.WhitePermissions;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.whiteList.WhiteListResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.discovery.ZoneItem;
import com.yorha.common.utils.DriveTrafficUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.directory.DirSession;
import com.yorha.directory.db.DirDbActionWrapper;
import com.yorha.directory.utils.DriveTrafficHelper;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CsAccount;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedList;
import java.util.List;
import java.util.OptionalInt;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_USER)
public class ServerController {
    private static final Logger LOGGER = LogManager.getLogger(ServerController.class);


    @CommandMapping(code = MsgType.DIRGETZONE_C2S_MSG)
    public GeneratedMessageV3 handle(DirSession dirSession, CsAccount.DirGetZone_C2S_Msg msg, int seqId) {
        LOGGER.info("atTraffic DirGetZone_C2S_Msg getZoneList start {} seqId={} {}", dirSession, seqId, msg);

        final String deviceId = msg.getDeviceId();
        final String systemHardware = msg.getSystemHardware();
        final int hardwareLevel = msg.getHardwareLevel();
        final String version = msg.getVersion();
        final String channelName = msg.getChannelName();

        // 验证请求并获取openId
        String openId = validateRequest(dirSession, msg);

        // 检查版本白名单
        checkVersionWhitelist(dirSession, msg, version);

        WhiteListResService whiteService = ResHolder.getResService(WhiteListResService.class);

        // 处理超级白名单用户
        CsAccount.DirGetZone_S2C_Msg superWhiteResult = handleSuperWhitePlayer(
                whiteService, openId, deviceId, dirSession.getClientIp(), msg);
        if (superWhiteResult != null) {
            return superWhiteResult;
        }

        // 处理已有角色的玩家
        CsAccount.DirGetZone_S2C_Msg existingPlayerResult = handleExistingPlayer(openId, channelName, msg);
        if (existingPlayerResult != null) {
            return existingPlayerResult;
        }

        // 处理新玩家
        CsAccount.DirGetZone_S2C_Msg result = handleNewPlayer(
                whiteService, systemHardware, openId, hardwareLevel, channelName, dirSession, msg);

        LOGGER.info("atTraffic DirGetZone_C2S_Msg end, result={} msg={}", result, msg);
        return result;
    }

    /**
     * 验证请求参数并获取openId
     */
    private String validateRequest(DirSession dirSession, CsAccount.DirGetZone_C2S_Msg msg) {
        AuthChannel authChannel = AuthChannelDispatcher.dispatchChannelBy(msg);
        if (authChannel == null) {
            throw new GeminiException("atTraffic DirGetZone_C2S_Msg failed channelType:{} is not supported, dirSession:{}",
                    AuthChannelDispatcher.getAuthChannelType(), dirSession);
        }

        String openId = authChannel.getOpenId(msg);
        // openId防止客户端传空
        if (StringUtils.isEmpty(openId)) {
            LOGGER.info("ServerController openId is empty");
            throw new GeminiException(ErrorCode.LOGIN_EMPTY_OPENID);
        }

        // 账号封禁
        if (DirDbActionWrapper.isAccountBanned(openId)) {
            LOGGER.info("ServerController Account banned, openid={}", openId);
            throw new GeminiException(ErrorCode.ACCOUNT_BANNED);
        }

        return openId;
    }

    /**
     * 检查版本白名单
     */
    private void checkVersionWhitelist(DirSession dirSession, CsAccount.DirGetZone_C2S_Msg msg, String version) {
        WhiteListResService whiteService = ResHolder.getResService(WhiteListResService.class);
        boolean isOpenVersionWhite = ClusterConfigUtils.getWorldConfig().getBooleanItem("is_open_version_white");
        boolean isWhiteVersion = whiteService.isWhiteVersion(version);

        if (isOpenVersionWhite && !isWhiteVersion) {
            if (!whiteService.clientCanLogin(version)) {
                LOGGER.warn("atTraffic DirGetZone_C2S_Msg atDir whiteVersion fail whiteVersion is not right, {} {}", dirSession, msg);
                throw new GeminiException(ErrorCode.LOGIN_CLIENT_VERSION_LIMIT);
            }
        }

        LOGGER.info("atTraffic DirGetZone_C2S_Msg atDir whiteVersion is right, {} {} {}", version, isOpenVersionWhite, isWhiteVersion);
    }

    /**
     * 处理超级白名单用户
     */
    private CsAccount.DirGetZone_S2C_Msg handleSuperWhitePlayer(WhiteListResService whiteService,
                                                                String openId, String deviceId, String clientIp, CsAccount.DirGetZone_C2S_Msg msg) {
        boolean isSuperWhitePlayer = whiteService.hasWhitePermission(
                0, openId, deviceId, clientIp, WhitePermissions.WP_POWER);

        if (!isSuperWhitePlayer) {
            return null;
        }

        if (msg.getNeedAllZoneList()) {
            LOGGER.info("atTraffic white_login DirGetZone_C2S_Msg client needAllZone and is SuperWhitePlayer");
            return DirGetZoneRetMsgFormatter.buildSuperWhitePlayerMsg(
                    msg.getChannelName(), DirDbActionWrapper.getAccountZoneList(openId));
        } else {
            LOGGER.warn("atTraffic white_login DirGetZone_C2S_Msg client noNeedAllZone and is SuperWhitePlayer, send to zone 1");
            return DirGetZoneRetMsgFormatter.buildDriveTrafficMsg(1,
                    CommonEnum.DriveTrafficType.DTAT_SERVER_FULLFILLED.getNumber(), msg.getChannelName());
        }
    }

    /**
     * 处理已有角色的玩家
     */
    private CsAccount.DirGetZone_S2C_Msg handleExistingPlayer(String openId, String channelName, CsAccount.DirGetZone_C2S_Msg msg) {
        final int lastLoginZone = DirDbActionWrapper.getLastLoginZone(openId);
        // 是否有角色
        if (lastLoginZone <= 0) {
            return null;
        }

        LOGGER.info("atTraffic DirGetZone_C2S_Msg atDir getZoneList end, lastLoginZoneId={} msg={}", lastLoginZone, msg);
        final ZoneItem targetZone = DefaultZoneGateItemHandler.getInstance().getZone(lastLoginZone);
        if (targetZone != null) {
            final ServerOpenStatus curStatus = ServerOpenStatus.valueOf(targetZone.getOpenStatus());
            if (curStatus != ServerOpenStatus.OPEN) {
                return DirGetZoneRetMsgFormatter.buildGameCloseMsg("TARGET_ZONE_NOT_OPEN");
            }
        } else {
            LOGGER.warn("atTraffic DirGetZone_C2S_Msg targetZoneId={}, has no heartbeat", lastLoginZone);
            return DirGetZoneRetMsgFormatter.buildGameCloseMsg("TARGET_ZONE_NO_HEARTBEAT");
        }

        return DirGetZoneRetMsgFormatter.buildNotDriveTrafficMsg(lastLoginZone, channelName);
    }

    /**
     * 处理新玩家（设备白名单检查和导量逻辑）
     */
    private CsAccount.DirGetZone_S2C_Msg handleNewPlayer(WhiteListResService whiteService, String systemHardware,
                                                         String openId, int hardwareLevel, String channelName, DirSession dirSession, CsAccount.DirGetZone_C2S_Msg msg) {

        // 设备机型白名单检查
        boolean isOpenModelWhite = ClusterConfigUtils.getWorldConfig().getBooleanItem("is_open_model_white");
        if (isOpenModelWhite) {
            if (!whiteService.isWhiteModel(systemHardware)) {
                LOGGER.warn("atTraffic DirGetZone_C2S_Msg atDir whiteModel fail whiteModel is not right, {} {}", dirSession, msg);
                return DirGetZoneRetMsgFormatter.buildGameCloseMsg("NOT_IN_DEVICE_WHITE_LIST");
            }
        }

        // 检查历史导量记录
        Triple<OptionalInt, Integer, Integer> preTargetZoneIdWithVersion = DirDbActionWrapper.getPlayerGuideRecord(openId);
        if (preTargetZoneIdWithVersion.getLeft().isPresent()) {
            int preTargetZoneId = preTargetZoneIdWithVersion.getLeft().getAsInt();
            int driveTrafficReason = preTargetZoneIdWithVersion.getMiddle();
            // 历史导量记录可进入
            if (DriveTrafficHelper.isNotFullZone(hardwareLevel, preTargetZoneId)) {
                LOGGER.info("atTraffic DirGetZone_C2S_Msg atDir getZoneList end, use preTargetZoneId={} msg={}", preTargetZoneId, msg);
                return DirGetZoneRetMsgFormatter.buildDriveTrafficMsg(preTargetZoneId, driveTrafficReason, channelName);
            }
        }

        // 走导量逻辑
        LOGGER.info("atTraffic DirGetZone_C2S_Msg atDir start driveTraffic msg={}", msg);
        return DirGetZoneRetMsgFormatter.startDriveTraffic(openId, hardwareLevel, preTargetZoneIdWithVersion.getRight(), channelName);
    }

    private static class DirGetZoneRetMsgFormatter {

        /**
         * 导量流程
         *
         * @param openId        玩家openId
         * @param hardwareLevel 硬件档位
         * @param version       玩家导量记录tcaplus版本
         * @param channelName   加速通道
         * @return 导量s2c or 关服s2c
         */
        private static CsAccount.DirGetZone_S2C_Msg startDriveTraffic(
                final String openId,
                final int hardwareLevel,
                final int version,
                final String channelName
        ) {
            // 导量机型
            final int driveTrafficLevel = DriveTrafficUtils.getTrafficLevel(hardwareLevel);

            // 所有服务器对内
            if (DriveTrafficHelper.isAllAliveServerInternal()) {
                return buildGameCloseMsg("SERVER_ALL_INTERNAL");
            }

            // 导量结果（含兜底）
            Pair<Integer, CommonEnum.DriveTrafficType> driveTrafficResult = DriveTrafficHelper.tryDriveTraffic(driveTrafficLevel, channelName);

            // 无服务器可进入
            if (driveTrafficResult.getFirst() == null) {
                LOGGER.warn("drive traffic fail, no available zone, openId={}, trafficType={}, driveTrafficLevel={}, hardwareLevel={}",
                        openId, driveTrafficResult.getSecond().name(), driveTrafficLevel, hardwareLevel);
                throw new GeminiException(ErrorCode.NO_AVAILABLE_ZONE);
            }

            int suggestZoneId = driveTrafficResult.getFirst();
            LOGGER.info("drive traffic result: targetZoneId={}, openId={}, trafficType={}, driveTrafficLevel={}, hardwareLevel={}",
                    suggestZoneId, openId, driveTrafficResult.getSecond().name(), driveTrafficLevel, hardwareLevel);
            // 多dir实例并发保护
            OptionalInt latestZoneId = DirDbActionWrapper.upsertPlayerGuideRecord(openId, driveTrafficResult, version);
            if (latestZoneId.isPresent()) {
                suggestZoneId = latestZoneId.getAsInt();
                LOGGER.info("concurrent protection: change targetZoneId={}, openId={}, trafficType={}, driveTrafficLevel={}, hardwareLevel={}",
                        suggestZoneId, openId, driveTrafficResult.getSecond().name(), driveTrafficLevel, hardwareLevel);
            }
            LOGGER.info("dir_drive_hardware_level_total drive traffic result: targetZoneId={}, openId={}, trafficType={}, hardwareLevel={}",
                    suggestZoneId, openId, driveTrafficResult.getSecond().name(), hardwareLevel);
            return buildDriveTrafficMsg(suggestZoneId, driveTrafficResult.getSecond().getNumber(), channelName);
        }

        /**
         * 构造游戏关服s2c
         *
         * @return 游戏关服s2c
         */
        private static CsAccount.DirGetZone_S2C_Msg buildGameCloseMsg(final String closeReason) {
            LOGGER.info("atTraffic DirGetZoneRetMsgFormatter buildGameCloseMsg closeReason={}", closeReason);
            CsAccount.DirGetZone_S2C_Msg.Builder message = CsAccount.DirGetZone_S2C_Msg.newBuilder();
            int announcementId = ServerContext.getCloseAnnouncementId();
            // 过期了就设置成默认的0号公告
            if (announcementId == 0 || ServerContext.getOpenAnnouncementTsMs() <= SystemClock.now()) {
                announcementId = GameLogicConstants.DEFAULT_ANNOUNCEMENT_ID;
            }
            message.getCloseInfoBuilder()
                    .setIsClose(true)
                    .setAnnouncementId(announcementId)
                    .setOpenTime(ServerContext.getOpenAnnouncementTsMs());
            return message.build();
        }

        /**
         * 构造超级白名单s2c
         *
         * @param channelName 加速通道
         * @return 超级白名单s2c
         */
        private static CsAccount.DirGetZone_S2C_Msg buildSuperWhitePlayerMsg(final String channelName, final Set<Integer> myZoneId) {
            CsAccount.DirGetZone_S2C_Msg.Builder message = CsAccount.DirGetZone_S2C_Msg.newBuilder();
            message.addAllAllZoneServerList(formAllZoneServer(channelName));
            message.addAllMyZoneId(myZoneId);
            return message.build();
        }

        /**
         * 构造非导量s2c（已有账号）
         *
         * @param zoneId      zoneId
         * @param channelName 加速通道
         * @return 非导量s2c
         */
        private static CsAccount.DirGetZone_S2C_Msg buildNotDriveTrafficMsg(final int zoneId, final String channelName) {
            CsAccount.DirGetZone_S2C_Msg.Builder message = CsAccount.DirGetZone_S2C_Msg.newBuilder();
            message.setTargetZoneServer(DirGetZoneRetMsgFormatter.formTargetServerInfo(zoneId, channelName));
            LOGGER.info("dir_drive_zone_success_total reason={} message={}", "LOGIN_EXISTED_ACCOUNT", message);
            return message.build();
        }

        /**
         * 构造导量s2c
         *
         * @param zoneId             zoneId
         * @param driveTrafficReason 导量策略
         * @param channelName        加速通道
         * @return 导量s2c
         */
        private static CsAccount.DirGetZone_S2C_Msg buildDriveTrafficMsg(final int zoneId, final int driveTrafficReason, final String channelName) {
            CsAccount.DirGetZone_S2C_Msg.Builder message = CsAccount.DirGetZone_S2C_Msg.newBuilder()
                    .setRegisterDriveTrafficReason(driveTrafficReason)
                    .setTargetZoneServer(DirGetZoneRetMsgFormatter.formTargetServerInfo(zoneId, channelName));
            CommonEnum.DriveTrafficType driveTrafficType = CommonEnum.DriveTrafficType.forNumber(driveTrafficReason);
            final String reason = driveTrafficType != null ? driveTrafficType.name() : ("UNKNOWN_REASON_" + driveTrafficReason);
            LOGGER.info("atTraffic DirGetZoneRetMsgFormatter buildDriveTrafficMsg dir_drive_zone_success_total reason={} message={}", reason, message);
            return message.build();
        }

        /**
         * 构建所有zone的信息
         *
         * @param channelName 加速通道
         * @return List
         */
        private static List<CommonMsg.ServerInfo> formAllZoneServer(final String channelName) {
            List<CommonMsg.ServerInfo> result = new LinkedList<>();
            for (Integer zoneId : ClusterConfigUtils.getZoneIdList()) {
                CommonMsg.ServerInfo.Builder builder = CommonMsg.ServerInfo.newBuilder()
                        .setZoneId(zoneId)
                        .setServerOpenStatus(DriveTrafficHelper.getZoneServerOpenStatus(zoneId).getNumber());
                Pair<String, Integer> gateAddress = DefaultZoneGateItemHandler.getInstance().getGateAddressByChannel(zoneId, channelName);
                if (gateAddress == null) {
                    LOGGER.warn("DirGetZoneRetMsgFormatter formAllZoneServer get gateAddress null when zone={}", zoneId);
                    builder.setIp("0").setPort(0);
                } else {
                    builder.setIp(gateAddress.getFirst()).setPort(gateAddress.getSecond());
                }
                result.add(builder.build());
            }

            return result;
        }

        /**
         * 构造目标zone信息
         *
         * @param zoneId      zoneId
         * @param channelName 加速通道
         * @return ServerInfo
         */
        private static CommonMsg.ServerInfo formTargetServerInfo(int zoneId, final String channelName) {
            Pair<String, Integer> gateAddress = DefaultZoneGateItemHandler.getInstance().getGateAddressByChannel(zoneId, channelName);
            if (gateAddress == null) {
                // 对应zone无可用加速通道
                LOGGER.warn("DirGetZoneRetMsgFormatter formTargetServerInfo dir_drive_zone_fail_total no available zone for channelName={}", channelName);
                throw new GeminiException(ErrorCode.NO_ADDRESS_FOR_CHANNEL);
            }
            CommonEnum.ServerEnterStatus serverOpenStatus = DriveTrafficHelper.getZoneServerOpenStatus(zoneId);
            CommonMsg.ServerInfo.Builder builder = CommonMsg.ServerInfo.newBuilder()
                    .setZoneId(zoneId)
                    .setServerOpenStatus(serverOpenStatus.getNumber())
                    .setIp(gateAddress.getFirst()).setPort(gateAddress.getSecond());
            return builder.build();

        }

    }
}

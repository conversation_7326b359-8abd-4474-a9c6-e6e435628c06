package com.yorha.directory.db;

import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.db.DbManager;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.*;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * dir所有db操作封装
 *
 * <AUTHOR>
 */
public class DirDbActionWrapper {
    private static final Logger LOGGER = LogManager.getLogger(DirDbActionWrapper.class);


    /**
     * 获取某个玩家已有账号的区
     */
    public static Set<Integer> getAccountZoneList(String openId) {
        Set<Integer> ret = new HashSet<>();
        GetByPartKeyResult<TcaplusDb.AccountRoleTable.Builder> result =
                DbManager.getInstance().getByPartKey(TcaplusDb.AccountRoleTable.newBuilder().setOpenId(openId),
                        GetByPartKeyOption.newBuilder().build());
        LOGGER.info("DirTcaplusManager getAccountZoneList requestId={}, code={}", result.requestId, result.getCode());
        if (result.code == TcaplusErrorCode.GEN_ERR_SUC) {
            for (ValueWithVersion<TcaplusDb.AccountRoleTable.Builder> e : result.getValues()) {
                ret.add(e.value.getRoleInfo().getZoneId());
            }
        }
        return ret;
    }

    /**
     * 获取某个玩家上次登录区
     *
     * @param openId 玩家openId
     * @return 上次登录区(- 1 账号不存在)
     */
    public static int getLastLoginZone(String openId) {

        GetByPartKeyResult<TcaplusDb.AccountRoleTable.Builder> result =
                DbManager.getInstance().getByPartKey(TcaplusDb.AccountRoleTable.newBuilder().setOpenId(openId),
                        GetByPartKeyOption.newBuilder().build());
        if (result.isRecordNotExist()) {
            return -1;
        }
        if (!result.isOk()) {
            LOGGER.error("DirTcaplusManager getLastLoginZone, db fail, code={}, requestId={}", result.getCode(), result.requestId);
            throw new GeminiException("getLastLoginZone db fail");
        }
        int ret = -1;
        long maxLoginTsMs = -1;
        for (ValueWithVersion<TcaplusDb.AccountRoleTable.Builder> e : result.getValues()) {
            final long loginTsMs = e.value.getRoleInfo().getLoginTsMs();
            if (maxLoginTsMs < loginTsMs) {
                maxLoginTsMs = loginTsMs;
                ret = e.value.getRoleInfo().getZoneId();
            }
        }
        return ret;
    }

    /**
     * 返回玩家历史导量记录
     *
     * @param openId 玩家openId
     * @return Triple<zoneId, driveTrafficReason, tcaplusVersion>
     */
    public static Triple<OptionalInt, Integer, Integer> getPlayerGuideRecord(final String openId) {
        GetResult<TcaplusDb.PlayerGuideRecordTable.Builder> result =
                DbManager.getInstance().get(TcaplusDb.PlayerGuideRecordTable.newBuilder().setOpenId(openId),
                        GetOption.newBuilder().build());
        // 数据存在
        if (result.isOk()) {
            long curTsSecond = SystemClock.nowOfSeconds();
            long insertTsSecond = result.value.getInsertTsSecond();

            // 未过期
            if (curTsSecond - insertTsSecond < 5) {
                LOGGER.info("getPlayerGuideRecord openId={} has recent drive traffic record, use it", openId);
                return Triple.of(OptionalInt.of(result.value.getTargetZoneId()), result.value.getGuideReason(), result.getVersion());
            }
            LOGGER.info("getPlayerGuideRecord openId={} has no available recent drive traffic record", openId);
            return Triple.of(OptionalInt.empty(), 0, result.getVersion());
        }
        LOGGER.info("getPlayerGuideRecord openId={} has no drive traffic record", openId);
        return Triple.of(OptionalInt.empty(), 0, 0);
    }

    /**
     * 更新玩家导量表
     *
     * @param openId             玩家openId
     * @param driveTrafficResult 导量结果
     * @param version            数据版本号
     * @return 出现并发问题时为上一个dir导去的zone
     */
    public static OptionalInt upsertPlayerGuideRecord(
            final String openId,
            final Pair<Integer, CommonEnum.DriveTrafficType> driveTrafficResult,
            final int version) {
        if (version > 0) {
            LOGGER.info("atTraffic upsertPlayerGuideRecord dir_update_drive_strategy_total cur tcaplus version={}", version);
        }
        UpsertResult<TcaplusDb.PlayerGuideRecordTable.Builder> result =
                DbManager.getInstance().upsert(
                        TcaplusDb.PlayerGuideRecordTable.newBuilder()
                                .setOpenId(openId)
                                .setInsertTsSecond(SystemClock.nowOfSeconds())
                                .setTargetZoneId(driveTrafficResult.getFirst())
                                .setGuideReason(driveTrafficResult.getSecond().getNumber()),
                        UpsertOption.newBuilder()
                                .setVersion(version)
                                .setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL)
                                .build()
                );
        if (result.isOk()) {
            return OptionalInt.empty();
        }
        if (DbUtil.isInvalidVersion(result.getCode())) {
            return OptionalInt.of(result.value.getTargetZoneId());
        }
        // 数据库数据出错，继续原流程
        LOGGER.error("atTraffic upsertPlayerGuideRecord tcaplus error={}", result.getCode());
        return OptionalInt.empty();
    }

    /**
     * @param openId 玩家openId
     * @return 玩家账号是否被封禁
     */
    public static boolean isAccountBanned(String openId) {
        GetResult<TcaplusDb.OpenIdInfoTable.Builder> result =
                DbManager.getInstance().get(TcaplusDb.OpenIdInfoTable.newBuilder().setOpenId(openId),
                        GetOption.newBuilder().build());
        if (result.isRecordNotExist()) {
            return false;
        }
        if (!result.isOk()) {
            LOGGER.error("DirTcaplusManager isAccountBanned, db fail, code={}", result.getCode());
            throw new GeminiException("isAccountBanned db fail");
        }
        if (!result.value.hasBanInfo()) {
            return false;
        }
        if (result.value.getBanInfo().getBanEndTimeMs() == -1) {
            return true;
        }
        return result.value.getBanInfo().getBanEndTimeMs() > SystemClock.nowNative();
    }

    /**
     * @return 所有起服的zone的导量数据
     */
    public static List<ValueWithVersion<TcaplusDb.ZoneGuideRecordTable.Builder>> getZoneGuideRecords() {
        // 心跳有效zone
        Set<Integer> validZoneIds = DefaultZoneGateItemHandler.getInstance().getWorldFoundZoneIdSet();
        List<TcaplusDb.ZoneGuideRecordTable.Builder> listReq = new ArrayList<>();
        for (Integer validZoneId : validZoneIds) {
            TcaplusDb.ZoneGuideRecordTable.Builder req = TcaplusDb.ZoneGuideRecordTable.newBuilder();
            req.setZoneId(validZoneId);
            listReq.add(req);
        }
        if (listReq.isEmpty()) {
            LOGGER.info("getZoneGuideRecords, listReq empty, validZoneIds={}", validZoneIds);
            return Collections.emptyList();
        }
        // 只拉取有效字段
        final BatchGetOption option = BatchGetOption.newBuilder()
                .withGetAllFields(false)
                .withFieldNames("dirAssumeRegisterNum", "dirAssumeHardwareLevel1Num", "dirAssumeHardwareLevel2Num")
                .build();
        final BatchGetResult<TcaplusDb.ZoneGuideRecordTable.Builder> result = DbManager.getInstance().batchGet(listReq, option);
        if (!result.isOk()) {
            LOGGER.warn("getZoneGuideRecords tcaplus requestId={}, errorCode={}", result.requestId, result.getCode());
            return Collections.emptyList();
        }
        if (result.values.isEmpty()) {
            LOGGER.info("getZoneGuideRecords data empty, validZoneIds={}, requestId={},", validZoneIds, result.requestId);
            return Collections.emptyList();
        }
        final StringBuilder sb = new StringBuilder();
        int index = 0;
        for (final ValueWithVersion<TcaplusDb.ZoneGuideRecordTable.Builder> valueWithVersion : result.values) {
            if (index != 0) {
                sb.append(" ,");
            }
            TcaplusDb.ZoneGuideRecordTable.Builder value = valueWithVersion.value;
            index++;
            sb.append(value.getZoneId())
                    .append(":")
                    .append("(")
                    .append(value.getDirAssumeRegisterNum())
                    .append(" ,")
                    .append(value.getDirAssumeHardwareLevel1Num())
                    .append(" ,")
                    .append(value.getDirAssumeHardwareLevel2Num());
            sb.append(")");
        }
        LOGGER.info("getZoneGuideRecords finished, validZoneIds={}, data={}, requestId={}", validZoneIds, sb.toString(), result.requestId);
        return result.values;
    }

    /**
     * 导量预占位
     *
     * @param zoneId       目标zone
     * @param trafficLevel 导量等级
     * @return 目标zone最新导量数据
     */
    public static TcaplusDb.ZoneGuideRecordTable.Builder preDriveTrafficToTargetZone(int zoneId, int trafficLevel) {
        TcaplusDb.ZoneGuideRecordTable.Builder increaseReq = TcaplusDb.ZoneGuideRecordTable.newBuilder().setZoneId(zoneId).setDirAssumeRegisterNum(1);

        switch (trafficLevel) {
            case 1:
                increaseReq.setDirAssumeHardwareLevel1Num(1);
                break;
            case 2:
                increaseReq.setDirAssumeHardwareLevel2Num(1);
                break;
            default:
                LOGGER.warn("atTraffic preDriveTrafficToTargetZone unknown trafficLevel={}, skip tcaplus field setting", trafficLevel);
        }

        IncreaseOption.Builder increaseOption = IncreaseOption.newBuilder();
        increaseOption.setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL);
        // increase并携带最新数据返回
        IncreaseResult<TcaplusDb.ZoneGuideRecordTable.Builder> increaseResult = DbManager.getInstance().increase(increaseReq, increaseOption.build());
        if (!increaseResult.isOk()) {
            LOGGER.warn("zoneId={}, trafficLevel={}, increase tcaplus errorCode={}", zoneId, trafficLevel, increaseResult.getCode());
            return null;
        }
        LOGGER.info("zoneId={}, trafficLevel={}, increaseResult={}", zoneId, trafficLevel, increaseResult.value);
        increaseResult.value.setDirAssumeRegisterNum(increaseResult.value.getDirAssumeRegisterNum() - 1);
        switch (trafficLevel) {
            case 1:
                increaseResult.value.setDirAssumeHardwareLevel1Num(increaseResult.value.getDirAssumeHardwareLevel1Num() - 1);
                break;
            case 2:
                increaseResult.value.setDirAssumeHardwareLevel2Num(increaseResult.value.getDirAssumeHardwareLevel2Num() - 1);
                break;
            default:

        }
        return increaseResult.value;
    }
}

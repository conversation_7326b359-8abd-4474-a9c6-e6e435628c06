package com.yorha.directory.utils;

import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.config.ConfigObj;
import com.yorha.common.server.discovery.ZoneItem;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.directory.db.DirDbActionWrapper;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class DriveTrafficHelper {

    private static final Logger LOGGER = LogManager.getLogger(DriveTrafficHelper.class);

    /**
     * 轮询起点
     */
    private static final AtomicInteger SUGGEST_ZONE_INDEX = new AtomicInteger(0);
    /**
     * 各zone导量数据内存缓存
     */
    private static volatile Map<Integer, TcaplusDb.ZoneGuideRecordTable.Builder> zoneGuideInfo = new ConcurrentHashMap<>();

    private DriveTrafficHelper() {
    }

    /**
     * 刷新tcaplus数据
     */
    public static void refreshTcaplusData() {
        List<ValueWithVersion<TcaplusDb.ZoneGuideRecordTable.Builder>> zoneGuideRecords = DirDbActionWrapper.getZoneGuideRecords();
        // 日志在上面已经打过
        if (zoneGuideRecords.isEmpty()) {
            return;
        }

        final Map<Integer, TcaplusDb.ZoneGuideRecordTable.Builder> newTcaplusData = new ConcurrentHashMap<>();
        for (ValueWithVersion<TcaplusDb.ZoneGuideRecordTable.Builder> valueWithVersion : zoneGuideRecords) {
            newTcaplusData.put(valueWithVersion.value.getZoneId(), valueWithVersion.value);
        }
        zoneGuideInfo = newTcaplusData;
    }

    /**
     * 是否所有存活服务器状态为对内
     *
     * @return true==所有错误服务器状态为对内
     */
    public static boolean isAllAliveServerInternal() {
        for (ZoneItem zoneItem : DefaultZoneGateItemHandler.getInstance().getZoneList()) {
            final ServerOpenStatus curStatus = ServerOpenStatus.valueOf(zoneItem.getOpenStatus());
            if (curStatus == ServerOpenStatus.OPEN) {
                return false;
            }
        }

        return true;
    }


    public static Pair<Integer, CommonEnum.DriveTrafficType> tryDriveTraffic(final int driveTrafficLevel, final String channelName) {
        // 根据配置导量比例尝试导量
        LOGGER.info("try findFitSuggestZone for driveTrafficLevel={}, channelName={}", driveTrafficLevel, channelName);
        Integer suggestZoneId = DriveTrafficHelper.findFitSuggestZone(driveTrafficLevel, channelName);
        if (suggestZoneId != null) {
            return Pair.of(suggestZoneId, CommonEnum.DriveTrafficType.DTAT_NORMAL);
        }

        // 寻找推荐服中未满的
        LOGGER.info("try findNotFullSuggestZone for driveTrafficLevel={}, channelName={}", driveTrafficLevel, channelName);
        suggestZoneId = DriveTrafficHelper.findNotFullSuggestZone(driveTrafficLevel, channelName);
        if (suggestZoneId != null) {
            return Pair.of(suggestZoneId, CommonEnum.DriveTrafficType.DTAT_SERVER_UNFULLFILLED);
        }

        // 寻找未满服务器（极限情况全满）
        LOGGER.info("try findNotFullZone for driveTrafficLevel={}, channelName={}", driveTrafficLevel, channelName);
        suggestZoneId = DriveTrafficHelper.findNotFullZone(driveTrafficLevel, channelName);
        return Pair.of(suggestZoneId, CommonEnum.DriveTrafficType.DTAT_SERVER_FULLFILLED);
    }


    public static CommonEnum.ServerEnterStatus getZoneServerOpenStatus(final int zoneId) {
        Supplier<CommonEnum.ServerEnterStatus> serverEnterStatusSupplier = () -> {
            ZoneItem targetZone = DefaultZoneGateItemHandler.getInstance().getZone(zoneId);
            // 心跳中无对应进程（维护）
            if (targetZone == null) {
                LOGGER.info("DriveTrafficHelper getZoneServerOpenStatus zoneId={} no heartBeat", zoneId);
                return CommonEnum.ServerEnterStatus.SES_MAINTENANCE;
            }

            if (getZoneConfigOrNull(zoneId) == null) {
                LOGGER.info("DriveTrafficHelper getZoneServerOpenStatus zoneId={} no zoneConfig", zoneId);
                return CommonEnum.ServerEnterStatus.SES_MAINTENANCE;
            }

            final ServerOpenStatus curStatus = ServerOpenStatus.valueOf(targetZone.getOpenStatus());

            // 服务器状态非对外（维护）
            if (curStatus != ServerOpenStatus.OPEN) {
                LOGGER.info("DriveTrafficHelper getZoneServerOpenStatus zoneId={} not open", zoneId);
                return CommonEnum.ServerEnterStatus.SES_MAINTENANCE;
            }

            long openZoneTime = targetZone.getOpenTsMs();
            // 开服时间在当前时间后（未开放）
            if (openZoneTime > SystemClock.now()) {
                LOGGER.info("DriveTrafficHelper getZoneServerOpenStatus zoneId={} openZoneTime={} after now", zoneId, openZoneTime);
                return CommonEnum.ServerEnterStatus.SES_WHITE_ONLY;
            }
            // 正常
            return CommonEnum.ServerEnterStatus.SES_OPEN;
        };

        CommonEnum.ServerEnterStatus result = serverEnterStatusSupplier.get();
        LOGGER.info("DriveTrafficHelper getZoneServerOpenStatus zoneId={} status={}", zoneId, result);
        return result;
    }

    @Nullable
    private static Integer findFitSuggestZone(final int trafficLevel, final String channelName) {
        List<Integer> suggestZoneList = DefaultZoneGateItemHandler.getInstance().getSuggestZoneList();
        if (suggestZoneList.isEmpty()) {
            return null;
        }
        // 轮播查找
        final int index = SUGGEST_ZONE_INDEX.getAndIncrement();
        LOGGER.info("atTraffic DriveTrafficHelper findFitSuggestZone cur index={}", index);
        int startIndex = index % suggestZoneList.size();
        for (int i = 0; i < suggestZoneList.size(); i++) {
            Integer suggestZoneId = suggestZoneList.get(startIndex);
            startIndex = (startIndex + 1) % suggestZoneList.size();
            final Pair<String, Integer> gateAddressByChannel = DefaultZoneGateItemHandler.getInstance().getGateAddressByChannel(suggestZoneId, channelName);
            if (gateAddressByChannel == null) {
                LOGGER.info("atTraffic DriveTrafficHelper findFitSuggestZone zoneId={} no match channel, continue", suggestZoneId);
                continue;
            }
            final boolean fitTrafficStrategyZone = isFitTrafficStrategyZone(trafficLevel, suggestZoneId, index);
            if (!fitTrafficStrategyZone) {
                LOGGER.info("atTraffic DriveTrafficHelper findFitSuggestZone zoneId={} not fit trafficStrategyZone, continue", suggestZoneId);
                continue;
            }
            return suggestZoneId;
        }
        return null;
    }

    @Nullable
    private static Integer findNotFullSuggestZone(final int trafficLevel, final String channelName) {
        Collection<Integer> suggestZoneList = DefaultZoneGateItemHandler.getInstance().getSuggestZoneList();
        for (final Integer suggestZoneId : suggestZoneList) {
            if (DefaultZoneGateItemHandler.getInstance().getGateAddressByChannel(suggestZoneId, channelName) != null
                    && isNotFullZone(trafficLevel, suggestZoneId)) {
                return suggestZoneId;
            }
            LOGGER.info("zoneId={} not fit trafficStrategyZone, continue", suggestZoneId);
        }
        return null;
    }

    @Nullable
    private static Integer findNotFullZone(final int trafficLevel, final String channelName) {
        // 从服务发现中存活zone中寻找
        for (final Integer zoneId : DefaultZoneGateItemHandler.getInstance().getWorldFoundZoneIdSet()) {
            if (DefaultZoneGateItemHandler.getInstance().getGateAddressByChannel(zoneId, channelName) != null && isNotFullZone(trafficLevel, zoneId)) {
                return zoneId;
            }
        }
        return null;
    }


    /**
     * 目标zone是否符合导量规则（db注册数与配置注册上限比对，db对应档位注册比例与实际比例比对）
     *
     * @param trafficLevel 导量机型档位
     * @param zoneId       zoneId
     * @return true==目标zone符合导量规则
     */
    private static boolean isFitTrafficStrategyZone(final int trafficLevel, final int zoneId, final int index) {
        // 普通玩家不可进
        if (getZoneServerOpenStatus(zoneId) != CommonEnum.ServerEnterStatus.SES_OPEN) {
            LOGGER.info("zoneId={} not open for normal player, index={}", zoneId, index);
            return false;
        }

        ConfigObj zoneConfig = getZoneConfigOrNull(zoneId);
        if (zoneConfig == null) {
            LOGGER.info("zoneId={} config null, index={}", zoneId, index);
            return false;
        }

        TcaplusDb.ZoneGuideRecordTable.Builder zoneInfo = getZoneRegisterInfo(zoneId);
        if (zoneInfo == null) {
            LOGGER.info("has no zoneRegisterInfo, zoneId={} index={}, zoneInfo null", zoneId, index);
            return false;
        }
        final int checkPreTraffic = checkDriveTraffic(zoneConfig, zoneInfo, trafficLevel);

        if (checkPreTraffic <= 0) {
            LOGGER.info("checkPreTraffic < 0, zoneId={} index={} checkPreTraffic={}", zoneId, index, checkPreTraffic);
            return false;
        }

        TcaplusDb.ZoneGuideRecordTable.Builder preDriveTrafficUpdate = DirDbActionWrapper.preDriveTrafficToTargetZone(zoneId, trafficLevel);
        if (preDriveTrafficUpdate == null) {
            LOGGER.info("preDriveTrafficUpdate null, zoneId={} index={}", zoneId, index);
            return false;
        }
        // 更新increase结果
        putZoneRegisterInfo(zoneId, preDriveTrafficUpdate);

        //二次校验
        return checkDriveTraffic(zoneConfig, preDriveTrafficUpdate, trafficLevel) > 0;
    }

    /**
     * 目标zone是否未满（db注册数与配置注册上限比对），含二次比对
     *
     * @param trafficLevel 导量机型档位
     * @param zoneId       zoneId
     * @return true==目标zone未满
     */
    public static boolean isNotFullZone(final int trafficLevel, final int zoneId) {
        // 普通玩家不可进
        if (getZoneServerOpenStatus(zoneId) != CommonEnum.ServerEnterStatus.SES_OPEN) {
            LOGGER.info("atTraffic DriveTrafficHelper isNotFullZone zoneId={} not open for normal player", zoneId);
            return false;
        }

        ConfigObj zoneConfig = getZoneConfigOrNull(zoneId);
        if (zoneConfig == null) {
            return false;
        }

        TcaplusDb.ZoneGuideRecordTable.Builder zoneInfo = getZoneRegisterInfo(zoneId);
        if (zoneInfo == null) {
            LOGGER.info("atTraffic DriveTrafficHelper isNotFullZone has no zoneRegisterInfo, zoneId={}", zoneId);
            return false;
        }
        int checkPreTraffic = checkDriveTraffic(zoneConfig, zoneInfo, trafficLevel);

        if (checkPreTraffic < 0) {
            return false;
        }

        TcaplusDb.ZoneGuideRecordTable.Builder preDriveTrafficUpdate = DirDbActionWrapper.preDriveTrafficToTargetZone(zoneId, trafficLevel);
        if (preDriveTrafficUpdate == null) {
            return false;
        }

        // 更新increase结果
        putZoneRegisterInfo(zoneId, preDriveTrafficUpdate);

        //二次校验
        return checkDriveTraffic(zoneConfig, preDriveTrafficUpdate, trafficLevel) >= 0;
    }

    /**
     * 根据zoneId获得对应zone etcd配置
     *
     * @param zoneId zoneId
     * @return etcd配置
     */
    private static ConfigObj getZoneConfigOrNull(int zoneId) {
        ConfigObj zoneConfig;
        zoneConfig = ClusterConfigUtils.getZoneConfigOrNull(zoneId);
        if (zoneConfig == null) {
            LOGGER.warn("DriveTrafficHelper getZoneConfigOrNull zoneId={} has no zoneConfig", zoneId);
        }
        return zoneConfig;
    }

    /**
     * 获得内存中zone注册信息（周期更新）
     *
     * @param zoneId zoneId
     * @return Zone注册信息
     */
    @javax.annotation.Nullable
    private static TcaplusDb.ZoneGuideRecordTable.Builder getZoneRegisterInfo(int zoneId) {
        TcaplusDb.ZoneGuideRecordTable.Builder zoneInfo;

        zoneInfo = zoneGuideInfo.getOrDefault(zoneId, null);
        return zoneInfo;
    }

    /**
     * 更新指定zone注册信息
     *
     * @param zoneId   zoneId
     * @param zoneInfo Zone注册信息
     */
    private static void putZoneRegisterInfo(int zoneId, TcaplusDb.ZoneGuideRecordTable.Builder zoneInfo) {
        zoneGuideInfo.put(zoneId, zoneInfo);
    }

    /**
     * 检查可否导量（注册总上限、档位上限）
     *
     * @param zoneConfig       zone etcd配置
     * @param zoneRegisterInfo zone db数据
     * @param trafficLevel     导量档位
     * @return -1==超注册总上限 0==超档位上限 1==Pass
     */
    private static int checkDriveTraffic(final ConfigObj zoneConfig, final TcaplusDb.ZoneGuideRecordTable.Builder zoneRegisterInfo, final int trafficLevel) {
        final int registerLimit = zoneConfig.getIntItem("register_limit");

        // 是否超注册上限
        if (zoneRegisterInfo.getDirAssumeRegisterNum() >= registerLimit) {
            LOGGER.info("dir_assume_zone_register_num over limit, registerLimit={}, trafficLevel={}, zoneRegisterInfo={}",
                    registerLimit, trafficLevel, zoneRegisterInfo);
            return -1;
        }

        int curPercent;
        switch (trafficLevel) {
            case 1:
                curPercent = zoneRegisterInfo.getDirAssumeHardwareLevel1Num() * 100 / registerLimit;
                break;
            case 2:
                curPercent = zoneRegisterInfo.getDirAssumeHardwareLevel2Num() * 100 / registerLimit;
                break;
            default:
                LOGGER.warn("unknown trafficLevel={}", trafficLevel);
                return 0;
        }

        Map<Integer, Integer> trafficLevelPercent = zoneConfig.getIntegerMapItem("drive_traffic_level_percent");
        final int maxPercent;
        // 未配置则不限制
        if (trafficLevelPercent == null) {
            maxPercent = 100;
            LOGGER.info("zoneId={} no etcd config 'drive_traffic_level_percent', so maxPercent={}", zoneRegisterInfo.getZoneId(), maxPercent);
        } else {
            // 未配置则不限制
            if (!trafficLevelPercent.containsKey(trafficLevel)) {
                maxPercent = 100;
                LOGGER.info("zoneId={} etcd config 'drive_traffic_level_percent'={}, no config for trafficLevel={}, so maxPercent={}",
                        zoneRegisterInfo.getZoneId(), trafficLevelPercent, trafficLevel, maxPercent);
            } else {
                maxPercent = trafficLevelPercent.get(trafficLevel);
            }
        }
        LOGGER.info("dir_assume_zone_drive_level_num dir assume zone={}, driveTrafficLevel1={}, driveTrafficLevel2={}, maxPercent={}, curPercent={}",
                zoneRegisterInfo.getZoneId(), zoneRegisterInfo.getDirAssumeHardwareLevel1Num(),
                zoneRegisterInfo.getDirAssumeHardwareLevel2Num(), maxPercent, curPercent);
        // 对应档位是否超注册上限
        if (curPercent < maxPercent) {
            return 1;
        }
        return 0;
    }


}

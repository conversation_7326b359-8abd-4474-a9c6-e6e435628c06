package com.yorha.directory.controller;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.auth.server.AuthChannelDispatcher;
import com.yorha.common.auth.server.gemini.YorhaAuthHandler;
import com.yorha.common.auth.server.result.AuthResult;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.CommandMapping;
import com.yorha.common.io.Controller;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.Pair;
import com.yorha.directory.DirSession;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CsAccount;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.proto.CsAccount.DirRegisterAccount_C2S_Msg;
import static com.yorha.proto.CsAccount.DirRegisterAccount_S2C_Msg;

/**
 * dir上代理账号注册鉴权服务
 *
 * <AUTHOR>
 */
@Controller(module = CommonEnum.ModuleEnum.ME_ACCOUNT)
public class AccountController {
    private static final Logger LOGGER = LogManager.getLogger(AccountController.class);

    @CommandMapping(code = MsgType.DIRREGISTERACCOUNT_C2S_MSG)
    public GeneratedMessageV3 handle(DirSession session, DirRegisterAccount_C2S_Msg msg, int seqId) {

        LOGGER.info("{} atDir registerOrLogin account start, session={} msg={} seqId={}", LogKeyConstants.GAME_PLAYER_LOGIN, session, msg, seqId);
        if (!AuthChannelDispatcher.isGeminiChannelAllow()) {
            LOGGER.warn("{} atDir registerOrLogin account failed, channelType:{} is not supported, DirSession:{}",
                    LogKeyConstants.GAME_PLAYER_LOGIN, AuthChannelDispatcher.getAuthChannelType(), session);
            throw new GeminiException(ErrorCode.AUTH_CHANNEL_WRONG);
        }

        CommonMsg.YoAccountToken.Builder builder = CommonMsg.YoAccountToken.newBuilder();
        AuthResult<CommonMsg.YoAccountToken> authResult = getR(msg, builder);

        DirRegisterAccount_S2C_Msg.Builder response = DirRegisterAccount_S2C_Msg.newBuilder()
                .setRetCode(authResult.getRetCode())
                .setToken(builder);

        if (authResult.isSuccess()) {
            LOGGER.info("{} atDir registerOrLogin account success, registerAccount {} {}", LogKeyConstants.GAME_PLAYER_LOGIN, response, session);
        } else {
            LOGGER.error("{} atDir registerOrLogin account failed, registerAccount {} {}", LogKeyConstants.GAME_PLAYER_LOGIN, response, session);
        }
        return response.build();
    }

    /**
     * 根据c2s的消息来获取鉴权结果，builder是传进来用来设置值的
     */
    private AuthResult<CommonMsg.YoAccountToken> getR(DirRegisterAccount_C2S_Msg msg, CommonMsg.YoAccountToken.Builder builder) {
        CommonMsg.AccountIdentity identity = msg.getIdentity();


        CommonMsg.AccountIdentityDeviceId d = identity.getDeviceId();
        String deviceId = d.getDeviceId();
        if (StringUtils.isNotEmpty(deviceId)) {
            // 走的设备id的直连模式

            // 使用设备id做openId吧，不然就得db存储映射关系了,token统一一个
            builder.setOpenId(deviceId);
            builder.setAccessToken("deviceId_login");
            return AuthResult.ofSuccess();
        }

        // yorha用户鉴权登录

        // 用账号名去换一个authorization_code
        CommonMsg.AccountIdentityYo entity = identity.getYoId();
        String code = YorhaAuthHandler.getInstance().getCode(entity.getAccountName());
        if (code == null) {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_ACCOUNT_NOT_EXIST, "getCode fail");
        }

        // 再用这个authorization_code去换对应的openId和accessToken
        Pair<String, String> res = YorhaAuthHandler.getInstance().getAccessToken(code);
        if (res == null) {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_TOKEN_INVALID, "getAccessToken fail");
        }

        builder.setOpenId(res.getFirst());
        builder.setAccessToken(res.getSecond());

        return AuthResult.ofSuccess();
    }

    @CommandMapping(code = MsgType.DIRDELACCOUNT_C2S_MSG)
    public GeneratedMessageV3 handle(DirSession dirSession, CsAccount.DirDelAccount_C2S_Msg msg, int seqId) {
        return CsAccount.DirDelAccount_S2C_Msg.getDefaultInstance();
    }

    @CommandMapping(code = MsgType.DIRPING_C2S_MSG)
    public GeneratedMessageV3 handle(DirSession dirSession, CsAccount.DirPing_C2S_Msg msg, int seqId) {
        return CsAccount.DirPing_S2C_Msg.getDefaultInstance();
    }
}

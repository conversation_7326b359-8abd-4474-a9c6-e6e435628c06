package com.yorha.directory;

import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import io.vertx.core.impl.ConcurrentHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;

public class DirSessionManager {
    private static final Logger LOGGER = LogManager.getLogger(DirSessionManager.class);

    private DirSessionManager() {
    }

    private static class InstanceHolder {
        private static final DirSessionManager INSTANCE = new DirSessionManager();
    }

    public static DirSessionManager getInstance() {
        return InstanceHolder.INSTANCE;
    }

    private final Set<Long> curSession = new ConcurrentHashSet<>();

    public void addSession(final long sessionId) {
        final boolean add = curSession.add(sessionId);
        if (!add) {
            LOGGER.error("DirSessionManager add session repeat, sessionId={}", sessionId);
            return;
        }
        MonitorUnit.SESSION_ADD_NUM_COUNTER.labels(ServerContext.getBusId()).inc();
        MonitorUnit.SESSION_CUR_NUM_GAUGE.labels(ServerContext.getBusId()).set(getSessionNum());
        LOGGER.info("DirSessionManager add session into mgr, sessionId={}, cur num={}", sessionId, getSessionNum());
    }

    public void removeSession(final long sessionId) {
        final boolean remove = curSession.remove(sessionId);
        if (!remove) {
            LOGGER.error("DirSessionManager remove session not exists, sessionId={}", sessionId);
            return;
        }
        MonitorUnit.SESSION_REMOVE_NUM_COUNTER.labels(ServerContext.getBusId()).inc();
        MonitorUnit.SESSION_CUR_NUM_GAUGE.labels(ServerContext.getBusId()).set(getSessionNum());
        LOGGER.info("DirSessionManager remove session from mgr, sessionId={}, cur num={}", sessionId, getSessionNum());
    }

    /**
     * 当前连接数
     */
    public int getSessionNum() {
        return this.curSession.size();
    }
}

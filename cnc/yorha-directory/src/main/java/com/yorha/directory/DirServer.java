package com.yorha.directory;

import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.auth.server.AuthChannelDispatcher;
import com.yorha.common.concurrent.NamedRunnable;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiFiberPoolExecutor;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.db.DbManager;
import com.yorha.common.etcd.EtcdClient;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.http.CmdReqHandler;
import com.yorha.common.http.HttpServerHandler;
import com.yorha.common.io.CommandMgr;
import com.yorha.common.io.ParseEngine;
import com.yorha.common.resource.ResLoader;
import com.yorha.common.server.AbstractServer;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.ProcessUtil;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.directory.controller.ServerController;
import com.yorha.directory.utils.DriveTrafficHelper;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.LengthFieldPrepender;
import io.netty.handler.timeout.IdleStateHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.InetSocketAddress;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 目录服
 *
 * <AUTHOR>
 */
public class DirServer extends AbstractServer {
    private static final Logger LOGGER = LogManager.getLogger(DirServer.class);
    private final EventLoopGroup bossGroup = new NioEventLoopGroup(1);
    private final EventLoopGroup workerGroup = new NioEventLoopGroup(4);
    private final GeminiThreadPoolExecutor scheduler;
    private final GeminiFiberPoolExecutor executor;

    private static final int DIR_WORKER_THREAD_NUM = 4;

    public DirServer(String[] args) {
        super(args);
        // 调度器
        this.scheduler = ConcurrentHelper.newFixedThreadExecutor("dir-scheduler", DIR_WORKER_THREAD_NUM, 0, false);
        // 执行器
        this.executor = ConcurrentHelper.newFixedFiberExecutor("dir-worker", this.scheduler, 4000, 6000);
        this.executor.setRejectedExecutionHandler(((r, executor) -> {
            WechatLog.error("{} refuse {}!", executor, r);
        }));
    }

    @Override
    public String getServerName() {
        return "dirsvr" + ServerContext.getServerInfo().getBusId();
    }

    @Override
    protected void onStart() throws GeminiException {
        if (ServerContext.getEtcdClient() == null) {
            List<String> addresses = ClusterConfigUtils.getWorldConfig().getListStrItem("etcd_address_list");
            if (addresses.isEmpty()) {
                throw new GeminiException("etcd config is null");
            }
            String[] etcdAddressArray = addresses.toArray(new String[addresses.size()]);
            final EtcdClient etcdClient = new EtcdClient(etcdAddressArray, null, null);
            ServerContext.setEtcdClient(etcdClient);
        }
        //数据库
        DbManager.getInstance().initFromServerContext();


        CommandMgr.getInstance().init(ServerController.class.getPackage().getName());

        // 加载策划数据
        final String localPath = ServerContext.getGameDataLocalPath();
        ResLoader.load(localPath);

        // 启动后台HTTP服务
        this.initHttpServer(1);
        HttpServerHandler.getInstance().registerUrlHandler(new CmdReqHandler(new DirCmdProcessorImpl()));
        HttpServerHandler.getInstance().switchOn();

        // id生成器启动
        IdFactory.run(ClusterConfigUtils.getWorldConfig().getStringItem("cluster_id_factory_key"), 2);

        // 触发auth channel加载
        boolean geminiChannelAllow = AuthChannelDispatcher.isGeminiChannelAllow();
        LOGGER.info("DirServer geminiChannelAllow={}", geminiChannelAllow);

        //启动游戏服务
        final int innerPort = ServerContext.getInnerPort();
        final boolean isCompress = ClusterConfigUtils.getWorldConfig().getBooleanItem("compress");
        final boolean isCrypto = ClusterConfigUtils.getWorldConfig().getBooleanItem("crypto");
        final int compressThreshold = ClusterConfigUtils.getWorldConfig().getIntItem("compress_threshold");
        this.initDirectoryServer(isCompress, isCrypto, compressThreshold, innerPort);
        if (!ServerContext.getEtcdClient().watchPrefix(ActorClusterUrlUtils.etcdWorldZoneFoundPrefix(), DefaultZoneGateItemHandler.getInstance())) {
            throw new GeminiException("watchPrefix {} fail!", ActorClusterUrlUtils.etcdWorldZoneFoundPrefix());
        }
        if (!ServerContext.getEtcdClient().watchPrefix(ActorClusterUrlUtils.etcdZoneConfigPrefix(), DefaultZoneGateItemHandler.getInstance())) {
            throw new GeminiException("watchPrefix {} fail!", ActorClusterUrlUtils.etcdZoneConfigPrefix());
        }

        this.trafficDataUpdateTimer = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(
                new NamedRunnable("Update Traffic Data", () -> {
                    ConcurrentHelper.newFiber("update Traffic Data", DriveTrafficHelper::refreshTcaplusData).start();
                }),
                0,
                20,
                TimeUnit.SECONDS);
    }

    @Override
    protected void postStart() {
        super.postStart();
        // 写dir的pid
        ProcessUtil.savePid(ServerContext.getBusId());
    }

    private ScheduledFuture<?> trafficDataUpdateTimer;

    private void initDirectoryServer(boolean isCompress, boolean isCrypto, int compressThreshold, int innerPort) {
        LOGGER.info("initialize TCP to start");
        ParseEngine instance = ParseEngine.getInstance();
        instance.setCompress(isCompress);
        instance.setCrypto(isCrypto);
        instance.setCompressThreshold(compressThreshold);
        // Configure the server.
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup);
        bootstrap.channel(NioServerSocketChannel.class);
        bootstrap.option(ChannelOption.SO_REUSEADDR, true);
        bootstrap.childOption(ChannelOption.TCP_NODELAY, true);
        // 43690 为默认值, 改为1K
        bootstrap.childOption(ChannelOption.SO_RCVBUF, 1024);
        // 32k为默认值, 改成1K
        bootstrap.childOption(ChannelOption.SO_SNDBUF, 1024);
        // 默认高水位是64K，低水位是64K
        bootstrap.childOption(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(64 * 1024, 64 * 1024));
        // 把worker线程给池化
        bootstrap.childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT);
        bootstrap.childHandler(new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) {
                final ChannelPipeline pip = ch.pipeline();
                // 暂定为20s的心跳
                final int heartBeat = 20;
                pip.addLast(new IdleStateHandler(heartBeat, 0, heartBeat));
                // 发消息处理器
                pip.addLast(new LengthFieldPrepender(4));
                // 收消息处理器
                pip.addLast(new LengthFieldBasedFrameDecoder(10240, 0, 4, 0, 4));
                // 处理函数
                pip.addLast(new DirectoryChannelInboundHandler());
            }
        });
        // Bind and start to accept incoming connections.
        try {
            bootstrap.bind(new InetSocketAddress(innerPort)).sync();
            LOGGER.info("gemini_port game tcp server start on {}", innerPort);
            LOGGER.info("initialize TCP to end");
        } catch (Exception e) {
            throw new GeminiException("initialize TCP failed", e);
        }
    }

    @Override
    protected void onStop() throws Exception {
        LOGGER.warn("gemini_system {} onStop", this);

        // 最多等待10秒，dir关闭所有cs连接
        int i = 0;
        while (i < 10) {
            int sessionNum = DirSessionManager.getInstance().getSessionNum();
            if (sessionNum != 0) {
                LOGGER.warn("DirServer onStop but sessionNum={}", sessionNum);
                Thread.sleep(1_000);
                i++;
            } else {
                break;
            }
        }

        LOGGER.info("DirServer cur session num={}", DirSessionManager.getInstance().getSessionNum());

        // 关闭http端口
        this.stopHttpServer();
        IdFactory.shutdown();
        if (ServerContext.getEtcdClient() != null) {
            ServerContext.getEtcdClient().shutDown();
            ServerContext.setEtcdClient(null);
        }
        if (this.trafficDataUpdateTimer != null) {
            this.trafficDataUpdateTimer.cancel(false);
            this.trafficDataUpdateTimer = null;
        }
        //断开数据库连接
        DbManager.getInstance().destroy();
        // 优雅退出，释放线程池资源
        this.bossGroup.shutdownGracefully();
        this.workerGroup.shutdownGracefully();
        // 优雅关闭执行器
        this.shutdownExecutorGracefully();
    }

    public GeminiFiberPoolExecutor getExecutor() {
        return executor;
    }

    private void shutdownExecutorGracefully() {
        if (this.executor == null) {
            return;
        }
        this.executor.shutdown();
        try {
            final boolean isOk = this.executor.awaitTermination(5, TimeUnit.SECONDS);
            LOGGER.info("shutdownExecutorGracefully executor! isOk {}", isOk);
        } catch (InterruptedException e) {
            LOGGER.error("shutdownExecutorGracefully executor!", e);
        }
        this.scheduler.shutdown();
        try {
            final boolean isOk = this.scheduler.awaitTermination(5, TimeUnit.SECONDS);
            LOGGER.info("shutdownExecutorGracefully scheduler! isOk {}", isOk);
        } catch (InterruptedException e) {
            LOGGER.error("shutdownExecutorGracefully scheduler!", e);
        }
    }
}

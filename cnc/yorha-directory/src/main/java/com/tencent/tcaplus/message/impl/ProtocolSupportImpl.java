//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.tencent.tcaplus.message.impl;

import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tcaplus.client.impl.RequestLite;
import com.tencent.tcaplus.client.impl.ResponseLite;
import com.tencent.tcaplus.message.MessageBuffer;
import com.tencent.tcaplus.message.ProtocolSupport;
import com.tencent.tcaplus.util.ClientInfoUtil;
import com.tencent.tdr.tcapdir_protocol_cs.*;
import com.tencent.tdr.tcaplus_protocol_cs.TCaplusAppSignupReq;
import com.tencent.tdr.tcaplus_protocol_cs.TCaplusPkg;
import com.tencent.tdr.tcaplus_protocol_cs.TCaplusRouterInfo;
import com.tencent.tdr.tsf4g.TdrException;
import com.yorha.common.server.ServerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

public class ProtocolSupportImpl implements ProtocolSupport {
    private final TCaplusPkg reqPkg = new TCaplusPkg();
    private TCaplusPkg rspPkg = new TCaplusPkg();
    private String signature;
    private final String apiVersion;
    private int appId;
    private int zoneId;
    MessageBufferPool sendPool = MessageBufferPool.create();
    MessageBufferPool recvPool = MessageBufferPool.create();
    private final AtomicInteger seqProducer = new AtomicInteger(0);
    private static final Logger LOGGER = LoggerFactory.getLogger(ProtocolSupportImpl.class);
    // yorha-fix
    private final ReentrantLock reqReentrantLock = new ReentrantLock();
    private final ReentrantLock rspReentrantLock = new ReentrantLock();

    public ProtocolSupportImpl(int appId, int zoneId, String signature, String apiVersion) {
        this.appId = appId;
        this.zoneId = zoneId;
        this.signature = signature;
        this.apiVersion = apiVersion;
        // yorha-fix
        if (!ServerContext.isProdSvr()) {
            rspPkg = null;
        }
    }

    @Override
    public MessageBuffer getAuthorizedReq() throws TdrException {
        // yorha-fix
        TCaplusPkg sysReqPkg = new TCaplusPkg();
        sysReqPkg.Head.construct();
        sysReqPkg.Head.Magic = 30019;
        sysReqPkg.Head.Version = 0;
        sysReqPkg.Head.Cmd = 51;
        sysReqPkg.Head.Seq = this.seqProducer.getAndIncrement();
        sysReqPkg.Head.RouterInfo = new TCaplusRouterInfo();
        sysReqPkg.Head.RouterInfo.AppID = this.appId;
        sysReqPkg.Head.RouterInfo.ZoneID = this.zoneId;
        sysReqPkg.Body.construct(this.reqPkg.Head.Cmd);
        sysReqPkg.Body.AppSignupReq = new TCaplusAppSignupReq();
        sysReqPkg.Body.AppSignupReq.Signature = this.signature.getBytes();
        sysReqPkg.Body.AppSignupReq.Type = 0;
        return this.sendPool.packMsgBuffer(sysReqPkg);
    }

    @Override
    public MessageBuffer getProxyStopRsp(MessageBuffer req) throws TdrException {
        TCaplusPkg pkg = new TCaplusPkg();
        pkg.Head.construct();
        pkg.Head.Magic = 30019;
        pkg.Head.Version = req.getVersion();
        pkg.Head.Cmd = 68;
        pkg.Head.Seq = req.getSeq();
        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer getProxyHeartBeatReq() throws TdrException {
        TCaplusPkg pkg = new TCaplusPkg();
        pkg.Head.construct();
        pkg.Head.Magic = 30019;
        pkg.Head.Cmd = 53;
        pkg.Head.Seq = this.seqProducer.getAndIncrement();
        pkg.Body.construct(pkg.Head.Cmd);
        pkg.Body.HeartBeatReq.CurTime = ClientInfoUtil.getCurTdrDateTime();
        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer getDirHeartbeatReq() throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        pkg.Head.construct();
        pkg.Head.Magic = -27275;
        pkg.Head.Cmd = 102;
        pkg.Head.Version = 0;
        pkg.Head.AppID = (long) this.appId;
        pkg.Body.construct(pkg.Head.Cmd);
        pkg.Body.ReqHeartBeat.HostTime = ClientInfoUtil.getCurTdrDateTime();
        pkg.Body.ReqHeartBeat.WithQos = 0;
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ReqHeartBeat:\n{}", pkg.visualize(0, '\n'));
        }

        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer getTableAndAccessReq() throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        pkg.Head.construct();
        pkg.Head.Magic = -27275;
        pkg.Head.Cmd = 106;
        pkg.Head.Version = 0;
        pkg.Head.AppID = (long) this.appId;
        pkg.Body.construct(pkg.Head.Cmd);
        pkg.Body.ReqGetTablesAndAccess = new ReqGetTablesAndAccess();
        pkg.Body.ReqGetTablesAndAccess.ZoneID = this.zoneId;
        pkg.Body.ReqGetTablesAndAccess.Signature = this.getSignature().getBytes();
        pkg.Body.ReqGetTablesAndAccess.Version = this.apiVersion.getBytes();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ReqGetTableAndAccessReq:\n{}", pkg.visualize(0, '\n'));
        }

        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer getSignUpAppReq() throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        pkg.Head.construct();
        pkg.Head.Magic = -27275;
        pkg.Head.Cmd = 100;
        pkg.Head.Version = 0;
        pkg.Head.AppID = (long) this.appId;
        pkg.Body.construct(pkg.Head.Cmd);
        pkg.Body.ReqSignUpApp = new ReqSignUpApp();
        pkg.Body.ReqSignUpApp.Signature = this.getSignature().getBytes();
        pkg.Body.ReqSignUpApp.Type = 0;
        pkg.Body.ReqSignUpApp.TableCount = 1;
        pkg.Body.ReqSignUpApp.TableList[0].ZoneID = this.zoneId;
        pkg.Body.ReqSignUpApp.ClientInfo.ApiVersion = 0;
        pkg.Body.ReqSignUpApp.ClientInfo.DetailVer = "java".getBytes();
        pkg.Body.ReqSignUpApp.ClientInfo.Platform = (short) ClientInfoUtil.getPlatform();
        pkg.Body.ReqSignUpApp.ClientInfo.TableCount = 0;
        pkg.Body.ReqSignUpApp.ClientInfo.TraitBits = 0;
        pkg.Body.ReqSignUpApp.ClientInfo.HostTime = ClientInfoUtil.getCurTdrDateTime();
        pkg.Body.ReqSignUpApp.ClientInfo.Version = ClientInfoUtil.getVersion().getBytes();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ReqGetSignUpAppReq:\n{}", pkg.visualize(0, '\n'));
        }

        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer getMetadataReq(String tableName) throws TdrException {
        TCaplusPkg pkg = new TCaplusPkg();
        pkg.Head.construct();
        pkg.Head.Magic = 30019;
        pkg.Head.Cmd = 27;
        pkg.Head.Seq = this.seqProducer.getAndIncrement();
        pkg.Body.construct(pkg.Head.Cmd);
        if (tableName != null) {
            byte[] byteName = tableName.getBytes();
            System.arraycopy(byteName, 0, pkg.Head.RouterInfo.TableName, 0, byteName.length);
            pkg.Head.RouterInfo.TableName[byteName.length] = 0;
            pkg.Head.RouterInfo.TableNameLen = byteName.length + 1;
        }

        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer getDirServerListReq() throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        pkg.Head.construct();
        pkg.Head.Magic = -27275;
        pkg.Head.Cmd = 104;
        pkg.Head.Version = 0;
        pkg.Head.AppID = (long) this.appId;
        pkg.Body.construct(pkg.Head.Cmd);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ReqGetDirServerListReq:\n{}", pkg.visualize(0, '\n'));
        }

        return this.sendPool.packMsgBuffer(pkg);
    }

    @Override
    public MessageBuffer packRequest(Request req) throws TdrException {
        // yorha-fix

        if (!ServerContext.isProdSvr()) {
            // 减少内存占用，改为每次new
            TCaplusPkg pkg = new TCaplusPkg();
            RequestLite reqImpl = (RequestLite) req;
            reqImpl.assemble(pkg);
            pkg.Head.Seq = this.seqProducer.getAndIncrement();
            return this.sendPool.packMsgBuffer(pkg);
        }

        reqReentrantLock.lock();
        try {
            RequestLite reqImpl = (RequestLite) req;
            reqImpl.assemble(this.reqPkg);
            this.reqPkg.Head.Seq = this.seqProducer.getAndIncrement();
            return this.sendPool.packMsgBuffer(this.reqPkg);
        } finally {
            reqReentrantLock.unlock();
        }
    }

    @Override
    public Response unpackResponse(MessageBuffer msg) throws TdrException {
        // yorha-fix
        rspReentrantLock.lock();
        try {
            // yorha-fix
            ResponseLite rsp = new ResponseLite();
            if (!ServerContext.isProdSvr()) {
                TCaplusPkg rspPkg = new TCaplusPkg();
                msg.unpack(rspPkg);
                rsp.disassembly(rspPkg);
                return rsp;
            }
            msg.unpack(this.rspPkg);
            rsp.disassembly(this.rspPkg);
            return rsp;
        } finally {
            rspReentrantLock.unlock();
        }
    }

    @Override
    public ResSignUpApp unpackSignUpRes(MessageBuffer msg) throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        msg.unpack(pkg);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ResSignUpApp:\n{}", pkg.visualize(0, '\n'));
        }

        if (pkg.Head.Cmd != 101) {
            LOGGER.error("unexpected cmd, cmd=" + pkg.Head.Cmd);
            return null;
        } else {
            if (pkg.Body.ResSignUpApp.Result != 0) {
                LOGGER.error("sign up failed, result=" + pkg.Body.ResSignUpApp.Result);
            }

            return pkg.Body.ResSignUpApp;
        }
    }

    @Override
    public ResGetTablesAndAccess unpackGetTablesAndAccessRes(MessageBuffer msg) throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        msg.unpack(pkg);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ResGetTablesAndAccess:\n{}", pkg.visualize(0, '\n'));
        }

        if (pkg.Head.Cmd != 107) {
            LOGGER.error("unexpected cmd, cmd=" + pkg.Head.Cmd);
            return null;
        } else {
            if (pkg.Body.ResGetTablesAndAccess.Result != 0) {
                LOGGER.error("getTableAndAccessReq() failed, result=" + pkg.Body.ResGetTablesAndAccess.Result);
            }

            return pkg.Body.ResGetTablesAndAccess;
        }
    }

    @Override
    public ResGetDirServerList unpackGetDirServerListRes(MessageBuffer msg) throws TdrException {
        TCapdirCSPkg pkg = new TCapdirCSPkg();
        msg.unpack(pkg);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("dump ResGetDirServerList:\n{}", pkg.visualize(0, '\n'));
        }

        if (pkg.Head.Cmd != 105) {
            LOGGER.error("unexpected cmd, cmd=" + pkg.Head.Cmd);
            return null;
        } else {
            if (pkg.Body.ResGetDirServerList.Result != 0) {
                LOGGER.error("getDirServerListReq() failed, result=" + pkg.Body.ResGetDirServerList.Result);
            }

            return pkg.Body.ResGetDirServerList;
        }
    }

    public String getSignature() {
        return this.signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public int getAppId() {
        return this.appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    public int getZoneId() {
        return this.zoneId;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    @Override
    public MessageBufferPool getSendPool() {
        return this.sendPool;
    }

    @Override
    public MessageBufferPool getRecvPool() {
        return this.recvPool;
    }
}

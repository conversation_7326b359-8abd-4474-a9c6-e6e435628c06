package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.common.constant.Constants;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 每个军团击败某类集结怪物的数量
 * 参数：要求野怪数量_野怪的Type_等级要求
 */
public class ClanKillRallyMonsterMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_CLAN;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_DEFEAT_THE_RALLY_MONSTER;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }

    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanKillRallyMonsterData) {
            ClanKillRallyMonsterData data = (ClanKillRallyMonsterData) taskData;
            if (!matchParam(data)) {
                return;
            }
            for (long killByClan : data.getKillByClans()) {
                Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
                MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.computeIfAbsent(killByClan, (key) -> {
                    MileStoneClanInfoProp prop = new MileStoneClanInfoProp();
                    prop.setClanId(key);
                    return prop;
                });
                setClanScore(mileStoneClanInfoProp, killByClan, mileStoneClanInfoProp.getScore() + 1, "onClanKillRallyMonster");
            }
        }
    }

    private boolean matchParam(ClanKillRallyMonsterData data) {
        String[] taskParam = getTaskParamById();
        String[] split = taskParam[1].split(Constants.BAN_JIAO_DOU_HAO);
        int level = Integer.parseInt(taskParam[2]);
        for (String buildTypeConfig : split) {
            int buildType = Integer.parseInt(buildTypeConfig);
            if (buildType == data.getType().getNumber() && (level == 0 || data.getLevel() >= level)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 满足积分
     */
    @Override
    public List<MileStoneClanInfoProp> filterMeetScore(List<MileStoneClanInfoProp> rankProp) {
        String[] taskParam = getTaskParamById();
        if (taskParam.length < 1) {
            LOGGER.error("ClanKillRallyMonsterMileStoneHandler param error. taskParam={} prop={}", taskParam, getProp());
        }
        int rankLimit = Integer.parseInt(taskParam[0]);
        return rankProp.stream().filter(it -> it.getScore() >= rankLimit).collect(Collectors.toList());
    }

    public static class ClanKillRallyMonsterData extends MileStoneTaskData {
        List<Long> killByClans;
        CommonEnum.MonsterCategory type;
        int level;

        public CommonEnum.MonsterCategory getType() {
            return type;
        }

        public int getLevel() {
            return level;
        }

        public ClanKillRallyMonsterData(List<Long> clans, CommonEnum.MonsterCategory type, int level, int value) {
            super(value);
            this.killByClans = clans;
            this.type = type;
            this.level = level;
        }

        public List<Long> getKillByClans() {
            return killByClans;
        }
    }

}

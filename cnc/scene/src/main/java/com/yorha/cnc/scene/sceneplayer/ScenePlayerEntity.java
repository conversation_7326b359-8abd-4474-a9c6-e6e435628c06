package com.yorha.cnc.scene.sceneplayer;

import com.yorha.cnc.mainScene.common.component.MainSceneBornMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.component.*;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.notification.NotificationTokenHelper;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.DungeonType;
import com.yorha.proto.CommonEnum.InnerCityBuildType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ScenePlayerEntity extends AbstractScenePlayerEntity {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerEntity.class);
    /**
     * 属性
     */
    private final ScenePlayerProp prop;
    private DungeonType dungeonType = DungeonType.DT_NONE;

    private final ScenePlayerPropComponent propComponent;
    private final ScenePlayerDbComponent dbComponent;

    private final ScenePlayerSearchComponent searchComponent;
    private final ScenePlayerQlogComponent qlogComponent;
    private final ScenePlayerPowerComponent powerComponent;
    private final ScenePlayerPositionMarkComponent positionMarkComponent;
    private final ScenePlayerLogisticMgrComponent logisticMgrComponent;
    private final ScenePlayerNotificationComponent ntfComponent;

    public ScenePlayerEntity(SceneEntity sceneEntity, long playerId, ScenePlayerProp prop, ScenePlayerBuilder builder) {
        super(sceneEntity, playerId, builder);
        this.prop = prop;
        propComponent = new ScenePlayerPropComponent(this);
        dbComponent = new ScenePlayerDbComponent(this);
        searchComponent = new ScenePlayerSearchComponent(this);
        qlogComponent = new ScenePlayerQlogComponent(this);
        powerComponent = new ScenePlayerPowerComponent(this);
        positionMarkComponent = new ScenePlayerPositionMarkComponent(this);
        logisticMgrComponent = new ScenePlayerLogisticMgrComponent(this);
        ntfComponent = new ScenePlayerNotificationComponent(this);
        initAllComponents();
    }

    @Override
    public void onLogin(IActorRef sessionRef) {
        bindSession(sessionRef);
        LOGGER.info("ScenePlayer onLogin {}", this);
        this.dungeonType = DungeonType.DT_NONE;
        if (getScene().isBigScene()) {
            // 发zoneEntity
            getScene().getBigScene().getZoneEntity().getPropComponent().onPlayerLogin(this);
        }
        getPropComponent().onLogin();
        getAoiObserverComponent().onPlayerLogin();
        getRallyComponent().onLogin();
        getPropComponent().refreshClanThings();
        getAdditionComponent().refreshAllAddition();
        getSoldierMgrComponent().printSnapshot();
    }

    @Override
    public void onLoginWithDungeon(IActorRef sessionRef, DungeonType type) {
        bindSession(sessionRef);
        if (getScene().isBigScene()) {
            getScene().getBigScene().getZoneEntity().getPropComponent().onPlayerLogin(this);
        }
        enterDungeon(type);
    }

    @Override
    public void onLogout() {
        LOGGER.info("ScenePlayer onLogout {}", this);
        this.dungeonType = DungeonType.DT_NONE;
        bindSession(null);
        getPropComponent().onPlayerLogout();
        getAoiObserverComponent().onPlayerLogout();
        getProp().setLogoutTime(SystemClock.now());
    }

    /**
     * 进入副本  设置成在副本中的状态 把aoi清掉
     */
    @Override
    public void enterDungeon(DungeonType type) {
        LOGGER.info("{} enterDungeon {}", this, type);
        this.dungeonType = type;
        // 清理下aoi
        getAoiObserverComponent().clearPlayerView();
    }

    @Override
    public void leaveDungeon() {
        if (!isInDungeon()) {
            return;
        }
        LOGGER.info("{} leaveDungeon", this);
        DungeonType old = this.dungeonType;
        this.dungeonType = DungeonType.DT_NONE;
        // 如果这个是不需要同步ScenePlayer的副本 那就发下
        if (!BigSceneConstants.needSyncScenePlayer(old)) {
            getPropComponent().onLogin();
        }
        // 发自己ob的  自己的行军、城
        getAoiObserverComponent().onPlayerLogin();
        getRallyComponent().onLogin();
    }

    public boolean isInDungeon() {
        return dungeonType != DungeonType.DT_NONE;
    }

    public DungeonType getDungeonType() {
        return dungeonType;
    }

    @Override
    public boolean isThisSceneOnline() {
        return super.isThisSceneOnline() && !isInDungeon();
    }

    public void beforeMigrate() {
        // 登出
        onLogout();
        // 结束战斗
        getMainCity().getBattleComponent().forceEndAllBattle();
        // 结束援助
        getMainCity().getInnerArmyComponent().returnAllArmy();
        // 清理所有部队
        getArmyMgrComponent().returnAllArmy();
        // 清理所有飞机
        getPlaneComponent().returnAllPlane("beforeMigrate");
        //取消对grid的占领状态
        MainSceneBornMgrComponent bornMgrComponent = getScene().getBornMgrComponent();
        if (bornMgrComponent != null) {
            bornMgrComponent.cancelOccupy(getMainCity().getCurPoint(), getMainCity());
        }
        // 不怎么重要的处理
        try {
            // 清理相关野怪
            ((MainSceneObjMgrComponent) getScene().getObjMgrComponent()).removeSkynetMonsterAboutMe(getEntityId());
        } catch (Exception e) {
            LOGGER.error("beforeMigrate failed {} ", this, e);
        }
    }

    public void deleteByMigrate() {
        if (getSceneClan() != null) {
            getSceneClan().getMemberComponent().onMemberLeaveMainScene(getEntityId());
        }
        // 停db
        getDbComponent().stopByMigrate();
        // 停prop listener
        PropertyChangeListener listener = getProp().getListener();
        if (listener != null) {
            ((CanStopPropertyChangeListener) listener).stop();
        }
        // 停定时器
        getScene().getTickMgrComponent().cancelSchedule(this, SceneTimerReason.TIMER_DEV_BUFF);
        NotificationTokenHelper.removePushNtfModel(this.getPlayerId());
        deleteObj();
    }


    @Override
    public boolean checkMonsterKillLevel(int monsterLevel) {
        return monsterLevel <= getProp().getKillMonsterMaxLevel() + 1;
    }

    @Override
    public String getClanName() {
        return getSceneClan() != null ? getSceneClan().getClanSimpleName() : "";
    }

    @Override
    public int getCityBuildLevel(InnerCityBuildType buildType) {
        CityBuildInfoProp cityBuildInfoProp = getProp().getCityBuildLevelMap().get(buildType.getNumber());
        if (cityBuildInfoProp == null) {
            return 0;
        }
        return cityBuildInfoProp.getLevel();
    }

    @Override
    public List<Integer> getUnlockSpyData() {
        return getProp().getTechModel().getUnlockSpyLevel();
    }

    @Override
    public boolean checkResourceUnlock(int type) {
        return getProp().getTechModel().getUnlockResource().contains(type);
    }

    public ScenePlayerProp getProp() {
        return prop;
    }

    /**
     * 获取场景上的联盟对象
     */
    @Override
    public SceneClanEntity getSceneClan() {
        if (!isInClan()) {
            return null;
        }
        return getScene().getClanMgrComponent().getSceneClanOrNull(getClanId());
    }

    @Override
    public long getClanId() {
        return getProp().getClanId();
    }

    @Override
    public Camp getCampEnum() {
        return getProp().getCamp();
    }

    @Override
    public PlayerCardHeadProp getCardHead() {
        return getProp().getCardHead();
    }

    @Override
    public Int32PlayerPickLimitMapProp getPickUpProp() {
        return getProp().getPickLimit();
    }

    @Override
    public WarningItemListProp getWarningProp() {
        return getProp().getWarning();
    }

    @Override
    public void setMainCityId(long cityId) {
        getProp().setMainCityId(cityId);
    }

    @Override
    public ScenePlayerCityComponent getCityComponent() {
        return (ScenePlayerCityComponent) super.getCityComponent();
    }

    @Override
    public ScenePlayerRallyComponent getRallyComponent() {
        return (ScenePlayerRallyComponent) super.getRallyComponent();
    }

    public ScenePlayerPropComponent getPropComponent() {
        return propComponent;
    }

    public ScenePlayerDbComponent getDbComponent() {
        return dbComponent;
    }

    @Override
    public ScenePlayerWallComponent getWallComponent() {
        return (ScenePlayerWallComponent) super.getWallComponent();
    }

    @Override
    public ScenePlayerSoldierComponent getSoldierMgrComponent() {
        return (ScenePlayerSoldierComponent) super.getSoldierMgrComponent();
    }

    @Override
    public ScenePlayerHospitalComponent getHospitalComponent() {
        return (ScenePlayerHospitalComponent) super.getHospitalComponent();
    }

    public ScenePlayerSearchComponent getSearchComponent() {
        return searchComponent;
    }

    public ScenePlayerPowerComponent getPowerComponent() {
        return powerComponent;
    }

    public ScenePlayerQlogComponent getQlogComponent() {
        return qlogComponent;
    }

    @Override
    public ScenePlayerPositionMarkComponent getPositionMarkComponent() {
        return positionMarkComponent;
    }

    public ScenePlayerLogisticMgrComponent getLogisticMgrComponent() {
        return logisticMgrComponent;
    }

    @Override
    public ScenePlayerArmyMgrComponent getArmyMgrComponent() {
        return (ScenePlayerArmyMgrComponent) super.getArmyMgrComponent();
    }

    @Override
    public ScenePlayerDevBuffComponent getDevBuffComponent() {
        return (ScenePlayerDevBuffComponent) super.getDevBuffComponent();
    }

    public ScenePlayerNotificationComponent getNtfComponent() {
        return ntfComponent;
    }

    @Override
    public void callAfterAllLoad() {
        getDevBuffComponent().afterAllLoad();
    }

    @Override
    public int getZoneId() {
        return getProp().getZoneModel().getZoneId();
    }

    @Override
    public ScenePlayerMonsterModelProp getMonsterProp() {
        return getProp().getMonsterModel();
    }
}

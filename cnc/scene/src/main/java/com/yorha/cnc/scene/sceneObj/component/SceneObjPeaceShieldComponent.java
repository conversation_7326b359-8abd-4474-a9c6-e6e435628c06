package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;

/**
 * sceneObj 和平护盾组件
 *
 * <AUTHOR>
 */
public class SceneObjPeaceShieldComponent extends SceneObjComponent<SceneObjEntity> {

    public SceneObjPeaceShieldComponent(SceneObjEntity owner) {
        super(owner);
    }

    public void onPeaceShieldOn(int shieldId, long shieldEndTime, boolean needNtfToPlayer) {
    }

    public void onPeaceShieldOff(int shieldId) {
    }

    public boolean canOpenPeaceShield() {
        return false;
    }

    public boolean isPeaceShieldOn() {
        return false;
    }

    public long getPeaceShieldEndTime() {
        return 0;
    }

    public void closePeaceShield() {
    }
}

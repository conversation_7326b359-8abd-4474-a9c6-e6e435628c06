package com.yorha.cnc.mainScene;

import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.proto.CommonEnum;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface IMainScene {
    SceneActor ownerActor();

    SceneClanEntity getSceneClanOrNull(long clanId);

    Collection<SceneClanEntity> getAllSceneClan();

    void openMapBuilding(CommonEnum.MapBuildingType type, int level, long endTsMs);

    void semiOpenMapBuilding(long endTsMs);

    MileStoneMgrComponent<?> getMileStoneOrNullComponent();
    
    MainSceneObjMgrComponent getObjMgrComponent();
}

package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

import java.util.*;

/**
 * 活动野怪：洛哈
 *
 * <AUTHOR>
 */
public class LuohaMonster implements MonsterFeature {
    private static final Logger LOGGER = LogManager.getLogger(LuohaMonster.class);

    @Override
    public void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity) {
        Point point = monsterEntity.getCurPoint();
        if (!sceneEntity.isMainScene()) {
            return;
        }
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) sceneEntity.getObjMgrComponent();
        objMgrComponent.updateActNum(MapGridDataManager.getRegionId(monsterEntity.getScene().getMapId(), point));
    }

    @Override
    public ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public void onDead(MonsterEntity monsterEntity) {

    }

    /**
     * 发送洛哈奖励
     * 活动野怪洛哈奖励分配  固定奖池随机奖励和经验
     */
    @Override
    public void sendKillAndRewardAll(MonsterEntity monsterEntity) {
        MonsterTemplate template = monsterEntity.getTemplate();
        Point curPoint = monsterEntity.getCurPoint();

        // 杀怪奖励
        Map<Long, Map<Long, Integer>> killRatio = monsterEntity.getBattleComponent().getKillRatioInfo();
        for (Map.Entry<Long, Map<Long, Integer>> rallyKillRatioMap : killRatio.entrySet()) {
            ArmyEntity army = monsterEntity.getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, rallyKillRatioMap.getKey());
            if (army == null) {
                continue;
            }
            if (!army.isRallyArmy()) {
                // 为啥普通的有伤害？？
                LOGGER.error("LuohaMonster sendKillAndRewardAll but {} is normal", army);
                continue;
            }
            final List<CommonMsg.MailReceiver> receivers = new LinkedList<>();
            // 发送奖励邮件
            int mailId = monsterEntity.getTemplate().getMailId();
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
            mailSendParams.setMailTemplateId(mailId)
                    .setIsOnlyForShow(true)
                    .getContentBuilder()
                    .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_LUOHA)
                    .getDisplayDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayPoint(curPoint, monsterEntity.getScene().getMapType(), monsterEntity.getScene().getMapIdForPoint()));
            Map<Long, List<ItemReward>> rewardRecord = new HashMap<>();
            for (Map.Entry<Long, Integer> entry : rallyKillRatioMap.getValue().entrySet()) {
                ArmyEntity subArmy = monsterEntity.getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, entry.getKey());
                if (subArmy == null) {
                    continue;
                }
                // 发送加经验 击杀记录
                int exp = SceneAddCalc.getFightMonsterExp(subArmy.getBattleComponent().getBattleRole(), template.getExp());
                monsterEntity.getRewardComponent().sendMonsterKillMsg(subArmy, exp);
                // 发送奖励
                if (template.getRewardIdList().size() <= 0) {
                    continue;
                }

                List<ItemReward> rewardList = new ArrayList<>();
                for (int rewardId : template.getRewardIdList()) {
                    rewardList.addAll(ResHolder.getResService(ItemResService.class).randomReward(rewardId));
                }

                if (CollectionUtils.isNotEmpty(rewardList)) {
                    // tell player 发奖
                    monsterEntity.getRewardComponent().sendRewardMsg(subArmy.getScenePlayer(), rewardList);
                    LOGGER.info("LuohaMonster sendKillAndRewardAll playerId:{} rewards:{}", subArmy.getPlayerId(), rewardList);

                    // 记录本玩家获得的奖励记录
                    long playerId = subArmy.getPlayerId();
                    rewardRecord.put(playerId, rewardList);
                    receivers.add(CommonMsg.MailReceiver.newBuilder().setPlayerId(playerId).setZoneId(subArmy.getScenePlayer().getZoneId()).build());
                }
            }

            if (mailId <= 0) {
                return;
            }
            for (Map.Entry<Long, List<ItemReward>> entry : rewardRecord.entrySet()) {
                AbstractScenePlayerEntity player = monsterEntity.getScene().getPlayerMgrComponent().getScenePlayer(entry.getKey());
                Struct.RewardRecord.Builder record = Struct.RewardRecord.newBuilder();
                record.setPlayerId(entry.getKey())
                        .setCardHead(player.getCardHead().getCopySsBuilder());
                for (ItemReward reward : entry.getValue()) {
                    Struct.ItemPair.Builder pairBuilder = Struct.ItemPair.newBuilder()
                            .setItemTemplateId(reward.getItemTemplateId()).setCount(reward.getCount());
                    record.getRewardsBuilder().addDatas(pairBuilder.build());
                }
                mailSendParams.getContentBuilder().getActivityRewardDataBuilder().getRewardRecordsBuilder().putDatas(player.getPlayerId(), record.build());
            }
            MailUtil.sendMailToPlayers(receivers, mailSendParams.build());
        }

    }
}

package com.yorha.cnc.scene.gm.command.map;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;

import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 获取目标点的州id
 */
public class QueryRegionPart implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int x = Integer.parseInt(args.get("x"));
        int y = Integer.parseInt(args.get("y"));
        int regionId = MapGridDataManager.getRegionId(actor.getScene().getMapId(), x * 1000, y * 1000);
        int partId = MapGridDataManager.getPartId(actor.getScene().getMapId(), x * 1000, y * 1000);
        int landformsType = MapGridDataManager.getLandformsType(actor.getScene().getMapId(), x * 1000, y * 1000);
        String ret = String.format("regionId: %d  partId: %d landformsType: %d", regionId, partId, landformsType);
        throw new GeminiException(ErrorCode.SYSTEM_WARNING, ret);
    }

    @Override
    public String showHelp() {
        return "QueryRegionPart x={value} y={value}";
    }
}

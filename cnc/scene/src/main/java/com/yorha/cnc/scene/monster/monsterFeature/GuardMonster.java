package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.proto.CommonEnum;
import res.template.MonsterTemplate;

/**
 * 守护者的特性
 *
 * <AUTHOR>
 */
public class GuardMonster implements MonsterFeature {
    @Override
    public CommonEnum.SceneObjectEnum getSceneObjType(MonsterTemplate template) {
        return CommonEnum.SceneObjectEnum.SOE_BUILDING_GUARD;
    }

    @Override
    public void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity) {
        final int partId = monsterEntity.getPartId();
        if (partId <= 0) {
            return;
        }
        if (!sceneEntity.isMainScene()) {
            return;
        }
        //更新城市守护者计数
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) sceneEntity.getObjMgrComponent();
        objMgrComponent.updateGuardianNum(partId, monsterEntity.getTemplateId());
    }

    @Override
    public ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public void onDead(MonsterEntity monsterEntity) {

    }

    @Override
    public void sendKillAndRewardAll(MonsterEntity monsterEntity) {
        monsterEntity.getRewardComponent().sendBaseReward();
    }
}

package com.yorha.cnc.scene.sceneObj.move;

import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;

import java.util.*;

/**
 * <AUTHOR>
 */
public class MoveData {
    /**
     * 移动路径
     */
    private List<Point> movePathList;
    private final List<Point> lastPathList;
    /**
     * 关卡路径的索引 -> 关卡的partId
     */
    private Map<Integer, Integer> crossingPart;
    /**
     * 到达每个路点的时间戳
     */
    private final List<Long> moveArriveTimeList = new ArrayList<>();
    /**
     * 路点个数
     */
    private int pointNum = 0;
    /**
     * 当前所在路端索引
     */
    private int curIndex = 0;
    /**
     * 寻路阻挡tag
     */
    private final int searchTag;
    /**
     * 当前速度  同行追击时为速度上限
     */
    private int moveSpeed;
    /**
     * 追击链路 用来判断并行追击是否继续  避免爆栈问题
     */
    private Set<Long> chaseIdSet;
    /**
     * 缓存的移动方向
     */
    private Double cacheMoveAngle = null;

    /**
     * 获取移动起始时间戳
     */
    public long getMoveStartTime() {
        return moveArriveTimeList.get(0);
    }

    public long getMoveEndTime() {
        return moveArriveTimeList.get(pointNum - 1);
    }

    public long getMoveCurTime() {
        return moveArriveTimeList.get(curIndex);
    }

    public Point getEndPoint() {
        return movePathList.get(pointNum - 1);
    }

    private Point getStartPoint() {
        return movePathList.get(0);
    }

    public List<Point> getMovePathList() {
        return movePathList;
    }

    public List<Long> getMoveArriveTimeList() {
        return moveArriveTimeList;
    }

    public int getSearchTag() {
        return searchTag;
    }

    public MoveData(List<Point> movePathList, int searchTag) {
        this(movePathList, null, null, searchTag);
    }

    public MoveData(List<Point> movePathList, Map<Integer, Integer> crossingPart, int searchTag) {
        this(movePathList, null, crossingPart, searchTag);
    }

    public MoveData(List<Point> movePathList, List<Point> lastPathList, Map<Integer, Integer> crossingPart, int searchTag) {
        this.movePathList = movePathList;
        this.lastPathList = lastPathList;
        this.searchTag = searchTag;
        this.crossingPart = crossingPart;
        pointNum = movePathList.size();
        if (pointNum < 2) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH.getCodeId(), String.format("%s", movePathList));
        }
    }

    /**
     * 修正最后一段路
     */
    public void correctTheLastPath(PathFindMgrComponent component, int fixDistance, Point endPoint) {
        // 预寻路时没有追击修正的 因为要等army创建出来才能得到修正距离
        if (lastPathList != null) {
            List<Point> list = component.correctPathByMoveTarget(lastPathList, fixDistance, endPoint);
            movePathList.addAll(list);
            lastPathList.clear();
        } else {
            movePathList = component.correctPathByMoveTarget(movePathList, fixDistance, endPoint);
        }
    }

    public void setChaseIdSet(Set<Long> chaseIdSet) {
        this.chaseIdSet = chaseIdSet;
    }

    public Set<Long> getChaseIdSet() {
        return chaseIdSet;
    }

    public boolean isParallelMoving() {
        return chaseIdSet != null;
    }

    public void setMoveSpeed(int moveSpeed) {
        this.moveSpeed = moveSpeed;
    }

    private void setCurIndex(int curIndex) {
        this.curIndex = curIndex;
        cacheMoveAngle = null;
    }

    public int getCurIndex() {
        return curIndex;
    }

    public Double getCacheMoveAngle() {
        if (cacheMoveAngle != null) {
            return cacheMoveAngle;
        }
        if (curIndex >= pointNum - 1) {
            return null;
        }
        cacheMoveAngle = movePathList.get(curIndex).calAngleWithPoint(movePathList.get(curIndex + 1));
        return cacheMoveAngle;
    }

    /**
     * 计算到达路点的时间
     *
     * @param movePathList       路点
     * @param moveArriveTimeList 到达的时间
     * @param moveStartTime      移动开始的时间
     * @param moveSpeed          移动速度
     */
    private void calcMoveArrivedTime(List<Point> movePathList, List<Long> moveArriveTimeList, long moveStartTime, int moveSpeed) {
        setCurIndex(0);
        cacheMoveAngle = null;
        float totalDistance = 0f;
        Point lastPoint = movePathList.get(0);
        for (int i = 1; i < movePathList.size(); i++) {
            Point curPoint = movePathList.get(i);
            totalDistance += Point.calDisBetweenTwoPoint(lastPoint, curPoint);
            long addCostTime = (long) (totalDistance / moveSpeed * 1000);
            moveArriveTimeList.add(addCostTime + moveStartTime);
            lastPoint = curPoint;
        }
    }

    /**
     * 是否到达终点
     */
    public boolean isArriveEnd(long curTime, Point point) {
        return curTime >= getMoveEndTime() || point.equals(getEndPoint());
    }

    public boolean isArriveEnd(long curTime) {
        return curTime >= getMoveEndTime();
    }

    /**
     * 开始移动
     *
     * @param moveStartTime 移动开始时间
     * @param obj           移动主体
     */
    public void startMove(long moveStartTime, SceneObjEntity obj) {
        if (lastPathList != null) {
            movePathList.addAll(lastPathList);
            lastPathList.clear();
        }
        pointNum = movePathList.size();
        int selfMoveSpeed = obj.getMoveComponent().getRealMoveSpeed(getSearchTag(), getEndPoint());
        // 同行追击有设定速度上限
        if (getChaseIdSet() != null && moveSpeed != 0) {
            selfMoveSpeed = Math.min(selfMoveSpeed, moveSpeed);
        } else {
            moveSpeed = selfMoveSpeed;
        }
        // 第一个点是起始点
        moveArriveTimeList.add(moveStartTime);
        this.calcMoveArrivedTime(this.movePathList, this.moveArriveTimeList, moveStartTime, selfMoveSpeed);
    }

    /**
     * 移动速度改变切割路点
     *
     * @param obj 移动主体
     * @return index from pathList, -1 if not in pathList
     */
    public boolean changeMoveSpeed(SceneObjEntity obj) {
        // 合围移动 不用管速度变化
        if (GameLogicConstants.isBesiegeMove(getSearchTag())) {
            return false;
        }
        int selfMoveSpeed = obj.getMoveComponent().getRealMoveSpeed(getSearchTag(), getEndPoint());
        // 同行追击有设定速度上限
        if (getChaseIdSet() != null) {
            selfMoveSpeed = Math.min(selfMoveSpeed, moveSpeed);
        } else {
            // 不是同行追击  那看下有没有变化 没变化就不重新算了
            if (moveSpeed == selfMoveSpeed) {
                return false;
            }
            moveSpeed = selfMoveSpeed;
        }
        long curTime = obj.getScene().now();
        Point p = calCurPoint(curTime);
        if (curIndex == pointNum - 1) {
            // 已经到达了
            return false;
        }
        int reCalcStartIndex = -1;
        Long t1 = moveArriveTimeList.get(curIndex);
        Long t2 = moveArriveTimeList.get(curIndex + 1);
        if (curTime < t1 && curIndex == 0) {
            // 还没出发阶段
            reCalcArrivedTime(getMoveStartTime(), 0, null, selfMoveSpeed);
            return true;
        }
        if (t1.equals(curTime)) {
            //后面的全重算
            reCalcStartIndex = curIndex;
            p = null;
        } else if (curTime == t2) {
            reCalcStartIndex = curIndex + 1;
            p = null;
        } else if (curTime > t1 && curTime < t2) {
            reCalcStartIndex = curIndex + 1;
        }
        if (reCalcStartIndex != -1) {
            reCalcArrivedTime(curTime, reCalcStartIndex, p, selfMoveSpeed);
            return true;
        }
        return false;
    }

    /**
     * 根据当前速度重新计算到达路点的时间
     *
     * @param curTime   当前时间
     * @param fromIndex 从第几个路点开始算
     * @param moveSpeed 移动速度
     */
    private void reCalcArrivedTime(long curTime, int fromIndex, Point startPoint, int moveSpeed) {
        moveArriveTimeList.clear();
        moveArriveTimeList.add(curTime);

        List<Point> newPointList = new ArrayList<>(this.movePathList.subList(fromIndex, this.movePathList.size()));
        if (startPoint != null) {
            newPointList.add(0, startPoint);
            fromIndex--;
        }
        if (crossingPart != null) {
            Map<Integer, Integer> newCrossingPart = new HashMap<>();
            for (int index : crossingPart.keySet()) {
                if (index < fromIndex) {
                    continue;
                }
                newCrossingPart.put(index - fromIndex, crossingPart.get(index));
            }
            crossingPart = newCrossingPart;
        }
        movePathList = newPointList;
        pointNum = movePathList.size();
        this.calcMoveArrivedTime(this.movePathList, this.moveArriveTimeList, curTime, moveSpeed);
    }

    /**
     * 计算获取当前点
     */
    public Point calCurPoint(long curTime) {
        if (isArriveEnd(curTime)) {
            setCurIndex(pointNum - 1);
            cacheMoveAngle = null;
            // 返回终点
            return getEndPoint();
        }
        if (curTime <= getMoveStartTime()) {
            return getStartPoint();
        }
        // 找到前往的下一个坐标点
        int nextMoveToPointIndex;
        for (nextMoveToPointIndex = curIndex; nextMoveToPointIndex < pointNum; nextMoveToPointIndex++) {
            if (moveArriveTimeList.get(nextMoveToPointIndex) > curTime) {
                break;
            }
        }
        Point lastPoint = movePathList.get(nextMoveToPointIndex - 1);
        Point nextPoint = movePathList.get(nextMoveToPointIndex);
        long lastArriveTime = moveArriveTimeList.get(nextMoveToPointIndex - 1);
        long nextArriveTime = moveArriveTimeList.get(nextMoveToPointIndex);
        setCurIndex(nextMoveToPointIndex - 1);
        cacheMoveAngle = null;
        // 如果是关隘那段  就返回起点，等真正过去的时候才改到关隘终点
        if (getCurCrossingPart() != 0) {
            return lastPoint;
        }
        float lerp = (curTime - lastArriveTime) * 1.0f / (nextArriveTime - lastArriveTime);
        return Point.lerpPoint(lastPoint, nextPoint, lerp);
    }

    /**
     * 如果当前路段是关卡中  获取该关卡的片id
     */
    public Integer getCurCrossingPart() {
        if (crossingPart == null) {
            return 0;
        }
        return crossingPart.getOrDefault(curIndex, 0);
    }

    public Map<Integer, Integer> getCrossingPart() {
        return crossingPart;
    }

    /**
     * 是否是需要通过关卡的路径
     */
    public boolean isCrossingPath() {
        return crossingPart != null && !crossingPart.isEmpty();
    }

    /**
     * 获取上个最近的移动点
     */
    public Point getRecentMovePoint(long now) {
        if (isArriveEnd(now)) {
            return movePathList.get(pointNum - 2);
        }
        for (int i = 0; i < moveArriveTimeList.size(); i++) {
            Long time = moveArriveTimeList.get(i);
            if (now < time && i > 0) {
                return movePathList.get(i - 1);
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "MoveData{" +
                "movePathList=" + movePathList +
                ", lastPathList=" + lastPathList +
                ", crossingPart=" + crossingPart +
                ", moveArriveTimeList=" + moveArriveTimeList +
                ", pointNum=" + pointNum +
                ", curIndex=" + curIndex +
                ", searchTag=" + searchTag +
                '}';
    }
}

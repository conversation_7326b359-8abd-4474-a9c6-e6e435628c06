package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjTransformComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.model.ModelGradientTemplateService;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Circle;

import java.util.HashSet;

/**
 * <AUTHOR>
 */
public class MonsterTransformComponent extends SceneObjTransformComponent {
    public MonsterTransformComponent(MonsterEntity owner, PointProp pointData) {
        super(owner, pointData);
        battleMeObj = new HashSet<>();
    }

    @Override
    public MonsterEntity getOwner() {
        return (MonsterEntity) super.getOwner();
    }

    @Override
    public void resetModelRadius() {
        MonsterEntity monsterEntity = getOwner();
        int newModelCircle = monsterEntity.getTemplate().getModelSize();
        if (newModelCircle == 0) {
            int liveSolider = monsterEntity.getBattleComponent().aliveCount();
            // 模型长度基数
            int soldierTypeSize = getSoldierTypeSize(monsterEntity.getProp().getTroop().getTroop().values());
            int modelBase = ResHolder.getResService(ModelGradientTemplateService.class).getNormalModelBase(liveSolider, soldierTypeSize);
            // 模型长度系数
            int range = ResHolder.getResService(ConstKVResService.class).getTemplate().getStandardModelDiameter();
            newModelCircle = modelBase * range;
        }
        setModelRadius(newModelCircle);
    }

    @Override
    public void addCollision() {
        int dynamicObstruct = getOwner().getTemplate().getDynamicObstruct();
        if (dynamicObstruct <= 0) {
            return;
        }
        Circle circle = Circle.valueOf(bornPoint.getX(), bornPoint.getY(), dynamicObstruct);
        getOwner().getScene().getPathFindMgrComponent().addDynamicCircleCollision(getEntityId(), circle);
    }

    @Override
    public int getCityMoveCollisionRadius() {
        return getOwner().getTemplate().getBlockRadius();
    }

    @Override
    public int getChaseDistance(SceneObjEntity target) {
        // 野怪可以有额外的普攻距离
        int dis = target.getTransformComponent().getModelRadius() +
                getModelRadius() +
                ResHolder.getResService(ConstKVResService.class).getTemplate().getStandardRange() +
                getOwner().getTemplate().getFarthestAttackDistance();
        return Math.max(0, dis);
    }
}

package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class OwnMapBuildingBatch implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        SceneClanEntity sceneClan = actor.getScenePlayer(playerId).getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        String[] split = args.get("partIdList").split(",");
        for (String str : split) {
            int partId = Integer.parseInt(str);
            MapBuildingEntity mapBuilding = actor.getScene().getBuildingMgrComponent().getMapBuilding(partId);
            if (mapBuilding.getTransformComponent().isDarkAltarCross()) {
                throw new GeminiException(ErrorCode.MAP_GRID_NOT_EXIST);
            }
            mapBuilding.getStageMgrComponent().gmSetOwner(sceneClan, playerId, sceneClan.getZoneId());
        }
    }

    @Override
    public String showHelp() {
        return "OwnMapBuildingBatch partIdList={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}

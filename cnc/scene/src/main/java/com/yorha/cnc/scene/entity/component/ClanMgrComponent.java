package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ClanFlagInfoProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.SsSceneClan;
import com.yorha.proto.SsSceneMap.FetchTerritoryMapAns;
import com.yorha.proto.StructPlayerPB.RallyInfoListPB;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 场景管理联盟映射的组件
 *
 * <AUTHOR>
 */
public class ClanMgrComponent extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(ClanMgrComponent.class);

    private final HashMap<Long, SceneClanEntity> sceneClanMap = new HashMap<>();

    private final HashMap<Integer, List<SceneClanEntity>> regionIdToSceneClanList = new HashMap<>();
    private final HashMap<Long, Integer> clanIdToRegionId = new HashMap<>();
    /**
     * 解散的联盟列表
     */
    private final Map<Integer, Long> dismissClan = Maps.newHashMap();
    /**
     * 只在开服用一次
     */
    private Map<Long, ClanProp> clanDb = new HashMap<>();
    /**
     * 只在开服用一次
     */
    private Map<Long, Pair<Integer, Long>> clanDbPower = new HashMap<>();
    /**
     * 联盟势力地图版本
     */
    private int mapVersion = 1;

    public ClanMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public int getMapVersion() {
        return mapVersion;
    }

    public int addMapVersion() {
        mapVersion++;
        return mapVersion;
    }

    /**
     * 世界准备好了 load+create都好了
     */
    public void onSceneOk() {
        for (SceneClanEntity sceneClan : sceneClanMap.values()) {
            ClanProp clanProp = clanDb.get(sceneClan.getClanId());
            // 原服恢复
            if (clanProp != null) {
                sceneClan.checkTerritoryData(clanProp.getTerritoryInfo().getPartNum(), clanProp.getTerritoryInfo().getTerritoryPower());
                // 直到此时，sceneClan, member 和 city 才都恢复完成，才可以去刷新sceneClan上的军团id
                addToRegionToClanMap(sceneClan);
            }
            // kvk恢复
            Pair<Integer, Long> pair = clanDbPower.get(sceneClan.getClanId());
            if (pair != null) {
                sceneClan.checkTerritoryData(pair.getFirst(), pair.getSecond());
            }
        }
        clanDb = null;
        clanDbPower = null;
    }

    public void addClanDbPowerWhenBigSceneInit(long clanId, ClanProp clanProp, Pair<Integer, Long> pair) {
        LOGGER.info("ClanMgrComponent addClanDbPowerWhenBigSceneInit {}", clanId);
        if (clanDb != null) {
            clanDb.put(clanId, clanProp);
        }
        if (pair != null) {
            clanDbPower.put(clanId, pair);
        }
    }

    public void onServerStop() {
        // 集结解散
        for (SceneClanEntity clan : sceneClanMap.values()) {
            clan.getRallyComponent().dismissAllRally(RallyDismissReason.RDR_BATTLE_END);
        }
    }

    /**
     * 获取当前新的地图势力数据
     */
    public FetchTerritoryMapAns buildTerritoryMap(int clientVersion) {
        FetchTerritoryMapAns.Builder ans = FetchTerritoryMapAns.newBuilder();
        ans.setVersion(mapVersion);
        if (clientVersion == mapVersion) {
            return ans.build();
        }
        for (SceneClanEntity sceneClan : sceneClanMap.values()) {
            sceneClan.getMapBuildingComponent().tryAddTerritoryMapItem(ans, clientVersion);
        }
        for (int dismissVersion : dismissClan.keySet()) {
            if (dismissVersion > clientVersion) {
                ans.addDelClanId(dismissClan.get(dismissVersion));
            }
        }
        return ans.build();
    }

    public void onKingdomColorSet(int zoneId) {
        for (SceneClanEntity sceneClan : sceneClanMap.values()) {
            if (sceneClan.getZoneId() != zoneId) {
                continue;
            }
            sceneClan.getMapBuildingComponent().onClanBaseChange(true);
        }
    }

    public void addSceneClan(SceneClanEntity sceneClanEntity) {
        LOGGER.info("try add {} into mgr", sceneClanEntity);
        long clanId = sceneClanEntity.getEntityId();
        if (sceneClanMap.containsKey(clanId)) {
            LOGGER.error("clanId:{} already in mgr", clanId);
        }
        sceneClanMap.put(clanId, sceneClanEntity);
        LOGGER.info("put {} into mgr, cur num:{}", sceneClanEntity, sceneClanMap.size());
        // 军团个数监控
        MonitorUnit.CLAN_ALIVE_NUM_GAUGE.labels(ServerContext.getBusId()).set(sceneClanMap.size());
    }

    public void removeSceneClan(long clanId) {
        LOGGER.info("try remove {} from mgr", clanId);
        SceneClanEntity remove = sceneClanMap.remove(clanId);
        if (remove == null) {
            LOGGER.error("clanId:{} not in mgr", clanId);
            return;
        }
        // 只有原服才需要维护regionMap
        removeFromRegionToClanMap(remove);
        LOGGER.info("remove clan:{} from mgr, cur num:{}", clanId, sceneClanMap.size());
        // 军团个数监控
        MonitorUnit.CLAN_ALIVE_NUM_GAUGE.labels(ServerContext.getBusId()).set(sceneClanMap.size());
        dismissClan.put(addMapVersion(), clanId);

        MileStoneMgrComponent mileStoneComponent = getOwner().getMileStoneOrNullComponent();
        if (mileStoneComponent != null) {
            mileStoneComponent.onClanDismiss(clanId);
        }
    }

    /**
     * 获取地图上的联盟实体
     */
    public SceneClanEntity getSceneClan(long clanId) {
        if (clanId <= 0) {
            throw new GeminiException("wrong clanId:{}", clanId);
        }
        SceneClanEntity sceneClanEntity = sceneClanMap.get(clanId);
        if (sceneClanEntity == null) {
            LOGGER.error("get clan fail, id:{} cur num:{}", clanId, sceneClanMap.size());
            return null;
        }
        return sceneClanEntity;
    }

    public SceneClanEntity getSceneClanOrNull(long clanId) {
        return sceneClanMap.get(clanId);
    }

    /**
     * 判断地图上的联盟实体是否存在
     */
    public boolean hasSceneClan(long clanId) {
        return sceneClanMap.containsKey(clanId);
    }

    /**
     * @return 获取所有联盟id
     */
    public Set<Long> getAllClanId() {
        return sceneClanMap.keySet();
    }

    /**
     * @return 获取所有活跃(scene认为内存中仍然存在)的联盟id
     */
    public Set<Long> getAllActiveClanId() {
        Set<Long> clanIds = Sets.newHashSet();
        for (SceneClanEntity sceneClanEntity : sceneClanMap.values()) {
            if (sceneClanEntity.isActive()) {
                clanIds.add(sceneClanEntity.getEntityId());
            }
        }
        return clanIds;
    }

    /**
     * 获取所有联盟的拥有的城市数量
     */
    public Map<Integer, Map<Long, Map<CommonEnum.MapBuildingType, Map<Integer, Integer>>>> getAllZoneClanOwnCityNum() {
        Map<Integer, Map<Long, Map<CommonEnum.MapBuildingType, Map<Integer, Integer>>>> ret = new HashMap<>();
        for (SceneClanEntity clan : sceneClanMap.values()) {
            Map<CommonEnum.MapBuildingType, Map<Integer, Integer>> cityType2Num = clan.getMapBuildingComponent().getMyCityType2Num();
            if (cityType2Num == null) {
                continue;
            }
            ret.computeIfAbsent(clan.getZoneId(), k -> new HashMap<>()).put(clan.getEntityId(), cityType2Num);
        }
        return ret;
    }

    /**
     * 玩家请求联盟战争页面
     */
    public RallyInfoListPB onPlayerQueryRallyInfoList(long playerId) {
        AbstractScenePlayerEntity player = getOwner().getPlayerMgrComponent().getScenePlayer(playerId);
        if (player == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        SceneClanEntity sceneClan = player.getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }
        return sceneClan.getRallyInfoList();
    }

    /**
     * 玩家请求指定的集结面板
     */
    public RallyInfoPB onPlayerQueryOneRally(long playerId, long rallyId) {
        AbstractScenePlayerEntity player = getOwner().getPlayerMgrComponent().getScenePlayer(playerId);
        if (player == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        SceneClanEntity sceneClan = player.getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }
        return sceneClan.getOneRallyInfo(rallyId);
    }

    public Collection<SceneClanEntity> getAllSceneClan() {
        return Collections.unmodifiableCollection(sceneClanMap.values());
    }

    public SceneClanEntity getClanEntity(long playerId) {
        AbstractScenePlayerEntity player = getOwner().getPlayerMgrComponent().getScenePlayer(playerId);
        if (player == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        SceneClanEntity sceneClan = player.getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
        }
        return sceneClan;
    }


    /**
     * 添加到 州 -> 军团的map中
     *
     * @param sceneClanEntity 场景上的军团实体
     */
    public void addToRegionToClanMap(SceneClanEntity sceneClanEntity) {
        // 根据军团长刷新下军团所在的州id信息
        // 重启或者新增军团都需要刷新监听
        sceneClanEntity.getMemberComponent().setClanRegionIdByOwner();
        int regionId = sceneClanEntity.getRegionId();

        // 填充到内存结构中
        if (!regionIdToSceneClanList.containsKey(regionId)) {
            regionIdToSceneClanList.put(regionId, Lists.newLinkedList());
        }
        regionIdToSceneClanList.get(regionId).add(sceneClanEntity);
        clanIdToRegionId.put(sceneClanEntity.getEntityId(), regionId);
    }

    /**
     * 从 州 -> 军团的map中移除
     *
     * @param sceneClanEntity 场景上的军团实体
     */
    public void removeFromRegionToClanMap(SceneClanEntity sceneClanEntity) {
        if (!clanIdToRegionId.containsKey(sceneClanEntity.getEntityId())) {
            LOGGER.error("clanIdToRegionId not contain {}", sceneClanEntity.getEntityId());
            return;
        }
        int regionId = clanIdToRegionId.get(sceneClanEntity.getEntityId());
        clanIdToRegionId.remove(sceneClanEntity.getEntityId());
        if (!regionIdToSceneClanList.containsKey(regionId)) {
            LOGGER.error("regionIdToSceneClanList not contain {}", regionId);
            return;
        }
        boolean isRemoved = regionIdToSceneClanList.get(regionId).remove(sceneClanEntity);
        if (!isRemoved) {
            LOGGER.error("regionIdToSceneClanList not contain scene clan {}", sceneClanEntity.getEntityId());
        }
    }

    /**
     * 推荐军团
     *
     * @param playerId              请求推荐的玩家id
     * @param betterNotRecomClanIds 最好不要推荐的军团id
     * @return 返回推荐的军团的id
     */
    public long recommendClan(long playerId, List<Long> betterNotRecomClanIds) {
        AbstractScenePlayerEntity ownerPlayerEntity = getOwner().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (ownerPlayerEntity == null) {
            LOGGER.info("recommendClan but player not exist {}", playerId);
            return 0;
        }
        int regionId = ownerPlayerEntity.getMainCity().getTransformComponent().getRegionId();
        long beginTsMs = System.currentTimeMillis();
        // 先在同州的军团中找一个
        if (regionIdToSceneClanList.get(regionId) != null) {
            Long sceneClanId = recommendFromSpecificRegion(playerId, regionId, betterNotRecomClanIds);
            if (sceneClanId != null) {
                return sceneClanId;
            }
        }
        LOGGER.info("cannot find clan in same region {}, already cost {}", regionId, System.currentTimeMillis() - beginTsMs);
        // 同州中军团如果都推荐过了，从其他州找一个
        for (int otherRegionId : regionIdToSceneClanList.keySet()) {
            if (otherRegionId == regionId) {
                continue;
            }
            Long sceneClanId = recommendFromSpecificRegion(playerId, otherRegionId, betterNotRecomClanIds);
            if (sceneClanId != null) {
                return sceneClanId;
            }
        }
        LOGGER.info("all clan have been recommended, already cost {}", System.currentTimeMillis() - beginTsMs);
        // 都找不到随机roll一个，不能保证玩家身上的clan依然存在
        Long sceneClanId = RandomUtils.randomSet(sceneClanMap.keySet());
        if (sceneClanId == null) {
            // 数据结构本身有问题，打log，返回0
            LOGGER.warn("random failed, playerId {}, betterNotRecomClanIds {}", playerId, betterNotRecomClanIds);
            return 0L;
        }
        sendRecommendClanNtfToPlayer(playerId, sceneClanMap.get(sceneClanId));
        return sceneClanId;
    }

    /**
     * @param playerId 玩家id
     * @return 根据玩家信息，生成并返回展示给玩家的默认军团列表
     */
    public SsSceneClan.FetchDefaultClanListAns fetchDefaultClanList(long playerId) {
        AbstractScenePlayerEntity ownerPlayerEntity = getOwner().getPlayerMgrComponent().getScenePlayer(playerId);
        int playerRegionId = ownerPlayerEntity.getMainCity().getTransformComponent().getRegionId();
        int maxClanListNum = ResHolder.getConsts(ConstClanTemplate.class).getClanListNum();

        Set<Long> retClanIdSet = new HashSet<>();
        List<Long> fixRecommendClanIdList = new LinkedList<>();
        List<Long> retClanIdList = new LinkedList<>();

        // 当全服军团达到指定数目时，才填充推荐位的军团id
        int fillFixLeastClanNum = ResHolder.getConsts(ConstClanTemplate.class).getClanFixRecommendLeast();
        if (sceneClanMap.size() >= fillFixLeastClanNum) {
            // 先填充固定推荐位的军团id
            fillFixRecommendClanIdList(playerRegionId, fixRecommendClanIdList, retClanIdSet);
        }
        // 再填充其他位置的军团id
        fillOtherDefaultClanList(playerRegionId, retClanIdList, retClanIdSet, maxClanListNum);
        // 组合固定推荐位和其他位置
        combineAndGetDefaultClanIdList(retClanIdList, fixRecommendClanIdList);

        // 把剩余数量的填充完
        int remainSize = maxClanListNum - retClanIdList.size();
        if (remainSize > 0) {
            List<Long> toRandomList = sceneClanMap.keySet().stream().filter(it -> !retClanIdList.contains(it)).collect(Collectors.toList());
            List<Long> rets = RandomUtils.randomList(toRandomList, remainSize);
            retClanIdList.addAll(rets);
        }

        return SsSceneClan.FetchDefaultClanListAns.newBuilder().addAllClanList(retClanIdList).build();
    }

    /**
     * 发送推荐军团的ntf给特定的player
     *
     * @param playerId        请求推荐的玩家id
     * @param sceneClanEntity 场景上的军团实体，用于获取数据
     */
    private void sendRecommendClanNtfToPlayer(long playerId, SceneClanEntity sceneClanEntity) {
        AbstractScenePlayerEntity needNtfPlayer = getOwner().getPlayerMgrComponent().getScenePlayer(playerId);
        AbstractScenePlayerEntity ownerPlayerEntity = getOwner().getPlayerMgrComponent().getScenePlayerOrNull(sceneClanEntity.getClanOwnerId());
        PlayerScene.Player_RecommendClan_NTF.Builder ntfBuilder = PlayerScene.Player_RecommendClan_NTF.newBuilder();
        ntfBuilder.setClanId(sceneClanEntity.getEntityId())
                .setClanSName(sceneClanEntity.getClanSimpleName())
                .setFlagColor(sceneClanEntity.getFlagColor())
                .setFlagShading(sceneClanEntity.getFlagShading())
                .setFlagSign(sceneClanEntity.getFlagSign())
                .setTerritoryColor(sceneClanEntity.getTerritoryColor())
                .setNationFlagId(sceneClanEntity.getNationFlagId());
        if (ownerPlayerEntity != null) {
            ntfBuilder.setOwnerCardHead(ownerPlayerEntity.getCardHead().getCopyCsBuilder());
            needNtfPlayer.sendMsgToClient(MsgType.PLAYER_RECOMMENDCLAN_NTF, ntfBuilder.build());
        } else {
            // 盟主可能不在当前场景，到名片上问
            CardHelper.queryPlayerCardHeadASync(ownerActor(), sceneClanEntity.getClanOwnerId(), (res) -> {
                ntfBuilder.getOwnerCardHeadBuilder().mergeFrom(res);
                needNtfPlayer.sendMsgToClient(MsgType.PLAYER_RECOMMENDCLAN_NTF, ntfBuilder.build());
            });
        }
    }

    /**
     * 从特定州推荐军团，如果未能成功推荐则返回null
     *
     * @param playerId               请求推荐的玩家id
     * @param regionId               州id
     * @param cannotRecommendClanIds 不能推荐的军团id
     * @return 如果成功推荐，则返回推荐的军团id；失败则返回null
     */
    private Long recommendFromSpecificRegion(long playerId, int regionId, List<Long> cannotRecommendClanIds) {
        List<SceneClanEntity> sceneClanEntityList = getSpecificRegionSortedClanList(regionId);
        for (SceneClanEntity sceneClanEntity : sceneClanEntityList) {
            // 不是所有人准入的忽略
            if (!sceneClanEntity.isEveryOneCanJoin()) {
                continue;
            }
            if (sceneClanEntity.getMemberComponent().getMemberSize() < ResHolder.getConsts(ConstClanTemplate.class).getRecommendClanMember()) {
                continue;
            }
            if (!cannotRecommendClanIds.contains(sceneClanEntity.getEntityId())) {
                sendRecommendClanNtfToPlayer(playerId, sceneClanEntity);
                return sceneClanEntity.getEntityId();
            }
        }
        return null;
    }

    /**
     * 填充固定推荐位上的军团id，最多填充到配置最大值，最少可能一个都没填充到
     *
     * @param playerRegionId         玩家所在的州id
     * @param fixRecommendClanIdList 固定推荐位上的军团id列表，上层传入
     * @param retClanIdSet           所有需要返回给玩家的军团id集合，去重用，上层传入
     */
    private void fillFixRecommendClanIdList(int playerRegionId, List<Long> fixRecommendClanIdList, Set<Long> retClanIdSet) {
        long beginTsMs = SystemClock.now();
        int fixRecommendMaxNum = ResHolder.getConsts(ConstClanTemplate.class).getClanFixRecommendLocs().size();
        // 先从玩家所在州找
        if (fillFixRecommendClanIdListImplAndCheckFull(playerRegionId, fixRecommendClanIdList, retClanIdSet, fixRecommendMaxNum)) {
            // 找满了就不再找了
            return;
        }
        LOGGER.info("cannot find enough everyone can join clan in region {}, already found {}, cost {}",
                playerRegionId, fixRecommendClanIdList.size(), SystemClock.now() - beginTsMs);
        // 同州中军团如果都推荐过了，从其他州找一个
        for (int otherRegionId : regionIdToSceneClanList.keySet()) {
            if (otherRegionId == playerRegionId) {
                continue;
            }
            if (fillFixRecommendClanIdListImplAndCheckFull(otherRegionId, fixRecommendClanIdList, retClanIdSet, fixRecommendMaxNum)) {
                // 在任何一个州找满了，就退出循环
                return;
            }
        }
        LOGGER.info("cannot find enough everyone can join clan in all region, already found {}, cost {}",
                fixRecommendClanIdList.size(), SystemClock.now() - beginTsMs);
    }

    /**
     * 填充固定推荐位上的军团id并且检查是否已经达到要求的最大值，如果达到要求的最大值，通知上层已经搜索完毕
     *
     * @param regionId               期望搜索的州id
     * @param fixRecommendClanIdList 已经检索到的固定推荐位上的军团id
     * @param retClanIdSet           所有要返回给玩家的军团id的集合
     * @param fixRecommendMaxNum     固定推荐位需要的军团个数
     * @return fixRecommendClanIdList的元素个数大于等于要求的最大值时就返回true，否则返回false
     */
    private boolean fillFixRecommendClanIdListImplAndCheckFull(int regionId, List<Long> fixRecommendClanIdList, Set<Long> retClanIdSet, int fixRecommendMaxNum) {
        if (isRecommendListSizeReachConfig(fixRecommendClanIdList, fixRecommendMaxNum)) {
            return true;
        }
        List<SceneClanEntity> sceneClanEntityList = getSpecificRegionSortedClanList(regionId);
        for (SceneClanEntity sceneClanEntity : sceneClanEntityList) {
            // 进入循环时检查是否已经达到要求值了
            if (isRecommendListSizeReachConfig(fixRecommendClanIdList, fixRecommendMaxNum)) {
                return true;
            }
            // 固定推荐位上的军团必须是所有人都可以加入的
            if (!sceneClanEntity.isEveryOneCanJoin()) {
                continue;
            }
            if (!retClanIdSet.contains(sceneClanEntity.getEntityId())) {
                fixRecommendClanIdList.add(sceneClanEntity.getEntityId());
                retClanIdSet.add(sceneClanEntity.getEntityId());
            } else {
                LOGGER.warn("get two same clanId {} when fill fix recommend clan id list", sceneClanEntity.getEntityId());
            }
        }
        return isRecommendListSizeReachConfig(fixRecommendClanIdList, fixRecommendMaxNum);
    }

    /**
     * 填充其他位置上的军团id，最多填充到配置最大值，最少可能一个都没填充到
     *
     * @param playerRegionId 玩家所在的州id
     * @param retClanIdList  要返回给玩家的军团id的列表，有顺序
     * @param retClanIdSet   所有需要返回给玩家的军团id集合，去重用，上层传入
     * @param targetNum      目标数目
     */
    private void fillOtherDefaultClanList(int playerRegionId, List<Long> retClanIdList, Set<Long> retClanIdSet, int targetNum) {
        long beginTsMs = SystemClock.now();
        // 先从玩家所在州找
        if (fillOtherDefaultClanIdListImplAndCheckFull(playerRegionId, retClanIdList, retClanIdSet, targetNum)) {
            // 找满了就不再找了
            return;
        }
        LOGGER.info("cannot find enough clan in region {}, already found {}, cost {}",
                playerRegionId, retClanIdSet.size(), SystemClock.now() - beginTsMs);
        // 同州中军团如果都推荐过了，从其他州找一个
        for (int otherRegionId : regionIdToSceneClanList.keySet()) {
            if (otherRegionId == playerRegionId) {
                continue;
            }
            if (fillOtherDefaultClanIdListImplAndCheckFull(otherRegionId, retClanIdList, retClanIdSet, targetNum)) {
                // 在任何一个州找满了，就退出循环
                return;
            }
        }
        LOGGER.info("cannot find enough clan in all region, already found {}, cost {}",
                retClanIdSet.size(), SystemClock.now() - beginTsMs);
    }

    /**
     * 填充其他默认推荐的军团id并且检查是否已经达到要求的最大值，如果达到要求的最大值，通知上层已经搜索完毕
     *
     * @param regionId      期望搜索的州id
     * @param retClanIdList 要返回给玩家的军团id的列表，有顺序
     * @param retClanIdSet  所有要返回给玩家的军团id的集合
     * @param maxNum        默认最大的军团id集合长度
     * @return retClanIdSet的元素个数大于等于要求的最大值时就返回true，否则返回false
     */
    private boolean fillOtherDefaultClanIdListImplAndCheckFull(int regionId, List<Long> retClanIdList, Set<Long> retClanIdSet, int maxNum) {
        if (isRetSetSizeReachConfig(retClanIdSet, maxNum)) {
            return true;
        }
        List<SceneClanEntity> sceneClanEntityList = getSpecificRegionSortedClanList(regionId);
        for (SceneClanEntity sceneClanEntity : sceneClanEntityList) {
            // 进入循环时检查是否已经达到要求值了
            if (isRetSetSizeReachConfig(retClanIdSet, maxNum)) {
                return true;
            }
            if (!retClanIdSet.contains(sceneClanEntity.getEntityId())) {
                retClanIdList.add(sceneClanEntity.getEntityId());
                retClanIdSet.add(sceneClanEntity.getEntityId());
            }
        }
        return isRetSetSizeReachConfig(retClanIdSet, maxNum);
    }


    /**
     * 组合并获取默认的军团id列表
     *
     * @param retClanIdList          所有需要返回给玩家的军团id列表，有顺序
     * @param fixRecommendClanIdList 固定推荐位的军团id列表
     */
    private void combineAndGetDefaultClanIdList(List<Long> retClanIdList, List<Long> fixRecommendClanIdList) {
        // 如果没找到固定推荐位上能放置的军团，直接返回
        if (fixRecommendClanIdList.isEmpty()) {
            return;
        }
        List<Integer> targetLocList = ResHolder.getConsts(ConstClanTemplate.class).getClanFixRecommendLocs();
        int index = 0;
        for (long fixRecomClanId : fixRecommendClanIdList) {
            int targetLoc = targetLocList.get(index) - 1;
            if (targetLoc >= retClanIdList.size()) {
                retClanIdList.add(fixRecomClanId);
            } else {
                retClanIdList.add(targetLoc, fixRecomClanId);
            }
            ++index;
        }
    }

    private boolean isRecommendListSizeReachConfig(List<Long> fixRecommendClanIdList, int fixRecommendMaxNum) {
        return fixRecommendClanIdList.size() >= fixRecommendMaxNum;
    }

    private boolean isRetSetSizeReachConfig(Set<Long> retClanIdSet, int maxNum) {
        return retClanIdSet.size() >= maxNum;
    }


    /**
     * 获取特定州排序的军团实体list
     * <p>
     * 排序规则：
     * 第一优先级：军团人数 不满 -> 满
     * 第二优先级：军团战力 高 -> 低
     * 第三优先级：军团人数 多 -> 少
     *
     * @param regionId 特定州id
     * @return 根据排序规则排序的军团实体list
     */
    private List<SceneClanEntity> getSpecificRegionSortedClanList(int regionId) {
        List<SceneClanEntity> sceneClanEntityList = regionIdToSceneClanList.get(regionId);
        if (null == sceneClanEntityList) {
            return new LinkedList<>();
        }
        // 排序，O(Nlog(N))
        sceneClanEntityList.sort(Comparator.comparing(SceneClanEntity::isClanFull)
                .thenComparing(SceneClanEntity::getPower, Comparator.reverseOrder())
                .thenComparing(SceneClanEntity::getMemberNum, Comparator.reverseOrder()));
        return sceneClanEntityList;
    }

    public void gmRandomAllotBuilding() {
        List<SceneClanEntity> clanList = new ArrayList<>();
        for (SceneClanEntity clan : sceneClanMap.values()) {
            if (clan.getMapBuildingComponent().isTerritoryNumFull(CommonEnum.MapAreaType.TERRITORY)) {
                continue;
            }
            clanList.add(clan);
        }

        List<MapBuildingEntity> objs = getOwner().getObjMgrComponent().getObjsByType(MapBuildingEntity.class);
        for (MapBuildingEntity obj : objs) {
            if (obj.getOccupyState() != CommonEnum.OccupyState.TOS_NEUTRAL) {
                continue;
            }
            SceneClanEntity clan = RandomUtils.randomList(clanList);
            obj.getStageMgrComponent().gmSetOwner(clan, null, clan.getZoneId());
            if (clan.getMapBuildingComponent().isTerritoryNumFull(CommonEnum.MapAreaType.TERRITORY)) {
                clanList.remove(clan);
                if (clanList.isEmpty()) {
                    return;
                }
            }
        }
    }

    /**
     * 设置联盟旗帜
     *
     * @param clanFlagInfoProp 联盟旗帜
     * @param player           所有者玩家
     */
    public void formClanFlag(ClanFlagInfoProp clanFlagInfoProp, AbstractScenePlayerEntity player) {
        final long clanId = player.getClanId();
        SceneClanEntity sceneClan = this.getSceneClanOrNull(clanId);
        if (sceneClan == null) {
            return;
        }
        sceneClan.copy2FlagInfo(clanFlagInfoProp);
    }
}

package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;

import java.util.Map;

/**
 * 打开某一对象的ai调试日志
 *
 * <AUTHOR>
 */
public class AiDebug implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        long entityId = Long.parseLong(args.get("entityId"));
        SceneObjEntity entity = actor.getScene().getObjMgrComponent().getSceneObjEntity(entityId);
        if (entity == null) {
            return;
        }
        if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
            boolean action = !((MonsterEntity) entity).getAiComponent().isDebugAble();
            ((MonsterEntity) entity).getAiComponent().setDebugAble(action);
            return;
        }
        if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
            boolean action = !((MapBuildingEntity) entity).getAiComponent().isDebugAble();
            ((MapBuildingEntity) entity).getAiComponent().setDebugAble(action);
            return;
        }
    }

    @Override
    public String showHelp() {
        return "AiDebug entityId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MONSTER;
    }
}

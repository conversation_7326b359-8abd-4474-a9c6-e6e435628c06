package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 重设下地图建筑状态
 */
public class ResetMapBuilding implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        long mapBuildingId = Long.parseLong(args.get("id"));
        MapBuildingEntity mapBuilding = actor.getScene().getObjMgrComponent().getSceneObjWithTypeWithException(MapBuildingEntity.class, mapBuildingId);
        mapBuilding.getStageMgrComponent().gmReset();
    }

    @Override
    public String showHelp() {
        return "ResetMapBuilding id={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAPBUILDING;
    }
}

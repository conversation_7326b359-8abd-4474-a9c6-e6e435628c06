package com.yorha.cnc.scene.event.dungeon;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class DungeonRallyEvent extends IEvent {
    private final SceneObjEntity sceneObj;

    public DungeonRallyEvent(SceneObjEntity sceneObj) {
        this.sceneObj = sceneObj;
    }

    public SceneObjEntity getSceneObj() {
        return sceneObj;
    }
}

package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.mainScene.common.component.MainSceneBornMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneGridAoiMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.city.CityFactory;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.event.army.PointChangeEvent;
import com.yorha.cnc.scene.event.city.CityAscendEvent;
import com.yorha.cnc.scene.event.city.CityMoveEvent;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.cnc.scene.sceneObj.component.aoi.SceneObjAoiNodeComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.zone.kindomSkil.KingdomSkill;
import com.yorha.common.aoiView.manager.AoiObserver;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CityKingdomSkillInfoProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.proto.CommonEnum.CityAscendReason;
import com.yorha.proto.CommonEnum.CityWallState;
import com.yorha.proto.CommonEnum.MoveCityType;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Core.Code;
import com.yorha.proto.PlayerScene.Player_MoveCityAction_NTF;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncMapActionCity;
import res.template.CityAscendTemplate;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CityTransformComponent extends BuildingTransformComponent {
    private static final Logger LOGGER = LogManager.getLogger(CityTransformComponent.class);

    private int regionId;

    public CityTransformComponent(CityEntity owner, PointProp pointData) {
        super(owner, owner, pointData);
    }

    public int getRegionId() {
        return regionId;
    }

    @Override
    public void init() {
        super.init();
        MainSceneBornMgrComponent bornMgrComponent = getOwner().getScene().getBornMgrComponent();
        if (getOwner().isPlayerCity() && bornMgrComponent != null) {
            // 设置当前州
            regionId = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), getCurPoint());
            bornMgrComponent.addCityNum(regionId);
            // 占用位置
            if (!isAscend()) {
                bornMgrComponent.occupy(getCurPoint(), getOwner());
            }
        }
        // 设置scenePlayer主城的创建
        AbstractScenePlayerEntity scenePlayer = getOwner().getScenePlayer();
        scenePlayer.getCityComponent().onNewMainCity(getOwner());
    }

    /**
     * 是否升天了
     */
    public boolean isAscend() {
        return getOwner().getProp().getAscendReason() != CityAscendReason.CAR_NONE;
    }

    /**
     * 定点迁城
     */
    public void moveCityFixed(int x, int y, MoveCityType moveCityType) {
        Code code = CityFactory.moveCityVerify(getOwner().getScene(), x, y, moveCityType, getOwner().getScenePlayer());
        if (!ErrorCode.isOK(code)) {
            throw new GeminiException(code.getId());
        }
        moveCity(x, y);
        LOGGER.info("{} moveCityFixed  x={} y={} moveCityType={}", getOwner(), x, y, moveCityType);
    }

    /**
     * 随机迁城
     */
    public Point moveCityRandom() {
        Point point = getRandomPoint();
        if (point == null) {
            throw new GeminiException(ErrorCode.CITY_MOVE_RANDOM_FAILED);
        }
        moveCity(point.getX(), point.getY());
        LOGGER.info("{} moveCityRandom  {}", getOwner(), point);
        return point;
    }

    public Point getRandomPoint() {
        if (getOwner().getScene().isMainScene()) {
            return getOwner().getScene().getBornMgrComponent().chooseCityRandomPoint(regionId, getOwner());
        }
        return null;
    }

    /**
     * 迁城具体逻辑
     */
    public void moveCity(int x, int y) {
        if (getOwner().getScene().isMainScene()) {
            checkKingdomSkill();
            moveCityInMainScene(x, y, getOwner().getScene());
            return;
        }
        // 改坐标
        changePoint(Point.valueOf(x, y));
        // change 阻挡
        changeCollision();
        getOwner().getEventDispatcher().dispatch(new CityMoveEvent(getEntityId()));
        // 通知其他模块
        onCityMove();
    }

    private void moveCityInMainScene(int x, int y, SceneEntity scene) {
        // 先发迁城特效
        moveCityActionNotify(x, y);
        Point curPoint = getCurPoint();
        MainSceneBornMgrComponent bornMgrComponent = scene.getBornMgrComponent();
        // 取消占地
        bornMgrComponent.cancelOccupy(getCurPoint(), getOwner());
        String oldPoint = curPoint.getX() + "," + curPoint.getY();
        int oldContinent = MapGridDataManager.getRegionId(scene.getMapId(), curPoint);
        // 改坐标
        changePoint(Point.valueOf(x, y));
        // 占地
        bornMgrComponent.occupy(getCurPoint(), getOwner());
        // 联盟堡垒
        getOwner().getSpecialSafeGuardComponent().checkAndRefreshFortressGuard();
        // change 阻挡
        changeCollision();
        getOwner().getEventDispatcher().dispatch(new CityMoveEvent(getEntityId()));
        // 通知其他模块
        onCityMove();
        // 清除搜索缓存
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) getOwner().getScenePlayer();
        scenePlayer.getSearchComponent().clearSearchCache();
        // 上报
        int newContinent = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), x, y);
        if (newContinent != oldContinent) {
            bornMgrComponent.changeCityNum(oldContinent, newContinent);
        }
        QlogCncMapActionCity flow = new QlogCncMapActionCity();
        flow.fillHead(scenePlayer.getQlogComponent());
        flow.setDtEventTime(TimeUtils.now2String())
                .setAction("move_city")
                .setSubAction("")
                .setBeforeCoordinate(oldPoint)
                .setAfterCoordinate(getCurPoint().getX() + "," + getCurPoint().getY())
                .setBeforeContinent(String.valueOf(oldContinent))
                .setAfterContinent(String.valueOf(newContinent));
        flow.sendToQlog();
    }

    private void onCityMove() {
        try {
            getOwner().getScenePlayer().getPlaneComponent().onCityMove();
            getOwner().getScenePlayer().getArmyMgrComponent().onCityMove();
            getOwner().getInnerArmyComponent().onSelfPointChange();
        } catch (Exception e) {
            LOGGER.error("onCityMove failed {}", getOwner(), e);
        }
    }

    /**
     * 检查王国技能
     */
    public ErrorCode checkKingdomSkill() {
        CityKingdomSkillInfoProp skillInfoProp = this.getOwner().getProp().getCityKingdomModel().getSkillMapV(KingdomSkill.LIMIT_MOVE_CITY.getSkillId());

        if (skillInfoProp == null) {
            return ErrorCode.OK;
        }
        // 囚笼技能生效
        if (TimeUtils.isAfterNow(skillInfoProp.getSkillEndTsMs())) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_CAN_NOT_MOVE_CITY);
        }
        return ErrorCode.OK;
    }

    /**
     * 迁城通知  做客户端表现
     */
    private void moveCityActionNotify(int x, int y) {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        // 自己所在格的观察者
        SceneObjAoiNodeComponent aoiNodeComponent = (SceneObjAoiNodeComponent) getOwner().getAoiNodeComponent();
        Set<AoiObserver> obPlayerList = aoiNodeComponent.getObPlayerIdList(BigSceneConstants.WORLD_NORMAL_MIN_LAYER);
        // 新地点所在格的观察者
        Circle circle = Circle.valueOf(x, y, getOwner().getBuildingTemplate().getPathRadius());
        Set<AoiObserver> aoiObserverSet = ((MainSceneGridAoiMgrComponent) getOwner().getScene().getAoiMgrComponent()).getAoiObserverSet(circle, BigSceneConstants.WORLD_NORMAL_MIN_LAYER);
        obPlayerList.addAll(aoiObserverSet);
        if (obPlayerList.isEmpty()) {
            return;
        }
        Player_MoveCityAction_NTF.Builder builder = Player_MoveCityAction_NTF.newBuilder().setCityId(getEntityId());
        getOwner().getScene().getAoiMgrComponent().broadcastAoiObserverList(
                obPlayerList,
                MsgType.PLAYER_MOVECITYACTIONNTF_NTF,
                builder.build()
        );
    }

    /**
     * 检测是否需要升天 需要的话直接升天
     */
    public boolean cityCheckAscend(long curTime, long logoutTime) {
        if (isAscend()) {
            return false;
        }
        long ascendTime = getCityAscendMs();
        return curTime - logoutTime > ascendTime;
    }

    /**
     * 获取升天毫秒数
     */
    private long getCityAscendMs() {
        CityAscendTemplate template = ResHolder.getInstance().findValueFromMap(CityAscendTemplate.class, getOwner().getProp().getLevel());
        if (template == null) {
            // 保底
            return GameLogicConstants.CITY_ASCEND_TIME;
        } else {
            if (ServerContext.isZoneServer()) {
                return TimeUnit.HOURS.toMillis(template.getHours());
            } else {
                LOGGER.error("getCityAscendMs but not in zone or kvk player={} city={}", getOwner().getPlayerId(), getOwner());
                return Long.MAX_VALUE;
            }
        }
    }

    /**
     * 城池升天 不可见
     */
    public void cityAscend(CityAscendReason reason) {
        //已升天，无需重复升天
        if (isAscend()) {
            return;
        }
        getOwner().getProp().setAscendReason(reason);
        // 设置下城墙状态
        getOwner().getProp().setWallState(CityWallState.CS_NORMAL);
        // 清理所有部队
        getOwner().getScenePlayer().getArmyMgrComponent().returnAllArmy();
        // 清理所有侦察机
        getOwner().getScenePlayer().getPlaneComponent().returnAllPlane("cityAscend");
        // remove 阻挡
        PathFindMgrComponent pathFindMgrComponent = getOwner().getScene().getPathFindMgrComponent();
        pathFindMgrComponent.removeDynamicCircleCollision(getEntityId());
        // 结束战场
        getOwner().getBattleComponent().forceEndAllBattle();
        getOwner().getEventDispatcher().dispatch(new CityAscendEvent(getEntityId()));

        LOGGER.info("cityAscend player:{} city:{} reason:{}", getOwner().getPlayerId(), getEntityId(), reason);
        //移除视野
        getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_AOI);
        if (getOwner().getScene().isMainScene()) {
            Point point = getCurPoint();
            //取消对grid的占领状态
            MainSceneBornMgrComponent bornMgrComponent = getOwner().getScene().getBornMgrComponent();
            if (bornMgrComponent != null) {
                bornMgrComponent.cancelOccupy(point, getOwner());
            }
            // 通知下联盟
            SceneClanEntity sceneClan = getOwner().getScenePlayer().getSceneClan();
            if (sceneClan != null) {
                sceneClan.getMemberComponent().onMemberCityChange();
                // 删除护盾
                getOwner().getPeaceShieldComponent().closePeaceShield();
            }
            QlogCncMapActionCity flow = new QlogCncMapActionCity();
            flow.fillHead(((ScenePlayerEntity) getOwner().getScenePlayer()).getQlogComponent());
            flow.setDtEventTime(TimeUtils.now2String())
                    .setAction("city_disappeared")
                    .setSubAction(reason.toString())
                    .setBeforeCoordinate(point.getX() + "," + point.getY())
                    .setAfterCoordinate("")
                    .setBeforeContinent(String.valueOf(MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), point)))
                    .setAfterContinent("");
            flow.sendToQlog();
        }
    }

    /**
     * 重新落下
     */
    public Point cityFall() {
        if (!isAscend()) {
            return null;
        }
        Point point = getRandomPoint();
        if (point == null) {
            throw new GeminiException(ErrorCode.CITY_MOVE_RANDOM_FAILED);
        }
        changePoint(point);
        MainSceneBornMgrComponent bornMgrComponent = getOwner().getScene().getBornMgrComponent();
        if (bornMgrComponent != null) {
            bornMgrComponent.occupy(getCurPoint(), getOwner());
            tryUpdatePeaceShield();
        }
        getOwner().getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_BORN);
        getOwner().getProp().setAscendReason(CityAscendReason.CAR_NONE);
        QlogCncMapActionCity flow = new QlogCncMapActionCity();
        flow.fillHead(((ScenePlayerEntity) getOwner().getScenePlayer()).getQlogComponent());
        flow.setDtEventTime(TimeUtils.now2String())
                .setAction("rebuild")
                .setSubAction("")
                .setBeforeCoordinate("rebuild")
                .setAfterCoordinate(getCurPoint().getX() + "," + getCurPoint().getY())
                .setBeforeContinent("")
                .setAfterContinent(String.valueOf(MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), point)));
        flow.sendToQlog();
        // 通知玩家落堡
        getOwner().getScenePlayer().tellPlayer(SsPlayerMisc.OnCityFallCmd.getDefaultInstance());
        LOGGER.info("cityFall: {} {} point: {}", getOwner().getPlayerId(), getEntityId(), point);
        return point;
    }

    /**
     * 更新主堡护盾
     */
    private void tryUpdatePeaceShield() {
        getOwner().getSpecialSafeGuardComponent().checkAndRefreshFortressGuard();
        getOwner().getSpecialSafeGuardComponent().updateShieldForLandProtectionActivity();
    }

    @Override
    public void changePoint(Point newPoint, boolean isSync) {
        super.changePoint(newPoint, isSync);
        SceneClanEntity sceneClan = getOwner().getScenePlayer().getSceneClan();
        if (sceneClan != null) {
            sceneClan.getMemberComponent().onMemberCityChange();
        }
        if (getOwner().getScene().isMainScene()) {
            regionId = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), newPoint);
        }
        getOwner().getEventDispatcher().dispatch(new PointChangeEvent(getCurPoint()));
    }

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }
}

package com.yorha.cnc.scene.sceneObj.ai.stateMachine.impl;

import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.AbstractStateDiagram;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.StateTransitionConfig;
import com.yorha.proto.CommonEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 兵线状态图
 * <AUTHOR>
 */
public class SoldierLineStateDiagram extends AbstractStateDiagram {
    private static final Map<CommonEnum.AIStateType, List<StateTransitionConfig>> STATE_MAP = new HashMap<>();


    static {
        addTransition(StateTransitionConfig.newBuilder().event(AIEvent.FIND_ENEMY).sourceState(CommonEnum.AIStateType.STAY).targetState(CommonEnum.AIStateType.CHASE).build(), STATE_MAP);
        addTransition(StateTransitionConfig.newBuilder().event(AIEvent.LOSE_TARGET).sourceState(CommonEnum.AIStateType.CHASE).targetState(CommonEnum.AIStateType.LEAVE_BATTLE).build(), STATE_MAP);
        addTransition(StateTransitionConfig.newBuilder().event(AIEvent.FIND_ENEMY).sourceState(CommonEnum.AIStateType.LEAVE_BATTLE).targetState(CommonEnum.AIStateType.CHASE).build(), STATE_MAP);
        addTransition(StateTransitionConfig.newBuilder().event(AIEvent.COME_BACK).sourceState(CommonEnum.AIStateType.LEAVE_BATTLE).targetState(CommonEnum.AIStateType.STAY).build(), STATE_MAP);
    }

    @Override
    public Map<CommonEnum.AIStateType, List<StateTransitionConfig>> getStateMap() {
        return STATE_MAP;
    }

    @Override
    public CommonEnum.AIStateType getInitState() {
        return CommonEnum.AIStateType.STAY;
    }
}

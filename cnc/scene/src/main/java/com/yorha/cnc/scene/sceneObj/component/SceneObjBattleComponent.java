package com.yorha.cnc.scene.sceneObj.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.yorha.cnc.battle.adapter.interfaces.IBattleAdditionAdapter;
import com.yorha.cnc.battle.adapter.interfaces.IBattleBuffAdapter;
import com.yorha.cnc.battle.adapter.interfaces.IBattleMoveAdapter;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.cnc.battle.context.BattleRecordMailSnapshot;
import com.yorha.cnc.battle.context.BattleRoleContext;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.*;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.skill.SkillResult;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.battle.soldier.SoldierUnit;
import com.yorha.cnc.battle.unit.BattleHelper;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerHospitalComponent;
import com.yorha.cnc.scene.abstractsceneplayer.soldier.HospitalSoldierHandleResult;
import com.yorha.cnc.scene.areaSkill.AreaSkillFactory;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.battle.SceneBattleAdditionAdapter;
import com.yorha.cnc.scene.battle.SceneBattleBuffAdapter;
import com.yorha.cnc.scene.battle.SceneBattleMoveAdapter;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.ScenePlayerMgrComponent;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.battle.*;
import com.yorha.cnc.scene.event.player.ClanChangeEventFirst;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.constant.BattleConstants.BattleRelationStatus;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.battle.BattleAttackType;
import com.yorha.common.enums.qlog.battle.BattleRepResult;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.io.MsgType;
import com.yorha.common.qlog.json.Army.ArmyConfig;
import com.yorha.common.qlog.json.hero.HeroLevelStar;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.PlayerScene.Player_OrdinaryAttack_NTF;
import com.yorha.proto.PlayerScene.Player_SkillFire_NTF;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import qlog.flow.QlogCncBattleReport;
import res.template.*;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * 战斗组件
 *
 * <AUTHOR> Jiang
 */
public abstract class SceneObjBattleComponent extends SceneObjComponent<SceneObjEntity> implements IBattleRoleAdapter {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjBattleComponent.class);

    /**
     * 战斗角色，实际执行战斗运算的单位
     */
    private final BattleRole battleRole;
    /**
     * 战斗中，加入过集结、援助防卫战斗 的Entity集合，用于发战报（因为中途有可能有部队退出，所以不能直接用发战报时的prop数据）
     */
    private final Map<Long, SimpleBattleEntityInfo> onceInBattleEntityCache = Maps.newHashMap();

    protected IBattleAdditionAdapter additionAdapter;
    protected IBattleBuffAdapter buffAdapter;
    protected IBattleMoveAdapter moveAdapter;

    public SceneObjBattleComponent(SceneObjEntity owner, CommonEnum.SceneObjType sceneObjType, BattleRole masterRole) {
        super(owner);
        initBattleHandler();
        this.battleRole = new BattleRole(this.getOwner().getScene().getBattleGroundComponent().getBattleGround(), this, sceneObjType, getEntityId(), masterRole);
    }

    protected static Map<Long, List<Struct.PlayerHospitalSoldier>> buildSevereSoldierData(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        Map<Long, List<Struct.PlayerHospitalSoldier>> result = Maps.newHashMap();
        for (Map.Entry<Long, Map<Integer, SoldierLossData>> entry : lossDetail.entrySet()) {
            for (Map.Entry<Integer, SoldierLossData> entry1 : entry.getValue().entrySet()) {
                if (entry1.getValue().getSevere() > 0) {
                    Struct.PlayerHospitalSoldier.Builder builder = Struct.PlayerHospitalSoldier.newBuilder()
                            .setSoldierId(entry1.getKey())
                            .setSevereNum(entry1.getValue().getSevere());
                    result.computeIfAbsent(entry.getKey(), v -> Lists.newArrayList()).add(builder.build());
                }
            }
        }
        return result;
    }

    protected void initBattleHandler() {
        if (getOwner().getAdditionComponent() != null) {
            this.additionAdapter = new SceneBattleAdditionAdapter(getOwner().getAdditionComponent());
        }
        if (getOwner().getBuffComponent() != null) {
            this.buffAdapter = new SceneBattleBuffAdapter(getOwner().getBuffComponent());
        }
        if (getOwner().getMoveComponent() != null) {
            this.moveAdapter = new SceneBattleMoveAdapter(getOwner().getMoveComponent());
        }
    }

    @Override
    public void init() {
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onClanChange, ClanChangeEventFirst.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSkillTrigger, SkillTriggerEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleRoundEvent, BuffEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleRoundEvent, FireSkillEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleRoundEvent, GarrisonChangeEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleRoundEvent, TroopEvent.class);
    }

    public boolean isInPk(long targetId) {
        return getBattleRole().hasRelationWith(targetId);
    }

    public boolean hasRunningRelation() {
        return getBattleRole().hasRunningRelation();
    }

    public boolean hasRelationWithRoleId(long roleId) {
        return getBattleRole().hasRelationWithRoleId(roleId);
    }

    @Override
    public CommonEnum.SceneObjType getType() {
        return getBattleRole().getType();
    }

    @Override
    public int getZoneId() {
        return getOwner().getZoneId();
    }

    public BattleRole getBattleRole() {
        return battleRole;
    }

    @Override
    public boolean canReady() {
        // 建筑类型 或 部队非空才能打
        return getOwner() instanceof BuildingEntityType || !getBattleRole().isSoldierEmpty();
    }

    public boolean isInBattle() {
        return getBattleProp().getBattleState() == CommonEnum.BattleState.BS_DOING;
    }

    /**
     * 战斗数据预构建
     */
    @Override
    public boolean ready(IBattleRoleAdapter other) {
        //如果正在战斗中的,则不需要在进行准备处理,用来夹击
        if (isInBattle()) {
            return true;
        }
        if (getBattleRole().isSoldierEmpty() && !getBattleRole().isGuardTowerAlive()) {
            // 如果是city 没有兵力也要战斗
            // 客户端不显示英雄，所以清下hero(临时处理) 后续战报要显示的
            // 没有兵力  不会进行伤害结算，但是要显示一次普攻特效
            if (getOwner().getEntityType() == EntityType.ET_City || getOwner().getEntityType() == EntityType.ET_MapBuilding) {
                sendOrdinaryAttack(getEntityId(), 0, 0);
                getBattleProp().setBattleState(CommonEnum.BattleState.BS_DOING);
                return true;
            } else {
                LOGGER.warn("army soldier is 0,army={} num=0", this.getOwner());
                return false;
            }
        }
        getBattleProp().setBattleState(CommonEnum.BattleState.BS_DOING);
        onEnterBattleState(other);
        return true;
    }

    /**
     * 进入战斗状态
     * 此时还未添加BattleField，所以无法拿到对方
     */
    protected void onEnterBattleState(IBattleRoleAdapter other) {
        getOwner().getEventDispatcher().dispatch(new EnterNewBattle(other.getRoleId(), other.getEntityType(), other.getType()));
    }

    /**
     * 添加至战场中
     */
    @Override
    public void onAddBattleRelation(BattleRelation battleRelation) {
    }

    public boolean canIBattle() {
        return !getBattleRole().getStateHandler().isinState(BattleConstants.BattleRoleState.FORBIDDEN);
    }

    /**
     * 【我】主动发起对【target】的战斗(会标记我的targetId)
     * <p>
     * 用(MyObjType, TargetObjType)二元组默认的BattleType开启战斗关系
     */
    public boolean tryStartBattleWith(SceneObjEntity target) {
        return getOwner().getScene().getBattleGroundComponent().tryStartBattleWith(getOwner(), target);
    }

    /**
     * 释放技能而开战
     */
    public void tryStartBattleBySkill(SceneObjEntity target) {
        getOwner().getScene().getBattleGroundComponent().tryStartBattleBySkill(getOwner(), target);
    }

    @Override
    public ErrorCode canBattleWithCode(BattleRole targetRole, boolean needCheckSiegeLimit) {
        SceneObjEntity target = getObjEntityByBattleRole(targetRole.getRoleId());
        if (target == null) {
            LOGGER.error("canBattle failed. targetRole:{} not found.", targetRole);
            return ErrorCode.SYSTEM_TARGET_NULL;
        }
        return getOwner().canBattleWithCode(target, needCheckSiegeLimit);
    }

    @Override
    public boolean campAllowBattle(BattleRole other) {
        SceneObjEntity target = getObjEntityByBattleRole(other.getRoleId());
        if (target == null) {
            LOGGER.error("campAllowBattle failed. other:{} not found.", other);
            return false;
        }
        return getOwner().campAllowBattle(target);
    }

    @Override
    public void sendOrdinaryAttack(DamageResult damageResult) {
        sendOrdinaryAttack(getEntityId(), damageResult.getOrdinaryAttackNum(), damageResult.getShieldValue());
    }

    private void sendOrdinaryAttack(long targetId, int value, int shieldValue) {
        if (value == 0 && shieldValue == 0) {
            return;
        }
        Player_OrdinaryAttack_NTF.Builder message = Player_OrdinaryAttack_NTF.newBuilder();
        message.setTargetId(targetId);
        message.setValue(value);
        message.setShieldValue(shieldValue);
        // 一些特殊单位需要必定显示，包成特殊协议下发，客户端仅对普通协议裁剪
        if (isMustNtfType()) {
            PlayerScene.Player_SpecialOrdinaryAttack_NTF.Builder specialMessage = PlayerScene.Player_SpecialOrdinaryAttack_NTF.newBuilder().setOrdinary(message);
            getOwner().getAoiNodeComponent().broadcast(MsgType.PLAYER_SPECIALORDINARYATTACK_NTF, specialMessage.build(), BigSceneConstants.WORLD_NORMAL_MIN_LAYER);
        } else {
            getOwner().getAoiNodeComponent().broadcast(MsgType.PLAYER_ORDINARYATTACK_NTF, message.build(), BigSceneConstants.WORLD_NORMAL_MIN_LAYER);
        }
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        getOwner().getEventDispatcher().dispatch(new EndAllBattleEvent(battleResult.alive));
    }

    @Override
    public void afterEndAllRelation() {
        // 发完战报了  清理下
        onceInBattleEntityCache.clear();
    }

    protected void batchSendRecordMail(Set<Long> playerIds, BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
        if (playerIds.isEmpty()) {
            LOGGER.error("role:{} batchSendRecordMail failed playerIds is empty!", getBattleRole());
            return;
        }

        if (recordAllProp.getSingleRecordList().isEmpty()) {
            LOGGER.error("role:{} batchSendRecordMail failed recordAllProp is Empty!", getBattleRole());
            return;
        }

        StructMail.MailSendParams mailSendParams = buildNewMailParam(recordAllProp, alive, anyEnemyAlive);
        List<CommonMsg.MailReceiver> receivers = new ArrayList<>(playerIds.size());
        for (final Long playerId : playerIds) {
            AbstractScenePlayerEntity player = this.getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            receivers.add(CommonMsg.MailReceiver.newBuilder()
                    .setPlayerId(playerId)
                    .setZoneId(player.getZoneId())
                    .build());
        }
        //   TODO (kvk接入)
        MailUtil.sendMailToPlayers(receivers, mailSendParams);

        sendBattleRecordQLog(playerIds, recordAllProp, alive, anyEnemyAlive);
    }

    private StructMail.MailSendParams buildNewMailParam(BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
        final BattleRecordRoleProp enemyRole = getSelfOrEnemyRole(recordAllProp, true);
        final int mailId;
        if (BattleRecord.isMonster(enemyRole.getRoleType())) {
            mailId = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getMonsterBattleReportMailId();
        } else {
            mailId = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getBattleReportMailId();
        }
        StructMail.MailSendParams.Builder mailSendParamsBuilder = StructMail.MailSendParams.newBuilder().setMailTemplateId(mailId);
        mailSendParamsBuilder.setTitle(getMailTitle(alive, anyEnemyAlive, recordAllProp));
        mailSendParamsBuilder.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_BATTLE_RECORD_ALL);
        mailSendParamsBuilder.getContentBuilder().setBattleRecord(recordAllProp.getCopySsBuilder());
        // 填充title额外参数
        if (!recordAllProp.getSingleRecordList().isEmpty()) {
            BattleRecordRoleProp selfRole = getSelfOrEnemyRole(recordAllProp, false);
            if (selfRole != null) {
                // 主将信息
                Struct.DisplayParam.Builder heroParam = Struct.DisplayParam.newBuilder().setType(CommonEnum.DisplayParamType.DPT_HERO_ID).setNumber(selfRole.getMainHero().getHeroId());
                mailSendParamsBuilder.getTitleBuilder().getExtraNotifyDataBuilder().getParamsBuilder().addDatas(heroParam);
            }
        }
        return mailSendParamsBuilder.build();
    }

    @Override
    public void beforeEndSingleRelation(CommonEnum.BattleOverType type, IBattleRoleAdapter other, boolean isEnemyDead, BattleRelation relation) {
    }

    @Override
    public void onEndSingleRelation(CommonEnum.BattleOverType type, boolean isDead, boolean isEnemyDead, IBattleRoleAdapter other, BattleRecord.RecordOne record, boolean enemyNpc) {
        BattleRecord.RoleRecord myRecord = record.getRecordByRoleId(battleRole.getRoleId());
        final BattleAttackType attackType;
        if (isArmyToArmyWild(record.getOneRecord().getEntityType(), record.getOtherRecord().getEntityType())) {
            attackType = BattleAttackType.WILD_ARMY;
        } else if (getBattleRole().getRoleId() == record.getOneRecord().getRoleId()) {
            attackType = BattleAttackType.ATTACK;
        } else {
            attackType = BattleAttackType.DEFENCE;
        }
        getOwner().getEventDispatcher().dispatch(new EndSingleBattleEvent(attackType, type, isDead, isEnemyDead, (SceneObjBattleComponent) other, myRecord, record.getBattleType(), enemyNpc));
    }

    /**
     * 是否是野外部队交战
     *
     * @param roleType
     * @param otherRoleType
     * @return
     */
    private boolean isArmyToArmyWild(final EntityAttrOuterClass.EntityType roleType, final EntityAttrOuterClass.EntityType otherRoleType) {
        return roleType == EntityType.ET_Army && otherRoleType == EntityType.ET_Army;
    }

    @Override
    public void afterEndSingleRelation(CommonEnum.BattleOverType type, boolean isDead, boolean isEnemyDead, IBattleRoleAdapter other, BattleRecord.RecordOne record) {
        if (getOwner().getScene().isMainScene()) {
            sendBattleQLog(record, isDead, isEnemyDead);
        }
    }

    public void forceEndAllBattle() {
        forceEndAllBattle(CommonEnum.BattleOverType.BOT_OUT_OF_LOSE);
    }

    private void forceEndAllBattle(CommonEnum.BattleOverType type) {
        if (battleRole.getRelations().size() <= 0) {
            return;
        }
        LOGGER.info("SceneObjBattleComponent forceEndAllBattle owner={} type={}", getOwner(), type);
        for (BattleRelation battleRelation : new LinkedList<>(battleRole.getRelations())) {
            battleRelation.forceEndRelation(type);
        }
    }

    public void onDeleteObj() {
        battleRole.onDeleteObj();
    }

    /**
     * 对方pk攻击自己的时候
     */
    protected void onTargetPkMe(long targetId) {
    }

    @Override
    public void beforeOrdinaryAttack(BattleRole attacker, BattleRole defender) {
        onTargetPkMe(attacker.getRoleId());
    }

    public long getTargetId() {
        return getBattleProp().getTargetId();
    }

    public long getActiveTargetId() {
        return getBattleProp().getActiveTargetId();
    }

    /**
     * 外围系统设置战斗体的主动攻击目标
     */
    public void setActiveTargetId(long targetId) {
        getBattleRole().setActiveTargetId(targetId);
    }

    public void stopCurActiveAttack() {
        getBattleRole().setActiveTargetId(0);
    }

    /**
     * 获取队伍最大数量
     */
    public int getMax() {
        return getBattleRole().getSoldierMax();
    }

    @Override
    public void broadcastSkillResult(SkillResult skillResult, BattleConstants.BattleBroadCastNtfReason reason) {
        if (skillResult == null) {
            return;
        }

        if (isSkillNeedToNtf(skillResult)) {
            // 旧代码保留：fire了但是effectList为空，返回
            // if (skillResult.getFireType() == SkillFireType.SFT_FIRE && CollectionUtils.isEmpty(effectList)) {
            //     return;
            // }
            List<PlayerScene.EffectDTO> effectList = skillResult.getEffectList();
            // skillResult不是fire阶段，默认均需要发ntf给客户端
            Player_SkillFire_NTF.Builder message = Player_SkillFire_NTF.newBuilder()
                    .setAttackerId(skillResult.getAttackerId())
                    .setSkillId(skillResult.getSkillId())
                    .setHeroId(skillResult.getHeroId())
                    .setType(skillResult.getFireType())
                    .setTargetId(skillResult.getTargetId());
            SceneObjMoveComponent moveComponent = getOwner().getMoveComponent();
            if (moveComponent != null && !moveComponent.canMove()) {
                // 如果被定身，需要传一个技能朝向给客户端
                message.setYaw(getOwner().getMoveComponent().getMoveProp().getYaw().getCopyCsBuilder().build());
            }
            if (!message.hasYaw() && skillResult.getYaw() != null) {
                // 没被设置过Yaw值并且SkillResult中有Yaw值
                StructPB.PointPB.Builder yaw = StructPB.PointPB.newBuilder()
                        .setX(skillResult.getYaw().getX()).setY(skillResult.getYaw().getY());
                message.setYaw(yaw);
            }
            if (effectList.size() > 0) {
                message.addAllEffectList(effectList);
            }
            LOGGER.debug("broadcastSkillResult, ntf={} reason={}", message, reason);
            // 一些特殊单位需要必定显示，包成特殊协议下发，客户端仅对普通协议裁剪
            if (isMustNtfType()) {
                PlayerScene.Player_SpecialSkillFire_NTF.Builder specialMessage = PlayerScene.Player_SpecialSkillFire_NTF.newBuilder().setSkillFire(message);
                getOwner().getAoiNodeComponent().broadcast(MsgType.PLAYER_SPECIALSKILLFIRE_NTF, specialMessage.build(), BigSceneConstants.WORLD_NORMAL_MIN_LAYER);
            } else {
                getOwner().getAoiNodeComponent().broadcast(MsgType.PLAYER_SKILLFIRE_NTF, message.build(), BigSceneConstants.WORLD_NORMAL_MIN_LAYER);
            }
        }
    }

    /**
     * @param skillResult 技能释放结果
     * @return 返回技能释放结果是否需要ntf给客户端
     */
    private boolean isSkillNeedToNtf(SkillResult skillResult) {
        if (skillResult == null) {
            return false;
        }
        if (skillResult.getFireType() != CommonEnum.SkillFireType.SFT_FIRE) {
            // skillResult不在fire阶段，均需要发ntf给客户端
            return true;
        }
        if (!skillResult.getEffectList().isEmpty()) {
            // 命中effect不为空，一定要展示
            return true;
        }
        // 在fire阶段，命中effect为空，查询配表
        int skillId = skillResult.getSkillId();
        SkillConfigTemplate skillTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillTemplate(skillId);
        if (skillTemplate == null) {
            // skillId对应的技能配置不存在，默认发ntf给客户端
            return false;
        }
//        if (skillTemplate.getCombatAgreement()) {
//            // skillId对应的技能需要发ntf给客户端
//            return true;
//        }
//        for (int skillEffectId : skillTemplate.getGroupSkillList()) {
//            if (isEffectNeedToNtf(skillEffectId)) {
//                // effectId对应的效果配置或加减的buff信息需要发ntf给客户端
//                return true;
//            }
//        }
        return false;
    }

    /**
     * @param skillEffectId 技能效果id
     * @return 返回技能效果数据单元是否需要ntf给客户端
     */
    private boolean isEffectNeedToNtf(int skillEffectId) {
        // 技能效果配置一定存在
        SkillEffectTemplate effectTemplate = ResHolder.findTemplate(SkillEffectTemplate.class, skillEffectId);
        if (effectTemplate == null) {
            return false;
        }
        if (effectTemplate.getCombatAgreement()) {
            // skill effectId对应的效果配置需要发ntf给客户端
            // 释放N个技能效果的效果，只需要检查技能效果配置本身
            return true;
        }
        CommonEnum.SkillEffectType effectType = effectTemplate.getType();

        if (effectType == CommonEnum.SkillEffectType.SET_ADD_BUFF) {
            // 增加buff需要额外判断buffId对应的buff是否需要发ntf给客户端，移除buff，免疫buff效果，多效果都不会走到这里
            for (IntPairType pair : effectTemplate.getGroupBuff1PairList()) {
                // 独立概率buff
                BattleBuffTemplate buffTemplate = ResHolder.findTemplate(BattleBuffTemplate.class, pair.getKey());
                if (buffTemplate == null) {
                    // 如果一个buff取不到配置，跳过
                    continue;
                }
                if (buffTemplate.getCombatAgreement()) {
                    // buffId对应的buff配置需要发ntf给客户端
                    return true;
                }
            }
            for (IntPairType pair : effectTemplate.getGroupBuff2PairList()) {
                // 共同概率buff
                BattleBuffTemplate buffTemplate = ResHolder.findTemplate(BattleBuffTemplate.class, pair.getKey());
                if (buffTemplate == null) {
                    // 如果一个buff取不到配置，跳过
                    continue;
                }
                if (buffTemplate.getCombatAgreement()) {
                    // buffId对应的buff配置需要发ntf给客户端
                    return true;
                }
            }
        }
        if (effectType == CommonEnum.SkillEffectType.SET_ADD_BATCH_BUFF) {
            int buffId = effectTemplate.getValue1();
            BattleBuffTemplate buffTemplate = ResHolder.findTemplate(BattleBuffTemplate.class, buffId);
            // buffId对应的buff配置需要发ntf给客户端
            return buffTemplate != null && buffTemplate.getCombatAgreement();
        }
        return false;
    }

    public BattleHero getMainHero() {
        return getBattleRole().getMainHero();
    }

    /**
     * 被发起攻击
     */
    public boolean beAttacked(ArmyEntity armyEntity, Long attackerPlayerId) {
        return true;
    }

    @Override
    public boolean hasAnyAlive() {
        return getBattleRole().hasAnyAlive();
    }

    @Override
    public void onSettleRound(DamageResult damageResult) {
        getOwner().getEventDispatcher().dispatch(new BattleRoleSettleRoundEvent(getOwner().getEntityId(), getOwner().getPlayerId(), damageResult));
    }

    @Override
    public void clearAfterSettle() {
    }

    public BattleRelation relationWith(SceneObjBattleComponent other) {
        if (other == null) {
            return null;
        }
        BattleGround battleGround = getOwner().getScene().getBattleGroundComponent().getBattleGround();
        return battleGround.findBattleRelationOrNull(this.getEntityId(), other.getEntityId());
    }

    public int aliveCount() {
        return getBattleRole().aliveCount();
    }

    public int aliveThousandthRatio() {
        long alive = getBattleRole().aliveCount();
        long radio = alive * GameLogicConstants.IPPM / getBattleRole().getSoldierMax();
        return (int) radio;
    }

    public void enterBattleRoleState(BattleConstants.BattleRoleState roleState) {
        getBattleRole().getStateHandler().enterState(roleState);
    }

    public void leaveBattleRoleState(BattleConstants.BattleRoleState roleState) {
        getBattleRole().getStateHandler().leaveState(roleState);
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return getOwner().getCampEnum();
    }

    @Override
    public long getClanId() {
        return getOwner().getClanId();
    }

    @Override
    public long getPlayerId() {
        return getOwner().getPlayerId();
    }

    @Override
    public long getRoleTypeId() {
        return getOwner().getPlayerId();
    }

    @Override
    public long getLeaderRoleId() {
        return getRoleId();
    }

    /**
     * 是否是敌方玩家，只给战争狂热相关用，其他功能慎用！！
     *
     * @param actionPlayerClanId 发起行为的玩家所在的联盟id，可能为0
     * @return 是否是敌方玩家
     */
    public boolean isEnemyPlayer(long actionPlayerClanId) {
        return getPlayerId() != 0;
    }

    /**
     * 城內部队都被遣返后
     */
    public void afterReturnAllInnerArmy(List<Long> roleIds) {

    }

    /**
     * 开战后城内部队撤出
     */
    protected void onInnerArmyOutDropChild(InnerArmyDelEvent event) {
        if (!isInBattle()) {
            return;
        }
        ArmyEntity innerArmy = event.getArmyEntity();
        for (int innerArmySoldierId : innerArmy.getProp().getTroop().getTroop().keySet()) {
            getBattleRole().dropSoldierUnit(innerArmy.getEntityId(), innerArmySoldierId);
        }
        getBattleRole().refreshTroop();
        onSingleArmyLeaveBattleRole(innerArmy);
        innerArmy.getStatusComponent().setDetailBattle(false);
    }

    /**
     * 开战后新增城内部队
     */
    protected void onInnerArmyArriveAddChild0(InnerArmyAddEvent event, CommonEnum.SceneObjType unitRoleType) {
        if (!isInBattle()) {
            return;
        }
        ArmyEntity innerArmy = event.getArmyEntity();
        for (SoldierProp innerArmySoldierProp : innerArmy.getProp().getTroop().getTroop().values()) {
            getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(innerArmy.getEntityId(), unitRoleType), innerArmySoldierProp);
        }
        getBattleRole().refreshTroop();
        innerArmy.changeStateByAssistCity(CommonEnum.ArmyState.AS_InBattle);
        innerArmy.getStatusComponent().setDetailBattle(true);
        onSingleArmyArriveBattleRole(innerArmy);
    }

    /**
     * 单支部队离开战斗中的BattleRole
     */
    public void onSingleArmyLeaveBattleRole(ArmyEntity armyEntity) {
        if (isInBattle()) {
            armyEntity.getBattleComponent().settleRoleMemberWhenLeaveBattleRole(getBattleRole());
            AbstractScenePlayerEntity scenePlayer = armyEntity.getScenePlayer();
            getOwner().getEventDispatcher().dispatch(new TroopEvent(armyEntity.getEntityId(),
                    SoldierNumChangeReason.army_cut_out,
                    armyEntity.getAliveSoldierNum(),
                    scenePlayer != null ? scenePlayer.getName() : "",
                    scenePlayer != null ? scenePlayer.getClanName() : "",
                    scenePlayer != null ? scenePlayer.getCardHeadSS() : Struct.PlayerCardHead.getDefaultInstance()));
        }
    }

    /**
     * 单支部队加入战斗中的BattleRole
     */
    public void onSingleArmyArriveBattleRole(ArmyEntity armyEntity) {
        if (!isInBattle()) {
            return;
        }
        armyEntity.getBattleComponent().fillRoleMemberWhenArriveBattleRole(getBattleRole());
        AbstractScenePlayerEntity scenePlayer = armyEntity.getScenePlayer();
        getOwner().getEventDispatcher().dispatch(new TroopEvent(armyEntity.getEntityId(),
                SoldierNumChangeReason.army_cut_in,
                armyEntity.getAliveSoldierNum(),
                scenePlayer != null ? scenePlayer.getName() : "",
                scenePlayer != null ? scenePlayer.getClanName() : "",
                scenePlayer != null ? scenePlayer.getCardHeadSS() : Struct.PlayerCardHead.getDefaultInstance()));
        addBattleEntityCache(armyEntity.getEntityId(), armyEntity.getPlayerId());
    }

    public void addBattleEntityCache(long entityId, long playerId) {
        onceInBattleEntityCache.put(entityId, new SimpleBattleEntityInfo().setEntityId(entityId).setPlayerId(playerId));
    }

    public Set<Long> getNeedSendRecordPlayerId() {
        return onceInBattleEntityCache.values().stream().map(SimpleBattleEntityInfo::getPlayerId).collect(Collectors.toSet());
    }

    public boolean hasTarget() {
        return battleRole.hasTarget();
    }

    public void recover() {
        battleRole.recover();
    }

    @Override
    public long getRoleId() {
        return getEntityId();
    }

    protected void clearTroop() {
        getTroop().getTroop().clear();
        // 原来的实现只是简单将主副将英雄id设置为0，会有部分残留数据
        getTroop().getMainHero().mergeFromSs(Struct.Hero.getDefaultInstance());
        getTroop().getDeputyHero().mergeFromSs(Struct.Hero.getDefaultInstance());
    }

    protected BattleRecordRoleProp getSelfOrEnemyRole(BattleRecordAllProp recordAllProp, boolean isEnemy) {
        BattleRecordRoleProp role = null;
        if (!recordAllProp.getSingleRecordList().isEmpty()) {
            // 取最后一个对手
            BattleRecordOneProp mainBattle = recordAllProp.getSingleRecordList().get(recordAllProp.getSingleRecordListSize() - 1);
            if (mainBattle.getIsLeftRole()) {
                role = isEnemy ? mainBattle.getRightRole() : mainBattle.getLeftRole();
            } else {
                role = isEnemy ? mainBattle.getLeftRole() : mainBattle.getRightRole();
            }
        } else {
            LOGGER.error("SingleRecordList is empty. role:{}", this.battleRole.getRoleId());
        }
        return role;
    }

    protected String getBattleRecordMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordRoleProp enemyRole) {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        if (enemyRole != null) {
            boolean isSuperWeaponAtk = enemyRole.getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill.getNumber();
            if (isSuperWeaponAtk) {
                // "战斗报告"
                return constTemplate.getBattleRecord3();
            }
        }
        if (alive && anyEnemyAlive) {
            // "战斗报告"
            return constTemplate.getBattleRecord3();
        } else {
            // "战斗胜利" : "战斗失败"
            return alive ? constTemplate.getBattleRecord1() : constTemplate.getBattleRecord2();
        }
    }

    /**
     * 是否有多个Army作战
     */
    protected boolean isMultiArmy(BattleRecordAllProp recordAllProp) {
        return recordAllProp.getSingleRecordListSize() > 1;
    }

    /**
     * 获取邮件title
     */
    protected StructMail.MailShowTitle getMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        return StructMail.MailShowTitle.getDefaultInstance();
    }

    /**
     * 发送战报qlog
     */
    private void sendBattleRecordQLog(Set<Long> playerIdList, BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        QlogCncBattleReport battleRepFlow = getBattleRepFlow(alive, anyEnemyAlive, recordAllProp);
        if (battleRepFlow == null) {
            return;
        }
        ScenePlayerMgrComponent playerMgrComponent = getOwner().getScene().getPlayerMgrComponent();
        for (Long playerId : playerIdList) {
            ScenePlayerEntity scenePlayer = (ScenePlayerEntity) playerMgrComponent.getScenePlayer(playerId);
            if (scenePlayer == null) {
                continue;
            }
            Gson gson = new Gson();
            QlogCncBattleReport newBattleFlow = gson.fromJson(gson.toJson(battleRepFlow), QlogCncBattleReport.class);
            newBattleFlow.setPlayerPower(0)
                    .setPlayerLevel(scenePlayer.getLevel())
                    .setClanId(scenePlayer.getClanId());
            newBattleFlow.fillHead(scenePlayer.getQlogComponent());
            newBattleFlow.sendToQlog();
        }
    }

    protected QlogCncBattleReport getBattleRepFlow(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        return null;
    }

    protected QlogCncBattle getBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        QlogCncBattle battleFlow = new QlogCncBattle();
        // 设置qlog产生时间、战斗id、战斗开始/结束时间，战斗结果等基础字段
        battleFlow.setDtEventTime(TimeUtils.now2String())
                .setBattleID(String.valueOf(record.getBattleId()))
                .setBattleTimeStart(TimeUtils.timeStampMs2String(record.getStartMs()))
                .setBattleTimeEnd(TimeUtils.timeStampMs2String(record.getEndMs()))
                .setBattleResult(BattleRepResult.getBattleResult(alive, isEnemyAlive).ordinal());
        // 判断并设置是进攻/防守方
        BattleRecord.RoleRecord needLogRecord = record.getRecordByRoleId(getBattleRole().getRoleId());
        BattleRecord.RoleRecord otherRecord = record.getOtherRecord();
        if (needLogRecord == record.getOneRecord()) {
            battleFlow.setAttackOrNot(BattleAttackType.ATTACK.ordinal());
        } else {
            needLogRecord = record.getOtherRecord();
            battleFlow.setAttackOrNot(BattleAttackType.DEFENCE.ordinal());
            otherRecord = record.getOneRecord();
        }
        // 可能是不需要记录或者记录缺失的
        if (needLogRecord == null || otherRecord == null) {
            LOGGER.debug("no need to record, record is {}, otherRecord is {}", record, otherRecord);
            return null;
        }
        // 主体坐标设置
        battleFlow.setBattleStartCoordinate(needLogRecord.getLocation().getX() + "," + needLogRecord.getLocation().getY());
        // 设置战斗主英雄详情
        battleFlow.setArmedMajorHeroConfig(getHeroConfigStrForBattleFlow(needLogRecord.getLeader().getMainHero()));
        // 设置战斗副英雄详情
        battleFlow.setArmedSubHeroConfig(getHeroConfigStrForBattleFlow(needLogRecord.getLeader().getDeputyHero()));
        // 设置敌方id和种类
        setEnemyIDAndTypeToFlow(otherRecord, battleFlow);
        return battleFlow;
    }

    /**
     * 构建战斗qlog，抽象方法
     */
    abstract protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record);

    /**
     * 区域内的敌方单位
     */
    public List<SceneObjEntity> getEnemyList(Shape shape, Predicate<SceneObjEntity> predicate) {
        List<SceneObjEntity> res = new ArrayList<>();
        getOwner().getScene().getAoiMgrComponent().consumerAffectSceneObj(
                shape,
                (obj) -> {
                    if (obj.isDestroy() || obj.getEntityId() == getEntityId() || !getOwner().canBattle(obj, true)) {
                        return;
                    }
                    if (!shape.containsPoint(obj.getCurPoint().getX(), obj.getCurPoint().getY())) {
                        return;
                    }
                    if (predicate == null || predicate.test(obj)) {
                        res.add(obj);
                    }
                });
        return res;
    }

    /**
     * 半径内的敌方单位  传入的是米为单位的！
     */
    public List<SceneObjEntity> getEnemyList(int radius, Predicate<SceneObjEntity> predicate) {
        return getEnemyList(getOwner().getTransformComponent().getRange(radius), predicate);
    }

    private void onClanChange(ClanChangeEventFirst event) {
        if (!isInBattle()) {
            return;
        }
        long oldClanId = event.getOldClanId();
        long newClanId = getClanId();
        if (oldClanId != newClanId) {
            getBattleRole().forEachRelation(relation -> {
                BattleRole enemyRole = relation.getEnemyRole(getRoleId());
                // 退出联盟
                if (oldClanId != 0) {
                    if (enemyRole.getRoleId() == getTargetId()) {
                        // 移除老联盟对目标的合围关系
                        enemyRole.getSiegeHandler().removeSiege(getBattleRole());
                    }
                    // 移除老联盟的仇恨
                    if (getBattleRole().getAdapter().getZoneId() == enemyRole.getAdapter().getZoneId()) {
                        getOwner().getScene().getHateComponent().removeHate(oldClanId, enemyRole.getAdapter().getClanId());
                    }
                }

                // 加入联盟
                if (newClanId != 0) {
                    if (enemyRole.getRoleId() == getTargetId()) {
                        boolean isNewClanSiegeLimit = enemyRole.getAdapter().isSiegeLimit(newClanId, getRoleId());
                        if (!isNewClanSiegeLimit) {
                            // 新联盟对当前目标还可以合围
                            // 新增新联盟对目标的合围关系。
                            enemyRole.getSiegeHandler().addSiege(getBattleRole(), relation, true);
                        }
                        // else在onSelfClanChange里面处理
                    }
                    // 新增新联盟的仇恨
                    if (getBattleRole().getAdapter().getZoneId() == enemyRole.getAdapter().getZoneId()) {
                        getOwner().getScene().getHateComponent().addHate(newClanId, enemyRole.getAdapter().getClanId());
                    }
                }
            });
        }
    }

    @Override
    public List<Long> getAllChildRoleIdList() {
        return Lists.newArrayList(getEntityId());
    }

    protected void goToHospital(AbstractScenePlayerHospitalComponent hospitalComp, BattleProp battleProp, List<Struct.PlayerHospitalSoldier> severeSoldier, Map<Integer, SoldierLossData> beforeHospitalData) {
        Map<Integer, SoldierLossData> newLossData = Maps.newHashMap();
        // 重伤进医院
        HospitalSoldierHandleResult hospitalResult = hospitalComp.onSettleRound(severeSoldier);

        // 记录重伤变死亡的数量
        final int deadInHospital = hospitalResult.totalDeadNum();
        battleProp.setHospitalFullCauseDeadCount(battleProp.getHospitalFullCauseDeadCount() + deadInHospital);

        for (HospitalSoldierHandleResult.Unit unit : hospitalResult.getUnits()) {
            SoldierLossData lossData = new SoldierLossData()
                    .setSevere(unit.getWaitingCure())
                    .setDead(unit.getDead());
            newLossData.put(unit.getSoldierId(), lossData);
        }

        // 修改原来的数据
        for (Map.Entry<Integer, SoldierLossData> hospitalRes : newLossData.entrySet()) {
            beforeHospitalData.get(hospitalRes.getKey())
                    .setSevere(hospitalRes.getValue().getSevere())
                    .increaseDead(hospitalRes.getValue().getDead());
        }
    }

    @Override
    public boolean isDistanceOk(long targetRoleId) {
        SceneObjEntity target = getObjEntityByBattleRole(targetRoleId);
        if (target == null) {
            return false;
        }
        return BattleHelper.isBattleDistanceOk(this.getBattleRole(), target.getBattleComponent().getBattleRole());
    }

    public boolean hasBattleWithSceneObjByType(CommonEnum.SceneObjType type) {
        for (BattleRelation relation : getBattleRole().getAllValidRelation()) {
            if (relation.getEnemyRole(getRoleId()).getType() == type) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void handleSoldierTreat(List<SoldierLossDTO> treatData) {
        if (treatData.isEmpty()) {
            return;
        }
        getOwner().getEventDispatcher().dispatch(new TreatSoldierEvent(treatData));
    }

    private void onSkillTrigger(SkillTriggerEvent event) {
        SkillSystem.trigger(battleRole, event.getType());
    }

    public double aliveSoldierRate() {
        return getBattleRole().aliveSoldierRate();
    }

    @Override
    public boolean isRally() {
        return false;
    }

    @Override
    public long getRallyCapacity() {
        return -1;
    }

    public void onBattleRoundEvent(BattleRoundEvent event) {
        getBattleRole().addBattleRoundEvent(event);
    }

    /**
     * 发送战斗qlog
     */
    public void sendBattleQLog(BattleRecord.RecordOne record, boolean isDead, boolean isEnemyDead) {
        // 战斗对象是Npc部队，没有record信息
        if (battleRole.isNpcTroop() || !getOwner().getScene().isMainScene()) {
            return;
        }
        QlogCncBattle battleFlow = constructBattleFlow(!isDead, !isEnemyDead, record);
        if (battleFlow != null) {
            BattleRecord.RoleRecord myRecord = record.getRecordByRoleId(battleRole.getRoleId());
            for (Map.Entry<Long, BattleRecord.RoleMemberRecord> entry : myRecord.getMembers().entrySet()) {
                long playerId = entry.getValue().getPlayerId();
                if (playerId <= 0) {
                    continue;
                }
                ScenePlayerEntity playerEntity = (ScenePlayerEntity) getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
                if (playerEntity == null) {
                    continue;
                }

                Gson gson = new Gson();
                QlogCncBattle newBattleFlow = gson.fromJson(gson.toJson(battleFlow), QlogCncBattle.class);
                setArmyConfigToBattleFlow(entry.getValue(), newBattleFlow);
                newBattleFlow.fillHead(playerEntity.getQlogComponent());
                newBattleFlow.sendToQlog();
            }
        }
    }

    /**
     * @param hero 英雄结构
     * @return 获取战斗流水需要的英雄信息string
     */
    private String getHeroConfigStrForBattleFlow(BattleRecord.BattleRecordHero hero) {
        if (null == hero) {
            return "";
        }
        HeroLevelStar subHero = new HeroLevelStar();
        subHero.setHeroId(hero.getHeroId());
        subHero.setHeroLevel(hero.getLv());
        subHero.setStarLevel(hero.getStar());
      //  subHero.setSkillList(hero.getSkills());
        return JsonUtils.toJsonString(subHero);
    }


    /**
     * 构建设置军队信息，填写到战斗流水中
     *
     * @param memberRecord 成员的record，其对应的军队信息，包括战斗前、轻伤、重伤、死亡
     * @param battleFlow   战斗流水结构
     */
    private void setArmyConfigToBattleFlow(BattleRecord.RoleMemberRecord memberRecord, QlogCncBattle battleFlow) {
        Map<Integer, ArmyConfig> soldierMap = new HashMap<>();
        Map<Integer, ArmyConfig> afterSoldierMap = new HashMap<>();
        Map<Integer, ArmyConfig> slightlyWoundArmyMap = new HashMap<>();
        Map<Integer, ArmyConfig> severeWoundArmyMap = new HashMap<>();
        Map<Integer, ArmyConfig> deadMap = new HashMap<>();

        for (Map.Entry<Integer, BattleRecord.BattleRecordSoldierResult> entry : memberRecord.getSoldier().entrySet()) {
            int soldierId = entry.getKey();
            BattleRecord.BattleRecordSoldierResult res = entry.getValue();
            soldierMap.put(soldierId, new ArmyConfig(soldierId, res.getTotalAlive()));
            if (res.getSumSlightWound() > 0) {
                slightlyWoundArmyMap.put(soldierId, new ArmyConfig(soldierId, res.getSumSlightWound()));
            }
            if (res.getSevereWound() > 0) {
                severeWoundArmyMap.put(soldierId, new ArmyConfig(soldierId, res.getSevereWound()));
            }
            if (res.getDead() > 0) {
                deadMap.put(soldierId, new ArmyConfig(soldierId, res.getDead()));
            }
            afterSoldierMap.put(soldierId, new ArmyConfig(soldierId, res.getLeftAlive()));
        }
        battleFlow.setArmyId(String.valueOf(memberRecord.getMemberRoleId()))
                .setSeriousInjuryArmyConfig(JsonUtils.toJsonString(severeWoundArmyMap.values()))
                .setSlightlyInjuryArmyConfig(JsonUtils.toJsonString(slightlyWoundArmyMap.values()))
                .setDeadArmyConfig(JsonUtils.toJsonString(deadMap.values()))
                .setStartArmyConfig(JsonUtils.toJsonString(soldierMap.values()))
                .setEndArmyConfig(JsonUtils.toJsonString(afterSoldierMap.values()));
    }

    private void setEnemyIDAndTypeToFlow(BattleRecord.RoleRecord enemyRecord, QlogCncBattle battleFlow) {
        // 设置敌方类型及敌方id
        battleFlow.setEnemyID("");
        if (enemyRecord.getRoleType() == CommonEnum.SceneObjType.SOT_MONSTER) {
            List<String> list = Lists.newArrayList();
            list.add(enemyRecord.getCardHead().getName());
            battleFlow.setEnemyType("monster")
                    .setEnemyID(QlogUtils.transCollectionString2ArrayString(list));
        } else if (enemyRecord.getRoleType() == CommonEnum.SceneObjType.SOT_STRONG_POINT_ARMY) {
            battleFlow.setEnemyType("monster_building");
        } else if (enemyRecord.getRoleType() == CommonEnum.SceneObjType.SOT_ARMY) {
            battleFlow.setEnemyType("single_army");
        } else if (enemyRecord.getRoleType() == CommonEnum.SceneObjType.SOT_ARMY_GROUP) {
            battleFlow.setEnemyType("assemble_army");
        } else if (enemyRecord.getRoleType() == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY || enemyRecord.getRoleType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF) {
            battleFlow.setEnemyType("reinforce_army");
        } else {
            battleFlow.setEnemyType("");
        }
    }

    protected void notifyScenePlayerEndAllRelation(BattleResult battleResult) {
        // 医院
        for (Long playerId : getNeedSendRecordPlayerId()) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
            if (scenePlayer.getHospitalComponent() != null) {
                scenePlayer.getHospitalComponent().onEndAllRelation();
            }
        }

        // 战损
        for (Map.Entry<Long, Long> entry : battleResult.getAllPlayerLossPower(getRoleId()).entrySet()) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(entry.getKey());
            if (scenePlayer.getBattleComponent() != null) {
                boolean fail = (!battleResult.alive) && battleResult.anyEnemyAlive;
                scenePlayer.getBattleComponent().onAllBattleEnd(entry.getValue(), fail);
            }
        }
    }

    protected void updateBattleRecordToPlayer(EndSingleBattleEvent event) {
        for (BattleRecord.RoleMemberRecord record : event.getRoleRecord().getMembers().values()) {
            if (record.getPlayerId() <= 0) {
                continue;
            }
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(record.getPlayerId());
            if (scenePlayer == null) {
                LOGGER.error("updateBattleRecordToPlayer failed scenePlayer is null={} {}", record.getPlayerId(), getOwner());
                continue;
            }
            if (scenePlayer.getBattleComponent() == null) {
                LOGGER.warn("updateBattleRecordToPlayer failed scenePlayer has no battleComponent={} {}", record.getPlayerId(), getOwner());
                continue;
            }
            scenePlayer.getBattleComponent().onBattleRelationEnd(event.getAttackType(), event.getResult(), record, event.isEnemyNpc(), event.getOther().getZoneId());
        }
    }

    public Map<Long, SimpleBattleEntityInfo> getOnceInBattleEntityCache() {
        return onceInBattleEntityCache;
    }

    /**
     * 是否达到合围上限
     */
    @Override
    public boolean isSiegeLimit(long clanId, long roleId) {
        if (!isInBattle() || !needCheckSiege()) {
            return false;
        }
        Set<Long> siegeRoles = getBattleRole().getSiegeHandler().getSiegeRoles(clanId);
        if (roleId > 0 && siegeRoles.contains(roleId)) {
            return false;
        }
        return siegeRoles.size() >= getSiegeLimitCount();
    }

    @Override
    public boolean needCheckSiege() {
        return getSiegeLimitCount() > 0;
    }

    /**
     * 获取合围数量
     *
     * @return <=0：无限
     */
    private int getSiegeLimitCount() {
        if (getOwner() instanceof BuildingEntityType) {
            return ((BuildingEntityType) getOwner()).getBuildingTemplate().getSiegeLimit();
        }
        return 0;
    }

    public void handUpRelationWith(SceneObjEntity targetEntity) {
        if (targetEntity != null && targetEntity.getBattleComponent() != null) {
            BattleRelation relation = getOwner().getBattleComponent().getBattleRole().relationWith(targetEntity.getBattleComponent().getBattleRole());
            if (relation != null) {
                relation.setStatus(BattleRelationStatus.Suspend);
            }
        }
    }

    @Override
    public Set<Long> getBattleRecordPlayerIds() {
        return getNeedSendRecordPlayerId();
    }

    protected void trySendBattleRecordAfterPlunder(BattleRelation relation) {
        // 尝试找出能够发邮件的战斗
        BattleRoleContext roleCtx = getBattleRole().getContext();
        long finishedRecordId = 0;

        for (Map.Entry<Long, BattleRecordMailSnapshot> entry : roleCtx.mailSnapshotMap.entrySet()) {
            BattleRecordMailSnapshot mailCtx = entry.getValue();
            mailCtx.pendingPlunderRelationIds.remove(relation.getId());
            if (mailCtx.pendingPlunderRelationIds.isEmpty()) {
                finishedRecordId = entry.getKey();
                break;
            }
        }
        if (finishedRecordId != 0) {
            BattleRecordMailSnapshot ctx = roleCtx.flushSnapshot(finishedRecordId);
            trySendRecordMail(ctx.needSendRecordPlayerIds, ctx.recordAllProp, ctx.isMeAlive, ctx.isAnyEnemyAlive);
        }
    }

    public void tryActivateRole(String reason) {
        getOwner().getScene().getBattleGroundComponent().getBattleGround().tryActivateRole(getBattleRole(), reason);
    }

    /**
     * 是否能被技能索敌选中
     */
    @Override
    public boolean canBeSearchSelect() {
        return true;
    }

    @Override
    public CommonEnum.DamageRatioTypeEnum getDamageRatioType() {
        if (getOwner() instanceof BuildingEntityType && getOwner().getClanId() > 0) {
            return ((BuildingEntityType) getOwner()).getBuildingTemplate().getRatioType();
        }
        return CommonEnum.DamageRatioTypeEnum.DTE_NONE;
    }

    @Override
    public Point getCurPoint() {
        return getOwner().getCurPoint();
    }

    @Override
    public boolean isDestroy() {
        return getOwner().isDestroy();
    }

    @Override
    public boolean isInClanTerritory() {
        return getOwner().isInClanTerritory();
    }

    @Override
    public void askCityToPlunder(Map<Long, Map<Long, SsPlayerMisc.PlunderWeight>> plunderWeightMap) {
    }

    @Override
    public CommonEnum.PlunderProtectReason getBePlunderReasonOrNull() {
        return null;
    }

    @Override
    public int reduceDurability(int effectValue) {
        return 0;
    }

    @Override
    public boolean isCollecting() {
        return false;
    }

    @Override
    public List<Integer> getBattleEvaSkills() {
        return Lists.newArrayList();
    }

    @Override
    public IBattleAdditionAdapter getAdditionAdapter() {
        return additionAdapter;
    }

    @Override
    public IBattleBuffAdapter getBuffAdapter() {
        return buffAdapter;
    }

    @Override
    public IBattleMoveAdapter getMoveAdapter() {
        return moveAdapter;
    }

    @Override
    public int getModelRadius() {
        return getOwner().getTransformComponent().getModelRadius();
    }

    public SceneObjEntity getObjEntityByBattleRole(long roleId) {
        return getOwner().getScene().getObjMgrComponent().getSceneObjEntity(roleId);
    }

    protected AbstractScenePlayerEntity getScenePlayer() {
        return null;
    }

    @Override
    public BattleRole invokeSummoningMonster(SummoningMonsterTemplate template, Point castPoint, SceneObjSpawnParam param) {
        long delayMs = param.getLifeTime() - SystemClock.now();
        if (delayMs <= 0) {
            LOGGER.error("SceneObjBattleComponent invokeSummoningMonster failed, life is before now, param={}", param);
            return null;
        }

        int castX = castPoint.getX();
        int castY = castPoint.getY();
        int offsetX = template.getDeviationPair().getKey();
        int offsetY = template.getDeviationPair().getValue();
        SceneEntity scene = getOwner().getScene();
        int monsterTemplateID = template.getMonsterID();
        int innerR = template.getRangePair().getKey();
        int outerR = template.getRangePair().getValue();


        // 中心点
        Point curPoint = Point.valueOf(castX + offsetX, castY + offsetY);
        // 出生点
        Point bornPoint = AreaSkillFactory.selectBornPointByRing(scene, curPoint, innerR, outerR);

        if (bornPoint == null) {
            LOGGER.error("SceneObjBattleComponent invokeSummoningMonster failed, scene={}, curPoint={} innerR={} outerR={}", scene, curPoint, innerR, outerR);
            return null;
        }
        MonsterEntity monsterEntity = MonsterFactory.initMonster(scene, monsterTemplateID, bornPoint, param);
        if (monsterEntity == null) {
            LOGGER.error("SceneObjBattleComponent invokeSummoningMonster failed, scene={}, monsterTemplateID={} bornPoint={} param={}", scene, monsterTemplateID, bornPoint, param);
            return null;
        }
        // 加入场景
        monsterEntity.addIntoScene();

        return monsterEntity.getBattleComponent().getBattleRole();
    }

    /**
     * BOSS、区域技能、玩家自身部队、玩家主城、据点和高级城市 攻击者为这些单位作为通知
     */
    public boolean isMustNtfType() {
        return false;
    }

    @Override
    public List<BattleRole> getGvgMapBuildInnerArmy() {
        return Lists.newArrayList();
    }

    public class SimpleBattleEntityInfo {
        private long entityId;
        private long playerId;

        public long getEntityId() {
            return entityId;
        }

        public SimpleBattleEntityInfo setEntityId(long entityId) {
            this.entityId = entityId;
            return this;
        }

        public long getPlayerId() {
            return playerId;
        }

        public SimpleBattleEntityInfo setPlayerId(long playerId) {
            this.playerId = playerId;
            return this;
        }
    }


    @Override
    public boolean isActivityEscortBoss() {
        return false;
    }

    @Override
    public CommonEnum.ArmyDetailState getArmyDetailState() {
        return CommonEnum.ArmyDetailState.ADS_NONE;
    }

    @Override
    public int getFreeHitMonsterCount() {
        return -1;
    }

    public void updateFreeHitMonsterCount(int count) {
    }
}

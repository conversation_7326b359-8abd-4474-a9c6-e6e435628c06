package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.army.BattleUnitPropChangeEvent;
import com.yorha.cnc.scene.event.battle.SkillTriggerEvent;
import com.yorha.cnc.scene.event.player.*;
import com.yorha.cnc.scene.sceneObj.component.SceneObjPropComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.game.gen.prop.ArmyProp;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.AttachState;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ArmyPropComponent extends SceneObjPropComponent {
    private static final Logger LOGGER = LogManager.getLogger(ArmyPropComponent.class);

    public ArmyPropComponent(ArmyEntity owner) {
        super(owner);
    }


    @Override
    public ArmyEntity getOwner() {
        return (ArmyEntity) super.getOwner();
    }

    private ArmyProp getProp() {
        return getOwner().getProp();
    }

    /**
     * 设置行军联盟id和简称
     */
    public void setClanIdName(long clanId, String clanName) {
        long oldClanId = getProp().getClanId();
        String oldName = getProp().getClanSname();
        getProp().setClanId(clanId).setClanSname(clanName);
        if (oldClanId != clanId) {
            getOwner().getEventDispatcher().dispatch(new ClanChangeEventFirst(getEntityId(), oldClanId, clanId));
            getOwner().getEventDispatcher().dispatch(new ClanChangeEvent(getEntityId(), oldClanId, clanId));
        } else if (!oldName.equals(clanName)) {
            getOwner().getEventDispatcher().dispatch(new ClanSimpleNameChangeEvent(getEntityId(), clanName));
        }
    }

    /**
     * 同步玩家名字
     */
    public void onSyncPlayerName(String newName) {
        getProp().getCardHead().setName(newName);
        getOwner().getEventDispatcher().dispatch(new PlayerNameChangeEvent(getEntityId(), newName));
    }

    public void onSyncPlayerPic(int pic, String picUrl) {
        getProp().getCardHead().setPic(pic).setPicUrl(picUrl);
        getOwner().getEventDispatcher().dispatch(new PlayerPicChangeEvent(getEntityId(), pic, picUrl));
    }

    public void onSyncPlayerPicFrame(int picFrame) {
        getProp().getCardHead().setPicFrame(picFrame);
        getOwner().getEventDispatcher().dispatch(new PlayerPicFrameChangeEvent(getEntityId(), picFrame));
    }

    /**
     * 更新联盟旗帜  只有集结部队会
     */
    public void updateClanFlag(SceneClanEntity sceneClan) {
        sceneClan.copy2FlagInfo(getProp().getClanFlag());
    }

    public void onHeroPropChange(List<Struct.Hero> heroList) {
        boolean syncFlag = false;
        // 过滤真实需要同步的hero
        List<Struct.Hero> heroRealChangeList = new ArrayList<>();
        if (heroList != null && heroList.size() > 0) {
            for (Struct.Hero hero : heroList) {
                HeroProp mainHero = getOwner().getProp().getTroop().getMainHero();
                if (mainHero.getHeroId() == hero.getHeroId()) {
                    heroRealChangeList.add(hero);
                    // 不可能主将副将同一个吧
                    continue;
                }
                HeroProp deputyHero = getOwner().getProp().getTroop().getDeputyHero();
                if (deputyHero.getHeroId() == hero.getHeroId()) {
                    heroRealChangeList.add(hero);
                }
            }
        }
        if (syncFlag || heroRealChangeList.size() > 0) {
            getOwner().getEventDispatcher().dispatch(new BattleUnitPropChangeEvent(heroRealChangeList));
        }
    }

    public void onAttachChange(AttachState state, long attachId) {
        AttachState oldState = getOwner().getProp().getAttachState();
        if (oldState == state) {
            return;
        }
        getOwner().getProp().setAttachState(state).setAttachId(attachId);
        LOGGER.info("{} onAttachChange state: {}  id: {}", getOwner(), state, attachId);

        if (state != AttachState.AAS_NONE) {
            return;
        }
        if (oldState == AttachState.AAS_ASSIST || oldState == AttachState.AAS_COLLECT) {
            getOwner().getEventDispatcher().dispatch(new SkillTriggerEvent(CommonEnum.TriggerType.TT_LEAVE_BUILDING));
            getOwner().getTransformComponent().forceUpdateModelRadius();
        }
    }
}

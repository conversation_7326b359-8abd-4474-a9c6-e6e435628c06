package com.yorha.cnc.scene.gm.command.city;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class QueryBornRegion implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isMainScene()) {
            return;
        }
        int regionId = Integer.parseInt(args.get("regionId"));
        Map<CommonEnum.MapAreaType, List<Integer>> regionPartList = actor.getScene().getMapTemplateDataItem().getRegionBornPartList(regionId);
        if (regionPartList == null) {
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, String.valueOf(0));
        }
        AtomicInteger sum = new AtomicInteger();
        regionPartList.values().forEach(item -> item.forEach(it -> {
            sum.addAndGet(actor.getScene().getBornMgrComponent().getPartToNum(it));
        }));
        throw new GeminiException(ErrorCode.SYSTEM_WARNING, String.valueOf(sum));
    }

    @Override
    public String showHelp() {
        return "QueryBornRegion regionId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

package com.yorha.cnc.scene.sceneclan;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneclan.component.*;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.eventdispatcher.EventDispatcher;
import com.yorha.game.gen.prop.ClanFlagInfoProp;
import com.yorha.game.gen.prop.Int64PositionMarkInfoMapProp;
import com.yorha.game.gen.prop.SceneClanProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.SsSceneClan;
import com.yorha.proto.Struct.SceneClan;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.StructPlayerPB.RallyInfoListPB;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 联盟实体在地图线程的映射
 *
 * <AUTHOR>
 */
public class SceneClanEntity extends AbstractEntity {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanEntity.class);

    private final SceneEntity sceneEntityRef;
    private final SceneClanRallyComponent rallyComponent;
    private final SceneClanMapBuildingComponent mapBuildingComponent;
    private final SceneClanMemberComponent memberComponent;
    private final SceneClanCrossComponent crossComponent;
    private final SceneClanAdditionComponent additionComponent;
    private final SceneClanBuildComponent buildComponent;
    private final SceneClanLogComponent logComponent;
    private final SceneClanQlogComponent qlogComponent;
    private final SceneClanDevBuffComponent devBuffComponent;
    private final SceneClanProp prop;
    private final EventDispatcher eventDispatcher = new EventDispatcher();

    public SceneClanEntity(long entityId, SceneEntity sceneEntity, SceneClan sceneClan) {
        super(entityId);
        this.prop = new SceneClanProp();
        this.prop.mergeChangeFromSs(sceneClan);
        this.sceneEntityRef = sceneEntity;
        this.rallyComponent = new SceneClanRallyComponent(this);
        this.mapBuildingComponent = new SceneClanMapBuildingComponent(this);
        this.memberComponent = new SceneClanMemberComponent(this);
        this.crossComponent = new SceneClanCrossComponent(this);
        this.additionComponent = new SceneClanAdditionComponent(this);
        this.buildComponent = new SceneClanBuildComponent(this);
        this.logComponent = new SceneClanLogComponent(this);
        this.qlogComponent = new SceneClanQlogComponent(this);
        this.devBuffComponent = new SceneClanDevBuffComponent(this);
        initAllComponents();
    }

    private List<GeneratedMessageV3> msgList = null;
    private boolean isUpdateBuff = false;

    public void setUpdateBuff(boolean updateBuff) {
        isUpdateBuff = updateBuff;
    }

    public void checkTerritoryData(int partNum, long totalPower) {
        // 緩存的消息
        if (msgList != null) {
            for (GeneratedMessageV3 msg : msgList) {
                tellClan(msg);
            }
            LOGGER.info("checkTerritoryData {} send msg cache msgList={}", getEntityId(), msgList.size());
            msgList = null;
        }
        // buff
        if (isUpdateBuff) {
            getMapBuildingComponent().sendTerritoryBuffSnapshot();
        }
        // 势力值快照
        Set<Integer> myPart = getMapBuildingComponent().getMyPart();
        int myPartNum = myPart.size();
        int myTotalPower = getMapBuildingComponent().getTotalPower();
        if (partNum != myPartNum || totalPower != myTotalPower) {
            getMapBuildingComponent().syncPowerToClan();
            LOGGER.info("checkTerritoryData {} myPartNum={} totalPower={}", getEntityId(), myPart.size(), totalPower);
        }
    }

    public void tellClan(GeneratedMessageV3 msg) {
        if (getSceneEntity().isInitOk()) {
            ownerActor().tellClan(getZoneId(), getEntityId(), msg);
            return;
        }
        if (msgList == null) {
            msgList = new ArrayList<>();
        }
        msgList.add(msg);
    }

    public int getZoneId() {
        return ServerContext.getZoneId();
    }

    public SceneClanProp getProp() {
        return prop;
    }

    public String getClanSimpleName() {
        return prop.getClanSimpleName();
    }

    public String getClanName() {
        return prop.getClanName();
    }

    public int getTerritoryColor() {
        return prop.getTerritoryColor();
    }

    /**
     * 王国区域描边显示颜色
     */
    public int getZoneAreaTerritoryColor() {
        return 0;
    }

    public int getNationFlagId() {
        return prop.getNationFlagId();
    }

    public int getFlagColor() {
        return prop.getFlagColor();
    }

    public int getFlagShading() {
        return prop.getFlagShading();
    }

    public int getFlagSign() {
        return prop.getFlagSign();
    }

    public long getClanOwnerId() {
        return prop.getOwnerId();
    }

    public int getRegionId() {
        return prop.getRegionId();
    }

    public StructClanPB.ClanFlagInfoPB getFlagInfoPb() {
        StructClanPB.ClanFlagInfoPB.Builder builder = StructClanPB.ClanFlagInfoPB.newBuilder();
        return builder.setFlagColor(getFlagColor())
                .setFlagShading(getFlagShading())
                .setFlagSign(getFlagSign())
                .setTerritoryColor(getTerritoryColor())
                .setNationFlagId(getNationFlagId()).build();
    }

    public void copy2FlagInfo(ClanFlagInfoProp prop) {
        prop.setFlagColor(getFlagColor())
                .setFlagShading(getFlagShading())
                .setFlagSign(getFlagSign())
                .setTerritoryColor(getTerritoryColor())
                .setNationFlagId(getNationFlagId());
    }

    /**
     * 联盟发生变化，接受同步
     */
    public void onSyncSceneClan(SsSceneClan.SyncSceneClanCmd ask) {
        SceneClan sceneClan = ask.getSceneClan();
        if (sceneClan.hasOwnerId()) {
            getMemberComponent().onMemberCityChange();
        }
        prop.mergeChangeFromSs(sceneClan);
        if (sceneClan.hasOwnerId()) {
            // 军团长变更，军团所属的州可能也会变更
            getMemberComponent().onOwnerChange();
        }
        // 写扩散到玩家和玩家的军队城池上
        if (sceneClan.hasClanSimpleName()) {
            getMemberComponent().onClanBaseChange();
        }
        // 旗帜/名字变了  写扩散到建筑、集结上
        if (sceneClan.hasClanName() || sceneClan.hasClanSimpleName()
                || sceneClan.hasFlagColor() || sceneClan.hasFlagSign()
                || sceneClan.hasFlagShading() || sceneClan.hasTerritoryColor() || sceneClan.hasNationFlagId()) {
            getMapBuildingComponent().onClanBaseChange(sceneClan.hasTerritoryColor() || sceneClan.hasNationFlagId());
            getRallyComponent().onClanBaseChange();
            getBuildComponent().onClanBaseChange();
        }
        if (sceneClan.hasPower() && getSceneEntity().getMileStoneOrNullComponent() != null) {
            getSceneEntity().getMileStoneOrNullComponent().onClanPowerChange(prop.getClanId(), sceneClan.getPower());
        }
        if (ask.hasMemberNum() && getSceneEntity().getMileStoneOrNullComponent() != null) {
            getSceneEntity().getMileStoneOrNullComponent().onClanPlayerMemberChange(getEntityId(), ask.getMemberNum());
        }
    }

    @Override
    public EntityType getEntityType() {
        return null;
    }

    @Override
    public SceneActor ownerActor() {
        return sceneEntityRef.ownerActor();
    }

    @Override
    public void deleteObj() {
        getRallyComponent().dismissAllRally(RallyDismissReason.RDR_CLAN_DISMISS);
        getMapBuildingComponent().onClanDismiss();
        getBuildComponent().onClanDismiss();
        getMemberComponent().onClanDisMiss();
        getSceneEntity().getClanMgrComponent().removeSceneClan(getEntityId());
        super.deleteObj();
        LOGGER.info("{} deleteObj", this);
    }

    @Override
    protected void onPostInitFailed() {
    }

    public SceneEntity getSceneEntity() {
        return sceneEntityRef;
    }

    /**
     * 获取联盟里所有集结列表
     */
    public RallyInfoListPB getRallyInfoList() {
        RallyInfoListPB.Builder listBuilder = RallyInfoListPB.newBuilder();
        getRallyComponent().copyRallyInfoListToBuilder(listBuilder);
        return listBuilder.build();
    }

    public Int64PositionMarkInfoMapProp getPositionMarkMap() {
        return getProp().getClanPosMarkMap();
    }

    /**
     * 获取联盟里指定id的集结信息
     */
    public RallyInfoPB getOneRallyInfo(long rallyId) {
        return getRallyComponent().getOneRallyInfo(rallyId);
    }

    /**
     * @return 是否是任何人都可以加入的
     */
    public boolean isEveryOneCanJoin() {
        return prop.getRequire() == CommonEnum.ClanEnterRequire.NONE;
    }

    /**
     * 获取军团战力，注意战力同步存在一定延迟，不是实时的
     */
    public long getPower() {
        return getProp().getPower();
    }

    /**
     * 获取军团人数
     */
    public int getMemberNum() {
        return getMemberComponent().getMemberSize();
    }

    /**
     * 返回军团是否已满员
     */
    public boolean isClanFull() {
        long curMemberNum = getMemberNum();
        long basicMaxMemberNum = ResHolder.getConsts(ConstClanTemplate.class).getClanMaxNum();
        long buffAddMemberNum = getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_ADD_CLAN_MEMBER_NUM.getNumber());
        return curMemberNum >= basicMaxMemberNum + buffAddMemberNum;
    }

    /**
     * 获取军团是否活跃(即是否仍在内存中), 仅用于qlog
     */
    public boolean isActive() {
        return getProp().getIsActive();
    }

    public long getClanId() {
        return getProp().getClanId();
    }

    /**
     * 获取集结entity
     */
    public RallyEntity getRallyEntity(long rallyId) {
        return getRallyComponent().getRallyEntity(rallyId);
    }

    public SceneClanRallyComponent getRallyComponent() {
        return rallyComponent;
    }

    public SceneClanMapBuildingComponent getMapBuildingComponent() {
        return mapBuildingComponent;
    }

    public SceneClanMemberComponent getMemberComponent() {
        return memberComponent;
    }

    public SceneClanCrossComponent getCrossComponent() {
        return crossComponent;
    }

    public SceneClanAdditionComponent getAddComponent() {
        return additionComponent;
    }

    public SceneClanBuildComponent getBuildComponent() {
        return buildComponent;
    }

    public SceneClanLogComponent getLogComponent() {
        return logComponent;
    }

    public EventDispatcher getEventDispatcher() {
        return eventDispatcher;
    }

    public SceneClanQlogComponent getQlogComponent() {
        return qlogComponent;
    }

    public SceneClanDevBuffComponent getDevBuffComponent() {
        return devBuffComponent;
    }
}

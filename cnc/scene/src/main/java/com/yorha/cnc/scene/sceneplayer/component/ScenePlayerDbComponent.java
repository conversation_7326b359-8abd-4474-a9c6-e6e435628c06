package com.yorha.cnc.scene.sceneplayer.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.ActorChangeAttrUpsertStrategy;
import com.yorha.common.dbactor.DbTaskProxy;
import com.yorha.common.dbactor.DefaultDbOperationStrategyImpl;
import com.yorha.common.dbactor.IDbChangeDeleteStrategy;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ScenePlayerProp;
import com.yorha.proto.Player;
import com.yorha.proto.TcaplusDb.ScenePlayerTable;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 */
public class ScenePlayerDbComponent extends AbstractComponent<ScenePlayerEntity> {
    private DbTaskProxy proxy;

    public ScenePlayerDbComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    /**
     * 设置存盘许可
     */
    public void beginSaveChangeToDb(final Player.ScenePlayer changeAttr) {
        if (this.proxy != null) {
            throw new GeminiException("already save scene player");
        }
        final ScenePlayerActorChangeStrategy strategy = new ScenePlayerActorChangeStrategy(this.getOwner(), changeAttr != null ? changeAttr : Player.ScenePlayer.getDefaultInstance());
        this.proxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString())
                .owner(this.ownerActor())
                .limitTimerOwner(getOwner().getTimerComponent())
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .upsert(strategy)
                .delete(strategy)
                .entityId(String.valueOf(this.getEntityId()))
                .intervalMs(DbLimitConstants.SCENE_PLAYER_DB_LIMIT_INTERVAL_MS)
                .build();
    }

    @Override
    public SceneActor ownerActor() {
        return getOwner().ownerActor();
    }

    /**
     * insert
     */
    public void insertIntoDb() {
        this.beginSaveChangeToDb(null);
        this.proxy.insert();
    }

    public void saveChangeToDb() {
        if (this.proxy == null) {
            throw new GeminiException("scene player not save change to db!");
        }
        this.proxy.update();
    }

    /**
     * 落脏（销毁前调用）
     */
    public boolean saveOnDestroy() {
        if (!this.ownerActor().isDestroy()) {
            WechatLog.error("ScenePlayerDbComponent endDb scene actor is not destroy! ScenePlayer {}", getEntityId());
        }
        if (this.proxy == null) {
            return false;
        }
        this.proxy.saveDbAsync();
        this.proxy.stop();
        return true;
    }

    public void stopByMigrate() {
        this.proxy.stop();
        this.proxy = null;
    }

    private static final class ScenePlayerActorChangeStrategy extends ActorChangeAttrUpsertStrategy<Player.ScenePlayer> implements IDbChangeDeleteStrategy {
        private final ScenePlayerEntity entity;

        public ScenePlayerActorChangeStrategy(ScenePlayerEntity entity, GeneratedMessageV3 msg) {
            super(msg);
            this.entity = entity;
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            return this.entity.getProp().copyChangeToDb((Player.ScenePlayer.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected Player.ScenePlayer buildFullAttrSaveData(AbstractActor actor) {
            return this.entity.getProp().getCopyDbBuilder().build();
        }

        @Override
        protected Player.ScenePlayer buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            final ScenePlayerTable.Builder builder = (ScenePlayerTable.Builder) result.value;
            return ScenePlayerProp.of(builder.getFullAttr(), builder.getChangedAttr()).getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Player.ScenePlayer fullAttr, @NotNull Player.ScenePlayer changeAttr) {
            final ScenePlayerTable.Builder req = ScenePlayerTable.newBuilder();
            req.setZoneId(this.entity.getScene().getZoneId());
            req.setScenePlayerId(this.entity.getEntityId());
            if (fullAttr != null) {
                req.setFullAttr(fullAttr);
            }
            req.setChangedAttr(changeAttr);
            return req;
        }

        @Override
        public Message.Builder buildDeleteRequest(AbstractActor actor) {
            final ScenePlayerTable.Builder request = ScenePlayerTable.newBuilder();
            request.setZoneId(this.entity.getScene().getZoneId());
            request.setScenePlayerId(this.entity.getEntityId());
            return request;
        }

    }

    @Override
    public String toString() {
        return "ScenePlayerDbComponent{" +
                "playerId=" + this.getEntityId() +
                '}';
    }
}

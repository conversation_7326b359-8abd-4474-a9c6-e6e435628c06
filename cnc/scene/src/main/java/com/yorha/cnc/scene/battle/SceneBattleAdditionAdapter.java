package com.yorha.cnc.scene.battle;

import com.yorha.cnc.battle.adapter.interfaces.IBattleAdditionAdapter;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
public class SceneBattleAdditionAdapter implements IBattleAdditionAdapter {
    private final SceneObjAdditionComponent component;

    public SceneBattleAdditionAdapter(SceneObjAdditionComponent component) {
        this.component = component;
    }

    @Override
    public Map<CommonEnum.BuffEffectType, Long> getAllAdditions() {
        return component.getAllAdditions();
    }

    @Override
    public long getAddition(CommonEnum.BuffEffectType additionId) {
        return component.getAddition(additionId);
    }

    @Override
    public void dispatchAdditionChange(CommonEnum.BuffEffectType additionId) {
        SceneObjAdditionComponent.dispatchChange(component.getOwner(), additionId);
    }
}

package com.yorha.cnc.scene.sceneclan.rally.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.army.ArmyFactory;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.proto.CommonEnum.ArmyDetailState;
import com.yorha.proto.CommonEnum.RallyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class RallyStateComponent extends AbstractComponent<RallyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(RallyStateComponent.class);

    public RallyStateComponent(RallyEntity owner) {
        super(owner);
    }

    /**
     * 开始倒计时
     */
    public void start(int waitTime) {
        LOGGER.info("{} enter prepare state. waitTime: {}", getOwner(), waitTime);
        long now = SystemClock.now();
        getProp().setRallyState(RallyState.RS_Preparing)
                .setStateStartTs(now)
                .setStateEndTs(now + waitTime * 1000L);
        getOwner().getTimerComponent().addTimer(TimerReasonType.RALLY_PREPARE,
                this::onPrepareStateEnd,
                waitTime, TimeUnit.SECONDS);
    }

    /**
     * 集结倒计时阶段结束
     * 进入集结等待阶段
     */
    public void onPrepareStateEnd() {
        LOGGER.info("{} leave prepare state", getOwner());
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.RALLY_PREPARE);
        enterWaitState();
    }

    /**
     * 进入集结等待阶段
     */
    private void enterWaitState() {
        // 最晚到达的时间
        long latestArriveTime = getOwner().getArmyMgrComponent().getLatestArriveTime();
        if (latestArriveTime == 0) {
            // 所有人已到齐 出发
            allReady();
        } else {
            getProp().setStateStartTs(SystemClock.now()).setRallyState(RallyState.RS_Waiting).setStateEndTs(latestArriveTime);
            // 更新army状态
            for (ArmyEntity army : getOwner().getArmyMgrComponent().getInRallyArmies()) {
                army.getStatusComponent().setDetailState(ArmyDetailState.ADS_RALLY, latestArriveTime);
            }
            LOGGER.info("{} enter wait state", getOwner());
        }
    }

    /**
     * 未到达队伍change
     */
    public void onUnArriveArmyChange(long arriveTs) {
        if (getProp().getRallyState() != RallyState.RS_Waiting) {
            return;
        }
        if (arriveTs == 0) {
            allReady();
            return;
        }
        if (arriveTs != getProp().getStateEndTs()) {
            getProp().setStateEndTs(arriveTs);
            // 更新army状态
            for (ArmyEntity army : getOwner().getArmyMgrComponent().getInRallyArmies()) {
                army.getStatusComponent().setDetailState(ArmyDetailState.ADS_RALLY, arriveTs);
            }
        }
    }

    /**
     * 时间到，所有条件符合后
     * 集结正式发车出发
     */
    private void allReady() {
        if (getOwner().getRallyArmyProp().size() < GameLogicConstants.RALLY_ARMY_MIN) {
            getOwner().dismiss(RallyDismissReason.RDR_NO_JOINER);
            return;
        }
        LOGGER.info("{} allReady", getOwner());
        try {
            ArmyFactory.createArmyFromRally(getOwner());
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                LOGGER.error("{} create rally army failed ", getOwner(), e);
            } else {
                LOGGER.info("{} create rally army failed ", getOwner(), e);
            }
            getOwner().dismiss(RallyDismissReason.RDR_NO_PATH);
        }
    }

    /**
     * 集结车队开始走向目标点
     * isOutBattleMove : 是否是脱战追击
     */
    public void onRallyArmyMove(long moveArriveTime, Point point, boolean isOutBattleMove) {
        if (!isOutBattleMove && getProp().getRallyState() == RallyState.RS_InBattle) {
            return;
        }
        getProp().setStateStartTs(SystemClock.now()).setRallyState(RallyState.RS_Moving).setStateEndTs(moveArriveTime);
        // 更新army状态
        for (ArmyEntity army : getOwner().getArmyMgrComponent().getInRallyArmies()) {
            army.getStatusComponent().setDetailState(ArmyDetailState.ADS_MOVE_BATTLE, moveArriveTime, point);
        }
        LOGGER.info("{} enter move, set time:{} {}", getOwner(), moveArriveTime, isOutBattleMove);
    }

    /**
     * 集结车队开始战斗
     */
    public void onRallyArmyStartBattle(SceneObjEntity target) {
        getProp().setRallyState(RallyState.RS_InBattle);
        // 更新army状态
        for (ArmyEntity army : getOwner().getArmyMgrComponent().getInRallyArmies()) {
            army.getStatusComponent().setDetailTarget(ArmyDetailState.ADS_BATTLE, target);
        }
    }

    /**
     * 是否可以加入
     */
    public boolean checkCanJoin() {
        return getProp().getRallyState() != RallyState.RS_Waiting;
    }


    /**
     * 解散
     */
    public void dismiss() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.RALLY_PREPARE);
    }

    public RallyInfoProp getProp() {
        return getOwner().getProp();
    }

    @Override
    public void init() {

    }
}

package com.yorha.cnc.scene.sceneObj.ai.stateMachine;

import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 状态机基类
 * <AUTHOR>
 */
public abstract class AbstractStateDiagram {

    public static List<StateTransitionConfig> getTransitions(CommonEnum.AIStateType state, Map<CommonEnum.AIStateType, List<StateTransitionConfig>> map) {
        return map.get(state);
    }

    public abstract Map<CommonEnum.AIStateType, List<StateTransitionConfig>> getStateMap();

    public abstract CommonEnum.AIStateType getInitState();

    public static void addTransition(StateTransitionConfig config, Map<CommonEnum.AIStateType, List<StateTransitionConfig>> map) {
        map.computeIfAbsent(config.getTargetState(), k -> new ArrayList<>()).add(config);
    }
}

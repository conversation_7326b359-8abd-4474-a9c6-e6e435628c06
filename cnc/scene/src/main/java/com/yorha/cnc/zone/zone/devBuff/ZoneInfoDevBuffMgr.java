package com.yorha.cnc.zone.zone.devBuff;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import res.template.BuffTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/5/9
 */
public class ZoneInfoDevBuffMgr extends DevBuffMgrBase<ZoneEntity> {
    public ZoneInfoDevBuffMgr(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public DevBuffMgrBase.LOGLEVEL logLevel() {
        return LOGLEVEL.INFO;
    }

    @Override
    public DevBuffSysProp getDevBuffSys() {
        return getOwner().getProp().getDevBuffSys();
    }

    @Override
    protected void afterAddDevBuff(DevBuffProp devBuff, int addLayer) {
        // 更新加成
        BuffTemplate buffTemplate = getBuffTemplate(devBuff.getDevBuffId());
        getOwner().getAdditionComponent().updateAddition(AdditionProviderType.ZONE_BUFF, buffTemplate.getType());
    }

    @Override
    protected void afterRemoveDevBuff(DevBuffProp devBuffToRemove, boolean isExpired, int decLayer) {
        // 更新加成
        BuffTemplate buffTemplate = getBuffTemplate(devBuffToRemove.getDevBuffId());
        getOwner().getAdditionComponent().updateAddition(AdditionProviderType.ZONE_BUFF, buffTemplate.getType());
    }

    @Override
    public CommonEnum.DevBuffType getBuffType() {
        return CommonEnum.DevBuffType.DBT_SCENE_BUFF;
    }

    @Override
    protected void addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        getOwner().getTimerComponent().addTimerWithPrefix(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    protected void cancelTimer(String prefix, TimerReasonType timerReasonType) {
        getOwner().getTimerComponent().cancelTimer(timerReasonType, prefix);
    }
}

package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerCityMgrComponent;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.event.army.BattleUnitPropChangeEvent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.Struct;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ScenePlayerCityComponent extends AbstractScenePlayerCityMgrComponent {

    public ScenePlayerCityComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    public void onNewMainCity(CityEntity city) {
        super.onNewMainCity(city);
        city.getEventDispatcher().addEventListenerRepeat((e) -> getOwner().getWallComponent().onCityBattleEnd(e), EndAllBattleEvent.class);
    }

    public boolean checkAscend(long curTime) {
        // 城池升天
        if (getOwner().isOnline()) {
            return false;
        }
        if (getMyMainCity() == null) {
            return false;
        }
        // return getMyMainCity().getTransformComponent().cityCheckAscend(curTime, getOwner().getProp().getLogoutTime());
        // FIXME: 不升天了
        return false;
    }

    /**
     * 英雄数据变更，写扩散到主城上，正在战斗中的主城需要重新构建守城英雄数据
     */
    public void spreadBattleUnitChangeToCity(List<Struct.Hero> heroList) {
        final CityEntity mainCity = getOwner().getMainCity();
        if (!mainCity.isInBattle()) {
            return;
        }
        // 过滤真实需要同步的hero
        List<Struct.Hero> heroRealChangeList = new ArrayList<>();
        if (heroList != null && !heroList.isEmpty()) {
            for (Struct.Hero hero : heroList) {
                TroopProp troop = mainCity.getProp().getTroop();
                if (troop.getMainHero().getHeroId() == hero.getHeroId()) {
                    heroRealChangeList.add(hero);
                }
                if (troop.getDeputyHero().getHeroId() == hero.getHeroId()) {
                    heroRealChangeList.add(hero);
                }
            }
        }

        if (!heroRealChangeList.isEmpty()) {
            mainCity.getEventDispatcher().dispatch(new BattleUnitPropChangeEvent(heroRealChangeList));
        }
    }

    /**
     * 获取玩家基地评级
     */
    public int getEra() {
        return getMyMainCity().getProp().getEraLevel();
    }

    public void updateCityBuildInfo(int templateId) {
        // ！这里注意如果有新增建筑类型需要同步到scene上的话，在GameLogicConstants.NEED_SYNC_SCENE_BUILD中需要加一下

    }
}

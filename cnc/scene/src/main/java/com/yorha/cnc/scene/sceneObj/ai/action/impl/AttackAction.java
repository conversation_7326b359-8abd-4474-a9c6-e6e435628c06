package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.battle.unit.BattleHelper;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;

/**
 * 原地反击
 * 进入条件： 仇恨列表不为空
 * 进入：设置战斗点位
 * 执行：原地反击仇恨值最高敌对单位，目标超出攻击距离丢失目标
 * 结束：停止与当前攻击目标战斗
 * <AUTHOR>
 */
public class AttackAction extends AbstractAIAction {

    public AttackAction() {
        super();
    }


    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        // 攻击状态要求当前仇恨列表不为空
        SceneObjEntity owner = component.getOwner();
        SceneObjEntity target = owner.getHateListComponent().mostHateEntity();
        if (target == null) {
            if (component.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", component.getLogHead(), getActionName(), false, "target is null");
            }
            return false;
        }
        if (outRange(owner, target)) {
            if (component.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", component.getLogHead(), getActionName(), false, target.getEntityId() + " out range");
            }
            return false;
        }
        if (component.isDebugAble()) {
            LOGGER.info("{}, action :{} isSatisfied：{}", component.getLogHead(), getActionName(), true);
        }
        return true;
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        SceneObjEntity owner = component.getOwner();
        owner.getTransformComponent().enterBattle();
    }

    private boolean outRange(SceneObjEntity owner, SceneObjEntity target) {
        return !BattleHelper.isBattleDistanceOk(owner.getBattleComponent().getBattleRole(), target.getBattleComponent().getBattleRole());
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
        SceneObjEntity owner = component.getOwner();
        SceneObjEntity target = component.getOwner().getHateListComponent().mostHateEntity();
        if (target == null || target.isDestroy()) {
            owner.getHateListComponent().clearHate(component.getOwner().getHateListComponent().getMostHateEntity());
            if (component.getOwner().getHateListComponent().getHateEntities().isEmpty()) {
                component.triggerEvent(AIEvent.LOSE_TARGET);
            }
            return;
        }
        if (outRange(owner, target)) {
            return;
        }
        if (component.getOwner().getBattleComponent().getTargetId() == target.getEntityId()) {
            return;
        }
        component.getOwner().getBattleComponent().tryStartBattleWith(target);
        if (component.isDebugAble()) {
            LOGGER.info("{}, fire and execute :{} target:{} ", component.getLogHead(), getActionName(), target);
        }
    }

    @Override
    protected String getActionName() {
        return "AttackAction";
    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        super.onEnd(component);
        SceneObjEntity owner = component.getOwner();
        long targetId = owner.getBattleComponent().getTargetId();
        if (targetId != 0) {
            owner.getBattleComponent().stopCurActiveAttack();
            owner.getHateListComponent().clearHate(targetId);
        }
    }
}

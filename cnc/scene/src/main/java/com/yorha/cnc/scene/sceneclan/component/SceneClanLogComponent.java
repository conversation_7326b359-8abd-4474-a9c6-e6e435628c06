package com.yorha.cnc.scene.sceneclan.component;

import com.yorha.cnc.scene.mapBuilding.component.MapBuildingClanLogComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanAttr;

/**
 * <AUTHOR>
 * 场景上联盟的日志管理
 */
public class SceneClanLogComponent extends AbstractComponent<SceneClanEntity> {

    public SceneClanLogComponent(SceneClanEntity owner) {
        super(owner);
    }

    /**
     * 创建改建完成的军团日志
     *
     * @param playerName 玩家名字
     * @param templateId 建筑templateId
     * @param point      建筑位置
     */
    public void recordCreateRebuildFinishLog(String playerName, int templateId, Point point) {
        SsClanAttr.CreateClanLogCmd.Builder cmdBuilder = SsClanAttr.CreateClanLogCmd.newBuilder();
        SsClanAttr.RebuildLogMsg.Builder rebuildLogBuilder = SsClanAttr.RebuildLogMsg.newBuilder();
        rebuildLogBuilder.setPoint(MapBuildingClanLogComponent.genPoint(point.getX(), point.getY(), getOwner().getSceneEntity()))
                .setTemplateId(templateId).setRebuildPlayerName(playerName);
        cmdBuilder.setRecordType(CommonEnum.ClanRecordType.CRT_LOG_REBUILD).setRebuildLogMsg(rebuildLogBuilder);
        getOwner().tellClan(cmdBuilder.build());
    }

    /**
     * 拆除改建完成的军团日志
     *
     * @param playerName 玩家名字
     * @param templateId 建筑templateId
     * @param point      位置信息
     */
    public void recordDestroyRebuildingLog(String playerName, int templateId, Point point) {
        SsClanAttr.CreateClanLogCmd.Builder cmdBuilder = SsClanAttr.CreateClanLogCmd.newBuilder();
        SsClanAttr.DestroyLogMsg.Builder destroyLogBuilder = SsClanAttr.DestroyLogMsg.newBuilder();
        destroyLogBuilder.setPoint(MapBuildingClanLogComponent.genPoint(point.getX(), point.getY(), getOwner().getSceneEntity()))
                .setTemplateId(templateId).setDestroyPlayerName(playerName);
        cmdBuilder.setRecordType(CommonEnum.ClanRecordType.CRT_LOG_DESTROY).setDestroyLogMsg(destroyLogBuilder);
        getOwner().tellClan(cmdBuilder.build());
    }
}

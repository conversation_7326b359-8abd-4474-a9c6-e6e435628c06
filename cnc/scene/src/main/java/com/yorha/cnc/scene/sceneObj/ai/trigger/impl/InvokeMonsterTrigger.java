package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;

import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.CircleType;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.BasicPB;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import org.apache.commons.collections4.CollectionUtils;
import res.template.AiInvokeTemplate;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * <p>
 * 召唤野怪触发器
 */
public class InvokeMonsterTrigger extends AiTrigger {

    /**
     * 召唤id
     */
    private List<Integer> invokeIds;

    /**
     * 召唤的儿子是否需要跟随销毁
     */
    private boolean needFollowDestroy;

    public InvokeMonsterTrigger(AiStateTriggerTemplate template) {
        super(template);
    }

    @Override
    protected void parse(List<IntPairType> param) {
        invokeIds = new ArrayList<>();
        for (IntPairType pair : param) {
            if (pair.getKey() == 1) {
                needFollowDestroy = (pair.getValue() == 1);
                continue;
            }
            invokeIds.add(pair.getValue());
        }
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        return true;
    }

    @Override
    protected void doTrigger(SceneObjEntity owner) {
        BasicPB.Int64ListPB.Builder monsterIds = BasicPB.Int64ListPB.newBuilder();
        invokeIds.forEach(invokeId -> {
            List<MonsterEntity> entities = invokeMonster(owner, invokeId, needFollowDestroy);
            if (CollectionUtils.isEmpty(entities)) {
                return;
            }
            entities.forEach(entity -> monsterIds.addDatas(entity.getEntityId()));
        });
        // 不需要回收自己的，发下通知
        PlayerScene.Player_MonsterInvoke_NTF.Builder response = PlayerScene.Player_MonsterInvoke_NTF.newBuilder();
        response.setMasterId(owner.getEntityId());
        response.setMonsterIds(monsterIds);
        owner.getAoiNodeComponent().broadcast(MsgType.PLAYER_MONSTERINVOKE_NTF, response.build());
    }

    /**
     * 召唤小野怪
     */
    public List<MonsterEntity> invokeMonster(SceneObjEntity master, int invokeId, boolean needFollowDestroy) {
        AiInvokeTemplate invokeTemplate = ResHolder.getInstance().getValueFromMap(AiInvokeTemplate.class, invokeId);
        SceneObjSpawnParam param = new SceneObjSpawnParam();
        // 与master朝向相同
        if (invokeTemplate.getYaw() == CommonEnum.InvokeYawType.IYT_FOLLOW_MASTER) {
            param.setYaw(master.getMoveComponent().getYaw().toPoint());
        }
        // 朝向master
        if (invokeTemplate.getYaw() == CommonEnum.InvokeYawType.IYT_TO_MASTER) {
            param.setYaw(master.getCurPoint());
        }

        List<MonsterEntity> monsters = new ArrayList<>();
        int num = invokeTemplate.getNum();
        for (int i = 0; i < num; i++) {
            Point curPoint = master.getCurPoint();
            CircleType bornPosition = RandomUtils.randomList(invokeTemplate.getBornAreaList());
            int x = curPoint.getX();
            int y = curPoint.getY();
            x += bornPosition.getX();
            y += bornPosition.getY();
            Circle circle = Circle.valueOf(x, y, bornPosition.getR());
            Point bornPoint = BornPointHelper.randomRingBornPoint(master.getScene(), circle, 0, true);
            if (bornPoint == null) {
                continue;
            }
            MonsterEntity entity = MonsterFactory.initMonster(master.getScene(), invokeTemplate.getMonsterId(), bornPoint, param);
            if (entity == null) {
                continue;
            }
            entity.getAiComponent().addRecord(CommonEnum.AiRecordType.ART_MASTER, master.getEntityId());
            entity.getAiComponent().triggerEvent(AIEvent.FIND_MASTER);
            entity.addIntoScene();
            monsters.add(entity);
            master.getAiComponent().addInvokes(needFollowDestroy, entity.getEntityId());
            if (invokeTemplate.getLifeTime() > 0) {
                entity.setForceRecyleTime(TimeUnit.SECONDS.toMillis(invokeTemplate.getLifeTime()));
            }
        }
        return monsters;
    }

    @Override
    protected String getTriggerParam() {
        return invokeIds.toString();
    }

    @Override
    protected String getTriggerName() {
        return "InvokeMonsterTrigger";
    }
}

package com.yorha.cnc.scene.gm.command.city;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.GmHelper;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.Map;

/**
 * 查询当前城池状态 总数目,升天数目
 *
 * <AUTHOR>
 */
public class QueryBornStatus implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isBigScene()) {
            return;
        }
        BigSceneEntity scene = (BigSceneEntity) actor.getScene();
        StringBuilder string = new StringBuilder();
        string.append("导量总人数: ");
        string.append(scene.getZoneEntity().getBornMgrComponent().getPlayerNum());
        string.append("\n");

        string.append("各州实时人数: ");
        for (int i = 0; i < 10; i++) {
            Map<CommonEnum.MapAreaType, List<Integer>> regionPartList = actor.getScene().getMapTemplateDataItem().getRegionBornPartList(i);
            if (regionPartList == null) {
                throw new GeminiException(ErrorCode.SYSTEM_WARNING, String.valueOf(0));
            }
            int sum = 0;
            for (List<Integer> item : regionPartList.values()) {
                for (Integer partId : item) {
                    sum += scene.getBornMgrComponent().getPartToNum(partId);
                }
            }
            string.append(i);
            string.append(": ");
            string.append(sum);
            string.append(", ");
        }
        string.append("\n");
        string.append("各区域导量人数: ");
        string.append("\n");
        int total = 0;
        for (Map.Entry<Integer, Integer> entry : scene.getBornMgrComponent().getAllPartToNum().entrySet()) {
            string.append(entry.getKey());
            string.append(": ");
            string.append(entry.getValue());
            string.append("   等级");
            string.append(": ");
            string.append(actor.getScene().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, entry.getKey()).getLevel());
            string.append("\n");
            total += entry.getValue();
        }
        string.append("各区域导量人数相加计算出的总人数: ");
        string.append(total);
        string.append("\n");

        int ascendNum = 0;
        for (CityEntity city : scene.getObjMgrComponent().getObjsByType(CityEntity.class)) {
            if (city.getTransformComponent().isAscend()) {
                ascendNum++;
            }
        }
        string.append("已升天数: ");
        string.append(ascendNum);
        string.append("\n");

        GmHelper.sendGmNtfMail(actor.getScenePlayer(playerId).getZoneId(), playerId, "QueryBornStatus", string.toString());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.common.utils.shape.Circle;
import res.template.MapBuildingTemplate;

/**
 * 公用建筑类型位置描述组件 凡是在地图建筑表里配置的都可用
 *
 * <AUTHOR>
 */
public class BuildingTransformComponent extends SceneObjTransformComponent {
    private final BuildingEntityType entity;

    public BuildingTransformComponent(SceneObjEntity owner, BuildingEntityType entity, PointProp pointRef) {
        super(owner, pointRef);
        this.entity = entity;
    }

    @Override
    public void resetModelRadius() {
        setModelRadius(getTemplate().getModelRadius());
    }

    @Override
    public void addCollision() {
        int pathRadius = getTemplate().getPathRadius();
        if (pathRadius <= 0) {
            return;
        }
        Circle collisionCircle = Circle.valueOf(getCurPoint().getX(), getCurPoint().getY(), pathRadius);
        getOwner().getScene().getPathFindMgrComponent().addDynamicCircleCollision(getEntityId(), collisionCircle);
    }

    @Override
    public int getCityMoveCollisionRadius() {
        return getTemplate().getCollisionRadius();
    }

    @Override
    public int getPathCollisionRadius() {
        return getTemplate().getPathRadius();
    }

    private MapBuildingTemplate getTemplate() {
        return entity.getBuildingTemplate();
    }
}

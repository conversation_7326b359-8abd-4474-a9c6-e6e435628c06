package com.yorha.cnc.scene.entity.component;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.framework.AbstractComponent;

/**
 * <AUTHOR>
 */
public abstract class SceneComponent extends AbstractComponent<SceneEntity> {

    public SceneComponent(SceneEntity owner) {
        super(owner);
    }
    
    @Override
    public SceneActor ownerActor() {
        return getOwner().ownerActor();
    }
}

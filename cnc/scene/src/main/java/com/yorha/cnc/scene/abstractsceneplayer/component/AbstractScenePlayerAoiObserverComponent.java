package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.aoiView.manager.AoiObserver;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.shape.AABB;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Entity.EntityNtfMsg;
import com.yorha.proto.EntityAttrOuterClass;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class AbstractScenePlayerAoiObserverComponent extends AbstractComponent<AbstractScenePlayerEntity> implements AoiObserver {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerAoiObserverComponent.class);

    public AbstractScenePlayerAoiObserverComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    /**
     * 更新视野新设的 包围盒和对应层级
     */
    private AABB aabb;
    private int newLayer;
    private int entityNumMax;
    /**
     * 当前看到的obj列表
     */
    private Set<Long> curViewObj = new LongOpenHashSet();
    /**
     * 已经算好的aoi格子的对应的层级
     */
    private Set<AoiGrid> curAoiGrids = new ObjectOpenHashSet<>();
    private int curLayer;
    /**
     * 玩家关注的对象列表
     * 在此列表中的对象不会发送创建和删除ntf并永远同步脏变化
     */
    private final Set<Long> obTargetSet = new HashSet<>();

    @Override
    public int getSceneZoneId() {
        return getOwner().getScene().getZoneId();
    }

    @Override
    public long getId() {
        return getEntityId();
    }

    @Override
    public IActorRef getSessionRef() {
        return getOwner().getSessionRef();
    }

    @Override
    public void setNewView(Set<AoiGrid> curAoiGrids, int layer) {
        this.curAoiGrids = curAoiGrids;
        this.curLayer = layer;
    }

    @Override
    public int getOldLayer() {
        return curLayer;
    }

    @Override
    public Set<AoiGrid> getOldAoiGrid() {
        return curAoiGrids;
    }

    @Override
    public void setUpdateView(AABB aabb, int layer, int entityNumMax) {
        this.aabb = aabb;
        this.newLayer = layer;
        this.entityNumMax = entityNumMax;
    }

    @Override
    public int getEntityNumMax() {
        return entityNumMax;
    }

    @Override
    public AABB getNewAABB() {
        return aabb;
    }

    @Override
    public int getNewLayer() {
        return newLayer;
    }

    @Override
    public Set<Long> getCurViewObj() {
        return Collections.unmodifiableSet(curViewObj);
    }

    @Override
    public void setCurViewObj(Set<Long> obj) {
        this.curViewObj = obj;
    }

    @Override
    public void addViewObj(long objId) {
        curViewObj.add(objId);
    }

    @Override
    public void removeViewObj(long objId) {
        curViewObj.remove(objId);
    }

    @Override
    public Set<Long> getObTargetSet() {
        return obTargetSet;
    }

    /**
     * 玩家登录场景
     */
    public void onPlayerLogin() {
        LOGGER.debug("{} login and reset aoi", getOwner());
        // 清理下视野
        clearPlayerView();
        // 把关注列表的下发了  自己的城 行军 参与的集结
        EntityNtfMsg.Builder msgBuilder = EntityNtfMsg.newBuilder();
        msgBuilder.setZoneId(getOwner().getScene().getZoneId());
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (long targetId : obTargetSet) {
            SceneObjEntity sceneObjEntity = objMgrComponent.getSceneObjEntity(targetId);
            if (sceneObjEntity == null) {
                LOGGER.error("{} ob {} not found", getOwner(), targetId);
                continue;
            }
            EntityAttrOuterClass.EntityAttr.Builder fullAttr = sceneObjEntity.getPropComponent().getFullAttr();
            msgBuilder.addNewEntities(fullAttr);
        }
        getOwner().sendMsgToClient(MsgType.ENTITYNTFMSG, msgBuilder.build());
    }

    /**
     * 下线了
     */
    public void onPlayerLogout() {
        LOGGER.debug("{} atScene logout, {} logout and reset aoi", LogKeyConstants.GAME_PLAYER_LOGIN, getOwner());
        clearPlayerView();
    }

    /**
     * 返回是否需要下发new
     */
    public boolean ob(SceneObjEntity target) {
        long targetId = target.getEntityId();
        if (obTargetSet.contains(targetId)) {
            return false;
        }
        obTargetSet.add(targetId);
        // 在当前的视野里面 删掉
        boolean remove = curViewObj.remove(targetId);
        if (!remove && getOwner().isThisSceneOnline()) {
            return true;
        }
        return false;
    }

    public void unOb(long targetId, boolean isDelete) {
        obTargetSet.remove(targetId);
        if (isDelete || !getOwner().isThisSceneOnline()) {
            return;
        }
        // 不是delete 的移除关注  如果在线 就加到当前看的列表里 等下回合刷
        curViewObj.add(targetId);
    }

    public void clearPlayerView() {
        for (AoiGrid aoiGrid : curAoiGrids) {
            aoiGrid.removeObserver(this, curLayer);
        }
        curAoiGrids.clear();
        curViewObj.clear();
        aabb = null;
    }

    /**
     * 清空玩家视野，重置aoi
     */
    public void clearPlayerView(boolean isChangeZone) {
        // 是changeZone 的 不用发delete
        if (isChangeZone) {
            clearPlayerView();
            return;
        }
        for (AoiGrid aoiGrid : curAoiGrids) {
            aoiGrid.removeObserver(this, curLayer);
        }
        curAoiGrids.clear();
        aabb = null;
        if (curViewObj.isEmpty()) {
            return;
        }
        EntityNtfMsg.Builder msgBuilder = EntityNtfMsg.newBuilder().setReason(SceneObjectNtfReason.SONR_AOI).addAllDelEntities(curViewObj);
        getOwner().sendMsgToClient(MsgType.ENTITYNTFMSG, msgBuilder.setZoneId(getOwner().getScene().getZoneId()).build());
        curViewObj.clear();
    }
}

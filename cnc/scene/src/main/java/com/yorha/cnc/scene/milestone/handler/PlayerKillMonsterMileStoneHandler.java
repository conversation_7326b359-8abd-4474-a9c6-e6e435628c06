package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.proto.CommonEnum;

/**
 * 个人特定建筑达到等级
 * 参数：建筑类型
 * 参数：等级
 */
public class PlayerKillMonsterMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_PLAYER;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_PLAYER_KILL_MONSTER;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }

}

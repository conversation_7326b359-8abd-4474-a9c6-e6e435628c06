package com.yorha.cnc.zone.activity;

import com.yorha.common.activity.BaseActivityUnit;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 作为BasePlayerActivityUnit在zone上的拓展
 */
public abstract class BaseZoneActivityUnit implements BaseActivityUnit {
    protected static final Logger LOGGER = LogManager.getLogger(BaseZoneActivityUnit.class);

    protected final ZoneActivity owner;
    protected final int activityId;
    protected final CommonEnum.ActivityUnitType unitType;

    /**
     * 这个zoneUnitId和玩家身上的unitId不是一个id，玩家身上的unit没有保证确定性，即同一个活动的一个排行unit，每个玩家身上的unitId可能是不一致的
     * <p>
     * zoneUnitId应当根据每个模块自己生成，建议使用配置id
     * <p>
     * 玩家侧的unitId设计有理解负担，为了减轻客户端接入的理解成本，建议不要给客户端引入zoneUnitId的概念
     */
    protected final int zoneUnitId;

    protected BaseZoneActivityUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        this.owner = owner;
        this.activityId = activityId;
        this.unitType = unitType;
        this.zoneUnitId = zoneUnitId;
    }

    /**
     * 过期淘汰unit，注销自身的一些逻辑
     */
    public abstract void onExpire();

    public abstract void forceOffImpl();

    @Override
    public void expire() {
        this.onExpire();
    }

    @Override
    public void forceOff() {
        this.forceOffImpl();
    }

    public BaseGameActor ownerActor() {
        return owner.ownerActor();
    }

    public int getZoneId() {
        return owner.owner().getZoneId();
    }
}

package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.army.PlayerOperationPreEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.PickUpInfoProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ArmyDetailState;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import com.yorha.proto.Core.Code;
import com.yorha.proto.SsPlayerScene;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncMapActionCollect;
import res.template.ConstTemplate;
import res.template.DropObjectTemplate;

/**
 * <AUTHOR>
 */
public class ArmyPickUpComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyPickUpComponent.class);
    private EventListener pickUpLoseListener;

    public ArmyPickUpComponent(ArmyEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        if (getOwner().isRallyArmy()) {
            return;
        }
        getOwner().getEventDispatcher().addEventListenerRepeat(this::checkCurPickUp, PlayerOperationPreEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onDead, DieEvent.class);
    }

    private PickUpInfoProp getPickUpProp() {
        return getOwner().getProp().getPickUpInfo();
    }

    private void sendErrorMsg(Code code) {
        getOwner().getScenePlayer().sendMsgToClient(
                MsgType.PLAYER_PLAYDIALOG_NTF,
                MsgHelper.buildErrorMsg(code)
        );
    }

    public long getDropEntityId() {
        return getPickUpProp().getDropEntityId();
    }

    /**
     * 死亡时结束拾取
     */
    private void onDead(DieEvent e) {
        long curDropObjId = getPickUpProp().getDropEntityId();
        if (curDropObjId == 0) {
            return;
        }
        LOGGER.info("{} onDead break pick. dropObjectId:{}", getOwner(), curDropObjId);
        pickBreak();
    }

    /**
     * 指令更改，主动打断， 检查当前的拾取情况
     */
    private void checkCurPickUp(PlayerOperationPreEvent e) {
        long curDropObjId = getPickUpProp().getDropEntityId();
        if (curDropObjId == 0) {
            return;
        }
        if (e.getArmyActionInfo().getTargetId() != curDropObjId) {
            LOGGER.info("{} checkCurPickUp break pick. change oeration. dropObjectId:{}", getOwner(), curDropObjId);
            getOwner().getMoveComponent().stopMove();
            pickBreak();
            sendErrorMsg(ErrorCode.PICK_CHANGE_TARGET.getCode());
        }
    }

    /**
     * 中断拾取
     */
    public void pickBreak() {
        DropObjectEntity dropObject = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(DropObjectEntity.class, getPickUpProp().getDropEntityId());
        if (dropObject != null) {
            dropObject.getPickUpComponent().pickUpBreak(getEntityId(), getOwner().getPlayerId());
        }
        clearPickUp();
        getOwner().getBehaviourComponent().refreshArmyState();
        getOwner().getStatusComponent().setStaying();
        getOwner().cancelSceneSchedule(SceneTimerReason.TIMER_PICK);
    }

    /**
     * 发起拾取
     */
    public void handlePickUp(long dropEntityId) {
        if (getPickUpProp().getDropEntityId() == dropEntityId) {
            throw new GeminiException(ErrorCode.DROP_OBJECT_IN_PICK_UP.getCodeId());
        }
        DropObjectEntity dropObject = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(DropObjectEntity.class, dropEntityId);
        if (dropObject == null || dropObject.isDestroy()) {
            throw new GeminiException(ErrorCode.DROP_OBJECT_ENTITY_CANT_PICK_UP.getCodeId());
        }
        // 掉落物本身限制
        dropObject.getPickUpComponent().startPickUpCheck(getEntityId(), getOwner().getPlayerId());
        // 拾取组 限制
        if (!getOwner().getScenePlayer().getPickUpComponent().checkCanPickUp(dropObject)) {
            throw new GeminiException(ErrorCode.DROP_OBJECT_PLAYER_PICK_LIMIT.getCodeId());
        }
        // 向目标掉落物移动
        getOwner().getMoveComponent().moveToTargetAsync(dropObject, TroopInteractionType.PICK_UP,
                () -> {
                    if (dropObject.getPickUpComponent().playerHasPicking(getOwner().getPlayerId())) {
                        getOwner().getScenePlayer().sendErrorCode(ErrorCode.DROP_OBJECT_PLAYER_CANT_REPEAT_PICK.getCodeId());
                        getOwner().getStatusComponent().setStaying();
                        return;
                    }
                    pickUpStart(dropObject);
                },
                (event) -> {
                    sendErrorMsg(ErrorCode.PICK_MOVE_TARGET_LOSE.getCode());
                    LOGGER.info("drop object lose,army {} break pick", getOwner());
                },
                (codeId) -> {
                    if (ErrorCode.isOK(codeId)) {
                        return;
                    }
                    getOwner().getScenePlayer().sendErrorCode(codeId);
                    getOwner().getStatusComponent().setStaying();
                });
    }

    /**
     * 拾取中
     */
    public boolean isPicking() {
        return getPickUpProp().getDropEntityId() != 0 && getPickUpProp().getEndTime() > SystemClock.now();
    }

    /**
     * 检测拾取是否结束
     */
    public void checkPickUpEnd() {
        if (getPickUpProp().getDropEntityId() == 0) {
            return;
        }
        DropObjectEntity dropObject = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(DropObjectEntity.class, getPickUpProp().getDropEntityId());
        clearPickUp();
        if (dropObject == null || dropObject.isDestroy()) {
            return;
        }
        // 拾取组 限制
        if (!getOwner().getScenePlayer().getPickUpComponent().checkCanPickUp(dropObject)) {
            sendErrorMsg(ErrorCode.DROP_OBJECT_PLAYER_PICK_LIMIT.getCode());
            return;
        }
        // 拾取掉落物二次校验
        ErrorCode errorCode = dropObject.getPickUpComponent().checkPickUp(getOwner().getPlayerId());
        if (errorCode.isNotOk()) {
            sendErrorMsg(errorCode.getCode());
            return;
        }
        DropObjectTemplate template = dropObject.getTemplate();
        if (template == null) {
            LOGGER.error("ArmyPickUpComponent checkPickUpEnd failed without template {} {}", getOwner(), dropObject);
            return;
        }
        if ((template.getCostItemsPairList() == null) || template.getCostItemsPairList().isEmpty()) {
            pickUpEnd(dropObject);
            return;
        }
        SsPlayerScene.ComsumeAssetAsk.Builder ask = SsPlayerScene.ComsumeAssetAsk.newBuilder();
        ask.setReason(CommonEnum.Reason.ICR_PICKUP);
        for (IntPairType pair : template.getCostItemsPairList()) {
            final Struct.YoAssetDesc.Builder desc = Struct.YoAssetDesc.newBuilder();
            desc.setType(1);
            desc.setId(pair.getKey());
            desc.setAmount(pair.getValue());
            ask.getPackageBuilder().getAssetsBuilder().addDatas(desc);
        }

        ownerActor().<SsPlayerScene.ComsumeAssetAns>askPlayer(getOwner().getZoneId(), getOwner().getPlayerId(), ask.build())
                .onComplete((ans, err) -> {
                    if (err != null) {
                        sendErrorMsg(ErrorCode.ITEM_NOT_ENOUGH.getCode());
                        LOGGER.error("ArmyPickUpComponent consumeItem failed {} {} {} ", getOwner(), dropObject, err);
                        return;
                    }
                    if (ans == null) {
                        sendErrorMsg(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCode());
                        LOGGER.error("ArmyPickUpComponent consumeItem failed ans is null {} {}", getOwner(), dropObject);
                        return;
                    }
                    if (!ans.getSuccess()) {
                        sendErrorMsg(ErrorCode.ITEM_NOT_ENOUGH.getCode());
                        LOGGER.info("ArmyPickUpComponent consumeItem failed {} {}", getOwner(), dropObject);
                        return;
                    }
                    pickUpEnd(dropObject);
                });
    }


    private void pickUpEnd(DropObjectEntity dropObject) {
        getOwner().getScenePlayer().getPickUpComponent().updatePickRecord(dropObject);
        dropObject.getPickUpComponent().pickUpFinished(getOwner());
        getOwner().getBattleComponent().afterBattleAction();
        // 发客户端通知
        getOwner().getScenePlayer().sendMsgToClient(
                MsgType.PLAYER_PLAYDIALOG_NTF,
                MsgHelper.buildClientShowMsg(
                        ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getDropObjectEndShow(),
                        getEntityId(),
                        dropObject.getTemplate().getId()
                )
        );
        if (getOwner().getScenePlayer() instanceof ScenePlayerEntity) {
            // 打qlog
            QlogCncMapActionCollect cncMapActionCollect = new QlogCncMapActionCollect();
            cncMapActionCollect.fillHead(((ScenePlayerEntity) getOwner().getScenePlayer()).getQlogComponent());
            cncMapActionCollect.setDtEventTime(TimeUtils.now2String())
                    .setAction("map_chest_collect")
                    .setMapChestType(dropObject.getTemplate().getOwnType())
                    .setUniqueMapChestID(dropObject.getEntityId())
                    .setMapChestID(dropObject.getTemplate().getId());
            cncMapActionCollect.sendToQlog();
        }
        LOGGER.info("ArmyPickUpComponent pickUpEnd dropObj {} {}", getOwner(), dropObject);
    }

    /**
     * 开始拾取
     */
    private void pickUpStart(DropObjectEntity dropObject) {
        getPickUpProp().setDropEntityId(dropObject.getEntityId()).setEndTime(SystemClock.now() + dropObject.getPickUpTime());
        getOwner().getBehaviourComponent().refreshArmyState();
        dropObject.getPickUpComponent().pickUpStart(getOwner());
        LOGGER.info("{} start pick {} endTime: {}", getOwner(), dropObject, TimeUtils.timeStampMs2String(getPickUpProp().getEndTime()));

        // 掉落物消失，中断拾取
        pickUpLoseListener = dropObject.getEventDispatcher().addEventListener(e -> {
            pickUpLoseListener = null;
            LOGGER.info("{} break pick. target lose. dropObjectId: {}", getOwner(), getPickUpProp().getDropEntityId());
            pickBreak();
            sendErrorMsg(ErrorCode.PICK_TARGET_LOSE.getCode());
        }, DeleteEvent.class);
        getOwner().getStatusComponent().setDetailState(ArmyDetailState.ADS_PICK_UP, dropObject, getPickUpProp().getEndTime());
        getOwner().addSceneSchedule(SceneTimerReason.TIMER_PICK, dropObject.getPickUpTime());
    }

    private void clearPickUp() {
        getPickUpProp().setDropEntityId(0).setEndTime(0);
        cancelListener();
    }

    private void cancelListener() {
        if (pickUpLoseListener != null) {
            pickUpLoseListener.cancel();
            pickUpLoseListener = null;
        }
    }
}

package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * gm按配置刷一波怪
 *
 * <AUTHOR>
 */
public class SpawnMonster implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
//        BigSceneEntity bigScene = actor.getBigScene();
//        bigScene.getObjMgrComponent().initMonsterRefresh(true);
    }

    @Override
    public String showHelp() {
        return "SpawnMonster playerId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MONSTER;
    }
}

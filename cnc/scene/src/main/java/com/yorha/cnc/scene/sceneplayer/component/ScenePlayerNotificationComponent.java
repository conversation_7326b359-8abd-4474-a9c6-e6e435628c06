package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.notification.NotificationBuilder;
import com.yorha.common.notification.NotificationTokenHelper;
import com.yorha.common.notification.PushNotificationManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.pushNotification.PushNotificationResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ScenePlayerPushNtfModelProp;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;

/**
 * 推送相关的component。
 *
 * <AUTHOR>
 */
public class ScenePlayerNotificationComponent extends AbstractComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerNotificationComponent.class);

    private final PushNotificationManager notificationManager = new PushNotificationManager();

    public ScenePlayerNotificationComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        // 已经初始化，更新缓存
        if (NotificationTokenHelper.isPushNtfMaskRight(this.getModel().getNotificationMask())) {
            NotificationTokenHelper.updateScenePlayerPushNtfModel(this.getEntityId(), this.getModel().getCopySsBuilder().build());
            return;
        }
        // 未初始化，使用配表中存在的数据初始化
        final List<Integer> openNtfModelIndexList = ResHolder.getResService(PushNotificationResService.class).getDefaultOpenNtfModeIdlList();
        this.refreshPushNtfMaskWithModelIdList(openNtfModelIndexList, Collections.emptyList(), "loadDefaultMaskFromConfig");
    }

    /**
     * 推送离线玩家的推送信息。
     *
     * @param ntf      推送消息。
     * @param playerId 玩家id。
     */
    public void pushOfflinePlayerSingleNotification(final NotificationBuilder ntf, final long playerId) {
        try {
            final SceneEntity scene = this.getOwner().getScene();
            if (!scene.isMainScene()) {
                LOGGER.debug("ScenePlayerNotificationComponent pushSingleNotification skip, not big scene, ntf={}, playerId={}", ntf.name(), playerId);
                return;
            }
            if (scene.getPlayerMgrComponent().isOnlinePlayer(playerId)) {
                LOGGER.debug("ScenePlayerNotificationComponent pushSingleNotification skip, player online, ntf={}, playerId={}", ntf.name(), playerId);
                return;
            }
            this.notificationManager.pushLocalSingleNotification(ntf, playerId);
        } catch (Exception e) {
            WechatLog.error("pushOfflinePlayerSingleNotification ntf={}, playerId={}, e=", ntf.name(), playerId);
        }
    }

    /**
     * 更新推送的token。
     *
     * @param token token。
     */
    public void refreshToken(final String token) {
        if (NotificationTokenHelper.isUselessClientToken(token)) {
            this.getModel().setIntlNtfToken(Constant.DEFAULT_STR_VALUE);
            this.getModel().setIntlTokenRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
            if (!this.getModel().hasAnyMark()) {
                return;
            }
            NotificationTokenHelper.updateScenePlayerPushNtfModel(this.getEntityId(), this.getModel().getCopySsBuilder().build());
            LOGGER.info("ScenePlayerNotificationComponent refreshToken playerId={}, token is empty", this.getEntityId());
            return;
        }
        this.getModel().setIntlNtfToken(token);
        this.getModel().setIntlTokenRefreshTsMs(SystemClock.nowNative());
        NotificationTokenHelper.updateScenePlayerPushNtfModel(this.getEntityId(), this.getModel().getCopySsBuilder().build());
        LOGGER.info("ScenePlayerNotificationComponent refreshToken, playerId={}, token={}", this.getEntityId(), token);
    }

    /**
     * 更新推送的language。
     * 注意：如果传入null or None，则会默认使用英语。
     *
     * @param language 新的语言。
     */
    public void refreshLanguage(final CommonEnum.Language language) {
        final CommonEnum.Language realLanguage;
        if (language == null || language == CommonEnum.Language.L_NONE) {
            realLanguage = CommonEnum.Language.en;
        } else {
            realLanguage = language;
        }
        if (realLanguage == this.getModel().getLanguage()) {
            return;
        }
        this.getModel().setLanguage(realLanguage);
        NotificationTokenHelper.updateScenePlayerPushNtfModel(this.getEntityId(), this.getModel().getCopySsBuilder().build());
        LOGGER.info("ScenePlayerNotificationComponent refreshLanguage, playerId={}, language={}, realLanguage={}", this.getEntityId(), language, realLanguage);
    }

    /**
     * 更新推送的状态。
     *
     * @param openNtfModelIdList 需要开启的推送模块序号列表。
     * @param reason             更新理由。
     */
    public void refreshPushNtfMaskWithModelIdList(final List<Integer> openNtfModelIdList, final List<Integer> closeNtfModelIdList, final String reason) {
        long prevMark = NotificationTokenHelper.getNotificationMask(this.getEntityId());
        if (!NotificationTokenHelper.isPushNtfMaskRight(prevMark)) {
            prevMark = NotificationTokenHelper.initNotificationMask();
        }
        long mask = NotificationTokenHelper.getPushNtfMaskFromPrevModel(prevMark, openNtfModelIdList, closeNtfModelIdList);
        this.getModel().setNotificationMask(mask);
        if (!this.getModel().hasAnyMark()) {
            return;
        }
        NotificationTokenHelper.updateScenePlayerPushNtfModel(this.getEntityId(), this.getModel().getCopyDbBuilder().build());
        LOGGER.info("ScenePlayerNotificationComponent refreshMask, playerId={}, openNtfModelIdList={}, closeNtfModelIdList={}, reason={}, mask={}",
                this.getEntityId(), openNtfModelIdList, closeNtfModelIdList, reason, mask);
    }

    private ScenePlayerPushNtfModelProp getModel() {
        return this.getOwner().getProp().getPushNtfModel();
    }
}

package com.yorha.cnc.scene.mapBuilding.component.stagenode.territory;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.mapbuilding.ChangeOccupierEvent;
import com.yorha.cnc.scene.event.mapbuilding.ChangeOwnerEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.CommonEnum.MailContentType;
import com.yorha.proto.CommonEnum.MarqueeType;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 占领中状态
 *
 * <AUTHOR>
 */
public class OccupyingStageNode extends StageNode {
    private static final Logger LOGGER = LogManager.getLogger(OccupyingStageNode.class);
    /**
     * 占领速度变化监听器
     */
    private EventListener listener;

    public OccupyingStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_OCCUPYING;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2OccupyFinished);
            listener = getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onOccupySpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
            return;
        }
        onOccupyFinished(getProp().getStateEndTsMs(), false);
    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        // 外面只设置了新的联盟id 然后就进来了
        long newClanId = getProp().getOccupyClanId();
        // 设置占领阶段数据
        SceneClanEntity occupySceneClan = getOccupyMgr().getOccupySceneClan();
        getOwner().copySceneClanAttr(occupySceneClan);
        getOwner().getProp().getTroop().clearTroop();
        // 加入占领中列表
        occupySceneClan.getMapBuildingComponent().addToOccupying(getOwner());
        // 检测首占奖励
        checkAndSendFirstOccupy(ts, occupySceneClan);
        // 给原先的拥有方发个被打下来的通知和邮件
        sendBeAttackSucceed();
        // 触发切换占领者
        getOwner().getEventDispatcher().dispatch(new ChangeOccupierEvent(getEntityId(), newClanId, CommonEnum.Camp.C_NONE));
        // 发个qlog
        getOwner().getQLogComponent().sendMapBuildLog(newClanId, "occupy", getOwner().getQLogComponent().getInnerPlayer());
        // 设置属性
        getProp().setState(getStage()).setStateStartTsMs(ts).setOccupyTsMs(ts).setOccupyNum(0);
        getOwner().getProp().getRecommendSoldierTypeList().clear();

        // 看下需不需要占领中阶段
        if (getTemplate().getOccupyTime() != 0) {
            resetOccupySpeed();
            return;
        }
        // 走占领完成的流程
        onOccupyFinished(ts, false);
    }

    /**
     * 城内军队数据变化，更新占领速度
     */
    private void onOccupySpeedChange(IEvent e) {
        getComponent().cancelStageTimer();
        long now = SystemClock.now();
        // 结算之前的占领进度
        long addProgress = (now - getProp().getOccupyNumCalcTsMs()) / 1000 * getProp().getOccupySpeed();
        getProp().setOccupyNum((int) (getProp().getOccupyNum() + addProgress));
        LOGGER.info("{} onOccupySpeedChange add:{} now:{}", getOwner(), getProp().getOccupyNum(), addProgress);
        // 更新占领数据
        resetOccupySpeed();
    }

    /**
     * 重设占领数据
     */
    private void resetOccupySpeed() {
        // check占领进度
        TerritoryBuildingTemplate template = getOwner().getTerritoryBuildingTemplate();
        // 占领时间可能为0
        int maxOccupyTime = template.getOccupyTime();
        long now = SystemClock.now();
        if (getProp().getOccupyNum() >= maxOccupyTime) {
            onOccupyFinished(now, false);
            return;
        }
        // 计算占领结束时间戳
        int newSpeed = (int) SceneAddCalc.getOccupySpeed(getOwner(), getOccupyMgr().getOccupySceneClan());
        long deltaTime = (maxOccupyTime - getProp().getOccupyNum()) / newSpeed;
        if (deltaTime <= 0) {
            onOccupyFinished(now, false);
            return;
        }
        getProp().setOccupyNumCalcTsMs(now).setOccupySpeed(newSpeed).setStateEndTsMs(now + TimeUtils.second2Ms(deltaTime));
        // 增加进入保护期的定时器
        addStageTimer(this::time2OccupyFinished, deltaTime, TimeUnit.SECONDS);
        if (listener == null) {
            listener = getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onOccupySpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
        }
        LOGGER.info("{} resetOccupySpeed. newSpeed:{} deltaTime:{}", getOwner(), newSpeed, deltaTime);
    }

    /**
     * 给原先的拥有方发个被打下来的通知和邮件
     */
    private void sendBeAttackSucceed() {
        long ownerClanId = getProp().getOwnerClanId();
        if (ownerClanId == 0) {
            return;
        }
        SceneClanEntity ownerSceneClan = getOwner().getBuildComponent().getOwnerSceneClan();
        // 通知所属者 转到被占领
        getOccupyMgr().getOwnerSceneClan().getMapBuildingComponent().addToBeOccupy(getEntityId(), false);
        // 只有城市才需要发邮件
        if (!GameLogicConstants.isMapCity(getOwner().getAreaType())) {
            return;
        }
        ConstClanTerritoryTemplate consts = ResHolder.getConsts(ConstClanTerritoryTemplate.class);
        StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder();
        mail.setMailTemplateId(consts.getBuildingOccupiedBeginByEnemy());
        // 副标题
        mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getOccupyinfo().getShowClanSimpleName()));
        // 正文参数设置
        mail.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        Struct.DisplayData.Builder contentData = mail.getContentBuilder().getDisplayDataBuilder();
        contentData.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()))
                .addDatas(MsgHelper.buildGotoDisplayPoint(getOwner().getCurPoint(), this.getOwner().getScene().getMapType(), this.getOwner().getScene().getMapIdForPoint()));
        MailUtil.sendClanMail(ownerSceneClan.getZoneId(), ownerClanId, mail.build());
    }

    private void time2OccupyFinished() {
        getComponent().clearStageTimer();
        onOccupyFinished(SystemClock.now(), false);
    }

    /**
     * 占领完成
     */
    public void onOccupyFinished(long finishOccupyTsMs, boolean isDirectOwner) {
        getComponent().clearStageTimer();
        LOGGER.info("{} occupy fished.", getOwner());
        // 判定下是不是能占下来
        SceneClanEntity occupySceneClan = getOccupyMgr().getOccupySceneClan();
        if (!occupySceneClan.getMapBuildingComponent().isCanRetake(getOwner())) {
            LOGGER.info("{} clan territory full", getOwner());
            // 不能的话要回归中立
            getComponent().transNeutralStage();
            return;
        }

        // 占领成功 设置所属者属性
        setOwnerClan(finishOccupyTsMs, isDirectOwner);
        // 先看下有没有坚守阶段
        if (getTemplate().getStickTime() != 0) {
            getComponent().transNewNode(new StickStageNode(getOwner()), finishOccupyTsMs);
            return;
        }
        // 再看下有没有保护阶段
        if (getOwner().getTerritoryBuildingTemplate().getProtectTime() != 0) {
            getComponent().transNewNode(new ProtectStageNode(getOwner()), finishOccupyTsMs);
            return;
        }
        // 看下有没有在指挥网中
        if (getOwner().getProp().getConstructInfo().getIsConnectedToCommandNet()) {
            getComponent().transNewNode(new InCommandNetStageNode(getOwner()), finishOccupyTsMs);
            return;
        }
        getComponent().transNewNode(new DesertedStageNode(getOwner()), finishOccupyTsMs);
    }

    /**
     * 占领成功 设置所属者
     *
     * @param finishOccupyTsMs 占领完成的时间
     */
    private void setOwnerClan(long finishOccupyTsMs, boolean isDirectOwner) {
        // 设置占领联盟相关属性,  做些操作（指挥网 罩子）
        long clanId = getProp().getOccupyClanId();

        // 发送占领邮件
        if (GameLogicConstants.isMapCity(getOwner().getAreaType())) {
            sendOccupyFinishMail();
        }
        // 结算下占领积分
        getOwner().getInnerArmyComponent().addAllPlayerClanOccupyScore(getProp().getStateEndTsMs(), getProp().getStateStartTsMs());
        // 原归属者移除
        SceneClanEntity oldOwner = getOwnerSceneClan();
        if (oldOwner != null) {
            oldOwner.getMapBuildingComponent().removeMapBuilding(getEntityId());
        }
        // 设置拥有者字段
        getProp().setOwnerOccupyTsMs(finishOccupyTsMs).setOwnerClanId(clanId).setOccupyClanId(0);
        // 加入拥有列表
        SceneClanEntity newOwner = getOwnerSceneClan();
        newOwner.getMapBuildingComponent().addToOwner(getOwner(), false);

        // 发个跑马灯和特效
        if (GameLogicConstants.isMapCity(getOwner().getAreaType())) {
            sendCityOwnEffect(newOwner);
        }
        // 发送qLog
        getOwner().getQLogComponent().sendMapBuildLog(clanId, "hold", getOwner().getQLogComponent().getInnerPlayer());
        // 发送军团日志，如果是首次建设军团基地时占领的据点建筑则跳过
        if (!isDirectOwner) {
            getOwner().getClanLogComponent().recordOccupyLog(getOwner().getTemplateId(), getOwner().getProp().getOccupyinfo().getLastHitPlayerName(), clanId, getOwner().getCurPoint());
        }
        // 进入保护期 算目标丢失
        getOwner().getEventDispatcher().dispatch(new ChangeOwnerEvent());
    }

    /**
     * 检查并发送首占奖励
     */
    public void checkAndSendFirstOccupy(long ts, SceneClanEntity sceneClan) {
        if (getProp().getFisrtOwnTsMs() != 0) {
            return;
        }
        getProp().setFisrtOwnTsMs(ts);
        int firstOwnMailId = getOwner().getTerritoryBuildingTemplate().getFirstOwnMailId();
        if (firstOwnMailId == 0) {
            return;
        }
        // 发个奖励邮件
        StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder();
        mail.setMailTemplateId(firstOwnMailId);
        MailUtil.sendClanMail(sceneClan.getZoneId(), sceneClan.getClanId(), mail.build());
    }

    /**
     * 发送占领完成和被占领邮件
     */
    private void sendOccupyFinishMail() {
        ConstClanTerritoryTemplate consts = ResHolder.getConsts(ConstClanTerritoryTemplate.class);
        // 占领方
        StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder();
        mail.setMailTemplateId(consts.getBuildingOccupationSuccess());
        // 副标题
        mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()));
        // 正文参数设置
        mail.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        Struct.DisplayData.Builder contentData = mail.getContentBuilder().getDisplayDataBuilder();
        contentData.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()))
                .addDatas(MsgHelper.buildGotoDisplayPoint(getOwner().getCurPoint(), this.getOwner().getScene().getMapType(), this.getOwner().getScene().getMapIdForPoint()));
        MailUtil.sendClanMail(getOccupySceneClan().getZoneId(), getProp().getOccupyClanId(), mail.build());
        // 被占领方
        if (getProp().getOwnerClanId() != 0) {
            StructMail.MailSendParams.Builder mai2 = StructMail.MailSendParams.newBuilder();
            mai2.setMailTemplateId(consts.getBuildingOccupiedByEnemy());
            // 副标题
            mai2.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()))
                    .addDatas(MsgHelper.buildDisPlayText(getOwner().getProp().getOccupyinfo().getShowClanSimpleName()));
            mai2.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
            Struct.DisplayData.Builder contentData2 = mai2.getContentBuilder().getDisplayDataBuilder();
            contentData2.getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()))
                    .addDatas(MsgHelper.buildGotoDisplayPoint(getOwner().getCurPoint(), this.getOwner().getScene().getMapType(), this.getOwner().getScene().getMapIdForPoint()));
            MailUtil.sendClanMail(getOwnerSceneClan().getZoneId(), getProp().getOwnerClanId(), mai2.build());
        }
    }

    /**
     * 城市占领 特效、跑马灯
     */
    private void sendCityOwnEffect(SceneClanEntity clan) {
        // 广播下特效
        PlayerScene.Player_PlayDialog_NTF.Builder builder = PlayerScene.Player_PlayDialog_NTF.newBuilder();
        builder.setId(GameLogicConstants.CITY_OCCUPY_SUCCEED_SHOW_ID);
        Point curPoint = getOwner().getCurPoint();
        builder.getParamsBuilder().setEntityId(getEntityId()).getPosBuilder().setX(curPoint.getX()).setY(curPoint.getY());
        builder.getParamsBuilder().getDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayTextPb(clan.getClanSimpleName()))
                .addDatas(MsgHelper.buildDisPlayTextPb(clan.getClanName()))
                .addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()));
        clan.getMemberComponent().broadcastOnlineClientMsg(MsgType.PLAYER_PLAYDIALOG_NTF, builder.build());

        // 跑马灯
        final int marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(MarqueeType.OCCUPY_MAPBUILDING_SUCCEED);
        StructPB.DisplayDataPB.Builder marqueeBuilder = StructPB.DisplayDataPB.newBuilder();
        marqueeBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(clan.getClanSimpleName()));
        marqueeBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayPointPb(
                getOwner().getCurPoint().getX(),
                getOwner().getCurPoint().getY(),
                this.getOwner().getScene().getMapType(),
                this.getOwner().getScene().getMapIdForPoint()));
        marqueeBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()));
        getOwner().getScene().getMarqueeComponent().sendSceneMarquee(marqueeId, marqueeBuilder.build(), null);
    }

    @Override
    public void onLeave() {
        super.onLeave();
        if (listener != null) {
            listener.cancel();
            listener = null;
        }
    }

    @Override
    public String toString() {
        return ClassNameCacheUtils.getSimpleName(getClass())
                + " ownerClanId=" + getProp().getOwnerClanId()
                + " occupyClanId=" + getProp().getOccupyClanId()
                ;
    }
}

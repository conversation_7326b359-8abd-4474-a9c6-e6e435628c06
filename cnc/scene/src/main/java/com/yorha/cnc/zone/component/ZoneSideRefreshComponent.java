package com.yorha.cnc.zone.component;

import com.yorha.cnc.zone.IZone;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ZoneSideRefreshModelProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

public class ZoneSideRefreshComponent extends AbstractComponent {
    private static final Logger LOGGER = LogManager.getLogger(ZoneSideRefreshComponent.class);
    private final IZone iZone;
    private boolean dayTask;
    private boolean weekTask;

    public ZoneSideRefreshComponent(IZone iZone, AbstractEntity entity) {
        super(entity);
        this.iZone = iZone;
    }

    public IZone getIZone() {
        return iZone;
    }

    public ZoneSideRefreshModelProp getProp() {
        return getIZone().getZoneSideProp().getRefreshModel();
    }

    public void initActivityRefresh() {
        if (TimeUtils.isSameDayWithNow(getProp().getDayRefreshTsMs())) {
            addDayRefreshTimer();
        } else {
            dispatchDayRefresh();
        }
        if (TimeUtils.isSameWeekWithNow(getProp().getWeekRefreshTsMs())) {
            addWeekRefreshTimer();
        } else {
            dispatchWeekRefresh();
        }
    }

    private void addDayRefreshTimer() {
        if (dayTask) {
            LOGGER.error("{} addDayRefreshTimer repeat", getOwner());
            return;
        }
        long initialDelay = TimeUtils.getNextDayDurMs(SystemClock.now()) + RandomUtils.nextInt(5000);
        getIZone().getTimerComponent().addTimer(
                TimerReasonType.ZONE_SIDE_DAY_REFRESH,
                this::dispatchDayRefresh,
                initialDelay, TimeUnit.MILLISECONDS);
        dayTask = true;
    }

    private void addWeekRefreshTimer() {
        if (weekTask) {
            LOGGER.error("{} addWeekRefreshTimer repeat", getOwner());
            return;
        }
        long nextWeekDurMs = TimeUtils.getNextWeekDurMs(SystemClock.now()) + RandomUtils.nextInt(5000);
        getIZone().getTimerComponent().addTimer(
                TimerReasonType.ZONE_SIDE_WEEK_REFRESH,
                this::dispatchWeekRefresh,
                nextWeekDurMs, TimeUnit.MILLISECONDS);
        weekTask = true;
    }

    private void dispatchDayRefresh() {
        dayTask = false;
        getProp().setDayRefreshTsMs(SystemClock.now());
        addDayRefreshTimer();

        getIZone().getSideActivityComponent().onDayRefresh();
    }

    private void dispatchWeekRefresh() {
        weekTask = false;
        getProp().setWeekRefreshTsMs(SystemClock.now());
        addWeekRefreshTimer();
    }
}
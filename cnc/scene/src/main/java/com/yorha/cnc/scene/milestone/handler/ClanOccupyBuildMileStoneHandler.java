package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.ClanOccupyBuildData;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.common.constant.Constants;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 每个军团占领某类领土建筑的数量
 * 参数：要求的领土数量_要求的领土建筑类型
 */
public class ClanOccupyBuildMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_CLAN;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_OCCUPIED_BUILDINGS_NUM;
    }


    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanOccupyBuildData) {
            ClanOccupyBuildData data = (ClanOccupyBuildData) taskData;
            LOGGER.info("ClanOccupyBuildMileStoneHandler recordRankData, milestoneId={} type={} level={} oldClanId={} newClanId={}", getProp().getMileStoneId(), data.getBuildType(), data.getBuildLevel(), data.getOldClanId(), data.getClanId());
            if (!matchParam(data)) {
                return;
            }
            Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
            MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.computeIfAbsent(data.getClanId(), (key) -> {
                MileStoneClanInfoProp prop = new MileStoneClanInfoProp();
                prop.setClanId(key);
                return prop;
            });
            LOGGER.info("ClanOccupyBuildMileStoneHandler recordRankData, add success, milestoneId={} clanId={} oldScore={}", getProp().getMileStoneId(), data.getClanId(), mileStoneClanInfoProp.getScore());
            setClanScore(mileStoneClanInfoProp, data.getClanId(), mileStoneClanInfoProp.getScore() + 1, "onMapBuildOccupyByClan");
            // 被占领的需要扣除积分
            if (data.getOldClanId() > 0 && rankInfoMap.containsKey(data.getOldClanId())) {
                MileStoneClanInfoProp oldClanProp = rankInfoMap.get(data.getOldClanId());
                LOGGER.info("ClanOccupyBuildMileStoneHandler recordRankData, ded update success, milestoneId={} clanId={} oldScore={}", getProp().getMileStoneId(), data.getOldClanId(), oldClanProp.getScore());
                setClanScore(oldClanProp, data.getOldClanId(), Math.max(0, oldClanProp.getScore() - 1), "onMapBuildBeOccupyByClan");
            }
        }
    }

    private boolean matchParam(ClanOccupyBuildData data) {
        String[] taskParam = getTaskParamById();
        String[] split = taskParam[1].split(Constants.BAN_JIAO_DOU_HAO);
        int level = Integer.parseInt(taskParam[2]);
        for (String buildTypeConfig : split) {
            int buildType = Integer.parseInt(buildTypeConfig);
            if (buildType == data.getBuildType() && (level == 0 || level == data.getBuildLevel())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 满足积分
     */
    @Override
    public List<MileStoneClanInfoProp> filterMeetScore(List<MileStoneClanInfoProp> rankProp) {
        String[] taskParam = getTaskParamById();
        if (taskParam.length < 1) {
            LOGGER.error("ClanOccupyBuildMileStoneHandler param error. taskParam={} prop={}", taskParam, getProp());
        }
        return rankProp.stream().filter(it -> it.getScore() >= Integer.parseInt(taskParam[0])).collect(Collectors.toList());
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }
}

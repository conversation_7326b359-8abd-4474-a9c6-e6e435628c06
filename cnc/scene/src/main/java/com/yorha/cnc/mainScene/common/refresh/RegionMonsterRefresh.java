package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class RegionMonsterRefresh {
    private static final Logger LOGGER = LogManager.getLogger(RegionMonsterRefresh.class);

    private final int templateId;
    private final int regionId;
    private final CommonEnum.MapAreaType areaType;
    private final long lifeTime;
    private final Map<Integer, MutableInt> monsterCount;
    private final SceneEntity owner;

    public RegionMonsterRefresh(SceneEntity owner, int templateId, int regionId, CommonEnum.MapAreaType areaType, long lifeTime, Map<Integer, MutableInt> monsterCount) {
        this.templateId = templateId;
        this.regionId = regionId;
        this.areaType = areaType;
        this.lifeTime = lifeTime;
        this.monsterCount = monsterCount;
        this.owner = owner;
    }

    public int run() {
        int cnt = 0;
        SceneObjSpawnParam param = new SceneObjSpawnParam();
        if (lifeTime > 0) {
            param.setLifeTime(lifeTime);
        }
        for (Map.Entry<Integer, MutableInt> next : monsterCount.entrySet()) {
            int templateId = next.getKey();
            for (int i = 0; i < next.getValue().getValue(); i++) {
                try {
                    Point point = MonsterFactory.randomBornPoint(owner, templateId, regionId, areaType);
                    if (point == null) {
                        LOGGER.warn("big scene spawn monster random born point fail:{}", templateId);
                        continue;
                    }
                    MonsterEntity monster = MonsterFactory.initMonster(owner, templateId, point, param);
                    if (monster == null) {
                        continue;
                    }
                    monster.addIntoScene();
                } catch (Exception e) {
                    LOGGER.error("RegionMonsterRefresh {} but ", this, e);
                }
                cnt++;
            }
        }
        return cnt;
    }

    public int getTemplateId() {
        return templateId;
    }

    @Override
    public String toString() {
        return "RegionMonsterRefresh{" +
                "templateId=" + templateId +
                ", regionId=" + regionId +
                ", monsterCount=" + monsterCount +
                '}';
    }
}
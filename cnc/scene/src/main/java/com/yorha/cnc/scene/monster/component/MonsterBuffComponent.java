package com.yorha.cnc.scene.monster.component;

import com.yorha.game.gen.prop.Int32BuffMapProp;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;

/**
 * <AUTHOR>
 */
public class MonsterBuffComponent extends SceneObjBuffComponent {
    public MonsterBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuff();
    }

    @Override
    public MonsterEntity getOwner() {
        return (MonsterEntity) super.getOwner();
    }
}

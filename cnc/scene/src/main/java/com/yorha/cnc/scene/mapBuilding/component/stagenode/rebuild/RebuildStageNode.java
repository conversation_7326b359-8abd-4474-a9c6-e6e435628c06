package com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.mapbuilding.TemplateChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.WorldLayerService;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.OccupyState;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 改建中状态
 *
 * <AUTHOR>
 */
public class RebuildStageNode extends ClanBuildingNode {
    private static final Logger LOGGER = LogManager.getLogger(RebuildStageNode.class);
    private EventListener listener;

    public RebuildStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_REBUILD;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2RebuildFinished);
            listener = getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onRebuildSpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
            return;
        }
        onRebuildFinished(getProp().getStateEndTsMs());
    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        getProp().setState(getStage()).setStateStartTsMs(SystemClock.now()).setRebuildNum(0);
        // 加入重建中列表
        SceneClanEntity ownerSceneClan = getOccupyMgr().getOwnerSceneClan();
        ownerSceneClan.getBuildComponent().addToRebuilding(getOwner());
        // 发送开始建造邮件
        int mailId = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class).getStartBuildMail();
        sendStartBuildMail(mailId);
        // 计算速度  增加到下阶段定时器  这里可能直接改建完成了。。。。
        resetRebuildSpeed();
        // change下寻路阻挡
        getOwner().getTransformComponent().changeCollision();
        // 重置模型圈
        getOwner().getTransformComponent().resetModelRadius();
        // 触发id改变事件
        getOwner().getEventDispatcher().dispatch(new TemplateChangeEvent(getEntityId()));
        // 通知视野层级变化
        int oldLayer = ResHolder.getResService(WorldLayerService.class).getObjectLayer(SceneObjectEnum.SOE_STRONGHOLD);
        getOwner().getAoiNodeComponent().onChangeLayer(oldLayer, getOwner().getScene().getMaxSceneLayer(getOwner()));
    }

    /**
     * 城内军队数据变化或加成变化，更新重建速度
     */
    public void onRebuildSpeedChange(IEvent e) {
        getComponent().cancelStageTimer();
        long now = SystemClock.now();
        // 结算之前的改建进度
        long addProgress = (now - getProp().getRebuildNumCalcTsMs()) / 1000 * getProp().getRebuildSpeed();
        getProp().setRebuildNum((int) (getProp().getRebuildNum() + addProgress));
        LOGGER.info("{} onRebuildSpeedChange add:{} now:{}", getOwner(), getProp().getRebuildNum(), addProgress);
        // 更新改建速度
        resetRebuildSpeed();
    }

    /**
     * 重置重建速度
     */
    private void resetRebuildSpeed() {
        // check重建进度
        int totalWorks = getProp().getRebuildTotalWork();
        long now = SystemClock.now();
        if (getProp().getRebuildNum() >= totalWorks) {
            onRebuildFinished(now);
            return;
        }
        // 计算重建结束时间戳
        int newSpeed = (int) SceneAddCalc.getRebuildSpeed(getOwner(), getOccupyMgr().getOwnerSceneClan());
        long deltaTime = (totalWorks - getProp().getRebuildNum()) / newSpeed;
        if (deltaTime <= 0) {
            onRebuildFinished(now);
            return;
        }
        getProp().setRebuildNumCalcTsMs(now).setRebuildSpeed(newSpeed).setStateEndTsMs(now + TimeUtils.second2Ms(deltaTime));
        // 增加进入重建结束状态的定时器
        addStageTimer(this::time2RebuildFinished, deltaTime, TimeUnit.SECONDS);
        if (listener == null) {
            listener = getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onRebuildSpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
        }
        LOGGER.info("{} resetRebuildSpeed. newSpeed:{} deltaTime:{}", getOwner(), newSpeed, deltaTime);
    }

    private void time2RebuildFinished() {
        getComponent().clearStageTimer();
        onRebuildFinished(SystemClock.now());
    }

    public void onRebuildFinished(long ts) {
        try {
            // 为所有建筑内部队计算积分
            getOwner().getInnerArmyComponent().addAllPlayerClanRebuildScore(getProp().getStateEndTsMs(), getProp().getStateStartTsMs());
        } catch (Exception e) {
            LOGGER.error("{} onRebuildFinished cal error ", getOwner(), e);
        }
        // 设置改建相关属性
        onBuildFinish();
        // 进入新的阶段
        getComponent().transNewNode(new AfterRebuildStageNode(getOwner()), ts);
    }

    /**
     * 改建完成  设置耐久， 通知联盟
     */
    private void onBuildFinish() {
        // 设置正常的耐久度
        TerritoryBuildingTemplate template = getOwner().getClanBuildingTemplate();
        // 联盟建筑耐久度
        int maxHp = (int) SceneAddCalc.getMaxHp(getOwner().getBuildComponent().getType(), template.getMaxHp(), getOccupyMgr().getOwnerSceneClan());
        getConstructInfoProp().setMaxDurability(maxHp).setCurrentDurability(maxHp);
        // 添加到已建设好列表
        SceneClanEntity ownerClan = getOwnerSceneClan();
        ownerClan.getBuildComponent().addToAlreadyRebuilt(getOwner());
        // 发送修建成功邮件
        sendFinishBuildMail();
        // 创建军团日志
        ownerClan.getLogComponent().recordCreateRebuildFinishLog(getConstructInfoProp().getStartPlayerName(),
                getOwner().getProp().getTemplateId(), getOwner().getCurPoint());
        // 打个Qlog
        getOwner().getQLogComponent().sendExpansionLog(ownerClan.getEntityId(), "build_complete", 0);
        // 里程碑埋点-联盟建筑完成
        if (getOwner().getScene().getMileStoneOrNullComponent() != null) {
            getOwner().getScene().getMileStoneOrNullComponent().onClanMapBuildFin(getOwner().getZoneId(), getOwner().getBuildingTemplate().getType().getNumber(), getOwner().getLevel(), getProp().getOwnerClanId());
        }
    }

    @Override
    public void onDestroyBuilding(boolean isActive, boolean needChangeStage) {
        LOGGER.info("{} stopRebuild. isActive:{}", getOwner(), isActive);
        // 建造中的联盟建筑拆除时，需要给所有在建筑内的军队添加积分
        getOwner().getInnerArmyComponent().addAllPlayerClanRebuildScore(SystemClock.now(), getProp().getStateStartTsMs());
        // 通知当前联盟停止建造或拆除
        getOwnerSceneClan().getBuildComponent().stopRebuilding(getEntityId());
        if (isActive) {
            // 发送主动拆除邮件
            sendRebuildMail(getConstClanTerritoryTemplate().getCancelBuildMail(),
                    getOwnerSceneClan().getEntityId(), getConstructInfoProp().getStartStaffId());
        } else {
            // 发送被动拆除邮件
            sendRebuildMail(getConstClanTerritoryTemplate().getAttackWhenBuildMail(), getOwnerSceneClan().getEntityId());
        }
        // 处理城内援军 属性  进入下一个阶段
        afterDestroy(needChangeStage);
    }

    @Override
    public void onAttackSuccess(long clanId) {
        // 停止联盟建筑建设
        onDestroyBuilding(false, true);
    }

    @Override
    protected void afterRefreshMaxHp(int oldHp, int newHp) {

    }

    @Override
    public void onLeave() {
        super.onLeave();
        if (listener != null) {
            listener.cancel();
            listener = null;
        }
    }
}


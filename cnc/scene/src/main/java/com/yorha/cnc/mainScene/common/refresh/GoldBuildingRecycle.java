package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneResRegionItem;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.resBuilding.enums.RefreshType;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.utils.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 黄金矿回收任务
 *
 * <AUTHOR>
 */
public class GoldBuildingRecycle extends RefreshTask {
    private static final Logger LOGGER = LogManager.getLogger(GoldBuildingRecycle.class);

    public final RefreshType refreshType;
    private final int regionId;

    public GoldBuildingRecycle(SceneEntity owner, int regionId, RefreshType refreshType) {
        super(owner);
        this.regionId = regionId;
        this.refreshType = refreshType;
    }

    @Override
    public int getHandleNumPerTick() {
        return GameLogicConstants.RECYCLE_MAX_PER_SECOND;
    }

    @Override
    public Pair<Boolean, Integer> run() {
        if (!(getOwner().getResMgrComponent() instanceof MainSceneResMgrComponent)) {
            return Pair.of(true, 0);
        }
        final MainSceneResMgrComponent mainSceneResMgrComponent = (MainSceneResMgrComponent) getOwner().getResMgrComponent();
        MainSceneResRegionItem mgrItem = mainSceneResMgrComponent.getResRegionItem(regionId);
        if (mgrItem == null) {
            LOGGER.error("GoldBuildingRecycle but regionItem not exist {} {}", regionId, refreshType);
            return Pair.of(true, 0);
        }
        return mgrItem.recycleGoldBuilding(refreshType, getHandleNumPerTick());
    }

    @Override
    public String toString() {
        return "GoldBuildingRecycle{" +
                "refreshType=" + refreshType +
                ", regionId=" + regionId +
                "}";
    }
}

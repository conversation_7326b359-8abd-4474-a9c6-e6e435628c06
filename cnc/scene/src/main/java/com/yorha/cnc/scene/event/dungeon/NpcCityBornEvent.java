package com.yorha.cnc.scene.event.dungeon;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class NpcCityBornEvent extends IEvent {
    private final CityEntity cityEntity;

    public NpcCityBornEvent(CityEntity cityEntity) {
        this.cityEntity = cityEntity;
    }

    public CityEntity getCityEntity() {
        return cityEntity;
    }
}

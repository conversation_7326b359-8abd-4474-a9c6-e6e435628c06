package com.yorha.cnc.zone.zone.activity;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;

/**
 * 领土保护活动效果
 *
 * <AUTHOR>
 */
public class LandProtectionEffect extends IActivityEffect {

    @Override
    protected void effectOnStart(ZoneEntity owner) {
        for (CityEntity cityEntity : owner.getBigScene().getObjMgrComponent().getObjsByType(CityEntity.class)) {
            cityEntity.getSpecialSafeGuardComponent().onLandProtectionActivityStart();
        }
    }

    @Override
    protected void effectOnExpire(ZoneEntity owner) {
        for (CityEntity cityEntity : owner.getBigScene().getObjMgrComponent().getObjsByType(CityEntity.class)) {
            cityEntity.getSpecialSafeGuardComponent().onLandProtectionActivityEnd();
        }
    }
}

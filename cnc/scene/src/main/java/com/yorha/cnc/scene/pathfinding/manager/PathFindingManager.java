package com.yorha.cnc.scene.pathfinding.manager;

import com.yorha.cnc.scene.pathfinding.PathFindingHelper;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.navmesh.GeminiNav;
import com.yorha.proto.SsPathFinding.SearchPathAsyncAns;
import com.yorha.proto.SsPathFinding.SearchPathAsyncAsk;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 普普通通的单个寻路管理器  本地使用
 *
 * <AUTHOR>
 */
public class PathFindingManager {
    private static final Logger LOGGER = LogManager.getLogger(PathFindingManager.class);
    /**
     * 地图id
     */
    private final int mapId;
    /**
     * 大世界  州id->nav
     * 副本    mapId->nav
     */
    protected final Map<Integer, GeminiNav> navMap = new HashMap<>();

    public PathFindingManager(int mapId) {
        this.mapId = mapId;
        initNav();
    }

    public int getMapId() {
        return mapId;
    }

    /**
     * 初始化副本地图数据
     */
    protected void initNav() {
        String navmeshFileName = ResHolder.getResService(SceneMapDataTemplateService.class).getNavmeshFileName(mapId);
        try {
            GeminiNav nav = new GeminiNav();
            // 普通的单个寻路管理器是用生成id 会释放的 只有会销毁的使用这个
            nav.init(PathFindingHelper.genNavId(), navmeshFileName);
            navMap.put(mapId, nav);
        } catch (Exception e) {
            WechatLog.error("load from navmesh:{} fail", navmeshFileName, e);
        }
        LOGGER.info("load from navmesh:{} success", navmeshFileName);
    }

    public SearchPathAsyncAns searchPathAsync(SearchPathAsyncAsk msg) {
        // 不用于异步 未实现
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    public List<Point> findNormalPath(long entityId, Point src, Point end, int searchTag, GeminiStopWatch watch) {
        if (GameLogicConstants.ignoreStatic(searchTag)) {
            List<Point> pointDataList = new ArrayList<>();
            pointDataList.add(src);
            pointDataList.add(end);
            return pointDataList;
        }
        List<Point> path = findNavPath(src, end);
        watch.mark("nav find path");
        if (path.size() < 2) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("%s %s %s", entityId, src, end));
        }
        if (path.size() > 2) {
            path = simplePath(path);
            watch.mark("simple path");
        }
        return path;
    }

    public List<Point> findNavPath(Point src, Point end) {
        return NavmeshApi.searchPath(getPointNav(src), src, end);
    }

    public boolean isNoCollision(Point src, Point end) {
        return NavmeshApi.isNoCollision(getPointNav(src), src, end);
    }

    public Point findNearestPointRayCast(Point src, Point end) {
        return NavmeshApi.findNearestPointRayCast(getPointNav(src), src, end);
    }

    public boolean isPointNavWalkable(Point p) {
        return NavmeshApi.isPointWalkable(getPointNav(p), p);
    }

    public Point findNearestWalkablePoint(Point p) {
        return NavmeshApi.findNearestWalkablePoint(getPointNav(p), p);
    }

    protected GeminiNav getPointNav(Point p) {
        GeminiNav nav = navMap.get(mapId);
        if (nav == null) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("nav null %s", p));
        }
        return nav;
    }

    public GeminiNav getNav() {
        return navMap.get(mapId);
    }

    public void onDestroy() {
        for (GeminiNav nav : navMap.values()) {
            nav.release();
        }
        navMap.clear();
    }


    /**
     * navmesh有时会给出奇怪弯曲路线 简化
     *
     * @param points 路点list
     * @return 优化后的路线
     */
    public List<Point> simplePath(List<Point> points) {
        List<Point> ret = new ArrayList<>();
        // 一层循环 O(N)有漏洞
//        Point cur = points.get(0);
//        ret.add(cur);
//
//        for (int i = 2; i < points.size(); i++) {
//            Point next = points.get(i);
//            // 如果有阻挡 就正常加到返回列表去
//            if (!NavmeshApi.isNoCollision(nav, cur, next)) {
//                ret.add(points.get(i - 1));
//                cur = points.get(i - 1);
//            }
//        }
//        ret.add(points.get(points.size() - 1));
        // 两层 O(N2) 优化全了 (可能非最优，尽可能优)
        // 起始点总要加回的
        ret.add(points.get(0));
        for (int i = 0; i < points.size() - 1; i++) {
            Point cur = points.get(i);
            int next = -1;
            // 向后遍历 寻找可直达点
            for (int j = i + 2; j < points.size(); j++) {
                Point now = points.get(j);
                if (isNoCollision(cur, now)) {
                    next = j;
                }
            }
            if (next != -1) {
                ret.add(points.get(next));
                i = next - 1;
            } else {
                // 找不到 加入下一个点
                ret.add(points.get(i + 1));
            }
        }
        if (points.size() != ret.size()) {
            LOGGER.debug("simplePath finished. old:{} new:{}", points, ret);
        }
        return ret;
    }
}

package com.yorha.cnc.scene.gm.command.city;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 强制升天
 */
public class CityAscend implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        scenePlayer.getMainCity().getTransformComponent().cityAscend(CommonEnum.CityAscendReason.CAR_WALL_DESTROY);
    }

    @Override
    public String showHelp() {
        return "CityAscend";
    }


    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

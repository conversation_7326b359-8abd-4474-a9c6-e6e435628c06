package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.CommonEnum.PlunderResultEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 掠夺资源
 *
 * <AUTHOR>
 */
public class ArmyPlunderComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyPlunderComponent.class);
    /**
     * 发生掠夺时缓存一下负重map，因为要跨阶段使用
     */
    private Map<Long, SsPlayerMisc.PlunderWeight> plunderWeight;

    public ArmyPlunderComponent(ArmyEntity owner) {
        super(owner);
    }

    /**
     * 获取当前部队可掠夺量
     *
     * @return <armyId, <weight, result>> 部队剩余的负重, <weight为正值表示可以获得资源;负值表示需要损失资源, 掠夺结果>
     */
    public Map<Long, SsPlayerMisc.PlunderWeight> getArmyPlunderWeight(boolean canPlunder) {
        Map<Long, SsPlayerMisc.PlunderWeight> armiesPlunderWeight = new HashMap<>();
        if (getOwner().isRallyArmy()) {
            for (ArmyEntity inRallyArmy : getOwner().getRallyEntity().getArmyMgrComponent().getInRallyArmies()) {
                armiesPlunderWeight.put(inRallyArmy.getEntityId(), getSingleArmyPlunderWeight(inRallyArmy, canPlunder));
            }
        } else {
            armiesPlunderWeight.put(getOwner().getEntityId(), getSingleArmyPlunderWeight(getOwner(), canPlunder));
        }
        if (canPlunder) {
            plunderWeight = armiesPlunderWeight;
        }
        return armiesPlunderWeight;
    }

    /**
     * 获取单部队可掠夺量 = min(部队剩余负重, 玩家当日剩余负重)
     *
     * @param canPlunder 只有攻城战会传 true
     */
    private SsPlayerMisc.PlunderWeight getSingleArmyPlunderWeight(ArmyEntity armyEntity, boolean canPlunder) {
        long maxBurden = armyEntity.getResourceComponent().getArmyMaxBurden();
        long curBurden = armyEntity.getResourceComponent().getCurBurden();
        // 部队剩余负重 正值：可获得资源；负值：需要损失资源
        long remainArmyWeight = maxBurden - curBurden;
        // 玩家当日剩余可掠夺量
        long remainPlayerWeight = 0;
        if (getOwner().getScenePlayer() instanceof ScenePlayerEntity) {
            remainPlayerWeight = getPlayerRemainBurden((ScenePlayerEntity) armyEntity.getScenePlayer());
        }
        long weight;
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            weight = remainArmyWeight;
        } else {
            weight = Math.min(remainArmyWeight, remainPlayerWeight);
        }
        PlunderResultEnum result;

        if (weight > 0) {
            result = canPlunder ? PlunderResultEnum.PRE_PLUNDER : PlunderResultEnum.PRE_PLUNDER_NONE;
        } else if (weight < 0) {
            result = PlunderResultEnum.PRE_LOSS;
        } else if (remainArmyWeight == 0) {
            if (maxBurden <= 0) {
                // 部队没负重，死光了
                result = PlunderResultEnum.PRE_PLUNDER_NONE;
            } else {
                result = PlunderResultEnum.PRE_ARMY_BURDEN_FULL;
            }
        } else {
            result = canPlunder ? PlunderResultEnum.PRE_PLAYER_PLUNDER_FULL : PlunderResultEnum.PRE_PLUNDER_NONE;
        }
        return SsPlayerMisc.PlunderWeight.newBuilder()
                .setPlunderWeight(weight)
                .setResult(result)
                .setOwnerPlayerId(armyEntity.getPlayerId())
                .build();
    }

    /**
     * 获取玩家当日剩余可掠夺资源数 = 当日掠夺量上限 - 当日掠夺量
     */
    private long getPlayerRemainBurden(ScenePlayerEntity scenePlayer) {
        // 刷新玩家当日掠夺量
        if (!TimeUtils.isSameDayWithNow(scenePlayer.getProp().getPlunderModel().getLastPlunderTime())) {
            scenePlayer.getProp().getPlunderModel().getTodayPlunder().clear();
            LOGGER.debug("player:{}, refresh today plunder weight.", scenePlayer.getPlayerId());
        }
        scenePlayer.getProp().getPlunderModel().setLastPlunderTime(SystemClock.now());
        long plunderWeightLimit = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getPlunderLimit();
        long todayPlunderWeight = scenePlayer.getProp().getPlunderModel().getTodayPlunder().values().stream().mapToLong(CurrencyProp::getCount).sum();
        return Math.max(plunderWeightLimit - todayPlunderWeight, 0);
    }

    /**
     * 分配掠夺的资源
     *
     * @param resources           <resourceType, Struct.Currency> 总共掠夺到的所有资源
     * @param armiesPlunderWeight <armyId, SsPlayerMisc.PlunderWeight> 部队剩余的负重, <SsPlayerMisc.PlunderWeight.weight为正值表示可以获得资源;负值表示需要损失资源, 掠夺结果>
     */
    public void handleArmyResourceAllot(Map<Integer, Struct.Currency> resources, Map<Long, SsPlayerMisc.PlunderWeight> armiesPlunderWeight, BattleRelation relation) {
        if (resources.isEmpty()) {
            return;
        }
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) getOwner().getScenePlayer();
        double totalWeight = armiesPlunderWeight.values().stream()
                .filter(it -> it.getPlunderWeight() > 0)
                .mapToLong(SsPlayerMisc.PlunderWeight::getPlunderWeight).
                sum();

        for (Map.Entry<Long, SsPlayerMisc.PlunderWeight> weightEntry : armiesPlunderWeight.entrySet()) {
            long armyId = weightEntry.getKey();
            PlunderResultEnum realResult = weightEntry.getValue().getResult();
            long realWeight = weightEntry.getValue().getPlunderWeight();
            long playerRemainBurden = getPlayerRemainBurden(scenePlayer);
            if (realWeight > playerRemainBurden) {
                if (playerRemainBurden == 0) {
                    realResult = PlunderResultEnum.PRE_PLAYER_PLUNDER_FULL;
                }
                realWeight = playerRemainBurden;
            }
            // 获取资源，按比例分配
            // 集结部队中某玩家掠夺的A资源 = 集结部队整体掠夺的A资源 * （该玩家部队最大资源掠夺量 / 集结部队最大资源掠夺量），结果向下取整
            double ratio = realWeight / totalWeight;
            Map<CurrencyType, Long> plunderRes = resources.entrySet()
                    .stream()
                    .collect(Collectors.toMap(it -> CurrencyType.forNumber(it.getKey()), it -> MathUtils.floorLong(it.getValue().getCount() * ratio)));

            // 发生掠夺，更新玩家当日掠夺量
            for (Map.Entry<CurrencyType, Long> entry1 : plunderRes.entrySet()) {
                CurrencyProp todayPlunderV = scenePlayer.getProp().getPlunderModel().getTodayPlunderV(entry1.getKey().getNumber());
                if (todayPlunderV != null) {
                    todayPlunderV.setCount(todayPlunderV.getCount() + entry1.getValue());
                } else {
                    scenePlayer.getProp().getPlunderModel().putTodayPlunderV(new CurrencyProp().setType(entry1.getKey().getNumber()).setCount(entry1.getValue()));
                }
            }

            SceneObjEntity objEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(armyId);
            if (objEntity != null) {
                ArmyEntity armyEntity = (ArmyEntity) objEntity;
                armyEntity.getResourceComponent().addPlunderResource(plunderRes);
            }
            if (objEntity == null) {
                // 补发--防止分配掠夺时行军已回城销毁
                reissuePlunder(weightEntry.getValue(), armyId, scenePlayer, plunderRes);
            }
            completeRecord(plunderRes, armyId, realResult, getOwner().getBattleComponent().getBattleRole(), relation);
        }
    }

    private void reissuePlunder(SsPlayerMisc.PlunderWeight weightData, long armyId, AbstractScenePlayerEntity leaderPlayer, Map<CurrencyType, Long> plunderRes) {
        AbstractScenePlayerEntity reissuePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(weightData.getOwnerPlayerId());
        if (weightData.getOwnerPlayerId() == 0 || reissuePlayer == null) {
            LOGGER.error("ArmyPlunderComponent reissuePlunder fail, player not exist, weightData={}, armyId={} leaderPlayer={} plunderRes={}", weightData.getOwnerPlayerId(), armyId, leaderPlayer, plunderRes);
            return;
        }
        LOGGER.info("ArmyPlunderComponent reissuePlunder success, army={}, player={} not found when allot resource. plunderRes={}", armyId, leaderPlayer, plunderRes);
        SsPlayerMisc.AddResourceAfterPlunderCmd.Builder cmd = SsPlayerMisc.AddResourceAfterPlunderCmd.newBuilder();
        for (Map.Entry<CurrencyType, Long> currencyTypeLongEntry : plunderRes.entrySet()) {
            cmd.putPlunderRes(currencyTypeLongEntry.getKey().getNumber(), currencyTypeLongEntry.getValue());
        }
        reissuePlayer.tellPlayer(cmd.build());
    }

    /**
     * 检查部队携带的资源，如负重变少，触发损失资源
     *
     * @param armiesPlunderWeight <armyId, Struct.PlunderWeight> 部队剩余的负重, <Struct.PlunderWeight.weight为正值表示可以获得资源;负值表示需要损失资源, 掠夺结果>
     */
    public void handleArmyResourceLoss(Map<Long, SsPlayerMisc.PlunderWeight> armiesPlunderWeight, BattleRole myRole, BattleRelation relation) {
        for (Map.Entry<Long, SsPlayerMisc.PlunderWeight> weightEntry : armiesPlunderWeight.entrySet()) {
            if (weightEntry.getValue().getPlunderWeight() > 0) {
                continue;
            }

            ArmyEntity armyEntity = getOwner();
            if (getOwner().isRallyArmy()) {
                armyEntity = getOwner().getRallyEntity().getArmyMgrComponent().findInRallyArmy(weightEntry.getKey());
            }
            if (armyEntity == null) {
                LOGGER.error("army:{}, inner army not found when check army resource. self armyId:{}", getOwner(), weightEntry.getKey());
                continue;
            }

            Map<CurrencyType, Long> loss = armyEntity.getResourceComponent().onLossResource(weightEntry.getValue().getPlunderWeight());
            completeRecord(loss, armyEntity.getEntityId(), weightEntry.getValue().getResult(), myRole, relation);
        }
    }

    public void handleArmyResourceLoss(BattleRole myRole, BattleRelation relation) {
        Map<Long, SsPlayerMisc.PlunderWeight> armiesPlunderWeight = plunderWeight;
        if (armiesPlunderWeight == null) {
            // 没有掠夺的缓存负重，则当场重新获取负重
            armiesPlunderWeight = getArmyPlunderWeight(false);
        }
        handleArmyResourceLoss(armiesPlunderWeight, myRole, relation);
        plunderWeight = null;
    }

    private static void completeRecord(Map<CurrencyType, Long> resources, long myEntityId, PlunderResultEnum result, BattleRole leaderRole, BattleRelation relation) {
        // 没有指定relation则给所有relation填充
        if (relation == null) {
            leaderRole.forEachRelation(r -> {
                BattleRecord.RoleRecord myRoleRecord = r.getContext().getRoleRecord(leaderRole.getRoleId());
                // 写战报
                myRoleRecord.fillPlunder(myEntityId, resources, result);
            });
        } else {
            BattleRecord.RoleRecord myRoleRecord = relation.getContext().getRoleRecord(leaderRole.getRoleId());
            // 写战报
            myRoleRecord.fillPlunder(myEntityId, resources, result);
        }
    }
}
package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ChangeMoveRatio implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int ratio = Integer.parseInt(args.get("ratio"));
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        scenePlayer.getArmyMgrComponent().setMoveSpeedRatio(ratio);
    }

    @Override
    public String showHelp() {
        return "ChangeMoveRatio ratio={value}";
    }
}

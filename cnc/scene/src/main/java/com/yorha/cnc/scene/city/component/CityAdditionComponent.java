package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;

/**
 * <AUTHOR>
 */
public class CityAdditionComponent extends SceneObjAdditionComponent {
    public CityAdditionComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }

    @Override
    public AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getScenePlayer();
    }
}

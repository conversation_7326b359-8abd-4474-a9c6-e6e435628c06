package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import res.template.BuffTemplate;

import java.util.Map;

public class AddDungeonBuff implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int buffId = Integer.parseInt(args.get("buffId"));
        BuffTemplate buffTemplate = DevBuffMgrBase.getBuffTemplate(buffId);
        if (!actor.getScene().isDungeon()) {
            return;
        }
        if (buffTemplate != null) {
            AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            long endTime;
            if (args.get("seconds") != null) {
                endTime = SystemClock.now() + Long.parseLong(args.get("seconds")) * 1000;
                scenePlayer.getDevBuffComponent().addDevBuff(buffId, endTime, CommonEnum.DevBuffSourceType.DBST_GM);
            } else {
                scenePlayer.getDevBuffComponent().addDevBuff(buffId, CommonEnum.DevBuffSourceType.DBST_GM);
            }
        }
    }
}

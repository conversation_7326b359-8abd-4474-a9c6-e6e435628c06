package com.yorha.cnc.scene.cave;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.game.gen.prop.CaveProp;

/**
 * 山洞工厂
 *
 * <AUTHOR>
 */
public class CaveFactory {

    /**
     * 创建山洞
     */
    public static CaveEntity createCave(SceneEntity scene, int templateId, int x, int y, int configId) {
        // 构建prop
        CaveProp prop = new CaveProp();
        prop.setTemplateId(templateId)
                .setConfigId(configId)
                .getPoint().setX(x).setY(y);
        prop.unMarkAll();
        // 构建entity
        CaveBuilder builder = new CaveBuilder(scene, scene.ownerActor().nextId(), prop);
        CaveEntity entity = new CaveEntity(builder);
        entity.addIntoScene();
        return entity;
    }
}

package com.yorha.cnc.scene.milestone;

import com.yorha.cnc.mainScene.IMainScene;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.milestone.MileStoneTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.game.gen.prop.MileStoneInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

public abstract class AbstractMileStoneHandler implements IMileStoneTaskHandler {
    protected static final Logger LOGGER = LogManager.getLogger(AbstractMileStoneHandler.class);

    private IMainScene iMainScene = null;

    protected MileStoneInfoProp prop;
    protected CommonEnum.MileStoneStatisticType statisticType;

    /**
     * 初始化任务处理器
     *
     * @param prop 属性系统载体
     */
    public void initHandler(IMainScene iMainScene, MileStoneInfoProp prop, CommonEnum.MileStoneStatisticType type) {
        this.iMainScene = iMainScene;
        this.prop = prop;
        this.statisticType = type;
    }

    /**
     * 获取Prop
     * 以后要是有定制化需求，考虑下沉到实现类中（大概率不会）
     */
    public MileStoneInfoProp getProp() {
        return prop;
    }

    @Override
    public boolean checkFin() {
        String[] taskParam = getTaskParamById();

        // 默认参数1为进度
        int playerNum = Integer.parseInt(taskParam[0]);
        return getProp().getProcess() >= playerNum;
    }

    protected String[] getTaskParamById() {
        MileStoneMgrComponent<?> mileStoneOrNullComponent = iMainScene.getMileStoneOrNullComponent();
        if (mileStoneOrNullComponent == null) {
            WechatLog.error("mileStoneOrNullComponent is null iMainScene={}", iMainScene);
            return null;
        }
        return ResHolder.getResService(MileStoneTemplateService.class).getTaskParamById(getProp().getMileStoneId(), mileStoneOrNullComponent.getCurTemplateId());
    }

    /**
     * 满足积分
     */
    public List<MileStoneClanInfoProp> filterMeetScore(List<MileStoneClanInfoProp> rankProp) {
        return rankProp;
    }


    @Override
    public void updateProcess(MileStoneTaskData data) {

    }

    @Override
    public void recordRankData(MileStoneTaskData data) {

    }

    /**
     * 更新联盟积分
     */
    public void setClanScore(MileStoneClanInfoProp mileStoneClanInfoProp, long clanId, long score, String reason) {
        if (iMainScene.getSceneClanOrNull(clanId) == null) {
            LOGGER.warn("milestone record clan id is null. clanId={} simpleClan={} reason={}", clanId, mileStoneClanInfoProp.getSimpleClan(), reason);
            return;
        }
        mileStoneClanInfoProp.setClanId(clanId).setScore(score).setLastUpdateTsMs(SystemClock.now());

        SceneClanEntity sceneClan = iMainScene.getSceneClanOrNull(clanId);
        mileStoneClanInfoProp.getSimpleClan().setClanId(clanId)
                .setSimpleName(sceneClan.getClanSimpleName())
                .setName(sceneClan.getClanName())
                .setTerritoryColor(sceneClan.getTerritoryColor())
                .setFlagColor(sceneClan.getFlagColor())
                .setFlagShading(sceneClan.getFlagShading())
                .setFlagSign(sceneClan.getFlagSign())
                .setNationFlagId(sceneClan.getNationFlagId());

    }

    /**
     * 任务统计类型
     */
    @Override
    public CommonEnum.MileStoneStatisticType getMileStoneStatisticType() {
        return statisticType;
    }


}

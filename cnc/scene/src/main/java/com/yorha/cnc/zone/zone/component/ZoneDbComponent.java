package com.yorha.cnc.zone.zone.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.ActorChangeAttrUpsertStrategy;
import com.yorha.common.dbactor.DbTaskProxy;
import com.yorha.common.dbactor.DefaultDbOperationStrategyImpl;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.proto.TcaplusDb;
import com.yorha.proto.Zone;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 */
public class ZoneDbComponent extends AbstractComponent<ZoneEntity> {
    /**
     * 操作增删改查的代理。
     */
    private final DbTaskProxy dbTaskProxy;

    public ZoneDbComponent(ZoneEntity owner, Zone.ZoneInfoEntity changedAttr) {
        super(owner);
        final ZoneDbActorUpsertStrategy strategy = new ZoneDbActorUpsertStrategy(changedAttr != null ? changedAttr : Zone.ZoneInfoEntity.getDefaultInstance());
        this.dbTaskProxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString() + " ZoneDbComponent")
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .upsert(strategy)
                .owner(ownerActor())
                .limitTimerOwner(getOwner().getTimerComponent())
                .entityId(String.valueOf(this.getEntityId()))
                .intervalMs(DbLimitConstants.ZONE_INFO_DB_LIMIT_INTERVAL_MS)
                .build();
    }

    public void saveChangeToDb() {
        this.dbTaskProxy.update();
    }

    public ZoneInfoProp getProp() {
        return getOwner().getProp();
    }

    /**
     * 属性增量更新同步db
     */
    public void saveOnDestroy() {
        if (dbTaskProxy == null) {
            return;
        }
        if (!this.getOwner().isDestroy()) {
            WechatLog.error("ZoneDbComponent endDb {} is not destroy!", getOwner());
        }
        this.dbTaskProxy.saveDbAsync();
        this.dbTaskProxy.stop();
    }

    private final static class ZoneDbActorUpsertStrategy extends ActorChangeAttrUpsertStrategy<Zone.ZoneInfoEntity> {
        public ZoneDbActorUpsertStrategy(GeneratedMessageV3 msg) {
            super(msg);
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            final SceneActor sceneActor = (SceneActor) actor;
            return sceneActor.getBigScene().getZoneEntity().getProp().copyChangeToDb((Zone.ZoneInfoEntity.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected Zone.ZoneInfoEntity buildFullAttrSaveData(AbstractActor actor) {
            final SceneActor sceneActor = (SceneActor) actor;
            return sceneActor.getBigScene().getZoneEntity().getProp().getCopyDbBuilder().build();
        }

        @Override
        protected Zone.ZoneInfoEntity buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            final TcaplusDb.ZoneTable.Builder recordBuilder = (TcaplusDb.ZoneTable.Builder) result.value;
            final ZoneInfoProp prop = ZoneInfoProp.of(recordBuilder.getFullAttr(), recordBuilder.getChangedAttr());
            return prop.getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Zone.ZoneInfoEntity fullAttr, @NotNull Zone.ZoneInfoEntity changeAttr) {
            TcaplusDb.ZoneTable.Builder request = TcaplusDb.ZoneTable.newBuilder()
                    .setZoneId(actor.getZoneId());
            if (fullAttr != null) {
                request.setFullAttr(fullAttr);
            }
            request.setChangedAttr(changeAttr);
            return request;
        }

    }

    @Override
    public String toString() {
        return "ZoneInfoDbComponent{" +
                "zoneId=" + this.getOwner().getZoneId() +
                '}';
    }
}

package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.game.gen.prop.WarningItemListProp;
import com.yorha.game.gen.prop.WarningItemProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 预警组件
 *
 * <AUTHOR>
 */
public class AbstractScenePlayerWarningComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerWarningComponent.class);

    public AbstractScenePlayerWarningComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    private WarningItemListProp getWarningProp() {
        return getOwner().getWarningProp();
    }

    /**
     * 获取预警列表
     */
    public StructPlayerPB.WarningInfoListPB getWarningList() {
        List<WarningItemProp> deleteList = new ArrayList<>();
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        List<StructPlayerPB.WarningInfoPB> warningList = new ArrayList<>();
        for (WarningItemProp prop : getWarningProp()) {
            long objId = prop.getArmyId();
            SceneObjEntity obj = objMgrComponent.getSceneObjEntity(objId);
            if (obj == null) {
                deleteList.add(prop);
                continue;
            }
            // 构建预警数据  如果出问题了  移除掉
            try {
                StructPlayerPB.WarningInfoPB.Builder builder = StructPlayerPB.WarningInfoPB.newBuilder();
                boolean ret = obj.copyToWarningInfo(builder);
                if (!ret) {
                    deleteList.add(prop);
                    continue;
                }
                builder.setType(prop.getType());
                warningList.add(builder.build());
            } catch (Exception e) {
                deleteList.add(prop);
                LOGGER.warn("{} copyToWarningInfo error ", obj, e);
            }
        }
        for (WarningItemProp prop : deleteList) {
            getWarningProp().remove(prop);
            LOGGER.warn("{} removeWarningItem {}", getOwner(), prop);
        }
        StructPlayerPB.WarningInfoListPB.Builder builder = StructPlayerPB.WarningInfoListPB.newBuilder();
        builder.addAllDatas(warningList);
        return builder.build();
    }

    public void addWarningItemNeedAddEvent(SceneObjEntity sceneObjEntity, CommonEnum.WarningType type) {
        long armyId = sceneObjEntity.getEntityId();
        for (WarningItemProp warningItemProp : getWarningProp()) {
            if (warningItemProp.getArmyId() == armyId) {
                return;
            }
        }
        sceneObjEntity.getEventDispatcher().addEventListener(this::onStopBattle, WarningRemoveEvent.class);
        getWarningProp().add(new WarningItemProp().setArmyId(armyId).setType(type).setIsIgnore(false));
        LOGGER.info("{} addWarningItem {} {}", getOwner(), armyId, type);
    }

    /**
     * 加入预警列表
     */
    public void addWarningItem(SceneObjEntity sceneObjEntity, CommonEnum.WarningType type) {
        long armyId = sceneObjEntity.getEntityId();
        for (WarningItemProp warningItemProp : getWarningProp()) {
            if (warningItemProp.getArmyId() == armyId) {
                return;
            }
        }
        getWarningProp().add(new WarningItemProp().setArmyId(armyId).setType(type).setIsIgnore(false));
        LOGGER.info("{} addWarningItem {} {}", getOwner(), armyId, type);
    }

    private void onStopBattle(WarningRemoveEvent event) {
        LOGGER.info("{} drop battle {}", getOwner(), event.getEntityId());
        removeWarningItem(event.getEntityId());
    }

    /**
     * 移除预警列表
     */
    public void removeWarningItem(long objId) {
        WarningItemProp cur = null;
        for (WarningItemProp warningItemProp : getWarningProp()) {
            if (warningItemProp.getArmyId() == objId) {
                cur = warningItemProp;
                break;
            }
        }
        if (cur != null) {
            getWarningProp().remove(cur);
            LOGGER.info("{} removeWarningItem {}", getOwner(), objId);
        }
    }

    public void removeWarningItem(Set<Long> objList) {
        List<WarningItemProp> remove = new ArrayList<>();
        for (WarningItemProp warningItemProp : getWarningProp()) {
            if (objList.contains(warningItemProp.getArmyId())) {
                remove.add(warningItemProp);
            }
        }
        for (WarningItemProp warningItemProp : remove) {
            getWarningProp().remove(warningItemProp);
            LOGGER.info("{} removeWarningItem {}", getOwner(), warningItemProp.getArmyId());
        }
    }

    /**
     * 忽略预警提示
     */
    public void setWarningItemIgnoreTag(long objId, boolean ignore) {
        LOGGER.debug("{} setWarningItemIgnoreTag armyId: {} ignore: {}", getOwner(), objId, ignore);
        for (WarningItemProp warningItemProp : getWarningProp()) {
            if (warningItemProp.getArmyId() == objId) {
                warningItemProp.setIsIgnore(ignore);
                return;
            }
        }
        LOGGER.warn("{} setWarningItemIgnoreTag  no item  armyId{}  ignore: {}", getOwner(), objId, ignore);
    }

    /**
     * 忽略当前所有预警
     */
    public void ignoreWarningAll() {
        getWarningProp().forEach(warningInfoProp -> warningInfoProp.setIsIgnore(true));
    }

}

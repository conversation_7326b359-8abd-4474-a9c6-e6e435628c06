package com.yorha.cnc.scene.npcplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerArmyMgrComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.proto.StructPlayer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class NpcPlayerArmyMgrComponent extends AbstractScenePlayerArmyMgrComponent {

    private final List<Integer> outHero = new ArrayList<>();

    public NpcPlayerArmyMgrComponent(NpcPlayerEntity owner) {
        super(owner);
    }

    /**
     * 构建Npc部队加入集结使用的英雄id
     */
    public void choseCityHero(StructPlayer.Troop.Builder builder) {
        int mainHeroId = 0;
        for (IntTripleType triple : getOwner().getMainCity().getTemplate().getHeroTripleList()) {
            if (outHero.contains(triple.getKey())) {
                continue;
            }
            outHero.add(triple.getKey());
            if (mainHeroId == 0) {
                builder.getMainHeroBuilder().setHeroId(triple.getKey()).setLevel(triple.getValue1()).setStar(triple.getValue2());
                TroopHelper.buildTroopHeroSkillBuilder(ResHolder.getInstance(), builder.getMainHeroBuilder());
                mainHeroId = triple.getKey();
                int limitStar = ResHolder.getResService(ConstKVResService.class).getTemplate().getUnlockDeputyHeroStarLimit();
                // 不能带副将
                if (triple.getValue2() < limitStar) {
                    return;
                }
            } else {
                builder.getDeputyHeroBuilder().setHeroId(triple.getKey()).setLevel(triple.getValue1()).setStar(triple.getValue2());
                TroopHelper.buildTroopHeroSkillBuilder(ResHolder.getInstance(), builder.getDeputyHeroBuilder());
                return;
            }
        }
    }

    public void returnHero(Integer mainHero, Integer deputyHero) {
        outHero.remove(mainHero);
        outHero.remove(deputyHero);
    }

    @Override
    protected void onNormalArmyDelete(ArmyEntity army) {
        super.onNormalArmyDelete(army);
        // 释放英雄
        returnHero(army.getMainHero().getHeroId(), army.getDeputyHero().getHeroId());
    }
}

package com.yorha.cnc.scene.monster;

import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.monster.component.*;
import com.yorha.cnc.scene.monster.monsterFeature.MonsterFeature;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.MonsterProp;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Entity.SceneObjBriefAttr;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class MonsterEntity extends SceneObjEntity {
    private static final Logger LOGGER = LogManager.getLogger(MonsterEntity.class);

    private final MonsterProp prop;

    private final MonsterAiComponent aiComponent = new MonsterAiComponent(this);
    private final MonsterMoveComponent moveComponent = new MonsterMoveComponent(this);
    private final SceneObjBuffComponent buffComponent = new MonsterBuffComponent(this);
    private final MonsterHateListComponent hateListComponent = new MonsterHateListComponent(this);
    private final SceneObjAdditionComponent addComponent = new SceneObjAdditionComponent(this);
    private final MonsterRewardComponent rewardComponent = new MonsterRewardComponent(this);
    private MonsterCastComponent castComponent;
    /**
     * 天网野怪
     */
    private MonsterSkynetComponent skynetComponent;
    /**
     * 构造时依赖army prop 只能放在构造函数里了
     */
    private final MonsterBattleComponent battleComponent;

    private boolean isDead = false;
    /**
     * 所属建筑片id
     */
    private int partId;
    private boolean isWaitRecycling = false;
    private int regionId;
    /**
     * 战斗召唤物（依赖生命周期的强制回收类型）
     */
    private boolean isBattleSummons = false;

    /**
     * 标识是不是GM召唤出来的
     */
    private boolean isGm = false;
    /**
     * 野怪的一些特性
     */
    private final MonsterFeature feature;

    public MonsterEntity(MonsterBuilder builder) {
        super(builder);
        prop = builder.getProp();
        feature = builder.getFeature();
        // 战斗召唤物
        if (builder.isBattleSummons()) {
            isBattleSummons = true;
        }
        battleComponent = new MonsterBattleComponent(this);
        // 蓄力组件
        int cast = getTemplate().getCast();
        if (cast > 0) {
            castComponent = new MonsterCastComponent(this);
        }
        initAllComponents();
        // 野怪都是不落库的，所以必定是新创建
        prop.unMarkAll();
        getPropComponent().initPropListener(false);
        final Point curPoint = getCurPoint();
        try {
            regionId = 0;
            if (getScene().isMainScene()) {
                regionId = MapGridDataManager.getRegionId(getScene().getMapId(), curPoint);
            }
        } catch (Exception e) {
            regionId = 0;
            LOGGER.warn("MonsterEntity setRegionId failed ", e);
        }
    }

    public MonsterFeature getFeature() {
        return feature;
    }

    public boolean isBattleSummons() {
        return this.isBattleSummons;
    }

    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        if (super.onTickDispatch(reason)) {
            return true;
        }
        switch (reason) {
            case TICK_AI:
                MonsterAiComponent aiComponent = getAiComponent();
                if (!aiComponent.isNormalRunning()) {
                    aiComponent.refreshTickRunning();
                }
                if (!aiComponent.isRunning()) {
                    return true;
                }
                aiComponent.onTick();
                return true;
            case TICK_HATE:
                getHateListComponent().onTick();
                return true;
            case TICK_CAST:
                getCastComponent().onTick();
                return true;
            case TICK_LIFE:
                if (!checkRecycle()) {
                    return true;
                }
                onRecycleConditionsMeet();
                return true;
            case TICK_BATTLE: {
                getBattleComponent().onTick();
                return true;
            }
            default:
                return false;
        }
    }

    @Override
    protected void onLifeEnd() {
        // 时间到了 但是不一定能回收  这样的话要启动tick
        if (!checkRecycle()) {
            addTick(SceneTickReason.TICK_LIFE);
            return;
        }
        onRecycleConditionsMeet();
    }

    /**
     * 检查是否可以回收
     *
     * @return true:应该回收
     */
    private boolean checkRecycle() {
        if (isDestroy() || isWaitRecycling) {
            return false;
        }
        // 不需要强制回收 && 战斗中
        if (!isBattleSummons && getBattleComponent().hasTarget()) {
            return false;
        }
        long lifeTime = getExpireTsMs();
        if (lifeTime <= 0) {
            return true;
        }
        return SystemClock.now() >= lifeTime;
    }

    public void initSkynet() {
        if (skynetComponent != null) {
            LOGGER.error("MonsterEntity initSkynet fail, repeat init, monster={}", this);
            return;
        }
        skynetComponent = new MonsterSkynetComponent(this);
    }

    public boolean isBanParallelChase() {
        return getTemplate().getBanParallelChase();
    }

    @Override
    public MonsterProp getProp() {
        return prop;
    }

    public int getMonsterId() {
        return prop.getMonsterId();
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Monster;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getMonsterAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getMonsterAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getMonsterAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        prop.getTarget().setTargetType(ArmyTargetType.ATT_MONSTER)
                .setName("")
                .setClanSimpleName("")
                .setTemplateId(getProp().getMonsterId())
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }

    @Override
    public MonsterBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return feature.getSceneObjType(getTemplate());
    }

    @Override
    public boolean briefEntityAttr(SceneObjBriefAttr.Builder builder) {
        builder.getMonsterAttrBuilder()
                .setTemplateId(getProp().getMonsterId())
                .getPointBuilder().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        return true;
    }

    // ------------------------------------------- component -------------------------------------------
    @Override
    public MonsterAiComponent getAiComponent() {
        return aiComponent;
    }

    @Override
    public MonsterMoveComponent getMoveComponent() {
        return moveComponent;
    }

    @Override
    public MonsterTransformComponent getTransformComponent() {
        return (MonsterTransformComponent) super.getTransformComponent();
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return buffComponent;
    }

    @Override
    public MonsterHateListComponent getHateListComponent() {
        return hateListComponent;
    }

    public MonsterCastComponent getCastComponent() {
        return castComponent;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return addComponent;
    }

    public MonsterRewardComponent getRewardComponent() {
        return rewardComponent;
    }

    public MonsterSkynetComponent getSkynetComponent() {
        if (skynetComponent == null) {
            LOGGER.error("MonsterEntity getSkynetComponent fail, skynetComponent not init, monster={}", this);
            initSkynet();
        }
        return skynetComponent;
    }
// ------------------------------------------- prop -------------------------------------------

    @Override
    public boolean onTimerDispatch(SceneTimerReason reason) {
        if (super.onTimerDispatch(reason)) {
            return true;
        }
        if (reason == SceneTimerReason.TIMER_MONSTER_BATTLE_FORBIDDEN) {
            getBattleComponent().leaveBattleRoleState(BattleConstants.BattleRoleState.FORBIDDEN);
            return true;
        }
        if (reason == SceneTimerReason.TIMER_FORCE_RECYCLE) {
            getBattleComponent().forceEndAllBattle();
            onLifeEnd();
            return true;
        }
        return false;
    }

    // ------------------------------------------- 生命周期 -------------------------------------------

    /**
     * 出生
     */
    public void onSpawn() {
        // 不可战斗状态
        if (getTemplate().getSpawnBattleForbidden() != 0) {
            getBattleComponent().enterBattleRoleState(BattleConstants.BattleRoleState.FORBIDDEN);
            if (getTemplate().getSpawnBattleForbidden() > 0) {
                addSceneSchedule(SceneTimerReason.TIMER_MONSTER_BATTLE_FORBIDDEN, getTemplate().getSpawnBattleForbidden());
            }
        }
        // 启动ai
        if (getAiComponent().getAiIndex() == 1) {
            return;
        }
        getAiComponent().start();
    }

    /**
     * 达成可回收条件
     */
    private void onRecycleConditionsMeet() {
        LOGGER.debug("monster life time over:{}", getEntityId());
        if (!getScene().isMainScene()) {
            forceRecycle();
            return;
        }
        isWaitRecycling = true;
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) getScene().getObjMgrComponent();
        objMgrComponent.addMonsterToRecycleQueue(this);
    }

    /**
     * 等待队列 触发回收
     */
    public void onRecycle() {
        isWaitRecycling = false;
        if (!checkRecycle()) {
            return;
        }
        forceRecycle();
    }

    /**
     * 强制回收
     */
    public void forceRecycle() {
        getBattleComponent().onRecycle();
        getAiComponent().trigger(MonsterTriggerType.MTT_DEAD);
        deleteObj();
    }

    public void setBelongPart(int partId) {
        this.partId = partId;
    }

    /**
     * 死亡
     */
    public void onDead() {
        if (isDestroy() || isDead) {
            return;
        }
        isDead = true;
        // 发送奖励 填充战报等
        getRewardComponent().sendKillAndRewardAll(feature);
        // 触发体力返还
        getBattleComponent().onMonsterDead();
        // 死亡触发器
        getAiComponent().trigger(MonsterTriggerType.MTT_DEAD);
        // 触发死亡时间
        getEventDispatcher().dispatch(new DieEvent(getEntityId(), getTemplate().getId()));
        // 触发掉落
        getRewardComponent().drop();

        if (getScene().isBigScene()) {
            // 更新原服/K服里程碑进度
            MileStoneMgrComponent mileStoneOrNullComponent = getScene().getMileStoneOrNullComponent();
            if (mileStoneOrNullComponent != null) {
                Collection<Long> armys = getBattleComponent().getBattleRole().getRelationKeys();
                mileStoneOrNullComponent.onMonsterDead(getTemplate().getCategory(), getTemplate(), getBattleComponent().getAssembledClans(), armys);
            }
        }
        feature.onDead(this);
//        this.deleteObj();
    }

    public int getPartId() {
        return partId;
    }

    public boolean isDead() {
        return isDead;
    }

    /**
     * 销毁
     */
    @Override
    public void deleteObj() {
        if (isDestroy()) {
            return;
        }
        if (getAiComponent() != null) {
            getAiComponent().onDead();
            getAiComponent().stop();
        }
        feature.onDeleteObj(this, getScene());
        getProp().setDeadTime(SystemClock.now());
        // 野怪死亡，delete之前将最后的状态数据下发客户端，方便做溃败等表现
        getPropComponent().ntfChangeToClient(false);
        super.deleteObj();
    }

    // ------------------------------------------- 场景 -------------------------------------------

    @Override
    public void addIntoScene() {
        onSpawn();
        super.addIntoScene();
    }

    @Override
    public SceneObjectNtfReason getRemoveReason() {
        if (getBattleComponent().hasAnyAlive()) {
            return SceneObjectNtfReason.SONR_RECYCLE;
        }
        return super.getRemoveReason();
    }

    // ------------------------------------------- 快捷方法 -------------------------------------------

    @Override
    public long getPlayerId() {
        return 0;
    }

    /**
     * 野怪阵营判断
     */
    @Override
    public long getClanId() {
        return 0;
    }

    /**
     * 区别于上面的clanId，这里特指联盟限制的专属联盟id
     */
    public long getBelongToClanId() {
        return getProp().getClanId();
    }

    @Override
    public Camp getCampEnum() {
        return getProp().getCamp();
    }

    public void SetCampEnum(Camp camp) {
        getProp().setCamp(camp);
    }

    @Override
    public ErrorCode canBattleWithCode(SceneObjEntity target, boolean needCheckSiegeLimit) {
        ErrorCode errorCode = feature.canBattle(this, target, needCheckSiegeLimit);
        if (errorCode.isNotOk()) {
            return errorCode;
        }
        errorCode = super.canBattleWithCode(target, needCheckSiegeLimit);
        if (errorCode.isNotOk()) {
            return errorCode;
        }
        return ErrorCode.OK;
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        // 只能被集结攻打
        if (isOnlyRally()) {
            if (attackerObj instanceof ArmyEntity) {
                ArmyEntity army = (ArmyEntity) attackerObj;
                if (!army.isRallyArmy()) {
                    return ErrorCode.BATTLE_CANT;
                }
            } else {
                return ErrorCode.BATTLE_CANT;
            }
        }
        // 各野怪能不能被打的特性
        return feature.canBeAttackBySceneObj(this, attackerObj, needCheckSiegeLimit);
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return feature.canBeAttackByScenePlayer(this, attackerPlayer, needCheckSiegeLimit);
    }

    @Override
    public ErrorCode canBeRallyWithCode() {
        BeAttackModel beAttackModel = getTemplate().getBeAttackModel();
        if (beAttackModel != BeAttackModel.BAM_NONE && beAttackModel != BeAttackModel.BAM_ONLY_ONE) {
            return ErrorCode.OK;
        }
        return ErrorCode.RALLY_CANT;
    }

    public boolean isOnlyRally() {
        return getTemplate().getBeAttackModel() == BeAttackModel.BAM_ONLY_RALLY;
    }

    public MonsterTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, getProp().getMonsterId());
    }

    public int getTemplateId() {
        return getProp().getMonsterId();
    }

    @Override
    public String toString() {
        return "Monster{" + "id:" + getEntityId() + " " + getCurPoint() + " templateId=" + prop.getMonsterId() + '}';
    }

    /**
     * copy自身相关属性到RallyEntity的目标数据中
     */
    public void copyToRallyInfo(RallyInfoProp rallyInfoProp) {
        rallyInfoProp.setTargetId(getEntityId())
                .setTargetType(RallyTargetType.RTT_MONSTER)
                .setTargetTemplateId(getMonsterId())
                .getTargetPos().setX(getCurPoint().getX()).setY(getCurPoint().getY());

    }

    @Override
    public boolean getBesiege() {
        return getTemplate().getBesiege();
    }

    @Override
    public int getLevel() {
        return getTemplate().getLevel();
    }

    public int getRegionId() {
        return regionId;
    }

    public void setForceRecyleTime(long delay) {
        addSceneSchedule(SceneTimerReason.TIMER_FORCE_RECYCLE, delay);
    }

    // ------------------------------------------- qlog ------------------------------------------- //

    /**
     * 野怪过期时间戳
     */
    public long getExpireTsMs() {
        return getProp().getLifeTime();
    }

    /**
     * 高品天网是BOSS
     */
    public static boolean isSkynetBoss(MonsterTemplate template) {
        if (template.getCategory() != MonsterCategory.SKYNET_MONSTER) {
            return false;
        }
        return template.getQuality() == SceneObjQuality.ELITE;
    }


    /**
     * 标记GM召唤出来的
     */
    public void markGm() {
        this.isGm = true;
    }

    /**
     * 是否是GM召唤出来的
     */
    public boolean isGm() {
        return this.isGm;
    }
}

package com.yorha.cnc.scene.event.dungeon;

import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class MonsterBornEvent extends IEvent {
    private final MonsterEntity monster;

    public MonsterBornEvent(MonsterEntity monster) {
        this.monster = monster;
    }

    public MonsterEntity getMonster() {
        return monster;
    }
}

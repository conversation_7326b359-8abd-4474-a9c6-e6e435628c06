package com.yorha.cnc.scene.sceneObj.camp;

import com.yorha.proto.CommonEnum;

/**
 * 阵营关系判断的依赖接口
 *
 * <AUTHOR>
 */
public interface CampRelationProvider {

    long getPlayerId();

    long getClanId();

    CommonEnum.Camp getCampEnum();

    int getZoneId();

    /**
     * 两者阵营之间能不能发生战斗
     */
    default boolean campAllowBattle(CampRelationProvider other) {
        if (getCampEnum() != CommonEnum.Camp.C_NONE && getCampEnum() == other.getCampEnum()) {
            return false;
        }
        if (getPlayerId() != 0 && getPlayerId() == other.getPlayerId()) {
            return false;
        }
        if (getClanId() != 0 && getClanId() == other.getClanId()) {
            return false;
        }
        return true;
    }
}

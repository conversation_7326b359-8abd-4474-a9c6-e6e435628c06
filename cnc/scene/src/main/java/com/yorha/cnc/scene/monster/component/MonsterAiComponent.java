package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;


/**
 * <AUTHOR>
 */
public class MonsterAiComponent extends SceneObjAiComponent {

    private boolean tickRunning = false;

    public MonsterAiComponent(MonsterEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        long startTime = SystemClock.now();
        getOwner().getProp().setBornTime(startTime);
        super.init();
    }

    @Override
    public void postInit() {
        super.postInit();
        // 生命周期
        long lifeTime = getOwner().getProp().getLifeTime();
        if (lifeTime > 0) {
            getOwner().addSceneSchedule(SceneTimerReason.TIMER_LIFE, lifeTime - getOwner().getScene().now());
        }
    }

    @Override
    public MonsterEntity getOwner() {
        return (MonsterEntity) super.getOwner();
    }

    @Override
    public boolean isRunning() {
        boolean isRunning = super.isRunning();
        if (isRunning) {
            return true;
        }
        if (!isInitOk()) {
            return false;
        }
        return tickRunning;
    }

    public void refreshTickRunning() {
        tickRunning = RandomUtils.isSuccessByPercentage(10);
    }

    public boolean isNormalRunning() {
        return isRunning();
    }

    @Override
    public int getAiIndex() {
        return getOwner().getTemplate().getAiIndex();
    }

    @Override
    protected boolean isCastFinish() {
        if (getOwner().getCastComponent() == null) {
            return false;
        }
        return getOwner().getCastComponent().isCastFinish();
    }

    @Override
    public void triggerEvent(AIEvent event) {
        super.triggerEvent(event);
        start();
    }
}

package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.Collection;

/**
 * 范围追击
 * 进入条件： 仇恨列表不为空
 * 进入：设置战斗点位
 * 执行：追击反击仇恨值最高敌对单位，目标超出攻击距离丢失目标
 * 结束：停止与当前攻击目标战斗
 *
 * <AUTHOR>
 */
public class ChaseAction extends AbstractAIAction {


    public ChaseAction() {
        super();
    }


    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        // 追击状态要求当前仇恨列表不为空
        SceneObjEntity owner = component.getOwner();
        SceneObjEntity target = owner.getHateListComponent().mostHateEntity();
        if (target == null) {
            if (component.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", component.getLogHead(), getActionName(), false, "target is null");
            }
            return false;
        }
        if (outRange(component, target.getCurPoint(), target.getEntityId())) {
            if (component.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {} target:{}, battle:{} born:{}", component.getLogHead(), getActionName(), false, target.getEntityId() + " out range", target.getCurPoint(), component.getOwner().getTransformComponent().getBattlePoint(), component.getOwner().getTransformComponent().getBornPoint());
            }
            return false;
        }
        if (component.isDebugAble()) {
            LOGGER.info("{}, action :{} isSatisfied：{}", component.getLogHead(), getActionName(), true);
        }
        return true;
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        SceneObjEntity owner = component.getOwner();
        owner.getTransformComponent().enterBattle();
    }

    public boolean outRange(SceneObjAiComponent component, Point point, long targetId) {
        Point battlePoint = component.getOwner().getTransformComponent().getBattlePoint();
        if (battlePoint == null) {
            battlePoint = component.getOwner().getTransformComponent().getBornPoint();
        }
        int chaseRange = component.getAiParams().get(CommonEnum.AiParams.AP_CHASE_RANGE);
        if (component.isTargetIgnoreRange(targetId)) {
            chaseRange = Integer.MAX_VALUE;
        }
        return battlePoint != null && chaseRange > 0 && Point.calDisBetweenTwoPoint(battlePoint, point) >= chaseRange;
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
        SceneObjEntity owner = component.getOwner();
        // 清除不在攻击范围内目标的仇恨值
        Collection<Long> needClearHateObjSet = new ArrayList<>();
        for (long objId : component.getOwner().getHateListComponent().getHateEntities()) {
            SceneObjEntity obj = owner.getScene().getObjMgrComponent().getSceneObjEntity(objId);
            if (obj == null || obj.isDestroy() || outRange(owner.getAiComponent(), obj.getCurPoint(), objId)) {
                needClearHateObjSet.add(objId);
            }
        }
        for (long needClearHateObj : needClearHateObjSet) {
            owner.getHateListComponent().clearHate(needClearHateObj);
        }
        // 进攻目标了
        SceneObjEntity target = owner.getHateListComponent().mostHateEntity();
        if (target == null || target.isDestroy() || outRange(component, target.getCurPoint(), target.getEntityId())) {
            if (target != null) {
                owner.getHateListComponent().clearHate(target.getEntityId());
            }
            if (owner.getHateListComponent().getHateEntities().isEmpty()) {
                component.triggerEvent(AIEvent.LOSE_TARGET);
            }
            return;
        }
        if (owner.getMoveComponent().getCurChaseTargetId() == target.getEntityId()) {
            return;
        }
        // 不可战斗状态不追击
        if (!owner.getBattleComponent().canIBattle()) {
            return;
        }
        owner.getMoveComponent().moveToTargetAsync(target,
                null,
                () -> {
                    long selfId = owner.getEntityId();
                    boolean needMoveBesiege = true;
                    // 对方目标也是我  而且刚建立或者已经建立并且向我移动中
                    if (target.getBattleComponent().getActiveTargetId() == selfId) {
                        if (owner.getScene().getBattleGroundComponent().hasPrepareBattleRelation(selfId, target.getEntityId())) {
                            needMoveBesiege = false;
                        } else if (owner.getScene().getBattleGroundComponent().hasBattleRelation(selfId, target.getEntityId())
                                && target.getMoveComponent() != null && target.getMoveComponent().isMoving()) {
                            needMoveBesiege = false;
                        }
                    }
                    // 战斗
                    if (!owner.getBattleComponent().tryStartBattleWith(target)) {
                        return;
                    }
                    // 围攻
                    if (owner.getBesiege()) {
                        owner.getMoveComponent().occupyAndMoveToBesiegePoint(target, needMoveBesiege, besiegePoint -> !outRange(component, besiegePoint, target.getEntityId()));
                    }
                },
                (event) -> {
                    if (owner.getHateListComponent().getHateEntities().isEmpty()) {
                        component.triggerEvent(AIEvent.LOSE_TARGET);
                    }
                },
                null);
        if (component.isDebugAble()) {
            LOGGER.info("{}, fire and execute :{} target:{} ", component.getLogHead(), getActionName(), target);
        }

    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        super.onEnd(component);
        SceneObjEntity owner = component.getOwner();
        long targetId = owner.getBattleComponent().getTargetId();
        if (targetId != 0) {
            owner.getBattleComponent().stopCurActiveAttack();
            owner.getHateListComponent().clearHate(targetId);
        }
        if (component.isDebugAble()) {
            LOGGER.info("{}, battle point : {}, cur point :{} ", component.getLogHead(), owner.getTransformComponent().getBattlePoint(), owner.getCurPoint());
        }
    }

    @Override
    protected String getActionName() {
        return "ChaseAction";
    }
}

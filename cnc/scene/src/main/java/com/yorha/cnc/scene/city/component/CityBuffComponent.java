package com.yorha.cnc.scene.city.component;

import com.yorha.game.gen.prop.Int32BuffMapProp;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;

/**
 * <AUTHOR>
 */
public class CityBuffComponent extends SceneObjBuffComponent {
    public CityBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuffSys().getBuff();
    }

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }
}

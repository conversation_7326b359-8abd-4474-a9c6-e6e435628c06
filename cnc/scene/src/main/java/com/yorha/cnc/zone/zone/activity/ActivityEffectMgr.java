package com.yorha.cnc.zone.zone.activity;

import com.google.common.collect.Maps;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ActivityEffectMgr {
    private static final org.apache.logging.log4j.Logger LOGGER = LogManager.getLogger(ActivityEffectMgr.class);

    static final Map<CommonEnum.ZoneActivityEffect, IActivityEffect> ACTIVITY_EFFECT_FACTORY = Maps.newHashMap();

    static {
        ACTIVITY_EFFECT_FACTORY.put(CommonEnum.ZoneActivityEffect.ZAE_LAND_PROTECTION, new LandProtectionEffect());
    }

    static public void activityEffectStart(CommonEnum.ZoneActivityEffect activityEffect, ZoneEntity owner) {
        if (!ACTIVITY_EFFECT_FACTORY.containsKey(activityEffect)) {
            LOGGER.error("no on action for activityEffect={}", activityEffect);
            return;
        }
        IActivityEffect effect = ACTIVITY_EFFECT_FACTORY.get(activityEffect);
        effect.efffectStart(owner);
    }

    static public boolean activityEffectIsOn(CommonEnum.ZoneActivityEffect activityEffect) {
        if (!ACTIVITY_EFFECT_FACTORY.containsKey(activityEffect)) {
            LOGGER.error("no activityEffect={}", activityEffect);
            return false;
        }

        IActivityEffect effect = ACTIVITY_EFFECT_FACTORY.get(activityEffect);
        return effect.isOn();
    }

    static public void activityActivityExpire(CommonEnum.ZoneActivityEffect activityEffect, ZoneEntity owner) {
        if (!ACTIVITY_EFFECT_FACTORY.containsKey(activityEffect)) {
            LOGGER.error("no expire action for activityEffect={}", activityEffect);
            return;
        }
        IActivityEffect effect = ACTIVITY_EFFECT_FACTORY.get(activityEffect);
        effect.efffectExpire(owner);
    }
}


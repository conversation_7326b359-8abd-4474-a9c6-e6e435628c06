package com.yorha.cnc.scene.gm.command.resbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstCollectTemplate;
import res.template.ConstTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryResBuildingRange implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        StringBuilder string = new StringBuilder();
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        Point mainCity = scenePlayer.getMainCity().getCurPoint();
        int searchDistance = ResHolder.getInstance().getConstTemplate(ConstCollectTemplate.class).getSearchDistance();
        Circle circleRange = Circle.valueOf(mainCity.getX(), mainCity.getY(), UnitConvertUtils.meterToCm(searchDistance));
        actor.getScene().getAoiMgrComponent().consumerAffectSceneObj(
                circleRange,
                (obj) -> {
                    if (obj.getClass() != ResBuildingEntity.class) {
                        return;
                    }
                    ResBuildingEntity resBuildingEntity = (ResBuildingEntity) obj;
                    string.append(obj.toString());
                    string.append(" level: ");
                    string.append(resBuildingEntity.getTemplate().getResLevel());
                    string.append("\n");
                }
        );
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(string.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("QueryResBuildingRange")
                .setSubTitle("QueryResBuildingRange");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(scenePlayer.getZoneId())
                        .build(),
                builder.build());
    }


    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COLLECT;
    }
}

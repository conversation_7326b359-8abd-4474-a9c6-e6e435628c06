package com.yorha.cnc.zone.zone;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneQlogComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.zone.IZone;
import com.yorha.cnc.zone.component.ZoneSideActivityComponent;
import com.yorha.cnc.zone.component.ZoneSideDbComponent;
import com.yorha.cnc.zone.component.ZoneSideRefreshComponent;
import com.yorha.cnc.zone.zone.component.*;
import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.cluster.node.NodeActor;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.discovery.ZoneItem;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ZoneEntity extends AbstractEntity implements IZone {
    private static final Logger LOGGER = LogManager.getLogger(ZoneEntity.class);

    private final SceneActor actor;
    private final ZoneBornMgrComponent bornMgrComponent = new ZoneBornMgrComponent(this);
    private final ZoneInfoPropComponent propComponent = new ZoneInfoPropComponent(this);
    private final ZoneActivityComponent activityComponent = new ZoneActivityComponent(this);
    private final ZoneOpenComponent openMgrComponent = new ZoneOpenComponent(this);
    private final ZoneDevBuffComponent devBuffComponent = new ZoneDevBuffComponent(this);
    private final ZoneAdditionComponent additionComponent = new ZoneAdditionComponent(this);
    private final ZoneKingdomComponent kingdomComponent = new ZoneKingdomComponent(this);
    private final ZoneSeasonComponent seasonComponent = new ZoneSeasonComponent(this);
    private final ZoneMilestoneComponent milestoneComponent = new ZoneMilestoneComponent(this);
    private final ZoneTimerComponent timerComponent;
    private final ZoneSideActivityComponent sideActivityComponent = new ZoneSideActivityComponent(this);
    private final ZoneSideRefreshComponent sideRefreshComponent = new ZoneSideRefreshComponent(this, this);
    private final ZoneDbComponent zoneDbComponent;
    private final ZoneSideDbComponent zoneSideDbComponent;
    private final ZoneInfoProp prop;
    private final ZoneSideProp zoneSideProp;
    private BigSceneEntity bigScene;


    public ZoneEntity(SceneActor actor, ZoneInfoProp prop, Zone.ZoneInfoEntity changedProp, ZoneSideProp zoneSideProp, Zoneside.ZoneSideEntity zoneSideChangedProp) {
        super(actor.getZoneId());
        this.actor = actor;
        // 下面的初始化依赖actor设置后才行
        this.timerComponent = new ZoneTimerComponent(this);
        this.prop = prop;
        this.zoneSideProp = zoneSideProp;
        this.zoneDbComponent = new ZoneDbComponent(this, changedProp);
        this.zoneSideDbComponent = new ZoneSideDbComponent(this, zoneSideChangedProp);
        this.checkOpenTime();
        initAllComponents();
    }

    /**
     * 场景刚创建 啥都没好，只能引用，不能调用
     */
    public void onSceneCreate(BigSceneEntity bigScene) {
        this.bigScene = bigScene;
        getMilestoneComponent().initMileStone(false);
    }

    /**
     * 场景的物体刚加载完  场景component没初始化好
     */
    public void callAfterAllLoad() {
        getActivityComponent().afterAllLoad();
        getDevBuffComponent().afterAllLoad();
    }

    /**
     * 场景初始化好了，准备开始运转了
     */
    public void onSceneOk() {
        // 刷加成
        getAdditionComponent().onSceneOk();
        // 刷赛季
        getSeasonComponent().onSceneOk();
        // 刷野怪里程碑
        getOpenMgrComponent().onSceneOk();
        if (isZoneOpen()) {
            // 初始化活动
            getSideActivityComponent().initActivity();
            getSideRefreshComponent().initActivityRefresh();
            // 开里程碑
            getMilestoneComponent().startMileStone();
            // 开野怪刷新
            getBigScene().getObjMgrComponent().onZoneOpen();
        }
        ZoneOpenComponent.updateZoneOpenTimeMonitor(getZoneId(), getServerOpenTsMs());
        getBornMgrComponent().ntfZoneItemCreate();
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        getDbComponent().saveOnDestroy();
        getProp().unMarkAll();
        getSideDbComponent().saveOnDestroy();
        getZoneSideProp().unMarkAll();
    }

    public boolean isZoneOpen() {
        return getProp().getIsOpened();
    }

    @Override
    public long getServerOpenTsMs() {
        return getProp().getServerOpenTsMs();
    }

    @Override
    public CommonEnum.ZoneSeason getZoneSeason() {
        return getSeasonComponent().getCurSeason();
    }

    @Override
    public CommonEnum.ZoneSeasonStage getZoneSeasonStage() {
        return getSeasonComponent().getCurSeasonStage();
    }

    /**
     * 1、创建或恢复大世界，并且此时大世界db数据没有开服，用config最新的开服时间
     * 2、没开服的情况下收到开服时间变化，用config最新的开服时间
     * 3、恢复大世界，并且大世界已经开服，用大世界db的开服时间
     */
    private void realSetOpenTime(long openTime) {
        LOGGER.info("realSetOpenTime {}", openTime);
        getProp().setServerOpenTsMs(openTime);
        ZoneContext.setServerOpenTsMs(openTime);
    }

    /**
     * 放这里是为了在所有逻辑之前就跑
     */
    private void checkOpenTime() {
        int zoneId = getZoneId();
        if (zoneId < 0) {
            throw new GeminiException("ZoneOpenComponent tryOpenZone but zoneId={}", zoneId);
        }

        final long configOpenTime = ClusterConfigUtils.getZoneConfig(zoneId).getLongItem("open_zone_time");
        long dbServerOpenTsMs = getProp().getServerOpenTsMs();
        LOGGER.info("ZoneOpenComponent onBigSceneCreate configOpenTime:{} dbServerOpenTsMs:{} isZoneOpen:{}", configOpenTime, dbServerOpenTsMs, isZoneOpen());

        if (!isZoneOpen()) {
            // 没开服，永远用config的
            realSetOpenTime(configOpenTime);
        } else {
            if (configOpenTime != dbServerOpenTsMs) {
                if (ServerContext.isProdSvr()) {
                    // 已经开服，结果config和db存的开服时间不一致，那出问题了
                    WechatLog.error("ZoneOpenComponent onBigSceneCreate but {} not equal {}", configOpenTime, dbServerOpenTsMs);
                } else {
                    LOGGER.error("ZoneOpenComponent onBigSceneCreate but {} not equal {}", configOpenTime, dbServerOpenTsMs);
                }
            }
            // 已经开服，则用db里的
            realSetOpenTime(dbServerOpenTsMs);
        }
    }

    /**
     * 刷新开服时间
     */
    public void refreshOpenZoneTime(long newOpenZoneTime) {
        long oldOpenZoneTime = getProp().getServerOpenTsMs();

        if (newOpenZoneTime == oldOpenZoneTime) {
            LOGGER.info("refreshOpenZoneTime old==new {}", oldOpenZoneTime);
            return;
        }
        if (isZoneOpen()) {
            // 已经开服的不允许再修改开服时间
            if (!ServerContext.isProdSvr()) {
                LOGGER.warn("zoneId:{} isOpened but try refreshOpenZoneTime from {} to {}", getZoneId(), oldOpenZoneTime, newOpenZoneTime);
                return;
            }
            WechatLog.error("zoneId:{} isOpened but try refreshOpenZoneTime from {} to {}", getZoneId(), oldOpenZoneTime, newOpenZoneTime);
            return;
        }
        if (newOpenZoneTime <= 0) {
            LOGGER.error("refreshOpenZoneTime but newOpenZoneTime:{} <= 0", newOpenZoneTime);
            return;
        }
        LOGGER.info("gemini_system reloadServerStatus refreshOpenZoneTime {}->{}", oldOpenZoneTime, newOpenZoneTime);

        // 设置新的开服时间，如果不设置，别的模块不知道什么时候开服
        realSetOpenTime(newOpenZoneTime);
        // 同步给zoneItem新的开服时间
        onServerOpenTsMsChanged(newOpenZoneTime);
        // 上报监控
        ZoneOpenComponent.updateZoneOpenTimeMonitor(ownerActor().getZoneId(), newOpenZoneTime);
    }

    // 首次初始化ZoneItem
    public void onFirstInit(CommonMsg.ZoneCard zoneCard) {
        LOGGER.info("onFirstInit");
        if (zoneItem != null) {
            LOGGER.error("onFirstInit but zoneItem already init");
        }
        ServerOpenStatus openStatus = ServerContext.getServerSetting().getOpenStatus();
        long serverOpenTsMs = getServerOpenTsMs();
        zoneItem = ZoneItem.newItem(this.getZoneId(), serverOpenTsMs, openStatus.name());
        zoneItem.setZoneCard(zoneCard);
        realPutZoneItem();
    }

    // 每60秒回调一次
    public void onRepeatTimerTriggerForZoneItem(CommonMsg.ZoneCard zoneCard) {
        LOGGER.info("onRepeatTimerTriggerForZoneItem");
        if (zoneItem == null) {
            LOGGER.error("onRepeatTimerTriggerForZoneItem but zoneItem is null");
            return;
        }
        if (Objects.equals(zoneItem.getZoneCard(), zoneCard)) {
            LOGGER.info("onRepeatTimerTriggerForZoneItem no need");
            return;
        }
        zoneItem.setZoneCard(zoneCard);
        realPutZoneItem();
    }

    // 开服时间调整后回调
    public void onServerOpenTsMsChanged(long serverOpenTsMs) {
        LOGGER.info("onServerOpenTsMsChanged");
        if (zoneItem == null) {
            LOGGER.error("onServerOpenTsMsChanged but zoneItem is null");
            return;
        }
        if (zoneItem.getOpenTsMs() == serverOpenTsMs) {
            LOGGER.info("onServerOpenTsMsChanged no need");
            return;
        }
        zoneItem.setOpenTsMs(serverOpenTsMs);
        realPutZoneItem();
    }

    public void onServerOpenStatusChanged(ServerOpenStatus openStatus) {
        LOGGER.info("onServerOpenStatusChanged");
        if (zoneItem == null) {
            LOGGER.error("onServerOpenStatusChanged but zoneItem is null");
            return;
        }
        if (Objects.equals(zoneItem.getOpenStatus(), openStatus.name())) {
            LOGGER.info("onServerOpenStatusChanged no need");
            return;
        }
        zoneItem.setOpenStatus(openStatus.name());
        realPutZoneItem();
    }

    private void realPutZoneItem() {
        final String value = JsonUtils.toJsonString(zoneItem);
        LOGGER.debug("gemini_system realPutZoneItem {}", value);
        ownerActor().tell(RefFactory.ofLocalNode(), new ActorRunnable<NodeActor>("realPutZoneItem", (nodeActor) -> {
            final String url = ActorClusterUrlUtils.etcdWorldZoneFoundUrl(this.getZoneId());
            final long leaseId = ServerContext.getEtcdClient().putWithAliveLease(url, value, 30);
            if (leaseId <= 0) {
                throw new GeminiException("gemini_system realPutZoneItem url={} value={} fail!", url, value);
            }
        }));
    }

    private ZoneItem zoneItem = null;

    public ZoneInfoProp getProp() {
        return prop;
    }

    @Override
    public int getZoneId() {
        return ownerActor().getZoneId();
    }

    @Override
    public ZoneSideProp getZoneSideProp() {
        return zoneSideProp;
    }

    @Override
    public ZoneSideActivityComponent getSideActivityComponent() {
        return sideActivityComponent;
    }

    public ZoneSideDbComponent getSideDbComponent() {
        return zoneSideDbComponent;
    }

    @Override
    public MainSceneQlogComponent getQlogComponent() {
        return getBigScene().getQlogComponent();
    }

    public ZoneBornMgrComponent getBornMgrComponent() {
        return bornMgrComponent;
    }

    public ZoneInfoPropComponent getPropComponent() {
        return propComponent;
    }

    public ZoneDbComponent getDbComponent() {
        return zoneDbComponent;
    }

    public ZoneActivityComponent getActivityComponent() {
        return activityComponent;
    }

    public ZoneOpenComponent getOpenMgrComponent() {
        return openMgrComponent;
    }

    public ZoneAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    public ZoneDevBuffComponent getDevBuffComponent() {
        return devBuffComponent;
    }

    public ZoneKingdomComponent getKingdomComponent() {
        return kingdomComponent;
    }

    public ZoneSeasonComponent getSeasonComponent() {
        return seasonComponent;
    }

    public ZoneMilestoneComponent getMilestoneComponent() {
        return milestoneComponent;
    }

    @Override
    public ZoneTimerComponent getTimerComponent() {
        return timerComponent;
    }

    public ZoneSideRefreshComponent getSideRefreshComponent() {
        return sideRefreshComponent;
    }

    @Override
    public EntityAttrOuterClass.EntityType getEntityType() {
        return EntityAttrOuterClass.EntityType.ET_Zone;
    }

    @Override
    public BigSceneEntity getBigScene() {
        return bigScene;
    }

    @Override
    public SceneActor ownerActor() {
        return actor;
    }

    @Override
    protected void onPostInitFailed() {
        getTimerComponent().onDestroy();
    }
}

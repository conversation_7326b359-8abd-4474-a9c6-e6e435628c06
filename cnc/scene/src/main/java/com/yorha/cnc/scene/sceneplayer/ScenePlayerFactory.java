package com.yorha.cnc.scene.sceneplayer;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ScenePlayerProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

/**
 * <AUTHOR>
 */
public class ScenePlayerFactory {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerFactory.class);

    public static ScenePlayerEntity createScenePlayer(SceneEntity sceneEntity, long playerId, String name, Struct.PlayerCardHead cardHead, long createTime) {
        LOGGER.debug("{} atScene create player start, playerId:{} create scene player", LogKeyConstants.GAME_PLAYER_LOGIN, playerId);
        // 构建初始化数据
        ScenePlayerProp prop = new ScenePlayerProp();
        prop.getCardHead().mergeFromSs(cardHead);
        prop.getCardHead().setName(name);
        if (sceneEntity.isBigScene() && createTime <= 0) {
            throw new GeminiException(ErrorCode.PLAYER_CREATE_TIME_INVALID);
        }
        prop.setCreateTime(createTime).setLogoutTime(createTime).getZoneModel().setZoneId(sceneEntity.getZoneId());
        // 创建ScenePlayerEntity
        prop.unMarkAll();
        ScenePlayerEntity scenePlayerEntity = new ScenePlayerEntity(sceneEntity, playerId, prop, new ScenePlayerBuilder());
        sceneEntity.getPlayerMgrComponent().addScenePlayer(scenePlayerEntity);
        LOGGER.info("{} atScene create player end, {} create {} success", LogKeyConstants.GAME_PLAYER_LOGIN, sceneEntity, scenePlayerEntity);
        return scenePlayerEntity;
    }

    /**
     * 添加初始账号士兵
     */
    public static void addDefaultSoldier(ScenePlayerEntity scenePlayerEntity) {
        // 默认编队
        // 新手关默认英雄和士兵 读取常量表
        ConstTemplate bean = ResHolder.getResService(ConstKVResService.class).getTemplate();
        for (IntPairType intPairType : bean.getInitialSolider()) {
            int soldierId = intPairType.getKey();
            int soldierNum = intPairType.getValue();
            scenePlayerEntity.getSoldierMgrComponent().addSoldier(soldierId, soldierNum, SoldierNumChangeReason.init);
        }
        scenePlayerEntity.getPowerComponent().updatePower(CommonEnum.PowerType.PT_SOLDIER, SoldierNumChangeReason.init);
    }

    public static void restoreScenePlayer(final SceneEntity sceneEntity, final long playerId, final Player.ScenePlayer fullAttr, final Player.ScenePlayer changeAttr) {
        final ScenePlayerProp prop = ScenePlayerProp.of(fullAttr, changeAttr);
        ScenePlayerEntity scenePlayerEntity = new ScenePlayerEntity(sceneEntity, playerId, prop, new ScenePlayerBuilder());
        sceneEntity.getPlayerMgrComponent().addScenePlayer(scenePlayerEntity);
        scenePlayerEntity.getDbComponent().beginSaveChangeToDb(changeAttr);
        LOGGER.info("{} restore {} success", sceneEntity, scenePlayerEntity);
        if (scenePlayerEntity.getClanId() != 0) {
            SceneClanEntity sceneClan = sceneEntity.getClanMgrComponent().getSceneClanOrNull(scenePlayerEntity.getClanId());
            if (sceneClan != null) {
                sceneClan.getMemberComponent().restorePlayer(playerId);
            } else {
                WechatLog.error("restore scenePlayer but clan not exist {} {}", playerId, scenePlayerEntity.getClanId());
            }
        }
        String name = scenePlayerEntity.getName();
        // 初始名必定包含下划线 正常改名不允许
        if (sceneEntity.isMainScene() && !name.contains(Constants.XIA_HUA_XIAN)) {
            NameHelper.syncToNameActor(sceneEntity.ownerActor(), scenePlayerEntity.getZoneId(), CommonEnum.NameType.PLAYER_NAME, name, playerId);
        }
    }
}

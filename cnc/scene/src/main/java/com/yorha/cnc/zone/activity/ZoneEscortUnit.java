package com.yorha.cnc.zone.activity;

import com.google.common.collect.Maps;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityTemplate;
import res.template.EventRankRewardsTemplate;
import res.template.RankTemplate;

import java.util.List;
import java.util.Map;

/**
 * 护送任务-zone
 *
 * <AUTHOR>
 */
public class ZoneEscortUnit extends ZoneActivityRankUnit {
    private static final Logger LOGGER = LogManager.getLogger(ZoneEscortUnit.class);

    public ZoneEscortUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    /**
     * 结算邮件、奖励、清榜等
     */
    @Override
    protected void settleRank() {
        final ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, activityId);
        final int rankId = getRankId(activityTemplate);

        LOGGER.info("ZoneEscortUnit: {} ask zone rank {}!", activityId, rankId);
        SsRank.DeleteRankAndGetTopAsk.Builder rankAsk = SsRank.DeleteRankAndGetTopAsk.newBuilder();
        rankAsk.setRankId(rankId);

        ActivityResService service = ResHolder.getResService(ActivityResService.class);
        final List<EventRankRewardsTemplate> rewardConfList = service.getRankReward(rankId);
        // 计算需要获取的排行榜排名范围
        Map<Integer, EventRankRewardsTemplate> rewardMap = Maps.newHashMap();
        for (EventRankRewardsTemplate template : rewardConfList) {
            IntPairType pairType = template.getRewardRangePair();
            if (pairType.getKey() > pairType.getValue()) {
                LOGGER.error("ZoneEscortUnit: {} templateId: {} RewardRangePair key > value", activityId, template.getId());
                continue;
            }
            for (int i = pairType.getKey(); i <= pairType.getValue(); i++) {
                rewardMap.put(i, template);
            }
        }
        RankTemplate rankTemplate = ResHolder.getTemplate(RankTemplate.class, rankId);
        int maxRank = rankTemplate.getMaxRank();
        rankAsk.setRange(CommonMsg.Range.newBuilder().setRangeStart(0).setRangeEnd(maxRank).build());

        // 按照排名发放奖励邮件
        ownerActor().<SsRank.DeleteRankAndGetTopAns>askZoneRank(getZoneId(), rankAsk.build()).onComplete((ans, err) -> {
            if (err != null) {
                LOGGER.error("ZoneEscortUnit: {} ask zone rank {} fail! err: {}", activityId, rankId, err);
                return;
            }
            final int mailId = getRewardMailId(activityTemplate);

            LOGGER.info("ZoneEscortUnit: {} get rank size: {}! ZoneEscortUnit", activityId, ans.getRangeMemberInfoMap());

            for (CommonMsg.MemberAllDto member : ans.getRangeMemberInfoMap().values()) {
                EventRankRewardsTemplate reward = rewardMap.get(member.getRank());

                if (reward == null) {
                    LOGGER.error("ClanEscortServiceImpl handleSendRankRewardCmd fail: rank={}", member.getRank());
                    return;
                }
                StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
                params.setMailTemplateId(mailId);
                // 自己的排名
                Struct.DisplayParam.Builder displayRankParam = Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_INT64)
                        .setNumber(member.getRank());
                // 自己的积分
                Struct.DisplayParam.Builder displayScoreParam = Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_INT64)
                        .setNumber(member.getScore());

                params.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                        .getDisplayDataBuilder()
                        .getParamsBuilder()
                        .addDatas(displayRankParam)
                        .addDatas(displayScoreParam);
                // 奖励
                List<IntPairType> rewardPairList = reward.getRewardPairList();
                for (IntPairType rewardPair : rewardPairList) {
                    Struct.ItemPair itemPair = Struct.ItemPair.newBuilder()
                            .setItemTemplateId(rewardPair.getKey())
                            .setCount(rewardPair.getValue())
                            .build();
                    params.getItemRewardBuilder()
                            .addDatas(itemPair);
                }

                MailUtil.sendClanMail(getZoneId(), member.getMemberId(), params.build());
            }
        });
    }
}

package com.yorha.cnc.scene.sceneplayer.devbuff;

import com.yorha.cnc.battle.event.BuffEvent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerMisc;
import res.template.BuffTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
public class ScenePlayerDevBuffMgr extends DevBuffMgrBase<ScenePlayerEntity> {
    public long pendingEndTime;

    public ScenePlayerDevBuffMgr(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    protected LOGLEVEL logLevel() {
        return LOGLEVEL.INFO;
    }

    @Override
    public DevBuffSysProp getDevBuffSys() {
        return getOwner().getProp().getDevBuffSysNew();
    }


    @Override
    public CommonEnum.DevBuffType getBuffType() {
        return CommonEnum.DevBuffType.DBT_PLAYER_BUFF;
    }

    @Override
    public DevBuffProp addDevBuff(int buffId,
                                  Long startTime,
                                  Long forceEndTime,
                                  CommonEnum.DevBuffType buffType,
                                  CommonEnum.DevBuffSourceType sourceType,
                                  Integer layer) {
        if (DevBuffUtil.isSceneBuff(buffId)) {
            if (DevBuffUtil.isPeaceShieldBuff(buffId)
                    && sourceType != CommonEnum.DevBuffSourceType.DBST_SYS
                    && !getOwner().getDevBuffComponent().canOpenPeaceShield()) {
                // 护盾相关
                // 玩家手动开启护盾校验
                LOGGER.error("player:{} buffId:{}, add peace shield failed. cause has battle army outside.", getOwner(), buffId);
                return null;
            }
            return super.addDevBuff(buffId, startTime, forceEndTime, buffType, sourceType, layer);
        } else {
            // player buff，tell到player上去加
            CommonMsg.DevBuffAddParam.Builder paramBuilder = CommonMsg.DevBuffAddParam.newBuilder()
                    .setDevBuffId(buffId)
                    .setBuffType(buffType)
                    .setSourceType(sourceType);
            if (startTime != null) {
                paramBuilder.setStartTime(startTime);
            }
            if (forceEndTime != null) {
                paramBuilder.setEndTime(forceEndTime);
            }
            if (layer != null) {
                paramBuilder.setLayer(layer);
            }
            CommonMsg.AddDevBuffAsk.Builder builder = CommonMsg.AddDevBuffAsk.newBuilder()
                    .setPlayerId(getOwner().getPlayerId())
                    .setParam(paramBuilder.build());
            SsPlayerMisc.AddDevBuffFromSceneCmd.Builder cmd = SsPlayerMisc.AddDevBuffFromSceneCmd.newBuilder()
                    .setCmd(builder);
            getOwner().tellPlayer(cmd.build());
            return null;
        }
    }

    @Override
    public DevBuffProp removeDevBuff(int buffId, Integer layer, boolean isExpired) {
        if (DevBuffUtil.isSceneBuff(buffId)) {
            return super.removeDevBuff(buffId, layer, isExpired);
        } else {
            // player buff，tell到player上去删除
            CommonMsg.DevBuffRemoveParam.Builder paramBuilder = CommonMsg.DevBuffRemoveParam.newBuilder()
                    .setDevBuffId(buffId);
            if (layer != null) {
                paramBuilder.setLayer(layer);
            }
            CommonMsg.RemoveDevBuffAsk.Builder builder = CommonMsg.RemoveDevBuffAsk.newBuilder()
                    .setPlayerId(getOwner().getPlayerId())
                    .setParam(paramBuilder.build());
            SsPlayerMisc.RemoveDevBuffFromSceneCmd.Builder cmd = SsPlayerMisc.RemoveDevBuffFromSceneCmd.newBuilder()
                    .setCmd(builder);
            getOwner().tellPlayer(cmd.build());
            return null;
        }
    }

    @Override
    protected void addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        getOwner().getTimerComponent().addTimerWithPrefix(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    protected void cancelTimer(String prefix, TimerReasonType timerReasonType) {
        getOwner().getTimerComponent().cancelTimer(timerReasonType, prefix);
    }

    @Override
    protected void afterAddDevBuff(DevBuffProp devBuff, int addLayer) {
        int buffId = devBuff.getDevBuffId();
        // 更新加成
        BuffTemplate buffTemplate = getBuffTemplate(buffId);
        getOwner().getAdditionComponent().updateAddition(AdditionProviderType.BUFF, buffTemplate.getType());

        if (DevBuffUtil.isNeedAddToCity(buffId)) {
            if (DevBuffUtil.isPeaceShieldBuff(buffId)) {
                // 护盾相关
                // 通知city开护盾
                getOwner().getMainCity().getPeaceShieldComponent().onPeaceShieldOn(buffId);
                LOGGER.info("{} peace shield on id:{} endTime:{} sourceType:{}", getOwner(), buffId, devBuff.getEndTime(), devBuff.getSourceType());
            } else {
                // 有city特效外显。要加到city上
                getOwner().getMainCity().getDevBuffComponent().addSceneDevBuff(buffId);
            }
        } else {
            BuffEvent buffEvent = new BuffEvent(CommonEnum.BattleLogBuffType.BLBT_ITEM, buffId, BuffEvent.OperationType.ADD);
            // 通知战斗中的部队
            for (ArmyEntity armyEntity : getOwner().getArmyMgrComponent().getMyArmyList()) {
                if (armyEntity.getBattleComponent().isInBattle()) {
                    armyEntity.getEventDispatcher().dispatch(buffEvent);
                }
            }
            // 通知战斗中的城池
            if (getOwner().getMainCity().getBattleComponent().isInBattle()) {
                getOwner().getMainCity().getEventDispatcher().dispatch(buffEvent);
            }
        }
    }

    @Override
    protected void afterRemoveDevBuff(DevBuffProp devBuffToRemove, boolean isExpired, int decLayer) {
        int buffId = devBuffToRemove.getDevBuffId();
        // 更新加成
        BuffTemplate buffTemplate = getBuffTemplate(buffId);
        getOwner().getAdditionComponent().updateAddition(AdditionProviderType.BUFF, buffTemplate.getType());

        if (DevBuffUtil.isNeedAddToCity(buffId)) {
            if (DevBuffUtil.isPeaceShieldBuff(buffId)) {
                // 护盾相关
                // 通知city删除护盾
                getOwner().getMainCity().getPeaceShieldComponent().onPeaceShieldOff(buffId, isExpired);
            } else {
                // 有city特效外显。从city上删掉
                getOwner().getMainCity().getDevBuffComponent().removeSceneDevBuff(buffId);
            }
        } else {
            BuffEvent buffEvent = new BuffEvent(CommonEnum.BattleLogBuffType.BLBT_ITEM, buffId, BuffEvent.OperationType.REMOVE);
            // 通知战斗中的部队
            for (ArmyEntity armyEntity : getOwner().getArmyMgrComponent().getMyArmyList()) {
                if (armyEntity.getBattleComponent().isInBattle()) {
                    armyEntity.getEventDispatcher().dispatch(buffEvent);
                }
            }
            // 通知战斗中的城池
            if (getOwner().getMainCity().getBattleComponent().isInBattle()) {
                getOwner().getMainCity().getEventDispatcher().dispatch(buffEvent);
            }
        }
    }

    @Override
    protected void addPendingTask(DevBuffProp devBuff) {
        pendingEndTime = devBuff.getEndTime();
        getOwner().getScene().getTickMgrComponent().addSchedule(getOwner(), SceneTimerReason.TIMER_DEV_BUFF, pendingEndTime - getOwner().getScene().now());
        LOGGER.debug("owner:{}, add dev buff timer id:{} pendingEndTime:{}", getOwner(), devBuff.getDevBuffId(), pendingEndTime);
    }

    @Override
    protected void cancelPendingTask() {
        if (pendingEndTime != 0) {
            LOGGER.debug("owner:{}, cancel dev buff timer id:{}, pendingEndTime:{}", getOwner(), pendingExpireBuffId, pendingEndTime);
            pendingExpireBuffId = 0;
            pendingEndTime = 0;
            getOwner().getScene().getTickMgrComponent().cancelSchedule(getOwner(), SceneTimerReason.TIMER_DEV_BUFF);
        }
    }

    @Override
    public void onPendingDevBuffExpired(long now) {
        pendingEndTime = 0;
        super.onPendingDevBuffExpired(now);
    }
}

package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.common.actor.SceneIdipService;
import com.yorha.proto.SsSceneIdip;

/**
 * idip
 *
 * <AUTHOR>
 */
public class SceneIdipServiceImpl implements SceneIdipService {

    private final SceneActor sceneActor;

    public SceneIdipServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public long getSceneId() {
        return this.sceneActor.getSceneId();
    }

    @Override
    public void handleSoldierAsk(SsSceneIdip.SoldierAsk ask) {
//        BigSceneEntity scene = sceneActor.getBigScene();
//        if (scene == null) {
//            throw new GeminiException("not in big scene, CurSceneId=" + getSceneId());
//        }
//
//        ScenePlayerEntity scenePlayer = getBigScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
//        if (!StringUtils.equals(ask.getOpenId(), scenePlayer.getOpenId())) {
//            playerActor.answer(SsPlayerIdip.ResourceAns.newBuilder().setExceptionId(ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()).build());
//            return;
//        }
//        int beforeCount = scenePlayer.getSoldierMgrComponent().getInCitySoldierNum(ask.getSoldierId());
//        if (ask.getValue() > 0) {
//            scenePlayer.getSoldierMgrComponent().addInCitySoldier(ask.getSoldierId(), ask.getValue(), SoldierNumChangeReason.idip);
//        } else {
//            int realSubCount = Math.min(beforeCount, -ask.getValue());
//            if (realSubCount > 0) {
//                scenePlayer.getSoldierMgrComponent().subInCitySoldier(ask.getSoldierId(), realSubCount, SoldierNumChangeReason.idip);
//            }
//        }
//        int afterCount = scenePlayer.getSoldierMgrComponent().getInCitySoldierNum(ask.getSoldierId());
//        LOGGER.info("SceneIdipServiceImpl handleSoldierAsk {} ask={} before={} after={}", scenePlayer, ask, beforeCount, afterCount);
//        sceneActor.answer(SsSceneIdip.SoldierAns.newBuilder().setBeforeValue(beforeCount).setAfterValue(afterCount).build());
    }
}

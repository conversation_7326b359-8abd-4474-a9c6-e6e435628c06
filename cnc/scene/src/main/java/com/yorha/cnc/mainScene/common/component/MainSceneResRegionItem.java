package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.mainScene.common.refresh.GoldBuildingRecycle;
import com.yorha.cnc.mainScene.common.refresh.GoldBuildingRefresh;
import com.yorha.cnc.mainScene.common.refresh.ResBuildingRecycle;
import com.yorha.cnc.mainScene.common.refresh.ResBuildingRefresh;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.resBuilding.ResBuildingFactory;
import com.yorha.cnc.scene.resBuilding.enums.RefreshType;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.resource.ResCollectService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstCollectTemplate;
import res.template.MapBuildingTemplate;
import res.template.RegionResourceTemplate;
import res.template.ResourceTemplate;

import java.util.*;

/**
 * 资源田州级管理单位
 *
 * <AUTHOR>
 */
public class MainSceneResRegionItem {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneResRegionItem.class);
    /**
     * 州id
     */
    private final int regionId;
    private final SceneEntity scene;
    /**
     * 资源类型 -> 资源等级 -> id ->entity
     */
    private final Map<CurrencyType, Map<Integer, Map<Long, ResBuildingEntity>>> building = new HashMap<>();
    /**
     * 金矿 资源等级 -> id ->entity
     */
    private final Map<Integer, Map<Long, ResBuildingEntity>> goldBuilding = new HashMap<>();
    /**
     * 资源田个数
     */
    private Integer buildingNum = 0;
    /**
     * 金矿个数
     */
    private Integer goldNum = 0;
    /**
     * 资源田列表  为了联盟占领时改联盟id用的
     * 片id-> entityList
     */
    private final Map<Integer, List<ResBuildingEntity>> partBuilding = new HashMap<>();
    /**
     * 金矿列表  为了联盟占领时改联盟id用的
     * 片id-> entityList
     */
    private final Map<Integer, List<ResBuildingEntity>> goldPartBuilding = new HashMap<>();
    /**
     * 刷新待扫描的片
     */
    private final Map<Integer, Integer> waitScanPartMap = new Int2IntOpenHashMap();
    private int waitCrateNum = 0;
    /**
     * 普通资源田 正在刷新类型   全局优先级高于补充  会覆盖 让补充刷新失效
     */
    private RefreshType refreshType = null;

    private RefreshType goldRefreshType = null;

    public MainSceneResRegionItem(int regionId, SceneEntity scene) {
        this.regionId = regionId;
        this.scene = scene;
    }

    public void globalRefresh(boolean isInit) {
        // 没配置
        RegionResourceTemplate template = ResHolder.getResService(ResCollectService.class).getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null || template.getNum() <= 0) {
            LOGGER.info("BigSceneResRegionItem refresh start global but no template region: {} {}", regionId, template);
            return;
        }
        LOGGER.info("BigSceneResRegionItem refresh start global region: {} buildingNum: {}", regionId, buildingNum);
        refreshType = RefreshType.GLOBAL;
        if (!isInit) {
            ((MainSceneObjMgrComponent) scene.getObjMgrComponent()).addRefreshTask(new ResBuildingRecycle(scene, regionId, refreshType));
        }
        ((MainSceneObjMgrComponent) scene.getObjMgrComponent()).addRefreshTask(new ResBuildingRefresh(scene, regionId));
    }

    public void onTaskFinish(int totalCreate) {
        refreshType = null;
        LOGGER.info("BigSceneResRegionItem refresh end region: {} totalCreate: {} current {}", regionId, totalCreate, buildingNum);
    }

    public void goldGlobalRefresh(boolean isInit) {
        // 没配置
        RegionResourceTemplate template = ResHolder.getResService(ResCollectService.class).getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null || template.getGoldNum() <= 0) {
            LOGGER.info("BigSceneResRegionItem refresh gold global but no template region: {} {}", regionId, template);
            return;
        }
        if (goldRefreshType == RefreshType.GLOBAL) {
            LOGGER.info("BigSceneResRegionItem is refreshing gold region: {} goldRefreshingRegion={}", regionId, goldNum);
            return;
        }
        LOGGER.info("BigSceneResRegionItem refresh start global gold region: {} regionGoldNum={}", regionId, goldNum);
        goldRefreshType = RefreshType.GLOBAL;
        if (!isInit) {
            ((MainSceneObjMgrComponent) scene.getObjMgrComponent()).addRefreshTask(new GoldBuildingRecycle(scene, regionId, goldRefreshType));
        }
        ((MainSceneObjMgrComponent) scene.getObjMgrComponent()).addRefreshTask(new GoldBuildingRefresh(scene, regionId));
    }

    public void onGoldRefreshTaskFinish(int totalCreate) {
        goldRefreshType = null;
        LOGGER.info("BigSceneResRegionItem refresh end gold region: {} totalCreate: {} current {}", regionId, totalCreate, goldNum);
    }

    /**
     * 构建刷新待选片队列
     */
    public int buildScanPartList() {
        ResCollectService resourceService = ResHolder.getResService(ResCollectService.class);
        RegionResourceTemplate template = resourceService.getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null) {
            LOGGER.error("BigSceneResRegionItem buildWaitPartList: {} no template", regionId);
            return 0;
        }
        waitScanPartMap.clear();
        waitCrateNum = 0;
        int minNum = template.getAreaMinResource();
        int maxNum = template.getAreaMaxResource();
        for (Map.Entry<CommonEnum.MapAreaType, List<Integer>> entry : scene.getMapTemplateDataItem().getRegionBornPartList(regionId).entrySet()) {
            for (Integer partId : entry.getValue()) {
                int curNum = 0;
                if (partBuilding.containsKey(partId)) {
                    curNum = partBuilding.get(partId).size();
                }
                if (curNum >= minNum) {
                    continue;
                }
                waitScanPartMap.put(partId, maxNum - curNum);
                waitCrateNum += maxNum - curNum;
            }
        }
        LOGGER.info("BigSceneResRegionItem buildWaitPartList regionId={} {} {}", regionId, waitScanPartMap.size(), waitCrateNum);
        return waitCrateNum;
    }

    /**
     * 刷新的资源田  补足个数
     */
    public int refreshResBuilding(int addScanNum) {
        // 待刷新优先队列
        ResCollectService resourceService = ResHolder.getResService(ResCollectService.class);
        int successCnt = 0;
        int scanCnt = 0;
        Iterator<Map.Entry<Integer, Integer>> iterator = waitScanPartMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, Integer> entry = iterator.next();
            for (int i = 0; i < entry.getValue(); i++) {
                int templateId = resourceService.getRandomResourceId(scene.getStoryId(), scene.getMapId(), entry.getKey());
                if (templateId == 0) {
                    continue;
                }
                if (createResBuilding(entry.getKey(), templateId)) {
                    successCnt++;
                }
                if (++scanCnt >= addScanNum) {
                    entry.setValue(entry.getValue() - i - 1);
                    if (successCnt != addScanNum) {
                        LOGGER.info("BigSceneResRegionItem refreshResBuilding but less regionId={} {}/{}", regionId, successCnt, addScanNum);
                    }
                    return successCnt;
                }
            }
            iterator.remove();
        }
        if (successCnt != addScanNum) {
            LOGGER.info("BigSceneResRegionItem refreshResBuilding but less regionId={} {}/{}", regionId, successCnt, addScanNum);
        }
        return successCnt;
    }

    /**
     * 刷新的黄金矿， 补足个数
     */
    public int refreshGoldBuilding(int addScanNum) {
        ResCollectService resCollectService = ResHolder.getResService(ResCollectService.class);
        int successCnt = 0;
        // 补足个数
        for (int i = 0; i < addScanNum; i++) {
            int templateId = resCollectService.getRandomGoldId(scene.getStoryId(), regionId);
            // 随机选个片
            int partId = scene.getMapTemplateDataItem().randomEffectivePart(regionId);
            if (createResBuilding(partId, templateId)) {
                successCnt++;
            }
        }
        if (successCnt != addScanNum) {
            LOGGER.info("BigSceneResRegionItem refreshGoldBuilding but less regionId={} {}/{}", regionId, successCnt, addScanNum);
        }
        return successCnt;
    }

    public int getGoldNeedCreateNum() {
        // 黄金矿只会全局刷，不会补刷
        RegionResourceTemplate template = ResHolder.getResService(ResCollectService.class).getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null) {
            LOGGER.error("BigSceneResRegionItem getGoldNeedCreateNum: {} no template", regionId);
            return 0;
        }
        // 需要刷新的数量
        return template.getGoldNum() - goldNum;
    }

    /**
     * 创建资源田
     */
    private boolean createResBuilding(int partId, int templateId) {
        List<Integer> gridList = MapGridDataManager.getGridList(scene.getMapId(), partId);
        if (CollectionUtils.isEmpty(gridList)) {
            return false;
        }
        int selfRadius = getRadius(templateId);
        MainSceneResMgrComponent resMgrComponent = getComponent();
        int index = RandomUtils.nextInt(gridList.size());
        for (int i = index; i < gridList.size() + index; i++) {
            int gridId = gridList.get(i % gridList.size());
            if (tryCreateResBuildingAtGrid(gridId, templateId, partId, selfRadius, resMgrComponent)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 尝试在指定网格创建资源田
     *
     * @param gridId          网格ID
     * @param templateId      模板ID
     * @param partId          片ID
     * @param selfRadius      自身半径
     * @param resMgrComponent 资源管理组件
     * @return 是否成功创建
     */
    private boolean tryCreateResBuildingAtGrid(int gridId, int templateId, int partId, int selfRadius, MainSceneResMgrComponent resMgrComponent) {
        if (resMgrComponent.checkResourceGrid(gridId)) {
            return false;
        }
        Point point = MapGridDataManager.getRandomBornPoint(gridId);
        int realRegionId = MapGridDataManager.getRegionId(scene.getMapId(), point);
        if (realRegionId != regionId) {
            LOGGER.debug("resBuilding should create in region{}, but create in region{}, part={}, gridId={}, point={}",
                    regionId, realRegionId, partId, gridId, point);
            return false;
        }
        if (!ErrorCode.isOK(BornPointHelper.collisionCheck(scene, point.getX(), point.getY(), selfRadius, 0))) {
            return false;
        }
        ResBuildingEntity entity = ResBuildingFactory.createResBuilding(scene, templateId, point.getX(), point.getY());
        if (entity == null) {
            return false;
        }
        resMgrComponent.occupyResourceGrid(entity.getCurPoint());
        onAddResBuilding(entity);
        return true;
    }

    /**
     * 回收资源田  返回现存数目
     */
    public Pair<Boolean, Integer> recycleResBuilding(RefreshType refreshType, int recycleNum) {
        if (refreshType != this.refreshType) {
            LOGGER.info("BigSceneResRegionItem recycleResBuilding but global replace replenish {}", regionId);
            return Pair.of(true, 0);
        }
        LOGGER.info("BigSceneResRegionItem recycleResBuilding {} {}", regionId, refreshType);
        int cnt = 0;
        MainSceneResMgrComponent resMgrComponent = getComponent();
        for (Map<Integer, Map<Long, ResBuildingEntity>> levelToEntity : building.values()) {
            if (levelToEntity == null || levelToEntity.isEmpty()) {
                continue;
            }
            for (Map<Long, ResBuildingEntity> map : levelToEntity.values()) {
                Iterator<Map.Entry<Long, ResBuildingEntity>> it = map.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<Long, ResBuildingEntity> next = it.next();
                    ResBuildingEntity entity = next.getValue();
                    if (!entity.isNeedRecycle(refreshType)) {
                        continue;
                    }
                    it.remove();
                    Point curPoint = entity.getCurPoint();
                    entity.recycle(false);
                    int partId = MapGridDataManager.getPartId(scene.getMapId(), curPoint);
                    buildingNum--;
                    partBuilding.get(partId).remove(entity);
                    resMgrComponent.releaseResourceGrid(curPoint);
                    if (++cnt > recycleNum) {
                        return Pair.of(false, cnt);
                    }
                }
            }
        }
        return Pair.of(true, cnt);
    }

    /**
     * 回收黄金矿
     */
    public Pair<Boolean, Integer> recycleGoldBuilding(RefreshType refreshType, int recycleNum) {
        ResCollectService resCollectService = ResHolder.getResService(ResCollectService.class);
        RegionResourceTemplate template = resCollectService.getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null) {
            LOGGER.error("BigSceneResRegionItem recycleGoldBuilding: {} no template", regionId);
            return Pair.of(true, 0);
        }
        LOGGER.info("BigSceneResRegionItem recycleGoldBuilding gold {} {}", regionId, refreshType);
        int cnt = 0;
        MainSceneResMgrComponent resMgrComponent = getComponent();
        for (Map<Long, ResBuildingEntity> map : goldBuilding.values()) {
            Iterator<Map.Entry<Long, ResBuildingEntity>> it = map.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<Long, ResBuildingEntity> next = it.next();
                ResBuildingEntity entity = next.getValue();
                if (!entity.isNeedRecycle(refreshType)) {
                    continue;
                }
                it.remove();
                Point curPoint = entity.getCurPoint();
                entity.recycle(false);
                int partId = MapGridDataManager.getPartId(scene.getMapId(), curPoint);
                goldNum--;
                goldPartBuilding.get(partId).remove(entity);
                resMgrComponent.releaseResourceGrid(curPoint);
                LOGGER.debug("BigSceneResRegionItem recycle gold entity {} from region{} part {}", entity, regionId, partId);
                if (++cnt > recycleNum) {
                    return Pair.of(false, cnt);
                }
            }
        }
        return Pair.of(true, cnt);
    }

    /**
     * 添加非黄金矿资源田
     */
    public void onAddResBuilding(ResBuildingEntity entity) {
        Point curPoint = entity.getCurPoint();
        int partId = MapGridDataManager.getPartId(scene.getMapId(), curPoint);
        CurrencyType type = entity.getTemplate().getResType();
        int level = entity.getTemplate().getResLevel();
        if (entity.getTemplate().getResType() == CurrencyType.DIAMOND) {
            goldPartBuilding.computeIfAbsent(partId, k -> new ArrayList<>()).add(entity);
            goldNum++;
            goldBuilding.computeIfAbsent(level, k -> new Long2ObjectOpenHashMap<>()).put(entity.getEntityId(), entity);
        } else {
            partBuilding.computeIfAbsent(partId, k -> new ArrayList<>()).add(entity);
            buildingNum++;
            Map<Integer, Map<Long, ResBuildingEntity>> levelMap = building.computeIfAbsent(type, k -> new Int2ObjectOpenHashMap<>());
            levelMap.computeIfAbsent(level, k -> new Long2ObjectOpenHashMap<>()).put(entity.getEntityId(), entity);
        }
    }

    /**
     * 移除矿资源田
     */
    public void onRemoveResBuilding(ResBuildingEntity entity) {
        Point curPoint = entity.getCurPoint();
        int partId = MapGridDataManager.getPartId(scene.getMapId(), curPoint);
        int level = entity.getTemplate().getResLevel();
        if (entity.getTemplate().getResType() == CurrencyType.DIAMOND) {
            goldPartBuilding.get(partId).remove(entity);
            if (goldNum != 0) {
                goldNum--;
            } else {
                LOGGER.error("BigSceneResRegionItem onRemoveResBuilding goldNum is 0 {}", regionId);
            }
            goldBuilding.get(level).remove(entity.getEntityId());
        } else {
            partBuilding.get(partId).remove(entity);
            if (buildingNum != 0) {
                buildingNum--;
            } else {
                LOGGER.error("BigSceneResRegionItem onRemoveResBuilding buildingNum is 0 {}", regionId);
            }
            building.get(entity.getTemplate().getResType()).get(level).remove(entity.getEntityId());
            // 黄金矿不补充刷新
            checkAndRefresh();
        }
    }

    /**
     * 补充刷新机制的check和触发
     */
    private void checkAndRefresh() {
        //正在刷新中  不用看了
        if (refreshType != null) {
            return;
        }
        RegionResourceTemplate template = ResHolder.getResService(ResCollectService.class).getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null) {
            LOGGER.error("BigSceneResRegionItem checkAndRefresh: {} no template", regionId);
            return;
        }
        int replenishTriggerNum = getReplenishTriggerNum();
        // 高于刷新线  不刷了
        if (buildingNum >= replenishTriggerNum) {
            return;
        }
        LOGGER.info("BigSceneResRegionItem refresh start replenish region: {} buildingNum: {} replenishTriggerNum: {}", regionId, buildingNum, replenishTriggerNum);
        refreshType = RefreshType.REPLENISH;
        ((MainSceneObjMgrComponent) scene.getObjMgrComponent()).addRefreshTask(new ResBuildingRecycle(scene, regionId, refreshType));
        ((MainSceneObjMgrComponent) scene.getObjMgrComponent()).addRefreshTask(new ResBuildingRefresh(scene, regionId));
    }

    /**
     * 获取补充刷新基准线
     */
    private int getReplenishTriggerNum() {
        RegionResourceTemplate template = ResHolder.getResService(ResCollectService.class).getRegionResourceTemplate(scene.getStoryId(), regionId);
        if (template == null) {
            LOGGER.error("BigSceneResRegionItem checkAndRefresh: {} no template", regionId);
            return 0;
        }
        int ratio = ResHolder.getInstance().getConstTemplate(ConstCollectTemplate.class).getReplenishRefreshRatio();
        return template.getNum() * ratio / GameLogicConstants.PERCENT_CONVERSION_UNITS;
    }

    /**
     * 地块联盟变化，更新资源田联盟id
     */
    public void refreshResBuildingClan(int partId, SceneClanEntity sceneClan) {
        List<ResBuildingEntity> list = partBuilding.get(partId);
        if (list != null && !list.isEmpty()) {
            for (ResBuildingEntity entity : list) {
                entity.refreshClan(sceneClan);
            }
        }
        list = goldPartBuilding.get(partId);
        if (list != null && !list.isEmpty()) {
            for (ResBuildingEntity entity : list) {
                entity.refreshClan(sceneClan);
            }
        }
    }

    private static int getRadius(int templateId) {
        int resId = ResHolder.getInstance().getValueFromMap(ResourceTemplate.class, templateId).getResID();
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, resId).getCollisionRadius();
    }

    public int getRegionId() {
        return regionId;
    }

    /**
     * 仅gm统计用
     */
    public Map<Integer, Integer> getGoldLevel2Num() {
        Map<Integer, Integer> ret = new HashMap<>();
        for (Map.Entry<Integer, Map<Long, ResBuildingEntity>> entry : goldBuilding.entrySet()) {
            ret.put(entry.getKey(), entry.getValue().size());
        }
        return ret;
    }

    public void dumpRegionResource(StringBuilder sb) {
        Map<Integer, Map<String, Integer>> tempMap = new HashMap<>();
        Map<CommonEnum.MapAreaType, List<Integer>> parts = scene.getMapTemplateDataItem().getRegionBornPartList(regionId);
        for (Map.Entry<CommonEnum.MapAreaType, List<Integer>> entry : parts.entrySet()) {
            sb.append("***** MapAreaType: ").append(entry.getKey()).append(" *****").append("\n");
            for (int part : entry.getValue()) {
                Map<String, Integer> partMap = tempMap.computeIfAbsent(part, (key) -> new HashMap<>());
                if (!partBuilding.containsKey(part)) {
                    continue;
                }
                for (ResBuildingEntity resource : partBuilding.get(part)) {
                    java.lang.String key = resource.getTemplate().getResType() + "_" + resource.getTemplate().getResLevel();
                    int num = partMap.getOrDefault(key, 0);
                    num++;
                    partMap.put(key, num);
                }
                sb.append("----- part: ").append(part).append(" -----").append("\n");
                sb.append(partMap).append("\n");
            }
        }
    }

    public void dumpRegionGoldResource(StringBuilder sb) {
        Map<Integer, Map<String, Integer>> tempMap = new HashMap<>();
        Map<CommonEnum.MapAreaType, List<Integer>> parts = scene.getMapTemplateDataItem().getRegionBornPartList(regionId);
        for (Map.Entry<CommonEnum.MapAreaType, List<Integer>> entry : parts.entrySet()) {
            sb.append("***** MapAreaType: ").append(entry.getKey()).append(" *****").append("\n");
            for (int part : entry.getValue()) {
                Map<String, Integer> partMap = tempMap.computeIfAbsent(part, (key) -> new HashMap<>());
                if (!goldPartBuilding.containsKey(part)) {
                    continue;
                }
                for (ResBuildingEntity resource : goldPartBuilding.get(part)) {
                    String key = resource.getTemplate().getResType() + "_" + resource.getTemplate().getResLevel();
                    int num = partMap.getOrDefault(key, 0);
                    num++;
                    partMap.put(key, num);
                }
                sb.append("----- part: ").append(part).append(" -----").append("\n");
                sb.append(partMap).append("\n");
            }
        }
    }

    public void dumpRegionResourceEntity(int type, int level, StringBuilder sb) {
        Map<Integer, Map<Long, ResBuildingEntity>> map1 = building.get(CommonEnum.CurrencyType.forNumber(type));
        if (map1 != null) {
            Map<Long, ResBuildingEntity> map2 = map1.get(level);
            if (map2 != null) {
                for (ResBuildingEntity v : map2.values()) {
                    sb.append(v.toString());
                    sb.append("\n");
                }
            }
        }
    }

    public int getBuildingNum() {
        return buildingNum;
    }

    public int getGoldNum() {
        return goldNum;
    }

    public int getPartBuildingNum(int partId) {
        return partBuilding.getOrDefault(partId, Collections.emptyList()).size();
    }

    private MainSceneResMgrComponent getComponent() {
        return (MainSceneResMgrComponent) scene.getResMgrComponent();
    }
}

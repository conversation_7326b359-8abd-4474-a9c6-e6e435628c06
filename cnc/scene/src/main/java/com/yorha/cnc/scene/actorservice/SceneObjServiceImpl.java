package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.mainScene.IMainScene;
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyFactory;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.search.MonsterSearch;
import com.yorha.common.actor.SceneObjectService;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.enums.DirectionEnum;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.map.BigSceneResService;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Ring;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MonsterCategory;
import com.yorha.proto.CommonEnum.SceneObjQuality;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.SsSceneObj.*;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BigSceneMonsterMilestoneTemplate;
import res.template.BigSceneSearchTemplate;
import res.template.ConstSkynetTemplate;
import res.template.MonsterTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 场景物体相关协议
 *
 * <AUTHOR>
 */
public class SceneObjServiceImpl implements SceneObjectService {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjServiceImpl.class);
    private final SceneActor sceneActor;

    public SceneObjServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    @Override
    public void handlePlayerSearchMonsterAsk(PlayerSearchMonsterAsk ask) {
        SceneEntity scene = getScene();
        if (scene == null || scene.isDestroy() || !scene.isMainScene()) {
            sceneActor.answer(PlayerSearchMonsterAns.getDefaultInstance());
            return;
        }
        BigSceneMonsterMilestoneTemplate monsterMilestoneTemplate = null;
        if (scene.isBigScene()) {
            monsterMilestoneTemplate = scene.getBigScene().getZoneEntity().getPropComponent().getMonsterMilestoneTemplate();
        }
        if (monsterMilestoneTemplate == null) {
            sceneActor.answer(PlayerSearchMonsterAns.getDefaultInstance());
            return;
        }
        final int searchMaxLevel = monsterMilestoneTemplate.getSearchMonsterLevelMax();
        final int searchMinLevel = monsterMilestoneTemplate.getSearchMonsterLevelMin();
        if (ask.getLevel() < searchMinLevel || ask.getLevel() > searchMaxLevel) {
            throw new GeminiException(ErrorCode.MONSTER_SEARCH_LEVEL_LIMT.getCodeId());
        }
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) scene.getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        MonsterEntity monster = scenePlayer.getSearchComponent().searchEntity(new MonsterSearch(ask.getLevel()));
        PlayerSearchMonsterAns.Builder ans = PlayerSearchMonsterAns.newBuilder();
        if (monster == null) {
            sceneActor.answer(ans.build());
            return;
        }
        ans.setMonsterId(monster.getEntityId()).getPosBuilder().setX(monster.getCurPoint().getX()).setY(monster.getCurPoint().getY());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleAddMonsterAsk(AddMonsterAsk ask) {
        LOGGER.info("search add monster {} {}", ask.getPlayerId(), ask.getLevel());
        final SceneEntity sceneEntity = getScene();
        int addMaxLevel = 0;
        if (sceneEntity.isBigScene()) {
            if (!ZoneContext.isServerOpen()) {
                LOGGER.info("AddMonsterAsk server is not open={}", ZoneContext.getServerOpenTsMs());
                addMaxLevel = ResHolder.getResService(BigSceneResService.class).getMonsterMilestone(getScene().getStoryId(), 0).getAddMonsterLevelMax();
            } else {
                BigSceneMonsterMilestoneTemplate monsterMilestoneTemplate = sceneEntity.getBigScene().getZoneEntity().getPropComponent().getMonsterMilestoneTemplate();
                addMaxLevel = monsterMilestoneTemplate.getAddMonsterLevelMax();
            }
        }
        if (addMaxLevel < ask.getLevel()) {
            LOGGER.info("search failed MonsterSearch failed {} 补怪等级上限 {} < {},限制此次补怪", ask.getPlayerId(), addMaxLevel, ask.getLevel());
            throw new GeminiException(ErrorCode.MONSTER_SEARCH_FAIL.getCodeId());
        }
        AddMonsterAns.Builder ans = AddMonsterAns.newBuilder();
        if (sceneEntity.isDestroy()) {
            sceneActor.answer(ans.build());
            return;
        }
        AbstractScenePlayerEntity scenePlayer = sceneEntity.getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer == null) {
            sceneActor.answer(ans.build());
            return;
        }
        BigSceneSearchTemplate searchTemplate = ResHolder.getResService(BigSceneResService.class).getSearchTemplate(ask.getLevel());
        Point mainCity = scenePlayer.getMainCity().getCurPoint();
        // 野怪生成范围军均分成n份，记录分割点，包含头尾
        int parts = searchTemplate.getPara();
        int interval = (searchTemplate.getMonsterMaxDis() - searchTemplate.getMonsterMinDis()) * 100;
        int range = interval / parts;
        List<Integer> rangeList = new ArrayList<>(parts + 1);
        rangeList.add(0);
        for (int part = 1; part < parts; part++) {
            rangeList.add(range * part);
        }
        rangeList.add(interval);
        LOGGER.debug("AddMonster, minDis={}, maxDis={}, rangeLis={}", searchTemplate.getMonsterMinDis(), searchTemplate.getMonsterMaxDis(), rangeList);
        List<DirectionEnum> dirList = Arrays.asList(DirectionEnum.values());
        Collections.shuffle(dirList);
        Point bornPoint = null;
        PathFindMgrComponent pathFindMgrComponent = sceneEntity.getPathFindMgrComponent();
        for (int index = 0; index < rangeList.size() - 1; index++) {
            int dis = searchTemplate.getMonsterMinDis() * 100 + RandomUtils.randomBetween(rangeList.get(index), rangeList.get(index + 1));
            LOGGER.debug("AddMonster, at round {}, monster dis={}", index + 1, dis);
            for (DirectionEnum direction : dirList) {
                int x = mainCity.getX() + direction.getAddX() * dis;
                int y = mainCity.getY() + direction.getAddY() * dis;
                Point point = Point.valueOf(x, y);
                if (!pathFindMgrComponent.isPointDynamicWalkable(point) || !pathFindMgrComponent.isPointStaticWalkable(point)) {
                    continue;
                }
                bornPoint = Point.valueOf(x, y);
                break;
            }
            if (bornPoint != null) {
                LOGGER.info("search loop add monster {} {}", ask.getPlayerId(), index + 1);
                break;
            }
        }

        if (bornPoint == null) {
            LOGGER.warn("search monster fail, scene:{} mainCity:{} monsterLevel:{}", sceneEntity.getEntityId(), mainCity, ask.getLevel());
            sceneActor.answer(ans.build());
            return;
        }

        // 创建野怪
        int storyId = getScene().ownerActor().getStoryId();
        List<MonsterTemplate> monsterTemplates = ResHolder.getResService(BigSceneResService.class).getMonsterTemplates(MonsterCategory.BIG_SCENE_ACTIVE, SceneObjQuality.NORMAL, ask.getLevel(), storyId);
        MonsterTemplate monsterTemplate = RandomUtils.randomList(monsterTemplates);
        if (monsterTemplate == null) {
            LOGGER.info("search add monster failed without template {} {}", ask.getPlayerId(), ask.getLevel());
            return;
        }
        SceneObjSpawnParam param = new SceneObjSpawnParam();
        param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(searchTemplate.getMonsterLifeTime()));
        param.setCreateType(CommonEnum.MonsterCreateType.SYSTEM_ADD);
        MonsterEntity monster = MonsterFactory.initMonster(sceneEntity, monsterTemplate.getId(), bornPoint, param);
        if (monster == null) {
            sceneActor.answer(ans.build());
            return;
        }
        monster.addIntoScene();
        ans.setMonsterId(monster.getEntityId()).getPosBuilder().setX(monster.getCurPoint().getX()).setY(monster.getCurPoint().getY());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleCheckCanBeAttackAsk(CheckCanBeAttackAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        ArmyFactory.checkCanAttackWithoutArmy(getScene(), scenePlayer, ask.getTargetId(), CommonEnum.ArmyActionType.AAT_None);
        sceneActor.answer(CheckCanBeAttackAns.getDefaultInstance());
    }

    @Override
    public void handleQueryMapBuildingIdAsk(QueryMapBuildingIdAsk ask) {
        BigSceneEntity sceneEntity = sceneActor.getBigScene();
        MapBuildingEntity mapBuilding = sceneEntity.getBuildingMgrComponent().getMapBuilding(ask.getPartId());
        sceneActor.answer(QueryMapBuildingIdAns.newBuilder().setId(mapBuilding.getEntityId()).build());
    }

    @Override
    public void handleGetMonsterNumAsk(GetMonsterNumAsk ask) {
        SceneEntity sceneEntity = sceneActor.getScene();
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        List<Integer> templates = ask.getTemplateIdsList();
        Set<SceneObjEntity> objEntities = sceneEntity.getAoiMgrComponent().getAffectedAoiSceneObjList(Circle.valueOf(scenePlayer.getCityComponent().getMyMainCity().getCurPoint(), ask.getParam()));
        int result = 0;
        for (SceneObjEntity obj : objEntities) {
            if (obj.getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
                continue;
            }
            if (!templates.contains(((MonsterEntity) obj).getTemplate().getId())) {
                continue;
            }
            result++;
        }
        sceneActor.answer(GetMonsterNumAns.newBuilder().setNum(result).build());
    }

    @Override
    public void handleRefreshActMonsterAsk(RefreshActMonsterAsk ask) {
        final SceneEntity sceneEntity = sceneActor.getScene();
        if (!sceneEntity.isMainScene()) {
            LOGGER.error("SceneObjServiceImpl handleRefreshActMonsterAsk not main scene");
            sceneActor.answer(RefreshActMonsterAns.newBuilder().setResult(false).build());
            return;
        }
        final IMainScene iMainScene = sceneEntity.getBigScene();
        final AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        final MonsterEntity monster = iMainScene.getObjMgrComponent().refreshActMonster(Circle.valueOf(scenePlayer.getCityComponent().getMyMainCity().getCurPoint(), ask.getRange()), ask.getTemplateId(), ask.getLifeTime());
        if (monster == null) {
            sceneActor.answer(RefreshActMonsterAns.newBuilder().setResult(false).build());
            return;
        }
        sceneActor.answer(RefreshActMonsterAns.newBuilder().setResult(true).setPoint(Struct.Point.newBuilder().setX(monster.getCurPoint().getX()).setY(monster.getCurPoint().getY()).build()).build());
    }

    @Override
    public void handleSummonSkynetMonsterAsk(SummonSkynetMonsterAsk ask) {
        LOGGER.info("SceneObjServiceImpl handleSummonSkynetMonsterAsk, player={} monster={}", ask.getOwnerPlayerId(), ask.getMonsterTemplateId());
        if (ask.getOwnerPlayerId() <= 0 || ask.getMonsterTemplateId() <= 0) {
            sceneActor.answer(SummonSkynetMonsterAns.newBuilder().setErrorCode(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId()).build());
            return;
        }
        if (!getScene().isMainScene()) {
            sceneActor.answer(SummonSkynetMonsterAns.newBuilder().setErrorCode(ErrorCode.SCENE_NOT_EXIT.getCodeId()).build());
            return;
        }
        long summonPlayerId = ask.getOwnerPlayerId();
        int templateId = ask.getMonsterTemplateId();
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(summonPlayerId);
        MonsterTemplate monsterTemplate = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, templateId);
        if (monsterTemplate.getCategory() != MonsterCategory.SKYNET_MONSTER) {
            sceneActor.answer(SummonSkynetMonsterAns.newBuilder().setErrorCode(ErrorCode.SKYNET_MONSTER_TYPE_FAIL.getCodeId()).build());
            return;
        }

        ConstSkynetTemplate consts = ResHolder.getConsts(ConstSkynetTemplate.class);
        int monsterLimit = consts.getMonsterLimit();
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) getScene().getObjMgrComponent();
        if (objMgrComponent.getSkynetMonsterNum() >= monsterLimit) {
            sceneActor.answer(SummonSkynetMonsterAns.newBuilder().setErrorCode(ErrorCode.SKYNET_MONSTER_FULL.getCodeId()).build());
            return;
        }

        SceneObjSpawnParam param = new SceneObjSpawnParam();
        if (monsterTemplate.getQuality() == SceneObjQuality.NORMAL) {
            param.setLifeTime(SystemClock.now() + TimeUtils.second2Ms(consts.getMonsterLifeTime()));

        }
        if (monsterTemplate.getQuality() == SceneObjQuality.ELITE) {
            param.setLifeTime(SystemClock.now() + TimeUtils.second2Ms(consts.getBossLifeTime()));
        }
        param.setSummonPlayerId(summonPlayerId);
        param.setCreateType(CommonEnum.MonsterCreateType.PLAYER_SUMMON);

        int minDis = consts.getMonsterRefreshRange().get(0).getKey() * 1000;
        int maxDis = consts.getMonsterRefreshRange().get(0).getValue() * 1000;

        Ring ring = Ring.valueOf(scenePlayer.getMainCity().getCurPoint(), maxDis, minDis);
        Point point = MonsterFactory.randomBornPoint(getScene(), templateId, ring, false);
        if (point == null) {
            sceneActor.answer(SummonSkynetMonsterAns.newBuilder().setErrorCode(ErrorCode.SKYNET_MONSTER_FULL.getCodeId()).build());
            return;
        }

        MonsterEntity monsterEntity = MonsterFactory.initMonster(getScene(), templateId, point, param, true);

        if (monsterEntity == null || monsterEntity.getEntityId() <= 0 || monsterEntity.getProp().getSummonPlayerId() <= 0) {
            LOGGER.info("SceneObjServiceImpl handleSummonSkynetMonsterAsk, monster create fail");
            throw new GeminiException(ErrorCode.MONSTER_NOT_EXIT);
        }
        monsterEntity.addIntoScene();
        objMgrComponent.addSkynetMonster(ask.getOwnerPlayerId(), monsterEntity.getEntityId());
        if (MonsterEntity.isSkynetBoss(monsterEntity.getTemplate())) {
            monsterEntity.initSkynet();
        }
        SummonSkynetMonsterAns build = SummonSkynetMonsterAns.newBuilder()
                .setMonsterId(monsterEntity.getEntityId())
                .setPoint(Struct.Point.newBuilder().setY(point.getY()).setX(point.getX()).build())
                .setExpireTsMs(monsterEntity.getExpireTsMs())
                .build();
        LOGGER.info("SceneObjServiceImpl handleSummonSkynetMonsterAsk success, build={}", build);

        sceneActor.answer(build);
    }
}

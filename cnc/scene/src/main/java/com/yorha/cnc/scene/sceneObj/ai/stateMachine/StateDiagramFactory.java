package com.yorha.cnc.scene.sceneObj.ai.stateMachine;

import com.yorha.cnc.scene.sceneObj.ai.stateMachine.impl.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

/**
 * 状态机工厂
 * <AUTHOR>
 */
public class StateDiagramFactory {
    public static AbstractStateDiagram newInstance(CommonEnum.AiBaseModelType type) {
        switch (type) {
            case AI_ACTIVE_MOSTER :{
                return new ActiveMonsterStateDiagram();
            }
            case AI_RALLY_MOSTER :{
                return new RallyMonsterStateDiagram();
            }
            case AI_ARROW_TOWER: {
                return new ArrowTowerStateDiagram();
            }
            case AI_SOLDIER_LINE: {
                return new SoldierLineStateDiagram();
            }
            case AI_GUARD: {
                return new GuardStateDiagram();
            }
            case AI_STAKE: {
                return new StakeStateDiagram();
            }
            default: {
                throw new GeminiException("unknown stateMachine:{}", type);
            }
        }
    }
}

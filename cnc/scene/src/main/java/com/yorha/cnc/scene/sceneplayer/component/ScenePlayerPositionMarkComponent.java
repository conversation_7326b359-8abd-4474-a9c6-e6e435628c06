package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.game.gen.prop.PositionMarkInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstPositionMarkTemplate;
import res.template.PositionMarkClanPicTemplate;
import res.template.PositionMarkPersonalPicTemplate;

/**
 * 坐标收藏 /分享组件
 *
 * <AUTHOR>
 */
public class ScenePlayerPositionMarkComponent extends AbstractComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerPositionMarkComponent.class);

    public ScenePlayerPositionMarkComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    public ErrorCode checkMarkAvaild(SsScenePlayer.MarkPositionAsk cmd) {
        // 坐标标记
        if (cmd.getActionType() == CommonEnum.PositionMarkActionType.PMAT_MARK) {
            // 个人坐标标记需要校验已标记个数
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
                if (getOwner().getProp().getPosMarkModel().getPersonalPosMarkMapSize() >= ResHolder.getInstance().getConstTemplate(ConstPositionMarkTemplate.class).getPerAllMarkRange()) {
                    throw new GeminiException(ErrorCode.MARK_PERONAL_OVER_LIMIT);
                }
            }
            // 军团坐标标记需要校验已标记个数
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_CLAN) {
                if (!getOwner().isInClan()) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "not in clan");
                }
            }
        }

        // 坐标修改
        if (cmd.getActionType() == CommonEnum.PositionMarkActionType.PMAT_EDIT) {
            // 个人坐标修改需要校验坐标id是否存在
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
                if (!getOwner().getProp().getPosMarkModel().getPersonalPosMarkMap().containsKey(cmd.getPositionMarkInfo().getMarkId())) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "personal mark not exist");
                }
            }
        }

        // 涉及标记图标
        if (cmd.getPositionMarkInfo().getMarkPicId() > 0) {
            // 个人坐标标记图标校验是否合法图标
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
                PositionMarkPersonalPicTemplate positionMarkPersonalPicTemplate = ResHolder.getInstance().getValueFromMap(PositionMarkPersonalPicTemplate.class, cmd.getPositionMarkInfo().getMarkPicId());
                if (positionMarkPersonalPicTemplate == null) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "personal mark pic not exist");
                }
            }
            // 军团坐标标记图标校验是否合法图标
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_CLAN) {
                PositionMarkClanPicTemplate positionMarkClanPicTemplate = ResHolder.getInstance().getValueFromMap(PositionMarkClanPicTemplate.class, cmd.getPositionMarkInfo().getMarkPicId());
                if (positionMarkClanPicTemplate == null) {
                    throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "clan mark pic not exist");
                }
            }
        }

        return ErrorCode.OK;
    }

    private void sendMarquee(CommonEnum.MarqueeType marqueeType) {
        int marqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(marqueeType);
        PlayerScene.Player_MarqueeMessage_NTF msg = PlayerScene.Player_MarqueeMessage_NTF.newBuilder().setMessageId(marqueeId).build();
        getOwner().sendMsgToClient(MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    public void markPosition(SsScenePlayer.MarkPositionAsk cmd) {
        //打标记
        if (cmd.getActionType() == CommonEnum.PositionMarkActionType.PMAT_MARK) {
            //组装标记信息
            long markId = getOwner().nextId();
            PositionMarkInfoProp markInfoProp = new PositionMarkInfoProp();
            markInfoProp.setMarkId(markId)
                    .setZoneId(cmd.getPositionMarkInfo().getZoneId())
                    .setMarkName(cmd.getPositionMarkInfo().getMarkName())
                    .setMarkPicId(cmd.getPositionMarkInfo().getMarkPicId())
                    .getPoint().setX(cmd.getPositionMarkInfo().getPoint().getX())
                    .setY(cmd.getPositionMarkInfo().getPoint().getY());
            // 新建个人标记
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
                getOwner().getProp().getPosMarkModel().getPersonalPosMarkMap().put(markId, markInfoProp);
                // 发跑马灯
                sendMarquee(CommonEnum.MarqueeType.MARK_PERSONAL_ADD);
                LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition mark personal ask:{}", getEntityId(), cmd);
                return;
            }

            // 新建联盟标记
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_CLAN) {
                tellToClan(markId, cmd);
                LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition mark clan ask:{}", getEntityId(), cmd);
                return;
            }
        }

        //修改标记
        if (cmd.getActionType() == CommonEnum.PositionMarkActionType.PMAT_EDIT) {
            //组装标记信息
            long markId = cmd.getPositionMarkInfo().getMarkId();

            // 编辑个人标记
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
                PositionMarkInfoProp positionMarkInfoProp = getOwner().getProp().getPosMarkModel().getPersonalPosMarkMap().get(markId);
                if (positionMarkInfoProp == null) {
                    throw new GeminiException("cant find mark :{}", markId);
                }
                positionMarkInfoProp.setMarkPicId(cmd.getPositionMarkInfo().getMarkPicId())
                        .setZoneId(cmd.getPositionMarkInfo().getZoneId())
                        .setMarkName(cmd.getPositionMarkInfo().getMarkName())
                        .getPoint().setX(cmd.getPositionMarkInfo().getPoint().getX()).setY(cmd.getPositionMarkInfo().getPoint().getY());
                // 发跑马灯
                sendMarquee(CommonEnum.MarqueeType.MARK_PERSONAL_EDIT);
                LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition edit personal ask:{}", getEntityId(), cmd);
                return;
            }

            // 编辑联盟标记
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_CLAN) {
                tellToClan(markId, cmd);
                LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition edit clan ask:{}", getEntityId(), cmd);
                return;
            }
        }

        //删除标记
        if (cmd.getActionType() == CommonEnum.PositionMarkActionType.PMAT_DELETE) {
            //组装标记信息
            long markId = cmd.getPositionMarkInfo().getMarkId();

            // 删除个人标记
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
                getOwner().getProp().getPosMarkModel().getPersonalPosMarkMap().remove(markId);
                // 发跑马灯
                sendMarquee(CommonEnum.MarqueeType.MARK_PERSONAL_DELETE);
                LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition edit personal ask:{}", getEntityId(), cmd);
                return;
            }

            // 删除联盟标记
            if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_CLAN) {
                tellToClan(markId, cmd);
                LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition edit clan ask:{}", getEntityId(), cmd);
                return;
            }
        }
        LOGGER.debug("player: {} ScenePlayerPositionMarkComponent markPosition unknown action type :{}", getEntityId(), cmd.getActionType());
    }

    public void setMarkReaded(SsScenePlayer.SetMarkReadedCmd cmd) {
        // 已读个人标记
        if (cmd.getPositionMarkType() == CommonEnum.PositionMarkType.PMT_PERSIONAL) {
            PositionMarkInfoProp positionMarkInfo = getOwner().getProp().getPosMarkModel().getPersonalPosMarkMap().get(cmd.getMarkId());
            if (positionMarkInfo == null) {
                throw new GeminiException("player :{} setMarkReaded personal markId:{} not found", getOwner().getPlayerId(), cmd.getMarkId());
            }
        }
    }

    public void onClanSync(int clanPositionMarkVersion) {
        getOwner().getProp().getPosMarkModel().setClanPosMarkVersion(clanPositionMarkVersion);
    }

    private void tellToClan(long markId, SsScenePlayer.MarkPositionAsk cmd) {
        SsClanAttr.ClanMarkPositionCmd.Builder builder = SsClanAttr.ClanMarkPositionCmd.newBuilder();
        builder.setPlayerId(getOwner().getPlayerId())
                .setActionType(cmd.getActionType())
                .setPositionMarkType(cmd.getPositionMarkType())
                .getPositionMarkInfoBuilder()
                .setMarkId(markId)
                .setZoneId(cmd.getPositionMarkInfo().getZoneId())
                .setMarkName(cmd.getPositionMarkInfo().getMarkName())
                .setMarkPicId(cmd.getPositionMarkInfo().getMarkPicId())
                .setOperatorName(getOwner().getName())
                .getPointBuilder().setX(cmd.getPositionMarkInfo().getPoint().getX())
                .setY(cmd.getPositionMarkInfo().getPoint().getY()).build();
        getOwner().getSceneClan().tellClan(builder.build());
    }
}

package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.SceneDungeonService;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsSceneDungeon.*;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;


/**
 * <AUTHOR>
 */
public class SceneDungeonServiceImpl implements SceneDungeonService {
    private static final Logger LOGGER = LogManager.getLogger(SceneDungeonServiceImpl.class);

    private final SceneActor sceneActor;

    public SceneDungeonServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    public long getSceneId() {
        return this.sceneActor.getSceneId();
    }

    @Override
    public void handleCreateDungeonAsk(CreateDungeonAsk ask) {
        sceneActor.answer(CreateDungeonAns.newBuilder().build());
    }

    /**
     * 进入副本
     * 1. 先call大世界  设置成进入副本状态，清理aoi等
     * 2. 再call副本    初始化数据等
     */
    @Override
    public void handleEnterDungeonAsk(EnterDungeonAsk ask) {
        getScene().getPlayerMgrComponent().playerEnterDungeon(ask.getPlayerId(), ask.getType());
        sceneActor.answer(EnterDungeonAns.newBuilder().build());
    }

    /**
     * 离开副本
     * 1. 先call副本  离开
     * 2. 再call大世界  重置状态，下发数据
     */
    @Override
    public void handleLeaveDungeonAsk(LeaveDungeonAsk ask) {
        // 大世界 接到进入副本的通知
        getScene().getPlayerMgrComponent().playerLeaveDungeon(ask.getPlayerId());
        sceneActor.answer(LeaveDungeonAns.newBuilder().build());
    }

    /**
     * 登录大世界  在副本中重连、顶号也会过来   isInDungeon会是true
     */
    @Override
    public void handlePlayerLoginAsk(PlayerLoginAsk ask) {
        LOGGER.info("{} atScene login start, {} handlePlayerLoginAsk player={}", LogKeyConstants.GAME_PLAYER_LOGIN, sceneActor, ask.getPlayerId());
        SceneEntity scene = getScene();
        PlayerLoginAns.Builder builder = PlayerLoginAns.newBuilder();
        if (scene == null || scene.isDestroy()) {
            builder.setIsOk(false);
            sceneActor.answer(builder.build());
            return;
        }
        final IActorRef sessionRef = RefFactory.fromPb(ask.getSessionRef());

        boolean isOk = scene.getPlayerMgrComponent().onPlayerLogin(ask.getPlayerId(), sessionRef, ask.getIsNewbie(), ask.getIntlNtfToken(), ask.getLanguage(), ask.getDungeonType());
        ScenePlayerEntity scenePlayer = sceneActor.getScenePlayer(ask.getPlayerId());
        if (scene.isBigScene()) {
            BigSceneEntity bigScene = scene.getBigScene();
            ZoneEntity zoneEntity = bigScene.getZoneEntity();
            // 王国加成
            builder.setZoneAdditionSys(zoneEntity.getAdditionComponent().getAllPlayerAdditions().getCopySsBuilder());
            // 移除注册限制
            scene.getPlayerMgrComponent().removeOpenIdRegisterLimit(ask.getOpenId());
        }
        // 补充数据校验部分
        if (scene.isMainScene()) {
            // 里程碑
            StructMsg.ZoneMilestoneInfoByLogin builderForValue = scene.getMileStoneOrNullComponent().buildMileStoneRewardWithPlayerStage(ask.getPlayerMilestoneInfo());
            builder.setZoneMilestoneInfo(builderForValue);
            // 天网野怪校验
            MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) getScene().getObjMgrComponent();
            builder.addAllSkynetMonsterList(objMgrComponent.validSkynetMonsterExist(ask.getSkynetMonsterListList()));
            // 行军占用英雄
            builder.addAllHeroList(scenePlayer.getArmyMgrComponent().getMyHeroList());
            builder.setClanId(scenePlayer.getClanId());
            // 侦察机占用玩家侧飞机
            Set<Long> planeList = scenePlayer.getPlaneComponent().getPlaneList();
            builder.addAllPlaneList(planeList);
            // 溃败部队检修
            try {
                scenePlayer.getArmyMgrComponent().checkRetreatArmy();
            } catch (Exception e) {
                LOGGER.error("checkRetreatArmy failed {} ", scenePlayer, e);
            }
            // 堡垒护盾检修
            try {
                var mainCity = scenePlayer.getMainCity();
                if (mainCity != null) {
                    mainCity.getSpecialSafeGuardComponent().loginCheckFortressGuard();
                }
            } catch (Exception e) {
                LOGGER.error("loginCheckFortressGuard failed {} ", scenePlayer, e);
            }
        }
        LOGGER.info("{} atScene login end, login scene player successful", LogKeyConstants.GAME_PLAYER_LOGIN);
        sceneActor.answer(builder.setIsOk(isOk).build());
    }

    @Override
    public void handlePlayerLogoutAsk(PlayerLogoutAsk ask) {
        LOGGER.info("handlePlayerLogoutAsk {} {}", sceneActor, ask.getPlayerId());
        getScene().getPlayerMgrComponent().onPlayerLogout(ask.getPlayerId());
        sceneActor.answer(PlayerLogoutAns.getDefaultInstance());
    }

    @Override
    public void handlePerformActionAsk(PerformActionAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }
}

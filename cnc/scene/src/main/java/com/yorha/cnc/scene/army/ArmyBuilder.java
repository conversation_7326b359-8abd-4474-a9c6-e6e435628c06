package com.yorha.cnc.scene.army;


import com.yorha.cnc.scene.army.component.*;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.sceneObj.component.SceneObjArrowComponent;
import com.yorha.game.gen.prop.ArmyProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class ArmyBuilder extends SceneObjBuilder<ArmyEntity, ArmyProp> {

    private final ScenePlayerArmyStatusProp statusProp;

    public ArmyBuilder(SceneEntity sceneEntity, long eid, ArmyProp prop, ScenePlayerArmyStatusProp statusProp) {
        super(sceneEntity, eid, prop);
        if (statusProp == null) {
            statusProp = new ScenePlayerArmyStatusProp();
            statusProp.setArmyId(eid).setState(CommonEnum.ArmyDetailState.ADS_STAYING);
        }
        this.statusProp = statusProp;
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getMove().getCurPoint();
    }

    public ArmyBattleComponent battleComponent(ArmyEntity owner) {
        if (getSceneEntity().isDungeon()) {
            return new DungeonArmyBattleComponent(owner);
        }
        return new SceneArmyBattleComponent(owner);

    }

    @Override
    public ArmyTransformComponent transformComponent(ArmyEntity owner) {
        return new ArmyTransformComponent(owner, getPointProp());
    }

    @Override
    public ArmyPropComponent propComponent(ArmyEntity owner) {
        return new ArmyPropComponent(owner);
    }

    public ScenePlayerArmyStatusProp getStatusProp() {
        return statusProp;
    }

    public ArmyMoveComponent moveComponent(ArmyEntity owner) {
        return new ArmyMoveComponent(owner);
    }

    @Override
    public SceneObjArrowComponent arrowComponent(ArmyEntity owner) {
        return new SceneObjArrowComponent(owner);
    }
}

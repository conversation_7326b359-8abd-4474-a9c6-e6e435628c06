package com.yorha.cnc.scene;

import com.yorha.cnc.mainScene.bigScene.BigSceneBuilder;
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.actorservice.*;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.actor.*;
import com.yorha.common.actor.node.ISceneActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.aoiView.manager.IActorForAoiTimer;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.config.ConfigObj;
import com.yorha.common.utils.id.SnowflakeIdGenerator;
import com.yorha.common.utils.id.ZoneSnowflakeFactory;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.navmesh.GeminiNav;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.TcaplusDb;
import com.yorha.proto.Zone;
import com.yorha.proto.Zoneside;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 一个场景一个actor
 *
 * <AUTHOR>
 */
public class SceneActor extends BaseGameActor implements SceneServices, ISceneActor, IActorForAoiTimer {
    private static final Logger LOGGER = LogManager.getLogger(SceneActor.class);

    private final long sceneId;
    private SceneClanServiceImpl clanService;
    private ScenePlayerServiceImpl playerService;
    private SceneObjServiceImpl objService;
    private SceneMapServiceImpl mapService;
    private SceneCityArmyServiceImpl cityArmyService;
    private SceneRallyAssistServiceImpl rallyAssistService;
    private SceneCollectServiceImpl collectService;
    private SceneInformationMgrServiceImpl informationMgrService;
    private ScenePlaneServiceImpl scenePlaneService;
    private SceneDungeonServiceImpl sceneDungeonService;
    private SceneActivityServiceImpl sceneActivityyService;
    private SceneKingdomService sceneKingdomService;
    private SceneMarqueeService sceneMarqueeService;
    private SceneIdipService sceneIdipService;
    private SceneMailService sceneMailService;
    private SceneActivityScheduleService activityScheduleService;
    private SceneEntity scene;
    /**
     * 挂载的副本寻路数据列表  actor销毁时保证释放
     */
    private final List<GeminiNav> navList = new ArrayList<>();

    private final SnowflakeIdGenerator idGenerator;

    public SceneActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.sceneId = Long.parseLong(self.getActorId());
        if (this.sceneId != ServerContext.getZoneId()) {
            throw new GeminiException("SceneActor init but sceneId {} not same to zoneId {}", this.sceneId, ServerContext.getZoneId());
        }
        initService();
        idGenerator = ZoneSnowflakeFactory.newInstance();
    }

    public void initService() {
        clanService = new SceneClanServiceImpl(this);
        playerService = new ScenePlayerServiceImpl(this);
        objService = new SceneObjServiceImpl(this);
        mapService = new SceneMapServiceImpl(this);
        cityArmyService = new SceneCityArmyServiceImpl(this);
        rallyAssistService = new SceneRallyAssistServiceImpl(this);
        collectService = new SceneCollectServiceImpl(this);
        informationMgrService = new SceneInformationMgrServiceImpl(this);
        scenePlaneService = new ScenePlaneServiceImpl(this);
        sceneDungeonService = new SceneDungeonServiceImpl(this);
        sceneActivityyService = new SceneActivityServiceImpl(this);
        sceneKingdomService = new SceneKingdomServiceImpl(this);
        sceneMarqueeService = new SceneMarqueeServiceImpl(this);
        sceneIdipService = new SceneIdipServiceImpl(this);
        sceneMailService = new SceneMailServiceImpl(this);
        activityScheduleService = new SceneActivityScheduleServiceImpl(this);
    }

    /**
     * 只允许ActorTimerController可以调用
     */
    @Nullable
    @Override
    public ActorTimer controllerAddTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return dangerousAddTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Nullable
    @Override
    public ActorTimer controllerAddRepeatTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit, boolean isFix) {
        return dangerousAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, isFix);
    }

    @Override
    @Nullable
    public ActorTimer addAoiTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit, boolean isFix) {
        return dangerousAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, isFix);
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    protected void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        if (getScene() == null) {
            return;
        }
        if (getScene().isBigScene()) {
            getBigScene().getZoneEntity().getSideActivityComponent().onReloadRes(updatedTemplates);
        }
    }

    @Override
    public void handleChangeServerStatus() {
        if (scene == null || !scene.isBigScene()) {
            return;
        }
        final String status = ClusterConfigUtils.getZoneConfig(this.getZoneId()).getStringItem("cur_server_status");
        final ServerOpenStatus curStatus = ServerOpenStatus.valueOf(status);
        LOGGER.info("SceneActor handleChangeServerStatus {} -> {}", ServerContext.getServerSetting().getOpenStatus(), curStatus);
        // 对外时
        if (curStatus == ServerOpenStatus.OPEN) {
            getScene().getPeaceShieldComponent().openGlobalPeaceShield(7200);
        }
        MonitorUnit.GAME_ZONE_STATUS_IS_INTERNAL.labels(
                        ServerContext.getBusId(),
                        ServerContext.getWorldIdStr(),
                        getZoneIdStr())
                .set(curStatus == ServerOpenStatus.INTERNAL ? 1 : 0);

        if (getScene() == null) {
            LOGGER.info("handleChangeServerStatus but scene is null");
            return;
        }
        if (!getScene().isBigScene()) {
            LOGGER.info("handleChangeServerStatus but scene is not big scene");
            return;
        }
        ZoneEntity zoneEntity = getBigScene().getZoneEntity();
        if (zoneEntity == null) {
            LOGGER.error("handleChangeServerStatus but zoneEntity is null");
            return;
        }
        zoneEntity.onServerOpenStatusChanged(curStatus);
    }

    @Override
    public void handleMultiLanguage(SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd ask) {
        if (scene == null || !scene.isBigScene()) {
            LOGGER.error("handleMultiLanguage but scene is not ok, {}", scene);
            return;
        }
        scene.getPlayerMgrComponent().broadcastOnlineClientWithLanguageCmd(ask);
    }

    @Override
    public void handleChangeOpenZoneTime() {
        if (!scene.isBigScene()) {
            return;
        }
        long newOpenZoneTime = ClusterConfigUtils.getZoneConfig(scene.getZoneId()).getLongItem("open_zone_time");
        LOGGER.info("gemini_system handleChangeOpenZoneTime zone: {} newOpenZoneTime: {}", getZoneId(), newOpenZoneTime);
        scene.getBigScene().getZoneEntity().refreshOpenZoneTime(newOpenZoneTime);
    }

    @Override
    public boolean initBigScene(boolean isFirstCreate, ZoneInfoProp prop, Zone.ZoneInfoEntity changedProp,
                                ZoneSideProp zoneSideProp, Zoneside.ZoneSideEntity zoneSideChangedProp,
                                List<ValueWithVersion<TcaplusDb.ClanTable.Builder>> clans,
                                List<ValueWithVersion<TcaplusDb.ScenePlayerTable.Builder>> players,
                                List<ValueWithVersion<TcaplusDb.SceneObjTable.Builder>> objs,
                                List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> mails) {
        final long bigSceneId = getBigSceneId();
        if (this.getSceneId() != bigSceneId) {
            LOGGER.error("not in big scene, CurSceneId={}, BigSceneId={}", getSceneId(), bigSceneId);
            return false;
        }
        if (scene != null) {
            LOGGER.warn("scene already init");
            return true;
        }
        LOGGER.info("begin init big world, sceneId={}", bigSceneId);
        GeminiStopWatch stopWatch = new GeminiStopWatch("initBigScene");
        try {
            BigSceneBuilder builder = new BigSceneBuilder();
            ZoneEntity zoneEntity = new ZoneEntity(this, prop, changedProp, zoneSideProp, zoneSideChangedProp);
            BigSceneEntity bigSceneEntity = new BigSceneEntity(this, builder, zoneEntity);
            bigSceneEntity.initAllComponents(stopWatch, clans, players, objs, mails);
            this.scene = bigSceneEntity;
            final String stat = StringUtils.format("gemini_perf initBigScene ok {} {}", this, stopWatch.stat());
            LOGGER.info(stat);
            // k8s console
            System.out.println(stat);
            // 上报起服后的监控
            reportMonitorOnBigSceneInitOk();
            LOGGER.info("init big world scene success");
        } catch (Exception e) {
            LOGGER.error("init big world failed ", e);
            return false;
        }
        return true;
    }

    private void reportMonitorOnBigSceneInitOk() {
        // bigScene初始化完毕后再上报监控
        ConfigObj zoneConfig = ClusterConfigUtils.getZoneConfig(getZoneId());
        final String status = ClusterConfigUtils.getZoneConfig(this.getZoneId()).getStringItem("cur_server_status");
        final ServerOpenStatus curStatus = ServerOpenStatus.valueOf(status);
        MonitorUnit.GAME_ZONE_STATUS_IS_INTERNAL.labels(
                        ServerContext.getBusId(),
                        ServerContext.getWorldIdStr(),
                        getZoneIdStr())
                .set(curStatus == ServerOpenStatus.INTERNAL ? 1 : 0);

        final int registerLimit = zoneConfig.getIntItem("register_limit");
        MonitorUnit.GAME_ZONE_STATUS_PLAYER_REGISTER_LIMIT.labels(
                        ServerContext.getBusId(),
                        ServerContext.getWorldIdStr(),
                        getZoneIdStr())
                .set(registerLimit);
        LOGGER.info("{} reportMonitorOnBigSceneInitOk status: {} register_limit: {}", getScene(), curStatus, registerLimit);
    }

    public long getSceneId() {
        return this.sceneId;
    }

    public SceneEntity getScene() {
        return scene;
    }

    public long nextId() {
        return idGenerator.nextId();
    }

    public int getStoryId() {
        BigSceneEntity bigSceneEntity = getBigScene();
        if (bigSceneEntity != null) {
            return 0;
        }
        return 0;
    }

    public BigSceneEntity getBigScene() {
        SceneEntity scene = getScene();
        if (scene != null && scene.isBigScene()) {
            return (BigSceneEntity) scene;
        }
        return null;
    }

    @Override
    public SceneActivityService getSceneActivityService() {
        return sceneActivityyService;
    }

    @Override
    public SceneActivityScheduleService getSceneActivityScheduleService() {
        return activityScheduleService;
    }

    @Override
    public SceneCityArmyService getSceneCityArmyService() {
        return cityArmyService;
    }

    @Override
    public SceneClanMgrService getSceneClanMgrService() {
        return clanService;
    }

    @Override
    public SceneCollectService getSceneCollectService() {
        return collectService;
    }

    @Override
    public SceneDungeonService getSceneDungeonService() {
        return sceneDungeonService;
    }

    @Override
    public SceneIdipService getSceneIdipService() {
        return sceneIdipService;
    }

    @Override
    public SceneInformationMgrService getSceneInformationMgrService() {
        return informationMgrService;
    }

    @Override
    public SceneMapService getSceneMapService() {
        return mapService;
    }

    @Override
    public SceneObjectService getSceneObjectService() {
        return objService;
    }

    @Override
    public ScenePlaneService getScenePlaneService() {
        return scenePlaneService;
    }

    @Override
    public ScenePlayerMgrService getScenePlayerMgrService() {
        return playerService;
    }

    @Override
    public SceneRallyAssistService getSceneRallyAssistService() {
        return rallyAssistService;
    }

    @Override
    public SceneKingdomService getSceneKingdomService() {
        return sceneKingdomService;
    }

    @Override
    public SceneMailService getSceneMailService() {
        return sceneMailService;
    }

    @Override
    public SceneMarqueeService getSceneMarqueeService() {
        return sceneMarqueeService;
    }

    public void addNav(GeminiNav nav) {
        navList.add(nav);
    }

    public void removeNav(GeminiNav nav) {
        navList.remove(nav);
    }

    @Override
    public void onStopping() {
        SceneEntity scene = getScene();
        // 处理下Actor和Entity还没绑定情况下的停服
        if (scene != null) {
            scene.onServerStop();
        }
        super.onStopping();
    }

    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
        if (scene != null) {
            scene.deleteObj();
            scene = null;
        }
        for (GeminiNav nav : navList) {
            nav.release();
        }
        navList.clear();
    }

    public ScenePlayerEntity getScenePlayer(long playerId) {
        return getScene().getBigScene().getPlayerMgrComponent().getScenePlayer(playerId);
    }

    public ScenePlayerEntity getScenePlayerOrNull(long playerId) {
        return getScene().getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
    }
}

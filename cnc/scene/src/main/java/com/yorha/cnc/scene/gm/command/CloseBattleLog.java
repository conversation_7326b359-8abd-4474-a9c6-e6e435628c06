package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 打开战斗日志
 *
 * <AUTHOR>
 */
public class CloseBattleLog implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(CloseBattleLog.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        LOGGER.info("closeBattleLog");
        actor.getScene().getBattleGroundComponent().closeLogSwitch();
    }

    @Override
    public String showHelp() {
        return "ResetMapBuilding id={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAPBUILDING;
    }
}

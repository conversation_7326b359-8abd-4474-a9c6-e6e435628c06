package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.tick.SceneSchedule;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.entity.tick.SceneTimerItem;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.wechatlog.WechatLog;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class TickMgrComponent extends SceneComponent {
    public static final Logger LOGGER = LogManager.getLogger(TickMgrComponent.class);
    /**
     * 需要tick的对象列表
     */
    private final Map<SceneTickReason, Set<SceneSchedule>> tickSet = Maps.newEnumMap(SceneTickReason.class);
    private final Map<SceneTickReason, Set<SceneSchedule>> waitAddSet = Maps.newEnumMap(SceneTickReason.class);
    private final Map<SceneTickReason, Set<SceneSchedule>> waitRemoveSet = Maps.newEnumMap(SceneTickReason.class);
    /**
     * timer的对象优先队列
     */
    private final Map<SceneTimerReason, PriorityQueue<SceneTimerItem>> timerSet = Maps.newEnumMap(SceneTimerReason.class);

    /**
     * 本tick中改变过位置的entity集合
     */
    private final Set<Long> curTickChangePointEntityId = new LongOpenHashSet();
    /**
     * 需要刷新追击的物体集合
     * 主动移动方
     */
    private final Map<Long, SceneObjMoveComponent> needRefreshChaseMap = new ConcurrentHashMap<>();

    public TickMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void onTick(GeminiStopWatch watch) {
        // tick
        for (SceneTickReason reason : SceneTickReason.values()) {
            if (!tickSet.containsKey(reason) || tickSet.get(reason).isEmpty()) {
                continue;
            }
            try {
                tickByReason(reason);
            } catch (Exception e) {
                LOGGER.error("TickMgrComponent tickByReason reason={} ", reason, e);
            }
            watch.mark(reason.name());
        }
        // timer
        for (SceneTimerReason reason : SceneTimerReason.values()) {
            if (!timerSet.containsKey(reason) || timerSet.get(reason).isEmpty()) {
                continue;
            }
            try {
                timerByReason(reason);
            } catch (Exception e) {
                LOGGER.error("TickMgrComponent timerByReason reason={} ", reason, e);
            }
            watch.mark(reason.name());
        }
        // 刷新追击
        for (SceneObjMoveComponent moveComponent : needRefreshChaseMap.values()) {
            try {
                moveComponent.tryRefreshChasePath();
            } catch (Exception e) {
                LOGGER.error("TickMgrComponent needRefreshChaseMap tryRefreshChasePath ", e);
            }
        }
        curTickChangePointEntityId.clear();
        watch.mark("ChaseRefresh");
    }

    public void afterTick() {
        // 将tick过程中加入tick的正式加入
        for (Map.Entry<SceneTickReason, Set<SceneSchedule>> entry : waitAddSet.entrySet()) {
            tickSet.computeIfAbsent(entry.getKey(), (k) -> new ObjectOpenHashSet<>()).addAll(entry.getValue());
        }
        waitAddSet.clear();
        for (Map.Entry<SceneTickReason, Set<SceneSchedule>> entry : waitRemoveSet.entrySet()) {
            if (tickSet.containsKey(entry.getKey())) {
                tickSet.get(entry.getKey()).removeAll(entry.getValue());
            }
        }
        waitRemoveSet.clear();
    }


    private void tickByReason(SceneTickReason reason) {
        Set<SceneSchedule> sceneSchedules = tickSet.get(reason);
        if (sceneSchedules == null) {
            LOGGER.error("TickMgrComponent tickByReason but is null {}", reason);
            return;
        }
        Iterator<SceneSchedule> iterator = sceneSchedules.iterator();
        while (iterator.hasNext()) {
            SceneSchedule next = iterator.next();
            try {
                final Set<SceneSchedule> waitRemove = waitRemoveSet.get(reason);
                if ((waitRemove != null) && (waitRemove.contains(next))) {
                    iterator.remove();
                    continue;
                }
                if (next.isDestroy()) {
                    LOGGER.error("scene tick destroy reason: {} obj: {} ", reason, next);
                    iterator.remove();
                    continue;
                }
                if (!next.onTickDispatch(reason)) {
                    LOGGER.error("scene tick cant handle reason: {} obj: {} ", reason, next);
                    iterator.remove();
                }
            } catch (Exception e) {
                if (GeminiException.isLogicException(e)) {
                    LOGGER.info("scene tick failed. reason: {} obj: {} ", reason, next, e);
                } else {
                    WechatLog.error("scene tick error. reason: {} obj: {} ", reason, next, e);
                }
            }
        }
    }

    private void timerByReason(SceneTimerReason reason) {
        SceneTimerItem peek;
        long now = getOwner().now();
        PriorityQueue<SceneTimerItem> buffTimer = timerSet.get(reason);
        do {
            peek = buffTimer.peek();
            if (peek == null || peek.getNextExecuteTsMs() > now) {
                break;
            }
            buffTimer.poll();
            if (peek.getObj().isDestroy()) {
                LOGGER.error("scene timer destroy reason: {} obj: {} ", reason, peek.getObj());
                continue;
            }
            try {
                peek.getObj().onTimerDispatch(reason);
            } catch (Exception e) {
                WechatLog.error("scene timer error. reason: {} obj: {} ", reason, peek.getObj(), e);
            }
        } while (true);
    }

    public void addTick(SceneSchedule obj, SceneTickReason reason) {
        waitRemoveSet.getOrDefault(reason, Collections.emptySet()).remove(obj);
        waitAddSet.getOrDefault(reason, Collections.emptySet()).remove(obj);
        if (getOwner().isInTick()) {
            waitAddSet.computeIfAbsent(reason, (k) -> new ObjectOpenHashSet<>()).add(obj);
            return;
        }
        tickSet.computeIfAbsent(reason, (k) -> new ObjectOpenHashSet()).add(obj);
    }

    public void removeTick(SceneSchedule obj, SceneTickReason reason) {
        waitRemoveSet.getOrDefault(reason, Collections.emptySet()).remove(obj);
        waitAddSet.getOrDefault(reason, Collections.emptySet()).remove(obj);
        if (!tickSet.containsKey(reason)) {
            return;
        }
        if (getOwner().isInTick()) {
            waitRemoveSet.computeIfAbsent(reason, (k) -> new ObjectOpenHashSet()).add(obj);
            return;
        }
        tickSet.get(reason).remove(obj);
    }

    public void addSchedule(SceneSchedule obj, SceneTimerReason reason, long delay) {
        SceneTimerItem item = new SceneTimerItem(obj, getOwner().now() + delay);
        timerSet.computeIfAbsent(reason, (k) -> new PriorityQueue<>(Comparator.comparingLong(SceneTimerItem::getNextExecuteTsMs))).add(item);
    }

    public void cancelSchedule(SceneSchedule obj, SceneTimerReason reason) {
        if (!timerSet.containsKey(reason)) {
            return;
        }
        Iterator<SceneTimerItem> iterator = timerSet.get(reason).iterator();
        while (iterator.hasNext()) {
            SceneTimerItem next = iterator.next();
            if (next.getObj().equals(obj)) {
                iterator.remove();
                return;
            }
        }
    }

    public void registerChangePointEntity(long eid) {
        curTickChangePointEntityId.add(eid);
    }

    /**
     * 查询本轮tick中对象是否改变过位置
     */
    public boolean isCurTickChangePoint(long eid) {
        return curTickChangePointEntityId.contains(eid);
    }

    /**
     * 注册刷新追击  只有在自己的追击目标是可移动目标的情况下 才需要刷新
     */
    public void registerRefreshChase(SceneObjMoveComponent moveComponent) {
        needRefreshChaseMap.put(moveComponent.getEntityId(), moveComponent);
    }

    public void unRegisterRefreshChase(long eid) {
        needRefreshChaseMap.remove(eid);
    }
}

package com.yorha.cnc.scene.areaSkill;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Ring;
import com.yorha.game.gen.prop.AreaSkillProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RangeSkillTemplate;

/**
 * <AUTHOR>
 * @date 2023/4/12
 */
public class AreaSkillFactory {
    private static final Logger LOGGER = LogManager.getLogger(AreaSkillFactory.class);

    /**
     * 创建一个区域技能
     *
     * @param scene            scene
     * @param ownerScenePlayer 主人，手动超武时填充
     * @param owner            主人，战斗内释放区域技能时填充
     * @param areaSkillId      区域技能id
     * @param point            选中初始点
     * @param lifeTime         生命周期（秒），null时读配置
     * @return AreaSkillEntity
     */
    public static AreaSkillEntity createAreaSkill(SceneEntity scene, AbstractScenePlayerEntity ownerScenePlayer, BattleRole owner, int areaSkillId, Point point, Integer lifeTime) {
        // 找出生点
        Point bornPoint;
        RangeSkillTemplate template = ResHolder.getTemplate(RangeSkillTemplate.class, areaSkillId);
        bornPoint = getConfigBornPoint(scene, point, template);
        if (bornPoint == null) {
            LOGGER.error("AreaSkillErrLog {} {} {}, createAreaSkill failed. bornPoint is null areaSkillId:{}, basePoint:{}", scene, ownerScenePlayer, owner, areaSkillId, point);
            return null;
        }

        if (lifeTime != null && lifeTime <= 0) {
            LOGGER.error("AreaSkillErrLog {} {} {}, createAreaSkill failed. lifeTime invalid areaSkillId:{}, lifeTime:{}", scene, ownerScenePlayer, owner, areaSkillId, lifeTime);
            return null;
        }

        if (ownerScenePlayer == null && owner == null) {
            LOGGER.error("AreaSkillErrLog createAreaSkill failed. scenePlayer and owner is null");
            return null;
        }

        int troopId = template.getTroopIndex();
        AreaSkillProp prop = new AreaSkillProp();
        prop.getTroop().mergeFromSs(TroopHelper.getTroopBuilder(ResHolder.getInstance(), troopId));
        prop.setTemplateId(troopId);
        prop.getPoint().setX(bornPoint.getX()).setY(bornPoint.getY());

        prop.unMarkAll();

        long areaSkillEntityId = scene.ownerActor().nextId();
        AreaSkillBuilder builder = new AreaSkillBuilder(scene, areaSkillEntityId, prop);
        AreaSkillEntity areaSkillEntity = new AreaSkillEntity(builder, ownerScenePlayer, owner, lifeTime == null ? template.getLifetime() : lifeTime, areaSkillId);
        areaSkillEntity.addIntoScene();
        areaSkillEntity.getBattleComponent().onAreaSkillCreated();
        return areaSkillEntity;
    }

    /**
     * 根据范围配置选点
     */
    private static Point getConfigBornPoint(SceneEntity scene, Point point, RangeSkillTemplate template) {
        if (template.getCreateRange() == CommonEnum.AreaSkillBornPointType.ASBPT_RING_RANDOM) {
            int innerR = template.getRangeList().get(0);
            int outerR = template.getRangeList().get(1);
            return selectBornPointByRing(scene, point, innerR, outerR);
        }
        if (template.getCreateRange() == CommonEnum.AreaSkillBornPointType.ASBPT_SPECIFIC_POINT) {
            return point;
        }
        return null;
    }

    /**
     * 环形选点
     *
     * @param point  基础点
     * @param innerR 内圆半径
     * @param outerR 外圆半径
     */
    public static Point selectBornPointByRing(SceneEntity scene, Point point, int innerR, int outerR) {
        if (scene == null || point == null) {
            LOGGER.error("AreaSkillErrLog selectBornPointByRing scene={} point={}", innerR, outerR);
            return null;
        }
        if (!Ring.checkInnerOuterData(innerR, outerR)) {
            LOGGER.error("AreaSkillErrLog selectBornPointByRing innerR={} outerR={}", innerR, outerR);
            return null;
        }
        Ring shape = Ring.valueOf(point, outerR, innerR);
        return BornPointHelper.randomRingBornPoint(scene, shape, 0, true);
    }
}

package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.ClanBuildData;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.common.constant.Constants;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 每个军团建造某类建筑的数量
 * 参数：要求的领土数量
 * 参数：要求的领土类型
 * 参数：要求的领土等级
 *
 * <AUTHOR>
 */
public class ClanBuildBuildingMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_CLAN;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_CLAN_BUILD;
    }


    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanBuildData) {
            ClanBuildData data = (ClanBuildData) taskData;
            if (!matchParam(data)) {
                return;
            }
            Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
            MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.computeIfAbsent(data.getClanId(), (key) -> {
                MileStoneClanInfoProp prop = new MileStoneClanInfoProp();
                prop.setClanId(key);
                return prop;
            });
            setClanScore(mileStoneClanInfoProp, data.getClanId(), mileStoneClanInfoProp.getScore() + 1, "onClanMapBuildFin");
        }
    }

    private boolean matchParam(ClanBuildData data) {
        int level = getBuildLevel();
        for (int buildType : getBuildTypes()) {
            // 存在指定等级的联盟建筑建筑（level=0为任意等级）
            if (buildType == data.getBuildType() && (level == 0 || level == data.getBuildLevel())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 满足积分
     */
    @Override
    public List<MileStoneClanInfoProp> filterMeetScore(List<MileStoneClanInfoProp> rankProp) {
        String[] taskParam = getTaskParamById();
        if (taskParam.length < 1) {
            LOGGER.error("ClanBuildBuildingMileStoneHandler param error. taskParam={} prop={}", taskParam, getProp());
        }
        final int targetScore = getBuildNum();
        return rankProp.stream().filter(it -> it.getScore() >= targetScore).collect(Collectors.toList());
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }

    private int[] getBuildTypes() {
        String[] buildTypes = getTaskParamById()[0].split(Constants.BAN_JIAO_DOU_HAO);
        int[] ret = new int[buildTypes.length];
        for (int i = 0; i < buildTypes.length; i++) {
            ret[i] = Integer.parseInt(buildTypes[i]);
        }
        return ret;
    }

    private int getBuildLevel() {
        return Integer.parseInt(getTaskParamById()[1]);
    }

    private int getBuildNum() {
        return Integer.parseInt(getTaskParamById()[2]);
    }
}

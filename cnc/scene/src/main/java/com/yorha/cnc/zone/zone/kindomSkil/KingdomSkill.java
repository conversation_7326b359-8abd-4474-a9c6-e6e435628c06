package com.yorha.cnc.zone.zone.kindomSkil;

import com.google.common.collect.Maps;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.kindomSkil.skillEffects.*;
import com.yorha.common.enums.error.ErrorCode;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.Map;

/**
 * 王国技能
 *
 * <AUTHOR>
 */
public enum KingdomSkill {
    /**
     * 赋税
     */
    GAIN_TAX(1, new GainTax()),

    /**
     * 囚笼
     */
    LIMIT_MOVE_CITY(2, new LimitMoveCity()),

    /**
     * 统御
     */
    LEAD_TEAM(3, new LeadTeam()),

    /**
     * 驱逐
     */
    EXPEL(4, new Expel()),
    ;


    /**
     * 策划配表id
     */
    private final int skillId;

    /**
     * 技能效果
     */
    private final ISkillEffect skillEffect;

    /**
     * 王国技能Map<skillId, KindomSkill>
     */
    private static final Map<Integer, KingdomSkill> KINDOM_SKIILS = Maps.newHashMap();

    static {
        for (KingdomSkill skill : KingdomSkill.values()) {
            if (KINDOM_SKIILS.put(skill.skillId, skill) != null) {
                throw new IllegalArgumentException("duplicate skill id: " + skill.skillId);
            }
        }
    }

    KingdomSkill(int skillId, ISkillEffect skillEffect) {
        this.skillId = skillId;
        this.skillEffect = skillEffect;
    }

    public ErrorCode checkCanUse(int skillId, long targetId, int targetZoneId, ZoneEntity zoneEntity) {
        return this.skillEffect.checkCanUse(skillId, targetId, targetZoneId, zoneEntity);
    }

    /**
     * 技能生效
     *
     * @param skillId    策划配置的技能id
     * @param targetId   目标id（语义取决于策划案）
     * @param zoneEntity 调用方Entity
     */
    public void takeEffect(int skillId, long targetId, ZoneEntity zoneEntity, int targetZoneId) {
        this.skillEffect.effect(skillId, targetId, zoneEntity, targetZoneId);
    }

    /**
     * 根据skillId获取技能
     *
     * @param skillId 技能Id
     * @return KindomSkill or Null
     */
    @Nullable
    public static KingdomSkill getBySkillId(int skillId) {
        return KINDOM_SKIILS.getOrDefault(skillId, null);
    }

    public int getSkillId() {
        return this.skillId;
    }
}
package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.game.gen.prop.Int32BuffMapProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.yorha.cnc.battle.buf.Buff;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class SceneObjBuffComponent extends SceneObjComponent<SceneObjEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjBuffComponent.class);

    public SceneObjBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    protected abstract Int32BuffMapProp getData();

    public void remove(int groupId) {
        getData().remove(groupId);
    }

    public void add(Buff buff) {
        getData().put(buff.getTemplate().getEffectGroupId(), buff.getData());
    }

    public void clear() {
        getData().clear();
    }

    public void addBuff(List<Integer> buffIdList) {
        for (Integer buffId : buffIdList) {
            addBuff(buffId, 0);
        }
    }

    public void addBuff(int buffId, int lifeCycle) {
        LOGGER.info("SceneObjBuffComponent addBuff {} buffId={} lifeCycle:{}", getOwner(), buffId, lifeCycle);
        BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
        EffectContext build = EffectContext.newBuilder()
                .setCastRole(battleRole)
                .setSkillId(0)
                .setType(CommonEnum.BattleLogSkillType.BLST_NONE)
                .setHeroId(0)
                .setEffectId(0)
                .setLeaderRoleId(getOwner().getBattleComponent().getLeaderRoleId())
                .setAttachBuffId(0)
                .setDotContext(null)
                .build(false);
        battleRole.getBuffHandler().addBuffWithLife(battleRole, buffId, 1, lifeCycle, build);
    }

    public void removeBuff(List<Integer> buffIdList) {
        LOGGER.debug("SceneObjBuffComponent addBuff {} buffIdList={}", this, buffIdList);
        BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
        for (Integer buffId : buffIdList) {
            battleRole.getBuffHandler().removeBuffById(buffId);
        }
    }
}

package com.yorha.cnc.scene.sceneObj.move;

import com.yorha.cnc.scene.pathfinding.manager.PathFindingManager;
import com.yorha.common.utils.shape.Point;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PrePath {
    public double distance = 0;
    public List<List<Point>> prePoint = new ArrayList<>();
    public List<Integer> prePart = new ArrayList<>();

    public int getLastPartId() {
        return prePart.get(prePart.size() - 1);
    }

    public List<Point> getFirstPath() {
        return prePoint.isEmpty() ? null : prePoint.get(0);
    }

    public boolean isNoPath() {
        return prePoint.isEmpty();
    }

    public List<Point> getLastPath() {
        if (prePoint.isEmpty()) {
            return null;
        }
        return prePoint.get(prePoint.size() - 1);
    }

    public Point getLastPoint() {
        if (prePoint.isEmpty()) {
            return null;
        }
        List<Point> points = prePoint.get(prePoint.size() - 1);
        return points.get(points.size() - 1);
    }

    public PrePath copy() {
        PrePath ret = new PrePath();
        ret.distance = this.distance;
        ret.prePart.addAll(this.prePart);
        // 注意 里面是浅拷贝 不过够用了
        ret.prePoint.addAll(this.prePoint);
        return ret;
    }

    public void simplePath(PathFindingManager manager) {
        List<List<Point>> newPath = new ArrayList<>();
        for (List<Point> points : prePoint) {
            if (points.size() > 2) {
                newPath.add(manager.simplePath(points));
            } else {
                newPath.add(points);
            }
        }
        prePoint = newPath;
    }
}

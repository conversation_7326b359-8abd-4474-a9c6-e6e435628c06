package com.yorha.cnc.scene.gm.command.resbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AllCollectEnd implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        for (ArmyEntity armyEntity : new ArrayList<>(scenePlayer.getArmyMgrComponent().getMyArmyList())) {
            if (armyEntity.getStatusComponent().getState() == CommonEnum.ArmyDetailState.ADS_COLLECT) {
                long attachId = armyEntity.getProp().getAttachId();
                ResBuildingEntity entity = actor.getScene().getObjMgrComponent().getSceneObjWithType(ResBuildingEntity.class, attachId);
                entity.getCollectComponent().gmCollectFinish();
            }
        }
    }


    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COLLECT;
    }
}

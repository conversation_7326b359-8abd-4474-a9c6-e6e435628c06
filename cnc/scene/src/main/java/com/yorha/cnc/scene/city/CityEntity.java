package com.yorha.cnc.scene.city;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.component.*;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.player.*;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.WarningInfoType;
import com.yorha.cnc.scene.sceneObj.camp.CampRelationProvider;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjDevBuffComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.SpecialShieldHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.CityProp;
import com.yorha.game.gen.prop.Int64ArmyArrowItemMapProp;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import res.template.ConstSettingTemplate;
import res.template.ConstSpyTemplate;
import res.template.MapBuildingTemplate;
import res.template.NpcCityTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CityEntity extends SceneObjEntity implements BuildingEntityType, WarningInfoType {
    private final CityProp prop;
    private final CityBattleComponent battleComponent;
    private final CityInnerArmyComponent innerArmyComponent = new CityInnerArmyComponent(this);
    private final SceneObjBuffComponent buffComponent = new CityBuffComponent(this);
    private final CityAdditionComponent additionComponent = new CityAdditionComponent(this);
    private final SceneObjDevBuffComponent devBuffComponent = new CityDevBuffComponent(this);
    private final CityPeaceShieldComponent peaceShieldComponent = new CityPeaceShieldComponent(this);
    private final CitySpecialSafeGuardComponent specialSafeGuardComponent = new CitySpecialSafeGuardComponent(this);
    private final CityKingdomComponent kingdomComponent = new CityKingdomComponent(this);
    private final CityExteriorComponent exteriorComponent = new CityExteriorComponent(this);

    private AbstractScenePlayerEntity abstractScenePlayerEntity = null;
    private int uniqueId = 0;

    public CityEntity(CityBuilder builder) {
        this(builder, false);
    }

    public CityEntity(CityBuilder builder, boolean isRestore) {
        super(builder);
        this.prop = builder.getProp();
        this.battleComponent = builder.battleComponent(this);
        initAllComponents();
        getPropComponent().initPropListener(isRestore);
        if (isPlayerCity()) {
            // 设置拥有者永远同步
            getAoiNodeComponent().setAlwaysSyncPlayer(getPlayerId());
        }
    }

    public AbstractScenePlayerEntity getScenePlayer() {
        if (abstractScenePlayerEntity != null) {
            return abstractScenePlayerEntity;
        }
        long ownerId = getProp().getOwnerId();
        if (isPlayerCity()) {
            abstractScenePlayerEntity = getScene().getPlayerMgrComponent().getScenePlayer(ownerId);
        } else {
            abstractScenePlayerEntity = getScene().getPlayerMgrComponent().getNpcPlayer(ownerId);
        }
        return abstractScenePlayerEntity;
    }

    // ------------------------------------------- 必须实现 -------------------------------------------
    @Override
    public CityProp getProp() {
        return prop;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_City;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getCityAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getCityAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getCityAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getCityAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getCityAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final CityProp cityProp = CityProp.of(builder.getFullAttr().getCityAttr(), builder.getChangedAttr().getCityAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setCityAttr(cityProp.getCopyDbBuilder());
    }

    @Override
    public CityBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        prop.getTarget().setTargetType(ArmyTargetType.ATT_CITY)
                .setName(getPlayerName())
                .setClanSimpleName(getBriefClanName())
                .setTemplateId(0)
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }

    public CityKingdomComponent getKingdomComponent() {
        return kingdomComponent;
    }
// ------------------------------------------- 场景 -------------------------------------------

    @Override
    public void addIntoScene() {
        if (!getTransformComponent().isAscend()) {
            getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_BORN);
        }
        getScene().getObjMgrComponent().addSceneObjEntity(this);
    }

    // ------------------------------------------- 快捷方法 -------------------------------------------

    @Override
    public int getLevel() {
        return getProp().getLevel();
    }

    @Override
    public long getPlayerId() {
        return getProp().getOwnerId();
    }

    @Override
    public long getClanId() {
        return getProp().getClanId();
    }

    @Override
    public Camp getCampEnum() {
        return getProp().getCamp();
    }

    public void SetCampEnum(Camp camp) {
        getProp().setCamp(camp);
    }

    @Override
    public int getZoneId() {
        return getProp().getZoneId();
    }

    @Override
    public ErrorCode canBeRallyWithCode() {
        SafeGuardReason safeGuardReason = checkShieldHasFunction(SafeGuardFunctionType.SGFT_CANNOT_BE_RALLY);
        // 领土保护活动特殊错误码
        if (safeGuardReason == CommonEnum.SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY) {
            return ErrorCode.SAFE_GUARD_LAND_PROTECT_ACTIVITY_OPEN;
        }
        return safeGuardReason == SafeGuardReason.SGR_NONE ? ErrorCode.OK : ErrorCode.RALLY_CANT;
    }

    @Override
    public boolean canBeSpy(long enemyPlayerId) {
        //主城等级差大于10级时不能侦查
        AbstractScenePlayerEntity enemy = getScene().getPlayerMgrComponent().getScenePlayer(enemyPlayerId);
        if (enemy == null) {
            return false;
        }
        int levelDiff = getScenePlayer().getMainCity().getLevel() - enemy.getMainCity().getLevel();
        if (levelDiff > ResHolder.getInstance().getConstTemplate(ConstSpyTemplate.class).getSpyMaxBaseLevelDifference()) {
            return false;
        }
        //玩家基地开罩子不能侦查
        return checkShieldHasFunction(SafeGuardFunctionType.SGFT_CANNOT_BE_SPY) == SafeGuardReason.SGR_NONE;
    }

    @Override
    public boolean canBlockSpy() {
        return getScenePlayer().getDevBuffComponent().isEffectOn(BuffEffectType.ET_BAN_SPY);
    }

    public boolean isPlayerCity() {
        return getProp().getTemplateId() == 0;
    }

    @Override
    public boolean isInClanTerritory() {
        if (getClanId() == 0 || !getScene().isMainScene()) {
            return false;
        }
        return getScene().getBuildingMgrComponent().getOwnerIdByPartId(getCurPoint()) == getClanId();
    }

    @Override
    public void setExpression(int expressionId) {
        getProp().getExpression().setExpressionId(expressionId).setTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(ResHolder.getConsts(ConstSettingTemplate.class).getExpressionLastTime()));
    }

    public void setPFlagId(int pFlagId) {
        getProp().setPFlagId(pFlagId);
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack(needCheckSiegeLimit, attackerObj, attackerObj.getEntityId());
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack(needCheckSiegeLimit, attackerPlayer, 0);
    }

    private ErrorCode commonCheckBeAttack(boolean needCheckSiegeLimit, CampRelationProvider provider, long roleId) {
        // 合围上限
        if (needCheckSiegeLimit && getBattleComponent().isSiegeLimit(provider.getClanId(), roleId)) {
            return ErrorCode.SIEGE_LIMIT;
        }
        return ErrorCode.OK;
    }

    /**
     * 是否开启和平护盾或特殊保护罩
     */
    @Override
    public boolean isShieldOn() {
        if (!isPlayerCity()) {
            return false;
        }
        return getPeaceShieldComponent().isPeaceShieldOn() || getSpecialSafeGuardComponent().isSpecialSafeGuardOn();
    }

    /**
     * 检查是否开启的特殊保护罩中是否有特定功能
     *
     * @param functionType 保护罩功能类型
     */
    @Override
    public SafeGuardReason checkShieldHasFunction(SafeGuardFunctionType functionType) {
        if (!isShieldOn()) {
            // 未开启任何保护罩
            return SafeGuardReason.SGR_NONE;
        }
        if (getPeaceShieldComponent().isPeaceShieldOn()) {
            // 和平护盾开启，任何功能都有
            return SafeGuardReason.SGR_NOT_SPECIAL_SAFE_GUARD;
        }
        // 特殊护盾，特殊检查
        return checkSpecialShieldHasFunction(functionType);
    }

    @Override
    public SafeGuardReason checkSpecialShieldHasFunction(SafeGuardFunctionType functionType) {
        return SpecialShieldHelper.checkHasFunction(getSpecialSafeGuardComponent().isSpecialSafeGuardOn(),
                getSpecialSafeGuardComponent().getSafeGuardReasons(), functionType);
    }

    public void checkCollision() {
        if (getScene().getCityMoveComponent().isPointNavMovable(getCurPoint())) {
            return;
        }
        // 发现在阻挡里
        LOGGER.info("restore but in static collision {}", this);
        getScenePlayer().getArmyMgrComponent().returnAllArmy();
        getTransformComponent().moveCityRandom();
    }

    // ------------------------------------------- 生命周期 -------------------------------------------

    @Override
    public void deleteObj() {
        getDbComponent().deleteDb();
        super.deleteObj();
    }

    public void deleteByMigrate() {
        getDbComponent().stopByMigrate();
        getScene().getBornMgrComponent().cancelOccupy(getCurPoint(), this);
        int region = MapGridDataManager.getRegionId(getScene().getMapId(), getCurPoint());
        getScene().getBornMgrComponent().decCityNum(region);
        super.deleteObj();
    }

    // ------------------------------------------- component -------------------------------------------

    @Override
    public CityTransformComponent getTransformComponent() {
        return (CityTransformComponent) super.getTransformComponent();
    }

    @Override
    public CityInnerArmyComponent getInnerArmyComponent() {
        return innerArmyComponent;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return buffComponent;
    }

    public CityPeaceShieldComponent getPeaceShieldComponent() {
        return peaceShieldComponent;
    }

    @Override
    public CityAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    @Override
    public CitySpecialSafeGuardComponent getSpecialSafeGuardComponent() {
        return specialSafeGuardComponent;
    }

    public SceneObjDevBuffComponent getDevBuffComponent() {
        return devBuffComponent;
    }

    public CityExteriorComponent getExteriorComponent() {
        return exteriorComponent;
    }

    // ------------------------------------------- prop -------------------------------------------

    public void onClanChange(long clanId, String name) {
        long oldClanId = getProp().getClanId();
        String oldName = getProp().getClanSname();
        getProp().setClanId(clanId).setClanSname(name);
        if (oldClanId != clanId) {
            getEventDispatcher().dispatch(new ClanChangeEventFirst(getEntityId(), oldClanId, clanId));
            getEventDispatcher().dispatch(new ClanChangeEvent(getEntityId(), oldClanId, clanId));
        } else if (!oldName.equals(name)) {
            getEventDispatcher().dispatch(new ClanSimpleNameChangeEvent(getEntityId(), name));
        }
        // 联盟堡垒护盾检测
        getSpecialSafeGuardComponent().checkAndRefreshFortressGuard();
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, GameLogicConstants.CITY_CONFIG_TEMPLATE_ID);
    }

    /**
     * copy自身相关属性到集结的目标数据中
     */
    public void copyToRallyInfo(RallyInfoProp rallyInfoProp) {
        rallyInfoProp
                .setTargetClanShortName(getProp().getClanSname())
                .setTargetId(getEntityId())
                .setTargetClanId(getClanId())
                .setTargetType(isPlayerCity() ? RallyTargetType.RTT_CITY : RallyTargetType.RTT_NPC_CITY)
                .setTargetTemplateId(getProp().getTemplateId())
                .getTargetPos().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        rallyInfoProp.getTargetCardHead().mergeFromSs(getCardHead());
    }

    /**
     * 更新玩家信息
     */
    public void onSyncPlayerName(String playerName) {
        if (StringUtils.isNotEmpty(playerName)) {
            getProp().getCardHead().setName(playerName);
            getEventDispatcher().dispatch(new PlayerNameChangeEvent(getEntityId(), playerName));
        }
    }

    public void onSyncPlayerPic(int pic, String picUrl) {
        getProp().getCardHead().setPic(pic).setPicUrl(picUrl);
        getEventDispatcher().dispatch(new PlayerPicChangeEvent(getEntityId(), pic, picUrl));
    }

    public void onSyncPlayerPicFrame(int picFrame) {
        getProp().getCardHead().setPicFrame(picFrame);
        getEventDispatcher().dispatch(new PlayerPicFrameChangeEvent(getEntityId(), picFrame));
    }

    public String getPlayerName() {
        return getProp().getCardHead().getName();
    }

    public String getBriefClanName() {
        return getProp().getClanSname();
    }

    public Struct.PlayerCardHead getCardHead() {
        return getProp().getCardHead().getCopySsBuilder().build();
    }

    @Override
    public Int64ArmyArrowItemMapProp getArrowProp() {
        return getProp().getArrow();
    }

    /**
     * 判断主堡是否在战斗中
     */
    public boolean isInBattle() {
        return getBattleComponent().isInBattle();
    }

    public NpcCityTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(NpcCityTemplate.class, getProp().getTemplateId());
    }

    public int getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(int uniqueId) {
        this.uniqueId = uniqueId;
    }

    @Override
    public boolean onTimerDispatch(SceneTimerReason reason) {
        if (super.onTimerDispatch(reason)) {
            return true;
        }
        if (reason == SceneTimerReason.TIMER_CITY_DRESS) {
            getExteriorComponent().onDressExpired();
            return true;
        }
        if (reason == SceneTimerReason.TIMER_CITY_NAMEPLATE) {
            getExteriorComponent().onNameplateExpired();
            return true;
        }
        return false;
    }

    @Override
    public void formWarningInfo(StructPlayerPB.WarningInfoPB.Builder builder) {
        builder.setTargetClanSimpleName(this.getBriefClanName())
                .setTargetPlayerName(this.getPlayerName());
    }

    public boolean isKing() {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        return service.isKing(prop.getCityKingdomModel().getOfficeId());
    }
}

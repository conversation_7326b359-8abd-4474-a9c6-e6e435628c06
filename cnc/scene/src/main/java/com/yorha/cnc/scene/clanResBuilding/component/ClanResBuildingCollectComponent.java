package com.yorha.cnc.scene.clanResBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingTaskDecider;
import com.yorha.cnc.scene.clanResBuilding.TaskDecisionType;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CityInnerArmyProp;
import com.yorha.game.gen.prop.ProgressInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.ClanResourceBuildingTemplate;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ClanResBuildingCollectComponent extends SceneObjComponent<ClanResBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildingCollectComponent.class);

    public ClanResBuildingCollectComponent(ClanResBuildingEntity owner) {
        super(owner);
    }

    /**
     * 建筑内正在采集和正在来采集的玩家id -> 军队id的映射
     */
    final private Map<Long, Long> playerId2ArmyIdMap = new HashMap<>();

    /**
     * 采集事件定时器
     */
    private TimerReasonType nextEventTimer = null;

    /**
     * 事件监听
     */
    private EventListener listener = null;

    /**
     * 首个要离开的军队的时间
     */
    private long firstLeaveArmyTsMs = Long.MAX_VALUE;

    /**
     * 首个要离开的军队的id
     */
    private long firstLeaveArmyId = -1L;

    /**
     * 决策器，根据时间戳决定接下来是什么事件
     */
    ClanResBuildingTaskDecider decider = null;

    /**
     * 内存中准确的采集速度，起服从army身上实时算出来
     */
    double buildCollectSpeedPerSec = 0.0;

    /**
     * 内存中的准确已采集数据，每次起服恢复的时候会取上界
     */
    double realAlreadyCollectNum = 0.0;

    /**
     * 起服恢复
     */
    @Override
    public void afterAllLoad() {
        if (getOwner().getState() != CommonEnum.ClanResBuildingStage.CRBS_CAN_COLLECT) {
            // 仅采集状态需要初始化
            return;
        }
        // 获取矿消失的时间，采集结束的时间和更新时间，更新时间用当前时间的后一秒
        long upperNowTsMs = getUpperSecondTsMs(SystemClock.now());
        long disappearTsMs = getOwner().getProp().getDisappearTsMs();
        long collectFinishTsMs = getProgressInfo().getStateEndTsMs();
        LOGGER.info("ClanResBuildingCollectComponent start collect restore, clanResBuilding {}", getOwner());
        if (isNeedDestroyDuringShutdown(disappearTsMs, collectFinishTsMs, upperNowTsMs)) {
            // 如果停服期间矿消失了，不用恢复了
            // 采集量可能会存在一定量的误差，策划表示可以接受
            onCollectFinishWithTsMs(Math.min(disappearTsMs, collectFinishTsMs));
            return;
        }
        // 发现在阻挡里
        if (!getOwner().getScene().getCityMoveComponent().isPointNavMovable(getOwner().getCurPoint())) {
            LOGGER.info("restore but in static collision {}", getOwner());
            onCollectFinishWithTsMs(upperNowTsMs);
            return;
        }
        // 停服期间矿未消失，原来计算的速度足够消耗，将所有停服前应该离开的军队遣返，注意此时事件监听还没恢复
        long needDecResNum = returnAllFullArmyDuringShutdown(upperNowTsMs);
        // 恢复第一个采集结束的军队的时间戳，需要遍历所有军队，O(n)
        restoreFirstLeaveArmyInfo();
        // 把已经走的玩家的资源扣掉，根据新的速度，重新更新矿消失的时间戳
        restoreCollectInfo(needDecResNum, upperNowTsMs);
        collectFinishTsMs = getProgressInfo().getStateEndTsMs();
        // 更新决策器
        updateTaskDecider(firstLeaveArmyTsMs, disappearTsMs, collectFinishTsMs, upperNowTsMs);
        // 决策并执行
        decideAndDo();
        // 恢复监听
        restoreListener();
    }

    /**
     * 进入采集状态
     */
    public void onEnterCollect(long enterTs) {
        LOGGER.info("{} onEnter collect, enterTs {}", getOwner(), enterTs);
        // 每个玩家只留下一个军队，其他部队回城
        // 回城会抛出InnerArmyDelEvent事件，会触发onCollectSpeedChange方法，所以listener不能提前设置
        reserveEachPlayerOneArmy();
        // 初始化军团资源中心的信息，用后一秒整毫秒的时间戳
        initCollectInfo(getUpperSecondTsMs(enterTs));
        // 设置listener
        restoreListener();
        // 更新决策器
        updateTaskDecider(firstLeaveArmyTsMs, getOwner().getProp().getDisappearTsMs(), getProgressInfo().getStateEndTsMs(), enterTs);
        // 决策并执行
        decideAndDo();
    }

    /**
     * 军队要离开建筑并回主城
     *
     * @param army    军队的实体
     * @param calTsMs 操作的计算时间戳
     */
    public long onCollectOutAndReturnMainCity(ArmyEntity army, long calTsMs) {
        LOGGER.info("{} collectOut, army = {}, calTsMs = {}", getOwner(), army, calTsMs);
        long tryAddNum = calSingleArmyCollectInfoFromLastCalTsMs(army, calTsMs);
        onCollectOutAndReturnMainCityWithNum(army, calTsMs, tryAddNum);
        return tryAddNum;
    }

    public void onCollectOutAndReturnMainCityWithNum(ArmyEntity army, long calTsMs, long addNum) {
        LOGGER.info("{} collectOut, army = {}, calTsMs = {}, addNum = {}", getOwner(), army, calTsMs, addNum);
        removeArmyCollectInfo(army.getPlayerId());
        army.getResourceComponent().onCalCollectEnd(getOwner(), getOwner().getCurrencyType(), addNum, calTsMs);
        army.getCollectComponent().collectEndAndReturn(getOwner());
    }

    /**
     * 结算整个建筑之前的采集进度
     *
     * @param speed       采集速度
     * @param calTsMs     用于结算的时间戳
     * @param army        军队实体
     * @param isArmyLeave 军队是否要离开
     */
    private long settleBeforeUpdate(double speed, long calTsMs, ArmyEntity army, boolean isArmyLeave) {
        long lastCalTsMs = getProgressInfo().getLastCalTsMs();
        long duration = calTsMs - lastCalTsMs;
        double settleNum = speed * TimeUtils.ms2Second(duration);
        LOGGER.info("settleBeforeUpdate: speed {}, settleNum {}, realAlreadyCollectNum {}, lastCalTsMs {}",
                speed, settleNum, realAlreadyCollectNum, lastCalTsMs);
        realAlreadyCollectNum = realAlreadyCollectNum + settleNum;
        getProgressInfo().setLastCalNum((long) (realAlreadyCollectNum))
                .setLastCalTsMs(calTsMs);
        if (isArmyLeave) {
            // 军队要走算一下军队期望的采集量
            return (long) (getCollectSpeedPerSec(army) * TimeUtils.ms2Second(duration));
        } else {
            // 军队进入，采集量为0
            return 0L;
        }
    }

    private void updateCollectSpeed(ArmyEntity army, boolean isAdd) {
        if (isAdd) {
            buildCollectSpeedPerSec += getCollectSpeedPerSec(army);
        } else {
            buildCollectSpeedPerSec -= getCollectSpeedPerSec(army);
        }
    }

    private void updateFirstLeaveArmyInfo() {
        firstLeaveArmyTsMs = Long.MAX_VALUE;
        firstLeaveArmyId = 0L;
        // 恢复最早离开的军队信息
        for (long playerId : getOwner().getProp().getEachPlayerCollectMap().keySet()) {
            if (!playerId2ArmyIdMap.containsKey(playerId)) {
                LOGGER.error("playerId = {} not in army list, but in eachPlayerCollectMap", playerId);
                continue;
            }
            ProgressInfoProp progressInfoProp = getOwner().getProp().getEachPlayerCollectMap().get(playerId);
            if (progressInfoProp.getStateEndTsMs() < firstLeaveArmyTsMs) {
                firstLeaveArmyTsMs = progressInfoProp.getStateEndTsMs();
                firstLeaveArmyId = playerId2ArmyIdMap.get(playerId);
            }
        }
    }

    private void restoreFirstLeaveArmyInfo() {
        buildCollectSpeedPerSec = 0.0;
        // 恢复playerId -> armyId的映射
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (long armyId : getOwner().getProp().getInnerArmy().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("ClanResBuilding, restoreFirstLeaveArmyInfo: army is null, armyId = {}", armyId);
                continue;
            }
            addArmyRecordToClanResBuilding(army.getPlayerId(), army.getEntityId());
            buildCollectSpeedPerSec += getCollectSpeedPerSec(army);
        }
        updateFirstLeaveArmyInfo();
    }

    /**
     * 每个玩家只留下一个军队，其他部队回城
     */
    private void reserveEachPlayerOneArmy() {
        Map<Long, Long> playerId2MaxCanCollectNumMap = new HashMap<>();
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        // 选出要留在军团资源中心的军队
        Set<Long> armyIdSet = new HashSet<>();
        for (long armyId : getOwner().getProp().getInnerArmy().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("ClanResBuilding, reserveEachPlayerOneArmy: army is null, armyId = {}", armyId);
                continue;
            }
            armyIdSet.add(armyId);
            // 剩余负重产生的可采集量
            long canBurdenToRes = army.getResourceComponent().getRestBurdenToRes(getOwner().getCurrencyType());
            if (playerId2MaxCanCollectNumMap.containsKey(army.getPlayerId())) {
                if (playerId2MaxCanCollectNumMap.get(army.getPlayerId()) < canBurdenToRes) {
                    playerId2MaxCanCollectNumMap.put(army.getPlayerId(), canBurdenToRes);
                    addArmyRecordToClanResBuilding(army.getPlayerId(), army.getEntityId());
                }
            } else {
                playerId2MaxCanCollectNumMap.put(army.getPlayerId(), canBurdenToRes);
                addArmyRecordToClanResBuilding(army.getPlayerId(), army.getEntityId());
            }
        }
        for (long armyId : armyIdSet) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("ClanResBuilding, reserveEachPlayerOneArmy: real remove army is null, armyId = {}", armyId);
                continue;
            }
            // 这里不应该有null
            if (playerId2ArmyIdMap.get(army.getPlayerId()) != army.getEntityId()) {
                LOGGER.info("ClanResBuilding, reserveEachPlayerOneArmy: remove army, armyId = {}", armyId);
                // 未选中的军队回城
                army.getAssistComponent().leaveAssist(null, true, true);
            }
        }
        // 未到达的玩家直接回城
        getOwner().getInnerArmyComponent().returnAllUnArrivedArmy();
    }

    /**
     * 初始化军团资源中心采集的信息
     */
    private void initCollectInfo(long enterTsMs) {
        // 设置状态和消失时间
        getOwner().getProp().setState(CommonEnum.ClanResBuildingStage.CRBS_CAN_COLLECT)
                .setDisappearTsMs(enterTsMs + TimeUnit.DAYS.toMillis(getOwner().getDisappearDay()));
        ClanResourceBuildingTemplate template = getOwner().getClanResourceBuildingTemplate();
        getProgressInfo().setUid(getEntityId()).setLastCalNum(0).setLastCalTsMs(enterTsMs)
                .setStateStartTsMs(enterTsMs).setMaxNum(template.getResNum());
        // 设置每个玩家的采集信息
        initAllArmyCollectInfo(enterTsMs);
        // 设置采集结束时间
        if (Math.abs(buildCollectSpeedPerSec) < 0.5f) {
            getProgressInfo().setStateEndTsMs(Long.MAX_VALUE);
        } else {
            long stateEndTsMs = getExpectedFinishTsMs(template.getResNum(),
                    buildCollectSpeedPerSec, getProgressInfo().getStateStartTsMs(), false);
            getProgressInfo().setStateEndTsMs(stateEndTsMs);
        }
        // 设置速度
        getProgressInfo().setSpeed((long) buildCollectSpeedPerSec);
    }

    /**
     * 初始化军团资源中心所有军队的采集信息
     */
    private void initAllArmyCollectInfo(long upperEnterTsMs) {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        Set<Long> armyIdSet = getOwner().getProp().getInnerArmy().getArmy().keySet();
        for (long armyId : armyIdSet) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("ClanResBuilding, initAllArmyCollectInfo: army is null, armyId = {}", armyId);
                continue;
            }
            addArmyCollectInfo(army, upperEnterTsMs);
            long armyExpectedLeaveTsMs = getOwner().getProp().getEachPlayerCollectMapV(army.getPlayerId()).getStateEndTsMs();
            // 更新最早离开的军队信息
            if (armyExpectedLeaveTsMs < firstLeaveArmyTsMs) {
                firstLeaveArmyTsMs = armyExpectedLeaveTsMs;
                firstLeaveArmyId = army.getEntityId();
            }
            // 更新速度信息
            buildCollectSpeedPerSec += getCollectSpeedPerSec(army);
            // qlog
            army.getCollectComponent().sendCollectQlog("regular_collect", getOwner(), 0);
        }
        getOwner().getProp().setIsCollecting(!armyIdSet.isEmpty()).setCurrentCollectNum(armyIdSet.size());
    }

    private void restoreCollectInfo(long needDecNum, long calTsMs) {
        if (needDecNum <= 0) {
            return;
        }
        getProgressInfo().setLastCalNum(getProgressInfo().getLastCalNum() + needDecNum).setLastCalTsMs(calTsMs);
        long leftNum = getProgressInfo().getMaxNum() - getProgressInfo().getLastCalNum();
        // 恢复的时候额外加1已采集，因为存库时会向下取整，+1可以防止多给到玩家
        realAlreadyCollectNum = getProgressInfo().getLastCalNum() + 1;
        refreshCollectInfoImpl(leftNum, calTsMs);
    }

    private void refreshCollectInfo(long calTsMs) {
        long leftNum = getProgressInfo().getMaxNum() - getProgressInfo().getLastCalNum();
        refreshCollectInfoImpl(leftNum, calTsMs);
    }

    private void refreshCollectInfoImpl(long leftNum, long stateBeginTsMs) {
        // 设置采集结束时间、更新速度
        if (Math.abs(buildCollectSpeedPerSec) < 0.5f) {
            getProgressInfo().setStateEndTsMs(getOwner().getProp().getDisappearTsMs());
        } else {
            long stateEndTsMs = getExpectedFinishTsMs(leftNum, buildCollectSpeedPerSec, stateBeginTsMs, false);
            getProgressInfo().setStateEndTsMs(stateEndTsMs);
        }
        getProgressInfo().setSpeed((long) buildCollectSpeedPerSec);
        // 采集信息需要刷新
        getOwner().getProp().setIsCollecting(!playerId2ArmyIdMap.isEmpty()).setCurrentCollectNum(playerId2ArmyIdMap.size());
    }

    public void addArmyCollectInfo(ArmyEntity army, long startTsMs) {
        if (army == null) {
            LOGGER.error("addArmyCollectInfo: army is null");
            return;
        }
        LOGGER.info("addArmyCollectInfo: armyId {}, playerId {}, startTsMs {}", army.getEntityId(), army.getPlayerId(), startTsMs);
        ProgressInfoProp armyProgressInfoProp = getOwner().getProp().addEmptyEachPlayerCollectMap(army.getPlayerId());
        // 提前设置进入的加成信息，后面的速度都从这里读取
        armyProgressInfoProp.setEnterAdditionValue(getArmyEnterAddition(army));
        // 拿到剩余可采集量和采集速度
        long restBurdenToRes = army.getResourceComponent().getRestBurdenToRes(getOwner().getCurrencyType());
        double speedPerSec = getCollectSpeedPerSec(army);
        long stateEndTsMs = getExpectedFinishTsMs(restBurdenToRes, speedPerSec, startTsMs, true);
        // 设置军队的进度条信息
        armyProgressInfoProp.setUid(army.getPlayerId())
                .setLastCalNum(army.getResourceComponent().getCurBurden())
                .setMaxNum(army.getResourceComponent().getArmyMaxBurden())
                .setLastCalTsMs(startTsMs)
                .setSpeed((long) speedPerSec)
                .setStateEndTsMs(stateEndTsMs)
                .setStateStartTsMs(startTsMs);
        addArmyRecordToClanResBuilding(army.getPlayerId(), army.getEntityId());
        // 更新最早离开的军队信息
        if (stateEndTsMs < firstLeaveArmyTsMs) {
            firstLeaveArmyTsMs = stateEndTsMs;
            firstLeaveArmyId = army.getEntityId();
            LOGGER.info("firstLeaveArmy refresh {} {}", firstLeaveArmyId, firstLeaveArmyTsMs);
        }
        // 将军队状态变为采集
        army.getPropComponent().onAttachChange(CommonEnum.AttachState.AAS_COLLECT, getEntityId());
        army.getProp().setArmyState(CommonEnum.ArmyState.AS_Collect);
        army.getStatusComponent().setDetailState(CommonEnum.ArmyDetailState.ADS_COLLECT, getOwner(), startTsMs, stateEndTsMs, 0);
    }

    private void removeArmyCollectInfo(long playerId) {
        getOwner().getProp().getEachPlayerCollectMap().remove(playerId);
        removeArmyRecordFromClanResBuilding(playerId);
    }

    @Nullable
    public ProgressInfoProp getCollectInfoByPlayerId(long playerId) {
        return getOwner().getProp().getEachPlayerCollectMap().get(playerId);
    }

    /**
     * @return 获取玩家进入资源中心的加成值
     */
    private Long getPlayerEnterAddition(long playerId) {
        ProgressInfoProp progressInfoProp = getCollectInfoByPlayerId(playerId);
        if (progressInfoProp == null) {
            LOGGER.error("getPlayerEnterAddition: playerId {} progressInfoProp is null", playerId);
            return null;
        }
        return progressInfoProp.getEnterAdditionValue();
    }

    /**
     * 获取军队身上的加成信息，应该仅在军队加入资源田时设置
     */
    private long getArmyEnterAddition(@NotNull ArmyEntity army) {
        long armyAddition = army.getAdditionComponent().getAddition(CommonEnum.BuffEffectType.ET_COLLECT_SPEED_UP);
        long configAddition = getOwner().getClanResourceBuildingTemplate().getResSpeedUpRate();
        return armyAddition + configAddition;
    }

    /**
     * 根据传入时间结算所有军队并依次移出军队
     *
     * @param calTsMs 结算时间
     */
    private void removeAllArmyWhenDestroy(long calTsMs) {
        LOGGER.info("ClanResBuilding {} : removeAllArmyWhenDestroy: calTsMs {}", getOwner(), calTsMs);
        CityInnerArmyProp innerArmyProp = getOwner().getInnerArmyComponent().getProp();
        // 根据军队的到达时间排序
        List<Long> sortedArmyByEnterTsMs = innerArmyProp.getArmy().keySet().stream()
                .sorted(Comparator.comparingLong(a -> innerArmyProp.getArmy().get(a).getEnterTsMs()))
                .collect(Collectors.toList());
        // 预算出每个军队可以获得的资源
        Map<Long, Long> armyToCanCollectNum = new HashMap<>();
        Map<Long, ArmyEntity> tmpArmyEntityMap = new HashMap<>();
        long totalTryCollectNumFromLastCal = 0;
        long buildLastCalTsMs = getProgressInfo().getLastCalTsMs();
        for (long armyId : innerArmyProp.getArmy().keySet()) {
            ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("ClanResBuilding: army is null, armyId = {}", armyId);
                continue;
            }
            // 军队的资源应该由两部分组成
            // 第一部分是军队从进来到建筑上次结算的时间段内的资源
            long beforeLastCalCanCollectNum = calSingleArmyCollectInfoFromLastCalTsMs(army, buildLastCalTsMs);
            // 第二部分是建筑从上次结算到现在的资源，这部分会存在最后的资源分配问题
            long afterLastCalCanCollectNum = calSingleArmyCollectInfoWithStartAndEndTime(army, buildLastCalTsMs, calTsMs);
            totalTryCollectNumFromLastCal += afterLastCalCanCollectNum;
            armyToCanCollectNum.put(armyId, beforeLastCalCanCollectNum + afterLastCalCanCollectNum);
            tmpArmyEntityMap.put(armyId, army);
        }
        // 获取建筑剩余的资源
        long leftNumAfterLastCal = getProgressInfo().getMaxNum() - getProgressInfo().getLastCalNum();
        LOGGER.info("removeAllArmyWhenDestroy: totalTryAddNumFromLastCal {}, leftNumAfterLastCal {}, buildLastCalTsMs {}",
                buildLastCalTsMs, totalTryCollectNumFromLastCal, leftNumAfterLastCal);
        if (leftNumAfterLastCal <= totalTryCollectNumFromLastCal) {
            if (totalTryCollectNumFromLastCal - leftNumAfterLastCal > 0.1 * getProgressInfo().getMaxNum()) {
                LOGGER.error("removeAllArmyWhenDestroy: leftNumAfterLastCal {} < totalTryAddNumFromLastCal {}, still add",
                        leftNumAfterLastCal, totalTryCollectNumFromLastCal);
            } else {
                LOGGER.warn("removeAllArmyWhenDestroy: leftNumAfterLastCal {} < totalTryAddNumFromLastCal {}, still add",
                        leftNumAfterLastCal, totalTryCollectNumFromLastCal);
            }
        } else {
            long needAssignNum = leftNumAfterLastCal - totalTryCollectNumFromLastCal;
            LOGGER.info("removeAllArmyWhenDestroy: needAssignNum = {}", needAssignNum);
            // 把剩余的资源按照进入据点的时间依次补到军队身上
            for (long armyId : sortedArmyByEnterTsMs) {
                ArmyEntity army = tmpArmyEntityMap.get(armyId);
                double armySpeed = getCollectSpeedPerSec(army);
                if (!armyToCanCollectNum.containsKey(armyId)) {
                    LOGGER.error("ClanResBuilding: armyToCanCollectNum not contains armyId = {}", armyId);
                    continue;
                }
                // 因为误差最多只有1秒，所以这里不用再乘一个时间了
                if (needAssignNum <= armySpeed) {
                    armyToCanCollectNum.put(armyId, armyToCanCollectNum.get(armyId) + needAssignNum);
                    // 已经分完了
                    break;
                } else {
                    needAssignNum -= armySpeed;
                    armyToCanCollectNum.put(armyId, (long) (armyToCanCollectNum.get(armyId) + armySpeed));
                }
            }
        }
        // 给军队加资源
        for (ArmyEntity army : tmpArmyEntityMap.values()) {
            onCollectOutAndReturnMainCityWithNum(army, calTsMs, armyToCanCollectNum.get(army.getEntityId()));
            sendCollectQLogWhenBuildingDestroy(army, armyToCanCollectNum.get(army.getEntityId()));
        }
    }

    /**
     * 所有原本计算已满应该返回的军队
     *
     * @param needReturnBeforeTsMs 所有在此时间戳之前到期的军队应该返回
     */
    private long returnAllFullArmyDuringShutdown(long needReturnBeforeTsMs) {
        LOGGER.info("ClanResBuilding: returnAllFullArmyDuringShutdown, needReturnBeforeTsMs = {}", needReturnBeforeTsMs);
        long armyTakenNum = 0L;
        CityInnerArmyProp innerArmyProp = getOwner().getInnerArmyComponent().getProp();
        Set<Long> needReturnArmyIdSet = new HashSet<>();
        for (long armyId : innerArmyProp.getArmy().keySet()) {
            ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("ClanResBuilding: army is null, armyId = {}", armyId);
                continue;
            }
            long stateEndTsMs = getOwner().getProgressInfoByPlayerId(army.getPlayerId()).getStateEndTsMs();
            if (stateEndTsMs <= needReturnBeforeTsMs) {
                needReturnArmyIdSet.add(armyId);
            }
        }
        // 不能在上面的循环里面直接remove，因为会导致ConcurrentModificationException
        for (long armyId : needReturnArmyIdSet) {
            // 此时army一定不为空
            ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
            // NOTE(furson): 调用接口时监听事件尚未恢复，InnerArmyDelEvent抛出后不会被处理
            getOwner().getInnerArmyComponent().removeArmy(armyId, army, 0L);
            armyTakenNum += onCollectOutAndReturnMainCity(army, needReturnBeforeTsMs);
        }
        return armyTakenNum;
    }

    /**
     * 是否在停机期间销毁了
     */
    private boolean isNeedDestroyDuringShutdown(long disppearTsMs, long collectFinishTsMs, long now) {
        return disppearTsMs <= now || collectFinishTsMs <= now;
    }

    // ------------------------------------ 决策器相关 ------------------------------------- //

    /**
     * 更新决策器
     */
    private void updateTaskDecider(long firstLeaveArmyTsMs, long disappearTsMs, long collectFinishTsMs, long now) {
        if (decider == null) {
            decider = new ClanResBuildingTaskDecider(firstLeaveArmyTsMs, disappearTsMs, collectFinishTsMs, now);
        } else {
            decider.updateAllTimeStamp(firstLeaveArmyTsMs, disappearTsMs, collectFinishTsMs, now);
        }
    }

    /**
     * 决策并执行
     */
    private void decideAndDo() {
        decider.decide();
        switch (decider.getNextDecisionType()) {
            case ADD_TIMER_EXPIRED:
            case ADD_TIMER_ONE_ARMY_LEAVE:
            case ADD_TIMER_FINISH_COLLECT:
                // 获取下次起定时器的时间
                addNextEventTimer(decider.getNextDecisionType(), decider.getNextDecisionTsMs());
                break;
            case ONE_ARMY_LEAVE:
                onOneArmyCollectFinish();
                break;
            case EXPIRED:
            case FINISH_COLLECT:
                // 删除建筑，结算
                onCollectFinish();
                break;
            default:
                LOGGER.error("wrong decision type = {}", decider.getNextDecisionType());
                break;
        }
        LOGGER.info("ClanResBuildingCollectComponent decideAndDo, clanResBuilding {}, progressInfo {}, decisionType {}",
                getOwner(), getProgressInfo(), decider.getNextDecisionType());
    }

    // ------------------------------------- timer设置及timer相关的方法 ----------------------------------------- //
    private void addNextEventTimer(TaskDecisionType type, long targetTsMs) {
        cancelNextEventTimer();
        switch (type) {
            case ADD_TIMER_FINISH_COLLECT:
            case ADD_TIMER_EXPIRED:
                getOwner().getTimerComponent().addTimer(TimerReasonType.NEXT_EVENT_TIMER_FINISH, this::onCollectFinish,
                        targetTsMs - SystemClock.now(), TimeUnit.MILLISECONDS);
                nextEventTimer = TimerReasonType.NEXT_EVENT_TIMER_FINISH;
                break;
            case ADD_TIMER_ONE_ARMY_LEAVE:
                getOwner().getTimerComponent().addTimer(TimerReasonType.NEXT_EVENT_TIMER_ONE_ARMY_FINISH, this::onOneArmyCollectFinish,
                        targetTsMs - SystemClock.now(), TimeUnit.MILLISECONDS);
                nextEventTimer = TimerReasonType.NEXT_EVENT_TIMER_ONE_ARMY_FINISH;
                break;
            default:
                LOGGER.error("wrong type {} to try add timer", type);
                break;
        }
        LOGGER.info("ClanResBuilding {} addNextEventTimer {}, {}", getOwner(), type, targetTsMs);
    }

    private void cancelNextEventTimer() {
        if (nextEventTimer != null) {
            getOwner().getTimerComponent().cancelTimer(nextEventTimer);
            nextEventTimer = null;
        }
    }

    public void onOneArmyCollectFinishWithTime(long calTsMs, long armyId) {
        LOGGER.info("ClanResBuilding: onOneArmyCollectFinishWithTime, calTsMs = {}, tryLeaveArmy = {}", calTsMs, armyId);
        // 拿出第一个离开的军队
        ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
        // 直接调用remove Army -> 触发InnerDelArmyEvent -> 再触发建筑的变动以及army资源的分配
        getOwner().getInnerArmyComponent().removeArmy(armyId, army, 0L);
    }

    public void onOneArmyCollectFinish() {
        onOneArmyCollectFinishWithTime(firstLeaveArmyTsMs, firstLeaveArmyId);
    }

    public void onCollectFinishWithTsMs(long calTsMs) {
        removeAllArmyWhenDestroy(calTsMs);
        getOwner().getInnerArmyComponent().returnAllUnArrivedArmy();
        if (calTsMs < getOwner().getProp().getDisappearTsMs()) {
            getOwner().sendExpansionLog("res_empty_disappear", 0L);
        } else {
            getOwner().sendExpansionLog("guild_building_disappear", 0L);
        }
        getOwner().destroy();
    }

    private void onCollectFinish() {
        cancelNextEventTimer();
        cancelListener();
        // 结算时间戳应该是建筑消失时间和被采空时间的最小值
        long calTsMs = Math.min(getOwner().getProp().getDisappearTsMs(), getProgressInfo().getStateEndTsMs());
        onCollectFinishWithTsMs(calTsMs);
    }

    public void onClanDismiss() {
        cancelNextEventTimer();
        cancelListener();
        long calTsMs = Math.min(SystemClock.now(), Math.min(getOwner().getProp().getDisappearTsMs(), getProgressInfo().getStateEndTsMs()));
        onCollectFinishWithTsMs(calTsMs);
    }

    public void gmCollectFinish() {
        cancelNextEventTimer();
        cancelListener();
        onCollectFinishWithTsMs(SystemClock.now());
    }

    // ---------------------------------------------- 事件监听及相关方法 ---------------------------------------------- //

    /**
     * 恢复事件监听
     */
    private void restoreListener() {
        if (listener == null) {
            listener = getOwner().getEventDispatcher()
                    .addMultiEventListenerRepeat(this::onCollectSpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
        }
    }

    /**
     * 取消事件监听
     */
    private void cancelListener() {
        if (listener != null) {
            listener.cancel();
            listener = null;
        }
    }

    public void onCollectSpeedChange(IEvent e) {
        cancelNextEventTimer();
        boolean isArmyLeave = e instanceof InnerArmyDelEvent;
        ArmyEntity army = null;
        if (isArmyLeave) {
            army = ((InnerArmyDelEvent) e).getArmyEntity();
        } else {
            army = ((InnerArmyAddEvent) e).getArmyEntity();
        }
        long upperNowTsMs = getUpperSecondTsMs(SystemClock.now());

        // 刷新之前先结算
        long needCheckNum = settleBeforeUpdate(buildCollectSpeedPerSec, upperNowTsMs, army, isArmyLeave);
        // 更新建筑上的采集速度
        if (isArmyLeave) {
            updateCollectSpeed(army, false);
            // 军队离开并获取资源
            long actualTakenNum = onCollectOutAndReturnMainCity(army, upperNowTsMs);
            // 由于浮点数可能会导致建筑上的资源被多扣除，需要看军队拿了多少补回来
            if (actualTakenNum < needCheckNum) {
                LOGGER.info("onOneArmyCollectFinishWithTime: actualTakenNum {}, needCheckNum {}", actualTakenNum, needCheckNum);
                getProgressInfo().setLastCalNum(getProgressInfo().getLastCalNum() - needCheckNum + actualTakenNum);
            }
            // actualTakenNum > needCheckNum的情况不需要打日志，因为建筑是军团进出就结算，军团只在自己出来的时候结算
            sendLeaveCollectQLog(army, actualTakenNum);
        } else {
            addArmyCollectInfo(army, upperNowTsMs);
            updateCollectSpeed(army, true);
            sendStartCollectQLog(army);
        }
        // 更新建筑上的采集信息
        refreshCollectInfo(upperNowTsMs);
        // 刷新最早离开军队的信息
        updateFirstLeaveArmyInfo();
        // 更新决策器
        updateTaskDecider(firstLeaveArmyTsMs, getOwner().getProp().getDisappearTsMs(), getProgressInfo().getStateEndTsMs(), upperNowTsMs);
        // 决策并执行
        decideAndDo();
    }

    // ---------------------------------------- qlog ------------------------------------------------- //

    public void sendStartCollectQLog(ArmyEntity army) {
        if (null == army) {
            LOGGER.error("sendStartCollectQLog army is null");
            return;
        }
        army.getCollectComponent().sendCollectQlog("regular_collect", getOwner(), 0);
    }

    public void sendLeaveCollectQLog(ArmyEntity army, long getNum) {
        if (null == army) {
            LOGGER.error("sendLeaveCollectQLog army is null");
            return;
        }
        // 从结果倒推原因：由于事件走入结算这里，不能知道是不是玩家操作，只能从结果倒推原因
        if (army.getResourceComponent().getRestBurdenToRes(getOwner().getCurrencyType()) <= 0) {
            army.getCollectComponent().sendCollectQlog("collect_full", getOwner(), getNum);
        } else {
            army.getCollectComponent().sendCollectQlog("cancel_collect", getOwner(), getNum);
        }
    }

    public void sendCollectQLogWhenBuildingDestroy(ArmyEntity army, long getNum) {
        if (null == army) {
            LOGGER.error("sendCollectQLogWhenBuildingDestroy army is null");
            return;
        }
        army.getCollectComponent().sendCollectQlog("building_destroy", getOwner(), getNum);
    }

    // ----------------------------------------- 快捷获取及工具方法 --------------------------------------- //

    /**
     * 记录军队到军团资源中心上（只要采集目标朝向资源中心就会记录）
     *
     * @param playerId 玩家id
     * @param armyId   军队id
     */
    public void addArmyRecordToClanResBuilding(long playerId, long armyId) {
        playerId2ArmyIdMap.put(playerId, armyId);
    }

    /**
     * 将军队记录从军团资源中心删掉（会在移动失败，采集结束，指令变更等情况下调用到）
     *
     * @param playerId 玩家id
     */
    public void removeArmyRecordFromClanResBuilding(long playerId) {
        playerId2ArmyIdMap.remove(playerId);
    }

    /**
     * 检查玩家军队是否已经在资源建筑或来资源建筑的路上
     */
    public boolean isPlayerAlreadyInBuilding(long playerId) {
        return playerId2ArmyIdMap.containsKey(playerId);
    }

    /**
     * 根据传入时间戳获取下一个整秒的时间戳
     */
    public long getUpperSecondTsMs(long tsMs) {
        if (tsMs % 1000 == 0) {
            return tsMs;
        }
        return (tsMs / 1000 + 1) * 1000;
    }

    /**
     * @param leftNum     剩余数量
     * @param speedPerSec 每秒的采集速度
     * @param startTsMs   采集开始的时间戳
     * @param giveMore    是否多给一秒
     * @return 获取（军队或建筑）采满或消失的时间戳
     */
    private long getExpectedFinishTsMs(long leftNum, double speedPerSec, long startTsMs, boolean giveMore) {
        if (giveMore) {
            return startTsMs + TimeUtils.second2Ms((long) (leftNum / speedPerSec) + 1);
        } else {
            return startTsMs + TimeUtils.second2Ms((long) (leftNum / speedPerSec));
        }
    }

    /**
     * 获取军队的采集速度
     */
    private double getCollectSpeedPerSec(ArmyEntity army) {
        float base = getOwner().getClanResourceBuildingTemplate().getResSpeed();
        Long addition = getPlayerEnterAddition(army.getPlayerId());
        if (addition == null) {
            double speed = SceneAddCalc.getCollectSpeed(army, getOwner().getCurrencyType(), base, 0);
            LOGGER.error("getCollectSpeedPerSec, cannot get addition, use the current value speed = {}", speed);
            return speed;
        }
        return SceneAddCalc.getClanResCollectSpeed(army, base, addition);
    }

    private ProgressInfoProp getProgressInfo() {
        return getOwner().getProp().getProgressInfo();
    }

    private long calSingleArmyCollectInfoFromLastCalTsMs(ArmyEntity army, long calTsMs) {
        ProgressInfoProp prop = getOwner().getCollectComponent().getCollectInfoByPlayerId(army.getPlayerId());
        if (null == prop) {
            LOGGER.error("ClanResBuilding, calSingleArmyCollectInfo: prop is null, armyId = {}", army.getEntityId());
            return 0L;
        }
        double collectSpeedPerSec = getCollectSpeedPerSec(army);
        long tryAdd = (long) (TimeUtils.ms2Second(calTsMs - prop.getLastCalTsMs()) * collectSpeedPerSec);
        return Math.min(tryAdd, prop.getMaxNum() - prop.getLastCalNum());
    }

    private long calSingleArmyCollectInfoWithStartAndEndTime(ArmyEntity army, long beginTsMs, long endTsMs) {
        double collectSpeedPerSec = getCollectSpeedPerSec(army);
        return (long) (TimeUtils.ms2Second(endTsMs - beginTsMs) * collectSpeedPerSec);
    }

    /**
     * 用于调试打印的方法
     */
    private void debugPrintMethod() {
        LOGGER.warn("------------------------------ ClanResBuilding: Begin ------------------------------");
        LOGGER.warn("ClanResBuilding: debugPrintMethod");
        // 把进度条prop打出来
        LOGGER.warn("ClanResBuilding: prop = {}", getProgressInfo());
        // 把内部军队的打出来
        LOGGER.warn("ClanResBuilding: playerId2ArmyIdMap = {}", playerId2ArmyIdMap);
        for (Map.Entry<Long, ProgressInfoProp> entry : getOwner().getProp().getEachPlayerCollectMap().entrySet()) {
            LOGGER.warn("ClanResBuilding: playerId = {}, progressInfoProp = {}", entry.getKey(), entry.getValue());
        }
        // 把内存映射打出来
        LOGGER.warn("ClanResBuilding: playerId2ArmyId begin");
        for (Long playerId : playerId2ArmyIdMap.keySet()) {
            LOGGER.warn("ClanResBuilding: playerId = {}, armyId = {}", playerId, playerId2ArmyIdMap.get(playerId));
        }
        LOGGER.warn("ClanResBuilding: playerId2ArmyId end");
        // 把一些简单的内存数据打出来
        LOGGER.warn("ClanResBuilding: firstLeaveArmyId = {}, firstLeaveArmyTsMs = {}", firstLeaveArmyId, firstLeaveArmyTsMs);
        LOGGER.warn("------------------------------ ClanResBuilding: End ------------------------------");
    }
}
package com.yorha.cnc.scene.event.rally;

import com.yorha.cnc.scene.event.ievent.IEventWithClanId;
import com.yorha.common.enums.reason.RallyDismissReason;

/**
 * <AUTHOR>
 * <p>
 * 被集结 解散
 */
public class BeRallyDismissEvent extends IEventWithClanId {
    private final RallyDismissReason dismissReason;
    private final long organizerId;

    public BeRallyDismissEvent(long entityId, long clanId, RallyDismissReason dismissReason, long playerId) {
        super(entityId, clanId);
        this.dismissReason = dismissReason;
        this.organizerId = playerId;
    }

    public RallyDismissReason getDismissReason() {
        return dismissReason;
    }

    public long getOrganizerId() {
        return organizerId;
    }


}

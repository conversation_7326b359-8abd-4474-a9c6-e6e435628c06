package com.yorha.cnc.scene.common;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.city.CityBornService;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Core;
import com.yorha.proto.EntityAttrOuterClass;
import res.template.ConstTemplate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 出生点共用接口
 *
 * <AUTHOR>
 */
public class BornPointHelper {

    /**
     * 出生点阻挡检测   迁城阻挡范围
     *
     * @param scene    场景
     * @param x        横坐标
     * @param y        纵坐标
     * @param radius   自身阻挡半径
     * @param ignoreId 无视的entityId
     * @return 坐标点是否可用
     */
    public static Core.Code collisionCheck(SceneEntity scene, int x, int y, int radius, long ignoreId) {
        return collisionCheck(scene, x, y, radius, ignoreId, true);
    }

    public static Core.Code collisionCheck(SceneEntity scene, int x, int y, int radius, long ignoreId, boolean isUseCityCollision) {
        Point point = Point.valueOf(x, y);
        if (isUseCityCollision) {
            if (!scene.getCityMoveComponent().isPointNavMovable(point)) {
                //LOGGER.warn("randomBornPoint fail nav {}", point);
                return ErrorCode.MAP_STATIC_COLLISION.getCode();
            }
        } else {
            // 静态阻挡
            if (!scene.getPathFindMgrComponent().isPointStaticWalkable(point)) {
                return ErrorCode.MAP_STATIC_COLLISION.getCode();
            }
        }
        // 搜索半径应该是自己的半径加上最大的半径
        int range = ResHolder.getResService(CityBornService.class).getMaxBuildMoveRange() + radius;
        // 获取周围的SceneObj对象
        Collection<SceneObjEntity> objEntitySet = scene.getAoiMgrComponent().getAoiSceneObjList(Circle.valueOf(point, range));
        // 动态阻挡
        for (SceneObjEntity obj : objEntitySet) {
            if (obj.getEntityId() == ignoreId) {
                continue;
            }
            int collisionRadius;
            //无视迁城阻挡只判断行军阻挡
            if (isUseCityCollision) {
                collisionRadius = obj.getTransformComponent().getCityMoveCollisionRadius();
            } else {
                collisionRadius = obj.getTransformComponent().getPathCollisionRadius();
            }
            if (Point.calDisBetweenTwoPoint(point, obj.getCurPoint()) < (collisionRadius + radius)) {
                //LOGGER.warn("randomBornPoint fail nav {} {}", obj.getEntityId(), point);
                return ErrorCode.MAP_DYNAMIC_COLLISION.getCode();
            }
        }
        // 村庄阻挡
        if (scene.isMainScene() && isUseCityCollision) {
            int partId = MapGridDataManager.getPartId(scene.getMapId(), x, y);
            if (scene.getBuildingMgrComponent().checkTownsCollision(partId, point)) {
                return ErrorCode.MAP_STATIC_COLLISION.getCode();
            }
            RegionalAreaSettingTemplate template = scene.getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
            // 再看看相邻区域
            for (Integer id : template.getAdjoinAreaIdList()) {
                if (scene.getBuildingMgrComponent().checkTownsCollision(id, point)) {
                    return ErrorCode.MAP_STATIC_COLLISION.getCode();
                }
            }
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 导量落城专属  在上面的基础上增加一条规则 城池间隙
     * 而且能压野怪也能压资源田
     */
    public static Core.Code cityBornCollisionCheck(SceneEntity scene, int x, int y, int radius) {
        Point point = Point.valueOf(x, y);
        // 搜索半径应该是自己的半径加上最大的半径
        int range = ResHolder.getResService(CityBornService.class).getMaxBuildMoveRange() + radius;
        // 要求的最小城池间距
        int limit = ResHolder.getConsts(ConstTemplate.class).getCityBornDistanceLimit();
        if (limit > range) {
            range = limit;
        }

        List<MonsterEntity> monsterList = new ArrayList<>();
        List<ResBuildingEntity> resBuildingList = new ArrayList<>();
        // 获取周围的SceneObj对象
        Collection<SceneObjEntity> objEntitySet = scene.getAoiMgrComponent().getAoiSceneObjList(Circle.valueOf(point, range));
        // 动态阻挡
        for (SceneObjEntity obj : objEntitySet) {
            double curDis = Point.calDisBetweenTwoPoint(point, obj.getCurPoint());
            if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_City && curDis < limit) {
                return ErrorCode.CITY_LESS_THAN_MINIMUM_CITY_DIS.getCode();
            }
            if (curDis > (obj.getTransformComponent().getCityMoveCollisionRadius() + radius)) {
                continue;
            }
            // 压死的野怪
            if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
                MonsterEntity monster = (MonsterEntity) obj;
                if (monster.getTemplate().getCategory() == CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE && !monster.getBattleComponent().hasTarget()) {
                    monsterList.add(monster);
                    continue;
                }
            }
            // 压没的田
            if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_ResBuilding) {
                ResBuildingEntity resBuilding = (ResBuildingEntity) obj;
                if (resBuilding.getProp().getState() == CommonEnum.ResourceBuildingState.RBS_IDLE) {
                    resBuildingList.add(resBuilding);
                    continue;
                }
            }
            return ErrorCode.MAP_DYNAMIC_COLLISION.getCode();
        }
        for (MonsterEntity monsterEntity : monsterList) {
            monsterEntity.forceRecycle();
        }
        for (ResBuildingEntity resBuildingEntity : resBuildingList) {
            resBuildingEntity.recycle(true);
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 环形范围内随机出生点
     *
     * @param scene         scene
     * @param selfRadius    自身阻挡半径
     * @param ignoreDynamic 是否忽略动态阻挡
     * @return 10次没找到返回null
     */
    public static Point randomRingBornPoint(SceneEntity scene, Shape shape, int selfRadius, boolean ignoreDynamic) {
        for (int i = 0; i < 10; i++) {
            Point point = shape.getRandomPoint();
            if (point == null) {
                continue;
            }
            if (ignoreDynamic) {
                if (!scene.getPathFindMgrComponent().isPointStaticWalkable(point)) {
                    continue;
                }
            } else {
                Core.Code code = collisionCheck(scene, point.getX(), point.getY(), selfRadius, 0);
                if (!ErrorCode.isOK(code)) {
                    continue;
                }
            }
            return point;
        }
        return null;
    }
}

package com.yorha.cnc.zone.zone.kindomSkil.skillEffects;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.enums.error.ErrorCode;

/**
 * 王国技能interface
 *
 * <AUTHOR>
 */
public interface ISkillEffect {
    /**
     * 技能是否生效
     */
    ErrorCode checkCanUse(int skillId, long targetId, int targetZoneId, ZoneEntity zoneEntity);

    /**
     * 技能效果生效
     *
     * @param skillId      策划配置的技能id
     * @param targetId     目标id（语义取决于策划案）
     * @param zoneEntity   zone实体
     * @param targetZoneId
     */
    void effect(int skillId, long targetId, ZoneEntity zoneEntity, int targetZoneId);
}

package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerBattleComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.qlog.battle.BattleAttackType;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncArmyChange;

/**
 * <AUTHOR>
 */
public class ScenePlayerBattleComponent extends AbstractScenePlayerBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerBattleComponent.class);

    public ScenePlayerBattleComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    /**
     * 更新玩家在场景上的数据统计
     */
    @Override
    public void onBattleRelationEnd(BattleAttackType attackType, CommonEnum.BattleResult battleResult, BattleRecord.RoleMemberRecord record, boolean enemyNpc, int otherCurZoneId) {
        if (record == null) {
            return;
        }

        SsPlayerMisc.OnBattleRelationEndCmd.Builder cmdBuilder = SsPlayerMisc.OnBattleRelationEndCmd.newBuilder();
        // 记录战斗结果
        cmdBuilder.setBattleResult(battleResult);

        // 记录战斗击杀
        if (!record.getKill().isEmpty()) {
            cmdBuilder.putAllKillRecord(record.getKill());
        }

        // 出战英雄数据
        cmdBuilder.getHeroOrPlaneRecordBuilder()
                .setMainHeroId(record.getMainHero().getHeroId())
                .setDeputyHeroId(record.getDeputyHero().getHeroId());

        // 士兵战斗详情
        cmdBuilder.addAllSoldierResult(record.getSoldierData());

        cmdBuilder.setNpcEnemy(enemyNpc);
        cmdBuilder.setOtherCurZoneId(otherCurZoneId);
        // 通知玩家
        getOwner().tellPlayer(cmdBuilder.build());
        LOGGER.debug("onBattleRelationEnd {} cmdBuilder:{} roleId:{}", getOwner(), cmdBuilder.build(), record.getMemberRoleId());
        // 发送死兵qlog
        this.sendSoldierDieQlog(record);
    }


    /**
     * 所有战斗关系结束
     *
     * @param totalLoss 指pvp战斗的战力损失，而且我方是单人
     * @param fail      战斗是否失败
     */
    @Override
    public void onAllBattleEnd(long totalLoss, boolean fail) {
        SsPlayerMisc.OnAllBattleEndCmd.Builder builder = SsPlayerMisc.OnAllBattleEndCmd.newBuilder();
        builder.setTotalLoss(totalLoss).setFail(fail);
        getOwner().tellPlayer(builder.build());
    }


    /**
     * 死兵qlog(包含战斗直接死亡与医院超容量死亡)
     *
     * @param record 战斗记录
     */
    private void sendSoldierDieQlog(BattleRecord.RoleMemberRecord record) {
        // 不变字段
        QlogCncArmyChange qlog = new QlogCncArmyChange()
                .setDtEventTime(TimeUtils.now2String())
                .setAction(SoldierNumChangeReason.battle_dead.name())
                .setBeforeArmyNum(0)
                .setAfterArmyNum(0);
        qlog.fillHead(getOwner().getQlogComponent());

        for (BattleRecord.BattleRecordSoldierResult soldierResult : record.getSoldier().values()) {
            final int deadNum = soldierResult.getDead();
            if (deadNum <= 0) {
                continue;
            }
            final int soldierId = soldierResult.getSoldierId();
            qlog.setICount(deadNum)
                    .setAddOrReduce(1)
                    .setArmyId(String.valueOf(soldierId));
            qlog.sendToQlog();
        }

    }

}

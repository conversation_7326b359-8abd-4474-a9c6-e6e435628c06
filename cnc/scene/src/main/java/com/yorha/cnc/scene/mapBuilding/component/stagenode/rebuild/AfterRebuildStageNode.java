package com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 改建完成状态
 *
 * <AUTHOR>
 */
public class AfterRebuildStageNode extends ClanBuildingNode {
    private static final Logger LOGGER = LogManager.getLogger(AfterRebuildStageNode.class);

    public AfterRebuildStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_AFTER_REBUILD;
    }

    @Override
    public void onLoad() {

    }

    @Override
    public void afterRefreshMaxHp(int oldHp, int newHp) {
        // 降低了  直接降就完事了
        if (oldHp >= newHp) {
            getConstructInfoProp().setCurrentDurability(newHp);
            return;
        }
        // 升高了 转入恢复状态
        getComponent().transNewNode(new RecoverStageNode(getOwner()));
    }

    @Override
    public void onHpDec(int decNum) {
        super.onHpDec(decNum);
        getComponent().transNewNode(new RecoverStageNode(getOwner()));
    }

    @Override
    public void onEnter(long enterTs) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        getProp().setState(getStage()).setStateStartTsMs(enterTs);
    }
}

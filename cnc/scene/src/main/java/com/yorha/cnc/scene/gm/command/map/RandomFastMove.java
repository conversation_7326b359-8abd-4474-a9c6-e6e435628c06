package com.yorha.cnc.scene.gm.command.map;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class RandomFastMove implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(RandomFastMove.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        for (ArmyEntity armyEntity : scenePlayer.getArmyMgrComponent().getMyArmyList()) {
            CommonEnum.ArmyActionType actionType = CommonEnum.ArmyActionType.AAT_Move;
            Point point = scenePlayer.getScene().getBigScene().getBornMgrComponent().getBornPointByRegion(RandomUtils.nextInt(0, 9));
            if (point == null) {
                LOGGER.info("RandomFastMove execute player {} {} curPoint {} next point is null", scenePlayer.getPlayerId(), armyEntity, armyEntity.getCurPoint());
                continue;
            }
            StructPlayer.ArmyActionInfo.Builder actionBuilder = StructPlayer.ArmyActionInfo.newBuilder();
            actionBuilder.setArmyActionType(actionType)
                    .setTargetPoint(Struct.Point.newBuilder().setX(point.getX()).setY(point.getY()).build())
                    .setDebugFastMove(true);
            armyEntity.getBehaviourComponent().handlePlayerReq(actionBuilder.build(), 0);
        }
    }

    @Override
    public String showHelp() {
        return "RandomFastMove";
    }
}

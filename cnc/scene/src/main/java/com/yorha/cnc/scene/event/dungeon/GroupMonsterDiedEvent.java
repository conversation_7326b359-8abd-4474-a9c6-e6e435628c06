package com.yorha.cnc.scene.event.dungeon;

import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class GroupMonsterDiedEvent extends IEvent {
    private final int groupId;
    private final boolean isAlive;

    public GroupMonsterDiedEvent(int groupId, boolean isAlive) {
        this.groupId = groupId;
        this.isAlive = isAlive;
    }

    public int getGroupId() {
        return groupId;
    }

    public boolean isAlive() {
        return isAlive;
    }
}

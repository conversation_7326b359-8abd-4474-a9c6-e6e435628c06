package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MonsterInfo implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        SceneEntity sceneEntity = actor.getScene();
        if (sceneEntity.isDestroy()) {
            throw new GeminiException(ErrorCode.MONSTER_DETAIL_LIST);
        }
        List<MonsterEntity> monsters = sceneEntity.getObjMgrComponent().getObjsByType(MonsterEntity.class);
        int total = monsters.size();
        Map<Integer, Integer> levelMap = new HashMap<>();
        Map<Integer, Integer> typeMap = new HashMap<>();
        monsters.forEach(monster -> levelMap.compute(monster.getTemplate().getLevel(), (k, v) -> v == null ? 1 : v + 1));
        monsters.forEach(monster -> {
            if (monster.getTemplate().getCategory() == CommonEnum.MonsterCategory.RALLY_MONSTER){
                typeMap.compute(monster.getTemplate().getLevel(), (k, v) -> v == null ? 1 : v + 1);
            }
        });
        StringBuilder builder = new StringBuilder();
        StringBuilder typeBuilder = new StringBuilder();
        levelMap.forEach((k, v) -> builder.append(k).append("_").append(v).append(";"));
        typeMap.forEach((k, v) -> typeBuilder.append(k).append("_").append(v).append(";"));
        throw new GeminiException(ErrorCode.MONSTER_DETAIL_LIST, String.format("总数:%s，等级分布:%s 集结城寨分布:%s", total, builder, typeBuilder));
    }

    @Override
    public String showHelp() {
        return "MonsterInfo";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MONSTER;
    }
}

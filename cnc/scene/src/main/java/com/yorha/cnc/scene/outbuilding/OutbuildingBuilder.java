package com.yorha.cnc.scene.outbuilding;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.game.gen.prop.OutbuildingProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class OutbuildingBuilder extends SceneObjBuilder<OutbuildingEntity, OutbuildingProp> {
    public OutbuildingBuilder(SceneEntity sceneEntity, long eid, OutbuildingProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public BuildingTransformComponent transformComponent(OutbuildingEntity owner) {
        return new BuildingTransformComponent(owner, owner, getPointProp());
    }
}

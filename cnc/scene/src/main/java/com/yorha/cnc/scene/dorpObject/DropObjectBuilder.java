package com.yorha.cnc.scene.dorpObject;

import com.yorha.cnc.scene.dorpObject.component.DropObjectPickUpComponent;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.game.gen.prop.DropObjectProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class DropObjectBuilder extends SceneObjBuilder<DropObjectEntity, DropObjectProp> {

    public DropObjectBuilder(SceneEntity sceneEntity, long eid, DropObjectProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    public DropObjectPickUpComponent pickUpComponent(DropObjectEntity owner) {
        return new DropObjectPickUpComponent(owner);
    }
}

package com.yorha.cnc.scene.mail;

import com.google.protobuf.Message;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.mail.MailMgrBase;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.mail.MailTemplateService;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.StructMail;
import com.yorha.proto.TcaplusDb;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class ZoneMailMgr extends MailMgrBase {
    public ZoneMailMgr(SceneActor actor) {
        super(actor);
    }

    @Override
    public SceneActor getActor() {
        return (SceneActor) super.getActor();
    }

    /**
     * 全服邮件
     */
    private final ZoneMailData zoneMailData = new ZoneMailData();

    /**
     * 加载邮件
     */
    public void loadMails(List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> mails) {
        for (ValueWithVersion<TcaplusDb.MailStorageTable.Builder> vv : mails) {
            final TcaplusDb.MailStorageTable.Builder mail = vv.value;
            final StructMail.NewMailCache cache = mail.getFullAttr();
            // cache 全服邮件
            if (mail.getFullAttr().getMailType() == CommonEnum.MailType.MAIL_TYPE_ZONE) {
                zoneMailData.addMailCache(cache);
            } else {
                LOGGER.warn("need del: ZoneMailMgr loadMails mail cache type failed, mailId={}", cache.getMailId());
            }
        }
        this.clearZoneMails();
    }

    @Override
    public int getMailIndex() {
        return zoneMailData.getMaxMailIndex();
    }

    @Override
    protected Message.Builder getInsertReqBuilder(StructMail.NewMailCache mailCache) {
        TcaplusDb.MailStorageTable.Builder req = TcaplusDb.MailStorageTable.newBuilder();
        req.setZoneId(getActor().getZoneId());
        req.setMailId(mailCache.getMailId());
        req.setFullAttr(mailCache);
        return req;
    }

    @Override
    protected Message.Builder getRemoveReqBuilder(long mailId) {
        TcaplusDb.MailStorageTable.Builder req = TcaplusDb.MailStorageTable.newBuilder();
        req.setZoneId(getActor().getZoneId());
        req.setMailId(mailId);
        return req;
    }

    @Override
    protected void afterRemoveMail(long mailId) {
        final boolean isRemoved = zoneMailData.removeMailCache(mailId);
        LOGGER.info("ZoneMailMgr afterRemoveMail mailId={}, isRemoved={}, zoneMailTotalNum={}", mailId, isRemoved, zoneMailData.getMailCacheMap().size());
    }

    /**
     * 发全服邮件
     *
     * @param mailId         邮件id
     * @param mailSendParams 发送邮件参数
     */
    public void sendZoneMail(final long mailId, StructMail.MailSendParams mailSendParams) {
        final int nextMailIndex = zoneMailData.getNextMailIndex();
        LOGGER.info("sendZoneMail mailId:{} nextMailIndex:{}", mailId, nextMailIndex);

        Consumer<StructMail.NewMailCache> afterDbInsert = (newMailCache) -> {
            // 通知在线玩家接收邮件
            broadcastZoneOnlinePlayerIds(newMailCache);
            LOGGER.info("ZoneMailMgr sendZoneMail mailId={}, templateId={}, mailIndex={}", newMailCache.getMailId(), newMailCache.getMailTemplateId(), nextMailIndex);
            clearZoneMails();
        };
        // 添加邮件storage
        addNewMailCacheToDb(mailSendParams, CommonEnum.MailType.MAIL_TYPE_ZONE, zoneMailData, getActor().getZoneId(), mailId, nextMailIndex, afterDbInsert);
    }

    public List<StructMail.NewMailCache> getOfflineMails(int mailIndex, long createTime) {
        List<StructMail.NewMailCache> offlineMails = new ArrayList<>();
        if (mailIndex < 0) {
            return offlineMails;
        }
        if (zoneMailData.getMaxMailIndex() > mailIndex) {
            for (StructMail.NewMailCache cache : zoneMailData.getMailCacheMap().values()) {
                if (MailUtil.mailIndexChecker(cache.getMailIndex(), mailIndex)
                        && MailUtil.mailExpireTimeChecker(cache.getExpireTimestamp())
                        && MailUtil.mailCreateTimeChecker(cache.getCreateTimestamp(), createTime)) {
                    offlineMails.add(cache);
                }
            }
        }
        return offlineMails;
    }

    /**
     * 发送所有在线玩家邮件
     *
     * @param newMailCache 邮件缓存
     */
    private void broadcastZoneOnlinePlayerIds(StructMail.NewMailCache newMailCache) {
        final SsPlayerMisc.OnReceiveMailCmd cmd = SsPlayerMisc.OnReceiveMailCmd.newBuilder()
                .setNewMailCache(newMailCache)
                .build();
        BroadcastHelper.toSsOnlinePlayerInZone(this.getActor().getZoneId(), cmd);
    }

    /**
     * 清理全区邮件
     */
    private void clearZoneMails() {
        final int expireDays = ResHolder.getResService(MailTemplateService.class).getMailExpireTemplate(CommonEnum.MailTabsType.MAIL_TABS_TYPE_PERSONAL).getExpire();
        final int maxZoneMailNum = ResHolder.getResService(MailTemplateService.class).getMaxMailNum();
        LOGGER.info("ZoneMailMgr start clearZoneMails size:{}", zoneMailData.getMailCacheMap().size());
        clearMails(zoneMailData, expireDays, maxZoneMailNum);
        // 监控全服邮件缓存数量，clearZoneMails在发送和load的时候都会调用，且是删除邮件的唯一入口，所以监控放在这里
        LOGGER.info("ZoneMailMgr after clearZoneMails size:{}", zoneMailData.getMailCacheMap().size());
    }
}

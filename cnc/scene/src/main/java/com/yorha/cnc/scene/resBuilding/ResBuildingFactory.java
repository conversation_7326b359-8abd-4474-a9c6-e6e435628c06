package com.yorha.cnc.scene.resBuilding;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ResBuildingProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrDb;
import res.template.ResourceTemplate;

/**
 * <AUTHOR>
 */
public class ResBuildingFactory {

    /**
     * 创建资源建筑
     */
    public static ResBuildingEntity createResBuilding(SceneEntity scene, int templateId, int x, int y) {
        int size = scene.getObjMgrComponent().getObjsNumByType(ResBuildingEntity.class);
        if (size >= BigSceneConstants.RES_BUILDING_NUM_MAX) {
            WechatLog.error("try createResBuilding but num is max {}", size);
            return null;
        }
        int resNum = ResHolder.getInstance().getValueFromMap(ResourceTemplate.class, templateId).getResNum();
        ResBuildingProp prop = new ResBuildingProp();
        prop.setTemplateId(templateId)
                .setState(CommonEnum.ResourceBuildingState.RBS_IDLE)
                .setCurNum(resNum)
                .getPoint().setX(x).setY(y);
        int partId = MapGridDataManager.getPartId(scene.getMapId(), x, y);
        MapBuildingEntity mapBuilding = scene.getBuildingMgrComponent().getMapBuilding(partId);
        // 所属联盟
        if (mapBuilding != null && mapBuilding.getOwnerClanId() != 0) {
            SceneClanEntity ownerSceneClan = mapBuilding.getOccupyComponent().getOwnerSceneClan();
            prop.setClanId(ownerSceneClan.getEntityId()).setClanSimpleName(ownerSceneClan.getClanSimpleName());
        }
        prop.unMarkAll();
        ResBuildingBuilder builder = new ResBuildingBuilder(scene, scene.ownerActor().nextId(), prop);
        ResBuildingEntity entity = new ResBuildingEntity(builder);
        entity.addIntoScene();
        return entity;
    }

    public static void restoreResBuilding(SceneEntity scene, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        ResBuildingProp prop = ResBuildingProp.of(fullAttr.getResBuildingAttr(), changedAttr.getResBuildingAttr());
        ResBuildingBuilder builder = new ResBuildingBuilder(scene, fullAttr.getEntityId(), prop);
        ResBuildingEntity entity = new ResBuildingEntity(builder, true);
        // kvk 兼容线上数据
        if (entity.getZoneId() == 0 && entity.getProp().getCollect().getPlayerId() != 0) {
            AbstractScenePlayerEntity scenePlayer = scene.getPlayerMgrComponent().getScenePlayer(entity.getProp().getCollect().getPlayerId());
            entity.getProp().getCollect().setZoneId(scenePlayer.getZoneId());
        }
        if (!(scene.getResMgrComponent() instanceof MainSceneResMgrComponent)) {
            return;
        }
        final MainSceneResMgrComponent mainSceneResMgrComponent = (MainSceneResMgrComponent) scene.getResMgrComponent();
        mainSceneResMgrComponent.addResBuilding(entity);
        entity.addIntoScene();
    }
}

package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CityInnerArmyProp;
import com.yorha.game.gen.prop.InnerArmyInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ClanScoreCategory;
import com.yorha.proto.CommonEnum.OccupyState;
import com.yorha.proto.SsClanAttr.OnAddClanScoreForClanCmd;
import com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 内部部队管理
 */
public class MapBuildingInnerArmyComponent extends SceneObjInnerArmyComponent {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingInnerArmyComponent.class);

    public MapBuildingInnerArmyComponent(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::endAllBattle, EndAllBattleEvent.class);
    }

    @Override
    public int getBeAidedMaxNum() {
        // 策划需求，防守方不看车头属性
        // 军团建筑需要读取改建后的最大容量
        return getOwner().getBuildingTemplate().getMaxSoldierNum();
    }

    /**
     * 军队占领成功的到达
     */
    public boolean armyArrivedByOccupy(ArmyEntity armyEntity) {
        if (!armyEntity.getBattleComponent().hasAnyAlive()) {
            return false;
        }
        if (getProp().getArmy().containsKey(armyEntity.getEntityId())) {
            return false;
        }
        int aliveSoldierNum = armyEntity.getAliveSoldierNum();
        if (!ErrorCode.isOK(checkArmyCanAssist(armyEntity.getPlayerId(), armyEntity.getCampEnum(), aliveSoldierNum, armyEntity.getEntityId()))) {
            return false;
        }
        armyArrived(armyEntity);
        // 改写成进入建筑援助的状态
        armyEntity.getAssistComponent().occupyToAssist(getOwner().getCurPoint(), getOwner());
        getOwner().getQLogComponent().sendExpansionLog(armyEntity.getClanId(), "defence_guild_building_conquering", armyEntity.getPlayerId());
        return true;
    }

    @Override
    public boolean addWarningItem(SceneObjEntity obj, CommonEnum.WarningType type) {
        if (!super.addWarningItem(obj, type)) {
            return false;
        }
        Set<Long> playerSet = new HashSet<>();
        try {
            // 预警  要给城内到的人都发一遍    一个人能派多个部队 要过滤
            for (InnerArmyInfoProp prop : getProp().getArmy().values()) {
                if (playerSet.contains(prop.getPlayerId())) {
                    continue;
                }
                AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(prop.getPlayerId());
                if (scenePlayer == null) {
                    continue;
                }
                scenePlayer.getWarningComponent().addWarningItem(obj, type);
                playerSet.add(prop.getPlayerId());
            }
        } catch (Exception e) {
            LOGGER.error("addWarningItem error ", e);
        }
        return true;
    }

    @Override
    public boolean removeWarningItem(long objId) {
        if (!super.removeWarningItem(objId)) {
            return false;
        }
        Set<Long> playerSet = new HashSet<>();
        try {
            // 预警  要给城内到的人都发一遍    一个人能派多个部队 要过滤
            for (InnerArmyInfoProp prop : getProp().getArmy().values()) {
                if (playerSet.contains(prop.getPlayerId())) {
                    continue;
                }
                AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(prop.getPlayerId());
                if (scenePlayer == null) {
                    continue;
                }
                scenePlayer.getWarningComponent().removeWarningItem(objId);
                playerSet.add(prop.getPlayerId());
            }
        } catch (Exception e) {
            LOGGER.error("removeWarningItem error ", e);
        }
        return true;
    }

    @Override
    public void armyArrived(ArmyEntity army) {
        super.armyArrived(army);
        // 第一个到达的设置为队长
        if (getProp().getLeaderArmyId() == 0) {
            changeLeader(army.getEntityId());
        }
        // 到达后要加未到达人的援助预警
        addWarningItemWhenArrive(army.getPlayerId());
        // 打个qlog
        getOwner().getQLogComponent().onArmyArrived(army.getClanId(), army.getPlayerId());
    }

    @Override
    protected void beforeRemoveArmy(ArmyEntity army, boolean isIn, long operatorId) {
        long armyId = army.getEntityId();
        if (isIn) {
            // 移除这个人对未到达人的援助预警
            removeWarningItemWhenLeave(army.getPlayerId());
            // 结算联盟积分  不能影响退出流程
            try {
                calClanScoreBeforeLeave(armyId, army, operatorId);
            } catch (Exception e) {
                LOGGER.error("{} calClanScoreBeforeLeave {} error ", getOwner(), army, e);
            }
        }
        if (armyId == getProp().getLeaderArmyId()) {
            chooseNewLeader(armyId);
        }
    }

    private void calClanScoreBeforeLeave(long armyId, ArmyEntity armyEntity, long operatorId) {
        if (getOwner().getProp().getOccupyinfo().getState() == OccupyState.TOS_OCCUPYING) {
            // 增加个人占领积分
            double occupyScoreRatio = SceneAddCalc.getScoreRatio(ResHolder.getResService(ConstClanKVResService.class).getTemplate().getOccupyScoreRatio(), getOwner().getOccupyComponent().getOccupySceneClan());
            long addScore = (long) Math.floor(TimeUtils.ms2Second((SystemClock.now() - getProp().getArmyV(armyId).getEnterTsMs())) * occupyScoreRatio);
            OnAddClanScoreCmd.Builder tell = OnAddClanScoreCmd.newBuilder().setScoreType(ClanScoreCategory.CSC_OCCUPY_AND_BUILD).setInBuildingWhenOccupy(false).setIsOccupyAddScore(true);
            long now = SystemClock.now();
            long beginOccupyTsMs = getOwner().getProp().getOccupyinfo().getStateStartTsMs();
            if (isArmyNeedAddTime(armyId, beginOccupyTsMs)) {
                final int intervalSec = (int) getReturnArmyAddTime(armyId, now, beginOccupyTsMs);
                tell.setScoreUseInterval(intervalSec);
            }
            armyEntity.getScenePlayer().tellPlayer(tell.setAddValue(addScore).build());
            addClanOccupyOrRebuildScore(addScore);
        } else if (getOwner().getProp().getOccupyinfo().getState() == OccupyState.TOS_REBUILD) {
            // 增加个人建设积分及建设时间
            double rebuildScoreRatio = SceneAddCalc.getScoreRatio(getOwner().getClanBuildingTemplate().getCoefficient(), getOwner().getOccupyComponent().getOwnerSceneClan());
            OnAddClanScoreCmd.Builder tell = OnAddClanScoreCmd.newBuilder().setScoreType(ClanScoreCategory.CSC_OCCUPY_AND_BUILD).setIsBuildAddScore(true);
            long now = SystemClock.now();
            long beginRebuildTsMs = getOwner().getProp().getOccupyinfo().getStateStartTsMs();
            long addScore = (long) Math.floor(TimeUtils.ms2Second(now - getProp().getArmyV(armyId).getEnterTsMs()) * rebuildScoreRatio);
            tell.setAddValue(addScore);
            if (isArmyNeedAddTime(armyId, beginRebuildTsMs)) {
                final int intervalSec = (int) getReturnArmyAddTime(armyId, now, beginRebuildTsMs);
                tell.setScoreUseInterval(intervalSec);
            }
            armyEntity.getScenePlayer().tellPlayer(tell.build());
            addClanOccupyOrRebuildScore(addScore);
        }
        // 发个Qlog
        getOwner().getQLogComponent().onArmyLeave(getOwner().getClanId(), operatorId);
    }

    /**
     * 获取玩家id到玩家联盟建筑建设分数的映射，建筑分数为玩家所有参与建筑的军队累加
     *
     * @param endTsMs        结算的结束时间
     * @param beginBuildTsMs 联盟建筑建设的开始时间
     * @return 玩家id到玩家联盟建筑建设分数的映射
     */
    private Map<Long, Integer> getPlayerClanRebuildScoreMap(long endTsMs, long beginBuildTsMs) {
        Map<Long, Integer> playerIdToRebuildScore = new HashMap<>();
        int rebuildScoreRatio = getOwner().getClanBuildingTemplate().getCoefficient();
        for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
            long playerId = entry.getValue().getPlayerId();
            int addScore = (int) (TimeUtils.ms2Second(endTsMs - Math.max(beginBuildTsMs, entry.getValue().getEnterTsMs())) * rebuildScoreRatio);
            if (playerIdToRebuildScore.containsKey(playerId)) {
                playerIdToRebuildScore.put(playerId, addScore + playerIdToRebuildScore.get(playerId));
            } else {
                playerIdToRebuildScore.put(playerId, addScore);
            }
        }
        return playerIdToRebuildScore;
    }

    /**
     * 为建筑内所有的军队添加建设分数、记录建设时间
     *
     * @param endTsMs        结算的结束时间
     * @param beginBuildTsMs 联盟建筑建设的开始时间
     */
    public void addAllPlayerClanRebuildScore(long endTsMs, long beginBuildTsMs) {
        try {
            Map<Long, Integer> playerToClanRebuildScore = getPlayerClanRebuildScoreMap(endTsMs, beginBuildTsMs);
            Map<Long, Integer> playerToClanRebuildTime = getPlayerTimeMap(endTsMs, beginBuildTsMs);

            Set<Long> alreadyAddPlayer = new HashSet<>();
            for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
                long playerId = entry.getValue().getPlayerId();
                if (alreadyAddPlayer.contains(playerId)) {
                    // 当前player已经被添加过分数，跳过
                    continue;
                }
                AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(entry.getValue().getPlayerId());
                if (scenePlayer == null) {
                    continue;
                }
                LOGGER.info("MapBuildingInnerArmyComponent addAllPlayerClanRebuildScore playerId={}", playerId);
                OnAddClanScoreCmd.Builder cmdBuilder = OnAddClanScoreCmd.newBuilder();
                cmdBuilder.setScoreType(ClanScoreCategory.CSC_OCCUPY_AND_BUILD)
                        .setIsBuildAddScore(true)
                        .setIsFinishBuild(true)
                        .setAddValue(playerToClanRebuildScore.getOrDefault(playerId, 0))
                        .setScoreUseInterval(playerToClanRebuildTime.getOrDefault(playerId, 0));
                scenePlayer.tellPlayer(cmdBuilder.build());
                alreadyAddPlayer.add(playerId);
            }
            // 所有玩家的分数总和等于军团获得的积分
            long sum = playerToClanRebuildScore.values().stream().mapToLong(x -> x).sum();
            addClanOccupyOrRebuildScore(sum);
        } catch (Exception e) {
            LOGGER.error("{} addAllPlayerClanRebuildScore cal error ", getOwner(), e);
        }
    }

    public void addAllPlayerClanOccupyScore(long endTsMs, long beginOccupyTsMs) {
        long totalScore = 0L;
        try {
            int occupyScoreRatio = ResHolder.getResService(ConstClanKVResService.class).getTemplate().getOccupyScoreRatio();
            Map<Long, Integer> playerToOccupyTime = getPlayerTimeMap(endTsMs, beginOccupyTsMs);
            // 统计每个玩家能获得的分数，保证对每个player仅发送一条消息（占领据点x次任务要求）
            Map<Long, Long> playerId2Score = new HashMap<>();
            for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
                int addScore = (int) (TimeUtils.ms2Second(endTsMs - entry.getValue().getEnterTsMs()) * occupyScoreRatio);
                long playerId = entry.getValue().getPlayerId();
                playerId2Score.put(playerId, playerId2Score.getOrDefault(playerId, 0L) + addScore);
            }
            for (Map.Entry<Long, Long> entry : playerId2Score.entrySet()) {
                OnAddClanScoreCmd.Builder tell = OnAddClanScoreCmd.newBuilder().setScoreType(ClanScoreCategory.CSC_OCCUPY_AND_BUILD)
                        .setInBuildingWhenOccupy(true)
                        .setIsOccupyAddScore(true)
                        .setScoreUseInterval(playerToOccupyTime.getOrDefault(entry.getKey(), 0))
                        .setAddValue(entry.getValue().intValue());
                AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(entry.getKey());
                scenePlayer.tellPlayer(tell.build());
                totalScore += entry.getValue();
            }
            // 所有玩家的分数总和等于军团获得的积分
            addClanOccupyOrRebuildScore(totalScore);
        } catch (Exception e) {
            LOGGER.error("{} addAllPlayerClanOccupyScore cal error ", getOwner(), e);
        }
    }

    private void addClanOccupyOrRebuildScore(long addScore) {
        if (addScore <= 0L) {
            // 白嫖军团建筑时addScore可能为0
            return;
        }
        OnAddClanScoreForClanCmd.Builder cmdBuilder = OnAddClanScoreForClanCmd.newBuilder();
        cmdBuilder.setReason("participate_build");
        cmdBuilder.setScore(addScore);
        cmdBuilder.setEntityId(getEntityId());
        if (getOwner().getClanId() == 0) {
            LOGGER.error("addScore {} may be lost because of trying to get clan Id 0", addScore);
            return;
        }
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(getOwner().getClanId());
        if (sceneClan == null) {
            LOGGER.error("addScore {} may be lost because of trying to get clan null {}", addScore, getOwner().getClanId());
            return;
        }
        sceneClan.tellClan(cmdBuilder.build());
    }

    public void chooseNewLeader(long oldLeaderArmyId) {
        List<Long> armyIds = new ArrayList<>(getProp().getArmy().keySet());
        armyIds.remove(oldLeaderArmyId);
        if (armyIds.size() == 0) {
            changeLeader(0);
            return;
        }
        armyIds.sort(Comparator.comparingInt(o -> getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, o).getArmyHeroPower()));
        changeLeader(armyIds.get(armyIds.size() - 1));
    }

    @Override
    public void checkCanRepatriateArmy(long playerId, ArmyEntity army, boolean isPermission) {
        // 自己的 可以踢
        if (playerId == army.getPlayerId()) {
            return;
        }
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        if (scenePlayer.getClanId() == 0 || scenePlayer.getClanId() != getOwner().getClanId()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        // 不是队长 又没有联盟权限
        ArmyEntity leaderArmy = getLeaderArmy();
        if (leaderArmy != null && leaderArmy.getPlayerId() != playerId && !isPermission) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
    }

    /**
     * 玩家操作主动更换队长
     */
    public void checkAndChangeLeader(long playerId, boolean isClanPermission, long armyId) {
        if (getProp().getLeaderArmyId() == armyId) {
            LOGGER.warn("{} checkAndChangeLeader army: {} already is leader", getOwner(), armyId);
            return;
        }
        // 不在援助中
        if (getProp().getArmyV(armyId) == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
//        // 战斗中无法更换
//        if (getOwner().getProp().getBattle().getBattleState() == BattleState.BS_DOING) {
//            throw new GeminiException(ErrorCode.ASSIST_IN_BATTLE.getCodeId());
//        }
        // 不是队长 又没有联盟权限
        ArmyEntity leaderArmy = getLeaderArmy();
        if (leaderArmy != null && leaderArmy.getPlayerId() != playerId && !isClanPermission) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        changeLeader(armyId);
    }

    @Override
    protected void changeLeader(long armyId) {
        super.changeLeader(armyId);
        if (getProp().getLeaderArmyId() <= 0) {
            getOwner().getProp().getRecommendSoldierTypeList().clear();
        }
    }

    @Override
    public CityInnerArmyProp getProp() {
        return getOwner().getProp().getInnerArmy();
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }
}

package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.pathfinding.manager.PathFindingManager;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class JudgeGateCrossing implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        PathFindingManager manager = actor.getScene().getPathFindMgrComponent().getManager();
        StringBuilder string = new StringBuilder();
        string.append("关隘通行校验不通过数据：\n");
        actor.getScene().getMapTemplateDataItem().getMap(RegionalAreaSettingTemplate.class).values().forEach(data -> {
            if (data.getAreaType() != CommonEnum.MapAreaType.CROSSING) {
                return;
            }

            List<Integer> Link1 = data.getLink1List();
            List<Integer> Link2 = data.getLink2List();

            for (int i = 0; i < data.getLink1List().size(); i++) {
                if (!manager.isPointNavWalkable(Point.valueOf(Link1.get(i), Link1.get(i + 1)))) {
                    string.append("片id：");
                    string.append(data.getId());
                    string.append(" ");
                    string.append("坐标： ");
                    string.append(Link1.get(i));
                    string.append(",");
                    string.append(Link1.get(i + 1));
                    string.append("\n");
                }

                if (!manager.isPointNavWalkable(Point.valueOf(Link2.get(i), Link2.get(i + 1)))) {
                    string.append("片id：");
                    string.append(data.getId());
                    string.append(" ");
                    string.append("坐标： ");
                    string.append(Link2.get(i));
                    string.append(",");
                    string.append(Link2.get(i + 1));
                    string.append("\n");
                }
                i++;
            }
        });

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(string.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("JudgeGate");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(actor.getScenePlayer(playerId).getZoneId())
                        .build(),
                builder.build());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

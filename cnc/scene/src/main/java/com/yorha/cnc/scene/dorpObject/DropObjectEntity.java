package com.yorha.cnc.scene.dorpObject;

import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.dorpObject.component.DropObjectPickUpComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.drop.DropTemplateService;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.DropObjectProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ArmyTargetType;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.lang3.NotImplementedException;
import qlog.flow.QlogCncMapPublicChest;
import res.template.DropObjectTemplate;

/**
 * <AUTHOR>
 */
public class DropObjectEntity extends SceneObjEntity {
    private final DropObjectProp prop;
    private final DropObjectPickUpComponent pickUpComponent;
    private int cost;

    public DropObjectEntity(DropObjectBuilder builder) {
        super(builder);
        prop = builder.getProp();
        pickUpComponent = builder.pickUpComponent(this);
        initAllComponents();
        getPropComponent().initPropListener(false);
    }

    @Override
    public void onLifeEnd() {
        if (getTemplate().getDropObjectType() == CommonEnum.DropObjectType.BUFF) {
            if (getScene().isMainScene()) {
                int partId = MapGridDataManager.getPartId(getScene().getMapId(), getCurPoint().getX(), getCurPoint().getY());
                ((MainSceneObjMgrComponent) getScene().getObjMgrComponent()).deleteDropRune(partId);
            }
        }
        if (getScene().isMainScene()) {
            QlogCncMapPublicChest.init(getScene().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("map_chest_disappeared")
                    .setRoleId(String.valueOf(0))
                    .setBeforePickTimes(getProp().getPickUpTimes())
                    .setAfterPickTimes(0)
                    .setMapChestID(getTemplate().getId())
                    .setUniqueMapChestID(getEntityId())
                    .sendToQlog();
        }

        deleteObj();
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_DropObject;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getDropObjectAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getDropObjectAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getDropObjectAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public DropObjectProp getProp() {
        return prop;
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return null;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return null;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return null;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return SceneObjectEnum.SOE_DROP;
    }

    @Override
    public boolean briefEntityAttr(Entity.SceneObjBriefAttr.Builder builder) {
        builder.getDropObjAttrBuilder().setTemplateId(getProp().getTemplateId())
                .getPointBuilder().setX(getCurPoint().getX()).setY(getCurPoint().getX());
        return true;
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        prop.getTarget().setTargetType(ArmyTargetType.ATT_DROP_OBJ)
                .setName("")
                .setClanSimpleName("")
                .setTemplateId(getProp().getTemplateId())
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }

    @Override
    public long getPlayerId() {
        return 0;
    }

    @Override
    public long getClanId() {
        return 0;
    }

    @Override
    public Camp getCampEnum() {
        return Camp.C_NONE;
    }

    public DropObjectPickUpComponent getPickUpComponent() {
        return pickUpComponent;
    }

    public DropObjectTemplate getTemplate() {
        return ResHolder.getResService(DropTemplateService.class).getDropTemplate(getProp().getTemplateId());
    }

    public int getPickUpTime() {
        return getTemplate().getPickUpTime();
    }

    @Override
    public String toString() {
        return "DropObj{" + "id=" + getEntityId() + " templateId=" + getProp().getTemplateId() + '}';
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public int getCost() {
        return cost;
    }


}

package com.yorha.cnc.scene.npcplayer;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerBuilder;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerRallyComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerAdditionComponent;
import com.yorha.cnc.scene.npcplayer.component.NpcPlayerArmyMgrComponent;
import com.yorha.cnc.scene.npcplayer.component.NpcPlayerRallyComponent;
import com.yorha.cnc.scene.npcplayer.component.NpcPlayerSoldierComponent;
import com.yorha.cnc.scene.sceneplayer.component.ScenePlayerDevBuffComponent;

/**
 * <AUTHOR>
 */
public class NpcPlayerBuilder extends AbstractScenePlayerBuilder<NpcPlayerEntity> {

    @Override
    public AbstractScenePlayerRallyComponent rallyComponent(NpcPlayerEntity owner) {
        return new NpcPlayerRallyComponent(owner);
    }

    @Override
    public ScenePlayerDevBuffComponent devBuffComponent(NpcPlayerEntity owner) {
        return null;
    }

    @Override
    public AbstractScenePlayerAdditionComponent additionComponent(NpcPlayerEntity owner) {
        return null;
    }

    @Override
    public NpcPlayerArmyMgrComponent armyMgrComponent(NpcPlayerEntity owner) {
        return new NpcPlayerArmyMgrComponent(owner);
    }

    @Override
    public NpcPlayerSoldierComponent soldierMgrComponent(NpcPlayerEntity owner) {
        return new NpcPlayerSoldierComponent(owner);
    }

}

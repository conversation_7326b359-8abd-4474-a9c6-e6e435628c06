package com.yorha.cnc.scene.actorservice;

import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.AoiMgrComponent;
import com.yorha.cnc.scene.entity.component.GridAoiMgrComponent;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ScenePlayerMgrService;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.notification.NotificationTokenHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.pushNotification.PushNotificationResService;
import com.yorha.common.utils.MiscUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.ScenePlayerProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.SsScenePlayer.*;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstNewbieSvrTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 场景玩家数据相关
 *
 * <AUTHOR>
 */
public class ScenePlayerServiceImpl implements ScenePlayerMgrService {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerServiceImpl.class);
    private final SceneActor sceneActor;

    public ScenePlayerServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    public long getSceneId() {
        return this.sceneActor.getSceneId();
    }

    public ScenePlayerEntity getScenePlayer(long playerId) {
        return sceneActor.getScenePlayer(playerId);
    }

    @Override
    public void handleFirstEnterBigSceneAsk(FirstEnterBigSceneAsk ask) {
        BigSceneEntity scene = sceneActor.getBigScene();
        if (scene == null) {
            throw new GeminiException("not in big scene, CurSceneId=" + getSceneId());
        }
        FirstEnterBigSceneAns.Builder builder = FirstEnterBigSceneAns.newBuilder();
        ErrorCode checkOpenIdCanRegister = scene.getPlayerMgrComponent().checkOpenIdCanRegister(ask.getOpenId());
        if (checkOpenIdCanRegister.isNotOk()) {
            sceneActor.answer(builder.setCode(checkOpenIdCanRegister.getCode()).build());
            return;
        }

        ZoneEntity zoneEntity = scene.getZoneEntity();
        // 导量check  白名单不限制
        if (!ask.getIsWhite()) {
            ErrorCode errorCode = zoneEntity.getBornMgrComponent().checkCreatePlayer();
            if (errorCode.isNotOk()) {
                sceneActor.answer(builder.setCode(errorCode.getCode()).build());
                return;
            }
        }
        // 导量选点
        Point point = scene.getBornMgrComponent().getDrivenBornPoint(ask.getPlayerId(), ask.getHardwareLevel());
        if (point == null) {
            sceneActor.answer(builder.setCode(ErrorCode.BORN_POINT_NOT_FOUND.getCode()).build());
            return;
        }
        // 需要将出生州id回传给player(之所以不在playerActor上计算，是因为未来PlayerActor所在的进程有可能不加载MapGridDataManager)
        int regionId = MapGridDataManager.getRegionId(scene.getMapId(), point);
        builder.setBornRegionId(regionId);
        // 创建scenePlayer 和 city
        int index = zoneEntity.getBornMgrComponent().getNameIndex() + 1;
        String playerName = NameHelper.newPlayerName(ask.getClientLanguage(), scene.getZoneId(), index);
        scene.getPlayerMgrComponent().createNewScenePlayer(ask.getPlayerId(), playerName, ask.getCardHead(), ask.getCreateTime(), point);
        zoneEntity.getBornMgrComponent().addPlayerNum(regionId, ask.getHardwareLevel(), ask.getIsInternalPlayer());
        builder.setServerOpenTsMs(scene.getOpenTsMs())
                .setBornIndex(index)
                .setName(playerName)
                .getBornPointBuilder().setX(point.getX()).setY(point.getY());
        LOGGER.info("atZoneDrive handleFirstEnterBigSceneAsk playerId={} playerName={} index={} mainCity={} internal={} hardwareLevel={} region={}", ask.getPlayerId(), playerName, index, point, ask.getIsInternalPlayer(), ask.getHardwareLevel(), regionId);
        sceneActor.answer(builder.setCode(ErrorCode.OK.getCode()).build());
    }

    @Override
    public void handleUpdatePlayerViewAsk(UpdatePlayerViewAsk ask) {
        AoiMgrComponent aoiMgrComponent = getScene().getAoiMgrComponent();
        if (aoiMgrComponent instanceof GridAoiMgrComponent) {
            boolean isOk = ((GridAoiMgrComponent) aoiMgrComponent).onPlayerUpdateView(ask);
            sceneActor.answer(UpdatePlayerViewAns.newBuilder().setIsOk(isOk).build());
            return;
        }
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleClearPlayerViewAsk(ClearPlayerViewAsk ask) {
        AoiMgrComponent aoiMgrComponent = getScene().getAoiMgrComponent();
        if (aoiMgrComponent instanceof GridAoiMgrComponent) {
            ((GridAoiMgrComponent) aoiMgrComponent).onPlayerClearView(ask.getPlayerId(), ask.getIsByStander(), ask.getIsChangeZone());
            sceneActor.answer(ClearPlayerViewAns.getDefaultInstance());
            return;
        }
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handlePlayerAddSoldierAsk(PlayerAddSoldierAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        scenePlayer.getSoldierMgrComponent().playerAddSoldier(ask.getSoldierId(), ask.getAddNum(), SoldierNumChangeReason.valueOf(ask.getReason()));
        sceneActor.answer(PlayerAddSoldierAns.getDefaultInstance());
    }

    @Override
    public void handleDismissInCitySoldiersAsk(DismissInCitySoldiersAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        scenePlayer.getSoldierMgrComponent().dismissInCitySoldiers(ask.getSoldierDataMap(), SoldierNumChangeReason.valueOf(ask.getReason()));
        sceneActor.answer(DismissInCitySoldiersAns.getDefaultInstance());
    }

    @Override
    public void handleReturnTreatOverSoldiersAsk(ReturnTreatOverSoldiersAsk ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        sceneActor.answer(scenePlayer.getHospitalComponent().handleReturnTreatOverSoldiers());
    }

    @Override
    public void handleGetAllSoldierAsk(GetAllSoldierAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        GetAllSoldierAns.Builder ans = GetAllSoldierAns.newBuilder();
        ans.putAllSoldier(scenePlayer.getSoldierMgrComponent().getAllSoldiers());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleHospitalTreatCheckAsk(HospitalTreatCheckAsk ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getHospitalComponent().handleHospitalTreatCheckAsk(ask);
        sceneActor.answer(HospitalTreatCheckAns.getDefaultInstance());
    }

    @Override
    public void handleHospitalTreatAsk(HospitalTreatAsk ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getHospitalComponent().handleHospitalTreatAsk(ask);
        sceneActor.answer(HospitalTreatAns.getDefaultInstance());
    }

    @Override
    public void handleHospitalFastTreatAsk(HospitalFastTreatAsk ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        Map<Integer, Integer> soldierId2Num = scenePlayer.getHospitalComponent().handleHospitalFastTreatAsk(ask);
        sceneActor.answer(HospitalFastTreatAns.newBuilder().putAllSoldierId2Num(soldierId2Num).build());
    }

    @Override
    public void handleHospitalTreatFinishCmd(HospitalTreatFinishCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getHospitalComponent().handleHospitalTreatFinishAsk();
    }

    @Override
    public void handleAddDevBuffFromPlayerAsk(AddDevBuffFromPlayerAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getAsk().getPlayerId());
        AddDevBuffFromPlayerAns.Builder ans = AddDevBuffFromPlayerAns.newBuilder();
        DevBuffProp prop = scenePlayer.getDevBuffComponent().addDevBuffByParam(ask.getAsk().getParam());
        if (prop != null) {
            CommonMsg.AddDevBuffAns.Builder builder = CommonMsg.AddDevBuffAns.newBuilder();
            builder.setBuff(prop.getCopySsBuilder());
            ans.setAns(builder.build());
        }
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleRemoveDevBuffFromPlayerAsk(RemoveDevBuffFromPlayerAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getAsk().getPlayerId());
        DevBuffProp prop = scenePlayer.getDevBuffComponent().removeDevBuffByParam(ask.getAsk().getParam());
        CommonMsg.RemoveDevBuffAns.Builder builder = CommonMsg.RemoveDevBuffAns.newBuilder();
        if (prop == null) {
            builder.setIsRemoved(true);
        } else {
            // 可能只是删层数，buff还在
            builder.setIsRemoved(prop.getLayer() <= 0);
            builder.setBuff(prop.getCopySsBuilder());
        }
        RemoveDevBuffFromPlayerAns.Builder ans = RemoveDevBuffFromPlayerAns.newBuilder()
                .setAns(builder.build());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleUpdateAdditionFromPlayerCmd(UpdateAdditionFromPlayerCmd ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getCmd().getPlayerId());
        scenePlayer.getAdditionComponent().updateAdditionFromPlayer(ask.getCmd().getSource(), ask.getCmd().getAdditionMap());
    }

    @Override
    public void handleGetSceneAdditionSysAsk(GetSceneAdditionSysAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        GetSceneAdditionSysAns.Builder ans = GetSceneAdditionSysAns.newBuilder();

        if (ask.getIsGm()) {
            ans.setAdditionSys(scenePlayer.getAdditionComponent().gmGetAllSceneAdditions().getCopySsBuilder());
        } else {
            scenePlayer.getAdditionComponent().copyAddition(ans.getAdditionSysBuilder().getAdditionBuilder());
        }
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleGetSceneDevBuffAsk(GetSceneDevBuffAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        GetSceneDevBuffAns.Builder ans = GetSceneDevBuffAns.newBuilder();
        ans.setBuffSys(scenePlayer.getDevBuffComponent().getBuffProp().getCopySsBuilder());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleMarkPositionAsk(MarkPositionAsk ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        ErrorCode errorCode = scenePlayer.getPositionMarkComponent().checkMarkAvaild(ask);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        scenePlayer.getPositionMarkComponent().markPosition(ask);
        sceneActor.answer(MarkPositionAns.getDefaultInstance());
    }

    @Override
    public void handleSetMarkReadedCmd(SetMarkReadedCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getPositionMarkComponent().setMarkReaded(ask);
    }

    /**
     * 进出联盟用 从player那边来的
     */
    @Override
    public void handleSyncPlayerClanIdNameCmd(SyncPlayerClanIdNameCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getPropComponent().onSyncClanId(ask.getClanId(), ask.getName());
    }

    @Override
    public void handleSyncPlayerNameCmd(SyncPlayerNameCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getPropComponent().onSyncPlayerName(ask.getName());
    }

    @Override
    public void handleSyncPlayerSoldierCmd(SyncPlayerSoldierCmd ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        ask.getAddSoldierMap().forEach((key, value) -> scenePlayer.getSoldierMgrComponent().addInCitySoldier(key, value, SoldierNumChangeReason.gm));
    }

    @Override
    public void handleSyncPlayerCityBuildLevelCmd(SyncPlayerCityBuildLevelCmd cmd) {
        ScenePlayerEntity scenePlayer = getScenePlayer(cmd.getPlayerId());
        //响应城建升级
        for (Integer templateId : cmd.getNewTemplateIdList()) {
            scenePlayer.getCityComponent().updateCityBuildInfo(templateId);
        }
    }

    @Override
    public void handleSyncPlaneCmd(SyncPlaneCmd ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (ask.getSpyPlaneListCount() > 0) {
            for (SyncSpyPlane syncSpyPlane : ask.getSpyPlaneListList()) {
                scenePlayer.getPlaneComponent().syncSpyPlane(syncSpyPlane);
            }
        }
    }

    @Override
    public void handleSyncPlayerHeroCmd(SyncPlayerHeroCmd ask) {
        List<Struct.Hero> heroChangeList = ask.getHeroListList();
        if (heroChangeList.size() <= 0) {
            LOGGER.error("SyncPlayerHeroCmd param error. ask:{}", ask);
            return;
        }
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getWallComponent().spreadBattleUnitChangeToWall(heroChangeList);
        scenePlayer.getArmyMgrComponent().spreadBattleUnitChangeToArmy(heroChangeList);
        scenePlayer.getCityComponent().spreadBattleUnitChangeToCity(heroChangeList);
    }

    @Override
    public void handleSyncTechDataCmd(SyncTechDataCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        if (ask.getUnlockResourceCount() > 0) {
            scenePlayer.getProp().getTechModel().getUnlockResource().clear();
            scenePlayer.getProp().getTechModel().getUnlockResource().addAll(ask.getUnlockResourceList());
        }
        if (ask.getUnlockSpyDataCount() > 0) {
            scenePlayer.getProp().getTechModel().getUnlockSpyLevel().clear();
            scenePlayer.getProp().getTechModel().getUnlockSpyLevel().addAll(ask.getUnlockSpyDataList());
        }
    }

    @Override
    public void handleSyncPlayerPicCmd(SyncPlayerPicCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getPropComponent().onSyncPlayerPic(ask.getPic(), ask.getPicUrl());
    }

    @Override
    public void handleSyncPlayerPicFrameCmd(SyncPlayerPicFrameCmd ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        scenePlayer.getPropComponent().onSyncPlayerPicFrame(ask.getPicFrame());
    }

    @Override
    public void handleSyncPlayerWallHeroPlaneCmd(SsScenePlayer.SyncPlayerWallHeroPlaneCmd cmd) {
        ScenePlayerEntity scenePlayer = getScenePlayer(cmd.getPlayerId());
        scenePlayer.getWallComponent().setGarrison(cmd.getMainHero(), cmd.getDeputyHero());
    }

    @Override
    public void handleMarkNewbieOverAsk(MarkNewbieOverAsk ask) {
        ScenePlayerEntity scenePlayer = getScenePlayer(ask.getPlayerId());
        ScenePlayerProp prop = scenePlayer.getProp();
        final int newbieKillMonsterMaxLevel = ResHolder.getConsts(ConstNewbieSvrTemplate.class).getNewbieKillMonsterMaxLevel();
        prop.setKillMonsterMaxLevel(Math.max(prop.getKillMonsterMaxLevel(), newbieKillMonsterMaxLevel));
        if (ask.getNewbieCheckFlag()) {
            scenePlayer.getSoldierMgrComponent().checkNewbieData();
        }
        sceneActor.answer(MarkNewbieOverAns.getDefaultInstance());
    }

    @Override
    public void handleBroadcastOnlinePlayerCsCmd(BroadcastOnlinePlayerCsCmd ask) {
        int msgType = ask.getMsgType();
        ByteString msgBytes = ask.getMsgBytes();
        getScene().getPlayerMgrComponent().broadcastOnlineClientMsgBytes(msgType, msgBytes);
    }

    @Override
    public void handleBroadcastOnlinePlayerCsWithMultiLanguageCmd(BroadcastOnlinePlayerCsWithMultiLanguageCmd ask) {
        getScene().getPlayerMgrComponent().broadcastOnlineClientWithLanguageCmd(ask);
    }

    @Override
    public void handleSyncPlayerEraCmd(SyncPlayerEraCmd cmd) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(cmd.getPlayerId());
        CityEntity mainCity = scenePlayer.getMainCity();
        int oldEraLevel = mainCity.getProp().getEraLevel();
        int newEraLevel = cmd.getNewEraLevel();
        if (newEraLevel <= oldEraLevel) {
            LOGGER.error("player city era change error. old:{} >= new:{}", oldEraLevel, newEraLevel);
            return;
        }
        LOGGER.info("{} player city era change. old:{} new:{}", cmd.getPlayerId(), oldEraLevel, newEraLevel);
        mainCity.getProp().setEraLevel(newEraLevel);
        MileStoneMgrComponent mileStoneOrNullComponent = getScene().getMileStoneOrNullComponent();
        if (mileStoneOrNullComponent != null) {
            mileStoneOrNullComponent.onPlayerEraChange(oldEraLevel, newEraLevel);
        }
    }

    @Override
    public void handleGetMileStoneHistoryAsk(GetMileStoneHistoryAsk ask) {
        GetMileStoneHistoryAns.Builder builder = GetMileStoneHistoryAns.newBuilder();
        if (getScene().isMainScene()) {
            for (Object o : getScene().getMileStoneOrNullComponent().getMileStoneBase()) {
                CommonMsg.MileStoneData mileStoneData = (CommonMsg.MileStoneData) o;
                builder.putMileStoneData(mileStoneData.getMileStoneId(), mileStoneData);
            }
            sceneActor.answer(builder.build());
            return;
        }
        throw new GeminiException(ErrorCode.MILESTONE_IS_NULL);
    }

    @Override
    public void handleGetMileStoneRankInfoAsk(GetMileStoneRankInfoAsk ask) {
        int mileStoneId = ask.getMileStoneId();
        GetMileStoneRankInfoAns ans = getScene().getMileStoneOrNullComponent().getMileStoneDesc(mileStoneId);
        sceneActor.answer(ans);
    }

    @Override
    public void handleFetchMainCityInfoAsk(FetchMainCityInfoAsk ask) {
        long playerId = ask.getPlayerId();
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        FetchMainCityInfoAns.Builder ans = FetchMainCityInfoAns.newBuilder();
        CityEntity mainCity = scenePlayer.getMainCity();
        if (mainCity != null) {
            ans.setPosition(MiscUtils.toPb(mainCity.getCurPoint()));
        }
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleSetExpressionCmd(SetExpressionCmd ask) {
        SceneObjEntity entity = getScene().getObjMgrComponent().getSceneObjEntity(ask.getEntityId());
        if (entity == null) {
            return;
        }
        if (entity.getPlayerId() != ask.getPlayerId()) {
            return;
        }
        entity.setExpression(ask.getExpressionId());
    }

    @Override
    public void handleSetPFlagCmd(SetPFlagCmd ask) {
        long playerId = ask.getPlayerId();
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        CityEntity mainCity = scenePlayer.getMainCity();
        if (mainCity != null) {
            mainCity.setPFlagId(ask.getPFlagId());
        }
    }

    @Override
    public void handleGetBattleLoseAsk(GetBattleLoseAsk ask) {
        long playerId = ask.getPlayerId();
        ScenePlayerEntity scenePlayer = getScenePlayer(playerId);
        GetBattleLoseAns.Builder builder = GetBattleLoseAns.newBuilder();
        builder.setBattleLose(scenePlayer.getLogisticMgrComponent().getBattleLose());
        sceneActor.answer(builder.build());
    }

    @Override
    public void handleUseDungeonSkillAsk(UseDungeonSkillAsk ask) {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleChangeCityDressAsk(ChangeCityDressAsk ask) {
        long playerId = ask.getPlayerId();
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        CityEntity mainCity = scenePlayer.getMainCity();
        if (mainCity != null) {
            mainCity.getExteriorComponent().setCityExterior(ask.getDressTemplateId(), ask.getDressTimeoutTsMs());
        }

        sceneActor.answer(ChangeCityDressAns.getDefaultInstance());
    }

    @Override
    public void handleCheckCanAddDevBuffAsk(CheckCanAddDevBuffAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        boolean can = scenePlayer.getDevBuffComponent().canAddDevBuff(ask.getBuffId());
        sceneActor.answer(CheckCanAddDevBuffAns.newBuilder().setCan(can).build());
    }

    @Override
    public void handleIdIpReturnAllArmyAsk(IdIpReturnAllArmyAsk ask) {
        LOGGER.info("idip return all army. ask={}", ask);
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        for (ArmyEntity army : new ArrayList<>(scenePlayer.getArmyMgrComponent().getMyArmyList())) {
            army.getMoveComponent().onReturnCityEnd();
        }
        sceneActor.answer(IdIpReturnAllArmyAns.getDefaultInstance());
    }

    @Override
    public void handleIdIpGlobalPeaceShieldAsk(IdIpGlobalPeaceShieldAsk ask) {
        LOGGER.info("idip peaceShield. ask={}", ask);
        if (ask.getIsZoneFlag()) {
            getScene().getPeaceShieldComponent().openGlobalPeaceShield(ask.getTimeSec());
        } else {
            for (Long playerId : ask.getPlayerIdsList()) {
                AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(playerId);
                getScene().getPeaceShieldComponent().openSinglePeaceShield(scenePlayer, ask.getTimeSec());
            }
        }
        sceneActor.answer(IdIpGlobalPeaceShieldAns.getDefaultInstance());
    }

    @Override
    public void handleSyncPlayerPushNtfInfoAsk(SyncPlayerPushNtfInfoAsk ask) {
        LOGGER.info("handleSyncPlayerPushNtfInfoAsk ask={}", ask);
        ScenePlayerEntity entity = getScenePlayer(ask.getPlayerId());
        if (ask.hasIntlNtfToken()) {
            entity.getNtfComponent().refreshToken(ask.getIntlNtfToken());
        }
        if (ask.hasLanguage()) {
            entity.getNtfComponent().refreshLanguage(ask.getLanguage());
        }
        final PushNotificationResService resService = ResHolder.getResService(PushNotificationResService.class);
        if (ask.hasChangeNtfStatusInfoCmd()) {
            List<Integer> openNtfModelIdList = resService.transferSettingIdList2PushNtfModelIdList(ask.getChangeNtfStatusInfoCmd().getOpenSettingIdListList());
            List<Integer> closeNtfModelIdList = resService.transferSettingIdList2PushNtfModelIdList(ask.getChangeNtfStatusInfoCmd().getCloseSettingIdListList());
            entity.getNtfComponent().refreshPushNtfMaskWithModelIdList(openNtfModelIdList, closeNtfModelIdList, "SyncPlayerPushNtfInfoAsk");
        }
        if (sceneActor.getCurrentEnvelope().getWay() == CommonEnum.CommunicationWay.TELL) {
            return;
        }
        final long notificationMask = NotificationTokenHelper.getNotificationMask(ask.getPlayerId());
        if (!NotificationTokenHelper.isPushNtfMaskRight(notificationMask)) {
            throw new GeminiException(ErrorCode.NOTIFICATION_SETTING_NOT_EXIST);
        }
        SyncPlayerPushNtfInfoAns.Builder ans = SyncPlayerPushNtfInfoAns.newBuilder();
        ans.addAllOpenSettingIdList(resService.getOpenPushSettingList(notificationMask))
                .addAllCloseSettingIdList(resService.getClosePushSettingList(notificationMask));
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleQueryPlayerPushNtfAsk(QueryPlayerPushNtfAsk ask) {
        final long notificationMask = NotificationTokenHelper.getNotificationMask(ask.getPlayerId());
        if (!NotificationTokenHelper.isPushNtfMaskRight(notificationMask)) {
            throw new GeminiException(ErrorCode.NOTIFICATION_SETTING_NOT_EXIST);
        }
        final PushNotificationResService resService = ResHolder.getResService(PushNotificationResService.class);
        QueryPlayerPushNtfAns.Builder ans = QueryPlayerPushNtfAns.newBuilder();
        ans.addAllOpenSettingIdList(resService.getOpenPushSettingList(notificationMask))
                .addAllCloseSettingIdList(resService.getClosePushSettingList(notificationMask));
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleIdIpModifySoldierAsk(IdIpModifySoldierAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        int beforeCount = scenePlayer.getSoldierMgrComponent().getInCitySoldierNum(ask.getSoldierId());
        if (ask.getValue() > 0) {
            scenePlayer.getSoldierMgrComponent().addInCitySoldier(ask.getSoldierId(), ask.getValue(), SoldierNumChangeReason.idip);
        } else {
            int realSubCount = Math.min(beforeCount, -ask.getValue());
            if (realSubCount > 0) {
                scenePlayer.getSoldierMgrComponent().subInCitySoldier(ask.getSoldierId(), realSubCount, SoldierNumChangeReason.idip);
            }
        }
        int afterCount = scenePlayer.getSoldierMgrComponent().getInCitySoldierNum(ask.getSoldierId());
        LOGGER.info("SceneIdipServiceImpl handleSoldierAsk {} ask={} before={} after={}", scenePlayer, ask, beforeCount, afterCount);
        sceneActor.answer(IdIpModifySoldierAns.newBuilder().setBeforeValue(beforeCount).setAfterValue(afterCount).build());
    }

    @Override
    public void handleSkynetFindMonsterAsk(SkynetFindMonsterAsk ask) {
        if (!sceneActor.getScene().isMainScene()) {
            throw new GeminiException("not in main scene, CurSceneId={}, BigSceneId={}", getSceneId());
        }
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) getScene().getObjMgrComponent();
        long player = objMgrComponent.getSkynetMonster2Player(ask.getMonsterId());
        if (scenePlayer.getPlayerId() != player) {
            throw new GeminiException(ErrorCode.MONSTER_NOT_EXIT);
        }
        SceneObjEntity sceneObjEntity = objMgrComponent.getSceneObjEntity(ask.getMonsterId());
        if (sceneObjEntity == null) {
            LOGGER.error("ScenePlayerServiceImpl handleSkynetFindMonsterAsk, monster not exist, monsterId={}", ask.getMonsterId());
            sceneActor.answer(SkynetFindMonsterAns.getDefaultInstance());
            return;
        }
        if (!(sceneObjEntity instanceof MonsterEntity)) {
            LOGGER.error("ScenePlayerServiceImpl handleSkynetFindMonsterAsk, sceneObjEntity type err, sceneObjEntity={}", sceneObjEntity);
            sceneActor.answer(SkynetFindMonsterAns.getDefaultInstance());
            return;
        }
        MonsterEntity monster = (MonsterEntity) sceneObjEntity;

        SkynetFindMonsterAns ans = SkynetFindMonsterAns.newBuilder()
                .setBornPoint(Struct.Point.newBuilder().setX(sceneObjEntity.getCurPoint().getX()).setY(sceneObjEntity.getCurPoint().getY()).build())
                .setLifeEndTsMs(monster.getProp().getLifeTime())
                .build();
        sceneActor.answer(ans);
    }

    @Override
    public void handleGetZoneSeasonAsk(GetZoneSeasonAsk ask) {
        GetZoneSeasonAns.Builder ans = GetZoneSeasonAns.newBuilder();
        ZoneEntity zoneEntity = sceneActor.getBigScene().getZoneEntity();
        ans.setZoneSeason(zoneEntity.getProp().getSeasonModel().getSeason());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleBroadOnlinePlayerSsCmd(BroadOnlinePlayerSsCmd ask) {
        final SceneEntity sceneEntity = this.getScene();
        if (sceneEntity.isMainScene()) {
            final Map<Long, AbstractScenePlayerEntity> onlinePlayers = sceneEntity.getPlayerMgrComponent().getOnlinePlayer();
            final List<IActorRef> onlinePlayerRefs = new ArrayList<>(onlinePlayers.size());
            final GeneratedMessageV3 ssMsg = MsgUtils.parseProtoPayload(ask.getMsgType(), ask.getMsgBytes());
            for (AbstractScenePlayerEntity player : onlinePlayers.values()) {
                onlinePlayerRefs.add(RefFactory.ofPlayer(player.getZoneId(), player.getPlayerId()));
            }
            BroadcastHelper.toAllTargets(onlinePlayerRefs, this.sceneActor.self(), ssMsg);
        } else {
            LOGGER.error("handleBroadOnlinePlayerSsCmd not bigScene or kvkScene");
        }
    }
}

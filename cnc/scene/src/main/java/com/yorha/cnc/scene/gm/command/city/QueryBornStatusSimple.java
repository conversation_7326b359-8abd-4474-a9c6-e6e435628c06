package com.yorha.cnc.scene.gm.command.city;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryBornStatusSimple implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isBigScene()) {
            return;
        }
        BigSceneEntity scene = (BigSceneEntity) actor.getScene();
        StringBuilder string = new StringBuilder();
        string.append("导量总人数: ");
        string.append(scene.getZoneEntity().getBornMgrComponent().getPlayerNum());
        string.append("\n");

        string.append("各州导量人数: ");
        for (int i = 0; i < 10; i++) {
            Map<CommonEnum.MapAreaType, List<Integer>> regionPartList = actor.getScene().getMapTemplateDataItem().getRegionBornPartList(i);
            if (regionPartList == null) {
                throw new GeminiException(ErrorCode.SYSTEM_WARNING, String.valueOf(0));
            }
            int sum = 0;
            for (List<Integer> item : regionPartList.values()) {
                for (Integer partId : item) {
                    sum += scene.getBornMgrComponent().getPartToNum(partId);
                }
            }
            string.append(i);
            string.append(": ");
            string.append(sum);
            string.append(", ");
        }
        string.append("\n");
        int total = 0;
        for (Map.Entry<Integer, Integer> entry : scene.getBornMgrComponent().getAllPartToNum().entrySet()) {
            total += entry.getValue();
        }
        string.append("各区域导量人数相加计算出的总人数: ");
        string.append(total);
        string.append("\n");

        int ascendNum = 0;
        for (CityEntity city : scene.getObjMgrComponent().getObjsByType(CityEntity.class)) {
            if (city.getTransformComponent().isAscend()) {
                ascendNum++;
            }
        }
        string.append("已升天数: ");
        string.append(ascendNum);
        string.append("\n");

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(string.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("QueryBornStatusSimple")
                .setSubTitle("QueryBornStatusSimple");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(actor.getScenePlayer(playerId).getZoneId())
                        .build(),
                builder.build());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

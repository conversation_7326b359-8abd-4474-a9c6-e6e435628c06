package com.yorha.cnc.scene.battle;

import com.yorha.cnc.battle.adapter.interfaces.IBattleMoveAdapter;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;
import com.yorha.common.utils.vector.Vector2f;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
public class SceneBattleMoveAdapter implements IBattleMoveAdapter {
    private final SceneObjMoveComponent component;

    public SceneBattleMoveAdapter(SceneObjMoveComponent component) {
        this.component = component;
    }

    @Override
    public Vector2f getYaw() {
        return component.getYaw();
    }

    @Override
    public boolean canMove() {
        return component.canMove();
    }

    @Override
    public void setIsFrozen(boolean flag) {
        component.setIsFrozen(flag);
    }

    @Override
    public void stopMove() {
        component.stopMove();
    }

    @Override
    public void setYaw(int x, int y) {
        component.setYaw(x, y);
    }

    @Override
    public boolean isMoving() {
        return component.isMoving();
    }
}

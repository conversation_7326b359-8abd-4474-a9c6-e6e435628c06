package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.CrossDataProp;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BigSceneMonsterMilestoneTemplate;

import java.util.concurrent.TimeUnit;

import static com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Zone;

/**
 * <AUTHOR>
 */
public class ZoneInfoPropComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneInfoPropComponent.class);

    /**
     * 日刷定时器
     */
    private boolean refreshSchedule = false;

    public ZoneInfoPropComponent(ZoneEntity owner) {
        super(owner);
    }

    public ZoneInfoProp getProp() {
        return getOwner().getProp();
    }

    public ZoneSideProp getZoneSideProp() {
        return getOwner().getZoneSideProp();
    }

    @Override
    public void init() {
        super.init();
        initPropListener();
    }

    private void initPropListener() {
        getProp().setListener(new PropertyChangeListener(() -> {
            if (!getProp().hasAnyMark()) {
                return;
            }
            ntfChangeToClient();
            getOwner().getDbComponent().saveChangeToDb();
            getProp().unMarkAll();
        }, getOwner().ownerActor().self()));
        if (getProp().hasAnyMark()) {
            getProp().getListener().trigger(getProp().getClass().getSimpleName());
        }
        getZoneSideProp().setListener(new PropertyChangeListener(() -> {
            if (!getProp().hasAnyMark()) {
                return;
            }
            getOwner().getSideDbComponent().saveChangeToDb();
            getZoneSideProp().unMarkAll();
        }, getOwner().ownerActor().self()));
        if (getZoneSideProp().hasAnyMark()) {
            getZoneSideProp().getListener().trigger(getZoneSideProp().getClass().getSimpleName());
        }
    }

    @Override
    public void postInit() {
        addRefreshDayTimer();
    }

    public Entity.EntityNtfMsg buildEntityMsg() {
        Entity.EntityNtfMsg.Builder ret = Entity.EntityNtfMsg.newBuilder();
        EntityAttrOuterClass.EntityAttr.Builder builder = EntityAttrOuterClass.EntityAttr.newBuilder();
        builder.setEntityType(ET_Zone).setEntityId(getEntityId());
        getProp().copyToCs(builder.getZoneInfoBuilder());
        ret.addNewEntities(builder.build()).setReason(CommonEnum.SceneObjectNtfReason.SONR_NONE)
                .setZoneId(getOwner().getZoneId());
        return ret.build();
    }

    public void onPlayerLogin(ScenePlayerEntity playerEntity) {
        playerEntity.sendMsgToClient(MsgType.ENTITYNTFMSG, buildEntityMsg());
    }

    /**
     * 关卡归属发生变化
     */
    public void onPassOwnerChange(int partId, long clanId) {
        CrossDataProp dataV = getProp().getCrossData().getDataV(partId);
        if (dataV == null) {
            dataV = getProp().getCrossData().addEmptyData(partId);
        }
        dataV.setOwnerClanId(clanId);
    }

    /**
     * 搜索野怪等级上限
     */
    public BigSceneMonsterMilestoneTemplate getMonsterMilestoneTemplate() {
        int nowMilestoneId = getProp().getCurMonsterMilestone();
        if (nowMilestoneId <= 0) {
            return null;
        }
        return ResHolder.getInstance().getValueFromMap(BigSceneMonsterMilestoneTemplate.class, nowMilestoneId);
    }

    /**
     * 属性增量更新通知客户端
     */
    private void ntfChangeToClient() {
        final EntityAttrOuterClass.EntityAttr.Builder entityAttrBuilder = EntityAttrOuterClass.EntityAttr.newBuilder();
        if (getProp().copyChangeToCs(entityAttrBuilder.getZoneInfoBuilder()) <= 0) {
            return;
        }
        entityAttrBuilder.setEntityType(ET_Zone).setEntityId(getEntityId());
        final Entity.EntityNtfMsg.Builder builder = Entity.EntityNtfMsg.newBuilder();
        builder.setZoneId(getOwner().getZoneId()).addModEntities(entityAttrBuilder);
        Entity.EntityNtfMsg msg = builder.build();
        // 广播本场景在线
        getOwner().getBigScene().getPlayerMgrComponent().broadcastOnlineClientMsg(MsgType.ENTITYNTFMSG, msg);
    }

    /**
     * 日刷新检测
     */
    private void addRefreshDayTimer() {
        LOGGER.info("addRefreshDayTimer");
        if (refreshSchedule) {
            LOGGER.error("addRefreshDayTimer fail, refreshSchedule repeat, refreshSchedule={}", refreshSchedule);
            return;
        }
        final long now = SystemClock.now();
        // 禁止回调
        if (getProp().getRefreshModel().getRefreshDay() > now) {
            WechatLog.error("zone refresh error. now:{} lastRefreshDayTsMs:{}", now, getProp().getRefreshModel().getRefreshDay());
        }
        final long delayMs = TimeUtils.getNextDayDurMs(now);
        getOwner().getTimerComponent().addTimer(TimerReasonType.ZONE_DAY_REFRESH, this::refreshEventDispatch, delayMs, TimeUnit.MILLISECONDS);
        refreshSchedule = true;
    }

    /**
     * 日刷派发
     */
    private void refreshEventDispatch() {
        refreshSchedule = false;
        final long now = SystemClock.now();
        getProp().getRefreshModel().setRefreshDay(now);
        // 添加定时器
        addRefreshDayTimer();
        // 业务
        refreshDayExec(now);
    }

    /**
     * 日刷的业务逻辑
     */
    private void refreshDayExec(long now) {
        try {
            LOGGER.info("refreshDayExec");
            // 未来会迁移到zone包，这里直接以消息的形式发
            // FIXME: 服务不可用 ServerStatisticLog.getInstance().record2StatisticServer(ServerStatisticLog.ServerStatisticType.DAY_TICK);
        } catch (Exception e) {
            LOGGER.error("ZoneInfoPropComponent refreshEventDispatch", e);
        }
    }

    @Override
    public SceneActor ownerActor() {
        return (SceneActor) super.ownerActor();
    }
}

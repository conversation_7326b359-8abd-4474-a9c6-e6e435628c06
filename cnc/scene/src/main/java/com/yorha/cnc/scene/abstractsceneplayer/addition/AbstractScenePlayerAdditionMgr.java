package com.yorha.cnc.scene.abstractsceneplayer.addition;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerArmyMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerRallyComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.component.ScenePlayerWallComponent;
import com.yorha.common.addition.AdditionMgrBase;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerAdditionMgr<T extends AbstractScenePlayerEntity> extends AdditionMgrBase<T> {

    protected AbstractScenePlayerAdditionMgr(T owner) {
        super(owner);
    }

    @Override
    public abstract AdditionSysProp getAdditionSys();

    private static void dispatch(AbstractScenePlayerEntity entity, BuffEffectType buffEffectType, long value) {
        switch (buffEffectType) {
            case ET_ARMY_MOVE_SPEED_ADD_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE1_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE2_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE3_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE4_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE1_FIXED:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE2_FIXED:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE3_FIXED:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE4_FIXED:
            case ET_MOVE_IN_CLAN_TERRITORY_SPEED_PERCENT:
                AbstractScenePlayerArmyMgrComponent.onArmyMoveSpeedAdditionChange(entity);
                break;
            case ET_WALL_HP_MAX_FIXED:
                if (entity instanceof ScenePlayerEntity) {
                    ScenePlayerEntity player = (ScenePlayerEntity) entity;
                    ScenePlayerWallComponent.onSyncWallHpMax(player, value);
                }
                break;
            case ET_BASE_REINFORCEMENT_CAP_FIXED:
                AbstractScenePlayerRallyComponent.setBeAidedMaxCap(entity, value);
                break;

            case ET_RALLY_CAP_FIXED:
            case ET_RALLY_CAP_PERCENT:
                AbstractScenePlayerRallyComponent.onRallyMaxCapChange(entity);
                break;
            default:
                break;
        }
    }

    public static void dispatchAdditionChange(Map<Integer, Long> newAdditions, AbstractScenePlayerEntity entity) {
        for (Map.Entry<Integer, Long> entry : newAdditions.entrySet()) {
            try {
                BuffEffectType buffEffectType = BuffEffectType.forNumber(entry.getKey());
                if (buffEffectType == null) {
                    LOGGER.error("owner:{}, dispatchAdditionChange failed. BuffEffectType not found. additionId:{}", entity, entry.getKey());
                    continue;
                }
                dispatch(entity, buffEffectType, entry.getValue());
            } catch (Exception e) {
                LOGGER.error("owner:{} dispatchAdditionChange failed. additionId: {}", entity, entry.getKey(), e);
            }
        }
    }

    @Override
    protected void update(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            LOGGER.error("{} update additions is empty. sourceType:{}", getOwner(), sourceType);
            return;
        }

        Map<Integer, Long> sceneAdditionMap = Maps.newHashMap();
        Map<Integer, Long> playerAdditionMap = Maps.newHashMap();
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            if (AdditionUtil.isSceneAddition(entry.getKey())) {
                sceneAdditionMap.put(entry.getKey(), entry.getValue());
            } else {
                playerAdditionMap.put(entry.getKey(), entry.getValue());
            }
        }
        updateAdditionToScene(sourceType, sceneAdditionMap);
        updateAdditionToPlayer(sourceType, playerAdditionMap);
    }

    protected void updateAdditionToScene(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            return;
        }
        Map<Integer, Long> oldAdditions = new HashMap<>();
        for (Integer additionId : additions.keySet()) {
            oldAdditions.put(additionId, getAddition(additionId));
        }

        // 更新加成
        updateAddition(sourceType, additions);

        Map<Integer, Long> newAdditions = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : oldAdditions.entrySet()) {
            long newAdditionValue = getAddition(entry.getKey());
            if (entry.getValue() != newAdditionValue) {
                newAdditions.put(entry.getKey(), newAdditionValue);
                LOGGER.info("{} update addition. additionId:{}, sourceType:{}, value:{} -> {}", getOwner(), entry.getKey(), sourceType, entry.getValue(), getAddition(entry.getKey()));
            }
        }

        // 通知加成变更事件
        dispatchAdditionChange(newAdditions, getOwner());
    }

    protected void updateAdditionToPlayer(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {

    }
}

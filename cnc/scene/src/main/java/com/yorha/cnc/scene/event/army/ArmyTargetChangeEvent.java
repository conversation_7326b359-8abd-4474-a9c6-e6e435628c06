package com.yorha.cnc.scene.event.army;

import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;

/**
 * <AUTHOR> dieying
 */
public class ArmyTargetChangeEvent extends IEventWithEntityId {
    final long newTargetId;

    public ArmyTargetChangeEvent(long entityId, long newTargetId) {
        super(entityId);
        this.newTargetId = newTargetId;
    }

    public long getNewTargetId() {
        return newTargetId;
    }
}

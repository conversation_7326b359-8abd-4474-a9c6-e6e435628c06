package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.mainScene.common.refresh.GuardRefreshTask;
import com.yorha.cnc.mainScene.common.refresh.RefreshTask;
import com.yorha.cnc.mainScene.common.refresh.RegionMonsterRefresh;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.qlog.json.monster.Monster;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.BigSceneResService;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BigSceneSpawnBuildingTemplate;
import res.template.ConstTemplate;
import res.template.MapBuildingTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class MainSceneObjMgrComponent extends ObjMgrComponent {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneObjMgrComponent.class);

    private final Map<Integer, Integer> monsterCount = new HashMap<>();
    //城市守护者 buildingId -> (monsterId->num)
    private final Map<Integer, Map<Integer, Integer>> guardianMap = new HashMap<>();
    private final Map<String, String> guardianRefreshTimerMap = new HashMap<>();
    private final Map<Integer, Integer> runeDropMap = new HashMap<>();
    // 天网召唤怪 monster -> player
    private final Map<Long, Long> skynetMap = new HashMap<>();
    /**
     * 活动野怪 州id -> 数量
     */
    private final Map<Integer, Integer> actMonsterNum = new HashMap<>();
    /**
     * 野怪回收队列
     */
    private final Queue<MonsterEntity> recycleMonsterQueue = new LinkedList<>();
    /**
     * 刷新任务队列
     */
    private final Queue<RefreshTask> taskQueue = new LinkedList<>();

    public MainSceneObjMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void onZoneOpen() {
        // 初始化大地图城市守护者野怪刷新
        initGuardianMap();
        initRefreshGuardianTimer();
    }

    public void addMonsterToRecycleQueue(MonsterEntity m) {
        recycleMonsterQueue.add(m);
    }

    /**
     * 回收野怪
     *
     * @param num 本次回收个数
     * @return 是否全部回收完成
     */
    public int recycleMonster(int num) {
        int times = Math.min(recycleMonsterQueue.size(), num);
        int cnt = 0;
        for (int i = 0; i < times; i++) {
            MonsterEntity m = recycleMonsterQueue.poll();
            if (m == null) {
                break;
            }
            m.onRecycle();
            cnt++;
        }
        return cnt;
    }

    public void addDropRune(int partId) {
        int num = runeDropMap.getOrDefault(partId, 0);
        num++;
        runeDropMap.put(partId, num);
    }

    public void deleteDropRune(int partId) {
        int num = runeDropMap.getOrDefault(partId, 0);
        num--;
        if (num >= 0) {
            runeDropMap.put(partId, num);
        }
    }

    public boolean canDropRune(int partId) {
        int num = runeDropMap.getOrDefault(partId, 0);
        return num < ResHolder.getConsts(ConstTemplate.class).getCityGuardRuneLimited();
    }

    private final List<RegionMonsterRefresh> regionMonsterRefreshList = new LinkedList<>();

    public void onTick() {
        // 主堡升天
        long s1 = SystemClock.now();
        int i1 = getOwner().getPlayerMgrComponent().playerCityAscend(100);
        if (i1 > 0) {
            LOGGER.info("playerCityAscend cost={}ms num={}", SystemClock.now() - s1, i1);
        }

        // 回收野怪
        long s2 = SystemClock.now();
        int i2 = recycleMonster(10_000);
        if (i2 > 0) {
            LOGGER.info("recycleMonster cost={}ms num={}", SystemClock.now() - s2, i2);
        }

        // 创建野怪，一次一个州
        long s3 = SystemClock.now();
        Iterator<RegionMonsterRefresh> it = regionMonsterRefreshList.iterator();
        if (it.hasNext()) {
            RegionMonsterRefresh refreshTask = it.next();
            it.remove();
            int i3 = refreshTask.run();
            LOGGER.info("RegionMonsterRefresh cost={}ms num={} {}", SystemClock.now() - s3, i3, refreshTask);
        }

        if (taskQueue.isEmpty()) {
            return;
        }
        // 总完成值，如果一次tick完成了50以上就足够了，这边只是过滤一些完成度0的任务
        int totalFinish = 0;
        // 总执行次数，为了防止无脑一直tick死循环，设置的上限
        int runTimes = 0;
        while (runTimes < 10) {
            runTimes++;
            long startTick = SystemClock.now();
            RefreshTask task = taskQueue.peek();
            if (task == null) {
                // queue里什么都没有了
                LOGGER.info("MainSceneObjMgrComponent try peek but nothing");
                return;
            }
            Pair<Boolean, Integer> result = null;
            try {
                result = task.run();
                if (result == null) {
                    LOGGER.error("MainSceneObjMgrComponent but result is null {} ", task);
                    taskQueue.remove();
                    continue;
                }
                if (result.getFirst()) {
                    LOGGER.info("MainSceneObjMgrComponent onTick finish task {} {}", task, result);
                    taskQueue.remove();
                }
            } catch (Exception e) {
                taskQueue.remove();
                WechatLog.error("MainSceneObjMgrComponent onTick fail, {} ", task, e);
            }
            int intValue = 0;
            if (result != null) {
                intValue = result.getSecond();
            }

            LOGGER.info("MainSceneObjMgrComponent BigSceneRefreshCost: type:{} num {}/{} cost:{} Ms runtimes={}", ClassNameCacheUtils.getSimpleName(task.getClass()), intValue, task.getHandleNumPerTick(), SystemClock.now() - startTick, runTimes);

            totalFinish += intValue;
            if (totalFinish >= 50) {
                LOGGER.info("MainSceneObjMgrComponent totalFinish={} break loop {}", totalFinish, runTimes);
                break;
            }
        }
    }

    public void addRefreshTask(RefreshTask task) {
        taskQueue.add(task);
    }

    /**
     * 初始化刷守护者定时器
     */
    public void initRefreshGuardianTimer() {
        // 重置上次初始化残留定时器
        for (String prefix : guardianRefreshTimerMap.values()) {
            getOwner().getTimerComponent().cancelTimer(TimerReasonType.INIT_REFRESH_GUARDIAN, prefix);
        }
        long now = SystemClock.now();
        int hour = TimeUtils.getHourOfDay(now);
        int min = TimeUtils.getMinuteOfDay(now);
        int storyId = ownerActor().getStoryId();
        for (BigSceneSpawnBuildingTemplate template : ResHolder.getInstance().getMap(BigSceneSpawnBuildingTemplate.class).values()) {
            if (template.getStoryId() != storyId) {
                continue;
            }
            for (IntPairType pair : template.getRefreshTimePairList()) {
                int diffHour = pair.getKey() - hour;
                int diffMin = pair.getValue() - min;
                if (!TimeUtils.isAfterNow(pair.getKey(), pair.getValue())) {
                    diffHour = MathUtils.addExact(diffHour, 24);
                }
                long diff = MathUtils.addExact(TimeUnit.HOURS.toMillis(diffHour), TimeUnit.MINUTES.toMillis(diffMin));
                if (diff <= 0) {
                    throw new GeminiException("initRefreshGuardianTimer fail now hour:{}, min:{} config hour:{}, min:{}", hour, min, pair.getKey(), pair.getValue());
                }
                String key = IntTripleType.makeTriple(template.getId(), pair.getKey(), pair.getValue()).toString();
                guardianRefreshTimerMap.computeIfPresent(key, (k, v) -> {
                    getOwner().getTimerComponent().cancelTimer(TimerReasonType.INIT_REFRESH_GUARDIAN, v);
                    return v;
                });
                final String prefix = getEntityId() + "-" + template.getId() + "-" + pair;
                getOwner().getTimerComponent().addFixRepeatTimerWithPrefix(
                        prefix,
                        TimerReasonType.INIT_REFRESH_GUARDIAN,
                        () -> addRefreshTask(new GuardRefreshTask(getOwner(), template.getType(), template.getId())),
                        diff,
                        TimeUnit.DAYS.toMillis(1),
                        TimeUnit.MILLISECONDS
                );
                guardianRefreshTimerMap.put(key, prefix);
            }
        }
    }

    public Map<Integer, Map<Integer, Integer>> getGuardianMap() {
        return guardianMap;
    }

    /**
     * 更新每个建筑的守护者数量，目前为每次更新减少一个野怪计数
     */
    public void updateGuardianNum(int partId, int monsterTemplateId) {
        Map<Integer, Integer> liveGuardianMap = guardianMap.getOrDefault(partId, null);
        if (liveGuardianMap == null) {
            return;
        }
        int num = liveGuardianMap.getOrDefault(monsterTemplateId, 0);
        if (num > 0) {
            num--;
        }
        liveGuardianMap.put(monsterTemplateId, num);
    }

    /**
     * 更新活动怪数量计数
     */
    public void updateActNum(int regionId) {
        int num = actMonsterNum.getOrDefault(regionId, 0);
        num--;
        if (num >= 0) {
            actMonsterNum.put(regionId, num);
        }
    }

    public void initGuardianMap() {
        int storyId = ownerActor().getStoryId();
        Map<CommonEnum.MapBuildingType, List<Integer>> templateMap = ResHolder.getResService(BigSceneResService.class).getMonsterSpawnBuildingMap(storyId);
        if (templateMap == null) {
            return;
        }
        for (RegionalAreaSettingTemplate regionalAreaSettingTemplate : getOwner().getMapTemplateDataItem().getMap(RegionalAreaSettingTemplate.class).values()) {
            if (regionalAreaSettingTemplate.getBuildingId() <= 0) {
                continue;
            }
            CommonEnum.MapBuildingType type = ResHolder.getTemplate(MapBuildingTemplate.class, regionalAreaSettingTemplate.getBuildingId()).getType();
            if (!templateMap.containsKey(type)) {
                continue;
            }
            Map<Integer, Integer> monsterMap = new HashMap<>();
            for (int configId : templateMap.get(type)) {
                BigSceneSpawnBuildingTemplate template = ResHolder.getTemplate(BigSceneSpawnBuildingTemplate.class, configId);
                for (IntPairType pair : template.getRefreshConfigPairList()) {
                    monsterMap.put(pair.getKey(), 0);
                }
            }
            guardianMap.put(regionalAreaSettingTemplate.getId(), monsterMap);
        }
    }

    public MonsterEntity refreshActMonster(Shape shape, int templateId, int lifeTime) {
        Point point = MonsterFactory.randomBornPoint(getOwner(), templateId, shape, true);
        if (point == null) {
            return null;
        }
        int regionId = MapGridDataManager.getRegionId(getOwner().getMapId(), point);
        int num = actMonsterNum.getOrDefault(regionId, 0);
        num++;
        if (num > GameLogicConstants.ACT_MONSTER_REGION_LIMIT) {
            LOGGER.warn("refreshActMonster regionId:{}, point:{} template:{} overlimit", regionId, point, templateId);
            return null;
        }
        SceneObjSpawnParam param = new SceneObjSpawnParam();
        if (lifeTime > 0) {
            param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(lifeTime));
        }
        MonsterEntity monster = MonsterFactory.initMonster(getOwner(), templateId, point, param);
        if (monster == null) {
            return null;
        }
        monster.addIntoScene();
        actMonsterNum.put(regionId, num);
        return monster;
    }

    /**
     * 给qlog打印用
     */
    public String getMonsterCountStr(Map<Integer, Integer> count) {
        if (count == null) {
            count = monsterCount;
        }
        List<Monster> monsterList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : count.entrySet()) {
            Monster monster = new Monster(entry.getKey(), entry.getValue());
            monsterList.add(monster);
        }
        return JsonUtils.toJsonString(monsterList);
    }

    @Override
    public void addSceneObjEntity(SceneObjEntity entity) {
        super.addSceneObjEntity(entity);
        if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
            MonsterEntity monster = (MonsterEntity) entity;
            int oldNum = monsterCount.getOrDefault(monster.getTemplate().getId(), 0);
            monsterCount.put(monster.getTemplate().getId(), oldNum + 1);
        }
    }

    @Override
    public void removeSceneObjEntity(SceneObjEntity entity) {
        super.removeSceneObjEntity(entity);
        if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
            MonsterEntity monster = (MonsterEntity) entity;
            int oldNum = monsterCount.getOrDefault(monster.getTemplate().getId(), 0);
            if (oldNum <= 0) {
                return;
            }
            monsterCount.put(monster.getTemplate().getId(), oldNum - 1);
        }
    }

    public Collection<SceneObjEntity> getSceneObjEntitys() {
        return Collections.unmodifiableCollection(entityMap.values());
    }

    public void addSkynetMonster(long playerId, long monsterId) {
        if (skynetMap.containsKey(monsterId)) {
            LOGGER.error("MainSceneObjMgrComponent addSkynetMonster, monsterId={} playerId={}", monsterId, playerId);
            return;
        }
        skynetMap.put(monsterId, playerId);
    }

    public void removeSkynetMonsterByMonster(long monsterId) {
        LOGGER.info("MainSceneObjMgrComponent removeSkynetByPlayerId, monsterId={}", monsterId);
        if (skynetMap.remove(monsterId) == null) {
            LOGGER.error("MainSceneObjMgrComponent removeSkynetMonsterByPlayer, not contain monsterId, monsterId={}", monsterId);
        }
    }

    public void removeSkynetMonsterAboutMe(long playerId) {
        for (long monsterId : new HashSet<>(skynetMap.keySet())) {
            if (skynetMap.getOrDefault(monsterId, 0L) != playerId) {
                continue;
            }
            MonsterEntity monster = getSceneObjWithType(MonsterEntity.class, monsterId);
            if (monster != null) {
                monster.forceRecycle();
            }
        }
    }

    public int getSkynetMonsterNum() {
        return skynetMap.size();
    }

    public Collection<Long> validSkynetMonsterExist(List<Long> skynetMonsterListList) {
        List<Long> validMonsters = new ArrayList<>();
        for (long monsterId : skynetMonsterListList) {
            if (skynetMap.containsKey(monsterId)) {
                validMonsters.add(monsterId);
            }
        }
        return validMonsters;
    }

    public long getSkynetMonster2Player(long monsterId) {
        return skynetMap.getOrDefault(monsterId, 0L);
    }
}

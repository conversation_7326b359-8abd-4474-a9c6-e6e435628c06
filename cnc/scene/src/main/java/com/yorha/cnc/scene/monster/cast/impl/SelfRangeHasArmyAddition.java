package com.yorha.cnc.scene.monster.cast.impl;

import com.yorha.common.constant.Constants;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.cast.AbstractMonsterCastAddition;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.commons.collections4.CollectionUtils;
import res.template.MonsterCastTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 半径范围内有敌方行军部队
 */
public class SelfRangeHasArmyAddition extends AbstractMonsterCastAddition {

    private int range;
    private int rate;

    public SelfRangeHasArmyAddition(MonsterCastTemplate template) {
        super(template);
    }

    @Override
    protected void parse(String param) {
        if (StringUtils.isEmpty(param)) {
            throw new IllegalArgumentException("SelfRangeHasArmyAddition param is null");
        }

        String[] split = StringUtils.split(param, Constants.XIA_HUA_XIAN);
        if (split.length != 2) {
            throw new IllegalArgumentException("SelfRangeHasArmyAddition param config error");
        }
        this.range = Integer.parseInt(split[0]);
        this.rate = Integer.parseInt(split[1]);
    }

    @Override
    protected int getRate(MonsterEntity owner) {
        return hasArmy(owner) ? basicRate * rate / 100 : basicRate;
    }

    private boolean hasArmy(MonsterEntity owner) {
        List<SceneObjEntity> enemyList = owner.getBattleComponent().getEnemyList(range, obj -> obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_Army);
        return CollectionUtils.isNotEmpty(enemyList);
    }
}

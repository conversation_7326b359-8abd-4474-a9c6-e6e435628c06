package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;

import com.yorha.cnc.scene.areaSkill.AreaSkillFactory;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Ring;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 放指定召唤物技能
 * <AUTHOR>
 */
public class CreateAreaSkillTrigger extends AiTrigger {
    public CreateAreaSkillTrigger(AiStateTriggerTemplate template) {
        super(template);
    }

    private List<IntPairType> skills;
    private long skillEndTime;
    @Override
    protected void parse(List<IntPairType> param) {
        skills = new ArrayList<>();
        for(IntPairType pair : param) {
            if (pair.getKey() == 0) {
                skillEndTime = SystemClock.now() + TimeUtils.second2Ms(pair.getValue());
                continue;
            }
            skills.add(pair);
        }
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        return SystemClock.now() < skillEndTime;
    }

    @Override
    protected void doTrigger(SceneObjEntity owner) {
        Point curPoint = owner.getCurPoint();
        int lifeTime = (int) TimeUtils.ms2Second(skillEndTime - SystemClock.now());
        if (lifeTime <= 0) {
            return;
        }
        for (IntPairType skill : skills) {
            Point point = Ring.valueOf(Point.valueOf(curPoint.getX(), curPoint.getY()), skill.getKey(), skill.getKey() - 1).getRandomPoint();
            AreaSkillFactory.createAreaSkill(owner.getScene(), null, owner.getBattleComponent().getBattleRole(), skill.getValue(), point, lifeTime);
        }
    }

    @Override
    protected String getTriggerParam() {
        return "skillEndTime:"+ skillEndTime +"/"+ skills.toString();
    }

    @Override
    protected String getTriggerName() {
        return "CreateAreaSkillTrigger";
    }
}

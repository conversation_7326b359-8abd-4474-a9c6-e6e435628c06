package com.yorha.cnc.scene.city.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.game.gen.prop.BattleProp;
import com.yorha.game.gen.prop.BattleRecordAllProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class CityBattleComponent extends SceneObjBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjBattleComponent.class);

    public CityBattleComponent(SceneObjEntity owner) {
        super(owner, CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF, null);
    }

    @Override
    protected AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getScenePlayer();
    }

    @Override
    public void init() {
        super.init();
        getBattleProp().setBattleState(CommonEnum.BattleState.BS_IDLE);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSettleRoundEvent, BattleRoleSettleRoundEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onInnerArmyArriveAddChild, InnerArmyAddEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onInnerArmyOutDropChild, InnerArmyDelEvent.class);
    }

    protected void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        getBattleRole().refreshTroop();
    }

    private void onInnerArmyArriveAddChild(InnerArmyAddEvent e) {
        onInnerArmyArriveAddChild0(e, CommonEnum.SceneObjType.SOT_CITY_ARMY_OTHER);
    }

    @Override
    public boolean ready(IBattleRoleAdapter other) {
        onAttackerArrived();
        return super.ready(other);
    }

    /**
     * 攻城队伍到达 初始化驻防队伍数据
     */
    public void onAttackerArrived() {
        // 通知玩家基地遭受攻击
        getOwner().getScenePlayer().tellPlayer(SsPlayerMisc.OnBaseBeAttackCmd.getDefaultInstance());
        // 战斗中无需构建数据
        if (isInBattle()) {
            return;
        }
        // 组装英雄
        buildGarrisonHeroOrMecha();
        // 组装士兵
        buildGarrisonSoldier();
        // 组装防御塔
        buildGuardTower();
        getBattleRole().refreshTroop();
        getBattleRole().buildHero();
        getOwner().getInnerArmyComponent().enterBattle();
    }

    /**
     * 组装守城战斗英雄，战斗机
     */
    protected abstract boolean buildGarrisonHeroOrMecha();

    /**
     * 组装守城战斗士兵
     */
    protected abstract void buildGarrisonSoldier();

    /**
     * 构建防御塔
     */
    protected abstract void buildGuardTower();

    /**
     * 行军拉出后  check守城队伍
     * 检查出去的士兵有没有占用战斗部队名额，重设num
     */
    public void onArmyOut(Collection<Struct.Soldier> outSoldiers) {

    }

    /**
     * 行军回城后  check守城队伍
     * 当前战斗部队未满需要补充部队
     */
    public void onArmyReturn(Collection<SoldierProp> soldierPropList) {

    }

    public void onGarrisonChange() {

    }

    public void onSoldierAdd(Collection<SoldierProp> soldierPropList, SoldierNumChangeReason reason) {

    }

    @Override
    public void clearAfterSettle() {
        super.clearAfterSettle();
        if (!getBattleRole().hasActiveRelation()) {
            clearBattleData();
        }
    }

    protected void clearBattleData() {
        LOGGER.info("role:{} clear battle.", getBattleRole());
        getBattleRole().clearAllProperty();
        clearTroop();
        // clear garrison
        getOwner().getProp().getGarrison().clearSoldierMap();
    }

    @Override
    public void afterReturnAllInnerArmy(List<Long> roleIds) {
        if (!isInBattle()) {
            return;
        }
        getBattleRole().dropRole(roleIds);
    }

    @Override
    public List<Long> getAllChildRoleIdList() {
        List<Long> ret = Lists.newArrayList();
        // 自己
        ret.add(getEntityId());
        // 增援部队
        for (ArmyEntity inRallyArmy : getOwner().getInnerArmyComponent().getInnerArmyList()) {
            ret.add(inRallyArmy.getEntityId());
        }
        return ret;
    }

    @Override
    public boolean isRally() {
        return !getOwner().getInnerArmyComponent().getInnerArmyList().isEmpty();
    }

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }

    public void setWallState(CommonEnum.CityWallState state) {
        getOwner().getProp().setWallState(state);
    }

    public CommonEnum.CityWallState getWallState() {
        return getOwner().getProp().getWallState();
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {

    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {
        // 集结体用的是车头的名字
        roleRecord.setClanName(getOwner().getProp().getClanSname());
        roleRecord.setLocation(getCurPoint().getX(), getCurPoint().getY(), this.getOwner().getScene().getMapIdForPoint(), this.getOwner().getScene().getMapType());
        roleRecord.setCardHead(getOwner().getProp().getCardHead().getCopySsBuilder().build());
    }


    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {

    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        return null;
    }
}

package com.yorha.cnc.mainScene.bigScene.component;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.city.CityFactory;
import com.yorha.cnc.scene.entity.component.ScenePlayerMgrComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerFactory;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BigScenePlayerMgrComponent extends ScenePlayerMgrComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(BigScenePlayerMgrComponent.class);

    public BigScenePlayerMgrComponent(BigSceneEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        initAscendTimer();
        updateOnlinePlayerMonitor();
    }

    @Override
    public BigSceneEntity getOwner() {
        return (BigSceneEntity) super.getOwner();
    }

    @Override
    public boolean onPlayerLogin(long playerId, IActorRef sessionRef, boolean isNewbie, String intelToken, CommonEnum.Language language, CommonEnum.DungeonType dungeonType) {
        ScenePlayerEntity scenePlayer = getScenePlayer(playerId);
        // 新手阶段如果升天了 直接尝试落城  落城失败走服务器已满
        if (isNewbie && scenePlayer.getMainCity().getTransformComponent().isAscend()) {
            boolean isFallOk = false;
            for (int i = 0; i < GameLogicConstants.CITY_FALL_MAX_TIMES_WHEN_NEWBIE; i++) {
                try {
                    scenePlayer.getMainCity().getTransformComponent().cityFall();
                    scenePlayer.getMainCity().getPropComponent().immediatelyFlushProp();
                    isFallOk = true;
                    break;
                } catch (Exception e) {
                    LOGGER.warn("{} city try fall when newbie.but ", scenePlayer, e);
                }
            }
            if (!isFallOk) {
                return false;
            }
        }
        boolean ret;
        // 直接进的副本
        if (dungeonType != CommonEnum.DungeonType.DT_NONE) {
            ret = onPlayerLogin(playerId, sessionRef, dungeonType);
        } else {
            ret = onPlayerLogin(playerId, sessionRef);
        }
        scenePlayer.getNtfComponent().refreshToken(intelToken);
        scenePlayer.getNtfComponent().refreshLanguage(language);
        updateOnlinePlayerMonitor();
        LOGGER.info("{} atScene bigScene login successful playerId={} cityId={} sessionRef={}",
                LogKeyConstants.GAME_PLAYER_LOGIN, playerId, scenePlayer.getProp().getMainCityId(), sessionRef);
        return ret;
    }

    /**
     * 玩家首次登录 创建ScenePlayer
     */
    public void createNewScenePlayer(long playerId, String name, Struct.PlayerCardHead cardHead, long createTime, Point bornPoint) {
        LOGGER.info("{} atScene register start, playerId={} register big scene map player", LogKeyConstants.GAME_PLAYER_LOGIN, playerId);
        ScenePlayerEntity scenePlayer = ScenePlayerFactory.createScenePlayer(getOwner(), playerId, name, cardHead, createTime);
        CityFactory.createCity(getOwner(), playerId, bornPoint);
        // 添加默认的士兵
        ScenePlayerFactory.addDefaultSoldier(scenePlayer);
        // city创建成功了再落地
        scenePlayer.getDbComponent().insertIntoDb();
        scenePlayer.getPropComponent().unlockInitialResource();
        LOGGER.info("{} atScene register end, playerId={} name: {} bornPoint:{}", LogKeyConstants.GAME_PLAYER_LOGIN, playerId, name, bornPoint);
    }

    public Collection<Long> getOnlinePlayerIdsFilterByCreateTime(long startTime, long endTime) {
        return onlinePlayer.values().stream()
                .filter(it -> startTime <= it.getProp().getCreateTime() && it.getProp().getCreateTime() <= endTime)
                .map(AbstractEntity::getEntityId)
                .collect(Collectors.toList());
    }

    @Override
    public void onPlayerLogout(long playerId) {
        super.onPlayerLogout(playerId);
        updateOnlinePlayerMonitor();
    }

    private void updateOnlinePlayerMonitor() {
        int size = onlinePlayer.size();
        MonitorUnit.SCENE_PLAYER_ONLINE_NUM_GAUGE.labels(ServerContext.getBusId()).set(size);
        MonitorUnit.GAME_ZONE_STATUS_PLAYER_ONLINE_NUM.labels(
                ServerContext.getBusId(),
                ServerContext.getWorldIdStr(),
                ownerActor().getZoneIdStr())
                .set(size);
    }

    @Override
    public void onDestroy() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.CITY_ASCEND_CHECK);
    }
}

package com.yorha.cnc.scene.sceneObj.ai.stateMachine.impl;

import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.AbstractStateDiagram;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.StateTransitionConfig;
import com.yorha.proto.CommonEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 守护者状态图
 * <AUTHOR>
 */
public class GuardStateDiagram extends AbstractStateDiagram {
    private static final Map<CommonEnum.AIStateType, List<StateTransitionConfig>> STATE_MAP = new HashMap<>();


    static {
        addTransition(StateTransitionConfig.newBuilder().event(AIEvent.FIND_MASTER).sourceState(CommonEnum.AIStateType.STAY).targetState(CommonEnum.AIStateType.MOVE_TO_MASTER).build(), STATE_MAP);
        addTransition(StateTransitionConfig.newBuilder().event(AIEvent.ARRIVE).sourceState(CommonEnum.AIStateType.MOVE_TO_MASTER).targetState(CommonEnum.AIStateType.SACRIFICE).build(), STATE_MAP);
    }

    @Override
    public Map<CommonEnum.AIStateType, List<StateTransitionConfig>> getStateMap() {
        return STATE_MAP;
    }

    @Override
    public CommonEnum.AIStateType getInitState() {
        return CommonEnum.AIStateType.STAY;
    }
}

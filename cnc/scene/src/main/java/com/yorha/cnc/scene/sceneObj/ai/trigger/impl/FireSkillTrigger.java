package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;

import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.resource.datatype.IntPairType;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 放指定技能
 * <AUTHOR>
 */
public class FireSkillTrigger extends AiTrigger {
    public FireSkillTrigger(AiStateTriggerTemplate template) {
        super(template);
    }

    private List<Integer> skills;

    @Override
    protected void parse(List<IntPairType> param) {
        skills = new ArrayList<>();
        for(IntPairType pair : param) {
            skills.add(pair.getValue());
        }
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        BattleHero mainHero = owner.getBattleComponent().getMainHero();
        return mainHero != null;
    }

    @Override
    protected void doTrigger(SceneObjEntity owner) {
        BattleHero mainHero = owner.getBattleComponent().getMainHero();
        for (int skillId : skills) {
            owner.getEventDispatcher().dispatch(new FireSkillEvent.Builder().setHeroId(mainHero.getId()).setSkillId(skillId).build());
        }
    }

    @Override
    protected String getTriggerParam() {
        return skills.toString();
    }

    @Override
    protected String getTriggerName() {
        return "FireSkillTrigger";
    }
}

package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DebugGroup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 创建野怪
 *
 * <AUTHOR>
 */
public class CreateMonster implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(CreateMonster.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        try {
            int x = Integer.parseInt(args.get("x"));
            int y = Integer.parseInt(args.get("y"));
            int monsterId = Integer.parseInt(args.get("monsterId"));
            int lifeTime = Integer.parseInt(args.get("lifeTime"));
            MonsterTemplate monsterTemplate = ResHolder.getInstance().findValueFromMap(MonsterTemplate.class, monsterId);
            if (monsterTemplate == null) {
                return;
            }
            CommonEnum.MonsterCategory category = monsterTemplate.getCategory();

            SceneObjSpawnParam param = new SceneObjSpawnParam();
            param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(lifeTime));
            if (category == CommonEnum.MonsterCategory.SKYNET_MONSTER) {
                throw new GeminiException(ErrorCode.SYSTEM_WARNING, "天网就正常用天网系统召唤");
            }

            MonsterEntity entity = MonsterFactory.initMonster(actor.getScene(), monsterId, Point.valueOf(x, y), param);

            if (entity == null) {
                return;
            }
            entity.markGm();
            entity.addIntoScene();

            LOGGER.info("gm create monsterId = {}", entity.getEntityId());
        } catch (Exception e) {
            LOGGER.info("gm create monster failed :{}", args, e);
        }

    }

    @Override
    public String showHelp() {
        return "CreateMonster x={midScreenX} y={midScreenY} monsterId={value} lifeTime=30";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MONSTER;
    }
}

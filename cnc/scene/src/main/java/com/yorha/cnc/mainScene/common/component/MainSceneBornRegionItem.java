package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.city.CityFactory;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.city.CityBornService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.proto.CommonEnum.MapAreaType;
import com.yorha.proto.Core;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BornAreaSelectTemplate;
import res.template.ConstTemplate;
import res.template.MapBuildingTemplate;

import java.util.*;

/**
 * 出生州级管理单位
 *
 * <AUTHOR>
 */
public class MainSceneBornRegionItem {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneBornRegionItem.class);
    /**
     * 州id
     */
    private final int regionId;
    private final SceneEntity scene;
    /**
     * 实际落在地上的主堡个数
     */
    private int actualCityNum = 0;
    /**
     * 主堡个数（含升天）
     */
    private int cityNum = 0;
    /**
     * 有效点个数
     */
    private int totalPointNum = 0;
    /**
     * 缓存的待可选片
     * 区域类型 ->区域等级 -> 片id的list
     */
    private final Map<MapAreaType, Map<Integer, List<Integer>>> selectedPool = new HashMap<>();
    /**
     * 片id，可选点的list
     * partId -> pointList
     */
    private final Map<Integer, List<Point>> partPointPool = new Int2ObjectOpenHashMap<>();
    /**
     * 备用数据 selectedPool中检测一次不通过的都放进去
     */
    private final Map<Integer, List<Point>> sparePartPointPool = new Int2ObjectOpenHashMap<>();
    /**
     * 循环次数 合并时清空
     */
    private int totalLoop = 0;
    private int effectiveLoop = 0;
    /**
     * 当前搜索循环次数
     */
    private int loop = 0;

    public MainSceneBornRegionItem(int regionId, SceneEntity scene) {
        this.regionId = regionId;
        this.scene = scene;
    }

    public int getRegionId() {
        return regionId;
    }

    public SceneEntity getScene() {
        return scene;
    }

    public void initPointList(RegionalAreaSettingTemplate template) {
        int partId = template.getId();
        // 随机坐标点
        List<Integer> gridList = MapGridDataManager.getGridList(getScene().getMapId(), partId);
        List<Point> pointList = new ArrayList<>();
        partPointPool.put(partId, pointList);

        for (int gridId : gridList) {
            Point point = MapGridDataManager.getRandomBornPoint(gridId);
            if (checkPointValid(template, point)) {
                pointList.add(point);
                totalPointNum++;
            }
        }
    }

    public int getTotalPointNum() {
        return totalPointNum;
    }

    private boolean checkPointValid(RegionalAreaSettingTemplate template, Point point) {
        if (!getScene().getCityMoveComponent().isPointNavMovable(point)) {
            return false;
        }
        if (template.getBuildingId() != 0) {
            // 判断和静态据点的障碍
            int radius = ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, template.getBuildingId()).getCollisionRadius();
            if (Point.calDisBetweenTwoPoint(Point.valueOf(template.getPosX(), template.getPosY()), point) < radius + CityFactory.getCityRadius()) {
                return false;
            }
        }
        // 看看本区域的
        if (checkBuildingCollision(template.getId(), point)) {
            return false;
        }
        // 再看看相邻区域
        for (Integer partId : template.getAdjoinAreaIdList()) {
            if (checkBuildingCollision(partId, point)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查坐标点是否与阻挡重合
     */
    private boolean checkBuildingCollision(int partId, Point point) {
        // 要求的最小城池间距
        if (partPointPool.isEmpty()) {
            return getScene().getBuildingMgrComponent().checkTownsCollision(partId, point);
        }
        int limit = ResHolder.getConsts(ConstTemplate.class).getCityBornDistanceLimit();
        for (Point point1 : partPointPool.getOrDefault(partId, Collections.emptyList())) {
            if (Point.calDisBetweenTwoPoint(point1, point) < limit) {
                return true;
            }
        }
        return getScene().getBuildingMgrComponent().checkTownsCollision(partId, point);
    }

    /**
     * 合并备选点
     */
    private void mergeSparePointPool(boolean isBorn) {
        GeminiStopWatch watch = new GeminiStopWatch("mergeSparePointPool");
        // 没有备选的？？
        if (sparePartPointPool.isEmpty()) {
            LOGGER.error("mergeSparePointPool but spare is null {}", regionId);
            return;
        }
        if (!isBorn && !partPointPool.isEmpty()) {
            // 不是出生的 非空就开始替换了 有问题
            LOGGER.error("mergeSparePointPool isNotBorn but cur is not empty regionId: {} {}", regionId, partPointPool.size());
        }
        int n = 0;
        for (Map.Entry<Integer, List<Point>> entry : sparePartPointPool.entrySet()) {
            LOGGER.info(" mergeSparePointPool regionId: {} isBorn: {} partId: {} cur: {} spare: {}",
                    regionId, isBorn, entry.getKey(),
                    partPointPool.getOrDefault(entry.getKey(), Collections.emptyList()).size(),
                    entry.getValue().size());
            partPointPool.computeIfAbsent(entry.getKey(), k -> new ArrayList<>()).addAll(entry.getValue());
            n++;
        }
        sparePartPointPool.clear();
        // 统计
        int totalNum = 0;
        for (List<Point> value : partPointPool.values()) {
            totalNum += value.size();
        }
        if (totalNum != totalPointNum) {
            LOGGER.error("mergeSparePointPool but totalPointNumChange {} -> {}", totalPointNum, totalNum);
            totalPointNum = totalNum;
        }
        watch.mark("finished");
        LOGGER.info("mergeSparePointPool end regionId: {}  isBorn: {} cost: {} mergePartNum: {}  loop: {}/{}", regionId, isBorn, watch.getTotalCost(), n, effectiveLoop, totalLoop);
        effectiveLoop = 0;
        totalLoop = 0;
    }

    public Point choosePartPoint(boolean isBorn) {
        GeminiStopWatch watch = new GeminiStopWatch("choosePartPoint");
        loop = 0;
        boolean isReplaced = false;
        if (partPointPool.isEmpty()) {
            mergeSparePointPool(isBorn);
            isReplaced = true;
        }
        // 还是空的
        if (partPointPool.isEmpty()) {
            LOGGER.error("choosePartPoint region pointPool is empty. 1 region: {}", regionId);
            return null;
        }
        Point point = null;
        List<BornAreaSelectTemplate> areaList = null;
        if (isBorn) {
            if (selectedPool.isEmpty()) {
                LOGGER.error("choosePartPoint region partPool is empty. region: {}", regionId);
                return null;
            }
            areaList = ResHolder.getResService(CityBornService.class).getPriorityAreaList();
            point = choosePointBorn(areaList);
        } else {
            point = choosePointRandom();
        }
        if (!isReplaced && point == null && loop < GameLogicConstants.BORN_RANDOM_LOOP_MAX) {
            // 那就把备选的加过来再遍历一遍  因为前面可能只剩下一点
            mergeSparePointPool(isBorn);
            // 还是空的
            if (partPointPool.isEmpty()) {
                LOGGER.error("choosePartPoint region pointPool is empty. 2 region: {}", regionId);
                return null;
            }
            if (isBorn) {
                point = choosePointBorn(areaList);
            } else {
                point = choosePointRandom();
            }
        }
        watch.mark("finish");
        long cost = watch.getTotalCost();
        if (point != null) {
            effectiveLoop++;
            LOGGER.info("choosePartPoint finish isBorn: {} region: {} loop:{} cost:{} effectiveLoop:{} totalLoop:{}", isBorn, regionId, loop, cost, effectiveLoop, totalLoop);
            return point;
        }
        // 失败次数
        MonitorUnit.BIG_SCENE_BORN_FAILED_COUNTER.labels(ServerContext.getBusId()).inc();
        if (loop >= GameLogicConstants.BORN_RANDOM_LOOP_MAX) {
            LOGGER.error("choosePartPoint failed loop is max isBorn: {} region: {} loop:{} cost:{}", isBorn, regionId, loop, cost);
            return null;
        }
        LOGGER.error("choosePartPoint failed cant find isBorn: {} region: {} loop:{} cost:{} totalLoop:{}", isBorn, regionId, loop, cost, totalLoop);
        return null;
    }

    private void addLoop() {
        loop++;
        totalLoop++;
    }

    /**
     * 随机迁城选点 基于现有的选
     */
    private Point choosePointRandom() {
        Iterator<Map.Entry<Integer, List<Point>>> iterator = partPointPool.entrySet().iterator();
        // 为了加随机性
        int index = RandomUtils.nextInt(partPointPool.size());
        int i = 0;
        while (iterator.hasNext()) {
            iterator.next();
            if (i++ >= index) {
                break;
            }
        }
        Point point = scanPointPool(iterator);
        if (point != null || loop >= GameLogicConstants.BORN_RANDOM_LOOP_MAX || partPointPool.isEmpty()) {
            return point;
        }
        iterator = partPointPool.entrySet().iterator();
        return scanPointPool(iterator);
    }

    private Point scanPointPool(Iterator<Map.Entry<Integer, List<Point>>> iterator) {
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<Point>> entry = iterator.next();
            Iterator<Point> pointIt = entry.getValue().iterator();
            while (pointIt.hasNext()) {
                Point point = pointIt.next();
                pointIt.remove();
                addLoop();
                sparePartPointPool.computeIfAbsent(entry.getKey(), (k) -> new ArrayList<>()).add(point);
                try {
                    Core.Code code = BornPointHelper.cityBornCollisionCheck(getScene(), point.getX(), point.getY(), CityFactory.getCityRadius());
                    if (ErrorCode.isOK(code)) {
                        return point;
                    }
                } catch (Exception e) {
                    LOGGER.error("choosePointRandom failed regionId: {} ", regionId, e);
                }
            }
            iterator.remove();
            if (loop >= GameLogicConstants.BORN_RANDOM_LOOP_MAX) {
                return null;
            }
        }
        return null;
    }

    /**
     * 出生选点 基于可选片
     */
    private Point choosePointBorn(List<BornAreaSelectTemplate> areaList) {
        // 遍历优先级排好的区域
        for (BornAreaSelectTemplate it : areaList) {
            List<Integer> partList = selectedPool.getOrDefault(it.getAreaType(), Collections.emptyMap()).getOrDefault(it.getAreaLevel(), Collections.emptyList());
            if (partList.isEmpty()) {
                // 1. 该区域可选片没了 导量太满了   2. 该区域这个州就没有
                continue;
            }
            // 随机选一个起始片
            int startIndex = RandomUtils.nextInt(partList.size());
            for (int i = startIndex; i < partList.size() + startIndex; i++) {
                int partId = partList.get(i % partList.size());
                List<Point> pointList = partPointPool.get(partId);
                // 已经移除过了
                if (pointList == null) {
                    continue;
                }
                // 上次用完了 移除下
                if (pointList.isEmpty()) {
                    partPointPool.remove(partId);
                    continue;
                }
                Iterator<Point> iterator = pointList.iterator();
                while (iterator.hasNext()) {
                    Point point = iterator.next();
                    iterator.remove();
                    sparePartPointPool.computeIfAbsent(partId, (k) -> new ArrayList<>()).add(point);
                    addLoop();
                    try {
                        Core.Code code = BornPointHelper.cityBornCollisionCheck(getScene(), point.getX(), point.getY(), CityFactory.getCityRadius());
                        if (ErrorCode.isOK(code)) {
                            return point;
                        }
                    } catch (Exception e) {
                        LOGGER.error("choosePointBorn failed regionId: {} ", regionId, e);
                    }
                }
                partPointPool.remove(partId);
                if (loop >= GameLogicConstants.BORN_RANDOM_LOOP_MAX) {
                    return null;
                }
            }
            if (loop >= GameLogicConstants.BORN_RANDOM_LOOP_MAX) {
                return null;
            }
        }
        return null;
    }

    public void addToSelectPartList(MapAreaType type, int level, int partId) {
        Map<Integer, List<Integer>> map = selectedPool.computeIfAbsent(type, k -> new HashMap<>());
        map.computeIfAbsent(level, k -> new ArrayList<>()).add(partId);
    }

    public void removeFromSelectPartList(MapAreaType type, int level, Integer partId) {
        Map<Integer, List<Integer>> map = selectedPool.get(type);
        if (map == null) {
            LOGGER.error("removeFromSelectPartList type not exist {}", partId);
            return;
        }
        List<Integer> map2 = map.get(level);
        if (map2 == null) {
            LOGGER.error("removeFromSelectPartList level not exist {}", partId);
            return;
        }
        if (map2.remove(partId)) {
            LOGGER.info("removeFromSelectPartList {}", partId);
        }
        // 是保底移除的 只要大于就尝试移除 所以不用打error
        //LOGGER.error("removeFromSelectPartList part not exist {}", partId);
    }

    public void incCityNum() {
        cityNum++;
        LOGGER.info("BigSceneBornRegionItem incCityNum region:{} city:{}", regionId, cityNum);
    }

    public void decCityNum() {
        cityNum--;
        LOGGER.info("BigSceneBornRegionItem decCityNum region:{} city:{}", regionId, cityNum);
    }

    public int getCityNum() {
        return cityNum;
    }

    public void incActualCityNum() {
        actualCityNum++;
        LOGGER.info("BigSceneBornRegionItem incActualCityNum region:{} actual city:{}", regionId, actualCityNum);
    }

    public void decActualCityNum() {
        actualCityNum--;
        LOGGER.info("BigSceneBornRegionItem decActualCityNum region:{} actual city:{}", regionId, actualCityNum);
    }

    public int getActualCityNum() {
        return actualCityNum;
    }
}

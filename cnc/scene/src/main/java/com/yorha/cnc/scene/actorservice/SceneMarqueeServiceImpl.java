package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.actor.SceneMarqueeService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsSceneMarquee;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SceneMarqueeServiceImpl implements SceneMarqueeService {
    private static final Logger LOGGER = LogManager.getLogger(SceneMarqueeServiceImpl.class);
    private final SceneActor sceneActor;

    public SceneMarqueeServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    @Override
    public void handleSendSceneMarqueeCmd(SsSceneMarquee.SendSceneMarqueeCmd ask) {
        if (!ask.hasMarqueeId()) {
            LOGGER.error("handleSendSceneMarqueeCmd: no marqueeId, invalid ask, ask={}", ask);
            return;
        }
        if (!ask.hasDisplayData()) {
            LOGGER.error("handleSendSceneMarqueeCmd: no displayData, invalid ask, ask={}", ask);
            return;
        }
        getScene().getMarqueeComponent().sendFullServerMarquee(
                getScene().getZoneId(),
                ask.getMarqueeId(),
                MsgHelper.getDisplayDataPB(ask.getDisplayData()),
                MsgHelper.getClanFlagInfoPB(ask.getClanFlagInfo()));
    }

    @Override
    public void handleSendSceneLoopMarqueeWithIdIpAsk(SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk ask) {
        if (ask.getOneLoopOfMs() <= 0 || ask.getPeriod() < 0) {
            throw new GeminiException(ErrorCode.FAILED, StringUtils.format("marquee task error. period={} count={}", ask.getOneLoopOfMs(), ask.getPeriod()));
        }
        getScene().getMarqueeComponent().sendLoopMarquee(ask.getOneLoopOfMs(), ask.getPeriod(), ask.getDisplayDataMap());
        sceneActor.answer(SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.getDefaultInstance());
    }
}

package com.yorha.cnc.scene.npcplayer;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.npcplayer.component.NpcPlayerArmyMgrComponent;
import com.yorha.cnc.scene.npcplayer.component.NpcPlayerSoldierComponent;
import com.yorha.cnc.scene.sceneplayer.component.ScenePlayerPositionMarkComponent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;

/**
 * <AUTHOR>
 */
public class NpcPlayerEntity extends AbstractScenePlayerEntity {

    public NpcPlayerEntity(SceneEntity sceneEntity, long playerId, NpcPlayerBuilder builder) {
        super(sceneEntity, playerId, builder);
        initAllComponents();
    }

    @Override
    public void tellPlayer(GeneratedMessageV3 msg) {
        throw new GeminiException("want tell npc player {}", msg);
    }

    @Override
    public NpcPlayerSoldierComponent getSoldierMgrComponent() {
        return (NpcPlayerSoldierComponent) super.getSoldierMgrComponent();
    }

    @Override
    public ScenePlayerPositionMarkComponent getPositionMarkComponent() {
        throw new GeminiException(getEntityType() + "not support positionMarkComponent");
    }

    @Override
    public NpcPlayerArmyMgrComponent getArmyMgrComponent() {
        return (NpcPlayerArmyMgrComponent) super.getArmyMgrComponent();
    }

    @Override
    public void onLogin(IActorRef sessionRef) {

    }

    @Override
    public EntityAttrOuterClass.EntityType getEntityType() {
        return null;
    }


    @Override
    public CommonEnum.Camp getCampEnum() {
        return getMainCity().getCampEnum();
    }

}

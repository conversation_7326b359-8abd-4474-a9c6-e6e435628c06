package com.yorha.cnc.scene.sceneObj.component.aoi;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.EntityAttrOuterClass;

import java.util.Set;

/**
 * <AUTHOR>
 */
public abstract class SceneObjAoiComponent extends SceneObjComponent<SceneObjEntity> {
    /**
     * 是否加入aoi视野
     */
    protected boolean isAddIntoAoi = false;

    public SceneObjAoiComponent(SceneObjEntity owner) {
        super(owner);
    }

    public boolean isInAoi() {
        return isAddIntoAoi;
    }

    /**
     * 加入视野 加入阻挡 阻挡是跟视野同步的
     */
    public abstract void addIntoAoi(SceneObjectNtfReason reason);

    /**
     * 离开视野 移除阻挡
     */
    public abstract void removeFromAoi(SceneObjectNtfReason reason);

    /**
     * 刷新aoi，对新增和离开aoi的对象发送视野信息
     */
    public void refreshAoi(SceneObjectNtfReason reason) {
    }


    /**
     * 对外广播
     */
    public abstract void broadcast(int msgType, GeneratedMessageV3 message);

    /**
     * 对外广播
     */
    public void broadcast(int msgType, GeneratedMessageV3 message, int layer) {
        broadcast(msgType, message);
    }

    /**
     * 以下都是AoiGrid世界独有的
     */
    public void setAlwaysSyncPlayer(Set<Long> playerIds) {
    }

    public void setAlwaysSyncPlayer(long playerId) {

    }

    public void addAlwaysSyncPlayer(long playerId) {

    }

    public void removeAlwaysSyncPlayer(long playerId) {

    }

    public void clearAlwaysSyncPlayer() {

    }

    public void onChangeLayer(int oldLayer, int newLayer) {

    }

    public void onBriefChange(int oldLayer, int newLayer) {
    }

    /**
     * 对外广播属性变更
     */
    public abstract void broadcastEntityModNtf(EntityAttrOuterClass.EntityAttr.Builder attrBuilder, boolean needMerge);
}

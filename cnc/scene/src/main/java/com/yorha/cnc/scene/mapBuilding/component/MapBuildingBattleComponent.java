package com.yorha.cnc.scene.mapBuilding.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.GarrisonChangeEvent;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.battle.soldier.SoldierUnit;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.army.ArmyHeroPropChangeEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.battle.AfterEndAllBattleEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.event.mapbuilding.ChangeAssistLeaderEvent;
import com.yorha.cnc.scene.event.mapbuilding.MapBuildingStateChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.battle.BattleAttackType;
import com.yorha.common.enums.qlog.battle.BattleRepDefenceType;
import com.yorha.common.enums.qlog.battle.BattleRepMainObjType;
import com.yorha.common.enums.qlog.battle.BattleRepResult;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import qlog.flow.QlogCncBattleReport;
import res.template.ConstClanTerritoryTemplate;
import res.template.ConstTemplate;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class MapBuildingBattleComponent extends SceneObjBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingBattleComponent.class);
    private final Map<Long, Integer> attackerDamage = new HashMap<>();
    /**
     * 结算占领时保存所有带军团id -> 军队实体的映射
     */
    private final Map<Long, ArmyEntity> armyIdToEntityMap = Maps.newHashMap();
    /**
     * 结算占领时军团id -> 集结军队实体的映射
     */
    private final Map<Long, ArmyEntity> clanToRallyArmyEntityMap = Maps.newHashMap();
    /**
     * 结算占领时所有攻击军队实体的列表
     */
    private final List<ArmyEntity> allAttackArmyEntityList = Lists.newLinkedList();
    /**
     * 结算占领时攻击军队id的列表
     */
    private final List<Long> allAttackArmyIdList = Lists.newLinkedList();
    /**
     * 结算占领时军团id -> 玩家id集合的映射
     */
    private final Map<Long, Set<Long>> clanToPlayerIdSetMap = Maps.newHashMap();
    /**
     * 记录是否成功占领
     */
    private boolean isBeAttackSuccess = false;

    /**
     * 等待处理的行军们
     */
    private final List<ArmyEntity> waitEndArmyList = Lists.newArrayList();

    public MapBuildingBattleComponent(MapBuildingEntity owner) {
        super(owner, judgeSceneObjType(owner), null);
    }

    private static SceneObjType judgeSceneObjType(MapBuildingEntity mapBuilding) {
        return mapBuilding.isNeutral() ? SceneObjType.SOT_STRONG_POINT_ARMY : SceneObjType.SOT_CLAN_BUILDING_ARMY;
    }

    @Override
    public void init() {
        super.init();
        getBattleProp().setBattleState(BattleState.BS_IDLE);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSettleRoundEvent, BattleRoleSettleRoundEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onInnerArmyArriveAddChild, InnerArmyAddEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onInnerArmyOutDropChild, InnerArmyDelEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onArmyHeroPropChange, ArmyHeroPropChangeEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onStateChange, MapBuildingStateChangeEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::checkLeaderArmyChange, ChangeAssistLeaderEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndSingleRelation, EndSingleBattleEvent.class);
    }

    private void onInnerArmyArriveAddChild(InnerArmyAddEvent e) {
        onInnerArmyArriveAddChild0(e, SceneObjType.SOT_CLAN_BUILDING_ARMY);
    }

    private void onStateChange(MapBuildingStateChangeEvent e) {
        getBattleRole().setType(judgeSceneObjType(getOwner()));
    }

    private void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        getBattleRole().refreshTroop();
        attackerDamage.clear();
        event.getDamageResult().getSoldierLossSource().forEach((armyId, soldierLossData) -> attackerDamage.put(armyId, soldierLossData.totalLoss()));
        getBattleRole().getRelationKeys().forEach(armyId -> {
                    if (getBattleRole().relationWith(armyId).getStatus() != BattleConstants.BattleRelationStatus.Running) {
                        attackerDamage.remove(armyId);
                    } else if (!attackerDamage.containsKey(armyId)) {
                        attackerDamage.put(armyId, 0);
                    }
                }
        );
    }

    private void checkLeaderArmyChange(IEvent e) {
        if (!isInBattle()) {
            return;
        }
        long newLeader = getOwner().getInnerArmyComponent().getLeaderArmyId();
        if (newLeader == 0) {
            // 队长没了 说明是所有人撤回了 那就不管了 后面会结束战斗的
            return;
        }
        setLeaderHero(getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, newLeader));
        getOwner().getEventDispatcher().dispatch(new GarrisonChangeEvent());
    }

    @Override
    public boolean beAttacked(ArmyEntity armyEntity, Long attackerPlayerId) {
        // 添加预警信息
        WarningType type = armyEntity.isRallyArmy() ? WarningType.WT_RallyAttack : WarningType.WT_Attack;
        getOwner().getInnerArmyComponent().addWarningItem(armyEntity, type);
        return super.beAttacked(armyEntity, attackerPlayerId);
    }

    @Override
    public boolean ready(IBattleRoleAdapter other) {
        onAttackerArrived();
        // 有人打领地建筑  发送qlog
        if (getOwner().getClanId() == 0) {
            // 发起攻击方军团打qlog
            getOwner().getQLogComponent().sendMapBuildLog(other.getClanId(), "attack_monster_building", String.valueOf(other.getPlayerId()));
            // 攻击方军团打一条军团日志
            getOwner().getClanLogComponent().recordStartAttackLog(getOwner().getProp().getTemplateId(), other.getClanId(), getOwner().getCurPoint());
        } else {
            // 发起攻击方军团打qlog
            getOwner().getQLogComponent().sendMapBuildLog(other.getClanId(), "attack_guild_building", String.valueOf(other.getPlayerId()));
            // 攻击方军团打一条军团日志
            getOwner().getClanLogComponent().recordStartAttackLog(getOwner().getProp().getTemplateId(), other.getClanId(), getOwner().getCurPoint());
            // 防守方军团打一条qlog
            getOwner().getQLogComponent().sendMapBuildLog(getOwner().getClanId(), "be_attack_guild_building", String.valueOf(other.getPlayerId()));
            // 防守方军团打一条军团日志
            try {
                AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(other.getPlayerId());
                getOwner().getClanLogComponent().recordBeAttackedLog(getOwner().getProp().getTemplateId(),
                        abstractScenePlayerEntity, getOwner().getClanId(), getOwner().getCurPoint());
            } catch (Exception e) {
                LOGGER.error("error when try record clan log, ", e);
            }

        }
        return super.ready(other);
    }

    /**
     * 攻城队伍到达 初始化驻防队伍数据
     */
    public void onAttackerArrived() {
        // 战斗中无需构建数据
        if (isInBattle()) {
            return;
        }
        MapBuildingInnerArmyComponent innerArmyComponent = getOwner().getInnerArmyComponent();
        innerArmyComponent.enterBattle();
        // 中立状态 使用配置部队
        if (isNeutral()) {
            getBattleRole().setType(SceneObjType.SOT_STRONG_POINT_ARMY);
            StructPlayer.Troop troop = TroopHelper.getTroopBuilder(ResHolder.getInstance(), getOwner().getBuildingTemplate().getTroopId());
            getTroop().mergeFromSs(troop);
            // 不是据点 而且被占领过 就是一次性兵力
            if (getOwner().getAreaType() != MapAreaType.TERRITORY) {
                OccupyInfoProp occupyProp = getOwner().getProp().getOccupyinfo();
                if (occupyProp.getFisrtOwnTsMs() != 0 || occupyProp.getOccupyTsMs() != 0) {
                    getTroop().getTroop().clear();
                }
            }
            getTroop().setTroopId(getOwner().getBuildingTemplate().getTroopId());
            getTroop().markAll();
            if (getTroop().getMainHero().getHeroId() != 0) {
                getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
            }
            if (getTroop().getDeputyHero().getHeroId() != 0) {
                getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
            }
            for (SoldierProp selfSoldier : getTroop().getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_STRONG_POINT_ARMY), selfSoldier);
            }
            return;
        }
        // 占领中/荒废中/ 状态  使用城内驻守部队
        long leaderArmyId = innerArmyComponent.getLeaderArmyId();
        getBattleRole().setType(SceneObjType.SOT_CLAN_BUILDING_ARMY);
        for (ArmyEntity innerArmy : innerArmyComponent.getInnerArmyList()) {
            TroopProp troop = innerArmy.getProp().getTroop();
            if (innerArmy.getEntityId() == leaderArmyId) {
                setLeaderHero(innerArmy);
            }
            for (SoldierProp innerArmySoldierProp : troop.getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(innerArmy.getEntityId(), SceneObjType.SOT_CLAN_BUILDING_ARMY), innerArmySoldierProp);
            }
            getBattleRole().refreshTroop();
            // 战斗发生了  战报有参与者一份
            addBattleEntityCache(innerArmy.getEntityId(), innerArmy.getPlayerId());
        }
    }

    private boolean isNeutral() {
        return getOwner().isNeutral();
    }

    private void setLeaderHero(ArmyEntity armyEntity) {
        if (armyEntity == null) {
            getTroop().getMainHero().setHeroId(0);
            getTroop().getDeputyHero().setHeroId(0);
            getBattleRole().setMainHero(null);
            getBattleRole().setDeputyHero(null);
            return;
        }
        TroopProp troop = armyEntity.getProp().getTroop();
        if (troop.getMainHero().getHeroId() != 0) {
            getTroop().getMainHero().mergeFromSs(troop.getMainHero().getCopySsBuilder().build());
            getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
        } else {
            getTroop().getMainHero().setHeroId(0);
            getBattleRole().setMainHero(null);
        }
        if (troop.getDeputyHero().getHeroId() != 0) {
            getTroop().getDeputyHero().mergeFromSs(troop.getDeputyHero().getCopySsBuilder().build());
            getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
        } else {
            getTroop().getDeputyHero().setHeroId(0);
            getBattleRole().setDeputyHero(null);
        }
    }

    public void onEndSingleRelation(EndSingleBattleEvent event) {
        if (!isNeutral()) {
            // 增加玩家战斗胜利失败的统计
            updateBattleRecordToPlayer(event);
        }
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
        LOGGER.info("{} endAllRelation. damage_map :{}", getOwner(), attackerDamage);
        notifyScenePlayerEndAllRelation(battleResult);
        // 如果赢了 不处理
        if (attackerDamage.isEmpty() || (battleResult.alive && battleResult.type != BattleOverType.BOT_END_MAPBUILDING_ABANDON)) {
            attackerDamage.clear();
            isBeAttackSuccess = false;
            return;
        }
        isBeAttackSuccess = true;
    }

    public void addWaitEndArmy(ArmyEntity army) {
        waitEndArmyList.add(army);
    }

    /**
     * 清理等待处理的行军们
     */
    public void clearWaitEndRally(long exceptedRallyArmyId) {
        if (waitEndArmyList.isEmpty()) {
            return;
        }
        LOGGER.info("{} waitEndArmyList: {}", getOwner(), waitEndArmyList);
        for (ArmyEntity army : waitEndArmyList) {
            if (army.getEntityId() == exceptedRallyArmyId) {
                continue;
            }
            if (army.isRallyArmy()) {
                army.getBattleComponent().forceEndAllBattle();
                RallyEntity rallyEntity = army.getRallyEntity();
                if (rallyEntity != null) {
                    rallyEntity.onRallyKillTarget();
                }
            } else {
                army.getBattleComponent().afterBattleAction();
                // 因为占领完援助设置属性时 联盟名还没有赋值上去 所以在这里刷一下...
                if (army.getStatusComponent().getState() == ArmyDetailState.ADS_ASSIST) {
                    army.getStatusComponent().setDetailTarget(ArmyDetailState.ADS_ASSIST, getOwner());
                }
            }
        }
        waitEndArmyList.clear();
    }

    @Override
    public void afterEndAllRelation() {
        // 这里会清理所有参与战斗的玩家id
        super.afterEndAllRelation();
        // 计算占领相关的数据
        calOccupyData();
        // 获取可以占领的军团id和军队id
        Pair<Long, Long> clanArmyPair = getSuccessOccupyClanAndArmyPair();
        // 被改建过的建筑不执行其他联盟占领逻辑
        if (getOwner().getProp().getConstructInfo().getType() == MapBuildingType.MBT_NONE) {
            // 仅当军团id和部队id都不是0的时候才执行占领逻辑
            if (clanArmyPair.getFirst() != 0L && clanArmyPair.getSecond() != 0L) {
                // 停止上一个联盟的占领
                getOwner().getOccupyComponent().stopOccupy(clanArmyPair.getFirst());
                // 将胜利者加入到据点上
                addWinnerToBuilding(clanArmyPair.getFirst(), clanToRallyArmyEntityMap.get(clanArmyPair.getFirst()), allAttackArmyEntityList);
                // 记录伤害最高的玩家名字到据点上，该玩家被认为是最后一击的玩家
                recordPlayerNameToBuilding(armyIdToEntityMap.get(clanArmyPair.getSecond()).getProp().getCardHead().getName());
                // 成功占领时，通知所有参与战斗的玩家
                ntfAllAttendPlayerWhenSuccessOccupy(clanArmyPair.getFirst());
            }
        } else {
            // 无论被攻击是否成功，当建筑是军团改建建筑时给建筑owner clan发送受到攻击邮件
            sendBeAttackedMailToOwnBuildingClan();
            // 改建建筑如果被攻击成功，根据建筑状态变为着火或回归中立
            if (isBeAttackSuccess) {
                getOwner().getStageMgrComponent().onAttackSuccess(clanArmyPair.getFirst());
            }
        }
        // 处理的等待的army 们
        clearWaitEndRally(0L);
        // 清除所有计算的占领数据
        clearOccupyData();
        getTroop().setTroopId(0);
        getOwner().getEventDispatcher().dispatch(new AfterEndAllBattleEvent());
        SceneObjAiComponent aiComponent = getOwner().getAiComponent();
        if (aiComponent != null) {
            aiComponent.reset();
        }
    }

    /**
     * 计算占领用的数据
     */
    private void calOccupyData() {
        if (attackerDamage.isEmpty()) {
            return;
        }
        LOGGER.info("{} start cal occupy information", getOwner());
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (long armyId : new ArrayList<>(attackerDamage.keySet())) {
            // 伤害结算的时候，军队已经不存在或无存活，则跳过
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null || !army.getBattleComponent().hasAnyAlive()) {
                continue;
            }
            final long armyClanId = army.getClanId();
            if (armyClanId != 0) {
                armyIdToEntityMap.put(armyId, army);
                if (army.isRallyArmy()) {
                    clanToRallyArmyEntityMap.put(armyClanId, army);
                }
                allAttackArmyIdList.add(armyId);
                // 额外记录占领时，本回合造成了伤害的军队或集结体的军团id -> 玩家id
                tryRecordAttendPlayerIds(army);
            }
        }
        // 攻击部队中没有军队属于任何军团
        if (armyIdToEntityMap.keySet().isEmpty()) {
            return;
        }
        // 根据造成的伤害，降序排序所有军队
        allAttackArmyIdList.sort(Comparator.comparing(o -> -attackerDamage.get(o)));
        allAttackArmyIdList.forEach(armyId -> allAttackArmyEntityList.add(armyIdToEntityMap.get(armyId)));
    }

    private void tryRecordAttendPlayerIds(ArmyEntity army) {
        final long armyClanId = army.getClanId();
        if (!clanToPlayerIdSetMap.containsKey(armyClanId)) {
            clanToPlayerIdSetMap.put(armyClanId, new HashSet<>());
        }
        if (!army.isRallyArmy()) {
            clanToPlayerIdSetMap.get(armyClanId).add(army.getPlayerId());
            return;
        }
        RallyEntity rallyEntity = army.getRallyEntity();
        if (null == rallyEntity) {
            LOGGER.error("tryRecordAttendPlayerIds, rallyEntity is null, armyId:{}", army.getEntityId());
            return;
        }
        for (ArmyEntity inRallyArmy : rallyEntity.getArmyMgrComponent().getInRallyArmies()) {
            // 获取在集结体内所有军队，并加入到集合内
            clanToPlayerIdSetMap.get(armyClanId).add(inRallyArmy.getPlayerId());
        }
    }


    /**
     * 清理所有临时计算的占领用数据
     */
    private void clearOccupyData() {
        armyIdToEntityMap.clear();
        clanToRallyArmyEntityMap.clear();
        allAttackArmyEntityList.clear();
        allAttackArmyIdList.clear();
        clanToPlayerIdSetMap.clear();
        isBeAttackSuccess = false;
    }

    /**
     * @return 获取成功占领的军团id和军队id
     */
    private Pair<Long, Long> getSuccessOccupyClanAndArmyPair() {
        // 攻击部队没有一只军队属于军团
        if (armyIdToEntityMap.size() == 0) {
            return Pair.of(0L, 0L);
        }
        if (armyIdToEntityMap.keySet().size() == 1) {
            // 攻击部队都是同一军团的（只会循环一次）
            for (long armyId : armyIdToEntityMap.keySet()) {
                long clanId = armyIdToEntityMap.get(armyId).getClanId();
                if (isClanCanOccupy(clanId)) {
                    return Pair.of(clanId, allAttackArmyIdList.get(0));
                }
            }
        } else {
            // 攻击部队隶属于多个军团，选出伤害最高的军队，将该军团标志为胜者
            for (long armyId : allAttackArmyIdList) {
                long clanId = armyIdToEntityMap.get(armyId).getClanId();
                if (isClanCanOccupy(clanId)) {
                    return Pair.of(clanId, armyId);
                }
            }
        }
        return Pair.of(0L, 0L);
    }

    /**
     * @param clanId 军团id
     * @return 返回军团id对应的军团是否可以成功占领
     */
    private boolean isClanCanOccupy(long clanId) {
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClan == null) {
            return false;
        }
        ErrorCode errorCode = sceneClan.getMapBuildingComponent().checkCanAttack(getOwner());
        return errorCode.isOk();
    }

    private void addWinnerToBuilding(long clanId, ArmyEntity rallyArmy, List<ArmyEntity> armyEntityList) {
        MapBuildingInnerArmyComponent innerArmyComponent = getOwner().getInnerArmyComponent();
        // 先把集结部队成员塞进去
        if (rallyArmy != null) {
            RallyEntity rallyEntity = rallyArmy.getRallyEntity();
            // 只能在这 因为dismiss那边不好拿自己  塞进去变成援助 塞不进去的看看挂了没 挂了就回城 没挂就是退出集结原地等着
            // 先把队长加进去
            if (!innerArmyComponent.armyArrivedByOccupy(rallyEntity.getLeaderArmy())) {
                rallyEntity.getLeaderArmy().getRallyComponent().onQuitRally(rallyArmy.getCurPoint(), true, 0);
            }
            // 再把已经到达的成员加进去
            for (ArmyEntity inRallyArmy : rallyEntity.getArmyMgrComponent().getInRallyArmies()) {
                if (inRallyArmy == rallyEntity.getLeaderArmy()) {
                    continue;
                }
                if (!innerArmyComponent.armyArrivedByOccupy(inRallyArmy)) {
                    inRallyArmy.getRallyComponent().onQuitRally(rallyArmy.getCurPoint(), true, 0);
                }
            }
            // 解散该集结(把未到的人改写成援助指令)
            rallyEntity.dismiss(RallyDismissReason.RDR_OCCUPY_TARGET);
        }
        // 把单人成员塞进去
        for (ArmyEntity army : armyEntityList) {
            if (army.getClanId() == clanId && !army.isRallyArmy()) {
                innerArmyComponent.armyArrivedByOccupy(army);
            }
        }
        attackerDamage.clear();
        getOwner().getOccupyComponent().startOccupy(clanId);
        if (rallyArmy != null) {
            clearWaitEndRally(rallyArmy.getEntityId());
        } else {
            clearWaitEndRally(0);
        }
    }

    private void recordPlayerNameToBuilding(String playerName) {
        getOwner().getProp().getOccupyinfo().setLastHitPlayerName(playerName);
    }

    /**
     * 成功占领后，通知所有参与战斗的玩家
     *
     * @param winnerClanId 胜利者军团id
     */
    private void ntfAllAttendPlayerWhenSuccessOccupy(final long winnerClanId) {
        if (!clanToPlayerIdSetMap.containsKey(winnerClanId)) {
            LOGGER.error("ntfAllAttendPlayerWhenSuccessOccupy, winnerClanId:{} not in clanToPlayerIdSetMap", winnerClanId);
            return;
        }
        SsPlayerScene.OnOccupySuccessCmd cmd = SsPlayerScene.OnOccupySuccessCmd.newBuilder()
                .setPartId(getOwner().getProp().getPartId())
                .setTemplateId(getOwner().getProp().getTemplateId())
                .setAreaType(getOwner().getAreaType())
                .build();
        for (long playerId : clanToPlayerIdSetMap.get(winnerClanId)) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            scenePlayer.tellPlayer(cmd);
        }
    }

    @Override
    public void clearAfterSettle() {
        super.clearAfterSettle();
        if (!getBattleRole().hasActiveRelation()) {
            clearBattleData();
        }
    }

    @Override
    public void trySendRecordMail(Set<Long> playerIds, BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
        if (getClanId() <= 0) {
            return;
        }
        if (!playerIds.isEmpty()) {
            batchSendRecordMail(playerIds, recordAllProp, alive, anyEnemyAlive);
        }
    }

    private void clearBattleData() {
        getBattleRole().clearAllProperty();
        clearTroop();
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {
        BattleRecordRoleSummaryProp selfSummary = recordAllProp.getSelfSummary();
        BattleRecordRoleProp selfRoleProp = getSelfOrEnemyRole(recordAllProp, false);
        selfSummary.setRoleType(SceneObjType.SOT_CLAN_BUILDING_ARMY);
        if (selfRoleProp != null) {
            selfSummary.getCardHead().mergeFromSs(selfRoleProp.getCardHead().getCopySsBuilder().build());
            selfSummary.setClanName(selfRoleProp.getClanName());
        } else {
            selfSummary.setClanName(getOwner().getProp().getOccupyinfo().getShowClanSimpleName());
            selfSummary.getCardHead().setPic(getOwner().getProp().getTemplateId());
            // 填充占领者信息
            ArmyEntity leaderArmy = getOwner().getInnerArmyComponent().getLeaderArmy();
            if (leaderArmy != null) {
                selfSummary.getCardHead().mergeFromSs(leaderArmy.getProp().getCardHead().getCopySsBuilder().build());
            } else {
                // 填充初始部队信息
                selfSummary.getCardHead().setName(String.valueOf(getOwner().getProp().getTemplateId()));
            }
        }
    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {
        roleRecord.setClanName(getOwner().getProp().getOccupyinfo().getShowClanSimpleName());
        Point pos = getOwner().getCurPoint();
        roleRecord.setLocation(pos.getX(), pos.getY(), this.getOwner().getScene().getMapIdForPoint(), this.getOwner().getScene().getMapType());
        roleRecord.setBuildingId(getOwner().getProp().getTemplateId());
        Struct.PlayerCardHead.Builder builder = Struct.PlayerCardHead.newBuilder()
                .setPic(getOwner().getProp().getTemplateId());
        // 填充占领者信息
        ArmyEntity leaderArmy = getOwner().getInnerArmyComponent().getLeaderArmy();
        if (leaderArmy != null) {
            builder.setName(leaderArmy.getPlayerName());
        } else {
            // 填充初始部队信息
            builder.setName(String.valueOf(getOwner().getProp().getTemplateId()));
        }
        roleRecord.setCardHead(builder.build());
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        // 初始野怪部队或无人占领
        if (isNeutral() || getOwner().getInnerArmyComponent().getInnerArmyList().isEmpty()) {
            roleRecord.addMember(buildRoleMemberRecord());
        }

        // 增援部队
        for (ArmyEntity inRallyArmy : getOwner().getInnerArmyComponent().getInnerArmyList()) {
            roleRecord.addMember(inRallyArmy.getBattleComponent().buildRoleMemberRecord());
        }
    }

    public long getBattleRecordPlayerId() {
        ArmyEntity leaderArmy = getOwner().getInnerArmyComponent().getLeaderArmy();
        if (leaderArmy != null) {
            return leaderArmy.getPlayerId();
        } else {
            return getOwner().getProp().getTemplateId();
        }
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        long battleRecordPlayerId = getBattleRecordPlayerId();
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getEntityId())
                .setPlayerId(getOwner().getPlayerId())
                .setClanName(getOwner().getProp().getOccupyinfo().getShowClanSimpleName())
                .setCardHead(Struct.PlayerCardHead.newBuilder().setName(String.valueOf(battleRecordPlayerId)).setPic((int) battleRecordPlayerId).build());
        HeroProp mainHeroProp = getBattleRole().getMainHero() != null ? getBattleRole().getMainHero().getHeroProp() : new HeroProp();
        HeroProp deputyHeroProp = getBattleRole().getDeputyHero() != null ? getBattleRole().getDeputyHero().getHeroProp() : new HeroProp();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }

    private void onArmyHeroPropChange(ArmyHeroPropChangeEvent event) {
        if (!isInBattle()) {
            return;
        }
        int heroId = event.getHeroChangeProp().getHeroId();
        LOGGER.debug("{} onArmyHeroPropChange armyId={} heroId={}", this, event.getEntityId(), heroId);
        if (event.getEntityId() == getOwner().getInnerArmyComponent().getLeaderArmyId()) {
            // 这里的mainHero或者deputyHero都是直接用的army上的heroProp，所以不需要刷新属性，直接重构BattleHero即可
            if (getBattleRole().getMainHero().getId() == heroId) {
                getBattleRole().getMainHero().refreshHeroByProp();
            } else if (getBattleRole().getDeputyHero().getId() == heroId) {
                getBattleRole().getDeputyHero().refreshHeroByProp();
            }
        }
    }

    public void endBattleWithClanId(long clanId) {
        getBattleRole().forEachCopyRelation(relation -> {
            BattleRole enemyRole = relation.getEnemyRole(getRoleId());
            SceneObjEntity enemy = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(enemyRole.getRoleId());
            if (enemy.getClanId() == clanId) {
                relation.forceEndRelation(BattleOverType.BOT_FORCE_END);
            }
        });
    }

    @Override
    protected StructMail.MailShowTitle getMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        StructMail.MailShowTitle.Builder builder = StructMail.MailShowTitle.newBuilder();
        BattleRecordRoleProp enemyRole = getSelfOrEnemyRole(recordAllProp, true);
        // title
        builder.setTitleKey(getBattleRecordMailTitle(alive, anyEnemyAlive, enemyRole));

        // sub title
        if (enemyRole != null) {
            boolean isSuperWeaponAtk = enemyRole.getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill.getNumber();
            if (isSuperWeaponAtk) {
                ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                // 超武战报
                String subTitleKey = temple.getBattleRecordSub16();
                builder.setSubTitleKey(subTitleKey);

                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayHero(enemyRole.getMainHero().getHeroId()));
            } else {
                // 防守地图建筑
                ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                String subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub7() : temple.getBattleRecordSub6();
                builder.setSubTitleKey(subTitleKey);

                // [%s]%s遭到了[%s]%s攻击
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()));
                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(enemyRole.getCardHead().getName()));
            }
        }
        return builder.build();
    }

    @Override
    protected QlogCncBattleReport getBattleRepFlow(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        QlogCncBattleReport battleRepFlow = new QlogCncBattleReport();
        ArmyEntity leaderArmy = getOwner().getInnerArmyComponent().getLeaderArmy();
        Map<Long, SceneObjBattleComponent.SimpleBattleEntityInfo> onceInBattleEntityCache = getOnceInBattleEntityCache();
        battleRepFlow.setDtEventTime(TimeUtils.now2String())
                .setBattleReportId(String.valueOf(recordAllProp.getRecordId()))
                .setBattleId(QlogUtils.transCollection2ArrayString(recordAllProp.getSingleRecordList().stream().map(BattleRecordOneProp::getBattleId).collect(Collectors.toList())))
                .setBattleReportTimeStart(TimeUtils.timeStampMs2String(recordAllProp.getStartMillis()))
                .setBattleReportTimeEnd(TimeUtils.timeStampMs2String(recordAllProp.getEndMillis()))
                .setBattleReportResult(BattleRepResult.getBattleResult(alive, anyEnemyAlive).ordinal())
                .setMainObjectType(BattleRepMainObjType.getMainObjType(onceInBattleEntityCache.size() > 1).ordinal())
                .setDefenceOrNot(BattleRepDefenceType.DEFENCE_CLAN_BUILDING.ordinal())
                .setMainObjectId(leaderArmy == null ? "" : String.valueOf(leaderArmy.getPlayerId()))
                .setEnemyId("")
                .setMainObjectBuildingId(String.valueOf(getOwner().getPartId()));
        return battleRepFlow;
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        // 战斗期间建筑内没有军队，不需要打battle log
        Map<Long, SimpleBattleEntityInfo> onceInBattleEntityCache = getOnceInBattleEntityCache();
        if (onceInBattleEntityCache.isEmpty()) {
            return null;
        }
        QlogCncBattle battleFlow = super.getBattleFlow(alive, isEnemyAlive, record);
        if (null == battleFlow) {
            return null;
        }
        // 一定是军团建筑驻防部队
        battleFlow.setDefenceOrNot(BattleRepDefenceType.DEFENCE_CLAN_BUILDING.ordinal());
        // 一定是防守方
        battleFlow.setAttackOrNot(BattleAttackType.DEFENCE.ordinal());
        // 设置主体建筑id
        battleFlow.setMainObjectBuildingID(String.valueOf(getOwner().getPartId()));
        // 设置战斗主体
        battleFlow.setMainObjectType(BattleRepMainObjType.getMainObjType(onceInBattleEntityCache.size() > 1).ordinal())
                .setMainObjectID(String.valueOf(getOwner().getPlayerId()));
        return battleFlow;
    }

    @Override
    public void afterGarrisonReplaced(BattleRecordAllProp recordAllProp) {
        super.afterGarrisonReplaced(recordAllProp);
        trySendRecordMail(getBattleRecordPlayerIds(), recordAllProp, getBattleRole().hasAnyAlive(), true);
    }

    @Override
    public long getRoleTypeId() {
        return getOwner().getProp().getTemplateId();
    }

    @Override
    public long getLeaderRoleId() {
        ArmyEntity leaderArmy = getOwner().getInnerArmyComponent().getLeaderArmy();
        if (leaderArmy != null) {
            return leaderArmy.getBattleComponent().getRoleId();
        } else {
            return getRoleId();
        }
    }

    @Override
    public boolean isEnemyPlayer(long actionPlayerClanId) {
        // 里面有军队
        if (getPlayerId() != 0) {
            return true;
        }
        // 不所属任何军团
        if (getClanId() <= 0) {
            return false;
        }
        // 军团是否一致
        return actionPlayerClanId != getClanId();
    }

    /**
     * 发送军团建筑的受击邮件
     */
    private void sendBeAttackedMailToOwnBuildingClan() {
        if (getOwner().getProp().getConstructInfo().getType() == MapBuildingType.MBT_NONE) {
            return;
        }
        long clanId = getOwner().getOwnerClanId();
        if (clanId <= 0) {
            LOGGER.info("clanId {} is wrong", clanId);
            return;
        }
        int attackAfterBuildMailId = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class).getAttackAfterBuildMail();
        getOwner().getBuildComponent().sendCommonRebuildMail(attackAfterBuildMailId, clanId, 0);
    }

    @Override
    public List<Long> getAllChildRoleIdList() {
        List<Long> ret = Lists.newArrayList();
        // 初始野怪部队，无人占领
        if (isNeutral()) {
            ret.add(getEntityId());
        }
        // 增援部队
        for (ArmyEntity inRallyArmy : getOwner().getInnerArmyComponent().getInnerArmyList()) {
            ret.add(inRallyArmy.getEntityId());
        }
        return ret;
    }

    @Override
    public void handleSoldierLoss(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        // 增援部队
        for (ArmyEntity inRallyArmy : getOwner().getInnerArmyComponent().getInnerArmyList()) {
            inRallyArmy.getBattleComponent().sendSevereWound2Hospital(lossDetail);
        }
    }

    @Override
    public boolean isRally() {
        return !getOwner().getInnerArmyComponent().getInnerArmyList().isEmpty();
    }

    @Override
    public int reduceDurability(int effectValue) {
        double reduceValue = getOwner().getBuildComponent().getCurHp() * effectValue / MathUtils.TEN_THOUSAND;
        int decNum = (int) MathUtils.ceilLong(reduceValue);
        if (decNum > 0) {
            getOwner().getBuildComponent().decHpBySkill(decNum);
        }
        LOGGER.info("reduceDurability {} effectValue={}, decNum={}", getOwner(), effectValue, decNum);
        return decNum;
    }

    @Override
    protected AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(getOwner().getPlayerId());
    }

    @Override
    public boolean isMustNtfType() {
        // 所有中立建筑为主体的飘字需要包装特殊协议
        return true;
    }
}

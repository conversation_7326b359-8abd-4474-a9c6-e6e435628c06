package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.MoveEndEvent;
import com.yorha.cnc.scene.event.MoveTargetChangeEvent;
import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.game.gen.prop.Int64ArmyArrowItemMapProp;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 小箭头管理
 *
 * <AUTHOR>
 */
public class SceneObjArrowComponent extends SceneObjComponent<SceneObjEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjArrowComponent.class);

    public SceneObjArrowComponent(SceneObjEntity owner) {
        super(owner);
    }

    private final Map<Long, EventListener> eventListener = new HashMap<>();

    public void addArrowItem(SceneObjEntity sceneObj) {
        Int64ArmyArrowItemMapProp arrowProp = getOwner().getArrowProp();
        if (arrowProp == null) {
            return;
        }
        if (arrowProp.containsKey(sceneObj.getEntityId())) {
            return;
        }
        arrowProp.addEmptyValue(sceneObj.getEntityId())
                .setClanId(sceneObj.getClanId()).getPoint()
                .setX(sceneObj.getCurPoint().getX())
                .setY(sceneObj.getCurPoint().getY());
        EventListener listener = sceneObj.getEventDispatcher().addMultiEventListenerRepeat(this::onTargetEventDispatch, DeleteEvent.class, ClanChangeEvent.class, MoveEndEvent.class, MoveTargetChangeEvent.class);
        eventListener.put(sceneObj.getEntityId(), listener);
        LOGGER.info("{} addArrowItem {}", getOwner(), sceneObj);
        if (eventListener.size() > ResHolder.getResService(ConstKVResService.class).getTemplate().getMarchingLineLimit()) {
            removeFirst();
        }
    }

    private void onTargetEventDispatch(IEvent event) {
        LOGGER.info("{} arrowComponent onArmyEventDispatch {}", getOwner(), event);
        if (event.equals(ClanChangeEvent.class)) {
            onArmyClanChange((ClanChangeEvent) event);
            return;
        }
        if (event.equals(MoveTargetChangeEvent.class)) {
            MoveTargetChangeEvent targetChangeEvent = (MoveTargetChangeEvent) event;
            if (targetChangeEvent.getNewTargetId() != getEntityId()) {
                removeArrowItem(targetChangeEvent.getEntityId());
            }
        } else {
            IEventWithEntityId e = (IEventWithEntityId) event;
            removeArrowItem(e.getEntityId());
        }
    }

    private void onArmyClanChange(ClanChangeEvent event) {
        long entityId = event.getEntityId();
        if (!getOwner().getArrowProp().containsKey(entityId)) {
            EventListener remove = eventListener.remove(entityId);
            if (remove != null) {
                remove.cancel();
            }
            // 沒了？？ 为啥呢
            LOGGER.error("{} onArmyClanChange {}", getOwner(), entityId);
            return;
        }
        getOwner().getArrowProp().get(entityId).setClanId(event.getClanId());
    }

    private void removeArrowItem(long armyId) {
        Int64ArmyArrowItemMapProp arrowProp = getOwner().getArrowProp();
        if (arrowProp == null) {
            return;
        }
        arrowProp.remove(armyId);
        EventListener remove = eventListener.remove(armyId);
        if (remove != null) {
            remove.cancel();
        }
        LOGGER.info("{} removeArrowItem {}", getOwner(), armyId);
    }

    private void removeFirst() {
        Iterator<Map.Entry<Long, EventListener>> iterator = eventListener.entrySet().iterator();
        if (iterator.hasNext()) {
            Map.Entry<Long, EventListener> next = iterator.next();
            iterator.remove();
            getOwner().getArrowProp().remove(next.getKey());
            next.getValue().cancel();
            LOGGER.info("{} removeArrowItem first {}", getOwner(), next.getKey());
        }
    }
}

package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;

/**
 * 建筑ai组件
 *
 * <AUTHOR>
 */
public class MapbuildingAiComponent extends SceneObjAiComponent {
    public MapbuildingAiComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public int getAiIndex() {
        return getOwner().getBuildingTemplate().getAiIndex();
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }
}

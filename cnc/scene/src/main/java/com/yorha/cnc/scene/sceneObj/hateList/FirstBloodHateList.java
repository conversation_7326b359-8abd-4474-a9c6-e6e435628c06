package com.yorha.cnc.scene.sceneObj.hateList;

import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.common.utils.time.SystemClock;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.constant.LogConstant.LOG_TYPE_AI;

/**
 * 一血优先仇恨列表
 * 一血对象死亡前，默认一血对象为最高仇恨对象
 * 一血对象死亡后，依据仇恨值高低，排需最高仇恨对象
 *
 * <AUTHOR>
 */
public class FirstBloodHateList extends AbstractHateList {
    private static final Logger LOGGER = LogManager.getLogger(LOG_TYPE_AI);
    private final Map<Long, HateInfo> hateMap;
    private long mostHateEntity;
    private long firstBloodTarget;
    private boolean isFirstBloodTargetDead;

    public FirstBloodHateList(SceneObjEntity owner) {
        super(owner);
        this.hateMap = new HashMap<>();
    }

    @Override
    public long getMostHateEntity() {
        if (!isFirstBloodTargetDead) {
            if (firstBloodTarget > 0) {
                return firstBloodTarget;
            }
        }
        return mostHateEntity;
    }


    @Override
    public Collection<Long> getHateEntities() {
        return Collections.unmodifiableSet(hateMap.keySet());
    }

    @Override
    public long getHate(long entityId) {
        return hateMap.containsKey(entityId) ? hateMap.get(entityId).getHate() : 0L;
    }

    @Override
    public HateInfo getHateInfo(long entityId) {
        return hateMap.get(entityId);
    }

    @Override
    public void addHate(long entityId, long hate) {
        if (getOwner().isDestroy()) {
            return;
        }
        if (hateMap.isEmpty()) {
            getOwner().tryRemoveTick(SceneTickReason.TICK_HATE);
            getOwner().addTick(SceneTickReason.TICK_HATE);
        }
        // 当前没有一血对象，本对象为一血对象，一血对象的仇恨不可以过期
        final boolean hasFirstBloodTarget = hasFirstBloodTarget();
        long totalHate = hateMap.computeIfAbsent(entityId, k -> new HateInfo()).addHate(hate, !hasFirstBloodTarget);
        // 设置一血仇恨目标
        if (!hasFirstBloodTarget) {
            firstBloodTarget = entityId;
            mostHateEntity = firstBloodTarget;
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
            if (getOwner().getAiComponent().isDebugAble()) {
                LOGGER.info("{}, set first blood target {}", getOwner().getAiComponent().getLogHead(), entityId);
            }
            return;
        }
        // 首次拥有仇恨目标对象
        if (mostHateEntity == 0) {
            this.mostHateEntity = entityId;
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
            return;
        }
        // 仇恨值最高的对象变化
        if (entityId != mostHateEntity && totalHate > getHate(mostHateEntity)) {
            this.mostHateEntity = entityId;
            // 一血仇恨目标没死，始终进攻一血目标
            if (!isFirstBloodTargetDead) {
                return;
            }
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
        }
    }

    private boolean hasFirstBloodTarget() {
        return firstBloodTarget > 0;
    }


    @Override
    public void clearHate(long entityId) {
        HateInfo hateInfo = hateMap.get(entityId);
        if (hateInfo == null) {
            return;
        }
        // 一血对象的仇恨也是不可清除的
        if (!hateInfo.isCanRemove()) {
            return;
        }
        hateMap.remove(entityId);
        if (entityId == mostHateEntity) {
            this.mostHateEntity = 0L;
            changeMostHateEntity(hateMap);
        }
    }

    public void forceClearHate(long entityId) {
        HateInfo hateInfo = hateMap.get(entityId);
        if (hateInfo == null) {
            return;
        }
        hateMap.remove(entityId);
        if (entityId == mostHateEntity) {
            this.mostHateEntity = 0L;
            changeMostHateEntity(hateMap);
        }
    }

    @Override
    public void clear() {
        if (!hasFirstBloodTarget() || isFirstBloodTargetDead) {
            this.mostHateEntity = 0L;
        }
        Iterator<Map.Entry<Long, HateInfo>> iterator = hateMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, HateInfo> entry = iterator.next();
            if (!entry.getValue().isCanRemove()) {
                continue;
            }
            iterator.remove();
        }
    }

    @Override
    public void onTick() {
        // 仇恨值过期
        if (hateMap.isEmpty()) {
            if (!hasFirstBloodTarget() || isFirstBloodTargetDead) {
                getOwner().removeTick(SceneTickReason.TICK_HATE);
                return;
            }
        }
        final SceneObjEntity firstBloodObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(firstBloodTarget);
        if (firstBloodObj == null) {
            isFirstBloodTargetDead = true;
        } else {
            isFirstBloodTargetDead = firstBloodObj.isDestroy();
        }

        if (isFirstBloodTargetDead) {
            forceClearHate(firstBloodTarget);
        }

        Set<Long> expireSet = new HashSet<>();
        hateMap.forEach((id, hateInfo) ->

        {
            if (SystemClock.now() >= TimeUnit.SECONDS.toMillis(getHateExpire()) + hateInfo.getLastHateTime()) {
                expireSet.add(id);
            }
        });
        if (CollectionUtils.isNotEmpty(expireSet)) {
            // 清除过期仇恨
            expireSet.forEach(hateMap::remove);
            // 重新计算仇恨值最高的玩家
            if (expireSet.contains(mostHateEntity)) {
                mostHateEntity = 0L;
                changeMostHateEntity(hateMap);
            }
        }
        if (getOwner().getAiComponent().isDebugAble()) {
            LOGGER.info("{}, onTick hateMap {}", getOwner().getAiComponent().getLogHead(), hateMap.entrySet());
        }
        // 失去一血目标
        if (isFirstBloodTargetDead && (mostHateEntity > 0)) {
            firstBloodTarget = mostHateEntity;
            isFirstBloodTargetDead = false;
            if (getOwner().getAiComponent().isDebugAble()) {
                LOGGER.info("{}, tick set first blood target {}", getOwner().getAiComponent().getLogHead(), firstBloodTarget);
            }
        }
    }

    @Override
    public void addFixHate(long entityId, long hate) {
        LOGGER.error("FirstBloodHateList addFixHate not impl {} {} {}", getOwner(), entityId, hate);
    }

    @Override
    public void setLockHateTarget(long lockHateTarget) {
        LOGGER.error("FirstBloodHateList setLockHateTarget not impl {} {}", getOwner(), lockHateTarget);
    }

    private void changeMostHateEntity(Map<Long, HateInfo> hateMap) {
        if (MapUtils.isEmpty(hateMap)) {
            return;
        }
        hateMap.entrySet().stream()
                .max(Comparator.comparingLong(o -> o.getValue().getHate()))
                .ifPresent(entry -> this.mostHateEntity = entry.getKey());
    }


}

package com.yorha.cnc.scene.areaSkill.component;

import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.areaSkill.AreaSkillEntity;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.BattleProp;
import com.yorha.game.gen.prop.BattleRecordAllProp;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import qlog.flow.QlogCncBattle;

/**
 * <AUTHOR>
 * @date 2023/4/12
 */
public class AreaSkillBattleComponent extends SceneObjBattleComponent {
    public AreaSkillBattleComponent(AreaSkillEntity owner, CommonEnum.SceneObjType sceneObjType, BattleRole master) {
        super(owner, sceneObjType, master);
    }

    @Override
    public void init() {
        super.init();
        getBattleRole().buildHero();
        getBattleRole().initSoldierByTroop();
        getBattleRole().initState();
    }

    @Override
    public void postInit() {
        super.postInit();
        getOwner().addTick(SceneTickReason.TICK_LIFE);
    }

    public void onAreaSkillCreated() {
        // 激活BattleRole
        tryActivateRole("areaSkill");
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {

    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {
        Point curPoint = getOwner().getCurPoint();
        roleRecord.setLocation(curPoint.getX(), curPoint.getY(), this.getOwner().getScene().getMapIdForPoint(), this.getOwner().getScene().getMapType());
        roleRecord.setClanName(getOwner().getClanSName());
        roleRecord.setCardHead(getOwner().getCardHead());
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        roleRecord.addMember(buildRoleMemberRecord());
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getEntityId())
                .setPlayerId(getOwner().getPlayerId())
                .setClanName(getOwner().getClanSName())
                .setCardHead(getOwner().getCardHead());
        HeroProp mainHeroProp = getBattleRole().getMainHero() != null ? getBattleRole().getMainHero().getHeroProp() : new HeroProp();
        HeroProp deputyHeroProp = getBattleRole().getDeputyHero() != null ? getBattleRole().getDeputyHero().getHeroProp() : new HeroProp();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    @Override
    public AreaSkillEntity getOwner() {
        return (AreaSkillEntity) super.getOwner();
    }

    @Override
    public boolean canAction() {
        return false;
    }

    @Override
    public boolean canHurtBack() {
        return false;
    }

    @Override
    public boolean needRemoveRoleAfterBattleEnd() {
        // 结束战斗不移除Role，直到Entity自行销毁
        return getOwner().isDestroy();
    }

    @Override
    public void sendOrdinaryAttack(DamageResult damageResult) {
    }

    @Override
    public boolean canBeSearchSelect() {
        return false;
    }

    @Override
    public long getRoleTypeId() {
        BattleRole ownerRole = getBattleRole().getMasterRole();
        if (ownerRole != null) {
            return ownerRole.getAdapter().getRoleTypeId();
        }
        return super.getRoleTypeId();
    }

    @Override
    public boolean isEnemyPlayer(long actionPlayerClanId) {
        return false;
    }

    @Override
    public CommonEnum.DamageRatioTypeEnum getDamageRatioType() {
        BattleRole ownerRole = getBattleRole().getMasterRole();
        if (ownerRole != null) {
            return ownerRole.getAdapter().getDamageRatioType();
        }
        return super.getDamageRatioType();
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }

    @Override
    public boolean isMustNtfType() {
        // 技能、普攻必定下发
        return true;
    }
}


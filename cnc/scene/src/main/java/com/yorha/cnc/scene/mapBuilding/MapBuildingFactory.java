package com.yorha.cnc.scene.mapBuilding;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.outbuilding.OutbuildingBuilder;
import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.MapBuildingProp;
import com.yorha.game.gen.prop.OutbuildingProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.OccupyState;
import com.yorha.proto.CommonEnum.OutbuildingState;
import com.yorha.proto.EntityAttrDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.InitializationWildBuildingTemplate;
import res.template.MapBuildingTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 地图建筑创建工厂
 */
public class MapBuildingFactory {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingFactory.class);

    /**
     * 创建地图建筑
     */
    public static void createMapBuilding(SceneEntity scene, RegionalAreaSettingTemplate template) {
        int partId = template.getId();
        int buildTemplateId = template.getBuildingId();
        MapBuildingTemplate template1 = ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, buildTemplateId);
        TerritoryBuildingTemplate template2 = ResHolder.getInstance().getValueFromMap(TerritoryBuildingTemplate.class, buildTemplateId);
        // 构建里程碑所需要的数据
        List<Integer> param = new ArrayList<>();
        param.add(template1.getType().getNumber());
        param.add(template2.getLevel());
        // 构建prop
        MapBuildingProp prop = new MapBuildingProp();
        // 计算状态和结束时间
        calculateState(scene, template, param, prop);
        prop.setPartId(partId).setTemplateId(buildTemplateId).getPoint().setX(template.getPosX()).setY(template.getPosY());
        prop.unMarkAll();
        // 构建entity
        MapBuildingBuilder builder = new MapBuildingBuilder(scene, partId, prop);
        MapBuildingEntity buildingEntity = new MapBuildingEntity(builder, false);
        buildingEntity.addIntoScene();
        scene.getBuildingMgrComponent().addMapBuilding(partId, buildingEntity);
        buildingEntity.getStageMgrComponent().onCreate();
        // 创建附属建筑
        createOutbuilding(scene, buildTemplateId, partId, template.getPosX(), template.getPosY(), false);
    }

    private static void calculateState(SceneEntity scene, RegionalAreaSettingTemplate template, List<Integer> param, MapBuildingProp prop) {
        if (scene.isBigScene()) {
            long openTsMs = scene.getBigScene().getMileStoneOrNullComponent().getOpenTsMsWithMapBuildParam(param);
            if (openTsMs == 0 || openTsMs > SystemClock.now()) {
                prop.getOccupyinfo().setState(OccupyState.TOS_CLOSE).setStateEndTsMs(openTsMs);
                return;
            }
            prop.getOccupyinfo().setState(OccupyState.TOS_NEUTRAL).setStateEndTsMs(0);
            return;
        }
        // 普通据点直接开放
        if (template.getAreaType() == CommonEnum.MapAreaType.TERRITORY) {
            prop.getOccupyinfo().setState(OccupyState.TOS_NEUTRAL).setStateEndTsMs(0);
            return;
        }

        // 看下开放时间
        MileStoneMgrComponent mileStoneOrNullComponent = scene.getMileStoneOrNullComponent();
        long openTsMs = mileStoneOrNullComponent == null ? 0 : mileStoneOrNullComponent.getOpenTsMsWithMapBuildParam(param);
        // 过开放时间了
        if (openTsMs != 0 && openTsMs <= SystemClock.now()) {
            prop.getOccupyinfo().setState(OccupyState.TOS_NEUTRAL).setStateEndTsMs(0);
            return;
        }
        // 有半开放阶段的关卡的  要看下半开放时间
        if (template.getAreaType() == CommonEnum.MapAreaType.CROSSING) {
            MapSubdivisionDataService resService = ResHolder.getResService(MapSubdivisionDataService.class);
            if (resService.isOutRegion2MiddleRegion(scene.getStoryId(), template)) {
                // 有开放时间  那就是半开放
                if (openTsMs != 0) {
                    prop.getOccupyinfo().setState(OccupyState.TOS_SEMI_OPEN).setStateEndTsMs(openTsMs);
                    return;
                }
                long halfOpen = mileStoneOrNullComponent == null ? 0 : mileStoneOrNullComponent.getOpenTsMsWithMapBuildHalf();
                // 还没半开放
                if (halfOpen == 0 || halfOpen > SystemClock.now()) {
                    prop.getOccupyinfo().setState(OccupyState.TOS_CLOSE).setStateEndTsMs(halfOpen);
                    return;
                }
                // 已经过了半开放时间了
                prop.getOccupyinfo().setState(OccupyState.TOS_SEMI_OPEN).setStateEndTsMs(0);
                return;
            }
        }
        prop.getOccupyinfo().setState(OccupyState.TOS_CLOSE).setStateEndTsMs(openTsMs);
    }

    public static void restoreMapBuilding(SceneEntity scene, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        MapBuildingProp prop = MapBuildingProp.of(fullAttr.getMapBuildingAttr(), changedAttr.getMapBuildingAttr());
        int partId = prop.getPartId();
        RegionalAreaSettingTemplate template = scene.getMapTemplateDataItem().findValueFromMap(RegionalAreaSettingTemplate.class, partId);
        if (template == null) {
            WechatLog.error("part delete id: {}", partId);
            return;
        }
        int oldTemplateId = prop.getTemplateId();
        if (GameLogicConstants.isRebuildBuilding(prop.getConstructInfo().getType())) {
            oldTemplateId = prop.getConstructInfo().getBeforeRebuildTemplateId();
        }
        if (template.getBuildingId() != oldTemplateId) {
            WechatLog.error("part building change id: {}. old/new {}/{}", partId, oldTemplateId, template.getBuildingId());
            // 还是让他恢复了
        }
        prop.getPoint().setX(template.getPosX()).setY(template.getPosY());
        // 构建entity
        MapBuildingBuilder builder = new MapBuildingBuilder(scene, fullAttr.getEntityId(), prop);
        MapBuildingEntity buildingEntity = new MapBuildingEntity(builder, true);
        buildingEntity.addIntoScene();
        scene.getBuildingMgrComponent().addMapBuilding(partId, buildingEntity);
        LOGGER.debug("{} restore success", buildingEntity);
        // 后面的是保底。。。
        boolean isOccupied = isOccupied(buildingEntity);
        createOutbuilding(scene, buildingEntity.getTemplateId(), partId, template.getPosX(), template.getPosY(), isOccupied);
    }

    public static boolean isOccupied(MapBuildingEntity buildingEntity) {
        if (buildingEntity.getClanId() != 0) {
            return true;
        }
        return buildingEntity.getProp().getOccupyinfo().getFisrtOwnTsMs() != 0;
    }

    public static void createOutbuilding(SceneEntity scene, int buildTemplateId, int partId, int x, int y, boolean isOccupied) {
        List<InitializationWildBuildingTemplate> outBuildingTemplate = ResHolder.getResService(MapSubdivisionDataService.class).getOutBuildingTemplate(buildTemplateId);
        if (outBuildingTemplate == null) {
            return;
        }
        for (InitializationWildBuildingTemplate template : outBuildingTemplate) {
            OutbuildingProp prop = new OutbuildingProp();
            if (isOccupied) {
                prop.setState(OutbuildingState.OS_OCCUPIED);
            } else {
                prop.setState(OutbuildingState.OS_PROTECT);
            }
            prop.setPartId(partId).setTemplateId(template.getTemplateId())
                    .getPoint().setX(x + template.getCoordinateX()).setY(y + template.getCoordinateY());
            // 构建entity
            OutbuildingBuilder builder = new OutbuildingBuilder(scene, scene.ownerActor().nextId(), prop);
            OutbuildingEntity outbuildingEntity = new OutbuildingEntity(builder);
            prop.unMarkAll();
            outbuildingEntity.addIntoScene();
        }
    }
}

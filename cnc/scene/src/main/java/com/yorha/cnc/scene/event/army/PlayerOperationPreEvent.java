package com.yorha.cnc.scene.event.army;

import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.proto.StructPlayer;

/**
 * <AUTHOR>
 * <p>
 * 玩家行军指令执行前
 */
public class PlayerOperationPreEvent extends IEvent {
    private final StructPlayer.ArmyActionInfo armyActionInfo;

    public PlayerOperationPreEvent(StructPlayer.ArmyActionInfo armyActionInfo) {
        this.armyActionInfo = armyActionInfo;
    }

    public StructPlayer.ArmyActionInfo getArmyActionInfo() {
        return armyActionInfo;
    }
}

package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;

import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 向master放指定技能
 * <AUTHOR>
 */
public class FireSkillToMasterTrigger extends AiTrigger {
    public FireSkillToMasterTrigger(AiStateTriggerTemplate template) {
        super(template);
    }

    private List<Integer> skills;

    @Override
    protected void parse(List<IntPairType> param) {
        skills = new ArrayList<>();
        for(IntPairType pair : param) {
            skills.add(pair.getValue());
        }
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        Object record = owner.getAiComponent().tryGetRecord(CommonEnum.AiRecordType.ART_MASTER);
        BattleHero mainHero = owner.getBattleComponent().getMainHero();
        return (record != null) && (mainHero != null);
    }

    @Override
    protected void doTrigger(SceneObjEntity owner) {
        long masterId = (long)owner.getAiComponent().tryGetRecord(CommonEnum.AiRecordType.ART_MASTER);
        BattleHero mainHero = owner.getBattleComponent().getMainHero();
        for (int skillId : skills) {
            FireSkillEvent fireSkillEvent = new FireSkillEvent.Builder()
                    .setHeroId(mainHero.getId())
                    .setTargetId(masterId)
                    .setSkillId(skillId)
                    .build();
            owner.getEventDispatcher().dispatch(fireSkillEvent);
        }
    }

    @Override
    protected String getTriggerParam() {
        return skills.toString();
    }

    @Override
    protected String getTriggerName() {
        return "FireSkillToMasterTrigger";
    }
}

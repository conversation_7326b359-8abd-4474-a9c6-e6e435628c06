package com.yorha.cnc.scene.sceneclan.rally;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.army.PointChangeEvent;
import com.yorha.cnc.scene.event.city.CityAscendEvent;
import com.yorha.cnc.scene.event.city.CityMoveEvent;
import com.yorha.cnc.scene.event.ievent.IEventWithClanId;
import com.yorha.cnc.scene.event.mapbuilding.ChangeOccupierEvent;
import com.yorha.cnc.scene.event.mapbuilding.ChangeOwnerEvent;
import com.yorha.cnc.scene.event.mapbuilding.LoseAdjoinPartEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.event.player.PlayerNameChangeEvent;
import com.yorha.cnc.scene.event.player.PlayerPicChangeEvent;
import com.yorha.cnc.scene.event.player.PlayerPicFrameChangeEvent;
import com.yorha.cnc.scene.event.rally.BeRallyDismissEvent;
import com.yorha.cnc.scene.event.rally.BeRallyEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.component.RallyArmyMgrComponent;
import com.yorha.cnc.scene.sceneclan.rally.component.RallyStateComponent;
import com.yorha.cnc.scene.sceneclan.rally.component.RallyTimerComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int64RallyArmyInfoMapProp;
import com.yorha.game.gen.prop.RallyArmyInfoProp;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import com.yorha.proto.User;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SoldierTypeTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 集结
 *
 * <AUTHOR>
 */
public class RallyEntity extends AbstractEntity {
    private static final Logger LOGGER = LogManager.getLogger(RallyEntity.class);
    // 集结本身数据 和集结内的部队数据是分开存的 注意!!!
    private final RallyInfoProp rallyInfoProp;
    private final Int64RallyArmyInfoMapProp rallyArmyProp;

    private final SceneEntity sceneEntityRef;
    private final SceneClanEntity organizerSceneClan;
    private final RallyStateComponent stateComponent;
    private final RallyArmyMgrComponent armyMgrComponent;
    private final RallyTimerComponent timerComponent;
    /**
     * 集结目标消失监听器 、集结目标移动监听器、集结目标名片变化监听器、发起者名片变化监听器
     */
    private final EventListener targetLoseEventListener;
    private final EventListener targetMoveEventListener;
    private EventListener targetInfoChangeEventListener;
    private EventListener organizerInfoChangeEventListener;
    /**
     * 解散标签
     */
    private boolean isDismissed;
    /**
     * 消费的体力值
     */
    private final Map<Long, Integer> costEnergyMap = Maps.newHashMap();

    public RallyEntity(SceneEntity scene, SceneClanEntity sceneClanEntity, RallyInfoProp rallyInfoProp, ArmyEntity leader, SceneObjEntity targetEntity, int costEnergy) {
        super(rallyInfoProp.getRallyId());
        this.sceneEntityRef = scene;
        this.organizerSceneClan = sceneClanEntity;
        this.rallyInfoProp = rallyInfoProp;
        this.rallyArmyProp = new RallyInfoProp().getRallyArmyInfoMap();
        this.stateComponent = new RallyStateComponent(this);
        this.armyMgrComponent = new RallyArmyMgrComponent(this, leader, costEnergy);
        this.timerComponent = new RallyTimerComponent(this);
        this.isDismissed = false;
        initAllComponents();
        targetLoseEventListener = targetEntity.getEventDispatcher().addMultiEventListenerRepeat(
                this::onTargetLose,
                DieEvent.class,
                DeleteEvent.class,
                ClanChangeEvent.class,
                CityMoveEvent.class,
                CityAscendEvent.class,
                ChangeOccupierEvent.class,
                ChangeOwnerEvent.class,
                LoseAdjoinPartEvent.class);
        // 监听目标位置变化，更新数据
        targetMoveEventListener = targetEntity.getEventDispatcher().addEventListenerRepeat((PointChangeEvent e) -> {
            getProp().getTargetPos().setX(e.getCurPoint().getX()).setY(e.getCurPoint().getY());
        }, PointChangeEvent.class);
        // 抛出被集结的事件
        targetEntity.getEventDispatcher().dispatch(new BeRallyEvent(getEntityId(), getProp().getOrganizerClanId()));
        if (scene.isMainScene()) {
            organizerInfoChangeEventListener = leader.getEventDispatcher().addMultiEventListenerRepeat(
                    this::updateOrganizerInfoChange,
                    PlayerNameChangeEvent.class, PlayerPicFrameChangeEvent.class, PlayerPicChangeEvent.class
            );
            if (targetEntity.getEntityType() == EntityType.ET_Army || targetEntity.getEntityType() == EntityType.ET_City) {
                targetInfoChangeEventListener = targetEntity.getEventDispatcher().addMultiEventListenerRepeat(
                        this::updateTargetInfoChange,
                        PlayerNameChangeEvent.class, PlayerPicFrameChangeEvent.class, PlayerPicChangeEvent.class
                );
            }

        }
    }

    public int getCostEnergy(long armyId) {
        return costEnergyMap.getOrDefault(armyId, 0);
    }

    public void putCostEnergy(long armyId, int costEnergy) {
        costEnergyMap.put(armyId, costEnergy);
    }

    /**
     * 集结目标没了  死亡/迁城/进入保护期/切换占领者   含联盟变更
     */
    private void onTargetLose(IEvent e) {
        if (e.equals(ClanChangeEvent.class)) {
            ClanChangeEvent event = (ClanChangeEvent) e;
            // 同盟 不能打了
            if (organizerSceneClan != null && event.getClanId() == organizerSceneClan.getEntityId()) {
                dismiss(RallyDismissReason.RDR_TARGET_CANT_ATTACK);
                return;
            }
            long oldClanId = getProp().getTargetClanId();
            getProp().setTargetClanId(event.getClanId());
            if (oldClanId != 0) {
                if (this.getSceneEntity().isMainScene()) {
                    SceneClanEntity sceneClan = getSceneEntity().getClanMgrComponent().getSceneClan(oldClanId);
                    if (sceneClan != null) {
                        sceneClan.getRallyComponent().onRemoveBeRally(this);
                    }
                }
                long playerId = getSceneEntity().getObjMgrComponent().getSceneObjEntity(getProp().getTargetId()).getPlayerId();
                AbstractScenePlayerEntity scenePlayer = getSceneEntity().getPlayerMgrComponent().getScenePlayer(playerId);
                User.RallyNtfMsg.Builder builder = User.RallyNtfMsg.newBuilder().setIsExist(false);
                scenePlayer.sendMsgToClient(MsgType.RALLYNTFMSG, builder.build());
            }
            if (event.getClanId() != 0) {
                if (this.getSceneEntity().isMainScene()) {
                    SceneClanEntity sceneClan = getSceneEntity().getClanMgrComponent().getSceneClan(event.getClanId());
                    if (sceneClan != null) {
                        sceneClan.getRallyComponent().onNewBeRally(this);
                    }
                }
            }
            return;
        }
        // 失去邻接地块
        if (e.equals(LoseAdjoinPartEvent.class)) {
            IEventWithClanId event = (IEventWithClanId) e;
            if (event.getClanId() == organizerSceneClan.getEntityId()) {
                dismiss(RallyDismissReason.RDR_TARGET_CANT_ATTACK);
            }
            return;
        }
        if (e.equals(DieEvent.class)) {
            // 如果集结的目标死亡了，但我不在和他战斗，就需要提前解散集结
            ArmyEntity rallyArmy = getRallyArmy();
            if (rallyArmy != null) {
                boolean has = rallyArmy.getBattleComponent().getBattleRole().hasRelationWith(getProp().getTargetId());
                if (!has) {
                    dismiss(RallyDismissReason.RDR_TARGET_LOSE);
                }
            }
            return;
        }
        // 被集结的目标可以在集结的准备、等待、行军阶段进行迁城。在准备期和等待期迁城集结正常进行，集结界面中玩家的坐标会发生变化。在行军阶段迁城会直接将进攻方的集结取消。
        if (e.equals(CityMoveEvent.class)) {
            if (getRallyArmy() == null) {
                return;
            }
        }

        LOGGER.info("{} onRallyTargetLose reason:{}", this, e);
        dismiss(RallyDismissReason.RDR_TARGET_LOSE);
    }

    /**
     * 开始
     */
    public void start(int waitTime) {
        if (organizerSceneClan != null) {
            organizerSceneClan.getRallyComponent().onNewRally(this);
        }
        // 被攻击方有联盟 加入下
        if (getProp().getTargetClanId() != 0 && this.getSceneEntity().isMainScene()) {
            SceneClanEntity sceneClan = getSceneEntity().getClanMgrComponent().getSceneClan(getProp().getTargetClanId());
            if (sceneClan != null) {
                sceneClan.getRallyComponent().onNewBeRally(this);
            } else {
                LOGGER.error("{} cant find target SceneClan. {}", this, getProp().getTargetClanId());
            }
        }
        getStateComponent().start(waitTime);
        LOGGER.info("{} start", this);

    }

    /**
     * 取消集结
     */
    public void cancel(long playerId) {
        LOGGER.info("{} cancel. playerId: {}", this, playerId);
        // 只有发起者可以取消
        if (playerId != getProp().getOrganizerId()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        dismiss(RallyDismissReason.RDR_CANCEL);
    }

    /**
     * 集结部队死亡
     */
    public void onRallyArmyDie() {
        // 可能因为最后一个人撤出导致先触发人数不够的解散 然后再挂了又触发了
        if (isDismissed) {
            LOGGER.info("{} die and rally is dismiss", this);
            return;
        }
        LOGGER.info("{} die", this);
        dismiss(RallyDismissReason.RDR_RETREAT);
    }

    /**
     * 集结部队击杀目标对象
     */
    public void onRallyKillTarget() {
        LOGGER.info("{} kill target", this);
        dismiss(RallyDismissReason.RDR_BATTLE_END);
    }

    public boolean checkDismissed() {
        return isDismissed;
    }

    /**
     * 集结解散
     * 触发条件：集结战胜目标 / 集结被杀光士兵 / 主动解散 / 没人解散
     */
    public void dismiss(RallyDismissReason reason) {
        if (isDismissed) {
            LOGGER.error("{} dismiss repeated. reason: {}", this, reason.name());
            return;
        }
        this.isDismissed = true;
        LOGGER.info("{} dismiss. reason: {}", this, reason.name());
        cancelAllEventListener();
        SceneObjEntity targetEntity = getSceneEntity().getObjMgrComponent().getSceneObjEntity(getProp().getTargetId());
        try {
            if (getArmyMgrComponent().getRallyArmy() != null) {
                // by awei: 这里是为了处理集结行军打架的时候，车头离开联盟了，会导致集结行军找不到InnerArmyList，所以先在这里结束战斗
                getArmyMgrComponent().getRallyArmy().getBattleComponent().forceEndAllBattle();
            }
            getStateComponent().dismiss();
            getArmyMgrComponent().returnRallyAllArmy(reason);
            if (getSceneEntity().isMainScene()) {
                // 非正常结束 发跑马灯
                if (reason != RallyDismissReason.RDR_BATTLE_END && reason != RallyDismissReason.RDR_RETREAT && reason != RallyDismissReason.RDR_OCCUPY_TARGET) {
                    // 给己方发
                    int attackMarqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(CommonEnum.MarqueeType.CANCEL_GATHER_2);
                    if (reason == RallyDismissReason.RDR_CLAN_DISMISS) { // 联盟没了导致的解散给自己发
                        sendMarquee(attackMarqueeId, MsgHelper.buildRallyCancelMarqueePbMsg(getProp().getOrganizerCardHead().getName()), 0, getProp().getOrganizerId());
                    } else if (reason == RallyDismissReason.RDR_QUIT_CLAN) { // 给发起者联盟发、给自己发
                        sendMarquee(attackMarqueeId, MsgHelper.buildRallyCancelMarqueePbMsg(getProp().getOrganizerCardHead().getName()), getProp().getOrganizerClanId(), 0);
                        sendMarquee(attackMarqueeId, MsgHelper.buildRallyCancelMarqueePbMsg(getProp().getOrganizerCardHead().getName()), 0, getProp().getOrganizerId());
                    } else {// 给发起者联盟发
                        sendMarquee(attackMarqueeId, MsgHelper.buildRallyCancelMarqueePbMsg(getProp().getOrganizerCardHead().getName()), getProp().getOrganizerClanId(), 0);
                    }
                    // 给敌方发
                    int defenderMarqueeId = ResHolder.getResService(MarqueeResService.class).getMarqueeId(CommonEnum.MarqueeType.CANCEL_GATHER);
                    if (targetEntity != null) {
                        sendMarquee(defenderMarqueeId, MsgHelper.buildRallyCancelMarqueePbMsg(getProp().getOrganizerClanShortName()), targetEntity.getClanId(), targetEntity.getPlayerId());
                    }
                }
            }

        } catch (Exception e) {
            WechatLog.error("{} dismiss error", this, e);
        } finally {
            // 抛出集结解散的事件
            if (targetEntity != null) {
                targetEntity.getEventDispatcher().dispatch(new BeRallyDismissEvent(getEntityId(), getProp().getOrganizerClanId(), reason, getProp().getOrganizerId()));
            }
            // 删除集结
            deleteObj();
        }
    }

    private void cancelAllEventListener() {
        targetLoseEventListener.cancel();
        targetMoveEventListener.cancel();
        if (organizerInfoChangeEventListener != null) {
            organizerInfoChangeEventListener.cancel();
            organizerInfoChangeEventListener = null;
        }
        if (targetInfoChangeEventListener != null) {
            targetInfoChangeEventListener.cancel();
            targetInfoChangeEventListener = null;
        }
    }

    private void sendMarquee(int marqueeId, StructPB.DisplayDataPB params, long clanId, long playerId) {
        getSceneEntity().getMarqueeComponent().sendClanOrPlayerMarquee(marqueeId, params, clanId, playerId);
    }

    /**
     * 获取加入集结时的移动目标entity
     */
    public SceneObjEntity getMoveTargetEntity() {
        ArmyEntity rallyArmy = getRallyArmy();
        if (rallyArmy != null) {
            return rallyArmy;
        }
        return getLeaderArmy().getScenePlayer().getMainCity();
    }

    /**
     * 设置推荐兵种
     */
    public void setRecommendSoldierType(long playerId, List<Integer> soldierType) {
        LOGGER.info("{} setRecommendSoldierType: {}", this, soldierType);
        if (playerId != getProp().getOrganizerId()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        getProp().getRecommendSoldierTypeList().clear();
        getProp().getRecommendSoldierTypeList().addAll(soldierType);
    }

    // ---------------------------------------- component ----------------------------------------

    public RallyStateComponent getStateComponent() {
        return stateComponent;
    }

    public RallyArmyMgrComponent getArmyMgrComponent() {
        return armyMgrComponent;
    }

    public RallyTimerComponent getTimerComponent() {
        return timerComponent;
    }

    @Override
    public EntityType getEntityType() {
        return null;
    }

    @Override
    public SceneActor ownerActor() {
        return sceneEntityRef.ownerActor();
    }

    public RallyInfoProp getProp() {
        return rallyInfoProp;
    }

    public Int64RallyArmyInfoMapProp getRallyArmyProp() {
        return rallyArmyProp;
    }

    @Override
    public void deleteObj() {
        if (getSceneEntity().isMainScene()) {
            if (organizerSceneClan != null) {
                organizerSceneClan.getRallyComponent().onRemoveRally(this);
            }
            if (getProp().getTargetClanId() != 0) {
                SceneClanEntity sceneClan = getSceneEntity().getClanMgrComponent().getSceneClan(getProp().getTargetClanId());
                if (sceneClan != null) {
                    sceneClan.getRallyComponent().onRemoveBeRally(this);
                }
            }
        } else {
            long organizerId = getProp().getOrganizerId();
            AbstractScenePlayerEntity scenePlayer = getSceneEntity().getPlayerMgrComponent().getScenePlayer(organizerId);
            scenePlayer.getRallyComponent().onRallyDelete(this);
        }
        super.deleteObj();
    }

    public ArmyEntity getLeaderArmy() {
        return getArmyMgrComponent().getLeaderArmy();
    }

    public ArmyEntity getRallyArmy() {
        return getArmyMgrComponent().getRallyArmy();
    }

    public SceneEntity getSceneEntity() {
        return sceneEntityRef;
    }

    /**
     * 获取联盟战阵面板显示的进攻集结数据
     */
    public RallyInfoPB getRallyInfoPb() {
        return getRallyInfoFullBuilder().build();
    }

    public RallyInfoPB getRallySimpleInfoPb() {
        RallyInfoPB.Builder builder = getRallyInfoBuilder();
        List<Integer> type = new ArrayList<>();
        Map<Integer, SoldierTypeTemplate> map = ResHolder.getInstance().getMap(SoldierTypeTemplate.class);
        for (RallyArmyInfoProp infoProp : getRallyArmyProp().values()) {
            for (SoldierProp soldierProp : infoProp.getTroop().getTroop().values()) {
                int num = soldierProp.getNum() - soldierProp.getDeadNum() - soldierProp.getSevereWoundNum() - soldierProp.getSlightWoundNum();
                if (num <= 0) {
                    continue;
                }
                int soldierType = map.get(soldierProp.getSoldierId()).getSoldierType();
                if (type.contains(soldierType)) {
                    continue;
                }
                type.add(soldierType);
                // 已经满了 不用再遍历了
                if (type.size() >= 4) {
                    break;
                }
            }
            // 已经满了 不用再遍历了
            if (type.size() >= 4) {
                break;
            }
        }
        builder.getCurSoldierTypeListBuilder().addAllDatas(type);
        return builder.build();
    }

    public RallyInfoPB getAssistSimpleInfoPb() {
        RallyInfoPB.Builder builder = getRallyInfoBuilder().clearRecommendSoldierTypeList();
        SceneObjEntity sceneObj = getSceneEntity().getObjMgrComponent().getSceneObjEntity(getProp().getTargetId());
        if (sceneObj == null) {
            LOGGER.error("getAssistSimpleInfoPb target is null {} {}", this, getProp().getTargetId());
            return null;
        }
        switch (sceneObj.getEntityType()) {
            // army需要特殊处理
            case ET_Army:
                // 自己盟的集结部队被打
                ArmyEntity army = (ArmyEntity) sceneObj;
                RallyEntity rallyEntity = army.getRallyEntity();
                if (rallyEntity == null) {
                    LOGGER.error("getAssistSimpleInfoPb rally is null {} {}", this, army.getRallyComponent().getCurRallyId());
                    return null;
                }
                builder.setCurSoldierNum(rallyEntity.getProp().getCurSoldierNum())
                        .setMaxSoldierNum(rallyEntity.getProp().getMaxSoldierNum())
                        .setBeRallyId(rallyEntity.getEntityId());
                break;
            default:
                if (sceneObj.getInnerArmyComponent() == null) {
                    LOGGER.error("getAssistSimpleInfoPb {} no innerArmyComponent!!", this);
                    return null;
                }
                sceneObj.getInnerArmyComponent().copyToRallyInfo(builder, false);

        }
        return builder.build();
    }

    /**
     * 获取联盟战争面板显示的被进攻的援助数据
     */
    public RallyInfoPB getAssistInfoPb() {
        RallyInfoPB.Builder builder = getRallyInfoBuilder().clearRecommendSoldierTypeList();
        SceneObjEntity sceneObj = getSceneEntity().getObjMgrComponent().getSceneObjEntity(getProp().getTargetId());
        if (sceneObj == null) {
            LOGGER.error("getAssistInfoPb target is null {} {}", this, getProp().getTargetId());
            return null;
        }
        switch (sceneObj.getEntityType()) {
            // army需要特殊处理
            case ET_Army:
                // 自己盟的集结部队被打
                ArmyEntity army = (ArmyEntity) sceneObj;
                RallyEntity rallyEntity = army.getRallyEntity();
                if (rallyEntity == null) {
                    LOGGER.error("getAssistInfoPb rally is null {} {}", this, army.getRallyComponent().getCurRallyId());
                    return null;
                }
                rallyEntity.getRallyArmyProp().copyToCs(builder.getRallyArmyInfoMapBuilder());
                builder.setCurSoldierNum(rallyEntity.getProp().getCurSoldierNum())
                        .setMaxSoldierNum(rallyEntity.getProp().getMaxSoldierNum())
                        .setBeRallyId(rallyEntity.getEntityId());
                break;
            default:
                if (sceneObj.getInnerArmyComponent() == null) {
                    LOGGER.error("getAssistInfoPb {} no innerArmyComponent!!", this);
                    return null;
                }
                sceneObj.getInnerArmyComponent().copyToRallyInfo(builder, true);

        }
        return builder.build();
    }

    private RallyInfoPB.Builder getRallyInfoBuilder() {
        return getProp().getCopyCsBuilder();
    }

    private RallyInfoPB.Builder getRallyInfoFullBuilder() {
        RallyInfoPB.Builder builder = getProp().getCopyCsBuilder();
        builder.setRallyArmyInfoMap(getRallyArmyProp().getCopyCsBuilder());
        return builder;
    }

    /**
     * 更新组织者名片
     */
    private void updateOrganizerInfoChange(IEvent e) {
        if (e.equals(PlayerNameChangeEvent.class)) {
            getProp().getOrganizerCardHead().setName(((PlayerNameChangeEvent) e).getNewName());
            return;
        }
        if (e.equals(PlayerPicChangeEvent.class)) {
            PlayerPicChangeEvent event = (PlayerPicChangeEvent) e;
            getProp().getOrganizerCardHead().setPicUrl(event.getPicUrl()).setPic(event.getPic());
            return;
        }
        if (e.equals(PlayerPicFrameChangeEvent.class)) {
            getProp().getOrganizerCardHead().setPicFrame(((PlayerPicFrameChangeEvent) e).getPicFrame());
        }
    }

    /**
     * 更新目标名片
     */
    private void updateTargetInfoChange(IEvent e) {
        if (e.equals(PlayerNameChangeEvent.class)) {
            getProp().getTargetCardHead().setName(((PlayerNameChangeEvent) e).getNewName());
            return;
        }
        if (e.equals(PlayerPicChangeEvent.class)) {
            PlayerPicChangeEvent event = (PlayerPicChangeEvent) e;
            getProp().getTargetCardHead().setPicUrl(event.getPicUrl()).setPic(event.getPic());
            return;
        }
        if (e.equals(PlayerPicFrameChangeEvent.class)) {
            getProp().getTargetCardHead().setPicFrame(((PlayerPicFrameChangeEvent) e).getPicFrame());
        }
    }

    @Override
    protected void onPostInitFailed() {
        getTimerComponent().onDestroy();
    }
}

package com.yorha.cnc.zone.zone.kindomSkil.skillEffects;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 赋税
 *
 * <AUTHOR>
 */
public class GainTax implements ISkillEffect {
    private static final Logger LOGGER = LogManager.getLogger(GainTax.class);

    @Override
    public ErrorCode checkCanUse(int skillId, long targetId, int targetZoneId, ZoneEntity zoneEntity) {
        return ErrorCode.OK;
    }

    @Override
    public void effect(int skillId, long targetId, ZoneEntity zoneEntity, int targetZoneId) {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        int gainTaxBuffId = service.getConstTemplate().getKingdomSkillTaxPara1();
        long skillEndTsMs = zoneEntity.getProp().getKingdomModel().getSkillModel().getSkillInfoV(skillId).getSkillEndTsMs();
        zoneEntity.getDevBuffComponent().addDevBuff(gainTaxBuffId, skillEndTsMs, CommonEnum.DevBuffSourceType.DBST_KINGDOM);
        zoneEntity.getBigScene().getKingdomComponent().addTaxSkillInfo();
        // 发送全服邮件
        sendSkillMail(skillId, zoneEntity);
    }

    private void sendSkillMail(int skillId, ZoneEntity zoneEntity) {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        StructMail.MailSendParams.Builder mailBuilder = zoneEntity.getKingdomComponent().getCommonBuffOrSkillMail(service.getConstTemplate().getKingdomSkillMailID(), "");
        if (mailBuilder == null) {
            LOGGER.info("mail builder is null");
            return;
        }
        mailBuilder.getContentBuilder().getKingBuffDataBuilder().setConfigId(skillId).setIsSkillData(true);
        MailUtil.sendZoneMail(zoneEntity.getZoneId(), mailBuilder.build());
    }

}

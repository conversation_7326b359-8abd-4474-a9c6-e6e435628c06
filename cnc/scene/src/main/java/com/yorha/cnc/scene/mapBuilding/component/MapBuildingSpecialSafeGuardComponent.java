package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjSpecialSafeGuardComponent;
import com.yorha.game.gen.prop.SpecialSafeGuardProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Collections;

public class MapBuildingSpecialSafeGuardComponent extends SceneObjSpecialSafeGuardComponent {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingSpecialSafeGuardComponent.class);

    public MapBuildingSpecialSafeGuardComponent(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }

    @Override
    public void specialSafeGuardOn(CommonEnum.SafeGuardReason safeGuardReason) {
        LOGGER.info("{} clan safe guard on reason:{}.", getOwner(), safeGuardReason);
        getOwner().getProp().getSafeGuard().setIsOpenSafeGuard(true);
        getOwner().getProp().getSafeGuard().getSafeGuardReasons().add(safeGuardReason.getNumber());
    }

    @Override
    public void specialSafeGuardOff(CommonEnum.SafeGuardReason safeGuardReason) {
        if (!getOwner().getProp().getSafeGuard().getSafeGuardReasons().contains(safeGuardReason.getNumber())) {
            return;
        }
        LOGGER.info("{} clan safe guard off reason:{}.", getOwner(), safeGuardReason);
        getOwner().getProp().getSafeGuard().getSafeGuardReasons().remove(safeGuardReason.getNumber());
        if (getOwner().getProp().getSafeGuard().getSafeGuardReasonsSize() == 0) {
            LOGGER.info("{} clan safe guard all off.", getOwner());
            getOwner().getProp().getSafeGuard().setIsOpenSafeGuard(false);
        }
    }

    @Override
    public void specialSafeGuardChange(boolean isOn, CommonEnum.SafeGuardReason safeGuardReason) {
        LOGGER.info("{} change clan safe guard from {} to {}, reason:{}", getOwner(), getOwner().getProp().getSafeGuard().getIsOpenSafeGuard(), isOn, safeGuardReason);
        if (isOn) {
            this.specialSafeGuardOn(safeGuardReason);
        } else {
            this.specialSafeGuardOff(safeGuardReason);
        }
    }

    @Override
    public boolean isSpecialSafeGuardOn() {
        return getOwner().getProp().getSafeGuard().getIsOpenSafeGuard();
    }

    @Override
    public Collection<Integer> getSafeGuardReasons() {
        return Collections.unmodifiableCollection(getProp().getSafeGuardReasons().getValues());
    }

    public SpecialSafeGuardProp getProp() {
        return getOwner().getProp().getSafeGuard();
    }
}

package com.yorha.cnc.zone.activity;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.Int64PlayerChooseItemMapProp;
import com.yorha.game.gen.prop.PlayerChooseItemProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsRank;
import com.yorha.proto.StructMsg;
import res.template.ActivityTemplate;
import res.template.BestCommanderSeasonTemplate;

import java.util.Map;

/**
 * 最强指挥官赛季活动
 *
 * <AUTHOR>
 */
public class ZoneBestCommanderTotalRankUnit extends ZoneActivityScoreRankUnit {

    public ZoneBestCommanderTotalRankUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    public void setPlayerChooseItem(long playerId, int itemId) {
        final BestCommanderSeasonTemplate template = getTemplate();
        if (template == null) {
            throw new GeminiException("ZoneBestCommanderTotalRankUnit setPlayerChooseItem template is null");
        }
        if (!template.getIsSelect()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "cur not select " + template.getId());
        }
        final ZoneBestCommanderUnit bestCommanderUnit = owner.findFirstUnitInTopFatherActOf(ZoneBestCommanderUnit.class);
        if (bestCommanderUnit == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "ZoneBestCommanderTotalRankUnit setPlayerChooseItem ZoneBestCommanderUnit is null");
        }
        if (!template.getRewardList().contains(itemId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "ZoneBestCommanderTotalRankUnit setPlayerChooseItem error item " + itemId + " with " + template.getId());
        }
        final PlayerChooseItemProp itemProp = new PlayerChooseItemProp();
        itemProp.setPlayerId(playerId).setItemId(itemId);
        getChooseItems().put(playerId, itemProp);
        LOGGER.info("ZoneBestCommanderTotalRankUnit setPlayerChooseItem {} {} template: {} item: {} size: {}", owner.getActivityId(), playerId, template.getId(), itemId, getChooseItems().size());
    }

    @Override
    protected void afterMailSend(SsRank.DeleteRankAndGetTopAns ans, Map<Long, StructMsg.RankInfoDTO> ret) {
        ZoneBestCommanderUnit bestCommanderUnit = owner.findFirstUnitInTopFatherActOf(ZoneBestCommanderUnit.class);
        if (bestCommanderUnit != null) {
            bestCommanderUnit.addRankSettleData(owner.getProp().getActivityId(), ans.getRangeMemberInfoMap());
        }

        // 总榜需要放到最强执政官历史大赏里面去
        ActivityTemplate template = ResHolder.getTemplate(ActivityTemplate.class, activityId);
        final int rankLimit = getRankLimit(template);
        StructMsg.BestCommanderHistoryRank.Builder history = StructMsg.BestCommanderHistoryRank.newBuilder();
        history.setTsSec(owner.getProp().getEndTsSec());
        for (StructMsg.RankInfoDTO rankInfoDTO : ret.values()) {
            // 只放进去前十的
            if (rankInfoDTO.getRank() <= rankLimit) {
                history.addRankList(rankInfoDTO);
            }
        }
        owner.owner().getSideActivityComponent().addBestCommanderHistoryRank(history);
    }

    private BestCommanderSeasonTemplate getTemplate() {
        final ZoneBestCommanderUnit bestCommanderUnit = owner.findFirstUnitInTopFatherActOf(ZoneBestCommanderUnit.class);
        if (bestCommanderUnit == null) {
            return null;
        }
        return bestCommanderUnit.getSeasonConfig();
    }

    @Override
    protected int getOptionalItemId(long playerId) {
        final BestCommanderSeasonTemplate template = getTemplate();
        if (template == null) {
            LOGGER.info("ZoneBestCommanderTotalRankUnit getOptionalItemId {} {} template is null", owner.getActivityId(), playerId);
            return 0;
        }
        LOGGER.info("ZoneBestCommanderTotalRankUnit getOptionalItemId {} template {}", owner.getActivityId(), template.getId());
        // 非自选
        if (!template.getIsSelect()) {
            if (template.getRewardList().size() <= 0) {
                throw new GeminiException("ZoneBestCommanderTotalRankUnit getOptionalItemId reward is null " + owner.getActivityId());
            }
            // 给第一个默认配置
            return template.getRewardList().get(0);
        }
        int itemId = -1;
        PlayerChooseItemProp chooseItemProp = getChooseItems().getOrDefault(playerId, null);
        if (chooseItemProp != null) {
            itemId = chooseItemProp.getItemId();
        }
        if (itemId <= 0) {
            // 给第一个默认配置
            itemId = template.getRewardList().get(0);
        }
        return itemId;
    }

    private Int64PlayerChooseItemMapProp getChooseItems() {
        return owner.getProp().getBcTotalRankUnit().getChooseItems();
    }
}

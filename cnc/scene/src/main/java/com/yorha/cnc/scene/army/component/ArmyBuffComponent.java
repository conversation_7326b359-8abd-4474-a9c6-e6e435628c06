package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.game.gen.prop.Int32BuffMapProp;
import com.yorha.proto.CommonEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ArmyBuffComponent extends SceneObjBuffComponent {

    public ArmyBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuff();
    }

    @Override
    public ArmyEntity getOwner() {
        return (ArmyEntity) super.getOwner();
    }

    public void addBuff(List<Integer> buffIdList) {
        for (Integer buffId : buffIdList) {
            addBuff(buffId, 0);
        }
    }

    public void addBuff(int buffId, int lifeCycle) {
        BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
        EffectContext build = EffectContext.newBuilder()
                .setCastRole(battleRole)
                .setSkillId(0)
                .setType(CommonEnum.BattleLogSkillType.BLST_NONE)
                .setHeroId(0)
                .setEffectId(0)
                .setLeaderRoleId(getOwner().getBattleComponent().getLeaderRoleId())
                .setAttachBuffId(0)
                .setDotContext(null)
                .build(false);
        battleRole.getBuffHandler().addBuffWithLife(battleRole, buffId, 1, lifeCycle, build);
    }

    public void removeBuff(List<Integer> buffIdList) {
        BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
        for (Integer buffId : buffIdList) {
            battleRole.getBuffHandler().removeBuffById(buffId);
        }
    }
}

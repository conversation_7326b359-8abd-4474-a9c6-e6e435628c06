package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.ClanBuildData;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.common.constant.Constants;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

/**
 * 全部军团总共建造某个军团建筑的数量
 * 参数：要求建造的建筑数量_要求建造的军团建筑类型
 */
public class AllClanTotalBuildMileStoneHandler extends AbstractMileStoneHandler {


    @Override
    public void updateProcess(MileStoneTaskData data) {
        if (data instanceof ClanBuildData) {
            ClanBuildData clanData = (ClanBuildData) data;
            String[] taskParam = getTaskParamById();
            Integer limit = Integer.parseInt(taskParam[0]);
            if (!matchParam(clanData)) {
                return;
            }
            getProp().setProcess(Math.min(clanData.getValue() + getProp().getProcess(), limit));
        }
    }


    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanBuildData) {
            ClanBuildData data = (ClanBuildData) taskData;
            if (!matchParam(data)) {
                return;
            }
            Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
            MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.computeIfAbsent(data.getClanId(), (key) -> {
                MileStoneClanInfoProp prop = new MileStoneClanInfoProp();
                prop.setClanId(key);
                return prop;
            });
            setClanScore(mileStoneClanInfoProp, data.getClanId(), mileStoneClanInfoProp.getScore() + 1, "onClanMapBuildFin");
        }
    }

    private boolean matchParam(ClanBuildData data) {
        String[] taskParam = getTaskParamById();
        String[] split = taskParam[1].split(Constants.BAN_JIAO_DOU_HAO);
        int level = Integer.parseInt(taskParam[2]);
        for (String buildTypeConfig : split) {
            int buildType = Integer.parseInt(buildTypeConfig);
            if (buildType == data.getBuildType() && (level == 0 || level == data.getBuildLevel())) {
                return true;
            }
        }
        return false;
    }


    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_ALL_PLAYER;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_BUILDINGS_BUILT_NUM;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END;
    }
}

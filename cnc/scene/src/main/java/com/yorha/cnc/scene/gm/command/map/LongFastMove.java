package com.yorha.cnc.scene.gm.command.map;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class LongFastMove implements SceneGmCommand {

    private static final Map<Integer, List<Point>> POINT_MAP = ImmutableMap.of(
            0, ImmutableList.of(Point.valueOf(278000, 44000), Point.valueOf(35000, 598000), Point.valueOf(304000, 61000), Point.valueOf(45000, 594000)),
            1, ImmutableList.of(Point.valueOf(408000, 393000), Point.valueOf(790000, 16000), Point.valueOf(393000, 392000), Point.valueOf(802000, 40000)),
            2, ImmutableList.of(Point.valueOf(905000, 21000), Point.valueOf(1203000, 489000), Point.valueOf(869000, 35000), Point.valueOf(1208000, 518000)),
            3, ImmutableList.of(Point.valueOf(1167000, 546000), Point.valueOf(1045000, 1206000), Point.valueOf(1197000, 577000), Point.valueOf(1023000, 1213000)),
            4, ImmutableList.of(Point.valueOf(931000, 987000), Point.valueOf(498000, 1213000), Point.valueOf(931000, 1024000), Point.valueOf(499000, 1204000)),
            5, ImmutableList.of(Point.valueOf(44000, 650000), Point.valueOf(346000, 1201000), Point.valueOf(34000, 655000), Point.valueOf(331000, 1213000))
    );

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        int regionId = scenePlayer.getMainCity().getTransformComponent().getRegionId();
        int turn = Integer.parseInt(args.get("turn"));
        for (ArmyEntity armyEntity : scenePlayer.getArmyMgrComponent().getMyArmyList()) {
            List<Point> pointList = POINT_MAP.get(regionId);
            Point point = pointList.get(turn % pointList.size());
            StructPlayer.ArmyActionInfo.Builder actionBuilder = StructPlayer.ArmyActionInfo.newBuilder();
            actionBuilder.setArmyActionType(CommonEnum.ArmyActionType.AAT_Move)
                    .setTargetPoint(Struct.Point.newBuilder().setX(point.getX()).setY(point.getY()).build())
                    .setDebugFastMove(true);
            armyEntity.getBehaviourComponent().handlePlayerReq(actionBuilder.build(), 0);
        }
    }

    @Override
    public String showHelp() {
        return "LongFastMove turn={value}";
    }
}

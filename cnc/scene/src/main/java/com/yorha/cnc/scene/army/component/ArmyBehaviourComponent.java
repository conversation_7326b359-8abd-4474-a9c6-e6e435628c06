package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.army.ArmyFactory;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.army.ArmyTargetChangeEvent;
import com.yorha.cnc.scene.event.army.PlayerOperationPreEvent;
import com.yorha.cnc.scene.event.battle.AbandonBeAttackEvent;
import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;
import com.yorha.cnc.scene.event.mapbuilding.LoseAdjoinPartEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneObj.move.IMoveTargetLoseHandler;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Core.Code;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ArmyBehaviourComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyBehaviourComponent.class);
    private static final List<ArmyState> noOperation = Arrays.asList(ArmyState.AS_Airborne, ArmyState.AS_TransportReturn);
    private boolean recvPlayerAction = true;
    /**
     * 战斗目标事件监听器
     */
    private EventListener battleTargetEventListener;
    /**
     * 当前部队操作目标id
     */
    private long curBehaviourTargetId = 0;
    /**
     * 部队上一次玩家输入的指令信息
     */
    private StructPlayer.ArmyActionInfo lastPlayerArmyActionInfo = null;

    public ArmyBehaviourComponent(ArmyEntity parent) {
        super(parent);
    }

    @Override
    public void init() {
        // 关注自身的死亡、删除事件  无需移除
        getOwner().getEventDispatcher().addMultiEventListenerRepeat(
                this::onDieOrDelete,
                DeleteEvent.class,
                DieEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(
                this::onSelfClanChange,
                ClanChangeEvent.class
        );
    }

    /**
     * 不再接受玩家操作指令
     */
    public void setRecvPlayerAction(boolean isRecv) {
        recvPlayerAction = isRecv;
    }

    /**
     * 尝试退出部队的上一次操作指令
     */
    private void tryExitLastArmyAction(StructPlayer.ArmyActionInfo curArmyActionInfo) {
        if (lastPlayerArmyActionInfo == null || lastPlayerArmyActionInfo.getArmyActionType() != ArmyActionType.AAT_Battle) {
            return;
        }
        if (lastPlayerArmyActionInfo.getTargetId() == curArmyActionInfo.getTargetId()) {
            // 同一个对象不执行退出逻辑
            return;
        }
        // 上一次是战斗指令
        long targetId = lastPlayerArmyActionInfo.getTargetId();
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        if (sceneObjEntity == null) {
            return;
        }
        sceneObjEntity.getTransformComponent().exitBesiege(getEntityId());
    }

    /**
     * 检测行军指令合法性  不合法直接抛异常
     */
    public void checkActionValid(ArmyActionType armyActionType, Point point, long targetId) {
        // 溃败
        if (!recvPlayerAction) {
            try {
                getOwner().checkRetreat();
            } catch (Exception e) {
                LOGGER.error("checkActionValid checkRetreat failed {} ", getOwner(), e);
            }
            throw new GeminiException(ErrorCode.ARMY_IN_RETREAT);
        }
        // 集结状态，无法操作
        if (getOwner().isInRally() && getOwner().getProp().getRallyRole() != RallyArmyRoleType.RART_Single) {
            throw new GeminiException(ErrorCode.RALLY_IN_RALLY);
        }
        // 运输下降 遣返中
        ArmyState armyState = getOwner().getProp().getArmyState();
        if (noOperation.contains(armyState)) {
            throw new GeminiException(ErrorCode.ARMY_CANT_RETURN);
        }
        // 运输起飞 飞行 只能遣返
        if (getOwner().isInTransport() && armyActionType != ArmyActionType.AAT_Return) {
            throw new GeminiException(ErrorCode.ARMY_IN_TRANSPORT);
        }
        // 关卡通行中 无法操作
        if (getOwner().getStatusComponent().isInPassing()) {
            throw new GeminiException(ErrorCode.ARMY_IN_CROSSING);
        }
        Point targetPoint = null;
        switch (armyActionType) {
            case AAT_Return:
                targetPoint = getOwner().getScenePlayer().getMainCity().getCurPoint();
                break;
            case AAT_PICK_UP:
                SceneObjEntity dropObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
                targetPoint = dropObj.getCurPoint();
                break;
            case AAT_Move:
                if (targetId == 0 || targetId == -1) {
                    targetPoint = point;
                } else {
                    SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
                    targetPoint = sceneObj.getCurPoint();
                }
                break;
            case AAT_Battle:
                SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
                ArmyFactory.checkCanAttackWithArmy(getOwner().getScene(), getOwner(), targetId);
                targetPoint = sceneObj.getTransformComponent().getBeBattlePoint(getOwner().getCurPoint(), getOwner().getClanId(), getOwner().getMoveComponent().getMoveSearchPathTag(true));
                break;
            case AAT_JoinRally:
                Code code = getOwner().getScenePlayer().getRallyComponent().checkJoinRally(targetId, getOwner().getAliveSoldierNum());
                if (!ErrorCode.isOK(code)) {
                    throw new GeminiException(code.getId());
                }
                RallyEntity rallyEntity = getOwner().getScenePlayer().getRallyComponent().getRallyEntity(targetId);
                targetPoint = rallyEntity.getMoveTargetEntity().getCurPoint();
                break;
            case AAT_ASSIST:
                Code codeAssist = getOwner().getScenePlayer().getRallyComponent().checkAssist(targetId, getOwner().getAliveSoldierNum(), this.getEntityId());
                if (!ErrorCode.isOK(codeAssist)) {
                    throw new GeminiException(codeAssist.getId());
                }
                targetPoint = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId).getCurPoint();
                break;
            case AAT_GATHER:
                SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
                if (entity.getEntityType() == EntityType.ET_ResBuilding) {
                    if (!getOwner().getScenePlayer().checkResourceUnlock(((ResBuildingEntity) entity).getTemplate().getResType().getNumber())) {
                        throw new GeminiException(ErrorCode.COLLECT_RESOURCE_LOCK);
                    }
                } else if (entity.getEntityType() == EntityType.ET_ClanResBuilding) {
                    if (!getOwner().getScenePlayer().checkResourceUnlock(((ClanResBuildingEntity) entity).getCurrencyType().getNumber())) {
                        throw new GeminiException(ErrorCode.COLLECT_RESOURCE_LOCK);
                    }
                    Code codeCollect = getOwner().getScenePlayer().getRallyComponent().checkCollect(targetId);
                    if (!ErrorCode.isOK(codeCollect)) {
                        throw new GeminiException(codeCollect.getId());
                    }
                }
                targetPoint = entity.getCurPoint();
            default:
                break;
        }
        if (targetPoint == null) {
            return;
        }
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(targetPoint, getOwner().getScene().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        // 因为修复要改城门线坐标 等vs7再改 先注掉前置坐标校验
//        if (!getOwner().getScene().getPathFindMgrComponent().isPointStaticWalkable(targetPoint)) {
//            throw new GeminiException(ErrorCode.ARMY_DST_POINT_NOT_WALKABLE_STATIC);
//        }
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        int regionId1 = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), targetPoint);
        int regionId2 = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), getOwner().getCurPoint());
        if (regionId1 != regionId2) {
            SceneClanEntity sceneClan = getOwner().getScenePlayer().getSceneClan();
            if (sceneClan == null) {
                if (armyActionType == ArmyActionType.AAT_Return) {
                    getOwner().getMoveComponent().setCanForceDefeat(true);
                }
                throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
            }
        }
    }

    /**
     * 检测行军动作指令的变化  返回是否应该中断执行
     */
    private boolean checkActionChange(ArmyActionType armyActionType, long targetId) {
        // 注释表示：释放技能的时候可以行动
//        if (getOwner().getBattleComponent().isInBattle() && getOwner().getBattleComponent().getBattleRole().isFiringSkill()) {
//            return true;
//        }
        if (lastPlayerArmyActionInfo == null) {
            return false;
        }
        if (lastPlayerArmyActionInfo.getArmyActionType() == ArmyActionType.AAT_Battle
                && lastPlayerArmyActionInfo.getTargetId() != 0
                && lastPlayerArmyActionInfo.getTargetId() != targetId) {
            getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
        }
        // 上次操作和本次操作都一样是驻扎，则中断执行
        return lastPlayerArmyActionInfo.getArmyActionType() == armyActionType && armyActionType == ArmyActionType.AAT_Stay;
    }

    /**
     * 处理玩家操作请求  有前置性check
     */
    public void handlePlayerReq(StructPlayer.ArmyActionInfo armyActionInfo, int costEnergy) {
        LOGGER.debug("{} recv player req:{}", getOwner(), armyActionInfo);
        ArmyActionType armyActionType = armyActionInfo.getArmyActionType();
        long curTargetId = armyActionInfo.getTargetId();
        // 检测指令合法性

        Point p = Point.valueOf(armyActionInfo.getTargetPoint().getX(), armyActionInfo.getTargetPoint().getY());
        checkActionValid(armyActionType, p, curTargetId);
        // 检测指令是否应该中断执行
        if (checkActionChange(armyActionType, curTargetId)) {
            return;
        }
        getOwner().getEventDispatcher().dispatch(new PlayerOperationPreEvent(armyActionInfo));
        // !! 可能是集结出来溃败了
        if (!recvPlayerAction) {
            return;
        }
        // 尝试退出围攻
        tryExitLastArmyAction(armyActionInfo);
        // 尝试取消之前注册的目标监听器
        cancelBattleTargetEventListener();
        // 处理指令
        handleAction(armyActionInfo, costEnergy);
    }

    /**
     * 处理操作
     */
    public void handleAction(StructPlayer.ArmyActionInfo armyActionInfo, int costEnergy) {
        ArmyActionType armyActionType = armyActionInfo.getArmyActionType();
        long curTargetId = armyActionInfo.getTargetId();
        ArmyMoveComponent moveComponent = getOwner().getMoveComponent();
        if (ServerContext.getServerDebugOption().isGmSwitch()) {
            if (armyActionInfo.getDebugFastMove()) {
                // 一万倍行军速度
                moveComponent.debugSetMoveSpeedRatio(10000);
            } else {
                moveComponent.debugSetMoveSpeedRatio(1);
            }
        }
        if (!moveComponent.canMove()) {
            LOGGER.debug("army:{} can not move.", getOwner());
            return;
        }
        getOwner().getBattleComponent().stopCurActiveAttack();
        switch (armyActionType) {
            case AAT_Move:
                getOwner().getStatusComponent().setStaying();
                if (curTargetId != 0 && curTargetId != -1) {
                    if (curTargetId == getEntityId()) {
                        LOGGER.info("army move follow but target is self {}", getOwner());
                        curTargetId = 0;
                        break;
                    }
                    SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(curTargetId);
                    Point followPoint = targetEntity.getTransformComponent().getFollowPoint(getOwner().getCurPoint(), null);
                    if (followPoint == null || followPoint.getX() == 0 || followPoint.getY() == 0) {
                        LOGGER.error("army:{} move target cant find point {} followPoint:{}", getOwner(), curTargetId, followPoint);
                        curTargetId = 0;
                        break;
                    }
                    if (!getOwner().getCurPoint().equals(followPoint)) {
                        moveComponent.moveToPointAsync(followPoint, () -> getOwner().getStatusComponent().setStaying());
                    }
                } else {
                    Struct.Point targetPoint = armyActionInfo.getTargetPoint();
                    moveComponent.moveToPointAsync(Point.valueOf(targetPoint.getX(), targetPoint.getY()), () -> getOwner().getStatusComponent().setStaying());
                }
                curTargetId = 0;
                break;
            case AAT_Stay:
                moveComponent.stopMove();
                getOwner().getStatusComponent().setStaying();
                break;
            case AAT_Return:
                if (getOwner().isInTransport()) {
                    getOwner().getTransportComponent().transportReturn();
                } else {
                    // 因为要做强制溃败功能 需要即时返回错误码  不能异步寻路了
                    moveComponent.handReturnMainCity();
                }
                curTargetId = getOwner().getScenePlayer().getMainCity().getEntityId();
                // 回城指令需要返还体力
                getOwner().getHuntingComponent().focusOnMonster(0, 0);
                break;
            case AAT_Battle:
                long targetId = armyActionInfo.getTargetId();
                SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
                if (targetEntity == null) {
                    LOGGER.warn("army:{} try battle, find no {}", getOwner(), targetId);
                    throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
                }
                if (targetEntity.getBattleComponent() == null) {
                    throw new GeminiException(ErrorCode.BATTLE_CANT);
                }
                ErrorCode errorCode = getOwner().canBattleWithCode(targetEntity, true);
                if (errorCode.isNotOk()) {
                    throw new GeminiException(errorCode);
                }
                if (targetEntity.getEntityType() == EntityType.ET_Monster) {
                    getOwner().getHuntingComponent().focusOnMonster(targetId, costEnergy);
                }
                getOwner().getBattleComponent().setIsStayAfterBattle(armyActionInfo.getIsStayAfterBattle());
                if (targetEntity.getEntityType() == EntityType.ET_ResBuilding) {
                    getOwner().getCollectComponent().handleCollect(targetId, true);
                } else if (targetEntity.getEntityType() == EntityType.ET_ClanResBuilding) {
                    throw new GeminiException(ErrorCode.BATTLE_CANT);
                } else {
                    // 执行前设置下 免得立马开战拿到的id不对
                    setCurBehaviourTargetId(curTargetId);
                    handleBattleToTarget(targetEntity);
                }
                break;
            case AAT_CreateRally:
                break;
            case AAT_JoinRally:
                getOwner().getRallyComponent().handleJoinRally(armyActionInfo, costEnergy);
                break;
            case AAT_ASSIST:
                getOwner().getAssistComponent().handleAssist(curTargetId);
                break;
            case AAT_Transport:
                Point point = Point.valueOf(armyActionInfo.getTargetPoint().getX(), armyActionInfo.getTargetPoint().getY());
                getOwner().getTransportComponent().startTransport(point, curTargetId);
                break;
            case AAT_PICK_UP:
                getOwner().getBattleComponent().setIsStayAfterBattle(armyActionInfo.getIsStayAfterBattle());
                getOwner().getPickUpComponent().handlePickUp(curTargetId);
                break;
            case AAT_GATHER:
                getOwner().getCollectComponent().handleCollect(curTargetId, false);
                break;
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        setCurBehaviourTargetId(curTargetId);
        lastPlayerArmyActionInfo = armyActionInfo;
        refreshArmyState();
    }

    /**
     * 处理攻击指令
     */
    private void handleBattleToTarget(SceneObjEntity targetEntity) {
        // add事件监听
        addBattleTargetEventListener(targetEntity);
        // move to target
        ArmyMoveComponent moveComponent = getOwner().getMoveComponent();
        IMoveTargetLoseHandler loseHandler = null;
        // 集结的话， rallyEntity那边管了 不用这边加
        if (!getOwner().isRallyArmy()) {
            loseHandler = (event) -> onTargetLose(targetEntity.getEntityId());
        }
        try {
            moveComponent.moveToTargetAsync(
                    targetEntity, null,
                    () -> onArriveTarget(targetEntity.getEntityId()),
                    loseHandler,
                    (codeId) -> {
                        if (ErrorCode.isOK(codeId)) {
                            return;
                        }
                        getOwner().getScenePlayer().sendErrorCode(codeId);
                        cancelBattleTargetEventListener();
                        getOwner().getStatusComponent().setStaying();
                    });
        } catch (Exception e) {
            cancelBattleTargetEventListener();
            throw e;
        }
    }

    /**
     * add 战斗对象事件监听
     */
    private void addBattleTargetEventListener(SceneObjEntity targetEntity) {
        // 集结的话， rallyEntity那边管了
        if (getOwner().isRallyArmy()) {
            return;
        }
        battleTargetEventListener = targetEntity.getEventDispatcher().addMultiEventListenerRepeat(
                this::onBattleTargetChange,
                ClanChangeEvent.class, LoseAdjoinPartEvent.class);
    }

    /**
     * 自身联盟发生变化
     */
    private void onSelfClanChange(ClanChangeEvent event) {
        if (!recvPlayerAction) {
            return;
        }
        if (curBehaviourTargetId == 0 || lastPlayerArmyActionInfo == null || lastPlayerArmyActionInfo.getArmyActionType() != ArmyActionType.AAT_Battle) {
            return;
        }
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(curBehaviourTargetId);
        // 自己联盟变了  不可战斗了
        ErrorCode errorCode = getOwner().canBattleWithCode(targetEntity, true);
        if (targetEntity == null || errorCode.isNotOk()) {
            cancelBattleTargetEventListener();
            getOwner().getMoveComponent().stopMove();
            getOwner().getStatusComponent().setStaying();
            getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));

            // 不能战斗了退出合围，变成驻扎状态，挂起战斗关系
            StructPlayer.ArmyActionInfo.Builder builder = StructPlayer.ArmyActionInfo.newBuilder()
                    .setArmyActionType(ArmyActionType.AAT_Stay)
                    .setTargetId(0);
            tryExitLastArmyAction(builder.build());
            cancelBattleTargetEventListener();
            handleAction(builder.build(), 0);
            lastPlayerArmyActionInfo = null;
            if (getOwner().getBattleComponent() != null) {
                // 挂起战斗
                getOwner().getBattleComponent().handUpRelationWith(targetEntity);
            }
            LOGGER.info("{} self clan change. end battle target: {}", getOwner(), curBehaviourTargetId);
        }
    }

    /**
     * 攻击目标联盟改变 / 不再满足邻接条件
     */
    private void onBattleTargetChange(IEvent e) {
        IEventWithEntityId event = (IEventWithEntityId) e;
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(event.getEntityId());
        // 对方联盟变了  不可战斗了
        if (!getOwner().canBattle(targetEntity, true)) {
            cancelBattleTargetEventListener();
            getOwner().getMoveComponent().stopMove();
            getOwner().getStatusComponent().setStaying();
            getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
            LOGGER.info("{} battleTarget clan change. end battle target: {}", getOwner(), event.getEntityId());
        }
    }

    /**
     * 自身死亡或删除 取消事件监听器
     */
    private void onDieOrDelete(IEvent event) {
        cancelBattleTargetEventListener();
        getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
    }

    /**
     * 战斗目标丢失(死亡/回城) 取消事件监听器
     */
    private void onTargetLose(long targetId) {
        LOGGER.info("{} battleTarget lose", getOwner());
        cancelBattleTargetEventListener();
        getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
        // 如果是在开战了，那在战斗那边处理了
        if (getOwner().getBattleComponent().isInPk(targetId)) {
            return;
        }
        getOwner().getBattleComponent().afterBattleAction();
    }

    /**
     * 到达目标体
     */
    private void onArriveTarget(long targetId) {
        LOGGER.debug("{} on arrive target:{}", getOwner(), targetId);
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        boolean needMoveBesiege = true;
        // 对方目标也是我  而且刚建立或者已经建立并且向我移动中
        if (target.getBattleComponent().getActiveTargetId() == getEntityId()) {
            if (getOwner().getScene().getBattleGroundComponent().hasPrepareBattleRelation(getEntityId(), targetId)) {
                needMoveBesiege = false;
            } else if (getOwner().getScene().getBattleGroundComponent().hasBattleRelation(getEntityId(), targetId)
                    && target.getMoveComponent() != null && target.getMoveComponent().isMoving()) {
                needMoveBesiege = false;
            }
        }
        ErrorCode errorCode = getOwner().canBattleWithCode(target, true);
        if (errorCode.isNotOk()) {
            boolean isShieldOn = errorCode.isSame(ErrorCode.SPY_IS_SHIELD_ON) || errorCode.isSame(ErrorCode.SPY_TARGET_IS_PROTECT);
            // 对方开盾了，给进攻方发邮件
            if (isShieldOn) {
                sendBattleCancelMail(target);
            }
            // 集结部队解散
            if (getOwner().isRallyArmy()) {
                getOwner().getRallyEntity().dismiss(RallyDismissReason.RDR_TARGET_CANT_ATTACK);
                return;
            }
            // 对方开盾了, 部队返回
            if (isShieldOn) {
                getOwner().getMoveComponent().returnMainCity();
                return;
            }
            tryBattleFailed();
            return;
        }
        if (!getOwner().getBattleComponent().tryStartBattleWith(target)) {
            tryBattleFailed();
            return;
        }
        if (target.getEntityType() == EntityType.ET_City || target.getEntityType() == EntityType.ET_MapBuilding) {
            getOwner().getBattleComponent().setCurAttackBuilding(targetId);
        }
        getOwner().getMoveComponent().occupyAndMoveToBesiegePoint(target, needMoveBesiege, null);
        getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_BATTLE, target);
    }

    /**
     * 到达攻击目标后尝试开战失败
     */
    private void tryBattleFailed() {
        cancelBattleTargetEventListener();
        getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
        getOwner().getMoveComponent().stopMove();
        getOwner().getStatusComponent().setStaying();
        refreshArmyState();
    }

    /**
     * 刷新部队状态标识
     */
    public void refreshArmyState() {
        // 运输/溃败 状态就不用刷了
        if (getOwner().isInTransport() || getOwner().getProp().getArmyState() == ArmyState.AS_Retreating) {
            return;
        }
        ArmyState curArmyState = getCurArmyState();
        getOwner().getProp().setArmyState(curArmyState);
    }

    public boolean canAttackAction() {
        if (getOwner().getMoveComponent().isSiegeMove()) {
            // 合围移动可以普攻
            return true;
        }
        if (getOwner().getMoveComponent().isParallelMoving()) {
            // 并行追击可以普攻
            return true;
        }
        // 普通移动不可以普攻
        return !getOwner().getMoveComponent().isMoving();
    }

    public ArmyState getCurArmyState() {
        // 死亡
        ArmyBattleComponent battleComponent = getOwner().getBattleComponent();
        if (!battleComponent.hasAnyAlive()) {
            return ArmyState.AS_Retreating;
        }
        // 战斗中 并且当前行为目标是我的战斗对象
        if (battleComponent.isInBattle() && battleComponent.isInPk(curBehaviourTargetId)) {
            return ArmyState.AS_InBattle;
        }
        // 不移动，驻扎中
        if (!getOwner().getMoveComponent().isMoving()) {
            // 战斗中
            if (battleComponent.hasRunningRelation()) {
                return ArmyState.AS_InBattle;
            }
            // 拾取中
            if (getOwner().getPickUpComponent().isPicking()) {
                return ArmyState.AS_PICK_UP;
            }
            // 采集中
            if (getOwner().getProp().getAttachState() == AttachState.AAS_COLLECT) {
                return ArmyState.AS_Collect;
            }
            return ArmyState.AS_Staying;
        }
        // 移动中
        if (getOwner().getScene().isMainScene()) {
            long mainCityId = getOwner().getScenePlayer().getMainCity().getEntityId();
            if (mainCityId == curBehaviourTargetId) {
                // 回城中
                return ArmyState.AS_Return;
            }
        }
        return ArmyState.AS_Moving;
    }

    /**
     * 取消行为目标的事件监听
     */
    private void cancelBattleTargetEventListener() {
        if (battleTargetEventListener != null) {
            battleTargetEventListener.cancel();
            battleTargetEventListener = null;
        }
    }

    public long getCurBehaviourTargetId() {
        return curBehaviourTargetId;
    }

    public void setCurBehaviourTargetId(long targetId) {
        if (curBehaviourTargetId == targetId) {
            return;
        }
        LOGGER.debug("{} change behaviour target from {} to {}", getOwner(), curBehaviourTargetId, targetId);
        if (curBehaviourTargetId != 0 && curBehaviourTargetId != targetId) {
            SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(curBehaviourTargetId);
            if (targetEntity != null && targetEntity.getBattleComponent() != null && targetEntity.getBattleComponent().hasAnyAlive()) {
                targetEntity.getEventDispatcher().dispatch(new AbandonBeAttackEvent(getEntityId()));
            }
        }
        this.curBehaviourTargetId = targetId;
        getOwner().getEventDispatcher().dispatch(new ArmyTargetChangeEvent(getEntityId(), targetId));
    }

    public ArmyActionType getCurArmyActionType() {
        if (lastPlayerArmyActionInfo != null) {
            return lastPlayerArmyActionInfo.getArmyActionType();
        } else {
            return null;
        }
    }

    public void tryOpenWarFrenzy() {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        if (isPvpArmyByAction()) {
            getOwner().getScenePlayer().getDevBuffComponent().openWarFrenzy();
        }
    }

    /**
     * 是否是pvp行军，根据army的指令判断
     */
    public boolean isPvpArmyByAction() {
        if (isPvpArmyAction()) {
            boolean isAssistArmy = getCurArmyActionType() == ArmyActionType.AAT_JoinRally;
            return isTargetIsEnemyPlayer(isAssistArmy);
        }
        return false;
    }

    /**
     * 是否是pvp行军，根据army的状态判断
     */
    public boolean isPvpArmyByState() {
        if (isPvpArmyState()) {
            boolean isAssistArmy = getOwner().getStatusComponent().getState() == ArmyDetailState.ADS_MOVE_ASSIST;
            return isTargetIsEnemyPlayer(isAssistArmy);
        }
        return false;
    }

    /**
     * 目标是否是敌方玩家
     */
    private boolean isTargetIsEnemyPlayer(boolean isAssistArmy) {
        long targetId = getCurBehaviourTargetId();

        if (isAssistArmy) {
            // 加入集结，要找出集结的目标
            RallyEntity rallyEntity = getOwner().getRallyComponent().getTargetRallyEntity(getCurBehaviourTargetId());
            if (rallyEntity != null) {
                targetId = rallyEntity.getProp().getTargetId();
            }
        }
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        if (targetEntity != null && targetEntity.getBattleComponent() != null) {
            return targetEntity.getBattleComponent().isEnemyPlayer(getOwner().getClanId());
        }
        return false;
    }

    /**
     * 依据军队状态是否是PVP行军
     */
    private boolean isPvpArmyState() {
        ArmyDetailState curArmyState = getOwner().getStatusComponent().getState();
        switch (curArmyState) {
            case ADS_BATTLE:
            case ADS_MOVE_BATTLE:
            case ADS_RALLY:
                // 进攻行军，战斗中，集结
                return true;
            case ADS_MOVE_ASSIST:
                // 援助移动，看是不是加入集结
                return getCurArmyActionType() == ArmyActionType.AAT_JoinRally;
            default:
                return false;
        }
    }

    /**
     * 依据玩家行为判断是否是PVP行军
     */
    private boolean isPvpArmyAction() {
        ArmyActionType curArmyActionType = getCurArmyActionType();
        if (curArmyActionType != null) {
            return curArmyActionType == ArmyActionType.AAT_Battle
                    || curArmyActionType == ArmyActionType.AAT_CreateRally
                    || curArmyActionType == ArmyActionType.AAT_JoinRally;
        } else {
            return false;
        }
    }

    private void sendBattleCancelMail(SceneObjEntity target) {
        final ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        final int mailId = constTemplate.getAttackCancelMail();

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(mailId);

        if (target.getEntityType() == EntityType.ET_City) {
            CityEntity cityEntity = (CityEntity) target;
            String clanName = StringUtils.isNotBlank(cityEntity.getProp().getClanSname()) ? "[" + cityEntity.getProp().getClanSname() + "]" : "";
            Struct.DisplayParam clanNameParam = MsgHelper.buildDisPlayText(clanName);
            Struct.DisplayParam playerNameParam = MsgHelper.buildDisPlayText(cityEntity.getPlayerName());

            StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder();
            titleBuilder.getSubTitleDataBuilder().getParamsBuilder().addDatas(clanNameParam);
            titleBuilder.getSubTitleDataBuilder().getParamsBuilder().addDatas(playerNameParam);

            StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
            contentBuilder.setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
            contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(clanNameParam);
            contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(playerNameParam);

            builder.setTitle(titleBuilder);
            builder.setContent(contentBuilder);
        }

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getScenePlayer().getZoneId())
                        .build(),
                builder
                        .build()
        );
    }
}
package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum.AdditionSourceType;
import com.yorha.proto.CommonEnum.DressType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.DressResTemplate;

/**
 * 主堡外观相关 皮肤、铭牌
 *
 * <AUTHOR>
 */
public class CityExteriorComponent extends SceneObjComponent<CityEntity> {
    private static final Logger LOGGER = LogManager.getLogger(CityExteriorComponent.class);

    public CityExteriorComponent(CityEntity owner) {
        super(owner);
    }

    public void setCityExterior(int templateId, long timeoutTsMs) {
        DressResTemplate template = ResHolder.getTemplate(DressResTemplate.class, templateId);
        if (template.getDressType() == DressType.DRESS_CITY_VALUE) {
            setDress(templateId, timeoutTsMs);
            return;
        }
        if (template.getDressType() == DressType.DRESS_NAME_PLATE_VALUE) {
            setNameplate(templateId, timeoutTsMs);
            return;
        }
    }

    /**
     * 设置基地皮肤
     *
     * @param dressTemplateId 皮肤配表id
     * @param timeoutTsMs     过期时间（0为永久）
     */
    private void setDress(int dressTemplateId, long timeoutTsMs) {
        if (dressTemplateId == getOwner().getProp().getDressTemplateId()) {
            throw new GeminiException(ErrorCode.DRESS_DUPLICATED);
        }
        int oldSkin = getOwner().getProp().getDressTemplateId();
        getOwner().getProp().setDressTemplateId(dressTemplateId).setDressTimeoutTsMs(timeoutTsMs);
        // 清除原皮肤加成
        if (oldSkin > 0) {
            getOwner().getScenePlayer().getAdditionComponent().removeAdditionBySource(AdditionSourceType.AST_CITY_DRESS);
        }
        getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_CITY_DRESS);
        LOGGER.info("setDress {} {}", getOwner(), dressTemplateId);
        if (dressTemplateId == 0 || timeoutTsMs == 0) {
            return;
        }
        getOwner().addSceneSchedule(SceneTimerReason.TIMER_CITY_DRESS, timeoutTsMs - getOwner().getScene().now());
    }

    /**
     * 设置基地铭牌
     *
     * @param templateId  铭牌配表id
     * @param timeoutTsMs 过期时间（0为永久）
     */
    private void setNameplate(int templateId, long timeoutTsMs) {
        if (templateId == getOwner().getProp().getNameplateId()) {
            throw new GeminiException(ErrorCode.DRESS_DUPLICATED);
        }
        int oldNameplateId = getOwner().getProp().getNameplateId();
        getOwner().getProp().setNameplateId(templateId).setNameplateTimeoutTsMs(timeoutTsMs);
        // 清除原皮肤加成
        if (oldNameplateId > 0) {
            getOwner().getScenePlayer().getAdditionComponent().removeAdditionBySource(AdditionSourceType.AST_CITY_NAMEPLATE);
        }
        getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_CITY_NAMEPLATE);
        LOGGER.info("setNameplate {} {}", getOwner(), templateId);
        if (templateId == 0 || timeoutTsMs == 0) {
            return;
        }
        getOwner().addSceneSchedule(SceneTimerReason.TIMER_CITY_NAMEPLATE, timeoutTsMs - getOwner().getScene().now());
    }

    public void onDressExpired() {
        if (getOwner().getProp().getDressTimeoutTsMs() > getOwner().getScene().now()) {
            LOGGER.error("onDressExpired but ts not match {} {} {}", getOwner(), getOwner().getProp().getDressTimeoutTsMs(), getOwner().getScene().now());
            return;
        }
        //设置为默认皮肤
        int oldSkin = getOwner().getProp().getDressTemplateId();
        getOwner().getProp().setDressTimeoutTsMs(0).setDressTemplateId(0);
        // 清除原皮肤加成
        if (oldSkin > 0) {
            getOwner().getScenePlayer().getAdditionComponent().removeAdditionBySource(AdditionSourceType.AST_CITY_DRESS);
        }
    }

    public void onNameplateExpired() {
        if (getOwner().getProp().getNameplateTimeoutTsMs() > getOwner().getScene().now()) {
            LOGGER.error("onNameplateExpired but ts not match {} {} {}", getOwner(), getOwner().getProp().getNameplateTimeoutTsMs(), getOwner().getScene().now());
            return;
        }
        //设置为默认铭牌
        int oldNameplate = getOwner().getProp().getNameplateId();
        getOwner().getProp().setNameplateId(0).setNameplateTimeoutTsMs(0);
        // 清除原铭牌加成
        if (oldNameplate > 0) {
            getOwner().getScenePlayer().getAdditionComponent().removeAdditionBySource(AdditionSourceType.AST_CITY_NAMEPLATE);
        }
    }
}

package com.yorha.cnc.scene.sceneObj.ai.action;

import com.yorha.common.statemachine.Action;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.common.constant.LogConstant.LOG_TYPE_AI;

/**
 * <AUTHOR>
 */
public abstract class AbstractAIAction implements Action<SceneObjAiComponent> {
    protected static final Logger LOGGER = LogManager.getLogger(LOG_TYPE_AI);

    public AbstractAIAction() {
    }


    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        return true;
    }

    @Override
    public void fire(SceneObjAiComponent component) {
        if (component.isDebugAble()) {
            LOGGER.debug("{}, fire and execute :{} ", component.getLogHead(), getActionName());
        }
        this.execute(component);
    }

    protected abstract void execute(SceneObjAiComponent component);

    /**
     * 获取action名
     **/
    protected abstract String getActionName();

    public void onEnter(SceneObjAiComponent component){
        if (component.isDebugAble()) {
            LOGGER.info("{}, onEnter action :{}", component.getLogHead(), getActionName());
        }
        // 出生触发
        component.trigger(CommonEnum.MonsterTriggerType.MTT_ENTER);
    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        if (component.isDebugAble()) {
            LOGGER.info("{}, onEnd action :{}", component.getLogHead(), getActionName());
        }
        component.clearAllTag();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        return EqualsBuilder.reflectionEquals(this, obj);
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }
}

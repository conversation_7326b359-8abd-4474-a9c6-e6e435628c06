package com.yorha.cnc.scene.logisticsPlane.component;

import com.yorha.cnc.scene.event.MoveEndEvent;
import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;
import com.yorha.cnc.scene.sceneObj.move.IMoveArriveHandler;
import com.yorha.cnc.scene.sceneObj.move.IMoveTargetLoseHandler;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.MoveProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.common.utils.FormulaUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstConpensationTemplate;

import java.util.function.Consumer;

import static com.yorha.proto.CommonEnum.BuffEffectType.ET_SPEED_UP_PERCENT;

/**
 * 运输机移动模块
 *
 * <AUTHOR>
 */
public class LogisticsPlaneMoveComponent extends SceneObjMoveComponent {
    private static final Logger LOGGER = LogManager.getLogger(LogisticsPlaneMoveComponent.class);

    public LogisticsPlaneMoveComponent(LogisticsPlaneEntity owner) {
        super(owner);
    }

    @Override
    public LogisticsPlaneEntity getOwner() {
        return (LogisticsPlaneEntity) super.getOwner();
    }

    @Override
    protected TroopProp getTroopProp() {
        return null;
    }

    @Override
    protected MoveProp getMoveProp() {
        return getOwner().getProp().getMove();
    }

    @Override
    protected void initMoveSpeedFromTroopProp() {
        moveSpeed = (int) FormulaUtils.f1(ResHolder.getInstance().getConstTemplate(ConstConpensationTemplate.class).getTransferTroopSpeed(), 0, getOwner().getPlayer().getAdditionComponent().getAddition(ET_SPEED_UP_PERCENT), 1);
    }

    @Override
    protected boolean moveToTarget(SceneObjEntity targetEntity, TroopInteractionType interactionType, IMoveArriveHandler arriveHandler, IMoveTargetLoseHandler loseHandler, boolean isAsync, Consumer<Integer> failHandler) {
        boolean isArrived = super.moveToTarget(targetEntity, interactionType, arriveHandler, loseHandler, isAsync, failHandler);
        if (isArrived) {
            return true;
        }
        // 是回自己的主城 不加小箭头
        if (targetEntity == getOwner().getPlayer().getMainCity()) {
            return false;
        }
        // 设置小箭头
        targetEntity.getArrowComponent().addArrowItem(getOwner());
        return false;
    }

    @Override
    public void onEndMove() {
        super.onEndMove();
        getOwner().getEventDispatcher().dispatch(new MoveEndEvent(getEntityId()));
    }


    @Override
    protected void onRefreshMove() {
        super.onRefreshMove();
        final CommonEnum.LogisticsPlaneState state = getOwner().getProp().getState();
        final long moveArriveTime = getMoveArriveTime();
        getOwner().getBehaviourComponent().refreshState(state, moveArriveTime);
        LOGGER.info("LogisticsPlaneMoveComponent onRefreshMove {} {} {}", getOwner().getEntityId(), state, moveArriveTime);
    }

    @Override
    protected int getMoveSearchPathTag(boolean isToTarget) {
        if (getOwner().getProp().getState() == CommonEnum.LogisticsPlaneState.LPS_RETURN) {
            return GameLogicConstants.AIRPORT_MOVE;
        }
        return GameLogicConstants.TRANSPORT_MOVE;
    }
}

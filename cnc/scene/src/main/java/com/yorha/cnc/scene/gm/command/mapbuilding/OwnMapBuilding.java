package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class OwnMapBuilding implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int partId = 0;
        String key = "partId";
        if (args.containsKey(key)) {
            partId = Integer.parseInt(args.get(key));
        }
        SceneClanEntity sceneClan = actor.getScenePlayer(playerId).getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        MapBuildingEntity mapBuilding = actor.getScene().getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            throw new GeminiException(ErrorCode.MAP_GRID_NOT_EXIST);
        }
        if (mapBuilding.getTransformComponent().isDarkAltarCross()) {
            throw new GeminiException(ErrorCode.MAP_GRID_NOT_EXIST);
        }
        mapBuilding.getStageMgrComponent().gmSetOwner(sceneClan, playerId, sceneClan.getZoneId());
    }

    @Override
    public String showHelp() {
        return "OwnMapBuilding partId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAPBUILDING;
    }
}

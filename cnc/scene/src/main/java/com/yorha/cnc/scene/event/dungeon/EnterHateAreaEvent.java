package com.yorha.cnc.scene.event.dungeon;

import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class EnterHateAreaEvent extends IEvent {
    private final int groupId;
    private final int monsterId;

    public EnterHateAreaEvent(int groupId, int monsterId) {
        this.groupId = groupId;
        this.monsterId = monsterId;
    }

    public int getMonsterId() { return this.monsterId; }

    public int getGroupId() { return this.groupId; }
}

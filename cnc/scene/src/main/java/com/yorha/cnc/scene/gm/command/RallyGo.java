package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class RallyGo implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScenePlayer(playerId);
        if (scenePlayer.getClanId() > 0) {
            SceneClanEntity sceneClan = actor.getScene().getClanMgrComponent().getSceneClan(scenePlayer.getClanId());
            if (sceneClan != null) {
                RallyEntity rallyEntity = sceneClan.getRallyComponent().getRallyEntity(scenePlayer.getRallyComponent().getCurRallyId());
                if (rallyEntity != null && (rallyEntity.getProp().getRallyState() == CommonEnum.RallyState.RS_Preparing || rallyEntity.getProp().getRallyState() == CommonEnum.RallyState.RS_Waiting)) {
                    rallyEntity.getProp().setStateEndTs(SystemClock.now());
                    rallyEntity.getStateComponent().onPrepareStateEnd();
                }
            }
        }
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_NONE;
    }
}

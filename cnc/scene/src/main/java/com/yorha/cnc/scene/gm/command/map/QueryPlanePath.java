package com.yorha.cnc.scene.gm.command.map;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.pathfinding.manager.MainScenePathFindingManager;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneObj.move.PrePath;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryPlanePath implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isBigScene()) {
            throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
        }
        int x1 = Integer.parseInt(args.get("x1"));
        int y1 = Integer.parseInt(args.get("y1"));
        int x2 = Integer.parseInt(args.get("x2"));
        int y2 = Integer.parseInt(args.get("y2"));
        int srcRegion = MapGridDataManager.getRegionId(actor.getScene().getMapId(), x1, y1);
        int endRegion = MapGridDataManager.getRegionId(actor.getScene().getMapId(), x2, y2);

        List<Integer> parts = gmFindPathWithAllCrossing(actor.getBigScene(), srcRegion, endRegion, Point.valueOf(x1, y1));
        StringBuilder string = new StringBuilder();
        string.append("关卡路径\n");
        for (Integer partId : parts) {
            string.append(partId).append("\n");
        }
        MainScenePathFindingManager manager = (MainScenePathFindingManager) actor.getScene().getPathFindMgrComponent().getManager();
        PrePath prePath = manager.findPathWithAllCrossing(0, srcRegion, endRegion, Point.valueOf(x1, y1), Point.valueOf(x2, y2), GameLogicConstants.AIRPORT_MOVE);
        MoveData path = actor.getBigScene().getPathFindMgrComponent().correctNavPath(prePath, Point.valueOf(x2, y2), GameLogicConstants.AIRPORT_MOVE, 0, new GeminiStopWatch("start find path"));
        string.append("坐标路径\n");
        for (Point point : path.getMovePathList()) {
            string.append(point).append("\n");
        }

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(string.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("QueryPlanePath")
                .setSubTitle("QueryPlanePath");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(actor.getScenePlayer(playerId).getZoneId())
                        .build(),
                builder.build());
    }


    /**
     * 使用全连通图寻路
     */
    public List<Integer> gmFindPathWithAllCrossing(BigSceneEntity scene, int srcRegion, int endRegion, Point src) {
        MainScenePathFindingManager manager = scene.getPathFindMgrComponent().getManager();
        List<Integer> regionPath = manager.getRegionMap().findShortestPath(srcRegion, endRegion);
        if (regionPath.isEmpty()) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH);
        }
        MapTemplateDataItem mapTemplateDataItem = scene.getPathFindMgrComponent().getManager().getMapTemplateDataItem();
        List<Point> path = new ArrayList<>();
        Point comparePoint = src;
        List<Integer> ret = new ArrayList<>();
        for (int i = 0; i < regionPath.size() - 1; i++) {
            List<Integer> crossPartId = mapTemplateDataItem.getRegionPartIdList(regionPath.get(i), regionPath.get(i + 1));
            // 竟然不通？？
            if (crossPartId == null || crossPartId.isEmpty()) {
                throw new GeminiException(ErrorCode.MOVE_NO_PATH);
            }
            // 简单点 找个最近的
            int curPartId = 0;
            double minDistance = -1;
            Point choosePoint = null;
            for (int partId : crossPartId) {
                RegionalAreaSettingTemplate template = mapTemplateDataItem.getValueFromMap(RegionalAreaSettingTemplate.class, partId);
                Point target = Point.valueOf(template.getPosX(), template.getPosY());
                double distance = Point.calDisBetweenTwoPoint(target, comparePoint);
                if (minDistance == -1 || distance < minDistance) {
                    minDistance = distance;
                    curPartId = partId;
                    choosePoint = target;
                }
            }
            if (curPartId == 0) {
                throw new GeminiException(ErrorCode.MOVE_NO_PATH);
            }
            // 如果是两段式拼接 那直接加上点就行了 肯定是无视静态阻挡的
            ret.add(curPartId);
            if (path.isEmpty() || !path.get(path.size() - 1).equals(comparePoint)) {
                path.add(comparePoint);
            }
            comparePoint = choosePoint;
        }
        return ret;
    }

    @Override
    public String showHelp() {
        return "QueryPlanePath  x1={value} y1={value} x2={value} y2={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }
}

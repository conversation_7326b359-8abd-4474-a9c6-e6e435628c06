package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.scene.entity.SceneEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class HateComponent extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(HateComponent.class);
    /**
     * 两个联盟的仇恨值
     */
    private final Map<String, Integer> clanHateMap = Maps.newHashMap();

    public HateComponent(SceneEntity owner) {
        super(owner);
    }

    public void addHate(IBattleRoleAdapter one, IBattleRoleAdapter other) {
        addHate(one.getClanId(), other.getClanId());
    }

    public void removeHate(IBattleRoleAdapter one, IBattleRoleAdapter other) {
        removeHate(one.getClanId(), other.getClanId());
    }

    public boolean isHate(IBattleRoleAdapter one, IBattleRoleAdapter other) {
        return isHate(one.getClanId(), other.getClanId());
    }

    public void addHate(long oneClanId, long otherClanId) {
        if (oneClanId == 0 || otherClanId == 0) {
            return;
        }
        String key = getHateKey(oneClanId, otherClanId);
        clanHateMap.put(key, clanHateMap.getOrDefault(key, 0) + 1);
        LOGGER.debug("add hate. key:{}, value:{}", key, clanHateMap.getOrDefault(key, 0));
    }

    public void removeHate(long oneClanId, long otherClanId) {
        String key = getHateKey(oneClanId, otherClanId);
        if (clanHateMap.containsKey(key)) {
            clanHateMap.put(key, clanHateMap.getOrDefault(key, 0) - 1);
            if (clanHateMap.getOrDefault(key, 0) <= 0) {
                clanHateMap.remove(key);
            }
            LOGGER.debug("remove hate. key:{}, value:{}", key, clanHateMap.getOrDefault(key, 0));
        }
    }

    private boolean isHate(long oneClanId, long otherClanId) {
        String key = getHateKey(oneClanId, otherClanId);
        return clanHateMap.getOrDefault(key, 0) > 0;
    }

    private String getHateKey(long oneClanId, long otherClanId) {
        if (oneClanId >= otherClanId) {
            return oneClanId + "-" + otherClanId;
        } else {
            return otherClanId + "-" + oneClanId;
        }
    }
}


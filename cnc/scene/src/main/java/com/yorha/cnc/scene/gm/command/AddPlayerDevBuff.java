package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import res.template.BuffTemplate;

import java.util.Map;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * 战斗力大增强
 * <AUTHOR>
 */
public class AddPlayerDevBuff implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int buffId = Integer.parseInt(args.get("buffId"));
        int lifeSec = Integer.parseInt(args.get("life"));
        int layer = Integer.parseInt(args.get("layer"));
        BuffTemplate valueFromMap = getResHolder().findValueFromMap(BuffTemplate.class, buffId);
        if (valueFromMap == null){
            String result = StringUtils.format("buff配置不存在 buffId={}", buffId);
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, result);
        }
        if (lifeSec <= 0){
            String result = StringUtils.format("层数不能为空 lifeSec={}", lifeSec);
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, result);
        }
        if (layer <= 0){
            String result = StringUtils.format("层数不能为空 layer={}", buffId);
            throw new GeminiException(ErrorCode.SYSTEM_WARNING, result);
        }
        ScenePlayerEntity scenePlayer = actor.getBigScene().getPlayerMgrComponent().getScenePlayer(playerId);
        long now = SystemClock.now();
        scenePlayer.getDevBuffComponent().addDevBuffByLayer(buffId, now, now + TimeUtils.second2Ms(lifeSec), CommonEnum.DevBuffType.DBT_SCENE_BUFF, CommonEnum.DevBuffSourceType.DBST_GM, layer);
    }
}

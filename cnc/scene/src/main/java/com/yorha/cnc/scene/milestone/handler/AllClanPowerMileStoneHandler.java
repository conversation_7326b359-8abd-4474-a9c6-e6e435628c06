package com.yorha.cnc.scene.milestone.handler;

import com.google.common.collect.Lists;
import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

import java.util.List;

/**
 * 全部军团战斗力排名
 * 参数：全部军团战斗力排名前X位
 */
public class AllClanPowerMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_CLAN;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_RANKING_ALL_LEGIONS;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }

    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanPowerData) {
            ClanPowerData data = (ClanPowerData) taskData;
            Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
            MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.addEmptyValue(data.getClanId());
            setClanScore(mileStoneClanInfoProp, data.getClanId(), data.getPower(), "onClanPowerChange");
        }
    }


    /**
     * 满足积分
     */
    @Override
    public List<MileStoneClanInfoProp> filterMeetScore(List<MileStoneClanInfoProp> rankProp) {
        List<MileStoneClanInfoProp> result = Lists.newArrayList();
        String[] taskParam = getTaskParamById();
        if (taskParam.length < 1) {
            LOGGER.error("PlayerFogMileStoneHandler param error. taskParam={} prop={}", taskParam, getProp());
            return result;
        }
        int rankLimit = Integer.parseInt(taskParam[0]);
        for (int i = 0; i < rankProp.size(); i++) {
            if (i >= rankLimit) {
                continue;
            }
            result.add(rankProp.get(i));
        }
        return result;
    }

    public static class ClanPowerData extends MileStoneTaskData {
        private final long clanId;
        private final long power;

        public ClanPowerData(long clanId, long power) {
            super(0);
            this.clanId = clanId;
            this.power = power;
        }

        public long getClanId() {
            return clanId;
        }

        public long getPower() {
            return power;
        }
    }
}

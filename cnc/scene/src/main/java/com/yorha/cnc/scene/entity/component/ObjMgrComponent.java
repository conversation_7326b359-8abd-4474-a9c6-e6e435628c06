package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Lists;
import com.mongodb.lang.Nullable;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import io.vertx.core.impl.ConcurrentHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ObjMgrComponent extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(ObjMgrComponent.class);

    private long printPropRatioTsMs;

    private Map<CommonEnum.SceneObjectEnum, List<Double>> propRatioDTOMap = new HashMap<>();

    /**
     * 这里的ConcurrentHashMap是为了遍历时可以删除， 不是为了线程并发问题！！  暂时不要改动了
     */
    protected final Map<Long, SceneObjEntity> entityMap = new ConcurrentHashMap<>();
    protected final Map<Class<? extends AbstractEntity>, Set<SceneObjEntity>> typeEntityMap = new ConcurrentHashMap<>();


    public ObjMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void callAllObjAfterAllLoad() {
        for (SceneObjEntity sceneObjEntity : entityMap.values()) {
            try {
                sceneObjEntity.callAfterAllLoad();
            } catch (Exception e) {
                WechatLog.error("ObjMgrComponent callAllObjAfterAllLoad {}", sceneObjEntity, e);
            }
        }
    }

    public void checkCityCollision() {
        for (CityEntity cityEntity : getObjsByType(CityEntity.class)) {
            try {
                cityEntity.checkCollision();
            } catch (Exception e) {
                LOGGER.error("checkCityCollision failed {} ", cityEntity, e);
            }
        }
    }

    public void addSceneObjEntity(SceneObjEntity entity) {
        entityMap.put(entity.getEntityId(), entity);
        Set<SceneObjEntity> sceneObjEntities = typeEntityMap.computeIfAbsent(entity.getClass(), k -> new ConcurrentHashSet<>());
        sceneObjEntities.add(entity);
        MonitorUnit.SCENE_OBJ_NUM_GAUGE.labels(ServerContext.getBusId(), entity.getEntityType().name()).set(sceneObjEntities.size());
        if (LOGGER.isDebugEnabled()) {
            // 大世界初始化就不打印了
            if (getOwner().isMainScene() && getOwner().isInitOk()) {
                LOGGER.debug("put {} into mgr, cur num:{}", entity, entityMap.size());
            }
        }
    }

    public void deleteAllObj() {
        for (SceneObjEntity value : entityMap.values()) {
            value.deleteObj();
        }
        typeEntityMap.clear();
        entityMap.clear();
    }

    public boolean isObjOnScene(SceneObjEntity entity) {
        return entityMap.containsKey(entity.getEntityId());
    }

    public void gmClearMonster() {
        List<MonsterEntity> monsters = getObjsByType(MonsterEntity.class);
        final long startTsMs = SystemClock.now();
        final int num = monsters.size();
        for (MonsterEntity monster : monsters) {
            monster.forceRecycle();
        }
        final long endTsMs = SystemClock.now();
        LOGGER.info("gmClearMonster num:{} costMs:{}", num, endTsMs - startTsMs);
    }
    

    public void removeSceneObjEntity(SceneObjEntity entity) {
        SceneObjEntity remove = entityMap.remove(entity.getEntityId());
        if (remove == null) {
            WechatLog.error("remove null entity id:{}, cur num:{}", entity.getEntityId(), entityMap.size());
            return;
        }
        Set<SceneObjEntity> sceneObjEntities = typeEntityMap.get(remove.getClass());
        sceneObjEntities.remove(remove);
        MonitorUnit.SCENE_OBJ_NUM_GAUGE.labels(ServerContext.getBusId(), remove.getEntityType().name()).set(sceneObjEntities.size());
    }

    @Nullable
    public SceneObjEntity getSceneObjEntity(long entityId) {
        return entityMap.get(entityId);
    }

    public SceneObjEntity getSceneObjEntityWithException(long entityId) {
        if (!entityMap.containsKey(entityId)) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        return entityMap.get(entityId);
    }

    public <T extends SceneObjEntity> T getSceneObjWithTypeWithException(Class<T> clazz, long entityId) {
        T sceneObjWithType = getSceneObjWithType(clazz, entityId);
        if (sceneObjWithType == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        return sceneObjWithType;
    }

    @SuppressWarnings("unchecked")
    @Nullable
    public <T extends SceneObjEntity> T getSceneObjWithType(Class<T> clazz, long entityId) {
        SceneObjEntity sceneObjEntity = getSceneObjEntity(entityId);
        if (sceneObjEntity == null) {
            return null;
        }
        if (clazz == sceneObjEntity.getClass()) {
            return (T) sceneObjEntity;
        }
        WechatLog.error("getSceneObjWithType error. want class:{}. actual entity:{}", clazz, sceneObjEntity);
        return null;
    }

    @SuppressWarnings("unchecked")
    public <T extends SceneObjEntity> List<T> getObjsByType(Class<T> type) {
        return typeEntityMap.getOrDefault(type, Collections.emptySet()).stream().map(entity -> (T) entity).collect(Collectors.toList());
    }

    public <T extends SceneObjEntity> int getObjsNumByType(Class<T> type) {
        return typeEntityMap.getOrDefault(type, Collections.emptySet()).size();
    }

    public Map<Long, SceneObjEntity> getEntityMap() {
        return entityMap;
    }

    public void openPrintPropRatioByGm() {
        printPropRatioTsMs = SystemClock.now() + Constants.PRINT_RATIO_MS;
        LOGGER.info("openPrintPropRatioByGm now={} end={}", TimeUtils.timeStampMs2String(SystemClock.now()), TimeUtils.timeStampMs2String(printPropRatioTsMs));
    }

    public void collectOrPrintPropRatio(Supplier<Pair<CommonEnum.SceneObjectEnum, Double>> runnable) {
        // 非dev、test环境禁止打印
        if (!ServerContext.isTestEnv() && !ServerContext.isDevEnv()) {
            return;
        }
        if (SystemClock.now() > printPropRatioTsMs) {
            if (printPropRatioTsMs > 0) {
                printPropRatioTsMs = 0;
                printPropRatio();
            }
            return;
        }
        Pair<CommonEnum.SceneObjectEnum, Double> dto = runnable.get();
        if (dto == null) {
            return;
        }
        List<Double> dtoList = propRatioDTOMap.computeIfAbsent(dto.getFirst(), (key) -> new ArrayList<>());
        dtoList.add(dto.getSecond());
    }

    private void printPropRatio() {
        for (Map.Entry<CommonEnum.SceneObjectEnum, List<Double>> entry : propRatioDTOMap.entrySet()) {
            List<Double> value = entry.getValue();
            Collections.sort(value);
            double min = value.get(0);
            double max = value.get(value.size() - 1);
            double avg = value.stream().mapToDouble(it -> it).sum() / value.size();
            int p90Index = (int) (0.9f * value.size());
            double pct90 = value.get(Math.min(p90Index, value.size() - 1));
            LOGGER.info("{} obj={} total={} min={} avg={} max={} pct90={}", LogKeyConstants.PRINT_PROP_RATIO, entry.getKey(), value.size(), min, avg, max, pct90);
        }
        propRatioDTOMap = new HashMap<>();
    }

    /**
     * 获取所有没有升天的CityEntity
     */
    public Collection<CityEntity> getAllUnAscendCityEntity() {
        List<CityEntity> list = Lists.newArrayList();
        for (CityEntity cityEntity : getOwner().getObjMgrComponent().getObjsByType(CityEntity.class)) {
            if (!cityEntity.isPlayerCity()) {
                continue;
            }
            if (cityEntity.getTransformComponent().isAscend()) {
                continue;
            }
            list.add(cityEntity);
        }
        return list;
    }

    /**
     * 获取一个服没有升天的CityEntity
     */
    public Collection<CityEntity> getAllUnAscendCityEntity(int zoneId) {
        List<CityEntity> list = Lists.newArrayList();
        for (CityEntity cityEntity : getOwner().getObjMgrComponent().getObjsByType(CityEntity.class)) {
            if (!cityEntity.isPlayerCity()) {
                continue;
            }
            if (cityEntity.getScenePlayer() == null || cityEntity.getScenePlayer().getZoneId() != zoneId) {
                continue;
            }
            if (cityEntity.getTransformComponent().isAscend()) {
                continue;
            }
            list.add(cityEntity);
        }
        return list;
    }
}

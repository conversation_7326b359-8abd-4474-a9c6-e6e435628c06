package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.bigScene.component.BigSceneMailComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.common.actor.SceneMailService;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.SsSceneMail;
import com.yorha.proto.StructMail;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SceneMailServiceImpl implements SceneMailService {
    private final SceneActor sceneActor;


    public SceneMailServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public BigSceneEntity getBigScene() {
        return sceneActor.getScene().getBigScene();
    }

    @Override
    public void handleSyncPlayerOfflineMailsAsk(SsSceneMail.SyncPlayerOfflineMailsAsk ask) {
        final BigSceneMailComponent sceneMailComponent = this.getBigScene().getMailComponent();
        List<StructMail.NewMailCache> ret = sceneMailComponent.syncPlayerOfflineMails(ask.getZoneMailIndex(), ask.getCreateTime());

        SsSceneMail.SyncPlayerOfflineMailsAns.Builder ans = SsSceneMail.SyncPlayerOfflineMailsAns.newBuilder();
        ans.getNewMailCacheListBuilder().addAllDatas(ret);
        ans.setZoneMailIndex(sceneMailComponent.getZoneMailIndex());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleSendZoneMailCmd(SsSceneMail.SendZoneMailCmd ask) {
        if (!this.sceneActor.getScene().isBigScene()) {
            WechatLog.error("handleSendZoneMailCmd not bigScene, sceneId={}, ask={}", this.sceneActor.getSceneId(), ask);
            return;
        }
        this.getBigScene().getMailComponent().sendZoneMail(ask.getMailId(), ask.getMailSendParams());
    }

}

package com.yorha.cnc.scene.city;

import com.yorha.cnc.mainScene.common.component.MainSceneBornMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.npcplayer.NpcPlayerBuilder;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.GuardTowerHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.city.CityBornService;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.CityProp;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Core;
import com.yorha.proto.EntityAttrDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.MapBuildingTemplate;
import res.template.NpcCityTemplate;

import java.util.List;


/**
 * <AUTHOR>
 */
public class CityFactory {
    private static final Logger LOGGER = LogManager.getLogger(CityFactory.class);

    public static void createCity(SceneEntity scene, long scenePlayerId, Point bornPoint) {
        LOGGER.info("{} create city for {}", scene, scenePlayerId);
        AbstractScenePlayerEntity scenePlayer = scene.getPlayerMgrComponent().getScenePlayer(scenePlayerId);
        // 构建city属性
        CityProp prop = new CityProp();
        prop.setOwnerId(scenePlayer.getEntityId())
                .setLevel(0)
                .setAscendReason(CityAscendReason.CAR_NONE)
                .setCamp(scenePlayer.getCampEnum())
                .setClanId(scenePlayer.getClanId())
                .setZoneId(scenePlayer.getZoneId())
                .setWallState(CityWallState.CS_NORMAL)
                .getPoint().setX(bornPoint.getX()).setY(bornPoint.getY());

        prop.getCardHead().mergeFromSs(scenePlayer.getCardHead().getCopySsBuilder().build());

        if (scenePlayer.getClanId() != 0) {
            SceneClanEntity sceneClan = scene.getClanMgrComponent().getSceneClan(scenePlayer.getClanId());
            if (sceneClan != null) {
                prop.setClanSname(sceneClan.getClanSimpleName());
            }
        }
        // 创建city
        prop.unMarkAll();
        CityBuilder builder = new CityBuilder(scene, scene.ownerActor().nextId(), prop);
        CityEntity cityEntity = new CityEntity(builder);
        cityEntity.addIntoScene();
        if (scene.isMainScene()) {
            cityEntity.getDbComponent().insertIntoDb();
        }
        LOGGER.info("{} create succes", cityEntity);
    }

    public static void restoreCity(SceneEntity sceneEntity, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        CityProp cityProp = CityProp.of(fullAttr.getCityAttr(), changedAttr.getCityAttr());
        long ownerId = cityProp.getOwnerId();
        if (sceneEntity.getPlayerMgrComponent().getScenePlayerOrNull(ownerId) == null) {
            LOGGER.info("restoreCity but is in migrate {} {}", ownerId, fullAttr.getEntityId());
            return;
        }
        CityBuilder builder = new CityBuilder(sceneEntity, fullAttr.getEntityId(), cityProp);
        CityEntity cityEntity = new CityEntity(builder, true);
        cityEntity.addIntoScene();
        LOGGER.info("{} restore {} success", sceneEntity, cityEntity);
        // kvk 兼容线上数据
        if (cityEntity.getZoneId() == 0) {
            cityEntity.getProp().setZoneId(cityEntity.getScenePlayer().getZoneId());
        }
    }

    public static int getCityRadius() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, GameLogicConstants.CITY_CONFIG_TEMPLATE_ID).getCollisionRadius();
    }

    public static Core.Code moveCityVerify(SceneEntity scene, int x, int y, MoveCityType moveCityType, AbstractScenePlayerEntity player) {
        // kvk不允许用新手迁城
        if (moveCityType == MoveCityType.MCT_NEWPLAYER) {
            return ErrorCode.FUNCTION_NOT_OPEN.getCode();
        }
        // 自身状态检测  部队/被集结战斗
        Core.Code code = cityMovePlayerCheck(player, moveCityType);
        if (!ErrorCode.isOK(code)) {
            return code;
        }
        //通用检测    合法点 和 禁止迁城区域   不可迁城到别的联盟的领土区域
        code = basicMoveCityVerify(scene, x, y, player.getClanId());
        if (!ErrorCode.isOK(code)) {
            return code;
        }
        // 格子占领检查  一个城半径7.5m, 格子变成10m, 所在的格子肯定不能再迁城
        if (!gridCheck(scene, x, y, player.getEntityId())) {
            return ErrorCode.MAP_DYNAMIC_COLLISION.getCode();
        }
        //业务检测
        code = logicMoveCityVerify(scene, x, y, moveCityType, player);
        if (!ErrorCode.isOK(code)) {
            return code;
        }
        //地貌检查（等有雪原啥的再加）

        //阻挡检查
        code = BornPointHelper.collisionCheck(scene, x, y, getCityRadius(), player.getMainCity().getEntityId());
        if (!ErrorCode.isOK(code)) {
            return code;
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 格子占领检查
     *
     * @param x 坐标x
     * @param y 坐标y
     * @return true or false
     */
    private static boolean gridCheck(SceneEntity scene, int x, int y, long playerId) {
        MainSceneBornMgrComponent bornMgrComponent = scene.getBornMgrComponent();
        if (bornMgrComponent == null) {
            return true;
        }
        int gridId = MapGridDataManager.getGridId(scene.getMapId(), x, y);
        CityEntity city = bornMgrComponent.getOccupiedCity(gridId);
        if (city == null) {
            return true;
        }
        // 占领地格的是自己的城池
        return city.getPlayerId() == playerId;
    }

    public static Core.Code cityMovePlayerCheck(AbstractScenePlayerEntity player, MoveCityType moveCityType) {
        if (player.getMainCity().getTransformComponent().isAscend()) {
            return ErrorCode.CITY_CITY_IS_ASCEND.getCode();
        }
        // 城外部队检查
        if (!player.getArmyMgrComponent().checkCanMoveCity()) {
            return ErrorCode.MAP_ARMY_OUTSIDE.getCode();
        }
        // 城外运输机检查
        if (player.getPlaneComponent().getLogisticPlaneEntityNum() > 0) {
            return ErrorCode.MAP_ARMY_OUTSIDE.getCode();
        }
        CityEntity mainCity = player.getMainCity();
        if (mainCity != null) {
            // 被集结战斗中检查
            if (mainCity.getBattleComponent().hasBattleWithSceneObjByType(SceneObjType.SOT_ARMY_GROUP)) {
                return ErrorCode.MAP_MAP_BE_RALLY.getCode();
            }
            // 王国技能，因为王国技能-驱逐触发的，不需要校验
            if (moveCityType != MoveCityType.MCT_KINGDOM_SKILL) {
                ErrorCode errorCode = mainCity.getTransformComponent().checkKingdomSkill();
                if (errorCode.isNotOk()) {
                    return errorCode.getCode();
                }
            }
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 通用迁城检查
     *
     * @return 错误码
     */
    private static Core.Code basicMoveCityVerify(SceneEntity scene, int x, int y, long clanId) {
        // 坐标点合法校验
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(Point.valueOf(x, y), scene.getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        if (x < getCityRadius() || scene.getMapWidth() - x < getCityRadius()) {
            return ErrorCode.MAP_MOVE_CITY_OUT_OF_MAP.getCode();
        }
        if (y < getCityRadius() || scene.getMapHeight() - y < getCityRadius()) {
            return ErrorCode.MAP_MOVE_CITY_OUT_OF_MAP.getCode();
        }
        if (!scene.getCityMoveComponent().isPointNavMovable(Point.valueOf(x, y))) {
            return ErrorCode.MAP_STATIC_COLLISION.getCode();
        }
        if (!scene.isMainScene()) {
            return ErrorCode.OK.getCode();
        }
        // 禁止迁城区域检查
        MapAreaType areaType = MapGridDataManager.getAreaType(scene.getMapId(), x, y);
        ErrorCode areaBornCode = ResHolder.getResService(CityBornService.class).getAreaBornCode(areaType);
        if (areaBornCode.isNotOk()) {
            return areaBornCode.getCode();
        }
        // 非大世界不用做下面的检测  kvk不在这里做哈
        if (!scene.isBigScene()) {
            return ErrorCode.OK.getCode();
        }
        //不可迁城至别联盟的领土内
        int partId = MapGridDataManager.getPartId(scene.getMapId(), x, y);
        long ownerId = scene.ownerActor().getBigScene().getBuildingMgrComponent().getOwnerIdByPartId(partId);
        if (ownerId != 0 && clanId != ownerId) {
            return ErrorCode.CITY_CANT_CLAN_MOVE.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 业务检测
     *
     * @param x            坐标x
     * @param y            坐标y
     * @param moveCityType 迁城类型
     * @param player       玩家实体
     * @return 错误码
     */
    private static Core.Code logicMoveCityVerify(SceneEntity scene, int x, int y, MoveCityType moveCityType, AbstractScenePlayerEntity player) {
        //新手迁城检测
        if (moveCityType == MoveCityType.MCT_NEWPLAYER) {
            int regionId = MapGridDataManager.getRegionId(scene.getMapId(), x, y);
            List<Integer> list = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getItemMoveCityNewPlayerRegion();
            if (!list.contains(regionId)) {
                return ErrorCode.MAP_ARMY_OUTSIDE.getCode();
            }
        }
        //领地迁城检测
        if (moveCityType == MoveCityType.MCT_TERRITORY) {
            if (!player.isInClan()) {
                return ErrorCode.CLAN_NOT_IN.getCode();
            }
            long ownerClanId = scene.getBuildingMgrComponent().getOwnerIdByPartId(x, y);
            // 大世界判自己的盟
            if (scene.isBigScene() && ownerClanId != player.getClanId()) {
                return ErrorCode.MAP_ERROR_TERRITORY.getCode();
            }
        }
        //州联通检查
        if (moveCityType == MoveCityType.MCT_NORMAL) {
            // 起始点不在地图内都通过
            Point curPoint = player.getMainCity().getCurPoint();
            // 州联通校验
            SceneClanEntity sceneClan = player.getSceneClan();
            if (sceneClan == null) {
                int regionId1 = MapGridDataManager.getRegionId(scene.getMapId(), curPoint);
                int regionId2 = MapGridDataManager.getRegionId(scene.getMapId(), x, y);
                if (regionId1 != regionId2) {
                    return ErrorCode.MAP_REGION_NOTCONNECT.getCode();
                }
            } else if (!sceneClan.getCrossComponent().checkSrcToEndCanPass(curPoint, Point.valueOf(x, y))) {
                return ErrorCode.MAP_REGION_NOTCONNECT.getCode();
            }
        }
        // 随机迁城检查目标点是本州
        if (moveCityType == MoveCityType.MCT_RANDOM) {
            Point curPoint = player.getMainCity().getCurPoint();
            if (curPoint.containsPoint(x, y)) {
                return ErrorCode.MAP_MOVE_CITY_INVAILD_POINT.getCode();
            }
            if (!MapGridDataManager.isSameRegion(scene.getMapId(), curPoint.getX(), curPoint.getY(), x, y, player.getScene().getMapConfig())) {
                return ErrorCode.MAP_MOVE_CITY_INVAILD_POINT.getCode();
            }
        }
        return ErrorCode.OK.getCode();
    }

    public static void createNpcCity(SceneEntity sceneEntity, int uniqueId, int templateId, int x, int y) {
        NpcCityTemplate template = ResHolder.getInstance().getValueFromMap(NpcCityTemplate.class, templateId);
        // 创城先创player
        NpcPlayerBuilder playerBuilder = new NpcPlayerBuilder();
        long id = sceneEntity.ownerActor().nextId();
        NpcPlayerEntity player = new NpcPlayerEntity(sceneEntity, id, playerBuilder);
        sceneEntity.getPlayerMgrComponent().addNpcPlayer(player);
        // 创城
        CityProp prop = new CityProp();
        int guardTowerHpMax = GuardTowerHelper.getGuardTowerHpMax(template.getGuardTowerId());
        prop.setOwnerId(id)
                .setLevel(template.getLevel())
                .setCamp(template.getCamp())
                .setTemplateId(templateId)
                .setGuardTowerHp(guardTowerHpMax)
                .getPoint().setX(x).setY(y);
        prop.unMarkAll();
        CityBuilder builder = new CityBuilder(sceneEntity, id, prop);
        CityEntity city = new CityEntity(builder);
        city.setUniqueId(uniqueId);
        city.addIntoScene();
        LOGGER.info("{} createNpcCity city: {} template: {}", sceneEntity, city, templateId);
    }
}

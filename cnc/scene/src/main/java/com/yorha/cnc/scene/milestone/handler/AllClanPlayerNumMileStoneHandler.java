package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

/**
 * 全部军团中超过一定人数的军团数量
 * 参数：要求军团数量_要求军团人数
 */
public class AllClanPlayerNumMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public void updateProcess(MileStoneTaskData data) {
        if (data instanceof ClanPlayerNumData) {
            String[] taskParam = getTaskParamById();
            int numLimit = Integer.parseInt(taskParam[0]);
            int scoreLimit = Integer.parseInt(taskParam[1]);
            int count = 0;
            for (MileStoneClanInfoProp clanProp : getProp().getRankInfo().getRankInfoMap().values()) {
                if (clanProp.getScore() >= scoreLimit) {
                    count++;
                }
            }
            getProp().setProcess(Math.min(numLimit, count));
        }
    }

    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanPlayerNumData) {
            ClanPlayerNumData data = (ClanPlayerNumData) taskData;
            Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
            MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.addEmptyValue(data.getClanId());
            setClanScore(mileStoneClanInfoProp, data.getClanId(), data.getMemberNum(), "onClanPlayerMemberChange");
        }
    }

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_ALL_CLAN;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_CONDITIONAL_LEGION_SIZE;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END;
    }

    public static class ClanPlayerNumData extends MileStoneTaskData {
        private final long clanId;
        private final int memberNum;

        public ClanPlayerNumData(long clanId, int memberNum) {
            super(0);
            this.clanId = clanId;
            this.memberNum = memberNum;
        }

        public long getClanId() {
            return clanId;
        }

        public int getMemberNum() {
            return memberNum;
        }
    }
}

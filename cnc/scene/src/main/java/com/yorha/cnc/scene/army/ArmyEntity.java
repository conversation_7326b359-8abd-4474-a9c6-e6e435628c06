package com.yorha.cnc.scene.army;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.component.*;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.WarningInfoType;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.HeroHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB.RallyArmyInfoPB;
import com.yorha.proto.StructPlayerPB.TroopPB;
import com.yorha.proto.StructPlayerPB.WarningInfoPB;
import com.yorha.proto.TcaplusDb;
import res.template.ConstSettingTemplate;
import res.template.ConstSpyTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ArmyEntity extends SceneObjEntity implements WarningInfoType {

    private final ArmyProp prop;
    private final ArmyBuffComponent buffComponent = new ArmyBuffComponent(this);
    private final ArmyBehaviourComponent behaviourComponent = new ArmyBehaviourComponent(this);
    private final ArmyRallyComponent rallyComponent = new ArmyRallyComponent(this);
    private final ArmyAssistComponent assistComponent = new ArmyAssistComponent(this);
    private final ArmyTransportComponent transportComponent = new ArmyTransportComponent(this);
    private final ArmyPickUpComponent pickUpComponent = new ArmyPickUpComponent(this);
    private final ArmyAdditionComponent additionComponent = new ArmyAdditionComponent(this);
    private final ArmyPlunderComponent plunderComponent = new ArmyPlunderComponent(this);
    private final ArmyResourceComponent resourceComponent = new ArmyResourceComponent(this);
    private final ArmyCollectComponent collectComponent = new ArmyCollectComponent(this);
    private final ArmyHuntingComponent huntingComponent = new ArmyHuntingComponent(this);
    private final ArmyStatusComponent statusComponent;
    /**
     * 构造时依赖army prop 只能放在构造函数里了
     */
    private final ArmyBattleComponent battleComponent;
    private final ArmyMoveComponent moveComponent;

    private AbstractScenePlayerEntity player = null;

    public ArmyEntity(ArmyBuilder builder) {
        this(builder, false);
    }

    public ArmyEntity(ArmyBuilder builder, boolean isRestore) {
        super(builder);
        this.statusComponent = new ArmyStatusComponent(this, builder.getStatusProp());
        this.prop = builder.getProp();
        // NOTE(furson): MoveComponent初始化必须在BattleComponent之前！
        this.moveComponent = builder.moveComponent(this);
        this.battleComponent = builder.battleComponent(this);
        initAllComponents();
        getPropComponent().initPropListener(isRestore);
        // 设置拥有者永远同步
        getAoiNodeComponent().setAlwaysSyncPlayer(getPlayerId());
    }

    public AbstractScenePlayerEntity getScenePlayer() {
        if (player != null) {
            return player;
        }
        long ownerId = getProp().getOwnerId();
        player = getScene().getPlayerMgrComponent().getScenePlayerOrNull(ownerId);
        if (player == null) {
            player = getScene().getPlayerMgrComponent().getNpcPlayer(ownerId);
        }
        return player;
    }

    @Override
    public void callAfterAllLoad() {
        // 援助采集状态不用处理
        AttachState attachState = getProp().getAttachState();
        if (attachState == AttachState.AAS_COLLECT || attachState == AttachState.AAS_ASSIST) {
            super.callAfterAllLoad();
            return;
        }
        // 非援助采集状态， 直接回城
        getMoveComponent().onReturnCityEnd();
        LOGGER.warn("army load but return main city {}", this);
    }

    // ------------------------------------------- 必须实现 -------------------------------------------
    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        if (super.onTickDispatch(reason)) {
            return true;
        }
        switch (reason) {
            case TICK_BATTLE:
                getBattleComponent().onTick();
                return true;
            default:
                return false;
        }
    }

    @Override
    public boolean onTimerDispatch(SceneTimerReason reason) {
        if (super.onTimerDispatch(reason)) {
            return true;
        }
        switch (reason) {
            case TIMER_PICK:
                getPickUpComponent().checkPickUpEnd();
                return true;
            case TIMER_MODEL_UPDATE:
                getTransformComponent().timerToUpdateModelRadius();
                return true;
        }
        return false;
    }

    @Override
    public ArmyProp getProp() {
        return prop;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Army;
    }

    @Override
    public int getZoneId() {
        return getProp().getZoneId();
    }

    @Override
    public void fullCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getArmyAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getArmyAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getArmyAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getArmyAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getArmyAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final ArmyProp armyProp = ArmyProp.of(builder.getFullAttr().getArmyAttr(), builder.getChangedAttr().getArmyAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setArmyAttr(armyProp.getCopyDbBuilder());
    }

    @Override
    public ArmyBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return SceneObjectEnum.SOE_ARMY;
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        if (isRallyArmy()) {
            prop.getTarget().setTargetType(ArmyTargetType.ATT_RALLY_ARMY);
        } else {
            prop.getTarget().setTargetType(ArmyTargetType.ATT_ARMY);
        }
        prop.getTarget().setName(getPlayerName()).setClanSimpleName(getClanBriefName()).setTemplateId(0).getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }
// ------------------------------------------- 生命周期 -------------------------------------------

    @Override
    public void deleteObj() {
        getDbComponent().deleteDb();
        getAoiNodeComponent().clearAlwaysSyncPlayer();
        super.deleteObj();
        getScenePlayer().getArmyMgrComponent().removeArmy(this);
    }

    @Override
    public SceneObjectNtfReason getRemoveReason() {
        return SceneObjectNtfReason.SONR_RETURN_CITY;
    }

    // ------------------------------------------- component -------------------------------------------

    @Override
    public ArmyMoveComponent getMoveComponent() {
        return moveComponent;
    }

    @Override
    public ArmyBuffComponent getBuffComponent() {
        return buffComponent;
    }

    public ArmyBehaviourComponent getBehaviourComponent() {
        return behaviourComponent;
    }

    public ArmyRallyComponent getRallyComponent() {
        return rallyComponent;
    }

    public ArmyAssistComponent getAssistComponent() {
        return assistComponent;
    }

    @Override
    public ArmyPropComponent getPropComponent() {
        return (ArmyPropComponent) super.getPropComponent();
    }

    @Override
    public ArmyTransformComponent getTransformComponent() {
        return (ArmyTransformComponent) super.getTransformComponent();
    }

    public ArmyTransportComponent getTransportComponent() {
        return transportComponent;
    }

    public ArmyPickUpComponent getPickUpComponent() {
        return pickUpComponent;
    }

    @Override
    public ArmyAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    public ArmyPlunderComponent getPlunderComponent() {
        return plunderComponent;
    }

    public ArmyResourceComponent getResourceComponent() {
        return resourceComponent;
    }

    public ArmyCollectComponent getCollectComponent() {
        return collectComponent;
    }

    public ArmyStatusComponent getStatusComponent() {
        return statusComponent;
    }

    public ArmyHuntingComponent getHuntingComponent() {
        return huntingComponent;
    }

    // ------------------------------------------- 快捷方法 -------------------------------------------

    @Override
    public long getPlayerId() {
        return getProp().getOwnerId();
    }

    @Override
    public long getClanId() {
        return getProp().getClanId();
    }

    @Override
    public Camp getCampEnum() {
        return getProp().getCamp();
    }

    @Override
    public void addIntoScene() {
        // 创建出来秒进入集结或者援助
        if (getProp().getAttachId() == 0 || !getScene().isMainScene()) {
            getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_BORN);
        }
        getScene().getObjMgrComponent().addSceneObjEntity(this);
    }

    @Override
    public ErrorCode canBattleWithCode(SceneObjEntity target, boolean needCheckSiegeLimit) {
        ErrorCode errorCode = super.canBattleWithCode(target, needCheckSiegeLimit);
        if (errorCode.isNotOk()) {
            return errorCode;
        }
        // 空运中 无法战斗
        if (isInTransport()) {
            return ErrorCode.BATTLE_CANT;
        }
        // 溃败中 无法战斗
        if (getProp().getArmyState() == ArmyState.AS_Retreating) {
            return ErrorCode.BATTLE_CANT;
        }
        return ErrorCode.OK;
    }

    @Override
    public void setExpression(int expressionId) {
        getProp().getExpression().setExpressionId(expressionId).setTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(ResHolder.getConsts(ConstSettingTemplate.class).getExpressionLastTime()));
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack();
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack();
    }

    private ErrorCode commonCheckBeAttack() {
        // 空运中 无法被发起战斗
        if (isInTransport()) {
            return ErrorCode.BATTLE_CANT;
        }
        // 溃败中 无法被发起战斗
        if (getProp().getArmyState() == ArmyState.AS_Retreating) {
            return ErrorCode.BATTLE_CANT;
        }
        // 援助，集结中 无法被发起战斗
        RallyArmyRoleType rallyRole = getProp().getRallyRole();
        if (rallyRole == RallyArmyRoleType.RART_Follower || rallyRole == RallyArmyRoleType.RART_Leader) {
            return ErrorCode.BATTLE_CANT;
        }
        return ErrorCode.OK;
    }

    @Override
    public boolean canBeSpy(long enemyPlayerId) {
        //主城等级差大于10级时不能侦查
        AbstractScenePlayerEntity enemy = getScene().getPlayerMgrComponent().getScenePlayer(enemyPlayerId);
        if (enemy == null) {
            return false;
        }
        int levelDiff = getScenePlayer().getMainCity().getLevel() - enemy.getMainCity().getLevel();
        return levelDiff <= ResHolder.getInstance().getConstTemplate(ConstSpyTemplate.class).getSpyMaxBaseLevelDifference();
    }

    /**
     * 是否在运输状态（起飞、运输、降落、运输遣返）
     */
    public boolean isInTransport() {
        ArmyState armyState = getProp().getArmyState();
        return GameLogicConstants.isTransportState(armyState);
    }

    @Override
    public ErrorCode canBeRallyWithCode() {
        // 集结体可以被集结
        return isRallyArmy() ? ErrorCode.OK : ErrorCode.RALLY_CANT;
    }

    /**
     * 是否处于集结中
     */
    public boolean isInRally() {
        return getRallyComponent().getCurRallyId() != 0;
    }

    /**
     * 是否是集结体
     */
    public boolean isRallyArmy() {
        return getProp().getRallyRole() == RallyArmyRoleType.RART_RallySelf;
    }

    public boolean isInAssist() {
        return getProp().getCurAssistTargetId() != 0
                && getProp().getRallyRole() == RallyArmyRoleType.RART_Follower;
    }

    @Override
    public boolean isInClanTerritory() {
        if (getClanId() == 0 || !getScene().isMainScene()) {
            return false;
        }
        return getScene().getBuildingMgrComponent().getOwnerIdByPartId(getCurPoint()) == getClanId();
    }

    /**
     * 获取当前所处的集结entity
     */
    public RallyEntity getRallyEntity() {
        if (!isInRally()) {
            return null;
        }
        if (getScene().isMainScene()) {
            SceneClanEntity sceneClan = getScene().getClanMgrComponent().getSceneClan(getClanId());
            if (sceneClan == null) {
                return null;
            }
            return sceneClan.getRallyEntity(getRallyComponent().getCurRallyId());
        }
        return getScenePlayer().getRallyComponent().getRallyEntity(getRallyComponent().getCurRallyId());
    }

    /**
     * 获取存活的士兵数目
     */
    public int getAliveSoldierNum() {
        return getBattleComponent().aliveCount();
    }

    /**
     * 获取存活的士兵map
     */
    public Map<Integer, Integer> getAliveSoldierMap(boolean withSlight) {
        return getBattleComponent().getBattleRole().aliveSoldiers(withSlight);
    }

    public Point getMainCityPoint() {
        if (getScenePlayer().getMainCity() == null) {
            return null;
        }
        return getScenePlayer().getMainCity().getCurPoint();
    }

    /**
     * 检查是否死亡
     * 仅设置成溃败状态 但是不会立刻回城  集结专用
     */
    public void checkDie() {
        if (!getBattleComponent().hasAnyAlive() && getProp().getArmyState() != ArmyState.AS_Retreating) {
            getProp().setArmyState(ArmyState.AS_Retreating);
            getBehaviourComponent().setRecvPlayerAction(false);
        }
    }

    /**
     * 登录检查败退部队的情况
     */
    public void checkRetreat() {
        // 集结部队或者在集结里的部队不处理
        if (isRallyArmy() || isInRally()) {
            return;
        }
        // 活着的不处理
        if (getBattleComponent().hasAnyAlive()) {
            return;
        }
        // 正在向主堡移动的不处理
        if (getMoveComponent().isMoving() && getMoveComponent().getCurChaseTargetId() == getScenePlayer().getMainCity().getEntityId()) {
            return;
        }
        getMoveComponent().retreat();
        LOGGER.warn("checkRetreat army={} player={}", this, getEntityId());
    }

    public void checkOnCityMove() {
        // 集结部队或者在集结里的部队不处理
        if (isRallyArmy() || isInRally()) {
            return;
        }
        // 正在向主堡移动的不处理
        if (getMoveComponent().isMoving() && getMoveComponent().getCurChaseTargetId() == getScenePlayer().getMainCity().getEntityId()) {
            getMoveComponent().returnMainCity();
        }
    }

    public int getArmyHeroPower() {
        int power = 0;
        HeroProp mainHero = getProp().getTroop().getMainHero();
        if (mainHero.getHeroId() != 0) {
            power += HeroHelper.getHeroPower(mainHero.getCopySsBuilder().build());
        }
        HeroProp deputyHero = getProp().getTroop().getDeputyHero();
        if (deputyHero.getHeroId() != 0) {
            power += HeroHelper.getHeroPower(deputyHero.getCopySsBuilder().build());
        }
        return power;
    }

    /**
     * 驻守的城进入战斗
     */
    public void changeStateByAssistCity(ArmyState state) {
        getProp().setArmyState(state);
    }

    /**
     * copy到预警界面的pb
     */
    @Override
    public boolean copyToWarningInfo(WarningInfoPB.Builder builder) {
        long curChaseTargetId = getMoveComponent().getCurChaseTargetId();
        builder.setArmyId(getEntityId()).setPlayerName(getPlayerName())
                .setClanSimpleName(getProp().getClanSname())
                .setArriveTs(getMoveComponent().getMoveArriveTime());
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntity(curChaseTargetId);
        if (target == null) {
            return false;
        }
        if (getCollectComponent().getCollectTarget() != 0) {
            SceneObjBattleComponent battleComponent = target.getBattleComponent();
            if (battleComponent != null) {
                target = battleComponent.getOwner();
            } else {
                return false;
            }
        }
        builder.setEntityType(target.getEntityType().getNumber()).setTargetId(curChaseTargetId);
        if (target instanceof WarningInfoType) {
            ((WarningInfoType) target).formWarningInfo(builder);
        }
        copyAliveTroop(builder.getTroopBuilder());
        return true;
    }

    /**
     * 构建集结信息面板的显示结构数据
     */
    public RallyArmyInfoPB.Builder getRallyArmyInfoBuilder(RallyArmyRoleType type) {
        RallyArmyInfoPB.Builder builder = RallyArmyInfoPB.newBuilder();
        long now = SystemClock.now();
        builder.setArmyId(getEntityId())
                .setOwnerId(getPlayerId())
                .setRallyRole(type)
                .setArmyState(getProp().getArmyState())
                .setArriveTs(now)
                .setJoinTs(now);
        builder.setCardHead(getProp().getCardHead().getCopyCsBuilder());
        copyAliveTroop(builder.getTroopBuilder());
        if (isNpcArmy()) {
            builder.setIsNpc(true);
        }
        return builder;
    }

    /**
     * 构建援助信息面板的显示结构数据
     */
    public RallyArmyInfoPB.Builder getAssistArmyInfoBuilder(RallyArmyRoleType type, long arrivedTsMs) {
        RallyArmyInfoPB.Builder builder = RallyArmyInfoPB.newBuilder();
        builder.setArmyId(getEntityId())
                .setOwnerId(getPlayerId())
                .setRallyRole(type)
                .setArmyState(getProp().getArmyState())
                .setJoinTs(getAssistComponent().getTryAssistTsMs());
        builder.setCardHead(getProp().getCardHead().getCopyCsBuilder());
        if (arrivedTsMs != 0) {
            // 说明已经到达的
            builder.setArriveTs(arrivedTsMs);
        } else {
            // 说明还在路上的
            builder.setArriveTs(getMoveComponent().getMoveArriveTime());
        }
        copyAliveTroop(builder.getTroopBuilder());
        return builder;
    }

    /**
     * 拷贝部队配置  只拷贝存活士兵
     */
    private void copyAliveTroop(TroopPB.Builder builder) {
        // 拷贝英雄
        getProp().getTroop().getMainHero().copyToCs(builder.getMainHeroBuilder());
        getProp().getTroop().getDeputyHero().copyToCs(builder.getDeputyHeroBuilder());

        // 拷贝士兵
        StructPB.Int32SoldierMapPB.Builder troopBuilder = builder.getTroopBuilder();
        for (SoldierProp prop : getProp().getTroop().getTroop().values()) {
            StructPB.SoldierPB.Builder soldierBuilder = StructPB.SoldierPB.newBuilder();
            soldierBuilder.setNum(prop.getNum() - prop.getSlightWoundNum() - prop.getSevereWoundNum() - prop.getDeadNum()).setSoldierId(prop.getSoldierId());
            troopBuilder.putDatas(prop.getSoldierId(), soldierBuilder.build());
        }
    }

    /**
     * copy自身相关属性到RallyEntity的目标数据中
     */
    public void copyToRallyInfo(RallyInfoProp rallyInfoProp) {
        rallyInfoProp
                .setTargetClanShortName(getProp().getClanSname())
                .setTargetId(getEntityId())
                .setTargetClanId(getClanId())
                .setTargetType(RallyTargetType.RTT_RALLY_ARMY)
                .getTargetPos().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        rallyInfoProp.getTargetCardHead().mergeFromSs(getProp().getCardHead().getCopySsBuilder().build());

    }

    public HeroProp getMainHero() {
        return getProp().getTroop().getMainHero();
    }

    public HeroProp getDeputyHero() {
        return getProp().getTroop().getDeputyHero();
    }

    public Int32SoldierMapProp getTroop() {
        return getProp().getTroop().getTroop();
    }

    public String getClanBriefName() {
        return getProp().getClanSname();
    }

    @Override
    public int getLevel() {
        CityEntity cityEntity;
        if (isRallyArmy()) {
            cityEntity = getRallyEntity().getLeaderArmy().getScenePlayer().getMainCity();

        } else {
            cityEntity = getScenePlayer().getMainCity();
        }

        if (cityEntity == null) {
            throw new GeminiException("army getLevel error city is null:{}", getRallyEntity().getLeaderArmy().getScenePlayer().getPlayerId());
        }
        return cityEntity.getLevel();
    }

    public String getPlayerName() {
        return getProp().getCardHead().getName();
    }

    public int getPic() {
        return getProp().getCardHead().getPic();
    }

    public boolean isNpcArmy() {
        return getScenePlayer() instanceof NpcPlayerEntity;
    }

    public void addWarningItem(SceneObjEntity obj, WarningType type) {
        if (!getScene().isOpenPlayerWarning()) {
            return;
        }
        try {
            RallyEntity rallyEntity = getRallyEntity();
            if (rallyEntity == null) {
                getScenePlayer().getWarningComponent().addWarningItemNeedAddEvent(obj, type);
                return;
            }
            rallyEntity.getArmyMgrComponent().addWarnItem(obj, type);
        } catch (Exception e) {
            LOGGER.error("addWarningItem error ", e);
        }
    }

    @Override
    public Int64ArmyArrowItemMapProp getArrowProp() {
        return getProp().getArrow();
    }

    public ScenePlayerArmyStatusProp getStatusProp() {
        return getStatusComponent().getProp();
    }

    @Override
    public void formWarningInfo(WarningInfoPB.Builder builder) {
        builder.setTargetClanSimpleName(this.getClanBriefName())
                .setTargetPlayerName(this.getPlayerName());
    }
}

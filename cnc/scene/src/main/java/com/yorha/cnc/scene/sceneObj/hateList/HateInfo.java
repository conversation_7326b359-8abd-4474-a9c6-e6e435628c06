package com.yorha.cnc.scene.sceneObj.hateList;

import com.yorha.common.utils.time.SystemClock;

/**
 * <AUTHOR>
 */
public class HateInfo {

    /**
     * 伤害值
     */
    private long hate;
    /**
     * 最后伤害时间
     */
    private long lastHateTime;
    /**
     * 是否可以清除的仇恨
     */
    private boolean canRemove;

    public HateInfo() {
    }

    public long addHate(long hate, boolean canRemove) {
        this.hate += hate;
        if (this.hate < 0) {
            this.hate = 0;
        }
        this.lastHateTime = SystemClock.now();
        this.canRemove = canRemove;
        return this.hate;
    }

    public long getHate() {
        return hate;
    }

    public long getLastHateTime() {
        return lastHateTime;
    }

    public boolean isCanRemove() {
        return canRemove;
    }

    @Override
    public String toString() {
        return "HateInfo{" +
                "hate=" + hate +
                ", lastHateTime=" + lastHateTime +
                ", canRemove=" + canRemove +
                '}';
    }
}

package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.game.gen.prop.SceneDevBuffProp;
import com.yorha.game.gen.prop.SceneDevBuffSysProp;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BuffTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * SceneObj DevBuff组件
 *
 * <AUTHOR>
 */
public class SceneObjDevBuffComponent extends SceneObjComponent<SceneObjEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjDevBuffComponent.class);

    public SceneObjDevBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    protected SceneDevBuffSysProp getSceneDevBuffSys() {
        return new SceneDevBuffSysProp();
    }

    public void addSceneDevBuff(int buffId) {
        LOGGER.info("SceneObjDevBuffComponent addSceneDevBuff {} buffId={}", getOwner(), buffId);
        getSceneDevBuffSys().putDevBuffSysV(new SceneDevBuffProp().setDevBuffId(buffId));
    }

    public void removeSceneDevBuff(int buffId) {
        LOGGER.info("SceneObjDevBuffComponent removeSceneDevBuff {} buffId={}", getOwner(), buffId);
        getSceneDevBuffSys().removeDevBuffSysV(buffId);
    }

    public boolean isEffectOn(CommonEnum.BuffEffectType effectType) {
        return !getDevBuffByEffectType(effectType).isEmpty();
    }

    public List<SceneDevBuffProp> getDevBuffByEffectType(CommonEnum.BuffEffectType effectType) {
        List<SceneDevBuffProp> res = new ArrayList<>();
        for (Map.Entry<Integer, SceneDevBuffProp> entry : getSceneDevBuffSys().getDevBuffSys().entrySet()) {
            BuffTemplate temp = getResHolder().findValueFromMap(BuffTemplate.class, entry.getKey());
            if (temp != null && temp.getType() == effectType.getNumber()) {
                if (temp.getType() == effectType.getNumber()) {
                    res.add(entry.getValue());
                }
            }
        }
        return res;
    }
}

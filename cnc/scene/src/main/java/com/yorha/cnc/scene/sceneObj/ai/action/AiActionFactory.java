package com.yorha.cnc.scene.sceneObj.ai.action;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.sceneObj.ai.action.impl.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum.AIStateType;

import java.util.Map;

/**
 * ai状态->行为工厂
 * <AUTHOR>
 */
public class AiActionFactory {
    private static final Map<AIStateType, AbstractAIAction> ACTION_MAP = Maps.newHashMap();

    static {
        for (AIStateType type : AIStateType.values()) {
            if (type == AIStateType.AST_NONE) {
                continue;
            }
            ACTION_MAP.put(type, newInstance(type));
        }
    }

    public static AbstractAIAction getAction(AIStateType type) {
        return ACTION_MAP.get(type);
    }

    private static AbstractAIAction newInstance(AIStateType type) {
        switch (type) {
            case STAY: {
                return new StayAndAlertAction();
            }
            case PATROL_RANGE: {
                return new PatrolRangeAction();
            }
            case CHASE: {
                return new ChaseAction();
            }
            case ATTACK: {
                return new AttackAction();
            }
            case LEAVE_BATTLE: {
                return new LeaveBattleAction();
            }
            case SKILL_ATTACK: {
                return new SkillAttackAction();
            }
            case MOVE_TO_MASTER: {
                return new MoveToMasterAction();
            }
            case SACRIFICE: {
                return new SacrificeAction();
            }
            case NEAR_DEATH: {
                return new NearDeathAction();
            }
            case LEAVE_BATTLE_AND_ALERT: {
                return new LeaveBattleAndAlertAction();
            }
            default: {
                throw new GeminiException("unknown actionType:{}", type);
            }
        }
    }

}

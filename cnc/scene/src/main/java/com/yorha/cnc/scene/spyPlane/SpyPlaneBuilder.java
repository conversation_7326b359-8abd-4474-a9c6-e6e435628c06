package com.yorha.cnc.scene.spyPlane;


import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.spyPlane.component.SpyPlaneTransformComponent;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.SpyPlaneProp;

/**
 * <AUTHOR>
 */
public class SpyPlaneBuilder extends SceneObjBuilder<SpyPlaneEntity, SpyPlaneProp> {

    public SpyPlaneBuilder(SceneEntity sceneEntity, long eid, SpyPlaneProp prop) {
        super(sceneEntity, eid, prop);
    }


    @Override
    public PointProp getPointProp() {
        return getProp().getMove().getCurPoint();
    }

    @Override
    public SpyPlaneTransformComponent transformComponent(SpyPlaneEntity owner) {
        return new SpyPlaneTransformComponent(owner, getPointProp());
    }
}
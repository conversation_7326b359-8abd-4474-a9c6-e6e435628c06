package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.drop.DropTemplateService;
import com.yorha.game.gen.prop.DropGroupLimitProp;
import com.yorha.game.gen.prop.Int32PlayerPickLimitMapProp;
import com.yorha.game.gen.prop.PickLimitInfoProp;
import com.yorha.game.gen.prop.PlayerPickLimitProp;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.DropGroupTemplate;

import java.util.Iterator;

/**
 * <AUTHOR>
 * <p>
 * 拾取记录
 */
public class AbstractScenePlayerPickUpComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerPickUpComponent.class);

    public AbstractScenePlayerPickUpComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    public Int32PlayerPickLimitMapProp getPickUpProp() {
        return getOwner().getPickUpProp();
    }

    /**
     * 更新拾取记录
     */
    public void updatePickRecord(DropObjectEntity dropObject) {
        DropGroupLimitProp groupLimit = dropObject.getProp().getGroupLimit();
        if (groupLimit == null || groupLimit.getOverTime() == 0) {
            return;
        }

        // 拾取限制
        PlayerPickLimitProp pickLimitProp = getPickUpProp().get(groupLimit.getGroupId());
        if (pickLimitProp == null) {
            pickLimitProp = getPickUpProp().addEmptyValue(groupLimit.getGroupId());
        }

        PickLimitInfoProp pickLimitInfo = null;
        for (Iterator<PickLimitInfoProp> iterator = pickLimitProp.getPickInfo().iterator(); iterator.hasNext(); ) {
            PickLimitInfoProp next = iterator.next();
            if (next.getOverTime() <= SystemClock.now()) {
                iterator.remove();
                continue;
            }
            if (next.getOverTime() != groupLimit.getOverTime()) {
                continue;
            }
            pickLimitInfo = next;
            break;
        }
        if (pickLimitInfo == null) {
            pickLimitInfo = new PickLimitInfoProp();
            pickLimitInfo.setOverTime(groupLimit.getOverTime());
            pickLimitProp.addPickInfo(pickLimitInfo);
        }
        pickLimitInfo.setCount(pickLimitInfo.getCount() + 1);
        checkOtherArmyPickUp(dropObject);
    }

    public void checkOtherArmyPickUp(DropObjectEntity dropObject) {
        // 中断拾取同一目标的其他部队
        getOwner().getArmyMgrComponent().getMyArmyList().stream().filter(armyEntity -> {
            if (armyEntity.getEntityId() == getOwner().getEntityId()) {
                return false;
            }
            if (!armyEntity.getPickUpComponent().isPicking()) {
                return false;
            }
            if (armyEntity.getPickUpComponent().getDropEntityId() != dropObject.getEntityId()) {
                return false;
            }
            return true;
        }).forEach(armyEntity -> {
            LOGGER.info("{} break pick. other army pick finish. dropObjectId: {}", getOwner(), dropObject.getEntityId());
            armyEntity.getPickUpComponent().pickBreak();
            getOwner().sendMsgToClient(
                    MsgType.PLAYER_PLAYDIALOG_NTF,
                    MsgHelper.buildErrorMsg(ErrorCode.PICK_TARGET_LOSE.getCode())
            );
        });
        // 中断掉落组的其他部队
    }

    /**
     * check 玩家是否能拾取
     */
    public boolean checkCanPickUp(DropObjectEntity dropObject) {
        DropGroupLimitProp groupLimit = dropObject.getProp().getGroupLimit();
        // 掉落组数据
        if (groupLimit == null || groupLimit.getOverTime() <= 0) {
            return true;
        }
        // 已经过期了
        if (groupLimit.getOverTime() <= SystemClock.now()) {
            return true;
        }
        // 玩家拾取记录
        PlayerPickLimitProp pickLimitProp = getPickUpProp().get(groupLimit.getGroupId());
        if (pickLimitProp == null) {
            return true;
        }
        DropGroupTemplate groupTemplate = ResHolder.getResService(DropTemplateService.class).getDropGroupTemplate(groupLimit.getGroupId());
        if (groupTemplate == null) {
            return true;
        }
        int count = 0;
        for (PickLimitInfoProp pickLimitInfoProp : pickLimitProp.getPickInfo()) {
            if (pickLimitInfoProp.getOverTime() != groupLimit.getOverTime()) {
                continue;
            }
            count += pickLimitInfoProp.getCount();
        }
        // 可以无限领取的
        if (groupTemplate.getLimit() == -1) {
            return true;
        }
        return count < groupTemplate.getLimit();
    }
}

package com.yorha.cnc.scene.entity.tick;

/**
 * <AUTHOR>
 * <p>
 * 增加时请注意  按顺序遍历的  注意顺序!!!!!!!!
 */
public enum SceneTimerReason {
    /**
     * 生命周期     Monster/AreaSkill/DropObject
     */
    TIMER_LIFE,
    /**
     * 野怪战斗封禁   Monster
     */
    TIMER_MONSTER_BATTLE_FORBIDDEN,
    /**
     * 拾取           Army
     */
    TIMER_PICK,
    /**
     * 模型圈更新      Army
     */
    TIMER_MODEL_UPDATE,
    /**
     * 主堡皮肤     City
     */
    TIMER_CITY_DRESS,
    /**
     * 主堡铭牌     City
     */
    TIMER_CITY_NAMEPLATE,
    /**
     * devBuff      ScenePlayer
     */
    TIMER_DEV_BUFF,
    /**
     * 调查      Spy
     */
    TIMER_SURVEY,
    /**
     * 迷雾探索      Spy
     */
    TIMER_EXPLORE,
    /**
     * 强制回收    Monster
     */
    TIMER_FORCE_RECYCLE,
    /**
     * 完成占领    GvgDungeonBuilding
     */
    TIMER_OCCUPY_COMPLETE,
    /**
     * 获得迁城次数
     */
    TIMER_MOVE_CITY_TIME,
}

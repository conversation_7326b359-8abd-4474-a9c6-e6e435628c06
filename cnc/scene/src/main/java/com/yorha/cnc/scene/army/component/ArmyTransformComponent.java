package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.army.PointChangeEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjTransformComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.model.ModelGradientTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.PointProp;
import res.template.ConstTemplate;

import java.util.HashSet;


/**
 * 描述场景部队位置的组件
 *
 * <AUTHOR>
 * 2021年10月18日 19:33:00
 */
public class ArmyTransformComponent extends SceneObjTransformComponent {
    private boolean isDelayUpdate;

    public ArmyTransformComponent(SceneObjEntity owner, PointProp pointData) {
        super(owner, pointData);
        battleMeObj = new HashSet<>();
    }

    @Override
    public void postInit() {
        super.postInit();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndRoundEvent, BattleRoleSettleRoundEvent.class);
        getOwner().getEventDispatcher().addMultiEventListenerRepeat((e) -> forceUpdateModelRadius(), DieEvent.class, EndAllBattleEvent.class);
    }

    public void forceUpdateModelRadius() {
        resetModelRadius();
        // 注册本轮移动过 让打我的人可以刷新追击
        getOwner().getScene().getTickMgrComponent().registerChangePointEntity(getEntityId());
        // 自己有追击的人 把自己加入到刷新追击列表
        if (getOwner().getMoveComponent().getCurChaseTargetId() != 0) {
            getOwner().getScene().getTickMgrComponent().registerRefreshChase(getOwner().getMoveComponent());
        }
    }

    public void timerToUpdateModelRadius() {
        if (!isDelayUpdate) {
            return;
        }
        isDelayUpdate = false;
        forceUpdateModelRadius();
    }

    private void modelRadiusInvalid() {
        if (isDelayUpdate) {
            return;
        }
        isDelayUpdate = true;
        getOwner().addSceneSchedule(SceneTimerReason.TIMER_MODEL_UPDATE, GameLogicConstants.MODEL_RADIUS_UPDATE_INTERVAL);
    }

    @Override
    public void resetModelRadius() {
        int liveSolider = getOwner().getBattleComponent().aliveCount();
        // 模型圈基数
        int soldierTypeSize = getSoldierTypeSize(getOwner().getProp().getTroop().getTroop().values());
        int modelBase = 0;
        if (getOwner().isRallyArmy()) {
            modelBase = ResHolder.getResService(ModelGradientTemplateService.class).getRallyModelBase(liveSolider, soldierTypeSize);
        } else {
            modelBase = ResHolder.getResService(ModelGradientTemplateService.class).getNormalModelBase(liveSolider, soldierTypeSize);
        }
        // 模型长度系数
        int range = ResHolder.getResService(ConstKVResService.class).getTemplate().getStandardModelDiameter();
        setModelRadius(modelBase * range);
    }

    @Override
    public void setModelRadius(int modelCircle) {
        super.setModelRadius(modelCircle);
        getOwner().getProp().setModelRadius(modelCircle);
    }

    @Override
    public void changePoint(Point newPoint, boolean isSync) {
        super.changePoint(newPoint, isSync);
        getOwner().getEventDispatcher().dispatch(new PointChangeEvent(getCurPoint()));
    }

    @Override
    public int getCityMoveCollisionRadius() {
        if ((getOwner()).isRallyArmy()) {
            return ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getAssembleArmyBlockRadius();
        }
        return ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getNormalArmyBlockRadius();
    }

    @Override
    public ArmyEntity getOwner() {
        return (ArmyEntity) super.getOwner();
    }

    private void onEndRoundEvent(BattleRoleSettleRoundEvent event) {
        if (event.getDamageResult().totalLoss() > 0) {
            // 模型圈失效
            modelRadiusInvalid();
        }
    }
}

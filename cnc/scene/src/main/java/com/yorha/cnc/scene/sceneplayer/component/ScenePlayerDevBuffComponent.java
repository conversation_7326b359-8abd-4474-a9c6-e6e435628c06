package com.yorha.cnc.scene.sceneplayer.component;


import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerDevBuffComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.devbuff.ScenePlayerDevBuffMgr;
import com.yorha.game.gen.prop.DevBuffSysProp;

/**
 * buff更新流程：
 * player侧：scenePlayer -> player
 * scene侧: scenePlayer
 *
 * <AUTHOR>
 */
public class ScenePlayerDevBuffComponent extends AbstractScenePlayerDevBuffComponent {

    public ScenePlayerDevBuffComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    public void init() {
        this.devBuffMgr = new ScenePlayerDevBuffMgr(getOwner());
    }

    public void afterAllLoad() {
        devBuffMgr.postInit();
    }

    @Override
    public DevBuffSysProp getBuffProp() {
        return getOwner().getProp().getDevBuffSysNew();
    }


}

package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneResRegionItem;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.utils.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 黄金矿刷新任务
 *
 * <AUTHOR>
 */
public class GoldBuildingRefresh extends RefreshTask {
    private static final Logger LOGGER = LogManager.getLogger(GoldBuildingRefresh.class);

    public final int regionId;
    private int needCreateNum;
    private int totalCreate;

    public GoldBuildingRefresh(SceneEntity owner, int regionId) {
        super(owner);
        this.regionId = regionId;
    }

    @Override
    public int getHandleNumPerTick() {
        return GameLogicConstants.RES_BUILDING_CREATE_MAX_PER_SECOND;
    }

    @Override
    public Pair<Boolean, Integer> run() {
        MainSceneResRegionItem mgrItem = ((MainSceneResMgrComponent) getOwner().getResMgrComponent()).getResRegionItem(regionId);
        if (mgrItem == null) {
            LOGGER.error("GoldBuildingRefresh but regionItem not exist {}", regionId);
            return Pair.of(true, 0);
        }
        if (needCreateNum == 0) {
            needCreateNum = mgrItem.getGoldNeedCreateNum();
            LOGGER.info("GoldBuildingRefresh getNeedCreateNum {} {}", regionId, needCreateNum);
        }
        if (needCreateNum <= 0) {
            mgrItem.onGoldRefreshTaskFinish(totalCreate);
            return Pair.of(true, 0);
        }
        if (needCreateNum > getHandleNumPerTick()) {
            totalCreate += mgrItem.refreshGoldBuilding(getHandleNumPerTick());
            needCreateNum -= getHandleNumPerTick();
            return Pair.of(false, getHandleNumPerTick());
        }
        totalCreate += mgrItem.refreshGoldBuilding(needCreateNum);
        mgrItem.onGoldRefreshTaskFinish(totalCreate);
        return Pair.of(true, needCreateNum);
    }

    @Override
    public String toString() {
        return "GoldBuildingRefresh{" +
                "regionId=" + regionId +
                ", needCreateNum=" + needCreateNum +
                '}';
    }
}

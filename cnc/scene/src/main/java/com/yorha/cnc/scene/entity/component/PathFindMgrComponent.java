package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.collision.AbstractCollisionWorld;
import com.yorha.cnc.scene.entity.collision.GridCollisionWorld;
import com.yorha.cnc.scene.entity.collision.NormalCollisionWorld;
import com.yorha.cnc.scene.pathfinding.PathFindingHelper;
import com.yorha.cnc.scene.pathfinding.manager.PathFindingManager;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneObj.move.SearchPathAsyncResult;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Line;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.vector.Vector2f;
import com.yorha.proto.Core;
import com.yorha.proto.SsPathFinding;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class PathFindMgrComponent extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(PathFindMgrComponent.class);

    protected PathFindingManager manager;

    /**
     * 寻路监控，区分阶段
     */
    protected AbstractCollisionWorld collisionWorld;

    public PathFindMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void initPathFind() {
        initNav();
        initCollisionWorld();
    }

    protected void initNav() {
        manager = new PathFindingManager(getOwner().getMapId());
        if (manager.getNav() == null) {
            return;
        }
        ownerActor().addNav(manager.getNav());
    }

    protected void initCollisionWorld() {
        int mapWidth = getOwner().getMapWidth();
        int mapHeight = getOwner().getMapHeight();

        int gridSize = GameLogicConstants.MAP_ID_2_COLLISION_WORLD_NUM.getOrDefault(getOwner().getMapId(), 0);
        if (gridSize <= 1) {
            collisionWorld = new NormalCollisionWorld(mapWidth, mapHeight);
        } else {
            collisionWorld = new GridCollisionWorld(mapWidth, mapHeight, gridSize);
        }
    }

    /**
     * 是否开启异步寻路
     */
    protected boolean isOpenAsyncPathFind() {
        return false;
    }

    /**
     * 无动态阻挡修正的寻路  同步  用于客户端查询路线耗时
     */
    public List<Point> searchPathWithoutCollision(AbstractScenePlayerEntity scenePlayer, Point srcPoint, Point endPoint) {
        List<Point> points = getManager().findNavPath(srcPoint, endPoint);
        if (points.size() < 2) {
            return Collections.emptyList();
        }
        return points;
    }

    /**
     * 预寻路 同步 一般用于action校验
     */
    public MoveData searchPrePath(SceneObjEntity entity, Point srcPoint, Point endPoint, int searchTag) {
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(endPoint, getOwner().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        return searchPath(entity, null, srcPoint, endPoint, searchTag, 0);
    }

    /**
     * 搜索到目标点的路径
     *
     * @param entity      寻路需求方  拿到联盟id / 出错时追究发起方（打log）  预寻路的时候传的是city
     * @param preData     预准备的路径数据
     * @param srcPoint    起始点
     * @param endPoint    目标点
     * @param searchTag   寻路tag  静态阻挡要不要  动态阻挡要不要  关卡要不要 等等
     * @param fixDistance 战斗追击/交互 到达指定目标的修正距离
     */
    public MoveData searchPath(SceneObjEntity entity, MoveData preData, Point srcPoint, Point endPoint, int searchTag, int fixDistance) {
        if (preData != null) {
            if (fixDistance <= 0) {
                return preData;
            }
            // 预寻路时没有追击修正的 因为要等army创建出来才能得到修正距离
            preData.correctTheLastPath(this, fixDistance, endPoint);
            return preData;
        }
        if (Objects.equals(srcPoint, endPoint)) {
            // 目的地与当前位置一样
            LOGGER.warn("cur:{} want go to same point", srcPoint);
            throw new GeminiException(ErrorCode.MOVE_SRC_EDN_EQUALS);
        }
        if (GameLogicConstants.ignoreAll(searchTag)) {
            return new MoveData(buildIgnoreAllPath(srcPoint, endPoint, fixDistance), searchTag);
        }
        GeminiStopWatch watch = new GeminiStopWatch("start find path");
        MoveData ret = searchPathReal(entity, srcPoint, endPoint, searchTag, fixDistance, watch);
        PathFindingHelper.monitSearchPathCost(srcPoint, endPoint, watch, "sync");
        return ret;
    }

    /**
     * 实际寻路
     */
    protected MoveData searchPathReal(SceneObjEntity entity, Point src, Point end, int searchTag, int fixDistance, GeminiStopWatch watch) {
        if (GameLogicConstants.ignoreStatic(searchTag)) {
            return new MoveData(buildIgnoreAllPath(src, end, fixDistance), searchTag);
        }
        Point newEnd = correctEndPointBeforeSearch(entity, src, end, searchTag, watch);
        List<Point> path = getManager().findNormalPath(entity.getEntityId(), src, newEnd, searchTag, watch);
        if (GameLogicConstants.dynamicPathCorrect(searchTag)) {
            path = correctPathByCityCollision(path);
            watch.mark("dynamic blocking correct");
        }
        if (fixDistance > 0) {
            path = correctPathByMoveTarget(path, fixDistance, end);
            watch.mark("move target dis correct");
        }
        return new MoveData(path, searchTag);
    }

    /**
     * 寻路前对终点修正
     */
    private Point correctEndPointBeforeSearch(SceneObjEntity entity, Point srcPoint, Point endPoint, int searchTag, GeminiStopWatch watch) {
        // 终点静态阻挡修正
        Point staticPoint = endPoint;
        if (GameLogicConstants.staticEndCorrect(searchTag)) {
            Point p1 = findNearestWalkablePoint(endPoint);
            if (p1 == null) {
                LOGGER.debug("{} static collision. srcPoint: {} endPoint: {}", entity, srcPoint, endPoint);
                throw new GeminiException(ErrorCode.ARMY_DST_POINT_NOT_WALKABLE_STATIC);
            }
            if (!endPoint.equals(p1)) {
                LOGGER.debug("{} static correct. endPoint: {} staticPoint: {}", entity, endPoint, staticPoint);
            }
            if (srcPoint.equals(p1)) {
                throw new GeminiException(ErrorCode.MOVE_SRC_EDN_EQUALS);
            }
            staticPoint = p1;
            if (watch != null) {
                watch.mark("static end correct");
            }
        }
        // 终点动态阻挡修正
        if (GameLogicConstants.dynamicEndCorrect(searchTag)) {
            Point p2 = findNearestDynamicWalkablePoint(srcPoint, staticPoint);
            if (p2 == null) {
                LOGGER.debug("{} dynamic collision. srcPoint: {} staticPoint: {} endPoint: {}", entity, srcPoint, staticPoint, endPoint);
                throw new GeminiException(ErrorCode.ARMY_DST_POINT_NOT_WALKABLE_DYNAMIC);
            }
            if (watch != null) {
                watch.mark("dynamic end correct");
            }
            if (srcPoint.equals(p2)) {
                throw new GeminiException(ErrorCode.MOVE_SRC_EDN_EQUALS);
            }
            return p2;
        }
        return staticPoint;
    }

    /**
     * 构建直接连接起点终点的路线
     */
    public List<Point> buildIgnoreAllPath(Point src, Point end, int fixDistance) {
        List<Point> pointDataList = new ArrayList<>();
        pointDataList.add(src);
        pointDataList.add(end);
        if (fixDistance > 0) {
            return correctPathByMoveTarget(pointDataList, fixDistance, end);
        }
        return pointDataList;
    }

    /**
     * 异步寻路
     */
    public MoveData searchPathAsync(SceneObjEntity entity, Point src, Point end, int searchTag, int fixDistance, Consumer<SearchPathAsyncResult> consumer) {
        if (GameLogicConstants.ignoreStatic(searchTag)) {
            return new MoveData(buildIgnoreAllPath(src, end, fixDistance), searchTag);
        }
        // 表示不使用异步机制
        if (!isOpenAsyncPathFind()) {
            GeminiStopWatch watch = new GeminiStopWatch("start find path");
            MoveData ret = searchPathReal(entity, src, end, searchTag, fixDistance, watch);
            PathFindingHelper.monitSearchPathCost(src, end, watch, "sync");
            return ret;
        }
        SsPathFinding.SearchPathAsyncAsk.Builder builder = buildSearchPathAsyncAsk(entity, src, end, searchTag);
        PathFindingHelper.askPathFindingActor(ownerActor(), getOwner().getMapId(), entity.getEntityId(), builder.build(), consumer);
        return null;
    }

    /**
     * 构建基本的异步寻路消息
     */
    protected SsPathFinding.SearchPathAsyncAsk.Builder buildSearchPathAsyncAsk(SceneObjEntity entity, Point src, Point end, int searchTag) {
        Point newEndPoint = correctEndPointBeforeSearch(entity, src, end, searchTag, null);
        SsPathFinding.SearchPathAsyncAsk.Builder builder = SsPathFinding.SearchPathAsyncAsk.newBuilder();
        builder.getSrcBuilder().setX(src.getX()).setY(src.getY());
        builder.getEndBuilder().setX(newEndPoint.getX()).setY(newEndPoint.getY());
        // 非大地图使用地图id作为索引
        builder.setEntityId(entity.getEntityId()).setSearchTag(searchTag)
                .setSrcRegion(getOwner().getMapId()).setEndRegion(getOwner().getMapId());
        return builder;
    }

    /**
     * 检查是否需要刷新路线。
     */
    public boolean checkNeedRefreshPath(Point point, int radius, long pathFindTsMs) {
        return collisionWorld.checkNeedRefreshPath(point, radius, pathFindTsMs);
    }

    /**
     * 判断点是否可达 （静态+动态）
     */
    public Core.Code isPointWalkable(Point point) {
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(point, getOwner().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        if (!isPointStaticWalkable(point)) {
            LOGGER.debug("static collision. point: {}", point);
            return ErrorCode.ARMY_DST_POINT_NOT_WALKABLE_STATIC.getCode();
        }
        if (!isPointDynamicWalkable(point)) {
            LOGGER.debug("dynamic collision. point: {}", point);
            return ErrorCode.ARMY_DST_POINT_NOT_WALKABLE_DYNAMIC.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 判断静态障碍 + 地图边界
     */
    public boolean isPointStaticWalkable(Point point) {
        if (point.getX() <= 0 || point.getX() > getOwner().getMapWidth()) {
            return false;
        }
        if (point.getY() <= 0 || point.getY() > getOwner().getMapHeight()) {
            return false;
        }
        return isPointNavWalkable(point);
    }

    /**
     * 获取静态障碍的最近可达点
     */
    private Point findNearestWalkablePoint(Point point) {
        long startTs = SystemClock.now();
        Point nearestWalkablePoint = findNavWalkablePoint(point);
        if (SystemClock.now() - startTs > 200) {
            LOGGER.warn("findNearestWalkablePoint cost than 200ms  {}  {}", point, nearestWalkablePoint);
        }
        return nearestWalkablePoint;
    }

    /**
     * 获取动态障碍的最近可达点
     */
    private Point findNearestDynamicWalkablePoint(Point sourcePoint, Point targetPoint) {
        if (sourcePoint.equals(targetPoint)) {
            // 起始点相同哦
            LOGGER.error("cant find valid point. src: {}  end:{}", sourcePoint, targetPoint);
            return targetPoint;
        }
        Collection<Shape> collisions = collisionWorld.getAllCollisionByShape(targetPoint).values();
        if (collisions.isEmpty()) {
            return targetPoint;
        }
        Circle circle = null;
        for (Shape shape : collisions) {
            if (circle == null) {
                circle = (Circle) shape;
                continue;
            }
            Circle c = (Circle) shape;
            if (c.getR() > circle.getR()) {
                circle = c;
            }
        }
        // 扩大一点点，防止算出来在圆内部
        circle = circle.newCircleWithNewRadius(circle.getR() + 5);
        Line line = Line.valueOf(sourcePoint, targetPoint);
        ImmutablePair<Point, Point> crossPoints = ShapeUtils.getShapeIntersection2(circle, line);
        Point ret = getValidPoint(sourcePoint, crossPoints);
        if (ret != null) {
            return ret;
        }
        LOGGER.info("cant find valid point1. src: {}  end:{}", sourcePoint, targetPoint);
        // 找垂直线的两个交点
        ImmutablePair<Point, Point> crossPoints2 = ShapeUtils.getShapeIntersection3(circle, line);
        ret = getValidPoint(sourcePoint, crossPoints2);
        if (ret != null) {
            return ret;
        }
        // 四个点都不行 有问题。。。
        LOGGER.warn("cant find valid point2. src: {}  end:{}", sourcePoint, targetPoint);
        return targetPoint;
    }

    private Point getValidPoint(Point src, ImmutablePair<Point, Point> crossPoints) {
        Point left = crossPoints.getLeft();
        if (left != null && !left.equals(src)) {
            LOGGER.debug("findNearestDynamicWalkablePoint left: {}", left);
            if (isPointStaticWalkable(left)) {
                return left;
            }
        }
        Point right = crossPoints.getRight();
        if (right != null && !right.equals(src)) {
            LOGGER.debug("findNearestDynamicWalkablePoint right: {}", right);
            if (isPointStaticWalkable(right)) {
                return right;
            }
        }
        return null;
    }

    /**
     * 判断动态障碍(城池、地图建筑)
     */
    public boolean isPointDynamicWalkable(Point point) {
        return !collisionWorld.isInCollision(point);
    }

    /**
     * 获取处在线段AABB中的动态圆阻挡
     */
    protected Collection<Shape> getDynamicCircleCollision(Line line) {
        return collisionWorld.getAllCollisionByShape(line).values();
    }

    /**
     * 只支持圆形动态寻路障碍
     *
     * @param id     障碍唯一id
     * @param circle 障碍形状
     */
    public void addDynamicCircleCollision(long id, Circle circle) {
        collisionWorld.add(id, circle);
    }

    public void removeDynamicCircleCollision(long id) {
        collisionWorld.remove(id);
    }

    /**
     * 生成追击路径
     */
    public List<Point> genChasePath(SceneObjEntity sceneObjEntity, List<Point> path, Map<Integer, Integer> cross, int startIndex, Point targetPoint, Point start) {
        int offsetX = start.getX() - targetPoint.getX();
        int offsetY = start.getY() - targetPoint.getY();
        List<Point> ret = new ArrayList<>();
        ret.add(start);
        Point last = start;
        boolean isFinish = false;
        for (int i = startIndex + 1; i < path.size(); i++) {
            // 关隘口
            if (cross != null && cross.getOrDefault(i, 0) != 0) {
                isFinish = true;
            }
            Point point = path.get(i);
            Point end = Point.valueOf(point.getX() + offsetX, point.getY() + offsetY);
            // 静态阻挡
            Point cur = getManager().findNearestPointRayCast(last, end);
            if (cur == null) {
                return ret;
            }
            if (!cur.roughlyEquals(end, GameLogicConstants.REY_CAST_POINT_EQUAL)) {
                isFinish = true;
            }
            // 动态阻挡
            Line line = Line.valueOf(last, cur);

            Collection<Shape> dynamicCircleCollision = getDynamicCircleCollision(line);
            Point bestLeft = null;
            int offset = Math.abs(cur.getX() - last.getX());
            for (Shape shape : dynamicCircleCollision) {
                // 只支持圆形动态寻路障碍
                Circle circle = (Circle) shape;
                ImmutablePair<Point, Point> crossPoints = ShapeUtils.getShapeIntersection(circle, line);
                Point leftPoint = null;
                if (crossPoints.getLeft() != null) {
                    leftPoint = crossPoints.getLeft();
                }
                if (crossPoints.getRight() != null) {
                    leftPoint = crossPoints.getRight();
                }
                if (leftPoint == null) {
                    continue;
                }
                int curOffset = Math.abs(leftPoint.getX() - last.getX());
                if (curOffset < offset) {
                    bestLeft = leftPoint;
                    offset = curOffset;
                }
            }
            if (bestLeft != null) {
                ret.add(bestLeft);
                return ret;
            }
            ret.add(cur);
            if (isFinish) {
                return ret;
            }
            last = cur;
        }
        return ret;
    }

    /**
     * 通过城阻挡 纠正 行军路线
     */
    public List<Point> correctPathByCityCollision(List<Point> points) {
        if (collisionWorld.getCollisionsCount() == 0) {
            return points;
        }
        // 需要加入的绕城点
        Map<Integer, List<Point>> needAddPoint = new HashMap<>();
        // 绕城的shape
        List<Circle> correctCollisionCircle = Lists.newArrayList();
        for (int i = 0; i < points.size() - 1; i++) {
            if (points.get(i).equals(points.get(i + 1))) {
                points.remove(i);
                i--;
                continue;
            }
            Line line = Line.valueOf(points.get(i), points.get(i + 1));
            if (line.getLength() < 500) {
                // 太短 不值得纠正
                continue;
            }
            Collection<Shape> dynamicCircleCollision = getDynamicCircleCollision(line);
            if (dynamicCircleCollision.isEmpty()) {
                continue;
            }
            List<Point> curIndexNeedAddPointList = new ArrayList<>();
            List<Circle> collisionCircleList = new ArrayList<>();
            for (Shape shape : dynamicCircleCollision) {
                // 只支持圆形动态寻路障碍
                collisionCircleList.add((Circle) shape);
            }
            for (Circle circle : collisionCircleList) {
                // 目标的阻挡圈过滤掉
                if (line.getEndPoint().equals(circle.getCircleCenter())) {
                    continue;
                }
                if (line.getSrcPoint().equals(circle.getCircleCenter())) {
                    continue;
                }
                ImmutablePair<Point, Point> crossPoints = ShapeUtils.getShapeIntersection(circle, line);
                if (crossPoints.getLeft() == null || crossPoints.getRight() == null) {
                    // LOGGER.error("correctPathByCityCollision {} {} {}", line.getSrcPoint(), line.getEndPoint(), circle.getCircleCenter());
                    continue;
                }
                Point leftPoint = crossPoints.getLeft();
                Point rightPoint = crossPoints.getRight();
                Vector2f oneV = Vector2f.getVectorFromPointToPoint(circle.getCircleCenter(), leftPoint).unitization();
                Vector2f otherV = Vector2f.getVectorFromPointToPoint(circle.getCircleCenter(), rightPoint).unitization();

                // 计算角平分线向量
                Vector2f midV = Vector2f.getMidVector(oneV, otherV);

                // 记录下被绕的城
                correctCollisionCircle.add(circle);
                // 扩大一圈
                Circle largerCircle = circle.newCircleWithNewRadius((int) (circle.getR() * 1.2));
                //Circle largerCircle = circle;
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(oneV));
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(otherV));
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(midV));

                double twoPointDis = Point.calDisBetweenTwoPoint(leftPoint, rightPoint);
                if (twoPointDis < circle.getR()) {
                    // 横切距离小于半径 则只选取一个点
                    continue;
                }
                // 再多加入一些点
                Vector2f l1V = Vector2f.add(oneV, midV).unitization();
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(l1V));
                Vector2f l2V = Vector2f.add(l1V, midV).unitization();
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(l2V));
                Vector2f r1V = Vector2f.add(otherV, midV).unitization();
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(r1V));
                Vector2f r2V = Vector2f.add(r1V, midV).unitization();
                curIndexNeedAddPointList.add(largerCircle.getPointFromVector(r2V));
            }
            if (curIndexNeedAddPointList.isEmpty()) {
                continue;
            }
            // 根据到起点距离排序纠正点
            curIndexNeedAddPointList.sort(Comparator.comparingDouble(o -> Point.calDisBetweenTwoPoint(line.getSrcPoint(), o)));
            needAddPoint.put(i, curIndexNeedAddPointList);
        }
        List<Point> newPath = new ArrayList<>();
        for (int i = 0; i < points.size() - 1; i++) {
            newPath.add(points.get(i));
            if (!needAddPoint.containsKey(i)) {
                continue;
            }
            newPath.addAll(needAddPoint.get(i));
        }
        newPath.add(points.get(points.size() - 1));
        List<Point> ret = new ArrayList<>();
        boolean error = false;
        for (Point p : newPath) {
            if (p.isEqualZeroPoint()) {
                error = true;
            } else {
                ret.add(p);
            }
        }
        if (error) {
            LOGGER.error("correctPathByCityCollision collisionCircle{} oldPath:{} newPath:{}", correctCollisionCircle, points, newPath);
        }
        return ret;
    }

    /**
     * 追击目标的路线修正
     *
     * @param points     寻路出来的路径
     * @param fixDis     修正距离
     * @param realTarget 修正前的真实目标点
     */
    public List<Point> correctPathByMoveTarget(List<Point> points, int fixDis, Point realTarget) {
        // 终点已经在交互距离之外了(动态阻挡圆过大,前面修正后,就会导致这样)
        if (Point.calDisBetweenTwoPoint(points.get(points.size() - 1), realTarget) >= fixDis) {
            return points;
        }
        // 起点在交互距离内 也不用修正了 没法修正呀
        if (Point.calDisBetweenTwoPoint(points.get(0), realTarget) <= fixDis) {
            return points;
        }
        int beProtectedFixDis = Math.max(0, fixDis - 1);
        Circle circle = Circle.valueOf(realTarget.getX(), realTarget.getY(), beProtectedFixDis);
        for (int i = points.size() - 2; i >= 0; i--) {
            if (points.get(i).equals(points.get(i + 1))) {
                points.remove(i);
                continue;
            }
            Line line = Line.valueOf(points.get(i), points.get(i + 1));
            ImmutablePair<Point, Point> crossPoints = ShapeUtils.getShapeIntersection(circle, line);
            Point remove = points.remove(i + 1);
            if (crossPoints.getRight() != null) {
                if (crossPoints.getRight().isEqualZeroPoint()) {
                    LOGGER.error("correctPathByMoveTarget zero point right. path:{}, index:{}, circle:{}", points, i, circle);
                } else {
                    points.add(crossPoints.getRight());
                    return points;
                }
            }
            if (crossPoints.getLeft() != null) {
                if (crossPoints.getLeft().isEqualZeroPoint()) {
                    LOGGER.error("correctPathByMoveTarget zero point left. path:{}, index:{}, circle:{}", points, i, circle);
                } else {
                    points.add(crossPoints.getLeft());
                    return points;
                }
            }
            // 最后一段没找到交点 算了 加回去吧  不能只剩一个点
            if (i == 0) {
                points.add(remove);
                LOGGER.warn("correctPathByMoveTarget error, {} {} {}", points, fixDis, realTarget);
            }
        }
        return points;
    }

    public PathFindingManager getManager() {
        return manager;
    }

    protected boolean isPointNavWalkable(Point p) {
        return getManager().isPointNavWalkable(p);
    }

    private Point findNavWalkablePoint(Point p) {
        return getManager().findNearestWalkablePoint(p);
    }

    public void destroyNav() {
        ownerActor().removeNav(manager.getNav());
        manager.onDestroy();
        manager = null;
    }
}

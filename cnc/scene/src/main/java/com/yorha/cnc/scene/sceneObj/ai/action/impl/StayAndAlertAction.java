package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;

import static com.yorha.proto.CommonEnum.AiParams.AP_ALERT_RANGE;

/**
 * 原地待机
 * 进入条件： 无
 * 进入：清除仇恨列表，停止位移
 * 执行：仇恨列表为空时警戒
 * 结束：无
 * <AUTHOR>
 */
public class StayAndAlertAction extends AbstractAIAction {


    public StayAndAlertAction() {
        super();
    }


    @Override
    protected void execute(SceneObjAiComponent component) {
        // 没有最高仇恨目标时扫敌
        if (component.getOwner().getHateListComponent().getMostHateEntity() <= 0L) {
            component.alert(component.getAiParams().get(AP_ALERT_RANGE));
            if (component.isDebugAble()) {
                LOGGER.info("{}, fire and execute :{} hatelist:{} mostHate:{}",
                        component.getLogHead(), getActionName(), component.getOwner().getHateListComponent().getHateEntities(), component.getOwner().getHateListComponent().getMostHateEntity());
            }
        }
    }

    @Override
    protected String getActionName() {
        return "StayAndAlertAction";
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        component.getOwner().getHateListComponent().clear();
        SceneObjMoveComponent moveComponent = component.getOwner().getMoveComponent();
        if (moveComponent == null) {
            return;
        }
        moveComponent.stopMove();
    }
}

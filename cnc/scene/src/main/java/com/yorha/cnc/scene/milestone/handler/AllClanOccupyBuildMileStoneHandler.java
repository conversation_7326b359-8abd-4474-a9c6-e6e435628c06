package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.ClanOccupyBuildData;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.common.constant.Constants;
import com.yorha.game.gen.prop.Int64MileStoneClanInfoMapProp;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

/**
 * 全部军团总共占领某类大地图建筑的数量
 * 参数：要求的领土建筑数量_要求的领土建筑类型
 */
public class AllClanOccupyBuildMileStoneHandler extends AbstractMileStoneHandler {


    @Override
    public void updateProcess(MileStoneTaskData data) {
        if (data instanceof ClanOccupyBuildData) {
            String[] taskParam = getTaskParamById();
            int limit = Integer.parseInt(taskParam[0]);
            ClanOccupyBuildData occupyBuildData = (ClanOccupyBuildData) data;
            if (!matchParam(occupyBuildData)) {
                return;
            }
            long min = Math.min(getProp().getProcess() + 1, limit);
            getProp().setProcess(min);
        }
    }

    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ClanOccupyBuildData) {
            ClanOccupyBuildData data = (ClanOccupyBuildData) taskData;
            LOGGER.info("AllClanOccupyBuildMileStoneHandler recordRankData, type={} level={} oldClanId={} newClanId={}", data.getBuildType(), data.getBuildLevel(), data.getOldClanId(), data.getClanId());
            if (!matchParam(data)) {
                return;
            }
            Int64MileStoneClanInfoMapProp rankInfoMap = getProp().getRankInfo().getRankInfoMap();
            MileStoneClanInfoProp mileStoneClanInfoProp = rankInfoMap.computeIfAbsent(data.getClanId(), (key) -> {
                MileStoneClanInfoProp prop = new MileStoneClanInfoProp();
                prop.setClanId(key);
                return prop;
            });
            LOGGER.info("AllClanOccupyBuildMileStoneHandler recordRankData, add success, clanId={} oldScore={}", data.getClanId(), mileStoneClanInfoProp.getScore());
            setClanScore(mileStoneClanInfoProp, data.getClanId(), mileStoneClanInfoProp.getScore() + 1, "onMapBuildOccupyByAllClan");

            // 被占领的需要扣除积分
            if (data.getOldClanId() > 0 && rankInfoMap.containsKey(data.getOldClanId())) {
                MileStoneClanInfoProp oldClanProp = rankInfoMap.get(data.getOldClanId());
                LOGGER.info("AllClanOccupyBuildMileStoneHandler recordRankData, ded success, clanId={} oldScore={}", data.getOldClanId(), oldClanProp.getScore());
                setClanScore(oldClanProp, data.getOldClanId(), Math.max(0, oldClanProp.getScore() - 1), "onMapBuildBeOccupyByClan");
            }
        }
    }

    private boolean matchParam(ClanOccupyBuildData data) {
        String[] taskParam = getTaskParamById();
        String[] split = taskParam[1].split(Constants.BAN_JIAO_DOU_HAO);
        int level = Integer.parseInt(taskParam[2]);
        for (String buildTypeConfig : split) {
            int buildType = Integer.parseInt(buildTypeConfig);
            if (buildType == data.getBuildType() && (level == 0 || level == data.getBuildLevel())) {
                return true;
            }
        }
        return false;
    }


    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_ALL_CLAN;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_OCCUPY_BUILDING_NUM;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END;
    }
}

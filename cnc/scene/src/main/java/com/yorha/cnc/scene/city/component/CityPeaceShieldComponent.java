package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.constant.DevBuffConstants;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;


/**
 * City的和平护盾组件
 *
 * <AUTHOR>
 */
public class CityPeaceShieldComponent extends SceneObjComponent<CityEntity> {
    private static final Logger LOGGER = LogManager.getLogger(CityPeaceShieldComponent.class);

    public CityPeaceShieldComponent(CityEntity owner) {
        super(owner);
    }

    public void onPeaceShieldOn(int shieldId) {
        getOwner().getDevBuffComponent().addSceneDevBuff(shieldId);
    }

    public void onPeaceShieldOff(int shieldId, boolean isExpired) {
        DevBuffConstants.PeaceShieldReason reason = isExpired ? DevBuffConstants.PeaceShieldReason.EXPIRED : DevBuffConstants.PeaceShieldReason.UNKNOWN;
        LOGGER.info("player:{} peace shield off id:{} reason:{}", getOwner().getScenePlayer(), shieldId, reason);
        getOwner().getDevBuffComponent().removeSceneDevBuff(shieldId);
    }

    public boolean isPeaceShieldOn() {
        return getOwner().getDevBuffComponent().isEffectOn(CommonEnum.BuffEffectType.ET_PEACE_SHIELD);
    }

    public long getPeaceShieldEndTime() {
        List<DevBuffProp> buffList = getOwner().getScenePlayer().getDevBuffComponent().getDevBuffByEffectType(CommonEnum.BuffEffectType.ET_PEACE_SHIELD);
        if (!buffList.isEmpty()) {
            return buffList.get(0).getEndTime();
        }
        return 0;
    }

    public void closePeaceShield() {
        getOwner().getScenePlayer().getDevBuffComponent().removeDevBuffByEffectType(CommonEnum.BuffEffectType.ET_PEACE_SHIELD);
    }
}

package com.yorha.cnc.scene.areaSkill.component;

import com.yorha.cnc.scene.areaSkill.AreaSkillEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.game.gen.prop.Int32BuffMapProp;

/**
 * 区域技能的buff组件
 *
 * <AUTHOR>
 */
public class AreaSkillBuffComponent extends SceneObjBuffComponent {
    public AreaSkillBuffComponent(AreaSkillEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuffSys().getBuff();
    }

    @Override
    public AreaSkillEntity getOwner() {
        return (AreaSkillEntity) super.getOwner();
    }
}

package com.yorha.cnc.scene.sceneclan.component;

import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.server.ServerContext;

/**
 * 大世界上的联盟简要头部
 *
 * <AUTHOR>
 */
public class SceneClanQlogComponent extends AbstractComponent<SceneClanEntity> implements QlogClanFlowInterface {

    public SceneClanQlogComponent(SceneClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
    }

    @Override
    public String getServer_type() {
        return "";
    }

    @Override
    public String getIZoneAreaID() {
        return String.valueOf(this.getOwner().getZoneId());
    }

    @Override
    public String getGuildCreate_time() {
        return "";
    }

    @Override
    public String getGuild_id() {
        return String.valueOf(getOwner().getProp().getClanId());
    }

    @Override
    public String getGuild_name() {
        return "";
    }

    @Override
    public String getGuildShortName() {
        return "";
    }

    @Override
    public int getGuild_level() {
        return 0;
    }

    @Override
    public int getGuild_population() {
        return 0;
    }

    @Override
    public int getGuild_online() {
        return 0;
    }

    @Override
    public int getGuildGiftLevel() {
        return 0;
    }

    @Override
    public long getGuild_power() {
        return 0;
    }

    @Override
    public long getGuild_influence() {
        return 0;
    }

    @Override
    public String getGuildLeader_id() {
        return "";
    }

    @Override
    public String getAccountId() {
        return "guild_" + ServerContext.getServerInfo().getWorldId() + "_" + getIZoneAreaID();
    }

    public void markClanActive() {
        getOwner().getProp().setIsActive(true);
    }

    public void markClanUnActive() {
        getOwner().getProp().setIsActive(false);
    }

}

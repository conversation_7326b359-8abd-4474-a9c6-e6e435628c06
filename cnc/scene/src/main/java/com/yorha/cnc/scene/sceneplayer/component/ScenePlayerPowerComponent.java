package com.yorha.cnc.scene.sceneplayer.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.power.PowerInterface;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.Map;


/**
 * 场景玩家战力相关
 *
 * <AUTHOR>
 */
public class ScenePlayerPowerComponent extends AbstractComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerPowerComponent.class);

    private final Map<CommonEnum.PowerType, PowerInterface> POWER_INTERFACE_MAP = Maps.newEnumMap(CommonEnum.PowerType.class);

    public ScenePlayerPowerComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        // 注册会提供power的component
        register(getOwner().getSoldierMgrComponent());
    }

    private void register(PowerInterface powerInterface) {
        POWER_INTERFACE_MAP.put(powerInterface.getPowerType(), powerInterface);
    }

    public void updatePower(CommonEnum.PowerType powerType, @Nullable SoldierNumChangeReason reason) {
        try {
            long newPower = getComponent(powerType).calcPower();
            if (newPower < 0) {
                LOGGER.error("player:{} calc power type:{} illegal.", getOwner(), powerType);
                throw new GeminiException(ErrorCode.POWER_CALC_ERROR);
            }

            SsPlayerMisc.UpdateScenePowerCmd.Builder cmdBuilder = SsPlayerMisc.UpdateScenePowerCmd.newBuilder()
                    .setPowerType(powerType)
                    .setNewPower(newPower);
            if (reason != null) {
                cmdBuilder.setSoldierNumChangeReason(reason.getId());
            }
            getOwner().tellPlayer(cmdBuilder.build());
        } catch (Exception e) {
            LOGGER.error("ScenePlayerPowerComponent updatePower fail, ", e);
        }
    }

    private PowerInterface getComponentOrNull(CommonEnum.PowerType powerType) {
        return POWER_INTERFACE_MAP.get(powerType);
    }

    private PowerInterface getComponent(CommonEnum.PowerType powerType) {
        PowerInterface componentOrNull = getComponentOrNull(powerType);
        if (componentOrNull == null) {
            LOGGER.error("power type:{} not register", powerType);
            throw new GeminiException(ErrorCode.POWER_TYPE_NOT_EXIST);
        }
        return componentOrNull;
    }
}

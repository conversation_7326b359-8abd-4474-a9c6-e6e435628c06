package com.yorha.cnc.scene.outbuilding.component;

import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.game.gen.prop.Int32BuffMapProp;

/**
 * <AUTHOR>
 */
public class OutbuildingBuffComponent extends SceneObjBuffComponent {
    public OutbuildingBuffComponent(OutbuildingEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuffSys().getBuff();
    }

    @Override
    public OutbuildingEntity getOwner() {
        return (OutbuildingEntity) super.getOwner();
    }
}


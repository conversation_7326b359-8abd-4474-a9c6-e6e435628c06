package com.yorha.cnc.scene.sceneplayer.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerArmyMgrComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.resource.ResCollectService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.MathUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ScenePlayerArmyMgrComponent extends AbstractScenePlayerArmyMgrComponent {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerArmyMgrComponent.class);

    public ScenePlayerArmyMgrComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    private ScenePlayerArmyModelProp getArmyModelProp() {
        return getOwner().getProp().getArmyModel();
    }

    @Override
    public void onNewArmy(ArmyEntity newArmyEntity) {
        super.onNewArmy(newArmyEntity);
        if (newArmyEntity.isRallyArmy()) {
            return;
        }
        getArmyModelProp().putArmyV(newArmyEntity.getStatusComponent().getProp());
    }

    @Override
    public void removeArmy(ArmyEntity army) {
        super.removeArmy(army);
        getArmyModelProp().removeArmyV(army.getEntityId());
    }

    @Override
    protected void onNormalArmyDelete(ArmyEntity army) {
        super.onNormalArmyDelete(army);
        // 通知player，部队回城
        SsPlayerMisc.OnArmyReturnCmd.Builder cmd = SsPlayerMisc.OnArmyReturnCmd.newBuilder();
        // 构建采集掠夺相关
        buildPlunderAndCollectToPlayer(army, cmd);
        // 构建释放英雄飞机
        buildHeroToPlayer(army, cmd);

        getOwner().tellPlayer(cmd.build());
        // qlog
        getOwner().getQlogComponent().sendArmyQLog(army, "army_depart");
    }

    /**
     * 出征部队数量检测
     */
    @Override
    public void checkArmyCreate() {

    }

    /**
     * 登录检查败退部队的情况
     */
    public void checkRetreatArmy() {
        for (ArmyEntity armyEntity : Lists.newArrayList(myArmyList)) {
            try {
                armyEntity.checkRetreat();
            } catch (Exception e) {
                LOGGER.error("checkRetreatArmy failed {} ", armyEntity, e);
            }
        }
    }


    /**
     * 构建英雄通知
     */
    private void buildHeroToPlayer(ArmyEntity armyEntity, SsPlayerMisc.OnArmyReturnCmd.Builder cmd) {
        int mainHeroId = armyEntity.getProp().getTroop().getMainHero().getHeroId();
        int deputyHeroId = armyEntity.getProp().getTroop().getDeputyHero().getHeroId();
        if (mainHeroId > 0 || deputyHeroId > 0) {
            SsPlayerMisc.ReleaseHeroCmd.Builder heroBuilder = SsPlayerMisc.ReleaseHeroCmd.newBuilder();
            if (mainHeroId > 0) {
                heroBuilder.addReleaseHeroIdList(mainHeroId);
            }
            if (deputyHeroId > 0) {
                heroBuilder.addReleaseHeroIdList(deputyHeroId);
            }
            cmd.setReleaseHeroCmd(heroBuilder);
        }
    }

    /**
     * 构建采集掠夺通知
     */
    private void buildPlunderAndCollectToPlayer(ArmyEntity armyEntity, SsPlayerMisc.OnArmyReturnCmd.Builder cmd) {
        builderPlunderToPlayer(armyEntity, cmd);
        buildCollectToPlayer(armyEntity, cmd);
    }

    /**
     * 构建掠夺的部分
     *
     * @param army 军队实体
     * @param cmd  给玩家的cmd
     */
    private void builderPlunderToPlayer(ArmyEntity army, SsPlayerMisc.OnArmyReturnCmd.Builder cmd) {
        ArmyResourcesModelProp resource = army.getProp().getResource();
        // 构造掠夺的资源
        for (CurrencyProp prop : resource.getPlunder().values()) {
            if (prop.getCount() <= 0) {
                continue;
            }
            cmd.putPlunderRes(prop.getType(), prop.getCount());
        }
    }

    /**
     * 构建采集的部分
     *
     * @param army 军队的实体
     * @param cmd  给玩家的cmd
     */
    private void buildCollectToPlayer(ArmyEntity army, SsPlayerMisc.OnArmyReturnCmd.Builder cmd) {
        ArmyResourcesModelProp resource = army.getProp().getResource();
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        List<Struct.Currency> contributeList = new ArrayList<>();
        for (ArmyCollectResourceProp prop : resource.getCollect()) {
            if (prop.getNum() <= 0) {
                continue;
            }
            int templateId = prop.getTemplateId();
            // 采集的来源可能是军团资源中心，也可能是资源田，配表不同
            ResCollectService service = ResHolder.getResService(ResCollectService.class);

            if (service.isCollectFromClanRes(templateId)) {
                int mailId = ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getClanCollectMailId();
                ClanResourceBuildingTemplate template = ResHolder.getInstance().getValueFromMap(ClanResourceBuildingTemplate.class, templateId);
                int type = template.getResType().getNumber();
                long old = cmd.getCollectResMap().getOrDefault(type, 0L);
                long heroExtraCount = SceneAddCalc.getArmyExtraCollect(army.getBattleComponent().getBattleRole(), prop.getNum());
                long kingDecCount = getGainTaxDecCount(type, prop.getNum());
                cmd.putCollectRes(type, prop.getNum() + old + heroExtraCount - kingDecCount).setIsCollectFromClanRes(true);
                sendCollectMail(mailId, template, prop.getPoint(), prop.getNum(), heroExtraCount, kingDecCount, prop.getCollectEndTsMs());
            } else {
                int mailId = ResHolder.getInstance().getConstTemplate(ConstCollectTemplate.class).getCollectMailId();
                ResourceTemplate template = ResHolder.getInstance().getValueFromMap(ResourceTemplate.class, templateId);
                int type = template.getResType().getNumber();
                long old = cmd.getCollectResMap().getOrDefault(type, 0L);
                long heroExtraCount = SceneAddCalc.getArmyExtraCollect(army.getBattleComponent().getBattleRole(), prop.getNum());
                long kingDecCount = getGainTaxDecCount(type, prop.getNum());
                cmd.putCollectRes(type, prop.getNum() + old + heroExtraCount - kingDecCount).setIsCollectFromClanRes(false);
                sendCollectMail(mailId, template, prop.getPoint(), prop.getNum(), heroExtraCount, kingDecCount, prop.getCollectEndTsMs());
                if (getOwner().isInClan()) {
                    ResBuildingEntity resBuildingEntity = objMgrComponent.getSceneObjWithType(ResBuildingEntity.class, prop.getId());
                    if (resBuildingEntity == null) {
                        continue;
                    }
                    if (!getOwner().isInClan()) {
                        continue;
                    }
                    if (resBuildingEntity.getProp().getClanId() != getOwner().getClanId()) {
                        continue;
                    }
                    long contribute = MathUtils.multiplyExact(prop.getNum(), constTemplate.getCollectResourcesContribute()) / GameLogicConstants.IPPM;
                    contributeList.add(Struct.Currency.newBuilder().setType(type).setCount(contribute).build());
                }
            }
        }
        if (!contributeList.isEmpty()) {
            SsClanBase.AddClanResourcesCmd.Builder addClanResourcesCmd = SsClanBase.AddClanResourcesCmd.newBuilder();
            addClanResourcesCmd.addAllResources(contributeList);
            getOwner().getSceneClan().tellClan(addClanResourcesCmd.build());
        }
        // 采集完次数
        cmd.setOutTimes(resource.getOutTimes());
    }


    /**
     * 发送军团资源中心的采集报告
     */
    private void sendCollectMail(int mailId, ClanResourceBuildingTemplate template, PointProp point, long num, long heroExtraCount, long kingDecCount, long collectTsMs) {
        StructMail.MailSendParams.Builder mailSendParams = getCollectMailParam(mailId, template.getResType(), template.getId(),
                point.setMapId(getOwner().getScene().getMapIdForPoint()).setMapType(getOwner().getScene().getMapType().getNumber()), num, heroExtraCount, kingDecCount, -1, collectTsMs);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getZoneId())
                        .build(),
                mailSendParams.build());
    }


    private long getGainTaxDecCount(int type, long resourceNum) {
        long kingDecCount = 0;
        if (getOwner().getScene().isBigScene()) {
            kingDecCount = getOwner().getScene().getBigScene().getKingdomComponent().getGainTaxDecCount(type, resourceNum);
        }
        return kingDecCount;
    }
}

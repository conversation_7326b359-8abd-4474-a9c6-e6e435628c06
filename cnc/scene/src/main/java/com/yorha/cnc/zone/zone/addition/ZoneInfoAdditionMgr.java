package com.yorha.cnc.zone.zone.addition;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.addition.AbstractScenePlayerAdditionMgr;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.addition.AdditionMgrBase;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/9
 */
public class ZoneInfoAdditionMgr extends AdditionMgrBase<ZoneEntity> {
    // player侧需要使用的加成
    private AdditionSysProp playerAdditionSys;

    public ZoneInfoAdditionMgr(ZoneEntity owner) {
        super(owner);
    }

    @Override
    protected void init() {
        super.init();
        this.playerAdditionSys = new AdditionSysProp();
        for (Map.Entry<Integer, AdditionProp> entry : getAdditionSys().getAddition().entrySet()) {
            if (!AdditionUtil.isSceneAddition(entry.getKey())) {
                AdditionProp additionProp = new AdditionProp();
                additionProp.mergeFromSs(entry.getValue().getCopySsBuilder().build());
                playerAdditionSys.getAddition().put(entry.getKey(), additionProp);
            }
        }
    }

    @Override
    public AdditionSysProp getAdditionSys() {
        return getOwner().getProp().getAdditionSys();
    }

    @Override
    protected void update(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        Map<Integer, Long> sceneAdditionMap = Maps.newHashMap();
        Map<Integer, Long> playerAdditionMap = Maps.newHashMap();
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            if (AdditionUtil.isSceneAddition(entry.getKey())) {
                sceneAdditionMap.put(entry.getKey(), entry.getValue());
            } else {
                playerAdditionMap.put(entry.getKey(), entry.getValue());
            }
        }

        Map<Integer, Long> oldAdditions = new HashMap<>();
        for (Integer additionId : additions.keySet()) {
            oldAdditions.put(additionId, getAddition(additionId));
        }

        updateAddition(sourceType, additions);

        for (Map.Entry<Integer, Long> entry : oldAdditions.entrySet()) {
            long newAdditionValue = getAddition(entry.getKey());
            if (entry.getValue() != newAdditionValue) {
                LOGGER.info("{} update addition. additionId:{}, sourceType:{}, value:{} -> {}", getOwner(), entry.getKey(), sourceType, entry.getValue(), getAddition(entry.getKey()));
            }
        }

        updateAdditionToScene(sourceType, sceneAdditionMap);
        updateAdditionToPlayer(sourceType, playerAdditionMap);
    }

    private void updateAdditionToScene(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        for (CityEntity cityEntity : getOwner().getBigScene().getPlayerMgrComponent().getAllUnAscendCityEntity()) {
            // 通知所有玩家加成变更事件
            try {
                AbstractScenePlayerAdditionMgr.dispatchAdditionChange(additions, cityEntity.getScenePlayer());
            } catch (Exception e) {
                LOGGER.error("ZoneInfoAdditionMgr updateAdditionToScene fail {} {} {} ", cityEntity, sourceType, additions, e);
            }
        }
    }

    private void updateAdditionToPlayer(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        AdditionUtil.updateAdditionSys(playerAdditionSys, sourceType, additions);

        SsPlayerMisc.UpdateAdditionFromZoneCmd.Builder cmd = SsPlayerMisc.UpdateAdditionFromZoneCmd.newBuilder()
                .setSource(sourceType)
                .putAllAddition(additions);
        // 通知在线玩家
        getOwner().getBigScene().getPlayerMgrComponent().broadcastOnlinePlayer(cmd.build());
    }

    public AdditionSysProp getAllPlayerAdditions() {
        return playerAdditionSys;
    }
}

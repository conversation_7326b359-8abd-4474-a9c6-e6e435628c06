package com.yorha.cnc.scene.outbuilding.component;

import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.SoldierUnit;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.scene.event.battle.AfterEndAllBattleEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.event.battle.EnterNewBattle;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.OutbuildingState;
import com.yorha.proto.CommonEnum.SceneObjType;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import qlog.flow.QlogCncBattle;
import res.template.MapBuildingTemplate;

import static com.yorha.proto.CommonEnum.MapBuildingType.MBT_TESLA_COIL;

/**
 * 超武附属建筑战斗组件
 *
 * <AUTHOR>
 */
public class OutbuildingBattleComponent extends SceneObjBattleComponent {
    private EventListener eventListener1;
    private EventListener eventListener2;

    public OutbuildingBattleComponent(SceneObjEntity owner, SceneObjType sceneObjType) {
        super(owner, sceneObjType, null);
    }

    @Override
    public void init() {
        super.init();
        getBattleProp().setBattleState(CommonEnum.BattleState.BS_IDLE);
        if (getOwner().getProp().getState() != OutbuildingState.OS_PROTECT) {
            return;
        }
        MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(getOwner().getPartId());
        mapBuilding.getLeaderComponent().incOutBuilding(getOwner());
        eventListener1 = mapBuilding.getEventDispatcher().addEventListenerRepeat(this::onLeaderEnterBattle, EnterNewBattle.class);
        eventListener2 = mapBuilding.getEventDispatcher().addEventListenerRepeat(this::onLeaderEndAllBattle, AfterEndAllBattleEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndSingleRelation, EndSingleBattleEvent.class);
    }

    /**
     * 老大开战了 自己破罩
     */
    private void onLeaderEnterBattle(EnterNewBattle e) {
        final SceneObjAiComponent aiComponent = getOwner().getAiComponent();
        if (aiComponent != null) {
            if (isTesla(getOwner())) {
                getOwner().getAiComponent().addRecord(CommonEnum.AiRecordType.ART_TARGET, e.getTargetId());
                getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
            }
        }

        if (getOwner().getProp().getState() != OutbuildingState.OS_PROTECT) {
            return;
        }
        getOwner().getProp().setState(OutbuildingState.OS_IDLE);
        if (aiComponent != null) {
            aiComponent.start();
        }
        if (getOwner().getHateListComponent() != null) {
            getOwner().getHateListComponent().addHate(e.getTargetId(), 1);
        }
    }

    /**
     * 磁暴线圈
     */
    private boolean isTesla(OutbuildingEntity outbuildingEntity) {
        CommonEnum.MapBuildingType mapBuildingType = outbuildingEntity.getBuildingTemplate().getType();
        return mapBuildingType == MBT_TESLA_COIL;
    }

    /**
     * 老大脱战了  加罩子 脱战
     */
    private void onLeaderEndAllBattle(AfterEndAllBattleEvent e) {
        final SceneObjAiComponent aiComponent = getOwner().getAiComponent();
        if (aiComponent != null) {
            if (isTesla(getOwner())) {
                aiComponent.removeRecord(CommonEnum.AiRecordType.ART_TARGET);
            }
            aiComponent.stop();
        }
        MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(getOwner().getPartId());
        forceEndAllBattle();
        if (mapBuilding.getOccupyState() == CommonEnum.OccupyState.TOS_NEUTRAL) {
            if (getOwner().getAiComponent() != null) {
                //重置建筑了
                getOwner().getAiComponent().reset();
            }
            if (getOwner().getProp().getState() == OutbuildingState.OS_DESTROY) {
                mapBuilding.getLeaderComponent().incOutBuilding(getOwner());
            }
            getOwner().getProp().setState(OutbuildingState.OS_PROTECT);
        } else {
            getOwner().getProp().setState(OutbuildingState.OS_OCCUPIED);
            eventListener1.cancel();
            eventListener2.cancel();
            eventListener1 = null;
            eventListener2 = null;
        }
    }

    @Override
    public boolean ready(IBattleRoleAdapter other) {
        onAttackerArrived();
        return super.ready(other);
    }

    public void onEndSingleRelation(EndSingleBattleEvent event) {
        if (!hasAnyAlive()) {
            if (!getOwner().isDestroy()) {
                onDead();
            }
        }
    }

    /**
     * 磁暴线圈转火逻辑
     */
    public void onTeslaLogic() {
        if (!isTesla(getOwner())) {
            return;
        }

        MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(getOwner().getPartId());
        long targetId = mapBuilding.getBattleComponent().getTargetId();
        Object lastTarget = getOwner().getAiComponent().tryGetRecord(CommonEnum.AiRecordType.ART_TARGET);
        if (targetId <= 0) {
            return;
        }
        if (lastTarget == null) {
            addTarget(targetId);
            return;
        }
        if ((long) lastTarget == (targetId)) {
            return;
        }
        addTarget(targetId);
    }

    private void addTarget(long targetId) {
        getOwner().getAiComponent().addRecord(CommonEnum.AiRecordType.ART_TARGET, targetId);
        if (getOwner().getHateListComponent() != null) {
            getOwner().getHateListComponent().addHate(targetId, 1);
        }
    }

    private void onDead() {
        MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(getOwner().getPartId());
        mapBuilding.getLeaderComponent().decOutBuilding(getOwner());
    }

    /**
     * 攻城队伍到达 初始化驻防队伍数据
     */
    public void onAttackerArrived() {
        // 战斗中无需构建数据
        if (isInBattle()) {
            return;
        }
        // 使用配置部队
        MapBuildingTemplate template = getOwner().getBuildingTemplate();
        StructPlayer.Troop troop = TroopHelper.getTroopBuilder(ResHolder.getInstance(), template.getTroopId());
        getTroop().mergeFromSs(troop);
        getTroop().setTroopId(template.getTroopId());
        getTroop().markAll();
        if (getTroop().getMainHero().getHeroId() != 0) {
            getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
        }
        if (getTroop().getDeputyHero().getHeroId() != 0) {
            getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
        }
        for (SoldierProp selfSoldier : getTroop().getTroop().values()) {
            getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_STRONG_POINT_ARMY), selfSoldier);
        }
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
        if (!battleResult.alive) {
            getOwner().getProp().setState(OutbuildingState.OS_DESTROY);
            final SceneObjAiComponent aiComponent = getOwner().getAiComponent();
            if (aiComponent != null) {
                aiComponent.stop();
            }
        }
    }

    @Override
    public void clearAfterSettle() {
        super.clearAfterSettle();
        if (!getBattleRole().hasActiveRelation()) {
            clearBattleData();
        }
    }

    private void clearBattleData() {
        getBattleRole().clearAllProperty();
        clearTroop();
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {
        BattleRecordRoleSummaryProp selfSummary = recordAllProp.getSelfSummary();
        selfSummary.setRoleType(SceneObjType.SOT_CLAN_BUILDING_ARMY);
        int templateId = getOwner().getProp().getTemplateId();
        selfSummary.getCardHead().setPic(templateId).setName(String.valueOf(templateId));
    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {
        Point pos = getOwner().getCurPoint();
        roleRecord.setLocation(pos.getX(), pos.getY(), getOwner().getScene().getMapIdForPoint(), getOwner().getScene().getMapType());
        roleRecord.setBuildingId(getOwner().getProp().getTemplateId());
        int templateId = getOwner().getProp().getTemplateId();
        Struct.PlayerCardHead.Builder builder = Struct.PlayerCardHead.newBuilder()
                .setPic(templateId)
                .setName(String.valueOf(templateId));
        roleRecord.setCardHead(builder.build());
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        roleRecord.addMember(buildRoleMemberRecord());
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        long playerId = getOwner().getPlayerId();
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getEntityId())
                .setPlayerId(playerId)
                .setCardHead(Struct.PlayerCardHead.newBuilder().setName(String.valueOf(getOwner().getProp().getTemplateId())).setPic(getOwner().getProp().getTemplateId()).build());
        HeroProp mainHeroProp = getBattleRole().getMainHero() != null ? getBattleRole().getMainHero().getHeroProp() : new HeroProp();
        HeroProp deputyHeroProp = getBattleRole().getDeputyHero() != null ? getBattleRole().getDeputyHero().getHeroProp() : new HeroProp();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }

    @Override
    public OutbuildingEntity getOwner() {
        return (OutbuildingEntity) super.getOwner();
    }

    @Override
    public long getRoleTypeId() {
        return getOwner().getProp().getTemplateId();
    }
}

package com.yorha.cnc.mainScene.bigScene.component;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.mail.ZoneMailMgr;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.proto.StructMail;
import com.yorha.proto.TcaplusDb;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BigSceneMailComponent extends AbstractComponent<BigSceneEntity> {
    /**
     * 全服邮件管理
     */
    private final ZoneMailMgr zoneMailMgr;

    public BigSceneMailComponent(BigSceneEntity owner) {
        super(owner);
        this.zoneMailMgr = new ZoneMailMgr(owner.ownerActor());
    }

    /**
     * 加载本服全服邮件
     */
    public void loadMails(List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> mails) {
        this.zoneMailMgr.loadMails(mails);
    }


    /**
     * @param zoneMailIndex < 0 则跳过所有离线全服邮件。
     * @param createTime    玩家创建时间
     * @return 所有离线邮件
     */
    public List<StructMail.NewMailCache> syncPlayerOfflineMails(int zoneMailIndex, long createTime) {
        return Collections.unmodifiableList(zoneMailMgr.getOfflineMails(zoneMailIndex, createTime));
    }

    /**
     * @return 返回当前Mail的游标。
     */
    public int getZoneMailIndex() {
        return this.zoneMailMgr.getMailIndex();
    }

    /**
     * 发送全服邮件
     *
     * @param mailId         邮件id
     * @param mailSendParams 邮件参数
     */
    public void sendZoneMail(final long mailId, StructMail.MailSendParams mailSendParams) {
        zoneMailMgr.sendZoneMail(mailId, mailSendParams);
    }
}

package com.yorha.cnc.scene.pathfinding.manager;

import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.common.utils.shape.Point;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FindNavPathApi {
    List<Point> findNavPath(Point src, Point end, int regionId);

    boolean isNoCollision(Point src, Point end);

    /**
     * 全州连通图
     */
    default BoolMap getRegionMap() {
        return null;
    }
}

package com.yorha.cnc.zone.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.cnc.scene.event.activity.IActivityEvent;
import com.yorha.cnc.zone.IZone;
import com.yorha.cnc.zone.activity.ActivityUnitEffect;
import com.yorha.cnc.zone.activity.BaseZoneActivityUnit;
import com.yorha.cnc.zone.activity.ZoneActivity;
import com.yorha.common.activity.TickLikeTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivitySchedule;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.eventdispatcher.EventDispatcher;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.BestCommanderHistoryRankModelProp;
import com.yorha.game.gen.prop.BestCommanderHistoryRankRecordProp;
import com.yorha.game.gen.prop.ZoneActivityModelProp;
import com.yorha.game.gen.prop.ZoneActivityProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityForceOffScheduleTemplate;
import res.template.ActivityScheduleTemplate;
import res.template.ActivityTemplate;

import javax.annotation.Nullable;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
public abstract class AbsZoneSideActivityComponent<T extends AbstractEntity> extends AbstractComponent<T> {
    private static final Logger LOGGER = LogManager.getLogger(AbsZoneSideActivityComponent.class);
    private final IZone iZone;

    private static final int BEST_COMMANDER_MAX_HISTORY_SIZE = 20;
    private final EventDispatcher dispatcher = new EventDispatcher();

    public static Map<CommonEnum.ActivityUnitType, ZoneUnitConstructor> getUnityFactory() {
        return ZoneSideActivityComponent.ZONE_UNIT_FACTORY;
    }

    public AbsZoneSideActivityComponent(IZone iZone, T entity) {
        super(entity);
        this.iZone = iZone;
    }

    @Override
    public void onDestroy() {
        dispatcher.clear();
    }

    public void dispatch(IActivityEvent event) {
        dispatcher.dispatch(event);
    }

    public <E extends IActivityEvent> EventListener addEventListener(Consumer<? extends E> eventHandler, Class<E> clz) {
        return dispatcher.addEventListener(eventHandler, clz);
    }

    public <E extends IActivityEvent> EventListener addEventListenerRepeat(Consumer<? extends E> eventHandler, Class<E> clz) {
        return dispatcher.addEventListenerRepeat(eventHandler, clz);
    }

    public void cancelEventListener(EventListener eventListener) {
        eventListener.cancel();
    }

    public IZone getIZone() {
        return iZone;
    }

    public interface ZoneUnitConstructor {
        BaseZoneActivityUnit create(ZoneActivity owner, ActivityTemplate template);
    }

    /**
     * 活跃的活动
     */
    private final Map<Integer, ZoneActivity> activities = Maps.newHashMap();

    private final TickLikeTimer reloadTimer = new TickLikeTimer();

    private final List<StructMsg.BestCommanderHistoryRank> bestCommanderHistoryRanks = Lists.newLinkedList();

    public void initActivity() {
        tryLoadAllSchedule();
        try {
            loadBestCommanderHistoryRanks();
        } catch (InvalidProtocolBufferException e) {
            throw new GeminiException("loadBestCommanderHistoryRanks parse failed.!");
        }
    }

    public void tryAddNextReloadTimer(Instant next) {
        long delay = Duration.between(SystemClock.nowInstant(), next).plusMillis(RandomUtils.nextInt(1, 3000)).toMillis();
        if (delay >= TimeUnit.DAYS.toMillis(365)) {
            // 大于1年就不定时了
            LOGGER.warn("tryAddNextReloadTimer add too long timer next={}", next);
            return;
        }
        this.reloadTimer.tryAddTickTimer(next, () -> iZone.getTimerComponent().addTimer(
                TimerReasonType.ACTIVITY_RELOAD,
                this::tryLoadAllSchedule,
                delay,
                TimeUnit.MILLISECONDS),
                () -> {
                    getIZone().getTimerComponent().cancelTimer(TimerReasonType.ACTIVITY_RELOAD);
                });
    }

    public void onDayRefresh() {
        tryLoadAllSchedule();
    }

    public void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {
        if (updatedTemplates.contains(ActivityScheduleTemplate.class) || updatedTemplates.contains(ActivityTemplate.class) || updatedTemplates.contains(ActivityForceOffScheduleTemplate.class)) {
            LOGGER.info("ZoneSideActivityComponent onReloadRes, will reloadAllSchedule.");
            tryLoadAllSchedule();
        }
    }

    protected abstract boolean isServerOpen();

    private void tryLoadAllSchedule() {
        LOGGER.info("ZoneActivityMgr start tryLoadAllSchedule");
        if (!isServerOpen()) {
            LOGGER.info("ZoneActivityMgr tryLoadAllSchedule end server is not open={}", getIZone().getServerOpenTsMs());
            return;
        }
        final ZoneActivityModelProp model = getIZone().getZoneSideProp().getActivityModel();

        final Set<Integer> forceOffScheduleIds = ResHolder.getResService(ActivityResService.class).getForceOffScheduleIds();
        // 最优先处理活动强制下架
        for (int forceOffScheduleId : forceOffScheduleIds) {
            for (ZoneActivityProp p : Lists.newLinkedList(model.getActivities().values())) {
                if (p.getActScheduleId() == forceOffScheduleId) {
                    forceCloseActivity(p);
                }
            }
        }

        // 已经存在的活动先load
        ArrayList<ZoneActivityProp> asProps = Lists.newArrayList(model.getActivities().values());
        for (ZoneActivityProp zoneActivityProp : asProps) {
            try {
                ZoneActivity zoneActivity = activities.get(zoneActivityProp.getActivityId());
                if (zoneActivity == null) {
                    // 排期prop有。但是内存没有，需要把活动加载出来
                    ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, zoneActivityProp.getActivityId());
                    loadActivity(null, zoneActivityProp, activityTemplate);
                } else {
                    zoneActivity.reload();
                }
            } catch (Exception e) {
                LOGGER.error("tryLoadAllSchedule load scheduleId={} activityId={} failed, ", zoneActivityProp.getActScheduleId(), zoneActivityProp.getActivityId(), e);
            }
        }
        // 开启常规的活动
        tryOpenNewActivity(ResHolder.getResService(ActivityResService.class).allActivitySchedule());
        LOGGER.info("ZoneActivityMgr tryLoadAllSchedule ok.");
    }

    /**
     * 尝试开启一批活动
     *
     * @param schedules 活动排期
     */
    private void tryOpenNewActivity(List<ActivitySchedule> schedules) {
        final Set<Integer> forceOffScheduleIds = ResHolder.getResService(ActivityResService.class).getForceOffScheduleIds();
        final Instant now = Instant.ofEpochMilli(SystemClock.now());
        final long serverOpenTsMs = getIZone().getServerOpenTsMs();
        final long zoneOpenDaysUntilNow;
        if (serverOpenTsMs > now.toEpochMilli()) {
            zoneOpenDaysUntilNow = 0;
        } else {
            zoneOpenDaysUntilNow = TimeUtils.getAbsNatureDaysBetween(serverOpenTsMs, now.toEpochMilli());
        }

        for (ActivitySchedule as : schedules) {
            try {
                ActivityScheduleTemplate scheduleTemplate = as.template;
                final int actScheduleId = scheduleTemplate.getId();
                if (forceOffScheduleIds.contains(actScheduleId)) {
                    continue;
                }
                if (!ActivityResService.isServerOpenLimitOk(scheduleTemplate, zoneOpenDaysUntilNow)) {
                    // 开服天数不对
                    continue;
                }
                if (activities.values().stream().anyMatch(it -> it.getProp().getActScheduleId() == actScheduleId)) {
                    // 这个排期已经有活动了
                    continue;
                }
                if (!ActivityResService.isZoneIdOk(scheduleTemplate, getIZone().getZoneId(), now)) {
                    // 当前区服限制
                    continue;
                }
                ActivitySchedule.Cell curOrNext = curOrNext(now, as);
                if (curOrNext == null || curOrNext.actId <= 0) {
                    continue;
                }
                final ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, curOrNext.actId);
                List<CommonEnum.ActivityUnitType> zoneUnitTypes = zoneUnitTypesWithChild(activityTemplate);
                if (zoneUnitTypes.isEmpty()) {
                    // 过滤掉不需要zone上逻辑的活动
                    continue;
                }
                if (curOrNext.expireTime.isBefore(now)) {
                    // 过期了不管啦
                    LOGGER.debug("{} activity expired, will ignore. {}", getOwner(), curOrNext.actId);
                } else if (curOrNext.startTime.isAfter(now)) {
                    // 活动尚未开始
                    tryAddNextReloadTimer(curOrNext.startTime);
                } else {
                    // 活动正在周期内
                    tryStartActivity(curOrNext, actScheduleId);
                }
            } catch (Exception e) {
                LOGGER.error("ZoneActivityMgr tryLoadAllSchedule error:", e);
            }
        }
    }

    private void forceCloseActivity(ZoneActivityProp activityProp) {
        final int actId = activityProp.getActivityId();
        int actScheduleId = activityProp.getActScheduleId();
        LOGGER.info("reloadActivitySchedule forceCloseActivity. {} {} {}", getOwner(), actScheduleId, actId);
        ZoneActivity loadedActivity = activities.get(actId);
        if (loadedActivity != null) {
            // 活动已经加载到内存中了，需要强制卸载，清理相关容器，这步做在卸载prop之前吧
            loadedActivity.forceOff();
        }
        ZoneActivityProp cloneProp = new ZoneActivityProp();
        cloneProp.mergeFromDb(activityProp.getCopyDbBuilder().build());
        // 强制卸载prop，转移到另外一个容器中去，等待脚本处理问题活动的数据
        // 因为无法判定活动到底是什么问题，所以每一个强制下架的活动都需要手动的脚本来处理活动数据
        final ZoneActivityModelProp model = getIZone().getZoneSideProp().getActivityModel();
        model.removeActivitiesV(actId);
        ZoneActivityProp existForceOffProp = model.getForceOffActivitiesV(actId);
        if (existForceOffProp != null) {
            LOGGER.error("reloadActivitySchedule forceCloseActivity, prop exist! {} {} {} existProp={} thisProp={}", getOwner(), actScheduleId, actId, existForceOffProp, cloneProp);
        } else {
            model.putForceOffActivitiesV(cloneProp);
        }
    }

    public ZoneActivityProp initFatherActivityProp(ActivityTemplate activityTemplate, int actScheduleId, Instant startTime) {
        ZoneActivityModelProp model = getIZone().getZoneSideProp().getActivityModel();
        ZoneActivityProp prop = model.addEmptyActivities(activityTemplate.getId());
        ZoneActivity.initProp(prop, activityTemplate, actScheduleId, startTime);
        return prop;
    }

    private void tryStartActivity(ActivitySchedule.Cell cell, int actScheduleId) throws Exception {
        int actId = cell.actId;
        ZoneActivity exist = activities.get(actId);
        if (exist != null) {
            WechatLog.error("{} tryStartActivity already exist! {}", getOwner(), actId);
            return;
        }
        ZoneActivityProp prop = initFatherActivityProp(cell.template, actScheduleId, cell.startTime);
        loadActivity(null, prop, cell.template);
    }

    @Nullable
    protected ActivitySchedule.Cell curOrNext(Instant now, ActivitySchedule as) {
        final Instant zoneOpenTime = Instant.ofEpochMilli(getIZone().getServerOpenTsMs());
        final ActivityScheduleTemplate template = as.template;
        CommonEnum.ActivityOpenType openType = template.getOpenType();

        ActivitySchedule.Cell curOrNext = null;
        switch (openType) {
            case AOT_FROM_ZONE_OPEN:
                curOrNext = ActivitySchedule.FromZoneOpen.cell(as, zoneOpenTime);
                break;
            case AOT_ON_DATE:
                curOrNext = ActivitySchedule.OnDate.cell(as);
                break;
            case AOT_ON_DATE_LOOP:
                curOrNext = ActivitySchedule.OnDateLoop.curOrNext(as, now);
                break;
            case AOT_ON_DATE_ZONE_LOOP:
                curOrNext = ActivitySchedule.OnDateZoneLoop.curOrNext(as, zoneOpenTime, now);
                break;
            case AOT_ZONE_LOOP:
                curOrNext = ActivitySchedule.ZoneLoop.curOrNext(as, zoneOpenTime, now);
                break;
            default:
        }
        return curOrNext;
    }


    public static List<CommonEnum.ActivityUnitType> zoneUnitTypes(ActivityTemplate activityTemplate) {
        Map<CommonEnum.ActivityUnitType, ZoneUnitConstructor> zoneUnitFactory = getUnityFactory();
        List<CommonEnum.ActivityUnitType> zoneUnitTypes = ActivityResService.parseCommonUnitTypes(activityTemplate).stream()
                .filter(zoneUnitFactory::containsKey)
                .collect(Collectors.toList());
        CommonEnum.ActivityUnitType specUnitType = activityTemplate.getSpecUnitType();
        if (specUnitType != null && zoneUnitFactory.containsKey(specUnitType)) {
            zoneUnitTypes.add(specUnitType);
        }
        return zoneUnitTypes;
    }

    public static List<CommonEnum.ActivityUnitType> zoneUnitTypesWithChild(ActivityTemplate activityTemplate) {
        List<CommonEnum.ActivityUnitType> zoneUnitTypes = zoneUnitTypes(activityTemplate);
        for (Integer childActId : activityTemplate.getChildActivityIdListList()) {
            final ActivityTemplate childActTemplate = ResHolder.findTemplate(ActivityTemplate.class, childActId);
            if (childActTemplate == null) {
                LOGGER.error("zoneUnitTypesWithChild failed child activity config not found fatherId={} childId={}", activityTemplate.getId(), childActId);
                continue;
            }
            zoneUnitTypes.addAll(zoneUnitTypesWithChild(childActTemplate));
        }
        return zoneUnitTypes;
    }

    public void expireActivity(ZoneActivity zoneActivity) {
        LOGGER.info("{} expireActivity {}", getOwner(), zoneActivity.getActivityId());
        activities.remove(zoneActivity.getActivityId());
        if (zoneActivity.getFather() == null) {
            getIZone().getZoneSideProp().getActivityModel().removeActivitiesV(zoneActivity.getActivityId());
        }
    }

    public void forceRemoveActivity(int activityId) {
        LOGGER.info("forceRemoveActivity {}", activityId);
        this.activities.remove(activityId);
    }

    public ZoneActivity loadActivity(@Nullable ZoneActivity father, ZoneActivityProp prop, ActivityTemplate activityTemplate) throws Exception {
        final int activityId = activityTemplate.getId();
        if (activities.containsKey(activityId)) {
            WechatLog.error("{} loadActivity already exist prop={}", getOwner(), prop);
            return null;
        }
        ZoneActivity zoneActivity = new ZoneActivity(iZone, father, prop, activityTemplate);

        zoneActivity.reload();

        // reload可能会过期，只有还激活的活动才需要加入到activitiesMap
        if (zoneActivity.isActive()) {
            activities.put(activityId, zoneActivity);

            // 拉取zoneUnit类型，通知scene对应活动效果
            activeSceneActivityEffect(prop, activityTemplate);
        }

        return zoneActivity;
    }

    private void activeSceneActivityEffect(ZoneActivityProp prop, ActivityTemplate activityTemplate) {
        List<CommonEnum.ActivityUnitType> zoneUnitTypes = zoneUnitTypes(activityTemplate);
        long expireTsMs = TimeUtils.second2Ms(prop.getEndTsSec());
        for (CommonEnum.ActivityUnitType zoneUnitType : zoneUnitTypes) {
            Optional<CommonEnum.ZoneActivityEffect> zoneActivityEffect = ActivityUnitEffect.getActivityEffect(zoneUnitType);
            // 无活动效果，无需通知
            if (!zoneActivityEffect.isPresent()) {
                continue;
            }
            informSceneActivityEffect(zoneActivityEffect.get(), expireTsMs);
        }
    }

    protected abstract void informSceneActivityEffect(CommonEnum.ZoneActivityEffect zoneActivityEffect, long expireTsMs);

    /**
     * 注意这里的zoneUnitId和玩家身上的unitId不是一个东西，不保证一致，模块同学自己校定
     */
    public BaseZoneActivityUnit findUnit(int activityId, int zoneUnitId) {
        ZoneActivity zoneActivity = activities.get(activityId);
        if (zoneActivity == null) {
            return null;
        }
        return zoneActivity.findUnit(zoneUnitId);
    }

    public BaseZoneActivityUnit findUnitWithTryReload(int activityId, int zoneUnitId) {
        BaseZoneActivityUnit unit = findUnit(activityId, zoneUnitId);
        if (unit == null) {
            tryLoadAllSchedule();
            unit = findUnit(activityId, zoneUnitId);
        }
        return unit;
    }

    /**
     * 谨慎！！ 获取指定类型的活动unit
     * <p>
     * 同时存在多个activity，返回null
     * 一个activity同时存在多个unit，返回null
     * 只判断SpecUnitType
     */
    public BaseZoneActivityUnit findUnitWithTryReloadByType(CommonEnum.ActivityUnitType unitType) {
        BaseZoneActivityUnit unit = findUnitByType(unitType);
        if (unit == null) {
            tryLoadAllSchedule();
            unit = findUnitByType(unitType);
        }
        return unit;
    }

    /**
     * 谨慎！！获取指定类型的活动unit
     * <p>
     * 同时存在多个activity，返回null
     * 一个activity同时存在多个unit，返回null
     * 只判断SpecUnitType
     */
    public BaseZoneActivityUnit findUnitByType(CommonEnum.ActivityUnitType unitType) {
        List<BaseZoneActivityUnit> unitList = new ArrayList<>();
        for (ZoneActivity value : activities.values()) {
            if (value.getTemplate().getSpecUnitType() != unitType) {
                continue;
            }
            if (!value.onlyOneUnit()) {
                LOGGER.error("AbsZoneSideActivityComponent findUnitByType fail, not onlyUnit, activity={}", value);
                return null;
            }
            unitList.add(value.getOnlyOneUnit());
        }
        if (unitList.size() > 1) {
            LOGGER.error("AbsZoneSideActivityComponent findUnitByType find many units unitType={}", unitType);
            return null;
        }
        if (unitList.isEmpty()) {
            return null;
        }
        return unitList.get(0);
    }

    private void loadBestCommanderHistoryRanks() throws InvalidProtocolBufferException {
        BestCommanderHistoryRankModelProp historyRankModel = getIZone().getZoneSideProp().getActivityModel().getBestCommanderHistoryRankModel();
        Collection<BestCommanderHistoryRankRecordProp> rankRecordProps = historyRankModel.getHistoryMap().values();
        for (BestCommanderHistoryRankRecordProp rankRecordProp : rankRecordProps) {
            StructMsg.BestCommanderHistoryRank historyRank = StructMsg.BestCommanderHistoryRank.newBuilder().mergeFrom(rankRecordProp.getHistoryRankBytes()).build();
            this.bestCommanderHistoryRanks.add(historyRank);
        }
        // 根据age顺序排列好
        this.bestCommanderHistoryRanks.sort(Comparator.comparingInt(StructMsg.BestCommanderHistoryRank::getAge).reversed());
    }

    public void addBestCommanderHistoryRank(StructMsg.BestCommanderHistoryRank.Builder history) {
        BestCommanderHistoryRankModelProp historyRankModel = getIZone().getZoneSideProp().getActivityModel().getBestCommanderHistoryRankModel();
        if (historyRankModel.getHistoryMap().size() >= BEST_COMMANDER_MAX_HISTORY_SIZE) {
            int lastIndex = bestCommanderHistoryRanks.size() - 1;
            int oldestAge = this.bestCommanderHistoryRanks.get(lastIndex).getAge();
            LOGGER.info("bestCommanderHistoryRank too much, will clear rank age={}", oldestAge);
            historyRankModel.removeHistoryMapV(oldestAge);
            this.bestCommanderHistoryRanks.remove(lastIndex);
        }

        int newestAge = 1;
        if (!this.bestCommanderHistoryRanks.isEmpty()) {
            newestAge = this.bestCommanderHistoryRanks.get(0).getAge() + 1;
        }
        history.setAge(newestAge);
        StructMsg.BestCommanderHistoryRank hr = history.build();
        historyRankModel.addEmptyHistoryMap(newestAge)
                .setAge(newestAge)
                .setHistoryRankBytes(hr.toByteString());
        this.bestCommanderHistoryRanks.add(0, hr);
        LOGGER.info("bestCommander newHistoryRank {}", hr);
    }

    public List<StructMsg.BestCommanderHistoryRank> getBestCommanderHistoryRanks() {
        return bestCommanderHistoryRanks;
    }

    public boolean hasOpenWithUnitType(CommonEnum.ActivityUnitType unitType) {
        for (ZoneActivity value : activities.values()) {
            if (value.getTemplate().getSpecUnitType() == unitType) {
                return value.getProp().getStatus() == CommonEnum.ActivityStatus.ACS_ACTIVE;
            }
        }
        return false;
    }
}

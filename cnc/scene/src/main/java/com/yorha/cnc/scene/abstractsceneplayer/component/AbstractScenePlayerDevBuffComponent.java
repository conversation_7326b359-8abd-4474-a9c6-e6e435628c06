package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.constant.DevBuffConstants;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructBattle;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BuffTemplate;
import res.template.WarManiaTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerDevBuffComponent extends AbstractComponent<AbstractScenePlayerEntity> implements AdditionProviderInterface {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerDevBuffComponent.class);

    protected DevBuffMgrBase<?> devBuffMgr;

    public AbstractScenePlayerDevBuffComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    public DevBuffMgrBase<?> getMgr() {
        return devBuffMgr;
    }

    public abstract DevBuffSysProp getBuffProp();

    public DevBuffProp addDevBuffByParam(CommonMsg.DevBuffAddParam param) {
        return getMgr().addDevBuff(param.getDevBuffId(),
                param.hasStartTime() ? param.getStartTime() : null,
                param.hasEndTime() ? param.getEndTime() : null,
                param.getBuffType(),
                param.getSourceType(),
                param.hasLayer() ? param.getLayer() : null);
    }

    public DevBuffProp removeDevBuffByParam(CommonMsg.DevBuffRemoveParam param) {
        return getMgr().removeDevBuff(param.getDevBuffId(),
                param.hasLayer() ? param.getLayer() : null,
                false);
    }

    /**
     * 添加buff
     *
     * @param sourceType 来源
     */
    public void addDevBuff(int buffId, CommonEnum.DevBuffSourceType sourceType) {
        getMgr().addDevBuff(buffId,
                null,
                null,
                getMgr().getBuffType(),
                sourceType,
                null);
    }

    /**
     * 添加buff
     *
     * @param endTime    指定的结束时间戳
     * @param sourceType 来源
     */
    public void addDevBuff(int buffId, long endTime, CommonEnum.DevBuffSourceType sourceType) {
        getMgr().addDevBuff(buffId,
                null,
                endTime,
                getMgr().getBuffType(),
                sourceType,
                null);
    }

    public DevBuffProp removeDevBuff(int buffId) {
        return getMgr().removeDevBuff(buffId, null, false);
    }

    public boolean isEffectOn(CommonEnum.BuffEffectType effectType) {
        return !getDevBuffByEffectType(effectType).isEmpty();
    }

    public boolean hasDevBuff(int buffId) {
        return getMgr().hasDevBuff(buffId);
    }

    /**
     * 移除指定效果的buff
     */
    public void removeDevBuffByEffectType(CommonEnum.BuffEffectType effectType) {
        for (DevBuffProp buff : getDevBuffByEffectType(effectType)) {
            removeDevBuff(buff.getDevBuffId());
        }
    }

    public List<DevBuffProp> getDevBuffByEffectType(CommonEnum.BuffEffectType type) {
        List<DevBuffProp> res = new ArrayList<>();
        for (Map.Entry<Integer, DevBuffProp> entry : getBuffProp().getDevBuff().entrySet()) {
            BuffTemplate buffTemplate = DevBuffMgrBase.getBuffTemplate(entry.getKey());
            if (buffTemplate != null) {
                if (buffTemplate.getType() == type.getNumber()) {
                    res.add(entry.getValue());
                }
            }
        }
        return res;
    }

    public boolean canOpenPeaceShield() {
        // 外面有PVP行军不能开盾
        for (ArmyEntity armyEntity : getOwner().getArmyMgrComponent().getMyArmyList()) {
            if (armyEntity.getBehaviourComponent().isPvpArmyByState()) {
                return false;
            }
        }
        return true;
    }

    public boolean canAddDevBuff(int buffId) {
        if (DevBuffUtil.isPeaceShieldBuff(buffId)) {
            if (!canOpenPeaceShield()) {
                return false;
            }
        }
        return getMgr().canAddDevBuff(buffId);
    }

    /**
     * 开启战争狂热
     */
    public void openWarFrenzy() {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        // 移除和平护盾
        removePeaceShield();

        // 加狂热buff
        WarManiaTemplate template = ResHolder.getInstance().findValueFromMap(WarManiaTemplate.class, getOwner().getMainCity().getLevel());
        if (template != null) {
            LOGGER.debug("player:{} openWarFrenzy. buffId:{}", getOwner(), template.getBuffId());
            addDevBuff(template.getBuffId(), CommonEnum.DevBuffSourceType.DBST_WAR_FRENZY);
        }
    }

    public boolean isWarFrenzyOn() {
        return !getDevBuffByEffectType(CommonEnum.BuffEffectType.ET_WAR_FRENZY).isEmpty();
    }

    public void removePeaceShield() {
        for (DevBuffProp buff : getDevBuffByEffectType(CommonEnum.BuffEffectType.ET_PEACE_SHIELD)) {
            if (removeDevBuff(buff.getDevBuffId()) != null) {
                LOGGER.info("player:{} peace shield off id:{} reason:{}", getOwner(), buff.getDevBuffId(), DevBuffConstants.PeaceShieldReason.MANUAL);
            }
        }
    }

    public void onPendingDevBuffExpired(long now) {
        getMgr().onPendingDevBuffExpired(now);
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.BUFF;
    }

    /**
     * buff比较特殊，加在scenePlayer上的buff必然是提供scene上用的加成。
     * 为了防止player和scenePlayer的加成相互覆盖，所以该接口只返回scene上用的加成
     */
    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        if (!AdditionUtil.isSceneAddition(additionId)) {
            return Maps.newHashMap();
        }
        return getMgr().getAdditionValue(additionId);
    }

    public List<DevBuffProp> getDevBuffByBuffType(CommonEnum.DevBuffType type) {
        return devBuffMgr.getDevBuffByBuffType(type);
    }

    public void addDevBuffByLayer(int buffId, Long startTime,
                                  Long endTime,
                                  CommonEnum.DevBuffType buffType,
                                  CommonEnum.DevBuffSourceType sourceType,
                                  int layer) {
        devBuffMgr.addDevBuff(buffId,
                startTime,
                endTime,
                buffType,
                sourceType,
                layer);
    }

    public void removeDevBuffByLayer(int buffId, int layer) {
        devBuffMgr.removeDevBuff(buffId, layer, false);
    }

    public void refreshClanBuff(StructBattle.DevBuffSys buffSys) {
        List<DevBuffProp> curClanBuff = getDevBuffByBuffType(CommonEnum.DevBuffType.DBT_CLAN_BUFF);
        DevBuffSysProp clanDevBuff = new DevBuffSysProp();
        clanDevBuff.mergeFromSs(buffSys);
        // 删除已经不存在的联盟buff
        for (DevBuffProp devBuffProp : curClanBuff) {
            if (clanDevBuff.getDevBuffV(devBuffProp.getDevBuffId()) == null) {
                removeDevBuff(devBuffProp.getDevBuffId());
            }
        }
        // 添加新增的联盟buff
        for (Map.Entry<Integer, DevBuffProp> entry : clanDevBuff.getDevBuff().entrySet()) {
            DevBuffProp existDevBuff = getBuffProp().getDevBuffV(entry.getKey());
            DevBuffProp buff = entry.getValue();
            // 船新的buff
            if (existDevBuff == null) {
                addDevBuffByLayer(
                        buff.getDevBuffId(),
                        buff.getStartTime(),
                        buff.getEndTime(),
                        buff.getDevBuffType(),
                        buff.getSourceType(),
                        buff.getLayer());
            } else {
                if (existDevBuff.getLayer() > buff.getLayer()) {
                    // 要删除层数
                    removeDevBuffByLayer(buff.getDevBuffId(), existDevBuff.getLayer() - buff.getLayer());
                } else if (existDevBuff.getLayer() < buff.getLayer()) {
                    // 要添加层数
                    addDevBuffByLayer(buff.getDevBuffId(),
                            buff.getStartTime(),
                            buff.getEndTime(),
                            buff.getDevBuffType(),
                            buff.getSourceType(),
                            buff.getLayer() - existDevBuff.getLayer());
                }
            }
        }
    }

    public void refreshAdditionCache() {
        devBuffMgr.refreshAdditionCache();
    }

    public void copyDevBuff(StructBattle.Int32DevBuffMap.Builder builder) {
        if (getBuffProp() == null) {
            return;
        }
        for (Map.Entry<Integer, DevBuffProp> buffProp : getBuffProp().getDevBuff().entrySet()) {
            builder.putDatas(buffProp.getKey(), buffProp.getValue().getCopySsBuilder().build());
        }
    }
}

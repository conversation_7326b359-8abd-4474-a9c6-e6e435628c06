package com.yorha.cnc.zone.zone.component;

import com.google.common.collect.Sets;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.activity.ActivityEffectMgr;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ActivityInfoProp;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 全服活动数据
 *
 * <AUTHOR>
 */
public class ZoneActivityComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneActivityComponent.class);

    public ZoneActivityComponent(ZoneEntity owner) {
        super(owner);
    }

    public void afterAllLoad() {
        refreshZoneEffect();
    }

    private void refreshZoneEffect() {
        long curTsMs = SystemClock.now();
        Set<Integer> activityInfoToDelete = Sets.newHashSet();
        for (ActivityInfoProp activityInfoProp : this.getProp().getSceneActivityInfoModel().getActivityInfos().values()) {
            int activityEffectVal = activityInfoProp.getActivityEffect();
            CommonEnum.ZoneActivityEffect activityEffect = CommonEnum.ZoneActivityEffect.forNumber(activityEffectVal);
            // 兜底：int转枚举失败（插入时有判断）
            if (activityEffect == null) {
                activityInfoToDelete.add(activityEffectVal);
                LOGGER.error("Unknown activityEffectVal={}", activityEffectVal);
                continue;
            }

            long expireTsMs = activityInfoProp.getActivityEndTsMs();
            // 已过期
            if (curTsMs > expireTsMs) {
                expireActivityEffect(activityEffect);
                activityInfoToDelete.add(activityEffectVal);
            } else { //未过期，添加效果
                addActivityEffect(activityEffect, expireTsMs);
            }
        }
        // 删除过期的&不存在的
        for (Integer activityEffectVal : activityInfoToDelete) {
            getProp().getSceneActivityInfoModel().removeActivityInfosV(activityEffectVal);
        }
    }

    /**
     * 添加活动效果
     *
     * @param activityEffect 活动效果
     * @param expireTsMs     活动过期时间
     */
    public void addActivityEffect(CommonEnum.ZoneActivityEffect activityEffect, long expireTsMs) {
        int activityEffectVal = activityEffect.getNumber();
        // 没有活动prop
        if (getProp().getSceneActivityInfoModel().getActivityInfosV(activityEffectVal) == null) {
            getProp().getSceneActivityInfoModel().putActivityInfosV(
                    new ActivityInfoProp()
                            .setActivityEffect(activityEffectVal)
                            .setActivityEndTsMs(expireTsMs)
            );
        }


        if (ActivityEffectMgr.activityEffectIsOn(activityEffect)) {
            return;
        }
        ActivityEffectMgr.activityEffectStart(activityEffect, this.getOwner());
        this.addExpireActivityTimer(activityEffect, expireTsMs);
    }

    private void addExpireActivityTimer(CommonEnum.ZoneActivityEffect activityEffect, long expireTsMs) {
        int activityEffectVal = activityEffect.getNumber();
        long expireDuration = expireTsMs - SystemClock.now();
        if (expireDuration <= 0) {
            LOGGER.warn("ZoneActivityComponent addExpireActivityTimer activityEffect={}, expireDuration:{} < 0, skipped ", activityEffect, expireDuration);
            return;
        }
        // 活动过期后触发相关逻辑
        getOwner().getTimerComponent().addTimerWithPrefix(getEntityId() + "-" + activityEffectVal,
                TimerReasonType.ZONE_EXPIRE_ACTIVITY_EFFECT,
                () -> this.expireActivityEffect(activityEffect),
                expireTsMs - SystemClock.now(),
                TimeUnit.MILLISECONDS);

    }

    /**
     * 活动是否有效
     *
     * @param activityEffect 活动效果
     * @return boolean
     */
    public boolean isActiveActivityEffect(CommonEnum.ZoneActivityEffect activityEffect) {
        ActivityInfoProp activityInfoProp = getProp().getSceneActivityInfoModel().getActivityInfosV(activityEffect.getNumber());
        if (activityInfoProp == null) {
            return false;
        }
        return activityInfoProp.getActivityEndTsMs() > SystemClock.now();
    }

    /**
     * 活动效果过期
     *
     * @param activityEffect 活动id
     */
    private void expireActivityEffect(CommonEnum.ZoneActivityEffect activityEffect) {
        int activityEffectVal = activityEffect.getNumber();
        ActivityInfoProp activityInfoProp = getProp().getSceneActivityInfoModel().getActivityInfosV(activityEffectVal);
        if (activityInfoProp == null) {
            return;
        }
        ActivityEffectMgr.activityActivityExpire(activityEffect, this.getOwner());

    }

    private ZoneInfoProp getProp() {
        return getOwner().getProp();
    }
}

package com.yorha.cnc.scene.event.player;

import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;

/**
 * <AUTHOR>
 */
public class PlayerPicFrameChangeEvent extends IEventWithEntityId {
    private final int picFrame;

    public PlayerPicFrameChangeEvent(long entityId, int picFrame) {
        super(entityId);
        this.picFrame = picFrame;
    }

    public int getPicFrame() {
        return picFrame;
    }
}

package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.zone.IZone;
import com.yorha.cnc.zone.activity.BaseZoneActivityUnit;
import com.yorha.cnc.zone.activity.ZoneBestCommanderTotalRankUnit;
import com.yorha.cnc.zone.activity.ZoneBestCommanderUnit;
import com.yorha.cnc.zone.activity.ZoneLotteryUnit;
import com.yorha.common.actor.SceneActivityScheduleService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneActivitySchedule.*;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SceneActivityScheduleServiceImpl implements SceneActivityScheduleService {
    private static final Logger LOGGER = LogManager.getLogger(SceneActivityScheduleServiceImpl.class);

    private final SceneActor sceneActor;

    public SceneActivityScheduleServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    private IZone getIZone() {
        if (sceneActor.getScene().isBigScene()) {
            return sceneActor.getBigScene().getZoneEntity();
        }
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    @Override
    public void handleBestCommanderFetchAsk(BestCommanderFetchAsk ask) {
        BaseZoneActivityUnit unit = getIZone().getSideActivityComponent().findUnitWithTryReload(ask.getActId(), ask.getActId());
        if (!(unit instanceof ZoneBestCommanderUnit)) {
            String msg = StringUtils.format("unit is not ZoneBestCommanderUnit {}", ask.getActId());
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, msg);
        }
        ZoneBestCommanderUnit bestCommanderUnit = (ZoneBestCommanderUnit) unit;
        BestCommanderFetchAns ans = bestCommanderUnit.handleFetch(ask);
        sceneActor.answer(ans);
    }

    @Override
    public void handleBestCommanderHistoryRankAsk(BestCommanderHistoryRankAsk ask) {
        List<StructMsg.BestCommanderHistoryRank> ranksList = getIZone().getSideActivityComponent().getBestCommanderHistoryRanks();
        BestCommanderHistoryRankAns.Builder ans = BestCommanderHistoryRankAns.newBuilder();
        ans.addAllHistoryRank(ranksList);
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleBestCommanderChooseItemAsk(BestCommanderChooseItemAsk ask) {
        BaseZoneActivityUnit unit = getIZone().getSideActivityComponent().findUnitWithTryReload(ask.getActId(), ask.getActId());
        if (!(unit instanceof ZoneBestCommanderTotalRankUnit)) {
            LOGGER.info("ZoneServiceImpl handleBestCommanderChooseItemAsk ask={}", ask);
            throw new GeminiException(ErrorCode.ACTIVITY_UNIT_NOT_EXIST);
        }
        ZoneBestCommanderTotalRankUnit zoneBestCommanderTotalRankUnit = (ZoneBestCommanderTotalRankUnit) unit;
        zoneBestCommanderTotalRankUnit.setPlayerChooseItem(ask.getPlayerId(), ask.getItemId());
        sceneActor.answer(BestCommanderChooseItemAns.getDefaultInstance());
    }

    @Override
    public void handleBestCommanderGetVolumeAsk(BestCommanderGetVolumeAsk ask) {
        final BaseZoneActivityUnit unit = getIZone().getSideActivityComponent().findUnitWithTryReload(ask.getActId(), ask.getActId());
        if (!(unit instanceof ZoneBestCommanderUnit)) {
            LOGGER.warn("ZoneServiceImpl handleBestCommanderGetVolumeAsk unit is null ask={}", ask);
            final BestCommanderGetVolumeAns.Builder ans = BestCommanderGetVolumeAns.newBuilder();
            ans.setActStage(CommonEnum.CommanderActivtyStage.CAS_NONE).setVolume(-1);
            sceneActor.answer(ans.build());
            return;
        }
        final ZoneBestCommanderUnit zoneBestCommanderUnit = (ZoneBestCommanderUnit) unit;
        final BestCommanderGetVolumeAns.Builder ans = BestCommanderGetVolumeAns.newBuilder();
        ans.setActStage(zoneBestCommanderUnit.getActStage()).setVolume(zoneBestCommanderUnit.getVolume());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleGetLotteryInfoAsk(GetLotteryInfoAsk ask) {
        ZoneLotteryUnit zoneLotteryUnit = (ZoneLotteryUnit) getIZone().getSideActivityComponent().findUnitWithTryReloadByType(CommonEnum.ActivityUnitType.AUT_LOTTERY);
        if (zoneLotteryUnit == null) {
            LOGGER.warn("SceneActivityScheduleServiceImpl GetLotteryInfoAsk fail, kvkPreActScoreRankUnit not exist");
            sceneActor.answer(GetLotteryInfoAns.getDefaultInstance());
            return;
        }
        sceneActor.answer(zoneLotteryUnit.getLotteryInfo());
    }
}

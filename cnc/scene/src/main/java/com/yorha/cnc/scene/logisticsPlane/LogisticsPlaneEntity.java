package com.yorha.cnc.scene.logisticsPlane;

import com.yorha.cnc.scene.logisticsPlane.component.LogisticsPlaneBehaviourComponent;
import com.yorha.cnc.scene.logisticsPlane.component.LogisticsPlaneMoveComponent;
import com.yorha.cnc.scene.logisticsPlane.component.LogisticsPlaneTransformComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.WarningInfoType;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.game.gen.prop.Int32CurrencyMapProp;
import com.yorha.game.gen.prop.LogisticsPlaneProp;
import com.yorha.proto.*;
import com.yorha.proto.EntityAttrOuterClass.EntityType;

/**
 * 物流飞机用于资源援助
 *
 * <AUTHOR>
 */
public class LogisticsPlaneEntity extends SceneObjEntity {
    private final LogisticsPlaneProp prop;
    private final LogisticsPlaneMoveComponent logisticsPlaneMoveComponent;
    private final LogisticsPlaneBehaviourComponent logisticsPlaneBehaviourComponent;
    private final ScenePlayerEntity player;

    public LogisticsPlaneEntity(LogisticsPlaneBuilder builder) {
        this(builder, false);
    }

    public LogisticsPlaneEntity(LogisticsPlaneBuilder builder, boolean isRestore) {
        super(builder);
        logisticsPlaneMoveComponent = builder.moveComponent(this);
        logisticsPlaneBehaviourComponent = builder.behaviourComponent(this);
        this.prop = builder.getProp();
        player = getScene().ownerActor().getScenePlayer(getPlayerId());
        initAllComponents();
        getPropComponent().initPropListener(isRestore);
        // 设置拥有者永远同步
        getAoiNodeComponent().setAlwaysSyncPlayer(getPlayerId());
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_LogisticsPlane;
    }

    @Override
    public LogisticsPlaneProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getLogisticsPlaneAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getLogisticsPlaneAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getLogisticsPlaneAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getLogisticsPlaneAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getLogisticsPlaneAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final LogisticsPlaneProp logisticsPlaneProp = LogisticsPlaneProp.of(builder.getFullAttr().getLogisticsPlaneAttr(), builder.getChangedAttr().getLogisticsPlaneAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setLogisticsPlaneAttr(logisticsPlaneProp.getCopyDbBuilder());
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return null;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return null;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return null;
    }

    @Override
    public CommonEnum.SceneObjectEnum getSceneObjType() {
        return CommonEnum.SceneObjectEnum.SOE_LOGISTICS_PLANE;
    }

    @Override
    public long getPlayerId() {
        return prop.getOwnerId();
    }

    public ScenePlayerEntity getPlayer() {
        return player;
    }

    @Override
    public long getClanId() {
        return getPlayer().getClanId();
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return null;
    }

    @Override
    public int getZoneId() {
        return getProp().getZoneId();
    }

    @Override
    public void deleteObj() {
        if (isDestroy()) {
            return;
        }
        getPlayer().getPlaneComponent().removeAttentionSpy(getEntityId());
        getDbComponent().deleteDb();
        getAoiNodeComponent().clearAlwaysSyncPlayer();
        super.deleteObj();
    }

    @Override
    public CommonEnum.SceneObjectNtfReason getRemoveReason() {
        return CommonEnum.SceneObjectNtfReason.SONR_RETURN_CITY;
    }

    @Override
    public LogisticsPlaneMoveComponent getMoveComponent() {
        return logisticsPlaneMoveComponent;
    }

    public LogisticsPlaneBehaviourComponent getBehaviourComponent() {
        return logisticsPlaneBehaviourComponent;
    }

    @Override
    public LogisticsPlaneTransformComponent getTransformComponent() {
        return (LogisticsPlaneTransformComponent) super.getTransformComponent();
    }

    public void setOwnerName(String name) {
        getProp().setOwnerName(name);
    }

    public void setClanName(String name) {
        getProp().setBriefClanName(name);
    }

    public void setClanId(long clanId) {
        getProp().setClanId(clanId);
    }

    public void setTemplateId(int templateId) {
        getProp().setTemplateId(templateId);
    }

    public Int32CurrencyMapProp getResource() {
        return getPlayer().getProp().getLogisticsModel().getLogisticsPlanesV(getEntityId()).getResources();
    }

    /**
     * copy到预警界面的pb
     */
    @Override
    public boolean copyToWarningInfo(StructPlayerPB.WarningInfoPB.Builder builder) {
        builder.setArmyId(getEntityId())
                .setPlayerName(getProp().getOwnerName())
                .setClanSimpleName(getProp().getBriefClanName())
                .setArriveTs(getMoveComponent().getMoveArriveTime());
        for (CurrencyProp currencyProp : getResource().values()) {
            builder.getResourcesBuilder().addDatas(StructPB.CurrencyPB.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount()).build());
        }
        long curChaseTargetId = getMoveComponent().getCurChaseTargetId();
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntity(curChaseTargetId);
        if (target == null) {
            return false;
        }
        builder.setEntityType(target.getEntityType().getNumber()).setTargetId(curChaseTargetId);
        if (target instanceof WarningInfoType) {
            ((WarningInfoType) target).formWarningInfo(builder);
        }
        return true;
    }


    public int getTemplate() {
        return prop.getTemplateId();
    }

}

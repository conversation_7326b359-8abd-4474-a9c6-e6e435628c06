package com.yorha.cnc.scene.actorservice;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.army.ArmyFactory;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.city.CityFactory;
import com.yorha.cnc.scene.common.ScenePushNotificationHelper;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.actor.SceneCityArmyService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ArmyActionInfoProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.ArmyActionType;
import com.yorha.proto.PlayerScene.Player_ChangeArmyAction_C2S;
import com.yorha.proto.SsSceneCityArmy.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 城池行军相关
 *
 * <AUTHOR>
 */
public class SceneCityArmyServiceImpl implements SceneCityArmyService {
    private static final Logger LOGGER = LogManager.getLogger(SceneCityArmyServiceImpl.class);
    private final SceneActor sceneActor;
    private final long sceneId;

    public SceneCityArmyServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
        sceneId = Long.parseLong(sceneActor.getId());
    }

    public SceneActor getSceneActor() {
        return sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    public long getSceneId() {
        return sceneId;
    }

    @Override
    public void handleRepairCityWallAsk(RepairCityWallAsk ask) {
        ScenePlayerEntity scenePlayer = sceneActor.getScenePlayer(ask.getPlayerId());
        scenePlayer.getWallComponent().repairWall();
        sceneActor.answer(RepairCityWallAns.getDefaultInstance());
    }

    @Override
    public void handleOutFireCityWallAsk(OutFireCityWallAsk ask) {
        ScenePlayerEntity scenePlayer = sceneActor.getScenePlayer(ask.getPlayerId());
        scenePlayer.getWallComponent().outFireWall();
        sceneActor.answer(OutFireCityWallAns.getDefaultInstance());
    }

    @Override
    public void handleSetCityFallAsk(SetCityFallAsk ask) {
        ScenePlayerEntity scenePlayer = sceneActor.getScenePlayer(ask.getPlayerId());
        Point newPoint = scenePlayer.getMainCity().getTransformComponent().cityFall();
        SetCityFallAns.Builder ans = SetCityFallAns.newBuilder();
        if (newPoint != null) {
            ans.setNewPoint(Struct.Point.newBuilder()
                    .setX(newPoint.getX())
                    .setY(newPoint.getY())
                    .build());
        }
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleMoveCityFixedAsk(MoveCityFixedAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer.getMainCity().getTransformComponent().isAscend()) {
            throw new GeminiException(ErrorCode.CITY_CITY_IS_ASCEND);
        }
        scenePlayer.getMainCity().getTransformComponent().moveCityFixed(ask.getX(), ask.getY(), ask.getMoveType());
        sceneActor.answer(MoveCityFixedAns.getDefaultInstance());
    }

    @Override
    public void handleMoveCityRandomAsk(MoveCityRandomAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer.getMainCity().getTransformComponent().isAscend()) {
            throw new GeminiException(ErrorCode.CITY_CITY_IS_ASCEND);
        }
        Point point = scenePlayer.getMainCity().getTransformComponent().moveCityRandom();
        MoveCityRandomAns.Builder builder = MoveCityRandomAns.newBuilder();
        sceneActor.answer(builder.setX(point.getX()).setY(point.getY()).build());
    }

    @Override
    public void handleMoveCityVerifyAsk(MoveCityVerifyAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        // 随机迁城的这个只检验玩家状态
        if (ask.getMoveType() == CommonEnum.MoveCityType.MCT_RANDOM) {
            Core.Code code = CityFactory.cityMovePlayerCheck(scenePlayer, ask.getMoveType());
            sceneActor.answer(MoveCityVerifyAns.newBuilder().setErrorCode(code.getId()).build());
            return;
        }
        Core.Code code = CityFactory.moveCityVerify(getScene(), ask.getX(), ask.getY(), ask.getMoveType(), scenePlayer);
        sceneActor.answer(MoveCityVerifyAns.newBuilder().setErrorCode(code.getId()).build());
    }

    @Override
    public void handleCreateArmyCheckAsk(CreateArmyCheckAsk ask) {
        CreateArmyCheckAns.Builder ans = CreateArmyCheckAns.newBuilder();
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL);
        }
        ArmyActionType armyActionType = ask.getParam().getArmyAction().getArmyActionType();
        long targetId = ask.getParam().getArmyAction().getTargetId();
        if (GameLogicConstants.needCheckEnergyActionType(armyActionType)) {
            Pair<Integer, Integer> monsterCostEnergy = getEnergy(scenePlayer, armyActionType, targetId, 0);
            if (monsterCostEnergy.getSecond() != 0) {
                ans.setMonsterNeedEnergy(monsterCostEnergy.getSecond());
            }
            if (monsterCostEnergy.getFirst() != 0) {
                ans.setMonsterId(monsterCostEnergy.getFirst());
            }
        }
        sceneActor.answer(ans.build());
    }

    /**
     * 获取体力消耗
     *
     * @return Pair<野怪配置id, 消耗体力>
     */
    private Pair<Integer, Integer> getEnergy(AbstractScenePlayerEntity scenePlayer, ArmyActionType actionType, long targetId, long armyId) {
        ArmyEntity army = null;
        if (armyId != 0) {
            army = getScene().getObjMgrComponent().getSceneObjWithTypeWithException(ArmyEntity.class, armyId);
        }
        // 加入集结 targetId 是集结id
        if (actionType == ArmyActionType.AAT_JoinRally) {
            if (scenePlayer.getClanId() == 0) {
                throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
            }
            RallyEntity rallyEntity = scenePlayer.getSceneClan().getRallyComponent().getRallyEntity(targetId);
            if (rallyEntity == null) {
                throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
            }
            targetId = rallyEntity.getProp().getTargetId();
        }
        SceneObjEntity sceneObjEntity = getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
        if (sceneObjEntity.isDestroy()) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY);
        }

        // 拾取掉落物 体力消耗
        if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_DropObject) {
            if ((army != null) && (army.getMoveComponent().getCurChaseTargetId() == targetId)) {
                return Pair.of(0, 0);
            }
            return Pair.of(0, ((DropObjectEntity) sceneObjEntity).getCost());
        }

        // 不是野怪 不用判了
        if (sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
            return Pair.of(0, 0);
        }
        MonsterEntity monsterEntity = ((MonsterEntity) sceneObjEntity);
        if (actionType == ArmyActionType.AAT_Battle) {
            // 普通野怪等级限制
            if (monsterEntity.getTemplate().getCategory() == CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE
                    && !scenePlayer.checkMonsterKillLevel(monsterEntity.getLevel())) {
                throw new GeminiException(ErrorCode.MONSTER_KILL_LEVEL_LIMIT);
            }
        } else {
            // 集结野怪限制
            ErrorCode rallyErrorCode = monsterEntity.canBeRallyWithCode();
            if (rallyErrorCode != ErrorCode.OK) {
                throw new GeminiException(rallyErrorCode);
            }
        }
        int energy = monsterEntity.getTemplate().getEnergy();
        if (GameLogicConstants.isRallyActionType(actionType)) {
            // 如果玩家是要集结野怪
            if (monsterEntity.getTemplate().getBeAttackModel() == CommonEnum.BeAttackModel.BAM_ONE_OR_RALLY) {
                // 并且是可以被单人进攻或集结的野怪
                energy = monsterEntity.getTemplate().getRallyEnergy();
                LOGGER.info("getEnergy: rally monster {} energy changed to {}", monsterEntity, energy);
            }
        }
        // 这个野怪配置就是不扣除体力，就不要走下面的计算了，公式会出错的
        if (energy <= 0) {
            return Pair.of(monsterEntity.getMonsterId(), 0);
        }
        if (armyId != 0) {
            // 同一个目标野怪不能多次扣除体力, 已经开战了，不用再扣了
            boolean isNewTarget = army.getStatusProp().getHuntingModel().getCurrentFocusMonsterId() != targetId;
            boolean alreadyBattle = army.getBattleComponent().hasRelationWithRoleId(targetId);
            if (isNewTarget && !alreadyBattle) {
                return Pair.of(monsterEntity.getMonsterId(), energy);
            } else {
                return Pair.of(monsterEntity.getMonsterId(), 0);
            }
        }

        return Pair.of(monsterEntity.getMonsterId(), energy);
    }

    @Override
    public void handleCreatePlayerArmyAsk(CreatePlayerArmyAsk ask) {
        ActorMsgEnvelope context = sceneActor.getCurrentEnvelope();
        final StructPlayer.CreateArmy_C2S_Param param = ask.getParam();
        ArmyFactory.createArmy(getScene(), ask.getPlayerId(), param, ask.getCostEnergy()
                , (e, army) -> afterArmyCreated(context, param, e, army));
    }

    protected void afterArmyCreated(ActorMsgEnvelope context, StructPlayer.CreateArmy_C2S_Param param, Exception e, ArmyEntity army) {
        if (e != null) {
            if (GeminiException.isLogicException(e)) {
                LOGGER.warn("exception_perf logic AbstractActor handle {} and catch exception:", context, e);
            } else {
                WechatLog.error("exception_perf AbstractActor handle {} and catch exception:", context, e);
            }
            sceneActor.answerWithException(context, e);
            return;
        }
        if (army == null) {
            sceneActor.answerWithException(context, e);
            return;
        }
        ArmyActionType actionType = param.getArmyAction().getArmyActionType();
        if (actionType == ArmyActionType.AAT_Battle) {
            notifyAttack(army, param.getArmyAction().getTargetId());
        }
        army.getBehaviourComponent().tryOpenWarFrenzy();
        CreatePlayerArmyAns.Builder builder = CreatePlayerArmyAns.newBuilder();
        if (actionType == ArmyActionType.AAT_Move) {
            if (param.getArmyAction().getTargetId() == 0 || param.getArmyAction().getTargetId() == -1) {
                Point endPoint = army.getMoveComponent().getEndPoint();
                if (endPoint != null) {
                    builder.getPointBuilder().setX(endPoint.getX()).setY(endPoint.getY());
                }
            }
        }
        sceneActor.answerWithContext(context, builder.setArmyId(army.getEntityId()).build());
    }

    @Override
    public void handleChangeArmyActionCheckAsk(ChangeArmyActionCheckAsk ask) {
        ChangeArmyActionCheckAns.Builder ans = ChangeArmyActionCheckAns.newBuilder();
        Player_ChangeArmyAction_C2S msg = MsgUtils.parseCsProto(MsgType.PLAYER_CHANGEARMYACTION_C2S, ask.getMsgBytes());

        SceneEntity scene = getScene();
        AbstractScenePlayerEntity scenePlayer = scene.getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (scenePlayer == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL);
        }
        ArmyActionType armyActionType = msg.getArmyAction().getArmyActionType();
        long targetId = msg.getArmyAction().getTargetId();

        SceneObjEntity sceneObjEntity = getScene().getObjMgrComponent().getSceneObjEntity(msg.getArmyId());
        if (sceneObjEntity == null || sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) {
            throw new GeminiException(ErrorCode.ARMY_NOT_EXIT.getCodeId());
        }
        ArmyEntity armyEntity = (ArmyEntity) sceneObjEntity;
        if (armyEntity.getPlayerId() != ask.getPlayerId()) {
            throw new GeminiException(ErrorCode.ARMY_NOT_OWNER);
        }

        // 检测指令合法性
        Point p = Point.valueOf(msg.getArmyAction().getTargetPoint().getX(), msg.getArmyAction().getTargetPoint().getY());
        armyEntity.getBehaviourComponent().checkActionValid(armyActionType, p, msg.getArmyAction().getTargetId());

        // 如果是打野的话需要查下需要多少体力
        if (GameLogicConstants.needCheckEnergyActionType(armyActionType)) {
            Pair<Integer, Integer> monsterCostEnergy = getEnergy(scenePlayer, armyActionType, targetId, msg.getArmyId());
            if (monsterCostEnergy.getFirst() != 0) {
                ans.setMonsterId(monsterCostEnergy.getFirst());
            }
            if (monsterCostEnergy.getSecond() != 0) {
                ans.setNeedEnergy(monsterCostEnergy.getSecond());
            }
            // 拿到玩家侧去算体力消耗
            ans.setKillStreak(armyEntity.getStatusProp().getHuntingModel().getKillStreak());
        }
        ans.setMainHeroId(armyEntity.getMainHero().getHeroId());
        ans.setDeputyHeroId(armyEntity.getDeputyHero().getHeroId());
        sceneActor.answer(ans.build());
    }

    public void notifyAttack(ArmyEntity army, long targetId) {
        SceneEntity scene = getScene();
        if (!scene.isMainScene()) {
            return;
        }
        SceneObjEntity sceneObj = scene.getObjMgrComponent().getSceneObjEntityWithException(targetId);
        if (sceneObj == null || sceneObj.isDestroy()) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        if (sceneObj.getEntityType() == EntityAttrOuterClass.EntityType.ET_City) {
            ScenePushNotificationHelper.pushBaseBeAttackNotification(scene, (CityEntity) sceneObj);
        }
        if (sceneObj.getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
            return;
        }

        MonsterEntity monster = (MonsterEntity) sceneObj;
        monster.getBattleComponent().addAttacker(army.getEntityId());

    }

    @Override
    public void handleChangePlayerArmyActionAsk(ChangePlayerArmyActionAsk ask) {
        Player_ChangeArmyAction_C2S msg = MsgUtils.parseCsProto(MsgType.PLAYER_CHANGEARMYACTION_C2S, ask.getMsgBytes());
        ArmyEntity armyEntity = getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, msg.getArmyId());
        if (armyEntity == null) {
            throw new GeminiException(ErrorCode.ARMY_NOT_EXIT.getCodeId());
        }
        if (armyEntity.getPlayerId() != ask.getPlayerId()) {
            sceneActor.answer(ChangePlayerArmyActionAns.getDefaultInstance());
            return;
        }
        ArmyActionInfoProp armyActionInfoProp = new ArmyActionInfoProp();
        armyActionInfoProp.mergeFromCs(msg.getArmyAction());
        StructPlayer.ArmyActionInfo action = armyActionInfoProp.getCopySsBuilder().build();
        if (action.getArmyActionType() == ArmyActionType.AAT_Battle) {
            notifyAttack(armyEntity, action.getTargetId());
        }
        ChangePlayerArmyActionAns.Builder res = ChangePlayerArmyActionAns.newBuilder();
        if (msg.getFollowArmys().getDatasList().isEmpty()) {
            armyEntity.getBehaviourComponent().handlePlayerReq(action, ask.getCostEnergy());
            armyEntity.getBehaviourComponent().tryOpenWarFrenzy();
            if (action.getArmyActionType() == ArmyActionType.AAT_Move) {
                if (action.getTargetId() == 0 || action.getTargetId() == -1) {
                    res.getPointBuilder().setX(action.getTargetPoint().getX()).setY(action.getTargetPoint().getY());
                }
            }
            sceneActor.answer(res.build());
            return;
        }
        // 多选部队，发起移动
        if (action.getArmyActionType() == ArmyActionType.AAT_Move) {
            Point target = Point.valueOf(action.getTargetPoint().getX(), action.getTargetPoint().getY());
            Map<Long, ArmyEntity> armyEntityMap = Maps.newHashMap();
            armyEntityMap.put(msg.getArmyId(), armyEntity);
            // 寻找离目标点最近的那支队伍
            long firstArriveArmyId = msg.getArmyId();
            double minDis = Point.calDisBetweenTwoPoint(armyEntity.getCurPoint(), target);
            for (long followArmyId : msg.getFollowArmys().getDatasList()) {
                ArmyEntity followArmy = getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, followArmyId);
                if (followArmy == null || followArmy.isDestroy() || followArmy.getPlayerId() != ask.getPlayerId()) {
                    continue;
                }
                armyEntityMap.put(followArmyId, followArmy);
                double curDis = Point.calDisBetweenTwoPoint(followArmy.getCurPoint(), target);
                if (curDis < minDis) {
                    firstArriveArmyId = followArmyId;
                    minDis = curDis;
                }
            }
            // 最近的到这个点  其他的跟随这个
            ArmyEntity firstArmy = armyEntityMap.get(firstArriveArmyId);
            firstArmy.getBehaviourComponent().handlePlayerReq(action, ask.getCostEnergy());
            for (ArmyEntity restArmy : armyEntityMap.values()) {
                if (restArmy.getEntityId() == firstArriveArmyId) {
                    continue;
                }
                StructPlayer.ArmyActionInfo.Builder builder = StructPlayer.ArmyActionInfo.newBuilder();
                builder.mergeFrom(action);
                Point newTarget = firstArmy.getTransformComponent().getFollowPoint(restArmy.getCurPoint(), target);
                builder.getTargetPointBuilder().setX(newTarget.getX()).setY(newTarget.getY());
                try {
                    restArmy.getBehaviourComponent().handlePlayerReq(builder.build(), ask.getCostEnergy());
                } catch (Exception e) {
                    if (GeminiException.isLogicException(e)) {
                        LOGGER.info("{} multiple army hand action failed {}", restArmy, ((GeminiException) e).getCodeId());
                    } else {
                        LOGGER.error("{} multiple army hand action failed ", restArmy, e);
                    }
                }
            }
            res.getPointBuilder().setX(target.getX()).setY(target.getY());
        } else {
            armyEntity.getBehaviourComponent().tryOpenWarFrenzy();
        }
        sceneActor.answer(res.build());
    }

    @Override
    public void handleForcedDefeatArmyAsk(ForcedDefeatArmyAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        // 战阵狂热中不可用
        if (scenePlayer.getDevBuffComponent().isWarFrenzyOn()) {
            throw new GeminiException(ErrorCode.FORCED_DEFEAT_ARMY_FAILED);
        }
        ArmyEntity armyEntity = getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, ask.getArmyId());
        if (armyEntity == null) {
            throw new GeminiException(ErrorCode.ARMY_NOT_EXIT.getCodeId());
        }
        if (armyEntity.getPlayerId() != ask.getPlayerId()) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
        }
        armyEntity.getMoveComponent().forceDefeat();
        sceneActor.answer(ForcedDefeatArmyAns.getDefaultInstance());
    }
}

package com.yorha.cnc.scene.event.battle;

import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.enums.qlog.battle.BattleAttackType;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.proto.CommonEnum;

/**
 * 战斗关系结束事件
 *
 * <AUTHOR>
 */
public class EndSingleBattleEvent extends IEvent {
    private final CommonEnum.BattleOverType type;
    private final boolean isSelfDead;
    private final boolean isEnemyDead;
    private final SceneObjBattleComponent other;
    private final CommonEnum.BattleType battleType;
    private final boolean enemyNpc;
    private final CommonEnum.BattleResult result;
    private final BattleAttackType attackType;
    /**
     * 自己的战斗报告
     */
    private final BattleRecord.RoleRecord roleRecord;

    public EndSingleBattleEvent(final BattleAttackType attackType, CommonEnum.BattleOverType type, boolean isSelfDead, boolean isEnemyDead, SceneObjBattleComponent other,
                                BattleRecord.RoleRecord roleRecord, CommonEnum.BattleType battleType, boolean enemyNpc) {
        this.attackType = attackType;
        this.type = type;
        this.isSelfDead = isSelfDead;
        this.isEnemyDead = isEnemyDead;
        this.other = other;
        this.roleRecord = roleRecord;
        this.battleType = battleType;
        this.enemyNpc = enemyNpc;
        this.result = getBattleResult(isEnemyDead, isSelfDead);
    }

    public static CommonEnum.BattleResult getBattleResult(boolean isEnemyDead, boolean isSelfDead) {
        if (!isEnemyDead && !isSelfDead) {
            return CommonEnum.BattleResult.BRT_DRAW;
        } else {
            return isEnemyDead ? CommonEnum.BattleResult.BRT_WIN : CommonEnum.BattleResult.BRT_LOSS;
        }
    }

    public CommonEnum.BattleOverType getType() {
        return type;
    }

    public boolean isSelfDead() {
        return isSelfDead;
    }

    public boolean isEnemyDead() {
        return isEnemyDead;
    }

    public SceneObjBattleComponent getOther() {
        return other;
    }

    public BattleRecord.RoleRecord getRoleRecord() {
        return roleRecord;
    }

    public CommonEnum.BattleType getBattleType() {
        return battleType;
    }

    public boolean isEnemyNpc() {
        return enemyNpc;
    }

    public CommonEnum.BattleResult getResult() {
        return result;
    }

    public BattleAttackType getAttackType() {
        return attackType;
    }
}

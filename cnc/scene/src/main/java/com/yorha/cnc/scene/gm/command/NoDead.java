package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.server.ServerContext;

import java.util.Map;

public class NoDead implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        boolean noDead = false;
        if (args.containsKey("flag")) {
            noDead = Integer.parseInt(args.get("flag")) == 1;
        }
        ServerContext.getServerDebugOption().setIsOpenNoDead(noDead);
    }

    @Override
    public String showHelp() {
        return "NoDead flag={value}";
    }
}

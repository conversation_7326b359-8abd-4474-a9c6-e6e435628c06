package com.yorha.cnc.scene.event.mapbuilding;

import com.yorha.cnc.scene.event.ievent.IEventWithClanId;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 * <p>
 * 地缘建筑切换占领者
 */
public class ChangeOccupierEvent extends IEventWithClanId {
    private final CommonEnum.Camp curCamp;

    public ChangeOccupierEvent(long entityId, long clanId, CommonEnum.Camp curCamp) {
        super(entityId, clanId);
        this.curCamp = curCamp;
    }

    public CommonEnum.Camp getCurCamp() {
        return curCamp;
    }
}

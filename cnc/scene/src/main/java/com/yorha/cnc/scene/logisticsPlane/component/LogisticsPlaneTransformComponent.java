package com.yorha.cnc.scene.logisticsPlane.component;

import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjTransformComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstSpyService;
import com.yorha.game.gen.prop.PointProp;

/**
 * 描述运输机位置的组件
 *
 * <AUTHOR>
 */
public class LogisticsPlaneTransformComponent extends SceneObjTransformComponent {

    public LogisticsPlaneTransformComponent(SceneObjEntity owner, PointProp pointData) {
        super(owner, pointData);
    }

    @Override
    public void init() {
        super.init();
    }


    @Override
    public LogisticsPlaneEntity getOwner() {
        return (LogisticsPlaneEntity) super.getOwner();
    }


    @Override
    public void resetModelRadius() {
        setModelRadius(ResHolder.getResService(ConstSpyService.class).getTemplate().getSpyPlaneRadius());
    }
}

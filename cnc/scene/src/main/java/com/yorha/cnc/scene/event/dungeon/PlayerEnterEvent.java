package com.yorha.cnc.scene.event.dungeon;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class PlayerEnterEvent extends IEvent {
    private final AbstractScenePlayerEntity player;

    public PlayerEnterEvent(AbstractScenePlayerEntity player) {
        this.player = player;
    }

    public AbstractScenePlayerEntity getPlayer() {
        return player;
    }
}

package com.yorha.cnc.scene.resBuilding;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.player.ClanSimpleNameChangeEvent;
import com.yorha.cnc.scene.resBuilding.component.ResBuildingCollectComponent;
import com.yorha.cnc.scene.resBuilding.enums.RefreshType;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int64ArmyArrowItemMapProp;
import com.yorha.game.gen.prop.ResBuildingProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.proto.CommonEnum.ArmyTargetType;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.ResourceBuildingState;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstSettingTemplate;
import res.template.MapBuildingTemplate;
import res.template.ResourceTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ResBuildingEntity extends SceneObjEntity implements BuildingEntityType {
    private static final Logger LOGGER = LogManager.getLogger(ResBuildingEntity.class);
    private final ResBuildingProp prop;
    private final ResBuildingCollectComponent collectComponent = new ResBuildingCollectComponent(this);

    public ResBuildingEntity(ResBuildingBuilder builder) {
        this(builder, false);
    }

    public ResBuildingEntity(ResBuildingBuilder builder, boolean isRestore) {
        super(builder);
        this.prop = builder.getProp();
        initAllComponents();
        getPropComponent().initPropListener(isRestore);
    }

    /**
     * 主动回收
     */
    public void recycle(boolean needNtfResMgr) {
        LOGGER.info("{} recycle res:{}", this, getProp().getCurNum());
        if (needNtfResMgr) {
            try {
                getScene().getResMgrComponent().removeResBuilding(this);
            } catch (Exception e) {
                WechatLog.error("remove resBuilding error", e);
            }
        }
        deleteObj();
    }

    @Override
    public void deleteObj() {
        super.deleteObj();
        getDbComponent().deleteDb();
    }

    public ResourceBuildingState getState() {
        return getProp().getState();
    }

    public long getArmyId() {
        return getProp().getCollect().getArmyId();
    }

    public void refreshClan(SceneClanEntity sceneClan) {
        LOGGER.info("{} refreshClan {}", this, sceneClan);
        if (sceneClan == null) {
            getProp().setClanId(0).setClanSimpleName("");
        } else {
            getProp().setClanId(sceneClan.getEntityId()).setClanSimpleName(sceneClan.getClanSimpleName());
        }
        getEventDispatcher().dispatch(new ClanSimpleNameChangeEvent(getEntityId(), getProp().getClanSimpleName()));
    }

    /**
     * 补充刷新是否要被回收掉
     */
    public boolean isNeedRecycle(RefreshType refreshType) {
        if (refreshType == RefreshType.GLOBAL) {
            return getCollectComponent().noOneCollect();
        }
        if (refreshType == RefreshType.REPLENISH) {
            // 没人在采集  且  是残矿
            return getCollectComponent().noOneCollect() && getProp().getCurNum() != getTemplate().getResNum();
        }
        return false;
    }

    @Override
    public void setExpression(int expressionId) {
        getProp().getExpression().setExpressionId(expressionId).setTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(ResHolder.getConsts(ConstSettingTemplate.class).getExpressionLastTime()));
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack();
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack();
    }

    private ErrorCode commonCheckBeAttack() {
        if (getProp().getCollect().getArmyId() == 0) {
            return ErrorCode.BATTLE_CANT;
        }
        return ErrorCode.OK;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_ResBuilding;
    }

    @Override
    public ResBuildingProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getResBuildingAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getResBuildingAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getResBuildingAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getResBuildingAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getResBuildingAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final ResBuildingProp resBuildingProp = ResBuildingProp.of(builder.getFullAttr().getResBuildingAttr(), builder.getChangedAttr().getResBuildingAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setResBuildingAttr(resBuildingProp.getCopyDbBuilder());
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        prop.getTarget().setTargetType(ArmyTargetType.ATT_RESBUILDING)
                .setName("")
                .setClanSimpleName(getProp().getClanSimpleName())
                .setTemplateId(getProp().getTemplateId())
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        long armyId = getArmyId();
        if (armyId == 0) {
            return null;
        }
        ArmyEntity sceneObj = getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
        if (sceneObj == null) {
            return null;
        }
        return sceneObj.getBattleComponent();
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return null;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return null;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public long getPlayerId() {
        long armyId = getArmyId();
        if (armyId == 0) {
            return 0;
        }
        return getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId).getPlayerId();
    }

    @Override
    public long getClanId() {
        long armyId = getArmyId();
        if (armyId == 0) {
            return 0;
        }
        return getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId).getClanId();
    }

    @Override
    public Camp getCampEnum() {
        long armyId = getArmyId();
        if (armyId == 0) {
            return Camp.C_NONE;
        }
        return getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId).getCampEnum();
    }

    @Override
    public int getZoneId() {
        return getProp().getCollect().getZoneId();
    }

    public ResourceTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(ResourceTemplate.class, getProp().getTemplateId());
    }

    @Override
    public Int64ArmyArrowItemMapProp getArrowProp() {
        return getProp().getArrow();
    }

    public ResBuildingCollectComponent getCollectComponent() {
        return collectComponent;
    }

    @Override
    public String toString() {
        return "ResBuildingEntity{" +
                " id=" + getEntityId() +
                " templateId=" + prop.getTemplateId() +
                " armyId=" + prop.getCollect().getArmyId() +
                " state=" + prop.getState() +
                " current " + getCurPoint() +
                '}';
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, getTemplate().getResID());
    }

    public String getCollectQLogAction() {
        return "res_collect";
    }
}

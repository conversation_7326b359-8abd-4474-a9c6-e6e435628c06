package com.yorha.cnc.scene.logisticsPlane;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.game.gen.prop.LogisticsPlaneProp;
import com.yorha.game.gen.prop.ScenePlayerLogisticsStatusProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.common.enums.error.ErrorCode.LOGISTICS_UNSUPPORT_ACTION;

/**
 * 运输机工厂
 *
 * <AUTHOR>
 */
public class LogisticsPlaneFactory {
    private static final Logger LOGGER = LogManager.getLogger(LogisticsPlaneFactory.class);

    public static LogisticsPlaneEntity createLogisticsPlaneEntity(SceneEntity scene, ScenePlayerEntity player, SsScenePlane.CreateLogisticsPlaneAsk ask) {
        // 出征部队数量检测
        player.getArmyMgrComponent().checkArmyCreate();
        // 目标对象不存在
        AbstractScenePlayerEntity playerEntity = scene.getPlayerMgrComponent().getScenePlayer(ask.getLogisticsInfo().getTargetId());
        if (playerEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY);
        }
        // 构建行军出生点
        Point bornPoint = getBornPoint(ask.getLogisticsInfo(), player, playerEntity.getMainCity().getCurPoint());
        LogisticsPlaneProp prop = new LogisticsPlaneProp();
        prop.setOwnerId(player.getPlayerId())
                .setZoneId(player.getZoneId())
                .setClanId(player.getClanId())
                .setOwnerName(player.getName())
                .getMove().getCurPoint().setX(bornPoint.getX()).setY(bornPoint.getY());
        CityEntity targetCity = playerEntity.getMainCity();
        if (targetCity.getTransformComponent().isAscend()) {
            throw new GeminiException(ErrorCode.ARMY_NOT_HAS_TARGET_POINT);
        }
        prop.getMove().getYaw().setX(targetCity.getCurPoint().getX() - bornPoint.getX()).setY(targetCity.getCurPoint().getY() - bornPoint.getY());
        MoveData path = player.getScene().getPathFindMgrComponent().searchPrePath(playerEntity.getMainCity(), bornPoint, targetCity.getCurPoint(), GameLogicConstants.TRANSPORT_MOVE);
        if (path == null) {
            throw new GeminiException(ErrorCode.MOVE_CROSS_LOSE);
        }
        // 填充运输的资源与赋税
        ScenePlayerLogisticsStatusProp logisticsStatusProp = new ScenePlayerLogisticsStatusProp();
        for (Struct.Currency currency : ask.getResourcesList()) {
            logisticsStatusProp.getResources().put(currency.getType(), new CurrencyProp().setType(currency.getType()).setCount(currency.getCount()));
        }
        for (Struct.Currency tax : ask.getTaxList()) {
            logisticsStatusProp.getTax().put(tax.getType(), new CurrencyProp().setType(tax.getType()).setCount(tax.getCount()));
        }
        long entityId = scene.ownerActor().nextId();
        logisticsStatusProp.setLogisticsPlaneId(entityId);
        //设置scenePlayer上当前运输机的状态
        player.getLogisticMgrComponent().setLogisticsPlaneStatus(logisticsStatusProp);
        LogisticsPlaneBuilder builder = new LogisticsPlaneBuilder(scene, entityId, prop);
        LogisticsPlaneEntity logisticsPlane = new LogisticsPlaneEntity(builder);
        logisticsPlane.addIntoScene();
        try {
            logisticsPlane.getBehaviourComponent().handleAction(ask.getLogisticsInfo(), path);
            logisticsPlane.getDbComponent().insertIntoDb();
        } catch (Exception e) {
            logisticsPlane.deleteObj();
            LOGGER.error("createLogisticsPlaneEntity handle action failed.", e);
            throw e;
        }
        return logisticsPlane;
    }

    private static Point getBornPoint(StructMsg.LogisticsInfo info, AbstractScenePlayerEntity playerEntity, Point targetPoint) {
        //主动资源援助类型
        if (info.getActionType() == CommonEnum.LogisticsActionType.LAT_RESOURCE_ASSIST) {
            CityEntity mainCity = playerEntity.getMainCity();
            Point cityPoint = mainCity.getCurPoint();
            return Point.getPointWithDisToSrcPoint(Point.valueOf(cityPoint.getX(), cityPoint.getY()),
                    targetPoint, mainCity.getTransformComponent().getPathCollisionRadius() + 1);
        }
        return null;
    }

    /**
     * db恢复
     */
    public static void restoreLogisticsPlane(SceneEntity sceneEntity, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        LogisticsPlaneProp logisticsPlaneProp = LogisticsPlaneProp.of(fullAttr.getLogisticsPlaneAttr(), changedAttr.getLogisticsPlaneAttr());
        AbstractScenePlayerEntity player = sceneEntity.getPlayerMgrComponent().getScenePlayer(logisticsPlaneProp.getOwnerId());
        // 校验scenePlayer上正确的有该运输机的状态信息
        if (player instanceof ScenePlayerEntity) {
            ScenePlayerEntity scenePlayer = (ScenePlayerEntity) player;
            ScenePlayerLogisticsStatusProp logisticsPlanesV = scenePlayer.getProp().getLogisticsModel().getLogisticsPlanesV(fullAttr.getEntityId());
            if (logisticsPlanesV == null) {
                LOGGER.error("{} has not logistics status prop {}", scenePlayer, fullAttr.getEntityId());
                throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "restoreLogisticsPlane player not have status: " + fullAttr.getEntityId());
            }
        } else {
            LOGGER.error("{} has not logistics status prop {}", player, fullAttr.getEntityId());
            throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "restoreLogisticsPlane player not have status: " + fullAttr.getEntityId());
        }
        LogisticsPlaneBuilder logisticsPlaneBuilder = new LogisticsPlaneBuilder(sceneEntity, fullAttr.getEntityId(), logisticsPlaneProp);
        LogisticsPlaneEntity logisticsPlaneEntity = new LogisticsPlaneEntity(logisticsPlaneBuilder, true);
        logisticsPlaneEntity.addIntoScene();
        LOGGER.info("create {} success", logisticsPlaneEntity);
        // 回城
        logisticsPlaneEntity.getBehaviourComponent().onReturnCityEnd();
    }
}




package com.yorha.cnc.scene.areaSkill;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.areaSkill.component.AreaSkillAdditionComponent;
import com.yorha.cnc.scene.areaSkill.component.AreaSkillBattleComponent;
import com.yorha.cnc.scene.areaSkill.component.AreaSkillBuffComponent;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.AreaSkillProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RangeSkillTemplate;

/**
 * 区域技能
 * 回收：lifeTime(秒)到期，配表控制:父类死亡 or 父类脱战
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
public class AreaSkillEntity extends SceneObjEntity {
    private static final Logger LOGGER = LogManager.getLogger(AreaSkillEntity.class);

    private final AreaSkillProp prop;
    /**
     * 到期销毁entity
     */
    private int lifeTime;
    private final AreaSkillBattleComponent battleComponent;
    private final AreaSkillBuffComponent buffComponent;
    private final AreaSkillAdditionComponent additionComponent;
    private final AbstractScenePlayerEntity ownerScenePlayer;
    private final int areaTemplateId;

    public AreaSkillEntity(AreaSkillBuilder builder, AbstractScenePlayerEntity scenePlayer, BattleRole master, int lifeTime, int templateId) {
        super(builder);
        this.prop = builder.getProp();
        this.ownerScenePlayer = scenePlayer;
        this.lifeTime = lifeTime;
        this.areaTemplateId = templateId;
        CommonEnum.SceneObjType sceneObjType = CommonEnum.SceneObjType.SOT_ARMY;
        if (master != null) {
            sceneObjType = master.getType();
        }

        // buffComponent和AdditionComponent的初始化一定要在battleComponent之前
        this.additionComponent = new AreaSkillAdditionComponent(this);
        this.buffComponent = new AreaSkillBuffComponent(this);
        this.battleComponent = new AreaSkillBattleComponent(this, sceneObjType, master);
        initAllComponents();
        getPropComponent().initPropListener(false);
        LOGGER.info("AreaSkillEntity created.id:{} templateId:{}, lifeTime:{}, ownerScenePlayer:{}, owner:{}", getEntityId(), prop.getTemplateId(), lifeTime, ownerScenePlayer, master);
    }

    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        if (super.onTickDispatch(reason)) {
            return true;
        }
        // 生命周期结束或者主人死亡后，销毁
        if (reason == SceneTickReason.TICK_LIFE) {
            if (needDelete()) {
                deleteObj();
            }
            return true;
        }
        return false;
    }

    public BattleRole getMaterRole() {
        return getBattleComponent().getBattleRole().getMasterRole();
    }

    private boolean needDelete() {
        RangeSkillTemplate template = ResHolder.getTemplate(RangeSkillTemplate.class, areaTemplateId);
        if (isLifeEnd()) {
            return true;
        }
        // 主人死亡
        if (template.getMasterDieRemove() > 0 && isOwnerDeadOrExists()) {
            return true;
        }
        // 主人脱战
        if (template.getMasterRemove() > 0 && isOwnerOutBattle()) {
            return true;
        }
        return false;
    }

    private boolean isLifeEnd() {
        return lifeTime-- <= 0;
    }

    private boolean isOwnerOutBattle() {
        BattleRole materRole = getMaterRole();
        return materRole != null && (materRole.getAllValidRelation().size() <= 0);
    }

    private boolean isOwnerDeadOrExists() {
        BattleRole materRole = getMaterRole();
        return materRole != null && (materRole.isDead() || materRole.getAdapter().isDestroy());
    }

    @Override
    public void deleteObj() {
        super.deleteObj();
        LOGGER.info("AreaSkillEntity destroy.id:{} templateId:{}, lifeTime:{}", getEntityId(), prop.getTemplateId(), lifeTime);
    }

    @Override
    public EntityAttrOuterClass.EntityType getEntityType() {
        return EntityAttrOuterClass.EntityType.ET_AreaSkill;
    }

    @Override
    public AreaSkillProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getAreaSkillAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getAreaSkillAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getAreaSkillAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public AreaSkillBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public AreaSkillBuffComponent getBuffComponent() {
        return buffComponent;
    }

    @Override
    public AreaSkillAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    @Override
    public CommonEnum.SceneObjectEnum getSceneObjType() {
        return CommonEnum.SceneObjectEnum.SOE_AREA_SKILL;
    }

    @Override
    public long getPlayerId() {
        if (ownerScenePlayer != null) {
            return ownerScenePlayer.getPlayerId();
        }
        return getMaterRole().getAdapter().getPlayerId();
    }

    @Override
    public long getClanId() {
        if (ownerScenePlayer != null) {
            return ownerScenePlayer.getClanId();
        }
        return getMaterRole().getAdapter().getClanId();
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        if (ownerScenePlayer != null) {
            return ownerScenePlayer.getCampEnum();
        }
        return getMaterRole().getAdapter().getCampEnum();
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return ErrorCode.FAILED;
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.FAILED;
    }

    public String getClanName() {
        SceneClanEntity sceneClan = getScene().getClanMgrComponent().getSceneClanOrNull(getClanId());
        if (sceneClan != null) {
            return sceneClan.getClanName();
        }
        return "";
    }

    public String getClanSName() {
        SceneClanEntity sceneClan = getScene().getClanMgrComponent().getSceneClanOrNull(getClanId());
        if (sceneClan != null) {
            return sceneClan.getClanSimpleName();
        }
        return "";
    }

    public Struct.PlayerCardHead getCardHead() {
        if (ownerScenePlayer != null) {
            return ownerScenePlayer.getCardHead().getCopySsBuilder().setName(getClanName()).build();
        } else {
            return Struct.PlayerCardHead.newBuilder().build();
        }
    }
}

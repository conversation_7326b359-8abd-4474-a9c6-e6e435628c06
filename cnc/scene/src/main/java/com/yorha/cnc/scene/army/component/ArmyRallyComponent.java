package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.TickMoveNoPathEvent;
import com.yorha.cnc.scene.event.army.ArmyEnterInteriorEvent;
import com.yorha.cnc.scene.event.army.PlayerOperationPreEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.io.MsgType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.StructPlayer.ArmyActionInfo;
import com.yorha.proto.User;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * <p>
 * 行军集结逻辑
 */
public class ArmyRallyComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyRallyComponent.class);

    public ArmyRallyComponent(ArmyEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        if (getOwner().isRallyArmy()) {
            return;
        }
        // 玩家指令前置事件 check 加入集结情况
        getOwner().getEventDispatcher().addEventListenerRepeat(this::checkRallyState, PlayerOperationPreEvent.class);
        // 自身死亡 删除 退盟  退出集结
        getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onRallySubEventDispatch, DieEvent.class, DeleteEvent.class, ClanChangeEvent.class, TickMoveNoPathEvent.class);
    }

    /**
     * 指令更改 需要检查是否更改了集结目标
     */
    private void checkRallyState(PlayerOperationPreEvent event) {
        // 正在去往集结点  改变指令 退出该集结
        long curRallyId = getCurRallyId();
        if (curRallyId == 0) {
            return;
        }
        ArmyActionInfo armyAction = event.getArmyActionInfo();
        if (armyAction.getArmyActionType() != ArmyActionType.AAT_JoinRally || curRallyId != armyAction.getTargetId()) {
            getOwner().getMoveComponent().stopMove();
            LOGGER.info("{} leaveRally {} reason: changeOperation", getOwner(), curRallyId);
            leaveRally(curRallyId);
        }
    }

    /**
     * 处理加入集结的指令
     */
    public void handleJoinRally(ArmyActionInfo armyActionInfo, int costEnergy) {
        long rallyId = armyActionInfo.getTargetId();
        // 重复指令
        if (getCurRallyId() == rallyId) {
            return;
        }
        RallyEntity targetRally = getTargetRallyEntity(rallyId);
        if (targetRally == null) {
            LOGGER.warn("{} tryJoinRally but target is null. rallyId: {}", getOwner(), rallyId);
            return;
        }
        LOGGER.info("{} tryJoinRally. rallyId: {}", getOwner(), rallyId);
        // 集结Id设置一下
        getOwner().getProp().setCurRallyId(rallyId);
        // 通知该集结体先占坑
        targetRally.getArmyMgrComponent().addNewArmy(getOwner(), RallyArmyRoleType.RART_Follower, costEnergy);
        if (ServerContext.getServerDebugOption().isGmSwitch() && armyActionInfo.getDebugFastMove()) {
            arriveRally(rallyId, RallyArmyRoleType.RART_Follower);
            return;
        }
        // 获取加入集结的移动目标体
        SceneObjEntity moveTargetEntity = targetRally.getMoveTargetEntity();
        try {
            getOwner().getMoveComponent().moveToTargetAsync(
                    moveTargetEntity,
                    TroopInteractionType.JOIN_RALLY,
                    () -> arriveRally(rallyId, RallyArmyRoleType.RART_Follower), null,
                    (codeId) -> {
                        if (ErrorCode.isOK(codeId)) {
                            return;
                        }
                        getOwner().getScenePlayer().sendErrorCode(codeId);
                        onMoveFailed();
                    });
        } catch (Exception e) {
            onMoveFailed();
            throw e;
        }
    }

    private void onMoveFailed() {
        RallyEntity targetRally = getTargetRallyEntity(getCurRallyId());
        getOwner().getProp().setCurRallyId(0);
        getOwner().getStatusComponent().setStaying();
        if (targetRally == null) {
            return;
        }
        targetRally.getArmyMgrComponent().removeArmy(getOwner().getPlayerId(), getEntityId(), false);
    }

    /**
     * 到达集结点  真正加入集结
     */
    public void arriveRally(long rallyId, RallyArmyRoleType type) {
        if (getCurRallyId() != rallyId) {
            RallyEntity targetRally = getTargetRallyEntity(rallyId);
            if (targetRally != null) {
                targetRally.getArmyMgrComponent().removeArmy(getOwner().getPlayerId(), getEntityId(), false);
            }
            LOGGER.error("arriveRally failed {} {} {}", getOwner(), getCurRallyId(), rallyId);
            return;
        }
        // 加入集结时结束掉所有战场
        getOwner().getBattleComponent().forceEndAllBattle();
        // 通知集结体到达
        RallyEntity targetRally = getTargetRallyEntity(rallyId);
        if (targetRally == null) {
            LOGGER.error("{} arriveRally rallyId: {}, type:{}", getOwner(), rallyId, type);
            return;
        }
        targetRally.getArmyMgrComponent().onArmyArriveRally(getOwner());
        // 等下 可能到了以后 触发集结出发 但是又解散了 得处理。。
        if (targetRally.isDestroy()) {
            LOGGER.info("arriveRally but rally dismiss {} {}", getOwner(), rallyId);
            return;
        }
        // 修改下自己的prop
        SceneObjEntity moveTargetEntity = targetRally.getMoveTargetEntity();
        getOwner().getPropComponent().onAttachChange(AttachState.AAS_RALLY, moveTargetEntity.getEntityId());
        getOwner().getProp().setCurRallyId(rallyId).setRallyRole(type);
        getOwner().getTransformComponent().changePoint(moveTargetEntity.getCurPoint().getDeepCopy(), true);
        // 移除视野
        if (getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_RALLY);
        }
        getOwner().getEventDispatcher().dispatch(new ArmyEnterInteriorEvent(getEntityId()));
        LOGGER.info("{} arriveRally. rallyId: {}, type:{}", getOwner(), rallyId, type);
    }

    /**
     * 更改指令 离开集结
     */
    private void leaveRally(long rallyId) {
        RallyEntity targetRally = getTargetRallyEntity(rallyId);
        if (targetRally == null) {
            LOGGER.error("{} leaveRally error.  rallyId: {}", getOwner(), rallyId);
            return;
        }
        targetRally.getArmyMgrComponent().removeArmy(getOwner().getPlayerId(), getEntityId(), false);
        LOGGER.info("{} leaveRally. rallyId: {}", getOwner(), rallyId);
    }

    /**
     * 退出集结后处理 回城   集结解散/被遣返/退出联盟
     */
    public void onQuitRally(Point returnStartPoint, boolean isNeedReturn, int rollBackEnergy) {
        LOGGER.info("{} quitRally. rallyId: {}", getOwner(), getOwner().getProp().getCurRallyId());
        if (getOwner().getProp().getRallyRole() != RallyArmyRoleType.RART_Single) {
            getOwner().getPropComponent().onAttachChange(AttachState.AAS_NONE, 0);
            getOwner().getProp().setRallyRole(RallyArmyRoleType.RART_Single);
        }
        getOwner().getProp().setCurRallyId(0);
        // 已经到达后退出的， 把回去的初始坐标改一下
        if (returnStartPoint != null) {
            getOwner().getTransformComponent().changePoint(returnStartPoint);
        }
        // 重新加入aoi
        if (!getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_RALLY);
        }
        if (getOwner().getBattleComponent().hasAnyAlive()) {
            if (isNeedReturn) {
                getOwner().getMoveComponent().returnMainCity();
                getOwner().getBehaviourComponent().refreshArmyState();
            } else {
                getOwner().getStatusComponent().setStaying();
            }
        } else {
            getOwner().getMoveComponent().retreat();
        }
        getOwner().getBattleComponent().trySendHospitalFullCauseDeadMail();
        // 回滚体力
        if (rollBackEnergy != 0) {
            SsPlayerMisc.EnergyRollbackCmd energyRollbackCmd = SsPlayerMisc.EnergyRollbackCmd.newBuilder().setEnergy(rollBackEnergy).build();
            getOwner().getScenePlayer().tellPlayer(energyRollbackCmd);
        }
    }

    /**
     * 集结地图建筑成功结束了  看看能不能转援助 不能就回家
     */
    public void rallyMapBuildingEnd(long targetId) {
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        if (target == null) {
            onQuitRally(null, true, 0);
            return;
        }
        if (ErrorCode.isOK(target.canBeAssist(getOwner().getPlayerId(), getOwner().getCampEnum(), getOwner().getAliveSoldierNum(), getOwner().getEntityId()))) {
            onQuitRally(null, false, 0);
            getOwner().getAssistComponent().handleAssist(targetId);
        } else {
            onQuitRally(null, true, 0);
        }
    }

    /**
     * 集结相关移动数据需要同步给集结体
     */
    public void onRefreshRallyMove(long moveArriveTs, Point point) {
        if (getOwner().isRallyArmy()) {
            if (getOwner().getRallyEntity() != null) {
                getOwner().getRallyEntity().getStateComponent().onRallyArmyMove(moveArriveTs, point, false);
            }
            return;
        }
        if (getCurRallyId() != 0) {
            RallyEntity targetRally = getTargetRallyEntity(getCurRallyId());
            if (targetRally != null) {
                targetRally.getArmyMgrComponent().onFlowArmyMove(getEntityId(), moveArriveTs);
                return;
            }
            LOGGER.error("onStartMove error army: {} rallyId: {}", getOwner(), getCurRallyId());
        }
    }

    /**
     * 集结关注事件触发（自身的） 死亡/删除/退盟/途中没路
     */
    private void onRallySubEventDispatch(IEvent event) {
        if (getCurRallyId() == 0) {
            return;
        }
        long clanId = getOwner().getClanId();
        // 退盟了自己属性就拿不到原来联盟了， 得从事件里拿
        if (event.equals(ClanChangeEvent.class)) {
            ClanChangeEvent e = (ClanChangeEvent) event;
            clanId = e.getOldClanId();
        }
        RallyEntity rallyEntity = getOwner().getScenePlayer().getRallyComponent().getRallyEntity(clanId, getCurRallyId());
        if (rallyEntity == null) {
            return;
        }
        if (rallyEntity.getProp().getOrganizerId() == getOwner().getPlayerId()) {
            // 是发起者 直接解散
            if (event.equals(ClanChangeEvent.class)) {
                rallyEntity.dismiss(RallyDismissReason.RDR_QUIT_CLAN);
            }
            // 城池升天回收时触发
            if (event.equals(DeleteEvent.class)) {
                rallyEntity.dismiss(RallyDismissReason.RDR_ORGANIZER_CITY_ASCEND);
            }
            // 发送集结解散
            if (getOwner().getScene().isMainScene()) {
                User.RallyNtfMsg.Builder builder = User.RallyNtfMsg.newBuilder().setIsExist(false);
                getOwner().getScenePlayer().sendMsgToClient(MsgType.RALLYNTFMSG, builder.build());
            }
        } else {
            rallyEntity.getArmyMgrComponent().removeArmy(getOwner().getPlayerId(), getEntityId(), event.equals(ClanChangeEvent.class));
        }
    }

    public long getCurRallyId() {
        return getOwner().getProp().getCurRallyId();
    }

    public RallyEntity getTargetRallyEntity(long rallyId) {
        return getOwner().getScenePlayer().getRallyComponent().getRallyEntity(rallyId);
    }
}

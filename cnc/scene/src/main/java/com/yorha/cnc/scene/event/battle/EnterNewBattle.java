package com.yorha.cnc.scene.event.battle;

import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;

/**
 * <AUTHOR>
 * <p>
 * 开始新战场
 */
public class EnterNewBattle extends IEvent {
    private final long targetId;
    private final EntityAttrOuterClass.EntityType entityType;
    private final CommonEnum.SceneObjType battleRoleType;

    public EnterNewBattle(long targetId, EntityAttrOuterClass.EntityType entityType, CommonEnum.SceneObjType battleRoleType) {
        this.targetId = targetId;
        this.entityType = entityType;
        this.battleRoleType = battleRoleType;
    }

    public long getTargetId() {
        return targetId;
    }

    public EntityAttrOuterClass.EntityType getEntityType() {
        return entityType;
    }

    public CommonEnum.SceneObjType getBattleRoleType() {
        return battleRoleType;
    }

    @Override
    public String toString() {
        return "EnterNewBattle{" +
                "targetId=" + targetId +
                ", entityType=" + entityType +
                ",battleRoleType=" + battleRoleType +
                '}';
    }
}

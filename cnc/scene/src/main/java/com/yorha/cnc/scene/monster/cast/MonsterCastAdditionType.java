package com.yorha.cnc.scene.monster.cast;

import com.yorha.cnc.scene.monster.cast.impl.SelfRangeHasArmyAddition;
import com.yorha.proto.CommonEnum.MonsterCastAddition;
import res.template.MonsterCastTemplate;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 野怪蓄力增益类型
 */
public enum MonsterCastAdditionType {

    /**
     * 半径范围内有玩家部队
     */
    SELF_RANGE_HAS_ARMY(MonsterCastAddition.ARMY_RANGE) {
        @Override
        public AbstractMonsterCastAddition createAddition(MonsterCastTemplate template) {
            return new SelfRangeHasArmyAddition(template);
        }
    };

    private static final Map<MonsterCastAddition, MonsterCastAdditionType> typeMap = new EnumMap<>(MonsterCastAddition.class);

    static {
        for (MonsterCastAdditionType type : MonsterCastAdditionType.values()) {
            typeMap.put(type.getAddition(), type);
        }
    }

    private final MonsterCastAddition addition;

    MonsterCastAdditionType(MonsterCastAddition addition) {
        this.addition = addition;
    }

    public MonsterCastAddition getAddition() {
        return addition;
    }

    public static MonsterCastAdditionType typeOf(MonsterCastAddition addition) {
        return typeMap.get(addition);
    }

    public abstract AbstractMonsterCastAddition createAddition(MonsterCastTemplate template);
}

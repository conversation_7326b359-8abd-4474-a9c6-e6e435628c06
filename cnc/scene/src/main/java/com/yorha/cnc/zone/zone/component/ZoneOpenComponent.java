package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.map.BigSceneResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BigSceneMonsterMilestoneTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 开服管理器
 *
 * <AUTHOR>
 */
public class ZoneOpenComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneOpenComponent.class);

    public ZoneOpenComponent(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public void init() {

    }

    public void onSceneOk() {
        refreshMonsterMilestone();
    }

    public void onTick() {
        tryOpenZone();
        // 刷新当前野怪里程碑
        refreshMonsterMilestone();
    }

    public void tryOpenZone() {
        // 开服过了
        if (getOwner().isZoneOpen()) {
            return;
        }
        final long openZoneTime = getProp().getServerOpenTsMs();
        if (openZoneTime <= 0) {
            LOGGER.error("tryOpenZone but openZoneTime:{} <= 0", openZoneTime);
            return;
        }
        // 没到开服时间
        if (SystemClock.now() < openZoneTime) {
            return;
        }
        // 开服大吉!!!
        LOGGER.info("open zone start");
        // 开服标记
        getProp().setIsOpened(true);
        getOwner().getSeasonComponent().onZoneOpen();
        // 踢所有人下线，为了让玩家再次登录，触发onLogin加载活动
        SessionHelper.kickOffAllSession(CommonEnum.SessionCloseReason.SCR_OPEN_ZONE);
        // 初始化activity
        getOwner().getSideActivityComponent().initActivity();
        getOwner().getSideRefreshComponent().initActivityRefresh();
        getOwner().getBigScene().getObjMgrComponent().onZoneOpen();
        // 启动大地图里程碑
        getOwner().getMilestoneComponent().initMileStone(true);
        LOGGER.info("open zone finish");
    }

    public void refreshMonsterMilestone() {
        if (!getOwner().isZoneOpen()) {
            return;
        }
        final long startMs = getOwner().getServerOpenTsMs();
        int hour = TimeUtils.subDateToHour(startMs, SystemClock.now());
        int storyId = getOwner().getBigScene().getStoryId();
        BigSceneMonsterMilestoneTemplate nowMilestone = ResHolder.getResService(BigSceneResService.class).getMonsterMilestone(storyId, hour);
        long delta = TimeUnit.HOURS.toMillis(nowMilestone.getTimePeriod() + 1);
        long newNextMilestoneStamp = startMs + delta;
        getProp().setNextMilestoneStamp(newNextMilestoneStamp);
        getProp().setCurMonsterMilestone(nowMilestone.getId());
    }

    public ZoneInfoProp getProp() {
        return getOwner().getProp();
    }

    public static void updateZoneOpenTimeMonitor(int zoneId, long value) {
        MonitorUnit.GAME_ZONE_STATUS_OPEN_TIME.labels(
                ServerContext.getBusId(),
                ServerContext.getWorldIdStr(),
                String.valueOf(zoneId))
                .set(value);
    }
}

package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.json.Army.ArmyConfig;
import com.yorha.common.qlog.json.Army.SoldierAllConfig;
import com.yorha.common.qlog.json.hero.HeroLevelStar;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.game.gen.prop.SoldierProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncArmyFormation;

import java.util.ArrayList;
import java.util.List;

/**
 * scene上触发玩家流水，尽量只上报一个玩家id
 *
 * <AUTHOR>
 */
public class ScenePlayerQlogComponent extends AbstractComponent<ScenePlayerEntity> implements QlogPlayerFlowInterface {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerQlogComponent.class);

    public ScenePlayerQlogComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public String getGameappid() {
        return "";
    }

    @Override
    public String getVOpenID() {
        return "";
    }

    @Override
    public String getAccountCreate_time() {
        return "";
    }

    @Override
    public String getAccountReg_time() {
        return "";
    }

    @Override
    public String getServer_type() {
        return "";
    }

    @Override
    public String getIZoneAreaID() {
        return "";
    }

    @Override
    public String getGameSvrId() {
        return String.valueOf(getOwner().getScene().getZoneId());
    }

    @Override
    public String getWorldId() {
        return String.valueOf(ServerContext.getServerInfo().getWorldId());
    }

    @Override
    public String getNow_coordinate() {
        return "";
    }

    @Override
    public String getVRoleID() {
        return String.valueOf(getOwner().getEntityId());
    }

    @Override
    public String getVRoleName() {
        return "";
    }

    @Override
    public int getRole_num() {
        return 0;
    }

    @Override
    public String getRole_type() {
        return "";
    }

    @Override
    public String getRoleCreate_time() {
        return "";
    }

    @Override
    public int getILevel() {
        return 0;
    }

    @Override
    public int getIVipLevel() {
        return 0;
    }

    @Override
    public long getIRoleCE() {
        return 0;
    }

    @Override
    public long getHighestRole_power() {
        return 0;
    }

    @Override
    public int getPlayerFriendsNum() {
        return 0;
    }

    @Override
    public float getRecharge_sum() {
        return 0;
    }

    @Override
    public long getIGuildID() {
        return 0;
    }

    @Override
    public String getVGuildName() {
        return "";
    }

    @Override
    public String getGuildGrade() {
        return "";
    }

    @Override
    public String getGuildClass() {
        return "";
    }

    @Override
    public int getPlatID() {
        return -1;
    }

    @Override
    public String getGame_language() {
        return "";
    }

    @Override
    public String getVClientIP() {
        return "";
    }

    @Override
    public String getVClientIPV6() {
        return "";
    }

    @Override
    public long getKill_num() {
        return 0;
    }

    @Override
    public String getClientVersion() {
        return "";
    }

    @Override
    public int getMoney_config() {
        return 0;
    }

    @Override
    public int getTransferOrNot() {
        return 0;
    }

    /**
     * 发送编队行军log
     */
    public void sendArmyQLog(ArmyEntity army, String action) {
        HeroProp mainHero = army.getProp().getTroop().getMainHero();
        HeroProp deputyHero = army.getProp().getTroop().getDeputyHero();

        boolean isReturn = "army_depart".equals(action);
        List<SoldierAllConfig> allSoldier = new ArrayList<>();
        List<ArmyConfig> aliveSoldier = new ArrayList<>();
        for (SoldierProp soldierProp : army.getProp().getTroop().getTroop().values()) {
            int count = soldierProp.getNum() - soldierProp.getDeadNum() - soldierProp.getSevereWoundNum() - soldierProp.getSlightWoundNum();
            ArmyConfig armyConfig = new ArmyConfig(soldierProp.getSoldierId(), count);
            aliveSoldier.add(armyConfig);
            if (isReturn) {
                allSoldier.add(new SoldierAllConfig(soldierProp));
            }
        }
        HeroLevelStar majorHero = new HeroLevelStar();
        majorHero.setHeroId(mainHero.getHeroId());
        majorHero.setHeroLevel(mainHero.getLevel());
        majorHero.setStarLevel(mainHero.getStar());

        HeroLevelStar subHero = new HeroLevelStar();
        subHero.setHeroId(deputyHero.getHeroId());
        subHero.setHeroLevel(deputyHero.getLevel());
        subHero.setStarLevel(deputyHero.getStar());


        QlogCncArmyFormation flow = new QlogCncArmyFormation();
        flow.fillHead(this);
        flow.setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setArmyId(String.valueOf(army.getEntityId()))
                .setArmyConfig(JsonUtils.toJsonString(aliveSoldier))
                .setMajorHeroConfig(JsonUtils.toJsonString(majorHero))
                .setSubHeroConfig(JsonUtils.toJsonString(subHero))
                .sendToQlog();
        if (isReturn) {
            LOGGER.info("army return soldier {}", JsonUtils.toJsonString(allSoldier));
        }
    }
}

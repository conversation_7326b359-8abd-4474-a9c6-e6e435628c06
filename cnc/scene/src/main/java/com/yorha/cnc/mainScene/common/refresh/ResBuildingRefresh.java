package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneResRegionItem;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.utils.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ResBuildingRefresh extends RefreshTask {
    private static final Logger LOGGER = LogManager.getLogger(ResBuildingRefresh.class);

    private final int regionId;
    private int needCreateNum;
    private int totalCreate;

    public ResBuildingRefresh(SceneEntity owner, int regionId) {
        super(owner);
        this.regionId = regionId;
    }

    @Override
    public int getHandleNumPerTick() {
        return GameLogicConstants.RES_BUILDING_CREATE_MAX_PER_SECOND;
    }

    @Override
    public Pair<Boolean, Integer> run() {
        if (!(getOwner().getResMgrComponent() instanceof MainSceneResMgrComponent)) {
            return Pair.of(true, 0);
        }
        final MainSceneResMgrComponent mainSceneResMgrComponent = (MainSceneResMgrComponent) getOwner().getResMgrComponent();
        final MainSceneResRegionItem mgrItem = mainSceneResMgrComponent.getResRegionItem(regionId);
        if (mgrItem == null) {
            LOGGER.error("ResBuildingRefresh but regionItem not exist {}", regionId);
            return Pair.of(true, 0);
        }
        if (needCreateNum == 0) {
            needCreateNum = mgrItem.buildScanPartList();
        }
        if (needCreateNum <= 0) {
            mgrItem.onTaskFinish(totalCreate);
            return Pair.of(true, 0);
        }
        if (needCreateNum > getHandleNumPerTick()) {
            totalCreate += mgrItem.refreshResBuilding(getHandleNumPerTick());
            needCreateNum -= getHandleNumPerTick();
            return Pair.of(false, getHandleNumPerTick());
        }
        totalCreate += mgrItem.refreshResBuilding(needCreateNum);
        mgrItem.onTaskFinish(totalCreate);
        return Pair.of(true, needCreateNum);
    }

    @Override
    public String toString() {
        return "ResBuildingRefresh{" +
                "regionId=" + regionId +
                ", needCreateNum=" + needCreateNum +
                '}';
    }
}

package com.yorha.cnc.scene.gm;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.common.gm.GmCommandMgr;

/**
 * <AUTHOR>
 */
public class SceneGmCommandMgr extends GmCommandMgr<SceneActor> {
    public static SceneGmCommandMgr getInstance() {
        return InstanceHolder.INSTANCE;
    }

    private static class InstanceHolder {
        private static final SceneGmCommandMgr INSTANCE = new SceneGmCommandMgr();
    }

    private SceneGmCommandMgr() {
    }

    @Override
    protected String getCommandDir() {
        return "com.yorha.cnc.scene.gm.command";
    }
}

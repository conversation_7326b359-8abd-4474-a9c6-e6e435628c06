package com.yorha.cnc.scene.dorpObject.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.dropObject.PickUpDropObjectSuccessEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.DropObjectProp;
import com.yorha.game.gen.prop.ItemProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncMapPublicChest;
import res.template.DropObjectTemplate;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class DropObjectPickUpComponent extends SceneObjComponent<DropObjectEntity> {
    private static final Logger LOGGER = LogManager.getLogger(DropObjectPickUpComponent.class);

    public DropObjectPickUpComponent(DropObjectEntity owner) {
        super(owner);
    }

    private final Set<Long> pickingPlayers = new HashSet<>();

    @Override
    public DropObjectEntity getOwner() {
        return super.getOwner();
    }

    @Override
    public void init() {
        if (getOwner().getScene().isMainScene()) {
            QlogCncMapPublicChest.init(getOwner().getScene().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("refresh")
                    .setRoleId(String.valueOf(0))
                    .setBeforePickTimes(0)
                    .setAfterPickTimes(0)
                    .setMapChestID(getOwner().getTemplate().getId())
                    .setUniqueMapChestID(getEntityId())
                    .sendToQlog();
        }
    }

    @Override
    public void postInit() {
        super.postInit();
        // 生命周期
        if (getOwner().getTemplate().getLifeTime() > 0) {
            getOwner().addSceneSchedule(SceneTimerReason.TIMER_LIFE, getOwner().getTemplate().getLifeTime());
        }
    }

    /**
     * 拾取开始
     */
    public void pickUpStart(ArmyEntity armyEntity) {
        getProp().getPickUpArmy().add(armyEntity.getEntityId());
        pickingPlayers.add(armyEntity.getPlayerId());
        LOGGER.info("{} start pick up.  army: {}", getOwner(), armyEntity.getEntityId());
    }

    /**
     * 拾取中断
     */
    public void pickUpBreak(long armyId, long playerId) {
        getProp().getPickUpArmy().remove(armyId);
        pickingPlayers.remove(playerId);
        LOGGER.info("{} break pick up.  army: {}", getOwner(), armyId);
    }

    /**
     * 拾取完成
     */
    public void pickUpFinished(ArmyEntity armyEntity) {
        // 增加拾取次数
        getProp().getPickUpPlayers().add(armyEntity.getPlayerId());
        getProp().getPickUpArmy().remove(armyEntity.getEntityId());
        pickingPlayers.remove(armyEntity.getPlayerId());
        getProp().setPickUpTimes(getProp().getPickUpTimes() + 1);
        LOGGER.info("{} pickUp finished. army: {}", getOwner(), armyEntity.getEntityId());
        sendReward(armyEntity);
        this.getOwner().getEventDispatcher().dispatch(new PickUpDropObjectSuccessEvent(this.getEntityId(), armyEntity.getEntityId()));
        if (getOwner().getScene().isMainScene()) {
            QlogCncMapPublicChest.init(getOwner().getScene().getQlogComponent())
                    .setDtEventTime(TimeUtils.now2String())
                    .setAction("map_chest_collect")
                    .setRoleId(String.valueOf(armyEntity.getPlayerId()))
                    .setBeforePickTimes(getProp().getPickUpTimes() - 1)
                    .setAfterPickTimes(getProp().getPickUpTimes())
                    .setMapChestID(getOwner().getTemplate().getId())
                    .setUniqueMapChestID(getEntityId())
                    .sendToQlog();
        }
        // 没有采集次数了，删除掉落物
        if (getOwner().getTemplate().getPickUpCount() > 0) {
            if (getProp().getPickUpTimes() >= getOwner().getTemplate().getPickUpCount()) {
                LOGGER.info("{} consumed", getOwner());
                getOwner().onLifeEnd();
            }
        }
    }

    /**
     * 拾取成功后发奖
     *
     * @param armyEntity
     */
    protected void sendReward(ArmyEntity armyEntity) {
        DropObjectTemplate template = ResHolder.getTemplate(DropObjectTemplate.class, getProp().getTemplateId());
        // 邮件的方式发奖励
        if (template.getMailId() > 0) {
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
            mailSendParams.setMailTemplateId(template.getMailId());
            Struct.ItemPairList.Builder itemBuilder = Struct.ItemPairList.newBuilder();
            for (ItemProp itemProp : getProp().getItemReward()) {
                Struct.ItemPair itemPair = Struct.ItemPair.newBuilder().setItemTemplateId(itemProp.getTemplateId()).setCount(itemProp.getNum()).build();
                itemBuilder.addDatas(itemPair);
            }
            if (template.getAutoCollect()) {
                mailSendParams.setIsOnlyForShow(true)
                        .getAddItemByMailBuilder()
                        .setAddItemReason(CommonEnum.Reason.ICR_PICKUP)
                        .setSubReason(String.valueOf(getProp().getTemplateId()))
                        .setAddItemWhenOnlyForShow(true);
            }
            mailSendParams.setItemReward(itemBuilder);
            MailUtil.sendMailToPlayer(
                    CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(armyEntity.getPlayerId())
                            .setZoneId(armyEntity.getScenePlayer().getZoneId())
                            .build(),
                    mailSendParams.build());
            return;
        }
       
        // 奖励直接进背包
        SsPlayerMisc.AddRewardCmd.Builder addPickUpRewardCmd = SsPlayerMisc.AddRewardCmd.newBuilder();
        addPickUpRewardCmd.setReason(CommonEnum.Reason.ICR_PICKUP).setSubReason(String.valueOf(getProp().getTemplateId()));
        getOwner().getProp().getItemReward().forEach(itemProp -> {
            addPickUpRewardCmd.getItemRewardsBuilder()
                    .addDatas(Struct.ItemReward.newBuilder()
                            .setItemTemplateId(itemProp.getTemplateId())
                            .setCount(itemProp.getNum()).build());
        });
        armyEntity.getScenePlayer().tellPlayer(addPickUpRewardCmd.build());
    }

    /**
     * 是否能拾取的检测
     */
    public void startPickUpCheck(long armyId, long playerId) {
        // 出生动画期间
        if (getProp().getBornTime() > SystemClock.now()) {
            throw new GeminiException(ErrorCode.DROP_OBJECT_NOT_BORN.getCodeId());
        }
        ErrorCode errorCode = checkPickUp(playerId);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        // 拾取中
        if (getProp().getPickUpArmy().contains(armyId)) {
            throw new GeminiException(ErrorCode.DROP_OBJECT_PLAYER_CANT_REPEAT_PICK.getCodeId());
        }
    }

    public ErrorCode checkPickUp(long playerId) {
        // 达到拾取上限
        DropObjectTemplate template = getOwner().getTemplate();
        if (template == null) {
            return ErrorCode.DROP_OBJECT_PICK_UP_TIMES_LIMIT;
        }
        if (template.getPickUpCount() > 0 && template.getPickUpCount() <= getProp().getPickUpTimes()) {
            return ErrorCode.DROP_OBJECT_PICK_UP_TIMES_LIMIT;
        }
        // 该玩家已经拾取过
        if (template.getDropObjectType() == CommonEnum.DropObjectType.ITEM) {
            if (getProp().getPickUpPlayers().contains(playerId)) {
                return ErrorCode.DROP_OBJECT_PLAYER_CANT_REPEAT_PICK;
            }
        }
        return ErrorCode.OK;
    }

    public boolean playerHasPicking(long playerId) {
        if (pickingPlayers.contains(playerId)) {
            LOGGER.info("{} pickUp already picking {}", getOwner(), playerId);
            return true;
        }
        return false;
    }

    protected DropObjectProp getProp() {
        return getOwner().getProp();
    }
}

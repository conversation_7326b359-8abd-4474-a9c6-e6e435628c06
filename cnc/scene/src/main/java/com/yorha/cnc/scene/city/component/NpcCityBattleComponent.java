package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.*;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerArmyMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerSoldierMgrComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.dungeon.NpcCityDiedEvent;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.cnc.scene.npcplayer.component.NpcPlayerArmyMgrComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.helper.GuardTowerHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.CityGarrisonProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SceneObjType;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import res.template.TroopTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class NpcCityBattleComponent extends CityBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(NpcCityBattleComponent.class);
    private boolean isCoreTaskTarget;

    public NpcCityBattleComponent(CityEntity owner) {
        super(owner);
    }

    @Override
    protected NpcPlayerEntity getScenePlayer() {
        return (NpcPlayerEntity) super.getScenePlayer();
    }

    @Override
    public void postInit() {
        // 初始化兵力
        for (IntPairType pair : getOwner().getTemplate().getSoldierPairList()) {
            getOwner().getScenePlayer().getSoldierMgrComponent().addInCitySoldier(pair.getKey(), pair.getValue(), null);
        }
    }

    /**
     * 成为核心目标  先构建出数据来
     */
    public void onBeCoreTarget() {
        if (isCoreTaskTarget) {
            return;
        }
        isCoreTaskTarget = true;
        buildGarrisonHeroOrMecha();
        buildGarrisonSoldier();
        buildGuardTower();
        getBattleRole().refreshTroop();
        getBattleRole().buildHero();
        getBattleRole().initState();
    }

    @Override
    public void onAttackerArrived() {
        if (isCoreTaskTarget) {
            return;
        }
        super.onAttackerArrived();
    }

    @Override
    protected void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        super.onSettleRoundEvent(event);
        // 防御塔减耐久
        SoldierLossDTO guardTowerLoss = event.getDamageResult().getGuardTowerLoss();
        if (guardTowerLoss != null) {
            int towerCurHp = getOwner().getProp().getGuardTowerHp();
            int decHp = Math.min(towerCurHp, Math.max(0, guardTowerLoss.totalDead()));
            LOGGER.debug("npcCity:{}, guardTower damaged:{}. towerCurHp:{}, decHealth:{}", getOwner(), guardTowerLoss, towerCurHp, decHp);
            int newHp = towerCurHp - decHp;
            if (newHp < 0) {
                getOwner().getProp().setGuardTowerHp(0);
                LOGGER.error("{} decDefenseTowerHp error. need dec:{}  after dec:{}", getOwner(), decHp, newHp);
                return;
            }
            getOwner().getProp().setGuardTowerHp(newHp);
        }
    }

    @Override
    protected void onInnerArmyOutDropChild(InnerArmyDelEvent event) {
        // 父方法都是非战斗不处理  但是这里是核心目标的时候要处理的
        if (!isInBattle() && !isCoreTaskTarget) {
            return;
        }
        ArmyEntity innerArmy = event.getArmyEntity();
        for (int innerArmySoldierId : innerArmy.getProp().getTroop().getTroop().keySet()) {
            getBattleRole().dropSoldierUnit(innerArmy.getEntityId(), innerArmySoldierId);
        }
        getBattleRole().refreshTroop();
        innerArmy.getStatusComponent().setDetailBattle(false);
        setDefaultHero();
    }

    @Override
    protected void onInnerArmyArriveAddChild0(InnerArmyAddEvent event, SceneObjType unitRoleType) {
        // 父方法都是非战斗不处理  但是这里是核心目标的时候要处理的
        if (!isInBattle() && !isCoreTaskTarget) {
            return;
        }
        ArmyEntity innerArmy = event.getArmyEntity();
        for (SoldierProp innerArmySoldierProp : innerArmy.getProp().getTroop().getTroop().values()) {
            getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(innerArmy.getEntityId(), unitRoleType), innerArmySoldierProp);
        }
        getBattleRole().refreshTroop();
        if (isInBattle()) {
            innerArmy.changeStateByAssistCity(CommonEnum.ArmyState.AS_InBattle);
            innerArmy.getStatusComponent().setDetailBattle(true);
        }
        setLeaderHero(event.getArmyEntity());
    }

    @Override
    public void onSingleArmyArriveBattleRole(ArmyEntity armyEntity) {

    }

    @Override
    public void onSingleArmyLeaveBattleRole(ArmyEntity armyEntity) {

    }

    @Override
    protected boolean buildGarrisonHeroOrMecha() {
        List<ArmyEntity> innerArmyList = getOwner().getInnerArmyComponent().getInnerArmyList();
        // 没援军用自己的  有援军用玩家的
        if (innerArmyList.isEmpty()) {
            setDefaultHero();
            return true;
        }
        for (ArmyEntity innerArmy : innerArmyList) {
            // 设置英雄
            setLeaderHero(innerArmy);
            return true;
        }
        return true;
    }

    private void setLeaderHero(ArmyEntity armyEntity) {
        TroopProp troop = armyEntity.getProp().getTroop();
        if (troop.getMainHero().getHeroId() != 0) {
            getTroop().getMainHero().mergeFromSs(troop.getMainHero().getCopySsBuilder().build());
            getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
        } else {
            getTroop().getMainHero().setHeroId(0);
            getBattleRole().setMainHero(null);
        }
        if (troop.getDeputyHero().getHeroId() != 0) {
            getTroop().getDeputyHero().mergeFromSs(troop.getDeputyHero().getCopySsBuilder().build());
            getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
        } else {
            getTroop().getDeputyHero().setHeroId(0);
            getBattleRole().setDeputyHero(null);
        }
    }

    private void setDefaultHero() {
        StructPlayer.Troop.Builder builder = StructPlayer.Troop.newBuilder();
        // 设置英雄
        AbstractScenePlayerArmyMgrComponent armyMgrComponent = getOwner().getScenePlayer().getArmyMgrComponent();
        NpcPlayerArmyMgrComponent component = (NpcPlayerArmyMgrComponent) armyMgrComponent;
        component.choseCityHero(builder);
        getTroop().mergeChangeFromSs(builder.build());
        if (getTroop().getMainHero().getHeroId() != 0) {
            getBattleRole().setMainHero(new BattleHero(getTroop().getMainHero(), getBattleRole(), false));
        } else {
            getBattleRole().setMainHero(null);
        }
        if (getTroop().getDeputyHero().getHeroId() != 0) {
            getBattleRole().setDeputyHero(new BattleHero(getTroop().getDeputyHero(), getBattleRole(), true));
        } else {
            getBattleRole().setDeputyHero(null);
        }
    }

    /**
     * npc城市的生命比例 只看自己的
     */
    @Override
    public int aliveThousandthRatio() {
        CityGarrisonProp garrisonProp = getOwner().getProp().getGarrison();
        long cur = 0;
        long total = 0;
        for (SoldierProp prop : garrisonProp.getSoldierMap().values()) {
            cur += prop.getNum() - prop.getSlightWoundNum() - prop.getSevereWoundNum() - prop.getDeadNum();
            total += prop.getNum();
        }
        long radio = cur * GameLogicConstants.IPPM / total;
        return (int) radio;
    }

    /**
     * 组装守城战斗士兵
     */
    @Override
    protected void buildGarrisonSoldier() {
        AbstractScenePlayerSoldierMgrComponent soldierMgrComponent = getOwner().getScenePlayer().getSoldierMgrComponent();
        Map<Integer, SoldierProp> selfSoldierMap = soldierMgrComponent.buildGarrisonSoldier();
        CityGarrisonProp garrisonProp = getOwner().getProp().getGarrison();
        for (SoldierProp selfSoldier : selfSoldierMap.values()) {
            soldierMgrComponent.subInCitySoldier(selfSoldier.getSoldierId(), selfSoldier.getNum(), null);
            SoldierProp newSp = garrisonProp.addEmptySoldierMap(selfSoldier.getSoldierId());
            Soldier.copyProp(selfSoldier, newSp);
            getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_CITY_ARMY_SELF), newSp);
        }
        CityInnerArmyComponent innerArmyComponent = getOwner().getInnerArmyComponent();
        for (ArmyEntity innerArmy : innerArmyComponent.getInnerArmyList()) {
            for (SoldierProp innerArmySoldierProp : innerArmy.getProp().getTroop().getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(innerArmy.getEntityId(), SceneObjType.SOT_CITY_ARMY_OTHER), innerArmySoldierProp);
            }
        }
        LOGGER.debug("city: {} buildGarrisonSoldier: {}", getEntityId(), getTroop().getTroop());
    }

    @Override
    protected void buildGuardTower() {
        int guardTowerId = getOwner().getTemplate().getGuardTowerId();
        int towerTroopId = GuardTowerHelper.getGuardTowerTroopId(guardTowerId);
        int guardTowerHpMax = GuardTowerHelper.getGuardTowerHpMax(guardTowerId);
        TroopTemplate troopTemplate = ResHolder.getInstance().findValueFromMap(TroopTemplate.class, towerTroopId);
        IntPairType soldierData = troopTemplate.getSoldierPairList().get(0);
        SoldierUnit.OwnerInfo ownerInfo = new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_CITY_ARMY_SELF);
        SoldierUnit towerUnit = new SoldierUnit(ownerInfo, SoldierData.fromProp(new SoldierProp().setSoldierId(soldierData.getKey()).setNum(getOwner().getProp().getGuardTowerHp())));
        getBattleRole().setGuardTower(new GuardTower(getBattleRole(), towerUnit, guardTowerHpMax, GuardTowerHelper.getGuardTowerLv(guardTowerId)));
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
        if (!isCoreTaskTarget) {
            // 加回轻伤和存活的兵
            AbstractScenePlayerEntity scenePlayer = getOwner().getScenePlayer();
            scenePlayer.getSoldierMgrComponent().onCityLeaveBattle(getOwner().getProp().getGarrison().getSoldierMap().values());
            // 释放英雄
            AbstractScenePlayerArmyMgrComponent armyMgrComponent = getOwner().getScenePlayer().getArmyMgrComponent();
            NpcPlayerArmyMgrComponent component = (NpcPlayerArmyMgrComponent) armyMgrComponent;
            component.returnHero(getTroop().getMainHero().getHeroId(), getTroop().getDeputyHero().getHeroId());
        }
        if (!battleResult.alive) {
            getOwner().getScene().getDispatcher().dispatch(new NpcCityDiedEvent(getOwner().getUniqueId()));
        }
    }

    @Override
    protected void clearBattleData() {
        if (isCoreTaskTarget) {
            return;
        }
        super.clearBattleData();
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }
}

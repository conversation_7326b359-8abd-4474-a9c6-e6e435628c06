package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.event.city.CityAscendEvent;
import com.yorha.cnc.scene.event.city.CityMoveEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.CityInnerArmyProp;
import com.yorha.game.gen.prop.InnerArmyInfoProp;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.WarningType;
import com.yorha.proto.Core.Code;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 城内增援管理器
 */
public class CityInnerArmyComponent extends SceneObjInnerArmyComponent {
    private static final Logger LOGGER = LogManager.getLogger(CityInnerArmyComponent.class);
    /**
     * 援助的玩家对应部队id   包括到达未到达的
     */
    private final Map<Long, Long> player2Army = new HashMap<>();

    public CityInnerArmyComponent(CityEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
        // 所有战斗结束 改城内军队状态
        getOwner().getEventDispatcher().addEventListenerRepeat(this::endAllBattle, EndAllBattleEvent.class);
        // 升天/退盟 退回所有驻军
        getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::needReturnAllArmy, CityAscendEvent.class, ClanChangeEvent.class);
        // 迁城 同步更改城内行军位置
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSelfPointChange, CityMoveEvent.class);
    }

    @Override
    public void afterAllLoad() {
        if (getProp().getArmy().isEmpty()) {
            return;
        }
        List<Long> deleteArmy = new ArrayList<>();
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (Long armyId : getProp().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("{} army: {} is not exist", getOwner(), armyId);
                deleteArmy.add(armyId);
                continue;
            }
            arrivedSoldierNum += army.getAliveSoldierNum();
            player2Army.put(army.getPlayerId(), armyId);
        }
        for (Long armyId : deleteArmy) {
            getProp().removeArmyV(armyId);
        }
        LOGGER.info("{} arrivedSoldierNum {}", getOwner(), arrivedSoldierNum);
        // 更新援助计数
        getOwner().getScenePlayer().getRallyComponent().updateAssistArmyNum(getProp().getArmy().size());
    }

    public void onSelfPointChange() {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        // 改写已经到达的
        for (long armyId : getProp().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                army.getTransformComponent().changePoint(getOwner().getCurPoint().getDeepCopy(), true);
            }
        }
        // 未到达的刷一下
        for (long armyId : unArrivedArmy.keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            try {
                if (army != null) {
                    army.getMoveComponent().tryRefreshChasePath();
                }
            } catch (Exception e) {
                LOGGER.error("onSelfPointChange failed {} ", armyId, e);
            }
        }
    }

    private void needReturnAllArmy(IEvent event) {
        LOGGER.info("{} needReturnAllArmy. reason: {}", getOwner(), event);
        returnAllArmy();
    }

    @Override
    public void armyJoin(ArmyEntity army) {
        super.armyJoin(army);
        player2Army.put(army.getPlayerId(), army.getEntityId());
    }

    @Override
    public boolean addWarningItem(SceneObjEntity obj, WarningType type) {
        if (!super.addWarningItem(obj, type)) {
            return false;
        }
        try {
            final AbstractScenePlayerWarningComponent cityOwnerWarningComponent = getOwner().getScenePlayer().getWarningComponent();
            if (cityOwnerWarningComponent == null) {
                LOGGER.info("CityInnerArmyComponent addWarningItem owner no warningComponent");
                return false;
            }
            cityOwnerWarningComponent.addWarningItem(obj, type);
            // 预警  要给城内到的人都发一遍
            for (InnerArmyInfoProp prop : getProp().getArmy().values()) {
                AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(prop.getPlayerId());
                if (scenePlayer == null) {
                    continue;
                }
                final AbstractScenePlayerWarningComponent scenePlayerWarningComponent = scenePlayer.getWarningComponent();
                if (scenePlayerWarningComponent == null) {
                    LOGGER.info("CityInnerArmyComponent addWarningItem no warningComponent");
                    return false;
                }
                scenePlayerWarningComponent.addWarningItem(obj, type);
            }
        } catch (Exception e) {
            LOGGER.error("addWarningItem error ", e);
        }
        return true;
    }

    @Override
    public boolean removeWarningItem(long objId) {
        if (!super.removeWarningItem(objId)) {
            return false;
        }
        try {
            final AbstractScenePlayerWarningComponent cityOwnerWarningComponent = getOwner().getScenePlayer().getWarningComponent();
            if (cityOwnerWarningComponent == null) {
                LOGGER.info("CityInnerArmyComponent removeWarningItem owner no warningComponent");
                return false;
            }
            getOwner().getScenePlayer().getWarningComponent().removeWarningItem(objId);
            // 援助预警  要给城内到的人都发一遍
            for (InnerArmyInfoProp prop : getProp().getArmy().values()) {
                AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(prop.getPlayerId());
                if (scenePlayer == null) {
                    continue;
                }
                final AbstractScenePlayerWarningComponent scenePlayerWarningComponent = scenePlayer.getWarningComponent();
                if (scenePlayerWarningComponent == null) {
                    LOGGER.info("CityInnerArmyComponent removeWarningItem no warningComponent");
                    return false;
                }
                scenePlayerWarningComponent.removeWarningItem(objId);
            }
        } catch (Exception e) {
            LOGGER.error("removeWarningItem error ", e);
        }
        return true;
    }

    @Override
    public void armyArrived(ArmyEntity army) {
        super.armyArrived(army);
        if (getOwner().getScene().isMainScene()) {
            // 到达后要加未到达人的援助预警
            addWarningItemWhenArrive(army.getPlayerId());
            // 加入历史记录
            SsPlayerMisc.AddAssistHistoryCmd addAssistHistoryCmd = SsPlayerMisc.AddAssistHistoryCmd.newBuilder()
                    .setProp(Struct.AssistRecord.newBuilder()
                            .setCardHead(Struct.PlayerCardHead.newBuilder().setName(army.getPlayerName()).setPic(army.getPic()).build())
                            .setAssistTsMs(SystemClock.now())
                            .setSoldierNum(army.getAliveSoldierNum())
                            .setPlayerId(army.getPlayerId())
                            .build()).build();
            getOwner().getScenePlayer().tellPlayer(addAssistHistoryCmd);
            // 更新援助计数
            getOwner().getScenePlayer().getRallyComponent().updateAssistArmyNum(getProp().getArmy().size());
        }
    }

    @Override
    protected void beforeRemoveArmy(ArmyEntity army, boolean isIn, long operatorId) {
        player2Army.remove(army.getPlayerId());
        if (isIn) {
            // 移除这个人对未到达人的援助预警
            removeWarningItemWhenLeave(army.getPlayerId());
        }
    }

    @Override
    protected void afterRemoveArmy(ArmyEntity army, boolean isIn, long operatorId) {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        if (isIn) {
            // 更新援助计数
            getOwner().getScenePlayer().getRallyComponent().updateAssistArmyNum(getProp().getArmy().size());
        }
    }

    @Override
    public void checkCanRepatriateArmy(long playerId, ArmyEntity army, boolean isPermission) {
        // 非城主 也非自己的部队
        if (getOwner().getPlayerId() != playerId && playerId != army.getPlayerId()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
    }

    @Override
    public void returnAllArmy() {
        player2Army.clear();
        super.returnAllArmy();
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        getOwner().getScenePlayer().getRallyComponent().updateAssistArmyNum(0);
    }

    @Override
    public Code checkArmyCanAssist(long playerId, Camp camp, long soldierNum, long armyId) {
        if (!getOwner().isPlayerCity() && getOwner().getCampEnum() != camp) {
            return ErrorCode.ASSIST_CANT.getCode();
        }
        // 城池每个玩家只能派出一支军队去援助
        if (player2Army.containsKey(playerId)) {
            return ErrorCode.ASSIST_ALREADY_HAVE_ONE.getCode();
        }
        return super.checkArmyCanAssist(playerId, camp, soldierNum, armyId);
    }

    @Override
    public StructPlayerPB.CityAssistInfoPB getAssistInfoPb(long playerId) {
        // 自己是城主或者Npc城 全量拉取
        if (playerId == getOwner().getPlayerId() || !getOwner().isPlayerCity()) {
            return super.getAssistInfoPb(playerId);
        }
        StructPlayerPB.CityAssistInfoPB.Builder builder = StructPlayerPB.CityAssistInfoPB.newBuilder();
        builder.setCurSoldierNum(getCurAssistSoldierNum()).setMaxSoldierNum(getBeAidedMaxNum());
        Long armyId = player2Army.getOrDefault(playerId, 0L);
        // 没有这个人的部队 直接返回
        if (armyId == 0) {
            return builder.build();
        }
        long ts = 0;
        if (getProp().getArmy().containsKey(armyId)) {
            ts = getProp().getArmyV(armyId).getEnterTsMs();
        }
        ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
        if (army != null && army.getPlayerId() == playerId) {
            addArmyPb(builder.getArmyBuilder(), army, ts);
        }
        return builder.build();
    }

    public boolean hasAnyAssistArmy() {
        return player2Army.size() > 0;
    }

    @Override
    public int getBeAidedMaxNum() {
        if (getOwner().isPlayerCity()) {
            return getOwner().getScenePlayer().getRallyComponent().getBeAidedMaxCap();
        }
        return getOwner().getTemplate().getAssistNumMax();
    }

    @Override
    public CityInnerArmyProp getProp() {
        return getOwner().getProp().getInnerArmy();
    }

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }
}

package com.yorha.cnc.scene.sceneObj.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.ActorChangeAttrUpsertStrategy;
import com.yorha.common.dbactor.DbTaskProxy;
import com.yorha.common.dbactor.DefaultDbOperationStrategyImpl;
import com.yorha.common.dbactor.IDbChangeDeleteStrategy;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.TcaplusDb.SceneObjTable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 */
public class SceneObjDbComponent extends SceneObjComponent<SceneObjEntity> {
    public static final Logger LOGGER = LogManager.getLogger(SceneObjDbComponent.class);
    private DbTaskProxy proxy;

    public SceneObjDbComponent(SceneObjEntity owner) {
        super(owner);
    }

    /**
     * 手动开启增量落库。
     *
     * @param isRestore 是否是从db恢复。
     */
    public void beginSaveChangeToDb(boolean isRestore) {
        if (this.proxy != null) {
            throw new GeminiException("already save change to db");
        }
        if (this.getOwner().getScene().isDungeon()) {
            LOGGER.error("SceneObjDbComponent beginSaveChangeToDb owner={}, in dungeon", getOwner());
            return;
        }
        final SceneObjActorChange change = new SceneObjActorChange(this.getOwner(), EntityAttrDb.EntityAttrDB.getDefaultInstance(), isRestore);
        this.proxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString())
                .owner(this.ownerActor())
                .limitTimerOwner(getOwner().getTimerComponent())
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .delete(change)
                .upsert(change)
                .intervalMs(DbLimitConstants.SCENE_OBJ_DB_LIMIT_INTERVAL_MS)
                .entityId(String.valueOf(this.getEntityId()))
                .build();
    }

    /**
     * 尝试存盘  目前只用于mapBuilding和resBuilding 请勿乱用
     */
    public void tryInsertDb() {
        if (proxy != null) {
            return;
        }
        if (this.getOwner().getScene().isDungeon()) {
            LOGGER.error("SceneObjDbComponent tryInsertDb owner={}, in dungeon", getOwner());
            return;
        }
        insertIntoDb();
    }

    /**
     * 插入数据库。
     */
    public void insertIntoDb() {
        if (this.getOwner().getScene().isDungeon()) {
            LOGGER.error("SceneObjDbComponent insertIntoDb owner={}, in dungeon", getOwner());
            return;
        }
        this.beginSaveChangeToDb(false);
        this.proxy.insert();
    }

    /**
     * 增量落库。
     */
    public void saveChangeToDb() {
        // 未开启，跳过。
        if (this.proxy == null) {
            return;
        }
        if (this.getOwner().getScene().isDungeon()) {
            LOGGER.error("SceneObjDbComponent saveChangeToDb owner={}, in dungeon", getOwner());
            return;
        }
        this.proxy.update();
    }

    public void deleteDb() {
        if (this.proxy == null) {
            // 未存储过的entity 也无需delete
            return;
        }
        if (this.getOwner().getScene().isDungeon()) {
            LOGGER.error("SceneObjDbComponent deleteDb owner={}, in dungeon", getOwner());
            return;
        }
        this.proxy.delete();
    }

    /**
     * 落脏（销毁前调用）
     */
    public boolean saveOnDestroy() {
        // 未开启，跳过。
        if (this.proxy == null) {
            return false;
        }
        if (this.getOwner().getScene().isDungeon()) {
            LOGGER.error("SceneObjDbComponent endDb owner={}, in dungeon", getOwner());
            return true;
        }
        if (!this.ownerActor().isDestroy()) {
            WechatLog.error("SceneObjDbComponent endDb scene actor is not destroy! SceneType {} SceneObj {}!", this.getEntityType(), this.getEntityId());
        }
        this.proxy.saveDbAsync();
        this.proxy.stop();
        return true;
    }

    public void stopByMigrate() {
        if (proxy == null) {
            return;
        }
        this.proxy.stop();
        this.proxy = null;
    }

    private static class SceneObjActorChange extends ActorChangeAttrUpsertStrategy<EntityAttrDb.EntityAttrDB> implements IDbChangeDeleteStrategy {
        private final SceneObjEntity entity;
        /**
         * 是否首次就触发fullAttrSave
         */
        private boolean firstFullAttrSave;

        public SceneObjActorChange(final SceneObjEntity entity, GeneratedMessageV3 msg, final boolean isFromRestore) {
            super(msg);
            this.entity = entity;
            firstFullAttrSave = isFromRestore;
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            if (this.firstFullAttrSave) {
                firstFullAttrSave = false;
                return -1;
            }
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            return this.entity.changedDbEntityAttr((EntityAttrDb.EntityAttrDB.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected EntityAttrDb.EntityAttrDB buildFullAttrSaveData(AbstractActor actor) {
            final EntityAttrDb.EntityAttrDB.Builder builder = EntityAttrDb.EntityAttrDB.newBuilder()
                    .setEntityId(this.entity.getEntityId())
                    .setEntityType(this.entity.getEntityType());
            this.entity.fullDbEntityAttr(builder);
            return builder.build();
        }

        @Override
        protected EntityAttrDb.EntityAttrDB buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            return this.entity.fullDbEntityAttr((SceneObjTable.Builder) result.value)
                    .setEntityId(this.entity.getEntityId())
                    .setEntityType(this.entity.getEntityType())
                    .build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, EntityAttrDb.EntityAttrDB fullAttr, @NotNull EntityAttrDb.EntityAttrDB changeAttr) {
            final SceneObjTable.Builder request = SceneObjTable.newBuilder();
            request.setZoneId(this.entity.getScene().getZoneId());
            request.setEntityId(this.entity.getEntityId());
            request.setSceneId(this.entity.getScene().getEntityId());
            if (fullAttr != null) {
                request.setFullAttr(fullAttr);
            }
            request.setChangedAttr(changeAttr);
            return request;
        }

        @Override
        public Message.Builder buildDeleteRequest(AbstractActor actor) {
            SceneObjTable.Builder request = SceneObjTable.newBuilder();
            request.setZoneId(this.entity.getScene().getZoneId());
            request.setEntityId(this.entity.getEntityId());
            request.setSceneId(this.entity.getScene().getEntityId());
            return request;
        }

    }

    @Override
    public String toString() {
        return "SceneObjDbComponent{" +
                "entityType=" + this.getEntityType() +
                ", entityId=" + this.getEntityId() +
                '}';
    }
}

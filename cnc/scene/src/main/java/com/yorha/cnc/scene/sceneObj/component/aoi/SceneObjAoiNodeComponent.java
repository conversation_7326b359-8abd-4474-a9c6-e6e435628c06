package com.yorha.cnc.scene.sceneObj.component.aoi;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.aoiView.AoiViewHelper;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.aoiView.manager.AoiObserver;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 有aoi格子的做法
 *
 * <AUTHOR>
 */
public class SceneObjAoiNodeComponent extends SceneObjAoiComponent {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjAoiNodeComponent.class);

    private Set<AoiGrid> curAoiGridSet = new HashSet<>();
    /**
     * 需要一直同步的人
     */
    private Set<Long> alwaysSyncPlayerIds;

    public SceneObjAoiNodeComponent(SceneObjEntity owner) {
        super(owner);
    }

    /**
     * 加入视野 加入阻挡 阻挡是跟视野同步的
     */
    @Override
    public void addIntoAoi(SceneObjectNtfReason reason) {
        if (isAddIntoAoi) {
            WechatLog.error("{} is already in aoi", getOwner());
            return;
        }
        if (getOwner().isDestroy()) {
            return;
        }
        isAddIntoAoi = true;
        SceneEntity scene = getOwner().getScene();
        if (!scene.isMainScene() || scene.isInitOk()) {
            LOGGER.debug("{} add into aoi reason: {}", getOwner(), reason);
        }
        refreshAoi(reason);
        // 加入阻挡
        getOwner().getTransformComponent().addCollision();
    }

    /**
     * 离开视野 移除阻挡
     */
    @Override
    public void removeFromAoi(SceneObjectNtfReason reason) {
        if (!isAddIntoAoi) {
            WechatLog.error("{} is not in aoi", getOwner());
            return;
        }
        isAddIntoAoi = false;
        LOGGER.info("{} remove from aoi reason: {}", getOwner(), reason);
        refreshAoi(reason);
        // 移除阻挡
        getOwner().getTransformComponent().removeCollision();
    }

    /**
     * 刷新aoi，对新增和离开aoi的对象发送视野信息
     */
    @Override
    public void refreshAoi(SceneObjectNtfReason reason) {
        Set<AoiGrid> oldAoiGrids = curAoiGridSet;
        Set<AoiGrid> newAoiGrids = getAffectAoiGrids();
        if (!AoiViewHelper.isAoiChanged(oldAoiGrids, newAoiGrids)) {
            return;
        }
        // 统计新老视野的玩家
        Set<AoiObserver> oldAreaNormalPlayerSet = new ObjectOpenHashSet<>();
        Set<AoiObserver> newAreaNormalPlayerSet = new ObjectOpenHashSet<>();
        int layer = getOwner().getScene().getMaxSceneLayer(getOwner());
        for (AoiGrid oldAoiGrid : oldAoiGrids) {
            if (newAoiGrids.contains(oldAoiGrid)) {
                continue;
            }
            // 旧的格子需要删除自己
            oldAoiGrid.removeSceneObj(getEntityId(), layer);
            // 位置变化不用即时更新
            if (reason == null) {
                continue;
            }
            for (AoiObserver observer : oldAoiGrid.getObserverIds()) {
                // 是常关注我的人
                if (alwaysSyncPlayerIds != null && alwaysSyncPlayerIds.contains(observer.getId())) {
                    continue;
                }
                // 不在该人的当前可见列表里
                if (!observer.getCurViewObj().contains(getEntityId())) {
                    continue;
                }
                observer.removeViewObj(getEntityId());
                oldAreaNormalPlayerSet.add(observer);
            }
        }
        for (AoiGrid newAoiGrid : newAoiGrids) {
            // 新的格子需要加入自己
            if (oldAoiGrids.contains(newAoiGrid)) {
                continue;
            }
            newAoiGrid.addSceneObj(getEntityId(), layer);
            // 位置变化不用即时更新
            if (reason == null) {
                continue;
            }
            for (AoiObserver observer : newAoiGrid.getObserverIds()) {
                // 是常关注我的人
                if (alwaysSyncPlayerIds != null && alwaysSyncPlayerIds.contains(observer.getId())) {
                    continue;
                }
                // 已经在该人的可见列表里
                if (observer.getCurViewObj().contains(getEntityId())) {
                    continue;
                }
                // 不能进入该人的视野范围
                if (observer.getNewAABB() == null || !ShapeUtils.isContact(observer.getNewAABB(), getOwner().getTransformComponent().getSelfShape())) {
                    continue;
                }
                // 移动的才需要判断
                if (getOwner().getMoveComponent() != null && observer.getCurViewObj().size() >= observer.getEntityNumMax()) {
                    continue;
                }
                observer.addViewObj(getEntityId());
                newAreaNormalPlayerSet.add(observer);
            }
        }
        curAoiGridSet = newAoiGrids;
        if (reason == null) {
            return;
        }
        if (!oldAreaNormalPlayerSet.isEmpty()) {
            Entity.EntityNtfMsg.Builder delEntityMsgBuilder = Entity.EntityNtfMsg.newBuilder();
            delEntityMsgBuilder.setReason(reason).setZoneId(getOwner().getScene().getZoneId()).addDelEntities(getEntityId());
            broadcast(oldAreaNormalPlayerSet, MsgType.ENTITYNTFMSG, delEntityMsgBuilder.build());
        }
        if (!newAreaNormalPlayerSet.isEmpty()) {
            broadcast(newAreaNormalPlayerSet, MsgType.ENTITYNTFMSG, getOwner().getPropComponent().getEntityNtfMsg(reason));
        }
        // 简要数据变更
        if (isAddIntoAoi) {
            onBriefAdd();
        } else {
            // 给简要层的关注玩家发移除消息
            onBriefChange(layer, 0);
        }
    }

    /**
     * 获取自己所在的视野格子
     */
    private Set<AoiGrid> getAffectAoiGrids() {
        if (!isAddIntoAoi) {
            return Collections.emptySet();
        }
        Shape selfShape = getOwner().getTransformComponent().getSelfShape();
        return getOwner().getScene().getAoiMgrComponent().getAffectAoiGrids(selfShape);
    }

    private void onBriefAdd() {
        int layer = getOwner().getScene().getMaxSceneLayer(getOwner());
        if (BigSceneConstants.isNormalLayer(layer)) {
            return;
        }
        Entity.SceneObjBriefAttr attr = getOwner().getPropComponent().genBriefMsg();
        if (attr == null) {
            LOGGER.error("{} not have brief attr", getOwner());
            return;
        }
        AoiViewHelper.sendBriefSceneObjAdd(
                ownerActor(), getOwner().getScene().getZoneId(),
                getEntityId(), attr,
                getOwner().getTransformComponent().getSelfShape().getAABB(),
                layer);
    }

    @Override
    public void onBriefChange(int oldLayer, int newLayer) {
        int layer = getOwner().getScene().getMaxSceneLayer(getOwner());
        if (BigSceneConstants.isNormalLayer(layer)) {
            return;
        }
        if (newLayer == 0) {
            AoiViewHelper.sendBriefSceneObjUpdate(
                    ownerActor(), getOwner().getScene().getZoneId(),
                    getEntityId(), null,
                    oldLayer,
                    newLayer);
            return;
        }
        Entity.SceneObjBriefAttr attr = getOwner().getPropComponent().genBriefMsg();
        if (attr == null) {
            LOGGER.error("{} not have brief attr", getOwner());
            return;
        }
        AoiViewHelper.sendBriefSceneObjUpdate(
                ownerActor(), getOwner().getScene().getZoneId(),
                getEntityId(), attr,
                oldLayer,
                newLayer);
    }

    /**
     * 获取指定层级的及以下的观察者
     */
    public Set<AoiObserver> getObPlayerIdList(int layer) {
        Set<AoiObserver> obPlayerIdList = new ObjectOpenHashSet<>();
        for (AoiGrid aoiGrid : curAoiGridSet) {
            obPlayerIdList.addAll(aoiGrid.getObserverIds(layer));
        }
        return obPlayerIdList;
    }

    /**
     * 获取视野内的玩家id列表
     */
    private Set<AoiObserver> getObPlayerIdList() {
        Set<AoiObserver> obPlayerIdList = new ObjectOpenHashSet<>();
        for (AoiGrid aoiGrid : curAoiGridSet) {
            obPlayerIdList.addAll(aoiGrid.getObserverIds());
        }
        obPlayerIdList.addAll(getAlwaysSyncObserverSet());
        return obPlayerIdList;
    }

    /**
     * 视野层级突变的   下发给差异观察者
     */
    @Override
    public void onChangeLayer(int oldLayer, int newLayer) {
        if (oldLayer == newLayer) {
            return;
        }
        onBriefChange(oldLayer, newLayer);
        LOGGER.info("{} onChangeLayer. oldLayer: {}  newLayer: {}", getOwner(), oldLayer, newLayer);
    }

    private void broadcastAlwaysSyncPlayer(GeneratedMessageV3 message) {
        broadcast(getAlwaysSyncObserverSet(), MsgType.ENTITYNTFMSG, message);
    }

    /**
     * 广播给指定层级及以下的  一般是表现类 技能效果、伤害什么的
     */
    @Override
    public void broadcast(int msgType, GeneratedMessageV3 message, int maxLayer) {
        broadcast(getObPlayerIdList(maxLayer), msgType, message);
    }

    /**
     * 对外 广播
     */
    @Override
    public void broadcast(int msgType, GeneratedMessageV3 message) {
        broadcast(getObPlayerIdList(), msgType, message);
    }

    private void broadcast(Set<AoiObserver> observerSet, int msgType, GeneratedMessageV3 message) {
        if (observerSet.isEmpty()) {
            return;
        }
        getOwner().getScene().getAoiMgrComponent().broadcastAoiObserverList(observerSet, msgType, message);
    }

    /**
     * 设置一直同步的人  仅限初始化时使用 !!!
     */
    @Override
    public void setAlwaysSyncPlayer(Set<Long> playerIds) {
        if (alwaysSyncPlayerIds == null) {
            alwaysSyncPlayerIds = new HashSet<>();
        }
        alwaysSyncPlayerIds.addAll(playerIds);
        Set<AoiObserver> needNtfSet = new ObjectOpenHashSet<>();
        // 添加到玩家的关注列表
        for (long playerId : playerIds) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            boolean needNtf = scenePlayer.getAoiObserverComponent().ob(getOwner());
            if (needNtf) {
                AoiObserver observer = scenePlayer.getAoiObserverComponent();
                needNtfSet.add(observer);
            }
        }
        if (!needNtfSet.isEmpty() && getOwner().getScene().isInitOk()) {
            broadcast(needNtfSet, MsgType.ENTITYNTFMSG, getOwner().getPropComponent().getEntityNtfMsg(SceneObjectNtfReason.SONR_BORN));
        }
    }

    @Override
    public void setAlwaysSyncPlayer(long playerId) {
        if (alwaysSyncPlayerIds == null) {
            alwaysSyncPlayerIds = new HashSet<>();
        }
        alwaysSyncPlayerIds.add(playerId);
        // 添加到玩家的关注列表
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        boolean needNtf = scenePlayer.getAoiObserverComponent().ob(getOwner());
        if (needNtf && getOwner().getScene().isInitOk()) {
            scenePlayer.sendMsgToClient(MsgType.ENTITYNTFMSG, getOwner().getPropComponent().getEntityNtfMsg(SceneObjectNtfReason.SONR_BORN));
        }
    }

    /**
     * 运行中add （目前集结用）
     */
    @Override
    public void addAlwaysSyncPlayer(long playerId) {
        if (alwaysSyncPlayerIds == null) {
            alwaysSyncPlayerIds = new HashSet<>();
        }
        if (alwaysSyncPlayerIds.contains(playerId)) {
            return;
        }
        alwaysSyncPlayerIds.add(playerId);
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        // 添加到玩家的关注列表
        boolean needNtf = scenePlayer.getAoiObserverComponent().ob(getOwner());
        // 不在视野内，需要先发个new的
        if (needNtf) {
            scenePlayer.sendMsgToClient(MsgType.ENTITYNTFMSG, getOwner().getPropComponent().getEntityNtfMsg(SceneObjectNtfReason.SONR_AOI));
        }
    }

    /**
     * 运行中remove （目前集结用）
     */
    @Override
    public void removeAlwaysSyncPlayer(long playerId) {
        if (!alwaysSyncPlayerIds.contains(playerId)) {
            return;
        }
        alwaysSyncPlayerIds.remove(playerId);
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        // 删除玩家的关注列表
        scenePlayer.getAoiObserverComponent().unOb(getEntityId(), false);
    }

    /**
     * delete时调用
     */
    @Override
    public void clearAlwaysSyncPlayer() {
        if (alwaysSyncPlayerIds == null) {
            return;
        }
        alwaysSyncPlayerIds.forEach(playerId -> {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            // 删除玩家的关注列表
            scenePlayer.getAoiObserverComponent().unOb(getEntityId(), true);
        });
        Entity.EntityNtfMsg.Builder delEntityMsgBuilder = Entity.EntityNtfMsg.newBuilder().setReason(SceneObjectNtfReason.SONR_RETURN_CITY);
        delEntityMsgBuilder.setZoneId(getOwner().getScene().getZoneId()).addDelEntities(getEntityId());
        broadcastAlwaysSyncPlayer(delEntityMsgBuilder.build());
        alwaysSyncPlayerIds.clear();
    }

    private final Set<AoiObserver> curSendModObserverSet = new ObjectOpenHashSet<>();

    @Override
    public void broadcastEntityModNtf(EntityAttrOuterClass.EntityAttr.Builder attrBuilder, boolean needMerge) {
        curSendModObserverSet.clear();
        // aoi grid里面的玩家
        for (AoiGrid aoiGrid : curAoiGridSet) {
            Set<AoiObserver> observerSet = aoiGrid.getObserverIds();
            if (aoiGrid.getObserverIds().isEmpty()) {
                continue;
            }
            for (AoiObserver observer : observerSet) {
                if (curSendModObserverSet.contains(observer) || !observer.getCurViewObj().contains(getEntityId())) {
                    continue;
                }
                curSendModObserverSet.add(observer);
                if (needMerge) {
                    getOwner().getScene().getAoiMgrComponent().addModEntityAttr(observer, getEntityId(), attrBuilder);
                }
            }
        }
        Set<AoiObserver> alwaysSyncObserverSet = getAlwaysSyncObserverSet();
        // 额外的玩家直接通知
        for (AoiObserver observer : alwaysSyncObserverSet) {
            if (curSendModObserverSet.contains(observer)) {
                continue;
            }
            curSendModObserverSet.add(observer);
            if (needMerge) {
                getOwner().getScene().getAoiMgrComponent().addModEntityAttr(observer, getEntityId(), attrBuilder);
            }
        }
        if (needMerge) {
            return;
        }
        final Entity.EntityNtfMsg.Builder builder = Entity.EntityNtfMsg.newBuilder();
        builder.setZoneId(getOwner().getScene().getZoneId()).addModEntities(attrBuilder);
        broadcast(curSendModObserverSet, MsgType.ENTITYNTFMSG, builder.build());
    }

    /**
     * 获取一直同步的玩家id列表
     */
    private Set<AoiObserver> getAlwaysSyncObserverSet() {
        if (alwaysSyncPlayerIds == null) {
            return Collections.emptySet();
        }
        Set<AoiObserver> obPlayerList = new ObjectOpenHashSet<>();
        for (long playerId : alwaysSyncPlayerIds) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            if (scenePlayer.isThisSceneOnline()) {
                AoiObserver observer = scenePlayer.getAoiObserverComponent();
                obPlayerList.add(observer);
            }
        }
        return obPlayerList;
    }
}

package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.proto.StructMail;

/**
 * <AUTHOR>
 */
public class SceneObjSpyComponent extends SceneObjComponent<SceneObjEntity> {
    public SceneObjSpyComponent(SceneObjEntity owner) {
        super(owner);
    }

    public boolean canSpy() {
        return true;
    }

    public void formSpySuccessMail(StructMail.MailSendParams.Builder mailSendParams, AbstractScenePlayerEntity player) {

    }
}

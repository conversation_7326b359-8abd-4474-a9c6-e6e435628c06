package com.yorha.cnc.scene.sceneclan.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.common.ScenePushNotificationHelper;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.marquee.MarqueeResService;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.CommonEnum.MarqueeType;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPB.DisplayDataPB;
import com.yorha.proto.StructPlayerPB.RallyInfoListPB;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import com.yorha.proto.User;
import it.unimi.dsi.fastutil.longs.Long2LongOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 联盟集结管理
 */
public class SceneClanRallyComponent extends AbstractComponent<SceneClanEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanRallyComponent.class);
    /**
     * 目标id: 进攻集结id
     */
    private final Map<Long, Long> targetId2RallyId = new Long2LongOpenHashMap();

    /**
     * 本联盟发起的进攻集结列表
     */
    private final Map<Long, RallyEntity> rallyEntityMap = new Long2ObjectOpenHashMap<>();

    /**
     * 本联盟成员城池、拥有建筑、集结军队 受到攻击时的集结id列表(用来做战争面板显示援助信息用)
     * rallyId -> rally
     */
    private final Map<Long, RallyEntity> beRallyMap = new Long2ObjectOpenHashMap<>();

    public SceneClanRallyComponent(SceneClanEntity owner) {
        super(owner);
    }

    /**
     * 创建集结
     */
    public RallyEntity createRally(AbstractScenePlayerEntity scenePlayer, ArmyEntity armyEntity, long targetId, int waitTime, int costEnergy) {
        LOGGER.info("{} try create Rally for {}, target:{} waitTime:{}", scenePlayer, armyEntity, targetId, waitTime);
        // 获取组装好组织者属性的集结prop
        RallyInfoProp rallyInfoProp = scenePlayer.getRallyComponent().buildOrganizerRallyProp();
        SceneObjEntity targetEntity = getOwner().getSceneEntity().getObjMgrComponent().getSceneObjEntity(targetId);
        // 进攻方跑马灯
        StructPB.DisplayDataPB.Builder attackParamBuilder = StructPB.DisplayDataPB.newBuilder();
        attackParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(rallyInfoProp.getOrganizerCardHead().getName()));
        // 被攻击方跑马灯
        StructPB.DisplayDataPB.Builder defenderParamBuilder = StructPB.DisplayDataPB.newBuilder();
        defenderParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(rallyInfoProp.getOrganizerClanShortName()));
        // 建立目标数据 并发送跑马灯
        MarqueeResService marqueeService = ResHolder.getResService(MarqueeResService.class);
        switch (targetEntity.getEntityType()) {
            // 目前只支持到army和city mapBuilding
            case ET_Army:
                ArmyEntity army = (ArmyEntity) targetEntity;
                army.copyToRallyInfo(rallyInfoProp);
                int armyAttackMarqueeId = marqueeService.getMarqueeId(MarqueeType.GATHER_ENEMY_ARMY);
                int armyDefenseMarqueeId = marqueeService.getMarqueeId(MarqueeType.ENEMY_GATHER_OUR_ARMY);
                sendArmyCityMarquee(armyAttackMarqueeId, armyDefenseMarqueeId, attackParamBuilder, defenderParamBuilder, rallyInfoProp, army);
                break;
            case ET_City:
                CityEntity city = (CityEntity) targetEntity;
                city.copyToRallyInfo(rallyInfoProp);
                int cityAttackMarqueeId = marqueeService.getMarqueeId(MarqueeType.GATHER_ENEMY_BASE);
                int cityDefenseMarqueeId = marqueeService.getMarqueeId(MarqueeType.ENEMY_GATHER_OUR_BASE);
                sendArmyCityMarquee(cityAttackMarqueeId, cityDefenseMarqueeId, attackParamBuilder, defenderParamBuilder, rallyInfoProp, city);
                // 推送：联盟集结玩家主城
                ScenePushNotificationHelper.pushClanRallyPlayerNotification(this.getOwner().getSceneEntity(), scenePlayer);
                break;
            case ET_Monster:
                MonsterEntity monster = (MonsterEntity) targetEntity;
                monster.copyToRallyInfo(rallyInfoProp);
                int monsterMarqueeId = marqueeService.getMarqueeId(MarqueeType.GATHER_MONSTER);
                if (monster.getTemplate().getCategory() == CommonEnum.MonsterCategory.ACT_LUOHA) {
                    // 洛哈，两个跑马灯
                    if (monster.getTemplate().getQuality() == CommonEnum.SceneObjQuality.NORMAL) {
                        monsterMarqueeId = marqueeService.getMarqueeId(MarqueeType.LUOHA_GATHER_SMALL);
                    }
                    if (monster.getTemplate().getQuality() == CommonEnum.SceneObjQuality.ELITE) {
                        monsterMarqueeId = marqueeService.getMarqueeId(MarqueeType.LUOHA_GATHER_BIG);
                    }
                } else {
                    // 其他只需要填写野怪等级
                    attackParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_INT64, monster.getTemplate().getLevel()));
                }
                sendClanOrPlayerMarquee(monsterMarqueeId, attackParamBuilder.build(), getEntityId(), 0);
                // 推送：联盟集结野怪
                ScenePushNotificationHelper.pushClanRallyMonsterNotification(this.getOwner().getSceneEntity(), scenePlayer);
                break;
            case ET_MapBuilding:
                int attackerMarqueeId = 0;
                int defenderMarqueeIdId = 0;
                final CommonEnum.MapType mapType = this.getOwner().getSceneEntity().getMapType();
                final long mapId = this.getOwner().getSceneEntity().getMapIdForPoint();
                MapBuildingEntity mapBuilding = (MapBuildingEntity) targetEntity;
                mapBuilding.copyToRallyInfo(rallyInfoProp);
                if (rallyInfoProp.getTargetClanId() != 0) {
                    // 联盟简称
                    attackParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(mapBuilding.getClanSimpleName()));
                    defenderParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(mapBuilding.getClanSimpleName()));
                    if (mapBuilding.isInOccupying()) {
                        attackerMarqueeId = marqueeService.getMarqueeId(MarqueeType.GATHER_ENEMY_STRONGHOLD_2);
                        defenderMarqueeIdId = marqueeService.getMarqueeId(MarqueeType.ENEMY_GATHER_OUR_STRONGHOLD_2);
                    } else {
                        attackerMarqueeId = marqueeService.getMarqueeId(MarqueeType.GATHER_ENEMY_STRONGHOLD_1);
                        defenderMarqueeIdId = marqueeService.getMarqueeId(MarqueeType.ENEMY_GATHER_OUR_STRONGHOLD_1);
                    }
                    defenderParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayPointPb(rallyInfoProp.getTargetPos().getX(), rallyInfoProp.getTargetPos().getY(), mapType, mapId));
                    defenderParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, mapBuilding.getProp().getTemplateId()));
                    sendClanOrPlayerMarquee(defenderMarqueeIdId, defenderParamBuilder.build(), rallyInfoProp.getTargetClanId(), 0);
                } else {
                    attackerMarqueeId = marqueeService.getMarqueeId(CommonEnum.MarqueeType.GATHER_STRONGHOLD);
                }
                attackParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayPointPb(rallyInfoProp.getTargetPos().getX(), rallyInfoProp.getTargetPos().getY(), mapType, mapId));
                attackParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayIdPb(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, mapBuilding.getProp().getTemplateId()));
                sendClanOrPlayerMarquee(attackerMarqueeId, attackParamBuilder.build(), getEntityId(), 0);
                // 推送：联盟建筑被集结
                ScenePushNotificationHelper.pushClanMapBuildingBeRallied(this.getOwner().getSceneEntity(), mapBuilding);
                break;
            default:
                throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        rallyInfoProp.setMaxSoldierNum(SceneAddCalc.getRallyCap(armyEntity.getBattleComponent().getBattleRole(), targetEntity));
        RallyEntity rallyEntity = new RallyEntity(scenePlayer.getScene(), getOwner(), rallyInfoProp, armyEntity, targetEntity, costEnergy);
        // 集结准备时间减少
        long addition = getOwner().getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_RALLY_PREPARE_TIME_FIXED_VALUE);
        if (waitTime <= addition) {
            LOGGER.error("{} rally wait time addition too much: {}", getOwner(), addition);
            addition = 0;
        }
        rallyEntity.start((int) (waitTime - addition));
        return rallyEntity;
    }

    private void sendArmyCityMarquee(int attackMarqueeId, int defenderMarqueeId, DisplayDataPB.Builder attackParamBuilder, DisplayDataPB.Builder defenderParamBuilder, RallyInfoProp rallyInfoProp, SceneObjEntity target) {
        attackParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(rallyInfoProp.getTargetCardHead().getName()));
        defenderParamBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayTextPb(rallyInfoProp.getTargetCardHead().getName()));
        sendClanOrPlayerMarquee(attackMarqueeId, attackParamBuilder.build(), getEntityId(), 0);
        sendClanOrPlayerMarquee(defenderMarqueeId, defenderParamBuilder.build(), rallyInfoProp.getTargetClanId(), target.getPlayerId());
    }

    private void sendClanOrPlayerMarquee(int marqueeId, StructPB.DisplayDataPB params, long clanId, long playerId) {
        getOwner().getSceneEntity().getMarqueeComponent().sendClanOrPlayerMarquee(marqueeId, params, clanId, playerId);
    }

    /**
     * 新的集结
     */
    public void onNewRally(RallyEntity rallyEntity) {
        long rallyId = rallyEntity.getEntityId();
        long targetId = rallyEntity.getProp().getTargetId();
        LOGGER.debug("{} try register rallyId:{} targetId:{}", getOwner(), rallyId, targetId);
        if (targetId2RallyId.containsKey(targetId)) {
            LOGGER.error("{} targetId:{} already register with rallyId:{}", getOwner(), targetId, targetId2RallyId.get(targetId));
            return;
        }
        targetId2RallyId.put(targetId, rallyId);
        rallyEntityMap.put(rallyEntity.getEntityId(), rallyEntity);
        LOGGER.info("{} put {} {} into rallyId2TargetId success, cur num:{}", getOwner(), targetId, rallyId, targetId2RallyId.size());
        checkRallyNtf(true);
        syncRallyToClan(rallyEntity.getEntityId(), true);
    }

    /**
     * 删除集结
     */
    public void onRemoveRally(RallyEntity rallyEntity) {
        long rallyId = rallyEntity.getEntityId();
        long targetId = rallyEntity.getProp().getTargetId();
        LOGGER.debug("{} try unregister rallyId:{} targetId:{}", getOwner(), rallyId, targetId);
        if (!targetId2RallyId.containsKey(targetId)) {
            LOGGER.error("has no key:{}", rallyId);
            return;
        }
        Long oldRallyId = targetId2RallyId.remove(targetId);
        if (oldRallyId != rallyId) {
            LOGGER.error("rallyId:{} unregister targetId:{} with wrong old rallyId:{}", rallyId, targetId, oldRallyId);
        }
        LOGGER.info("{} remove {} {} from rallyId2TargetId success, cur num:{}", getOwner(), targetId, rallyId, targetId2RallyId.size());
        rallyEntityMap.remove(rallyEntity.getEntityId());
        checkRallyNtf(false);
        syncRallyToClan(rallyEntity.getEntityId(), false);
    }

    /**
     * 新的被集结信息
     */
    public void onNewBeRally(RallyEntity rallyEntity) {
        beRallyMap.put(rallyEntity.getEntityId(), rallyEntity);
        LOGGER.info("{} onNewBeRally {}, cur num:{}", getOwner(), rallyEntity, beRallyMap.size());
        checkRallyNtf(true);
        syncRallyToClan(rallyEntity.getEntityId(), true);
    }

    /**
     * 移除被集结信息
     */
    public void onRemoveBeRally(RallyEntity rallyEntity) {
        if (!beRallyMap.containsKey(rallyEntity.getEntityId())) {
            LOGGER.error("{} onRemoveBeRally error{}, cur: {}", getOwner(), rallyEntity, beRallyMap.keySet());
            return;
        }
        beRallyMap.remove(rallyEntity.getEntityId());
        LOGGER.info("{} onRemoveBeRally {}, cur num:{}", getOwner(), rallyEntity, beRallyMap.size());
        checkRallyNtf(false);
        syncRallyToClan(rallyEntity.getEntityId(), false);
    }

    public boolean checkExistRally() {
        return rallyEntityMap.size() != 0 || beRallyMap.size() != 0;
    }

    private void checkRallyNtf(boolean isNew) {
        int size = rallyEntityMap.size() + beRallyMap.size();
        User.RallyNtfMsg.Builder builder = User.RallyNtfMsg.newBuilder();
        if (!isNew && size == 0) {
            builder.setIsExist(false);
        } else if (isNew && size == 1) {
            builder.setIsExist(true);
        } else {
            return;
        }
        getOwner().getMemberComponent().broadcastOnlineClientMsg(MsgType.RALLYNTFMSG, builder.build());
    }

    /**
     * 同步新增或删除的集结信息给军团
     */
    private void syncRallyToClan(long rallyId, boolean isNewRally) {
        SsClanAttr.SyncRedDotToClanCmd.Builder builder = SsClanAttr.SyncRedDotToClanCmd.newBuilder();
        builder.setType(CommonEnum.RedDotKey.RDK_CLAN_WAR).setRallyId(rallyId).setIsNewRally(isNewRally);
        getOwner().tellClan(builder.build());
    }

    /**
     * 检测是否可以发起对目标的集结
     */
    public boolean checkHasRallyToTargetId(long targetId) {
        return targetId2RallyId.containsKey(targetId);
    }

    /**
     * 联盟解散 解散集结
     */
    public void dismissAllRally(RallyDismissReason reason) {
        for (long rallyId : new ArrayList<>(rallyEntityMap.keySet())) {
            RallyEntity rallyEntity = rallyEntityMap.get(rallyId);
            if (rallyEntity == null) {
                continue;
            }
            rallyEntity.dismiss(RallyDismissReason.RDR_CLAN_DISMISS);
        }
        rallyEntityMap.clear();
    }

    /**
     * 获取联盟里所有集结列表
     */
    public void copyRallyInfoListToBuilder(RallyInfoListPB.Builder builder) {
        // 发起的集结  显示参与进攻的
        for (RallyEntity rallyEntity : rallyEntityMap.values()) {
            RallyInfoPB msg = rallyEntity.getRallySimpleInfoPb();
            if (msg == null) {
                continue;
            }
            builder.addDatas(msg);
        }
        // 被发起的集结  显示参与援助的
        for (RallyEntity rallyEntity : beRallyMap.values()) {
            RallyInfoPB msg = rallyEntity.getAssistSimpleInfoPb();
            if (msg == null) {
                continue;
            }
            builder.addDatas(msg);
        }
    }

    /**
     * 获取联盟里指定id的集结信息
     */
    public RallyInfoPB getOneRallyInfo(long rallyId) {
        RallyInfoPB ret = null;
        RallyEntity rallyEntity = rallyEntityMap.get(rallyId);
        if (rallyEntity != null) {
            ret = rallyEntity.getRallyInfoPb();
        }
        // 被发起的集结  显示参与援助的
        RallyEntity beRallyEntity = beRallyMap.get(rallyId);
        if (beRallyEntity != null) {
            ret = beRallyEntity.getAssistInfoPb();
        }
        if (ret == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY.getCodeId());
        }
        return ret;
    }

    /**
     * 获取集结entity
     */
    public RallyEntity getRallyEntity(long rallyId) {
        return rallyEntityMap.get(rallyId);
    }

    /**
     * 军团基础数据变更 更新
     */
    public void onClanBaseChange() {
        for (RallyEntity rally : rallyEntityMap.values()) {
            rally.getProp().setOrganizerClanShortName(getOwner().getClanSimpleName());
            ArmyEntity rallyArmy = rally.getArmyMgrComponent().getRallyArmy();
            // 更新旗帜
            if (rallyArmy != null) {
                rallyArmy.getPropComponent().updateClanFlag(getOwner());
            }
        }
        for (RallyEntity rally : beRallyMap.values()) {
            rally.getProp().setTargetClanShortName(getOwner().getClanSimpleName());
        }
    }
}

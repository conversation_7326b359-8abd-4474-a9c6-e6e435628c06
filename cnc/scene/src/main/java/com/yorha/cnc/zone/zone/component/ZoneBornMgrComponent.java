package com.yorha.cnc.zone.zone.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneBornMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.rank.RankHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.city.CityBornService;
import com.yorha.common.resource.resservice.multiServer.MultiServerService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.DriveTrafficUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.DriveTrafficHardwareLimitItemProp;
import com.yorha.game.gen.prop.RegionDriveTrafficHardwareLimitProp;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsRank;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BornAreaLimitTemplate;
import res.template.BornRegionSelectTemplate;
import res.template.ConstServerTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * zone导量管理器
 * <p>
 *
 * <AUTHOR>
 */
public class ZoneBornMgrComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneBornMgrComponent.class);

    /**
     * 已导量人数
     */
    private int playerNum = 0;
    /**
     * 导量档位1人数
     */
    private int level1Num = 0;
    /**
     * 导量档位2人数
     */
    private int level2Num = 0;

    /**
     * 上次计算的全服战力
     */
    private long lastZonePower = 0;

    public ZoneBornMgrComponent(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
        for (RegionDriveTrafficHardwareLimitProp prop : getProp().getRegionDriveTrafficHardwareLimit().values()) {
            for (DriveTrafficHardwareLimitItemProp trafficHardwareLimitItemProp : prop.getDriveTrafficHardwareLimit().values()) {
                playerNum += trafficHardwareLimitItemProp.getCount();
                switch (trafficHardwareLimitItemProp.getDriveTrafficHardwareLevel()) {
                    case 1:
                        level1Num += trafficHardwareLimitItemProp.getCount();
                        break;
                    case 2:
                        level2Num += trafficHardwareLimitItemProp.getCount();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @Override
    public void postInit() {
        updateRegisterMonitor();
    }

    public void addRepeatTimerToUpdateZoneGuideAndZoneCard() {
        // 定期更新注册数据
        getOwner().getTimerComponent().addRepeatTimer(this.getEntityId(), TimerReasonType.UPDATE_ZONE_GUIDE_RECORD_TABLE, () -> {
            // 停服时不再更新（actor停服处理先于定时器取消，避免报错）
            if (ServerContext.getServerStopStep() > 0) {
                return;
            }
            TcaplusDb.ZoneGuideRecordTable.Builder zoneGuideReq = TcaplusDb.ZoneGuideRecordTable.newBuilder()
                    .setZoneId(this.getOwner().getZoneId())
                    .setDirAssumeRegisterNum(playerNum)
                    .setDirAssumeHardwareLevel1Num(level1Num)
                    .setDirAssumeHardwareLevel2Num(level2Num);
            this.ownerActor().askGameDb(new UpsertAsk<>(zoneGuideReq, UpsertOption.getDefaultInstance())).onComplete((res, e) -> {
                if (e != null) {
                    LOGGER.error("upsert zoneGuideRecordTable fail", e);
                    return;
                }
                if (!DbUtil.isOk(res.getCode())) {
                    LOGGER.error("upsert zoneGuideRecordTable fail, code={}", res.getCode());
                } else {
                    LOGGER.info("upsert zoneGuideRecordTable success, data={}", zoneGuideReq);
                }
            });
            BigSceneEntity bigScene = getOwner().getBigScene();
            if (bigScene == null || !bigScene.isInitOk()) {
                LOGGER.error("updateZoneCardWhenChange but scene not init ok!");
                return;
            }
            this.getServerPowerAsync((power) -> {
                CommonMsg.ZoneCard zoneCard = this.buildZoneCard(power);
                // 同步新的zoneCard(zoneActor做了防重保护)
                getOwner().onRepeatTimerTriggerForZoneItem(zoneCard);
            });
        }, 0, 60, TimeUnit.SECONDS);
    }

    /**
     * 通知zoneActor去put数据ZoneItem到etcd
     * 整个SceneActor的sceneEntity、zoneEntity创建好之后才允许调用！
     */
    public void ntfZoneItemCreate() {
        LOGGER.info("ntfZoneItemCreate");
        this.getServerPowerAsync((power) -> {
            CommonMsg.ZoneCard zoneCard = this.buildZoneCard(power);
            LOGGER.info("ntfZoneItemCreate");
            getOwner().onFirstInit(zoneCard);
            // 需要第一次ZoneItem同步后才可以后续定时刷新
            getOwner().getBornMgrComponent().addRepeatTimerToUpdateZoneGuideAndZoneCard();
        });
    }

    public long getLastZonePower() {
        return lastZonePower;
    }

    /**
     * 获取导量出生州
     */
    public int getDrivenBornRegion(final int hardwareLevel) {
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            // 方便调试压测，固定出生州
            return 0;
        }
        final int driveTrafficLevel = DriveTrafficUtils.getTrafficLevel(hardwareLevel);

        // 根据typeNum导量机型获取对应州
        int targetRegionId = this.getDrivenTypeNumRegion(driveTrafficLevel);
        if (targetRegionId >= 0) {
            LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenBornRegion drive by typeNum, hardwareLevel={}, driveTrafficLevel={}, regionId={}", hardwareLevel, driveTrafficLevel, targetRegionId);
            return targetRegionId;
        }


        // 根据playerNum各州导量人数获取对应州
        targetRegionId = this.getDrivenPlayerNumRegion(driveTrafficLevel);
        if (targetRegionId >= 0) {
            LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenBornRegion drive by playerNum, hardwareLevel={}, driveTrafficLevel={}, regionId={}", hardwareLevel, driveTrafficLevel, targetRegionId);
            return targetRegionId;
        }

        // 根据numLimit各州人数限制获取对应州
        targetRegionId = this.getDrivenNumLimitRegion();
        if (targetRegionId >= 0) {
            LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenBornRegion drive by numLimit, hardwareLevel={}, driveTrafficLevel={}, regionId={}", hardwareLevel, driveTrafficLevel, targetRegionId);
            return targetRegionId;
        }
        LOGGER.warn("atZoneDrive ZoneBornMgrComponent getDrivenBornRegion drive fail, hardwareLevel={}, driveTrafficLevel={}, all zone reach numLimit", hardwareLevel, driveTrafficLevel);
        return -1;
    }

    /**
     * 根据playerNum配置导量
     *
     * @param driveTrafficLevel 导量档位
     * @return regionId（-1为不存在可用州）
     */
    private int getDrivenPlayerNumRegion(final int driveTrafficLevel) {
        int bornStage = ResHolder.getResService(CityBornService.class).getStage(playerNum + 1);
        if (bornStage == 0) {
            LOGGER.warn("atZoneDrive ZoneBornMgrComponent getDrivenPlayerNumRegion no stage for playerNum={}", playerNum + 1);
            return -1;
        }
        List<IntPairType> playerNumPairList = ResHolder.getInstance().getValueFromMap(BornRegionSelectTemplate.class, bornStage).getPlayerNumPairList();
        if (playerNumPairList.isEmpty()) {
            LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenPlayerNumRegion no playerNum config for curStage={}", bornStage);
        }
        int beginIndex = RandomUtils.nextInt(playerNumPairList.size());
        // 随机选择一个判断playerNum可否导量
        for (int i = 0; i < playerNumPairList.size(); i++) {
            IntPairType regionPlayerNum = playerNumPairList.get(beginIndex);
            beginIndex = (beginIndex + 1) % playerNumPairList.size();

            final int regionPlayerNumLimit = regionPlayerNum.getValue();
            final int targetRegionId = regionPlayerNum.getKey();
            if (isOverLimit(targetRegionId, regionPlayerNumLimit)) {
                LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenPlayerNumRegion regionId={} over playerNum limit, continue", targetRegionId);
                continue;
            }
            if (!this.isOverNumLimit(targetRegionId)) {
                LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenPlayerNumRegion driveTrafficLevel={}, regionId={}", driveTrafficLevel, targetRegionId);
                return targetRegionId;
            }
        }


        return -1;
    }

    /**
     * 构建全量写入ETCD的ZoneCard
     *
     * @return CommonMsg.ZoneInfoData
     */
    private CommonMsg.ZoneCard buildZoneCard(final long serverPower) {
        CommonEnum.ZoneSeasonStage curSeasonStage = getOwner().getSeasonComponent().getCurSeasonStage();
        final int registerNum = playerNum;
        CommonMsg.ZoneCard.Builder builder = CommonMsg.ZoneCard.newBuilder()
                .setServerJamStatus(getServerJamStatus(getOwner().getServerOpenTsMs(), registerNum, this.getOwner().getBigScene().getPlayerMgrComponent().getOnlinePlayerIds().size()))
                .setMileStoneId(this.getOwner().getMilestoneComponent().getCurMileStone())
                .setZoneSeason(this.getOwner().getSeasonComponent().getCurSeason())
                .setZoneSeasonStage(curSeasonStage)
                .setServerPower(serverPower);
        return builder.build();
    }


    private void getServerPowerAsync(Consumer<Long> onPower) {
        this.ownerActor().askZoneRank(getOwner().getZoneId(), SsRank.GetRankInfoByLimitAsk.newBuilder()
                .setRankId(RankConstant.ZONE_PLAYER_POWER_RANK)
                .setRange(RankHelper.buildRange(0, ResHolder.getConsts(ConstServerTemplate.class).getHighPowerServerPlayerNum() - 1))
                .build()).onComplete((ret, e) -> {
            // ask失败了就用旧的
            if (e != null) {
                LOGGER.error("getServerPowerAsync err,", e);
                onPower.accept(lastZonePower);
                return;
            }
            final SsRank.GetRankInfoByLimitAns ans = (SsRank.GetRankInfoByLimitAns) ret;
            long sum = 0;
            final long powerLimit = ResHolder.getConsts(ConstServerTemplate.class).getHighPowerServerRankingPower();
            LOGGER.info("getServerPowerAsync powerLimit={}", powerLimit);
            for (final CommonMsg.MemberAllDto memberAllDto : ans.getMembersList()) {
                long score = memberAllDto.getScore();
                if (score > powerLimit) {
                    score = powerLimit;
                }
                sum += score;
            }
            LOGGER.info("getServerPowerAsync serverPower={}", sum);
            lastZonePower = sum;
            onPower.accept(sum);
        });
    }

    /**
     * 获取服务器拥挤状态（开服时间必须小于当前时间，否则有未知错误）
     *
     * @param serverOpenTsMs    开服时间
     * @param registerPlayerNum 注册人数
     * @param onlinePlayerNum   在线人数
     * @return 服务器拥挤状态
     */
    public static CommonEnum.ServerJamStatus getServerJamStatus(final long serverOpenTsMs, final int registerPlayerNum, final int onlinePlayerNum) {
        long curTsMs = SystemClock.now();
        // 开服时间在未来，保护
        if (curTsMs < serverOpenTsMs) {
            return CommonEnum.ServerJamStatus.SJS_FULL;
        }
        // 前n天为爆满
        if (TimeUtils.getAbsDaysBetween(serverOpenTsMs, curTsMs) < ResHolder.getInstance().getConstTemplate(ConstServerTemplate.class).getServerFullDay()) {
            return CommonEnum.ServerJamStatus.SJS_FULL;
        }
        final MultiServerService multiServerService = ResHolder.getResService(MultiServerService.class);
        // 在线人数达到爆满阈值 && 注册人数达到爆满阈值
        if (registerPlayerNum >= multiServerService.getFullRegisterPlayerNum() && onlinePlayerNum >= multiServerService.getFullOnlinePlayerNum()) {
            return CommonEnum.ServerJamStatus.SJS_FULL;
        }
        // 在线人数达到拥挤阈值
        if (onlinePlayerNum >= multiServerService.getBusyOnlinePlayerNum()) {
            return CommonEnum.ServerJamStatus.SJS_HALF_FULL;
        }

        return CommonEnum.ServerJamStatus.SJS_NORMAL;
    }

    /**
     * 目标region人数超过playerLimit
     *
     * @param targetRegionId 目标regionId
     * @param playerLimit    玩家人数限制
     * @return true==目标region人数>=playerLimit
     */
    private boolean isOverLimit(final int targetRegionId, final int playerLimit) {
        return getOwner().getBigScene().getBornMgrComponent().getRegionPlayerNum(targetRegionId) >= playerLimit;
    }


    /**
     * 目标region人数超出导量numLimit
     *
     * @param targetRegionId 目标regionId
     * @return true==目标region人数>=numLimit
     */
    private boolean isOverNumLimit(final int targetRegionId) {
        BornAreaLimitTemplate bornAreaLimitTemplate = ResHolder.getResService(CityBornService.class).getBornAreaLimitTemplate(targetRegionId);
        if (bornAreaLimitTemplate == null) {
            LOGGER.error("atZoneDrive ZoneBornMgrComponent isOverNumLimit targetRegionId={}, no template", targetRegionId);
            return true;
        }
        int regionPlayerNumLimit = bornAreaLimitTemplate.getNumLimit();
        // 州落堡人数是否大于配置上限
        return this.isOverLimit(targetRegionId, regionPlayerNumLimit);
    }

    /**
     * 根据typeNum获取的导量州
     *
     * @param driveTrafficLevel 导量机型
     * @return regionId（-1为不存在可用州）
     */
    private int getDrivenTypeNumRegion(final int driveTrafficLevel) {
        int bornStage = ResHolder.getResService(CityBornService.class).getStage(playerNum + 1);
        if (bornStage == 0) {
            LOGGER.warn("atZoneDrive ZoneBornMgrComponent getDrivenTypeNumRegion no stage for playerNum={}", playerNum + 1);
            return -1;
        }
        final Map<Integer, List<Pair<Integer, Integer>>> curStageTypeNumLimit = ResHolder.getResService(CityBornService.class).getDriveTypeNum(bornStage);
        List<Integer> availableRegion = Lists.newArrayList();
        if (curStageTypeNumLimit.isEmpty()) {
            LOGGER.warn("atZoneDrive ZoneBornMgrComponent getDrivenTypeNumRegion no typeNum drive traffic for stage={}", bornStage);
            return -1;
        }
        // 筛选本档位下各州限制
        List<Pair<Integer, Integer>> orDefault = curStageTypeNumLimit.get(driveTrafficLevel);
        if (orDefault == null) {
            return -1;
        }
        for (Pair<Integer, Integer> regionLimit : orDefault) {
            Integer limit = regionLimit.getSecond();
            Integer regionId = regionLimit.getFirst();
            // 判断该州是否达到对应导量档位上限
            if (this.getDriveHardwareNum(regionId, driveTrafficLevel) >= limit) {
                LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenTypeNumRegion reach typeNum limit, driveTrafficLevel={}, regionId={}, limit={}", driveTrafficLevel, regionId, limit);
                continue;
            }

            BornAreaLimitTemplate bornAreaLimitTemplate = ResHolder.getResService(CityBornService.class).getBornAreaLimitTemplate(regionId);
            if (bornAreaLimitTemplate == null) {
                LOGGER.error("atZoneDrive ZoneBornMgrComponent ZoneBornMgrComponent getDrivenTypeNumRegion getBornAreaLimitTemplate is null, {} {}", driveTrafficLevel, regionId);
                return -1;
            }
            int regionPlayerNumLimit = bornAreaLimitTemplate.getNumLimit();
            if (getOwner().getBigScene().getBornMgrComponent().getRegionPlayerNum(regionId) >= regionPlayerNumLimit) {
                LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenTypeNumRegion reach num limit, driveTrafficLevel={}, regionId={}, limit={}", driveTrafficLevel, regionId, regionPlayerNumLimit);
                continue;
            }
            availableRegion.add(regionId);
        }


        // 存在导量机型可用州
        if (!availableRegion.isEmpty()) {
            int randomIndex = RandomUtils.nextInt(availableRegion.size());
            return availableRegion.get(randomIndex);
        }
        return -1;
    }

    /**
     * 寻找未满的导量州
     *
     * @return regionId（-1为不存在可用州）
     */
    private int getDrivenNumLimitRegion() {
        List<Integer> availableRegion = Lists.newArrayList();
        MainSceneBornMgrComponent component = getOwner().getBigScene().getBornMgrComponent();
        // 寻找未达人数上限州
        for (BornAreaLimitTemplate bornAreaLimitTemplate : ResHolder.getInstance().getListFromMap(BornAreaLimitTemplate.class)) {
            int regionId = bornAreaLimitTemplate.getStateId();
            int numLimit = bornAreaLimitTemplate.getNumLimit();

            if (component.getRegionPlayerNum(regionId) >= numLimit) {
                LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenNumLimitRegion reach num limit, regionId={}, limit={}", regionId, numLimit);
                continue;
            }
            availableRegion.add(regionId);
        }

        // 存在人数未满州
        if (!availableRegion.isEmpty()) {
            int randomIndex = RandomUtils.nextInt(availableRegion.size());
            int targetRegion = availableRegion.get(randomIndex);
            LOGGER.info("atZoneDrive ZoneBornMgrComponent getDrivenNumLimitRegion not reach numLimit, regionId={}", targetRegion);
            return targetRegion;
        }
        return -1;
    }

    private int getDriveHardwareNum(int regionId, int driveHardwareLevel) {
        RegionDriveTrafficHardwareLimitProp regionDriveTrafficHardwareLimitProp = this.getProp().getRegionDriveTrafficHardwareLimitV(regionId);
        if (regionDriveTrafficHardwareLimitProp == null) {
            return 0;
        }
        DriveTrafficHardwareLimitItemProp driveTrafficHardwareLimitItemProp = regionDriveTrafficHardwareLimitProp.getDriveTrafficHardwareLimitV(driveHardwareLevel);
        if (driveTrafficHardwareLimitItemProp == null) {
            return 0;
        }
        return driveTrafficHardwareLimitItemProp.getCount();
    }

    /**
     * 获取出生州
     */
    public int getBornRegion() {
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            // 方便调试压测，固定出生州
            return 0;
        }
        int bornStage = ResHolder.getResService(CityBornService.class).getStage(playerNum + 1);
        return ResHolder.getResService(CityBornService.class).getBornRegion(bornStage, playerNum + 1);
    }

    /**
     * 导量检测
     */
    public ErrorCode checkCreatePlayer() {
        int bornStage = ResHolder.getResService(CityBornService.class).getStage(playerNum + 1);
        // 导量分配表限制了
        if (bornStage == 0) {
            LOGGER.info("{} atScene, zone={} city born limit. curCount {}", LogKeyConstants.GAME_PLAYER_LOGIN, getOwner().getZoneId(), playerNum);
            return ErrorCode.BORN_STAGE_LIMIT;
        }
        // 导量总数限制
        final int registerLimit = ClusterConfigUtils.getZoneConfig(getOwner().getZoneId()).getIntItem("register_limit");
        if (playerNum >= registerLimit) {
            LOGGER.info("{} atScene, zone={} player Num limit. curCount {}", LogKeyConstants.GAME_PLAYER_LOGIN, getOwner().getZoneId(), playerNum);
            return ErrorCode.BORN_TOTAL_NUM_LIMIT;
        }
        return ErrorCode.OK;
    }

    /**
     * 导量计数增加
     */
    public void addPlayerNum(final int regionId, final int hardwareLevel, boolean isInnerPlayer) {
        playerNum++;
        getOwner().getSeasonComponent().addPlayerBornIndex();
        //州统计
        if (!getProp().getRegionDriveTrafficHardwareLimit().containsKey(regionId)) {
            getProp().getRegionDriveTrafficHardwareLimit().addEmptyValue(regionId);
        }

        this.addDriveTrafficLevelNum(hardwareLevel, regionId);


        if (isInnerPlayer) {
            int oldNum = getProp().getInternalPlayerNum();
            getProp().setInternalPlayerNum(oldNum + 1);
        }
        updateRegisterMonitor();
    }


    private void addDriveTrafficLevelNum(final int hardwareLevel, final int regionId) {
        //州内导量档位统计
        final int driveTrafficLevel = DriveTrafficUtils.getTrafficLevel(hardwareLevel);
        if (!getProp().getRegionDriveTrafficHardwareLimitV(regionId).getDriveTrafficHardwareLimit().containsKey(driveTrafficLevel)) {
            getProp().getRegionDriveTrafficHardwareLimitV(regionId).getDriveTrafficHardwareLimit().addEmptyValue(driveTrafficLevel);
        }
        DriveTrafficHardwareLimitItemProp prop = getProp().getRegionDriveTrafficHardwareLimitV(regionId).getDriveTrafficHardwareLimitV(driveTrafficLevel);
        prop.setCount(prop.getCount() + 1);


        LOGGER.info("addDriveTrafficLevelNum zone_drive_level_num_total cur level1Num={}, cur leve2Num={}, hardwareLevel={}, regionId={}", level1Num, level2Num, hardwareLevel, regionId);
        switch (driveTrafficLevel) {
            case 1:
                level1Num++;
                break;
            case 2:
                level2Num++;
                break;
            default:
                break;
        }
        LOGGER.info("addDriveTrafficLevelNum zone: {} addPlayerNum total: {} driveTrafficLevel: {} curCount: {}", getOwner().getZoneId(), playerNum, driveTrafficLevel, prop.getCount());
    }


    /**
     * 注册监控
     */

    private void updateRegisterMonitor() {
        final int registerLimit = ClusterConfigUtils.getZoneConfig(getOwner().getZoneId()).getIntItem("register_limit");
        MonitorUnit.GAME_ZONE_STATUS_PLAYER_REGISTER_NUM.labels(
                        ServerContext.getBusId(),
                        ServerContext.getWorldIdStr(),
                        ownerActor().getZoneIdStr())
                .set(playerNum);

        LOGGER.info("gemini_perf Register Player, worldId:{}, zoneId:{}, limit:{}, playerNum={} ", ServerContext.getWorldIdStr()
                , ownerActor().getZoneIdStr(), registerLimit, playerNum);
    }

    public int getPlayerNum() {
        return playerNum;
    }

    public int getNameIndex() {
        return getOwner().getSeasonComponent().getPlayerBornIndex();
    }

    private ZoneInfoProp getProp() {
        return getOwner().getProp();
    }

    @Override
    public SceneActor ownerActor() {
        return (SceneActor) super.ownerActor();
    }
}


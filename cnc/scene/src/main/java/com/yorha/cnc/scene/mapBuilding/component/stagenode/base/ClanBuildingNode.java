package com.yorha.cnc.scene.mapBuilding.component.stagenode.base;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.event.mapbuilding.TemplateChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.NeutralStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.FireStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.DesertedStageNode;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import com.yorha.proto.CommonEnum.OccupyState;
import com.yorha.proto.SsClanTerritory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.TerritoryBuildingTemplate;

/**
 * 改建状态基类
 *
 * <AUTHOR>
 */
public abstract class ClanBuildingNode extends StageNode {
    private static final Logger LOGGER = LogManager.getLogger(ClanBuildingNode.class);

    protected ClanBuildingNode(MapBuildingEntity owner) {
        super(owner);
    }

    public ConstClanTerritoryTemplate getConstClanTerritoryTemplate() {
        return ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
    }

    /**
     * 被打成功
     * 改建中  直接拆掉
     * 燃烧中  不处理
     * 恢复中  结算下  再进入燃烧
     * 改建好  直接进入燃烧
     *
     * @param clanId 攻击者联盟
     */
    public void onAttackSuccess(long clanId) {
        // 获取着火状态的加成
        SceneClanEntity sceneClanEntity = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        long addition = 0L;
        if (null != sceneClanEntity) {
            addition = sceneClanEntity.getAddComponent().getAddition(BuffEffectType.ET_INC_EMENY_CLAN_BUILDING_FIRE_SPEED_PERCENT_VALUE);
            sceneClanEntity.getBuildComponent().addBuildingWhenTriggerFire(getOwner().getPartId());
            getProp().setTriggerFireClanId(clanId);
        }
        // 设置下属性
        setFireData(addition, 0L);
        getComponent().transNewNode(new FireStageNode(getOwner()));
    }

    /**
     * 破坏建筑
     * 改建中被攻击/被拆除   白嫖回归中立  非白嫖进入荒废
     * 其他状态  回归荒废
     *
     * @param isActive        是否是主动的  主动拆除、改建中被攻击、烧完了
     * @param needChangeStage 是否需要切状态   烧完了是外面自己处理进入中立的  其他都是进入中立或者荒废
     */
    public void onDestroyBuilding(boolean isActive, boolean needChangeStage) {
        LOGGER.info("{} {}  destroyClanBuilding", getOwner(), this);
        SceneClanEntity ownerSceneClan = getOwnerSceneClan();
        // 通知当前联盟拆除
        boolean isNeedUpdateBuff = ownerSceneClan.getBuildComponent().destroyBuild(getEntityId());
        if (isActive) {
            // 发送主动拆除邮件
            sendRebuildMail(getConstClanTerritoryTemplate().getRemoveBuildingMail(), getOwner().getOwnerClanId(), getConstructInfoProp().getStartStaffId());
        } else {
            // 燃烧没了
            sendRebuildMail(getConstClanTerritoryTemplate().getDetroyByFireMail());
        }
        // 处理城内援军 属性  进入荒废
        afterDestroy(needChangeStage);
        // buff变更 发送快照
        if (isNeedUpdateBuff) {
            ownerSceneClan.getMapBuildingComponent().sendTerritoryBuffSnapshot();
        }
    }

    protected void afterDestroy(boolean needChangeStage) {
        // 遣返建筑内所有军队
        getOwner().getInnerArmyComponent().returnAllArmy();
        // 还原回原来的建筑
        int oldLayer = getOwner().getMaxSceneLayer();
        // 主基地建筑消失时（可能是被动回归中立或主动删除）冗余将是否免费获取置为false
        boolean isMainBase = getConstructInfoProp().getType() == CommonEnum.MapBuildingType.MBT_MAIN_BASE;
        // 清理改建相关prop
        getComponent().clearRebuildProp();
        // change下寻路阻挡
        getOwner().getTransformComponent().changeCollision();
        // 重置模型圈
        getOwner().getTransformComponent().resetModelRadius();
        // 通知视野层级变化
        getOwner().getAoiNodeComponent().onChangeLayer(oldLayer, getOwner().getMaxSceneLayer());
        getOwner().getEventDispatcher().dispatch(new TemplateChangeEvent(getEntityId()));
        if (!needChangeStage) {
            return;
        }
        getComponent().cancelStageTimer();
        // 如果是主基地 回归中立
        if (isMainBase) {
            getComponent().transNewNode(new NeutralStageNode(getOwner()));
            return;
        }
        // 剩下的回归荒废
        getComponent().transNewNode(new DesertedStageNode(getOwner()));
        getProp().setWouldOverBurn(false);
    }

    public void onRefreshMaxDurability() {
        // 更新最大耐久度
        TerritoryBuildingTemplate template = getOwner().getClanBuildingTemplate();
        int maxHp = (int) SceneAddCalc.getMaxHp(getOwner().getBuildComponent().getType(), template.getMaxHp(), getOccupyMgr().getOwnerSceneClan());
        int oldMaxHp = getConstructInfoProp().getMaxDurability();
        if (maxHp == oldMaxHp) {
            return;
        }
        getConstructInfoProp().setMaxDurability(maxHp);
        LOGGER.info("{} onRefreshMaxDurability old: {} new: {}", getOwner(), oldMaxHp, maxHp);
        afterRefreshMaxHp(oldMaxHp, maxHp);
    }

    protected abstract void afterRefreshMaxHp(int oldHp, int newHp);

    /**
     * 设置着火相关字段  返回燃烧结束时间戳
     * addition为-1表示非燃烧速度变更导致的 不需要更新燃烧速度
     */
    protected void setFireData(long addition, long alreadyFireTsMs) {
        ConstClanTerritoryTemplate template = getConstClanTerritoryTemplate();
        long realFireSpeed = getProp().getFireSpeed();
        if (addition != -1) {
            realFireSpeed = SceneAddCalc.getFireSpeed(addition);
        }
        long now = SystemClock.now();
        // 计算剩余hp可以燃烧的毫秒数，当前hp需要乘以10000，配合hp燃烧速度
        long hpCanFireTsMs = TimeUtils.second2Ms(getConstructInfoProp().getCurrentDurability() * Constants.N_10_000L / realFireSpeed);
        // 着火的配置时间
        long maxFireTsMs = TimeUtils.second2Ms(template.getMaxFireTime());
        // 跟已经着火的时间作比较
        maxFireTsMs = Math.max(maxFireTsMs - alreadyFireTsMs, 0L);
        // 计算燃烧结束时间
        long stateEndTsMs = 0L;
        // hp可燃烧时间小于配置燃烧时间  直接烧毁 回归中立
        if (hpCanFireTsMs <= maxFireTsMs) {
            stateEndTsMs = now + hpCanFireTsMs;
            getProp().setWouldOverBurn(true);
        } else {
            stateEndTsMs = now + maxFireTsMs;
            getProp().setWouldOverBurn(false);
        }
        getProp().setFileNumCalcTsMs(now).setFireSpeed((int) realFireSpeed).setStateEndTsMs(stateEndTsMs);
        getConstructInfoProp().setIsOnFire(true);
        LOGGER.info("{} resetFireSpeed. hpCanFireTime:{} maxFireTime:{}", getOwner(), hpCanFireTsMs, maxFireTsMs);
    }

    /**
     * 更新耐久度
     */
    protected void refreshDurability() {
        // 第一步先把timer取消了
        getComponent().cancelStageTimer();
        // 结算进度
        int curHp = getConstructInfoProp().getCurrentDurability();
        int maxHp = getConstructInfoProp().getMaxDurability();
        long lastCalcTsMs = getProp().getFileNumCalcTsMs();
        ConstClanTerritoryTemplate template = getConstClanTerritoryTemplate();
        // gm调整时间后或长时间停机后，now可能已经大于建筑状态本身的结束时间戳，需要保证计算耐久度只会到状态结束
        long settleTsMs = Math.min(SystemClock.now(), getProp().getStateEndTsMs());
        long calcTs = TimeUtils.ms2Second(settleTsMs - lastCalcTsMs);
        if (getConstructInfoProp().getIsOnFire()) {
            // 燃烧速度是模拟小数计算的会额外乘以10000，所以在计算扣除hp的时候要把这部分除掉
            int deductHp = (int) (getProp().getFireSpeed() * calcTs / Constants.N_10_000L);
            getConstructInfoProp().setCurrentDurability(Math.max(curHp - deductHp, 0));
        }
        if (getProp().getState() == OccupyState.TOS_AFTER_FIRE_RECOVER) {
            int addHp = (int) (template.getRecoverFireSpeed() * calcTs);
            getConstructInfoProp().setCurrentDurability(Math.min(curHp + addHp, maxHp));
        }
        getProp().setFileNumCalcTsMs(settleTsMs);
    }

    /**
     * 外部引起的扣除耐久度
     */
    public void onHpDec(int decNum) {
        int curHp = getConstructInfoProp().getCurrentDurability();
        int finalHp = Math.max(0, curHp - decNum);
        getConstructInfoProp().setCurrentDurability(finalHp);
        LOGGER.info("{} onHpDec curHp: {} needDec: {} finalHp: {}", getOwner(), curHp, decNum, finalHp);
    }

    protected void sendRebuildMail(int mailId) {
        getBuildMgr().sendCommonRebuildMail(mailId, getOwner().getOwnerClanId(), 0);
    }

    protected void sendRebuildMail(int mailId, long clanId) {
        syncClanBuildStatus(CommonEnum.ClanBuildStatus.CBS_CANCEL);
        getBuildMgr().sendCommonRebuildMail(mailId, clanId, 0);
    }

    protected void sendRebuildMail(int mailId, long clanId, int staffId) {
        syncClanBuildStatus(CommonEnum.ClanBuildStatus.CBS_CANCEL);
        getBuildMgr().sendCommonRebuildMail(mailId, clanId, staffId);
    }

    protected void sendStartBuildMail(int mailId) {
        getBuildMgr().sendStartRebuildMail(mailId);
    }

    protected void sendFinishBuildMail() {
        syncClanBuildStatus(CommonEnum.ClanBuildStatus.CBS_FINISH);
        // 邮件在clan上去发，因为要拿到建筑的建造消耗
    }

    private void syncClanBuildStatus(CommonEnum.ClanBuildStatus status) {
        SsClanTerritory.SyncClanBuildStatusCmd.Builder cmdBuilder = SsClanTerritory.SyncClanBuildStatusCmd.newBuilder();
        cmdBuilder.setId(getEntityId());
        cmdBuilder.setPoint(getOwner().formScenePoint());
        cmdBuilder.setStatus(status);
        cmdBuilder.setTemplateId(getOwner().getProp().getTemplateId());
        cmdBuilder.setIsClanResBuilding(false);
        getBuildMgr().getOwnerSceneClan().tellClan(cmdBuilder.build());
    }
}

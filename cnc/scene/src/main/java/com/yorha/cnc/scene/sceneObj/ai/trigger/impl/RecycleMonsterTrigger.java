package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;

import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.resource.datatype.IntPairType;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * <p>
 * 回收野怪触发器
 */
public class RecycleMonsterTrigger extends AiTrigger {


    public RecycleMonsterTrigger(AiStateTriggerTemplate template) {
        super(template);
    }

    @Override
    protected void parse(List<IntPairType> param) {

    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        return true;
    }

    @Override
    protected void doTrigger(SceneObjEntity owner) {
        ObjMgrComponent objMgrComponent = owner.getScene().getObjMgrComponent();
        SceneObjAiComponent aiComponent = owner.getAiComponent();
        List<Long> list = new ArrayList<>();
        list.addAll(aiComponent.getInvokesNotFollowDestory());
        list.addAll(aiComponent.getInvokesFollowDestory());
        for (long invokeId : list) {
            SceneObjEntity objEntity = objMgrComponent.getSceneObjEntity(invokeId);
            if (objEntity == null) {
                continue;
            }
            objEntity.deleteObj();
        }
        aiComponent.clearAllInvokes();
    }



    @Override
    protected String getTriggerParam() {
        return "";
    }

    @Override
    protected String getTriggerName() {
        return "RecycleInvokeMonsterTrigger";
    }
}

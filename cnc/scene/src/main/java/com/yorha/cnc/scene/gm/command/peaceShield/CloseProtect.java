package com.yorha.cnc.scene.gm.command.peaceShield;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.component.PeaceShieldComponent;
import com.yorha.cnc.scene.entity.component.ScenePlayerMgrComponent;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.resource.ResHolder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.Map;

/**
 * 关闭所有罩子
 *
 * <AUTHOR>
 */
public class CloseProtect implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(CloseProtect.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        boolean isSysShield = Boolean.parseBoolean(args.get("isSysShield"));
        int buffId = 0;
        if (isSysShield) {
            buffId = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getServerMaintenanceBuff();
        }
        PeaceShieldComponent peaceShieldComponent = actor.getScene().getPeaceShieldComponent();
        for (Object player : actor.getScene().getPlayerMgrComponent().getAllScenePlayer()) {
            try {
                peaceShieldComponent.closeSinglePeaceShield((AbstractScenePlayerEntity)player, buffId);
            } catch (Exception e) {
                LOGGER.error("", e);
            }
        }
    }

    @Override
    public String showHelp() {
        return "CloseProtect isSysShield={value}";
    }
}

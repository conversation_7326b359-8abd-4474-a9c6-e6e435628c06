package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;

/**
 * 献祭
 * 进入条件： master存在
 * 进入：给master加血
 * 执行：无
 * 结束：无
 *
 * <AUTHOR>
 */
public class SacrificeAction extends AbstractAIAction {


    public SacrificeAction() {
        super();
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
    }

    @Override
    protected String getActionName() {
        return "SacrificeAction";
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        component.addRecord(CommonEnum.AiRecordType.ART_SACRIFICE_TIME, SystemClock.now() + TimeUtils.second2Ms(2));
    }

    @Override
    public void fire(SceneObjAiComponent component) {
        super.fire(component);
        Object record = component.tryGetRecord(CommonEnum.AiRecordType.ART_SACRIFICE_TIME);
        if (record == null) {
            return;
        }
        if (SystemClock.now() < (long) record) {
            return;
        }
        component.removeRecord(CommonEnum.AiRecordType.ART_SACRIFICE_TIME);
        // 自杀
        component.getOwner().deleteObj();
    }
}

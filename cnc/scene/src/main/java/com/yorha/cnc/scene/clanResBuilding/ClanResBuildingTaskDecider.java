package com.yorha.cnc.scene.clanResBuilding;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 军团资源中心起服恢复决策器
 *
 * <AUTHOR>
 */
public class ClanResBuildingTaskDecider {
    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildingTaskDecider.class);
    private long firstLeaveArmyTsMs;

    private long disappearTsMs;

    private long collectFinishTsMs;

    private long now;

    private TaskDecisionType decisionType;

    private boolean alreadyDecideDestroy = false;

    private boolean alreadyAddTimer = false;

    public ClanResBuildingTaskDecider(long firstLeaveArmyTsMs, long disappearTsMs, long collectFinishTsMs, long now) {
        this.firstLeaveArmyTsMs = firstLeaveArmyTsMs;
        this.disappearTsMs = disappearTsMs;
        this.collectFinishTsMs = collectFinishTsMs;
        this.now = now;
    }

    public void decide() {
        long minTsMs = Math.min(firstLeaveArmyTsMs, Math.min(disappearTsMs, collectFinishTsMs));
        if (isAllTimeStampLaterThanNow()) {
            // 选出离now最近的时间起定时器，一旦起了定时器，决策器就不需要再决策了
            if (minTsMs == firstLeaveArmyTsMs) {
                decisionType = TaskDecisionType.ADD_TIMER_ONE_ARMY_LEAVE;
            } else if (minTsMs == disappearTsMs) {
                decisionType = TaskDecisionType.ADD_TIMER_EXPIRED;
            } else {
                decisionType = TaskDecisionType.ADD_TIMER_FINISH_COLLECT;
            }
            alreadyAddTimer = true;
            LOGGER.info("ClanResBuildingTaskDecider decide, all timestamp later than now, decisionType:{}", decisionType);
            return;
        }
        if (minTsMs == firstLeaveArmyTsMs) {
            decisionType = TaskDecisionType.ONE_ARMY_LEAVE;
        } else if (minTsMs == disappearTsMs) {
            decisionType = TaskDecisionType.EXPIRED;
            alreadyDecideDestroy = true;
        } else {
            decisionType = TaskDecisionType.FINISH_COLLECT;
            alreadyDecideDestroy = true;
        }
        LOGGER.info("ClanResBuildingTaskDecider decide, decisionType:{}", decisionType);
    }

    public long getNextDecisionTsMs() {
        decide();
        switch (decisionType) {
            case ADD_TIMER_EXPIRED:
            case EXPIRED:
                return disappearTsMs;
            case FINISH_COLLECT:
            case ADD_TIMER_FINISH_COLLECT:
                return collectFinishTsMs;
            case ONE_ARMY_LEAVE:
            case ADD_TIMER_ONE_ARMY_LEAVE:
                return firstLeaveArmyTsMs;
            default:
                return 0;
        }
    }

    public TaskDecisionType getNextDecisionType() {
        return decisionType;
    }

    /**
     * 无论哪种时间戳更新都可能导致决策结果变化，把不需要决策的标志位置为false
     */
    private void updateImpl() {
        alreadyAddTimer = false;
        alreadyDecideDestroy = false;
    }

    public void updateAllTimeStamp(long firstLeaveArmyTsMs, long disappearTsMs, long collectFinishTsMs, long now) {
        this.firstLeaveArmyTsMs = firstLeaveArmyTsMs;
        this.disappearTsMs = disappearTsMs;
        this.collectFinishTsMs = collectFinishTsMs;
        this.now = now;
        LOGGER.info("ClanResBuildingTaskDecider update, firstLeaveArmyTsMs:{}, disappearTsMs:{}, collectFinishTsMs:{}, now:{}",
                firstLeaveArmyTsMs, disappearTsMs, collectFinishTsMs, now);
        updateImpl();
    }

    public void updateFirstLeaveArmyTsMs(long firstLeaveArmyTsMs) {
        this.firstLeaveArmyTsMs = firstLeaveArmyTsMs;
        updateImpl();
    }

    public void updateCollectFinishTsMs(long collectFinishTsMs) {
        this.collectFinishTsMs = collectFinishTsMs;
        updateImpl();
    }

    public void updateNow(long now) {
        this.now = now;
        updateImpl();
    }

    public boolean isNeedDecide() {
        return !alreadyAddTimer && !alreadyDecideDestroy;
    }

    private boolean isAllTimeStampLaterThanNow() {
        return firstLeaveArmyTsMs > now && disappearTsMs > now && collectFinishTsMs > now;
    }

}

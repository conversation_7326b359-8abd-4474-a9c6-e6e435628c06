package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Core;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.Map;

/**
 * 所有建筑改建成军团建筑的阻挡检查
 *
 * <AUTHOR>
 */
public class AllBuildingCollisionCheck implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        Map<Integer, Core.Code> partIdToErrorCode = actor.getScene().getBuildingMgrComponent().gmCheckAllBuildingCollision();

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("AllBuildingCollisionCheck")
                .setSubTitle("AllBuildingCollisionCheck To Clan Main Base");
        builder.setTitle(titleBuilder);

        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        StringBuilder sb = new StringBuilder();
        sb.append("total failed size is ").append(partIdToErrorCode.size()).append("\n");
        if (!partIdToErrorCode.isEmpty()) {
            contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
            for (int partId : partIdToErrorCode.keySet()) {
                sb.append("partId: ").append(partId).append("\n")
                        .append(actor.getScene().getBuildingMgrComponent().getMapBuilding(partId).getProp().getPoint().toString())
                        .append(" ").append(partIdToErrorCode.get(partId).toString())
                        .append("\n");
            }
            contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(sb.toString()));
        }
        builder.setContent(contentBuilder.build());

        ScenePlayerEntity scenePlayer = actor.getScenePlayer(playerId);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(scenePlayer.getZoneId())
                        .build(),
                builder.build());
    }

    @Override
    public String showHelp() {
        return "AllBuildingCollisionCheck";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}

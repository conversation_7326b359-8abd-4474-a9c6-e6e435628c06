package com.yorha.cnc.scene.entity.collision;

import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.SystemClock;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
public class GridCollisionWorld extends AbstractCollisionWorld {
    private static class Grid {
        final int x;
        final int y;
        long updateTsMs;
        final Map<Long, Collision> collisionMap = new HashMap<>();

        public Grid(int x, int y, long updateTsMs) {
            this.x = x;
            this.y = y;
            this.updateTsMs = updateTsMs;
        }

        public void put(long id, Collision collision, long updateTsMs) {
            collisionMap.put(id, collision);
            this.updateTsMs = updateTsMs;
        }

        public void remove(long id, long updateTsMs) {
            collisionMap.remove(id);
            this.updateTsMs = updateTsMs;
        }
    }

    private final int gridSize;
    private final int gridWidth;
    private final int gridHeight;
    private final Grid[][] grids;

    public GridCollisionWorld(int width, int height, int gridSize) {
        super(width, height);
        this.gridSize = gridSize;
        gridWidth = (width + gridSize - 1) / gridSize;
        gridHeight = (height + gridSize - 1) / gridSize;
        long updateTsMs = SystemClock.now();
        grids = new Grid[gridWidth][gridHeight];
        for (int x = 0; x < gridWidth; ++x) {
            for (int y = 0; y < gridHeight; ++y) {
                grids[x][y] = new Grid(x, y, updateTsMs);
            }
        }
    }

    @Override
    public boolean add(long id, Shape shape) {
        Collision collision = new Collision(shape);
        if (collisions.putIfAbsent(id, collision) != null) {
            return false;
        }
        long now = SystemClock.now();
        traverseGridsIntersectWithAABB(collision.shapeAABB, grid -> {
            grid.put(id, collision, now);
            return true;
        });

        return true;
    }

    @Override
    public boolean remove(long id) {
        Collision collision = collisions.remove(id);
        if (collision == null) {
            return false;
        }
        long now = SystemClock.now();
        traverseGridsIntersectWithAABB(collision.shapeAABB, grid -> {
            grid.remove(id, now);
            return true;
        });

        return true;
    }

    @Override
    public boolean isInCollision(Shape shape) {
        AtomicBoolean ret = new AtomicBoolean(false);
        traverseGridsIntersectWithAABB(shape.getAABB(), grid -> {
            for (Collision collision : grid.collisionMap.values()) {
                if (ShapeUtils.isContact(shape, collision.shape)) {
                    ret.set(true);
                    return false;
                }
            }
            return true;
        });
        return ret.get();
    }

    @Override
    public boolean checkNeedRefreshPath(Point p, int radius, long t) {
        int left = Math.max(toGridX(p.getX() - radius), 0);
        int top = Math.max(toGridY(p.getY() - radius), 0);
        int right = Math.min(toGridX(p.getX() + radius), gridWidth - 1);
        int bottom = Math.min(toGridY(p.getY() + radius), gridHeight - 1);
        for (int x = left; x <= right; ++x) {
            for (int y = top; y <= bottom; ++y) {
                if (grids[x][y].updateTsMs > t) {
                    return true;
                }
            }
        }
        return false;
    }


    private int toGridX(int x) {
        return x / gridSize;
    }

    private int toGridY(int y) {
        return y / gridSize;
    }

    @Override
    public Map<Long, Shape> getAllCollisionByShape(Shape shape) {
        Map<Long, Shape> ret = new HashMap<>();
        traverseGridsIntersectWithAABB(shape.getAABB(), grid -> {
            for (Entry<Long, Collision> entry : grid.collisionMap.entrySet()) {
                if (ret.containsKey(entry.getKey())) {
                    continue;
                }
                if (ShapeUtils.isContact(shape, entry.getValue().shape)) {
                    ret.put(entry.getKey(), entry.getValue().shape);
                }
            }
            return true;
        });
        return ret;
    }

    /**
     * 使用aabb遍历所有格子执行操作
     *
     * @param aabb      形状包围盒
     * @param predicate 返回true代表继续下一个格子
     */
    private void traverseGridsIntersectWithAABB(AABB aabb, Predicate<Grid> predicate) {
        int left = Math.max(toGridX(aabb.getLeft()), 0);
        int top = Math.max(toGridY(aabb.getTop()), 0);
        int right = Math.min(toGridX(aabb.getRight()), gridWidth - 1);
        int bottom = Math.min(toGridY(aabb.getBottom()), gridHeight - 1);

        for (int x = left; x <= right; ++x) {
            for (int y = top; y <= bottom; ++y) {
                if (predicate.test(grids[x][y])) {
                    continue;
                }
                return;
            }
        }
    }
}

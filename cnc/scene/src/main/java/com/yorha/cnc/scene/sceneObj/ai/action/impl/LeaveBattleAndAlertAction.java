package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;

import com.yorha.proto.CommonEnum;

import static com.yorha.proto.CommonEnum.AiRecordType.ART_BACK_TO_POINT;

/**
 * 脱战且回到原点
 * 进入条件： 仇恨列表清空
 * 进入: 清空仇恨列表
 * 执行: 回血，警戒，回到原点
 * 结束： 无
 *
 * <AUTHOR>
 */
public class LeaveBattleAndAlertAction extends AbstractAIAction {

    public LeaveBattleAndAlertAction() { super(); }

    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        return component.canLeaveBattle(getActionName());
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        component.getOwner().getHateListComponent().clear();
        component.addRecord(ART_BACK_TO_POINT, false);
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
        // 脱战
        component.leaveBattle(component, getActionName());
        // 持续警戒
        if (component.getOwner().getHateListComponent().getMostHateEntity() <= 0L) {
            component.alert(component.getAiParams().get(CommonEnum.AiParams.AP_ALERT_RANGE));
            if (component.isDebugAble()) {
                LOGGER.info("{}, fire and execute :{} hatelist:{} mostHate:{}",
                        component.getLogHead(), getActionName(), component.getOwner().getHateListComponent().getHateEntities(), component.getOwner().getHateListComponent().getMostHateEntity());
            }
        }
    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        super.onEnd(component);
        SceneObjEntity owner = component.getOwner();
        boolean isBacking = (boolean) component.getRecord(ART_BACK_TO_POINT);
        if (isBacking) {
            owner.getMoveComponent().stopMove();
        }
        component.removeRecord(ART_BACK_TO_POINT);
    }


    @Override
    protected String getActionName() {
        return "LeaveBattleAndAlert";
    }
}

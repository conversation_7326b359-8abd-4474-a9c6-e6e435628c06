package com.yorha.cnc.scene.sceneObj;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.tick.SceneSchedule;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.sceneObj.camp.CampRelationProvider;
import com.yorha.cnc.scene.sceneObj.component.*;
import com.yorha.cnc.scene.sceneObj.component.aoi.SceneObjAoiComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.WorldLayerService;
import com.yorha.common.utils.eventdispatcher.EventDispatcher;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int64ArmyArrowItemMapProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Core.Code;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
public abstract class SceneObjEntity extends AbstractEntity implements CampRelationProvider, SceneSchedule {
    public static final Logger LOGGER = LogManager.getLogger(SceneObjEntity.class);
    private final SceneEntity sceneEntityRef;

    private final SceneObjPropComponent propComponent;
    private final SceneObjDbComponent dbComponent;
    private final SceneObjTransformComponent transformComponent;
    private final SceneObjAoiComponent aoiNodeComponent;
    private final SceneObjArrowComponent arrowComponent;
    private final SceneObjTimerComponent timerComponent;
    private final EventDispatcher eventDispatcher = new EventDispatcher();
    private final SceneObjSpyComponent spyComponent;

    public SceneObjEntity(SceneObjBuilder builder) {
        super(builder.getEntityId());
        this.sceneEntityRef = builder.getSceneEntity();
        this.propComponent = builder.propComponent(this);
        this.dbComponent = builder.dbComponent(this);
        this.transformComponent = builder.transformComponent(this);
        this.aoiNodeComponent = builder.aoiNodeComponent(this);
        this.arrowComponent = builder.arrowComponent(this);
        this.spyComponent = builder.spyComponent(this);
        this.timerComponent = builder.timerComponent(this);
    }

    @Override
    public SceneActor ownerActor() {
        return sceneEntityRef.ownerActor();
    }

    @Override
    public int getZoneId() {
        return getScene().getZoneId();
    }

    // ------------------------- 必须实现 -------------------------
    public abstract AbstractPropNode getProp();

    public abstract void fullCsEntityAttr(EntityAttr.Builder builder);

    public abstract int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder);

    public abstract int changedCsEntityAttr(EntityAttr.Builder builder);

    public abstract void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder);

    public abstract int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder);

    public abstract EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder);

    public abstract SceneObjBattleComponent getBattleComponent();

    public abstract SceneObjAdditionComponent getAdditionComponent();

    public abstract SceneObjBuffComponent getBuffComponent();

    /**
     * 场景物体类型  无极缩放使用
     */
    public abstract SceneObjectEnum getSceneObjType();

    /**
     * 生命周期到了
     */
    protected void onLifeEnd() {

    }

    public boolean briefEntityAttr(Entity.SceneObjBriefAttr.Builder builder) {
        return false;
    }

    /**
     * 被army作为目标时，需要copy数据给客户端显示
     */
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
    }

    /**
     * copy到预警界面的pb
     */
    public boolean copyToWarningInfo(StructPlayerPB.WarningInfoPB.Builder builder) {
        return false;
    }


    // ------------------------- 生命周期 -------------------------

    @Override
    public void deleteObj() {
        super.deleteObj();
        getScene().getTickMgrComponent().unRegisterRefreshChase(getEntityId());
        getEventDispatcher().dispatch(new DeleteEvent(getEntityId()));
        if (getScene().getObjMgrComponent().isObjOnScene(this)) {
            removeFromScene();
        }
        if (getBattleComponent() != null) {
            getBattleComponent().forceEndAllBattle();
            getBattleComponent().onDeleteObj();
        }
        // 解除所有监听的引用
        getEventDispatcher().clear();
        // 取消所有timer和tick
        clearTickAndSchedule();
    }

    @Override
    protected void onPostInitFailed() {
        clearTickAndSchedule();
        getTimerComponent().onDestroy();
    }

    @Override
    protected Collection<SceneObjComponent> getAllComponents() {
        return super.getAllComponents();
    }

    public void callAfterAllLoad() {
        getAllComponents().forEach(SceneObjComponent::afterAllLoad);
    }

    public void addIntoScene() {
        getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_BORN);
        getScene().getObjMgrComponent().addSceneObjEntity(this);
    }

    public void removeFromScene() {
        if (getAoiNodeComponent().isInAoi()) {
            getAoiNodeComponent().removeFromAoi(getRemoveReason());
        }
        getScene().getObjMgrComponent().removeSceneObjEntity(this);
    }

    public SceneEntity getScene() {
        return this.sceneEntityRef;
    }

    public SceneObjectNtfReason getRemoveReason() {
        return SceneObjectNtfReason.SONR_DIE;
    }

    // ------------------------- 快捷方法 -------------------------

    /**
     * 获取当前位置
     */
    public Point getCurPoint() {
        return getTransformComponent().getCurPoint();
    }

    /**
     * 生成坐标点（邮件、聊天需要）
     */
    public final Struct.Point formScenePoint() {
        return Struct.Point.newBuilder()
                .setX(getCurPoint().getX())
                .setY(getCurPoint().getY())
                .setMapId(this.getScene().getMapIdForPoint())
                .setMapType(this.getScene().getMapType().getNumber()).build();
    }

    public int getVersion() {
        return getPropComponent().getVersion();
    }

    public boolean isShieldOn() {
        return false;
    }

    public CommonEnum.SafeGuardReason checkShieldHasFunction(CommonEnum.SafeGuardFunctionType functionType) {
        return CommonEnum.SafeGuardReason.SGR_NONE;
    }

    public CommonEnum.SafeGuardReason checkSpecialShieldHasFunction(CommonEnum.SafeGuardFunctionType functionType) {
        return CommonEnum.SafeGuardReason.SGR_NONE;
    }

    /**
     * scene上对两个obj之间能否发生战斗的限定，包括场景中obj阵营、所处区域等条件
     */
    public final boolean canBattle(SceneObjEntity target, boolean needCheckSiegeLimit) {
        return canBattleWithCode(target, needCheckSiegeLimit).isOk();
    }

    /**
     * 能不能被打
     * other， scenePlayer只有一个会有值
     * scenePlayer != null 代表玩家尝试创建行军发起进攻
     */
    public final boolean canBeAttack(SceneObjEntity other, AbstractScenePlayerEntity scenePlayer, boolean needCheckSiegeLimit) {
        return canBeAttackWithCode(other, scenePlayer, needCheckSiegeLimit).isOk();
    }

    public ErrorCode canBattleWithCode(SceneObjEntity target, boolean needCheckSiegeLimit) {
        if (target == null) {
            return ErrorCode.SYSTEM_TARGET_NULL;
        }
        boolean realNeedCheckSiegeLimit = needCheckSiegeLimitByRally(this, needCheckSiegeLimit);
        return target.canBeAttackWithCode(this, null, realNeedCheckSiegeLimit);
    }

    public ErrorCode canBeAttackWithCode(SceneObjEntity other, AbstractScenePlayerEntity scenePlayer, boolean needCheckSiegeLimit) {
        if (getBattleComponent() == null) {
            return ErrorCode.BATTLE_CANT;
        }
        if (!getBattleComponent().canIBattle()) {
            return ErrorCode.BATTLE_CANT;
        }
        // 和平护盾相关
        CommonEnum.SafeGuardReason reason = checkShieldHasFunction(CommonEnum.SafeGuardFunctionType.SGFT_CANNOT_BE_ATTACK);
        if (reason != CommonEnum.SafeGuardReason.SGR_NONE) {
            if (reason == CommonEnum.SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY) {
                // 如果是受到活动保护，都先返回活动的错误码
                return ErrorCode.SAFE_GUARD_LAND_PROTECT_ACTIVITY_OPEN;
            }
            if (getEntityType() == EntityAttrOuterClass.EntityType.ET_City) {
                // 玩家主堡，理论上现在不会走到这儿了
                return ErrorCode.SPY_IS_SHIELD_ON;
            } else {
                // 非玩家主堡认为是在保护期内
                return ErrorCode.SPY_TARGET_IS_PROTECT;
            }
        }
        // 能不能被SceneObj打
        if (other != null) {
            if (!campAllowBattle(other)) {
                return ErrorCode.BATTLE_CAMP_NOT_ALLOW;
            }
            boolean realNeedCheckSiegeLimit = needCheckSiegeLimitByRally(other, needCheckSiegeLimit);
            return canBeAttackBySceneObj(other, realNeedCheckSiegeLimit);
        }
        // 能不能被Player打
        if (scenePlayer != null) {
            if (!campAllowBattle(scenePlayer)) {
                return ErrorCode.BATTLE_CAMP_NOT_ALLOW;
            }
            return canBeAttackByScenePlayer(scenePlayer, needCheckSiegeLimit);
        }
        return ErrorCode.OK;
    }

    /**
     * 集结不检查合围上限
     */
    private boolean needCheckSiegeLimitByRally(SceneObjEntity attacker, boolean needCheckSiegeLimit) {
        boolean realNeedCheckSiegeLimit = needCheckSiegeLimit;
        if (attacker.getEntityType() == EntityAttrOuterClass.EntityType.ET_Army) {
            ArmyEntity armyEntity = (ArmyEntity) attacker;
            if (armyEntity.isRallyArmy()) {
                realNeedCheckSiegeLimit = false;
            }
        }
        return realNeedCheckSiegeLimit;
    }

    public void setExpression(int expressionId) {

    }

    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    /**
     * 能否被侦查，默认sceneObj不可被侦查（新增可被侦查对象时需要复写本方法）
     */
    public boolean canBeSpy(long enemyPlayerId) {
        return false;
    }

    /**
     * 能否屏蔽侦查，（可以被侦查，但是侦查报告为空）
     */
    public boolean canBlockSpy() {
        return false;
    }

    /**
     * 是否参与围攻
     * bool besiege
     */
    public boolean getBesiege() {
        return false;
    }

    /**
     * 是否可以被集结
     */
    public ErrorCode canBeRallyWithCode() {
        return ErrorCode.RALLY_CANT;
    }

    /**
     * 是否可以被援助
     */
    public Code canBeAssist(long playerId, Camp camp, long soldierNum, long armyId) {
        SceneObjInnerArmyComponent innerArmyComponent = getInnerArmyComponent();
        if (innerArmyComponent == null) {
            return ErrorCode.ASSIST_CANT.getCode();
        }
        return innerArmyComponent.checkArmyCanAssist(playerId, camp, soldierNum, armyId);
    }

    /**
     * 是否可以来采集
     */
    public Code canCollect(long playerId) {
        SceneObjInnerArmyComponent innerArmyComponent = getInnerArmyComponent();
        if (innerArmyComponent == null) {
            return ErrorCode.ASSIST_CANT.getCode();
        }
        return innerArmyComponent.checkArmyCanCollect(playerId);
    }

    /**
     * 获取所在场景层级的最高层
     */
    public int getMaxSceneLayer() {
        SceneObjectEnum sceneObjType = getSceneObjType();
        if (sceneObjType == null) {
            return 1;
        }
        WorldLayerService resService = ResHolder.getResService(WorldLayerService.class);
        int objectLayer = resService.getObjectLayer(sceneObjType);
        if (objectLayer != 0) {
            return objectLayer;
        }
        int regionId = MapGridDataManager.getRegionId(getScene().getMapId(), getCurPoint());
        return resService.getObjectLayer(sceneObjType, regionId);
    }

    public int getLevel() {
        return 0;
    }

    public Int64ArmyArrowItemMapProp getArrowProp() {
        return null;
    }

    public boolean isInClanTerritory() {
        return false;
    }

    // ------------------------- 组件获取 -------------------------

    public SceneObjPropComponent getPropComponent() {
        return propComponent;
    }

    public SceneObjDbComponent getDbComponent() {
        return dbComponent;
    }

    public SceneObjTransformComponent getTransformComponent() {
        return transformComponent;
    }

    public SceneObjAoiComponent getAoiNodeComponent() {
        return aoiNodeComponent;
    }

    public SceneObjArrowComponent getArrowComponent() {
        return arrowComponent;
    }

    public SceneObjMoveComponent getMoveComponent() {
        return null;
    }

    public SceneObjAiComponent getAiComponent() {
        return null;
    }

    public SceneObjSpecialSafeGuardComponent getSpecialSafeGuardComponent() {
        return null;
    }

    public SceneObjInnerArmyComponent getInnerArmyComponent() {
        return null;
    }

    public StructCommonPB.ProgressInfoPB.Builder getProgressInfoPBBuilder() {
        return null;
    }

    public SceneObjHateListComponent getHateListComponent() {
        throw new GeminiException("getHateListComponent: {} not implement", getEntityType());
    }

    public SceneObjSpyComponent getSpyComponent() {
        return spyComponent;
    }

    public SceneObjTimerComponent getTimerComponent() {
        return timerComponent;
    }
    // ------------------------- 容器重写 -------------------------

    @Override
    public int hashCode() {
        return Objects.hash(getEntityId());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        SceneObjEntity entity = (SceneObjEntity) obj;
        return getEntityId() == entity.getEntityId();
    }

    @Override
    public String toString() {
        return super.toString() + " current point:" + getCurPoint();
    }

    public EventDispatcher getEventDispatcher() {
        return eventDispatcher;
    }

    /**
     * 添加后，会每次被tick
     * 适合移动计算，停下来后就remove
     */
    private Set<SceneTickReason> tickReasonSet;
    /**
     * 永远只判断最近一次timer是否到期
     * 适合buff到期计算
     */
    private Set<SceneTimerReason> timerReasonSet;

    public void addTick(SceneTickReason reason) {
        if (!isInitOk()) {
            WechatLog.error("{} add tick not initOk {}", this, reason);
            return;
        }
        if (isDestroy()) {
            LOGGER.warn("{} add tick is destroy {}", this, reason);
            return;
        }
        if (tickReasonSet == null) {
            tickReasonSet = new ObjectOpenHashSet<>();
        }
        if (!tickReasonSet.add(reason)) {
            LOGGER.error("{} repeat add tick {}", this, reason);
            return;
        }
        getScene().getTickMgrComponent().addTick(this, reason);
    }

    public void tryRemoveTick(SceneTickReason reason) {
        if (tickReasonSet == null) {
            return;
        }
        if (!tickReasonSet.contains(reason)) {
            return;
        }
        tickReasonSet.remove(reason);
        getScene().getTickMgrComponent().removeTick(this, reason);
    }

    public void removeTick(SceneTickReason reason) {
        if (tickReasonSet == null) {
            LOGGER.error("{} remove tick but null {}", this, reason);
            return;
        }
        if (!tickReasonSet.remove(reason)) {
            LOGGER.error("{} repeat remove tick {}", this, reason);
            return;
        }
        getScene().getTickMgrComponent().removeTick(this, reason);
    }

    /**
     * 增加场景定时器
     *
     * @param reason 定时器类型
     * @param delay  毫秒
     */
    public void addSceneSchedule(SceneTimerReason reason, long delay) {
        if (!isInitOk()) {
            WechatLog.error("{} add tick not initOk {}", this, reason);
            return;
        }
        if (timerReasonSet == null) {
            timerReasonSet = new ObjectOpenHashSet<>();
        }
        if (!timerReasonSet.add(reason)) {
            LOGGER.error("{} repeat add timer {}", this, reason);
            return;
        }
        getScene().getTickMgrComponent().addSchedule(this, reason, delay);
    }

    public void cancelSceneSchedule(SceneTimerReason reason) {
        if (timerReasonSet == null) {
            LOGGER.error("{} remove timer but null {}", this, reason);
            return;
        }
        if (!timerReasonSet.remove(reason)) {
            LOGGER.error("{} repeat timer but null {}", this, reason);
            return;
        }
        getScene().getTickMgrComponent().cancelSchedule(this, reason);
    }

    public boolean tryCancelSceneSchedule(SceneTimerReason reason) {
        if (timerReasonSet == null) {
            return false;
        }
        if (!timerReasonSet.contains(reason)) {
            return false;
        }
        getScene().getTickMgrComponent().cancelSchedule(this, reason);
        return timerReasonSet.remove(reason);
    }

    private void clearTickAndSchedule() {
        if (tickReasonSet != null) {
            for (SceneTickReason reason : tickReasonSet) {
                getScene().getTickMgrComponent().removeTick(this, reason);
            }
            tickReasonSet = null;
        }
        if (timerReasonSet != null) {
            for (SceneTimerReason reason : timerReasonSet) {
                getScene().getTickMgrComponent().cancelSchedule(this, reason);
            }
            timerReasonSet = null;
        }
    }

    @Override
    public boolean onTimerDispatch(SceneTimerReason reason) {
        timerReasonSet.remove(reason);
        if (isDestroy()) {
            return true;
        }
        if (reason == SceneTimerReason.TIMER_LIFE) {
            onLifeEnd();
            return true;
        }
        return false;
    }

    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        if (isDestroy()) {
            return true;
        }
        switch (reason) {
            case TICK_MOVE:
                getMoveComponent().onTick();
                return true;
            default:
                return false;
        }
    }
}

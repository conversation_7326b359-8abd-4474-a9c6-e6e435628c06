package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.event.battle.EnterNewBattle;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.cnc.scene.sceneclan.rally.component.RallyArmyMgrComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.clan.ClanGiftRecordUtils;
import com.yorha.common.enums.qlog.battle.BattleRepDefenceType;
import com.yorha.common.enums.qlog.battle.BattleRepMainObjType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import res.template.MonsterTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SceneArmyBattleComponent extends ArmyBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(SceneArmyBattleComponent.class);

    public SceneArmyBattleComponent(ArmyEntity parent) {
        super(parent);
    }

    @Override
    protected void initListener() {
        super.initListener();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleStart, EnterNewBattle.class);
    }

    private void onBattleStart(EnterNewBattle event) {
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(event.getTargetId());
        if (sceneObjEntity == null || sceneObjEntity.getEntityType() != event.getEntityType()) {
            LOGGER.error("why sceneObjEntity is fail. event:{} sceneObjEntity:{}", event.getTargetId(), sceneObjEntity);
            return;
        }
        SsPlayerMisc.EnterBattleCmd.Builder cmd = SsPlayerMisc.EnterBattleCmd.newBuilder();
        cmd.setTargetId(event.getTargetId()).setTargetType(event.getEntityType()).setBattleRoleType(event.getBattleRoleType());
        switch (event.getBattleRoleType()) {
            case SOT_MONSTER: {
                MonsterEntity monster = (MonsterEntity) sceneObjEntity;
                cmd.setTemplateId(monster.getTemplate().getId());
                break;
            }
            default: {
            }
        }
        if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
            cmd.setTargetLv(sceneObjEntity.getLevel());
        }
        getOwner().getScenePlayer().tellPlayer(cmd.build());
    }

    @Override
    protected void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        if (getOwner().isRallyArmy()) {
            // 集结体重新计算集体troop数据
            getBattleRole().refreshTroop();
        }
        // 刷新战力
        refreshPower();

        if (!getOwner().isRallyArmy()) {
            return;
        }
        // 同步给集结体，战损同步到面板
        RallyEntity rallyEntity = getOwner().getRallyEntity();
        if (rallyEntity != null) {
            rallyEntity.getArmyMgrComponent().onDamage(event.getDamageResult().getSoldierLossList());
        } else {
            LOGGER.error("getRallyEntity failed. curRallyId: {}  army: {}", getOwner().getRallyComponent().getCurRallyId(), getOwner());
        }
    }

    @Override
    protected void onEndSingleRelationInBigScene(EndSingleBattleEvent event) {
        if (getOwner().isNpcArmy()) {
            return;
        }
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        try {
            // 增加玩家战斗胜利失败的统计
            updateBattleRecordToPlayer(event);
            // 杀死野怪可以加联盟礼物
            tryAddClanGiftOnKillMonster(event.isEnemyDead(), event.getOther());
            // 尝试记录战损
            ((ScenePlayerEntity) getOwner().getScenePlayer()).getLogisticMgrComponent().trySysAssist(event.getRoleRecord(), event.getOther(), event.getBattleType());
        } catch (Exception e) {
            LOGGER.error("onEndSingleRelationInBigScene failed {}", getOwner(), e);
        }
    }

    @Override
    public void onEndSingleRelation(CommonEnum.BattleOverType type, boolean isDead, boolean isEnemyDead, IBattleRoleAdapter other, BattleRecord.RecordOne record, boolean enemyNpc) {
        super.onEndSingleRelation(type, isDead, isEnemyDead, other, record, enemyNpc);
    }

    @Override
    public void afterEndSingleRelation(CommonEnum.BattleOverType type, boolean isDead, boolean isEnemyDead, IBattleRoleAdapter other, BattleRecord.RecordOne record) {
        super.afterEndSingleRelation(type, isDead, isEnemyDead, other, record);
    }


    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
        trySendHospitalFullCauseDeadMail();
        notifyScenePlayerEndAllRelation(battleResult);
    }

    @Override
    public void handleSoldierLoss(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        sendSevereWound2Hospital(lossDetail);
    }

    @Override
    public void sendSevereWound2Hospital(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        Map<Long, List<Struct.PlayerHospitalSoldier>> go2HospitalMap = buildSevereSoldierData(lossDetail);
        if (getOwner().isRallyArmy()) {
            for (Map.Entry<Long, List<Struct.PlayerHospitalSoldier>> entry : go2HospitalMap.entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    ArmyEntity memberArmy = getOwner().getRallyEntity().getArmyMgrComponent().findInRallyArmy(entry.getKey());
                    if (memberArmy == null) {
                        WechatLog.error("childArmy not in rally!!!rally:{} member:{}", getOwner(), entry.getKey());
                    } else {
                        memberArmy.getBattleComponent().goToHospital(entry.getValue(), lossDetail.get(entry.getKey()));
                    }
                }
            }
        } else {
            long roleId = getBattleRole().getRoleId();
            List<Struct.PlayerHospitalSoldier> hospitalData = go2HospitalMap.get(roleId);
            if (CollectionUtils.isNotEmpty(hospitalData)) {
                goToHospital(hospitalData, lossDetail.get(roleId));
            }
        }
    }

    @Override
    public void trySendHospitalFullCauseDeadMail() {
        // 集结体里的子army的邮件在onQuitRally时触发，这里就不作处理了
        if (getOwner().isRallyArmy()) {
            return;
        }
        int deadCount = getOwner().getProp().getBattle().getHospitalFullCauseDeadCount();
        if (deadCount > 0) {
            getOwner().getScenePlayer().getSoldierMgrComponent().sendHospitalFullDeadMail(deadCount);
            getOwner().getProp().getBattle().setHospitalFullCauseDeadCount(0);
        }
    }

    private void tryAddClanGiftOnKillMonster(boolean isEnemyDead, SceneObjBattleComponent other) {
        if (!isEnemyDead) {
            return;
        }
        if (other.getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
            return;
        }
        MonsterEntity monster = (MonsterEntity) other.getOwner();
        final int monsterId = monster.getMonsterId();
        MonsterTemplate monsterTemplate = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, monsterId);
        final int giftItemId = monsterTemplate.getClanGiftId();
        if (giftItemId <= 0) {
            return;
        }
        final long clanId = getOwner().getProp().getClanId();
        if (clanId <= 0) {
            return;
        }
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClan == null) {
            return;
        }
        LOGGER.debug("{} kill {}, addClanGift {} {}", getOwner(), monster, clanId, giftItemId);
        try {
            SsClanGift.AddClanGiftCmd.Builder addGiftCmd = SsClanGift.AddClanGiftCmd.newBuilder();
            addGiftCmd.setGiftItemId(giftItemId);
            if (getOwner().isRallyArmy()) {
                addGiftCmd.setGiftRecord(ClanGiftRecordUtils.formTogetherKillMonsterRecord(this.getPlayerId(), monsterId));
            } else {
                addGiftCmd.setGiftRecord(ClanGiftRecordUtils.formMemberKillMonsterRecord(this.getPlayerId(), getOwner().getScenePlayer().getName(), monsterId));
            }
            sceneClan.tellClan(addGiftCmd.build());
        } catch (Exception e) {
            LOGGER.error("tryAddClanGiftOnKillMonster error", e);
        }
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        QlogCncBattle battleFlow = super.getBattleFlow(alive, isEnemyAlive, record);
        if (null == battleFlow) {
            return null;
        }
        // 一定是非驻防部队
        battleFlow.setDefenceOrNot(BattleRepDefenceType.DEFENCE_NONE.ordinal());
        // 设置战斗主体
        battleFlow.setMainObjectType(BattleRepMainObjType.getMainObjType(getOwner().isRallyArmy()).ordinal())
                .setMainObjectID(String.valueOf(getOwner().getPlayerId()));
        // 设置主体建筑id
        BattleRecord.RoleRecord enemyRole = record.getRecordByOtherRoleId(getRoleId());
        battleFlow.setMainObjectBuildingID("");
        if (BattleRecord.isPlayerCity(enemyRole.getRoleType())) {
            battleFlow.setMainObjectBuildingID(String.valueOf(enemyRole.getRoleId()));
        } else if (BattleRecord.isMapBuilding(enemyRole.getRoleType())) {
            SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(enemyRole.getRoleId());
            if (sceneObjEntity instanceof MapBuildingEntity) {
                battleFlow.setMainObjectBuildingID(String.valueOf(((MapBuildingEntity) sceneObjEntity).getPartId()));
            }
        }
        return battleFlow;
    }

    @Override
    public void beforeEndSingleRelation(CommonEnum.BattleOverType type, IBattleRoleAdapter other, boolean isEnemyDead, BattleRelation relation) {
        // 处理资源损失
        getOwner().getPlunderComponent().handleArmyResourceLoss(getBattleRole(), relation);
    }

    @Override
    public void handlePlunderResult(SsPlayerMisc.PlunderResult plunderResult, BattleRelation relation) {
        // 掠夺资源分配
        if (plunderResult != null) {
            getOwner().getPlunderComponent().handleArmyResourceAllot(plunderResult.getPlunderRes().getDatasMap(), plunderResult.getPlunderAsk().getPlunderWeightMap(), relation);
        }
        trySendBattleRecordAfterPlunder(relation);
    }

    private void refreshPower() {
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        try {
            if (getOwner().isRallyArmy()) {
                RallyArmyMgrComponent armyMgrComponent = getOwner().getRallyEntity().getArmyMgrComponent();
                for (ArmyEntity inRallyArmy : armyMgrComponent.getInRallyArmies()) {
                    ((ScenePlayerEntity) inRallyArmy.getScenePlayer()).getPowerComponent().updatePower(CommonEnum.PowerType.PT_SOLDIER, null);
                }
            } else {
                ((ScenePlayerEntity) getOwner().getScenePlayer()).getPowerComponent().updatePower(CommonEnum.PowerType.PT_SOLDIER, null);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }
}

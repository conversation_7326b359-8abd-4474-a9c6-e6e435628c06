package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.resource.ResCollectService;
import com.yorha.common.utils.MailUtil;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.AttachState;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ResourceTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */
public class AbstractScenePlayerArmyMgrComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerArmyMgrComponent.class);
    /**
     * debug改行军速度用
     */
    private int moveSpeedRatio = 1;
    /**
     * 自己的行军list  遍历请用copy式  防止迭代器异常
     */
    protected final List<ArmyEntity> myArmyList = new ArrayList<>();

    public AbstractScenePlayerArmyMgrComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }


    public int getMoveSpeedRatio() {
        return moveSpeedRatio;
    }

    public void setMoveSpeedRatio(int moveSpeedRatio) {
        this.moveSpeedRatio = moveSpeedRatio;
    }

    public static void onArmyMoveSpeedAdditionChange(AbstractScenePlayerEntity entity) {
        for (ArmyEntity armyEntity : Lists.newArrayList(entity.getArmyMgrComponent().myArmyList)) {
            if (armyEntity.isRallyArmy()) {
                continue;
            }
            armyEntity.getMoveComponent().onSpeedChanged();
        }
    }

    public Set<Long> getSelfArmyAndTargetId() {
        Set<Long> ret = new LongOpenHashSet();
        for (ArmyEntity armyEntity : myArmyList) {
            ret.add(armyEntity.getEntityId());
            ret.addAll(armyEntity.getProp().getBattle().getRelationIdList());
        }
        return ret;
    }

    public StructPlayer.Troop beforeCreateArmy(long armyId, StructPlayer.Troop troopInfo) {
        Struct.Hero mainHero = troopInfo.getMainHero();
        Struct.Hero deputyHero = troopInfo.getDeputyHero();
        Collection<Struct.Soldier> soldierPropCollection = troopInfo.getTroop().getDatasMap().values();
        // 检测士兵
        getOwner().getSoldierMgrComponent().checkSoldierWhenCreateArmy(soldierPropCollection);
        // 扣士兵
        getOwner().getSoldierMgrComponent().subSoldierWhenCreateArmy(armyId, soldierPropCollection);
        // 通知城池 有士兵出去了
        getOwner().getMainCity().getBattleComponent().onArmyOut(soldierPropCollection);
        // 构建部队数据
        StructPlayer.Troop.Builder troopPropBuilder = StructPlayer.Troop.newBuilder();
        troopPropBuilder.getMainHeroBuilder().mergeFrom(mainHero);
        if (deputyHero.getHeroId() > 0) {
            troopPropBuilder.getDeputyHeroBuilder().mergeFrom(deputyHero);
        }
        for (Struct.Soldier soldierAttrData : soldierPropCollection) {
            troopPropBuilder.getTroopBuilder().putDatas(soldierAttrData.getSoldierId(), soldierAttrData);
        }
        return troopPropBuilder.build();
    }

    /**
     * 行军创建或db恢复 都会调用该接口
     */
    public void onNewArmy(ArmyEntity newArmyEntity) {
        for (ArmyEntity armyEntity : myArmyList) {
            if (armyEntity.getEntityId() == newArmyEntity.getEntityId()) {
                LOGGER.error("onNewArmy {} already has {}", getOwner(), newArmyEntity);
                return;
            }
        }
        myArmyList.add(newArmyEntity);
    }

    public void removeArmy(ArmyEntity army) {
        boolean removed = myArmyList.removeIf(armyEntity -> armyEntity.getEntityId() == army.getEntityId());
        if (removed) {
            if (!army.isRallyArmy()) {
                onNormalArmyDelete(army);
            }
            return;
        }
        LOGGER.error("removeArmy not in myArmyList {} {}", getOwner(), army);
    }

    protected void onNormalArmyDelete(ArmyEntity army) {
        LOGGER.info("army delete {} {}", getOwner(), army);
        // 把兵力释放掉
        if (getOwner().getSoldierMgrComponent() != null) {
            getOwner().getSoldierMgrComponent().addSoldierWhenArmyReturn(army);
        }
    }

    /**
     * 同步玩家名字
     */
    public void onSyncPlayerName(String name) {
        for (ArmyEntity armyEntity : myArmyList) {
            armyEntity.getPropComponent().onSyncPlayerName(name);
        }
    }

    public void onSyncPlayerPic(int pic, String picUrl) {
        for (ArmyEntity armyEntity : myArmyList) {
            armyEntity.getPropComponent().onSyncPlayerPic(pic, picUrl);
        }
    }

    public void onSyncPlayerPicFrame(int pic) {
        for (ArmyEntity armyEntity : myArmyList) {
            armyEntity.getPropComponent().onSyncPlayerPicFrame(pic);
        }
    }

    /**
     * 同步联盟名字
     */
    public void onSyncPlayerClan(long clanId, String name) {
        for (ArmyEntity armyEntity : Lists.newArrayList(myArmyList)) {
            // by awei: 这里是为了处理集结行军打架的时候，车头离开联盟了，会导致集结行军找不到RallyEntity，所以不修改集结行军的clanId
            if (armyEntity.isRallyArmy() && clanId != armyEntity.getClanId()) {
                continue;
            }
            armyEntity.getPropComponent().setClanIdName(clanId, name);
        }
    }

    /**
     * 将城外部队瞬时返回
     */
    public void returnAllArmy() {
        for (ArmyEntity armyEntity : Lists.newArrayList(myArmyList)) {
            if (armyEntity.isRallyArmy()) {
                continue;
            }
            armyEntity.getMoveComponent().onReturnCityEnd();
        }
    }

    /**
     * 出征部队数量检测
     */
    public void checkArmyCreate() {

    }

    public int getArmyNumWithoutRally() {
        int cnt = 0;
        for (ArmyEntity armyEntity : myArmyList) {
            if (armyEntity.isRallyArmy()) {
                continue;
            }
            cnt++;
        }
        return cnt;
    }

    public void onCityMove() {
        for (ArmyEntity armyEntity : Lists.newArrayList(myArmyList)) {
            armyEntity.checkOnCityMove();
        }
    }

    public List<ArmyEntity> getMyArmyList() {
        return this.myArmyList;
    }

    public List<Integer> getMyHeroList() {
        List<Integer> heroList = new ArrayList<>();
        for (ArmyEntity armyEntity : this.myArmyList) {
            if (armyEntity.getMainHero().getHeroId() > 0) {
                heroList.add(armyEntity.getMainHero().getHeroId());
            }
            if (armyEntity.getDeputyHero().getHeroId() > 0) {
                heroList.add(armyEntity.getDeputyHero().getHeroId());
            }
        }
        return heroList;
    }


    /**
     * 获取所有城外存活的士兵
     *
     * @param withSlight 是否包含轻伤
     */
    public Map<Integer, Integer> getArmyAliveSoldiers(boolean withSlight) {
        Map<Integer, Integer> ret = new HashMap<>();
        for (ArmyEntity armyEntity : Lists.newArrayList(myArmyList)) {
            // 集结体的army不算上
            if (armyEntity.isRallyArmy()) {
                continue;
            }
            for (Map.Entry<Integer, Integer> entry : armyEntity.getAliveSoldierMap(withSlight).entrySet()) {
                ret.merge(entry.getKey(), entry.getValue(), Integer::sum);
            }
        }
        return ret;
    }

    /**
     * 英雄数据变更，写扩散到外出的army上
     */
    public void spreadBattleUnitChangeToArmy(List<Struct.Hero> heroList) {
        for (ArmyEntity armyEntity : myArmyList) {
            armyEntity.getPropComponent().onHeroPropChange(heroList);
        }
    }

    public boolean checkCanMoveCity() {
        for (ArmyEntity army : myArmyList) {
            // 没在依附状态 不能迁城
            if (army.getProp().getAttachState() == AttachState.AAS_NONE || army.getProp().getAttachState() == AttachState.AAS_RALLY) {
                return false;
            }
        }
        return true;
    }

    /**
     * 发送普通资源田的采集报告
     */
    protected void sendCollectMail(int mailId, ResourceTemplate template, PointProp point, long num, long heroExtraCount, long kingDecCount, long collectTsMs) {
        StructMail.MailSendParams.Builder mailSendParams = getCollectMailParam(mailId, template.getResType(), template.getId(),
                point.setMapId(getOwner().getScene().getMapIdForPoint()).setMapType(getOwner().getScene().getMapType().getNumber()), num, heroExtraCount, kingDecCount, template.getResLevel(), collectTsMs);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    protected StructMail.MailSendParams.Builder getCollectMailParam(int mailId, CommonEnum.CurrencyType resType, int templateId,
                                                                    PointProp point, long num, long heroExtraCount, long kingDecCount,
                                                                    int level, long collectTsMs) {
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        mailSendParams.getContentBuilder().getCollectDataBuilder()
                .setPoint(point.getCopySsBuilder())
                .setCurrencyType(resType)
                .setLastRefreshCollectStamp(collectTsMs)
                .setCount(num)
                .setExtraCount(heroExtraCount)
                .setDecCount(kingDecCount);
        ResCollectService service = ResHolder.getResService(ResCollectService.class);
        if (service.isCollectFromClanRes(templateId)) {
            // 军团资源中心需要额外设置templateId
            mailSendParams.getContentBuilder().getCollectDataBuilder().setTemplateId(templateId);
            // 军团资源中心副标题需要显示军团资源中心名字
            mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, templateId));
        } else {
            // 常规资源点才有等级
            mailSendParams.getContentBuilder().getCollectDataBuilder().setLevel(level);
            // 常规资源点副标题需要显示常规资源点的等级等信息
            mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_RESOURCE, templateId));
        }
        return mailSendParams;
    }
}

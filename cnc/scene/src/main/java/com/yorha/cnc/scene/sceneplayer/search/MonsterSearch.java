package com.yorha.cnc.scene.sceneplayer.search;

import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.map.BigSceneResService;
import com.yorha.proto.CommonEnum;
import res.template.MonsterTemplate;

/**
 * <AUTHOR>
 */
public class MonsterSearch extends ISearch<MonsterEntity> {
    public MonsterSearch(int level) {
        super(level);
    }

    @Override
    public Class<? extends SceneObjEntity> getClazz() {
        return MonsterEntity.class;
    }

    @Override
    public int getSearchRange() {
        return ResHolder.getResService(BigSceneResService.class).getSearchTemplate(this.getLevel()).getSearchRange();
    }

    @Override
    public boolean matching(ScenePlayerEntity player, MonsterEntity entity) {
        MonsterTemplate template = entity.getTemplate();
        // 相同等级的普通活跃野怪
        if (template.getCategory() != CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE) {
            return false;
        }
        if (template.getQuality() != CommonEnum.SceneObjQuality.NORMAL) {
            return false;
        }
        return template.getLevel() == getLevel() && !entity.getBattleComponent().hasTarget();
    }

    @Override
    public String toString() {
        return "MonsterSearch";
    }
}

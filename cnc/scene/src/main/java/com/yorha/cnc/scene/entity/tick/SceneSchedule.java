package com.yorha.cnc.scene.entity.tick;

/**
 * <AUTHOR>
 * <p>
 * 场景上能被tick timer调度的实现方法
 */
public interface SceneSchedule {
    /**
     * timer 触发
     *
     * @param reason 触发枚举
     * @return 是否成功执行
     */
    boolean onTimerDispatch(SceneTimerReason reason);

    /**
     * tick 触发
     *
     * @param reason 触发枚举
     * @return 是否成功执行
     */
    boolean onTickDispatch(SceneTickReason reason);

    boolean isDestroy();
}
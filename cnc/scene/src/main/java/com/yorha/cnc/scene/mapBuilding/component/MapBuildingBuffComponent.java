package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.game.gen.prop.Int32BuffMapProp;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;

/**
 * <AUTHOR>
 */
public class MapBuildingBuffComponent extends SceneObjBuffComponent {
    public MapBuildingBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected Int32BuffMapProp getData() {
        return getOwner().getProp().getBuffSys().getBuff();
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }
}

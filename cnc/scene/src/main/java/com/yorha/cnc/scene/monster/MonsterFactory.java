package com.yorha.cnc.scene.monster;

import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.monsterFeature.*;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.monster.MonsterTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.vector.Vector2i;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.MonsterProp;
import com.yorha.game.gen.prop.MoveProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Core;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

/**
 * <AUTHOR>
 */
public class MonsterFactory {
    private static final Logger LOGGER = LogManager.getLogger(MonsterFactory.class);

    public static MonsterEntity initMonster(SceneEntity scene, int templateId, Point bornPoint, SceneObjSpawnParam param) {
        return initMonster(scene, templateId, bornPoint, param, false);
    }

    public static MonsterEntity initMonster(SceneEntity scene, int templateId, Point bornPoint, SceneObjSpawnParam param, boolean ignoreNumLimit) {
        if (!ignoreNumLimit && scene.isMainScene()) {
            int size = scene.getObjMgrComponent().getObjsNumByType(MonsterEntity.class);
            if (size >= BigSceneConstants.MONSTER_NUM_MAX) {
                WechatLog.error("try createMonster but num is max {}", size);
                return null;
            }
        }
        MonsterTemplate monsterTemplate = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, templateId);
        MonsterProp prop = new MonsterProp();
        prop.setMonsterId(monsterTemplate.getId());
        prop.getCast().setFinish(false);

        MoveProp moveProp = prop.getMove();
        moveProp.getCurPoint().setX(bornPoint.getX());
        moveProp.getCurPoint().setY(bornPoint.getY());
        // 出生静止朝向
        if (param != null && param.getYaw() != null) {
            moveProp.getYaw().setX(param.getYaw().getX()).setY(param.getYaw().getY());
        } else {
            Vector2i bronYaw = ResHolder.getResService(MonsterTemplateService.class).getBronYaw(templateId);
            moveProp.getYaw().setX(bronYaw.getX()).setY(bronYaw.getY());
        }

        int troopIndex = monsterTemplate.getTroopIndex();
        if (troopIndex != 0) {
            StructPlayer.Troop troop = TroopHelper.getTroopBuilder(ResHolder.getInstance(), troopIndex);
            prop.getTroop().mergeFromSs(troop);
            prop.getTroop().setTroopId(troopIndex);
        }

        CommonEnum.Camp camp = CommonEnum.Camp.forNumber(monsterTemplate.getCamp());
        prop.setCamp(camp != null ? camp : CommonEnum.Camp.C_NONE);

        // 设置动态参数
        boolean isBattleSummons = false;
        if (param != null) {
            if (param.getLifeTime() > 0) {
                prop.setLifeTime(param.getLifeTime());
            }
            if (param.getCreateType() != null) {
                prop.setCreateType(param.getCreateType());
            }
            if (param.getSummonPlayerId() > 0) {
                prop.setSummonPlayerId(param.getSummonPlayerId());
            }
            isBattleSummons = param.isBattleSummons();
        }
        if (prop.getCreateType() == null) {
            prop.setCreateType(CommonEnum.MonsterCreateType.SPAWN);
        }

        long objId = scene.ownerActor().nextId();
        // 默认活跃野怪的特性，需要使用
        MonsterFeature feature;
        switch (monsterTemplate.getCategory()) {
            case BUILDING_GUARD: {
                feature = new GuardMonster();
                break;
            }
            case ACT_LUOHA: {
                feature = new LuohaMonster();
                break;
            }
            case SKYNET_MONSTER: {
                feature = new SkyMonster();
                break;
            }
            case BIG_SCENE_ACTIVE: {
                feature = new NormalActiveMonster();
                break;
            }
            case RALLY_MONSTER: {
                feature = new RallyMonster();
                break;
            }
            case BIG_SCENE_QUEST:
            case BIG_SCENE_BOSS:
            case NEWBIE_MONSTER:
            case DUNGEON_MONSTER: {
                feature = new DefaultMonster();
                break;
            }
            default:
                feature = new DefaultMonster();
                LOGGER.error("MonsterFactory initMonster unknown category={} monsterTemplate={}", monsterTemplate.getCategory(), monsterTemplate.getId());
        }
        MonsterBuilder monsterBuilder = new MonsterBuilder(scene, objId, prop, isBattleSummons, feature);
        return new MonsterEntity(monsterBuilder);
    }

    /**
     * 随机野怪刷新点
     */
    public static Point randomBornPoint(SceneEntity scene, int templateId, int regionId, CommonEnum.MapAreaType areaType) {
        // 迁城搜索半径
        int selfRadius = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, templateId).getBlockRadius();
        for (int i = 0; i < 10; i++) {
            Point point = MapGridDataManager.getRandomBornPoint(scene.getMapId(), regionId, areaType);
            if (point == null) {
                //LOGGER.warn("randomBornPoint fail no born {} {} {}", templateId, regionId, areaType);
                continue;
            }
            Core.Code code = BornPointHelper.collisionCheck(scene, point.getX(), point.getY(), selfRadius, 0);
            if (!ErrorCode.isOK(code)) {
                //LOGGER.warn("randomBornPoint fail collision {} {} {}", templateId, regionId, point);
                continue;
            }
            return point;
        }
        return null;
    }

    /**
     * 随机野怪刷新点
     */
    public static Point randomBornPoint(SceneEntity scene, int templateId, Shape shape, boolean isUseCityCollision) {
        // 迁城搜索半径
        int selfRadius = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, templateId).getBlockRadius();
        for (int i = 0; i < 10; i++) {
            Point point = shape.getRandomPoint();
            if (point == null) {
                continue;
            }
            Core.Code code = BornPointHelper.collisionCheck(scene, point.getX(), point.getY(), selfRadius, 0, isUseCityCollision);
            if (!ErrorCode.isOK(code)) {
                continue;
            }
            return point;
        }
        return null;
    }
}

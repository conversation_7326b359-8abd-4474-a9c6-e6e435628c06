package com.yorha.cnc.zone.activity;

import com.yorha.proto.CommonEnum;


public class ZoneActivityLandProtectionUnit extends BaseZoneActivityUnit {

    public ZoneActivityLandProtectionUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    @Override
    public void load(boolean isInitial) {
    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }
}

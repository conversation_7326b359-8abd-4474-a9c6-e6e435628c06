package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.battle.skill.SkillResult;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.common.constant.BattleConstants;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 临时给客户端测试协议解析用的
 *
 * <AUTHOR>
 */
public class SendManySkillFire implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int count = 100;
        String key = "count";
        if (args.containsKey(key)) {
            count = Integer.parseInt(args.get(key));
        }
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        CityEntity mainCity = scenePlayer.getMainCity();
        for (int i = 0; i < count; i++) {
            SkillResult skillResult = new SkillResult();
            skillResult.setFireType(CommonEnum.SkillFireType.SFT_FIRE);
            skillResult.setAttackerId(mainCity.getEntityId());
            skillResult.setSkillId(1003000101);
            skillResult.setHeroId(10001);
//            skillResult.addEffect(StructPB.EffectDTOPB.newBuilder()
//                    .setAttackerId(mainCity.getEntityId())
//                    .setTargetId(mainCity.getEntityId())
//                    .setValue(100)
//                    .setType(CommonEnum.SkillEffectType.SET_DAMAGE).build());
            mainCity.getBattleComponent().broadcastSkillResult(skillResult, BattleConstants.BattleBroadCastNtfReason.TEST);
        }
    }

    @Override
    public String showHelp() {
        return "SendManySkillFire count={value}";
    }
}

package com.yorha.cnc.scene.event.ievent;

import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * 带entityId的
 *
 * <AUTHOR>
 */
public class IEventWithEntityId extends IEvent {
    private final long entityId;

    public IEventWithEntityId(long entityId) {
        this.entityId = entityId;
    }

    public long getEntityId() {
        return entityId;
    }

    @Override
    public String toString() {
        return ClassNameCacheUtils.getSimpleName(getClass()) +
                "{entityId=" + entityId +
                '}';
    }
}

package com.yorha.cnc.scene.spyPlane;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.WarningInfoType;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.scene.spyPlane.component.SpyPlaneAdditionComponent;
import com.yorha.cnc.scene.spyPlane.component.SpyPlaneBehaviourComponent;
import com.yorha.cnc.scene.spyPlane.component.SpyPlaneMoveComponent;
import com.yorha.cnc.scene.spyPlane.component.SpyPlaneTransformComponent;
import com.yorha.game.gen.prop.SpyPlaneProp;
import com.yorha.proto.*;
import com.yorha.proto.EntityAttrOuterClass.EntityType;

/**
 * <AUTHOR>
 */
public class SpyPlaneEntity extends SceneObjEntity {
    private final SpyPlaneProp prop;
    private final SpyPlaneMoveComponent spyPlaneMoveComponent = new SpyPlaneMoveComponent(this);
    private final SpyPlaneBehaviourComponent spyPlaneBehaviourComponent = new SpyPlaneBehaviourComponent(this);
    private final SceneObjAdditionComponent additionComponent = new SpyPlaneAdditionComponent(this);
    private final AbstractScenePlayerEntity player;

    public SpyPlaneEntity(SpyPlaneBuilder builder) {
        this(builder, false);
    }

    public SpyPlaneEntity(SpyPlaneBuilder builder, boolean isRestore) {
        super(builder);
        this.prop = builder.getProp();
        player = getScene().getPlayerMgrComponent().getScenePlayer(getPlayerId());
        initAllComponents();
        getPropComponent().initPropListener(isRestore);
        // 设置拥有者永远同步
        getAoiNodeComponent().setAlwaysSyncPlayer(getPlayerId());
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_SpyPlane;
    }

    @Override
    public boolean onTimerDispatch(SceneTimerReason reason) {
        if (super.onTimerDispatch(reason)) {
            return true;
        }
        if (reason == SceneTimerReason.TIMER_SURVEY) {
            getSpyPlaneBehaviourComponent().finishSurvey();
            return true;
        }
        if (reason == SceneTimerReason.TIMER_EXPLORE) {
            getSpyPlaneBehaviourComponent().moveToNextExplorePoint(0);
            return true;
        }
        return false;
    }

    @Override
    public SpyPlaneProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getSpyPlaneBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getSpyPlaneBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getSpyPlaneBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getSpyPlaneBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getSpyPlaneBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final SpyPlaneProp spyPlaneProp = SpyPlaneProp.of(builder.getFullAttr().getSpyPlane(), builder.getChangedAttr().getSpyPlane());
        return EntityAttrDb.EntityAttrDB.newBuilder().setSpyPlane(spyPlaneProp.getCopyDbBuilder());
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return null;
    }

    @Override
    public CommonEnum.SceneObjectEnum getSceneObjType() {
        return CommonEnum.SceneObjectEnum.SOE_SPYPLANE;
    }

    @Override
    public long getPlayerId() {
        return prop.getOwnerId();
    }

    public AbstractScenePlayerEntity getPlayer() {
        return player;
    }

    @Override
    public long getClanId() {
        return getPlayer().getClanId();
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return CommonEnum.Camp.C_NONE;
    }

    @Override
    public int getZoneId() {
        return getProp().getZoneId();
    }

    @Override
    public void deleteObj() {
        if (isDestroy()) {
            return;
        }
        player.getPlaneComponent().removeAttentionSpy(getEntityId());
        getDbComponent().deleteDb();
        getAoiNodeComponent().clearAlwaysSyncPlayer();
        // 同步player侧释放侦察机
        long playerPlaneId = getProp().getPlayerPlaneId();
        SsPlayerMisc.ReleasePlaneCmd cmd = SsPlayerMisc.ReleasePlaneCmd.newBuilder().setSpyPlaneId(playerPlaneId).build();
        player.getPlaneComponent().releasePlayerPlane(cmd);
        super.deleteObj();
    }

    @Override
    public CommonEnum.SceneObjectNtfReason getRemoveReason() {
        return CommonEnum.SceneObjectNtfReason.SONR_RETURN_CITY;
    }

    @Override
    public SpyPlaneMoveComponent getMoveComponent() {
        return spyPlaneMoveComponent;
    }

    public SpyPlaneBehaviourComponent getSpyPlaneBehaviourComponent() {
        return spyPlaneBehaviourComponent;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return null;
    }

    @Override
    public SpyPlaneTransformComponent getTransformComponent() {
        return (SpyPlaneTransformComponent) super.getTransformComponent();
    }

    public void setOwnerName(String name) {
        getProp().setOwnerName(name);
    }

    public void setClanName(String name) {
        getProp().setBriefClanName(name);
    }

    public void setClanId(long clanId) {
        getProp().setClanId(clanId);
    }

    public void setTemplateId(int templateId) {
        getProp().setTemplateId(templateId);
    }

    public long getTargetPlayerId() {
        return getProp().getTargetPlayerId();
    }

    public long getTargetId() {
        return getProp().getTargetId();
    }

    /**
     * copy到预警界面的pb
     */
    @Override
    public boolean copyToWarningInfo(StructPlayerPB.WarningInfoPB.Builder builder) {
        builder.setArmyId(getEntityId())
                .setPlayerName(getProp().getOwnerName())
                .setClanSimpleName(getProp().getBriefClanName())
                .setSpyType(getProp().getSpyType())
                .setTargetId(getTargetId())
                .setArriveTs(getMoveComponent().getMoveArriveTime());
        long curChaseTargetId = getMoveComponent().getCurChaseTargetId();
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntity(curChaseTargetId);
        if (target == null) {
            return false;
        }
        builder.setEntityType(target.getEntityType().getNumber()).setTargetId(curChaseTargetId);
        if (target instanceof WarningInfoType) {
            ((WarningInfoType) target).formWarningInfo(builder);
        }
        return true;
    }

    public void setTargetPlayerId(long playerId) {
        getProp().setTargetPlayerId(playerId);
    }

    public int getTemplate() {
        return prop.getTemplateId();
    }
}

package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RecycleAllArmy implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        List<ArmyEntity> objsByType = actor.getScene().getObjMgrComponent().getObjsByType(ArmyEntity.class);
        for (ArmyEntity armyEntity : new ArrayList<>(objsByType)) {
            armyEntity.getMoveComponent().onReturnCityEnd();
        }
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }
}

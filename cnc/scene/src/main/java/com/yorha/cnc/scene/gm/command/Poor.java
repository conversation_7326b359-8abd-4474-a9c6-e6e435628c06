package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.proto.CommonEnum.DebugGroup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 一键去除 所有士兵
 *
 * <AUTHOR>
 */
public class Poor implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(Poor.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        SceneEntity sceneEntity = actor.getScene();
        LOGGER.debug("{} try poor", playerId);
        AbstractScenePlayerEntity scenePlayer = sceneEntity.getPlayerMgrComponent().getScenePlayer(playerId);
        scenePlayer.getSoldierMgrComponent().removeAllSoldier();
    }

    @Override
    public String showHelp() {
        return "Poor";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_PLAYER;
    }
}
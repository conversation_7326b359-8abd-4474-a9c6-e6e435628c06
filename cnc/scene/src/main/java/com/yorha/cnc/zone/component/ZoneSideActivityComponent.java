package com.yorha.cnc.zone.component;

import com.google.common.collect.ImmutableMap;
import com.yorha.cnc.zone.activity.*;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.server.ZoneContext;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneActivity;

import java.util.Map;

import static com.yorha.proto.CommonEnum.ActivityUnitType;

/**
 * zone上的活动manager
 * <p>
 * 管理多玩家一同参与的内容，比如排行榜、合作任务等等
 * <p>
 * 和个人活动共享配置
 */
public class ZoneSideActivityComponent extends AbsZoneSideActivityComponent<ZoneEntity> {

    static Map<ActivityUnitType, ZoneUnitConstructor> ZONE_UNIT_FACTORY;

    static {
        ZONE_UNIT_FACTORY = ImmutableMap.<ActivityUnitType, ZoneUnitConstructor>builder()
                .put(ActivityUnitType.AUT_HISTORY_POWER_MAX, (owner, template) -> new ZoneActivityRankUnit(owner, template.getId(), ActivityUnitType.AUT_HISTORY_POWER_MAX, template.getId()))
                .put(ActivityUnitType.AUT_ZLCB_RANK, (owner, template) -> new ZoneActivityRankUnit(owner, template.getId(), ActivityUnitType.AUT_ZLCB_RANK, template.getId()))
                .put(ActivityUnitType.AUT_LAND_PROTECTION, (owner, template) -> new ZoneActivityLandProtectionUnit(owner, template.getId(), ActivityUnitType.AUT_LAND_PROTECTION, template.getId()))
                .put(ActivityUnitType.AUT_SCORE_RANK, (owner, template) -> new ZoneActivityScoreRankUnit(owner, template.getId(), ActivityUnitType.AUT_SCORE_RANK, template.getId()))
                .put(ActivityUnitType.AUT_BEST_COMMANDER_SUB_RANK, (owner, template) -> new ZoneBestCommanderSubRankUnit(owner, template.getId(), ActivityUnitType.AUT_BEST_COMMANDER_SUB_RANK, template.getId()))
                .put(ActivityUnitType.AUT_BEST_COMMANDER_TOTAL_RANK, (owner, template) -> new ZoneBestCommanderTotalRankUnit(owner, template.getId(), ActivityUnitType.AUT_BEST_COMMANDER_TOTAL_RANK, template.getId()))
                .put(ActivityUnitType.AUT_BEST_COMMANDER_SEASON, (owner, template) -> new ZoneBestCommanderUnit(owner, template.getId(), ActivityUnitType.AUT_BEST_COMMANDER_SEASON, template.getId()))
                .put(ActivityUnitType.AUT_LOTTERY, (owner, template) -> new ZoneLotteryUnit(owner, template.getId(), ActivityUnitType.AUT_LOTTERY, template.getId()))
                .build();
    }

    public ZoneSideActivityComponent(ZoneEntity entity) {
        super(entity, entity);
    }

    @Override
    protected boolean isServerOpen() {
        return ZoneContext.isServerOpen();
    }

    @Override
    protected void informSceneActivityEffect(CommonEnum.ZoneActivityEffect zoneActivityEffect, long expireTsMs) {
        SsSceneActivity.EnableActivityEffectAsk.Builder ask = SsSceneActivity.EnableActivityEffectAsk.newBuilder();
        ask.setActivityEffect(zoneActivityEffect);
        ask.setExpireTsMs(expireTsMs);
        getIZone().ownerActor().tellBigScene(getIZone().getZoneId(), ask.build());
    }
}


package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum.DebugGroup;
import com.yorha.proto.CommonEnum.OccupyState;

import java.util.Map;

public class StartMapBuildingFire implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int partId = 0;
        String key = "partId";
        if (args.containsKey(key)) {
            partId = Integer.parseInt(args.get(key));
        }
        SceneEntity scene = actor.getScene();
        SceneClanEntity sceneClan = scene.getPlayerMgrComponent().getScenePlayer(playerId).getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        MapBuildingEntity mapBuilding = scene.getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            throw new GeminiException(ErrorCode.MAP_BUILDING_NOT_EXISTS);
        }
        // 只能对已经建成的建筑或正在建的建筑着火
        OccupyState state = mapBuilding.getStageMgrComponent().getState();
        if (state != OccupyState.TOS_REBUILD && state != OccupyState.TOS_AFTER_REBUILD) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_IN_WRONG_STATE);
        }
        mapBuilding.getStageMgrComponent().onAttackSuccess(0L);
    }

    @Override
    public String showHelp() {
        return "StartMapBuildingFire partId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAPBUILDING;
    }
}

package com.yorha.cnc.scene.milestone;

import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.proto.CommonEnum;

/**
 * 里程碑任务接口
 */
public interface IMileStoneTaskHandler {

    /**
     * 更新里程碑任务进度
     */
    void updateProcess(MileStoneTaskData data);

    /**
     * 记录排行信息
     */
    void recordRankData(MileStoneTaskData data);

    /**
     * 检测任务完成
     */
    boolean checkFin();

    /**
     * 发奖范围
     */
    CommonEnum.MileStoneRewardRange getRewardRange();

    /**
     * 任务类型
     */
    CommonEnum.MileStoneTaskType getMileStoneTaskType();

    /**
     * 结算类型
     */
    CommonEnum.MileStoneEndType getMileStoneEndType();

    /**
     * 任务统计类型
     */
    CommonEnum.MileStoneStatisticType getMileStoneStatisticType();

}

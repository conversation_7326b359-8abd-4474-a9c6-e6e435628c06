package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.hateList.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;

import static com.yorha.common.constant.GameLogicConstants.DEFAULT_HATE_EXPIRE;
import static com.yorha.common.constant.LogConstant.LOG_TYPE_AI;

/**
 * <AUTHOR>
 */
public class SceneObjHateListComponent extends SceneObjComponent<SceneObjEntity> implements IHateList {
    private static final Logger LOGGER = LogManager.getLogger(LOG_TYPE_AI);
    private AbstractHateList hateList;

    public SceneObjHateListComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public long getMostHateEntity() {
        return hateList.getMostHateEntity();
    }

    @Override
    public Collection<Long> getHateEntities() {
        return hateList.getHateEntities();
    }

    @Override
    public long getHate(long entityId) {
        return hateList.getHate(entityId);
    }

    @Override
    public HateInfo getHateInfo(long entityId) {
        return hateList.getHateInfo(entityId);
    }

    @Override
    public void addHate(long entityId, long hate) {
        this.hateList.addHate(entityId, hate);
    }

    @Override
    public void addFixHate(long entityId, long hate) {
        this.hateList.addFixHate(entityId, hate);
    }

    @Override
    public void clearHate(long entityId) {
        hateList.clearHate(entityId);
    }

    @Override
    public void clear() {
        if (this.hateList == null) {
            return;
        }
        this.hateList.clear();
    }

    @Override
    public void onTick() {
        hateList.onTick();
    }

    public SceneObjEntity mostHateEntity() {
        long hateEntity = getMostHateEntity();
        if (hateEntity > 0) {
            SceneEntity scene = getOwner().getScene();
            if (scene != null && !scene.isDestroy()) {
                SceneObjEntity target = scene.getObjMgrComponent().getSceneObjEntity(hateEntity);
                if (target != null && !target.isDestroy() && getOwner().canBattle(target, true)) {
                    return target;
                }
            }
        }

        return null;
    }

    @Override
    public void init() {
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndRoundEvent, BattleRoleSettleRoundEvent.class);
        final CommonEnum.HateListType hateListType = getHateListType();
        switch (hateListType) {
            case HLT_DEFAULT:
                this.hateList = new DefaultHateList(getOwner());
                break;
            case HLT_FIRST_BLOOD:
                this.hateList = new FirstBloodHateList(getOwner());
                break;
            default:
                throw new GeminiException("unsupported hateList type");
        }
        if (getOwner().getAiComponent().isDebugAble()) {
            LOGGER.info("{} {} {} init with hateList type {}", getOwner().getAiComponent().getLogHead(), getEntityId(), getEntityType(), hateListType);
        }
    }

    protected CommonEnum.HateListType getHateListType() {
        return CommonEnum.HateListType.HLT_DEFAULT;
    }

    protected void onEndRoundEvent(BattleRoleSettleRoundEvent event) {
        if (getOwner().getAiComponent() == null) {
            return;
        }
        event.getDamageResult().getSoldierLossSource().forEach((attackerBattleRoleId, lossData) ->
                addHate(attackerBattleRoleId, lossData.totalLoss())
        );
    }

    @Override
    public void setLockHateTarget(long lockHateTarget) {
        this.hateList.setLockHateTarget(lockHateTarget);
    }

    @Override
    public int getHateExpire() {
        return DEFAULT_HATE_EXPIRE;
    }
}

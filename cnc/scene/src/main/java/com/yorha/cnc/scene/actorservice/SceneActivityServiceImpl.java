package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.common.actor.SceneActivityService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.proto.SsSceneActivity;

public class SceneActivityServiceImpl implements SceneActivityService {

    private final SceneActor sceneActor;

    public SceneActivityServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    @Override
    public void handleEnableActivityEffectAsk(SsSceneActivity.EnableActivityEffectAsk ask) {
        sceneActor.getBigScene().getZoneEntity().getActivityComponent().addActivityEffect(ask.getActivityEffect(), ask.getExpireTsMs());

        ActorMsgEnvelope currentEnvelope = sceneActor.getCurrentEnvelope();
        if (currentEnvelope != null && !currentEnvelope.isByTell()) {
            this.sceneActor.answer(SsSceneActivity.EnableActivityEffectAns.getDefaultInstance());
        }
    }
}

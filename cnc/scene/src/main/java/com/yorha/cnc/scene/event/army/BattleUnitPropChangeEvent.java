package com.yorha.cnc.scene.event.army;

import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.proto.Struct;

import java.util.List;

/**
 * 战斗单位属性变更了
 *
 * <AUTHOR>
 */
public class BattleUnitPropChangeEvent extends IEvent {

    private final List<Struct.Hero> heroList;


    public BattleUnitPropChangeEvent(List<Struct.Hero> heroList) {
        this.heroList = heroList;
    }

    public List<Struct.Hero> getHeroList() {
        return heroList;
    }
}

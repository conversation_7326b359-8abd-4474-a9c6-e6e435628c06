package com.yorha.cnc.zone.zone.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.addition.ZoneInfoAdditionMgr;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/5/9
 */
public class ZoneAdditionComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneAdditionComponent.class);

    private ZoneInfoAdditionMgr additionMgr;

    public ZoneAdditionComponent(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        additionMgr = new ZoneInfoAdditionMgr(getOwner());
        additionMgr.register(getOwner().getDevBuffComponent());
    }

    public void onSceneOk() {
        refreshAllAddition();
    }

    public void refreshAllAddition() {
        getOwner().getDevBuffComponent().refreshAdditionCache();
        additionMgr.refreshAllAddition();
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionId   加成id
     */
    public void updateAddition(AdditionProviderType providerType, int additionId) {
        if (additionId > 0) {
            additionMgr.updateAdditionByProvider(providerType, Lists.newArrayList(additionId));
        } else {
            LOGGER.error("{}, updateAddition, providerType:{}, additionId:{} invalid", getOwner(), providerType, additionId);
        }
    }

    public long getAddition(int additionId) {
        if (!AdditionUtil.isSceneAddition(additionId)) {
            WechatLog.error("{} getAddition failed in scene. id:{}", getOwner(), additionId);
        }
        return additionMgr.getAddition(additionId);
    }

    public void copyAllSceneAddition(Map<CommonEnum.BuffEffectType, Long> ret) {
        for (Map.Entry<Integer, AdditionProp> entry : getAdditions().getAddition().entrySet()) {
            if (entry.getValue().getTotalValue() != 0 && AdditionUtil.isSceneAddition(entry.getKey())) {
                CommonEnum.BuffEffectType effectType = CommonEnum.BuffEffectType.forNumber(entry.getKey());
                ret.put(effectType, ret.getOrDefault(effectType, 0L) + entry.getValue().getTotalValue());
            }
        }
    }

    public AdditionSysProp getAllSceneAdditionDeepCopy() {
        AdditionSysProp additionSysProp = new AdditionSysProp();
        for (Map.Entry<Integer, AdditionProp> entry : getAdditions().getAddition().entrySet()) {
            if (AdditionUtil.isSceneAddition(entry.getKey())) {
                AdditionProp additionProp = new AdditionProp();
                additionProp.mergeFromSs(entry.getValue().getCopySsBuilder().build());
                additionSysProp.getAddition().put(entry.getKey(), additionProp);
            }
        }
        return additionSysProp;
    }

    public AdditionSysProp getAllPlayerAdditions() {
        return additionMgr.getAllPlayerAdditions();
    }

    public AdditionSysProp getAdditions() {
        return additionMgr.getAdditionSys();
    }
}

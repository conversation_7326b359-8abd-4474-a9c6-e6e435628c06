package com.yorha.cnc.scene.spyPlane;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstSpyService;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.SpyPlaneProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.SsScenePlane;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SpyPlaneTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.yorha.proto.CommonEnum.SpyPlaneActionType.SPAT_EXPLORE;

/**
 * <AUTHOR>
 */
public class SpyPlaneFactory {
    private static final Logger LOGGER = LogManager.getLogger(SpyPlaneFactory.class);

    public static SpyPlaneEntity createSpyPlaneEntity(SceneEntity scene, long scenePlayerId, SsScenePlane.CreateSpyPlaneAsk ask) {
        LOGGER.info("SpyPlaneFactory createSpyPlaneEntity {} {}", scenePlayerId, ask);
        AbstractScenePlayerEntity player = scene.getPlayerMgrComponent().getScenePlayer(scenePlayerId);
        if (player == null) {
            //玩家不存在
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL);
        }
        //配置不存在id
        int templateId = ask.getSpyInfo().getSpyModel();
        if (templateId <= 0) {
            //抛异常
            throw new GeminiException(ErrorCode.PLANE_NEED_UNLOCK_AIRPORT);
        }

        SpyPlaneProp prop = new SpyPlaneProp();
        prop.setOwnerId(scenePlayerId)
                .setState(CommonEnum.SpyPlaneState.SPS_ANI_TAKE_OFF)
                .setClanId(player.getClanId())
                .setZoneId(player.getZoneId())
                .setOwnerName(player.getName())
                .setCamp(player.getCampEnum())
                .setAction(ask.getSpyInfo().getActionType())
                .setFinishNeedReturn(ask.getSpyInfo().getFinishNeedReturn())
                .setTemplateId(templateId)
                .setTargetId(ask.getSpyInfo().getTargetId())
                .setPlayerPlaneId(ask.getSpyInfo().getSpyPlaneId());

        Point askPoint = null;
        Point sPoint = null;
        if (ask.getSpyInfo().hasPoint()) {
            StructPB.PointPB point = ask.getSpyInfo().getPoint();
            askPoint = Point.valueOf(point.getX(), point.getY());
        }
        if (ask.getSpyInfo().hasSrcPoint()) {
            StructPB.PointPB point = ask.getSpyInfo().getSrcPoint();
            sPoint = Point.valueOf(point.getX(), point.getY());
        }
        SceneObjEntity sceneObjEntity = null;
        if (ask.getSpyInfo().getTargetId() > 0) {
            sceneObjEntity = scene.getObjMgrComponent().getSceneObjEntity(ask.getSpyInfo().getTargetId());
            if (sceneObjEntity != null) {
                askPoint = sceneObjEntity.getCurPoint();
                sPoint = sceneObjEntity.getCurPoint();
                prop.setTargetPlayerId(sceneObjEntity.getPlayerId());
            }
        }
        if (askPoint == null) {
            //抛异常
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "askPoint is null");
        }

        if (sPoint == null) {
            //抛异常
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "sPoint is null");
        }

        if (player.getPlaneComponent().haveSpyPlane(prop.getPlayerPlaneId())) {
            //抛异常
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "haveSpyPlane prop.getPlayerPlaneId()");
        }

        Point point = Objects.requireNonNull(player.getMainCity()).getCurPoint();
        Point bornPoint = Point.getPointWithDisToSrcPoint(Point.valueOf(point.getX(), point.getY()), sPoint
                , player.getMainCity().getTransformComponent().getPathCollisionRadius());
        prop.getMove().getCurPoint().setX(bornPoint.getX()).setY(bornPoint.getY());
        int x = sPoint.getX() - bornPoint.getX();
        int y = sPoint.getY() - bornPoint.getY();
        prop.getMove().getYaw().setX(x).setY(y);
        if (player.getSceneClan() != null) {
            prop.setBriefClanName(player.getSceneClan().getClanSimpleName());
        }
        MoveData path = null;
        if (sceneObjEntity != null) {
            path = player.getScene().getPathFindMgrComponent().searchPrePath(player.getMainCity(), bornPoint, sceneObjEntity.getCurPoint(), GameLogicConstants.AIRPORT_MOVE);
        }

        SpyPlaneBuilder builder = new SpyPlaneBuilder(scene, prop.getPlayerPlaneId(), prop);
        SpyPlaneEntity spyPlaneEntity = new SpyPlaneEntity(builder);
        List<StructPB.PointPB> pointList = null;
        if (prop.getAction() == SPAT_EXPLORE) {
            SpyPlaneTemplate template = ResHolder.getInstance().getValueFromMap(SpyPlaneTemplate.class, ask.getSpyInfo().getSpyModel());
            pointList = getFlyLine(askPoint, template.getExploreNum(), sPoint, scene.getMapId());
        }

        // 升空
        try {
            spyPlaneEntity.getSpyPlaneBehaviourComponent().checkAction(prop.getAction(), pointList, ask.getSpyInfo().getTargetId(), path);
            if (scene.isMainScene()) {
                spyPlaneEntity.getDbComponent().insertIntoDb();
            }
            spyPlaneEntity.addIntoScene();
        } catch (Exception e) {
            spyPlaneEntity.getSpyPlaneBehaviourComponent().onReturnCityEnd("create fail");
            throw e;
        }

        // 出发
        List<StructPB.PointPB> finalPointList = pointList;
        MoveData finalPath = path;
        spyPlaneEntity.getTimerComponent().addTimer(TimerReasonType.ANIMATION_TAKE_OFF, () -> {
            try {
                spyPlaneEntity.getSpyPlaneBehaviourComponent().changeAction(prop.getAction(), finalPointList, ask.getSpyInfo().getTargetId(), finalPath);
            } catch (Exception e) {
                spyPlaneEntity.getSpyPlaneBehaviourComponent().onReturnCityEnd("changeAction fail");
                throw e;
            }

        }, ResHolder.getResService(ConstSpyService.class).getTemplate().getAniTakeOffMs(), TimeUnit.MILLISECONDS);

        return spyPlaneEntity;
    }

    /**
     * 获取飞行路线(螺旋矩阵)
     *
     * @param leftPoint
     * @param edgeNum
     * @param sPoint
     * @return
     */
    public static List<StructPB.PointPB> getFlyLine(Point leftPoint, int edgeNum, Point sPoint, int map) {
        List<StructPB.PointPB> res = new ArrayList<>();
        
        return res;
    }

    public static void restoreSpyPlane(SceneEntity scene, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        SpyPlaneProp spyPlaneProp = SpyPlaneProp.of(fullAttr.getSpyPlane(), changedAttr.getSpyPlane());
        SpyPlaneBuilder builder = new SpyPlaneBuilder(scene, fullAttr.getEntityId(), spyPlaneProp);
        SpyPlaneEntity plane = new SpyPlaneEntity(builder, true);
        plane.addIntoScene();
        plane.getSpyPlaneBehaviourComponent().onReturnCityEnd("restore");
        LOGGER.info("{} restore {} success", scene, plane);
    }

    /**
     * 判断是否需要进入战斗狂热状态
     *
     * @param scene
     * @param actionType
     * @param targetId
     * @param clanId
     * @return
     */
    public static boolean needEnterWarFrenzyState(SceneEntity scene, CommonEnum.SpyPlaneActionType actionType, long targetId, long clanId) {
        if (actionType != CommonEnum.SpyPlaneActionType.SPAT_SPY) {
            return false;
        }
        if (targetId <= 0) {
            return false;
        }
        SceneObjEntity targetEntity = scene.getObjMgrComponent().getSceneObjEntity(targetId);
        if (targetEntity != null && targetEntity.getBattleComponent() != null) {
            return targetEntity.getBattleComponent().isEnemyPlayer(clanId);
        }
        return false;
    }
}

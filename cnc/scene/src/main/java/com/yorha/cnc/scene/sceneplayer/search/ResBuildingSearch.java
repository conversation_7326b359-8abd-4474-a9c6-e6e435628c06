package com.yorha.cnc.scene.sceneplayer.search;

import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.CurrencyType;
import res.template.ConstCollectTemplate;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ResBuildingSearch extends ISearch<ResBuildingEntity> {
    public final CurrencyType type;

    public ResBuildingSearch(int level, CurrencyType type) {
        super(level);
        this.type = type;
    }

    @Override
    public Class<? extends SceneObjEntity> getClazz() {
        return ResBuildingEntity.class;
    }

    @Override
    public int getSearchRange() {
        return ResHolder.getInstance().getConstTemplate(ConstCollectTemplate.class).getSearchDistance();
    }

    @Override
    public boolean matching(ScenePlayerEntity player, ResBuildingEntity entity) {
        // 有人在采集
        if (entity.getState() != CommonEnum.ResourceBuildingState.RBS_IDLE) {
            return false;
        }
        // 类型不同
        if (entity.getTemplate().getResType() != getCurrencyType()) {
            return false;
        }
        // 等级不符
        if (entity.getTemplate().getResLevel() < getLevel()) {
            return false;
        }
        // 自己正在前往/采集的
        if (entity.getCollectComponent().inInOrMoving(player.getEntityId())) {
            return false;
        }
        // 不可抵达的
        try {
            player.getScene().getPathFindMgrComponent().searchPrePath(player.getMainCity(), player.getMainCity().getCurPoint(), entity.getCurPoint(), GameLogicConstants.NORMAL_MOVE_TARGET);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public CurrencyType getCurrencyType() {
        return type;
    }

    @Override
    public boolean equals(Object o) {
        if (!super.equals(o)) {
            return false;
        }
        ResBuildingSearch that = (ResBuildingSearch) o;
        return getLevel() == that.getLevel() && type == that.type;
    }

    /**
     * 资源田优先级计算
     * <p>
     * 返回结果条件顺序
     * 1.军团领地内与玩家主堡距离Xm内空闲的等于当前搜索等级的资源田
     * 2.与玩家主堡距离Xm内空闲的等于当前搜索等级的资源田
     * 3.军团领地内与玩家主堡距离Xm内空闲的大于当前搜索等级的资源田
     * 4.与玩家主堡距离Xm内空闲的大于当前搜索等级的资源田
     */
    @Override
    public double getPriorityV(ScenePlayerEntity player, ResBuildingEntity entity) {
        int base = UnitConvertUtils.meterToCm(getSearchRange());
        // 等级相符就不加  不符合 就加，降低优先级
        int levelAdd = base * (entity.getTemplate().getResLevel() == getLevel() ? 0 : 2);
        // 是本联盟的 就减  提高优先级
        int clanAdd = 0;
        if (player.getClanId() != 0) {
            clanAdd = -base * (entity.getProp().getClanId() == player.getClanId() ? 1 : 0);
        }
        return Point.calDisBetweenTwoPoint(entity.getCurPoint(), player.getMainCity().getCurPoint()) + levelAdd + clanAdd;
    }

    @Override
    public int hashCode() {
        return Objects.hash(getLevel(), type);
    }

    @Override
    public boolean isAlwaysFirst() {
        return true;
    }

    @Override
    public String toString() {
        return "ResBuildingSearch";
    }
}

package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;

import static com.yorha.proto.CommonEnum.AiParams.AP_ALERT_RANGE;

/**
 * 濒死状态
 * 进入条件： 无存活战力
 * 进入：无
 * 执行：无
 * 结束：无
 * <AUTHOR>
 */
public class NearDeathAction extends AbstractAIAction {


    public NearDeathAction() {
        super();
    }

    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        return !component.getOwner().getBattleComponent().hasAnyAlive();
    }

    @Override
    protected void execute(SceneObjAiComponent component) {

    }

    @Override
    protected String getActionName() {
        return "NearDeathAction";
    }

}

package com.yorha.cnc.scene.sceneclan.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.addition.SceneClanAdditionMgr;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class SceneClanAdditionComponent extends AbstractComponent<SceneClanEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanAdditionComponent.class);

    private SceneClanAdditionMgr additionMgr;

    public SceneClanAdditionComponent(SceneClanEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        additionMgr = new SceneClanAdditionMgr(getOwner());
    }

    public void updateAdditionFromClan(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        additionMgr.updateAdditionBySource(sourceType, additions);
    }

    public long getAddition(int additionId) {
        return additionMgr.getAddition(additionId);
    }

    public void updateScenePlayerAdditionFromClan(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        Map<Integer, Long> printMap = Maps.newHashMap();
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            if (entry.getValue() == 0) {
                continue;
            }
            printMap.put(entry.getKey(), entry.getValue());
        }
        LOGGER.info("{} updateScenePlayerAdditionFromClan source:{} additions:{}", getOwner(), sourceType, printMap);
        // clan更新了scene上的加成，要同步给所有成员
        for (Long memberId : getOwner().getMemberComponent().getMember()) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayer(memberId);
            if (null == scenePlayer) {
                LOGGER.error("updateScenePlayerAdditionFromClan failed scenePlayer is null, memberId:{}", memberId);
                continue;
            }
            if (scenePlayer.getClanId() == 0 || scenePlayer.getClanId() != getOwner().getEntityId()) {
                LOGGER.error("updateScenePlayerAdditionFromClan failed scenePlayer clan is invalid playerClan:{} clanId:{} memberId:{}", scenePlayer.getClanId(), getOwner().getEntityId(), memberId);
                continue;
            }
            scenePlayer.getAdditionComponent().updateAdditionFromClan(sourceType, additions);
        }
    }
}

package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 半开放关隘
 *
 * <AUTHOR>
 */
public class SemiOpenCrossing implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        actor.getScene().getBuildingMgrComponent().semiOpenMapBuilding(1);
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}

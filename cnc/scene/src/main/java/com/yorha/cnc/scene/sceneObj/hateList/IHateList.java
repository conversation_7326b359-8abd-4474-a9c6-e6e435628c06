package com.yorha.cnc.scene.sceneObj.hateList;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface IHateList {

    /**
     * 获取仇恨最高的对象
     */
    long getMostHateEntity();

    /**
     * 获取仇恨对象列表
     */
    Collection<Long> getHateEntities();

    /**
     * 获取对指定对象的仇恨
     */
    long getHate(long entityId);

    /**
     * 获取对指定对象的仇恨
     */
    HateInfo getHateInfo(long entityId);

    /**
     * 增加对指定目标的仇恨
     */
    void addHate(long entityId, long hate);

    /**
     * 增加不可以被清理的仇恨
     */
    void addFixHate(long entityId, long hate);

    /**
     * 清楚指定目标的仇恨
     */
    void clearHate(long entityId);

    /**
     * 清空所有仇恨
     */
    void clear();

    void onTick();

    /**
     * 设置锁定仇对象
     */
    void setLockHateTarget(long lockHateTarget);

    /**
     * 获得仇恨过期时间(单位秒)
     */
    int getHateExpire();
}

package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.wechatlog.WechatLog;

/**
 * <AUTHOR>
 */
public class AbstractScenePlayerCityMgrComponent extends AbstractComponent<AbstractScenePlayerEntity> {

    /**
     * 自己的主堡
     */
    protected CityEntity myMainCity;

    public AbstractScenePlayerCityMgrComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    /**
     * 主堡创建或db恢复 都会调用该接口
     */
    public void onNewMainCity(CityEntity city) {
        if (myMainCity != null && myMainCity.getEntityId() != city.getEntityId()) {
            WechatLog.error("{} repeat city. {} {}", getOwner(), myMainCity.getEntityId(), city.getEntityId());
        }
        myMainCity = city;
        getOwner().setMainCityId(myMainCity.getEntityId());
    }

    public CityEntity getMyMainCity() {
        return myMainCity;
    }
}

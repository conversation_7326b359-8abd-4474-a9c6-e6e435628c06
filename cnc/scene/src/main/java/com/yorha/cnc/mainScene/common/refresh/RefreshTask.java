package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.utils.Pair;

/**
 * <AUTHOR>
 */
public abstract class RefreshTask {
    private final SceneEntity owner;

    public RefreshTask(SceneEntity owner) {
        this.owner = owner;
    }

    /**
     * 获取单次tick处理数
     *
     * @return 单次tick处理数
     */
    public abstract int getHandleNumPerTick();

    /**
     * 执行逻辑
     *
     * @return 处理是否完毕 实际处理数
     */
    public abstract Pair<Boolean, Integer> run();

    public SceneEntity getOwner() {
        return owner;
    }
}

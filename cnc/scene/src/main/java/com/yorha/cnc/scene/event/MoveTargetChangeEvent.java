package com.yorha.cnc.scene.event;

import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;

/**
 * <AUTHOR>
 */
public class MoveTargetChangeEvent extends IEventWithEntityId {
    final long newTargetId;

    public MoveTargetChangeEvent(long entityId, long newTargetId) {
        super(entityId);
        this.newTargetId = newTargetId;
    }

    public long getNewTargetId() {
        return newTargetId;
    }
}

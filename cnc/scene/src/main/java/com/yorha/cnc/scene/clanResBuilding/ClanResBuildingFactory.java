package com.yorha.cnc.scene.clanResBuilding;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.clan.ClanResBuildTemplateResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanResBuildingProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MapBuildingType;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.Struct.Point;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ClanResBuildingFactory {
    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildingFactory.class);

    public static ClanResBuildingEntity createClanResBuilding(SceneEntity scene, MapBuildingType type, Point point, long entityId) {
        // 设置prop前的检查和数据准备
        ClanResBuildTemplateResService service = ResHolder.getResService(ClanResBuildTemplateResService.class);
        int x = point.getX(), y = point.getY();
        int partId = MapGridDataManager.getPartId(scene.getMapId(), x, y);
        MapBuildingEntity mapBuilding = scene.getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            WechatLog.error("createClanResBuilding failed, mapBuilding is null, partId:{}, x: {}, y: {}", partId, x, y);
            return null;
        }
        SceneClanEntity ownerSceneClan = mapBuilding.getOccupyComponent().getOwnerSceneClan();
        if (ownerSceneClan == null) {
            WechatLog.error("createClanResBuilding failed, ownerSceneClan is null, partId:{}, x: {}, y: {}", partId, x, y);
            return null;
        }

        ClanResBuildingProp prop = new ClanResBuildingProp();
        // 设置唯一id
        prop.setEntityId(entityId);
        // 设置模板id
        prop.setTemplateId(service.getResBuildTemplateIdByType(type));
        // 初始状态即为可建造
        prop.setState(CommonEnum.ClanResBuildingStage.CRBS_BUILD);
        // 坐标点
        prop.getPoint().setX(point.getX()).setY(point.getY());
        // 军团id
        prop.setClanId(ownerSceneClan.getEntityId()).setZoneId(ownerSceneClan.getZoneId());
        // 军团名称
        prop.setShowClanSimpleName(ownerSceneClan.getClanSimpleName());
        // 军团简称
        prop.setShowClanName(ownerSceneClan.getClanName());
        // 军团旗帜信息
        ownerSceneClan.copy2FlagInfo(prop.getFlagInfo());

        LOGGER.info("createClanResBuilding success, entityId: {}, type: {}, point: {}, prop: {}", entityId, type, point, prop);
        ClanResBuildingBuilder builder = new ClanResBuildingBuilder(scene, entityId, prop);
        ClanResBuildingEntity entity = new ClanResBuildingEntity(builder, true);
        entity.getBuildComponent().onEnterBuild(SystemClock.now());
        entity.addIntoScene();
        LOGGER.info("begin insert to db, entity: {}", entity);
        entity.getDbComponent().insertIntoDb();
        LOGGER.info("insert to db success, entity: {}", entity);
        return entity;
    }

    public static void restoreClanResBuilding(SceneEntity scene, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        ClanResBuildingProp prop = ClanResBuildingProp.of(fullAttr.getClanResBuildingAttr(), changedAttr.getClanResBuildingAttr());
        if (prop.getTemplateId() == 0) {
            LOGGER.error("restoreClanResBuilding failed, templateId is 0, prop: {}", prop);
            return;
        }
        ClanResBuildingBuilder builder = new ClanResBuildingBuilder(scene, prop.getEntityId(), prop);
        ClanResBuildingEntity entity = new ClanResBuildingEntity(builder, false, true);
        SceneClanEntity sceneClan = scene.getClanMgrComponent().getSceneClan(prop.getClanId());
        if (sceneClan == null || sceneClan.isDestroy()) {
            LOGGER.info("no need restoreClanResBuilding, sceneClan is null, clanId: {}", prop.getClanId());
            // 如果sceneClan没有了，ClanResBuilding就可以删了
            entity.getCollectComponent().onClanDismiss();
            return;
        }
        entity.addIntoScene();
        sceneClan.getBuildComponent().addClanResBuilding(entity);
        // kvk 兼容线上数据
        if (entity.getZoneId() == 0) {
            entity.getProp().setZoneId(sceneClan.getZoneId());
        }
        LOGGER.info("restoreClanResBuilding success, entity: {}", entity);
    }
}

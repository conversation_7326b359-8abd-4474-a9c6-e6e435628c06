package com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 血量恢复时间
 *
 * <AUTHOR>
 */
public class RecoverStageNode extends ClanBuildingNode {
    private static final Logger LOGGER = LogManager.getLogger(RecoverStageNode.class);

    public RecoverStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_AFTER_FIRE_RECOVER;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2RecoverFinish);
            return;
        }
        // 这个无所谓进入时间戳
        time2RecoverFinish();
    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        int recoverTime = calHpAndGetRecoverTime();
        if (recoverTime <= 0) {
            getComponent().transNewNode(new AfterRebuildStageNode(getOwner()));
            return;
        }
        getProp().setState(getStage()).setStateStartTsMs(ts).setStateEndTsMs(ts + TimeUtils.second2Ms(recoverTime))
                .setFileNumCalcTsMs(ts);
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2RecoverFinish, recoverTime, TimeUnit.SECONDS);
            return;
        }
        time2RecoverFinish();
    }

    @Override
    protected void afterRefreshMaxHp(int oldHp, int newHp) {
        refreshDurability();
        // 重入
        onEnter(SystemClock.now());
    }

    @Override
    public void onHpDec(int decNum) {
        refreshDurability();
        super.onHpDec(decNum);
        // 重入
        onEnter(SystemClock.now());
    }

    private void time2RecoverFinish() {
        getComponent().clearStageTimer();
        getConstructInfoProp().setCurrentDurability(getConstructInfoProp().getMaxDurability());
        getComponent().transNewNode(new AfterRebuildStageNode(getOwner()));
    }

    /**
     * 计算恢复时间
     */
    private int calHpAndGetRecoverTime() {
        getConstructInfoProp().setIsOnFire(false);
        ConstClanTerritoryTemplate template = getConstClanTerritoryTemplate();
        int deductHp = getConstructInfoProp().getMaxDurability() - getConstructInfoProp().getCurrentDurability();
        return deductHp / template.getRecoverFireSpeed();
    }

    @Override
    public void onAttackSuccess(long clanId) {
        // 恢复状态得先结算下
        refreshDurability();
        super.onAttackSuccess(clanId);
    }
}

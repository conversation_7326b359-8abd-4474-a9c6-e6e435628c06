package com.yorha.cnc.scene.clanResBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ProgressInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanTerritory;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanResourceBuildingTemplate;
import res.template.ConstClanTerritoryTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ClanResBuildingBuildComponent extends SceneObjComponent<ClanResBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildingBuildComponent.class);

    private EventListener listener;

    private TimerReasonType buildFinishTimer = null;

    public ClanResBuildingBuildComponent(ClanResBuildingEntity owner) {
        super(owner);
    }

    @Override
    public void afterAllLoad() {
        if (getOwner().getState() != CommonEnum.ClanResBuildingStage.CRBS_BUILD) {
            // 仅建设状态需要恢复
            return;
        }
        // 发现在阻挡里
        if (!getOwner().getScene().getCityMoveComponent().isPointNavMovable(getOwner().getCurPoint())) {
            LOGGER.info("restore but in static collision {}", getOwner());
            getOwner().getInnerArmyComponent().returnAllArmy();
            getOwner().sendExpansionLog("guild_building_disappear", 0L);
            sendFinishBuildMail(CommonEnum.ClanBuildStatus.CBS_CANCEL);
            getOwner().destroy();
            return;
        }
        if (getProgressInfo().getStateEndTsMs() > SystemClock.now()) {
            addBuildFinishTimer(this::time2BuildFinish);
            listener = getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onBuildSpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
        } else {
            onBuildFinish(getProgressInfo().getStateEndTsMs());
        }
        LOGGER.info("ClanResBuildingCollectComponent finish build restore, clanResBuilding {}", getOwner());
    }

    public void onEnterBuild(long enterTs) {
        LOGGER.info("{} onEnter build, enterTs = {}", getOwner(), enterTs);
        ClanResourceBuildingTemplate template = getOwner().getClanResourceBuildingTemplate();
        getProgressInfo().setLastCalTsMs(enterTs)
                .setLastCalNum(0)
                .setMaxNum(template.getTotalWorks())
                .setStateStartTsMs(enterTs);
        // 发送开始建造邮件
        sendStartBuildMail();
        // 重置建设速度
        resetBuildSpeed();
    }

    public void onBuildSpeedChange(IEvent e) {
        cancelBuildFinishTimer();
        long now = SystemClock.now();
        // 结算之前的建设进度
        long addProgress = (now - getProgressInfo().getLastCalTsMs()) / 1000 * getProgressInfo().getSpeed();
        getProgressInfo().setLastCalNum(getProgressInfo().getLastCalNum() + addProgress);
        LOGGER.info("{} onBuildSpeedChange, addProgress = {}, lastCalNum = {}", getOwner(), addProgress, getProgressInfo().getLastCalNum());
        // 更新建设进度
        resetBuildSpeed();
    }

    /**
     * 重置速度，返回任务结束的时间戳
     */
    private void resetBuildSpeed() {
        cancelBuildFinishTimer();
        long totalWorks = getProgressInfo().getMaxNum();
        long now = SystemClock.now();
        if (getProgressInfo().getLastCalNum() >= totalWorks) {
            // 已经建设完成
            onBuildFinish(now);
            return;
        }
        // 计算建设结束的时间戳
        long newSpeed = SceneAddCalc.getRebuildSpeed(getOwner(), getOwner().getOwnerSceneClan());
        long deltaTs = (totalWorks - getProgressInfo().getLastCalNum()) / newSpeed;
        if (deltaTs <= 0) {
            onBuildFinish(now);
            return;
        }
        getProgressInfo().setLastCalTsMs(now).setSpeed(newSpeed).setStateEndTsMs(now + TimeUtils.second2Ms(deltaTs));
        addBuildFinishTimer(this::time2BuildFinish, deltaTs, TimeUnit.SECONDS);
        if (listener == null) {
            listener = getOwner().getEventDispatcher().addMultiEventListenerRepeat(this::onBuildSpeedChange, InnerArmyAddEvent.class, InnerArmyDelEvent.class);
        }
        LOGGER.info("{} resetBuildSpeed, newSpeed = {}, deltaTs = {}", getOwner(), newSpeed, deltaTs);
    }

    /**
     * 基于当前结算数量、总量和速度，计算完成总量的时间
     *
     * @return 返回剩余时间
     */
    private long calcFinishTimeMs() {
        long maxNum = getProgressInfo().getMaxNum();
        long lastCalNum = getProgressInfo().getLastCalNum();
        if (maxNum - lastCalNum <= 0) {
            LOGGER.info("ProgressInfo: calcRemainMs, already finished");
            return 0;
        }
        long speed = getProgressInfo().getSpeed();
        if (speed <= 0) {
            LOGGER.info("ProgressInfo: calcRemainMs failed, speed {} wrong", speed);
            return 0;
        }
        long remainTsMs = (maxNum - lastCalNum) / speed;
        return getProgressInfo().getLastCalTsMs() + remainTsMs;
    }

    public void time2BuildFinish() {
        cancelBuildFinishTimer();
        onBuildFinish(SystemClock.now());
    }

    private void onBuildFinish(long ts) {
        cancelBuildFinishTimer();
        cancelListener();
        try {
            // 为建筑内所有部队计算积分
            long clanRebuildScore = getOwner().getInnerArmyComponent().addAllPlayerClanRebuildScore(getProgressInfo().getStateEndTsMs(), getProgressInfo().getStateStartTsMs());
            // 所有玩家的分数总和等于军团获得的积分
            getOwner().getInnerArmyComponent().addClanOccupyOrRebuildScore(clanRebuildScore);
        } catch (Exception e) {
            LOGGER.error("ClanResBuilding onBuildFinished error, ts = {}", ts, e);
        }
        // 建造完成的处理
        afterBuildFinish();
        // 初始化可采集状态
        getOwner().getCollectComponent().onEnterCollect(ts);
        // 打个qlog
        getOwner().sendExpansionLog("build_complete", 0);
    }

    private void afterBuildFinish() {
        sendFinishBuildMail(CommonEnum.ClanBuildStatus.CBS_FINISH);
    }

    private void addBuildFinishTimer(Runnable runnable) {
        long waitTimeMs = getProgressInfo().getStateEndTsMs() - SystemClock.now();
        buildFinishTimer = TimerReasonType.CLAN_RES_BUILDING_FINISH;
        getOwner().getTimerComponent().addTimer(TimerReasonType.CLAN_RES_BUILDING_FINISH, runnable, waitTimeMs, TimeUnit.MILLISECONDS);
    }

    private void addBuildFinishTimer(Runnable runnable, long delay, TimeUnit unit) {
        buildFinishTimer = TimerReasonType.CLAN_RES_BUILDING_FINISH_2;
        getOwner().getTimerComponent().addTimer(TimerReasonType.CLAN_RES_BUILDING_FINISH_2, runnable, delay, unit);
    }

    private void cancelBuildFinishTimer() {
        if (buildFinishTimer != null) {
            getOwner().getTimerComponent().cancelTimer(buildFinishTimer);
            buildFinishTimer = null;
        }
    }

    private void cancelListener() {
        if (listener != null) {
            listener.cancel();
            listener = null;
        }
    }

    private ProgressInfoProp getProgressInfo() {
        return getOwner().getProp().getProgressInfo();
    }

    private void sendStartBuildMail() {
        int mailId = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class).getStartBuildMail();
        if (mailId <= 0) {
            LOGGER.error("mailId is {} wrong when sen rebuild mail", mailId);
            return;
        }
        StructMail.MailSendParams.Builder mailBuilder = StructMail.MailSendParams.newBuilder();
        mailBuilder.setMailTemplateId(mailId);
        // 添加额外内容
        mailBuilder.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_CLAN_BUILDING);
        mailBuilder.getContentBuilder().getClanBuildingDataBuilder().setTemplateId(getOwner().getProp().getTemplateId())
                .setP(getOwner().formScenePoint());
        MailUtil.sendClanMail(getOwner().getZoneId(), getOwner().getClanId(), mailBuilder.build());
    }

    protected void sendFinishBuildMail(CommonEnum.ClanBuildStatus status) {
        SsClanTerritory.SyncClanBuildStatusCmd.Builder cmdBuilder = SsClanTerritory.SyncClanBuildStatusCmd.newBuilder();
        cmdBuilder.setId(getOwner().getEntityId());
        cmdBuilder.setPoint(Struct.Point.newBuilder().setX(getOwner().getCurPoint().getX()).setY(getOwner().getCurPoint().getY()));
        cmdBuilder.setStatus(status);
        cmdBuilder.setTemplateId(getOwner().getProp().getTemplateId());
        cmdBuilder.setIsClanResBuilding(true);
        getOwner().getOwnerSceneClan().tellClan(cmdBuilder.build());
    }
}

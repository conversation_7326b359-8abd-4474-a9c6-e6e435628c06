package com.yorha.cnc.scene.clanResBuilding;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjTransformComponent;
import com.yorha.game.gen.prop.ClanResBuildingProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class ClanResBuildingBuilder extends SceneObjBuilder<ClanResBuildingEntity, ClanResBuildingProp> {
    public ClanResBuildingBuilder(SceneEntity sceneEntity, long eid, ClanResBuildingProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public SceneObjTransformComponent transformComponent(ClanResBuildingEntity owner) {
        return new BuildingTransformComponent(owner, owner, getPointProp());
    }
}

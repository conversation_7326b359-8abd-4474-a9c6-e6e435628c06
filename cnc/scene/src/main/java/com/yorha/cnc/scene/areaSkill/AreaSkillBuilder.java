package com.yorha.cnc.scene.areaSkill;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.game.gen.prop.AreaSkillProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 * @date 2023/4/12
 */
public class AreaSkillBuilder extends SceneObjBuilder<AreaSkillEntity, AreaSkillProp> {

    public AreaSkillBuilder(SceneEntity sceneEntity, long eid, AreaSkillProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }
}

package com.yorha.cnc.scene.sceneObj.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AiActionFactory;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.AbstractStateDiagram;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.StateDiagramFactory;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.StateTransition;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.StateTransitionConfig;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.ai.AiResService;
import com.yorha.common.statemachine.StateContext;
import com.yorha.common.statemachine.StateMachine;
import com.yorha.common.statemachine.StateMachineFactory;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.AIStateType;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.AiInstanceTemplate;
import res.template.AiStateTriggerTemplate;

import java.util.*;

import static com.yorha.common.constant.LogConstant.LOG_TYPE_AI;
import static com.yorha.proto.CommonEnum.AiRecordType.ART_BACK_TO_POINT;
import static com.yorha.proto.CommonEnum.MonsterTriggerType.MTT_REDUCE_SOLDIER;
import static com.yorha.proto.CommonEnum.MonsterTriggerType.MTT_TAG;


/**
 * <AUTHOR>
 */
public class SceneObjAiComponent extends SceneObjComponent<SceneObjEntity> implements StateContext<AIStateType, AIEvent, SceneObjAiComponent> {

    private static final Logger LOGGER = LogManager.getLogger(LOG_TYPE_AI);
    private static final Logger GS_LOGGER = LogManager.getLogger(SceneObjAiComponent.class);
    // 状态机
    private StateMachine<AIStateType, AIEvent, SceneObjAiComponent> stateMachine;
    // 状态->触发器
    private final Map<AIStateType, Map<CommonEnum.MonsterTriggerType, List<AiTrigger>>> triggers = new HashMap<>();
    private boolean isDebugAble = false;
    private final Queue<AIEvent> eventQueue = new LinkedList<>();
    /**
     * 是否在运行中
     */
    private boolean isRunning;
    /**
     * ai 黑板, 这里的记录，通常为手动添加，删除
     */
    private final Map<CommonEnum.AiRecordType, Object> blackBoard = new HashMap<>();

    /**
     * 当前state的tag cache，状态切换清空tagId->过期时间，通常为自动过期删除的
     */
    private final Map<Integer, Long> stateTags = Maps.newHashMap();
    /**
     * 配置参数
     */
    private Map<CommonEnum.AiParams, Integer> aiParams;
    /**
     * 主人脱战即死的召唤物们
     */
    private final List<Long> invokesFollowDestory = new ArrayList<>();
    /**
     * 非同生共死的召唤物们
     */
    private final List<Long> invokesNotFollowDestory = new ArrayList<>();

    private long targetIgnoreRange;

    public SceneObjAiComponent(SceneObjEntity owner) {
        super(owner);
    }


    public boolean isDebugAble() {
        return isDebugAble;
    }

    public void setDebugAble(boolean debugAble) {
        isDebugAble = debugAble;
    }

    public void reset() {
        GS_LOGGER.info("{}, reset", getLogHead());
        recycleAllInvoke();
        triggerEvent(AIEvent.RESET);
    }

    private void innerReset() {
        GS_LOGGER.info("{}, innerReset", getLogHead());
        triggers.clear();
        eventQueue.clear();
        blackBoard.clear();
        stateTags.clear();
        invokesFollowDestory.clear();
        invokesNotFollowDestory.clear();
        postInit();
    }

    public Map<CommonEnum.AiParams, Integer> getAiParams() {
        if (aiParams != null) {
            return aiParams;

        }
        return ResHolder.getResService(AiResService.class).getParams(getAiIndex());
    }

    public void addInvokes(boolean needFollowDestroy, long invokesId) {
        GS_LOGGER.info("{}, addInvokes {} {}", getLogHead(), needFollowDestroy, invokesId);
        if (needFollowDestroy) {
            invokesFollowDestory.add(invokesId);
            return;
        }
        invokesNotFollowDestory.add(invokesId);
    }

    public List<Long> getInvokesFollowDestory() {
        return invokesFollowDestory;
    }

    public List<Long> getInvokesNotFollowDestory() {
        return invokesNotFollowDestory;
    }

    public void clearAllInvokes() {
        GS_LOGGER.info("{}, clearAllInvokes", getLogHead());
        getInvokesNotFollowDestory().clear();
        getInvokesFollowDestory().clear();
    }

    private void recycleAllInvoke() {
        List<Long> list = new ArrayList<>();
        list.addAll(getInvokesNotFollowDestory());
        list.addAll(getInvokesFollowDestory());
        for (long invokeId : list) {
            SceneObjEntity objEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(invokeId);
            if (objEntity == null) {
                continue;
            }
            objEntity.deleteObj();
        }
        clearAllInvokes();
    }

    public void onInvokeDead(long invokeId) {
        if (!getInvokesFollowDestory().remove(invokeId)) {
            getInvokesNotFollowDestory().remove(invokeId);
        }
    }

    /**
     * 插入tag
     */
    public void addTag(List<Pair<Integer, Integer>> tagList) {
        if (isDebugAble()) {
            LOGGER.info("{}, addTag:{}", getLogHead(), tagList);
        }
        for (Pair<Integer, Integer> tagPair : tagList) {
            int tag = tagPair.getFirst();
            long lifetime = tagPair.getSecond();
            // 设置过期时间
            long overTime = MathUtils.addExact(lifetime, SystemClock.now());
            stateTags.put(tag, overTime);
        }
        trigger(MTT_TAG);
    }

    /**
     * 是否有tag
     *
     * @param tag
     * @return
     */
    public boolean hasTag(Integer tag) {
        return stateTags.containsKey(tag);
    }

    /**
     * 清除所有tag，不管是否到期
     */
    public void clearAllTag() {
        stateTags.clear();
    }

    /**
     * 向黑板插入记录
     */
    public void addRecord(CommonEnum.AiRecordType key, Object value) {
        blackBoard.put(key, value);
        if (isDebugAble()) {
            LOGGER.info("{}, addRecord:{},{} ", getLogHead(), key, value);
        }
    }

    /**
     * 删除黑板记录
     */
    public void removeRecord(CommonEnum.AiRecordType key) {
        Object removed = blackBoard.remove(key);
        if (removed != null) {
            if (isDebugAble()) {
                LOGGER.info("{}, removeRecord:{} ", getLogHead(), key);
            }
        }
    }

    /**
     * 获取黑板记录，无记录返回null
     */
    public Object tryGetRecord(CommonEnum.AiRecordType key) {
        return blackBoard.getOrDefault(key, null);
    }

    /**
     * 获取黑板记录，获取前需确保有记录
     */
    public Object getRecord(CommonEnum.AiRecordType key) {
        return blackBoard.get(key);
    }

    public int getAiIndex() {
        return 0;
    }

    @Override
    public void postInit() {
        super.postInit();
        int aiIndex = getAiIndex();
        if (aiIndex == 0) {
            return;
        }
        getOwner().tryRemoveTick(SceneTickReason.TICK_AI);
        getOwner().addTick(SceneTickReason.TICK_AI);
        aiParams = ResHolder.getResService(AiResService.class).getParams(getAiIndex());

        AiResService aiResService = ResHolder.getResService(AiResService.class);
        AiInstanceTemplate aiInstance = aiResService.getConfig(aiIndex);
        if (aiInstance == null) {
            return;
        }
        initStateMachine(aiInstance);
        initTrigger(aiInstance);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndSingleRelation, EndSingleBattleEvent.class);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    /**
     * 初始化状态机
     */
    private void initStateMachine(AiInstanceTemplate aiInstance) {
        AiResService aiResService = ResHolder.getResService(AiResService.class);
        AbstractStateDiagram stateDiagram = StateDiagramFactory.newInstance(aiInstance.getModel());
        StateMachineFactory<AIStateType, AIEvent, SceneObjAiComponent> factory = new StateMachineFactory<>(stateDiagram.getInitState());
        List<Integer> statesList = aiResService.getStates(aiInstance.getModel());
        if (CollectionUtils.isNotEmpty(statesList)) {
            statesList.forEach(state -> {
                AIStateType stateType = AIStateType.forNumber(state);
                List<StateTransitionConfig> configs = AbstractStateDiagram.getTransitions(stateType, stateDiagram.getStateMap());
                if (configs == null) {
                    return;
                }
                configs.forEach(config -> {
                    if (stateType == null) {
                        throw new GeminiException("unknown stateType:{}", stateType);
                    }
                    StateTransition transition = StateTransition.valueOf(config, AiActionFactory.getAction(stateType));
                    factory.addTransition(transition);
                });
            });
        }
        stateMachine = factory.newInstance();
        if (isDebugAble()) {
            LOGGER.info("{}, init aiType:{}", getLogHead(), aiInstance.getId());
        }
        if (stateMachine.getAction(getCurrentState()).isPresent()) {
            stateMachine.getAction(getCurrentState()).get().onEnter(this);
        }
    }

    /**
     * 初始化触发器
     */
    private void initTrigger(AiInstanceTemplate aiInstance) {
        List<IntPairType> triggerList = aiInstance.getTriggersPairList();
        if (CollectionUtils.isEmpty(triggerList)) {
            return;
        }
        boolean careHurt = false;
        for (IntPairType pair : triggerList) {
            AiStateTriggerTemplate template = getTemplate(pair.getValue());
            if (template == null) {
                return;
            }
            if (template.getTriggerType() == MTT_REDUCE_SOLDIER) {
                careHurt = true;
            }
            triggers.computeIfAbsent(AIStateType.forNumber(pair.getKey()), key -> new HashMap<>()).computeIfAbsent(template.getTriggerType(), key -> new ArrayList<>()).add(AiTrigger.createTrigger(template));
        }
        if (careHurt) {
            // 收到伤害触发
            getOwner().getEventDispatcher().addEventListenerRepeat((BattleRoleSettleRoundEvent event) -> {
                trigger(MTT_REDUCE_SOLDIER);
            }, BattleRoleSettleRoundEvent.class);
        }
    }


    public AIStateType getCurrentState() {
        return stateMachine.currentState();
    }

    public void triggerEvent(AIEvent event) {
        eventQueue.add(event);
    }

    private void onEvent(AIEvent event) {
        if (isDebugAble()) {
            LOGGER.info("{}, processEvent:{}", getLogHead(), event);
        }
        if (getOwner().getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
            GS_LOGGER.info("{}, processEvent:{}", getLogHead(), event);
        }
        if (event == AIEvent.RESET) {
            innerReset();
            return;
        }
        if (!isRunning()) {
            return;
        }
        // 前一个状态
        AIStateType preState = stateMachine.currentState();
        // 检验能否状态跳转
        if (!stateMachine.transitIsSatisfied(preState, event, this)) {
            return;
        }
        // 中断前一状态
        stateMachine.getAction(preState).ifPresent(preAction -> preAction.onEnd(this));
        // 跳转进入下一状态
        AIStateType postState = stateMachine.transit(preState, event, this);

        if (preState == postState) {
            LOGGER.warn("{}, processEvent:{} prestate:{} equal postState:{}", getLogHead(), event, preState, postState);
        }

    }

    public void onTick() {
        try {
            if (!isRunning()) {
                return;
            }
            while (!eventQueue.isEmpty()) {
                onEvent(eventQueue.poll());
            }
            stateMachine.getAction(getCurrentState()).ifPresent(postAction -> {
                postAction.fire(this);
            });

            cleanStateTag();

            if (isCastFinish()) {
                // 蓄力完成触发
                trigger(CommonEnum.MonsterTriggerType.MTT_CAST_FINISH);
            }
        } catch (GeminiException e) {
            if (e.getCodeId() == ErrorCode.MOVE_NO_PATH.getCodeId()) {
                LOGGER.warn("SceneObjAiComponent onTick exception {}", getOwner(), e);
                return;
            }
            LOGGER.error("SceneObjAiComponent onTick exception {}", getOwner(), e);
        }
    }

    /**
     * 删除过期tag
     */
    private void cleanStateTag() {
        if (stateTags.isEmpty()) {
            return;
        }
        long now = SystemClock.now();
        List<Integer> deleteTagList = new ArrayList<>();
        for (Map.Entry<Integer, Long> entry : stateTags.entrySet()) {
            long overTime = entry.getValue();
            // 无限期的不删除
            if (overTime < 0) {
                continue;
            }
            if (now > overTime) {
                deleteTagList.add(entry.getKey());
            }
        }
        for (Integer key : deleteTagList) {
            stateTags.remove(key);
        }
        if (deleteTagList.size() > 0) {
            if (isDebugAble()) {
                LOGGER.info("{}, cleanStateTag:{} ", getLogHead(), deleteTagList);
            }
            trigger(MTT_TAG);
        }
    }

    protected boolean isCastFinish() {
        return false;
    }


    public boolean isRunning() {
        return isRunning;
    }

    protected boolean isInitOk() {
        return stateMachine != null;
    }

    public void start() {
        if (stateMachine == null) {
            return;
        }
        if (isRunning) {
            return;
        }
        isRunning = true;
        trigger(CommonEnum.MonsterTriggerType.MTT_ENTER);
        if (getOwner().getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
            GS_LOGGER.info("{}, start", getLogHead());
        }
    }

    public void stop() {
        isRunning = false;
        if (getOwner().getEntityType() != EntityAttrOuterClass.EntityType.ET_Monster) {
            recycleAllInvoke();
            GS_LOGGER.info("{}, stop", getLogHead());
        }
    }

    public AiStateTriggerTemplate getTemplate(int triggerId) {
        return ResHolder.getInstance().getValueFromMap(AiStateTriggerTemplate.class, triggerId);
    }

    public void trigger(CommonEnum.MonsterTriggerType triggerType) {
        if (!isRunning) {
            return;
        }
        if (!triggers.containsKey(getCurrentState())) {
            return;
        }
        Map<CommonEnum.MonsterTriggerType, List<AiTrigger>> aiTriggers = triggers.get(getCurrentState());

        if (!aiTriggers.containsKey(triggerType)) {
            return;
        }
        for (AiTrigger trigger : aiTriggers.get(triggerType)) {
            trigger.trigger(getOwner());
        }
    }

    public void alert(int alertRange) {
        if (alertRange <= 0) {
            return;
        }
        SceneObjEntity owner = getOwner();
        Point curPoint = owner.getCurPoint();
        Circle alertCircle = Circle.valueOf(curPoint.getX(), curPoint.getY(), alertRange);
        Optional<SceneObjEntity> nearliestObj = owner.getBattleComponent().getEnemyList(alertCircle, null).stream()
                .min(Comparator.comparingDouble(o -> Point.calDisBetweenTwoPoint(o.getCurPoint(), curPoint)));
        // 设置仇恨值
        nearliestObj.ifPresent(sceneObjEntity -> owner.getHateListComponent().addHate(sceneObjEntity.getEntityId(), 1));
    }

    public void leaveBattle(SceneObjAiComponent component, String actionName) {
        SceneObjEntity owner = component.getOwner();
        if (owner.getTransformComponent().getBattlePoint() == null) {
            LOGGER.error("LeaveBattleAndAlert battlePoint is null entity{}", owner.getEntityId());
            return;
        }
        if (component.getAiParams().get(CommonEnum.AiParams.AP_RECOVERY_SOLDIER) != 0) {
            owner.getBattleComponent().recover();
        }
        Object object = component.getRecord(ART_BACK_TO_POINT);
        boolean isBacking = false;
        if (object != null) {
            isBacking = (boolean) object;
        }
        if (!isBacking) {
            if (owner.getTransformComponent().onBattlePlace()) {
                back(component);
            } else {
                SceneObjMoveComponent moveComponent = owner.getMoveComponent();
                if (moveComponent == null) {
                    back(component);
                    return;
                }
                try {
                    owner.getMoveComponent().moveToPointAsyncIgnoreException(owner.getTransformComponent().getBattlePoint(), () -> back(component));
                } catch (Exception e) {
                    LOGGER.warn("{} fire and execute :{} ", component.getLogHead(), actionName, e);
                    back(component);
                }
            }
            component.addRecord(ART_BACK_TO_POINT, true);
            if (component.isDebugAble()) {
                LOGGER.info("{} fire and execute :{} ", component.getLogHead(), actionName);
            }
        }
    }

    public void back(SceneObjAiComponent component) {
        component.triggerEvent(AIEvent.COME_BACK);
    }

    public String getLogHead() {
        return "AiComponent entity: " + getEntityId() + " entityType: " + getOwner().getEntityType() + " ";
    }

    public void onDead() {
        onEvent(AIEvent.DEAD);
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        List<Long> tempList = new ArrayList<>(invokesFollowDestory);
        for (long invokes : tempList) {
            SceneObjEntity objEntity = objMgrComponent.getSceneObjEntity(invokes);
            if (objEntity == null) {
                continue;
            }
            objEntity.deleteObj();
            LOGGER.info("{}, onDead invokes: {} dead", getLogHead(), invokes);
        }
        invokesFollowDestory.clear();
        // 通知master自己死亡
        Object record = tryGetRecord(CommonEnum.AiRecordType.ART_MASTER);
        if (record == null) {
            return;
        }
        SceneObjEntity objEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity((long) record);
        if (objEntity == null) {
            return;
        }
        objEntity.getAiComponent().onInvokeDead(getEntityId());
    }

    public void onEndSingleRelation(EndSingleBattleEvent event) {
        SceneObjHateListComponent sceneObjHateListComponent = getOwner().getHateListComponent();
        if (sceneObjHateListComponent == null) {
            return;
        }
        sceneObjHateListComponent.clearHate(event.getOther().getEntityId());
        if (getOwner().getHateListComponent().getHateEntities().isEmpty()) {
            triggerEvent(AIEvent.LOSE_TARGET);
        }
    }

    public boolean canLeaveBattle(String actionName) {
        // 脱战状态要求当前仇恨列表为空
        SceneObjEntity owner = this.getOwner();
        SceneObjEntity target = owner.getHateListComponent().mostHateEntity();
        if (target == null) {
            if (this.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", this.getLogHead(), actionName, true, "target is null");
            }
            return true;
        }
        if (target.isDestroy()) {
            if (this.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", this.getLogHead(), actionName, true, target.getEntityId() + "is destroy");
            }
            return true;
        }
        if (this.isDebugAble()) {
            LOGGER.info("{}, action :{} isSatisfied：{}", this.getLogHead(), actionName, false);
        }
        return false;
    }

    public boolean outChaseRange(Point point) {
        Point battlePoint = getOwner().getTransformComponent().getBattlePoint();
        if (battlePoint == null) {
            battlePoint = getOwner().getTransformComponent().getBornPoint();
        }
        int chaseRange = getAiParams().get(CommonEnum.AiParams.AP_CHASE_RANGE);
        return battlePoint != null && chaseRange > 0 && Point.calDisBetweenTwoPoint(battlePoint, point) >= chaseRange;
    }

    public void setTargetIgnoreRange(long targetId) {
        if (this.isDebugAble()) {
            LOGGER.info("{}, setTargetIgnoreRange {} -> {}", this.getLogHead(), this.targetIgnoreRange, targetId);
        }
        this.targetIgnoreRange = targetId;
    }

    public boolean isTargetIgnoreRange(long targetId) {
        return this.targetIgnoreRange == targetId;
    }

}

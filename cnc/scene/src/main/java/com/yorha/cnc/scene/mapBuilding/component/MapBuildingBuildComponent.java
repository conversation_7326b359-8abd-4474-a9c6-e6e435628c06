package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.FireStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.RebuildStageNode;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.map.BigMapBuildingTemplateService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ConstructInfoProp;
import com.yorha.game.gen.prop.OccupyInfoProp;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Core;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.MapBuildingTemplate;
import res.template.TerritoryBuildingTemplate;

/**
 * 建设相关
 *
 * <AUTHOR>
 */
public class MapBuildingBuildComponent extends SceneObjComponent<MapBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingBuildComponent.class);
    /**
     * 锁过期时间，单位毫秒，经验设置为5000ms。请勿随意更改.
     */
    private static final long LOCK_EXPIRE_TS_MS = 5 * 1000L;
    private long operateLockExpireTsMs = 0L;

    public MapBuildingBuildComponent(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        SceneClanEntity ownerSceneClan = getOwnerSceneClan();
        if (ownerSceneClan == null) {
            return;
        }
        // 恢复联盟建筑的管理
        OccupyState state = getOwner().getStageMgrComponent().getState();
        if (state == OccupyState.TOS_REBUILD) {
            // 改建中
            ownerSceneClan.getBuildComponent().restoreBuilding(getOwner());
        } else if (state == OccupyState.TOS_AFTER_REBUILD || state == OccupyState.TOS_AFTER_FIRE_RECOVER || state == OccupyState.TOS_FIRE) {
            // 改建后拥有
            ownerSceneClan.getBuildComponent().restoreBuild(getOwner());
        }
    }

    /**
     * 将地图上的地缘建筑改建为联盟建筑的前置检查
     */
    public void startRebuildPreCheck(long playerId, MapBuildingType buildingType, SceneClanEntity sceneClan) {
        // 非据点不能改建
        if (getOwner().getAreaType() != MapAreaType.TERRITORY) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_REQUEST_ILLEGAL);
        }
        // 地图建筑是否开放检测
        if (getOwner().isClose()) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_NOT_OPEN);
        }
        if (getType() != MapBuildingType.MBT_NONE) {
            // 建筑可能已经被改建
            throw new GeminiException(ErrorCode.CLAN_BUILDING_CLAN_BUILDING_REBUILD_BY_OTHERS);
        }
        // 主基地是可以白嫖的
        if (buildingType == MapBuildingType.MBT_MAIN_BASE) {
            if (getOwner().getOccupyState() != OccupyState.TOS_NEUTRAL) {
                if (getOwner().getOwnerClanId() != sceneClan.getEntityId()) {
                    // 不是中立 而且 不是自己占领完成的
                    throw new GeminiException(ErrorCode.CLAN_BUILDING_NOT_YOUR_CLAN_BUILDING);
                }
            } else {
                if (sceneClan.getMapBuildingComponent().isTerritoryNumFull(getOwner().getAreaType())) {
                    // 中立 建设会额外走占领逻辑 需要检查领土数量是否已满
                    throw new GeminiException(ErrorCode.TERRITORY_NUM_LIMIT);
                }
            }
            int buildingNum = sceneClan.getBuildComponent().getBuildingNum(MapBuildingType.MBT_MAIN_BASE);
            // 第一个主基地只能在外圈州建设
            if (buildingNum == 0) {
                if (!MapGridDataManager.isOutCircleRegion(getOwner().getScene().getMapId(), getOwner().getScene().getStoryId(), getOwner().getProp().getPoint().getX(), getOwner().getProp().getPoint().getY())) {
                    throw new GeminiException(ErrorCode.CLAN_BUILDING_NOT_IN_OUT_CIRCLE_REGION);
                }
            }
            // 主基地改建需要联通才行
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            boolean pass = sceneClan.getCrossComponent().checkSrcToEndCanPass(scenePlayer.getMainCity().getCurPoint(), getOwner().getCurPoint());
            if (!pass) {
                throw new GeminiException(ErrorCode.CLAN_BUILDING_NO_PATH_TO_BUILD);
            }
        } else {
            // 建筑不属于自己的军团
            if (getOwner().getOwnerClanId() != sceneClan.getEntityId()) {
                throw new GeminiException(ErrorCode.CLAN_BUILDING_NOT_YOUR_CLAN_BUILDING);
            }
        }
        if (buildingType == MapBuildingType.MBT_CLAN_FORTRESS) {
            if (getOwnerSceneClan().getBuildComponent().isFortressNearBy(getOwner().getCurPoint())) {
                // 要塞之间距离过近
                throw new GeminiException(ErrorCode.CLAN_BUILDING_CLAN_BUILDING_FORTRESS_TOO_CLOSE);
            }
        }
        // 阻挡检查
        Core.Code code = BornPointHelper.collisionCheck(getOwner().getScene(),
                getOwner().getProp().getPoint().getX(),
                getOwner().getProp().getPoint().getY(),
                getCollisionRadiusByType(buildingType), getEntityId());
        if (!ErrorCode.isOK(code)) {
            LOGGER.debug("collision check failed, reason {}", code);
            throw new GeminiException(ErrorCode.CLAN_BUILDING_HAVE_COLLISION);
        }
    }

    /**
     * 开始改建
     */
    public void startRebuild(MapBuildingType buildingType, int staffId, String startPlayerName) {
        // 设置改建相关属性
        getConstructInfoProp().setType(buildingType)
                .setBeforeRebuildTemplateId(getOwner().getProp().getTemplateId())
                .setMaxDurability(0).setCurrentDurability(0)
                .setIsConnectedToCommandNet(false)
                .setIsOnFire(false)
                .setStartStaffId(staffId)
                .setStartPlayerName(startPlayerName);
        // 获取建设配置
        TerritoryBuildingTemplate template = ResHolder.getResService(BigMapBuildingTemplateService.class)
                .getClanBuildingTemplate(buildingType, getOwner().getScene().getStoryId());
        getOwner().getProp().setTemplateId(template.getId())
                .getOccupyinfo().setRebuildTotalWork(template.getTotalWorks()).setRebuildNum(0);
        // 进入改建阶段
        getOwner().getStageMgrComponent().transNewNode(new RebuildStageNode(getOwner()));
    }

    /**
     * 更新耐久度
     */
    public void refreshDurability() {
        int curHp = getConstructInfoProp().getCurrentDurability();
        long lastCalcTsMs = getOccupyInfoProp().getFileNumCalcTsMs();
        ConstClanTerritoryTemplate template = getConstClanTerritoryTemplate();
        // gm调整时间后或长时间停机后，now可能已经大于建筑状态本身的结束时间戳，需要保证计算耐久度只会到状态结束
        long settleTsMs = Math.min(SystemClock.now(), getOccupyInfoProp().getStateEndTsMs());
        long calcTs = TimeUtils.ms2Second(settleTsMs - lastCalcTsMs);
        if (getConstructInfoProp().getIsOnFire()) {
            // 燃烧速度是模拟小数计算的会额外乘以10000，所以在计算扣除hp的时候要把这部分除掉
            int deductHp = (int) (getOccupyInfoProp().getFireSpeed() * calcTs / Constants.N_10_000L);
            getConstructInfoProp().setCurrentDurability(curHp - deductHp);
        }
        if (getOccupyInfoProp().getState() == OccupyState.TOS_AFTER_FIRE_RECOVER) {
            int addHp = (int) (template.getRecoverFireSpeed() * calcTs);
            getConstructInfoProp().setCurrentDurability(curHp + addHp);
        }
        getOccupyInfoProp().setFileNumCalcTsMs(settleTsMs);
    }

    /**
     * 主动灭火前置检查
     *
     * @param clanId 协议上传的clanId
     */
    public void stopFirePreCheck(long clanId) throws GeminiException {
        // 判断建筑是否在当前联盟所属
        if (getOwner().getOwnerClanId() != clanId) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_NOT_YOUR_CLAN_BUILDING);
        }
        // 建筑是否真的在着火
        if (!getConstructInfoProp().getIsOnFire()) {
            LOGGER.warn("not on fire time but try to stop fire");
            throw new GeminiException(ErrorCode.MAP_BUILDING_NOT_FIRE);
        }
        // 建筑是否处于强制燃烧状态
        long now = SystemClock.now();
        ConstClanTerritoryTemplate template = getConstClanTerritoryTemplate();
        if (TimeUtils.ms2Second(now - getOccupyInfoProp().getStateStartTsMs()) <= template.getCannotExtinguishTime()) {
            LOGGER.info("still in cannot extinguish time");
            throw new GeminiException(ErrorCode.CLAN_BUILDING_TRY_EXTINGUISH_WHEN_CANNOT_EXTINGUISH);
        }

    }

    /**
     * 主动熄灭着火状态
     */
    public void tryStopFire(long clanId) {
        // 预先检查
        stopFirePreCheck(clanId);
        StageNode stageNode = getOwner().getStageMgrComponent().getStageNode();
        if (stageNode.getStage() != OccupyState.TOS_FIRE) {
            return;
        }
        ((FireStageNode) stageNode).onStopFire();
    }

    /**
     * 发送通用的改建建筑相关的邮件
     *
     * @param mailId 邮件id
     * @param clanId 军团id
     */
    public void sendCommonRebuildMail(int mailId, long clanId, int staffId) {
        StructMail.MailSendParams.Builder mailBuilder = getCommonRebuildMailBuilder(mailId);
        if (mailBuilder == null) {
            LOGGER.error("sendCommonRebuildMail is null {} {} {}", mailId, clanId, staffId);
            return;
        }
        mailBuilder.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        Struct.DisplayData.Builder displayDataBuilder = mailBuilder.getContentBuilder().getDisplayDataBuilder();
        if (staffId != 0) {
            displayDataBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_STAFF_ID_FOR_NAME, staffId));
        }
        displayDataBuilder.getParamsBuilder().addDatas(MsgHelper.buildGotoDisplayPoint(
                getOwner().getProp().getPoint().getX(),
                getOwner().getProp().getPoint().getY(),
                getOwner().getScene().getMapType(),
                getOwner().getScene().getMapIdForPoint())
        );
        displayDataBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getOwner().getProp().getTemplateId()));
        SceneClanEntity sceneClanOrNull = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClanOrNull != null) {
            MailUtil.sendClanMail(sceneClanOrNull.getZoneId(), clanId, mailBuilder.build());
        }

    }

    /**
     * 发送改建开始的邮件
     *
     * @param mailId 邮件id
     */
    public void sendStartRebuildMail(int mailId) {
        StructMail.MailSendParams.Builder mailBuilder = getCommonRebuildMailBuilder(mailId);
        // 添加额外内容
        if (null != mailBuilder) {
            mailBuilder.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_CLAN_BUILDING);
            mailBuilder.getContentBuilder().getClanBuildingDataBuilder().setTemplateId(getOwner().getProp().getTemplateId())
                    .setP(getOwner().formScenePoint());
            MailUtil.sendClanMail(getOwnerSceneClan().getZoneId(), getOwner().getOwnerClanId(), mailBuilder.build());
        }
    }

    private StructMail.MailSendParams.Builder getCommonRebuildMailBuilder(int mailId) {
        if (mailId <= 0) {
            LOGGER.error("mailId is {} wrong when sen rebuild mail", mailId);
            return null;
        }
        StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder();
        mail.setMailTemplateId(mailId);
        return mail;
    }

    public void setCommandNet(boolean isConnectNet) {
        if (isConnectNet == getConstructInfoProp().getIsConnectedToCommandNet()) {
            return;
        }
        getConstructInfoProp().setIsConnectedToCommandNet(isConnectNet);
        LOGGER.info("{} setCommandNet :{}", getOwner(), isConnectNet);
    }

    public int changeAffectedByWhichMainBase(int uniqueId, boolean isAdd) {
        int base = getConstructInfoProp().getAffectedByWhichMainBase();
        int newBase = base;
        if (isAdd) {
            newBase |= (1 << uniqueId);
        } else {
            newBase &= (~(1 << uniqueId));
        }
        getConstructInfoProp().setAffectedByWhichMainBase(newBase);
        LOGGER.info("{} changeAffectedByWhichMainBase. old:{} new:{}", getOwner(), base, newBase);
        return newBase;
    }

    /**
     * 因为技能扣耐久
     */
    public void decHpBySkill(int decNum) {
        if (!getOwner().isClanBuilding() || decNum <= 0) {
            LOGGER.error("{} decHpBySkill error decNum: {}", getOwner(), decNum);
            return;
        }
        ((ClanBuildingNode) getOwner().getStageMgrComponent().getStageNode()).onHpDec(decNum);
    }

    // ----------------------------------------- 建筑加锁相关 ------------------------------ //

    /**
     * 给建筑尝试添加操作锁
     *
     * @return 是否成功上锁
     */
    public boolean tryAddOperateLock() {
        long now = System.currentTimeMillis();
        if (operateLockExpireTsMs > now) {
            return false;
        }
        operateLockExpireTsMs = now + LOCK_EXPIRE_TS_MS;
        return true;
    }

    /**
     * 移除操作锁
     */
    public void removeOperateLock() {
        operateLockExpireTsMs = 0L;
    }
    // ----------------------------------------- 建筑加锁相关 ------------------------------ //

    public int getAffectedByWhichMainBase() {
        return getConstructInfoProp().getAffectedByWhichMainBase();
    }

    public void setCommandNetMainNo(int uniqueId) {
        if (uniqueId == 0) {
            setCommandNet(false);
            getConstructInfoProp().setAffectedByWhichMainBase(0).setNoMainBase(0);
        } else {
            setCommandNet(true);
            getConstructInfoProp().setAffectedByWhichMainBase(1 << uniqueId).setNoMainBase(uniqueId);
        }
        LOGGER.info("{} setCommandMainNo. uniqueId:{}", getOwner(), uniqueId);
    }

    private OccupyInfoProp getOccupyInfoProp() {
        return getOwner().getProp().getOccupyinfo();
    }

    public MapBuildingType getType() {
        return getConstructInfoProp().getType();
    }

    public int getCurHp() {
        OccupyState occupyState = getOwner().getOccupyState();
        if (occupyState == OccupyState.TOS_AFTER_FIRE_RECOVER || occupyState == OccupyState.TOS_FIRE) {
            refreshDurability();
        }
        return getConstructInfoProp().getCurrentDurability();
    }

    public int getMaxHp() {
        return getConstructInfoProp().getMaxDurability();
    }

    public int getMainBaseNum() {
        return getConstructInfoProp().getNoMainBase();
    }

    private ConstructInfoProp getConstructInfoProp() {
        return getOwner().getProp().getConstructInfo();
    }

    public SceneClanEntity getOwnerSceneClan() {
        if (getOccupyInfoProp().getOwnerClanId() == 0) {
            return null;
        }
        return getOwner().getScene().getClanMgrComponent().getSceneClan(getOccupyInfoProp().getOwnerClanId());
    }

    public ConstClanTerritoryTemplate getConstClanTerritoryTemplate() {
        return ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
    }

    private int getCollisionRadiusByType(MapBuildingType type) {
        for (MapBuildingTemplate template : ResHolder.getInstance().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getType() == type) {
                return template.getCollisionRadius();
            }
        }
        // 应该假定是一定能取到的
        return 0;
    }
}

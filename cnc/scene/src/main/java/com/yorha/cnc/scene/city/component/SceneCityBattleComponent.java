package com.yorha.cnc.scene.city.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.event.GarrisonChangeEvent;
import com.yorha.cnc.battle.event.TroopEvent;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.*;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.army.BattleUnitPropChangeEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.component.ScenePlayerHospitalComponent;
import com.yorha.common.enums.qlog.battle.BattleAttackType;
import com.yorha.common.enums.qlog.battle.BattleRepDefenceType;
import com.yorha.common.enums.qlog.battle.BattleRepMainObjType;
import com.yorha.common.enums.qlog.battle.BattleRepResult;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.helper.GuardTowerHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import qlog.flow.QlogCncBattleReport;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;
import res.template.TroopTemplate;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SceneCityBattleComponent extends CityBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(SceneCityBattleComponent.class);

    public SceneCityBattleComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    protected ScenePlayerEntity getScenePlayer() {
        return (ScenePlayerEntity) getOwner().getScenePlayer();
    }

    @Override
    public void init() {
        super.init();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndSingleRelation, EndSingleBattleEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleUnitPropChange, BattleUnitPropChangeEvent.class);
    }

    @Override
    public void postInit() {
        if (getOwner().getProp().getWallState() != CityWallState.CS_NORMAL) {
            getScenePlayer().getWallComponent().onRestore();
        }
    }

    @Override
    protected void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        super.onSettleRoundEvent(event);
        // 向scenePlayer同步
        getScenePlayer().getSoldierMgrComponent().syncCityBattleSoldier(event.getDamageResult().getChildSoldierLoss().getOrDefault(getRoleId(), Lists.newArrayList()));
        // 防御塔减耐久
        SoldierLossDTO guardTowerLoss = event.getDamageResult().getGuardTowerLoss();
        if (guardTowerLoss != null && getScenePlayer().getWallComponent().getDefenseTowerId() > 0) {
            int towerCurHp = getScenePlayer().getWallComponent().getDefenseTowerCurHp();
            int decHealth = Math.min(towerCurHp, Math.max(0, guardTowerLoss.totalDead()));
            LOGGER.debug("city:{}, guardTower damaged:{}. towerCurHp:{}, decHealth:{}", getOwner(), guardTowerLoss, towerCurHp, decHealth);
            getScenePlayer().getWallComponent().decDefenseTowerHp(decHealth);
        }
    }

    protected void onEndSingleRelation(EndSingleBattleEvent event) {
        // 增加玩家战斗胜利失败的统计
        updateBattleRecordToPlayer(event);
        getScenePlayer().getLogisticMgrComponent().trySysAssist(event.getRoleRecord(), event.getOther(), event.getBattleType());
    }

    private void onBattleUnitPropChange(BattleUnitPropChangeEvent event) {
        LOGGER.debug("onBattleUnitChange. event:{}", event);
        if (!isInBattle()) {
            return;
        }
        if (event.getHeroList() != null && event.getHeroList().size() > 0) {
            for (Struct.Hero hero : event.getHeroList()) {
                LOGGER.debug("{} onHeroPropChange heroId={}", this, hero);
                if (getTroop().getMainHero().getHeroId() == hero.getHeroId()) {
                    getTroop().getMainHero().mergeChangeFromSs(hero);
                    BattleHero mainHero = getMainHero();
                    mainHero.refreshHeroByProp();
                } else if (getTroop().getDeputyHero().getHeroId() == hero.getHeroId()) {
                    getTroop().getDeputyHero().mergeChangeFromSs(hero);
                    BattleHero deputyHero = getBattleRole().getDeputyHero();
                    if (deputyHero != null) {
                        deputyHero.refreshHeroByProp();
                    } else {
                        // 副将为空的时候不应该有数据同步过来
                        LOGGER.error("onBattleUnitPropChange. deputy:{} battleRole:{}", getTroop().getDeputyHero(), getBattleRole());
                    }
                }
            }
        }
    }

    @Override
    public boolean beAttacked(ArmyEntity armyEntity, Long attackerPlayerId) {
        // 添加预警信息
        WarningType type = armyEntity.isRallyArmy() ? WarningType.WT_RallyAttack : WarningType.WT_Attack;
        getOwner().getInnerArmyComponent().addWarningItem(armyEntity, type);
        return super.beAttacked(armyEntity, attackerPlayerId);
    }

    /**
     * 组装守城战斗英雄，战斗机
     */
    @Override
    protected boolean buildGarrisonHeroOrMecha() {
        boolean flag = false;
        WallInfoProp sceneWallProp = getScenePlayer().getProp().getWall();
        LOGGER.info("{} refreshGarrisonInfo. {} {}", getOwner(), sceneWallProp.getMainHero(), sceneWallProp.getDeputyHero());
        // 主将
        final int mainHeroIdBefore = getTroop().getMainHero().getHeroId();
        if (replaceTroopHero(getTroop().getMainHero(), sceneWallProp.getMainHero())) {
            flag = true;
        }
        // 副将
        final int deputyHeroIdBefore = getTroop().getDeputyHero().getHeroId();
        if (replaceTroopHero(getTroop().getDeputyHero(), sceneWallProp.getDeputyHero())) {
            flag = true;
        }
        LOGGER.debug("{} refreshGarrisonInfo mainHero:{}->{} deputyHero:{}->{}",
                getOwner(),
                mainHeroIdBefore,
                getTroop().getMainHero().getHeroId(),
                deputyHeroIdBefore,
                getTroop().getDeputyHero().getHeroId());

        return flag;
    }

    /**
     * @return true:确实替换为了新的
     */
    private boolean replaceTroopHero(HeroProp troopHero, @Nullable HeroProp chosenHeroProp) {
        boolean replaced = false;
        if (chosenHeroProp == null) {
            if (troopHero.getHeroId() > 0) {
                replaced = true;
                troopHero.mergeFromDb(Struct.Hero.getDefaultInstance());
            }
        } else {
            if (troopHero.getHeroId() != chosenHeroProp.getHeroId()) {
                replaced = true;
                Struct.Hero.Builder builder = Struct.Hero.newBuilder();
                chosenHeroProp.copyToSs(builder);
                troopHero.mergeFromSs(builder.build());
            }
        }
        return replaced;
    }

    /**
     * 组装守城战斗士兵
     */
    @Override
    protected void buildGarrisonSoldier() {
        Map<Integer, SoldierProp> selfSoldierMap = getScenePlayer().getSoldierMgrComponent().buildGarrisonSoldier();
        CityGarrisonProp garrisonProp = getOwner().getProp().getGarrison();
        for (SoldierProp selfSoldier : selfSoldierMap.values()) {
            SoldierProp newSp = garrisonProp.addEmptySoldierMap(selfSoldier.getSoldierId());
            Soldier.copyProp(selfSoldier, newSp);
            getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_CITY_ARMY_SELF), newSp);
        }
        CityInnerArmyComponent innerArmyComponent = getOwner().getInnerArmyComponent();
        for (ArmyEntity innerArmy : innerArmyComponent.getInnerArmyList()) {
            for (SoldierProp innerArmySoldierProp : innerArmy.getProp().getTroop().getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(innerArmy.getEntityId(), SceneObjType.SOT_CITY_ARMY_OTHER), innerArmySoldierProp);
            }
            // 战斗发生了  战报有参与者一份
            addBattleEntityCache(innerArmy.getEntityId(), innerArmy.getPlayerId());
        }
        // 战斗发生了  战报也有主人一份
        addBattleEntityCache(getEntityId(), getOwner().getPlayerId());
        LOGGER.debug("city: {} buildGarrisonSoldier: {}", getEntityId(), getTroop().getTroop());
    }

    /**
     * 构建防御塔
     */
    @Override
    protected void buildGuardTower() {
        int defenseTowerId = getScenePlayer().getWallComponent().getDefenseTowerId();
        int towerTroopId = GuardTowerHelper.getGuardTowerTroopId(defenseTowerId);
        TroopTemplate troopTemplate = ResHolder.getInstance().findValueFromMap(TroopTemplate.class, towerTroopId);
        if (troopTemplate != null) {
            IntPairType soldierData = troopTemplate.getSoldierPairList().get(0);
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierData.getKey());
            if (soldierTemplate != null) {
                int towerMaxHp = getScenePlayer().getWallComponent().getDefenseTowerMaxHp();
                int towerCurHp = getScenePlayer().getWallComponent().getDefenseTowerCurHp();
                SoldierUnit.OwnerInfo ownerInfo = new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_CITY_ARMY_SELF);
                SoldierUnit towerUnit = new SoldierUnit(ownerInfo, SoldierData.fromProp(new SoldierProp().setSoldierId(soldierTemplate.getId()).setNum(towerCurHp)));
                getBattleRole().setGuardTower(new GuardTower(getBattleRole(), towerUnit, towerMaxHp, GuardTowerHelper.getGuardTowerLv(defenseTowerId)));
            }
        }
        getScenePlayer().getWallComponent().onCityEnterBattle();
    }

    /**
     * 行军拉出后  check守城队伍
     * 检查出去的士兵有没有占用战斗部队名额，重设num
     */
    @Override
    public void onArmyOut(Collection<Struct.Soldier> outSoldiers) {
        if (!isInBattle()) {
            return;
        }
        // recalculate soldier
        int totalOutNum = 0;
        final Int32SoldierMapProp garrisonSoldierMap = getOwner().getProp().getGarrison().getSoldierMap();
        for (Struct.Soldier outSoldier : outSoldiers) {
            SoldierProp garrisonSoldier = garrisonSoldierMap.get(outSoldier.getSoldierId());
            if (garrisonSoldier != null) {
                int oldNum = garrisonSoldier.getNum();
                garrisonSoldier.setNum(Math.max(0, oldNum - outSoldier.getNum()));
                totalOutNum += Math.min(oldNum, outSoldier.getNum());
            }
        }
        getOwner().getEventDispatcher().dispatch(new TroopEvent(getEntityId(),
                SoldierNumChangeReason.army_out,
                totalOutNum,
                getScenePlayer().getName(),
                getScenePlayer().getClanName(),
                getScenePlayer().getCardHeadSS()));
        LOGGER.debug("city: {} rebuildGarrisonSoldier: {}, aliveRate:{}", getOwner(), garrisonSoldierMap, getBattleRole().aliveSoldierRate());
    }

    /**
     * 行军回城后  check守城队伍
     * 当前战斗部队未满需要补充部队
     */
    @Override
    public void onArmyReturn(Collection<SoldierProp> soldierPropList) {
        if (!isInBattle()) {
            return;
        }
        onSoldierAdd(soldierPropList, SoldierNumChangeReason.army_return);
    }

    @Override
    public void onGarrisonChange() {
        if (!isInBattle()) {
            return;
        }
        boolean realChange = buildGarrisonHeroOrMecha();
        if (realChange) {
            getOwner().getEventDispatcher().dispatch(new GarrisonChangeEvent());
        }
    }

    @Override
    public void onSoldierAdd(Collection<SoldierProp> soldierPropList, SoldierNumChangeReason reason) {
        if (!isInBattle()) {
            return;
        }
        int maxSoldierNum = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getCityBattleMaxSoldier();
        int total = cityOwnerGarrisonSoldierTotal();
        int maxCanAdd = maxSoldierNum - total;
        if (maxCanAdd <= 0) {
            return;
        }
        List<SoldierProp> soldierProps = new ArrayList<>(soldierPropList);
        soldierProps.sort(Comparator.comparing(SoldierProp::getSoldierId).reversed());

        CityGarrisonProp garrisonProp = getOwner().getProp().getGarrison();
        int totalAddNum = 0;
        // 遍历回城的士兵list
        for (SoldierProp prop : soldierProps) {
            if (maxCanAdd <= 0) {
                return;
            }
            int addNum = Math.min(Soldier.aliveCountOf(prop), maxCanAdd);
            SoldierProp existSp = garrisonProp.getSoldierMapV(prop.getSoldierId());
            if (existSp == null) {
                existSp = garrisonProp.addEmptySoldierMap(prop.getSoldierId());
                existSp.setNum(addNum);
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(getEntityId(), SceneObjType.SOT_CITY_ARMY_SELF), existSp);
            } else {
                getBattleRole().addSoldier4ExistUnit(this.getBattleRole(), prop.getSoldierId(), addNum);
            }
            maxCanAdd -= addNum;
            totalAddNum += addNum;
        }
        // 构建总troop
        getBattleRole().refreshTroop();
        getOwner().getEventDispatcher().dispatch(new TroopEvent(getEntityId(),
                reason,
                totalAddNum,
                getScenePlayer().getName(),
                getScenePlayer().getClanName(),
                getScenePlayer().getCardHeadSS()));
        LOGGER.debug("city: {} rebuildGarrisonSoldier: {} {}, aliveRate:{}", getEntityId(), getTroop().getTroop(), maxCanAdd, getBattleRole().aliveSoldierRate());
    }

    private int cityOwnerGarrisonSoldierTotal() {
        Collection<SoldierProp> garrisonSoldiers = getOwner().getProp().getGarrison().getSoldierMap().values();
        return garrisonSoldiers.stream().mapToInt(SoldierProp::getNum).sum();
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
        if (!battleResult.alive) {
            getOwner().getScenePlayer().tellPlayer(SsPlayerMisc.MainCityDefendLoseCmd.getDefaultInstance());
        }
        // 释放轻伤的占用
        getScenePlayer().getSoldierMgrComponent().onCityLeaveBattle(getOwner().getProp().getGarrison().getSoldierMap().values());
        int deadCount = getOwner().getProp().getBattle().getHospitalFullCauseDeadCount();
        if (deadCount > 0) {
            getScenePlayer().getSoldierMgrComponent().sendHospitalFullDeadMail(deadCount);
            getOwner().getProp().getBattle().setHospitalFullCauseDeadCount(0);
        }
        notifyScenePlayerEndAllRelation(battleResult);
    }

    @Override
    public void trySendRecordMail(Set<Long> playerIds, BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
        batchSendRecordMail(playerIds, recordAllProp, alive, anyEnemyAlive);
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {
        BattleRecordRoleSummaryProp selfSummary = recordAllProp.getSelfSummary();
        BattleRecordRoleProp selfRoleProp = getSelfOrEnemyRole(recordAllProp, false);
        if (selfRoleProp != null) {
            selfSummary.setClanName(selfRoleProp.getClanName());
            selfSummary.getCardHead().mergeFromSs(selfRoleProp.getCardHead().getCopySsBuilder().build());
        } else {
            selfSummary.setClanName(getOwner().getProp().getClanSname());
            selfSummary.getCardHead().mergeFromSs(getOwner().getProp().getCardHead().getCopySsBuilder().build());
        }
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        // 自己
        roleRecord.addMember(buildRoleMemberRecord());
        // 增援部队
        for (ArmyEntity inRallyArmy : getOwner().getInnerArmyComponent().getInnerArmyList()) {
            roleRecord.addMember(inRallyArmy.getBattleComponent().buildRoleMemberRecord());
        }
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getEntityId())
                .setPlayerId(getOwner().getPlayerId())
                .setClanName(getOwner().getProp().getClanSname())
                .setCardHead(getOwner().getProp().getCardHead().getCopySsBuilder().build());
        HeroProp mainHeroProp = getBattleRole().getMainHero() != null ? getBattleRole().getMainHero().getHeroProp() : new HeroProp();
        HeroProp deputyHeroProp = getBattleRole().getDeputyHero() != null ? getBattleRole().getDeputyHero().getHeroProp() : new HeroProp();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    @Override
    protected StructMail.MailShowTitle getMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        StructMail.MailShowTitle.Builder builder = StructMail.MailShowTitle.newBuilder();
        BattleRecordRoleProp enemyRole = getSelfOrEnemyRole(recordAllProp, true);
        // title
        builder.setTitleKey(getBattleRecordMailTitle(alive, anyEnemyAlive, enemyRole));

        // sub title
        if (enemyRole != null) {
            boolean isSuperWeaponAtk = enemyRole.getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill.getNumber();
            if (isSuperWeaponAtk) {
                ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                // 超武战报
                String subTitleKey = temple.getBattleRecordSub16();
                builder.setSubTitleKey(subTitleKey);

                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayHero(enemyRole.getMainHero().getHeroId()));
            } else {
                // 防守基地
                ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
                String subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub2() : temple.getBattleRecordSub1();
                builder.setSubTitleKey(subTitleKey);

                // 您的基地遭到了%s的攻击
                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(enemyRole.getCardHead().getName()));
            }
        }
        return builder.build();
    }


    @Override
    protected QlogCncBattleReport getBattleRepFlow(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        QlogCncBattleReport battleRepFlow = new QlogCncBattleReport();
        List<ArmyEntity> innerArmies = getOwner().getInnerArmyComponent().getInnerArmyList();
        battleRepFlow.setDtEventTime(TimeUtils.now2String())
                .setBattleReportId(String.valueOf(recordAllProp.getRecordId()))
                .setBattleId(QlogUtils.transCollection2ArrayString(recordAllProp.getSingleRecordList().stream().map(BattleRecordOneProp::getBattleId).collect(Collectors.toList())))
                .setBattleReportTimeStart(TimeUtils.timeStampMs2String(recordAllProp.getStartMillis()))
                .setBattleReportTimeEnd(TimeUtils.timeStampMs2String(recordAllProp.getEndMillis()))
                .setBattleReportResult(BattleRepResult.getBattleResult(alive, anyEnemyAlive).ordinal())
                .setMainObjectType(BattleRepMainObjType.getMainObjType(!innerArmies.isEmpty()).ordinal())
                .setDefenceOrNot(BattleRepDefenceType.DEFENCE_CITY.ordinal())
                .setMainObjectId(String.valueOf(getOwner().getPlayerId()))
                .setEnemyId("")
                .setMainObjectBuildingId(String.valueOf(getOwner().getEntityId()));
        return battleRepFlow;
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        QlogCncBattle battleFlow = super.getBattleFlow(alive, isEnemyAlive, record);
        if (null == battleFlow) {
            return null;
        }
        // 一定是玩家建筑驻防部队
        battleFlow.setDefenceOrNot(BattleRepDefenceType.DEFENCE_CITY.ordinal());
        // 一定是防守方
        battleFlow.setAttackOrNot(BattleAttackType.DEFENCE.ordinal());
        // 设置主体建筑id
        battleFlow.setMainObjectBuildingID(String.valueOf(getOwner().getEntityId()));
        // 设置战斗主体
        List<ArmyEntity> innerArmies = getOwner().getInnerArmyComponent().getInnerArmyList();
        battleFlow.setMainObjectType(BattleRepMainObjType.getMainObjType(!innerArmies.isEmpty()).ordinal())
                .setMainObjectID(String.valueOf(getOwner().getPlayerId()));
        return battleFlow;
    }

    @Override
    public void afterGarrisonReplaced(BattleRecordAllProp recordAllProp) {
        super.afterGarrisonReplaced(recordAllProp);
        trySendRecordMail(getBattleRecordPlayerIds(), recordAllProp, getBattleRole().hasAnyAlive(), true);
    }

    @Override
    public Set<Long> getAllEnemyClan() {
        Set<Long> ret = Sets.newHashSet();
        for (ArmyEntity armyEntity : getScenePlayer().getArmyMgrComponent().getMyArmyList()) {
            if (armyEntity.getBattleComponent().isInBattle()) {
                ret.addAll(armyEntity.getBattleComponent().getBattleRole().getAllEnemyClan());
            }
        }
        if (getScenePlayer().getMainCity().getBattleComponent().isInBattle()) {
            ret.addAll(getScenePlayer().getMainCity().getBattleComponent().getBattleRole().getAllEnemyClan());
        }
        return ret;
    }

    @Override
    public Set<Long> getAllEnemyPlayerId() {
        Set<Long> ret = Sets.newHashSet();
        for (ArmyEntity armyEntity : getScenePlayer().getArmyMgrComponent().getMyArmyList()) {
            if (armyEntity.getBattleComponent().isInBattle()) {
                ret.addAll(armyEntity.getBattleComponent().getBattleRole().getAllEnemyPlayerId());
            }
        }
        if (getScenePlayer().getMainCity().getBattleComponent().isInBattle()) {
            ret.addAll(getScenePlayer().getMainCity().getBattleComponent().getBattleRole().getAllEnemyPlayerId());
        }
        return ret;
    }

    @Override
    public void handleSoldierLoss(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        sendSevereWound2Hospital(lossDetail);
    }

    private void sendSevereWound2Hospital(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        Map<Long, List<Struct.PlayerHospitalSoldier>> go2HospitalMap = buildSevereSoldierData(lossDetail);
        go2HospitalMap.forEach((childRoleId, childSevereWound) -> {
            if (!childSevereWound.isEmpty()) {
                if (childRoleId == getEntityId()) {
                    // city自身防守部队
                    goToHospital(childSevereWound, lossDetail.get(childRoleId));
                } else {
                    ArmyEntity innerArmy = getOwner().getInnerArmyComponent().findInnerArmy(childRoleId);
                    if (innerArmy == null) {
                        WechatLog.error("childArmy not in city!!!city:{} innerArmy:{}", getOwner(), childRoleId);
                    } else {
                        innerArmy.getBattleComponent().goToHospital(childSevereWound, lossDetail.get(childRoleId));
                    }
                }
            }
        });
    }

    private void goToHospital(List<Struct.PlayerHospitalSoldier> severeSoldier, Map<Integer, SoldierLossData> beforeHospitalData) {
        ScenePlayerHospitalComponent hospital = getScenePlayer().getHospitalComponent();
        goToHospital(hospital, getOwner().getProp().getBattle(), severeSoldier, beforeHospitalData);
    }

    @Override
    public void handlePlunderResult(SsPlayerMisc.PlunderResult plunderResult, BattleRelation relation) {
        // 掠夺资源损失
        if (plunderResult != null) {
            handleResourceLoss(plunderResult, relation);
        }
        trySendBattleRecordAfterPlunder(relation);
    }

    private void handleResourceLoss(SsPlayerMisc.PlunderResult plunderResult, BattleRelation relation) {
        Map<CurrencyType, Long> loss = new HashMap<>();
        for (Struct.Currency value : plunderResult.getLossRes().getDatasMap().values()) {
            CurrencyType currencyType = CurrencyType.forNumber(value.getType());
            if (currencyType == null) {
                LOGGER.error("relation:{}, calc plunder error, currencyType:{} not found.", relation, value.getType());
                continue;
            }
            loss.put(currencyType, -value.getCount());
        }
        // 资源被掠夺,写战报
        if (!loss.isEmpty()) {
            BattleRecord.RoleRecord myRoleRecord = relation.getContext().getRoleRecord(getBattleRole().getRoleId());
            myRoleRecord.fillPlunder(getRoleId(), loss, PlunderResultEnum.PRE_LOSS);
        }
    }

    @Override
    public void askCityToPlunder(Map<Long, Map<Long, SsPlayerMisc.PlunderWeight>> plunderWeightMap) {
        if (plunderWeightMap.isEmpty()) {
            return;
        }

        SsPlayerMisc.OnCityBattleEndAsk.Builder ask = SsPlayerMisc.OnCityBattleEndAsk.newBuilder();
        for (Map.Entry<Long, Map<Long, SsPlayerMisc.PlunderWeight>> entry : plunderWeightMap.entrySet()) {
            SsPlayerMisc.PlunderAsk.Builder builder = SsPlayerMisc.PlunderAsk.newBuilder();
            builder.putAllPlunderWeight(entry.getValue());
            ask.putPlunderAsk(entry.getKey(), builder.build());
        }

        BattleGround battleGround = getOwner().getScene().getBattleGroundComponent().getBattleGround();
        getOwner().ownerActor().<SsPlayerMisc.OnCityBattleEndAns>askPlayer(getScenePlayer().getZoneId(), getOwner().getPlayerId(), ask.build())
                .onComplete((ans, err) -> {
                    if (err != null) {
                        LOGGER.error("role:{} ask plunder failed.", this, err);
                        for (Long relationId : ask.getPlunderAskMap().keySet()) {
                            BattleRelation relation = battleGround.plunderRelations.remove(relationId);
                            if (relation != null) {
                                relation.handlePlunderResult(null);
                            }
                        }
                    } else {
                        for (Map.Entry<Long, SsPlayerMisc.PlunderResult> entry : ans.getPlunderResultMap().entrySet()) {
                            BattleRelation relation = battleGround.plunderRelations.remove(entry.getKey());
                            if (relation != null) {
                                relation.handlePlunderResult(entry.getValue());
                            } else {
                                LOGGER.error("role:{} cannot find relation. enemyId:{}", this, entry.getKey());
                            }
                        }
                    }
                });
    }

    @Override
    public PlunderProtectReason getBePlunderReasonOrNull() {
        return null;
    }


    @Override
    public boolean isMustNtfType() {
        return true;
    }
}

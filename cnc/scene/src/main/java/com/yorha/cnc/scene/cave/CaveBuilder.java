package com.yorha.cnc.scene.cave;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.game.gen.prop.CaveProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class CaveBuilder extends SceneObjBuilder<CaveEntity, CaveProp> {
    public CaveBuilder(SceneEntity sceneEntity, long eid, CaveProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public BuildingTransformComponent transformComponent(CaveEntity owner) {
        return new BuildingTransformComponent(owner, owner, getPointProp());
    }

}

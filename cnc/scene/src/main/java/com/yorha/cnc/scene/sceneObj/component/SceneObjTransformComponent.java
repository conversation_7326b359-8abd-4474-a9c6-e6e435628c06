package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.model.InteractionDistanceTemplateService;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Line;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.vector.Vector2f;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SoldierTypeTemplate;

import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述场景物体位置的组件
 *
 * <AUTHOR>
 */
public class SceneObjTransformComponent extends SceneObjComponent<SceneObjEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjTransformComponent.class);

    /**
     * 持有的prop的ref 改变会走属性同步
     */
    private final PointProp pointPropRef;
    /**
     * 出生点
     */
    protected Point bornPoint;
    /**
     * 战斗原点
     */
    private Point battlePoint;

    private Point fixBattlePoint;
    /**
     * 当前真实的位置
     */
    private Point curPoint;
    /**
     * 围攻的初始基准向量  以第一个进入者为准
     */
    private Vector2f baseBesiegeVector = null;
    /**
     * 下标:围攻位置号 value:围攻者id
     */
    private long[] besiegeIndexArray;
    /**
     * 物体模型圈大小
     */
    private int modelCircle = 0;
    /**
     * 占据我围攻位置 正在打我的obj们
     */
    protected Set<Long> battleMeObj;

    public SceneObjTransformComponent(SceneObjEntity owner, PointProp pointRef) {
        super(owner);
        this.pointPropRef = pointRef;
        this.curPoint = Point.valueOf(pointPropRef.getX(), pointPropRef.getY());
    }

    @Override
    public void init() {
        initBesiegeIndexArray();
        this.bornPoint = getCurPoint();
    }

    @Override
    public void postInit() {
        resetModelRadius();
    }

    public Point getBornPoint() {
        return bornPoint;
    }

    public Point getBattlePoint() {
        if (fixBattlePoint != null) {
            return fixBattlePoint;
        }
        return this.battlePoint;
    }

    public void setFixBattlePoint(Point point) {
        this.fixBattlePoint = point;
    }

    public void enterBattle() {
        this.battlePoint = getCurPoint();
    }


    public boolean onBattlePlace() {
        return Objects.equals(battlePoint, getCurPoint());
    }


    /**
     * 传入的是米单位的!!!
     */
    public Circle getRange(int radius) {
        Point curPoint = getCurPoint();
        return Circle.valueOf(curPoint.getX(), curPoint.getY(), UnitConvertUtils.meterToCm(radius));
    }

    /**
     * 形状  用于加入AOI 视野格子的占据  默认都是自己的坐标点
     */
    public Shape getSelfShape() {
        return getCurPoint();
    }

    /**
     * 加入动态寻路阻挡
     */
    public void addCollision() {
    }

    public void changeCollision() {
        removeCollision();
        addCollision();
    }

    /**
     * 移除动态阻挡
     */
    public void removeCollision() {
        getOwner().getScene().getPathFindMgrComponent().removeDynamicCircleCollision(getEntityId());
    }

    /**
     * 获取迁城阻挡半径
     */
    public int getCityMoveCollisionRadius() {
        return 0;
    }

    public int getPathCollisionRadius() {
        return 0;
    }
    // ------------------------------------- 围攻相关 -------------------------------------

    public Point getCurPoint() {
        return curPoint;
    }

    /**
     * 改变位置
     */
    public void changePoint(Point newPoint) {
        changePoint(newPoint, true);
    }

    /**
     * 改变位置
     *
     * @param newPoint 新位置
     * @param isSync   是否设置属性并同步客户端
     */
    public void changePoint(Point newPoint, boolean isSync) {
        if (newPoint == null) {
            WechatLog.error("{} try changePoint is null", getOwner());
            return;
        }
        curPoint = newPoint;
        if (isSync) {
            pointPropRef.setX(newPoint.getX()).setY(newPoint.getY());
        }
        // 登记本轮移动过
        getOwner().getScene().getTickMgrComponent().registerChangePointEntity(getEntityId());
        getOwner().getAoiNodeComponent().refreshAoi(getEntityType() == EntityAttrOuterClass.EntityType.ET_City ? CommonEnum.SceneObjectNtfReason.SONR_AOI : null);
    }

    private void initBesiegeIndexArray() {
        besiegeIndexArray = new long[GameLogicConstants.BESIEGE_MAX_SIZE];
        baseBesiegeVector = null;
    }

    /**
     * 退出围攻
     *
     * @param casterId 退出围攻者的id
     */
    public void exitBesiege(long casterId) {
        if (battleMeObj != null) {
            battleMeObj.remove(casterId);
        }
        int besiegeIndex = getBesiegeIndexById(casterId);
        if (besiegeIndex < 0) {
            return;
        }
        besiegeIndexArray[besiegeIndex] = 0;
        for (long l : besiegeIndexArray) {
            if (l > 0) {
                return;
            }
        }
        // 围攻者都退出了
        initBesiegeIndexArray();
    }

    /**
     * 通过id获取其当前占据的围攻位置号
     *
     * @param id 围攻者id
     * @return 位置号 -1代表没有占据位置
     */
    private int getBesiegeIndexById(long id) {
        for (int i = 0; i < besiegeIndexArray.length; i++) {
            if (besiegeIndexArray[i] == id) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 获取围攻一片角度大小
     */
    private int getSliceAngle() {
        return Constants.ANGLE_MAX_VALUE / GameLogicConstants.BESIEGE_MAX_SIZE;
    }

    /**
     * 通过围攻位置号获取对应向量
     */
    private Vector2f getVectorBySliceIndex(int sliceIndex) {
        // 基础向量旋转
        int rotateAngle = sliceIndex * getSliceAngle();
        return baseBesiegeVector.rotate(rotateAngle);
    }

    public Point occupyAndGetBesiegePoint(SceneObjEntity caster) {
        long casterId = caster.getEntityId();
        if (battleMeObj != null) {
            battleMeObj.add(casterId);
        }
        Point casterPoint = caster.getCurPoint();
        Point selfPoint = getCurPoint();
        int battleDistance = caster.getTransformComponent().getChaseDistance(getOwner());
        if (baseBesiegeVector == null) {
            // 第一位攻击者确定围攻基准向量
            baseBesiegeVector = Vector2f.getVectorFromPointToPoint(selfPoint, casterPoint);
            Point point = selfPoint.getPointWithVectorAndDis(baseBesiegeVector, battleDistance);
            if (ErrorCode.isOK(getOwner().getScene().getPathFindMgrComponent().isPointWalkable(point))) {
                besiegeIndexArray[0] = casterId;
                return point;
            }
            besiegeIndexArray[0] = -1;
            point = tryOccupyBesiegeIndex(0, battleDistance, casterId);
            return point == null ? casterPoint : point;
        }
        int besiegeIndex = getBesiegeIndexById(casterId);
        if (besiegeIndex >= 0) {
            // 已经占据围攻位置了
            Vector2f vector2f = getVectorBySliceIndex(besiegeIndex);
            Point point = selfPoint.getPointWithVectorAndDis(vector2f, battleDistance);
            if (!ErrorCode.isOK(getOwner().getScene().getPathFindMgrComponent().isPointWalkable(point))) {
                return casterPoint;
            }
            return point;
        }
        // 第一次占据
        // 获取角度
        Vector2f vector = Vector2f.getVectorFromPointToPoint(selfPoint, casterPoint);
        float intersectionAngle2 = Vector2f.getIntersectionAngle2(baseBesiegeVector, vector);
        // 找到插入的位置
        Point point = tryOccupyBesiegeIndex(intersectionAngle2, battleDistance, casterId);
        return point == null ? casterPoint : point;
    }

    /**
     * 获取空缺的围攻位置号
     */
    private Point tryOccupyBesiegeIndex(float intersectionAngle, int battleDistance, long casterId) {
        float index = intersectionAngle / getSliceAngle();
        // 最近贴合位置
        int firstJudgeIndex = Math.round(index);
        // 开始寻找的方向
        int dir = index > firstJudgeIndex ? 1 : -1;
        int maxSize = GameLogicConstants.BESIEGE_MAX_SIZE;
        if (firstJudgeIndex == maxSize) {
            firstJudgeIndex = 0;
        }
        LOGGER.debug("besiegeIndexArray:{} firstJudgeIndex:{}", besiegeIndexArray, firstJudgeIndex);
        if (firstJudgeIndex < 0 || firstJudgeIndex > maxSize - 1) {
            return null;
        }
        int curJudgeIndex = firstJudgeIndex;
        PathFindMgrComponent component = getOwner().getScene().getPathFindMgrComponent();
        for (int i = 0; i < maxSize; i++) {
            if (i % 2 == 0) {
                // 奇数序列
                curJudgeIndex -= dir * i;
            } else {
                curJudgeIndex += dir * i;
            }
            curJudgeIndex = curbIndex(curJudgeIndex, maxSize);
            if (besiegeIndexArray[curJudgeIndex] != 0) {
                continue;
            }
            Vector2f vectorBySliceIndex = getVectorBySliceIndex(curJudgeIndex);
            Point point = getCurPoint().getPointWithVectorAndDis(vectorBySliceIndex, battleDistance);
            if (!ErrorCode.isOK(component.isPointWalkable(point))) {
                besiegeIndexArray[curJudgeIndex] = -1;
                continue;
            }
            // 如果首选位置是不可达点 那就用最近可达点覆盖下
            if (besiegeIndexArray[firstJudgeIndex] == -1) {
                firstJudgeIndex = curJudgeIndex;
            }
            besiegeIndexArray[curJudgeIndex] = casterId;
            // 不用调整位置 直接返回
            if (firstJudgeIndex == curJudgeIndex) {
                return point;
            }
            // 调整其他人位置
            int finalIndex = adjustBesiegeIndex(dir, firstJudgeIndex, curJudgeIndex, casterId);
            vectorBySliceIndex = getVectorBySliceIndex(finalIndex);
            point = getCurPoint().getPointWithVectorAndDis(vectorBySliceIndex, battleDistance);
            if (!ErrorCode.isOK(component.isPointWalkable(point))) {
                return null;
            }
            return point;
        }
        return null;
    }

    /**
     * 调整
     *
     * @param deviation       偏向 1为偏向顺时针 -1为偏向逆时针
     * @param firstJudgeIndex 计算最近位置索引
     * @param curJudgeIndex   计算空闲位置索引
     * @param casterId        占位者id
     * @return 真实占据位置
     */
    private int adjustBesiegeIndex(int deviation, int firstJudgeIndex, int curJudgeIndex, long casterId) {
        // 自己主攻的人过掉，不调整他
        long exceptedId = getOwner().getBattleComponent().getBattleRole().getTargetId();
        int dir = 1;
        int half = GameLogicConstants.BESIEGE_MAX_SIZE / 2;
        int diff = curJudgeIndex - firstJudgeIndex;

        // 算怎么遍历
        if (Math.abs(diff) < half) {
            dir = diff < 0 ? 1 : -1;
        } else if (Math.abs(diff) > half) {
            dir = diff > 0 ? 1 : -1;
        } else {
            dir = -deviation;
        }
        int maxSize = GameLogicConstants.BESIEGE_MAX_SIZE;
        // 本身的偏向与遍历方向不同，那需要处理下首选的   处理完了看看一致不
        if (deviation != dir) {
            firstJudgeIndex = curbIndex(firstJudgeIndex + deviation, maxSize);
            if (firstJudgeIndex == curJudgeIndex) {
                return firstJudgeIndex;
            }
        }
        int finalIndex = firstJudgeIndex;
        // 先看下这个遍历方向有没有阻挡  有的话看另一个方向有没有阻挡  也有的话自己走
        boolean isChange = false;
        for (int i = curJudgeIndex; curbIndex(i, maxSize) != firstJudgeIndex; i += dir) {
            if (besiegeIndexArray[curJudgeIndex] == -1) {
                dir = -dir;
                isChange = true;
                break;
            }
        }
        if (isChange) {
            for (int i = curJudgeIndex; curbIndex(i, maxSize) != firstJudgeIndex; i += dir) {
                if (besiegeIndexArray[curJudgeIndex] == -1) {
                    return curJudgeIndex;
                }
            }
        }
        for (int i = curJudgeIndex; curbIndex(i, maxSize) != firstJudgeIndex; i += dir) {
            i = curbIndex(i, maxSize);
            int nextIndex = curbIndex(i + dir, maxSize);
            // 如果那个人是面向的人
            if (besiegeIndexArray[nextIndex] == exceptedId) {
                // 如果面向的人就是本来的首选位置
                if (nextIndex == firstJudgeIndex) {
                    finalIndex = i;
                    break;
                }
                nextIndex = curbIndex(nextIndex + dir, maxSize);
                besiegeIndexArray[i] = besiegeIndexArray[nextIndex];
                besiegeIndexArray[nextIndex] = 0;
                adjustBesiegePoint(besiegeIndexArray[i]);
                i += dir;
                continue;
            }
            long adjustPlayerId = besiegeIndexArray[nextIndex];
            besiegeIndexArray[i] = adjustPlayerId;
            besiegeIndexArray[nextIndex] = 0;
            adjustBesiegePoint(adjustPlayerId);
        }
        besiegeIndexArray[finalIndex] = casterId;
        return finalIndex;
    }

    /**
     * 将索引约束在0-max之间
     */
    private int curbIndex(int index, int maxSize) {
        if (index >= maxSize) {
            return index - maxSize;
        } else if (index < 0) {
            return index + maxSize;
        }
        return index;
    }

    /**
     * 调整合围插入过程中被波及到的army们
     */
    protected void adjustBesiegePoint(long id) {
        SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(id);
        if (sceneObj == null) {
            return;
        }
        sceneObj.getMoveComponent().occupyAndMoveToBesiegePoint(getOwner(), true, null);
    }

    public Set<Long> getBattleMeObj() {
        if (battleMeObj == null) {
            return null;
        }
        return new HashSet<>(battleMeObj);
    }

    /**
     * 重设模型圈半径
     */
    public void resetModelRadius() {

    }

    /**
     * 获取模型圈半径（根据标识选择是否重初始化）
     */
    public int getModelRadius() {
        return modelCircle;
    }

    public void setModelRadius(int modelCircle) {
        this.modelCircle = modelCircle;
    }

    /**
     * 获取跟随移动的坐标点
     *
     * @param otherPoint 对方的坐标
     * @param selfPoint  自己的坐标
     */
    public Point getFollowPoint(Point otherPoint, Point selfPoint) {
        if (selfPoint == null) {
            selfPoint = getCurPoint();
        }
        if (otherPoint.equals(selfPoint)) {
            return selfPoint;
        }
        Line line = Line.valueOf(selfPoint, otherPoint);
        int routingCircle = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getRoutingCircle();
        return line.getPointByDisFromSrc(getModelRadius() + routingCircle);
    }

    /**
     * 获取非战斗的交互距离
     */
    public int getInteractionDistance(SceneObjEntity target, TroopInteractionType type) {
        int config = ResHolder.getResService(InteractionDistanceTemplateService.class).getInteractionDistance(type).getDistanceOfAction();
        return Math.max(0, target.getTransformComponent().getModelRadius() + getModelRadius() + config);
    }


    /**
     * 获取距离目标的战斗攻击距离   用于计算移动到目标的终点位置 和 启动追击的距离
     */
    public int getChaseDistance(SceneObjEntity target) {
        int dis = target.getTransformComponent().getModelRadius() + getModelRadius() + ResHolder.getResService(ConstKVResService.class).getTemplate().getStandardRange();
        if (getOwner().getMoveComponent() != null && getOwner().getMoveComponent().isParallelMoving()) {
            dis += ResHolder.getResService(ConstKVResService.class).getTemplate().getParallelDistance();
        }
        return Math.max(0, dis);
    }

    /**
     * 获取士兵类型数量
     */
    public int getSoldierTypeSize(Collection<SoldierProp> soldierProps) {
        return soldierProps.stream()
                .collect(Collectors
                        .groupingBy(it -> ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, it.getSoldierId()).getSoldierType()))
                .keySet()
                .size();
    }

    /**
     * 获取从建筑中离开的出去点  根据方向和模型圈半径
     */
    public Point getLeavePosition(ArmyEntity armyEntity, Point targetPoint) {
        // 如果没有  就不管了
        if (targetPoint == null) {
            return null;
        }
        if (targetPoint.equals(getCurPoint())) {
            return targetPoint;
        }
        Line line = Line.valueOf(getOwner().getCurPoint(), targetPoint);
        return line.getPointByDisFromSrc(getModelRadius());
    }

    /**
     * 获取被进攻时 进攻点的位置
     */
    public Point getBeBattlePoint(Point srcPoint, long clanId, int searchTag) {
        return getCurPoint();
    }
}

package com.yorha.cnc.scene.common;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.enums.DirectionEnum;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.map.BigSceneResService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BigSceneSearchTemplate;
import res.template.MonsterTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class CreateMonsterHelper {
    private static final Logger LOGGER = LogManager.getLogger(CreateMonsterHelper.class);

    /**
     * 搜索特定等级野怪触发的补怪逻辑
     *
     * @param sceneEntity  sceneEntity
     * @param monsterLevel 野怪等级
     * @return
     */
    public static MonsterEntity createMonsterForSearch(SceneEntity sceneEntity, long playerId, int monsterLevel) {
        LOGGER.info("search add monster {} {}", playerId, monsterLevel);
        if (sceneEntity.isDestroy()) {
            return null;
        }
        AbstractScenePlayerEntity scenePlayer = sceneEntity.getPlayerMgrComponent().getScenePlayer(playerId);
        if (scenePlayer == null) {
            return null;
        }
        BigSceneSearchTemplate searchTemplate = ResHolder.getResService(BigSceneResService.class).getSearchTemplate(monsterLevel);
        // 生成搜索范围列表
        List<Integer> rangeList = genRangeList(searchTemplate.getMonsterMinDis(), searchTemplate.getMonsterMaxDis(), searchTemplate.getPara());
        // 选取出生点
        Point bornPoint = selectBornPoint(sceneEntity, scenePlayer, rangeList);
        if (bornPoint == null) {
            LOGGER.warn("search monster fail, scene:{} mainCity:{} monsterLevel:{}", sceneEntity.getEntityId(), scenePlayer.getMainCity().getCurPoint(), monsterLevel);
            return null;
        }
        // 生成野怪
        int storyId = sceneEntity.ownerActor().getStoryId();
        List<MonsterTemplate> monsterTemplates = ResHolder.getResService(BigSceneResService.class).getMonsterTemplates(CommonEnum.MonsterCategory.BIG_SCENE_ACTIVE, CommonEnum.SceneObjQuality.NORMAL, monsterLevel, storyId);
        MonsterTemplate monsterTemplate = RandomUtils.randomList(monsterTemplates);
        SceneObjSpawnParam param = new SceneObjSpawnParam();
        param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(searchTemplate.getMonsterLifeTime()));
        param.setCreateType(CommonEnum.MonsterCreateType.SYSTEM_ADD);
        MonsterEntity monster = MonsterFactory.initMonster(sceneEntity, monsterTemplate.getId(), bornPoint, param);
        if (monster != null) {
            monster.addIntoScene();
        }
        return monster;
    }
    
    /**
     * 生成搜索半径列表，记录分割点，包含头尾
     */
    private static List<Integer> genRangeList(int minDis, int maxDis, int cycles) {
        int interval = (maxDis - minDis) * 100;
        int range = interval / cycles;
        List<Integer> rangeList = new ArrayList<>(cycles + 1);
        rangeList.add(minDis * 100);
        for (int part = 1; part < cycles; part++) {
            rangeList.add(minDis * 100 + range * part);
        }
        rangeList.add(maxDis * 100);
        LOGGER.debug("CreateMonsterHelper genSearchRangeList, minDis={}, maxDis={}, rangeLis={}", minDis, maxDis, rangeList);
        return rangeList;
    }

    private static Point selectBornPoint(SceneEntity sceneEntity, AbstractScenePlayerEntity abstractScenePlayerEntity, List<Integer> rangeList) {
        Point mainCity = abstractScenePlayerEntity.getMainCity().getCurPoint();
        List<DirectionEnum> dirList = Arrays.asList(DirectionEnum.values());
        Collections.shuffle(dirList);
        Point bornPoint = null;
        PathFindMgrComponent pathFindMgrComponent = sceneEntity.getPathFindMgrComponent();
        for (int index = 0; index < rangeList.size() - 1; index++) {
            int dis = RandomUtils.randomBetween(rangeList.get(index), rangeList.get(index + 1));
            LOGGER.debug("AddMonster, at round {}, monster dis={}", index + 1, dis);
            for (DirectionEnum direction : dirList) {
                int x = mainCity.getX() + direction.getAddX() * dis;
                int y = mainCity.getY() + direction.getAddY() * dis;
                Point point = Point.valueOf(x, y);
                if (!pathFindMgrComponent.isPointDynamicWalkable(point) || !pathFindMgrComponent.isPointStaticWalkable(point)) {
                    continue;
                }
                bornPoint = Point.valueOf(x, y);
                break;
            }
            if (bornPoint != null) {
                LOGGER.info("search loop add monster {} {}", abstractScenePlayerEntity.getPlayerId(), index + 1);
                break;
            }
        }
        return bornPoint;
    }
}

package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class StartOccupy implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int partId = Integer.parseInt(args.get("partId"));
        MapBuildingEntity mapBuilding = actor.getScene().getBuildingMgrComponent().getMapBuilding(partId);
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        long clanId = scenePlayer.getClanId();
        if (clanId == 0) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        if (mapBuilding.getOccupyState() != CommonEnum.OccupyState.TOS_NEUTRAL) {
            throw new GeminiException("不是中立状态无法使用");
        }
        mapBuilding.getOccupyComponent().startOccupy(clanId);
    }

    @Override
    public String showHelp() {
        return "StartOccupy partId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}


package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.common.enums.qlog.battle.BattleAttackType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.proto.CommonEnum;

/**
 * 医院的数据放在scenePlayer上，因为scene上的读写操作比较多，而且scene上每回合战斗死兵数据依赖医院数据
 */
public abstract class AbstractScenePlayerBattleComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    public AbstractScenePlayerBattleComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    /**
     * 更新玩家在场景上的数据统计
     * @param otherCurZoneId 对手所属区服
     */
    abstract public void onBattleRelationEnd(BattleAttackType attackType, CommonEnum.BattleResult battleResult, BattleRecord.RoleMemberRecord record, boolean enemyNpc, int otherCurZoneId);

    /**
     * 所有战斗关系结束
     *
     * @param totalLoss 指pvp战斗的战力损失，而且我方是单人
     * @param fail      战斗是否失败
     */
    abstract public void onAllBattleEnd(long totalLoss, boolean fail);
}

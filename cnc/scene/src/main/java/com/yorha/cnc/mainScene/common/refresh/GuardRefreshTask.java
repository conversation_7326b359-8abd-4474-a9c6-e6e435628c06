package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Rectangle;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BigSceneSpawnBuildingTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 守护者刷新
 *
 * <AUTHOR>
 */
public class GuardRefreshTask extends RefreshTask {
    private static final Logger LOGGER = LogManager.getLogger(GuardRefreshTask.class);

    private final Set<Long> hasRefreshBuildingId = new HashSet<>();
    final private CommonEnum.MapBuildingType type;
    final private int configId;

    public GuardRefreshTask(SceneEntity owner, CommonEnum.MapBuildingType type, int configId) {
        super(owner);
        this.type = type;
        this.configId = configId;
        LOGGER.info("add RefreshTask {}", this);
    }

    @Override
    public String toString() {
        return "GuardRefreshTask{" +
                ", type=" + type +
                ", configId=" + configId +
                '}';
    }

    @Override
    public int getHandleNumPerTick() {
        return GameLogicConstants.CREATE_MAX_PER_SECOND;
    }

    @Override
    public Pair<Boolean, Integer> run() {
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) getOwner().getObjMgrComponent();
        List<MapBuildingEntity> buildingEntities = getOwner().getBuildingMgrComponent().getMapBuildingByType(type);
        BigSceneSpawnBuildingTemplate config = ResHolder.getTemplate(BigSceneSpawnBuildingTemplate.class, configId);
        Map<Integer, Map<Integer, Integer>> guardianMap = objMgrComponent.getGuardianMap();
        int hasCreateNum = 0;
        for (MapBuildingEntity entity : buildingEntities) {
            if (hasRefreshBuildingId.contains(entity.getEntityId())) {
                continue;
            }
            hasRefreshBuildingId.add(entity.getEntityId());
            Map<Integer, Integer> liveGuardianMap = guardianMap.get(entity.getPartId());
            Rectangle rectangle = Rectangle.valueOf(entity.getCurPoint(), config.getRange(), config.getRange());
            if (liveGuardianMap == null) {
                LOGGER.warn("BigSceneObjMgrComponent refreshGuardian type :{} not have entity data :{}", type, entity.getEntityId());
                continue;
            }
            for (IntPairType pair : config.getRefreshConfigPairList()) {
                int oldNum = liveGuardianMap.getOrDefault(pair.getKey(), 0);
                int newNum = spawnGuardian(rectangle, pair.getKey(), pair.getValue() - oldNum, config.getLifeTime(), entity.getPartId());
                LOGGER.info("refreshGuardianBatch config:{} monster:{} num:{} partId:{}", configId, pair.getKey(), newNum, entity.getPartId());
                hasCreateNum += newNum;
                newNum += oldNum;
                liveGuardianMap.put(pair.getKey(), Math.min(newNum, pair.getValue()));
            }
            if (hasCreateNum >= getHandleNumPerTick()) {
                return Pair.of(false, getHandleNumPerTick());
            }
        }
        return Pair.of(true, hasCreateNum);
    }

    private int spawnGuardian(Shape shape, int templateId, int num, int lifeTime, int partId) {
        int result = 0;
        for (int i = 0; i < num; i++) {
            Point point = MonsterFactory.randomBornPoint(getOwner(), templateId, shape, false);
            if (point == null) {
                LOGGER.info("big scene spawnGuardian random born point fail:{}", templateId);
                continue;
            }
            SceneObjSpawnParam param = new SceneObjSpawnParam();
            if (lifeTime > 0) {
                param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(lifeTime));
            }
            MonsterEntity monster = MonsterFactory.initMonster(getOwner(), templateId, point, param);
            if (monster == null) {
                continue;
            }
            monster.setBelongPart(partId);
            monster.addIntoScene();
            result++;
        }
        return result;
    }
}

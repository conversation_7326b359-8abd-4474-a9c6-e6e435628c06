package com.yorha.cnc.zone.activity;

import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsRank;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 最强指挥官赛季版，每日子榜活动id
 *
 * <AUTHOR>
 */
public class ZoneBestCommanderSubRankUnit extends ZoneActivityScoreRankUnit {
    private static final Logger LOGGER = LogManager.getLogger(ZoneBestCommanderSubRankUnit.class);

    public ZoneBestCommanderSubRankUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    @Override
    protected void afterMailSend(SsRank.DeleteRankAndGetTopAns ans, Map<Long, StructMsg.RankInfoDTO> ret) {
        // 结算完毕之后，这个榜的排名信息需要缓存到整个activity结束的
        ZoneBestCommanderUnit bestCommanderUnit = owner.findFirstUnitInTopFatherActOf(ZoneBestCommanderUnit.class);
        if (bestCommanderUnit == null) {
            LOGGER.info("ZoneActivity {} bestCommanderUnit=null!", owner.getProp());
            return;
        }
        bestCommanderUnit.addRankSettleData(owner.getProp().getActivityId(), ans.getRangeMemberInfoMap());
    }
}

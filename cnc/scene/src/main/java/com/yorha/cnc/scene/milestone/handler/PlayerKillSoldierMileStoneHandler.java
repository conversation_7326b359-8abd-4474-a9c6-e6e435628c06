package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.game.gen.prop.MileStoneClanInfoProp;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人重伤或击杀非本服的士兵数量
 * 参数：重伤/击杀数量
 */
public class PlayerKillSoldierMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_PLAYER;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_PLAYER_KILL_OTHER_SOLDIER;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }

}

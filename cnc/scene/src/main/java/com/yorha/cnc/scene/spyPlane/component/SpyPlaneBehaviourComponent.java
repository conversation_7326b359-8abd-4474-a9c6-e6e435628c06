package com.yorha.cnc.scene.spyPlane.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.cave.CaveEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.common.ScenePushNotificationHelper;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstSpyService;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.resource.resservice.spy.SpyTechService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstSpyTemplate;
import res.template.MapBuildingTemplate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.error.ErrorCode.SYSTEM_TARGET_NULL;
import static com.yorha.proto.CommonEnum.SpyContentType.SCT_CAN_ROB_RESOURCE;
import static com.yorha.proto.CommonEnum.SpyPlaneActionType.SPAT_RETURN;
import static com.yorha.proto.CommonEnum.SpyType.ST_MAPBUILDING;


/**
 * <AUTHOR>
 */
public class SpyPlaneBehaviourComponent extends SceneObjComponent<SpyPlaneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SpyPlaneBehaviourComponent.class);
    boolean hasAnimationTask = false;
    List<Point> curTargetPoint = new ArrayList<>();

    public SpyPlaneBehaviourComponent(SpyPlaneEntity owner) {
        super(owner);
    }

    public static void fillAssistBArmyInfo(boolean showHeroAndMecha, ArmyEntity armyEntity, StructMail.MailSendParams.Builder mailSendParams, AbstractScenePlayerEntity player) {
        if (armyEntity == null) {
            return;
        }
        Struct.SpyArmyData.Builder spyArmyDataBuilder = mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyAssistArmyDataBuilder();
        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(player.getUnlockSpyData());
        // 士兵信息
        if (spyContentList.contains(SpyContentType.SCT_ASSISTB_SOLDIER_TYPE)) {
            for (SoldierProp soldierProp : armyEntity.getTroop().values()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_ASSISTB_NUM_FUZZY)) {
                    soldierBuilder.setNum(getFuzzyNum(soldierProp.getNum()));
                }
                if (spyContentList.contains(SpyContentType.SCT_ASSISTB_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                spyArmyDataBuilder.getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }
        if (showHeroAndMecha) {
            fillHeroAndMechaInfo(armyEntity, spyArmyDataBuilder, player);
        }

    }

    private static int getFuzzyNum(int num) {
        if (num <= 0) {
            return 0;
        }
        ConstSpyTemplate constSpyTemplate = ResHolder.getResService(ConstSpyService.class).getTemplate();
        double temp = (num * 1.0 * RandomUtils.randomBetween(constSpyTemplate.getSpyFuzzyMin(), constSpyTemplate.getSpyFuzzyMax())) / GameLogicConstants.PERCENT_CONVERSION_UNITS;
        num = (int) Math.ceil(temp / GameLogicConstants.PERCENT_CONVERSION_UNITS) * GameLogicConstants.PERCENT_CONVERSION_UNITS;
        return num;
    }

    private static void fillHeroAndMechaInfo(ArmyEntity armyEntity, Struct.SpyArmyData.Builder spyArmyDataBuilder, AbstractScenePlayerEntity player) {
        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(player.getUnlockSpyData());
        HeroProp mainHero = armyEntity.getMainHero();
        // 主将信息
        if (mainHero != null) {
            Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_ASSISTB_HERO_ID)) {
                mainHeroBuilder.setHeroId(mainHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_ASSISTB_HERO_INFO)) {
                mainHeroBuilder = mainHero.getCopySsBuilder();
            }
            spyArmyDataBuilder.getHeroListBuilder().addDatas(mainHeroBuilder);
            spyArmyDataBuilder.getMainHeroIdListBuilder().addDatas(mainHero.getHeroId());
        }
        HeroProp deputyHero = armyEntity.getDeputyHero();
        // 副将信息
        if (deputyHero != null) {
            Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_ASSISTB_HERO_ID)) {
                deputyHeroBuilder.setHeroId(deputyHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_ASSISTB_HERO_INFO)) {
                deputyHeroBuilder = deputyHero.getCopySsBuilder();
            }
            spyArmyDataBuilder.getHeroListBuilder().addDatas(deputyHeroBuilder);
        }
    }

    public static void fillBuildingTroop(TroopProp targetTroop, int troopId, boolean isInBattle, StructMail.MailSendParams.Builder mailSendParams, AbstractScenePlayerEntity player) {
        if (targetTroop == null) {
            return;
        }
        HeroProp mainHero = null;
        HeroProp deputyHero = null;
        Collection<SoldierProp> soldierProps = null;
        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(player.getUnlockSpyData());
        if (isInBattle) {
            // 战斗中填充实时兵力
            mainHero = targetTroop.getMainHero();
            deputyHero = targetTroop.getDeputyHero();
            soldierProps = targetTroop.getTroop().values();
        } else {
            // 非战斗中
            StructPlayer.Troop troop = TroopHelper.getTroopBuilder(ResHolder.getInstance(), troopId);
            TroopProp troopProp = new TroopProp();
            troopProp.mergeFromSs(troop);
            mainHero = troopProp.getMainHero();
            deputyHero = troopProp.getDeputyHero();
            soldierProps = troopProp.getTroop().values();
        }
        Struct.SpyArmyData.Builder spyArmyDataBuilder = mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder();
        // 主将信息
        if (mainHero != null) {
            Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_BUILDING_HERO_ID)) {
                mainHeroBuilder.setHeroId(mainHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_BUILDING_HERO_INFO)) {
                mainHeroBuilder = mainHero.getCopySsBuilder();
            }
            spyArmyDataBuilder.getHeroListBuilder().addDatas(mainHeroBuilder);
            spyArmyDataBuilder.getMainHeroIdListBuilder().addDatas(mainHero.getHeroId());
        }
        // 副将信息
        if (deputyHero != null) {
            Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_BUILDING_HERO_ID)) {
                deputyHeroBuilder.setHeroId(deputyHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_BUILDING_HERO_INFO)) {
                deputyHeroBuilder = deputyHero.getCopySsBuilder();
            }
            spyArmyDataBuilder.getHeroListBuilder().addDatas(deputyHeroBuilder);
        }

        if (spyContentList.contains(SpyContentType.SCT_ASSISTB_SOLDIER_TYPE)) {
            //士兵
            for (SoldierProp soldierProp : soldierProps) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_ASSISTB_NUM_FUZZY)) {
                    soldierBuilder.setNum(getFuzzyNum(soldierProp.getNum()));
                }
                if (spyContentList.contains(SpyContentType.SCT_ASSISTB_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                spyArmyDataBuilder.getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }
    }

    public static String getClanNameString(String clanName) {
        if (clanName.isEmpty()) {
            return clanName;
        }
        return "[" + clanName + "]";
    }

    @Override
    public void init() {
        getOwner().getPlayer().getPlaneComponent().addAttention(getOwner().getEntityId());
    }

    @Override
    public void postInit() {
        super.postInit();
        // 侦察状态
        if (getOwner().getProp().getState() == CommonEnum.SpyPlaneState.SPS_SURVEY) {
            if (!TimeUtils.isBeforeNow(getOwner().getProp().getSurveyE())) {
                getOwner().addSceneSchedule(SceneTimerReason.TIMER_SURVEY, (SystemClock.now() - getOwner().getProp().getSurveyE()));
                return;
            }
            finishSurvey();
        }
        // 探索状态
        if (getOwner().getProp().getState() == CommonEnum.SpyPlaneState.SPS_EXPLORE) {
            getOwner().addSceneSchedule(SceneTimerReason.TIMER_EXPLORE, TimeUtils.second2Ms(1));
        }
    }

    public void stay() {
        LOGGER.info("player:{} SpyPlane:{} enter state :{} ", getOwner().getPlayerId(), getOwner(), SpyPlaneState.SPS_STAY);
        getOwner().getProp().setState(SpyPlaneState.SPS_STAY);
        getOwner().getProp().setStayStartStamp(SystemClock.now());
        getOwner().getMoveComponent().stopMove();
        //清理状态
        getOwner().getProp().getExploreGridId().clear();
        getOwner().getProp().setTotalExploreGridSize(0);
        getOwner().getProp().setTargetConfigId(0);
    }

    public void checkAction(SpyPlaneActionType actionType, List<StructPB.PointPB> pointList, long target, MoveData path) {
        switch (actionType) {
            case SPAT_EXPLORE:
                checkExplore(pointList);
                break;
            case SPAT_SPY:
                checkSpy(target, path);
                break;
            case SPAT_SURVEY:
                checkSurvey(target);
                break;
            case SPAT_RETURN:
                break;
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    private void checkSurvey(long targetId) {
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        if (sceneObjEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY);
        }

        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(sceneObjEntity.getCurPoint(), getOwner().getScene().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
    }

    private void checkExplore(List<StructPB.PointPB> pointList) {
        if (pointList.isEmpty()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (pointList.size() > ResHolder.getInstance().getConstTemplate(ConstSpyTemplate.class).getMaxFogExplore()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        List<StructPB.PointPB> illgalPointList = new ArrayList<>();
        for (StructPB.PointPB pointPB : pointList) {
            boolean isLegalPoint = false;
            ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(Point.valueOf(pointPB.getX(), pointPB.getY()), getOwner().getScene().getMapConfig());
            if (errorCode.isOk()) {
                isLegalPoint = true;
            }
            if (!isLegalPoint) {
                illgalPointList.add(pointPB);
            }
        }
        pointList.removeAll(illgalPointList);
        if (pointList.isEmpty()) {
            throw new GeminiException(ErrorCode.MAP_REGION_NOT_OPEN);
        }
    }

    private void checkSpy(long targetId, MoveData pathData) {
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        if (sceneObjEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY);
        }
        if (!sceneObjEntity.getSpyComponent().canSpy()) {
            throw new GeminiException(ErrorCode.CANT_SPY);
        }
        MoveData path = getOwner().getScene().getPathFindMgrComponent().searchPath(getOwner(), pathData, getOwner().getCurPoint(), sceneObjEntity.getCurPoint(), GameLogicConstants.AIRPORT_MOVE, 0);
        if (path == null) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH);
        }
        SpyType spyType = getOwner().getPlayer().getPlaneComponent().getSpyType(sceneObjEntity);
        if (spyType == SpyType.SPYTYPE_NONE) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        spyFinishCheck(targetId);
    }

    private void clearState() {
        getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_SURVEY);
        getOwner().getMoveComponent().stopMove();
        getOwner().getProp().getExploreGridId().clear();
        getOwner().getProp().setState(SpyPlaneState.SPS_IDLE);
        getOwner().getProp().setTotalExploreGridSize(0);
        getOwner().getProp().setSpyType(SpyType.SPYTYPE_NONE);
        getOwner().getProp().setTargetConfigId(0);
        curTargetPoint.clear();
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.EXPLORE_FINISH);
    }

    public void changeAction(SpyPlaneActionType actionType, List<StructPB.PointPB> pointList, long target, MoveData path) {
        LOGGER.info("player:{} SpyPlaneBehaviourComponent changeAction befor:{} after:{} target:{}", getOwner().getPlayerId(), getOwner().getProp().getAction(), actionType, target);
        //清理状态
        clearState();
        //设置目标对象
        getOwner().getProp().setTargetId(target);
        getOwner().getProp().setAction(actionType);
        //取消预警
        removeWarningItem();
        switch (actionType) {
            case SPAT_EXPLORE:
                exploreFog(pointList);
                break;
            case SPAT_SPY:
                spy(path);
                break;
            case SPAT_RETURN:
                returnHome();
                break;
            case SPAT_SURVEY:
                survey(path);
                break;
            default:
                LOGGER.debug("player:{} SpyPlaneBehaviourComponent nodefineAction action:{}", getOwner().getPlayerId(), actionType);
        }
    }

    /**
     * 调查
     */
    private void survey(MoveData pathData) {
        LOGGER.info("player:{} SpyPlane:{} enter state :{} ", getOwner().getPlayerId(), getOwner(), SpyPlaneState.SPS_MOVE_TO_SURVEY);
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(getOwner().getProp().getTargetId());
        if (sceneObjEntity == null) {
            throw new GeminiException("survey cant find target:{}", getOwner().getProp().getTargetId());
        }
        MoveData path = getOwner().getScene().getPathFindMgrComponent().searchPath(getOwner(), pathData, getOwner().getCurPoint(), sceneObjEntity.getCurPoint(), GameLogicConstants.AIRPORT_MOVE, 0);
        if (path == null) {
            throw new GeminiException("survey cant find path target:{}", getOwner().getProp().getTargetId());
        }
        getOwner().getProp().setState(SpyPlaneState.SPS_MOVE_TO_SURVEY);
        int templateId = 0;
        if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Cave) {
            templateId = ((CaveEntity) sceneObjEntity).getProp().getConfigId();
        }

        if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
            templateId = ((MapBuildingEntity) sceneObjEntity).getPartId();
        }
        getOwner().getProp().setTargetConfigId(templateId);
        getOwner().getMoveComponent().setCreatePrePath(path);

        getOwner().getMoveComponent().moveToTargetAsync(sceneObjEntity, null,
                () -> {
                    getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_SURVEY);

                    LOGGER.debug("player:{} SpyPlane:{} enter state :{} ", getOwner().getPlayerId(), getOwner(), SpyPlaneState.SPS_SURVEY);
                    long now = SystemClock.now();
                    long delay = 1; //FIXME
                    getOwner().getProp().setState(SpyPlaneState.SPS_SURVEY);
                    getOwner().getProp().setSurveyS(now)
                            .setSurveyE(now + delay);
                    if (TimeUtils.isBeforeNow(getOwner().getProp().getSurveyE())) {
                        return;
                    }
                    getOwner().addSceneSchedule(SceneTimerReason.TIMER_SURVEY, delay);
                },
                (event) -> {
                    changeAction(SPAT_RETURN, new ArrayList<>(), 0, null);
                }, null);
        getOwner().getMoveComponent().clearCreatePrePath();
    }

    /**
     * 设置完成任务后是否直接返城
     */
    public void setFinishReturn(boolean finishReturn) {
        LOGGER.debug("SpyPlane :{} setFinishReturn before:{} after:{}", getEntityId(), getOwner().getProp().getFinishNeedReturn(), finishReturn);
        getOwner().getProp().setFinishNeedReturn(finishReturn);
    }

    /**
     * 侦查
     */
    private void spy(MoveData pathData) {
        LOGGER.info("player:{} SpyPlane:{} enter state :{} ", getOwner().getPlayerId(), getOwner(), SpyPlaneState.SPS_SPY);
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(getOwner().getProp().getTargetId());
        if (sceneObjEntity == null) {
            throw new GeminiException(SYSTEM_TARGET_NULL, "targetId: " + getOwner().getProp().getTargetId());
        }
        MoveData path = getOwner().getScene().getPathFindMgrComponent().searchPath(getOwner(), pathData, getOwner().getCurPoint(), sceneObjEntity.getCurPoint(), GameLogicConstants.AIRPORT_MOVE, 0);
        if (path == null) {
            throw new GeminiException("spy cant find path target:{}", getOwner().getProp().getTargetId());
        }
        SpyType spyType = getOwner().getPlayer().getPlaneComponent().getSpyType(sceneObjEntity);
        if (spyType == SpyType.SPYTYPE_NONE) {
            throw new GeminiException("plane:{} spy not not support target: {}", getEntityId(), sceneObjEntity.getEntityType());
        }
        //增加预警
        addWarningItem(sceneObjEntity);
        LOGGER.debug("SpyPlaneBehaviourComponent spy, sceneObjEntity={}", sceneObjEntity);
        // 发送主堡被侦察成功推送
        if (sceneObjEntity instanceof CityEntity) {
            ScenePushNotificationHelper.pushBaseBeSpyNotification(getOwner().getScene(), (CityEntity) sceneObjEntity);
        }
        getOwner().getProp().setSpyType(spyType);
        getOwner().getProp().setState(SpyPlaneState.SPS_MOVE_TO_SPY);
        getOwner().getMoveComponent().setCreatePrePath(path);
        getOwner().getMoveComponent().moveToTargetAsync(sceneObjEntity, null,
                () -> {
                    getOwner().getProp().setState(SpyPlaneState.SPS_SPY);
                    //判定是否可以侦查完成
                    int checkCode = spyFinishCheck(getOwner().getProp().getTargetId());

                    //预期以外的失败，打印错误日志
                    if (checkCode < 0) {
                        LOGGER.error("player:{} spyplane:{} spy finish error:{}", getOwner().getPlayerId(), getEntityId(), checkCode);
                    }
                    //预期以内的侦查失败
                    if (checkCode > 0) {
                        //发送侦查失败的侦查报告, 发送错误码提示(待配置)
                        sendSpyFailMail(checkCode, sceneObjEntity.getCurPoint());
                    }
                    //侦查成功
                    if (checkCode == 0) {
                        if (sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_City) {
                            //发送侦查报告
                            sendSpySuccessMail();
                        } else if (sceneObjEntity.canBlockSpy()) {
                            //主堡反侦察检测
                            //发送空的侦查报告
                            sendTargetCityNoneSpyMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailSpyFailDespy(), (CityEntity) sceneObjEntity);
                            //发送反侦查报告
                            sendTargetCityBeSpyMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseBeSpiedSuccessDespy(), (CityEntity) sceneObjEntity);
                        } else {
                            //发送侦查报告
                            sendSpySuccessMail();
                        }
                    }
                    //返城
                    changeAction(SPAT_RETURN, new ArrayList<>(), 0, null);

                },
                (event) -> {
                    LOGGER.debug("player:{} spyPlane:{} targetLose", getOwner().getPlayerId(), getEntityId());

                    switch (getOwner().getProp().getSpyType()) {
                        //主堡迁移发送侦查失败邮件
                        case ST_CITY:
                            sendSpyFailMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseSpyFailLost(), sceneObjEntity.getCurPoint());
                            break;
                        // 玩家部队目标丢失
                        case ST_ARMY:
                            sendSpyFailMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailArmySpyFailLost(), sceneObjEntity.getCurPoint());
                            break;
                        //集结目标丢失
                        case ST_RALLY:
                            sendSpyFailMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailGrouparmySpyFailLost(), sceneObjEntity.getCurPoint());
                            break;
                        // 地图建筑归属权发生变化
                        case ST_MAPBUILDING:
                            break;
                        default:
                            throw new GeminiException("unSupport target");
                    }
                    //返城
                    changeAction(SPAT_RETURN, new ArrayList<>(), 0, null);
                    LOGGER.debug("player:{} spyPlane:{} targetLose spyType:{}", getOwner().getPlayerId(), getEntityId(), getOwner().getProp().getSpyType());
                },
                null
        );
        getOwner().getMoveComponent().clearCreatePrePath();
    }

    private void addWarningItem(SceneObjEntity obj) {
        if (obj == null) {
            throw new GeminiException("SpyPlaneBehaviourComponent addWarningItem obj is null plane :{}", getEntityId());
        }
        // 不是大世界也不是gvg
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        EntityAttrOuterClass.EntityType entityType = obj.getEntityType();
        switch (entityType) {
            case ET_City:
                CityEntity cityEntity = (CityEntity) obj;
                cityEntity.getInnerArmyComponent().addWarningItem(getOwner(), WarningType.WT_Spy);
                return;
            case ET_MapBuilding:
                MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) obj;
                mapBuildingEntity.getInnerArmyComponent().addWarningItem(getOwner(), WarningType.WT_Spy);
                return;
            case ET_Army:
                ArmyEntity army = (ArmyEntity) obj;
                army.addWarningItem(getOwner(), WarningType.WT_Spy);
                return;
            default:
                return;
        }

    }

    private void removeWarningItem() {
        getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
    }

    private void fillHeroInfo(boolean isMainHero, Struct.Hero hero, StructMail.MailSendParams.Builder mailSendParams, Set<CommonEnum.SpyContentType> spyContentList) {
        Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
        if (spyContentList.contains(SpyContentType.SCT_ARMY_HERO_ID)) {
            mainHeroBuilder.setHeroId(hero.getHeroId());
        }
        if (spyContentList.contains(SpyContentType.SCT_ARMY_HERO_INFO)) {
            mainHeroBuilder.mergeFrom(hero);
        }
        mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getHeroListBuilder().addDatas(mainHeroBuilder);
        if (isMainHero) {
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getMainHeroIdListBuilder().addDatas(hero.getHeroId());
        }
    }

    private void fillSpyCityMail(CityEntity cityEntity, StructMail.MailSendParams.Builder mailSendParams) {
        Set<SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(getOwner().getPlayer().getUnlockSpyData());
        AbstractScenePlayerEntity scenePlayer = cityEntity.getScenePlayer();
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder().setClanBriefName(cityEntity.getBriefClanName())
                .setPoint(cityEntity.formScenePoint())
                .setCardHead(cityEntity.getProp().getCardHead().getCopySsBuilder())
                .getSpyCityDataBuilder()
                // 城墙等级、当前及最大hp，防御塔等级，当前及最大hp
                .setWallLevel(scenePlayer.getCityBuildLevel(InnerCityBuildType.LASER_FENCE))
                .setCurWallHp(scenePlayer.getWallComponent().getWallHp())
                .setMaxWallHp(scenePlayer.getWallComponent().getWallHpMax());

        if (spyContentList.contains(SpyContentType.SCT_FENCE_INFO)) {
            mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                    .getSpyDataBuilder().getSpyCityDataBuilder()
                    .setTowerLevel(scenePlayer.getCityBuildLevel(InnerCityBuildType.DEFENSIVE_TOWER))
                    .setCurTowerHp(scenePlayer.getWallComponent().getDefenseTowerCurHp())
                    .setMaxTowerHp(scenePlayer.getWallComponent().getDefenseTowerMaxHp());
        }

        // 获取场景玩家实体，判空交给底层调用方法
        // 城防部队信息
        Struct.Hero mainHero = scenePlayer.getWallComponent().getWallMainHero();
        Struct.Hero deputyHero = scenePlayer.getWallComponent().getWallDeputyHero();
        if (mainHero != null) {
            fillHeroInfo(true, mainHero, mailSendParams, spyContentList);
        }
        if (deputyHero != null) {
            fillHeroInfo(false, deputyHero, mailSendParams, spyContentList);
        }

        if (spyContentList.contains(SpyContentType.SCT_ARMY_SOLDIER_TYPE)) {
            //士兵
            for (SoldierProp soldierProp : scenePlayer.getSoldierMgrComponent().getInCitySoldier().values()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_ARMY_NUM_FUZZY)) {
                    soldierBuilder.setNum(getFuzzyNum(soldierProp.getNum()));
                }
                if (spyContentList.contains(SpyContentType.SCT_ARMY_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }

        //援助部队信息
        for (ArmyEntity armyEntity : cityEntity.getInnerArmyComponent().getInnerArmyList()) {
            fillAssitArmyInfo(armyEntity, mailSendParams);
        }
        // 集结部队信息
        long rallyId = scenePlayer.getRallyComponent().getCurRallyId();
        if (rallyId > 0) {
            RallyEntity rallyEntity = scenePlayer.getRallyComponent().getRallyEntityWithException(rallyId);
            if (rallyEntity == null) {
                return;
            }
            for (ArmyEntity armyEntity : rallyEntity.getArmyMgrComponent().getInRallyArmies()) {
                if (armyEntity.getEntityId() == rallyEntity.getLeaderArmy().getEntityId()) {
                    fillCityRallyArmyInfo(true, armyEntity, mailSendParams);
                    continue;
                }
                fillCityRallyArmyInfo(false, armyEntity, mailSendParams);
            }
        }
    }

    private void fillSpyMapBuildingMail(MapBuildingEntity mapBuildingEntity, StructMail.MailSendParams.Builder mailSendParams) {
        if (mapBuildingEntity == null || mailSendParams == null) {
            return;
        }
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(mapBuildingEntity.getClanSimpleName())))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, mapBuildingEntity.getTemplateId()));
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder().setClanBriefName(mapBuildingEntity.getClanSimpleName())
                .setPoint(mapBuildingEntity.formScenePoint())
                .getSpyBuildingDataBuilder().setTemplateId(mapBuildingEntity.getTemplateId())
                .setCurHp(mapBuildingEntity.getBuildComponent().getCurHp())
                .setMaxHp(mapBuildingEntity.getBuildComponent().getMaxHp());
        // 地图建筑是中立建筑
        if (mapBuildingEntity.isNeutral()) {
            fillBuildingTroop(mapBuildingEntity.getBattleComponent().getTroop(),
                    mapBuildingEntity.getBuildingTemplate().getTroopId(),
                    mapBuildingEntity.getBattleComponent().isInBattle(),
                    mailSendParams, getOwner().getPlayer());
            return;
        }
        Collection<InnerArmyInfoProp> armies = mapBuildingEntity.getProp().getInnerArmy().getArmy().values();
        for (InnerArmyInfoProp army : armies) {
            ArmyEntity armyEntity = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, army.getArmyId());
            if (armyEntity == null) {
                continue;
            }
            // 车头需要填充英雄和机甲信息
            boolean showHeroAndMecha = mapBuildingEntity.getInnerArmyComponent().getLeaderArmyId() == armyEntity.getEntityId();
            fillAssistBArmyInfo(showHeroAndMecha, armyEntity, mailSendParams, getOwner().getPlayer());
        }
    }

    private void fillSpyOutbuildingMail(OutbuildingEntity outbuilding, StructMail.MailSendParams.Builder mailSendParams) {
        if (outbuilding == null || mailSendParams == null) {
            return;
        }
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, outbuilding.getTemplateId()));
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder().setClanBriefName("")
                .setPoint(outbuilding.formScenePoint())
                .getSpyBuildingDataBuilder().setTemplateId(outbuilding.getTemplateId())
                .setCurHp(0)
                .setMaxHp(0);
        fillBuildingTroop(outbuilding.getBattleComponent().getTroop(),
                outbuilding.getBuildingTemplate().getTroopId(),
                outbuilding.getBattleComponent().isInBattle(),
                mailSendParams, getOwner().getPlayer());
    }

    private void fillSpyArmyMail(ArmyEntity army, StructMail.MailSendParams.Builder mailSendParams) {
        if (army == null || mailSendParams == null) {
            return;
        }
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder().setClanBriefName(army.getClanBriefName())
                .setCardHead(army.getProp().getCardHead().getCopySsBuilder())
                .setPoint(army.formScenePoint());
        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(getOwner().getPlayer().getUnlockSpyData());
        com.yorha.proto.Struct.SpyArmyData.Builder spyArmyDataBuilder = mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder();
        HeroProp mainHero = army.getMainHero();
        //主将
        if (mainHero != null) {
            Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_ARMY_HERO_ID)) {
                mainHeroBuilder.setHeroId(mainHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_ARMY_HERO_INFO)) {
                mainHeroBuilder = mainHero.getCopySsBuilder();
            }
            spyArmyDataBuilder.getHeroListBuilder().addDatas(mainHeroBuilder);
            spyArmyDataBuilder.getMainHeroIdListBuilder().addDatas(mainHero.getHeroId());
        }
        HeroProp deputyHero = army.getDeputyHero();
        //副将
        if (deputyHero != null) {
            Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_ARMY_HERO_ID)) {
                deputyHeroBuilder.setHeroId(deputyHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_ARMY_HERO_INFO)) {
                deputyHeroBuilder = deputyHero.getCopySsBuilder();
            }
            spyArmyDataBuilder.getHeroListBuilder().addDatas(deputyHeroBuilder);
        }
        if (spyContentList.contains(SpyContentType.SCT_ARMY_SOLDIER_TYPE)) {
            //士兵
            for (SoldierProp soldierProp : army.getTroop().values()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_ARMY_NUM_FUZZY)) {
                    soldierBuilder.setNum(getFuzzyNum(soldierProp.getNum()));
                }
                if (spyContentList.contains(SpyContentType.SCT_ARMY_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                spyArmyDataBuilder.getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }
    }

    private void fillSpyRallyArmyMail(ArmyEntity army, StructMail.MailSendParams.Builder mailSendParams) {
        if (army == null || mailSendParams == null) {
            return;
        }
        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder().setClanBriefName(army.getClanBriefName())
                .setCardHead(army.getProp().getCardHead().getCopySsBuilder())
                .setPoint(army.formScenePoint());

        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(getOwner().getPlayer().getUnlockSpyData());
        HeroProp mainHero = army.getMainHero();
        //主将
        if (mainHero != null) {
            Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_ID)) {
                mainHeroBuilder.setHeroId(mainHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_INFO)) {
                mainHeroBuilder = mainHero.getCopySsBuilder();
            }
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getHeroListBuilder().addDatas(mainHeroBuilder);
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getMainHeroIdListBuilder().addDatas(mainHero.getHeroId());
        }
        HeroProp deputyHero = army.getDeputyHero();
        //副将
        if (deputyHero != null) {
            Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
            if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_ID)) {
                deputyHeroBuilder.setHeroId(deputyHero.getHeroId());
            }
            if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_INFO)) {
                deputyHeroBuilder = deputyHero.getCopySsBuilder();
            }
            mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getHeroListBuilder().addDatas(deputyHeroBuilder);
        }
        if (spyContentList.contains(SpyContentType.SCT_RALLY_SOLDIER_TYPE)) {
            //士兵
            ConstSpyTemplate constSpyTemplate = ResHolder.getResService(ConstSpyService.class).getTemplate();
            for (SoldierProp soldierProp : army.getTroop().values()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_RALLY_NUM_FUZZY)) {
                    soldierBuilder.setNum((soldierProp.getNum() * RandomUtils.randomBetween(constSpyTemplate.getSpyFuzzyMin(), constSpyTemplate.getSpyFuzzyMax())) / GameLogicConstants.PERCENT_CONVERSION_UNITS);
                }
                if (spyContentList.contains(SpyContentType.SCT_RALLY_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyArmyDataBuilder().getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }
    }

    private void sendTargetCityNoneSpyMail(int mailId, CityEntity cityEntity) {
        if (cityEntity == null) {
            throw new GeminiException("unknown error sendTargetCityNoneSpyMail cityEntity is null plane:{}", getEntityId());
        }

        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(mailId);
        String clanName = "";
        if (cityEntity.getScenePlayer().getSceneClan() != null) {
            clanName = cityEntity.getScenePlayer().getSceneClan().getClanSimpleName();
        }
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(clanName)))
                .addDatas(MsgHelper.buildDisPlayText(cityEntity.getScenePlayer().getName()));

        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder()
                .setClanBriefName(clanName)
                .setCardHead(cityEntity.getProp().getCardHead().getCopySsBuilder())
                .setTargetId(cityEntity.getPlayerId())
                .setPoint(cityEntity.formScenePoint());

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getPlayer().getZoneId())
                        .build(),
                mailSendParams.build());

    }

    /**
     * 地图建筑被侦查战报
     *
     * @param targetId
     * @param mailId
     * @param mapBuildingEntity
     */
    private void sendTargetBuildingBeSpyMail(long targetId, int mailId, MapBuildingEntity mapBuildingEntity) {
        if (targetId <= 0) {
            return;
        }
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(mailId);
        AbstractScenePlayerEntity targetPlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetId);
        String clanName = "";
        if (getOwner().getPlayer().getSceneClan() != null) {
            clanName = getOwner().getPlayer().getSceneClan().getClanSimpleName();
        }
        String targetClanName = "";
        if (targetPlayer.getSceneClan() != null) {
            targetClanName = targetPlayer.getSceneClan().getClanSimpleName();
        }
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(clanName)))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getPlayer().getName()))
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(targetClanName)))
                .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, mapBuildingEntity.getTemplateId()));

        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder()
                .setClanBriefName(clanName)
                .setTargetId(getOwner().getPlayerId())
                .setCardHead(getOwner().getPlayer().getCardHead().getCopySsBuilder())
                .setPoint(mapBuildingEntity.formScenePoint())
                .getSpyBuildingDataBuilder().setTemplateId(mapBuildingEntity.getTemplateId());

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(targetId)
                        .setZoneId(getOwner().getPlayer().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    /**
     * 基地被侦查战报
     *
     * @param mailId
     * @param cityEntity
     */
    private void sendTargetCityBeSpyMail(int mailId, CityEntity cityEntity) {
        if (cityEntity == null) {
            throw new GeminiException("sendTargetCityBeSpyMail mailId:{} cityEntity is null", mailId);
        }
        if (cityEntity.getPlayerId() <= 0) {
            throw new GeminiException("sendTargetCityBeSpyMail mailId:{} player is null, city:{}", mailId, cityEntity.getEntityId());
        }
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(mailId);
        String clanName = "";
        if (getOwner().getPlayer().getSceneClan() != null) {
            clanName = getOwner().getPlayer().getSceneClan().getClanSimpleName();
        }
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(clanName)))
                .addDatas(MsgHelper.buildDisPlayText(getOwner().getPlayer().getName()));

        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder()
                .setClanBriefName(clanName)
                .setTargetId(getOwner().getPlayerId())
                .setCardHead(getOwner().getPlayer().getCardHead().getCopySsBuilder())
                .setPoint(cityEntity.formScenePoint());

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(cityEntity.getPlayerId())
                        .setZoneId(cityEntity.getScenePlayer().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    private void sendTargetArmyBeSpyMail(int mailId, ArmyEntity armyEntity, CityEntity city) {
        if (armyEntity == null) {
            throw new GeminiException("sendTargetArmyBeSpyMail mailId:{} armyEntity is null", mailId);
        }
        if (armyEntity.getPlayerId() <= 0) {
            throw new GeminiException("sendTargetArmyBeSpyMail mailId:{} player is null, army:{}", mailId, armyEntity.getEntityId());
        }
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(mailId);
        String clanName = "";
        if (getOwner().getPlayer().getSceneClan() != null) {
            clanName = getOwner().getPlayer().getSceneClan().getClanSimpleName();
        }
        String beSpyClanName = clanName;
        String playerName = getOwner().getPlayer().getName();
        //在援助别人的城池，邮件副标题显示的是被侦查玩家的信息
        if (city != null) {
            beSpyClanName = city.getBriefClanName();
            playerName = city.getScenePlayer().getName();
        }
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(beSpyClanName)))
                .addDatas(MsgHelper.buildDisPlayText(playerName));

        mailSendParams.getContentBuilder().setContentType(MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getSpyDataBuilder()
                .setClanBriefName(clanName)
                .setCardHead(getOwner().getPlayer().getCardHead().getCopySsBuilder())
                .setTargetId(getOwner().getPlayerId())
                .setPoint(armyEntity.formScenePoint());

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(armyEntity.getPlayerId())
                        .setZoneId(armyEntity.getScenePlayer().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    /**
     * 发送侦查成功的报告
     */
    private void sendSpySuccessMail() {
        LOGGER.info("player:{} spyplane:{} sendSpySuccessMail", getOwner().getPlayerId(), getEntityId());
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        String name = "";
        int mailId = 0;
        int beSpyId;
        String clanName = "";
        //填充目标玩家名相关信息
        long targetPlayerId = getOwner().getTargetPlayerId();
        if (targetPlayerId > 0) {
            AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId);
            name = abstractScenePlayerEntity.getName();
            mailSendParams.getContentBuilder().getSpyDataBuilder().setCardHead(abstractScenePlayerEntity.getCardHead().getCopySsBuilder());
            if (abstractScenePlayerEntity.getSceneClan() != null) {
                clanName = abstractScenePlayerEntity.getSceneClan().getClanSimpleName();
            }
        }
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(getOwner().getProp().getTargetId());
        if (sceneObjEntity == null) {
            throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail target not find :{}", getOwner().getPlayerId(), getEntityId(), getOwner().getProp().getTargetId());
        }
        final SpyType spyType = getOwner().getProp().getSpyType();
        //侦查地图建筑
        if (spyType == ST_MAPBUILDING) {
            mailId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBuildingSpySuccess();
            if (sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_MapBuilding) {
                throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail spyType:{} unKnow type:{}", getOwner().getPlayerId(), getEntityId(), getOwner().getProp().getSpyType(), sceneObjEntity.getEntityType());
            }
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) sceneObjEntity;
            fillSpyMapBuildingMail(mapBuildingEntity, mailSendParams);
            beSpyId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBuildingBeSpiedSuccess();
            //增援部队通知被侦查
            for (ArmyEntity army : mapBuildingEntity.getInnerArmyComponent().getInnerArmyList()) {
                //联盟建筑被侦查的邮件
                sendTargetBuildingBeSpyMail(army.getPlayerId(), beSpyId, mapBuildingEntity);
            }
        } else if (spyType == SpyType.ST_OUTBUILDING) {
            mailId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBuildingSpySuccess();
            mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(getClanNameString(clanName)));
            fillSpyOutbuildingMail((OutbuildingEntity) sceneObjEntity, mailSendParams);
        } else {
            //侦查玩家主堡
            if (spyType == SpyType.ST_CITY) {
                mailId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseSpySuccess();
                if (sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_City) {
                    throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail spyType:{} unKnow type:{}", getOwner().getPlayerId(), getEntityId(), getOwner().getProp().getSpyType(), sceneObjEntity.getEntityType());
                }
                CityEntity cityEntity = (CityEntity) sceneObjEntity;
                fillSpyCityMail(cityEntity, mailSendParams);
                beSpyId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseBeSpiedSuccess();
                //主堡被侦查的邮件
                sendTargetCityBeSpyMail(beSpyId, cityEntity);
                //援助部队被侦查的邮件
                beSpyId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailHelpBaseBeSpiedSuccess();
                for (ArmyEntity army : cityEntity.getInnerArmyComponent().getInnerArmyList()) {
                    sendTargetArmyBeSpyMail(beSpyId, army, cityEntity);
                }
            }
            //侦查玩家部队
            if (spyType == SpyType.ST_ARMY) {
                mailId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailArmySpySuccess();
                if (sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) {
                    throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail spyType:{} unKnow type:{}", getOwner().getPlayerId(), getEntityId(), getOwner().getProp().getSpyType(), sceneObjEntity.getEntityType());
                }
                ArmyEntity armyEntity = (ArmyEntity) sceneObjEntity;
                fillSpyArmyMail(armyEntity, mailSendParams);
                beSpyId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailArmyBeSpiedSuccess();
                //玩家部队被侦查的邮件
                sendTargetArmyBeSpyMail(beSpyId, armyEntity, null);
            }
            //侦查集结部队
            if (spyType == SpyType.ST_RALLY) {
                mailId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailGrouparmySpySuccess();
                if (sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) {
                    throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail spyType:{} unKnow type:{}", getOwner().getPlayerId(), getEntityId(), getOwner().getProp().getSpyType(), sceneObjEntity.getEntityType());
                }
                //集结部队本质是一个army，所以和玩家部队一样的方式填充数据
                ArmyEntity armyEntity = (ArmyEntity) sceneObjEntity;
                RallyEntity rallyEntity = armyEntity.getRallyEntity();
                if (rallyEntity == null) {
                    throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail unKnow error army is not rally :{}", getOwner().getPlayerId(), getEntityId(), armyEntity.getEntityId());
                }
                beSpyId = ResHolder.getResService(ConstSpyService.class).getTemplate().getMailGrouparmyBeSpiedSuccess();
                fillSpyRallyArmyMail(armyEntity, mailSendParams);
                //给集结所有成员发送被侦查的邮件
                for (ArmyEntity army : rallyEntity.getArmyMgrComponent().getInRallyArmies()) {
                    sendTargetArmyBeSpyMail(beSpyId, army, null);
                }
            }
            mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                    .addDatas(MsgHelper.buildDisPlayText(clanName))
                    .addDatas(MsgHelper.buildDisPlayText(name));
        }

        if (mailId <= 0) {
            throw new GeminiException("player: {} spyplane:{} sendSpySuccessMail unKnow mailId :{}", getOwner().getPlayerId(), getEntityId(), mailId);
        }
        mailSendParams.setMailTemplateId(mailId);
        com.yorha.proto.Basic.Int32List.Builder spyTechBuilder = mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyTechBuilder();
        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(getOwner().getPlayer().getUnlockSpyData());
        for (SpyContentType techId : spyContentList) {
            spyTechBuilder.addDatas(techId.getNumber());
        }
        // 还要查询可掠夺资源(副本无该功能，跳过)
        if (getOwner().getProp().getSpyType() == SpyType.ST_CITY && !this.getOwner().getScene().isDungeon()) {
            AbstractScenePlayerEntity targetPlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(targetPlayerId);
            if (targetPlayer == null) {
                LOGGER.error("player:{} spyplane:{} sendSpySuccessMail, no targetPlayer entity:{}", getOwner().getPlayerId(), getEntityId(), targetPlayerId);
                sendSpyMail(mailSendParams);
                return;
            }
            ownerActor().<SsPlayerMisc.GetSpyDataAns>askPlayer(targetPlayer.getZoneId(), targetPlayer.getPlayerId(), SsPlayerMisc.GetSpyDataAsk.getDefaultInstance())
                    .onComplete((ans, err) -> {
                                if (err != null) {
                                    LOGGER.error("player:{} spyplane:{} sendSpySuccessMail send err: {}", getOwner().getPlayerId(), getEntityId(), err);
                                    sendSpyMail(mailSendParams);
                                    return;
                                }
                                if (spyContentList.contains(SCT_CAN_ROB_RESOURCE)) {
                                    for (int type : ans.getResourceMap().keySet()) {
                                        Struct.Currency.Builder data = Struct.Currency.newBuilder().setType(type).setCount(ans.getResourceMap().get(type));
                                        mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyCityDataBuilder().getResourcesBuilder().putDatas(type, data.build());
                                    }
                                }
                                sendSpyMail(mailSendParams);
                            }
                    );
            return;
        }
        sendSpyMail(mailSendParams);
    }

    private void sendSpyMail(StructMail.MailSendParams.Builder mailSendParams) {
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getPlayer().getZoneId())
                        .build(),
                mailSendParams.build());
        LOGGER.debug("player:{} spyplane:{} sendSpySuccessMail send mail:{}", getOwner().getPlayerId(), getEntityId(), mailSendParams.getMailTemplateId());
    }

    private void fillAssitArmyInfo(ArmyEntity armyEntity, StructMail.MailSendParams.Builder mailSendParams) {
        if (armyEntity == null) {
            return;
        }

        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(getOwner().getPlayer().getUnlockSpyData());

        // 获取
        Struct.SpyArmyData.Builder spyArmyDataBuilder = mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyAssistArmyDataBuilder();
        fillHeroAndMechaInfo(armyEntity, spyArmyDataBuilder, getOwner().getPlayer());
        if (spyContentList.contains(SpyContentType.SCT_ASSIST_SOLDIER_TYPE)) {
            //士兵
            for (SoldierProp soldierProp : armyEntity.getTroop().values()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_ASSIST_NUM_FUZZY)) {
                    soldierBuilder.setNum(getFuzzyNum(soldierProp.getNum()));
                }
                if (spyContentList.contains(SpyContentType.SCT_ASSIST_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyAssistArmyDataBuilder().getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }
    }

    private void fillCityRallyArmyInfo(boolean showHeroAndPlane, ArmyEntity armyEntity, StructMail.MailSendParams.Builder
            mailSendParams) {
        if (armyEntity == null) {
            return;
        }
        Set<CommonEnum.SpyContentType> spyContentList = ResHolder.getResService(SpyTechService.class).getSpyContentList(getOwner().getPlayer().getUnlockSpyData());

        if (showHeroAndPlane) {
            HeroProp mainHero = armyEntity.getMainHero();
            //主将
            if (mainHero != null) {
                Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
                if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_ID)) {
                    mainHeroBuilder.setHeroId(mainHero.getHeroId());
                }
                if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_INFO)) {
                    mainHeroBuilder = mainHero.getCopySsBuilder();
                }
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyRallyArmyDataBuilder().getHeroListBuilder().addDatas(mainHeroBuilder);
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyRallyArmyDataBuilder().getMainHeroIdListBuilder().addDatas(mainHero.getHeroId());
            }
            HeroProp deputyHero = armyEntity.getDeputyHero();
            //副将
            if (deputyHero != null) {
                Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
                if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_ID)) {
                    deputyHeroBuilder.setHeroId(deputyHero.getHeroId());
                }
                if (spyContentList.contains(SpyContentType.SCT_RALLY_LEADER_HERO_INFO)) {
                    deputyHeroBuilder = deputyHero.getCopySsBuilder();
                }
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyRallyArmyDataBuilder().getHeroListBuilder().addDatas(deputyHeroBuilder);
            }
        }

        if (spyContentList.contains(SpyContentType.SCT_RALLY_SOLDIER_TYPE)) {
            //士兵
            for (SoldierProp soldierProp : armyEntity.getTroop().values()) {
                Struct.Soldier.Builder soldierBuilder = Struct.Soldier.newBuilder();
                soldierBuilder.setSoldierId(soldierProp.getSoldierId());
                if (spyContentList.contains(SpyContentType.SCT_RALLY_NUM_FUZZY)) {
                    soldierBuilder.setNum(getFuzzyNum(soldierProp.getNum()));
                }
                if (spyContentList.contains(SpyContentType.SCT_RALLY_NUM_TRUE)) {
                    soldierBuilder = soldierProp.getCopySsBuilder();
                }
                mailSendParams.getContentBuilder().getSpyDataBuilder().getSpyRallyArmyDataBuilder().getSoldierListBuilder().addDatas(soldierBuilder);
            }
        }
    }

    /**
     * 发送侦查失败的报告
     */
    private void sendSpyFailMail(int mailId, Point point) {
        LOGGER.info("player:{} spyplane:{} sendSpyFailMail mailId:{}", getOwner().getPlayerId(), getEntityId(), mailId);
        String clanName = "";
        String playerName = "";
        long targetPlayerId = getOwner().getTargetPlayerId();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        if (targetPlayerId > 0) {
            AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId);
            if (abstractScenePlayerEntity != null) {
                playerName = abstractScenePlayerEntity.getName();
                if (abstractScenePlayerEntity.getSceneClan() != null) {
                    clanName = abstractScenePlayerEntity.getSceneClan().getClanSimpleName();
                }
                //如果对手开盾了
                if (mailId == ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseSpyFailShield()) {
                    //发送对方因为开盾防御成功的相关反侦查报告
                    sendTargetCityBeSpyMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseBeSpiedFailShield(), abstractScenePlayerEntity.getMainCity());
                }

                //如果对方等级差超过10级
                if (mailId == ResHolder.getResService(ConstSpyService.class).getTemplate().getBeSpiedMaxBaseLevelDifference()) {
                    //发送对方因为等级差防御成功的相关反侦查报告
                    sendTargetCityBeSpyMail(ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseBeSpiedSuccessLevel(), abstractScenePlayerEntity.getMainCity());
                }
                // 填充玩家头像
                mailSendParams.getContentBuilder().getSpyDataBuilder().setCardHead(abstractScenePlayerEntity.getCardHead().getCopySsBuilder());
            }
        }


        mailSendParams.setMailTemplateId(mailId);
        mailSendParams.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayText(getClanNameString(clanName)))
                .addDatas(MsgHelper.buildDisPlayText(playerName));

        mailSendParams.getContentBuilder().getSpyDataBuilder()
                .setTargetId(targetPlayerId)
                .setClanBriefName(clanName)
                .getPointBuilder().setX(point.getX()).setY(point.getY());

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getPlayer().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    /**
     * 侦查完成判定 0 表示成功，否则返回失败邮件id
     *
     * @return
     */
    private int spyFinishCheck(long targetId) {
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        //目标对象不存在了
        if (sceneObjEntity == null) {
            LOGGER.debug("spyFinishCheck cant find target:{}", targetId);
            if (getOwner().getProp().getSpyType() == SpyType.ST_CITY) {
                return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseSpyFailLost();
            }
            if (getOwner().getProp().getSpyType() == SpyType.ST_ARMY) {
                return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailArmySpyFailLost();
            }
            if (getOwner().getProp().getSpyType() == SpyType.ST_RALLY) {
                return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailGrouparmySpyFailLost();
            }
            return -1;
        }

        if (sceneObjEntity.getPlayerId() > 0) {
            AbstractScenePlayerEntity target = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(sceneObjEntity.getPlayerId());
            if (target.getMainCity() == null) {
                LOGGER.debug("spyFinishCheck target:{} city is null", targetId);
                return -2;
            }

            //主堡开罩子
            if (getOwner().getProp().getSpyType() == SpyType.ST_CITY) {
                if (target.getMainCity().checkShieldHasFunction(SafeGuardFunctionType.SGFT_CANNOT_BE_SPY) != SafeGuardReason.SGR_NONE) {
                    LOGGER.debug("spyFinishCheck target:{} isShieldOn", targetId);
                    return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBaseSpyFailShield();
                }
            }
            if (getOwner().getPlayer().getMainCity() == null) {
                LOGGER.debug("spyFinishCheck target:{} city is null", getOwner());
                return -3;
            }
            //主堡等级差超过10
            if (target.getMainCity().getLevel() - getOwner().getPlayer().getMainCity().getLevel() > ResHolder.getInstance().getConstTemplate(ConstSpyTemplate.class).getSpyMaxBaseLevelDifference()) {
                LOGGER.debug("spyFinishCheck target:{} level diff more than 10", targetId);
                return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailSpyFailLevelLow();
            }
            //相同联盟
            if (target.isInClan() && (target.getClanId() == getOwner().getPlayer().getClanId())) {
                LOGGER.debug("spyFinishCheck target:{} in same clan", targetId);
                return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailSpyFailSameLegion();
            }
        }

        //地图建筑处于保护期
        if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) sceneObjEntity;
            if (mapBuildingEntity.getOccupyState() == OccupyState.TOS_PROTECT) {
                return ResHolder.getResService(ConstSpyService.class).getTemplate().getMailBuildingSpyFailShield();
            }
        }
        return 0;
    }

    /**
     * 迷雾探索
     *
     * @param pointList
     */
    private void exploreFog(List<StructPB.PointPB> pointList) {
        LOGGER.info("player:{} SpyPlane:{} enter state :{} ", getOwner().getPlayerId(), getOwner(), SpyPlaneState.SPS_EXPLORE);
        getOwner().getProp().setState(SpyPlaneState.SPS_EXPLORE);
        getOwner().getProp().setTotalExploreGridSize(pointList.size());

        getOwner().getProp().getExploreGridId().clear();
        for (StructPB.PointPB point : pointList) {
            Point pathPoint = Point.valueOf(point.getX(), point.getY());
            getOwner().getProp().getExploreGridId().add(new PointProp().setX(pathPoint.getX()).setY(pathPoint.getY()));
        }
        getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_EXPLORE);
        getOwner().addSceneSchedule(SceneTimerReason.TIMER_EXPLORE, TimeUtils.second2Ms(1));
    }

    public void finishSurvey() {
        //调查结束回家
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(getOwner().getProp().getTargetId());
        if (sceneObjEntity == null) {
            LOGGER.info("survey finish cant find target:{}", getOwner().getProp().getTargetId());
            onReturnCityEnd("finish survey");
            return;
        }
        SsPlayerMisc.SendSurveyMailCmd.Builder builder = SsPlayerMisc.SendSurveyMailCmd.newBuilder();
        int templateId = 0;
        if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Cave) {
            templateId = ((CaveEntity) sceneObjEntity).getProp().getConfigId();
        }

        if (sceneObjEntity instanceof MapBuildingEntity) {
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) sceneObjEntity;
            templateId = mapBuildingEntity.getPartId();
            MapBuildingTemplate template = ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, mapBuildingEntity.getTemplateId());
            builder.setType(template.getType());
        }
        // 发送调查邮件
        builder.setTemplateId(templateId).setPoint(Struct.Point.newBuilder().setX(sceneObjEntity.getCurPoint().getX()).setY(sceneObjEntity.getCurPoint().getY()).build());
        getOwner().getPlayer().tellPlayer(builder.build());
        changeAction(SPAT_RETURN, new ArrayList<>(), 0, null);
    }

    /**
     * 发送探索完成的提示
     */
    public void sendClientNtf() {
        PlayerCommon.Player_ExploreInfo_NTF.Builder ntf = PlayerCommon.Player_ExploreInfo_NTF.newBuilder();
        ntf.setExploreInfo(ResHolder.getInstance().getConstTemplate(ConstSpyTemplate.class).getFogExploreFinishTips());
        getOwner().getPlayer().sendMsgToClient(MsgType.PLAYER_EXPLOREINFONTF_NTF, ntf.build());
    }

    /**
     * 前往探索点
     */
    public void moveToNextExplorePoint(int times) {
        //迷雾集合探索完成
        if (getOwner().getProp().getExploreGridId().size() <= 0) {
            if (curTargetPoint.size() > 0) {
                return;
            }
            if (getOwner().getProp().getState() != SpyPlaneState.SPS_EXPLORE) {
                return;
            }
            sendClientNtf();
            if (getOwner().getProp().getFinishNeedReturn()) {
                //回城
                changeAction(SPAT_RETURN, new ArrayList<>(), 0, null);
                return;
            }
            stay();
            getOwner().getTimerComponent().cancelTimer(TimerReasonType.EXPLORE_FINISH);
            getOwner().getTimerComponent().addTimer(TimerReasonType.EXPLORE_FINISH,
                    () -> changeAction(SPAT_RETURN, new ArrayList<>(), 0, null),
                    1, TimeUnit.SECONDS);
            return;
        }

        if (curTargetPoint.size() > 0) {
            return;
        }

        times++;
        if (times >= GameLogicConstants.FOG_EXPLORE_STACK_DEPTH) {
            getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_EXPLORE);
            getOwner().addSceneSchedule(SceneTimerReason.TIMER_EXPLORE, TimeUtils.second2Ms(1));
            return;
        }

        LOGGER.debug("SpyPlane:{} BehaviourComponent.moveToNextExplorePoint left:{}", getOwner().getEntityId(), getOwner().getProp().getExploreGridId().size());
        List<Struct.Point> pointList = new ArrayList<>();
        for (PointProp point : getOwner().getProp().getExploreGridId()) {
            pointList.add(Struct.Point.newBuilder().setX(point.getX()).setY(point.getY()).build());
            Point pathPoint = Point.valueOf(point.getX(), point.getY());
            curTargetPoint.add(pathPoint);
        }

        int finalTimes = times;
        curTargetPoint.clear();

        // 删除已解锁的迷雾点
        List<PointProp> openedPointList = new ArrayList<>();
        getOwner().getProp().getExploreGridId().removeAll(openedPointList);

        if (getOwner().getProp().getExploreGridId().size() <= 0) {
            getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_EXPLORE);
            getOwner().addSceneSchedule(SceneTimerReason.TIMER_EXPLORE, TimeUtils.second2Ms(1));
            return;
        }
        PointProp pointProp = getOwner().getProp().getExploreGridId().get(0);
        getOwner().getProp().getExploreGridId().remove(pointProp);
        Point pathPoint = Point.valueOf(pointProp.getX(), pointProp.getY());
        MoveData path = null;
        try {
            path = getOwner().getScene().getPathFindMgrComponent().searchPrePath(getOwner(), getOwner().getCurPoint(), pathPoint, GameLogicConstants.AIRPORT_MOVE);
        } catch (Exception e) {
            LOGGER.warn("spyPlane:{}, moveToNextExplorePoint point:{} ", getOwner(), pathPoint, e);
        }

        if (path == null) {
            getOwner().tryCancelSceneSchedule(SceneTimerReason.TIMER_EXPLORE);
            getOwner().addSceneSchedule(SceneTimerReason.TIMER_EXPLORE, TimeUtils.second2Ms(1));
            return;
        }
        // 向下一目标点开始移动
        getOwner().getMoveComponent().setCreatePrePath(path);
        getOwner().getMoveComponent().moveToPointAsyncIgnoreException(pathPoint, () -> {
            //递归
            moveToNextExplorePoint(finalTimes);
        });
        getOwner().getMoveComponent().clearCreatePrePath();
    }

    private void returnHome() {
        LOGGER.info("player:{} SpyPlane:{} enter state :{} ", getOwner().getPlayerId(), getOwner(), SpyPlaneState.SPS_RETURN);
        if (getOwner().getProp().getState() == SpyPlaneState.SPS_RETURN) {
            return;
        }
        if ((getOwner().getProp().getState() == SpyPlaneState.SPS_ANI_LANDING) && (hasAnimationTask)) {
            return;
        }
        Point curPoint = getOwner().getTransformComponent().getCurPoint();

        try {
            getOwner().getProp().setState(SpyPlaneState.SPS_RETURN);
            getOwner().getMoveComponent().moveToTargetAsync(
                    getOwner().getPlayer().getMainCity(),
                    TroopInteractionType.RETURN_CITY,
                    this::onLanding,
                    null, null);
        } catch (Exception e) {
            LOGGER.error("player:{} SpyPlane:{} returnHome failed :{} ", getOwner().getPlayerId(), getOwner(), e);
            // vs5补丁,以防万一飞机回不了家
            ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(curPoint, getOwner().getScene().getMapConfig());
            if (errorCode.isNotOk()) {
                onReturnCityEnd("return home fail");
            }
        }

    }

    public void onCityMove() {
        getOwner().getMoveComponent().moveToTargetAsync(
                getOwner().getPlayer().getMainCity(),
                TroopInteractionType.RETURN_CITY,
                this::onLanding,
                null, null);
    }

    /**
     * 回到主城 归还兵力 删除行军
     */
    public void onLanding() {
        LOGGER.info("{} arrive city", getOwner());
        getOwner().getProp().setState(SpyPlaneState.SPS_ANI_LANDING);
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.ANIMATION_LANDING);
        hasAnimationTask = false;
        getOwner().getTimerComponent().addTimer(TimerReasonType.ANIMATION_LANDING, () -> onReturnCityEnd("normal landing"),
                ResHolder.getResService(ConstSpyService.class).getTemplate().getAniLandingMs(), TimeUnit.MILLISECONDS);
        hasAnimationTask = true;
    }

    /**
     * 回到主城 归还兵力 删除行军
     */
    public void onReturnCityEnd(String reason) {
        LOGGER.info("SpyPlaneBehaviourComponent onReturnCityEnd {} {}", getOwner(), reason);
        getOwner().deleteObj();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        removeWarningItem();
    }
}
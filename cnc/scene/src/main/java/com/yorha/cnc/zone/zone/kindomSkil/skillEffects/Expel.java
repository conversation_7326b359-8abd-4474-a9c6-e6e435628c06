package com.yorha.cnc.zone.zone.kindomSkil.skillEffects;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;

/**
 * 驱逐
 * <p>
 * 不能对本王国的人使用
 *
 * <AUTHOR>
 */
public class Expel implements ISkillEffect {

    @Override
    public ErrorCode checkCanUse(int skillId, long targetId, int targetZoneId, ZoneEntity zoneEntity) {
        return ErrorCode.KINGDOM_SKILL_4_TARGET_IS_SAME_ZONE;
    }

    @Override
    public void effect(int skillId, long playerId, ZoneEntity zoneEntity, int targetZoneId) {
        throw new GeminiException(ErrorCode.KINGDOM_SKILL_4_TARGET_IS_SAME_ZONE);
    }
}

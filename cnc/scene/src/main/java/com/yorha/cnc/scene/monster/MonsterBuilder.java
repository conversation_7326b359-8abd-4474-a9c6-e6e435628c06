package com.yorha.cnc.scene.monster;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.component.MonsterTransformComponent;
import com.yorha.cnc.scene.monster.monsterFeature.MonsterFeature;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.game.gen.prop.MonsterProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class MonsterBuilder extends SceneObjBuilder<MonsterEntity, MonsterProp> {

    private boolean isBattleSummons;
    private MonsterFeature feature;

    public MonsterBuilder(SceneEntity sceneEntity, long eid, MonsterProp prop, boolean isBattleSummons, MonsterFeature feature) {
        super(sceneEntity, eid, prop);
        this.isBattleSummons = isBattleSummons;
        this.feature = feature;
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getMove().getCurPoint();
    }

    @Override
    public MonsterTransformComponent transformComponent(MonsterEntity owner) {
        return new MonsterTransformComponent(owner, getPointProp());
    }

    public boolean isBattleSummons() {
        return isBattleSummons;
    }

    public MonsterFeature getFeature() {
        return feature;
    }
}

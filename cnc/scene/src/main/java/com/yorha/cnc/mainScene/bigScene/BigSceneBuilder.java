package com.yorha.cnc.mainScene.bigScene;

import com.yorha.cnc.mainScene.bigScene.component.BigScenePlayerMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneCityMoveComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneGridAoiMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainScenePathFindComponent;
import com.yorha.cnc.scene.entity.SceneBuilder;
import com.yorha.cnc.scene.entity.component.BattleGroundComponent;
import com.yorha.cnc.scene.entity.component.CityMoveComponent;

/**
 * <AUTHOR>
 */
public class BigSceneBuilder extends SceneBuilder<BigSceneEntity> {

    @Override
    public BattleGroundComponent battleGroundComponent(BigSceneEntity owner) {
        BattleGroundComponent battleGroundComponent = super.battleGroundComponent(owner);
        // 大世界开启forkJoinPool
        battleGroundComponent.getBattleGround().openParallel();
        return battleGroundComponent;
    }

    @Override
    public MainSceneObjMgrComponent objMgrComponent(BigSceneEntity owner) {
        return new MainSceneObjMgrComponent(owner);
    }

    @Override
    public MainScenePathFindComponent pathFindMgrComponent(BigSceneEntity owner) {
        return new MainScenePathFindComponent(owner);
    }

    @Override
    public MainSceneGridAoiMgrComponent aoiMgrComponent(BigSceneEntity owner) {
        return new MainSceneGridAoiMgrComponent(owner);
    }

    @Override
    public BigScenePlayerMgrComponent playerMgrComponent(BigSceneEntity owner) {
        return new BigScenePlayerMgrComponent(owner);
    }

    @Override
    public CityMoveComponent cityMoveComponent(BigSceneEntity owner) {
        return new MainSceneCityMoveComponent(owner);
    }
}

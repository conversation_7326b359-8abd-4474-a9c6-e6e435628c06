package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneEntity;
import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneFactory;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.cnc.scene.spyPlane.SpyPlaneFactory;
import com.yorha.common.actor.ScenePlaneService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.SsScenePlane;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import res.template.SpyPlaneTemplate;

import java.util.List;

import static com.yorha.proto.CommonEnum.LogisticsActionType.LAT_RETURN;
import static com.yorha.proto.CommonEnum.SpyPlaneActionType.SPAT_EXPLORE;

/**
 * 小飞机相关
 *
 * <AUTHOR>
 */
public class ScenePlaneServiceImpl implements ScenePlaneService {
    private final SceneActor sceneActor;

    public ScenePlaneServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }


    @Override
    public void handleCreateSpyPlaneAsk(SsScenePlane.CreateSpyPlaneAsk ask) {
        AbstractScenePlayerEntity player = sceneActor.getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (player == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        if (player.getMainCity().getTransformComponent().isAscend()) {
            throw new GeminiException(ErrorCode.CITY_CITY_IS_ASCEND);
        }
        SpyPlaneEntity spyPlaneEntity = SpyPlaneFactory.createSpyPlaneEntity(sceneActor.getScene(), player.getEntityId(), ask);
        long planeId = spyPlaneEntity.getEntityId();
        long clanId = player.getClanId();
        boolean needEnterWarFrenzyState = SpyPlaneFactory.needEnterWarFrenzyState(sceneActor.getScene(), ask.getSpyInfo().getActionType(), ask.getSpyInfo().getTargetId(), clanId);
        if (needEnterWarFrenzyState) {
            player.getDevBuffComponent().openWarFrenzy();
        }
        SsScenePlane.CreateSpyPlaneAns.Builder builder = SsScenePlane.CreateSpyPlaneAns.newBuilder();
        builder.setPlaneEntityId(planeId);
        sceneActor.answer(builder.build());
    }

    @Override
    public void handleChangeActionSpyPlaneAsk(SsScenePlane.ChangeActionSpyPlaneAsk ask) {
        AbstractScenePlayerEntity player = sceneActor.getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (player == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        SpyPlaneEntity spyPlaneEntity = sceneActor.getScene().getObjMgrComponent().getSceneObjWithType(SpyPlaneEntity.class, ask.getSpyInfo().getSpyPlaneId());
        if (spyPlaneEntity == null) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        if (spyPlaneEntity.getPlayer().getPlayerId() != player.getPlayerId()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }

        if (ask.getSpyInfo().getTargetId() > 0) {
            SceneObjEntity sceneObjEntity = sceneActor.getScene().getObjMgrComponent().getSceneObjEntity(ask.getSpyInfo().getTargetId());
            if (sceneObjEntity != null) {
                spyPlaneEntity.setTargetPlayerId(sceneObjEntity.getPlayerId());
            }
        }
        spyPlaneEntity.getSpyPlaneBehaviourComponent().setFinishReturn(ask.getSpyInfo().getFinishNeedReturn());
        List<StructPB.PointPB> pointList = null;
        if (ask.getSpyInfo().getActionType() == SPAT_EXPLORE) {
            SpyPlaneTemplate template = ResHolder.getInstance().getValueFromMap(SpyPlaneTemplate.class, spyPlaneEntity.getTemplate());
            Point askPoint = null;
            Point sPoint = null;
            if (ask.getSpyInfo().hasPoint()) {
                StructPB.PointPB point = ask.getSpyInfo().getPoint();
                askPoint = Point.valueOf(point.getX(), point.getY());
            }
            if (ask.getSpyInfo().hasSrcPoint()) {
                StructPB.PointPB sPointPB = ask.getSpyInfo().getSrcPoint();
                sPoint = Point.valueOf(sPointPB.getX(), sPointPB.getY());
            }
            pointList = SpyPlaneFactory.getFlyLine(askPoint, template.getExploreNum(), sPoint, player.getScene().getMapId());
        }
        spyPlaneEntity.getSpyPlaneBehaviourComponent().checkAction(ask.getSpyInfo().getActionType(), pointList, ask.getSpyInfo().getTargetId(), null);
        spyPlaneEntity.getSpyPlaneBehaviourComponent().changeAction(ask.getSpyInfo().getActionType(), pointList, ask.getSpyInfo().getTargetId(), null);
        spyPlaneEntity.getMoveComponent().clearCreatePrePath();
        boolean needEnterWarFrenzyState = SpyPlaneFactory.needEnterWarFrenzyState(sceneActor.getScene(), ask.getSpyInfo().getActionType(), ask.getSpyInfo().getTargetId(), player.getClanId());
        if (needEnterWarFrenzyState) {
            player.getDevBuffComponent().openWarFrenzy();
        }
        SsScenePlane.ChangeActionSpyPlaneAns.Builder ans = SsScenePlane.ChangeActionSpyPlaneAns.newBuilder();
        ans.setPlaneEntityId(spyPlaneEntity.getEntityId());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleCheckMapCreateSpyPlaneAsk(SsScenePlane.CheckMapCreateSpyPlaneAsk ask) {
        AbstractScenePlayerEntity player = sceneActor.getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        SsScenePlane.CheckMapCreateSpyPlaneAns.Builder builder = SsScenePlane.CheckMapCreateSpyPlaneAns.newBuilder();
        if (player == null) {
            sceneActor.answer(builder.setErrorCode(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId()).build());
            return;
        }
        //大世界侧构建侦察机检测
        ErrorCode errorCode = player.getPlaneComponent().actionCheck(ask.getSpyInfo(), builder);
        builder.setErrorCode(errorCode.getCodeId());
        if (ErrorCode.isOK(errorCode.getCodeId())) {
            Struct.Currency currency = player.getPlaneComponent().getPlaneCost(ask.getSpyInfo().getActionType(), ask.getSpyInfo().getTargetId());
            //返回玩家侧检测结果
            if (currency != null) {
                builder.setCurrency(currency);
            }
        }
        sceneActor.answer(builder.build());
    }

    /**
     * 创建运输机
     */
    @Override
    public void handleCreateLogisticsPlaneAsk(SsScenePlane.CreateLogisticsPlaneAsk ask) {
        if (getScene().isMainScene()) {
            SsScenePlane.CreateLogisticsPlaneAns ans = SsScenePlane.CreateLogisticsPlaneAns.getDefaultInstance();
            ScenePlayerEntity player = sceneActor.getScenePlayer(ask.getPlayerId());
            LogisticsPlaneFactory.createLogisticsPlaneEntity(sceneActor.getScene(), player, ask);
            sceneActor.answer(ans);
            return;
        }
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    /**
     * 运输机行为scene上检测
     */
    @Override
    public void handleCheckLogisticsActionAsk(SsScenePlane.CheckLogisticsActionAsk ask) {
        AbstractScenePlayerEntity player = sceneActor.getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (player == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        player.getPlaneComponent().checkLogisticAction(getScene(), player, ask.getLogisticsInfo());
        sceneActor.answer(SsScenePlane.CheckLogisticsActionAns.getDefaultInstance());
    }

    /**
     * 修改运输机的行为
     */
    @Override
    public void handleChangeLogisticActionAsk(SsScenePlane.ChangeLogisticActionAsk ask) {
        AbstractScenePlayerEntity player = sceneActor.getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (player == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        if (ask.getLogisticsInfo().getActionType() != LAT_RETURN) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        player.getPlaneComponent().checkLogisticAction(getScene(), player, ask.getLogisticsInfo());
        LogisticsPlaneEntity planeEntity = getScene().getObjMgrComponent().getSceneObjWithType(LogisticsPlaneEntity.class, ask.getLogisticsInfo().getEntityId());
        planeEntity.getBehaviourComponent().handleAction(ask.getLogisticsInfo(), null);
        sceneActor.answer(SsScenePlane.ChangeLogisticsActionAns.getDefaultInstance());
    }
}

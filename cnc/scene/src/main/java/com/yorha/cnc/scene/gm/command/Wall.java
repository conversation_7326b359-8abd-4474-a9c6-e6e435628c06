package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;

import java.util.Map;

public class Wall implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        String type = args.get("type");
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        switch (type) {
            case "wall": {
                scenePlayer.getWallComponent().recoverWallGm();
                break;
            }
            case "tower": {
                scenePlayer.getWallComponent().recoverTower();
                break;
            }
            default:
                break;
        }
    }
}

package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.GridAoiMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.aoiView.AoiViewHelper;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.aoiView.manager.AoiGridType;
import com.yorha.common.aoiView.manager.AoiNodeManager;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.proto.EntityAttrOuterClass.EntityType;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MainSceneGridAoiMgrComponent extends GridAoiMgrComponent {

    public MainSceneGridAoiMgrComponent(SceneEntity owner) {
        super(owner);
    }

    @Override
    public void initAoi() {
        // 普通层视野
        manager = new AoiNodeManager(ownerActor(), ownerActor(), getOwner().getZoneId(), AoiGridType.AGT_NORMAL, getOwner().getMapWidth(), getOwner().getMapHeight(), getAoiGridNum(), getAoiGridNum());
        // 初始化缩略层视野线程
        AoiViewHelper.initAoiViewActor(
                getOwner().getZoneId(),
                getOwner().getMapWidth(),
                getOwner().getMapHeight(),
                GameLogicConstants.BIG_MAP_BRIEF_AOI_NUM,
                GameLogicConstants.BIG_MAP_BRIEF_AOI_NUM);
    }

    public String gmPerfMonsterAoi() {
        List<Integer> monsterNumList = new LinkedList<>();
        long totalMonsterNum = 0;
        long gridNum = 0;
        long nullGridNum = 0;
        Set<AoiGrid> allAoiGrid = manager.getAllAoiGrid();
        for (AoiGrid a : allAoiGrid) {
            gridNum++;
            Set<Long> sceneObjIds = a.getSceneObjIds();
            int num = 0;
            for (long id : sceneObjIds) {
                SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(id);
                if (sceneObjEntity.getEntityType() == EntityType.ET_Monster) {
                    num++;
                    totalMonsterNum++;
                }
            }
            if (num == 0) {
                nullGridNum++;
            }
            monsterNumList.add(num);
        }
        if (monsterNumList.size() <= 0) {
            return "perfMonster fail. gridMonsterNumList <= 0";
        }
        if (totalMonsterNum <= 0) {
            return "perfMonster fail. monster num <= 0";
        }
        if (gridNum <= 0) {
            return "perfMonster fail. grid num <= 0";
        }

        Collections.sort(monsterNumList);
        int totalSize = monsterNumList.size();
        StringBuilder sb = new StringBuilder();
        if (totalSize > 100) {
            int p50Index = (int) (0.5f * totalSize);
            p50Index = Math.min(p50Index, totalSize - 1);

            int p90Index = (int) (0.9f * totalSize);
            p90Index = Math.min(p90Index, totalSize - 1);

            int p99Index = (int) (0.99f * totalSize);
            p99Index = Math.min(p99Index, totalSize - 1);

            int num50 = monsterNumList.get(p50Index);
            int num90 = monsterNumList.get(p90Index);
            int num99 = monsterNumList.get(p99Index);
            sb.append("pct50:").append(num50).append("\n");
            sb.append("pct90:").append(num90).append("\n");
            sb.append("pct99:").append(num99).append("\n");
        }
        sb.append("单地格最少野怪数(min):").append(monsterNumList.get(0)).append("\n");
        sb.append("单地格平均野怪数(avg):").append(((double) totalMonsterNum) / gridNum).append("\n");
        sb.append("单地格最大野怪数(max):").append(monsterNumList.get(totalSize - 1)).append("\n");
        sb.append("空野怪地格数(nullGrid):").append(nullGridNum).append("\n");
        sb.append("大世界总野怪数(total):").append(totalMonsterNum).append("\n");
        sb.append("总地格数(totalGrid):").append(allAoiGrid.size()).append("\n");
        return sb.toString();
    }

}

package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 打开战斗日志
 *
 * <AUTHOR>
 */
public class OpenBattleLog implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(OpenBattleLog.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        LOGGER.info("openBattleLog");
        actor.getScene().getBattleGroundComponent().openLogSwitch();
    }

    @Override
    public String showHelp() {
        return "OpenBattleLog";
    }
}

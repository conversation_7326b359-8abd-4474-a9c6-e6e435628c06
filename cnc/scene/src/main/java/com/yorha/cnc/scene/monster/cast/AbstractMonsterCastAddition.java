package com.yorha.cnc.scene.monster.cast;

import com.yorha.cnc.scene.monster.MonsterEntity;
import res.template.MonsterCastTemplate;

/**
 * <AUTHOR>
 */
public abstract class AbstractMonsterCastAddition {

    protected int basicRate;
    protected int maxCastValue;

    public AbstractMonsterCastAddition(MonsterCastTemplate template) {
        this.basicRate = template.getCastRate();
        this.maxCastValue = template.getCastValue();
        this.parse(template.getParams());
    }

    protected abstract void parse(String param);

    /**
     * 更细蓄力速率
     */
    public final void updateRate(MonsterEntity owner) {
        int curRate = getRate(owner);
        if (curRate != owner.getProp().getCast().getCastRate()) {
            owner.getProp().getCast().setCastRate(curRate);
        }
    }

    protected abstract int getRate(MonsterEntity owner);

    public boolean isMax(MonsterEntity owner) {
        return owner.getProp().getCast().getCastValue() >= this.maxCastValue;
    }

    /**
     * 更新蓄力值
     */
    public void updateValue(MonsterEntity owner) {
        int castRate = owner.getProp().getCast().getCastRate();
        int curCast = owner.getProp().getCast().getCastValue();
        int cast = Math.min(castRate + curCast, this.maxCastValue);
        owner.getProp().getCast().setCastValue(cast);
    }
}

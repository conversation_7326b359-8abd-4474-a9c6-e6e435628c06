package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;

/**
 * <AUTHOR>
 */
public class ArmyAdditionComponent extends SceneObjAdditionComponent {
    public ArmyAdditionComponent(ArmyEntity owner) {
        super(owner);
    }

    @Override
    public ArmyEntity getOwner() {
        return (ArmyEntity) super.getOwner();
    }

    @Override
    public AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getScenePlayer();
    }
}

package com.yorha.cnc.zone.zone.kindomSkil;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.proto.CommonEnum;
import res.template.KingdomSkillTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/9
 */
public class KingdomSkillHelper {
    public static List<Integer> addDevBuff(int skillId, AbstractScenePlayerEntity scenePlayer) {
        KingdomSkillTemplate skillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getSkillTemplate(skillId);
        for (Integer buffId : skillTemplate.getBuffList()) {
            scenePlayer.getDevBuffComponent().addDevBuff(buffId, CommonEnum.DevBuffSourceType.DBST_KINGDOM);
        }
        return skillTemplate.getBuffList();
    }

    public static List<Integer> removeDevBuff(int skillId, AbstractScenePlayerEntity scenePlayer) {
        KingdomSkillTemplate skillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getSkillTemplate(skillId);
        for (Integer buffId : skillTemplate.getBuffList()) {
            scenePlayer.getDevBuffComponent().removeDevBuff(buffId);
        }
        return skillTemplate.getBuffList();
    }

    public static List<Integer> addBattleBuff(int skillId, CityEntity city) {
        KingdomSkillTemplate skillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getSkillTemplate(skillId);
        city.getBuffComponent().addBuff(skillTemplate.getBattlebuffList());
        return skillTemplate.getBattlebuffList();
    }

    public static List<Integer> removeBattleBuff(int skillId, CityEntity city) {
        KingdomSkillTemplate skillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getSkillTemplate(skillId);
        city.getBuffComponent().removeBuff(skillTemplate.getBattlebuffList());
        return skillTemplate.getBattlebuffList();
    }
}

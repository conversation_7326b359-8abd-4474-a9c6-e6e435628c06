package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.mainScene.common.component.MainScenePathFindComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.PointProp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */
public class CrossTransformComponent extends MapBuildingTransformComponent {
    private static final Logger LOGGER = LogManager.getLogger(CrossTransformComponent.class);
    /**
     * 黑暗祭坛关卡的 外->内
     */
    private Pair<Integer, Integer> darkAltarBindRegion = null;
    /**
     * 外圈进内圈关隘的绑定州
     */
    private int bindRegionId = -1;
    /**
     * 围攻占据的位置
     */
    private final Map<Integer, long[]> besiegeIndexArray = new HashMap<>();

    public CrossTransformComponent(MapBuildingEntity owner, PointProp pointProp, int partId) {
        super(owner, pointProp, partId);
    }

    @Override
    public void init() {
        super.init();
    }

    @Override
    public int getBindRegionId() {
        return bindRegionId;
    }

    @Override
    public Pair<Integer, Integer> getDarkAltarBindRegion() {
        return darkAltarBindRegion;
    }

    @Override
    public Point occupyAndGetBesiegePoint(SceneObjEntity caster) {
        long casterId = caster.getEntityId();
        Point casterPoint = caster.getCurPoint();
        int regionId = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), casterPoint);
        RegionalAreaSettingTemplate template = getOwner().getAreaSettingTemplate();
        List<Integer> besiegeAngleList = ResHolder.getResService(MapSubdivisionDataService.class).getCrossBesiegeAngle(getOwner().getScene().getMapId(), getOwner().getPartId(), regionId);
        if (CollectionUtils.isEmpty(besiegeAngleList)) {
            return casterPoint;
        }
        Point selfPoint = getCurPoint();
        int battleDistance = getChaseDistance(caster);
        int baseAngle = template.getRotation() / 1000;
        PathFindMgrComponent component = getOwner().getScene().getPathFindMgrComponent();
        final int maxSize = besiegeAngleList.size();
        long[] index2Id = besiegeIndexArray.computeIfAbsent(regionId, k -> new long[maxSize]);
        List<Integer> unoccupied = new ArrayList<>();
        for (int i = 0; i < maxSize; i++) {
            if (index2Id[i] == casterId) {
                // 已经占据围攻位置了
                int angle = besiegeAngleList.get(i) + baseAngle;
                Point point = selfPoint.getPointWithAngleAndDis(Math.toRadians(angle), battleDistance);
                if (!ErrorCode.isOK(component.isPointWalkable(point))) {
                    return casterPoint;
                }
                return point;
            }
            if (index2Id[i] == 0) {
                unoccupied.add(i);
            }
        }
        // 占用满了
        if (unoccupied.isEmpty()) {
            return casterPoint;
        }
        // 算相对角度
        int angle = (int) (selfPoint.calAngleWithPoint(casterPoint) - baseAngle);
        angle = Constants.curbAngle(angle);
        // 算占据的优先列表
        Map<Integer, Integer> score = new HashMap<>(maxSize);
        List<Integer> closestAngle = new ArrayList<>(maxSize);
        for (int i = 0; i < maxSize; i++) {
            score.put(i, Math.abs(besiegeAngleList.get(i) - angle));
            closestAngle.add(i);
        }
        closestAngle.sort(Comparator.comparingDouble(score::get));

        // 计算最终占领位置
        int finalIndex = tryOccupyBesiegeIndex(index2Id, closestAngle, besiegeAngleList, angle, casterId);
        int besiegeAngle = besiegeAngleList.get(finalIndex) + baseAngle;
        Point point = selfPoint.getPointWithAngleAndDis(Math.toRadians(besiegeAngle), battleDistance);
        if (!ErrorCode.isOK(component.isPointWalkable(point))) {
            return casterPoint;
        }
        return point;
    }

    private Integer tryOccupyBesiegeIndex(long[] index2Id, List<Integer> closestAngle, List<Integer> besiegeAngleList, int angle, long casterId) {
        Integer firstIndex = closestAngle.get(0);
        // 最近的能直接进去
        if (index2Id[firstIndex] == 0) {
            index2Id[firstIndex] = casterId;
            return firstIndex;
        }
        // 找到第一个能进的
        int curIndex = -1;
        for (Integer index : closestAngle) {
            if (index2Id[index] == 0) {
                curIndex = index;
                break;
            }
        }
        // 找到后看一下  如果当前位置在首选位置和能选位置之间，那首选位置要往能选位置进一位  然后判是不是相等了
        if ((angle - besiegeAngleList.get(curIndex)) * (angle - besiegeAngleList.get(firstIndex)) < 0) {
            firstIndex += curIndex > firstIndex ? 1 : -1;
        }
        if (curIndex == firstIndex) {
            index2Id[firstIndex] = casterId;
            return firstIndex;
        }
        return adjustBesiegeIndex(firstIndex, curIndex, index2Id, casterId);
    }

    private int adjustBesiegeIndex(int firstIndex, int curIndex, long[] index2Id, long casterId) {
        int dir = curIndex > firstIndex ? -1 : 1;
        int finalIndex = firstIndex;
        long exceptedId = getOwner().getBattleComponent().getBattleRole().getTargetId();
        for (int i = curIndex; i != firstIndex; i += dir) {
            int nextIndex = i + dir;
            // 如果那个人是面向的人
            if (index2Id[nextIndex] == exceptedId) {
                // 如果面向的人就是本来的首选位置
                if (nextIndex == firstIndex) {
                    finalIndex = i;
                    break;
                }
                nextIndex = nextIndex + dir;
                index2Id[i] = index2Id[nextIndex];
                index2Id[nextIndex] = 0;
                adjustBesiegePoint(index2Id[i]);
                i += dir;
                continue;
            }
            index2Id[i] = index2Id[nextIndex];
            index2Id[nextIndex] = 0;
            adjustBesiegePoint(index2Id[i]);
        }
        index2Id[finalIndex] = casterId;
        return finalIndex;
    }

    @Override
    public void exitBesiege(long casterId) {
        for (long[] index2Id : besiegeIndexArray.values()) {
            for (int i = 0; i < index2Id.length; i++) {
                if (index2Id[i] == casterId) {
                    index2Id[i] = 0;
                    return;
                }
            }
        }
    }

    @Override
    public Point getLeavePosition(ArmyEntity armyEntity, Point targetPoint) {
        try {
            int regionId = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), targetPoint);
            // 先看看是不是本州的
            Point cross = getOwner().getScene().getMapTemplateDataItem().getPointWithSrcAndCross(targetPoint, getOwner().getPartId(), regionId);
            if (cross != null) {
                return cross;
            }
            //  再看看本联盟  本联盟没有联通路就返回随便一个了
            if (armyEntity.getClanId() != 0) {
                SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(armyEntity.getClanId());
                if (sceneClan != null) {
                    Point point = sceneClan.getCrossComponent().getPointWithSrcAndCross(getOwner().getPartId(), targetPoint, regionId);
                    if (point != null) {
                        return point;
                    }
                }
            }
            BoolMap regionMap = ((MainScenePathFindComponent) getOwner().getScene().getPathFindMgrComponent()).getManager().getRegionMap();
            return getOwner().getScene().getMapTemplateDataItem().getPointWithSrcAndAllCross(regionMap, targetPoint, getOwner().getPartId(), regionId);
        } catch (Exception e) {
            LOGGER.error("{} getLeavePosition army: {} {} error. e", getOwner(), armyEntity, targetPoint, e);
        }
        return null;
    }

    /**
     * 获取被进攻时 进攻点的位置
     */
    @Override
    public Point getBeBattlePoint(Point srcPoint, long clanId, int searchTag) {
        int regionId = MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), srcPoint);
        Point targetPoint = getOwner().getScene().getMapTemplateDataItem().getPointWithSrcAndCross(srcPoint, getOwner().getPartId(), regionId);
        if (targetPoint == null) {
            if (GameLogicConstants.ignoreCrossOwner(searchTag)) {
                BoolMap regionMap = ((MainScenePathFindComponent) getOwner().getScene().getPathFindMgrComponent()).getManager().getRegionMap();
                targetPoint = getOwner().getScene().getMapTemplateDataItem().getPointWithSrcAndAllCross(regionMap, srcPoint, getOwner().getPartId(), regionId);
            } else {
                SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
                if (sceneClan == null) {
                    throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
                }
                targetPoint = sceneClan.getCrossComponent().getPointWithSrcAndCross(getOwner().getPartId(), srcPoint, regionId);
            }
            // 找不到目标可通行点，过不去
            if (targetPoint == null) {
                throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("%s %s %s", getEntityId(), srcPoint, regionId));
            }
        }
        return targetPoint;
    }
}

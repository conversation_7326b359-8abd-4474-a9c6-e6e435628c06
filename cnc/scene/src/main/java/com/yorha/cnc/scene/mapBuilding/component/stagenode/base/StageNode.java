package com.yorha.cnc.scene.mapBuilding.component.stagenode.base;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.MapBuildingBuildComponent;
import com.yorha.cnc.scene.mapBuilding.component.MapBuildingOccupyComponent;
import com.yorha.cnc.scene.mapBuilding.component.MapBuildingStageMgrComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.game.gen.prop.ConstructInfoProp;
import com.yorha.game.gen.prop.OccupyInfoProp;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.OccupyState;
import res.template.TerritoryBuildingTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public abstract class StageNode {
    /**
     * 所属者
     */
    private final MapBuildingEntity owner;

    protected StageNode(MapBuildingEntity owner) {
        this.owner = owner;
    }

    public OccupyInfoProp getProp() {
        return owner.getProp().getOccupyinfo();
    }

    public ConstructInfoProp getConstructInfoProp() {
        return getOwner().getProp().getConstructInfo();
    }

    public MapBuildingEntity getOwner() {
        return owner;
    }

    public SceneActor ownerActor() {
        return getOwner().ownerActor();
    }

    public MapBuildingStageMgrComponent getComponent() {
        return owner.getStageMgrComponent();
    }

    public MapBuildingOccupyComponent getOccupyMgr() {
        return getOwner().getOccupyComponent();
    }

    public MapBuildingBuildComponent getBuildMgr() {
        return getOwner().getBuildComponent();
    }

    public TerritoryBuildingTemplate getTemplate() {
        return getOwner().getTerritoryBuildingTemplate();
    }

    public SceneClanEntity getOwnerSceneClan() {
        if (getProp().getOwnerClanId() == 0) {
            return null;
        }
        return getOwner().getScene().getClanMgrComponent().getSceneClan(getProp().getOwnerClanId());
    }

    public SceneClanEntity getOccupySceneClan() {
        if (getProp().getOccupyClanId() == 0) {
            return null;
        }
        return getOwner().getScene().getClanMgrComponent().getSceneClan(getProp().getOccupyClanId());
    }

    /**
     * 获取当前阶段
     */
    public abstract OccupyState getStage();

    /**
     * 从db加载出来
     */
    public abstract void onLoad();

    /**
     * 进入阶段
     */
    public abstract void onEnter(long enterTs);

    /**
     * 离开阶段  返回新的阶段
     */
    public void onLeave() {
        getComponent().cancelStageTimer();
    }

    /**
     * 增加阶段转换定时器
     *
     * @param runnable 回调
     * @param waitTime 等待时间
     * @param unit     等待时间单位
     */
    public void addStageTimer(Runnable runnable, long waitTime, TimeUnit unit) {
        owner.getTimerComponent().addTimer(TimerReasonType.STAGE_NODE_TIMER, runnable, waitTime, unit);
        getComponent().setStageTimer(TimerReasonType.STAGE_NODE_TIMER);
    }

    public void addStageTimer(Runnable runnable) {
        long waitTimeMs = getProp().getStateEndTsMs() - SystemClock.now();
        owner.getTimerComponent().addTimer(TimerReasonType.STAGE_NODE_TIMER, runnable, waitTimeMs, TimeUnit.MILLISECONDS);
        getComponent().setStageTimer(TimerReasonType.STAGE_NODE_TIMER);
    }

    public long getEntityId() {
        return getOwner().getEntityId();
    }

    @Override
    public String toString() {
        return ClassNameCacheUtils.getSimpleName(getClass()) + " ownerClanId=" + getProp().getOwnerClanId();
    }
}

package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.mainScene.IMainScene;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.SceneMileStoneModelProp;
import res.template.ConstTemplate;

/**
 * 大世界里程碑
 *
 * <AUTHOR>
 */
public class ZoneMilestoneComponent extends MileStoneMgrComponent<ZoneEntity> {

    public ZoneMilestoneComponent(ZoneEntity owner) {
        super(owner, owner);
    }

    public ZoneEntity getZoneEntity() {
        return getOwner().getBigScene().getZoneEntity();
    }

    @Override
    protected SceneMileStoneModelProp getProp() {
        return getZoneEntity().getProp().getSceneMileStoneModel();
    }

    @Override
    protected IMainScene getIScene() {
        return getOwner().getBigScene();
    }

    @Override
    protected boolean isCanOpen() {
        return getZoneEntity().isZoneOpen();
    }

    @Override
    public int getMileStoneStarId() {
        return ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getMileStoneStarId();
    }
}

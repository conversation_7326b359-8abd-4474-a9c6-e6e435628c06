package com.yorha.cnc.scene.resBuilding;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjTransformComponent;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.ResBuildingProp;

/**
 * <AUTHOR>
 */
public class ResBuildingBuilder extends SceneObjBuilder<ResBuildingEntity, ResBuildingProp> {
    public ResBuildingBuilder(SceneEntity sceneEntity, long eid, ResBuildingProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public SceneObjTransformComponent transformComponent(ResBuildingEntity owner) {
        return new BuildingTransformComponent(owner, owner, getPointProp());
    }

}

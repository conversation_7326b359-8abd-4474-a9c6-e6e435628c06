package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.actor.SceneRallyAssistService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.PlayerRally.Player_FetchCityInnerArmy_S2C;
import com.yorha.proto.PlayerRally.Player_FetchWarningList_S2C;
import com.yorha.proto.PlayerRally.Player_QueryOneRally_S2C;
import com.yorha.proto.PlayerRally.Player_QueryRallyList_S2C;
import com.yorha.proto.SsSceneRallyAssist.*;
import com.yorha.proto.StructCommonPB;
import com.yorha.proto.StructPlayerPB.RallyInfoListPB;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import com.yorha.proto.StructPlayerPB.WarningInfoListPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class SceneRallyAssistServiceImpl implements SceneRallyAssistService {
    private static final Logger LOGGER = LogManager.getLogger(SceneRallyAssistServiceImpl.class);
    private final SceneActor sceneActor;
    private final long sceneId;

    public SceneRallyAssistServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
        this.sceneId = Long.parseLong(sceneActor.getId());
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    public long getSceneId() {
        return sceneId;
    }

    @Override
    public void handleFetchRallyListAsk(FetchRallyListAsk ask) {
        RallyInfoListPB list = getScene().getClanMgrComponent().onPlayerQueryRallyInfoList(ask.getPlayerId());
        Player_QueryRallyList_S2C build = Player_QueryRallyList_S2C.newBuilder().setRallyList(list).build();
        sceneActor.answer(FetchRallyListAns.newBuilder().setMsgBytes(build.toByteString()).build());
    }

    @Override
    public void handleFetchOneRallyAsk(FetchOneRallyAsk ask) {
        RallyInfoPB info = getScene().getClanMgrComponent().onPlayerQueryOneRally(ask.getPlayerId(), ask.getRallyId());
        Player_QueryOneRally_S2C build = Player_QueryOneRally_S2C.newBuilder().setRallyInfo(info).build();
        sceneActor.answer(FetchOneRallyAns.newBuilder().setMsgBytes(build.toByteString()).build());
    }

    @Override
    public void handlePlayerCancelRallyAsk(PlayerCancelRallyAsk ask) {
        RallyEntity rallyEntity = getRallyEntity(ask.getPlayerId(), ask.getRallyId());
        rallyEntity.cancel(ask.getPlayerId());
        sceneActor.answer(PlayerCancelRallyAns.getDefaultInstance());
    }

    @Override
    public void handleRepatriateRallyMemberAsk(RepatriateRallyMemberAsk ask) {
        RallyEntity rallyEntity = getRallyEntity(ask.getPlayerId(), ask.getRallyId());
        rallyEntity.getArmyMgrComponent().repatriateMember(ask.getPlayerId(), ask.getArmyId());
        sceneActor.answer(RepatriateRallyMemberAns.getDefaultInstance());
    }

    private RallyEntity getRallyEntity(long playerId, long rallyId) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        return scenePlayer.getRallyComponent().getRallyEntityWithException(rallyId);
    }

    @Override
    public void handleSetRallyRecommendSoldierAsk(SetRallyRecommendSoldierAsk ask) {
        if (ask.getIsAssist()) {
            SceneObjEntity entity = getScene().getObjMgrComponent().getSceneObjEntity(ask.getEntityId());
            if (entity == null) {
                throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
            }
            if (entity.getEntityType() != EntityAttrOuterClass.EntityType.ET_MapBuilding) {
                throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
            }
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) entity;
            if (!ask.getIsPermission()) {
                if (mapBuildingEntity.getInnerArmyComponent().getLeaderArmyId() <= 0) {
                    throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
                }
                ArmyEntity armyEntity = mapBuildingEntity.getInnerArmyComponent().getLeaderArmy();
                if (armyEntity == null) {
                    throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
                }
                if (armyEntity.getPlayerId() != ask.getPlayerId()) {
                    throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
                }
            }

            mapBuildingEntity.setRecommendSoldierType(ask.getPlayerId(), ask.getSoldierType().getDatasList());
            sceneActor.answer(SetRallyRecommendSoldierAns.getDefaultInstance());
            return;
        }
        RallyEntity rallyEntity = getRallyEntity(ask.getPlayerId(), ask.getEntityId());
        rallyEntity.setRecommendSoldierType(ask.getPlayerId(), ask.getSoldierType().getDatasList());
        sceneActor.answer(SetRallyRecommendSoldierAns.getDefaultInstance());
    }

    @Override
    public void handleFetchWarningAsk(FetchWarningAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        WarningInfoListPB list = scenePlayer.getWarningComponent().getWarningList();
        Player_FetchWarningList_S2C.Builder builder = Player_FetchWarningList_S2C.newBuilder();
        builder.setWarningList(list);
        Player_FetchWarningList_S2C build = builder.build();
        sceneActor.answer(FetchWarningAns.newBuilder().setMsgBytes(build.toByteString()).build());
    }

    @Override
    public void handleSetWarningItemTagAsk(SetWarningItemTagAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        scenePlayer.getWarningComponent().setWarningItemIgnoreTag(ask.getArmyId(), ask.getIgnore());
        sceneActor.answer(SetWarningItemTagAns.getDefaultInstance());
    }

    @Override
    public void handleIgnoreAllWarningAsk(IgnoreAllWarningAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        scenePlayer.getWarningComponent().ignoreWarningAll();
        sceneActor.answer(IgnoreAllWarningAns.getDefaultInstance());
    }

    @Override
    public void handleFetchInnerArmyAsk(FetchInnerArmyAsk ask) {
        AbstractScenePlayerEntity operatorScenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getOperatorId());
        FetchInnerArmyAns.Builder ans = FetchInnerArmyAns.newBuilder();
        Player_FetchCityInnerArmy_S2C.Builder csRetBuilder = Player_FetchCityInnerArmy_S2C.newBuilder();

        if (!ask.hasTargetId()) {
            // 无targetId的情况默认是查询某个军团内playerId对应的内部军队信息
            AbstractScenePlayerEntity targetScenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getTargetPlayerId());
            // 非同军团不可拉取
            if (targetScenePlayer.getClanId() != operatorScenePlayer.getClanId()) {
                LOGGER.warn("targetPlayer {}' clan is {}, not the same clan with operator {}, clan {}",
                        ask.getTargetPlayerId(), targetScenePlayer.getClanId(), ask.getOperatorId(), operatorScenePlayer.getClanId());
                sceneActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
                return;
            }
            // 同军团返回target玩家id信息
            csRetBuilder.setCityAssistInfo(targetScenePlayer.getMainCity().getInnerArmyComponent().getAssistInfoPb(ask.getOperatorId()));
            csRetBuilder.setTargetId(targetScenePlayer.getMainCity().getEntityId());
            sceneActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
            return;
        }
        // 自己的城
        if (Objects.requireNonNull(operatorScenePlayer.getMainCity()).getEntityId() == ask.getTargetId()) {
            csRetBuilder.setCityAssistInfo(operatorScenePlayer.getMainCity().getInnerArmyComponent().getAssistInfoPb(ask.getOperatorId()));
            sceneActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
            return;
        }
        // 盟友的城 或者 地图建筑
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntityWithException(ask.getTargetId());
        // 非同盟不可拉取
        if (operatorScenePlayer.getClanId() == 0 || target.getClanId() != operatorScenePlayer.getClanId()) {
            sceneActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
            return;
        }
        int version = target.getVersion();
        csRetBuilder.setVersion(version);
        // 客户端版本是0，无条件返回
        if (ask.hasVersion() && ask.getVersion() != 0 && ask.getVersion() == version) {
            sceneActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
            return;
        }

        SceneObjInnerArmyComponent innerArmyComponent = target.getInnerArmyComponent();
        if (innerArmyComponent == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        csRetBuilder.setCityAssistInfo(innerArmyComponent.getAssistInfoPb(ask.getOperatorId()));
        StructCommonPB.ProgressInfoPB.Builder progressBuilder = target.getProgressInfoPBBuilder();
        if (progressBuilder != null) {
            csRetBuilder.setProgress(progressBuilder.build());
        }
        sceneActor.answer(ans.setMsgBytes(csRetBuilder.build().toByteString()).build());
    }

    @Override
    public void handleRepatriateAssistMemberAsk(RepatriateAssistMemberAsk ask) {
        SceneObjEntity target = getScene().getObjMgrComponent().getSceneObjEntityWithException(ask.getTargetId());
        SceneObjInnerArmyComponent innerArmyComponent = target.getInnerArmyComponent();
        if (innerArmyComponent == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        innerArmyComponent.repatriateArmy(ask.getPlayerId(), ask.getArmyId(), ask.getIsPermission());
        sceneActor.answer(RepatriateAssistMemberAns.getDefaultInstance());
    }

    @Override
    public void handleChangeAssistLeaderAsk(ChangeAssistLeaderAsk ask) {
        MapBuildingEntity mapBuilding = getScene().getObjMgrComponent().getSceneObjWithTypeWithException(MapBuildingEntity.class, ask.getCityId());
        mapBuilding.getInnerArmyComponent().checkAndChangeLeader(ask.getPlayerId(), ask.getIsPermission(), ask.getTargetId());
        sceneActor.answer(ChangeAssistLeaderAns.getDefaultInstance());
    }
}

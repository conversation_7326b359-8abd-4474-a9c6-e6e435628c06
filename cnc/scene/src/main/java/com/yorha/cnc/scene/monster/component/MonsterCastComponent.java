package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.cast.AbstractMonsterCastAddition;
import com.yorha.cnc.scene.monster.cast.MonsterCastAdditionType;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.resource.ResHolder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterCastTemplate;

/**
 * <AUTHOR>
 * <p>
 * 野怪蓄力
 */
public class MonsterCastComponent extends SceneObjComponent<MonsterEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MonsterCastComponent.class);
    private AbstractMonsterCastAddition castAddition;

    public MonsterCastComponent(MonsterEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        // 开启蓄力
        int cast = getOwner().getTemplate().getCast();
        MonsterCastTemplate castTemplate = ResHolder.getInstance().getValueFromMap(MonsterCastTemplate.class, cast);
        if (castTemplate != null) {
            MonsterCastAdditionType castAdditionType = MonsterCastAdditionType.typeOf(castTemplate.getAdditionType());
            if (castAdditionType == null) {
                return;
            }
            this.castAddition = castAdditionType.createAddition(castTemplate);
            getOwner().addTick(SceneTickReason.TICK_CAST);
        }
    }

    public void onTick() {
        if (castAddition == null || castAddition.isMax(getOwner())) {
            return;
        }
        castAddition.updateRate(getOwner());
        castAddition.updateValue(getOwner());
        // 蓄力完成
        if (castAddition.isMax(getOwner())) {
            getOwner().getProp().getCast().setFinish(true);
            getOwner().removeTick(SceneTickReason.TICK_CAST);
            LOGGER.info("{} cast finish", getOwner());
        }
    }

    public boolean isCastFinish() {
        if (castAddition == null) {
            return false;
        }
        return getOwner().getProp().getCast().getFinish();
    }
}

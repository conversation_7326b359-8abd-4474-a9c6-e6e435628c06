package com.yorha.cnc.scene.sceneclan.component;

import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.proto.CommonMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2023/7/6
 */
public class SceneClanDevBuffComponent extends AbstractComponent<SceneClanEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanDevBuffComponent .class);

    public SceneClanDevBuffComponent(SceneClanEntity owner) {
        super(owner);
    }

    public void addDevBuffFromClan(CommonMsg.DevBuffAddParam param) {
        // clan新增了scene上的buff，要同步给所有成员
        LOGGER.info("{} addDevBuffFromClan buff:{}", getOwner(), param);
        for (Long memberId : getOwner().getMemberComponent().getMember()) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayer(memberId);
            if (null == scenePlayer) {
                LOGGER.error("addDevBuffFromClan failed scenePlayer is null, memberId:{}", memberId);
                continue;
            }
            if (scenePlayer.getClanId() == 0 || scenePlayer.getClanId() != getOwner().getEntityId()) {
                LOGGER.error("addDevBuffFromClan failed scenePlayer clan is invalid playerClan:{} clanId:{} memberId:{}", scenePlayer.getClanId(), getOwner().getEntityId(), memberId);
                continue;
            }
            scenePlayer.getDevBuffComponent().addDevBuffByParam(param);
        }
    }

    public void removeDevBuffFromClan(CommonMsg.DevBuffRemoveParam param) {
        // clan移除了scene上的buff，要同步给所有成员
        LOGGER.info("{} removeDevBuffFromClan buff:{}", getOwner(), param);
        for (Long memberId : getOwner().getMemberComponent().getMember()) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayer(memberId);
            if (null == scenePlayer) {
                LOGGER.error("removeDevBuffFromClan failed scenePlayer is null, memberId:{}", memberId);
                continue;
            }
            if (scenePlayer.getClanId() == 0 || scenePlayer.getClanId() != getOwner().getEntityId()) {
                LOGGER.error("removeDevBuffFromClan failed scenePlayer clan is invalid playerClan:{} clanId:{} memberId:{}", scenePlayer.getClanId(), getOwner().getEntityId(), memberId);
                continue;
            }
            scenePlayer.getDevBuffComponent().removeDevBuffByParam(param);
        }
    }
}

package com.yorha.cnc.scene.entity.collision;

import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class AbstractCollisionWorld {

    protected final int width;
    protected final int height;
    protected final Map<Long, Collision> collisions = new HashMap<>();


    public AbstractCollisionWorld(int width, int height) {
        this.width = width;
        this.height = height;
    }

    public abstract boolean add(long id, Shape shape);

    public abstract boolean remove(long id);

    public abstract boolean isInCollision(Shape shape);

    public abstract Map<Long, Shape> getAllCollisionByShape(Shape shape);

    public abstract boolean checkNeedRefreshPath(Point p, int radius, long t);

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public int getCollisionsCount() {
        return collisions.size();
    }

    public boolean hasCollisionObj(long id) {
        return collisions.containsKey(id);
    }
}
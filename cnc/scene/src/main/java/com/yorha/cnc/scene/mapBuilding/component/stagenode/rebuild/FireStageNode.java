package com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild;

import com.yorha.cnc.scene.event.mapbuilding.FireSpeedChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.NeutralStageNode;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 着火状态
 *
 * <AUTHOR>
 */
public class FireStageNode extends ClanBuildingNode {
    private static final Logger LOGGER = LogManager.getLogger(FireStageNode.class);
    private EventListener listener;

    public FireStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_FIRE;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2FireFinish);
            listener = getOwner().getEventDispatcher().addEventListenerRepeat(this::onFireSpeedChange, FireSpeedChangeEvent.class);
            restoreTriggerFireClanToBuilding();
            return;
        }
        // 马上就跳阶段 不用跟clan那边说建立映射了 自己也删掉
        getProp().setTriggerFireClanId(0L);
        onFireFinish(getProp().getStateEndTsMs());
    }

    /**
     * 恢复触发着火的军团到建筑的关系
     */
    private void restoreTriggerFireClanToBuilding() {
        long triggerFireClanId = getProp().getTriggerFireClanId();
        if (triggerFireClanId <= 0) {
            return;
        }
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(triggerFireClanId);
        if (null == sceneClan) {
            LOGGER.error("restore trigger scene clan {} failed, cannot get scene clan entity", triggerFireClanId);
            return;
        }
        sceneClan.getBuildComponent().addBuildingWhenTriggerFire(getOwner().getPartId());
    }

    @Override
    public void onEnter(long ts) {
        getProp().setState(OccupyState.TOS_FIRE).setStateStartTsMs(ts);
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2FireFinish);
            listener = getOwner().getEventDispatcher().addEventListenerRepeat(this::onFireSpeedChange, FireSpeedChangeEvent.class);
            return;
        }
        onFireFinish(getProp().getStateEndTsMs());
        // 返回所有部队
        getOwner().getInnerArmyComponent().returnAllArmy();
    }

    @Override
    protected void afterRefreshMaxHp(int oldMaxHp, int newMaxHp) {
        // 如果是变大了 那无所谓
        if (oldMaxHp <= newMaxHp) {
            return;
        }
        // 变小了  需要减掉差值
        int newCurHp = getConstructInfoProp().getCurrentDurability() - oldMaxHp + newMaxHp;
        getConstructInfoProp().setCurrentDurability(newCurHp);
        // 结算下 再刷下燃烧
        refreshDurability();
        // 更新状态数据
        updateFireStatus();
    }

    @Override
    public void onHpDec(int decNum) {
        refreshDurability();
        super.onHpDec(decNum);
        // 更新状态数据
        updateFireStatus();
    }

    private void updateFireStatus() {
        // 已经结束或者扣完了
        long now = SystemClock.now();
        if (now >= getProp().getStateEndTsMs() || getConstructInfoProp().getCurrentDurability() == 0) {
            onFireFinish(now);
            return;
        }
        // 更新下状态
        resetFireSpeed(-1);
    }

    private void time2FireFinish() {
        getComponent().clearStageTimer();
        onFireFinish(SystemClock.now());
    }

    private void onFireFinish(long ts) {
        if (getProp().getWouldOverBurn()) {
            // 建筑的数据处理下
            onDestroyBuilding(false, false);
            // 回归中立
            getComponent().transNewNode(new NeutralStageNode(getOwner()), ts);
            return;
        }
        // 结算 阶段切换
        refreshDurability();
        getComponent().transNewNode(new RecoverStageNode(getOwner()));
    }

    private void onFireSpeedChange(FireSpeedChangeEvent e) {
        // 结算燃烧状态
        refreshDurability();
        // 更新着火速度
        resetFireSpeed(e.getAddition());
    }

    /**
     * 重置着火速度  状态刷新
     * addition为-1表示非燃烧速度变更导致的 不需要更新燃烧速度
     */
    private void resetFireSpeed(long addition) {
        // check重建进度
        long now = SystemClock.now();
        if (now >= getProp().getStateEndTsMs()) {
            onFireFinish(now);
            return;
        }
        setFireData(addition, now - getProp().getStateStartTsMs());
        // 重新设置后的时间是以前的时间
        if (getProp().getStateEndTsMs() <= SystemClock.now()) {
            onFireFinish(getProp().getStateEndTsMs());
            return;
        }
        addStageTimer(this::time2FireFinish);
        LOGGER.info("{} resetFireSpeed", getOwner());
    }

    /**
     * 外部
     */
    public void onStopFire() {
        // 结算下燃烧的hp
        refreshDurability();
        // 如果当前没烧完，会设置下是否燃烧完  这样下面走finish流程就正常了
        if (getConstructInfoProp().getCurrentDurability() > 0) {
            getProp().setWouldOverBurn(false);
            getConstructInfoProp().setIsOnFire(false);
        }
        // 不灭火情况下总耐久度损耗大于现有hp时记录不灭火会烧毁
        onFireFinish(SystemClock.now());
    }

    private void tryRemoveTriggerFireClan() {
        // 建筑灭火或烧毁，删除造成着火状态的军团对此建筑的索引
        if (getProp().getTriggerFireClanId() <= 0) {
            return;
        }
        long triggerFireClanId = getProp().getTriggerFireClanId();
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClan(triggerFireClanId);
        if (sceneClan == null) {
            // 可能会出现触发着火的军团已经解散的情况，故此处仅打info
            LOGGER.info("try disconnect clan {} with building {}, while clan not found", triggerFireClanId, getEntityId());
            return;
        }
        sceneClan.getBuildComponent().removeBuildingWhenTriggerFire(getOwner().getPartId());
        getProp().setTriggerFireClanId(0L);
    }

    @Override
    public void onAttackSuccess(long clanId) {
        // 如果在燃烧中 不做处理
    }

    @Override
    public void onLeave() {
        super.onLeave();
        if (listener != null) {
            listener.cancel();
            listener = null;
        }
        tryRemoveTriggerFireClan();
        getProp().setWouldOverBurn(false);
        getConstructInfoProp().setIsOnFire(false);
    }
}


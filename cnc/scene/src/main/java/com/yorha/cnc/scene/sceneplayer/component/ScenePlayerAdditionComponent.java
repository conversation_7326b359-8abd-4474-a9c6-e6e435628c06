package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerAdditionComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.addition.ScenePlayerAdditionMgr;
import com.yorha.game.gen.prop.AdditionSysProp;

/**
 * <AUTHOR>
 */
public class ScenePlayerAdditionComponent extends AbstractScenePlayerAdditionComponent {
    public ScenePlayerAdditionComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        additionMgr = new ScenePlayerAdditionMgr(getOwner());
        additionMgr.register(getOwner().getDevBuffComponent());
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    protected AdditionSysProp getAdditionProp() {
        return getOwner().getProp().getAdditionSys();
    }
}

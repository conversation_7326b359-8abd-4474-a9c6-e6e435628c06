package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.mainScene.bigScene.component.BigScenePlayerMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.kindomSkil.KingdomSkill;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.KingdomHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import it.unimi.dsi.fastutil.longs.Long2IntOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import qlog.flow.QlogCncKingdomActivate;
import res.template.ConstKingdomTemplate;
import res.template.KingdomBuffTemplate;
import res.template.KingdomGiftTemplate;
import res.template.KingdomSkillTemplate;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 王国
 *
 * <AUTHOR>
 */
public class ZoneKingdomComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneKingdomComponent.class);
    private final Set<Long> alreadyGiveGiftPlayers = new LongOpenHashSet();
    /**
     * 玩家id——> 职位id
     */
    private final Map<Long, Integer> playerIdToOfficeIdMap = new Long2IntOpenHashMap();
    private boolean refreshKingdomGift = false;

    public ZoneKingdomComponent(ZoneEntity owner) {
        super(owner);
    }

    private boolean settleGainTax = false;

    @Override
    public void postInit() {
        super.postInit();
        restoreGiftMap();
        restoreOfficeMap();
        restoreGiftTimer();
        restoreGainTaxTimer();
        KingdomHelper.initHistoryKingCache(ownerActor(), getNowKingNumber());
    }

    @Override
    public void onDestroy() {
        if (refreshKingdomGift) {
            getOwner().getTimerComponent().cancelTimer(TimerReasonType.KINGDOM_GIFT_REFRESH);
        }
        refreshKingdomGift = false;
    }

    public Set<Long> getOfficePlayers() {
        return playerIdToOfficeIdMap.keySet();
    }

    /**
     * 任命本王国玩家官职
     *
     * @param operatorId       操作者的id
     * @param operatorOfficeId 操作者的职位id
     * @param newOfficeId      新的职位id
     * @param toPlayerId       被任命的玩家id
     */
    public void appoint(long operatorId, int operatorOfficeId, int newOfficeId, long toPlayerId) throws GeminiException {
        // 判断被任命的玩家是不是本王国的玩家，how？

        // 获取玩家之前的职位
        int oldOfficeId = getKingdomOffice(toPlayerId);

        // 如果玩家之前的职位跟任命的玩家职位是同阶级甚至更高阶级的，是不能任命的
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        if (oldOfficeId != 0 && !service.isCanAppointRank(operatorOfficeId, oldOfficeId)) {
            LOGGER.info("appoint failed, operatorOfficeId:{} player:{} oldOfficeId:{}", operatorOfficeId, toPlayerId, oldOfficeId);
            throw new GeminiException(ErrorCode.KINGDOM_CANNOT_APPOINT_HIGH_POSITION);
        }

        // 新职位原来有人，发给toPlayerId替换职位信息，生效buff，发给新职位原来的人，删除buff
        long beforeInNewOfficePlayer = applyOfficeChange(toPlayerId, oldOfficeId, newOfficeId);
        if (beforeInNewOfficePlayer == toPlayerId) {
            LOGGER.warn("appoint error, toPlayerId:{} already in newOfficeId:{}", toPlayerId, newOfficeId);
            throw new GeminiException(ErrorCode.KINGDOM_CANNOT_APPOINT);
        }
        boolean needReplace = beforeInNewOfficePlayer != 0L;

        // 通知player职位变化
        if (needReplace) {
            sendOfficeChangeToPlayer(beforeInNewOfficePlayer, newOfficeId, 0, null);
            playerIdToOfficeIdMap.remove(beforeInNewOfficePlayer);
        }
        // 通知player change buff、card 发跑马灯
        String clanSimpleName = null;
        ScenePlayerEntity scenePlayerOrNull = getOwner().getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(toPlayerId);
        if (scenePlayerOrNull != null) {
            if (scenePlayerOrNull.getSceneClan() == null) {
                clanSimpleName = "";
            } else {
                clanSimpleName = scenePlayerOrNull.getSceneClan().getClanSimpleName();
            }
        }
        sendOfficeChangeToPlayer(toPlayerId, oldOfficeId, newOfficeId, clanSimpleName);
        playerIdToOfficeIdMap.put(toPlayerId, newOfficeId);
    }

    public void openBuff(int buffId, String playerName) throws GeminiException {
        KingdomBuffTemplate template = ResHolder.findTemplate(KingdomBuffTemplate.class, buffId);
        if (null == template) {
            LOGGER.error("openBuff error, buffId:{}", buffId);
            return;
        }
        // 开buff
        long now = SystemClock.now();
        long buffEndTsMs = now + TimeUtils.second2Ms(template.getBuffTime());
        getOwner().getDevBuffComponent().addDevBuff(template.getBuffID(), buffEndTsMs, CommonEnum.DevBuffSourceType.DBST_KINGDOM);

        // 设置zone上其他必要的值
        long cdEndTsMs = now + TimeUtils.second2Ms(ResHolder.getConsts(ConstKingdomTemplate.class).getKingdomBuffCD());
        KingdomBuffModelProp buffModelProp = getKingdomBuffModel();
        buffModelProp.setActiveBuffId(buffId);
        buffModelProp.setBuffEndTsMs(buffEndTsMs);
        buffModelProp.setCdEndTsMs(cdEndTsMs);

        // 发送全服邮件
        sendBuffMail(buffId, playerName);
        // qlog
        sendSkillOrBuffQlog("activate_buff", buffId, getZoneKingPlayerId(), 0);
    }

    public void sendGift(int kingdomGiftId, long toPlayerId, String clanSimpleName, Struct.PlayerCardHead cardHead) throws GeminiException {
        // 检查能不能发礼物
        checkCanSendGift(kingdomGiftId, toPlayerId);
        // 记录赠礼信息
        recordGiftInfo(kingdomGiftId, toPlayerId, clanSimpleName, cardHead);
        // 发邮件给这个玩家
        sendGiftMail(kingdomGiftId, toPlayerId);
    }

    private void checkCanUseSkill(int skillId) {
        // 有没有国王呢
        if (!hasKing()) {
            throw new GeminiException(ErrorCode.KINGDOM_NO_KING);
        }
        MapBuildingEntity kingCity = getOwner().getBigScene().getBuildingMgrComponent().getKingCity();
        if (kingCity == null) {
            throw new GeminiException(ErrorCode.KINGDOM_NO_KING);
        }
        if (kingCity.getClanId() <= 0) {
            throw new GeminiException(ErrorCode.KINGDOM_NO_KING);
        }
        // 获取技能
        KingdomSkill skill = KingdomSkill.getBySkillId(skillId);
        if (skill == null) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_NOT_EXIST);
        }
        // 判断技能是否在CD中
        if (skillInCd(skillId)) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_IN_CD);
        }
    }

    public void checkCanUseSkill(int skillId, long targetId, int targetZoneId, ActorMsgEnvelope actorMsgEnvelope) {
        checkCanUseSkill(skillId);
        KingdomSkill skill = KingdomSkill.getBySkillId(skillId);
        if (skill == null) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_NOT_EXIST);
        }
        // 在本服检查
        ErrorCode errorCode = skill.checkCanUse(skillId, targetId, targetZoneId, getOwner());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        getOwner().ownerActor().answer(SsSceneKingdom.KingCheckCanUseSkillAns.getDefaultInstance());
    }

    public void useSkill(int skillId, long targetId, int targetZoneId) throws GeminiException {
        checkCanUseSkill(skillId);
        // 先记录技能生效进入生效+CD
        kingdomSkillRecordInfo(skillId);
        KingdomSkill skill = KingdomSkill.getBySkillId(skillId);
        if (skill == null) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_NOT_EXIST);
        }
        skill.checkCanUse(skillId, targetId, targetZoneId, getOwner());
        skill.takeEffect(skillId, targetId, getOwner(), targetZoneId);

        LOGGER.info("useSkill skillId={}, targetId={} targetZoneId={} success", skillId, targetId, targetZoneId);
        // qlog
        sendSkillOrBuffQlog("use_skill", skillId, getZoneKingPlayerId(), targetId);
    }

    /**
     * @param taxResourceType 税收资源类型
     * @param resourceNum     玩家获得的基础资源数
     * @return 返回国王赋税收取的数量
     */
    public long getGainTaxDecCount(int taxResourceType, long resourceNum) {
        if (!isGainTaxOpen()) {
            return 0L;
        }
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        long tryDecNum = resourceNum * service.getGainTaxDecRate() / Constants.N_10_000;
        if (tryDecNum == 0) {
            return 0L;
        }
        TaxGetResourceInfoProp resourceInfoProp = getTaxGetResourceInfo(taxResourceType);
        long beforeNum = resourceInfoProp.getNowNum();
        if (beforeNum + tryDecNum > service.getMaxTaxNum()) {
            tryDecNum = service.getMaxTaxNum() - resourceInfoProp.getNowNum();
        }
        resourceInfoProp.setNowNum(resourceInfoProp.getNowNum() + tryDecNum);
        LOGGER.info("getGainTaxDecCount, taxResourceType:{}, resourceNum:{}, tryDecNum:{}, before:{}, after:{}",
                taxResourceType, resourceNum, tryDecNum, beforeNum, resourceInfoProp.getNowNum());
        return tryDecNum;
    }

    /**
     * @return 返回赋税技能是否开启
     */
    private boolean isGainTaxOpen() {
        // 最多几个技能循环不了几次
        for (KingdomSkillInfoProp prop : getKingdomSkillModel().getSkillInfo().values()) {
            // 当前技能还在生效
            if (SystemClock.now() < prop.getSkillEndTsMs()) {
                if (prop.getSkillId() == KingdomSkill.GAIN_TAX.getSkillId()) {
                    return true;
                }
            }
        }
        return false;
    }

    private long getGainTaxEndTsMs() {
        // 最多几个技能循环不了几次
        for (KingdomSkillInfoProp prop : getKingdomSkillModel().getSkillInfo().values()) {
            // 当前技能还在生效
            if (SystemClock.now() < prop.getSkillEndTsMs()) {
                if (prop.getSkillId() == KingdomSkill.GAIN_TAX.getSkillId()) {
                    return prop.getSkillEndTsMs();
                }
            }
        }
        return 0L;
    }

    /**
     * 技能是否在CD中
     *
     * @param skillId 技能ID
     * @return true==在CD
     */
    private boolean skillInCd(int skillId) {
        KingdomSkillInfoProp skillInfoProp = this.getOwner().getProp().getKingdomModel().getSkillModel().getSkillInfo().getOrDefault(skillId, null);

        if (skillInfoProp == null) {
            return false;
        }
        return skillInfoProp.getCdEndTsMs() > SystemClock.now();
    }

    /**
     * 记录王国技能使用信息
     *
     * @param skillId 技能ID
     */
    private void kingdomSkillRecordInfo(int skillId) {
        KingdomSkillTemplate kingdomSkillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getKingdomSkillById(skillId);
        if (kingdomSkillTemplate == null) {
            LOGGER.error("kingdomSkillRecordInfo error, skillId:{}", skillId);
            return;
        }
        int skillCdSecond = kingdomSkillTemplate.getCd();
        int effectDurationSecond = kingdomSkillTemplate.getEffectTime();
        long curTsMs = SystemClock.now();
        // 技能生效结束后开始进入CD
        long skillEndTsMs = curTsMs + TimeUtils.second2Ms(effectDurationSecond);
        long skillCdEndTsMs = curTsMs + TimeUtils.second2Ms(skillCdSecond + effectDurationSecond);
        this.getOwner().getProp().getKingdomModel().getSkillModel().putSkillInfoV(
                new KingdomSkillInfoProp()
                        .setSkillId(skillId)
                        .setSkillEndTsMs(skillEndTsMs)
                        .setCdEndTsMs(skillCdEndTsMs));
        LOGGER.info("kingdomSkillRecordInfo success, skillId: {}, skillEndTsMs: {}, skillCdEndTsMs: {}", skillId, skillEndTsMs, skillCdEndTsMs);

    }

    public boolean hasKing() {
        return getZoneKingPlayerId() != 0L;
    }

    public long getZoneKingPlayerId() {
        KingdomTemplateResService kingdomTemplateResService = ResHolder.getResService(KingdomTemplateResService.class);
        int officeId = kingdomTemplateResService.getKingOfficeId();
        KingdomOfficeInfoProp kingInfoProp = getKingdomOfficeInfo(officeId);
        if (kingInfoProp == null) {
            return 0L;
        } else {
            return kingInfoProp.getPlayerId();
        }
    }

    /**
     * @param page 从1开始，每页HistoryPerPageNum条
     * @return 页数 + 总页数 + 历任国王的信息 + 当前国王的信息（可能没有）
     */
    public void fetchHistoryKing(ActorMsgEnvelope envelope, int page) throws GeminiException {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        int perPageNum = service.getConstTemplate().getKingHistoryPerPageNum();
        SsSceneKingdom.FetchHistoryKingAns.Builder retBuilder = SsSceneKingdom.FetchHistoryKingAns.newBuilder();
        int nowKingNumber = getNowKingNumber();
        int totalPage = nowKingNumber / perPageNum + 1;
        if (page > totalPage) {
            LOGGER.warn("fetchHistoryKing page:{} > totalPage:{}", page, totalPage);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 返回给客户端是倒序的，page=1就是从最新的往下查询，right=nowKingNumber
        int right = Math.max(nowKingNumber - ((page - 1) * perPageNum), 1);
        int left = Math.max(right - perPageNum + 1, 1);
        // 先填充一些prop里或ask中就有的数据
        if (hasKing()) {
            // 读取当前国王的信息
            StructMsg.SimpleKingInfo kindInfo = KingdomHelper.getKindInfo(getNowKingNumber());
            if (kindInfo != null) {
                retBuilder.setCurKingInfo(kindInfo);
            }
        }
        retBuilder.setPage(page);
        retBuilder.setTotalPage(totalPage);
        int maxCacheNum = KingdomHelper.getMaxCacheHistoryKingCount();
        // 理论上缓存中最小的num
        int minCacheNum = Math.max(nowKingNumber - maxCacheNum + 1, 1);
        LOGGER.info("fetchHistoryKing page={} nowKingNumber={} left={} right={} maxCacheNum={} minCacheNum={}", page, nowKingNumber, left, right, maxCacheNum, minCacheNum);
        if (left >= minCacheNum) {
            // 从缓存读，倒序给客户端
            for (int num = right; num >= left; --num) {
                StructMsg.SimpleKingInfo kingInfo = KingdomHelper.getKindInfo(num);
                if (null == kingInfo) {
                    LOGGER.error("{} not in cache, but we expect it in cache", num);
                    continue;
                }
                retBuilder.addKingInfos(kingInfo);
            }
            // 主动回包
            ownerActor().answerWithContext(envelope, retBuilder.build());
        } else {
            KingdomHelper.loadHistoryKingFromDb(ownerActor(), envelope, retBuilder, left, right);
        }
    }

    public SsSceneKingdom.FetchKingdomGiftAns.Builder fetchGiftInfo(boolean isFetchingLeftNum, int specificGiftId, long checkPlayerId) throws GeminiException {
        SsSceneKingdom.FetchKingdomGiftAns.Builder retBuilder = SsSceneKingdom.FetchKingdomGiftAns.newBuilder();
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        Int32KingdomGiftInfoMapProp giftInfoMapProp = getKingdomGiftModel().getGiftInfo();
        if (isFetchingLeftNum) {
            for (int giftId : service.getAllGiftIds()) {
                int totalCanGive = service.getTotalCanGiveNum(giftId);
                int leftNum = totalCanGive;
                if (giftInfoMapProp.containsKey(giftId)) {
                    leftNum = totalCanGive - giftInfoMapProp.get(giftId).getSentGiftInfoSize();
                }
                retBuilder.putLeftNum(giftId, leftNum);
            }
        } else {
            if (!giftInfoMapProp.containsKey(specificGiftId)) {
                // 有可能对应礼物数据是还没发的
                return retBuilder.setGiftInfo(Zone.KingdomGiftInfo.getDefaultInstance());
            }
            retBuilder.setGiftInfo(giftInfoMapProp.get(specificGiftId).getCopySsBuilder());
        }
        if (!alreadyGiveGiftPlayers.contains(checkPlayerId)) {
            retBuilder.setCanGiveGift(true);
        }
        return retBuilder;
    }

    public SsSceneKingdom.FetchKingdomOfficeAns.Builder fetchOfficeInfo() throws GeminiException {
        SsSceneKingdom.FetchKingdomOfficeAns.Builder retBuilder = SsSceneKingdom.FetchKingdomOfficeAns.newBuilder();
        Int32KingdomOfficeInfoMapProp kingdomOfficeInfoMapProp = getKingdomOfficeModel().getOfficeInfo();
        for (KingdomOfficeInfoProp officeInfoProp : kingdomOfficeInfoMapProp.values()) {
            int officeId = officeInfoProp.getOfficeId();
            long playerId = officeInfoProp.getPlayerId();
            ZonePB.KingdomOfficeInfoPB.Builder officeInfoBuilder = ZonePB.KingdomOfficeInfoPB.newBuilder();
            officeInfoBuilder.setOfficeId(officeId);
            officeInfoBuilder.setPlayerId(playerId);
            // 找人
            AbstractScenePlayerEntity scenePlayer = getOwner().getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
            if (null == scenePlayer) {
                retBuilder.putOfficeInfo(officeId, officeInfoBuilder.build());
                continue;
            }
            officeInfoBuilder.getPlayerCardHeadBuilder().mergeFrom(scenePlayer.getCardHead().getCopyCsBuilder().build());
            SceneClanEntity sceneClanEntity = scenePlayer.getSceneClan();
            if (sceneClanEntity != null) {
                officeInfoBuilder.setSname(scenePlayer.getSceneClan().getClanSimpleName());
            } else {
                officeInfoBuilder.setSname("");
            }
            retBuilder.putOfficeInfo(officeId, officeInfoBuilder.build());
        }
        return retBuilder;
    }

    public void newKing(MapBuildingEntity kingCity, final SceneClanEntity sceneClan) {
        int nextKingNumber = getNowKingNumber() + 1;
        final long clanOwnerPlayerId = sceneClan.getClanOwnerId();
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        // 国王任数
        setNowKingNumber(nextKingNumber);
        // 任命国王的处理：任命，跑马灯，发送给玩家
        int oldOfficeId = getKingdomOffice(clanOwnerPlayerId);
        applyOfficeChange(clanOwnerPlayerId, oldOfficeId, service.getKingOfficeId());
        // 通知player change buff、card 发跑马灯、加国王历史
        sendOfficeChangeToPlayer(clanOwnerPlayerId, oldOfficeId, service.getKingOfficeId(), sceneClan.getClanSimpleName());
        playerIdToOfficeIdMap.put(clanOwnerPlayerId, service.getKingOfficeId());

        // 要把国王的头像信息设置上去
        AbstractScenePlayerEntity scenePlayer = getOwner().getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(clanOwnerPlayerId);
        if (scenePlayer != null) {
            kingCity.getStageMgrComponent().updateKingCardHead(scenePlayer.getCardHead().getCopyCsBuilder().build());
            return;
        }
        // 没有就只能。。。
        CardHelper.queryPlayerCardHeadASync(ownerActor(), clanOwnerPlayerId, (res) -> {
            // 这么巧 过期了。
            if (getZoneKingPlayerId() != clanOwnerPlayerId) {
                return;
            }
            if (kingCity.getOwnerClanId() != sceneClan.getEntityId()) {
                return;
            }
            kingCity.getStageMgrComponent().updateKingCardHead(res);
        });
    }

    public void kingFall(MapBuildingEntity kingCity) {
        KingdomTemplateResService kingdomTemplateResService = ResHolder.getResService(KingdomTemplateResService.class);
        KingdomOfficeInfoProp kingdomOfficeInfoProp = getKingdomOfficeModel().getOfficeInfoV(kingdomTemplateResService.getKingOfficeId());
        getKingdomOfficeModel().removeOfficeInfoV(kingdomTemplateResService.getKingOfficeId());
        if (null != kingdomOfficeInfoProp) {
            long oldKingPlayerId = kingdomOfficeInfoProp.getPlayerId();
            playerIdToOfficeIdMap.remove(oldKingPlayerId);
            // 通知player change buff、card
            KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
            sendOfficeChangeToPlayer(oldKingPlayerId, service.getKingOfficeId(), 0, null);
            // 处理场景上的city
            AbstractScenePlayerEntity oldKingPlayer = getOwner().getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(oldKingPlayerId);
            if (oldKingPlayer != null) {
                oldKingPlayer.getMainCity().getProp().getCityKingdomModel().setOfficeId(0);
            }
        }
        kingCity.getStageMgrComponent().clearKingdomData();
    }


    public void addTaxSkillInfo() {
        TaxSkillInfoProp taxSkillInfoProp = getKingdomSkillModel().getTaxSkillInfo();
        taxSkillInfoProp.setUsePlayerId(getZoneKingPlayerId());
        // 清掉上次的资源
        taxSkillInfoProp.clearTaxResource();
        setSettleGainTaxTimer();
    }

    private void restoreGainTaxTimer() {
        if (!isGainTaxOpen()) {
            return;
        }
        long settleTsMs = getGainTaxEndTsMs();
        long now = SystemClock.now();
        if (settleTsMs <= now) {
            settleGainTax();
            return;
        }
        setSettleGainTaxTimer();
    }

    private void setSettleGainTaxTimer() {
        long endTsMs = getGainTaxEndTsMs();
        if (settleGainTax) {
            LOGGER.error("last gain tax not cancel");
            getOwner().getTimerComponent().cancelTimer(TimerReasonType.KINGDOM_GAIN_TAX_SKILL_END);
        }
        getOwner().getTimerComponent().addTimer(TimerReasonType.KINGDOM_GAIN_TAX_SKILL_END,
                this::settleGainTax, endTsMs - SystemClock.now(), TimeUnit.MILLISECONDS);
        settleGainTax = true;
    }

    private void settleGainTax() {
        settleGainTax = false;
        TaxSkillInfoProp prop = getOwner().getKingdomComponent().getKingdomSkillModel().getTaxSkillInfo();
        ConstKingdomTemplate constKingdomTemplate = ResHolder.getResService(KingdomTemplateResService.class).getConstTemplate();
        int mailId = constKingdomTemplate.getKingdomSkillTaxMailID();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_KINGDOM_GAIN_TAX);

        SsPlayerKingdom.KingdomSettleGainTaxCmd.Builder toPlayerBuilder = SsPlayerKingdom.KingdomSettleGainTaxCmd.newBuilder();
        for (int currencyType : constKingdomTemplate.getKingdomSkillTaxResourceID()) {
            long addNum = 0L;
            if (prop.getTaxResource().get(currencyType) != null) {
                addNum = prop.getTaxResource().get(currencyType).getNowNum();
                toPlayerBuilder.putResource(currencyType, addNum);
            }
            mailSendParams.getContentBuilder().getKingGainTaxDataBuilder()
                    .getTaxBuilder().putDatas(currencyType, Struct.Currency.newBuilder().setType(currencyType).setCount(addNum).build());
        }
        mailSendParams.getContentBuilder().getKingGainTaxDataBuilder().setCalTsMs(getGainTaxEndTsMs());
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(prop.getUsePlayerId())
                        .setZoneId(getOwner().getZoneId())
                        .build(),
                mailSendParams.build());
        getOwner().ownerActor().tellPlayer(getOwner().getZoneId(), prop.getUsePlayerId(), toPlayerBuilder.build());
    }

    @NotNull
    private TaxGetResourceInfoProp getTaxGetResourceInfo(int taxResourceType) {
        TaxGetResourceInfoProp resourceInfoProp = getKingdomSkillModel().getTaxSkillInfo().getTaxResourceV(taxResourceType);
        if (resourceInfoProp == null) {
            resourceInfoProp = getKingdomSkillModel().getTaxSkillInfo().addEmptyTaxResource(taxResourceType);
        }
        return resourceInfoProp;
    }

    private int getKingdomOffice(long playerId) {
        return playerIdToOfficeIdMap.get(playerId) != null ? playerIdToOfficeIdMap.get(playerId) : 0;
    }

    private long applyOfficeChange(long toPlayerId, int oldOfficeId, int newOfficeId) throws GeminiException {
        // 不存在替换逻辑，自己所在的旧职位数据直接删除
        if (oldOfficeId != 0) {
            getKingdomOfficeModel().removeOfficeInfoV(oldOfficeId);
        }
        long beforeInOfficePlayerId = 0L;
        KingdomOfficeInfoProp officeInfoProp = getKingdomOfficeInfo(newOfficeId);
        BigScenePlayerMgrComponent playerMgrComponent = getOwner().getBigScene().getPlayerMgrComponent();
        // 把新职位原来的人下掉
        if (officeInfoProp != null) {
            beforeInOfficePlayerId = officeInfoProp.getPlayerId();
            AbstractScenePlayerEntity scenePlayer = getOwner().getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(officeInfoProp.getPlayerId());
            if (scenePlayer != null) {
                scenePlayer.getMainCity().getProp().getCityKingdomModel().setOfficeId(0);
            }
        }
        KingdomOfficeInfoProp newOfficeInfoProp = getKingdomOfficeModel().addEmptyOfficeInfo(newOfficeId);
        newOfficeInfoProp.setOfficeId(newOfficeId);
        newOfficeInfoProp.setPlayerId(toPlayerId);

        AbstractScenePlayerEntity scenePlayer = playerMgrComponent.getScenePlayerOrNull(toPlayerId);
        if (scenePlayer != null) {
            scenePlayer.getMainCity().getProp().getCityKingdomModel().setOfficeId(newOfficeId);
        }
        return beforeInOfficePlayerId;
    }

    private void sendOfficeChangeToPlayer(long playerId, int oldOfficeId, int newOfficeId, String clanSimpleName) {
        SsPlayerKingdom.KingdomOfficeChangeCmd.Builder changeCmd = SsPlayerKingdom.KingdomOfficeChangeCmd.newBuilder();
        changeCmd.setOldOfficeId(oldOfficeId)
                .setNewOfficeId(newOfficeId)
                .setKingNum(getNowKingNumber());
        if (clanSimpleName != null) {
            changeCmd.setClanSimpleName(clanSimpleName);
        }
        // 直接给对应玩家发消息
        IActorRef playerRef = RefFactory.ofPlayer(ownerActor().getZoneId(), playerId);
        ActorSendMsgUtils.send(playerRef, changeCmd.build());
    }

    private void checkCanSendGift(int giftId, long toPlayerId) throws GeminiException {
        if (alreadyGiveGiftPlayers.contains(toPlayerId)) {
            LOGGER.warn("checkCanSendGift: already give gift, giftId={}, toPlayerId={}", giftId, toPlayerId);
            throw new GeminiException(ErrorCode.KINGDOM_GIFT_ALREADY_GIVE);
        }
        KingdomGiftInfoProp giftInfoProp = getKingdomGiftModel().getGiftInfoV(giftId);
        if (null == giftInfoProp) {
            return;
        }
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        int totalCanGive = service.getTotalCanGiveNum(giftId);
        if (totalCanGive <= giftInfoProp.getSentGiftInfo().size()) {
            LOGGER.warn("checkCanSendGift: gift already give all, giftId={}, toPlayerId={}", giftId, toPlayerId);
            throw new GeminiException(ErrorCode.KINGDOM_GIFT_LEFT_NOT_ENOUGH);
        }
    }

    private void recordGiftInfo(int giftId, long toPlayerId, String clanSimpleName, Struct.PlayerCardHead cardHead) {
        boolean notContains = alreadyGiveGiftPlayers.add(toPlayerId);
        if (!notContains) {
            LOGGER.error("recordGiftInfo: already give gift, giftId={}, toPlayerId={}", giftId, toPlayerId);
            return;
        }
        KingdomGiftInfoProp giftInfoProp = getKingdomGiftModel().getGiftInfoV(giftId);
        if (null == giftInfoProp) {
            giftInfoProp = getKingdomGiftModel().addEmptyGiftInfo(giftId);
        }
        KingdomSentGiftInfoProp sentGiftInfoProp = giftInfoProp.addEmptySentGiftInfo(toPlayerId);
        sentGiftInfoProp.setSentTsMs(SystemClock.now()).setSname(clanSimpleName);
        sentGiftInfoProp.getPlayerCardHead().mergeFromSs(cardHead);
    }

    private void sendGiftMail(int giftId, long toPlayerId) {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        KingdomGiftTemplate giftTemplate = service.getGiftMailId(giftId);
        if (null == giftTemplate) {
            LOGGER.error("sendGiftMail: giftTemplate is null, giftId={}", giftId);
            return;
        }
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
        params.setMailTemplateId(giftTemplate.getMail());

        List<IntPairType> rewardPairList = giftTemplate.getItemPairList();
        for (IntPairType rewardPair : rewardPairList) {
            Struct.ItemPair itemPair = Struct.ItemPair.newBuilder()
                    .setItemTemplateId(rewardPair.getKey())
                    .setCount(rewardPair.getValue())
                    .build();
            params.getItemRewardBuilder()
                    .addDatas(itemPair);
        }
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(toPlayerId)
                        .setZoneId(getOwner().getZoneId())
                        .build(),
                params.build());
    }

    private void restoreGiftMap() {
        alreadyGiveGiftPlayers.clear();
        Int32KingdomGiftInfoMapProp giftInfoMapProp = getKingdomGiftModel().getGiftInfo();
        for (int giftId : giftInfoMapProp.keySet()) {
            giftInfoMapProp.get(giftId).getSentGiftInfo().forEach((playerId, sentGiftInfoProp) -> {
                alreadyGiveGiftPlayers.add(playerId);
            });
        }
    }

    private void restoreOfficeMap() {
        Int32KingdomOfficeInfoMapProp officeInfoMapProp = getKingdomOfficeModel().getOfficeInfo();
        for (int officeId : officeInfoMapProp.keySet()) {
            KingdomOfficeInfoProp officeInfoProp = officeInfoMapProp.get(officeId);
            if (officeInfoProp.getPlayerId() > 0L) {
                playerIdToOfficeIdMap.put(officeInfoProp.getPlayerId(), officeId);
            }
        }
    }

    public StructMail.MailSendParams.Builder getCommonBuffOrSkillMail(int mailId, String playerName) {
        // 找到王国要塞 拿联盟简称
        MapBuildingEntity kingCity = getOwner().getBigScene().getBuildingMgrComponent().getKingCity();
        if (null == kingCity) {
            LOGGER.error("kingCity is null");
            return null;
        }
        StructMail.MailSendParams.Builder mailBuilder = StructMail.MailSendParams.newBuilder();
        mailBuilder.setMailTemplateId(mailId);
        Struct.DisplayData.Builder subTitleDisPlayerData = Struct.DisplayData.newBuilder().setParams(
                Struct.DisplayParamList.newBuilder()
                        .addDatas(Struct.DisplayParam.newBuilder().setType(CommonEnum.DisplayParamType.DPT_TEXT).setText(kingCity.getClanSimpleName()))
                        .addDatas(Struct.DisplayParam.newBuilder().setType(CommonEnum.DisplayParamType.DPT_TEXT).setText(playerName)));
        mailBuilder.getTitleBuilder().setSubTitleData(subTitleDisPlayerData);
        mailBuilder.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        Struct.DisplayData.Builder displayDataBuilder = mailBuilder.getContentBuilder().getDisplayDataBuilder();
        displayDataBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(kingCity.getClanSimpleName()));
        displayDataBuilder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(playerName));
        return mailBuilder;
    }

    private void sendBuffMail(int buffId, String playerName) {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        StructMail.MailSendParams.Builder mailBuilder = getCommonBuffOrSkillMail(service.getConstTemplate().getKingdomBuffMailID(), playerName);
        if (mailBuilder == null) {
            LOGGER.info("mail builder is null");
            return;
        }
        mailBuilder.getContentBuilder().getKingBuffDataBuilder().setConfigId(buffId).setIsSkillData(false);

        MailUtil.sendZoneMail(getOwner().getZoneId(), mailBuilder.build());
    }

    private void restoreGiftTimer() {
        long now = SystemClock.now();
        long nextRefreshGiftTsMs = getKingdomGiftModel().getRefreshGiftTsMs();
        if (nextRefreshGiftTsMs != 0 && now >= nextRefreshGiftTsMs) {
            // 重启的时候正好需要刷新了
            refreshGift();
        }
        if (refreshKingdomGift) {
            // 还有定时器
            return;
        }
        nextRefreshGiftTsMs = TimeUtils.getNextWeekStartMs(now);
        getKingdomGiftModel().setRefreshGiftTsMs(nextRefreshGiftTsMs);
        getOwner().getTimerComponent().addTimer(TimerReasonType.KINGDOM_GIFT_REFRESH,
                this::refreshGift, nextRefreshGiftTsMs - now, TimeUnit.MILLISECONDS);
        refreshKingdomGift = true;
    }

    private void refreshGift() {
        refreshKingdomGift = false;
        // 刷新礼物
        getKingdomGiftModel().clearGiftInfo();
        // 内存数据清空
        alreadyGiveGiftPlayers.clear();
        // reset下次刷新礼物的时间
        getKingdomGiftModel().setRefreshGiftTsMs(0L);
        // 尝试restore
        restoreGiftTimer();
    }

    private KingdomGiftModelProp getKingdomGiftModel() {
        return getOwner().getProp().getKingdomModel().getGiftModel();
    }

    private KingdomBuffModelProp getKingdomBuffModel() {
        return getOwner().getProp().getKingdomModel().getBuffModel();
    }

    private KingdomSkillModelProp getKingdomSkillModel() {
        return getOwner().getProp().getKingdomModel().getSkillModel();
    }

    private KingdomOfficeModelProp getKingdomOfficeModel() {
        return getOwner().getProp().getKingdomModel().getOfficeModel();
    }

    public int getNowKingNumber() {
        return getOwner().getProp().getKingdomModel().getNowKingNumber();
    }

    private void setNowKingNumber(int newNowKingNumber) {
        int nowKingNumber = getNowKingNumber();
        if (newNowKingNumber <= nowKingNumber) {
            LOGGER.error("setNowKingNumber: newNowKingNumber={} <= nowKingNumber={}", newNowKingNumber, nowKingNumber);
            return;
        }
        getOwner().getProp().getKingdomModel().setNowKingNumber(newNowKingNumber);
    }

    private KingdomOfficeInfoProp getKingdomOfficeInfo(int officeId) {
        return getKingdomOfficeModel().getOfficeInfoV(officeId);
    }

    private void sendSkillOrBuffQlog(String action, int configId, long operatorId, long targetId) {
        QlogCncKingdomActivate.init(getOwner().getBigScene().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setConfigId(configId)
                .setOperationRoleId(String.valueOf(operatorId))
                .setTargetRoleId(String.valueOf(targetId))
                .sendToQlog();
    }

    public void playerMigrateOut(long playerId) {
        int oldOfficeId = getKingdomOffice(playerId);
        if (oldOfficeId <= 0) {
            return;
        }

        KingdomOfficeInfoProp kingdomOfficeInfo = getKingdomOfficeInfo(oldOfficeId);
        if (kingdomOfficeInfo == null) {
            LOGGER.error("ZoneKingdomComponent playerMigrateOut player={} oldOfficeId={} but prop is null", playerId, oldOfficeId);
        } else {
            if (kingdomOfficeInfo.getPlayerId() != playerId) {
                LOGGER.error("ZoneKingdomComponent playerMigrateOut player={} oldOfficeId={} playerId wrong. owner={}", playerId, oldOfficeId, kingdomOfficeInfo.getPlayerId());
            } else {
                // 清掉头衔
                getKingdomOfficeModel().removeOfficeInfoV(oldOfficeId);
            }
        }
        playerIdToOfficeIdMap.remove(playerId);
    }

    @Override
    public SceneActor ownerActor() {
        return (SceneActor) super.ownerActor();
    }
}

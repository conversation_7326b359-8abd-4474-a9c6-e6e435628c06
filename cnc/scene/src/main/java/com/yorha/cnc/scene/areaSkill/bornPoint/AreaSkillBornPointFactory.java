package com.yorha.cnc.scene.areaSkill.bornPoint;

import com.google.common.collect.Maps;
import com.yorha.proto.CommonEnum;
import res.template.RangeSkillTemplate;

import javax.annotation.Nullable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
public class AreaSkillBornPointFactory {
    private static final Map<CommonEnum.AreaSkillBornPointType, IAreaSkillBornPoint> TYPE_MAP = Maps.newEnumMap(CommonEnum.AreaSkillBornPointType.class);

    static {
        TYPE_MAP.put(CommonEnum.AreaSkillBornPointType.ASBPT_SPECIFIC_POINT, new SpecificPoint());
        TYPE_MAP.put(CommonEnum.AreaSkillBornPointType.ASBPT_RING_RANDOM, new RandomRingPoint());
    }

    @Nullable
    public static IAreaSkillBornPoint of(RangeSkillTemplate template) {
        return TYPE_MAP.get(template.getCreateRange());
    }
}

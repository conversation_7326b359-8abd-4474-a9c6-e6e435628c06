package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.common.framework.AbstractComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;

/**
 * scene obj 组件基类
 *
 * <AUTHOR>
 */
public class SceneObjComponent<T extends SceneObjEntity> extends AbstractComponent<T> {

    public SceneObjComponent(T owner) {
        super(owner);
    }

    public void afterAllLoad() {
    }

    @Override
    public SceneActor ownerActor() {
        return (SceneActor) super.ownerActor();
    }
}

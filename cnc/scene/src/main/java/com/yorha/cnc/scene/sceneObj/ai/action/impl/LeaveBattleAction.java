package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;

import static com.yorha.proto.CommonEnum.AiRecordType.ART_BACK_TO_POINT;

/**
 * 脱战且回到原点
 * 进入条件： 仇恨列表为空
 * 进入：清空仇恨列表
 * 执行：回血，回到原点
 * 结束：无
 *
 * <AUTHOR>
 */
public class LeaveBattleAction extends AbstractAIAction {

    public LeaveBattleAction() {
        super();
    }

    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        return component.canLeaveBattle(getActionName());
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        component.getOwner().getHateListComponent().clear();
        component.addRecord(ART_BACK_TO_POINT, false);
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
        // 脱战
        component.leaveBattle(component, getActionName());
    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        component.removeRecord(ART_BACK_TO_POINT);
    }

    @Override
    protected String getActionName() {
        return "LeaveBattleAction";
    }
}

package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.devBuff.ZoneInfoDevBuffMgr;
import com.yorha.common.addition.AdditionProviderInterface;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/9
 */
public class ZoneDevBuffComponent extends AbstractComponent<ZoneEntity> implements AdditionProviderInterface {

    private ZoneInfoDevBuffMgr devBuffMgr;

    public ZoneDevBuffComponent(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        this.devBuffMgr = new ZoneInfoDevBuffMgr(getOwner());
    }

    public void afterAllLoad() {
        this.devBuffMgr.postInit();
    }

    /**
     * 添加buff
     *
     * @param sourceType 来源
     */
    public void addDevBuff(int buffId, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                null,
                devBuffMgr.getBuffType(),
                sourceType,
                null);
    }

    /**
     * 添加buff
     *
     * @param endTime    指定的结束时间戳
     * @param sourceType 来源
     */
    public void addDevBuff(int buffId, long endTime, CommonEnum.DevBuffSourceType sourceType) {
        devBuffMgr.addDevBuff(buffId,
                null,
                endTime,
                devBuffMgr.getBuffType(),
                sourceType,
                null);
    }

    @Override
    public AdditionProviderType type() {
        return AdditionProviderType.ZONE_BUFF;
    }

    @Override
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionFromProvider(Integer additionId) {
        return devBuffMgr.getAdditionValue(additionId);
    }

    public void refreshAdditionCache() {
        devBuffMgr.refreshAdditionCache();
    }
}

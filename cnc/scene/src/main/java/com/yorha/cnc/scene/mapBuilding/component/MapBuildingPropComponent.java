package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjPropComponent;
import com.yorha.game.gen.prop.MapBuildingProp;
import com.yorha.game.gen.prop.OccupyInfoProp;
import com.yorha.proto.CommonEnum.MapAreaType;

/**
 * <AUTHOR>
 */
public class MapBuildingPropComponent extends SceneObjPropComponent {
    public MapBuildingPropComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public void ntfChangeToClient(boolean needMerge) {
        super.ntfChangeToClient(needMerge);
        OccupyInfoProp prop = getOwner().getProp().getOccupyinfo();
        // 联盟id、template、state 发生变化 重新发下
        if (prop.hasMark(OccupyInfoProp.FIELD_INDEX_OWNERCLANID)
                || prop.hasMark(OccupyInfoProp.FIELD_INDEX_STATE)
                || prop.hasMark(OccupyInfoProp.FIELD_INDEX_ZONEID)
                || prop.hasMark(OccupyInfoProp.FIELD_INDEX_OCCUPYCLANID)
                || getOwner().getProp().hasMark(MapBuildingProp.FIELD_INDEX_TEMPLATEID)) {
            briefCsMsgChange();
        }
        // 通知zoneInfo 关卡归属发生改变
        if (getOwner().getAreaType() == MapAreaType.CROSSING && prop.hasMark(OccupyInfoProp.FIELD_INDEX_OWNERCLANID)) {
            if (getOwner().getScene().isBigScene()) {
                getOwner().getScene().getBigScene().getZoneEntity().getPropComponent().onPassOwnerChange(getOwner().getPartId(), getOwner().getOwnerClanId());
            }
        }
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }

}

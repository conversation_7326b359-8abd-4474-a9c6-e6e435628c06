package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;

/**
 * <AUTHOR>
 */
public class DarkAltarMonster implements MonsterFeature {
    @Override
    public boolean isRecordFirstKill() {
        return false;
    }

    @Override
    public void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity) {

    }

    @Override
    public ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public void onDead(MonsterEntity monsterEntity) {

    }

    @Override
    public void sendKillAndRewardAll(MonsterEntity monsterEntity) {
        monsterEntity.getRewardComponent().sendBaseReward();
    }
}

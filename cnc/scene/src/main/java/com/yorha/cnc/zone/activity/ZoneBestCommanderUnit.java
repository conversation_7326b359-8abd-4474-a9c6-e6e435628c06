package com.yorha.cnc.zone.activity;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.SeasonActivityHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.BestCommanderActResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.ZoneSideBestCommanderModelProp;
import com.yorha.game.gen.prop.ZoneSideBestCommanderRankSettleDataProp;
import com.yorha.game.gen.prop.ZoneSideBestCommanderRankSettleItemProp;
import com.yorha.game.gen.prop.ZoneSideBestCommanderUnitProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsSceneActivitySchedule;
import res.template.BestCommanderSeasonTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.error.ErrorCode.PARAM_PARAMETER_EXCEPTION;

/**
 * 最强指挥官赛季版活动主题
 *
 * <AUTHOR>
 */
public class ZoneBestCommanderUnit extends BaseZoneActivityUnit {
    public ZoneBestCommanderUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }


    private void init() {
        final ZoneSideBestCommanderModelProp zoneSideBestCommanderModelProp = owner.owner().getZoneSideProp().getActivityModel().getBcModel();
        long lastUpdateTsMs = zoneSideBestCommanderModelProp.getLastUpdateTsMs();
        if (lastUpdateTsMs != 0 && lastUpdateTsMs >= TimeUnit.SECONDS.toMillis(owner.getProp().getStartTsSec())) {
            // 防重入
            return;
        }
        final CommonEnum.ZoneSeason oldSeason = zoneSideBestCommanderModelProp.getCurSeason();
        final CommonEnum.ZoneSeasonStage oldStage = zoneSideBestCommanderModelProp.getCurStage();
        final CommonEnum.CommanderActivtyStage oldActStage = SeasonActivityHelper.getActStage(oldSeason, oldStage);
        final CommonEnum.ZoneSeason curSeason = owner.owner().getZoneSeason();
        final CommonEnum.ZoneSeasonStage curStage = owner.owner().getZoneSeasonStage();
        final CommonEnum.CommanderActivtyStage curActStage = SeasonActivityHelper.getActStage(curSeason, curStage);
        final int oldVolume = zoneSideBestCommanderModelProp.getVolumeCurSeason();
        int newVolume = oldVolume + 1;
        if (oldActStage != curActStage) {
            newVolume = 1;
        }
        updateVolme(curSeason, curStage, newVolume);
        zoneSideBestCommanderModelProp.setLastUpdateTsMs(SystemClock.now());
        LOGGER.info("ZoneBestCommanderUnit init {} {} {} -> {} {} {} activityId={}", oldSeason, oldStage, oldVolume, curSeason, curStage, newVolume, activityId);
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            init();
        }
    }

    /**
     * 更新期数
     */
    private void updateVolme(CommonEnum.ZoneSeason newZoneSeason, CommonEnum.ZoneSeasonStage newStage, int newVolume) {
        final ZoneSideBestCommanderModelProp zoneSideBestCommanderModelProp = owner.owner().getZoneSideProp().getActivityModel().getBcModel();
        final ZoneSideBestCommanderUnitProp prop = owner.getProp().getBestCommanderUnit();
        // 设置历史期数，只可用于下一期活动时初始化期数
        zoneSideBestCommanderModelProp.setCurSeason(newZoneSeason).setCurStage(newStage).setVolumeCurSeason(newVolume);
        // 本次活动期数，本次活动内对期数有依赖，从这里获取
        prop.setCurSeason(newZoneSeason).setCurStage(newStage).setVolumeCurSeason(newVolume);
        LOGGER.info("ZoneBestCommanderUnit updateVolme season: {} stage: {} volume: {} activityId={}", newZoneSeason, newStage, newVolume, activityId);
    }

    public CommonEnum.CommanderActivtyStage getActStage() {
        final CommonEnum.ZoneSeason curSeason = owner.getProp().getBestCommanderUnit().getCurSeason();
        final CommonEnum.ZoneSeasonStage curStage = owner.getProp().getBestCommanderUnit().getCurStage();
        return SeasonActivityHelper.getActStage(curSeason, curStage);
    }

    public int getVolume() {
        return owner.getProp().getBestCommanderUnit().getVolumeCurSeason();
    }

    public BestCommanderSeasonTemplate getSeasonConfig() {
        final int actId = owner.getActivityId();
        final CommonEnum.CommanderActivtyStage actStage = getActStage();
        final int volume = getVolume();
        final BestCommanderSeasonTemplate template = ResHolder.getResService(BestCommanderActResService.class).getSeasonConfig(actId, actStage, volume);
        if (template == null) {
            throw new GeminiException(PARAM_PARAMETER_EXCEPTION, actStage + "-" + volume + "this volume without config");
        }
        return template;
    }

    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }

    public void addRankSettleData(int activityId, Map<Integer, CommonMsg.MemberAllDto> memberAllDtoMap) {
        LOGGER.info("ZoneBestCommanderUnit addRankSettleData {} settle act {}", owner.getActivityId(), activityId);
        ZoneSideBestCommanderUnitProp unitProp = owner.getProp().getBestCommanderUnit();
        ZoneSideBestCommanderRankSettleDataProp exist = unitProp.getSubRankSettleDataV(activityId);
        if (exist != null) {
            throw new GeminiException("BestCommander addRankSettleData childSettleData exist! {} {}", activityId, memberAllDtoMap);
        }
        ZoneSideBestCommanderRankSettleDataProp settleData = unitProp.addEmptySubRankSettleData(activityId).setActId(activityId);
        memberAllDtoMap.forEach((rank, dto) ->
                settleData.addEmptyRankMap(dto.getMemberId())
                        .setPlayerId(dto.getMemberId())
                        .setRank(rank));
    }

    public SsSceneActivitySchedule.BestCommanderFetchAns handleFetch(SsSceneActivitySchedule.BestCommanderFetchAsk
                                                                             ask) {
        SsSceneActivitySchedule.BestCommanderFetchAns.Builder ans = SsSceneActivitySchedule.BestCommanderFetchAns.newBuilder();
        final long playerId = ask.getPlayerId();
        ZoneSideBestCommanderUnitProp prop = owner.getProp().getBestCommanderUnit();
        for (Integer subRankActId : getSeasonConfig().getSubRankActIdList()) {
            ZoneSideBestCommanderRankSettleDataProp subRankSettleData = prop.getSubRankSettleDataV(subRankActId);
            if (subRankSettleData != null) {
                ZoneSideBestCommanderRankSettleItemProp settleItem = subRankSettleData.getRankMapV(playerId);
                ans.addFetchItems(SsSceneActivitySchedule.BestCommanderFetchItem.newBuilder()
                        .setActId(subRankActId)
                        .setRank(settleItem == null ? -1 : settleItem.getRank())
                        .build());
            }
        }
        final int totalRankActId = getSeasonConfig().getTotalRankActId();
        if (totalRankActId > 0) {
            ZoneSideBestCommanderRankSettleDataProp subRankSettleData = prop.getSubRankSettleDataV(totalRankActId);
            if (subRankSettleData != null) {
                ZoneSideBestCommanderRankSettleItemProp settleItem = subRankSettleData.getRankMapV(playerId);
                ans.addFetchItems(SsSceneActivitySchedule.BestCommanderFetchItem.newBuilder()
                        .setActId(totalRankActId)
                        .setRank(settleItem == null ? -1 : settleItem.getRank())
                        .build());
            }
        }
        return ans.build();
    }
}

package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.enums.DirectionEnum;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.yorha.proto.CommonEnum.AiParams.*;
import static com.yorha.proto.CommonEnum.AiRecordType.ART_LAST_PATROL_TIME;

/**
 * 范围巡逻且警戒
 * 进入条件： 无
 * 进入：停止位移
 * 执行：仇恨列表为空时警戒，巡逻
 * 结束：无
 *
 * <AUTHOR>
 */
public class PatrolRangeAction extends AbstractAIAction {

    public PatrolRangeAction() {
        super();
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
        SceneObjEntity owner = component.getOwner();
        if (component.getOwner().getHateListComponent().getMostHateEntity() <= 0L) {
            component.alert(component.getAiParams().get(AP_ALERT_RANGE));
        }

        if (owner.getMoveComponent().isMoving()) {
            return;
        }
        long now = SystemClock.now();
        Object record = component.tryGetRecord(ART_LAST_PATROL_TIME);
        if (record != null) {
            long lastPatrolTime = (long) record;
            if (now < lastPatrolTime) {
                return;
            }
        }
        if (!owner.getScene().fireAiPatrol()) {
            return;
        }
        int distance = RandomUtils.randomBetween(component.getAiParams().get(AP_MOVE_MIN_DISTANCE), component.getAiParams().get(AP_MOVE_MAX_DISTANCE));
        if (distance <= 0) {
            owner.getMoveComponent().stopMove();
            return;
        }
        moveToNextPatrolPoint(component, distance);
        int configInterval = component.getAiParams().get(AP_PATROL_INTERVAL);
        int interval = RandomUtils.randomBetween(configInterval / 2, configInterval);
        component.addRecord(ART_LAST_PATROL_TIME, now + interval);
        if (component.isDebugAble()) {
            LOGGER.info("{}, fire and execute :{} distance:{} ", component.getLogHead(), getActionName(), distance);
        }
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        component.getOwner().getMoveComponent().stopMove();
        component.addRecord(ART_LAST_PATROL_TIME, SystemClock.now());
    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        super.onEnd(component);
        component.getOwner().getMoveComponent().stopMove();
        component.removeRecord(ART_LAST_PATROL_TIME);
    }

    private void moveToNextPatrolPoint(SceneObjAiComponent component, int distance) {
        SceneObjEntity owner = component.getOwner();
        Point nextPoint = getNextPoint(component, distance);

        if (nextPoint == null) {
            owner.getMoveComponent().stopMove();
            return;
        }

        Point bornPoint = owner.getTransformComponent().getBornPoint();
        if (Point.calDisBetweenTwoPoint(bornPoint, nextPoint) > component.getAiParams().get(AP_PATROL_RANGE)) {
            nextPoint = bornPoint;
        }
        if (Objects.equals(owner.getCurPoint(), nextPoint)) {
            return;
        }
        try {
            owner.getMoveComponent().moveToPointAsyncIgnoreException(nextPoint, null);
        } catch (Exception e) {
            //WechatLog.error("monster move error : {} src:{} end:{}", e, owner.getCurPoint(), nextPoint);
        }
    }

    private Point getNextPoint(SceneObjAiComponent component, int distance) {
        SceneObjEntity owner = component.getOwner();
        PathFindMgrComponent pathFindMgrComponent = owner.getScene().getPathFindMgrComponent();
        Point curPoint = owner.getCurPoint();
        List<DirectionEnum> dirList = Arrays.asList(DirectionEnum.values());
        Collections.shuffle(dirList);
        for (DirectionEnum direction : dirList) {
            int x = curPoint.getX() + direction.getAddX() * distance;
            int y = curPoint.getY() + direction.getAddY() * distance;
            Point point = Point.valueOf(x, y);
            if (!pathFindMgrComponent.isPointDynamicWalkable(point) || !pathFindMgrComponent.isPointStaticWalkable(point)) {
                continue;
            }
            return point;
        }

        // 最小范围移动
        for (DirectionEnum direction : dirList) {
            int x = curPoint.getX() + direction.getAddX() * component.getAiParams().get(AP_MOVE_MIN_DISTANCE);
            int y = curPoint.getY() + direction.getAddY() * component.getAiParams().get(AP_MOVE_MAX_DISTANCE);
            Point point = Point.valueOf(x, y);
            if (!pathFindMgrComponent.isPointDynamicWalkable(point) || !pathFindMgrComponent.isPointStaticWalkable(point)) {
                continue;
            }
            return point;
        }

        return null;
    }


    @Override
    protected String getActionName() {
        return "PatrolRangeAction";
    }
}

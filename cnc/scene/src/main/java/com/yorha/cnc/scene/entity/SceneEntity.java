package com.yorha.cnc.scene.entity;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneBornMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneBuildingMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneQlogComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.*;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.eventdispatcher.EventDispatcher;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapConfigTemplate;
import res.template.WorldObjectTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public abstract class SceneEntity extends AbstractEntity {
    private static final Logger LOGGER = LogManager.getLogger(SceneEntity.class);
    private final SceneActor actor;
    private final AoiMgrComponent aoiMgrComponent;
    private final BattleGroundComponent battleGroundComponent;
    private final ScenePlayerMgrComponent scenePlayerMgrComponent;
    private final PathFindMgrComponent pathFindMgrComponent;
    private final CityMoveComponent cityMoveComponent;
    private final ObjMgrComponent objMgrComponent;
    private final ClanMgrComponent clanMgrComponent;
    private final PeaceShieldComponent peaceShieldComponent;
    private final HateComponent hateComponent;
    private final TickMgrComponent tickMgrComponent;
    private final SceneTimerComponent timerComponent;
    private final MarqueeComponent marqueeComponent;
    /**
     * 此时业务逻辑是否处于onTick中
     */
    protected boolean isInTick = false;
    private int mapWidth = 0;
    private int mapHeight = 0;
    private boolean isInTaskQueue = false;
    // 全图ai同时发起行为的限制-同时最多发起500个野怪的巡逻
    private int aiPatrolLimit = GameLogicConstants.AI_PATROL_LIMIT_PERSCENE;

    @SuppressWarnings("unchecked")
    public SceneEntity(SceneActor sceneActor, long entityId, SceneBuilder builder) {
        super(entityId);
        this.actor = sceneActor;
        this.tickMgrComponent = builder.tickMgrComponent(this);
        this.aoiMgrComponent = builder.aoiMgrComponent(this);
        this.battleGroundComponent = builder.battleGroundComponent(this);
        this.scenePlayerMgrComponent = builder.playerMgrComponent(this);
        this.pathFindMgrComponent = builder.pathFindMgrComponent(this);
        this.cityMoveComponent = builder.cityMoveComponent(this);
        this.objMgrComponent = builder.objMgrComponent(this);
        this.clanMgrComponent = builder.clanMgrComponent(this);
        this.peaceShieldComponent = builder.peaceShieldComponent(this);
        this.hateComponent = builder.hateComponent(this);
        this.timerComponent = builder.timerComponent(this);
        this.marqueeComponent = builder.marqueeComponent(this);
    }

    /**
     * @return 地图数据id
     */
    public abstract int getMapId();

    /**
     * @return 坐标跳转用地图类型
     */
    public abstract CommonEnum.MapType getMapType();

    /**
     * @return 坐标跳转用地图id
     */
    public abstract long getMapIdForPoint();

    public int getStoryId() {
        return 0;
    }

    public MapTemplateDataItem getMapTemplateDataItem() {
        return ResHolder.getResService(MapSubdivisionDataService.class).getMapTemplateDataItem(getMapId());
    }

    public int getRegionNum() {
        return BigSceneConstants.MAP_REGION_NUM.get(getMapId());
    }

    public MapConfigTemplate getMapConfig() {
        return ResHolder.getResService(SceneMapDataTemplateService.class).getMapConfig(getMapId());
    }

    /**
     * @return 副本id
     */
    public int getDungeonId() {
        return 0;
    }

    /**
     * @return 厘米单位
     */
    public int getMapWidth() {
        if (mapWidth == 0) {
            mapWidth = UnitConvertUtils.meterToCm(getMapConfig().getWidth());
        }
        return mapWidth;
    }

    /**
     * @return 厘米单位
     */
    public int getMapHeight() {
        if (mapHeight == 0) {
            mapHeight = UnitConvertUtils.meterToCm(getMapConfig().getLength());
        }
        return mapHeight;
    }

    public void startTick() {
        long initialDelay = 1000;
        long period = 1000;
        // 使用fix能更稳定60秒60次，不然影响例如gvg等每秒加分的场景
//        getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.SCENE_TICK, this::onTick, initialDelay, period, TimeUnit.MILLISECONDS);
        getTimerComponent().addFixRepeatTimer(getEntityId(), TimerReasonType.SCENE_TICK, this::onTick, initialDelay, period, TimeUnit.MILLISECONDS);
    }

    public void onTick() {
        this.isInTick = true;
        aiPatrolLimit = GameLogicConstants.AI_PATROL_LIMIT_PERSCENE;
        getTickMgrComponent().onTick(new GeminiStopWatch("start_onTick"));
        getBattleGroundComponent().onTick();
        this.isInTick = false;
        afterTick();
    }

    protected void afterTick() {
        // 一次tick结束调用
        getTickMgrComponent().afterTick();
    }

    public void setIsInTaskQueue() {
        this.isInTaskQueue = true;
    }

    public boolean getIsInTaskQueue() {
        return isInTaskQueue;
    }

    public void cancelIsInTaskQueue() {
        this.isInTaskQueue = false;
    }

    public boolean isInTick() {
        return isInTick;
    }

    public boolean isBigScene() {
        return getEntityType() == EntityType.ET_BigScene;
    }

    public boolean isDungeon() {
        return getEntityType() == EntityType.ET_Dungeon;
    }

    public long getOpenTsMs() {
        throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
    }

    public boolean isMainScene() {
        return isBigScene();
    }

    public boolean needSendBattleMail() {
        return false;
    }

    /**
     * 是否开启玩家预警
     */
    public boolean isOpenPlayerWarning() {
        return isMainScene();
    }

    /**
     * 是否关闭 有些东西不运转了
     */
    public boolean isClose() {
        return isDestroy();
    }

    public void onServerStop() {
        stopTick();
        // 集结解散
        try {
            getClanMgrComponent().onServerStop();
        } catch (Exception e) {
            LOGGER.error("onServerStop stop rally failed ", e);
        }
        // 战斗停止
        getBattleGroundComponent().getBattleGround().forEachCopyRelationByStopServer("onServerStop", relation -> relation.forceEndRelation(CommonEnum.BattleOverType.BOT_FORCE_END));
        // 部队回家
        int num = 0;
        List<ArmyEntity> armyList = getObjMgrComponent().getObjsByType(ArmyEntity.class);
        for (ArmyEntity army : armyList) {
            // 援助采集状态不用处理
            CommonEnum.AttachState attachState = army.getProp().getAttachState();
            if (attachState == CommonEnum.AttachState.AAS_COLLECT || attachState == CommonEnum.AttachState.AAS_ASSIST) {
                continue;
            }
            try {
                // 非援助采集状态， 直接回城
                army.getMoveComponent().onReturnCityEnd();
                num++;
            } catch (Exception e) {
                LOGGER.error("onServerStop return army failed {} ", army, e);
            }
        }
        LOGGER.info("gemini_system shutdown army return end {}/{}", num, armyList.size());
        // 侦察机回家
        num = 0;
        List<SpyPlaneEntity> spyPlaneEntityList = getObjMgrComponent().getObjsByType(SpyPlaneEntity.class);
        for (SpyPlaneEntity spyPlaneEntity : spyPlaneEntityList) {
            try {
                // 直接回城
                spyPlaneEntity.getSpyPlaneBehaviourComponent().onReturnCityEnd("server stop");
                num++;
            } catch (Exception e) {
                LOGGER.error("onServerStop return SpyPlaneEntity failed {} ", spyPlaneEntity, e);
            }
        }
        LOGGER.info("gemini_system shutdown spy plane return end {}/{}", num, spyPlaneEntityList.size());
    }

    /**
     * 获取所在场景层级的最高层
     */
    public int getMaxSceneLayer(SceneObjEntity sceneObjEntity) {
        CommonEnum.SceneObjectEnum sceneObjType = sceneObjEntity.getSceneObjType();
        if (sceneObjType == null) {
            return 1;
        }
        return ResHolder.getInstance().getValueFromMap(WorldObjectTemplate.class, sceneObjType.getNumber()).getLayer();
    }

    @Override
    public void deleteObj() {
        super.deleteObj();
        stopTick();
        LOGGER.info("{} delete", this);
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        getPathFindMgrComponent().destroyNav();
    }

    @Override
    protected void onPostInitFailed() {
        getTimerComponent().onDestroy();
    }

    public void stopTick() {
        getTimerComponent().cancelTimer(TimerReasonType.SCENE_TICK);
        LOGGER.info("{} cancel tick", this);
    }

    // ------------------------- 组件获取 -------------------------

    public AoiMgrComponent getAoiMgrComponent() {
        return aoiMgrComponent;
    }

    public BattleGroundComponent getBattleGroundComponent() {
        return battleGroundComponent;
    }

    public ObjMgrComponent getObjMgrComponent() {
        return objMgrComponent;
    }

    public ScenePlayerMgrComponent getPlayerMgrComponent() {
        return scenePlayerMgrComponent;
    }

    public PathFindMgrComponent getPathFindMgrComponent() {
        return pathFindMgrComponent;
    }

    public CityMoveComponent getCityMoveComponent() {
        return cityMoveComponent;
    }

    public ClanMgrComponent getClanMgrComponent() {
        return clanMgrComponent;
    }

    public PeaceShieldComponent getPeaceShieldComponent() {
        return peaceShieldComponent;
    }

    public HateComponent getHateComponent() {
        return hateComponent;
    }

    public TickMgrComponent getTickMgrComponent() {
        return tickMgrComponent;
    }

    public RallyMgrComponent getRallyMgrComponent() {
        return null;
    }

    public MainSceneBuildingMgrComponent getBuildingMgrComponent() {
        return null;
    }

    public MainSceneBornMgrComponent getBornMgrComponent() {
        return null;
    }

    public ResMgrComponent getResMgrComponent() {
        return null;
    }

    public SceneTimerComponent getTimerComponent() {
        return timerComponent;
    }

    public MarqueeComponent getMarqueeComponent() {
        return marqueeComponent;
    }

    public MainSceneQlogComponent getQlogComponent() {
        return null;
    }

    public MileStoneMgrComponent getMileStoneOrNullComponent() {
        return null;
    }

    public EventDispatcher getDispatcher() {
        return null;
    }

    @Override
    public SceneActor ownerActor() {
        return actor;
    }

    public int getZoneId() {
        return this.actor.getZoneId();
    }

    /**
     * 便捷获取大世界，不是大世界抛异常中断
     */
    public BigSceneEntity getBigScene() {
        if (isBigScene()) {
            return (BigSceneEntity) this;
        } else {
            throw new GeminiException(ErrorCode.SCENE_NOT_EXIT);
        }
    }

    public long now() {
        return SystemClock.now();
    }

    public boolean fireAiPatrol() {
        aiPatrolLimit--;
        return aiPatrolLimit >= 0;
    }
}


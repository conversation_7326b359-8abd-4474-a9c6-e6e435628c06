package com.yorha.cnc.scene.sceneObj.ai.trigger;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.impl.*;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.AiStateTriggerTemplate;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.constant.LogConstant.LOG_TYPE_AI;

/**
 * <AUTHOR>
 */
public abstract class AiTrigger {
    protected static final Logger LOGGER = LogManager.getLogger(LOG_TYPE_AI);
    /**
     * 触发器id
     */
    private final int triggerId;
    /**
     * 触发器执行次数
     */
    private int executeCount;
    /**
     * 下次触发时间
     */
    private long nextTriggerTime;

    public AiTrigger(AiStateTriggerTemplate template) {
        triggerId = template.getId();
        this.parse(template.getEffectParamsPairList());
    }

    public AiStateTriggerTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(AiStateTriggerTemplate.class, triggerId);
    }

    protected abstract void parse(List<IntPairType> param);

    public final void trigger(SceneObjEntity owner) {
        LOGGER.debug("{}, trigger :{} {}", getLogHead(owner), getTriggerName(), getTriggerParam());

        // 触发上限
        AiStateTriggerTemplate template = getTemplate();
        if (template.getCount() != 0 && executeCount >= template.getCount()) {
            return;
        }

        // 触发参数校验
        switch (template.getTriggerType()) {
            case MTT_ENTER:
            case MTT_DEAD:
            case MTT_CAST_FINISH: {
                break;
            }
            case MTT_TAG: {
                if (!tagTriggerCheck(owner)) {
                    return;
                }
                break;
            }
            case MTT_REDUCE_SOLDIER: {
                if (!reduceSoldierCheck(owner)) {
                    return;
                }
                break;
            }
            case MTT_RANGE_TRIGGER: {
                if (!rangeTriggerCheck(owner)) {
                    return;
                }
                break;
            }
            default:{
                throw new GeminiException("unknown trigger :{} ", template.getTriggerType());
            }
        }

        // 执行时间间隔
        if (template.getPeriod() != 0) {
            if (nextTriggerTime > SystemClock.now()) {
                return;
            }
            if (nextTriggerTime == 0) {
                this.nextTriggerTime = SystemClock.now();
            }
        }

        // 满足触发效果执行条件
        if (isEffectSatisfied(owner)) {
            SceneObjAiComponent ai = owner.getAiComponent();
            if ((ai != null) && (ai.isDebugAble())) {
                LOGGER.info("{}, doTrigger :{} {}", getLogHead(owner), getTriggerName(), getTriggerParam());
            }
            doTrigger(owner);
            this.executeCount++;
            if (template.getPeriod() != 0) {
                this.nextTriggerTime += TimeUnit.SECONDS.toMillis(template.getPeriod());
            }
        }

    }

    private boolean tagTriggerCheck(SceneObjEntity owner) {
        SceneObjAiComponent ai = owner.getAiComponent();
        if (ai == null) {
            return false;
        }
        List<IntPairType> params = getTemplate().getTriggerParamsPairList();
        for (IntPairType param : params) {
            boolean target = (param.getValue() > 0);
            if (target != ai.hasTag(param.getKey())) {
                return false;
            }

        }
        return true;
    }

    /**
     * 减员触发器参数校验
     */
    private boolean reduceSoldierCheck(SceneObjEntity owner) {
        for (IntPairType pair : getTemplate().getTriggerParamsPairList()) {
            // 我方血量触发
            if (pair.getKey() == 1) {
                int max = owner.getBattleComponent().getMax();
                int total = owner.getBattleComponent().aliveCount();
                if ((total * 100f / max) > pair.getValue()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 范围触发参数校验
     */
    private boolean rangeTriggerCheck(SceneObjEntity owner) {
        SceneEntity scene = owner.getScene();
        if (scene == null || scene.isDestroy()) {
            return false;
        }
        for (IntPairType pair : getTemplate().getTriggerParamsPairList()) {
            // 范围触发，范围内有可战斗对象
            if (pair.getKey() == 1) {
                Circle range = owner.getTransformComponent().getRange(pair.getValue());

                Set<SceneObjEntity> entityList = scene.getAoiMgrComponent().getAffectedAoiSceneObjList(range);
                if (CollectionUtils.isEmpty(entityList)) {
                    return false;
                }
                for (SceneObjEntity objEntity : entityList) {
                    if (objEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) {
                        continue;
                    }
                    if (objEntity.isDestroy()) {
                        continue;
                    }
                    if (!ShapeUtils.isContact(range, objEntity.getCurPoint())) {
                        continue;
                    }
                    if (!owner.canBattle(objEntity, true)) {
                        continue;
                    }
                    break;
                }
            }
        }
        return true;
    }
    protected abstract boolean isEffectSatisfied(SceneObjEntity owner);

    protected abstract void doTrigger(SceneObjEntity owner);

    protected abstract String getTriggerParam();

    protected  abstract String getTriggerName();
    public static AiTrigger createTrigger(AiStateTriggerTemplate template) {
        switch (template.getTriggerEffect()) {
            case MTE_FIRE_SKILL:
            {
                return new FireSkillTrigger(template);
            }
            case MTE_INVOKE_MONSTER:
            {
                return new InvokeMonsterTrigger(template);
            }
            case MTE_ADD_TAG:
            {
                return new AddTagTrigger(template);
            }
            case MTE_FIXPOINT_MOVE:
            {
                return new FpMoveTrigger(template);
            }
            case MTE_RECYCLE_MONSTER:
            {
                return new RecycleMonsterTrigger(template);
            }
            case MTE_AREA_SKILL:
            {
                return new CreateAreaSkillTrigger(template);
            }
            case MTE_FIRE_SKILL_T0_MASTER:
            {
                return new FireSkillToMasterTrigger(template);
            }
            default:{
                throw new GeminiException("unknown trigger type:{}", template.getTriggerEffect());
            }
        }
    }

    public String getLogHead(SceneObjEntity owner) {
        return "AiComponent entity: " + owner.getEntityId() + " entityType: " + owner.getEntityType() + " ";
    }
}

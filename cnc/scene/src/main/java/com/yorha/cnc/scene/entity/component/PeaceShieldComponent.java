package com.yorha.cnc.scene.entity.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.constant.DevBuffConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class PeaceShieldComponent extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(PeaceShieldComponent.class);
    private List<CityEntity> cityEntityList;
    private long durationSec;
    private final int count = 500;

    public PeaceShieldComponent(SceneEntity owner) {
        super(owner);
    }

    /**
     * 全服开启和平护盾
     *
     * @param durationSec 护盾持续时间
     */
    public void openGlobalPeaceShield(long durationSec) {
        LOGGER.info("openGlobalPeaceShield sec:{}", durationSec);
        this.durationSec = durationSec;
        this.cityEntityList = new LinkedList<>(getOwner().getPlayerMgrComponent().getAllUnAscendCityEntity());
        //取消上一个
        cancelTask();
        final SceneTimerComponent timerComponent = getOwner().getTimerComponent();
        final TimerReasonType timerReasonType = TimerReasonType.OPEN_GLOBAL_PEACE_SHIELD_TASK;
        timerComponent.addRepeatTimer(getEntityId(), timerReasonType, this::dispatchOpenGlobalPeaceShield, 1, 1, TimeUnit.SECONDS);
    }

    /**
     * 单个玩家开启和平护盾
     *
     * @param durationSec 护盾持续时间
     */
    public void openSinglePeaceShield(AbstractScenePlayerEntity abstractScenePlayerEntity, long durationSec) {
        long endTime = SystemClock.now() + TimeUtils.second2Ms(durationSec);

        // 原有的护盾时间更长，保留时间长的护盾
        if (abstractScenePlayerEntity.getMainCity().getPeaceShieldComponent().getPeaceShieldEndTime() > endTime) {
            return;
        }

        // 把原来的护盾删了
        abstractScenePlayerEntity.getDevBuffComponent().removePeaceShield();
        // 删除战争狂热
        abstractScenePlayerEntity.getDevBuffComponent().removeDevBuffByEffectType(CommonEnum.BuffEffectType.ET_WAR_FRENZY);

        // 加新护盾
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        abstractScenePlayerEntity.getDevBuffComponent().addDevBuff(constTemplate.getServerMaintenanceBuff(), endTime, CommonEnum.DevBuffSourceType.DBST_SYS);
    }

    /**
     * 单个玩家关闭和平护盾
     */
    public void closeSinglePeaceShield(AbstractScenePlayerEntity abstractScenePlayerEntity, int buffId) {
        CityEntity mainCity = abstractScenePlayerEntity.getMainCity();
        if (mainCity != null) {
            for (DevBuffProp prop : abstractScenePlayerEntity.getDevBuffComponent().getDevBuffByEffectType(CommonEnum.BuffEffectType.ET_PEACE_SHIELD)) {
                if (buffId <= 0 || prop.getDevBuffId() == buffId) {
                    if (abstractScenePlayerEntity.getDevBuffComponent().removeDevBuff(prop.getDevBuffId()) != null) {
                        LOGGER.info("player:{} peace shield off id:{} reason:{}", abstractScenePlayerEntity, prop.getDevBuffId(), DevBuffConstants.PeaceShieldReason.SYS);
                    }
                }
            }
        }
    }

    @Override
    public void onDestroy() {
        cancelTask();
    }

    private void cancelTask() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.OPEN_GLOBAL_PEACE_SHIELD_TASK);
    }

    private void dispatchOpenGlobalPeaceShield() {
        if (cityEntityList == null || cityEntityList.size() == 0) {
            cancelTask();
            return;
        }

        int tempCount = 0;
        Iterator<CityEntity> it = cityEntityList.iterator();
        while (it.hasNext()) {
            CityEntity cityEntity = it.next();
            it.remove();
            if (cityEntity == null || cityEntity.isDestroy()) {
                continue;
            }
            try {
                openSinglePeaceShield(cityEntity.getScenePlayer(), durationSec);
            } catch (Exception e) {
                LOGGER.error("PeaceShieldComponent dispatchOpenGlobalPeaceShield fail {} ", cityEntity, e);
            }

            tempCount++;
            if (tempCount >= this.count) {
                return;
            }
        }
    }
}

package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.proto.CommonEnum;


/**
 * 向master移动
 * 进入条件： master存在
 * 进入：向master移动
 * 执行：无
 * 结束：无
 * <AUTHOR>
 */
public class MoveToMasterAction extends AbstractAIAction {


    public MoveToMasterAction() {
        super();
    }

    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        Object record = component.tryGetRecord(CommonEnum.AiRecordType.ART_MASTER);
        if (record == null) {
            return false;
        }
        long masterId = (long) record;
        SceneObjEntity master = component.getOwner().getScene().getObjMgrComponent().getSceneObjEntity(masterId);
        if (master == null) {
            return false;
        }
        return true;
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
    }

    @Override
    protected String getActionName() {
        return "MoveToMasterAction";
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        long masterId = (long)component.tryGetRecord(CommonEnum.AiRecordType.ART_MASTER);
        SceneObjEntity master = component.getOwner().getScene().getObjMgrComponent().getSceneObjEntity(masterId);
        component.getOwner().getMoveComponent().moveToTargetAsync(master,
                null,
                () -> {
                    component.triggerEvent(AIEvent.ARRIVE);
                },
                null,
                null);
    }
}

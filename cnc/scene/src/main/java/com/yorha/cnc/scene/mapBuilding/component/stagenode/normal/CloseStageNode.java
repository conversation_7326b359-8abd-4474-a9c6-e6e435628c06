package com.yorha.cnc.scene.mapBuilding.component.stagenode.normal;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.OccupyState;

/**
 * 关闭状态
 *
 * <AUTHOR>
 */
public class CloseStageNode extends StageNode {

    public CloseStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_CLOSE;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() == 0) {
            return;
        }
        onEnter(0);
    }

    @Override
    public void onEnter(long enterTs) {
        if (getProp().getStateEndTsMs() == 0) {
            return;
        }
        // close肯定是在初始化就设置好的  不用再设置prop了
        if (getProp().getStateEndTsMs() <= SystemClock.now()) {
            enterNextState();
            return;
        }
        // 如果是黑暗祭坛关卡 由外部驱动
        if (getOwner().getTransformComponent().isDarkAltarCross()) {
            return;
        }
        addStageTimer(this::enterNextState);
    }

    private void enterNextState() {
        getComponent().clearStageTimer();
        // 如果是有半开放状态的关隘  那是进入半开放状态
        if (getOwner().getTransformComponent().getBindRegionId() != -1) {
            getProp().setStateEndTsMs(0);
            getComponent().transNewNode(new SemiOpenStageNode(getOwner()));
            return;
        }
        getComponent().transNeutralStage();
    }

    @Override
    public String toString() {
        return ClassNameCacheUtils.getSimpleName(getClass());
    }
}

package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.city.component.CityBattleComponent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.GuardTowerHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.game.gen.prop.WallInfoProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstBattleTemplate;
import res.template.ConstTemplate;
import res.template.TroopTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 城墙模块
 *
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerWallComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerWallComponent.class);
    private boolean isInBattle = false;

    public AbstractScenePlayerWallComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        getProp().setBurnSpeed(ResHolder.getResService(ConstKVResService.class).getTemplate().getCityWallHpReduceSpeed());
    }


    /**
     * city从db恢复时
     */
    public void onRestore() {
        CityBattleComponent battleComponent = getMainCity().getBattleComponent();
        long now = SystemClock.now();
        // 关服前是冒烟状态
        if (battleComponent.getWallState() == CommonEnum.CityWallState.CS_SMOKE) {
            long restTime = getProp().getStateEndTsMs() - now;
            if (restTime <= 0) {
                battleComponent.setWallState(CommonEnum.CityWallState.CS_NORMAL);
            } else {
                addStateTask(restTime, TimeUnit.MILLISECONDS);
            }
        }
        // 关服前是燃烧状态
        if (battleComponent.getWallState() == CommonEnum.CityWallState.CS_BURNING) {
            if (getProp().getStateEndTsMs() <= now) {
                // 已经结束了  结算下 回归正常
                settleBurningHpDec(getProp().getStateEndTsMs());
                battleComponent.setWallState(CommonEnum.CityWallState.CS_NORMAL);
            } else {
                addBurnEndTimer();
            }
        }
    }

    /**
     * 设置驻防英雄和战机
     */
    public void setGarrison(Struct.Hero mainHero, Struct.Hero deputyHero) {
        WallInfoProp prop = getProp();

        prop.getMainHero().mergeFromSs(mainHero);
        prop.getDeputyHero().mergeFromSs(deputyHero);

        // 通知城池战斗更换
        getMainCity().getBattleComponent().onGarrisonChange();
    }

    /**
     * 城墙维修
     */
    public void repairWall() {
        boolean isBurning = getMainCity().getBattleComponent().getWallState() == CommonEnum.CityWallState.CS_BURNING;
        if (isBurning) {
            settleBurningHpDec(0);
        }
        if (getProp().getHp() == getProp().getHpMax()) {
            return;
        }
        if (getProp().getCanRepairTsMs() > SystemClock.now()) {
            return;
        }
        ConstTemplate template = ResHolder.getResService(ConstKVResService.class).getTemplate();
        int newHp = Math.min(getProp().getHp() + template.getCityWallRepairHp(), getProp().getHpMax());
        LOGGER.info("{} repair wall oldHp:{} newHp:{}", getOwner(), getProp().getHp(), newHp);
        getProp().setHp(newHp).setCanRepairTsMs(SystemClock.now() + TimeUtils.second2Ms(template.getCityWallRepairCd()));
        if (isBurning) {
            addBurnEndTimer();
        }
    }

    /**
     * 城墙灭火
     */
    public void outFireWall() {
        CityBattleComponent battleComponent = getMainCity().getBattleComponent();
        if (battleComponent.getWallState() != CommonEnum.CityWallState.CS_BURNING) {
            return;
        }
        settleBurningHpDec(0);
        battleComponent.setWallState(CommonEnum.CityWallState.CS_NORMAL);
        LOGGER.info("{} outFireWall", getOwner());
    }

    /**
     * 获取当前城墙耐久
     */
    public int getWallHp() {
        if (getMainCity().getBattleComponent().getWallState() != CommonEnum.CityWallState.CS_BURNING) {
            return getProp().getHp();
        }
        settleBurningHpDec(0);
        addBurnEndTimer();
        return getProp().getHp();
    }

    /**
     * 同步防御塔id
     */
    public void onSyncWallTowerId(int towerId) {
        try {
            settleDefenseTowerHp();
        } catch (Exception e) {
            LOGGER.error("{} settleDefenseTowerHp error", getOwner(), e);
        }
        // 更新防御塔数据  id hp hpMax
        getProp().setTowerId(towerId);
        int towerTroopId = GuardTowerHelper.getGuardTowerTroopId(getDefenseTowerId());
        if (towerTroopId > 0) {
            int durability = ResHolder.getInstance().getValueFromMap(TroopTemplate.class, towerTroopId).getSoldierPairList().get(0).getValue();
            int curHp = durability;
            if (getProp().getTowerHp() != getProp().getTowerHpMax() && getProp().getTowerHpMax() != 0) {
                curHp = (int) (getProp().getTowerHp() * 1.0 / getProp().getTowerHpMax() * durability);
            }
            getProp().setTowerHpMax(durability).setTowerHp(curHp);
        }
    }

    /**
     * 获取防御塔当前耐久
     */
    public int getDefenseTowerCurHp() {
        try {
            settleDefenseTowerHp();
        } catch (Exception e) {
            LOGGER.error("{} settleDefenseTowerHp error", getOwner(), e);
        }
        return getProp().getTowerHp();
    }

    /**
     * 结算防御塔自动恢复的耐久
     */
    protected void settleDefenseTowerHp() {
        if (getProp().getTowerHp() == getProp().getTowerHpMax()) {
            getProp().setTowerHpCalTsMs(SystemClock.now());
            return;
        }
        // 战斗中不回复
        if (isInBattle) {
            getProp().setTowerHpCalTsMs(SystemClock.now());
            return;
        }
        long deltaMs = TimeUtils.getDeltaMs(getProp().getTowerHpCalTsMs());
        float ratio = ResHolder.getInstance().getConstTemplate(ConstBattleTemplate.class).getDefenseTowerHpRecover();
        int addHp = (int) (TimeUnit.MILLISECONDS.toMinutes(deltaMs) * ratio * getProp().getTowerHpMax());
        int curHp = Math.min(getProp().getTowerHpMax(), getProp().getTowerHp() + addHp);
        getProp().setTowerHpCalTsMs(SystemClock.now()).setTowerHp(curHp);
    }

    /**
     * 扣除防御塔耐久
     */
    public void decDefenseTowerHp(int decHp) {
        int newHp = getProp().getTowerHp() - decHp;
        if (newHp < 0) {
            getProp().setTowerHp(0);
            LOGGER.error("{} decDefenseTowerHp error. need dec:{}  after dec:{}", getOwner(), decHp, newHp);
            return;
        }
        getProp().setTowerHp(newHp);
    }

    /**
     * 获取防御塔id
     */
    public int getDefenseTowerId() {
        return getProp().getTowerId();
    }

    /**
     * 获取防御塔最大耐久
     */
    public int getDefenseTowerMaxHp() {
        return getProp().getTowerHpMax();
    }

    /**
     * city从正常进入战斗状态时
     */
    public void onCityEnterBattle() {
        isInBattle = true;
        settleDefenseTowerHp();
        // 设置城墙状态为冒烟 如果已经在燃烧中，不管了
        if (getMainCity().getBattleComponent().getWallState() == CommonEnum.CityWallState.CS_BURNING) {
            return;
        }
        getMainCity().getBattleComponent().setWallState(CommonEnum.CityWallState.CS_SMOKE);
        LOGGER.info("{} enter smoke state by battle", getOwner());
    }

    /**
     * city战斗结束  刷新下状态
     */
    public void onCityBattleEnd(EndAllBattleEvent e) {
        LOGGER.info("{} on city battle end. isAlive: {}", getOwner(), e.isAlive());
        // 重新设置下防御塔开始恢复的时间戳
        isInBattle = false;
        getProp().setTowerHpCalTsMs(SystemClock.now());
        // 如果是赢了
        if (e.isAlive()) {
            // 如果当前处于燃烧状态 不用改成冒烟状态
            if (getMainCity().getBattleComponent().getWallState() == CommonEnum.CityWallState.CS_BURNING) {
                return;
            }
            int citySmokeTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getCityWallSmokeDuration();
            getMainCity().getBattleComponent().setWallState(CommonEnum.CityWallState.CS_SMOKE);
            getProp().setStateEndTsMs(SystemClock.now() + TimeUtils.second2Ms(citySmokeTime));
            LOGGER.info("{} enter smoke state. endTsMs: {}", getOwner(), getProp().getStateEndTsMs());
            // n秒后冒烟结束
            addStateTask(citySmokeTime, TimeUnit.SECONDS);
            return;
        }
        // 战斗失败
        // 如果当前处于燃烧状态 先结算下扣除的hp吧
        if (getMainCity().getBattleComponent().getWallState() == CommonEnum.CityWallState.CS_BURNING) {
            if (settleBurningHpDec(0)) {
                return;
            }
        }
        // 更新下燃烧结束时间
        int cityBurningDuration = this.getCityBurningDuration();
        getMainCity().getBattleComponent().setWallState(CommonEnum.CityWallState.CS_BURNING);
        getProp().setStateEndTsMs(SystemClock.now() + TimeUtils.second2Ms(cityBurningDuration)).setBurningCalTsMs(SystemClock.now());
        LOGGER.info("{} enter burning state. endTsMs: {}", getOwner(), getProp().getStateEndTsMs());
        // 重新刷下定时器
        addBurnEndTimer();
    }

    /**
     * 主堡燃烧时间(秒)
     *
     * @return
     */
    abstract protected int getCityBurningDuration();

    /**
     * 结算燃烧的hp 返回是否烧完了
     */
    protected boolean settleBurningHpDec(long endTsMs) {
        cancelStateTask();
        if (endTsMs == 0) {
            endTsMs = SystemClock.now();
        }
        long decHp = getBurnSpeed() * TimeUtils.ms2Second(endTsMs - getProp().getBurningCalTsMs()) / 60;
        int restHp = (int) (getProp().getHp() - decHp);
        LOGGER.info("{} settleBurningHpDec decHp: {} restHp:{}", getOwner(), decHp, restHp);
        if (restHp <= 0) {
            onHpBurnOut();
            return true;
        } else {
            getProp().setHp(restHp).setBurningCalTsMs(endTsMs);
            return false;
        }
    }

    /**
     * 增加燃烧结束的timer  烧完了/自然结束
     */
    protected void addBurnEndTimer() {
        int burnOutTimeSec = getProp().getHp() * 60 / getBurnSpeed();
        // 先烧完了了
        long burnOutEndTsMs = TimeUtils.second2Ms(burnOutTimeSec) + getProp().getBurningCalTsMs();
        LOGGER.info("{} cal burn end status. burnOutTsMs: {} endTsMs: {}", getOwner(), burnOutEndTsMs, getProp().getStateEndTsMs());
        if (burnOutEndTsMs <= getProp().getStateEndTsMs()) {
            cancelStateTask();
            long restTime = burnOutEndTsMs - SystemClock.now();
            if (restTime <= 0) {
                onHpBurnOut();
                return;
            }
            getOwner().getTimerComponent().addTimer(TimerReasonType.CITY_BURN_STATE,
                    this::onHpBurnOut,
                    restTime, TimeUnit.MILLISECONDS);
            return;
        }
        // 自然结束
        addStateTask(getProp().getStateEndTsMs() - SystemClock.now(), TimeUnit.MILLISECONDS);
    }

    /**
     * 城墙烧完了耐久归零
     */
    protected void onHpBurnOut() {
        LOGGER.info("{} onHpBurnOut", getOwner());
        // 升天回满
        getProp().setHp(getProp().getHpMax());
        CityEntity mainCity = getMainCity();
        mainCity.getBattleComponent().setWallState(CommonEnum.CityWallState.CS_NORMAL);
        mainCity.getTransformComponent().cityAscend(CommonEnum.CityAscendReason.CAR_WALL_DESTROY);
    }

    protected void cancelStateTask() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.CITY_BURN_STATE);
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.CITY_STATE_TASK);
    }

    protected void addStateTask(long delay, TimeUnit unit) {
        cancelStateTask();
        getOwner().getTimerComponent().addTimer(TimerReasonType.CITY_STATE_TASK,
                this::onTimerToNormal,
                delay, unit);
    }

    protected void onTimerToNormal() {
        CityBattleComponent battleComponent = getMainCity().getBattleComponent();
        if (battleComponent.getWallState() == CommonEnum.CityWallState.CS_BURNING) {
            settleBurningHpDec(0);
        }
        LOGGER.info("{} onEnterNormal old:{}", getOwner(), battleComponent.getWallState());
        battleComponent.setWallState(CommonEnum.CityWallState.CS_NORMAL);
    }

    /**
     * 获取燃烧速度 可能后续会受加成影响 所以抽个接口
     */
    protected int getBurnSpeed() {
        return getProp().getBurnSpeed();
    }

    abstract protected WallInfoProp getProp();

    protected CityEntity getMainCity() {
        return getOwner().getMainCity();
    }


    public int getWallHpMax() {
        return getProp().getHpMax();
    }

    public Struct.Hero getWallMainHero() {
        HeroProp mainHero = getProp().getMainHero();
        if (mainHero.getHeroId() <= 0) {
            return null;
        }
        return mainHero.getCopySsBuilder().build();
    }

    public Struct.Hero getWallDeputyHero() {
        HeroProp deputyHero = getProp().getDeputyHero();
        if (deputyHero.getHeroId() <= 0) {
            return null;
        }
        return deputyHero.getCopySsBuilder().build();
    }

    /**
     * 变更战斗单位数据
     */
    public void spreadBattleUnitChangeToWall(List<Struct.Hero> heroList) {
        if (heroList != null && heroList.size() > 0) {
            for (Struct.Hero hero : heroList) {
                if (getProp().getMainHero().getHeroId() == hero.getHeroId()) {
                    getProp().getMainHero().mergeChangeFromSs(hero);
                }
                if (getProp().getDeputyHero().getHeroId() == hero.getHeroId()) {
                    getProp().getDeputyHero().mergeChangeFromSs(hero);
                }
            }
        }
    }

    public void recoverWallGm() {
        outFireWall();
        getProp().setHp(getProp().getHpMax());
    }

    public void recoverTower() {
        getProp().setTowerHpCalTsMs(SystemClock.now()).setTowerHp(getProp().getTowerHpMax());
    }
    
}

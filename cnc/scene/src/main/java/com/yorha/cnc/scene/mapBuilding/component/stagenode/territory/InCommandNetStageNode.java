package com.yorha.cnc.scene.mapBuilding.component.stagenode.territory;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class InCommandNetStageNode extends StageNode {
    private static final Logger LOGGER = LogManager.getLogger(InCommandNetStageNode.class);

    public InCommandNetStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_CITY_IN_COMMAND_NET;
    }

    @Override
    public void onLoad() {

    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        getProp().setState(getStage()).setStateStartTsMs(ts);
    }
}


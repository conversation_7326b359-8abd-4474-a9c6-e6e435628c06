package com.yorha.cnc.zone.activity;

import com.yorha.cnc.scene.event.activity.IActivityEvent;
import com.yorha.cnc.zone.IZone;
import com.yorha.cnc.zone.component.ZoneSideActivityComponent;
import com.yorha.common.activity.ActivityPropBase;
import com.yorha.common.activity.BaseActivity;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.game.gen.prop.ZoneActivityProp;
import com.yorha.proto.CommonEnum;
import res.template.ActivityTemplate;

import javax.annotation.Nullable;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.function.Consumer;

import static com.yorha.proto.CommonEnum.ActivityUnitType;

/**
 * 一个活动在zone上的逻辑实体(可能是个子活动哦)
 */
public class ZoneActivity extends BaseActivity<ZoneActivity, BaseZoneActivityUnit> {

    private final IZone owner;
    private final ZoneActivityProp prop;
    private final ActivityPropBase.ZoneActivityPropWrapper propDelegate;

    public ZoneActivity(IZone owner, ZoneActivity father, ZoneActivityProp prop, ActivityTemplate activityTemplate) {
        super(father, prop.getActScheduleId(), activityTemplate);
        this.owner = owner;
        this.prop = prop;
        this.propDelegate = new ActivityPropBase.ZoneActivityPropWrapper(prop);
    }

    public static void initProp(ZoneActivityProp prop, ActivityTemplate activityTemplate, int actScheduleId, Instant startTime) {
        prop.setActivityId(activityTemplate.getId())
                .setActScheduleId(actScheduleId)
                .setStartTsSec((int) startTime.getEpochSecond())
                .setStatus(CommonEnum.ActivityStatus.ACS_NONE)
                .setEndTsSec((int) startTime.plus(activityTemplate.getLastingHours(), ChronoUnit.HOURS).getEpochSecond())
                .setStatusEnterTsSec((int) startTime.getEpochSecond());
    }

    @Override
    protected ActivityPropBase baseProp() {
        return this.propDelegate;
    }

    @Override
    protected IZone owner() {
        return owner;
    }

    @Override
    protected void tryAddReloadTimer(Instant reloadTime) {
        owner().getSideActivityComponent().tryAddNextReloadTimer(reloadTime);
    }

    @Override
    protected void loadUnits(ActivityTemplate activityTemplate, boolean isInitial) {
        List<ActivityUnitType> zoneUnitTypes = ZoneSideActivityComponent.zoneUnitTypes(activityTemplate);
        for (ActivityUnitType zoneUnitType : zoneUnitTypes) {
            ZoneSideActivityComponent.ZoneUnitConstructor unitConstructor = ZoneSideActivityComponent.getUnityFactory().get(zoneUnitType);
            BaseZoneActivityUnit unit = unitConstructor.create(this, activityTemplate);
            unit.load(isInitial);
            registerUnit(unit.zoneUnitId, unit);
        }
    }

    @Override
    protected void reloadUnits() {

    }

    @Nullable
    @Override
    protected ZoneActivity tryLoadChildAct(ActivityTemplate childTemplate, Instant childStartTime, Instant childExpireTime) throws Exception {
        ZoneActivityProp childProp = getOrCreateChildProp(childTemplate.getId(), childTemplate, childStartTime);
        if (childProp.getStatus() == CommonEnum.ActivityStatus.ACS_EXPIRED) {
            return null;
        }
        return owner.getSideActivityComponent().loadActivity(this, childProp, childTemplate);
    }

    private ZoneActivityProp getOrCreateChildProp(int childActivityId, ActivityTemplate childTemplate, Instant childStartTime) {
        ZoneActivityProp childActProp = prop.getChildActivitiesV(childActivityId);
        if (childActProp == null) {
            childActProp = prop.addEmptyChildActivities(childActivityId);
            initProp(childActProp, childTemplate, prop.getActScheduleId(), childStartTime);
        }
        return childActProp;
    }

    @Override
    protected void expireImpl() {
        owner().getSideActivityComponent().expireActivity(this);
    }

    @Override
    protected boolean allFinished() {
        return false;
    }

    @Override
    public void forceOffImpl() {
        owner().getSideActivityComponent().forceRemoveActivity(this.activityId);
    }

    public BaseGameActor ownerActor() {
        return owner().ownerActor();
    }

    public ZoneActivityProp getProp() {
        return prop;
    }

    public void dispatch(IActivityEvent event) {
        owner.getSideActivityComponent().dispatch(event);
    }

    public EventListener addEventListener(Consumer<IActivityEvent> eventHandler, Class<IActivityEvent> clz) {
        return owner.getSideActivityComponent().addEventListener(eventHandler, clz);
    }

    public EventListener addEventListenerRepeat(Consumer<IActivityEvent> eventHandler, Class<IActivityEvent> clz) {
        return owner.getSideActivityComponent().addEventListenerRepeat(eventHandler, clz);
    }

    public void cancelEventListener(EventListener eventListener) {
        owner.getSideActivityComponent().cancelEventListener(eventListener);
    }
}

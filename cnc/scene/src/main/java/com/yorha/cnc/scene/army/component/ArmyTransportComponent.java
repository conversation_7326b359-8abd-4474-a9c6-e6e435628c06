package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.ArmyDetailState;
import com.yorha.proto.CommonEnum.ArmyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * <p>
 * 运输机运输流程组件
 */
public class ArmyTransportComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyTransportComponent.class);

    public ArmyTransportComponent(ArmyEntity owner) {
        super(owner);
    }

    /**
     * 开始运输 进入起飞状态
     */
    public void startTransport(Point point, long targetId) {
        getOwner().getProp().setArmyState(ArmyState.AS_LiftOff).setEnterStateTs(SystemClock.now());
        int liftOffTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getLandingTime();
        long moveStartTsMs = SystemClock.now() + TimeUtils.second2Ms(liftOffTime);
        getOwner().getMoveComponent().moveToPointAsync(point, () -> onTransportArrive(false), moveStartTsMs, null);
        // 增加起飞结束定时器
        getOwner().getTimerComponent().addTimer(TimerReasonType.ARMY_LIFT_OFF,
                () -> enterTransport(point, targetId),
                liftOffTime, TimeUnit.SECONDS);

        getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_MOVE_TRANSPORT, null);
        LOGGER.debug("{} enter LiftOff. point: {}, targetId: {}", getOwner(), point, targetId);
    }


    /**
     * 起飞结束 进入运输移动状态
     */
    private void enterTransport(Point point, long targetId) {
        LOGGER.info("{} enter LiftOff. point: {}, targetId: {}", getOwner(), point, targetId);
        // 解除起飞timer引用
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.ARMY_LIFT_OFF);
        getOwner().getProp().setArmyState(ArmyState.AS_AirTransport).setEnterStateTs(SystemClock.now());
    }

    /**
     * 处理空运中返回请求   主动遣返
     */
    public void transportReturn() {
        ArmyState armyState = getOwner().getProp().getArmyState();
        // 取消可能存在的定时器
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.ARMY_LIFT_OFF);
        if (armyState == ArmyState.AS_LiftOff) {
            // 起飞状态 直接降落
            long now = SystemClock.now();
            // 已经起飞时间
            long liftoffTime = now - getOwner().getProp().getEnterStateTs();
            // 起飞所需总时间
            long totalTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getLandingTime() * 1000L;
            // 往前推移
            long start = now - (totalTime - (liftoffTime));
            getOwner().getProp().setArmyState(ArmyState.AS_Airborne).setEnterStateTs(Math.min(start, now));
            // 降落结束回调
            getOwner().getTimerComponent().addTimer(TimerReasonType.ARMY_AIR_BORNE,
                    () -> onTransportBorneEnd(true),
                    liftoffTime, TimeUnit.MILLISECONDS);

        } else if (armyState == ArmyState.AS_AirTransport) {
            // 飞行状态
            getOwner().getProp().setArmyState(ArmyState.AS_TransportReturn);
            getOwner().getMoveComponent().returnMainCity();
        }
    }


    /**
     * 空运到达目的地/遣返回城
     */
    public void onTransportArrive(boolean isReturnCity) {
        // 空运到达 开始降落
        getOwner().getProp().setArmyState(ArmyState.AS_Airborne).setEnterStateTs(SystemClock.now());
        int airborneTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getLandingTime();
        getOwner().getTimerComponent().addTimer(TimerReasonType.ARMY_AIR_ARRIVE,
                () -> onTransportBorneEnd(isReturnCity),
                airborneTime, TimeUnit.SECONDS);
    }

    /**
     * 降落结束  到达目的地/回城
     */
    private void onTransportBorneEnd(boolean isReturnCity) {
        LOGGER.info("{} onTransportBorneEnd. isReturnCity: {}", getOwner(), isReturnCity);
        if (isReturnCity) {
            // 运输机和战斗机在飞机组件中一起释放
            getOwner().getMoveComponent().onReturnCityEnd();
        } else {
            // 返航运输机
            // 解除行军与运输机绑定，运输机单独释放
            getOwner().getProp().getTransportPlane().setId(0).setPlaneModel(0).setPlaneSpeed(0);
            getOwner().getProp().setArmyState(ArmyState.AS_Staying).setEnterStateTs(SystemClock.now());
            getOwner().getStatusComponent().setStaying();
        }
    }
}

package com.yorha.cnc.scene.sceneObj.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.assist.AssistArmyTreatEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyAddEvent;
import com.yorha.cnc.scene.event.assist.InnerArmyDelEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.event.battle.TreatSoldierEvent;
import com.yorha.cnc.scene.event.city.CityMoveEvent;
import com.yorha.cnc.scene.event.mapbuilding.ChangeAssistLeaderEvent;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CityInnerArmyProp;
import com.yorha.game.gen.prop.InnerArmyInfoProp;
import com.yorha.proto.CommonEnum.ArmyState;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.RallyArmyRoleType;
import com.yorha.proto.CommonEnum.WarningType;
import com.yorha.proto.Core.Code;
import com.yorha.proto.StructPlayerPB;
import com.yorha.proto.StructPlayerPB.CityAssistInfoPB;
import com.yorha.proto.StructPlayerPB.Int64RallyArmyInfoMapPB;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 城内增员管理器
 */
public abstract class SceneObjInnerArmyComponent extends SceneObjComponent<SceneObjEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjInnerArmyComponent.class);
    /**
     * 还在路上的army 未到达   armyId->士兵占坑数
     */
    protected final Map<Long, Integer> unArrivedArmy = Maps.newHashMap();
    /**
     * 未到达的士兵总数
     */
    protected long unArrivedSoldierNum = 0;
    /**
     * 已到达的士兵总数
     */
    protected long arrivedSoldierNum = 0;
    /**
     * 记录当前加的预警
     */
    private final Map<Long, Pair<WarningType, EventListener>> addWaningId = new Long2ObjectOpenHashMap<>();

    public SceneObjInnerArmyComponent(SceneObjEntity owner) {
        super(owner);
    }

    /**
     * @return 城内部队最大容量
     */
    public abstract int getBeAidedMaxNum();

    /**
     * 城内军队prop
     */
    public abstract CityInnerArmyProp getProp();

    @Override
    public void init() {
        // 战斗回合结算  重新计算兵力  check死亡状态
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSettleRoundEvent, BattleRoleSettleRoundEvent.class);
        // 治疗事件发生时
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onAssistArmyTreatEvent, AssistArmyTreatEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onTreatSoldierEvent, TreatSoldierEvent.class);
    }

    @Override
    public void afterAllLoad() {
        if (getProp().getArmy().isEmpty()) {
            return;
        }
        List<Long> deleteArmy = new ArrayList<>();
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (Long armyId : getProp().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army == null) {
                LOGGER.error("{} army: {} is not exist", getOwner(), armyId);
                deleteArmy.add(armyId);
                continue;
            }
            arrivedSoldierNum += army.getAliveSoldierNum();
        }
        for (Long armyId : deleteArmy) {
            getProp().removeArmyV(armyId);
        }
        LOGGER.info("{} arrivedSoldierNum {}", getOwner(), arrivedSoldierNum);
    }

    /**
     * 退回所有army
     */
    public void returnAllArmy() {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        // 清理未到达的
        for (long armyId : unArrivedArmy.keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                army.getAssistComponent().leaveAssist(null, false, true);
                // 移除援助预警
                removeWarningItem(army.getEntityId());
            }
        }
        List<Long> armyIdList = new ArrayList<>();
        // 清理已经到达的
        for (long armyId : getProp().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                army.getAssistComponent().leaveAssist(null, false, true);
                armyIdList.add(armyId);
                removeWarningItemWhenLeave(army.getPlayerId());
                // 战斗过程中 如果被returnAll 不会抛出InnerArmyDelEvent事件 但是需要改状态
                army.getStatusComponent().setDetailBattle(false);

            }
        }
        getProp().getArmy().clear();
        changeLeader(0);
        arrivedSoldierNum = 0;
        unArrivedSoldierNum = 0;
        unArrivedArmy.clear();
        // 战斗数据处理
        if (getOwner().getBattleComponent() == null) {
            return;
        }
        getOwner().getBattleComponent().afterReturnAllInnerArmy(armyIdList);
    }

    /**
     * 自身位置发生改变 如迁城  城内军队需要同步改变位置
     */
    protected void onSelfPointChange(CityMoveEvent event) {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (long armyId : getProp().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                army.getTransformComponent().changePoint(getOwner().getCurPoint().getDeepCopy());
            }
        }
    }

    /**
     * 更换队长
     */
    protected void changeLeader(long armyId) {
        long oldLeader = getProp().getLeaderArmyId();
        getProp().setLeaderArmyId(armyId);
        if (oldLeader != armyId) {
            getOwner().getEventDispatcher().dispatch(new ChangeAssistLeaderEvent());
        }
    }

    /**
     * 获取队长army
     */
    public ArmyEntity getLeaderArmy() {
        if (getProp().getLeaderArmyId() == 0) {
            return null;
        }
        return getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, getProp().getLeaderArmyId());
    }

    public long getLeaderArmyId() {
        return getProp().getLeaderArmyId();
    }

    /**
     * 判断是否能援助
     */
    public Code checkArmyCanAssist(long playerId, Camp camp, long soldierNum, long armyId) {
        if (getBeAidedMaxNum() == 0) {
            return ErrorCode.ASSIST_NOT_HAVE_BUILD.getCode();
        }
        if (getBeAidedMaxNum() < getCurAssistSoldierNum() + soldierNum) {
            return ErrorCode.ASSIST_NUM_LIMIT.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    public Code checkArmyCanCollect(long playerId) {
        return ErrorCode.OK.getCode();
    }

    public long getCurAssistSoldierNum() {
        return arrivedSoldierNum + unArrivedSoldierNum;
    }

    public long getArrivedSoldierNum() {
        return arrivedSoldierNum;
    }

    /**
     * 军队发起加入
     */
    public void armyJoin(ArmyEntity armyEntity) {
        long armyId = armyEntity.getEntityId();
        int soldierNum = armyEntity.getAliveSoldierNum();
        unArrivedArmy.put(armyId, soldierNum);
        unArrivedSoldierNum += soldierNum;
        LOGGER.info("{} army: {} try join assist add={} total={} all={}", getOwner(), armyId, soldierNum, unArrivedSoldierNum, getCurAssistSoldierNum());
        // 加预警
        addWarningItem(armyEntity, WarningType.WT_Assist);
    }

    /**
     * 援助预警  要给城内到的人都发一遍
     */
    public boolean addWarningItem(SceneObjEntity obj, WarningType type) {
        if (addWaningId.containsKey(obj.getEntityId())) {
            return false;
        }
        EventListener eventListener = null;
        if (type != WarningType.WT_Assist) {
            eventListener = obj.getEventDispatcher().addEventListener((e) -> {
                        if (!addWaningId.containsKey(e.getEntityId())) {
                            return;
                        }
                        // 先移除 否则会重复cancel事件
                        addWaningId.put(e.getEntityId(), Pair.of(addWaningId.get(e.getEntityId()).getFirst(), null));
                        removeWarningItem(e.getEntityId());
                    }
                    , WarningRemoveEvent.class);
        }
        addWaningId.put(obj.getEntityId(), Pair.of(type, eventListener));
        return true;
    }

    /**
     * 移除援助预警  要给城内到的人都移除一遍
     */
    public boolean removeWarningItem(long objId) {
        Pair<WarningType, EventListener> remove = addWaningId.remove(objId);
        if (remove == null) {
            return false;
        }
        // 取消下事件
        if (remove.getSecond() != null) {
            remove.getSecond().cancel();
        }
        return true;
    }

    /**
     * 部队新到达 那就给那个人加所有预警
     */
    protected void addWarningItemWhenArrive(long playerId) {
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (scenePlayer == null) {
            return;
        }
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        try {
            for (Map.Entry<Long, Pair<WarningType, EventListener>> entry : addWaningId.entrySet()) {
                SceneObjEntity sceneObjEntity = objMgrComponent.getSceneObjEntity(entry.getKey());
                if (sceneObjEntity == null) {
                    continue;
                }
                final AbstractScenePlayerWarningComponent playerWarningComponent = scenePlayer.getWarningComponent();
                if (playerWarningComponent == null) {
                    LOGGER.info("SceneObjInnerArmyComponent addWarningItemWhenArrive no warningComponent");
                    continue;
                }
                playerWarningComponent.addWarningItem(sceneObjEntity, entry.getValue().getFirst());
            }
        } catch (Exception e) {
            LOGGER.error("addWarningItemWhenArrive error ", e);
        }
    }

    /**
     * 部队离开 那就给那个人移除所有预警
     */
    protected void removeWarningItemWhenLeave(long playerId) {
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (scenePlayer == null) {
            return;
        }

        final AbstractScenePlayerWarningComponent playerWarningComponent = scenePlayer.getWarningComponent();
        if (playerWarningComponent == null) {
            LOGGER.info("SceneObjInnerArmyComponent removeWarningItemWhenLeave no warningComponent");
            return;
        }
        try {
            playerWarningComponent.removeWarningItem(addWaningId.keySet());
        } catch (Exception e) {
            LOGGER.error("removeWarningItemWhenLeave error ", e);
        }
    }

    /**
     * 清理预警
     */
    public void clearWarningItem() {
        if (addWaningId.isEmpty()) {
            return;
        }
        for (Long objId : new HashSet<>(addWaningId.keySet())) {
            removeWarningItem(objId);
        }
    }

    /**
     * 军队行进到达
     */
    public void armyArrived(ArmyEntity armyEntity) {
        long armyId = armyEntity.getEntityId();
        if (unArrivedArmy.containsKey(armyId)) {
            Integer soldierNum = unArrivedArmy.remove(armyId);
            unArrivedSoldierNum -= soldierNum;
            // 让之前已经到达的人 移除这个预警
            removeWarningItem(armyId);
        }
        int aliveSoldierNum = armyEntity.getAliveSoldierNum();
        getProp().addEmptyArmy(armyId).setEnterTsMs(SystemClock.now()).setPlayerId(armyEntity.getPlayerId());
        arrivedSoldierNum += aliveSoldierNum;
        armyEntity.getTransformComponent().changePoint(getOwner().getCurPoint().getDeepCopy());
        LOGGER.info("{} army: {} arrived assist add={} total={} all={}", getOwner(), armyEntity.getEntityId(), aliveSoldierNum, arrivedSoldierNum, getCurAssistSoldierNum());
        getOwner().getEventDispatcher().dispatch(new InnerArmyAddEvent(armyEntity));
    }

    /**
     * 军队主动离开援助
     * point 将要去的地方
     */
    public Point armyLeave(ArmyEntity armyEntity, Point point) {
        boolean needChangePoint = getProp().getArmy().containsKey(armyEntity.getEntityId());
        removeArmy(armyEntity.getEntityId(), armyEntity, armyEntity.getPlayerId());
        if (needChangePoint && point != null) {
            return getOwner().getTransformComponent().getLeavePosition(armyEntity, point);
        }
        return null;
    }

    /**
     * 移除军队数据
     */
    public void removeArmy(long armyId, ArmyEntity armyEntity, long operatorId) {
        boolean isIn = getProp().getArmy().containsKey(armyId);
        try {
            beforeRemoveArmy(armyEntity, isIn, operatorId);
        } catch (Exception e) {
            LOGGER.info("{} beforeRemoveArmy error ", getOwner(), e);
        }
        if (unArrivedArmy.containsKey(armyId)) {
            Integer soldierNum = unArrivedArmy.remove(armyId);
            unArrivedSoldierNum -= soldierNum;
            LOGGER.info("{} army: {} abandon assist dec={} total={}", getOwner(), armyEntity.getEntityId(), soldierNum, unArrivedSoldierNum);
            // 移除已经到达人对此的援助预警
            removeWarningItem(armyEntity.getEntityId());
        } else if (getProp().getArmy().containsKey(armyId)) {
            getProp().removeArmyV(armyId);
            int aliveSoldierNum = armyEntity.getAliveSoldierNum();
            arrivedSoldierNum -= aliveSoldierNum;
            getOwner().getEventDispatcher().dispatch(new InnerArmyDelEvent(armyEntity));
            LOGGER.info("{} army: {} leave assist dec={} total={}", getOwner(), armyEntity.getEntityId(), aliveSoldierNum, arrivedSoldierNum);
        }
        try {
            afterRemoveArmy(armyEntity, isIn, operatorId);
        } catch (Exception e) {
            LOGGER.info("{} afterRemoveArmy error ", getOwner(), e);
        }
    }

    protected void beforeRemoveArmy(ArmyEntity army, boolean isIn, long operatorId) {

    }

    protected void afterRemoveArmy(ArmyEntity army, boolean isIn, long operatorId) {

    }

    /**
     * 遣返军队
     *
     * @param playerId     操作者
     * @param armyId       被遣返者
     * @param isPermission 是否通过联盟权限效验
     */
    public void repatriateArmy(long playerId, long armyId, boolean isPermission) {
        ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
        if (army == null) {
            // 部队已经不在了
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
        }
        // 部队不在里面了
        if (!unArrivedArmy.containsKey(armyId) && !getProp().getArmy().containsKey(armyId)) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL);
        }
        checkCanRepatriateArmy(playerId, army, isPermission);
        // 自己负责后处理 因为操作id可能不是自己
        army.getAssistComponent().leaveAssist(null, false, true);
        removeArmy(armyId, army, playerId);
        LOGGER.info("{} army: {} be repatriated. playerId: {}", getOwner(), armyId, playerId);
    }

    /**
     * 遣返权限检测
     */
    public abstract void checkCanRepatriateArmy(long playerId, ArmyEntity army, boolean isPermission);

    /**
     * 构建城池援助军队列表面板数据  只有查询玩家的军队
     * 拉盟友的城
     */
    public CityAssistInfoPB getAssistInfoPb(long playerId) {
        CityAssistInfoPB.Builder builder = CityAssistInfoPB.newBuilder();
        builder.setCurSoldierNum(getCurAssistSoldierNum())
                .setMaxSoldierNum(getBeAidedMaxNum()).setLeaderArmyId(getLeaderArmyId());
        copyToRallyArmyInfo(builder.getArmyBuilder());
        return builder.build();
    }

    /**
     * copy 至战争面板看到的被集结 援助信息
     */
    public void copyToRallyInfo(StructPlayerPB.RallyInfoPB.Builder builder, boolean needDetails) {
        builder.setCurSoldierNum(getCurAssistSoldierNum())
                .setMaxSoldierNum(getBeAidedMaxNum());
        if (needDetails) {
            copyToRallyArmyInfo(builder.getRallyArmyInfoMapBuilder());
        }
    }

    /**
     * 将援助的行军数据copy至行军map数据结构
     */
    private void copyToRallyArmyInfo(Int64RallyArmyInfoMapPB.Builder builder) {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        // 已经到达的
        for (long armyId : getProp().getArmy().keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                addArmyPb(builder, army, getProp().getArmyV(armyId).getEnterTsMs());
            }
        }
        // 未到达的
        for (long armyId : unArrivedArmy.keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                addArmyPb(builder, army, 0);
            }
        }
    }

    protected void addArmyPb(Int64RallyArmyInfoMapPB.Builder builder, ArmyEntity armyEntity, long arrivedTsMs) {
        if (armyEntity.getEntityId() == getProp().getLeaderArmyId()) {
            builder.putDatas(armyEntity.getEntityId(), armyEntity.getAssistArmyInfoBuilder(RallyArmyRoleType.RART_Leader, arrivedTsMs).build());
            return;
        }
        builder.putDatas(armyEntity.getEntityId(), armyEntity.getAssistArmyInfoBuilder(RallyArmyRoleType.RART_Follower, arrivedTsMs).build());
    }

    /**
     * 获取城内的军队  战斗组建士兵使用
     */
    public List<ArmyEntity> getInnerArmyList() {
        ArrayList<ArmyEntity> result = Lists.newArrayList();
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (Long armyId : getProp().getArmy().keySet()) {
            ArmyEntity armyEntity = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (armyEntity == null) {
                LOGGER.error("getInnerArmyList {}, armyId={} not found entity", getOwner(), armyId);
                continue;
            }
            result.add(armyEntity);
        }
        return result;
    }


    @Nullable
    public ArmyEntity findInnerArmy(long armyId) {
        InnerArmyInfoProp innerArmyInfo = getProp().getArmyV(armyId);
        if (innerArmyInfo == null) {
            return null;
        }
        return getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
    }

    /**
     * 进入战斗 把城内的军队状态改一下
     */
    public void enterBattle() {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (long armyId : getProp().getArmy().keySet()) {
            ArmyEntity armyEntity = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (armyEntity != null) {
                armyEntity.changeStateByAssistCity(ArmyState.AS_InBattle);
                armyEntity.getStatusComponent().setDetailBattle(true);
            }
        }
    }

    /**
     * 结束战斗  把城内的军队状态改一下
     */
    protected void endAllBattle(EndAllBattleEvent e) {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (long armyId : getProp().getArmy().keySet()) {
            ArmyEntity armyEntity = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (armyEntity != null) {
                armyEntity.changeStateByAssistCity(ArmyState.AS_Staying);
                armyEntity.getStatusComponent().setDetailBattle(false);
            }
        }
    }

    /**
     * 战斗一回合结束  重算兵力， 检查死亡
     */
    private void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        long innerSoldierNum = 0;
        ArrayList<Long> idList = new ArrayList<>(getProp().getArmy().keySet());
        for (long armyId : idList) {
            ArmyEntity armyEntity = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (armyEntity != null) {
                if (armyEntity.getAliveSoldierNum() <= 0) {
                    try {
                        armyEntity.getAssistComponent().leaveAssist(null, true, true);
                    } catch (Exception e) {
                        LOGGER.error("{} remove army error ", getOwner(), e);
                    }
                    continue;
                }
                innerSoldierNum += armyEntity.getAliveSoldierNum();
            }
        }
        arrivedSoldierNum = innerSoldierNum;
    }

    /**
     * 参与援助的军队发生治疗行为的事件
     * 目前是专给打建筑使用  打建筑成功时，立刻进入援助，计算了当前士兵数， 然后才触发脱战回血  此时需要把这部分差量加上
     *
     * @param event 治疗行为事件，包括军队id和治疗数目
     */
    private void onAssistArmyTreatEvent(AssistArmyTreatEvent event) {
        onSettleRoundEvent(null);
        LOGGER.info("{} AssistArmyTreatEvent resetNum: {}", getOwner(), arrivedSoldierNum);
    }

    /**
     * 治疗事件发生  仅限脱战后治疗
     * 援助状态脱战回血进入
     *
     * @param event 治疗事件内容
     */
    private void onTreatSoldierEvent(TreatSoldierEvent event) {
        // 不是脱战后的不管
        if (getOwner().getBattleComponent().isInBattle()) {
            return;
        }
        onSettleRoundEvent(null);
        LOGGER.info("{} TreatSoldierEvent resetNum: {}", getOwner(), arrivedSoldierNum);
    }

    /**
     * 撤离军队是否需要增加（占领据点、联盟建设）时间
     *
     * @param armyId 撤离的军队Id
     * @return 该军队撤离是否需要增加（占领据点、联盟建设）时间
     */
    protected boolean isArmyNeedAddTime(long armyId, long startTime) {
        long playerId = getProp().getArmyV(armyId).getPlayerId();
        long thisArmyEnterTsMs = getProp().getArmyV(armyId).getEnterTsMs();
        // 判断玩家当前撤离的军队是否不是最早进入的军队
        for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
            // 跳过要撤离的军队id
            if (entry.getValue().getArmyId() == armyId) {
                continue;
            }
            // 不属于玩家自己的军队，跳过
            if (entry.getValue().getPlayerId() != playerId) {
                continue;
            }
            // 当前撤离军队不是该玩家最早进入的军队，则无需增加联盟建设时间
            if (thisArmyEnterTsMs >= entry.getValue().getEnterTsMs()) {
                return false;
            }
            // 当前撤离军队是玩家最早进入的军队，但玩家有其他军队在联盟建设开始前进入，也无需增加见联盟建设时间
            if (entry.getValue().getEnterTsMs() <= startTime) {
                return false;
            }
        }
        return true;
    }

    /**
     * 撤离某只军队时，需要增加的（占领据点、联盟建设）时间
     *
     * @param armyId 撤离的军队Id
     * @return 军队撤离时需要增加的（占领据点、联盟建设）时间
     */
    protected long getReturnArmyAddTime(long armyId, long now, long beginTsMs) {
        long playerId = getProp().getArmyV(armyId).getPlayerId();
        long thisArmyEnterTsMs = getProp().getArmyV(armyId).getEnterTsMs();
        long nextArmyEnterTsMs = Long.MAX_VALUE;
        boolean isOnlyOneArmy = true;
        // 判断玩家当前撤离的军队是否不是最早进入的军队
        for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
            // 跳过要撤离的军队id
            if (entry.getValue().getArmyId() == armyId) {
                continue;
            }
            // 不属于玩家自己的军队，跳过
            if (entry.getValue().getPlayerId() != playerId) {
                continue;
            }
            isOnlyOneArmy = false;
            long armyEnterTsMs = entry.getValue().getEnterTsMs();
            nextArmyEnterTsMs = Math.min(armyEnterTsMs, nextArmyEnterTsMs);
        }
        if (isOnlyOneArmy) {
            return TimeUtils.ms2Second((now - Math.max(thisArmyEnterTsMs, beginTsMs)));
        } else {
            return TimeUtils.ms2Second((nextArmyEnterTsMs - Math.max(thisArmyEnterTsMs, beginTsMs)));
        }
    }

    /**
     * 获取玩家id到玩家(占据据点、联盟建筑建设）时间的映射，时间为玩家所有参与(占据据点、联盟建筑建设）的军队的最大值
     *
     * @param endTsMs   结算的结束时间
     * @param beginTsMs (占据据点、联盟建筑建设）的开始时间
     * @return 玩家id到玩家(占据据点 、 联盟建筑建设 ） 时间的映射
     */
    protected Map<Long, Integer> getPlayerTimeMap(long endTsMs, long beginTsMs) {
        Map<Long, Integer> playerIdToTime = new HashMap<>();
        for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
            long playerId = entry.getValue().getPlayerId();
            int interval = (int) (TimeUtils.ms2Second(endTsMs - Math.max(beginTsMs, entry.getValue().getEnterTsMs())));
            if (playerIdToTime.containsKey(playerId)) {
                playerIdToTime.put(playerId, Math.max(interval, playerIdToTime.get(playerId)));
            } else {
                playerIdToTime.put(playerId, interval);
            }
        }
        return playerIdToTime;
    }
}

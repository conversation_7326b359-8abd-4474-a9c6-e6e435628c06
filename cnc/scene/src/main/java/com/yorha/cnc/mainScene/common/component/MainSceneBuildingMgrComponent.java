package com.yorha.cnc.mainScene.common.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.cave.CaveFactory;
import com.yorha.cnc.scene.city.CityFactory;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingFactory;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.FogBuildingTemplate;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.city.CityBornService;
import com.yorha.common.resource.resservice.map.BigMapBuildingTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MapBuildingType;
import com.yorha.proto.Core;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapBuildingTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 地缘建筑管理器
 */
public class MainSceneBuildingMgrComponent extends AbstractComponent<SceneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneBuildingMgrComponent.class);
    /**
     * partId-> MapBuildingEntity
     */
    private final Map<Integer, MapBuildingEntity> mapBuilding = Maps.newHashMap();
    /**
     * 城市的索引map   建筑templateId -> MapBuildingEntity
     */
    private final Map<Integer, List<MapBuildingEntity>> cityBuilding = Maps.newHashMap();
    /**
     * 村庄的map  partId->坐标列表
     */
    private final Map<Integer, List<Point>> townsPoint = new HashMap<>();
    /**
     * 王城
     */
    private MapBuildingEntity kingCity;

    public MainSceneBuildingMgrComponent(SceneEntity owner) {
        super(owner);
        // 先加载村庄的数据
        Map<Integer, MapBuildingTemplate> mapBuildingTemplateMap = ResHolder.getInstance().getMap(MapBuildingTemplate.class);
        for (FogBuildingTemplate template : getOwner().getMapTemplateDataItem().getMap(FogBuildingTemplate.class).values()) {
            if (mapBuildingTemplateMap.get(template.getBuildingId()).getType() != MapBuildingType.MBT_TOWNS) {
                continue;
            }
            Point p = Point.valueOf(template.getPosX(), template.getPosY());
            int partId = MapGridDataManager.getPartId(owner.getMapId(), p);
            townsPoint.computeIfAbsent(partId, (k) -> new ArrayList<>()).add(p);
        }
    }

    /**
     * 拿到王城
     */
    public MapBuildingEntity getKingCity() {
        return kingCity;
    }

    /**
     * 检查坐标点是否与村庄重合
     */
    public boolean checkTownsCollision(int partId, Point point) {
        // 村庄的
        int limit = ResHolder.getResService(CityBornService.class).getTownsCityMoveRadius() + CityFactory.getCityRadius();
        List<Point> list = townsPoint.getOrDefault(partId, Collections.emptyList());
        for (Point point1 : list) {
            if (Point.calDisBetweenTwoPoint(point1, point) < limit) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void postInit() {
        LOGGER.info("begin checkMapBuilding {}", mapBuilding.size());
        for (RegionalAreaSettingTemplate data : getOwner().getMapTemplateDataItem().getMap(RegionalAreaSettingTemplate.class).values()) {
            if (data.getBuildingId() == 0) {
                continue;
            }
            if (GameLogicConstants.isMapStrongholdOrCity(data.getAreaType())) {
                if (mapBuilding.containsKey(data.getId())) {
                    continue;
                }
                MapBuildingFactory.createMapBuilding(getOwner(), data);
                continue;
            }
        }
        Map<Integer, MapBuildingTemplate> mapBuildingTemplateMap = ResHolder.getInstance().getMap(MapBuildingTemplate.class);
        for (FogBuildingTemplate template : getOwner().getMapTemplateDataItem().getMap(FogBuildingTemplate.class).values()) {
            if (mapBuildingTemplateMap.get(template.getBuildingId()).getType() == MapBuildingType.MBT_CAVE) {
                CaveFactory.createCave(getOwner(), template.getBuildingId(), template.getPosX(), template.getPosY(), template.getId());
            }
        }
        LOGGER.info("checkMapBuilding {}", mapBuilding.size());
    }

    public List<MapBuildingEntity> getMapBuildingByType(MapBuildingType type) {
        List<MapBuildingEntity> result = new ArrayList<>();
        List<Integer> list = ResHolder.getResService(BigMapBuildingTemplateService.class).getMapBuildingIdListByType(type);
        for (Integer templateId : list) {
            result.addAll(cityBuilding.getOrDefault(templateId, new ArrayList<>()));
        }
        return result;
    }

    /**
     * 获取对应片的拥有者联盟
     */
    public long getOwnerIdByPartId(Point p) {
        int partId = MapGridDataManager.getPartId(getOwner().getMapId(), p);
        return getOwnerIdByPartId(partId);
    }

    /**
     * 获取对应片的拥有者联盟
     */
    public long getOwnerIdByPartId(int x, int y) {
        int partId = MapGridDataManager.getPartId(getOwner().getMapId(), x, y);
        return getOwnerIdByPartId(partId);
    }

    /**
     * 获取对应片的拥有者联盟
     */
    public long getOwnerIdByPartId(int partId) {
        MapBuildingEntity mapBuildingEntity = getMapBuilding(partId);
        if (mapBuildingEntity == null) {
            return 0;
        }
        return mapBuildingEntity.getOwnerClanId();
    }

    /**
     * 开放地图建筑
     *
     * @param endTsMs 0:立即开始 XXX:开始时间戳
     */
    public void openMapBuilding(MapBuildingType type, int level, long endTsMs) {
        if (type == MapBuildingType.MBT_TERRITORY_CITY) {
            for (MapBuildingEntity entity : mapBuilding.values()) {
                if (entity.getBuildingTemplate().getType() != type) {
                    continue;
                }
                entity.getStageMgrComponent().onOpen(endTsMs);
            }
            return;
        }
        List<Integer> list = ResHolder.getResService(BigMapBuildingTemplateService.class).getMapBuildingIdListByType(type);
        for (Integer templateId : list) {
            TerritoryBuildingTemplate template = ResHolder.getInstance().getMap(TerritoryBuildingTemplate.class).get(templateId);
            if (template == null) {
                continue;
            }
            if (level != 0 && level != template.getLevel()) {
                continue;
            }
            List<MapBuildingEntity> entities = cityBuilding.getOrDefault(templateId, Collections.emptyList());
            for (MapBuildingEntity entity : entities) {
                entity.getStageMgrComponent().onOpen(endTsMs);
            }
        }
    }

    public void semiOpenMapBuilding(long endTsMs) {
        List<Integer> list = ResHolder.getResService(BigMapBuildingTemplateService.class).getMapBuildingIdListByType(MapBuildingType.MBT_PASS);
        for (Integer templateId : list) {
            TerritoryBuildingTemplate template = ResHolder.getInstance().getMap(TerritoryBuildingTemplate.class).get(templateId);
            if (template == null) {
                continue;
            }
            List<MapBuildingEntity> entities = cityBuilding.getOrDefault(templateId, Collections.emptyList());
            for (MapBuildingEntity entity : entities) {
                if (entity.getTransformComponent().getBindRegionId() == -1) {
                    continue;
                }
                entity.getStageMgrComponent().onSemiOpen(endTsMs);
            }
        }
    }

    public void gmOpen() {
        for (MapBuildingEntity entity : mapBuilding.values()) {
            entity.getStageMgrComponent().onOpen(1);
        }
    }

    public Map<Integer, Core.Code> gmCheckAllBuildingCollision() {
        int collisionRadius = 0;
        Map<Integer, Core.Code> partIdToErrorCode = new HashMap<>();
        for (MapBuildingTemplate template : ResHolder.getInstance().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getType() == MapBuildingType.MBT_MAIN_BASE) {
                collisionRadius = template.getCollisionRadius();
            }
        }
        for (MapBuildingEntity entity : mapBuilding.values()) {
            if (entity.getAreaType() != CommonEnum.MapAreaType.TERRITORY) {
                continue;
            }
            Core.Code code = BornPointHelper.collisionCheck(getOwner(),
                    entity.getProp().getPoint().getX(),
                    entity.getProp().getPoint().getY(),
                    collisionRadius, entity.getEntityId());
            if (ErrorCode.isOK(code)) {
                continue;
            }
            // 动态阻挡屏蔽掉
            if (code.getId() == 10021009) {
                continue;
            }
            partIdToErrorCode.put(entity.getPartId(), code);
        }
        return partIdToErrorCode;
    }

    public void addMapBuilding(int partId, MapBuildingEntity entity) {
        mapBuilding.put(partId, entity);
        if (GameLogicConstants.isMapCity(entity.getAreaType())) {
            cityBuilding.computeIfAbsent(entity.getTemplateId(), k -> new ArrayList<>()).add(entity);
        }
        if (GameLogicConstants.isKingCity(entity.getBuildingTemplate().getType())) {
            kingCity = entity;
        }
    }
    

    public MapBuildingEntity getMapBuilding(int partId) {
        return mapBuilding.get(partId);
    }
}


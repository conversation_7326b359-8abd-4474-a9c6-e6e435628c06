package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.mainScene.common.component.MainSceneObjMgrComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.SsPlayerMisc;

/**
 * 天网野怪
 *
 * <AUTHOR>
 */
public class SkyMonster implements MonsterFeature {

    @Override
    public boolean isRecordFirstKill() {
        return false;
    }

    @Override
    public void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity) {
        if (!monsterEntity.getScene().isMainScene()) {
            return;
        }
        final int monsterTemplateId = monsterEntity.getTemplateId();
        MainSceneObjMgrComponent objMgrComponent = (MainSceneObjMgrComponent) sceneEntity.getObjMgrComponent();
        objMgrComponent.removeSkynetMonsterByMonster(monsterEntity.getEntityId());
        long summonPlayerId = monsterEntity.getProp().getSummonPlayerId();
        AbstractScenePlayerEntity scenePlayer = monsterEntity.getScene().getPlayerMgrComponent().getScenePlayerOrNull(summonPlayerId);
        if (scenePlayer == null) {
            WechatLog.error("MonsterEntity deleteObj fail, skynet summonPlayer is null, monsterId={}", monsterTemplateId);
            return;
        }
        SsPlayerMisc.MonsterDeadNtfOwnerCmd build = SsPlayerMisc.MonsterDeadNtfOwnerCmd.newBuilder()
                .setMonsterId(monsterEntity.getEntityId())
                .setMonsterTemplateId(monsterTemplateId)
                .setBeKill(monsterEntity.isDead())
                .build();
        scenePlayer.tellPlayer(build);
    }

    @Override
    public ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        if (!MonsterEntity.isSkynetBoss(monsterEntity.getTemplate())) {
            return ErrorCode.OK;
        }
        return monsterEntity.getSkynetComponent().beAttackCheck(attackerObj.getPlayerId(), attackerObj.getEntityId());
    }

    @Override
    public ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public void onDead(MonsterEntity monsterEntity) {

    }

    @Override
    public void sendKillAndRewardAll(MonsterEntity monsterEntity) {
        monsterEntity.getRewardComponent().sendBaseReward();
    }

    @Override
    public ErrorCode canBattle(MonsterEntity monsterEntity, SceneObjEntity target, boolean needCheckSiegeLimit) {
        ErrorCode errorCode = defaultCanBattle(monsterEntity, target);
        if (errorCode.isNotOk()) {
            return errorCode;
        }
        if (target.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) {
            return ErrorCode.BATTLE_CANT;
        }
        if (!MonsterEntity.isSkynetBoss(monsterEntity.getTemplate())) {
            return ErrorCode.OK;
        }
        ArmyEntity armyTarget = (ArmyEntity) target;
        return monsterEntity.getSkynetComponent().beAttackCheck(armyTarget.getScenePlayer().getPlayerId(), target.getEntityId());
    }
}

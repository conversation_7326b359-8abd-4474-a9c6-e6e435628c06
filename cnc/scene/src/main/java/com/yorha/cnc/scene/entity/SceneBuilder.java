package com.yorha.cnc.scene.entity;

import com.yorha.cnc.scene.entity.component.*;
import com.yorha.common.constant.BattleConstants;

/**
 * 场景entity创建器
 * 定义骨架和默认组件
 * 提供attr
 *
 * <AUTHOR>
 */
public class SceneBuilder<T extends SceneEntity> {

    public SceneBuilder() {
    }

    // ---------------- scene骨架 必须具有的组件列表 ----------------

    public AoiMgrComponent aoiMgrComponent(T owner) {
        return new AoiMgrComponent(owner);
    }

    public BattleGroundComponent battleGroundComponent(T owner) {
        return new BattleGroundComponent(owner, BattleConstants.BattleGroundType.BIG_SCENE, null);
    }

    public ObjMgrComponent objMgrComponent(T owner) {
        return new ObjMgrComponent(owner);
    }

    public ScenePlayerMgrComponent playerMgrComponent(T owner) {
        return new ScenePlayerMgrComponent(owner);
    }

    public PathFindMgrComponent pathFindMgrComponent(T owner) {
        return new PathFindMgrComponent(owner);
    }

    public CityMoveComponent cityMoveComponent(T owner) {
        return new CityMoveComponent(owner);
    }

    public ClanMgrComponent clanMgrComponent(T owner) {
        return new ClanMgrComponent(owner);
    }

    public PeaceShieldComponent peaceShieldComponent(T owner) {
        return new PeaceShieldComponent(owner);
    }

    public HateComponent hateComponent(T owner) {
        return new HateComponent(owner);
    }

    public SceneTimerComponent timerComponent(T owner) {
        return new SceneTimerComponent(owner);
    }

    public TickMgrComponent tickMgrComponent(T owner) {
        return new TickMgrComponent(owner);
    }

    public MarqueeComponent marqueeComponent(T owner) {
        return new MarqueeComponent(owner);
    }
    // ---------------- end
}
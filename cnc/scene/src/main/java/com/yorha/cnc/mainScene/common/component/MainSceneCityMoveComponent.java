package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.CityMoveComponent;
import com.yorha.cnc.scene.pathfinding.PathFindingHelper;
import com.yorha.cnc.scene.pathfinding.manager.NavmeshApi;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.navmesh.GeminiNav;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MainSceneCityMoveComponent extends CityMoveComponent {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneCityMoveComponent.class);

    private final Map<Integer, GeminiNav> navMap = new HashMap<>();

    public MainSceneCityMoveComponent(SceneEntity owner) {
        super(owner);
    }

    public void initNav() {
        for (int fileId = 0; fileId < 9; fileId++) {
            String navmeshFileName = ResHolder.getResService(SceneMapDataTemplateService.class).getNavmeshFileName(fileId + getOwner().getMapId());
            try {
                GeminiNav nav = new GeminiNav();
                nav.init(PathFindingHelper.genNavId(), navmeshFileName);
                navMap.put(fileId, nav);
                LOGGER.info("load from navmesh:{} success", navmeshFileName);
            } catch (Exception e) {
                WechatLog.error("load from navmesh:{} fail", navmeshFileName, e);
            }
        }

    }

    @Override
    public boolean isPointNavMovable(Point p) {
        int mapHeight = getOwner().getMapHeight();
        int mapWidth = getOwner().getMapWidth();
        int curSearchMapId = p.getX() / (mapWidth / 3) + (p.getY() / (mapHeight / 3)) * 3;
        return NavmeshApi.isPointWalkable(getPointNav(curSearchMapId), p);
    }

    private GeminiNav getPointNav(int curSearchMapId) {
        GeminiNav nav = navMap.get(curSearchMapId);
        if (nav == null) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.valueOf(curSearchMapId));
        }
        return nav;
    }

    @Override
    public void onDestroy() {
        for (GeminiNav nav : navMap.values()) {
            nav.release();
        }
        navMap.clear();
    }
}

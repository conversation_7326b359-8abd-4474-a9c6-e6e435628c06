package com.yorha.cnc.zone.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.TextFormat;
import com.yorha.common.actorservice.msg.GeminiCompletionStage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.rank.RankConstant;
import com.yorha.common.rank.RankHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.*;
import qlog.flow.QlogCncPlayerRankings;
import res.template.ActivityTemplate;
import res.template.EventRankRewardsTemplate;
import res.template.RankTemplate;

import java.util.*;

/**
 * 通用的活动排行榜unit
 * <p>
 * 冲积分、算排行、结算发奖
 *
 * <AUTHOR>
 */
public abstract class BaseZoneActivityRankUnit extends BaseZoneActivityUnit {

    protected BaseZoneActivityRankUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    @Override
    public void load(boolean isInitial) {
        openRank(isInitial);
    }

    protected abstract int getRankId(ActivityTemplate activityTemplate);

    protected abstract int getRewardMailId(ActivityTemplate activityTemplate);

    protected abstract int getRankLimit(ActivityTemplate activityTemplate);

    protected void afterMailSend(SsRank.DeleteRankAndGetTopAns ans, Map<Long, StructMsg.RankInfoDTO> ret) {
    }


    @Override
    public void onExpire() {
        settleRank();
    }

    @Override
    public void forceOffImpl() {
        LOGGER.warn("BaseZoneActivityRankUnit forceOffImpl, need handle rank data.");
    }

    /**
     * 结算邮件、奖励、清榜等
     */
    protected void settleRank() {
        final ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, activityId);
        final int rankId = getRankId(activityTemplate);

        LOGGER.info("ZoneActivityRankUnit: {} ask zone rank {}!", activityId, rankId);

        // 构建排行榜请求
        SsRank.DeleteRankAndGetTopAsk.Builder rankAsk = SsRank.DeleteRankAndGetTopAsk.newBuilder();
        rankAsk.setRankId(rankId);

        // 构建奖励映射和排名列表
        RankRewardData rewardData = buildRewardMapAndRankList(rankId);

        // 设置排行榜范围
        RankTemplate rankTemplate = ResHolder.getTemplate(RankTemplate.class, rankId);
        int maxRank = rankTemplate.getMaxRank();
        rankAsk.setRange(CommonMsg.Range.newBuilder().setRangeStart(0).setRangeEnd(maxRank).build());

        // 发送排行榜请求
        SsRank.DeleteRankAndGetTopAsk ask = rankAsk.build();
        this.<SsRank.DeleteRankAndGetTopAns>askRank(ask).onComplete((ans, err) ->
                processRankResponse(ans, err, activityTemplate, rewardData, rankTemplate, rankId));
    }

    /**
     * 构建奖励映射和排名列表
     */
    private RankRewardData buildRewardMapAndRankList(int rankId) {
        ActivityResService service = ResHolder.getResService(ActivityResService.class);
        final List<EventRankRewardsTemplate> rewardConfList = service.getRankReward(rankId);

        Map<Integer, EventRankRewardsTemplate> rewardMap = Maps.newHashMap();
        List<Integer> playerDetailRankList = new ArrayList<>();

        for (EventRankRewardsTemplate template : rewardConfList) {
            IntPairType pairType = template.getRewardRangePair();
            if (pairType.getKey() > pairType.getValue()) {
                LOGGER.error("ZoneActivityRankUnit: {} templateId: {} RewardRangePair key > value", activityId, template.getId());
                continue;
            }
            for (int i = pairType.getKey(); i <= pairType.getValue(); i++) {
                rewardMap.put(i, template);
                playerDetailRankList.add(i);
            }
        }

        return new RankRewardData(rewardMap, playerDetailRankList);
    }

    /**
     * 处理排行榜响应
     */
    private void processRankResponse(SsRank.DeleteRankAndGetTopAns ans, Throwable err,
                                     ActivityTemplate activityTemplate, RankRewardData rewardData,
                                     RankTemplate rankTemplate, int rankId) {
        if (err != null) {
            LOGGER.error("ZoneActivityRankUnit: {} ask zone rank {} fail! err: {}", activityId, rankId, err);
            return;
        }
        if (!ErrorCode.isOK(ans.getCode())) {
            WechatLog.error("BaseZoneActivityRankUnit settleRank, ask={} ans={}", ans);
            return;
        }

        // 构建排行榜消息映射和成员集合
        RankMemberData memberData = buildRankMsgMapAndMemberSet(ans, rewardData.getPlayerDetailRankList());

        // 根据排行榜类型处理查询
        if (rankTemplate.getMember() == RankConstant.MEMBER_PLAYER) {
            handlePlayerRankQuery(ans, activityTemplate, rewardData.getRewardMap(), rankTemplate,
                    rankId, memberData);
        }
        if (rankTemplate.getMember() == RankConstant.MEMBER_CLAN) {
            handleClanRankQuery(ans, activityTemplate, rewardData.getRewardMap(), rankTemplate,
                    rankId, memberData);
        }
    }

    /**
     * 构建排行榜消息映射和成员集合
     */
    private RankMemberData buildRankMsgMapAndMemberSet(SsRank.DeleteRankAndGetTopAns ans,
                                                       List<Integer> playerDetailRankList) {
        Set<Integer> detailRankList = Sets.newHashSet(playerDetailRankList);
        Map<Integer, CommonMsg.MemberAllDto> rankMsgMap = new HashMap<>();
        Set<Long> memberSet = Sets.newHashSet();

        for (Integer rank : detailRankList) {
            CommonMsg.MemberAllDto memberAllDto = ans.getRangeMemberInfoMap().get(rank);
            if (memberAllDto == null) {
                continue;
            }
            rankMsgMap.put(memberAllDto.getRank(), memberAllDto);
            memberSet.add(memberAllDto.getMemberId());
        }

        return new RankMemberData(rankMsgMap, memberSet);
    }

    /**
     * 处理玩家排行榜查询
     */
    private void handlePlayerRankQuery(SsRank.DeleteRankAndGetTopAns ans, ActivityTemplate activityTemplate,
                                       Map<Integer, EventRankRewardsTemplate> rewardMap, RankTemplate rankTemplate,
                                       int rankId, RankMemberData memberData) {
        CardHelper.batchQueryPlayerRankWithClan(ownerActor(), memberData.getMemberSet(),
                (players, clans) -> {
                    Map<Long, StructMsg.RankInfoDTO> ret = new HashMap<>();
                    for (CommonMsg.MemberAllDto member : memberData.getRankMsgMap().values()) {
                        StructPB.PlayerCardInfoPB pb = players.get(member.getMemberId());
                        if (pb != null) {
                            CommonMsg.ClanSimpleInfo simpleClan = clans.get(pb.getClanId());
                            StructMsg.RankInfoDTO rankInfoDTO = RankHelper.buildPlayerRankInfoDTO(
                                    rankId, member.getRank(), member.getScore(), member.getZoneId(), pb, simpleClan);
                            ret.put(pb.getPlayerId(), rankInfoDTO);
                        }
                    }
                    onComplete(ans, activityTemplate, rewardMap, rankTemplate, ret);
                });
    }

    /**
     * 处理公会排行榜查询
     */
    private void handleClanRankQuery(SsRank.DeleteRankAndGetTopAns ans, ActivityTemplate activityTemplate,
                                     Map<Integer, EventRankRewardsTemplate> rewardMap, RankTemplate rankTemplate,
                                     int rankId, RankMemberData memberData) {
        CardHelper.batchQueryClanSimple(ownerActor(), memberData.getMemberSet(),
                (map) -> {
                    Map<Long, StructMsg.RankInfoDTO> ret = new HashMap<>();
                    for (CommonMsg.MemberAllDto member : memberData.getRankMsgMap().values()) {
                        CommonMsg.ClanSimpleInfo simpleClan = map.get(member.getMemberId());
                        if (simpleClan != null) {
                            StructMsg.RankInfoDTO rankInfoDTO = RankHelper.buildClanRankInfoDTO(
                                    rankId, member.getRank(), member.getScore(), member.getZoneId(), simpleClan);
                            ret.put(rankInfoDTO.getPlayerId(), rankInfoDTO);
                        }
                    }
                    onComplete(ans, activityTemplate, rewardMap, rankTemplate, ret);
                });
    }

    /**
     * 奖励数据内部类
     */
    private static class RankRewardData {
        private final Map<Integer, EventRankRewardsTemplate> rewardMap;
        private final List<Integer> playerDetailRankList;

        public RankRewardData(Map<Integer, EventRankRewardsTemplate> rewardMap, List<Integer> playerDetailRankList) {
            this.rewardMap = rewardMap;
            this.playerDetailRankList = playerDetailRankList;
        }

        public Map<Integer, EventRankRewardsTemplate> getRewardMap() {
            return rewardMap;
        }

        public List<Integer> getPlayerDetailRankList() {
            return playerDetailRankList;
        }
    }

    /**
     * 排行榜成员数据内部类
     */
    private static class RankMemberData {
        private final Map<Integer, CommonMsg.MemberAllDto> rankMsgMap;
        private final Set<Long> memberSet;

        public RankMemberData(Map<Integer, CommonMsg.MemberAllDto> rankMsgMap, Set<Long> memberSet) {
            this.rankMsgMap = rankMsgMap;
            this.memberSet = memberSet;
        }

        public Map<Integer, CommonMsg.MemberAllDto> getRankMsgMap() {
            return rankMsgMap;
        }

        public Set<Long> getMemberSet() {
            return memberSet;
        }
    }

    /**
     * 名片查询完毕的后置发奖逻辑
     *
     * @param ret id -> memberDto、名片
     */
    private void onComplete(SsRank.DeleteRankAndGetTopAns ans, ActivityTemplate activityTemplate, Map<Integer, EventRankRewardsTemplate> rewardMap, RankTemplate rankTemplate, Map<Long, StructMsg.RankInfoDTO> ret) {
        final int mailId = getRewardMailId(activityTemplate);
        final CommonEnum.RankScoreType rankScoreType = rankTemplate.getRankScoreType();
        // 获取排行榜大家的内容
        Struct.ArenaMailPlayerInfoList.Builder playerList = Struct.ArenaMailPlayerInfoList.newBuilder();
        // 取一下公共头部的排行信息
        for (StructMsg.RankInfoDTO member : ret.values()) {
            if (member.getRank() > getRankLimit(activityTemplate)) {
                continue;
            }
            Struct.ArenaMailPlayerInfo.Builder playerBuilder = Struct.ArenaMailPlayerInfo.newBuilder();
            playerBuilder.setScore((int) member.getValue())
                    .setScoreType(rankScoreType)
                    .setRank(member.getRank())
                    .setSClanName(member.getClansimpleName());
            if (rankTemplate.getMember() == RankConstant.MEMBER_PLAYER) {
                playerBuilder.setName(member.getCardHead().getName());
            }
            if (rankTemplate.getMember() == RankConstant.MEMBER_CLAN) {
                playerBuilder.setName(member.getClanName());
            }
            EventRankRewardsTemplate rewardsTemplate = rewardMap.get(member.getRank());
            if (rewardsTemplate != null) {
                final int itemNum = rewardsTemplate.getOptionalRewardNum();
                final int itemId = getOptionalItemId(member.getPlayerId());
                if ((itemId <= 0) || (itemNum <= 0)) {
                    playerList.addDatas(playerBuilder);
                    continue;
                }
                playerBuilder.setOptionalItemId(itemId).setOptionalItemNum(itemNum);
            }
            playerList.addDatas(playerBuilder);
        }
        LOGGER.info("ZoneActivityRankUnit: {} get rank size: {}! top player: {}!", activityId, ret.size(), playerList);
        // 发奖励邮件
        sendRankRewardMail(rankTemplate, rewardMap, ret, mailId, playerList);

        try {
            afterMailSend(ans, ret);
        } catch (Exception e) {
            WechatLog.error("afterMailSend err:", e);
        }
    }

    private void sendRankRewardMail(final RankTemplate rankTemplate, final Map<Integer, EventRankRewardsTemplate> rewardMap, Map<Long, StructMsg.RankInfoDTO> rankMsgMap, final int mailId, final Struct.ArenaMailPlayerInfoList.Builder playerList) {
        // 给排行榜上的玩家发邮件
        for (StructMsg.RankInfoDTO member : rankMsgMap.values()) {
            EventRankRewardsTemplate rewardsTemplate = rewardMap.get(member.getRank());
            // 参与的成员id
            long memberId = rankTemplate.getMember() == RankConstant.MEMBER_PLAYER ? member.getPlayerId() : member.getClanId();
            if (rewardsTemplate == null) {
                LOGGER.error("ZoneActivityRankUnit: {} member: {} rank: {} not template", activityId, memberId, member.getRank());
                continue;
            }
            StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder();
            params.setMailTemplateId(mailId);
            // 自己的排名
            Struct.DisplayData.Builder builder = Struct.DisplayData.newBuilder();
            builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, member.getRank()));
            Struct.DisplayData displayData = builder.build();
            params.getTitleBuilder().setTitleData(displayData).setSubTitleData(displayData);
            params.getContentBuilder()
                    .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                    .setDisplayData(displayData);
            // 奖励
            List<IntPairType> rewardPairList = Lists.newArrayList(rewardsTemplate.getRewardPairList());
            int optionalItem = 0;
            if (rankTemplate.getMember() == RankConstant.MEMBER_PLAYER) {
                optionalItem = getOptionalItemId(memberId);
                if ((optionalItem > 0) && (rewardsTemplate.getOptionalRewardNum() > 0)) {
                    rewardPairList.add(IntPairType.makePair(optionalItem, rewardsTemplate.getOptionalRewardNum()));
                }
            }
            for (IntPairType rewardPair : rewardPairList) {
                Struct.ItemPair itemPair = Struct.ItemPair.newBuilder()
                        .setItemTemplateId(rewardPair.getKey())
                        .setCount(rewardPair.getValue())
                        .build();
                params.getItemRewardBuilder()
                        .addDatas(itemPair);
            }
            // 排行榜展示前几名
            Struct.ArenaMailData arenaMailData = Struct.ArenaMailData.newBuilder()
                    .setPlayerInfo(playerList)
                    .setSelfRank(member.getRank())
                    .build();
            params.getContentBuilder().setArenaMailData(arenaMailData).build();
            // 发邮件
            if (rankTemplate.getMember() == RankConstant.MEMBER_PLAYER) {
                MailUtil.sendMailToPlayer(
                        CommonMsg.MailReceiver.newBuilder()
                                .setPlayerId(memberId)
                                .setZoneId(member.getZoneId())
                                .build(),
                        params.build());
            }
            if (rankTemplate.getMember() == RankConstant.MEMBER_CLAN) {
                MailUtil.sendClanMail(member.getZoneId(), memberId, params.build());
            }
            // 排行榜上的积分如果是负数时间，则转化未整数时间
            long rankingScore = member.getValue();
            if (rankTemplate.getRankScoreType() == CommonEnum.RankScoreType.RST_NEGATIVE_TIME_MS) {
                rankingScore = Math.abs(rankingScore);
            }
            // qlog
            QlogCncPlayerRankings.init(getQlogHead())
                    .setRankingName(String.valueOf(rankTemplate.getId()))
                    .setDtEventTime(TimeUtils.now2String())
                    .setRanking(member.getRank())
                    .setIGuildId(0)
                    .setVRoleId(String.valueOf(memberId))
                    .setRankingScore(rankingScore)
                    .setOptionalReward(optionalItem)
                    .sendToQlog();
            LOGGER.info("ZoneActivityRankUnit: {} memberId:{} rank: {} send mail:{} {}", activityId, memberId, member.getRank(), mailId, TextFormat.printer().printToString(params.getItemReward()));
        }
    }

    protected void openRank(boolean isInitial) {
        try {
            openRankWithException(isInitial);
        } catch (Exception e) {
            LOGGER.error("ZoneActivityRankUnit: {} open rank fail! ", activityId, e);
        }
    }

    /**
     * 可能抛出异常
     */
    private void openRankWithException(boolean isInitial) {
        SsRank.OpenRankAsk.Builder builder = SsRank.OpenRankAsk.newBuilder();
        final ActivityTemplate activityTemplate = ResHolder.getInstance().getValueFromMap(ActivityTemplate.class, activityId);
        builder.setRankId(getRankId(activityTemplate))
                .setIsCreate(isInitial)
                .setRankEndTsMs(TimeUtils.second2Ms(this.owner.getProp().getEndTsSec()));
        LOGGER.info("BaseZoneActivityRankUnit openRank activityId={} rankId={} isInitial={}", activityId, builder.getRankId(), isInitial);
        this.<SsRank.OpenRankAns>askRank(builder.build()).onComplete(
                (res, t) -> {
                    if (t != null) {
                        LOGGER.error("ZoneActivityRankUnit: {} open rank fail! ", activityId, t);
                    }
                }
        );
    }

    protected int getOptionalItemId(long playerId) {
        return 0;
    }

    protected <RESP> GeminiCompletionStage<RESP> askRank(GeneratedMessageV3 msg) {
        return ownerActor().askZoneRank(getZoneId(), msg);
    }

    public QlogServerFlowInterface getQlogHead() {
        return owner.owner().getQlogComponent();
    }
}

package com.yorha.cnc.scene.clanResBuilding;

import com.yorha.cnc.scene.clanResBuilding.component.ClanResBuildingBuildComponent;
import com.yorha.cnc.scene.clanResBuilding.component.ClanResBuildingCollectComponent;
import com.yorha.cnc.scene.clanResBuilding.component.ClanResBuildingInnerArmyComponent;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ClanResBuildingProp;
import com.yorha.game.gen.prop.ProgressInfoProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.proto.*;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncGuildExpansion;
import res.template.ClanResourceBuildingTemplate;
import res.template.ConstClanTemplate;
import res.template.MapBuildingTemplate;

/**
 * <AUTHOR>
 */
public class ClanResBuildingEntity extends SceneObjEntity implements BuildingEntityType {
    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildingEntity.class);

    private final ClanResBuildingProp prop;

    private final ClanResBuildingCollectComponent collectComponent = new ClanResBuildingCollectComponent(this);

    private final ClanResBuildingBuildComponent buildComponent = new ClanResBuildingBuildComponent(this);

    private final ClanResBuildingInnerArmyComponent innerArmyComponent = new ClanResBuildingInnerArmyComponent(this);

    public ClanResBuildingEntity(ClanResBuildingBuilder builder, boolean isCreateNew) {
        this(builder, isCreateNew, false);
    }

    public ClanResBuildingEntity(ClanResBuildingBuilder builder, boolean isCreateNew, boolean isRestore) {
        super(builder);
        prop = builder.getProp();
        initAllComponents();
        if (isCreateNew) {
            prop.unMarkAll();
        }
        getPropComponent().initPropListener(isRestore);
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_ClanResBuilding;
    }

    @Override
    public void deleteObj() {
        getDbComponent().deleteDb();
        super.deleteObj();
        getOwnerSceneClan().getBuildComponent().removeClanResBuilding();
    }

    @Override
    public ClanResBuildingProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getClanResBuildingAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getClanResBuildingAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getClanResBuildingAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getClanResBuildingAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getClanResBuildingAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final ClanResBuildingProp clanResBuildingProp = ClanResBuildingProp.of(builder.getFullAttr().getClanResBuildingAttr(), builder.getChangedAttr().getClanResBuildingAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setClanResBuildingAttr(clanResBuildingProp.getCopyDbBuilder());
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        prop.getTarget().setTargetType(CommonEnum.ArmyTargetType.ATT_CLAN_RESBUILDING)
                .setName("")
                .setTemplateId(getProp().getTemplateId())
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }

    @Override
    public boolean briefEntityAttr(Entity.SceneObjBriefAttr.Builder builder) {
        // 简要信息记录point和templateId
        builder.getClanResAttrBuilder().getPointBuilder().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        builder.getClanResAttrBuilder().setTemplateId(getTemplateId());
        return true;
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return null;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return null;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return null;
    }

    @Override
    public CommonEnum.SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public long getPlayerId() {
        return 0;
    }

    @Override
    public long getClanId() {
        return getProp().getClanId();
    }

    @Override
    public int getZoneId() {
        return getProp().getZoneId();
    }

    public void destroy() {
        LOGGER.info("destroy clanResBuilding {}", this);
        deleteObj();
    }

    // -------------------------------------------- get component -------------------------------------------- //

    public ClanResBuildingCollectComponent getCollectComponent() {
        return collectComponent;
    }

    public ClanResBuildingBuildComponent getBuildComponent() {
        return buildComponent;
    }

    @Override
    public ClanResBuildingInnerArmyComponent getInnerArmyComponent() {
        return innerArmyComponent;
    }

    public ClanResourceBuildingTemplate getClanResourceBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(ClanResourceBuildingTemplate.class, getTemplateId());
    }

    public int getDisappearDay() {
        return ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getClanResourceBuildingDisappearTime();
    }

    public SceneClanEntity getOwnerSceneClan() {
        if (getProp().getClanId() == 0) {
            LOGGER.error("ClanResBuilding: clanId is 0");
            return null;
        }
        return getScene().getClanMgrComponent().getSceneClan(getProp().getClanId());
    }

    public int getTemplateId() {
        return getProp().getTemplateId();
    }

    public CommonEnum.ClanResBuildingStage getState() {
        return getProp().getState();
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return null;
    }

    public ProgressInfoProp getProgress() {
        return getProp().getProgressInfo();
    }

    public long getDisappearTsMs() {
        return getProp().getDisappearTsMs();
    }

    @Override
    public StructCommonPB.ProgressInfoPB.Builder getProgressInfoPBBuilder() {
        StructCommonPB.ProgressInfoPB.Builder builder = StructCommonPB.ProgressInfoPB.newBuilder();
        builder.setUid(getEntityId())
                .setLastCalNum(getProgress().getLastCalNum())
                .setLastCalTsMs(getProgress().getLastCalTsMs())
                .setMaxNum(getProgress().getMaxNum())
                .setSpeed(getProgress().getSpeed())
                .setStateEndTsMs(getProgress().getStateEndTsMs())
                .setStateStartTsMs(getProgress().getStateStartTsMs());
        return builder;
    }

    public StructCommon.ProgressInfo.Builder getProgressInfoByPlayerId(long playerId) {
        if (getProp().getEachPlayerCollectMap().containsKey(playerId)) {
            LOGGER.info("getProgressInfoByPlayerId: {}, playerId {}", getProp().getEachPlayerCollectMap().get(playerId), playerId);
            return getProp().getEachPlayerCollectMap().get(playerId).getCopySsBuilder();
        }
        LOGGER.info("getProgressInfoByPlayerId: null, playerId {}", playerId);
        return null;
    }

    public CommonEnum.CurrencyType getCurrencyType() {
        return getClanResourceBuildingTemplate().getResType();
    }

    public void sendExpansionLog(String action, long roleId) {
        QlogCncGuildExpansion flow = new QlogCncGuildExpansion();
        flow.setDtEventTime(TimeUtils.now2String())
                .setBuildingId(MapGridDataManager.getPartId(getScene().getMapId(), getProp().getPoint().getX(), getProp().getPoint().getY()))
                .setBuildingCoordinate(String.format("%d,%d", getProp().getPoint().getX(), getProp().getPoint().getY()))
                .setAction(action)
                .setOptionRoleId(String.valueOf(roleId))
                .setGuildBuildingID(0L);
        SceneClanEntity sceneClanEntity = getScene().getClanMgrComponent().getSceneClan(getClanId());
        if (sceneClanEntity == null) {
            return;
        }
        flow.fillHead(sceneClanEntity.getQlogComponent());
        flow.sendToQlog();
    }

    @Override
    public String toString() {
        return "ClanResBuildingEntity { " +
                "id = " + getEntityId() +
                ", templateId = " + prop.getTemplateId() +
                ", clanId = " + prop.getClanId() +
                ", state = " + prop.getState() +
                ", point = " + prop.getPoint() +
                " }";
    }

    public String getCollectQLogAction() {
        return "clan_res_collect";
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, getTemplateId());
    }

    public void updateClanFlag(SceneClanEntity sceneClan) {
        sceneClan.copy2FlagInfo(getProp().getFlagInfo());
    }
}

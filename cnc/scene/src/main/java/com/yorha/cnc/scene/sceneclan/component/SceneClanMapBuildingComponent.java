package com.yorha.cnc.scene.sceneclan.component;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.enums.AbandonBuildingReason;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.DevBuffSourceType;
import com.yorha.proto.CommonEnum.MapAreaType;
import com.yorha.proto.CommonEnum.MapBuildingType;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 联盟领地  地缘建筑管理
 */
public class SceneClanMapBuildingComponent extends AbstractComponent<SceneClanEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanMapBuildingComponent.class);
    /**
     * 拥有的地图建筑（据点、城池） 完好状态，没被别的占领   myMapBuilding包括城池   城池的定义是非据点..
     */
    private final Map<Long, MapBuildingEntity> myMapBuilding = new Long2ObjectOpenHashMap<>();
    private final Map<Long, MapBuildingEntity> myCity = new Long2ObjectOpenHashMap<>();
    /**
     * 正在占领中的地图建筑（据点、城池）
     */
    private final Map<Long, MapBuildingEntity> occupyingMapBuilding = new Long2ObjectOpenHashMap<>();
    /**
     * 正在被占领中的地图建筑（据点、城池）（所属方是自己）
     */
    private final Map<Long, MapBuildingEntity> beOccupyingMapBuilding = new Long2ObjectOpenHashMap<>();
    /**
     * 联盟当前占有的地片， 用来做能否攻占某个地图建筑（据点、城池）的判定
     */
    private final Set<Integer> myPart = new IntOpenHashSet();
    /**
     * 据点势力值  城池势力值  联盟建筑势力值  联盟势力变更版本号  当前联盟指挥中心数量
     */
    private int strongholdPower;
    private int cityPower;
    private int clanBuildingPower;
    private int version;

    public SceneClanMapBuildingComponent(SceneClanEntity owner) {
        super(owner);
        version = owner.getSceneEntity().getClanMgrComponent().getMapVersion();
    }

    /**
     * 判断某个坐标点是否是本联盟的
     */
    public boolean isMyTerritory(int x, int y) {
        int partId = MapGridDataManager.getPartId(getOwner().getSceneEntity().getMapId(), x, y);
        return myPart.contains(partId);
    }

    public boolean isOwnPart(int partId) {
        return myPart.contains(partId);
    }

    public boolean isOwnCity(long mapBuildingId) {
        return myCity.containsKey(mapBuildingId);
    }

    public Collection<MapBuildingEntity> getOwnMapBuildings() {
        return myMapBuilding.values();
    }

    public MapBuildingEntity getOwnMapBuilding(long id) {
        return myMapBuilding.get(id);
    }

    /**
     * 发送领土模块buff快照
     */
    public void sendTerritoryBuffSnapshot() {
        if (!getOwner().getSceneEntity().isInitOk()) {
            getOwner().setUpdateBuff(true);
            return;
        }
        SsClanTerritory.SyncTerritoryBuffSnapshotCmd.Builder builder = SsClanTerritory.SyncTerritoryBuffSnapshotCmd.newBuilder();
        Map<Integer, CommonMsg.TerritoryBuffItem.Builder> ret = getOwner().getMapBuildingComponent().buildTerritoryBuffSnapshot();
        for (CommonMsg.TerritoryBuffItem.Builder value : ret.values()) {
            builder.putBuff(value.getBuffId(), value.build());
        }
        getOwner().tellClan(builder.build());
    }

    /**
     * 构造领土模块buff快照
     * kvk 没有主基地和指挥中心的额外加成
     */
    public Map<Integer, CommonMsg.TerritoryBuffItem.Builder> buildTerritoryBuffSnapshot() {
        Map<Integer, CommonMsg.TerritoryBuffItem.Builder> ret = new HashMap<>();
        for (MapBuildingEntity entity : myMapBuilding.values()) {
            TerritoryBuildingTemplate template = entity.getTerritoryBuildingTemplate();
            List<Integer> buffIdList = null;
            if (entity.getProp().getConstructInfo().getIsConnectedToCommandNet()) {
                buffIdList = template.getIncorporateBuffList();
            } else {
                buffIdList = template.getBaseBuffList();
            }
            if (CollectionUtils.isEmpty(buffIdList)) {
                continue;
            }
            for (Integer buffId : buffIdList) {
                if (!ret.containsKey(buffId)) {
                    CommonMsg.TerritoryBuffItem.Builder builder = CommonMsg.TerritoryBuffItem.newBuilder();
                    if (GameLogicConstants.isMapCity(entity.getAreaType())) {
                        builder.setType(DevBuffSourceType.DBST_CLAN_OWN_CITY);
                    } else {
                        builder.setType(DevBuffSourceType.DBST_CLAN_BUILDING);
                    }
                    ret.put(buffId, builder.setBuffId(buffId));
                }
                ret.get(buffId).setLayers(ret.get(buffId).getLayers() + 1);
            }
        }
        // 指挥中心
        int commandCenterLayers = getOwner().getBuildComponent().getCommandCenterExtraBuffLayers();
        if (commandCenterLayers != 0) {
            ConstClanTerritoryTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
            int singleBuff = template.getCommandCenterSingleBuff();
            CommonMsg.TerritoryBuffItem.Builder builder = CommonMsg.TerritoryBuffItem.newBuilder();
            ret.put(singleBuff, builder.setBuffId(singleBuff).setLayers(commandCenterLayers).setType(DevBuffSourceType.DBST_CLAN_COMMAND_CENTER));
        }

        // 主基地
        int mainBaseLayers = getOwner().getBuildComponent().getMainBaseExtraBuffLayers();
        if (mainBaseLayers != 0) {
            ConstClanTerritoryTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
            int mainBaseBuff = template.getMainBaseBuff();
            CommonMsg.TerritoryBuffItem.Builder builder = CommonMsg.TerritoryBuffItem.newBuilder();
            ret.put(mainBaseBuff, builder.setBuffId(mainBaseBuff).setLayers(mainBaseLayers).setType(DevBuffSourceType.DBST_CLAN_MAIN_BASE));
        }
        LOGGER.info("buildTerritoryBuffSnapshot clanId={} {}", getEntityId(), ret);
        return ret;
    }

    /**
     * 占领中的
     */
    public void addToOccupying(MapBuildingEntity mapBuildingEntity) {
        occupyingMapBuilding.put(mapBuildingEntity.getEntityId(), mapBuildingEntity);
        LOGGER.info("{} start occupy {}", getOwner(), mapBuildingEntity);
        addVersion();
    }

    /**
     * 中断占领
     */
    public void stopOccupy(long mapBuildingId) {
        occupyingMapBuilding.remove(mapBuildingId);
        LOGGER.info("{} stop occupy {}", getOwner(), mapBuildingId);
        addVersion();
    }

    /**
     * 加入拥有列表
     */
    public void addToOwner(MapBuildingEntity mapBuilding, boolean isRestore) {
        long entityId = mapBuilding.getEntityId();
        if (myMapBuilding.containsKey(entityId)) {
            return;
        }
        LOGGER.info("{} add new MapBuilding {}", getOwner(), mapBuilding);
        myMapBuilding.put(entityId, mapBuilding);
        // 从正在占领中/被正在占领
        occupyingMapBuilding.remove(entityId);
        beOccupyingMapBuilding.remove(entityId);

        int partId = mapBuilding.getPartId();
        // 片加上
        myPart.add(partId);
        addVersion();
        // 势力值加上
        onPowerChange(mapBuilding, 1, isRestore);

        // 根据领土类型后处理
        RegionalAreaSettingTemplate template = mapBuilding.getAreaSettingTemplate();
        // 关卡连通图
        if (template.getAreaType() == CommonEnum.MapAreaType.CROSSING) {
            getOwner().getCrossComponent().updateCrossingMap(partId, true);
        }
        // city 计数器+1
        if (GameLogicConstants.isMapCity(mapBuilding.getAreaType())) {
            myCity.put(entityId, mapBuilding);
            if (!isRestore && GameLogicConstants.isKingCity(mapBuilding.getBuildingTemplate().getType())) {
                if (getOwner().getSceneEntity().isBigScene()) {
                    getOwner().getSceneEntity().getBigScene().getKingdomComponent().newKing(mapBuilding, getOwner());
                }
            }
        }
        if (!isRestore) {
            // buff 堡垒  指挥网相关判定
            getOwner().getBuildComponent().afterOwnNewMapBuilding(mapBuilding);
            // 刷新资源田归属
            refreshResource(partId, getEntityId());
            // 增加里程碑积分
            if (getOwner().getSceneEntity().getMileStoneOrNullComponent() != null) {
                getOwner().getSceneEntity().getMileStoneOrNullComponent().onMapBuildOccupyByClan(getOwner().getZoneId(), 0, getEntityId(), 0, mapBuilding.getTemplateId());
            }
        }
    }

    private void afterLoseMapBuilding(MapBuildingEntity entity, boolean isRestore) {
        final int partId = entity.getPartId();
        // 片扣掉
        myPart.remove(partId);
        addVersion();
        // 扣除势力值
        onPowerChange(entity, -1, isRestore);
        // 根据领土类型后处理
        RegionalAreaSettingTemplate template = entity.getAreaSettingTemplate();
        // 关卡连通图
        if (template.getAreaType() == CommonEnum.MapAreaType.CROSSING) {
            getOwner().getCrossComponent().updateCrossingMap(partId, false);
        }
        // city 计数器
        if (GameLogicConstants.isMapCity(entity.getAreaType())) {
            myCity.remove(entity.getEntityId());
            if (!isRestore && GameLogicConstants.isKingCity(entity.getBuildingTemplate().getType())) {
                if (getOwner().getSceneEntity().isBigScene()) {
                    getOwner().getSceneEntity().getBigScene().getKingdomComponent().kingFall(entity);
                }
            }
        }
        if (!isRestore) {
            // 为了在起服流程中 等所有建筑恢复完再去做关联性的事
            ownerActor().tell(ownerActor().self(), new ActorRunnable<SceneActor>("CheckMapBuildingAdjoin",
                    (actor) -> {
                        // 周围占领中的 能否继续占领判定
                        RegionalAreaSettingTemplate template1 = getOwner().getSceneEntity().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
                        SceneEntity scene = actor.getScene();
                        for (int adjoinPartId : template1.getAdjoinAreaIdList()) {
                            MapBuildingEntity mapBuilding = scene.getBuildingMgrComponent().getMapBuilding(adjoinPartId);
                            if (mapBuilding == null || !occupyingMapBuilding.containsKey(mapBuilding.getEntityId())) {
                                continue;
                            }
                            // 新增不满足连接条件的mapBuilding们  都触发一遍
                            if (!checkAdjoin(mapBuilding.getPartId(), false)) {
                                mapBuilding.onLoseAdjoin(getEntityId());
                                // 占领中的移除下
                                occupyingMapBuilding.remove(mapBuilding.getEntityId());
                            }
                        }
                    }));
            // buff 堡垒  指挥网相关判定
            getOwner().getBuildComponent().afterLoseMapBuilding(entity);
            // 刷资源田
            refreshResource(entity.getPartId(), 0);
            // 扣里程碑进度(过滤恢复)
            getOwner().getSceneEntity().getMileStoneOrNullComponent().onMapBuildOccupyByClan(0, getOwner().getZoneId(), 0, getOwner().getClanId(), entity.getTemplateId());
        }
    }

    private void refreshResource(int partId, long clanId) {
        if (!getOwner().getSceneEntity().isMainScene()) {
            return;
        }
        MainSceneResMgrComponent resMgrComponent = (MainSceneResMgrComponent) getOwner().getSceneEntity().getResMgrComponent();
        resMgrComponent.refreshResBuildingClan(partId, clanId == 0 ? null : getOwner());
    }

    /**
     * 某建筑被别人占领中
     */
    public void addToBeOccupy(long mapBuildingId, boolean isRestore) {
        MapBuildingEntity entity = myMapBuilding.remove(mapBuildingId);
        if (entity == null) {
            return;
        }
        beOccupyingMapBuilding.put(mapBuildingId, entity);
        afterLoseMapBuilding(entity, isRestore);
        LOGGER.info("{} be occupy MapBuilding {}", getOwner(), mapBuildingId);
    }

    /**
     * 丢失  荒废期自然结束/主动放弃/被别人占领走了/ 初始地只能有一个
     */
    public void removeMapBuilding(long mapBuildingId) {
        final MapBuildingEntity entity = myMapBuilding.remove(mapBuildingId);
        // 从正在占领中/被正在占领/有状况的移除
        occupyingMapBuilding.remove(mapBuildingId);
        beOccupyingMapBuilding.remove(mapBuildingId);
        if (entity != null) {
            afterLoseMapBuilding(entity, false);
        }
        LOGGER.info("{} lose MapBuilding {}", getOwner(), mapBuildingId);
    }

    /**
     * 拉取联盟领地信息
     */
    public CommonMsg.ClanTerritoryPage getClanTerritoryPage() {
        CommonMsg.ClanTerritoryPage.Builder builder = CommonMsg.ClanTerritoryPage.newBuilder();
        builder.setZoneId(getOwner().getSceneEntity().getZoneId())
                .setCityPower(cityPower).setStrongholdPower(strongholdPower).setClanBuildingPower(clanBuildingPower)
                .setCityNum(myCity.size()).setStrongholdNum(myMapBuilding.size() - myCity.size())
                .setStrongholdNumMax(SceneAddCalc.getStrongholdMax(getOwner()));
        // 如果有军团资源中心，加入到builder中
        CommonMsg.ClanResBuildingInfo.Builder resBuildingBuilder = getOwner().getBuildComponent().getClanResBuildingPage();
        if (resBuildingBuilder != null) {
            builder.setClanResBuldingInfo(resBuildingBuilder);
        }
        // 建筑细节只发对应服
        if (!getOwner().getSceneEntity().isBigScene()) {
            return builder.build();
        }
        long clanId = getEntityId();
        // 拥有的
        for (long mapBuildingId : myMapBuilding.keySet()) {
            myMapBuilding.get(mapBuildingId).addShowDetailsPb(builder, clanId);
        }
        // 占领中
        for (MapBuildingEntity mapBuildingEntity : occupyingMapBuilding.values()) {
            mapBuildingEntity.addShowDetailsPb(builder, clanId);
        }
        // 被占领中
        for (long mapBuildingId : beOccupyingMapBuilding.keySet()) {
            beOccupyingMapBuilding.get(mapBuildingId).addShowDetailsPb(builder, clanId);
        }
        return builder.build();
    }

    /**
     * 联盟解散
     */
    public void onClanDismiss() {
        myMapBuilding.values().forEach(
                entity -> {
                    entity.getStageMgrComponent().ownerAbandon(AbandonBuildingReason.ABR_CLAN_DISMISS);
                    // 刷资源田
                    refreshResource(entity.getPartId(), 0);
                });
        occupyingMapBuilding.values().forEach(entity -> entity.getStageMgrComponent().occupierAbandon(AbandonBuildingReason.ABR_CLAN_DISMISS));
        beOccupyingMapBuilding.values().forEach(entity -> entity.getStageMgrComponent().ownerAbandon(AbandonBuildingReason.ABR_CLAN_DISMISS));
        myMapBuilding.clear();
        beforeMyCityClear();
        myCity.clear();
        occupyingMapBuilding.clear();
        beOccupyingMapBuilding.clear();
        myPart.clear();
        LOGGER.info("{} onDismiss", getOwner());
    }

    /**
     * 检查是否能攻击对应的据点
     */
    public ErrorCode checkCanAttack(MapBuildingEntity entity) {
        int partId = entity.getPartId();
        // 千人同屏不开
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            return ErrorCode.OK;
        }
        // 判断gm命令是否打开，判断是否跳过连地检查
        if (ServerContext.getServerDebugOption().isGmSwitch()) {
            if (!ServerContext.getServerDebugOption().isOpenAdjoinCheck()) {
                return ErrorCode.OK;
            }
        }
        // 军团未拥有任何地块时，不允许玩家进攻据点
        if (myPart.size() == 0) {
            return ErrorCode.CLAN_BUILDING_NO_TERRITORY;
        }
        // 军团有且只有一个地块，且是建设中的军团基地时不允许玩家进攻据点
        if (myPart.size() == 1 && getOwner().getBuildComponent().isUniqueMainBaseStillBuild()) {
            return ErrorCode.CLAN_BUILDING_FREE_GOT_BUILDING_CANNOT_ATTACK;
        }
        // 攻打联盟堡垒，不需要连地，需要在判断连地前判断
        if (entity.getProp().getConstructInfo().getType() == MapBuildingType.MBT_CLAN_FORTRESS) {
            return ErrorCode.OK;
        }
        // 如果是据点  需要判定数量是否超过上限
        if (isTerritoryNumFull(entity.getAreaType())) {
            return ErrorCode.TERRITORY_NUM_LIMIT;
        }
        // 需要判定连地
        if (!checkAdjoin(partId, true)) {
            return ErrorCode.TERRITORY_NEED_ADJACENT;
        }
        return ErrorCode.OK;
    }

    /**
     * @param mapAreaType 地块类型
     * @return 判断领土数量是否达到上限
     */
    public boolean isTerritoryNumFull(MapAreaType mapAreaType) {
        if (GameLogicConstants.isMapStronghold(mapAreaType)) {
            int strongholdNum = myMapBuilding.size() - myCity.size();
            return strongholdNum >= SceneAddCalc.getStrongholdMax(getOwner());
        }
        return false;
    }

    /**
     * 原先是自己 被别人打了  现在别人放弃了  看看能不能走夺回逻辑    or    占领时间结束判一下能不能拥有
     * 不能的话 需要移除下
     * 判一下上限数量就行了
     */
    public boolean isCanRetake(MapBuildingEntity entity) {
        if (isTerritoryNumFull(entity.getAreaType())) {
            removeMapBuilding(entity.getEntityId());
            return false;
        }
        return true;
    }

    /**
     * 判断连地条件是否满足
     */
    private boolean checkAdjoin(int partId, boolean withoutBuildingMainBase) {
        RegionalAreaSettingTemplate template = getOwner().getSceneEntity().getMapTemplateDataItem()
                .getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        for (int adjoinPartId : template.getAdjoinAreaIdList()) {
            if (!myPart.contains(adjoinPartId)) {
                continue;
            }
            // 无视建设中主基地
            if (withoutBuildingMainBase && getOwner().getBuildComponent().isStillBuildMainBase(adjoinPartId)) {
                continue;
            }
            return true;
        }
        return false;
    }

    /**
     * 势力值变更
     */
    public void onPowerChange(MapBuildingEntity mapBuilding, int isNew, boolean isRestore) {
        if (mapBuilding.getBuildComponent().getType() != MapBuildingType.MBT_NONE) {
            if (mapBuilding.isInRebuilding()) {
                // 仍在建设中需要使用原据点的势力值，此逻辑应该只在重启恢复时调到
                strongholdPower += mapBuilding.getTerritoryPower() * isNew;
            } else {
                // 不在建设中状态认为建筑已经成功建设，均添加军团建筑本身的势力值
                clanBuildingPower += mapBuilding.getBuildingPower(mapBuilding.getBuildComponent().getType()) * isNew;
            }
        } else {
            MapAreaType areaType = mapBuilding.getAreaType();
            int addPower = mapBuilding.getTerritoryPower();
            if (GameLogicConstants.isMapStronghold(areaType)) {
                strongholdPower += addPower * isNew;
            } else if (GameLogicConstants.isMapCity(areaType)) {
                cityPower += addPower * isNew;
            }
        }
        LOGGER.info("{} powerChange. isNew: {}  total: {}", getOwner(), isNew, getTotalPower());
        // restore过程中不管
        if (!isRestore) {
            syncPowerWithPartInfoToClan(isNew == 1, mapBuilding.getPartId());
        }
    }

    public void onPowerChange(MapBuildingEntity mapBuilding, MapBuildingType oldType, MapBuildingType newType) {
        if (oldType == MapBuildingType.MBT_NONE) {
            strongholdPower -= mapBuilding.getTerritoryPower();
            clanBuildingPower += mapBuilding.getBuildingPower(newType);
        } else {
            strongholdPower += mapBuilding.getTerritoryPower();
            clanBuildingPower -= mapBuilding.getBuildingPower(oldType);
        }
        // 地图初始化完成了会总的同步一次  restore过程中不管
        if (getOwner().getSceneEntity().isInitOk()) {
            syncPowerToClan();
        }
    }

    /**
     * 同步最新的势力值给联盟那边
     */
    public void syncPowerToClan() {
        SsClanTerritory.SyncTerritoryInfoCmd.Builder cmd = SsClanTerritory.SyncTerritoryInfoCmd.newBuilder();
        cmd.setPartNum(myPart.size()).setTerritoryPower(getTotalPower());
        getOwner().tellClan(cmd.build());
    }

    /**
     * 同步势力值 + 地块信息给联盟那边
     */
    public void syncPowerWithPartInfoToClan(boolean isAdd, int partId) {
        SsClanTerritory.SyncTerritoryInfoCmd.Builder cmd = SsClanTerritory.SyncTerritoryInfoCmd.newBuilder();
        cmd.setPartNum(myPart.size()).setTerritoryPower(getTotalPower());
        cmd.setAddPartOrReduce(isAdd).setPartId(partId);
        getOwner().tellClan(cmd.build());
    }

    public int getTotalPower() {
        return strongholdPower + cityPower + clanBuildingPower;
    }

    public Set<Integer> getMyPart() {
        return Collections.unmodifiableSet(myPart);
    }

    /**
     * 联盟基础信息发送变化  同步到所有建筑
     */
    public void onClanBaseChange(boolean isAddTerritoryVersion) {
        for (MapBuildingEntity entity : myMapBuilding.values()) {
            if (entity.getClanId() == getEntityId()) {
                entity.onShowClanInfoChange(getOwner());
                refreshResource(entity.getPartId(), getEntityId());
            }
        }
        for (MapBuildingEntity entity : occupyingMapBuilding.values()) {
            if (entity.getClanId() == getEntityId()) {
                entity.onShowClanInfoChange(getOwner());
            }
        }
        if (isAddTerritoryVersion) {
            addVersion();
        }
    }

    /**
     * 获取大世界势力图数据
     */
    public void tryAddTerritoryMapItem(SsSceneMap.FetchTerritoryMapAns.Builder ans, int clientVersion) {
        if (version <= clientVersion) {
            return;
        }
        StructPB.TerritoryMapItemPB.Builder builder = StructPB.TerritoryMapItemPB.newBuilder();
        int nationFlagId = getOwner().getNationFlagId();
        int zoneAreaTerritoryColor = getOwner().getZoneAreaTerritoryColor();
        // 国家旗帜都是要设置上去的
        builder.setClanId(getEntityId())
                .setNationFlagId(nationFlagId)
                .getOwnPartIdBuilder().addAllDatas(myPart);
        // 王国要塞有，就设置该颜色，没有，国家旗帜也没有，我就设置领土颜色  显示优先级应该是颜色，再国家旗帜
        if (zoneAreaTerritoryColor != 0) {
            builder.setColor(zoneAreaTerritoryColor);
        } else if (nationFlagId == 0) {
            builder.setColor(getOwner().getTerritoryColor());
        }
        for (MapBuildingEntity entity : occupyingMapBuilding.values()) {
            builder.getOccupyPartIdBuilder().addDatas(entity.getPartId());
        }
        ans.addMapItem(builder.build());
    }

    /**
     * 建筑类型->level->num
     */
    public Map<MapBuildingType, Map<Integer, Integer>> getMyCityType2Num() {
        if (myCity.isEmpty()) {
            return null;
        }
        Map<MapBuildingType, Map<Integer, Integer>> ret = new HashMap<>();
        for (MapBuildingEntity entity : myCity.values()) {
            final Map<Integer, Integer> level2Num = ret.computeIfAbsent(entity.getBuildingTemplate().getType(), key -> new HashMap<>());
            level2Num.put(entity.getLevel(), level2Num.getOrDefault(entity.getLevel(), 0) + 1);
        }
        return ret;
    }

    public void tryChangeZoneKing() {
        for (MapBuildingEntity city : myCity.values()) {
            if (GameLogicConstants.isKingCity(city.getBuildingTemplate().getType())) {
                if (getOwner().getSceneEntity().isBigScene()) {
                    getOwner().getSceneEntity().getBigScene().getKingdomComponent().kingFall(city);
                    getOwner().getSceneEntity().getBigScene().getKingdomComponent().newKing(city, getOwner());
                }
                break;
            }
        }
    }

    private void beforeMyCityClear() {
        for (MapBuildingEntity city : myCity.values()) {
            if (GameLogicConstants.isKingCity(city.getBuildingTemplate().getType())) {
                // 如果军团占有王城，军团解散，那国王就陨落了
                if (getOwner().getSceneEntity().isBigScene()) {
                    getOwner().getSceneEntity().getBigScene().getKingdomComponent().kingFall(city);
                }
                break;
            }
        }
    }

    private void addVersion() {
        version = getOwner().getSceneEntity().getClanMgrComponent().addMapVersion();
    }

    public void onMemberNameChange(AbstractScenePlayerEntity member) {
        if (getOwner().getClanOwnerId() != member.getPlayerId()) {
            return;
        }
        MapBuildingEntity kingBuilding = getKingBuilding();
        if (kingBuilding != null) {
            kingBuilding.getStageMgrComponent().updateKingCardHead(member.getCardHead().getCopyCsBuilder().build());
        }
    }

    private MapBuildingEntity getKingBuilding() {
        for (MapBuildingEntity city : myCity.values()) {
            if (GameLogicConstants.isKingCity(city.getBuildingTemplate().getType())) {
                return city;
            }
        }
        return null;
    }
}

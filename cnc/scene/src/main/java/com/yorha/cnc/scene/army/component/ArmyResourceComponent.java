package com.yorha.cnc.scene.army.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.resource.ResCollectService;
import com.yorha.common.server.ServerContext;
import com.yorha.game.gen.prop.ArmyCollectResourceProp;
import com.yorha.game.gen.prop.ArmyResourcesModelProp;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanResourceBuildingTemplate;
import res.template.ResourceTemplate;
import res.template.SoldierTypeTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 资源
 *
 * <AUTHOR>
 */
public class ArmyResourceComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyResourceComponent.class);

    public ArmyResourceComponent(ArmyEntity owner) {
        super(owner);
    }

    /**
     * 资源田 采集完成 加资源
     */
    public void onCalCollectEnd(SceneObjEntity building, CurrencyType resType, long addNum, long tsMs) {
        for (ArmyCollectResourceProp prop : getProp().getCollect()) {
            if (prop.getId() == building.getEntityId()) {
                prop.setNum(prop.getNum() + addNum).setCollectEndTsMs(tsMs);
                // 设置下当前负重
                getOwner().getStatusComponent().addBurden(resType, addNum);
                return;
            }
        }
        ArmyCollectResourceProp prop = getProp().getCollect().addEmptyValue();
        if (building.getEntityType() == EntityType.ET_ClanResBuilding) {
            ClanResBuildingEntity clanResBuilding = (ClanResBuildingEntity) building;
            prop.setTemplateId(clanResBuilding.getTemplateId());
        } else if (building.getEntityType() == EntityType.ET_ResBuilding) {
            ResBuildingEntity resBuilding = (ResBuildingEntity) building;
            prop.setTemplateId(resBuilding.getProp().getTemplateId());
        }
        prop.setCollectEndTsMs(tsMs).setNum(addNum)
                .setId(building.getEntityId())
                .getPoint().setX(building.getCurPoint().getX())
                .setY(building.getCurPoint().getY());
        // 设置下当前负重
        getOwner().getStatusComponent().addBurden(resType, addNum);
    }

    /**
     * 获取当前的资源负担
     */
    public long getCurBurden() {
        return getOwner().getStatusComponent().getProp().getCurBurden();
    }


    /**
     * 获取部队最大负重
     */
    public long getArmyMaxBurden() {
        int sumBurden = 0;
        for (SoldierProp sp : getOwner().getProp().getTroop().getTroop().values()) {
            SoldierTypeTemplate temp = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, sp.getSoldierId());
            sumBurden += SceneAddCalc.getArmyBurden(getOwner(), temp, Soldier.aliveCountOf(sp));
        }
        return sumBurden;
    }

    /**
     * 获取剩余负重转换资源量
     */
    public long getRestBurdenToRes(CurrencyType resType) {
        long restBurden = getArmyMaxBurden() - getCurBurden();
        int radio = GameLogicConstants.getCurrencyBurdenRadio(resType);
        return restBurden / radio;
    }

    /**
     * 战损了， 负重减少 资源损失  totalLossCount是负数哈
     */
    public Map<CurrencyType, Long> onLossResource(long totalLossCount) {
        LOGGER.info("{} onLossResource. weight:{}", getOwner(), totalLossCount);
        Map<CurrencyType, Pair<Long, Long>> resourceChange = new HashMap<>(5);
        Map<CurrencyType, Long> lossRes = Maps.newHashMap();
        if (totalLossCount == 0) {
            return lossRes;
        }
        long actualBurdenLoss = 0;
        // 损失资源
        // 扣除部队所持石油资源 = 部队所持石油资源量 / 部队所持资源总量 * 损失的负载，计算结果向上取整。
        double totalBurden = getCurBurden();
        for (CurrencyProp prop : getProp().getPlunder().values()) {
            // 按比例损失每种资源
            double ratio = prop.getCount() * 1.0 / totalBurden;
            long lossCount = MathUtils.ceilLong(totalLossCount * ratio);
            lossRes.put(CurrencyType.forNumber(prop.getType()), lossCount);
            resourceChange.put(CurrencyType.forNumber(prop.getType()), Pair.of(prop.getCount(), prop.getCount() + lossCount));
            prop.setCount(prop.getCount() + lossCount);
            actualBurdenLoss += lossCount;
        }
        ResCollectService service = ResHolder.getResService(ResCollectService.class);
        for (ArmyCollectResourceProp prop : getProp().getCollect()) {
            CurrencyType resType;
            if (service.isCollectFromClanRes(prop.getTemplateId())) {
                resType = ResHolder.getTemplate(ClanResourceBuildingTemplate.class, prop.getTemplateId()).getResType();
            } else {
                resType = ResHolder.getTemplate(ResourceTemplate.class, prop.getTemplateId()).getResType();
            }
            if (resType == CurrencyType.CT_None) {
                LOGGER.error("ArmyResourceComponent onLossResource. resType is none. army:{}, resType:{}", getOwner().getEntityId(), resType);
                continue;
            }
            // 按比例损失每种资源
            int currencyBurdenRadio = GameLogicConstants.getCurrencyBurdenRadio(resType);
            double ratio = prop.getNum() * currencyBurdenRadio * 1.0 / totalBurden;
            // 损失资源负重
            long lossCount = MathUtils.ceilLong(totalLossCount * ratio);
            // 实际损失资源量
            long lossResNum = lossCount / currencyBurdenRadio;
            // 实际损失负重
            lossCount = lossResNum * currencyBurdenRadio;
            lossRes.put(resType, lossRes.getOrDefault(resType, 0L) + lossResNum);
            if (resourceChange.containsKey(resType)) {
                Pair<Long, Long> old = resourceChange.get(resType);
                resourceChange.put(resType, Pair.of(old.getFirst() + prop.getNum(), old.getSecond() + prop.getNum() + lossResNum));
            } else {
                resourceChange.put(resType, Pair.of(prop.getNum(), prop.getNum() + lossResNum));
            }
            prop.setNum(prop.getNum() + lossResNum);
            actualBurdenLoss += lossCount;
        }
        getOwner().getStatusComponent().addBurden(actualBurdenLoss);

        if (ServerContext.isDevEnv() || ServerContext.isTestEnv()) {
            LOGGER.info("player:{}, army:{}, update army resource. loss resource:{}", getOwner().getScenePlayer(), getOwner().getEntityId(), getResourceUpdateStr(resourceChange));
        }
        return lossRes;
    }

    /**
     * 增加掠夺资源
     */
    public void addPlunderResource(Map<CurrencyType, Long> resources) {
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) getOwner().getScenePlayer();
        Map<CurrencyType, Pair<Long, Long>> resourceChange = new HashMap<>(5);
        int totalRes = 0;
        for (Map.Entry<CurrencyType, Long> entry : resources.entrySet()) {
            if (entry.getValue() == 0) {
                continue;
            }
            totalRes += entry.getValue();
            int type = entry.getKey().getNumber();
            long oldCount = 0;
            if (getProp().getPlunder().containsKey(type)) {
                oldCount = getProp().getPlunderV(type).getCount();
            } else {
                getProp().addEmptyPlunder(type);
            }
            getProp().getPlunderV(type).setCount(oldCount + entry.getValue());
            resourceChange.put(entry.getKey(), Pair.of(oldCount, oldCount + entry.getValue()));
            // 发生掠夺，更新玩家当日掠夺量
            CurrencyProp todayPlunderV = scenePlayer.getProp().getPlunderModel().getTodayPlunderV(type);
            if (todayPlunderV != null) {
                todayPlunderV.setCount(todayPlunderV.getCount() + entry.getValue());
            } else {
                scenePlayer.getProp().getPlunderModel().putTodayPlunderV(new CurrencyProp().setType(type).setCount(entry.getValue()));
            }
        }
        getOwner().getStatusComponent().addBurden(totalRes);

        if (ServerContext.isDevEnv() || ServerContext.isTestEnv()) {
            LOGGER.info("player:{}, army:{}, update army resource. add resource:{}", getOwner().getScenePlayer(), getOwner().getEntityId(), getResourceUpdateStr(resourceChange));
        }
    }

    private ArmyResourcesModelProp getProp() {
        return getOwner().getProp().getResource();
    }

    private String getResourceUpdateStr(Map<CurrencyType, Pair<Long, Long>> resourceChange) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<CurrencyType, Pair<Long, Long>> entry : resourceChange.entrySet()) {
            sb.append(entry.getKey())
                    .append(": ")
                    .append(entry.getValue().getFirst())
                    .append(" -> ")
                    .append(entry.getValue().getSecond())
                    .append(", ");
        }
        return sb.toString();
    }

    public void addOutTimes() {
        LOGGER.info("ArmyResourceComponent addOutTimes, oldOutTimes={}", getProp().getOutTimes());
        getProp().setOutTimes(getProp().getOutTimes() + 1);
    }
}

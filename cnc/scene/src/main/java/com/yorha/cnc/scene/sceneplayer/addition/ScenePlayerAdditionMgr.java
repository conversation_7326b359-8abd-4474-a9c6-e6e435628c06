package com.yorha.cnc.scene.sceneplayer.addition;

import com.yorha.cnc.scene.abstractsceneplayer.addition.AbstractScenePlayerAdditionMgr;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerMisc;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ScenePlayerAdditionMgr extends AbstractScenePlayerAdditionMgr<ScenePlayerEntity> {
    public ScenePlayerAdditionMgr(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public AdditionSysProp getAdditionSys() {
        return getOwner().getProp().getAdditionSys();
    }

    @Override
    protected void updateAdditionToPlayer(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            return;
        }
        tellPlayerUpdateAdditionFromScene(sourceType, additions);
    }

    private void tellPlayerUpdateAdditionFromScene(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        SsPlayerMisc.UpdateAdditionFromSceneCmd.Builder cmd = SsPlayerMisc.UpdateAdditionFromSceneCmd.newBuilder()
                .setCmd(CommonMsg.UpdatePlayerAdditionCmd.newBuilder()
                        .setPlayerId(getOwner().getEntityId())
                        .setSource(sourceType)
                        .putAllAddition(additions));
        getOwner().tellPlayer(cmd.build());
    }


    @Override
    public long getAddition(int additionId) {
        long zoneAddition = 0;
        if (getOwner().getScene().isBigScene()) {
            zoneAddition = getOwner().getScene().getBigScene().getZoneEntity().getAdditionComponent().getAddition(additionId);
        }
        return super.getAddition(additionId) + zoneAddition;
    }
}

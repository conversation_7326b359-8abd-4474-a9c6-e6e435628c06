package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.SsClanBase;
import com.yorha.proto.SsPlayerMisc;
import qlog.flow.*;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 大世界qlog组件
 *
 * <AUTHOR>
 */
public class MainSceneQlogComponent extends AbstractComponent<SceneEntity> implements QlogServerFlowInterface {

    public MainSceneQlogComponent(SceneEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        // 服务器状态qlog
        getOwner().getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.QLOG_GAME_SERVER_STATE, () -> {
            QlogGameSvrState.init(this)
                    .setVGameIP("xxx")
                    .setDtEventTime(TimeUtils.now2String())
                    .sendToQlog();
        }, 10, 5 * 60, TimeUnit.SECONDS);

        // 服务器在线人数qlog
        getOwner().getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.QLOG_CNC_ONLINE_COUNT, () -> {
            QlogOnlineCnt.init(this)
                    .setDtEventTime(TimeUtils.now2String())
                    .setGameAppid(GameLogicConstants.GAME_APPID)
                    .setOnlinecntAndroid(getOwner().getPlayerMgrComponent().getOnlinePlayerIds().size())
                    .setOnlinecntIos(0)
                    .setTimeKey(SystemClock.now())
                    .setGsId(String.valueOf(ServerContext.getServerInfo().getWorldId()))
                    .setZoneAreaId(getOwner().getZoneId())
                    .setCountry("")
                    .sendToQlog();

        }, 1, 60, TimeUnit.SECONDS);

        // 触发玩家打qlog
        long now = SystemClock.now() / 1000;
        long initialDelay = 86400 - now % 86400;
        getOwner().getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.QLOG_PLAYER_SNAP_SHOT, () -> {
            Collection<ScenePlayerEntity> allScenePlayer = getOwner().getPlayerMgrComponent().getAllScenePlayer();
            for (ScenePlayerEntity abstractScenePlayerEntity : allScenePlayer) {
                if (abstractScenePlayerEntity.isOnline()) {
                    getOwner().ownerActor().tellPlayer(abstractScenePlayerEntity.getZoneId(), abstractScenePlayerEntity.getPlayerId(), SsPlayerMisc.RecordZoneSnapshotCmd.getDefaultInstance());
                }
            }
        }, initialDelay, 86400, TimeUnit.SECONDS);

        getOwner().getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.QLOG_CLAN_SNAP_SHOT, () -> {
            final Set<Long> allClanId = getOwner().getBigScene().getClanMgrComponent().getAllActiveClanId();
            for (long clanId : allClanId) {
                SsClanBase.RecordClanSnapshotCmd.Builder cmd = SsClanBase.RecordClanSnapshotCmd.newBuilder();
                getOwner().ownerActor().tellClan(getOwner().getZoneId(), clanId, cmd.build());
            }
        }, MathUtils.addExact(initialDelay, (10 * 60)), 86400, TimeUnit.SECONDS);

        //野怪监控流水
        getOwner().getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.QLOG_MONSTER_COUNT, () -> {
            QlogCncMapMonsterCount.init(this)
                    .setDtEventTime(TimeUtils.now2String())
                    .setMonsterConfig(getOwner().getBigScene().getObjMgrComponent().getMonsterCountStr(null))
                    .sendToQlog();
        }, 10, 5 * 60, TimeUnit.SECONDS);
    }

    public void sendMonsterSpawnQLog(String spawnCount, String action) {
        QlogCncMapMonster.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setMonsterConfig(spawnCount)
                .setAction(action)
                .sendToQlog();
    }

    public void sendBossQLog(String action, int partId, int bossId, int level) {
        QlogCncBossFlow.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setAction(action)
                .setAreaID(partId)
                .setMonsterId(bossId)
                .setMonsterLevel(level)
                .sendToQlog();
    }

    public void sendSeasonQLog(int season) {
        QlogCncSeasonState.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setSeason(season)
                .sendToQlog();
    }

    // --------------------------------------------------- qlog参数 ---------------------------------------------------
    @Override
    public String getServerType() {
        return "";
    }

    @Override
    public int getIZoneAreaID() {
        return getOwner().getZoneId();
    }

    @Override
    public String getServerOpenTime() {
        return TimeUtils.timeStampMs2String(getOwner().getOpenTsMs());
    }

    @Override
    public long getServerOpenTimeFar() {
        return TimeUtils.ms2Second((SystemClock.now() - getOwner().getOpenTsMs()));
    }

    @Override
    public int getServerOnline() {
        return getOwner().getPlayerMgrComponent().getOnlinePlayerIds().size();
    }

    @Override
    public String getServerCondition() {
        return "xxx";
    }

    @Override
    public int getServerMilestoneStage() {
        MileStoneMgrComponent mileStoneOrNullComponent = getOwner().getMileStoneOrNullComponent();
        if (mileStoneOrNullComponent == null) {
            return 0;
        }
        return mileStoneOrNullComponent.getCurMileStone();
    }

    @Override
    public String getAccountId() {
        return "server_" + ServerContext.getServerInfo().getWorldId();
    }
}

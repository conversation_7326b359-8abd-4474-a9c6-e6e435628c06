package com.yorha.cnc.scene.entity.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.actor.cluster.toplogy.Node;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.server.NodeRole;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class MarqueeComponent extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(MarqueeComponent.class);

    public MarqueeComponent(SceneEntity owner) {
        super(owner);
    }

    public void sendPlayerMarquee(final long playerId, final int marqueeId, final StructPB.DisplayDataPB param) {
        AbstractScenePlayerEntity scenePlayer = getOwner().getPlayerMgrComponent().getScenePlayer(playerId);
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, param);
        scenePlayer.sendMsgToClient(MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    /**
     * 联盟跑马灯
     */
    public void sendClanMarquee(final long clanId, final int marqueeId, final StructPB.DisplayDataPB param, @Nullable final StructClanPB.ClanFlagInfoPB flagInfo) {
        SceneClanEntity clan = getOwner().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (clan == null) {
            LOGGER.error("sendClanMarquee failed clanId={} marqueeId={}", clanId, marqueeId);
            return;
        }
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, param, flagInfo);
        BroadcastHelper.toCsOnlinePlayerInClan(clan.getZoneId(), clan.getEntityId(), MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    /**
     * 全服广播
     */
    public void sendFullServerMarquee(final int zoneId, final int marqueeId, StructPB.DisplayDataPB displayData, StructClanPB.ClanFlagInfoPB flagInfo) {
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, displayData, flagInfo);
        BroadcastHelper.toCsOnlinePlayerInZone(zoneId, MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    /**
     * 场景广播
     */
    public void sendSceneMarquee(final int marqueeId, StructPB.DisplayDataPB displayData, StructClanPB.ClanFlagInfoPB flagInfo) {
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, displayData, flagInfo);
        getOwner().getPlayerMgrComponent().broadcastOnlineClientMsg(MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    /**
     * 全world广播
     */
    public void sendWorldMarquee(final int marqueeId, final StructPB.DisplayDataPB displayData) {
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, displayData);
        List<Node> nodeList = ServerContext.getActorSystem().getRegistryValue().getServerTypeNodeList(NodeRole.Zone.getTypeId());
        for (Node node : nodeList) {
            BroadcastHelper.toCsOnlinePlayerInZone(node.getZoneId(), MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
        }
    }

    /**
     * 场景联盟跑马灯
     */
    public void sendSceneClanMarquee(final long clanId, final int marqueeId, final StructPB.DisplayDataPB param) {
        SceneClanEntity clan = getOwner().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (clan == null) {
            LOGGER.error("sendSceneClanMarquee failed clanId={} marqueeId={}", clanId, marqueeId);
            return;
        }
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(marqueeId, param, clan.getFlagInfoPb());
        clan.getMemberComponent().broadcastOnlineClientMsg(MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
    }

    public void sendClanOrPlayerMarquee(int marqueeId, StructPB.DisplayDataPB params, long clanId, long playerId) {
        if (marqueeId == 0) {
            return;
        }
        if (clanId != 0) {
            sendSceneClanMarquee(clanId, marqueeId, params);
        } else if (playerId != 0) {
            sendPlayerMarquee(playerId, marqueeId, params);
        }
    }

    /**
     * 针对IDIP的循环跑马灯
     *
     * @param periodMs 间隔时间
     * @param count    播放次数
     */
    public void sendLoopMarquee(int periodMs, int count, Map<Integer, Struct.DisplayData> msgMap) {
        // 无脑取消旧的
        cancelMarquee(count == 0 ? "active" : "pass");
        if (count == 0) {
            return;
        }
        LOGGER.info("sendBatchPlayerMarquee periodMs={} count={}", periodMs, count);
        long endTsMs = SystemClock.now() + ((long) periodMs * count);
        AtomicInteger laveCount = new AtomicInteger(count);
        final Runnable runnable = () -> {
            if (endTsMs < SystemClock.now()) {
                cancelMarquee("expire");
                return;
            }
            LOGGER.info("MarqueeComponent sendLoopMarquee trigger marquee, lave={}", laveCount.decrementAndGet());
            SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd.Builder builder = SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd.newBuilder();
            builder.setMsgType(MsgType.PLAYER_MARQUEEMESSAGE_NTF);
            for (Map.Entry<Integer, Struct.DisplayData> entry : msgMap.entrySet()) {
                builder.putMsgMultiLanguageBytes(entry.getKey(), MsgHelper.buildMarqueeMsg(Constants.IDIP_MARQUEE_ID, entry.getValue()).toByteString());
            }
            BroadcastHelper.toCsOnlinePlayerInZoneWithMultiLanguage(ownerActor().getZoneId(), builder.build());
        };
        getOwner().getTimerComponent().addRepeatTimer(Constants.IDIP_MARQUEE_ID, TimerReasonType.MARQUEE_SCHEDULE_REPEAT, runnable, 0, periodMs, TimeUnit.MILLISECONDS);
    }

    public void cancelMarquee(String reason) {
        LOGGER.info("cancel marquee, reason={}", reason);
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.MARQUEE_SCHEDULE_REPEAT, String.valueOf(Constants.IDIP_MARQUEE_ID));
    }
}

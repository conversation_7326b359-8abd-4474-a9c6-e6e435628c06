package com.yorha.cnc.scene.sceneplayer;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerBuilder;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerBattleComponent;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;
import com.yorha.cnc.scene.sceneplayer.component.*;

/**
 * <AUTHOR>
 */
public class ScenePlayerBuilder extends AbstractScenePlayerBuilder<ScenePlayerEntity> {
    @Override
    public ScenePlayerSoldierComponent soldierMgrComponent(ScenePlayerEntity owner) {
        return new ScenePlayerSoldierComponent(owner);
    }

    @Override
    public ScenePlayerDevBuffComponent devBuffComponent(ScenePlayerEntity owner) {
        return new ScenePlayerDevBuffComponent(owner);
    }

    @Override
    public ScenePlayerAdditionComponent additionComponent(ScenePlayerEntity owner) {
        return new ScenePlayerAdditionComponent(owner);
    }

    @Override
    public ScenePlayerArmyMgrComponent armyMgrComponent(ScenePlayerEntity owner) {
        return new ScenePlayerArmyMgrComponent(owner);
    }

    @Override
    public ScenePlayerCityComponent cityComponent(ScenePlayerEntity owner) {
        return new ScenePlayerCityComponent(owner);
    }
    
    @Override
    public AbstractScenePlayerWarningComponent warningComponent(ScenePlayerEntity owner) {
        return new AbstractScenePlayerWarningComponent(owner);
    }

    @Override
    public ScenePlayerRallyComponent rallyComponent(ScenePlayerEntity owner) {
        return new ScenePlayerRallyComponent(owner);
    }

    @Override
    public ScenePlayerHospitalComponent hospitalComponent(ScenePlayerEntity owner) {
        return new ScenePlayerHospitalComponent(owner);
    }

    @Override
    public AbstractScenePlayerBattleComponent battleComponent(ScenePlayerEntity owner) {
        return new ScenePlayerBattleComponent(owner);
    }

    @Override
    public ScenePlayerWallComponent wallComponent(ScenePlayerEntity owner) {
        return new ScenePlayerWallComponent(owner);
    }
}

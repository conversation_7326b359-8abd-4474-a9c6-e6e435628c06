package com.yorha.cnc.mainScene.bigScene;

import com.yorha.cnc.mainScene.IMainScene;
import com.yorha.cnc.mainScene.bigScene.component.BigSceneBornMgrComponent;
import com.yorha.cnc.mainScene.bigScene.component.BigSceneMailComponent;
import com.yorha.cnc.mainScene.bigScene.component.BigScenePlayerMgrComponent;
import com.yorha.cnc.mainScene.common.component.*;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.SceneLoadHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanFactory;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerFactory;
import com.yorha.cnc.zone.component.MileStoneMgrComponent;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.component.ZoneKingdomComponent;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.WorldLayerService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.SsPlayer;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class BigSceneEntity extends SceneEntity implements IMainScene {
    private static final Logger LOGGER = LogManager.getLogger(BigSceneEntity.class);

    private final MainSceneBuildingMgrComponent buildingMgrComponent = new MainSceneBuildingMgrComponent(this);
    private final BigSceneBornMgrComponent bornMgrComponent = new BigSceneBornMgrComponent(this);
    private final BigSceneMailComponent mailComponent = new BigSceneMailComponent(this);
    private final MainSceneQlogComponent qlogComponent = new MainSceneQlogComponent(this);
    private final MainSceneResMgrComponent resMgrComponent = new MainSceneResMgrComponent(this);
    /**
     * zone数据逻辑管理器
     */
    private final ZoneEntity zoneEntity;
    private long totalTickNum = 0;
    private long battleTickTotalCostMs = 0;
    private long tickTotalCostMs = 0;
    /**
     * data patch版本号，用于通知path给在线玩家
     */
    private int dataPatchVersion;

    public BigSceneEntity(SceneActor sceneActor, BigSceneBuilder builder, ZoneEntity zoneEntity) {
        super(sceneActor, sceneActor.getSceneId(), builder);
        this.zoneEntity = zoneEntity;
        zoneEntity.onSceneCreate(this);
        dataPatchVersion = getDataPatchVersion();
        // 真的没办法 有时序依赖 又不能放在postInit 因为load前要准备好
        getCityMoveComponent().initNav();
        getPathFindMgrComponent().initPathFind();
        getAoiMgrComponent().initAoi();
        getBornMgrComponent().initBornItem();
    }

    public ZoneEntity getZoneEntity() {
        return zoneEntity;
    }

    @Override
    public long getOpenTsMs() {
        return getZoneEntity().getServerOpenTsMs();
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_BigScene;
    }

    @Override
    public int getMapId() {
        return BigSceneConstants.BIG_SCENE_MAP_ID;
    }

    @Override
    public CommonEnum.MapType getMapType() {
        return CommonEnum.MapType.MAT_BIG_SCENE;
    }

    @Override
    public long getMapIdForPoint() {
        return this.getZoneId();
    }

    @Override
    public boolean needSendBattleMail() {
        return true;
    }

    @Override
    public void onTick() {
        this.totalTickNum++;
        this.isInTick = true;
        // 测量一次onTick的耗时
        GeminiStopWatch watchForOnTick = new GeminiStopWatch("start_onTick");
        try {
            getTickMgrComponent().onTick(watchForOnTick);
        } catch (Exception e) {
            WechatLog.error(e);
        }
        // 测量一次onTick中战斗部分的耗时
        long battleTickCost = getBattleGroundComponent().onTick();
        // 战斗tick平均耗时
        this.battleTickTotalCostMs += battleTickCost;
        if (this.totalTickNum % 10 == 0) {
            long avgCostMs = this.battleTickTotalCostMs / 10;
            this.battleTickTotalCostMs = 0;
            LOGGER.info("gemini_pref BattleGround tick avg:{}ms", avgCostMs);
            MonitorUnit.BATTLE_TICK_AVG_COST_TIME.labels(ServerContext.getBusId()).set(avgCostMs);
        }
        watchForOnTick.mark("end_battle_tick");
        // 开服
        getZoneEntity().getOpenMgrComponent().onTick();
        watchForOnTick.mark("zone onTick");
        // 刷新任务
        try {
            getObjMgrComponent().onTick();
        } catch (Exception e) {
            LOGGER.error("BigSceneEntity getObjMgrComponent onTick fail, ", e);
        }
        watchForOnTick.mark("RefreshTask");
        // patch tick
        dataPatchTick();
        watchForOnTick.mark("dataPatchTick");
        this.isInTick = false;
        afterTick();
        watchForOnTick.mark("afterTick");

        if (totalTickNum % 300 == 0) {
            // 300次输出一次地图信息
            getResMgrComponent().showLog();
        }

        final long onTickTotalCost = watchForOnTick.getTotalCost();
        if (onTickTotalCost > MonitorConstant.BIG_SCENE_TICK_OVER_TIME) {
            LOGGER.warn("gemini_perf scene tick over time threshold:{},interval:{},{}",MonitorConstant.BIG_SCENE_TICK_OVER_TIME,onTickTotalCost, watchForOnTick.stat());
        }

        this.tickTotalCostMs += onTickTotalCost;
        if (this.totalTickNum % 60 == 0) {
            long avgCostMs = this.tickTotalCostMs / 60;
            this.tickTotalCostMs = 0;
            LOGGER.info("gemini_pref tickTotalCostMs tick avg:{}ms", avgCostMs);
            MonitorUnit.BIG_SCENE_TICK_COST.labels(ServerContext.getBusId()).set(avgCostMs);
        }
    }

    /**
     * 广播所有PlayerActor执行data patch
     */
    private void dataPatchTick() {
        try {
            if (dataPatchVersion == getDataPatchVersion()) {
                return;
            }
            LOGGER.info("BigSceneEntity dataPatchTick start oldVersion={} newVersion={}", dataPatchVersion, getDataPatchVersion());
            dataPatchVersion = getDataPatchVersion();

            // 广播所有存在的playerActor
            SsPlayer.ApplyDataPatchCmd cmd = SsPlayer.ApplyDataPatchCmd.newBuilder().setVersion(dataPatchVersion).build();
            final TypedMsg typedMsg = (new TypedMsg(SsMsgTypes.getTypeFromMsg(cmd), cmd));
            final Set<String> playerActorSet = Stream.of(ActorRole.Player.name()).collect(Collectors.toSet());
            ServerContext.getActorSystem().getRegistryValue().broadcastLocal(playerActorSet, typedMsg);

            LOGGER.info("BigSceneEntity dataPatchTick end nowVersion={}", dataPatchVersion);
        } catch (Exception e) {
            LOGGER.error("BigSceneEntity dataPatchTick onTick fail, ", e);
        }
    }

    /**
     * 需要通知在线玩家的patch 变更version！！！
     *
     * @return 最新的patch版本号
     */
    private int getDataPatchVersion() {
        return 0;
    }

    @Override
    protected void afterTick() {
        super.afterTick();
        getAoiMgrComponent().afterTick();
    }

    public void initAllComponents(GeminiStopWatch watch,
                                  List<ValueWithVersion<TcaplusDb.ClanTable.Builder>> clans,
                                  List<ValueWithVersion<TcaplusDb.ScenePlayerTable.Builder>> players,
                                  List<ValueWithVersion<TcaplusDb.SceneObjTable.Builder>> objs,
                                  List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> mails) {
        watch.mark("before start");
        for (ValueWithVersion<TcaplusDb.ClanTable.Builder> vv : clans) {
            TcaplusDb.ClanTable.Builder v = vv.value;
            try {
                ClanProp prop = ClanProp.of(v.getFullAttr(), v.getChangedAttr());
                SceneClanFactory.restoreSceneClan(this, prop);
            } catch (Exception e) {
                WechatLog.error("loadSingleClanError, clanId={}", v.getClanId(), e);
            }
        }
        watch.mark("restore clan");
        for (ValueWithVersion<TcaplusDb.ScenePlayerTable.Builder> vv : players) {
            TcaplusDb.ScenePlayerTable.Builder v = vv.value;
            try {
                ScenePlayerFactory.restoreScenePlayer(this, v.getScenePlayerId(), v.getFullAttr(), v.getChangedAttr());
            } catch (Exception e) {
                WechatLog.error("loadSingleScenePlayerFromDb player={}", v.getScenePlayerId(), e);
            }
        }
        watch.mark("restore scenePlayer");
        for (ValueWithVersion<TcaplusDb.SceneObjTable.Builder> vv : objs) {
            TcaplusDb.SceneObjTable.Builder v = vv.value;
            try {
                SceneLoadHelper.createSceneObjFromDbAttr(this, v.getFullAttr(), v.getChangedAttr());
            } catch (Exception e) {
                WechatLog.error("loadSceneObjFromDb {} error", v.getEntityId(), e);
            }
        }
        watch.mark("restore obj");
        getMailComponent().loadMails(mails);
        watch.mark("restore mail");
        // 调用所有scenePlayer的 afterAllLoad方法
        getPlayerMgrComponent().callAllPlayerAfterAllLoad();
        watch.mark("player after all load");
        // 调用所有obj的 afterAllLoad方法
        getObjMgrComponent().callAllObjAfterAllLoad();
        watch.mark("scene obj after all load");
        getZoneEntity().callAfterAllLoad();
        watch.mark("zone after all load");
        // 初始化component 大多是刷新的东西  没存盘的mapBuilding、田
        initAllComponents();
        watch.mark("init component");
        // clan数据自恢复
        getClanMgrComponent().onSceneOk();
        watch.mark("check clan");
        // check 阻挡相关
        getObjMgrComponent().checkCityCollision();
        watch.mark("check collision");
        // 通知zone
        getZoneEntity().onSceneOk();
        startTick();
        watch.mark("startTick");
        ownerActor().setBanCall();
    }

    @Override
    public MainSceneBuildingMgrComponent getBuildingMgrComponent() {
        return buildingMgrComponent;
    }

    @Override
    public BigSceneBornMgrComponent getBornMgrComponent() {
        return bornMgrComponent;
    }

    @Override
    public MainSceneObjMgrComponent getObjMgrComponent() {
        return (MainSceneObjMgrComponent) super.getObjMgrComponent();
    }

    @Override
    public MainScenePathFindComponent getPathFindMgrComponent() {
        return (MainScenePathFindComponent) super.getPathFindMgrComponent();
    }

    @Override
    public MainSceneCityMoveComponent getCityMoveComponent() {
        return (MainSceneCityMoveComponent) super.getCityMoveComponent();
    }

    @Override
    public MainSceneGridAoiMgrComponent getAoiMgrComponent() {
        return (MainSceneGridAoiMgrComponent) super.getAoiMgrComponent();
    }

    @Override
    public MileStoneMgrComponent<?> getMileStoneOrNullComponent() {
        return getZoneEntity().getMilestoneComponent();
    }

    @Override
    public MainSceneResMgrComponent getResMgrComponent() {
        return resMgrComponent;
    }

    public ZoneKingdomComponent getKingdomComponent() {
        return getZoneEntity().getKingdomComponent();
    }

    public BigSceneMailComponent getMailComponent() {
        return mailComponent;
    }

    @Override
    public BigScenePlayerMgrComponent getPlayerMgrComponent() {
        return (BigScenePlayerMgrComponent) super.getPlayerMgrComponent();
    }

    @Override
    public MainSceneQlogComponent getQlogComponent() {
        return qlogComponent;
    }

    @Override
    protected void afterDestroy() {
        super.afterDestroy();
        int num = 0;
        for (SceneObjEntity entity : getObjMgrComponent().getSceneObjEntitys()) {
            try {
                if (entity.getDbComponent().saveOnDestroy()) {
                    num++;
                }
                entity.getProp().unMarkAll();
            } catch (Exception e) {
                LOGGER.error("afterDestroy entity save failed {} ", entity, e);
            }
        }
        LOGGER.info("gemini_system shutdown SceneObj save end {}/{}", num, getObjMgrComponent().getSceneObjEntitys().size());
        num = 0;
        for (ScenePlayerEntity player : getPlayerMgrComponent().getAllScenePlayer()) {
            try {
                if (player.getDbComponent().saveOnDestroy()) {
                    num++;
                }
                player.getProp().unMarkAll();
            } catch (Exception e) {
                LOGGER.error("afterDestroy player save failed {} ", player, e);
            }
        }
        LOGGER.info("gemini_system shutdown ScenePlayer save end {}/{}", num, getPlayerMgrComponent().getAllScenePlayer().size());
        zoneEntity.deleteObj();
    }

    @Override
    public int getMaxSceneLayer(SceneObjEntity sceneObjEntity) {
        CommonEnum.SceneObjectEnum sceneObjType = sceneObjEntity.getSceneObjType();
        if (sceneObjType == null) {
            return 1;
        }
        WorldLayerService resService = ResHolder.getResService(WorldLayerService.class);
        int objectLayer = resService.getObjectLayer(sceneObjType);
        if (objectLayer != 0) {
            return objectLayer;
        }
        int regionId = MapGridDataManager.getRegionId(getMapId(), sceneObjEntity.getCurPoint());
        return resService.getObjectLayer(sceneObjType, regionId);
    }

    @Override
    public SceneClanEntity getSceneClanOrNull(long clanId) {
        if (!getClanMgrComponent().hasSceneClan(clanId)) {
            return null;
        }
        return getClanMgrComponent().getSceneClan(clanId);
    }

    @Override
    public Collection<SceneClanEntity> getAllSceneClan() {
        return getClanMgrComponent().getAllSceneClan();
    }

    @Override
    public void openMapBuilding(CommonEnum.MapBuildingType type, int level, long endTsMs) {
        getBuildingMgrComponent().openMapBuilding(type, level, endTsMs);
    }

    @Override
    public void semiOpenMapBuilding(long endTsMs) {
        getBuildingMgrComponent().semiOpenMapBuilding(endTsMs);
    }
}

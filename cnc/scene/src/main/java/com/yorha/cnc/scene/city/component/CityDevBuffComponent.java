package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjDevBuffComponent;
import com.yorha.game.gen.prop.SceneDevBuffSysProp;

/**
 * City DevBuff组件
 *
 * <AUTHOR>
 */
public class CityDevBuffComponent extends SceneObjDevBuffComponent {

    public CityDevBuffComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }

    @Override
    protected SceneDevBuffSysProp getSceneDevBuffSys() {
        return getOwner().getProp().getDevBuffSys();
    }
}

package com.yorha.cnc.scene.pathfinding.manager;

import com.yorha.cnc.scene.pathfinding.PathFindingHelper;
import com.yorha.cnc.scene.sceneObj.move.PrePath;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.common.utils.shape.Line;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.gemini.navmesh.GeminiNav;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPathFinding;
import com.yorha.proto.SsPathFinding.PathList;
import com.yorha.proto.SsPathFinding.SearchPathAsyncAns;
import com.yorha.proto.SsPathFinding.SearchPathAsyncAsk;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 主世界寻路公用
 *
 * <AUTHOR>
 */
public class MainScenePathFindingManager extends PathFindingManager {
    private static final Logger LOGGER = LogManager.getLogger(MainScenePathFindingManager.class);
    /**
     * 州个数
     */
    private int regionNum;
    /**
     * 州全联通图
     */
    private BoolMap regionMap;

    public MainScenePathFindingManager(int mapId) {
        super(mapId);
    }

    public MapTemplateDataItem getMapTemplateDataItem() {
        return ResHolder.getResService(MapSubdivisionDataService.class).getMapTemplateDataItem(getMapId());
    }

    /**
     * 初始化大世界地图数据
     */
    @Override
    protected void initNav() {
        this.regionNum = BigSceneConstants.MAP_REGION_NUM.getOrDefault(getMapId(), 0);
        // 初始化寻路图
        SceneMapDataTemplateService resService = ResHolder.getResService(SceneMapDataTemplateService.class);
        for (int i = 0; i < regionNum; i++) {
            GeminiNav nav = new GeminiNav();
            String navmeshFileName = resService.getNavmeshFileName(getMapId() + i + 9);
            try {
                nav.init(PathFindingHelper.genNavId(), navmeshFileName);
            } catch (Exception e) {
                throw new GeminiException(StringUtils.format("load from navmesh:{} fail", navmeshFileName), e);
            }
            navMap.put(i, nav);
        }
        regionMap = BoolMap.getInstance(regionNum, regionNum);
        // 初始化州连通图
        for (Pair<Integer, Integer> pair : getMapTemplateDataItem().getCrossingRegion()) {
            regionMap.setGridStatusTwoWay(pair.getFirst(), pair.getSecond(), true);
        }
    }

    public BoolMap getRegionMap() {
        return regionMap;
    }

    @Override
    public SearchPathAsyncAns searchPathAsync(SearchPathAsyncAsk msg) {
        GeminiStopWatch watch = new GeminiStopWatch("start find path");
        Point src = MsgHelper.transPoint(msg.getSrc());
        Point end = MsgHelper.transPoint(msg.getEnd());
        if (msg.getSrcRegion() == msg.getEndRegion()) {
            List<Point> pointList = findNormalPath(msg.getEntityId(), src, end, msg.getSearchTag(), watch);
            PathFindingHelper.monitSearchPathCost(src, end, watch, "normal async");
            SsPathFinding.SearchPathAsyncAns.Builder builder = SsPathFinding.SearchPathAsyncAns.newBuilder();
            SsPathFinding.PathList.Builder pathBuilder = SsPathFinding.PathList.newBuilder();
            for (Point point : pointList) {
                pathBuilder.addPoint(MsgHelper.transPoint(point));
            }
            builder.addPathList(pathBuilder);
            return builder.setCode(ErrorCode.OK.getCodeId()).build();
        }
        // 无视关卡占有 使用全州联通图
        if (GameLogicConstants.ignoreCrossOwner(msg.getSearchTag())) {
            PrePath crossingPath = findPathWithAllCrossing(msg.getEntityId(), msg.getSrcRegion(), msg.getEndRegion(), src, end, msg.getSearchTag());
            watch.mark("search end");
            SearchPathAsyncAns ans = correctNavPathAsync(crossingPath, msg.getSearchTag(), watch);
            PathFindingHelper.monitSearchPathCost(src, end, watch, "all cross async");
            return ans;
        }
        // 构建依赖关卡数据
        BoolMap crossingMap = BoolMap.getInstance(regionNum, regionNum);
        Map<Pair<Integer, Integer>, Set<Integer>> crossing = new HashMap<>();
        for (Integer partId : msg.getCrossList()) {
            List<Integer> list = getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId).getLinkRegionList();
            crossingMap.setGridStatusTwoWay(list.get(0), list.get(1), true);
            crossing.computeIfAbsent(Pair.of(list.get(0), list.get(1)), k -> new HashSet<>()).add(partId);
            crossing.computeIfAbsent(Pair.of(list.get(1), list.get(0)), k -> new HashSet<>()).add(partId);
        }
        PrePath crossingPath = findCrossingPath(1, crossingMap, crossing, msg.getSrcRegion(), msg.getEndRegion(), src, end, msg.getSearchTag());
        watch.mark("cross nav find crossing path");
        if (crossingPath == null || crossingPath.getFirstPath() == null || crossingPath.getFirstPath().isEmpty()) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("%s %s %s", msg.getEntityId(), src, end));
        }
        SearchPathAsyncAns ans = correctNavPathAsync(crossingPath, msg.getSearchTag(), watch);
        PathFindingHelper.monitSearchPathCost(src, end, watch, "clan cross async");
        return ans;
    }

    private SearchPathAsyncAns correctNavPathAsync(PrePath prePath, int searchTag, GeminiStopWatch watch) {
        /// 路径简化
        if (!GameLogicConstants.ignoreStatic(searchTag)) {
            prePath.simplePath(this);
            watch.mark("simple crossing path");
        }
        boolean isIgnoreCrossOwner = GameLogicConstants.ignoreCrossOwner(searchTag);
        SearchPathAsyncAns.Builder builder = SearchPathAsyncAns.newBuilder();
        for (int i = 0; i < prePath.prePoint.size(); i++) {
            List<Point> list = prePath.prePoint.get(i);
            if (!isIgnoreCrossOwner && i < prePath.prePoint.size() - 1) {
                builder.addPart(prePath.prePart.get(i));
            }
            PathList.Builder pathBuilder = PathList.newBuilder();
            for (Point point : list) {
                pathBuilder.addPoint(MsgHelper.transPoint(point));
            }
            builder.addPathList(pathBuilder);
        }
        return builder.setCode(ErrorCode.OK.getCodeId()).build();
    }

    @Override
    protected GeminiNav getPointNav(Point p) {
        GeminiNav nav = navMap.get(MapGridDataManager.getRegionId(getMapId(), p));
        if (nav == null) {
            return navMap.get(0);
        }
        return nav;
    }

    /**
     * 搜索跨州的路径
     */
    public PrePath findCrossingPath(long owner, BoolMap crossingMap, Map<Pair<Integer, Integer>, Set<Integer>> crossing, int srcRegion, int endRegion, Point src, Point end, int searchTag) {
        if (crossingMap == null) {
            throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
        }
        // 根据州连通图 搜索州路径
        List<List<Integer>> regionPathList = crossingMap.findAllPath(srcRegion, endRegion);
        if (regionPathList.isEmpty()) {
            throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
        }
        PrePath bestPath = null;
        // 多条可通行关卡路径 选最短
        for (List<Integer> regionPath : regionPathList) {
            PrePath prePath = calRegionPath(owner, crossing, regionPath, srcRegion, endRegion, src, end, searchTag);
            if (prePath == null) {
                continue;
            }
            if (bestPath == null || bestPath.distance > prePath.distance) {
                bestPath = prePath;
            }
        }
        return bestPath;
    }

    /**
     * 根据州通行路径和拥有的关卡 选出最短的返回
     */
    private PrePath calRegionPath(long owner, Map<Pair<Integer, Integer>, Set<Integer>> crossing, List<Integer> regionPath, int startRegion, int endRegion, Point src, Point end, int searchTag) {
        List<PrePath> prePartPathList = new ArrayList<>();
        // 第一段 起点到关卡
        Set<Integer> crossPartIdSet = crossing.get(Pair.of(regionPath.get(0), regionPath.get(1)));
        // 竟然不通？？
        if (crossPartIdSet == null || crossPartIdSet.isEmpty()) {
            LOGGER.info("{} calRegionPath failed 1 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, 0);
            return null;
        }
        for (int partId : crossPartIdSet) {
            List<Point> pathWithCross = findPathWithCross(src, null, startRegion, partId, searchTag);
            // 1个点是有可能的 起点就在城门线上
            if (pathWithCross.size() < 1) {
                continue;
            }
            PrePath firstPath = new PrePath();
            firstPath.distance = getPathDistance(pathWithCross);
            firstPath.prePoint.add(pathWithCross);
            firstPath.prePart.add(partId);
            prePartPathList.add(firstPath);
        }
        if (prePartPathList.isEmpty()) {
            LOGGER.info("{} calRegionPath failed 2 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, 0);
            return null;
        }
        // 后面开始  关卡到关卡
        for (int i = 2; i < regionPath.size(); i++) {
            crossPartIdSet = crossing.get(Pair.of(regionPath.get(i - 1), regionPath.get(i)));
            // 竟然不通？？
            if (crossPartIdSet == null || crossPartIdSet.isEmpty()) {
                LOGGER.info("{} calRegionPath failed 3 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, 0);
                return null;
            }
            if (crossPartIdSet.size() == 1) {
                // 只有一个 选出最优路径  其他都扔掉
                PrePath best = null;
                for (int partId : crossPartIdSet) {
                    for (PrePath prePath : prePartPathList) {
                        // 部分优化 如果没加就超过最优了 不算了
                        if (best != null && best.distance < prePath.distance) {
                            continue;
                        }
                        List<Point> pathWithCross = findPathBetweenTwoCross(regionPath.get(i - 1), prePath.getLastPartId(), partId, searchTag);
                        if (pathWithCross.size() < 2) {
                            LOGGER.error("{} calRegionPath error 4 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, i);
                            continue;
                        }
                        prePath.distance += getPathDistance(pathWithCross);
                        if (best == null || best.distance > prePath.distance) {
                            best = prePath;
                        } else {
                            continue;
                        }
                        prePath.prePoint.add(pathWithCross);
                        prePath.prePart.add(partId);
                    }
                }
                // 都找不到路
                if (best == null) {
                    LOGGER.error("{} calRegionPath error 5 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, i);
                    return null;
                }
                prePartPathList.clear();
                prePartPathList.add(best);
            } else {
                // 多个的时候 需要都算一遍
                List<PrePath> curPartPathList = new ArrayList<>();
                for (int partId : crossPartIdSet) {
                    for (PrePath prePath : prePartPathList) {
                        List<Point> pathWithCross = findPathBetweenTwoCross(regionPath.get(i - 1), prePath.getLastPartId(), partId, searchTag);
                        if (pathWithCross.size() < 2) {
                            LOGGER.error("{} calRegionPath error 6 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, i);
                            continue;
                        }
                        PrePath copy = prePath.copy();
                        copy.distance += getPathDistance(pathWithCross);
                        copy.prePoint.add(pathWithCross);
                        copy.prePart.add(partId);
                        curPartPathList.add(copy);
                    }
                }
                // 都找不到路
                if (curPartPathList.isEmpty()) {
                    LOGGER.error("{} calRegionPath error 7 {} path:{} src: {} end: {} index:{}", owner, crossing, regionPath, src, end, i);
                    return null;
                }
                prePartPathList.clear();
                prePartPathList.addAll(curPartPathList);
            }
        }
        // 最后一段路加上
        PrePath best = null;
        for (PrePath prePath : prePartPathList) {
            // 部分优化 如果没加上的时候就超过最优了 不算了
            if (best != null && best.distance < prePath.distance) {
                continue;
            }
            List<Point> pathWithCross = findPathWithCross(null, end, endRegion, prePath.getLastPartId(), searchTag);
            // 1个点是有可能的 终点就在城门线上
            if (pathWithCross.size() < 1) {
                continue;
            }
            prePath.distance += getPathDistance(pathWithCross);
            if (best == null || best.distance > prePath.distance) {
                best = prePath;
            } else {
                continue;
            }
            prePath.prePoint.add(pathWithCross);
        }
        return best;
    }

    /**
     * 使用全连通图寻路
     */
    public PrePath findPathWithAllCrossing(long entityId, int srcRegion, int endRegion, Point src, Point end, int searchTag) {
        List<Integer> regionPath = regionMap.findShortestPath(srcRegion, endRegion);
        if (regionPath.isEmpty()) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("1 %s %s %s", entityId, src, end));
        }
        PrePath prePath = new PrePath();
        int lastPartId = -1;
        Point comparePoint = src;
        for (int i = 0; i < regionPath.size() - 1; i++) {
            List<Integer> crossPartId = getMapTemplateDataItem().getRegionPartIdList(regionPath.get(i), regionPath.get(i + 1));
            // 竟然不通？？
            if (crossPartId == null || crossPartId.isEmpty()) {
                throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("2 %s %s %s", entityId, src, end));
            }
            // 简单点 找个最近的
            int curPartId = 0;
            double minDistance = -1;
            Point choosePoint = null;
            for (int partId : crossPartId) {
                RegionalAreaSettingTemplate template = getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
                Point target = Point.valueOf(template.getPosX(), template.getPosY());
                double distance = Point.calDisBetweenTwoPoint(target, comparePoint);
                if (minDistance == -1 || distance < minDistance) {
                    minDistance = distance;
                    curPartId = partId;
                    choosePoint = target;
                }
            }
            if (curPartId == 0) {
                throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("3 %s %s %s", entityId, src, end));
            }
            // 如果是两段式拼接 那直接加上点就行了 肯定是无视静态阻挡的
            if ((searchTag & GameLogicConstants.CROSS_TYPE_INSERT_POINT) > 0) {
                if (prePath.isNoPath()) {
                    List<Point> path = new ArrayList<>();
                    path.add(comparePoint);
                    prePath.prePoint.add(path);
                } else if (!prePath.getLastPoint().equals(comparePoint)) {
                    prePath.getLastPath().add(comparePoint);
                }
            } else {
                // 三段式拼接
                List<Point> pathWithCross;
                if (i == 0) {
                    // 首段路径
                    pathWithCross = findPathWithCross(src, null, srcRegion, curPartId, searchTag);
                    // 1个点是有可能的 终点就在城门线上
                    if (pathWithCross.size() < 1) {
                        throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("4 %s %s %s", entityId, src, end));
                    }
                } else {
                    // 中间路径
                    pathWithCross = findPathBetweenTwoCross(regionPath.get(i), lastPartId, curPartId, searchTag);
                    if (pathWithCross.size() < 2) {
                        throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("5 %s %s %s", entityId, src, end));
                    }
                }
                prePath.prePoint.add(pathWithCross);
            }
            lastPartId = curPartId;
            comparePoint = choosePoint;
        }
        // 最后一段接上
        // 如果是两段式拼接 那直接加上点就行了 肯定是无视静态阻挡的
        if ((searchTag & GameLogicConstants.CROSS_TYPE_INSERT_POINT) > 0) {
            if (prePath.isNoPath()) {
                List<Point> path = new ArrayList<>();
                path.add(comparePoint);
                prePath.prePoint.add(path);
            } else if (!prePath.getLastPoint().equals(comparePoint)) {
                prePath.getLastPath().add(comparePoint);
            }
            prePath.getLastPath().add(end);
            return prePath;
        }
        // 三段式 接上
        List<Point> pathWithCross = findPathWithCross(null, end, endRegion, lastPartId, searchTag);
        // 1个点是有可能的 终点就在城门线上
        if (pathWithCross.size() < 1) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("6 %s %s %s", entityId, src, end));
        }
        prePath.prePoint.add(pathWithCross);
        return prePath;
    }

    /**
     * 首尾段计算  src和end有一个是null
     */
    public List<Point> findPathWithCross(Point src, Point end, int regionId, int crossPartId, int searchTag) {
        if (end == null) {
            end = src;
        }
        // 计算交点
        Point crossPoint = getMapTemplateDataItem().getPointWithSrcAndCross(end, crossPartId, regionId);
        if (crossPoint == null) {
            return Collections.emptyList();
        }
        if (GameLogicConstants.ignoreStatic(searchTag)) {
            ArrayList<Point> path = new ArrayList<>();
            if (src != null) {
                path.add(src);
                path.add(crossPoint);
            } else {
                path.add(crossPoint);
                path.add(end);
            }
            return path;
        }
        if (src != null) {
            return findNavPath(src, crossPoint, regionId);
        } else {
            return findNavPath(crossPoint, end, regionId);
        }
    }

    /**
     * 中间段计算 从一个关卡到另一个关卡
     */
    public List<Point> findPathBetweenTwoCross(int regionId, int crossPart1, int crossPart2, int searchTag) {
        Line line = getMapTemplateDataItem().getSrcAndEndBetweenCross(regionId, crossPart1, crossPart2);
        if (line == null) {
            return Collections.emptyList();
        }
        if (GameLogicConstants.ignoreStatic(searchTag)) {
            ArrayList<Point> path = new ArrayList<>();
            path.add(line.getSrcPoint());
            path.add(line.getEndPoint());
            return path;
        }
        return findNavPath(line.getSrcPoint(), line.getEndPoint(), regionId);
    }

    /**
     * 计算一段路径的距离
     */
    public static double getPathDistance(List<Point> path) {
        double dis = 0;
        for (int i = 1; i < path.size(); i++) {
            dis += Point.calDisBetweenTwoPoint(path.get(i - 1), path.get(i));
        }
        return dis;
    }

    /**
     * navmesh有时会给出奇怪弯曲路线 简化
     *
     * @param points 路点list
     * @return 优化后的路线
     */
    public static List<Point> simplePath(FindNavPathApi api, List<Point> points) {
        List<Point> ret = new ArrayList<>();
        // 一层循环 O(N)有漏洞
//        Point cur = points.get(0);
//        ret.add(cur);
//
//        for (int i = 2; i < points.size(); i++) {
//            Point next = points.get(i);
//            // 如果有阻挡 就正常加到返回列表去
//            if (!NavmeshApi.isNoCollision(nav, cur, next)) {
//                ret.add(points.get(i - 1));
//                cur = points.get(i - 1);
//            }
//        }
//        ret.add(points.get(points.size() - 1));
        // 两层 O(N2) 优化全了 (可能非最优，尽可能优)
        // 起始点总要加回的
        ret.add(points.get(0));
        for (int i = 0; i < points.size() - 1; i++) {
            Point cur = points.get(i);
            int next = -1;
            // 向后遍历 寻找可直达点
            for (int j = i + 2; j < points.size(); j++) {
                Point now = points.get(j);
                if (api.isNoCollision(cur, now)) {
                    next = j;
                }
            }
            if (next != -1) {
                ret.add(points.get(next));
                i = next - 1;
            } else {
                // 找不到 加入下一个点
                ret.add(points.get(i + 1));
            }
        }
        if (points.size() != ret.size()) {
            LOGGER.debug("simplePath finished. old:{} new:{}", points, ret);
        }
        return ret;
    }

    public List<Point> findNavPath(Point src, Point end, int regionId) {
        return NavmeshApi.searchPath(navMap.get(regionId), src, end);
    }
}

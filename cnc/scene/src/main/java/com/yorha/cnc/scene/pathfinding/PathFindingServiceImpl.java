package com.yorha.cnc.scene.pathfinding;

import com.yorha.common.actor.PathFindingService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsPathFinding.SearchPathAsyncAns;
import com.yorha.proto.SsPathFinding.SearchPathAsyncAsk;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PathFindingServiceImpl implements PathFindingService {
    private static final Logger LOGGER = LogManager.getLogger(PathFindingServiceImpl.class);

    private final PathFindingActor owner;

    public PathFindingServiceImpl(PathFindingActor owner) {
        this.owner = owner;
    }

    @Override
    public void handleSearchPathAsyncAsk(SearchPathAsyncAsk ask) {
        try {
            SearchPathAsyncAns ans = owner.getManager().searchPathAsync(ask);
            owner.answer(ans);
        } catch (GeminiException e) {
            owner.answer(SearchPathAsyncAns.newBuilder().setCode(e.getCodeId()).build());
        } catch (Exception e) {
            LOGGER.warn("SearchPathAsyncAsk error", e);
            owner.answer(SearchPathAsyncAns.newBuilder().setCode(ErrorCode.FAILED.getCodeId()).build());
        }
    }
}

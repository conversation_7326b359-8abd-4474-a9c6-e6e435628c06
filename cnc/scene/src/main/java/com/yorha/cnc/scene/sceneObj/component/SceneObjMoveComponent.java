package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.MoveTargetChangeEvent;
import com.yorha.cnc.scene.event.TickMoveNoPathEvent;
import com.yorha.cnc.scene.event.city.CityAscendEvent;
import com.yorha.cnc.scene.event.city.CityMoveEvent;
import com.yorha.cnc.scene.event.mapbuilding.ChangeOccupierEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.move.IMoveArriveHandler;
import com.yorha.cnc.scene.sceneObj.move.IMoveTargetLoseHandler;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneObj.move.SearchPathAsyncResult;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.vector.Vector2f;
import com.yorha.game.gen.prop.MoveProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.MapAreaType;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
public abstract class SceneObjMoveComponent extends SceneObjComponent<SceneObjEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjMoveComponent.class);

    /**
     * debug模式设定行军移动速度
     */
    protected int debugMoveSpeedRatio = 0;
    /**
     * 移动速度
     */
    protected int moveSpeed = 0;
    /**
     * 当前移动相关数据
     */
    private MoveData curMoveData;
    /**
     * 到达回调
     * 丢失回调
     * 移动类型
     */
    private IMoveArriveHandler arriveHandler;
    private IMoveTargetLoseHandler moveTargetLoseHandler;
    private TroopInteractionType interactionType;
    /**
     * 移动目标丢失事件监听器
     */
    private EventListener moveTargetLoseEventListener;
    /**
     * 移动目标数据变化监听器
     */
    private EventListener moveTargetChangeEventListener;
    private MoveData createPrePath = null;
    /**
     * 当前异步寻路id 异步回来时不一致就说明失效了
     */
    private int curSearchPathAsyncId;

    public SceneObjMoveComponent(SceneObjEntity owner) {
        super(owner);
    }

    public void setCreatePrePath(MoveData createPrePath) {
        this.createPrePath = createPrePath;
    }

    public void clearCreatePrePath() {
        createPrePath = null;
    }

    @Override
    public void init() {
        getOwner().getEventDispatcher().addEventListener((DieEvent e) -> cancelMoveSubEventListener(), DieEvent.class);
    }

    @Override
    public void postInit() {
        initMoveSpeedFromTroopProp();
    }

    public boolean isMoving() {
        return curMoveData != null;
    }

    public boolean isParallelMoving() {
        return curMoveData != null && curMoveData.isParallelMoving();
    }

    public int getRealMoveSpeed(int searchTag, Point endPoint) {
        if (GameLogicConstants.isBesiegeMove(searchTag)) {
            return ResHolder.getConsts(ConstTemplate.class).getSiegeMoveSpeed();
        }
        if (debugMoveSpeedRatio > 0) {
            return moveSpeed * debugMoveSpeedRatio;
        }
        return moveSpeed;
    }

    public void debugSetMoveSpeedRatio(int ratio) {
        LOGGER.warn("debugSetMoveSpeed! ratio={} old={}", ratio, moveSpeed);
        this.debugMoveSpeedRatio = ratio;
    }

    /**
     * 获取当前位置
     */
    public Point getCurPoint() {
        return getOwner().getCurPoint();
    }

    /**
     * 通过士兵属性 初始化 移速
     */
    protected void initMoveSpeedFromTroopProp() {
        int curMinSpeed = -1;
        for (SoldierProp soldierPropData : getTroopProp().getTroop().values()) {
            SoldierTypeTemplate soldierTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldierPropData.getSoldierId());
            int newMoveSpeed = soldierTemplate.getMoveSpeed();
            if (curMinSpeed == -1 || newMoveSpeed < curMinSpeed) {
                // 获取士兵中的最低移速
                curMinSpeed = newMoveSpeed;
            }
        }
        if (getOwner().getEntityType() == EntityType.ET_Monster) {
            // 拿到野怪的移速系数
            final float moveSpeedParam = ((MonsterEntity) getOwner()).getTemplate().getMoveSpeedParam();
            // FIXME(furson): 如果moveSpeedParam配的太大爆炸了怎么办？而且上面也没保证curMinSpeed更新了
            moveSpeed = (int) (curMinSpeed * moveSpeedParam);
        } else {
            moveSpeed = curMinSpeed;
        }
    }

    /**
     * 返回寻路tag  无视阻挡/只静态/只动态/所有阻挡
     */
    protected int getMoveSearchPathTag(boolean isToTarget) {
        return isToTarget ? GameLogicConstants.NORMAL_MOVE_TARGET : GameLogicConstants.NORMAL_MOVE_POINT;
    }

    public void moveToPointAsyncIgnoreException(Point point, IMoveArriveHandler arriveHandler) {
        moveToPointAsync(point, arriveHandler, getOwner().getScene().now(), null);
    }

    /**
     * 异步寻路
     *
     * @param point         终点
     * @param arriveHandler 到达回调
     * @param startTime     移动开始时间戳
     * @param backHandler   寻路结束回调  失败可能要发送错误码
     */
    public void moveToPointAsync(Point point, IMoveArriveHandler arriveHandler, long startTime, Consumer<Integer> backHandler) {
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(point, getOwner().getScene().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        if (Objects.equals(getCurPoint(), point)) {
            // 目的地与当前位置一样
            throw new GeminiException(ErrorCode.MOVE_SRC_EDN_EQUALS);
        }
        if (!canMove()) {
            LOGGER.debug("{} can not move to point={}, cause frozen.", getOwner(), point);
            return;
        }
        stopMove();
        int searchTag = getMoveSearchPathTag(false);
        setArriveHandler(arriveHandler);
        innerSearchPointAsync(point, startTime, 0, searchTag, backHandler, false);
    }

    public int getCurSearchPathAsyncId() {
        return curSearchPathAsyncId;
    }

    /**
     * 同步接口  移动到某个目标    返回是否直接到达
     *
     * @param targetEntity    目标entity
     * @param interactionType 交互类型 战斗为null
     * @param arriveHandler   到达回调
     * @param loseHandler     对方消失回调
     */
    public boolean moveToTarget(SceneObjEntity targetEntity, TroopInteractionType interactionType, IMoveArriveHandler arriveHandler, IMoveTargetLoseHandler loseHandler) {
        return moveToTarget(targetEntity, interactionType, arriveHandler, loseHandler, false, null);
    }

    /**
     * 异步接口  移动到某个目标   返回是否直接到达
     */
    public boolean moveToTargetAsync(SceneObjEntity targetEntity, TroopInteractionType interactionType, IMoveArriveHandler arriveHandler, IMoveTargetLoseHandler loseHandler, Consumer<Integer> backHandler) {
        return moveToTarget(targetEntity, interactionType, arriveHandler, loseHandler, true, backHandler);
    }

    /**
     * 内部接口 移动到某个目标
     * backHandler  异步寻路结束回调  成功或失败
     * ret 是否是立即到达
     */
    protected boolean moveToTarget(SceneObjEntity targetEntity, TroopInteractionType interactionType, IMoveArriveHandler arriveHandler, IMoveTargetLoseHandler loseHandler, boolean isAsync, Consumer<Integer> backHandler) {
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(targetEntity.getCurPoint(), getOwner().getScene().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        if (!canMove()) {
            LOGGER.debug("{} can not move to target", getOwner());
            return false;
        }
        int searchTag = getMoveSearchPathTag(true);
        stopMove();
        // 没传的，说明其他地方已经处理过或者不关心
        if (loseHandler != null) {
            // 注册目标消失相关事件监听
            moveTargetLoseEventListener = targetEntity.getEventDispatcher().addMultiEventListener(
                    this::onTargetLose,
                    DieEvent.class,
                    DeleteEvent.class,
                    CityMoveEvent.class,
                    CityAscendEvent.class,
                    ChangeOccupierEvent.class);
        }
        setMoveTarget(targetEntity);
        this.moveTargetLoseHandler = loseHandler;
        this.interactionType = interactionType;
        setArriveHandler(arriveHandler);

        Point targetPoint = targetEntity.getTransformComponent().getBeBattlePoint(getCurPoint(), getOwner().getClanId(), searchTag);
        // 计算到达距离
        int chaseDistance = getMoveToTargetDistance(targetEntity, interactionType);
        if (targetEntity.getEntityType() == EntityType.ET_MapBuilding) {
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) targetEntity;
            if (mapBuildingEntity.getAreaType() == MapAreaType.CROSSING) {
                // 移动到关隘实际会转化为移动到城门点，所以不用 交互圈修正了
                chaseDistance = 0;
            }
        }
        // 移动/直接到达
        double v = Point.calDisBetweenTwoPoint(getCurPoint(), targetPoint);
        if (v > chaseDistance) {
            try {
                if (isAsync) {
                    innerSearchPointAsync(targetPoint, getOwner().getScene().now(), chaseDistance, searchTag, backHandler, true);
                } else {
                    innerMoveToPoint(targetPoint, getOwner().getScene().now(), chaseDistance, true);
                }
            } catch (Exception e) {
                // 因为要清理上面设置过得handler及目标
                stopMove();
                throw e;
            }
            return false;
        } else {
            clearCreatePrePath();
            arriveEnd();
            return true;
        }
    }

    private void innerSearchPointAsync(Point point, long startTime, int fixDis, int searchTag, Consumer<Integer> backHandler, boolean needStop) {
        // 无视所有直接拼接的
        if (GameLogicConstants.ignoreAll(searchTag)) {
            List<Point> pointList = getOwner().getScene().getPathFindMgrComponent().buildIgnoreAllPath(getCurPoint(), point, fixDis);
            onMoveDataFind(new MoveData(pointList, searchTag), startTime, false);
            return;
        }
        // 有预寻路数据的
        if (createPrePath != null) {
            if (fixDis <= 0) {
                onMoveDataFind(createPrePath, startTime, false);
                clearCreatePrePath();
                return;
            }
            // 预寻路时没有追击修正的 因为要等army创建出来才能得到修正距离
            createPrePath.correctTheLastPath(getOwner().getScene().getPathFindMgrComponent(), fixDis, point);
            onMoveDataFind(createPrePath, startTime, false);
            clearCreatePrePath();
            return;
        }
        // 发起异步寻路
        final int curId = ++curSearchPathAsyncId;
        final Point fPoint = point.getDeepCopy();
        MoveData ret = getOwner().getScene().getPathFindMgrComponent().searchPathAsync(
                getOwner(),
                getCurPoint(),
                point,
                searchTag,
                fixDis,
                (SearchPathAsyncResult result) -> onAsyncSearchAnswer(result, curId, startTime, fPoint, searchTag, fixDis, backHandler, needStop));
        // 是即时返回的   无视静态阻挡 大世界且同州
        if (ret != null) {
            onMoveDataFind(ret, startTime, false);
            if (backHandler != null) {
                backHandler.accept(ErrorCode.OK.getCodeId());
            }
        }
    }

    /**
     * 异步寻路回复结果
     */
    private void onAsyncSearchAnswer(SearchPathAsyncResult result, int curId, long startTime, Point fPoint, int searchTag, int fixDis, Consumer<Integer> backHandler, boolean needStop) {
        // 已经失效了 什么都不要做
        if (curId != getCurSearchPathAsyncId()) {
            return;
        }
        // 已经回收了 什么都不要做
        if (getOwner().isDestroy()) {
            return;
        }
        if (ErrorCode.isOK(result.getCode())) {
            MoveData moveData = result.genMoveData(getOwner().getScene().getPathFindMgrComponent(), searchTag, fixDis, fPoint);
            if (moveData != null) {
                onMoveDataFind(moveData, startTime, true);
            } else if (needStop) {
                // 因为要清理上面设置过得handler及目标
                stopMove();
            }
        } else if (needStop) {
            // 因为要清理上面设置过得handler及目标
            stopMove();
        }
        if (backHandler != null) {
            backHandler.accept(result.getCode());
        }
    }

    /**
     * move data 找到了
     *
     * @param isAsync 是否异步回来的
     */
    protected void onMoveDataFind(MoveData moveData, long startTime, boolean isAsync) {
        if (!canMove()) {
            return;
        }
        if (startTime == 0) {
            startTime = getOwner().getScene().now();
        }
        moveData.startMove(startTime, getOwner());
        setMoveData(moveData);
        onStartMove();
    }

    /**
     * 内部接口，只是移动
     *
     * @param point     目标点
     * @param startTime 开始时间
     * @param fixDis    追击圈距离
     */
    protected void innerMoveToPoint(Point point, long startTime, int fixDis, boolean isToTarget) {
        // 使异步的寻路失效
        curSearchPathAsyncId++;
        int searchTag = getMoveSearchPathTag(isToTarget);
        // 同步寻路
        MoveData moveData = getOwner().getScene().getPathFindMgrComponent().searchPath(getOwner(), createPrePath, getCurPoint(), point, searchTag, fixDis);
        onMoveDataFind(moveData, startTime, false);
        clearCreatePrePath();
    }


    /**
     * 不执行到达回调的内部寻路  目前围攻专用
     */
    private void innerMoveNoExecuteArrive(Point point, int searchTag, long startTime) {
        // 为了日落峡谷那边无视地形用的
        int moveSearchPathTag = getMoveSearchPathTag(true);
        // 如果是无视所有的  那优先级更高
        if (GameLogicConstants.ignoreAll(moveSearchPathTag)) {
            searchTag = moveSearchPathTag | GameLogicConstants.BESIEGE_TAG;
        }
        try {
            innerSearchPointAsync(point, startTime, 0, searchTag, null, false);
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                LOGGER.error("{} innerMoveNoExecuteArrive error {}", getOwner(), point, e);
            }
        }
    }

    /**
     * 事件触发 目标丢失
     */
    private void onTargetLose(IEvent e) {
        LOGGER.info("{} move target lose. reason: {} ", getOwner(), e);
        // 使得这期间触发的寻路失效  因为移动到目标会先监听目标事件，再发起异步寻路
        curSearchPathAsyncId++;
        moveTargetLoseEventListener = null;
        // 使用缓存丢失回调  因为可能在回调里发起了新的寻路。。。
        IMoveTargetLoseHandler moveTargetLoseHandlerTemp = moveTargetLoseHandler;
        stopMove();
        if (moveTargetLoseHandlerTemp != null) {
            moveTargetLoseHandlerTemp.onTargetLose(e);
        }
    }

    /**
     * 到达终点
     */
    private void arriveEnd() {
        int searchTag = 0;
        if (curMoveData != null) {
            searchTag = curMoveData.getSearchTag();
        }
        setMoveData(null);
        onEndMove();
        IMoveArriveHandler arriveHandlerTemp = this.arriveHandler;

        // 交互类型清掉移动数据 不追了
        if (interactionType != null) {
            clearMoveCache();
        }
        // 围攻不执行
        if (arriveHandlerTemp != null && !GameLogicConstants.isBesiegeMove(searchTag)) {
            try {
                arriveHandlerTemp.onArrive();
            } catch (Exception e) {
                LOGGER.error("SceneObjMoveComponent arriveEnd arriveHandlerTemp.onArrive fail, ", e);
            }
        }
    }

    protected void setMoveTarget(SceneObjEntity targetEntity) {
        cancelMoveTargetChangeEvent();
        long targetId = getCurChaseTargetId();
        if (targetEntity == null) {
            if (targetId != 0) {
                SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
                if (target != null) {
                    target.getTransformComponent().exitBesiege(getEntityId());
                }
            }
            getMoveProp().setTargetId(0).setTargetClanId(0);
            getOwner().getEventDispatcher().dispatch(new MoveTargetChangeEvent(getEntityId(), 0));
            getOwner().getScene().getTickMgrComponent().unRegisterRefreshChase(getEntityId());
            return;
        }
        getMoveProp().setTargetId(targetEntity.getEntityId()).setTargetClanId(targetEntity.getClanId());
        getOwner().getEventDispatcher().dispatch(new MoveTargetChangeEvent(getEntityId(), targetEntity.getEntityId()));
        moveTargetChangeEventListener = targetEntity.getEventDispatcher().addEventListenerRepeat(this::onMoveTargetChange, ClanChangeEvent.class);
        if (targetEntity.getMoveComponent() != null) {
            getOwner().getScene().getTickMgrComponent().registerRefreshChase(this);
        }
    }

    private void cancelMoveTargetChangeEvent() {
        if (moveTargetChangeEventListener == null) {
            return;
        }
        moveTargetChangeEventListener.cancel();
        moveTargetChangeEventListener = null;
    }


    private void onMoveTargetChange(ClanChangeEvent e) {
        if (getCurChaseTargetId() != e.getEntityId()) {
            LOGGER.error("{} onMoveTargetChange curTarget: {} event: {}", getOwner(), getCurChaseTargetId(), e.getEntityId());
            return;
        }
        getMoveProp().setTargetClanId(e.getClanId());
    }

    /**
     * 移动到围攻点
     */
    public void occupyAndMoveToBesiegePoint(SceneObjEntity target, boolean needMove, Predicate<Point> besiegePredicate) {
        Point besiegePoint;
        try {
            besiegePoint = target.getTransformComponent().occupyAndGetBesiegePoint(getOwner());
        } catch (Exception e) {
            LOGGER.error("{} target: {} moveToBesiegePoint error ", getOwner(), target, e);
            return;
        }
        if (!needMove) {
            return;
        }
        if (besiegePredicate != null && !besiegePredicate.test(besiegePoint)) {
            LOGGER.debug("{} try move to besiege {} fail", getOwner(), besiegePoint);
            return;
        }
        LOGGER.debug("{} get besiegePoint:{}", getOwner(), besiegePoint);
        if (besiegePoint == null || getCurPoint().roughlyEquals(besiegePoint, GameLogicConstants.ACCURACY_ERROR_SIZE)) {
            // 围攻位置就是当前点
            return;
        }
        innerMoveNoExecuteArrive(besiegePoint, GameLogicConstants.BESIEGE_MOVE, getOwner().getScene().now());
        // 目标正在动 那要加到check并行追击队列
        if (target.getMoveComponent() != null && target.getMoveComponent().isMoving()) {
            target.getMoveComponent().chaseCheckList.add(getEntityId());
        }
    }

    /**
     * 驻扎
     */
    public void stopMove() {
        // 停止需要让正在寻路的请求失效
        curSearchPathAsyncId++;
        // 结算下正在进行的移动 更新位置
        calPointWhenStopMove();
        boolean isMoving = isMoving();
        // 清理各种移动缓存
        clearMoveCache();
        // 移动结束
        if (!isMoving) {
            return;
        }
        onEndMove();
    }

    /**
     * 清理各种移动缓存
     */
    private void clearMoveCache() {
        interactionType = null;
        // 清理移动回调和事件监听器
        setArriveHandler(null);
        moveTargetLoseHandler = null;
        cancelMoveSubEventListener();
        // 清空移动数据
        setMoveData(null);
        setMoveTarget(null);
    }

    private void calPointWhenStopMove() {
        if (!isMoving()) {
            return;
        }
        // 计算当前最新位置
        Point newPoint = curMoveData.calCurPoint(getOwner().getScene().now());
        // 能进来说明上一次tick还不是在通过关卡状态  如果刚好此时算出来在关卡中 那当做不用走
        // 需要停下的话 就用
        if (getCurCrossingPart() != 0) {
            getOwner().getTransformComponent().changePoint(getCurPoint(), true);
            return;
        }
        getOwner().getTransformComponent().changePoint(newPoint, true);
    }


    /**
     * 检查并刷新关卡条件 返回是否不能继续行军了
     */
    protected boolean checkCrossing() {
        // 不是需要过关卡的路径 直接返回
        if (!isCrossingPath()) {
            return false;
        }
        // 寻路时就无视关卡归属了 直接返回可以通过
        if (GameLogicConstants.ignoreCrossOwner(curMoveData.getSearchTag())) {
            return false;
        }
        int curCrossingPart = getCurCrossingPart();
        // 没有要过关卡
        if (curCrossingPart == 0) {
            return false;
        }
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(getOwner().getClanId());
        if (getOwner().getClanId() == 0) {
            onPathLoseWhenMoveTick(curCrossingPart, ErrorCode.MOVE_CROSS_LOSE);
            return true;
        }
        ErrorCode code = sceneClan.getCrossComponent().canPassCross(getOwner(), MapGridDataManager.getRegionId(getOwner().getScene().getMapId(), getCurPoint()), curCrossingPart);
        if (code.isOk()) {
            return false;
        }
        onPathLoseWhenMoveTick(curCrossingPart, code);
        return true;
    }

    protected boolean isCrossingPath() {
        return curMoveData.isCrossingPath();
    }

    /**
     * 刷新计算当前位置点  并检测是否到达   tick专用
     */
    public void onTick() {
        if (!isMoving()) {
            return;
        }
        long curTime = getOwner().getScene().now();
        // 计算当前tick最新位置
        Point newPoint = null;
        try {
            newPoint = curMoveData.calCurPoint(curTime);
        } catch (Exception e) {
            LOGGER.error(StringUtils.format("{} calCurPoint error moveData:{}", getOwner(), curMoveData), e);
            clearMoveCache();
            onEndMove();
            return;
        }
        // check关卡情况  返回是否不能继续行军
        if (checkCrossing()) {
            return;
        }
        getOwner().getTransformComponent().changePoint(newPoint, false);
        if (curMoveData.isArriveEnd(curTime, newPoint)) {
            // 到达终点
            arriveEnd();
            return;
        }
        tickParallelChaseList();
        // 检查是否需要刷新 动态阻挡发生变化
        int searchTag = curMoveData.getSearchTag();
        // 同行追击模式不用变 目标变的时候自然会刷
        if (isParallelMoving()) {
            return;
        }
        PathFindMgrComponent pathComponent = getOwner().getScene().getPathFindMgrComponent();
        int modelRadius = getOwner().getTransformComponent().getModelRadius();
        if (GameLogicConstants.dynamicPathCorrect(searchTag) && pathComponent.checkNeedRefreshPath(newPoint, modelRadius, curMoveData.getMoveStartTime())) {
            innerSearchPointAsync(curMoveData.getEndPoint(), getOwner().getScene().now(), 0, searchTag, this::onChaseSearchPathEnd, true);
        }
    }

    public Point getEndPoint() {
        if (!isMoving()) {
            return null;
        }
        return curMoveData.getEndPoint();
    }

    /**
     * 获取当前所在路段的关卡  没有为0
     */
    public int getCurCrossingPart() {
        if (curMoveData == null) {
            return 0;
        }
        return curMoveData.getCurCrossingPart();
    }

    /**
     * 追击目标位置/追赶距离 发生变化 刷新一条新的追赶路径
     */
    public void tryRefreshChasePath() {
        // 不能移动就不处理
        if (!canMove()) {
            return;
        }
        long moveToTargetId = getCurChaseTargetId();
        if (moveToTargetId == 0) {
            return;
        }
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(moveToTargetId);
        if (targetEntity == null) {
            stopMove();
            // 应该不存在这种情况   估计有人MoveToTarget的时候没传loseHandler或者外面没有监听目标丢失的情况  打个log
            LOGGER.warn("{} tryRefreshChasePath but target lose {}", getOwner(), moveToTargetId);
            return;
        }
        // 可能因为自己的模型圈半径进来的 所以算这一次就够了 从追击列表移除
        if (targetEntity.getMoveComponent() == null) {
            getOwner().getScene().getTickMgrComponent().unRegisterRefreshChase(getEntityId());
        }
        int chaseDistance = getChaseDistance(targetEntity);
        if (chaseDistance <= 0) {
            return;
        }
        int tag = getMoveSearchPathTag(true);
        if (curMoveData != null) {
            tag = curMoveData.getSearchTag();
        }
        Point targetPoint = targetEntity.getTransformComponent().getBeBattlePoint(getCurPoint(), getOwner().getClanId(), tag);
        int searchTag = getMoveSearchPathTag(true);
        // 关卡的话 要补偿时间
        long startTsMs = getOwner().getScene().now();
        if (curMoveData != null && getCurCrossingPart() != 0) {
            startTsMs = curMoveData.getMoveCurTime();
        }
        try {
            innerSearchPointAsync(targetPoint, startTsMs, chaseDistance, searchTag, this::onChaseSearchPathEnd, false);
        } catch (GeminiException e) {
            onChaseSearchPathEnd(e.getCodeId());
        } catch (Exception e) {
            onChaseSearchPathEnd(ErrorCode.FAILED.getCodeId());
        }
    }

    /**
     * tick中检查发现原有路径不行了
     * 可选参数 关卡片id
     */
    protected void onPathLoseWhenMoveTick(int partId, ErrorCode code) {
        getOwner().getEventDispatcher().dispatch(new TickMoveNoPathEvent());
        onTargetLose(new TickMoveNoPathEvent());
    }

    /**
     * tick刷新路线失败会进来
     */
    private void onChaseSearchPathEnd(int codeId) {
        if (ErrorCode.isOK(codeId)) {
            return;
        }
        //  追着追着没路了
        if (getOwner().getEntityType() == EntityType.ET_Army) {
            ArmyEntity army = (ArmyEntity) getOwner();
            if (army.isRallyArmy()) {
                army.getRallyEntity().dismiss(RallyDismissReason.RDR_NO_PATH);
                return;
            }
        }
        // 这里无法判断是因为哪个关卡丢失了导致无法行军  所以传0
        onPathLoseWhenMoveTick(0, ErrorCode.MOVE_NO_PATH);
    }

    /**
     * 获取追击距离
     */
    private int getChaseDistance(SceneObjEntity targetEntity) {
        if (targetEntity == null) {
            return 0;
        }
        BattleGround battleGround = getOwner().getScene().getBattleGroundComponent().getBattleGround();
        // 有prepare的 说明刚开战 不管了
        if (battleGround.hasPrepareBattleRelation(getEntityId(), targetEntity.getEntityId())) {
            return 0;
        }
        BattleRelation battleRelation = battleGround.findBattleRelationOrNull(getEntityId(), targetEntity.getEntityId());
        if (battleRelation == null) {
            /* 行军追击判断 */
            // 距离未发生变化
            if (!getOwner().getScene().getTickMgrComponent().isCurTickChangePoint(targetEntity.getEntityId())) {
                return 0;
            }
            // 目标距离足够
            int chaseDistance = getMoveToTargetDistance(targetEntity, interactionType);
            if (Point.calDisBetweenTwoPoint(getCurPoint(), targetEntity.getCurPoint()) <= chaseDistance) {
                // 距离够了，开打
                arriveEnd();
                return 0;
            }
            return chaseDistance;
        } else {
            /* 战斗追击判断 */
            // 非进攻者 且非野怪 不追击
            if (!battleRelation.isRelativelyAttacker(getOwner().getEntityId()) && getEntityType() != EntityType.ET_Monster) {
                return 0;
            }
            int chaseDistance = getMoveToTargetDistance(targetEntity, interactionType);
            if (Point.calDisBetweenTwoPoint(getCurPoint(), targetEntity.getCurPoint()) <= chaseDistance) {
                return 0;
            }
            // 触发追击的时候, 需要先退出围攻
            targetEntity.getTransformComponent().exitBesiege(getEntityId());
            return chaseDistance;
        }
    }

    /**
     * 设置移动数据
     */
    private void setMoveData(MoveData moveData) {
        // 要清空了 先设置下方向
        if (moveData == null && curMoveData != null) {
            onEndMovePre();
            getOwner().tryRemoveTick(SceneTickReason.TICK_MOVE);
        }
        if (moveData != null && curMoveData == null) {
            getOwner().addTick(SceneTickReason.TICK_MOVE);
        }
        curMoveData = moveData;
        // 同步到prop 给client
        syncMoveProp();
    }

    /**
     * 修改移动属性 同步客户端
     */
    private void syncMoveProp() {
        getMoveProp().getCurPoint().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        getMoveProp().getPointList().clear();
        if (curMoveData == null) {
            // 清空同步
            getMoveProp().getCurPoint().setX(getCurPoint().getX()).setY(getCurPoint().getY());
            getMoveProp().setStartTs(0).setEndTs(0);
        } else {
            // 增量同步
            getMoveProp().setStartTs(curMoveData.getMoveStartTime()).setEndTs(curMoveData.getMoveEndTime());
            for (Point point : curMoveData.getMovePathList()) {
                PointProp pointData = new PointProp();
                pointData.setX(point.getX()).setY(point.getY());
                getMoveProp().getPointList().add(pointData);
            }
        }
    }

    /**
     * 行进中速度更改   有问题的
     */
    public void onSpeedChanged() {
        initMoveSpeedFromTroopProp();
        LOGGER.info("{} onSpeedChanged newSpeed:{}", getOwner(), moveSpeed);
        if (isMoving() && curMoveData.changeMoveSpeed(getOwner())) {
            //同步给客户端
            syncMoveProp();
            onRefreshMove();
        }
    }

    /**
     * 取消对移动目标的事件监听
     */
    private void cancelMoveSubEventListener() {
        if (moveTargetLoseEventListener != null) {
            moveTargetLoseEventListener.cancel();
            moveTargetLoseEventListener = null;
        }
        cancelMoveTargetChangeEvent();
    }

    /**
     * 获取移动到目标点的距离
     */
    private int getMoveToTargetDistance(SceneObjEntity targetEntity, TroopInteractionType interactionType) {
        if (interactionType == null) {
            return getOwner().getTransformComponent().getChaseDistance(targetEntity);
        }
        return getOwner().getTransformComponent().getInteractionDistance(targetEntity, interactionType);
    }

    /**
     * 获取移动结束时间戳 单位ms
     */
    public long getMoveArriveTime() {
        if (!isMoving()) {
            return 0;
        }
        return curMoveData.getMoveEndTime();
    }

    public void setArriveHandler(IMoveArriveHandler arriveHandler) {
        this.arriveHandler = arriveHandler;
    }

    protected abstract TroopProp getTroopProp();

    protected abstract MoveProp getMoveProp();

    public long getCurChaseTargetId() {
        return getMoveProp().getTargetId();
    }

    /**
     * 在开始行军的时候调用
     * 包括指令、追击目标变化刷新
     */
    protected void onStartMove() {
        onRefreshMove();
    }

    /**
     * 在行军路线变化的时候调用
     * 包括 开始行军、动态避障、速度变化刷新
     */
    protected void onRefreshMove() {

    }

    private final ArrayList<Long> chaseCheckList = new ArrayList<>();

    /**
     * 开始移动了  check围攻我的人  即时追击
     */
    protected void checkAndRefreshBattleMeObj() {
        if (!getOwner().getBattleComponent().hasAnyAlive()) {
            return;
        }
        Set<Long> battleMeObj = getOwner().getTransformComponent().getBattleMeObj();
        if (battleMeObj == null || battleMeObj.isEmpty()) {
            return;
        }
        int selfSpeed = getRealMoveSpeed(curMoveData.getSearchTag(), curMoveData.getEndPoint());
        // 构建追击链路
        Set<Long> oldChaseIdSet = curMoveData.getChaseIdSet();
        if (oldChaseIdSet == null) {
            oldChaseIdSet = new HashSet<>();
        }
        Set<Long> newChaseIdSet = new HashSet<>(oldChaseIdSet);
        newChaseIdSet.add(getEntityId());
        long selfMoveTargetId = getCurChaseTargetId();
        for (Long objId : battleMeObj) {
            try {
                if (!checkNeedParallelChase(objId, selfMoveTargetId, oldChaseIdSet)) {
                    continue;
                }
                SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(objId);
                // 判断追击角度  如果不是远离方向的  就加入check队列  等真的远离了 才动
                if (!checkParallelChaseAngle(sceneObj.getCurPoint())) {
                    chaseCheckList.add(objId);
                    continue;
                }
                List<Point> pointList = getOwner().getScene().getPathFindMgrComponent().genChasePath(sceneObj,
                        curMoveData.getMovePathList(),
                        curMoveData.getCrossingPart(),
                        0,
                        getCurPoint(),
                        sceneObj.getCurPoint());
                if (pointList.size() < 2) {
                    continue;
                }
                MoveData moveData = new MoveData(pointList, curMoveData.getSearchTag());
                moveData.setChaseIdSet(newChaseIdSet);
                moveData.setMoveSpeed(selfSpeed);
                sceneObj.getMoveComponent().calPointWhenStopMove();
                sceneObj.getMoveComponent().onMoveDataFind(moveData, curMoveData.getMoveStartTime(), false);
            } catch (Exception e) {
                LOGGER.error("{} onMoveRefresh try chase failed objId: {} ", getOwner(), objId, e);
            }
        }
    }

    /**
     * 判断是否能并行追击
     *
     * @param objId          被判断的目标id
     * @param selfMoveTarget 自己的移动对象
     * @param chaseSet       自己的追击链路
     * @return 是否能并行追击
     */
    private boolean checkNeedParallelChase(long objId, long selfMoveTarget, Set<Long> chaseSet) {
        // 自己的移动目标不调整
        if (selfMoveTarget == objId) {
            return false;
        }
        // 目标不存在 已死亡 不支持
        SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(objId);
        if (sceneObj == null || !sceneObj.getBattleComponent().hasAnyAlive()) {
            return false;
        }
        // 野怪 看配置支持不支持
        if (sceneObj.getEntityType() == EntityType.ET_Monster) {
            if (((MonsterEntity) sceneObj).isBanParallelChase() || !sceneObj.getMoveComponent().canMove()) {
                return false;
            }
        }
        // 如果对方的跟随调整队列里有追击链路上的人  那就是成环了  会爆栈
        Set<Long> battleTargetSet = sceneObj.getTransformComponent().getBattleMeObj();
        if (battleTargetSet != null) {
            for (Long id : battleTargetSet) {
                if (chaseSet.contains(id)) {
                    LOGGER.warn("checkNeedParallelChase a ring");
                    // 成环了  不调整
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 判断并行追击角度是否ok
     */
    private boolean checkParallelChaseAngle(Point targetPoint) {
        // 自己与目标的角度
        double v1 = getCurPoint().calAngleWithPoint(targetPoint);
        // 自己的行军方向
        Double v2 = curMoveData.getCacheMoveAngle();
        if (v2 == null) {
            return false;
        }
        double v = Constants.curbAngle((int) (v2 - v1));
        // 映射到0-180之间
        if (v > 180) {
            v = 360 - v;
        }
        return v >= GameLogicConstants.PARALLEL_CHASE_ANGLE;
    }

    /**
     * 并行追击触发tick
     */
    private void tickParallelChaseList() {
        if (chaseCheckList.isEmpty()) {
            return;
        }
        long selfMoveTargetId = getCurChaseTargetId();
        // 构建追击链路
        Set<Long> oldChaseIdSet = curMoveData.getChaseIdSet();
        if (oldChaseIdSet == null) {
            oldChaseIdSet = new HashSet<>();
        }
        Set<Long> newChaseIdSet = new HashSet<>(oldChaseIdSet);
        newChaseIdSet.add(getEntityId());
        int selfSpeed = getRealMoveSpeed(curMoveData.getSearchTag(), curMoveData.getEndPoint());
        Iterator<Long> iterator = chaseCheckList.iterator();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(next);
            if (!checkNeedParallelChase(next, selfMoveTargetId, oldChaseIdSet)) {
                iterator.remove();
                continue;
            }
            if (sceneObj == null) {
                continue;
            }
            if (checkParallelChaseAngle(sceneObj.getCurPoint())) {
                iterator.remove();
                List<Point> pointList = getOwner().getScene().getPathFindMgrComponent().genChasePath(sceneObj,
                        curMoveData.getMovePathList(),
                        curMoveData.getCrossingPart(),
                        curMoveData.getCurIndex(),
                        getCurPoint(),
                        sceneObj.getCurPoint());
                if (pointList.size() < 2) {
                    continue;
                }
                MoveData moveData = new MoveData(pointList, curMoveData.getSearchTag());
                moveData.setChaseIdSet(newChaseIdSet);
                moveData.setMoveSpeed(selfSpeed);
                sceneObj.getMoveComponent().calPointWhenStopMove();
                sceneObj.getMoveComponent().onMoveDataFind(moveData, getOwner().getScene().now(), false);
            }
        }
    }

    public void checkAndStopBattleMeObj() {
        Set<Long> battleMeObj = getOwner().getTransformComponent().getBattleMeObj();
        if (battleMeObj == null || battleMeObj.isEmpty()) {
            return;
        }
        for (Long objId : battleMeObj) {
            SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(objId);
            if (sceneObj == null || !sceneObj.getBattleComponent().hasAnyAlive()) {
                continue;
            }
            try {
                sceneObj.getMoveComponent().onChaseTargetStopMove();
            } catch (Exception e) {
                LOGGER.error("{} checkAndStopBattleMeObj try stop failed objId: {} ", getOwner(), objId, e);
            }
        }
    }

    private void onChaseTargetStopMove() {
        if (!isParallelMoving()) {
            return;
        }
        // 结算下正在进行的移动 更新位置
        calPointWhenStopMove();
        setMoveData(null);
        onEndMove();
        SceneObjEntity moveTarget = getMoveTarget();
        if (moveTarget == null) {
            return;
        }
        // 调整合围角度
        moveTarget.getTransformComponent().exitBesiege(getEntityId());
        occupyAndMoveToBesiegePoint(moveTarget, true, null);

    }

    /**
     * 结束移动，但还未清除移动数据时调用
     */
    private void onEndMovePre() {
        if (curMoveData == null) {
            return;
        }
        long now = getOwner().getScene().now();
        Point prePoint = curMoveData.getRecentMovePoint(now);
        if (prePoint == null) {
            LOGGER.error("未找到上个寻路点: movePath:{} == movePathTime:{} == 现在时间:{}", curMoveData.getMovePathList(), curMoveData.getMoveArriveTimeList(), now);
            return;
        }
        Point curPoint = getOwner().getCurPoint();
        int yawX = curPoint.getX() - prePoint.getX();
        int yawY = curPoint.getY() - prePoint.getY();
        getMoveProp().getYaw().setX(yawX).setY(yawY);
    }

    /**
     * 结束移动调用
     */
    protected void onEndMove() {
        chaseCheckList.clear();
        checkAndStopBattleMeObj();
    }

    @Override
    public void onDestroy() {
        cancelMoveSubEventListener();
    }

    public void setYaw(int x, int y) {
        getMoveProp().getYaw().setX(x).setY(y);
    }

    public Vector2f getYaw() {
        return Vector2f.valueOf((float) getMoveProp().getYaw().getX(), (float) getMoveProp().getYaw().getY());
    }

    public void setIsFrozen(boolean isFrozen) {
        getMoveProp().setIsFrozen(isFrozen);
    }

    public boolean canMove() {
        return !getMoveProp().getIsFrozen();
    }

    public SceneObjEntity getMoveTarget() {
        long targetId = getCurChaseTargetId();
        if (targetId == 0) {
            return null;
        }
        return getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
    }

    public boolean isSiegeMove() {
        if (curMoveData != null) {
            return GameLogicConstants.isBesiegeMove(curMoveData.getSearchTag());
        }
        return false;
    }
}
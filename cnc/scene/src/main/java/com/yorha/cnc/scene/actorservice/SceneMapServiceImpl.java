package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.gm.SceneGmCommandMgr;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.actor.SceneMapService;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.SsSceneMap.*;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 地图相关  寻路、开副本、地图数据
 *
 * <AUTHOR>
 */
public class SceneMapServiceImpl implements SceneMapService {
    private static final Logger LOGGER = LogManager.getLogger(SceneMapServiceImpl.class);
    private final SceneActor sceneActor;
    private final long sceneId;

    public SceneMapServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
        this.sceneId = Long.parseLong(sceneActor.getId());
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    public long getSceneId() {
        return sceneId;
    }

    @Override
    public void handleExecuteSceneGmAsk(ExecuteSceneGmAsk ask) {
        try {
            SceneGmCommandMgr.getInstance().handle(sceneActor, ask.getPlayerId(), ask.getCommand());
        } catch (Exception e) {
            LOGGER.error("scene gm fail. msg:{}", ask, e);
            // e.getMessage()可能为null，直接setResult会NPE
            sceneActor.answer(ExecuteSceneGmAns.newBuilder().setResult(e.getMessage() == null ? e.getClass().getName() : e.getMessage()).build());
            return;
        }
        sceneActor.answer(ExecuteSceneGmAns.getDefaultInstance());
    }

    @Override
    public void handleSearchPathAsk(SearchPathAsk ask) {
        Point src = Point.valueOf(ask.getSrc().getX(), ask.getSrc().getY());
        Point end;
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        if (ask.getRallyId() != 0) {
            if (!scenePlayer.isInClan()) {
                sceneActor.answer(SearchPathAns.getDefaultInstance());
                return;
            }
            RallyEntity rallyEntity = scenePlayer.getRallyComponent().getRallyEntity(ask.getRallyId());
            if (rallyEntity == null) {
                sceneActor.answer(SearchPathAns.getDefaultInstance());
                return;
            }
            end = rallyEntity.getMoveTargetEntity().getCurPoint();
        } else {
            long targetId = ask.getTargetId();
            if (targetId != 0) {
                SceneObjEntity sceneObjEntity = getScene().getObjMgrComponent().getSceneObjEntity(targetId);
                if (sceneObjEntity == null) {
                    end = Point.valueOf(ask.getEnd().getX(), ask.getEnd().getY());
                } else {
                    end = sceneObjEntity.getTransformComponent().getBeBattlePoint(src, scenePlayer.getClanId(), GameLogicConstants.NORMAL_MOVE_TARGET);
                }
            } else {
                end = Point.valueOf(ask.getEnd().getX(), ask.getEnd().getY());
            }
        }
        SearchPathAns.Builder ans = SearchPathAns.newBuilder();
        List<Point> points = getScene().getPathFindMgrComponent().searchPathWithoutCollision(scenePlayer, src, end);
        for (Point p : points) {
            ans.addPath(Struct.Point.newBuilder().setX(p.getX()).setY(p.getY()).build());
        }
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleFetchClanCityPointListAsk(FetchClanCityPointListAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        SceneClanEntity sceneClan = scenePlayer.getSceneClan();
        FetchClanCityPointListAns.Builder ans = FetchClanCityPointListAns.newBuilder();
        if (sceneClan == null) {
            ans.setVersion(0);
            sceneActor.answer(ans.build());
            return;
        }
        sceneClan.getMemberComponent().buildMemberCity(ans, ask.getVersion(), ask.getPlayerId());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleFetchSingleClanMemberCityPointAsk(FetchSingleClanMemberCityPointAsk ask) {
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        SceneClanEntity sceneClan = scenePlayer.getSceneClan();
        FetchSingleClanMemberCityPointAns.Builder ans = FetchSingleClanMemberCityPointAns.newBuilder();
        if (sceneClan == null) {
            sceneActor.answer(ans.build());
            return;
        }
        // 要获取城池的玩家id
        long fetchPlayerId = ask.getFetchPlayerId();
        if (ask.getOnlyFetchOwner()) {
            fetchPlayerId = sceneClan.getClanOwnerId();
        }
        Point p = sceneClan.getMemberComponent().findMemberCity(fetchPlayerId);
        if (p == null) {
            sceneActor.answer(ans.build());
            return;
        }
        sceneActor.answer(ans.setMemberPos(Struct.Point.newBuilder().setX(p.getX()).setY(p.getY())).build());
    }

    @Override
    public void handleFetchTerritoryMapAsk(FetchTerritoryMapAsk ask) {
        FetchTerritoryMapAns ans = getScene().getClanMgrComponent().buildTerritoryMap(ask.getVersion());
        sceneActor.answer(ans);
    }
}

package com.yorha.cnc.scene.mapBuilding;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.component.CrossTransformComponent;
import com.yorha.cnc.scene.mapBuilding.component.MapBuildingPropComponent;
import com.yorha.cnc.scene.mapBuilding.component.MapBuildingTransformComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.game.gen.prop.MapBuildingProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class MapBuildingBuilder extends SceneObjBuilder<MapBuildingEntity, MapBuildingProp> {
    public MapBuildingBuilder(SceneEntity sceneEntity, long eid, MapBuildingProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public MapBuildingTransformComponent transformComponent(MapBuildingEntity owner) {
        RegionalAreaSettingTemplate template = getSceneEntity().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, getProp().getPartId());
        if (template.getAreaType() != CommonEnum.MapAreaType.CROSSING) {
            return new MapBuildingTransformComponent(owner, getPointProp(), getProp().getPartId());
        }
        return new CrossTransformComponent(owner, getPointProp(), getProp().getPartId());
    }

    @Override
    public MapBuildingPropComponent propComponent(MapBuildingEntity owner) {
        return new MapBuildingPropComponent(owner);
    }
}

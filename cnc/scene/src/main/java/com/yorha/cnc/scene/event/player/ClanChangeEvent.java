package com.yorha.cnc.scene.event.player;

import com.yorha.cnc.scene.event.ievent.IEventWithClanId;

/**
 * <AUTHOR>
 * <p>
 * 联盟改变事件
 */
public class ClanChangeEvent extends IEventWithClanId {

    private final long oldClanId;

    public ClanChangeEvent(long entityId, long oldClanId, long clanId) {
        super(entityId, clanId);
        this.oldClanId = oldClanId;
    }

    public long getOldClanId() {
        return oldClanId;
    }
}

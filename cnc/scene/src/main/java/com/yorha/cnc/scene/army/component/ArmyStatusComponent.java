package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.battle.ArmyStateChangeEvent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.event.battle.EnterNewBattle;
import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;
import com.yorha.cnc.scene.event.mapbuilding.TemplateChangeEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.event.player.ClanSimpleNameChangeEvent;
import com.yorha.cnc.scene.event.player.PlayerNameChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.ArmyDetailState;
import com.yorha.proto.CommonEnum.ArmyTargetType;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ArmyStatusComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyStatusComponent.class);

    private final ScenePlayerArmyStatusProp prop;
    /**
     * 目标数据变化的事件
     */
    private EventListener targetChangeEvent = null;
    /**
     * 目标id
     */
    private long targetId = 0;
    private int curPassPartId;

    public ArmyStatusComponent(ArmyEntity owner, ScenePlayerArmyStatusProp prop) {
        super(owner);
        this.prop = prop;
    }

    @Override
    public void init() {
        if (getOwner().isRallyArmy()) {
            return;
        }
        // 所有战斗结束 改战斗状态
        getOwner().getEventDispatcher().addEventListenerRepeat((e) -> {
            setDetailBattle(false);
        }, EndAllBattleEvent.class);

        getOwner().getEventDispatcher().addEventListenerRepeat((e) -> {
            setDetailBattle(true);
        }, EnterNewBattle.class);
    }

    public ArmyDetailState getState() {
        return getProp().getState();
    }

    public boolean isInPassing() {
        return prop.getIsInPassing();
    }

    public void setIsInPassing(boolean isInPassing, int partId) {
        // 运输机不影响开关门
        if (getOwner().isInTransport()) {
            return;
        }
        if (prop.getIsInPassing() == isInPassing) {
            return;
        }
        prop.setIsInPassing(isInPassing);
        if (isInPassing) {
            curPassPartId = partId;
            MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(partId);
            mapBuilding.getProp().setPassingArmyNum(mapBuilding.getProp().getPassingArmyNum() + 1);
        } else {
            MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(curPassPartId);
            mapBuilding.getProp().setPassingArmyNum(mapBuilding.getProp().getPassingArmyNum() - 1);
            curPassPartId = 0;
        }
        LOGGER.info("{} setArmyDetailState setIsInPassing {} {}", getOwner(), isInPassing, partId);
    }

    /**
     * 设置行军驻扎状态
     */
    public void setStaying() {
        if (getProp().getState() == ArmyDetailState.ADS_STAYING) {
            return;
        }
        LOGGER.info("{} setArmyDetailState staying", getOwner());
        getProp().getTarget().setTargetType(ArmyTargetType.ATT_NONE);
        setArmyState(ArmyDetailState.ADS_STAYING);
        cancelEvent();
    }

    /**
     * 集结里更新队员行军  高频
     */
    public void setDetailState(ArmyDetailState state, long endTsMs, Point point) {
        LOGGER.debug("{} setArmyDetailState  state: {} endTsMs: {} {}", getOwner(), state, endTsMs, point);
        getProp().setEndTsMs(endTsMs).getTarget().getPoint().setX(point.getX()).setY(point.getY());
        setArmyState(state);
    }

    /**
     * 集结等待时间变化了  更新
     */
    public void setDetailState(ArmyDetailState state, long endTsMs) {
        LOGGER.info("{} setArmyDetailState  state: {} endTsMs: {}", getOwner(), state, endTsMs);
        getProp().setEndTsMs(endTsMs);
        setArmyState(state);
    }

    /**
     * 更新集结等待、 拾取
     */
    public void setDetailState(ArmyDetailState state, SceneObjEntity entity, long endTsMs) {
        setDetailState(state, entity, endTsMs, 0);
    }

    /**
     * 更新采集
     */
    public void setDetailState(ArmyDetailState state, SceneObjEntity entity, long endTsMs, long collectNum) {
        LOGGER.info("{} setArmyDetailState  state: {} endTsMs: {} collectNum:{}", getOwner(), state, endTsMs, collectNum);
        setDetailTarget(state, entity);
        getProp().setEndTsMs(endTsMs).setCollectNum(collectNum).setStartTsMs(SystemClock.now());
    }

    /**
     * 更新采集，带开始采集时间戳
     */
    public void setDetailState(ArmyDetailState state, SceneObjEntity entity, long startTsMs, long endTsMs, long collectNum) {
        LOGGER.info("{} setArmyDetailState  state: {} endTsMs: {} collectNum:{}", getOwner(), state, endTsMs, collectNum);
        setDetailTarget(state, entity);
        getProp().setStartTsMs(startTsMs).setEndTsMs(endTsMs).setCollectNum(collectNum);
    }


    /**
     * 设置结束时间  移动专用  高频
     */
    public void setDetailState(long endTsMs, Point point) {
        LOGGER.debug("{} setArmyDetailState endTsMs: {}  point:{}", getOwner(), endTsMs, point);
        getProp().setEndTsMs(endTsMs).getTarget().getPoint().setX(point.getX()).setY(point.getY());
        // 追击会进来
        if (getProp().getState() == ArmyDetailState.ADS_BATTLE) {
            BattleRelation relation1 = getOwner().getScene().getBattleGroundComponent().findBattleRelationOrNull(getEntityId(), targetId);
            BattleRelation relation2 = getOwner().getScene().getBattleGroundComponent().findPrepareBattleRelationOrNull(getEntityId(), targetId);
            if (relation1 == null && relation2 == null) {
                setArmyState(ArmyDetailState.ADS_MOVE_BATTLE);
            }
        }
    }

    /**
     * 收束行军状态变更入口（不包括初始驻扎）
     */
    private void setArmyState(ArmyDetailState state) {
        ArmyDetailState oldState = getProp().getState();
        getProp().setState(state);
        getOwner().getEventDispatcher().dispatch(new ArmyStateChangeEvent(oldState, getProp().getState()));
    }

    /**
     * 开始移动  需要   异步移动时就靠这个更新状态了
     */
    public void onMoveStart() {
        getOwner().getBehaviourComponent().refreshArmyState();
        SceneObjEntity moveTarget = getOwner().getMoveComponent().getMoveTarget();
        // 如果是已经在跟田里的部队开战了 不刷
        if (moveTarget != null
                && moveTarget.getEntityType() == EntityAttrOuterClass.EntityType.ET_ResBuilding
                && getState() == ArmyDetailState.ADS_BATTLE
                && moveTarget.getBattleComponent() != null
                && targetId == moveTarget.getBattleComponent().getOwner().getEntityId()) {
            return;
        }
        ArmyDetailState newArmyState = getNewArmyState(moveTarget);
        long newTargetId = moveTarget == null ? 0 : moveTarget.getEntityId();
        if (getState() == newArmyState && targetId == newTargetId) {
            return;
        }
        setDetailTarget(newArmyState, moveTarget);
    }

    private ArmyDetailState getNewArmyState(SceneObjEntity moveTarget) {
        if (moveTarget == null) {
            if (GameLogicConstants.isTransportState(getOwner().getProp().getArmyState())) {
                return ArmyDetailState.ADS_MOVE_TRANSPORT;
            } else {
                return ArmyDetailState.ADS_MOVE;
            }
        } else {
            if (moveTarget.getEntityType() == EntityAttrOuterClass.EntityType.ET_DropObject) {
                return ArmyDetailState.ADS_MOVE_COLLECT;
            }
            if (moveTarget == getOwner().getScenePlayer().getMainCity()) {
                return ArmyDetailState.ADS_MOVE_RETURN;
            } else if (getOwner().getProp().getCurAssistTargetId() != 0) {
                return ArmyDetailState.ADS_MOVE_ASSIST;
            } else if (getOwner().getCollectComponent().getCollectTarget() != 0) {
                boolean battleToCollect = getOwner().getCollectComponent().isBattleToCollect();
                return battleToCollect ? ArmyDetailState.ADS_MOVE_BATTLE : ArmyDetailState.ADS_MOVE_COLLECT;
            } else if (getOwner().getRallyComponent().getCurRallyId() != 0 && !getOwner().isRallyArmy()) {
                // 向城 是增援行军  向部队 是进攻行军
                if (moveTarget.getEntityType() == EntityAttrOuterClass.EntityType.ET_City) {
                    return ArmyDetailState.ADS_MOVE_ASSIST;
                } else {
                    return ArmyDetailState.ADS_MOVE_BATTLE;
                }
            } else {
                // 前面都不是 好像就是battle了
                // 追击触发的
                if (getProp().getState() == ArmyDetailState.ADS_BATTLE) {
                    BattleRelation relation1 = getOwner().getScene().getBattleGroundComponent().findBattleRelationOrNull(getEntityId(), targetId);
                    BattleRelation relation2 = getOwner().getScene().getBattleGroundComponent().findPrepareBattleRelationOrNull(getEntityId(), targetId);
                    if (relation1 == null && relation2 == null) {
                        return ArmyDetailState.ADS_MOVE_BATTLE;
                    }
                    return ArmyDetailState.ADS_BATTLE;
                }
                moveTarget.getBattleComponent().beAttacked(getOwner(), getOwner().getPlayerId());
                return ArmyDetailState.ADS_MOVE_BATTLE;
            }
        }
    }

    /**
     * 设置状态及目标
     */
    public void setDetailTarget(ArmyDetailState state, SceneObjEntity entity) {
        setArmyState(state);
        long newTargetId = entity == null ? 0 : entity.getEntityId();
        if (entity != null) {
            entity.copyScenePlayerArmyTargetStatus(prop);
            if (newTargetId != targetId) {
                addEvent(entity);
            }
        } else {
            cancelEvent();
            prop.getTarget().setName("").setClanSimpleName("").setTemplateId(0).setTargetType(ArmyTargetType.ATT_NONE);
        }
        targetId = newTargetId;
        LOGGER.info("{} setArmyDetailState state: {} target: {}", getOwner(), state, entity);
    }

    /**
     * 设置进入战斗状态  自己开战、援助对象开战
     */
    public void setDetailBattle(boolean isBattle) {
        LOGGER.info("{} setArmyDetailState isBattle: {}", getOwner(), isBattle);
        getProp().setIsBattle(isBattle);
    }

    /**
     * 更新负重  合并计
     */
    public void addBurden(long num) {
        prop.setCurBurden(prop.getCurBurden() + num);
        LOGGER.info("{} updateBurden. add={} now={}", getOwner(), num, prop.getCurBurden());
    }

    /**
     * 更新负重  单一资源  需要计算负重比
     */
    public void addBurden(CurrencyType resType, long resNum) {
        long burden = resNum * GameLogicConstants.getCurrencyBurdenRadio(resType);
        prop.setCurBurden(prop.getCurBurden() + burden);
        LOGGER.info("{} updateBurden. resType={} add={} now={}", getOwner(), resType, burden, prop.getCurBurden());
    }

    public ScenePlayerArmyStatusProp getProp() {
        return prop;
    }

    /**
     * 设置目标时调用 增加目标信息变化的监听器
     */
    private void addEvent(SceneObjEntity entity) {
        cancelEvent();
        targetChangeEvent = entity.getEventDispatcher().addMultiEventListenerRepeat((e) -> {
            IEventWithEntityId event = (IEventWithEntityId) e;
            SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(event.getEntityId());
            target.copyScenePlayerArmyTargetStatus(getProp());
        }, PlayerNameChangeEvent.class, ClanSimpleNameChangeEvent.class, ClanChangeEvent.class, TemplateChangeEvent.class);
    }

    /**
     * 清理目标时调用  取消监听
     */
    private void cancelEvent() {
        if (targetChangeEvent == null) {
            return;
        }
        targetChangeEvent.cancel();
        targetChangeEvent = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelEvent();
    }
}

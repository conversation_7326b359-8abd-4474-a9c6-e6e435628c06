package com.yorha.cnc.scene.sceneplayer.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerHospitalComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.qlog.HospitalActionType;
import com.yorha.common.qlog.json.Army.ArmyConfig;
import com.yorha.game.gen.prop.Int32PlayerHospitalSoldierMapProp;
import com.yorha.game.gen.prop.PlayerHospitalSoldierProp;
import com.yorha.game.gen.prop.ScenePlayerHospitalExclusiveRegionProp;
import com.yorha.game.gen.prop.ScenePlayerHospitalModelProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncHospitalFlow;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 医院的数据放在scenePlayer上，因为scene上的读写操作比较多，而且scene上每回合战斗死兵数据依赖医院数据
 */
public class ScenePlayerHospitalComponent extends AbstractScenePlayerHospitalComponent {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerHospitalComponent.class);


    public ScenePlayerHospitalComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    protected ScenePlayerHospitalModelProp prop() {
        return getOwner().getProp().getHospitalModel();
    }

    @Override
    public void onEndAllRelation() {
        try {
            // qlog
            List<ArmyConfig> collect = soldierInHospitalDuringBattle.entrySet().stream()
                    .map(it -> new ArmyConfig(it.getKey(), it.getValue()))
                    .collect(Collectors.toList());
            sendHospitalQlog(HospitalActionType.INJURED_ENTER_HOSPITAL, collect);
        } catch (Exception e) {
            LOGGER.error("", e);
        } finally {
            super.onEndAllRelation();
        }
    }


    public void handleHospitalTreatCheckAsk(SsScenePlayer.HospitalTreatCheckAsk ask) {
        assertTreatSoldiersCount(ask.getTreatUnion());
        // 队列是否空闲
        if (!prop().getInTreatmentSoldiers().isEmpty() || prop().getExclusiveRegion().getInTreatmentSoldiers() > 0) {
            throw new GeminiException(ErrorCode.ALREADY_SOME_SOLDIERS_IN_TREATMENT);
        }

        if (getOwner().getMainCity().getBattleComponent().getBattleRole().hasActiveRelation()) {
            throw new GeminiException(ErrorCode.HOSPITAL_CITY_UNDER_ATTACK_HOSPITAL_CANNOT_TREAT);
        }
    }


    public void handleHospitalTreatAsk(SsScenePlayer.HospitalTreatAsk ask) {
        CommonMsg.HospitalTreatUnion treatUnion = ask.getTreatUnion();

        Collection<StructPB.PlayerHospitalSoldierPB> normalSoldiers = treatUnion.getToTreatSoldiers().getDatasMap().values();
        mergeMinusByPb(prop().getWaitingSoldiers(), normalSoldiers);
        mergePlusByPb(prop().getInTreatmentSoldiers(), normalSoldiers);

        tryMinusExclusiveRegionWaiting(treatUnion);
        tryPlusExclusiveRegionInTreatment(treatUnion);

        // qlog
        List<ArmyConfig> collect = normalSoldiers.stream().map(it -> new ArmyConfig(it.getSoldierId(), it.getSevereNum())).collect(Collectors.toList());
        if (treatUnion.hasToTreatExclusive()) {
            collect.add(new ArmyConfig(treatUnion.getToTreatExclusive().getSoldierId(), treatUnion.getToTreatExclusive().getSevereNum()));
        }
        sendHospitalQlog(HospitalActionType.START_RECOVERING, collect);
    }


    public Map<Integer, Integer> handleHospitalFastTreatAsk(SsScenePlayer.HospitalFastTreatAsk ask) {
        CommonMsg.HospitalTreatUnion treatUnion = ask.getTreatUnion();

        // qlog
        sendHospitalQlog(HospitalActionType.START_FAST_RECOVERING, null);

        mergeMinusByPb(prop().getWaitingSoldiers(), treatUnion.getToTreatSoldiers().getDatasMap().values());
        ArrayList<StructPB.PlayerHospitalSoldierPB> treatOver = Lists.newArrayList(treatUnion.getToTreatSoldiers().getDatasMap().values());

        tryMinusExclusiveRegionWaiting(treatUnion);
        StructPB.PlayerHospitalSoldierPB toTreatExclusive = treatUnion.getToTreatExclusive();
        if (toTreatExclusive.getSoldierId() > 0 && toTreatExclusive.getSevereNum() > 0) {
            treatOver.add(toTreatExclusive);
        }

        getOwner().getSoldierMgrComponent().addTreatOverSoldiers(treatOver);
        Map<Integer, Integer> soldierId2Num = new HashMap<>();
        for (StructPB.PlayerHospitalSoldierPB soldierPB : treatOver) {
            soldierId2Num.put(soldierPB.getSoldierId(), soldierPB.getSevereNum());
        }

        // qlog
        List<ArmyConfig> collect = soldierId2Num.entrySet().stream()
                .map(it -> new ArmyConfig(it.getKey(), it.getValue()))
                .collect(Collectors.toList());
        sendHospitalQlog(HospitalActionType.COMPLETE_RECOVERING, collect);
        sendHospitalQlog(HospitalActionType.RECOVERED_LEAVE_HOSPITAL, collect);

        return soldierId2Num;
    }


    public SsScenePlayer.ReturnTreatOverSoldiersAns handleReturnTreatOverSoldiers() {
        SsScenePlayer.ReturnTreatOverSoldiersAns.Builder builder = SsScenePlayer.ReturnTreatOverSoldiersAns.newBuilder();
        Int32PlayerHospitalSoldierMapProp treatOverSoldiers = prop().getTreatOverSoldiers();

        // 收取普通区士兵
        List<PlayerHospitalSoldierProp> toAdd = Lists.newArrayList(treatOverSoldiers.values());
        prop().clearTreatOverSoldiers();

        // 收取专区士兵
        ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = prop().getExclusiveRegion();
        int regionTreatOver = exclusiveRegion.getTreatOverSoldiers();
        if (regionTreatOver > 0) {
            exclusiveRegion.setTreatOverSoldiers(0);
            toAdd.add(new PlayerHospitalSoldierProp()
                    .setSoldierId(exclusiveRegion.getSoldierId())
                    .setSevereNum(regionTreatOver)
            );
        }

        getOwner().getSoldierMgrComponent().addTreatOverSoldiersByProp(toAdd);

        for (PlayerHospitalSoldierProp p : toAdd) {
            builder.putSoldierId2Num(p.getSoldierId(), p.getSevereNum());
        }

        // qlog
        List<ArmyConfig> collect = builder.getSoldierId2NumMap().entrySet().stream()
                .map(it -> new ArmyConfig(it.getKey(), it.getValue()))
                .collect(Collectors.toList());
        sendHospitalQlog(HospitalActionType.RECOVERED_LEAVE_HOSPITAL, collect);

        return builder.build();
    }


    public void handleHospitalTreatFinishAsk() {
        List<PlayerHospitalSoldierProp> inTreatmentSoldiers = Lists.newArrayList(prop().getInTreatmentSoldiers().values());
        prop().clearInTreatmentSoldiers();
        // 挪到treatOver里面去就可以了，真正退还到玩家身上需要客户端请求returnSoldiers
        mergePlusByProp(prop().getTreatOverSoldiers(), inTreatmentSoldiers);

        ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = prop().getExclusiveRegion();
        int regionInTreatment = exclusiveRegion.getInTreatmentSoldiers();
        if (regionInTreatment > 0) {
            exclusiveRegion.setInTreatmentSoldiers(0);
            exclusiveRegion.setTreatOverSoldiers(exclusiveRegion.getTreatOverSoldiers() + regionInTreatment);
        }

        // qlog
        List<ArmyConfig> collect = inTreatmentSoldiers.stream()
                .map(it -> new ArmyConfig(it.getSoldierId(), it.getSevereNum()))
                .collect(Collectors.toList());
        sendHospitalQlog(HospitalActionType.COMPLETE_RECOVERING, collect);
    }

    private void sendHospitalQlog(HospitalActionType actionType, List<ArmyConfig> armyConfigs) {
        Collection<ArmyConfig> allSoldierInHospital = getAllSoldierInHospital();
        QlogCncHospitalFlow flow = new QlogCncHospitalFlow();
        flow.fillHead(getOwner().getQlogComponent());
        flow.setDtEventTime(TimeUtils.now2String())
                .setAction(actionType.getType())
                .setArmyConfig(armyConfigs == null ? JsonUtils.toJsonString(allSoldierInHospital) : JsonUtils.toJsonString(armyConfigs))
                .setArmyInHosConfig(JsonUtils.toJsonString(allSoldierInHospital));
        flow.sendToQlog();
    }

}

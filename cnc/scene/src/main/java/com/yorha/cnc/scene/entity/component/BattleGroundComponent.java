package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Sets;
import com.yorha.cnc.battle.adapter.interfaces.IBattleGroundAdapter;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.areaSkill.AreaSkillEntity;
import com.yorha.cnc.scene.areaSkill.AreaSkillFactory;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.db.tcaplus.msg.GameDbReq;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.BattleTemplateService;
import com.yorha.common.resource.resservice.battle.DamageRatioConf;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.yorha.cnc.battle.core.BattleGround.getBattleLog;


/**
 * 统管场景上的所有战斗关系
 *
 * <AUTHOR>
 */
public class BattleGroundComponent extends SceneComponent implements IBattleGroundAdapter {
    private static final Logger LOGGER = LogManager.getLogger(BattleGroundComponent.class);

    /**
     * 战场
     */
    protected final BattleGround battleGround;

    public BattleGroundComponent(SceneEntity owner, BattleConstants.BattleGroundType type, @Nullable CommonEnum.DungeonType dungeonType) {
        super(owner);
        this.battleGround = new BattleGround(this, type, dungeonType, owner.getMapIdForPoint(), owner::getMapType);
    }

    @Override
    public void init() {
    }

    /**
     * 判断两军是否战斗中
     */
    public boolean hasBattleRelation(long oneId, long otherId) {
        return battleGround.hasBattleRelation(oneId, otherId);
    }

    /**
     * 判断两军是否有预备战斗的关系
     */
    public boolean hasPrepareBattleRelation(long oneId, long otherId) {
        return battleGround.hasPrepareBattleRelation(oneId, otherId);
    }

    /**
     * 获取两军的战斗关系
     */
    @Nullable
    public BattleRelation findBattleRelationOrNull(long oneId, long otherId) {
        return battleGround.findBattleRelationOrNull(oneId, otherId);
    }

    /**
     * 获取两军的预备战斗关系
     */
    @Nullable
    public BattleRelation findPrepareBattleRelationOrNull(long oneId, long otherId) {
        return battleGround.findPrepareBattleRelationOrNull(oneId, otherId);
    }

    public BattleGround getBattleGround() {
        return battleGround;
    }

    public long onTick() {
        final long start = SystemClock.now();
        try {
            battleGround.tick();
            long cost = SystemClock.now() - start;
            if (cost > MonitorConstant.BATTLE_TICK_OVER_TIME) {
                LOGGER.warn("gemini_pref BattleGround tick time:{}ms, round:{}, stat:{}", cost, battleGround.getGroundRound(), battleGround.getTickCtx().watchForOnTick.stat());
            }
            return cost;
        } catch (Exception e) {
            if (getBattleGround().isInSimulator()) {
                throw e;
            } else {
                WechatLog.error(e);
            }
        }
        return SystemClock.now() - start;
    }

    /**
     * 开启战斗日志
     */
    public void openLogSwitch() {
        getBattleLog().openLogSwitch(false);
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.CLOSE_LOG_SWITCH, getOwner().ownerActor().getId());
        // 日志打印会有额外开销，不能一直开着，需要自动关闭
        getOwner().getTimerComponent().addTimerWithPrefix(getOwner().ownerActor().getId(), TimerReasonType.CLOSE_LOG_SWITCH, this::closeLogSwitch, Constants.BATTLE_LOG_RESET_TIME_SEC, TimeUnit.SECONDS);
        LOGGER.info("add schedule by BattleLogPrint.");
    }

    /**
     * 主动关闭战斗日志
     */
    public void closeLogSwitch() {
        getBattleLog().closeLogSwitch();
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.CLOSE_LOG_SWITCH, getOwner().ownerActor().getId());
    }

    @Override
    public Set<Long> allEntityIds() {
        return getOwner().getObjMgrComponent().getEntityMap().keySet();
    }

    @Override
    public int allEntityCount() {
        return getOwner().getObjMgrComponent().getEntityMap().size();
    }

    @Override
    public long now() {
        return getOwner().now();
    }

    @Override
    public boolean isBattleRoleExists(long roleId) {
        return getOwner().getObjMgrComponent().getSceneObjEntity(roleId) != null;
    }

    @Override
    public int getMapId() {
        return getOwner().getMapId();
    }

    @Override
    public void addHate(IBattleRoleAdapter one, IBattleRoleAdapter other) {
        getOwner().getHateComponent().addHate(one, other);
    }

    @Override
    public void removeHate(IBattleRoleAdapter one, IBattleRoleAdapter other) {
        getOwner().getHateComponent().removeHate(one, other);
    }

    @Override
    public boolean isHate(IBattleRoleAdapter one, IBattleRoleAdapter other) {
        return getOwner().getHateComponent().isHate(one, other);
    }

    @Override
    public void tellGameDb(GameDbReq<?> msg) {
        getOwner().ownerActor().tellGameDb(msg);
    }

    @Override
    public DamageRatioConf getDamageRatioConf(long roleId, CommonEnum.BattleType battleType, CommonEnum.SceneObjType objType, CommonEnum.DamageRatioTypeEnum ratioTypeEnum) {
        return ResHolder.getResService(BattleTemplateService.class).getDamageRatioTemplate(getMapId(), battleType, objType, ratioTypeEnum);
    }

    @Override
    public BattleRole getBattleRoleById(long entityId) {
        SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(entityId);
        if (sceneObjEntity == null || sceneObjEntity.getBattleComponent() == null) {
            return null;
        }
        return sceneObjEntity.getBattleComponent().getBattleRole();
    }

    @Override
    public Set<BattleRole> getBatchBattleRoleById(Set<Long> entityList) {
        Set<BattleRole> result = new HashSet<>();
        for (long entityId : entityList) {
            SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(entityId);
            if (sceneObjEntity == null || sceneObjEntity.getBattleComponent() == null) {
                continue;
            }
            result.add(sceneObjEntity.getBattleComponent().getBattleRole());
        }
        return result;
    }

    @Override
    public BattleRole createAreaSkill(BattleRole owner, int skillId, Point point, Integer lifeTime) {
        AreaSkillEntity areaSkill = AreaSkillFactory.createAreaSkill(getOwner(), null, owner, skillId, point, lifeTime);
        return areaSkill != null ? areaSkill.getBattleComponent().getBattleRole() : null;
    }

    @Override
    public long getBuildingOwnerClanId(Point point) {
        if (!getOwner().isMainScene()) {
            return 0;
        }
        return getOwner().getBuildingMgrComponent().getOwnerIdByPartId(point);
    }

    @Override
    public Set<BattleRole> getAoiBattleRoles(Shape shape) {
        Set<BattleRole> ret = Sets.newHashSet();
        Set<SceneObjEntity> affectedAoiSceneObjList = getOwner().getAoiMgrComponent().getAffectedAoiSceneObjList(shape);
        for (SceneObjEntity inRangeEntity : affectedAoiSceneObjList) {
            SceneObjBattleComponent inRangeComponent = inRangeEntity.getBattleComponent();
            if (inRangeComponent == null) {
                continue;
            }
            ret.add(inRangeEntity.getBattleComponent().getBattleRole());
        }
        return ret;
    }

    public boolean tryStartBattleWith(SceneObjEntity attacker, SceneObjEntity target) {
        SceneObjBattleComponent attackerComp = attacker.getBattleComponent();
        SceneObjBattleComponent targetComp = target.getBattleComponent();
        if (attackerComp == null) {
            LOGGER.error("tryStartBattleWith failed. attacker:{} target:{} attacker battleComponent is null", attacker, target);
            return false;
        }
        if (target.getBattleComponent() == null) {
            LOGGER.error("tryStartBattleWith failed. attacker:{} target:{} target battleComponent is null", attacker, target);
            return false;
        }

        BattleRelation battleRelation = getBattleGround().tryStartBattleWith(attackerComp.getBattleRole(), targetComp.getBattleRole());
        return battleRelation != null;
    }

    public void tryStartBattleBySkill(SceneObjEntity attacker, SceneObjEntity target) {
        SceneObjBattleComponent attackerComp = attacker.getBattleComponent();
        SceneObjBattleComponent targetComp = target.getBattleComponent();
        if (attackerComp == null) {
            LOGGER.error("tryStartBattleBySkill failed. attacker:{} target:{} attacker battleComponent is null", attacker, target);
            return;
        }
        if (target.getBattleComponent() == null) {
            LOGGER.error("tryStartBattleBySkill failed. attacker:{} target:{} target battleComponent is null", attacker, target);
            return;
        }
        BattleRelation relation = getBattleGround().tryStartBattleBySkill(attackerComp.getBattleRole(), targetComp.getBattleRole());
        LOGGER.debug("create battle buildRelation success with tryStartBattleBySkill. relationId:{}", relation);

    }
}

package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.error.ErrorCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 单一部队攻打限制
 */
public class MonsterSkynetComponent extends SceneObjComponent<MonsterEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MonsterSkynetComponent.class);

    /**
     * 已经被发起进攻
     */
    private long beAttackArmyId;

    public MonsterSkynetComponent(MonsterEntity owner) {
        super(owner);
    }

    /**
     * 被攻击
     */
    public ErrorCode beAttackCheck(long playerId, long entityId){
        if (beAttackArmyId > 0 && beAttackArmyId != entityId){
            return ErrorCode.SKYNET_MONSTER_BOSS_BE_ATTACK;
        }
        if (getOwner().getProp().getSummonPlayerId() <= 0){
            return ErrorCode.EXCLUSIVE_MONSTER;
        }
        if (getOwner().getProp().getSummonPlayerId() != playerId){
            return ErrorCode.SKYNET_MONSTER_BOSS_BE_ATTACK_ON_PLAYER;
        }
        return ErrorCode.OK;
    }

    public void onAttackBePlayer(long entityId){
        if (beAttackArmyId > 0){
            LOGGER.error("MonsterSkynetComponent onAttackBePlayer hasBeAttack={} mosnter={}", beAttackArmyId, getEntityId());
            return;
        }
        beAttackArmyId = entityId;
    }

    public void clearBeAttackData() {
        beAttackArmyId = 0;
    }
}

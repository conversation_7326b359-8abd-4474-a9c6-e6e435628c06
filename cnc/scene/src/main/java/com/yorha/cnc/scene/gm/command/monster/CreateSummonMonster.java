package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.MonsterFactory;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 创建专属野怪
 */
public class CreateSummonMonster implements SceneGmCommand {
    private static final Logger LOGGER = LogManager.getLogger(CreateMonster.class);

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        try {
            int x = Integer.parseInt(args.get("x"));
            int y = Integer.parseInt(args.get("y"));
            int monsterId = Integer.parseInt(args.get("monsterId"));
            int lifeTime = Integer.parseInt(args.get("lifeTime"));
            if (!ResHolder.getInstance().getMap(MonsterTemplate.class).containsKey(monsterId)) {
                return;
            }
            SceneObjSpawnParam param = new SceneObjSpawnParam();
            param.setSummonPlayerId(playerId);
            param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(lifeTime));
            MonsterEntity entity = MonsterFactory.initMonster(actor.getScene(), monsterId, Point.valueOf(x, y), param);
            if (entity == null) {
                return;
            }
            entity.addIntoScene();
            LOGGER.info("gm create monsterId = {}", entity.getEntityId());
        } catch (Exception e) {
            LOGGER.info("gm create monster failed :{}", args, e);
        }

    }

    @Override
    public String showHelp() {
        return "CreateSummonMonster x={midScreenX} y={midScreenY} monsterId={value} lifeTime=30";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MONSTER;
    }
}

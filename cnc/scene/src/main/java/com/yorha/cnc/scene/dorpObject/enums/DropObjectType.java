package com.yorha.cnc.scene.dorpObject.enums;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;

import java.util.EnumMap;

/**
 * <AUTHOR>
 * <p>
 * 掉落物类型
 */
public enum DropObjectType {
    /**
     * 道具
     */
    ITEM(CommonEnum.DropObjectType.ITEM),
    /**
     * buff
     */
    BUFF(CommonEnum.DropObjectType.BUFF);


    private final CommonEnum.DropObjectType type;

    DropObjectType(CommonEnum.DropObjectType type) {
        this.type = type;
    }

    public CommonEnum.DropObjectType getType() {
        return type;
    }

}

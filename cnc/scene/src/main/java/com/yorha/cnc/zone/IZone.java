package com.yorha.cnc.zone;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.zone.component.AbsZoneSideActivityComponent;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.utils.time.schedule.ActorTimerInterface;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public interface IZone {

    /**
     * @return zoneId or kvkId
     */
    int getZoneId();

    /**
     * @return 开服时间
     */
    long getServerOpenTsMs();

    /**
     * @return 当前赛季
     */
    CommonEnum.ZoneSeason getZoneSeason();

    /**
     * @return 当前赛季阶段
     */
    CommonEnum.ZoneSeasonStage getZoneSeasonStage();

    /**
     * @return zone side prop
     */
    ZoneSideProp getZoneSideProp();

    /**
     * @return 活动管理
     */
    AbsZoneSideActivityComponent<? extends AbstractEntity> getSideActivityComponent();

    /**
     * qlog所需
     */
    QlogServerFlowInterface getQlogComponent();

    /**
     * 加timer用
     */
    ActorTimerInterface getTimerComponent();

    /**
     * @return 通信的 actor
     */
    BaseGameActor ownerActor();

    /**
     * 获取原服大世界Scene
     *
     * @return null
     */
    BigSceneEntity getBigScene();
}

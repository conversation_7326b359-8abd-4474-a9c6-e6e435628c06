package com.yorha.cnc.scene.entity.collision;

import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.SystemClock;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class NormalCollisionWorld extends AbstractCollisionWorld {
    public NormalCollisionWorld(int width, int height) {
        super(width, height);
    }

    private long updateTsMs;

    @Override
    public boolean add(long id, Shape shape) {
        Collision collision = new Collision(shape);
        this.updateTsMs = SystemClock.now();
        return collisions.putIfAbsent(id, collision) == null;
    }

    @Override
    public boolean remove(long id) {
        Collision collision = collisions.remove(id);
        this.updateTsMs = SystemClock.now();
        return collision != null;
    }

    @Override
    public boolean isInCollision(Shape shape) {
        for (Collision collision : collisions.values()) {
            if (ShapeUtils.isContact(shape, collision.shape)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<Long, Shape> getAllCollisionByShape(Shape shape) {
        Map<Long, Shape> ret = new HashMap<>();
        for (Map.Entry<Long, Collision> entry : collisions.entrySet()) {
            if (ret.containsKey(entry.getKey())) {
                continue;
            }
            if (ShapeUtils.isContact(shape, entry.getValue().shape)) {
                ret.put(entry.getKey(), entry.getValue().shape);
            }
        }
        return ret;
    }

    @Override
    public boolean checkNeedRefreshPath(Point p, int radius, long t) {
        return updateTsMs > t;
    }
}

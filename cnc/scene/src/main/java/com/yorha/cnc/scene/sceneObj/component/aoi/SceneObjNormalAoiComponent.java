package com.yorha.cnc.scene.sceneObj.component.aoi;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.io.MsgType;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class SceneObjNormalAoiComponent extends SceneObjAoiComponent {
    private static final Logger LOGGER = LogManager.getLogger(SceneObjNormalAoiComponent.class);

    public SceneObjNormalAoiComponent(SceneObjEntity owner) {
        super(owner);
    }

    /**
     * 加入视野 加入阻挡 阻挡是跟视野同步的
     */
    @Override
    public void addIntoAoi(SceneObjectNtfReason reason) {
        if (isAddIntoAoi) {
            WechatLog.error("{} is already in aoi", getOwner());
            return;
        }
        if (getOwner().isDestroy()) {
            return;
        }
        isAddIntoAoi = true;
        broadcast(MsgType.ENTITYNTFMSG, getOwner().getPropComponent().getEntityNtfMsg(reason));
        SceneEntity scene = getOwner().getScene();
        if (!scene.isMainScene() || scene.isInitOk()) {
            LOGGER.info("{} add into aoi reason: {}", getOwner(), reason);
        }
        // 加入阻挡
        getOwner().getTransformComponent().addCollision();
    }

    /**
     * 离开视野 移除阻挡
     */
    @Override
    public void removeFromAoi(SceneObjectNtfReason reason) {
        // 非大世界模式的aoi 移除理由为依附性的 无视
        if (reason == SceneObjectNtfReason.SONR_ASSIST || reason == SceneObjectNtfReason.SONR_RALLY || reason == CommonEnum.SceneObjectNtfReason.SONR_COLLECT) {
            return;
        }
        if (!isAddIntoAoi) {
            WechatLog.error("{} is not in aoi", getOwner());
            return;
        }
        Entity.EntityNtfMsg.Builder delEntityMsgBuilder = Entity.EntityNtfMsg.newBuilder();
        delEntityMsgBuilder.setReason(reason).setZoneId(getOwner().getScene().getZoneId()).addDelEntities(getEntityId());
        broadcast(MsgType.ENTITYNTFMSG, delEntityMsgBuilder.build());
        isAddIntoAoi = false;
        LOGGER.info("{} remove from aoi reason: {}", getOwner(), reason);
        // 移除阻挡
        getOwner().getTransformComponent().removeCollision();
    }

    @Override
    public void broadcast(int msgType, GeneratedMessageV3 message) {
        // 没在场景了不广播了
        if (!isAddIntoAoi) {
            return;
        }
        getOwner().getScene().getPlayerMgrComponent().broadcastOnlineClientMsg(msgType, message);
    }

    @Override
    public void broadcastEntityModNtf(EntityAttrOuterClass.EntityAttr.Builder attrBuilder, boolean needMerge) {
        final Entity.EntityNtfMsg.Builder builder = Entity.EntityNtfMsg.newBuilder()
                .addModEntities(attrBuilder).setZoneId(getOwner().getScene().getZoneId());
        broadcast(MsgType.ENTITYNTFMSG, builder.build());
    }
}

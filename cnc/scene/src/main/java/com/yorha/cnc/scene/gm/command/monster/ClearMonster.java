package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * 清理地图上所有野怪
 *
 * <AUTHOR>
 */
public class ClearMonster implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        actor.getScene().getObjMgrComponent().gmClearMonster();
    }

    @Override
    public String showHelp() {
        return "ClearMonster playerId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MONSTER;
    }
}

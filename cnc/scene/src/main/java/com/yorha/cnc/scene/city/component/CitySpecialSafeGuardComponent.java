package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.event.ClanBuiltBuilldingPartChangeEvent;
import com.yorha.cnc.scene.event.city.CityMoveEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjSpecialSafeGuardComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.SpecialSafeGuardProp;
import com.yorha.proto.CommonEnum.SafeGuardReason;
import com.yorha.proto.CommonEnum.ZoneActivityEffect;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Collections;

public class CitySpecialSafeGuardComponent extends SceneObjSpecialSafeGuardComponent {
    private static final Logger LOGGER = LogManager.getLogger(CitySpecialSafeGuardComponent.class);

    public CitySpecialSafeGuardComponent(CityEntity owner) {
        super(owner);
    }

    private EventListener landProtectionCityMoveListener;
    private EventListener landProtectionClanChangeListener;
    private EventListener landProtectionClanPartChangeListener;

    @Override
    public CityEntity getOwner() {
        return (CityEntity) super.getOwner();
    }

    @Override
    public void postInit() {
        this.onLandProtectionActivityStart();   // 活动开始后创建的应能加入活动效果
    }

    @Override
    public void specialSafeGuardOn(SafeGuardReason safeGuardReason) {
        LOGGER.info("city:{} clan safe guard on reason:{}.", getOwner(), safeGuardReason);
        getSafeGuardProp().setIsOpenSafeGuard(true);
        getSafeGuardProp().getSafeGuardReasons().add(safeGuardReason.getNumber());
        // 加了罩子 强制结束战斗
        if (getOwner().getBattleComponent().isInBattle()) {
            getOwner().getBattleComponent().forceEndAllBattle();
        }
    }

    @Override
    public void specialSafeGuardOff(SafeGuardReason safeGuardReason) {
        if (!getSafeGuardProp().getSafeGuardReasons().contains(safeGuardReason.getNumber())) {
            return;
        }
        LOGGER.info("city:{} clan safe guard off reason:{}.", getOwner(), safeGuardReason);
        getSafeGuardProp().getSafeGuardReasons().remove(safeGuardReason.getNumber());
        if (getSafeGuardProp().getSafeGuardReasonsSize() == 0) {
            LOGGER.info("city:{} clan safe guard all off.", getOwner());
            getSafeGuardProp().setIsOpenSafeGuard(false);
        }
    }

    @Override
    public void specialSafeGuardChange(boolean isOn, SafeGuardReason safeGuardReason) {
        LOGGER.info("city:{} change clan safe guard from {} to {} reason:{}", getOwner(), getSafeGuardProp().getIsOpenSafeGuard(), isOn, safeGuardReason);
        if (isOn) {
            this.specialSafeGuardOn(safeGuardReason);
        } else {
            this.specialSafeGuardOff(safeGuardReason);
        }
    }

    @Override
    public boolean isSpecialSafeGuardOn() {
        return getSafeGuardProp().getIsOpenSafeGuard();
    }

    @Override
    public Collection<Integer> getSafeGuardReasons() {
        return Collections.unmodifiableCollection(getSafeGuardProp().getSafeGuardReasons().getValues());
    }

    public void loginCheckFortressGuard() {
        if (!getSafeGuardProp().getSafeGuardReasons().contains(SafeGuardReason.SGR_CLAN_FORTRESS.getNumber())) {
            return;
        }
        if (!isAdjoinClanFortress()) {
            specialSafeGuardOff(SafeGuardReason.SGR_CLAN_FORTRESS);
            LOGGER.info("loginCheckFortressGuard remove guard {}", getOwner());
        }
    }

    /**
     * 主堡迁城、落地、加盟   判断堡垒情况
     */
    public void checkAndRefreshFortressGuard() {
        if (isAdjoinClanFortress()) {
            specialSafeGuardOn(SafeGuardReason.SGR_CLAN_FORTRESS);
        } else {
            specialSafeGuardOff(SafeGuardReason.SGR_CLAN_FORTRESS);
        }
    }

    /**
     * 是否位于联盟堡垒邻近地块
     */
    public boolean isAdjoinClanFortress() {
        try {
            if (getOwner().getScenePlayer().getSceneClan() == null) {
                // 玩家本身未加入联盟拿不到联盟数据
                return false;
            }
            int partId = MapGridDataManager.getPartId(getOwner().getScene().getMapId(), getOwner().getCurPoint());
            return getOwner().getScenePlayer().getSceneClan().getBuildComponent().isInFortressProtectPart(partId);
        } catch (Exception e) {
            LOGGER.error("adjoin clan fortress check failed, ", e);
        }
        return false;
    }

    /**
     * 指定保护原因是否存在
     *
     * @param safeGuardReason city保护原因
     * @return boolean
     */
    private boolean safeGuardIsOnForReason(SafeGuardReason safeGuardReason) {
        return getSafeGuardProp().getSafeGuardReasons().contains(safeGuardReason.getNumber());
    }

    /**
     * 领土保护活动尝试打开保护（领土保护效果专用）
     */
    private void tryTurnOnSafeGuardForLandProtectionActivity() {
        // 不可添加 或 已添加
        if (!checkTurnOnSafeGuardForLandProtectionActivity() || this.safeGuardIsOnForReason(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY)) {
            return;
        }
        this.specialSafeGuardOn(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY);
    }

    /**
     * 领土保护活动开始（领土保护效果专用）
     */
    public void onLandProtectionActivityStart() {
        if (!this.getOwner().getScene().isBigScene()) {
            return;
        }
        // 直接拿会爆炸
        BigSceneEntity bigSceneEntity = (BigSceneEntity) this.getOwner().getScene();
        // 兜底：活动未开始
        if (!bigSceneEntity.getZoneEntity().getActivityComponent().isActiveActivityEffect(ZoneActivityEffect.ZAE_LAND_PROTECTION)) {
            return;
        }
        // 兜底：活动开始时就满足保护条件（目前为开服活动，应该无效）
        tryTurnOnSafeGuardForLandProtectionActivity();
        // 监听自己城市移动
        landProtectionCityMoveListener = this.getOwner().getEventDispatcher().addEventListenerRepeat(this::onCityMoveEventForLandProtectionActivity, CityMoveEvent.class);
        // 监听自身联盟变化
        landProtectionClanChangeListener = this.getOwner().getEventDispatcher().addEventListenerRepeat(this::onClanChangeEventForLandProtectionActivity, ClanChangeEvent.class);
        // 监听当前联盟领土变化
        this.addLandProtectionClanPartChangeListenerForCurClan();
    }

    /**
     * 领土保护活动结束（领土保护效果专用）
     */
    public void onLandProtectionActivityEnd() {
        // 关闭活动保护 & 清空动态监听
        this.specialSafeGuardOff(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY);
        if (landProtectionCityMoveListener != null) {
            landProtectionCityMoveListener.cancel();
            landProtectionCityMoveListener = null;
        }

        if (landProtectionClanChangeListener != null) {
            landProtectionClanChangeListener.cancel();
            landProtectionClanChangeListener = null;
        }

        if (landProtectionClanPartChangeListener != null) {
            landProtectionClanPartChangeListener.cancel();
            landProtectionClanPartChangeListener = null;
        }
    }

    /**
     * 检测是否可打开保护（领土保护效果专用）
     *
     * @return boolean 是否满足打开保护罩条件(活动效果生效 & 在联盟 & 城市在联盟建成的建筑领土)
     */
    private boolean checkTurnOnSafeGuardForLandProtectionActivity() {
        BigSceneEntity scene = (BigSceneEntity) getOwner().getScene();
        // 活动效果未生效
        if (!scene.getZoneEntity().getActivityComponent().isActiveActivityEffect(ZoneActivityEffect.ZAE_LAND_PROTECTION)) {
            return false;
        }

        long clanId = this.getOwner().getClanId();
        // 不在联盟内
        if (clanId == 0) {
            return false;
        }

        SceneClanEntity sceneClan = scene.getClanMgrComponent().getSceneClan(clanId);
        Point curPoint = this.getOwner().getCurPoint();
        int partId = MapGridDataManager.getPartId(getOwner().getScene().getMapId(), curPoint);
        // 在不在联盟建成的建筑领土上
        return sceneClan.getBuildComponent().isInBuiltBuildingPart(partId);
    }

    /**
     * 迁城事件逻辑（领土保护效果专用）
     *
     * @param event 迁城事件
     */
    private void onCityMoveEventForLandProtectionActivity(CityMoveEvent event) {
        this.updateShieldForLandProtectionActivity();
    }


    /**
     * 联盟变化事件逻辑（领土保护效果专用）
     *
     * @param event 联盟变化事件
     */
    private void onClanChangeEventForLandProtectionActivity(ClanChangeEvent event) {
        this.updateShieldForLandProtectionActivity();
        this.addLandProtectionClanPartChangeListenerForCurClan();
    }

    /**
     * 监听当前联盟领土变化（领土保护效果专用）
     */
    private void addLandProtectionClanPartChangeListenerForCurClan() {
        // 兜底：空指针
        if (landProtectionClanPartChangeListener != null) {
            landProtectionClanPartChangeListener.cancel();
            landProtectionClanPartChangeListener = null;
        }
        // 兜底：没有加入联盟
        if (this.getOwner().getClanId() == 0) {
            return;
        }
        SceneClanEntity curClan = this.getOwner().getScene().getClanMgrComponent().getSceneClan(this.getOwner().getClanId());
        // 兜底：没有联盟
        if (curClan != null) {
            landProtectionClanPartChangeListener = curClan.getEventDispatcher().addEventListenerRepeat(this::onClanPartChangeEventForLandProtectionActivity, ClanBuiltBuilldingPartChangeEvent.class);
        }
    }

    /**
     * 联盟领土变化事件逻辑（领土保护效果专用）
     *
     * @param event 联盟领土变化事件
     */
    private void onClanPartChangeEventForLandProtectionActivity(ClanBuiltBuilldingPartChangeEvent event) {
        Point curPoint = this.getOwner().getCurPoint();
        int partId = MapGridDataManager.getPartId(getOwner().getScene().getMapId(), curPoint);
        // 非city所在part
        if (partId != event.getPartId()) {
            LOGGER.info("city: {} changedPartId={} not related", this.getOwner(), event.getPartId());
            return;
        }
        // 兜底：非本联盟
        if (event.getClanId() != this.getOwner().getClanId()) {
            LOGGER.info("city: {} clanId={} not related", this.getOwner(), event.getClanId());
            return;
        }
        LOGGER.info("city: {} partId={} isOwned={}", this.getOwner(), event.getPartId(), event.isOwned());
        // 新拥有part，有机会开启
        if (event.isOwned()) {
            tryTurnOnSafeGuardForLandProtectionActivity();
        } else {
            this.specialSafeGuardOff(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY);
        }
    }

    /**
     * 更新护盾（领土保护效果专用）
     */
    public void updateShieldForLandProtectionActivity() {
        long clanId = this.getOwner().getClanId();
        if (clanId == 0) {
            this.specialSafeGuardOff(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY);
            return;
        }
        SceneClanEntity curClan = this.getOwner().getScene().getClanMgrComponent().getSceneClan(clanId);
        // 兜底：空指针（clan不存在）
        if (curClan == null) {
            this.specialSafeGuardOff(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY);
            return;
        }
        Point curPoint = this.getOwner().getCurPoint();
        if (curClan.getMapBuildingComponent().isMyTerritory(curPoint.getX(), curPoint.getY())) {
            tryTurnOnSafeGuardForLandProtectionActivity();
        } else {
            this.specialSafeGuardOff(SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY);
        }
    }

    private SpecialSafeGuardProp getSafeGuardProp() {
        return getOwner().getProp().getSafeGuard();
    }
}

package com.yorha.cnc.scene.gm.command.peaceShield;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 给所有人加罩子
 *
 * <AUTHOR>
 */
public class ProtectAll implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        long durationSec = Long.parseLong(args.get("seconds"));
        actor.getScene().getPeaceShieldComponent().openGlobalPeaceShield(durationSec);
    }

    @Override
    public String showHelp() {
        return "ProtectAll seconds={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

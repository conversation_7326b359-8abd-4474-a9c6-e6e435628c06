package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.BuildingTransformComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Shape;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 * <p>
 * 地图建筑位置管理
 */
public class MapBuildingTransformComponent extends BuildingTransformComponent {
    /**
     * 占据AOI视野的形状圆
     */
    private final AABB aoiShape;

    public MapBuildingTransformComponent(MapBuildingEntity owner, PointProp pointProp, int partId) {
        super(owner, owner, pointProp);
        aoiShape = MapGridDataManager.getPartAABB(owner.getScene().getMapId(), partId);
    }

    public int getBindRegionId() {
        return -1;
    }

    public Pair<Integer, Integer> getDarkAltarBindRegion() {
        return null;
    }

    public boolean isDarkAltarCross() {
        return getDarkAltarBindRegion() != null;
    }

    @Override
    public Shape getSelfShape() {
        return aoiShape;
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }
}

package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.city.CityBornService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.proto.CommonEnum.MapAreaType;
import it.unimi.dsi.fastutil.ints.Int2LongOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BornAreaSelectTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 出生管理
 *
 * <AUTHOR>
 */
public class MainSceneBornMgrComponent extends AbstractComponent<SceneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneBornMgrComponent.class);

    /**
     * partId -> num
     */
    private final Map<Integer, Integer> partToNum = new HashMap<>();
    /**
     * gridId -> cityId
     */
    private final Map<Integer, Long> occupiedGrids = new Int2LongOpenHashMap();
    /**
     * 出生管理单元
     * regionId -> item
     */
    protected final Map<Integer, MainSceneBornRegionItem> regionItemMap = new HashMap<>();

    public MainSceneBornMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void initBornItem() {
        for (int i = 0; i < getOwner().getRegionNum(); i++) {
            regionItemMap.put(i, new MainSceneBornRegionItem(i, getOwner()));
        }
        Map<Pair<MapAreaType, Integer>, Integer> areaSelect = ResHolder.getResService(CityBornService.class).getAreaSelect();
        GeminiStopWatch geminiStopWatch = new GeminiStopWatch("init big scene born point");
        // 构建落城数据
        for (RegionalAreaSettingTemplate it : getOwner().getMapTemplateDataItem().getMap(RegionalAreaSettingTemplate.class).values()) {
            Pair<MapAreaType, Integer> key = Pair.of(it.getAreaType(), it.getLevel());
            if (!areaSelect.containsKey(key)) {
                continue;
            }
            // 加入可选片
            tryAddToPartPool(it);
            // 生成点
            regionItemMap.get(it.getRegionId()).initPointList(it);
        }
        // 统计输出
        int total = 0;
        Map<Integer, Integer> map = new HashMap<>();
        for (MainSceneBornRegionItem item : regionItemMap.values()) {
            total += item.getTotalPointNum();
            map.put(item.getRegionId(), item.getTotalPointNum());
        }
        geminiStopWatch.mark("end");
        LOGGER.info("scene born point init ok, cost={} total={} region_total={}", geminiStopWatch.getTotalCost(), total, map);
    }

    private void tryAddToPartPool(RegionalAreaSettingTemplate template) {
        BornAreaSelectTemplate template2 = ResHolder.getResService(CityBornService.class).getBornAreaSelectTemplate(template.getAreaType(), template.getLevel());
        // 不是导量可选片 不管
        if (template2 == null) {
            return;
        }
        if (partToNum.getOrDefault(template.getId(), 0) < template2.getLimit()) {
            if (!regionItemMap.containsKey(template.getRegionId())) {
                LOGGER.error("tryAddToPartPool failed {}", template.getRegionId());
                return;
            }
            regionItemMap.get(template.getRegionId()).addToSelectPartList(template.getAreaType(), template.getLevel(), template.getId());
        }
    }

    public int getPartToNum(int partId) {
        return partToNum.getOrDefault(partId, 0);
    }

    public Map<Integer, Integer> getAllPartToNum() {
        return partToNum;
    }

    public int getRegionPlayerNum(int regionId) {
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            return 0;
        }
        return item.getCityNum();
    }

    public Point chooseCityRandomPoint(int regionId, CityEntity city) {
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            LOGGER.error("chooseCityRandomPoint failed playerId:{} region: {} city: {}", city.getPlayerId(), regionId, city);
            return null;
        }
        return item.choosePartPoint(false);
    }

    /**
     * 获取占领地格的对象
     */
    public CityEntity getOccupiedCity(int gridId) {
        long cityId = occupiedGrids.getOrDefault(gridId, 0L);
        if (cityId == 0) {
            return null;
        }
        return getOwner().getObjMgrComponent().getSceneObjWithType(CityEntity.class, cityId);
    }

    /**
     * 占领格子
     */
    public void occupy(Point point, CityEntity city) {
        int gridId = MapGridDataManager.getGridId(getOwner().getMapId(), point);
        int partId = MapGridDataManager.getPartId(getOwner().getMapId(), gridId);
        int regionId = MapGridDataManager.getRegionId(getOwner().getMapId(), point);
        partToNum.put(partId, partToNum.getOrDefault(partId, 0) + 1);
        occupiedGrids.put(gridId, city.getEntityId());
        if (regionItemMap.containsKey(regionId)) {
            regionItemMap.get(regionId).incActualCityNum();
        }
        // 恢复city阶段 还没初始化 不用管
        if (!getOwner().isInitOk()) {
            return;
        }
        changeBornSelectPool(partId, true);
    }

    /**
     * 取消占领
     */
    public void cancelOccupy(Point point, CityEntity city) {
        int gridId = MapGridDataManager.getGridId(getOwner().getMapId(), point);
        int partId = MapGridDataManager.getPartId(getOwner().getMapId(), gridId);
        int regionId = MapGridDataManager.getRegionId(getOwner().getMapId(), point);
        if (occupiedGrids.getOrDefault(gridId, 0L) == city.getEntityId()) {
            occupiedGrids.remove(gridId);
            if (partToNum.getOrDefault(partId, 0) != 0) {
                partToNum.put(partId, partToNum.getOrDefault(partId, 0) - 1);
            } else {
                LOGGER.error("cancelOccupy try minus partToNum regionId={} playerNum==0 but cancelOccupy, point={} cityPlayerId={}", regionId, point, city.getPlayerId());
            }
            MainSceneBornRegionItem item = regionItemMap.get(regionId);
            if (item != null && item.getActualCityNum() != 0) {
                item.decActualCityNum();
            } else {
                LOGGER.error("cancelOccupy try minus regionPlayerNum regionId={} playerNum==0 but cancelOccupy, point={} cityPlayerId={}", regionId, point, city.getPlayerId());
            }
            changeBornSelectPool(partId, false);
        }
    }

    /**
     * 城池坐标占位和非占位的 对可选片的影响
     */
    private void changeBornSelectPool(Integer partId, boolean isOccupy) {
        RegionalAreaSettingTemplate template = getOwner().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        BornAreaSelectTemplate template2 = ResHolder.getResService(CityBornService.class).getBornAreaSelectTemplate(template.getAreaType(), template.getLevel());
        // 不是导量可选片 不管
        if (template2 == null) {
            return;
        }
        MainSceneBornRegionItem item = regionItemMap.get(template.getRegionId());
        if (item == null) {
            LOGGER.error("changeBornSelectPool failed {}", template.getRegionId());
            return;
        }
        if (!isOccupy && partToNum.get(partId) == template2.getLimit() - 1) {
            item.addToSelectPartList(template.getAreaType(), template.getLevel(), template.getId());
        }
        if (isOccupy && partToNum.get(partId) >= template2.getLimit()) {
            item.removeFromSelectPartList(template.getAreaType(), template.getLevel(), template.getId());
        }
    }

    /**
     * 主堡计数
     */
    public void addCityNum(int regionId) {
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            LOGGER.error("addCityNum but region is null {}", regionId);
            return;
        }
        item.incCityNum();
    }

    /**
     * 主堡计数
     */
    public void decCityNum(int regionId) {
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            LOGGER.error("decCityNum but region is null {}", regionId);
            return;
        }
        item.decCityNum();
    }

    /**
     * 主堡计数
     */
    public void changeCityNum(int oldRegionId, int newRegionId) {
        MainSceneBornRegionItem oldRegion = regionItemMap.get(oldRegionId);
        if (oldRegion == null || oldRegion.getCityNum() == 0) {
            LOGGER.error("changeCityNum old is 0 {} {}", oldRegionId, newRegionId);
            return;
        }
        MainSceneBornRegionItem newRegion = regionItemMap.get(newRegionId);
        if (newRegion == null) {
            LOGGER.error("changeCityNum new is null {} {}", oldRegionId, newRegionId);
            return;
        }
        oldRegion.decCityNum();
        newRegion.incCityNum();
    }
}

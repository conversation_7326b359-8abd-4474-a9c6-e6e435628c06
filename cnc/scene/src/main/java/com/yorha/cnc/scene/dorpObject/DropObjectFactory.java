package com.yorha.cnc.scene.dorpObject;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.resource.resservice.drop.DropTemplateService;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemRewardBox;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.DropObjectProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.StructPB;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.DropGroupTemplate;
import res.template.DropObjectTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class DropObjectFactory {
    private static final Logger LOGGER = LogManager.getLogger(DropObjectFactory.class);

    public static DropObjectEntity initDropObject(SceneEntity scene, StructPB.DropGroupLimitPB limitPb, int templateId, Point bornPoint, long ownerId) {
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(bornPoint, scene.getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        DropObjectProp prop = new DropObjectProp();
        prop.getPoint().setX(bornPoint.getX()).setY(bornPoint.getY());
        DropObjectTemplate template = ResHolder.getResService(DropTemplateService.class).getDropTemplate(templateId);
        if (template == null) {
            return null;
        }
        prop.setTemplateId(templateId);
        long bornTime = SystemClock.now();
        // 出生动画结束之后才真正算作出生，可以被拾取
        if (template.getApprearAnimationTime() > 0) {
            bornTime += template.getApprearAnimationTime();
        }
        prop.setBornTime(bornTime);
        prop.setOwnerId(ownerId);
        // 掉落组拾取限制(针对相同来源的，同一个时间点)
        if (limitPb != null) {
            prop.getGroupLimit().mergeFromCs(limitPb);
        }

        // 固定拾取奖励
        if (template.getDropObjectType() == CommonEnum.DropObjectType.ITEM && template.getReward() > 0) {
            List<ItemReward> rewards = ResHolder.getResService(ItemResService.class).randomReward(template.getReward());
            if (CollectionUtils.isNotEmpty(rewards)) {
                prop.getItemReward().mergeFromCs(ItemResService.makeItemListPB(rewards).build());
            }
        }

        long objId = scene.ownerActor().nextId();
        DropObjectBuilder dropObjectBuilder = new DropObjectBuilder(scene, objId, prop);
        return new DropObjectEntity(dropObjectBuilder);
    }

    public static List<DropObjectEntity> initDropGroup(SceneEntity scene, int groupId, Point bornPoint) {
        DropGroupTemplate groupTemplate = ResHolder.getResService(DropTemplateService.class).getDropGroupTemplate(groupId);
        if (groupTemplate == null) {
            return Collections.emptyList();
        }
        if (bornPoint == null) {
            WechatLog.error("init drop group {} born point not exist", groupId);
            return Collections.emptyList();
        }

        // 掉落组信息
        StructPB.DropGroupLimitPB groupLimitPb = null;
        if (groupTemplate.getOverTime() != 0) {
            // 有结束时间才限制  没有结束时间不管了
            groupLimitPb = StructPB.DropGroupLimitPB.newBuilder()
                    .setGroupId(groupTemplate.getId())
                    .setOverTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(groupTemplate.getOverTime()))
                    .build();
        }

        Circle circle = Circle.valueOf(bornPoint.getX(), bornPoint.getY(), groupTemplate.getRadius());
        // 获取周边的掉落物
        List<DropObjectEntity> drops = new ArrayList<>();
        scene.getAoiMgrComponent().consumerAffectSceneObj(
                circle,
                (obj) -> {
                    if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_DropObject) {
                        drops.add((DropObjectEntity) obj);
                    }
                }
        );

        PathFindMgrComponent pathFindMgr = scene.getPathFindMgrComponent();
        List<DropObjectEntity> dropList = new ArrayList<>();
        StructPB.DropGroupLimitPB finalGroupLimitPb = groupLimitPb;
        ItemRewardBox rewardBox = new ItemRewardBox();
        for (IntTripleType tripleType : groupTemplate.getDropListTripleList()) {
            rewardBox.addReward(ItemReward.newBuilder().setItemTemplate(tripleType.getKey()).setCount(tripleType.getValue1()).setWeight(tripleType.getValue2()).build());
        }
        ItemReward dropObject = rewardBox.reward();
        if (dropObject == null) {
            LOGGER.info("initDropGroup group:{}, weight:{}", groupTemplate.getId(), rewardBox.getWeight());
            return new ArrayList<>();
        }
        // 掉落物概率掉落
        DropObjectTemplate template = ResHolder.getResService(DropTemplateService.class).getDropTemplate(dropObject.getItemTemplateId());
        if (template == null) {
            return new ArrayList<>();
        }

        for (int i = 0; i < dropObject.getCount(); i++) {
            // 半径范围内随机出生点
            Point point = null;
            for (int j = 0; j < 10; j++) {
                int radius = RandomUtils.nextInt(groupTemplate.getRadius() + 100);
                Circle circle0 = Circle.valueOf(bornPoint.getX(), bornPoint.getY(), radius);
                Point point0 = circle0.getRandomPoint();
                if (!pathFindMgr.isPointDynamicWalkable(point0) || !pathFindMgr.isPointStaticWalkable(point0)) {
                    continue;
                }
                point = point0;
                break;
            }

            if (point == null) {
                continue;
            }
            int partId = MapGridDataManager.getPartId(scene.getMapId(), point);

            if (template.getDropObjectType() == CommonEnum.DropObjectType.BUFF) {
                if (scene.isBigScene()) {
                    if (!scene.getBigScene().getObjMgrComponent().canDropRune(partId)) {
                        continue;
                    }
                }

            }

            DropObjectEntity entity = DropObjectFactory.initDropObject(scene, finalGroupLimitPb, template.getId(), point, 0);
            if (entity == null) {
                continue;
            }
            entity.setCost(template.getCost());
            drops.add(entity);
            dropList.add(entity);
            if (template.getDropObjectType() == CommonEnum.DropObjectType.BUFF) {
                if (scene.isBigScene()) {
                    scene.getBigScene().getObjMgrComponent().addDropRune(partId);
                }
            }
        }

        return dropList;
    }
}

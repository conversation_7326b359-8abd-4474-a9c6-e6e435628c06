package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class FinishMapBuildingBuild implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int partId = 0;
        String key = "partId";
        if (args.containsKey(key)) {
            partId = Integer.parseInt(args.get(key));
        }
        SceneEntity scene = actor.getScene();
        SceneClanEntity sceneClan = scene.getPlayerMgrComponent().getScenePlayer(playerId).getSceneClan();
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_IN);
        }
        MapBuildingEntity mapBuilding = scene.getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            throw new GeminiException(ErrorCode.MAP_GRID_NOT_EXIST);
        }
        if (mapBuilding.getProp().getOccupyinfo().getState() != CommonEnum.OccupyState.TOS_REBUILD) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        mapBuilding.getStageMgrComponent().gmCompleteBuild();
    }

    @Override
    public String showHelp() {
        return "FinishMapBuildingBuild partId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}

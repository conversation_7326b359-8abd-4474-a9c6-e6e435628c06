package com.yorha.cnc.scene.cave;

import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.CaveProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.TcaplusDb;
import res.template.MapBuildingTemplate;

/**
 * 山洞
 *
 * <AUTHOR>
 */
public class CaveEntity extends SceneObjEntity implements BuildingEntityType {
    private final CaveProp prop;

    public CaveEntity(CaveBuilder builder) {
        super(builder);
        this.prop = builder.getProp();
        initAllComponents();
        getPropComponent().initPropListener(false);
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Cave;
    }
    
    @Override
    public CaveProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getCaveAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getCaveAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getCaveAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getCaveAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getCaveAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final CaveProp caveProp = CaveProp.of(builder.getFullAttr().getCaveAttr(), builder.getChangedAttr().getCaveAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setCaveAttr(caveProp.getCopyDbBuilder());
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return null;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return null;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return null;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public boolean briefEntityAttr(Entity.SceneObjBriefAttr.Builder builder) {
        builder.getCaveAttrBuilder()
                .setConfigId(getProp().getConfigId())
                .getPointBuilder().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        return true;
    }

    @Override
    public String toString() {
        return "Cave{" + "point=" + prop.getPoint()
                + " templateId=" + prop.getTemplateId();
    }

    @Override
    public long getPlayerId() {
        return 0;
    }

    @Override
    public long getClanId() {
        return 0;
    }

    @Override
    public CommonEnum.Camp getCampEnum() {
        return CommonEnum.Camp.C_NONE;
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, getProp().getTemplateId());
    }
}

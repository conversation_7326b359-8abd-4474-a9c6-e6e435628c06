package com.yorha.cnc.zone.activity;

import com.yorha.common.helper.SeasonActivityHelper;
import com.yorha.game.gen.prop.ZoneSideLotteryModelProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneActivitySchedule;

/**
 * 幸运大转盘赛季版活动主题
 *
 * <AUTHOR>
 */
public class ZoneLotteryUnit extends BaseZoneActivityUnit {
    public ZoneLotteryUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }


    private ZoneSideLotteryModelProp getProp() {
        return owner.owner().getZoneSideProp().getActivityModel().getLotteryModel();
    }

    @Override
    public void load(boolean isInitial) {
        if (isInitial) {
            ZoneSideLotteryModelProp prop = getProp();

            CommonEnum.ZoneSeason oldSeason = prop.getCurSeason();
            CommonEnum.ZoneSeasonStage oldStage = prop.getCurStage();
            CommonEnum.ZoneSeason curSeason = owner.owner().getZoneSeason();
            CommonEnum.ZoneSeasonStage curStage = owner.owner().getZoneSeasonStage();
            LOGGER.info("ZoneLotteryUnit init {} {} -> {} {} activityId={} oldVolume={}", oldSeason, oldStage, curSeason, curStage, activityId, prop.getCurVolume());

            final CommonEnum.CommanderActivtyStage oldActStage = SeasonActivityHelper.getActStage(oldSeason, oldStage);

            final CommonEnum.CommanderActivtyStage curActStage = SeasonActivityHelper.getActStage(curSeason, curStage);
            if (oldActStage != curActStage) {
                prop.setCurVolume(0);
            }
            prop.setCurSeason(curSeason).setCurStage(curStage).setCurVolume(prop.getCurVolume() + 1);
        }
    }


    @Override
    public void onExpire() {

    }

    @Override
    public void forceOffImpl() {

    }

    public SsSceneActivitySchedule.GetLotteryInfoAns getLotteryInfo() {
        SsSceneActivitySchedule.GetLotteryInfoAns.Builder builder = SsSceneActivitySchedule.GetLotteryInfoAns.newBuilder();
        ZoneSideLotteryModelProp prop = getProp();
        return builder.setSeason(prop.getCurSeason()).setVolume(prop.getCurVolume()).setStage(prop.getCurStage()).build();
    }
}

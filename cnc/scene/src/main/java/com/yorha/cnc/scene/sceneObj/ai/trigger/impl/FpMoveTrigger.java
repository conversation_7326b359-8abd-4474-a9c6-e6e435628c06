package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;


import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 定点移动
 * <AUTHOR>
 */
public class FpMoveTrigger extends AiTrigger {
    private List<IntPairType> params;
    public FpMoveTrigger(AiStateTriggerTemplate template) {
        super(template);
    }


    @Override
    protected void parse(List<IntPairType> param) {
        this.params = new ArrayList<>();
        this.params.addAll(param);
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        return true;
    }


    @Override
    protected void doTrigger(SceneObjEntity owner) {
        moveToPathPoint(owner);
    }

    private void moveToPathPoint(SceneObjEntity owner) {
        int circleNum = 0;
        // 当前走到路点中的索引
        int index = 0;
        Object record2 = owner.getAiComponent().tryGetRecord(CommonEnum.AiRecordType.ART_MOVE_INDEX);
        if (record2 != null) {
            index = (int)record2;
        }
        if (index >= (params.size() - 1)) {
            owner.getAiComponent().addRecord(CommonEnum.AiRecordType.ART_MOVE_INDEX, 0);
            owner.getAiComponent().addRecord(CommonEnum.AiRecordType.ART_MOVE_TIMES, circleNum + 1);
        }

        Object record1 = owner.getAiComponent().tryGetRecord(CommonEnum.AiRecordType.ART_MOVE_TIMES);
        if (record1 != null) {
            circleNum = (int)record1;
        }
        // 定点移动走完的圈数，达到触发上限则不继续了
        int limit = getTemplate().getCount();
        if ((limit > 0) && (circleNum >= limit)){
            return;
        }

        IntPairType pair = params.get(index);
        if ((owner.getAiComponent().isDebugAble())) {
            LOGGER.info("{}, moveToPathPoint :{} {}", getLogHead(owner), pair.getKey(), pair.getValue());
        }
        owner.getMoveComponent().moveToPointAsyncIgnoreException(Point.valueOf(pair.getKey(), pair.getValue()), ()->{
            // 记录走完本路点了
            int lastIndex = 0;
            Object record3 = owner.getAiComponent().tryGetRecord(CommonEnum.AiRecordType.ART_MOVE_INDEX);
            if (record3 != null) {
                lastIndex = (int)record3;
            }
            owner.getAiComponent().addRecord(CommonEnum.AiRecordType.ART_MOVE_INDEX, lastIndex + 1);
            // 递归走下一个路点
            moveToPathPoint(owner);
        });
    }

    @Override
    protected String getTriggerParam() {
        return params.toString();
    }

    @Override
    protected String getTriggerName() {
        return "FpMoveTrigger";
    }
}

package com.yorha.cnc.scene.mapBuilding.component.stagenode.normal;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.OccupyState;

/**
 * 半开放状态  只有外圈到中圈的关隘有
 *
 * <AUTHOR>
 */
public class SemiOpenStageNode extends StageNode {
    public SemiOpenStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_SEMI_OPEN;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() == 0) {
            return;
        }
        onEnter(0);
    }

    @Override
    public void onEnter(long enterTs) {
        // 设置属性
        getProp().setState(getStage()).setStateStartTsMs(enterTs);
        if (getProp().getStateEndTsMs() == 0) {
            return;
        }
        if (getProp().getStateEndTsMs() <= SystemClock.now()) {
            enterNeutral();
            return;
        }
        addStageTimer(this::enterNeutral);
    }

    private void enterNeutral() {
        getComponent().clearStageTimer();
        getComponent().transNeutralStage();
    }

    @Override
    public String toString() {
        return ClassNameCacheUtils.getSimpleName(getClass());
    }
}

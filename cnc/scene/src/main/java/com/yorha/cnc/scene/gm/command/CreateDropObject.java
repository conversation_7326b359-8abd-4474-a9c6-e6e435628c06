package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectFactory;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum.DebugGroup;
import res.template.DropObjectTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 创建掉落物
 */
public class CreateDropObject implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int x = Integer.parseInt(args.get("x"));
        int y = Integer.parseInt(args.get("y"));
        int templateId = Integer.parseInt(args.get("templateId"));
        boolean owner = Boolean.parseBoolean(args.get("owner"));

        DropObjectTemplate template = ResHolder.getInstance().findValueFromMap(DropObjectTemplate.class, templateId);
        if (template == null) {
            return;
        }
        DropObjectEntity dropObject = DropObjectFactory.initDropObject(actor.getScene(), null, templateId, Point.valueOf(x, y), owner ? playerId : 0L);
        if (dropObject == null) {
            throw new GeminiException(ErrorCode.DROP_OBJECT_NOT_BORN.getCodeId());
        }
        dropObject.addIntoScene();
    }

    @Override
    public String showHelp() {
        return "CreateDropObject x={midScreenX} y={midScreenY} templateId=1 owner=false";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MONSTER;
    }
}

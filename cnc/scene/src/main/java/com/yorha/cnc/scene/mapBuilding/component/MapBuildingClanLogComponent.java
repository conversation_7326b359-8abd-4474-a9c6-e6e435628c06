package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * <p>
 * 地图建筑军团日志相关
 */
public class MapBuildingClanLogComponent extends SceneObjComponent<MapBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingClanLogComponent.class);

    public MapBuildingClanLogComponent(MapBuildingEntity owner) {
        super(owner);
    }

    /**
     * 开始攻击某领地建筑的军团日志
     *
     * @param templateId 建筑templateId
     * @param clanId     需要打日志的军团id
     * @param point      位置信息
     */
    public void recordStartAttackLog(int templateId, long clanId, Point point) {
        SsClanAttr.CreateClanLogCmd.Builder cmdBuilder = SsClanAttr.CreateClanLogCmd.newBuilder();
        SsClanAttr.StartAttackLogMsg.Builder startAttackLogBuilder = SsClanAttr.StartAttackLogMsg.newBuilder();
        startAttackLogBuilder.setTemplateId(templateId)
                .setPoint(genPoint(point.getX(), point.getY(), getOwner().getScene()));
        cmdBuilder.setRecordType(CommonEnum.ClanRecordType.CRT_LOG_START_ATTACK).setStartAttackLogMsg(startAttackLogBuilder);
        SceneClanEntity sceneClanOrNull = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClanOrNull != null) {
            sceneClanOrNull.tellClan(cmdBuilder.build());
        }
    }

    /**
     * 领地建筑遭到某人攻击的军团日志
     *
     * @param templateId       建筑templateId
     * @param otherScenePlayer 攻击者
     * @param clanId           需要打日志的军团id
     * @param point            位置信息
     */
    public void recordBeAttackedLog(int templateId, AbstractScenePlayerEntity otherScenePlayer, long clanId, Point point) {
        if (null == otherScenePlayer) {
            LOGGER.warn("recordBeAttackedLog: can not find scene player when try record clan log");
            return;
        }
        String otherPlayerName = otherScenePlayer.getName();
        long otherClanId = otherScenePlayer.getClanId();
        SceneClanEntity sceneClanEntity = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(otherClanId);
        if (null == sceneClanEntity) {
            LOGGER.warn("recordBeAttackedLog: can not find scene clan {} when try record clan log", otherClanId);
            return;
        }
        SsClanAttr.CreateClanLogCmd.Builder cmdBuilder = SsClanAttr.CreateClanLogCmd.newBuilder();
        SsClanAttr.BeAttackedLogMsg.Builder beAttackedLogBuilder = SsClanAttr.BeAttackedLogMsg.newBuilder();
        beAttackedLogBuilder.setTemplateId(templateId).setClanSName(sceneClanEntity.getClanSimpleName()).setAttackerPlayerName(otherPlayerName)
                .setPoint(genPoint(point.getX(), point.getY(), getOwner().getScene()));
        cmdBuilder.setRecordType(CommonEnum.ClanRecordType.CRT_LOG_BUILDING_ATTACKED).setBeAttackedLogMsg(beAttackedLogBuilder);
        SceneClanEntity sceneClanOrNull = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClanOrNull != null) {
            sceneClanOrNull.tellClan(cmdBuilder.build());
        }
    }

    /**
     * 占领的军团日志
     *
     * @param templateId        建筑templateId
     * @param lastHitPlayerName 最后一击的玩家名字
     * @param clanId            需要打日志的军团id
     * @param point             位置信息
     */
    public void recordOccupyLog(int templateId, String lastHitPlayerName, long clanId, Point point) {
        SsClanAttr.CreateClanLogCmd.Builder cmdBuilder = SsClanAttr.CreateClanLogCmd.newBuilder();
        SsClanAttr.OccupyLogMsg.Builder occupyLogBuilder = SsClanAttr.OccupyLogMsg.newBuilder();
        occupyLogBuilder.setTemplateId(templateId).setLastHitPlayerName(lastHitPlayerName)
                .setPoint(genPoint(point.getX(), point.getY(), getOwner().getScene()));
        cmdBuilder.setRecordType(CommonEnum.ClanRecordType.CRT_LOG_OCCUPY).setOccupyLogMsg(occupyLogBuilder);
        SceneClanEntity sceneClanOrNull = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClanOrNull != null) {
            sceneClanOrNull.tellClan(cmdBuilder.build());
        }
    }
    
    public static Struct.Point genPoint(int x, int y, SceneEntity scene) {
        return Struct.Point.newBuilder()
                .setX(x)
                .setY(y)
                .setMapType(scene.getMapType().getNumber())
                .setMapId(scene.getMapIdForPoint())
                .build();
    }
}

package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;

/**
 * 普通活跃野怪
 *
 * <AUTHOR>
 */
public class NormalActiveMonster implements MonsterFeature {

    @Override
    public void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity) {

    }

    @Override
    public ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        AbstractScenePlayerEntity otherScenePlayer = monsterEntity.getScene().getPlayerMgrComponent().getScenePlayerOrNull(attackerObj.getPlayerId());
        if (otherScenePlayer != null && !otherScenePlayer.checkMonsterKillLevel(monsterEntity.getLevel())) {
            return ErrorCode.MONSTER_KILL_LEVEL_LIMIT;
        }
        return ErrorCode.OK;
    }

    @Override
    public ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        // 大世界野怪等级限制
        if (!attackerPlayer.checkMonsterKillLevel(monsterEntity.getLevel())) {
            return ErrorCode.MONSTER_KILL_LEVEL_LIMIT;
        }
        return ErrorCode.OK;
    }

    @Override
    public void onDead(MonsterEntity monsterEntity) {

    }

    @Override
    public void sendKillAndRewardAll(MonsterEntity monsterEntity) {
        monsterEntity.getRewardComponent().sendBaseReward();
    }
}

package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.NeutralStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.DesertedStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.InCommandNetStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.OccupyingStageNode;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.OccupyInfoProp;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TerritoryBuildingTemplate;

/**
 * 占领接口相关
 *
 * <AUTHOR>
 */
public class MapBuildingOccupyComponent extends SceneObjComponent<MapBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingOccupyComponent.class);

    public MapBuildingOccupyComponent(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        SceneClanEntity ownerSceneClan = getOwnerSceneClan();
        SceneClanEntity occupySceneClan = getOccupySceneClan();
        // 保护下 记录的联盟没了怎么办
        // 占领联盟没了
        if (getOccupyClanId() != 0 && occupySceneClan == null) {
            WechatLog.error("{} occupySceneClan lose!! clanId:{}", getOwner(), getOccupyClanId());
            //这时候还没有初始化stageNode  所以只是为了清理下数据
            new NeutralStageNode(getOwner()).onEnter(SystemClock.now());
            if (getOwner().getProp().getConstructInfo().getBeforeRebuildTemplateId() != 0) {
                getOwner().getStageMgrComponent().clearRebuildProp();
            }
        }
        // 拥有的联盟没了
        if (getOwnerClanId() != 0 && ownerSceneClan == null) {
            WechatLog.error("{} ownerSceneClan lose!! clanId:{}", getOwner(), getOccupyInfoProp().getOwnerClanId());
            if (occupySceneClan == null) {
                //这时候还没有初始化stageNode  所以只是为了清理下数据
                new NeutralStageNode(getOwner()).onEnter(SystemClock.now());
                if (getOwner().getProp().getConstructInfo().getBeforeRebuildTemplateId() != 0) {
                    getOwner().getStageMgrComponent().clearRebuildProp();
                }
            } else {
                getOccupyInfoProp().setOwnerClanId(0);
            }
        }
        OccupyState state = getOccupyInfoProp().getState();
        // 状态不对等脏数据的错误恢复
        if (state == OccupyState.TOS_OCCUPYING && getOccupyClanId() == 0) {
            LOGGER.info("restore but prop dirty 1 {}", getOwner());
            //这时候还没有初始化stageNode  所以只是为了清理下数据
            new NeutralStageNode(getOwner()).onEnter(SystemClock.now());
        }
        if (state == OccupyState.TOS_NEUTRAL) {
            if (getOccupyClanId() != 0 || getOwnerClanId() != 0) {
                LOGGER.info("restore but prop dirty 2 {} {} {}", getOwner(), getOccupyClanId(), getOwnerClanId());
                getOwner().getStageMgrComponent().clearShowClan();
            }
        }
        // 通知scene clan  加入列表，纳入管理
        // 有归属方
        if (ownerSceneClan != null) {
            // 加入归属列表
            ownerSceneClan.getMapBuildingComponent().addToOwner(getOwner(), true);
            // kvk 兼容线上数据
            if (getOwner().getZoneId() == 0) {
                getOwner().getProp().getOccupyinfo().setZoneId(ownerSceneClan.getZoneId());
            }
        }
        // 占领中
        if (occupySceneClan != null) {
            // 加入正在占领列表
            occupySceneClan.getMapBuildingComponent().addToOccupying(getOwner());
            // kvk 兼容线上数据
            if (getOwner().getZoneId() == 0) {
                getOwner().getProp().getOccupyinfo().setZoneId(occupySceneClan.getZoneId());
            }
            // 有归属方  加入所属方的被占领列表
            if (ownerSceneClan != null) {
                ownerSceneClan.getMapBuildingComponent().addToBeOccupy(getEntityId(), true);
            }
        }
        // 关系都处理完了 可以初始化状态节点
        getOwner().getStageMgrComponent().initNode();
    }

    /**
     * 被击破 停止占领 并遣返
     */
    public void stopOccupy(long newClanId) {
        if (newClanId == getOccupyInfoProp().getOccupyClanId()) {
            LOGGER.error("{} try stop clan is same with the initial one {}", getOwner(), newClanId);
            return;
        }
        LOGGER.info("{} stopOccupy newClanId: {}", getOwner(), newClanId);
        // 是正在占领阶段
        if (getOccupyInfoProp().getState() == OccupyState.TOS_OCCUPYING) {
            // 通知原先的占领者 停止占领
            getOccupySceneClan().getMapBuildingComponent().stopOccupy(getEntityId());
            // 把原占领积分结算下
            getOwner().getInnerArmyComponent().addAllPlayerClanOccupyScore(SystemClock.now(), getOccupyInfoProp().getStateStartTsMs());
        }
        getOwner().getInnerArmyComponent().returnAllArmy();
        // 把预警处理下
        getOwner().getInnerArmyComponent().clearWarningItem();
    }

    /**
     * 开始占领
     */
    public void startOccupy(long newClanId) {
        // 当前正在占领者就是胜利联盟 错误
        long oldOccupyClanId = getOccupyInfoProp().getOccupyClanId();
        if (oldOccupyClanId == newClanId) {
            LOGGER.error("{} same clan battle? clanId: {}", getOwner(), newClanId);
            return;
        }
        // 后面发qlog的时候，如果没有占领者，就用原拥有者
        if (oldOccupyClanId == 0) {
            oldOccupyClanId = getOccupyInfoProp().getOwnerClanId();
        }
        // 胜利者是所属者  走夺回逻辑
        if (getOccupyInfoProp().getOwnerClanId() == newClanId) {
            // 不需要重新占领
            if (!checkAndSetOwnerRetake()) {
                return;
            }
        }
        getOccupyInfoProp().setOccupyClanId(newClanId);
        getOwner().getStageMgrComponent().transNewNode(new OccupyingStageNode(getOwner()));
        if (oldOccupyClanId != 0) {
            getOwner().getQLogComponent().sendMapBuildLog(oldOccupyClanId, "be_occupy", getOwner().getQLogComponent().getInnerPlayer());
        }
        // 尝试存盘
        getOwner().getDbComponent().tryInsertDb();
    }

    /**
     * 所属者夺回 （打回来， 或者占领者联盟放弃了）  返回是否需要重新占领
     */
    public boolean checkAndSetOwnerRetake() {
        TerritoryBuildingTemplate template = getOwner().getTerritoryBuildingTemplate();
        long deltaTime = template.getProtectTime() + template.getDiscardTime();
        long loseTsMs = getOccupyInfoProp().getOwnerOccupyTsMs() + TimeUtils.second2Ms(deltaTime);
        // 被夺期间还没有荒废
        SceneClanEntity ownerSceneClan = getOwnerSceneClan();
        if (loseTsMs > SystemClock.now()) {
            // 设置联盟属性
            getOccupyInfoProp().setOccupyClanId(0);
            getOwner().copySceneClanAttr(ownerSceneClan);
            // 夺回了， 加回到拥有列表
            ownerSceneClan.getMapBuildingComponent().addToOwner(getOwner(), false);
            if (!getOwner().getProp().getConstructInfo().getIsConnectedToCommandNet()) {
                // 进入荒废阶段
                long startTsMs = getOccupyInfoProp().getOwnerOccupyTsMs() + TimeUtils.second2Ms(template.getProtectTime());
                getOwner().getStageMgrComponent().transNewNode(new DesertedStageNode(getOwner()), startTsMs);
            } else {
                // 在指挥网中 不进入荒废
                getOwner().getStageMgrComponent().transNewNode(new InCommandNetStageNode(getOwner()));
            }
            LOGGER.info("{} recapture. enter deserted stage. clanId: {}", getOwner(), getOccupyInfoProp().getOwnerOccupyTsMs());
            return false;
        }
        // 需要重新占领  清下归属数据
        ownerSceneClan.getMapBuildingComponent().removeMapBuilding(getEntityId());
        getOccupyInfoProp().setOwnerClanId(0);
        return true;
    }

    public long getOccupyClanId() {
        return getOccupyInfoProp().getOccupyClanId();
    }

    public long getOwnerClanId() {
        return getOccupyInfoProp().getOwnerClanId();
    }

    public SceneClanEntity getOccupySceneClan() {
        if (getOccupyInfoProp().getOccupyClanId() == 0) {
            return null;
        }
        return getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(getOccupyInfoProp().getOccupyClanId());
    }

    public SceneClanEntity getOwnerSceneClan() {
        if (getOccupyInfoProp().getOwnerClanId() == 0) {
            return null;
        }
        return getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(getOccupyInfoProp().getOwnerClanId());
    }

    private OccupyInfoProp getOccupyInfoProp() {
        return getOwner().getProp().getOccupyinfo();
    }
}

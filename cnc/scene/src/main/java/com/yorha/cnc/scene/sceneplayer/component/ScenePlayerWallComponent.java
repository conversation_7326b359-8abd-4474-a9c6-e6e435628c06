package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWallComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.game.gen.prop.WallInfoProp;
import com.yorha.proto.CommonEnum.CityWallState;

/**
 * 城墙模块
 *
 * <AUTHOR>
 */
public class ScenePlayerWallComponent extends AbstractScenePlayerWallComponent {

    public ScenePlayerWallComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    /**
     * 更新最大耐久
     */
    public static void onSyncWallHpMax(ScenePlayerEntity entity, long hpMax) {
        if (!entity.getScene().isMainScene()) {
            return;
        }
        WallInfoProp prop = entity.getWallComponent().getProp();
        if (prop.getHp() == prop.getHpMax() && entity.getMainCity().getBattleComponent().getWallState() != CityWallState.CS_BURNING) {
            prop.setHp((int) hpMax);
        }
        prop.setHpMax((int) hpMax);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    protected int getCityBurningDuration() {
        return ResHolder.getResService(ConstKVResService.class).getTemplate().getCityWallBurningDuration();
    }

    @Override
    protected WallInfoProp getProp() {
        return this.getOwner().getProp().getWall();
    }
}

package com.yorha.cnc.scene.entity.component;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjPropChangeListener;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.aoiView.manager.AoiObserver;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.utils.shape.Shape;
import com.yorha.proto.EntityAttrOuterClass;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * aoi基类 没做什么 都是直接取所有的obj和observer
 *
 * <AUTHOR>
 */
public class AoiMgrComponent extends SceneComponent {
    public static final Logger LOGGER = LogManager.getLogger(AoiMgrComponent.class);
    /**
     * tick内所有发生变化的属性监听器，在afterTick中一次性处理
     */
    protected final Map<Long, SceneObjPropChangeListener> propChangeListenerMap = new ConcurrentHashMap<>();

    public AoiMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void addModEntityAttr(AoiObserver observer, long objId, EntityAttrOuterClass.EntityAttr.Builder attrBuilder) {

    }

    public void addPropChangeListener(long id, SceneObjPropChangeListener propChangeListener) {
        if (id <= 0) {
            LOGGER.error("{} wrong id, addPropChangeListener", id);
            return;
        }
        SceneObjPropChangeListener oldListener = propChangeListenerMap.get(id);
        if (oldListener != null) {
            LOGGER.error("{} repeat addPropChangeListener", id);
        }
        propChangeListenerMap.put(id, propChangeListener);
    }

    /**
     * 消费所有产生属性变化的对象，收脏和处理
     */
    public void consumeAllPropChangeListener() {
        // 先cancel，避免下面出问题，就再也没置成功了
        getOwner().cancelIsInTaskQueue();
        for (SceneObjPropChangeListener propChangeListener : propChangeListenerMap.values()) {
            try {
                propChangeListener.dangerousRun();
            } catch (Exception e) {
                LOGGER.error("consumeAllPropChangeListener ", e);
            }
        }
        propChangeListenerMap.clear();
    }

    /**
     * 格子相关的只有格子类型会实现  非格子是会返回空的!!!
     */
    public Set<AoiGrid> getAffectAoiGrids(Shape shape) {
        return Collections.emptySet();
    }

    /**
     * 格子相关的只有格子类型会实现  非格子是空的!!!
     */
    public void consumerAffectAoiGrids(Shape shape, Consumer<AoiGrid> consumer) {

    }

    /**
     * 避免频繁new
     */
    protected Set<SceneObjEntity> affectedAoiSceneObjRet = new ObjectOpenHashSet<>();

    public void consumerAffectSceneObj(Shape shape, Consumer<SceneObjEntity> consumer) {
        if (shape == null) {
            return;
        }
        for (SceneObjEntity target : getOwner().getObjMgrComponent().getEntityMap().values()) {
            // 判断模型圈是否包含目标中心点
            if (!shape.containsPoint(target.getCurPoint().getX(), target.getCurPoint().getY())) {
                continue;
            }
            consumer.accept(target);
        }
    }

    /**
     * 获取指定范围内的所有 与自己相交的SceneObj
     * 一般获取了返回值后，直接for循环处理，严禁把返回值容器继续传递
     * 共用了返回值，小心！！！
     * 必要时刻需要在外面深拷贝使用！
     */
    public Set<SceneObjEntity> getAffectedAoiSceneObjList(Shape shape) {
        if (shape == null) {
            return Collections.emptySet();
        }
        affectedAoiSceneObjRet.clear();
        for (SceneObjEntity target : getOwner().getObjMgrComponent().getEntityMap().values()) {
            // 判断模型圈是否包含目标中心点
            if (!shape.containsPoint(target.getCurPoint().getX(), target.getCurPoint().getY())) {
                continue;
            }
            affectedAoiSceneObjRet.add(target);
        }
        return Collections.unmodifiableSet(affectedAoiSceneObjRet);
    }

    /**
     * 获取指定范围内的所有SceneObj
     * 一般获取了返回值后，直接for循环处理，严禁把返回值容器继续传递
     * 共用了返回值，小心！！！
     * 必要时刻需要在外面深拷贝使用！
     */
    public Collection<SceneObjEntity> getAoiSceneObjList(Shape shape) {
        if (shape == null) {
            return Collections.emptySet();
        }
        affectedAoiSceneObjRet.clear();
        return Collections.unmodifiableCollection(getOwner().getObjMgrComponent().getEntityMap().values());
    }

    public void broadcastAoiObserverList(Set<AoiObserver> observers, int msgType, GeneratedMessageV3 csMsg) {
        if (observers == null) {
            LOGGER.error("broadcastAoiObserverList but observers is null");
            return;
        }
        if (observers.size() <= 1) {
            // 就1个人，还是直接单播吧
            for (AoiObserver observer : observers) {
                IActorRef sessionRef = observer.getSessionRef();
                if (sessionRef == null) {
                    continue;
                }
                SessionHelper.sendMsgToSession(sessionRef, ownerActor(), msgType, csMsg);
            }
            return;
        }

        // 获取所有在线玩家playerId对应的SessionRef
        List<IActorRef> ret = new LinkedList<>();
        for (AoiObserver observer : observers) {
            IActorRef sessionRef = observer.getSessionRef();
            if (sessionRef == null) {
                continue;
            }
            ret.add(sessionRef);
        }

        if (ret.isEmpty()) {
            return;
        }
        // 其他则走网络广播咯，比如kvk、远征副本、埃及之战等
        SessionHelper.broadcastMsgToSessions(ret, ownerActor().self(), msgType, csMsg);
    }
}

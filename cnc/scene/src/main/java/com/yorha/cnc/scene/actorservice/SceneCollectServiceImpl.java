package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.search.ResBuildingSearch;
import com.yorha.common.actor.SceneCollectService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneCollect.SearchResourceAns;
import com.yorha.proto.SsSceneCollect.SearchResourceAsk;

/**
 * <AUTHOR> zeo
 */
public class SceneCollectServiceImpl implements SceneCollectService {
    private final SceneActor sceneActor;

    public SceneCollectServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    @Override
    public void handleSearchResourceAsk(SearchResourceAsk ask) {
        if (ask.getCurrencyType() == CommonEnum.CurrencyType.DIAMOND) {
            throw new GeminiException(ErrorCode.NOT_SUPPORT_SEARCH_GOLD_BUILDING);
        }
        ScenePlayerEntity player = sceneActor.getScenePlayer(ask.getPlayerId());
        if (player == null) {
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        ResBuildingEntity resBuildingEntity = player.getSearchComponent().searchEntity(new ResBuildingSearch(ask.getLevel(), ask.getCurrencyType()));
        SearchResourceAns.Builder builder = SearchResourceAns.newBuilder();
        if (resBuildingEntity != null) {
            builder.setEntityId(resBuildingEntity.getEntityId());
            Point point = resBuildingEntity.getCurPoint();
            builder.getPointBuilder().setX(point.getX()).setY(point.getY()).build();
        }
        sceneActor.answer(builder.build());
    }
}

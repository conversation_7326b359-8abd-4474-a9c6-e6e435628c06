package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.proto.CommonEnum;

/**
 * 全服玩家的击杀野怪数量
 * 参数：要求野怪数量_野怪的Type_等级要求
 */
public class AllPlayerKillMonsterMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public void updateProcess(MileStoneTaskData taskData) {
        if (taskData instanceof KillMonsterData) {
            KillMonsterData data = (KillMonsterData) taskData;
            String[] taskParam = getTaskParamById();
            int numLimit = Integer.parseInt(taskParam[0]);
            int monsterType = Integer.parseInt(taskParam[1]);
            int monsterLevelLimit = Integer.parseInt(taskParam[2]);
            if (matchParam(data, monsterType, monsterLevelLimit)) {
                long min = Math.min(data.getValue() + getProp().getProcess(), numLimit);
                getProp().setProcess(min);
            }
        }
    }

    private boolean matchParam(KillMonsterData data, int monsterType, int monsterLevelLimit) {
        return data.getType().getNumber() == monsterType && (monsterLevelLimit == 0 || data.getLevel() >= monsterLevelLimit);
    }

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_ALL_PLAYER;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_KILL_REBEL_NUM;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END;
    }


    public static class KillMonsterData extends MileStoneTaskData {
        CommonEnum.MonsterCategory type;
        int level;

        public CommonEnum.MonsterCategory getType() {
            return type;
        }

        public int getLevel() {
            return level;
        }

        public KillMonsterData(CommonEnum.MonsterCategory type, int level, int value) {
            super(value);
            this.type = type;
            this.level = level;
        }
    }

}

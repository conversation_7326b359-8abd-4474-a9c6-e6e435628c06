package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.datatype.IntPairType;
import res.template.AiStateTriggerTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 旁白事件
 */
public class DialogEventTrigger extends AiTrigger {

    private int eventId;

    public DialogEventTrigger(AiStateTriggerTemplate template) {
        super(template);
    }

    @Override
    protected void parse(List<IntPairType> param) {
        for (IntPairType pair : param) {
            if (pair.getKey() == 1) {
                eventId = pair.getValue();
            }
        }
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        return true;
    }

    @Override
    protected void doTrigger(SceneObjEntity owner) {
        owner.getAoiNodeComponent().broadcast(MsgType.PLAYER_PLAYDIALOG_NTF, MsgHelper.buildBuildingClientShowMsg(eventId));
    }

    @Override
    protected String getTriggerParam() {
        return String.valueOf(eventId);
    }

    @Override
    protected String getTriggerName() {
        return "DialogEventTrigger";
    }
}

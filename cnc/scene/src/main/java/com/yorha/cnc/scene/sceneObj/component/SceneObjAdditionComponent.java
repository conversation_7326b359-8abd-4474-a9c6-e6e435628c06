package com.yorha.cnc.scene.sceneObj.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.troop.TroopResService;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class SceneObjAdditionComponent extends SceneObjComponent<SceneObjEntity> {

    public static void dispatchChange(SceneObjEntity owner, CommonEnum.BuffEffectType buffEffectType) {
        switch (buffEffectType) {
            case ET_ARMY_MOVE_SPEED_ADD_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE1_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE2_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE3_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE4_PERCENT:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE1_FIXED:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE2_FIXED:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE3_FIXED:
            case ET_ARMY_MOVE_SPEED_ADD_TYPE4_FIXED: {
                if (owner instanceof ArmyEntity) {
                    ((ArmyEntity) owner).getMoveComponent().onSpeedChanged();
                }
                break;
            }
            default: {
                break;
            }
        }
    }

    public SceneObjAdditionComponent(SceneObjEntity owner) {
        super(owner);
    }

    public AbstractScenePlayerEntity getScenePlayer() {
        return null;
    }

    public Map<CommonEnum.BuffEffectType, Long> getAllAdditions() {
        Map<CommonEnum.BuffEffectType, Long> ret = Maps.newHashMap();

        // 玩家加成
        AbstractScenePlayerEntity scenePlayer = getScenePlayer();
        if (scenePlayer != null && scenePlayer.getAdditionComponent() != null) {
            AdditionUtil.mergeAddition(ret, scenePlayer.getAdditionComponent().getAllAddition());
        }

        if (getOwner().getBattleComponent() != null) {
            BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
            AdditionUtil.mergeAddition(ret, battleRole.getAdditionHandler().getAllAddition());

            int troopId = getOwner().getBattleComponent().getTroop().getTroopId();
            if (troopId > 0) {
                Map<CommonEnum.BuffEffectType, Long> troopAdditions = ResHolder.getResService(TroopResService.class).getTroopAdditions(troopId);
                AdditionUtil.mergeAddition(ret, troopAdditions);
            }
        }


        return ret;
    }

    public long getAddition(CommonEnum.BuffEffectType additionId) {
        long ret = 0;

        // 玩家加成
        AbstractScenePlayerEntity scenePlayer = getScenePlayer();
        if (scenePlayer != null && scenePlayer.getAdditionComponent() != null) {
            ret += scenePlayer.getAdditionComponent().getAddition(additionId);
        }

        if (getOwner().getBattleComponent() != null) {
            BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
            ret += battleRole.getAdditionHandler().getAddition(additionId);
            // 部队配置加成
            int troopId = getOwner().getBattleComponent().getTroop().getTroopId();
            ret += ResHolder.getResService(TroopResService.class).getTroopAddition(troopId, additionId);
        }
        return ret;
    }

    public long getGVGCollectAddition(CommonEnum.BuffEffectType additionId) {
        long ret = 0;

        // 玩家加成
        AbstractScenePlayerEntity scenePlayer = getScenePlayer();
        if (scenePlayer != null && scenePlayer.getAdditionComponent() != null) {
            ret += scenePlayer.getAdditionComponent().getAddition(additionId);
        }

        if (getOwner().getBattleComponent() != null) {
            BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
            ret += battleRole.getAdditionHandler().getAddition(additionId) * 2;
            // 部队配置加成
            int troopId = getOwner().getBattleComponent().getTroop().getTroopId();
            ret += ResHolder.getResService(TroopResService.class).getTroopAddition(troopId, additionId);
        }


        return ret;
    }
}

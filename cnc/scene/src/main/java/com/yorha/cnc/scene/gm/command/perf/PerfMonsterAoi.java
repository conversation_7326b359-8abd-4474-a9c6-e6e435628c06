package com.yorha.cnc.scene.gm.command.perf;

import com.yorha.cnc.mainScene.common.component.MainSceneGridAoiMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.helper.GmHelper;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 分析野怪aoi的分布信息
 */
public class PerfMonsterAoi implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isMainScene()) {
            return;
        }
        MainSceneGridAoiMgrComponent aoiMgrComponent = (MainSceneGridAoiMgrComponent) actor.getScene().getAoiMgrComponent();
        String s = aoiMgrComponent.gmPerfMonsterAoi();
        String key = "PerfMonsterAoi";
        int zoneId = actor.getScenePlayer(playerId).getZoneId();

        GmHelper.sendGmNtfMail(zoneId, playerId, key, s);
    }

    @Override
    public String showHelp() {
        return "PerfMonsterAoi";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MONSTER;
    }
}
package com.yorha.cnc.scene.event.army;

import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;
import com.yorha.proto.Struct;

/**
 * army的英雄属性变更了
 *
 * <AUTHOR>
 */
public class ArmyHeroPropChangeEvent extends IEventWithEntityId {
    private final Struct.Hero heroChangeProp;

    public ArmyHeroPropChangeEvent(long entityId, Struct.Hero heroChangeProp) {
        super(entityId);
        this.heroChangeProp = heroChangeProp;
    }

    public Struct.Hero getHeroChangeProp() {
        return heroChangeProp;
    }
}

package com.yorha.cnc.mainScene.bigScene.component;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.mainScene.common.component.MainSceneBornMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneBornRegionItem;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class BigSceneBornMgrComponent extends MainSceneBornMgrComponent {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneBornMgrComponent.class);

    public BigSceneBornMgrComponent(BigSceneEntity owner) {
        super(owner);
    }

    @Override
    public BigSceneEntity getOwner() {
        return (BigSceneEntity) super.getOwner();
    }

    /**
     * 导量出生选点
     */
    public Point getDrivenBornPoint(final long playerId, final int hardwareLevel) {
        // 导量选州
        int regionId = getOwner().getZoneEntity().getBornMgrComponent().getDrivenBornRegion(hardwareLevel);
        if (regionId == -1) {
            LOGGER.warn("atZoneDrive BigSceneBornMgrComponent getDrivenBornPoint failed playerId={} region=-1", playerId);
            return null;
        }
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            LOGGER.error("atZoneDrive BigSceneBornMgrComponent getDrivenBornPoint failed playerId={} region={}", playerId, regionId);
            return null;
        }
        Point point = item.choosePartPoint(true);
        if (point != null) {
            LOGGER.info("atZoneDrive BigSceneBornMgrComponent getDrivenBornPoint playerId={} regionId={} point={}", playerId, regionId, point);
            return point;
        }
        WechatLog.error("atZoneDrive BigSceneBornMgrComponent getDrivenBornPoint failed playerId={} regionId={}", playerId, regionId);
        return null;
    }

    /**
     * 出生选点
     */
    public Point getBornPoint(final long playerId) {
        // 选州
        int regionId = getOwner().getZoneEntity().getBornMgrComponent().getBornRegion();
        if (regionId == -1) {
            LOGGER.info("{} getBornPoint failed playerId:{} region: -1", getOwner(), playerId);
            return null;
        }
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            LOGGER.error("getBornPoint failed playerId:{} region: {}", playerId, regionId);
            return null;
        }
        Point point = item.choosePartPoint(true);
        if (point != null) {
            LOGGER.info("{} getBornPoint playerId:{} regionId:{} point: {}", getOwner(), playerId, regionId, point);
            return point;
        }
        LOGGER.info("{} getBornPoint failed playerId:{} regionId:{}", getOwner(), playerId, regionId);
        return null;
    }

    public Point getBornPointByRegion(final int regionId) {
        MainSceneBornRegionItem item = regionItemMap.get(regionId);
        if (item == null) {
            LOGGER.error("getBornPointByRegion failed region: {}", regionId);
            return null;
        }
        Point point = item.choosePartPoint(true);
        if (point != null) {
            return point;
        }
        LOGGER.info("getBornPointByRegion failed regionId:{}", regionId);
        return null;
    }
}

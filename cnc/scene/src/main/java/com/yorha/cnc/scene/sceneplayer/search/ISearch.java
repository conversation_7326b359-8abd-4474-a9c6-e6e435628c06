package com.yorha.cnc.scene.sceneplayer.search;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public abstract class ISearch<T extends SceneObjEntity> {
    // 半小時的失效時間
    public static final int INVALID_MIL_SECOND = 1800000;
    private final int level;
    private long searchTsMs;

    public ISearch(int level) {
        this.level = level;
        this.searchTsMs = SystemClock.now();
    }

    /**
     * 搜索的目标entity类型
     */
    public abstract Class<? extends SceneObjEntity> getClazz();

    /**
     * 搜索等级
     */
    public int getLevel() {
        return level;
    }

    /**
     * 搜索范围
     *
     * @return
     */
    public abstract int getSearchRange();

    /**
     * 是否匹配
     */
    public abstract boolean matching(ScenePlayerEntity player, T entity);

    /**
     * 是否永远取第一个
     */
    public boolean isAlwaysFirst() {
        return false;
    }

    public double getPriorityV(ScenePlayerEntity player, T entity) {
        return Point.calDisBetweenTwoPoint(entity.getCurPoint(), player.getMainCity().getCurPoint());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ISearch<?> iSearch = (ISearch<?>) o;
        if (level != iSearch.level) {
            return false;
        }
        return SystemClock.now() - iSearch.searchTsMs < INVALID_MIL_SECOND;
    }

    @Override
    public int hashCode() {
        return Objects.hash(level);
    }
}

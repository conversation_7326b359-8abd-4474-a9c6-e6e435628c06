package com.yorha.cnc.scene.abstractsceneplayer.addition;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.utils.FormulaUtils;
import com.yorha.common.utils.MathUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.CommonEnum.SoldierType;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

/**
 * 加成公式计算器
 * <p>
 * scene所有加成相关计算公式
 * <p>
 * 1. ScenePlayer.getAdditionComponent().getAddition(additionId)    获取ScenePlayer的加成
 * 2. SceneObjEntity.getAdditionComponent().getAddition(additionId) 获取SceneObjEntity的加成，包含ScenePlayer + 技能释放产生的buff加成 + 英雄 + 机甲 + 配置等 的加成
 * 3. BattleRole.getFinalBuffValue(additionId)                      同2.
 *
 * <AUTHOR>
 */
public class SceneAddCalc {
    private static final Logger LOGGER = LogManager.getLogger(SceneAddCalc.class);


    /**
     * 部队负载
     */
    public static long getArmyBurden(ArmyEntity army, SoldierTypeTemplate template, int soldierNum) {
        BuffEffectType burdenAdditionId4Percent = AdditionConstants.SOLDIER_TYPE_BURDEN_EFFECT_PER_TYPE_MAP.get(SoldierType.forNumber(template.getSoldierType()));
        BuffEffectType burdenAdditionId = AdditionConstants.SOLDIER_TYPE_BURDEN_EFFECT_TYPE_MAP.get(SoldierType.forNumber(template.getSoldierType()));
        //单兵种负载百分比
        long soldierBurdenPer = army.getAdditionComponent().getAddition(burdenAdditionId4Percent);
        //英雄提供负载百分比  +  玩家提供的加成
        long heroBurdenPer = army.getAdditionComponent().getAddition(BuffEffectType.ET_BURDEN_PERCENT);
        //单兵种负载绝对值
        long soldierBurden = army.getAdditionComponent().getAddition(burdenAdditionId);
        long percent = Math.addExact(soldierBurdenPer, heroBurdenPer);
        // 部队总负载 = （配表Tx单兵种单个负载 + ∑单兵种负载固定值提升/降低） * （1 + ∑全兵种负载百分比 + ∑单兵种负载万分比）* Tx单兵种士兵个数
        return FormulaUtils.f1(template.getBurden(), soldierBurden, percent, soldierNum);
    }


    /**
     * 全兵种移速
     */
    public static long getSoldierAllTypeSpeed(BattleRole battleRole) {
        // 全兵种
        long allAdd = battleRole.getFinalBuffValue(BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_PERCENT);
        if (battleRole.getAdapter().isRally()) {
            // 集结时移速
            allAdd += battleRole.getFinalBuffValue(BuffEffectType.ET_RALLY_SPEED_PERCENT);
        }
        return allAdd;
    }

    /**
     * 兵种移速
     */
    public static long getSoldierTypeSpeed(BattleRole battleRole, SoldierType soldierType, int baseSpeed, long allSpeedAdd, boolean isInClanTerritory) {
        // 单兵种
        long add = battleRole.getFinalBuffValue(AdditionConstants.SOLDIER_TYPE_SPEED_INC_MAP.get(soldierType).getFirst());
        // 最终移速万分比
        long finalAdd = Math.addExact(allSpeedAdd, add);
        // 领土加成
        if (isInClanTerritory) {
            long extraAdd = battleRole.getFinalBuffValue(BuffEffectType.ET_MOVE_IN_CLAN_TERRITORY_SPEED_PERCENT);
            finalAdd = Math.addExact(finalAdd, extraAdd);
        }
        long minPercent = Math.min(finalAdd, (long) ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getMarchSpeedUpMax() * FormulaUtils.RATIO);
        long percent = Math.max(minPercent, (long) ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getMarchSpeedUpMin() * FormulaUtils.RATIO);
        // 最终移速固定值
        long fixed = battleRole.getFinalBuffValue(AdditionConstants.SOLDIER_TYPE_SPEED_INC_MAP.get(soldierType).getSecond());
        // Tx单兵种行军速度 = (Tx单兵种配表行军速度 + ∑单兵种基础行军速度增加) *
        //                 (1 + max(行军速度加成最小值，min(∑单兵种行军速度增加 + ∑全兵种行军速度增加)，行军速度加成最大值))
        return FormulaUtils.f1(baseSpeed, fixed, percent, 1);
    }

    /**
     * 侦察机移速
     *
     * @param spyPlaneEntity 侦察机
     * @param moveSpeed      移速
     * @return
     */
    public static int getSpyPlaneSpeed(SpyPlaneEntity spyPlaneEntity, int moveSpeed) {
        long addition = spyPlaneEntity.getAdditionComponent().getAddition(BuffEffectType.ET_SPY_PLANE_SPEED_PERCENT);
        // 运输机行军速度 = 运输机配表行军速度 * （1 + ∑运输机行军速度加成百分比）
        return (int) FormulaUtils.f1(moveSpeed, 0, addition, 1);
    }

    /**
     * 运输机移速
     */
    public static int getTransportPlaneSpeed(ArmyEntity armyEntity, int transportSpeed) {
        long addition = armyEntity.getAdditionComponent().getAddition(BuffEffectType.ET_TRANSPORT_PLANE_SPEED_INC_PERCENT);
        // 运输机行军速度 = 运输机配表行军速度 * （1 + ∑运输机行军速度加成百分比）
        return (int) FormulaUtils.f1(transportSpeed, 0, addition, 1);
    }

    /**
     * 医院容量
     */
    public static int getHospitalCapacity(AbstractScenePlayerEntity player) {
        return (int) FormulaUtils.f1(
                0,
                player.getAdditionComponent().getAddition(BuffEffectType.ET_HOSPITAL_WOUNDED_CAPACITY),
                player.getAdditionComponent().getAddition(BuffEffectType.ET_HOSPITAL_WOUNDED_CAPACITY_PERCENT),
                1);
    }

    /**
     * 打野经验值
     */
    public static int getFightMonsterExp(BattleRole battleRole, int baseExp) {
        return (int) FormulaUtils.f1(baseExp, 0, battleRole.getFinalBuffValue(BuffEffectType.ET_PLAYER_MONSTER_EXP_UP), 1);
    }


    /**
     * 获取联盟资源田采集速度
     */
    public static double getClanResCollectSpeed(ArmyEntity army, float base, long addition) {
        //采集速度 = 基础采集速度 * ( 1 + ∑全资源采集速度万分比 + ∑单个采集速度万分比)
        double v = MathUtils.TEN_THOUSAND + addition;
        return base * v / MathUtils.TEN_THOUSAND;
    }

    /**
     * 资源田采集速度
     */
    public static double getCollectSpeed(ArmyEntity army, CurrencyType type, float base, long clanId) {
        long all = army.getAdditionComponent().getAddition(BuffEffectType.ET_COLLECT_SPEED_UP);
        BuffEffectType additionId = AdditionConstants.RESOURCE_COLLECT_INT_MAP.get(type);
        long single;
        if (additionId == null) {
            single = 0;
        } else {
            single = army.getAdditionComponent().getAddition(additionId);
        }
        if ((clanId > 0) && (army.getClanId() == clanId)) {
            BuffEffectType clanAdditionId = AdditionConstants.RESOURCE_COLLECT_CLAN_INT_MAP.get(type);
            single += army.getAdditionComponent().getAddition(clanAdditionId);
        }
        //采集速度 = 基础采集速度 * ( 1 + ∑全资源采集速度万分比 + ∑单个采集速度万分比)
        double v = MathUtils.TEN_THOUSAND + all + single;
        return base * v / MathUtils.TEN_THOUSAND;
    }

    /**
     * 英雄额外采集增幅
     */
    public static long getArmyExtraCollect(BattleRole battleRole, long base) {
        if (base <= 0) {
            return 0;
        }
        // 部队采集资源 * （ 1 + ∑采集完成额外获得资源）
        long heroExtraRate = battleRole.getFinalBuffValue(CommonEnum.BuffEffectType.ET_HERO_COLLECT_RESOURCE_INC_PERCENT);
        long totalNum = FormulaUtils.f1(base, 0, heroExtraRate, 1);
        return Math.max(0, totalNum - base);
    }

    /**
     * 打野体力消耗
     */
    public static long getHuntMonsterEnergyCost(ArmyEntity armyEntity, int baseCost, int killStreak) {
        long costReduceFixed = 0;
        long costReducePercent = 0;

        ConstTemplate consts = ResHolder.getConsts(ConstTemplate.class);
        int continuityMonsterEnergy = consts.getContinuityMonsterEnergy();
        int continuityMonsterEnergyMaxTimes = consts.getContinuityMonsterEnergyMaxTimes();
        costReduceFixed += (long) Math.min(killStreak, continuityMonsterEnergyMaxTimes) * continuityMonsterEnergy;

        costReduceFixed += armyEntity.getAdditionComponent().getAddition(BuffEffectType.ET_ENERGY_COST_REDUCE_FIXED);
        costReducePercent += armyEntity.getAdditionComponent().getAddition(BuffEffectType.ET_ENERGY_COST_REDUCE_PERCENT);

        // 打野体力消耗 = （打野配表体力消耗 – 打野体力消耗降低固定值）*（ 1 – 打野体力消耗降低百分比）
        return FormulaUtils.f4(baseCost, costReduceFixed, costReducePercent, 1);
    }

    /**
     * 获取集结容量
     */
    public static long getRallyCap(BattleRole battleRole, SceneObjEntity targetEntity) {
        long capFixed = battleRole.getFinalBuffValue(BuffEffectType.ET_RALLY_CAP_FIXED);
        long capPercent = battleRole.getFinalBuffValue(BuffEffectType.ET_RALLY_CAP_PERCENT);
        // 集结部队容量 = (配表 + 集结部队容量上限) * (1 + ∑集结容量加成万分比)
        long finalV = FormulaUtils.f1(0, capFixed, capPercent, 1);
        // 目标是联盟建筑时，以联盟建筑集结上限为标准
        if (targetEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) targetEntity;
            int maxSoldierNum = mapBuildingEntity.getBuildingTemplate().getMaxSoldierNum();
            return Math.min(finalV, maxSoldierNum);
        }
        return finalV;
    }

    /**
     * 集结上限
     */
    public static long getRallyCap(AbstractScenePlayerEntity scenePlayer) {
        long capFixed = scenePlayer.getAdditionComponent().getAddition(BuffEffectType.ET_RALLY_CAP_FIXED);
        long capPercent = scenePlayer.getAdditionComponent().getAddition(BuffEffectType.ET_RALLY_CAP_PERCENT);
        // 集结部队容量 = 集结部队容量上限 *（1 + ∑集结容量加成万分比）
        return FormulaUtils.f1(0, capFixed, capPercent, 1);
    }

    /**
     * 占领速度
     */
    public static long getOccupySpeed(MapBuildingEntity mapBuilding, SceneClanEntity sceneClan) {
        ConstClanTerritoryTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
        long curSoliderNum = mapBuilding.getInnerArmyComponent().getArrivedSoldierNum();
        long soliderOccupyRatio = constTemplate.getSoldierOccupyRatio();
        long addition = sceneClan.getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_OCCUPY_STRONGHOLD_SPEED_PERCENT_VALUE);
        // 占领速度 = (基础据点占领值+ 据点内总兵力/ 兵力系数) *（1 + ∑联盟建筑占领速度万分比）
        return FormulaUtils.f6(constTemplate.getOccupyBaseSpeed(), curSoliderNum, soliderOccupyRatio, addition);
    }

    /**
     * 获取军团建筑（军团基地、军团指挥中心、军团堡垒）的重建速度
     */
    public static long getRebuildSpeed(MapBuildingEntity mapBuilding, SceneClanEntity sceneClan) {
        long buildSpeedUp = 0;
        TerritoryBuildingTemplate template = mapBuilding.getClanBuildingTemplate();
        long basicSpeed = template.getBasicSpeed();
        long curSoliderNum = mapBuilding.getInnerArmyComponent().getArrivedSoldierNum();
        long soliderRebuildRatio = template.getSoldierRebuildRatio();
        // 对scene clan判空
        if (null == sceneClan) {
            LOGGER.error("scene clan is null , return basic speed, mapBuilding is {}", mapBuilding);
            return FormulaUtils.f6(basicSpeed, curSoliderNum, soliderRebuildRatio, buildSpeedUp);
        }

        CommonEnum.MapBuildingType rebuildType = mapBuilding.getProp().getConstructInfo().getType();
        if (AdditionConstants.CLAN_BUILDING_BUILD_SPEEDUP_EFFECT_TYPE_MAP.containsKey(rebuildType)) {
            // 判断军团基地、堡垒和指挥中心是否有建造速度提升的增益
            CommonEnum.BuffEffectType buffEffectType = AdditionConstants.CLAN_BUILDING_BUILD_SPEEDUP_EFFECT_TYPE_MAP.get(rebuildType);
            long addition = sceneClan.getAddComponent().getAddition(buffEffectType.getNumber());
            buildSpeedUp += addition;
            // 判断是否有军团全部类型建造速度提升的增益
            addition = sceneClan.getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_BUILD_CLAN_BUILDING_SPEED_PERCENT.getNumber());
            buildSpeedUp += addition;
        }
        // 改建速度 = (基础速度 + 建筑内总兵力 / 兵力系数) * (1 + 军团建筑建造速度加成)
        return FormulaUtils.f6(basicSpeed, curSoliderNum, soliderRebuildRatio, buildSpeedUp);
    }

    /**
     * 获取军团资源建筑的重建速度
     */
    public static long getRebuildSpeed(ClanResBuildingEntity clanResBuilding, SceneClanEntity sceneClan) {
        long buildSpeedUp = 0;
        ClanResourceBuildingTemplate template = clanResBuilding.getClanResourceBuildingTemplate();
        long basicSpeed = template.getBasicSpeed();
        long curSoliderNum = clanResBuilding.getInnerArmyComponent().getArrivedSoldierNum();
        long soliderRebuildRatio = template.getSoldierRebuildRatio();
        // 对scene clan判空
        if (null == sceneClan) {
            LOGGER.error("scene clan is null , return basic speed, clanResBuilding is {}", clanResBuilding);
            return FormulaUtils.f6(basicSpeed, curSoliderNum, soliderRebuildRatio, buildSpeedUp);
        }
        // 判断是否有军团全部类型建造速度提升的增益
        long addition = sceneClan.getAddComponent().getAddition(CommonEnum.BuffEffectType.ET_BUILD_CLAN_BUILDING_SPEED_PERCENT.getNumber());
        buildSpeedUp += addition;
        // 改建速度 = (基础速度 + 建筑内总兵力 / 兵力系数) * (1 + 建筑建造速度加成)
        return FormulaUtils.f6(basicSpeed, curSoliderNum, soliderRebuildRatio, buildSpeedUp);
    }

    /**
     * 燃烧速度
     */
    public static long getFireSpeed(long addition) {
        // 着火速度的配置会额外乘以10000，来模拟小数的速度计算
        ConstClanTerritoryTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
        return FormulaUtils.f1(template.getBasicFireSpeed(), 0, addition, 1);
    }

    /**
     * 最大hp
     */
    public static long getMaxHp(CommonEnum.MapBuildingType rebuildType, int basicMaxHp, SceneClanEntity sceneClan) {
        // 对scene clan判空
        if (null == sceneClan) {
            LOGGER.error("scene clan is null , return basic max hp");
            return basicMaxHp;
        }
        long hpAdditionPercent = sceneClan.getAddComponent().getAddition(BuffEffectType.ET_CLAN_BUILDING_HP_MAX_PERCENT.getNumber());
        if (AdditionConstants.CLAN_BUILDING_MAX_HP_EFFECT_TYPE_MAP.containsKey(rebuildType)) {
            CommonEnum.BuffEffectType buffEffectType = AdditionConstants.CLAN_BUILDING_MAX_HP_EFFECT_TYPE_MAP.get(rebuildType);
            // 判断军团基地、堡垒和指挥中心是否有建造速度提升的增益
            long addition = sceneClan.getAddComponent().getAddition(buffEffectType.getNumber());
            hpAdditionPercent += addition;
        }
        // 联盟建筑耐久度 = 基础耐久度 *（1 + 耐久度增加百分比）
        return FormulaUtils.f1(basicMaxHp, 0, hpAdditionPercent, 1);
    }

    public static int getStrongholdMax(SceneClanEntity sceneClanEntity) {
        int num = ResHolder.getConsts(ConstClanTerritoryTemplate.class).getInitialBuildingNum();
        long addition = sceneClanEntity.getAddComponent().getAddition(BuffEffectType.ET_CAN_OCCUPY_STRONGHOLD_NUM_FIXED_VALUE);
        return (int) (num + addition);
    }

    /**
     * 积分速度
     */
    public static double getScoreRatio(int base, SceneClanEntity sceneClan) {
        long addition = sceneClan.getAddComponent().getAddition(BuffEffectType.ET_BUILD_CLAN_BUILDING_SCORE_SPEED_VALUE);
        return base * (1 + addition / MathUtils.TEN_THOUSAND);
    }

    /**
     * 部队溃败回城速度
     */
    public static int getRetreatSpeed(ArmyEntity armyEntity, int curSpeed) {
        long addition = armyEntity.getAdditionComponent().getAddition(BuffEffectType.ET_ARMY_RETREAT_SPEED_ADD_PERCENT);
        // 溃败回城速度 = 当前速度 * （1 + ∑溃败回城速度加成万分比）
        return (int) FormulaUtils.f1(curSpeed, 0, addition, 1);
    }
}

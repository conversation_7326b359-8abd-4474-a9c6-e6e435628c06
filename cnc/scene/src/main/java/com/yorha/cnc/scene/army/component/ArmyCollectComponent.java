package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.TickMoveNoPathEvent;
import com.yorha.cnc.scene.event.army.ArmyEnterInteriorEvent;
import com.yorha.cnc.scene.event.army.PlayerOperationPreEvent;
import com.yorha.cnc.scene.event.battle.EndAllBattleEvent;
import com.yorha.cnc.scene.event.battle.EnterNewBattle;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncCollectRes;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 采集
 *
 * <AUTHOR>
 */
public class ArmyCollectComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyCollectComponent.class);
    private long collectTarget;
    private boolean isBattle;
    private boolean isClanCollect;
    private EventListener startBattleE;
    private EventListener endBattleE;
    private long battlerTargetId;

    public ArmyCollectComponent(ArmyEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        if (getOwner().getProp().getAttachState() == AttachState.AAS_COLLECT) {
            collectTarget = getOwner().getProp().getAttachId();
            startBattleE = getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleStart, EnterNewBattle.class);
            endBattleE = getOwner().getEventDispatcher().addEventListenerRepeat(this::onAllBattleEnd, EndAllBattleEvent.class);
        }
        //  玩家指令前置事件 check 援助情况
        getOwner().getEventDispatcher().addEventListenerRepeat(this::checkCollectState, PlayerOperationPreEvent.class);
        getOwner().getEventDispatcher().addMultiEventListenerRepeat((e) -> {
            if (collectTarget == 0) {
                return;
            }
            stopCollect(null);
        }, DieEvent.class, DeleteEvent.class, TickMoveNoPathEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat((e) -> {
            if (collectTarget == 0) {
                return;
            }
            if (!isClanCollect) {
                return;
            }
            stopCollect(null);
        }, ClanChangeEvent.class);
    }

    /**
     * 指令更改 需要检查是否更改了采集目标
     */
    private void checkCollectState(PlayerOperationPreEvent event) {
        if (collectTarget == 0) {
            return;
        }
        // 改变指令 退出采集
        StructPlayer.ArmyActionInfo actionInfo = event.getArmyActionInfo();
        if (actionInfo.getArmyActionType() != ArmyActionType.AAT_GATHER || collectTarget != actionInfo.getTargetId()) {
            getOwner().getMoveComponent().stopMove();
            LOGGER.info("{} leaveCollect {} reason: changeOperation", getOwner(), collectTarget);
            Point targetPoint = null;
            if (actionInfo.hasTargetPoint()) {
                targetPoint = Point.valueOf(actionInfo.getTargetPoint().getX(), actionInfo.getTargetPoint().getY());
            } else if (actionInfo.getTargetId() != -1 && actionInfo.getTargetId() != 0) {
                SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(actionInfo.getTargetId());
                if (sceneObj != null) {
                    targetPoint = sceneObj.getCurPoint();
                }
            }
            if (targetPoint == null) {
                targetPoint = getOwner().getMainCityPoint();
            }
            stopCollect(targetPoint);
        }
    }

    /**
     * 采集指令
     */
    public void handleCollect(long targetId, boolean isBattle) {
        // 重复指令
        if (collectTarget == targetId && this.isBattle == isBattle) {
            return;
        }
        // 获取采集的目标体
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        collectTarget = targetId;
        this.isBattle = isBattle;
        this.isClanCollect = true;
        if (target.getEntityType() == EntityType.ET_ResBuilding) {
            this.isClanCollect = false;
            ((ResBuildingEntity) target).getCollectComponent().addCollect(getOwner(), isBattle);
        } else if (target.getEntityType() == EntityType.ET_ClanResBuilding) {
            ((ClanResBuildingEntity) target).getCollectComponent().addArmyRecordToClanResBuilding(getOwner().getPlayerId(), getEntityId());
        }
        LOGGER.info("{} tryCollect. targetId: {} isBattle: {}", getOwner(), targetId, isBattle);
        try {
            if (target.getEntityType() == EntityType.ET_ResBuilding) {
                // 移动至目标
                getOwner().getMoveComponent().moveToTargetAsync(
                        target,
                        isBattle ? null : TroopInteractionType.ENTER_BUILD,
                        this::arriveResBuilding, null,
                        (codeId) -> {
                            if (ErrorCode.isOK(codeId)) {
                                return;
                            }
                            getOwner().getScenePlayer().sendErrorCode(codeId);
                            onMoveFailed();
                        });
            } else if (target.getEntityType() == EntityType.ET_ClanResBuilding) {
                getOwner().getMoveComponent().moveToTargetAsync(
                        target,
                        isBattle ? null : TroopInteractionType.ENTER_BUILD,
                        this::arriveClanResBuilding, null,
                        (codeId) -> {
                            if (ErrorCode.isOK(codeId)) {
                                return;
                            }
                            getOwner().getScenePlayer().sendErrorCode(codeId);
                            onMoveFailed();
                        });
            }
        } catch (Exception e) {
            onMoveFailed();
            throw e;
        }
    }

    public boolean isBattleToCollect() {
        return isBattle;
    }

    public long getCollectTarget() {
        return collectTarget;
    }

    public SceneObjEntity getCollectTargetEntity() {
        return getOwner().getScene().getObjMgrComponent().getSceneObjEntity(collectTarget);
    }

    private void onMoveFailed() {
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(collectTarget);
        collectTarget = 0;
        this.isBattle = false;
        getOwner().getStatusComponent().setStaying();
        if (target == null) {
            return;
        }
        if (target.getEntityType() == EntityType.ET_ResBuilding) {
            ((ResBuildingEntity) target).getCollectComponent().leaveCollect(getOwner(), getOwner().getCurPoint());
        } else if (target.getEntityType() == EntityType.ET_ClanResBuilding) {
            ((ClanResBuildingEntity) target).getInnerArmyComponent().removeArmy(getEntityId(), getOwner(), 0L);
        }
    }

    /**
     * 到达资源田
     */
    private void arriveResBuilding() {
        ResBuildingEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ResBuildingEntity.class, collectTarget);
        if (target == null) {
            collectTarget = 0L;
            getOwner().getMoveComponent().returnMainCity();
            return;
        }
        if (isBattle) {
            // 到达时候idle状态  直接进入采集
            if (target.getProp().getState() == ResourceBuildingState.RBS_IDLE || target.getBattleComponent() == null) {
                tryEnterCollect(target);
                return;
            }
            if (getOwner().getBattleComponent().tryStartBattleWith(target)) {
                getOwner().getBattleComponent().setCurAttackBuilding(target.getArmyId());
                getOwner().getMoveComponent().occupyAndMoveToBesiegePoint(target.getBattleComponent().getOwner(), true, null);
                // 实际跟里面的行军打
                getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_BATTLE, target.getBattleComponent().getOwner());
            } else {
                collectTarget = 0L;
                getOwner().getMoveComponent().returnMainCity();
                target.getCollectComponent().leaveCollect(getOwner(), null);
            }
            return;
        }
        tryEnterCollect(target);
    }

    private void arriveClanResBuilding() {
        ClanResBuildingEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ClanResBuildingEntity.class, collectTarget);
        if (target == null) {
            collectTarget = 0L;
            getOwner().getMoveComponent().returnMainCity();
            return;
        }
        // 加入采集时结束掉所有战场
        getOwner().getBattleComponent().forceEndAllBattle();
        // 通知目标到达援助
        target.getInnerArmyComponent().armyArrived(getOwner());
        // 移除视野
        if (getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_COLLECT);
        }
        getOwner().getEventDispatcher().dispatch(new ArmyEnterInteriorEvent(getEntityId()));
    }

    /**
     * 判断是不是在打资源建筑
     */
    public boolean checkIsAttackResBuilding() {
        if (collectTarget == 0 || !isBattle) {
            return false;
        }
        ResBuildingEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ResBuildingEntity.class, collectTarget);
        if (target == null) {
            afterLeaveCollect(null);
            return false;
        }
        return true;
    }

    /**
     * 判断打资源田处理   返回是否成功进入采集状态
     */
    public boolean onAttackSucceed(BattleOverType type, boolean isEnemyDead) {
        ResBuildingEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ResBuildingEntity.class, collectTarget);
        if (target == null) {
            LOGGER.warn("onAttackSucceed but target not exist {} {}", getOwner(), collectTarget);
            clearAfterBattle(null);
            return false;
        }
        if (!getOwner().getBattleComponent().hasAnyAlive()) {
            clearAfterBattle(target);
            return false;
        }
        // 不是别人撤出或死亡
        if (type != BattleOverType.BOT_OUT_OF_LOSE && !isEnemyDead) {
            clearAfterBattle(target);
            return false;
        }
        // 建筑是自己的目标 但是里面的状态是正在采集   说明被别人先进去了
        if (target.getState() == ResourceBuildingState.RBS_COLLECTING) {
            clearAfterBattle(target);
            return false;
        }
        boolean isOk = target.getCollectComponent().onAttackSucceed(getOwner());
        if (isOk) {
            getOwner().getBattleComponent().forceEndAllBattle();
            afterEnterCollect();
            sendCollectQlog("victory_collect", target, target.getProp().getCurNum(), 0);
        } else {
            clearAfterBattle(target);
        }
        return isOk;
    }

    /**
     * 战斗结束后清理  只有在战斗成功却无法进入时调用
     */
    private void clearAfterBattle(ResBuildingEntity target) {
        afterLeaveCollect(null);
        if (target != null) {
            target.getCollectComponent().leaveCollect(getOwner(), getOwner().getMainCityPoint());
        }
    }

    /**
     * 进入采集状态
     */
    private void tryEnterCollect(ResBuildingEntity target) {
        battlerTargetId = 0;
        isBattle = false;
        boolean result = target.getCollectComponent().onArriveCollect(getOwner());
        if (!result) {
            collectTarget = 0L;
            getOwner().getMoveComponent().returnMainCity();
            return;
        }
        afterEnterCollect();
        sendCollectQlog("regular_collect", target, target.getProp().getCurNum(), 0);
        LOGGER.info("{} enterCollect.  targetId: {}", getOwner(), collectTarget);

    }

    private void afterEnterCollect() {
        getOwner().getPropComponent().onAttachChange(AttachState.AAS_COLLECT, collectTarget);
        getOwner().getProp().setEnterStateTs(SystemClock.now()).setArmyState(ArmyState.AS_Collect);
        startBattleE = getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleStart, EnterNewBattle.class);
        endBattleE = getOwner().getEventDispatcher().addEventListenerRepeat(this::onAllBattleEnd, EndAllBattleEvent.class);
        // 移除视野
        if (getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_COLLECT);
        }
        getOwner().getEventDispatcher().dispatch(new ArmyEnterInteriorEvent(getEntityId()));
    }

    public void sendCollectQlog(String reason, ResBuildingEntity resBuildingEntity, long resStock, long resGet) {
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) getOwner().getScenePlayer();
        QlogCncCollectRes flow = new QlogCncCollectRes();
        flow.fillHead(scenePlayer.getQlogComponent());
        flow.setDtEventTime(TimeUtils.now2String());
        flow.setAction(resBuildingEntity.getCollectQLogAction());
        flow.setReason(reason);
        flow.setResourceID(resBuildingEntity.getProp().getTemplateId());
        flow.setResBuildingID(resBuildingEntity.getEntityId());
        PointProp pointProp = resBuildingEntity.getProp().getPoint();
        flow.setResourcePartID(MapGridDataManager.getPartId(getOwner().getScene().getMapId(), pointProp.getX(), pointProp.getY()));
        flow.setResStock(resStock);
        flow.setResGet(resGet);
        flow.sendToQlog();
    }

    public void sendCollectQlog(String reason, ClanResBuildingEntity clanResBuilding, long resGet) {
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) getOwner().getScenePlayer();
        QlogCncCollectRes flow = new QlogCncCollectRes();
        flow.fillHead(scenePlayer.getQlogComponent());
        flow.setDtEventTime(TimeUtils.now2String());
        flow.setAction(clanResBuilding.getCollectQLogAction());
        flow.setReason(reason);
        flow.setResourceID(clanResBuilding.getProp().getTemplateId());
        flow.setResBuildingID(clanResBuilding.getEntityId());
        PointProp pointProp = clanResBuilding.getProp().getPoint();
        flow.setResourcePartID(MapGridDataManager.getPartId(getOwner().getScene().getMapId(), pointProp.getX(), pointProp.getY()));
        flow.setResStock(clanResBuilding.getProgress().getMaxNum() - clanResBuilding.getProgress().getLastCalNum());
        flow.setResGet(resGet);
        flow.sendToQlog();
    }

    /**
     * 指令主动停止采集  可能在路上/在采集
     */
    private void stopCollect(Point targetPos) {
        // 通知资源田清理结算资源
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(collectTarget);
        Point leavePosition = null;
        if (target != null) {
            if (target.getEntityType() == EntityType.ET_ResBuilding) {
                // 资源田处理
                leavePosition = ((ResBuildingEntity) target).getCollectComponent().leaveCollect(getOwner(), targetPos);
                boolean needEndBattle = getOwner().getProp().getAttachState() == AttachState.AAS_COLLECT;
                // 清理数据 取消事件监听
                afterLeaveCollect(leavePosition);
                if (needEndBattle) {
                    // 如果是战斗中 就停止所有战斗  在这里，攻击方endSingleBattle时就进采集田了
                    getOwner().getBattleComponent().forceEndAllBattle();
                }
            } else if (target.getEntityType() == EntityType.ET_ClanResBuilding) {
                ClanResBuildingEntity clanResBuildingEntity = (ClanResBuildingEntity) target;
                final boolean isInBuilding = clanResBuildingEntity.getProgressInfoByPlayerId(getOwner().getPlayerId()) != null;
                // NOTE(furson): 军团资源中心处理，用InnerArmyDelEvent事件来处理
                ((ClanResBuildingEntity) target).getInnerArmyComponent().removeArmy(getEntityId(), getOwner(), 0L);
                if (!isInBuilding) {
                    // 还没到建筑，使用目标位置直接离开
                    collectEndAndToTarget(targetPos);
                }
            }
        } else {
            afterLeaveCollect(null);
        }
    }

    /**
     * 实际离开后处理 清数据
     */
    public void afterLeaveCollect(Point leavePosition) {
        clearEvent();
        collectTarget = 0;
        if (getOwner().getProp().getAttachState() == AttachState.AAS_COLLECT) {
            getOwner().getPropComponent().onAttachChange(AttachState.AAS_NONE, 0);
            if (leavePosition != null) {
                getOwner().getTransformComponent().changePoint(leavePosition);
            }
            getOwner().getStatusComponent().setStaying();
            // 重新加入aoi
            if (!getOwner().getAoiNodeComponent().isInAoi()) {
                getOwner().getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_COLLECT);
            }
        }
        LOGGER.info("{} afterLeaveCollect", getOwner());
    }

    /**
     * 采集结束回城、 战斗结束无法继续采集回城
     */
    public void collectEndAndReturn(SceneObjEntity entity) {
        battlerTargetId = 0;
        LOGGER.info("{} collectEnd. {}", getOwner(), getOwner().getProp().getResource().getCollect());
        if (entity == null) {
            afterLeaveCollect(null);
        } else {
            Point cityPos = getOwner().getMainCityPoint();
            afterLeaveCollect(entity.getTransformComponent().getLeavePosition(getOwner(), cityPos));
        }
        getOwner().getMoveComponent().returnMainCity();
    }

    /**
     * 停止采集，到目标位置，目前仅用于军团资源中心
     *
     * @param targetPos 目标位置
     */
    public void collectEndAndToTarget(@Nullable Point targetPos) {
        Point leavePosition = getOwner().getTransformComponent().getLeavePosition(getOwner(), targetPos);
        afterLeaveCollect(leavePosition);
    }

    private void onBattleStart(EnterNewBattle e) {
        // 已经设置过了 说明有人开战了 不用再管
        if (battlerTargetId != 0) {
            return;
        }
        ResBuildingEntity resBuildingEntity = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ResBuildingEntity.class, collectTarget);
        if (resBuildingEntity != null) {
            resBuildingEntity.getCollectComponent().startBattle(getOwner());
            // 战斗要给别人看到.....  重新加入aoi
            if (!getOwner().getAoiNodeComponent().isInAoi()) {
                getOwner().getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_COLLECT);
            }
            // 要设置成正在与xxx战斗状态
            SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(e.getTargetId());
            battlerTargetId = e.getTargetId();
            getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_BATTLE, target);
        } else {
            // 资源田没了？？
            afterLeaveCollect(null);
            LOGGER.error("{} onBattleStart but resBuilding is null : {}", getOwner(), collectTarget);
        }
    }

    /**
     * 在采集中 一场战斗结束了  看看是不是我当前状态指向的  如果是 要重新设置下目标
     */
    public void onSingleBattleEnd(long targetId) {
        if (targetId != battlerTargetId) {
            return;
        }
        battlerTargetId = 0;
        if (!getOwner().getBattleComponent().hasAnyAlive()) {
            return;
        }
        List<BattleRelation> allValidRelation = getOwner().getBattleComponent().getBattleRole().getAllValidRelation();
        if (allValidRelation.isEmpty()) {
            return;
        }
        for (BattleRelation relation : allValidRelation) {
            long roleId = relation.getEnemyRole(getEntityId()).getRoleId();
            if (roleId == targetId) {
                continue;
            }
            SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(roleId);
            if (target == null) {
                continue;
            }
            battlerTargetId = roleId;
            getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_BATTLE, target);
            return;
        }
    }

    /**
     * 战斗全部结束
     */
    private void onAllBattleEnd(EndAllBattleEvent e) {
        battlerTargetId = 0;
        // 在endSingle判定攻打成功后进来了  然后endAll又进来了 过滤
        if (getOwner().getStatusComponent().getState() == ArmyDetailState.ADS_COLLECT) {
            return;
        }
        ResBuildingEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ResBuildingEntity.class, collectTarget);
        // 挂了 清理数据就行
        if (!getOwner().getBattleComponent().hasAnyAlive()) {
            Point point = null;
            if (target != null) {
                point = target.getCollectComponent().leaveCollect(getOwner(), getOwner().getMainCityPoint());
            }
            afterLeaveCollect(point);
            return;
        }
        // 赢了 看看能不能继续采集
        if (target != null) {
            boolean result = target.getCollectComponent().endBattle(getOwner());
            // 负重不够了 不能继续采集 回城
            if (!result) {
                collectEndAndReturn(target);
                return;
            }
            // 重新进入采集状态
            getOwner().getProp().setArmyState(ArmyState.AS_Collect).setEnterStateTs(SystemClock.now());
            getOwner().getBehaviourComponent().refreshArmyState();
            // 移除视野
            if (getOwner().getAoiNodeComponent().isInAoi()) {
                getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_COLLECT);
            }
        } else {
            // 资源田没了？？
            collectEndAndReturn(null);
            LOGGER.error("{} onAllBattleEnd but resBuilding is null : {}", getOwner(), collectTarget);
        }
    }

    private void clearEvent() {
        if (startBattleE != null) {
            startBattleE.cancel();
            startBattleE = null;
        }
        if (endBattleE != null) {
            endBattleE.cancel();
            endBattleE = null;
        }
    }

    @Override
    public void onDestroy() {
        clearEvent();
    }

    public void collectEndReward() {
        int rewardId = (int) getOwner().getAdditionComponent().getAddition(BuffEffectType.ET_COLLECT_END_REWARD);
        if (rewardId > 0) {
            SsPlayerMisc.OnCollectEndCmd.Builder builder = SsPlayerMisc.OnCollectEndCmd.newBuilder();
            builder.setRewardId(rewardId);
            getOwner().getScenePlayer().tellPlayer(builder.build());
        }
    }
}

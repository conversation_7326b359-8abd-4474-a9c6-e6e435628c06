package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.proto.CommonEnum;

import java.util.Collection;

public abstract class SceneObjSpecialSafeGuardComponent extends SceneObjComponent<SceneObjEntity> {

    public SceneObjSpecialSafeGuardComponent(SceneObjEntity owner) {
        super(owner);
    }

    public abstract void specialSafeGuardOn(CommonEnum.SafeGuardReason safeGuardReason);

    public abstract void specialSafeGuardOff(CommonEnum.SafeGuardReason safeGuardReason);

    public abstract void specialSafeGuardChange(boolean isOn, CommonEnum.SafeGuardReason safeGuardReason);

    public boolean isSpecialSafeGuardOn() {
        return false;
    }

    public abstract Collection<Integer> getSafeGuardReasons();
}

package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.ConstTemplate;

import static com.yorha.proto.CommonEnum.MapBuildingType.*;

/**
 * 玩法组件
 * <AUTHOR>
 */
public class SuperWeaponLeaderComponent extends SceneObjComponent<MapBuildingEntity> {
    public SuperWeaponLeaderComponent(MapBuildingEntity owner) {
        super(owner);
    }
    private int outBuildingNum = 0;

    public void incOutBuilding(OutbuildingEntity outbuildingEntity) {
        if (!isPlant(outbuildingEntity)) {
            return;
        }
        outBuildingNum++;
    }

    public void decOutBuilding(OutbuildingEntity outbuildingEntity) {
        if (!isPlant(outbuildingEntity)) {
            return;
        }
        outBuildingNum--;
        if (outBuildingNum > 0) {
            return;
        }
        //对owner释放一个技能加buff
        BattleHero mainHero = getOwner().getBattleComponent().getMainHero();
        if (mainHero == null) {
            return;
        }
        int skillId = ResHolder.getConsts(ConstTemplate.class).getSuperWeaponLeaderSkill();
        getOwner().getEventDispatcher().dispatch(new FireSkillEvent.Builder().setHeroId(mainHero.getId()).setSkillId(skillId).build());
    }

    /**
     * 电网
      */
    private boolean isPlant(OutbuildingEntity outbuildingEntity) {
        CommonEnum.MapBuildingType mapBuildingType = outbuildingEntity.getBuildingTemplate().getType();
        if (mapBuildingType == MBT_ELECTRIC_PLANT) {
            return true;
        }
        return mapBuildingType == MBT_CHEMICAL_PLANT;
    }

    @Override
    public SceneActor ownerActor() {
        return getOwner().ownerActor();
    }

    @Override
    public void init() {

    }
}

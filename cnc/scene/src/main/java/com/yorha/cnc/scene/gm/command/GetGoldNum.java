package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.Map;

/**
 * 获取当前黄金矿的数量
 *
 * <AUTHOR>
 */
public class GetGoldNum implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isMainScene()) {
            throw new GeminiException(ErrorCode.INTERFACE_NOT_IMPLEMENTED);
        }
        MainSceneResMgrComponent resMgrComponent = (MainSceneResMgrComponent) actor.getScene().getResMgrComponent();
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getPersonalMailId());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(resMgrComponent.getCurGoldNum()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("GetGoldNum")
                .setSubTitle("GetGoldNUm");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(actor.getScenePlayer(playerId).getZoneId())
                        .build(),
                builder.build());
    }
}

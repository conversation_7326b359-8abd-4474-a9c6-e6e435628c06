package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * gm设置是否开启连地检测 默认开启  debug下才可以设置
 *
 * <AUTHOR>
 */
public class SetIsOpenAdjoinCheck implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        boolean isOpen = true;
        if (args.containsKey("isOpen")) {
            isOpen = Integer.parseInt(args.get("isOpen")) == 1;
        }
        ServerContext.getServerDebugOption().setIsOpenAdjoinCheck(isOpen);
    }

    @Override
    public String showHelp() {
        return "SetIsOpenAdjoinCheck isOpen={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}

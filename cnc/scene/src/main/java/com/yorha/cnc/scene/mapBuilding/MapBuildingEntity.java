package com.yorha.cnc.scene.mapBuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.event.mapbuilding.LoseAdjoinPartEvent;
import com.yorha.cnc.scene.event.player.ClanSimpleNameChangeEvent;
import com.yorha.cnc.scene.mapBuilding.component.*;
import com.yorha.cnc.scene.mapBuilding.enums.AbandonBuildingReason;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.WarningInfoType;
import com.yorha.cnc.scene.sceneObj.camp.CampRelationProvider;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjHateListComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.SpecialShieldHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.BigMapBuildingTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.CommonMsg.ClanTerritoryPage;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstSettingTemplate;
import res.template.MapBuildingTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * <p>
 * 地图建筑
 */
public class MapBuildingEntity extends SceneObjEntity implements BuildingEntityType, WarningInfoType {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingEntity.class);
    private final MapBuildingProp prop;
    private final MapBuildingInnerArmyComponent innerArmyComponent = new MapBuildingInnerArmyComponent(this);
    private final MapBuildingQLogComponent qLogComponent = new MapBuildingQLogComponent(this);
    private final MapBuildingBuffComponent buffComponent = new MapBuildingBuffComponent(this);
    private final MapBuildingOccupyComponent occupyComponent = new MapBuildingOccupyComponent(this);
    private final MapBuildingBuildComponent buildComponent = new MapBuildingBuildComponent(this);
    private final MapBuildingAdditionComponent additionComponent = new MapBuildingAdditionComponent(this);
    private final MapBuildingClanLogComponent clanLogComponent = new MapBuildingClanLogComponent(this);
    private final MapBuildingSpecialSafeGuardComponent specialSafeGuardComponent = new MapBuildingSpecialSafeGuardComponent(this);
    private final MapBuildingStageMgrComponent stageMgrComponent = new MapBuildingStageMgrComponent(this);
    private final SuperWeaponLeaderComponent leaderComponent = new SuperWeaponLeaderComponent(this);
    private final SceneObjHateListComponent hateListComponent;
    private final MapbuildingAiComponent aiComponent;
    /**
     * 构造时依赖army prop 只能放在构造函数里了
     */
    private final MapBuildingBattleComponent battleComponent;

    public MapBuildingEntity(MapBuildingBuilder builder, boolean isRestore) {
        super(builder);
        prop = builder.getProp();
        battleComponent = new MapBuildingBattleComponent(this);
        if (getBuildingTemplate().getAiIndex() > 0) {
            aiComponent = new MapbuildingAiComponent(this);
            hateListComponent = new SceneObjHateListComponent(this);
        } else {
            aiComponent = null;
            hateListComponent = null;
        }
        initAllComponents();
        getPropComponent().initPropListener(isRestore);
    }

    // ------------------------------------------- 必须实现 -------------------------------------------
    @Override
    public MapBuildingProp getProp() {
        return prop;
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_MapBuilding;
    }

    @Override
    public int getZoneId() {
        return getProp().getOccupyinfo().getZoneId();
    }

    @Override
    public void fullCsEntityAttr(EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getMapBuildingAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getMapBuildingAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getMapBuildingAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        getProp().copyToDb(builder.getMapBuildingAttrBuilder());
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        return getProp().copyChangeToDb(builder.getMapBuildingAttrBuilder());
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        final MapBuildingProp mapBuildingProp = MapBuildingProp.of(builder.getFullAttr().getMapBuildingAttr(), builder.getChangedAttr().getMapBuildingAttr());
        return EntityAttrDb.EntityAttrDB.newBuilder().setMapBuildingAttr(mapBuildingProp.getCopyDbBuilder());
    }

    @Override
    public MapBuildingBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public boolean briefEntityAttr(Entity.SceneObjBriefAttr.Builder builder) {
        OccupyInfoProp occupyInfo = getProp().getOccupyinfo();
        builder.getMapBuildingAttrBuilder()
                .setPartId(getPartId())
                .setClanId(getClanId())
                .setState(occupyInfo.getState())
                .setTemplateId(getProp().getTemplateId())
                .setClanSimpleName(getClanSimpleName())
                .setTerritoryColor(occupyInfo.getShowColor())
                .setFlagColor(occupyInfo.getFlagColor())
                .setFlagShading(occupyInfo.getFlagShading())
                .setFlagSign(occupyInfo.getFlagSign())
                .setNationFlagId(occupyInfo.getNationFlagId())
                .setZoneId(getZoneId())
                .getPointBuilder().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        return true;
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        prop.getTarget().setTargetType(ArmyTargetType.ATT_MAPBUILDING)
                .setName("")
                .setClanSimpleName(getClanSimpleName())
                .setTemplateId(getTemplateId())
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }
// ------------------------------------------- component -------------------------------------------

    @Override
    public MapBuildingTransformComponent getTransformComponent() {
        return (MapBuildingTransformComponent) super.getTransformComponent();
    }

    @Override
    public MapBuildingInnerArmyComponent getInnerArmyComponent() {
        return innerArmyComponent;
    }

    public MapBuildingStageMgrComponent getStageMgrComponent() {
        return stageMgrComponent;
    }

    public MapBuildingQLogComponent getQLogComponent() {
        return qLogComponent;
    }

    @Override
    public SceneObjBuffComponent getBuffComponent() {
        return buffComponent;
    }

    @Override
    public MapBuildingSpecialSafeGuardComponent getSpecialSafeGuardComponent() {
        return specialSafeGuardComponent;
    }

    public MapBuildingOccupyComponent getOccupyComponent() {
        return occupyComponent;
    }

    public MapBuildingBuildComponent getBuildComponent() {
        return buildComponent;
    }

    @Override
    public MapBuildingAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    public MapBuildingClanLogComponent getClanLogComponent() {
        return clanLogComponent;
    }

    @Override
    public StructCommonPB.ProgressInfoPB.Builder getProgressInfoPBBuilder() {
        StructCommonPB.ProgressInfoPB.Builder builder = StructCommonPB.ProgressInfoPB.newBuilder();
        if (getProp().getOccupyinfo().getState() == OccupyState.TOS_OCCUPYING) {
            builder.setSpeed(getProp().getOccupyinfo().getOccupySpeed())
                    .setMaxNum(getTerritoryBuildingTemplate().getOccupyTime())
                    .setLastCalNum(getProp().getOccupyinfo().getOccupyNum())
                    .setLastCalTsMs(getProp().getOccupyinfo().getOccupyNumCalcTsMs());
        } else if (getProp().getOccupyinfo().getState() == OccupyState.TOS_REBUILD) {
            builder.setSpeed(getProp().getOccupyinfo().getRebuildSpeed())
                    .setMaxNum(getProp().getOccupyinfo().getRebuildTotalWork())
                    .setLastCalNum(getProp().getOccupyinfo().getRebuildNum())
                    .setLastCalTsMs(getProp().getOccupyinfo().getRebuildNumCalcTsMs());
        }
        builder.setStateStartTsMs(getProp().getOccupyinfo().getStateStartTsMs())
                .setStateEndTsMs(getProp().getOccupyinfo().getStateEndTsMs());
        return builder;
    }

    @Override
    public int getLevel() {
        //联盟建筑没有等级，默认0级
        if (GameLogicConstants.isRebuildBuilding(getProp().getConstructInfo().getType())) {
            return 0;
        }
        //地缘建筑读取配置表
        TerritoryBuildingTemplate template = getTerritoryBuildingTemplate();
        if (null == template) {
            return 0;
        }
        return Math.max(template.getLevel(), 0);
    }

    @Override
    public void setExpression(int expressionId) {
        getProp().getExpression().setExpressionId(expressionId).setTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(ResHolder.getConsts(ConstSettingTemplate.class).getExpressionLastTime()));
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack(attackerObj, needCheckSiegeLimit, attackerObj.getEntityId());
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack(attackerPlayer, needCheckSiegeLimit, 0);
    }

    private ErrorCode commonCheckBeAttack(CampRelationProvider provider, boolean needCheckSiegeLimit, long roleId) {
        // 黑暗祭坛关卡
        if (getTransformComponent().getDarkAltarBindRegion() != null) {
            return ErrorCode.BATTLE_CANT;
        }
        OccupyState state = getProp().getOccupyinfo().getState();
        // 保护状态限制
        if (state == OccupyState.TOS_PROTECT || state == OccupyState.TOS_CLOSE) {
            return ErrorCode.BATTLE_CANT;
        }
        // 联盟据点连接检测
        SceneClanEntity sceneClan = getScene().getClanMgrComponent().getSceneClanOrNull(provider.getClanId());
        if (sceneClan == null) {
            return ErrorCode.BATTLE_CANT;
        }
        // 合围上限
        if (needCheckSiegeLimit && getBattleComponent().isSiegeLimit(provider.getClanId(), roleId)) {
            return ErrorCode.SIEGE_LIMIT;
        }
        return sceneClan.getMapBuildingComponent().checkCanAttack(this);
    }

    @Override
    public long getPlayerId() {
        ArmyEntity leaderArmy = getInnerArmyComponent().getLeaderArmy();
        return leaderArmy == null ? 0 : leaderArmy.getPlayerId();
    }

    /**
     * 用正在占领方的联盟id，如果是非占领阶段 就是所属方
     */
    @Override
    public long getClanId() {
        // 占领期间就是占领方
        if (isInOccupying()) {
            return getProp().getOccupyinfo().getOccupyClanId();
        }
        // 其他阶段就是所属方
        return getProp().getOccupyinfo().getOwnerClanId();
    }

    /**
     * 是否开启保护罩（包括和平护盾及联盟堡垒保护)
     */
    @Override
    public boolean isShieldOn() {
        return getSpecialSafeGuardComponent().isSpecialSafeGuardOn();
    }

    /**
     * 检查是否开启的特殊保护罩中是否有特定功能
     *
     * @param functionType 保护罩功能类型
     */
    @Override
    public SafeGuardReason checkShieldHasFunction(CommonEnum.SafeGuardFunctionType functionType) {
        if (!isShieldOn()) {
            // 未开启任何保护罩
            return SafeGuardReason.SGR_NONE;
        }
        // 特殊护盾，特殊检查
        return checkSpecialShieldHasFunction(functionType);
    }

    @Override
    public SafeGuardReason checkSpecialShieldHasFunction(SafeGuardFunctionType functionType) {
        return SpecialShieldHelper.checkHasFunction(getSpecialSafeGuardComponent().isSpecialSafeGuardOn(),
                getSpecialSafeGuardComponent().getSafeGuardReasons(), functionType);
    }

    /**
     * 拥有者联盟  如果在占领中 肯定是0
     */
    public long getOwnerClanId() {
        OccupyInfoProp occupyInfo = getProp().getOccupyinfo();
        if (occupyInfo.getOccupyClanId() != 0) {
            return 0;
        }
        return occupyInfo.getOwnerClanId();
    }

    /**
     * 获取 占领方的联盟名字
     */
    public String getClanName() {
        return getProp().getOccupyinfo().getShowClanName();
    }

    public String getClanSimpleName() {
        return getProp().getOccupyinfo().getShowClanSimpleName();
    }

    @Override
    public Camp getCampEnum() {
        return Camp.C_NEUTRAL;
    }

    @Override
    public ErrorCode canBeRallyWithCode() {
        OccupyState state = getProp().getOccupyinfo().getState();
        if (state == OccupyState.TOS_PROTECT || state == OccupyState.TOS_CLOSE) {
            return ErrorCode.BATTLE_CANT;
        }
        SafeGuardReason safeGuardReason = checkShieldHasFunction(SafeGuardFunctionType.SGFT_CANNOT_BE_RALLY);
        // 领土保护活动特殊错误码
        if (safeGuardReason == CommonEnum.SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY) {
            return ErrorCode.SAFE_GUARD_LAND_PROTECT_ACTIVITY_OPEN;
        }
        return safeGuardReason == SafeGuardReason.SGR_NONE ? ErrorCode.OK : ErrorCode.RALLY_CANT;
    }


    /**
     * 是否是占领期间
     */
    public boolean isInOccupying() {
        return getProp().getOccupyinfo().getState() == OccupyState.TOS_OCCUPYING;
    }

    /**
     * 是否在建设中
     */
    public boolean isInRebuilding() {
        return getProp().getOccupyinfo().getState() == OccupyState.TOS_REBUILD;
    }

    /**
     * 是否未开放
     */
    public boolean isClose() {
        return getProp().getOccupyinfo().getState() == OccupyState.TOS_CLOSE;
    }

    /**
     * 获取所在片的配置
     */
    public RegionalAreaSettingTemplate getAreaSettingTemplate() {
        return getScene().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, getPartId());
    }

    /**
     * 获取区域类型
     */
    public MapAreaType getAreaType() {
        return getAreaSettingTemplate().getAreaType();
    }

    /**
     * 获取领土建筑本身的势力值
     */
    public int getTerritoryPower() {
        return getTerritoryBuildingTemplate().getPowerScore();
    }

    /**
     * 获取联盟建筑改建后的势力值
     */
    public int getBuildingPower(MapBuildingType type) {
        TerritoryBuildingTemplate clanBuildingTemplate = ResHolder.getResService(BigMapBuildingTemplateService.class)
                .getClanBuildingTemplate(type, getScene().getStoryId());
        return clanBuildingTemplate.getPowerScore();
    }

    public boolean isClanBuilding() {
        return getProp().getConstructInfo().getType() != MapBuildingType.MBT_NONE;
    }

    public boolean isClanMainBuild() {
        return getProp().getConstructInfo().getType() == MapBuildingType.MBT_MAIN_BASE;
    }

    public int getPartId() {
        return getProp().getPartId();
    }

    @Override
    public boolean canBeSpy(long enemyPlayerId) {
        return getProp().getOccupyinfo().getState() != OccupyState.TOS_CLOSE;
    }

    /**
     * 获取领地建筑的配置
     */
    public TerritoryBuildingTemplate getTerritoryBuildingTemplate() {
        // 因为getProp().getTemplateId() 会被改建污染 所以不能用!
        //return ResHolder.getInstance().getValueFromMap(TerritoryBuildingTemplate.class, getProp().getTemplateId());
        return ResHolder.getInstance().getValueFromMap(TerritoryBuildingTemplate.class, getAreaSettingTemplate().getBuildingId());
    }

    /**
     * 获取联盟建筑的配置  如果没有改建 就返回null
     */
    public TerritoryBuildingTemplate getClanBuildingTemplate() {
        return ResHolder.getResService(BigMapBuildingTemplateService.class)
                .getClanBuildingTemplate(getProp().getConstructInfo().getType(), getScene().getStoryId());
    }

    /**
     * 这个联盟丢失了邻接条件
     */
    public void onLoseAdjoin(long clanId) {
        LOGGER.info("{} onLoseAdjoin  {}", clanId, this);
        // 不满足邻接条件后，占领中的要失去
        if (clanId == getProp().getOccupyinfo().getOccupyClanId()) {
            getStageMgrComponent().occupierAbandon(AbandonBuildingReason.ABR_ADJOIN_LOSE);
        }
        // 结束该联盟正在交战的战斗
        getBattleComponent().endBattleWithClanId(clanId);
        // 抛个事件  解散对应集结 进攻的行军停止
        getEventDispatcher().dispatch(new LoseAdjoinPartEvent(getEntityId(), clanId));
    }

    public OccupyState getOccupyState() {
        return getProp().getOccupyinfo().getState();
    }

    /**
     * copy自身相关属性到集结的目标数据中
     */
    public void copyToRallyInfo(RallyInfoProp rallyInfoProp) {
        rallyInfoProp.setTargetTemplateId(getProp().getTemplateId()).setTargetId(getEntityId()).setTargetClanId(getClanId())
                .setTargetType(RallyTargetType.RTT_MAP_BUILDING)
                .getTargetPos().setX(getCurPoint().getX()).setY(getCurPoint().getY());
    }

    public void addShowDetailsPb(ClanTerritoryPage.Builder pageBuilder, long clanId) {
        if (pageBuilder.getCityMap().containsKey(getEntityId())
                || pageBuilder.getStrongholdMap().containsKey(getEntityId())
                || pageBuilder.getClanBuildingMap().containsKey(getEntityId())) {
            return;
        }
        CommonMsg.MapBuildingInfo.Builder builder = CommonMsg.MapBuildingInfo.newBuilder().setState(getTerritoryPageState(clanId));
        // 排序时间戳
        if (getProp().getOccupyinfo().getOwnerClanId() == clanId) {
            builder.setSortTsMs(getProp().getOccupyinfo().getOwnerOccupyTsMs());
        } else {
            builder.setSortTsMs(getProp().getOccupyinfo().getStateEndTsMs());
        }
        builder.setTemplateId(getProp().getTemplateId())
                .setMapBuildingId(getEntityId())
                .getPointBuilder().setX(getProp().getPoint().getX()).setY(getProp().getPoint().getY());
        // 判断是否连入指挥网
        if (getProp().getConstructInfo().getIsConnectedToCommandNet()) {
            builder.getClanBuildingInfoBuilder().setIsConnectedToCommandNet(true);
        }
        if (getOwnerClanId() == clanId) {
            builder.setIsOwn(true);
        }
        // 改建的
        if (getProp().getConstructInfo().getType() != MapBuildingType.MBT_NONE) {
            builder.getClanBuildingInfoBuilder().setBuildingType(getProp().getConstructInfo().getType())
                    .setIsOnFire(getProp().getConstructInfo().getIsOnFire())
                    .setIsDeclaredWar(getBattleComponent().isInBattle())
                    .setIsConnectedToCommandNet(getProp().getConstructInfo().getIsConnectedToCommandNet())
                    .setIsConstructing(getProp().getOccupyinfo().getState() == OccupyState.TOS_REBUILD)
                    .setBeforeRebuildTemplateId(getProp().getConstructInfo().getBeforeRebuildTemplateId());
            if (getProp().getOccupyinfo().getState() == OccupyState.TOS_REBUILD) {
                // 正在改建的建筑，需要显示百分比，与大世界计算一致
                builder.getClanBuildingInfoBuilder().setRebuildNum(getProp().getOccupyinfo().getRebuildNum())
                        .setRebuildNumCalcTsMs(getProp().getOccupyinfo().getRebuildNumCalcTsMs())
                        .setRebuildTotalWork(getProp().getOccupyinfo().getRebuildTotalWork())
                        .setRebuildSpeed(getProp().getOccupyinfo().getRebuildSpeed());
            }
            pageBuilder.putClanBuilding(getEntityId(), builder.build());
            return;
        }
        // 据点
        if (GameLogicConstants.isMapStronghold(getAreaType())) {
            pageBuilder.putStronghold(getEntityId(), builder.build());
            return;
        }
        //城市
        pageBuilder.putCity(getEntityId(), builder.build());
    }

    private TerritoryState getTerritoryPageState(long clanId) {
        if (getBattleComponent().isInBattle()) {
            return TerritoryState.TS_BATTLE;
        }
        if (getStageMgrComponent().isBeRallyExceptClan(clanId)) {
            return TerritoryState.TS_RALLY;
        }
        // 占领联盟是自己
        if (getProp().getOccupyinfo().getOccupyClanId() == clanId) {
            return TerritoryState.TS_OCCUPYING;
        }
        if (getProp().getOccupyinfo().getOwnerClanId() == clanId) {
            // 所属是自己  有人在占领
            if (getProp().getOccupyinfo().getOccupyClanId() != 0) {
                return TerritoryState.TS_BE_OCCUPYING;
            }
        }
        if (getProp().getOccupyinfo().getState() == OccupyState.TOS_DESERTED) {
            return TerritoryState.TS_DESERTED;
        }
        if (getProp().getOccupyinfo().getState() == OccupyState.TOS_PROTECT) {
            return TerritoryState.TS_PROTECT;
        }
        return TerritoryState.TS_IDLE;
    }

    public int getTemplateId() {
        return getProp().getTemplateId();
    }

    @Override
    public Int64ArmyArrowItemMapProp getArrowProp() {
        return getProp().getArrow();
    }

    @Override
    public String toString() {
        return "MapBuilding{" + "partId=" + prop.getPartId()
                + " templateId=" + prop.getTemplateId()
                + " stage=" + getStageMgrComponent().getStageNode() + '}';
    }

    @Override
    public SceneActor ownerActor() {
        return getScene().ownerActor();
    }

    public boolean isNeutral() {
        return getProp().getOccupyinfo().getState() == OccupyState.TOS_NEUTRAL
                || getProp().getOccupyinfo().getState() == OccupyState.TOS_SEMI_OPEN;
    }

    public boolean isInCommandNet() {
        return getProp().getConstructInfo().getIsConnectedToCommandNet();
    }

    /**
     * 设置推荐兵种
     */
    public void setRecommendSoldierType(long playerId, List<Integer> soldierType) {
        LOGGER.info("{} setRecommendSoldierType: {}", this, soldierType);
        SceneClanEntity clan = getScene().getClanMgrComponent().getClanEntity(playerId);
        if (getClanId() != clan.getEntityId()) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        getProp().getRecommendSoldierTypeList().clear();
        getProp().getRecommendSoldierTypeList().addAll(soldierType);
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, getProp().getTemplateId());
    }

    public SuperWeaponLeaderComponent getLeaderComponent() {
        return leaderComponent;
    }

    @Override
    public SceneObjHateListComponent getHateListComponent() {
        return hateListComponent;
    }

    @Override
    public MapbuildingAiComponent getAiComponent() {
        return aiComponent;
    }

    public void onShowClanInfoChange(SceneClanEntity sceneClan) {
        boolean isClanSimpleNameChange = !sceneClan.getClanSimpleName().equals(getProp().getOccupyinfo().getShowClanSimpleName());
        copySceneClanAttr(sceneClan);
        // 简要数据变化 下发
        getPropComponent().briefCsMsgChange();
        if (isClanSimpleNameChange) {
            getEventDispatcher().dispatch(new ClanSimpleNameChangeEvent(getEntityId(), sceneClan.getClanSimpleName()));
        }
    }

    public void copySceneClanAttr(SceneClanEntity sceneClan) {
        getProp().getOccupyinfo()
                .setZoneId(sceneClan.getZoneId())
                .setShowClanName(sceneClan.getClanName())
                .setShowClanSimpleName(sceneClan.getClanSimpleName())
                .setShowColor(sceneClan.getTerritoryColor())
                .setFlagColor(sceneClan.getFlagColor())
                .setFlagShading(sceneClan.getFlagShading())
                .setFlagSign(sceneClan.getFlagSign())
                .setNationFlagId(sceneClan.getNationFlagId())
                .setZoneColor(sceneClan.getZoneAreaTerritoryColor());
    }

    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        if (super.onTickDispatch(reason)) {
            return true;
        }
        if (reason == SceneTickReason.TICK_AI) {
            SceneObjAiComponent aiComponent = getAiComponent();
            if (aiComponent == null) {
                return false;
            }
            if (!aiComponent.isRunning()) {
                return true;
            }
            aiComponent.onTick();
            return true;
        }

        if (reason == SceneTickReason.TICK_HATE) {
            getHateListComponent().onTick();
            return true;
        }
        return false;
    }

    @Override
    public void formWarningInfo(StructPlayerPB.WarningInfoPB.Builder builder) {
        builder.setTargetClanSimpleName(this.getClanSimpleName())
                .setTemplateId(this.getTemplateId());
    }
}

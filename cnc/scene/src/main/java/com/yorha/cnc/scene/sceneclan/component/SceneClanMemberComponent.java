package com.yorha.cnc.scene.sceneclan.component;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.component.ScenePlayerMgrComponent;
import com.yorha.cnc.scene.event.army.PointChangeEvent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.node.IGateActor;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.SceneClanProp;
import com.yorha.proto.SsSceneMap.FetchClanCityPointListAns;
import com.yorha.proto.Struct;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 场景上成员管理
 *
 * <AUTHOR>
 */
public class SceneClanMemberComponent extends AbstractComponent<SceneClanEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanMemberComponent.class);

    private int memberCityVersion;
    private EventListener ownerPointChangeEventListener;
    private int clanPositionMarkVersion = 0;

    /**
     * 在本场景的联盟成员  注意不是全盟的人！！
     */
    private final Set<Long> members = new LongOpenHashSet();


    public SceneClanMemberComponent(SceneClanEntity owner) {
        super(owner);
        onMemberCityChange();
    }

    public void onJoinClan(ScenePlayerEntity player, long newClanId, long oldClanId) {
        members.add(player.getEntityId());
        onMemberCityChange();
    }

    public void onLeaveClan(long playerId, long newClanId, long oldClanId) {
        members.remove(playerId);
        onMemberCityChange();
    }

    public void restorePlayer(long playerId) {
        members.add(playerId);
    }

    public void onMemberLeaveMainScene(long playerId) {
        members.remove(playerId);
        LOGGER.info("onMemberLeaveMainScene {} {}", getOwner(), playerId);
    }

    public void onMemberAddMainScene(long playerId) {
        members.add(playerId);
        LOGGER.info("onMemberAddMainScene {} {}", getOwner(), playerId);
        if (getOwner().getSceneEntity().isBigScene()) {
            // 军团长回来了，刷新一下军团州info
            if (getClanOwnerId() == playerId) {
                getOwner().getSceneEntity().getClanMgrComponent().addToRegionToClanMap(getOwner());
            }
        }
    }

    /**
     * 广播cs消息到本场景联盟成员  直发session
     */
    public void broadcastOnlineClientMsg(int msgType, GeneratedMessageV3 csMsg) {
        List<IActorRef> sessionRefList = new ArrayList<>();
        ScenePlayerMgrComponent playerMgrComponent = getOwner().getSceneEntity().getPlayerMgrComponent();
        for (Long playerId : members) {
            IActorRef sessionRef = playerMgrComponent.getScenePlayer(playerId).getSessionRef();
            if (sessionRef != null) {
                sessionRefList.add(sessionRef);
            }
        }
        if (sessionRefList.isEmpty()) {
            return;
        }
        if (ServerContext.isZoneServer()) {
            IActorRef gateActorRef = RefFactory.ofLocalGate();
            ActorSendMsgUtils.send(gateActorRef, ownerActor().self(), new ActorRunnable<IGateActor>("ScenePlayerMgrComponent#broadcastOnlineClientMsg", iGateActor -> {
                SessionHelper.broadcastMsgToSessions(sessionRefList, gateActorRef, msgType, csMsg);
            }));
        } else {
            SessionHelper.broadcastMsgToSessions(sessionRefList, ownerActor().self(), msgType, csMsg);
        }

    }

    public void onClanDisMiss() {
        if (ownerPointChangeEventListener != null) {
            ownerPointChangeEventListener.cancel();
            ownerPointChangeEventListener = null;
        }
    }

    public Set<Long> getMember() {
        return members;
    }

    public int getMemberSize() {
        return members.size();
    }

    public long getClanOwnerId() {
        return getOwner().getProp().getOwnerId();
    }

    public boolean isInClan(long playerId) {
        return members.contains(playerId);
    }

    /**
     * 联盟名字发生变化  成员们的写扩散
     */
    public void onClanBaseChange() {
        ScenePlayerMgrComponent playerMgrComponent = getOwner().getSceneEntity().getPlayerMgrComponent();
        for (long playerId : getMember()) {
            AbstractScenePlayerEntity scenePlayer = playerMgrComponent.getScenePlayer(playerId);
            scenePlayer.onSyncClanSimpleName(getProp().getClanSimpleName());
        }
    }

    /**
     * 军团长变更
     */
    public void onOwnerChange() {
        // 新军团长的city一定是存在的，需要重新设置监听
        setClanRegionIdByOwner();
        // 如果军团占了王城，换下国王
        getOwner().getMapBuildingComponent().tryChangeZoneKing();
    }

    public void onMemberCityChange() {
        memberCityVersion++;
    }

    public void buildMemberCity(FetchClanCityPointListAns.Builder builder, int version, long playerId) {
        builder.setVersion(memberCityVersion);
        if (version == memberCityVersion) {
            return;
        }
        for (long memberId : getMember()) {
            if (memberId == playerId) {
                continue;
            }
            AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayer(memberId);
            if (null == scenePlayer || null == scenePlayer.getMainCity()) {
                LOGGER.error("buildMemberCity error, scenePlayer is null, memberId:{}", memberId);
                continue;
            }
            if (scenePlayer.getClanId() != getEntityId() || scenePlayer.getMainCity().getTransformComponent().isAscend()) {
                continue;
            }
            Point curPoint = scenePlayer.getMainCity().getCurPoint();
            if (memberId == getClanOwnerId()) {
                builder.getOwnerPosBuilder().setX(curPoint.getX()).setY(curPoint.getY());
            } else {
                builder.addPointList(Struct.Point.newBuilder().setX(curPoint.getX()).setY(curPoint.getY()));
            }
        }
    }

    public Point findMemberCity(long memberId) {
        AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayerOrNull(memberId);
        if (scenePlayer == null) {
            return null;
        }
        long scenePlayerClanId = scenePlayer.getClanId();
        long thisClanId = getEntityId();
        if (scenePlayerClanId != thisClanId) {
            //LOGGER.error("findMemberCity error, scenePlayer {} not in clan {} {}", memberId, scenePlayerClanId, thisClanId);
            return null;
        }
        if (scenePlayer.getMainCity().getTransformComponent().isAscend()) {
            return null;
        }
        return scenePlayer.getMainCity().getCurPoint();
    }

    public void onPositionMarkChange() {
        ScenePlayerMgrComponent playerMgrComponent = getOwner().getSceneEntity().getPlayerMgrComponent();
        clanPositionMarkVersion++;
        for (long playerId : getMember()) {
            AbstractScenePlayerEntity scenePlayer = playerMgrComponent.getScenePlayer(playerId);
            if (scenePlayer == null) {
                throw new GeminiException("onPositionMarkChange scenePlayer :{} is null", playerId);
            }
            scenePlayer.getPositionMarkComponent().onClanSync(clanPositionMarkVersion);
        }
    }

    public int getClanPositionMarkVersion() {
        return clanPositionMarkVersion;
    }

    public void setRegionId(int regionId) {
        getProp().setRegionId(regionId);
    }

    private SceneClanProp getProp() {
        return getOwner().getProp();
    }

    /**
     * 根据军团长所在州设置军团州id
     */
    public void setClanRegionIdByOwner() {
        long ownerId = getClanOwnerId();
        // 此时scenePlayer可能为空（加载过程中)，为空则返回
        AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayerOrNull(ownerId);
        if (abstractScenePlayerEntity == null) {
            // 此时scenePlayer可能在kvk里面，所以null合理
            LOGGER.warn("setClanRegionIdByOwner: try get scenePlayerEntity {} is null", ownerId);
            return;
        }
        CityEntity ownerCityEntity = abstractScenePlayerEntity.getMainCity();
        if (ownerCityEntity == null) {
            LOGGER.error("setClanRegionIdByOwner: try get ownerCityEntity {} null", ownerId);
            return;
        }
        Point curPoint = abstractScenePlayerEntity.getMainCity().getTransformComponent().getCurPoint();
        refreshClanRegionId(curPoint);
        // 刷新listener
        if (ownerPointChangeEventListener != null) {
            ownerPointChangeEventListener.cancel();
            ownerPointChangeEventListener = null;
        }
        ownerPointChangeEventListener = ownerCityEntity.getEventDispatcher().addEventListenerRepeat((PointChangeEvent e) -> {
            refreshClanRegionId(e.getCurPoint());
        }, PointChangeEvent.class);
    }

    /**
     * 刷新军团州id
     *
     * @param point 军团长的主城id
     */
    private void refreshClanRegionId(Point point) {
        int regionId = MapGridDataManager.getRegionId(getOwner().getSceneEntity().getMapId(), point);
        LOGGER.info("set regionId {} to scene clan {}, x {}, y {}", regionId, getOwner().getEntityId(), point.getX(), point.getY());
        setRegionId(regionId);
    }
}

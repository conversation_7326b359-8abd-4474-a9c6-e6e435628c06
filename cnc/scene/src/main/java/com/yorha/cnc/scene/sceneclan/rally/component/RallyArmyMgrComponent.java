package com.yorha.cnc.scene.sceneclan.rally.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.mail.RallyMailService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.Int64RallyArmyInfoMapProp;
import com.yorha.game.gen.prop.RallyArmyInfoProp;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Core;
import com.yorha.proto.PlayerRally.Player_OnJoinRallyArmy_NTF;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructPlayerPB;
import it.unimi.dsi.fastutil.longs.Long2LongOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class RallyArmyMgrComponent extends AbstractComponent<RallyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(RallyArmyMgrComponent.class);
    /**
     * 集结的车头
     */
    private final ArmyEntity leaderArmy;
    /**
     * 集结本体 发车后的聚集体
     */
    private ArmyEntity rallyArmy;
    /**
     * 集结已经到达部队
     * armyId -> army
     */
    private final Map<Long, ArmyEntity> inRallyArmyMap = new Long2ObjectOpenHashMap<>();
    /**
     * 集结在路上的部队
     * armyId -> army
     */
    private final Map<Long, ArmyEntity> unArrivedArmyMap = new Long2ObjectOpenHashMap<>();
    /**
     * 参与的玩家  playerId -> armyId
     */
    private final Map<Long, Long> playerArmy = new Long2LongOpenHashMap();

    /**
     * 记录当前加的预警
     */
    private final Map<Long, Pair<WarningType, EventListener>> addWaningId = new Long2ObjectOpenHashMap<>();

    public RallyArmyMgrComponent(RallyEntity owner, ArmyEntity leader, int costEnergy) {
        super(owner);
        leaderArmy = leader;
        addNewArmy(leaderArmy, RallyArmyRoleType.RART_Leader, costEnergy);
        leaderArmy.getProp().setCurRallyId(getEntityId());
    }

    public void addWarnItem(SceneObjEntity obj, WarningType type) {
        if (!getOwner().getSceneEntity().isOpenPlayerWarning()) {
            return;
        }
        EventListener eventListener = null;
        if (type != WarningType.WT_Assist) {
            eventListener = obj.getEventDispatcher().addEventListener((e) -> {
                        if (!addWaningId.containsKey(e.getEntityId())) {
                            return;
                        }
                        // 先移除 否则会重复cancel事件
                        addWaningId.put(e.getEntityId(), Pair.of(addWaningId.get(e.getEntityId()).getFirst(), null));
                        removeWarningItem(e.getEntityId());
                    },
                    WarningRemoveEvent.class);
        }
        addWaningId.put(obj.getEntityId(), Pair.of(type, eventListener));
        for (ArmyEntity army : inRallyArmyMap.values()) {
            final AbstractScenePlayerWarningComponent playerWarningComponent = army.getScenePlayer().getWarningComponent();
            if (playerWarningComponent == null) {
                LOGGER.info("RallyArmyMgrComponent addWarningItemWhenArrive no warningComponent");
                continue;
            }
            playerWarningComponent.addWarningItem(obj, type);
        }
    }

    private void removeWarningItem(long armyId) {
        if (!getOwner().getSceneEntity().isOpenPlayerWarning()) {
            return;
        }
        if (armyId != 0) {
            Pair<WarningType, EventListener> remove = addWaningId.remove(armyId);
            if (remove == null) {
                return;
            }
            // 移除事件监听
            if (remove.getSecond() != null) {
                remove.getSecond().cancel();
            }
        }
        for (ArmyEntity army : inRallyArmyMap.values()) {
            if (army.getEntityId() == armyId) {
                continue;
            }
            final AbstractScenePlayerWarningComponent playerWarningComponent = army.getScenePlayer().getWarningComponent();
            if (playerWarningComponent == null) {
                LOGGER.info("RallyArmyMgrComponent removeWarningItem no warningComponent");
                continue;
            }
            try {
                if (armyId == 0) {
                    playerWarningComponent.removeWarningItem(addWaningId.keySet());
                } else {
                    playerWarningComponent.removeWarningItem(armyId);
                }
            } catch (Exception e) {
                LOGGER.error("removeWarningItem error ", e);
            }
        }
        if (armyId != 0) {
            return;
        }
        for (Pair<WarningType, EventListener> pair : addWaningId.values()) {
            if (pair.getSecond() != null) {
                pair.getSecond().cancel();
            }
        }
        addWaningId.clear();
    }

    private void addWarnItemWhenArrive(long playerId) {
        if (!getOwner().getSceneEntity().isOpenPlayerWarning()) {
            return;
        }
        AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (scenePlayer == null) {
            return;
        }
        ObjMgrComponent objMgrComponent = getOwner().getSceneEntity().getObjMgrComponent();
        try {
            for (Map.Entry<Long, Pair<WarningType, EventListener>> entry : addWaningId.entrySet()) {
                SceneObjEntity sceneObjEntity = objMgrComponent.getSceneObjEntity(entry.getKey());
                if (sceneObjEntity == null) {
                    continue;
                }
                final AbstractScenePlayerWarningComponent playerWarningComponent = scenePlayer.getWarningComponent();
                if (playerWarningComponent == null) {
                    LOGGER.info("RallyArmyMgrComponent addWarnItemWhenArrive no warningComponent");
                    continue;
                }
                playerWarningComponent.addWarningItem(sceneObjEntity, entry.getValue().getFirst());
            }
        } catch (Exception e) {
            LOGGER.error("addWarningItemWhenArrive error ", e);
        }
    }

    private void removeWarnItemWhenLeave(long playerId) {
        if (!getOwner().getSceneEntity().isOpenPlayerWarning()) {
            return;
        }
        AbstractScenePlayerEntity scenePlayer = getOwner().getSceneEntity().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (scenePlayer == null) {
            return;
        }
        if (scenePlayer.getWarningComponent() == null) {
            LOGGER.info("RallyArmyMgrComponent removeWarnItemWhenLeave no warningComponent");
            return;
        }
        try {
            scenePlayer.getWarningComponent().removeWarningItem(addWaningId.keySet());
        } catch (Exception e) {
            LOGGER.error("removeWarningItemWhenLeave error ", e);
        }
    }

    /**
     * 当部队发出指令加入集结
     */
    public void addNewArmy(ArmyEntity armyEntity, RallyArmyRoleType type, int costEnergy) {
        long playerId = armyEntity.getPlayerId();
        if (playerArmy.containsKey(playerId)) {
            LOGGER.error("{} owner:{} already in rally:{} playerArmy:{}", armyEntity, playerId, this, playerArmy);
            return;
        }
        playerArmy.put(playerId, armyEntity.getEntityId());
        unArrivedArmyMap.put(armyEntity.getEntityId(), armyEntity);
        addArmyTroopProp(armyEntity, type);
        // 有人加入集结 还没出发  需要内部所有人加预警
        if (type != RallyArmyRoleType.RART_Leader && rallyArmy == null) {
            addWarnItem(armyEntity, WarningType.WT_Assist);
        }
        getOwner().putCostEnergy(armyEntity.getEntityId(), costEnergy);
        LOGGER.info("{} add new army {}. type:{}", getOwner(), armyEntity, type);
    }

    /**
     * 部队到达集结
     */
    public void onArmyArriveRally(ArmyEntity armyEntity) {
        unArrivedArmyMap.remove(armyEntity.getEntityId());
        changeArmyTroopProp(armyEntity);
        inRallyArmyMap.put(armyEntity.getEntityId(), armyEntity);
        long targetId = getProp().getTargetId();
        SceneObjEntity target = getOwner().getSceneEntity().getObjMgrComponent().getSceneObjEntity(targetId);
        // 集结军队已经出发了
        if (rallyArmy != null) {
            Player_OnJoinRallyArmy_NTF.Builder builder = Player_OnJoinRallyArmy_NTF.newBuilder();
            builder.setArmyId(rallyArmy.getEntityId());
            rallyArmy.getTransformComponent().forceUpdateModelRadius();
            rallyArmy.getAoiNodeComponent().addAlwaysSyncPlayer(armyEntity.getPlayerId());
            if (getProp().getRallyState() == RallyState.RS_InBattle) {
                armyEntity.getStatusComponent().setDetailTarget(ArmyDetailState.ADS_BATTLE, target);
            } else {
                armyEntity.getStatusComponent().setDetailTarget(ArmyDetailState.ADS_MOVE_BATTLE, target);
            }
            //需要特效表现  通知下客户端
            armyEntity.getScenePlayer().sendMsgToClient(MsgType.PLAYER_ONJOINRALLYARMY_NTF, builder.build());
        } else {
            armyEntity.getStatusComponent().setDetailState(ArmyDetailState.ADS_RALLY, target, getProp().getStateEndTs());
            // 没出发  到达时 移除这个人的预警
            removeWarningItem(armyEntity.getEntityId());
            // 给这个人加预警
            addWarnItemWhenArrive(armyEntity.getPlayerId());
            // 检查是否开车
            getOwner().getStateComponent().onUnArriveArmyChange(getLatestArriveTime());
        }
        LOGGER.info("{} put {} into mgr, cur size:{}", getOwner(), armyEntity, inRallyArmyMap.size());
    }

    /**
     * 遣返成员
     *
     * @param playerId 操作玩家Id
     * @param armyId   被遣返的行军Id
     */
    public void repatriateMember(long playerId, long armyId) {
        RallyArmyInfoProp armyProp = getRallyArmyProp().get(armyId);
        if (armyProp == null) {
            // 部队已经不在集结中
            throw new GeminiException(ErrorCode.RALLY_NOT_IN_RALLY.getCodeId());
        }
        if (leaderArmy.getEntityId() == armyId) {
            // 不能遣返发起者的
            throw new GeminiException(ErrorCode.RALLY_ORGANIZER_CANT_REPATRIATE.getCodeId());
        }
        // 队员无法遣返其他人的队伍
        long targetPlayerId = armyProp.getOwnerId();
        if (getProp().getOrganizerId() != playerId && playerId != targetPlayerId) {
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT.getCodeId());
        }
        removeArmy(targetPlayerId, armyId, true);
    }

    /**
     * 移除军队  遣返/退盟/死亡/玩家改变指令
     * isNeedReturn: 是否需要回城
     */
    public void removeArmy(long playerId, long armyId, boolean isNeedReturn) {
        playerArmy.remove(playerId);
        int rollBackEnergy = getOwner().getCostEnergy(armyId);
        // 没到达的  肯定返回体力
        if (unArrivedArmyMap.containsKey(armyId)) {
            ArmyEntity army = unArrivedArmyMap.remove(armyId);
            army.getRallyComponent().onQuitRally(null, isNeedReturn, rollBackEnergy);
            removeArmyTroopProp(armyId, null);
            if (rallyArmy == null) {
                // 没出发 移除所有人对这个人的预警
                removeWarningItem(armyId);
            }
            // 等待状态 检查是否开车/解散
            getOwner().getStateComponent().onUnArriveArmyChange(getLatestArriveTime());
        }
        // 已经到达的  如果开战了，就不返还
        else if (inRallyArmyMap.containsKey(armyId)) {
            if (getProp().getRallyState() == RallyState.RS_InBattle) {
                rollBackEnergy = 0;
            }
            ArmyEntity army = inRallyArmyMap.remove(armyId);
            removeArmyTroopProp(armyId, army);
            Point rallyCurPoint = getRallyLeavePoint(army.getMainCityPoint());
            if (rallyArmy != null) {
                rallyArmy.getTransformComponent().forceUpdateModelRadius();
                rallyArmy.getBattleComponent().onSingleArmyLeaveBattleRole(army);
            } else {
                // 没出发 移除预警
                removeWarnItemWhenLeave(army.getPlayerId());
            }
            army.getRallyComponent().onQuitRally(rallyCurPoint, isNeedReturn, rollBackEnergy);
        }
        LOGGER.info("{} repatriate army. playerId: {} armyId: {}", getOwner(), playerId, armyId);
        if (rallyArmy != null) {
            rallyArmy.getAoiNodeComponent().removeAlwaysSyncPlayer(playerId);
            // 集结出发了， 参与者都跑了，解散
            if (getRallyArmyProp().size() < GameLogicConstants.RALLY_ARMY_MIN) {
                getOwner().dismiss(RallyDismissReason.RDR_NO_JOINER);
            }
        }
    }

    /**
     * 加入army prop  用于集结面板显示
     */
    private void addArmyTroopProp(ArmyEntity armyEntity, RallyArmyRoleType type) {
        StructPlayerPB.RallyArmyInfoPB.Builder builder = armyEntity.getRallyArmyInfoBuilder(type);
        RallyArmyInfoProp rallyArmyInfoProp = new RallyArmyInfoProp();
        rallyArmyInfoProp.mergeFromCs(builder.build());
        // 计算占坑数 只加了存活兵
        long total = 0;
        for (SoldierProp p : armyEntity.getProp().getTroop().getTroop().values()) {
            int num = Soldier.aliveCountOf(p);
            total += num;
            rallyArmyInfoProp.getTroop().addEmptyTroop(p.getSoldierId()).setNum(num);
        }
        // 记录加入的兵力总量
        rallyArmyInfoProp.setUseNum(total);
        getRallyArmyProp().put(armyEntity.getEntityId(), rallyArmyInfoProp);
        getProp().setCurSoldierNum(total + getOwner().getProp().getCurSoldierNum());
        LOGGER.info("{} soldier add. add: {}, now: {}", getOwner(), total, getProp().getCurSoldierNum());
    }

    /**
     * army真正到达，需要重新计算坑位（路上可能受伤）
     */
    private void changeArmyTroopProp(ArmyEntity armyEntity) {
        RallyArmyInfoProp rallyArmyInfoProp = getRallyArmyProp().get(armyEntity.getEntityId());
        rallyArmyInfoProp.setArmyState(ArmyState.AS_Staying);
        //到达时  重新计算占坑数  只加存活兵  并且记录下此时的伤兵情况(用于退出时对比计算减兵数)
        int total = 0;
        for (SoldierProp sp : armyEntity.getProp().getTroop().getTroop().values()) {
            int num = Soldier.aliveCountOf(sp);
            total += num;
            rallyArmyInfoProp.getTroop().getTroopV(sp.getSoldierId()).setNum(num);
        }
        // 已经出发了
        if (rallyArmy != null) {
            rallyArmy.getBattleComponent().onRallyAddInnerArmy(armyEntity, getProp().getMaxSoldierNum());
        }
        long dec = rallyArmyInfoProp.getUseNum() - total;
        // 路上损耗/恢复的兵 设置一下
        if (dec != 0) {
            getProp().setCurSoldierNum(getProp().getCurSoldierNum() - dec);
            // 记录加入的兵力总量
            rallyArmyInfoProp.setUseNum(total);
            LOGGER.info("{} soldier change by {}. dec: {}, now: {}", getOwner(), armyEntity.getEntityId(), dec, getProp().getCurSoldierNum());
        }
    }

    /**
     * 移除army prop
     *
     * @param armyId 行军id
     * @param army   军队实体  已经到达集结点的才会传
     */
    private void removeArmyTroopProp(long armyId, ArmyEntity army) {
        RallyArmyInfoProp prop = getRallyArmyProp().remove(armyId);
        getProp().setCurSoldierNum(getProp().getCurSoldierNum() - prop.getUseNum());
        // 集结已出发 该军队已到达 要扣除集结行军的兵
        if (rallyArmy != null && army != null) {
            rallyArmy.getBattleComponent().onRallyDelInnerArmy(armyId, army.getProp());
        }
        LOGGER.info("{} soldier del. dec: {}, now: {}", getOwner(), prop.getUseNum(), getProp().getCurSoldierNum());
    }

    /**
     * 集结车队受到伤害  战损同步到面板
     */
    public void onDamage(Collection<SoldierLossDTO> lossDTOs) {
        LOGGER.debug("rally onDamage:{}", lossDTOs);
        // 重新计算兵力 并check子army的died
        long armySoldierTotal = 0;
        for (ArmyEntity armyEntity : inRallyArmyMap.values()) {
            armyEntity.checkDie();
            long armyId = armyEntity.getEntityId();
            RallyArmyInfoProp armyInfoProp = getRallyArmyProp().get(armyId);
            if (armyInfoProp == null) {
                LOGGER.error("onSoldierChange RallyProp no army. id: {}", armyId);
                continue;
            }
            long soldierTotal = 0;
            for (SoldierProp prop : armyEntity.getProp().getTroop().getTroop().values()) {
                int soldierId = prop.getSoldierId();
                SoldierProp troopV = armyInfoProp.getTroop().getTroopV(prop.getSoldierId());
                if (troopV == null) {
                    LOGGER.error("onSoldierChange RallyProp no troop. id: {}, soldierId: {}", armyId, soldierId);
                    continue;
                }
                int num = Soldier.aliveCountOf(prop);
                troopV.setNum(num);
                soldierTotal += num;
            }
            armyInfoProp.setUseNum(soldierTotal);
            armySoldierTotal += soldierTotal;
        }
        // 加上未到达的
        for (long armyId : unArrivedArmyMap.keySet()) {
            RallyArmyInfoProp prop = getRallyArmyProp().get(armyId);
            if (prop != null) {
                armySoldierTotal += prop.getUseNum();
            }
        }
        getProp().setCurSoldierNum(armySoldierTotal);
    }

    /**
     * 车身开始开往集结点
     */
    public void onFlowArmyMove(long armyId, long arriveTs) {
        getRallyArmyProp().get(armyId).setArmyState(ArmyState.AS_Moving).setArriveTs(arriveTs);
        getOwner().getStateComponent().onUnArriveArmyChange(getLatestArriveTime());
    }

    /**
     * 判断玩家是否可以加入集结
     */
    public Core.Code checkPlayerCanJoin(long playerId, long soldierNum) {
        if (playerArmy.containsKey(playerId)) {
            return ErrorCode.RALLY_ALREADY_IN.getCode();
        }
        if (getProp().getCurSoldierNum() + soldierNum > getProp().getMaxSoldierNum()) {
            return ErrorCode.RALLY_NUM_LIMIT.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 集结车队创建成功
     */
    public void onRallyArmyCreated(ArmyEntity armyEntity) {
        this.rallyArmy = armyEntity;
        // 改写参与者的依附对象
        for (ArmyEntity army : inRallyArmyMap.values()) {
            army.getProp().setAttachId(armyEntity.getEntityId());
        }
    }

    /**
     * 集结车队解散 处理集结中的军队和集结体
     */
    public void returnRallyAllArmy(RallyDismissReason reason) {
        leaderArmy.getScenePlayer().getRallyComponent().onRallyDelete(getOwner());
        // 要移除所有所有的预警
        removeWarningItem(0);
        if (reason == RallyDismissReason.RDR_OCCUPY_TARGET) {
            // 打下来占领了   处理没到达的 返回体力
            for (ArmyEntity armyEntity : unArrivedArmyMap.values()) {
                // 未到的人  能进去的，改写成援助指令  不能进去的回家
                armyEntity.getRallyComponent().rallyMapBuildingEnd(getProp().getTargetId());
            }
        } else {
            Set<Long> players = new HashSet<>();
            // 没到达的  返回体力
            for (ArmyEntity armyEntity : unArrivedArmyMap.values()) {
                armyEntity.getRallyComponent().onQuitRally(null, true, getOwner().getCostEnergy(armyEntity.getEntityId()));
                players.add(armyEntity.getPlayerId());
            }
            for (ArmyEntity armyEntity : inRallyArmyMap.values()) {
                players.add(armyEntity.getPlayerId());
                // 因为leader的城没了导致的解散 leader不用处理
                if (reason == RallyDismissReason.RDR_ORGANIZER_CITY_ASCEND && armyEntity.getEntityId() == leaderArmy.getEntityId()) {
                    continue;
                }
                Point rallyCurPoint = getRallyLeavePoint(armyEntity.getMainCityPoint());
                int costEnergy = getOwner().getCostEnergy(armyEntity.getEntityId());
                if (getProp().getRallyState() == RallyState.RS_InBattle) {
                    // 已经到达的  如果开战了 不返回体力
                    costEnergy = 0;
                }
                armyEntity.getRallyComponent().onQuitRally(rallyCurPoint, true, costEnergy);
            }
            // 发解散邮件
            if (!players.isEmpty()) {
                sendDismissMail(reason, players);
            }
        }
        inRallyArmyMap.clear();
        unArrivedArmyMap.clear();
        if (rallyArmy != null) {
            rallyArmy.getMoveComponent().stopMove();
            rallyArmy.deleteObj();
            rallyArmy = null;
        }
    }

    /**
     * 发解散邮件
     */
    private void sendDismissMail(RallyDismissReason reason, Set<Long> players) {
        if (!getOwner().getSceneEntity().isMainScene()) {
            return;
        }
        int mailId = ResHolder.getResService(RallyMailService.class).getDismissMail(reason);
        if (mailId == 0) {
            return;
        }
        StructMail.MailSendParams.Builder mail = StructMail.MailSendParams.newBuilder().setMailTemplateId(mailId);
        switch (getProp().getTargetType()) {
            case RTT_CITY:
                mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayText(""))
                        .addDatas(MsgHelper.buildDisPlayText(getProp().getTargetCardHead().getName()));
                break;
            case RTT_RALLY_ARMY:
                mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayText(getProp().getTargetClanShortName()))
                        .addDatas(MsgHelper.buildDisPlayMultiLangKey("rally_target_2"));
                break;
            case RTT_MAP_BUILDING:
                if (getProp().getTargetClanId() == 0) {
                    mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                            .addDatas(MsgHelper.buildDisPlayMultiLangKey("rally_target_1"));
                } else {
                    mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                            .addDatas(MsgHelper.buildDisPlayText(getProp().getTargetClanShortName()));
                }
                mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, getProp().getTargetTemplateId()));
                break;
            case RTT_MONSTER:
                mail.getTitleBuilder().getSubTitleDataBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayText(""))
                        .addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_MONSTER_ID, getProp().getTargetTemplateId()));
                break;
            default:
                return;
        }
        //  TODO (kvk接入)
        int zoneId = getOwner().getLeaderArmy().getScenePlayer().getZoneId();
        List<CommonMsg.MailReceiver> receivers = players.stream()
                .map(playerId -> CommonMsg.MailReceiver.newBuilder().setPlayerId(playerId).setZoneId(zoneId).build())
                .collect(Collectors.toList());
        MailUtil.sendMailToPlayers(receivers, mail.build());
    }

    /**
     * 获取等待中的行军最晚到达时间
     */
    public long getLatestArriveTime() {
        long latestArriveTime = 0;
        for (ArmyEntity armyEntity : unArrivedArmyMap.values()) {
            long moveArriveTime = armyEntity.getMoveComponent().getMoveArriveTime();
            latestArriveTime = Math.max(latestArriveTime, moveArriveTime);
        }
        return latestArriveTime;
    }

    /**
     * 到达集结的兵力统计  用于初始化集结行军
     */
    public HashMap<Integer, SoldierProp> mergeInRallySoldiers() {
        HashMap<Integer, SoldierProp> totalTroop = Maps.newHashMap();
        for (ArmyEntity army : inRallyArmyMap.values()) {
            for (SoldierProp armySoldierProp : army.getProp().getTroop().getTroop().values()) {
                int soldierId = armySoldierProp.getSoldierId();
                SoldierProp mergedProp = totalTroop.computeIfAbsent(soldierId, k -> new SoldierProp().setSoldierId(soldierId));
                Soldier.plusMergeProp(mergedProp, armySoldierProp);
            }
        }
        return totalTroop;
    }

    /**
     * 获取集结当前位置点
     */
    public Point getRallyCurPoint() {
        // 已经出发
        if (rallyArmy != null) {
            return rallyArmy.getCurPoint();
        }
        // 还未出发
        return leaderArmy.getCurPoint();
    }

    public Point getRallyLeavePoint(Point target) {
        // 已经出发
        if (rallyArmy != null) {
            return rallyArmy.getCurPoint();
        }
        return leaderArmy.getScenePlayer().getMainCity().getTransformComponent().getLeavePosition(null, target);
    }

    public ArmyEntity getLeaderArmy() {
        return leaderArmy;
    }

    public ArmyEntity getRallyArmy() {
        return rallyArmy;
    }

    public RallyInfoProp getProp() {
        return getOwner().getProp();
    }

    public Int64RallyArmyInfoMapProp getRallyArmyProp() {
        return getOwner().getRallyArmyProp();
    }

    public Set<Long> getInRallyArmyIds() {
        Set<Long> playerIds = new HashSet<>();
        inRallyArmyMap.values().forEach(armyEntity -> {
            playerIds.add(armyEntity.getPlayerId());
        });
        return playerIds;
    }

    @Override
    public void init() {

    }

    public Collection<ArmyEntity> getInRallyArmies() {
        return inRallyArmyMap.values();
    }

    public ArmyEntity findInRallyArmy(long armyId) {
        return inRallyArmyMap.get(armyId);
    }

}

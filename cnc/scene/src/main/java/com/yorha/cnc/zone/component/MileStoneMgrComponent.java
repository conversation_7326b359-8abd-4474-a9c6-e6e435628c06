package com.yorha.cnc.zone.component;

import com.google.common.collect.Maps;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.mainScene.IMainScene;
import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.IMileStoneTaskHandler;
import com.yorha.cnc.scene.milestone.bean.ClanBuildData;
import com.yorha.cnc.scene.milestone.bean.ClanOccupyBuildData;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.cnc.scene.milestone.bean.ZoneOccupyBuildData;
import com.yorha.cnc.scene.milestone.handler.*;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.zone.IZone;
import com.yorha.common.constant.Constants;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.resource.resservice.milestone.MileStoneTemplateService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncMileStone;
import res.template.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 里程碑
 *
 * <AUTHOR>
 */
public abstract class MileStoneMgrComponent<T extends AbstractEntity> extends AbstractComponent<T> {
    private static final Logger LOGGER = LogManager.getLogger(MileStoneMgrComponent.class);
    private final IZone iZone;

    private final Map<Integer, AbstractMileStoneHandler> taskHandlerMap = new HashMap<>();
    /**
     * 变异人小屋等级
     */
    protected int rallyMonsterLevel;

    /**
     * 卡巴尔营地等级
     */
    protected int smallCableCampLevel = 0;

    /**
     * 进度守护定时器（填充假数据）
     */
    private String daemonSchedule;

    public MileStoneMgrComponent(T owner, IZone iZone) {
        super(owner);
        this.iZone = iZone;
    }

    public IZone getIZone() {
        return iZone;
    }

    /**
     * 获取当前里程碑
     *
     * @return 0未初始化 X里程碑Id -1里程碑已结束
     */
    public int getCurMileStone() {
        return getProp().getCurMileStone();
    }

    protected abstract boolean isCanOpen();

    /**
     * 获取prop
     */
    protected abstract SceneMileStoneModelProp getProp();

    protected abstract IMainScene getIScene();

    /**
     * 获取当前场景开始章节
     */
    protected abstract int getMileStoneStarId();

    public int getCurTemplateId() {
        return getProp().getTemplateId();
    }

    protected void onInit() {
        for (MileStoneInfoProp prop : getAllEndMileStone()) {
            for (Map.Entry<Integer, MileStoneGamePlayerOpenDataProp> gamePlayer : prop.getGamePlayerOpenData().entrySet()) {
                MilestoneGameplayUnlockTemplate valueFromMap = ResHolder.getInstance().getValueFromMap(MilestoneGameplayUnlockTemplate.class, gamePlayer.getKey());
                LOGGER.info("milestone unlock gameplay, milestoneId={}, gameId={} openTsMs={}", prop.getMileStoneId(), valueFromMap.getId(), gamePlayer.getValue().getOpenTsMs());
                switch (valueFromMap.getUnlockGameplayType()) {
                    case MSF_UNLOCK_MUTANT_HOUSE: {
                        initRallyMonsterStage(gamePlayer.getKey(), gamePlayer.getValue().getOpenTsMs());
                        break;
                    }
                    case MSF_UNLOCK_MUTANT_CAMP: {
                        initSmallCableCampStage(gamePlayer.getKey(), gamePlayer.getValue().getOpenTsMs());
                        break;
                    }
                    default: {
                        break;
                    }
                }
            }
        }
    }

    private void initSmallCableCampStage(int gameId, long openTsMs) {
        if (gameId > 0 && openTsMs > 0 && openTsMs <= SystemClock.now()) {
            MilestoneGameplayUnlockTemplate gameTemplate = ResHolder.getInstance().getValueFromMap(MilestoneGameplayUnlockTemplate.class, gameId);
            int rallyLevelConfig = Integer.parseInt(gameTemplate.getUnlockGameplayConst().split(Constants.XIA_HUA_XIAN)[0]);
            smallCableCampLevel = Math.max(rallyLevelConfig, smallCableCampLevel);
        }
    }

    private void initRallyMonsterStage(int gameId, long openTsMs) {
        if (openTsMs > 0 && openTsMs <= SystemClock.now()) {
            MilestoneGameplayUnlockTemplate gameTemplate = ResHolder.getInstance().getValueFromMap(MilestoneGameplayUnlockTemplate.class, gameId);
            int rallyLevelConfig = Integer.parseInt(gameTemplate.getUnlockGameplayConst().split(Constants.XIA_HUA_XIAN)[0]);
            rallyMonsterLevel = Math.max(rallyLevelConfig, rallyMonsterLevel);
        }
    }

    /**
     * 获取所有已结束的里程碑
     */
    private List<MileStoneInfoProp> getAllEndMileStone() {
        return getProp().getMileStoneInfo().values().stream().filter(it -> isOverStatus(it.getStatus())).sorted(Comparator.comparing(MileStoneInfoProp::getEndTsMs)).collect(Collectors.toList());
    }

    /**
     * 获取所有未结束的里程碑
     */
    private List<MileStoneInfoProp> getAllUnEndMileStone() {
        return getProp().getMileStoneInfo().values().stream().filter(it -> !isOverStatus(it.getStatus())).collect(Collectors.toList());
    }

    /**
     * 获取当前章节开始时间
     *
     * @return 无章节返回-1
     */
    public long getCurMileStoneStartTsMs() {
        if (getCurMileStone() <= 0) {
            return -1;
        }
        return getCurMileStoneProp().getStartTsMs();
    }

    /**
     * 获取当前章节结束时间
     *
     * @return 无章节返回-1
     */
    public long getCurMileStoneEndTsMs() {
        if (getCurMileStone() <= 0) {
            return -1;
        }
        return getCurMileStoneProp().getEndTsMs();
    }

    private String milestoneScheduleId(int id) {
        return getEntityId() + "-" + id;
    }

    public boolean isOverMileStone() {
        return getCurMileStone() <= 0;
    }

    /**
     * 重置里程碑（仅GM可以重置）
     */
    public void resetOrInitMileStone(boolean needReStart) {
        LOGGER.info("mileStone reset start needReStart={}", needReStart);
        getProp().setLastResetTsMs(SystemClock.now());
        getProp().getMileStoneInfo().clear();
        if (needReStart) {
            initMileStoneProp("reset");
            loadHandler();
            getCurMileStoneProp().setStatus(CommonEnum.MileStoneStatus.MSS_PROCESSING);

            broadcastAllOnline(SsPlayerMisc.BroadcastMileStoneResetCmd.newBuilder().build());
        }
        LOGGER.info("mileStone reset finished");
    }


    /**
     * load里程碑数据，已经可以对外服务，但是无定时器
     *
     * @param needStartSchedule 重启时需要组件初始化完成后统一启动定时器
     */
    public void initMileStone(boolean needStartSchedule) {
        if (!isCanOpen()) {
            LOGGER.info("Milestone unInit, reason=zoneClose openTsMs={}", getIZone().getServerOpenTsMs());
            return;
        }
        LOGGER.info("initOrLoadMileStone start, curMilestone={}", getCurMileStone());
        try {
            int curMileStone = getCurMileStone();
            if (curMileStone == 0) {
                // 初始化
                initMileStoneProp("onOpen");
            }
            // 兼容已经开服了的
            if (getCurTemplateId() == 0) {
                setTemplate();
            }
            loadHandler();
            onInit();
            if (needStartSchedule) {
                startMileStone();
            }
        } catch (GeminiException e) {
            LOGGER.error("mileStone init fail, prop={}", getProp());
        }
        LOGGER.info("initOrLoadMileStone end");
    }

    /**
     * 启动里程碑定时器
     * 外围判断是否开服
     */
    public void startMileStone() {
        LOGGER.info("mileStone start");
        resetSwitchSchedule(getCurMileStone());
        // 进度守护定时器
        tryOpenDaemonProcessSchedule();
    }

    /**
     * 查看地图建筑半开放玩法的开启时间戳
     *
     * @return 0:未开放 other:开启时间戳
     */
    public long getOpenTsMsWithMapBuildHalf() {
        try {
            // 里程碑匹配验证
            List<MileStoneInfoProp> allEndMileStone = getAllEndMileStone();
            for (MileStoneInfoProp mileStoneInfoProp : allEndMileStone) {
                for (MileStoneGamePlayerOpenDataProp gameProp : mileStoneInfoProp.getGamePlayerOpenData().values()) {
                    MilestoneGameplayUnlockTemplate unlockTemplate = ResHolder.getInstance().findValueFromMap(MilestoneGameplayUnlockTemplate.class, gameProp.getPrivateKey());
                    // 检查配置
                    if (unlockTemplate == null) {
                        continue;
                    }
                    if (unlockTemplate.getUnlockGameplayType() == CommonEnum.MileStoneFuncUnlockType.MSF_UNLOCK_PASS_HALF) {
                        return gameProp.getOpenTsMs();
                    }
                }
            }
            return 0;
        } catch (GeminiException e) {
            WechatLog.error("里程碑关卡开启判断报错", e);
            return 0;
        }
    }

    /**
     * 查看地图建筑玩法开启时间戳
     *
     * @return 0:未开放 other:开启时间戳
     */
    public long getOpenTsMsWithMapBuildParam(List<Integer> matchParam) {
        try {
            if (matchParam == null || matchParam.size() != 2) {
                LOGGER.error("MileStoneTemplateService getOpenTsMsWithMapBuildParam param size != 2, matchParam={}", matchParam);
                return 0;
            }
            // 里程碑匹配验证
            List<MileStoneInfoProp> allEndMileStone = getAllEndMileStone();
            for (MileStoneInfoProp mileStoneInfoProp : allEndMileStone) {
                // 判断开启(参数匹配 & 已开启)
                long openTsMsWithGamePlayer = getOpenTsMsWithMapBuildMapBuild(mileStoneInfoProp, matchParam);
                if (openTsMsWithGamePlayer > 0) {
                    return openTsMsWithGamePlayer;
                }
            }
            return 0;
        } catch (GeminiException e) {
            WechatLog.error("里程碑关卡开启判断报错", e);
            return 0;
        }
    }

    /**
     * 玩家登录时同步的里程碑数据
     */
    public StructMsg.ZoneMilestoneInfoByLogin buildMileStoneRewardWithPlayerStage(StructMsg.PlayerMilestoneInfoByLogin playerInfo) {
        LOGGER.info("{} atScene login start, player={} milestone curMilestone={}", LogKeyConstants.GAME_PLAYER_LOGIN, playerInfo.getPlayerId(), getCurMileStone());
        List<MileStoneInfoProp> shouldSyncMileStoneList = new ArrayList<>();
        List<StructMsg.MileStoneRewardData> rewardDataList = new ArrayList<>();
        for (MileStoneInfoProp mileStoneInfoProp : getProp().getMileStoneInfo().values()) {
            // 世界里程碑未结算
            if (isUnEnd(mileStoneInfoProp.getStatus())) {
                continue;
            }
            // 玩家已结算
            if (playerInfo.getPlayerMileStoneList().contains(mileStoneInfoProp.getMileStoneId())) {
                continue;
            }
            shouldSyncMileStoneList.add(mileStoneInfoProp);
        }

        for (MileStoneInfoProp prop : shouldSyncMileStoneList) {
            StructMsg.MileStoneRewardData mileStoneRewardData = buildMileStoneReward(prop);
            if (mileStoneRewardData != null) {
                rewardDataList.add(mileStoneRewardData);
            }
        }
        StructMsg.ZoneMilestoneInfoByLogin.Builder result = StructMsg.ZoneMilestoneInfoByLogin.newBuilder();
        result.addAllPlayerRewardData(rewardDataList);
        result.setCurMileStoneId(getCurMileStone())
                .setCurMileStoneTemplateId(getCurTemplateId())
                .setCurMileStoneEndTsMs(getCurMileStoneEndTsMs());
        LOGGER.info("{} atScene login, player={} milestone end", LogKeyConstants.GAME_PLAYER_LOGIN, playerInfo.getPlayerId());
        return result.build();
    }

    /**
     * 更新里程碑任务进度
     */
    private void updateProcess(CommonEnum.MileStoneTaskType mileStoneTaskType, MileStoneTaskData data) {
        if (isOverMileStone()) {
            return;
        }
        MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);
        for (MileStoneInfoProp mileStoneInfoProp : getProp().getMileStoneInfo().values()) {
            if (!isUnEnd(mileStoneInfoProp.getStatus())) {
                continue;
            }
            CommonEnum.MileStoneTaskType taskTypeById = resService.getTaskTypeById(mileStoneInfoProp.getMileStoneId());
            if (mileStoneTaskType != taskTypeById) {
                continue;
            }
            int mileStoneId = mileStoneInfoProp.getMileStoneId();
            IMileStoneTaskHandler handler = getMileStoneHandler(mileStoneId);
            if (handler == null) {
                continue;
            }
            // 开启后统计进度类型需要里程碑是进行中的
            if (isProcessingStatistics(handler) && !isProcessingStatus(mileStoneInfoProp.getStatus())) {
                continue;
            }

            handler.recordRankData(data);
            handler.updateProcess(data);
            updateProcessPost("processCheck");
        }
    }

    /**
     * 获取当前变异人小屋等级
     */
    public int getRallyMonsterLevel() {
        return rallyMonsterLevel;
    }

    /**
     * 获取里程碑信息
     */
    public ArrayList<CommonMsg.MileStoneData> getMileStoneBase() {
        ArrayList<CommonMsg.MileStoneData> result = new ArrayList<>();
        MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);
        for (MileStoneInfoProp mileStoneInfoProp : getProp().getMileStoneInfo().values()) {
            CommonMsg.MileStoneData.Builder build = CommonMsg.MileStoneData.newBuilder();
            build.setMileStoneId(mileStoneInfoProp.getMileStoneId());
            if (isDoingOrOver(mileStoneInfoProp.getStatus())) {
                build.setEndTsMs(mileStoneInfoProp.getEndTsMs())
                        .setProcess(mileStoneInfoProp.getProcess());
            }
            String taskConfig = resService.getTaskParamStrById(mileStoneInfoProp.getMileStoneId(), getCurTemplateId());
            build.setStatus(mileStoneInfoProp.getStatus()).setConfigParam(taskConfig);
            result.add(build.build());
        }
        return result;
    }

    /**
     * 获取里程碑排行详情
     */
    public SsScenePlayer.GetMileStoneRankInfoAns getMileStoneDesc(int mileStoneId) {
        SsScenePlayer.GetMileStoneRankInfoAns.Builder builder = SsScenePlayer.GetMileStoneRankInfoAns.newBuilder();
        MileStoneInfoProp mileStoneInfoProp = getProp().getMileStoneInfo().get(mileStoneId);
        Int64MileStoneClanInfoMapProp rankInfo = mileStoneInfoProp.getRankInfo().getRankInfoMap();
        Int32MileStoneZoneInfoMapProp zoneRankInfoMap = mileStoneInfoProp.getRankInfo().getZoneRankInfoMap();
        builder.putAllMileStoneRankData(rankInfo.getCopySsBuilder().getDatasMap());
        builder.putAllMileStoneZoneRankData(zoneRankInfoMap.getCopySsBuilder().getDatasMap());
        return builder.build();
    }

    /**
     * 基地评级发生变化
     */
    public void onPlayerEraChange(int oldEraLevel, int newEraLevel) {
        if (isOverMileStone()) {
            return;
        }
        AllPlayerCityLevelMileStoneHandler.PlayerCityEraData data = new AllPlayerCityLevelMileStoneHandler.PlayerCityEraData(oldEraLevel, newEraLevel, 1);
        updateProcess(CommonEnum.MileStoneTaskType.MST_BASE_RATING, data);
    }

    /**
     * 当联盟创建时
     */
    public void onClanCreate(long clanId, long power, int datasCount) {
        onClanPlayerMemberChange(clanId, datasCount);
        onClanPowerChange(clanId, power);
    }

    /**
     * 联盟成员发生变化
     */
    public void onClanPlayerMemberChange(long clanId, int memberSize) {
        if (isOverMileStone()) {
            return;
        }
        updateProcess(CommonEnum.MileStoneTaskType.MST_CONDITIONAL_LEGION_SIZE, new AllClanPlayerNumMileStoneHandler.ClanPlayerNumData(clanId, memberSize));
    }

    /**
     * 联盟成员发生变化
     */
    public void onClanPowerChange(long clanId, long power) {
        if (isOverMileStone()) {
            return;
        }
        updateProcess(CommonEnum.MileStoneTaskType.MST_RANKING_ALL_LEGIONS, new AllClanPowerMileStoneHandler.ClanPowerData(clanId, power));
    }

    /**
     * 当联盟占领中立建筑时
     */
    public void onMapBuildOccupyByClan(int ownerZoneId, int oldOwnerZoneId, long occupyClanId, long oldClanId, int mapBuildId) {
        if (isOverMileStone()) {
            return;
        }
        MapBuildingTemplate mapBuildingTemplate = ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, mapBuildId);
        TerritoryBuildingTemplate mapBuildLevelTemplate = ResHolder.getInstance().getValueFromMap(TerritoryBuildingTemplate.class, mapBuildId);
        ClanOccupyBuildData clanOccupyBuildData = new ClanOccupyBuildData(occupyClanId, oldClanId, mapBuildingTemplate.getType().getNumber(), mapBuildLevelTemplate.getLevel(), 1);
        updateProcess(CommonEnum.MileStoneTaskType.MST_OCCUPY_BUILDING_NUM, clanOccupyBuildData);
        updateProcess(CommonEnum.MileStoneTaskType.MST_OCCUPIED_BUILDINGS_NUM, clanOccupyBuildData);

        ZoneOccupyBuildData zoneOccupyBuildData = new ZoneOccupyBuildData(ownerZoneId, oldOwnerZoneId, mapBuildingTemplate.getType().getNumber(), mapBuildLevelTemplate.getLevel(), 1);
        updateProcess(CommonEnum.MileStoneTaskType.MST_ZONE_OCCUPY_BUILD, zoneOccupyBuildData);
    }

    /**
     * 当联盟建造军团建筑成功
     */
    public void onClanMapBuildFin(int ownerZoneId, int type, int level, long clanId) {
        if (isOverMileStone()) {
            return;
        }
        ClanBuildData data = new ClanBuildData(ownerZoneId, type, level, clanId, 1);
        updateProcess(CommonEnum.MileStoneTaskType.MST_BUILDINGS_BUILT_NUM, data);
        updateProcess(CommonEnum.MileStoneTaskType.MST_CLAN_BUILD, data);
    }

    public void onMonsterDead(CommonEnum.MonsterCategory monsterType, MonsterTemplate template, List<Long> assembledClans, Collection<Long> armys) {
        if (isOverMileStone()) {
            return;
        }
        switch (monsterType) {
            case ACT_LUOHA:
            case RALLY_MONSTER: {
                ClanKillRallyMonsterMileStoneHandler.ClanKillRallyMonsterData killMonsterData = new ClanKillRallyMonsterMileStoneHandler.ClanKillRallyMonsterData(assembledClans, template.getCategory(), template.getLevel(), 1);
                updateProcess(CommonEnum.MileStoneTaskType.MST_DEFEAT_THE_RALLY_MONSTER, killMonsterData);
                break;
            }
            case BUILDING_GUARD: {
                AllPlayerKillMonsterMileStoneHandler.KillMonsterData killMonsterData = new AllPlayerKillMonsterMileStoneHandler.KillMonsterData(template.getCategory(), template.getLevel(), 1);
                onPlayerKillMonster(killMonsterData);
                break;
            }
            case BIG_SCENE_ACTIVE:
            case SKYNET_MONSTER: {
                if (template.getCategory() == CommonEnum.MonsterCategory.SKYNET_MONSTER && MonsterEntity.isSkynetBoss(template)) {
                    return;
                }
                AllPlayerKillMonsterMileStoneHandler.KillMonsterData killMonsterData = new AllPlayerKillMonsterMileStoneHandler.KillMonsterData(template.getCategory(), template.getLevel(), armys.size());
                updateProcess(CommonEnum.MileStoneTaskType.MST_KILL_REBEL_NUM, killMonsterData);
                break;
            }
            default: {
                break;
            }
        }
    }

    /**
     * 当玩家击杀野怪
     */
    private void onPlayerKillMonster(AllPlayerKillMonsterMileStoneHandler.KillMonsterData killMonsterData) {
        if (isOverMileStone()) {
            return;
        }
        updateProcess(CommonEnum.MileStoneTaskType.MST_KILL_REBEL_NUM, killMonsterData);
    }

    /**
     * 当联盟解散
     */
    public void onClanDismiss(long clanId) {
        for (MileStoneInfoProp mileStoneInfoProp : getAllUnEndMileStone()) {
            mileStoneInfoProp.getRankInfo().removeRankInfoMapV(clanId);
        }
    }

    /**
     * 构建玩家奖励
     */
    private StructMsg.MileStoneRewardData buildMileStoneReward(MileStoneInfoProp mileStoneInfoProp) {
        AbstractMileStoneHandler handler = getMileStoneHandler(mileStoneInfoProp.getMileStoneId());
        StructMsg.MileStoneRewardData.Builder builder = StructMsg.MileStoneRewardData.newBuilder().setIsSuccess(mileStoneInfoProp.getIsSuccess()).setMileStoneId(mileStoneInfoProp.getMileStoneId());
        if (handler == null) {
            return null;
        }
        if (isUnEnd(mileStoneInfoProp.getStatus())) {
            return null;
        }
        switch (handler.getRewardRange()) {
            case MSRR_CONDITION_CLAN: {
                for (MileStoneRewardClanInfoProp clanInfoProp : mileStoneInfoProp.getRewardInfo().getRewardClanInfo().values()) {
                    for (ClanIdDataProp clanInfo : clanInfoProp.getRewardClans().values()) {
                        builder.putClan2Reward(clanInfo.getClanId(), clanInfoProp.getRewardLevel());
                    }
                }
                break;
            }
            case MSRR_CONDITION_ZONE: {
                builder.addAllRewardZoneId(mileStoneInfoProp.getRewardInfo().getRewardZone().getValues());
                break;
            }
            default: {
                break;
            }
        }
        builder.setRewardRange(handler.getRewardRange())
                .setEndTsMs(mileStoneInfoProp.getEndTsMs());
        return builder.build();
    }

    /**
     * 初始化里程碑数据
     */
    protected void initMileStoneProp(String reason) {
        LOGGER.info("mileStone init star, isGm={}", reason);
        int mileStoneStarId = getMileStoneStarId();
        boolean success = setTemplate();
        if (!success) {
            return;
        }
        MilestoneChapterTemplate originalTemplate = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, mileStoneStarId);
        List<MilestoneChapterTemplate> milestoneChapterTemplateList = ResHolder.getResService(MileStoneTemplateService.class).getChapterList(mileStoneStarId);
        for (MilestoneChapterTemplate mileStoneTemplate : milestoneChapterTemplateList) {
            getProp().addEmptyMileStoneInfo(mileStoneTemplate.getId()).setStatus(CommonEnum.MileStoneStatus.MSS_UNOPENED);
        }
        setCurMileStone(originalTemplate.getId());
        getCurMileStoneProp().setEndTsMs(milestoneStartTsMs() + TimeUtils.second2Ms(originalTemplate.getDuration())).setStatus(CommonEnum.MileStoneStatus.MSS_PROCESSING);
        LOGGER.info("mileStone init end, isGm={}", reason);
    }

    /**
     * 获取里程碑模块开始的时间
     * 原服：开服时间
     */
    public long milestoneStartTsMs() {
        return getIZone().getServerOpenTsMs();
    }

    private boolean setTemplate() {
        ServerAttributeTemplate template = ResHolder.getInstance().findValueFromMap(ServerAttributeTemplate.class, getIZone().getZoneId());
        if (template == null) {
            WechatLog.error("milestone template is null, zoneId={}", getIZone().getZoneId());
            return false;
        }
        getProp().setTemplateId(template.getMilestoneTemplateId());
        return true;
    }

    protected void setCurMileStone(int milestoneId) {
        LOGGER.info("setCurMileStone, curMilestone={}", milestoneId);
        getProp().setCurMileStone(milestoneId);
        MileStoneInfoProp mileStonePropById = getCurMileStoneProp();
        mileStonePropById.setStartTsMs(SystemClock.now());
    }

    private AbstractMileStoneHandler createHandler(CommonEnum.MileStoneTaskType taskType, MileStoneInfoProp mileStoneInfoProp) {
        AbstractMileStoneHandler handler;
        switch (taskType) {
            case MST_KILL_REBEL_NUM: {
                handler = new AllPlayerKillMonsterMileStoneHandler();
                break;
            }
            case MST_BASE_RATING: {
                handler = new AllPlayerCityLevelMileStoneHandler();
                break;
            }
            case MST_DEFEAT_THE_RALLY_MONSTER: {
                handler = new ClanKillRallyMonsterMileStoneHandler();
                break;
            }
            case MST_CONDITIONAL_LEGION_SIZE: {
                handler = new AllClanPlayerNumMileStoneHandler();
                break;
            }
            case MST_OCCUPIED_BUILDINGS_NUM: {
                handler = new ClanOccupyBuildMileStoneHandler();
                break;
            }
            case MST_RANKING_ALL_LEGIONS: {
                handler = new AllClanPowerMileStoneHandler();
                break;
            }
            case MST_OCCUPY_BUILDING_NUM: {
                handler = new AllClanOccupyBuildMileStoneHandler();
                break;
            }
            case MST_BUILDINGS_BUILT_NUM: {
                handler = new AllClanTotalBuildMileStoneHandler();
                break;
            }
            case MST_ZONE_OCCUPY_BUILD: {
                handler = new AllZoneOccupyBuildMileStoneHandler();
                break;
            }
            case MST_CLAN_BUILD: {
                handler = new ClanBuildBuildingMileStoneHandler();
                break;
            }
            case MST_PLAYER_BUILD: {
                handler = new PlayerBuildMileStoneHandler();
                break;
            }
            case MST_PLAYER_KILL_OTHER_SOLDIER: {
                handler = new PlayerKillSoldierMileStoneHandler();
                break;
            }
            case MST_PLAYER_KILL_MONSTER: {
                handler = new PlayerKillMonsterMileStoneHandler();
                break;
            }
            default: {
                throw new GeminiException("unsupported task type, type={}", taskType);
            }
        }
        if (mileStoneInfoProp != null) {
            CommonEnum.MileStoneTaskType taskTypeById = ResHolder.getResService(MileStoneTemplateService.class).getTaskTypeById(mileStoneInfoProp.getMileStoneId());
            MilestoneTaskTypeTemplate taskTypeTemplate = ResHolder.getInstance().getValueFromMap(MilestoneTaskTypeTemplate.class, taskTypeById.getNumber());
            handler.initHandler(getIScene(), mileStoneInfoProp, taskTypeTemplate.getStatisticType());
        }
        return handler;
    }

    /**
     * 初始化里程碑定时器
     */
    protected void resetSwitchSchedule(int oldMileStoneId) {
        if (getCurMileStone() < 0) {
            return;
        }
        MileStoneInfoProp mileStoneInfo = getCurMileStoneProp();
        long delayMs = mileStoneInfo.getEndTsMs() - SystemClock.now();
        if (delayMs < 0) {
            checkOrSwitchMileStone();
            return;
        }
        // 删除旧定时器
        getIZone().getTimerComponent().cancelTimer(TimerReasonType.MILESTONE_SWITCH, milestoneScheduleId(oldMileStoneId));
        getIZone().getTimerComponent().addTimerWithPrefix(milestoneScheduleId(getCurMileStone()), TimerReasonType.MILESTONE_SWITCH, this::checkOrSwitchMileStone, delayMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 切换里程碑
     */
    private void checkOrSwitchMileStone() {
        if (getCurMileStone() < 0) {
            return;
        }
        long now = SystemClock.now();
        int oldMileStoneId = getCurMileStone();
        MileStoneInfoProp curMileStoneProp = getCurMileStoneProp();

        if (curMileStoneProp.getEndTsMs() > now) {
            LOGGER.error("checkOrSwitchMileStone fail, oldMilestone={}", oldMileStoneId);
            return;
        }
        MilestoneChapterTemplate oldMileStoneTemplate = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, oldMileStoneId);
        curMileStoneProp.setStatus(CommonEnum.MileStoneStatus.MSS_SETTLED);

        int nextChapterID = oldMileStoneTemplate.getNextChapterID();
        if (nextChapterID > 0) {
            // 初始化时检测过，放心大胆用，不会空指针
            MilestoneChapterTemplate nextMileStoneTemplate = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, nextChapterID);
            MileStoneInfoProp mileStoneInfoProp = getMileStonePropById(nextChapterID);
            mileStoneInfoProp.setStatus(CommonEnum.MileStoneStatus.MSS_PROCESSING).setEndTsMs(SystemClock.now() + TimeUtils.second2Ms(nextMileStoneTemplate.getDuration()));
            setCurMileStone(nextChapterID);
            resetSwitchSchedule(oldMileStoneId);
        }

        // 记录获奖玩家（提前结束类型的里程碑需要满足任务进度才可以发奖）
        IMileStoneTaskHandler mileStoneHandler = getMileStoneHandler(oldMileStoneId);
        if (mileStoneHandler == null) {
            return;
        }

        // 标识里程碑任务是否完成（用于是否发奖）
        boolean isSuccess = mileStoneHandler.getMileStoneEndType() != CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END || mileStoneHandler.checkFin();
        // 记录发奖
        recordRewardDataByRank(oldMileStoneId, isSuccess);

        LOGGER.info("mileStone switch success, curMileStone={}", getProp().getCurMileStone());

        // 后置逻辑
        switchMileStonePost(oldMileStoneId, isSuccess, mileStoneHandler.getRewardRange());
    }

    /**
     * 切换里程碑（后置逻辑）
     * 发奖、发邮件、解锁逻辑
     */
    protected void switchMileStonePost(int oldMileStoneId, boolean isSuccess, CommonEnum.MileStoneRewardRange rewardRange) {
        // 发奖
        sendReward(oldMileStoneId, isSuccess, rewardRange);

        // 解锁功能
        unlockGameplay(oldMileStoneId);

        // QLOG
        sendMileStoneQlog(oldMileStoneId);

        // 部分里程碑需要初始化进度
        initSingleMileStoneData();

        // 进度守护定时器
        tryOpenDaemonProcessSchedule();
    }


    /**
     * 里程碑切换后置逻辑
     * 1、通知所有在线玩家，切换里程碑（player侧自己计算奖励）
     * 2、发送邮件
     */
    protected void sendReward(int oldMileStoneId, boolean isSuccess, CommonEnum.MileStoneRewardRange rewardRange) {
        MileStoneInfoProp mileStoneInfo = getMileStonePropById(oldMileStoneId);
        mileStoneInfo.setIsSuccess(isSuccess);
        Map<Long, Integer> clan2Reward = new HashMap<>();
        if (rewardRange == CommonEnum.MileStoneRewardRange.MSRR_CONDITION_CLAN) {
            for (Map.Entry<Integer, MileStoneRewardClanInfoProp> rewardLevel2Clans : mileStoneInfo.getRewardInfo().getRewardClanInfo().entrySet()) {
                MileStoneRewardClanInfoProp rewardClanProp = rewardLevel2Clans.getValue();
                for (ClanIdDataProp clanProp : rewardClanProp.getRewardClans().values()) {
                    // 屏蔽不存在的联盟
                    if (getIScene().getSceneClanOrNull(clanProp.getClanId()) == null) {
                        continue;
                    }
                    clan2Reward.put(clanProp.getClanId(), rewardLevel2Clans.getKey());
                }
            }
        }

        // 广播里程碑切换
        StructMsg.MileStoneRewardData.Builder rewardDataBuilder = StructMsg.MileStoneRewardData.newBuilder().setMileStoneId(oldMileStoneId).setIsSuccess(isSuccess).setRewardRange(rewardRange).setEndTsMs(mileStoneInfo.getEndTsMs());
        if (clan2Reward.size() > 0) {
            rewardDataBuilder.putAllClan2Reward(clan2Reward);
        }
        if (rewardRange == CommonEnum.MileStoneRewardRange.MSRR_CONDITION_ZONE) {
            rewardDataBuilder.addAllRewardZoneId(mileStoneInfo.getRewardInfo().getRewardZone().getValues());
        }
        SsPlayerMisc.BroadcastMileStoneSwitchCmd cmd = buildBroadcastMsg(oldMileStoneId, rewardDataBuilder.build());
        LOGGER.info("milestone switch broadcast, rewardMilestoneId={}, endTsMs={} rewardIds={}", cmd.getRewardData().getMileStoneId(), cmd.getCurMileStoneEndTsMs(), cmd.getRewardData().getClan2RewardMap().size());
        broadcastAllOnline(cmd);
    }

    /**
     * 里程碑切换通知所有在线，默认本zone
     */
    protected void broadcastAllOnline(GeneratedMessageV3 cmd) {
        BroadcastHelper.toSsOnlinePlayerInZone(getIZone().getZoneId(), cmd);
    }

    private void sendMileStoneQlog(int oldMileStoneId) {
        IMileStoneTaskHandler mileStoneHandler = getMileStoneHandler(oldMileStoneId);
        if (mileStoneHandler == null) {
            return;
        }
        CommonEnum.MileStoneRewardRange mileStoneRewardRange = mileStoneHandler.getRewardRange();
        int rewardNum = 0;
        MileStoneInfoProp mileStoneProp = getMileStonePropById(oldMileStoneId);
        if (mileStoneProp == null) {
            return;
        }
        switch (mileStoneRewardRange) {
            case MSRR_CONDITION_PLAYER: {
                break;
            }
            case MSRR_CONDITION_ZONE: {
                rewardNum = mileStoneProp.getRewardInfo().getRewardZone().size();
                break;
            }
            case MSRR_ALL_PLAYER: {
                if (mileStoneProp.getRewardInfo().getIsAllPlayer()) {
                    rewardNum = Constants.MILESTONE_NOT_REWARD_TAG;
                }
                break;
            }
            case MSRR_ALL_CLAN:
            case MSRR_CONDITION_CLAN: {
                for (MileStoneRewardClanInfoProp clanInfoProp : mileStoneProp.getRewardInfo().getRewardClanInfo().values()) {
                    for (ClanIdDataProp clanIdProp : clanInfoProp.getRewardClans().values()) {
                        SceneClanEntity sceneClanOrNull = getIScene().getSceneClanOrNull(clanIdProp.getClanId());
                        if (sceneClanOrNull == null) {
                            continue;
                        }
                        rewardNum += sceneClanOrNull.getMemberComponent().getMemberSize();
                    }
                }
                break;
            }
            default: {
                throw new GeminiException("sendMileStoneQlog fail, mileStoneRewardRange={}", mileStoneRewardRange);
            }
        }

        QlogCncMileStone.init(getIZone().getQlogComponent())
                .setDtEventTime(TimeUtils.now2String())
                .setMilestoneID(oldMileStoneId)
                .setAction("milestone_completed")
                .setRewardGroup(mileStoneRewardRange.name())
                .setMilestoneCompletedNum(rewardNum)
                .sendToQlog();
    }

    /**
     * 加载任务处理类
     */
    private void loadHandler() {
        for (MileStoneInfoProp mileStoneInfoProp : getProp().getMileStoneInfo().values()) {
            if (mileStoneInfoProp.getMileStoneId() < 0) {
                continue;
            }
            CommonEnum.MileStoneTaskType taskType = ResHolder.getResService(MileStoneTemplateService.class).getTaskTypeById(mileStoneInfoProp.getMileStoneId());
            taskHandlerMap.put(mileStoneInfoProp.getMileStoneId(), createHandler(taskType, mileStoneInfoProp));
        }
    }

    /**
     * 记录获奖
     */
    protected void recordRewardDataByRank(int oldMileStoneId, boolean isSuccess) {
        if (oldMileStoneId <= 0) {
            return;
        }
        AbstractMileStoneHandler handler = getMileStoneHandler(oldMileStoneId);
        if (handler == null) {
            LOGGER.error("mileStone handler is null, mileStoneId={}", oldMileStoneId);
            return;
        }
        // 走进度条的但没完成
        if (!isSuccess) {
            return;
        }

        MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);
        MileStoneInfoProp prop = handler.getProp();
        CommonEnum.MileStoneRewardRange rewardRange = handler.getRewardRange();
        // 奖励prop
        MileStoneRewardProp rewardInfo = prop.getRewardInfo();
        // 排行prop
        switch (rewardRange) {
            case MSRR_CONDITION_ZONE: {
                Int32MileStoneZoneInfoMapProp zoneRankInfoMap = prop.getRankInfo().getZoneRankInfoMap();
                for (MileStoneZoneInfoProp zoneInfo : zoneRankInfoMap.values()) {
                    rewardInfo.addRewardZone(zoneInfo.getZoneId());
                }
                break;
            }
            case MSRR_CONDITION_PLAYER: {
                break;
            }
            case MSRR_ALL_PLAYER: {
                rewardInfo.setIsAllPlayer(true);
                break;
            }
            case MSRR_ALL_CLAN: {
                MileStoneRewardClanInfoProp mileStoneRewardClanInfoProp = rewardInfo.getRewardClanInfo().addEmptyValue(1);
                for (SceneClanEntity sceneClan : getIScene().getAllSceneClan()) {
                    mileStoneRewardClanInfoProp.addEmptyRewardClans(sceneClan.getClanId());
                }
                break;
            }
            case MSRR_CONDITION_CLAN: {
                Int64MileStoneClanInfoMapProp rankProp = prop.getRankInfo().getRankInfoMap();
                Collection<MileStoneClanInfoProp> allData = rankProp.values();
                // 按积分重排
                List<MileStoneClanInfoProp> sortClanProp = sortRankByScore(allData);
                // 获取条件筛选
                List<MileStoneClanInfoProp> finClanProp = handler.filterMeetScore(sortClanProp);

                // 当前奖励等级
                int curLevel = 1;
                for (int i = 0; i < finClanProp.size(); i++) {
                    MileStoneClanInfoProp mileStoneClanInfoProp = finClanProp.get(i);
                    MileStoneTemplateService.MileStoneRewardNode rewardByLevel = resService.getRewardByLevel(oldMileStoneId, curLevel);
                    // 根据奖励等级来限制排行领奖
                    if (rewardByLevel == null) {
                        break;
                    }
                    if (rewardByLevel.getReward() <= 0) {
                        continue;
                    }
                    if (!rewardByLevel.isInRange(i)) {
                        curLevel++;
                    }
                    MileStoneRewardClanInfoProp rewardClanInfoV = rewardInfo.getRewardClanInfoV(curLevel);
                    if (rewardClanInfoV == null) {
                        rewardClanInfoV = rewardInfo.getRewardClanInfo().addEmptyValue(curLevel);
                    }
                    // 记录奖励联盟
                    if (getIScene().getSceneClanOrNull(mileStoneClanInfoProp.getClanId()) != null) {
                        rewardClanInfoV.addEmptyRewardClans(mileStoneClanInfoProp.getClanId());
                    } else {
                        LOGGER.error("clan is null, clanId={}", mileStoneClanInfoProp.getClanId());
                    }
                }
                break;
            }
            default: {
                throw new GeminiException(StringUtils.format("unsupported Reward Range, rewardRange={}", rewardRange));
            }
        }
        // 低频操作，多就多了，真多的时候就要考虑下实现是不是有问题了为啥要记这么多
        LOGGER.info("recordRewardDataByRank, rewardInfo={}", rewardInfo);
    }

    /**
     * 初始化单个里程碑数据
     */
    private void initSingleMileStoneData() {
        if (isOverMileStone()) {
            return;
        }
        MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);
        CommonEnum.MileStoneTaskType taskTypeById = resService.getTaskTypeById(getCurMileStone());
        AbstractMileStoneHandler mileStoneHandler = getMileStoneHandler(getCurMileStone());
        if (mileStoneHandler == null) {
            LOGGER.error("handler is null, milestone={}", getCurMileStone());
            return;
        }
        switch (taskTypeById) {
            case MST_CONDITIONAL_LEGION_SIZE: {
                MileStoneInfoProp curMileStoneProp = getCurMileStoneProp();
                for (SceneClanEntity sceneClan : getIScene().getAllSceneClan()) {
                    int memberSize = sceneClan.getMemberComponent().getMemberSize();
                    MileStoneClanInfoProp mileStoneClanInfoProp = curMileStoneProp.getRankInfo().getRankInfoMap().addEmptyValue(sceneClan.getEntityId());
                    mileStoneHandler.setClanScore(mileStoneClanInfoProp, sceneClan.getEntityId(), memberSize, "全部军团中超过一定人数的军团数量");
                }
                updateProcessPost("initProcessCheck");
                break;
            }
            case MST_RANKING_ALL_LEGIONS: {
                MileStoneInfoProp curMileStoneProp = getCurMileStoneProp();
                for (SceneClanEntity sceneClan : getIScene().getAllSceneClan()) {
                    long power = sceneClan.getProp().getPower();
                    MileStoneClanInfoProp mileStoneClanInfoProp = curMileStoneProp.getRankInfo().getRankInfoMap().addEmptyValue(sceneClan.getEntityId());
                    mileStoneHandler.setClanScore(mileStoneClanInfoProp, sceneClan.getEntityId(), power, "每个玩家的探索迷雾数量");
                }
                break;
            }
            case MST_BASE_RATING:
            case MST_OCCUPY_BUILDING_NUM: {
                updateProcessPost("initProcessCheck");
                break;
            }
            default:
                break;
        }
    }

    /**
     * 变更进度的后续逻辑，主要是check
     */
    private void updateProcessPost(String reason) {
        IMileStoneTaskHandler mileStoneHandler = getMileStoneHandler(getCurMileStone());
        if (mileStoneHandler == null) {
            return;
        }
        if (mileStoneHandler.checkFin() && isCanSwitch(getCurMileStone())) {
            earlyClosureMileStone(reason);
        }
    }

    private void unlockGameplay(int mileStoneId) {
        LOGGER.info("ZoneMileStoneMgrComponent unlockGamePlayer, milestone={}", mileStoneId);
        MilestoneChapterTemplate template = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, mileStoneId);
        MileStoneInfoProp mileStoneProp = getMileStonePropById(mileStoneId);
        if (mileStoneProp == null) {
            return;
        }
        LOGGER.info("ZoneMileStoneMgrComponent unlockGameplay, unlock={}", template.getUnlockGameplayTypeList());
        for (Integer gameId : template.getUnlockGameplayTypeList()) {
            MilestoneGameplayUnlockTemplate unlockTemplate = ResHolder.getInstance().findValueFromMap(MilestoneGameplayUnlockTemplate.class, gameId);
            if (unlockTemplate == null) {
                continue;
            }
            long openTsMs = calcOpenDailyMs(unlockTemplate.getUnlockTimeType());
            LOGGER.info("ZoneMileStoneMgrComponent gameplay open config={} dailyMs={}", unlockTemplate.getId(), openTsMs);
            String[] gameParamById = ResHolder.getResService(MileStoneTemplateService.class).getGameParamById(gameId);
            switch (unlockTemplate.getUnlockGameplayType()) {
                case MSF_UNLOCK_PASS: {
                    String[] buildTypeList = gameParamById[0].split(Constants.BAN_JIAO_DOU_HAO);
                    int buildLevel = Integer.parseInt(gameParamById[1]);
                    for (String buildType : buildTypeList) {
                        CommonEnum.MapBuildingType type = CommonEnum.MapBuildingType.forNumber(Integer.parseInt(buildType));
                        getIScene().openMapBuilding(type, buildLevel, openTsMs);
                    }
                    break;
                }
                case MSF_UNLOCK_MUTANT_HOUSE: {
                    if (gameParamById.length > 0) {
                        int monsterLevel = Integer.parseInt(gameParamById[0]);
                        rallyMonsterLevel = Math.max(rallyMonsterLevel, monsterLevel);
                    }
                    break;
                }
                case MSF_UNLOCK_MUTANT_CAMP: {
                    if (gameParamById.length > 0) {
                        int monsterLevel = Integer.parseInt(gameParamById[0]);
                        smallCableCampLevel = Math.max(smallCableCampLevel, monsterLevel);
                    }
                    break;
                }
                case MSF_UNLOCK_PASS_HALF: {
                    getIScene().semiOpenMapBuilding(openTsMs);
                    break;
                }
                default:
                    break;
            }
            MileStoneGamePlayerOpenDataProp gamePlayer = mileStoneProp.getGamePlayerOpenData().addEmptyValue(gameId);
            gamePlayer.setOpenTsMs(openTsMs);
        }
    }

    /**
     * 计算延迟开启时间
     *
     * @param unlockTimeType 开启时间规则
     * @return 0:直接开启 long:延迟毫秒数
     */
    private long calcOpenDailyMs(CommonEnum.MileStoneFuncUnlockTimeType unlockTimeType) {
        switch (unlockTimeType) {
            case MSFU_NORMAL_UNLOCK: {
                return SystemClock.now();
            }
            case MSFU_SPECIAL_UNLOCK: {
                IntPairType mileStoneFuncUnlockTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getMileStoneFuncUnlockTime();
                // 获取明天的utc0时区的0点0分时间戳
                long nextDayDurMsWithNow = TimeUtils.getNextDayStartMs(SystemClock.now());

                // 当天指定时间开放
                if (TimeUtils.getHourOfDay(SystemClock.now()) < mileStoneFuncUnlockTime.getKey()) {
                    // 如果今天已经经过的小时数 小于 配置的第一个值（18个小时）
                    // 则返回 今天的零点时间戳 + 配置的第二个值
                    return nextDayDurMsWithNow - TimeUnit.DAYS.toMillis(1) + TimeUnit.HOURS.toMillis(mileStoneFuncUnlockTime.getValue());
                } else {
                    // 如果已经过了18点了，改成明天的20点开放
                    return nextDayDurMsWithNow + TimeUnit.HOURS.toMillis(mileStoneFuncUnlockTime.getValue());
                }
            }
            default: {
                throw new GeminiException("Unlock type not allowed");
            }
        }
    }

    /**
     * 按积分降序重拍，相同的积分按更新时间排
     */
    private List<MileStoneClanInfoProp> sortRankByScore(Collection<MileStoneClanInfoProp> data) {
        return data.stream()
                .sorted(Comparator
                        .comparing(MileStoneClanInfoProp::getScore).reversed()
                        .thenComparing(MileStoneClanInfoProp::getLastUpdateTsMs))
                .collect(Collectors.toList());
    }

    /**
     * 是否可以切换
     */
    private boolean isCanSwitch(int mileStoneId) {
        if (isOverMileStone()) {
            return false;
        }
        MileStoneInfoProp mileStoneProp = getMileStonePropById(mileStoneId);
        if (mileStoneProp == null) {
            return false;
        }
        if (isOverStatus(mileStoneProp.getStatus())) {
            return false;
        }
        // 里程碑进行中 && 是任务完成即立即切换
        IMileStoneTaskHandler mileStoneHandler = getMileStoneHandler(mileStoneId);
        if (mileStoneHandler == null) {
            return false;
        }
        return isProcessingStatus(mileStoneProp.getStatus()) && isTaskFinOver(mileStoneHandler.getMileStoneEndType());
    }

    /**
     * 里程碑任务是完成就立即切换类型
     */
    private boolean isTaskFinOver(CommonEnum.MileStoneEndType mileStoneEndType) {
        return mileStoneEndType == CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END;
    }

    /**
     * 提前结束里程碑
     */
    private void earlyClosureMileStone(String reason) {
        int oldCurMileStone = getCurMileStone();
        getCurMileStoneProp().setEndTsMs(SystemClock.now());
        checkOrSwitchMileStone();
        LOGGER.info("mileStone={} over, curMileStone={} reason={}", oldCurMileStone, getCurMileStone(), reason);
    }


    /**
     * 进行状态
     */
    private static boolean isProcessingStatus(CommonEnum.MileStoneStatus status) {
        return status == CommonEnum.MileStoneStatus.MSS_PROCESSING;
    }

    /**
     * 结束状态
     */
    private static boolean isOverStatus(CommonEnum.MileStoneStatus status) {
        return status == CommonEnum.MileStoneStatus.MSS_SETTLED;
    }

    /**
     * 未结束状态
     */
    private static boolean isUnEnd(CommonEnum.MileStoneStatus status) {
        return status == CommonEnum.MileStoneStatus.MSS_PROCESSING || status == CommonEnum.MileStoneStatus.MSS_UNOPENED;
    }

    /**
     * 未开始状态
     */
    private static boolean isDoingOrOver(CommonEnum.MileStoneStatus status) {
        return status == CommonEnum.MileStoneStatus.MSS_PROCESSING || status == CommonEnum.MileStoneStatus.MSS_SETTLED;
    }


    /**
     * 是开启后统计进度的里程碑
     */
    private boolean isProcessingStatistics(IMileStoneTaskHandler handler) {
        // 真正开启时才会统计
        return handler.getMileStoneStatisticType() == CommonEnum.MileStoneStatisticType.MSST_AFTER_RECEIVING;
    }

    protected AbstractMileStoneHandler getMileStoneHandler(int mileStoneId) {
        AbstractMileStoneHandler handler = taskHandlerMap.get(mileStoneId);
        if (handler == null) {
            LOGGER.error("mileStone handler is null, mileStoneId={}", mileStoneId);
            return null;
        }
        return handler;
    }

    /**
     * 玩法是否已经开启
     */
    private long getOpenTsMsWithMapBuildMapBuild(MileStoneInfoProp prop, List<Integer> param) {
        MilestoneChapterTemplate template = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, prop.getMileStoneId());
        long result = 0;
        for (Integer gameId : template.getUnlockGameplayTypeList()) {
            MilestoneGameplayUnlockTemplate unlockTemplate = ResHolder.getInstance().findValueFromMap(MilestoneGameplayUnlockTemplate.class, gameId);
            // 检查配置
            if (unlockTemplate == null) {
                continue;
            }
            if (unlockTemplate.getUnlockGameplayType() != CommonEnum.MileStoneFuncUnlockType.MSF_UNLOCK_PASS) {
                continue;
            }

            boolean isMatchMileStone = matchMapBuildGameTemplate(unlockTemplate, param);
            if (!isMatchMileStone) {
                continue;
            }
            MileStoneGamePlayerOpenDataProp mileStoneGamePlayerOpenDataProp = prop.getGamePlayerOpenData().get(gameId);
            if (mileStoneGamePlayerOpenDataProp != null) {
                result = mileStoneGamePlayerOpenDataProp.getOpenTsMs();
                break;
            }
        }
        // 未开启
        return result;
    }

    /**
     * 获取当前里程碑prop
     */
    private MileStoneInfoProp getCurMileStoneProp() {
        int curMileStone = getProp().getCurMileStone();
        if (curMileStone == 0) {
            throw new GeminiException("init fail, mileStone not exist. curMileStone={}", curMileStone);
        }
        return getMileStonePropById(curMileStone);
    }

    /**
     * 获取指定里程碑prop
     */
    protected MileStoneInfoProp getMileStonePropById(int mileStoneId) {
        if (mileStoneId < 0) {
            WechatLog.error("mileStone is over, mileStone={}", mileStoneId);
            return null;
        }
        MileStoneInfoProp mileStoneInfoV = getProp().getMileStoneInfoV(mileStoneId);
        if (mileStoneInfoV == null) {
            WechatLog.error("init fail, mileStone not exist, mileStone={}", mileStoneId);
            mileStoneInfoV = getProp().addEmptyMileStoneInfo(mileStoneId).setStatus(CommonEnum.MileStoneStatus.MSS_UNOPENED);
        }
        return mileStoneInfoV;
    }

    /**
     * 尝试开启进度守护定时器
     */
    private void tryOpenDaemonProcessSchedule() {
        getIZone().getTimerComponent().cancelTimer(TimerReasonType.MILESTONE_DAEMON_PROCESS, daemonSchedule);
        if (isOverMileStone()) {
            return;
        }
        MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);
        MilestoneChapterTemplate chapterTemplate = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, getCurMileStone());
        MilestoneTaskTemplate taskTemplate = ResHolder.getInstance().getValueFromMap(MilestoneTaskTemplate.class, chapterTemplate.getTaskId());

        if (!resService.isFakeProcessMilestone(taskTemplate.getMilestoneTaskTypeId())) {
            return;
        }
        String[] taskParamById = resService.getTaskParamById(getCurMileStone(), getCurTemplateId());
        long maxProcess = Long.parseLong(taskParamById[0]);
        if (getCurMileStoneProp().getProcess() >= maxProcess) {
            LOGGER.error("tryOpenDaemonProcessSchedule process fail, curProcess={} maxProcess={}", getCurMileStoneProp().getProcess(), maxProcess);
            return;
        }
        checkOrSuppleProgress(getCurMileStone());
    }

    private void checkOrSuppleProgress(int milestoneId) {
        try {
            MileStoneInfoProp curMileStoneProp = getMileStonePropById(milestoneId);
            if (curMileStoneProp == null) {
                return;
            }
            MilestoneChapterTemplate chapterTemplate = ResHolder.getInstance().getValueFromMap(MilestoneChapterTemplate.class, milestoneId);
            MileStoneTemplateService resService = ResHolder.getResService(MileStoneTemplateService.class);

            int laveSec = (int) TimeUtils.ms2Second(curMileStoneProp.getEndTsMs() - SystemClock.now());
            if (laveSec <= 0) {
                LOGGER.info("checkOrSuppleProgress, but early over");
                return;
            }
            int passSec = chapterTemplate.getDuration() - laveSec;

            // 里程碑最大进度
            String[] taskParamById = resService.getTaskParamById(milestoneId, getCurTemplateId());
            long maxProgress = Long.parseLong(taskParamById[0]);
            // 里程碑当前进度百分比
            int progressRatio100 = (int) (curMileStoneProp.getProcess() * Constants.N_100 / maxProgress);
            // 需要补充的进度百分比
            int fakeRatio100 = resService.getPreRandomFakeRatio(chapterTemplate.getId(), passSec, progressRatio100);
            if (fakeRatio100 > 0) {
                // 取出实际补充
                int fakeValue = (int) Math.min((maxProgress * fakeRatio100 / Constants.N_100), maxProgress - curMileStoneProp.getProcess());
                // 无脑补充
                suppleFakeProgress(curMileStoneProp.getMileStoneId(), fakeValue);
            }

            LOGGER.info("checkOrSuppleProgress, milestoneId={} passSec={} endTsMs={} fakeRatio100={}", milestoneId, passSec, curMileStoneProp.getEndTsMs(), fakeRatio100);
            Integer nextTriggerSec = resService.getNextScheduleTriggerSec(chapterTemplate.getId(), passSec);
            if (nextTriggerSec <= 0) {
                return;
            }

            String entityKey = milestoneScheduleId(milestoneId);
            getIZone().getTimerComponent().addTimerWithPrefix(entityKey, TimerReasonType.MILESTONE_DAEMON_PROCESS, () -> checkOrSuppleProgress(milestoneId), nextTriggerSec, TimeUnit.SECONDS);
            daemonSchedule = entityKey;
        } catch (Exception e) {
            WechatLog.error("checkOrSuppleProgress, milestoneId={}", milestoneId, e);
        }
    }

    /**
     * 对指定里程碑补充假进度，无脑的哈，外围保证好数据（尤其注意是否抄上限）
     */
    private void suppleFakeProgress(int milestoneId, long value) {
        if (value <= 0) {
            LOGGER.error("suppleFakeProgress fail, milestoneId:{} value={}", milestoneId, value);
            return;
        }
        MileStoneInfoProp mileStoneInfoProp = getMileStonePropById(milestoneId);
        if (mileStoneInfoProp == null) {
            LOGGER.error("suppleFakeProgress fail, milestoneId not exist={}", milestoneId);
            return;
        }
        long oldProgress = mileStoneInfoProp.getProcess();
        setProgress(mileStoneInfoProp, oldProgress + value, "FakeProgress");
        LOGGER.info("suppleFakeProgress, milestoneId={} value={} oldProgress={} finProgress={}", milestoneId, value, oldProgress, mileStoneInfoProp.getProcess());
    }

    /**
     * 手动设置进度
     */
    private void setProgress(MileStoneInfoProp mileStoneInfoProp, long newProgress, String reason) {
        // 这里不大能加日志，太频繁了
        mileStoneInfoProp.setProcess(newProgress);
        updateProcessPost(reason);
    }

    /**
     * 根据参数匹配地图建筑玩法配置
     *
     * @return true 可以开
     */
    private boolean matchMapBuildGameTemplate(MilestoneGameplayUnlockTemplate gameTemplate, List<Integer> matchParam) {
        String[] gameParamById = ResHolder.getResService(MileStoneTemplateService.class).getGameParamById(gameTemplate.getId());
        if (gameParamById == null) {
            LOGGER.error("MileStoneTemplateService matchMapBuildGameTemplate, gameId={} matchParam={}", gameTemplate.getId(), matchParam);
            return false;
        }
        int buildType = matchParam.get(0);
        int entityBuildLevel = matchParam.get(1);

        int configBuildLevelParam = Integer.parseInt(gameParamById[1]);
        String[] buildTypeParam = gameParamById[0].split(Constants.BAN_JIAO_DOU_HAO);
        for (String param : buildTypeParam) {
            int configMapBuildType = Integer.parseInt(param);
            if (buildType != configMapBuildType) {
                continue;
            }
            // 0代表全等级解锁
            if (configBuildLevelParam == 0) {
                return true;
            }
            if (entityBuildLevel == configBuildLevelParam) {
                return true;
            }
        }
        return false;
    }

    /**
     * GM完成当前里程碑
     */
    public void finMileStoneByGm() {
        if (isOverMileStone()) {
            return;
        }
        earlyClosureMileStone("gm");
    }

    public void addMileStoneProcessByGm(int value) {
        if (isOverMileStone()) {
            return;
        }
        AbstractMileStoneHandler handler = taskHandlerMap.get(getCurMileStone());
        setProgress(handler.getProp(), handler.getProp().getProcess() + value, "gm");
    }

    /**
     * 构建广播消息
     */
    protected SsPlayerMisc.BroadcastMileStoneSwitchCmd buildBroadcastMsg(int oldMileStoneId, StructMsg.MileStoneRewardData rewardData) {
        SsPlayerMisc.BroadcastMileStoneSwitchCmd.Builder cmd;
        cmd = SsPlayerMisc.BroadcastMileStoneSwitchCmd.newBuilder()
                .setMileStoneTemplateId(getCurTemplateId())
                .setNewMileStoneId(getCurMileStone())
                .setCurMileStoneEndTsMs(getCurMileStoneEndTsMs())
                .setOldMileStoneId(oldMileStoneId)
                .setRewardData(rewardData)
                .setNewMileStoneStartTsMs(getCurMileStoneStartTsMs());
        return cmd.build();
    }

    public Map<Integer, Long> getAllValidMileStoneInfo() {
        Map<Integer, Long> allInfo = Maps.newHashMap();
        for (MileStoneInfoProp info : getProp().getMileStoneInfo().values()) {
            if (info.getStartTsMs() > 0) {
                allInfo.put(info.getMileStoneId(), info.getStartTsMs());
            }
        }
        return allInfo;
    }
}

package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.scene.sceneplayer.search.ISearch;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ScenePlayerSearchComponent extends AbstractComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerSearchComponent.class);
    private final Map<ISearch, TreeMap<Double, Long>> cacheSearchMap = new HashMap<>();

    public ScenePlayerSearchComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    public void clearSearchCache() {
        cacheSearchMap.clear();
    }

    public <T extends SceneObjEntity> T searchEntity(ISearch<T> search) {
        LOGGER.info("search ScenePlayerSearchComponent searchEntity {} {} {} {}", getOwner().getPlayerId(), search.toString(), search.getLevel(), search.getSearchRange());
        SceneEntity scene = getOwner().getScene();
        T result = null;
        if (cacheSearchMap.containsKey(search)) {
            result = searchCacheEntity(search);
            if (result != null) {
                return result;
            }
        }
        // 主城范围搜索
        Point mainCity = Objects.requireNonNull(getOwner().getMainCity()).getCurPoint();
        Circle circleRange = Circle.valueOf(mainCity.getX(), mainCity.getY(), UnitConvertUtils.meterToCm(search.getSearchRange()));
        TreeMap<Double, Long> treeMap = new TreeMap<>();
        ObjMgrComponent objComponent = getOwner().getScene().getObjMgrComponent();
        scene.getAoiMgrComponent().consumerAffectAoiGrids(
                circleRange,
                (aoiGrid) -> {
                    Set<Long> sceneObjIds = aoiGrid.getSceneObjIds();
                    for (Long sceneObjId : sceneObjIds) {
                        SceneObjEntity entity = objComponent.getSceneObjEntity(sceneObjId);
                        if (entity == null || entity.getClass() != search.getClazz()) {
                            continue;
                        }
                        if (!search.matching(getOwner(), (T) entity)) {
                            continue;
                        }
                        // 搜索是按视野格子搜索的，范围很大， 不一定在搜索圈范围内
                        if (!circleRange.containsPoint(entity.getCurPoint().getX(), entity.getCurPoint().getY())) {
                            continue;
                        }
                        treeMap.put(search.getPriorityV(getOwner(), (T) entity), entity.getEntityId());
                    }
                }
        );
        cacheSearchMap.put(search, treeMap);
        return searchCacheEntity(search);
    }

    private <T extends SceneObjEntity> T searchCacheEntity(ISearch<T> search) {
        TreeMap<Double, Long> cacheEntity = cacheSearchMap.get(search);
        if (cacheEntity == null) {
            return null;
        }
        if (MapUtils.isEmpty(cacheEntity)) {
            return null;
        }
        T result = null;
        TreeMap<Double, Long> tempMap = new TreeMap<>();
        while (cacheEntity.size() > 0) {
            Map.Entry<Double, Long> priorityEntity = cacheEntity.pollFirstEntry();
            if (priorityEntity == null) {
                continue;
            }
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(search.getClazz(), priorityEntity.getValue());
            if (entity == null) {
                continue;
            }
            if (!search.matching(getOwner(), (T) entity)) {
                tempMap.put(priorityEntity.getKey(), priorityEntity.getValue());
                continue;
            }
            result = (T) entity;
            if (search.isAlwaysFirst()) {
                tempMap.put(priorityEntity.getKey(), priorityEntity.getValue());
            }
            break;
        }
        cacheEntity.putAll(tempMap);
        return result;
    }
}

package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.scene.event.StartMoveEvent;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;
import com.yorha.game.gen.prop.MoveProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class MonsterMoveComponent extends SceneObjMoveComponent {
    private static final Logger LOGGER = LogManager.getLogger(MonsterMoveComponent.class);

    public MonsterMoveComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public MonsterEntity getOwner() {
        return (MonsterEntity) super.getOwner();
    }

    @Override
    protected TroopProp getTroopProp() {
        return getOwner().getProp().getTroop();
    }

    @Override
    protected MoveProp getMoveProp() {
        return getOwner().getProp().getMove();
    }

    @Override
    public boolean canMove() {
        // 变异人小屋不能动 防御性
        if (getOwner().getTemplate().getCategory() == CommonEnum.MonsterCategory.RALLY_MONSTER) {
            return false;
        }

        return super.canMove();
    }

    @Override
    protected void onStartMove() {
        super.onStartMove();
        try {
            getOwner().getEventDispatcher().dispatch(new StartMoveEvent());
        } catch (Exception e) {
            LOGGER.error("{} onStartMove failed ", getOwner(), e);
        }
    }

    @Override
    protected void onRefreshMove() {
        super.onRefreshMove();
        try {
            checkAndRefreshBattleMeObj();
        } catch (Exception e) {
            LOGGER.error("{} checkAndRefreshBattleMeObj failed ", getOwner(), e);
        }
    }

    @Override
    public void stopMove() {
        super.stopMove();

    }

    /**
     * 行进中速度更改
     */
    @Override
    public void onSpeedChanged() {
        super.onSpeedChanged();
    }
}

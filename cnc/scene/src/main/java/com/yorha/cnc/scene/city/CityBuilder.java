package com.yorha.cnc.scene.city;

import com.yorha.cnc.scene.city.component.CityBattleComponent;
import com.yorha.cnc.scene.city.component.CityTransformComponent;
import com.yorha.cnc.scene.city.component.NpcCityBattleComponent;
import com.yorha.cnc.scene.city.component.SceneCityBattleComponent;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.game.gen.prop.CityProp;
import com.yorha.game.gen.prop.PointProp;

/**
 * <AUTHOR>
 */
public class CityBuilder extends SceneObjBuilder<CityEntity, CityProp> {

    public CityBuilder(SceneEntity sceneEntity, long eid, CityProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getPoint();
    }

    @Override
    public CityTransformComponent transformComponent(CityEntity owner) {
        return new CityTransformComponent(owner, getPointProp());
    }

    public CityBattleComponent battleComponent(CityEntity owner) {
        if (getProp().getTemplateId() != 0) {
            return new NpcCityBattleComponent(owner);
        }
        return new SceneCityBattleComponent(owner);
    }
}

package com.yorha.cnc.scene.entity.tick;

/**
 * <AUTHOR>
 */
public class SceneTimerItem {
    private final SceneSchedule obj;
    private final long nextExecuteTsMs;

    public SceneTimerItem(SceneSchedule obj, long nextExecuteTsMs) {
        this.obj = obj;
        this.nextExecuteTsMs = nextExecuteTsMs;
    }

    public long getNextExecuteTsMs() {
        return nextExecuteTsMs;
    }

    public SceneSchedule getObj() {
        return obj;
    }
}

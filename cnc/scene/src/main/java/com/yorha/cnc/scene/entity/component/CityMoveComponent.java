package com.yorha.cnc.scene.entity.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.utils.shape.Point;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class CityMoveComponent extends SceneComponent {

    private static final Logger LOGGER = LogManager.getLogger(CityMoveComponent.class);

    public CityMoveComponent(SceneEntity owner) {
        super(owner);
    }

    public boolean isPointNavMovable(Point p) {
        LOGGER.error("Scene does not support city move!");
        return false;
    }
}

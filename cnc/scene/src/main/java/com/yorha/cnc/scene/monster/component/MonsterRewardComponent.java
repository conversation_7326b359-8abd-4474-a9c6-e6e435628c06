package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.cnc.scene.dorpObject.DropObjectFactory;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.monster.monsterFeature.MonsterFeature;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.proto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MonsterRewardComponent extends SceneObjComponent<MonsterEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MonsterRewardComponent.class);

    public MonsterRewardComponent(MonsterEntity owner) {
        super(owner);
    }

    /**
     * 发送击杀奖励 填充战报
     */
    public void sendKillAndRewardAll(MonsterFeature feature) {
        // 副本不发奖
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        Collection<Long> armys = getOwner().getBattleComponent().getBattleRole().getRelationKeys();
        if (CollectionUtils.isEmpty(armys)) {
            return;
        }
        // 各野怪击杀特性
        feature.sendKillAndRewardAll(getOwner());
    }

    public void sendBaseReward() {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        Collection<Long> armys = getOwner().getBattleComponent().getBattleRole().getRelationKeys();
        for (long armyId : armys) {
            SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(armyId);
            if (sceneObjEntity == null || sceneObjEntity.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) {
                continue;
            }
            BattleRelation relation = getOwner().getBattleComponent().relationWith(sceneObjEntity.getBattleComponent());
            if (relation == null) {
                continue;
            }
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(sceneObjEntity.getPlayerId());
            if (scenePlayer == null) {
                continue;
            }
            if (!(scenePlayer instanceof ScenePlayerEntity)) {
                LOGGER.error("scenePlayer is not ScenePlayerEntity, id={}", getOwner().getEntityId());
                continue;
            }
            ArmyEntity army = (ArmyEntity) sceneObjEntity;
            // 更新累计击杀数
            army.getHuntingComponent().incrementKillStreak();
            // 发奖
            sendNormalReward(army, (ScenePlayerEntity) scenePlayer, relation);
        }
    }

    /**
     * 普通野怪奖励   道具+击杀经验
     */
    private void sendNormalReward(ArmyEntity army, ScenePlayerEntity abstractScenePlayerEntity, BattleRelation relation) {
        // 杀怪奖励
        MonsterTemplate template = getOwner().getTemplate();
        List<ItemReward> rewards = new ArrayList<>();
        List<Integer> rewardIdList = template.getRewardIdList();

        // 要计入首杀-野怪类型
        boolean isRecordFirstKill = getOwner().getFeature().isRecordFirstKill();

        // 大世界已击杀的最高等级的野怪
        if (isRecordFirstKill && template.getLevel() <= abstractScenePlayerEntity.getProp().getKillMonsterMaxLevel()) {
            if (CollectionUtils.isNotEmpty(rewardIdList)) {
                rewardIdList.forEach(rewardId -> {
                    List<ItemReward> rewardList = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
                    if (CollectionUtils.isNotEmpty(rewardList)) {
                        rewards.addAll(rewardList);
                    }
                });
            }
        }
        BattleRecord.RoleRecord attackerRecord = relation.getContext().getRoleRecord(army.getEntityId());
        for (ItemReward reward : rewards) {
            // 战报插入奖励
            attackerRecord.addItemReward(reward);
        }
        // 填充战报经验
        int exp = SceneAddCalc.getFightMonsterExp(army.getBattleComponent().getBattleRole(), getOwner().getTemplate().getExp());
        for (BattleRecord.RoleMemberRecord record : attackerRecord.getMembers().values()) {
            record.getMainHero().setExp(exp);
            record.getDeputyHero().setExp(exp);
        }
        // 技能产生的额外奖励
        extraMonsterKillReward(army, rewards);
        sendRewardMsg(army.getScenePlayer(), rewards);
        sendMonsterKillMsg(army, exp);
    }

    private void extraMonsterKillReward(ArmyEntity army, List<ItemReward> rewards) {
        if (!getOwner().getBattleComponent().getBattleRole().isBelongsToNpc()) {
            // 如果策划配表觉得它不吃NPC增益加成，那么直接返回，不计算这个加成
            LOGGER.info("{} try get extraMonsterKillReward but not belongs to npc", getOwner());
            return;
        }
        if (getOwner().getTemplate().getEnergy() <= 0) {
            // 不消耗体力的怪，不能获得额外的奖励
            return;
        }
        int rewardId = (int) army.getAdditionComponent().getAddition(CommonEnum.BuffEffectType.ET_MONSTER_KILL_REWARD);
        if (rewardId > 0) {
            List<ItemReward> extra = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
            rewards.addAll(extra);
        }
    }

    /**
     * 发送击杀道具奖励
     */
    public void sendRewardMsg(AbstractScenePlayerEntity scenePlayer, List<ItemReward> rewards) {
        if (rewards.isEmpty()) {
            LOGGER.info("{} sendRewardMsg but rewards is empty", getOwner());
            return;
        }
        // 发送奖励
        SsPlayerMisc.AddRewardCmd.Builder builder = SsPlayerMisc.AddRewardCmd.newBuilder();
        for (ItemReward reward : rewards) {
            builder.getItemRewardsBuilder().addDatas(Struct.ItemReward.newBuilder()
                    .setItemTemplateId(reward.getItemTemplateId())
                    .setCount(reward.getCount())
                    .setWeight(reward.getWeight()).build());
        }
        builder.setReason(CommonEnum.Reason.ICR_MONSTER).setSubReason(String.valueOf(getOwner().getMonsterId()));
        scenePlayer.tellPlayer(builder.build());
    }

    /**
     * 发送击杀消息到player   加经验
     */
    public void sendMonsterKillMsg(ArmyEntity army, int exp) {
        List<Integer> list = new ArrayList<>();
        HeroProp mainHero = army.getMainHero();
        if ((mainHero != null) && (mainHero.getHeroId() > 0)) {
            list.add(mainHero.getHeroId());
        }
        HeroProp deputyHero = army.getDeputyHero();
        if ((deputyHero != null) && (deputyHero.getHeroId() > 0)) {
            list.add(deputyHero.getHeroId());
        }
        SsPlayerMisc.KillBigSceneMonsterCmd.Builder killMonsterCmd = SsPlayerMisc.KillBigSceneMonsterCmd.newBuilder()
                .setMonsterId(getOwner().getMonsterId())
                .setCreateType(getOwner().getProp().getCreateType())
                .setExp(exp)
                .setEntityId(getOwner().getEntityId())
                .addAllHeroList(list);
        // player杀怪回调
        army.getScenePlayer().tellPlayer(killMonsterCmd.build());
    }


    /**
     * 掉落
     */
    public void drop() {
        for (IntPairType pair : getOwner().getTemplate().getDropGroupIdPairList()) {
            // 掉落组概率掉落
            if (RandomUtils.nextInt(GameLogicConstants.IPPM) < pair.getValue()) {
                dropWithDropGroup(pair.getKey());
            }
        }
    }

    /**
     * 掉落掉落组
     */
    private void dropWithDropGroup(int dropGroupId) {
        if (dropGroupId <= 0) {
            return;
        }
        Point curPoint = getOwner().getCurPoint();
        List<DropObjectEntity> dropGroup = DropObjectFactory.initDropGroup(getOwner().getScene(), dropGroupId, curPoint);
        if (CollectionUtils.isNotEmpty(dropGroup)) {
            // 野怪击杀掉落消息推送
            PlayerScene.Player_MonsterDropOut_NTF.Builder response = PlayerScene.Player_MonsterDropOut_NTF.newBuilder();
            BasicPB.Int64ListPB.Builder drops = BasicPB.Int64ListPB.newBuilder();
            dropGroup.forEach(dropObject -> drops.addDatas(dropObject.getEntityId()));
            response.setDropIds(drops);

            StructPB.PointPB.Builder point = StructPB.PointPB.newBuilder();
            point.setX(curPoint.getX());
            point.setY(curPoint.getY());
            response.setPoint(point);
            getOwner().getAoiNodeComponent().broadcast(MsgType.PLAYER_MONSTERDROPOUT_NTF, response.build());
            // 加入场景
            dropGroup.forEach(SceneObjEntity::addIntoScene);
        }
    }
}

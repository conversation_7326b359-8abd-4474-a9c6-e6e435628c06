package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.newbie.NewBieTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int32SoldierMapProp;
import com.yorha.game.gen.prop.ScenePlayerSoldierProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.NewbieCheckTemplate;
import res.template.SoldierTypeTemplate;

import java.util.*;

/**
 * 特别：
 * 1. 轻伤兵的增减，不更新战力变化
 * 2. 轻伤兵的数据记录在Soldier.SlightWoundNum中，num的数量包含了SlightWoundNum
 * 3. 死、重伤兵会直接扣除num
 * 4. 获取可用士兵数量时需要剔除SlightWoundNum中，使用接口getAliveNum()
 *
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerSoldierMgrComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerSoldierMgrComponent.class);

    public AbstractScenePlayerSoldierMgrComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    protected abstract ScenePlayerSoldierProp getSoldierProp();

    public long getTotalSoldierNum() {
        long total = 0;
        for (SoldierProp prop : getSoldierProp().getSoldierMap().values()) {
            total += prop.getNum();
        }
        return total;
    }

    /**
     * 获取存活的兵力，扣除轻伤兵力
     */
    public int getAliveNum(SoldierProp soldier) {
        return Math.max(0, soldier.getNum() - soldier.getSlightWoundNum());
    }

    public Pair<Integer, Integer> addSoldier(int soldierId, int addNum, SoldierNumChangeReason reason) {
        if (addNum <= 0) {
            return null;
        }
        SoldierTypeTemplate soldierTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldierId);
        if (soldierTemplate == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ID_NULL.getCodeId());
        }
        if (!GameLogicConstants.VALID_SOLDIER_TYPE.contains(soldierTemplate.getSoldierType())) {
            throw new GeminiException(ErrorCode.SOLDIER_SOLDIER_UNLOCK.getCodeId());
        }
        Int32SoldierMapProp soldierMap = getSoldierProp().getSoldierMap();
        int beforeNum = 0;
        SoldierProp soldierProp = soldierMap.get(soldierId);
        if (soldierProp == null) {
            soldierProp = soldierMap.addEmptyValue(soldierId);
            soldierProp.setNum(addNum);
        } else {
            beforeNum = soldierProp.getNum();
            // 2024-03-14-17 策划lory说士兵数量使用int32就够。ROK单兵最大就几千万
            int afterNum = MathUtils.addExact(beforeNum, addNum);
            soldierProp.setNum(afterNum);
        }
        LOGGER.debug("{} add soldierId:{} before:{} add:{} after:{}, reason:{}", getOwner(), soldierId, beforeNum, addNum, soldierProp.getNum(), reason);
        return Pair.of(beforeNum, soldierProp.getNum());
    }

    /**
     * 增加城内士兵数量
     */
    public void addInCitySoldier(int soldierId, int addNum, SoldierNumChangeReason reason) {
        addSoldier(soldierId, addNum, reason);
    }

    protected Pair<Integer, Integer> subSoldier(int soldierId, int subNum, SoldierNumChangeReason reason) {
        if (subNum <= 0) {
            return null;
        }
        SoldierProp soldierAttrData = getSoldierProp().getSoldierMap().get(soldierId);
        if (soldierAttrData == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ATTR_NOT_EXIST);
        }
        int beforeNum = soldierAttrData.getNum();
        // 扣的兵不能大于扣完轻伤后的剩余兵力
        subNum = Math.min(getAliveNum(soldierAttrData), subNum);
        int afterNum = beforeNum - subNum;
        if (afterNum < 0) {
            afterNum = 0;
        }
        LOGGER.debug("{} sub soldierId:{} before:{} sub:{} after:{}, reason:{}", getOwner(), soldierId, beforeNum, subNum, afterNum, reason);
        soldierAttrData.setNum(afterNum);
        return Pair.of(beforeNum, afterNum);
    }

    public void subInCitySoldier(int soldierId, int subNum, SoldierNumChangeReason reason) {
        subSoldier(soldierId, subNum, reason);
    }

    /**
     * 去除所有士兵
     */
    public void removeAllSoldier() {
        getSoldierProp().getSoldierMap().clear();
    }

    /**
     * 获取城内指定id的可用士兵数量
     */
    public int getInCitySoldierNum(int soldierId) {
        SoldierProp soldierAttrData = getSoldierProp().getSoldierMap().get(soldierId);
        if (soldierAttrData == null) {
            return 0;
        }
        return getAliveNum(soldierAttrData);
    }


    /**
     * 创建行军时 检测士兵
     */
    public void checkSoldierWhenCreateArmy(Collection<Struct.Soldier> soldierAttrDataCollection) {
        checkInCitySoldierEnough(soldierAttrDataCollection);
    }

    /**
     * 创建行军时 扣除士兵
     */
    public void subSoldierWhenCreateArmy(long armyId, Collection<Struct.Soldier> soldierAttrDataCollection) {
        for (Struct.Soldier soldierAttrData : soldierAttrDataCollection) {
            subInCitySoldier(soldierAttrData.getSoldierId(), soldierAttrData.getNum(), SoldierNumChangeReason.army_out);
        }
    }

    /**
     * 行军回城后 归还士兵
     */
    public void addSoldierWhenArmyReturn(ArmyEntity army) {
        Collection<SoldierProp> list = army.getProp().getTroop().getTroop().values();
        for (SoldierProp prop : list) {
            addInCitySoldier(prop.getSoldierId(), Soldier.aliveCountOf(prop), SoldierNumChangeReason.army_return);
            addInCitySoldier(prop.getSoldierId(), prop.getSlightWoundNum(), SoldierNumChangeReason.slight_injury_recovered);
        }
        // 副本可能没城
        if (getOwner().getMainCity() != null) {
            getOwner().getMainCity().getBattleComponent().onArmyReturn(list);
        }
    }

    public boolean syncCityBattleSoldier(Collection<SoldierLossDTO> lossDTOs) {
        boolean needUpdatePower = false;
        for (SoldierLossDTO lossDTO : lossDTOs) {
            if (lossDTO.getLossData().getDead() + lossDTO.getLossData().getSevere() > 0) {
                needUpdatePower = true;
            }
            subSoldier(lossDTO.getSoldierId(), lossDTO.getLossData().getDead(), SoldierNumChangeReason.battle_dead);
            subSoldier(lossDTO.getSoldierId(), lossDTO.getLossData().getSevere(), SoldierNumChangeReason.serious_injury);
            // 轻伤兵的数量记录在 slightWounderNum中，不扣除num，slightWounderNum战斗结束时清0
            addInCitySlightSoldier(lossDTO.getSoldierId(), lossDTO.getLossData().getSlight(), SoldierNumChangeReason.slight_injury);
            // 产生了治疗，恢复轻伤
            subInCitySlightSoldier(lossDTO.getSoldierId(), lossDTO.getLossData().getTreatment(), SoldierNumChangeReason.slight_injury_recovered);
        }
        return needUpdatePower;
    }


    public void onCityLeaveBattle(Collection<SoldierProp> soldierProps) {
        // 轻伤的加回来
        for (SoldierProp prop : soldierProps) {
            SoldierProp soldierAttrData = getSoldierProp().getSoldierMap().get(prop.getSoldierId());
            int realSlightNum = prop.getSlightWoundNum();
            if (soldierAttrData.getSlightWoundNum() != prop.getSlightWoundNum()) {
                realSlightNum = soldierAttrData.getSlightWoundNum();
                LOGGER.error("self:{} soldierId:{}, ori:{}, toRecover:{}, slight to recover is wrong.pls check out!", getOwner(), prop.getSoldierId(), soldierAttrData.getSlightWoundNum(), prop.getSlightWoundNum());
            }
            subInCitySlightSoldier(prop.getSoldierId(), realSlightNum, SoldierNumChangeReason.slight_injury_recovered);
        }
        for (SoldierProp soldier : getSoldierProp().getSoldierMap().values()) {
            if (soldier.getSlightWoundNum() != 0) {
                LOGGER.error("self:{} soldierId:{}, slightWoundNum:{}, still has slight wound soldier.pls check out!", getOwner(), soldier.getSoldierId(), soldier.getSlightWoundNum());
            }
        }
    }

    private void addInCitySlightSoldier(int soldierId, int num, SoldierNumChangeReason reason) {
        if (num < 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (num == 0) {
            return;
        }
        SoldierProp soldierAttrData = getSoldierProp().getSoldierMap().get(soldierId);
        if (soldierAttrData == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ATTR_NOT_EXIST);
        }
        int beforeNum = soldierAttrData.getSlightWoundNum();
        int afterNum = Math.min(beforeNum + num, soldierAttrData.getNum());
        LOGGER.debug("{} add slight soldierId:{} before:{} num:{} after:{}, reason:{}", getOwner(), soldierId, beforeNum, num, afterNum, reason);
        soldierAttrData.setSlightWoundNum(afterNum);
    }

    private void subInCitySlightSoldier(int soldierId, int num, SoldierNumChangeReason reason) {
        if (num < 0) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        if (num == 0) {
            return;
        }
        SoldierProp soldierAttrData = getSoldierProp().getSoldierMap().get(soldierId);
        if (soldierAttrData == null) {
            throw new GeminiException(ErrorCode.SOLDIER_ATTR_NOT_EXIST);
        }
        int beforeNum = soldierAttrData.getSlightWoundNum();
        int afterNum = Math.max(beforeNum - num, 0);
        LOGGER.debug("{} sub slight soldierId:{} before:{} num:{} after:{}, reason:{}", getOwner(), soldierId, beforeNum, num, afterNum, reason);
        soldierAttrData.setSlightWoundNum(afterNum);
    }

    /**
     * 根据主将获取驻守士兵
     */
    public Map<Integer, SoldierProp> buildGarrisonSoldier() {
        int maxSoldierNum = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getCityBattleMaxSoldier();
        List<SoldierProp> soldierProps = new ArrayList<>(getSoldierProp().getSoldierMap().values());
        soldierProps.sort(Comparator.comparing(SoldierProp::getSoldierId).reversed());

        Map<Integer, SoldierProp> soldierPropMap = new HashMap<>();
        int now = 0;
        for (SoldierProp prop : soldierProps) {
            if (prop.getNum() <= 0) {
                continue;
            }
            if (now + prop.getNum() >= maxSoldierNum) {
                SoldierProp p = copySoldierProp(prop, maxSoldierNum - now);
                soldierPropMap.put(p.getSoldierId(), p);
                return soldierPropMap;
            }
            SoldierProp p = copySoldierProp(prop, prop.getNum());
            soldierPropMap.put(p.getSoldierId(), p);
            now += prop.getNum();
        }
        return soldierPropMap;
    }

    private SoldierProp copySoldierProp(SoldierProp p, int newNum) {
        StructPB.SoldierPB.Builder builder = StructPB.SoldierPB.newBuilder();
        p.copyToCs(builder);
        builder.setNum(newNum);
        SoldierProp prop = new SoldierProp();
        prop.mergeFromCs(builder.build());
        return prop;
    }

    /**
     * 不治身亡士兵邮件
     */
    public void sendHospitalFullDeadMail(int deadCount) {
        LOGGER.debug("{} hospital sendDeadMail {}", getOwner(), deadCount);
        int mailId = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getHospitalDeadMailId();
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        mailSendParams.getContentBuilder().getDisplayDataBuilder().getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, deadCount));
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getZoneId())
                        .build(),
                mailSendParams.build());
    }

    public void playerAddSoldier(int soldierId, int addNum, SoldierNumChangeReason reason) {
        addInCitySoldier(soldierId, addNum, reason);
        ArrayList<SoldierProp> toAdd = Lists.newArrayList(new SoldierProp().setSoldierId(soldierId).setNum(addNum));
        getOwner().getMainCity().getBattleComponent().onSoldierAdd(toAdd, reason);
    }

    /**
     * 玩家添加大量兵
     *
     * @param soldierMap 士兵Map<key=soldierId,val=num>
     * @param reason     数量变化原因
     */
    public void playerAddSoldier(Map<Integer, Integer> soldierMap, SoldierNumChangeReason reason) {
        ArrayList<SoldierProp> toAdd = new ArrayList<>(soldierMap.size());
        for (Map.Entry<Integer, Integer> soldierEntry : soldierMap.entrySet()) {
            Integer soldierId = soldierEntry.getKey();
            Integer addNum = soldierEntry.getValue();
            addInCitySoldier(soldierId, addNum, reason);
            toAdd.add(new SoldierProp().setSoldierId(soldierId).setNum(addNum));
        }
        getOwner().getMainCity().getBattleComponent().onSoldierAdd(toAdd, reason);
    }

    /**
     * 遣散士兵
     */
    public void dismissInCitySoldiers(Map<Integer, Integer> soldierData, SoldierNumChangeReason reason) {
        checkInCitySoldierEnough(soldierData);
        for (Integer soldierId : soldierData.keySet()) {
            subInCitySoldier(soldierId, soldierData.get(soldierId), reason);
        }
    }

    public Int32SoldierMapProp getInCitySoldier() {
        return getSoldierProp().getSoldierMap();
    }

    /**
     * 检查士兵数量是否足够
     */
    public void checkInCitySoldierEnough(Collection<Struct.Soldier> soldierData) {
        if (soldierData.size() == 0) {
            throw new GeminiException(ErrorCode.SOLDIER_ID_NULL.getCodeId());
        }
        for (Struct.Soldier soldier : soldierData) {
            int inCitySoldierNum = getInCitySoldierNum(soldier.getSoldierId());
            if (inCitySoldierNum < soldier.getNum()) {
                if (ServerContext.getServerDebugOption().isBattleTestServer()) {
                    addInCitySoldier(soldier.getSoldierId(), soldier.getNum(), SoldierNumChangeReason.gm);
                    continue;
                }
                throw new GeminiException(ErrorCode.SOLDIER_NUM_SHORTAGE.getCodeId());
            }
        }
    }

    public void checkInCitySoldierEnough(Map<Integer, Integer> soldierData) {
        if (soldierData.size() == 0) {
            throw new GeminiException(ErrorCode.SOLDIER_ID_NULL.getCodeId());
        }
        for (Integer soldierId : soldierData.keySet()) {
            int inCitySoldierNum = getInCitySoldierNum(soldierId);
            if (inCitySoldierNum < soldierData.get(soldierId)) {
                throw new GeminiException(ErrorCode.SOLDIER_NUM_SHORTAGE.getCodeId());
            }
        }
    }

    /**
     * 获取城内+城外所有存活着的士兵, 不算轻伤
     */
    public Map<Integer, Integer> getAllSoldiers() {
        Map<Integer, Integer> ret = new HashMap<>(getOwner().getArmyMgrComponent().getArmyAliveSoldiers(false));
        for (Map.Entry<Integer, SoldierProp> entry : getSoldierProp().getSoldierMap().entrySet()) {
            ret.merge(entry.getKey(), getAliveNum(entry.getValue()), Integer::sum);
        }
        return ret;
    }

    /**
     * 获取城内+城外所有存活着的士兵, 算轻伤
     */
    public Map<Integer, Integer> getAllSoldiersWithSlight() {
        Map<Integer, Integer> ret = new HashMap<>(getOwner().getArmyMgrComponent().getArmyAliveSoldiers(true));
        for (Map.Entry<Integer, SoldierProp> entry : getSoldierProp().getSoldierMap().entrySet()) {
            ret.merge(entry.getKey(), entry.getValue().getNum(), Integer::sum);
        }
        return ret;
    }

    public void checkNewbieData() {
        List<NewbieCheckTemplate> soldierCheck = ResHolder.getResService(NewBieTemplateService.class).getNewbieCheckByType(CommonEnum.NewbieCheckType.NCT_SOLDIER);
        ArrayList<String> errorStr = new ArrayList<>();
        for (NewbieCheckTemplate checkTemplate : soldierCheck) {
            int soldierId = checkTemplate.getTypeId();
            int num = checkTemplate.getValue();
            SoldierProp soldierMapV = getSoldierProp().getSoldierMapV(soldierId);
            if (soldierMapV != null && soldierMapV.getNum() == num) {
                continue;
            }
            errorStr.add(StringUtils.format("士兵校验失败.士兵={} 期望={} 实际={}", soldierId, num, soldierMapV == null ? 0 : soldierMapV.getNum()));
        }
        if (!errorStr.isEmpty()) {
            WechatLog.error(new ResourceException("新手数据异常. 异常信息：{}", errorStr));
        }
    }
}

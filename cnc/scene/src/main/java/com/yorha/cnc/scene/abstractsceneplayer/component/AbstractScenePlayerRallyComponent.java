package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.RallyInfoProp;
import com.yorha.game.gen.prop.ScenePlayerRallyBaseProp;
import com.yorha.proto.Core;
import com.yorha.proto.Core.Code;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 场景玩家 集结 组件
 *
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerRallyComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerRallyComponent.class);

    public AbstractScenePlayerRallyComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    protected abstract ScenePlayerRallyBaseProp getRallyProp();

    /**
     * 检测能否发起集结
     *
     * @param targetId   目标id
     * @param waitTime   准备时间
     * @param soldierNum 士兵数目
     * @return ErrorCode
     */
    public Core.Code checkCreateRally(long targetId, int waitTime, long soldierNum) {
        // 是否发起者没有发起其他的集结
        if (getCurRallyId() != 0) {
            return ErrorCode.RALLY_ALREADY_HAVE.getCode();
        }
        // 集结目标合理性判断
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
        final ErrorCode errorCode = targetEntity.canBeRallyWithCode();
        if (errorCode != ErrorCode.OK) {
            return errorCode.getCode();
        }
        // 是否能集结战斗的判定
        if (getOwner().getClanId() != 0 && targetEntity.getClanId() == getOwner().getClanId()) {
            return ErrorCode.RALLY_CANT.getCode();
        }
        // 集结时间判断

        // 超过上限
        if (soldierNum > getRallyMaxCap(targetId)) {
            return ErrorCode.RALLY_NUM_LIMIT.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 检测是否能加入集结
     *
     * @param rallyId    集结id
     * @param soldierNum 士兵数目
     * @return ErrorCode
     */
    public Code checkJoinRally(long rallyId, long soldierNum) {
        RallyEntity rallyEntity = getRallyEntity(rallyId);
        if (rallyEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        // 兵力超出上限 / 已经在集结中
        Code errCode = rallyEntity.getArmyMgrComponent().checkPlayerCanJoin(getEntityId(), soldierNum);
        if (!ErrorCode.isOK(errCode)) {
            return errCode;
        }
        // 等待期间不能加入
        if (!rallyEntity.getStateComponent().checkCanJoin()) {
            return ErrorCode.RALLY_WAITTING.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    /**
     * 是否能援助
     *
     * @param targetId   目标id
     * @param soldierNum 士兵数量
     * @param armyId     可能为0(army还没创出来)
     */
    public Code checkAssist(long targetId, long soldierNum, long armyId) {
        // 援助目标合理性判断
        if (getOwner().getMainCity() != null && targetId == getOwner().getMainCity().getEntityId()) {
            return ErrorCode.ASSIST_CANT.getCode();
        }
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
        // 判断联盟是否相同
        if (getOwner().getClanId() != target.getClanId()) {
            return ErrorCode.ASSIST_CANT.getCode();
        }
        return target.canBeAssist(getEntityId(), getOwner().getCampEnum(), soldierNum, armyId);
    }

    /**
     * 是否能采集
     */
    public Code checkCollect(long targetId) {
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
        if (getOwner().getClanId() != target.getClanId()) {
            return ErrorCode.CLAN_RES_BUILDING_CANNOT_COLLECT.getCode();
        }
        return target.canCollect(getEntityId());
    }

    protected void setCurRallyId(long rallyId) {
        getRallyProp().setCurRallyId(rallyId);
    }

    public long getCurRallyId() {
        return getRallyProp().getCurRallyId();
    }

    /**
     * 更新集结最大上限  只有自己的 但是英雄也有相关加成 真正用会再算一遍
     */
    public static void onRallyMaxCapChange(AbstractScenePlayerEntity entity) {
        long newRallyCap = SceneAddCalc.getRallyCap(entity);
        ScenePlayerRallyBaseProp rallyProp = entity.getRallyComponent().getRallyProp();
        if (newRallyCap < rallyProp.getRallyMaxCap()) {
            LOGGER.debug("rally cap value reduced. oldCapValue:{}, newCapValue:{}", rallyProp.getRallyMaxCap(), newRallyCap);
        }
        rallyProp.setRallyMaxCap((int) newRallyCap);
    }

    /**
     * 获取集结最大上限
     */
    public int getRallyMaxCap(long targetId) {
        SceneObjEntity targetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
        int rallyMaxCap = getRallyProp().getRallyMaxCap();
        if (targetEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
            MapBuildingEntity mapBuildingEntity = (MapBuildingEntity) targetEntity;
            int maxSoldierNum = mapBuildingEntity.getBuildingTemplate().getMaxSoldierNum();
            return Math.min(rallyMaxCap, maxSoldierNum);
        }
        // GVG建筑
        if (targetEntity instanceof BuildingEntityType && targetEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_DungeonBuilding) {
            return ((BuildingEntityType) targetEntity).getBuildingTemplate().getMaxSoldierNum();
        }
        return rallyMaxCap;
    }

    /**
     * 更新援助最大上限
     */
    public static void setBeAidedMaxCap(AbstractScenePlayerEntity entity, long newAssistCapMax) {
        ScenePlayerRallyBaseProp rallyProp = entity.getRallyComponent().getRallyProp();
        if (newAssistCapMax < rallyProp.getBeAidedMaxCap()) {
            LOGGER.debug("be aided cap value reduced. oldCapValue:{}, newCapValue:{}", rallyProp.getRallyMaxCap(), newAssistCapMax);
        }
        rallyProp.setBeAidedMaxCap((int) newAssistCapMax);
    }

    /**
     * 获取最大可被援助上限
     */
    public int getBeAidedMaxCap() {
        return getRallyProp().getBeAidedMaxCap();
    }

    /**
     * 更新当前援助行军个数
     */
    public void updateAssistArmyNum(int num) {
        getRallyProp().setAssistArmyNum(num);
    }

    /**
     * 构建集结 组织者数据
     */
    public RallyInfoProp buildOrganizerRallyProp() {
        RallyInfoProp rallyInfoProp = new RallyInfoProp();
        CityEntity city = getOwner().getMainCity();
        Point curPoint = city.getCurPoint();
        rallyInfoProp.setRallyId(getOwner().nextId())
                .setOrganizerId(getEntityId())
                .setOrganizerClanId(city.getProp().getClanId())
                .setOrganizerClanShortName(city.getProp().getClanSname())
                .setOrganizerCityId(city.getEntityId())
                .getOrganizerCityPos().setX(curPoint.getX()).setY(curPoint.getY());
        rallyInfoProp.getOrganizerCardHead().mergeFromSs(city.getCardHead());
        return rallyInfoProp;
    }

    public abstract RallyEntity createRally(ArmyEntity armyEntity, long targetId, int waitTime, int costEnergy);

    public void onRallyDelete(RallyEntity rallyEntity) {
        setCurRallyId(0);
    }

    public abstract RallyEntity getRallyEntity(long rallyId);

    public abstract RallyEntity getRallyEntity(long clanId, long rallyId);

    public RallyEntity getRallyEntityWithException(long rallyId) {
        RallyEntity rallyEntity = getRallyEntity(rallyId);
        if (rallyEntity == null) {
            throw new GeminiException(ErrorCode.SYSTEM_TARGET_NULL.getCodeId());
        }
        return rallyEntity;
    }
}

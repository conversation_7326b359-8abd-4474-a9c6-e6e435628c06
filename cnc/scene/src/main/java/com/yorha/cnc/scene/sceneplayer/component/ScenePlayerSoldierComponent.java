package com.yorha.cnc.scene.sceneplayer.component;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerSoldierMgrComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.power.PowerInterface;
import com.yorha.common.qlog.json.Army.SoldierAllConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.PlayerHospitalSoldierProp;
import com.yorha.game.gen.prop.ScenePlayerSoldierProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncArmyChange;
import res.template.SoldierTypeTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ScenePlayerSoldierComponent extends AbstractScenePlayerSoldierMgrComponent implements PowerInterface {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerSoldierComponent.class);

    private final Map<Long, Map<Integer, Integer>> outSoldiers = new HashMap<>();

    public ScenePlayerSoldierComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    public CommonEnum.PowerType getPowerType() {
        return CommonEnum.PowerType.PT_SOLDIER;
    }

    @Override
    protected ScenePlayerSoldierProp getSoldierProp() {
        return getOwner().getProp().getSoldier();
    }

    @Override
    public void subSoldierWhenCreateArmy(long armyId, Collection<Struct.Soldier> soldierAttrDataCollection) {
        if (outSoldiers.containsKey(armyId)) {
            LOGGER.error("subSoldierWhenCreateArmy but armyId is exist {} {}", getOwner(), armyId);
        }
        outSoldiers.put(armyId, new HashMap<>());
        for (Struct.Soldier soldierAttrData : soldierAttrDataCollection) {
            subInCitySoldier(soldierAttrData.getSoldierId(), soldierAttrData.getNum(), SoldierNumChangeReason.army_out);
            outSoldiers.get(armyId).put(soldierAttrData.getSoldierId(), soldierAttrData.getNum());
        }
    }

    public void onArmyRestore(ArmyEntity army) {
        long armyId = army.getEntityId();
        if (outSoldiers.containsKey(armyId)) {
            LOGGER.error("onArmyRestore but armyId is exist {} {}", getOwner(), armyId);
        }
        outSoldiers.put(armyId, new HashMap<>());
        for (SoldierProp prop : army.getProp().getTroop().getTroop().values()) {
            outSoldiers.get(armyId).put(prop.getSoldierId(), prop.getNum());
        }
    }

    /**
     * 行军回城后 归还士兵
     */
    @Override
    public void addSoldierWhenArmyReturn(ArmyEntity army) {
        long armyId = army.getEntityId();
        if (!outSoldiers.containsKey(armyId)) {
            WechatLog.error("addSoldierWhenArmyReturn but armyId not exist {} {}", getOwner(), army);
            return;
        }
        outSoldiers.remove(armyId);
        Collection<SoldierProp> list = army.getProp().getTroop().getTroop().values();
        for (SoldierProp prop : list) {
            addInCitySoldier(prop.getSoldierId(), Soldier.aliveCountOf(prop), SoldierNumChangeReason.army_return);
            LOGGER.info("addSoldierWhenArmyReturn armyId={} soldierId={} count= {} reason={}", armyId, prop.getSoldierId(), Soldier.aliveCountOf(prop), SoldierNumChangeReason.army_return);
            addInCitySoldier(prop.getSoldierId(), prop.getSlightWoundNum(), SoldierNumChangeReason.slight_injury_recovered);
            LOGGER.info("addSoldierWhenArmyReturn armyId={} soldierId={} count= {} reason={}", armyId, prop.getSoldierId(), prop.getSlightWoundNum(), SoldierNumChangeReason.slight_injury_recovered);
        }
        getOwner().getMainCity().getBattleComponent().onArmyReturn(list);
    }

    public void printSnapshot() {
        // 城内兵力快照
        List<SoldierAllConfig> soldier = new ArrayList<>();
        for (SoldierProp soldierProp : getOwner().getProp().getSoldier().getSoldierMap().values()) {
            soldier.add(new SoldierAllConfig(soldierProp));
        }
        LOGGER.info("ScenePlayer onLogin snapshot soldier {} {}", getEntityId(), soldier);
        LOGGER.info("ScenePlayer onLogin snapshot outSoldiers {} {}", getEntityId(), outSoldiers);
        Map<Long, List<SoldierAllConfig>> armySoldier = new HashMap<>();
        for (ArmyEntity armyEntity : getOwner().getArmyMgrComponent().getMyArmyList()) {
            armySoldier.put(armyEntity.getEntityId(), new ArrayList<>());
            for (SoldierProp soldierProp : armyEntity.getProp().getTroop().getTroop().values()) {
                armySoldier.get(armyEntity.getEntityId()).add(new SoldierAllConfig(soldierProp));
            }
        }
        LOGGER.info("ScenePlayer onLogin snapshot army {} {}", getEntityId(), armySoldier);
    }

    @Override
    public long calcPower() {
        long sum = 0;
        for (Map.Entry<Integer, Integer> entry : getAllSoldiersWithSlight().entrySet()) {
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(entry.getKey());
            if (soldierTemplate != null) {
                sum += (long) soldierTemplate.getPower() * entry.getValue();
            }
        }
        return sum;
    }

    /**
     * 增加城内士兵数量
     */
    @Override
    public void addInCitySoldier(int soldierId, int addNum, SoldierNumChangeReason reason) {
        Pair<Integer, Integer> data = addSoldier(soldierId, addNum, reason);
        if (data == null) {
            return;
        }
        if (reason != null) {
            // 更新战力
            if (reason.isNeedUpdatePower()) {
                getOwner().getPowerComponent().updatePower(CommonEnum.PowerType.PT_SOLDIER, reason);
                Map<Integer, Integer> soldierId2Num = getAllSoldiers();
                SsPlayerMisc.SoldierNumCmd soldierNumCmd = SsPlayerMisc.SoldierNumCmd.newBuilder().putAllSoldierId2Num(soldierId2Num).build();
                getOwner().tellPlayer(soldierNumCmd);
            }
            sendSoldierChangeQlog(soldierId, data.getFirst(), data.getSecond(), reason);
        }
    }

    @Override
    public void subInCitySoldier(int soldierId, int subNum, SoldierNumChangeReason reason) {
        Pair<Integer, Integer> data = subSoldier(soldierId, subNum, reason);
        if (data == null) {
            return;
        }
        if (reason != null) {
            // 更新战力
            if (reason.isNeedUpdatePower()) {
                getOwner().getPowerComponent().updatePower(CommonEnum.PowerType.PT_SOLDIER, reason);
            }
            sendSoldierChangeQlog(soldierId, data.getFirst(), data.getSecond(), reason);
        }
    }

    @Override
    public boolean syncCityBattleSoldier(Collection<SoldierLossDTO> lossDTOs) {
        boolean needUpdatePower = super.syncCityBattleSoldier(lossDTOs);
        if (needUpdatePower) {
            getOwner().getPowerComponent().updatePower(CommonEnum.PowerType.PT_SOLDIER, null);
        }
        return needUpdatePower;
    }

    /**
     * 和楼下addTreatOverSoldiers 属于重复代码，修改要同步
     */
    public void addTreatOverSoldiersByProp(Collection<PlayerHospitalSoldierProp> soldiers) {
        List<SoldierProp> toAdd = Lists.newArrayList();
        for (PlayerHospitalSoldierProp item : soldiers) {
            addInCitySoldier(item.getSoldierId(), item.getSevereNum(), SoldierNumChangeReason.serious_recovered);
            toAdd.add(new SoldierProp()
                    .setSoldierId(item.getSoldierId())
                    .setNum(item.getSevereNum())
            );
        }
        getOwner().getMainCity().getBattleComponent().onSoldierAdd(toAdd, SoldierNumChangeReason.serious_recovered);
    }

    /**
     * 和楼上addTreatOverSoldiersByProp 属于重复代码，修改要同步
     */
    public void addTreatOverSoldiers(Collection<StructPB.PlayerHospitalSoldierPB> soldiers) {
        List<SoldierProp> toAdd = Lists.newArrayList();
        for (StructPB.PlayerHospitalSoldierPB item : soldiers) {
            addInCitySoldier(item.getSoldierId(), item.getSevereNum(), SoldierNumChangeReason.serious_recovered);
            toAdd.add(new SoldierProp()
                    .setSoldierId(item.getSoldierId())
                    .setNum(item.getSevereNum())
            );
        }
        getOwner().getMainCity().getBattleComponent().onSoldierAdd(toAdd, SoldierNumChangeReason.serious_recovered);
    }


    /**
     * 士兵数量变化qlog
     */
    private void sendSoldierChangeQlog(int soldierId, int before, int after, SoldierNumChangeReason reason) {
        QlogCncArmyChange qlog = new QlogCncArmyChange()
                .setDtEventTime(TimeUtils.now2String())
                .setAction(reason == null ? "" : reason.name())
                .setBeforeArmyNum(before)
                .setAfterArmyNum(after)
                .setICount(after - before)
                .setAddOrReduce(after > before ? 0 : 1)
                .setArmyId(String.valueOf(soldierId));
        qlog.fillHead(getOwner().getQlogComponent());
        qlog.sendToQlog();
    }

    public void copySoldier(Map<Integer, Integer> soldierMapMap) {
        for (SoldierProp prop : getSoldierProp().getSoldierMap().values()) {
            soldierMapMap.put(prop.getSoldierId(), prop.getNum());
        }
    }
}

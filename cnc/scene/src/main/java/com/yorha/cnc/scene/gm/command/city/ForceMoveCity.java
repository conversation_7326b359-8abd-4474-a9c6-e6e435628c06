package com.yorha.cnc.scene.gm.command.city;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ForceMoveCity implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        int x = Integer.parseInt(args.get("x"));
        int y = Integer.parseInt(args.get("y"));
        scenePlayer.getMainCity().getTransformComponent().moveCity(x, y);
    }

    @Override
    public String showHelp() {
        return "ForceMoveCity x={value} y={value}";
    }


    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_PLAYER;
    }
}

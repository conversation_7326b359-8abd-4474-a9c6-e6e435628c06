package com.yorha.cnc.scene.gm.command.resbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RecycleRandomResBuilding implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        List<ResBuildingEntity> list = actor.getScene().getObjMgrComponent().getObjsByType(ResBuildingEntity.class);
        int num = Integer.parseInt(args.get("num"));
        while (num-- > 0) {
            int index = RandomUtils.nextInt(list.size());
            list.get(index).recycle(true);
        }
    }


    @Override
    public String showHelp() {
        return "RecycleRandomResBuilding num={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COLLECT;
    }
}

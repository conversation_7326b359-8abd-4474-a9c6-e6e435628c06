package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerRallyComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.io.MsgType;
import com.yorha.game.gen.prop.ScenePlayerRallyBaseProp;
import com.yorha.proto.Core;
import com.yorha.proto.User;

/**
 * <AUTHOR>
 */
public class ScenePlayerRallyComponent extends AbstractScenePlayerRallyComponent {
    public ScenePlayerRallyComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public ScenePlayerEntity getOwner() {
        return (ScenePlayerEntity) super.getOwner();
    }

    @Override
    public void init() {
        setCurRallyId(0);
    }

    @Override
    protected ScenePlayerRallyBaseProp getRallyProp() {
        return getOwner().getProp().getRally();
    }


    /**
     * 登录时看下有没有集结 有就发
     */
    public void onLogin() {
        SceneClanEntity sceneClan = getOwner().getSceneClan();
        if (sceneClan != null && sceneClan.getRallyComponent().checkExistRally()) {
            User.RallyNtfMsg.Builder builder = User.RallyNtfMsg.newBuilder().setIsExist(true);
            getOwner().sendMsgToClient(MsgType.RALLYNTFMSG, builder.build());
        }
    }

    /**
     * 检测能否发起集结
     *
     * @param targetId   目标id
     * @param waitTime   准备时间
     * @param soldierNum 士兵数目
     * @return ErrorCode
     */
    @Override
    public Core.Code checkCreateRally(long targetId, int waitTime, long soldierNum) {
        // 是否加入联盟
        if (!getOwner().isInClan()) {
            return ErrorCode.CLAN_NOT_IN.getCode();
        }
        super.checkCreateRally(targetId, waitTime, soldierNum);
        // 是否其他联盟成员没有发起对目标的集结
        SceneClanEntity sceneClan = getOwner().getSceneClan();
        if (sceneClan == null) {
            return ErrorCode.CLAN_NOT_IN.getCode();
        }
        boolean isOtherClanMemberRallyToTarget = sceneClan.getRallyComponent().checkHasRallyToTargetId(targetId);
        if (isOtherClanMemberRallyToTarget) {
            return ErrorCode.RALLY_OTHER_HAVE.getCode();
        }
        return ErrorCode.OK.getCode();
    }

    @Override
    public Core.Code checkJoinRally(long rallyId, long soldierNum) {
        // 是否加入联盟
        if (!getOwner().isInClan()) {
            return ErrorCode.CLAN_NOT_IN.getCode();
        }
        return super.checkJoinRally(rallyId, soldierNum);
    }

    @Override
    public Core.Code checkAssist(long targetId, long soldierNum, long armyId) {
        // 是否加入联盟
        if (!getOwner().isInClan()) {
            return ErrorCode.CLAN_NOT_IN.getCode();
        }
        return super.checkAssist(targetId, soldierNum, armyId);
    }

    @Override
    public RallyEntity createRally(ArmyEntity armyEntity, long targetId, int waitTime, int costEnergy) {
        SceneClanEntity sceneClan = getOwner().getSceneClan();
        RallyEntity rally = sceneClan.getRallyComponent().createRally(getOwner(), armyEntity, targetId, waitTime, costEnergy);
        setCurRallyId(rally.getEntityId());
        return rally;
    }

    @Override
    public RallyEntity getRallyEntity(long clanId, long rallyId) {
        SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClan(clanId);
        if (sceneClan == null) {
            return null;
        }
        return sceneClan.getRallyEntity(rallyId);
    }

    @Override
    public RallyEntity getRallyEntity(long rallyId) {
        SceneClanEntity sceneClan = getOwner().getSceneClan();
        if (sceneClan == null) {
            return null;
        }
        return sceneClan.getRallyEntity(rallyId);
    }

}

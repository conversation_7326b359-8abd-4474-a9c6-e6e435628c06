package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.common.actor.SceneKingdomService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsSceneKingdom.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 王国ss服务
 *
 * <AUTHOR>
 */
public class SceneKingdomServiceImpl implements SceneKingdomService {
    private static final Logger LOGGER = LogManager.getLogger(SceneKingdomServiceImpl.class);

    private final SceneActor sceneActor;

    public SceneKingdomServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public BigSceneEntity getBigScene() {
        return sceneActor.getBigScene();
    }

    @Override
    public void handleKingAppointAsk(KingAppointAsk ask) {
        // 参数检查
        if (!ask.hasKingdomOfficeId() || !ask.hasOperatorId() || !ask.hasToPlayerId()) {
            LOGGER.warn("handleKingAppointAsk: invalid ask, ask={}", ask);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        getBigScene().getKingdomComponent().appoint(ask.getOperatorId(), ask.getOperatorOfficeId(),
                ask.getKingdomOfficeId(), ask.getToPlayerId());
        sceneActor.answer(KingAppointAns.getDefaultInstance());
    }

    @Override
    public void handleKingOpenBuffAsk(KingOpenBuffAsk ask) {
        if (!ask.hasKingdomBuffId()) {
            LOGGER.warn("handleKingOpenBuffAsk: invalid ask, ask={}", ask);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        getBigScene().getKingdomComponent().openBuff(ask.getKingdomBuffId(), ask.getName());
        sceneActor.answer(KingOpenBuffAns.getDefaultInstance());
    }

    @Override
    public void handleKingSendGiftAsk(KingSendGiftAsk ask) {
        if (!ask.hasKingdomGiftId() || !ask.hasToPlayerId()) {
            LOGGER.warn("handleKingSendGiftAsk: invalid ask, ask={}", ask);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        getBigScene().getKingdomComponent().sendGift(ask.getKingdomGiftId(), ask.getToPlayerId(), ask.getClanSimpleName(), ask.getCardHead());
        sceneActor.answer(KingSendGiftAns.getDefaultInstance());
    }

    @Override
    public void handleKingCheckCanUseSkillAsk(KingCheckCanUseSkillAsk ask) {
        final ActorMsgEnvelope actorMsgEnvelope = sceneActor.getCurrentEnvelope();
        getBigScene().getKingdomComponent().checkCanUseSkill(ask.getKingdomSkillId(), ask.getToTargetId(), ask.getZoneId(), actorMsgEnvelope);
    }

    @Override
    public void handleKingUseSkillAsk(KingUseSkillAsk ask) {
        if (!ask.hasKingdomSkillId()) {
            LOGGER.warn("handleKingUseSkillAsk: invalid ask, ask={}", ask);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        getBigScene().getKingdomComponent().useSkill(ask.getKingdomSkillId(), ask.getToTargetId(), ask.getZoneId());
        sceneActor.answer(KingUseSkillAns.getDefaultInstance());
    }

    @Override
    public void handleFetchHistoryKingAsk(FetchHistoryKingAsk ask) {
        if (!ask.hasPage()) {
            LOGGER.warn("handleFetchHistoryKingAsk: invalid ask, ask={}", ask);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        getBigScene().getKingdomComponent().fetchHistoryKing(sceneActor.getCurrentEnvelope(), ask.getPage());
    }

    @Override
    public void handleFetchKingdomGiftAsk(FetchKingdomGiftAsk ask) {
        FetchKingdomGiftAns.Builder ret =
                getBigScene().getKingdomComponent().fetchGiftInfo(ask.getIsFetchingLeftNum(), ask.getGiftId(), ask.getCheckPlayerId());
        sceneActor.answer(ret.build());
    }

    @Override
    public void handleFetchKingdomOfficeAsk(FetchKingdomOfficeAsk ask) {
        FetchKingdomOfficeAns.Builder ret = getBigScene().getKingdomComponent().fetchOfficeInfo();
        sceneActor.answer(ret.build());
    }

}

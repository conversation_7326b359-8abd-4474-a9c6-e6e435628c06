package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.event.battle.AfterEndAllBattleEvent;
import com.yorha.cnc.scene.event.mapbuilding.MapBuildingStateChangeEvent;
import com.yorha.cnc.scene.event.rally.BeRallyDismissEvent;
import com.yorha.cnc.scene.event.rally.BeRallyEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.CloseStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.NeutralStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.SemiOpenStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.AfterRebuildStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.FireStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.RebuildStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.RecoverStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.*;
import com.yorha.cnc.scene.mapBuilding.enums.AbandonBuildingReason;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.OccupyInfoProp;
import com.yorha.proto.CommonEnum.MapBuildingType;
import com.yorha.proto.CommonEnum.OccupyState;
import com.yorha.proto.SsPlayerScene;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MapBuildingStageMgrComponent extends SceneObjComponent<MapBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(MapBuildingStageMgrComponent.class);
    private final Set<Long> beRallyClan = new HashSet<>();
    /**
     * 阶段切换定时器
     */
    private TimerReasonType stageTimer;

    private StageNode stageNode;

    public MapBuildingStageMgrComponent(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        // 被集结 事件监听
        getOwner().getEventDispatcher().addEventListenerRepeat((e) -> {
            beRallyClan.add(e.getClanId());
        }, BeRallyEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat((e) -> {
            beRallyClan.remove(e.getClanId());
        }, BeRallyDismissEvent.class);
    }

    public void initNode() {
        if (stageNode != null) {
            throw new GeminiException("init repeat");
        }
        stageNode = getNode(getOccupyInfoProp().getState(), getOwner());
    }

    /**
     * db捞出来的
     * 先走当前状态onLoad  如果有跳阶段 内部会走新阶段的onEnter  如果跳阶段 那会经过多个onEnter
     */
    @Override
    public void afterAllLoad() {
        // 如果onLoad里发生了状态切换 那会通过setNewNode去更新的
        stageNode.onLoad();
    }

    /**
     * new出来的
     */
    public void onCreate() {
        stageNode = getNode(getOccupyInfoProp().getState(), getOwner());
        stageNode.onEnter(0);
    }

    /**
     * 开放建筑
     */
    public void onOpen(long endTsMs) {
        // 黑暗祭坛关卡不受这个控制
        if (getOwner().getTransformComponent().isDarkAltarCross()) {
            return;
        }
        if (getState() != OccupyState.TOS_CLOSE && getState() != OccupyState.TOS_SEMI_OPEN) {
            return;
        }
        cancelStageTimer();
        getOccupyInfoProp().setStateEndTsMs(endTsMs);
        // 重入状态
        stageNode.onEnter(0);
    }

    /**
     * 半开放建筑
     */
    public void onSemiOpen(long endTsMs) {
        if (getOwner().getTransformComponent().isDarkAltarCross()) {
            return;
        }
        if (getState() != OccupyState.TOS_CLOSE) {
            return;
        }
        cancelStageTimer();
        getOccupyInfoProp().setStateEndTsMs(endTsMs);
        // 重入关闭状态
        stageNode.onEnter(0);
    }

    /**
     * 直接将建筑设置为军团拥有的状态，仅用于白嫖首次占领军团基地的时候的逻辑
     */
    public void directSetOwner(SceneClanEntity sceneClan) {
        LOGGER.info("direct set Owner for map building {}", getOwner());
        if (getState() != OccupyState.TOS_NEUTRAL) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_CANNOT_REBUILD_DURING_OCCUPY);
        }
        directOwner(sceneClan);
    }

    /**
     * 拆掉建筑  可能正在改建 也可能是改建好了等等
     * 主动拆除 / 改建中被打了
     */
    public void destroyClanBuilding(boolean isActive) {
        LOGGER.info("{} {} stopRebuild. isActive:{}", getOwner(), stageNode, isActive);
        ((ClanBuildingNode) stageNode).onDestroyBuilding(isActive, true);
    }

    /**
     * 被攻击成功了
     */
    public void onAttackSuccess(long clanId) {
        if (!getOwner().isClanBuilding()) {
            return;
        }
        LOGGER.info("{} {} onStartFire. clanId:{}", getOwner(), stageNode, clanId);
        ((ClanBuildingNode) stageNode).onAttackSuccess(clanId);
    }

    /**
     * 被联盟成员主动放弃
     */
    public void ownerAbandon(long clanId) {
        if (clanId != getOwner().getOwnerClanId()) {
            throw new GeminiException(ErrorCode.MAP_ERROR_TERRITORY);
        }
        // 地图建筑被改建不允许联盟成员放弃据点
        if (getOwner().getBuildComponent().getType() != MapBuildingType.MBT_NONE) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_DURING_REBUILD_TRY_ABANDON);
        }
        if (stageNode instanceof ClanBuildingNode) {
            throw new GeminiException(ErrorCode.CLAN_BUILDING_DURING_REBUILD_TRY_ABANDON);
        }
        // 回归中立 这里没有清数据 因为在中立状态进入时需要通知clan的
        transNeutralStage();
    }

    /**
     * 拥有者要删   联盟解散
     */
    public void ownerAbandon(AbandonBuildingReason reason) {
        LOGGER.info("{} ownerAbandon reason:{}", getOwner(), reason);
        if (getOccupyInfoProp().getOccupyClanId() != 0) {
            // 有占领者  就清理下归属数据就行了
            getOccupyInfoProp().setOwnerClanId(0);
            return;
        }
        // 不用执行一些后处理  直接清下数据切阶段
        clearAndTransNeutralStage();
    }

    /**
     * 占领者要删    不满足连地条件/初始地多个/宣战结束还在占领中
     */
    public void occupierAbandon(AbandonBuildingReason reason) {
        LOGGER.info("{} occupierAbandon reason:{}", getOwner(), reason);
        // 没有所属者
        if (getOccupyInfoProp().getOwnerClanId() == 0) {
            // 不用执行一些后处理  直接清下数据切阶段
            clearAndTransNeutralStage();
            return;
        }
        SceneClanEntity ownerSceneClan = stageNode.getOwnerSceneClan();
        if (ownerSceneClan.getMapBuildingComponent().isCanRetake(getOwner())) {
            // 有归属  但是需要重新占领的  进入中立阶段
            if (getOwner().getOccupyComponent().checkAndSetOwnerRetake()) {
                // 不用执行一些后处理  直接清下数据切阶段
                clearAndTransNeutralStage();
            } else {
                getOwner().getInnerArmyComponent().returnAllArmy();
            }
            return;
        }
        // 不用执行一些后处理  直接清下数据切阶段
        clearAndTransNeutralStage();
    }

    /**
     * gm直接拥有
     */
    public void gmSetOwner(SceneClanEntity sceneClan, @Nullable Long playerId, int zoneId) {
        LOGGER.info("clanId {} ,{} gmSetOwner", sceneClan.getEntityId(), getOwner());
        if (getState() != OccupyState.TOS_NEUTRAL) {
            throw new GeminiException("state need is neutral");
        }
        directOwner(sceneClan);
        // 假装触发下 让附属建筑该状态，。，，，
        getOwner().getEventDispatcher().dispatch(new AfterEndAllBattleEvent());
        if (playerId == null) {
            return;
        }
        SsPlayerScene.OnOccupySuccessCmd cmd = SsPlayerScene.OnOccupySuccessCmd.newBuilder()
                .setPartId(getOwner().getProp().getPartId())
                .setTemplateId(getOwner().getProp().getTemplateId())
                .setAreaType(getOwner().getAreaType())
                .build();
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        scenePlayer.tellPlayer(cmd);
    }

    private void directOwner(SceneClanEntity sceneClan) {
        getOccupyInfoProp().setOccupyClanId(sceneClan.getEntityId());
        getOwner().copySceneClanAttr(sceneClan);
        OccupyingStageNode node = new OccupyingStageNode(getOwner());
        node.checkAndSendFirstOccupy(SystemClock.now(), sceneClan);
        node.onOccupyFinished(SystemClock.now(), true);
        getOwner().getDbComponent().tryInsertDb();
    }

    /**
     * gm占领完成
     */
    public void gmCompleteOccupy() {
        LOGGER.info("{} gmCompleteOccupy", getOwner());
        if (stageNode.getStage() != OccupyState.TOS_OCCUPYING) {
            throw new GeminiException("state need is occupying");
        }
        cancelStageTimer();
        ((OccupyingStageNode) stageNode).onOccupyFinished(SystemClock.now(), true);
    }

    /**
     * gm 改建完成
     */
    public void gmCompleteBuild() {
        if (stageNode.getStage() != OccupyState.TOS_REBUILD) {
            return;
        }
        cancelStageTimer();
        ((RebuildStageNode) stageNode).onRebuildFinished(SystemClock.now());
    }

    /**
     * gm 回归中立
     */
    public void gmReset() {
        LOGGER.info("{} gmReset", getOwner());
        cancelStageTimer();
        getOccupyInfoProp().setOccupyTsMs(0).setFisrtOwnTsMs(0);
        if (getOwner().isClanBuilding()) {
            // 走主动拆除流程吧 都gm了
            destroyClanBuilding(true);
        }
        transNeutralStage();
    }

    /**
     * gm 直接关闭
     */
    public void gmSetClose() {
        gmReset();
        getOccupyInfoProp().setStateEndTsMs(SystemClock.now() + 300000);
        transNewNode(new CloseStageNode(getOwner()));
    }

    public void transNewNode(StageNode newNode) {
        transNewNode(newNode, SystemClock.now());
    }

    public void transNewNode(StageNode newNode, long enterTsMs) {
        cancelStageTimer();
        stageNode.onLeave();
        stageNode = newNode;
        newNode.onEnter(enterTsMs);
        getOwner().getEventDispatcher().dispatch(new MapBuildingStateChangeEvent());
    }

    /**
     * 事先清数据版回归中立  == 不通知联盟，不做后处理 只有解散能进
     */
    public void clearAndTransNeutralStage() {
        if (getOwner().isClanBuilding()) {
            clearRebuildProp();
        }
        clearShowClan();
        transNewNode(new NeutralStageNode(getOwner()));
    }

    /**
     * 不事先清数据版回归中立  会通知联盟， 做后处理
     */
    public void transNeutralStage() {
        transNewNode(new NeutralStageNode(getOwner()));
    }

    public void setStageTimer(TimerReasonType stageTimer) {
        this.stageTimer = stageTimer;
    }

    /**
     * timer回调中设置为null
     */
    public void clearStageTimer() {
        stageTimer = null;
    }

    /**
     * 取消阶段转换定时器
     */
    public void cancelStageTimer() {
        if (stageTimer == null) {
            return;
        }
        getOwner().getTimerComponent().cancelTimer(stageTimer);
        stageTimer = null;
    }

    public StageNode getStageNode() {
        return stageNode;
    }

    public OccupyState getState() {
        return getOccupyInfoProp().getState();
    }

    public OccupyInfoProp getOccupyInfoProp() {
        return getOwner().getProp().getOccupyinfo();
    }

    public void clearShowClan() {
        getOccupyInfoProp()
                .setZoneId(0)
                .setOwnerClanId(0).setOccupyClanId(0)
                .setShowClanName("")
                .setShowClanSimpleName("")
                .setShowColor(0)
                .setFlagColor(0)
                .setFlagSign(0)
                .setFlagShading(0);
    }

    public void clearRebuildProp() {
        getOwner().getProp().setTemplateId(getOwner().getProp().getConstructInfo().getBeforeRebuildTemplateId());
        getOwner().getProp().getConstructInfo().setBeforeRebuildTemplateId(0).setCurrentDurability(0).setMaxDurability(0)
                .setIsConnectedToCommandNet(false).setType(MapBuildingType.MBT_NONE).setIsOnFire(false)
                .setAffectedByWhichMainBase(0).setNoMainBase(0);
    }

    /**
     * 是否被其他联盟集结
     */
    public boolean isBeRallyExceptClan(long clanId) {
        if (beRallyClan.size() == 0) {
            return false;
        }
        return beRallyClan.size() > 1 || !beRallyClan.contains(clanId);
    }

    /**
     * 黑暗祭坛驱动进入保护期间
     */
    public void darkAltarEnterProtect(long endTsMs) {
        if (getOwner().getTransformComponent().getDarkAltarBindRegion() == null) {
            LOGGER.error("darkAltarEnterProtect but not is dark altar cross {}", getOwner());
            return;
        }
        transNewNode(new ProtectStageNode(getOwner()), SystemClock.now());
        getOccupyInfoProp().setStateEndTsMs(endTsMs);
    }

    /**
     * 黑暗祭坛驱动进入开放
     */
    public void darkAltarEnterOpen(long endTsMs) {
        if (getOwner().getTransformComponent().getDarkAltarBindRegion() == null) {
            LOGGER.error("darkAltarEnterOpen but not is dark altar cross {}", getOwner());
            return;
        }
        transNeutralStage();
        getOccupyInfoProp().setStateEndTsMs(endTsMs);
    }

    public static StageNode getNode(OccupyState stage, MapBuildingEntity owner) {
        switch (stage) {
            case TOS_CLOSE:
                return new CloseStageNode(owner);
            case TOS_NEUTRAL:
                return new NeutralStageNode(owner);
            case TOS_OCCUPYING:
                return new OccupyingStageNode(owner);
            case TOS_STICK:
                return new StickStageNode(owner);
            case TOS_PROTECT:
                return new ProtectStageNode(owner);
            case TOS_DESERTED:
                return new DesertedStageNode(owner);
            case TOS_CITY_IN_COMMAND_NET:
                return new InCommandNetStageNode(owner);
            case TOS_REBUILD:
                return new RebuildStageNode(owner);
            case TOS_AFTER_REBUILD:
                return new AfterRebuildStageNode(owner);
            case TOS_FIRE:
                return new FireStageNode(owner);
            case TOS_AFTER_FIRE_RECOVER:
                return new RecoverStageNode(owner);
            case TOS_SEMI_OPEN:
                return new SemiOpenStageNode(owner);
            default:
                throw new GeminiException("node is not exist stage {}", stage);
        }
    }

    public void updateKingCardHead(StructPB.PlayerCardHeadPB cardHead) {
        LOGGER.info("MapBuildingStageMgrComponent updateKingCardHead cardHead={}", cardHead);
        getOwner().getProp().getKingdomModel().getKingCardHead().mergeFromCs(cardHead);
    }

    /**
     * 清理王国数据
     */
    public void clearKingdomData() {
        getOwner().getProp().getKingdomModel().getKingCardHead().setPic(0).setPicFrame(0).setName("").setPicUrl("");
    }

    /**
     * 是否是完成状态
     * 改建期、改建后拥有状态、改建后拥有状态、着火后恢复状态
     */
    public boolean isFinishStatus() {
        OccupyState state = getOccupyInfoProp().getState();
        if (state == OccupyState.TOS_FIRE) {
            return true;
        }
        if (state == OccupyState.TOS_AFTER_REBUILD) {
            return true;
        }
        return state == OccupyState.TOS_AFTER_FIRE_RECOVER;
    }
}

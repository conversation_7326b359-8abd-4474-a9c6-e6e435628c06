package com.yorha.cnc.scene.common;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.notification.NotificationBuilder;
import com.yorha.proto.SsClanBase;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
public class ScenePushNotificationHelper {
    private static final Logger LOGGER = LogManager.getLogger(ScenePushNotificationHelper.class);

    private static @Nullable
    ScenePlayerEntity getScenePlayerOrNull(SceneEntity sceneEntity, final long playerId) {
        if (sceneEntity.isBigScene()) {
            return sceneEntity.getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        }
        return null;
    }

    public static void pushBaseBeAttackNotification(SceneEntity sceneEntity, CityEntity cityEntity) {
        LOGGER.debug("send base be attack notification, cityEntity={}", cityEntity);
        if (!sceneEntity.isMainScene()) {
            LOGGER.debug("PushNotificationHelper pushBaseBeAttackNotification, cur scene is not mainScene, skip");
            return;
        }
        final long targetPlayerId = cityEntity.getPlayerId();
        ScenePlayerEntity scenePlayerEntity = getScenePlayerOrNull(sceneEntity, targetPlayerId);
        if (scenePlayerEntity == null) {
            LOGGER.error("PushNotificationHelper pushBaseBeAttackNotification city={}, playerId={}, scenePlayerEntity null", cityEntity.getEntityId(), targetPlayerId);
            return;
        }
        scenePlayerEntity.getNtfComponent().pushOfflinePlayerSingleNotification(NotificationBuilder.BASE_BE_ATTACK, targetPlayerId);
    }

    public static void pushBaseBeRallyNotification(SceneEntity scene, CityEntity cityEntity) {
        LOGGER.debug("send base be rally notification, cityEntity={}", cityEntity);
        if (!scene.isMainScene()) {
            LOGGER.debug("PushNotificationHelper pushBaseBeRallyNotification, cur scene is not mainScene, skip");
            return;
        }
        ScenePlayerEntity scenePlayer = getScenePlayerOrNull(scene, cityEntity.getPlayerId());
        if (scenePlayer == null) {
            LOGGER.error("PushNotificationHelper pushBaseBeRallyNotification city={}, playerId={}, scenePlayerEntity null", cityEntity.getEntityId(), cityEntity.getPlayerId());
            return;
        }
        scenePlayer.getNtfComponent().pushOfflinePlayerSingleNotification(NotificationBuilder.BASE_BE_RALLY, scenePlayer.getPlayerId());
    }

    public static void pushBaseBeSpyNotification(SceneEntity sceneEntity, CityEntity cityEntity) {
        LOGGER.debug("send base be spy notification, cityEntity={}", cityEntity);
        if (!sceneEntity.isMainScene()) {
            LOGGER.debug("PushNotificationHelper pushBaseBeSpyNotification, cur scene is not mainScene, skip");
            return;
        }
        final long targetPlayerId = cityEntity.getPlayerId();
        ScenePlayerEntity scenePlayer = getScenePlayerOrNull(sceneEntity, targetPlayerId);
        if (scenePlayer == null) {
            LOGGER.error("PushNotificationHelper pushBaseBeSpyNotification city={}, playerId={}, scenePlayerEntity null", cityEntity.getEntityId(), cityEntity.getPlayerId());
            return;
        }
        scenePlayer.getNtfComponent().pushOfflinePlayerSingleNotification(NotificationBuilder.BASE_BE_DETECT, targetPlayerId);
    }

    public static void pushClanRallyPlayerNotification(SceneEntity sceneEntity, final AbstractScenePlayerEntity rallyLeader) {
        LOGGER.debug("send clan rally player notification, rallyLeader={}", rallyLeader);
        if (!sceneEntity.isMainScene()) {
            LOGGER.debug("PushNotificationHelper pushClanRallyPlayerNotification, cur scene is not mainScene, skip");
            return;
        }
        if (!rallyLeader.isInClan()) {
            LOGGER.debug("pushClanRallyPlayerNotification not in clan");
            return;
        }
        ActorSendMsgUtils.send(RefFactory.ofClan(rallyLeader.getZoneId(), rallyLeader.getClanId()), SsClanBase.PushNotifyCmd.newBuilder().setNotifyId(NotificationBuilder.RALLY_PLAYER.getNotificationId()).build());
    }

    public static void pushClanRallyMonsterNotification(SceneEntity sceneEntity, final AbstractScenePlayerEntity rallyLeader) {
        LOGGER.debug("send clan rally monster notification, rallyLeader={}", rallyLeader);
        if (!sceneEntity.isMainScene()) {
            LOGGER.debug("PushNotificationHelper pushClanRallyMonsterNotification, cur scene is not mainScene, skip");
            return;
        }
        if (!rallyLeader.isInClan()) {
            LOGGER.debug("pushClanRallyMonsterNotification not in clan");
            return;
        }
        ActorSendMsgUtils.send(RefFactory.ofClan(rallyLeader.getZoneId(), rallyLeader.getClanId()), SsClanBase.PushNotifyCmd.newBuilder().setNotifyId(NotificationBuilder.RALLY_MONSTER.getNotificationId()).build());
    }

    public static void pushClanMapBuildingBeRallied(SceneEntity sceneEntity, MapBuildingEntity mapBuilding) {
        LOGGER.info("send clanBuilding be rally notification, mapBuilding={}, type={}", mapBuilding, mapBuilding.getBuildingTemplate().getType());
        if (!sceneEntity.isMainScene()) {
            LOGGER.debug("PushNotificationHelper pushClanMapBuildingBeRallied, cur scene is not mainScene, skip");
            return;
        }
        if (mapBuilding.getClanId() == 0) {
            LOGGER.debug("pushClanMapBuildingBeRallied not in clan");
            return;
        }
        IActorRef clanRef = RefFactory.ofClan(mapBuilding.getZoneId(), mapBuilding.getClanId());
        switch (mapBuilding.getBuildingTemplate().getType()) {
            case MBT_MAIN_BASE:
            case MBT_COMMAND_CENTER:
            case MBT_CLAN_FORTRESS:
                ActorSendMsgUtils.send(clanRef, SsClanBase.PushNotifyCmd.newBuilder().setNotifyId(NotificationBuilder.CLAN_BUILDING_BE_RALLIED.getNotificationId()).build());
                return;
            case MBT_CITY_ONT:
            case MBT_CITY_TWO:
            case MBT_CITY_THREE:
            case MBT_CITY_FOUR:
            case MBT_ROYAL_CITY:
                ActorSendMsgUtils.send(clanRef, SsClanBase.PushNotifyCmd.newBuilder().setNotifyId(NotificationBuilder.CLAN_CITY_BE_RALLIED.getNotificationId()).build());
                return;
            case MBT_PASS:
                ActorSendMsgUtils.send(clanRef, SsClanBase.PushNotifyCmd.newBuilder().setNotifyId(NotificationBuilder.CLAN_PASS_BE_RALLIED.getNotificationId()).build());
                return;
            default:
                // 无需推送
        }
    }
}

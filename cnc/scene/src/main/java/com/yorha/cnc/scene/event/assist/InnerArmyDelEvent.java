package com.yorha.cnc.scene.event.assist;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * 城内已到达部队移除   (莫随意增减触发点)
 *
 * <AUTHOR>
 */
public class InnerArmyDelEvent extends IEvent {
    private final ArmyEntity armyEntity;

    public InnerArmyDelEvent(ArmyEntity armyEntity) {
        this.armyEntity = armyEntity;
    }

    public ArmyEntity getArmyEntity() {
        return armyEntity;
    }
}

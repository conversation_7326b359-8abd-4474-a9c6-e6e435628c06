package com.yorha.cnc.scene.event.dungeon;

import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class LeaveAreaEvent extends IEvent {
    private final int areaId;
    private final int groupId;
    public LeaveAreaEvent(int areaId) {
        this.areaId = areaId;
        this.groupId = -1;
    }

    public LeaveAreaEvent(int areaId, int groupId) {
        this.areaId = areaId;
        this.groupId = groupId;
    }
    public int getAreaId() { return this.areaId; }

    public int getGroupId() { return this.groupId; }
}

package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingFactory;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanFactory;
import com.yorha.common.actor.SceneClanMgrService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.OccupyState;
import com.yorha.proto.SsSceneClan.*;
import com.yorha.proto.StructCommon;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 场景联盟相关
 *
 * <AUTHOR>
 */
public class SceneClanServiceImpl implements SceneClanMgrService {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanServiceImpl.class);
    private final SceneActor sceneActor;

    public SceneClanServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    private SceneClanEntity getSceneClan(long clanId) {
        SceneClanEntity sceneClan = getScene().getClanMgrComponent().getSceneClan(clanId);
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.CLAN_NOT_EXIST);
        }
        return sceneClan;
    }

    private SceneClanEntity getSceneClanOrNull(long clanId) {
        return getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
    }

    private MapBuildingEntity getMapBuilding(long id) {
        return getScene().getObjMgrComponent().getSceneObjWithTypeWithException(MapBuildingEntity.class, id);
    }

    @Override
    public void handleFetchTerritoryPageAsk(FetchTerritoryPageAsk ask) {
        LOGGER.info("start handle Fetch Territory Page");
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        FetchTerritoryPageAns.Builder builder = FetchTerritoryPageAns.newBuilder();
        builder.setPage(sceneClan.getMapBuildingComponent().getClanTerritoryPage());
        sceneActor.answer(builder.build());
    }

    @Override
    public void handleAbandonClanMapBuildingAsk(AbandonClanMapBuildingAsk ask) {
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        mapBuilding.getStageMgrComponent().ownerAbandon(ask.getClanId());
        mapBuilding.getQLogComponent().sendExpansionLog(ask.getClanId(), "abandon_guild_building", ask.getPlayerId());
        sceneActor.answer(AbandonClanMapBuildingAns.getDefaultInstance());
    }

    @Override
    public void handleOnClanCreatedCmd(OnClanCreatedCmd ask) {
        LOGGER.info("onSyncClanCreated: {}", ask.getClanId());
        SceneClanFactory.createSceneClan(getScene(), ask.getClanId(), ask.getSceneClan(), sceneActor.getCurrentEnvelope().getSender().getZoneId());
    }

    @Override
    public void handleOnClanDissolutionCmd(OnClanDissolutionCmd ask) {
        LOGGER.info("onSyncClanDissolution: {}", ask.getClanId());
        SceneClanEntity sceneClan = getScene().getClanMgrComponent().getSceneClan(ask.getClanId());
        if (sceneClan == null) {
            LOGGER.error("onSyncClanDissolution not find {}", ask.getClanId());
            return;
        }
        sceneClan.deleteObj();
    }

    @Override
    public void handleSyncSceneClanCmd(SyncSceneClanCmd ask) {
        SceneClanEntity sceneClan = getSceneClanOrNull(ask.getClanId());
        if (sceneClan == null) {
            return;
        }
        //坐标标记
        if (ask.hasHasPositionMarkChange()) {
            sceneClan.getMemberComponent().onPositionMarkChange();
            return;
        }
        sceneClan.onSyncSceneClan(ask);
        LOGGER.info("SyncSceneClanCmd {}", ask.getSceneClan());
    }

    @Override
    public void handleSyncClanActorStatusCmd(SyncClanActorStatusCmd ask) {
        if (!ask.hasClanId() || !ask.hasIsActorDestroy()) {
            LOGGER.error("handleSyncClanEntityStatusCmd error: {}", ask);
            return;
        }
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        if (ask.getIsActorDestroy()) {
            sceneClan.getQlogComponent().markClanUnActive();
        } else {
            sceneClan.getQlogComponent().markClanActive();
        }
    }

    @Override
    public void handleConstructClanBuildingAsk(ConstructClanBuildingAsk ask) {
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        // 白嫖军团建筑的情况作为额外逻辑需要先做，直接设置军团为建筑owner，并设置白嫖标志位
        if (ask.getBuildingType() == CommonEnum.MapBuildingType.MBT_MAIN_BASE && mapBuilding.getOwnerClanId() == 0) {
            mapBuilding.getStageMgrComponent().directSetOwner(sceneClan);
        }
        AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());
        // 建设前置条件检测
        mapBuilding.getBuildComponent().startRebuildPreCheck(ask.getPlayerId(), ask.getBuildingType(), sceneClan);
        // 执行建设逻辑
        mapBuilding.getBuildComponent().startRebuild(ask.getBuildingType(), ask.getStaffId(), scenePlayer.getName());
        // 释放建设锁
        mapBuilding.getBuildComponent().removeOperateLock();
        // 打个qlog
        mapBuilding.getQLogComponent().sendExpansionLog(ask.getClanId(), "start_build_guildbuilding", ask.getPlayerId());
        ConstructClanBuildingAns.Builder builder = ConstructClanBuildingAns.newBuilder();
        sceneActor.answer(builder.build());
    }

    @Override
    public void handleDestroyClanBuildingAsk(DestroyClanBuildingAsk ask) {
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        if (!mapBuilding.isClanBuilding()) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 发送主动拆除建筑的日志
        getSceneClan(ask.getClanId()).getLogComponent().recordDestroyRebuildingLog(ask.getPlayerName(), mapBuilding.getProp().getTemplateId(), mapBuilding.getCurPoint());
        mapBuilding.getStageMgrComponent().destroyClanBuilding(true);
        sceneActor.answer(DestroyClanBuildingAns.getDefaultInstance());
    }

    @Override
    public void handleExtinguishBuildingFireAsk(ExtinguishBuildingFireAsk ask) {
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        // 执行灭火逻辑
        mapBuilding.getBuildComponent().tryStopFire(ask.getClanId());
        // 释放灭火锁
        mapBuilding.getBuildComponent().removeOperateLock();
        sceneActor.answer(ExtinguishBuildingFireAns.getDefaultInstance());
    }

    @Override
    public void handleFetchClanBuildingHpAsk(FetchClanBuildingHpAsk ask) {
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        FetchClanBuildingHpAns.Builder builder = FetchClanBuildingHpAns.newBuilder();
        OccupyState state = mapBuilding.getProp().getOccupyinfo().getState();
        if (state == OccupyState.TOS_AFTER_FIRE_RECOVER || state == OccupyState.TOS_FIRE) {
            mapBuilding.getBuildComponent().refreshDurability();
            int curHp = mapBuilding.getProp().getConstructInfo().getCurrentDurability();
            int maxHp = mapBuilding.getProp().getConstructInfo().getMaxDurability();
            if (curHp > maxHp) {
                LOGGER.error("curHp {} is bigger than maxHp {}", curHp, maxHp);
                curHp = maxHp;
            }
            builder.setCurHp(curHp);
        } else {
            builder.setCurHp(mapBuilding.getProp().getConstructInfo().getMaxDurability());
        }
        builder.setMaxHp(mapBuilding.getProp().getConstructInfo().getMaxDurability());
        sceneActor.answer(builder.build());
    }

    @Override
    public void handleCheckCanFreeRebuildAsk(CheckCanFreeRebuildAsk ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        CheckCanFreeRebuildAns.Builder ans = CheckCanFreeRebuildAns.newBuilder();
        int buildingNum = sceneClan.getBuildComponent().getBuildingNum(CommonEnum.MapBuildingType.MBT_MAIN_BASE);
        ans.setMainBaseNum(buildingNum).setCanFreeRebuild(buildingNum == 0);
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleVerifyCanRebuildAsk(VerifyCanRebuildAsk ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        VerifyCanRebuildAns.Builder ans = VerifyCanRebuildAns.newBuilder();
        // 建设前置条件检测
        mapBuilding.getBuildComponent().startRebuildPreCheck(ask.getPlayerId(), ask.getType(), sceneClan);
        // 来自改建的前置检查，需要检查是否已经上锁
        if (ask.getIsConstructCheck()) {
            if (!mapBuilding.getBuildComponent().tryAddOperateLock()) {
                LOGGER.info("get build lock failed, try build {}", ask.getMapBuildingId());
                ans.setCanRebuild(false);
                sceneActor.answer(ans.build());
                return;
            }
        }
        ans.setCanRebuild(true).setStoryId(getScene().getStoryId());
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleVerifyCanExtinguishAsk(VerifyCanExtinguishAsk ask) {
        VerifyCanExtinguishAns.Builder ans = VerifyCanExtinguishAns.newBuilder();
        MapBuildingEntity mapBuilding = getMapBuilding(ask.getMapBuildingId());
        // 先排除其他可能的GeminiException
        mapBuilding.getBuildComponent().stopFirePreCheck(ask.getClanId());
        // 再排除建筑操作锁无法获得的情况
        boolean hasGotLock = mapBuilding.getBuildComponent().tryAddOperateLock();
        if (!hasGotLock) {
            LOGGER.info("get operator lock failed, try extinguish {}", ask.getMapBuildingId());
            ans.setCanExtinguish(false);
        } else {
            ans.setCanExtinguish(true).setBuildingTemplateId(mapBuilding.getProp().getTemplateId());
        }
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleSyncClanAdditionCmd(SyncClanAdditionCmd ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        sceneClan.getAddComponent().updateAdditionFromClan(ask.getSource(), ask.getAdditionMap());
    }

    @Override
    public void handleAskForClanRecommendAsk(AskForClanRecommendAsk ask) {
        long recommendClanId = 0;
        if (getScene().isBigScene()) {
            recommendClanId = getScene().getClanMgrComponent().recommendClan(ask.getPlayerId(), ask.getBetterNotRecomClanIdsList());
        }
        AskForClanRecommendAns.Builder ans = AskForClanRecommendAns.newBuilder();
        sceneActor.answer(ans.setRecommendClanId(recommendClanId).build());
    }

    @Override
    public void handleFetchDefaultClanListAsk(FetchDefaultClanListAsk ask) {
        sceneActor.answer(getScene().getClanMgrComponent().fetchDefaultClanList(ask.getPlayerId()));
    }

    @Override
    public void handleVerifyCanPlaceClanResAsk(VerifyCanPlaceClanResAsk ask) {
        LOGGER.info("handleVerifyCanPlaceClanResAsk ask: {}", ask);
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        // 检查是否已经军团资源中心存在
        if (sceneClan.getBuildComponent().hasClanResBuilding()) {
            throw new GeminiException(ErrorCode.CLAN_RES_BUILDING_ALREADY_EXISTS);
        }
        // 静、动态阻挡检查，位置是否属于军团检查
        sceneClan.getBuildComponent().checkCanPlaceClanResBuilding(ask.getType(), ask.getP(), ask.getClanId());
        // 尝试上锁，防止重入
        sceneClan.getBuildComponent().tryAddClanResBuildLock();
        // 回包
        sceneActor.answer(VerifyCanPlaceClanResAns.newBuilder().build());
    }

    @Override
    public void handlePlaceClanResBuildInSceneAsk(PlaceClanResBuildInSceneAsk ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        // 创建ClanResBuilding，并加入场景
        ClanResBuildingEntity clanResBuilding = ClanResBuildingFactory.createClanResBuilding(getScene(), ask.getType(), ask.getP(), ask.getEntityId());
        // 把ClanResBuilding挂到SceneClan上
        sceneClan.getBuildComponent().addClanResBuilding(clanResBuilding);
        // 解锁
        sceneClan.getBuildComponent().removeClanResBuildLock();
        // 回包
        sceneActor.answer(PlaceClanResBuildInSceneAns.newBuilder().build());
    }

    @Override
    public void handleFetchClanResBuildSimpleInfoAsk(FetchClanResBuildSimpleInfoAsk ask) {
        ClanResBuildingEntity entity = getScene().getObjMgrComponent().getSceneObjWithType(ClanResBuildingEntity.class, ask.getTargetId());
        FetchClanResBuildSimpleInfoAns.Builder ret = FetchClanResBuildSimpleInfoAns.newBuilder();
        if (entity == null) {
            sceneActor.answer(ret.build());
            return;
        }
        ret.setDisappearTsMs(entity.getDisappearTsMs());
        StructCommon.ProgressInfo.Builder builder = entity.getProgressInfoByPlayerId(ask.getPlayerId());
        if (builder != null) {
            ret.setProgressInfo(builder);
        }
        sceneActor.answer(ret.build());
    }

    @Override
    public void handleCheckPlayerCityInTerritoryAsk(CheckPlayerCityInTerritoryAsk ask) {
        CheckPlayerCityInTerritoryAns.Builder ans = CheckPlayerCityInTerritoryAns.newBuilder().setIsInTerritory(false);

        // 便于提前退出，少写return
        while (true) {
            long playerId = ask.getPlayerId();
            // playerId无效会抛出异常
            AbstractScenePlayerEntity scenePlayer = getScene().getPlayerMgrComponent().getScenePlayer(ask.getPlayerId());

            long clanId = scenePlayer.getClanId();
            // 防御：没有联盟
            if (clanId == 0) {
                break;
            }
            // clanId无效会抛出异常
            SceneClanEntity sceneClan = getSceneClan(clanId);
            // 防御：不在联盟内
            if (!sceneClan.getMemberComponent().isInClan(playerId)) {
                break;
            }
            Point playerCityPoint = scenePlayer.getMainCity().getCurPoint();

            // 判断主城是否在领土上
            boolean isInTerritory = sceneClan.getMapBuildingComponent().isMyTerritory(playerCityPoint.getX(), playerCityPoint.getY());
            ans.setIsInTerritory(isInTerritory);
            break;
        }

        sceneActor.answer(ans.build());
    }

    @Override
    public void handleAddDevBuffFromClanCmd(AddDevBuffFromClanCmd ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        sceneClan.getDevBuffComponent().addDevBuffFromClan(ask.getParam());
    }

    @Override
    public void handleRemoveDevBuffFromClanCmd(RemoveDevBuffFromClanCmd ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        sceneClan.getDevBuffComponent().removeDevBuffFromClan(ask.getParam());
    }

    @Override
    public void handleUpdateAdditionFromClanCmd(UpdateAdditionFromClanCmd ask) {
        SceneClanEntity sceneClan = getSceneClan(ask.getClanId());
        sceneClan.getAddComponent().updateScenePlayerAdditionFromClan(ask.getSource(), ask.getAdditionsMap());
    }

    @Override
    public void handleGetClanCommandCenterNumAsk(GetClanCommandCenterNumAsk ask) {
        GetClanCommandCenterNumAns.Builder ans = GetClanCommandCenterNumAns.newBuilder();
        for (long clanId : ask.getClanIdsList()) {
            SceneClanEntity sceneClan = getSceneClan(clanId);
            int num = sceneClan.getBuildComponent().getCurCommandCenterNum();
            ans.putCenterNums(clanId, num);
        }
        sceneActor.answer(ans.build());
    }
}

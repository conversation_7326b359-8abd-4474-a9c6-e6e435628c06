package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerTimerComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.plane.PlaneService;
import com.yorha.game.gen.prop.ScenePlayerLogisticsModelProp;
import com.yorha.game.gen.prop.ScenePlayerLogisticsStatusProp;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConpensationBaselevelTemplate;
import res.template.ConstConpensationTemplate;
import res.template.ItemTemplate;
import res.template.SoldierTypeTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.concurrent.TimeUnit;

import static com.yorha.proto.CommonEnum.BattleType.BT_CITY_ARMY_2_ARMY;
import static com.yorha.proto.CommonEnum.BattleType.BT_WILD_ARMY_2_ARMY;
import static com.yorha.proto.CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF;

/**
 * <AUTHOR>
 */
public class ScenePlayerLogisticMgrComponent extends AbstractComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerLogisticMgrComponent.class);
    // 战损记录
    private PriorityQueue<Long> battleLoseRecord = new PriorityQueue<>();
    // 周补偿记录
    private PriorityQueue<Long> makeUpRecord = new PriorityQueue<>();
    // 战损量记录
    private int battleLose = 0;
    // 记录战损各兵种详细信息 soldierId -> 重伤_死亡
    private final Map<Integer, Pair<Integer, Integer>> battleLoseMap = new HashMap<>();
    // 记录战损各兵种详细信息 soldierLevel -> 重伤_死亡
    private final Map<Integer, Integer> battleLoseByWeekMap = new HashMap<>();

    public ScenePlayerLogisticMgrComponent(ScenePlayerEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        super.postInit();
        final AbstractScenePlayerTimerComponent timerComponent = getOwner().getTimerComponent();
        final TimerReasonType timerReasonType = TimerReasonType.BATTLE_LOSE;
        final long initDelay = TimeUtils.getNextWeekDurMs(SystemClock.now());
        final long period = TimeUnit.DAYS.toMillis(7);
        timerComponent.cancelTimer(timerReasonType);
        timerComponent.addFixRepeatTimer(getEntityId(), timerReasonType, battleLoseByWeekMap::clear, initDelay, period, TimeUnit.MILLISECONDS);
    }

    public ScenePlayerLogisticsModelProp getLogisticsProp() {
        return getOwner().getProp().getLogisticsModel();
    }

    public int getBattleLose() {
        return battleLose;
    }

    public void setLogisticsPlaneStatus(ScenePlayerLogisticsStatusProp status) {
        getLogisticsProp().putLogisticsPlanesV(status);
    }

    public ScenePlayerLogisticsStatusProp getLogisticsPlaneStatus(long planeId) {
        return getLogisticsProp().getLogisticsPlanesV(planeId);
    }

    public void clearLogisticPlaneStatus(long planeId) {
        getLogisticsProp().removeLogisticsPlanesV(planeId);
    }

    private boolean checkSysAssitScene(BattleRecord.RoleRecord roleRecord, CommonEnum.BattleType type) {
        // 守城方
        if ((roleRecord.getRoleType() == SOT_CITY_ARMY_SELF) && (type == BT_CITY_ARMY_2_ARMY)) {
            return true;
        }
        // pvp 野战
        if (type == BT_WILD_ARMY_2_ARMY) {
            return true;
        }
        return false;
    }

    /**
     * 尝试系统援助
     */
    public void trySysAssist(BattleRecord.RoleRecord roleRecord, SceneObjBattleComponent target, CommonEnum.BattleType type) {
        // 校验战斗场景是否记录战损
        if (!checkSysAssitScene(roleRecord, type)) {
            return;
        }
        ConstConpensationTemplate conpensationTemplate = ResHolder.getInstance().getConstTemplate(ConstConpensationTemplate.class);
        // 清楚过期的补偿记录
        cleanMakeUpRecord();
        // 补偿上限检测
        if (makeUpRecord.size() >= conpensationTemplate.getCompensateCount()) {
            return;
        }
        // 本次战损
        int lose = 0;
        Map<Integer, SoldierTypeTemplate> soldierTypeTemplateMap = ResHolder.getInstance().getMap(SoldierTypeTemplate.class);
        PlaneService service = ResHolder.getResService(PlaneService.class);
        int cityLevel = getOwner().getMainCity().getLevel();
        for (BattleRecord.RoleMemberRecord record : roleRecord.getMembers().values()) {
            if (record.getPlayerId() != getEntityId()) {
                continue;
            }
            for (Struct.Soldier soldier : record.getSoldierData()) {
                // 记录战损值
                SoldierTypeTemplate template = soldierTypeTemplateMap.get(soldier.getSoldierId());
                Pair<Integer, Integer> oldPair = battleLoseMap.getOrDefault(soldier.getSoldierId(), new Pair<>(0, 0));
                Pair<Integer, Integer> pair = new Pair(soldier.getSevereWoundNum() + oldPair.getFirst(), soldier.getDeadNum() + oldPair.getSecond());
                lose = lose + template.getPower() * (soldier.getSevereWoundNum() + soldier.getDeadNum());
                // 记录补偿值
                int oldNum = battleLoseByWeekMap.getOrDefault(template.getSoldierLevel(), 0);
                if (oldNum >= service.getMakeUpConfig(cityLevel, template.getSoldierLevel()).getSoildierMaxEveryWeek()) {
                    continue;
                }
                battleLoseByWeekMap.put(template.getSoldierLevel(), oldNum + soldier.getSevereWoundNum() + soldier.getDeadNum());
                battleLoseMap.put(soldier.getSoldierId(), pair);
            }
        }
        // 战损累积
        battleLose += lose;
        // 检测战损是否达到触发线
        if (battleLose < ResHolder.getInstance().getValueFromMap(ConpensationBaselevelTemplate.class, cityLevel).getBattleLose()) {
            return;
        }
        // 检查各兵种的补偿值是否已达上限
        if (battleLoseMap.isEmpty()) {
            LOGGER.info("all level soldier reach makeup limit! battleLoseByWeekMap={}", battleLoseByWeekMap);
            return;
        }
        // 补偿
        giveAssistReward(battleLoseMap, target);
        // 清除战损记录
        battleLose = 0;
        battleLoseMap.clear();
        makeUpRecord.add(TimeUtils.getNextWeekStartMs(SystemClock.now()));
    }

    /**
     * 清除过期的补偿记录
     */
    private void cleanMakeUpRecord() {
        long now = SystemClock.now();
        int maxRound = battleLoseRecord.size();
        for (int i = 0; i < maxRound; i++) {
            long time = battleLoseRecord.peek();
            if (time > now) {
                return;
            }
            battleLoseRecord.poll();
        }

        maxRound = makeUpRecord.size();
        for (int i = 0; i < maxRound; i++) {
            long time = makeUpRecord.peek();
            if (time > now) {
                return;
            }
            makeUpRecord.poll();
        }
    }

    /**
     * 发放系统援助奖励
     *
     * @param map
     * @param target
     */
    private void giveAssistReward(Map<Integer, Pair<Integer, Integer>> map, SceneObjBattleComponent target) {
        Map<Integer, SoldierTypeTemplate> soldierTypeTemplateMap = ResHolder.getInstance().getMap(SoldierTypeTemplate.class);
        ConstConpensationTemplate conpensationTemplate = ResHolder.getInstance().getConstTemplate(ConstConpensationTemplate.class);
        Map<Integer, ItemTemplate> itemMap = ResHolder.getInstance().getMap(ItemTemplate.class);
        Map<Integer, Integer> reward = new HashMap<>();
        long now = SystemClock.now();
        int cityLevel = getOwner().getMainCity().getLevel();
        // 记录补偿
        battleLoseRecord.add(now + TimeUnit.SECONDS.toMillis(conpensationTemplate.getCompensateCD()));
        int item = 0;
        int itemNum = 0;
        PlaneService service = ResHolder.getResService(PlaneService.class);
        for (Map.Entry<Integer, Pair<Integer, Integer>> entry : map.entrySet()) {
            SoldierTypeTemplate template = soldierTypeTemplateMap.get(entry.getKey());
            int soldierLevel = template.getSoldierLevel();
            // 读取系统补偿表hao
            // 资源补偿
            for (IntPairType trainCost : template.getTrainRssCostPairList()) {
                int currencyId = trainCost.getKey();
                if (currencyId == CommonEnum.CurrencyType.OIL_VALUE) {
                    item = conpensationTemplate.getOilItemId();
                }
                if (currencyId == CommonEnum.CurrencyType.STEEL_VALUE) {
                    item = conpensationTemplate.getSteelItemId();
                }
                if (currencyId == CommonEnum.CurrencyType.RARE_EARTH_VALUE) {
                    item = conpensationTemplate.getRareEarthItemId();
                }
                if (currencyId == CommonEnum.CurrencyType.TIBERIUM_VALUE) {
                    item = conpensationTemplate.getTiberiumItemId();
                }
                itemNum = (int) Math.ceil(1.0 * ((entry.getValue().getFirst() + entry.getValue().getSecond()) * trainCost.getValue()) * service.getMakeUpConfig(cityLevel, soldierLevel).getResource() / GameLogicConstants.IPPM / itemMap.get(item).getEffectValue());
                itemNum += reward.getOrDefault(item, 0);
                if (itemNum <= 0) {
                    continue;
                }
                reward.put(item, itemNum);
            }
            // 治疗加速补偿
            itemNum = (int) Math.ceil(1.0 * entry.getValue().getFirst() * (int) TimeUtils.ms2Second(template.getTreatTimeMillis()) * service.getMakeUpConfig(cityLevel, soldierLevel).getHealTime() / GameLogicConstants.IPPM / itemMap.get(conpensationTemplate.getOneMinuteForTreatItemId()).getEffectValue());
            itemNum += reward.getOrDefault(conpensationTemplate.getOneMinuteForTreatItemId(), 0);
            if (itemNum > 0) {
                reward.put(conpensationTemplate.getOneMinuteForTreatItemId(), itemNum);
            }
            // 训练加速补偿
            itemNum = (int) Math.ceil(1.0 * entry.getValue().getSecond() * (int) TimeUtils.ms2Second(template.getTrainTimeMs()) * service.getMakeUpConfig(cityLevel, soldierLevel).getTrainingTime() / GameLogicConstants.IPPM / itemMap.get(conpensationTemplate.getOneMinuteForTrainItemId()).getEffectValue());
            itemNum += reward.getOrDefault(conpensationTemplate.getOneMinuteForTrainItemId(), 0);
            if (itemNum > 0) {
                reward.put(conpensationTemplate.getOneMinuteForTrainItemId(), itemNum);
            }
        }
        // tell player 准备系统援助邮件
        SsPlayerMisc.SendSysAssistCmd.Builder cmd = SsPlayerMisc.SendSysAssistCmd.newBuilder();
        if (!getOwner().isInClan()) {
            cmd.getSysAssistInfoBuilder().setMailId(conpensationTemplate.getNoAllianceCompensateMailId());
        } else {
            cmd.getSysAssistInfoBuilder().setMailId(conpensationTemplate.getHasAllianceCompensateMailId());
        }
        cmd.getSysAssistInfoBuilder().setExpiration(now + TimeUtils.second2Ms(conpensationTemplate.getRssAssistSystemSaveTime()));

        long targetPlayer = target.getOwner().getPlayerId();
        if (targetPlayer > 0) {
            AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayer);
            SceneClanEntity clan = abstractScenePlayerEntity.getSceneClan();
            if (clan != null) {
                cmd.getSysAssistInfoBuilder().setTargetClanShortName(clan.getClanSimpleName());
            }
            cmd.getSysAssistInfoBuilder().setTargetPlayerName(abstractScenePlayerEntity.getName());

        }
        Struct.ItemPairList.Builder builder = cmd.getSysAssistInfoBuilder().getItemRewardBuilder();
        for (Map.Entry<Integer, Integer> entry : reward.entrySet()) {
            builder.addDatas(Struct.ItemPair.newBuilder().setItemTemplateId(entry.getKey()).setCount(entry.getValue()).build());
        }
        if (getOwner().isInClan()) {
            for (long member : getOwner().getSceneClan().getMemberComponent().getMember()) {
                if (getOwner().getPlayerId() == member) {
                    break;
                }
                if (cmd.getSysAssistInfoBuilder().getAssistPlayerNameBuilder().getDatasList().size() >= 10) {
                    break;
                }
                String name = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(member).getName();
                cmd.getSysAssistInfoBuilder().getAssistPlayerNameBuilder().addDatas(name);
            }
        }
        getOwner().tellPlayer(cmd.build());
    }
}

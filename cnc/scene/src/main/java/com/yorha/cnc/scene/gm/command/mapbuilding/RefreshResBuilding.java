package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class RefreshResBuilding implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        ((MainSceneResMgrComponent) actor.getScene().getResMgrComponent()).globalRefresh();
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_COLLECT;
    }
}

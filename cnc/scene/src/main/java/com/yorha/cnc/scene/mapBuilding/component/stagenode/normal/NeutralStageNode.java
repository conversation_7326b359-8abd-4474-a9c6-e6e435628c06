package com.yorha.cnc.scene.mapBuilding.component.stagenode.normal;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.MapbuildingAiComponent;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.OccupyState;

/**
 * 中立状态
 *
 * <AUTHOR>
 */
public class NeutralStageNode extends StageNode {
    public NeutralStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_NEUTRAL;
    }

    @Override
    public void onLoad() {
        final MapbuildingAiComponent aiComponent = getOwner().getAiComponent();
        if (aiComponent != null) {
            aiComponent.start();
        }
    }

    @Override
    public void onEnter(long ts) {
        getProp().setState(OccupyState.TOS_NEUTRAL).setStateStartTsMs(ts);
        // 如果是黑暗祭坛关卡 由外部驱动
        if (getOwner().getTransformComponent().isDarkAltarCross()) {
            return;
        }
        getOwner().getProp().getRecommendSoldierTypeList().clear();
        // 移除原先拥有联盟的联系
        SceneClanEntity occupySceneClan = getOccupyMgr().getOccupySceneClan();
        if (occupySceneClan != null) {
            occupySceneClan.getMapBuildingComponent().removeMapBuilding(getEntityId());
        }
        SceneClanEntity ownerSceneClan = getOccupyMgr().getOwnerSceneClan();
        if (ownerSceneClan != null) {
            ownerSceneClan.getMapBuildingComponent().removeMapBuilding(getEntityId());
        }
        // 清理联盟相关字段
        getComponent().clearShowClan();
        // 清理下罩子
        getOwner().getSpecialSafeGuardComponent().specialSafeGuardOff(CommonEnum.SafeGuardReason.SGR_CLAN_FORTRESS);
        // 返回部队
        getOwner().getInnerArmyComponent().returnAllArmy();
        // 强制结束所有战斗
        getOwner().getBattleComponent().forceEndAllBattle();
        final MapbuildingAiComponent aiComponent = getOwner().getAiComponent();
        if (aiComponent != null) {
            aiComponent.start();
        }
    }


    @Override
    public void onLeave() {
        super.onLeave();
        final MapbuildingAiComponent aiComponent = getOwner().getAiComponent();
        if (aiComponent != null) {
            aiComponent.stop();
        }
    }

    @Override
    public String toString() {
        return ClassNameCacheUtils.getSimpleName(getClass());
    }
}

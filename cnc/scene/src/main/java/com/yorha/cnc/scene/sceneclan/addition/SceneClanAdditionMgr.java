package com.yorha.cnc.scene.sceneclan.addition;

import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.component.SceneClanBuildComponent;
import com.yorha.common.addition.AdditionMgrBase;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SceneClanAdditionMgr extends AdditionMgrBase<SceneClanEntity> {
    private final static Map<Integer, SceneClanAdditionDispatcher> ADDITION_DISPATCHER = new HashMap<>();

    static {
        ADDITION_DISPATCHER.put(CommonEnum.BuffEffectType.ET_BUILD_COMMAND_CENTER_SPEED_PERCENT_VALUE, SceneClanBuildComponent::onConstructSpeedAdditionChange);
        ADDITION_DISPATCHER.put(CommonEnum.BuffEffectType.ET_BUILD_MAIN_BASE_SPEED_PERCENT_VALUE, SceneClanBuildComponent::onConstructSpeedAdditionChange);
        ADDITION_DISPATCHER.put(CommonEnum.BuffEffectType.ET_BUILD_CLAN_FORTRESS_SPEED_PERCENT_VALUE, SceneClanBuildComponent::onConstructSpeedAdditionChange);
        ADDITION_DISPATCHER.put(CommonEnum.BuffEffectType.ET_CLAN_FORTRESS_HP_MAX_PERCENT_VALUE, SceneClanBuildComponent::onMaxHpAdditionChange);
        ADDITION_DISPATCHER.put(CommonEnum.BuffEffectType.ET_CLAN_BUILDING_HP_MAX_PERCENT_VALUE, SceneClanBuildComponent::onMaxHpAdditionChange);
        ADDITION_DISPATCHER.put(CommonEnum.BuffEffectType.ET_INC_EMENY_CLAN_BUILDING_FIRE_SPEED_PERCENT_VALUE, SceneClanBuildComponent::onFireSpeedChange);

    }

    public SceneClanAdditionMgr(SceneClanEntity owner) {
        super(owner);
    }

    @Override
    public AdditionSysProp getAdditionSys() {
        return getOwner().getProp().getAdditionSys();
    }

    @Override
    protected void update(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        if (additions.isEmpty()) {
            LOGGER.error("{} update additions is empty. sourceType:{}", getOwner(), sourceType);
            return;
        }

        Map<Integer, Long> oldAdditions = new HashMap<>();
        for (int additionId : additions.keySet()) {
            oldAdditions.put(additionId, getAddition(additionId));
        }

        updateAddition(sourceType, additions);

        Map<Integer, Long> newAdditions = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : oldAdditions.entrySet()) {
            long newAdditionValue = getAddition(entry.getKey());
            if (entry.getValue() != newAdditionValue) {
                newAdditions.put(entry.getKey(), newAdditionValue);
                LOGGER.info("scene clan:{} update addition. additionId:{}, sourceType:{}, value:{} -> {}", getOwner(), entry.getKey(), sourceType, entry.getValue(), getAddition(entry.getKey()));
            }
        }
        // 通知加成变更事件
        dispatchAdditionChange(newAdditions, getOwner());
    }

    public static void dispatchAdditionChange(Map<Integer, Long> newAdditions, SceneClanEntity entity) {
        for (int additionId : newAdditions.keySet()) {
            if (ADDITION_DISPATCHER.containsKey(additionId)) {
                ADDITION_DISPATCHER.get(additionId).dispatch(entity, additionId, newAdditions.get(additionId));
            }
        }
    }
}

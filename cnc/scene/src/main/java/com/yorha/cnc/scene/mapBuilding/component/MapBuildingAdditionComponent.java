package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;

/**
 * <AUTHOR>
 */
public class MapBuildingAdditionComponent extends SceneObjAdditionComponent {
    public MapBuildingAdditionComponent(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public MapBuildingEntity getOwner() {
        return (MapBuildingEntity) super.getOwner();
    }

    @Override
    public AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getScene().getPlayerMgrComponent().getScenePlayerOrNull(getOwner().getPlayerId());
    }
}

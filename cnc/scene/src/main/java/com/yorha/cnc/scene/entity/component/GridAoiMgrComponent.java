package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.aoiView.AoiViewHelper;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.aoiView.manager.AoiGridType;
import com.yorha.common.aoiView.manager.AoiNodeManager;
import com.yorha.common.aoiView.manager.AoiObserver;
import com.yorha.common.aoiView.manager.AoiObserverItem;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.SsScenePlayer;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2DoubleOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2IntOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongIterator;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveAction;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class GridAoiMgrComponent extends AoiMgrComponent {
    /**
     * 需要下发属性变化的entity
     */
    private final Map<AoiObserver, Entity.EntityNtfMsg.Builder> propChangeNtfMap = Maps.newHashMap();
    /**
     * 线程数
     */
    private final static int PARALLELISM = 4;
    /**
     * 执行总轮次
     */
    private final static int TICK_TURN = 4;
    /**
     * 多线程
     */
    private final ForkJoinPool forkJoinPool = ConcurrentHelper.newForkJoinPool("aoi-compute-", PARALLELISM);
    /**
     * 消息组装任务队列
     */
    private final List<SendNtfTask> sendNtfTaskList = new CopyOnWriteArrayList<>();
    /**
     * 本轮未移动 但是需要下发new的obj的attr
     */
    private final Map<Long, EntityAttrOuterClass.EntityAttr> newObjAll = new ConcurrentHashMap<>();
    protected AoiNodeManager manager;
    /**
     * 公共set 避免每次都new
     */
    LongOpenHashSet commonNewObj = new LongOpenHashSet();
    LongOpenHashSet commonDelObj = new LongOpenHashSet();
    /**
     * 当前总轮次
     */
    private int curTickTurn = 0;

    public GridAoiMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public void initAoi() {
        // 采用分层存储类型
        manager = new AoiNodeManager(ownerActor(), ownerActor(), getOwner().getZoneId(), AoiGridType.AGT_BRIEF, getOwner().getMapWidth(), getOwner().getMapHeight(), getAoiGridNum(), getAoiGridNum());
    }

    @Override
    public void addModEntityAttr(AoiObserver observer, long objId, EntityAttrOuterClass.EntityAttr.Builder attrBuilder) {
        Entity.EntityNtfMsg.Builder builder = propChangeNtfMap.computeIfAbsent(observer, k -> Entity.EntityNtfMsg.newBuilder()).addModEntities(attrBuilder);
        if (builder.getModEntitiesCount() >= GameLogicConstants.AOI_SYNC_PACKAGE_ENTITY_NUM) {
            builder.setZoneId(getOwner().getZoneId()).setReason(CommonEnum.SceneObjectNtfReason.SONR_AOI);
            SessionHelper.sendMsgToSession(observer.getSessionRef(), ownerActor(), MsgType.ENTITYNTFMSG, builder.build());
            propChangeNtfMap.remove(observer);
        }
    }

    /**
     * 处理mod的entity队列 并发送ntf
     */
    @Override
    public void consumeAllPropChangeListener() {
        super.consumeAllPropChangeListener();
        if (propChangeNtfMap.isEmpty()) {
            return;
        }
        try {
            for (Map.Entry<AoiObserver, Entity.EntityNtfMsg.Builder> entry : propChangeNtfMap.entrySet()) {
                Entity.EntityNtfMsg msg = entry.getValue().setZoneId(getOwner().getZoneId()).setReason(CommonEnum.SceneObjectNtfReason.SONR_AOI).build();
                SessionHelper.sendMsgToSession(entry.getKey().getSessionRef(), ownerActor(), MsgType.ENTITYNTFMSG, msg);
            }
        } finally {
            propChangeNtfMap.clear();
        }
    }

    public void afterTick() {
        try {
            forkJoinPool.invoke(new AoiUpdateTask(null));
        } catch (Exception e) {
            WechatLog.error("BigSceneAoiMgrComponent tick ", e);
        }
        newObjAll.clear();
        sendNtfTaskList.clear();
        curTickTurn++;
        if (curTickTurn >= TICK_TURN) {
            curTickTurn = 0;
        }
    }

    public void onPlayerClearView(long playerId, boolean isBystander, boolean isChangeZone) {
        if (isBystander) {
            manager.clearBystanderView(playerId, isChangeZone);
            return;
        }
        AbstractScenePlayerEntity scenePlayer = getOwner().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (scenePlayer == null) {
            return;
        }
        scenePlayer.getAoiObserverComponent().clearPlayerView(isChangeZone);
    }

    /**
     * 玩家更新普通层视野位置
     */
    public boolean onPlayerUpdateView(SsScenePlayer.UpdatePlayerViewAsk ask) {
        long playerId = ask.getPlayerId();
        AoiObserver observer;
        AbstractScenePlayerEntity scenePlayer = getOwner().getPlayerMgrComponent().getScenePlayerOrNull(ask.getPlayerId());
        if (scenePlayer != null) {
            observer = scenePlayer.getAoiObserverComponent();
        } else {
            observer = manager.getBystander(playerId);
            if (observer == null) {
                final IActorRef sessionRef = RefFactory.fromPb(ask.getSessionRef());
                if (StringUtils.isEmpty(sessionRef.getBusId())) {
                    LOGGER.error("checkAddBystander but byStander session ref is null {}", playerId);
                    return false;
                }
                observer = manager.checkAddBystander(playerId, ask.getZoneId(), sessionRef);
                if (observer == null) {
                    return false;
                }
            }
        }
        AABB aabb = AoiViewHelper.getAABBFromPoints(ask.getP1(), ask.getP2(), ask.getP3(), ask.getP4());
        int entityNumMax = ask.getEntityNumMax();
        if (entityNumMax == 0) {
            entityNumMax = GameLogicConstants.VIEW_OBJ_NUM_MAX;
            LOGGER.warn("onPlayerUpdateView entityNumMax is 0 {}", ask.getPlayerId());
        }
        observer.setUpdateView(aabb, ask.getLayerId(), entityNumMax);
        commonNewObj.clear();
        commonDelObj.clear();
        // 更新并计算出新的看到的人
        LongOpenHashSet newAoiSceneObj = updateAoiGrid(observer);
        // 计算需要下发的obj
        Pair<LongOpenHashSet, LongOpenHashSet> pair = calObserverNewAndDel(observer, newAoiSceneObj, commonNewObj, commonDelObj);
        if (pair == null) {
            return true;
        }
        if (pair.getFirst().isEmpty() && pair.getSecond().isEmpty()) {
            return true;
        }
        ObjMgrComponent objMgrComponent = getOwner().getObjMgrComponent();
        for (Map.Entry<Long, EntityAttrOuterClass.EntityAttr> entry : newObjAll.entrySet()) {
            SceneObjEntity sceneObjEntity = objMgrComponent.getSceneObjEntity(entry.getKey());
            entry.setValue(sceneObjEntity.getPropComponent().getFullAttr().build());
        }
        sendObserverEntityNtf(observer, pair.getFirst(), pair.getSecond());
        newObjAll.clear();
        return true;
    }

    /**
     * 更新视野格子
     */
    private LongOpenHashSet updateAoiGrid(AoiObserver observer) {
        Set<AoiGrid> curAoiGrids = observer.getOldAoiGrid();
        int newLayer = observer.getNewLayer();
        int oldLayer = observer.getOldLayer();
        AABB aabb = observer.getNewAABB();
        Set<AoiGrid> newAoiGrids = getAffectAoiGrids(observer.getNewAABB());
        // 如果没有改变层级 那就判一下是不是一样先 一样就不做操作了
        if (oldLayer == newLayer && !AoiViewHelper.isAoiChanged(curAoiGrids, newAoiGrids)) {
            return calNewViewObj(observer);
        }
        LongOpenHashSet newAoiSceneObj = new LongOpenHashSet();
        for (AoiGrid aoiGrid : curAoiGrids) {
            // 新的没了 remove
            if (!newAoiGrids.contains(aoiGrid)) {
                aoiGrid.removeObserver(observer, oldLayer);
            }
        }
        for (AoiGrid aoiGrid : newAoiGrids) {
            // 旧的没有 add
            if (!curAoiGrids.contains(aoiGrid)) {
                aoiGrid.addObserver(observer, newLayer);
            } else if (newLayer != oldLayer) {
                // 旧的有 change layer
                aoiGrid.changeObserverLayer(observer, oldLayer, newLayer);
            }
            Set<Long> sceneObjIds = aoiGrid.getSceneObjIds(newLayer);
            for (long objId : sceneObjIds) {
                SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(objId);
                if (sceneObjEntity == null) {
                    continue;
                }
                if (ShapeUtils.isContact(aabb, sceneObjEntity.getTransformComponent().getSelfShape())) {
                    newAoiSceneObj.add(objId);
                }
            }
        }
        observer.setNewView(newAoiGrids, newLayer);
        return newAoiSceneObj;
    }

    /**
     * 计算该观察者的new 和 del
     */
    private Pair<LongOpenHashSet, LongOpenHashSet> calObserverNewAndDel(AoiObserver observer, LongOpenHashSet newAoiSceneObj, LongOpenHashSet newObj, LongOpenHashSet delObj) {
        // 自己关注的都移除 这个走额外的途径
        newAoiSceneObj.removeAll(observer.getObTargetSet());
        // 数量
        if (newAoiSceneObj.size() > observer.getEntityNumMax()) {
            if (observer.getSceneZoneId() == getOwner().getZoneId()) {
                newAoiSceneObj = calAndSelectObjTop(observer, newAoiSceneObj);
            } else {
                newAoiSceneObj = calAndSelectObjTopBystander(observer, newAoiSceneObj);
            }
        }
        Set<Long> oldVewObj = observer.getCurViewObj();
        for (long objId : oldVewObj) {
            if (newAoiSceneObj.contains(objId)) {
                continue;
            }
            if (delObj == null) {
                delObj = new LongOpenHashSet();
            }
            delObj.add(objId);
        }
        // 没有要删除的 个数还一样
        if (oldVewObj.size() == newAoiSceneObj.size()) {
            if (delObj == null || delObj.isEmpty()) {
                return null;
            }
        }
        for (long objId : newAoiSceneObj) {
            if (oldVewObj.contains(objId)) {
                continue;
            }
            if (newObj == null) {
                newObj = new LongOpenHashSet();
            }
            newObj.add(objId);
            newObjAll.put(objId, EntityAttrOuterClass.EntityAttr.getDefaultInstance());
        }
        observer.setCurViewObj(newAoiSceneObj);
        return Pair.of(newObj, delObj);
    }

    /**
     * 计算能看到的top n 跨服视野
     */
    private LongOpenHashSet calAndSelectObjTopBystander(AoiObserver observer, LongOpenHashSet newAoiSceneObj) {
        TreeMap<Double, Long> scoreObj = new TreeMap<>();
        LongIterator iterator = newAoiSceneObj.iterator();
        Point center = observer.getNewAABB().getCenter();
        // 建筑个数不算在上限里面
        int buildingNum = 0;
        while (iterator.hasNext()) {
            long objId = iterator.nextLong();
            SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(objId);
            if (sceneObjEntity == null) {
                iterator.remove();
                continue;
            }
            // 无法移动的 建筑 优先级无限高
            if (sceneObjEntity.getMoveComponent() == null) {
                buildingNum++;
                continue;
            }
            // 上次有 也保留了
            if (observer.getCurViewObj().contains(objId)) {
                continue;
            }
            double score = 0;
            score += Point.calDisSquareBetweenTwoPoint(center, sceneObjEntity.getCurPoint());
            scoreObj.put(score, objId);
            iterator.remove();
        }
        int entityNumMax = observer.getEntityNumMax();
        int restNum = entityNumMax + buildingNum - newAoiSceneObj.size();
        if (restNum >= scoreObj.size()) {
            newAoiSceneObj.addAll(scoreObj.values());
            return newAoiSceneObj;
        }
        for (int i = 0; i < restNum; i++) {
            Map.Entry<Double, Long> entry = scoreObj.firstEntry();
            if (entry == null) {
                return newAoiSceneObj;
            }
            newAoiSceneObj.add(entry.getValue());
        }
        return newAoiSceneObj;
    }

    /**
     * 计算top n 能看到的
     */
    private LongOpenHashSet calAndSelectObjTop(AoiObserver observer, LongOpenHashSet newAoiSceneObj) {
        LongOpenHashSet skipScanObj = new LongOpenHashSet();
        Map<Long, Integer> gradeMap = new Long2IntOpenHashMap();
        Map<Long, Double> distanceMap = new Long2DoubleOpenHashMap();
        Point center = observer.getNewAABB().getCenter();
        int buildingNum = 0;
        // 自己关注的人
        AbstractScenePlayerEntity scenePlayer = getOwner().getPlayerMgrComponent().getScenePlayer(observer.getId());
        Set<Long> selfArmyAndTargetId = scenePlayer.getArmyMgrComponent().getSelfArmyAndTargetId();
        for (long objId : selfArmyAndTargetId) {
            if (!newAoiSceneObj.contains(objId)) {
                continue;
            }
            skipScanObj.add(objId);
        }
        // 第一轮先扫一遍 定档位
        for (long objId : newAoiSceneObj) {
            SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(objId);
            if (sceneObjEntity == null) {
                continue;
            }
            // 无法移动的 建筑 优先级无限高
            if (sceneObjEntity.getMoveComponent() == null) {
                skipScanObj.add(objId);
                buildingNum++;
                continue;
            }
            // 是精英野怪
            if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
                MonsterEntity monster = (MonsterEntity) sceneObjEntity;
                if (monster.getTemplate().getQuality() == CommonEnum.SceneObjQuality.ELITE) {
                    skipScanObj.add(objId);
                    // 野怪的攻击目标
                    long targetId = monster.getBattleComponent().getTargetId();
                    if (newAoiSceneObj.contains(targetId)) {
                        // 直接最高优先级
                        skipScanObj.add(objId);
                    }
                    continue;
                }
            }
            // 是集结部队
            if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Army) {
                ArmyEntity army = (ArmyEntity) sceneObjEntity;
                if (army.isRallyArmy()) {
                    skipScanObj.add(objId);
                    // 集结部队的攻击目标
                    if (army.getRallyEntity() != null) {
                        long targetId = army.getRallyEntity().getProp().getTargetId();
                        if (newAoiSceneObj.contains(targetId)) {
                            skipScanObj.add(objId);
                        }
                    }
                    continue;
                }
            }
            // 已经加过了
            if (skipScanObj.contains(objId)) {
                continue;
            }
            distanceMap.put(objId, Point.calDisSquareBetweenTwoPoint(center, sceneObjEntity.getCurPoint()));
            int oldGrade = gradeMap.getOrDefault(objId, 5);
            // 是army
            if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Army) {
                ArmyEntity army = (ArmyEntity) sceneObjEntity;
                if (scenePlayer.getClanId() != 0 && army.getClanId() == scenePlayer.getClanId()) {
                    gradeMap.put(objId, 4);
                    for (long targetId : army.getProp().getBattle().getRelationIdList()) {
                        if (!newAoiSceneObj.contains(targetId)) {
                            continue;
                        }
                        int targetOldGrade = gradeMap.getOrDefault(targetId, 5);
                        if (targetOldGrade > 4) {
                            gradeMap.put(targetId, 4);
                        }
                    }
                    continue;
                }
            }
            if (oldGrade < 5) {
                continue;
            }
            gradeMap.put(objId, 5);
        }
        // 数量够了
        int entityNumMax = observer.getEntityNumMax() + buildingNum;
        if (newAoiSceneObj.size() <= entityNumMax) {
            return newAoiSceneObj;
        }
        // 跟自己强相关 和 必须要显示 的数目够了
        if (skipScanObj.size() >= entityNumMax) {
            return skipScanObj;
        }
        // 分级别  按距离排序
        Map<Integer, TreeMap<Double, Long>> gradeScoreObjSet = new Int2ObjectOpenHashMap<>();
        // 上一轮能看到的
        Map<Integer, List<Long>> lastHas = new Int2ObjectOpenHashMap<>();
        // 统计下
        for (Map.Entry<Long, Integer> entry : gradeMap.entrySet()) {
            Double score = distanceMap.get(entry.getKey());
            if (score == null) {
                continue;
            }
            // 上一轮有的
            if (observer.getCurViewObj().contains(entry.getKey())) {
                lastHas.computeIfAbsent(entry.getValue(), k -> new ArrayList<>()).add(entry.getKey());
                continue;
            }
            gradeScoreObjSet.computeIfAbsent(entry.getValue(), k -> new TreeMap<Double, Long>()).put(score, entry.getKey());
        }
        for (int i = 4; i < 6; i++) {
            // 把上一轮有的加进来
            if (lastHas.containsKey(i)) {
                skipScanObj.addAll(lastHas.get(i));
                if (skipScanObj.size() >= entityNumMax) {
                    return skipScanObj;
                }
            }
            if (!gradeScoreObjSet.containsKey(i)) {
                continue;
            }
            // 按照距离排序加进来
            TreeMap<Double, Long> treeMap = gradeScoreObjSet.get(i);
            int num = treeMap.size() + skipScanObj.size();
            if (num == entityNumMax) {
                skipScanObj.addAll(treeMap.values());
                return skipScanObj;
            }
            if (num < entityNumMax) {
                skipScanObj.addAll(treeMap.values());
                continue;
            }
            int restNum = entityNumMax - skipScanObj.size();
            for (int j = 0; j < restNum; j++) {
                Map.Entry<Double, Long> entry = treeMap.pollFirstEntry();
                if (entry == null) {
                    return skipScanObj;
                }
                skipScanObj.add(entry.getValue());
            }
            return skipScanObj;
        }
        return skipScanObj;
    }

    private void sendObserverEntityNtf(AoiObserver observer, LongOpenHashSet newObj, LongOpenHashSet delObj) {
        Entity.EntityNtfMsg.Builder builder = Entity.EntityNtfMsg.newBuilder();
        builder.setZoneId(getOwner().getZoneId()).setReason(CommonEnum.SceneObjectNtfReason.SONR_AOI);
        boolean isNeedSendDel = false;
        if (delObj != null) {
            builder.addAllDelEntities(delObj);
            // 有delete的 必须得发一次
            isNeedSendDel = !delObj.isEmpty();
        }
        int num = 0;
        if (newObj != null) {
            for (long objId : newObj) {
                EntityAttrOuterClass.EntityAttr attr = newObjAll.get(objId);
                if (attr == null) {
                    continue;
                }
                builder.addNewEntities(attr);
                if (++num >= GameLogicConstants.AOI_SYNC_PACKAGE_ENTITY_NUM) {
                    SessionHelper.sendMsgToSession(observer.getSessionRef(), ownerActor(), MsgType.ENTITYNTFMSG, builder.build());
                    builder = Entity.EntityNtfMsg.newBuilder().setReason(CommonEnum.SceneObjectNtfReason.SONR_AOI).setZoneId(getOwner().getZoneId());
                    num = 0;
                    isNeedSendDel = false;
                }
            }
        }
        if (num != 0 || isNeedSendDel) {
            SessionHelper.sendMsgToSession(observer.getSessionRef(), ownerActor(), MsgType.ENTITYNTFMSG, builder.build());
        }
    }

    /**
     * 计算新的看到的obj
     */
    private LongOpenHashSet calNewViewObj(AoiObserver observer) {
        LongOpenHashSet newAoiSceneObj = new LongOpenHashSet();
        int layer = observer.getOldLayer();
        AABB aabb = observer.getNewAABB();
        for (AoiGrid aoiGrid : observer.getOldAoiGrid()) {
            Set<Long> sceneObjIds = aoiGrid.getSceneObjIds(layer);
            for (long objId : sceneObjIds) {
                if (newAoiSceneObj.contains(objId)) {
                    continue;
                }
                SceneObjEntity sceneObjEntity = getOwner().getObjMgrComponent().getSceneObjEntity(objId);
                if (sceneObjEntity == null) {
                    LOGGER.error("sendObserverEntityNtf but attr is null {} {}", observer.getId(), objId);
                    continue;
                }
                if (ShapeUtils.isContact(aabb, sceneObjEntity.getTransformComponent().getSelfShape())) {
                    newAoiSceneObj.add(objId);
                }
            }
        }
        // 自己关注的都移除 这个走额外的途径
        newAoiSceneObj.removeAll(observer.getObTargetSet());
        return newAoiSceneObj;
    }

    /**
     * 获取aoi格子划分单轴个数
     *
     * @return 一个方向切分的格子个数
     */
    protected int getAoiGridNum() {
        return GameLogicConstants.MAP_ID_2_AOI_NUM.get(getOwner().getMapId());
    }

    /**
     * 获取指定范围内 指定层级及以下 所有的ObserverId(mapPlayer)
     */
    public Set<AoiObserver> getAoiObserverSet(Shape shape, int layer) {
        return manager.getAoiNormalObserverList(shape, layer);
    }

    @Override
    public Set<AoiGrid> getAffectAoiGrids(Shape shape) {
        return manager.getAffectAoiGrids(shape);
    }

    @Override
    public void consumerAffectAoiGrids(Shape shape, Consumer<AoiGrid> consumer) {
        manager.consumerAffectAoiGrids(shape, consumer);
    }

    @Override
    public void consumerAffectSceneObj(Shape shape, Consumer<SceneObjEntity> consumer) {
        ObjMgrComponent objComponent = getOwner().getObjMgrComponent();
        Set<AoiGrid> affectAoiGrids = getAffectAoiGrids(shape);
        for (AoiGrid grid : affectAoiGrids) {
            Set<Long> sceneObjIds = grid.getSceneObjIds();
            for (Long sceneObjId : sceneObjIds) {
                SceneObjEntity target = objComponent.getSceneObjEntity(sceneObjId);
                if (target == null) {
                    continue;
                }
                consumer.accept(target);
            }
        }
    }

    /**
     * 获取指定范围内的所有 与自己相交的SceneObj
     * 一般获取了返回值后，直接for循环处理，严禁把返回值容器继续传递
     * 共用了返回值，小心！！！
     * 必要时刻需要在外面深拷贝使用！
     */
    @Override
    public Set<SceneObjEntity> getAffectedAoiSceneObjList(Shape shape) {
        if (shape == null) {
            return Collections.emptySet();
        }
        affectedAoiSceneObjRet.clear();
        ObjMgrComponent objComponent = getOwner().getObjMgrComponent();
        Set<AoiGrid> affectAoiGrids = getAffectAoiGrids(shape);
        for (AoiGrid grid : affectAoiGrids) {
            Set<Long> sceneObjIds = grid.getSceneObjIds();
            for (Long sceneObjId : sceneObjIds) {
                SceneObjEntity target = objComponent.getSceneObjEntity(sceneObjId);
                // 判断模型圈是否包含目标中心点
                if (target == null || !shape.containsPoint(target.getCurPoint().getX(), target.getCurPoint().getY())) {
                    continue;
                }
                affectedAoiSceneObjRet.add(target);
            }
        }
        return Collections.unmodifiableSet(affectedAoiSceneObjRet);
    }

    /**
     * 获取指定范围内的所有SceneObj
     * 一般获取了返回值后，直接for循环处理，严禁把返回值容器继续传递
     * 共用了返回值，小心！！！
     * 必要时刻需要在外面深拷贝使用！
     */
    @Override
    public Collection<SceneObjEntity> getAoiSceneObjList(Shape shape) {
        if (shape == null) {
            return Collections.emptySet();
        }
        affectedAoiSceneObjRet.clear();
        ObjMgrComponent objComponent = getOwner().getObjMgrComponent();
        Set<AoiGrid> affectAoiGrids = getAffectAoiGrids(shape);
        for (AoiGrid grid : affectAoiGrids) {
            Set<Long> sceneObjIds = grid.getSceneObjIds();
            for (Long sceneObjId : sceneObjIds) {
                SceneObjEntity target = objComponent.getSceneObjEntity(sceneObjId);
                if (target == null) {
                    continue;
                }
                affectedAoiSceneObjRet.add(target);
            }
        }
        return Collections.unmodifiableSet(affectedAoiSceneObjRet);
    }

    class SendNtfTask extends RecursiveAction {
        private final AoiObserver observer;
        private final LongOpenHashSet newObj;
        private final LongOpenHashSet delObj;

        public SendNtfTask(AoiObserver observer, LongOpenHashSet newObj, LongOpenHashSet delObj) {
            this.observer = observer;
            this.newObj = newObj;
            this.delObj = delObj;
        }

        @Override
        protected void compute() {
            sendObserverEntityNtf(observer, newObj, delObj);
        }
    }

    class AoiUpdateTask extends RecursiveAction {
        private final AoiObserver observer;

        public AoiUpdateTask(AoiObserver observer) {
            this.observer = observer;
        }

        @Override
        protected void compute() {
            if (observer == null) {
                GeminiStopWatch watch = new GeminiStopWatch("aoi tick");
                Map<Long, AbstractScenePlayerEntity> onlinePlayer = getOwner().getPlayerMgrComponent().getOnlinePlayer();
                List<AoiUpdateTask> taskList = new ArrayList<>(onlinePlayer.size() / TICK_TURN + 1);
                for (AbstractScenePlayerEntity player : onlinePlayer.values()) {
                    if (player.getEntityId() % TICK_TURN != curTickTurn) {
                        continue;
                    }
                    if (player.getAoiObserverComponent().getNewAABB() == null) {
                        continue;
                    }
                    taskList.add(new AoiUpdateTask(player.getAoiObserverComponent()));
                }
                for (AoiObserverItem item : manager.getBystanderMap().values()) {
                    if (item.getId() % TICK_TURN != curTickTurn) {
                        continue;
                    }
                    taskList.add(new AoiUpdateTask(item));
                }
                watch.mark("for end " + taskList.size());
                // 计算视野差
                invokeAll(taskList);
                watch.mark("cal end " + taskList.size());
                ObjMgrComponent objMgrComponent = getOwner().getObjMgrComponent();
                for (Map.Entry<Long, EntityAttrOuterClass.EntityAttr> entry : newObjAll.entrySet()) {
                    SceneObjEntity sceneObjEntity = objMgrComponent.getSceneObjEntity(entry.getKey());
                    entry.setValue(sceneObjEntity.getPropComponent().getFullAttr().build());
                }
                if (!sendNtfTaskList.isEmpty()) {
                    invokeAll(sendNtfTaskList);
                    watch.mark("send ntf end " + sendNtfTaskList.size());
                }
                if (watch.getTotalCost() > 5) {
                    LOGGER.info("BigSceneAoiMgrComponent AoiUpdateTask {}", watch.stat());
                }
                return;
            }
            // 计算新一轮的视野对象列表
            LongOpenHashSet newViewObj = calNewViewObj(observer);
            // 计算new和del 并更新当前视野对象列表
            Pair<LongOpenHashSet, LongOpenHashSet> pair = calObserverNewAndDel(observer, newViewObj, null, null);
            // 没有需要发的
            if (pair == null) {
                return;
            }
            if (pair.getFirst() == null && pair.getSecond() == null) {
                return;
            }
            sendNtfTaskList.add(new SendNtfTask(observer, pair.getFirst(), pair.getSecond()));
        }
    }
}

package com.yorha.cnc.scene.event.battle;

import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 * <p>
 * 战斗角色回合结算事件
 */
public class BattleRoleSettleRoundEvent extends IEvent {

    private final DamageResult damageResult;
    private final long playerId;
    private final long entityId;

    public BattleRoleSettleRoundEvent(long entityId, long playerId, DamageResult damageResult) {
        this.entityId = entityId;
        this.damageResult = damageResult;
        this.playerId = playerId;
    }

    public DamageResult getDamageResult() {
        return damageResult;
    }

    public long getPlayerId() {
        return playerId;
    }

    public long getEntityId() {
        return entityId;
    }
}

package com.yorha.cnc.scene.event.player;

import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;

/**
 * <AUTHOR>
 */
public class PlayerPicChangeEvent extends IEventWithEntityId {
    private final int pic;
    private final String picUrl;

    public PlayerPicChangeEvent(long entityId, int pic, String picUrl) {
        super(entityId);
        this.pic = pic;
        this.picUrl = picUrl;
    }

    public int getPic() {
        return pic;
    }

    public String getPicUrl() {
        return picUrl;
    }
}


package com.yorha.cnc.scene.sceneObj.ai.trigger.impl;


import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.trigger.AiTrigger;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import res.template.AiStateTriggerTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 加state tag
 * <AUTHOR>
 */
public class AddTagTrigger extends AiTrigger {
    private List<IntPairType> params;
    public AddTagTrigger(AiStateTriggerTemplate template) {
        super(template);
    }


    @Override
    protected void parse(List<IntPairType> param) {
        this.params = new ArrayList<>();
        this.params.addAll(param);
    }

    @Override
    protected boolean isEffectSatisfied(SceneObjEntity owner) {
        return true;
    }


    @Override
    protected void doTrigger(SceneObjEntity owner) {
        List<Pair<Integer, Integer>> tagList = new ArrayList<>();
        for (IntPairType param : params) {
            tagList.add(Pair.of(param.getKey(), param.getValue()));
        }
        owner.getAiComponent().addTag(tagList);
    }

    @Override
    protected String getTriggerParam() {
        return params.toString();
    }

    @Override
    protected String getTriggerName() {
        return "AddTag";
    }
}

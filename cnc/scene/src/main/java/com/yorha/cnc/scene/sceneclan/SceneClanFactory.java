package com.yorha.cnc.scene.sceneclan;

import com.google.common.collect.Lists;
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.helper.NameHelper;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.game.gen.prop.ClanMemberProp;
import com.yorha.game.gen.prop.ClanProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsName;
import com.yorha.proto.Struct.SceneClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;


/**
 * <AUTHOR>
 */
public class SceneClanFactory {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanFactory.class);

    public static void createSceneClan(SceneEntity sceneEntity, long clanId, SceneClan sceneClan, int zoneId) {
        if (sceneEntity.isBigScene()) {
            SceneClanEntity sceneClanEntity = new SceneClanEntity(clanId, sceneEntity, sceneClan);
            sceneEntity.getClanMgrComponent().addSceneClan(sceneClanEntity);
            LOGGER.info("create {} in {} success", sceneClanEntity, sceneEntity);
            // 手动把新创建的军团的放到州id -> sceneClan实体的映射中
            sceneEntity.getClanMgrComponent().addToRegionToClanMap(sceneClanEntity);
            sceneEntity.getBigScene().getMileStoneOrNullComponent().onClanCreate(sceneClan.getClanId(), sceneClan.getPower(), 1);
        }
    }

    public static void restoreSceneClan(BigSceneEntity sceneEntity, ClanProp prop) {
        long clanId = prop.getId();
        LOGGER.info("restoreSceneClan, start restore scene clan {} in scene {}", clanId, sceneEntity);
        SceneClan.Builder builder = SceneClan.newBuilder();
        builder.setClanId(clanId)
                .setClanName(prop.getBase().getName())
                .setClanSimpleName(prop.getBase().getSname())
                .setTerritoryColor(prop.getBase().getTerritoryColor())
                .setFlagColor(prop.getBase().getFlagColor())
                .setFlagSign(prop.getBase().getFlagSign())
                .setFlagShading(prop.getBase().getFlagShading())
                .setNationFlagId(prop.getBase().getNationFlagId())
                .setOwnerId(prop.getOwnerId())
                .setPower(prop.getCombat());
        if (prop.getOwnerId() != 0) {
            ClanMemberProp memberV = prop.getMemberV(prop.getOwnerId());
            if (memberV == null) {
                // 盟主都找不到了 应该解散了 为啥db里还有
                WechatLog.error("try restore SceneClan but owner is null {}", clanId);
                return;
            }
            builder.setOwnerName(memberV.getCardHead().getName());
        }
        sceneEntity.getClanMgrComponent().addClanDbPowerWhenBigSceneInit(clanId, prop, null);
        LOGGER.info("clanId {}, member size {}", clanId, prop.getMemberSize());
        // 设置当前状态，仅loading时有效
        builder.setStage(prop.getStageModel().getStage());
        // load clan 加成
        AdditionSysProp additionSysProp = AdditionUtil.loadSceneClanAdditionFromDb(prop.getAdditionSys());
        additionSysProp.copyToSs(builder.getAdditionSysBuilder());

        SceneClanEntity sceneClanEntity = new SceneClanEntity(clanId, sceneEntity, builder.build());
        sceneEntity.getClanMgrComponent().addSceneClan(sceneClanEntity);
        LOGGER.info("restore success {} {}", sceneClanEntity, prop.getMemberSize());
        // 处理名字搜索
        ArrayList<SsName.NamePair> list = Lists.newArrayList();
        list.add(MsgHelper.buildNamePair(CommonEnum.NameType.CLAN_SIMPLE_NAME, sceneClanEntity.getClanSimpleName()));
        list.add(MsgHelper.buildNamePair(CommonEnum.NameType.CLAN_NAME, sceneClanEntity.getClanName()));
        NameHelper.syncToNameActor(sceneEntity.ownerActor(), list, clanId);
    }
}

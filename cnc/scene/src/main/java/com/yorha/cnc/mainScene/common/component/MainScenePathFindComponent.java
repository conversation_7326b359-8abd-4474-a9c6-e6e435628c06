package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.pathfinding.PathFindingHelper;
import com.yorha.cnc.scene.pathfinding.manager.MainScenePathFindingManager;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneObj.move.PrePath;
import com.yorha.cnc.scene.sceneObj.move.SearchPathAsyncResult;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.proto.SsPathFinding;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.function.Consumer;

/**
 * 主世界分州寻路
 *
 * <AUTHOR>
 */
public class MainScenePathFindComponent extends PathFindMgrComponent {
    private static final Logger LOGGER = LogManager.getLogger(MainScenePathFindComponent.class);

    public MainScenePathFindComponent(SceneEntity owner) {
        super(owner);
    }

    @Override
    protected void initNav() {
        // 初始化异步寻路actor
        PathFindingHelper.initPathFindingActor(getOwner().getMapId());
        manager = new MainScenePathFindingManager(getOwner().getMapId());
    }

    @Override
    protected boolean isOpenAsyncPathFind() {
        return true;
    }

    /**
     * 无动态阻挡修正的寻路， 用于客户端查询路线耗时
     */
    @Override
    public List<Point> searchPathWithoutCollision(AbstractScenePlayerEntity scenePlayer, Point srcPoint, Point endPoint) {
        int srcRegion = MapGridDataManager.getRegionId(getOwner().getMapId(), srcPoint);
        int endRegion = MapGridDataManager.getRegionId(getOwner().getMapId(), endPoint);
        if (srcRegion == endRegion) {
            List<Point> points = manager.findNavPath(srcPoint, endPoint);
            if (points.size() < 2) {
                return Collections.emptyList();
            }
            return points;
        }
        SceneClanEntity sceneClan = scenePlayer.getSceneClan();
        if (sceneClan == null) {
            return Collections.emptyList();
        }
        try {
            PrePath crossingPath = getManager().findCrossingPath(getEntityId(), sceneClan.getCrossComponent().getCrossingMap(), sceneClan.getCrossComponent().getCrossing(), srcRegion, endRegion, srcPoint, endPoint, 0);
            if (crossingPath == null || crossingPath.getFirstPath() == null || crossingPath.getFirstPath().isEmpty()) {
                return Collections.emptyList();
            }
            List<Point> ret = new ArrayList<>();
            for (List<Point> pointList : crossingPath.prePoint) {
                ret.addAll(pointList);
            }
            return ret;
        } catch (Exception e) {
            if (!GeminiException.isLogicException(e)) {
                LOGGER.error("searchPathWithoutCollision failed {} ", scenePlayer, e);
            }
            return Collections.emptyList();
        }
    }

    @Override
    protected MoveData searchPathReal(SceneObjEntity obj, Point src, Point end, int searchTag, int fixDistance, GeminiStopWatch watch) {
        int srcRegion = MapGridDataManager.getRegionId(getOwner().getMapId(), src);
        int endRegion = MapGridDataManager.getRegionId(getOwner().getMapId(), end);
        if (srcRegion == endRegion) {
            return super.searchPathReal(obj, src, end, searchTag, fixDistance, watch);
        }
        // 无视关卡占有 使用全州联通图
        if (GameLogicConstants.ignoreCrossOwner(searchTag)) {
            PrePath path = getManager().findPathWithAllCrossing(obj.getEntityId(), srcRegion, endRegion, src, end, searchTag);
            watch.mark("search end");
            return correctNavPath(path, end, searchTag, fixDistance, watch);
        }
        SceneClanEntity sceneClan = getOwner().getClanMgrComponent().getSceneClanOrNull(obj.getClanId());
        if (sceneClan == null) {
            throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
        }
        BoolMap crossingMap = sceneClan.getCrossComponent().getCrossingMap();
        if (crossingMap == null) {
            throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
        }
        PrePath crossingPath = getManager().findCrossingPath(getEntityId(), crossingMap, sceneClan.getCrossComponent().getCrossing(), srcRegion, endRegion, src, end, searchTag);
        if (crossingPath == null || crossingPath.getFirstPath() == null || crossingPath.getFirstPath().isEmpty()) {
            throw new GeminiException(ErrorCode.MOVE_NO_PATH, String.format("%s %s %s", obj.getEntityId(), src, end));
        }
        watch.mark("cross nav find crossing path");
        return correctNavPath(crossingPath, end, searchTag, fixDistance, watch);
    }

    public MoveData correctNavPath(PrePath prePath, Point end, int searchTag, int fixDistance, GeminiStopWatch watch) {
        // 路径简化
        if (!GameLogicConstants.ignoreStatic(searchTag)) {
            prePath.simplePath(getManager());
            watch.mark("simple crossing path");
        }
        // 绕城计算 + 终点模型圈修正
        boolean isIgnoreCrossOwner = GameLogicConstants.ignoreCrossOwner(searchTag);
        boolean isNeedCorrectByCollision = GameLogicConstants.dynamicPathCorrect(searchTag);
        List<Point> finalPath = new ArrayList<>();
        List<Point> lastPathList = new ArrayList<>();
        Map<Integer, Integer> indexToCrossPart = new HashMap<>();
        for (int i = 0; i < prePath.prePoint.size(); i++) {
            List<Point> list = prePath.prePoint.get(i);
            if (isNeedCorrectByCollision) {
                list = correctPathByCityCollision(list);
                watch.mark("dynamic blocking correct");
            }
            if (i == prePath.prePoint.size() - 1) {
                // 终点交互修正
                if (fixDistance > 0) {
                    list = correctPathByMoveTarget(list, fixDistance, end);
                    watch.mark("move target dis correct");
                }
                lastPathList = list;
                if (isIgnoreCrossOwner) {
                    finalPath.addAll(list);
                }
            } else {
                finalPath.addAll(list);
                if (!isIgnoreCrossOwner) {
                    indexToCrossPart.put(finalPath.size() - 1, prePath.prePart.get(i));
                }
            }
        }
        if (isIgnoreCrossOwner) {
            return new MoveData(finalPath, searchTag);
        }
        return new MoveData(finalPath, lastPathList, indexToCrossPart, searchTag);
    }

    @Override
    public MoveData searchPathAsync(SceneObjEntity entity, Point src, Point end, int searchTag, int fixDistance, Consumer<SearchPathAsyncResult> consumer) {
        // 同州且无视静态阻挡 直接拼接
        int srcRegion = MapGridDataManager.getRegionId(getOwner().getMapId(), src);
        int endRegion = MapGridDataManager.getRegionId(getOwner().getMapId(), end);
        if (srcRegion == endRegion && GameLogicConstants.ignoreStatic(searchTag)) {
            return new MoveData(buildIgnoreAllPath(src, end, fixDistance), searchTag);
        }
        SsPathFinding.SearchPathAsyncAsk.Builder builder = buildSearchPathAsyncAsk(entity, src, end, searchTag);
        builder.setSrcRegion(srcRegion).setEndRegion(endRegion);
        if (srcRegion != endRegion && !GameLogicConstants.ignoreCrossOwner(searchTag)) {
            SceneClanEntity sceneClan = getOwner().getClanMgrComponent().getSceneClanOrNull(entity.getClanId());
            if (sceneClan == null) {
                throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
            }
            // 跨关隘寻路  需要copy关隘数据过去
            sceneClan.getCrossComponent().buildSearchPathAsyncAsk(entity, srcRegion, endRegion, builder);
        }
        PathFindingHelper.askPathFindingActor(ownerActor(), getOwner().getMapId(), entity.getEntityId(), builder.build(), consumer);
        return null;
    }
    

    @Override
    public MainScenePathFindingManager getManager() {
        return (MainScenePathFindingManager) super.getManager();
    }

    @Override
    public void destroyNav() {
        manager.onDestroy();
        manager = null;
    }
}

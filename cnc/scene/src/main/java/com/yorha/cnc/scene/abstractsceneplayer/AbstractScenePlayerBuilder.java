package com.yorha.cnc.scene.abstractsceneplayer;

import com.yorha.cnc.scene.abstractsceneplayer.component.*;

/**
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerBuilder<T extends AbstractScenePlayerEntity> {

    public abstract AbstractScenePlayerRallyComponent rallyComponent(T owner);

    public abstract AbstractScenePlayerSoldierMgrComponent soldierMgrComponent(T owner);

    public abstract AbstractScenePlayerDevBuffComponent devBuffComponent(T owner);

    public AbstractScenePlayerAdditionComponent additionComponent(T owner) {
        return null;
    }

    public AbstractScenePlayerArmyMgrComponent armyMgrComponent(T owner) {
        return new AbstractScenePlayerArmyMgrComponent(owner);
    }

    public AbstractScenePlayerPlaneComponent planeMgrComponent(T owner) {
        return new AbstractScenePlayerPlaneComponent(owner);
    }

    public AbstractScenePlayerCityMgrComponent cityComponent(T owner) {
        return new AbstractScenePlayerCityMgrComponent(owner);
    }

    public AbstractScenePlayerPickUpComponent pickUpComponent(T owner) {
        return new AbstractScenePlayerPickUpComponent(owner);
    }

    public AbstractScenePlayerAoiObserverComponent aoiObserverComponent(T owner) {
        return new AbstractScenePlayerAoiObserverComponent(owner);
    }

    public AbstractScenePlayerWarningComponent warningComponent(T owner) {
        return null;
    }

    public AbstractScenePlayerTimerComponent timerComponent(T owner) {
        return new AbstractScenePlayerTimerComponent(owner);
    }

    public AbstractScenePlayerHospitalComponent hospitalComponent(T owner) {
        return null;
    }

    public AbstractScenePlayerBattleComponent battleComponent(T owner) {
        return null;
    }

    public AbstractScenePlayerWallComponent wallComponent(T owner) {
        return null;
    }
}

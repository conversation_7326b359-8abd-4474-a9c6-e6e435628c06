package com.yorha.cnc.scene;

import com.yorha.common.actor.mailbox.MsgContext;
import com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl;
import com.yorha.common.actorservice.ActorMetaData;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.actor.msg.ActorTimerMsg;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class SceneActorMiddlewareImpl extends DefaultMailboxMonitorMiddlewareImpl {
    private static final Logger LOGGER = LogManager.getLogger(SceneActorMiddlewareImpl.class);

    public SceneActorMiddlewareImpl(ActorMetaData metaData) {
        super(metaData);
    }

    @Override
    protected void warnRealCostOverTimeMessage(MsgContext context, long startTsNs) {
        final IActorMsg payload = context.getEnvelope().getPayload();
        final long costNs = SystemClock.nanoTimeNative() - startTsNs;
        if (costNs <= 20_000_000) {
            return;
        }
        if (payload instanceof ActorTimerMsg) {
            ActorTimerMsg timerMsg = (ActorTimerMsg) payload;
            if (timerMsg.profName().endsWith(TimerReasonType.SCENE_TICK.name())) {
                return;
            }
        } else if (payload instanceof TypedMsg) {
            TypedMsg typedMsg = (TypedMsg) payload;
            // 打印详细参数
            LOGGER.warn("gemini_perf scene handle msg over time,{} ms,{} {} ", costNs / 1000000,typedMsg, typedMsg.getMsg());
        } else {
            LOGGER.warn("gemini_perf scene handle msg over time,{} ms, {}", costNs / 1000000,context.getEnvelope());
        }
        super.warnRealCostOverTimeMessage(context, startTsNs);
    }
}

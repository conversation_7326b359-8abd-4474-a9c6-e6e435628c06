package com.yorha.cnc.scene.mapBuilding.component.stagenode.territory;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 保护阶段
 *
 * <AUTHOR>
 */
public class ProtectStageNode extends StageNode {
    private static final Logger LOGGER = LogManager.getLogger(ProtectStageNode.class);

    public ProtectStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_PROTECT;
    }

    @Override
    public void onLoad() {
        // 如果是黑暗祭坛关卡 由外部驱动
        if (getOwner().getTransformComponent().isDarkAltarCross()) {
            return;
        }
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2ProtectEnd);
            return;
        }
        onProtectEnd(getProp().getStateEndTsMs());
    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        // 如果是黑暗祭坛关卡 由外部驱动
        if (getOwner().getTransformComponent().isDarkAltarCross()) {
            getProp().setState(getStage()).setStateStartTsMs(ts);
            return;
        }
        long endTs = TimeUtils.second2Ms(getTemplate().getProtectTime()) + ts;
        if (endTs <= SystemClock.now()) {
            onProtectEnd(endTs);
            return;
        }
        // 强制结束所有战斗
        getOwner().getBattleComponent().forceEndAllBattle();
        getProp().setState(getStage()).setStateStartTsMs(ts).setStateEndTsMs(endTs);
        addStageTimer(this::time2ProtectEnd);
    }

    private void time2ProtectEnd() {
        getComponent().clearStageTimer();
        onProtectEnd(SystemClock.now());
    }

    private void onProtectEnd(long ts) {
        // 看下有没有在指挥网中
        if (getOwner().getProp().getConstructInfo().getIsConnectedToCommandNet()) {
            getComponent().transNewNode(new InCommandNetStageNode(getOwner()), ts);
            return;
        }
        getComponent().transNewNode(new DesertedStageNode(getOwner()), ts);
    }
}

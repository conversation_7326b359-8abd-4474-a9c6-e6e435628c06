package com.yorha.cnc.scene.gm.command.clanResBuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class DestroyClanResBuilding implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        long clanId = scenePlayer.getClanId();
        SceneClanEntity sceneClan = actor.getScene().getClanMgrComponent().getSceneClan(clanId);
        sceneClan.getBuildComponent().gmRemoveClanResBuilding();
    }

    @Override
    public String showHelp() {
        return "DestroyClanResBuilding";
    }
}

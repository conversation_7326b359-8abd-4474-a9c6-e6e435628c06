package com.yorha.cnc.scene.pathfinding;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.sceneObj.move.SearchPathAsyncResult;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.proto.SsPathFinding;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 主世界寻路接口
 * 目前原则上 一个map对应二个寻路actor
 *
 * <AUTHOR>
 */
public class PathFindingHelper {
    private static final Logger LOGGER = LogManager.getLogger(PathFindingHelper.class);

    public static final int PATHFINDING_ACTOR_NUM = 2;

    /**
     * c++层nav的id生成器
     */
    private static final AtomicInteger ID = new AtomicInteger(1);

    /**
     * 到c++层的id需要保证同进程唯一
     * 生成id 保证正数
     * 前一部分是保留位 用于公用的大世界
     * 0-9是本地迁城阻挡
     * 100-200是本地寻路图
     * [200-400) 是异步寻路线程
     * 副本本地使用生成id actor会销毁释放
     */
    public static int genNavId() {
        return ID.updateAndGet(operand -> {
            if (operand < Integer.MAX_VALUE) {
                return operand + 1;
            } else {
                return 1;
            }
        });
    }

    /**
     * 根据类型和使用的id映射到对应的actorId
     */
    public static String genActorId(int mapId, long id) {
        return String.valueOf(mapId) + id % PATHFINDING_ACTOR_NUM;
    }

    /**
     * 初始化寻路actor
     */
    public static void initPathFindingActor(int mapId) {
        int configThreadNum = ServerContext.getActorSystem().getDispatcher(ActorRole.PathFinding.name()).getParallelism();
        if (configThreadNum != PATHFINDING_ACTOR_NUM) {
            throw new GeminiException("PathFindHelper initPathFindingActor, pathFinding Actor configThreadNum not equal PATHFINDING_ACTOR_NUM, configThreadNum={}, PATHFINDING_ACTOR_NUM={}",
                    configThreadNum, PATHFINDING_ACTOR_NUM);
        }
        for (int i = 0; i < PATHFINDING_ACTOR_NUM; i++) {
            final IActorRef ref = RefFactory.ofPathFinding(ServerContext.getBusId(), String.valueOf(mapId) + i);
            ActorSendMsgUtils.sendAndCreate(ref, new ActorRunnable<>("init pathfinding actor",
                    (actor) -> ((PathFindingActor) actor).init(mapId)));
        }
    }

    /**
     * 发到对应的寻路actor上
     */
    public static void askPathFindingActor(AbstractActor actor, int mapId, long id, GeneratedMessageV3 msg, Consumer<SearchPathAsyncResult> consumer) {
        actor.ask(RefFactory.ofPathFinding(ServerContext.getBusId(), genActorId(mapId, id)), msg).onComplete(
                (ret, err) -> consumer.accept(new SearchPathAsyncResult(ret == null ? null : (SsPathFinding.SearchPathAsyncAns) ret, err))
        );
    }

    public static void monitSearchPathCost(Point src, Point end, GeminiStopWatch watch, String source) {
        final long searchPathTotalCostMs = watch.getTotalCost();
        if (searchPathTotalCostMs > MonitorConstant.SEARCH_PATH_OVER_TIME) {
            LOGGER.warn("gemini_perf search path cost too much {}. src:{} end:{} {}", source, src, end, watch.stat());
        }
        MonitorUnit.SEARCH_PATH_COUNTER.labels(ServerContext.getBusId()).inc();
    }
}

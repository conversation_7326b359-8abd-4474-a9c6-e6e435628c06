package com.yorha.cnc.scene.monster.component;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.event.battle.AbandonBeAttackEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.helper.TroopHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.BattleProp;
import com.yorha.game.gen.prop.BattleRecordAllProp;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import res.template.SoldierTypeTemplate;

import java.util.*;

import static com.yorha.common.constant.Constants.N_100;
import static com.yorha.proto.CommonEnum.SceneObjType.SOT_ARMY_GROUP;

/**
 * <AUTHOR>
 */
public class MonsterBattleComponent extends SceneObjBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(MonsterBattleComponent.class);
    /**
     * 发起攻击的对象集合
     */
    private final Set<Long> launchAttacker;
    /**
     * 战斗最后一轮伤害记录
     */
    private Map<Long, Integer> attackerDamage;

    public MonsterBattleComponent(SceneObjEntity owner) {
        super(owner, CommonEnum.SceneObjType.SOT_MONSTER, null);
        this.launchAttacker = new HashSet<>();
    }

    @Override
    public void init() {
        super.init();
        getBattleRole().buildHero();
        getBattleRole().initSoldierByTroop();
        getBattleRole().initState();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::abandonBeAttack, AbandonBeAttackEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndSingleRelation, EndSingleBattleEvent.class);
    }

    @Override
    public void postInit() {
        super.postInit();
        // 战斗召唤物
        if (getOwner().isBattleSummons()) {
            getOwner().addTick(SceneTickReason.TICK_BATTLE);
        }
    }

    /**
     * 注册对回合结算事件的监听 只有极个别野怪需要   所以使用显式注册
     */
    public void registerBattleSettleRoundEvent() {
        if (attackerDamage != null) {
            // 防重入
            return;
        }
        attackerDamage = new HashMap<>();
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSettleRoundEvent, BattleRoleSettleRoundEvent.class);
    }


    private void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        getBattleRole().refreshTroop();
        attackerDamage.clear();
        event.getDamageResult().getSoldierLossSource().forEach((armyId, soldierLossData) -> attackerDamage.put(armyId, soldierLossData.totalLoss()));
        getBattleRole().getRelationKeys().forEach(armyId -> {
                    if (!attackerDamage.containsKey(armyId) && !getBattleRole().relationWith(armyId).isStopped()) {
                        attackerDamage.put(armyId, 0);
                    }
                }
        );
    }

    /**
     * 获取最后一击的玩家部队
     */
    public ArmyEntity getLastHitArmy() {
        if (attackerDamage == null || attackerDamage.isEmpty()) {
            return null;
        }
        List<Long> allAttackArmyIdList = new ArrayList<>(attackerDamage.keySet());
        // 根据造成的伤害，降序排序所有军队
        allAttackArmyIdList.sort(Comparator.comparing(o -> -attackerDamage.get(o)));
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (Long armyId : allAttackArmyIdList) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null && !army.isNpcArmy()) {
                return army;
            }
        }
        return null;
    }

    @Override
    public MonsterEntity getOwner() {
        return (MonsterEntity) super.getOwner();
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    public void onEndSingleRelation(EndSingleBattleEvent event) {
        if (!hasAnyAlive()) {
            if (!getOwner().isDestroy()) {
                getOwner().onDead();
            }
        }
        // 尝试退出围攻
        event.getOther().getOwner().getTransformComponent().exitBesiege(getEntityId());
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        if (MonsterEntity.isSkynetBoss(getOwner().getTemplate())) {
            getOwner().getSkynetComponent().clearBeAttackData();
        }
        if (!battleResult.alive) {
            if (!getOwner().isDestroy()) {
                getOwner().deleteObj();
            }
        }
    }

    @Override
    public void onAddBattleRelation(BattleRelation battleRelation) {
        super.onAddBattleRelation(battleRelation);
        if (MonsterEntity.isSkynetBoss(getOwner().getTemplate())) {
            BattleRole enemyRole = battleRelation.getEnemyRole(getOwner().getEntityId());
            getOwner().getSkynetComponent().onAttackBePlayer(enemyRole.getRoleId());
        }
    }

    @Override
    public boolean beAttacked(ArmyEntity armyEntity, Long attackerPlayerId) {
        return super.beAttacked(armyEntity, attackerPlayerId) && !this.launchAttacker.contains(armyEntity.getEntityId());
    }

    public void addAttacker(long entityId) {
        this.launchAttacker.add(entityId);
    }

    public void abandonAttack(long attackerEntityId, boolean monsterDead) {
        SceneObjEntity attackerEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(attackerEntityId);
        if (attackerEntity == null) {
            return;
        }
        long playerId = attackerEntity.getPlayerId();
        if (playerId <= 0) {
            return;
        }
        launchAttacker.remove(attackerEntityId);
        if (monsterDead && attackerEntity instanceof ArmyEntity) {
            ((ArmyEntity) attackerEntity).getHuntingComponent().onMonsterDead(getEntityId());
        }
    }

    /**
     * 回收
     */
    public void onRecycle() {
        // 回收时，所有发起战斗的玩家返回体力
        getAttackers().forEach(attacker -> {
            if (getOwner().getScene().isMainScene()) {
                SceneObjEntity attackerEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(attacker);
                if (attackerEntity == null) {
                    return;
                }
                long playerId = attackerEntity.getPlayerId();
                if (playerId <= 0) {
                    return;
                }
                if (attackerEntity instanceof ArmyEntity) {
                    ((ArmyEntity) attackerEntity).getHuntingComponent().onMonsterDead(getEntityId());
                }
            }
        });
        launchAttacker.clear();
    }

    public void onMonsterDead() {
        // 发起战斗，但为造成伤害，返回体力
        for (Long attackerId : Sets.newHashSet(launchAttacker)) {
            abandonAttack(attackerId, true);
        }
    }

    public Set<Long> getAttackers() {
        return Sets.newHashSet(launchAttacker);
    }

    private void abandonBeAttack(AbandonBeAttackEvent event) {
        // 大世界地图，返回攻击野怪的体力
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        abandonAttack(event.getEntityId(), false);
    }

    @Override
    public boolean canHurtBack() {
        return !getOwner().getTemplate().getCantCounterAttack();
    }

    @Override
    public boolean canAction() {
        if (getOwner().getTemplate().getBanAttackAction()) {
            return false;
        }
        // 主动攻击目标不为0 或者 没移动中  都可以出手
        return getOwner().getBattleComponent().getBattleProp().getActiveTargetId() != 0 || !getOwner().getMoveComponent().isMoving();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {
        recordAllProp.getSelfSummary().getCardHead()
                .setName(String.valueOf(getOwner().getTemplate().getId()))
                .setPic(getOwner().getTemplate().getId());
    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {
        roleRecord.setCardHead(Struct.PlayerCardHead.newBuilder().setPic(getOwner().getTemplate().getId()).setName(String.valueOf(getOwner().getTemplate().getId())).build());
        Point curPoint = getOwner().getCurPoint();
        roleRecord.setLocation(curPoint.getX(), curPoint.getY(), this.getOwner().getScene().getMapIdForPoint(), this.getOwner().getScene().getMapType());
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        roleRecord.addMember(buildRoleMemberRecord());
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        int templateId = getOwner().getTemplate().getId();
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getEntityId())
                .setPlayerId(getOwner().getPlayerId())
                .setCardHead(Struct.PlayerCardHead.newBuilder().setName(String.valueOf(templateId)).setPic(templateId).build());
        HeroProp mainHeroProp = getBattleRole().getMainHero() != null ? getBattleRole().getMainHero().getHeroProp() : new HeroProp();
        HeroProp deputyHeroProp = getBattleRole().getDeputyHero() != null ? getBattleRole().getDeputyHero().getHeroProp() : new HeroProp();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    @Override
    public long getRoleTypeId() {
        return getOwner().getTemplate().getId();
    }

    @Override
    public Set<Long> getAllEnemyClan() {
        return getBattleRole().getAllEnemyClan();
    }

    @Override
    public Set<Long> getAllEnemyPlayerId() {
        return getBattleRole().getAllEnemyPlayerId();
    }

    /**
     * 获取伤害占比详情
     *
     * @return <sceneObjId, ratio:百分比>
     */
    public Map<Long, Map<Long, Integer>> getKillRatioInfo() {

        StructPlayer.Troop monsterTroop = TroopHelper.getTroopBuilder(ResHolder.getInstance(), getOwner().getTemplate().getTroopIndex());

        Map<Integer, Double> factorMap = getMonsterTroopSoldierFactor(monsterTroop);
        double totalDamage = getMonsterTotalDamage(monsterTroop, factorMap);

        Map<Long, Map<Long, Integer>> res = Maps.newHashMap();
        int totalRatio = 0;
        for (BattleRelation relation : getBattleRole().getRelations()) {
            // 攻击者
            BattleRole attackRole = relation.getOne().getRoleId() == getRoleId() ? relation.getOther() : relation.getOne();
            // role -> 击杀比率
            Map<Long, Integer> killRatioInfo = res.computeIfAbsent(attackRole.getRoleId(), (key) -> new HashMap<>());

            // 攻击者数据报告
            BattleRecord.RoleRecord attackRecord = relation.getContext().getRoleRecord(attackRole.getRoleId());

            for (Map.Entry<Long, Map<Integer, Long>> entry : attackRecord.getMemberKill().entrySet()) {
                // 攻击者成员造成总伤害
                double singleDamage = 0;
                for (Map.Entry<Integer, Long> killEntry : entry.getValue().entrySet()) {
                    double factor = factorMap.getOrDefault(killEntry.getKey(), 0.0);
                    singleDamage += killEntry.getValue() * factor;
                }
                // 攻击者成员伤害占比, 因为四舍五入会有1的误差，策划说，直接把误差丢给最后一个人
                int ratio = Math.min(MathUtils.roundInt(singleDamage / totalDamage * N_100), N_100 - totalRatio);
                // 记录攻击者成员击杀占比
                killRatioInfo.put(entry.getKey(), ratio);
                totalRatio += ratio;
            }
        }

        if (totalRatio > N_100) {
            LOGGER.error("calc damage ratio error. monsterTotalSoldier:{} resInfo:{} totalRatio:{} totalDamage:{} factorMap:{}", monsterTroop, res, totalRatio, totalDamage, factorMap);
        }
        return res;
    }

    /**
     * 获取集结联盟
     */
    public List<Long> getAssembledClans() {
        List<Long> resultClan = new ArrayList<>();
        for (BattleRelation relation : getBattleRole().getRelations()) {
            // 攻击者
            BattleRole attackRole = relation.getOne().getRoleId() == getRoleId() ? relation.getOther() : relation.getOne();

            long clanId = attackRole.getAdapter().getClanId();
            if (attackRole.getType() == SOT_ARMY_GROUP && clanId != 0) {
                resultClan.add(clanId);
            }
        }
        return resultClan;
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }

    /**
     * 获取击杀野怪所需总伤害值
     */
    private double getMonsterTotalDamage(StructPlayer.Troop monsterTroop, Map<Integer, Double> factorMap) {
        long totalDamage = 0;
        for (Map.Entry<Integer, Struct.Soldier> troopSoldierEntry : monsterTroop.getTroop().getDatasMap().entrySet()) {
            totalDamage += troopSoldierEntry.getValue().getNum() * factorMap.getOrDefault(troopSoldierEntry.getKey(), 0.0);
        }
        return totalDamage;
    }

    /**
     * 获取野怪士兵的因子（血量 * 防御）
     *
     * @param monsterTroop
     */
    private Map<Integer, Double> getMonsterTroopSoldierFactor(StructPlayer.Troop monsterTroop) {
        // 部队兵种血量占比
        Map<Integer, Double> factorMap = Maps.newHashMap();
        for (Map.Entry<Integer, Struct.Soldier> entry : monsterTroop.getTroop().getDatasMap().entrySet()) {
            SoldierTypeTemplate soldierTypeTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, entry.getKey());
            double factor = soldierTypeTemplate.getHp() * soldierTypeTemplate.getDef();
            factorMap.put(entry.getKey(), factor);
        }
        return factorMap;
    }

    /**
     * 目前只有战斗召唤物使用
     */
    public void onTick() {
        if (getBattleRole().getSummoningHandler().isShouldDel()) {
            getOwner().deleteObj();
        }
    }

    @Override
    public boolean isMustNtfType() {
        // 精英级别的野怪需要包装为特殊飘字
        return getOwner().getTemplate().getQuality() == CommonEnum.SceneObjQuality.ELITE;
    }

}
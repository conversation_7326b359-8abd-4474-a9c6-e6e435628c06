package com.yorha.cnc.scene.army.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerMisc;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class DungeonArmyBattleComponent extends ArmyBattleComponent {

    public DungeonArmyBattleComponent(ArmyEntity parent) {
        super(parent);
    }

    @Override
    public void afterBattleAction() {
        // 援助就不做后处理了  占领完就进入援助状态    在boss战场内也不用
        if (getOwner().isInAssist()) {
            getOwner().getBehaviourComponent().refreshArmyState();
            return;
        }
        getOwner().getMoveComponent().stopMove();
        getOwner().getStatusComponent().setStaying();
        getOwner().getBehaviourComponent().refreshArmyState();
    }

    @Override
    public Map<Long, SsPlayerMisc.PlunderWeight> getPlunderWeight() {
        return Maps.newHashMap();
    }

    @Override
    public void endAllRelation(BattleResult battleResult) {
        super.endAllRelation(battleResult);
    }

    @Override
    public void handleSoldierLoss(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        super.handleSoldierLoss(lossDetail);
    }

    @Override
    public void sendSevereWound2Hospital(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
        super.sendSevereWound2Hospital(lossDetail);
    }

    @Override
    protected void onEndSingleRelationInBigScene(EndSingleBattleEvent event) {

    }

    @Override
    public void beforeEndSingleRelation(CommonEnum.BattleOverType type, IBattleRoleAdapter other, boolean isEnemyDead, BattleRelation relation) {
        // 处理资源损失
        getOwner().getPlunderComponent().handleArmyResourceLoss(getBattleRole(), relation);
    }
}

package com.yorha.cnc.scene.sceneclan.component;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPathFinding;

import java.util.*;

/**
 * 关卡、州联调、跨州寻路选择
 *
 * <AUTHOR>
 */
public class SceneClanCrossComponent extends AbstractComponent<SceneClanEntity> {
    /**
     * 关卡连通图
     */
    private BoolMap crossingMap;
    /**
     * 州id1，州id2 -> 关卡的片id   寻路时取关隘用的 所以祭坛关隘也在里面
     */
    private Map<Pair<Integer, Integer>, Set<Integer>> crossing;


    public SceneClanCrossComponent(SceneClanEntity owner) {
        super(owner);
    }

    /**
     * 更新关卡连通图
     */
    public void updateCrossingMap(int partId, boolean isOwn) {
        RegionalAreaSettingTemplate template = getOwner().getSceneEntity().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        if (template.getAreaType() != CommonEnum.MapAreaType.CROSSING) {
            return;
        }
        if (crossingMap == null) {
            crossingMap = BoolMap.getInstance(getOwner().getSceneEntity().getRegionNum(), getOwner().getSceneEntity().getRegionNum());
            crossing = new HashMap<>();
        }
        int firstRegion = Math.min(template.getLinkRegionList().get(0), template.getLinkRegionList().get(1));
        int secondRegion = Math.max(template.getLinkRegionList().get(0), template.getLinkRegionList().get(1));
        crossingMap.setGridStatusTwoWay(firstRegion, secondRegion, isOwn);
        Pair<Integer, Integer> key1 = Pair.of(firstRegion, secondRegion);
        Pair<Integer, Integer> key2 = Pair.of(secondRegion, firstRegion);
        if (isOwn) {
            crossing.computeIfAbsent(key1, k -> new HashSet<>()).add(partId);
            crossing.computeIfAbsent(key2, k -> new HashSet<>()).add(partId);
            return;
        }
        crossing.get(key1).remove(partId);
        crossing.get(key2).remove(partId);
    }

    /**
     * 检测两个点是否联通
     */
    public boolean checkSrcToEndCanPass(Point src, Point end) {
        int regionId1 = MapGridDataManager.getRegionId(getOwner().getSceneEntity().getMapId(), src);
        int regionId2 = MapGridDataManager.getRegionId(getOwner().getSceneEntity().getMapId(), end);
        if (regionId1 == regionId2) {
            return true;
        }
        if (crossingMap == null) {
            return false;
        }
        return crossingMap.checkPath(regionId1, regionId2);
    }

    /**
     * 找能连通到这个点的 这个关卡的城门线交点
     *
     * @param partId   关卡片id
     * @param p        起点
     * @param regionId 起点州id
     * @return 找点的站点
     */
    public Point getPointWithSrcAndCross(int partId, Point p, int regionId) {
        if (crossingMap == null) {
            return null;
        }
        MapTemplateDataItem mapTemplateDataItem = getOwner().getSceneEntity().getMapTemplateDataItem();
        RegionalAreaSettingTemplate template = mapTemplateDataItem.getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        List<Integer> path1 = crossingMap.findShortestPath(regionId, template.getLinkRegionList().get(0));
        List<Integer> path2 = crossingMap.findShortestPath(regionId, template.getLinkRegionList().get(1));
        if (path1.isEmpty() && path2.isEmpty()) {
            return null;
        }
        if (path1.isEmpty() || (!path2.isEmpty() && path1.size() > path2.size())) {
            return mapTemplateDataItem.getCrossPointWithLine(p, partId, template.getLink2List());
        }
        return mapTemplateDataItem.getCrossPointWithLine(p, partId, template.getLink1List());
    }

    /**
     * 拷贝异步寻路需要的数据
     */
    public void buildSearchPathAsyncAsk(SceneObjEntity entity, int srcRegion, int endRegion, SsPathFinding.SearchPathAsyncAsk.Builder builder) {
        // 要看关卡所属的 结果没关卡 不用寻路了
        if ((crossing == null || crossing.isEmpty())) {
            throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
        }
        for (Set<Integer> partIdSet : crossing.values()) {
            for (Integer partId : partIdSet) {
                builder.addCross(partId);
            }
        }
    }

    /**
     * 是否可以通关关卡
     */
    public ErrorCode canPassCross(SceneObjEntity sceneObjEntity, int regionId, int partId) {
        return getOwner().getMapBuildingComponent().isOwnPart(partId) ? ErrorCode.OK : ErrorCode.MOVE_CROSS_LOSE;
    }

    public BoolMap getCrossingMap() {
        return crossingMap;
    }

    public Map<Pair<Integer, Integer>, Set<Integer>> getCrossing() {
        return crossing;
    }
}

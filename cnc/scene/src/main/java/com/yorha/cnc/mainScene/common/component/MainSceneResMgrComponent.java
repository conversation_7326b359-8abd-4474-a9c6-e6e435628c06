package com.yorha.cnc.mainScene.common.component;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.ResMgrComponent;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.resource.ResCollectService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstCollectTemplate;
import res.template.RegionResourceTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 资源田管理
 *
 * <AUTHOR>
 */
public class MainSceneResMgrComponent extends ResMgrComponent {
    private static final Logger LOGGER = LogManager.getLogger(MainSceneResMgrComponent.class);
    /**
     * 资源田占用地标
     */
    private final boolean[][] grids = new boolean[BigSceneConstants.GRID_NUM][BigSceneConstants.GRID_NUM];
    /**
     * 资源田刷新州级管理器
     */
    private final Map<Integer, MainSceneResRegionItem> resMgrItemMap = new HashMap<>();

    public MainSceneResMgrComponent(SceneEntity owner) {
        super(owner);
        Map<Integer, RegionResourceTemplate> map = ResHolder.getResService(ResCollectService.class).getRegionResourceTemplateMap(owner.getStoryId());
        if (map == null) {
            return;
        }
        int regionNum = getOwner().getRegionNum();
        for (int i = 0; i < regionNum; i++) {
            // 没有配置的不刷了
            if (!map.containsKey(i)) {
                continue;
            }
            resMgrItemMap.put(i, new MainSceneResRegionItem(i, getOwner()));
        }
    }

    public MainSceneResRegionItem getResRegionItem(int regionId) {
        return resMgrItemMap.get(regionId);
    }

    @Override
    public void postInit() {
        if (resMgrItemMap.isEmpty()) {
            return;
        }
        // 场景启动 刷新
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            item.globalRefresh(true);
            item.goldGlobalRefresh(true);
        }
        // 全局刷新定时器
        long nextDayDurMsWithNow = TimeUtils.getNextDayDurMs(SystemClock.now());
        getOwner().getTimerComponent().addFixRepeatTimer(getEntityId(), TimerReasonType.RES_BUILDING_GLOBAL_REFRESH,
                this::globalRefresh, nextDayDurMsWithNow, TimeUnit.DAYS.toMillis(1), TimeUnit.MILLISECONDS);
        // 黄金矿刷新定时器
        int refreshInterval = ResHolder.getConsts(ConstCollectTemplate.class).getGoldRefreshHour();
        long nextGoldRefreshTime = TimeUnit.HOURS.toMillis(refreshInterval);
        getOwner().getTimerComponent().addFixRepeatTimer(getEntityId(), TimerReasonType.GOLD_GLOBAL_REFRESH,
                this::goldRefresh, nextGoldRefreshTime, nextGoldRefreshTime, TimeUnit.MILLISECONDS);

        // dev环境输出资源点刷新情况报表
        if (ServerContext.isDevEnv()) {
            getOwner().getTimerComponent().addTimer(TimerReasonType.BIG_SCENE_DUMP, this::dumpAllResource, 120, TimeUnit.SECONDS);
        }
    }

    /**
     * 打印地图上的资源、金矿等信息
     */
    public void showLog() {
        for (MainSceneResRegionItem resRegionItem : resMgrItemMap.values()) {
            LOGGER.info("BigSceneResMgrComponent buildingNum:{} goldNum:{}", resRegionItem.getBuildingNum(), resRegionItem.getGoldNum());
        }
    }

    /**
     * 全局刷新
     */
    public void globalRefresh() {
        LOGGER.info("{} BigSceneResMgrComponent global refresh start", getOwner());
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            item.globalRefresh(false);
        }
    }

    /**
     * 黄金矿全局刷新
     */
    public void goldRefresh() {
        LOGGER.info("Gold-Log : {} BigSceneResMgrComponent gold refresh start", getOwner());
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            item.goldGlobalRefresh(false);
        }
    }

    public void addResBuilding(ResBuildingEntity entity) {
        Point curPoint = entity.getCurPoint();
        occupyResourceGrid(curPoint);
        int regionId = MapGridDataManager.getRegionId(getOwner().getMapId(), curPoint);
        MainSceneResRegionItem mgrItem = getResRegionItem(regionId);
        if (mgrItem == null) {
            LOGGER.error("removeResBuilding but regionItem not exist {} {}", regionId, entity);
            return;
        }
        mgrItem.onAddResBuilding(entity);
    }

    @Override
    public void removeResBuilding(ResBuildingEntity entity) {
        Point curPoint = entity.getCurPoint();
        releaseResourceGrid(curPoint);
        int regionId = MapGridDataManager.getRegionId(getOwner().getMapId(), curPoint);
        MainSceneResRegionItem mgrItem = getResRegionItem(regionId);
        if (mgrItem == null) {
            LOGGER.error("removeResBuilding but regionItem not exist {} {}", regionId, entity);
            return;
        }
        mgrItem.onRemoveResBuilding(entity);
    }

    public void occupyResourceGrid(Point curPoint) {
        int i = curPoint.getX() / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        int j = curPoint.getY() / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        grids[i][j] = true;
    }

    public void releaseResourceGrid(Point curPoint) {
        int i = curPoint.getX() / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        int j = curPoint.getY() / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        grids[i][j] = false;
    }

    public boolean checkResourceGrid(int gridId) {
        int i = (gridId & BigSceneConstants.MAP_GRID_X) >> BigSceneConstants.MAP_GRID_Y_DIGIT_NUM;
        int j = gridId & BigSceneConstants.MAP_GRID_Y;
        return grids[i][j];
    }

    /**
     * 地块联盟变化，更新资源田联盟id
     */
    public void refreshResBuildingClan(int partId, SceneClanEntity sceneClan) {
        RegionalAreaSettingTemplate template = getOwner().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        MainSceneResRegionItem mgrItem = getResRegionItem(template.getRegionId());
        if (mgrItem == null) {
            LOGGER.error("refreshResBuildingClan but regionItem not exist {}", template.getRegionId());
            return;
        }
        mgrItem.refreshResBuildingClan(partId, sceneClan);
    }

    @Override
    public void onDestroy() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.RES_BUILDING_GLOBAL_REFRESH);
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.GOLD_GLOBAL_REFRESH);
    }

    public String getCurGoldNum() {
        Map<Integer, String> goldMap = new HashMap<>();
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            goldMap.put(item.getRegionId(), item.getGoldLevel2Num().toString());
        }
        String goldInfo = JsonUtils.toJsonString(goldMap);
        LOGGER.info("Gold-Log : print gold info = {}", goldInfo);
        return goldInfo;
    }

    private void dumpAllResource() {
        StringBuilder sb = new StringBuilder();
        sb.append("========================= show all resource in big scene by zeo ==============================").append("\n");
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            sb.append(item).append(item.getRegionId()).append(" :").append(item.getBuildingNum());
        }
        sb.append("\n");
        sb.append("================================= show detail by zeo =========================================").append("\n");
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            item.dumpRegionResource(sb);
        }
        LOGGER.debug(sb);
        dumpAllGoldResource();
    }

    private void dumpAllGoldResource() {
        StringBuilder sb = new StringBuilder();
        sb.append("========================= show all gold resource in big scene by zeo ==============================").append("\n");
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            sb.append(item).append(item.getRegionId()).append(" :").append(item.getGoldNum());
        }
        sb.append("\n");
        sb.append("================================= show detail by zeo =========================================").append("\n");
        for (MainSceneResRegionItem item : resMgrItemMap.values()) {
            item.dumpRegionGoldResource(sb);
        }
        LOGGER.debug(sb);
    }

}

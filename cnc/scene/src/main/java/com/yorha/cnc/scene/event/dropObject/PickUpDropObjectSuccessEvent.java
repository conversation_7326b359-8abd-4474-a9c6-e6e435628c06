package com.yorha.cnc.scene.event.dropObject;

import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class PickUpDropObjectSuccessEvent extends IEvent {
    private final long dropObjectId;
    private final long armyId;

    public PickUpDropObjectSuccessEvent(final long dropObjectEntityId, final long armyEntityId) {
        this.dropObjectId = dropObjectEntityId;
        this.armyId = armyEntityId;
    }

    public long getDropObjectId() {
        return dropObjectId;
    }

    public long getArmyId() {
        return armyId;
    }
}

package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * 立刻完成占领
 *
 * <AUTHOR>
 */
public class CompleteOccupy implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        long mapBuildingId = Long.parseLong(args.get("id"));
        ObjMgrComponent objMgrComponent = actor.getScene().getObjMgrComponent();
        MapBuildingEntity obj = objMgrComponent.getSceneObjWithTypeWithException(MapBuildingEntity.class, mapBuildingId);
        obj.getStageMgrComponent().gmCompleteOccupy();
    }

    @Override
    public String showHelp() {
        return "CompleteOccupy id={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAPBUILDING;
    }
}

package com.yorha.cnc.scene.sceneObj.hateList;

import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.AiParams;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 默认仇恨列表，支持锁定仇恨，支持固定仇恨
 *
 * <AUTHOR>
 */
public class DefaultHateList extends AbstractHateList {
    private static final Logger LOGGER = LogManager.getLogger(DefaultHateList.class);
    private final Map<Long, HateInfo> hateMap;
    private long mostHateEntity;

    private long lockHateTarget;

    public DefaultHateList(SceneObjEntity owner) {
        super(owner);
        this.hateMap = new HashMap<>();
    }

    @Override
    public long getMostHateEntity() {
        checkLockHateAndChange();
        return mostHateEntity;
    }

    private void checkLockHateAndChange() {
        if (lockHateTarget == 0 || lockHateTarget == mostHateEntity) {
            return;
        }
        //  判一下距离够不够
        SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(lockHateTarget);
        if (sceneObj == null) {
            lockHateTarget = 0;
        } else if (!getOwner().getAiComponent().outChaseRange(sceneObj.getCurPoint())) {
            this.mostHateEntity = lockHateTarget;
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
        }
    }

    @Override
    public Collection<Long> getHateEntities() {
        return Collections.unmodifiableSet(hateMap.keySet());
    }

    @Override
    public long getHate(long entityId) {
        return hateMap.containsKey(entityId) ? hateMap.get(entityId).getHate() : 0L;
    }

    @Override
    public HateInfo getHateInfo(long entityId) {
        return hateMap.get(entityId);
    }

    @Override
    public void addHate(long entityId, long hate) {
        addHate(entityId, hate, true);
    }

    private void addHate(long entityId, long hate, boolean canRemove) {
        if (getOwner().isDestroy()) {
            return;
        }
        if (hateMap.isEmpty()) {
            getOwner().tryRemoveTick(SceneTickReason.TICK_HATE);
            getOwner().addTick(SceneTickReason.TICK_HATE);
        }
        long totalHate = hateMap.computeIfAbsent(entityId, k -> new HateInfo()).addHate(hate, canRemove);
        // 有锁定仇恨的目标
        if (lockHateTarget > 0 && mostHateEntity == lockHateTarget) {
            return;
        }
        // 首次拥有仇恨目标对象
        if (mostHateEntity == 0) {
            this.mostHateEntity = entityId;
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
            return;
        }
        // 仇恨值最高的对象变化
        if (entityId != mostHateEntity && totalHate > getHate(mostHateEntity)) {
            this.mostHateEntity = entityId;
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
        }
    }

    @Override
    public void addFixHate(long entityId, long hate) {
        addHate(entityId, hate, false);
    }

    @Override
    public void clearHate(long entityId) {
        HateInfo hateInfo = hateMap.get(entityId);
        if (hateInfo == null) {
            return;
        }
        if (!hateInfo.isCanRemove()) {
            return;
        }
        hateMap.remove(entityId);
        if (entityId == mostHateEntity) {
            this.mostHateEntity = 0L;
            changeMostHateEntity(hateMap);
        }
    }

    @Override
    public void setLockHateTarget(long lockHateTarget) {
        if (lockHateTarget != 0) {
            int chaseRange = getOwner().getAiComponent().getAiParams().getOrDefault(AiParams.AP_CHASE_RANGE, 0);
            if (chaseRange == 0) {
                LOGGER.error("{} want to lock hate but ai not supported", getOwner());
                return;
            }
            this.lockHateTarget = lockHateTarget;
            getOwner().getAiComponent().triggerEvent(AIEvent.FIND_ENEMY);
        } else {
            if (this.lockHateTarget != 0) {
                // 如果当前目标就是锁定目标 要change下
                if (mostHateEntity == this.lockHateTarget) {
                    this.mostHateEntity = 0L;
                    this.lockHateTarget = 0;
                    changeMostHateEntity(hateMap);
                    return;
                }
                this.lockHateTarget = 0;
            }
        }
    }

    @Override
    public void clear() {
        this.mostHateEntity = 0L;
        Iterator<Map.Entry<Long, HateInfo>> iterator = hateMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, HateInfo> entry = iterator.next();
            if (!entry.getValue().isCanRemove()) {
                continue;
            }
            iterator.remove();
        }
        if (this.lockHateTarget != 0) {
            setLockHateTarget(lockHateTarget);
        }
    }

    @Override
    public void onTick() {
        // 仇恨值过期
        if (hateMap.isEmpty()) {
            getOwner().removeTick(SceneTickReason.TICK_HATE);
            return;
        }
        Set<Long> expireSet = new HashSet<>();
        hateMap.forEach((id, hateInfo) -> {
            if (!hateInfo.isCanRemove()) {
                return;
            }
            if (SystemClock.now() >= TimeUnit.SECONDS.toMillis(getHateExpire()) + hateInfo.getLastHateTime()) {
                expireSet.add(id);
            }
        });
        if (CollectionUtils.isNotEmpty(expireSet)) {
            // 清除过期仇恨
            expireSet.forEach(hateMap::remove);
            // 重新计算仇恨值最高的玩家
            if (expireSet.contains(mostHateEntity)) {
                mostHateEntity = 0L;
                changeMostHateEntity(hateMap);
            }
        }
    }

    private void changeMostHateEntity(Map<Long, HateInfo> hateMap) {
        if (lockHateTarget != 0) {
            SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(lockHateTarget);
            if (sceneObj == null) {
                lockHateTarget = 0;
            } else if (!getOwner().getAiComponent().outChaseRange(sceneObj.getCurPoint())) {
                this.mostHateEntity = lockHateTarget;
                return;
            }
        }
        if (MapUtils.isEmpty(hateMap)) {
            return;
        }
        hateMap.entrySet().stream()
                .max(Comparator.comparingLong(o -> o.getValue().getHate()))
                .ifPresent(entry -> this.mostHateEntity = entry.getKey());
    }


}

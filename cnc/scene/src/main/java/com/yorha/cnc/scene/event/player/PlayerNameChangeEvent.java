package com.yorha.cnc.scene.event.player;

import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;

/**
 * <AUTHOR>
 */
public class PlayerNameChangeEvent extends IEventWithEntityId {
    private final String newName;

    public PlayerNameChangeEvent(long entityId, String newName) {
        super(entityId);
        this.newName = newName;
    }

    public String getNewName() {
        return newName;
    }
}

package com.yorha.cnc.scene.logisticsPlane;


import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.logisticsPlane.component.LogisticsPlaneBehaviourComponent;
import com.yorha.cnc.scene.logisticsPlane.component.LogisticsPlaneMoveComponent;
import com.yorha.cnc.scene.logisticsPlane.component.LogisticsPlaneTransformComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjBuilder;
import com.yorha.game.gen.prop.LogisticsPlaneProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.ScenePlayerLogisticsStatusProp;

/**
 * <AUTHOR>
 */
public class LogisticsPlaneBuilder extends SceneObjBuilder<LogisticsPlaneEntity, LogisticsPlaneProp> {

    public LogisticsPlaneBuilder(SceneEntity sceneEntity, long eid, LogisticsPlaneProp prop) {
        super(sceneEntity, eid, prop);
    }

    @Override
    public PointProp getPointProp() {
        return getProp().getMove().getCurPoint();
    }

    @Override
    public LogisticsPlaneTransformComponent transformComponent(LogisticsPlaneEntity owner) {
        return new LogisticsPlaneTransformComponent(owner, getPointProp());
    }

    public LogisticsPlaneMoveComponent moveComponent(LogisticsPlaneEntity owner) {
        return new LogisticsPlaneMoveComponent(owner);
    }

    public LogisticsPlaneBehaviourComponent behaviourComponent(LogisticsPlaneEntity owner) {
        return new LogisticsPlaneBehaviourComponent(owner);
    }
}
package com.yorha.cnc.scene.spyPlane.component;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjTransformComponent;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstSpyService;
import com.yorha.game.gen.prop.PointProp;

/**
 * 描述侦察机位置的组件
 *
 * <AUTHOR>
 */
public class SpyPlaneTransformComponent extends SceneObjTransformComponent {

    public SpyPlaneTransformComponent(SceneObjEntity owner, PointProp pointData) {
        super(owner, pointData);
    }

    @Override
    public void init() {
        super.init();
    }


    @Override
    public SpyPlaneEntity getOwner() {
        return (SpyPlaneEntity) super.getOwner();
    }


    @Override
    public void resetModelRadius() {
        setModelRadius(ResHolder.getResService(ConstSpyService.class).getTemplate().getSpyPlaneRadius());
    }
}

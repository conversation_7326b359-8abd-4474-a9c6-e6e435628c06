package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.proto.CommonEnum;

/**
 * 全服玩家的基地评级
 * 参数：要求人数_基地评级等级
 */
public class AllPlayerCityLevelMileStoneHandler extends AbstractMileStoneHandler {


    @Override
    public void updateProcess(MileStoneTaskData data) {
        if (data instanceof PlayerCityEraData) {
            PlayerCityEraData eraData = (PlayerCityEraData) data;
            String[] taskParam = getTaskParamById();
            int playerNum = Integer.parseInt(taskParam[0]);
            int levelLimit = Integer.parseInt(taskParam[1]);
            if (eraData.getOldEraLevel() < levelLimit && eraData.getNewEraLevel() >= levelLimit) {
                long curProcess = Math.min(playerNum, getProp().getProcess() + data.getValue());
                getProp().setProcess(curProcess);
            }
        }
    }

    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_ALL_PLAYER;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_BASE_RATING;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_FINISH_OR_TIME_END;
    }


    public static class PlayerCityEraData extends MileStoneTaskData {
        private final int oldEraLevel;
        private final int newEraLevel;

        public int getOldEraLevel() {
            return oldEraLevel;
        }

        public int getNewEraLevel() {
            return newEraLevel;
        }

        /**
         * @param oldEraLevel 旧的时代等级
         * @param newEraLevel 新的时代等级
         * @param value       进度计数
         */
        public PlayerCityEraData(int oldEraLevel, int newEraLevel, int value) {
            super(value);
            this.oldEraLevel = oldEraLevel;
            this.newEraLevel = newEraLevel;
        }
    }
}

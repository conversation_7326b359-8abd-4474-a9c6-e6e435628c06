package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.actor.IActorWithTimer;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.utils.time.schedule.ActorTimerControllerForSceneActor;
import com.yorha.common.utils.time.schedule.ActorTimerInterface;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

/**
 * zone 定时器组件
 *
 * <AUTHOR>
 */
public class ZoneTimerComponent extends AbstractComponent<ZoneEntity> implements ActorTimerInterface, IActorWithTimer {
    public ZoneTimerComponent(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public SceneActor ownerActor() {
        return (SceneActor) super.ownerActor();
    }

    @Nullable
    @Override
    public ActorTimer addTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return addTimer(String.valueOf(prefix), timerReasonType, runnable, initialDelay, unit);
    }

    @Nullable
    @Override
    public ActorTimer addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return ownerActor().dangerousAddTimer(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    private final ActorTimerControllerForSceneActor controller = new ActorTimerControllerForSceneActor(getOwner(), ownerActor());

    @Override
    public void onDestroy() {
        super.onDestroy();
        controller.onDestroy();
    }

    @Override
    public void addTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        controller.addTimer(timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    public void addTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        controller.addTimerWithPrefix(prefix, timerReasonType, runnable, initialDelay, unit);
    }

    @Override
    public void addRepeatTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        controller.addRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit);
    }

    @Override
    public void addRepeatTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        controller.addRepeatTimerWithPrefix(prefix, timerReasonType, runnable, initialDelay, period, unit);
    }

    @Override
    public void addFixRepeatTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        controller.addFixRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit);
    }

    @Override
    public void addFixRepeatTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        controller.addFixRepeatTimerWithPrefix(prefix, timerReasonType, runnable, initialDelay, period, unit);
    }

    @Override
    public void cancelTimer(TimerReasonType timerReasonType) {
        controller.cancelTimer(timerReasonType);
    }

    @Override
    public void cancelTimer(TimerReasonType timerReasonType, String prefix) {
        controller.cancelTimer(timerReasonType, prefix);
    }

    @Override
    public boolean containTimer(TimerReasonType timerReasonType, String prefix) {
        return controller.containTimer(timerReasonType, prefix);
    }
}

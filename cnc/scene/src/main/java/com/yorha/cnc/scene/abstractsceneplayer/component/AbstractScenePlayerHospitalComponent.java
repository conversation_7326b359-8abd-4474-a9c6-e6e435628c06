package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.abstractsceneplayer.soldier.HospitalSoldierHandleResult;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.lock.SpinLock;
import com.yorha.common.qlog.json.Army.ArmyConfig;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.Int32PlayerHospitalSoldierMapProp;
import com.yorha.game.gen.prop.PlayerHospitalSoldierProp;
import com.yorha.game.gen.prop.ScenePlayerHospitalExclusiveRegionProp;
import com.yorha.game.gen.prop.ScenePlayerHospitalModelProp;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 医院的数据放在scenePlayer上，因为scene上的读写操作比较多，而且scene上每回合战斗死兵数据依赖医院数据
 */
public abstract class AbstractScenePlayerHospitalComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private final SpinLock spinLock = new SpinLock();

    /**
     * 兵种价值比较，Integer是soldierId，目前用于医院里容量满的时候，价值高的优先进医院
     */
    public static final Comparator<Integer> HOSPITAL_SOLDIER_VALUE_COMPARATOR = (o1, o2) -> {
        if (o1.equals(o2)) {
            return 0;
        }
        SoldierTypeTemplate template1 = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, o1);
        SoldierTypeTemplate template2 = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, o2);

        return Integer.compare(template1.getDiePriority(), template2.getDiePriority());
    };
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerHospitalComponent.class);
    /**
     * 比较哪个兵种优先进医院，所以是倒序的
     */
    private static final Comparator<Struct.PlayerHospitalSoldier> COMPARATOR = (o1, o2) -> HOSPITAL_SOLDIER_VALUE_COMPARATOR.compare(o2.getSoldierId(), o1.getSoldierId());


    /**
     * 一次战斗中所有进医院的士兵
     */
    protected final Map<Integer, Integer> soldierInHospitalDuringBattle = Maps.newHashMap();

    public AbstractScenePlayerHospitalComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    public static int sumProto(Collection<Struct.PlayerHospitalSoldier> soldierMap) {
        return soldierMap.stream().mapToInt(Struct.PlayerHospitalSoldier::getSevereNum).sum();
    }

    public static int sum(Collection<PlayerHospitalSoldierProp> soldierMap) {
        return soldierMap.stream().mapToInt(PlayerHospitalSoldierProp::getSevereNum).sum();
    }

    public static void mergePlusByProp(Int32PlayerHospitalSoldierMapProp to, Collection<PlayerHospitalSoldierProp> plus) {
        for (PlayerHospitalSoldierProp fromItem : plus) {
            PlayerHospitalSoldierProp toItem = to.computeIfAbsent(fromItem.getSoldierId(), sid -> new PlayerHospitalSoldierProp().setSoldierId(sid));
            toItem.setSevereNum(toItem.getSevereNum() + fromItem.getSevereNum());
        }
    }

    public static void mergePlusByPb(Int32PlayerHospitalSoldierMapProp to, Collection<StructPB.PlayerHospitalSoldierPB> plus) {
        for (StructPB.PlayerHospitalSoldierPB fromItem : plus) {
            PlayerHospitalSoldierProp toItem = to.computeIfAbsent(fromItem.getSoldierId(), sid -> new PlayerHospitalSoldierProp().setSoldierId(sid));
            toItem.setSevereNum(toItem.getSevereNum() + fromItem.getSevereNum());
        }
    }

    public static void mergeMinusByProp(Int32PlayerHospitalSoldierMapProp to, Collection<PlayerHospitalSoldierProp> plus) {
        for (PlayerHospitalSoldierProp fromItem : plus) {
            PlayerHospitalSoldierProp toItem = to.get(fromItem.getSoldierId());
            if (toItem != null) {
                toItem.setSevereNum(Math.max(0, toItem.getSevereNum() - fromItem.getSevereNum()));
                if (toItem.getSevereNum() <= 0) {
                    to.remove(fromItem.getSoldierId());
                }
            }
        }
    }

    public static void mergeMinusByPb(Int32PlayerHospitalSoldierMapProp to, Collection<StructPB.PlayerHospitalSoldierPB> plus) {
        for (StructPB.PlayerHospitalSoldierPB fromItem : plus) {
            PlayerHospitalSoldierProp toItem = to.get(fromItem.getSoldierId());
            if (toItem != null) {
                toItem.setSevereNum(Math.max(0, toItem.getSevereNum() - fromItem.getSevereNum()));
                if (toItem.getSevereNum() <= 0) {
                    to.remove(fromItem.getSoldierId());
                }
            }
        }
    }

    abstract protected ScenePlayerHospitalModelProp prop();

    public HospitalSoldierHandleResult onSettleRound(List<Struct.PlayerHospitalSoldier> severeWoundSoldiers) {
        HospitalSoldierHandleResult result = new HospitalSoldierHandleResult();
        spinLock.lock();
        try {
            LOGGER.debug("hospital onSettleRound: {}", severeWoundSoldiers);
            result = handleSevereSoldiers(severeWoundSoldiers);
            LOGGER.debug("hospital onSettleRound result: {}", result);
            for (HospitalSoldierHandleResult.Unit unit : result.getUnits()) {
                soldierInHospitalDuringBattle.put(unit.getSoldierId(), soldierInHospitalDuringBattle.getOrDefault(unit.getSoldierId(), 0) + unit.getWaitingCure());
            }
        } finally {
            spinLock.unlock();
        }
        return result;
    }

    public void onEndAllRelation() {
        soldierInHospitalDuringBattle.clear();
    }

    protected int getHospitalCapacity() {
        return SceneAddCalc.getHospitalCapacity(getOwner());
    }


    /**
     * 有新的重伤兵送进医院
     *
     * @return 重伤不治的单位数量
     */
    protected HospitalSoldierHandleResult handleSevereSoldiers(List<Struct.PlayerHospitalSoldier> severeWoundSoldiers) {
        HospitalSoldierHandleResult result = new HospitalSoldierHandleResult();

        ScenePlayerHospitalModelProp prop = prop();
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);

        // 大世界才处理专属容量
        if (this.getOwner().getScene().isMainScene()) {
            // 先处理专属容量的鬼兵种
            final int exclusiveSoldierId = constTemplate.getHospitalExclusiveSoldierId();

            List<Struct.PlayerHospitalSoldier> exclusiveSoldiers = severeWoundSoldiers.stream()
                    .filter(it -> it.getSoldierId() == exclusiveSoldierId).collect(Collectors.toList());
            for (Struct.PlayerHospitalSoldier es : exclusiveSoldiers) {
                if (es.getSoldierId() == exclusiveSoldierId && es.getSevereNum() > 0) {
                    // 尝试将专属兵种放入专区
                    final int exclusiveCapacity = constTemplate.getHospitalExclusiveSoldierCapacity();

                    ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = prop.getExclusiveRegion();
                    if (exclusiveRegion.getSoldierId() <= 0) {
                        exclusiveRegion.setSoldierId(exclusiveSoldierId);
                    }
                    if (exclusiveRegion.getSoldierId() != exclusiveSoldierId) {
                        WechatLog.error("hospital exclusive soldierId changed!! {} {}", exclusiveRegion.getSoldierId(), exclusiveSoldierId);
                    } else {
                        final int alreadyInHospital = exclusiveRegion.getWaitingSoldiers() + exclusiveRegion.getInTreatmentSoldiers() + exclusiveRegion.getTreatOverSoldiers();
                        final int canAdd = Math.max(0, exclusiveCapacity - alreadyInHospital);
                        final int realAdd = Math.min(es.getSevereNum(), canAdd);
                        exclusiveRegion.setWaitingSoldiers(exclusiveRegion.getWaitingSoldiers() + realAdd);
                        // 专属容量不够，不代表就死定了，还要进普通容量里算一下的
                        result.addUnit(exclusiveSoldierId, realAdd, 0);

                        severeWoundSoldiers.remove(es);
                        if (es.getSevereNum() > realAdd) {
                            LOGGER.debug("exclusiveRegionFull {} {}", es.getSevereNum(), realAdd);
                            severeWoundSoldiers.add(es.toBuilder().setSevereNum(es.getSevereNum() - realAdd).build());
                        }
                    }
                }
            }
        }


        final int totalSevereCount = sumProto(severeWoundSoldiers);
        int capacity = this.getHospitalCapacity();

        if (capacity <= 0) {
            LOGGER.debug("hospital no capacity cause dead:{} - {}", getOwner(), totalSevereCount);
            return result.withAllDead(severeWoundSoldiers);
        }
        int canAddCount = capacity - inHospitalSoldierCount();
        if (canAddCount <= 0) {
            LOGGER.debug("hospital full cause dead:{} - {}", getOwner(), totalSevereCount);
            return result.withAllDead(severeWoundSoldiers);
        }
        int inHospitalPer100Before = inHospitalSoldierCount() * Constants.N_100 / capacity;

        severeWoundSoldiers.sort(COMPARATOR);
        Int32PlayerHospitalSoldierMapProp waitingSoldiers = prop.getWaitingSoldiers();

        // 记录真实进入医院的士兵数据
        for (Struct.PlayerHospitalSoldier fromItem : severeWoundSoldiers) {
            if (canAddCount > 0) {
                PlayerHospitalSoldierProp toItem = waitingSoldiers.computeIfAbsent(fromItem.getSoldierId(), sid -> new PlayerHospitalSoldierProp().setSoldierId(sid));
                int realAdd = Math.min(canAddCount, fromItem.getSevereNum());
                toItem.setSevereNum(toItem.getSevereNum() + realAdd);
                canAddCount -= realAdd;

                result.addUnit(fromItem.getSoldierId(), realAdd, fromItem.getSevereNum() - realAdd);
            } else {
                result.addUnit(fromItem.getSoldierId(), 0, fromItem.getSevereNum());
            }
        }
        int warningPer100 = constTemplate.getHospitalFirstWarningPer100();
        int inHospitalPer100After = inHospitalSoldierCount() * Constants.N_100 / capacity;
        if (inHospitalPer100Before < warningPer100 && inHospitalPer100After >= warningPer100) {
            send80PerWarningMail();
        }
        if (LOGGER.isDebugEnabled() && result.totalDeadNum() > 0) {
            LOGGER.debug("hospital capacity not enough cause dead:{} - {}", getOwner(), result.totalDeadNum());
        }
        return result;
    }

    protected void send80PerWarningMail() {
        LOGGER.debug("{} hospital send80PerWarningMail", getOwner());
        int mailId = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getHospitalFirstReach80PercentMailId();
        StructMail.MailSendParams mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId)
                .build();

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder().
                        setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getZoneId())
                        .build(),
                mailSendParams);
    }

    protected int inHospitalSoldierCount() {
        final ScenePlayerHospitalModelProp prop = prop();
        return sum(prop.getWaitingSoldiers().values())
                + sum(prop.getInTreatmentSoldiers().values())
                + sum(prop.getTreatOverSoldiers().values());
    }

    /**
     * 校验治疗兵力数量合法
     *
     * @param treatUnion
     */
    protected void assertTreatSoldiersCount(CommonMsg.HospitalTreatUnion treatUnion) {
        ScenePlayerHospitalModelProp prop = prop();
        // 兵力数字合法
        for (StructPB.PlayerHospitalSoldierPB item : treatUnion.getToTreatSoldiers().getDatasMap().values()) {
            PlayerHospitalSoldierProp waitingItem = prop.getWaitingSoldiers().get(item.getSoldierId());
            if (waitingItem == null || waitingItem.getSevereNum() < item.getSevereNum()) {
                throw new GeminiException(ErrorCode.TREAT_SOLDIER_NOT_ENOUGH);
            }
        }
        if (prop.getExclusiveRegion().getWaitingSoldiers() < treatUnion.getToTreatExclusive().getSevereNum()) {
            throw new GeminiException(ErrorCode.TREAT_SOLDIER_NOT_ENOUGH);
        }
    }


    protected void tryMinusExclusiveRegionWaiting(CommonMsg.HospitalTreatUnion treatUnion) {
        if (treatUnion.hasToTreatExclusive()) {
            StructPB.PlayerHospitalSoldierPB toTreatExclusive = treatUnion.getToTreatExclusive();
            ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = prop().getExclusiveRegion();
            exclusiveRegion.setWaitingSoldiers(Math.max(0, exclusiveRegion.getWaitingSoldiers() - toTreatExclusive.getSevereNum()));
        }
    }

    protected void tryPlusExclusiveRegionInTreatment(CommonMsg.HospitalTreatUnion treatUnion) {
        if (treatUnion.hasToTreatExclusive()) {
            StructPB.PlayerHospitalSoldierPB toTreatExclusive = treatUnion.getToTreatExclusive();
            ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = prop().getExclusiveRegion();
            exclusiveRegion.setInTreatmentSoldiers(exclusiveRegion.getInTreatmentSoldiers() + toTreatExclusive.getSevereNum());
        }
    }

    protected Collection<ArmyConfig> getAllSoldierInHospital() {
        Map<Integer, ArmyConfig> allSoldierInHospital = Maps.newHashMap();
        // 待治疗
        for (PlayerHospitalSoldierProp value : prop().getWaitingSoldiers().values()) {
            if (value.getSevereNum() == 0) {
                continue;
            }
            ArmyConfig armyConfig = allSoldierInHospital.get(value.getSoldierId());
            if (armyConfig == null) {
                armyConfig = new ArmyConfig(value.getSoldierId(), value.getSevereNum());
            } else {
                armyConfig.setICount(armyConfig.getICount() + value.getSevereNum());
            }
            allSoldierInHospital.put(value.getSoldierId(), armyConfig);
        }
        // 治疗中
        for (PlayerHospitalSoldierProp value : prop().getInTreatmentSoldiers().values()) {
            if (value.getSevereNum() == 0) {
                continue;
            }
            ArmyConfig armyConfig = allSoldierInHospital.get(value.getSoldierId());
            if (armyConfig == null) {
                armyConfig = new ArmyConfig(value.getSoldierId(), value.getSevereNum());
            } else {
                armyConfig.setICount(armyConfig.getICount() + value.getSevereNum());
            }
            allSoldierInHospital.put(value.getSoldierId(), armyConfig);
        }
        //专区
        ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = prop().getExclusiveRegion();
        int exclusiveRegionSum = exclusiveRegion.getWaitingSoldiers() + exclusiveRegion.getInTreatmentSoldiers();
        if (exclusiveRegionSum != 0) {
            ArmyConfig armyConfig = allSoldierInHospital.get(exclusiveRegion.getSoldierId());
            if (armyConfig == null) {
                armyConfig = new ArmyConfig(exclusiveRegion.getSoldierId(), exclusiveRegionSum);
            } else {
                armyConfig.setICount(armyConfig.getICount() + exclusiveRegionSum);
            }
            allSoldierInHospital.put(exclusiveRegion.getSoldierId(), armyConfig);
        }
        return allSoldierInHospital.values();
    }
}

package com.yorha.cnc.scene;

import com.google.protobuf.Message;
import com.yorha.cnc.scene.army.ArmyFactory;
import com.yorha.cnc.scene.city.CityFactory;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingFactory;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneFactory;
import com.yorha.cnc.scene.mapBuilding.MapBuildingFactory;
import com.yorha.cnc.scene.resBuilding.ResBuildingFactory;
import com.yorha.cnc.scene.spyPlane.SpyPlaneFactory;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.option.DeleteOption;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class SceneLoadHelper {
    private static final Logger LOGGER = LogManager.getLogger(SceneLoadHelper.class);

    /**
     * 通过db的属性恢复场景物体
     */
    public static void createSceneObjFromDbAttr(SceneEntity sceneEntity, final EntityAttrDb.EntityAttrDB fullAttr, final EntityAttrDb.EntityAttrDB changeAttr) {
        switch (fullAttr.getEntityType()) {
            case ET_City:
                CityFactory.restoreCity(sceneEntity, fullAttr, changeAttr);
                break;
            case ET_Army:
                ArmyFactory.restoreArmy(sceneEntity, fullAttr, changeAttr);
                break;
            case ET_MapBuilding:
                MapBuildingFactory.restoreMapBuilding(sceneEntity, fullAttr, changeAttr);
                break;
            case ET_SpyPlane:
                SpyPlaneFactory.restoreSpyPlane(sceneEntity, fullAttr, changeAttr);
                break;
            case ET_ResBuilding:
                ResBuildingFactory.restoreResBuilding(sceneEntity, fullAttr, changeAttr);
                break;
            case ET_ClanResBuilding:
                ClanResBuildingFactory.restoreClanResBuilding(sceneEntity, fullAttr, changeAttr);
                break;
            case ET_LogisticsPlane:
                LogisticsPlaneFactory.restoreLogisticsPlane(sceneEntity, fullAttr, changeAttr);
                break;
            default:
                TcaplusDb.SceneObjTable.Builder req = TcaplusDb.SceneObjTable.newBuilder();
                req.setEntityId(fullAttr.getEntityId()).setZoneId(sceneEntity.getZoneId()).setSceneId(sceneEntity.getZoneId());
                final DeleteAsk<Message.Builder> msg = new DeleteAsk<>(req, DeleteOption.newBuilder().withRetry().build());
                sceneEntity.ownerActor().tellGameDb(msg);

                LOGGER.error("createSceneObjFromDbAttr wrong type:{} attr:{}", fullAttr.getEntityType(), fullAttr);
                break;
        }
    }
}

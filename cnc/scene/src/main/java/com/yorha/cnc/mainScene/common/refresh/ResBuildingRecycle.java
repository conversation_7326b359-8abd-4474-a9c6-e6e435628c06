package com.yorha.cnc.mainScene.common.refresh;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneResRegionItem;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.resBuilding.enums.RefreshType;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.utils.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ResBuildingRecycle extends RefreshTask {
    private static final Logger LOGGER = LogManager.getLogger(ResBuildingRecycle.class);

    public final RefreshType refreshType;
    private final int regionId;

    public ResBuildingRecycle(SceneEntity owner, int regionId, RefreshType refreshType) {
        super(owner);
        this.regionId = regionId;
        this.refreshType = refreshType;
    }

    @Override
    public int getHandleNumPerTick() {
        return GameLogicConstants.RECYCLE_MAX_PER_SECOND;
    }

    @Override
    public Pair<Boolean, Integer> run() {
        if (!(getOwner().getResMgrComponent() instanceof MainSceneResMgrComponent)) {
            return Pair.of(true, 0);
        }
        final MainSceneResMgrComponent mainSceneResMgrComponent = (MainSceneResMgrComponent) getOwner().getResMgrComponent();
        MainSceneResRegionItem mgrItem = mainSceneResMgrComponent.getResRegionItem(regionId);
        if (mgrItem == null) {
            LOGGER.error("ResBuildingRecycle but regionItem not exist {} {}", regionId, refreshType);
            return Pair.of(true, 0);
        }
        return mgrItem.recycleResBuilding(refreshType, getHandleNumPerTick());
    }

    @Override
    public String toString() {
        return "ResBuildingRecycle{" +
                "refreshType=" + refreshType +
                ", regionId=" + regionId +
                '}';
    }
}

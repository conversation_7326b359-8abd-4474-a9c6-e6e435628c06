package com.yorha.cnc.zone.zone.kindomSkil.skillEffects;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.cnc.zone.zone.kindomSkil.KingdomSkill;
import com.yorha.cnc.zone.zone.kindomSkil.KingdomSkillHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 囚笼
 * 限制迁城
 * <p>
 * 不能对盟友和其他国王使用
 *
 * <AUTHOR>
 */
public class LimitMoveCity implements ISkillEffect {
    private static final Logger LOGGER = LogManager.getLogger(LimitMoveCity.class);

    @Override
    public ErrorCode checkCanUse(int skillId, long targetId, int targetZoneId, ZoneEntity zoneEntity) {
        MapBuildingEntity kingCity = zoneEntity.getBigScene().getBuildingMgrComponent().getKingCity();
        if (kingCity == null) {
            throw new GeminiException(ErrorCode.KINGDOM_NO_KING);
        }
        long clanId = kingCity.getClanId();
        ScenePlayerEntity targetPlayer = zoneEntity.getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(targetId);
        if (targetPlayer == null) {
            return ErrorCode.SCENE_PLAYER_NOT_EXIST;
        }
        CityEntity targetCity = targetPlayer.getMainCity();
        if (targetCity == null) {
            throw new GeminiException(ErrorCode.PLAYER_MAIN_CITY_NULL);
        }
        if (targetPlayer.getClanId() == clanId) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_2_TARGET_IS_SAME_CLAN);
        }
        return ErrorCode.OK;
    }

    @Override
    public void effect(int skillId, long playerId, ZoneEntity zoneEntity, int targetZoneId) {
        MapBuildingEntity kingCity = zoneEntity.getBigScene().getBuildingMgrComponent().getKingCity();
        String kingName = "";
        String kingClanName = "";
        if (null == kingCity) {
            LOGGER.error("LimitMoveCity kingCity is null");
        } else {
            kingName = kingCity.getProp().getKingdomModel().getKingCardHead().getName();
            kingClanName = kingCity.getClanSimpleName();
        }
        ScenePlayerEntity targetPlayer = zoneEntity.getBigScene().getPlayerMgrComponent().getScenePlayerOrNull(playerId);
        if (targetPlayer == null) {
            throw new GeminiException(ErrorCode.SCENE_PLAYER_NOT_EXIST);
        }
        CityEntity targetCity = targetPlayer.getMainCity();
        if (targetCity == null) {
            throw new GeminiException(ErrorCode.PLAYER_MAIN_CITY_NULL);
        }

        KingdomSkillHelper.addDevBuff(skillId, targetPlayer);
        targetCity.getKingdomComponent().kingdomSkillEffect(KingdomSkill.LIMIT_MOVE_CITY.getSkillId());
        // 跑马灯
        StructPB.DisplayDataPB.Builder marqueeBuilder = StructPB.DisplayDataPB.newBuilder();
        marqueeBuilder.getParamsBuilder()
                .addDatas(StructPB.DisplayParamPB.newBuilder().setType(CommonEnum.DisplayParamType.DPT_ZONE_ID).setText(String.valueOf(zoneEntity.getZoneId())))
                .addDatas(MsgHelper.buildDisPlayTextPb(kingClanName))
                .addDatas(MsgHelper.buildDisPlayTextPb(kingName))
                .addDatas(MsgHelper.buildDisPlayTextPb(targetPlayer.getClanName()))
                .addDatas(MsgHelper.buildDisPlayTextPb(targetPlayer.getName()));
        zoneEntity.getBigScene().getMarqueeComponent().sendFullServerMarquee(zoneEntity.getZoneId(), CommonEnum.MarqueeType.KINGDOM_SKILL_2_VALUE, marqueeBuilder.build(), null);
    }
}

package com.yorha.cnc.zone.zone.activity;

import com.yorha.cnc.zone.zone.ZoneEntity;

/**
 * 活动产生的全服效果
 *
 * <AUTHOR>
 */
public abstract class IActivityEffect {
    private boolean isOn = false;

    public boolean isOn() {
        return this.isOn;
    }

    final public void efffectStart(ZoneEntity owner) {
        this.isOn = true;
        this.effectOnStart(owner);
    }

    final public void efffectExpire(ZoneEntity owner) {
        this.isOn = false;
        this.effectOnExpire(owner);
    }

    /**
     * 活动开始时
     *
     * @param owner
     */
    protected abstract void effectOnStart(ZoneEntity owner);

    /**
     * 活动结束时
     *
     * @param owner
     */
    protected abstract void effectOnExpire(ZoneEntity owner);
}

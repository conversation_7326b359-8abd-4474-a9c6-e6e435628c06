package com.yorha.cnc.scene.gm.command.resbuilding;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneResRegionItem;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryResBuildingStatus2 implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isMainScene()) {
            return;
        }
        SceneEntity scene = actor.getScene();
        StringBuilder string = new StringBuilder();
        int region = Integer.parseInt(args.get("region"));
        int type = Integer.parseInt(args.get("type"));
        int level = Integer.parseInt(args.get("level"));

        MainSceneResRegionItem resRegionItem = ((MainSceneResMgrComponent) scene.getResMgrComponent()).getResRegionItem(region);
        if (resRegionItem != null) {
            resRegionItem.dumpRegionResourceEntity(type, level, string);
        }

        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(string.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("QueryResBuildingStatus2")
                .setSubTitle("QueryResBuildingStatus2");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(actor.getScenePlayer(playerId).getZoneId())
                        .build(),
                builder.build());
    }

    @Override
    public String showHelp() {
        return "QueryResBuildingStatus2 region={value} type={value} level={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COLLECT;
    }
}

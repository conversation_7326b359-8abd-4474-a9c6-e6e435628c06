package com.yorha.cnc.scene.sceneplayer.component;

import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.io.MsgType;
import com.yorha.common.prop.PropNotifier;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.ScenePlayerProp;
import com.yorha.gemini.props.CanStopPropertyChangeListener;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Entity.EntityNtfMsg;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.SsClanBase;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

import static com.yorha.common.addition.AdditionUtil.CLAN_ADDITION_SOURCES;

/**
 * <AUTHOR>
 */
public class ScenePlayerPropComponent extends AbstractComponent<ScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerPropComponent.class);

    private final ScenePlayerPropNotifier propNotifier;

    public ScenePlayerPropComponent(ScenePlayerEntity owner) {
        super(owner);
        propNotifier = new ScenePlayerPropNotifier();
    }

    @Override
    public void init() {
        getOwner().getProp().setListener(new CanStopPropertyChangeListener(this::flushProp, getOwner().ownerActor().self()));
    }

    /**
     * 下发变体属性
     * 所以是mod而不是new
     */
    public void onLogin() {
        propNotifier.reset();
        // 会下发全量prop
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());
    }

    public void onPlayerLogout() {
        this.propNotifier.reset();
    }

    private void flushProp() {
        if (!getOwner().getProp().hasAnyMark()) {
            return;
        }
        if (getOwner().isDestroy()) {
            LOGGER.error("ScenePlayerPropComponent {} has dirty data!", getOwner());
            return;
        }
        // propNotifier 自身是幂等的，对hasAnyMark有自己的控制
        propNotifier.tryMarkChangeAndNtf(getOwner().getProp());
        this.getOwner().getDbComponent().saveChangeToDb();
        this.getOwner().getProp().unMarkAll();
    }

    /**
     * 玩家名字变了
     */
    public void onSyncPlayerName(String name) {
        getOwner().getProp().getCardHead().setName(name);
        getOwner().getMainCity().onSyncPlayerName(name);
        getOwner().getArmyMgrComponent().onSyncPlayerName(name);
        getOwner().getPlaneComponent().refreshSpyPlaneOwnerName(name);
        SceneClanEntity sceneClan = getOwner().getSceneClan();
        if (sceneClan != null) {
            sceneClan.getMapBuildingComponent().onMemberNameChange(getOwner());
        }
        LOGGER.info("{} onSyncPlayerName {}", getOwner(), name);
    }

    /**
     * 玩家换头像
     */
    public void onSyncPlayerPic(int pic, String picUrl) {
        getOwner().getProp().getCardHead().setPic(pic).setPicUrl(picUrl);
        getOwner().getMainCity().onSyncPlayerPic(pic, picUrl);
        getOwner().getArmyMgrComponent().onSyncPlayerPic(pic, picUrl);
        LOGGER.info("{} onSyncPlayerPic {}", getOwner(), pic);
    }

    /**
     * 玩家换头像框
     */
    public void onSyncPlayerPicFrame(int picFrame) {
        getOwner().getProp().getCardHead().setPicFrame(picFrame);
        getOwner().getMainCity().onSyncPlayerPicFrame(picFrame);
        getOwner().getArmyMgrComponent().onSyncPlayerPicFrame(picFrame);
        LOGGER.info("{} onSyncPlayerPicFrame {}", getOwner(), picFrame);
    }

    /**
     * 玩家联盟变了
     */
    public void onSyncClanId(long clanId, String name) {
        long oldClanId = getOwner().getProp().getClanId();
        getOwner().getProp().setClanId(clanId);
        getOwner().getMainCity().onClanChange(clanId, name);
        getOwner().getArmyMgrComponent().onSyncPlayerClan(clanId, name);
        getOwner().getPlaneComponent().onPlayerClanChange(clanId, name);
        int version = 0;
        if (clanId > 0) {
            SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClan(clanId);
            version = sceneClan.getMemberComponent().getClanPositionMarkVersion();
            sceneClan.getMemberComponent().onJoinClan(getOwner(), clanId, oldClanId);
            refreshClanThings();
        } else {
            removeClanBuff();
            removeClanAddition();
            SceneClanEntity sceneClan = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(oldClanId);
            if (sceneClan != null) {
                sceneClan.getMemberComponent().onLeaveClan(getEntityId(), clanId, oldClanId);
            }
        }
        getOwner().getPositionMarkComponent().onClanSync(version);
        getOwner().getRallyComponent().onLogin();
        LOGGER.info("{} onSyncClanId {} {}", getOwner(), clanId, name);
    }

    public void refreshClanThings() {
        if (!getOwner().isInClan()) {
            removeClanBuff();
            removeClanAddition();
            return;
        }

        // ask 同步clan上的buff, addition
        getOwner().ownerActor().askClan(getOwner().getZoneId(), getOwner().getClanId(), SsClanBase.RefreshClanThingsAsk.newBuilder().setPlayerId(getOwner().getPlayerId()).build())
                .onComplete((res, e) -> {
                    if (res == null || e != null) {
                        if (e instanceof GeminiException) {
                            LOGGER.warn("{} refreshClanThings failed clanId:{}", getOwner(), getOwner().getClanId(), e);
                        } else {
                            LOGGER.error("{} refreshClanThings failed clanId:{}", getOwner(), getOwner().getClanId(), e);
                        }
                        removeClanBuff();
                        removeClanAddition();
                        return;
                    }

                    LOGGER.debug("{} refreshClanThings ans:{}", getOwner(), res);

                    SsClanBase.RefreshClanThingsAns ans = (SsClanBase.RefreshClanThingsAns) res;
                    getOwner().getDevBuffComponent().refreshClanBuff(ans.getBuffSys());
                    getOwner().getAdditionComponent().refreshClanAddition(ans.getAddition());
                });
    }

    private void removeClanBuff() {
        List<DevBuffProp> curClanBuff = getOwner().getDevBuffComponent().getDevBuffByBuffType(CommonEnum.DevBuffType.DBT_CLAN_BUFF);
        // 离开联盟, 移除联盟相关buff
        for (DevBuffProp devBuffProp : curClanBuff) {
            getOwner().getDevBuffComponent().removeDevBuff(devBuffProp.getDevBuffId());
        }
    }

    private void removeClanAddition() {
        // 离开联盟, 移除联盟相关加成
        for (CommonEnum.AdditionSourceType additionSourceType : CLAN_ADDITION_SOURCES) {
            getOwner().getAdditionComponent().removeAdditionBySource(additionSourceType);
        }
    }

    public void unlockInitialResource() {
        List<Integer> techInitUnlockResource = ResHolder.getResService(ConstKVResService.class).getTemplate().getTechUnlockRareEarthTiberiumDiamond();
        getOwner().getProp().getTechModel().getUnlockResource().addAll(techInitUnlockResource);
    }

    class ScenePlayerPropNotifier extends PropNotifier<ScenePlayerProp, PlayerPB.PlayerEntityPB.Builder> {
        public ScenePlayerPropNotifier() {
            super(PlayerPB.PlayerEntityPB.newBuilder());
        }

        @Override
        public boolean canNtfToClient() {
            return getOwner().isOnline();
        }

        @Override
        public boolean ntfToClient(EntityNtfMsg notify) {
            if (getOwner().isInDungeon() && !BigSceneConstants.needSyncScenePlayer(getOwner().getDungeonType())) {
                return true;
            }
            return getOwner().sendMsgToClientIgnoreInDungeon(MsgType.ENTITYNTFMSG, notify);
        }

        @Override
        public void fillEntityAttr(EntityAttr.Builder entityAttr, PlayerPB.PlayerEntityPB.Builder builder) {
            entityAttr.setEntityId(getEntityId())
                    .setEntityType(getEntityType())
                    .setPlayerAttr(builder.build());
        }

        @Override
        public int copyChange(ScenePlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            return prop.copyChangeToCs(changeCollector.getScenePlayerBuilder());
        }

        @Override
        public void copyFull(ScenePlayerProp prop, PlayerPB.PlayerEntityPB.Builder changeCollector) {
            prop.copyToCs(changeCollector.getScenePlayerBuilder());
        }

        @Override
        protected void fillNewEntityNtf(EntityNtfMsg.Builder builder, EntityAttr.Builder entityAttr) {
            // scenePlayer都是以PlayerEntity的mod形式下发的
            builder.addModEntities(entityAttr);
        }
    }
}

package com.yorha.cnc.scene.pathfinding.manager;

import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.Easy3dNav.EasyNavFunc;
import com.yorha.common.utils.shape.Point;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class NavmeshApi {
    private static final Logger LOGGER = LogManager.getLogger(NavmeshApi.class);

    public static float[] transToNavPoint(Point p) {
        float x = p.getX() / (float) GameLogicConstants.LTNCR;
        float y = p.getY() / (float) GameLogicConstants.LTNCR;
        return new float[]{x, 0f, y};
    }

    public static Point transToPoint(float[] p) {
        int pointX = (int) (p[0] * GameLogicConstants.LTNCR);
        int pointY = (int) (p[2] * GameLogicConstants.LTNCR);
        return Point.valueOf(pointX, pointY);
    }

    /**
     * 判断某点是否处于可行走区域
     */
    public static boolean isPointWalkable(EasyNavFunc nav, Point oPoint) {
        float[] point = transToNavPoint(oPoint);
        float[] nearest = nav.findNearest(point);
        if (nearest == null || nearest.length <= 0) {
            return false;
        }
        float xDis = Math.abs(point[0] - nearest[0]);
        float yDis = Math.abs(point[2] - nearest[2]);
        if (xDis > 0.1f || yDis > 0.1f) {
            return false;
        }
        return true;
    }

    public static Point findNearestWalkablePoint(EasyNavFunc nav, Point oPoint) {
        float[] point = transToNavPoint(oPoint);
        float[] nearest = nav.findNearest(point);
        if (nearest == null || nearest.length <= 0) {
            return null;
        }
        return transToPoint(nearest);
    }

    public static List<Point> searchPath(EasyNavFunc nav, Point srcPoint, Point endPoint) {
        float[] sPoint = transToNavPoint(srcPoint);
        float[] ePoint = transToNavPoint(endPoint);
        List<float[]> pointList = nav.find(sPoint, ePoint);

        List<Point> ret = new ArrayList<>();
        for (float[] p : pointList) {
            // 返回点的单位 int厘米
            ret.add(transToPoint(p));
        }
        if (ret.size() > 2) {
            searchPathLongCorrect(ret, nav, ePoint, endPoint, 0);
        }
        return ret;
    }

    /**
     * 长距离寻路一次搜索可能只有半截，需要手动拼接
     * 标准是返回的最后一个点与实际目标终点距离大于20m
     * 最多拼接3次
     */
    private static void searchPathLongCorrect(List<Point> ret, EasyNavFunc nav, float[] ePoint, Point endPoint, int cnt) {
        Point point = ret.get(ret.size() - 1);
        if (Point.calDisBetweenTwoPoint(point, endPoint) <= 2000) {
            if (cnt > 0) {
                LOGGER.info("searchPathLongCorrect src={} end={} cnt={}", ret.get(0), endPoint, cnt);
            }
            return;
        }
        if (cnt > 2) {
            LOGGER.error("searchPathLongCorrect want to exceed src={} end={} cnt={}", ret.get(0), endPoint, cnt);
            return;
        }
        float[] sPointNew = transToNavPoint(point);
        List<float[]> pointList = nav.find(sPointNew, ePoint);
        // 第一个就是上一次的终点 不加了
        for (int i = 1; i < pointList.size(); i++) {
            // 返回点的单位 int厘米
            ret.add(transToPoint(pointList.get(i)));
        }
        if (pointList.size() <= 2) {
            LOGGER.info("searchPathLongCorrect src={} end={} cnt={}", ret.get(0), endPoint, cnt + 1);
            return;
        }
        searchPathLongCorrect(ret, nav, ePoint, endPoint, cnt + 1);
    }

    /**
     * 判断两点之间是否无静态阻挡
     */
    public static boolean isNoCollision(EasyNavFunc nav, Point srcPoint, Point endPoint) {
        float[] sPoint = transToNavPoint(srcPoint);
        float[] ePoint = transToNavPoint(endPoint);
        float[] fPoint = nav.raycast(sPoint, ePoint);
        if (fPoint == null || fPoint.length < 3) {
            return false;
        }
        return Math.abs(fPoint[0] - ePoint[0]) < 0.1f && Math.abs(fPoint[2] - ePoint[2]) < 0.1f;
    }

    public static Point findNearestPointRayCast(EasyNavFunc nav, Point srcPoint, Point endPoint) {
        float[] sPoint = transToNavPoint(srcPoint);
        float[] ePoint = transToNavPoint(endPoint);
        float[] fPoint = nav.raycast(sPoint, ePoint);
        if (fPoint == null || fPoint.length < 3) {
            return null;
        }
        return transToPoint(fPoint);
    }
}

package com.yorha.cnc.scene.mapBuilding.component;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.InnerArmyInfoProp;
import com.yorha.game.gen.prop.MapBuildingProp;
import qlog.flow.QlogCncGuildExpansion;
import qlog.flow.QlogCncGuildMapHolyBuilding;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MapBuildingQLogComponent extends SceneObjComponent<MapBuildingEntity> {
    public MapBuildingQLogComponent(MapBuildingEntity owner) {
        super(owner);
    }

    /**
     * 战略要地争夺日志所需的城市状态
     */
    public int getQLogState() {
        switch (getProp().getOccupyinfo().getState()) {
            case TOS_CLOSE:
            case TOS_NEUTRAL:
                return 0;
            case TOS_PROTECT:
            case TOS_DESERTED:
                // 首次占据
                if (getProp().getOccupyinfo().getFisrtOwnTsMs() == getProp().getOccupyinfo().getOwnerOccupyTsMs()) {
                    return 2;
                }
                return 3;
            case TOS_OCCUPYING:
                return 1;
            default:
                return -1;
        }
    }

    public String getInnerPlayer() {
        List<Long> players = new ArrayList<>();
        for (InnerArmyInfoProp prop : getOwner().getProp().getInnerArmy().getArmy().values()) {
            players.add(prop.getPlayerId());
        }
        return QlogUtils.transCollection2ArrayString(players);
    }

    public void sendMapBuildLog(long clanId, String action, String roleIds) {
        if (clanId <= 0) {
            return;
        }
        QlogCncGuildMapHolyBuilding flow = new QlogCncGuildMapHolyBuilding();
        flow.setDtEventTime(TimeUtils.now2String())
                .setBuildingId(getProp().getPartId())
                .setState(getQLogState())
                .setHolyOwnerId(String.valueOf(getOwner().getClanId()))
                .setAction(action)
                .setOptionRoleID(roleIds);
        SceneClanEntity sceneClanEntity = getOwner().getScene().getClanMgrComponent().getSceneClanOrNull(clanId);
        if (sceneClanEntity == null) {
            return;
        }
        flow.fillHead(sceneClanEntity.getQlogComponent());
        flow.sendToQlog();
    }

    /**
     * 军团建筑qlog
     * start_build_guildbuilding 开始改建
     * participate_build 参与改建
     * leave_build 离开改建
     * build_complete 改建完成
     * defence_guild_building 驻防
     * defence_guild_building_conquering 参与占领
     * leave_defence_guild_building 离开驻防
     * abandon_guild_building 放弃驻防
     * waste_guild_building 开始荒废
     * complete_waste_building 完成荒废 回归中立
     */
    public void sendExpansionLog(long clanId, String action, long roleId) {
        QlogCncGuildExpansion flow = new QlogCncGuildExpansion();
        flow.setDtEventTime(TimeUtils.now2String())
                .setBuildingId(getOwner().getPartId())
                .setBuildingCoordinate(String.format("%d,%d", getOwner().getProp().getPoint().getX(), getOwner().getProp().getPoint().getY()))
                .setAction(action)
                .setOptionRoleId(String.valueOf(roleId))
                .setGuildBuildingID(getEntityId());
        SceneClanEntity sceneClanEntity = getOwner().getScene().getClanMgrComponent().getSceneClan(clanId);
        if (sceneClanEntity == null) {
            return;
        }
        flow.fillHead(sceneClanEntity.getQlogComponent());
        flow.sendToQlog();
    }

    public void onArmyArrived(long clanId, long roleId) {
        if (getOwner().isInOccupying()) {
            sendExpansionLog(clanId, "defence_guild_building_conquering", roleId);
        } else if (getOwner().isInRebuilding()) {
            sendExpansionLog(clanId, "participate_build", roleId);
        } else {
            sendExpansionLog(clanId, "defence_guild_building", roleId);
        }
    }

    public void onArmyLeave(long clanId, long roleId) {
        if (getOwner().isInRebuilding()) {
            sendExpansionLog(clanId, "leave_build", roleId);
        } else {
            sendExpansionLog(clanId, "leave_defence_guild_building", roleId);
        }
    }

    private MapBuildingProp getProp() {
        return getOwner().getProp();
    }

    @Override
    public SceneActor ownerActor() {
        return getOwner().ownerActor();
    }

    @Override
    public void init() {

    }
}

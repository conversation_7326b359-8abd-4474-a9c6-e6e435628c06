package com.yorha.cnc.scene.sceneObj.ai.stateMachine;

import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.ai.stateMachine.impl.ActiveMonsterStateDiagram;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class StateTransitionConfig {

    private final CommonEnum.AIStateType sourceState;
    private final CommonEnum.AIStateType targetState;
    private final AIEvent event;

    public static class Builder {
        private CommonEnum.AIStateType sourceState;
        private CommonEnum.AIStateType targetState;
        private AIEvent event;

        public Builder sourceState(CommonEnum.AIStateType state) {
            sourceState = state;
            return this;
        }

        public Builder targetState(CommonEnum.AIStateType state) {
            targetState = state;
            return this;
        }

        public Builder event(AIEvent event) {
            this.event = event;
            return this;
        }

        public StateTransitionConfig build() {
            return new StateTransitionConfig(this);
        }
    }

    private StateTransitionConfig(Builder builder) {
        sourceState = builder.sourceState;
        targetState = builder.targetState;
        event = builder.event;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public CommonEnum.AIStateType getSourceState() {
        return sourceState;
    }

    public CommonEnum.AIStateType getTargetState() {
        return targetState;
    }

    public AIEvent getEvent() {
        return event;
    }
}

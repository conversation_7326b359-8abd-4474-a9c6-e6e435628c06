package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.event.DeleteEvent;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.TickMoveNoPathEvent;
import com.yorha.cnc.scene.event.army.ArmyEnterInteriorEvent;
import com.yorha.cnc.scene.event.army.PlayerOperationPreEvent;
import com.yorha.cnc.scene.event.assist.AssistArmyTreatEvent;
import com.yorha.cnc.scene.event.battle.TreatSoldierEvent;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 援助管理
 */
public class ArmyAssistComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyAssistComponent.class);

    /**
     * 发起援助时间戳
     */
    private long tryAssistTsMs;

    public ArmyAssistComponent(ArmyEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        if (getAssistTargetId() != 0 && getOwner().getProp().getRallyRole() == RallyArmyRoleType.RART_Single) {
            // 关服前还没有到达援助城市的  不去了 重置下
            getOwner().getProp().setCurAssistTargetId(0);
        }
        //  玩家指令前置事件 check 援助情况
        getOwner().getEventDispatcher().addEventListenerRepeat(this::checkAssistState, PlayerOperationPreEvent.class);
        // 自身死亡 删除 退盟  退出援助
        getOwner().getEventDispatcher().addMultiEventListenerRepeat((IEvent e) -> {
            if (getAssistTargetId() == 0) {
                return;
            }
            LOGGER.info("{} leaveAssist {} reason: {}", getOwner(), getAssistTargetId(), e);
            // 退出联盟导致的退出，需要回城
            boolean needReturn = e.equals(ClanChangeEvent.class);
            leaveAssist(null, true, needReturn);
        }, DieEvent.class, DeleteEvent.class, ClanChangeEvent.class, TickMoveNoPathEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onTreatSoldierEvent, TreatSoldierEvent.class);
    }

    /**
     * 指令更改 需要检查是否更改了援助目标
     */
    private void checkAssistState(PlayerOperationPreEvent event) {
        long assistTargetId = getAssistTargetId();
        if (assistTargetId == 0) {
            return;
        }
        // 改变指令 退出该援助
        StructPlayer.ArmyActionInfo actionInfo = event.getArmyActionInfo();
        if (actionInfo.getArmyActionType() != ArmyActionType.AAT_ASSIST || assistTargetId != actionInfo.getTargetId()) {
            LOGGER.info("{} leaveAssist {} reason: changeOperation", getOwner(), assistTargetId);
            getOwner().getMoveComponent().stopMove();
            if (actionInfo.getTargetId() != -1 && actionInfo.getTargetId() != 0) {
                SceneObjEntity sceneObj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(actionInfo.getTargetId());
                if (sceneObj != null) {
                    leaveAssist(sceneObj.getCurPoint(), true, false);
                    return;
                }
            }
            if (actionInfo.getArmyActionType() == ArmyActionType.AAT_Return) {
                leaveAssist(getOwner().getMainCityPoint(), true, false);
                return;
            }
            leaveAssist(Point.valueOf(actionInfo.getTargetPoint().getX(), actionInfo.getTargetPoint().getY()), true, false);
        }
    }

    /**
     * 处理援助指令
     */
    public void handleAssist(long targetId) {
        // 重复指令
        if (getAssistTargetId() == targetId) {
            return;
        }
        // 获取加入援助的目标体
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        if (target == null) {
            LOGGER.error("{} tryJoinAssist error. targetId: {}", getOwner(), targetId);
            return;
        }
        // 通知目标发起援助
        target.getInnerArmyComponent().armyJoin(getOwner());
        // 记录援助id
        getOwner().getProp().setCurAssistTargetId(targetId);
        tryAssistTsMs = SystemClock.now();
        try {
            // 移动至目标
            getOwner().getMoveComponent().moveToTargetAsync(
                    target,
                    TroopInteractionType.ENTER_BUILD,
                    this::arriveAssistTarget, null,
                    (codeId) -> {
                        if (ErrorCode.isOK(codeId)) {
                            return;
                        }
                        getOwner().getScenePlayer().sendErrorCode(codeId);
                        onMoveFailed();
                    }
            );
        } catch (Exception e) {
            onMoveFailed();
            throw e;
        }
        LOGGER.info("{} tryJoinAssist. targetId: {}", getOwner(), targetId);
    }

    private void onMoveFailed() {
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(getAssistTargetId());
        getOwner().getProp().setCurAssistTargetId(0);
        getOwner().getStatusComponent().setStaying();
        if (target == null) {
            return;
        }
        target.getInnerArmyComponent().armyLeave(getOwner(), getOwner().getCurPoint());
    }

    /**
     * 到达援助建筑
     */
    private void arriveAssistTarget() {
        long assistTargetId = getAssistTargetId();
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(assistTargetId);
        if (target == null) {
            getOwner().getProp().setCurAssistTargetId(0);
            LOGGER.info("{} arriveAssistTarget but target is null {}", getOwner(), assistTargetId);
            return;
        }
        // 加入援助时结束掉所有战场
        getOwner().getBattleComponent().forceEndAllBattle();
        // 通知目标到达援助
        target.getInnerArmyComponent().armyArrived(getOwner());
        getOwner().getPropComponent().onAttachChange(AttachState.AAS_ASSIST, assistTargetId);
        getOwner().getProp().setRallyRole(RallyArmyRoleType.RART_Follower);
        // 移除视野
        if (getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_ASSIST);
        }
        getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_ASSIST, target);
        getOwner().getEventDispatcher().dispatch(new ArmyEnterInteriorEvent(getEntityId()));
        LOGGER.info("{} arrivedAssist.  targetId: {}", getOwner(), assistTargetId);
    }


    /**
     * 取消援助
     * 主动 其他指令打断/自身死亡/删除/退盟/途中没路
     * 被动  遣返
     *
     * @param targetPoint 将要去的地方  没指定就用主城的
     */
    public void leaveAssist(Point targetPoint, boolean needNtfTarget, boolean needReturn) {
        LOGGER.info("{} leaveAssist  targetId: {} needNtfTarget: {} needReturn: {}", getOwner(), getAssistTargetId(), needNtfTarget, needReturn);
        SceneObjEntity target = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(getAssistTargetId());
        if (getOwner().getProp().getRallyRole() != RallyArmyRoleType.RART_Single) {
            getOwner().getPropComponent().onAttachChange(AttachState.AAS_NONE, 0);
            getOwner().getProp().setRallyRole(RallyArmyRoleType.RART_Single);
        }
        getOwner().getProp().setCurAssistTargetId(0);
        getOwner().getStatusComponent().setStaying();
        // 重新加入aoi
        if (!getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().addIntoAoi(SceneObjectNtfReason.SONR_ASSIST);
        }
        if (target != null) {
            if (needReturn) {
                targetPoint = getOwner().getMainCityPoint();
            }
            Point point = null;
            if (needNtfTarget) {
                point = target.getInnerArmyComponent().armyLeave(getOwner(), targetPoint);
            } else if (targetPoint != null) {
                point = target.getTransformComponent().getLeavePosition(getOwner(), targetPoint);
            }
            if (point != null) {
                getOwner().getTransformComponent().changePoint(point);
            }
        }
        if (!needReturn) {
            return;
        }
        if (getOwner().getBattleComponent().hasAnyAlive()) {
            getOwner().getMoveComponent().returnMainCity();
            getOwner().getBehaviourComponent().refreshArmyState();
        } else {
            getOwner().getMoveComponent().retreat();
        }
    }

    /**
     * 占领成功 转援助进入
     */
    public void occupyToAssist(Point p, SceneObjEntity target) {
        long targetId = target.getEntityId();
        getOwner().getMoveComponent().stopMove();
        getOwner().getTransformComponent().changePoint(p, true);
        getOwner().getPropComponent().onAttachChange(AttachState.AAS_ASSIST, targetId);
        getOwner().getProp().setCurRallyId(0).setCurAssistTargetId(targetId).setRallyRole(RallyArmyRoleType.RART_Follower);
        tryAssistTsMs = SystemClock.now();
        getOwner().getBehaviourComponent().refreshArmyState();
        // 移除视野  如果是集结成员 那肯定移除过了
        if (getOwner().getAoiNodeComponent().isInAoi()) {
            getOwner().getAoiNodeComponent().removeFromAoi(SceneObjectNtfReason.SONR_ASSIST);
        }
        getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_ASSIST, target);
        getOwner().getEventDispatcher().dispatch(new ArmyEnterInteriorEvent(getEntityId()));
        getOwner().getBattleComponent().forceEndAllBattle();
        LOGGER.info("{} occupyToAssist target: {}", getOwner(), targetId);
    }

    /**
     * 治疗事件发生  仅限脱战后治疗
     *
     * @param event 治疗事件内容
     */
    private void onTreatSoldierEvent(TreatSoldierEvent event) {
        // 不是脱战后的不管
        if (getOwner().getBattleComponent().isInBattle()) {
            return;
        }
        if (getOwner().isRallyArmy()) {
            // 是集结体  就抛给对应的子部队的援助目标
            for (SoldierLossDTO treatDatum : event.getTreatData()) {
                for (Map.Entry<Long, SoldierLossData> entry : treatDatum.getChildLossMap().entrySet()) {
                    ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, entry.getKey());
                    if (army == null) {
                        continue;
                    }
                    SceneObjEntity target = army.getAssistComponent().getAssistTarget();
                    if (target != null) {
                        target.getEventDispatcher().dispatch(new AssistArmyTreatEvent());
                        // 集结的援助对象都是同一个  触发过就不出发了
                        return;
                    }
                }
            }
            return;
        }
        SceneObjEntity sceneObjEntity = getAssistTarget();
        if (sceneObjEntity == null) {
            return;
        }
        sceneObjEntity.getEventDispatcher().dispatch(new AssistArmyTreatEvent());
    }

    public long getAssistTargetId() {
        return getOwner().getProp().getCurAssistTargetId();
    }

    public long getTryAssistTsMs() {
        return tryAssistTsMs;
    }

    public SceneObjEntity getAssistTarget() {
        long assistTargetId = getAssistTargetId();
        if (assistTargetId <= 0L) {
            return null;
        }
        return getOwner().getScene().getObjMgrComponent().getSceneObjEntity(assistTargetId);
    }
}

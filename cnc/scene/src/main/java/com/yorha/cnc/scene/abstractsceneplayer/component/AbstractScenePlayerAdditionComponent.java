package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.AbstractScenePlayerAdditionMgr;
import com.yorha.common.addition.AdditionProviderType;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.AdditionItemProp;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 加成更新流程：
 * player侧: scenePlayer -> player
 * scene侧: scenePlayer
 *
 * <AUTHOR>
 */
public class AbstractScenePlayerAdditionComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerAdditionComponent.class);

    protected AbstractScenePlayerAdditionMgr<?> additionMgr;

    public AbstractScenePlayerAdditionComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    protected AdditionSysProp getAdditionProp() {
        return null;
    }

    public void copyAddition(Struct.Int32AdditionMap.Builder builder) {
        if (getAdditionProp() == null) {
            return;
        }
        for (Map.Entry<Integer, AdditionProp> entry : getAdditionProp().getAddition().entrySet()) {
            if (entry.getValue().getTotalValue() == 0) {
                continue;
            }
            builder.putDatas(
                    entry.getKey(),
                    Struct.Addition.newBuilder()
                            .setAdditionId(entry.getKey())
                            .setTotalValue(entry.getValue().getTotalValue())
                            .build()
            );
        }
    }

    public void copyGvgAddition(Struct.Int32AdditionMap.Builder builder) {
        if (getAdditionProp() == null) {
            return;
        }
        for (Map.Entry<Integer, AdditionProp> entry : getAdditionProp().getAddition().entrySet()) {
            // 和平护盾不生效
            if (entry.getKey() == CommonEnum.BuffEffectType.ET_PEACE_SHIELD.getNumber()) {
                continue;
            }
            Struct.Addition.Builder additionBuilder = Struct.Addition.newBuilder()
                    .setAdditionId(entry.getKey());
            long totalValue = 0;
            for (Map.Entry<Integer, AdditionItemProp> itemEntry : entry.getValue().getAdditionItems().entrySet()) {
                totalValue += itemEntry.getValue().getValue();
                additionBuilder.getAdditionItemsBuilder().putDatas(itemEntry.getKey(), itemEntry.getValue().getCopySsBuilder().build());
            }
            additionBuilder.setTotalValue(totalValue);
            if (totalValue == 0) {
                continue;
            }
            builder.putDatas(entry.getKey(), additionBuilder.build());
        }
        LOGGER.info("AbstractScenePlayerAdditionComponent player={} copyGvgAddition={}", getOwner(), builder);
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionId   加成id
     */
    public void updateAddition(AdditionProviderType providerType, int additionId) {
        if (additionId > 0) {
            additionMgr.updateAdditionByProvider(providerType, Lists.newArrayList(additionId));
        } else {
            LOGGER.error("{}, updateAddition, providerType:{}, additionId:{} invalid", getOwner(), providerType, additionId);
        }
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     * @param additionIds  加成id list
     */
    public void updateAddition(AdditionProviderType providerType, @NotNull List<Integer> additionIds) {
        additionMgr.updateAdditionByProvider(providerType, additionIds);
    }

    public void updateAdditionFromClan(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        additionMgr.updateAdditionBySource(sourceType, additions);
    }

    /**
     * 更新player上指定来源的加成
     *
     * @param sourceType 加成来源
     * @param additions  加成值
     */
    public void updateAdditionFromPlayer(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions) {
        additionMgr.updateAdditionBySource(sourceType, additions);
    }

    public void removeAdditionBySource(CommonEnum.AdditionSourceType sourceType) {
        Set<Integer> additionIdsBySource = additionMgr.getAdditionIdsBySource(sourceType);
        Map<Integer, Long> additionToRemove = Maps.newHashMap();
        for (Integer id : additionIdsBySource) {
            additionToRemove.put(id, 0L);
        }
        additionMgr.updateAdditionBySource(sourceType, additionToRemove);
    }

    public long getAddition(CommonEnum.BuffEffectType addition) {
        if (!AdditionUtil.isSceneAddition(addition.getNumber())) {
            WechatLog.error("{} getAddition failed in scene. id:{}", getOwner(), addition.getNumber());
        }
        if (additionMgr == null) {
            return 0;
        }
        return additionMgr.getAddition(addition.getNumber());
    }

    public Map<CommonEnum.BuffEffectType, Long> getAllAddition() {
        if (additionMgr == null) {
            return Collections.emptyMap();
        }
        Map<CommonEnum.BuffEffectType, Long> ret = Maps.newHashMap();
        for (Map.Entry<Integer, AdditionProp> entry : additionMgr.getAdditionSys().getAddition().entrySet()) {
            if (entry.getValue().getTotalValue() != 0) {
                ret.put(CommonEnum.BuffEffectType.forNumber(entry.getKey()), entry.getValue().getTotalValue());
            }
        }
        // zone相关加成
        if (getOwner().getScene().isBigScene()) {
            getOwner().getScene().getBigScene().getZoneEntity().getAdditionComponent().copyAllSceneAddition(ret);
        }
        return ret;
    }

    public AdditionSysProp gmGetAllSceneAdditions() {
        AdditionSysProp allProp = new AdditionSysProp();
        allProp.mergeFromSs(additionMgr.getAdditionSys().getCopySsBuilder().build());
        return allProp;
    }

    public void refreshClanAddition(Struct.AdditionSys additionSys) {
        additionMgr.updateAdditionBySys(additionSys);
    }

    public void refreshAllAddition() {
        getOwner().getDevBuffComponent().refreshAdditionCache();
        additionMgr.refreshAllAddition();
    }
}

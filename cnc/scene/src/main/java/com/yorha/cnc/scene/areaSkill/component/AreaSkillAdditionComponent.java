package com.yorha.cnc.scene.areaSkill.component;

import com.yorha.cnc.scene.areaSkill.AreaSkillEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;

/**
 * 区域技能加成组件
 *
 * <AUTHOR>
 */
public class AreaSkillAdditionComponent extends SceneObjAdditionComponent {
    public AreaSkillAdditionComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public AreaSkillEntity getOwner() {
        return (AreaSkillEntity) super.getOwner();
    }
}

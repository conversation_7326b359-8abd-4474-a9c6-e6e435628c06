package com.yorha.cnc.scene.npcplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerRallyComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.game.gen.prop.ScenePlayerRallyBaseProp;
import com.yorha.proto.Core;

/**
 * <AUTHOR>
 */
public class NpcPlayerRallyComponent extends AbstractScenePlayerRallyComponent {
    public NpcPlayerRallyComponent(NpcPlayerEntity owner) {
        super(owner);
    }

    @Override
    protected ScenePlayerRallyBaseProp getRallyProp() {
        return null;
    }

    @Override
    public RallyEntity getRallyEntity(long clanId, long rallyId) {
        return getRallyEntity(rallyId);
    }

    @Override
    public RallyEntity getRallyEntity(long rallyId) {
        return getOwner().getScene().getRallyMgrComponent().getRally(rallyId);
    }

    @Override
    public Core.Code checkJoinRally(long rallyId, long soldierNum) {
        return ErrorCode.OK.getCode();
    }

    @Override
    public RallyEntity createRally(ArmyEntity armyEntity, long targetId, int waitTime, int costEnergy) {
        return null;
    }
}

package com.yorha.cnc.scene.outbuilding;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.outbuilding.component.OutbuildingAiComponent;
import com.yorha.cnc.scene.outbuilding.component.OutbuildingBattleComponent;
import com.yorha.cnc.scene.outbuilding.component.OutbuildingBuffComponent;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.camp.CampRelationProvider;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjHateListComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.OutbuildingProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.Camp;
import com.yorha.proto.CommonEnum.OutbuildingState;
import com.yorha.proto.CommonEnum.SceneObjType;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import org.apache.commons.lang3.NotImplementedException;
import res.template.MapBuildingTemplate;

/**
 * 超武附属建筑
 *
 * <AUTHOR>
 */
public class OutbuildingEntity extends SceneObjEntity implements BuildingEntityType {
    private final OutbuildingProp prop;
    private final OutbuildingBuffComponent buffComponent = new OutbuildingBuffComponent(this);
    private final SceneObjAdditionComponent additionComponent = new SceneObjAdditionComponent(this);
    private final SceneObjHateListComponent hateListComponent;
    private final OutbuildingAiComponent aiComponent;
    /**
     * 构造时依赖 prop 只能放在构造函数里了
     */
    private final OutbuildingBattleComponent battleComponent;

    public OutbuildingEntity(OutbuildingBuilder builder) {
        super(builder);
        this.prop = builder.getProp();
        battleComponent = new OutbuildingBattleComponent(this, SceneObjType.SOT_STRONG_POINT_ARMY);
        if (getBuildingTemplate().getAiIndex() > 0) {
            aiComponent = new OutbuildingAiComponent(this);
            hateListComponent = new SceneObjHateListComponent(this);
        } else {
            aiComponent = null;
            hateListComponent = null;
        }
        initAllComponents();
        getPropComponent().initPropListener(false);
    }

    @Override
    protected ErrorCode canBeAttackBySceneObj(SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack(attackerObj, needCheckSiegeLimit);
    }

    @Override
    protected ErrorCode canBeAttackByScenePlayer(AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return commonCheckBeAttack(attackerPlayer, needCheckSiegeLimit);
    }

    private ErrorCode commonCheckBeAttack(CampRelationProvider provider, boolean needCheckSiegeLimit) {
        if (getProp().getState() != OutbuildingState.OS_IDLE) {
            return ErrorCode.BATTLE_CANT;
        }
        return ErrorCode.OK;
    }

    @Override
    public boolean canBeSpy(long enemyPlayerId) {
        return true;
    }

    @Override
    public EntityAttrOuterClass.EntityType getEntityType() {
        return EntityAttrOuterClass.EntityType.ET_Outbuilding;
    }

    @Override
    public OutbuildingProp getProp() {
        return prop;
    }

    @Override
    public void fullCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        getProp().copyToCs(builder.getOutbuildingAttrBuilder());
    }

    @Override
    public int changedCsAndClearDelKeyEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToAndClearDeleteKeysCs(builder.getOutbuildingAttrBuilder());
    }

    @Override
    public int changedCsEntityAttr(EntityAttrOuterClass.EntityAttr.Builder builder) {
        return getProp().copyChangeToCs(builder.getOutbuildingAttrBuilder());
    }

    @Override
    public void fullDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public int changedDbEntityAttr(EntityAttrDb.EntityAttrDB.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public EntityAttrDb.EntityAttrDB.Builder fullDbEntityAttr(TcaplusDb.SceneObjTable.Builder builder) {
        throw new NotImplementedException(StringUtils.format("Entity With Queue Not Implement, entityType={}", this.getEntityType()));
    }

    @Override
    public void copyScenePlayerArmyTargetStatus(ScenePlayerArmyStatusProp prop) {
        // 显示上能用这个类型兼容 就没问题
        prop.getTarget().setTargetType(CommonEnum.ArmyTargetType.ATT_MAPBUILDING)
                .setName("")
                .setTemplateId(getTemplateId())
                .getPoint().setX(getCurPoint().getX())
                .setY(getCurPoint().getY());
    }

    @Override
    public SceneObjBattleComponent getBattleComponent() {
        return battleComponent;
    }

    @Override
    public OutbuildingBuffComponent getBuffComponent() {
        return buffComponent;
    }

    @Override
    public SceneObjAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    @Override
    public SceneObjHateListComponent getHateListComponent() {
        return hateListComponent;
    }

    @Override
    public SceneObjAiComponent getAiComponent() {
        return aiComponent;
    }

    @Override
    public SceneObjectEnum getSceneObjType() {
        return getBuildingTemplate().getObjType();
    }

    @Override
    public boolean briefEntityAttr(Entity.SceneObjBriefAttr.Builder builder) {
        builder.getPointAttrBuilder().setX(getCurPoint().getX()).setY(getCurPoint().getY());
        return true;
    }

    @Override
    public long getPlayerId() {
        return 0;
    }

    @Override
    public long getClanId() {
        return 0;
    }

    @Override
    public Camp getCampEnum() {
        return CommonEnum.Camp.C_NEUTRAL;
    }

    public int getPartId() {
        return getProp().getPartId();
    }

    public int getTemplateId() {
        return getProp().getTemplateId();
    }

    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        if (super.onTickDispatch(reason)) {
            return true;
        }
        if (reason == SceneTickReason.TICK_AI) {
            battleComponent.onTeslaLogic();
            SceneObjAiComponent aiComponent = getAiComponent();
            if (aiComponent == null) {
                return false;
            }
            if (!aiComponent.isRunning()) {
                return true;
            }
            aiComponent.onTick();
            return true;
        }

        if (reason == SceneTickReason.TICK_HATE) {
            getHateListComponent().onTick();
            return true;
        }
        return false;
    }

    @Override
    public MapBuildingTemplate getBuildingTemplate() {
        return ResHolder.getInstance().getValueFromMap(MapBuildingTemplate.class, getTemplateId());
    }


}

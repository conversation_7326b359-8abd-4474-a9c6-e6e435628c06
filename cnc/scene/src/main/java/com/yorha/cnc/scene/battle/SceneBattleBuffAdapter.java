package com.yorha.cnc.scene.battle;

import com.yorha.cnc.battle.adapter.interfaces.IBattleBuffAdapter;
import com.yorha.cnc.battle.buf.Buff;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBuffComponent;


/**
 * <AUTHOR>
 * @date 2023/5/29
 */
public class SceneBattleBuffAdapter implements IBattleBuffAdapter {
    private final SceneObjBuffComponent component;

    public SceneBattleBuffAdapter(SceneObjBuffComponent component) {
        this.component = component;
    }

    @Override
    public void remove(int groupId) {
        component.remove(groupId);
    }

    @Override
    public void add(Buff buff) {
        component.add(buff);
    }

    @Override
    public void clear() {
        component.clear();
    }
}

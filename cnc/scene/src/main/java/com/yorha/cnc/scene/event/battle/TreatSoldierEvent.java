package com.yorha.cnc.scene.event.battle;

import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.common.utils.eventdispatcher.IEvent;

import java.util.List;

public class TreatSoldierEvent extends IEvent {
    List<SoldierLossDTO> treatData;

    public TreatSoldierEvent(List<SoldierLossDTO> treatData) {
        this.treatData = treatData;
    }

    public List<SoldierLossDTO> getTreatData() {
        return treatData;
    }
}

package com.yorha.cnc.scene.gm.command.city;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryBornPart implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isMainScene()) {
            return;
        }
        int partId = Integer.parseInt(args.get("partId"));
        int count = actor.getScene().getBornMgrComponent().getPartToNum(partId);
        throw new GeminiException(ErrorCode.SYSTEM_WARNING, String.valueOf(count));
    }

    @Override
    public String showHelp() {
        return "QueryBornPart partId={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_SERVER;
    }
}

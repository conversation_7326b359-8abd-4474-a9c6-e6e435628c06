package com.yorha.cnc.scene.areaSkill.bornPoint;

import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Ring;
import res.template.RangeSkillTemplate;

/**
 * 环内随机点
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
public class RandomRingPoint implements IAreaSkillBornPoint {
    @Override
    public Point select(SceneEntity scene, RangeSkillTemplate template, Point basePoint) {
        int innerR = template.getRangeList().get(0);
        int outerR = template.getRangeList().get(1);
        Ring ring = Ring.valueOf(basePoint, outerR, innerR);
        return BornPointHelper.randomRingBornPoint(scene, ring, 0, true);
    }
}

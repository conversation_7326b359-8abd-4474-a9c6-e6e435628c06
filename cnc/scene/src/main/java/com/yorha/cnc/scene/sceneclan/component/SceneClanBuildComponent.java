package com.yorha.cnc.scene.sceneclan.component;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.common.BornPointHelper;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.event.ClanBuiltBuilldingPartChangeEvent;
import com.yorha.cnc.scene.event.mapbuilding.FireSpeedChangeEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.ClanBuildingNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.rebuild.RebuildStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.DesertedStageNode;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.territory.InCommandNetStageNode;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.MapBuildingType;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.MapBuildingTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */
public class SceneClanBuildComponent extends AbstractComponent<SceneClanEntity> {
    private static final Logger LOGGER = LogManager.getLogger(SceneClanBuildComponent.class);
    /**
     * 主基地建筑id到唯一id[1-所允许联盟建筑最大个数]
     */
    private final Map<Long, Integer> mainBaseUniqueId = Maps.newHashMap();
    /**
     * 堡垒占有的地片，用来判断给迁入的玩家城堡加罩子
     */
    private final Set<Integer> fortressPart = new IntOpenHashSet();
    /**
     * 指挥中心个数  已经建设完成的
     */
    private int curCommandCenterNum = 0;
    /**
     * 改建中的地图建筑
     */
    private final Map<Long, MapBuildingEntity> building = new Long2ObjectOpenHashMap<>();
    /**
     * 已经改建好的地图建筑
     */
    private final Map<Long, MapBuildingEntity> alreadyBuilding = new Long2ObjectOpenHashMap<>();
    /**
     * 已经改建好的地图建筑partID
     */
    private final Set<Integer> alreadyBuildingPartIds = Sets.newHashSet();
    /**
     * 已经改建好的军团建筑的 州->partSet
     */
    private final Map<Integer, Set<Integer>> alreadyBuildingRegion2PartId = new Int2ObjectOpenHashMap<>();
    /**
     * 联盟建筑类型->数目  含正在建设中的
     */
    private final Map<Integer, Integer> type2Num = Maps.newHashMap();

    /**
     * 联盟建筑类型->数目  仅含已建设成的
     */
    private final Map<Integer, Integer> alreadyBuiltType2Num = Maps.newHashMap();
    /**
     * 所有被本军团攻击造成燃烧的建筑
     */
    private final Set<Integer> attackCauseFireBuildingSet = new HashSet<>();

    private ClanResBuildingEntity resBuildingEntity = null;

    private long operateLockExpireTsMs = 0L;

    private final long LOCK_EXPIRE_TS_MS = 5 * 1000L;

    public SceneClanBuildComponent(SceneClanEntity owner) {
        super(owner);
    }

    // ------------------------------------------- 军团资源中心 ------------------------------------- //

    /**
     * 将军团资源中心的实体记录在scene clan上
     */
    public void addClanResBuilding(ClanResBuildingEntity resBuildingEntity) {
        this.resBuildingEntity = resBuildingEntity;
    }

    /**
     * 检查是否已有军团资源中心
     */
    public boolean hasClanResBuilding() throws GeminiException {
        return resBuildingEntity != null;
    }

    /**
     * @param type   建筑类型
     * @param p      放置位置的中心点
     * @param clanId 军团id
     * @throws GeminiException
     */
    public void checkCanPlaceClanResBuilding(MapBuildingType type, Struct.Point p, long clanId) throws GeminiException {
        Core.Code code = BornPointHelper.collisionCheck(ownerActor().getScene(),
                p.getX(),
                p.getY(),
                getCollisionRadiusByType(type), getEntityId());
        if (!ErrorCode.isOK(code)) {
            throw new GeminiException(ErrorCode.CLAN_RES_BUILDING_COLLISION);
        }
        int x = p.getX(), y = p.getY();
        int partId = MapGridDataManager.getPartId(getOwner().getSceneEntity().getMapId(), x, y);
        MapBuildingEntity mapBuilding = getOwner().getSceneEntity().getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            LOGGER.info("createClanResBuilding failed, mapBuilding is null, partId:{}, x: {}, y: {}", partId, x, y);
            throw new GeminiException(ErrorCode.MAP_BUILDING_NOT_EXISTS);
        }
        SceneClanEntity ownerSceneClan = mapBuilding.getOccupyComponent().getOwnerSceneClan();
        if (ownerSceneClan == null) {
            LOGGER.info("createClanResBuilding failed, ownerSceneClan is null, partId:{}, x: {}, y: {}", partId, x, y);
            throw new GeminiException(ErrorCode.CLAN_RES_BUILDING_ONLY_CAN_BUILD_IN_OWN_TERRITORY);
        }
        if (clanId != ownerSceneClan.getEntityId()) {
            LOGGER.info("createClanResBuilding failed, clanId not match, partId:{}, x: {}, y: {}", partId, x, y);
            throw new GeminiException(ErrorCode.CLAN_RES_BUILDING_ONLY_CAN_BUILD_IN_OWN_TERRITORY);
        }
    }

    /**
     * 尝试添加建设军团资源中心的锁
     */
    public void tryAddClanResBuildLock() throws GeminiException {
        long now = SystemClock.now();
        if (operateLockExpireTsMs > now) {
            throw new GeminiException(ErrorCode.CLAN_RES_BUILDING_BUILD_LOCKING);
        }
        operateLockExpireTsMs = now + LOCK_EXPIRE_TS_MS;
    }

    /**
     * 移除建设军团资源中心的锁
     */
    public void removeClanResBuildLock() {
        operateLockExpireTsMs = 0L;
    }

    /**
     * @param type 建筑类型
     * @return 根据建筑类型获取建筑碰撞半径
     */
    private int getCollisionRadiusByType(MapBuildingType type) {
        for (MapBuildingTemplate template : ResHolder.getInstance().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getType() == type) {
                return template.getCollisionRadius();
            }
        }
        LOGGER.error("ClanResBuilding: can not find template for type {}", type);
        // 应该假定是一定能取到的
        return 0;
    }

    public void removeClanResBuilding() {
        resBuildingEntity = null;
    }

    public void gmRemoveClanResBuilding() {
        if (resBuildingEntity != null) {
            resBuildingEntity.getCollectComponent().gmCollectFinish();
            resBuildingEntity = null;
        }
    }

    public CommonMsg.ClanResBuildingInfo.Builder getClanResBuildingPage() {
        if (resBuildingEntity == null) {
            return null;
        }
        CommonMsg.ClanResBuildingInfo.Builder retBuilder = CommonMsg.ClanResBuildingInfo.newBuilder();
        retBuilder.setEntityId(resBuildingEntity.getEntityId())
                .setTemplateId(resBuildingEntity.getTemplateId())
                .setPoint(StructPB.PointPB.newBuilder().setX(resBuildingEntity.getProp().getPoint().getX())
                        .setY(resBuildingEntity.getProp().getPoint().getY()))
                .setState(resBuildingEntity.getProp().getState())
                .setProgressInfo(resBuildingEntity.getProgressInfoPBBuilder())
                .setZoneId(getOwner().getSceneEntity().getZoneId());
        return retBuilder;
    }

    // ------------------------------------------- 军团建筑 ------------------------------------- //

    /**
     * 重启时恢复 添加到重建中
     */
    public void restoreBuilding(MapBuildingEntity mapBuildingEntity) {
        building.put(mapBuildingEntity.getEntityId(), mapBuildingEntity);
        MapBuildingType type = mapBuildingEntity.getBuildComponent().getType();
        type2Num.put(type.getNumber(), type2Num.getOrDefault(type.getNumber(), 0) + 1);
    }

    /**
     * 重启时恢复 添加到改建完成
     */
    public void restoreBuild(MapBuildingEntity mapBuilding) {
        addBuiltBuilding(mapBuilding);
        long entityId = mapBuilding.getEntityId();
        MapBuildingType type = mapBuilding.getBuildComponent().getType();
        switch (type) {
            case MBT_MAIN_BASE:
                mainBaseUniqueId.put(entityId, mapBuilding.getBuildComponent().getMainBaseNum());
                break;
            case MBT_COMMAND_CENTER:
                curCommandCenterNum += 1;
                break;
            case MBT_CLAN_FORTRESS:
                fortressPart.add(mapBuilding.getPartId());
                break;
            default:
                LOGGER.error("{} {} stage is build but type error. type:{}", getOwner(), mapBuilding, type);
        }
        type2Num.put(type.getNumber(), type2Num.getOrDefault(type.getNumber(), 0) + 1);
    }

    public int getCurCommandCenterNum() {
        return curCommandCenterNum;
    }

    /**
     * 添加到重建中
     */
    public void addToRebuilding(MapBuildingEntity mapBuilding) {
        building.put(mapBuilding.getEntityId(), mapBuilding);
        LOGGER.info("{} start rebuild {}", getOwner(), mapBuilding);
        // 缓存计数
        int type = mapBuilding.getBuildComponent().getType().getNumber();
        type2Num.put(type, type2Num.getOrDefault(type, 0) + 1);
        // 同步给联盟那边
        syncClanBuildingToClan(type);
    }

    /**
     * 中断正在改建的建筑
     */
    public void stopRebuilding(long mapBuildingId) {
        MapBuildingEntity mapBuilding = building.remove(mapBuildingId);
        if (mapBuilding == null) {
            return;
        }
        int type = mapBuilding.getBuildComponent().getType().getNumber();
        type2Num.put(type, type2Num.get(type) - 1);
        // 同步给联盟那边
        syncClanBuildingToClan(type);
    }

    private void addBuiltBuilding(MapBuildingEntity mapBuilding) {
        long entityId = mapBuilding.getEntityId();
        int buildingType = mapBuilding.getBuildComponent().getType().getNumber();
        building.remove(entityId);
        alreadyBuilding.put(entityId, mapBuilding);
        alreadyBuildingPartIds.add(mapBuilding.getPartId());
        alreadyBuiltType2Num.put(buildingType, alreadyBuiltType2Num.getOrDefault(buildingType, 0) + 1);
        this.getOwner().getEventDispatcher().dispatch(new ClanBuiltBuilldingPartChangeEvent(this.getEntityId(), this.getOwner().getProp().getClanId(), mapBuilding.getPartId(), true));

        // 处理黑暗祭坛那边的相关逻辑
        int regionId = MapGridDataManager.getRegionId(getOwner().getSceneEntity().getMapId(), mapBuilding.getCurPoint());
        Set<Integer> set = alreadyBuildingRegion2PartId.get(regionId);
        if (set == null) {
            alreadyBuildingRegion2PartId.computeIfAbsent(regionId, k -> new HashSet<>()).add(mapBuilding.getPartId());
            return;
        }
        set.add(mapBuilding.getPartId());
    }

    private MapBuildingEntity removeBuiltBuilding(long mapBuildingId) {
        MapBuildingEntity mapBuilding = alreadyBuilding.remove(mapBuildingId);
        if (mapBuilding == null) {
            return null;
        }
        MapBuildingType type = mapBuilding.getBuildComponent().getType();
        alreadyBuiltType2Num.put(type.getNumber(), alreadyBuiltType2Num.getOrDefault(type.getNumber(), 1) - 1);
        this.getOwner().getEventDispatcher().dispatch(new ClanBuiltBuilldingPartChangeEvent(this.getEntityId(), this.getOwner().getProp().getClanId(), mapBuilding.getPartId(), false));
        alreadyBuildingPartIds.remove(mapBuilding.getPartId());

        // 处理黑暗祭坛那边的相关逻辑
        int regionId = MapGridDataManager.getRegionId(getOwner().getSceneEntity().getMapId(), mapBuilding.getCurPoint());
        Set<Integer> set = alreadyBuildingRegion2PartId.get(regionId);
        if (set != null) {
            set.remove(mapBuilding.getPartId());
            if (set.isEmpty()) {
                alreadyBuildingRegion2PartId.remove(regionId);
            }
        } else {
            LOGGER.error("remove from alreadyBuildingRegion2PartId but nit exist {}", mapBuildingId);
        }
        return mapBuilding;
    }

    public boolean isInBuiltBuildingPart(int partId) {
        return this.alreadyBuildingPartIds.contains(partId);
    }

    /**
     * 添加到已重建完成   指挥网 罩子等后处理
     */
    public void addToAlreadyRebuilt(MapBuildingEntity mapBuilding) {
        addBuiltBuilding(mapBuilding);
        long entityId = mapBuilding.getEntityId();
        int buildingType = mapBuilding.getBuildComponent().getType().getNumber();
        LOGGER.info("{} add to build finish {}", getOwner(), mapBuilding);
        boolean isNeedUpdateBuff = false;
        switch (mapBuilding.getBuildComponent().getType()) {
            case MBT_MAIN_BASE:
                // 主基地需要申领唯一的指挥网id
                mainBaseUniqueId.put(entityId, getUniqueMainBaseId());
                isNeedUpdateBuff = true;
                break;
            case MBT_COMMAND_CENTER:
                // 更新指挥中心个数 并判断是否需要更新buff
                isNeedUpdateBuff = updateCommandCenterNum(true);
                break;
            case MBT_CLAN_FORTRESS:
                fortressPart.add(mapBuilding.getPartId());
                // 给周围的加罩子
                changeClanFortressSafeGuard(mapBuilding, true);
                break;
            default:
                LOGGER.error("{} {} try add build finish ?", getOwner(), mapBuilding);
        }
        // 尝试加入指挥网
        long calLinkNetWorkStart = SystemClock.nowNative();
        boolean isInCommandNet = tryAddInToCommandNet(mapBuilding);
        // 同步军团建筑个数
        syncClanBuildingToClan(buildingType);
        // 势力值变更
        getOwner().getMapBuildingComponent().onPowerChange(mapBuilding, MapBuildingType.MBT_NONE, mapBuilding.getBuildComponent().getType());
        // buff变更 发送快照
        isNeedUpdateBuff |= isInCommandNet;
        if (isNeedUpdateBuff) {
            getOwner().getMapBuildingComponent().sendTerritoryBuffSnapshot();
        }
        long calLinkNetWorkTsMs = SystemClock.nowNative() - calLinkNetWorkStart;
        // 连入指挥网耗时监控
        LOGGER.info("SceneClanBuildComponent addToAlreadyRebuilt calCommandNet cost {}, clanId {}, mapBuilding {}", calLinkNetWorkTsMs, getOwner().getEntityId(), mapBuilding);
    }

    /**
     * 销毁改建建筑 返回是否需要更新buff
     */
    public boolean destroyBuild(long mapBuildingId) {
        MapBuildingEntity mapBuilding = removeBuiltBuilding(mapBuildingId);
        if (mapBuilding == null) {
            LOGGER.error("destroyBuild: mapBuilding {} is null", mapBuildingId);
            return false;
        }
        LOGGER.info("{} destroyBuild {}", getOwner(), mapBuilding);
        MapBuildingType type = mapBuilding.getBuildComponent().getType();
        boolean isNeedUpdateBuff = false;
        switch (type) {
            case MBT_MAIN_BASE:
                isNeedUpdateBuff = true;
                break;
            case MBT_COMMAND_CENTER:
                isNeedUpdateBuff = updateCommandCenterNum(false);
                break;
            case MBT_CLAN_FORTRESS:
                fortressPart.remove(mapBuilding.getPartId());
                changeClanFortressSafeGuard(mapBuilding, false);
                break;
            default:
                LOGGER.error("{} {} try add build finish ?", getOwner(), mapBuilding);
        }
        // 在指挥网的建筑拆除时，需要将周围受影响的建筑从指挥网中去掉
        boolean isConnectedToCommandNet = mapBuilding.getProp().getConstructInfo().getIsConnectedToCommandNet();
        if (isConnectedToCommandNet) {
            // 拆除一个在指挥网的建筑时，需要删除连入指挥网的buff，恢复改建前的buff，未连入指挥网的联盟建筑无需修改buff
            deleteFromCommandNet(mapBuilding);
            isNeedUpdateBuff = true;
        }
        // 减少记录数据，同步给联盟
        int num = type2Num.getOrDefault(type.getNumber(), 0);
        if (num <= 0) {
            LOGGER.error("try destroy build {}, num {} not right", mapBuildingId, num);
        }
        type2Num.put(type.getNumber(), Math.max(num - 1, 0));
        // 同步军团建筑个数
        syncClanBuildingToClan(mapBuilding.getBuildComponent().getType().getNumber());
        // 势力值变更
        getOwner().getMapBuildingComponent().onPowerChange(mapBuilding, mapBuilding.getBuildComponent().getType(), MapBuildingType.MBT_NONE);
        return isNeedUpdateBuff;
    }

    /**
     * 获取唯一指挥网id
     */
    int getUniqueMainBaseId() {
        TerritoryBuildingTemplate template = null;
        Collection<TerritoryBuildingTemplate> listTemplate = ResHolder.getInstance().getListFromMap(TerritoryBuildingTemplate.class);
        for (TerritoryBuildingTemplate t : listTemplate) {
            if (t.getType() == MapBuildingType.MBT_MAIN_BASE) {
                template = t;
                break;
            }
        }
        if (null == template) {
            LOGGER.error("wrong config, please check territory building template");
            return 0;
        }
        int limit = template.getLimit();
        for (int no = 1; no <= limit; ++no) {
            if (!mainBaseUniqueId.values().contains(no)) {
                return no;
            }
        }
        LOGGER.error("all number have been used, please check limit for main base");
        return 0;
    }

    /**
     * 广度搜索，跟据配表获取相邻地块建筑，将广度搜索到的建筑从指挥网中删除
     */
    public void deleteFromCommandNet(MapBuildingEntity mapBuilding) {
        long mapBuildingId = mapBuilding.getEntityId();
        int partId = mapBuilding.getPartId();
        // 是主基地 直接删就行了
        if (mainBaseUniqueId.containsKey(mapBuildingId)) {
            changeBuffFromMainBase(mapBuildingId, partId, false, mapBuildingId);
            return;
        }
        SceneEntity sceneEntity = getOwner().getSceneEntity();
        int aroundBuildingNum = 0;
        // 当前建筑不是主基地的情况下，需要判断是否为中间节点
        RegionalAreaSettingTemplate regionalAreaSettingTemplate = mapBuilding.getAreaSettingTemplate();
        for (int adjoinPartId : regionalAreaSettingTemplate.getAdjoinAreaIdList()) {
            MapBuildingEntity adJoinMapBuilding = sceneEntity.getBuildingMgrComponent().getMapBuilding(adjoinPartId);
            // 建筑不存在 或建筑不是本联盟的
            if (adJoinMapBuilding == null || adJoinMapBuilding.getOwnerClanId() != getEntityId()) {
                continue;
            }
            // 如果不是在指挥网的 那也不用
            if (!adJoinMapBuilding.isInCommandNet()) {
                continue;
            }
            ++aroundBuildingNum;
        }
        // 边缘节点脱离指挥网，无需处理，直接返回，交给上层去增删buff
        if (aroundBuildingNum == 1) {
            return;
        }
        // 刷新指挥网
        refreshCommandNetByIntermediateNode();
    }

    /**
     * 判断新增联盟建筑是否新建或加入到指挥网中
     */
    public boolean tryAddInToCommandNet(MapBuildingEntity thisMapBuilding) {
        long mapBuildingId = thisMapBuilding.getEntityId();
        int partId = thisMapBuilding.getPartId();
        MapBuildingType type = thisMapBuilding.getProp().getConstructInfo().getType();
        // 联盟主基地自成指挥网
        if (type == MapBuildingType.MBT_MAIN_BASE) {
            // 设置下自身属性
            thisMapBuilding.getBuildComponent().setCommandNetMainNo(mainBaseUniqueId.get(mapBuildingId));
            // 广度搜索 修改以这个为源头形成的指挥网
            changeBuffFromMainBase(mapBuildingId, partId, true, 0L);
            return true;
        }
        // 找下周围连接入指挥的个数
        int aroundBuildingNum = 0;
        RegionalAreaSettingTemplate regionalAreaSettingTemplate = thisMapBuilding.getAreaSettingTemplate();
        for (int adjoinPartId : regionalAreaSettingTemplate.getAdjoinAreaIdList()) {
            MapBuildingEntity adJoinMapBuilding = getOwner().getSceneEntity().getBuildingMgrComponent().getMapBuilding(adjoinPartId);
            // 建筑不存在 或建筑不是本联盟的
            if (adJoinMapBuilding == null || adJoinMapBuilding.getOwnerClanId() != getEntityId()) {
                continue;
            }
            // 如果不是在指挥网的 那也不用
            if (!adJoinMapBuilding.isInCommandNet()) {
                continue;
            }
            ++aroundBuildingNum;
        }
        // 周围没有在网里的
        if (aroundBuildingNum == 0) {
            return false;
        }
        // 刷新指挥网
        refreshCommandNetByIntermediateNode();
        return thisMapBuilding.getProp().getConstructInfo().getIsConnectedToCommandNet();
    }

    /**
     * 指挥中心和堡垒变更时刷新指挥网
     */
    public void refreshCommandNetByIntermediateNode() {
        // 需要记录当前所有建筑指挥网字段，并清空指挥网字段
        Map<Long, Integer> buildingIdToAffected = new HashMap<>();
        SceneClanMapBuildingComponent mapBuildingComponent = getOwner().getMapBuildingComponent();
        for (MapBuildingEntity building : mapBuildingComponent.getOwnMapBuildings()) {
            long entityId = building.getEntityId();
            buildingIdToAffected.put(entityId, building.getBuildComponent().getAffectedByWhichMainBase());
            building.getBuildComponent().setCommandNetMainNo(mainBaseUniqueId.getOrDefault(entityId, 0));
        }
        // 从每个主基地开始更新指挥网字段
        for (Long buildingId : mainBaseUniqueId.keySet()) {
            changeBuffFromMainBase(buildingId, alreadyBuilding.get(buildingId).getPartId(), true, buildingId);
        }
        Set<Long> changedBuilding = new HashSet<>();
        // 有变化的建筑记录一下
        for (Long buildingId : buildingIdToAffected.keySet()) {
            MapBuildingEntity curMapBuilding = mapBuildingComponent.getOwnMapBuilding(buildingId);
            int newAffectedByWhichMainBase = curMapBuilding.getBuildComponent().getAffectedByWhichMainBase();
            // 检查指挥网状态是否有变化
            boolean commandNetChanged = isCommandNetChanged(newAffectedByWhichMainBase, buildingIdToAffected.get(buildingId));
            if (commandNetChanged) {
                changedBuilding.add(buildingId);
            }
        }
        LOGGER.info("refreshCommandNet clan={} changed={}", getEntityId(), changedBuilding);
    }

    /**
     * 判断指挥网状态是否发生改变
     *
     * @param newFlag 当前新的指挥网状态
     * @param oldFlag 旧的指挥网状态
     * @return 是否发生指挥网改变
     */
    private boolean isCommandNetChanged(int newFlag, int oldFlag) {
        // 前后flag相同，无变化
        if (oldFlag == newFlag) {
            return false;
        }
        // 前后flag不同，与结果不为零，代表指挥网始终为有指挥网链接的状态，无变化
        // flag不相同，且指挥网与为0，状态发生变化
        return (oldFlag & newFlag) == 0;
    }

    /**
     * 从特定主基地出发使用广度搜索逐步修改周围建筑的指挥网信息，更改实际生效buff或者仅更改指挥网状态
     *
     * @param mapBuildingId 主基地的 mapBuilding id
     * @param partId        主基地所在partId
     * @param isAdd         当前遍历的主基地是新增的主基地，还是即将被删除的主基地，用于判断是增删哪一部分的buff
     * @param needIgnoreId  需要忽略的 mapBuilding id
     */
    private void changeBuffFromMainBase(long mapBuildingId, int partId, boolean isAdd, long needIgnoreId) {
        Set<Long> alreadyVisited = new HashSet<>();
        Queue<Integer> currentVisit = new LinkedList<>();
        alreadyVisited.add(mapBuildingId);
        if (needIgnoreId != 0) {
            alreadyVisited.add(needIgnoreId);
        }
        currentVisit.add(partId);
        SceneEntity sceneEntity = getOwner().getSceneEntity();
        int uniqueId = mainBaseUniqueId.get(mapBuildingId);
        MapTemplateDataItem templateDataItem = getOwner().getSceneEntity().getMapTemplateDataItem();
        while (!currentVisit.isEmpty()) {
            int curPartId = currentVisit.poll();
            RegionalAreaSettingTemplate regionalAreaSettingTemplate = templateDataItem.getValueFromMap(RegionalAreaSettingTemplate.class, curPartId);
            // 遍历相邻的片 判断要不要加入指挥网
            for (int adjoinPartId : regionalAreaSettingTemplate.getAdjoinAreaIdList()) {
                MapBuildingEntity adJoinMapBuilding = sceneEntity.getBuildingMgrComponent().getMapBuilding(adjoinPartId);
                // 建筑不存在或建筑已经被访问时跳过
                if (adJoinMapBuilding == null || alreadyVisited.contains(adJoinMapBuilding.getEntityId())) {
                    continue;
                }
                long entityId = adJoinMapBuilding.getEntityId();
                // 建筑  不是已经改建好的  而且  不是当前拥有的城市   不会连入指挥网,跳过
                if (!alreadyBuilding.containsKey(entityId) && !getOwner().getMapBuildingComponent().isOwnCity(entityId)) {
                    continue;
                }
                // 在已访问节点中添加entityId，在要访问的节点中增加对应的partId
                alreadyVisited.add(entityId);
                currentVisit.add(adJoinMapBuilding.getPartId());
                boolean isConnectedToCommandNet = adJoinMapBuilding.getProp().getConstructInfo().getIsConnectedToCommandNet();
                if (isAdd) {
                    // 添加新建筑逻辑中，临近建筑已经加入指挥网，需要更新受到主基地影响的字段
                    adJoinMapBuilding.getBuildComponent().changeAffectedByWhichMainBase(uniqueId, true);
                    // 想增加 且之前未连入指挥网
                    if (!isConnectedToCommandNet) {
                        adJoinMapBuilding.getBuildComponent().setCommandNet(true);
                        // 城市新增指挥网，且城市已经处于荒废状态，需要将城市设置为不荒废状态。
                        if (GameLogicConstants.isMapCity(adJoinMapBuilding.getAreaType()) && adJoinMapBuilding.getOccupyState() == CommonEnum.OccupyState.TOS_DESERTED) {
                            adJoinMapBuilding.getStageMgrComponent().transNewNode(new InCommandNetStageNode(adJoinMapBuilding));
                        }
                    }
                } else {
                    // 原本就没有连进来
                    if (!isConnectedToCommandNet) {
                        continue;
                    }
                    // 更新受到主基地影响的字段
                    int affectedBase = adJoinMapBuilding.getBuildComponent().changeAffectedByWhichMainBase(uniqueId, false);
                    if (affectedBase == 0) {
                        adJoinMapBuilding.getBuildComponent().setCommandNet(false);
                    }
                    // 城市删除指挥网，且城市已经处于连入指挥网状态，需要将城市设置为荒废状态。
                    if (GameLogicConstants.isMapCity(adJoinMapBuilding.getAreaType()) && adJoinMapBuilding.getOccupyState() == CommonEnum.OccupyState.TOS_CITY_IN_COMMAND_NET) {
                        adJoinMapBuilding.getStageMgrComponent().transNewNode(new DesertedStageNode(adJoinMapBuilding));
                    }
                }
            }
        }
        if (!isAdd) {
            mainBaseUniqueId.remove(mapBuildingId);
        }
    }

    /**
     * 新获得一个地图建筑  检查下是不是连入指挥网/加罩子 刷buff
     */
    public void afterOwnNewMapBuilding(MapBuildingEntity entity) {
        if (GameLogicConstants.isMapCity(entity.getAreaType())) {
            // 城市需要额外判断是否在指挥网中
            tryAddInToCommandNet(entity);
        }
        // 刷buff
        getOwner().getMapBuildingComponent().sendTerritoryBuffSnapshot();

        // 建筑是邻近堡垒的情况下，需要给建筑和建筑上的所有军团成员的主城添加军团堡垒罩子
        RegionalAreaSettingTemplate regionalAreaSettingTemplate = entity.getAreaSettingTemplate();
        for (int adjoinPartId : regionalAreaSettingTemplate.getAdjoinAreaIdList()) {
            if (fortressPart.contains(adjoinPartId)) {
                // 初始领地和城市关隘是不加的
                if (!GameLogicConstants.isMapCity(entity.getAreaType())) {
                    entity.getSpecialSafeGuardComponent().specialSafeGuardChange(true, CommonEnum.SafeGuardReason.SGR_CLAN_FORTRESS);
                }
                changeCitySafeGuard(getOwner().getSceneEntity(), entity.getPartId(), true);
                return;
            }
        }
    }

    public void afterLoseMapBuilding(MapBuildingEntity entity) {
        if (entity.getProp().getConstructInfo().getIsConnectedToCommandNet()) {
            getOwner().getBuildComponent().deleteFromCommandNet(entity);
            entity.getProp().getConstructInfo().setIsConnectedToCommandNet(false);
        }
        // 刷buff
        getOwner().getMapBuildingComponent().sendTerritoryBuffSnapshot();

        // 失去的时候 需要尝试移除
        changeMapBuildingSafeGuard(getOwner().getSceneEntity(), entity.getPartId(), false);
        changeCitySafeGuard(getOwner().getSceneEntity(), entity.getPartId(), false);
    }

    /**
     * 判断是否有堡垒建在当前建筑附近
     */
    public boolean isFortressNearBy(Point newFortressPoint) {
        ConstClanTerritoryTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
        int forMinDis = constTemplate.getFortMinDis();
        for (MapBuildingEntity buildingEntity : alreadyBuilding.values()) {
            if (buildingEntity.getProp().getConstructInfo().getType() != MapBuildingType.MBT_CLAN_FORTRESS) {
                continue;
            }
            Point buildingPoint = buildingEntity.getCurPoint();
            double twoPointDis = Point.calDisBetweenTwoPoint(Point.valueOf(buildingPoint.getX(), buildingPoint.getY()),
                    Point.valueOf(newFortressPoint.getX(), newFortressPoint.getY()));
            if (twoPointDis < forMinDis) {
                return true;
            }
        }
        for (MapBuildingEntity buildingEntity : building.values()) {
            if (buildingEntity.getProp().getConstructInfo().getType() != MapBuildingType.MBT_CLAN_FORTRESS) {
                continue;
            }
            Point buildingPoint = buildingEntity.getCurPoint();
            double twoPointDis = Point.calDisBetweenTwoPoint(Point.valueOf(buildingPoint.getX(), buildingPoint.getY()),
                    Point.valueOf(newFortressPoint.getX(), newFortressPoint.getY()));
            if (twoPointDis < forMinDis) {
                return true;
            }
        }
        return false;
    }

    public int getCommandCenterExtraBuffLayers() {
        // 获取策划期望添加一层指挥中心数量的配置
        ConstClanTerritoryTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
        int commandCenterNumToGetBuff = template.getCommandCenterNumToGetBuff();
        return curCommandCenterNum / commandCenterNumToGetBuff;
    }

    /**
     * 更新指挥中心的数量
     *
     * @param isAdd 是否是新增
     * @return 是否需要更新buff
     */
    private boolean updateCommandCenterNum(boolean isAdd) {
        LOGGER.info("curCommandCenterNum is {}, isAdd {}", curCommandCenterNum, isAdd);
        final int afterCommandCenterNum = isAdd ? curCommandCenterNum + 1 : curCommandCenterNum - 1;
        // 获取策划期望添加一层指挥中心数量的配置
        ConstClanTerritoryTemplate template = ResHolder.getInstance().getConstTemplate(ConstClanTerritoryTemplate.class);
        int commandCenterNumToGetBuff = template.getCommandCenterNumToGetBuff();

        // 比较层数是否发生变化
        final int beforeLayer = curCommandCenterNum / commandCenterNumToGetBuff;
        final int afterLayer = afterCommandCenterNum / commandCenterNumToGetBuff;
        // 更新当前指挥中心数量
        curCommandCenterNum = afterCommandCenterNum;
        return beforeLayer != afterLayer;
    }

    public int getMainBaseExtraBuffLayers() {
        return mainBaseUniqueId.size();
    }

    /**
     * 联盟解散
     */
    public void onClanDismiss() {
        // 联盟解散时去除所有堡垒所在地片周围建筑及玩家堡垒身上的联盟护盾
        SceneEntity sceneEntity = getOwner().getSceneEntity();
        for (int partId : fortressPart) {
            changeClanFortressSafeGuard(sceneEntity.getBuildingMgrComponent().getMapBuilding(partId), false);
        }
        building.clear();
        alreadyBuilding.clear();
        alreadyBuiltType2Num.clear();
        mainBaseUniqueId.clear();
        fortressPart.clear();
        if (resBuildingEntity != null) {
            resBuildingEntity.getCollectComponent().onClanDismiss();
            resBuildingEntity = null;
        }
        LOGGER.info("{} clanBuilding on dismiss", getOwner());
    }

    /**
     * 为联盟堡垒附近地块的建筑添加、删除联盟堡垒护罩
     */
    public void changeClanFortressSafeGuard(MapBuildingEntity mapBuilding, boolean isOn) {
        RegionalAreaSettingTemplate regionalAreaSettingTemplate = mapBuilding.getAreaSettingTemplate();
        SceneEntity sceneEntity = getOwner().getSceneEntity();
        // 遍历当前地块玩家主堡，添加堡垒护罩
        changeCitySafeGuard(sceneEntity, mapBuilding.getPartId(), isOn);
        // 遍历邻近地块地图建筑及玩家主堡，添加堡垒护罩
        for (int adjoinPartId : regionalAreaSettingTemplate.getAdjoinAreaIdList()) {
            // 军团未时间占有邻近地块时，不执行给其上主堡或建筑增加军团堡垒护罩的操作
            // 同理删除时，由于军团堡垒仅对已拥有地块增加保护，非军团拥有地块无需判断
            if (!getOwner().getMapBuildingComponent().isOwnPart(adjoinPartId)) {
                continue;
            }
            changeMapBuildingSafeGuard(sceneEntity, adjoinPartId, isOn);
            changeCitySafeGuard(sceneEntity, adjoinPartId, isOn);
        }
    }

    /**
     * 给建筑添加、删除罩子
     */
    private void changeMapBuildingSafeGuard(SceneEntity sceneEntity, int partId, boolean isOn) {
        // 初始领地和城市关隘是不加的
        MapBuildingEntity mapBuilding = sceneEntity.getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            LOGGER.debug("find map building is null in {} while changing safe guard", partId);
            return;
        }
        // 初始领地和城市关隘是不加的
        if (GameLogicConstants.isMapCity(mapBuilding.getAreaType())) {
            return;
        }
        mapBuilding.getSpecialSafeGuardComponent().specialSafeGuardChange(isOn, CommonEnum.SafeGuardReason.SGR_CLAN_FORTRESS);
    }

    /**
     * 给玩家城池添加、删除罩子
     */
    private void changeCitySafeGuard(SceneEntity sceneEntity, int partId, boolean isOn) {
        // 地块内玩家主堡，添加来自联盟堡垒的保护罩子
        List<Integer> gridList = MapGridDataManager.getGridList(sceneEntity.getMapId(), partId);
        if (null == gridList) {
            LOGGER.warn("gridList is null during safe guard change");
            return;
        }
        for (Integer grid : gridList) {
            CityEntity city = sceneEntity.getBornMgrComponent().getOccupiedCity(grid);
            if (city == null) {
                // 当前grid上无玩家建筑
                continue;
            }
            // 对应盟的玩家才要修改
            if (city.getClanId() == getEntityId()) {
                city.getSpecialSafeGuardComponent().specialSafeGuardChange(isOn, CommonEnum.SafeGuardReason.SGR_CLAN_FORTRESS);
            }
        }
    }

    /**
     * 判断传入地块是否在联盟堡垒可以保护的地块
     */
    public boolean isInFortressProtectPart(int partId) {
        if (fortressPart.contains(partId)) {
            return true;
        }
        RegionalAreaSettingTemplate template = getOwner().getSceneEntity().getMapTemplateDataItem().getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        for (int adjoinPartId : template.getAdjoinAreaIdList()) {
            // 堡垒在邻近区域，并且当前地块归属于军团
            if (fortressPart.contains(adjoinPartId) && getOwner().getMapBuildingComponent().isOwnPart(partId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 同步当前的联盟建筑给联盟那边
     */
    public void syncClanBuildingToClan(int buildingType) {
        SsClanTerritory.SyncTerritoryInfoCmd.Builder cmd = SsClanTerritory.SyncTerritoryInfoCmd.newBuilder()
                .setBuildingType(buildingType)
                .setAlreadyBuiltNum(alreadyBuiltType2Num.getOrDefault(buildingType, 0))
                .setTotalNum(type2Num.getOrDefault(buildingType, 0));

        getOwner().tellClan(cmd.build());
    }

    /**
     * 唯一的军团主基地是否仍处于建设中
     */
    public boolean isUniqueMainBaseStillBuild() {
        // 已经成功建设的建筑不为0或者正在建设的建筑不为1时
        if (alreadyBuilding.size() != 0 || building.size() != 1) {
            return false;
        }
        // 获取到唯一在建设中的建筑
        for (MapBuildingEntity mapBuildingEntity : building.values()) {
            if (mapBuildingEntity == null) {
                return false;
            }
            // 当前在建设中的建筑不是主基地
            return mapBuildingEntity.getProp().getConstructInfo().getType() == MapBuildingType.MBT_MAIN_BASE;
        }
        LOGGER.error("building size is 1, where try load failed for check clan {}'s building", getOwner().getEntityId());
        return false;
    }

    /**
     * 是否是建设中的主基地
     */
    public boolean isStillBuildMainBase(int partId) {
        MapBuildingEntity mapBuilding = getOwner().getSceneEntity().getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            return false;
        }
        if (!building.containsKey(mapBuilding.getEntityId())) {
            return false;
        }
        return mapBuilding.getProp().getConstructInfo().getType() == MapBuildingType.MBT_MAIN_BASE;
    }

    /**
     * 建筑速度加成变化的处理方法
     *
     * @param sceneClanEntity 场景军团实体
     * @param additionId      加成id
     * @param additionValue   加成数值
     */
    public static void onConstructSpeedAdditionChange(SceneClanEntity sceneClanEntity, int additionId, long additionValue) {
        boolean isAllBuildSpeedAddition = false;
        if (additionId == CommonEnum.BuffEffectType.ET_BUILD_CLAN_BUILDING_SPEED_PERCENT_VALUE) {
            isAllBuildSpeedAddition = true;
        }
        MapBuildingType targetType = AdditionConstants.BUILDING_SPEED_EFFECT_TYPE_INT_CLAN_BUILDING_TYPE_MAP.get(additionId);
        if (targetType == null && !isAllBuildSpeedAddition) {
            LOGGER.error("wrong addition {} have been dispatch", additionId);
            return;
        }
        Map<Long, MapBuildingEntity> constructingBuildings = sceneClanEntity.getBuildComponent().getConstructingBuilding();
        for (MapBuildingEntity mapBuilding : constructingBuildings.values()) {
            if (isAllBuildSpeedAddition || targetType == mapBuilding.getProp().getConstructInfo().getType()) {
                StageNode stageNode = mapBuilding.getStageMgrComponent().getStageNode();
                if (stageNode.getStage() != CommonEnum.OccupyState.TOS_REBUILD) {
                    continue;
                }
                ((RebuildStageNode) stageNode).onRebuildSpeedChange(null);
            }
        }
    }

    /**
     * 最大耐久度加成变化的处理方法
     *
     * @param sceneClanEntity 场景军团实体
     * @param additionId      加成id
     * @param additionValue   加成数值
     */
    public static void onMaxHpAdditionChange(SceneClanEntity sceneClanEntity, int additionId, long additionValue) {
        boolean isAllBuildHpAddition = false;
        if (additionId == CommonEnum.BuffEffectType.ET_CLAN_BUILDING_HP_MAX_PERCENT_VALUE) {
            isAllBuildHpAddition = true;
        }
        MapBuildingType targetType = AdditionConstants.MAX_HP_EFFECT_TYPE_INT_CLAN_BUILDING_TYPE_MAP.get(additionId);
        if (targetType == null && !isAllBuildHpAddition) {
            LOGGER.error("wrong addition {} have been dispatch", additionId);
            return;
        }
        Map<Long, MapBuildingEntity> builtBuilding = sceneClanEntity.getBuildComponent().getBuiltBuilding();
        for (MapBuildingEntity mapBuilding : builtBuilding.values()) {
            if (isAllBuildHpAddition || targetType == mapBuilding.getProp().getConstructInfo().getType()) {
                if (!mapBuilding.isClanBuilding()) {
                    continue;
                }
                StageNode stageNode = mapBuilding.getStageMgrComponent().getStageNode();
                ((ClanBuildingNode) stageNode).onRefreshMaxDurability();
            }
        }
    }

    /**
     * 建筑速度加成变化的处理方法
     *
     * @param sceneClanEntity 场景军团实体
     * @param additionId      加成id
     * @param additionValue   加成数值
     */
    public static void onFireSpeedChange(SceneClanEntity sceneClanEntity, int additionId, long additionValue) {
        for (int partId : sceneClanEntity.getBuildComponent().getAttackCauseFireBuildingSet()) {
            MapBuildingEntity mapBuilding = sceneClanEntity.getSceneEntity().getBuildingMgrComponent().getMapBuilding(partId);
            if (null == mapBuilding) {
                LOGGER.error("get mapBuilding {} failed when try change its fire speed", partId);
                continue;
            }
            mapBuilding.getEventDispatcher().dispatch(new FireSpeedChangeEvent(additionValue));
        }
    }


    public void addBuildingWhenTriggerFire(int partId) {
        if (attackCauseFireBuildingSet.contains(partId)) {
            LOGGER.error("duplicate add map building {} in set", partId);
            return;
        }
        attackCauseFireBuildingSet.add(partId);
    }

    public void removeBuildingWhenTriggerFire(int partId) {
        if (attackCauseFireBuildingSet.contains(partId)) {
            attackCauseFireBuildingSet.remove(partId);
            return;
        }
        LOGGER.error("cannot find map building {} in set when try remove", partId);
    }

    public Map<Long, MapBuildingEntity> getConstructingBuilding() {
        return building;
    }

    public Map<Long, MapBuildingEntity> getBuiltBuilding() {
        return alreadyBuilding;
    }

    public Set<Integer> getAttackCauseFireBuildingSet() {
        return attackCauseFireBuildingSet;
    }

    public void gmCompleteBuild() {
        for (MapBuildingEntity entity : new ArrayList<>(building.values())) {
            entity.getStageMgrComponent().gmCompleteBuild();
        }
        if (resBuildingEntity != null && resBuildingEntity.getState() == CommonEnum.ClanResBuildingStage.CRBS_BUILD) {
            resBuildingEntity.getBuildComponent().time2BuildFinish();
        }
    }


    public int getBuildingNum(MapBuildingType type) {
        return type2Num.getOrDefault(type.getNumber(), 0);
    }

    @Override
    public SceneActor ownerActor() {
        return getOwner().ownerActor();
    }

    public void onClanBaseChange() {
        if (resBuildingEntity != null) {
            resBuildingEntity.updateClanFlag(getOwner());
        }
    }
}

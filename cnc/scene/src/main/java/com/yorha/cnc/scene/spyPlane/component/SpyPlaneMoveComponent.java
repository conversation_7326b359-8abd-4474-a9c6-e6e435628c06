package com.yorha.cnc.scene.spyPlane.component;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.event.MoveEndEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;
import com.yorha.cnc.scene.sceneObj.move.IMoveArriveHandler;
import com.yorha.cnc.scene.sceneObj.move.IMoveTargetLoseHandler;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.MoveProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;
import res.template.SpyPlaneTemplate;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class SpyPlaneMoveComponent extends SceneObjMoveComponent {
    private static final Logger LOGGER = LogManager.getLogger(SpyPlaneMoveComponent.class);

    public SpyPlaneMoveComponent(SpyPlaneEntity owner) {
        super(owner);
    }

    @Override
    public SpyPlaneEntity getOwner() {
        return (SpyPlaneEntity) super.getOwner();
    }

    @Override
    protected TroopProp getTroopProp() {
        return null;
    }

    @Override
    protected MoveProp getMoveProp() {
        return getOwner().getProp().getMove();
    }

    @Override
    protected void initMoveSpeedFromTroopProp() {
        SpyPlaneTemplate template = ResHolder.getInstance().findValueFromMap(SpyPlaneTemplate.class, getOwner().getProp().getTemplateId());
        if (template == null) {
            throw new GeminiException(ErrorCode.TEMPLATE_NOT_EXIST);
        }
        moveSpeed = template.getSpeed();
    }

    @Override
    public int getRealMoveSpeed(int searchTag, Point endPoint) {
        if (GameLogicConstants.isBesiegeMove(searchTag)) {
            return ResHolder.getConsts(ConstTemplate.class).getSiegeMoveSpeed();
        }
        int realMoveSpeed = SceneAddCalc.getSpyPlaneSpeed(this.getOwner(), moveSpeed);
        if (debugMoveSpeedRatio > 0) {
            realMoveSpeed = realMoveSpeed * debugMoveSpeedRatio;
        }
        return realMoveSpeed;
    }

    @Override
    public void onTick() {
        super.onTick();
    }

    @Override
    protected int getMoveSearchPathTag(boolean isToTarget) {
        return GameLogicConstants.AIRPORT_MOVE;
    }

    @Override
    protected boolean moveToTarget(SceneObjEntity targetEntity, TroopInteractionType interactionType, IMoveArriveHandler arriveHandler, IMoveTargetLoseHandler loseHandler, boolean isAsync, Consumer<Integer> failHandler) {
        boolean isArrived = super.moveToTarget(targetEntity, interactionType, arriveHandler, loseHandler, isAsync, failHandler);
        if (isArrived) {
            return true;
        }
        // 是回自己的主城 不加小箭头
        if (targetEntity == getOwner().getPlayer().getMainCity()) {
            return false;
        }
        // 设置小箭头
        targetEntity.getArrowComponent().addArrowItem(getOwner());
        return false;
    }

    private void syncPlayerMove() {
        SsPlayerMisc.SpyMoveCmd.Builder builder = SsPlayerMisc.SpyMoveCmd.newBuilder()
                .setCurPoint(getMoveProp().getCopySsBuilder().getCurPoint())
                .setMoveSpeed(moveSpeed)
                .setPlaneId(getOwner().getProp().getPlayerPlaneId())
                .setStartTs(getMoveProp().getStartTs())
                .setEndTs(getMoveProp().getEndTs());
        for (PointProp pointProp : getMoveProp().getPointList()) {
            builder.addPointList(pointProp.getCopySsBuilder());
        }
        getOwner().getPlayer().tellPlayer(builder.build());
    }

    @Override
    public void onEndMove() {
        super.onEndMove();
        getOwner().getEventDispatcher().dispatch(new MoveEndEvent(getEntityId()));
    }

    @Override
    protected void onRefreshMove() {
        super.onRefreshMove();
        try {
            syncPlayerMove();
        } catch (Exception e) {
            LOGGER.error("SpyPlaneMoveComponent onRefreshMove {} failed ", getOwner(), e);
        }
    }
}

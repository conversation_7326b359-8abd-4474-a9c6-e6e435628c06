package com.yorha.cnc.zone.zone.kindomSkil.skillEffects;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.KingdomSkillTemplate;

/**
 * 统御
 * <p>
 * 只能对本王国的部队释放
 *
 * <AUTHOR>
 */
public class LeadTeam implements ISkillEffect {
    private static final Logger LOGGER = LogManager.getLogger(LeadTeam.class);

    @Override
    public ErrorCode checkCanUse(int skillId, long targetId, int targetZoneId, ZoneEntity zoneEntity) {
        return ErrorCode.OK;
    }

    @Override
    public void effect(int skillId, long armyId, ZoneEntity zoneEntity, int targetZoneId) {
        MapBuildingEntity kingCity = zoneEntity.getBigScene().getBuildingMgrComponent().getKingCity();
        String kingName = "";
        String kingClanName = "";
        if (null == kingCity) {
            LOGGER.error("LeadTeam effect kingCity is null");
        } else {
            kingName = kingCity.getProp().getKingdomModel().getKingCardHead().getName();
            kingClanName = kingCity.getClanSimpleName();
        }

        ArmyEntity targetArmy = zoneEntity.getBigScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, armyId);
        if (targetArmy == null) {
            throw new GeminiException(ErrorCode.ARMY_NOT_EXIT);
        }

        // 战斗buff
        KingdomSkillTemplate skillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getSkillTemplate(skillId);
        targetArmy.getBuffComponent().addBuff(skillTemplate.getBattlebuffList());

        // 跑马灯
        StructPB.DisplayDataPB.Builder marqueeBuilder = StructPB.DisplayDataPB.newBuilder();
        marqueeBuilder.getParamsBuilder()
                .addDatas(StructPB.DisplayParamPB.newBuilder().setType(CommonEnum.DisplayParamType.DPT_ZONE_ID).setText(String.valueOf(zoneEntity.getZoneId())))
                .addDatas(MsgHelper.buildDisPlayTextPb(kingClanName))
                .addDatas(MsgHelper.buildDisPlayTextPb(kingName));
        zoneEntity.getBigScene().getMarqueeComponent().sendFullServerMarquee(zoneEntity.getZoneId(), CommonEnum.MarqueeType.KINGDOM_SKILL_3_VALUE, marqueeBuilder.build(), null);
    }
}

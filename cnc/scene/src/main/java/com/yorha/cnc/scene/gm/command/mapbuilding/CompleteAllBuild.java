package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CompleteAllBuild implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        AbstractScenePlayerEntity scenePlayer = actor.getScenePlayer(playerId);
        if (scenePlayer.getClanId() == 0) {
            return;
        }
        SceneClanEntity sceneClan = scenePlayer.getSceneClan();
        sceneClan.getBuildComponent().gmCompleteBuild();
    }

    @Override
    public String showHelp() {
        return "CompleteAllBuild";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MAPBUILDING;
    }
}

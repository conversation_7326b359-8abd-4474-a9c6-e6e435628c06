package com.yorha.cnc.scene.sceneObj;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.IActorRef;
import com.yorha.gemini.props.PropertyChangeListener;
import org.jetbrains.annotations.NotNull;

/**
 * SceneObj的专属
 * 整个scene共享一次prop触发消息
 */
public class SceneObjPropChangeListener extends PropertyChangeListener {

    protected final SceneEntity sceneEntity;
    protected final long entityId;

    public SceneObjPropChangeListener(@NotNull SceneEntity sceneEntity, long entityId, @NotNull Runnable runnable, IActorRef actorRef) {
        super(runnable, actorRef);
        this.sceneEntity = sceneEntity;
        this.entityId = entityId;
    }

    @Override
    protected void realTell(String propName) {
        if (PropertyChangeListener.isStop) {
            return;
        }
        if (sceneEntity.isDestroy()) {
            return;
        }
        sceneEntity.getAoiMgrComponent().addPropChangeListener(entityId, this);
        if (sceneEntity.getIsInTaskQueue()) {
            return;
        }
        ActorSendMsgUtils.send(this.actorRef, new ActorRunnable<>(
                "SceneObj.prop.trigger",
                (actor) -> {
                    sceneEntity.getAoiMgrComponent().consumeAllPropChangeListener();
                }
        ));
        sceneEntity.setIsInTaskQueue();
    }
}

package com.yorha.cnc.scene.actorservice;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.ScenePlayerMgrComponent;
import com.yorha.common.actor.SceneInformationMgrService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.SsSceneInfoMgr.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SceneInformationMgrServiceImpl implements SceneInformationMgrService {
    private static final Logger LOGGER = LogManager.getLogger(SceneInformationMgrServiceImpl.class);
    private final SceneActor sceneActor;

    public SceneInformationMgrServiceImpl(SceneActor sceneActor) {
        this.sceneActor = sceneActor;
    }

    public SceneEntity getScene() {
        return sceneActor.getScene();
    }

    @Override
    public void handleGetOnlinePlayerIdAsk(GetOnlinePlayerIdAsk ask) {
        Collection<Long> onlinePlayerIds = getScene().getPlayerMgrComponent().getOnlinePlayerIds();
        GetOnlinePlayerIdAns.Builder ans = GetOnlinePlayerIdAns.newBuilder();
        ans.getPlayerBuilder().addAllDatas(onlinePlayerIds);
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleGetOnlinePlayerIdFilterByCreateTimeAsk(GetOnlinePlayerIdFilterByCreateTimeAsk ask) {
        Collection<Long> players = getScene().getBigScene().getPlayerMgrComponent().getOnlinePlayerIdsFilterByCreateTime(ask.getStartTime(), ask.getEndTime());
        GetOnlinePlayerIdFilterByCreateTimeAns.Builder ans = GetOnlinePlayerIdFilterByCreateTimeAns.newBuilder();
        ans.getPlayerBuilder().addAllDatas(players);
        sceneActor.answer(ans.build());
    }

    @Override
    public void handleCheckPlayerOnlineAsk(CheckPlayerOnlineAsk ask) {
        ScenePlayerMgrComponent playerMgrComponent = getScene().getPlayerMgrComponent();
        CheckPlayerOnlineAns.Builder builder = CheckPlayerOnlineAns.newBuilder();
        for (Long playerId : ask.getPlayerList()) {
            AbstractScenePlayerEntity scenePlayer = playerMgrComponent.getScenePlayerOrNull(playerId);
            if (scenePlayer != null && scenePlayer.isThisSceneOnline()) {
                builder.addOnlinePlayer(playerId);
            }
        }
        sceneActor.answer(builder.build());
    }
    
    @Override
    public void handleGetZoneIpPortAsk(GetZoneIpPortAsk ask) {
        final int zoneId = sceneActor.getZoneId();
        LOGGER.info("handleGetZoneIpPortAsk try get ipPort for zoneId={}, channelName={}, isSuperWhite={}", zoneId, ask.getChannelName(), ask.getIsSuperWhite());
        GetZoneIpPortAns.Builder ans = GetZoneIpPortAns.newBuilder();
        Map<String, Pair<String, Integer>> channelMap = getAccelerationChannel();
        Pair<String, Integer> ipPort = channelMap.getOrDefault(ask.getChannelName(), null);
        ans.setChannelName(ask.getChannelName());
        // ask携带的加速通道无可用ipPort时走origin兜底
        if (ipPort == null) {
            LOGGER.info("handleGetZoneIpPortAsk get ipPort for zoneId={}, channelName={} fail, use default channel=origin", zoneId, ask.getChannelName());
            ipPort = channelMap.getOrDefault(ask.getChannelName(), null);
            ans.setChannelName("origin");
            // 兜底origin通道依旧失败
            if (ipPort == null) {
                LOGGER.error("handleGetZoneIpPortAsk get ipPort for zoneId={} fail, origin not exist", zoneId);
                throw new GeminiException(ErrorCode.MULTI_SERVER_GET_TARGET_SERVER_INFO_FAIL);
            }
        }
        final boolean canRegister;
        // 超级白名单无脑返回可注册
        if (ask.getIsSuperWhite()) {
            canRegister = true;
        } else {
            canRegister = this.checkZoneCanRegister();
        }

        ans.setCanRegister(canRegister).setIp(ipPort.getFirst()).setPort(ipPort.getSecond());
        sceneActor.answer(ans.build());
    }

    /**
     * @return 本服所有加速通道
     */
    private Map<String, Pair<String, Integer>> getAccelerationChannel() {
        final int zoneId = sceneActor.getZoneId();
        final Map<String, Object> accChannel = ClusterConfigUtils.getZoneConfig(zoneId).getStringMapItem("acceleration_channel");
        if (accChannel == null) {
            return Collections.emptyMap();
        }
        final Map<String, Pair<String, Integer>> channelMap = new HashMap<>(accChannel.size());
        for (final Map.Entry<String, Object> zoneInfoEntry : accChannel.entrySet()) {
            @SuppressWarnings("unchecked") final Map<String, Object> ipPortInfo = (Map<String, Object>) zoneInfoEntry.getValue();
            channelMap.put(zoneInfoEntry.getKey(), Pair.of(ipPortInfo.get("ip").toString(), Integer.parseInt(ipPortInfo.get("port").toString())));
        }
        return channelMap;
    }

    /**
     * 检查服可否注册（是否对外、是否开服、是否达注册上限）
     *
     * @return true==可注册
     */
    private boolean checkZoneCanRegister() {
        final int zoneId = sceneActor.getZoneId();
        // 未对外
        if (ServerContext.getServerSetting().getOpenStatus() != ServerOpenStatus.OPEN) {
            LOGGER.info("checkZoneCanRegister zoneId={} status not open to player", zoneId);
            return false;
        }
        // 未开服
        if (sceneActor.getScene().getOpenTsMs() > SystemClock.now()) {
            LOGGER.info("checkZoneCanRegister zoneId={} openTsMs not open to player", zoneId);
            return false;
        }
        final int registerLimit = ClusterConfigUtils.getZoneConfig(zoneId).getIntItem("register_limit");
        final int registerNum = sceneActor.getBigScene().getZoneEntity().getBornMgrComponent().getPlayerNum();
        return registerNum < registerLimit;
    }
}

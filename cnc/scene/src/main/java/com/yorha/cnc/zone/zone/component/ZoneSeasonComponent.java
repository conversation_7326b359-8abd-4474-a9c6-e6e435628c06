package com.yorha.cnc.zone.zone.component;

import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.server.ZoneContext;
import com.yorha.game.gen.prop.ZoneSeasonModelProp;
import com.yorha.proto.CommonEnum.ZoneSeason;
import com.yorha.proto.CommonEnum.ZoneSeasonStage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 服务器赛季相关
 *
 * <AUTHOR>
 */
public class ZoneSeasonComponent extends AbstractComponent<ZoneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ZoneSeasonComponent.class);

    public ZoneSeasonComponent(ZoneEntity owner) {
        super(owner);
    }

    @Override
    public void postInit() {
        // 兼容线上初始化index
        if (getPlayerBornIndex() <= 0) {
            final int curPlayerName = getOwner().getBornMgrComponent().getPlayerNum();
            getProp().setPlayerBornIndex(curPlayerName);
            LOGGER.info("postInit {}", curPlayerName);
        }
        ZoneContext.getZoneSeasonInfo().setSeason(getCurSeason());
        ZoneContext.getZoneSeasonInfo().setSeasonStage(getCurSeasonStage(), getProp().getStageEnterTsMs());
    }

    public void onSceneOk() {
        // 没开服 直接初始化成建设期就行了
        if (!getOwner().isZoneOpen()) {
            if (getProp().getSeason() == ZoneSeason.ZS_NONE) {
                setNewSeason(ZoneSeason.ZS_BUILDING);
            }
            return;
        }
        // 开服了
        if (getProp().getSeason() == ZoneSeason.ZS_NONE || getProp().getSeason() == ZoneSeason.ZS_BUILDING) {
            setNewSeason(ZoneSeason.ZS_BUILDING);
        }
    }

    /**
     * 进入备战赛季  开服15天后
     */
    public void enterPrepareSeason() {
        if (getCurSeason() != ZoneSeason.ZS_NONE && getCurSeason() != ZoneSeason.ZS_BUILDING) {
            LOGGER.warn("enterPrepareSeason but curSeason not match {}", getCurSeason());
            return;
        }
        LOGGER.info("ZoneSeasonComponent enterPrepareSeason");
        // 进入备战赛季
        setNewSeason(ZoneSeason.ZS_PREPARE);
    }

    /**
     * 开服了
     */
    public void onZoneOpen() {
        setNewSeason(ZoneSeason.ZS_BUILDING);
    }

    private void setNewSeason(ZoneSeason season) {
        ZoneSeason oldSeason = getProp().getSeason();
        if (oldSeason == season) {
            return;
        }
        getProp().setSeason(season);
        ZoneContext.getZoneSeasonInfo().setSeason(season);
        LOGGER.info("ZoneSeasonComponent setNewSeason {}->{}", oldSeason, season);
        getOwner().getBigScene().getQlogComponent().sendSeasonQLog(season.getNumber());
    }

    public ZoneSeason getCurSeason() {
        return getProp().getSeason();
    }

    public int getPlayerBornIndex() {
        return getProp().getPlayerBornIndex();
    }

    public void addPlayerBornIndex() {
        getProp().setPlayerBornIndex(getProp().getPlayerBornIndex() + 1);
    }

    public ZoneSeasonStage getCurSeasonStage() {
        return getProp().getStage();
    }

    private ZoneSeasonModelProp getProp() {
        return getOwner().getProp().getSeasonModel();
    }
}

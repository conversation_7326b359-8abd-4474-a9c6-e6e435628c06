package com.yorha.cnc.scene.pathfinding;

import com.yorha.cnc.scene.pathfinding.manager.MainScenePathFindingManager;
import com.yorha.cnc.scene.pathfinding.manager.PathFindingManager;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.PathFindingService;
import com.yorha.common.actor.PathFindingServices;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.gemini.actor.msg.TypedMsg;

/**
 * <AUTHOR>
 */
public class PathFindingActor extends BaseGameActor implements PathFindingServices {
    private final PathFindingService pathFindingService;
    private PathFindingManager manager;

    public PathFindingActor(ActorSystem system, IActorRef self) {
        super(system, self);
        pathFindingService = new PathFindingServiceImpl(this);
    }

    public void init(int mapId) {
        // 多开 已经初始化过了
        if (manager != null) {
            return;
        }
        manager = new MainScenePathFindingManager(mapId);
    }

    public PathFindingManager getManager() {
        return manager;
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    public PathFindingService getPathFindingService() {
        return pathFindingService;
    }
}

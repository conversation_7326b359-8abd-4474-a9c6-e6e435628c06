package com.yorha.cnc.scene.gm.command.resbuilding;

import com.yorha.cnc.mainScene.common.component.MainSceneResMgrComponent;
import com.yorha.cnc.mainScene.common.component.MainSceneResRegionItem;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryResBuildingStatus implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        if (!actor.getScene().isMainScene()) {
            return;
        }
        MainSceneResMgrComponent resMgrComponent = (MainSceneResMgrComponent) actor.getScene().getResMgrComponent();
        StringBuilder string = new StringBuilder();
        for (int i = 0; i < 1; i++) {
            MainSceneResRegionItem resRegionItem = resMgrComponent.getResRegionItem(i);
            string.append(i);
            string.append("号州资源田数: ");
            string.append(resRegionItem.getBuildingNum());
            string.append("\n");

            string.append("州内各区域资源田数: \n");
            Map<CommonEnum.MapAreaType, List<Integer>> regionPartList = actor.getScene().getMapTemplateDataItem().getRegionBornPartList(i);
            for (Map.Entry<CommonEnum.MapAreaType, List<Integer>> entry : regionPartList.entrySet()) {
                if (BigSceneConstants.isBanEntityBornPart(entry.getKey())) {
                    continue;
                }
                for (Integer partId : entry.getValue()) {
                    if (resRegionItem.getPartBuildingNum(partId) != 0) {
                        continue;
                    }
                    string.append(partId);
                    string.append(": ");
                    string.append(resRegionItem.getPartBuildingNum(partId));
                    string.append("\n");
                }
            }

        }
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getIdipMail());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(string.toString()));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle("QueryResBuildingStatus")
                .setSubTitle("QueryResBuildingStatus");

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);

        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(actor.getScenePlayer(playerId).getZoneId())
                        .build(),
                builder.build());
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COLLECT;
    }
}

package com.yorha.cnc.scene.entity.component;

import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.node.IGateActor;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.DungeonType;
import com.yorha.proto.CommonEnum.Language;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.PlayerScene.Player_ChangeMap_NTF;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ScenePlayerMgrComponent<T extends AbstractScenePlayerEntity> extends SceneComponent {
    private static final Logger LOGGER = LogManager.getLogger(ScenePlayerMgrComponent.class);
    /**
     * 所有玩家
     */
    protected final Map<Long, T> scenePlayerMap = new ConcurrentHashMap<>();
    /**
     * 在线玩家
     */
    protected final Map<Long, T> onlinePlayer = new ConcurrentHashMap<>();
    protected final Map<Long, IActorRef> onlineSessionRefMap = new ConcurrentHashMap<>();
    /**
     * npc玩家
     */
    private Map<Long, NpcPlayerEntity> npcPlayerMap;
    /**
     * key=openId, val=registerLimitTsMs 同账号注册限制
     */
    private final Map<String, Long> openIdRegisterLimit = new LinkedHashMap<>();
    /**
     * 升天待检测队列
     */
    private Queue<Long> ascendPlayerQueue;

    public ScenePlayerMgrComponent(SceneEntity owner) {
        super(owner);
    }

    public boolean onPlayerLogin(long playerId, IActorRef sessionRef, boolean isNewbie, final String intelToken, final Language language, DungeonType dungeonType) {
        return onPlayerLogin(playerId, sessionRef);
    }

    public boolean onPlayerLogin(long playerId, IActorRef sessionRef) {
        T scenePlayer = getScenePlayer(playerId);
        Player_ChangeMap_NTF.Builder builder = Player_ChangeMap_NTF.newBuilder();
        builder.setMapId(getOwner().getMapId()).setSceneId(getOwner().getEntityId());
        SessionHelper.sendMsgToSession(sessionRef, ownerActor(), MsgType.PLAYER_CHANGEMAP_NTF, builder.build());
        onlinePlayer.put(playerId, scenePlayer);
        onlineSessionRefMap.put(playerId, sessionRef);
        scenePlayer.onLogin(sessionRef);
        PlayerScene.Player_ChangeMapDone_NTF.Builder changeMapDoneBuilder = PlayerScene.Player_ChangeMapDone_NTF.newBuilder();
        changeMapDoneBuilder.setMapId(getOwner().getMapId()).setSceneId(getOwner().getEntityId());
        scenePlayer.sendMsgToClient(MsgType.PLAYER_CHANGEMAPDONE_NTF, changeMapDoneBuilder.build());
        return true;
    }

    public boolean onPlayerLogin(long playerId, IActorRef sessionRef, CommonEnum.DungeonType type) {
        T scenePlayer = getScenePlayer(playerId);
        onlinePlayer.put(playerId, scenePlayer);
        onlineSessionRefMap.put(playerId, sessionRef);
        scenePlayer.onLoginWithDungeon(sessionRef, type);
        return true;
    }

    public void onPlayerLogout(long playerId) {
        AbstractScenePlayerEntity scenePlayer = getScenePlayer(playerId);
        onlinePlayer.remove(playerId);
        onlineSessionRefMap.remove(playerId);
        scenePlayer.onLogout();
    }

    /**
     * 进入副本
     */
    public void playerEnterDungeon(long playerId, DungeonType type) {
        AbstractScenePlayerEntity scenePlayer = getScenePlayer(playerId);
        // 进入副本  设置成在副本中的状态 把aoi清掉
        scenePlayer.enterDungeon(type);
    }

    /**
     * 离开副本 进入大世界
     */
    public void playerLeaveDungeon(long playerId) {
        AbstractScenePlayerEntity scenePlayer = getScenePlayer(playerId);
        if (scenePlayer == null) {
            throw new GeminiException(ErrorCode.SCENE_PLAYER_NOT_EXIST);
        }
        Player_ChangeMap_NTF.Builder builder = Player_ChangeMap_NTF.newBuilder();
        builder.setMapId(getOwner().getMapId());
        builder.setSceneId(getOwner().getEntityId());
        scenePlayer.sendMsgToClient(MsgType.PLAYER_CHANGEMAP_NTF, builder.build());

        // 离开副本 回到当前场景  把自己的ob下发
        scenePlayer.leaveDungeon();

        PlayerScene.Player_ChangeMapDone_NTF.Builder changeMapDoneBuilder = PlayerScene.Player_ChangeMapDone_NTF.newBuilder();
        changeMapDoneBuilder.setMapId(getOwner().getMapId());
        changeMapDoneBuilder.setSceneId(getOwner().getEntityId());
        scenePlayer.sendMsgToClient(MsgType.PLAYER_CHANGEMAPDONE_NTF, changeMapDoneBuilder.build());
    }

    public T getScenePlayerOrNull(long playerId) {
        return scenePlayerMap.get(playerId);
    }

    public T getScenePlayer(long playerId) {
        T player = scenePlayerMap.get(playerId);
        if (player == null) {
            LOGGER.error("get scene player fail id:{}", playerId);
            throw new GeminiException(ErrorCode.MAP_MAP_PLAYER_NULL.getCodeId());
        }
        return player;
    }

    public void removeScenePlayer(long playerId, int zoneId) {
        scenePlayerMap.remove(playerId);
        onlinePlayer.remove(playerId);
        onlineSessionRefMap.remove(playerId);
    }

    public void addScenePlayer(T abstractScenePlayerEntity) {
        scenePlayerMap.put(abstractScenePlayerEntity.getEntityId(), abstractScenePlayerEntity);
        LOGGER.debug("{} put {} into mgr, cur num:{}", getOwner(), abstractScenePlayerEntity, scenePlayerMap.size());
    }

    public Collection<T> getAllScenePlayer() {
        return Collections.unmodifiableCollection(scenePlayerMap.values());
    }

    public int getAllPlayerCount() {
        return scenePlayerMap.size();
    }

    public Set<Long> getOnlinePlayerIds() {
        return Collections.unmodifiableSet(onlinePlayer.keySet());
    }

    public Set<Long> getAllPlayerIdList() {
        return scenePlayerMap.keySet();
    }

    public Map<Long, AbstractScenePlayerEntity> getOnlinePlayer() {
        return Collections.unmodifiableMap(onlinePlayer);
    }

    public boolean isOnlinePlayer(long playerId) {
        return onlinePlayer.containsKey(playerId);
    }

    /**
     * 场景广播
     * 广播ss消息到所有场景在线的PlayerActor
     */
    public void broadcastOnlinePlayer(GeneratedMessageV3 ssMsg) {
        final List<IActorRef> playerRefList = getOnlinePlayerActorRef();
        if (playerRefList.isEmpty()) {
            return;
        }
        BroadcastHelper.toAllTargets(playerRefList, this.ownerActor().self(), ssMsg);
    }

    /**
     * 场景广播
     * 广播cs消息到gate，再到所有在线的sessionActor，再下发至客户端
     */
    public void broadcastOnlineClientMsg(int msgType, GeneratedMessageV3 csMsg) {
        Collection<IActorRef> sessionRefList = getOnlineSession();
        if (sessionRefList.isEmpty()) {
            return;
        }
        if (ServerContext.isZoneServer()) {
            IActorRef gateActorRef = RefFactory.ofLocalGate();
            ActorSendMsgUtils.send(gateActorRef, ownerActor().self(), new ActorRunnable<IGateActor>("ScenePlayerMgrComponent#broadcastOnlineClientMsg", iGateActor -> {
                SessionHelper.broadcastMsgToSessions(sessionRefList, gateActorRef, msgType, csMsg);
            }));
        } else {
            SessionHelper.broadcastMsgToSessions(sessionRefList, ownerActor().self(), msgType, csMsg);
        }

    }

    /**
     * 场景广播
     */
    public void broadcastOnlineClientMsgBytes(int msgType, ByteString msgBytes) {
        Collection<IActorRef> sessionRefList = getOnlineSession();
        if (sessionRefList.isEmpty()) {
            return;
        }
        if (ServerContext.isZoneServer()) {
            IActorRef gateActorRef = RefFactory.ofLocalGate();
            ActorSendMsgUtils.send(gateActorRef, ownerActor().self(), new ActorRunnable<IGateActor>("ScenePlayerMgrComponent#broadcastOnlineClientMsgBytes", iGateActor -> {
                SessionHelper.broadcastMsgToSessions(sessionRefList, gateActorRef, msgType, msgBytes);
            }));
        } else {
            SessionHelper.broadcastMsgToSessions(sessionRefList, ownerActor().self(), msgType, msgBytes);
        }
    }

    /**
     * 场景广播
     * 广播cs消息到场景所有在线的sessionActor，再下发至客户端
     * 这个会根据语言下发不同的消息
     */
    public void broadcastOnlineClientWithLanguage(int msgType, Map<Language, ByteString> msgMap) {
        Collection<IActorRef> sessionRefList = getOnlineSession();
        SessionHelper.broadcastMsgToSessionsWithMultiLanguage(sessionRefList, this.ownerActor().self(), msgType, msgMap);
    }

    /**
     * 场景广播
     * 上面那个接口的封装额外版
     */
    public void broadcastOnlineClientWithLanguageCmd(SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd ask) {
        int msgType = ask.getMsgType();
        int size = ask.getMsgMultiLanguageBytesMap().size();
        Map<Language, ByteString> msgMap = new HashMap<>(size);
        for (Map.Entry<Integer, ByteString> entry : ask.getMsgMultiLanguageBytesMap().entrySet()) {
            Language language = Language.forNumber(entry.getKey());
            msgMap.put(language, entry.getValue());
        }
        broadcastOnlineClientWithLanguage(msgType, msgMap);
    }

    protected List<IActorRef> getOnlinePlayerActorRef() {
        List<IActorRef> playerRefList = new LinkedList<>();
        for (AbstractScenePlayerEntity abstractScenePlayerEntity : onlinePlayer.values()) {
            playerRefList.add(abstractScenePlayerEntity.genPlayerRef());
        }
        return playerRefList;
    }

    private Collection<IActorRef> getOnlineSession() {
        return Collections.unmodifiableCollection(onlineSessionRefMap.values());
    }

    public void addNpcPlayer(NpcPlayerEntity player) {
        if (npcPlayerMap == null) {
            npcPlayerMap = new HashMap<>();
        }
        npcPlayerMap.put(player.getEntityId(), player);
    }

    public NpcPlayerEntity getNpcPlayer(long id) {
        if (npcPlayerMap == null) {
            return null;
        }
        return npcPlayerMap.get(id);
    }

    /**
     * 获取所有没有升天的CityEntity
     */
    public Collection<CityEntity> getAllUnAscendCityEntity() {
        List<CityEntity> list = Lists.newArrayList();
        for (T value : scenePlayerMap.values()) {
            if (value.getMainCity() == null) {
                continue;
            }
            if (value.getMainCity().getTransformComponent().isAscend()) {
                continue;
            }
            list.add(value.getMainCity());
        }
        return list;
    }

    public void callAllPlayerAfterAllLoad() {
        for (AbstractScenePlayerEntity abstractScenePlayerEntity : scenePlayerMap.values()) {
            try {
                abstractScenePlayerEntity.callAfterAllLoad();
            } catch (Exception e) {
                LOGGER.error("ScenePlayerMgrComponent callAllPlayerAfterAllLoad initAllComponents ", e);
            }
        }
    }

    /**
     * 检查openId能否注册（限制5s内无法再次注册）
     *
     * @param openId
     * @return ErrorCode
     */
    public ErrorCode checkOpenIdCanRegister(final String openId) {
        final Long limitTsMs = openIdRegisterLimit.get(openId);
        final long nowTsMs = SystemClock.now();
        if (limitTsMs != null) {
            if (limitTsMs > nowTsMs) {
                LOGGER.error("checkOpenIdCanRegister openId={} reach register limit", openId);
                return ErrorCode.MULTI_SERVER_REGISTER_REACH_ZONE_LIMIT;
            }
        }
        final long newLimitTsMs = nowTsMs + TimeUtils.second2Ms(5);
        // 5s内限制同openId注册
        openIdRegisterLimit.put(openId, newLimitTsMs);
        LOGGER.info("checkOpenIdCanRegister openId={} limit register till {}", openId, newLimitTsMs);
        return ErrorCode.OK;
    }

    /**
     * 移出openId注册限制
     */
    public void removeOpenIdRegisterLimit(final String openId) {
        Long limitTsMs = openIdRegisterLimit.remove(openId);
        if (limitTsMs != null) {
            LOGGER.info("removeOpenIdRegisterLimit openId={} limitTsMs={}", openId, limitTsMs);
        }
    }

    protected void initAscendTimer() {
        ascendPlayerQueue = new LinkedList<>();
        getOwner().getTimerComponent().addRepeatTimer(getEntityId(), TimerReasonType.CITY_ASCEND_CHECK,
                () -> {
                    long curTime = SystemClock.now();
                    for (AbstractScenePlayerEntity entity : scenePlayerMap.values()) {
                        try {
                            ScenePlayerEntity scenePlayer = (ScenePlayerEntity) entity;
                            if (scenePlayer.getCityComponent().checkAscend(curTime)) {
                                ascendPlayerQueue.add(entity.getPlayerId());
                            }
                        } catch (Exception e) {
                            WechatLog.error("{} CityAscendCheck error", entity, e);
                        }
                    }
                }, 60, 60 * 60, TimeUnit.SECONDS);

    }

    public int playerCityAscend(int num) {
        int times = Math.min(ascendPlayerQueue.size(), num);
        if (times <= 0) {
            return 0;
        }
        LOGGER.info("ascendPlayerQueue {}", ascendPlayerQueue.size());
        long curTime = SystemClock.now();
        int cnt = 0;
        for (int i = 0; i < times; i++) {
            Long playerId = ascendPlayerQueue.poll();
            if (playerId == null) {
                return cnt;
            }
            AbstractScenePlayerEntity player = getScenePlayerOrNull(playerId);
            if (player == null) {
                continue;
            }
            ScenePlayerEntity scenePlayer = (ScenePlayerEntity) player;
            if (scenePlayer.getCityComponent().checkAscend(curTime)) {
                scenePlayer.getMainCity().getTransformComponent().cityAscend(CommonEnum.CityAscendReason.CAR_NOT_ONLINE_LONG_TIME);
                cnt++;
            }
        }
        return cnt;
    }
}

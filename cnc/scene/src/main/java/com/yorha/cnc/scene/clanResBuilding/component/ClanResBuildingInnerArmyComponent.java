package com.yorha.cnc.scene.clanResBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjInnerArmyComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.game.gen.prop.CityInnerArmyProp;
import com.yorha.game.gen.prop.InnerArmyInfoProp;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Core;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.SsPlayerClan;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 军团资源中心内部部队管理
 *
 * <AUTHOR>
 */
public class ClanResBuildingInnerArmyComponent extends SceneObjInnerArmyComponent {

    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildingInnerArmyComponent.class);

    public ClanResBuildingInnerArmyComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        super.init();
    }

    @Override
    public int getBeAidedMaxNum() {
        if (getOwner().getState() == CommonEnum.ClanResBuildingStage.CRBS_BUILD) {
            // 建设读取配表上限
            return getOwner().getClanResourceBuildingTemplate().getMaxSoldierNum();
        } else if (getOwner().getState() == CommonEnum.ClanResBuildingStage.CRBS_CAN_COLLECT) {
            // 采集应该是无穷大
            return Integer.MAX_VALUE;
        } else {
            LOGGER.error("ClanResBuildingInnerArmyComponent getBeAidedMaxNum error, state: {}", getOwner().getState());
            return 0;
        }
    }

    @Override
    public void checkCanRepatriateArmy(long playerId, ArmyEntity army, boolean isPermission) {
        // do nothing
    }

    @Override
    public void armyArrived(ArmyEntity armyEntity) {
        super.armyArrived(armyEntity);
        if (getOwner().getState() == CommonEnum.ClanResBuildingStage.CRBS_BUILD) {
            // 打qlog
            getOwner().sendExpansionLog("participate_build", armyEntity.getPlayerId());
        }
    }

    @Override
    public Core.Code checkArmyCanCollect(long playerId) {
        if (getOwner().getState() != CommonEnum.ClanResBuildingStage.CRBS_CAN_COLLECT) {
            return ErrorCode.CLAN_RES_BUILDING_CANNOT_COLLECT.getCode();
        }
        if (getOwner().getCollectComponent().isPlayerAlreadyInBuilding(playerId)) {
            return ErrorCode.CLAN_RES_BUILDING_ALREADY_HAVE_ONE.getCode();
        }
        return super.checkArmyCanCollect(playerId);
    }

    /**
     * 获取玩家id到玩家联盟建筑建设分数的映射，建筑分数为玩家所有参与建筑的军队累加
     *
     * @param endTsMs        结算的结束时间
     * @param beginBuildTsMs 联盟建筑建设的开始时间
     * @return 玩家id到玩家联盟建筑建设分数的映射
     */
    private Map<Long, Integer> getPlayerClanRebuildScoreMap(long endTsMs, long beginBuildTsMs) {
        Map<Long, Integer> playerIdToRebuildScore = new HashMap<>();
        int rebuildScoreRatio = getOwner().getClanResourceBuildingTemplate().getCoefficient();
        for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
            long playerId = entry.getValue().getPlayerId();
            int addScore = (int) (TimeUtils.ms2Second(endTsMs - Math.max(beginBuildTsMs, entry.getValue().getEnterTsMs())) * rebuildScoreRatio);
            if (playerIdToRebuildScore.containsKey(playerId)) {
                playerIdToRebuildScore.put(playerId, addScore + playerIdToRebuildScore.get(playerId));
            } else {
                playerIdToRebuildScore.put(playerId, addScore);
            }
        }
        return playerIdToRebuildScore;
    }

    public long addSingleArmyBuildScore(long playerId, long armyId) {
        double rebuildScoreRatio = SceneAddCalc.getScoreRatio(getOwner().getClanResourceBuildingTemplate().getCoefficient(), getOwner().getOwnerSceneClan());
        SsPlayerClan.OnAddClanScoreCmd.Builder tell = SsPlayerClan.OnAddClanScoreCmd.newBuilder().setScoreType(CommonEnum.ClanScoreCategory.CSC_OCCUPY_AND_BUILD).setIsBuildAddScore(true);
        long now = SystemClock.now();
        long beginBuildTsMs = getOwner().getProgress().getStateStartTsMs();
        long addScore = (long) Math.floor(TimeUtils.ms2Second(now - getProp().getArmyV(armyId).getEnterTsMs()) * rebuildScoreRatio);
        tell.setAddValue(addScore);
        if (isArmyNeedAddTime(armyId, beginBuildTsMs)) {
            tell.setScoreUseInterval((int) getReturnArmyAddTime(armyId, now, beginBuildTsMs));
        }
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        scenePlayer.tellPlayer(tell.build());
        return addScore;
    }

    /**
     * 为建筑内所有的军队添加建设分数、记录建设时间
     *
     * @param endTsMs        结算的结束时间
     * @param beginBuildTsMs 联盟建筑建设的开始时间
     */
    public long addAllPlayerClanRebuildScore(long endTsMs, long beginBuildTsMs) {
        Map<Long, Integer> playerToClanRebuildScore = getPlayerClanRebuildScoreMap(endTsMs, beginBuildTsMs);
        Map<Long, Integer> playerToClanRebuildTime = getPlayerTimeMap(endTsMs, beginBuildTsMs);
        Set<Long> alreadyAddPlayer = new HashSet<>();
        for (Map.Entry<Long, InnerArmyInfoProp> entry : getProp().getArmy().entrySet()) {
            long playerId = entry.getValue().getPlayerId();
            if (alreadyAddPlayer.contains(playerId)) {
                // 当前player已经被添加过分数，跳过
                continue;
            }
            SsPlayerClan.OnAddClanScoreCmd.Builder cmdBuilder = SsPlayerClan.OnAddClanScoreCmd.newBuilder();
            cmdBuilder.setScoreType(CommonEnum.ClanScoreCategory.CSC_OCCUPY_AND_BUILD)
                    .setIsBuildAddScore(true)
                    .setAddValue(playerToClanRebuildScore.getOrDefault(playerId, 0))
                    .setScoreUseInterval(playerToClanRebuildTime.getOrDefault(playerId, 0));
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(entry.getValue().getPlayerId());
            scenePlayer.tellPlayer(cmdBuilder.build());
            alreadyAddPlayer.add(playerId);
        }
        return playerToClanRebuildScore.values().stream().mapToLong(x -> x).sum();
    }

    public void addClanOccupyOrRebuildScore(long addScore) {
        if (addScore <= 0L) {
            // 白嫖军团建筑时addScore可能为0
            return;
        }
        SsClanAttr.OnAddClanScoreForClanCmd.Builder cmdBuilder = SsClanAttr.OnAddClanScoreForClanCmd.newBuilder();
        cmdBuilder.setReason("participate_build");
        cmdBuilder.setScore(addScore);
        cmdBuilder.setEntityId(getOwner().getEntityId());
        if (getOwner().getClanId() == 0) {
            LOGGER.error("addScore {} may be lost because of trying to get clan Id 0", addScore);
            return;
        }
        getOwner().getOwnerSceneClan().tellClan(cmdBuilder.build());
    }

    @Override
    protected void beforeRemoveArmy(ArmyEntity army, boolean isIn, long operatorId) {
        long armyId = army.getEntityId();
        if (getProp().getArmy().containsKey(armyId)) {
            // 建筑内有这个数据代表军队已经到了，需要计算建设值
            if (getOwner().getState() == CommonEnum.ClanResBuildingStage.CRBS_BUILD) {
                long addScore = addSingleArmyBuildScore(army.getPlayerId(), armyId);
                addClanOccupyOrRebuildScore(addScore);
            }
            getOwner().sendExpansionLog("leave_build", army.getPlayerId());
        }
        // 如果建筑已经进入可采集状态了，被调用到removeArmy接口，需要把建筑上已经写入的军队信息删除掉
        if (getOwner().getState() == CommonEnum.ClanResBuildingStage.CRBS_CAN_COLLECT) {
            getOwner().getCollectComponent().removeArmyRecordFromClanResBuilding(army.getPlayerId());
        }
    }

    /**
     * 清理所有未到达的军队
     * 只能资源田用 如果要应用于其他entity 注意预警!!
     */
    public void returnAllUnArrivedArmy() {
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        // 清理未到达的
        for (long armyId : unArrivedArmy.keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null) {
                army.getAssistComponent().leaveAssist(null, false, true);
            }
        }
        unArrivedSoldierNum = 0;
        unArrivedArmy.clear();
    }

    @Override
    public CityInnerArmyProp getProp() {
        return getOwner().getProp().getInnerArmy();
    }

    @Override
    public ClanResBuildingEntity getOwner() {
        return (ClanResBuildingEntity) super.getOwner();
    }
}

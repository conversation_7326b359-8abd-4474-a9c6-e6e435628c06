package com.yorha.cnc.scene.npcplayer.component;

import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerSoldierMgrComponent;
import com.yorha.cnc.scene.npcplayer.NpcPlayerEntity;
import com.yorha.game.gen.prop.ScenePlayerSoldierProp;
import com.yorha.game.gen.prop.SoldierProp;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class NpcPlayerSoldierComponent extends AbstractScenePlayerSoldierMgrComponent {

    private final ScenePlayerSoldierProp prop;

    public NpcPlayerSoldierComponent(NpcPlayerEntity owner) {
        super(owner);
        prop = new ScenePlayerSoldierProp();
    }

    @Override
    protected ScenePlayerSoldierProp getSoldierProp() {
        return prop;
    }

    @Override
    public Map<Integer, SoldierProp> buildGarrisonSoldier() {
        return new HashMap<>();
    }

    @Override
    public void onCityLeaveBattle(Collection<SoldierProp> soldierProps) {
        // 除了重伤死亡的加回来
        for (SoldierProp prop : soldierProps) {
            int num = prop.getNum() - prop.getDeadNum() - prop.getSevereWoundNum();
            addInCitySoldier(prop.getSoldierId(), num, null);
        }
    }
}

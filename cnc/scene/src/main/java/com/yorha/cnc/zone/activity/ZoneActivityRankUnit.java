package com.yorha.cnc.zone.activity;

import com.yorha.common.resource.resservice.rank.RankDataTemplateService;
import com.yorha.proto.CommonEnum;
import res.template.ActivityTemplate;

public class ZoneActivityRankUnit extends BaseZoneActivityRankUnit {
    public ZoneActivityRankUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    @Override
    protected int getRankId(ActivityTemplate activityTemplate) {
        return RankDataTemplateService.getRankIdByRankType(activityTemplate.getActivityRankType());
    }

    @Override
    protected int getRewardMailId(ActivityTemplate activityTemplate) {
        return activityTemplate.getActMailId();
    }

    @Override
    protected int getRankLimit(ActivityTemplate activityTemplate) {
        return activityTemplate.getRankLimit();
    }
}

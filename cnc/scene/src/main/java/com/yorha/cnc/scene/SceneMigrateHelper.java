package com.yorha.cnc.scene;

import com.google.protobuf.Message;
import com.yorha.cnc.scene.city.CityBuilder;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerBuilder;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.CityProp;
import com.yorha.game.gen.prop.ScenePlayerProp;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.Player;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class SceneMigrateHelper {
    private static final Logger LOGGER = LogManager.getLogger(SceneMigrateHelper.class);

    public static void insertTargetZone(ScenePlayerEntity scenePlayer, int zoneId, int regionId, Consumer<GeminiException> onComplete) {
        LOGGER.info("insertTargetZone start {} zoneId={} regionId={}", scenePlayer, zoneId, regionId);
        // 前置处理
        scenePlayer.beforeMigrate();
        CityEntity mainCity = scenePlayer.getMainCity();
        // scenePlayer存盘
        final TcaplusDb.ScenePlayerTable.Builder req1 = TcaplusDb.ScenePlayerTable.newBuilder();
        req1.setZoneId(zoneId).setScenePlayerId(scenePlayer.getEntityId());
        req1.setFullAttr(scenePlayer.getProp().getCopyDbBuilder()).setChangedAttr(Player.ScenePlayer.getDefaultInstance());
        // city存盘
        TcaplusDb.SceneObjTable.Builder req2 = TcaplusDb.SceneObjTable.newBuilder();
        req2.setZoneId(zoneId).setEntityId(mainCity.getEntityId()).setSceneId(zoneId);
        req2.getFullAttrBuilder()
                .setEntityId(mainCity.getEntityId())
                .setEntityType(EntityAttrOuterClass.EntityType.ET_City)
                .setCityAttr(mainCity.getProp().getCopyDbBuilder());
        req2.setChangedAttr(EntityAttrDb.EntityAttrDB.getDefaultInstance());
        // 目标是普通服 要么移民 要么迁移回本服 设置下zoneId
        req1.getFullAttrBuilder().getZoneModelBuilder().setZoneId(zoneId);
        req2.getFullAttrBuilder().getCityAttrBuilder().setZoneId(zoneId);
        final UpsertAsk<Message.Builder> ask1 = new UpsertAsk<>(req1, UpsertOption.getDefaultInstance());
        final UpsertAsk<Message.Builder> ask2 = new UpsertAsk<>(req2, UpsertOption.getDefaultInstance());
        SceneActor sceneActor = scenePlayer.ownerActor();
        sceneActor.askGameDb(ask1)
                .onComplete(
                        (res, t) -> {
                            if (t != null || res == null) {
                                // 回复
                                LOGGER.error("insertTargetZone player failed {} {} ", scenePlayer, res, t);
                                onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                return;
                            }
                            if (!res.isRecordAlreadyExist() && !res.isOk()) {
                                LOGGER.error("insertTargetZone player failed {} {}", scenePlayer, res);
                                onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                return;
                            }
                            LOGGER.info("insertTargetZone player end {} zoneId={} {}", scenePlayer, zoneId, res.getCode());
                            sceneActor.askGameDb(ask2)
                                    .onComplete(
                                            (res2, t2) -> {
                                                if (t2 != null || res2 == null || !res2.isOk()) {
                                                    // 回复
                                                    LOGGER.error("insertTargetZone city failed {} {} ", scenePlayer, res2, t2);
                                                    onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                                    return;
                                                }
                                                if (!res.isRecordAlreadyExist() && !res.isOk()) {
                                                    LOGGER.error("insertTargetZone city failed {} {}", scenePlayer, res2);
                                                    onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                                    return;
                                                }
                                                LOGGER.info("insertTargetZone city end {} zoneId={} {}", scenePlayer, zoneId, res2.getCode());
                                                // 回复
                                                onComplete.accept(null);
                                            });
                        }
                );
    }

    public static void deleteTargetZone(long playerId, long cityId, int zoneId, SceneActor actor, Consumer<GeminiException> onComplete) {
        TcaplusDb.SceneObjTable.Builder request = TcaplusDb.SceneObjTable.newBuilder();
        request.setZoneId(zoneId).setEntityId(cityId).setSceneId(zoneId);
        actor.askGameDb(new DeleteAsk<>(request)).onComplete(
                (res, t) -> {
                    if (t != null || res == null) {
                        // 回复
                        LOGGER.error("deleteTargetZone city failed {} {} ", playerId, res, t);
                        onComplete.accept(new GeminiException(ErrorCode.FAILED));
                        return;
                    }
                    if (!res.isRecordNotExist() && !res.isOk()) {
                        LOGGER.error("deleteTargetZone city failed {} {}", playerId, res);
                        onComplete.accept(new GeminiException(ErrorCode.FAILED));
                        return;
                    }
                    LOGGER.info("deleteTargetZone city end playerId={} zoneId={} {}", playerId, zoneId, res.getCode());
                    final TcaplusDb.ScenePlayerTable.Builder req = TcaplusDb.ScenePlayerTable.newBuilder();
                    req.setZoneId(zoneId).setScenePlayerId(playerId);
                    actor.askGameDb(new DeleteAsk<>(req)).onComplete(
                            (res2, t2) -> {
                                if (t2 != null || res2 == null) {
                                    // 回复
                                    LOGGER.error("deleteTargetZone player failed {} {} ", playerId, res2, t2);
                                    onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                    return;
                                }
                                if (!res2.isRecordNotExist() && !res2.isOk()) {
                                    LOGGER.error("deleteTargetZone player failed {} {}", playerId, res2);
                                    onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                    return;
                                }
                                LOGGER.info("deleteTargetZone player end playerId={} zoneId={} {}", playerId, zoneId, res2.getCode());
                                // 回复
                                onComplete.accept(null);
                            }
                    );
                }
        );
    }

    private static void saveNewPlayerCity(int zoneId, SceneActor actor, long playerId, long cityId, ScenePlayerProp scenePlayerProp, CityProp cityProp, Consumer<GeminiException> onComplete) {
        cityProp.unMarkAll();
        scenePlayerProp.unMarkAll();
        // save city
        TcaplusDb.SceneObjTable.Builder req1 = TcaplusDb.SceneObjTable.newBuilder();
        req1.setZoneId(zoneId).setEntityId(cityId).setSceneId(zoneId);
        req1.getFullAttrBuilder()
                .setEntityId(cityId)
                .setEntityType(EntityAttrOuterClass.EntityType.ET_City)
                .setCityAttr(cityProp.getCopyDbBuilder());
        req1.setChangedAttr(EntityAttrDb.EntityAttrDB.getDefaultInstance());
        final UpdateAsk<TcaplusDb.SceneObjTable.Builder> ask1 = new UpdateAsk<>(req1);
        actor.askGameDb(ask1).onComplete(
                (res, t) -> {
                    if (t != null || res == null || !res.isOk()) {
                        // 回复
                        LOGGER.error("saveNewPlayerCity city failed {} {} {} ", playerId, cityId, res, t);
                        onComplete.accept(new GeminiException(ErrorCode.FAILED));
                        return;
                    }
                    final TcaplusDb.ScenePlayerTable.Builder req2 = TcaplusDb.ScenePlayerTable.newBuilder();
                    req2.setZoneId(zoneId).setScenePlayerId(playerId)
                            .setFullAttr(scenePlayerProp.getCopyDbBuilder())
                            .setChangedAttr(Player.ScenePlayer.getDefaultInstance());
                    final UpdateAsk<TcaplusDb.ScenePlayerTable.Builder> ask2 = new UpdateAsk<>(req2);
                    actor.askGameDb(ask2).onComplete(
                            (res2, t2) -> {
                                if (t2 != null || res2 == null || !res2.isOk()) {
                                    // 回复
                                    LOGGER.error("saveNewPlayerCity player failed {} {} {} ", playerId, cityId, res2, t2);
                                    onComplete.accept(new GeminiException(ErrorCode.FAILED));
                                    return;
                                }
                                LOGGER.info("saveNewPlayerCity all end {} {}", playerId, cityId);
                                onComplete.accept(null);
                            }
                    );
                }
        );
    }

    private static void beforeZoneMigrateIn(long playerId, ScenePlayerProp scenePlayerProp, CityProp cityProp) {
        LOGGER.info("SceneMigrateHelper onMigrateIn start zoneId {}", playerId);
        // 清理王国头衔
        cityProp.getCityKingdomModel().setOfficeId(0);
        // 清理buff
        scenePlayerProp.getAdditionSys().clearAddition();
        scenePlayerProp.getDevBuffSysNew().clearDevBuff();
        cityProp.getDevBuffSys().clearDevBuffSys();
        cityProp.getBuffSys().clearBuff();
    }

    private static void onQueryPlayerCityEnd(int zoneId, long playerId, ScenePlayerProp playerProp, CityProp cityProp, SceneActor actor, String migrateType, Consumer<Boolean> onComplete) {
        SceneMigrateHelper.saveNewPlayerCity(zoneId, actor,
                playerId, playerProp.getMainCityId(),
                playerProp, cityProp,
                (exception) -> {
                    if (exception != null) {
                        onComplete.accept(false);
                        return;
                    }
                    try {
                        // load player
                        ScenePlayerEntity scenePlayerEntity = new ScenePlayerEntity(actor.getScene(), playerId, playerProp, new ScenePlayerBuilder());
                        actor.getScene().getPlayerMgrComponent().addScenePlayer(scenePlayerEntity);
                        scenePlayerEntity.getDbComponent().beginSaveChangeToDb(null);
                        LOGGER.info("{} scene restore player entity success {}", migrateType, scenePlayerEntity);
                        // load city
                        CityBuilder builder = new CityBuilder(actor.getScene(), playerProp.getMainCityId(), cityProp);
                        CityEntity cityEntity = new CityEntity(builder, true);
                        cityEntity.addIntoScene();
                        if (scenePlayerEntity.getSceneClan() != null) {
                            scenePlayerEntity.getSceneClan().getMemberComponent().onMemberAddMainScene(playerId);
                        }
                        LOGGER.info("{} scene restore player city success {}", migrateType, cityEntity);
                        onComplete.accept(true);
                    } catch (Exception e) {
                        WechatLog.error("{} scene restore player city failed {} ", migrateType, playerId, e);
                        onComplete.accept(false);
                    }
                });
    }
}

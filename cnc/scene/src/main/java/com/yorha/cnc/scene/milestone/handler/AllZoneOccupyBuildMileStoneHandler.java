package com.yorha.cnc.scene.milestone.handler;

import com.yorha.cnc.scene.milestone.AbstractMileStoneHandler;
import com.yorha.cnc.scene.milestone.bean.MileStoneTaskData;
import com.yorha.cnc.scene.milestone.bean.ZoneOccupyBuildData;
import com.yorha.common.constant.Constants;
import com.yorha.game.gen.prop.Int32MileStoneZoneInfoMapProp;
import com.yorha.game.gen.prop.MileStoneZoneInfoProp;
import com.yorha.proto.CommonEnum;

/**
 * 所在服占领某类建筑数量
 * 参数：建筑类型_建筑等级_建筑数量
 */
public class AllZoneOccupyBuildMileStoneHandler extends AbstractMileStoneHandler {

    @Override
    public void updateProcess(MileStoneTaskData data) {
        if (data instanceof ZoneOccupyBuildData) {
            String[] taskParam = getTaskParamById();
            int limit = Integer.parseInt(taskParam[0]);
            ZoneOccupyBuildData occupyBuildData = (ZoneOccupyBuildData) data;
            if (!matchParam(occupyBuildData)) {
                return;
            }
            long min = Math.min(getProp().getProcess() + 1, limit);
            getProp().setProcess(min);
        }
    }

    @Override
    public void recordRankData(MileStoneTaskData taskData) {
        if (taskData instanceof ZoneOccupyBuildData) {
            ZoneOccupyBuildData data = (ZoneOccupyBuildData) taskData;
            LOGGER.info("AllZoneOccupyBuildMileStoneHandler recordRankData, type={} level={} oldZoneId={} newZoneId={}", data.getBuildType(), data.getBuildLevel(), data.getOldOccupyZoneId(), data.getOccupyZoneId());
            if (!matchParam(data)) {
                return;
            }
            Int32MileStoneZoneInfoMapProp zoneRankInfoMap = getProp().getRankInfo().getZoneRankInfoMap();
            MileStoneZoneInfoProp mileStoneZoneInfoProp = zoneRankInfoMap.computeIfAbsent(data.getOccupyZoneId(), (key) -> {
                MileStoneZoneInfoProp prop = new MileStoneZoneInfoProp();
                prop.setZoneId(key);
                return prop;
            });
            LOGGER.info("AllZoneOccupyBuildMileStoneHandler recordRankData, add success, zone={} oldZoneId={} score={}", data.getOccupyZoneId(), data.getOldOccupyZoneId(), mileStoneZoneInfoProp.getScore());
        }
    }

    private boolean matchParam(ZoneOccupyBuildData data) {
        String[] taskParam = getTaskParamById();
        String[] split = taskParam[1].split(Constants.BAN_JIAO_DOU_HAO);
        int level = Integer.parseInt(taskParam[2]);
        for (String buildTypeConfig : split) {
            int buildType = Integer.parseInt(buildTypeConfig);
            if (buildType == data.getBuildType() && (level == 0 || level == data.getBuildLevel())) {
                return true;
            }
        }
        return false;
    }


    @Override
    public CommonEnum.MileStoneRewardRange getRewardRange() {
        return CommonEnum.MileStoneRewardRange.MSRR_CONDITION_ZONE;
    }

    @Override
    public CommonEnum.MileStoneTaskType getMileStoneTaskType() {
        return CommonEnum.MileStoneTaskType.MST_ZONE_OCCUPY_BUILD;
    }

    @Override
    public CommonEnum.MileStoneEndType getMileStoneEndType() {
        return CommonEnum.MileStoneEndType.MSET_TIME_END;
    }
}

package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.common.HonorTarget;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.event.activity.PreActThirdMonsterDead;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 野蛮人城寨特性
 *
 * <AUTHOR>
 */
public class RallyMonster implements MonsterFeature {
    private static final Logger LOGGER = LogManager.getLogger(RallyMonster.class);

    @Override
    public void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity) {

    }

    @Override
    public ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit) {
        return ErrorCode.OK;
    }

    @Override
    public void onDead(MonsterEntity monsterEntity) {
        LOGGER.info("RallyMonster onDead this={}", this);
        if (!monsterEntity.getScene().isBigScene()) {
            return;
        }
        // 发活动事件
        monsterEntity.getScene().getBigScene().getZoneEntity().getSideActivityComponent().dispatch(new PreActThirdMonsterDead());
    }

    /**
     * 发送野蛮人城寨奖励
     * 野蛮人城寨奖励分配  根据伤害获得不同奖池随机奖励，经验固定
     */
    @Override
    public void sendKillAndRewardAll(MonsterEntity monsterEntity) {
        MonsterTemplate template = monsterEntity.getTemplate();
        Point curPoint = monsterEntity.getCurPoint();
        int level = template.getLevel();
        // 杀怪奖励
        Map<Long, Map<Long, Integer>> killRatio = monsterEntity.getBattleComponent().getKillRatioInfo();
        final List<HonorTarget> honorTargets = new ArrayList<>();
        for (Map.Entry<Long, Map<Long, Integer>> rallyKillRatioMap : killRatio.entrySet()) {
            ArmyEntity army = monsterEntity.getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, rallyKillRatioMap.getKey());
            if (army == null) {
                continue;
            }
            if (!army.isRallyArmy()) {
                // 为啥普通的有伤害？？
                LOGGER.error("{} sendRallyReward but {} is normal", this, army);
                continue;
            }
            for (Map.Entry<Long, Integer> entry : rallyKillRatioMap.getValue().entrySet()) {
                ArmyEntity subArmy = monsterEntity.getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, entry.getKey());
                if (subArmy == null) {
                    continue;
                }
                int rewardId = 0;
                int stage = 0;
                for (IntPairType pair : template.getDamageRewardPairList()) {
                    if (entry.getValue() < pair.getKey()) {
                        break;
                    }
                    stage++;
                    rewardId = pair.getValue();
                }
                if (stage == 0) {
                    continue;
                }
                StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
                List<ItemReward> rewardList = ResHolder.getResService(ItemResService.class).randomReward(rewardId);
                if (CollectionUtils.isNotEmpty(rewardList)) {
                    monsterEntity.getRewardComponent().sendRewardMsg(subArmy.getScenePlayer(), rewardList);
                    for (ItemReward reward : rewardList) {
                        // 填充邮件展示
                        Struct.ItemPair.Builder pairBuilder = Struct.ItemPair.newBuilder()
                                .setItemTemplateId(reward.getItemTemplateId()).setCount(reward.getCount());
                        mailSendParams.getItemRewardBuilder().addDatas(pairBuilder.build());
                    }
                }
                // 发送加经验 击杀记录
                int exp = SceneAddCalc.getFightMonsterExp(subArmy.getBattleComponent().getBattleRole(), template.getExp());
                monsterEntity.getRewardComponent().sendMonsterKillMsg(subArmy, exp);
                // 发送邮件
                int mailId = monsterEntity.getTemplate().getMailId();
                if (mailId <= 0) {
                    continue;
                }
                mailSendParams.setMailTemplateId(mailId)
                        .setIsOnlyForShow(true)
                        .getContentBuilder()
                        .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                        .getDisplayDataBuilder().getParamsBuilder()
                        .addDatas(MsgHelper.buildDisPlayPoint(curPoint, monsterEntity.getScene().getMapType(), monsterEntity.getScene().getMapIdForPoint()))
                        .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, level))
                        .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, entry.getValue()))
                        .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_INT64, stage));
                MailUtil.sendMailToPlayer(
                        CommonMsg.MailReceiver.newBuilder()
                                .setPlayerId(subArmy.getPlayerId())
                                .setZoneId(subArmy.getScenePlayer().getZoneId())
                                .build(),
                        mailSendParams.build());

                LOGGER.info("RallyMonster sendKillAndRewardAll playerId:{} damage:{} stage:{} reward:{}", subArmy.getPlayerId(), entry.getValue(), stage, rewardList);
            }
            final HonorTarget honorTarget = new HonorTarget(0, army.getZoneId(), army.getClanId());
            honorTargets.add(honorTarget);
        }
        if (honorTargets.size() <= 0) {
            return;
        }
        SceneEntity sceneEntity = monsterEntity.getScene();
        if (sceneEntity == null) {
            return;
        }
    }
}

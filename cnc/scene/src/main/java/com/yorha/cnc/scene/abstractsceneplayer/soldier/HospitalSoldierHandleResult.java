package com.yorha.cnc.scene.abstractsceneplayer.soldier;

import com.google.common.collect.Maps;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.Struct;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 医院接受重伤士兵的处理结果，多少能进医院，多少会死掉
 */
public class HospitalSoldierHandleResult {

    private final Map<Integer, Unit> units;

    public HospitalSoldierHandleResult() {
        this.units = Maps.newHashMap();
    }

    public void addUnit(Unit unit) {
        Unit exist = units.get(unit.getSoldierId());
        if (exist == null) {
            units.put(unit.getSoldierId(), unit);
        } else {
            exist.setWaitingCure(exist.waitingCure + unit.waitingCure);
            exist.setDead(exist.dead + unit.dead);
        }
    }

    public void addUnit(int soldierId, int waitingCure, int dead) {
        addUnit(new Unit(soldierId, waitingCure, dead));
    }

    public HospitalSoldierHandleResult withAllDead(List<Struct.PlayerHospitalSoldier> hospitalSoldiers) {
        for (Struct.PlayerHospitalSoldier hospitalSoldier : hospitalSoldiers) {
            addUnit(new Unit(hospitalSoldier.getSoldierId(), 0, hospitalSoldier.getSevereNum()));
        }
        return this;
    }

    public int totalDeadNum() {
        int total = 0;
        for (Unit unit : units.values()) {
            total += unit.dead;
        }
        return total;
    }

    public Collection<Unit> getUnits() {
        return units.values();
    }

    public static class Unit {
        private final int soldierId;
        private int waitingCure;
        private int dead;

        public Unit(int soldierId, int waitingCure, int dead) {
            this.soldierId = soldierId;
            this.waitingCure = waitingCure;
            this.dead = dead;
        }

        public int getSoldierId() {
            return soldierId;
        }

        public int getWaitingCure() {
            return waitingCure;
        }

        public int getDead() {
            return dead;
        }

        void setWaitingCure(int waitingCure) {
            this.waitingCure = waitingCure;
        }

        void setDead(int dead) {
            this.dead = dead;
        }

        @Override
        public String toString() {
            return StringUtils.reflectionToString(this);
        }
    }

    @Override
    public String toString() {
        return StringUtils.reflectionToString(this);
    }
}

package com.yorha.cnc.scene.monster.component;

import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjHateListComponent;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class MonsterHateListComponent extends SceneObjHateListComponent {

    public MonsterHateListComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public MonsterEntity getOwner() {
        return (MonsterEntity) super.getOwner();
    }


    @Override
    protected void onEndRoundEvent(BattleRoleSettleRoundEvent event) {
        if (getOwner().getTemplate().getCloseDamageHate()) {
            return;
        }
        super.onEndRoundEvent(event);
    }

    @Override
    protected CommonEnum.HateListType getHateListType() {
        return getOwner().getTemplate().getHateListType();
    }

    @Override
    public int getHateExpire() {
        final int hateExpire = getOwner().getTemplate().getHateExpire();
        if (hateExpire > 0) {
            return hateExpire;
        }
        return super.getHateExpire();
    }
}

package com.yorha.cnc.scene.mapBuilding.component.stagenode.territory;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 荒废阶段 配0表示永久不荒废
 *
 * <AUTHOR>
 */
public class DesertedStageNode extends StageNode {
    private static final Logger LOGGER = LogManager.getLogger(DesertedStageNode.class);

    public DesertedStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_DESERTED;
    }

    @Override
    public void onLoad() {
        if (getTemplate().getDiscardTime() == 0) {
            return;
        }
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::timer2DesertedEnd);
            return;
        }
        onDesertedEnd();
    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        getProp().setStateStartTsMs(ts).setState(getStage());
        if (getTemplate().getDiscardTime() == 0) {
            getProp().setStateEndTsMs(0);
            return;
        }
        long endTs = TimeUtils.second2Ms(getTemplate().getDiscardTime()) + ts;
        // 打个qLog
        getOwner().getQLogComponent().sendExpansionLog(getProp().getOwnerClanId(), "waste_guild_building", 0);

        if (endTs <= SystemClock.now()) {
            timer2DesertedEnd();
            return;
        }
        getProp().setStateEndTsMs(endTs);
        addStageTimer(this::timer2DesertedEnd);
    }

    private void timer2DesertedEnd() {
        getComponent().clearStageTimer();
        // 打个qLog
        getOwner().getQLogComponent().sendExpansionLog(getProp().getOwnerClanId(), "complete_waste_building", 0);
        onDesertedEnd();
    }

    private void onDesertedEnd() {
        getComponent().transNeutralStage();
    }
}

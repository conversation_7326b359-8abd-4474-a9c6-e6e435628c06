package com.yorha.cnc.scene.gm.command.monster;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * 设置最高击杀野怪等级
 *
 * <AUTHOR>
 */
public class SetKillMonsterMaxLevel implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        ScenePlayerEntity scenePlayer = actor.getScenePlayer(playerId);
        int parseInt = Integer.parseInt(args.get("level"));
        scenePlayer.getProp().setKillMonsterMaxLevel(parseInt);
    }

    @Override
    public String showHelp() {
        return "SetKillMonsterMaxLevel level={value}";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MONSTER;
    }
}

package com.yorha.cnc.scene.army;


import com.google.protobuf.Message;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.clanResBuilding.ClanResBuildingEntity;
import com.yorha.cnc.scene.common.ScenePushNotificationHelper;
import com.yorha.cnc.scene.dorpObject.DropObjectEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.cnc.scene.event.battle.SkillTriggerEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.option.DeleteOption;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.ArmyProp;
import com.yorha.game.gen.prop.ScenePlayerArmyStatusProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.proto.CommonEnum.ArmyActionType;
import com.yorha.proto.CommonEnum.RallyArmyRoleType;
import com.yorha.proto.CommonEnum.TriggerType;
import com.yorha.proto.Core.Code;
import com.yorha.proto.EntityAttrDb;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.Struct;
import com.yorha.proto.Struct.SceneTransportPlane;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayer.ArmyActionInfo;
import com.yorha.proto.StructPlayer.CreateArmy_C2S_Param;
import com.yorha.proto.StructPlayer.Troop;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 */
public class ArmyFactory {
    private static final Logger LOGGER = LogManager.getLogger(ArmyFactory.class);


    /**
     * 创建行军  创建前会异步寻路 失败就中断
     */
    public static void createArmy(SceneEntity scene, long scenePlayerId, CreateArmy_C2S_Param param, int costEnergy, BiConsumer<Exception, ArmyEntity> onComplete) {
        LOGGER.debug("ScenePlayer:{} try create army in {} param:{}", scenePlayerId, scene, param);
        final ArmyActionInfo armyAction = param.getArmyAction();
        AbstractScenePlayerEntity scenePlayer = scene.getPlayerMgrComponent().getScenePlayer(scenePlayerId);
        if (scenePlayer.getMainCity().getTransformComponent().isAscend()) {
            throw new GeminiException(ErrorCode.CITY_CITY_IS_ASCEND);
        }
        // 出征部队数量检测
        scenePlayer.getArmyMgrComponent().checkArmyCreate();
        // 行军动作合法性预检测
        ArmyProp armyProp = ArmyFactory.checkArmyAction(scene, scenePlayerId, param);
        // 构建行军出生点
        CityEntity mainCity = scenePlayer.getMainCity();
        Point bornPoint = getBornPoint(mainCity, armyAction);
        armyProp.getMove().getCurPoint().setX(bornPoint.getX()).setY(bornPoint.getY());
        // 设置朝向
        armyProp.getMove().getYaw().setX(bornPoint.getX() - mainCity.getCurPoint().getX())
                .setY(bornPoint.getY() - mainCity.getCurPoint().getY());
        // 预先获得寻路路径  如果寻路前不能走 也属于直接exception了
        getArmyPath(scene, scenePlayer, armyAction, bornPoint,
                (code, armyPath) -> {
                    // 寻路失败
                    if (!ErrorCode.isOK(code)) {
                        onComplete.accept(new GeminiException(code), null);
                        return;
                    }
                    try {
                        ArmyEntity armyEntity = afterSearchPathWhenCreate(scene, scenePlayer, param, costEnergy, armyProp, armyPath);
                        onComplete.accept(null, armyEntity);
                    } catch (Exception e) {
                        onComplete.accept(e, null);
                    }
                });
    }

    /**
     * 创建行军异步寻路回调
     */
    private static ArmyEntity afterSearchPathWhenCreate(SceneEntity scene, AbstractScenePlayerEntity scenePlayer,
                                                        CreateArmy_C2S_Param param, int costEnergy,
                                                        ArmyProp armyProp, MoveData armyPath) {
        final ArmyActionInfo armyAction = param.getArmyAction();
        final ArmyActionType armyActionType = armyAction.getArmyActionType();
        final List<Integer> recommendSoldierTypeList = param.getRecommendSoldierTypeList().getDatasList();
        // 构建行军基础属性
        armyProp.setOwnerId(scenePlayer.getEntityId())
                .setClanId(scenePlayer.getClanId())
                .setCamp(scenePlayer.getCampEnum())
                .setZoneId(scenePlayer.getZoneId());
        armyProp.getCardHead().mergeFromSs(scenePlayer.getCardHead().getCopySsBuilder().build());
        if (scenePlayer.getClanId() != 0 && scene.isMainScene()) {
            SceneClanEntity sceneClan = scene.getClanMgrComponent().getSceneClan(scenePlayer.getClanId());
            if (sceneClan != null) {
                armyProp.setClanSname(sceneClan.getClanSimpleName());
            }
        }
        long armyId = scene.ownerActor().nextId();
        Troop troop = scenePlayer.getArmyMgrComponent().beforeCreateArmy(armyId, param.getTroopInfo());
        armyProp.getTroop().mergeFromSs(troop);
        armyProp.unMarkAll();
        ArmyBuilder armyBuilder = new ArmyBuilder(scene, armyId, armyProp, null);
        // 创建行军
        ArmyEntity armyEntity = new ArmyEntity(armyBuilder);
        armyEntity.getMoveComponent().setCreatePrePath(armyPath);
        scenePlayer.getArmyMgrComponent().onNewArmy(armyEntity);
        armyEntity.addIntoScene();
        armyEntity.getEventDispatcher().dispatch(new SkillTriggerEvent(TriggerType.TT_LEAVE_BUILDING));
        try {
            armyEntity.getBehaviourComponent().handleAction(armyAction, costEnergy);
        } catch (Exception e) {
            armyEntity.getMoveComponent().onReturnCityEnd();
            if (GeminiException.isLogicException(e)) {
                LOGGER.warn("army handle action failed.", e);
            } else {
                LOGGER.error("army handle action failed.", e);
            }
            throw e;
        }
        armyEntity.getMoveComponent().clearCreatePrePath();
        if (scene.isMainScene()) {
            armyEntity.getDbComponent().insertIntoDb();
        }
        // 创建集结
        if (armyActionType == ArmyActionType.AAT_CreateRally) {
            RallyEntity rallyEntity = scenePlayer.getRallyComponent().createRally(
                    armyEntity,
                    armyAction.getTargetId(),
                    armyAction.getWaitSecondsTime(),
                    costEnergy
            );
            // 集结设置推荐部队
            rallyEntity.setRecommendSoldierType(scenePlayer.getEntityId(), recommendSoldierTypeList);
            // 集结车头直接到达
            armyEntity.getRallyComponent().arriveRally(rallyEntity.getEntityId(), RallyArmyRoleType.RART_Leader);
        }
        if (scenePlayer instanceof ScenePlayerEntity) {
            ((ScenePlayerEntity) scenePlayer).getQlogComponent().sendArmyQLog(armyEntity, "armed");
        }
        LOGGER.info("{} createArmy {}", scenePlayer, armyEntity.getEntityId());
        return armyEntity;
    }

    private static void getArmyPath(SceneEntity scene, AbstractScenePlayerEntity scenePlayer, ArmyActionInfo action, Point bornPoint, BiConsumer<Integer, MoveData> onComplete) {
        if (action.getDebugFastMove()) {
            onComplete.accept(ErrorCode.OK.getCodeId(), null);
            return;
        }
        PathFindMgrComponent pathFindMgrComponent = scene.getPathFindMgrComponent();
        CityEntity mainCity = scenePlayer.getMainCity();
        Point targetPoint = Point.valueOf(action.getTargetPoint().getX(), action.getTargetPoint().getY());
        long targetId = action.getTargetId();
        SceneObjEntity targetEntity;
        // 移动类型
        int moveType = -1;
        // 是否使用寻路出来的数据
        boolean needUse = true;
        // 压测无视关卡阻挡
        boolean isIgnoreCrossingOwner = ServerContext.getServerDebugOption().isIgnoreCrossingOwner();
        switch (action.getArmyActionType()) {
            case AAT_Move:
                if (targetId != 0 && targetId != 1) {
                    targetEntity = scene.getObjMgrComponent().getSceneObjEntityWithException(targetId);
                    targetPoint = targetEntity.getTransformComponent().getFollowPoint(bornPoint, null);
                    moveType = isIgnoreCrossingOwner ? GameLogicConstants.DIED_ARMY_MOVE : GameLogicConstants.NORMAL_MOVE_TARGET;
                } else {
                    moveType = isIgnoreCrossingOwner ? GameLogicConstants.DIED_ARMY_MOVE : GameLogicConstants.NORMAL_MOVE_POINT;
                }
                break;
            case AAT_CreateRally:
                // 创建集结得先看看能不能过去打  但是路径不用记录下来
                needUse = false;
            case AAT_PICK_UP:
            case AAT_Battle:
            case AAT_ASSIST:
            case AAT_GATHER:
                targetEntity = scene.getObjMgrComponent().getSceneObjEntityWithException(targetId);
                targetPoint = targetEntity.getTransformComponent().getBeBattlePoint(bornPoint, scenePlayer.getClanId(), GameLogicConstants.NORMAL_MOVE_TARGET);
                moveType = isIgnoreCrossingOwner ? GameLogicConstants.DIED_ARMY_MOVE : GameLogicConstants.NORMAL_MOVE_TARGET;
                break;
            case AAT_JoinRally:
                RallyEntity rallyEntity = scenePlayer.getRallyComponent().getRallyEntityWithException(targetId);
                targetPoint = rallyEntity.getArmyMgrComponent().getRallyCurPoint();
                moveType = isIgnoreCrossingOwner ? GameLogicConstants.DIED_ARMY_MOVE : GameLogicConstants.NORMAL_MOVE_TARGET;
                break;
            default:
                break;
        }
        // 不用寻路
        if (moveType == -1) {
            onComplete.accept(ErrorCode.OK.getCodeId(), null);
            return;
        }
        final Point endPoint = targetPoint;
        final int moveTarget = moveType;
        boolean finalNeedUse = needUse;
        MoveData ret = pathFindMgrComponent.searchPathAsync(mainCity, bornPoint, endPoint, moveTarget, 0,
                (result) -> {
                    // 可能生成过程中报错了，那会给result的errorCode赋值的
                    MoveData moveData = result.genMoveData(pathFindMgrComponent, moveTarget, 0, endPoint);
                    onComplete.accept(result.getCode(), finalNeedUse ? moveData : null);
                });
        // 是即时返回的   无视静态阻挡 大世界且同州
        if (ret != null) {
            onComplete.accept(ErrorCode.OK.getCodeId(), ret);
        }
    }

    /**
     * 检测创建行军时的合法性
     */
    private static ArmyProp checkArmyAction(SceneEntity scene, long scenePlayerId, CreateArmy_C2S_Param param) {
        ArmyActionInfo action = param.getArmyAction();
        Troop troop = param.getTroopInfo();
        AbstractScenePlayerEntity scenePlayer = scene.getPlayerMgrComponent().getScenePlayer(scenePlayerId);
        long targetId = action.getTargetId();
        ArmyActionType armyActionType = action.getArmyActionType();
        ArmyProp armyProp = new ArmyProp();
        Point targetPoint = Point.valueOf(action.getTargetPoint().getX(), action.getTargetPoint().getY());
        switch (armyActionType) {
            case AAT_Move:
                if (!action.hasTargetPoint()) {
                    // 没传坐标 但是有传目标id  OK
                    if (targetId != 0 && targetId != 1) {
                        break;
                    }
                    throw new GeminiException(ErrorCode.ARMY_NOT_HAS_TARGET_POINT);
                }
                ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(targetPoint, scene.getMapConfig());
                if (errorCode.isNotOk()) {
                    throw new GeminiException(errorCode);
                }
                break;
            case AAT_CreateRally:
                Code checkCreateRally = scenePlayer.getRallyComponent().checkCreateRally(targetId, action.getWaitSecondsTime(), getTroopSoldierNum(troop));
                if (!ErrorCode.isOK(checkCreateRally)) {
                    throw new GeminiException(checkCreateRally.getId());
                }
                armyProp.setRallyRole(RallyArmyRoleType.RART_Leader);
                checkCanAttackWithoutArmy(scene, scenePlayer, targetId, armyActionType);
                break;
            case AAT_Battle:
                checkCanAttackWithoutArmy(scene, scenePlayer, targetId, armyActionType);
                break;
            case AAT_JoinRally:
                // 加入集结 预检测
                Code checkJoinRally = scenePlayer.getRallyComponent().checkJoinRally(targetId, getTroopSoldierNum(troop));
                if (!ErrorCode.isOK(checkJoinRally)) {
                    throw new GeminiException(checkJoinRally.getId());
                }
                break;
            case AAT_ASSIST:
                // 援助 预检测
                Code checkAssistCode = scenePlayer.getRallyComponent().checkAssist(targetId, getTroopSoldierNum(troop), 0);
                if (!ErrorCode.isOK(checkAssistCode)) {
                    throw new GeminiException(checkAssistCode.getId());
                }
                break;
            case AAT_Transport:
                if (!scene.getMapConfig().getTransport()) {
                    throw new GeminiException(ErrorCode.ARMY_CANT_TRANSPORT.getCodeId());
                }
                // 空投运输机 预检测
                SceneTransportPlane transportPlane = param.getArmyAction().getTransportPlane();
                if (transportPlane.getPlaneSpeed() <= 0) {
                    throw new GeminiException(ErrorCode.TRANSPORT_PLANE_NO_SPEED.getCodeId());
                }
                Code checkPoint = scene.getPathFindMgrComponent().isPointWalkable(targetPoint);
                if (!ErrorCode.isOK(checkPoint)) {
                    LOGGER.info("transport point not walkable. point: {}", targetPoint);
                    throw new GeminiException(checkPoint.getId());
                }
                if (scene.isMainScene()) {
                    // 运输机是否可达 需要通过州联通校验
                    SceneClanEntity sceneClan = scenePlayer.getSceneClan();
                    Point curPoint = scenePlayer.getMainCity().getCurPoint();
                    if (sceneClan == null) {
                        int regionId1 = MapGridDataManager.getRegionId(scene.getMapId(), curPoint);
                        int regionId2 = MapGridDataManager.getRegionId(scene.getMapId(), targetPoint);
                        if (regionId1 != regionId2) {
                            throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
                        }
                    } else if (!sceneClan.getCrossComponent().checkSrcToEndCanPass(curPoint, targetPoint)) {
                        throw new GeminiException(ErrorCode.MOVE_NO_CROSS);
                    }
                }
                armyProp.getTransportPlane().mergeFromSs(transportPlane);
                break;
            case AAT_PICK_UP:
                DropObjectEntity dropObject = scene.getObjMgrComponent().getSceneObjWithType(DropObjectEntity.class, targetId);
                if (dropObject == null || dropObject.isDestroy()) {
                    throw new GeminiException(ErrorCode.DROP_OBJECT_ENTITY_CANT_PICK_UP.getCodeId());
                }
                // 掉落物本身限制
                dropObject.getPickUpComponent().startPickUpCheck(0, scenePlayerId);
                // 拾取组 限制
                if (!scenePlayer.getPickUpComponent().checkCanPickUp(dropObject)) {
                    throw new GeminiException(ErrorCode.DROP_OBJECT_PLAYER_PICK_LIMIT.getCodeId());
                }
                break;
            case AAT_GATHER:
                SceneObjEntity entity = scene.getObjMgrComponent().getSceneObjEntityWithException(targetId);

                if (entity.getEntityType() == EntityType.ET_ResBuilding) {
                    if (!scenePlayer.checkResourceUnlock(((ResBuildingEntity) entity).getTemplate().getResType().getNumber())) {
                        throw new GeminiException(ErrorCode.COLLECT_RESOURCE_LOCK);
                    }
                } else if (entity.getEntityType() == EntityType.ET_ClanResBuilding) {
                    if (!scenePlayer.checkResourceUnlock(((ClanResBuildingEntity) entity).getCurrencyType().getNumber())) {
                        throw new GeminiException(ErrorCode.COLLECT_RESOURCE_LOCK);
                    }
                    Code checkCollectCode = scenePlayer.getRallyComponent().checkCollect(targetId);
                    if (!ErrorCode.isOK(checkCollectCode)) {
                        throw new GeminiException(checkCollectCode.getId());
                    }
                }
                break;
            case AAT_Stay:
            case AAT_Return:
                break;
            default:
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        return armyProp;
    }

    /**
     * check 是否能攻击，没有创建army
     */
    public static void checkCanAttackWithoutArmy(SceneEntity scene, AbstractScenePlayerEntity scenePlayer, long targetId, ArmyActionType actionType) {
        SceneObjEntity targetEntity = scene.getObjMgrComponent().getSceneObjEntityWithException(targetId);
        checkCanAttackCom(scene, scenePlayer, targetEntity, actionType);

        if (targetEntity.getEntityType() == EntityType.ET_MapBuilding) {
            if (scenePlayer.getClanId() == targetEntity.getClanId()) {
                throw new GeminiException(ErrorCode.BATTLE_CANT.getCodeId());
            }
        }

        // 目标是否可以被进攻检测
        // 集结不检测合围
        ErrorCode errorCode = targetEntity.canBeAttackWithCode(null, scenePlayer, actionType != ArmyActionType.AAT_CreateRally);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
    }

    /**
     * check 是否能攻击，有army
     */
    public static void checkCanAttackWithArmy(SceneEntity scene, ArmyEntity armyEntity, long targetId) {
        SceneObjEntity targetEntity = scene.getObjMgrComponent().getSceneObjEntityWithException(targetId);
        checkCanAttackCom(scene, armyEntity.getScenePlayer(), targetEntity, ArmyActionType.AAT_None);

        // 是否可以进攻目标检测
        ErrorCode errorCode = armyEntity.canBattleWithCode(targetEntity, true);
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
    }

    private static void checkCanAttackCom(SceneEntity scene, AbstractScenePlayerEntity scenePlayer, SceneObjEntity targetEntity, ArmyActionType actionType) {
        if (targetEntity.getBattleComponent() == null) {
            throw new GeminiException(ErrorCode.BATTLE_CANT.getCodeId());
        }
        if (targetEntity.getEntityType() == EntityType.ET_MapBuilding) {
            if (scenePlayer.getClanId() == 0) {
                throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
            }
            // 联盟据点连接检测
            SceneClanEntity sceneClan = scene.getClanMgrComponent().getSceneClan(scenePlayer.getClanId());
            if (sceneClan == null) {
                throw new GeminiException(ErrorCode.CLAN_NOT_IN.getCodeId());
            }
            MapBuildingEntity mapBuilding = (MapBuildingEntity) targetEntity;
            ErrorCode checkCanAttack = sceneClan.getMapBuildingComponent().checkCanAttack(mapBuilding);
            if (!checkCanAttack.isOk()) {
                throw new GeminiException(checkCanAttack.getCodeId());
            }
        }
        // 城池特殊逻辑
        if (targetEntity.getEntityType() == EntityType.ET_City) {
            // 城池8级前不能打别人的城池
            if (scene.isBigScene() && scenePlayer.getMainCity().getProp().getLevel() < ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getRemoveProtectTowerLevel()) {
                throw new GeminiException(ErrorCode.BATTLE_CANT.getCodeId());
            }
        }
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(targetEntity.getCurPoint(), scene.getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
    }

    private static Point getBornPoint(CityEntity cityEntity, ArmyActionInfo armyAction) {
        ArmyActionType armyActionType = armyAction.getArmyActionType();
        long targetId = armyAction.getTargetId();
        Struct.Point actionTargetPoint = armyAction.getTargetPoint();
        Point cityPoint = cityEntity.getCurPoint();
        // 默认先采用传上来的坐标  有目标的在下面处理
        Point targetPoint = Point.valueOf(actionTargetPoint.getX(), actionTargetPoint.getY());
        LOGGER.debug("born {} {} {}", armyActionType, targetId, actionTargetPoint);
        switch (armyActionType) {
            case AAT_Move:
                if (targetId != 0) {
                    SceneObjEntity sceneObjEntity = cityEntity.getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
                    targetPoint = sceneObjEntity.getCurPoint();
                }
                break;
            case AAT_Transport:
                break;
            case AAT_ASSIST:
            case AAT_PICK_UP:
            case AAT_Battle:
            case AAT_GATHER:
                SceneObjEntity sceneObjEntity = cityEntity.getScene().getObjMgrComponent().getSceneObjEntityWithException(targetId);
                targetPoint = sceneObjEntity.getCurPoint();
                break;
            case AAT_CreateRally:
                // 创建集结
                return cityPoint;
            case AAT_JoinRally:
                // 加入集结
                AbstractScenePlayerEntity player = cityEntity.getScenePlayer();
                RallyEntity rallyEntity = player.getRallyComponent().getRallyEntityWithException(targetId);
                targetPoint = rallyEntity.getArmyMgrComponent().getRallyCurPoint();
                break;
            default:
                LOGGER.error("{} create army wrong action {}", cityEntity, armyActionType);
                throw new GeminiException(ErrorCode.ARMY_ACTION_ERROR);
        }
        return Point.getPointWithDisToSrcPoint(Point.valueOf(cityPoint.getX(), cityPoint.getY()),
                targetPoint,
                cityEntity.getTransformComponent().getPathCollisionRadius() + 1);
    }

    private static long getTroopSoldierNum(StructPlayer.Troop troop) {
        long num = 0;
        for (Struct.Soldier pb : troop.getTroop().getDatasMap().values()) {
            num += pb.getNum();
        }
        return num;
    }

    /**
     * db恢复army
     */
    public static void restoreArmy(SceneEntity sceneEntity, EntityAttrDb.EntityAttrDB fullAttr, EntityAttrDb.EntityAttrDB changedAttr) {
        ArmyProp armyProp = ArmyProp.of(fullAttr.getArmyAttr(), changedAttr.getArmyAttr());
        AbstractScenePlayerEntity abstractScenePlayer = sceneEntity.getPlayerMgrComponent().getScenePlayerOrNull(armyProp.getOwnerId());
        if (!(abstractScenePlayer instanceof ScenePlayerEntity)) {
            LOGGER.error("restoreArmy {} but player not match", armyProp);
            return;
        }
        ScenePlayerEntity scenePlayer = (ScenePlayerEntity) abstractScenePlayer;
        ScenePlayerArmyStatusProp armyV = scenePlayer.getProp().getArmyModel().getArmyV(fullAttr.getEntityId());
        if (armyV == null) {
            LOGGER.error("restoreArmy {} has not army status prop {}", scenePlayer, fullAttr.getEntityId());
            TcaplusDb.SceneObjTable.Builder request = TcaplusDb.SceneObjTable.newBuilder();
            request.setZoneId(sceneEntity.getZoneId());
            request.setEntityId(fullAttr.getEntityId());
            request.setSceneId(sceneEntity.getEntityId());
            final DeleteAsk<Message.Builder> msg = new DeleteAsk<>(request, DeleteOption.newBuilder().withRetry().build());
            sceneEntity.ownerActor().tellGameDb(msg);
            return;
        }
        ArmyBuilder armyBuilder = new ArmyBuilder(sceneEntity, fullAttr.getEntityId(), armyProp, armyV);
        ArmyEntity armyEntity = new ArmyEntity(armyBuilder, true);
        scenePlayer.getSoldierMgrComponent().onArmyRestore(armyEntity);
        armyEntity.addIntoScene();
        armyEntity.getScenePlayer().getArmyMgrComponent().onNewArmy(armyEntity);
        // kvk 兼容线上数据
        if (armyEntity.getZoneId() == 0) {
            armyEntity.getProp().setZoneId(armyEntity.getScenePlayer().getZoneId());
        }
        LOGGER.info("create {} in {} success", armyEntity, sceneEntity);
    }

    /**
     * 创建一个集结行军
     */
    public static void createArmyFromRally(RallyEntity rallyEntity) {
        LOGGER.info("try create army from {}", rallyEntity);
        final SceneEntity sceneEntity = rallyEntity.getSceneEntity();
        ArmyEntity leaderArmy = rallyEntity.getLeaderArmy();
        AbstractScenePlayerEntity leaderPlayer = leaderArmy.getScenePlayer();

        CityEntity mainCity = leaderPlayer.getMainCity();
        // check能不能走
        StructPlayer.ArmyActionInfo.Builder armyActionBuilder = StructPlayer.ArmyActionInfo.newBuilder();
        armyActionBuilder.setArmyActionType(ArmyActionType.AAT_Battle)
                .setTargetId(rallyEntity.getProp().getTargetId());
        final StructPlayer.ArmyActionInfo action = armyActionBuilder.build();
        final Point bornPoint = getBornPoint(mainCity, action);
        if (bornPoint == null) {
            LOGGER.error("createArmyFromRally get bornPoint fail");
            rallyEntity.dismiss(RallyDismissReason.RDR_NO_PATH);
            return;
        }
        // 预先获得寻路路径  如果不能走 直接exception了
        getArmyPath(sceneEntity, leaderPlayer, action, bornPoint,
                (code, path) -> {
                    // 寻路失败
                    if (!ErrorCode.isOK(code)) {
                        rallyEntity.dismiss(RallyDismissReason.RDR_NO_PATH);
                        return;
                    }
                    try {
                        afterSearchPathWhenRally(sceneEntity, rallyEntity, leaderArmy, bornPoint, action, path);
                    } catch (Exception e) {
                        rallyEntity.dismiss(RallyDismissReason.RDR_NO_PATH);
                        if (!GeminiException.isLogicException(e)) {
                            LOGGER.error("exception_perf create rally army failed {} ", rallyEntity, e);
                        } else {
                            LOGGER.info("exception_perf create rally army failed {} ", rallyEntity, e);
                        }
                    }
                });
    }

    /**
     * 创建集结先看能不能走 再真正构建Army
     */
    private static void afterSearchPathWhenRally(SceneEntity sceneEntity, RallyEntity rallyEntity, ArmyEntity leaderArmy, Point bornPoint, ArmyActionInfo armyActionInfoPb, MoveData armyPath) {
        // 异步寻路可能可能刚好卡集结解散了
        if (rallyEntity.isDestroy()) {
            return;
        }
        AbstractScenePlayerEntity leaderPlayer = leaderArmy.getScenePlayer();
        CityEntity mainCity = leaderPlayer.getMainCity();
        // 构建行军基础属性
        ArmyProp armyProp = new ArmyProp();
        armyProp.setOwnerId(leaderPlayer.getEntityId())
                .setClanId(leaderArmy.getClanId())
                .setClanSname(leaderArmy.getProp().getClanSname())
                .setCamp(leaderPlayer.getCampEnum())
                .setZoneId(leaderPlayer.getZoneId())
                .setRallyRole(RallyArmyRoleType.RART_RallySelf)
                .setCurRallyId(rallyEntity.getEntityId())
                .getMove().getCurPoint().setX(bornPoint.getX()).setY(bornPoint.getY());
        // 联盟旗帜
        sceneEntity.getClanMgrComponent().formClanFlag(armyProp.getClanFlag(), leaderPlayer);

        armyProp.getMove().getYaw().setX(bornPoint.getX() - mainCity.getCurPoint().getX()).setY(bornPoint.getY() - mainCity.getCurPoint().getY());
        armyProp.getCardHead().mergeFromSs(leaderArmy.getProp().getCardHead().getCopySsBuilder().build());
        // 设置集合体兵力
        HashMap<Integer, SoldierProp> totalTroop = rallyEntity.getArmyMgrComponent().mergeInRallySoldiers();
        long total = 0;
        for (Entry<Integer, SoldierProp> entry : totalTroop.entrySet()) {
            // 这里put的troop数据其实会在ArmyBattleComponent.init这里被覆盖掉
            armyProp.getTroop().putTroopV(entry.getValue());
            total += Soldier.aliveCountOf(entry.getValue());
        }
        // 集结体特殊兵力槽
        armyProp.setHpMax(total);
        LOGGER.debug("rally soldier hp {}", total);
        // 主将拷贝
        Struct.Hero.Builder mainHeroBuilder = Struct.Hero.newBuilder();
        leaderArmy.getProp().getTroop().getMainHero().copyToSs(mainHeroBuilder);
        armyProp.getTroop().getMainHero().mergeFromSs(mainHeroBuilder.build());
        // 副将拷贝
        Struct.Hero.Builder deputyHeroBuilder = Struct.Hero.newBuilder();
        leaderArmy.getProp().getTroop().getDeputyHero().copyToSs(deputyHeroBuilder);
        armyProp.getTroop().getDeputyHero().mergeFromSs(deputyHeroBuilder.build());

        LOGGER.debug("createArmyFromRally total troop:{}", armyProp.getTroop());

        armyProp.unMarkAll();
        ArmyBuilder armyBuilder = new ArmyBuilder(sceneEntity, sceneEntity.ownerActor().nextId(), armyProp, null);
        // 创建行军
        ArmyEntity armyEntity = new ArmyEntity(armyBuilder);
        armyEntity.getMoveComponent().setCreatePrePath(armyPath);
        try {
            // 加入到场景
            armyEntity.addIntoScene();
            leaderPlayer.getArmyMgrComponent().onNewArmy(armyEntity);
            armyEntity.getBehaviourComponent().handleAction(armyActionInfoPb, 0);
            armyEntity.getAoiNodeComponent().setAlwaysSyncPlayer(rallyEntity.getArmyMgrComponent().getInRallyArmyIds());
        } catch (Exception e) {
            LOGGER.warn("{} create rally army warn", rallyEntity, e);
            armyEntity.deleteObj();
            rallyEntity.dismiss(RallyDismissReason.RDR_TARGET_CANT_ATTACK);
            return;
        }
        // 加入到场景后再触发技能，否则会找不到army
        armyEntity.getEventDispatcher().dispatch(new SkillTriggerEvent(TriggerType.TT_LEAVE_BUILDING));
        // 集结不接受玩家改变操作
        armyEntity.getBehaviourComponent().setRecvPlayerAction(false);
        LOGGER.info("{} create {} in {} from rally:{} success", leaderPlayer, armyEntity, sceneEntity, rallyEntity);
        // 集结army不存盘
        rallyEntity.getArmyMgrComponent().onRallyArmyCreated(armyEntity);
        // 向被集结的玩家推送基地被集结消息

        final long targetId = rallyEntity.getProp().getTargetId();
        final SceneObjEntity sceneObjEntity = sceneEntity.getObjMgrComponent().getSceneObjEntity(targetId);
        if (sceneObjEntity instanceof CityEntity) {
            ScenePushNotificationHelper.pushBaseBeRallyNotification(sceneEntity, (CityEntity) sceneObjEntity);
        }
    }

    /**
     * 预创建的行军
     */
    public static ArmyEntity createDungeonPlayerArmy(AbstractScenePlayerEntity player, StructPlayer.Troop troop, Point point, Point yaw) {
        ArmyProp armyProp = new ArmyProp();
        armyProp.setOwnerId(player.getPlayerId())
                .setCamp(player.getCampEnum())
                .getMove().getCurPoint().setX(point.getX()).setY(point.getY());
        armyProp.getMove().getYaw().setX(yaw.getX()).setY(yaw.getY());
        armyProp.getCardHead().mergeFromSs(player.getCardHead().getCopySsBuilder().build());
        armyProp.getTroop().mergeFromSs(troop);
        armyProp.unMarkAll();
        ArmyBuilder armyBuilder = new ArmyBuilder(player.getScene(), player.getScene().ownerActor().nextId(), armyProp, null);
        // 创建行军
        ArmyEntity armyEntity = new ArmyEntity(armyBuilder);
        player.getArmyMgrComponent().onNewArmy(armyEntity);
        armyEntity.addIntoScene();
        return armyEntity;
    }

    /**
     * 预创建的行军
     */
    public static ArmyEntity createSimulatePlayerArmy(AbstractScenePlayerEntity player, int id, StructPlayer.Troop troop, Point point, Point yaw) {
        ArmyProp armyProp = new ArmyProp();
        armyProp.setOwnerId(player.getPlayerId()).getMove().getCurPoint().setX(point.getX()).setY(point.getY());
        armyProp.getCardHead().mergeFromSs(player.getCardHead().getCopySsBuilder().build());
        armyProp.getTroop().mergeFromSs(troop);
        armyProp.getMove().getYaw().setX(yaw.getX()).setY(yaw.getY());
        armyProp.unMarkAll();
        ArmyBuilder armyBuilder = new ArmyBuilder(player.getScene(), id + BattleConstants.SHIT_ENTITY_ID_PREFIX, armyProp, null);
        // 创建行军
        ArmyEntity armyEntity = new ArmyEntity(armyBuilder);
        player.getArmyMgrComponent().onNewArmy(armyEntity);
        armyEntity.addIntoScene();
        return armyEntity;
    }

}

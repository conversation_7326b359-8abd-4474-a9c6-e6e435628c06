package com.yorha.cnc.scene.sceneObj.ai.stateMachine;

import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.common.statemachine.Action;
import com.yorha.common.statemachine.Transition;
import com.yorha.proto.CommonEnum.AIStateType;

/**
 * <AUTHOR>
 */
public class StateTransition implements Transition<AIStateType, AIEvent, SceneObjAiComponent> {

    private StateTransitionConfig builder;
    private Action<SceneObjAiComponent> action;

    public static StateTransition valueOf(StateTransitionConfig builder, Action<SceneObjAiComponent> action) {
        StateTransition config = new StateTransition();
        config.builder = builder;
        config.action = action;
        return config;
    }

    @Override
    public AIStateType sourceState() {
        return builder.getSourceState();
    }

    @Override
    public AIStateType targetState() {
        return builder.getTargetState();
    }

    @Override
    public AIEvent getEvent() {
        return builder.getEvent();
    }

    @Override
    public Action<SceneObjAiComponent> getAction() {
        return action;
    }


}

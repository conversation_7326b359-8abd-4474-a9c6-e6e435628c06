package com.yorha.cnc.scene.event.dungeon;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * <AUTHOR>
 */
public class PlayerLeaveEvent extends IEvent {
    private final AbstractScenePlayerEntity player;

    public PlayerLeaveEvent(AbstractScenePlayerEntity scenePlayer) {
        player = scenePlayer;
    }

    public AbstractScenePlayerEntity getPlayer() {
        return player;
    }
}

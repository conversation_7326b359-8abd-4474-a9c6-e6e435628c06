package com.yorha.cnc.scene.city.component;

import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CityKingdomSkillInfoProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.KingdomSkillTemplate;

/**
 * 城市王国管理器
 *
 * <AUTHOR>
 */
public class CityKingdomComponent extends SceneObjComponent<CityEntity> {
    private static final Logger LOGGER = LogManager.getLogger(CityKingdomComponent.class);

    public CityKingdomComponent(CityEntity owner) {
        super(owner);
    }

    public void kingdomSkillEffect(int skillId) {
        KingdomSkillTemplate kingdomSkillTemplate = ResHolder.getResService(KingdomTemplateResService.class).getKingdomSkillById(skillId);
        if (kingdomSkillTemplate == null) {
            throw new GeminiException(ErrorCode.KINGDOM_SKILL_NOT_EXIST);
        }

        long endTsMs = SystemClock.now() + TimeUtils.second2Ms(kingdomSkillTemplate.getEffectTime());
        LOGGER.info("CityKingdomComponent kingdomSkillEffect city={} skillId={} endTsMs={}", getOwner(), skillId, endTsMs);
        this.getOwner().getProp().getCityKingdomModel().putSkillMapV(
                new CityKingdomSkillInfoProp()
                        .setSkillId(skillId)
                        .setSkillEndTsMs(endTsMs));
    }
}

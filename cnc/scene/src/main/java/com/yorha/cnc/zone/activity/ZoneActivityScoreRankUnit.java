package com.yorha.cnc.zone.activity;

import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.ActivityScoreRankTemplate;
import res.template.ActivityTemplate;

/**
 * 这个是积分排行榜
 *
 * <AUTHOR>
 */
public class ZoneActivityScoreRankUnit extends BaseZoneActivityRankUnit {

    public ZoneActivityScoreRankUnit(ZoneActivity owner, int activityId, CommonEnum.ActivityUnitType unitType, int zoneUnitId) {
        super(owner, activityId, unitType, zoneUnitId);
    }

    @Override
    protected int getRankId(ActivityTemplate activityTemplate) {
        final int scoreRankConfId = activityTemplate.getScoreRankId();
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, scoreRankConfId);
        return scoreRankTemplate.getRankId();
    }

    @Override
    protected int getRewardMailId(ActivityTemplate activityTemplate) {
        final int scoreRankConfId = activityTemplate.getScoreRankId();
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, scoreRankConfId);
        return scoreRankTemplate.getRewardMailId();
    }

    @Override
    protected int getRankLimit(ActivityTemplate activityTemplate) {
        final int scoreRankConfId = activityTemplate.getScoreRankId();
        ActivityScoreRankTemplate scoreRankTemplate = ResHolder.getTemplate(ActivityScoreRankTemplate.class, scoreRankConfId);
        return scoreRankTemplate.getRankLimit();
    }

}
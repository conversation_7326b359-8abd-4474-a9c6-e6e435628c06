package com.yorha.cnc.scene.abstractsceneplayer.component;

import com.google.common.collect.Sets;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.cave.CaveEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneEntity;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.BuildingEntityType;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.plane.PlaneService;
import com.yorha.common.resource.resservice.scene.SceneMapDataTemplateService;
import com.yorha.common.utils.shape.Point;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapBuildingTemplate;
import res.template.SpyCostTypeTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.yorha.common.enums.error.ErrorCode.LOGISTICS_UNSUPPORT_ACTION;
import static com.yorha.proto.EntityAttrOuterClass.EntityType.ET_LogisticsPlane;
import static com.yorha.proto.EntityAttrOuterClass.EntityType.ET_MapBuilding;

/**
 * ScenePlayer上的运输机组件
 *
 * <AUTHOR> yuhy
 */
public class AbstractScenePlayerPlaneComponent extends AbstractComponent<AbstractScenePlayerEntity> {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerPlaneComponent.class);
    // 目前包含侦察机与运输机
    private final Set<Long> planeList = Sets.newHashSet();

    public AbstractScenePlayerPlaneComponent(AbstractScenePlayerEntity owner) {
        super(owner);
    }

    /**
     * 飞机列表移除飞机关注
     */
    public void removeAttentionSpy(long planeId) {
        LOGGER.info("removeAttention. player:{}, plane:{}", getOwner().getPlayerId(), planeId);
        planeList.remove(planeId);
    }

    /**
     * 飞机列表添加飞机关注
     */
    public void addAttention(long planeId) {
        LOGGER.info("addAttentionSpy. player:{}, lane:{}", getOwner().getPlayerId(), planeId);
        planeList.add(planeId);
    }

    public Set<Long> getPlaneList() {
        return Collections.unmodifiableSet(planeList);
    }

    public boolean haveSpyPlane(long planeId) {
        return planeList.contains(planeId);
    }

    public boolean checkPlaneEmpty() {
        return planeList.isEmpty();
    }

    /**
     * 运输机返航(EVA运输机技能屏蔽)
     */
    public void releasePlayerPlane(SsPlayerMisc.ReleasePlaneCmd cmd) {
        if (cmd.getTransportPlaneId() <= 0 && cmd.getBattlePlaneId() <= 0 && cmd.getSpyPlaneId() <= 0) {
            LOGGER.error("releasePlayerPlane not any one plane. cmd:{}", cmd);
            return;
        }
        LOGGER.info("release plane. cmd:{}", cmd);
        getOwner().tellPlayer(cmd);
    }

    public Struct.Currency getPlaneCost(CommonEnum.SpyPlaneActionType actionType, long targetId) {
        if (targetId <= 0) {
            return null;
        }
        if (actionType != CommonEnum.SpyPlaneActionType.SPAT_SPY) {
            return null;
        }
        SceneObjEntity obj = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        CommonEnum.SpyType spyTargetType = getOwner().getPlaneComponent().getSpyType(obj);
        if (spyTargetType == CommonEnum.SpyType.SPYTYPE_NONE) {
            throw new GeminiException("plane:{} planeCost spy not not support target: {}", getEntityId(), obj.getEntityType());
        }
        Integer num = ResHolder.getResService(PlaneService.class).getCostNum(spyTargetType, obj.getLevel());
        if (num == null) {
            return null;
        }
        CommonEnum.CurrencyType currencyType = ResHolder.getTemplate(SpyCostTypeTemplate.class, getOwner().getMainCity().getLevel()).getCurrencyType();
        return Struct.Currency.newBuilder().setType(currencyType.getNumber()).setCount(num).build();
    }

    /**
     * 获取侦察目标类型
     *
     * @param obj
     * @return
     */
    public CommonEnum.SpyType getSpyType(SceneObjEntity obj) {
        if (obj == null) {
            return CommonEnum.SpyType.SPYTYPE_NONE;
        }
        if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_City) {
            return CommonEnum.SpyType.ST_CITY;
        }
        if (obj.getEntityType() == ET_MapBuilding) {
            return CommonEnum.SpyType.ST_MAPBUILDING;
        }
        if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_Outbuilding) {
            return CommonEnum.SpyType.ST_OUTBUILDING;
        }
        if (obj.getEntityType() == EntityAttrOuterClass.EntityType.ET_Army) {
            ArmyEntity armyEntity = (ArmyEntity) obj;
            if (armyEntity.isRallyArmy()) {
                return CommonEnum.SpyType.ST_RALLY;
            }
            return CommonEnum.SpyType.ST_ARMY;
        }
        return CommonEnum.SpyType.SPYTYPE_NONE;
    }

    /**
     * 行为合法检测
     *
     * @return
     */
    public ErrorCode actionCheck(StructMsg.SpyInfo spyInfo, SsScenePlane.CheckMapCreateSpyPlaneAns.Builder builder) {
        ErrorCode res = ErrorCode.OK;
        //迷雾探索检测
        if (spyInfo.getActionType() == CommonEnum.SpyPlaneActionType.SPAT_EXPLORE) {
            res = checkExplore(spyInfo);
        }

        //侦察检测
        if (spyInfo.getActionType() == CommonEnum.SpyPlaneActionType.SPAT_SPY) {
            res = checkSpy(spyInfo);
            if (!ErrorCode.isOK(res.getCode())) {
                return res;
            }
        }

        if (spyInfo.getActionType() == CommonEnum.SpyPlaneActionType.SPAT_SURVEY) {
            SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(spyInfo.getTargetId());
            if (sceneObjEntity == null) {
                String desc = StringUtils.format("actionCheck survey cant find target:{}", spyInfo.getTargetId());
                throw new GeminiException(ErrorCode.FAILED, desc);
            }
            // MapBuild配表中的大地图物件
            if (sceneObjEntity instanceof BuildingEntityType) {
                MapBuildingTemplate buildingTemplate = ((BuildingEntityType) sceneObjEntity).getBuildingTemplate();
                builder.setBuildType(buildingTemplate.getType());
                int configId = 0;
                if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_Cave) {
                    configId = ((CaveEntity) sceneObjEntity).getProp().getConfigId();
                }

                if (sceneObjEntity.getEntityType() == EntityAttrOuterClass.EntityType.ET_MapBuilding) {
                    assert sceneObjEntity instanceof MapBuildingEntity;
                    MapBuildingEntity mapBuildEntity = (MapBuildingEntity) sceneObjEntity;
                    configId = mapBuildEntity.getPartId();
                }
                builder.setConfigId(configId);
            }
        }
        return res;
    }

    /**
     * 迷雾探索相关检查
     *
     * @return
     */
    private ErrorCode checkExplore(StructMsg.SpyInfo spyInfo) {
        //没有路点
        if (!spyInfo.hasPoint()) {
            throw new GeminiException("try create SpyPlane with error param point");
        }
        if (!spyInfo.hasSrcPoint()) {
            throw new GeminiException("try create SpyPlane with error param spoint");
        }
        StructPB.PointPB pointPB = spyInfo.getPoint();
        ErrorCode errorCode = SceneMapDataTemplateService.isLegalPoint(Point.valueOf(pointPB.getX(), pointPB.getY()), getOwner().getScene().getMapConfig());
        if (errorCode.isNotOk()) {
            throw new GeminiException(errorCode);
        }
        return ErrorCode.OK;
    }

    /**
     * 侦察相关检查
     *
     * @return
     */
    protected ErrorCode checkSpy(StructMsg.SpyInfo spyInfo) {
        if (spyInfo.getTargetId() <= 0) {
            throw new GeminiException(ErrorCode.PLANE_CANT_SPY, "checkSpy error param");
        }
        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(spyInfo.getTargetId());
        if (sceneObjEntity == null) {
            throw new GeminiException(ErrorCode.PLANE_CANT_SPY, "cant find target " + spyInfo.getTargetId());
        }
        if (!sceneObjEntity.canBeSpy(getOwner().getPlayerId())) {
            throw new GeminiException(ErrorCode.PLANE_CANT_SPY.getCodeId());
        }
        return ErrorCode.OK;
    }

    public void refreshSpyPlaneOwnerName(String name) {
        for (long planeId : planeList) {
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(planeId);
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_SpyPlane) {
                SpyPlaneEntity spyPlaneEntity = (SpyPlaneEntity) entity;
                spyPlaneEntity.setOwnerName(name);
            }
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_LogisticsPlane) {
                LogisticsPlaneEntity logisticsPlane = (LogisticsPlaneEntity) entity;
                logisticsPlane.setOwnerName(name);
            }
        }

    }

    public void onPlayerClanChange(long clanId, String name) {
        for (long planeId : planeList) {
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(planeId);
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_SpyPlane) {
                SpyPlaneEntity spyPlaneEntity = (SpyPlaneEntity) entity;
                long oldClanId = spyPlaneEntity.getClanId();
                spyPlaneEntity.setClanName(name);
                spyPlaneEntity.setClanId(clanId);
                if (oldClanId != clanId) {
                    spyPlaneEntity.getEventDispatcher().dispatch(new ClanChangeEvent(getEntityId(), oldClanId, clanId));
                }
            }
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_LogisticsPlane) {
                LogisticsPlaneEntity logisticsPlane = (LogisticsPlaneEntity) entity;
                long oldClanId = logisticsPlane.getClanId();
                logisticsPlane.setClanName(name);
                logisticsPlane.setClanId(clanId);
                if (oldClanId != clanId) {
                    logisticsPlane.getEventDispatcher().dispatch(new ClanChangeEvent(getEntityId(), oldClanId, clanId));
                }
            }
        }
    }

    public void returnAllPlane(String reason) {
        List<Long> tempList = new ArrayList<>(planeList);
        for (long planeId : tempList) {
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(planeId);
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_SpyPlane) {
                SpyPlaneEntity spyPlaneEntity = (SpyPlaneEntity) entity;
                spyPlaneEntity.getSpyPlaneBehaviourComponent().onReturnCityEnd(reason);
            }
            if (entity.getEntityType() == ET_LogisticsPlane) {
                LogisticsPlaneEntity logisticsPlane = (LogisticsPlaneEntity) entity;
                logisticsPlane.getBehaviourComponent().onStopServer();
            }
        }
    }

    public void onCityMove() {
        List<Long> tempList = new ArrayList<>(planeList);
        for (long planeId : tempList) {
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(planeId);
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_SpyPlane) {
                SpyPlaneEntity spyPlaneEntity = (SpyPlaneEntity) entity;
                if (spyPlaneEntity.getProp().getState() != CommonEnum.SpyPlaneState.SPS_RETURN) {
                    continue;
                }
                spyPlaneEntity.getSpyPlaneBehaviourComponent().onCityMove();
            }
            if (entity.getEntityType() == ET_LogisticsPlane) {
                LogisticsPlaneEntity logisticsPlane = (LogisticsPlaneEntity) entity;
                logisticsPlane.getBehaviourComponent().checkOnCityMove();
            }
        }
    }

    /**
     * 获取侦察机数量
     */
    public int getSpyPlaneEntityNum() {
        int result = 0;
        for (long planeId : planeList) {
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(planeId);
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_SpyPlane) {
                result++;
            }
        }
        return result;
    }

    /**
     * 获取运输机数量
     */
    public int getLogisticPlaneEntityNum() {
        int result = 0;
        for (long planeId : planeList) {
            SceneObjEntity entity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(planeId);
            if (entity.getEntityType() == EntityAttrOuterClass.EntityType.ET_LogisticsPlane) {
                result++;
            }
        }
        return result;
    }

    /**
     * 同步侦察机数据
     */
    public void syncSpyPlane(SsScenePlayer.SyncSpyPlane spyPlane) {
        if (!planeList.contains(spyPlane.getId())) {
            LOGGER.warn("sync spy plane fail. plane not used. prop={} syncSpyData={}", planeList, spyPlane);
            return;
        }
        SpyPlaneEntity spyPlaneEntity = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(SpyPlaneEntity.class, spyPlane.getId());
        if (spyPlaneEntity == null) {
            LOGGER.error("sync spy plane fail. plane obj is null. prop={} syncSpyData={}", planeList, spyPlane);
            return;
        }
        spyPlaneEntity.setTemplateId(spyPlane.getPlaneModel());
    }

    /**
     * 物流运输机行为检测
     *
     * @param scene
     * @param playerEntity
     * @param info
     */
    public void checkLogisticAction(SceneEntity scene, AbstractScenePlayerEntity playerEntity, StructMsg.LogisticsInfo info) {
        switch (info.getActionType()) {
            case LAT_RESOURCE_ASSIST: {
                // 目标对象不存在
                AbstractScenePlayerEntity targetPlayer = scene.getPlayerMgrComponent().getScenePlayer(info.getTargetId());
                if (targetPlayer == null) {
                    throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY);
                }
                // 援助对象非同盟
                if (!playerEntity.isInClan()) {
                    throw new GeminiException(ErrorCode.NOT_SAME_CLAN);
                }
                if (targetPlayer.getClanId() != playerEntity.getClanId()) {
                    throw new GeminiException(ErrorCode.NOT_SAME_CLAN);
                }
                break;
            }
            case LAT_RETURN: {
                // 操作对象不存在
                LogisticsPlaneEntity planeEntity = scene.getObjMgrComponent().getSceneObjWithType(LogisticsPlaneEntity.class, info.getEntityId());
                if (planeEntity == null) {
                    throw new GeminiException(ErrorCode.SYSTEM_NO_ENTITY);
                }
                if ((planeEntity.getProp().getState() == CommonEnum.LogisticsPlaneState.LPS_RETURN) || (planeEntity.getProp().getState() == CommonEnum.LogisticsPlaneState.LPS_LANDING)) {
                    throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "already return");
                }
                break;
            }
            default:
                throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "checkAction unsupport action type: " + info.getActionType());
        }
    }
}

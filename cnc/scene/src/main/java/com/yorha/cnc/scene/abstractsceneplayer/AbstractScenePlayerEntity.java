package com.yorha.cnc.scene.abstractsceneplayer;

import com.google.common.collect.Sets;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.MapEntry;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.abstractsceneplayer.component.*;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.tick.SceneSchedule;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.entity.tick.SceneTimerReason;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.camp.CampRelationProvider;
import com.yorha.cnc.scene.sceneclan.SceneClanEntity;
import com.yorha.cnc.scene.sceneplayer.component.ScenePlayerPositionMarkComponent;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.concurrent.IGeminiDispatcher;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.time.schedule.ScheduleMgr;
import com.yorha.game.gen.prop.Int32PlayerPickLimitMapProp;
import com.yorha.game.gen.prop.PlayerCardHeadProp;
import com.yorha.game.gen.prop.ScenePlayerMonsterModelProp;
import com.yorha.game.gen.prop.WarningItemListProp;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import com.yorha.proto.Struct;
import io.netty.channel.Channel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
public abstract class AbstractScenePlayerEntity extends AbstractEntity implements CampRelationProvider, SceneSchedule {
    private static final Logger LOGGER = LogManager.getLogger(AbstractScenePlayerEntity.class);
    private final SceneEntity curSceneEntity;

    private final AbstractScenePlayerArmyMgrComponent armyMgrComponent;
    private final AbstractScenePlayerPickUpComponent pickUpComponent;
    private final AbstractScenePlayerAdditionComponent additionComponent;
    private final AbstractScenePlayerDevBuffComponent devBuffComponent;
    private final AbstractScenePlayerRallyComponent rallyComponent;
    private final AbstractScenePlayerSoldierMgrComponent soldierMgrComponent;
    private final AbstractScenePlayerCityMgrComponent cityComponent;
    private final AbstractScenePlayerAoiObserverComponent aoiObserverComponent;
    private final AbstractScenePlayerPlaneComponent planeMgrComponent;
    private final AbstractScenePlayerWarningComponent warningComponent;
    private final AbstractScenePlayerHospitalComponent hospitalComponent;
    private final AbstractScenePlayerBattleComponent battleComponent;
    private final AbstractScenePlayerTimerComponent timerComponent;
    private final AbstractScenePlayerWallComponent wallComponent;
    private IActorRef sessionRef;

    /**
     * 理论上属于”公共“部分的对象其实都不需要计入ScenePlayer占用内存
     */
    public static final Set<Class<?>> JOL_EXCLUDE = Sets.newHashSet(ScheduleMgr.class, Class.class, ThreadPoolExecutor.class,
            IGeminiDispatcher.class, Thread.class,
            // channel会引用到netty下层的一堆东西
            Channel.class,
            // 这个是Protobuf底层对结构体metadata的描述内容，有点大
            MapEntry.class,
            ActorSystem.class,
            ActorRunnable.class,
            ActorMsgEnvelope.class,
            SceneEntity.class,
            PropertyChangeListener.class,
            SceneObjEntity.class,
            AoiGrid.class,
            Runnable.class);

    public AbstractScenePlayerEntity(SceneEntity sceneEntity, long playerId, AbstractScenePlayerBuilder builder) {
        super(playerId);
        this.curSceneEntity = sceneEntity;
        armyMgrComponent = builder.armyMgrComponent(this);
        pickUpComponent = builder.pickUpComponent(this);
        additionComponent = builder.additionComponent(this);
        devBuffComponent = builder.devBuffComponent(this);
        rallyComponent = builder.rallyComponent(this);
        soldierMgrComponent = builder.soldierMgrComponent(this);
        cityComponent = builder.cityComponent(this);
        aoiObserverComponent = builder.aoiObserverComponent(this);
        planeMgrComponent = builder.planeMgrComponent(this);
        warningComponent = builder.warningComponent(this);
        hospitalComponent = builder.hospitalComponent(this);
        battleComponent = builder.battleComponent(this);
        timerComponent = builder.timerComponent(this);
        wallComponent = builder.wallComponent(this);
    }

    public abstract void onLogin(IActorRef sessionRef);

    public void onLoginWithDungeon(IActorRef sessionRef, CommonEnum.DungeonType type) {

    }

    public void onLogout() {
    }

    public void enterDungeon(CommonEnum.DungeonType type) {
    }

    public void leaveDungeon() {
    }

    public boolean isThisSceneOnline() {
        return isOnline();
    }

    @Override
    public EntityType getEntityType() {
        return EntityType.ET_Player;
    }

    @Override
    public SceneActor ownerActor() {
        return curSceneEntity.ownerActor();
    }

    public Long nextId() {
        return ownerActor().nextId();
    }

    public boolean sendMsgToClient(int msgType, GeneratedMessageV3 msg) {
        if (!this.isOnline()) {
            return true;
        }
        SessionHelper.sendMsgToSession(sessionRef, ownerActor(), msgType, msg);
        return true;
    }

    public void sendErrorCode(int codeId) {
        sendMsgToClient(MsgType.PLAYER_PLAYDIALOG_NTF, MsgHelper.buildErrorMsg(codeId));
    }

    public boolean sendMsgToClientIgnoreInDungeon(int msgType, GeneratedMessageV3 msg) {
        if (sessionRef == null) {
            return true;
        }
        SessionHelper.sendMsgToSession(sessionRef, ownerActor(), msgType, msg);
        return true;
    }

    public void tellPlayer(GeneratedMessageV3 msg) {
        ownerActor().tellPlayer(getZoneId(), getEntityId(), msg);
    }

    public void bindSession(IActorRef sessionRef) {
        this.sessionRef = sessionRef;
    }

    public boolean isOnline() {
        return this.sessionRef != null;
    }

    public boolean checkMonsterKillLevel(int monsterLevel) {
        return true;
    }

    /**
     * 用于scene上的actor消息广播优化
     * 禁止用于其他使用
     */
    public IActorRef getSessionRef() {
        return sessionRef;
    }


    public SceneEntity getScene() {
        return curSceneEntity;
    }

    /**
     * @return 获取主堡
     */
    public CityEntity getMainCity() {
        return getCityComponent().getMyMainCity();
    }

    public void setMainCityId(long cityId) {
    }

    @Override
    public long getPlayerId() {
        return getEntityId();
    }

    @Override
    public long getClanId() {
        return 0;
    }

    public PlayerCardHeadProp getCardHead() {
        return new PlayerCardHeadProp();
    }

    public Struct.PlayerCardHead getCardHeadSS() {
        return getCardHead().getCopySsBuilder().build();
    }

    public Int32PlayerPickLimitMapProp getPickUpProp() {
        return null;
    }

    public WarningItemListProp getWarningProp() {
        return null;
    }

    public boolean isInClan() {
        return getClanId() != 0;
    }

    /**
     * 获取场景上的联盟对象
     */
    public SceneClanEntity getSceneClan() {
        return null;
    }

    public int getLevel() {
        if (getMainCity() == null) {
            return 0;
        }
        return getMainCity().getLevel();
    }

    public AbstractScenePlayerAoiObserverComponent getAoiObserverComponent() {
        return aoiObserverComponent;
    }

    public AbstractScenePlayerArmyMgrComponent getArmyMgrComponent() {
        return armyMgrComponent;
    }

    public AbstractScenePlayerSoldierMgrComponent getSoldierMgrComponent() {
        return soldierMgrComponent;
    }

    public AbstractScenePlayerRallyComponent getRallyComponent() {
        return rallyComponent;
    }

    public AbstractScenePlayerPlaneComponent getPlaneComponent() {
        return planeMgrComponent;
    }

    public AbstractScenePlayerPickUpComponent getPickUpComponent() {
        return pickUpComponent;
    }

    public AbstractScenePlayerAdditionComponent getAdditionComponent() {
        return additionComponent;
    }

    public AbstractScenePlayerDevBuffComponent getDevBuffComponent() {
        return devBuffComponent;
    }

    public AbstractScenePlayerWarningComponent getWarningComponent() {
        return warningComponent;
    }

    public AbstractScenePlayerCityMgrComponent getCityComponent() {
        return cityComponent;
    }

    public AbstractScenePlayerHospitalComponent getHospitalComponent() {
        return hospitalComponent;
    }

    public AbstractScenePlayerBattleComponent getBattleComponent() {
        return battleComponent;
    }

    public AbstractScenePlayerTimerComponent getTimerComponent() {
        return timerComponent;
    }

    public abstract ScenePlayerPositionMarkComponent getPositionMarkComponent();
    // ------------------------- 容器重写 -------------------------

    @Override
    public int hashCode() {
        return Objects.hash(getEntityId());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        AbstractScenePlayerEntity entity = (AbstractScenePlayerEntity) obj;
        return getEntityId() == entity.getEntityId();
    }

    public String getName() {
        return getCardHead().getName();
    }

    public String getClanName() {
        return "";
    }

    public int getCityBuildLevel(CommonEnum.InnerCityBuildType buildType) {
        return 0;
    }

    public List<Integer> getUnlockSpyData() {
        return Collections.emptyList();
    }

    public boolean checkResourceUnlock(int type) {
        return true;
    }

    /**
     * 联盟的简称变了
     */
    public void onSyncClanSimpleName(String name) {
        long clanId = getClanId();
        if (getMainCity() != null) {
            getMainCity().onClanChange(clanId, name);
        }
        getArmyMgrComponent().onSyncPlayerClan(clanId, name);
        getPlaneComponent().onPlayerClanChange(clanId, name);
        LOGGER.info("{} onSyncClanSimpleName {}", this, name);
    }

    public void callAfterAllLoad() {
    }

    @Override
    public boolean onTimerDispatch(SceneTimerReason reason) {
        if (reason == SceneTimerReason.TIMER_DEV_BUFF) {
            getDevBuffComponent().onPendingDevBuffExpired(getScene().now());
            return true;
        }
        return false;
    }

    @Override
    public boolean onTickDispatch(SceneTickReason reason) {
        return false;
    }

    @Override
    public int getZoneId() {
        return ownerActor().getZoneId();
    }

    public IActorRef genPlayerRef() {
        return RefFactory.ofPlayer(getZoneId(), getPlayerId());
    }


    @Override
    protected void onPostInitFailed() {
        getTimerComponent().onDestroy();
    }

    @Nullable
    public AbstractScenePlayerWallComponent getWallComponent() {
        return wallComponent;
    }

    public ScenePlayerMonsterModelProp getMonsterProp() {
        return null;
    }
}

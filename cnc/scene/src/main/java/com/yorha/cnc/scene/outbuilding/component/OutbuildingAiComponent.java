package com.yorha.cnc.scene.outbuilding.component;

import com.yorha.cnc.scene.outbuilding.OutbuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;

/**
 * 超武附属建筑ai组件
 *
 * <AUTHOR>
 */
public class OutbuildingAiComponent extends SceneObjAiComponent {
    public OutbuildingAiComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public int getAiIndex() {
        return getOwner().getBuildingTemplate().getAiIndex();
    }

    @Override
    public OutbuildingEntity getOwner() {
        return (OutbuildingEntity) super.getOwner();
    }
}

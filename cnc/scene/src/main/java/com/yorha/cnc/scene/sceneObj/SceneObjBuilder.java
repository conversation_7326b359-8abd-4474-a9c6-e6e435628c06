package com.yorha.cnc.scene.sceneObj;

import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.entity.component.GridAoiMgrComponent;
import com.yorha.cnc.scene.sceneObj.component.*;
import com.yorha.cnc.scene.sceneObj.component.aoi.SceneObjAoiComponent;
import com.yorha.cnc.scene.sceneObj.component.aoi.SceneObjAoiNodeComponent;
import com.yorha.cnc.scene.sceneObj.component.aoi.SceneObjNormalAoiComponent;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.gemini.props.AbstractPropNode;

/**
 * <AUTHOR>
 */
public abstract class SceneObjBuilder<T extends SceneObjEntity, F extends AbstractPropNode> {

    protected final SceneEntity sceneEntity;
    private final long eid;
    private final F prop;


    public SceneObjBuilder(SceneEntity sceneEntity, long eid, F prop) {
        this.sceneEntity = sceneEntity;
        this.eid = eid;
        this.prop = prop;
    }

    public SceneEntity getSceneEntity() {
        return sceneEntity;
    }

    public F getProp() {
        return this.prop;
    }

    public long getEntityId() {
        return eid;
    }

    /**
     * 获取属性
     *
     * @return 位置属性
     */
    public abstract PointProp getPointProp();

    public SceneObjPropComponent propComponent(T owner) {
        return new SceneObjPropComponent(owner);
    }

    public SceneObjDbComponent dbComponent(T owner) {
        return new SceneObjDbComponent(owner);
    }

    public SceneObjTransformComponent transformComponent(T owner) {
        return new SceneObjTransformComponent(owner, getPointProp());
    }

    public SceneObjAoiComponent aoiNodeComponent(T owner) {
        // 看scene 是什么类型 是scene是格子的 那obj就用格子的
        if (sceneEntity.getAoiMgrComponent() instanceof GridAoiMgrComponent) {
            return new SceneObjAoiNodeComponent(owner);
        }
        return new SceneObjNormalAoiComponent(owner);
    }

    public SceneObjArrowComponent arrowComponent(T owner) {
        return new SceneObjArrowComponent(owner);
    }

    public SceneObjSpyComponent spyComponent(T owner) {
        return new SceneObjSpyComponent(owner);
    }

    public SceneObjTimerComponent timerComponent(T owner) {
        return new SceneObjTimerComponent(owner);
    }
}
package com.yorha.cnc.scene.gm.command.mapbuilding;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum.DebugGroup;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class SetMapBuildingClose implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int partId = Integer.parseInt(args.get("partId"));
        MapBuildingEntity mapBuilding = actor.getScene().getBuildingMgrComponent().getMapBuilding(partId);
        if (mapBuilding == null) {
            throw new GeminiException(ErrorCode.MAP_BUILDING_NOT_EXISTS);
        }
        mapBuilding.getStageMgrComponent().gmSetClose();
    }

    @Override
    public String showHelp() {
        return "SetMapBuildingClose partId={value}";
    }

    @Override
    public DebugGroup getGroup() {
        return DebugGroup.DG_MAPBUILDING;
    }
}

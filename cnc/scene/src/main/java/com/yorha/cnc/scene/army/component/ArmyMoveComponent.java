package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.event.DieEvent;
import com.yorha.cnc.scene.event.MoveEndEvent;
import com.yorha.cnc.scene.event.StartMoveEvent;
import com.yorha.cnc.scene.event.army.ArmyReturnCityEvent;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjMoveComponent;
import com.yorha.cnc.scene.sceneObj.move.IMoveArriveHandler;
import com.yorha.cnc.scene.sceneObj.move.IMoveTargetLoseHandler;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.MoveProp;
import com.yorha.game.gen.prop.PointProp;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ArmyState;
import com.yorha.proto.CommonEnum.SoldierType;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstClanTerritoryTemplate;
import res.template.ConstTemplate;
import res.template.SoldierTypeTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class ArmyMoveComponent extends SceneObjMoveComponent {
    private static final Logger LOGGER = LogManager.getLogger(ArmyMoveComponent.class);
    /**
     * 领土内的移动速度
     */
    protected int moveSpeedInClanTerritory = 0;
    /**
     * 是否可以强制溃败
     */
    private boolean canForceDefeat;

    public ArmyMoveComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public ArmyEntity getOwner() {
        return (ArmyEntity) super.getOwner();
    }

    @Override
    public int getRealMoveSpeed(int searchTag, Point endPoint) {
        if (GameLogicConstants.isBesiegeMove(searchTag)) {
            return ResHolder.getConsts(ConstTemplate.class).getSiegeMoveSpeed();
        }
        ArmyState armyState = getOwner().getProp().getArmyState();
        // 溃败状态用溃败速度
        if (armyState == ArmyState.AS_Retreating) {
            return SceneAddCalc.getRetreatSpeed(this.getOwner(), ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getArmyRetreatSpeed()) * getOwner().getScenePlayer().getArmyMgrComponent().getMoveSpeedRatio();
        }
        // 运输状态用运输机速度
        if (getOwner().isInTransport()) {
            int speed = getOwner().getProp().getTransportPlane().getPlaneSpeed();
            int transportPlaneSpeed = SceneAddCalc.getTransportPlaneSpeed(getOwner(), speed);
            if (debugMoveSpeedRatio > 0) {
                return transportPlaneSpeed * debugMoveSpeedRatio;
            }
            return transportPlaneSpeed;
        }
        // 目标是自己联盟的领土 就用另一个
        if (getOwner().getClanId() != 0 && getOwner().getScene().isMainScene()) {
            long ownerIdByPartId = getOwner().getScene().getBuildingMgrComponent().getOwnerIdByPartId(endPoint);
            if (getOwner().getClanId() == ownerIdByPartId) {
                return moveSpeedInClanTerritory * getOwner().getScenePlayer().getArmyMgrComponent().getMoveSpeedRatio();
            }
        }
        return super.getRealMoveSpeed(searchTag, endPoint) * getOwner().getScenePlayer().getArmyMgrComponent().getMoveSpeedRatio();
    }

    @Override
    protected void initMoveSpeedFromTroopProp() {
        int curMinSpeed = -1, curMinSpeedClanTerritory = -1;
        // 每个兵种最小移动基础速度
        Map<SoldierType, Integer> typeSpeed = new HashMap<>();
        for (SoldierProp soldierPropData : getTroopProp().getTroop().values()) {
            SoldierTypeTemplate template = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldierPropData.getSoldierId());
            SoldierType soldierType = SoldierType.forNumber(template.getSoldierType());
            Integer curSpeed = typeSpeed.getOrDefault(soldierType, template.getMoveSpeed());
            curSpeed = Math.min(curSpeed, template.getMoveSpeed());
            typeSpeed.put(soldierType, curSpeed);
        }

        BattleRole battleRole = getOwner().getBattleComponent().getBattleRole();
        long allSpeed = SceneAddCalc.getSoldierAllTypeSpeed(battleRole);
        for (Map.Entry<SoldierType, Integer> entry : typeSpeed.entrySet()) {
            int newSpeed = (int) SceneAddCalc.getSoldierTypeSpeed(battleRole, entry.getKey(), entry.getValue(), allSpeed, false);
            if (curMinSpeed == -1 || newSpeed < curMinSpeed) {
                curMinSpeed = newSpeed;
            }

            int newSpeed2 = (int) SceneAddCalc.getSoldierTypeSpeed(battleRole, entry.getKey(), entry.getValue(), allSpeed, true);
            if (curMinSpeedClanTerritory == -1 || curMinSpeedClanTerritory < newSpeed2) {
                curMinSpeedClanTerritory = newSpeed2;
            }
        }
        moveSpeedInClanTerritory = curMinSpeedClanTerritory;
        moveSpeed = curMinSpeed;
    }

    @Override
    protected TroopProp getTroopProp() {
        return getOwner().getProp().getTroop();
    }

    @Override
    protected MoveProp getMoveProp() {
        return getOwner().getProp().getMove();
    }

    @Override
    protected void onMoveDataFind(MoveData moveData, long startTime, boolean isAsync) {
        super.onMoveDataFind(moveData, startTime, isAsync);
        getOwner().getStatusComponent().onMoveStart();
    }

    @Override
    protected void onStartMove() {
        super.onStartMove();
        getOwner().getEventDispatcher().dispatch(new StartMoveEvent());
        LOGGER.debug("{} begin move", getOwner());
    }

    @Override
    protected void onRefreshMove() {
        super.onRefreshMove();
        long moveArriveTime = getMoveArriveTime();
        SceneObjEntity moveTarget = getMoveTarget();
        Point point;
        if (moveTarget == null) {
            PointProp pointProp = getMoveProp().getPointList().get(getMoveProp().getPointList().size() - 1);
            point = Point.valueOf(pointProp.getX(), pointProp.getY());
        } else {
            point = moveTarget.getCurPoint();
        }
        getOwner().getRallyComponent().onRefreshRallyMove(moveArriveTime, point);
        if (!getOwner().isRallyArmy()) {
            getOwner().getStatusComponent().setDetailState(moveArriveTime, point);
        }
        try {
            checkAndRefreshBattleMeObj();
        } catch (Exception e) {
            LOGGER.error("{} checkAndRefreshBattleMeObj failed ", getOwner(), e);
        }
    }

    @Override
    public void onEndMove() {
        super.onEndMove();
        LOGGER.debug("{} end move", getOwner());
        getOwner().getBehaviourComponent().refreshArmyState();
        getOwner().getEventDispatcher().dispatch(new MoveEndEvent(getEntityId()));
        // 保底措施 如果别的原因导致中断了  还得处理下
        if (getOwner().getStatusComponent().isInPassing()) {
            getOwner().getStatusComponent().setIsInPassing(false, 0);
            LOGGER.warn("{} end move but inPassing", getOwner());
        }
    }

    public void moveToPointAsync(Point point, IMoveArriveHandler arriveHandler) {
        moveToPointAsync(point, arriveHandler, getOwner().getScene().now(),
                codeId -> {
                    if (ErrorCode.isOK(codeId)) {
                        return;
                    }
                    getOwner().getScenePlayer().sendErrorCode(codeId);
                });
    }

    @Override
    protected boolean moveToTarget(SceneObjEntity targetEntity, TroopInteractionType interactionType, IMoveArriveHandler arriveHandler, IMoveTargetLoseHandler loseHandler, boolean isAsync, Consumer<Integer> backHandler) {
        boolean isArrived = super.moveToTarget(targetEntity, interactionType, arriveHandler, loseHandler, isAsync, backHandler);
        // 直接到达的 不加小箭头
        if (isArrived) {
            return true;
        }
        // 是回自己的主城 不加小箭头
        if (targetEntity == getOwner().getScenePlayer().getMainCity()) {
            return false;
        }
        // 设置小箭头
        targetEntity.getArrowComponent().addArrowItem(getOwner());
        return false;
    }

    @Override
    protected boolean checkCrossing() {
        boolean cantPass;
        int curCrossingPart = getCurCrossingPart();
        // 已经在关卡里了 不用判定
        if (getOwner().getStatusComponent().isInPassing()) {
            cantPass = false;
        } else {
            cantPass = super.checkCrossing();
        }
        // 关卡不能通行了 处理下
        if (cantPass) {
            if (getOwner().isRallyArmy()) {
                getOwner().getRallyEntity().dismiss(RallyDismissReason.RDR_NO_PATH);
            } else {
                //要回城 回不去就停下
                returnMainCity();
            }
            return true;
        }
        if (curCrossingPart != 0) {
            getOwner().getStatusComponent().setIsInPassing(true, curCrossingPart);
        } else if (getOwner().getStatusComponent().isInPassing()) {
            getOwner().getStatusComponent().setIsInPassing(false, 0);
        }
        return false;
    }

    /**
     * tick中检查发现原有路径不行了
     * 如果是关卡丢失导致的  发送移动中途关卡失去通知
     */
    @Override
    protected void onPathLoseWhenMoveTick(int partId, ErrorCode code) {
        getOwner().getStatusComponent().setIsInPassing(false, 0);
        super.onPathLoseWhenMoveTick(partId, code);
        if (partId == 0) {
            return;
        }
        // 发提示
        getOwner().getScenePlayer().sendMsgToClient(MsgType.PLAYER_PLAYDIALOG_NTF, MsgHelper.buildErrorMsg(code.getCodeId()));
        // 发邮件
        if (code.getCodeId() != ErrorCode.MOVE_CROSS_LOSE.getCodeId()) {
            return;
        }
        int mailId = ResHolder.getConsts(ConstClanTerritoryTemplate.class).getPassBuildingImpassable();
        if (mailId == 0) {
            return;
        }
        MapBuildingEntity mapBuilding = getOwner().getScene().getBuildingMgrComponent().getMapBuilding(partId);
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailId);
        // 正文参数设置
        mailSendParams.getContentBuilder().setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA);
        Struct.DisplayData.Builder contentData = mailSendParams.getContentBuilder().getDisplayDataBuilder();
        contentData.getParamsBuilder()
                .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, mapBuilding.getProp().getTemplateId()))
                .addDatas(MsgHelper.buildGotoDisplayPoint(getOwner().getCurPoint(), this.getOwner().getScene().getMapType(), this.getOwner().getScene().getMapIdForPoint()));
        // TODO (kvk接入)
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(getOwner().getPlayerId())
                        .setZoneId(getOwner().getScene().getZoneId())
                        .build(),
                mailSendParams
                        .build());
    }

    @Override
    protected int getMoveSearchPathTag(boolean isToTarget) {
        // 压测无视关卡归属
        if (ServerContext.getServerDebugOption().isIgnoreCrossingOwner()) {
            return GameLogicConstants.DIED_ARMY_MOVE;
        }
        if (getOwner().getProp().getArmyState() == ArmyState.AS_Retreating) {
            return GameLogicConstants.DIED_ARMY_MOVE;
        }
        if (getOwner().getProp().getArmyState() == ArmyState.AS_TransportReturn) {
            // 遣返的时候无视关卡占有
            return GameLogicConstants.TRANSPORT_MOVE_RETURN;
        }
        if (GameLogicConstants.isTransportState(getOwner().getProp().getArmyState())) {
            return GameLogicConstants.TRANSPORT_MOVE;
        }
        return super.getMoveSearchPathTag(isToTarget);
    }

    /**
     * 败退回城
     * 极端情况可能会被调用多次，需要做好可重入保证
     * 禁止玩家所有操作请求
     */
    public void retreat() {
        getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
        getOwner().getProp().setArmyState(ArmyState.AS_Retreating);
        getOwner().getBehaviourComponent().setRecvPlayerAction(false);
        getOwner().getEventDispatcher().dispatch(new DieEvent(getEntityId(), 0));
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            onReturnCityEnd();
        } else {
            returnMainCity();
        }
    }

    /**
     * 强制溃败
     */
    public void forceDefeat() {
        if (getOwner().getBattleComponent().isInBattle()) {
            throw new GeminiException(ErrorCode.FORCED_DEFEAT_ARMY_FAILED);
        }
        if (!canForceDefeat) {
            throw new GeminiException(ErrorCode.FORCED_DEFEAT_ARMY_FAILED);
        }
        // 强制溃败是把存活兵力改为0
        for (SoldierProp prop : getOwner().getProp().getTroop().getTroop().values()) {
            int alive = prop.getNum() - prop.getDeadNum() - prop.getSevereWoundNum() - prop.getSlightWoundNum();
            prop.setSlightWoundNum(prop.getSlightWoundNum() + alive);
        }
        retreat();
        LOGGER.info("{} forceDefeat  player: {}", getOwner(), getOwner().getPlayerId());
    }

    public void setCanForceDefeat(boolean canForceDefeat) {
        this.canForceDefeat = canForceDefeat;
    }

    /**
     * 手动操作回城 会抛异常的
     */
    public void handReturnMainCity() {
        try {
            CityEntity mainCity = getOwner().getScenePlayer().getMainCity();
            moveToTarget(mainCity, TroopInteractionType.RETURN_CITY, this::onReturnCityArrive, null);
        } catch (GeminiException e) {
            // 设置可强制溃败标志位
            checkForceDefeatError(e.getCodeId());
            getOwner().getMoveComponent().stopMove();
            getOwner().getStatusComponent().setStaying();
            throw e;
        }
    }

    private void checkForceDefeatError(int codeId) {
        if (codeId == ErrorCode.MOVE_NO_CROSS.getCodeId()) {
            canForceDefeat = true;
        }
    }

    /**
     * 回主城  各种被动的
     */
    public void returnMainCity() {
        getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
        AbstractScenePlayerEntity scenePlayer = getOwner().getScenePlayer();
        try {
            // 从db恢复的时候就要回城  城池还没恢复   副本中也会走这个
            if (scenePlayer.getMainCity() == null) {
                onReturnCityArrive();
                return;
            }
            CityEntity mainCity = getOwner().getScenePlayer().getMainCity();
            moveToTarget(mainCity, TroopInteractionType.RETURN_CITY, this::onReturnCityArrive, null);
            getOwner().getBehaviourComponent().setCurBehaviourTargetId(mainCity.getEntityId());
        } catch (Exception e) {
            stopMove();
            getOwner().getStatusComponent().setStaying();
            if (GeminiException.isLogicException(e)) {
                LOGGER.info("{} returnMainCity failed ", getOwner(), e);
            } else {
                LOGGER.error("{} returnMainCity failed ", getOwner(), e);
            }
            if (getOwner().getProp().getArmyState() == ArmyState.AS_Retreating) {
                // 如果溃败都回不去 就直接回城吧
                onReturnCityArrive();
                LOGGER.info("{} retreating but cant find path arrive now", getOwner());
                return;
            }
            // 在初始化过程中要回去的 而且是没路
            if (!getOwner().getScene().isInitOk() && e instanceof GeminiException && ((GeminiException) e).getCodeId() == ErrorCode.MOVE_NO_PATH.getCodeId()) {
                onReturnCityArrive();
                LOGGER.info("{} want return but cant find path arrive now", getOwner());
            }
        }
    }

    /**
     * 回城移动到达  包括走回去和遣返
     */
    private void onReturnCityArrive() {
        // 空投遣返还有一个降落过程
        if (getOwner().getProp().getArmyState() == ArmyState.AS_TransportReturn) {
            getOwner().getTransportComponent().onTransportArrive(true);
            return;
        }
        returnCityEnd();
    }

    /**
     * 回到主城 归还兵力 删除行军
     */
    public void onReturnCityEnd() {
        stopMove();
        returnCityEnd();
    }

    private void returnCityEnd() {
        final AbstractScenePlayerEntity scenePlayer = getOwner().getScenePlayer();
        if (getOwner().isDestroy()) {
            LOGGER.error("returnCityEnd but is destroy {} {}", scenePlayer.getPlayerId(), getOwner());
            return;
        }
        LOGGER.info("returnCityEnd player {} {} arrive city", scenePlayer.getPlayerId(), getOwner());
        this.getOwner().getEventDispatcher().dispatch(new ArmyReturnCityEvent(this.getEntityId()));
        getOwner().deleteObj();
    }
}
package com.yorha.cnc.scene.army.component;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.game.gen.prop.HuntingModelProp;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 打野相关的数据处理（目前只有体力啦）
 */
public class ArmyHuntingComponent extends SceneObjComponent<ArmyEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ArmyHuntingComponent.class);

    public ArmyHuntingComponent(ArmyEntity owner) {
        super(owner);
    }

    public HuntingModelProp getProp() {
        return getOwner().getStatusProp().getHuntingModel();
    }

    public void focusOnMonster(long monsterId, int costEnergy) {
        HuntingModelProp prop = getProp();
        if (monsterId != prop.getCurrentFocusMonsterId()) {
            tryRollBackEnergy();

            LOGGER.debug("{} focus on monster {} {}", getOwner(), monsterId, costEnergy);
            prop.setCurrentFocusMonsterId(monsterId);
            prop.setCurrentTargetCostEnergy(costEnergy);
            prop.setAlreadyBattled(false);
            // 已经开战
            if (getOwner().getBattleComponent().hasRelationWithRoleId(monsterId)) {
                prop.setAlreadyBattled(true);
            }
        }
    }

    public void tryRollBackEnergy() {
        final HuntingModelProp prop = getProp();
        final long currentFocusMonsterId = prop.getCurrentFocusMonsterId();
        final int currentTargetCostEnergy = prop.getCurrentTargetCostEnergy();
        final boolean alreadyBattled = prop.getAlreadyBattled();

        // 避免多次返还，这里会置为0
        prop.setCurrentFocusMonsterId(0);
        prop.setCurrentTargetCostEnergy(0);
        prop.setAlreadyBattled(false);

        if (currentFocusMonsterId > 0 && currentTargetCostEnergy > 0 && !alreadyBattled) {
            LOGGER.info("{} will roll back energy {} {}", getOwner(), currentFocusMonsterId, currentTargetCostEnergy);

            SsPlayerMisc.EnergyRollbackCmd energyRollbackCmd = SsPlayerMisc.EnergyRollbackCmd.newBuilder().setEnergy(currentTargetCostEnergy).build();
            getOwner().getScenePlayer().tellPlayer(energyRollbackCmd);
        }
    }

    public void onAddBattleRelation(long enemyRoleId) {
        if (enemyRoleId == getProp().getCurrentFocusMonsterId()) {
            getProp().setAlreadyBattled(true);
        }
    }

    public void onMonsterDead(long monsterId) {
        if (getOwner().isRallyArmy()) {
            return;
        }
        if (monsterId == getProp().getCurrentFocusMonsterId()) {
            tryRollBackEnergy();
        }
    }

    public void incrementKillStreak() {
        HuntingModelProp huntingModel = getOwner().getStatusProp().getHuntingModel();
        huntingModel.setKillStreak(huntingModel.getKillStreak() + 1);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        tryRollBackEnergy();
    }
}

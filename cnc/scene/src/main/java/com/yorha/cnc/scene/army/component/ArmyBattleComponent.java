package com.yorha.cnc.scene.army.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.BattleArmyStateChangeEvent;
import com.yorha.cnc.battle.event.BattleRoundEvent;
import com.yorha.cnc.battle.event.TriggerSkillEvent;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.battle.soldier.SoldierUnit;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.city.CityEntity;
import com.yorha.cnc.scene.entity.tick.SceneTickReason;
import com.yorha.cnc.scene.event.army.ArmyHeroPropChangeEvent;
import com.yorha.cnc.scene.event.army.BattleUnitPropChangeEvent;
import com.yorha.cnc.scene.event.army.PlayerOperationPreEvent;
import com.yorha.cnc.scene.event.battle.ArmyStateChangeEvent;
import com.yorha.cnc.scene.event.battle.BattleRoleSettleRoundEvent;
import com.yorha.cnc.scene.event.battle.EndSingleBattleEvent;
import com.yorha.cnc.scene.event.warn.WarningRemoveEvent;
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.cnc.scene.sceneclan.rally.RallyEntity;
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.enums.qlog.battle.BattleRepDefenceType;
import com.yorha.common.enums.qlog.battle.BattleRepMainObjType;
import com.yorha.common.enums.qlog.battle.BattleRepResult;
import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.QlogUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.EntityAttrOuterClass.EntityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncBattle;
import qlog.flow.QlogCncBattleReport;
import res.template.ConstTemplate;
import res.template.MonsterTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 战斗组件，连接BattleRole和ArmyEntity
 *
 * <AUTHOR> Jiang
 */
public class ArmyBattleComponent extends SceneObjBattleComponent {
    private static final Logger LOGGER = LogManager.getLogger(ArmyBattleComponent.class);
    /**
     * 是否战斗结束后保持停留 否则回城
     */
    private boolean isStayAfterBattle = true;
    /**
     * 是否是攻打建筑战斗
     */
    private long curAttackBuilding = 0;

    public ArmyBattleComponent(ArmyEntity parent) {
        super(parent, judgeSceneObjType(parent), null);
    }

    private static SceneObjType judgeSceneObjType(ArmyEntity army) {
        return army.isRallyArmy() ? SceneObjType.SOT_ARMY_GROUP : SceneObjType.SOT_ARMY;
    }

    @Override
    public ArmyEntity getOwner() {
        return (ArmyEntity) super.getOwner();
    }

    @Override
    public void init() {
        super.init();
        initListener();
        getBattleRole().buildHero();
        if (getOwner().isRallyArmy()) {
            fillSoldierChildForRally();
            getBattleRole().refreshTroop();
        } else {
            addBattleEntityCache(getEntityId(), getOwner().getPlayerId());
            getBattleRole().initSoldierByTroop();
        }
        getBattleRole().initState();
    }

    @Override
    public void postInit() {
        super.postInit();
        getOwner().addTick(SceneTickReason.TICK_BATTLE);
    }

    protected void initListener() {
        getOwner().getEventDispatcher().addEventListenerRepeat(this::checkAttackCityField, PlayerOperationPreEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onSettleRoundEvent, BattleRoleSettleRoundEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onBattleUnitPropChange, BattleUnitPropChangeEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onEndSingleRelation, EndSingleBattleEvent.class);
        getOwner().getEventDispatcher().addEventListenerRepeat(this::onArmyStateChange, ArmyStateChangeEvent.class);
    }

    public void onTick() {
        if (isInBattle()) {
            getBattleRole().addBattleRoundEvent(new TriggerSkillEvent(TriggerType.TT_GLOBAL_ROUND_START));
        } else {
            if (!isRally() && getOwner().isInRally()) {
                return;
            }
            // 大世界回合开始时触发器
            SkillSystem.trigger(getBattleRole(), TriggerType.TT_GLOBAL_ROUND_START);
        }
    }

    @Override
    public TroopProp getTroop() {
        return getOwner().getProp().getTroop();
    }

    @Override
    public BattleProp getBattleProp() {
        return getOwner().getProp().getBattle();
    }

    public void setIsStayAfterBattle(boolean isStayAfterBattle) {
        this.isStayAfterBattle = isStayAfterBattle;
    }

    public void setCurAttackBuilding(long curAttackBuilding) {
        this.curAttackBuilding = curAttackBuilding;
    }

    protected void onSettleRoundEvent(BattleRoleSettleRoundEvent event) {
        if (getOwner().isRallyArmy()) {
            // 集结体重新计算集体troop数据
            getBattleRole().refreshTroop();
        }

        if (!getOwner().isRallyArmy()) {
            return;
        }
        // 同步给集结体，战损同步到面板
        RallyEntity rallyEntity = getOwner().getRallyEntity();
        if (rallyEntity != null) {
            rallyEntity.getArmyMgrComponent().onDamage(event.getDamageResult().getSoldierLossList());
        } else {
            LOGGER.error("getRallyEntity failed. curRallyId: {}  army: {}", getOwner().getRallyComponent().getCurRallyId(), getOwner());
        }
    }

    public void sendSevereWound2Hospital(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {

    }

    /**
     * 检查攻城战场的运行态
     */
    private void checkAttackCityField(PlayerOperationPreEvent event) {
        if (curAttackBuilding == 0) {
            return;
        }
        StructPlayer.ArmyActionInfo armyActionInfo = event.getArmyActionInfo();
        BattleRelation battleRelation =
                getOwner().getScene().getBattleGroundComponent().findBattleRelationOrNull(getEntityId(), curAttackBuilding);
        if (battleRelation == null) {
            return;
        }
        if (battleRelation.getStatus() == BattleConstants.BattleRelationStatus.Running && armyActionInfo.getTargetId() != curAttackBuilding) {
            battleRelation.setStatus(BattleConstants.BattleRelationStatus.Suspend);
        }
    }

    /**
     * 集结体有人加入
     *
     * @param maxNum 集结容量上限
     */
    public void onRallyAddInnerArmy(ArmyEntity childArmyEntity, long maxNum) {
        ArmyProp armyProp = childArmyEntity.getProp();
        for (SoldierProp sp : armyProp.getTroop().getTroop().values()) {
            // 设置集结体兵力槽分母  未达集结兵力上限,就加上去。
            getOwner().getProp().setHpMax(Math.min(maxNum, Soldier.aliveCountOf(sp) + getOwner().getProp().getHpMax()));
            getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(childArmyEntity.getEntityId(), SceneObjType.SOT_ARMY_GROUP), sp);
            LOGGER.debug("join rally add army:{} id: {} num:{}, aliveRate:{}", childArmyEntity.getEntityId(), sp.getSoldierId(), Soldier.aliveCountOf(sp), getBattleRole().aliveSoldierRate());
        }
        getBattleRole().refreshTroop();
        // 有人加入集结，填充战报
        onSingleArmyArriveBattleRole(childArmyEntity);
        // 集结的话 是直接构建的 有人加入直接加就行
        addBattleEntityCache(childArmyEntity.getEntityId(), childArmyEntity.getPlayerId());
    }

    /**
     * 集结体有人离开
     *
     * @param armyId 走掉的armyId
     */
    public void onRallyDelInnerArmy(long armyId, ArmyProp armyProp) {
        for (SoldierProp sp : armyProp.getTroop().getTroop().values()) {
            final int soldierId = sp.getSoldierId();
            getBattleRole().dropSoldierUnit(armyId, soldierId);
            // 没有伤兵  兵力槽会跟着扣  有伤兵就不扣了
            if (sp.getSlightWoundNum() <= 0 && sp.getSevereWoundNum() <= 0 && sp.getDeadNum() <= 0) {
                getOwner().getProp().setHpMax(getOwner().getProp().getHpMax() - sp.getNum());
            }
            LOGGER.debug("leave rally dec army:{} id: {} num:{}, aliveRate:{}", armyId, soldierId, Soldier.aliveCountOf(sp), getBattleRole().aliveSoldierRate());
        }
        getBattleRole().refreshTroop();
    }

    @Override
    public Map<Long, SsPlayerMisc.PlunderWeight> getPlunderWeight() {
        return getOwner().getPlunderComponent().getArmyPlunderWeight(true);
    }

    protected void onEndSingleRelationInBigScene(EndSingleBattleEvent event) {

    }

    protected void onArmyStateChange(ArmyStateChangeEvent event) {
        // 不是隐身状态就不处理状态变更事件
        if (getBattleRole() == null || !getBattleRole().isInstate(BattleConstants.BattleRoleState.STEALTH)) {
            return;
        }
        getBattleRole().addBattleRoundEvent(new BattleArmyStateChangeEvent(event.getOldState(), event.getNewState()));
    }

    protected void onEndSingleRelation(EndSingleBattleEvent event) {
        SceneObjBattleComponent other = event.getOther();
        BattleOverType type = event.getType();
        boolean enemyDead = event.isEnemyDead();
        boolean isBuilding = other.getEntityId() == curAttackBuilding;
        if (isBuilding) {
            setCurAttackBuilding(0);
            // 打玩家城市胜利 给自己发一个胜利弹框(仅大世界)
            if (hasAnyAlive() && enemyDead && other.getEntityType() == EntityType.ET_City) {
                CityEntity target = (CityEntity) (other.getOwner());
                if (target.isPlayerCity() && this.getOwner().getScene().isMainScene()) {
                    ntfAtkCityWin(other);
                }
            }
        }
        // 尝试退出围攻
        other.getOwner().getTransformComponent().exitBesiege(getEntityId());
        // 一场战斗结束 记录战损什么的
        onEndSingleRelationInBigScene(event);
        // 采集中被打
        if (getOwner().getProp().getAttachState() == AttachState.AAS_COLLECT) {
            getOwner().getCollectComponent().onSingleBattleEnd(other.getEntityId());
        }
        // 直接delete导致的结束战斗  不要处理了
        if (getOwner().isDestroy()) {
            // 触发预警的移除
            getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
            return;
        }
        // 打其他的
        if (getActiveTargetId() == other.getEntityId()) {
            // 打资源田打赢了
            if (isBuilding && getOwner().getCollectComponent().checkIsAttackResBuilding()) {
                getOwner().getMoveComponent().stopMove();
                // 没进去田
                if (!getOwner().getCollectComponent().onAttackSucceed(type, enemyDead)) {
                    afterBattleAction();
                }
                // 触发预警的移除
                getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
                return;
            }
            // 切换自动追击
            boolean isChangeToMoveBattle = false;
            if (type != BattleOverType.BOT_OUT_OF_LOSE && !enemyDead && hasAnyAlive() && getOwner().getStatusComponent().getState() == ArmyDetailState.ADS_BATTLE && getOwner().getMoveComponent().isMoving()) {
                isChangeToMoveBattle = true;
                // 自动追击
                getOwner().getStatusComponent().setDetailTarget(ArmyDetailState.ADS_MOVE_BATTLE, other.getOwner());
                if (getOwner().isRallyArmy()) {
                    RallyEntity rallyEntity = getOwner().getRallyEntity();
                    if (rallyEntity != null) {
                        rallyEntity.getStateComponent().onRallyArmyMove(getOwner().getMoveComponent().getMoveArriveTime(), getOwner().getMoveComponent().getEndPoint(), true);
                    }
                }
            } else {
                // 触发预警的移除
                getOwner().getEventDispatcher().dispatch(new WarningRemoveEvent(getEntityId()));
            }
            if (enemyDead) {
                // 同归于尽
                if (!getBattleRole().hasAnyAlive()) {
                    getOwner().getMoveComponent().retreat();
                    return;
                }
                // 非战斗目标的胜利不处理
                //攻打地图建筑不在这边处理，在建筑那边
                if (other.getOwner().getEntityType() == EntityType.ET_MapBuilding) {
                    MapBuildingEntity mapBuilding = (MapBuildingEntity) other.getOwner();
                    mapBuilding.getBattleComponent().addWaitEndArmy(getOwner());
                    return;
                }
                // 集结体直接通成功即可
                if (getOwner().isRallyArmy()) {
                    RallyEntity rallyEntity = getOwner().getRallyEntity();
                    if (rallyEntity != null) {
                        rallyEntity.onRallyKillTarget();
                    }
                } else {
                    afterBattleAction();
                }
                return;
            }
            // 对方没挂 是自己挂了 在endAll里处理
            if (!hasAnyAlive()) {
                return;
            }
            // 双方都没挂 还不是在追击
            if (!isChangeToMoveBattle) {
                if (getOwner().isRallyArmy()) {
                    RallyEntity rallyEntity = getOwner().getRallyEntity();
                    if (rallyEntity != null && !rallyEntity.checkDismissed()) {
                        rallyEntity.dismiss(RallyDismissReason.RDR_TARGET_CANT_ATTACK);
                    }
                } else {
                    afterBattleAction();
                }
            }
        }
    }

    private void ntfAtkCityWin(SceneObjBattleComponent other) {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        PlayerScene.Player_PlayDialog_NTF.Builder builder = PlayerScene.Player_PlayDialog_NTF.newBuilder().setId(constTemplate.getVictoryOpenUi());
        builder.getParamsBuilder().setEntityId(other.getEntityId());
        this.getOwner().getScenePlayer().sendMsgToClient(MsgType.PLAYER_PLAYDIALOG_NTF, builder.build());
    }

    @Override
    public void afterEndAllRelation() {
        super.afterEndAllRelation();
        // 援助就不做后处理了  占领完就进入援助状态
        if (getOwner().isInAssist()) {
            getOwner().getBehaviourComponent().refreshArmyState();
            return;
        }
        if (!getBattleRole().hasAnyAlive()) {
            if (getOwner().isDestroy()) {
                return;
            }
            // 集结挂了 通知集结体
            if (getOwner().isRallyArmy()) {
                getOwner().getRallyEntity().onRallyArmyDie();
                return;
            }
            getOwner().getMoveComponent().retreat();
        } else {
            getOwner().getBehaviourComponent().refreshArmyState();
        }
    }

    @Override
    public void fillRoleMember(BattleRecord.RoleRecord roleRecord) {
        if (getOwner().isRallyArmy()) {
            for (ArmyEntity inRallyArmy : getOwner().getRallyEntity().getArmyMgrComponent().getInRallyArmies()) {
                roleRecord.addMember(inRallyArmy.getBattleComponent().buildRoleMemberRecord());
            }
        } else {
            roleRecord.addMember(getOwner().getBattleComponent().buildRoleMemberRecord());
        }
    }

    @Override
    public BattleRecord.RoleMemberRecord buildRoleMemberRecord() {
        BattleRecord.RoleMemberRecord member = new BattleRecord.RoleMemberRecord()
                .setMemberRoleId(getRoleId())
                .setPlayerId(getOwner().getPlayerId())
                .setClanName(getOwner().getProp().getClanSname())
                .setIsCutIn(false)
                .setCardHead(getOwner().getProp().getCardHead().getCopySsBuilder().build());
        HeroProp mainHeroProp = getOwner().getProp().getTroop().getMainHero();
        HeroProp deputyHeroProp = getOwner().getProp().getTroop().getDeputyHero();
        return member.buildRoleMemberRecord(mainHeroProp, deputyHeroProp, getBattleRole().aliveCountByMember());
    }

    public void trySendHospitalFullCauseDeadMail() {

    }


    public void afterBattleAction() {
        // 援助就不做后处理了  占领完就进入援助状态
        if (getOwner().isInAssist()) {
            getOwner().getBehaviourComponent().refreshArmyState();
            return;
        }
        if (isStayAfterBattle) {
            getOwner().getMoveComponent().stopMove();
            getOwner().getStatusComponent().setStaying();
        } else {
            // 击败了目标 且选择了不停留 则回主堡
            getOwner().getMoveComponent().returnMainCity();
        }
        getOwner().getBehaviourComponent().refreshArmyState();
    }

    @Override
    public boolean beAttacked(ArmyEntity armyEntity, Long attackerPlayerId) {
        // 添加预警信息
        getOwner().addWarningItem(armyEntity, armyEntity.isRallyArmy() ? WarningType.WT_RallyAttack : WarningType.WT_Attack);
        return super.beAttacked(armyEntity, attackerPlayerId);
    }

    @Override
    protected void onTargetPkMe(long targetId) {
//        if (this.getTargetId() == 0) {
//            ArmyState state = getOwner().getProp().getArmyState();
//            if (state != ArmyState.AS_Staying) {
//                return;
//            }
//            setActiveTargetId(targetId);
//        }
        getOwner().getBehaviourComponent().refreshArmyState();
    }

    @Override
    protected void onEnterBattleState(IBattleRoleAdapter other) {
        super.onEnterBattleState(other);
        getOwner().getBehaviourComponent().refreshArmyState();
    }

    @Override
    public void onAddBattleRelation(BattleRelation battleRelation) {
        super.onAddBattleRelation(battleRelation);
        getOwner().getBehaviourComponent().refreshArmyState();
        BattleRole other = battleRelation.getOther();
        // 集结体与目标开战了
        if (getOwner().isRallyArmy() && other.getRoleId() == getOwner().getBehaviourComponent().getCurBehaviourTargetId()) {
            SceneObjEntity target = getObjEntityByBattleRole(other.getRoleId());
            if (target != null) {
                getOwner().getRallyEntity().getStateComponent().onRallyArmyStartBattle(target);
            } else {
                LOGGER.error("onAddBattleRelation failed. otherRole:{} not found.", other);
            }
        }
        BattleRole enemyRole = battleRelation.getEnemyRole(getRoleId());
        getOwner().getHuntingComponent().onAddBattleRelation(enemyRole.getRoleId());
    }

    @Override
    public boolean canAction() {
        return getOwner().getBehaviourComponent().canAttackAction();
    }

    @Override
    public void fillRoleSummary(BattleRecordAllProp recordAllProp) {
        // 集结体用的是车头的名字
        BattleRecordRoleSummaryProp selfSummary = recordAllProp.getSelfSummary();
        BattleRecordRoleProp selfRoleProp = getSelfOrEnemyRole(recordAllProp, false);
        if (selfRoleProp != null) {
            selfSummary.setClanName(selfRoleProp.getClanName());
            selfSummary.getCardHead().mergeFromSs(selfRoleProp.getCardHead().getCopySsBuilder().build());
        } else {
            selfSummary.setClanName(getOwner().getProp().getClanSname());
            selfSummary.getCardHead().mergeFromSs(getOwner().getProp().getCardHead().getCopySsBuilder().build());
        }
    }

    @Override
    public void fillRole(BattleRecord.RoleRecord roleRecord) {
        // 集结体用的是车头的名字
        roleRecord.setClanName(getOwner().getProp().getClanSname());
        Point cityLocation = getOwner().getMainCityPoint();
        if (cityLocation != null) {
            roleRecord.setLocation(cityLocation.getX(), cityLocation.getY(), this.getOwner().getScene().getMapIdForPoint(), this.getOwner().getScene().getMapType());
        }
        roleRecord.setCardHead(getOwner().getProp().getCardHead().getCopySsBuilder().build());
    }

    /**
     * 如果是集结体的话，需要填充BattleRole持有的Soldier的子部队，因为战斗里Soldier伤兵计算依赖所有子部队
     */
    private void fillSoldierChildForRally() {
        if (!getOwner().isRallyArmy()) {
            return;
        }
        RallyEntity rallyEntity = getOwner().getRallyEntity();
        Collection<ArmyEntity> inRallyArmies = rallyEntity.getArmyMgrComponent().getInRallyArmies();
        for (ArmyEntity memberArmy : inRallyArmies) {
            for (SoldierProp soldierProp : memberArmy.getProp().getTroop().getTroop().values()) {
                getBattleRole().addSoldierUnit(new SoldierUnit.OwnerInfo(memberArmy.getEntityId(), SceneObjType.SOT_ARMY_GROUP), soldierProp);
            }
        }
    }

    private void onBattleUnitPropChange(BattleUnitPropChangeEvent event) {
        LOGGER.debug("{} onBattleUnitPropChange event={}", this.getOwner(), event);
        if (event.getHeroList() != null && event.getHeroList().size() > 0) {
            for (Struct.Hero hero : event.getHeroList()) {
                final int heroId = hero.getHeroId();
                TroopProp troopProp = getOwner().getProp().getTroop();
                if (troopProp.getMainHero().getHeroId() == heroId) {
                    troopProp.getMainHero().mergeChangeFromSs(hero);
                    if (getMainHero() != null) {
                        getMainHero().refreshHeroByProp();
                    }
                }
                if (troopProp.getDeputyHero().getHeroId() == heroId) {
                    troopProp.getDeputyHero().mergeChangeFromSs(hero);
                    if (getBattleRole().getDeputyHero() != null) {
                        getBattleRole().getDeputyHero().refreshHeroByProp();
                    }
                }
                if (getOwner().isInAssist()) {
                    final long curAssistTargetId = getOwner().getAssistComponent().getAssistTargetId();
                    SceneObjEntity assistTargetEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(curAssistTargetId);
                    if (assistTargetEntity != null) {
                        assistTargetEntity.getEventDispatcher().dispatch(new ArmyHeroPropChangeEvent(getEntityId(), hero));
                    }
                }
            }
        }
    }

    @Override
    protected String getBattleRecordMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordRoleProp enemyRole) {
        ConstTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
        if (alive && anyEnemyAlive) {
            // "战争报告" : "战斗报告";
            return getOwner().isRallyArmy() ? constTemplate.getBattleRecord6() : constTemplate.getBattleRecord3();
        } else if (alive) {
            // "战争胜利" : "战斗胜利";
            return getOwner().isRallyArmy() ? constTemplate.getBattleRecord4() : constTemplate.getBattleRecord1();
        } else {
            // "战争失败" : "战斗失败";
            return getOwner().isRallyArmy() ? constTemplate.getBattleRecord5() : constTemplate.getBattleRecord2();
        }
    }

    @Override
    protected StructMail.MailShowTitle getMailTitle(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        StructMail.MailShowTitle.Builder builder = StructMail.MailShowTitle.newBuilder();
        BattleRecordRoleProp enemyRole = getSelfOrEnemyRole(recordAllProp, true);
        // title
        builder.setTitleKey(getBattleRecordMailTitle(alive, anyEnemyAlive, enemyRole));

        boolean isRallyArmy = getOwner().isRallyArmy();

        // sub title
        if (enemyRole != null) {
            boolean isCityAtk = BattleRecord.isPlayerCity(enemyRole.getRoleType());
            boolean isBuildingAtk = BattleRecord.isMapBuilding(enemyRole.getRoleType());
            boolean isMonsterAtk = BattleRecord.isMonster(enemyRole.getRoleType());
            boolean isSuperWeaponAtk = enemyRole.getEntityType() == EntityType.ET_AreaSkill.getNumber();

            String subTitleKey;
            ConstTemplate temple = ResHolder.getInstance().getConstTemplate(ConstTemplate.class);
            if (isCityAtk) {
                // 攻基地
                subTitleKey = temple.getBattleRecordSub3();
                String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(enemyRole.getCardHead().getName()));
            } else if (isBuildingAtk) {
                // 攻击地图建筑
                // 没有占领者
                if (enemyRole.getRoleType() == SceneObjType.SOT_STRONG_POINT_ARMY) {
                    subTitleKey = temple.getBattleRecordSub9();
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, enemyRole.getBuildingId()));
                } else {
                    String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));

                    // 打建筑同时被其他部队打 与[clanName]playerName等人发生了战斗
                    if (isMultiArmy(recordAllProp)) {
                        subTitleKey = isRallyArmy ? temple.getBattleRecordSub13() : temple.getBattleRecordSub5();
                        builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(enemyRole.getCardHead().getName()));
                    } else {
                        // 攻击了[clanName]的buildingId
                        subTitleKey = temple.getBattleRecordSub8();
                        builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_TERRITORY_ID_FOR_NAME, enemyRole.getBuildingId()));
                    }
                }
            } else {
                // 野外遭遇战（野怪，采集点，驻扎点）
                if (isMonsterAtk) {
                    // 打野，填充野怪lv, id
                    MonsterTemplate template = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, Integer.parseInt(enemyRole.getCardHead().getName()));
                    if (template.getCategory() == CommonEnum.MonsterCategory.BUILDING_GUARD || template.getCategory() == CommonEnum.MonsterCategory.DUNGEON_MONSTER) {
                        // 城市守护者不太一样，不传等级
                        if (isRallyArmy) {
                            subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub13() : temple.getBattleRecordSub12();
                        } else {
                            subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub5() : temple.getBattleRecordSub4();
                        }
                        builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(""));
                    } else {
                        // 通用打野，要有等级
                        if (isRallyArmy) {
                            subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub15() : temple.getBattleRecordSub14();
                        } else {
                            subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub11() : temple.getBattleRecordSub10();
                        }
                        builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(String.valueOf(template.getLevel())));
                    }
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayId(DisplayParamType.DPT_MONSTER_ID, Long.parseLong(enemyRole.getCardHead().getName())));
                } else if (isSuperWeaponAtk) {
                    // 超武战报
                    subTitleKey = temple.getBattleRecordSub16();
                    String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayHero(enemyRole.getMainHero().getHeroId()));
                } else {
                    if (isRallyArmy) {
                        subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub13() : temple.getBattleRecordSub12();
                    } else {
                        subTitleKey = isMultiArmy(recordAllProp) ? temple.getBattleRecordSub5() : temple.getBattleRecordSub4();
                    }
                    String clanName = StringUtils.isNotBlank(enemyRole.getClanName()) ? "[" + enemyRole.getClanName() + "]" : "";
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(clanName));
                    builder.getSubTitleDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(enemyRole.getCardHead().getName()));
                }
            }
            builder.setSubTitleKey(subTitleKey);
        }
        return builder.build();
    }

    @Override
    protected QlogCncBattleReport getBattleRepFlow(boolean alive, boolean anyEnemyAlive, BattleRecordAllProp recordAllProp) {
        QlogCncBattleReport battleRepFlow = new QlogCncBattleReport();
        battleRepFlow.setDtEventTime(TimeUtils.now2String())
                .setBattleReportId(String.valueOf(recordAllProp.getRecordId()))
                .setBattleId(QlogUtils.transCollection2ArrayString(recordAllProp.getSingleRecordList().stream().map(BattleRecordOneProp::getBattleId).collect(Collectors.toList())))
                .setBattleReportTimeStart(TimeUtils.timeStampMs2String(recordAllProp.getStartMillis()))
                .setBattleReportTimeEnd(TimeUtils.timeStampMs2String(recordAllProp.getEndMillis()))
                .setBattleReportResult(BattleRepResult.getBattleResult(alive, anyEnemyAlive).ordinal())
                .setMainObjectType(BattleRepMainObjType.getMainObjType(getOwner().isRallyArmy()).ordinal())
                .setDefenceOrNot(BattleRepDefenceType.DEFENCE_NONE.ordinal())
                .setMainObjectId(String.valueOf(getOwner().getPlayerId()))
                .setMainObjectBuildingId("");
        List<String> list = Lists.newArrayList();
        for (BattleRecordOneProp battleRecordOneProp : recordAllProp.getSingleRecordList()) {
            if (!BattleRecord.isMonster(battleRecordOneProp.getRightRole().getRoleType())) {
                continue;
            }
            list.add(battleRecordOneProp.getRightRole().getCardHead().getName());
        }
        battleRepFlow.setEnemyId(QlogUtils.transCollectionString2ArrayString(list));
        return battleRepFlow;
    }

    @Override
    protected QlogCncBattle constructBattleFlow(boolean alive, boolean isEnemyAlive, BattleRecord.RecordOne record) {
        return null;
    }


    /**
     * 中途离开战斗中的BattleRole，结算资源，填充战报
     */
    public void settleRoleMemberWhenLeaveBattleRole(BattleRole battleRole) {
        if (!(getOwner().getScenePlayer() instanceof ScenePlayerEntity)) {
            return;
        }
        // 当前负重
        Map<Long, SsPlayerMisc.PlunderWeight> weightMap = getOwner().getPlunderComponent().getArmyPlunderWeight(false);
        // 处理资源损失
        getOwner().getPlunderComponent().handleArmyResourceLoss(weightMap, battleRole, null);
        // 遍历退出的集结的所有relation，检查资源负重，写战报
        battleRole.forEachRelation(relation -> {
            // 战报填充
            BattleRecord.RoleRecord roleRecord = relation.getContext().getRoleRecord(battleRole.getRoleId());
            for (Soldier soldier : getBattleRole().getSoldierList()) {
                roleRecord.setMbrLeftAlive(getRoleId(), soldier.getId(), soldier.aliveCount());
            }
        });
    }

    /**
     * 中途加入战斗中的BattleRole，填充战报
     */
    public void fillRoleMemberWhenArriveBattleRole(BattleRole battleRole) {
        BattleRecord.RoleMemberRecord roleMemberRecord = getOwner().getBattleComponent().buildRoleMemberRecord().setIsCutIn(true);
        battleRole.forEachRelation(battleRelation -> battleRelation.getContext().getRoleRecord(battleRole.getRoleId()).addMember(roleMemberRecord));
    }

    @Override
    public void onBattleRoundEvent(BattleRoundEvent event) {
        if (!isInBattle()) {
            return;
        }
        if (!getOwner().isRallyArmy() && getOwner().isInRally()) {
            if (getOwner().getRallyEntity().getLeaderArmy().getEntityId() == getEntityId()) {
                // 我是集结车头
                getOwner().getRallyEntity().getArmyMgrComponent().getRallyArmy().getEventDispatcher().dispatch(event);
            }
        } else {
            super.onBattleRoundEvent(event);
        }
    }

    @Override
    public Set<Long> getAllEnemyClan() {
        return getOwner().getScenePlayer().getMainCity().getBattleComponent().getAllEnemyClan();
    }

    @Override
    public Set<Long> getAllEnemyPlayerId() {
        return getOwner().getScenePlayer().getMainCity().getBattleComponent().getAllEnemyPlayerId();
    }

    @Override
    public List<Long> getAllChildRoleIdList() {
        List<Long> ret = Lists.newArrayList();
        if (getOwner().isRallyArmy()) {
            for (ArmyEntity inRallyArmy : getOwner().getRallyEntity().getArmyMgrComponent().getInRallyArmies()) {
                ret.add(inRallyArmy.getEntityId());
            }
        } else {
            ret.add(getEntityId());
        }
        return ret;
    }

    @Override
    public void trySendRecordMail(Set<Long> playerIds, BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
        if (!getOwner().getScene().needSendBattleMail()) {
            return;
        }
        batchSendRecordMail(playerIds, recordAllProp, alive, anyEnemyAlive);
    }

    @Override
    public Set<Long> getBattleRecordPlayerIds() {
        return getOwner().isRallyArmy() ? super.getBattleRecordPlayerIds() : Sets.newHashSet(getOwner().getPlayerId());
    }

    public void goToHospital(List<Struct.PlayerHospitalSoldier> severeSoldier, Map<Integer, SoldierLossData> beforeHospitalData) {
        if (getOwner().getScenePlayer().getHospitalComponent() != null) {
            goToHospital(getOwner().getScenePlayer().getHospitalComponent(), getOwner().getProp().getBattle(), severeSoldier, beforeHospitalData);
        }

    }

    @Override
    public long getLeaderRoleId() {
        if (getOwner().isRallyArmy()) {
            RallyEntity rallyEntity = getOwner().getRallyEntity();
            if (rallyEntity == null) {
                WechatLog.error("ArmyBattleComponent getLeaderRoleId fail, entityId={}", getOwner().getEntityId());
                return 0L;
            }
            return rallyEntity.getLeaderArmy().getBattleComponent().getRoleId();
        } else {
            return getRoleId();
        }
    }

    @Override
    public boolean isRally() {
        return getOwner().isRallyArmy();
    }

    @Override
    public long getRallyCapacity() {
        RallyEntity rallyEntity = getOwner().getRallyEntity();
        if (rallyEntity != null) {
            return rallyEntity.getProp().getMaxSoldierNum();
        }
        return -1;
    }

    @Override
    public boolean canBeSearchSelect() {
        // 采集状态需要过滤AOI索敌
        return !isCollecting();
    }

    @Override
    public boolean isCollecting() {
        return getOwner().getProp().getAttachState() == CommonEnum.AttachState.AAS_COLLECT;
    }

    @Override
    protected AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getScenePlayer();
    }

    @Override
    public boolean ready(IBattleRoleAdapter other) {
        // 构建战报成员
        buildBattleEntityCache();
        return super.ready(other);
    }

    private void buildBattleEntityCache() {
        if (isInBattle()) {
            return;
        }
        if (getOwner().isRallyArmy()) {
            Collection<ArmyEntity> inRallyArmies = getOwner().getRallyEntity().getArmyMgrComponent().getInRallyArmies();
            for (ArmyEntity memberArmy : inRallyArmies) {
                addBattleEntityCache(memberArmy.getEntityId(), memberArmy.getPlayerId());
            }
        } else {
            addBattleEntityCache(getEntityId(), getOwner().getPlayerId());
        }
    }

    @Override
    public CommonEnum.ArmyDetailState getArmyDetailState() {
        return getOwner().getStatusComponent().getState();
    }

    @Override
    public int getFreeHitMonsterCount() {
        ScenePlayerMonsterModelProp monsterProp = getOwner().getScenePlayer().getMonsterProp();
        if (monsterProp == null) {
            return super.getFreeHitMonsterCount();
        }
        if (!TimeUtils.isSameDayWithNow(monsterProp.getCountLastUpdateTsMs())) {
            // 不是同一个天，刷新次数
            monsterProp.setTodayFreeHitCount(0);
            monsterProp.setCountLastUpdateTsMs(SystemClock.now());
            LOGGER.info("getFreeHitMonsterCount refresh! player={} army={} ", getOwner().getScenePlayer(), getOwner());
        }
        return monsterProp.getTodayFreeHitCount();
    }

    public void updateFreeHitMonsterCount(int count) {
        if (count <= 0) {
            return;
        }
        ScenePlayerMonsterModelProp monsterProp = getOwner().getScenePlayer().getMonsterProp();
        if (monsterProp == null) {
            return;
        }
        monsterProp.setTodayFreeHitCount(getFreeHitMonsterCount() + count);
        monsterProp.setCountLastUpdateTsMs(SystemClock.now());
        LOGGER.info("updateFreeHitMonsterCount player={} army={} count={}", getOwner().getScenePlayer(), getOwner(), monsterProp.getTodayFreeHitCount());
    }
}
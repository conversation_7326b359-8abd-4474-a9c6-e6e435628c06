package com.yorha.cnc.scene.event.battle;

import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.proto.CommonEnum;

public class ArmyStateChangeEvent extends IEvent {

    private CommonEnum.ArmyDetailState oldState;
    private CommonEnum.ArmyDetailState newState;

    public CommonEnum.ArmyDetailState getOldState() {
        return oldState;
    }

    public CommonEnum.ArmyDetailState getNewState() {
        return newState;
    }

    public ArmyStateChangeEvent(CommonEnum.ArmyDetailState oldState, CommonEnum.ArmyDetailState newState) {
        this.oldState = oldState;
        this.newState = newState;
    }

}

package com.yorha.cnc.scene.logisticsPlane.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;
import com.yorha.cnc.scene.logisticsPlane.LogisticsPlaneEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.cnc.scene.sceneObj.move.MoveData;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.game.gen.prop.ScenePlayerLogisticsStatusProp;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstConpensationTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.enums.error.ErrorCode.LOGISTICS_UNSUPPORT_ACTION;


/**
 * 运输机行为模块
 *
 * <AUTHOR>
 */
public class LogisticsPlaneBehaviourComponent extends SceneObjComponent<LogisticsPlaneEntity> {
    private static final Logger LOGGER = LogManager.getLogger(LogisticsPlaneBehaviourComponent.class);
    /**
     * 预警的玩家对象
     */
    private final Set<Long> warningPlayerSet = new HashSet<>();
    TimerReasonType transportTask = null;
    long targetPlayerId = 0; // 因为有可能援助对象主包升天了，这时候援助失败的邮件里需要有援助对象的玩家名

    public LogisticsPlaneBehaviourComponent(LogisticsPlaneEntity owner) {
        super(owner);
    }

    @Override
    public void init() {
        getOwner().getPlayer().getPlaneComponent().addAttention(getEntityId());
    }


    /**
     * 行为处理
     *
     * @param info
     */
    public void handleAction(StructMsg.LogisticsInfo info, MoveData path) {
        switch (info.getActionType()) {
            case LAT_RESOURCE_ASSIST: {
                targetPlayerId = 0;
                resourceAssist(info, path);
                break;
            }
            case LAT_RETURN:
                // 资源返还发送援助失败邮件
                returnResource("cancel and return");
                // 移除预警
                removeWarningItem();
                returnHome();
                targetPlayerId = 0;
                break;
            default:
                throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "handleAction unsupport action type: " + info.getActionType());
        }

    }

    private ScenePlayerLogisticsStatusProp getStatusProp() {
        return getOwner().getPlayer().getLogisticMgrComponent().getLogisticsPlaneStatus(getEntityId());
    }

    /**
     * 主动资源援助
     */
    private void resourceAssist(StructMsg.LogisticsInfo info, MoveData path) {
        // 缓存目标对象的玩家id，用于发邮件用
        targetPlayerId = info.getTargetId();
        // 起飞
        takeOff(info, path);
    }

    /**
     * 起飞
     *
     * @param info
     */
    private void takeOff(StructMsg.LogisticsInfo info, MoveData path) {
        if (transportTask != null) {
            getOwner().getTimerComponent().cancelTimer(transportTask);
            transportTask = null;
        }
        // 状态修改
        int liftOffTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getLandingTime();
        long moveStartTsMs = SystemClock.now() + TimeUtils.second2Ms(liftOffTime);
        // 增加起飞结束定时器
        getOwner().getTimerComponent().addTimer(TimerReasonType.PLANE_TAKE_OFF,
                () -> enterTransport(getOwner().getCurPoint(), info.getTargetId(), path),
                liftOffTime, TimeUnit.SECONDS);
        transportTask = TimerReasonType.PLANE_TAKE_OFF;
        refreshState(CommonEnum.LogisticsPlaneState.LPS_TAKE_OFF, moveStartTsMs);
    }

    private void onTargetLose(IEvent event) {
        onTargetLose(0);
    }

    /**
     * 目标丢失（目标不可运输）
     */
    private void onTargetLose(int codeId) {
        // 资源返还
        returnResource("onTargetLose: " + codeId);
        // 折返回城
        returnHome();
    }

    /**
     * 发送给自己 资源援助失败的邮件
     */
    private void sendAssistFailMail() {
        int mailId = ResHolder.getConsts(ConstConpensationTemplate.class).getRssAssistFailedReport();
        AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId);
        StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
        mailSendParams.setMailTemplateId(mailId);
        mailSendParams.getContentBuilder()
                .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_DISPLAY_DATA)
                .getDisplayDataBuilder()
                .getParamsBuilder()
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(abstractScenePlayerEntity.getName()));
        Struct.Int32CurrencyMap.Builder resourcesBuilder = mailSendParams.getContentBuilder().getAssistDataBuilder().getResourcesBuilder();
        Map<Integer, Struct.Currency> tempMap = new HashMap<>();
        for (CurrencyProp currencyProp : getStatusProp().getResources().values()) {
            if (!tempMap.containsKey(currencyProp.getType())) {
                tempMap.put(currencyProp.getType(), Struct.Currency.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount()).build());
                continue;
            }
            tempMap.put(currencyProp.getType(), Struct.Currency.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount() + tempMap.get(currencyProp.getType()).getCount()).build());
        }
        for (CurrencyProp taxProp : getStatusProp().getTax().values()) {
            if (!tempMap.containsKey(taxProp.getType())) {
                tempMap.put(taxProp.getType(), Struct.Currency.newBuilder().setType(taxProp.getType()).setCount(taxProp.getCount()).build());
                continue;
            }
            tempMap.put(taxProp.getType(), Struct.Currency.newBuilder().setType(taxProp.getType()).setCount(taxProp.getCount() + tempMap.get(taxProp.getType()).getCount()).build());
        }
        resourcesBuilder.putAllDatas(tempMap);
        sendMail(getOwner().getPlayerId(), mailSendParams);
    }

    /**
     * 发送给自己和对方 资源援助成功的邮件
     */
    private void sendAssistSuccessMail() {
        int toSelfMailId = ResHolder.getConsts(ConstConpensationTemplate.class).getRssAssistReport();
        int toTargetMailId = ResHolder.getConsts(ConstConpensationTemplate.class).getRssAssistedReport();
        AbstractScenePlayerEntity abstractScenePlayerEntity = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId);
        StructMail.MailSendParams.Builder mailSendParamsToSelf = StructMail.MailSendParams.newBuilder();
        StructMail.MailSendParams.Builder mailSendParamsToTarget = StructMail.MailSendParams.newBuilder();
        // 给自己邮件
        mailSendParamsToSelf.setMailTemplateId(toSelfMailId)
                .getTitleBuilder()
                .getSubTitleDataBuilder()
                .getParamsBuilder()
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(abstractScenePlayerEntity.getName()));
        mailSendParamsToSelf.getContentBuilder()
                .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA)
                .getDisplayDataBuilder()
                .getParamsBuilder()
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(abstractScenePlayerEntity.getName()));
        Struct.Int32CurrencyMap.Builder resourcesBuilder = mailSendParamsToSelf.getContentBuilder().getAssistDataBuilder().getResourcesBuilder();
        Struct.Int32CurrencyMap.Builder toTargetResourcesBuilder = mailSendParamsToTarget.getContentBuilder().getAssistDataBuilder().getResourcesBuilder();
        for (CurrencyProp currencyProp : getStatusProp().getResources().values()) {
            resourcesBuilder.putDatas(currencyProp.getType(), Struct.Currency.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount()).build());
            toTargetResourcesBuilder.putDatas(currencyProp.getType(), Struct.Currency.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount()).build());
        }
        Struct.Int32CurrencyMap.Builder taxBuilder = mailSendParamsToSelf.getContentBuilder().getAssistDataBuilder().getTaxBuilder();
        for (CurrencyProp taxProp : getStatusProp().getTax().values()) {
            taxBuilder.putDatas(taxProp.getType(), Struct.Currency.newBuilder().setType(taxProp.getType()).setCount(taxProp.getCount()).build());
        }
        sendMail(getOwner().getPlayerId(), mailSendParamsToSelf);
        // 给对方的邮件
        mailSendParamsToTarget.setMailTemplateId(toTargetMailId)
                .getTitleBuilder()
                .getSubTitleDataBuilder()
                .getParamsBuilder()
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(getOwner().getPlayer().getName()));
        mailSendParamsToTarget.getContentBuilder()
                .setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA)
                .getDisplayDataBuilder()
                .getParamsBuilder()
                .addDatas(Struct.DisplayParam.newBuilder()
                        .setType(CommonEnum.DisplayParamType.DPT_TEXT)
                        .setText(getOwner().getPlayer().getName()));
        // 资源前面已经填充了，且不填充赋税
        sendMail(targetPlayerId, mailSendParamsToTarget);
    }

    /**
     * 发邮件
     *
     * @param playerId
     * @param mailSendParams
     */
    private void sendMail(long playerId, StructMail.MailSendParams.Builder mailSendParams) {
        MailUtil.sendMailToPlayer(
                CommonMsg.MailReceiver.newBuilder()
                        .setPlayerId(playerId)
                        .setZoneId(getOwner().getPlayer().getZoneId())
                        .build(),
                mailSendParams.build());
        LOGGER.debug("player:{} logistics:{} send mail:{}", getOwner().getPlayerId(), getEntityId(), mailSendParams.getMailTemplateId());
    }

    /**
     * 给运输对象资源
     */
    private void giveTargetResource() {
        SceneObjEntity sceneObjEntity = getOwner().getMoveComponent().getMoveTarget();
        if (sceneObjEntity == null) {
            throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, getOwner() + "giveTargetResource target is null");
        }
        // 给目标玩家运输的资源
        giveResource(sceneObjEntity.getPlayerId(), getStatusProp().getResources().values(), "giveTargetResource");
        // 发送运输成功的邮件
        sendAssistSuccessMail();
        // 给自己加援助资源
        addResourceAssistRecord();
    }

    private void addResourceAssistRecord() {
        SsPlayerMisc.AddResourceAssistRecordCmd.Builder cmd = SsPlayerMisc.AddResourceAssistRecordCmd.newBuilder();
        Map<Integer, Long> currencyMap = new HashMap<>();
        for (CurrencyProp currencyProp : getStatusProp().getResources().values()) {
            cmd.addCurrencys(Struct.Currency.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount()).build());
            currencyMap.put(currencyProp.getType(), currencyProp.getCount());
        }
        LOGGER.info("playerId={} addResourceAssistRecord currencys={}", getOwner().getPlayerId(), currencyMap);
        getOwner().getPlayer().tellPlayer(cmd.build());
    }

    /**
     * 资源返还
     */
    private void returnResource(String reason) {
        ScenePlayerLogisticsStatusProp statusProp = getStatusProp();
        Collection<CurrencyProp> resources = new ArrayList<>(Collections.emptyList());
        resources.addAll(statusProp.getResources().values());
        resources.addAll(statusProp.getTax().values());
        // 返还玩家运输的资源与赋税
        giveResource(getOwner().getPlayerId(), resources, "returnResource: " + reason);
        // 发送援助失败的邮件
        sendAssistFailMail();
    }

    /**
     * 给资源
     *
     * @param playerId
     */
    private void giveResource(long playerId, Collection<CurrencyProp> resources, String reason) {
        SsPlayerMisc.GiveCurrencyCmd.Builder cmd = SsPlayerMisc.GiveCurrencyCmd.newBuilder();
        Map<Integer, Long> currencyMap = new HashMap<>();
        for (CurrencyProp currencyProp : resources) {
            cmd.addCurrencys(Struct.Currency.newBuilder().setType(currencyProp.getType()).setCount(currencyProp.getCount()).build());
            currencyMap.put(currencyProp.getType(), currencyProp.getCount());
        }
        LOGGER.info("playerId={} giveResource currencys={} reason={}", getOwner().getPlayerId(), currencyMap, reason);
        ownerActor().tellPlayer(getOwner().getPlayer().getZoneId(), playerId, cmd.build());
    }

    /**
     * 起飞结束 进入运输移动状态
     */
    private void enterTransport(Point point, long targetPlayerId, MoveData path) {
        LOGGER.info("{} enter LiftOff. point: {}, targetId: {}", getOwner(), point, targetPlayerId);
        // 预警
        addWarningItem(targetPlayerId);
        long targetId = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId).getMainCity().getEntityId();

        SceneObjEntity sceneObjEntity = getOwner().getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        // 解除起飞timer引用
        if (transportTask != null) {
            getOwner().getTimerComponent().cancelTimer(transportTask);
            transportTask = null;
        }
        getOwner().getMoveComponent().setCreatePrePath(path);
        refreshState(CommonEnum.LogisticsPlaneState.LPS_TRANSPORT, getOwner().getMoveComponent().getMoveArriveTime());
        getOwner().getMoveComponent().moveToTargetAsync(sceneObjEntity, null,
                () -> onTransportArrive(false),
                this::onTargetLose,
                (codeId) -> {
                    if (ErrorCode.isOK(codeId)) {
                        return;
                    }
                    onTargetLose(codeId);
                });
        getOwner().getMoveComponent().clearCreatePrePath();
    }

    private void onClanNotSame(String reason) {
        LOGGER.info("LogisticsPlaneBehaviourComponent onClanNotSame {}", reason);
        // 移除预警
        removeWarningItem();
        // 回家
        returnHome();
        returnResource("target is not same clan");
    }

    /**
     * 到达目的地/回城
     */
    private void onTransportArrive(boolean isReturnCity) {
        LOGGER.info("LogisticsPlaneBehaviourComponent {} onTransportArrive LisReturnCity {}", getOwner(), isReturnCity);
        // 到达目的地
        if (!isReturnCity) {
            // 双方非同联盟退还自己全部资源
            AbstractScenePlayerEntity planeOwner = getOwner().getPlayer();
            if (planeOwner == null) {
                onClanNotSame("planeOwner is null");
                return;
            }
            boolean isInClan = planeOwner.isInClan();
            if (!isInClan) {
                onClanNotSame("planeOwner not in clan");
                return;
            }
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId);
            if (scenePlayer == null) {
                LOGGER.error("LogisticsPlaneBehaviourComponent onTransportArrive {} {}", targetPlayerId, getOwner());
                onClanNotSame("scenePlayer is null");
                return;
            }
            long planeClanId = getOwner().getClanId();
            long playerClanId = scenePlayer.getClanId();
            if (planeClanId != playerClanId) {
                LOGGER.info("LogisticsPlaneBehaviourComponent onTransportArrive not same clanId {} {}", planeClanId, playerClanId);
                onClanNotSame("not same clan");
                return;
            }
            // 给对方本次运输的资源
            giveTargetResource();
        }
        // 开始降落
        int airborneTime = ResHolder.getResService(ConstKVResService.class).getTemplate().getLandingTime();
        if (transportTask != null) {
            getOwner().getTimerComponent().cancelTimer(transportTask);
            transportTask = null;
        }
        getOwner().getTimerComponent().addTimer(TimerReasonType.PLANE_LANDING,
                this::onTransportBorneEnd,
                airborneTime, TimeUnit.SECONDS);
        transportTask = TimerReasonType.PLANE_LANDING;
        // 开始降落
        refreshState(CommonEnum.LogisticsPlaneState.LPS_LANDING, SystemClock.now() + airborneTime);
        // 移除预警
        removeWarningItem();
    }

    public void checkOnCityMove() {
        if (!getOwner().getMoveComponent().isMoving()) {
            return;
        }
        if (getOwner().getMoveComponent().getCurChaseTargetId() != getOwner().getPlayer().getMainCity().getEntityId()) {
            return;
        }
        returnHome();
    }

    /**
     * 回家
     */
    private void returnHome() {
        refreshState(CommonEnum.LogisticsPlaneState.LPS_RETURN, getOwner().getMoveComponent().getMoveArriveTime());
        getOwner().getMoveComponent().moveToTargetAsync(
                getOwner().getPlayer().getMainCity(),
                CommonEnum.TroopInteractionType.RETURN_CITY,
                () -> onTransportArrive(true),
                null, null);
    }

    /**
     * 降落结束  到达目的地/回城
     */
    private void onTransportBorneEnd() {
        LOGGER.info("{} onTransportBorneEnd", getOwner());
        if (transportTask != null) {
            getOwner().getTimerComponent().cancelTimer(transportTask);
            transportTask = null;
        }
        // 回家
        getOwner().getBehaviourComponent().onReturnCityEnd();
    }

    /**
     * 运输机状态刷新，且同步scenePlayer自己的状态
     *
     * @param state
     * @param endTs
     */
    public void refreshState(CommonEnum.LogisticsPlaneState state, long endTs) {
        getOwner().getProp().setState(state).setEnterStateTs(SystemClock.now());
        getStatusProp().setStartTsMs(getOwner().getProp().getEnterStateTs())
                .setEndTsMs(endTs);
    }


    /**
     * 添加援助预警
     *
     * @param targetPlayerId
     */
    private void addWarningItem(long targetPlayerId) {
        if (targetPlayerId <= 0) {
            throw new GeminiException(LOGISTICS_UNSUPPORT_ACTION, "playerId invailed target: " + targetPlayerId);
        }
        AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(targetPlayerId);
        final AbstractScenePlayerWarningComponent playerWarningComponent = scenePlayer.getWarningComponent();
        if (playerWarningComponent == null) {
            LOGGER.info("LogisticsPlaneBehaviourComponent addWarningItem no warningComponent");
            return;
        }
        playerWarningComponent.addWarningItem(getOwner(), CommonEnum.WarningType.WT_Resource_Assist);
        warningPlayerSet.add(targetPlayerId);
    }

    /**
     * 移除援助预警
     */
    private void removeWarningItem() {
        for (long playerId : warningPlayerSet) {
            AbstractScenePlayerEntity scenePlayer = getOwner().getScene().getPlayerMgrComponent().getScenePlayer(playerId);
            if (scenePlayer == null) {
                continue;
            }
            final AbstractScenePlayerWarningComponent playerWarningComponent = scenePlayer.getWarningComponent();
            if (playerWarningComponent == null) {
                LOGGER.info("LogisticsPlaneBehaviourComponent removeWarningItem no warningComponent");
                continue;
            }
            scenePlayer.getWarningComponent().removeWarningItem(getEntityId());
        }
        warningPlayerSet.clear();
    }

    /**
     * 回到主城 返还运输资源
     */
    public void onReturnCityEnd() {
        targetPlayerId = 0;
        //清除本运输机的状态同步
        getOwner().getPlayer().getLogisticMgrComponent().clearLogisticPlaneStatus(getEntityId());
        getOwner().deleteObj();
    }

    /**
     * 停服时对场景上的运输机处理
     */
    public void onStopServer() {
        CommonEnum.LogisticsPlaneState state = getOwner().getProp().getState();
        // 对于起飞与运输中的运输机返还资源
        if (state == CommonEnum.LogisticsPlaneState.LPS_TRANSPORT || state == CommonEnum.LogisticsPlaneState.LPS_TAKE_OFF) {
            returnResource("stop Server");
        }
        // 回家
        onReturnCityEnd();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        removeWarningItem();
    }
}
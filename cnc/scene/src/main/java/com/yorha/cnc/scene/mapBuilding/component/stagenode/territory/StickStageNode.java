package com.yorha.cnc.scene.mapBuilding.component.stagenode.territory;

import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity;
import com.yorha.cnc.scene.mapBuilding.component.stagenode.base.StageNode;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.OccupyState;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 坚守阶段  阶段结束获得超武技能
 *
 * <AUTHOR>
 */
public class StickStageNode extends StageNode {
    private static final Logger LOGGER = LogManager.getLogger(StickStageNode.class);

    public StickStageNode(MapBuildingEntity owner) {
        super(owner);
    }

    @Override
    public OccupyState getStage() {
        return OccupyState.TOS_STICK;
    }

    @Override
    public void onLoad() {
        if (getProp().getStateEndTsMs() > SystemClock.now()) {
            addStageTimer(this::time2StickEnd);
            return;
        }
        onStickEnd(getProp().getStateEndTsMs());
    }

    @Override
    public void onEnter(long ts) {
        LOGGER.info("{} {} onEnter", getOwner(), this);
        long endTs = TimeUtils.second2Ms(getTemplate().getStickTime()) + ts;
        if (endTs <= SystemClock.now()) {
            onStickEnd(endTs);
            return;
        }
        getProp().setState(getStage()).setStateStartTsMs(ts).setStateEndTsMs(endTs);
        addStageTimer(this::time2StickEnd);
    }

    private void time2StickEnd() {
        getComponent().clearStageTimer();
        onStickEnd(SystemClock.now());
    }

    private void onStickEnd(long ts) {
        // 看下有没有保护阶段
        if (getOwner().getTerritoryBuildingTemplate().getProtectTime() != 0) {
            getComponent().transNewNode(new ProtectStageNode(getOwner()), ts);
            return;
        }
        // 看下有没有在指挥网中
        if (getOwner().getProp().getConstructInfo().getIsConnectedToCommandNet()) {
            getComponent().transNewNode(new InCommandNetStageNode(getOwner()), ts);
            return;
        }
        getComponent().transNewNode(new DesertedStageNode(getOwner()), ts);
    }
}

package com.yorha.cnc.scene.sceneObj.hateList;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;

/**
 * <AUTHOR>
 * <p>
 * 仇恨列表
 */
public abstract class AbstractHateList implements IHateList {

    private final SceneObjEntity owner;

    public AbstractHateList(SceneObjEntity owner) {
        this.owner = owner;
    }

    public SceneObjEntity getOwner() {
        return owner;
    }

    @Override
    public int getHateExpire() {
        return owner.getHateListComponent().getHateExpire();
    }
}

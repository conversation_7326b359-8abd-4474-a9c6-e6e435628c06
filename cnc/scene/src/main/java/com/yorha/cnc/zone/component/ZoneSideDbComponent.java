package com.yorha.cnc.zone.component;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.zone.IZoneSideComponent;
import com.yorha.cnc.zone.zone.ZoneEntity;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.DbLimitConstants;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.dbactor.ActorChangeAttrUpsertStrategy;
import com.yorha.common.dbactor.DbTaskProxy;
import com.yorha.common.dbactor.DefaultDbOperationStrategyImpl;
import com.yorha.common.framework.AbstractComponent;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.proto.TcaplusDb;
import com.yorha.proto.Zoneside;
import org.jetbrains.annotations.NotNull;

public class ZoneSideDbComponent extends AbstractComponent<ZoneEntity> implements IZoneSideComponent {

    /**
     * 操作增删改查的代理。
     */
    private final DbTaskProxy dbTaskProxy;

    public ZoneSideDbComponent(ZoneEntity entity, Zoneside.ZoneSideEntity changedAttr) {
        super(entity);
        ZoneSideChangeAttrUpsertStrategy s = new ZoneSideChangeAttrUpsertStrategy(changedAttr != null ? changedAttr : Zoneside.ZoneSideEntity.getDefaultInstance());
        this.dbTaskProxy = DbTaskProxy.newBuilder()
                .name(this.getOwner().toString() + " ZoneSideDbComponent")
                .operation(new DefaultDbOperationStrategyImpl(RefFactory.dbActorRef()))
                .upsert(s)
                .owner(ownerActor())
                .limitTimerOwner(getOwner().getTimerComponent())
                .entityId(String.valueOf(this.getEntityId()))
                .intervalMs(DbLimitConstants.ZONE_SIDE_DB_LIMIT_INTERVAL_MS)
                .build();
    }


    /**
     * 属性增量更新同步db
     */
    public void saveOnDestroy() {
        if (dbTaskProxy == null) {
            return;
        }
        if (!this.getOwner().isDestroy()) {
            WechatLog.error("ZoneSideDbComponent endDb {} is not destroy!", getOwner());
        }
        this.dbTaskProxy.saveDbAsync();
        this.dbTaskProxy.stop();
    }

    public void saveChangeToDb() {
        this.dbTaskProxy.update();
    }


    private static class ZoneSideChangeAttrUpsertStrategy extends ActorChangeAttrUpsertStrategy<Zoneside.ZoneSideEntity> {
        public ZoneSideChangeAttrUpsertStrategy(GeneratedMessageV3 msg) {
            super(msg);
        }

        @Override
        protected int getTriggerFullAttrSaveSize() {
            return 2 * 1024;
        }

        @Override
        protected boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder) {
            final SceneActor sceneActor = (SceneActor) actor;
            return sceneActor.getBigScene().getZoneEntity().getZoneSideProp().copyChangeToDb((Zoneside.ZoneSideEntity.Builder) changeAttrSaveDataBuilder) > 0;
        }

        @Override
        protected Zoneside.ZoneSideEntity buildFullAttrSaveData(AbstractActor actor) {
            final SceneActor sceneActor = (SceneActor) actor;
            return sceneActor.getBigScene().getZoneEntity().getZoneSideProp().getCopyDbBuilder().build();
        }

        @Override
        protected Zoneside.ZoneSideEntity buildFullAttrSaveData(UpdateResult<Message.Builder> result) {
            final TcaplusDb.ZoneTable.Builder recordBuilder = (TcaplusDb.ZoneTable.Builder) result.value;
            final ZoneSideProp prop = ZoneSideProp.of(recordBuilder.getZoneSideFullAttr(), recordBuilder.getZoneSideChangedAttr());
            return prop.getCopyDbBuilder().build();
        }

        @Override
        protected Message.Builder newDbSaveRequest(AbstractActor actor, Zoneside.ZoneSideEntity fullAttr, @NotNull Zoneside.ZoneSideEntity changeAttr) {
            TcaplusDb.ZoneTable.Builder request = TcaplusDb.ZoneTable.newBuilder()
                    .setZoneId(actor.getZoneId());
            if (fullAttr != null) {
                request.setZoneSideFullAttr(fullAttr);
            }
            request.setZoneSideChangedAttr(changeAttr);
            return request;
        }

    }
}

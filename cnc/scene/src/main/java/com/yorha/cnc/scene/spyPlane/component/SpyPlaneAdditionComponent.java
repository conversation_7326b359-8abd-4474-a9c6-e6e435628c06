package com.yorha.cnc.scene.spyPlane.component;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAdditionComponent;
import com.yorha.cnc.scene.spyPlane.SpyPlaneEntity;

public class SpyPlaneAdditionComponent extends SceneObjAdditionComponent {
    public SpyPlaneAdditionComponent(SceneObjEntity owner) {
        super(owner);
    }

    @Override
    public SpyPlaneEntity getOwner() {
        return (SpyPlaneEntity) super.getOwner();
    }

    @Override
    public AbstractScenePlayerEntity getScenePlayer() {
        return getOwner().getPlayer();
    }
}

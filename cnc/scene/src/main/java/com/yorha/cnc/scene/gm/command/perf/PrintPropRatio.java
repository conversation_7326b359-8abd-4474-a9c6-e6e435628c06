package com.yorha.cnc.scene.gm.command.perf;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class PrintPropRatio implements SceneGmCommand {

    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        actor.getScene().getObjMgrComponent().openPrintPropRatioByGm();
    }


    @Override
    public String showHelp() {
        return "PrintPropRatio";
    }

    @Override
    public CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_MONSTER;
    }
}

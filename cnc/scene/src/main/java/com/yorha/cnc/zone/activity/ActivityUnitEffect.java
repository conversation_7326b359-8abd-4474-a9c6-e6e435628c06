package com.yorha.cnc.zone.activity;

import com.yorha.proto.CommonEnum;

import java.util.Optional;

public class ActivityUnitEffect {

    /**
     * ActivityUnitType与ZoneActivityEffect的映射
     * 注：需要在scene上实现对应效果
     *
     * @param activityUnitType 活动unit类型
     * @return 活动效果 or Optional.empty()
     */
    public static Optional<CommonEnum.ZoneActivityEffect> getActivityEffect(CommonEnum.ActivityUnitType activityUnitType) {
        switch (activityUnitType) {
            case AUT_LAND_PROTECTION: {
                return Optional.of(CommonEnum.ZoneActivityEffect.ZAE_LAND_PROTECTION);
            }
            default: {
                return Optional.empty();
            }
        }

    }
}

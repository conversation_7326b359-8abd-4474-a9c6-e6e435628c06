package com.yorha.cnc.scene.sceneObj.move;

import com.yorha.cnc.scene.entity.component.PathFindMgrComponent;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.SsPathFinding;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SearchPathAsyncResult {
    private static final Logger LOGGER = LogManager.getLogger(SearchPathAsyncResult.class);

    int code;
    SsPathFinding.SearchPathAsyncAns ans;

    public SearchPathAsyncResult(SsPathFinding.SearchPathAsyncAns ans, Throwable err) {
        this.ans = ans;
        if (err != null) {
            Throwable cause = err.getCause();
            if (cause instanceof GeminiException) {
                code = (((GeminiException) cause).getCodeId());
            } else {
                code = ErrorCode.FAILED.getCodeId();
            }
        } else {
            code = ans.getCode();
        }
    }

    public int getCode() {
        return code;
    }

    /**
     * 异步寻路结果进行后处理 生成最终的moveData
     */
    public MoveData genMoveData(PathFindMgrComponent component, int searchTag, int fixDistance, Point end) {
        try {
            List<Point> path = new ArrayList<>();
            Map<Integer, Integer> crossingPart = new HashMap<>();
            int size = ans.getPathListCount();
            for (int i = 0; i < size; i++) {
                List<Struct.Point> pointList = ans.getPathList(i).getPointList();
                List<Point> list = new ArrayList<>();
                for (Struct.Point point : pointList) {
                    list.add(MsgHelper.transPoint(point));
                }
                if (GameLogicConstants.dynamicPathCorrect(searchTag)) {
                    list = component.correctPathByCityCollision(list);
                }
                if (i == size - 1 && fixDistance > 0) {
                    list = component.correctPathByMoveTarget(list, fixDistance, end);
                }
                path.addAll(list);
                if (ans.getPartCount() != 0 && i < size - 1) {
                    crossingPart.put(path.size() - 1, ans.getPart(i));
                }
            }
            if (path.size() < 2) {
                code = ErrorCode.MOVE_NO_PATH.getCodeId();
                return null;
            }
            return new MoveData(path, crossingPart.isEmpty() ? null : crossingPart, searchTag);
        } catch (Exception e) {
            code = ErrorCode.MOVE_NO_PATH.getCodeId();
            LOGGER.error("genMoveData error ans: {} ", ans, e);
            return null;
        }
    }
}

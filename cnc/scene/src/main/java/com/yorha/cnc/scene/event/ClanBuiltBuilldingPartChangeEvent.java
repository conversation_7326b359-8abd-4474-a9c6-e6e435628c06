package com.yorha.cnc.scene.event;

import com.yorha.cnc.scene.event.ievent.IEventWithClanId;

public class ClanBuiltBuilldingPartChangeEvent extends IEventWithClanId {
    final private int partId;
    final private boolean isOwned;

    public ClanBuiltBuilldingPartChangeEvent(long entityId, long clanId, int partId, boolean isOwned) {
        super(entityId, clanId);
        this.partId = partId;
        this.isOwned = isOwned;
    }

    public int getPartId() {
        return partId;
    }

    public boolean isOwned() {
        return this.isOwned;
    }
}

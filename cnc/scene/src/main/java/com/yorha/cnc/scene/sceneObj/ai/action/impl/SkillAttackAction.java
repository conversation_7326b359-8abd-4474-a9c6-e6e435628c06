package com.yorha.cnc.scene.sceneObj.ai.action.impl;

import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.ai.action.AbstractAIAction;
import com.yorha.cnc.scene.sceneObj.ai.event.AIEvent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjAiComponent;
import com.yorha.cnc.scene.sceneObj.component.SceneObjBattleComponent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.Collection;

import static com.yorha.proto.CommonEnum.AiParams.AP_ALERT_RANGE;
import static com.yorha.proto.CommonEnum.AiRecordType.ART_LAST_SKILL_TIME;

/**
 * 原地技能攻击
 * 进入条件： 仇恨列表不为空
 * 进入：设置战斗点位
 * 执行：对仇恨值最高，且可以攻击到的对象放技能
 * 结束：停止与当前攻击目标战斗
 * <AUTHOR>
 */
public class SkillAttackAction extends AbstractAIAction {

    public SkillAttackAction() {
        super();
    }


    @Override
    public boolean isSatisfied(SceneObjAiComponent component) {
        // 攻击状态要求当前仇恨列表不为空
        SceneObjEntity owner = component.getOwner();
        Object object = component.tryGetRecord(CommonEnum.AiRecordType.ART_TARGET);
        SceneObjEntity target;
        if (object != null) {
            long targetId = (long)object;
            target = owner.getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        } else {
            target = owner.getHateListComponent().mostHateEntity();

        }
        if (target == null) {
            if (component.isDebugAble()) {
                LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", component.getLogHead(), getActionName(), false, "target is null");
            }
            return false;
        }
        if (object == null) {
            if (outRange(owner, target)) {
                if (component.isDebugAble()) {
                    LOGGER.info("{}, action :{} isSatisfied：{} reason: {}", component.getLogHead(), getActionName(), false, target.getEntityId() + " out range");
                }
                return false;
            }
        }
        if (component.isDebugAble()) {
            LOGGER.info("{}, action :{} isSatisfied：{}", component.getLogHead(), getActionName(), true);
        }
        return true;
    }

    @Override
    public void onEnter(SceneObjAiComponent component) {
        super.onEnter(component);
        SceneObjEntity owner = component.getOwner();
        owner.getTransformComponent().enterBattle();
    }

    private boolean outRange(SceneObjEntity owner, SceneObjEntity target) {
        return Point.calDisBetweenTwoPoint(owner.getCurPoint(), target.getCurPoint()) > owner.getAiComponent().getAiParams().get(AP_ALERT_RANGE);
    }

    @Override
    protected void execute(SceneObjAiComponent component) {
        SceneObjEntity owner = component.getOwner();
        Object object = component.tryGetRecord(CommonEnum.AiRecordType.ART_TARGET);
        SceneObjEntity target = null;
        if (object != null) {
            long targetId = (long)object;
            target = owner.getScene().getObjMgrComponent().getSceneObjEntity(targetId);
        }
        if (target == null) {
            // 警戒圈加仇恨值为了转火
            if (owner.getHateListComponent().getHateEntities().isEmpty()) {
                component.alert(component.getAiParams().get(AP_ALERT_RANGE));
            }

            if (owner.getHateListComponent().getHateEntities().isEmpty()) {
                component.triggerEvent(AIEvent.LOSE_TARGET);
                return;
            }
            // 清除不在攻击范围内目标的仇恨值
            Collection<Long> needClearHateObjSet = new ArrayList<>();
            for (long objId : component.getOwner().getHateListComponent().getHateEntities()) {
                SceneObjEntity obj = owner.getScene().getObjMgrComponent().getSceneObjEntity(objId);
                if (obj == null || obj.isDestroy() || outRange(owner, obj)) {
                    needClearHateObjSet.add(objId);
                }
            }
            for (long needClearHateObj : needClearHateObjSet) {
                owner.getHateListComponent().clearHate(needClearHateObj);
            }

            target = component.getOwner().getHateListComponent().mostHateEntity();
        }
        if (target == null) {
            return;
        }
        // 放技能
        Object record = component.tryGetRecord(ART_LAST_SKILL_TIME);
        if (record != null) {
            long lastSkillTime = (long) record;
            if ((SystemClock.now() - lastSkillTime) < component.getAiParams().get(CommonEnum.AiParams.AP_SKILL_INTERVAL)) {
                return;
            }
        }
        int skillId = Math.toIntExact(component.getAiParams().get(CommonEnum.AiParams.AP_SKILL));
        SceneObjBattleComponent battleComponent = owner.getBattleComponent();
        if (!battleComponent.isInBattle()) {
            battleComponent.tryStartBattleBySkill(target);
        }
        BattleHero mainHero = battleComponent.getMainHero();
        if (mainHero == null) {
            return;
        }
        owner.getEventDispatcher().dispatch(new FireSkillEvent.Builder().setHeroId(mainHero.getId()).setSkillId(skillId).setTargetId(target.getEntityId()).build());
        component.addRecord(ART_LAST_SKILL_TIME, SystemClock.now());
        if (component.isDebugAble()) {
            LOGGER.info("{}, fire and execute :{} target:{} ", component.getLogHead(), getActionName(), target);
        }
    }

    @Override
    protected String getActionName() {
        return "SkillAttackAction";
    }

    @Override
    public void onEnd(SceneObjAiComponent component) {
        super.onEnd(component);
        SceneObjEntity owner = component.getOwner();
        component.removeRecord(ART_LAST_SKILL_TIME);
        owner.getHateListComponent().clear();
    }
}

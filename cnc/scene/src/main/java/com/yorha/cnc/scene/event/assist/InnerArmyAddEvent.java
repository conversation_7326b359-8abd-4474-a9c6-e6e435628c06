package com.yorha.cnc.scene.event.assist;

import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.common.utils.eventdispatcher.IEvent;

/**
 * 城内部队增加   (莫随意增减触发点)
 *
 * <AUTHOR>
 */
public class InnerArmyAddEvent extends IEvent {
    private final ArmyEntity armyEntity;

    public InnerArmyAddEvent(ArmyEntity armyEntity) {
        this.armyEntity = armyEntity;
    }

    public ArmyEntity getArmyEntity() {
        return armyEntity;
    }
}

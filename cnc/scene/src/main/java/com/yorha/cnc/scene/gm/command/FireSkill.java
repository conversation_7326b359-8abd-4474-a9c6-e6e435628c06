package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 所有行军放一个技能
 *
 * <AUTHOR>
 */
public class FireSkill implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int heroId = Integer.parseInt(args.get("heroId"));
        int skillId = Integer.parseInt(args.get("skillId"));

        Circle circle = null;
        if (args.containsKey("x")){
            Point point = Point.valueOf(Integer.parseInt(args.get("x")), Integer.parseInt(args.get("y")));
            int range = Integer.parseInt(args.get("range"));
            circle = Circle.valueOf(point, range);
        }
        AbstractScenePlayerEntity scenePlayer = actor.getScene().getPlayerMgrComponent().getScenePlayer(playerId);
        for (ArmyEntity armyEntity : scenePlayer.getArmyMgrComponent().getMyArmyList()) {
            FireSkillEvent.Builder builder = new FireSkillEvent.Builder().setHeroId(heroId).setSkillId(skillId);
            if (circle != null){
                Set<Long> aoiBattleRoles = armyEntity.getBattleComponent().getBattleRole().getGround().getAdapter().getAoiBattleRoles(circle).stream().map(it -> it.getAdapter().getRoleId()).collect(Collectors.toSet());
                builder.setSelectTargets(aoiBattleRoles);
            }
            armyEntity.getEventDispatcher().dispatch(builder.build());
        }
    }

    @Override
    public String showHelp() {
        return "FireSkill heroId={value} skillId={value} x={} y={} range={}";
    }

}

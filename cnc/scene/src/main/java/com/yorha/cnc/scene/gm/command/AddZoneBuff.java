package com.yorha.cnc.scene.gm.command;

import com.yorha.cnc.scene.SceneActor;
import com.yorha.cnc.scene.gm.SceneGmCommand;
import com.yorha.common.buff.DevBuffMgrBase;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import res.template.BuffTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/10
 */
public class AddZoneBuff implements SceneGmCommand {
    @Override
    public void execute(SceneActor actor, long playerId, Map<String, String> args) {
        int buffId = Integer.parseInt(args.get("buffId"));
        BuffTemplate buffTemplate = DevBuffMgrBase.getBuffTemplate(buffId);
        if (buffTemplate != null) {
            long endTime;
            if (args.get("seconds") != null) {
                endTime = SystemClock.now() + Long.parseLong(args.get("seconds")) * 1000;
                actor.getBigScene().getZoneEntity().getDevBuffComponent().addDevBuff(buffId, endTime, CommonEnum.DevBuffSourceType.DBST_GM);
            } else {
                actor.getBigScene().getZoneEntity().getDevBuffComponent().addDevBuff(buffId, CommonEnum.DevBuffSourceType.DBST_GM);
            }
        }
    }
}

package com.yorha.cnc.scene.resBuilding.component;

import com.yorha.cnc.scene.abstractsceneplayer.addition.SceneAddCalc;
import com.yorha.cnc.scene.abstractsceneplayer.component.AbstractScenePlayerWarningComponent;
import com.yorha.cnc.scene.army.ArmyEntity;
import com.yorha.cnc.scene.entity.component.ObjMgrComponent;
import com.yorha.cnc.scene.event.ievent.IEventWithEntityId;
import com.yorha.cnc.scene.event.player.ClanChangeEvent;
import com.yorha.cnc.scene.event.player.ClanSimpleNameChangeEvent;
import com.yorha.cnc.scene.event.player.PlayerNameChangeEvent;
import com.yorha.cnc.scene.resBuilding.ResBuildingEntity;
import com.yorha.cnc.scene.sceneObj.component.SceneObjComponent;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.game.gen.prop.ResBuildingProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ArmyDetailState;
import com.yorha.proto.CommonEnum.ResourceBuildingState;
import com.yorha.proto.CommonEnum.WarningType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 采集逻辑
 *
 * <AUTHOR>
 */
public class ResBuildingCollectComponent extends SceneObjComponent<ResBuildingEntity> {
    private static final Logger LOGGER = LogManager.getLogger(ResBuildingCollectComponent.class);
    /**
     * 采集结束timer
     */
    private boolean hasTask = false;
    /**
     * 在路上的行军
     */
    private final Map<Long, Boolean> collectArmy = new HashMap<>();
    /**
     * 缓存的速度
     */
    private double speed;

    private EventListener eventListener;

    public ResBuildingCollectComponent(ResBuildingEntity owner) {
        super(owner);
    }

    @Override
    public void afterAllLoad() {
        ResourceBuildingState state = getOwner().getProp().getState();
        // 发现在阻挡里
        if (!getOwner().getScene().getCityMoveComponent().isPointNavMovable(getOwner().getCurPoint())) {
            LOGGER.info("restore but in static collision {}", getOwner());
            if (state == ResourceBuildingState.RBS_IDLE) {
                getOwner().recycle(true);
                return;
            }
            ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, getProp().getCollect().getArmyId());
            if (army == null) {
                getOwner().recycle(true);
                return;
            }
            speed = getCollectSpeed(army);
            calResourceWhenStop(army, "cancel_collect");
            army.getCollectComponent().collectEndAndReturn(null);
            getOwner().recycle(true);
            return;
        }
        if (state == ResourceBuildingState.RBS_IDLE) {
            return;
        }
        ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, getProp().getCollect().getArmyId());
        if (army == null) {
            getOwner().getProp().setState(ResourceBuildingState.RBS_IDLE);
            getProp().getCollect().setArmyId(0).setPlayerId(0).setPlayerName("").setClanSimpleName("");
            return;
        }
        speed = getCollectSpeed(army);
        long now = SystemClock.now();
        // 战斗中
        if (state == ResourceBuildingState.RBS_BATTLE) {
            getOwner().getProp().setState(state);
            Pair<Long, Long> result = calCollectSecond(army);
            long second = result.getFirst();
            // 就拿上次的吧..
            long newEndTsMs = getProp().getCollect().getEndTsMs() + TimeUtils.second2Ms(second);
            if (newEndTsMs <= now) {
                time2CollectFinish(newEndTsMs);
            } else {
                getProp().getCollect().setEndTsMs(newEndTsMs).setEstimateNum(result.getSecond());
                addTimer(newEndTsMs - now, TimeUnit.MILLISECONDS);
                army.getStatusComponent().setDetailState(ArmyDetailState.ADS_COLLECT, getOwner(), newEndTsMs, result.getSecond());
                eventListener = army.getEventDispatcher().addMultiEventListenerRepeat(this::onCollectorAttrChange, PlayerNameChangeEvent.class, ClanSimpleNameChangeEvent.class, ClanChangeEvent.class);
            }
            return;
        }
        // 采集中
        if (state == ResourceBuildingState.RBS_COLLECTING) {
            long restTime = getProp().getCollect().getEndTsMs() - now;
            if (restTime <= 0) {
                time2CollectFinish(getProp().getCollect().getEndTsMs());
                return;
            }
            addTimer(restTime, TimeUnit.MILLISECONDS);
            eventListener = army.getEventDispatcher().addMultiEventListenerRepeat(this::onCollectorAttrChange, PlayerNameChangeEvent.class, ClanSimpleNameChangeEvent.class, ClanChangeEvent.class);
        }
    }

    /**
     * 获取采集时间
     */
    private Pair<Long, Long> calCollectSecond(ArmyEntity army) {
        speed = getCollectSpeed(army);
        // 剩余负重产生的可采集量
        long restBurdenToRes = army.getResourceComponent().getRestBurdenToRes(getOwner().getTemplate().getResType());
        int curNum = getOwner().getProp().getCurNum();
        if (curNum <= 0 || restBurdenToRes <= 0) {
            return Pair.of(0L, 0L);
        }
        if (restBurdenToRes < curNum) {
            getProp().getCollect().setIsCollectOut(false);
            long second1 = (long) (restBurdenToRes / speed) + 1;
            return Pair.of(second1, restBurdenToRes);
        }
        getProp().getCollect().setIsCollectOut(true);
        long second2 = (long) (curNum / speed) + 1;
        return Pair.of(second2, (long) curNum);
    }

    /**
     * 获取采集速度
     */
    private double getCollectSpeed(ArmyEntity army) {
        float base = getOwner().getTemplate().getResSpeed();
        // 金条也要受资源采集加成
        return SceneAddCalc.getCollectSpeed(army, getOwner().getTemplate().getResType(), base, getOwner().getProp().getClanId());
    }

    public void addCollect(ArmyEntity army, boolean isBattle) {
        collectArmy.put(army.getEntityId(), isBattle);
        if (!isBattle) {
            return;
        }
        ArmyEntity curCollectArmy = getCurCollectArmy();
        if (curCollectArmy == null) {
            return;
        }
        curCollectArmy.getScenePlayer().getWarningComponent().addWarningItem(army, WarningType.WT_Attack);
    }

    private void removeCollect(long armyId) {
        if (!collectArmy.containsKey(armyId)) {
            return;
        }
        boolean isBattle = collectArmy.get(armyId);
        collectArmy.remove(armyId);
        if (!isBattle) {
            return;
        }
        ArmyEntity curCollectArmy = getCurCollectArmy();
        if (curCollectArmy == null) {
            return;
        }
        curCollectArmy.getScenePlayer().getWarningComponent().removeWarningItem(armyId);
    }

    private ArmyEntity getCurCollectArmy() {
        if (getProp().getCollect().getArmyId() == 0) {
            return null;
        }
        return getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, getProp().getCollect().getArmyId());
    }

    /**
     * 到达资源田 尝试开始采集
     */
    public boolean onArriveCollect(ArmyEntity army) {
        removeCollect(army.getEntityId());
        // 到达时已经被别人先进了
        if (getProp().getState() != ResourceBuildingState.RBS_IDLE) {
            LOGGER.info("{} try collect. but another is in : {}", army, getOwner());
            return false;
        }
        return tryEnterCollect(army);
    }

    /**
     * 尝试开始采集
     */
    private boolean tryEnterCollect(ArmyEntity army) {
        Pair<Long, Long> result = calCollectSecond(army);
        long second = result.getFirst();
        if (second <= 0) {
            LOGGER.info("{} try tryEnterCollect. but resNum is 0 : {} burden:{}", army, getOwner(), result.getSecond());
            return false;
        }
        // 进入采集时结束掉所有战场
        army.getBattleComponent().forceEndAllBattle();
        long endTsMs = SystemClock.now() + TimeUtils.second2Ms(second);
        getProp().setState(ResourceBuildingState.RBS_COLLECTING).getCollect()
                .setArmyId(army.getEntityId())
                .setPlayerId(army.getPlayerId())
                .setPlayerName(army.getScenePlayer().getName())
                .setPlayerCamp(army.getScenePlayer().getCampEnum())
                .setClanId(army.getClanId())
                .setClanSimpleName(army.getClanBriefName())
                .setZoneId(army.getZoneId())
                .setStartTsMs(SystemClock.now())
                .setEndTsMs(endTsMs)
                .setEstimateNum(result.getSecond());
        addTimer(second, TimeUnit.SECONDS);
        army.getStatusComponent().setDetailState(ArmyDetailState.ADS_COLLECT, getOwner(), endTsMs, result.getSecond());
        army.getTransformComponent().changePoint(getOwner().getCurPoint().getDeepCopy());

        if (eventListener != null) {
            eventListener.cancel();
        }
        eventListener = army.getEventDispatcher().addMultiEventListenerRepeat(this::onCollectorAttrChange, PlayerNameChangeEvent.class, ClanSimpleNameChangeEvent.class, ClanChangeEvent.class);
        LOGGER.info("{} tryEnterCollect ok {}", getOwner(), army);
        // 尝试存盘
        if (getOwner().getScene().isMainScene()) {
            getOwner().getDbComponent().tryInsertDb();
        }
        // 遍历当前的增加预警
        addWarningItem(army);
        return true;
    }

    private void addWarningItem(ArmyEntity army) {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        try {
            for (Map.Entry<Long, Boolean> entry : collectArmy.entrySet()) {
                if (!entry.getValue()) {
                    continue;
                }
                ArmyEntity other = objMgrComponent.getSceneObjWithType(ArmyEntity.class, entry.getKey());
                if (other == null) {
                    continue;
                }
                if (other.getPlayerId() == army.getPlayerId()) {
                    continue;
                }
                // 同盟过
                if (other.getClanId() != 0 && other.getClanId() == getOwner().getClanId()) {
                    continue;
                }
                final AbstractScenePlayerWarningComponent playerWarningComponent = army.getScenePlayer().getWarningComponent();
                if (playerWarningComponent == null) {
                    LOGGER.info("ResBuildingCollectComponent addWarningItem no warningComponent");
                    continue;
                }
                playerWarningComponent.addWarningItem(other, WarningType.WT_Attack);
            }
        } catch (Exception e) {
            LOGGER.error("addWarningItem error", e);
        }
    }

    private void removeWarningItem(ArmyEntity army) {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        try {
            for (Map.Entry<Long, Boolean> entry : collectArmy.entrySet()) {
                if (!entry.getValue()) {
                    continue;
                }
                final AbstractScenePlayerWarningComponent playerWarningComponent = army.getScenePlayer().getWarningComponent();
                if (playerWarningComponent == null) {
                    LOGGER.info("ResBuildingCollectComponent removeWarningItem no warningComponent");
                    continue;
                }
                playerWarningComponent.removeWarningItem(entry.getKey());
            }
        } catch (Exception e) {
            LOGGER.error("addWarningItem error", e);
        }
    }

    /**
     * 开始战斗
     */
    public void startBattle(ArmyEntity army) {
        // 结算下资源  timer取消掉
        calResourceWhenStop(army, "war_pause_collect");
        getProp().setState(ResourceBuildingState.RBS_BATTLE);
    }

    /**
     * 攻打者攻击成功
     */
    public boolean onAttackSucceed(ArmyEntity army) {
        removeCollect(army.getEntityId());
        // 已经在采集状态  被别人先进去了
        if (getProp().getState() == ResourceBuildingState.RBS_COLLECTING) {
            return false;
        }
        if (!army.getScenePlayer().checkResourceUnlock(getOwner().getTemplate().getResType().getNumber())) {
            return false;
        }
        long oldArmyId = getProp().getCollect().getArmyId();
        boolean isOk = tryEnterCollect(army);
        // 老的被换下了  说明老的挂了
        if (isOk && oldArmyId != 0) {
            ArmyEntity oldArmy = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, oldArmyId);
            if (oldArmy == null || oldArmy.getProp().getAttachId() != getEntityId()) {
                return true;
            }
            Point point = getOwner().getTransformComponent().getLeavePosition(oldArmy, oldArmy.getMainCityPoint());
            removeWarningItem(oldArmy);
            oldArmy.getCollectComponent().afterLeaveCollect(point);
        }
        return isOk;
    }

    /**
     * 战斗结束 看能不能继续采集
     */
    public boolean endBattle(ArmyEntity army) {
        // 都被别人进去了
        if (getProp().getCollect().getArmyId() != army.getEntityId()) {
            return false;
        }
        Pair<Long, Long> result = calCollectSecond(army);
        long second = result.getFirst();
        if (second <= 0) {
            clearCollectorData();
            removeWarningItem(army);
            LOGGER.info("{} endBattle try collect. but resNum is 0 : {}", army, getOwner());
            return false;
        }
        getProp().setState(ResourceBuildingState.RBS_COLLECTING);
        getProp().getCollect().setEstimateNum(result.getSecond())
                .setEndTsMs(SystemClock.now() + TimeUtils.second2Ms(second)).setStartTsMs(SystemClock.now());
        addTimer(second, TimeUnit.SECONDS);
        army.getStatusComponent().setDetailState(ArmyDetailState.ADS_COLLECT, getOwner(), getProp().getCollect().getEndTsMs(), result.getSecond());
        return true;
    }

    /**
     * 主动离开
     */
    public Point leaveCollect(ArmyEntity army, Point targetPos) {
        // 在路上 移除一下就行了
        if (collectArmy.containsKey(army.getEntityId())) {
            removeCollect(army.getEntityId());
            return null;
        }
        if (getProp().getCollect().getArmyId() != army.getEntityId()) {
            // 攻打资源田成功，想进去发现负重满了，执行退出流程时发现资源田的id不是自己
            LOGGER.info("{} leaveCollect {}", getOwner(), army);
            return null;
        }
        // 正在采集中才要结算  战斗不用
        if (getProp().getState() == ResourceBuildingState.RBS_COLLECTING) {
            calResourceWhenStop(army, "cancel_collect");
        }
        // 清理数据
        clearCollectorData();
        removeWarningItem(army);
        if (targetPos == null) {
            return null;
        }
        return getOwner().getTransformComponent().getLeavePosition(army, targetPos);
    }

    private void clearCollectorData() {
        getProp().setState(ResourceBuildingState.RBS_IDLE).getCollect()
                .setArmyId(0).setPlayerId(0).setClanId(0).setPlayerName("")
                .setClanSimpleName("").setPlayerCamp(CommonEnum.Camp.C_NONE);
        if (eventListener == null) {
            return;
        }
        eventListener.cancel();
        eventListener = null;
    }

    public void gmCollectFinish() {
        getOwner().getTimerComponent().cancelTimer(TimerReasonType.RES_BUILDING_COLLECT_FINISH);
        time2CollectFinish(0);
    }

    /**
     * 采集结束  采完/采满
     */
    private void time2CollectFinish(long tsMs) {
        LOGGER.info("{} time2CollectFinish", getOwner());
        hasTask = false;
        if (tsMs == 0) {
            tsMs = SystemClock.now();
        }
        ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, getProp().getCollect().getArmyId());
        if (army == null) {
            clearCollectorData();
            return;
        }
        int curNum = getProp().getCurNum();
        if (getProp().getCollect().getIsCollectOut()) {
            collectOut(army, tsMs);
            return;
        }
        // 剩余负重产生的可采集量
        long restBurdenToRes = army.getResourceComponent().getRestBurdenToRes(getOwner().getTemplate().getResType());
        if (curNum <= restBurdenToRes) {
            // 脱战奶了一口  负重上升  刚好又超过当前资源量
            LOGGER.warn("{} {} time2CollectFinish warning. curNum:{} restBurden:{}", getOwner(), army, curNum, restBurdenToRes);
            collectOut(army, tsMs);
            return;
        }
        getProp().setCurNum((int) (curNum - restBurdenToRes));
        army.getResourceComponent().onCalCollectEnd(getOwner(), getOwner().getTemplate().getResType(), restBurdenToRes, tsMs);
        army.getCollectComponent().collectEndAndReturn(getOwner());
        clearCollectorData();
        removeWarningItem(army);
        if (getProp().getCurNum() <= 0) {
            army.getCollectComponent().sendCollectQlog("collect_all", getOwner(), getProp().getCurNum(), restBurdenToRes);
            return;
        }
        army.getCollectComponent().sendCollectQlog("collect_full", getOwner(), getProp().getCurNum(), restBurdenToRes);
    }

    private void collectOut(ArmyEntity army, long tsMs) {
        LOGGER.info("{} onCollectOut", getOwner());
        int curNum = getProp().getCurNum();
        getProp().setCurNum(0);
        army.getResourceComponent().onCalCollectEnd(getOwner(), getOwner().getTemplate().getResType(), curNum, tsMs);
        army.getResourceComponent().addOutTimes();
        army.getCollectComponent().collectEndAndReturn(getOwner());
        army.getCollectComponent().collectEndReward();
        removeWarningItem(army);
        getOwner().recycle(true);
        army.getCollectComponent().sendCollectQlog("collect_all", getOwner(), getProp().getCurNum(), curNum);
    }

    /**
     * 给部队结算采集资源
     */
    private void calResourceWhenStop(ArmyEntity army, String reason) {
        if (getOwner().getProp().getState() != ResourceBuildingState.RBS_COLLECTING) {
            return;
        }
        if (hasTask) {
            getOwner().getTimerComponent().cancelTimer(TimerReasonType.RES_BUILDING_COLLECT_FINISH);
            hasTask = false;
        }
        long enterStateTs = getProp().getCollect().getStartTsMs();
        long num = (long) (speed * TimeUtils.ms2Second((SystemClock.now() - enterStateTs)));
        // 剩余负重产生的可采集量
        long restBurdenToRes = army.getResourceComponent().getRestBurdenToRes(getOwner().getTemplate().getResType());
        if (restBurdenToRes < num) {
            // 如果是在田里战斗死亡的 不会进来  所以只会是强制溃败触发的死亡
            if (army.getProp().getArmyState() == CommonEnum.ArmyState.AS_Retreating) {
                LOGGER.info("{} {} calResource failed. num:{} restBurden:{}", getOwner(), army, num, restBurdenToRes);
            } else {
                LOGGER.warn("{} {} calResource failed. num:{} restBurden:{}", getOwner(), army, num, restBurdenToRes);
            }
            num = restBurdenToRes;
        }
        int curNum = getOwner().getProp().getCurNum();
        if (curNum < num) {
            LOGGER.warn("{} {} calResource failed. num:{} curNum:{}", getOwner(), army, num, curNum);
            num = curNum;
        }
        if (num <= 0) {
            return;
        }
        getOwner().getProp().setCurNum((int) (curNum - num));
        army.getResourceComponent().onCalCollectEnd(getOwner(), getOwner().getTemplate().getResType(), num, SystemClock.now());
        army.getCollectComponent().sendCollectQlog(reason, getOwner(), getProp().getCurNum(), num);
        LOGGER.info("calResourceWhenStop {} {} {} {}", getOwner(), army, num, reason);
    }


    private void addTimer(long waitTime, TimeUnit unit) {
        getOwner().getTimerComponent().addTimer(TimerReasonType.RES_BUILDING_COLLECT_FINISH,
                () -> time2CollectFinish(0),
                waitTime, unit);
        hasTask = true;
        LOGGER.info("{} add resource collect timer isOut: {}, waitTime: {} unit: {} speed: {}", getOwner(), getProp().getCollect().getIsCollectOut(), waitTime, unit, speed);
    }

    /**
     * 是否没有人在用
     */
    public boolean noOneCollect() {
        return getProp().getCollect().getPlayerId() == 0 && collectArmy.isEmpty();
    }

    /**
     * 此人是否有部队来采集
     */
    public boolean inInOrMoving(long playerId) {
        if (getProp().getCollect().getPlayerId() == playerId) {
            return true;
        }
        ObjMgrComponent objMgrComponent = getOwner().getScene().getObjMgrComponent();
        for (Long armyId : collectArmy.keySet()) {
            ArmyEntity army = objMgrComponent.getSceneObjWithType(ArmyEntity.class, armyId);
            if (army != null && army.getPlayerId() == playerId) {
                return true;
            }
        }
        return false;
    }

    private void onCollectorAttrChange(IEvent e) {
        IEventWithEntityId event = (IEventWithEntityId) e;
        if (event.getEntityId() != getProp().getCollect().getArmyId()) {
            return;
        }
        ArmyEntity army = getOwner().getScene().getObjMgrComponent().getSceneObjWithType(ArmyEntity.class, event.getEntityId());
        getProp().getCollect().setClanSimpleName(army.getClanBriefName()).setClanId(army.getClanId()).setPlayerName(army.getProp().getCardHead().getName());
    }

    private ResBuildingProp getProp() {
        return getOwner().getProp();
    }
}

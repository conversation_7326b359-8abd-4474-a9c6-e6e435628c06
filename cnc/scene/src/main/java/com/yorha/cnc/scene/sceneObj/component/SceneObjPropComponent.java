package com.yorha.cnc.scene.sceneObj.component;

import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjPropChangeListener;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Entity;
import com.yorha.proto.Entity.EntityNtfMsg;
import com.yorha.proto.Entity.SceneObjBriefAttr;
import com.yorha.proto.EntityAttrOuterClass.EntityAttr;

/**
 * <AUTHOR>
 */
public class SceneObjPropComponent extends SceneObjComponent<SceneObjEntity> {

    /**
     * 为了让mod和new的数据成对，如果没有这个full，会导致mod需要千人千面
     * 禁止直接使用，必须用getFullAttr接口获取
     */
    private EntityAttr.Builder cachedFullCs = null;
    private int sceneObjVersion = 0;

    public SceneObjPropComponent(SceneObjEntity owner) {
        super(owner);
    }

    public void initPropListener(boolean isRestore) {
        if (isRestore) {
            getOwner().getDbComponent().beginSaveChangeToDb(true);
        }
        if (getOwner().getProp().hasAnyMark()) {
            getOwner().getDbComponent().saveChangeToDb();
            getOwner().getProp().unMarkAll();
        }
        initCachedFullCs();
        getOwner().getProp().setListener(new SceneObjPropChangeListener(getOwner().getScene(), getEntityId(), this::onPropChangeListener, getOwner().ownerActor().self()));
    }

    protected void initCachedFullCs() {
        if (cachedFullCs != null) {
            WechatLog.error("SceneObjPropComponent initCachedFullCs but cachedFullCs not null {}", getOwner());
        }
        cachedFullCs = EntityAttr.newBuilder();
        cachedFullCs.setEntityId(getEntityId());
        cachedFullCs.setEntityType(getEntityType());
        getOwner().fullCsEntityAttr(cachedFullCs);
    }

    /**
     * 立刻触发属性更新  下发change和存盘
     * 目前只有登录前城池落地有需求  其他谨慎使用!
     */
    public void immediatelyFlushProp() {
        onPropChangeListener();
    }

    /**
     * 属性系统变更的回调信息
     */
    protected void onPropChangeListener() {
        if (getOwner().isDestroy()) {
            return;
        }
        if (!getOwner().getProp().hasAnyMark()) {
            return;
        }
        ntfChangeToClient(true);
        getOwner().getDbComponent().saveChangeToDb();
        getOwner().getProp().unMarkAll();
    }

    public Entity.SceneObjBriefAttr genBriefMsg() {
        SceneObjBriefAttr.Builder builder = SceneObjBriefAttr.newBuilder();
        builder.setEntityType(getOwner().getEntityType()).setEntityId(getOwner().getEntityId());
        boolean ret = getOwner().briefEntityAttr(builder);
        if (!ret) {
            return null;
        }
        return builder.build();
    }

    /**
     * 简要数据变化 重新build并即时下发
     */
    public void briefCsMsgChange() {
        if (!getOwner().getScene().isMainScene()) {
            return;
        }
        // 简要数据变化 下发
        getOwner().getAoiNodeComponent().onBriefChange(getOwner().getScene().getMaxSceneLayer(getOwner()), getOwner().getScene().getMaxSceneLayer(getOwner()));
    }

    /**
     * 获取全量的prop消息
     */
    public EntityNtfMsg getEntityNtfMsg(SceneObjectNtfReason reason) {
        return EntityNtfMsg.newBuilder()
                .setReason(reason)
                .setZoneId(getOwner().getScene().getZoneId())
                .addNewEntities(getFullAttr())
                .build();
    }

    public EntityAttr.Builder getFullAttr() {
        if (cachedFullCs == null) {
            // 不应该会空，但是保底一下
            initCachedFullCs();
            WechatLog.error("SceneObjPropComponent getFullAttr but cachedFullCs is null {}", getOwner());
        }
        return cachedFullCs;
    }

    /**
     * 属性系统propListener导致
     * destroy前强制下发变化
     */
    public void ntfChangeToClient(boolean needMerge) {
        sceneObjVersion++;

        // 把full更新到最新
        int changedCsFieldCount = getOwner().changedCsAndClearDelKeyEntityAttr(getFullAttr());
        if (changedCsFieldCount == 0) {
            return;
        }
        EntityAttr.Builder cachedDeltaCs = EntityAttr.newBuilder();
        cachedDeltaCs.setEntityId(getEntityId()).setEntityType(getEntityType());
        // 确实有变化，把脏通知给所有关注我的人
        getOwner().changedCsEntityAttr(cachedDeltaCs);
        getOwner().getAoiNodeComponent().broadcastEntityModNtf(cachedDeltaCs, needMerge);
    }

    public int getVersion() {
        return sceneObjVersion;
    }
}

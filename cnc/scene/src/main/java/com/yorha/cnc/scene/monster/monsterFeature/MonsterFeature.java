package com.yorha.cnc.scene.monster.monsterFeature;

import com.yorha.cnc.scene.abstractsceneplayer.AbstractScenePlayerEntity;
import com.yorha.cnc.scene.entity.SceneEntity;
import com.yorha.cnc.scene.monster.MonsterEntity;
import com.yorha.cnc.scene.sceneObj.SceneObjEntity;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;
import res.template.MonsterTemplate;

/**
 * 野怪特性
 * 特殊野怪的特性请通过实现本接口实现不要写在monsterEntity中
 *
 * <AUTHOR>
 */
public interface MonsterFeature {

    default boolean isRecordFirstKill() {
        return true;
    }

    /**
     * 场景物体类型 无极缩放使用
     */
    default CommonEnum.SceneObjectEnum getSceneObjType(MonsterTemplate template) {
        if (template.getQuality() == CommonEnum.SceneObjQuality.ELITE) {
            return CommonEnum.SceneObjectEnum.SOE_MONSTER_ELITE;
        }
        return CommonEnum.SceneObjectEnum.SOE_MONSTER;
    }

    /**
     * 删除时特殊处理
     */
    void onDeleteObj(MonsterEntity monsterEntity, SceneEntity sceneEntity);

    /**
     * 能否被sceneObj攻击的特殊判断
     */
    ErrorCode canBeAttackBySceneObj(MonsterEntity monsterEntity, SceneObjEntity attackerObj, boolean needCheckSiegeLimit);

    /**
     * 能否被玩家攻击的特殊判断
     */
    ErrorCode canBeAttackByScenePlayer(MonsterEntity monsterEntity, AbstractScenePlayerEntity attackerPlayer, boolean needCheckSiegeLimit);

    /**
     * 死亡时特殊处理
     */
    void onDead(MonsterEntity monsterEntity);

    void sendKillAndRewardAll(MonsterEntity monsterEntity);

    default ErrorCode canBattle(MonsterEntity monsterEntity, SceneObjEntity target, boolean needCheckSiegeLimit) {
        return defaultCanBattle(monsterEntity, target);
    }

    /**
     * 能否进攻另外一个battle
     */
    default ErrorCode defaultCanBattle(MonsterEntity monsterEntity, SceneObjEntity target) {
        // 无阵营或者中立时，只能攻击行军部队
        final boolean noCamp = monsterEntity.getCampEnum() == CommonEnum.Camp.C_NONE;
        final boolean neutralCamp = monsterEntity.getCampEnum() == CommonEnum.Camp.C_NEUTRAL;
        final boolean noOrNeutral = noCamp || neutralCamp;
        if ((target.getEntityType() != EntityAttrOuterClass.EntityType.ET_Army) && noOrNeutral) {
            return ErrorCode.BATTLE_CANT;
        }
        return ErrorCode.OK;
    }
}

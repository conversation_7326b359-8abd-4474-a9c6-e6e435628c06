package com.yorha.cnc.scene.collision;

import com.yorha.cnc.scene.entity.collision.GridCollisionWorld;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class GridCollisionTest {

    private static GridCollisionWorld collisionWorld;

    @BeforeAll
    public static void init() {
        collisionWorld = new GridCollisionWorld(50000, 50000, 5000);
    }

    @Test
    public void testCollision() {
        collisionWorld.add(1L, Circle.valueOf(5300, 4000, 300));
        // 无碰撞
        Assertions.assertFalse(collisionWorld.isInCollision(Point.valueOf(4999, 4000)));
        Assertions.assertFalse(collisionWorld.isInCollision(Point.valueOf(5601, 4000)));
        Assertions.assertFalse(collisionWorld.isInCollision(Point.valueOf(5300, 4301)));

        // 有碰撞
        Assertions.assertTrue(collisionWorld.isInCollision(Circle.valueOf(5200, 4200, 300)));
    }
}
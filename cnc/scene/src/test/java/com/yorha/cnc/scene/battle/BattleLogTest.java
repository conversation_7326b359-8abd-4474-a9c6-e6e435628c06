package com.yorha.cnc.scene.battle;//package com.yorha.game.battle;
//
//import com.github.luben.zstd.Zstd;
//import com.yorha.common.enums.battle.BattleUnitType;
//import com.yorha.common.enums.reason.SoldierNumChangeReason;
//import com.yorha.game.mapservice.scene.battle.BattleRole;
//import com.yorha.game.mapservice.scene.battle.context.RoundContext;
//import com.yorha.game.mapservice.scene.battle.record.RoundRoleAction;
//import com.yorha.game.mapservice.scene.battle.record.EffectContext;
//import com.yorha.game.mapservice.scene.battle.record.action.BuffAction;
//import com.yorha.game.mapservice.scene.battle.record.action.SkillAction;
//import com.yorha.game.mapservice.scene.battle.record.action.TroopAction;
//import com.yorha.game.mapservice.scene.battle.skill.effect.value.buf.lifecycle.RoundLifeCycle;
//import com.yorha.game.mapservice.scene.battle.soldier.SoldierLossData;
//import com.yorha.gemini.utils.StringUtils;
//import com.yorha.gemini.utils.ZipUtils;
//import com.yorha.gemini.utils.time.GeminiStopWatch;
//import com.yorha.proto.CommonBattle;
//import com.yorha.proto.CommonEnum;
////import net.jpountz.lz4.LZ4Compressor;
////import net.jpountz.lz4.LZ4Factory;
////import net.jpountz.lz4.LZ4FastDecompressor;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.junit.jupiter.api.BeforeAll;
//import org.junit.jupiter.api.Test;
//
//import java.io.IOException;
//import java.math.BigDecimal;
//import java.nio.ByteBuffer;
//import java.nio.ByteOrder;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
//public class BattleLogTest {
//    private static Logger logger = LogManager.getLogger(BattleLogTest.class);
//    static int SAVE_LOG_PER_ROUND = 100;
//    static int roundNum = 500;
//    static List<RoundContext> roundContextList = new ArrayList<>();
////    static LZ4Compressor compressor = LZ4Factory.fastestInstance().fastCompressor();
////    static LZ4FastDecompressor  deCompressor = LZ4Factory.fastestInstance().fastDecompressor();
//
//    @BeforeAll
//    public static void init() {
//        BattleRole blueRole = new BattleRole(null, null, CommonEnum.SceneObjType.SOT_ARMY, 1);
//        BattleRole redRole = new BattleRole(null,null, CommonEnum.SceneObjType.SOT_ARMY, 2);
//        for (int i = 1; i <= roundNum; i++) {
//            roundContextList.add(buildRound(blueRole, redRole, i));
//        }
//    }
//
//    @Test
//    public void tt() {
//        // warmup
//        for (int i = 0; i < 100; i++) {
//            logTest();
//        }
//        logger.debug("warmup finish");
//        for (int i = 0; i < 10; i++) {
//            logger.debug("======================");
//            logTest();
//            logger.debug("");
//        }
//    }
//
//    @Test
//    public void logTest() {
//        int logPage = 0;
//        for (int i = 1; i <= roundNum; i++) {
//            if (i == (logPage + 1) * SAVE_LOG_PER_ROUND) {
//                if (pack(roundContextList, i, logPage)) {
//                    logPage++;
//                }
//            }
//        }
//
//        if (pack(roundContextList, roundNum, logPage)) {
//            logPage++;
//        }
//
//        System.out.println(logPage);
//    }
//
//    @Test
//    public void compressTest() {
//        int i = 0;
//        int length = roundContextList.stream().mapToInt(it -> it.getRoundLog().toByteArray().length).sum();
//        GeminiStopWatch watch = new GeminiStopWatch(StringUtils.format("log size:{}", length));
//        while (i < 100) {
//            i++;
//            for (RoundContext roundContext : roundContextList) {
//                zstdTest(roundContext);
//            }
//            watch.mark(String.valueOf(i));
//        }
//        logger.debug(watch.statWithNoCompact());
//    }
//
//    @Test
//    public void zstdTest(RoundContext roundContext) {
//        Zstd.compress(roundContext.getRoundLog().toByteArray());
//    }
//
//    @Test
//    public void zipTest() throws IOException {
//        int before = roundContextList.get(0).getRoundLog().toByteArray().length;
//        GeminiStopWatch watch = new GeminiStopWatch("log");
//        int zipAfter = ZipUtils.compress(roundContextList.get(0).getRoundLog().toByteArray()).length;
//        watch.mark("start log");
//        float zipRatio = (float) zipAfter / before;
//
//        logger.debug("zip before:{} after:{} ratio:{}%, cost:{}", before, zipAfter, zipRatio * 100, watch.statWithNoCompact());
//    }
//
//    private static boolean pack(List<RoundContext> roundContextList, int curRound, int logPage) {
//        CommonBattle.BattleRoundPage.Builder pageBuilder = CommonBattle.BattleRoundPage.newBuilder();
//        int startRound = logPage * SAVE_LOG_PER_ROUND + 1;
//        if (startRound <= curRound) {
//            pageBuilder.setPageId(logPage + 1);
//            for (int i = startRound - 1; i < curRound; i++) {
//                CommonBattle.BattleRound roundLog = roundContextList.get(i).getRoundLog();
//                if (roundLog != null) {
//                    pageBuilder.addRounds(roundLog);
//                }
//            }
//
//            CommonBattle.BattleRoundPage build = pageBuilder.build();
////            logger.debug("pageBuilder:{}", build.toByteArray().length);
//            Zstd.compress(build.toByteArray(), 1);
////            lz4(build.toByteArray());
////            byte[] deCompress = deLz4(compress);
////            CommonBattle.BattleRoundPage battleRoundPage = CommonBattle.BattleRoundPage.parseFrom(deCompress);
//            return true;
//        }
//        return false;
//    }
//
//    private static RoundContext buildRound(BattleRole blueRole, BattleRole redRole, int roundNum) {
//        RoundContext roundContext = new RoundContext(blueRole, redRole, roundNum);
//        RoundRoleAction blueAction = roundContext.getRoleAction(1);
//        RoundRoleAction redAction = roundContext.getRoleAction(2);
//
//        // 普攻
//        SoldierLossData soldierLossData = new SoldierLossData();
//        soldierLossData.setTreatment(1000);
//        soldierLossData.setSlight(1000);
//        soldierLossData.setSevere(1000);
//        soldierLossData.setDead(1000);
//        blueAction.setAtkBackAction();
//        blueAction.setAtkAction();
//        redAction.setAtkBackAction();
//        redAction.setAtkAction();
//
//        blueAction.getAtkAction().plusUnitDataChange(BattleUnitType.SOLDIER,1, 1, soldierLossData);
//        redAction.getAtkAction().plusUnitDataChange(BattleUnitType.SOLDIER,1, 1, soldierLossData);
//        blueAction.getAtkBackAction().plusUnitDataChange(BattleUnitType.SOLDIER,1, 1, soldierLossData);
//        redAction.getAtkBackAction().plusUnitDataChange(BattleUnitType.SOLDIER,1, 1, soldierLossData);
//
//        // 技能 预备
//        blueAction.addSkillAction(new SkillAction(1, 1, 1, true));
//        redAction.addSkillAction(new SkillAction(1, 1, 1, true));
//
//        // 技能
//        for (int i = 0; i < 30; i++) {
//            SkillAction skillAction = new SkillAction(i, 1, 1, false);
//            blueAction.addSkillAction(skillAction);
//            redAction.addSkillAction(skillAction);
//        }
//
//        // 增益
//        for (int i = 0; i < 30; i++) {
//            BuffAction buffAction = new BuffAction()
//                    .setBuffId(115014121)
//                    .setLogBuffType(CommonEnum.BattleLogBuffType.BLBT_SKILL)
//                    .setAdd(true)
//                    .setInit(true)
//                    .setLifeCycleValue(new RoundLifeCycle(CommonEnum.LifeCycleType.LCT_ROUND, 10).getValue())
//                    .setExecutorInfo(new EffectContext(null, 1, 1, 1, 1));
//            blueAction.addBuffList(buffAction);
//            redAction.addBuffList(buffAction);
//        }
//
//        // 部队
//        for (int i = 0; i < 10; i++) {
//            TroopAction troopAction = new TroopAction()
//                    .setSoldierNum(100)
//                    .setReason(SoldierNumChangeReason.army_cut_in);
//            blueAction.addTroopList(troopAction);
//            redAction.addTroopList(troopAction);
//        }
//        roundContext.packCurRound();
//        return roundContext;
//    }
//
////    public static byte[] lz4(byte[] data) {
////        int oriLen = data.length;
////        ByteBuffer dest = ByteBuffer.allocate(compressor.maxCompressedLength(oriLen));
////        int compressedLen = compressor.compress(ByteBuffer.wrap(data), 0, oriLen, dest, 0, dest.capacity());
////        ByteBuffer out = ByteBuffer.allocate(compressedLen + 2);
////        out.order(ByteOrder.LITTLE_ENDIAN);
////        out.putShort((short) oriLen);
////        out.put(dest.array(), 0, compressedLen);
////
////        return out.array();
////    }
////
////    public static byte[] deLz4(byte[] data) {
////        int dataLen = data.length;
////        ByteBuffer dataBuffer = ByteBuffer.wrap(data);
////        dataBuffer.order(ByteOrder.LITTLE_ENDIAN);
////
////        short oriLen = dataBuffer.getShort();
////        byte[] sourceArray = new byte[dataLen - 2];
////
////        ByteBuffer dataBuffer1 = ByteBuffer.wrap(data);
////        dataBuffer1.position(2);
////        dataBuffer1.get(sourceArray);
////        ByteBuffer source = ByteBuffer.wrap(sourceArray);
////
////        ByteBuffer out = ByteBuffer.allocate(oriLen);
////        deCompressor.decompress(source, 0, out, 0, oriLen);
////        return out.array();
////    }
//}

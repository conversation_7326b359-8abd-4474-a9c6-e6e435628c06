package com.google.firebase.internal;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * CncFirebase使用的线程管理器，底层使用弹性伸缩的协程池。
 * 原因是，firebase SDK使用了重IO的阻塞式的线程模型，但是SDK本身又使用了CachedThreadPool，可能会导致线程爆炸。
 *
 * <AUTHOR>
 */
public class CncDefaultFirebaseThreadManager extends FirebaseThreadManagers.GlobalThreadManager {
    private static final Logger LOGGER = LogManager.getLogger(CncDefaultFirebaseThreadManager.class);

    /**
     * 调度线程数量。
     */
    private final int threadSize;


    public CncDefaultFirebaseThreadManager(int threadSize) {
        this.threadSize = threadSize;
    }

    /**
     * 注意：不允许park或者打日志。
     *
     * @return 构建好的线程池。
     */
    @Override
    protected ExecutorService doInit() {
        // 创建物理线程池作为调度器，最多缓存线程数 * 10
        final GeminiThreadPoolExecutor threadPoolExecutor =
                ConcurrentHelper.newFixedThreadExecutor("firebase-scheduler", this.threadSize, this.threadSize * 10, false);
        threadPoolExecutor.setRejectedExecutionHandler((r, e) -> {
            if (r instanceof Future<?>) {
                LOGGER.error("CncDefaultFirebaseThreadManager overflow, refuse and cancel {}", r);
                ((Future<?>) r).cancel(true);
            } else {
                LOGGER.error("CncDefaultFirebaseThreadManager overflow, refuse {}", r);
            }
        });
        // 2min 不活跃可淘汰
        threadPoolExecutor.setKeepAliveTime(120, TimeUnit.SECONDS);
        threadPoolExecutor.allowCoreThreadTimeOut(true);
        return threadPoolExecutor;
    }

    @Override
    protected void doCleanup(ExecutorService executorService) {
        executorService.shutdownNow();
    }

    @Override
    protected ThreadFactory getThreadFactory() {
        return ConcurrentHelper.newThreadFactory("firebase", false, ConcurrentHelper.UNCAUGHT_EXCEPTION_HANDLER);
    }
}

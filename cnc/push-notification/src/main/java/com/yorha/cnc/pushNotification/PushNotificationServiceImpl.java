package com.yorha.cnc.pushNotification;

import com.yorha.cnc.pushNotification.sender.firebase.FirebaseHelper;
import com.yorha.cnc.pushNotification.sender.firebase.FirebasePushNotificationImpl;
import com.yorha.common.actor.PushNotificationService;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd;
import com.yorha.proto.SsPushNotification.PushSingleNotificationCmd;
import com.yorha.proto.SsPushNotification.PushTopicNotificationCmd;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.LinkedList;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class PushNotificationServiceImpl implements PushNotificationService {
    private static final Logger LOGGER = LogManager.getLogger(PushNotificationServiceImpl.class);

    private final PushNotificationActor actor;
    private final PushNotificationWrapper wrapper;
    private final LinkedList<IPushNotification> elderNotificationList;

    private ActorTimer removeTimer;

    public PushNotificationServiceImpl(PushNotificationActor actor) {
        this.actor = actor;
        this.elderNotificationList = new LinkedList<>();
        // 队列最多缓存最近20秒能推的数据，每秒最多推线程数*10
        this.wrapper = new PushNotificationWrapper(actor, FirebaseHelper.FIREBASE_THREAD_NUM * 200, FirebaseHelper.FIREBASE_THREAD_NUM * 10);
        this.reloadCoreNotification();
    }

    @Override
    public void handlePushSingleNotificationCmd(PushSingleNotificationCmd ask) {
        this.wrapper.pushNotification(ask);
    }

    @Override
    public void handlePushMultipleNotificationCmd(PushMultipleNotificationCmd ask) {
        this.wrapper.pushMultipleNotification(ask);
    }

    @Override
    public void handlePushTopicNotificationCmd(PushTopicNotificationCmd ask) {
        this.wrapper.pushTopicNotification(ask);
    }

    /**
     * 关闭当前notification。
     */
    public void closeNotification() {
        try {
            this.wrapper.close();
        } catch (IOException e) {
            LOGGER.error("PushNotificationServiceImpl closeNotification wrapper={}", this.wrapper);
        }
    }

    /**
     * 重载当前notification。
     */
    public void reloadCoreNotification() {
        try {
            LOGGER.info("PushNotificationServiceImpl reloadNotification begin, wrapper={}", this.wrapper);
            // 新建notification
            final IPushNotification newNotification = FirebasePushNotificationImpl.newInstance(this.actor.self());
            // 更新notification
            final IPushNotification elderNotification = this.wrapper.getCoreNotification();
            // 设置新的核心推送逻辑
            this.wrapper.setCoreNotification(newNotification);

            LOGGER.info("PushNotificationServiceImpl reloadNotification end, wrapper={}, elderNotification={}", this.wrapper, elderNotification);
            // 开始删除旧的notification
            if (this.removeTimer != null) {
                return;
            }

            if (elderNotification == null) {
                return;
            }

            this.elderNotificationList.add(elderNotification);

            final int rpcTimeoutTimeMs = ServerContext.getRpcTimeout() * 2;
            this.removeTimer = this.actor.addRepeatTimer(TimerReasonType.REMOVE_NOTIFICATION,
                    () -> {
                        //异步处理，需要double check，保证流程时ok的。
                        if (this.elderNotificationList.isEmpty()) {
                            if (this.removeTimer != null) {
                                this.removeTimer.cancel();
                                this.removeTimer = null;
                            }
                            return;
                        }
                        final IPushNotification removed = this.elderNotificationList.getFirst();
                        // 关闭并删除
                        try {
                            removed.close();
                            this.elderNotificationList.pollFirst();
                            LOGGER.info("PushNotificationServiceImpl removeNotification notification={}", removed);
                        } catch (IOException e) {
                            LOGGER.error("PushNotificationServiceImpl removeNotification notification={}, e=", removed, e);
                        }
                    },
                    rpcTimeoutTimeMs,
                    rpcTimeoutTimeMs,
                    TimeUnit.MILLISECONDS);
        } catch (IOException e) {
            LOGGER.error("PushNotificationServiceImpl reloadNotification end, wrapper={}, e=", this.wrapper, e);
        }
    }

}

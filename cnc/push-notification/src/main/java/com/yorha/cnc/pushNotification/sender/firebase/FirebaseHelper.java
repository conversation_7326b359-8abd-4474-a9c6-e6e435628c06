package com.yorha.cnc.pushNotification.sender.firebase;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.internal.CncDefaultFirebaseThreadManager;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.json.JsonUtils;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Firebase相关的工具类库。
 *
 * <AUTHOR>
 */
public final class FirebaseHelper {

    private FirebaseHelper() {
    }

    public static final int FIREBASE_THREAD_NUM = 10;

    /**
     * 根据集群配置构建出FirebaseApp。
     *
     * @param name app 名称。
     * @return FirebaseApp。
     * @throws IOException IO异常。
     */
    public static FirebaseApp newAppFromClusterConfig(final String name) throws IOException {
        final Map<String, String> contentMap = getFirebaseConfig();
        return newApp(contentMap, name);
    }

    /**
     * 获取etcd集群配置中firebase的相关配置
     *
     * @return 配置
     */
    public static Map<String, String> getFirebaseConfig() {
        final List<String> fieldNames = Arrays.asList(
                "type",
                "project_id",
                "private_key_id",
                "private_key",
                "client_email",
                "client_id",
                "auth_uri",
                "token_uri",
                "auth_provider_x509_cert_url",
                "client_x509_cert_url",
                "universe_domain"
        );
        final String prefix = "firebase_";
        final Map<String, String> contentMap = new LinkedHashMap<>(fieldNames.size());
        for (final String fieldName : fieldNames) {
            final String clusterFieldName = prefix + fieldName;
            final String fieldContent = ClusterConfigUtils.getWorldConfig().getStringItem(clusterFieldName);
            contentMap.put(fieldName, fieldContent);
        }
        return contentMap;
    }

    /**
     * 根据KV构建出FirebaseApp。
     *
     * @param content KV map。
     * @param name    名称。
     * @return FirebaseApp。
     * @throws IOException IO异常。
     */
    public static FirebaseApp newApp(final Map<String, String> content, final String name) throws IOException {
        final String toJsonString = JsonUtils.toJsonString(content);
        final ByteArrayInputStream inputStream = new ByteArrayInputStream(toJsonString.getBytes(StandardCharsets.UTF_8));
        return newApp(inputStream, name);
    }

    /**
     * 根据JSON文件构建出FirebaseApp。
     *
     * @param googleAppCredentialFilePath 文件路径。
     * @param name                        名称。
     * @return FirebaseApp。
     * @throws IOException IO异常。
     */
    public static FirebaseApp newApp(final String googleAppCredentialFilePath, final String name) throws IOException {
        final FileInputStream inputStream = new FileInputStream(googleAppCredentialFilePath);
        return newApp(inputStream, name);
    }

    private static FirebaseApp newApp(final InputStream inputStream, final String name) throws IOException {
        // 1. HttpTransport使用默认的OAuth2Utils.HTTP_TRANSPORT_FACTORY(单例，无状态)。
        // 2. Scope是FIREBASE_SCOPES
        /**
         // Enables access to Firebase Realtime Database.
         "https://www.googleapis.com/auth/firebase.database",

         // Enables access to the email address associated with a project.
         "https://www.googleapis.com/auth/userinfo.email",

         // Enables access to Google Identity Toolkit (for user management APIs).
         "https://www.googleapis.com/auth/identitytoolkit",s

         // Enables access to Google Cloud Storage.
         "https://www.googleapis.com/auth/devstorage.full_control",

         // Enables access to Google Cloud Firestore
         "https://www.googleapis.com/auth/cloud-platform",
         "https://www.googleapis.com/auth/datastore"
         */
        // DEFAULT_TOKEN_REFRESHER_FACTORY
        final GoogleCredentials credentials = GoogleCredentials.fromStream(inputStream);

        // httpTransport OAuth2Utils.HTTP_TRANSPORT_FACTORY
        final FirebaseOptions options = FirebaseOptions.builder()
                .setCredentials(credentials)
                // 单位: ms，发起连接的超时时间
                .setConnectTimeout((int) TimeUnit.SECONDS.toMillis(5))
                // 单位: ms，远端数据返回的超时时间。
                .setReadTimeout(5000)
                .setDatabaseUrl("https://<DATABASE_NAME>.firebaseio.com/")
                .setThreadManager(new CncDefaultFirebaseThreadManager(FIREBASE_THREAD_NUM))
                .build();
        return FirebaseApp.initializeApp(options, name);
    }
}

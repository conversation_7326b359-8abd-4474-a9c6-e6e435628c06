package com.yorha.cnc.pushNotification.sender.firebase;

/**
 * 推送类型枚举
 *
 * <AUTHOR>
 */

public enum FirebasePushType {
    /**
     * 单推
     */
    SINGLE_PUSH,
    /**
     * 多推
     */
    MULTI_PUSH,
    /**
     * 主题推送
     */
    TOPIC_PUSH;

    public String success() {
        return this.name() + "_" + "SUCCESSFUL";
    }

    public String fail() {
        return this.name() + "_" + "FAIL";
    }

    public String pushCancel() {
        return this.name() + "_" + "CANCEL";
    }

    public String pushNotFinish() {
        return this.name() + "_" + "NOT_FINISH";
    }

    public String pushPartSuccess() {
        return this.name() + "_" + "PART_SUCCESS";
    }

    public String pushException() {
        return this.name() + "_" + "EXCEPTION";
    }

    public static String sdkOverFlow() {
        return "Sdk_OverFlow";
    }
}

package com.yorha.cnc.pushNotification.sender.firebase;

import com.google.api.core.ApiFuture;
import com.google.firebase.ErrorCode;
import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.*;
import com.yorha.cnc.pushNotification.IPushNotification;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.notification.NotificationTokenHelper;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.language.ServerLanguageTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPushNotification;
import com.yorha.proto.SsScenePlayer;
import it.unimi.dsi.fastutil.longs.Long2ObjectMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncNotification;

import javax.annotation.concurrent.ThreadSafe;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static com.google.firebase.messaging.MessagingErrorCode.UNREGISTERED;

/**
 * 基于Firebase的异步推送器，不允许在协程使用。
 * <p>
 * 错误码文档：https://firebase.google.com/docs/reference/fcm/rest/v1/ErrorCode?hl=zh-cn&authuser=0
 * 后续可参考指数退避算法进行重试，如需，https://googleapis.github.io/google-http-java-client/exponential-backoff.html
 *
 * <AUTHOR>
 */
@ThreadSafe
public class FirebasePushNotificationImpl implements IPushNotification, QlogServerFlowInterface {
    private static final Logger LOGGER = LogManager.getLogger(FirebasePushNotificationImpl.class);

    private static final int FIREBASE_MESSAGE_BATCH_MAX_NUMBER = 100;

    private final FirebaseApp app;
    private final FirebaseMessaging firebaseMessaging;
    private final Executor executor;
    private int batchSeqNo = 0;

    public static IPushNotification newInstance(final IActorRef sender) throws IOException {
        final FirebaseApp app = FirebaseHelper.newAppFromClusterConfig("message-push-" + SystemClock.nowNative());
        final Executor executor = command -> ActorSendMsgUtils.send(sender, new ActorRunnable<AbstractActor>("firebase-callback", (actor) -> command.run()));
        return new FirebasePushNotificationImpl(app, executor);
    }

    public FirebasePushNotificationImpl(final FirebaseApp app, final Executor executor) {
        this.app = app;
        this.firebaseMessaging = FirebaseMessaging.getInstance(this.app);
        this.executor = executor;
    }

    /**
     * 发送单个消息。
     * 参考文档：https://firebase.google.com/docs/cloud-messaging/send-message?hl=zh-cn&authuser=0#send-messages-to-specific-devices
     *
     * @param msg 消息。
     */
    @Override
    public final void pushNotification(SsPushNotification.PushSingleNotificationCmd msg) {
        // 参数校验
        if (!msg.hasTitle()) {
            throw new GeminiException("no title, msg={}", msg);
        }
        if (!msg.hasBody()) {
            throw new GeminiException("no body, msg={}", msg);
        }
        if (!msg.hasPlayerId() || msg.getPlayerId() <= 0) {
            throw new GeminiException("no player id, msg={}", msg);
        }


        final long playerId = msg.getPlayerId();
        final String token = NotificationTokenHelper.getToken(playerId);
        if (token == null) {
            LOGGER.warn("no fire base token, playerId={}", playerId);
            return;
        }
        final CommonEnum.Language language = NotificationTokenHelper.getLanguage(playerId);
        final String title = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(language, msg.getTitle());
        final String body = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(language, msg.getBody());

        // 准备数据
        final Message message = Message.builder()
                .setToken(token)
                .setNotification(Notification.builder().setTitle(title).setBody(body).build())
                .setAndroidConfig(AndroidConfig.builder()
                        .setNotification(AndroidNotification.builder().setIcon("drawable/app_icon").build())
                        .build())
                .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder().build())
                        .setFcmOptions(ApnsFcmOptions.builder().setImage("drawable/app_icon").build())
                        .build())
                .build();

        // 发送请求
        final ApiFuture<String> future = this.firebaseMessaging.sendAsync(message, false);
        future.addListener(() -> {
            // 已取消
            if (future.isCancelled()) {
                LOGGER.warn("FirebaseMessageAsyncSenderImpl sendMessage cancel, title={}, body={}, playerId={}, token={}", title, body, playerId, token);
                sendNotificationQlog(title, body, String.valueOf(playerId), FirebasePushType.SINGLE_PUSH.fail());
                return;
            }

            // 未完成
            if (!future.isDone()) {
                LOGGER.error("FirebaseMessageAsyncSenderImpl sendMessage isNotDone, title={}, body={}, playerId={}, token={}", title, body, playerId, token);
                sendNotificationQlog(title, body, String.valueOf(playerId), FirebasePushType.SINGLE_PUSH.fail());
                return;
            }

            try {
                // 打印结果
                final String result = future.get(1, TimeUnit.NANOSECONDS);
                LOGGER.info("FirebaseMessageAsyncSenderImpl sendMessage successful, title={}, body={}, playerId={}, token={}, result={}",
                        title, body, playerId, token, result);
                sendNotificationQlog(title, body, String.valueOf(playerId), FirebasePushType.SINGLE_PUSH.success());
            } catch (Throwable t) {
                sendNotificationQlog(title, body, String.valueOf(playerId), FirebasePushType.SINGLE_PUSH.fail());
                // 详细打印
                if (t.getCause() instanceof FirebaseMessagingException) {
                    final FirebaseMessagingException e = (FirebaseMessagingException) t.getCause();
                    if (e.getMessagingErrorCode() == UNREGISTERED) {
                        ActorSendMsgUtils.send(RefFactory.ofBigScene(ServerContext.getZoneId()), SsScenePlayer.SyncPlayerPushNtfInfoAsk.newBuilder()
                                .setPlayerId(playerId)
                                .setIntlNtfToken("")
                                .build());
                        LOGGER.warn("FirebaseMessageAsyncSenderImpl sendMessage fail clear token playerId={}, token={}", playerId, token);
                        return;
                    }
                    logPushException(title, body, String.format("playerId=%d, token=%s", playerId, token), e);
                    return;
                }
                LOGGER.error("FirebaseMessageAsyncSenderImpl sendMessage fail, title={}, body={}, playerId={}, token={}, t=",
                        title, body, playerId, token, t);
            }
        }, this.executor);

    }

    /**
     * 发送多个消息。
     * 参考文档：https://firebase.google.com/docs/cloud-messaging/send-message?hl=zh-cn&authuser=0#send-messages-to-multiple-devices
     * 注意：firebase最多向500个设备进行发送，因此会主动做拆分。
     *
     * @param msg 消息。
     */
    @Override
    public final void pushMultipleNotification(SsPushNotification.PushMultipleNotificationCmd msg) {
        // 参数校验
        if (!msg.hasTitle()) {
            throw new GeminiException("no title msg={}", msg);
        }
        if (!msg.hasBody()) {
            throw new GeminiException("no body msg={}", msg);
        }

        if (msg.getPlayerIdListCount() == 0) {
            throw new GeminiException("no playerIds msg={}", msg);
        }
        // 处理语言类型
        final Map<CommonEnum.Language, List<Long>> languageMap = new HashMap<>();

        for (final long playerId : msg.getPlayerIdListList()) {
            // 加入到容器中
            CommonEnum.Language lang = NotificationTokenHelper.getLanguage(playerId);
            languageMap.computeIfAbsent(lang, (k) -> new LinkedList<>()).add(playerId);
        }

        for (Map.Entry<CommonEnum.Language, List<Long>> entry : languageMap.entrySet()) {
            pushMultipleNotificationWithLanguage(entry.getValue(), entry.getKey(), msg.getTitle(), msg.getBody());
        }

    }

    private void pushMultipleNotificationWithLanguage(List<Long> playerIds, CommonEnum.Language language, final int titleId, final int bodyId) {
        // 序号+1
        final int curSeqNo = this.batchSeqNo;
        this.batchSeqNo++;
        // 从msg中获取基本数据
        final String title = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(language, titleId);
        final String body = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(language, bodyId);

        // 迭代数据
        LinkedHashMap<Long, String> tokenMap = null;

        for (Long playerId : playerIds) {
            final String token = NotificationTokenHelper.getToken(playerId);
            if (token == null) {
                LOGGER.warn("pushMultipleNotification no token, playerId={}", playerId);
                continue;
            }
            // 准备map
            if (tokenMap == null) {
                tokenMap = new LinkedHashMap<>(Math.min(playerIds.size(), FIREBASE_MESSAGE_BATCH_MAX_NUMBER));
            }
            tokenMap.put(playerId, token);

            if (tokenMap.size() != FIREBASE_MESSAGE_BATCH_MAX_NUMBER) {
                continue;
            }

            // 满100直接发送
            this.sendBatchMessageHelper(curSeqNo, title, body, tokenMap);
            tokenMap = null;
        }

        if (tokenMap == null) {
            return;
        }
        // 发送未满100的玩家
        this.sendBatchMessageHelper(curSeqNo, title, body, tokenMap);
    }


    private void sendBatchMessageHelper(final int seqNo, final String title, final String body, LinkedHashMap<Long, String> tokenMap) {
        final MulticastMessage message = MulticastMessage.builder()
                .addAllTokens(tokenMap.values())
                .setNotification(Notification.builder().setTitle(title).setBody(body).build())
                .setAndroidConfig(AndroidConfig.builder()
                        .setNotification(AndroidNotification.builder().setIcon("drawable/app_icon").build())
                        .build())
                .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder().build())
                        .setFcmOptions(ApnsFcmOptions.builder().setImage("drawable/app_icon").build())
                        .build())
                .build();
        final ApiFuture<BatchResponse> future = this.firebaseMessaging.sendEachForMulticastAsync(message);
        future.addListener(() -> {
            // 取消的直接返回
            if (future.isCancelled()) {
                LOGGER.warn("FirebaseMessageAsyncSenderImpl sendBatchMessage cancel, seqNo={}, title={}, body={}, tokenMap={}",
                        seqNo, title, body, tokenMap);
                sendNotificationQlog(title, body, StringUtils.join(tokenMap.keySet().toArray()), FirebasePushType.MULTI_PUSH.fail());
                return;
            }
            // 未完成的直接返回
            if (!future.isDone()) {
                LOGGER.error("FirebaseMessageAsyncSenderImpl sendBatchMessage isNotDone, seqNo={}, title={}, body={}, tokenMap={}",
                        seqNo, title, body, tokenMap);
                sendNotificationQlog(title, body, StringUtils.join(tokenMap.keySet().toArray()), FirebasePushType.MULTI_PUSH.fail());
                return;
            }
            try {
                // 获取返回值
                final BatchResponse batchResponse = future.get(1, TimeUnit.NANOSECONDS);
                if (batchResponse.getSuccessCount() == batchResponse.getSuccessCount()) {
                    LOGGER.info("FirebaseMessageAsyncSenderImpl sendBatchMessage successful, seqNo={}, title={}, body={}, tokenMap={}",
                            seqNo, title, body, tokenMap);
                    sendNotificationQlog(title, body, StringUtils.join(tokenMap.keySet().toArray()), FirebasePushType.MULTI_PUSH.success());
                    return;
                }

                // 准备打印结果
                final LongList playerIdList = new LongArrayList(tokenMap.keySet());
                final Long2ObjectMap<String> successfulMap = new Long2ObjectOpenHashMap<>(batchResponse.getSuccessCount());

                for (int index = 0; index < batchResponse.getResponses().size(); index++) {
                    // 请求失败，打印
                    final SendResponse response = batchResponse.getResponses().get(index);
                    final long playerId = playerIdList.getLong(index);
                    final String token = tokenMap.get(playerId);
                    if (response.isSuccessful()) {
                        successfulMap.put(playerId, token);
                        continue;
                    }
                    if ((response.getException() != null) && (response.getException().getCause() instanceof FirebaseMessagingException)) {
                        final FirebaseMessagingException e = (FirebaseMessagingException) response.getException().getCause();
                        if (e.getMessagingErrorCode() == UNREGISTERED) {
                            ActorSendMsgUtils.send(RefFactory.ofBigScene(ServerContext.getZoneId()), SsScenePlayer.SyncPlayerPushNtfInfoAsk.newBuilder()
                                    .setPlayerId(playerId)
                                    .setIntlNtfToken("")
                                    .build());
                            LOGGER.warn("FirebaseMessageAsyncSenderImpl sendBatchMessageHelper fail clear token playerId={}, token={}", playerId, token);
                            return;
                        }
                    }
                    logPushException(title, body, String.format("seqNo=%d, playerId=%d, token=%s", seqNo, playerId, token), response.getException());
                }

                LOGGER.info("FirebaseMessageAsyncSenderImpl sendBatchMessage successful, seqNo={}, title={}, body={}, playerTokenMap={}",
                        seqNo, title, body, successfulMap);
                sendNotificationQlog(title, body, StringUtils.join(tokenMap.keySet().toArray()), FirebasePushType.MULTI_PUSH.fail());
            } catch (Throwable t) {
                sendNotificationQlog(title, body, StringUtils.join(tokenMap.keySet().toArray()), FirebasePushType.MULTI_PUSH.fail());
                if (t.getCause() instanceof FirebaseMessagingException) {
                    logPushException(title, body, String.format("seqNo=%d, tokenMap=%s", seqNo, tokenMap), (FirebaseMessagingException) t.getCause());
                    return;
                }
                LOGGER.error("FirebaseMessageAsyncSenderImpl sendBatchMessage fail, seqNo={}, title={}, body={}, tokenMap={}, t=",
                        seqNo, title, body, tokenMap, t);
            }
        }, this.executor);
    }

    /**
     * 参考文档：https://firebase.google.com/docs/cloud-messaging/send-message?hl=zh-cn&authuser=0#send-messages-to-topics
     * JOSEFREN: 暂不支持，等开发
     *
     * @param msg 消息类型
     */
    @Override
    public final void pushTopicNotification(SsPushNotification.PushTopicNotificationCmd msg) {
        if (!msg.hasTitle()) {
            throw new GeminiException("no title, msg={}", msg);
        }
        if (!msg.hasBody()) {
            throw new GeminiException("no body, msg={}", msg);
        }
        if (!msg.hasTopic()) {
            throw new GeminiException("no topic, msg={}", msg);
        }

        final String topic = msg.getTopic();
        final String title = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(CommonEnum.Language.en, msg.getTitle());
        final String body = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(CommonEnum.Language.en, msg.getBody());

        // 准备数据
        final Message message = Message.builder()
                .setTopic(topic)
                .setNotification(Notification.builder().setTitle(title).setBody(body).build())
                .setAndroidConfig(AndroidConfig.builder()
                        .setNotification(AndroidNotification.builder().setIcon("drawable/app_icon").build())
                        .build())
                .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder().build())
                        .setFcmOptions(ApnsFcmOptions.builder().setImage("drawable/app_icon").build())
                        .build())
                .build();

        final ApiFuture<String> future = this.firebaseMessaging.sendAsync(message);
        future.addListener(() -> {
            // 已取消
            if (future.isCancelled()) {
                LOGGER.warn("FirebaseMessageAsyncSenderImpl sendTopicMessage cancel, title={}, body={}, topic={}", title, body, topic);
                sendNotificationQlog(title, body, topic, FirebasePushType.TOPIC_PUSH.fail());
                return;
            }

            // 未完成
            if (!future.isDone()) {
                LOGGER.error("FirebaseMessageAsyncSenderImpl sendTopicMessage isNotDone, title={}, body={}, topic={}", title, body, topic);
                sendNotificationQlog(title, body, topic, FirebasePushType.TOPIC_PUSH.fail());
                return;
            }

            try {
                // 打印结果
                final String result = future.get(1, TimeUnit.NANOSECONDS);
                LOGGER.info("FirebaseMessageAsyncSenderImpl sendTopicMessage successful, title={}, body={}, topic={}, result={}", title, body, topic, result);
                sendNotificationQlog(title, body, topic, FirebasePushType.TOPIC_PUSH.success());
            } catch (Throwable t) {
                sendNotificationQlog(title, body, topic, FirebasePushType.TOPIC_PUSH.fail());
                // 详细打印
                if (t.getCause() instanceof FirebaseMessagingException) {
                    logPushException(title, body, String.format("topic=%s", topic), (FirebaseMessagingException) t.getCause());
                    return;
                }
                LOGGER.error("FirebaseMessageAsyncSenderImpl sendTopicMessage fail, title={}, body={}, topic={}, t=",
                        title, body, topic, t);
            }
        }, this.executor);
    }

    public void logPushException(String title, String body, String extraInfo, FirebaseMessagingException exception) {
        if (exception.getHttpResponse() == null) {
            if (exception.getErrorCode() == null || exception.getErrorCode() == ErrorCode.DEADLINE_EXCEEDED) {
                LOGGER.warn("FireBaseMessageAsyncSenderImpl logPushException, title={}, body={}, {}, message={}, errorCode={}, errorCode={}",
                        title, body, extraInfo, exception.getMessage(), exception.getMessagingErrorCode(), exception.getErrorCode());
                return;
            }
            LOGGER.error("FireBaseMessageAsyncSenderImpl logPushException, title={}, body={}, {}, message={}, messagingErrorCode={}, errorCode={}",
                    title, body, extraInfo, exception.getMessage(), exception.getMessagingErrorCode(), exception.getErrorCode());
            return;
        }

        LOGGER.error("FirebaseMessageAsyncSenderImpl logPushException, title={}, body={}, {}, {} {} {}, message={}, errorCode={}, content={}",
                title, body, extraInfo,
                exception.getHttpResponse().getRequest().getMethod(),
                exception.getHttpResponse().getRequest().getUrl(),
                exception.getHttpResponse().getStatusCode(),
                exception.getMessage(),
                exception.getMessagingErrorCode(),
                exception.getHttpResponse().getContent()
        );
    }

    public void sendNotificationQlog(String title, String content, String target, String result) {
        QlogCncNotification.init(this)
                .setDtEventTime(TimeUtils.now2String())
                .setTitle(title)
                .setContent(content)
                .setNotificationTargetList(target)
                .setNotificationTime(TimeUtils.now2String())
                .setNotificationResult(result)
                .sendToQlog();
    }

    @Override
    public void close() {
        this.app.delete();
        LOGGER.info("FirebaseMessageAsyncSenderImpl close app={}", this.app.getName());
    }

    @Override
    public String toString() {
        return "FirebasePushNotificationImpl{" +
                "app=" + this.app.getName() +
                ", batchSeqNo=" + batchSeqNo +
                '}';
    }

    @Override
    public String getServerType() {
        return "ZoneServer";
    }

    @Override
    public int getIZoneAreaID() {
        return 0;
    }

    @Override
    public String getServerOpenTime() {
        return "";
    }

    @Override
    public long getServerOpenTimeFar() {
        return 0;
    }

    @Override
    public int getServerOnline() {
        return 0;
    }

    @Override
    public String getServerCondition() {
        return "";
    }

    @Override
    public int getServerMilestoneStage() {
        return 0;
    }

    @Override
    public String getAccountId() {
        return "server_" + ServerContext.getServerInfo().getWorldId();
    }
}

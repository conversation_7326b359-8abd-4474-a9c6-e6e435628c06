package com.yorha.cnc.pushNotification;

import com.yorha.proto.SsPushNotification;

import java.io.Closeable;

/**
 * 消息推送器。
 *
 * <AUTHOR>
 */
public interface IPushNotification extends Closeable {
    /**
     * 推送单个消息。
     *
     * @param msg 消息体。
     */
    void pushNotification(SsPushNotification.PushSingleNotificationCmd msg);

    /**
     * 推送多个消息。
     *
     * @param msg 消息体结合。
     */
    void pushMultipleNotification(SsPushNotification.PushMultipleNotificationCmd msg);

    /**
     * 推送消息到被订阅的topic。
     *
     * @param msg 消息体。
     */
    void pushTopicNotification(SsPushNotification.PushTopicNotificationCmd msg);
}

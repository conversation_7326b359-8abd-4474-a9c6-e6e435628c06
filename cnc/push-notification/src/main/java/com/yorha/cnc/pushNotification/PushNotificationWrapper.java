package com.yorha.cnc.pushNotification;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.notification.NotificationTokenHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.SsPushNotification;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.LinkedList;
import java.util.concurrent.TimeUnit;

/**
 * 延迟推送的包裹函数。
 * 1. 限制频率。
 * 2. 拆多推为单推送。
 *
 * <AUTHOR>
 */
public class PushNotificationWrapper
        implements IPushNotification {

    private static final Logger LOGGER = LogManager.getLogger(PushNotificationWrapper.class);

    /**
     * 推送实体。
     */
    private interface IItem {
        /**
         * 返回消息。
         *
         * @return 推送消息。
         */
        GeneratedMessageV3 getCmd();

        /**
         * 返回时长。
         *
         * @return 推送实体的超时时间。
         */
        long getTimeoutTsMs();

        /**
         * 推送内容。
         *
         * @param notification 推送实体。
         */
        void push(IPushNotification notification);
    }

    /**
     * 单推实体。
     */
    private static class PushSingleItem implements IItem {
        private final long timeoutTsMs;
        private final SsPushNotification.PushSingleNotificationCmd cmd;

        private PushSingleItem(long timeoutTsMs, @NotNull SsPushNotification.PushSingleNotificationCmd cmd) {
            this.timeoutTsMs = timeoutTsMs;
            this.cmd = cmd;
        }

        @Override
        public SsPushNotification.PushSingleNotificationCmd getCmd() {
            return cmd;
        }

        @Override
        public long getTimeoutTsMs() {
            return timeoutTsMs;
        }

        @Override
        public void push(IPushNotification notification) {
            notification.pushNotification(this.cmd);
        }
    }

    /**
     * topic类型的推送实体。
     */
    private static class PushTopicItem implements IItem {
        private final long timeoutTsMs;
        private final SsPushNotification.PushTopicNotificationCmd cmd;

        private PushTopicItem(long timeoutTsMs, @NotNull SsPushNotification.PushTopicNotificationCmd cmd) {
            this.timeoutTsMs = timeoutTsMs;
            this.cmd = cmd;
        }

        @Override
        public SsPushNotification.PushTopicNotificationCmd getCmd() {
            return this.cmd;
        }

        @Override
        public long getTimeoutTsMs() {
            return this.timeoutTsMs;
        }

        @Override
        public void push(IPushNotification notification) {
            notification.pushTopicNotification(this.cmd);
        }
    }

    /**
     * 吞掉推送。
     */
    private static final class PushItemSwallow
            implements IPushNotification {

        @Override
        public void pushNotification(SsPushNotification.PushSingleNotificationCmd msg) {
            LOGGER.error("PushItemSwallow swallow PushSingleNotificationCmd={}", msg);
        }

        @Override
        public void pushMultipleNotification(SsPushNotification.PushMultipleNotificationCmd msg) {
            LOGGER.error("PushItemSwallow swallow PushMultipleNotificationCmd={}", msg);
        }

        @Override
        public void pushTopicNotification(SsPushNotification.PushTopicNotificationCmd msg) {
            LOGGER.error("PushItemSwallow swallow PushTopicNotificationCmd={}", msg);
        }

        @Override
        public void close() {
        }

        @Override
        public String toString() {
            return "PushItemSwallow{}";
        }
    }


    private final PushNotificationActor actor;
    private final LinkedList<IItem> notificationItems;
    private final int maxQueueSize;
    private final int maxRequestPerSec;
    private ActorTimer timer;
    private IPushNotification coreNotification;

    public PushNotificationWrapper(PushNotificationActor actor, int maxQueueSize, int maxRequestPerSec) {
        this.actor = actor;
        this.notificationItems = new LinkedList<>();
        this.maxQueueSize = maxQueueSize;
        this.maxRequestPerSec = maxRequestPerSec;
        this.timer = null;
        this.coreNotification = new PushItemSwallow();
    }

    /**
     * 设置核心推送逻辑。
     *
     * @param coreNotification 核心推送逻辑。
     */
    public void setCoreNotification(IPushNotification coreNotification) {
        this.coreNotification = coreNotification;
    }

    /**
     * 获取核心推送逻辑本体。
     *
     * @return 核心推送逻辑。
     */
    public IPushNotification getCoreNotification() {
        return coreNotification;
    }

    @Override
    public void pushNotification(SsPushNotification.PushSingleNotificationCmd cmd) {
        if (!this.isPushNotificationEnable()) {
            return;
        }
        if (!isCanNtfPlayer(cmd.getPlayerId(), cmd.getModelId())) {
            return;
        }
        // 将推送要求送入队
        this.queueNotification(cmd);
    }

    /**
     * @param playerId 目标玩家id
     * @param modelId  推送类型
     * @return 是否是可推送玩家（玩家开启推送 & token存在）
     */
    private boolean isCanNtfPlayer(final long playerId, final int modelId) {
        // 验证是否需要推送
        if (!NotificationTokenHelper.isNeedNotification(playerId, modelId)) {
            LOGGER.info("PushNotificationWrapper isCanNtfPlayer set no ntf, playerId={}, modelId={}", playerId, modelId);
            return false;
        }
        final String token = NotificationTokenHelper.getToken(playerId);
        // token无效，则过滤
        if (token == null) {
            LOGGER.info("PushNotificationWrapper isCanNtfPlayer token is null, playerId={}, modelId={}", playerId, modelId);
            return false;
        }
        return true;
    }

    @Override
    public void pushMultipleNotification(SsPushNotification.PushMultipleNotificationCmd msg) {
        if (msg.getPlayerIdListCount() == 0) {
            return;
        }

        if (!this.isPushNotificationEnable()) {
            return;
        }

        // 将推送消息化整为零，因为firebase底层自己也做了这个事情
        for (final Long playerId : msg.getPlayerIdListList()) {
            if (!isCanNtfPlayer(playerId, msg.getModelId())) {
                continue;
            }
            final SsPushNotification.PushSingleNotificationCmd cmd = SsPushNotification.PushSingleNotificationCmd.newBuilder()
                    .setTitle(msg.getTitle())
                    .setBody(msg.getBody())
                    .setPlayerId(playerId)
                    .build();
            // 将推送要求送入队
            this.queueNotification(cmd);
        }
    }

    @Override
    public void pushTopicNotification(SsPushNotification.PushTopicNotificationCmd msg) {
        if (!this.isPushNotificationEnable()) {
            return;
        }
        // 将推送要求送入队
        this.queueNotification(msg);
    }

    @Override
    public void close() throws IOException {
        // 取消定时器
        if (this.timer != null) {
            for (int i = 0; i < 10000; i++) {
                // 直接触发定时器发送消息的回调
                this.onTimerOfPushNotification();
                if (this.timer == null) {
                    break;
                }
            }
        }
        // 关闭核心逻辑
        this.coreNotification.close();
        // 设置核心逻辑为空
        this.setCoreNotification(new PushItemSwallow());
    }

    private void queueNotification(GeneratedMessageV3 cmd) {
        final long timeoutTsMs = SystemClock.nowNative() + ServerContext.getRpcTimeout() * 2L;
        // 单推入队
        if (cmd instanceof SsPushNotification.PushSingleNotificationCmd) {
            this.notificationItems.add(new PushSingleItem(timeoutTsMs, (SsPushNotification.PushSingleNotificationCmd) cmd));
        } else if (cmd instanceof SsPushNotification.PushTopicNotificationCmd) {
            // topic推送入队
            this.notificationItems.add(new PushTopicItem(timeoutTsMs, (SsPushNotification.PushTopicNotificationCmd) cmd));
        } else {
            // 超出预期的消息
            throw new NotImplementedException(cmd.getClass().getSimpleName() + " Not Implemented");
        }

        // 未加定时器，则加定时器
        if (this.timer == null) {
            // add timer
            this.timer = this.actor.addRepeatTimer(TimerReasonType.FIREBASE_DELAY_PUSH_TASK, this::onTimerOfPushNotification, 0, 1, TimeUnit.SECONDS);
        }

        if (this.notificationItems.isEmpty()) {
            return;
        }
        if (this.notificationItems.size() < this.maxQueueSize) {
            return;
        }

        // 淘汰多余的数据
        final IItem removedItem = this.notificationItems.pollFirst();
        LOGGER.warn("DelaySinglePushWrapper queueNotification fullfilled, maxSize={}, cmd={} is removed by fifo", this.maxQueueSize, removedItem.getCmd());
    }

    /**
     * 定时器触发的推送任务。
     */
    private void onTimerOfPushNotification() {
        // 有可能close的瞬间收到了tick消息
        if (this.timer == null) {
            return;
        }

        final long nowTsMs = SystemClock.nowNative();

        int pushedCnt = 0;
        int discardCnt = 0;

        for (int i = 0; i < this.maxRequestPerSec; i++) {
            // 检查是否过期
            final IItem head = this.notificationItems.pollFirst();
            if (head == null) {
                break;
            }

            // 超时，则丢弃
            if (nowTsMs >= head.getTimeoutTsMs()) {
                LOGGER.warn("DelaySinglePushWrapper onTimerOfPushNotification is expire, nowTsMs={}, timeoutTsMs={}, cmd={}",
                        nowTsMs, head.getTimeoutTsMs(), head.getCmd());
                discardCnt += 1;
                continue;
            }

            // 推送
            head.push(this.coreNotification);
            pushedCnt += 1;
        }

        // 推送未完成，则等待下一次tick
        if (!this.notificationItems.isEmpty()) {
            LOGGER.info("DelaySinglePushWrapper onTimerOfPushNotification part is done, nowTsMs={}, push={}, discard={}, others={}",
                    nowTsMs, pushedCnt, discardCnt, this.notificationItems.size());
            return;
        }

        // 推送完成，取消定时器
        this.timer.cancel();
        this.timer = null;
        LOGGER.info("DelaySinglePushWrapper onTimerOfPushNotification all is done, nowTsMs={}, push={}, discard={}",
                nowTsMs, pushedCnt, discardCnt);
    }

    private boolean isPushNotificationEnable() {
        return ClusterConfigUtils.getWorldConfig().getBooleanItem("push_notification_enable");
    }

    @Override
    public String toString() {
        return "PushNotificationWrapper{" +
                "coreNotification=" + coreNotification +
                ", actor=" + actor +
                ", notificationItemsSize=" + notificationItems.size() +
                ", maxSize=" + maxQueueSize +
                ", timer=" + timer +
                '}';
    }
}

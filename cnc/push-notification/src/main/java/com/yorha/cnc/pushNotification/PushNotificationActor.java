package com.yorha.cnc.pushNotification;

import com.yorha.cnc.pushNotification.sender.firebase.FirebaseHelper;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.PushNotificationService;
import com.yorha.common.actor.PushNotificationServices;
import com.yorha.common.actor.node.IClusterConfigWatcherActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.utils.StringUtils;

import javax.annotation.Nullable;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 与sceneActor成1对1关系（减轻scene负担）
 *
 * <AUTHOR>
 */
public class PushNotificationActor extends BaseGameActor implements PushNotificationServices, IClusterConfigWatcherActor {
    private final PushNotificationServiceImpl service;
    private String md5;

    public PushNotificationActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.service = new PushNotificationServiceImpl(this);
        this.md5 = StringUtils.md5(FirebaseHelper.getFirebaseConfig().toString());
    }

    @Nullable
    public ActorTimer addRepeatTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(getId(), timerReasonType, runnable, initialDelay, period, unit, false);
    }

    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
        this.service.closeNotification();
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        this.dispatchProtoMsg(typedMsg);
    }

    @Override
    public PushNotificationService getPushNotificationService() {
        return this.service;
    }


    @Override
    public void handleClusterConfigReload() {
        final Map<String, String> config = FirebaseHelper.getFirebaseConfig();
        if (!this.md5.equals(StringUtils.md5(config.toString()))) {
            this.service.reloadCoreNotification();
            this.md5 = StringUtils.md5(config.toString());
        }
    }
}

pipeline {
    agent {
        label 'linux'
    }
    
    environment {
        CNC_WORLD_ID = "${params.WORLD_ID}"
        DOCKER_REGISTRY = '***********:5000'
        GRADLE_OPTS = '-Xms1g -Xmx4g -Dfile.encoding=UTF-8'
        JAVA_HOME = '/usr/local/TencentKona-21.0.7.b1'
    }
    
    parameters {
        string(name: 'WORLD_ID', defaultValue: '128', description: 'CNC World ID')
        choice(name: 'SERVER_BRANCH', choices: ['dev', 'master', 'release'], description: 'Server repository branch')
        choice(name: 'TOOLS_BRANCH', choices: ['master', 'dev', 'release'], description: 'Tools repository branch')
        choice(name: 'GAMEDATA_BRANCH', choices: ['main', 'dev', 'release'], description: 'Game data repository branch')
        booleanParam(name: 'SKIP_DOCKER_BUILD', defaultValue: false, description: 'Skip Docker image build for testing')
        booleanParam(name: 'SKIP_DOCKER_PUSH', defaultValue: false, description: 'Skip Docker image push for testing')
        booleanParam(name: 'SKIP_K8S_DEPLOY', defaultValue: false, description: 'Skip Kubernetes deployment for testing')
    }
    
    stages {
        stage('Environment Check') {
            steps {
                script {
                    echo "=== Environment Information ==="
                    echo "World ID: ${params.WORLD_ID}"
                    echo "Server branch: ${params.SERVER_BRANCH}"
                    echo "Tools branch: ${params.TOOLS_BRANCH}"
                    echo "Game data branch: ${params.GAMEDATA_BRANCH}"
                    echo "Skip Docker build: ${params.SKIP_DOCKER_BUILD}"
                    echo "Skip Docker push: ${params.SKIP_DOCKER_PUSH}"
                    echo "Skip K8s deploy: ${params.SKIP_K8S_DEPLOY}"
                    echo "=============================="
                    
                    echo "=== Current Directory ==="
                    sh 'pwd'
                    sh 'ls -la'
                    echo "========================="
                }
            }
        }
        
        stage('Checkout Code') {
            parallel {
                stage('Checkout Server') {
                    steps {
                        dir('server') {
                            checkout([
                                $class: 'GitSCM',
                                branches: [[name: "*/${params.SERVER_BRANCH}"]],
                                userRemoteConfigs: [[
                                    credentialsId: 'cnc_server_ci-gitlab',
                                    url: 'http://gitlab.oa.com/cnc_server/server.git'
                                ]]
                            ])
                            script {
                                def commitInfo = sh(script: 'git log -1 --oneline', returnStdout: true).trim()
                                echo "Server commit: ${commitInfo}"
                            }
                        }
                    }
                }
                stage('Checkout Tools') {
                    steps {
                        dir('tools') {
                            checkout([
                                $class: 'GitSCM',
                                branches: [[name: "*/${params.TOOLS_BRANCH}"]],
                                userRemoteConfigs: [[
                                    credentialsId: 'cnc_server_ci-gitlab',
                                    url: 'http://gitlab.oa.com/cnc_server/tools.git'
                                ]]
                            ])
                            script {
                                def commitInfo = sh(script: 'git log -1 --oneline', returnStdout: true).trim()
                                echo "Tools commit: ${commitInfo}"
                            }
                        }
                    }
                }
                stage('Checkout Game Data') {
                    steps {
                        dir('game_data') {
                            checkout([
                                $class: 'GitSCM',
                                branches: [[name: "*/${params.GAMEDATA_BRANCH}"]],
                                extensions: [[$class: 'GitLFSPull']],
                                userRemoteConfigs: [[
                                    credentialsId: 'cnc_server_ci-gitlab',
                                    url: 'http://gitlab.oa.com/cnc_dev/cnc.git'
                                ]]
                            ])
                            script {
                                def commitInfo = sh(script: 'git log -1 --oneline', returnStdout: true).trim()
                                echo "Game data commit: ${commitInfo}"
                            }
                        }
                    }
                }
            }
        }
        
        stage('Wait for Resource Loader Test') {
            steps {
                script {
                    echo "Waiting for cnc_resloadertest to complete..."
                    build job: 'cnc_resloadertest', 
                          wait: true,
                          propagate: true
                }
            }
        }
        
        stage('Build Application') {
            steps {
                dir('server/cnc') {
                    script {
                        def gitBranch = sh(script: 'git rev-parse --abbrev-ref HEAD', returnStdout: true).trim()
                        def gitCommit = sh(script: 'git rev-parse --short=8 HEAD', returnStdout: true).trim()
                        def gitCount = sh(script: 'git rev-list --count HEAD', returnStdout: true).trim()
                        
                        echo "Git Branch: ${gitBranch}"
                        echo "Git Commit: ${gitCommit}"
                        echo "Git Count: ${gitCount}"
                        
                        sh '''
                            export GRADLE_OPTS="${GRADLE_OPTS}"
                            chmod +x gradlew
                            ./gradlew clean build \
                                --no-daemon \
                                --parallel \
                                --build-cache \
                                --configuration-cache \
                                -x test \
                                -x checkstyleMain \
                                -x checkstyleTest \
                                -x checkstyleJmh \
                                -x pmdMain \
                                -x pmdTest \
                                -x pmdJmh \
                                -x spotbugsMain \
                                -x spotbugsTest \
                                -x spotbugsJmh
                        '''
                    }
                }
            }
        }
        
        stage('Copy Artifacts') {
            steps {
                script {
                    echo "Copying build artifacts..."
                    sh '''
                        echo "copy jar and so on"
                        # 检查构建产物
                        echo "=== Build artifacts ==="
                        find server/cnc -name "*.jar" -type f | head -10
                        echo "======================"
                    '''
                }
            }
        }
        
        stage('Prepare Docker Build') {
            when {
                not {
                    equals expected: true, actual: params.SKIP_DOCKER_BUILD
                }
            }
            steps {
                script {
                    echo "=== Preparing Docker Build Environment ==="
                    
                    // 检查当前目录结构
                    sh '''
                        echo "Current workspace structure:"
                        ls -la
                        echo ""
                        echo "Checking for Dockerfile variants:"
                        find . -name "*ockerfile*" -o -name "*ockerFile*" -o -name "Dockerfile*" 2>/dev/null || echo "No Dockerfile found"
                        echo ""
                    '''
                    
                    // 检查是否有必要的构建文件和目录
                    sh '''
                        echo "Checking required directories:"
                        echo "- server/cnc/build: $(test -d server/cnc/build && echo 'exists' || echo 'missing')"
                        echo "- tools: $(test -d tools && echo 'exists' || echo 'missing')"
                        echo "- game_data: $(test -d game_data && echo 'exists' || echo 'missing')"
                        echo ""
                    '''
                    
                    // 创建基础的Dockerfile（如果不存在）
                    def dockerfileExists = sh(script: 'test -f Dockerfile && echo "exists" || echo "missing"', returnStdout: true).trim()
                    
                    if (dockerfileExists == "missing") {
                        echo "Creating basic Dockerfile for build..."
                        sh '''
                            cat > Dockerfile << 'EOF'
FROM centos:centos7

# 安装基础工具
RUN curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo || \\
    curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.cloud.tencent.com/repo/centos7_base.repo

RUN yum install -y tzdata openssl curl telnet dos2unix traceroute nc ca-certificates fontconfig gzip net-tools.x86_64 lrzsz vim unzip zip tar epel-release sshpass rsync && \\
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \\
    echo "Asia/Shanghai" > /etc/timezone

# 添加JDK（如果存在）
ADD software/TencentKona-21.0.7.b1-jdk_linux-x86_64.tar.gz /usr/local/jdk/ || echo "JDK not found, skipping"

# 根据BUILD_CONTEXT复制不同的运行文件
ARG BUILD_CONTEXT=dirsvr
ADD run/${BUILD_CONTEXT} /cnc/run/server/${BUILD_CONTEXT} || echo "Run directory not found, creating empty"
RUN mkdir -p /cnc/run/server/${BUILD_CONTEXT}

WORKDIR /cnc/run/server/${BUILD_CONTEXT}
EOF
                        '''
                        echo "Basic Dockerfile created"
                    } else {
                        echo "Dockerfile already exists"
                    }
                }
            }
        }
        
        stage('Build Docker Images') {
            when {
                not {
                    equals expected: true, actual: params.SKIP_DOCKER_BUILD
                }
            }
            parallel {
                stage('Build DirSvr Image') {
                    steps {
                        script {
                            try {
                                sh '''
                                    echo "Building DirSvr image..."
                                    docker build -t docker-dirsvr \\
                                        --build-arg BUILD_CONTEXT=dirsvr \\
                                        . || echo "DirSvr build failed, but continuing..."
                                '''
                                echo "✅ DirSvr image build completed"
                            } catch (Exception e) {
                                echo "❌ DirSvr image build failed: ${e.getMessage()}"
                            }
                        }
                    }
                }
                stage('Build GameSvr Image') {
                    steps {
                        script {
                            try {
                                sh '''
                                    echo "Building GameSvr image..."
                                    docker build -t docker-gamesvr \\
                                        --build-arg BUILD_CONTEXT=gamesvr \\
                                        . || echo "GameSvr build failed, but continuing..."
                                '''
                                echo "✅ GameSvr image build completed"
                            } catch (Exception e) {
                                echo "❌ GameSvr image build failed: ${e.getMessage()}"
                            }
                        }
                    }
                }
                stage('Build GameData Image') {
                    steps {
                        script {
                            try {
                                sh '''
                                    echo "Building GameData image..."
                                    docker build -t docker-gamedata \\
                                        --build-arg BUILD_CONTEXT=gamedata \\
                                        . || echo "GameData build failed, but continuing..."
                                '''
                                echo "✅ GameData image build completed"
                            } catch (Exception e) {
                                echo "❌ GameData image build failed: ${e.getMessage()}"
                            }
                        }
                    }
                }
            }
        }
        
        stage('Push Docker Images') {
            when {
                allOf {
                    not {
                        equals expected: true, actual: params.SKIP_DOCKER_BUILD
                    }
                    not {
                        equals expected: true, actual: params.SKIP_DOCKER_PUSH
                    }
                }
            }
            parallel {
                stage('Push DirSvr Image') {
                    steps {
                        script {
                            def branchSuffix = params.SERVER_BRANCH == 'master' ? '' : "-${params.SERVER_BRANCH}"
                            def imageTag = "${DOCKER_REGISTRY}/cnc/dirsvr${branchSuffix}:latest"
                            echo "Pushing image: ${imageTag}"
                            
                            try {
                                sh """
                                    docker tag docker-dirsvr ${imageTag}
                                    docker push ${imageTag}
                                """
                                echo "✅ DirSvr image pushed successfully"
                            } catch (Exception e) {
                                echo "❌ DirSvr image push failed: ${e.getMessage()}"
                            }
                        }
                    }
                }
                stage('Push GameSvr Image') {
                    steps {
                        script {
                            def branchSuffix = params.SERVER_BRANCH == 'master' ? '' : "-${params.SERVER_BRANCH}"
                            def imageTag = "${DOCKER_REGISTRY}/cnc/gamesvr${branchSuffix}:latest"
                            echo "Pushing image: ${imageTag}"
                            
                            try {
                                sh """
                                    docker tag docker-gamesvr ${imageTag}
                                    docker push ${imageTag}
                                """
                                echo "✅ GameSvr image pushed successfully"
                            } catch (Exception e) {
                                echo "❌ GameSvr image push failed: ${e.getMessage()}"
                            }
                        }
                    }
                }
                stage('Push GameData Image') {
                    steps {
                        script {
                            def branchSuffix = params.GAMEDATA_BRANCH == 'main' ? '' : "-${params.GAMEDATA_BRANCH}"
                            def imageTag = "${DOCKER_REGISTRY}/cnc/gamedata${branchSuffix}:latest"
                            echo "Pushing image: ${imageTag}"
                            
                            try {
                                sh """
                                    docker tag docker-gamedata ${imageTag}
                                    docker push ${imageTag}
                                """
                                echo "✅ GameData image pushed successfully"
                            } catch (Exception e) {
                                echo "❌ GameData image push failed: ${e.getMessage()}"
                            }
                        }
                    }
                }
            }
        }
        
        stage('Deploy to Kubernetes') {
            when {
                not {
                    equals expected: true, actual: params.SKIP_K8S_DEPLOY
                }
            }
            steps {
                script {
                    echo "Starting Kubernetes deployment..."
                    
                    sh '''
                        kubectl delete service dirsvr-${CNC_WORLD_ID} || true
                        kubectl delete service zone-${CNC_WORLD_ID} || true
                        kubectl delete service global-${CNC_WORLD_ID} || true
                        kubectl delete service idip-${CNC_WORLD_ID} || true
                        kubectl delete pod dirsvr-${CNC_WORLD_ID} || true
                        kubectl delete pod zone-${CNC_WORLD_ID} || true
                        kubectl delete pod global-${CNC_WORLD_ID} || true
                        kubectl delete pod idip-${CNC_WORLD_ID} || true
                    '''
                    
                    def worldId = env.CNC_WORLD_ID as Integer
                    def dirPort = 30800 + worldId + 11
                    def zonePort = 30800 + worldId + 42
                    def globalPort = 30800 + worldId + 51
                    def idipPort = 30800 + worldId + 31
                    
                    echo "Port configuration:"
                    echo "dirport: ${dirPort}"
                    echo "zoneport: ${zonePort}"
                    echo "globalport: ${globalPort}"
                    echo "idipport: ${idipPort}"
                    
                    def serverBranchSuffix = params.SERVER_BRANCH == 'master' ? '' : "-${params.SERVER_BRANCH}"
                    
                    sh """
                        kubectl create service nodeport dirsvr-${CNC_WORLD_ID} --tcp=8080:8080 --node-port=${dirPort}
                        kubectl create service nodeport zone-${CNC_WORLD_ID} --tcp=8080:8080 --node-port=${zonePort}
                        kubectl create service nodeport global-${CNC_WORLD_ID} --tcp=8080:8080 --node-port=${globalPort}
                        kubectl create service nodeport idip-${CNC_WORLD_ID} --tcp=8080:8080 --node-port=${idipPort}
                        
                        kubectl run dirsvr-${CNC_WORLD_ID} --image=${DOCKER_REGISTRY}/cnc/dirsvr${serverBranchSuffix}:latest
                        kubectl run zone-${CNC_WORLD_ID} --image=${DOCKER_REGISTRY}/cnc/gamesvr${serverBranchSuffix}:latest
                        kubectl run global-${CNC_WORLD_ID} --image=${DOCKER_REGISTRY}/cnc/gamesvr${serverBranchSuffix}:latest
                        kubectl run idip-${CNC_WORLD_ID} --image=${DOCKER_REGISTRY}/cnc/gamesvr${serverBranchSuffix}:latest
                        
                        kubectl patch service dirsvr-${CNC_WORLD_ID} -p '{"spec":{"selector":{"run":"dirsvr-${CNC_WORLD_ID}"}}}'
                        kubectl patch service zone-${CNC_WORLD_ID} -p '{"spec":{"selector":{"run":"zone-${CNC_WORLD_ID}"}}}'
                        kubectl patch service global-${CNC_WORLD_ID} -p '{"spec":{"selector":{"run":"global-${CNC_WORLD_ID}"}}}'
                        kubectl patch service idip-${CNC_WORLD_ID} -p '{"spec":{"selector":{"run":"idip-${CNC_WORLD_ID}"}}}'
                    """
                    
                    sh '''
                        echo "Waiting for pod to be available..."
                        kubectl wait --for=condition=Ready pod/dirsvr-${CNC_WORLD_ID} --timeout=300s || echo "DirSvr pod timeout"
                        kubectl wait --for=condition=Ready pod/zone-${CNC_WORLD_ID} --timeout=300s || echo "Zone pod timeout"
                        kubectl wait --for=condition=Ready pod/global-${CNC_WORLD_ID} --timeout=300s || echo "Global pod timeout"
                        kubectl wait --for=condition=Ready pod/idip-${CNC_WORLD_ID} --timeout=300s || echo "Idip pod timeout"
                    '''
                    
                    echo "Kubernetes deployment completed!"
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "=== Build Summary ==="
                echo "Server branch: ${params.SERVER_BRANCH}"
                echo "Tools branch: ${params.TOOLS_BRANCH}"
                echo "Game data branch: ${params.GAMEDATA_BRANCH}"
                echo "World ID: ${params.WORLD_ID}"
                echo "Docker build skipped: ${params.SKIP_DOCKER_BUILD}"
                echo "Docker push skipped: ${params.SKIP_DOCKER_PUSH}"
                echo "K8s deploy skipped: ${params.SKIP_K8S_DEPLOY}"
                echo "===================="
            }
            cleanWs()
        }
        success {
            echo '🎉 Pipeline completed successfully!'
        }
        failure {
            echo '❌ Pipeline failed! Check the logs above for details.'
        }
    }
}

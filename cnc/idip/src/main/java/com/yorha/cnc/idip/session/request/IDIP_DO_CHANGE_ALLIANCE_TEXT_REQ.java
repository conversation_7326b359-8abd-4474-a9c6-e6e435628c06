package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsClanAttr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 重置联盟信息
 *
 * <AUTHOR>
 */
public class IDIP_DO_CHANGE_ALLIANCE_TEXT_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_CHANGE_ALLIANCE_TEXT_REQ.class);


    /**
     * clanId 联盟id
     * zoneId 小服id
     */

    public long ClanId;
    public int Name;
    public int Describe;
    public int ClanShortName;
    public int Interval;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (ClanId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "ClanId <= 0");
        }
        if (Name < 0 || Name > 1) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Name non-bool type");
        }
        if (Describe < 0 || Describe > 1) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Describe non-bool type");
        }
        if (ClanShortName < 0 || ClanShortName > 1) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "ClanShortName non-bool type");
        }
        if (ClanShortName == 0 && Describe == 0 && Name == 0) {
            return Result.error(IdIpErrorCode.IDIP_KEY_PARAMETER_MISSING, "no ClanShortName or Describe or Name");
        }

        if (Interval <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Interval <= 0");
        }
        try {
            SsClanAttr.IDIPResetClanInfoAsk build = SsClanAttr.IDIPResetClanInfoAsk.newBuilder()
                    .setResetClanName(isTrue(Name))
                    .setResetClanDescribe(isTrue(Describe))
                    .setResetClanShortName(isTrue(ClanShortName))
                    .setQuietSecondTime(Interval)
                    .build();
            actor.callClan(Partition, ClanId, build);
            return Result.success();
        } catch (GeminiException e) {
            LOGGER.error("IDIP_DO_CHANGE_ALLIANCE_TEXT_REQ fail, ", e);
            return Result.error(IdIpErrorCode.IDIP_CLAN_IS_NOT_EXIST, e.getMessage());
        }
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;

/**
 * 取消跑马灯
 *
 * <AUTHOR>
 */

public class CancelMarquee extends BaseRequest {

    public int MarqueeId;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition == 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
//        SsMarquee.CancelMarqueeCmd build = SsMarquee.CancelMarqueeCmd.newBuilder().setMarqueeId(MarqueeId).build();
//        actor.callMarquee(Partition, build);
        return Result.success();
    }
}

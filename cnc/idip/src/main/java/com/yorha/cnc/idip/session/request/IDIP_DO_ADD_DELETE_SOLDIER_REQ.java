package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.SoldierInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerIdip;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SoldierTypeTemplate;

/**
 * 个人士兵增删
 *
 * <AUTHOR>
 */

public class IDIP_DO_ADD_DELETE_SOLDIER_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_ADD_DELETE_SOLDIER_REQ.class);
    public int Partition;
    public String openid;
    public Long RoleId;
    public int SoldierId;
    public int Value;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(openid)) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "OpenId is invalid");
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "RoleId is invalid");
        }
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }
        if (Value == 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Value is 0");
        }
        SoldierTypeTemplate template = ResHolder.findTemplate(SoldierTypeTemplate.class, SoldierId);
        if (template == null) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Soldier_id not exist");
        }
        Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
        if (result != null) {
            return result;
        }
        LOGGER.info("IDIP_DO_ADD_DELETE_SOLDIER_REQ start, ZoneID={}, playerId={}, Soldier_id={}, Value={}", Partition, RoleId, SoldierId, Value);

        try {
            SsPlayerIdip.ModifySoldierAsk.Builder build = SsPlayerIdip.ModifySoldierAsk.newBuilder()
                    .setPlayerId(RoleId)
                    .setSoldierId(SoldierId)
                    .setOpenId(openid)
                    .setValue(Value);
            SsPlayerIdip.ModifySoldierAns ans = actor.callPlayer(Partition, RoleId, build.build());
            if (ans.getExceptionId() > 0 && ans.getExceptionId() == ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()){
                return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
            }
            return Result.success(new SoldierInfo(ans.getBeforeValue(), ans.getAfterValue()));
        } catch (Exception e) {
            LOGGER.error("IDIP_DO_ADD_DELETE_SOLDIER_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

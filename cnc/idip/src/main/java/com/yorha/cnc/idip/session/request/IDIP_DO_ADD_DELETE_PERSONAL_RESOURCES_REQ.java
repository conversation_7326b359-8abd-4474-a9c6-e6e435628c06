package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ResourceInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsPlayerIdip;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 个人资源增删
 *
 * <AUTHOR>
 */

public class IDIP_DO_ADD_DELETE_PERSONAL_RESOURCES_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_ADD_DELETE_PERSONAL_RESOURCES_REQ.class);
    public int Partition;
    public String openid;
    public Long RoleId;
    public int ResourceType;
    public int Value;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(openid)) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "OpenId is invalid");
        }
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "RoleId is invalid");
        }
        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }
        CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(ResourceType);
        if (currencyType == null || currencyType == CommonEnum.CurrencyType.CT_None || currencyType == CommonEnum.CurrencyType.DIAMOND) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Resource_type invalid");
        }
        Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
        if (result != null) {
            return result;
        }
        LOGGER.info("IDIP_DO_ADD_DELETE_PERSONAL_RESOURCES_REQ start, ZoneID={}, playerId={}, Resource_type={}, Value={}", Partition, RoleId, ResourceType, Value);
        try {
            SsPlayerIdip.ResourceAsk.Builder build = SsPlayerIdip.ResourceAsk.newBuilder()
                    .setOpenId(openid)
                    .setCurrencyType(currencyType)
                    .setValue(Value);
            SsPlayerIdip.ResourceAns ans = actor.callPlayer(Partition, RoleId, build.build());
            if (ans.getExceptionId() > 0 && ans.getExceptionId() == ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()){
                return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
            }
            return Result.success(new ResourceInfo(ans.getBeforeValue(), ans.getAfterValue()));
        } catch (Exception e) {
            LOGGER.error("IDIP_DO_ADD_DELETE_PERSONAL_RESOURCES_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

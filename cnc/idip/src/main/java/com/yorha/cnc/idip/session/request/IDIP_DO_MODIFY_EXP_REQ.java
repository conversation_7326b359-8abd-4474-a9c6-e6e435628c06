package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ModifyVipExpInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerIdip;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 修改VIP经验(单扣)
 * <AUTHOR>
 */
public class IDIP_DO_MODIFY_EXP_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_MODIFY_EXP_REQ.class);

    /**
     * 区服id
     */
    public int Partition;
    /**
     * 账号id
     */
    public String openid;
    /**
     * 角色id
     */
    public long RoleId;
    /**
     * 修改值，负数代表扣减
     */
    public int Value;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(openid)) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "OpenId == null");
        }
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "RoleId <= 0");
        }
        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }
        if (Value <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "value <= 0");
        }
        Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
        if (result != null) {
            return result;
        }

        LOGGER.info("IDIP_DO_MODIFY_EXP_REQ begin, ZoneID={}, playerId={}, openid={}, value={}", Partition, RoleId, openid, Value);

        // 需求：该接口只允许扣经验
        SsPlayerIdip.ModifyVipExpAsk ask = SsPlayerIdip.ModifyVipExpAsk.newBuilder()
                .setOpenId(openid)
                .setValue(Value)
                .build();
        try{
            SsPlayerIdip.ModifyVipExpAns ans = actor.callPlayer(Partition, RoleId, ask);
            LOGGER.info("IDIP_DO_MODIFY_EXP_REQ end ans={}", ans);
            if (ans.getExceptionId() > 0 && ans.getExceptionId() == ErrorCode.IDIP_PLAYER_NOT_EXIST.getCodeId()){
                return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
            }
            return Result.success(new ModifyVipExpInfo(ans.getBeforeLevel(), ans.getAfterLevel(), ans.getBeforeExp(), ans.getAfterExp()));
        } catch (Exception e) {
            LOGGER.error("IDIP_DO_MODIFY_EXP_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

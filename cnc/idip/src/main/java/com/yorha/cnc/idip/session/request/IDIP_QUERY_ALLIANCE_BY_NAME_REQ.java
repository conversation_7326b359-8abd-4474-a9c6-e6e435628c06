package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ClanInfo;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.SsName;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IDIP_QUERY_ALLIANCE_BY_NAME_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_QUERY_ALLIANCE_BY_NAME_REQ.class);

    public int Partition;
    public String ClanName;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(ClanName)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "ClanName is null");
        }
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        try {
            SsName.SearchNameMatchedAsk searchPlayerNameAsk = SsName.SearchNameMatchedAsk.newBuilder()
                    .setName(ClanName)
                    .setNameType(CommonEnum.NameType.CLAN_NAME)
                    .build();
            SsName.SearchNameMatchedAns searchAns = actor.callName(Partition, searchPlayerNameAsk);
            if (searchAns.getOwnerId() == 0) {
                return Result.error(IdIpErrorCode.IDIP_CLAN_IS_NOT_EXIST);
            }
            // 向clanActor获取数据
            SsClanAttr.IDIPQueryClanInfoAsk.Builder ask = SsClanAttr.IDIPQueryClanInfoAsk.newBuilder();
            SsClanAttr.IDIPQueryClanInfoAns ans = actor.callClan(Partition, searchAns.getOwnerId(), ask.build());
            return Result.success(new ClanInfo(ans, Partition));

        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return Result.error(IdIpErrorCode.IDIP_CLAN_IS_NOT_EXIST);
    }

}


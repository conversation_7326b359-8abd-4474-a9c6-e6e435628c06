package com.yorha.cnc.idip.session;

import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.common.reflections.JavaClassScanner;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class RequestProcessManager {
    private static final Logger LOGGER = LogManager.getLogger(RequestProcessManager.class);

    private static class LazyHolder {
        private static final RequestProcessManager INSTANCE = new RequestProcessManager();
    }

    private static Map<String, Class<? extends BaseRequest>> reqClass = new HashMap<>();

    public static RequestProcessManager getInstance() {
        return RequestProcessManager.LazyHolder.INSTANCE;
    }

    private RequestProcessManager() {
        final JavaClassScanner scanner = new JavaClassScanner();
        Set<Class<? extends BaseRequest>> subTypesOf = scanner.getSubTypesOf("com.yorha.cnc.idip.session.request", BaseRequest.class);
        for (Class<? extends BaseRequest> clazz : subTypesOf) {
            String name = clazz.getSimpleName();
            char[] chars = name.toCharArray();
            chars[0] += 32;
            reqClass.put(String.valueOf(chars), clazz);
        }
        LOGGER.info("RequestProcessManager init end, {}", reqClass);
    }

    public Class<? extends BaseRequest> getReqClass(String name) {
        return reqClass.get(name);
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.PlayerInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 根据玩家名称查询玩家信息
 *
 * <AUTHOR>
 */
public class IDIP_QUERY_ROLE_BY_NAME_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_QUERY_ROLE_BY_NAME_REQ.class);
    /**
     * 玩家名称
     */
    public String RoleName;
    /**
     * zoneId
     */
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (StringUtils.isEmpty(RoleName)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleName is null");
        }
        long playerId = PlayerIdIpHelper.getPlayerIdsByName(actor, RoleName, Partition);
        if (playerId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        try {
            SsPlayerMisc.GetIdIpPlayerInfoAns ans = actor.callPlayer(Partition, playerId, SsPlayerMisc.GetIdIpPlayerInfoAsk.getDefaultInstance());
            return Result.success(new PlayerInfo(ans));
        } catch (GeminiException e) {
            LOGGER.error("IDIP_QUERY_ROLE_BY_NAME_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }

    }
}

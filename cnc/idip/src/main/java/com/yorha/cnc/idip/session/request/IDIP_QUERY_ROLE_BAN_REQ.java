package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.BanInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 根据玩家id查询玩家封禁信息
 *
 * <AUTHOR>
 */

public class IDIP_QUERY_ROLE_BAN_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_QUERY_ROLE_BAN_REQ.class);
    public long RoleId;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleId <= 0");
        }
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        try {
            Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
            if (result != null) {
                return result;
            }
            SsPlayerMisc.GetIdIpPlayerInfoAns playerInfoAns = actor.callPlayer(Partition, RoleId, SsPlayerMisc.GetIdIpPlayerInfoAsk.getDefaultInstance());
            return Result.success(new BanInfo(playerInfoAns));
        } catch (GeminiException e) {
            LOGGER.error("IDIP_QUERY_ROLE_BY_ROLEID_REQ fail, ", e);
        }
        return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST, "no matching role");
    }
}

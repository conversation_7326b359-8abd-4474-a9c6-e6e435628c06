package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsScenePlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 遣返行军
 *
 * <AUTHOR>
 */

public class IDIP_DO_FORCED_BACK_CITY_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_FORCED_BACK_CITY_REQ.class);

    /**
     * PlayerId
     */
    public String openid;
    public int Partition;
    public Long RoleId;
    public Long Source; // 渠道号
    public String Serial; // 流水号

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (StringUtils.isEmpty(openid)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "openId is null");
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleId <= 0");
        }
        Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
        if (result != null) {
            return result;
        }
        try {
            actor.callScene(Partition, SsScenePlayer.IdIpReturnAllArmyAsk.newBuilder().setPlayerId(RoleId).build());
            return Result.success();
        } catch (GeminiException e) {
            LOGGER.error("IDIP_DO_FORCED_BACK_CITY_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

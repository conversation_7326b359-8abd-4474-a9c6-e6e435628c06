package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 重置玩家信息（昵称和头像）
 *
 * <AUTHOR>
 */
public class IDIP_DO_CHANGE_CHARACTER_TEXT_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_CHANGE_CHARACTER_TEXT_REQ.class);

    /**
     * playerIdList: 批量重置信息的玩家ID列表
     * nickname: 是否重置玩家昵称
     * header: 是否重置玩家头像
     * interval: 静默时长
     */
    public Long RoleId;
    public int Nickname;
    public int Header;
    public int Interval;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (Nickname < 0 || Nickname > 1) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Nickname non-bool type");
        }
        if (Header < 0 || Header > 1) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Header non-bool type");
        }
        if (!isTrue(Nickname) && !isTrue(Header)) {
            return Result.error(IdIpErrorCode.IDIP_KEY_PARAMETER_MISSING, "no Nickname or no Header");
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleId <= 0");
        }
        if (Interval <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Interval <= 0");
        }
        try {
            Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
            if (result != null) {
                return result;
            }
            SsPlayerMisc.ResetPlayerInfoAsk build = SsPlayerMisc.ResetPlayerInfoAsk.newBuilder()
                    .setResetName(isTrue(Nickname))
                    .setResetPic(isTrue(Header))
                    .setQuietSecondTime(Interval)
                    .build();
            actor.callPlayer(Partition, RoleId, build);
        } catch (
                GeminiException e) {
            LOGGER.error("IDIP_DO_PROHIBIT_SPEAKING_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }


        return Result.success();
    }
}

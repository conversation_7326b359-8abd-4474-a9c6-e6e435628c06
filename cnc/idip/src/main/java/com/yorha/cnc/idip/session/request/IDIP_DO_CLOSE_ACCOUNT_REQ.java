package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.UpsertResult;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayer;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * @author: zpr
 * Date: 2023/8/7
 * Description: 封openid账号
 */
public class IDIP_DO_CLOSE_ACCOUNT_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_CLOSE_ACCOUNT_REQ.class);

    public String OpenId;
    public long BanTime;
    public String Reason;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(OpenId)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "OpenId == null");
        }

        if (BanTime < -1) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "BanTime < -1");
        }

        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }

        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }

        if (StringUtils.isEmpty(Reason)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Reason == null");
        }

        LOGGER.info("IDIP_DO_CLOSE_ACCOUNT_REQ ban Account begin, openid={}, banTime={}s", OpenId, BanTime);

        long banEndTimeMs = BanTime == -1 ? -1 : (SystemClock.nowNative() + BanTime * 1000);
        // upsert封号表
        CommonMsg.OpenIdBanInfo.Builder banInfo = CommonMsg.OpenIdBanInfo.newBuilder();
        banInfo.setIsBanned(true)
                .setBanBeginTimeMs(SystemClock.nowNative())
                .setBanEndTimeMs(banEndTimeMs)
                .setBanReason(Reason)
                .build();
        TcaplusDb.OpenIdInfoTable.Builder upsertReq = TcaplusDb.OpenIdInfoTable.newBuilder();
        upsertReq.setOpenId(OpenId).setBanInfo(banInfo);
        try {
            UpsertResult<TcaplusDb.OpenIdInfoTable.Builder> upsertRes = actor.callGameDb(new UpsertAsk<>(upsertReq, UpsertOption.getDefaultInstance()));
            if (!upsertRes.isOk()) {
                LOGGER.error("IDIP_DO_CLOSE_ACCOUNT_REQ db fail, code={}", upsertRes.getCode());
                return Result.error(IdIpErrorCode.ERROR);
            }
        } catch (Exception e) {
            return Result.error(IdIpErrorCode.ERROR);
        }
        LOGGER.info("IDIP_DO_CLOSE_ACCOUNT_REQ ban Account success, openid={}, banTime={}s", OpenId, BanTime);

        // get角色，踢人
        List<Pair<Long, Integer>> playerId2ZoneList = PlayerIdIpHelper.getPlayerIdsByOpenId(actor, OpenId);
        // DB操作失败
        if (playerId2ZoneList == null) {
            return Result.error(IdIpErrorCode.ERROR);
        }
        // 无角色
        if (playerId2ZoneList.isEmpty()) {
            LOGGER.info("IDIP_DO_CLOSE_ACCOUNT_REQ kick out but account do not exist character, openid={}", OpenId);
            return Result.success();
        }

        for (Pair<Long, Integer> value : playerId2ZoneList) {
            int zoneId = value.getSecond();
            long playerId = value.getFirst();
            actor.tellPlayer(zoneId, playerId, SsPlayer.KickOffPlayerCmd.newBuilder().setCloseReason(CommonEnum.SessionCloseReason.SCR_ACCOUNT_BAN).build());
            LOGGER.info("IDIP_DO_CLOSE_ACCOUNT_REQ kick out character, openid={}, zoneId={}, playerId={}", OpenId, zoneId, playerId);
        }

        return Result.success();
    }
}

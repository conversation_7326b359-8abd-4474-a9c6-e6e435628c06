package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @author: zpr
 * Date: 2023/8/7
 * Description: 解封openId账号
 */
public class IDIP_DO_OPEN_ACCOUNT_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_OPEN_ACCOUNT_REQ.class);

    public String OpenId;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(OpenId)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "OpenId == null");
        }

        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }

        LOGGER.info("IDIP_DO_OPEN_ACCOUNT_REQ begin, openid={}", OpenId);

        // update 封号表
        TcaplusDb.OpenIdInfoTable.Builder updateReq = TcaplusDb.OpenIdInfoTable.newBuilder();
        CommonMsg.OpenIdBanInfo.Builder openInfo = CommonMsg.OpenIdBanInfo.newBuilder();
        openInfo.setIsBanned(false).setBanEndTimeMs(SystemClock.nowNative());
        updateReq.setOpenId(OpenId).setBanInfo(openInfo);
        try{
            UpdateResult<TcaplusDb.OpenIdInfoTable.Builder> updateRes = actor.callGameDb(new UpdateAsk<>(updateReq));
            if (updateRes.isRecordNotExist()){
                LOGGER.info("IDIP_DO_OPEN_ACCOUNT_REQ OpenIdInfoTable record not exist, openid={}", OpenId);
                return Result.success();
            }
            if (!updateRes.isOk()) {
                LOGGER.error("IDIP_DO_OPEN_ACCOUNT_REQ db fail, code={}", updateRes.getCode());
                return Result.error(IdIpErrorCode.ERROR);
            }
        } catch (Exception e) {
            return Result.error(IdIpErrorCode.ERROR);
        }

        LOGGER.info("IDIP_DO_OPEN_ACCOUNT_REQ success, openid={}", OpenId);
        return Result.success();
    }
}

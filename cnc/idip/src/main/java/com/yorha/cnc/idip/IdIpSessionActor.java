package com.yorha.cnc.idip;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.cnc.idip.session.RequestProcessManager;
import com.yorha.cnc.idip.session.common.*;
import com.yorha.cnc.idip.session.request.*;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.gemini.actor.msg.TypedMsg;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.handler.codec.http.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class IdIpSessionActor extends GameActorWithCall {
    private static final Map<Integer, Class<? extends BaseRequest>> ID_IP_POST_METHOD;

    static {
        Map<Integer, Class<? extends BaseRequest>> idIpPostMethodMod = new HashMap<>();
        //自己内部使用的
        idIpPostMethodMod.put(1001, IDIP_QUERY_SERVER_INFO_REQ.class);
        idIpPostMethodMod.put(2003, IDIP_EXECUTE_SCRIPT_REQ.class);
        // 腾讯测接口
        idIpPostMethodMod.put(4097, IDIP_DO_SEND_MAIL_REQ.class);
        idIpPostMethodMod.put(4099, IDIP_DO_SEND_ITEM_REQ.class);
        idIpPostMethodMod.put(4101, IDIP_DO_CHANGE_CHARACTER_TEXT_REQ.class);
        idIpPostMethodMod.put(4103, IDIP_DO_CHANGE_ALLIANCE_TEXT_REQ.class);
        idIpPostMethodMod.put(4105, IDIP_DO_BAN_CHARACTER_REQ.class);
        idIpPostMethodMod.put(4107, IDIP_DO_RELEASE_CHARACTER_REQ.class);
        idIpPostMethodMod.put(4109, IDIP_DO_PROHIBIT_SPEAKING_REQ.class);
        idIpPostMethodMod.put(4111, IDIP_DO_SEND_ITEM_PARITION_REQ.class);
        idIpPostMethodMod.put(4113, IDIP_DO_SEND_MAIL_PARTITION_REQ.class);
        idIpPostMethodMod.put(4117, IDIP_QUERY_ROLE_BY_ROLEID_REQ.class);
        idIpPostMethodMod.put(4119, IDIP_QUERY_ROLE_BY_NAME_REQ.class);
        idIpPostMethodMod.put(4121, IDIP_QUERY_ACCOUNT_INFO_REQ.class);
        idIpPostMethodMod.put(4123, IDIP_QUERY_ALLIANCE_BY_ID_REQ.class);
        idIpPostMethodMod.put(4125, IDIP_QUERY_ALLIANCE_BY_NAME_REQ.class);
        idIpPostMethodMod.put(4127, IDIP_QUERY_ROLE_BAN_REQ.class);
        idIpPostMethodMod.put(4129, IDIP_DO_UNMUTE_REQ.class);

        idIpPostMethodMod.put(4131, IDIP_DO_FORCED_BACK_CITY_REQ.class);
        idIpPostMethodMod.put(4137, IDIP_DO_FORCED_OPEN_HOOD_REQ.class);
        idIpPostMethodMod.put(4139, IDIP_DO_FULL_OEPN_HOOD_REQ.class);
        idIpPostMethodMod.put(4141, IDIP_DO_CLOSE_ACCOUNT_REQ.class);
        idIpPostMethodMod.put(4143, IDIP_DO_OPEN_ACCOUNT_REQ.class);
        idIpPostMethodMod.put(4145, IDIP_DO_DELETE_GOLD_REQ.class);
        idIpPostMethodMod.put(4147, IDIP_DO_DELETE_ITEMS_REQ.class);
        idIpPostMethodMod.put(4149, IDIP_DO_MASTER_NOTIFY_BALANCE_REQ.class);
        idIpPostMethodMod.put(4151, IDIP_QUERY_LOGIN_TIME_REQ.class);
        idIpPostMethodMod.put(4153, IDIP_DO_DELETE_ACCOUNT_REQ.class);

        // 三期（全服跑马灯、全服通知栏消息、增删资源、增删士兵、修改vip经验）
        idIpPostMethodMod.put(4155, IDIP_DO_FULL_SERVER_MARQUEE_REQ.class);
        idIpPostMethodMod.put(4157, IDIP_DO_SYSTEM_BOX_REQ.class);
        idIpPostMethodMod.put(4159, IDIP_DO_ADD_DELETE_PERSONAL_RESOURCES_REQ.class);
        idIpPostMethodMod.put(4161, IDIP_DO_ADD_DELETE_SOLDIER_REQ.class);
        idIpPostMethodMod.put(4163, IDIP_DO_MODIFY_EXP_REQ.class);


        ID_IP_POST_METHOD = Collections.unmodifiableMap(idIpPostMethodMod);
    }

    private static Class<? extends BaseRequest> getPostMethod(int cmd) {
        return ID_IP_POST_METHOD.get(cmd);
    }

    public IdIpSessionActor(ActorSystem system, IActorRef self) {
        super(system, self);
    }

    private Channel channel;
    private FullHttpRequest req;
    private FullHttpResponse resp;

    public void handleHttpRequestMsg(Channel channel, FullHttpRequest req, String data, FullHttpResponse resp) {
        this.channel = channel;
        this.req = req;
        this.resp = resp;
        processRequest(data);
    }

    public void processRequest(String data) {
        LOGGER.info("{} http req uri={} jsonData={}", this, req.uri(), data);
        BaseRequest requestMethod;
        IdIpHead resHead;
        IdIpHead reqHead;
        if (req.method() == HttpMethod.GET) {
            // get
            // 获取请求数据，获取不到会直接返回错误码
            Map<String, Object> dataMap = getDataPacket();
            if (dataMap == null) {
                return;
            }

            // 获取请求头，获取不到会直接返回错误码
            reqHead = getIdIpHead(dataMap);
            if (reqHead == null) {
                return;
            }

            resHead = reqHead.getResponseHead();

            // 获取具体的请求Data，获取不到会直接返回错误码
            Class<? extends BaseRequest> baseRequest = getRequestMethod(resHead, reqHead);

            try {
                requestMethod = JsonUtils.parseObject(JsonUtils.toJsonString(dataMap.get("body")), baseRequest);
            } catch (Exception e) {
                LOGGER.warn("{} parse body fail", this, e);
                sendErrorResponse(resHead, IdIpErrorCode.IDIP_CANT_PARSE_PARAM_DATA);
                return;
            }
        } else if (req.method() == HttpMethod.POST) {
            String sign = getSignByReq(req);
            if (sign == null || !checkIdIpMD5(sign, data)) {
                LOGGER.error("IDIP_VERIFY_FAILED, sign={} data={}", sign, data);
                MonitorUnit.IDIP_REQUEST_TOTAL.labels(ServerContext.getBusId(), "IDIP_VERIFY_FAILED").inc();
                sendErrorResponse(null, IdIpErrorCode.IDIP_VERIFY_FAILED);
                return;
            }

            JsonObject jsonData = JsonUtils.parseObject(data);
            reqHead = getIdIpHeadByPost(jsonData);
            resHead = reqHead.getResponseHead();
            MonitorUnit.IDIP_REQUEST_TOTAL.labels(ServerContext.getBusId(), String.valueOf(reqHead.getCmdid())).inc();
            Class<? extends BaseRequest> baseRequest = getRequestMethodByPost(resHead, reqHead, jsonData);
            try {
                requestMethod = JsonUtils.parseObject(JsonUtils.toJsonString(jsonData.get("body")), baseRequest);
            } catch (Exception e) {
                LOGGER.warn("{} error parse body", this, e);
                sendErrorResponse(resHead, IdIpErrorCode.IDIP_CANT_PARSE_PARAM_DATA);
                return;
            }
        } else {
            sendErrorResponse(null, IdIpErrorCode.IDIP_CANT_PARSE_PARAM_DATA);
            return;
        }
        LOGGER.info("data parse success, reqHead={} resHead={} requestMethod={}", reqHead, resHead, requestMethod);
        extracted(resHead, requestMethod);
    }

    /**
     * 获取IdIp类
     *
     * @param resHead 响应头
     * @param reqHead 请求头
     */
    private Class<? extends BaseRequest> getRequestMethod(IdIpHead resHead, IdIpHead reqHead) {

        // 目前不支持Get以外的
        if (req.method() != HttpMethod.GET) {
            sendErrorResponse(resHead, IdIpErrorCode.IDIP_REQUESTS_TYPE_ERROR);
            return null;
        }
        // 没有具体业务接口
        String funcName = reqHead.getServiceName();
        Class<? extends BaseRequest> reqClass = RequestProcessManager.getInstance().getReqClass(funcName);

        if (reqClass == null) {
            sendErrorResponse(resHead, IdIpErrorCode.IDIP_METHOD_ERROR);
            return null;
        }
        return reqClass;
    }

    private Class<? extends BaseRequest> getRequestMethodByPost(IdIpHead resHead, IdIpHead reqHead, JsonObject jsonData) {
        int cmdid = reqHead.getCmdid();
        if (cmdid <= 0) {
            LOGGER.error("post method not exist, cmd={}", reqHead.getCmdid());
            sendErrorResponse(resHead, IdIpErrorCode.IDIP_PARAM_ERROR);
            return null;
        }
        Class<? extends BaseRequest> postMethod = getPostMethod(reqHead.getCmdid());
        if (postMethod == null) {
            LOGGER.error("post method not exist, cmd={}", reqHead.getCmdid());
            return null;
        }
        return postMethod;
    }

    @Nullable
    private Map<String, Object> getDataPacket() {
        QueryStringDecoder queryStringDecoder = new QueryStringDecoder(req.uri());
        Map<String, String> store = new HashMap<>();
        Map<String, List<String>> map = queryStringDecoder.parameters();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            store.put(entry.getKey(), entry.getValue().getFirst().trim());
        }
        // 无所需参数
        String data = store.get("data_packet");
        if (StringUtils.isEmpty(data)) {
            sendErrorResponse(null, IdIpErrorCode.IDIP_NO_PARAM_DATA);
            return null;
        }
        LOGGER.info("{} parse data {}", this, data);
        Map<String, Object> dataMap = new HashMap<>();
        try {
            dataMap = JsonUtils.parseObject(data, dataMap.getClass());
        } catch (Exception e) {
            LOGGER.error("{} error processRequest", this, e);
            sendErrorResponse(null, IdIpErrorCode.IDIP_CANT_PARSE_PARAM_DATA);
            return null;
        }
        return dataMap;
    }

    private IdIpHead getIdIpHead(Map<String, Object> dataMap) {
        // 没有消息头
        if (!dataMap.containsKey("head")) {
            sendErrorResponse(null, IdIpErrorCode.IDIP_NO_HEAD_PARAM);
            return null;
        }
        // 解析消息头
        IdIpHead reqHead;
        try {
            reqHead = JsonUtils.parseObject(JsonUtils.toJsonString(dataMap.get("head")), IdIpHead.class);
        } catch (Exception e) {
            LOGGER.error("{} error parse head", this, e);
            sendErrorResponse(null, IdIpErrorCode.IDIP_CANT_PARSE_HEAD);
            return null;
        }
        return reqHead;
    }

    private void extracted(IdIpHead resHead, BaseRequest reqObject) {
        // 执行业务
        IdIpResponse idIpResponse;
        try {
            Result result = reqObject.process(resHead, this);
            idIpResponse = new IdIpResponse(resHead, result);
        } catch (Exception e) {
            LOGGER.error("{} error processRequest", this, e);
            sendErrorResponse(resHead, IdIpErrorCode.ERROR);
            return;
        }
        String text = JsonUtils.toJsonString(idIpResponse);
        LOGGER.info("sendResponse {}", text);
        resp.content().writeBytes(text.getBytes());
        sendRes();
    }


    private IdIpHead getIdIpHeadByPost(JsonObject jsonData) {
        JsonElement head = jsonData.get("head");
        try {
            if (head == null) {
                sendErrorResponse(null, IdIpErrorCode.IDIP_CANT_PARSE_PARAM_DATA);
            }
            return JsonUtils.parseObject(head, IdIpHead.class);
        } catch (Exception e) {
            LOGGER.error("{} error processRequest", this, e);
            sendErrorResponse(null, IdIpErrorCode.IDIP_CANT_PARSE_PARAM_DATA);
            return null;
        }
    }

    private void sendErrorResponse(IdIpHead head, IdIpErrorCode errorCode) {
        IdIpResponse idIpResponse = new IdIpResponse(head, errorCode);
        String text = JsonUtils.toJsonString(idIpResponse);
        LOGGER.info("sendErrorResponse {}", text);
        resp.content().writeBytes(text.getBytes());
        sendRes();
    }

    protected void sendRes() {
        resp.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/html; charset=UTF-8");
        int length = resp.content().readableBytes();
        if (length == 0) {
            resp.setStatus(HttpResponseStatus.NO_CONTENT);
        }
        resp.headers().set(HttpHeaderNames.CONTENT_LENGTH, length);
        boolean keepAlive = HttpUtil.isKeepAlive(req);
        if (keepAlive) {
            resp.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);
        }
        ChannelFuture future = channel.writeAndFlush(resp);
        // Close the non-keep-alive connection after the write operation is
        // done.
        if (!keepAlive || resp.status() != HttpResponseStatus.OK) {
            future.addListener(ChannelFutureListener.CLOSE);
        }
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {

    }

    public static String getSignByReq(FullHttpRequest req) {
        String[] split = req.uri().split("idip_sign=");
        if (split.length <= 1) {
            return null;
        }
        return split[1];
    }

    public static boolean checkIdIpMD5(String sign, String reqBody) {
        String idIp_md5_key = ClusterConfigUtils.getWorldConfig().getStringItem("idip_md5_key");
        String param = reqBody + idIp_md5_key;
        String result = DigestUtils.md5Hex(param.getBytes(StandardCharsets.UTF_8));
        return sign.equals(result);
    }

}

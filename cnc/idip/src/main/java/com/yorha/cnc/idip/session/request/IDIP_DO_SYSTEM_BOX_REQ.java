package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsZoneChat;
import com.yorha.proto.StructPB;

import java.util.List;

/**
 * 系统聊天框消息
 *
 * <AUTHOR>
 */
public class IDIP_DO_SYSTEM_BOX_REQ extends BaseRequest {
    public int Partition;
    public int MessageList_count;
    public List<ChatMessage> MessageList;
    public int LoopTimes;
    public int LoopIntervalMinute;

    public static class ChatMessage {
        public String Content;
        public String Language;
    }

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        Result result = checkParam();
        if (result != null) {
            return result;
        }
        final CommonMsg.ChatMessage.Builder chatMessage = CommonMsg.ChatMessage.newBuilder();
        formChatMessageBuilder(chatMessage);
        final SsZoneChat.IdIpSendChatMsgAsk cmd = SsZoneChat.IdIpSendChatMsgAsk.newBuilder()
                .setLoopTimes(LoopTimes)
                .setLoopIntervalMiniute(LoopIntervalMinute)
                .setChatMessage(chatMessage)
                .build();

        actor.callZoneChat(Partition, cmd);
        return Result.success();
    }

    protected void formChatMessageBuilder(CommonMsg.ChatMessage.Builder builder) {
        builder.setType(CommonEnum.MessageType.MT_SYSTEM);
        builder.setChatTimestamp(SystemClock.now());

        // 聊天消息
        {
            CommonMsg.MessageData.Builder messageDataBuilder = CommonMsg.MessageData.newBuilder();
            messageDataBuilder.setMessageDataType(CommonEnum.MessageDataType.MDT_DISPLAY_DATA);
            messageDataBuilder.getMsgParamBuilder().getParamsBuilder().addDatas(getMultiLanguageDisplayParam(MessageList));
            builder.setMessageData(messageDataBuilder);
        }
    }

    static protected StructPB.DisplayParamPB getMultiLanguageDisplayParam(final List<ChatMessage> messageList) {
        StructPB.DisplayParamPB.Builder displayParamBuilder = StructPB.DisplayParamPB.newBuilder();
        for (final ChatMessage chatMessage : messageList) {
            final int lang = CommonEnum.Language.valueOf(chatMessage.Language).getNumber();
            displayParamBuilder.getMultiLangTxtBuilder().putDatas(lang, StructPB.MultiLangTxtPB.newBuilder().setLanguage(lang).setText(chatMessage.Content).build());
        }
        return displayParamBuilder.build();
    }

    /**
     * 参数校验
     */
    private Result checkParam() {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }

        if (MessageList_count <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "chat language count <= 0");
        }

        // 数量对不上
        if (MessageList_count != MessageList.size()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "chat language count error");
        }

        if (LoopTimes <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "chat LoopTimes <= 0");
        }

        if (LoopIntervalMinute <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "chat LoopIntervalMinute <= 0");
        }
        return null;
    }

}
package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 玩家禁言
 *
 * <AUTHOR>
 */
public class IDIP_DO_PROHIBIT_SPEAKING_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_PROHIBIT_SPEAKING_REQ.class);

    /**
     * PlayerIds
     * banTime
     */
    public Long RoleId;
    public int BanTime;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (BanTime <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "BanTime <= 0");
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleId <= 0");
        }
        try {
            Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
            if (result != null) {
                return result;
            }
            // 发送禁言指令
            SsPlayerMisc.MutePlayerFixMsAsk build = SsPlayerMisc.MutePlayerFixMsAsk.newBuilder()
                    .setPlayerId(RoleId)
                    .setMuteFixMs(TimeUtils.second2Ms(BanTime))
                    .build();
            actor.callPlayer(Partition, RoleId, build);
            return Result.success();
        } catch (GeminiException e) {
            LOGGER.error("IDIP_DO_PROHIBIT_SPEAKING_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

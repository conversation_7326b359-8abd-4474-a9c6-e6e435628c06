package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncAccountCancellation;

import java.util.ArrayList;
import java.util.List;

/**
 * 注销账号
 * 本地测试用例：http://***********:12031/cnc/idip_sign?data_packet={%22body%22:{%22AreaId%22:118,%22Partition%22:1,%22PlatId%22:1,%22OpenId%22:%222e3009eb3a884924a8f878e9d189e499%22},%22head%22:{%22Cmdid%22:1,%22PacketLen%22:0,%22Result%22:0,%22Seqid%22:1,%22ServiceName%22:%22iDIP_DO_DELETE_ACCOUNT_REQ%22}}
 *
 * <AUTHOR>
 */
public class IDIP_DO_DELETE_ACCOUNT_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_DELETE_ACCOUNT_REQ.class);

    public String OpenId;
    /**
     * 此接口该字段无效，纯适配解析使用
     */
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(OpenId)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "OpenId is null");
        }
        if (AreaId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId <= 0");
        }
        LOGGER.info("IDIP_DO_DELETE_ACCOUNT_REQ log off account begin, openId={}, worldID={}", OpenId, AreaId);
        List<Pair<Long, Integer>> playerId2ZoneList = PlayerIdIpHelper.getPlayerIdsByOpenId(actor, OpenId);
        if (playerId2ZoneList == null || playerId2ZoneList.isEmpty()) {
            LOGGER.info("IDIP_DO_LOGOFF_ACCOUNT_REQ log off account, account do not create any role");
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        LOGGER.info("IDIP_DO_DELETE_ACCOUNT_REQ, get account all role, openId={}, playerId2Zone={}", OpenId, playerId2ZoneList);
        List<Pair<Long, Integer>> failedPlayerList = new ArrayList<>();
        for (Pair<Long, Integer> value : playerId2ZoneList) {
            try {
                int zoneId = value.getSecond();
                long playerId = value.getFirst();
                actor.callPlayer(zoneId, playerId, SsPlayerMisc.LogOffAccountAsk.getDefaultInstance());
            } catch (Exception e) {
                failedPlayerList.add(value);
                WechatLog.error("IDIP_DO_DELETE_ACCOUNT_REQ logOff account role failed, openId={}, roleInfo={}", OpenId, value, e);
            }
        }
        if (!failedPlayerList.isEmpty()) {
            LOGGER.error("IDIP_DO_DELETE_ACCOUNT_REQ some player logOff failed, openId={}, failedPlayerList={}", OpenId, failedPlayerList);
            return Result.error(IdIpErrorCode.ERROR, "some player logOff failed: " + failedPlayerList);
        }
        QlogCncAccountCancellation.init(asServerHead())
                .setDtEventTime(TimeUtils.now2String())
                .setVOpenId(OpenId)
                .setAction("complete_cancel")
                .sendToQlog();
        LOGGER.info("IDIP_DO_LOGOFF_ACCOUNT_REQ process success, openId={}", OpenId);
        return Result.success();
    }

    public QlogServerFlowInterface asServerHead() {
        return new QlogServerFlowInterface() {
            @Override
            public String getServerType() {
                return "IdIp";
            }

            @Override
            public int getIZoneAreaID() {
                return 0;
            }

            @Override
            public String getServerOpenTime() {
                return "";
            }

            @Override
            public long getServerOpenTimeFar() {
                return 0;
            }

            @Override
            public int getServerOnline() {
                return 0;
            }

            @Override
            public String getServerCondition() {
                return "";
            }

            @Override
            public int getServerMilestoneStage() {
                return 0;
            }

            @Override
            public String getAccountId() {
                return "server_" + ServerContext.getServerInfo().getWorldId();
            }
        };
    }
}

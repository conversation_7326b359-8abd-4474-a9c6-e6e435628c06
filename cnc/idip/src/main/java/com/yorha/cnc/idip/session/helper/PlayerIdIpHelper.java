package com.yorha.cnc.idip.session.helper;

import com.google.common.collect.Lists;
import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.PlayerInfo;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.helper.CardHelper;
import com.yorha.common.utils.Pair;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;

public class PlayerIdIpHelper {
    protected static final Logger LOGGER = LogManager.getLogger(PlayerIdIpHelper.class);

    /**
     * 根据名字和zoneId查询玩家
     */
    public static Long getPlayerIdsByName(IdIpSessionActor actor, String name, int zoneId) {
        SsName.SearchNameMatchedAsk searchPlayerNameAsk = SsName.SearchNameMatchedAsk.newBuilder()
                .setName(name)
                .setNameType(CommonEnum.NameType.PLAYER_NAME)
                .build();
        SsName.SearchNameMatchedAns ans = actor.callName(zoneId, searchPlayerNameAsk);
        if (ans.getOwnerId() == 0) {
            return 0L;
        }
        Result result = checkPlayerExist(actor, zoneId, ans.getOwnerId());
        if (result != null) {
            return 0L;
        }
        return ans.getOwnerId();
    }

    /**
     * 根据openId查询玩家
     */
    public static List<Pair<Long, Integer>> getPlayerIdsByOpenId(IdIpSessionActor actor, String openId) {
        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder()
                .setOpenId(openId);
        GetByPartKeyResult<TcaplusDb.AccountRoleTable.Builder> ans = actor.callGameDb(new SelectAsk<>(req));

        if (DbUtil.isRecordNotExist(ans.getCode())) {
            return Collections.emptyList();
        }
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.error("PlayerIdIpHelper getPlayerIdsByOpenId, db fail, requestId={}, code={}", ans.requestId, ans.getCode());
            return null;
        }
        List<Pair<Long, Integer>> playerIds = Lists.newArrayListWithCapacity(ans.getValues().size());
        for (ValueWithVersion<TcaplusDb.AccountRoleTable.Builder> vv : ans.getValues()) {
            TcaplusDb.AccountRoleTable.Builder v = vv.value;
            playerIds.add(new Pair<>(v.getPlayerId(), v.getRoleInfo().getZoneId()));
        }
        return playerIds;
    }

    public static Result findPlayerByRoleId(IdIpSessionActor actor, int partition, Long roleId) {
        SsPlayerCard.QueryPlayerCardAns queryAns = CardHelper.queryPlayerCardSync(actor, roleId);
        if (queryAns == null || !queryAns.hasCardInfo()) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        if (partition != queryAns.getCardInfo().getZoneId()) {
            // 可能是idip客服填错了partition
            LOGGER.warn("PlayerIdIpHelper findPlayerByRoleId wrong zone not same {} {}", partition, queryAns.getCardInfo().getZoneId());
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "partition not match");
        }
        SsPlayerMisc.GetIdIpPlayerInfoAns playerInfoAns = actor.callPlayer(partition, roleId, SsPlayerMisc.GetIdIpPlayerInfoAsk.getDefaultInstance());
        return Result.success(new PlayerInfo(playerInfoAns));
    }

    public static Result checkPlayerExist(IdIpSessionActor actor, int partition, Long roleId) {
        SsPlayerCard.QueryPlayerCardAns queryAns = CardHelper.queryPlayerCardSync(actor, roleId);
        if (queryAns == null || !queryAns.hasCardInfo()) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        if (partition != queryAns.getCardInfo().getZoneId()) {
            LOGGER.warn("checkPlayerExist fail, zone error {} {}", partition, queryAns.getCardInfo().getZoneId());
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        return null;
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.node.TempWhiteListConfig;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.YamlUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 修改动态白名单
 *
 * <AUTHOR>
 */

public class UpdateWhiteList extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(UpdateWhiteList.class);

    public List<TempWhiteListConfig.WhiteConfig> List;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (List == null || List.size() <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "idipConfigList is null");
        }
        //加载etcd中的白名单
        String etcdWhiteListUrl = ActorClusterUrlUtils.etcdWhiteListUrl();
        String etcdWhiteListStr = ServerContext.getEtcdClient().getSingle(etcdWhiteListUrl);
        // VS5运营需求 不再累加白名单，而是覆盖
//        List<TempWhiteListConfig.WhiteConfig> list = new ArrayList<>();
//        if (etcdWhiteListStr != null) {
//            @SuppressWarnings("unchecked")
//            List<Map<String, Object>> configs = YamlUtils.newInstance(etcdWhiteListStr, List.class);
//            TempWhiteListConfig config = TempWhiteListConfig.valueOf(configs);
//            list.addAll(config.getWhiteConfigList());
//        }
        //新增白名单操作
//        list.addAll(idipConfigList);
        List<Object> yaml = new ArrayList<>();
        for (TempWhiteListConfig.WhiteConfig config : List) {
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("worldId", config.getWorldId());
            data.put("zoneId", config.getZoneId());
            data.put("deviceId", config.getDeviceId());
            data.put("openId", config.getOpenId());
            data.put("ip", config.getIp());
            data.put("debugLog", config.getDebugLog());
            data.put("registerLimit", config.getRegisterLimit());
            data.put("ipLimit", config.getIpLimit());
            data.put("powerUser", config.getPowerUser());
            data.put("desc", config.getDesc());
            data.put("noAuth", config.getNoAuth());
            yaml.add(data);
        }

        String config = YamlUtils.dumpToYaml(yaml);
        boolean succeed = ServerContext.getEtcdClient().setNX(
                ActorClusterUrlUtils.etcdWhiteListUrl(),
                config
        );
        if (!succeed) {
            succeed = ServerContext.getEtcdClient().cas(
                    ActorClusterUrlUtils.etcdWhiteListUrl(),
                    etcdWhiteListStr,
                    config
            );
        }
        if (!succeed) {
            LOGGER.error("ChangeWhiteList failed");
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "etcd  operatefail");
        }
        return Result.success();
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.*;
import com.yorha.cnc.idip.session.dto.AccountListInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerMisc;

import java.util.List;


/**
 * 根据玩家openId查询玩家列表
 *
 * <AUTHOR>
 */
public class IDIP_QUERY_ACCOUNT_INFO_REQ extends BaseRequest {

    public String OpenId;
    public int PageNo;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (PageNo <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "PageNo <= 0");
        }
        if (StringUtils.isEmpty(OpenId)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "openId is null");
        }

        List<Pair<Long, Integer>> playerId2ZoneList = PlayerIdIpHelper.getPlayerIdsByOpenId(actor, OpenId);
        if (playerId2ZoneList == null) {
            return Result.error(IdIpErrorCode.ERROR);
        }
        if (playerId2ZoneList.isEmpty()) {
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        int totalPageNo = (int) Math.ceil(playerId2ZoneList.size() / IdIpConstant.PAGE_SIZE);
        if (PageNo > totalPageNo) {
            return Result.error(IdIpErrorCode.IDIP_PAGE_OUT_OF_BOUNDS);
        }

        int totalSizeDown = (int) ((PageNo - 1) * IdIpConstant.PAGE_SIZE);
        int totalSizeUp = (int) (PageNo * IdIpConstant.PAGE_SIZE);

        AccountListInfo accountListInfo = new AccountListInfo();
        for (int i = 0; i < playerId2ZoneList.size(); i++) {
            if (i < totalSizeDown || i >= totalSizeUp) {
                continue;
            }
            Pair<Long, Integer> playerPair = playerId2ZoneList.get(i);
            SsPlayerMisc.GetIdIpPlayerInfoAns ans = actor.callPlayer(playerPair.getSecond(), playerPair.getFirst(), SsPlayerMisc.GetIdIpPlayerInfoAsk.getDefaultInstance());
            accountListInfo.addAccountInfo(ans);
        }
        accountListInfo.setAccountList_count();
        accountListInfo.setPageData(PageNo, totalPageNo);

        return Result.success(accountListInfo);
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.utils.MailUtil;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMailPB;
import com.yorha.proto.StructMsg;

import java.util.Objects;

/**
 * 发生玩家邮件 请求
 *
 * <AUTHOR>
 */
public class IDIP_DO_SEND_ITEM_REQ extends IDIP_DO_SEND_MAIL_PARTITION_REQ {

    public Long RoleId;
    public int Source;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        Result result = checkParam();
        if (result != null) {
            return result;
        }
        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleId < 0");
        }
        if (ItemId <= 0 || ItemNum <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "item not exist");
        }
        result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
        if (result != null) {
            return result;
        }
        StructMailPB.MailSendParamsPB.Builder builder = StructMailPB.MailSendParamsPB.newBuilder();
        String ret = getMailBuilder(builder);
        if (!StringUtils.isEmpty(ret)) {
            return Result.error(IdIpErrorCode.IDIP_MAIL_TEXT_IS_EMPTY, ret);
        }
        CommonMsg.MailReceiver receiver = CommonMsg.MailReceiver.newBuilder().setPlayerId(RoleId).setZoneId(Partition).build();
        MailUtil.sendIdIpMailToPlayer(receiver, Objects.requireNonNull(MailUtil.mailParamPb2proto(builder)), StructMsg.IdIpMailData.newBuilder().setCmdId(String.valueOf(resHead.getCmdid())).setSource(String.valueOf(Source)).setSerial(resHead.getSeqid()).build());
        return Result.success();
    }
}

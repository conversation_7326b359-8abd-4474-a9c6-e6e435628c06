package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ConsumeInfo;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerIdip;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

/**
 * @author: zpr
 * Date: 2023/8/9
 * Description:
 */
public class IDIP_DO_DELETE_ITEMS_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_DELETE_ITEMS_REQ.class);

    public int Partition;
    public String OpenId;
    public long RoleId;
    public int ItemId;
    public int ItemNum;
    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (StringUtils.isEmpty(OpenId)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "OpenId == null");
        }

        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }

        if (ItemNum < 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "FixValue < 0");
        }

        if (RoleId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "RoleId <= 0");
        }

        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }

        ItemTemplate itemTemplate = ResHolder.findTemplate(ItemTemplate.class, ItemId);
        if (itemTemplate == null) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "item not exist");
        }

        Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, RoleId);
        if (result != null) {
            return result;
        }

        LOGGER.info("IDIP_DO_DELETE_ITEMS_REQ begin, ZoneID={}, playerId={}, ItemId={}, ItemNum={}", Partition, RoleId, ItemId, ItemNum);

        SsPlayerIdip.ConsumeItemsAsk ask = SsPlayerIdip.ConsumeItemsAsk.newBuilder()
                .setItemId(ItemId)
                .setItemNum(ItemNum)
                .setOpenId(OpenId)
                .build();
        try{
            SsPlayerIdip.ConsumeItemsAns ans = actor.callPlayer(Partition, RoleId, ask);
            LOGGER.info("IDIP_DO_DELETE_ITEMS_REQ success, ZoneID={}, playerId={}, itemId={}, beforeValue={}, afterValue={}", Partition, RoleId, ItemId, ans.getBeforeValue(), ans.getAfterValue());
            return Result.success(new ConsumeInfo(ans.getBeforeValue(), ans.getAfterValue()));
        } catch (GeminiException e) {
            LOGGER.error("IDIP_DO_DELETE_ITEMS_REQ fail, ", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

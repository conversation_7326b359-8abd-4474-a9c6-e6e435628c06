package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsSceneMarquee;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 跑马灯
 * <AUTHOR>
 */

public class IDIP_DO_FULL_SERVER_MARQUEE_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_DO_FULL_SERVER_MARQUEE_REQ.class);

    public int Partition;  // 小区
    public int LoopTimes; // 循环次数
    public int LoopIntervalMinute; // 循环间隔分钟

    public int MessageList_count; // 消息列表的最大数量

    /**
     * 语言[zh 中文；en 英文；zh_tw 繁中；de 德语；ru 俄语；it 意大利语；es 西班牙语；pt 葡萄牙语；tr 土耳其语；ide 印尼语；th 泰语；vi 越南语；jp 日语；ko 韩语；ar 阿拉伯语；fr 法语；other 其他]
     */
    public List<Marquee> MessageList; // 消息内容

    public static class Marquee {
        public String Language; // 语言
        public String Content; // 消息正文
    }

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (AreaId != ServerContext.getWorldId()) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
        }
        if (Partition == 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        if (LoopTimes < 0){
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "LoopTimes < 0");
        }
        if (LoopIntervalMinute <= 0){
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "LoopIntervalMinute <= 0");
        }
        // 数量对不上
        if (MessageList.size() != MessageList_count) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "marquee language count error");
        }
        if (MessageList_count <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "marquee language count <= 0");
        }

        Map<Integer, Struct.DisplayData> msgMap = new HashMap<>();

        for (Marquee marquee : MessageList) {
            Struct.DisplayData.Builder builder = Struct.DisplayData.newBuilder();
            builder.getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(marquee.Content)).build();
            CommonEnum.Language language = CommonEnum.Language.valueOf(marquee.Language);
            int languageId = CommonEnum.Language.en_VALUE;
            if (language == CommonEnum.Language.L_NONE) {
                // 不合法枚举，返回语言错误
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "language error");
            }
            if (language != CommonEnum.Language.other) {
                // other默认使用英文
                languageId = language.getNumber();
            }
            LOGGER.info("sendMarquee lan:{} rsp:{}", language, marquee);
            msgMap.put(languageId, builder.build());
        }

        SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk builder = SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.newBuilder()
                .setPeriod(LoopTimes)
                .setOneLoopOfMs((int) TimeUnit.MINUTES.toMillis(LoopIntervalMinute))
                .putAllDisplayData(msgMap)
                .build();
        actor.callScene(Partition, builder);
        return Result.success();
    }

}

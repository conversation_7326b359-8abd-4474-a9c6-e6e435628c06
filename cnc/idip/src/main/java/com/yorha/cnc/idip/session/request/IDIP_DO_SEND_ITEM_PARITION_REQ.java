package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseMailRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.common.utils.MailUtil;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.StructMailPB;

/**
 * 发生全服邮件 道具
 *
 * <AUTHOR>
 */
public class IDIP_DO_SEND_ITEM_PARITION_REQ extends BaseMailRequest {

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        Result result = checkParam();
        if (ItemId <= 0 || ItemNum <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "item not exist");
        }

        if (result != null) {
            return result;
        }

        StructMailPB.MailSendParamsPB.Builder builder = StructMailPB.MailSendParamsPB.newBuilder();
        String ret = getMailBuilder(builder);
        if (!StringUtils.isEmpty(ret)) {
            return Result.error(IdIpErrorCode.IDIP_MAIL_TEXT_IS_EMPTY, ret);
        }
        MailUtil.sendZoneMail(Partition, MailUtil.mailParamPb2proto(builder));
        return Result.success();
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.AccountLastLoginInfo;
import com.yorha.common.helper.ZoneFindHelper;
import com.yorha.common.utils.PlayerUtils;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 查询账号上次登录时间
 * 本地测试用例：http://***********:12031/cnc/idip_sign?data_packet={%22body%22:{%22AreaId%22:118,%22OpenId%22:%222e3009eb3a884924a8f878e9d189e499%22},%22head%22:{%22Cmdid%22:1,%22PacketLen%22:0,%22Result%22:0,%22Seqid%22:1,%22ServiceName%22:%22iDIP_QUERY_LOGIN_TIME_REQ%22}}
 * <AUTHOR>
 */
public class IDIP_QUERY_LOGIN_TIME_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_QUERY_LOGIN_TIME_REQ.class);

    public int Partition;
    public String OpenId;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (AreaId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId <= 0");
        }
        if (Partition < 0) {
            // 这里小服id是没用的，只需要保证是非负数就好了，可以为0
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition < 0");
        }
        if (StringUtils.isEmpty(OpenId)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "OpenId is null");
        }
        // 获取openId下最后一个登录的账号 playerId -> version
        Pair<Long, Integer> lastRole = PlayerUtils.getLastLoginPlayerIdWithVersion(OpenId, actor);
        if (lastRole.getFirst() == 0) {
            LOGGER.info("IDIP_QUERY_LOGIN_TIME_REQ, account do not create any role, OpenId={}", OpenId);
            return Result.error(IdIpErrorCode.IDIP_PLAYER_IS_NOT_EXIST);
        }
        LOGGER.info("IDIP_QUERY_LOGIN_TIME_REQ, account last login role={}", lastRole);
        // 查询名片，获取zoneId
        int zoneId = ZoneFindHelper.queryPlayerZoneIdSync(actor, lastRole.getFirst());
        if (zoneId <= 0) {
            LOGGER.error("IDIP_QUERY_LOGIN_TIME_REQ, query player zoneId <= 0, openId={}, playerId={}, zoneId={}", OpenId, lastRole.getFirst(), zoneId);
            return Result.error(IdIpErrorCode.ERROR);
        }
        // 查看账号的最近登录时间
        SsPlayerMisc.GetLastLoginTimeAns lastLoginTimeAns = actor.callPlayer(zoneId, lastRole.getFirst(), SsPlayerMisc.GetLastLoginTimeAsk.getDefaultInstance());
        LOGGER.info("IDIP_QUERY_LOGIN_TIME_REQ process, openId={}, lastPlayerId={}, lastZoneId={}, lastLoginTime={}", OpenId, lastRole.getFirst(), zoneId, lastLoginTimeAns.getLastLoginTsMs());
        // 登录时间接口要求使用int
        return Result.success(new AccountLastLoginInfo((int) TimeUtils.ms2Second(lastLoginTimeAns.getLastLoginTsMs())));
    }
}

package com.yorha.cnc.idip;

import com.google.common.collect.ImmutableList;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.helper.ZoneFindHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerMisc;
import com.yorha.proto.StructMail;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.handler.codec.http.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SurveyRewardTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
public class IMurSessionActor extends GameActorWithCall {
    private static final Logger LOGGER = LogManager.getLogger(IMurSessionActor.class);
    private Channel channel;
    private FullHttpRequest req;
    private FullHttpResponse resp;
    // 参与加密的字段
    private List<String> encryptedFields;

    public IMurSessionActor(ActorSystem system, IActorRef self) {
        super(system, self);
    }

    public void handleHttpRequestMsg(Channel channel, FullHttpRequest req, FullHttpResponse resp) {
        this.channel = channel;
        this.req = req;
        this.resp = resp;
        this.encryptedFields = ImmutableList.of("sid", "uid", "user_type", "uid_source", "timestamp", "callback_params");
        processRequest();
    }

    public void processRequest() {
        LOGGER.info("IMurLog : {} http req uri={}", this, req.uri());
        QueryStringDecoder queryStringDecoder = new QueryStringDecoder(req.uri());
        Map<String, String> store = new TreeMap<>();
        Map<String, List<String>> map = queryStringDecoder.parameters();
        String sign = "";
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            // sign, effective, aid字段不参与加密
            if ("sign".equals(entry.getKey())) {
                sign = entry.getValue().get(0).trim();
                LOGGER.info("IMurLog : origin sign is {}", sign);
                continue;
            }
            // encryptdFields中是需要参与加密的字段，不在其中则不参与加密
            if (!this.encryptedFields.contains(entry.getKey())) {
                continue;
            }
            store.put(entry.getKey(), entry.getValue().get(0).trim());
        }
        if (sign.isEmpty()) {
            sendErrorResponse("req doesn't carry sign!");
            return;
        }
        // 先检查签名是否正确
        store.put("appSecret", "btE60iWg");
        String calcSign = this.calcSign(store);
        LOGGER.info("IMurLog : calc sign is {}", calcSign);
        if (!calcSign.equals(sign)) {
            sendErrorResponse("incorrect signature!");
            return;
        }
        // 建议一些必要的参数是否存在
        String uid = store.get("uid");
        if (uid == null) {
            sendErrorResponse("absent uid!");
            return;
        }
        // 获取roleId,通过callback_params传递
        String roleId = store.get("callback_params");
        if (roleId == null) {
            sendErrorResponse("absent roleId");
            return;
        }
        // 获取问卷id
        String sid = store.get("sid");
        if (sid == null) {
            sendErrorResponse("absent sid");
            return;
        }
        int templateId = 0;
        for (SurveyRewardTemplate template : ResHolder.getInstance().getListFromMap(SurveyRewardTemplate.class)) {
            if (template.getSurveyId().equals(sid)) {
                templateId = template.getRewardMaillId();
                break;
            }
        }
        if (templateId == 0) {
            sendErrorResponse("this questionnaire is not config a reward");
        }
        long playerId = Long.parseLong(roleId);
        // 发问卷
        sendImur(playerId, sid, templateId);
    }

    /**
     * 给玩家发送问卷
     *
     * @param playerId
     * @param sid
     * @param imurTemplateId
     */
    private void sendImur(long playerId, String sid, int imurTemplateId) {
        try {
            int zoneId = ZoneFindHelper.queryPlayerZoneIdSync(this, playerId);
            if (zoneId <= 0) {
                sendErrorResponse("can't find player");
                return;
            }
            // 判断问卷是否已经答过
            SsPlayerMisc.CheckImurAsk.Builder check = SsPlayerMisc.CheckImurAsk.newBuilder();
            check.setSid(sid);
            SsPlayerMisc.CheckImurAns ans = this.callPlayer(zoneId, playerId, check.build());
            if (ans.getResult()) {
                LOGGER.info("IMurLog : player: {} sid: {} already finished", playerId, sid);
                sendSuccess();
                return;
            }
            // 发送奖励邮件
            LOGGER.info("IMurLog : send reward mail player: {}, sid: {}", playerId, sid);
            StructMail.MailSendParams.Builder mailSendParams = StructMail.MailSendParams.newBuilder();
            mailSendParams.setMailTemplateId(imurTemplateId);

            MailUtil.sendMailToPlayer(CommonMsg.MailReceiver.newBuilder()
                            .setPlayerId(playerId)
                            .setZoneId(zoneId).build(),
                    mailSendParams.build());
            sendSuccess();
        } catch (Exception e) {
            LOGGER.error("sendImur player {} sid {} imurTemplateId {}", playerId, sid, imurTemplateId, e);
            sendErrorResponse("failed");
        }
    }

    /**
     * 正常结束
     */
    private void sendSuccess() {
        Map<String, String> status = new HashMap<>();
        status.put("status", "ok");
        String text = JsonUtils.toJsonString(status);
        resp.content().writeBytes(text.getBytes());
        sendRes();
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {

    }

    /**
     * 签名检查：
     * 提供必要参数（详情看API接口），使用kv数据结构；
     * 添加appSecret作为签名密钥字段到kv数据结构；
     * 对key进行按ascii升序排序；
     * 遍历排序后的kv数据结构，把所有元素，按照“key1value1key2value2”的模式拼接成字符串；
     * 对拼接的数据库进行md5摘要，即可得sign签名；
     * 对比接收到的sign和5中计算得到的sign签名；
     * 返回状态码status。
     */
    private String calcSign(Map<String, String> map) {
        String result = "";
        // 构造签名键值对的格式
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> item : map.entrySet()) {
            sb.append(item.getKey()).append(item.getValue());
        }
        result = sb.toString();
        LOGGER.info("IMurLog : ordered key value string = {}", result);
        // MD5加密
        result = DigestUtils.md5Hex(result);
        return result;
    }

    private void sendErrorResponse(String errorReason) {
        Map<String, String> status = new HashMap<>();
        status.put("reason", errorReason);
        status.put("status", "fail");
        String text = JsonUtils.toJsonString(status);
        LOGGER.info("IMurLog : sendErrorResponse {}", text);
        resp.content().writeBytes(text.getBytes());
        sendRes();
    }

    protected void sendRes() {
        resp.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/html; charset=UTF-8");
        int length = resp.content().readableBytes();
        if (length == 0) {
            resp.setStatus(HttpResponseStatus.NO_CONTENT);
        }
        resp.headers().set(HttpHeaderNames.CONTENT_LENGTH, length);
        boolean keepAlive = HttpUtil.isKeepAlive(req);
        if (keepAlive) {
            resp.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);
        }
        ChannelFuture future = channel.writeAndFlush(resp);
        // Close the non-keep-alive connection after the write operation is
        // done.
        if (!keepAlive || resp.status() != HttpResponseStatus.OK) {
            future.addListener(ChannelFutureListener.CLOSE);
        }
    }
}

package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ExecScriptInfo;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsZoneChat;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 通过IDIP执行服务器脚本的请求处理类
 *
 * <AUTHOR> Jiang
 */
public class IDIP_EXECUTE_SCRIPT_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_EXECUTE_SCRIPT_REQ.class);
    public int Partition;
    public String script;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "partition <= 0");
        }
        if (StringUtils.isEmpty(script)) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "ScriptContent is empty");
        }
        try {
            // 构建执行脚本的Ask对象
            SsZoneChat.IdIpExecScriptMsgAsk ask = SsZoneChat.IdIpExecScriptMsgAsk.newBuilder()
                    .setScript(script)
                    .build();
            SsZoneChat.IdIpExecScriptMsgAns  result = actor.callZoneChat(Partition, ask);
            return Result.success(new ExecScriptInfo(result));
        } catch (Exception e) {
            LOGGER.error("Failed to execute Groovy script via IDIP", e);
            return Result.error(IdIpErrorCode.ERROR, e.getMessage());
        }
    }
}

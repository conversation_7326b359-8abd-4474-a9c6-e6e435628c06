package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.helper.PlayerIdIpHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.SsPlayerIdip;

/**
 * 米大师通知余额刷新
 */
public class IDIP_DO_MASTER_NOTIFY_BALANCE_REQ extends BaseRequest {

    public int Partition;
    public String OpenId;
    public long Uid;
    public String Chargeser;
    public int ChargeNum;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        // 参数校验
        {
            if (StringUtils.isEmpty(OpenId)) {
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "OpenId == null");
            }

            if (Partition <= 0) {
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
            }

            if (Uid == 0) {
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Uid == 0");
            }

            if (StringUtils.isEmpty(Chargeser)) {
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Chargeser == null");
            }

            if (ChargeNum == 0) {
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "ChargeNum == 0");
            }

            if (AreaId != ServerContext.getWorldId()) {
                return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "AreaId do not match");
            }
        }
        Result result = PlayerIdIpHelper.checkPlayerExist(actor, Partition, Uid);
        if (result != null) {
            return result;
        }
        actor.tellPlayer(Partition, Uid, SsPlayerIdip.PullMidasCmd.newBuilder().setChargeNum(ChargeNum).setChargeSer(Chargeser).build());
        return Result.success();
    }
}

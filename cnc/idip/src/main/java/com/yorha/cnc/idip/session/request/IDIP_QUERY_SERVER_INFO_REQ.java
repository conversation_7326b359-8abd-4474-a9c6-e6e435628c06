package com.yorha.cnc.idip.session.request;


import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ZoneInfo;
import com.yorha.cnc.idip.session.dto.ZoneListInfo;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsZoneCard;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 查询所有服务器的 ID, IP, 端口等信息
 * <AUTHOR>
 */
public class IDIP_QUERY_SERVER_INFO_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_QUERY_SERVER_INFO_REQ.class);

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        final SsZoneCard.GetAllZoneInfoAns ans;
        try {
            ans = actor.call(RefFactory.ofZoneCard(), SsZoneCard.GetAllZoneInfoAsk.newBuilder().setIsSuperWhite(true).build());
        } catch (Exception e) {
            LOGGER.error("getAllZoneServerInfo fail, ", e);
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_SERVERS_INFO_FAIL);
        }
        ZoneListInfo array=  new ZoneListInfo();
        List<CommonMsg.ZoneServerInfo> list=ans.getZoneServerListList();
        for (CommonMsg.ZoneServerInfo info : list) {
            ZoneInfo zoneInfo=new ZoneInfo(info);
            array.add(zoneInfo);
        }
        return Result.success(array);
    }
}

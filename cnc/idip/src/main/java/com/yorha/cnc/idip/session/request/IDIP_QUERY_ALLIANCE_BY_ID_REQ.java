package com.yorha.cnc.idip.session.request;

import com.yorha.cnc.idip.IdIpSessionActor;
import com.yorha.cnc.idip.session.common.BaseRequest;
import com.yorha.cnc.idip.session.common.IdIpErrorCode;
import com.yorha.cnc.idip.session.common.IdIpHead;
import com.yorha.cnc.idip.session.common.Result;
import com.yorha.cnc.idip.session.dto.ClanInfo;
import com.yorha.common.helper.CardHelper;
import com.yorha.proto.SsClanAttr;
import com.yorha.proto.SsClanCard;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IDIP_QUERY_ALLIANCE_BY_ID_REQ extends BaseRequest {
    private static final Logger LOGGER = LogManager.getLogger(IDIP_QUERY_ALLIANCE_BY_ID_REQ.class);

    public long ClanId;
    public int Partition;

    @Override
    public Result process(IdIpHead resHead, IdIpSessionActor actor) {
        if (ClanId <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "ClanId <= 0");
        }
        if (Partition <= 0) {
            return Result.error(IdIpErrorCode.IDIP_PARAM_ERROR, "Partition <= 0");
        }
        try {
            SsClanCard.QueryClanCardAns ans = CardHelper.queryClanCardSync(actor, ClanId);
            // 向sceneActor查联盟是否存在
            if (ans.hasInfo()) {
                // 向clanActor获取数据
                SsClanAttr.IDIPQueryClanInfoAsk.Builder ask1 = SsClanAttr.IDIPQueryClanInfoAsk.newBuilder();
                SsClanAttr.IDIPQueryClanInfoAns ans1 = actor.callClan(Partition, ClanId, ask1.build());
                return Result.success(new ClanInfo(ans1, Partition));
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return Result.error(IdIpErrorCode.ERROR);
    }

}


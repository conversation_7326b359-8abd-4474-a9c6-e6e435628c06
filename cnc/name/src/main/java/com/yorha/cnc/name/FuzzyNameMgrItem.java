package com.yorha.cnc.name;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.NameType;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 名字管理 以类型为单位
 *
 * <AUTHOR>
 */
public class FuzzyNameMgrItem {
    private static final Logger LOGGER = LogManager.getLogger(FuzzyNameMgrItem.class);
    private final NameType type;
    private final Map<Long, String> nameMap = new Long2ObjectOpenHashMap<>();
    private final Map<String, LongOpenHashSet> indexMap = new Object2ObjectOpenHashMap<>();

    public FuzzyNameMgrItem(NameType type) {
        this.type = type;
    }

    /**
     * 占用名字  会释放旧的
     */
    public void occupyName(String name, long ownerId) {
        String old = nameMap.get(ownerId);
        if (old != null) {
            releaseName(ownerId);
        }
        // 直接转为小写存
        String newName = name.toLowerCase();
        nameMap.put(ownerId, newName);
        for (int i = 0; i < newName.length(); i++) {
            indexMap.computeIfAbsent(newName.substring(i, i + 1), k -> new LongOpenHashSet()).add(ownerId);
        }
    }

    public void releaseName(long ownerId) {
        if (!nameMap.containsKey(ownerId)) {
            return;
        }
        String name = nameMap.remove(ownerId);
        for (int i = 0; i < name.length(); i++) {
            LongOpenHashSet idSet = indexMap.get(name.substring(i, i + 1));
            if (idSet == null) {
                continue;
            }
            idSet.remove(ownerId);
        }
    }

    public List<Long> search(String name, int num) {
        if (StringUtils.isEmpty(name)) {
            return Collections.emptyList();
        }
        // 转为小写搜索
        String searchStr = name.toLowerCase();
        // 初步搜索
        Set<Long> result = preliminarySearch(searchStr);
        if (result.isEmpty()) {
            return Collections.emptyList();
        }
        LinkedList<Long> ret = new LinkedList<>();
        // 搜索字符串长度只有1 不过了
        if (searchStr.length() == 1) {
            if (result.size() <= num) {
                ret.addAll(result);
                return ret;
            }
            for (Long id : result) {
                ret.add(id);
                if (ret.size() >= num) {
                    return ret;
                }
            }
        }
        // 开始匹配
        for (Long ownerId : result) {
            String str = nameMap.get(ownerId);
            if (str == null) {
                LOGGER.error("NameTypeItem has dirty data search {} {} {}", type, ownerId, name);
                continue;
            }
            if (!str.contains(searchStr)) {
                continue;
            }
            ret.addLast(ownerId);
            if (ret.size() >= num) {
                return ret;
            }
        }
        return ret;
    }

    /**
     * 已经匹配过得字符 不用找了
     */
    private final Set<String> alreadySearchChar = new HashSet<>();

    private Set<Long> preliminarySearch(String name) {
        alreadySearchChar.clear();
        // 搜第一个字符
        String firstChar = name.substring(0, 1);
        alreadySearchChar.add(firstChar);
        LongOpenHashSet first = indexMap.get(firstChar);
        if (CollectionUtils.isEmpty(first)) {
            return Collections.emptySet();
        }
        // 搜后面的 取交集
        LongOpenHashSet result = new LongOpenHashSet(first);
        for (int i = 1; i < name.length(); i++) {
            String curChar = name.substring(i, i + 1);
            if (alreadySearchChar.contains(curChar)) {
                continue;
            }
            alreadySearchChar.add(curChar);
            LongOpenHashSet idSet = indexMap.get(curChar);
            if (CollectionUtils.isEmpty(idSet)) {
                return Collections.emptySet();
            }
            // 取交集
            result.retainAll(idSet);
            if (result.isEmpty()) {
                return Collections.emptySet();
            }
        }
        return result;
    }
}

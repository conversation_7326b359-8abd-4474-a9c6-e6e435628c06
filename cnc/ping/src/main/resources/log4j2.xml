<?xml version="1.0" encoding="UTF-8"?>
<!-- test环境、线上环境 -->
<configuration status="warn" monitorInterval="0" shutdownHook="disable">
    <properties>
        <!-- windows环境下需要此变量 -->
        <property name="LOG_HOME">${sys:LOG_ROOT}</property>
        <property name="PING_LOG_NAME">${env:PODNAME}_pingsvr</property>
        <property name="QLOG_LOG_NAME">${env:PODNAME}_qlog_flow_log</property>
    </properties>
    <appenders>
        <!-- 定义控制台输出-->
        <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
            <PatternLayout
                    pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%X{actorId}] - %msg%n"/>
        </Console>
        <!-- 游戏日志 -->
        <RollingRandomAccessFile name="FileAppender" fileName="${LOG_HOME}/${PING_LOG_NAME}"
                                 filePattern="${LOG_HOME}/${PING_LOG_NAME}.%d{yyyy-MM-dd-HH}.%i.log"
                                 immediateFlush="true">
            <PatternLayout
                    pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%X{actorId}] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
        <!-- 经分流水日志 -->
        <RollingRandomAccessFile name="QlogFlowAppender" fileName="${LOG_HOME}/${QLOG_LOG_NAME}"
                                 filePattern="${LOG_HOME}/${QLOG_LOG_NAME}.%d{yyyy-MM-dd-HH}.%i.log"
                                 immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
    </appenders>
    <loggers>
        <!-- 3party Loggers -->
        <Logger name="org.springframework" level="warn">
        </Logger>
        <Logger name="io.netty" level="warn">
        </Logger>
        <Logger name="org.apache.http" level="warn">
        </Logger>
        <Logger name="org.apache.commons" level="warn">
        </Logger>
        <Logger name="com.mchange.v2" level="warn">
        </Logger>
        <Logger name="org.reflections" level="warn">
        </Logger>
        <Logger name="io.etcd" level="warn">
        </Logger>
        <Logger name="java.sql" level="warn">
        </Logger>
        <Logger name="io.jaegertracing" level="warn">
        </Logger>
        <Logger name="com.tencent.tcaplus" level="warn">
        </Logger>
        <Logger name="io.grpc" level="warn">
        </Logger>
        <Logger name="QlogFlowLog" additivity="false">
            <appender-ref ref="QlogFlowAppender"/>
        </Logger>
        <!-- Root Logger -->
        <Root level="info" includeLocation="false">
            <appender-ref ref="FileAppender"/>
            <!-- 本地开发打开 -->
        <appender-ref ref="ConsoleAppender"/>
        </Root>
    </loggers>
    <Filters>
        <DynamicThresholdFilter key="debugLog" onMatch="ACCEPT" onMismatch="NEUTRAL">
            <KeyValuePair key="true" value="DEBUG"/>
        </DynamicThresholdFilter>
    </Filters>
</configuration>
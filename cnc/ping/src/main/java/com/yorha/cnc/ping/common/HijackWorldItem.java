package com.yorha.cnc.ping.common;

/**
 * 劫持描述对象。
 *
 * <AUTHOR>
 */
public class HijackWorldItem {
    /**
     * 对应平台。
     */
    private final ClientPlatform platform;
    /**
     * 世界id。
     */
    private final String worldId;
    /**
     * 劫持去向的世界id。
     */
    private final String hijackWorldId;
    /**
     * 四元版本对象。
     */
    private final TriadVersion version;
    /**
     * 版本。
     */
    private final String rawVersion;

    public HijackWorldItem(ClientPlatform platform, String worldId, String version, String hijackWorldId) {
        this.platform = platform;
        this.worldId = worldId;
        this.rawVersion = version;
        this.version = TriadVersion.valueOf3Version(version);
        this.hijackWorldId = hijackWorldId;
    }

    public ClientPlatform getPlatform() {
        return platform;
    }

    public String getHijackWorldId() {
        return hijackWorldId;
    }

    public String getRawVersion() {
        return this.rawVersion;
    }

    public TriadVersion getVersion() {
        return version;
    }

    public String getWorldId() {
        return worldId;
    }


    @Override
    public String toString() {
        return "HijackWorldName{" +
                "platform=" + this.platform +
                ", worldId='" + this.worldId + '\'' +
                ", version='" + this.rawVersion + '\'' +
                ", hijackWorldId='" + this.hijackWorldId + '\'' +
                '}';
    }
}

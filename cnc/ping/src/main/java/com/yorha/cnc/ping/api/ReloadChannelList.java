package com.yorha.cnc.ping.api;

import com.yorha.cnc.ping.PingServerContext;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 热更新通道信息
 *
 * <AUTHOR>
 */
public class ReloadChannelList extends HttpApi {
    private static final Logger LOGGER = LogManager.getLogger(ReloadChannelList.class);

    public ReloadChannelList(Channel channel, FullHttpRequest request, FullHttpResponse response, Map<String, String> params) {
        super(channel, request, response, params);
    }


    @Override
    protected void realRun() {
        PingServerContext.getInstance().reloadYaml();
        this.setOk();
        LOGGER.info("reload channel successful, channel={}", this.getChannel());
    }
}

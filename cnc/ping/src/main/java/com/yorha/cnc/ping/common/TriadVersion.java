package com.yorha.cnc.ping.common;

/**
 * 四元版本号对象。
 *
 * <AUTHOR>
 */
public class TriadVersion {
    public static TriadVersion smallestVersion = new TriadVersion(Integer.MIN_VALUE, Integer.MIN_VALUE, Integer.MIN_VALUE);

    /**
     * 首项。
     */
    private final int first;
    /**
     * 次项。
     */
    private final int second;
    /**
     * 第三项。
     */
    private final int third;

    private TriadVersion(int first, int second, int third) {
        this.first = first;
        this.second = second;
        this.third = third;
    }

    @Override
    public String toString() {
        return "QuadrupleVersion{" +
                "first=" + first +
                ", second=" + second +
                ", third=" + third +
                '}';
    }

    /**
     * 版本是否>入参版本。
     *
     * @param version 其他版本。
     * @return true表示版本较高， false表示版本不大于version。
     */
    public boolean isAbove(final TriadVersion version) {
        if (this.first != version.first) {
            return this.first > version.first;
        }
        if (this.second != version.second) {
            return this.second > version.second;
        }
        if (this.third != version.third) {
            return this.third > version.third;
        }
        return false;
    }

    /**
     * 根据字符串构造一个四元对象。
     *
     * @param version 版本川。
     * @return 四元对象。
     */
    public static TriadVersion valueOf4Version(final String version) {
        final String[] items = version.split("\\.");
        if (items.length != 4) {
            throw new IllegalArgumentException("version=" + version + " not right");
        }
        final int i1 = Integer.parseInt(items[0]);
        final int i2 = Integer.parseInt(items[1]);
        final int i3 = Integer.parseInt(items[2]);
        return new TriadVersion(i1, i2, i3);
    }

    public static TriadVersion valueOf3Version(final String version) {
        final String[] items = version.split("\\.");
        if (items.length != 3) {
            throw new IllegalArgumentException("version=" + version + " not right");
        }
        final int i1 = Integer.parseInt(items[0]);
        final int i2 = Integer.parseInt(items[1]);
        final int i3 = Integer.parseInt(items[2]);
        return new TriadVersion(i1, i2, i3);
    }
}

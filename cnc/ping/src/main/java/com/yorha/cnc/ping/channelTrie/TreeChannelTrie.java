package com.yorha.cnc.ping.channelTrie;

import com.yorha.common.utils.json.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 通道树
 *
 * <AUTHOR>
 */
public class TreeChannelTrie implements ChannelTrie {
    private final ReentrantLock lock = new ReentrantLock(true);
    private volatile RootNode treeRoot;

    @Override
    public boolean isSame(Map<String, Object> channelInfo) {
        if (treeRoot == null) {
            return false;
        }
        String oldInfo = JsonUtils.toJsonString(treeRoot.toMap());
        RootNode rootNode = new RootNode("root");
        rootNode.buildChannelTree(channelInfo);
        String newInfo = JsonUtils.toJsonString(rootNode.toMap());
        return oldInfo.equals(newInfo);
    }

    @Override
    public void loadChannelTrie(Map<String, Object> yaml) {
        lock.lock();
        try {
            if (isSame(yaml)) {
                return;
            }
            RootNode newRoot = new RootNode("root");
            newRoot.buildChannelTree(yaml);
            treeRoot = newRoot;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<Object> getChannelInfo(String worldId, String continent, String country, String ipMainInfo) {
        List<String> path = new ArrayList<>();
        path.add(worldId);
        path.add(continent);
        path.add(country);
        path.add(ipMainInfo);
        return treeRoot.seekLeafNode(path, 0);
    }
}

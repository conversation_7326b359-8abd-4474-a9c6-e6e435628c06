package com.yorha.cnc.ping;

import com.yorha.cnc.ping.api.*;
import com.yorha.common.qlog.QlogManager;
import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.ip.IpSeekerUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.*;
import io.netty.handler.timeout.IdleStateHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 拨测服务器
 *
 * <AUTHOR>
 */
public class PingServer implements QlogServerFlowInterface {
    private static final Logger LOGGER = LogManager.getLogger(PingServer.class);
    private static final int WEB_PORT = 10086;
    private static final int CONTROL_PORT = 10087;

    /**
     * 业务侧端口和线程组
     */
    private NioEventLoopGroup httpIoGroup;
    /**
     * 工作线程组
     */
    private NioEventLoopGroup httpWorkerGroup;
    /**
     * 控制侧端口和线程组,控制侧业务很低频，使用单线程
     */
    private NioEventLoopGroup controlIoGroup;
    /**
     * 大洲
     */
    private final String continent;
    /**
     * 服务器开放时间
     */
    private final long openTsMs;
    /**
     * 测试环境
     */
    private final boolean isTestEnv;

    public PingServer(String[] args) {
        LOGGER.info("Local server start! args={}", Arrays.asList(args));
        // 启动后台HTTP服务
        this.continent = args[0];
        // 设置环境
        this.isTestEnv = args.length > 1 && "test".equals(args[1]);
        // 启动时间
        this.openTsMs = SystemClock.nowNative();
        // 增加新号hook,
        this.addShutDownHook();
        // 初始化上下文
        PingServerContext.getInstance().init(this);
        // 初始化ipSeeker
        IpSeekerUtils.init();
        // 初始化qlog
        QlogManager.getInstance().initLocal();
        // 初始化http
        this.initHttpServer();
    }

    /**
     * 是否处于测试环境。
     *
     * @return true or false。
     */
    public boolean isTestEnv() {
        return isTestEnv;
    }

    /**
     * 返回服务所属大洲。
     *
     * @return 大洲。
     */
    public String getContinent() {
        return continent;
    }

    /**
     * SIGINT	2	中断（同 Ctrl + C）会触发触发shutdownhook
     * SIGKILL	9	终止进程，强制杀死进程
     * SIGTERM	15	终止进程，会先释放自己的资源，触发shutdownhook然后终止进程
     * 此处会hook 2 15的信号
     * 若此函数死循环导致进程无法退出，需要kill -9 pid
     */
    protected void addShutDownHook() {
        Runtime.getRuntime().addShutdownHook(ConcurrentHelper.newThread("ShutdownHook", false, () -> {
            LOGGER.info("gemini_system ShutDownHook Trigger");
            this.stopHttpServer();
        }));
    }

    /**
     * 初始化http服务器。
     */
    private void initHttpServer() {
        LOGGER.info("gemini_system init HTTP server to start");
        try {
            startBusinessProcess(4);
            startControlProcess();
        } catch (Exception e) {
            LOGGER.error("gemini_system initHttpServer fail, e=", e);
            System.exit(-1);
        }
    }

    /**
     * 开启业务服务器。
     *
     * @param httpWorkerNum 工作线程数量。
     */
    private void startBusinessProcess(final int httpWorkerNum) {
        // netty主从reactor模型
        this.httpIoGroup = new NioEventLoopGroup(1);
        this.httpWorkerGroup = new NioEventLoopGroup(httpWorkerNum);

        PingServerHandler.getInstance().registerHttpApi("/channel/list", HttpMethod.GET, QueryChannelList.class);
        PingServerHandler.getInstance().registerHttpApi("/channel/list/v2", HttpMethod.GET, QueryChannelListV2.class);
        PingServerHandler.getInstance().registerHttpApi("/channel/report", HttpMethod.POST, ReportServiceQuality.class);
        PingServerHandler.getInstance().registerHttpApi("/channel/reportServerQuality", HttpMethod.POST, NewReportServiceQuality.class);
        PingServerHandler.getInstance().registerHttpApi("/errorReport", HttpMethod.POST, ErrorReport.class);
        if (isTestEnv) {
            PingServerHandler.getInstance().registerHttpApi("/channel/getWorldList", HttpMethod.GET, GetWorldList.class);
        }

        ServerBootstrap businessBootStrap = new ServerBootstrap();
        businessBootStrap.group(this.httpIoGroup, this.httpWorkerGroup)
                // 设置服务端通道实现类型
                .channel(NioServerSocketChannel.class)
                // 设置线程队列得到连接个数
                .option(ChannelOption.SO_BACKLOG, 128)
                .option(ChannelOption.SO_REUSEADDR, true)
                .childOption(ChannelOption.TCP_NODELAY, true)
                // 设置保持活动连接状态
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                // 使用匿名内部类的形式初始化通道对象
                .childHandler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel ch) {
                        ChannelPipeline pipeline = ch.pipeline();
                        pipeline.addLast("decoder", new HttpRequestDecoder(200, 2000, 300));
                        pipeline.addLast("aggregator", new HttpObjectAggregator(2500));
                        pipeline.addLast("encoder", new HttpResponseEncoder());
                        pipeline.addLast("deflate", new HttpContentCompressor());
                        pipeline.addLast("handler", PingServerHandler.getInstance());
                        pipeline.addLast("alive", new IdleStateHandler(0, 1, 0, TimeUnit.MINUTES));
                    }
                });
        try {
            LOGGER.info("gemini_system http server business process start on {} isTestEnv {}", WEB_PORT, isTestEnv);
            businessBootStrap.bind(new InetSocketAddress(WEB_PORT)).sync();
        } catch (Exception e) {
            e.printStackTrace();
            throw new GeminiException(StringUtils.format("init HTTP server business process to fail {}", WEB_PORT), e);
        }
    }

    private void startControlProcess() {
        // netty单线程模型
        this.controlIoGroup = new NioEventLoopGroup(1);

        PingServerHandler.getControlInstance().registerHttpApi("/channel/reload", HttpMethod.POST, ReloadChannelList.class);
        PingServerHandler.getControlInstance().registerHttpApi("/channel/ping", HttpMethod.POST, Ping.class);

        ServerBootstrap controlBootStrap = new ServerBootstrap();
        controlBootStrap.group(this.controlIoGroup)
                // 设置服务端通道实现类型
                .channel(NioServerSocketChannel.class)
                // 设置线程队列得到连接个数
                .option(ChannelOption.SO_BACKLOG, 128)
                .option(ChannelOption.SO_REUSEADDR, true)
                .childOption(ChannelOption.TCP_NODELAY, true)
                // 设置保持活动连接状态
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                // 使用匿名内部类的形式初始化通道对象
                .childHandler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel ch) {
                        ChannelPipeline pipeline = ch.pipeline();
                        pipeline.addLast("decoder", new HttpRequestDecoder(200, 2000, 300));
                        pipeline.addLast("aggregator", new HttpObjectAggregator(2500));
                        pipeline.addLast("encoder", new HttpResponseEncoder());
                        pipeline.addLast("deflate", new HttpContentCompressor());
                        pipeline.addLast("handler", PingServerHandler.getControlInstance());
                        pipeline.addLast("alive", new IdleStateHandler(0, 1, 0, TimeUnit.MINUTES));
                    }
                });
        try {
            LOGGER.info("gemini_system http server control process start on {} isTestEnv {}", CONTROL_PORT, isTestEnv);
            controlBootStrap.bind(new InetSocketAddress(CONTROL_PORT)).sync();
        } catch (Exception e) {
            e.printStackTrace();
            throw new GeminiException(StringUtils.format("init HTTP server control process to fail {}", CONTROL_PORT), e);
        }
    }

    /**
     * 关闭http服务器。
     */
    private void stopHttpServer() {
        if (PingServerHandler.getInstance().isInitOk()) {
            PingServerHandler.getInstance().shutdown();
        }
        if (this.httpIoGroup != null) {
            this.httpIoGroup.shutdownGracefully();
            this.httpWorkerGroup.shutdownGracefully();
        }
        if (PingServerHandler.getControlInstance().isInitOk()) {
            PingServerHandler.getControlInstance().shutdown();
        }
        if (this.controlIoGroup != null) {
            this.controlIoGroup.shutdownGracefully();
        }
    }

    @Override
    public String getServerType() {
        return "PingServer";
    }

    @Override
    public int getIZoneAreaID() {
        return 0;
    }

    @Override
    public String getServerOpenTime() {
        return String.valueOf(this.openTsMs);
    }

    @Override
    public long getServerOpenTimeFar() {
        return TimeUtils.ms2Second(SystemClock.now() - this.openTsMs);
    }

    @Override
    public int getServerOnline() {
        return 0;
    }

    @Override
    public String getServerCondition() {
        return "xxx";
    }

    @Override
    public int getServerMilestoneStage() {
        return 0;
    }

    @Override
    public String getAccountId() {
        return this.continent;
    }
}

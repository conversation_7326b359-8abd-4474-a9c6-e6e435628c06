package com.yorha.cnc.ping.common;

/**
 * 客户端枚举。
 *
 * <AUTHOR>
 */
public enum ClientPlatform {
    /**
     * 苹果平台
     */
    IOS,
    /**
     * 安卓平台。
     */
    ANDROID,
    /**
     * 未知平台。
     */
    UNKNOWN;

    /**
     * 转换数字为没证据。
     *
     * @param number 数字。
     * @return 枚举。
     */
    public static ClientPlatform forNumber(final int number) {
        switch (number) {
            case 1:
                return ANDROID;
            case 2:
                return IOS;
            default:
                return UNKNOWN;
        }
    }

    /**
     * 根据名称转换对应枚举。
     *
     * @param name 名称。
     * @return 枚举。
     */
    public static ClientPlatform forName(final String name) {
        switch (name) {
            case "ios":
                return IOS;
            case "android":
                return ANDROID;
            default:
                return UNKNOWN;
        }
    }
}

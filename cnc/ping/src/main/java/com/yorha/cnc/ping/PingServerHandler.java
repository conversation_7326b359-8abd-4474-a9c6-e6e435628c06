package com.yorha.cnc.ping;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.yorha.cnc.ping.api.HttpApi;
import com.yorha.cnc.ping.api.HttpApiBadParams;
import com.yorha.cnc.ping.api.HttpApiBadUrl;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.ChannelHelper;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.WechatLog;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 业务侧handler
 *
 * <AUTHOR>
 */
@ChannelHandler.Sharable
public class PingServerHandler extends SimpleChannelInboundHandler<HttpObject> {
    private static final Logger LOGGER = LogManager.getLogger(PingServerHandler.class);


    /**
     * 状态标记
     */
    private enum LocalServerStatus {
        /**
         * 初始化完成
         */
        INIT_OK,
        /**
         * 销毁
         */
        DESTROY,
    }


    private static class LazyHolder {
        private static final PingServerHandler INSTANCE = new PingServerHandler(
                ConcurrentHelper.newFixedThreadExecutor("ping-server-business", 8, 10000, false));
        private static final PingServerHandler CONTROL_INSTANCE = new PingServerHandler(
                ConcurrentHelper.newFixedThreadExecutor("ping-server-control", 1, 10, false));

        static {
            INSTANCE.service.setRejectedExecutionHandler(PingServerHandler::rejectHandler);
            CONTROL_INSTANCE.service.setRejectedExecutionHandler(PingServerHandler::rejectHandler);
        }
    }


    public static void rejectHandler(Runnable r, ThreadPoolExecutor executor) {
        HttpApi api = (HttpApi) r;
        api.getResp().setStatus(HttpResponseStatus.INTERNAL_SERVER_ERROR);
        LOGGER.warn("PingServerHandler discard runnable {}! executor {}!", r, executor);
    }

    public static PingServerHandler getInstance() {
        return PingServerHandler.LazyHolder.INSTANCE;
    }

    public static PingServerHandler getControlInstance() {
        return PingServerHandler.LazyHolder.CONTROL_INSTANCE;
    }

    /**
     * http url分发映射
     */
    private volatile Map<String, Constructor<HttpApi>> postFunc = Collections.emptyMap();
    private volatile Map<String, Constructor<HttpApi>> getFunc = Collections.emptyMap();
    /**
     * 属性
     */
    private volatile LocalServerStatus status;
    /**
     * 读写锁
     */
    private final ReentrantLock lock = new ReentrantLock(true);
    /**
     * 线程池
     */
    private final GeminiThreadPoolExecutor service;

    private PingServerHandler(GeminiThreadPoolExecutor executor) {
        this.service = executor;
        this.status = LocalServerStatus.INIT_OK;
    }

    public boolean isInitOk() {
        return this.status == LocalServerStatus.INIT_OK;
    }

    /**
     * 关闭所属服务器。
     */
    public void shutdown() {
        this.lock.lock();
        try {
            if (this.status != LocalServerStatus.INIT_OK) {
                throw new GeminiException("status {} not right!", this.status);
            }
            this.status = LocalServerStatus.DESTROY;
        } finally {
            this.lock.unlock();
        }
        this.service.shutdown();
    }

    /**
     * 注册HttpApi。
     *
     * @param url      url
     * @param apiClass api类型。
     * @param <T>      实际类型。
     */
    @SuppressWarnings("unchecked")
    public <T extends HttpApi> void registerHttpApi(final String url, final HttpMethod method, final Class<T> apiClass) {
        this.lock.lock();
        try {
            if (this.status != LocalServerStatus.INIT_OK) {
                throw new GeminiException("status {} not right!", this.status);
            }
            final Constructor<T> constructor = apiClass.getConstructor(Channel.class, FullHttpRequest.class, FullHttpResponse.class, Map.class);
            final HashMap<String, Constructor<HttpApi>> newMap;
            if (method == HttpMethod.POST) {
                newMap = new HashMap<>(this.postFunc);
            } else if (method == HttpMethod.GET) {
                newMap = new HashMap<>(this.getFunc);
            } else {
                throw new NotImplementedException("Not Support " + method.name());
            }
            newMap.put(url, (Constructor<HttpApi>) constructor);
            if (method == HttpMethod.POST) {
                this.postFunc = newMap;
            } else {
                this.getFunc = newMap;
            }
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            this.lock.unlock();
        }
        LOGGER.info("http register. {}: {}", url, apiClass.getSimpleName());
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        LOGGER.info("{} channelActive", ctx.channel());
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        LOGGER.info("{} channelInactive  ", ctx.channel());
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, HttpObject msg) {
        final Channel ch = ctx.channel();
        if (msg instanceof HttpRequest) {
            final FullHttpRequest req = (FullHttpRequest) msg;
            if (HttpUtil.is100ContinueExpected(req)) {
                send100Continue(ctx);
            }
            final FullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK);
            this.handleReq(ctx.channel(), req, resp);
        } else {
            LOGGER.error("channelRead0 fail! msg={} not right!", msg);
            ChannelHelper.closeChannel(ch, "PingServerHandler channelRead0 fail");
        }
    }


    protected static void send100Continue(ChannelHandlerContext ctx) {
        FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.CONTINUE);
        ctx.write(response);
    }

    private void handleReq(final Channel channel, final FullHttpRequest req, final FullHttpResponse resp) {
        final String key = req.uri().split("[?]")[0];
        final Map<String, Constructor<HttpApi>> apiFuncView;
        if (req.method() == HttpMethod.POST) {
            apiFuncView = this.postFunc;
        } else if (req.method() == HttpMethod.GET) {
            apiFuncView = this.getFunc;
        } else {
            this.service.execute(new HttpApiBadUrl(channel, req, resp, Collections.emptyMap()));
            return;
        }
        if (!apiFuncView.containsKey(key)) {
            this.service.execute(new HttpApiBadUrl(channel, req, resp, Collections.emptyMap()));
            return;
        }
        // 获取参数
        Map<String, String> params = Collections.emptyMap();
        try {
            params = this.parseParams(channel, req);
            final HttpApi func = apiFuncView.get(key).newInstance(channel, req, resp, params);
            this.service.execute(func);
        } catch (Exception e) {
            this.service.execute(new HttpApiBadParams(channel, req, resp, params, e));
        }
    }

    private Map<String, String> parseParams(final Channel channel, FullHttpRequest req) {
        if (req.method() == HttpMethod.POST) {
            if (!req.content().isReadable()) {
                return Collections.emptyMap();
            }
            byte[] buffer = new byte[req.content().readableBytes()];
            req.content().readBytes(buffer);
            final String buffStr = new String(buffer, StandardCharsets.UTF_8);
            try {
                final JsonObject jsonData = JsonUtils.parseObject(buffStr);
                final Map<String, String> paramMap = new HashMap<>(jsonData.size());
                List<String> errorKeys = new LinkedList<>();
                for (Map.Entry<String, JsonElement> entry : jsonData.entrySet()) {
                    final JsonElement e = entry.getValue();
                    final String str;
                    if (e.isJsonNull()) {
                        str = "_cnc_server_null";
                        errorKeys.add(entry.getKey());
                    } else if (e.isJsonObject()) {
                        str = "_cnc_server_object";
                        errorKeys.add(entry.getKey());
                    } else {
                        str = e.getAsString();
                    }
                    paramMap.put(entry.getKey(), str);
                }
                if (!errorKeys.isEmpty()) {
                    LOGGER.error("parseParams, not all string item, buffStr={}, errorKeys={}, channel={}", buffStr, errorKeys, channel);
                }
                return paramMap;
            } catch (Exception e) {
                throw new GeminiException("body not right: " + buffStr, e);
            }
        }
        if (req.method() == HttpMethod.GET) {
            final QueryStringDecoder decoder = new QueryStringDecoder(req.uri());
            final Map<String, String> paramMap = new HashMap<>(decoder.parameters().size());
            for (final Map.Entry<String, List<String>> entry : decoder.parameters().entrySet()) {
                paramMap.put(entry.getKey(), entry.getValue().get(0));
            }
            return paramMap;
        }
        throw new GeminiException("Only support GET and POST, but get {}", req.method());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable err) {
        try {
            if (err instanceof IOException) {
                LOGGER.info("channel={} io change, msg={}", ctx.channel(), err.getMessage());
            } else {
                WechatLog.error("channel={} exceptionCaught, err=", ctx.channel(), err);
            }
        } finally {
            ChannelHelper.closeChannel(ctx.channel(), "PingServerHandler exceptionCaught");
        }
    }
}

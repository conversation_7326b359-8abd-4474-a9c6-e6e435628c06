package com.yorha.cnc.ping.channelTrie;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ChannelTrie {

    /**
     * 判断内容是否相同
     * @param channelInfo yaml解析出的通道信息
     * @return 是否相同
     */
    boolean isSame(Map<String, Object> channelInfo);

    /**
     * 加载前缀树
     * @param yaml yaml解析出的通道信息
     */
    void loadChannelTrie(Map<String, Object> yaml);

    /**
     * 获取通道信息
     * @param worldId 大区id
     * @param continent ip所在大洲
     * @param country ip所在国家
     * @param ipMainInfo ipSeekerUtils得到的ip mainInfo
     * @return 对应的通道信息
     */
    List<Object> getChannelInfo(String worldId, String continent, String country, String ipMainInfo);
}

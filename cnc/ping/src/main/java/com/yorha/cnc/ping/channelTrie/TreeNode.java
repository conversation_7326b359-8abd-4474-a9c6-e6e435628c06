package com.yorha.cnc.ping.channelTrie;

/**
 * 通道树节点
 * <AUTHOR>
 */
public class TreeNode {
    public String nodeName;

    public boolean isLeafNode() {
        return false;
    }

    /**
     * 是否匹配当前节点
     */
    public boolean matchNode(String key) {
        if (nodeName != null) {
            return key.contains(nodeName) || nodeName.contains(key);
        }
        return false;
    }



}

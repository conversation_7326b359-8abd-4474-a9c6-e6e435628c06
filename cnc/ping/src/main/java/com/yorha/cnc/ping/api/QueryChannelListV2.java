package com.yorha.cnc.ping.api;

import com.github.jarod.qqwry.IPZone;
import com.yorha.cnc.ping.PingErrorCode;
import com.yorha.cnc.ping.PingServerContext;
import com.yorha.cnc.ping.common.ClientPlatform;
import com.yorha.cnc.ping.common.HijackWorldItem;
import com.yorha.cnc.ping.common.TriadVersion;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.ip.IpSeekerUtils;
import com.yorha.common.utils.json.JsonUtils;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.InetSocketAddress;
import java.util.List;
import java.util.Map;

/**
 * 带有劫持版本的QueryChannelListV2
 *
 * <AUTHOR>
 */
public class QueryChannelListV2 extends HttpApi {
    private static final Logger LOGGER = LogManager.getLogger(QueryChannelListV2.class);
    private static final String QUERY_CHANNEL_V2_REQUEST_LABEL_WORLD_ID = "worldId";
    private static final String QUERY_CHANNEL_V2_REQUEST_LABEL_VERSION = "version";
    private static final String QUERY_CHANNEL_V2_REQUEST_LABEL_PLATFORM_ID = "platformId";
    private static final String QUERY_CHANNEL_V2_REQUEST_LABEL_DEVICE_ID = "deviceId";
    private static final String QUERY_CHANNEL_V2_REQUEST_LABEL_COUNTRY_NAME = "countryName";

    private static class Response {
        private final String worldId;
        private final List<Object> channels;

        private Response(String worldId, List<Object> channels) {
            this.worldId = worldId;
            this.channels = channels;
        }

        @Override
        public String toString() {
            return "Response{" +
                    "worldId='" + worldId + '\'' +
                    ", channels=" + channels +
                    '}';
        }
    }

    public QueryChannelListV2(Channel channel, FullHttpRequest request, FullHttpResponse response, Map<String, String> params) {
        super(channel, request, response, params);
    }

    @Override
    protected void realRun() {
        try {
            // 准备入参
            final String worldId = this.getParams().get(QUERY_CHANNEL_V2_REQUEST_LABEL_WORLD_ID);
            if (StringUtils.isEmpty(worldId)) {
                throw new GeminiException("worldId null", null, ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId());
            }
            final String deviceId = this.getParams().getOrDefault(QUERY_CHANNEL_V2_REQUEST_LABEL_DEVICE_ID, "NOT SET");
            final String continent = PingServerContext.getInstance().getContinent();
            final String rawVersion = this.getParams().get(QUERY_CHANNEL_V2_REQUEST_LABEL_VERSION);
            final String rawPlatformId = this.getParams().get(QUERY_CHANNEL_V2_REQUEST_LABEL_PLATFORM_ID);

            // 入参转换
            final ClientPlatform platform = rawPlatformId != null ? ClientPlatform.forNumber(Integer.parseInt(rawPlatformId)) : ClientPlatform.UNKNOWN;
            final TriadVersion version = rawVersion != null ? TriadVersion.valueOf4Version(rawVersion) : TriadVersion.smallestVersion;

            // 准备远端ip
            final InetSocketAddress inSocket = (InetSocketAddress) this.getChannel().remoteAddress();
            final String ip = inSocket.getAddress().getHostAddress();
            final IPZone ipZone = IpSeekerUtils.getIpZone(ip);

            // 根据劫持配置准备数据
            final HijackWorldItem hijackWorldItem = PingServerContext.getInstance().getHijackWorldItem(platform, worldId);
            final String responseWorldId;
            if (hijackWorldItem != null && version.isAbove(hijackWorldItem.getVersion())) {
                responseWorldId = hijackWorldItem.getHijackWorldId();
            } else {
                responseWorldId = worldId;
            }

            // 准备CountryName
            final String countryName;
            if (!this.getParams().containsKey(QUERY_CHANNEL_V2_REQUEST_LABEL_COUNTRY_NAME)) {
                countryName = IpSeekerUtils.isChina(ipZone) ? "中国" : ipZone.getMainInfo();
            } else {
                countryName = this.getParams().get(QUERY_CHANNEL_V2_REQUEST_LABEL_COUNTRY_NAME);
            }
            // 读取结果
            final List<Object> channelInfo = PingServerContext.getInstance().getChannelInfo(responseWorldId, continent, countryName, ipZone.getMainInfo());
            final Response response = new Response(responseWorldId, channelInfo);

            // 打印以及回包
            LOGGER.info("QueryChannelListV2, deviceId={}, params={}, hijackItem={}, continent={}, countryName={}, ipZone={}, result={}, channel={}",
                    deviceId, this.getParams(), hijackWorldItem, continent, countryName, ipZone.getMainInfo(), response, this.getChannel());
            this.setOkWithResult(JsonUtils.toJsonString(response));
        } catch (Exception e) {
            LOGGER.error("QueryChannelListV2 fail, params={}, channel={}, e=", this.getParams(), this.getChannel(), e);
            this.setErrorCode(PingErrorCode.LACK_PARAMS);
        }
    }
}
package com.yorha.cnc.ping.channelTrie;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.json.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分支节点
 *
 * <AUTHOR>
 */
public class BranchNode extends TreeNode {

    private static final Logger LOGGER = LogManager.getLogger(BranchNode.class);

    protected final List<LeafNode> leafNodes;
    protected final List<BranchNode> branchNodes;

    public BranchNode(String nodeKey) {
        this.nodeName = nodeKey;
        leafNodes = new ArrayList<>();
        branchNodes = new ArrayList<>();
    }

    /**
     * 递归构建通道树
     *
     * @param channelInfo yaml文件解析出的channelInfo
     * @return 根节点
     */
    public void buildChannelTree(Map<String, Object> channelInfo) {
        for (Map.Entry<String, Object> entry : channelInfo.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof ArrayList<?>) {
                List<Object> list = new ArrayList<>((List<?>) value);
                leafNodes.add(new LeafNode(entry.getKey(), list));
            } else if (value instanceof Map) {
                Map<String, Object> infos = JsonUtils.parseObject(JsonUtils.toJsonString(value), HashMap.class);
                BranchNode branchNode = new BranchNode(entry.getKey());
                branchNode.buildChannelTree(infos);
                branchNodes.add(branchNode);
            } else {
                LOGGER.error("channelTree build failed, yaml format not correct! channelInfo = {}", channelInfo);
                throw new GeminiException("channelTree build failed");
            }
        }
    }

    /**
     * 通道信息转换成map
     *
     * @return map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        for (LeafNode leafNode : leafNodes) {
            map.put(leafNode.nodeName, leafNode.getChannelInfo());
        }
        for (BranchNode branchNode : branchNodes) {
            map.put(branchNode.nodeName, branchNode.toMap());
        }
        return map;
    }

    /**
     * 查找通道叶节点
     */

    public List<Object> seekLeafNode(List<String> path, int index) {
        List<Object> res = null;
        final String defaultLeaf = "default_channel";
        if (index >= path.size()) {
            throw new GeminiException("search path invalid");
        }
        String curLevel = path.get(index);
        // 搜索叶子节点
        for (LeafNode leafNode : leafNodes) {
            if (leafNode.matchNode(curLevel)) {
                return leafNode.getChannelInfo();
            }
        }
        if (index == 0) {
            // 第一级分支全匹配
            for (BranchNode branchNode : branchNodes) {
                if (branchNode.nodeName.equals(curLevel)) {
                    return branchNode.seekLeafNode(path, index + 1);
                }
            }

        } else {
            // 搜索分支节点
            for (BranchNode branchNode : branchNodes) {
                if (branchNode.matchNode(curLevel)) {
                    return branchNode.seekLeafNode(path, index + 1);
                }
            }
        }

        // 都没有找到则获取默认通道(我知道可以放到搜索叶子节点里面，只是康乐看不懂哈哈）
        for (LeafNode leafNode : leafNodes) {
            if (leafNode.matchNode(defaultLeaf)) {
                res = leafNode.getChannelInfo();
                break;
            }
        }
        return res;
    }
}

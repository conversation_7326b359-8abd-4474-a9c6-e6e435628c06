package com.yorha.cnc.ping.channelTrie;

import java.util.ArrayList;
import java.util.List;

/**
 * 叶节点，节点值包含了通道信息
 *
 * <AUTHOR>
 */
public class LeafNode extends TreeNode {
    private final List<Object> channelInfo;

    public LeafNode(String nodeKey, List<Object> nodeValue) {
        this.nodeName = nodeKey;
        channelInfo = new ArrayList<>(nodeValue);
    }

    @Override
    public boolean isLeafNode() {
        return true;
    }

    public List<Object> getChannelInfo() {
        return new ArrayList<>(channelInfo);
    }
}

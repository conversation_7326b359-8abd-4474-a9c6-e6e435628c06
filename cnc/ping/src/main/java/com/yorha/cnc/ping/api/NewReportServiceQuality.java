package com.yorha.cnc.ping.api;

import com.github.jarod.qqwry.IPZone;
import com.yorha.cnc.ping.PingErrorCode;
import com.yorha.cnc.ping.PingServerContext;
import com.yorha.common.utils.ip.IpSeekerUtils;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import qlog.flow.QlogCncChannelReport;

import java.net.InetSocketAddress;
import java.util.Map;

/**
 * 服务质量上报
 *
 * <AUTHOR>
 */
public class NewReportServiceQuality extends HttpApi {
    private static final Logger LOGGER = LogManager.getLogger(NewReportServiceQuality.class);

    public NewReportServiceQuality(Channel channel, FullHttpRequest request, FullHttpResponse response, Map<String, String> params) {
        super(channel, request, response, params);
    }

    @Override
    protected void realRun() {
        InetSocketAddress inSocket = (InetSocketAddress) this.getChannel().remoteAddress();
        String ip = inSocket.getAddress().getHostAddress();
        IPZone ipZone = IpSeekerUtils.getIpZone(ip);
        try {
            QlogCncChannelReport.init(PingServerContext.getInstance().getServer())
                    .setType(this.getParams().get("type"))
                    .setDeviceId(this.getParams().get("deviceId"))
                    .setOpenId(0)
                    .setNewOpenId(this.getParams().getOrDefault("openId", "0"))
                    .setPlayerId(Long.parseLong(this.getParams().getOrDefault("playerId", "0")))
                    .setWorlId(Integer.parseInt(this.getParams().get("worldId")))
                    .setZoneId(Integer.parseInt(this.getParams().getOrDefault("zoneId", "0")))
                    .setChannelName(this.getParams().get("channelName"))
                    .setCostMs(Long.parseLong(this.getParams().get("costMs")))
                    .setClientIp(ip)
                    .setClientCountry(this.getParams().getOrDefault("countryName", ""))
                    .setServerCountry(ipZone.getMainInfo())
                    .setSystemHardware(this.getParams().getOrDefault("systemHardware", ""))
                    .setNetType(this.getParams().getOrDefault("netType", ""))
                    .setGear(Integer.parseInt(this.getParams().getOrDefault("gear", "0")))
                    .sendLocalQlog();
            LOGGER.info("NewReportServiceQuality Service Quality Success, params={}, ipZone={}, channel={}", this.getParams(), ipZone, this.getChannel());
            this.setOk();
        } catch (Exception e) {
            LOGGER.error("NewReportServiceQuality Service Quality fail, params={}, channel={}, e=", this.getParams(), this.getChannel(), e);
            this.setErrorCode(PingErrorCode.LACK_PARAMS);
        }
    }
}

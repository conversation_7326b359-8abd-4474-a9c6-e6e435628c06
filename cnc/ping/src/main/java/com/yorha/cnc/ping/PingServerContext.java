package com.yorha.cnc.ping;

import com.google.common.collect.Maps;
import com.yorha.cnc.ping.channelTrie.ChannelTrie;
import com.yorha.cnc.ping.channelTrie.TreeChannelTrie;
import com.yorha.cnc.ping.common.ClientPlatform;
import com.yorha.cnc.ping.common.HijackWorldItem;
import com.yorha.cnc.ping.common.WorldName;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.YamlUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public final class PingServerContext {
    private static final Logger LOGGER = LogManager.getLogger(PingServerContext.class);
    /**
     * 劫持的字段。
     */
    private static final String HIJACK_WORLD_YAML_LABEL_WORLD_ID = "worldId";
    private static final String HIJACK_WORLD_YAML_LABEL_MAX_VERSION = "maxVersion";
    private static final String HIJACK_WORLD_YAML_LABEL_HIJACK_WORLD_ID = "hijackWorldId";
    /**
     * 文件路径。
     */
    private static final String DEFAULT_YAML_PATH = "./multichannel.yaml";
    private static final String WORLD_NAME_YAML_PATH = "./worldName.yaml";
    private static final String HIJACK_WORLD_YAML_PATH = "./hijackWorld.yaml";

    private static class LazyHolder {
        private static final PingServerContext INSTANCE = new PingServerContext();
    }

    public static PingServerContext getInstance() {
        return LazyHolder.INSTANCE;
    }

    /**
     * 频道前缀树
     */
    private final ChannelTrie channelTrie = new TreeChannelTrie();
    /**
     * 大区名
     */
    private volatile Map<String, WorldName> worldListMap = Collections.emptyMap();
    /**
     * 劫持香菇按数据。
     */
    private volatile Map<ClientPlatform, Map<String, HijackWorldItem>> hijackWorldNameMap = Collections.emptyMap();
    /**
     * 更新锁。
     */
    private final ReentrantLock lock = new ReentrantLock(true);

    private PingServer server;

    private PingServerContext() {
    }

    /**
     * 初始化进程上下文。
     *
     * @param server 服务器。
     */
    void init(PingServer server) {
        this.lock.lock();
        try {
            if (this.server != null) {
                throw new GeminiException("PingServerContext is already init, elderServer={}, server={}", this.server, server);
            }
            this.server = server;
            this.reloadYaml();
        } finally {
            this.lock.unlock();
        }
    }

    /**
     * 本服所属大洲。
     *
     * @return 大洲。
     */
    public String getContinent() {
        return this.server.getContinent();
    }

    /**
     * 频道信息。
     *
     * @param worldId   世界id。
     * @param continent 大洲信息。
     * @param country   国家信息。
     * @param ipMainInf 城市信息。
     * @return channel列表。
     */
    public List<Object> getChannelInfo(String worldId, String continent, String country, String ipMainInf) {
        return this.channelTrie.getChannelInfo(worldId, continent, country, ipMainInf);
    }

    /**
     * 世界列表。
     *
     * @return 直接列表。
     */
    public Collection<WorldName> getWorldList() {
        return this.worldListMap.values();
    }

    /**
     * 返回劫持列表。
     *
     * @param platform 平台信息。
     * @param worldId  世界id。
     * @return null 表示不存在，存在则返回具体数据。
     */
    public HijackWorldItem getHijackWorldItem(final ClientPlatform platform, final String worldId) {
        final Map<String, HijackWorldItem> itemMap = this.hijackWorldNameMap.get(platform);
        if (itemMap == null) {
            return null;
        }
        return itemMap.get(worldId);
    }

    /**
     * @return ping server。
     */
    public PingServer getServer() {
        return server;
    }

    /**
     * 重载yaml。
     */
    public void reloadYaml() {
        LOGGER.info("reloadYaml start");
        this.lock.lock();
        try {
            // 读取数据
            final Map<String, Object> channelInfo = Collections.unmodifiableMap(this.loadYaml2Map(DEFAULT_YAML_PATH));
            final Map<String, WorldName> worldListMap = this.loadWorldListMap(channelInfo);
            final Map<ClientPlatform, Map<String, HijackWorldItem>> hijack = this.loadHijackWorldMap(channelInfo);
            // load数据
            this.channelTrie.loadChannelTrie(channelInfo);
            this.worldListMap = Collections.unmodifiableMap(worldListMap);
            this.hijackWorldNameMap = Collections.unmodifiableMap(hijack);
        } finally {
            this.lock.unlock();
        }
        LOGGER.info("reloadYaml end");
    }

    /**
     * load world配置。
     *
     * @param channelInfo 当前channel信息。
     * @return map。
     */
    private Map<String, WorldName> loadWorldListMap(Map<String, Object> channelInfo) {
        if (!this.server.isTestEnv()) {
            return Collections.emptyMap();
        }
        Map<String, WorldName> result = new HashMap<>();
        Map<String, Object> worldList = this.loadYaml2Map(WORLD_NAME_YAML_PATH);
        for (String worldId : channelInfo.keySet()) {
            if ("default_channel".equals(worldId)) {
                continue;
            }
            WorldName worldName = new WorldName();
            if (!worldList.containsKey(worldId)) {
                worldName.setId(worldId);
                worldName.setName(worldId);
                result.put(worldId, worldName);
                continue;
            }
            worldName.setName((String) worldList.get(worldId));
            worldName.setId(worldId);
            result.put(worldId, worldName);
        }
        return result;
    }

    /**
     * load劫持world的配置。
     *
     * @param channelInfo 当前channel信息。
     * @return map。
     */
    @SuppressWarnings("unchecked")
    private Map<ClientPlatform, Map<String, HijackWorldItem>> loadHijackWorldMap(Map<String, Object> channelInfo) {
        // 读取劫持配置
        final Map<String, Object> yamlMap = this.loadYaml2Map(HIJACK_WORLD_YAML_PATH);

        final Map<ClientPlatform, Map<String, HijackWorldItem>> result = Maps.newHashMap();
        for (final String platform : yamlMap.keySet()) {
            // 检查平台是否正确
            final ClientPlatform clientPlatform = ClientPlatform.forName(platform);
            if (clientPlatform == ClientPlatform.UNKNOWN) {
                throw new GeminiException("refreshHijackWorldMap path={}, platform={} not right", HIJACK_WORLD_YAML_PATH, platform);
            }

            // 检查数据
            final List<Map<String, Object>> items = (List<Map<String, Object>>) yamlMap.get(platform);
            for (Map<String, Object> item : items) {
                final String worldId = (String) item.get(HIJACK_WORLD_YAML_LABEL_WORLD_ID);
                final String maxVersion = (String) item.get(HIJACK_WORLD_YAML_LABEL_MAX_VERSION);
                final String hijackWorldId = (String) item.get(HIJACK_WORLD_YAML_LABEL_HIJACK_WORLD_ID);
                // 检查配置的worldId是否正确
                if (!channelInfo.containsKey(worldId)) {
                    throw new GeminiException("refreshHijackWorldMap path={}, platform={}, worldId={} useless",
                            HIJACK_WORLD_YAML_PATH, clientPlatform, worldId);
                }
                // 检查是否已经设置过
                if (result.getOrDefault(clientPlatform, Collections.emptyMap()).containsKey(worldId)) {
                    throw new GeminiException("refreshHijackWorldMap path={}, platform={}, worldId={} repeat",
                            HIJACK_WORLD_YAML_PATH, clientPlatform, worldId);
                }
                // 检查被劫持的id是否正确
                if (!channelInfo.containsKey(hijackWorldId)) {
                    throw new GeminiException("refreshHijackWorldMap path={}, platform={}, worldId={}, hijackWorldId={} not right",
                            HIJACK_WORLD_YAML_PATH, clientPlatform, worldId, hijackWorldId);
                }

                // 注入到容器
                final HijackWorldItem hijackWorldName = new HijackWorldItem(clientPlatform, worldId, maxVersion, hijackWorldId);
                result.computeIfAbsent(clientPlatform, key -> Maps.newHashMap()).put(worldId, hijackWorldName);
            }
        }

        // 确保子集无法修改
        for (final ClientPlatform clientPlatform : ClientPlatform.values()) {
            if (!result.containsKey(clientPlatform)) {
                continue;
            }
            result.put(clientPlatform, Collections.unmodifiableMap(result.get(clientPlatform)));
        }

        // 返回结果
        return result;
    }


    /**
     * 从对应文件路径中读取yaml数据。
     *
     * @param path 文件路径。
     * @return 内存数据。
     */
    private Map<String, Object> loadYaml2Map(final String path) {
        final File yamlFile = new File(path);
        if (!yamlFile.exists()) {
            LOGGER.warn("loadYaml2Map path={}, file not exists", path);
            return Collections.emptyMap();
        }
        final StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader fileReader = new BufferedReader(new FileReader(yamlFile));
            String temp;
            while ((temp = fileReader.readLine()) != null) {
                if (temp.trim().startsWith("#")) {
                    continue;
                }
                stringBuilder.append(temp).append("\n");
            }
        } catch (IOException e) {
            LOGGER.error("loadYaml2Map path={} fail, e=", path, e);
            throw new RuntimeException(e.getMessage());
        }
        final String yaml = stringBuilder.toString();
        final Map<String, Object> result = YamlUtils.newInstance(yaml);
        LOGGER.info("loadYaml2Map path={}, yaml={} result={}", path, yaml, result);
        return result != null ? result : Collections.emptyMap();
    }
}

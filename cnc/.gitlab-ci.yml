variables:
  JAVA_TOOL_OPTIONS: "-Dfile.encoding=UTF-8"
  GIT_DEPTH: 0
  GIT_STRATEGY: clone
stages:
  - build
  - quality-check

cnc_build:
  stage: build
  script:
    - cd cnc && chmod +x ./gradlew && ./gradlew build -x test --build-cache


cnc_checkstyle:
  stage: quality-check
  script:
    - cd cnc && chmod +x ./gradlew && ./gradlew checkstyleMain -PrunCodeQuality
  except:
    - master
    - dev

cnc_pmd:
  stage: quality-check
  script:
    - cd cnc && chmod +x ./gradlew && ./gradlew pmdMain -PrunCodeQuality
  except:
    - master
    - dev
#  when: manual
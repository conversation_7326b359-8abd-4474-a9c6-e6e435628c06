package com.yorha.cnc.battle.context;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleLogService;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.AdditionSysProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

import static com.yorha.cnc.battle.core.BattleGround.getBattleLog;


/**
 * 一段战斗关系的上下文，生命周期随战斗关系，但战斗关系终止之后，context仍可以被RoleContext持有
 * <p>
 * 这里不仅仅是战报
 *
 * <AUTHOR>
 */
public class BattleRelationContext {
    private static final Logger LOGGER = LogManager.getLogger(BattleRelationContext.class);

    /**
     * 开始结束时间戳，永远和tick时间保持一致
     */
    private long startMillis = 0;
    /**
     * 如果并不是在战斗tick中结束的战斗关系，endMillis将会保持在上一个tick的时间
     */
    private long endMillis = 0;
    private final Point location;
    private final BattleRelation relation;
    private BattleRecord.RecordOne recordOne;
    private BattleLogService battleLogService;
    /**
     * 当前回合上下文
     */
    private RoundContext curRoundCtx;
    /**
     * 换将切分战报后会存在这里
     */
    private final List<BattleRecord.RecordOne> historyRecords;

    public BattleRelationContext(BattleRelation relation, Point point) {
        this.relation = relation;
        this.location = point;

        if (relation.getGround().isInBigScene()) {
            this.battleLogService = new BattleLogService(this);
        }
        this.recordOne = new BattleRecord.RecordOne(relation.getId(), relation.getOne(), relation.getOther(), relation.getBattleType());
        this.historyRecords = Lists.newArrayList();
    }

    public void clear() {
        this.startMillis = 0;
        this.endMillis = 0;
        if (relation.getGround().isInBigScene()) {
            this.battleLogService = new BattleLogService(this);
        }
        this.curRoundCtx = null;
        this.recordOne = new BattleRecord.RecordOne(relation.getId(), relation.getOne(), relation.getOther(), relation.getBattleType());
    }

    public BattleRecord.RoleRecord getRoleRecord(long roleId) {
        return recordOne.getOneRecord().getRoleId() == roleId ? recordOne.getOneRecord() : recordOne.getOtherRecord();
    }

    public BattleRole getOneRole() {
        return relation.getOne();
    }

    public BattleRole getOtherRole() {
        return relation.getOther();
    }

    public long getStartMillis() {
        return startMillis;
    }

    public long getEndMillis() {
        return endMillis;
    }

    public Point getLocation() {
        return location;
    }

    public BattleRecord.RoleRecord getOneRecord() {
        return recordOne.getOneRecord();
    }

    public BattleRecord.RoleRecord getOtherRecord() {
        return recordOne.getOtherRecord();
    }

    public BattleRelation getRelation() {
        return relation;
    }

    public BattleRecord.RecordOne getRecordOne() {
        return recordOne;
    }

    /**
     * 当前relation一回合开始了
     */
    public void prepareRound(BattleTickContext tickContext) {
        if (startMillis <= 0) {
            startMillis = tickContext.getTickMillis();
        }
        endMillis = tickContext.getTickMillis();
        boolean isFirstRound = false;
        if (curRoundCtx == null) {
            curRoundCtx = new RoundContext(this, 1);
            // 首回合先把所有的buff记录一下
            isFirstRound = true;
        } else {
            curRoundCtx = new RoundContext(this, curRoundCtx.getNumber() + 1);
        }
        curRoundCtx.init(this, isFirstRound);
    }

    public void afterSettleRound(BattleTickContext tickContext) {
        this.endMillis = tickContext.getTickMillis();
        try {
            if (battleLogService != null) {
                // 打包当前回合
                battleLogService.addRoundLog(curRoundCtx().packCurRound());
                // 投递战斗日志
                battleLogService.postRoundLogAfterSettleRound();
            }
        } catch (Exception e) {
            LOGGER.error("BattleErrLog", e);
        }
        curRoundCtx().clear();
    }

    /**
     * 获取当前回合
     */
    public RoundContext curRoundCtx() {
        return curRoundCtx;
    }

    /**
     * 获取当前回合数
     */
    public int curRoundNumber() {
        return curRoundCtx.getNumber();
    }

    public void end(@Nullable BattleTickContext tickContext) {
        if (tickContext != null) {
            endMillis = tickContext.getTickMillis();
        }
        endRecord();
        try {
            if (battleLogService != null) {
                // 打包当前回合信息
                battleLogService.addRoundLog(curRoundCtx().packCurRound());
                // 投递战斗日志
                battleLogService.postRoundLog();
                // 投递战斗日志概要
                battleLogService.postBattleLogBrief();
            }
        } catch (Exception e) {
            LOGGER.error("BattleErrLog ", e);
        }
        // 日志-战斗日志结束
        getBattleLog().printfRelationOver(relation);
    }

    private void setLeftAlive() {
        getOneRecord().setLeftAlive(relation.getOne().soldierAliveCount());
        getOtherRecord().setLeftAlive(relation.getOther().soldierAliveCount());
        for (Map.Entry<Long, Map<Integer, Integer>> entry : relation.getOne().aliveCountByMember().entrySet()) {
            for (Map.Entry<Integer, Integer> sEntry : entry.getValue().entrySet()) {
                getOneRecord().setMbrLeftAlive(entry.getKey(), sEntry.getKey(), sEntry.getValue());
            }
        }
        for (Map.Entry<Long, Map<Integer, Integer>> entry : relation.getOther().aliveCountByMember().entrySet()) {
            for (Map.Entry<Integer, Integer> sEntry : entry.getValue().entrySet()) {
                getOtherRecord().setMbrLeftAlive(entry.getKey(), sEntry.getKey(), sEntry.getValue());
            }
        }
        if (relation.getOne().getGuardTower() != null) {
            getOneRecord().setGuardTowerLeftAlive(relation.getOne().getGuardTower().aliveCount());
        }
        if (relation.getOther().getGuardTower() != null) {
            getOtherRecord().setGuardTowerLeftAlive(relation.getOther().getGuardTower().aliveCount());
        }
    }

    /**
     * relation开始，填充战报初始信息
     */
    public void initRelationRecord() {
        recordOne.setStartMs(this.startMillis);
        fillRole(relation.getOne(), recordOne.getOneRecord());
        fillRole(relation.getOther(), recordOne.getOtherRecord());
        // 日志-战斗日志开始
        getBattleLog().printfRelationInit(relation, recordOne);
    }

    private void endRecord() {
        recordOne.setStartMs(this.startMillis);
        recordOne.setEndMs(this.endMillis);
        recordOne.setLocation(location.getX(), location.getY(), this.relation.getGround().getPointMapId(), this.relation.getGround().getMapType().getNumber());

        setLeftAlive();
    }

    private void fillRole(BattleRole role, BattleRecord.RoleRecord roleRecord) {
        // 概要
        role.getAdapter().fillRole(roleRecord);
        roleRecord.setTotalAlive(role.aliveCount());
        // 成员详情
        role.getAdapter().fillRoleMember(roleRecord);
        // 增益
        AdditionSysProp additionSysProp = AdditionUtil.convert2AdditionSysProp(role.getAllBuffValue());
        roleRecord.setAdditionSysProp(additionSysProp);
        // 防御塔
        if (role.isGuardTowerAlive()) {
            roleRecord.initGuardTower(role.getGuardTower());
        }
    }

    /**
     * 是否需要输出战报
     */
    public boolean needFlushRelation() {
        // 如果这个战斗关系有伤害产生，或者双方其中有一方的战斗目标是对面，就需要输出
        // 换句话就是，没伤害且是AOE技能打到的，不输出战报
        return relation.isHasDamage()
                || (relation.getOne().getActiveTargetId() == relation.getOther().getRoleId()
                || relation.getOther().getActiveTargetId() == relation.getOne().getRoleId());
    }

    public boolean needFlush() {
        return relation.getType() != BattleConstants.BattleRelationType.AreaSkill
                || (relation.getOne().getMasterRole() == null
                && relation.getOther().getMasterRole() == null);
    }

    public void addHistoryRecords() {
        historyRecords.add(recordOne);
    }

    public List<BattleRecord.RecordOne> getHistoryRecords() {
        return historyRecords;
    }
}

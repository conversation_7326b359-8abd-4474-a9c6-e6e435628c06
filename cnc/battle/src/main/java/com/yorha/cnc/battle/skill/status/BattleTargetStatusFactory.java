package com.yorha.cnc.battle.skill.status;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.skill.status.impl.*;
import com.yorha.proto.CommonEnum.StatusType;

import java.util.Map;

/**
 * 根据目标状态来过滤
 *
 * <AUTHOR>
 */
public class BattleTargetStatusFactory {

    private static final Map<StatusType, StatusChecker> TARGET_STATUS_MAP = Maps.newEnumMap(StatusType.class);

    static {
        TARGET_STATUS_MAP.put(StatusType.STT_NONE, new AlwaysTrueCheck());
        TARGET_STATUS_MAP.put(StatusType.ST_CONTAIN_BUFF, new HasBuffStatusChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_PINCER, new InPincerStatusChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_NO_PINCER, new NoPincerStatusChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_CONTAINS_N_SOLDIER_TYPE, new ContainsNTypeSoldierStatusChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_SOLDIER_NUM_BELOW, new SoldierProportionChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_BUILDING_TYPE, new BuildingTypeChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_DEF_BUILDING_TYPE, new DefBuildingTypeChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_HIT_NUM, new SkillHitChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_HERO_POSITION, new HeroPositionChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_GET_BUFF, new GetBuffChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_IS_COLLECTING, new IsCollectingStatusChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_COMMON_RATE, new AlwaysTrueCheck());
        TARGET_STATUS_MAP.put(StatusType.ST_HERO_STAR, new HeroStarChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_SOLDIER_TYPE, new SoldierTypeChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_IS_MONSTER, new IsMonsterChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_OUTSIDE_UNION_TERRITORY, new OutsideUnionTerritory());
        TARGET_STATUS_MAP.put(StatusType.ST_RELEASE_SKILLS, new ActiveSkillChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_SOLDIER_NUM_ABOVE, new SoldierProportionAboveChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_NOT_CONTAIN_BUFF, new NotHasBuffStatusChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_SUMMONING_NUM, new SummonsChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_SUMMONING_GROUP_NUM, new SummonsGroupChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_HAS_SAME_CLAN_BUFF, new HasSameClanBuffChecker());
        TARGET_STATUS_MAP.put(StatusType.ST_IS_IN_WILD, new IsInWildChecker());
    }

    public static IBattleTargetStatusChecker getTargetChecker(StatusType troopState) {
        return TARGET_STATUS_MAP.getOrDefault(troopState, TARGET_STATUS_MAP.get(StatusType.STT_NONE));
    }

    public static IBattleTargetStatusChecker getSelfChecker(StatusType troopState) {
        switch (troopState) {
            case ST_X_ROUND:
                return new RoundIntervalCountChecker();
            case ST_ATK_X_COUNT:
                return new AttackCountChecker();
            default:
                return getTargetChecker(troopState);
        }
    }
}

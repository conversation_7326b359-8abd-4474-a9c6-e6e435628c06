package com.yorha.cnc.battle.common;

import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.BattleTemplateService;
import com.yorha.common.resource.resservice.battle.DamageRatioConf;

/**
 * <AUTHOR>
 */
public class SevereDeadRatio {
    /**
     * 死亡率
     */
    private double deadRatio;
    /**
     * 重伤率
     */
    private double severeRatio;

    public SevereDeadRatio() {
        // 默认走配置
        DamageRatioConf defaultDamageRatioConf = ResHolder.getResService(BattleTemplateService.class).getDefaultDamageRatioConf();
        this.deadRatio = defaultDamageRatioConf.getDeadInSevereRatio();
        this.severeRatio = defaultDamageRatioConf.getSevereWoundRatio();
    }

    public static SevereDeadRatio empty() {
        return new SevereDeadRatio();
    }

    public double getDeadRatio() {
        return Math.max(0, deadRatio);
    }

    public SevereDeadRatio setDeadRatio(double deadRatio) {
        this.deadRatio = deadRatio;
        return this;
    }

    public double getSevereRatio() {
        return Math.max(0, severeRatio);
    }

    public SevereDeadRatio setSevereRatio(double severeRatio) {
        this.severeRatio = severeRatio;
        return this;
    }

    @Override
    public String toString() {
        return "SevereDeadRatio{" +
                "死亡比率=" + deadRatio +
                ", 重伤比率=" + severeRatio +
                '}';
    }
}

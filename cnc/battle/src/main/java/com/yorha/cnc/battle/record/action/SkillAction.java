package com.yorha.cnc.battle.record.action;

import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;


/**
 * <AUTHOR>
 */
public class SkillAction {
    private final boolean isPrepare;
    private final EffectContext info;

    public SkillAction(EffectContext info, boolean isPrepare) {
        this.isPrepare = isPrepare;
        this.info = info;
    }

    public EffectContext getInfo() {
        return info;
    }

    public CommonBattle.BattleEvent convert2Event() {
        return CommonBattle.BattleEvent.newBuilder()
                .setEvent(CommonEnum.BattleEventEnum.BET_SKILL)
                .setSkillEvent(convert2SkillEvent())
                .build();
    }

    private CommonBattle.SkillEvent convert2SkillEvent() {
        CommonBattle.SkillEvent.Builder builder = CommonBattle.SkillEvent.newBuilder()
                .setIsPrepare(isPrepare)
                .setInfo(info.convert2Pb());
        return builder.build();
    }
}

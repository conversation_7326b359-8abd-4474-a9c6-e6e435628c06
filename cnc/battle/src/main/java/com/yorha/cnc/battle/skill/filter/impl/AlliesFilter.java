package com.yorha.cnc.battle.skill.filter.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.filter.ISkillEffectRelationFilter;
import com.yorha.cnc.battle.skill.filter.SkillFilterHelper;

import java.util.Set;

/**
 * 友军
 */
public class AlliesFilter implements ISkillEffectRelationFilter {

    @Override
    public Set<BattleRole> filter(SkillEffect effect, BattleRole castObj, Set<BattleRole> targetSet, BattleRole castTargetObj) {
        // 可战斗筛选
        return SkillFilterHelper.filterByCamp(castObj, targetSet);
    }
}

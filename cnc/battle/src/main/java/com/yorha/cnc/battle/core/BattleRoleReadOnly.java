package com.yorha.cnc.battle.core;

import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.context.BattleRelationContext;
import com.yorha.cnc.battle.context.BattleRoleContext;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 * @date 2023/7/3
 */
public interface BattleRoleReadOnly {
    long getRoleId();

    boolean isMonsterObj();

    boolean isNpcTroop();

    boolean isInSimulator();

    BattleRoleSnapshot getSnapShot(String reason);

    IBattleRoleAdapter getAdapter();

    int getRallyCapacity();

    int aliveCount();

    int tempAliveCount();

    CommonEnum.SceneObjType getType();

    BattleRelationContext getRelationContext(long otherRoleId);

    long getTargetId();

    BattleRoleContext getContext();
}

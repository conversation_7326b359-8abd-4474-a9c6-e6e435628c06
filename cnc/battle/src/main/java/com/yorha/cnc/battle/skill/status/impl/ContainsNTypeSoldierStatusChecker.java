package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.status.IBattleTargetStatusChecker;

/**
 * 包含N种以上的兵种
 *
 * <AUTHOR>
 */
public class ContainsNTypeSoldierStatusChecker extends StatusChecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        return IBattleTargetStatusChecker.numCompare(role.containedAliveSoldierType().size(), Integer.parseInt(params[1]), params[0]);
    }
}

package com.yorha.cnc.battle.record.action;

import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class AtkAction {
    private final boolean isAtkBack;
    public final UnitDataChangeAction unitData;
    /**
     * 护盾抵扣伤害
     */
    private long shieldDamage;

    public AtkAction(boolean isAtkBack) {
        this.isAtkBack = isAtkBack;
        this.unitData = new UnitDataChangeAction();
    }

    public void setShieldDamage(long shieldDamage) {
        this.shieldDamage = shieldDamage;
    }

    public void plusUnitDataChange(long memberRoleId, int unitId, SoldierLossData lossData) {
        unitData.plusUnitDataChange(memberRoleId, unitId, lossData);
    }

    public CommonBattle.BattleEvent convert2Event() {
        return CommonBattle.BattleEvent.newBuilder()
                .setEvent(CommonEnum.BattleEventEnum.BET_ATK)
                .setAtkEvent(convert2AtkEvent())
                .build();
    }

    private CommonBattle.AtkEvent convert2AtkEvent() {
        return CommonBattle.AtkEvent.newBuilder()
                .setType(isAtkBack ? CommonEnum.BattleLogDamageType.BLDT_ATK_BACK : CommonEnum.BattleLogDamageType.BLDT_ATK)
                .setData(unitData.convert2Pb())
                .setDecreaseDamageByShield(shieldDamage)
                .build();
    }
}

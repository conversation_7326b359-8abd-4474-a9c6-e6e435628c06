package com.yorha.cnc.battle.buf.lifecycle.impl;

import com.yorha.cnc.battle.buf.lifecycle.ILifeCycle;
import com.yorha.proto.CommonEnum.LifeCycleType;

import java.text.MessageFormat;

/**
 * 防御次数
 *
 * <AUTHOR>
 */
public class DefendTimesLifeCycle implements ILifeCycle {

    private int times;
    private int max;
    private LifeCycleType type;

    @Override
    public LifeCycleType getType() {
        return type;
    }

    @Override
    public long getValue() {
        return times;
    }

    public DefendTimesLifeCycle(LifeCycleType type, long times) {
        this(type, (int) times);
    }

    public DefendTimesLifeCycle(LifeCycleType type, int times) {
        this.times = times;
        this.type = type;
        this.max = times;
    }

    public int getTimes() {
        return times;
    }


    @Override
    public boolean checkValid() {
        return times > 0;
    }

    @Override
    public void execute() {
        times--;
    }

    /**
     * 剩余信息
     * <p>
     * return 剩余信息提示
     */
    @Override
    public String getMessage() {
        if (times > 0) {
            if (max == times) {
                return MessageFormat.format("本回合开始生效,共{0}次防御次数", this.times);
            } else {
                return MessageFormat.format("剩余{0}次防御次数", this.times);
            }
        } else {
            return "下回合失效";
        }
    }

    @Override
    public void reset() {
        this.times = this.max;
    }
}

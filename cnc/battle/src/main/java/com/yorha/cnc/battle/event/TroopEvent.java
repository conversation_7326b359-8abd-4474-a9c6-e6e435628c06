package com.yorha.cnc.battle.event;

import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.proto.Struct;

/**
 * <AUTHOR>
 */
public class TroopEvent extends BattleRoundEvent {
    private final SoldierNumChangeReason reason;
    private final int soldierNum;
    private final long memberId;
    private final String name;
    private final String clanName;
    private final Struct.PlayerCardHead cardHead;

    public TroopEvent(long memberId, SoldierNumChangeReason reason, int soldierNum, String name, String clanName, Struct.PlayerCardHead cardHead) {
        this.reason = reason;
        this.soldierNum = soldierNum;
        this.memberId = memberId;
        this.name = name;
        this.clanName = clanName;
        this.cardHead = cardHead;
    }

    public SoldierNumChangeReason getReason() {
        return reason;
    }

    public int getSoldierNum() {
        return soldierNum;
    }

    public long getMemberId() {
        return memberId;
    }

    public String getName() {
        return name;
    }

    public String getClanName() {
        return clanName;
    }

    public Struct.PlayerCardHead getCardHead() {
        return cardHead;
    }
}

package com.yorha.cnc.battle.snapshot.unit;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IBattleUnit {
    double calcAttack(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot);

    double calcDefence(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot);

    double calcHp(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot);

    double calcBaseHp(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot);

    double calcTotalAttack();

    double calcTotalDefence();

    double getAtk();

    double getDefence();

    double getHp();

    double getBaseHp();
}

package com.yorha.cnc.battle.handler;

import com.yorha.cnc.battle.adapter.interfaces.IBattleMoveAdapter;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.cnc.battle.skill.*;
import com.yorha.cnc.battle.skill.task.DelayTask;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.vector.Vector2f;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB;

import javax.annotation.Nullable;
import java.util.PriorityQueue;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class SkillHandler extends BattleHandlerAbs<BattleRole> {
    /**
     * 技能释放对象
     * <p>
     * 如果技能释放对象死亡，技能释放失败
     */
    private long skillTargetId;
    /**
     * 技能预释放时，玩家技能释放的朝向
     */
    private Vector2f yaw = null;
    /**
     * 技能预释放时，玩家的坐标
     */
    private Point castRolePoint = null;
    /**
     * 技能预释放时，目标的坐标
     */
    private Point targetRolePoint = null;
    /**
     * 上次释放主动技能的回合
     * <p>
     * 用于保证技能释放和技能预备不在同个回合出现
     */
    private int lastFireActiveSkillRound;
    /**
     * 本回合技能黑板
     */
    private SkillBlackboard skillBlackboard;

    public SkillHandler(BattleRole role) {
        super(role);
        resetBlackboard();
    }

    public void resetBlackboard() {
        this.skillBlackboard = new SkillBlackboard();
    }

    public long getSkillTargetId() {
        return skillTargetId;
    }

    public void setSkillTargetId(long skillTargetId) {
        this.skillTargetId = skillTargetId;
    }

    public int getLastFireActiveSkillRound() {
        return lastFireActiveSkillRound;
    }

    public void setLastFireActiveSkillRound(int lastFireActiveSkillRound) {
        this.lastFireActiveSkillRound = lastFireActiveSkillRound;
    }

    public SkillBlackboard getSkillBlackboard() {
        return skillBlackboard;
    }

    public void prepareNextRoundSkill(BattleTickContext tickContext) {
        BattleHero mainHero = role.getMainHero();
        BattleHero deputyHero = role.getDeputyHero();

        // 移动中不能预释放技能，但原来的设置需要保留
        IBattleMoveAdapter moveAdapter = role.getAdapter().getMoveAdapter();
        if (moveAdapter != null && moveAdapter.isMoving()) {
            if (role.getAdapter().getBattleProp().getTargetId() <= 0) {
                // 没有攻击目标，不能放技能
                return;
            }
        }

        if (mainHero != null) {
            mainHero.setCanFireActiveSkill(false);
        }
        if (deputyHero != null) {
            deputyHero.setCanFireActiveSkill(false);
        }

        // 没有攻击目标，不能放技能
        if (role.getTargetId() <= 0) {
            return;
        }

        // 沉默状态就终止
        if (role.getStateHandler().isinState(BattleConstants.BattleRoleState.SILENCE)) {
            return;
        }

        // 当前回合有技能在释放
        if (getLastFireActiveSkillRound() == tickContext.getGroundRound()) {
            return;
        }

        if (deputyHero != null && deputyHero.canTriggerDeputyHeroSkill()) {
            // 副将技能
            deputyHero.setCanFireActiveSkill(true);

            prepareFireActiveSkill(deputyHero);
        } else if (mainHero != null && (mainHero.canTriggerMainHeroSkill())) {
            // 主将技能
            mainHero.setCanFireActiveSkill(true);

            prepareFireActiveSkill(mainHero);
        }
    }

    public boolean isFiringSkill() {
        BattleHero mainHero = role.getMainHero();
        BattleHero deputyHero = role.getDeputyHero();
        return (mainHero != null && mainHero.canFireActiveSkill()) || (deputyHero != null && deputyHero.canFireActiveSkill());
    }

    private void prepareFireActiveSkill(BattleHero hero) {
        setSkillTargetId(role.getTargetId());

        if (hero.getActiveSkill() != null) {
            // 记录预释放信息
            recordPrepareFireInfo();
            // 准备阶段的广播
            SkillResult skillResult = new SkillResult()
                    .setFireType(CommonEnum.SkillFireType.SFT_PREPARE)
                    .setAttackerId(role.getRoleId())
                    .setSkillId(hero.getActiveSkill().getId())
                    .setHeroId(hero.getId())
                    .setTargetId(skillTargetId);
            if (yaw != null) {
                skillResult.setYaw(StructPB.PointPB.newBuilder().setX((int) yaw.getX()).setY((int) yaw.getY()).build());
            }
            role.getAdapter().broadcastSkillResult(skillResult, BattleConstants.BattleBroadCastNtfReason.PREPARE);
            // 写日志
            BattleLogUtil.logSkillPrepareFire(role, hero.getActiveSkill(), hero);
        }
    }

    private void recordPrepareFireInfo() {
        // 记录预释放的朝向
        recordSkillYaw();
        // 记录预释放的释放者坐标
        recordSkillCastPoint();
        // 记录预释放的目标坐标
        recordSkillTargetPoint();
    }

    /**
     * 记录技能释放者的朝向
     */
    private void recordSkillYaw() {
        yaw = SkillHelper.getSkillYawBeforeRotate(role, role.getTargetRole());
    }

    @Nullable
    public Vector2f getYaw() {
        return yaw;
    }

    /**
     * 记录技能释放者所在的坐标
     */
    private void recordSkillCastPoint() {
        castRolePoint = Point.valueOf(role.getAdapter().getCurPoint().getX(), role.getAdapter().getCurPoint().getY());
    }

    public Point getCastRolePoint() {
        return castRolePoint;
    }

    /**
     * 记录技能目标所在的坐标
     */
    private void recordSkillTargetPoint() {
        if (role.getTargetRole() == null) {
            return;
        }
        targetRolePoint = Point.valueOf(role.getTargetRole().getAdapter().getCurPoint().getX(), role.getTargetRole().getAdapter().getCurPoint().getY());
    }

    public Point getTargetRolePoint() {
        return targetRolePoint;
    }

    private void clearPrePareFireInfo() {
        yaw = null;
        castRolePoint = null;
        targetRolePoint = null;
    }

    public void tryFireDelaySkill(BattleTickContext tickContext) {
        PriorityQueue<DelayTask> delayTaskList = role.getContext().delayTaskList;
        while (!delayTaskList.isEmpty()) {
            DelayTask task = delayTaskList.peek();
            if (tickContext.getGroundRound() >= task.getRunRound()) {
                delayTaskList.poll();
                task.run(tickContext);
            } else {
                break;
            }
        }
    }

    /**
     * 释放主动技能,主将和副将必定不会同时释放技能
     */
    public boolean fireActiveSkill(BattleTickContext tickContext) {
        BattleHero mainHero = role.getMainHero();
        BattleHero deputyHero = role.getDeputyHero();

        boolean isSuccessFire = false;
        boolean thisRoundCanFire = false;

        // 主将存在，主将可以释放技能（满足怒气条件）
        if (mainHero != null && mainHero.canFireActiveSkill()) {
            // 本回合能放技能
            thisRoundCanFire = true;
            // 先把主将设置为不能释放技能
            mainHero.setCanFireActiveSkill(false);
            // 如果副将不为空，下次就是副将放技能了
            if (deputyHero != null) {
                deputyHero.startDeputyHeroTrigger();
            }
            if (role.getGround().isRoleAlive(getSkillTargetId())) {
                // 技能目标存活，扣怒气，释放技能
                mainHero.decreaseAngerAfterSkillFire();
                isSuccessFire = this.fireHeroActiveSkill(mainHero);
            }
        } else if (deputyHero != null && deputyHero.canFireActiveSkill()) {
            // 本回合能放技能
            thisRoundCanFire = true;
            // 先把副将设置为不能释放技能
            deputyHero.setCanFireActiveSkill(false);
            // 重置为主将释放技能
            deputyHero.resetDeputyHeroTriggerRound();
            if (role.getGround().isRoleAlive(getSkillTargetId())) {
                // 技能目标存活，释放技能
                isSuccessFire = this.fireHeroActiveSkill(deputyHero);
            }
        }
        if (thisRoundCanFire) {
            // 只要释放了，无论成不成功，都应该把本回合记录为放技能
            setLastFireActiveSkillRound(tickContext.getGroundRound());
            // 释放技能后，清除预释放信息，NOTE(furson): 这里清除预释放信息让吟唱技能无法使用预释放的位置信息，吟唱技能以实际释放回合为准了，策划要改就改这里
            clearPrePareFireInfo();
        }
        return isSuccessFire;
    }

    private boolean fireHeroActiveSkill(BattleHero hero) {
        Skill activeSkill = hero.getActiveSkill();
        if (activeSkill == null) {
            return false;
        }
        SkillSystem.fire(role, role.getGround().getRole(getSkillTargetId()), hero.getId(), activeSkill, false, null, null);
        role.getContext().drawRoundPoint();
        // 记录主动技能
        getSkillBlackboard().setActiveSkill(activeSkill);
        return true;
    }
}

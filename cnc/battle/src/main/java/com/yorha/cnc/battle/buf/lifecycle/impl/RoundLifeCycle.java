package com.yorha.cnc.battle.buf.lifecycle.impl;

import com.yorha.cnc.battle.buf.lifecycle.ILifeCycle;
import com.yorha.proto.CommonEnum.LifeCycleType;

import java.text.MessageFormat;

/**
 * 周期性的生命周期
 *
 * <AUTHOR>
 */
public class RoundLifeCycle implements ILifeCycle {

    private int round;
    private int max;
    private LifeCycleType type;

    public RoundLifeCycle(LifeCycleType type, long round) {
        this(type, (int) round);
    }

    public RoundLifeCycle(LifeCycleType type, int round) {
        this.round = round;
        this.type = type;
        this.max = round;
    }

    @Override
    public LifeCycleType getType() {
        return type;
    }

    @Override
    public long getValue() {
        return this.round;
    }

    @Override
    public boolean checkValid() {
        // 这里为什么是 >=0? listen:
        // 因为buff的execute在每回合的buff tick中执行，buff tick在battle tick之前
        // tick1加入的round=3的buff，理论上在tick4的战斗中也能生效，但是tick4开始的时候，round就已经减少为0了
        return this.round >= 0;
    }

    @Override
    public void execute() {
        this.round--;
    }


    public int getRound() {
        return round;
    }

    /**
     * 剩余信息
     * <p>
     * return 剩余信息提示
     */
    @Override
    public String getMessage() {
        if (this.round > 0) {
            if (max == this.round) {
                return MessageFormat.format("本回合开始生效,共{0}回合", this.round);
            } else {
                return MessageFormat.format("剩余{0}回合", this.round);
            }
        } else {
            return "下回合失效";
        }
    }

    @Override
    public void reset() {
        this.round = this.max;
    }
}


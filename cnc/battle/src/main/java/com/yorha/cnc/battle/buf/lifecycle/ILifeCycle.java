package com.yorha.cnc.battle.buf.lifecycle;

import com.yorha.proto.CommonEnum.LifeCycleType;

/**
 * <AUTHOR>
 */
public interface ILifeCycle {

    /**
     * 类型
     *
     * @return 类型
     */
    LifeCycleType getType();

    /**
     * buf值
     *
     * @return buf值
     */
    long getValue();

    /**
     * 检测有效性
     *
     * @return 是否有效
     */
    boolean checkValid();

    /**
     * 使用一下
     */
    void execute();

    /**
     * 剩余信息
     * <p>
     * return 剩余信息提示
     */
    String getMessage();


    /**
     * 重置
     */
    void reset();

}

package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.status.IBattleTargetStatusChecker;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import res.template.SkillConfigTemplate;

import java.util.Map;

/**
 * 技能命中数
 *
 * <AUTHOR>
 */
public class SkillHitChecker extends StatusChecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        String operation = params[0];
        int count = Integer.parseInt(params[1]);
        int category = Integer.parseInt(params[2]);

        for (Map.Entry<Integer, Integer> entry : role.getSkillBlackboard().getSkillHitCountMap().entrySet()) {
            SkillConfigTemplate skillTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillTemplate(entry.getKey());
            if (skillTemplate.getSkillId() == category) {
                return IBattleTargetStatusChecker.numCompare(entry.getValue(), count, operation);
            }
        }
        return false;
    }
}

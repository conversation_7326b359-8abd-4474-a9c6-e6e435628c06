package com.yorha.cnc.battle.adapter.interfaces;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.db.tcaplus.msg.GameDbReq;
import com.yorha.common.resource.resservice.battle.DamageRatioConf;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.proto.CommonEnum;

import java.util.Set;

/**
 * 战斗场景扩展
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
public interface IBattleGroundAdapter {
    Set<Long> allEntityIds();
    int allEntityCount();
    long now();
    boolean isBattleRoleExists(long roleId);
    int getMapId();
    BattleRole getBattleRoleById(long entityId);
    Set<BattleRole> getBatchBattleRoleById(Set<Long> skillMarkTarget);
    BattleRole createAreaSkill(BattleRole owner, int skillId, Point point, Integer lifeTime);
    long getBuildingOwnerClanId(Point point);
    Set<BattleRole> getAoiBattleRoles(Shape shape);

    // ========择敌仇恨========
    void addHate(IBattleRoleAdapter one, IBattleRoleAdapter other);
    void removeHate(IBattleRoleAdapter one, IBattleRoleAdapter other);
    boolean isHate(IBattleRoleAdapter one, IBattleRoleAdapter other);

    void tellGameDb(GameDbReq<?> msg);
    DamageRatioConf getDamageRatioConf(long roleId, CommonEnum.BattleType battleType, CommonEnum.SceneObjType objType, CommonEnum.DamageRatioTypeEnum ratioTypeEnum);
}

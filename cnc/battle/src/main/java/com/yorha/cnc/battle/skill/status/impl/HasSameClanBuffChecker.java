package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.buf.Buff;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测-拥有同联盟友方添加的buff
 * <AUTHOR>
 */
public class HasSameClanBuffChecker extends StatusChecker {

    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        if (role == null){
            return false;
        }
        if (params.length != 2){
            return false;
        }
        int targetBuffIdConfig = Integer.parseInt(params[0]);
        int tagConfig = Integer.parseInt(params[1]);

        // 参数2: 1(从自身持有BUFF判断)  2(从普攻目标持有BUFF判断)
        List<Buff> allBuff = new ArrayList<>();
        if (tagConfig == 1){
            allBuff = role.getBuffHandler().getAllBuff();
        }
        if (tagConfig == 2){
            BattleRole targetRole = role.getTargetRole();
            if (targetRole == null){
                return false;
            }
            allBuff = targetRole.getBuffHandler().getAllBuff();
        }
        for (Buff buff : allBuff) {
            if (targetBuffIdConfig != 0 && buff.getTemplateId() != targetBuffIdConfig){
                continue;
            }
            BattleRole giver = buff.getGiver();
            if (giver == null){
                continue;
            }
            CommonEnum.Camp campEnum = giver.getAdapter().getCampEnum();
            if (campEnum != role.getAdapter().getCampEnum()){
                continue;
            }
            return true;
        }
        return false;
    }
}

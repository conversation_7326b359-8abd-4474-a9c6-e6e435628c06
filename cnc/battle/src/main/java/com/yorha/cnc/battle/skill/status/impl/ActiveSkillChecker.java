package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.Skill;
import com.yorha.cnc.battle.skill.SkillEffect;

/**
 * 检测-指定主动技能释放后
 *
 * <AUTHOR>
 */
public class ActiveSkillChecker extends Status<PERSON>hecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        int position = 0;
        Skill activeSkill = role.getSkillBlackboard().getActiveSkill();
        if (activeSkill != null) {
            position = activeSkill.getSkillTemplate().getSkillId();
        }
        return position == Integer.parseInt(params[0]);
    }
}

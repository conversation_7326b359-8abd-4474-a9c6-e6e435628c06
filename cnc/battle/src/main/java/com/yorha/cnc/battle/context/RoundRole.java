package com.yorha.cnc.battle.context;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.RoundRoleAction;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RoundRole {
    private final long roleId;
    /**
     * 回合中发生的行为
     */
    private final RoundRoleAction roleAction;
    /**
     * 受到的所有伤害
     */
    private final List<DamageContext> dmgList;

    public RoundRole(BattleRole role) {
        this.roleId = role.getRoleId();
        this.roleAction = new RoundRoleAction(role);
        this.dmgList = Lists.newArrayList();
    }

    public RoundRoleAction getRoleAction() {
        return roleAction;
    }

    public long getRoleId() {
        return roleId;
    }

    public List<DamageContext> getDmgList() {
        return dmgList;
    }

    public void addDmgList(DamageContext ctx) {
        dmgList.add(ctx);
    }
}

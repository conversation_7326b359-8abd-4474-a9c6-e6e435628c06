package com.yorha.cnc.battle.handler;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class AdditionHandler extends BattleHandlerAbs<BattleRole> {
    public AdditionHandler(BattleRole role) {
        super(role);
    }

    /**
     * 获取加成最终值
     * value = battleRole提供
     *
     * @return 万分比
     */
    public long getAddition(CommonEnum.BuffEffectType additionId) {
        long ret = 0;
        // 战斗buff加成
        ret += getBuffAddition(additionId);
        // 英雄加成
        ret += getHeroAddition(additionId);
        // 有新增模块往这里加哦！


        return ret;
    }

    /**
     * 获取加成最终值Map
     * value = battleRole提供
     *
     * @return 万分比
     */
    public Map<CommonEnum.BuffEffectType, Long> getAllAddition() {
        Map<CommonEnum.BuffEffectType, Long> ret = Maps.newHashMap();
        // 战斗buff加成
        AdditionUtil.mergeAddition(ret, getBuffAllAddition());
        // 英雄加成
        AdditionUtil.mergeAddition(ret, getHeroAllAddition());
        // 有新增模块往这里加哦！


        return ret;
    }

    /**
     * 获取加成最终值
     * value = battleRole提供 + 战斗外围提供
     *
     * @return 万分比
     */
    public long getFinalAddition(CommonEnum.BuffEffectType buff) {
        long fValue = 0;
        if (role.getAdapter().getAdditionAdapter() == null) {
            return fValue;
        }
        fValue += role.getAdapter().getAdditionAdapter().getAddition(buff);
        return fValue;
    }

    /**
     * 获取加成最终值Map
     * value = battleRole提供 + 战斗外围提供
     *
     * @return 万分比
     */
    public Map<CommonEnum.BuffEffectType, Long> getAllFinalAddition() {
        Map<CommonEnum.BuffEffectType, Long> ret = Maps.newHashMap();
        if (role.getAdapter().getAdditionAdapter() == null) {
            return ret;
        }
        for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : role.getAdapter().getAdditionAdapter().getAllAdditions().entrySet()) {
            ret.put(entry.getKey(), ret.getOrDefault(entry.getKey(), 0L) + entry.getValue());
        }
        return ret;
    }

    private long getBuffAddition(CommonEnum.BuffEffectType additionId) {
        return role.getBuffHandler().getBuffValue(additionId);
    }

    private Map<CommonEnum.BuffEffectType, Long> getBuffAllAddition() {
        return role.getBuffHandler().getAllBuffValue();
    }

    private Map<CommonEnum.BuffEffectType, Long> getHeroAllAddition() {
        Map<CommonEnum.BuffEffectType, Long> ret = Maps.newHashMap();
        // 获取英雄提供的增益
        if (role.getMainHero() != null) {
            for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : role.getMainHero().getAllAddition().entrySet()) {
                ret.put(entry.getKey(), ret.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
        }
        if (role.getDeputyHero() != null) {
            for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : role.getDeputyHero().getAllAddition().entrySet()) {
                ret.put(entry.getKey(), ret.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
        }
        return ret;
    }

    private long getHeroAddition(CommonEnum.BuffEffectType additionId) {
        long ret = 0;
        // 获取英雄提供的增益
        if (role.getMainHero() != null) {
            ret += role.getMainHero().getAllAddition().getOrDefault(additionId, 0L);
        }
        if (role.getDeputyHero() != null) {
            ret += role.getDeputyHero().getAllAddition().getOrDefault(additionId, 0L);
        }
        return ret;
    }
    
}

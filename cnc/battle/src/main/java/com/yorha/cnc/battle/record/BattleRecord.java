package com.yorha.cnc.battle.record;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.soldier.GuardTower;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.game.gen.prop.*;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 战报
 *
 * <AUTHOR>
 */
public class BattleRecord {
    private static final Logger LOGGER = LogManager.getLogger(BattleRecord.class);

    /**
     * 对应协议结构 BattleRecordOne
     */
    public static class RecordOne {
        private final long battleId;
        private final long relationId;
        private final RoleRecord oneRecord;
        private final RoleRecord otherRecord;
        private long startMs;
        private long endMs;
        private Struct.Point location;
        // <logId, timeMs>
        private final List<Pair<Long, Long>> battleLogIds;
        // 是否需要合并同个battlerole的record. 主堡换将产生的战报分割不用合并
        private boolean needMerge;
        private final CommonEnum.BattleType battleType;

        public RecordOne(long relationId, BattleRole one, BattleRole other, CommonEnum.BattleType battleType) {
            this.oneRecord = new RoleRecord(one);
            this.otherRecord = new RoleRecord(other);
            this.battleId = IdFactory.nextId("battle_id");
            this.battleLogIds = Lists.newArrayList();
            this.needMerge = true;
            this.battleType = battleType;
            this.relationId = relationId;
        }

        public CommonEnum.BattleType getBattleType() {
            return battleType;
        }

        public long getBattleId() {
            return battleId;
        }

        public RoleRecord getRecordByRoleId(long roleId) {
            return oneRecord.roleId == roleId ? oneRecord : otherRecord;
        }

        public RoleRecord getRecordByOtherRoleId(long myRoleId) {
            return oneRecord.roleId == myRoleId ? otherRecord : oneRecord;
        }

        public RoleRecord getOneRecord() {
            return oneRecord;
        }

        public RoleRecord getOtherRecord() {
            return otherRecord;
        }

        public long getStartMs() {
            return startMs;
        }

        public void setStartMs(long startMs) {
            this.startMs = startMs;
        }

        public long getEndMs() {
            return endMs;
        }

        public void setEndMs(long endMs) {
            this.endMs = endMs;
        }

        public void setLocation(int x, int y, long mapId, int mapType) {
            this.location = Struct.Point.newBuilder().setX(x).setY(y).setMapId(mapId).setMapType(mapType).build();
        }

        public void addBattleLogId(long battleLogId) {
            this.battleLogIds.add(Pair.of(battleLogId, SystemClock.now()));
        }

        public boolean isNeedMerge() {
            return needMerge;
        }

        public void setNeedMerge(boolean needMerge) {
            this.needMerge = needMerge;
        }

        public long getRelationId() {
            return relationId;
        }

        public BattleRecordOneProp convert2Prop(BattleRole role) {
            RoleRecord record = role.getRoleId() == oneRecord.roleId ? oneRecord : otherRecord;
            BattleRecordOneProp prop = new BattleRecordOneProp()
                    .setStartMillis(startMs)
                    .setEndMillis(endMs)
                    .setBattleId(battleId)
                    .setIsLeftRole(role.getRoleId() == oneRecord.roleId);
            battleLogIds.sort(Comparator.comparing(Pair::getSecond));
            prop.getBattleLogIdList().addAll(battleLogIds.stream().map(Pair::getFirst).collect(Collectors.toList()));
            if (location == null) {
                LOGGER.warn("BattleLog battleId={}, convert2Prop location is null!", battleId);
                location = Struct.Point.getDefaultInstance();
            }
            prop.getLocation().mergeFromSs(location);

            boolean allLoss = cannotShowSoldierReport(record, role);
            prop.setIsAllLoss(allLoss);
            oneRecord.fillRoleProp(prop.getLeftRole(), prop.getIsLeftRole(), !prop.getIsLeftRole() && allLoss);
            otherRecord.fillRoleProp(prop.getRightRole(), !prop.getIsLeftRole(), prop.getIsLeftRole() && allLoss);
            return prop;
        }

        private boolean cannotShowSoldierReport(RoleRecord record, BattleRole role) {
            EntityAttrOuterClass.EntityType entityType = role.getAdapter().getEntityType();
            return record.isAllLoss() && (entityType != EntityAttrOuterClass.EntityType.ET_MapBuilding && entityType != EntityAttrOuterClass.EntityType.ET_City);
        }

        public PlayerMail.BattleRecordDetail genDetailProto(BattleRole role) {
            RoleRecord myRecord = role.getRoleId() == oneRecord.roleId ? oneRecord : otherRecord;
            RoleRecord enemyRecord = role.getRoleId() == oneRecord.roleId ? otherRecord : oneRecord;

            PlayerMail.BattleRecordDetail.Builder builder = PlayerMail.BattleRecordDetail.newBuilder()
                    .addAllMyMembers(myRecord.getMembers().values().stream()
                            .map(it -> it.covert2RecordProp().getCopyCsBuilder().build())
                            .collect(Collectors.toList()));

            boolean allLoss = cannotShowSoldierReport(myRecord, role);
            if (!allLoss) {
                // 我全败，看不到对面的部队详情和增益，所以不填充
                builder.setMyAddition(myRecord.getAdditionSysProp().getCopyCsBuilder())
                        .setEnemyAddition(enemyRecord.getAdditionSysProp().getCopyCsBuilder())
                        .addAllEnemyMembers(enemyRecord.getMembers().values().stream()
                                .map(it -> it.covert2RecordProp().getCopyCsBuilder().build())
                                .collect(Collectors.toList()));
            }
            return builder.build();
        }

        public void merge(RecordOne toMerge) {
            setLocation(toMerge.location.getX(), toMerge.location.getY(), toMerge.location.getMapId(), toMerge.location.getMapType());
            setEndMs(toMerge.endMs);
            battleLogIds.addAll(toMerge.battleLogIds);
            // 合并one
            if (toMerge.oneRecord.roleId == oneRecord.roleId) {
                oneRecord.merge(toMerge.oneRecord);
            } else if (toMerge.oneRecord.roleId == otherRecord.roleId) {
                otherRecord.merge(toMerge.oneRecord);
            }
            // 合并other
            if (toMerge.otherRecord.roleId == oneRecord.roleId) {
                oneRecord.merge(toMerge.otherRecord);
            } else if (toMerge.otherRecord.roleId == otherRecord.roleId) {
                otherRecord.merge(toMerge.otherRecord);
            }
        }
    }

    /**
     * 基于relation的role记录，如role在这段关系中的士兵数据，英雄数据等
     */
    public static class RoleRecord {
        private final long roleId;
        private final CommonEnum.SceneObjType roleType;
        private final long roleTypeId;
        private String clanName;
        private Struct.PlayerCardHead cardHead;
        private Struct.Point location;
        private int lostPower;
        // 部队士兵总详情
        private final BattleRecordSoldierResult soldierRes;
        // BattleRole成员详情 key：memberRoleIdId(不是playerId)
        private final Map<Long, RoleMemberRecord> members;
        // 奖励
        private final ArrayList<ItemReward> rewards;
        // 防御塔
        private BattleRecordGuardTower guardTower;
        // 击杀 <soldierId, num>
        private final Map<Integer, Long> totalKill;
        // 资源详情
        private BattleRecordResource resource;
        // 增益
        private AdditionSysProp additionSysProp;
        private long buildingId;
        private final long leaderMemberRoleId;
        private final EntityAttrOuterClass.EntityType entityType;

        public RoleRecord(BattleRole role) {
            this.roleId = role.getRoleId();
            this.roleType = role.getType();
            this.roleTypeId = role.getAdapter().getRoleTypeId();
            this.soldierRes = new BattleRecordSoldierResult(0);
            this.members = Maps.newHashMap();
            this.rewards = Lists.newArrayList();
            this.totalKill = Maps.newHashMap();
            this.clanName = StringUtils.EMPTY;
            this.leaderMemberRoleId = role.getAdapter().getLeaderRoleId();
            this.entityType = role.getAdapter().getEntityType();
        }

        public BattleRecordSoldierResult getSoldierRes() {
            return soldierRes;
        }

        public long getRoleId() {
            return roleId;
        }

        public CommonEnum.SceneObjType getRoleType() {
            return roleType;
        }

        public long getRoleTypeId() {
            return roleTypeId;
        }

        public EntityAttrOuterClass.EntityType getEntityType() {
            return entityType;
        }

        public void addMember(RoleMemberRecord member) {
            members.putIfAbsent(member.getMemberRoleId(), member);
        }

        public void setTotalAlive(int totalAlive) {
            soldierRes.setTotalAlive(totalAlive);
        }

        public void setClanName(String clanName) {
            this.clanName = clanName;
        }

        public void setCardHead(Struct.PlayerCardHead cardHead) {
            this.cardHead = cardHead;
        }

        public void addTreatment(int treatment) {
            soldierRes.addTreatment(treatment);
        }

        public void addMbrTreatment(long memberRoleId, int soldierId, int treatment) {
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.getSoldierById(soldierId).addTreatment(treatment);
            }
        }

        public void addDead(int dead) {
            soldierRes.addDead(dead);
        }

        public void addMbrDead(long memberRoleId, int soldierId, int dead) {
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.getSoldierById(soldierId).addDead(dead);
            }
        }

        public void addSevereWound(int severeWound) {
            soldierRes.addSevereWound(severeWound);
        }

        public void addMbrSevereWound(long memberRoleId, int soldierId, int severeWound) {
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.getSoldierById(soldierId).addSevereWound(severeWound);
            }
        }

        public void addSlightWound(int slightWound) {
            soldierRes.addSlightWound(slightWound);
        }

        public void addMbrSlightWound(long memberRoleId, int soldierId, int slightWound) {
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.getSoldierById(soldierId).addSlightWound(slightWound);
            }
        }

        public void setLeftAlive(int leftAlive) {
            soldierRes.setLeftAlive(leftAlive);
        }

        public void setMbrLeftAlive(long memberRoleId, int soldierId, int leftAlive) {
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.getSoldierById(soldierId).setLeftAlive(leftAlive);
            }
        }

        public void addLostPower(int lostPower) {
            this.lostPower += lostPower;
        }

        public void addMemberLostPower(long memberRoleId, int lostPower) {
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.addLostPower(lostPower);
            }
        }

        public void addItemReward(ItemReward reward) {
            for (int i = 0; i < rewards.size(); i++) {
                ItemReward exits = rewards.get(i);
                if (exits.canPlus(reward)) {
                    rewards.set(i, exits.plus(reward));
                    return;
                }
            }
            rewards.add(reward);
        }

        public void initGuardTower(GuardTower tower) {
            this.guardTower = new BattleRecordGuardTower(tower.getId(), tower.getLv(), (int) tower.getMaxHp());
            this.guardTower.res.setTotalAlive(tower.aliveCount());
            getLeader().getSoldierById(tower.getId()).setTotalAlive(guardTower.res.totalAlive);
        }

        public void setGuardTowerLeftAlive(int num) {
            if (guardTower != null) {
                guardTower.res.setLeftAlive(num);
                getLeader().getSoldierById(guardTower.res.soldierId).setLeftAlive(num);
            }
        }

        public void addGuardTowerDead(int num) {
            if (guardTower != null) {
                guardTower.res.addDead(num);
                getLeader().getSoldierById(guardTower.res.soldierId).addDead(num);
            }
        }

        public Map<Long, RoleMemberRecord> getMembers() {
            return members;
        }

        public void fillPlunder(long roleId, Map<CommonEnum.CurrencyType, Long> resources, CommonEnum.PlunderResultEnum result) {
            RoleMemberRecord roleMemberRecord = members.get(roleId);
            if (roleMemberRecord != null) {
                roleMemberRecord.resource.setResources(resources, result);
            }
            // 集结行军，只展示车头的掠夺信息
            if (roleId == leaderMemberRoleId) {
                if (resource == null) {
                    resource = new BattleRecordResource();
                }
                resource.setResources(resources, result);
            }
        }

        public boolean isAllLoss() {
            return soldierRes.isAllLoss();
        }

        public RoleMemberRecord getLeader() {
            return members.getOrDefault(leaderMemberRoleId, new RoleMemberRecord());
        }

        public CommonBattle.BattleLogGuardTower getGuardTowerPb() {
            CommonBattle.BattleLogGuardTower.Builder builder = CommonBattle.BattleLogGuardTower.newBuilder();
            if (guardTower != null) {
                builder.setLv(guardTower.lv)
                        .setHealth(guardTower.res.totalAlive)
                        .setHpMax(guardTower.hpMax);
            }
            return builder.build();
        }

        public void addKill(long memberRoleId, int soldierId, long num) {
            totalKill.put(soldierId, totalKill.getOrDefault(soldierId, 0L) + num);
            RoleMemberRecord memberRec = members.get(memberRoleId);
            if (memberRec != null) {
                memberRec.addKill(soldierId, num);
            }
        }

        public long getTotalKill() {
            return totalKill.values().stream().mapToLong(it -> it).sum();
        }

        /**
         * 获取击杀数
         *
         * @return <memberRoleId, <soldierId, killNum>>
         */
        public Map<Long, Map<Integer, Long>> getMemberKill() {
            Map<Long, Map<Integer, Long>> res = Maps.newHashMap();
            for (RoleMemberRecord memberRole : members.values()) {
                res.put(memberRole.memberRoleId, memberRole.getKill());
            }
            return res;
        }

        public Map<Integer, Long> getMemberKill(long memberRoleId) {
            RoleMemberRecord memberRecord = members.get(memberRoleId);
            if (memberRecord == null) {
                return Maps.newHashMap();
            }
            return memberRecord.getKill();
        }

        public void setAdditionSysProp(AdditionSysProp additionSysProp) {
            this.additionSysProp = additionSysProp;
        }

        public void setLocation(int x, int y, long mapId, CommonEnum.MapType mapType) {
            this.location = Struct.Point.newBuilder().setX(x).setY(y).setMapId(mapId).setMapType(mapType.getNumber()).build();
        }

        public void setBuildingId(long buildingId) {
            this.buildingId = buildingId;
        }

        public String getClanName() {
            return clanName;
        }

        public AdditionSysProp getAdditionSysProp() {
            return additionSysProp;
        }

        public Struct.Point getLocation() {
            return location;
        }

        public Struct.PlayerCardHead getCardHead() {
            return cardHead;
        }

        public long getLeaderMemberRoleId() {
            return leaderMemberRoleId;
        }

        /**
         * 获取战损
         *
         * @return <playerId, loss>
         */
        public Map<Long, Long> getMemberPowerLossByPlayerId() {
            Map<Long, Long> res = Maps.newHashMap();
            for (RoleMemberRecord memberRole : members.values()) {
                if (memberRole.playerId <= 0) {
                    continue;
                }
                res.put(memberRole.playerId, res.getOrDefault(memberRole.playerId, 0L) + memberRole.getLostPower());
            }
            return res;
        }

        public void fillRoleProp(BattleRecordRoleProp roleProp, boolean isSelf, boolean isAllLoss) {
            RoleMemberRecord leader = getLeader();
            roleProp
                    .setClanName(clanName)
                    .setLostPower(lostPower)
                    .setRoleId(leaderMemberRoleId)
                    .setRoleType(roleType)
                    .setBuildingId(buildingId)
                    .setEntityType(entityType.getNumber());
            roleProp.getLocation().mergeFromSs(location);
            roleProp.getMainHero().mergeFromSs(leader.mainHero.convert2Proto());
            roleProp.getDeputyHero().mergeFromSs(leader.deputyHero.convert2Proto());
            roleProp.getCardHead().mergeFromSs(cardHead);
            for (ItemReward reward : rewards) {
                roleProp.getReward().addRewards(reward.toDisplay());
            }
            if (guardTower != null) {
                roleProp.getGuardTowerReport().mergeFromSs(guardTower.res.covert2Proto());
            }
            if (resource != null && isSelf) {
                roleProp.getPlunder().mergeFromSs(resource.convert2Proto());
            }
            // 全败看不到对面兵力
            if (!isAllLoss) {
                roleProp.getSoldierReport().mergeFromSs(soldierRes.covert2Proto());
            }
        }

        public void merge(RoleRecord toMerge) {
            CommonEnum.MapType mapType = CommonEnum.MapType.forNumber(toMerge.location.getMapType());
            setLocation(toMerge.location.getX(), toMerge.location.getY(), toMerge.location.getMapId(), mapType == null ? CommonEnum.MapType.MAT_NONE : mapType);
            addLostPower(toMerge.lostPower);
            soldierRes.setLeftAlive(toMerge.soldierRes.leftAlive);
            soldierRes.merge(toMerge.soldierRes);
            // 成员
            for (Map.Entry<Long, RoleMemberRecord> entry : toMerge.members.entrySet()) {
                RoleMemberRecord mergeToOneMemberRecord = entry.getValue();
                if (members.containsKey(entry.getKey())) {
                    members.get(entry.getKey()).merge(mergeToOneMemberRecord);
                } else {
                    addMember(mergeToOneMemberRecord);
                }
            }
            for (ItemReward reward : toMerge.rewards) {
                addItemReward(reward);
            }
            if (resource != null) {
                resource.merge(toMerge.resource);
            }
        }

        public int getLostPower() {
            return lostPower;
        }

        @Override
        public String toString() {
            return "RoleRecord{" +
                    "roleId=" + roleId +
                    ", roleType=" + roleType +
                    ", roleTypeId=" + roleTypeId +
                    ", lostPower=" + lostPower +
                    ", soldierRes=" + soldierRes +
                    ", members=" + members +
                    ", rewards=" + rewards +
                    ", guardTower=" + guardTower +
                    ", totalKill=" + totalKill +
                    ", resource=" + resource +
                    ", leaderMemberRoleId=" + leaderMemberRoleId +
                    '}';
        }
    }

    /**
     * role中的成员详情
     */
    public static class RoleMemberRecord {
        private long memberRoleId;
        private long playerId;
        private String clanName;
        private final BattleRecordHero mainHero;
        private final BattleRecordHero deputyHero;
        // 士兵详情
        private final Map<Integer, BattleRecordSoldierResult> soldier;
        // 资源详情
        private BattleRecordResource resource;
        // 是否是中途加入
        private boolean isCutIn;
        // 击杀详情
        private final Map<Integer, Long> kill;
        private Struct.PlayerCardHead cardHead;
        private long lostPower;

        public RoleMemberRecord() {
            this.clanName = StringUtils.EMPTY;
            this.mainHero = new BattleRecordHero();
            this.deputyHero = new BattleRecordHero();
            this.soldier = Maps.newHashMap();
            this.resource = new BattleRecordResource();
            this.isCutIn = false;
            this.kill = Maps.newHashMap();
            this.cardHead = Struct.PlayerCardHead.getDefaultInstance();
        }

        public long getMemberRoleId() {
            return memberRoleId;
        }

        public RoleMemberRecord setMemberRoleId(long memberEntityId) {
            this.memberRoleId = memberEntityId;
            return this;
        }

        public long getPlayerId() {
            return playerId;
        }

        public RoleMemberRecord setPlayerId(long playerId) {
            this.playerId = playerId;
            return this;
        }

        public RoleMemberRecord setClanName(String clanName) {
            this.clanName = clanName;
            return this;
        }

        public RoleMemberRecord setIsCutIn(boolean isCutIn) {
            this.isCutIn = isCutIn;
            return this;
        }

        public BattleRecordHero getMainHero() {
            return mainHero;
        }

        public BattleRecordHero getDeputyHero() {
            return deputyHero;
        }

        public BattleRecordSoldierResult getSoldierById(int soldierId) {
            return soldier.computeIfAbsent(soldierId, v -> new BattleRecordSoldierResult(soldierId));
        }

        public RoleMemberRecord setResource(BattleRecordResource resource) {
            this.resource = resource;
            return this;
        }

        public void addKill(int soldierId, long num) {
            kill.put(soldierId, kill.getOrDefault(soldierId, 0L) + num);
        }

        public Map<Integer, Long> getKill() {
            return kill;
        }

        public RoleMemberRecord setCardHead(Struct.PlayerCardHead cardHead) {
            this.cardHead = cardHead;
            return this;
        }

        public long getLostPower() {
            return lostPower;
        }

        public void addLostPower(long lostPower) {
            this.lostPower += lostPower;
        }

        public RoleMemberRecord buildRoleMemberRecord(HeroProp mainHero, HeroProp deputyHero, Map<Long, Map<Integer, Integer>> aliveCountMap) {
            // hero
            getMainHero().init(mainHero);
            getDeputyHero().init(deputyHero);
            // soldier
            for (Map.Entry<Integer, Integer> entry : aliveCountMap.getOrDefault(getMemberRoleId(), Maps.newHashMap()).entrySet()) {
                getSoldierById(entry.getKey()).setTotalAlive(entry.getValue());
            }
            return this;
        }

        public List<Struct.Soldier> getSoldierData() {
            return soldier.values().stream().map(BattleRecordSoldierResult::covert2SoldierProto).collect(Collectors.toList());
        }

        public BattleRecordRoleMemberProp covert2RecordProp() {
            BattleRecordRoleMemberProp memberProp = new BattleRecordRoleMemberProp()
                    .setRoleId(memberRoleId)
                    .setClanName(clanName);
            memberProp.getCardHead().mergeFromSs(cardHead);
            memberProp.getMainHero().mergeFromSs(mainHero.convert2Proto());
            memberProp.getDeputyHero().mergeFromSs(deputyHero.convert2Proto());
            memberProp.getPlunder().mergeFromSs(resource.convert2Proto());
            soldier.values().stream().map(BattleRecordSoldierResult::convert2DetailProp).forEach(it -> memberProp.getSoldierDetail().add(it));
            return memberProp;
        }

        public CommonBattle.BattleLogRoleMember convert2LogPb() {
            CommonBattle.BattleLogRoleMember.Builder builder = CommonBattle.BattleLogRoleMember.newBuilder()
                    .setRoleId(memberRoleId)
                    .setName(cardHead.getName())
                    .setClanName(clanName)
                    .mergeMainHero(mainHero.convert2LogPb())
                    .mergeDeputyHero(deputyHero.convert2LogPb())
                    // 只显示士兵的兵力，不要防御塔的
                    .setSoldier(soldier.values().stream()
                            .filter(it -> ResHolder.getResService(SoldierResService.class).findSoldierTemplate(it.soldierId).getSoldierType() != CommonEnum.SoldierType.ST_GuardTower_VALUE)
                            .mapToInt(it -> it.totalAlive)
                            .sum())
                    .setIsJoin(isCutIn);
            return builder.build();
        }

        public void merge(RoleMemberRecord toMerge) {
            // 士兵
            for (Map.Entry<Integer, BattleRecordSoldierResult> soldierEntry : soldier.entrySet()) {
                BattleRecordSoldierResult res = toMerge.soldier.get(soldierEntry.getKey());
                if (res != null) {
                    soldierEntry.getValue().setLeftAlive(res.leftAlive);
                    soldierEntry.getValue().merge(res);
                }
            }
            // 资源
            if (resource != null) {
                resource.merge(toMerge.resource);
            }
            // 战损
            lostPower += toMerge.lostPower;
        }

        public Map<Integer, BattleRecordSoldierResult> getSoldier() {
            return soldier;
        }

        @Override
        public String toString() {
            return "RoleMemberRecord{" +
                    "memberRoleId=" + memberRoleId +
                    ", playerId=" + playerId +
                    ", clanName='" + clanName + '\'' +
                    ", mainHero=" + mainHero +
                    ", deputyHero=" + deputyHero +
                    ", soldier=" + soldier +
                    ", resource=" + resource +
                    ", isCutIn=" + isCutIn +
                    ", kill=" + kill +
                    '}';
        }
    }

    /**
     * 英雄
     * <p>
     * 战斗开始时初始化，不会变
     */
    public static class BattleRecordHero {
        private int heroId;
        private int lv;
        private int star;
        private  SimpleSkillListProp skills;
        private int exp;

        public BattleRecordHero(){
        }

        public void init(HeroProp hero) {
            heroId = hero.getHeroId();
            lv = hero.getLevel();
            star = hero.getStar();
            skills = hero.getSkills();
        }


        public void setExp(int exp) {
            this.exp = exp;
        }

        public int getHeroId() {
            return heroId;
        }

        public int getLv() {
            return lv;
        }

        public int getStar() {
            return star;
        }

        public SimpleSkillListProp getSkills() {
            return skills;
        }

        public StructBattle.BattleRecordHeroSummary convert2Proto() {
            StructBattle.BattleRecordHeroSummary.Builder builder = StructBattle.BattleRecordHeroSummary.newBuilder()
                    .setHeroId(heroId)
                    .setLevel(lv)
                    .setAddExp(exp)
                    .setStar(star);
            return builder.build();
        }

        public CommonBattle.HeroSummary convert2LogPb() {
            return CommonBattle.HeroSummary.newBuilder()
                    .setHeroId(heroId)
                    .setLevel(lv)
                    .setStar(star)
                    .setExp(exp)
                  //  .addAllSkills(skills)
                    .build();
        }

        @Override
        public String toString() {
            return "BattleRecordHero{" +
                    "heroId=" + heroId +
                    ", lv=" + lv +
                    ", star=" + star +
                    ", skills=" + skills +
                    ", exp=" + exp +
                    '}';
        }
    }


    /**
     * 士兵战斗结果
     */
    public static class BattleRecordSoldierResult {
        // 士兵id
        private final int soldierId;
        // 总数
        private int totalAlive;
        // 被治疗
        private int treatment;
        // 死亡
        private int dead;
        // 重伤
        private int severeWound;
        // 累计轻伤
        private int sumSlightWound;
        // 剩余
        private int leftAlive;

        public BattleRecordSoldierResult(int soldierId) {
            this.soldierId = soldierId;
        }

        public int getTreatment() {
            return treatment;
        }

        public void setTotalAlive(int total) {
            this.totalAlive = total;
        }

        public void addTreatment(int treatment) {
            this.treatment += treatment;
        }

        public void addDead(int dead) {
            this.dead += dead;
        }

        public void addSevereWound(int severeWound) {
            this.severeWound += severeWound;
        }

        public void addSlightWound(int slightWound) {
            this.sumSlightWound += slightWound;
        }

        public void setLeftAlive(int leftAlive) {
            this.leftAlive = leftAlive;
        }

        public boolean isAllLoss() {
            return leftAlive + sumSlightWound <= 0;
        }

        public int getSumSlightWound() {
            return sumSlightWound;
        }

        public int getTotalAlive() {
            return totalAlive;
        }

        public int getDead() {
            return dead;
        }

        public int getSevereWound() {
            return severeWound;
        }

        public int getLeftAlive() {
            return leftAlive;
        }

        public int getSoldierId() {
            return soldierId;
        }

        public void merge(BattleRecordSoldierResult other) {
            if (other == null) {
                return;
            }
            this.treatment += other.treatment;
            this.dead += other.dead;
            this.severeWound += other.severeWound;
            this.sumSlightWound += other.sumSlightWound;
        }

        public BattleSoldierDetailProp convert2DetailProp() {
            BattleSoldierDetailProp prop = new BattleSoldierDetailProp()
                    .setSoldierId(soldierId);
            prop.getSoldierReport().mergeFromSs(covert2Proto());
            return prop;
        }

        public StructBattle.BattleRecordSoldierReport covert2Proto() {
            return StructBattle.BattleRecordSoldierReport.newBuilder()
                    .setTotal(totalAlive)
                    .setTreatment(treatment)
                    .setDead(dead)
                    .setSevereWound(severeWound)
                    .setSlightWound(sumSlightWound)
                    .setLeftAlive(leftAlive)
                    .build();
        }

        public Struct.Soldier covert2SoldierProto() {
            return Struct.Soldier.newBuilder()
                    .setSoldierId(soldierId)
                    .setNum(totalAlive)
                    .setSlightWoundNum(getSumSlightWound())
                    .setSevereWoundNum(severeWound)
                    .setDeadNum(dead)
                    .build();
        }

        @Override
        public String toString() {
            return "BattleRecordSoldierResult{" +
                    "soldierId=" + soldierId +
                    ", totalAlive=" + totalAlive +
                    ", treatment=" + treatment +
                    ", dead=" + dead +
                    ", severeWound=" + severeWound +
                    ", sumSlightWound=" + sumSlightWound +
                    ", leftAlive=" + leftAlive +
                    '}';
        }
    }

    /**
     * 部队资源
     */
    public static class BattleRecordResource {
        // 资源
        private final Map<CommonEnum.CurrencyType, Long> resources;
        // 掠夺结果
        private CommonEnum.PlunderResultEnum result;

        public BattleRecordResource() {
            resources = Maps.newHashMap();
            result = CommonEnum.PlunderResultEnum.PRE_PLUNDER_NONE;
        }

        public void setResources(Map<CommonEnum.CurrencyType, Long> resources, CommonEnum.PlunderResultEnum result) {
            setResult(result);
            resources.entrySet().stream()
                    .filter(it -> it.getValue() != 0)
                    .forEach(it -> this.resources.put(it.getKey(), it.getValue()));
        }

        public void setResult(CommonEnum.PlunderResultEnum result) {
            this.result = result;
        }

        public void merge(BattleRecordResource other) {
            if (other == null) {
                return;
            }
            for (Map.Entry<CommonEnum.CurrencyType, Long> entry : other.resources.entrySet()) {
                resources.put(entry.getKey(), resources.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
        }

        public StructBattle.Plunder convert2Proto() {
            StructBattle.Plunder.Builder builder = StructBattle.Plunder.newBuilder()
                    .setResult(result);
            for (Map.Entry<CommonEnum.CurrencyType, Long> entry : resources.entrySet()) {
                builder.getResourcesBuilder().putDatas(
                        entry.getKey().getNumber(),
                        Struct.Currency.newBuilder().setType(entry.getKey().getNumber()).setCount(entry.getValue()).build()
                );
            }
            return builder.build();
        }

        @Override
        public String toString() {
            return "BattleRecordResource{" +
                    "resources=" + resources +
                    ", result=" + result +
                    '}';
        }
    }

    public static class BattleRecordGuardTower {
        private final int lv;
        private final BattleRecordSoldierResult res;
        private final int hpMax;

        public BattleRecordGuardTower(int soldierId, int lv, int hpMax) {
            this.lv = lv;
            this.res = new BattleRecordSoldierResult(soldierId);
            this.hpMax = hpMax;
        }
    }

    public static boolean isPlayerCity(CommonEnum.SceneObjType type) {
        return type == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF;
    }

    public static boolean isMapBuilding(CommonEnum.SceneObjType type) {
        return type == CommonEnum.SceneObjType.SOT_STRONG_POINT_ARMY || type == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY;
    }

    public static boolean isMonster(CommonEnum.SceneObjType type) {
        return type == CommonEnum.SceneObjType.SOT_MONSTER;
    }
}

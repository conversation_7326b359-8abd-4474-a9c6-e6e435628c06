package com.yorha.cnc.battle.common;

/**
 * 伤害/治疗/护盾值 值修正
 *
 * <AUTHOR>
 */
public class Amend {

    private double ratio = 1;

    public Amend multiRatio(double anotherRatio) {
        this.ratio = this.ratio * anotherRatio;
        return this;
    }

    public double getRatio() {
        return ratio;
    }

    /**
     * @return 新的Amend对象
     */
    public Amend plus(Amend otherAmend) {
        return new Amend()
                .multiRatio(this.ratio)
                .multiRatio(otherAmend.ratio);
    }

    @Override
    public String toString() {
        return "Amend{" +
                "ratio=" + ratio +
                '}';
    }
}

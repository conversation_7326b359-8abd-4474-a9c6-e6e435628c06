package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.common.resource.ResHolder;
import res.template.BattleBuffTemplate;

import java.util.List;

import static com.yorha.common.constant.BattleConstants.*;

/**
 * 获得buff
 *
 * <AUTHOR>
 */
public class Get<PERSON>uffChecker extends StatusChecker {

    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        int type = Integer.parseInt(params[0]);
        int v = Integer.parseInt(params[1]);
        return isTargetBuff(role.getSkillBlackboard().getCurRoundValidBuffs(), type, v);
    }

    public static boolean isTargetBuff(List<Integer> buffs, int type, int value) {
        switch (type) {
            case BUFF_TAG:
                return checkByTag(buffs, value);
            case BUFF_GROUP_ID:
                return checkByGroupId(buffs, value);
            case BUFF_ID:
                return checkById(buffs, value);
            case BUFF_ALWAYS:{
                return true;
            }
            default:
                return false;
        }
    }

    private static boolean checkById(List<Integer> buffs, int value) {
        return buffs.contains(value);
    }

    private static boolean checkByTag(List<Integer> buffs, int value) {
        for (Integer buffId : buffs) {
            BattleBuffTemplate template = ResHolder.getInstance().findValueFromMap(BattleBuffTemplate.class, buffId);
            if (template == null) {
                continue;
            }
            if (template.getTagList().contains(value)) {
                return true;
            }
        }
        return false;
    }

    private static boolean checkByGroupId(List<Integer> buffs, int value) {
        for (Integer buffId : buffs) {
            BattleBuffTemplate template = ResHolder.getInstance().findValueFromMap(BattleBuffTemplate.class, buffId);
            if (template == null) {
                continue;
            }
            if (template.getEffectGroupId() == value) {
                return true;
            }
        }
        return false;
    }
}

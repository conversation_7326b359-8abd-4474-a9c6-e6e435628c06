package com.yorha.cnc.battle.context;

import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.game.gen.prop.BattleRecordAllProp;

import java.util.List;
import java.util.Set;

/**
 * 战斗邮件快照，给掠夺用
 *
 * <AUTHOR>
 * @date 2023/4/3
 */
public class BattleRecordMailSnapshot {
    public final boolean isAnyEnemyAlive;
    public final boolean isMeAlive;
    public final List<BattleRecord.RecordOne> recordOnes;
    public final BattleRecordAllProp recordAllProp;
    public final Set<Long> pendingPlunderRelationIds;
    public final Set<Long> needSendRecordPlayerIds;

    public BattleRecordMailSnapshot(boolean isAnyEnemyAlive,
                                    boolean isMeAlive,
                                    BattleRecordAllProp recordAllProp,
                                    List<BattleRecord.RecordOne> recordOnes,
                                    Set<Long> pendingPlunderRelationIds,
                                    Set<Long> needSendRecordPlayerIds) {
        this.isAnyEnemyAlive = isAnyEnemyAlive;
        this.isMeAlive = isMeAlive;
        this.recordOnes = recordOnes;
        this.recordAllProp = recordAllProp;
        this.pendingPlunderRelationIds = pendingPlunderRelationIds;
        this.needSendRecordPlayerIds = needSendRecordPlayerIds;
    }
}

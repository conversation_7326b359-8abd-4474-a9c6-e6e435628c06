package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.common.Amend;
import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.context.TreatmentContext;
import com.yorha.cnc.battle.core.BattleFormula;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.common.utils.MathUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 百分比治疗
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
public class PercentageTreatmentValue extends AbstractSkillEffectValue {
    public PercentageTreatmentValue() {
        super(CommonEnum.SkillEffectType.SET_PERCENTAGE_TREATMENT);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        Amend baseAmend = getBaseAmend(actionCtx.getTreatmentAmend(target.getRoleId()), template.getValue1() / MathUtils.TEN_THOUSAND);
        // 目标禁疗
        boolean isJinLiao = target.getStateHandler().isinState(BattleConstants.BattleRoleState.NO_TREATMENT);
        int treatmentValue = 0;
        if (!isJinLiao) {
            BattleRoleSnapshot attackerSnapshot = getTreatmentAttackSnapshot(attacker, target, effectContext, template.getValue2());
            TreatmentContext treatmentCtx = BattleFormula.stdApplyPercentageTreatment(attackerSnapshot, target.getSnapShot("DefPercentageTreatment"), actionCtx, effectContext, baseAmend);
            treatmentValue = treatmentCtx.getTotalTreat();
            if (treatmentValue > 0) {
                target.getContext().addTreatmentCtx(treatmentCtx);
                target.getGround().addTreatmentRole(target, "PercentageTreatment " + effectContext.isDot());
            }
        }

        if (treatmentValue > 0 || isJinLiao) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(getType());
            builder.setValue(treatmentValue);
            builder.setEffectId(template.getId());
            ret.add(builder.build());
            // 受到治疗触发器
            SkillSystem.trigger(target, CommonEnum.TriggerType.TT_BE_TREATED);
        }
        return ret;
    }
}

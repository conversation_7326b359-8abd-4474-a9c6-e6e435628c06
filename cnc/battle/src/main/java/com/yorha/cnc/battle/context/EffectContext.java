package com.yorha.cnc.battle.context;

import com.yorha.cnc.battle.common.SevereDeadRatio;
import com.yorha.cnc.battle.context.dot.DotContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class EffectContext {

    private final BattleRole castRole;
    private final int effectId;
    private final int heroId;
    private final int id;
    private final CommonEnum.BattleLogSkillType type;
    private int attachBuffId;
    private final long leaderRoleId;
    private DotContext dotContext;

    public EffectContext(BattleRole castRole, int effectId, int heroId, int id, CommonEnum.BattleLogSkillType type, int attachBuffId, long leaderRoleId, DotContext dotContext) {
        this.castRole = castRole;
        this.effectId = effectId;
        this.heroId = heroId;
        this.id = id;
        this.type = type;
        this.attachBuffId = attachBuffId;
        this.leaderRoleId = leaderRoleId;
        this.dotContext = dotContext;
    }

    public long getLeaderRoleId() {
        return leaderRoleId;
    }

    public BattleRole getRole() {
        return castRole;
    }

    public long getRoleId() {
        return this.castRole.getRoleId();
    }

    public int getHeroId() {
        return heroId;
    }

    public int getId() {
        return id;
    }

    public int getEffectId() {
        return effectId;
    }

    public boolean isSkill() {
        return type == CommonEnum.BattleLogSkillType.BLST_SKILL;
    }

    public CommonEnum.BattleLogSkillType getType() {
        return type;
    }

    public boolean hasAttachBuff() {
        return attachBuffId > 0;
    }

    public CommonBattle.SkillExecutor convert2Pb() {
        CommonBattle.SkillExecutor.Builder builder = CommonBattle.SkillExecutor.newBuilder()
                .setRoleId(leaderRoleId)
                .setHeroId(getHeroId())
                .setType(type)
                .setId(id)
                .setAttachBuffId(attachBuffId);
        return builder.build();
    }

    public BattleRoleSnapshot getDotSnapshot() {
        return dotContext.getSnapshot();
    }

    public Map<CommonEnum.SceneObjType, SevereDeadRatio> getDotDeadRatio(){
        return dotContext.getDotDeadRatioMap();
    }

    public boolean isDot(){
        return dotContext != null;
    }


    public BattleRole getCastRole() {
        return castRole;
    }

    public int getAttachBuffId() {
        return attachBuffId;
    }

    public DotContext getDotContext() {
        return dotContext;
    }


    @Override
    public String toString() {
        return "SkillExecutorInfo{" +
                "castRole=" + castRole +
                ", effectId=" + effectId +
                ", heroId=" + heroId +
                ", id=" + id +
                ", type=" + type +
                ", attachBuffId=" + attachBuffId +
                '}';
    }

    public String toLogString() {
        String str = StringUtils.format("类型={} id={} 效果={} 英雄={}", type, id, effectId, heroId);
        if (attachBuffId > 0) {
            str += " 关联BuffId=" + attachBuffId;
        }
        return str;
    }


    public static class EffectContextBuilder {
        private static final Logger LOGGER = LogManager.getLogger(EffectContext.class);

        private BattleRole castRole;
        private int effectId;
        private int heroId;
        private int id;
        private CommonEnum.BattleLogSkillType type;
        private int attachBuffId;
        private long leaderRoleId;
        private DotContext dotContext;
        private int field = 7;


        public EffectContextBuilder setCastRole(BattleRole castRole) {
            if (this.castRole != null){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder castRole={}", this.castRole);
            }
            field--;
            this.castRole = castRole;
            return this;
        }

        public EffectContextBuilder setEffectId(int effectId) {
            if (this.effectId != 0){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder effectId={}", this.effectId);
            }
            field--;
            this.effectId = effectId;
            return this;
        }

        public EffectContextBuilder setHeroId(int heroId) {
            if (this.heroId != 0){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder heroId={}", this.heroId);
            }
            field--;
            this.heroId = heroId;
            return this;
        }

        public EffectContextBuilder setSkillId(int id) {
            if (this.id != 0){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder id={}", this.id);
            }
            field--;
            this.id = id;
            return this;
        }

        public EffectContextBuilder setType(CommonEnum.BattleLogSkillType type) {
            if (this.type != null){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder type={}", this.type);
            }
            field--;
            this.type = type;
            return this;
        }

        public EffectContextBuilder setAttachBuffId(int attachBuffId) {
            if (this.attachBuffId != 0){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder attachBuffId={}", this.attachBuffId);
            }
            field--;
            this.attachBuffId = attachBuffId;
            return this;
        }

        public EffectContextBuilder setLeaderRoleId(long leaderRoleId) {
            if (this.leaderRoleId != 0){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder leaderRoleId={}", this.leaderRoleId);
            }
            field--;
            this.leaderRoleId = leaderRoleId;
            return this;
        }

        public EffectContextBuilder setDotContext(DotContext dotContext) {
            if (this.dotContext != null){
                LOGGER.error("BattleErrLog repeat set field, EffectContextBuilder dotContext={}", this.dotContext);
            }
            this.dotContext = dotContext;
            return this;
        }

        public EffectContext build(boolean isCheckMonster){
            if (field > 0){
                LOGGER.error("BattleErrLog EffectContext incomplete, this={}", this.toString());
            }
            // castRole用主人的
            BattleRole castRole;
            if (this.castRole != null && isCheckMonster && this.castRole.getMasterRole() != null) {
                castRole = this.castRole.getMasterRole();
            } else {
                castRole = this.castRole;
            }
            // 英雄id用主人的
            int heroId;
            if (this.castRole != null && isCheckMonster && (this.castRole.getMasterRole() != null || this.heroId == 0) && this.castRole.getMainHero() != null) {
                heroId = this.castRole.getMainHero().getId();
            } else {
                heroId = this.heroId;
            }
            long leaderRoleId;
            if (this.castRole != null && this.castRole.getMasterRole() != null && isCheckMonster){
                leaderRoleId = this.castRole.getMasterRole().getAdapter().getLeaderRoleId();
            } else {
                leaderRoleId = this.leaderRoleId;
            }
            return new EffectContext(castRole, effectId, heroId, id, type, attachBuffId, leaderRoleId, dotContext);
        }



        @Override
        public String toString() {
            return "EffectContextBuilder{" +
                    "castRole=" + castRole +
                    ", effectId=" + effectId +
                    ", heroId=" + heroId +
                    ", id=" + id +
                    ", type=" + type +
                    ", attachBuffId=" + attachBuffId +
                    ", leaderRoleId=" + leaderRoleId +
                    ", dotContext=" + dotContext +
                    ", field=" + field +
                    '}';
        }
    }

    public static EffectContextBuilder newBuilder(){
        return new EffectContextBuilder();
    }

    public static EffectContext copyNewInstance(EffectContext context){
        return newBuilder().setCastRole(context.getRole())
                .setSkillId(context.getId())
                .setType(context.getType())
                .setHeroId(context.getHeroId())
                .setEffectId(context.getEffectId())
                .setLeaderRoleId(context.getLeaderRoleId())
                .setAttachBuffId(context.getAttachBuffId())
                .setDotContext(context.getDotContext())
                .build(false);
    }


}

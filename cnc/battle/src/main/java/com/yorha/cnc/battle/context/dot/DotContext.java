package com.yorha.cnc.battle.context.dot;

import com.yorha.cnc.battle.common.SevereDeadRatio;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.proto.CommonEnum;

import java.util.Map;

public class DotContext {

    /**
     * dot相关
     */
    private final BattleRoleSnapshot snapshot;

    /**
     * dot相关:攻守双方数据结合算出的防守方重伤死亡率
     */
    private final Map<CommonEnum.SceneObjType, SevereDeadRatio> dotDeadRatioMap;

    public BattleRoleSnapshot getSnapshot() {
        return snapshot;
    }

    public Map<CommonEnum.SceneObjType, SevereDeadRatio> getDotDeadRatioMap() {
        return dotDeadRatioMap;
    }

    public DotContext(BattleRoleSnapshot snapshot, Map<CommonEnum.SceneObjType, SevereDeadRatio> dotDeadRatioMap) {
        this.snapshot = snapshot;
        this.dotDeadRatioMap = dotDeadRatioMap;
    }
}

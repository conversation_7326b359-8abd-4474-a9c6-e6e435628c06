package com.yorha.cnc.battle.core;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.buf.Buff;
import com.yorha.cnc.battle.buf.ShieldBuff;
import com.yorha.cnc.battle.common.ActionType;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.context.*;
import com.yorha.cnc.battle.event.BattleRoundEvent;
import com.yorha.cnc.battle.handler.*;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.skill.SkillBlackboard;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.cnc.battle.soldier.*;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.*;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.SsPlayerMisc;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstBattleTemplate;
import res.template.SoldierTypeTemplate;
import res.template.TroopTemplate;

import javax.annotation.Nullable;
import java.util.*;
import java.util.function.Consumer;

/**
 * BattleRole的基础实现类
 *
 * <AUTHOR>
 */
public class BattleRole implements BattleRoleActionOnly, BattleRoleSettleOnly {
    private static final Logger LOGGER = LogManager.getLogger(BattleRole.class);

    protected final long roleId;
    protected SceneObjType type;
    protected final IBattleRoleAdapter adapter;
    protected BattleRoleContext context;
    /**
     * 我当前的战斗关系，可能包含已经stop的relation
     */
    protected final Map<Long, BattleRelation> myBattleRelation = new HashMap<>();
    protected final Map<String, BattleRelation> myToAddRelation = new HashMap<>();

    /**
     * 所有士兵，soldierType->SoldierGroup
     */
    protected final Map<Integer, SoldierGroup> groupMap = Maps.newHashMap();
    /**
     * 防御塔
     */
    protected GuardTower guardTower;
    /**
     * 主将
     */
    protected BattleHero mainHero;
    /**
     * 副将
     */
    protected BattleHero deputyHero;
    /**
     * 战场
     */
    private final BattleGround ground;
    private BattleRoleSnapshot snapshot;
    /**
     * 战斗buff
     */
    private final BuffHandler buffHandler;
    /**
     * 技能
     */
    private final SkillHandler skillHandler;
    /**
     * 加成
     */
    private final AdditionHandler additionHandler;
    /**
     * 合围
     */
    private final SiegeHandler siegeHandler;
    /**
     * 状态
     */
    private final StateHandler stateHandler;
    /**
     * 并行结算
     */
    private final ParallelSettleHandler parallelSettleHandler;
    /**
     * 结算
     */
    private final SettleHandler settleHandler;
    /**
     * 事件
     */
    private final EventHandler eventHandler;
    /**
     * 召唤物
     */
    private SummoningHandler summoningHandler;

    private boolean isInTick;

    private final BattleRole masterRole;

    /**
     * BattleRole是否属于野怪
     * NOTE(furson): 即时读表可能会造成部队属性变化，字段仅能初始化一次
     */
    private boolean belongsToNpc;

    public BattleRole(BattleGround ground, IBattleRoleAdapter adapter, SceneObjType type, long entityId, BattleRole masterRole) {
        this.ground = ground;
        this.roleId = entityId;
        this.type = type;
        this.adapter = adapter;
        this.masterRole = masterRole;
        this.context = new BattleRoleContext(this);

        this.buffHandler = new BuffHandler(this);
        this.skillHandler = new SkillHandler(this);
        this.additionHandler = new AdditionHandler(this);
        this.siegeHandler = new SiegeHandler(this);
        this.stateHandler = new StateHandler(this);
        this.parallelSettleHandler = new ParallelSettleHandler(this);
        this.settleHandler = new SettleHandler(this);
        this.eventHandler = new EventHandler(this);
        this.summoningHandler = new SummoningHandler(this);
        initBelongsToNpc(adapter);
    }

    @Override
    public BattleRole getMasterRole() {
        return masterRole;
    }

    @Override
    public BattleRoleContext getContext() {
        return context;
    }

    @Override
    public SceneObjType getType() {
        return type;
    }

    public void setType(SceneObjType type) {
        if (this.type != type) {
            LOGGER.debug("BattleLog {} set role type={}->{}", this, this.type, type);
            this.type = type;
        }
    }

    @Override
    public IBattleRoleAdapter getAdapter() {
        return adapter;
    }

    @Override
    public long getRoleId() {
        return this.roleId;
    }

    /**
     * 初始化战斗角色是否属于NPC军队
     *
     * @param adapter 战斗角色适配器
     */
    private void initBelongsToNpc(IBattleRoleAdapter adapter) {
        int troopId = adapter.getTroop().getTroopId();
        TroopTemplate troopTemplate = ResHolder.findTemplate(TroopTemplate.class, troopId);
        if (null != troopTemplate) {
            belongsToNpc = troopTemplate.getBelongsToNpc();
        }
    }

    public void prepareAction(BattleTickContext tickContext) {
        if (!canAction()) {
            setTargetId(0);
        } else {
            // 出手之前确定一下本回合的战斗目标
            refreshBattleTarget();
        }

        // 刷新夹击
        siegeHandler.refreshPincerNum();

        // 战斗开始触发器
        tryTriggerBattleStart();
        // 回合开始时触发器
        SkillSystem.trigger(this, TriggerType.TT_ROUND_START);
    }

    public void tryTriggerBattleStart() {
        if (context.isNewBoy()) {
            context.setNewBoy(false);
            SkillSystem.trigger(this, CommonEnum.TriggerType.TT_BATTLE_START);
        }
    }

    public void tryAction(BattleTickContext tickContext) {
        // 快照生成触发器
        SkillSystem.trigger(this, TriggerType.TT_GEN_SNAPSHOT);

        long actionTargetId = getTargetId();
        BattleRelation actionRelation = myBattleRelation.get(actionTargetId);
        if (actionRelation == null || actionRelation.shouldHangup()) {
            // 技能的目标可以是上回合的目标，即使当前回合目标不存在，依然需要尝试释放
            tryFireSkill(tickContext);
            return;
        }
        BattleRole target = actionRelation.getRole(actionTargetId);
        if (target == null) {
            WechatLog.error("BattleErrLog actionTarget is null={}={}", roleId, actionTargetId);
            return;
        }

        getAdapter().beforeOrdinaryAttack(this, target);

        BattleLogUtil.logAtk(this, target, false);

        // 普攻
        boolean atkSuccess = ordinaryAttack(new ActionContext(this, ActionType.ORDINARY_ATTACK), target);
        if (atkSuccess) {
            // 普攻触发器
            SkillSystem.trigger(this, CommonEnum.TriggerType.TT_ORDINARY_ATTACK);
            // 受到普攻触发器
            SkillSystem.trigger(target, CommonEnum.TriggerType.TT_UNDER_ORDINARY_ATTACK);
        }

        // 对面反击
        boolean retAtkSuccess = target.onBeOrdinaryAttacked(this, atkSuccess);
        if (retAtkSuccess) {
            // 受击方反击触发器
            SkillSystem.trigger(target, CommonEnum.TriggerType.TT_HIT_BACK);
        }
        // 尝试用技能目标释放技能
        tryFireSkill(tickContext);
    }

    private void tryFireSkill(BattleTickContext tickContext) {
        // 主将/副将施放技能
        boolean fireSuccess = skillHandler.fireActiveSkill(tickContext);
        // AI技能释放
        this.handleAiSkillEvent(tickContext);
        // 释放延时技能
        skillHandler.tryFireDelaySkill(tickContext);
        // 技能释放成功
        if (fireSuccess) {
            // 主动技能释放触发器
            SkillSystem.trigger(this, CommonEnum.TriggerType.TT_FIRE_SKILL);
            // 目标一定是存在的
            BattleRole skillTarget = getGround().getRole(skillHandler.getSkillTargetId());
            // 受到主动技能伤害触发器
            if (skillTarget.getSkillBlackboard().isDamagedByActiveSkill()) {
                SkillSystem.trigger(skillTarget, CommonEnum.TriggerType.TT_UNDER_SKILL_ATTACK);
            }
        }
    }

    protected void refreshBattleTarget() {
        long activeTargetId = getActiveTargetId();
        long targetIdToSet = getTargetId();

        if (activeTargetId <= 0) {
            // 如果没有主动追击的目标，在回合开始前需要重新判定本回合的出手目标
            long curTargetId = getTargetId();
            BattleRelation curTargetRelation = myBattleRelation.get(curTargetId);
            if (curTargetRelation == null || curTargetRelation.shouldHangup()) {
                for (Map.Entry<Long, BattleRelation> entry : myBattleRelation.entrySet()) {
                    long rTid = entry.getKey();
                    BattleRelation r = entry.getValue();
                    BattleRole target = r.getRole(rTid);
                    if (target.getAdapter().canBeSearchSelect() && rTid != curTargetId && !r.shouldHangup()) {
                        targetIdToSet = rTid;
                        break;
                    }
                }
            }
        } else if (getTargetId() != activeTargetId) {
            targetIdToSet = activeTargetId;
        }

        // 检查攻击距离是否满足条件
        if (targetIdToSet != 0 && !getAdapter().isDistanceOk(targetIdToSet)) {
            targetIdToSet = 0;
        }

        setTargetId(targetIdToSet);
    }

    /**
     * 简单普攻
     */
    public boolean ordinaryAttack(ActionContext actionCtx, BattleRole target) {
        boolean isOrdAtk = actionCtx.getType() == ActionType.ORDINARY_ATTACK;
        // 普攻 + 缴械 = 啥都不干
        if (isOrdAtk && getStateHandler().isinState(BattleConstants.BattleRoleState.DISARM)) {
            return false;
        }

        long oldDefenceShield = target.getBuffHandler().getBuffValue(CommonEnum.BuffEffectType.ET_SHIELD_BUF);

        // doAttack
        DamageContext damageCtx = BattleFormula.stdApplyDamage(getSnapShot("AtkOrdinaryAttack"), target.getSnapShot("DefOrdinaryAttack"), actionCtx, null, null, oldDefenceShield);

        // 结算护盾
        target.decShieldValue(damageCtx.getDecreaseDamage(), oldDefenceShield);

        target.getContext().addDamageCtx(damageCtx);

        // postAttack
        increaseAngerByOrdAtk(actionCtx.getType());
        return true;
    }

    public boolean onBeOrdinaryAttacked(BattleRole attacker, boolean atkSuccess) {
        if (!atkSuccess || !canAttackBack()) {
            // attacker没有普攻，或自己无法反击
            return false;
        }

        BattleLogUtil.logAtk(this, attacker, true);

        // 反击
        return ordinaryAttack(new ActionContext(this, ActionType.ATTACK_BACK), attacker);
    }

    public void checkDeadAndEndRelation(BattleTickContext tickContext) {
        //只要一方死
        if (isDead()) {
            LOGGER.debug("BattleLog 一方战死,battle end,one={},other={}", this, myBattleRelation.keySet());
            this.clearOnDead(tickContext);
        }
    }

    public boolean isDead() {
        return !hasAnyAlive();
    }

    public boolean hasActiveRelation() {
        for (BattleRelation relation : myBattleRelation.values()) {
            if (!relation.isStopped()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 自己死掉了
     */
    private void clearOnDead(BattleTickContext tickContext) {
        if (isCityObj() && !isInDungeon()) {
            // 主堡被打败了，要走掠夺逻辑
            handleCityDead(tickContext);
        } else {
            myBattleRelation.forEach((otherId, relation) -> relation.end(BattleOverType.BOT_END, tickContext));
        }
    }

    private void handleCityDead(BattleTickContext tickContext) {
        // 资源保护理由
        PlunderProtectReason plunderProtectReason = getAdapter().getBePlunderReasonOrNull();
        Map<Long, Map<Long, SsPlayerMisc.PlunderWeight>> plunderWeightMap = Maps.newHashMap();
        myBattleRelation.forEach((otherId, relation) -> {
            if (relation.isStopped()) {
                // 这里如果不直接return，会有死循环问题
                // 例:dead->end->endSingleRelation->deleteObj->end (集结体)
                return;
            }
            if (plunderProtectReason == null) {
                // 资源不被保护
                BattleRole enemyRole = relation.getEnemyRole(roleId);
                Map<Long, SsPlayerMisc.PlunderWeight> plunderWeight = enemyRole.getAdapter().getPlunderWeight();
                // 进攻方没有死亡，可以发起掠夺，获得对方负载
                if (!enemyRole.isDead()) {
                    // 任意一个行军有负重就需要发起掠夺
                    boolean needPlunder = IterableUtils.matchesAny(plunderWeight.values(), it -> it.getPlunderWeight() > 0);
                    if (needPlunder) {
                        plunderWeightMap.put(relation.getId(), plunderWeight);
                        LOGGER.info("BattleLog toAdd plunder, relation={} bePlunder={}", relation, this);
                        // 标记该relation为待掠夺，等ask回来再处理邮件相关内容
                        ground.plunderRelations.put(relation.getId(), relation);
                        enemyRole.context.pendingPlunderRelationIds.add(relation.getId());
                        context.pendingPlunderRelationIds.add(relation.getId());
                    }
                }
            } else {
                // 资源受保护
                BattleRecord.RoleRecord otherRecord = relation.getContext().getRoleRecord(otherId);
                otherRecord.fillPlunder(otherId, new HashMap<>(), PlunderResultEnum.PRE_RESOURCE_BE_PROTECT);

                BattleRecord.RoleRecord selfRecord = relation.getContext().getRoleRecord(getRoleId());
                selfRecord.fillPlunder(getRoleId(), new HashMap<>(), CommonEnum.PlunderResultEnum.PRE_RESOURCE_BE_PROTECT);
            }
            relation.end(BattleOverType.BOT_END, tickContext);
        });
        context.pendingPlunderRelationIds.clear();

        getAdapter().askCityToPlunder(plunderWeightMap);
    }

    protected void beforeEndSingleRelation(BattleOverType type, BattleRole other, boolean isEnemyDead, BattleRelation relation) {
        try {
            getAdapter().beforeEndSingleRelation(type, other.getAdapter(), isEnemyDead, relation);
        } catch (Exception e) {
            LOGGER.error("BattleErrLog beforeEndSingleRelation failed one={} other={} relation={} type={}", this, other, relation, type, e);
        }
    }

    protected void onEndSingleRelation(BattleOverType type, BattleRole other, boolean isDead, boolean isEnemyDead, BattleRecord.RecordOne record) {
        try {
            getAdapter().onEndSingleRelation(type, isDead, isEnemyDead, other.getAdapter(), record, other.isNpcTroop());
        } catch (Exception e) {
            LOGGER.error("BattleErrLog onEndSingleRelation failed one={} other={} type={}", this, other, type, e);
        }
        if (getTargetId() == other.getRoleId()) {
            setTargetId(0);
        }
        getContext().onEndSingleRelation();
        // 击败了玩家
        if (!isDead() && isEnemyDead) {
            if (other.isPlayerObj()) {
                // 击败玩家触发器
                SkillSystem.trigger(this, CommonEnum.TriggerType.TT_BEAT_PLAYER);
            } else if (other.isNpcTroop()) {
                // 击败野怪触发器
                SkillSystem.trigger(this, CommonEnum.TriggerType.TT_BEAT_MONSTER);
            }
        }
    }

    @Override
    public boolean isNpcTroop() {
        return this.type == SceneObjType.SOT_MONSTER || this.type == SceneObjType.SOT_STRONG_POINT_ARMY;
    }

    public boolean isBelongsToNpc() {
        return this.belongsToNpc;
    }

    protected void afterEndSingleRelation(BattleOverType type, BattleRole other, boolean isDead, boolean isEnemyDead, BattleRecord.RecordOne record) {
        try {
            getAdapter().afterEndSingleRelation(type, isDead, isEnemyDead, other.getAdapter(), record);
        } catch (Exception e) {
            LOGGER.error("BattleErrLog afterEndSingleRelation failed one={} other={} type={}", this, other, type, e);
        }
        if (!hasActiveRelation()) {
            onEndAllRelation(type);
        }
        if (getActiveTargetId() == other.getRoleId()) {
            setActiveTargetId(0);
        }
    }

    public void clearOnEndAllRelation() {
        LOGGER.debug("BattleLog battle clear={}", this);
        BattleProp battleProp = getAdapter().getBattleProp();
        battleProp.setTargetId(0);
        battleProp.setMany(0);
        setActiveTargetId(0);
        battleProp.setBattleState(BattleState.BS_Over);

        // fix(1018653):区域技能结束战斗关系后依然要保持状态，直到自动回收
        if (getAdapter().getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill) {
            return;
        }
        eventHandler.clear();
        skillHandler.resetBlackboard();
        if (mainHero != null) {
            mainHero.clearSkill();
        }
        if (deputyHero != null) {
            deputyHero.clearSkill();
        }
        getSiegeHandler().clear();

        // 脱战清除buff
        this.getBuffHandler().removeBuffOnOutBattle();
        if (CollectionUtils.isNotEmpty(context.delayTaskList)) {
            LOGGER.warn("BattleLog skillDelayFire not empty on EndAllRelation={}, size={}", this, context.delayTaskList.size());
        }
        // 这里其实也不太对，上面先打个日志看着
        context.delayTaskList.clear();
    }

    /**
     * 普攻/反击条件下怒气回复
     */
    private void increaseAngerByOrdAtk(ActionType type) {
        // 这里没有广播普攻，放在外面了
        // 普通攻击怒气恢复
        ConstBattleTemplate constBattleTemplate = ResHolder.getResService(ConstBattleKVResService.class).getTemplate();
        int angerRecovery = 0;
        switch (type) {
            case ATTACK_BACK: {
                angerRecovery = constBattleTemplate.getAntiAtkRageRecover();
                break;
            }
            case ORDINARY_ATTACK: {
                angerRecovery = constBattleTemplate.getDirectAtkRageRecover();
                break;
            }
            default: {
                break;
            }
        }
        // 怒气值
        double value = BattleFormula.getHeroPower(getSnapShot("IncreaseAngerByOrdAtk"), angerRecovery);

        BattleHero mainHero = getMainHero();
        if (mainHero != null) {
            mainHero.increaseCacheAnger(value, type);
        }
    }

    public void buildHero() {
        TroopProp troopProp = getAdapter().getTroop();
        if (troopProp.getMainHero().getHeroId() != 0) {
            BattleGround.getBattleLog().printfRelationHeroChange(this, troopProp, true);
            mainHero = new BattleHero(troopProp.getMainHero(), this, false);
        } else {
            // 英雄置空
            clearHero(mainHero);
            mainHero = null;
        }

        if (troopProp.getDeputyHero().getHeroId() != 0) {
            BattleGround.getBattleLog().printfRelationHeroChange(this, troopProp, false);
            deputyHero = new BattleHero(troopProp.getDeputyHero(), this, true);
        } else {
            clearHero(deputyHero);
            deputyHero = null;
        }

        // 重置副将技能触发回合数
        if (deputyHero != null) {
            deputyHero.setCanFireActiveSkill(false);
            deputyHero.resetDeputyHeroTriggerRound();
        }
    }

    private void clearHero(BattleHero hero) {
        if (hero != null) {
            hero.clear();
        }
    }


    public void initSoldierByTroop() {
        TroopProp troopProp = getAdapter().getTroop();
        for (SoldierProp soldierProp : troopProp.getTroop().values()) {
            addSoldierUnit(new SoldierUnit.OwnerInfo(getRoleId(), getType()), soldierProp);
        }
    }

    public void initState() {
        getAdapter().getBattleProp().setBattleState(BattleState.BS_IDLE);
    }

    public void addRelation(long enemyRoleId, BattleRelation battleRelation) {
        myBattleRelation.put(enemyRoleId, battleRelation);
        clearToAddRelation(battleRelation.getKey());
        getAdapter().getBattleProp().addRelationIdList(enemyRoleId);
        context.addRelation(enemyRoleId, battleRelation.getContext(), true);
    }

    public void clearMyRelationWith(BattleRole other) {
        myBattleRelation.remove(other.getRoleId());
        getAdapter().getBattleProp().removeRelationIdList(other.getRoleId());
        if (myBattleRelation.size() <= 0) {
            getAdapter().afterEndAllRelation();
        }
        LOGGER.debug("BattleLog remove relation self:{}, other:{} myBattleRelation:{}", this, other, myBattleRelation);
        if (needRemoveRoleAfterBattleEnd()) {
            getGround().activeRoleMap.remove(getRoleId());

            afterDelete();
        }
    }

    public boolean hasRelationWith(long roleId) {
        return myBattleRelation.containsKey(roleId);
    }

    public boolean hasRunningRelation() {
        for (BattleRelation relation : myBattleRelation.values()) {
            if (relation.getStatus() == BattleConstants.BattleRelationStatus.Running && !relation.isHangupInLastTick()) {
                return true;
            }
        }
        return false;
    }

    public BattleRelation relationWith(long roleId) {
        return myBattleRelation.get(roleId);
    }

    public void forEachRelation(Consumer<BattleRelation> consumer) {
        for (BattleRelation relation : myBattleRelation.values()) {
            consumer.accept(relation);
        }
    }

    public void forEachCopyRelation(Consumer<BattleRelation> consumer) {
        for (BattleRelation relation : new LinkedList<>(myBattleRelation.values())) {
            consumer.accept(relation);
        }
    }

    public Set<Long> getRelationKeys() {
        return myBattleRelation.keySet();
    }

    public Collection<BattleRelation> getRelations() {
        return myBattleRelation.values();
    }

    public void forEachValidRelation(Consumer<BattleRelation> consumer) {
        for (BattleRelation relation : myBattleRelation.values()) {
            if (!relation.isStopped()) {
                consumer.accept(relation);
            }
        }
    }

    public List<BattleRelation> getAllValidRelation() {
        List<BattleRelation> ret = Lists.newArrayList();
        for (BattleRelation relation : myBattleRelation.values()) {
            if (!relation.isStopped()) {
                ret.add(relation);
            }
        }
        return ret;
    }

    // region **********soldier**********

    private SoldierGroup findOrCreateGroup(int soldierType) {
        return groupMap.computeIfAbsent(soldierType, k -> new SoldierGroup(this, soldierType));
    }

    private static int soldierTypeOf(int soldierId) {
        // 找不到就报错了
        SoldierTypeTemplate template = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldierId);
        return template.getSoldierType();
    }

    /**
     * 为role新增一个兵力unit，基于传入的 SoldierProp 对象
     *
     * @param childOwnerInfo unit归属者
     * @param soldierProp    unit持有的实际兵力prop，战斗内的掉血会实时扣除这个prop
     */
    public void addSoldierUnit(SoldierUnit.OwnerInfo childOwnerInfo, SoldierProp soldierProp) {
        int soldierType = soldierTypeOf(soldierProp.getSoldierId());
        SoldierGroup group = findOrCreateGroup(soldierType);
        group.addSoldierUnit(childOwnerInfo, soldierProp);
    }

    /**
     * 为已存在的unit增添兵力，会直接加到持有的prop上去，调用者勿要反复往同一个prop中add
     * <p>
     * 调用前确认unit已添加，由adapter层做控制
     *
     * @param childAdapter unit归属者
     * @param soldierId    兵种id
     * @param num          增加的兵力数目，会直接加到 {@link SoldierProp#setNum} 上
     */
    public void addSoldier4ExistUnit(BattleRole childAdapter, int soldierId, int num) {
        int soldierType = soldierTypeOf(soldierId);
        SoldierGroup group = findOrCreateGroup(soldierType);
        group.addSoldier4ExistUnit(childAdapter, soldierId, num);
    }

    public void dropSoldierUnit(long roleId, int soldierId) {
        SoldierTypeTemplate template = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, soldierId);
        int soldierType = template.getSoldierType();
        SoldierGroup soldierGroup = groupMap.get(soldierType);
        if (soldierGroup == null) {
            WechatLog.error("BattleErrLog dropSoldierChild soldierGroup not init, {}", soldierType);
            return;
        }
        soldierGroup.dropSoldierChild(roleId, soldierId);
    }
    // endregion

    public void dropRole(long roleId) {
        for (SoldierGroup group : groupMap.values()) {
            group.dropSoldierChild(roleId);
        }
    }

    public void dropRole(List<Long> roleIds) {
        for (Long roleId : roleIds) {
            dropRole(roleId);
        }
    }

    public void clearAllProperty() {
        LOGGER.info("BattleLog role={} clearAllProperty", this);
        groupMap.clear();
        guardTower = null;
        snapshot = null;
    }

    public boolean isSoldierEmpty() {
        return groupMap.isEmpty();
    }

    public boolean hasAnyAlive() {
        return aliveCount() > 0;
    }

    /**
     * 获取队伍总数
     */
    public int soldierAliveCount() {
        int ret = 0;
        for (SoldierGroup e : groupMap.values()) {
            ret += e.aliveCount();
        }
        return ret;
    }

    public int guardTowerAliveCount() {
        return guardTower != null ? guardTower.aliveCount() : 0;
    }

    @Override
    public int aliveCount() {
        return soldierAliveCount() + guardTowerAliveCount();
    }

    /**
     * 获取队伍总数
     */
    @Override
    public int tempAliveCount() {
        int ret = 0;
        for (SoldierGroup e : groupMap.values()) {
            ret += e.tempAliveCount();
        }
        return ret;
    }

    /**
     * 获取队伍成员总数
     *
     * @return <roleId, <soldierId, count>>
     */
    public Map<Long, Map<Integer, Integer>> aliveCountByMember() {
        Map<Long, Map<Integer, Integer>> ret = Maps.newHashMap();
        for (SoldierGroup e : groupMap.values()) {
            for (Map.Entry<Long, Map<Integer, Integer>> entry : e.aliveCountByMember().entrySet()) {
                ret.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap()).putAll(entry.getValue());
            }
        }
        return ret;
    }

    /**
     * 获取队伍map
     */
    public Map<Integer, Integer> aliveSoldiers(boolean withSlight) {
        Map<Integer, Integer> ret = new HashMap<>();
        for (SoldierGroup e : groupMap.values()) {
            for (Soldier s : e.getSoldiers()) {
                if (withSlight) {
                    ret.put(s.getId(), s.aliveAndSlight());
                } else {
                    ret.put(s.getId(), s.aliveCount());
                }
            }
        }
        return ret;
    }

    public boolean isGuardTowerAlive() {
        return guardTowerAliveCount() > 0;
    }

    /**
     * 是否可以主动出手
     */
    public boolean canAction() {
        return getAdapter().canAction();
    }

    /**
     * 能否进行反击
     */
    @Override
    public boolean canAttackBack() {
        return getAdapter().canHurtBack();
    }

    public void setMainHero(BattleHero hero) {
        this.mainHero = hero;
    }

    /**
     * 做全员恢复
     */
    public void recover() {
        for (SoldierGroup e : groupMap.values()) {
            e.recover();
        }
    }

    public int getPincerAttackerNum() {
        return getAdapter().getBattleProp().getMany();
    }

    public boolean hasTarget() {
        return !myBattleRelation.isEmpty();
    }

    /**
     * 获取一种兵的最大数量
     */
    public int getSoldierMax() {
        return groupMap.values().stream().mapToInt(SoldierGroup::getSoldierMax).sum();
    }

    public Map<BuffEffectType, Long> getAllBuffValue() {
        return getAdditionHandler().getAllFinalAddition();
    }

    public long getFinalBuffValue(BuffEffectType buff) {
        return getAdditionHandler().getFinalAddition(buff);
    }

    public boolean hasRelationWith(BattleRole other) {
        return this.hasRelationWith(other.getRoleId());
    }

    public boolean hasRelation() {
        return !myBattleRelation.isEmpty();
    }

    public boolean needRemoveRoleAfterBattleEnd() {
        return getAdapter().needRemoveRoleAfterBattleEnd() && !hasRelation() && hasActiveRole();
    }

    private boolean hasActiveRole() {
        return getGround().activeRoleMap.containsKey(getRoleId());
    }

    public BattleRelation relationWith(BattleRole other) {
        return this.relationWith(other.getRoleId());
    }

    @Nullable
    public BattleHero getMainHero() {
        return mainHero;
    }

    @Nullable
    public BattleHero getDeputyHero() {
        return deputyHero;
    }

    public void setDeputyHero(BattleHero hero) {
        this.deputyHero = hero;
    }

    @Override
    public long getTargetId() {
        return getAdapter().getBattleProp().getTargetId();
    }

    public void setTargetId(long targetId) {
        long oldTargetId = getTargetId();
        if (targetId == oldTargetId) {
            return;
        }
        getAdapter().getBattleProp().setTargetId(targetId);
        siegeHandler.refreshSiege(oldTargetId, targetId);
        // 日志-变更目标
        BattleGround.getBattleLog().printfRelationTargetId(this, targetId);
    }

    public void setActiveTargetId(long activeTargetId) {
        getAdapter().getBattleProp().setActiveTargetId(activeTargetId);
    }

    public long getActiveTargetId() {
        return getAdapter().getBattleProp().getActiveTargetId();
    }

    /**
     * 取出所有战斗上下文信息，清理context
     */
    public BattleRecordAllProp flushRecord() {
        return context.flushRecord();
    }

    /**
     * 包含了多少种兵种
     */
    public Set<Integer> containedSoldierType() {
        return groupMap.keySet();
    }

    /**
     * @return 包含了多少种兵种（如果兵种全是轻伤、重伤和死亡就不计入）
     */
    public Set<Integer> containedAliveSoldierType() {
        Set<Integer> ret = Sets.newHashSet();
        for (Map.Entry<Integer, SoldierGroup> entry : groupMap.entrySet()) {
            if (entry.getValue().aliveCount() > 0) {
                ret.add(entry.getKey());
            }
        }
        return ret;
    }

    /**
     * 用所有unit的soldier数据合并组装出role面板troop的士兵数据，仅集结体类型可以执行本方法
     */
    public void refreshTroop() {
        TroopProp troop = getAdapter().getTroop();
        // 先清理再合并
        troop.clearTroop();

        for (SoldierGroup group : groupMap.values()) {
            for (Soldier soldier : group.getSoldiers()) {
                soldier.forEachUnit(this::plusSoldierPropToTroop);
            }
        }

        if (guardTower != null) {
            plusSoldierPropToTroop(guardTower.getTowerUnit());
        }
    }

    private void plusSoldierPropToTroop(SoldierUnit unit) {
        SoldierData data = unit.getData();
        SoldierProp soldierProp = getAdapter().getTroop().getTroopV(data.getSoldierId());
        if (soldierProp == null) {
            soldierProp = getAdapter().getTroop().addEmptyTroop(data.getSoldierId());
        }
        data.plusTo(soldierProp);
    }

    public void afterDelete() {
        LOGGER.info("BattleLog remove role, role={}", this);
        this.context.clear();
        getAdapter().clearAfterSettle();
    }

    public void addBattleRoundEvent(BattleRoundEvent event) {
        getEventHandler().addBattleRoundEvent(event);
    }

    public void handleBattleRoundEvent(BattleTickContext tickContext) {
        getEventHandler().handleBattleRoundEvent(tickContext);
    }

    public void handleAiSkillEvent(BattleTickContext tickContext) {
        getEventHandler().handleAiSkillEvent(tickContext);
    }

    /**
     * 获取排序好后的士兵列表
     */
    public List<Soldier> getSoldierList() {
        List<Soldier> ret = new ArrayList<>();
        for (SoldierGroup group : groupMap.values()) {
            ret.addAll(group.getSoldiers());
        }
        return ret;
    }

    public void setGuardTower(GuardTower guardTower) {
        this.guardTower = guardTower;
    }

    @Override
    public GuardTower getGuardTower() {
        return guardTower;
    }

    @Override
    public Map<Integer, SoldierGroup> getGroupMap() {
        return groupMap;
    }

    public Set<Long> getAllEnemyClan() {
        Set<Long> ret = Sets.newHashSet();
        for (BattleRelation relation : myBattleRelation.values()) {
            long clanId = relation.getEnemyRole(roleId).getAdapter().getClanId();
            if (clanId > 0) {
                ret.add(clanId);
            }
        }
        return ret;
    }

    @Override
    public String toString() {
        return "BattleRole:[" + adapter.getEntityType() + "-" + type + "-" + roleId + "]";
    }

    public Set<Long> getAllEnemyPlayerId() {
        Set<Long> playerIds = Sets.newHashSet();
        for (BattleRelation relation : myBattleRelation.values()) {
            long playerId = relation.getEnemyRole(roleId).getAdapter().getPlayerId();
            if (playerId != 0) {
                playerIds.add(playerId);
            }
        }
        return playerIds;
    }

    public boolean hasBattleWithNeutral() {
        for (BattleRelation relation : myBattleRelation.values()) {
            if (relation.getEnemyRole(roleId).getAdapter().getCampEnum() == Camp.C_NEUTRAL) {
                return true;
            }
        }
        return false;
    }

    public boolean isFiringSkill() {
        return skillHandler.isFiringSkill();
    }

    public BattleRole getTargetRole() {
        return getGround().getRoleOrNull(getTargetId());
    }

    /**
     * 当前血量百分比
     */
    public double aliveSoldierRate() {
        return (double) aliveCount() / getSoldierMax();
    }

    @Override
    public boolean isMonsterObj() {
        return getType() == SceneObjType.SOT_MONSTER;
    }

    public boolean isPlayerObj() {
        return adapter.getPlayerId() > 0;
    }

    public boolean isInDungeon() {
        return ground.isInDungeon();
    }

    public boolean needBattleRecord() {
        return ground.needBattleRecord();
    }

    @Override
    public boolean isInSimulator() {
        return ground.isInSimulator();
    }

    public SkillBlackboard getSkillBlackboard() {
        return skillHandler.getSkillBlackboard();
    }

    public boolean isCityObj() {
        return getType() == SceneObjType.SOT_CITY_ARMY_SELF;
    }

    public boolean isTroopObj() {
        return getType() == CommonEnum.SceneObjType.SOT_ARMY || getType() == CommonEnum.SceneObjType.SOT_ARMY_GROUP;
    }

    public boolean isSinglePlayerArmy() {
        return getType() == CommonEnum.SceneObjType.SOT_ARMY && isPlayerObj();
    }


    @Override
    public int getRallyCapacity() {
        int rallyCapacity = (int) adapter.getRallyCapacity();
        if (rallyCapacity < 0) {
            rallyCapacity = getSoldierMax();
        }
        return rallyCapacity;
    }

    @Override
    public BattleRoleSnapshot getSnapShot(String reason) {
        if (snapshot != null && snapshot.getSnapshotRound() == ground.getGroundRound()) {
            return snapshot;
        }
        // 快照回合数和当前回合数不同，重新生成

        // 和目标没有relation时，帮对方刷一下快照（主要用于给队友加治疗、护盾、怒气）
        if (getGround().getRoleOrNull(this.getRoleId()) == null) {
            refreshNewRoundSnapShot();
            return snapshot;
        }
        // 保底
        snapshot = new BattleRoleSnapshot(this);
        LOGGER.warn("BattleErrLog snapshot should constructed, round:{} role:{} reason:{}", snapshot.getSnapshotRound(), this, reason);
        return snapshot;
    }

    public void refreshNewRoundSnapShot() {
        // 根据回合数和是否已刷新过来确认一下是否需要刷新
        if (snapshot != null && snapshot.getSnapshotRound() == getGround().getGroundRound()) {
            LOGGER.debug("BattleLog refreshNewRoundSnapShot success snapshot={}", snapshot);
            return;
        }
        LOGGER.debug("BattleLog refreshNewRoundSnapShot start");
        snapshot = new BattleRoleSnapshot(this);
        BattleGround.getBattleLog().printfSnapshot(this);
        LOGGER.debug("BattleLog refreshNewRoundSnapShot end, snapshot={}", snapshot);
    }

    private void onEndAllRelation(CommonEnum.BattleOverType type) {
        if (adapter.getBattleProp().getBattleState() == CommonEnum.BattleState.BS_Over) {
            // 单个role只能endAll一次
            LOGGER.warn("BattleLog {} onEndAllRelation state=over", this);
            return;
        }
        try {
            boolean isAnyEnemyAlive = IterableUtils.matchesAny(myBattleRelation.values(), relation -> relation.getEnemyRole(this.roleId).hasAnyAlive());
            boolean isMeAlive = hasAnyAlive();
            BattleResult.Builder resultBuilder = new BattleResult.Builder()
                    .setType(type)
                    .setAlive(isMeAlive)
                    .setAnyEnemyAlive(isAnyEnemyAlive);
            List<BattleRecord.RecordOne> allRecords = Lists.newArrayList();
            for (BattleRelation value : myBattleRelation.values()) {
                allRecords.add(value.getContext().getRecordOne());
                allRecords.addAll(value.getContext().getHistoryRecords());
            }
            resultBuilder.setAllRecords(allRecords);
            adapter.endAllRelation(resultBuilder.build());

            if (context.isWaitingForPlunder()) {
                if (ServerContext.isTestEnv() || ServerContext.isDevEnv()) {
                    LOGGER.info("BattleLog plunder process, {} waiting for plunder", this);
                }
                // 构建战报快照，等掠夺ask回来再发战报
                context.buildRecordSnapshot(isMeAlive, isAnyEnemyAlive, adapter.getBattleRecordPlayerIds());
            } else {
                BattleRecordAllProp recordAllProp = flushRecord();
                adapter.trySendRecordMail(adapter.getBattleRecordPlayerIds(), recordAllProp, isMeAlive, isAnyEnemyAlive);
            }

            if (!getAdapter().isDestroy() && !isDead()) {
                // 脱战未死亡触发器
                SkillSystem.trigger(this, CommonEnum.TriggerType.TT_END_BATTLE_NOT_DEFEAT);
            }
        } catch (Exception e) {
            LOGGER.error("BattleErrLog onEndAllRelation failed role={} type={}", this, type, e);
        } finally {
            clearOnEndAllRelation();
        }
    }

    public BattleGround getGround() {
        return ground;
    }

    public boolean isInTick() {
        return isInTick;
    }

    public void setInTick(boolean inTick) {
        isInTick = inTick;
    }

    public BuffHandler getBuffHandler() {
        return buffHandler;
    }

    public AdditionHandler getAdditionHandler() {
        return additionHandler;
    }

    public SiegeHandler getSiegeHandler() {
        return siegeHandler;
    }

    public StateHandler getStateHandler() {
        return stateHandler;
    }

    public ParallelSettleHandler getParallelSettleHandler() {
        return parallelSettleHandler;
    }

    @Override
    public SettleHandler getSettleHandler() {
        return settleHandler;
    }

    public SkillHandler getSkillHandler() {
        return skillHandler;
    }

    public EventHandler getEventHandler() {
        return eventHandler;
    }

    public SummoningHandler getSummoningHandler() {
        return summoningHandler;
    }

    public Map<Long, BattleRelation> getMyBattleRelation() {
        return myBattleRelation;
    }

    @Override
    public boolean isInstate(BattleConstants.BattleRoleState state) {
        return getStateHandler().isinState(state);
    }

    @Override
    public BattleRoleReadOnly getRole(long targetRoleId) {
        return getGround().getRole(targetRoleId);
    }

    @Override
    public boolean hasTreatmentRole(long roleId) {
        return getGround().hasTreatmentRole(roleId);
    }

    @Override
    public void removeTreatmentRole(long roleId) {
        getGround().removeTreatmentRole(roleId);
    }

    @Override
    public BattleRelationContext getRelationContext(long otherRoleId) {
        BattleRelation battleRelation = myBattleRelation.get(otherRoleId);
        if (battleRelation == null) {
            return null;
        }
        return battleRelation.getContext();
    }

    /**
     * 扣减护盾
     */
    public void decShieldValue(double dec, long oldDefenceShield) {
        if (dec <= 0) {
            return;
        }
        // 保底检测(强转一下，解决精度问题)
        if ((long) dec > oldDefenceShield) {
            LOGGER.error("BattleErrLog decShieldValue fail, dec > curShield, dec={}, curShield={}", dec, snapshot.getShieldValue());
            dec = oldDefenceShield;
        }
        List<Buff> shieldBuffList = getBuffHandler().getBuffList(BuffEffectType.ET_SHIELD_BUF);
        for (Buff baseBuff : shieldBuffList) {
            ShieldBuff shieldBuff = (ShieldBuff) baseBuff;
            BuffSpecDataShieldProp shieldData = shieldBuff.getData().getSpecData().getShieldData();
            double dec4buff = Math.min(dec, shieldData.getShieldValue());
            shieldData.setShieldValue((long) (shieldData.getShieldValue() - dec4buff));
            dec -= dec4buff;
            if (shieldData.getShieldValue() <= 0) {
                // 护盾破碎就清除
                getBuffHandler().removeBuff(shieldBuff);
                // 护盾破损触发器
                SkillSystem.trigger(this, TriggerType.TT_SHIELD_BROKEN);
            }
        }
    }

    /**
     * 获取战斗关系
     *
     * @param otherRoleId 0:任意对手 long:指定对手
     */
    public boolean hasRelationWithRoleId(long otherRoleId) {
        BattleGround ground = getGround();
        return ground.hasPrepareBattleRelation(getRoleId(), otherRoleId) || ground.hasBattleRelation(getRoleId(), otherRoleId);
    }

    public void onDeleteObj() {
        getSummoningHandler().recycleSummons();
        ground.removeToAddRole(this, "deleteObj");
    }
    

    public void addToAddRelation(BattleRelation toAddRelation) {
        myToAddRelation.put(toAddRelation.getKey(), toAddRelation);
    }

    public void clearToAddRelation(String key) {
        myToAddRelation.remove(key);
    }
}

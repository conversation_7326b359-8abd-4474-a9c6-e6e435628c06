package com.yorha.cnc.battle.skill.effect.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.EffectResult;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.common.constant.Constants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 释放多重效果的效果
 *
 * <AUTHOR>
 */
public class MultipleEffectValue extends AbstractSkillEffectValue {
    private static final Logger LOGGER = LogManager.getLogger(MultipleEffectValue.class);

    public MultipleEffectValue() {
        super(SkillEffectType.SET_MULTIPLE_EFFECT);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext,
                                              ActionContext actionCtx,
                                              SkillEffectTemplate template,
                                              BattleRole attacker,
                                              BattleRole target,
                                              EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        List<Integer> effectIds = Lists.newArrayList();
        // 给共用概率池用
        Map<Integer, Pair<Integer, Integer>> commonRateMap = Maps.newHashMap();

        for (Integer effectId : ResHolder.getResService(SkillDataTemplateService.class).getComboEffects(template)) {
            SkillEffectTemplate childTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillEffectTemplate(effectId);
            // 公用概率情况下不考虑自身状态2，且效果值也必定不为空
            if (childTemplate.getSelfTroopsState1() == CommonEnum.StatusType.ST_COMMON_RATE) {
                String[] value = ResHolder.getResService(SkillDataTemplateService.class).getEffectSelfStatusValue(effectId).getFirst();
                commonRateMap.put(effectId, Pair.of(Integer.parseInt(value[0]), Integer.parseInt(value[1])));
            } else {
                effectIds.add(effectId);
            }
        }
        // 共用概率池,随机一个效果出来
        if (!commonRateMap.isEmpty()) {
            int random = RandomUtils.nextInt(1, Constants.N_10_000 + 1);
            for (Map.Entry<Integer, Pair<Integer, Integer>> entry : commonRateMap.entrySet()) {
                if (entry.getValue().getFirst() <= random && random <= entry.getValue().getSecond()) {
                    effectIds.add(entry.getKey());
                    break;
                }
            }
        }

        for (int effectId : effectIds) {
            EffectContext effectSubContext = EffectContext.newBuilder()
                    .setCastRole(attacker)
                    .setSkillId(effectContext.getId())
                    .setType(effectContext.getType())
                    .setHeroId(effectContext.getHeroId())
                    .setEffectId(effectId)
                    .setDotContext(effectContext.getDotContext())
                    .setAttachBuffId(effectContext.getAttachBuffId())
                    .setLeaderRoleId(effectContext.getLeaderRoleId())
                    .build(true);
            SkillEffect effect = SkillEffect.createSubEffect(effectId, effectSubContext);
            if (effect == null) {
                LOGGER.error("BattleErrLog {} cast effect:{} failed.", attacker, effectId);
                continue;
            }
            EffectResult castRes = effect.cast(tickContext, actionCtx, attacker, target, effectSubContext, null);
            ret.addAll(castRes.getDto());
        }
        return ret;
    }
}

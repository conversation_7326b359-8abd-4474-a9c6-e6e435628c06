package com.yorha.cnc.battle.buf;

import com.yorha.cnc.battle.adapter.interfaces.IBattleMoveAdapter;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.utils.shape.Point;

/**
 * 定身buff
 *
 * <AUTHOR>
 */
public class FrozenBuff extends StateBuff {

    public FrozenBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public BattleConstants.BattleRoleState getState() {
        return BattleConstants.BattleRoleState.FROZEN;
    }

    @Override
    public void destroy(BattleRole owner) {
        super.destroy(owner);
        if (!owner.getStateHandler().isinState(getState()) && owner.getAdapter().getMoveAdapter() != null) {
            owner.getAdapter().getMoveAdapter().setIsFrozen(false);
        }
    }

    @Override
    public void execute(BattleRole owner) {
        super.execute(owner);
        IBattleMoveAdapter moveHandler = owner.getAdapter().getMoveAdapter();
        if (moveHandler != null) {
            // 停止移动
            moveHandler.setIsFrozen(true);
            moveHandler.stopMove();
            // 设置朝向
            BattleRole targetRole = owner.getTargetRole();
            if (targetRole != null) {
                Point targetPoint = targetRole.getAdapter().getCurPoint();
                Point curPoint = owner.getAdapter().getCurPoint();
                int x = targetPoint.getX() - curPoint.getX();
                int y = targetPoint.getY() - curPoint.getY();
//                LOGGER.debug("BattleLog targetPoint:{}, curPoint:{}", targetPoint, curPoint);
                moveHandler.setYaw(x, y);
//                LOGGER.debug("BattleLog yaw X:{}, Y:{}, angel:{}, radians:{}", battleEntity.getMoveComponent().getYaw().getX(), battleEntity.getMoveComponent().getYaw().getY(), battleEntity.getMoveComponent().getYaw().getAngle(), battleEntity.getMoveComponent().getYaw().getRadians());
            }
        }
    }
}

package com.yorha.cnc.battle.adapter.interfaces;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.common.BattleResult;
import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.skill.SkillResult;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.game.gen.prop.BattleProp;
import com.yorha.game.gen.prop.BattleRecordAllProp;
import com.yorha.game.gen.prop.TroopProp;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;
import com.yorha.proto.SsPlayerMisc;
import res.template.SummoningMonsterTemplate;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 战斗角色的外围扩展
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
public interface IBattleRoleAdapter {
    // ===========base=============
    default boolean canHurtBack() {
        return true;
    }

    default boolean canAction() {
        return true;
    }

    CommonEnum.Camp getCampEnum();

    long getClanId();

    long getPlayerId();

    long getRoleTypeId();

    long getLeaderRoleId();

    TroopProp getTroop();

    BattleProp getBattleProp();

    long getRoleId();

    CommonEnum.SceneObjType getType();

    int getZoneId();

    // ===========流程=============
    void beforeEndSingleRelation(CommonEnum.BattleOverType type, IBattleRoleAdapter other, boolean isEnemyDead, BattleRelation relation);

    void onEndSingleRelation(CommonEnum.BattleOverType type, boolean isDead, boolean isEnemyDead, IBattleRoleAdapter other, BattleRecord.RecordOne record, boolean enemyNpc);

    void afterEndSingleRelation(CommonEnum.BattleOverType type, boolean isDead, boolean isEnemyDead, IBattleRoleAdapter other, BattleRecord.RecordOne record);

    void beforeOrdinaryAttack(BattleRole attacker, BattleRole defender);

    void broadcastSkillResult(SkillResult skillResult, BattleConstants.BattleBroadCastNtfReason reason);

    void sendOrdinaryAttack(DamageResult damageResult);

    void endAllRelation(BattleResult battleResult);

    void onSettleRound(DamageResult damageResult);

    void clearAfterSettle();

    void afterEndAllRelation();

    void onAddBattleRelation(BattleRelation battleRelation);

    ErrorCode canBattleWithCode(BattleRole targetRole, boolean needCheckSiegeLimit);

    boolean campAllowBattle(BattleRole other);

    boolean canReady();

    boolean ready(IBattleRoleAdapter other);

    // ===========杂=============
    default Set<Long> getAllEnemyClan() {
        return Sets.newHashSet();
    }

    default Set<Long> getAllEnemyPlayerId() {
        return Sets.newHashSet();
    }

    List<Long> getAllChildRoleIdList();

    default void handleSoldierLoss(Map<Long, Map<Integer, SoldierLossData>> lossDetail) {
    }

    boolean isDistanceOk(long targetRoleId);

    void handleSoldierTreat(List<SoldierLossDTO> treatData);

    default boolean needRemoveRoleAfterBattleEnd() {
        return true;
    }

    CommonEnum.DamageRatioTypeEnum getDamageRatioType();

    EntityAttrOuterClass.EntityType getEntityType();

    Point getCurPoint();

    boolean isSiegeLimit(long clanId, long roleId);

    boolean needCheckSiege();

    boolean isDestroy();

    boolean isInClanTerritory();

    int reduceDurability(int effectValue);

    boolean isCollecting();

    // 能否被机制或AOE、AOI索敌
    boolean canBeSearchSelect();

    CommonEnum.ArmyDetailState getArmyDetailState();

    // ===========集结=============
    boolean isRally();

    long getRallyCapacity();

    // ===========战报=============
    void fillRoleSummary(BattleRecordAllProp recordAllProp);

    void fillRole(BattleRecord.RoleRecord roleRecord);

    void fillRoleMember(BattleRecord.RoleRecord roleRecord);

    BattleRecord.RoleMemberRecord buildRoleMemberRecord();

    default void trySendRecordMail(Set<Long> playerIds, BattleRecordAllProp recordAllProp, boolean alive, boolean anyEnemyAlive) {
    }

    Set<Long> getBattleRecordPlayerIds();

    default void afterGarrisonReplaced(BattleRecordAllProp recordAllProp) {
    }

    // ===========掠夺=============
    default Map<Long, SsPlayerMisc.PlunderWeight> getPlunderWeight() {
        return Maps.newHashMap();
    }

    default void handlePlunderResult(SsPlayerMisc.PlunderResult plunderResult, BattleRelation relation) {
    }

    CommonEnum.PlunderProtectReason getBePlunderReasonOrNull();

    void askCityToPlunder(Map<Long, Map<Long, SsPlayerMisc.PlunderWeight>> plunderWeightMap);

    // ===========EVA=============
    List<Integer> getBattleEvaSkills();

    // ===========handler=============
    IBattleAdditionAdapter getAdditionAdapter();

    IBattleBuffAdapter getBuffAdapter();

    @Nullable
    IBattleMoveAdapter getMoveAdapter();

    int getModelRadius();

    // =========召唤==========
    BattleRole invokeSummoningMonster(SummoningMonsterTemplate template, Point point, SceneObjSpawnParam param);

    // =========建筑=========
    List<BattleRole> getGvgMapBuildInnerArmy();

    boolean hasAnyAlive();

    /**
     * 有和护送野怪的战斗关系
     */
    boolean isActivityEscortBoss();

    /**
     * 获取无消耗aoe野怪的次数
     * 只有玩家部队需要重写
     */
    int getFreeHitMonsterCount();

    /**
     * 更新无消耗aoe野怪的次数
     * 只有玩家部队需要重写
     */
    void updateFreeHitMonsterCount(int count);
}

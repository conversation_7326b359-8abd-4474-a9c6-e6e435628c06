package com.yorha.cnc.battle.skill.effect;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;
import res.template.SkillEffectTemplate;

import java.util.List;

public interface ISkillEffectValue {
    CommonEnum.SkillEffectType getType();

    List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext,
                                       ActionContext actionCtx,
                                       SkillEffectTemplate template,
                                       BattleRole attacker,
                                       BattleRole target,
                                       EffectContext effectContext);
}

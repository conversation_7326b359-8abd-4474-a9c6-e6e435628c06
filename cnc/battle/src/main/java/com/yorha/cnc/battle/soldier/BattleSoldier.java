package com.yorha.cnc.battle.soldier;

import com.yorha.cnc.battle.core.BattleRole;


/**
 * <AUTHOR>
 */
public abstract class BattleSoldier {
    protected final BattleRole battleRole;

    protected BattleSoldier(BattleRole battleRole) {
        this.battleRole = battleRole;
    }

    abstract public int getId();

    abstract public int aliveCount();

    abstract public int getMaxCanTreat();

    abstract public int getMax();
}

package com.yorha.cnc.battle.buf.lifecycle;

import com.yorha.cnc.battle.buf.lifecycle.impl.AllLifeCycle;
import com.yorha.cnc.battle.buf.lifecycle.impl.AttackTimesLifeCycle;
import com.yorha.cnc.battle.buf.lifecycle.impl.DefendTimesLifeCycle;
import com.yorha.cnc.battle.buf.lifecycle.impl.RoundLifeCycle;
import com.yorha.proto.CommonEnum.LifeCycleType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BattleBuffTemplate;

/**
 * 生命周期管理器
 *
 * <AUTHOR>
 */
public class LifeCycleFactory {
    private static final Logger LOGGER = LogManager.getLogger(LifeCycleFactory.class);

    public static ILifeCycle create(BattleBuffTemplate template, long value) {
        LifeCycleType type = LifeCycleType.forNumber(template.getLifeCycleType());
        if (type == null) {
            LOGGER.error("BattleErrLog type not defined type={}", template.getLifeCycleType());
            return null;
        }
        value = value > 0 ? value : template.getLifeCycleValue();
        switch (type) {
            case LCT_ALL: {
                return new AllLifeCycle(type);
            }
            case LCT_ROUND: {
                return new RoundLifeCycle(type, value);
            }
            case LCT_ATTACKEE_TIMES:
                return new AttackTimesLifeCycle(type, value);

            case LCT_ATTACKER_TIMES: {
                return new DefendTimesLifeCycle(type, value);
            }
            default: {
                return null;
            }

        }
    }
}

package com.yorha.cnc.battle.soldier;

import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.game.gen.prop.SoldierProp;

/**
 * 战斗侧不直接使用SoldierProp，方便未来和外层的prop对象作隔离
 * <p>
 * 更方便地提供数据操作接口
 *
 * <AUTHOR>
 */
public class SoldierData {
    private final SoldierProp prop;
    /**
     * 兵损缓存，在apply中清空
     */
    private int tempLoss;

    private SoldierData(SoldierProp prop) {
        this.prop = prop;
    }

    public static SoldierData fromProp(SoldierProp prop) {
        return new SoldierData(prop);
    }

    public void applyLoss(SoldierLossData lossData) {
        tempLoss = 0;
        // 不掉血模式
        if (ServerContext.getServerDebugOption().isOpenNoDead() && ServerContext.getServerDebugOption().isBattleTestServer()) {
            return;
        }
        setSlight(getSlight() + lossData.getSlight());
        setSevere(getSevere() + lossData.getSevere());
        setDead(getDead() + lossData.getDead());
    }

    public int aliveCount() {
        return Math.max(0, getNum() - getSlight() - getSevere() - getDead());
    }

    public void applyTempLoss(int tempLoss) {
        this.tempLoss += tempLoss;
    }

    public int tempAliveCount() {
        return Math.max(0, getNum() - getSlight() - getSevere() - getDead() - tempLoss);
    }

    public void recover() {
        setSlight(0);
        setSevere(0);
        setDead(0);
    }

    /**
     * 存活 + 轻伤 士兵数量
     */
    public int aliveAndSlight() {
        return Math.max(0, getNum() - getSevere() - getDead());
    }

    public int getSoldierId() {
        return prop.getSoldierId();
    }

    public int getNum() {
        return prop.getNum();
    }

    public void setNum(int num) {
        prop.setNum(num);
    }

    public int getSlight() {
        return prop.getSlightWoundNum();
    }

    public void setSlight(int slight) {
        prop.setSlightWoundNum(slight);
    }

    public int getSevere() {
        return prop.getSevereWoundNum();
    }

    public void setSevere(int severe) {
        prop.setSevereWoundNum(severe);
    }

    public int getDead() {
        return prop.getDeadNum();
    }

    public void setDead(int dead) {
        prop.setDeadNum(dead);
    }

    public void plusTo(SoldierProp prop) {
        prop.setNum(prop.getNum() + getNum());
        prop.setSlightWoundNum(prop.getSlightWoundNum() + getSlight());
        prop.setSevereWoundNum(prop.getSevereWoundNum() + getSevere());
        prop.setDeadNum(prop.getDeadNum() + getDead());
    }

    @Override
    public String toString() {
        return "SoldierData{" +
                "prop=" + prop +
                ", tempLoss=" + tempLoss +
                '}';
    }
}

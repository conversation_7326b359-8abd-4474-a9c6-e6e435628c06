package com.yorha.cnc.battle.handler;

import com.google.common.collect.Maps;
import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.core.BattleRole;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class StateHandler extends BattleHandlerAbs<BattleRole> {
    /**
     * 战斗单位状态
     */
    private final Map<BattleConstants.BattleRoleState, Integer> stateMap = Maps.newEnumMap(BattleConstants.BattleRoleState.class);

    public StateHandler(BattleRole role) {
        super(role);
    }

    public boolean isinState(BattleConstants.BattleRoleState state) {
        return stateMap.getOrDefault(state, 0) > 0;
    }

    public void enterState(BattleConstants.BattleRoleState state) {
        stateMap.put(state, stateMap.getOrDefault(state, 0) + 1);
    }

    public void leaveState(BattleConstants.BattleRoleState state) {
        stateMap.put(state, stateMap.getOrDefault(state, 0) - 1);
        if (stateMap.get(state) <= 0) {
            stateMap.remove(state);
        }
    }
}

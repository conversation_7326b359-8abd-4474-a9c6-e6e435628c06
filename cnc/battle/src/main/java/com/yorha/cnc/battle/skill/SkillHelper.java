package com.yorha.cnc.battle.skill;

import com.yorha.cnc.battle.adapter.interfaces.IBattleMoveAdapter;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.ISkillEffectValue;
import com.yorha.cnc.battle.skill.effect.SkillEffectValueFactory;
import com.yorha.cnc.battle.skill.filter.ISkillEffectRelationFilter;
import com.yorha.cnc.battle.skill.filter.SkillEffectTargetTypeFactory;
import com.yorha.cnc.battle.skill.status.BattleTargetStatusFactory;
import com.yorha.cnc.battle.skill.status.IBattleTargetStatusChecker;
import com.yorha.common.constant.Constants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.utils.shape.*;
import com.yorha.common.utils.vector.Vector2f;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ShapeType;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MonsterTemplate;
import res.template.SkillEffectTemplate;
import res.template.SkillRangeTemplate;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能效果，一个技能可以有多个效果
 *
 * <AUTHOR> Jiang
 */
public class SkillHelper {

    private static final Logger LOGGER = LogManager.getLogger(SkillHelper.class);


    public static ISkillEffectValue getValue(SkillEffectTemplate template) {
        return SkillEffectValueFactory.of(template);
    }

    private static ISkillEffectRelationFilter getTargetFilter(SkillEffectTemplate template) {
        return SkillEffectTargetTypeFactory.of(template);
    }

    public static IBattleTargetStatusChecker getTargetChecker(SkillEffectTemplate template) {
        return BattleTargetStatusFactory.getTargetChecker(template.getTroopsState());
    }

    /**
     * 对目标排序
     */
    public static Comparator<BattleRole> getComparatorByEnemyRelation(SkillEffectTemplate template, long selfId) {
        Comparator<BattleRole> comparator;
        switch (template.getEnemyRelation()) {
            case ERL_FRIENDLY: {
                comparator = (r1, r2) -> {
                    if (r1.getAdapter().getPlayerId() == selfId && r2.getAdapter().getPlayerId() != selfId) {
                        return -1;
                    } else {
                        return 1;
                    }
                };
                break;
            }
            case ERL_ENEMY: {
                comparator = (r1, r2) -> {
                    if (r1.getAdapter().getPlayerId() != 0 && r2.getAdapter().getPlayerId() == 0) {
                        return -1;
                    } else {
                        return 1;
                    }
                };
                break;
            }
            default: {
                comparator = (r1, r2) -> 0;
                break;
            }
        }
        return comparator;
    }

    /**
     * 根据范围选择目标对象
     */
    public static Set<BattleRole> getTargetListByRange(SkillEffectTemplate effectTemplate,
                                                       BattleRole castObj,
                                                       BattleRole castTargetObj,
                                                       Set<Long> canNullSkillMarkTarget) {
        if (canNullSkillMarkTarget != null) {
            return castObj.getGround().getAdapter().getBatchBattleRoleById(canNullSkillMarkTarget);
        }
        SkillRangeTemplate rangeTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillRangeTemplateOrNull(effectTemplate.getRange());
        Set<BattleRole> ret = new HashSet<>();
        if (rangeTemplate == null) {
            // 指向性技能
            if (castTargetObj != null && castTargetObj.hasAnyAlive()) {
                ret.add(castTargetObj);
            } else {
                LOGGER.debug("BattleLog target is dead, castTargetObj={}", castTargetObj);
            }
            return ret;
        }
        Shape shape = getShape(castObj, castTargetObj, rangeTemplate);
        if (shape == null) {
            return ret;
        }
        // 一些底层机制的filter，暂不开放配置
        Set<BattleRole> aoiBattleRoles = castObj.getGround().getAdapter().getAoiBattleRoles(shape);
        return SkillHelper.filterByMechanism(castObj, aoiBattleRoles);
    }

    /**
     * 对目标进行过滤
     *
     * @param effect          效果
     * @param castObj         施法目标
     * @param selectTargetObj 选中目标（普攻目标）
     * @param castTargetObj   施法目标
     * @param targetList      施法选中的目标list
     */
    public static Set<BattleRole> filterTarget(SkillEffect effect, BattleRole castObj, BattleRole selectTargetObj, BattleRole castTargetObj, Set<BattleRole> targetList) {
        SkillEffectTemplate effectTemplate = effect.getTemplate();
        // 根据目标类型过滤
        targetList = filterByEntityType(effect, castTargetObj, targetList);
        // 根据敌我方关系过滤
        targetList = filterByRelation(effect, castObj, targetList, castTargetObj);
        // 根据目标BUFF过滤
        targetList = filterByBuff(effect, targetList);
        // 根据野怪免费aoe
        targetList = filterByFreeHit(effect, castObj, targetList);

        // 必中规则
        Set<BattleRole> mustBeHitList = mustBeHit(effectTemplate, castObj, castTargetObj, selectTargetObj);
        // 根据图形上限筛选（一定要在最后一步，有对最终数量的过滤）
        targetList = filterByRangeLimit(effectTemplate, castObj, selectTargetObj, targetList, mustBeHitList);
        // 记录野怪免费aoe次数
        recordFreeHit(castObj, targetList);

        return targetList;
    }

    private static Set<BattleRole> filterByFreeHit(SkillEffect effect, BattleRole castObj, Set<BattleRole> targetList) {
        SkillDataTemplateService skillDataTemplateService = ResHolder.getResService(SkillDataTemplateService.class);
        SkillRangeTemplate rangeTemplate = skillDataTemplateService.getSkillRangeTemplateOrNull(effect.getTemplate().getRange());
        if (rangeTemplate == null) {
            return targetList;
        }
        if (!castObj.isSinglePlayerArmy()) {
            return targetList;
        }
        int freeHitMonsterCount = castObj.getAdapter().getFreeHitMonsterCount();
        if (freeHitMonsterCount < 0) {
            return targetList;
        }
        int freeHitMaxCount = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getFreeHitMonster();
        int count = 0;
        Set<BattleRole> ret = new HashSet<>();
        for (BattleRole targetRole : targetList) {
            if (targetRole.isMonsterObj()) {
                // 1.需要体力消耗
                // 2.没建立战斗关系
                // 3.今日已经没有免费的打怪次数了
                // 过滤掉这个野怪，不能被aoe索敌选中
                MonsterTemplate template = ResHolder.getInstance().findValueFromMap(MonsterTemplate.class, (int) targetRole.getAdapter().getRoleTypeId());
                if (template == null) {
                    continue;
                }
                if (template.getEnergy() <= 0 || castObj.hasRelationWith(targetRole)) {
                    ret.add(targetRole);
                    continue;
                }
                if (freeHitMonsterCount + count < freeHitMaxCount) {
                    ret.add(targetRole);
                    count++;
                }
            } else {
                ret.add(targetRole);
            }
        }
        return ret;
    }

    private static void recordFreeHit(BattleRole castObj, Set<BattleRole> targetList) {
        int count = 0;
        for (BattleRole targetRole : targetList) {
            if (targetRole.isMonsterObj()) {
                MonsterTemplate template = ResHolder.getInstance().findValueFromMap(MonsterTemplate.class, (int) targetRole.getAdapter().getRoleTypeId());
                if (template == null) {
                    continue;
                }
                if (template.getEnergy() <= 0 || castObj.hasRelationWith(targetRole)) {
                    continue;
                }
                count++;
            }
        }
        castObj.getAdapter().updateFreeHitMonsterCount(count);
    }

    /**
     * 一些固定的过滤机制
     * 对战双方是否可以索敌被索敌
     */
    private static Set<BattleRole> filterByMechanism(BattleRole castObj, Set<BattleRole> targetList) {
        Set<BattleRole> ret = new HashSet<>();
        for (BattleRole target : targetList) {
            if (!canSearchSelect(castObj, target)) {
                continue;
            }
            ret.add(target);
        }
        return ret;
    }

    /**
     * 根据双方判定是否可以被索到
     */
    private static boolean canSearchSelect(BattleRole castObj, BattleRole target) {
        if (!target.getAdapter().canBeSearchSelect()) {
            return false;
        }
        // 区域技能可以无视是否存活
        if (castObj.getAdapter().getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill) {
            return true;
        }
        return target.getAdapter().hasAnyAlive();
    }


    public static BattleRole getReleaseTargetObj(SkillEffectTemplate effectTemplate, BattleRole attacker, BattleRole originTarget) {
        CommonEnum.TargetType targetType = effectTemplate.getTargetType();
        switch (targetType) {
            case TT_DEFENDER: { // 选中者
                return originTarget;
            }
            case TT_ATTACKER: { // 进攻者
                return attacker;
            }
            case TT_DEFENDER_TARGET: { // 选中者的普攻目标
                if (originTarget == null) {
                    return null;
                }
                return originTarget.getTargetRole();
            }
            case TT_ATTACKER_TARGET: { // 进攻者的普攻目标
                if (attacker == null) {
                    return null;
                }
                return attacker.getTargetRole();
            }
            default: {
                return null;
            }
        }
    }

    public static Vector2f getSkillYawBeforeRotate(BattleRole castObj, BattleRole castTargetObj) {
        IBattleMoveAdapter moveHandler = castObj.getAdapter().getMoveAdapter();
        Vector2f yaw;
        if (castTargetObj == null) {
            if (moveHandler != null) {
                // 没有施法目标，返回当前朝向
                yaw = moveHandler.getYaw();
            } else {
                // 没有施法目标，没有朝向，放不出技能
                return null;
            }
        } else {
            if (moveHandler != null && !moveHandler.canMove()) {
                // 有施法目标，被定身，技能朝向为定身时的朝向
                yaw = moveHandler.getYaw();
            } else {
                // 有施法目标，朝向为施法者到施法目标的方向
                yaw = Vector2f.getVectorFromPointToPoint(castObj.getAdapter().getCurPoint(), castTargetObj.getAdapter().getCurPoint());
            }
        }
        return yaw;
    }

    protected static Vector2f getSkillYaw(SkillRangeTemplate rangeTemplate, BattleRole castObj, BattleRole castTargetObj) {
        Vector2f yaw = castObj.getSkillHandler().getYaw();
        if (yaw != null) {
            // 主副将释放的技能，已经预释放过，保存在了role身上
            return yaw.rotate(rangeTemplate.getRevolve());
        } else {
            // 没有预释放过，需要根据施法者、施法目标计算
            yaw = getSkillYawBeforeRotate(castObj, castTargetObj);
            if (yaw != null) {
                return yaw.rotate(rangeTemplate.getRevolve());
            }
        }
        return null;
    }

    /**
     * 根据施法者、施法目标获取择敌图
     * 矩阵/扇形需要以施法者为原点
     * 原型/环形需要以施法目标为中心
     */
    private static Shape getShape(BattleRole castObj, BattleRole castTargetObj, SkillRangeTemplate template) {
        Vector2f yaw = getSkillYaw(template, castObj, castTargetObj);
        if (yaw == null) {
            LOGGER.error("BattleErrLog yaw is null, templateId={}", template.getId());
            return null;
        }
        ShapeType type = ShapeType.forNumber(template.getType());
        if (type == null) {
            LOGGER.error("BattleErrLog shapeType is null, template={}", template.getId());
            return null;
        }
        Point castObjPoint = getCastObjPoint(castObj);
        Point castTargetPoint = getCastTargetObjPoint(castObj, castTargetObj);
        switch (type) {
            case ST_RECTANGLE: {
                return Rectangle.valueOf(castObjPoint, yaw, template.getAngle(), template.getRadius());
            }
            case ST_CIRCLE: {
                if (template.getAngle() == Constants.ANGLE_MAX_VALUE) {
                    if (castTargetObj == null || castTargetPoint == null) {
                        LOGGER.error("BattleErrLog circle curPoint castTargetObj is null, template={}, castTargetObj={}, castTargetPoint={}", template.getId(), castTargetObj, castTargetPoint);
                        return null;
                    }
                    return Circle.valueOf(castTargetPoint, template.getRadius());
                }
                return Sector.valueOf(castObjPoint, yaw, template.getRadius(), template.getAngle());
            }
            case ST_RING: {
                if (castTargetObj == null || castTargetPoint == null) {
                    LOGGER.error("BattleErrLog ring curPoint castTargetObj is null, template={}, castTargetObj={}, castTargetPoint={}", template.getId(), castTargetObj, castTargetPoint);
                    return null;
                }
                return Ring.valueOf(castTargetPoint, template.getRadius(), template.getInnerDiameter());
            }
            default: {
                return null;
            }
        }
    }

    /**
     * 获取释放技能的角色的位置
     * 释放技能如果经过了预释放阶段，释放技能角色的位置应该以预释放阶段记录的信息为准；如果没有，则以当前位置为准
     *
     * @param castObj 释放技能的角色
     */
    private static Point getCastObjPoint(BattleRole castObj) {
        // 获取需要使用的施法角色的Point
        Point castObjPoint = castObj.getAdapter().getCurPoint();
        if (castObj.getSkillHandler().getCastRolePoint() != null) {
            castObjPoint = castObj.getSkillHandler().getCastRolePoint();
        }
        return castObjPoint;
    }

    /**
     * 获取技能目标的位置
     * 释放技能如果经过了预释放阶段，技能目标角色的位置应该以预释放阶段记录的信息为准；如果没有，则以当前位置为准
     *
     * @param castObj       释放技能的角色
     * @param castTargetObj 技能目标的角色
     */
    @Nullable
    private static Point getCastTargetObjPoint(BattleRole castObj, BattleRole castTargetObj) {
        // 获取需要使用的目标角色的Point
        Point castTargetPoint = null;
        if (castTargetObj == null) {
            // 目标为null
            return castTargetPoint;
        }
        if (castObj.getRoleId() == castTargetObj.getRoleId()) {
            // 技能目标是自己，尝试获取自己上一回合的目标点
            return getCastObjPoint(castObj);
        } else {
            // 技能目标是别人
            if (castObj.getSkillHandler().getTargetRolePoint() == null) {
                castTargetPoint = castTargetObj.getAdapter().getCurPoint();
            } else {
                castTargetPoint = castObj.getSkillHandler().getTargetRolePoint();
            }
        }

        return castTargetPoint;
    }

    /**
     * 选中目标点,敌我双方
     *
     * @return 目标
     */
    private static Set<BattleRole> filterByRelation(SkillEffect effect, BattleRole castObj, Set<BattleRole> target, BattleRole castTargetObj) {
        ISkillEffectRelationFilter targetFilter = getTargetFilter(effect.getTemplate());
        if (targetFilter == null) {
            return target;
        }
        return targetFilter.filter(effect, castObj, target, castTargetObj);
    }

    /**
     * 根据目标类型过滤
     */
    private static Set<BattleRole> filterByEntityType(SkillEffect effect, BattleRole castTargetObj, Set<BattleRole> targetList) {
        List<CommonEnum.EnemyType> enemyTypeList = effect.getTemplate().getEnemyTypeList().stream().map(CommonEnum.EnemyType::forNumber).collect(Collectors.toList());
        Set<BattleRole> ret = new HashSet<>();
        for (BattleRole target : targetList) {
            // 一条来自策划的默认规则（建筑是施法目标时，需要索敌到）
            if (enemyTypeList.size() <= 0) {
                if (isBattleSceneBuild(target.getAdapter().getEntityType()) && target != castTargetObj) {
                    continue;
                }
                // 只有army和monster会被范围技能溅射到
                // 建筑虽然不会被溅射到，但是如果建筑是当前的施法目标，那范围选敌还是要能选到它的
                ret.add(target);
                continue;
            }
            if (isInEnemyType(target, enemyTypeList)) {
                ret.add(target);
            }
        }
        return ret;
    }

    public static boolean isBattleSceneBuild(EntityAttrOuterClass.EntityType type) {
        return type != EntityAttrOuterClass.EntityType.ET_Army && type != EntityAttrOuterClass.EntityType.ET_Monster;
    }

    private static boolean isInEnemyType(BattleRole role, List<CommonEnum.EnemyType> enemyTypeList) {
        for (CommonEnum.EnemyType enemyType : enemyTypeList) {
            if (role.isTroopObj() && enemyType == CommonEnum.EnemyType.ETT_PLAYER_ARMY) {
                return true;
            }
            if (isBattleSceneBuild(role.getAdapter().getEntityType()) && enemyType == CommonEnum.EnemyType.ETT_BUILD) {
                return true;
            }
            if (role.isMonsterObj() && enemyType == CommonEnum.EnemyType.ETT_MONSTER) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据图形类型排序并筛选
     */
    private static Set<BattleRole> filterByRangeLimit(SkillEffectTemplate effectTemplate,
                                                      BattleRole castObj,
                                                      BattleRole atkTarget,
                                                      Set<BattleRole> targetList,
                                                      Set<BattleRole> mustBeHitList) {
        SkillRangeTemplate rangeTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillRangeTemplateOrNull(effectTemplate.getRange());

        // 无需筛选
        if (rangeTemplate == null) {
            if (mustBeHitList.size() > 0) {
                targetList.addAll(mustBeHitList);
            }
            return targetList;
        }

        // 过滤必中较多的情况
        if (rangeTemplate.getLimit() < 1 || rangeTemplate.getLimit() < mustBeHitList.size()) {
            LOGGER.error("BattleErrLog config fail, rangeTemplateLimit < 1, template={} mustBeHitListSize={}", rangeTemplate, mustBeHitList.size());
            return targetList;
        }
        if (mustBeHitList.size() == rangeTemplate.getLimit()) {
            return mustBeHitList;
        }

        // 后续都是必中较少 添加必中
        targetList.addAll(mustBeHitList);
        if (targetList.size() <= rangeTemplate.getLimit()) {
            return targetList;
        }

        HashSet<BattleRole> ret = new HashSet<>(mustBeHitList);

        // 清掉必中
        targetList.removeAll(mustBeHitList);
        if (targetList.isEmpty()) {
            return mustBeHitList;
        }
        // 非必中里面补几个命中
        int lave = rangeTemplate.getLimit() - mustBeHitList.size();

        ArrayList<BattleRole> result = new ArrayList<>(targetList);

        // sort1为默认的阵营关系排序规则 sort2为配置的范围排序规则，在sort2的基础上进行sort1排序
        result.sort(SkillHelper.getComparatorByConfig(rangeTemplate, atkTarget)
                .thenComparing(SkillHelper.getComparatorByEnemyRelation(effectTemplate, castObj.getRoleId())));

        ret.addAll(result.stream()
                .limit(lave)
                .collect(Collectors.toList()));
        return ret;
    }

    private static Comparator<BattleRole> getComparatorByConfig(SkillRangeTemplate template, BattleRole castObj) {
        switch (template.getSortType()) {
            case SRS_SOLDIER_NUM: {
                return (target1, target2) -> target1.aliveSoldierRate() > target2.aliveSoldierRate() ? 0 : 1;
            }
            case SRS_ATTACK_TARGET_BEHIND: {
                // fix:处理一下普攻目标优先在无普攻目标时的处理
                if (castObj == null) {
                    return (target1, target2) -> 0;
                }
                return (target1, target2) -> {
                    if (target1.getRoleId() == castObj.getRoleId() && target2.getRoleId() != castObj.getRoleId()) {
                        return 1; // obj1的ID等于指定ID，将obj1排在后面
                    } else if (target1.getRoleId() != castObj.getRoleId() && target2.getRoleId() == castObj.getRoleId()) {
                        return -1; // obj2的ID等于指定ID，将obj2排在后面
                    } else {
                        return 0; // 其他情况保持原有顺序
                    }
                };
            }
            case SRS_RALLY_PRIORITY: {
                return (target1, target2) -> {
                    // 当满足集结部队和个人部队比较时，取集结优先
                    if (target2.getType() == CommonEnum.SceneObjType.SOT_ARMY_GROUP && target1.getType() == CommonEnum.SceneObjType.SOT_ARMY) {
                        return 1;
                    }
                    if (target1.getType() == CommonEnum.SceneObjType.SOT_ARMY_GROUP && target2.getType() == CommonEnum.SceneObjType.SOT_ARMY) {
                        return -1;
                    }
                    return 0;
                };
            }
            default: {
                return (r1, r2) -> 0;
            }
        }
    }

    private static Set<BattleRole> mustBeHit(SkillEffectTemplate effectTemplate,
                                             BattleRole castObj,
                                             BattleRole castTargetObj,
                                             BattleRole selectTargetObj) {
        Set<BattleRole> obj = new HashSet<>();
        for (Integer integer : effectTemplate.getMustTypeList()) {
            CommonEnum.MustBeHitType mustBeHitType = CommonEnum.MustBeHitType.forNumber(integer);
            if (mustBeHitType == null) {
                return null;
            }
            switch (mustBeHitType) {
                case MBC_CAST: {
                    if (castTargetObj == null) {
                        LOGGER.error("BattleErrLog SkillHelper mustBeHit. effectId={} castObj={}", effectTemplate.getId(), castObj);
                    }
                    obj.add(castObj);
                    break;
                }
                case MBC_TARGET: {
                    if (castTargetObj == null) {
                        LOGGER.error("BattleErrLog SkillHelper mustBeHit. effectId={} selectTargetObj={}", effectTemplate.getId(), selectTargetObj);
                    }
                    obj.add(selectTargetObj);
                    break;
                }
                case MBC_CAST_TARGET: {
                    if (castTargetObj == null) {
                        LOGGER.error("BattleErrLog SkillHelper mustBeHit. effectId={} castTargetObj={}", effectTemplate.getId(), castTargetObj);
                    }
                    obj.add(castTargetObj);
                    break;
                }
                default: {
                    break;
                }
            }
        }
        return obj;
    }

    public static List<BattleRole> buildSearchResult(Set<BattleRole> targetList) {
        ArrayList<BattleRole> result = new ArrayList<>();
        for (BattleRole objSet : targetList) {
            if (objSet != null) {
                result.add(objSet);
            }
        }
        return result;
    }

    /**
     * 过滤含有配置BUFF的对象
     */
    private static Set<BattleRole> filterByBuff(SkillEffect effect, Set<BattleRole> targetList) {
        SkillDataTemplateService skillDataTemplateService = ResHolder.getResService(SkillDataTemplateService.class);
        SkillRangeTemplate rangeTemplate = skillDataTemplateService.getSkillRangeTemplateOrNull(effect.getTemplate().getRange());
        if (rangeTemplate == null) {
            return targetList;
        }
        return targetList.stream()
                .filter(target -> rangeTemplate.getFilterbuffList().stream().noneMatch(buffId -> target.getBuffHandler().hasBuff(buffId)))
                .collect(Collectors.toSet());
    }
}

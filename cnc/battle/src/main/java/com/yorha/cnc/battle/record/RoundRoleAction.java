package com.yorha.cnc.battle.record;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.action.*;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 一个回合中玩家发生的行为
 *
 * <AUTHOR>
 */
public class RoundRoleAction {
    private final BattleRole role;
    /**
     * 回合开始时的兵力
     */
    private final long aliveCount;
    /**
     * 普攻
     */
    private AtkAction atkAction;
    /**
     * 反击
     */
    private AtkAction atkBackAction;
    /**
     * 技能 key1=<heroId, skillId>
     */
    private final Map<CommonEnum.BattleLogSkillType, Map<Pair<Integer, Integer>, SkillAction>> skillActionMap;
    /**
     * 下回合预备释放的技能
     */
    private final List<SkillAction> prepareSkillAction;
    /**
     * 受到的技能效果 key=roleId, key1=<heroId, effectId>
     */
    private final Map<Long, Map<Pair<Integer, Integer>, EffectAction>> effectMap;
    /**
     * 增益
     */
    private final List<BuffAction> buffList;
    /**
     * 部队
     */
    private final List<TroopAction> troopList;

    public RoundRoleAction(BattleRole role) {
        this.role = role;
        this.aliveCount = role.aliveCount();
        this.skillActionMap = Maps.newHashMap();
        this.prepareSkillAction = Lists.newArrayList();
        this.buffList = Lists.newArrayList();
        this.troopList = Lists.newArrayList();
        this.effectMap = Maps.newHashMap();
    }

    public BattleRole getRole() {
        return role;
    }

    public long getRoleId() {
        return role.getRoleId();
    }

    @Nullable
    public AtkAction getAtkAction() {
        return atkAction;
    }

    @Nullable
    public AtkAction getAtkBackAction() {
        return atkBackAction;
    }

    public void setAtkAction() {
        this.atkAction = new AtkAction(false);
    }

    public void setAtkBackAction() {
        this.atkBackAction = new AtkAction(true);
    }

    public void addSkillAction(SkillAction skillAction) {
        skillActionMap
                .computeIfAbsent(skillAction.getInfo().getType(), v -> Maps.newHashMap())
                .put(Pair.of(skillAction.getInfo().getHeroId(), skillAction.getInfo().getId()), skillAction);
    }

    public void addPrepareSkillAction(SkillAction skillAction) {
        prepareSkillAction.add(skillAction);
    }

    public EffectAction getEffectAction(EffectContext effectContext) {
        return effectMap
                .getOrDefault(effectContext.getRoleId(), Maps.newHashMap())
                .get(Pair.of(effectContext.getHeroId(), effectContext.getEffectId()));
    }

    public void putEffectList(EffectAction effectAction) {
        effectMap
                .computeIfAbsent(effectAction.getExecutorInfo().getRoleId(), v -> Maps.newHashMap())
                .put(Pair.of(effectAction.getExecutorInfo().getHeroId(), effectAction.getExecutorInfo().getEffectId()), effectAction);
    }

    public void addBuffList(BuffAction buffAction) {
        if (buffAction.needLog()) {
            buffList.add(buffAction);
        }
        // 日志-新增buff
        BattleGround.getBattleLog().printfRelationBuffWithRole(role, buffAction);
    }

    public void addTroopList(TroopAction troopAction) {
        troopList.add(troopAction);
        // 日志-新增部队
        BattleGround.getBattleLog().printfRelationTroopWithRole(role, troopAction);
    }

    public CommonBattle.BattleRoundRole convert2Pb(RoundRoleAction enemy) {
        CommonBattle.BattleRoundRole.Builder builder = CommonBattle.BattleRoundRole.newBuilder()
                .setSoldier(aliveCount);
        long roundSoldierResult = getRoundSoldierResult(enemy);
        if (roundSoldierResult != 0) {
            builder.setLossSoldier(getRoundSoldierResult(enemy));
        }
        if (atkAction != null) {
            builder.addEvents(atkAction.convert2Event());
        }
        if (atkBackAction != null) {
            builder.addEvents(atkBackAction.convert2Event());
        }
        if (!skillActionMap.isEmpty()) {
            for (Map<Pair<Integer, Integer>, SkillAction> value : skillActionMap.values()) {
                builder.addAllEvents(value.values().stream().map(SkillAction::convert2Event).collect(Collectors.toList()));
            }
        }
        if (!prepareSkillAction.isEmpty()) {
            builder.addAllEvents(prepareSkillAction.stream().map(SkillAction::convert2Event).collect(Collectors.toList()));
        }
        if (!buffList.isEmpty()) {
            builder.addAllEvents(buffList.stream().map(BuffAction::convert2Event).collect(Collectors.toList()));
        }
        if (!troopList.isEmpty()) {
            builder.addAllEvents(troopList.stream().map(TroopAction::convert2Event).collect(Collectors.toList()));
        }
        if (!effectMap.isEmpty()) {
            for (Map<Pair<Integer, Integer>, EffectAction> value : effectMap.values()) {
                builder.addAllEvents(value.values().stream().map(EffectAction::convert2Event).collect(Collectors.toList()));
            }
        }
        return builder.build();
    }

    /**
     * 当前回合士兵结果
     */
    private long getRoundSoldierResult(RoundRoleAction enemy) {
        // 对面普攻对我造成的损兵
        long sum = enemy.getAtkLossCount();
        // 受到技能效果后士兵数量
        for (Map<Pair<Integer, Integer>, EffectAction> value : effectMap.values()) {
            sum += value.values().stream().mapToLong(it -> it.unitData.getAllLossCount()).sum();
        }
        // 士兵进出
        for (TroopAction troopAction : troopList) {
            sum += troopAction.getNumToDisplay();
        }
        return sum;
    }

    /**
     * 普攻造成对面损兵数量
     */
    public long getCommonAtkLossCount() {
        long ret = 0;
        if (atkAction != null) {
            return atkAction.unitData.getAllLossCountWithoutTreatment();
        }
        return ret;
    }

    /**
     * 反击造成对面损兵数量
     */
    public long getCommonAtkBackLossCount() {
        long ret = 0;
        if (atkBackAction != null) {
            ret = atkBackAction.unitData.getAllLossCountWithoutTreatment();
        }
        return ret;
    }

    /**
     * 普攻+反击造成对面损兵数量
     */
    public long getAtkLossCount() {
        long sum = 0;
        if (atkAction != null) {
            sum += atkAction.unitData.getAllLossCount();
        }
        if (atkBackAction != null) {
            sum += atkBackAction.unitData.getAllLossCount();
        }
        return sum;
    }
}

package com.yorha.cnc.battle.skill.effect;

import com.yorha.cnc.battle.common.Amend;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.List;

/**
 * 技能效果逻辑
 *
 * <AUTHOR>
 */
public abstract class AbstractSkillEffectValue implements ISkillEffectValue {

    private final SkillEffectType type;

    public AbstractSkillEffectValue(SkillEffectType type) {
        this.type = type;
    }

    @Override
    public SkillEffectType getType() {
        return this.type;
    }

    /**
     * 处理这个技能效果的逻辑
     */
    @Override
    public abstract List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext,
                                                       ActionContext actionCtx,
                                                       SkillEffectTemplate template,
                                                       BattleRole attacker,
                                                       BattleRole target,
                                                       EffectContext effectContext);

    protected Amend getBaseAmend(Amend baseAmend, double value) {
        Amend amend = new Amend().multiRatio(baseAmend.getRatio());
        if (value > 0) {
            amend.multiRatio(value);
        }
        return amend;
    }


    /**
     * 根据实际数量和配置参数进行比较
     * (召唤物使用)
     *
     * @param num       实际数量
     * @param configNum 配置数量
     * @param rule      规则 1:大于 2:大于等于 3:等于 4:小于 5:小于等于
     * @return true:符合配置
     */
    public static boolean matchNumByConfig(int num, int configNum, int rule) {
        switch (rule) {
            case 1: {
                if (num > configNum) {
                    return true;
                }
                return false;
            }
            case 2: {
                if (num >= configNum) {
                    return true;
                }
                return false;
            }
            case 3: {
                if (num == configNum) {
                    return true;
                }
                return false;
            }
            case 4: {
                if (num < configNum) {
                    return true;
                }
                return false;
            }
            case 5: {
                if (num <= configNum) {
                    return true;
                }
                return false;
            }
            default: {
                return false;
            }
        }
    }

    /**
     * 获取治疗公式中所需的进攻方快照
     * @return 某些情况下防守方也会成为进攻方
     */
    protected BattleRoleSnapshot getTreatmentAttackSnapshot(BattleRole attacker, BattleRole target, EffectContext effectContext, int param2) {
        // 参数2：治疗公式中是否忽视进攻方，忽视情况下使用防守方作为进攻方数据
        boolean isIgnoreAttack = param2 > 0;
        if (isIgnoreAttack){
            return target.getSnapShot("Def-AtkPercentageTreatment");
        }

        BattleRoleSnapshot attackerSnapshot;
        if (effectContext.isDot()) {
            attackerSnapshot = effectContext.getDotSnapshot();
        } else {
            attacker.refreshNewRoundSnapShot();
            attackerSnapshot = attacker.getSnapShot("AtkPercentageTreatment");
        }
        return attackerSnapshot;
    }
}

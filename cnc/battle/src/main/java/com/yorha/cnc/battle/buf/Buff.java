package com.yorha.cnc.battle.buf;

import com.yorha.cnc.battle.buf.lifecycle.ILifeCycle;
import com.yorha.cnc.battle.buf.lifecycle.LifeCycleFactory;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.BuffEvent;
import com.yorha.cnc.battle.skill.SkillEffectContainer;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.BuffProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BuffEffectType;
import res.template.BattleBuffTemplate;

/**
 * 效果
 *
 * <AUTHOR> Jiang
 */
public abstract class Buff extends SkillEffectContainer {

    private final BuffProp data;

    private final int buffTemplateId;

    private final ILifeCycle lifeCycle;

    private boolean ready;
    /**
     * buff施加者信息
     */
    private final EffectContext effectContext;

    public Buff(BattleRole owner, PendingBuff builder) {
        BattleBuffTemplate template = ResHolder.getTemplate(BattleBuffTemplate.class, builder.getBuffId());
        this.buffTemplateId = builder.getBuffId();
        this.ready = false;
        this.effectContext = builder.getExecutorInfo();
        this.lifeCycle = LifeCycleFactory.create(template, builder.getLifeCycle());
        this.data = new BuffProp()
                .setLayer(builder.getLayer())
                .setTemplateId(builder.getBuffId())
                .setGroupId(template.getEffectGroupId());
        initEffects();
    }

    @Override
    public void initEffects() {
        for (Integer effectId : getTemplate().getAddSkillEffectList()) {
            EffectContext effectContext = EffectContext.newBuilder()
                    .setSkillId(this.effectContext.getId())
                    .setEffectId(effectId)
                    .setAttachBuffId(getTemplateId())
                    .setCastRole(this.effectContext.getCastRole())
                    .setHeroId(this.effectContext.getHeroId())
                    .setType(this.effectContext.getType())
                    .setLeaderRoleId(this.effectContext.getLeaderRoleId())
                    .setDotContext(this.effectContext.getDotContext())
                    .build(false);
            addEffect(effectId, effectContext);
        }
    }

    public boolean isValid() {
        return lifeCycle.checkValid();
    }

    public boolean isReady() {
        return ready;
    }

    public void setReady(boolean ready) {
        this.ready = ready;
    }

    public boolean update(BattleRole owner) {
        if (!isReady()) {
            setReady(true);
            execute(owner);
            data.setCreateTsMs(owner.getGround().getAdapter().now());

            if (!isImmediately()) {
                BuffEvent buffEvent = new BuffEvent(CommonEnum.BattleLogBuffType.BLBT_SKILL, getTemplateId(), BuffEvent.OperationType.TAKE_EFFECT);
                owner.addBattleRoundEvent(buffEvent);
            }
        }

        if (lifeCycle.checkValid()) {
            lifeCycle.execute();
            if ((this instanceof StealthBuff) && owner.hasRelation()){
                return true;
            }
        }
        return !isValid();
    }

    public int getTemplateId() {
        return buffTemplateId;
    }

    public BattleBuffTemplate getTemplate() {
        return ResHolder.getTemplate(BattleBuffTemplate.class, getTemplateId());
    }

    public int getGroupId() {
        return getTemplate().getEffectGroupId();
    }

    public long getValue() {
        return (long) getTemplate().getValue() * getLayer();
    }

    public BuffEffectType getType() {
        return BuffEffectType.forNumber(getTemplate().getType());
    }

    public BuffProp getData() {
        return data;
    }

    public int getLayer() {
        return data.getLayer();
    }

    public void setLayer(int layer) {
        data.setLayer(layer);
    }

    public ILifeCycle getLifeCycle() {
        return lifeCycle;
    }

    public EffectContext getExecutorInfo() {
        return effectContext;
    }

    public boolean isImmediately() {
        return getTemplate().getEffectiveImmediately();
    }

    /**
     * buff被移除
     *
     * @param owner
     */
    public abstract void destroy(BattleRole owner);

    /**
     * 处理buf逻辑
     *
     * @param owner
     */
    public abstract void add(BattleRole owner);

    /**
     * buf执行逻辑
     *
     * @param owner
     */
    public abstract void execute(BattleRole owner);

    public BattleRole getGiver() {
        return effectContext.getRole();
    }

    public boolean isWorking() {
        return isReady() && isValid();
    }
}

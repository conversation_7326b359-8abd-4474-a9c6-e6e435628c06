package com.yorha.cnc.battle.record.action;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.common.enums.battle.BattleUnitType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;
import res.template.SoldierTypeTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class UnitDataChangeAction {
    public final Map<BattleUnitType, SoldierChangeAction> data;

    public UnitDataChangeAction() {
        this.data = Maps.newHashMap();
    }

    public long getAllLossCount() {
        long sum = 0;
        for (SoldierChangeAction value : data.values()) {
            sum += value.sum();
        }
        return sum;
    }

    public long getAllLossCountWithoutTreatment() {
        long sum = 0;
        for (SoldierChangeAction value : data.values()) {
            sum += value.sumWithoutTreatment();
        }
        return sum;
    }

    public void plusUnitDataChange(long memberRoleId, int unitId, SoldierLossData lossData) {
        SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(unitId);
        BattleUnitType unitType;
        if (soldierTemplate.getSoldierType() == CommonEnum.SoldierType.ST_GuardTower_VALUE) {
            unitType = BattleUnitType.GUARD_TOWER;
        } else {
            unitType = BattleUnitType.SOLDIER;
        }
        data.computeIfAbsent(unitType, v -> new SoldierChangeAction()).plusSoldierData(memberRoleId, unitId, lossData);
    }

    public CommonBattle.UnitData convert2Pb() {
        CommonBattle.UnitData.Builder builder = CommonBattle.UnitData.newBuilder();
        for (Map.Entry<BattleUnitType, SoldierChangeAction> entry : data.entrySet()) {
            if (entry.getKey() == BattleUnitType.SOLDIER) {
                builder.setSoldier(CommonBattle.SoldierData.newBuilder().putAllLoss(entry.getValue().allCountMap()));
            }
            if (entry.getKey() == BattleUnitType.GUARD_TOWER) {
                builder.setGuardTower(CommonBattle.GuardTowerData.newBuilder().setLoss(entry.getValue().allCount()));
            }
        }
        return builder.build();
    }
}

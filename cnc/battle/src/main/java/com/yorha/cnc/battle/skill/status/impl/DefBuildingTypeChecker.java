package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.proto.CommonEnum;

import static com.yorha.common.constant.BattleConstants.*;

/**
 * 防守建筑
 *
 * <AUTHOR>
 */
public class DefBuilding<PERSON><PERSON><PERSON><PERSON><PERSON> extends <PERSON><PERSON><PERSON><PERSON> {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        switch (Integer.parseInt(params[0])) {
            case MAPBUILDING_AND_CITY:
                return role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF || role.getType() == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY;
            case MAPBUILDING:
                return role.getType() == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY;
            case CITY:
                return role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF;
            default:
                return false;
        }
    }
}

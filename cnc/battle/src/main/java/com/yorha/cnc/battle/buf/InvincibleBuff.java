package com.yorha.cnc.battle.buf;

import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.core.BattleRole;


/**
 * 无敌buff
 *
 * <AUTHOR>
 * @date 2023/5/11
 */
public class InvincibleBuff extends StateBuff {
    public InvincibleBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public BattleConstants.BattleRoleState getState() {
        return BattleConstants.BattleRoleState.INVINCIBLE;
    }
}

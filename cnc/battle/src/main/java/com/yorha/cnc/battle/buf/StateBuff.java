package com.yorha.cnc.battle.buf;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.constant.BattleConstants;


/**
 * 状态buff
 *
 * <AUTHOR>
 * @date 2023/5/11
 */
public abstract class StateBuff extends Buff {
    public StateBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    public abstract BattleConstants.BattleRoleState getState();

    @Override
    public void destroy(BattleRole owner) {
        owner.getStateHandler().leaveState(getState());
    }

    @Override
    public void add(BattleRole owner) {

    }

    @Override
    public void execute(BattleRole owner) {
        owner.getStateHandler().enterState(getState());
    }
}

package com.yorha.cnc.battle.core;

import com.yorha.cnc.battle.handler.SettleHandler;
import com.yorha.cnc.battle.soldier.GuardTower;
import com.yorha.cnc.battle.soldier.SoldierGroup;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/3
 */
public interface BattleRoleSettleOnly extends BattleRoleReadOnly {
    Map<Integer, SoldierGroup> getGroupMap();

    GuardTower getGuardTower();

    boolean hasTreatmentRole(long roleId);

    void removeTreatmentRole(long roleId);

    SettleHandler getSettleHandler();
}

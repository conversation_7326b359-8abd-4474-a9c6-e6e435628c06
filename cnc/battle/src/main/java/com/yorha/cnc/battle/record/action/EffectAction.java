package com.yorha.cnc.battle.record.action;

import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;


/**
 * <AUTHOR>
 */
public class EffectAction {
    private final EffectContext effectContext;
    private final CommonEnum.SkillEffectType type;
    public final UnitDataChangeAction unitData;
    private final long value;
    /**
     * 护盾抵扣伤害
     */
    private long shieldDamage;

    public EffectAction(EffectContext effectContext, CommonEnum.SkillEffectType type, int value) {
        this.effectContext = effectContext;
        this.type = type;
        this.unitData = new UnitDataChangeAction();
        this.value = value;
    }

    public void setShieldDamage(long shieldDamage) {
        this.shieldDamage = shieldDamage;
    }

    public EffectContext getExecutorInfo() {
        return effectContext;
    }

    public void plusUnitDataChange(long memberRoleId, int unitId, SoldierLossData lossData) {
        unitData.plusUnitDataChange(memberRoleId, unitId, lossData);
    }

    public CommonBattle.BattleEvent convert2Event() {
        return CommonBattle.BattleEvent.newBuilder()
                .setEvent(CommonEnum.BattleEventEnum.BET_EFFECT)
                .setEffectEvent(convert2EffectEvent())
                .build();
    }

    private CommonBattle.EffectEvent convert2EffectEvent() {
        return CommonBattle.EffectEvent.newBuilder()
                .setType(type)
                .setValue(value)
                .setExecutor(effectContext.convert2Pb())
                .setData(unitData.convert2Pb())
                .setDecreaseDamageByShield(shieldDamage)
                .build();
    }
}

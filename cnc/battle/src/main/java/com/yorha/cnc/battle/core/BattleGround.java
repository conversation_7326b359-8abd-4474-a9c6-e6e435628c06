package com.yorha.cnc.battle.core;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.adapter.interfaces.IBattleGroundAdapter;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.log.BattleLogManager;
import com.yorha.cnc.battle.unit.BattleHelper;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.constant.BattleConstants.BattleGroundType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.EntityAttrOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import javax.annotation.concurrent.NotThreadSafe;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveAction;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.yorha.common.resource.resservice.battle.BattleTemplateService.defaultBattleTypeWith;

/**
 * 一片战场（或对应地图的一个场景）
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class BattleGround {
    private static final Logger LOGGER = LogManager.getLogger(BattleGround.class);

    public static final boolean isLogEnable = ServerContext.isTestEnv() || ServerContext.isDevEnv();

    /**
     * 战场扩展
     */
    private final IBattleGroundAdapter adapter;
    /**
     * 战场上的所有战斗关系
     */
    protected final Map<String, BattleRelation> battleRelationMap = Maps.newHashMap();
    /**
     * 当前有战斗关系的role
     */
    protected final Map<Long, BattleRole> activeRoleMap = Maps.newLinkedHashMap();
    protected final Map<Long, BattleRole> toAddRoleMap = Maps.newLinkedHashMap();
    /**
     * 新建的relation一开始会被加入到这里，在tick中统一处理真正的加入逻辑
     */
    protected final Map<String, BattleRelation> toAddRelationMap = Maps.newHashMap();
    /**
     * 整个战场的回合，从0递增，不存库
     * <p>
     * 可用于实现某个部队每N秒最多触发一次某个效果，不用基于relation里面的round
     */
    protected int groundRound;
    /**
     * 战斗日志管理器
     */
    private static final BattleLogManager battleLog = new BattleLogManager();
    /**
     * 需要治疗的role
     */
    private final Map<Long, BattleRole> roleToTreatment = Maps.newHashMap();

    /**
     * BattleGround一次Tick的上下文
     */
    private BattleTickContext tickContext;

    private final BattleGroundType type;

    private final CommonEnum.DungeonType dungeonType;

    private final long pointMapId;

    private final Supplier<CommonEnum.MapType> mapTypeSupplier;
    /**
     * 等待掠夺结果的relation
     * 6回合后强制结束，ask超时为5s
     */
    public final Map<Long, BattleRelation> plunderRelations = Maps.newHashMap();
    /**
     * buff tick
     */
    private final Set<BattleRole> roleTickSet = Sets.newHashSet();
    private final Set<BattleRole> roleTickToRemoveSet = Sets.newHashSet();
    private final Set<BattleRole> roleTickToAddSet = Sets.newHashSet();

    private ForkJoinPool forkJoinPool = null;

    private final static int PARALLELISM = 4;
    private final static int PARALLEL_ENABLE_NUM = 50;

    /**
     * 大世界战斗tick是一个持续时间较长的同步操作
     * 为解决tick中forceEndAll删除relation、role容器元素会报ConcurrentModificationException的问题引入tick标识
     */
    private boolean isOnTick = false;

    public BattleGround(IBattleGroundAdapter adapter, BattleGroundType type, @Nullable CommonEnum.DungeonType dungeonType, long pointMapId, Supplier<CommonEnum.MapType> mapTypeSupplier) {
        this.adapter = adapter;
        this.type = type;
        this.dungeonType = dungeonType;
        this.pointMapId = pointMapId;
        this.mapTypeSupplier = mapTypeSupplier;
    }

    public void preAddRelation(BattleRelation relation) {
        if (relation == null || StringUtils.isEmpty(relation.getKey())) {
            LOGGER.error("BattleErrLog preAddRelation fail, key is null, relation={}", relation);
            return;
        }
        if (toAddRelationMap.containsKey(relation.getKey())) {
            LOGGER.error("BattleErrLog preAddRelation fail, key is exist, relation={} dirtyRelation={}", relation, toAddRelationMap.get(relation.getKey()));
            return;
        }
        if (battleRelationMap.containsKey(relation.getKey())) {
            LOGGER.error("BattleErrLog preAddRelation fail, key already add, relation={} dirtyRelation={}", relation, toAddRelationMap.get(relation.getKey()));
            return;
        }
        toAddRelationMap.put(relation.getKey(), relation);
        relation.getOne().addToAddRelation(relation);
        relation.getOther().addToAddRelation(relation);
    }

    /**
     * 事务性添加relation到
     */
    public void addRelation(BattleRelation relation, boolean addByPreAdd) {
        try {
            if (relation == null || StringUtils.isEmpty(relation.getKey())) {
                LOGGER.error("BattleErrLog addRelation fail, key is null, relation={}", relation);
                return;
            }
            if (battleRelationMap.containsKey(relation.getKey())) {
                LOGGER.error("BattleErrLog addRelation fail, key is exist, relation={} dirtyRelation={}", relation, battleRelationMap.get(relation.getKey()));
                return;
            }
            battleRelationMap.put(relation.getKey(), relation);

            // 來自AOE的，在这里不操作activeRoleMap，会ConcurrentModificationException
            if (addByPreAdd) {
                addRole(relation.getOne(), "addRelation");
                addRole(relation.getOther(), "addRelation");
            }
            relation.init();
        } catch (Exception e) {
            LOGGER.error("BattleErrLog addRelation fail relation={}", relation, e);
        }
    }


    public BattleRole getRole(long roleId) {
        BattleRole role = activeRoleMap.get(roleId);
        if (role == null) {
            throw new GeminiException(ErrorCode.BATTLE_ROLE_NOT_EXIST);
        }
        return role;
    }

    public BattleRole getRoleOrNull(long roleId) {
        return activeRoleMap.get(roleId);
    }

    /**
     * 判断两军是否战斗中
     */
    public boolean hasBattleRelation(long oneId, long otherId) {
        return findBattleRelationOrNull(oneId, otherId) != null;
    }

    /**
     * 判断两军是否已准备建立relation准备战斗
     */
    public boolean hasPrepareBattleRelation(long oneId, long otherId) {
        return findPrepareBattleRelationOrNull(oneId, otherId) != null;
    }

    /**
     * 获取两军的预准备的战斗关系
     */
    @Nullable
    public BattleRelation findPrepareBattleRelationOrNull(long oneId, long otherId) {
        return toAddRelationMap.get(BattleHelper.keyOf(oneId, otherId));
    }

    /**
     * 获取两军的战斗关系
     */
    @Nullable
    public BattleRelation findBattleRelationOrNull(long oneId, long otherId) {
        return battleRelationMap.get(BattleHelper.keyOf(otherId, oneId));
    }

    /**
     * tick一次战斗，遍历所有战斗关系，执行一个回合的出手
     */
    public void tick() {
        // buff更新tick
        buffTick();
        // 战斗流程tick
        battleTick();
    }

    /**
     * 战斗执行逻辑
     * 1、准备。回合号、初始Relation、清理脏数据、初始Role
     * 2、快照。并发记录所有战斗关系当前数据
     * 3、出手。双方同时出手，记录damage、treatment、buff
     * 4、结算。根据出手阶段的数据结算
     * 5、清理。清理掉一些已经结束的relation、role
     */
    private void battleTick() {
        isOnTick = true;

        groundRound++;

        tickContext = new BattleTickContext(groundRound, getAdapter().now());

        flushNewRelation();
        // 打印一下当前relation、role
        printfBattleGroudStatus();

        forceEndPendingPlunderRelation();

        forEachRelation("relation.prepare", relation -> relation.prepareBeforeAction(tickContext));

        // 结算回合外的治疗
        clear("onPrepare");

        flushNewRole();

        // 两次tick之间，战斗层之外可能引起战斗数据的一些变化，但是并不能操作战斗内私有的状态，需要在tick中做对应的操作
        forEachRole("handleBattleRoundEvent", role -> role.handleBattleRoundEvent(tickContext));

        forEachRole("rolePrepare", role -> role.prepareAction(tickContext));

        tickContext.watchForOnTick.mark("prepare");

        // 刷新快照
        snapshot();
        tickContext.watchForOnTick.mark("snapshot_parallel");

        // 同时出手，攻击，放技能
        action();
        tickContext.watchForOnTick.mark("action");

        // 结算
        settle();
        tickContext.watchForOnTick.mark("settle");

        forEachRelation("relation.afterSettleRound", relation -> relation.afterSettleRound(tickContext));

        // settleRound之后判断role是否已经死亡,清理relation
        forEachRole("endRelation", role -> role.checkDeadAndEndRelation(tickContext));

        // 清理
        clear("afterTick");

        tickContext.watchForOnTick.mark("end");

        isOnTick = false;
    }

    /**
     * tick过程中的清理过程
     */
    private void clear(String reason) {
        // 1、结算回合外的治疗
        Iterator<BattleRole> iterator = roleToTreatment.values().iterator();
        while (iterator.hasNext()) {
            BattleRole role = iterator.next();
            try {
                role.getParallelSettleHandler().settleTreatment(tickContext);
            } catch (Exception e) {
                LOGGER.error("BattleErrLog settleTreatmentRole failed reason={} role={}", reason, role, e);
            } finally {
                iterator.remove();
            }
        }

        // 2、清理stop的relation
        Iterator<BattleRelation> relationIterator = battleRelationMap.values().iterator();
        while (relationIterator.hasNext()) {
            BattleRelation relation = relationIterator.next();
            try {
                if (relation.isStopped()) {
                    relationIterator.remove();
                    relation.afterRemoveRelation();
                }
            } catch (Exception e) {
                LOGGER.error("BattleErrLog relation.afterRemoveRelation failed reason={} relation={}", reason, relation, e);
            }
        }

        // 3、清理stop的activeRole
        Iterator<BattleRole> roleIterator = activeRoleMap.values().iterator();
        while (roleIterator.hasNext()) {
            BattleRole role = roleIterator.next();
            try {
                if (role.needRemoveRoleAfterBattleEnd()) {
                    roleIterator.remove();
                    role.afterDelete();
                }
            } catch (Exception e) {
                LOGGER.error("BattleErrLog role.afterDelete failed reason={} role={}", reason, role, e);
            }
        }
    }

    private void printfBattleGroudStatus() {
        int relationNum = battleRelationMap.size();
        int roleNum = activeRoleMap.size();
        if (isInSimulator()) {
            LOGGER.debug("BattleLog gemini_pref BattleGround tick round:{} runningRelation:{}, runningRelationDetail:{} prepareRelation:{} prepareRelationDetail:{} role:{} relationNum:{}",
                    groundRound,
                    relationNum,
                    battleRelationMap.keySet(),
                    toAddRelationMap.size(),
                    toAddRelationMap.keySet(),
                    getAdapter().allEntityIds(),
                    getAdapter().allEntityCount());
        } else {
            if (relationNum > 0) {
                LOGGER.info("BattleLog gemini_pref BattleGround tick num:{} {}", relationNum, roleNum);
            }
        }
        MonitorUnit.BATTLE_RELATION_NUM_GAUGE.labels(ServerContext.getBusId()).set(relationNum);
        MonitorUnit.BATTLE_ROLE_NUM_GAUGE.labels(ServerContext.getBusId()).set(roleNum);
    }

    /**
     * 将新增的战斗关系加入到战场中
     * <p>
     * 可重入
     */
    private void flushNewRelation() {
        if (toAddRelationMap.isEmpty()) {
            return;
        }
        safeFor("flushNewRelation", toAddRelationMap.values(), relation -> {
            if (!getAdapter().isBattleRoleExists(relation.getOne().getRoleId()) || !getAdapter().isBattleRoleExists(relation.getOther().getRoleId())) {
                checkOrRollbackRelation(relation);
                return;
            }
            // preAdd -> add
            addRelation(relation, true);
        });
        toAddRelationMap.clear();
    }

    /**
     * 回滚relation，主要用于relation未正常开战，需要回滚某些状态
     * 危险接口，谨慎使用
     */
    private void checkOrRollbackRelation(BattleRelation relation) {
        BattleRole one = relation.getOne();
        BattleRole other = relation.getOther();
        if (one == null || other == null) {
            LOGGER.error("BattleErrLog BattleGround checkOrRollbackRelation fail, relation={} one={} other={}", relation, one, other);
            return;
        }
        if (other.getRoleId() != 0 && one.getActiveTargetId() == other.getRoleId()) {
            LOGGER.info("BattleLog BattleGround checkOrRollbackRelation, rollback OneActiveTarget, relation={} one={} oldActiveTargetId={}", relation, one, one.getActiveTargetId());
            one.setActiveTargetId(0);
            one.clearToAddRelation(relation.getKey());
        }
        if (one.getRoleId() != 0 && other.getActiveTargetId() == one.getRoleId()) {
            LOGGER.info("BattleLog BattleGround checkOrRollbackRelation, rollback OneActiveTarget, relation={} other={} oldActiveTargetId={}", relation, other, other.getActiveTargetId());
            other.setActiveTargetId(0);
            other.clearToAddRelation(relation.getKey());
        }
    }

    public void forEachRole(String sign, Consumer<BattleRole> consumer) {
        safeFor(sign, activeRoleMap.values(), consumer);
    }

    public void forEachRelation(String sign, Consumer<BattleRelation> consumer) {
        safeFor(sign, battleRelationMap.values(), consumer);
    }

    /**
     * 适用于停服时强制结束所有战斗关系
     */
    public void forEachCopyRelationByStopServer(String sign, Consumer<BattleRelation> consumer) {
        safeFor(sign, new LinkedList<>(battleRelationMap.values()), consumer);
    }

    private <T> void safeFor(String sign, Collection<T> collection, Consumer<T> consumer) {
        for (T t : collection) {
            try {
                consumer.accept(t);
            } catch (Exception e) {
                if (isInSimulator()) {
                    throw e;
                } else {
                    WechatLog.error("BattleErrLog on {}, element={}", sign, t, e);
                }
            }
        }
    }

    public int getMapId() {
        return getAdapter().getMapId();
    }

    public long getPointMapId() {
        return pointMapId;
    }

    public CommonEnum.MapType getMapType() {
        return mapTypeSupplier.get();
    }

    public static BattleLogManager getBattleLog() {
        return battleLog;
    }

    public boolean isRoleAlive(long targetRoleId) {
        BattleRole role = activeRoleMap.get(targetRoleId);
        if (role == null) {
            return false;
        }
        return !role.isDead();
    }

    public void addTreatmentRole(BattleRole role, String reason) {
        if (role == null || role.getRoleId() <= 0) {
            LOGGER.error("BattleErrLog addTreatmentRole fail, role:{} reason={}", role, reason);
            return;
        }
        // 支持重入，仅作拦截不告警
        if (roleToTreatment.containsKey(role.getRoleId())) {
            return;
        }
        roleToTreatment.put(role.getRoleId(), role);
    }

    public int getGroundRound() {
        return groundRound;
    }

    public BattleTickContext getTickCtx() {
        return tickContext;
    }

    public boolean isInDungeon() {
        return isIn(BattleGroundType.DUNGEON);
    }

    public boolean needBattleRecord() {
        //不在副本里
        return !isInDungeon();
    }

    public boolean isInSpecificDungeon(CommonEnum.DungeonType dungeonType) {
        return isInDungeon() && this.dungeonType == dungeonType;
    }

    public boolean isInSimulator() {
        return isIn(BattleGroundType.SIMULATE);
    }

    public boolean isInBigScene() {
        return isIn(BattleGroundType.BIG_SCENE);
    }

    public boolean isIn(BattleGroundType type) {
        return this.type == type;
    }

    private void forceEndPendingPlunderRelation() {
        // 掠夺超时保底
        List<Long> toRemove = Lists.newArrayList();
        safeFor("forceEndPendingPlunderRelation", plunderRelations.entrySet(), entry -> {
            BattleRelation relation = entry.getValue();
            if (++relation.settleBattleRound > 6) {
                LOGGER.error("BattleErrLog plunder timeout FORCE END! pls figure out why! relation:{}, settleBattleRound:{}, one:{}, other:{}", relation, entry.getValue().settleBattleRound, relation.getOne(), relation.getOther());
                relation.handlePlunderResult(null);
                toRemove.add(entry.getKey());
            }
        });
        safeFor("forceEndPendingPlunderRelation_remove", toRemove, plunderRelations::remove);
    }

    public void tryActivateRole(BattleRole role, String reason) {
        if (role == null || role.getRoleId() <= 0) {
            LOGGER.error("BattleErrLog tryActivateRole fail, roleId < 0 ?, role:{}, reason={}", role, reason);
            return;
        }
        if (toAddRoleMap.containsKey(role.getRoleId())) {
            // 外围显式激活已存在的toAddRoleMap，是合理的，不报错了
            return;
        }
        if (activeRoleMap.containsKey(role.getRoleId())) {
            // 外围显式激活已存在的activeRoleMap，是合理的，不报错了
            return;
        }
        LOGGER.info("BattleLog to add role, round={} role={} reason={}", getGroundRound(), role, reason);
        toAddRoleMap.put(role.getRoleId(), role);
    }

    private void addRole(BattleRole role, String reason) {
        if (role == null || role.getRoleId() <= 0) {
            LOGGER.error("BattleErrLog addRole fail, roleId < 0 ?, role:{} reason={}", role, reason);
            return;
        }
        if (activeRoleMap.containsKey(role.getRoleId())) {
            // 外围显式激活已存在的activeRole，是合理的，不报错了
            return;
        }
        LOGGER.info("BattleLog add role, role={} reason={}", role, reason);
        activeRoleMap.put(role.getRoleId(), role);
    }

    private void flushNewRole() {
        if (toAddRoleMap.size() <= 0) {
            return;
        }
        safeFor("flushNewRole", toAddRoleMap.values(), role -> {
            // 大世界上没有这个BattleRole
            if (!getAdapter().isBattleRoleExists(role.getRoleId())) {
                LOGGER.error("BattleErrLog flushNewRole failed role not exists in scene={} round={} ", role.getRoleId(), getGroundRound());
                return;
            }
            // preAdd -> add
            addRole(role, "flushNewRole");
        });
        toAddRoleMap.clear();
    }

    /**
     * 召唤
     *
     * @param owner    施法者
     * @param skillId  构建信息
     * @param point
     * @param lifeTime
     * @return
     */
    public BattleRole createAreaSkill(BattleRole owner, int skillId, Point point, Integer lifeTime) {
        return getAdapter().createAreaSkill(owner, skillId, point, lifeTime);
    }

    public void addTickRole(BattleRole role) {
        if (role.isInTick()) {
            roleTickToAddSet.add(role);
            roleTickToRemoveSet.remove(role);
            return;
        }
        roleTickSet.add(role);
    }

    public void removeTickRole(BattleRole role) {
        if (role.isInTick()) {
            roleTickToRemoveSet.add(role);
            roleTickToAddSet.remove(role);
            return;
        }
        roleTickSet.remove(role);
    }

    private void buffTick() {
        Set<BattleRole> roleToRemove = Sets.newHashSet();
        safeFor("buffTick", roleTickSet, battleRole -> {
            if (battleRole.getAdapter().isDestroy() || !battleRole.getBuffHandler().needTick()) {
                roleToRemove.add(battleRole);
                return;
            }
            battleRole.setInTick(true);
            battleRole.getBuffHandler().onTick();
            battleRole.setInTick(false);
        });

        roleTickSet.addAll(roleTickToAddSet);
        roleTickSet.removeAll(roleTickToRemoveSet);
        roleTickToAddSet.clear();
        roleTickToRemoveSet.clear();

        roleTickSet.removeAll(roleToRemove);
    }

    public boolean isRoleActive(BattleRole role) {
        return activeRoleMap.containsKey(role.getRoleId()) || toAddRoleMap.containsKey(role.getRoleId());
    }

    public IBattleGroundAdapter getAdapter() {
        return adapter;
    }

    public boolean hasBeenConstructed(long one, long other) {
        if (hasPrepareBattleRelation(one, other)) {
            return true;
        }
        return hasBattleRelation(one, other);
    }

    /**
     * 构建战斗关系
     *
     * @param aim 是否是普攻开战；true：需要检查普攻距离，false：不检查普攻距离
     */
    public BattleRelation buildRelation(boolean aim, BattleRole one, BattleRole other, CommonEnum.BattleType battleType, BattleConstants.BattleRelationType type) {
        BattleRelation ret = null;
        // 1.在战斗过程中就不用加入战斗
        // 2.预备发生战斗的relation不重复添加
        BattleRelation relation = findBattleRelationOrNull(one.getRoleId(), other.getRoleId());
        BattleRelation prepareRelation = findPrepareBattleRelationOrNull(one.getRoleId(), other.getRoleId());

        // 包含已激活或者预激活战斗关系的情况下不需要重复构建
        if (hasActiveOrPrepareRelation(one, other, type, relation, prepareRelation)) {
            return relation != null ? relation : prepareRelation;
        }
        Point oneP = one.getAdapter().getCurPoint();
        Point otherP = other.getAdapter().getCurPoint();
        boolean isDistanceOk = BattleHelper.isBattleDistanceOk(one, other);
        //距离超过普攻的距离，但技能能打到的情况
        if ((!aim) || isDistanceOk) {
            LOGGER.debug("BattleLog start battle,one={},other={}", one, other);
            if (one.getAdapter().canReady() && other.getAdapter().canReady()) {
                one.getAdapter().ready(other.getAdapter());
                other.getAdapter().ready(one.getAdapter());
            } else {
                return null;
            }
            ret = new BattleRelation(this, otherP, battleType, one, other, type);
            if (aim) {
                one.setActiveTargetId(other.getRoleId());
                other.getSiegeHandler().addSiege(one, ret, true);
            }
            LOGGER.info("BattleLog create battle, relation={} one={}, other={}, battleType={} type={} aim={}", ret, one, other, battleType, type, aim);
            preAddRelation(ret);
            one.getAdapter().onAddBattleRelation(ret);
            other.getAdapter().onAddBattleRelation(ret);
        } else {
            int battleDistance = BattleHelper.getBattleDistance(one, other);
            double distance = Point.calDisBetweenTwoPoint(oneP, otherP);
            LOGGER.warn("BattleLog attack distance is too far,battleDistance={},distance={},one={},other={}", battleDistance, distance, one, other);
        }
        return ret;
    }


    /**
     * 包含已激活或者预激活战斗关系的情况下不需要重复构建
     * 如果已有已激活relation，会强制修改type和status
     */
    private boolean hasActiveOrPrepareRelation(BattleRole one, BattleRole other, BattleConstants.BattleRelationType type, BattleRelation relation, BattleRelation prepareRelation) {
        if (relation == null && prepareRelation == null) {
            return false;
        }
        if (prepareRelation != null) {
            one.setActiveTargetId(other.getRoleId());
            return true;
        }
        if (relation.getStatus() == BattleConstants.BattleRelationStatus.Stop) {
            return true;
        }
        // 把挂起中的被动战斗关系变成主动
        if (type == BattleConstants.BattleRelationType.Active) {
            LOGGER.info("BattleLog change relation status, relation={} oldState={} battleRelationType={}", relation, relation.getStatus(), type);
            relation.setStatus(BattleConstants.BattleRelationStatus.Running);
            if (relation.getType() != type) {
                relation.setType(type);
            }
            other.getSiegeHandler().addSiege(one, relation, false);
        }
        one.setActiveTargetId(other.getRoleId());
        return true;
    }

    /**
     * 主动进攻而开战
     */
    public BattleRelation tryStartBattleWith(BattleRole attacker, BattleRole target) {
        ErrorCode code = attacker.getAdapter().canBattleWithCode(target, false);
        if (code.isNotOk()) {
            LOGGER.warn("BattleErrLog tryStartBattleWith failed, attacker:{}, defender:{} can not battle. code:{}", attacker, target, code);
            return null;
        }
        CommonEnum.BattleType battleType = defaultBattleTypeWith(attacker.getType(), target.getType());
        if (battleType == null) {
            LOGGER.error("BattleErrLog tryStartBattleWith failed, attacker:{}, defender:{} unsupported battleType.", attacker, target);
            return null;
        }
        return buildRelation(true, attacker, target, battleType, BattleConstants.BattleRelationType.Active);
    }

    /**
     * 被AOE技能打到而开战
     */
    public Pair<BattleRelation, ErrorCode> tryStartBattleByDamage(BattleRole attacker, BattleRole target, boolean dot) {
        BattleRelation relation = attacker.relationWith(target.getRoleId());
        if (relation != null) {
            return Pair.of(relation, ErrorCode.OK);
        }

        ErrorCode code = attacker.getAdapter().canBattleWithCode(target, false);
        if (code.isNotOk()) {
            LOGGER.error("BattleErrLog tryStartBattleByDamage failed, attacker:{}, defender:{} can not battle. code:{}", attacker, target, code);
            return Pair.of(null, code);
        }
        CommonEnum.BattleType battleType = defaultBattleTypeWith(attacker.getType(), target.getType());
        if (battleType == null) {
            LOGGER.error("BattleErrLog tryStartBattleByDamage failed, attacker:{}, defender:{} unsupported battleType.", attacker, target);
            return Pair.of(null, code);
        }
        BattleConstants.BattleRelationType type = BattleConstants.BattleRelationType.Passive;
        if (target.getAdapter().getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
            type = BattleConstants.BattleRelationType.Active;
        } else if (attacker.getAdapter().getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill) {
            type = BattleConstants.BattleRelationType.AreaSkill;
        } else if (attacker.getTargetRole() != null && attacker.getTargetRole().getAdapter().getPlayerId() == target.getAdapter().getPlayerId()) {
            // 当玩家有其他部队正在被进攻方锤的时候，当作主动进攻
            type = BattleConstants.BattleRelationType.Active;
        }
        relation = buildRelation(false, attacker, target, battleType, type);
        LOGGER.info("BattleLog tryStartBattleByDamage, relation={} type={} dot={}", relation, type, dot);
        // AOE要造成伤害需要有完整relation，所以这里提前手动刷出一个完整relation(目前需要6步、最后一步在action后面)
        if (relation != null) {
            // AOE伤害1、删除PreRelations，添加到战斗Relations中
            toAddRelationMap.remove(relation.getKey());
            addRelation(relation, false);
            // AOE伤害2、添加PreActiveRoles，并保证结算前删除Pre并添加到ActiveRoles
            tryActivateRole(attacker, "Passive " + dot);
            tryActivateRole(target, "Passive " + dot);
            // AOE伤害3、设置一下开启标记
            relation.prepareBeforeAction(tickContext);
            // AOE伤害4、手动触发战斗开始触发器
            attacker.tryTriggerBattleStart();
            target.tryTriggerBattleStart();
            // AOE伤害5、刷新快照
            attacker.refreshNewRoundSnapShot();
            target.refreshNewRoundSnapShot();
        }
        return Pair.of(relation, ErrorCode.OK);
    }

    /**
     * 技能主动开战，AI在用
     */
    public BattleRelation tryStartBattleBySkill(BattleRole attacker, BattleRole target) {
        BattleRelation relation = attacker.relationWith(target.getRoleId());
        if (relation != null) {
            return relation;
        }

        ErrorCode code = attacker.getAdapter().canBattleWithCode(target, false);
        if (code.isNotOk()) {
            LOGGER.warn("BattleErrLog tryStartBattleBySkill failed, attacker:{}, defender:{} can not battle. code:{}", attacker, target, code);
            return null;
        }
        CommonEnum.BattleType battleType = defaultBattleTypeWith(attacker.getType(), target.getType());
        if (battleType == null) {
            LOGGER.error("BattleErrLog tryStartBattleBySkill failed, attacker:{}, defender:{} unsupported battleType.", attacker, target);
            return null;
        }
        BattleConstants.BattleRelationType type = BattleConstants.BattleRelationType.Passive;
        if (target.getAdapter().getEntityType() == EntityAttrOuterClass.EntityType.ET_Monster) {
            type = BattleConstants.BattleRelationType.Active;
        } else if (attacker.getAdapter().getEntityType() == EntityAttrOuterClass.EntityType.ET_AreaSkill) {
            type = BattleConstants.BattleRelationType.AreaSkill;
        }
        relation = buildRelation(false, attacker, target, battleType, type);
        LOGGER.info("BattleLog tryStartBattleByAi, relation={} one={} other={}", relation, attacker, target);
        return relation;
    }

    public boolean hasTreatmentRole(long roleId) {
        if (roleId <= 0) {
            LOGGER.error("BattleErrLog hasTreatmentRole fail, roleId <= 0");
            return false;
        }
        return roleToTreatment.containsKey(roleId);
    }

    public void removeTreatmentRole(long roleId) {
        roleToTreatment.remove(roleId);
    }

    class ParallelTask extends RecursiveAction {
        private final List<Long> roles;
        private final Consumer<BattleRole> consumer;
        private final String sign;

        public ParallelTask(List<Long> roles, Consumer<BattleRole> consumer, String sign) {
            this.roles = roles;
            this.consumer = consumer;
            this.sign = sign;
        }

        @Override
        protected void compute() {
            if (roles.size() <= PARALLEL_ENABLE_NUM) {
                safeFor(sign, roles, roleId -> consumer.accept(activeRoleMap.get(roleId)));
            } else {
                int m = roles.size() / 2;
                ParallelTask left = new ParallelTask(roles.subList(0, m), consumer, sign);
                ParallelTask right = new ParallelTask(roles.subList(m, roles.size()), consumer, sign);
                invokeAll(left, right);
            }
        }
    }

    public void forEachRoleParallel(String sign, Consumer<BattleRole> consumer) {
        if (forkJoinPool != null) {
            if (activeRoleMap.isEmpty()) {
                return;
            }
            List<Long> collect = new ArrayList<>(activeRoleMap.keySet());
            forkJoinPool.invoke(new ParallelTask(collect.subList(0, collect.size()), consumer, sign));
        } else {
            forEachRole(sign, consumer);
        }
    }

    public void openParallel() {
        if (forkJoinPool != null) {
            LOGGER.error("BattleErrLog openParallel fail, forkJoinPool already");
            return;
        }
        LOGGER.info("BattleLog openParallel, type={} parallelism={}", type, PARALLELISM);
        forkJoinPool = ConcurrentHelper.newForkJoinPool("battle-compute-", PARALLELISM);
    }

    public void closeParallel(String reason) {
        if (forkJoinPool == null) {
            LOGGER.error("BattleErrLog closeParallel fail, forkJoinPool already close reason={}", reason);
            return;
        }
        // 断引用
        ForkJoinPool forkJoinPoolTemp = forkJoinPool;
        forkJoinPool = null;
        LOGGER.info("BattleLog closeParallel, type={} parallelism={} reason={}", type, PARALLELISM, reason);

        // 后续保证释放完毕
        try {
            forkJoinPoolTemp.shutdown();
            if (!forkJoinPoolTemp.awaitTermination(5, TimeUnit.SECONDS)) {
                // 在等待一定时间后，线程池的任务还未全部执行完毕
                forkJoinPoolTemp.shutdownNow();// 强制终止线程池
                LOGGER.error("BattleErrLog closeParallel timeout");
            }
        } catch (InterruptedException e) {
            forkJoinPoolTemp.shutdownNow(); // 当前线程被中断时，强制终止线程池
            LOGGER.error("BattleErrLog closeParallel fail");
        }
    }

    public void snapshot() {
        forEachRoleParallel("refreshSnapshot", BattleRole::refreshNewRoundSnapShot);
    }

    public void action() {
        forEachRole("action", role -> role.tryAction(tickContext));

        // action过程中会产生新的relation
        // （由于造成伤害需要relation，所以产生新的relation需要实时刷新，但是activeRole无法并发添加，所以在结算前手动调一下）
        // AOE伤害6、移除PreActiveRoles并添加到ActiveRoles
        flushNewRole();
    }

    public void settle() {
        // 遍历战场上所有关系，每一个单位结算受到的效果，”同时“收到伤害、治疗、buff
        forEachRoleParallel("settle1", role -> role.getParallelSettleHandler().settleRound(tickContext));
        tickContext.watchForOnTick.mark("settle1_parallel");
        forEachRole("settle2", role -> role.getSettleHandler().afterSettleRound(tickContext));
        tickContext.watchForOnTick.mark("settle2");
        forEachRoleParallel("settle3", role -> role.getParallelSettleHandler().settleKill(tickContext));
        tickContext.watchForOnTick.mark("settle3_parallel");
    }

    public void removeToAddRole(BattleRole role, String reason) {
        if (toAddRoleMap.remove(role.getRoleId()) != null) {
            LOGGER.info("BattleLog remove to add role, round={} role={} reason={}", getGroundRound(), role, reason);
        }
    }

    public boolean isOnTick() {
        return isOnTick;
    }
}

package com.yorha.cnc.battle.skill.task;

import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.common.constant.Constants;

/**
 * 延时任务
 *
 * <AUTHOR>
 */
public abstract class DelayTask implements Task {
    int runRound;

    public DelayTask(BattleTickContext tickContext, long delayMillis) {
        runRound = tickContext.getGroundRound() + (int) delayMillis / Constants.N_1000;
    }

    public int getRunRound() {
        return runRound;
    }
}

package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.common.Amend;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.context.TreatmentContext;
import com.yorha.cnc.battle.core.BattleFormula;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.common.constant.BattleConstants;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 治疗
 *
 * <AUTHOR> Jiang
 */
public class TreatmentValue extends AbstractSkillEffectValue {

    public TreatmentValue() {
        super(SkillEffectType.SET_TREATMENT);
    }

    /**
     * 向下取整
     * 总治疗量 = 参加部队数^部队数量计算次方 * 技能系数 * 治疗修正系数
     * 根据部队组成，分摊至不同ID的士兵恢复量（同伤害分摊规则）
     * 治疗量不超过本次战斗中的轻伤总量
     * 部队数量计算次方（battle_constant -> 5），技能系数（effect value），治疗修正系数（battle_constant -> 12）*部队的平均攻击力
     */
    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext,
                                              ActionContext actionCtx,
                                              SkillEffectTemplate template,
                                              BattleRole attacker,
                                              BattleRole target,
                                              EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        Amend baseAmend = getBaseAmend(actionCtx.getTreatmentAmend(target.getRoleId()), template.getValue1());
        int treatmentValue = 0;
        // 目标禁疗
        boolean isJinLiao = target.getStateHandler().isinState(BattleConstants.BattleRoleState.NO_TREATMENT);
        if (!isJinLiao) {
            BattleRoleSnapshot attackerSnapshot = getTreatmentAttackSnapshot(attacker, target, effectContext, template.getValue2());
            TreatmentContext treatmentCtx = BattleFormula.stdApplyTreatment(attackerSnapshot, target.getSnapShot("DefTreatment"), actionCtx, effectContext, baseAmend);
            treatmentValue = treatmentCtx.getTotalTreat();
            if (treatmentValue > 0) {
                target.getContext().addTreatmentCtx(treatmentCtx);
                target.getGround().addTreatmentRole(target, "TreatmentValue " + effectContext.isDot());
            }
        }

        if (treatmentValue > 0 || isJinLiao) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(getType());
            builder.setValue(treatmentValue);
            builder.setEffectId(template.getId());
            ret.add(builder.build());
            // 受到治疗触发器
            SkillSystem.trigger(target, CommonEnum.TriggerType.TT_BE_TREATED);
        }
        return ret;
    }
}

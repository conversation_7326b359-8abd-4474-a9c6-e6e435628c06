package com.yorha.cnc.battle.snapshot;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.core.BattleFormula;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.snapshot.group.SoldierGroupSnapshot;
import com.yorha.cnc.battle.snapshot.unit.GuardTowerSnapshot;
import com.yorha.cnc.battle.snapshot.unit.SoldierSnapshot;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.battle.soldier.SoldierGroup;
import com.yorha.common.constant.BattleConstants;
import com.yorha.proto.CommonEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class BattleRoleSnapshot {

    private final Map<Integer, SoldierGroupSnapshot> soldierGroupMap;
    private final GuardTowerSnapshot guardTower;

    private final BattleRole role;
    private final long roleId;
    private final CommonEnum.SceneObjType type;
    private final int rallyCapacity;
    private final double soldierCountRatio;
    private final boolean isInDungeon;
    private final long roleTypeId;
    private final int pincerAttackerNum;
    private final boolean isRally;
    private final boolean isInvincible;
    private final boolean isMonster;
    private final Map<CommonEnum.BuffEffectType, Long> additionMap;
    private final int snapshotRound;
    private final boolean isBelongToNpc;
    /**
     * 攻击力占比 <roleId, ratio>
     */
    private final Map<Long, Double> atkRatioMap;

    public BattleRoleSnapshot(BattleRole role) {
        this.role = role;
        this.roleId = role.getRoleId();
        this.type = role.getType();
        this.rallyCapacity = role.getRallyCapacity();
        this.soldierCountRatio = BattleFormula.soldierCountRatio(role);
        this.isInDungeon = role.isInDungeon();
        this.isRally = role.getAdapter().isRally();
        this.isBelongToNpc = role.isBelongsToNpc();
        this.pincerAttackerNum = role.getPincerAttackerNum();
        this.roleTypeId = role.getAdapter().getRoleTypeId();
        this.soldierGroupMap = Maps.newHashMap();

        this.isInvincible = role.getStateHandler().isinState(BattleConstants.BattleRoleState.INVINCIBLE);
        this.isMonster = role.isMonsterObj();

        // 前BUFF相关
        if (role.getAdapter().getAdditionAdapter() != null) {
            this.additionMap = role.getAdapter().getAdditionAdapter().getAllAdditions();
        } else {
            this.additionMap = Maps.newHashMap();
        }

        // 刷一下快照，依赖上面的加成快照
        initSoldierSnapshot(role);
        this.guardTower = role.getGuardTower() != null ? new GuardTowerSnapshot(role, role.getGuardTower(), this.additionMap) : null;

        // 前加成相关
        this.snapshotRound = role.getGround().getGroundRound();
        this.atkRatioMap = calcAtkRatio(role, this.additionMap);
    }

    private void initSoldierSnapshot(BattleRole role) {
        for (Map.Entry<Integer, SoldierGroup> entry : role.getGroupMap().entrySet()) {
            List<SoldierSnapshot> soldiers = Lists.newArrayList();
            for (Soldier soldier : entry.getValue().getSoldiers()) {
                if (soldier.aliveCount() <= 0) {
                    continue;
                }
                soldiers.add(new SoldierSnapshot(role, soldier, this.additionMap));
            }
            if (soldiers.isEmpty()) {
                continue;
            }
            this.soldierGroupMap.put(entry.getKey(), new SoldierGroupSnapshot(entry.getKey(), soldiers));
        }
    }

    public long getFinalBuffValue(CommonEnum.BuffEffectType buff) {
        return additionMap.getOrDefault(buff, 0L);
    }

    public long getRoleId() {
        return this.roleId;
    }

    public int aliveCount() {
        return soldierAliveCount() + getGuardTowerAliveCount();
    }

    public int getRallyCapacity() {
        return rallyCapacity;
    }

    public double soldierCountRatio() {
        return soldierCountRatio;
    }

    public boolean isInDungeon() {
        return isInDungeon;
    }

    public boolean isBelongToNpc() {
        return isBelongToNpc;
    }

    public long getRoleTypeId() {
        return roleTypeId;
    }

    public int getPincerAttackerNum() {
        return pincerAttackerNum;
    }

    public boolean isRally() {
        return isRally;
    }

    public double getSoldierAvgAtk() {
        return getSoldierTotalAtk() / soldierAliveCount();
    }

    public List<SoldierSnapshot> getSoldierListWithSort() {
        List<SoldierSnapshot> ret = new ArrayList<>();
        for (SoldierGroupSnapshot group : soldierGroupMap.values()) {
            ret.addAll(group.getSoldiers());
        }
        Collections.sort(ret);
        return ret;
    }

    public Map<Integer, SoldierGroupSnapshot> getGroupSnapshotMap() {
        return soldierGroupMap;
    }

    public int soldierAliveCount() {
        return this.soldierGroupMap.values().stream().mapToInt(SoldierGroupSnapshot::aliveCount).sum();
    }

    public double getSoldierTotalAtk() {
        return this.soldierGroupMap.values().stream().mapToDouble(SoldierGroupSnapshot::getTotalAtk).sum();
    }

    public double getSoldierTotalDefence() {
        return this.soldierGroupMap.values().stream().mapToDouble(SoldierGroupSnapshot::getTotalDefence).sum();
    }

    public int getGuardTowerAliveCount() {
        if (guardTower != null) {
            return guardTower.aliveCount();
        }
        return 0;
    }

    public GuardTowerSnapshot getGuardTower() {
        return guardTower;
    }

    public double getGuardTowerTotalAtk() {
        if (guardTower == null) {
            return 0;
        }
        return guardTower.calcTotalAttack();
    }

    public double getGuardTowerTotalDefence() {
        if (guardTower == null) {
            return 0;
        }
        return guardTower.calcTotalDefence();
    }

    public double getGuardRatio() {
        if (guardTower == null) {
            return 0;
        }
        return guardTower.getGuardRatio();
    }

    /**
     * 部队的平均攻击
     */
    public double calcAverageAttack() {
        return calcTotalAttack() / aliveCount();
    }

    /**
     * 部队的平均防御
     */
    public double calcAverageDefence() {
        return calcTotalDefence() / aliveCount();
    }

    /**
     * 部队的总攻击
     */
    private double calcTotalAttack() {
        return getSoldierTotalAtk() + getGuardTowerTotalAtk();
    }

    /**
     * 部队的总防御
     */
    private double calcTotalDefence() {
        return getSoldierTotalDefence() + getGuardTowerTotalDefence();
    }

    public boolean isGuardTowerAlive() {
        return getGuardTowerAliveCount() > 0;
    }

    public CommonEnum.SceneObjType getType() {
        return type;
    }

    public boolean isInvincible() {
        return isInvincible;
    }

    public boolean isMonster() {
        return isMonster;
    }

    /*==基础相关==*/
    public int getSnapshotRound() {
        return snapshotRound;
    }

    public Map<Long, Double> getAtkRatioMap() {
        return atkRatioMap;
    }

    private Map<Long, Double> calcAtkRatio(BattleRole role, Map<CommonEnum.BuffEffectType, Long> additionMap) {
        Map<Long, Double> ret = Maps.newHashMap();
        double totalAtk = calcTotalAttack();
        if (totalAtk <= 0) {
            return ret;
        }
        for (Long childRoleId : role.getAdapter().getAllChildRoleIdList()) {
            double memberAtk = calcTotalAttackByRoleId(role, childRoleId, additionMap);
            double atkRatio = memberAtk / totalAtk;
            ret.put(childRoleId, atkRatio);
        }
        return ret;
    }

    private double calcTotalAttackByRoleId(BattleRole role, long memberRoleId, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        double ret = 0;
        for (SoldierGroupSnapshot e : soldierGroupMap.values()) {
            for (SoldierSnapshot soldier : e.getSoldiers()) {
                ret += soldier.calcTotalAttackByRoleId(role, memberRoleId, roleAdditionSnapshot);
            }
        }
        if (memberRoleId == role.getAdapter().getLeaderRoleId() && isGuardTowerAlive()) {
            ret += guardTower.calcTotalAttack();
        }
        return ret;
    }

    /**
     * 严禁业务使用，仅用于日志打印！！快照中不应该有透传数据
     */
    public double getShieldValue(){
        return role.getBuffHandler().getBuffValue(CommonEnum.BuffEffectType.ET_SHIELD_BUF);
    }

    /**
     * 严禁业务使用，仅用于日志打印！！快照中不应该有透传数据
     */
    public BattleRole getRole() {
        return role;
    }

    @Override
    public String toString() {
        return "BattleRoleSnapshot:[" + type + "-" + roleId + "]";
    }

    public String toLogString() {
        return "快照信息 " + role.getRoleId() +
                " {士兵=" + soldierGroupMap +
                ", 防御塔=" + guardTower +
                ", 类型=" + type +
                ", 集结容量=" + rallyCapacity +
                ", 兵力系数=" + soldierCountRatio +
                ", 是否属于NPC部队=" + isBelongToNpc +
                ", 夹击数=" + pincerAttackerNum +
                ", 是野怪=" + isMonster +
                ", 加成详情=" + additionMap +
                ", 回合数=" + snapshotRound +
                ", 攻击力占比=" + atkRatioMap +
                '}';
    }
}

package com.yorha.cnc.battle.context;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.common.ActionType;

import java.util.Map;

/**
 * 一次伤害的上下文
 *
 * <AUTHOR>
 */
public class DamageContext {

    /**
     * 这次伤害的出手
     */
    private final ActionContext actionCtx;
    /**
     * 技能释放者数据，只有是技能伤害时才有值，普攻为null
     */
    private final EffectContext effectContext;
    /**
     * 伤害造成的兵损 key：soldierId
     */
    private final Map<Integer, Integer> lossMap;
    private final Map<Integer, Double> damageCache;
    private final double battleScale;
    /**
     * 护盾兵损抵扣
     */
    private int decreaseLoss;
    private final Map<Integer, Double> decreaseDamageCache;

    public DamageContext(ActionContext actionCtx, EffectContext effectContext, double battleScale) {
        this.actionCtx = actionCtx;
        this.effectContext = effectContext;
        this.lossMap = Maps.newHashMap();
        this.damageCache = Maps.newHashMap();
        this.decreaseDamageCache = Maps.newHashMap();
        this.battleScale = battleScale;
    }

    public boolean isOrdinaryAttackOrBack() {
        return actionCtx.getType() == ActionType.ATTACK_BACK || actionCtx.getType() == ActionType.ORDINARY_ATTACK;
    }

    public boolean isSkill() {
        return actionCtx.getType() == ActionType.SKILL;
    }

    public ActionContext getActionCtx() {
        return actionCtx;
    }

    public EffectContext getEffectContext() {
        return effectContext;
    }

    public double getBattleScale() {
        return battleScale;
    }

    public int getTotalLoss() {
        int sum = 0;
        for (Integer value : lossMap.values()) {
            sum += value;
        }
        return sum;
    }

    public long getAttackerId() {
        return actionCtx.getAttackerId();
    }

    public void putLoss(int soldierId, int loss) {
        lossMap.put(soldierId, loss);
    }

    public Map<Integer, Integer> getLossMap() {
        return lossMap;
    }

    public void addCacheDamage(int soldierId, double damage) {
        if (damage <= 0) {
            return;
        }
        damageCache.put(soldierId, damageCache.getOrDefault(soldierId, 0.0) + damage);
    }

    public boolean isDOT() {
        return effectContext != null && effectContext.isDot();
    }

    public double getDamageCache(int soldierId) {
        return damageCache.getOrDefault(soldierId, 0.0);
    }

    public double getDecreaseDamage() {
        return decreaseDamageCache.values().stream().mapToDouble(it -> it).sum();
    }

    public double getDecreaseDamageCache(int soldierId) {
        return decreaseDamageCache.getOrDefault(soldierId, 0.0);
    }

    public void addCacheDecreaseDamage(int soldierId, double decreaseDamage) {
        if (decreaseDamage <= 0) {
            return;
        }
        decreaseDamageCache.put(soldierId, decreaseDamageCache.getOrDefault(soldierId, 0.0) + decreaseDamage);
    }

    public void putDecreaseLoss(int decreaseLoss) {
        this.decreaseLoss += decreaseLoss;
    }

    public int getDecreaseLoss() {
        return decreaseLoss;
    }

    @Override
    public String toString() {
        return "DamageContext{" +
                "actionCtx=" + actionCtx +
                ", effectContext=" + effectContext +
                ", lossMap=" + lossMap +
                ", decreaseDamageCache=" + decreaseDamageCache +
                ", decreaseLoss=" + decreaseLoss +
                '}';
    }
}

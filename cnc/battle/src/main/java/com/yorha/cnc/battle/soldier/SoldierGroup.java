package com.yorha.cnc.battle.soldier;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * 同一兵种的组
 *
 * <AUTHOR>
 */
public class SoldierGroup {

    private final BattleRole battleRole;
    private final int type;
    /**
     * 当前兵种组中不同阶的士兵组成
     */
    private final List<Soldier> soldiers;

    public SoldierGroup(BattleRole battleRole, int type) {
        this.battleRole = battleRole;
        this.type = type;
        this.soldiers = Lists.newArrayList();
    }

    @Nullable
    private Soldier find(int soldierId) {
        for (Soldier soldier : soldiers) {
            if (soldier.getId() == soldierId) {
                return soldier;
            }
        }
        return null;
    }

    private Soldier addSoldier(int soldierId) {
        Soldier soldier = new Soldier(this.battleRole, soldierId);
        this.soldiers.add(soldier);
        return soldier;
    }

    public int getType() {
        return type;
    }

    public List<Soldier> getSoldiers() {
        return soldiers;
    }

    /**
     * 获取一种兵的总存活数量
     */
    public int aliveCount() {
        int ret = 0;
        for (Soldier e : soldiers) {
            ret += e.aliveCount();
        }
        return ret;
    }

    /**
     * 获取一种兵的总存活数量
     */
    public int tempAliveCount() {
        int ret = 0;
        for (Soldier e : soldiers) {
            ret += e.tempAliveCount();
        }
        return ret;
    }

    /**
     * 获取每个成员一种兵的总存活数量
     *
     * @return <roleId, <soldierId, count>>
     */
    public Map<Long, Map<Integer, Integer>> aliveCountByMember() {
        Map<Long, Map<Integer, Integer>> ret = Maps.newHashMap();
        for (Soldier e : soldiers) {
            for (Map.Entry<Long, Integer> entry : e.aliveCountByMember().entrySet()) {
                ret.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap()).put(e.getId(), entry.getValue());
            }
        }
        return ret;
    }

    /**
     * 获取一种兵的最大数量
     */
    public int getSoldierMax() {
        return soldiers.stream().mapToInt(Soldier::getMax).sum();
    }

    public void recover() {
        for (Soldier e : soldiers) {
            e.recover();
        }
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public void addSoldier4ExistUnit(BattleRole childRole, int soldierId, int addNum) {
        Soldier soldier = find(soldierId);
        if (soldier == null) {
            WechatLog.error("BattleErrLog addSoldier4ExistUnit soldier not exist! {} {} {}", childRole, soldierId, addNum);
            return;
        }
        soldier.add4ExistUnit(childRole, addNum);
    }

    public void addSoldierUnit(SoldierUnit.OwnerInfo childOwnerInfo, SoldierProp prop) {
        Soldier soldier = find(prop.getSoldierId());
        if (soldier == null) {
            soldier = addSoldier(prop.getSoldierId());
        }
        soldier.addUnit(childOwnerInfo, SoldierData.fromProp(prop));
    }

    public void dropSoldierChild(long childRoleId, int soldierId) {
        Soldier soldier = find(soldierId);
        if (soldier == null) {
            WechatLog.error("BattleErrLog dropSoldierChild Soldier not init.{}", soldierId);
            return;
        }
        soldier.dropUnit(childRoleId, true);
    }

    public void dropSoldierChild(long roleId) {
        for (Soldier soldier : soldiers) {
            soldier.dropUnit(roleId, false);
        }
    }
}

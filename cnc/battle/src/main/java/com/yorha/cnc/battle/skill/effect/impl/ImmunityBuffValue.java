package com.yorha.cnc.battle.skill.effect.impl;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.cnc.battle.skill.status.impl.GetBuffChecker;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 免疫buff
 *
 * <AUTHOR>
 */
public class ImmunityBuffValue extends AbstractSkillEffectValue {

    private ImmunityBuffValue() {
        super(CommonEnum.SkillEffectType.SET_IMMUNITY);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        return new ArrayList<>();
    }

    public static boolean isImmunized(BattleRole attacker, BattleRole target, SkillEffect skillEffect, int buffId) {
        return skillEffect.canTrigger(attacker, target)
                && GetBuffChecker.isTargetBuff(Lists.newArrayList(buffId), skillEffect.getTemplate().getValue1(), skillEffect.getTemplate().getValue2());
    }
}

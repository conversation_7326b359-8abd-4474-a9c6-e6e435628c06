package com.yorha.cnc.battle.context;

import com.google.common.collect.Maps;


import java.util.Map;

/**
 * 单次治疗的上下文
 *
 * <AUTHOR>
 */
public class TreatmentContext {
    private final ActionContext actionCtx;
    /**
     * 治疗了多少士兵
     */
    private final Map<Integer, Integer> treatMap;
    /**
     * 技能信息
     */
    private final EffectContext effectContext;

    public TreatmentContext(ActionContext actionCtx, EffectContext effectContext) {
        this.actionCtx = actionCtx;
        this.effectContext = effectContext;
        this.treatMap = Maps.newHashMap();
    }

    public Map<Integer, Integer> getTreatMap() {
        return treatMap;
    }

    public ActionContext getActionCtx() {
        return actionCtx;
    }

    public EffectContext getExecutorInfo() {
        return effectContext;
    }

    public int getTotalTreat() {
        int sum = 0;
        for (Integer value : treatMap.values()) {
            sum += value;
        }
        return sum;
    }

    public void addTreatValue(int soldierId, int value) {
        treatMap.put(soldierId, value);
    }
}

package com.yorha.cnc.battle.skill;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.proto.CommonEnum;

import java.util.List;
import java.util.Map;

/**
 * 技能效果容器
 *
 * <AUTHOR>
 */
public abstract class SkillEffectContainer {

    /**
     * 技能效果
     */
    private final Map<CommonEnum.TriggerType, List<SkillEffect>> effectsMap = Maps.newHashMap();

    /**
     * 初始化技能效果
     */
    abstract protected void initEffects();

    protected void addEffect(int effectId, EffectContext effectContext) {
        SkillEffect effect = SkillEffect.create(effectId, effectContext);
        if (effect != null) {
            effectsMap.computeIfAbsent(effect.getTemplate().getTrigger(), v -> Lists.newArrayList()).add(effect);
        }
    }

    /**
     * 获取技能效果
     */
    public List<SkillEffect> getEffects(CommonEnum.TriggerType triggerType) {
        return effectsMap.getOrDefault(triggerType, Lists.newArrayList());
    }

    public void clear() {
        for (List<SkillEffect> value : effectsMap.values()) {
            for (SkillEffect effect : value) {
                effect.clear();
            }
        }
    }

    public boolean hasEffect() {
        return !effectsMap.isEmpty();
    }
}

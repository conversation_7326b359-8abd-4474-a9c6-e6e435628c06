package com.yorha.cnc.battle.buf.lifecycle.impl;

import com.yorha.cnc.battle.buf.lifecycle.ILifeCycle;
import com.yorha.proto.CommonEnum.LifeCycleType;

/**
 * 永久有效周期
 *
 * <AUTHOR>
 */
public class AllLifeCycle implements ILifeCycle {
    private LifeCycleType type;

    public AllLifeCycle(LifeCycleType type) {
        this.type = type;
    }

    @Override
    public boolean checkValid() {
        return true;
    }

    @Override
    public LifeCycleType getType() {
        return type;
    }

    @Override
    public long getValue() {
        return 0;
    }

    @Override
    public void execute() {

    }

    @Override
    public String getMessage() {
        return "一直有效";
    }

    @Override
    public void reset() {

    }
}

package com.yorha.cnc.battle.skill.filter.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.filter.ISkillEffectRelationFilter;
import com.yorha.cnc.battle.skill.filter.SkillFilterHelper;

import java.util.Set;

/**
 * 敌方
 * 有仇恨判断
 */
public class EnemyFilter implements ISkillEffectRelationFilter {

    @Override
    public Set<BattleRole> filter(SkillEffect effect, BattleRole castObj, Set<BattleRole> target, BattleRole castTargetObj) {
        Set<BattleRole> ret;
        // 可战斗筛选
        ret = SkillFilterHelper.filterByCanBattle(castObj, target);
        if (castObj.isNpcTroop()
                || castObj.isInSimulator()
                || castObj.isInDungeon()
                || effect.isDot()) {
            return ret;
        }
        // 仇恨筛选，只有大世界上的普通伤害才需要判断，dot不用
        ret = SkillFilterHelper.filterByHate(castObj, ret);
        return ret;
    }
}

package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.yorha.common.constant.BattleConstants.*;

/**
 * 移除buff
 *
 * <AUTHOR>
 */
public class DisperseValue extends AbstractSkillEffectValue {

    public DisperseValue() {
        super(SkillEffectType.SET_DISPERSE);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        int type = template.getValue1();
        int v = template.getValue2();
        boolean result = false;
        switch (type) {
            case BUFF_TAG:
                result = target.getBuffHandler().removeBuffByTag(v);
                break;
            case BUFF_GROUP_ID:
                result = target.getBuffHandler().removeBuffByGroupId(v);
                break;
            case BUFF_ID:
                result = target.getBuffHandler().removeBuffById(v);
                break;
            default:
                break;
        }
        if (result) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(getType());
            builder.setValue(template.getId());
            builder.setEffectId(template.getId());
            ret.add(builder.build());
        }
        return ret;
    }

}

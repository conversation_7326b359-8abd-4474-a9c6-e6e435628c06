package com.yorha.cnc.battle.record;

import com.github.luben.zstd.Zstd;
import com.github.luben.zstd.ZstdException;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.yorha.cnc.battle.context.BattleRelationContext;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * 只记录战斗的前100回合，后100回合。最多存两页，每页最多100回合
 */
public class BattleLogService {
    private static final Logger LOGGER = LogManager.getLogger(BattleLogService.class);

    private final BattleRelationContext ctx;
    private final long relationId;
    private final long logId;
    /**
     * 每X回合存盘一次日志
     */
    private final int SAVE_LOG_PER_ROUND = 100;
    /**
     * 当前日志页
     */
    private int logPage;
    /**
     * 回合日志，每次打包后都会清空
     */
    private final LinkedList<CommonBattle.BattleRound> roundLogList;

    public BattleLogService(BattleRelationContext ctx) {
        this.ctx = ctx;
        this.relationId = ctx.getRelation().getId();
        this.logId = IdFactory.nextId("battle_log");
        this.roundLogList = Lists.newLinkedList();
    }

    /**
     * 每X回合打包一次日志
     */
    public void postRoundLogAfterSettleRound() {
        // 第一个100回合完成了，打包一次
        if (logPage == 0 && roundLogList.size() == SAVE_LOG_PER_ROUND) {
            postRoundLog();
        }
    }

    /**
     * 打包回合日志
     */
    public void postRoundLog() {
        if (roundLogList.isEmpty()) {
            return;
        }
        CommonBattle.BattleRoundPage.Builder pageBuilder = CommonBattle.BattleRoundPage.newBuilder();
        pageBuilder.setPageId(++logPage);
        for (CommonBattle.BattleRound roundLog : roundLogList) {
            pageBuilder.addRounds(roundLog);
        }
        saveBattleLogPage(pageBuilder.build());
        roundLogList.clear();
        LOGGER.debug("BattleLog relation={} battleId={} post battle round log, logId={}, page={}, roundSize={}, pageBuilder={}", relationId, ctx.getRecordOne().getBattleId(), logId, logPage, ctx.curRoundNumber(), pageBuilder);
    }

    /**
     * 压缩，存库战斗日志页
     */
    private void saveBattleLogPage(CommonBattle.BattleRoundPage page) {
        try {
            TcaplusDb.BattleLogPageTable.Builder req = TcaplusDb.BattleLogPageTable.newBuilder();
            req.setLogId(logId).setPageId(logPage);
            GeminiStopWatch stopWatch = new GeminiStopWatch(StringUtils.format("battle_log_compress_size={}", page.toByteArray().length));
            ByteString bytes = ByteString.copyFrom(Zstd.compress(page.toByteArray()));
            req.setLogPage(bytes);
            stopWatch.mark("end");
            if (stopWatch.getTotalCost() > 5) {
                LOGGER.info("BattleLog gemini_perf compress battle log {}", stopWatch.stat());
            }
            InsertAsk<TcaplusDb.BattleLogPageTable.Builder> ask = new InsertAsk<>(req);
            ctx.getRelation().getGround().getAdapter().tellGameDb(ask);
        } catch (ZstdException e) {
            LOGGER.error("BattleErrLog battle log compress failed!", e);
        }
    }

    /**
     * 打包日志概要
     */
    public void postBattleLogBrief() {
        ctx.getRecordOne().addBattleLogId(logId);
        CommonBattle.BattleLog.Builder builder = CommonBattle.BattleLog.newBuilder();
        builder.setLogId(logId)
                .setStartMillis(ctx.getStartMillis())
                .setEndMillis(ctx.getEndMillis())
                .setRoundPages(logPage)
                .mergeBlueLogRole(buildLogRole(ctx.getOneRecord()))
                .mergeRedLogRole(buildLogRole(ctx.getOtherRecord()))
                .setResult(getBattleResult())
                .setRelationId(relationId)
                .getLocationBuilder().setX(ctx.getLocation().getX()).setY(ctx.getLocation().getY()).setMapId(ctx.getRelation().getGround().getPointMapId()).setMapType(ctx.getRelation().getGround().getMapType().getNumber());

        saveBattleLogBrief(builder.build());
        if (ServerContext.isDevEnv() || ServerContext.isTestEnv()) {
            LOGGER.info("BattleLog relation={} battleId={} post battle log brief, logId={}, page={}, roundSize={}, logBuilder={}", relationId, ctx.getRecordOne().getBattleId(), logId, logPage, ctx.curRoundNumber(), builder);
        }
    }

    private CommonBattle.BattleLogRole buildLogRole(BattleRecord.RoleRecord oneRecord) {
        List<CommonBattle.BattleLogRoleMember> members = oneRecord.getMembers().values()
                .stream()
                .map(BattleRecord.RoleMemberRecord::convert2LogPb)
                .collect(Collectors.toList());

        CommonBattle.BattleLogRole.Builder builder = CommonBattle.BattleLogRole.newBuilder()
                .setRoleId(oneRecord.getLeaderMemberRoleId())
                .addAllMemberList(members)
                .setRoleType(oneRecord.getRoleType())
                .setRoleTypeId(oneRecord.getRoleTypeId())
                .setGuardTower(oneRecord.getGuardTowerPb())
                .setEntityType(oneRecord.getEntityType().getNumber());

        return builder.build();
    }

    private CommonEnum.BattleLogResultType getBattleResult() {
        if (ctx.getOneRole().hasAnyAlive() && ctx.getOtherRole().hasAnyAlive()) {
            return CommonEnum.BattleLogResultType.BLRT_RETREAT;
        } else if (!ctx.getOneRole().hasAnyAlive() && !ctx.getOtherRole().hasAnyAlive()) {
            return CommonEnum.BattleLogResultType.BLRT_DRAW;
        } else if (ctx.getOneRole().hasAnyAlive()) {
            return CommonEnum.BattleLogResultType.BLRT_BLUE_WIN;
        } else {
            return CommonEnum.BattleLogResultType.BLRT_RED_WIN;
        }
    }

    private void saveBattleLogBrief(CommonBattle.BattleLog log) {
        TcaplusDb.BattleLogTable.Builder req = TcaplusDb.BattleLogTable.newBuilder();
        req.setLogId(logId).setLog(log);
        InsertAsk<TcaplusDb.BattleLogTable.Builder> ask = new InsertAsk<>(req);
        ctx.getRelation().getGround().getAdapter().tellGameDb(ask);
    }

    public void addRoundLog(CommonBattle.BattleRound log) {
        if (log != null) {
            // 对第二页的回合记录做一个FIFO，因为只需要记录最后100回合
            if (logPage >= 1 && roundLogList.size() >= SAVE_LOG_PER_ROUND) {
                roundLogList.removeFirst();
            }
            roundLogList.add(log);
        }
    }
}

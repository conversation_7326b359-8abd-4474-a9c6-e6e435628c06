package com.yorha.cnc.battle.snapshot.unit;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.soldier.BattleSoldier;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import org.jetbrains.annotations.NotNull;
import res.template.SoldierTypeTemplate;

import java.util.Map;


/**
 * <AUTHOR>
 */
public abstract class AbstractBattleSoldierSnapshot implements IBattleUnit, Comparable<AbstractBattleSoldierSnapshot> {
    private final int id;
    private final int aliveCount;
    private final int maxCanTreat;
    private int cacheTreatCount;
    private final int max;

    private final double atk;
    private final double defence;
    private final double hp;
    private final double baseHp;

    public AbstractBattleSoldierSnapshot(BattleRole role, BattleSoldier soldier, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        this.id = soldier.getId();
        this.aliveCount = soldier.aliveCount();
        this.maxCanTreat = soldier.getMaxCanTreat();
        this.max = soldier.getMax();

        this.atk = calcAttack(role, roleAdditionSnapshot);
        this.defence = calcDefence(role, roleAdditionSnapshot);
        this.hp = calcHp(role, roleAdditionSnapshot);
        this.baseHp = calcBaseHp(role, roleAdditionSnapshot);
    }

    public SoldierTypeTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, id);
    }

    public int getId() {
        return id;
    }

    /**
     * 伤害造成的士兵损失数量（轻 + 重 + 死）
     * 士兵损失 = 受到的伤害 / 生命值 (向上去整)
     */
    public int calcLoss(double damage) {
        return (int) Math.ceil(damage / getHp());
    }

    public int aliveCount() {
        return aliveCount;
    }

    public int getMaxCanTreat() {
        return maxCanTreat;
    }

    public void addCacheTreat(int value) {
        cacheTreatCount += value;
    }

    public int getCacheTreat() {
        return cacheTreatCount;
    }

    public int getMax() {
        return max;
    }

    @Override
    public double calcTotalAttack() {
        return aliveCount() * getAtk();
    }

    @Override
    public double calcTotalDefence() {
        return aliveCount() * getDefence();
    }

    @Override
    public int compareTo(@NotNull AbstractBattleSoldierSnapshot o) {
        return this.getTemplate().getBattleCalcPriority() - o.getTemplate().getBattleCalcPriority();
    }

    @Override
    public double getAtk() {
        return this.atk;
    }

    @Override
    public double getDefence() {
        return this.defence;
    }

    @Override
    public double getHp() {
        return this.hp;
    }

    @Override
    public double getBaseHp() {
        return this.baseHp;
    }
}

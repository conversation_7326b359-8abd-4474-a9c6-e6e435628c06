package com.yorha.cnc.battle.soldier;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Map;

/**
 * 单一士兵伤兵详情
 *
 * <AUTHOR>
 */
public class SoldierLossDTO {

    private final int soldierId;
    /**
     * 总的损兵数据
     */
    private final SoldierLossData lossData;
    /**
     * childRoleId -> lossData
     *
     * 每个成员的损兵详情，总和等于 lossData
     */
    private final Map<Long, SoldierLossData> childLossMap;

    /**
     * 伤害来源roleId -> lossData
     */
    private final Map<Long, SoldierLossData> lossSourceMap;

    /**
     * 伤害来源roleId -> <childRoleId -> lossData>
     *
     * 每个成员的详情伤害来源
     */
    private final Map<Long, Map<Long, SoldierLossData>> childLossSourceMap;

    public SoldierLossDTO(int soldierId) {
        this.soldierId = soldierId;
        this.lossData = new SoldierLossData();
        this.childLossMap = Maps.newHashMap();
        this.lossSourceMap = Maps.newHashMap();
        this.childLossSourceMap = Maps.newHashMap();
    }

    public boolean hasDamage() {
        return lossData.totalLoss() > 0;
    }

    public int getSoldierId() {
        return soldierId;
    }

    public SoldierLossData getLossData() {
        return lossData;
    }

    public int totalLoss() {
        return lossData.totalLoss();
    }

    public int totalDead() {
        return lossData.getDead();
    }

    public void plusLoss(SoldierLossData lossData) {
        this.lossData.merge(lossData);
    }

    public void plusChildLoss(long roleId, SoldierLossData lossData) {
        childLossMap.computeIfAbsent(roleId, k -> new SoldierLossData())
                .merge(lossData);
    }

    public void plusSourceCauseLoss(long attackerId, SoldierLossData lossData) {
        lossSourceMap.computeIfAbsent(attackerId, k -> new SoldierLossData())
                .merge(lossData);
    }

    public void plusSourceCauseChildLoss(long attackerId, long roleId, SoldierLossData lossData) {
        childLossSourceMap.computeIfAbsent(attackerId, k -> Maps.newHashMap())
                .computeIfAbsent(roleId, k -> new SoldierLossData())
                .merge(lossData);
    }

    public Map<Long, SoldierLossData> getChildLossMap() {
        return childLossMap;
    }

    public Map<Long, SoldierLossData> getLossSourceMap() {
        return lossSourceMap;
    }

    public Map<Long, SoldierLossData> getChildLossSourceMap(long atkId) {
        return childLossSourceMap.getOrDefault(atkId, Maps.newHashMap());
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

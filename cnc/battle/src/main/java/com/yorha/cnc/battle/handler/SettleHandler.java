package com.yorha.cnc.battle.handler;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.cnc.battle.common.SevereDeadRatio;
import com.yorha.cnc.battle.context.BattleRelationContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.RoundContext;
import com.yorha.cnc.battle.core.*;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.cnc.battle.soldier.SoldierGroup;
import com.yorha.cnc.battle.soldier.SoldierUnit;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.DamageRatioConf;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.Map;

import static com.yorha.common.resource.resservice.battle.BattleTemplateService.defaultBattleTypeWith;

/**
 * 结算串行处理
 *
 * <AUTHOR>
 * @date 2023/7/3
 */

@NotThreadSafe
public class SettleHandler extends BattleHandlerAbs<BattleRole> {
    private static final Logger LOGGER = LogManager.getLogger(SettleHandler.class);
    /**
     * 本回合兵损结算数据
     */
    private DamageResult damageResult;
    /**
     * 兵损数据，用于结算敌方的击杀 <attackerRoleId, <soldierId, loss>>
     */
    public final Map<Long, Map<Integer, Integer>> lossResultMap = Maps.newHashMap();

    public SettleHandler(BattleRole role) {
        super(role);
    }

    public void setDamageResult(DamageResult damageResult) {
        this.damageResult = damageResult;
    }

    public void afterSettleRound(BattleTickContext tickContext) {
        try {
            if (damageResult != null) {
                // 广播普攻
                role.getAdapter().sendOrdinaryAttack(damageResult);
                // 写战报
                role.getContext().settleRound(damageResult);
                role.getAdapter().onSettleRound(damageResult);
                BattleGround.getBattleLog().printfRelationRoundResult(role, damageResult);
            }
            if (!role.isDead()) {
                // 结算主将怒气，副将技能回合
                settleMainHeroAnger();
                settleDeputyHero();
                // 记录下回合要释放的技能
                role.getSkillHandler().prepareNextRoundSkill(tickContext);
            }
            dispatchKillToEnemy();
        } finally {
            damageResult = null;
            lossResultMap.clear();
        }
    }

    /**
     * 结算怒气
     */
    private void settleMainHeroAnger() {
        BattleHero mainHero = role.getMainHero();
        if (mainHero == null) {
            return;
        }

        int extraAnger = getExtraAnger();

        LOGGER.debug("BattleLog owner={},extra anger={}", role, extraAnger);
        // 结算错峰怒气值
        mainHero.increaseCacheAnger(extraAnger, null);

        // 结算怒气值
        mainHero.settleAnger();

    }

    private int getExtraAnger() {
        int extraAnger = 0;

        // 获取battle role本轮目标的ctx
        BattleRelationContext ctx = role.getRelationContext(role.getTargetId());
        if (ctx == null) {
            // 如果ctx为null，返回（本回合没目标，或者ctx已经不存在了）
            return extraAnger;
        }
        // 拿出对面的role
        BattleRole enemyRole = ctx.getRelation().getEnemyRole(role.getRoleId());
        if (enemyRole == null) {
            // role没了，为什么？
            return extraAnger;
        }
        // 对面打的不是我
        if (enemyRole.getTargetId() != role.getRoleId()) {
            return extraAnger;
        }

        int value = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getDisadvantageRageRecover();
        // 获取当前回合的ctx
        RoundContext context = ctx.curRoundCtx();
        boolean succ = context.isOrdinaryAttackSucc(role.getRoleId());
        if (!succ) {
            extraAnger += value;
        }
        succ = context.isAttackBackSucc(role.getRoleId());
        if (!succ) {
            extraAnger += value;
        }

        return extraAnger;
    }

    private void settleDeputyHero() {
        if (role.getDeputyHero() != null) {
            role.getDeputyHero().executeDeputyHeroSkillTrigger();
        }
    }

    private void dispatchKillToEnemy() {
        for (Map.Entry<Long, Map<Integer, Integer>> entry : lossResultMap.entrySet()) {
            BattleRole attackerRole = role.getGround().getRoleOrNull(entry.getKey());
            if (attackerRole == null) {
                continue;
            }
            // Npc部队不用算击杀
            if (attackerRole.isNpcTroop()) {
                continue;
            }
            Map<Integer, Integer> killMap = attackerRole.getParallelSettleHandler().killResultList.computeIfAbsent(role.getRoleId(), v -> Maps.newHashMap());
            for (Map.Entry<Integer, Integer> lossEntry : entry.getValue().entrySet()) {
                killMap.put(lossEntry.getKey(), killMap.getOrDefault(lossEntry.getKey(), 0) + lossEntry.getValue());
            }
        }
    }

    public Map<CommonEnum.SceneObjType, SevereDeadRatio> buildDeadRatioMap(long enemyRoleId) {
        Map<CommonEnum.SceneObjType, SevereDeadRatio> ratioMap;
        CommonEnum.BattleType battleType;
        CommonEnum.DamageRatioTypeEnum damageRatioType;

        BattleRole enemyRole = role.getGround().getRoleOrNull(enemyRoleId);
        if (enemyRole == null) {
            LOGGER.error("BattleErrLog {} buildDeadRatioMap failed, enemyRole:{} is null", role, enemyRoleId);
            return Maps.newHashMap();
        }
        if (enemyRoleId == role.getRoleId()) {
            // 自己和自己就不用算重伤比了。因为不允许出现自己打自己。
            LOGGER.warn("BattleErrLog {} buildDeadRatioMap failed, enemy is self enemyRoleId={}", role, enemyRoleId);
            return Maps.newHashMap();
        }

        BattleRelation relation = role.relationWith(enemyRoleId);
        if (relation == null) {
            // 被施加了DOT类buff，此时双方没有战斗关系
            battleType = defaultBattleTypeWith(role.getType(), enemyRole.getType());
            damageRatioType = BattleRelation.getDamageRatioType(role, enemyRole);
        } else {
            battleType = relation.getBattleType();
            damageRatioType = relation.getDamageRatioType();
        }
        ratioMap = calcRoleSeverDeadRatio(role, enemyRole, relation, battleType, damageRatioType);
        return ratioMap;
    }

    private static Map<CommonEnum.SceneObjType, SevereDeadRatio> calcRoleSeverDeadRatio(BattleRole one,
                                                                                        BattleRoleSettleOnly other,
                                                                                        BattleRelation relation,
                                                                                        CommonEnum.BattleType battleType,
                                                                                        CommonEnum.DamageRatioTypeEnum ratioTypeEnum) {
        double onePower = BattleFormula.calcAveragePower(one.getSnapShot("OnePower"), other.getSnapShot("OtherPower"));
        double otherPower = BattleFormula.calcAveragePower(other.getSnapShot("OtherPower"), one.getSnapShot("OnePower"));
        Map<CommonEnum.SceneObjType, SevereDeadRatio> ratioMap = Maps.newHashMap();

        for (SoldierGroup dGroup : one.getGroupMap().values()) {
            for (Soldier soldier : dGroup.getSoldiers()) {
                for (SoldierUnit unit : soldier.getSoldierUnitList()) {
                    CommonEnum.SceneObjType objType = unit.getObjType4DamageCalc();
                    if (!ratioMap.containsKey(objType)) {
                        DamageRatioConf conf = one.getGround().getAdapter().getDamageRatioConf(one.getRoleId(), battleType, objType, ratioTypeEnum);
                        boolean hasNpcTroop = one.isNpcTroop() || other.isNpcTroop();
                        ratioMap.put(objType, getSeverDeadRatio(one.getSnapShot("OnePower"), hasNpcTroop, conf, onePower, otherPower));
                    }
                }
            }
        }
        LOGGER.debug("BattleLog one:{}, other:{}, onePower:{}, otherPower:{}.", one, other, onePower, otherPower);
        BattleGround.getBattleLog().printfSeverDeadRatio(one, other.getRoleId(), onePower, otherPower, battleType, ratioTypeEnum, relation, ratioMap);
        if (ratioMap.isEmpty()) {
            LOGGER.warn("BattleLog one:{}, other:{}, ratioMap is empty soldierGroupSize={}", one, other, one.getGroupMap().size());
        }
        return ratioMap;
    }

    private static SevereDeadRatio getSeverDeadRatio(BattleRoleSnapshot snapshot,
                                                     boolean hasNpcTroop,
                                                     DamageRatioConf conf,
                                                     double attackerPower,
                                                     double defenderPower) {
        if (attackerPower <= 0 || defenderPower <= 0) {
            return new SevereDeadRatio()
                    .setSevereRatio(BattleFormula.getServerRatio(snapshot, conf.getSevereWoundRatio()))
                    .setDeadRatio(BattleFormula.getDeadRatio(snapshot, conf.getDeadInSevereRatio()));
        }
        // 实际重伤率
        double realSevereWoundRatio = calcRealSeverWoundRatio(hasNpcTroop, attackerPower, defenderPower, conf);
        // 0 <= 实际重伤率 <= 1
        return new SevereDeadRatio()
                .setSevereRatio(BattleFormula.getServerRatio(snapshot, Math.max(0, Math.min(1, realSevereWoundRatio))))
                .setDeadRatio(BattleFormula.getDeadRatio(snapshot, conf.getDeadInSevereRatio()));
    }

    /**
     * 计算实际重伤率
     */
    private static double calcRealSeverWoundRatio(boolean hasNpcTroop, double attackerPower, double defenderPower, DamageRatioConf conf) {
        double powerFactor = Math.max(attackerPower, defenderPower) / Math.min(attackerPower, defenderPower);
        if (hasNpcTroop
                || powerFactor <= ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getWoundPowerLimit()) {
            // 实际重伤率 = 基础重伤率 * max{攻击方战斗力 / 受击方战斗力 ，受击方战斗力 / 攻击方战斗力}
            return conf.getSevereWoundRatio() * powerFactor;
        }
        // 若战斗力差距大于某个常数（配置在战斗常量表里），则该回合战力高的一方受伤全部为轻伤，另一方受伤全部为重伤
        return attackerPower > defenderPower ? 0 : 1;
    }
}

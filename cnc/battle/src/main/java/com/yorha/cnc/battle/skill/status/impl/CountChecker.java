package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.status.IBattleTargetStatusChecker;

/**
 * 计数器checker
 *
 * <AUTHOR>
 */
public abstract class Count<PERSON><PERSON><PERSON> implements IBattleTargetStatusChecker {
    protected int count;

    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        return ++count >= Integer.parseInt(params[0]);
    }

    @Override
    public void mark() {
        count = 0;
    }

    @Override
    public void clear() {
        count = 0;
    }
}

package com.yorha.cnc.battle.skill;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;

import java.util.List;

public class EffectResult {
    private final List<PlayerScene.EffectDTO> dto;
    private final List<BattleRole> targetRoleList;
    /**
     * 发起广播的role
     * 因为DOT，HOT广播的时候，施法者可能已经死了
     */
    private final BattleRole broadcastRole;

    public EffectResult(BattleRole broadcastRole) {
        this.dto = Lists.newArrayList();
        this.targetRoleList = Lists.newArrayList();
        this.broadcastRole = broadcastRole;
    }

    public List<PlayerScene.EffectDTO> getDto() {
        return dto;
    }

    public List<BattleRole> getTargetRoleList() {
        return targetRoleList;
    }

    public BattleRole getBroadcastRole() {
        return broadcastRole;
    }
}

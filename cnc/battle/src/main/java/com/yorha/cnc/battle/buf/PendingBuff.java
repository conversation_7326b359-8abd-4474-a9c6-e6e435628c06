package com.yorha.cnc.battle.buf;

import com.yorha.cnc.battle.common.SevereDeadRatio;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.context.dot.DotContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.proto.CommonEnum;
import res.template.BattleBuffTemplate;
import res.template.SkillEffectTemplate;

import java.util.List;
import java.util.Map;


/**
 * 预备融合的buff
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public class PendingBuff {

    private final int buffId;
    private final EffectContext effectContext;
    private long value;
    private int layer;
    private final long lifeCycle;

    public PendingBuff(int buffId, int layer, BattleRole owner, EffectContext effectContext, long lifeCycle) {
        this.buffId = buffId;
        this.layer = layer;
        this.effectContext = decorateEffectContext(effectContext.getRole(), owner, effectContext);
        this.lifeCycle = lifeCycle;
    }

    /**
     * dot类 快照使用预施加回合的，防止生效阶段executor已经无了
     *
     * @param caster BUFF施加方
     * @param owner  BUFF拥有者
     */
    public EffectContext decorateEffectContext(BattleRole caster, BattleRole owner, EffectContext effectContext) {
        if (owner == null) {
            return EffectContext.copyNewInstance(effectContext);
        }
        List<Integer> effectList = ResHolder.getTemplate(BattleBuffTemplate.class, buffId).getAddSkillEffectList();
        if (effectList.isEmpty()) {
            return EffectContext.copyNewInstance(effectContext);
        }

        // effect不为空，则表示这个buff是持续提供效果的，如dot，hot。需要获取施法方当前的快照
        BattleRoleSnapshot snapshot = caster.getSnapShot("KeepEffect");
        DotContext dotContext = null;
        // 检测是否持续性的效果。dot需要快照及重伤比 hot需要快照
        for (int effectId : effectList) {
            SkillEffectTemplate skillEffectTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillEffectTemplate(effectId);
            // 持续伤害
            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_DAMAGE && skillEffectTemplate.getValue2() == BattleConstants.BATTLE_DAMAGE_WITH_DOT) {
                Map<CommonEnum.SceneObjType, SevereDeadRatio> deadRatio = owner.getSettleHandler().buildDeadRatioMap(effectContext.getRole().getRoleId());
                dotContext = new DotContext(snapshot, deadRatio);
            }
            // 持续治疗
            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_TREATMENT && skillEffectTemplate.getValue2() == BattleConstants.BATTLE_TREATMENT_WITH_HOT) {
                dotContext = new DotContext(snapshot, null);
            }
            // 持续百分比伤害
            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_PERCENTAGE_TREATMENT && skillEffectTemplate.getValue2() == BattleConstants.BATTLE_TREATMENT_WITH_HOT) {
                dotContext = new DotContext(snapshot, null);
            }
        }
        return EffectContext.newBuilder()
                .setDotContext(dotContext)
                .setCastRole(effectContext.getRole())
                .setSkillId(effectContext.getId())
                .setType(effectContext.getType())
                .setHeroId(effectContext.getHeroId())
                .setEffectId(effectContext.getEffectId())
                .setLeaderRoleId(effectContext.getLeaderRoleId())
                .setAttachBuffId(effectContext.getAttachBuffId())
                .build(true);
    }


    public void setLayer(int layer) {
        this.layer = layer;
    }

    public EffectContext getExecutorInfo() {
        return effectContext;
    }

    public int getLayer() {
        return layer;
    }

    public int getBuffId() {
        return buffId;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public long getLifeCycle() {
        return lifeCycle;
    }

    @Override
    public String toString() {
        return "PendingBuff{" +
                "buffId=" + buffId +
                ", value=" + value +
                ", layer=" + layer +
                ", lifeCycle=" + lifeCycle +
                '}';
    }
}

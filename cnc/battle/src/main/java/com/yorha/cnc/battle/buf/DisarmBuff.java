package com.yorha.cnc.battle.buf;

import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.core.BattleRole;

/**
 * 缴械BUFF 阻止普攻允许反击
 */
public class DisarmBuff extends StateBuff {
    public DisarmBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public BattleConstants.BattleRoleState getState() {
        return BattleConstants.BattleRoleState.DISARM;
    }
}

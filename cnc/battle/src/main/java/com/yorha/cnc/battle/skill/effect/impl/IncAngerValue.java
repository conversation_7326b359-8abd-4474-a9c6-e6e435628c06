package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleFormula;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 增加怒气
 *
 * <AUTHOR>
 */
public class IncAngerValue extends AbstractSkillEffectValue {

    public IncAngerValue() {
        super(SkillEffectType.SET_INCR_ANGER);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        if (target.getMainHero() == null || target.getMainHero().getId() == 0) {
            return ret;
        }
        double incValue = BattleFormula.getHeroPower(target.getSnapShot("IncAngerValue"), template.getValue1());
        target.getMainHero().increaseCacheAnger(incValue, actionCtx.getType());
        if (incValue > 0) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(this.getType());
            builder.setValue((int) incValue);
            builder.setEffectId(template.getId());
            ret.add(builder.build());
        }
        return ret;
    }

}
package com.yorha.cnc.battle.skill;

import com.google.common.collect.Lists;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;

import java.util.ArrayList;
import java.util.List;

/**
 * 释放一个技能后的结果
 *
 * <AUTHOR>
 */
public class SkillResult {
    private int skillId;
    private int heroId;
    private long attackerId;
    // 技能释放时的朝向
    private StructPB.PointPB yaw;
    // 技能释放的原点
    private StructPB.PointPB prepareOrigin;
    // 技能目标id
    private long targetId;
    /**
     * 此次技能生效的技能效果
     */
    private final List<EffectResult> effectResults = new ArrayList<>();
    private CommonEnum.SkillFireType fireType = CommonEnum.SkillFireType.SFT_NONE;

    public SkillResult() {
    }

    public int getSkillId() {
        return skillId;
    }

    public SkillResult setSkillId(int skillId) {
        this.skillId = skillId;
        return this;
    }

    public void addEffectRes(EffectResult res) {
        this.effectResults.add(res);
    }

    public int getHeroId() {
        return heroId;
    }

    public SkillResult setHeroId(int heroId) {
        this.heroId = heroId;
        return this;
    }

    public long getAttackerId() {
        return attackerId;
    }

    public SkillResult setAttackerId(long attackerId) {
        this.attackerId = attackerId;
        return this;
    }

    public List<PlayerScene.EffectDTO> getEffectList() {
        List<PlayerScene.EffectDTO> res = Lists.newArrayList();
        for (EffectResult effectResult : effectResults) {
            res.addAll(effectResult.getDto());
        }
        return res;
    }

    public CommonEnum.SkillFireType getFireType() {
        return fireType;
    }

    public SkillResult setFireType(CommonEnum.SkillFireType fireType) {
        this.fireType = fireType;
        return this;
    }

    public List<EffectResult> getEffectResults() {
        return effectResults;
    }

    public StructPB.PointPB getYaw() {
        return yaw;
    }

    public SkillResult setYaw(StructPB.PointPB yaw) {
        this.yaw = yaw;
        return this;
    }

    public StructPB.PointPB getPrepareOrigin() {
        return prepareOrigin;
    }

    public SkillResult setPrepareOrigin(StructPB.PointPB prepareOrigin) {
        this.prepareOrigin = prepareOrigin;
        return this;
    }

    public long getTargetId() {
        return targetId;
    }

    public SkillResult setTargetId(long targetId) {
        this.targetId = targetId;
        return this;
    }

    @Override
    public String toString() {
        return "SkillResult{" +
                "skillId=" + skillId +
                ", heroId=" + heroId +
                ", attackerId=" + attackerId +
                ", fireType=" + fireType +
                ", yaw=" + yaw +
                ", prepareOrigin=" + prepareOrigin +
                ", targetId=" + targetId +
                '}';
    }
}

package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.proto.EntityAttrOuterClass;

import static com.yorha.common.constant.BattleConstants.*;

/**
 * 建筑类型
 *
 * <AUTHOR>
 */
public class BuildingTypeChecker extends <PERSON><PERSON><PERSON><PERSON> {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        EntityAttrOuterClass.EntityType entityType = role.getAdapter().getEntityType();
        switch (Integer.parseInt(params[0])) {
            case MAPBUILDING_AND_CITY:
                return entityType == EntityAttrOuterClass.EntityType.ET_City || entityType == EntityAttrOuterClass.EntityType.ET_MapBuilding;
            case MAPBUILDING:
                return entityType == EntityAttrOuterClass.EntityType.ET_MapBuilding;
            case CITY:
                return entityType == EntityAttrOuterClass.EntityType.ET_City;
            default:
                return false;
        }
    }
}

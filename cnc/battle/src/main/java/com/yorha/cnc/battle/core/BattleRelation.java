package com.yorha.cnc.battle.core;


import com.yorha.cnc.battle.context.BattleRelationContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.unit.BattleHelper;
import com.yorha.common.constant.BattleConstants.BattleRelationStatus;
import com.yorha.common.constant.BattleConstants.BattleRelationType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BattleOverType;
import com.yorha.proto.CommonEnum.BattleType;
import com.yorha.proto.SsPlayerMisc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.MonsterTemplate;

/**
 * 1v1的战斗关系
 * 完整的生命周期 buildRelation -> prepare -> active -> stop -> remove
 * tick驱动：由战斗tick驱动
 * force驱动：由业务侧驱动，需要处理完整生命周期
 * ps：清理时需要先清理relation再清理one、other
 */
public class BattleRelation {
    private static final Logger LOGGER = LogManager.getLogger(BattleRelation.class);

    private final long id;
    /**
     * oneId:otherId
     */
    private final String key;
    /**
     * one是这段战斗关系的主动发起者，在战斗关系结束之前都不会改变这个定位，即便中途one的targetId变更了
     */
    private final BattleRole one;
    /**
     * 战斗关系的被动建立者
     */
    private final BattleRole other;

    /**
     * 上下文记录
     */
    protected final BattleRelationContext context;

    /**
     * 所处战场
     */
    private final BattleGround ground;
    private BattleRelationStatus status;
    /**
     * 本战斗关系的类型，用于读取不同的战损比例配置等
     */
    private final BattleType battleType;
    /**
     * 当前已经挂起了几个回合，超过7回合时战斗关系会自动终止
     */
    private int hangupRound;
    private boolean hangupThisRound;
    /**
     * 战斗关系本回合是否已经准备好中间状态
     */
    private boolean prepared;
    /**
     * 已经尝试结算数据的回合数，6回合未结算成功。强制进入Stop状态
     */
    public int settleBattleRound;
    /**
     * 是否产生过伤害
     */
    private boolean hasDamage;
    /**
     * 战斗关系的类型。用于实现功能：被玩家技能溅射到的玩家战斗关系，不会发生普攻反击。被溅射到的野怪，会正常发起普攻反击
     */
    private BattleRelationType type;
    /**
     * 本战斗关系的重伤比类型
     */
    private final CommonEnum.DamageRatioTypeEnum damageRatioType;

    public BattleRelation(BattleGround ground, Point point, BattleType battleType, BattleRole one, BattleRole other, BattleRelationType type) {
        this.id = IdFactory.nextIdForRecycleIncrease("battle_relation");
        this.key = BattleHelper.keyOf(one.getRoleId(), other.getRoleId());
        this.battleType = battleType;
        this.ground = ground;
        this.hangupRound = 0;
        this.hangupThisRound = false;
        this.status = BattleRelationStatus.Running;
        this.one = one;
        this.other = other;
        this.context = new BattleRelationContext(this, point);
        this.type = type;
        // 防守方是玩家驻防的建筑，则需要判断建筑类型
        this.damageRatioType = getDamageRatioType(one, other);
        addHate();
    }

    public static CommonEnum.DamageRatioTypeEnum getDamageRatioType(BattleRole one, BattleRole other) {
        CommonEnum.DamageRatioTypeEnum otherDamageType = other.getAdapter().getDamageRatioType();
        if (otherDamageType != CommonEnum.DamageRatioTypeEnum.DTE_NONE) {
            return otherDamageType;
        } else {
            return one.getAdapter().getDamageRatioType();
        }
    }

    private void addHate() {
        getGround().getAdapter().addHate(one.getAdapter(), other.getAdapter());
    }

    public BattleRole getRole(long roleId) {
        if (one.roleId == roleId) {
            return one;
        }
        if (other.roleId == roleId) {
            return other;
        }
        return null;
    }

    public BattleRole getEnemyRole(long roleId) {
        if (one.roleId == roleId) {
            return other;
        }
        if (other.roleId == roleId) {
            return one;
        }
        return null;
    }

    public boolean isRelationOf(long oneId, long otherId) {
        return (one.roleId == oneId && other.roleId == otherId) || (one.roleId == otherId && other.roleId == oneId);
    }

    public boolean isStopped() {
        return status == BattleRelationStatus.Stop;
    }

    public String getKey() {
        return this.key;
    }

    public boolean shouldHangup() {
        if (prepared) {
            return hangupThisRound;
        }
        hangupThisRound = false;
        // 被动建立的关系，需要挂起，如：被技能溅射到的
        if (type == BattleRelationType.Passive) {
            hangupThisRound = true;
            return true;
        }
        if (type == BattleRelationType.AreaSkill) {
            hangupThisRound = true;
            return false;
        }
        // 攻城战中 进攻部队改变指令不进攻  不计算
        if (status == BattleRelationStatus.Suspend) {
            hangupThisRound = true;
            return true;
        }
        if (!one.getAdapter().isDistanceOk(other.roleId)) {
            hangupThisRound = true;
        }
        return hangupThisRound;
    }

    public boolean isHangupInLastTick() {
        return this.hangupThisRound;
    }

    /**
     * 上个回合到现在外围状态可能发生变更，战斗关系也需要重新构建
     */
    public void prepareBeforeAction(BattleTickContext tickContext) {
        if (prepared) {
            return;
        }
        if (isStopped()) {
            return;
        }

        // 准备阶段
        context.prepareRound(tickContext);

        if (one.getAdapter().canBattleWithCode(other, false).isNotOk()) {
            // 本来可以建立relation，现在不行了，比如其中一个联盟改变了，导致现在两个army是同盟的
            this.end(BattleOverType.BOT_END_OTHER, tickContext);
            // 这里还是比较少见的，先info日志看看
            LOGGER.info("BattleLog cannot battle anymore! [{}]vs[{}]", one, other);
            return;
        }

        // 判断是否脱战
        if (shouldHangup()) {
            hangupRound++;
            int maxTickTimes = getMaxHangupRound();
            if (hangupRound >= maxTickTimes) {
                //脱离战斗，原地呆着
                this.end(BattleOverType.BOT_OUT_OF_ROUND, tickContext);
                LOGGER.debug("BattleLog {}回合没打起来，battle end,one={},other={}", maxTickTimes, one, other);
            }
        } else {
            // relation可以正常继续
            hangupRound = 0;
        }
        prepared = true;
    }

    private int getMaxHangupRound() {
        // 和野怪战斗需要读取野怪的脱战回合数
        int monsterId = 0;
        if (one.isMonsterObj()) {
            monsterId = (int) one.getAdapter().getRoleTypeId();
        } else if (other.isMonsterObj()) {
            monsterId = (int) other.getAdapter().getRoleTypeId();
        }
        MonsterTemplate template = ResHolder.findTemplate(MonsterTemplate.class, monsterId);
        if (template != null && template.getOutBattleTime() > 0) {
            return template.getOutBattleTime();
        }
        return ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getMaxTickTimes();
    }

    public void afterSettleRound(BattleTickContext tickContext) {
        prepared = false;
        context.afterSettleRound(tickContext);
    }

    /**
     * 强制结束战斗关系（战斗侧提供给业务侧强制结束战斗关系的接口）
     * 此接口是提供给业务侧使用的，战斗侧触发则无需REMOVE操作，由战斗tick自己处理
     * 1、ToAddRelation需要删除
     * 2、ACTION 需要 STOP REMOVE
     * 3、STOP 需要 REMOVE
     */
    public void forceEndRelation(BattleOverType type) {
        if (isStopped()){
            return;
        }
        LOGGER.info("BattleLog force end battle, relation={} reason={} one={}, other={}", this, type, one, other);

        // 处理 ACTION -> STOP
        try {
            end(type, getGround().getTickCtx());
        } catch (Exception e) {
            LOGGER.error("BattleErrLog relation={} end failed! ACTION -> STOP one={}, other={}, type={}", this, one, other, type, e);
        }

        // 处理 PREPARE -> REMOVE (toAdd的无脑直接删)
        try {
            BattleRelation removeToAddRelation = getGround().toAddRelationMap.remove(this.getKey());
            if (removeToAddRelation != null) {
                getOne().clearToAddRelation(removeToAddRelation.getKey());
                getOther().clearToAddRelation(removeToAddRelation.getKey());
            }
        }catch (Exception e){
            LOGGER.error("BattleErrLog relation={} remove failed! PREPARE -> REMOVE one={}, other={}, type={}", this, one, other, type, e);
        }

        if (getGround().isOnTick()){
            LOGGER.info("BattleLog force end battle! isOnTick, one={}, other={} type={}", one, other, type);
            return;
        }

        // 处理 STOP -> REMOVE
        try {
            BattleRelation removeRelation = getGround().battleRelationMap.remove(this.getKey());
            if (removeRelation != null){
                getOne().clearMyRelationWith(getOther());
                getOther().clearMyRelationWith(getOne());
            }
        } catch (Exception e) {
            LOGGER.error("BattleErrLog relation={} remove failed! STOP -> REMOVE one={}, other={}, type={}", this, one, other, type, e);
        }
    }

    public void end(BattleOverType type, @NotNull BattleTickContext tickContext) {
        if (isStopped()) {
            // 这里如果不直接return，会有死循环问题
            // 例:dead->end->endSingleRelation->deleteObj->end (集结体)
            return;
        }
        setStatus(BattleRelationStatus.Stop);
        LOGGER.info("BattleLog stop battle, relation={} reason={} one={}, other={}", this, type, one, other);

        getGround().getAdapter().removeHate(one.getAdapter(), other.getAdapter());

        boolean isOneDead = !one.hasAnyAlive();
        boolean isOtherDead = !other.hasAnyAlive();

        other.beforeEndSingleRelation(type, one, isOneDead, this);
        one.beforeEndSingleRelation(type, other, isOtherDead, this);

        other.onEndSingleRelation(type, one, isOtherDead, isOneDead, getContext().getRecordOne());
        one.onEndSingleRelation(type, other, isOneDead, isOtherDead, getContext().getRecordOne());

        context.end(tickContext);

        other.afterEndSingleRelation(type, one, isOtherDead, isOneDead, getContext().getRecordOne());
        one.afterEndSingleRelation(type, other, isOneDead, isOtherDead, getContext().getRecordOne());
    }

    public long getId() {
        return id;
    }

    public boolean isHasDamage() {
        return hasDamage;
    }

    public void setHasDamage(boolean hasDamage) {
        this.hasDamage = hasDamage;
    }

    public BattleRelationStatus getStatus() {
        return status;
    }

    public void setStatus(BattleRelationStatus status) {
        this.status = status;
    }

    public int curRoundNumber() {
        return context.curRoundNumber();
    }

    public BattleType getBattleType() {
        return battleType;
    }

    public BattleRole getOne() {
        return one;
    }

    public BattleRole getOther() {
        return other;
    }


    /**
     * 相对的进攻方id
     *
     * @return 0表示这段战斗关系双方均不作为主动进攻方
     */
    public long getRelativelyAttackerId() {
        if (one.getActiveTargetId() > 0) {
            if (one.getActiveTargetId() == other.roleId) {
                return one.roleId;
            } else {
                if (other.getActiveTargetId() > 0) {
                    if (other.getActiveTargetId() == one.roleId) {
                        return other.roleId;
                    }
                } else {
                    if (other.getTargetId() == one.roleId) {
                        return other.roleId;
                    }
                }
            }
        } else {
            if (other.getActiveTargetId() > 0) {
                if (other.getActiveTargetId() == one.roleId) {
                    return other.roleId;
                } else {
                    if (one.getTargetId() == other.roleId) {
                        return one.roleId;
                    }
                }
            } else {
                if (one.getTargetId() == other.roleId) {
                    return one.roleId;
                } else if (other.getTargetId() == one.roleId) {
                    return other.roleId;
                }
            }
        }
        return 0;
    }

    public BattleRelationContext getContext() {
        return context;
    }

    public BattleGround getGround() {
        return ground;
    }

    @Override
    public String toString() {
        return "BattleRelation:[" + id + "-" + key + "]";
    }

    public void init() {
        if (prepared) {
            return;
        }
        one.addRelation(other.roleId, this);
        other.addRelation(one.roleId, this);
        context.initRelationRecord();
    }

    public void setType(BattleRelationType type) {
        this.type = type;
    }

    public BattleRelationType getType() {
        return type;
    }

    public void handlePlunderResult(SsPlayerMisc.PlunderResult plunderResult) {
        if (ServerContext.isDevEnv() || ServerContext.isTestEnv()) {
            LOGGER.info("BattleLog plunder process handlePlunderResult relation={}, one={}, other:{}, isForceEnd:{}", id, one, other, plunderResult == null);
        }
        one.context.pendingPlunderRelationIds.remove(id);
        other.context.pendingPlunderRelationIds.remove(id);

        one.getAdapter().handlePlunderResult(plunderResult, this);
        other.getAdapter().handlePlunderResult(plunderResult, this);
    }

    public void resetHangupRound() {
        hangupRound = 0;
    }

    public CommonEnum.DamageRatioTypeEnum getDamageRatioType() {
        return damageRatioType;
    }

    /**
     * 判断目标是否为进攻方：这里的进攻方是相对的
     */
    public boolean isRelativelyAttacker(long entityId) {
        return getRelativelyAttackerId() == entityId;
    }

    public void afterRemoveRelation() {
        LOGGER.info("BattleLog remove battle, relation={}", this);
        getOne().clearMyRelationWith(getOther());
        getOther().clearMyRelationWith(getOne());
    }
}

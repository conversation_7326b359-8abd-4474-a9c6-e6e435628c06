package com.yorha.cnc.battle.skill.filter;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.proto.CommonEnum;

import java.util.HashSet;
import java.util.Set;

public class SkillFilterHelper {

    /**
     * 根据仇恨筛选
     */
    public static Set<BattleRole> filterByHate(BattleRole attacker, Set<BattleRole> targetList) {
        Set<BattleRole> ret = new HashSet<>();
        Set<Long> allEnemyClan = attacker.getAdapter().getAllEnemyClan();
        Set<Long> allEnemyPlayerId = attacker.getAdapter().getAllEnemyPlayerId();
        boolean hasBattleWithNeutral = attacker.hasBattleWithNeutral();
        long attackerClanId = attacker.getAdapter().getClanId();
        long attackerZoneId = attacker.getAdapter().getZoneId();

        for (BattleRole target : targetList) {
            long targetClanId = target.getAdapter().getClanId();
            long targetZoneId = target.getAdapter().getZoneId();

            if (attacker.hasRelationWith(target.getRoleId())) {
                // 已经建立战斗关系，已经是仇恨关系了
                ret.add(target);
            } else {
                if (target.getAdapter().getCampEnum() == CommonEnum.Camp.C_NEUTRAL) {
                    // 目标是中立阵营，进攻方战斗关系中有中立阵营（玩家正在打一个野怪，可以AOE到别的野怪）
                    if (hasBattleWithNeutral) {
                        ret.add(target);
                    }
                    continue;
                }

                if (attackerZoneId != targetZoneId) {
                    // 是不同服的人，判断这个两个国家是否敌对
                    if (attacker.getGround().getAdapter().isHate(attacker.getAdapter(), target.getAdapter())) {
                        ret.add(target);
                    }
                    continue;
                }

                if (attackerClanId != 0) {
                    // 进攻方有联盟
                    if (targetClanId != 0
                            && attacker.getGround().getAdapter().isHate(attacker.getAdapter(), target.getAdapter())) {
                        // 两个联盟是仇恨关系
                        ret.add(target);
                    } else if (targetClanId == 0 && target.getAdapter().getAllEnemyClan().contains(attackerClanId)) {
                        // 目标没有联盟，但目标的仇恨联盟有进攻方的联盟
                        ret.add(target);
                    }
                } else {
                    // 进攻方没有联盟
                    if (targetClanId != 0) {
                        // 目标有联盟，属于进攻方的仇恨联盟
                        if (allEnemyClan.contains(targetClanId)) {
                            ret.add(target);
                        }
                    } else {
                        // 目标没有联盟，属于进攻方战斗关系中的同个entity下的目标（同个玩家的多个部队）
                        if (allEnemyPlayerId.contains(target.getAdapter().getPlayerId())) {
                            ret.add(target);
                        }
                    }
                }
            }
        }
        return ret;
    }

    /**
     * 筛选可战斗
     */
    public static Set<BattleRole> filterByCanBattle(BattleRole attacker, Set<BattleRole> target) {
        Set<BattleRole> ret = new HashSet<>();
        for (BattleRole e : target) {
            if (attacker.getRoleId() != e.getRoleId()
                    && attacker.getAdapter().canBattleWithCode(e, false).isOk()) {
                ret.add(e);
            }
        }
        return ret;
    }

    /**
     * 筛选相同阵营
     */
    public static Set<BattleRole> filterByCamp(BattleRole castObj, Set<BattleRole> target) {
        HashSet<BattleRole> ret = new HashSet<>();
        for (BattleRole e : target) {
            if (!castObj.getAdapter().campAllowBattle(e)) {
                ret.add(e);
            }
        }
        return ret;
    }

}

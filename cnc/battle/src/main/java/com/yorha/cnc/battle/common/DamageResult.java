package com.yorha.cnc.battle.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.handler.ParallelSettleHandler;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.SoldierTypeTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 回合里一个战斗单元受到的伤害汇总
 *
 * <AUTHOR>
 */
public class DamageResult {

    /**
     * 根据不同soldierId，统计的伤兵数据（也有区分了child和伤害来源的数据，可谓是伤兵大全呐）
     */
    private final Collection<SoldierLossDTO> soldierLossList;
    /**
     * attacker battleRoleId->造成的死兵数
     */
    private final Map<Long, SoldierLossData> soldierLossSource;
    /**
     * 防御塔损失
     */
    private SoldierLossDTO guardTowerLoss;
    /**
     * battleRoleId->兵损详情
     */
    private final Map<Long, List<SoldierLossDTO>> childSoldierLoss;
    /**
     * 普攻造成的损失
     */
    private final int ordinaryAttackNum;

    /**
     * 对护盾造成的伤害
     */
    private final int shieldValue;

    public DamageResult(ParallelSettleHandler.SettleDTO settleDTO) {
        this.soldierLossList = Lists.newArrayList();
        this.soldierLossSource = Maps.newHashMap();
        this.childSoldierLoss = Maps.newHashMap();
        this.ordinaryAttackNum = settleDTO.getDamageValue();
        this.shieldValue = settleDTO.getShieldValue();
        buildLossData(settleDTO.getSoldierLossMap().values());
    }

    private void buildLossData(Collection<SoldierLossDTO> soldierLossList) {
        for (SoldierLossDTO dto : soldierLossList) {
            // 构建士兵，防御塔数据
            buildLossData(dto);
            // 构建兵损来源
            buildSoldierLossSourceData(dto);
            // 构建成员兵损
            buildChildSoldierLossData(dto);
        }
    }

    private void buildLossData(SoldierLossDTO lossDTO) {
        SoldierTypeTemplate soldierTemplate = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, lossDTO.getSoldierId());
        if (soldierTemplate.getSoldierType() == CommonEnum.SoldierType.ST_GuardTower_VALUE) {
            guardTowerLoss = lossDTO;
        } else {
            soldierLossList.add(lossDTO);
        }
    }

    private void buildSoldierLossSourceData(SoldierLossDTO lossDTO) {
        for (Map.Entry<Long, SoldierLossData> entry : lossDTO.getLossSourceMap().entrySet()) {
            soldierLossSource.computeIfAbsent(entry.getKey(), k -> new SoldierLossData()).merge(entry.getValue());
        }
    }

    private void buildChildSoldierLossData(SoldierLossDTO lossDTO) {
        for (Map.Entry<Long, SoldierLossData> entry : lossDTO.getChildLossMap().entrySet()) {
            SoldierLossDTO childDto = new SoldierLossDTO(lossDTO.getSoldierId());
            childDto.plusLoss(entry.getValue());
            childSoldierLoss.computeIfAbsent(entry.getKey(), k -> Lists.newArrayList()).add(childDto);
        }
    }

    public Collection<SoldierLossDTO> getSoldierLossList() {
        return soldierLossList;
    }

    public Map<Long, SoldierLossData> getSoldierLossSource() {
        return soldierLossSource;
    }

    public SoldierLossDTO getGuardTowerLoss() {
        return guardTowerLoss;
    }

    /**
     * 包括轻伤重伤阵亡
     */
    public int totalLoss() {
        return soldierLossList.stream().mapToInt(SoldierLossDTO::totalLoss).sum();
    }

    public Map<Long, List<SoldierLossDTO>> getChildSoldierLoss() {
        return childSoldierLoss;
    }

    public int getOrdinaryAttackNum() {
        return ordinaryAttackNum;
    }

    public int getShieldValue() {
        return shieldValue;
    }

    @Override
    public String toString() {
        return "DamageResult{" +
                "soldierLossList=" + soldierLossList +
                ", soldierLossSource=" + soldierLossSource +
                ", guardTowerLoss=" + guardTowerLoss +
                ", childSoldierLoss=" + childSoldierLoss +
                '}';
    }

    public String toLogString() {
        return StringUtils.format("总损失={}, 损失详情={}, 防御塔损失={}, 攻击方击杀={}",
                soldierLossList.stream().collect(Collectors.toMap(SoldierLossDTO::getSoldierId, SoldierLossDTO::getLossData)),
                childSoldierLoss.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, it -> it.getValue().stream().collect(Collectors.toMap(SoldierLossDTO::getSoldierId, SoldierLossDTO::getLossData)))),
                guardTowerLoss != null ? Pair.of(guardTowerLoss.getSoldierId(), guardTowerLoss.totalLoss()) : null,
                soldierLossSource
                );
    }
}
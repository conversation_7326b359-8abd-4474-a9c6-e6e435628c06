package com.yorha.cnc.battle.buf;

import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.core.BattleRole;

/**
 * 隐身
 * <AUTHOR>
 */
public class StealthBuff extends StateBuff {
    public StealthBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public BattleConstants.BattleRoleState getState() {
        return BattleConstants.BattleRoleState.STEALTH;
    }
}

package com.yorha.cnc.battle.context;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.GeminiStopWatch;

/**
 * 战斗tick行文信息,一回合一次
 *
 * <AUTHOR>
 */
public class BattleTickContext {

    /**
     * 战斗的tick是‘瞬时’的，像战报里的时间应当是统一的
     */
    private final long tickMillis;
    /**
     * 战场的全局round
     */
    private final int groundRound;
    public final GeminiStopWatch watchForOnTick;

    public BattleTickContext(int groundRound, long tickMillis) {
        this.groundRound = groundRound;
        this.tickMillis = tickMillis;
        this.watchForOnTick = new GeminiStopWatch(StringUtils.format("battle_onTick_round_{}", groundRound));
    }

    public int getGroundRound() {
        return groundRound;
    }

    public long getTickMillis() {
        return tickMillis;
    }
}

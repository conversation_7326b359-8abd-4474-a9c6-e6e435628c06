package com.yorha.cnc.battle.handler;

import com.yorha.cnc.battle.adapter.interfaces.IBattleRoleAdapter;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.actorservice.proto.SceneObjSpawnParam;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SummoningMonsterTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 召唤
 * -创建召唤物
 * -提供召唤物的地址
 * -也可以反向拿到master的地址
 *
 * <AUTHOR>
 */
public class SummoningHandler extends BattleHandlerAbs<BattleRole> {
    private static final Logger LOGGER = LogManager.getLogger(SummoningHandler.class);

    /**
     * 做为召唤物并初始成功
     */
    private boolean initOk = false;

    /**
     * 组id
     */
    private int templateId;

    /**
     * 创建者
     */
    private BattleRole creator;

    /**
     * 召唤兽（组 -> List<召唤兽>）
     */
    private final Map<Integer, List<BattleRole>> summonsGroup = new HashMap<>();

    /**
     * 程序侧的最大召唤数
     */
    private final Integer MAX_SUMMON_NUM = 10;

    public SummoningHandler(BattleRole role) {
        super(role);
    }

    /**
     * 召唤入口
     *
     * @param type       目前只支持区域技能、野怪
     * @param templateId 配置id，类型不同配置表读取方式不同
     * @param point      选出生点的基准点
     * @return 创建对象
     */
    public BattleRole createSummons(SummonsType type, int templateId, Point point) {
        if (templateId <= 0) {
            LOGGER.error("SummoningHandler createSummons fail, templateId={}", point);
            return null;
        }
        BattleRole summons = null;
        if (type == SummonsType.MONSTER) {
            summons = innerCreateSummonsWithMonster(role, templateId);
        }
        if (summons != null) {
            LOGGER.info("SummoningHandler createSummons success, master={} summons={}", role, summons);
            createSummonsPost(type, templateId, summons);
            return summons;
        }
        LOGGER.error("SummoningHandler createSummons fail, type={} templateId={} point={}", type, templateId, point);
        return null;
    }

    /**
     * 可以召唤检测
     */
    public boolean isCanSummoning() {
        int totalCount = 0;
        for (List<BattleRole> value : summonsGroup.values()) {
            totalCount += value.size();
        }
        return MAX_SUMMON_NUM > totalCount;
    }

    /**
     * 检查是否应回收
     */
    public boolean isShouldDel() {
        if (!initOk) {
            return false;
        }
        if (getCreator() == null) {
            return true;
        }
        if (getCreator().isDead()) {
            return true;
        }
        if (!getCreator().hasTarget()) {
            return true;
        }
        return false;
    }

    /**
     * 回收
     * ---断引用
     * ---通知创造者移除自己
     */
    public void recycleSummons() {
        if (!initOk) {
            return;
        }
        if (this.getCreator() == null) {
            LOGGER.error("SummoningHandler recycleSummonsExec fail, monster is null role={}", role);
            return;
        }
        LOGGER.info("SummoningHandler recycleSummonsExec role={} creator={}", role, getCreator());
        BattleRole creator = this.getCreator();
        this.creator = null;
        summonsGroup.clear();
        initOk = false;
        SummoningMonsterTemplate template = ResHolder.getInstance().getValueFromMap(SummoningMonsterTemplate.class, templateId);
        creator.getSummoningHandler().removeSummons(template.getGroupId(), role.getRoleId());
    }

    /**
     * 根据配置id获取召唤物数量
     *
     * @param summonConfigId 技能召唤表配置id
     * @return 数量
     */
    public int getSummonsSize(int summonConfigId) {
        int count = 0;
        for (List<BattleRole> value : summonsGroup.values()) {
            for (BattleRole battleRole : value) {
                if (battleRole.getSummoningHandler().getTemplate() == null) {
                    continue;
                }
                if (battleRole.getSummoningHandler().getTemplate().getId() == summonConfigId) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 根据组id获取召唤物数量
     *
     * @param groupId 配置组id
     * @return 数量
     */
    public int getSummonsGroupSize(int groupId) {
        if (!summonsGroup.containsKey(groupId)) {
            return 0;
        }
        return summonsGroup.get(groupId).size();
    }

    public SummoningMonsterTemplate getTemplate() {
        return ResHolder.getInstance().getValueFromMap(SummoningMonsterTemplate.class, templateId);
    }

    public BattleRole getCreator() {
        return creator;
    }

    /**
     * 删除召唤物
     */
    private boolean removeSummons(int groupId, long roleId) {
        if (!summonsGroup.containsKey(groupId)) {
            return false;
        }
        Iterator<BattleRole> iterator = summonsGroup.get(groupId).iterator();
        while (iterator.hasNext()) {
            BattleRole next = iterator.next();
            // 按唯一id删的，提前终止
            if (next.getRoleId() == roleId) {
                iterator.remove();
                return true;
            }
        }
        return false;
    }

    /**
     * 召唤后续
     * 添加召唤列表，初始化召唤物
     */
    private void createSummonsPost(SummonsType type, int templateId, BattleRole summons) {
        int groupId = 0;
        SummoningMonsterTemplate template = null;
        if (type == SummonsType.MONSTER) {
            template = ResHolder.getInstance().getValueFromMap(SummoningMonsterTemplate.class, templateId);
            groupId = template.getGroupId();
        }
        List<BattleRole> summonsList = summonsGroup.computeIfAbsent(groupId, (key) -> new ArrayList<>());
        summonsList.add(summons);
        // 初始化并开启服务
        summons.getSummoningHandler().initMaster(role, template, type);
        LOGGER.info("SummoningHandler createSummonsPost bind success, master={} summons={}", role, summons);
    }

    /**
     * 初始化summonsHandler
     */
    private void initMaster(BattleRole creator, SummoningMonsterTemplate template, SummonsType type) {
        if (initOk) {
            LOGGER.error("SummoningHandler setMaster fail, summonHandler has been activated , master={} owner={}", getCreator(), role);
            return;
        }
        // 不允许空哈，目前仅允许monster
        if (template == null) {
            LOGGER.error("SummoningHandler setMaster fail, creator been set, config is null");
            return;
        }
        // 这里校验的是参数creator哈
        if (creator == null) {
            LOGGER.error("SummoningHandler setMaster fail, creator is null");
            return;
        }
        if (getCreator() != null) {
            LOGGER.error("SummoningHandler setMaster fail, creator been set, master={} owner={}", getCreator(), role);
            return;
        }

        this.creator = creator;
        this.templateId = template.getId();
        this.initOk = true;
    }

    /**
     * 召唤野怪
     */
    private static BattleRole innerCreateSummonsWithMonster(BattleRole master, int summonTemplateId) {
        SummoningMonsterTemplate template = ResHolder.getInstance().getValueFromMap(SummoningMonsterTemplate.class, summonTemplateId);

        SceneObjSpawnParam param = new SceneObjSpawnParam();
        IBattleRoleAdapter adapter = master.getAdapter();
        if (adapter.getMoveAdapter() != null) {
            // 与master朝向相同
            if (template.getBirthFace() == CommonEnum.InvokeYawType.IYT_FOLLOW_MASTER) {
                param.setYaw(adapter.getMoveAdapter().getYaw().toPoint());
            }
        } else {
            LOGGER.warn("SummoningHandler createSummonsWithMonster, summoner moveAdapter not exist, role={}", master);
        }
        // 朝向master
        if (template.getBirthFace() == CommonEnum.InvokeYawType.IYT_TO_MASTER) {
            param.setYaw(adapter.getCurPoint());
        }
        // 强制回收
        param.setBattleSummons(true);
        param.setLifeTime(SystemClock.now() + TimeUnit.SECONDS.toMillis(template.getSavetime()));
        BattleRole battleRole = adapter.invokeSummoningMonster(template, adapter.getCurPoint(), param);
        if (battleRole != null) {
            return battleRole;
        }
        LOGGER.error("SummoningHandler createSummonsWithMonster fail, summonTemplateId={} param={}", summonTemplateId, param);
        return null;
    }

    /**
     * 召唤的类型
     * -区域技能
     * -野怪
     */
    public enum SummonsType {
        AREA_SKILL,
        MONSTER
    }
}

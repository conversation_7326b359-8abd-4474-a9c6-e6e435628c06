package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;

import static com.yorha.common.constant.BattleConstants.*;

/**
 * 身上没有指定buff
 *
 * <AUTHOR>
 */
public class NotHasBuffStatusChecker extends Status<PERSON>hecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        int type = Integer.parseInt(params[0]);
        int v = Integer.parseInt(params[1]);
        switch (type) {
            case BUFF_TAG:
                return !role.getBuffHandler().hasBuffByTag(v);
            case BUFF_GROUP_ID:
                return !role.getBuffHandler().hasBuffByGroupId(v);
            case BUFF_ID:
                return !role.getBuffHandler().hasBuff(v);
            default:
                return false;
        }
    }
}

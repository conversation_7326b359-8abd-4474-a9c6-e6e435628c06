package com.yorha.cnc.battle.context;

import com.yorha.cnc.battle.record.RoundRoleAction;
import com.yorha.proto.CommonBattle;

/**
 * relation中的一个回合
 *
 * <AUTHOR>
 */
public class RoundContext {
    /**
     * 回合数
     */
    private final int roundNumber;
    /**
     * 回合中的蓝方
     */
    private RoundRole blueRole;
    /**
     * 回合中的红方
     */
    private RoundRole redRole;

    public RoundContext(BattleRelationContext relationCtx, int round) {
        this.roundNumber = round;
        this.blueRole = new RoundRole(relationCtx.getOneRole());
        this.redRole = new RoundRole(relationCtx.getOtherRole());
    }

    public void init(BattleRelationContext relationCtx, boolean isFirstRound) {
        if (isFirstRound) {
            relationCtx.getOneRole().getBuffHandler().logInitBuff(blueRole.getRoleAction());
            relationCtx.getOtherRole().getBuffHandler().logInitBuff(redRole.getRoleAction());
        }
    }

    private RoundRoleAction getBlueRoleAction() {
        return blueRole.getRoleAction();
    }

    private RoundRoleAction getRedRoleAction() {
        return redRole.getRoleAction();
    }

    public RoundRoleAction getRoleAction(long roleId) {
        return getBlueRoleAction().getRoleId() == roleId ? getBlueRoleAction() : getRedRoleAction();
    }

    /**
     * 普攻兵损是否较少
     */
    public boolean isOrdinaryAttackSucc(long roleId) {
        if (roleId == getBlueRoleAction().getRoleId()) {
            return getBlueRoleAction().getCommonAtkLossCount() >= getRedRoleAction().getCommonAtkLossCount();
        } else {
            return getRedRoleAction().getCommonAtkLossCount() >= getBlueRoleAction().getCommonAtkLossCount();
        }
    }

    /**
     * 反击兵损是否较少
     */
    public boolean isAttackBackSucc(long roleId) {
        if (roleId == getBlueRoleAction().getRoleId()) {
            return getBlueRoleAction().getCommonAtkBackLossCount() >= getRedRoleAction().getCommonAtkBackLossCount();
        } else {
            return getRedRoleAction().getCommonAtkBackLossCount() >= getBlueRoleAction().getCommonAtkBackLossCount();
        }
    }

    public int getNumber() {
        return roundNumber;
    }

    public CommonBattle.BattleRound packCurRound() {
        CommonBattle.BattleRound log = null;
        if (blueRole != null && redRole != null) {
            log = CommonBattle.BattleRound.newBuilder()
                    .setRound(roundNumber)
                    .mergeBlueRound(getBlueRoleAction().convert2Pb(getRedRoleAction()))
                    .mergeRedRound(getRedRoleAction().convert2Pb(getBlueRoleAction()))
                    .build();
        }
        return log;
    }

    public void clear() {
        blueRole = null;
        redRole = null;
    }

    public boolean isEnd() {
        return blueRole == null || redRole == null;
    }
}


package com.yorha.cnc.battle.buf.lifecycle.impl;

import com.yorha.cnc.battle.buf.lifecycle.ILifeCycle;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum.LifeCycleType;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 */
public class TimeLife<PERSON>ycle implements ILifeCycle {
    private final int interval;
    private long endTime;
    private final LifeCycleType type;

    @Override
    public LifeCycleType getType() {
        return type;
    }

    @Override
    public long getValue() {
        return endTime;
    }

    public TimeLifeCycle(LifeCycleType type, int interval) {
        this.interval = interval;
        this.endTime = SystemClock.now() + interval;
        this.type = type;
    }


    public long getEndTime() {
        return endTime;
    }

    @Override
    public boolean checkValid() {
        return SystemClock.now() < this.endTime;
    }

    @Override
    public void execute() {

    }

    /**
     * 剩余信息
     * <p>
     * return 剩余信息提示
     */
    @Override
    public String getMessage() {
        long now = SystemClock.now();
        if (now < this.endTime) {
            long interval = TimeUtils.ms2Second(endTime - now);
            return MessageFormat.format("还有 {0}秒过期", interval);
        } else {
            return "已过期";
        }
    }

    @Override
    public void reset() {
        this.endTime = SystemClock.now() + interval;
    }
}

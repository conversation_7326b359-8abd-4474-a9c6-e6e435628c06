package com.yorha.cnc.battle.buf;

import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.core.BattleRole;

/**
 * 禁疗BUFF 阻止治疗
 * <AUTHOR>
 */
public class NoTreatmentBuff extends StateBuff {
    public NoTreatmentBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public BattleConstants.BattleRoleState getState() {
        return BattleConstants.BattleRoleState.NO_TREATMENT;
    }
}

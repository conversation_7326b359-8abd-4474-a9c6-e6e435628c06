package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;

import java.util.Set;

/**
 * 单兵种
 *
 * <AUTHOR>
 */

public class SoldierType<PERSON>hecker extends Status<PERSON>hecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        Set<Integer> types = role.containedAliveSoldierType();
        return types.size() == 1 && types.contains(Integer.parseInt(params[0]));
    }
}

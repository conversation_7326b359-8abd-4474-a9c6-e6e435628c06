package com.yorha.cnc.battle.soldier;

import com.yorha.proto.CommonEnum;

import javax.annotation.Nullable;

/**
 * 一个士兵单元，战斗中承受伤害/治疗的最小单元
 *
 * <AUTHOR>
 */
public class SoldierUnit {

    private final OwnerInfo ownerInfo;

    /**
     * 兵力数据
     */
    private final SoldierData data;

    public SoldierUnit(OwnerInfo ownerInfo, SoldierData data) {
        this.ownerInfo = ownerInfo;
        this.data = data;
    }

    /**
     * SoldierUnit的拥有者信息
     */
    public static class OwnerInfo {
        final long extensionId;
        final CommonEnum.SceneObjType objType4DamageCalc;

        public OwnerInfo(long extensionId, CommonEnum.SceneObjType objType4DamageCalc) {
            this.extensionId = extensionId;
            this.objType4DamageCalc = objType4DamageCalc;
        }

        public long getAdapterId() {
            return extensionId;
        }
    }

    public long getAdapterId() {
        return ownerInfo.extensionId;
    }

    public CommonEnum.SceneObjType getObjType4DamageCalc() {
        return ownerInfo.objType4DamageCalc;
    }

    public SoldierData getData() {
        return data;
    }

    public int maxCanTreat() {
        return data.getSlight();
    }

    public void applyLoss(SoldierLossData loss) {
        data.applyLoss(loss);
    }

    public int applyTreat(int treatSoldier) {
        int maxCanTreat = maxCanTreat();
        if (treatSoldier > maxCanTreat) {
            treatSoldier = maxCanTreat;
        }
        data.setSlight(data.getSlight() - treatSoldier);
        return treatSoldier;
    }

    public void addSoldier(int addNum) {
        data.setNum(data.getNum() + addNum);
    }

    @Override
    public String toString() {
        return "SoldierUnit{" +
                "data=" + data +
                '}';
    }
}

package com.yorha.cnc.battle.skill.status.impl;


import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.cnc.battle.core.BattleRole;

/**
 * 英雄星级
 *
 * <AUTHOR>
 */
public class HeroStarChecker extends <PERSON>Checker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        BattleHero hero = null;
        if (role.getMainHero() != null && role.getMainHero().getId() == effect.getExecutorInfo().getHeroId()) {
            hero = role.getMainHero();
        } else if (role.getDeputyHero() != null && role.getDeputyHero().getId() == effect.getExecutorInfo().getHeroId()) {
            hero = role.getDeputyHero();
        }
        if (hero != null) {
            return hero.getHeroProp().getStar() == Integer.parseInt(params[0]);
        }
        return false;
    }
}

package com.yorha.cnc.battle.record.action;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.soldier.SoldierLossData;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SoldierChangeAction {
    /**
     * memberRoleId -> <soldierId, SoldierLossData>
     */
    private final Map<Long, Map<Integer, SoldierLossData>> data;

    public SoldierChangeAction() {
        this.data = Maps.newHashMap();
    }

    public void plusSoldierData(long memberRoleId, int soldierId, SoldierLossData lossData) {
        data
                .computeIfAbsent(memberRoleId, v -> Maps.newHashMap())
                .computeIfAbsent(soldierId, v -> new SoldierLossData())
                .merge(lossData);
    }

    /**
     * 所有损失的士兵
     *
     * @return 返回的值表示损兵
     */
    public long sumWithoutTreatment() {
        return data.values()
                .stream()
                .mapToLong(it -> it.values().stream().mapToLong(SoldierLossData::totalLoss).sum())
                .sum();
    }

    /**
     * 所有损失的士兵
     *
     * @return 返回的是负值表示损兵，正值表示治疗士兵
     */
    public long sum() {
        return data.values()
                .stream()
                .mapToLong(it -> it.values().stream().mapToLong(SoldierLossData::sum).sum())
                .sum();
    }

    /**
     * 每个成员的士兵
     */
    public Map<Long, Long> allCountMap() {
        return data.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, it -> it.getValue().values().stream().mapToLong(SoldierLossData::totalCount).sum()));
    }

    /**
     * 所有士兵
     *
     * @return 返回的是负值表示损兵，正值表示治疗士兵
     */
    public Long allCount() {
        long count = 0;
        for (Map.Entry<Long, Map<Integer, SoldierLossData>> entry : data.entrySet()) {
            for (Map.Entry<Integer, SoldierLossData> vEntry : entry.getValue().entrySet()) {
                count += vEntry.getValue().totalCount();
            }
        }
        return count;
    }
}

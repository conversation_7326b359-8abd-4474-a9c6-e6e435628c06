package com.yorha.cnc.battle.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.buf.*;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.BattleArmyStateChangeEvent;
import com.yorha.cnc.battle.event.BuffEvent;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.cnc.battle.record.RoundRoleAction;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.skill.effect.impl.ImmunityBuffValue;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BattleBuffTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 战斗buff处理器
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public class BuffHandler extends BattleHandlerAbs<BattleRole> {
    private static final Logger LOGGER = LogManager.getLogger(BuffHandler.class);

    /**
     * 身上的buf[key:groupId,buff]
     */
    private final Map<Integer, Buff> buffStore = new HashMap<>();
    /**
     * 本回合预施加的buff key:groupId
     */
    private final Map<Integer, List<PendingBuff>> pendingBuffStore = new HashMap<>();

    public BuffHandler(BattleRole role) {
        super(role);
    }

    public void onTick() {
        update();
        mergePendingBuff(pendingBuffStore, true);
        if (buffStore.isEmpty() && pendingBuffStore.isEmpty()) {
            role.getGround().removeTickRole(role);
        }
    }

    /**
     * 删除buff
     */
    public void removeBuff(Buff buff) {
        removeBuff(buff, false, true);
    }

    public void removeBuffByTick(Buff buff) {
        removeBuff(buff, true, true);
    }

    /**
     * 删除buff
     */
    private void removeBuff(Buff buff, boolean isByTick, boolean needLog) {
        LOGGER.debug("BattleLog {} removeBattleBuff={}", role, buff.getTemplateId());
        if (!isByTick && role.isInTick()) {
            LOGGER.error("BuffHandler removeBuff on Tick, buffId={} buffGroupId={} effectId={} buffStoreContain={}", buff.getTemplateId(), buff.getGroupId(), buff.getExecutorInfo().getEffectId(), this.buffStore.containsKey(buff.getGroupId()));
        }
        this.buffStore.remove(buff.getGroupId());
        if (role.getAdapter().getBuffAdapter() != null) {
            role.getAdapter().getBuffAdapter().remove(buff.getGroupId());
        }
        buff.destroy(role);
        if (isByTick) {
            // 通过tick结束的需要发事件给战斗tick，因为buff的tick在战斗tick之前执行，此时战斗还在上一个回合
            if (role.getGround().isRoleActive(role)) {
                role.addBattleRoundEvent(new BuffEvent(CommonEnum.BattleLogBuffType.BLBT_SKILL, buff.getTemplateId(), BuffEvent.OperationType.REMOVE));
            }
        } else {
            if (needLog) {
                BattleLogUtil.logRemoveBuff(role, buff.getTemplateId());
            }
        }
    }

    /**
     * 新增BUF,需要融合
     *
     * @param buffId 增益id
     * @param layer  层数
     * @return null没加上
     */
    public Integer addBuff(BattleRole executor, int buffId, int layer, EffectContext effectContext) {
        return addBuffWithLife(executor, buffId, layer, 0, effectContext);
    }

    /**
     * 新增BUF,需要融合
     *
     * @param buffId    增益id
     * @param layer     层数
     * @param lifeCycle 生效时长， <=0取配表值
     * @return null没加上
     */
    public Integer addBuffWithLife(BattleRole executor, int buffId, int layer, int lifeCycle, EffectContext effectContext) {
        if (layer < 1) {
            LOGGER.error("BuffHandler addBuff fail, buffId={} layer={}", buffId, layer);
            return null;
        }
        List<SkillEffect> skillEffects = SkillSystem.getSkillEffects(role, CommonEnum.TriggerType.TT_ADD_BUFF);
        for (SkillEffect skillEffect : skillEffects) {
            // buff被免疫
            if (skillEffect.getTemplate().getType() == CommonEnum.SkillEffectType.SET_IMMUNITY && ImmunityBuffValue.isImmunized(executor, role, skillEffect, buffId)) {
                BattleLogUtil.logImmunityBuff(executor, role, buffId, effectContext);
                BattleGround.getBattleLog().printfImmunizeBuff(role, executor, buffId, skillEffect);
                return null;
            }
        }

        BattleBuffTemplate newTemp = ResHolder.getTemplate(BattleBuffTemplate.class, buffId);
        long realLifeCycle = lifeCycle > 0 ? lifeCycle : newTemp.getLifeCycleValue();
        PendingBuff pendingBuff = new PendingBuff(buffId, layer, role, effectContext, realLifeCycle);
        if (buffStore.isEmpty() && pendingBuffStore.isEmpty()) {
            role.getGround().addTickRole(role);
        }
        // 本回合立即生效
        if (newTemp.getEffectiveImmediately()) {
            Map<Integer, List<PendingBuff>> pendingBuffStore = new HashMap<>();
            pendingBuffStore.computeIfAbsent(newTemp.getEffectGroupId(), v -> Lists.newArrayList()).add(pendingBuff);
            Buff buff = mergePendingBuff(pendingBuffStore, false);
            return buff != null ? buffId : null;
        } else {
            LOGGER.debug("BattleLog {} addBattleBuff pendingBuff={}", role, pendingBuff);
            pendingBuffStore.computeIfAbsent(newTemp.getEffectGroupId(), v -> Lists.newArrayList()).add(pendingBuff);
            BattleLogUtil.logAddPendingBuff(role, pendingBuff, CommonEnum.BattleLogBuffType.BLBT_SKILL);
            return buffId;
        }
    }

    /**
     * 融合buff
     */
    private Buff mergePendingBuff(Map<Integer, List<PendingBuff>> pendingBuffStore, boolean isByTick) {
        Buff buff = null;
        boolean needActivateRole = false;
        for (Map.Entry<Integer, List<PendingBuff>> entry : pendingBuffStore.entrySet()) {
            Buff curBuff = buffStore.get(entry.getKey());
            PendingBuff beMergeBuff = null;
            boolean mergeHappened = false;

            // 把现有的buff拿出来，作为第一个被融合的
            if (curBuff != null) {
                beMergeBuff = new PendingBuff(curBuff.getTemplateId(), curBuff.getLayer(), null, curBuff.getExecutorInfo(), curBuff.getLifeCycle().getValue());
                beMergeBuff.setValue(curBuff.getValue());
            }

            // 融合buff
            for (PendingBuff toMergeBuff : entry.getValue()) {
                PendingBuff retBuff = merge(beMergeBuff, toMergeBuff);
                if (retBuff != null) {
                    // 日志相关
                    // 发生融合了，被融合掉的buff从日志中移除
                    if (beMergeBuff != null) {
                        if (isByTick && role.getGround().isRoleActive(role)) {
                            role.addBattleRoundEvent(new BuffEvent(CommonEnum.BattleLogBuffType.BLBT_SKILL, beMergeBuff.getBuffId(), BuffEvent.OperationType.REMOVE));
                        } else {
                            BattleLogUtil.logRemoveBuff(role, beMergeBuff.getBuffId());
                        }
                    }
                    beMergeBuff = retBuff;
                    mergeHappened = true;
                }
            }

            // 删除被顶替的buff，加新buff
            if (mergeHappened) {
                if (curBuff != null) {
                    // needLog = false，因为在上面已经删除过了
                    removeBuff(curBuff, isByTick, false);
                }
                buff = createAndAddBuff(beMergeBuff, isByTick);
                if (buff != null) {
                    // 日志相关
                    if (!isByTick) {
                        BattleLogUtil.logAddBuff(role, buff.getTemplateId(), CommonEnum.BattleLogBuffType.BLBT_SKILL);
                    }
                    buff.add(role);
                    buff.update(role);
                    if (!needActivateRole && buff.hasEffect()) {
                        needActivateRole = true;
                    }
                }
            }
        }
        pendingBuffStore.clear();

        if (needActivateRole) {
            // 有OverTimeBuff 需要激活Role
            role.getGround().tryActivateRole(role, "mergePendingBuff");
        }
        return buff;
    }

    /**
     * 融合buff
     *
     * @param curBuff   被融合的buff
     * @param toAddBuff 进行融合的buff
     * @return toAddBuff 融合成功；null 融合失败
     */
    private PendingBuff merge(PendingBuff curBuff, PendingBuff toAddBuff) {
        BattleRole executor = toAddBuff.getExecutorInfo().getRole();
        BattleBuffTemplate newTemp = ResHolder.getTemplate(BattleBuffTemplate.class, toAddBuff.getBuffId());
        if (curBuff == null) {
            return toAddBuff;
        }

        // 护盾buff有特殊逻辑
        if (CommonEnum.BuffEffectType.forNumber(newTemp.getType()) == CommonEnum.BuffEffectType.ET_SHIELD_BUF) {
            long newValue = ShieldBuff.getInitValue(executor, newTemp);
            if (newValue > curBuff.getValue()) {
                return toAddBuff;
            }
            return null;
        }

        // 相同id，或者优先级高的顶替掉原来的。如果可以叠加，则会继承原来的层数
        BattleBuffTemplate curTemp = ResHolder.getTemplate(BattleBuffTemplate.class, curBuff.getBuffId());
        if (curTemp.getId() == newTemp.getId() || newTemp.getEffectPriority() >= curTemp.getEffectPriority()) {
            // 新BUFF最大层数（0<=1代表替换）
            if (newTemp.getRuleValue() > 1) {
                // min(配置值， merge总层数)
                toAddBuff.setLayer(Math.min(newTemp.getRuleValue(), curBuff.getLayer() + toAddBuff.getLayer()));
            }
            return toAddBuff;
        }
        return null;
    }

    private Buff createAndAddBuff(PendingBuff pendingBuff, boolean isByTick) {
        Buff buff = createBuff(pendingBuff);
        if (buff == null) {
            return null;
        }
        if (!isByTick && role.isInTick()) {
            LOGGER.error("BuffHandler createAndAddBuff on Tick, buffId={} buffGroupId={} effectId={} buffStoreContain={}", buff.getTemplateId(), buff.getGroupId(), buff.getExecutorInfo().getEffectId(), this.buffStore.containsKey(buff.getGroupId()));
        }
        LOGGER.debug("BattleLog {} createAndAddBattleBuff:{}", role, pendingBuff);
        if (role.getAdapter().getBuffAdapter() != null) {
            role.getAdapter().getBuffAdapter().add(buff);
        }
        this.buffStore.put(buff.getGroupId(), buff);
        return buff;
    }

    private Buff createBuff(PendingBuff builder) {
        BattleBuffTemplate template = ResHolder.getTemplate(BattleBuffTemplate.class, builder.getBuffId());
        CommonEnum.BuffEffectType effectType = CommonEnum.BuffEffectType.forNumber(template.getType());
        if (effectType == null) {
            LOGGER.error("BattleErrLog {} createBuff failed, effectType not exist:{}, buffId:{}", role, template.getType(), builder.getBuffId());
            return null;
        }
        switch (effectType) {
            case ET_SHIELD_BUF:
                return new ShieldBuff(role, builder);
            case ET_SILENCE:
                return new SilenceBuff(role, builder);
            case ET_FROZEN:
                return new FrozenBuff(role, builder);
            case ET_DISARM:
                return new DisarmBuff(role, builder);
            case ET_INVINCIBLE:
                return new InvincibleBuff(role, builder);
            case ET_NO_TREATMENT:
                return new NoTreatmentBuff(role, builder);
            case ET_STEALTH:
                return new StealthBuff(role, builder);
            default:
                return new AdditionBuff(role, builder);
        }
    }

    /**
     * buff执行次数
     */
    private void update() {
        boolean needActivateRole = false;
        Iterator<Buff> buffIterator = buffStore.values().iterator();
        while (buffIterator.hasNext()) {
            Buff buff = buffIterator.next();
            boolean end = buff.update(role);
            if (end) {
                buffIterator.remove();
                removeBuffByTick(buff);
            } else {
                if (buff.hasEffect()) {
                    needActivateRole = true;
                }
            }
        }
        if (needActivateRole) {
            // 有OverTimeBuff 需要激活Role
            role.getGround().tryActivateRole(role, "BuffHandlerUpdate");
        }
    }

    /**
     * 获取相同效果的buff列表
     */
    public List<Buff> getBuffList(CommonEnum.BuffEffectType type) {
        List<Buff> ret = new ArrayList<>();
        for (Buff e : this.buffStore.values()) {
            if (e.getType() == type) {
                ret.add(e);
            }
        }
        return ret;
    }

    public Buff getBuff(int templateId) {
        for (Buff e : this.buffStore.values()) {
            if (e.getTemplateId() == templateId) {
                return e;
            }
        }
        return null;
    }

    public boolean hasBuff(int templateId) {
        for (Buff e : this.buffStore.values()) {
            if (e.getTemplateId() == templateId && e.isWorking()) {
                return true;
            }
        }
        return false;
    }

    public boolean hasBuff(CommonEnum.BuffEffectType type) {
        for (Buff e : this.buffStore.values()) {
            if (e.getType() == type && e.isWorking()) {
                return true;
            }
        }
        return false;
    }

    public boolean hasBuffByTag(int tag) {
        for (Buff e : this.buffStore.values()) {
            if (e.isWorking() && e.getTemplate().getTagList().contains(tag)) {
                return true;
            }
        }
        return false;
    }

    public boolean hasBuffByGroupId(int groupId) {
        for (Buff e : this.buffStore.values()) {
            if (e.getGroupId() == groupId && e.isWorking()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取相同效果的buff的值融合
     */
    public long getBuffValue(CommonEnum.BuffEffectType type) {
        long ret = 0;
        List<Buff> list = this.getBuffList(type);
        for (Buff e : list) {
            if (e.isWorking()) {
                ret += e.getValue();
            }
        }
        return ret;
    }

    /**
     * 根据类型删除buf
     */
    public void removeBuffByType(CommonEnum.BuffEffectType type) {
        List<Buff> list = this.buffStore.values().stream().filter(buff -> buff.getType() == type).collect(Collectors.toList());
        for (Buff e : list) {
            removeBuff(e);
        }
    }

    /**
     * 脱战清除buff
     */
    public void removeBuffOnOutBattle() {
        List<Buff> collect = this.buffStore.values().stream().filter(it -> it.getTemplate().getOutFightRemove() == 0).collect(Collectors.toList());
        for (Buff e : collect) {
            removeBuff(e);
        }

        List<Integer> toClear = Lists.newArrayList();
        for (Map.Entry<Integer, List<PendingBuff>> entry : pendingBuffStore.entrySet()) {
            entry.getValue().removeIf(it -> ResHolder.getTemplate(BattleBuffTemplate.class, it.getBuffId()).getOutFightRemove() == 0);
            if (entry.getValue().isEmpty()) {
                toClear.add(entry.getKey());
            }
        }
        for (Integer integer : toClear) {
            pendingBuffStore.remove(integer);
        }
    }

    public Map<CommonEnum.BuffEffectType, Long> getAllBuffValue() {
        Map<CommonEnum.BuffEffectType, Long> ret = Maps.newHashMap();
        Collection<Buff> list = this.buffStore.values();
        for (Buff e : list) {
            if (e.getValue() != 0 && e.isWorking()) {
                ret.put(e.getType(), ret.getOrDefault(e.getType(), 0L) + e.getValue());
            }
        }
        return ret;
    }

    public void logInitBuff(RoundRoleAction roleAction) {
        BattleLogUtil.logInitBuff(roleAction, buffStore);
    }

    public List<Buff> getAllBuff() {
        List<Buff> ret = Lists.newArrayList();
        Collection<Buff> list = this.buffStore.values();
        for (Buff e : list) {
            if (e.isWorking()) {
                ret.add(e);
            }
        }
        return ret;
    }

    public boolean removeBuffById(int buffId) {
        List<Buff> list = this.buffStore.values().stream().filter(buff -> buff.getTemplateId() == buffId).collect(Collectors.toList());
        for (Buff e : list) {
            removeBuff(e);
        }
        return !list.isEmpty();
    }

    public boolean removeBuffByTag(int tag) {
        List<Buff> list = this.buffStore.values().stream().filter(buff -> buff.getTemplate().getTagList().contains(tag)).collect(Collectors.toList());
        for (Buff e : list) {
            removeBuff(e);
        }
        return !list.isEmpty();
    }

    public boolean removeBuffByGroupId(int groupId) {
        List<Buff> list = this.buffStore.values().stream().filter(buff -> buff.getGroupId() == groupId).collect(Collectors.toList());
        for (Buff e : list) {
            removeBuff(e);
        }
        return !list.isEmpty();
    }

    public boolean needTick() {
        return !buffStore.isEmpty() || !pendingBuffStore.isEmpty();
    }

    /**
     * 检测行军状态，非预期状态需要打破隐身
     *
     * @return true:打破隐身
     */
    public boolean checkOrClearStealth(BattleArmyStateChangeEvent event) {
        switch (event.getNewState()) {
            case ADS_MOVE:
            case ADS_MOVE_BATTLE:
            case ADS_MOVE_COLLECT:
            case ADS_MOVE_RETURN:
            case ADS_STAYING:
            case ADS_MOVE_ASSIST:
            case ADS_MOVE_TRANSPORT: {
                return false;
            }
            default: {
                if (role.isInstate(BattleConstants.BattleRoleState.STEALTH)) {
                    removeBuffByType(CommonEnum.BuffEffectType.ET_STEALTH);
                }
                return false;
            }
        }
    }
}

package com.yorha.cnc.battle.context;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.protobuf.ByteString;
import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.skill.task.DelayTask;
import com.yorha.cnc.battle.soldier.SoldierLossDTO;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.game.gen.prop.*;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerMail;
import com.yorha.proto.Struct;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SoldierTypeTemplate;

import java.util.*;

/**
 * 战斗角色的上下文记录
 *
 * <AUTHOR>
 */
public class BattleRoleContext {
    private static final Logger LOGGER = LogManager.getLogger(BattleRoleContext.class);

    private final BattleRole role;
    /**
     * 战斗开始时士兵数量
     */
    private int totalAlive;
    /**
     * 当前剩余士兵数量
     */
    private int leftAlive;
    /**
     * 关联的战斗关系，战报发送后将会清理
     */
    private final List<BattleRelationContext> relationContextList;
    /**
     * enemyRoleId -> 我的 RoleRecord 对象
     */
    private final Map<Long, BattleRecord.RoleRecord> myRecordInRelation;
    /**
     * 回合集合，只用于战报中的折线图画线
     * 这个集合中不是每回合都有
     */
    private final Map<Integer, Round> roundMap;
    private final List<BattleRecord.RecordOne> settledSingleRecordOneList;
    /**
     * 刚刚加入战场
     */
    private boolean newBoy;
    /**
     * 延时任务
     */
    public final PriorityQueue<DelayTask> delayTaskList = new PriorityQueue<>(Comparator.comparingLong(DelayTask::getRunRound));
    /**
     * 开始战斗的回合
     */
    private int startBattleRoundId;
    public final Map<Long, List<DamageContext>> damageCtxMap = Maps.newHashMap();
    public final List<TreatmentContext> treatmentCtxMap = Lists.newArrayList();
    /**
     * 战报快照
     * 用于掠夺完成后发邮件
     * key: plunderId
     */
    public final Map<Long, BattleRecordMailSnapshot> mailSnapshotMap = Maps.newLinkedHashMap();
    /**
     * 等待处理的有掠夺的战斗关系
     */
    public final Set<Long> pendingPlunderRelationIds = Sets.newHashSet();

    public BattleRoleContext(BattleRole role) {
        this.role = role;
        this.relationContextList = Lists.newArrayList();
        this.myRecordInRelation = Maps.newHashMap();
        this.newBoy = true;
        this.settledSingleRecordOneList = Lists.newArrayList();
        this.startBattleRoundId = 0;
        this.roundMap = Maps.newHashMap();
    }

    public void clear() {
        if (role.isInSimulator()) {
            return;
        }
        relationContextList.clear();
        myRecordInRelation.clear();
        totalAlive = role.aliveCount();
        newBoy = true;
        settledSingleRecordOneList.clear();
        leftAlive = role.aliveCount();
        startBattleRoundId = 0;
        roundMap.clear();
        pendingPlunderRelationIds.clear();
    }

    public boolean nothingHappened() {
        // 默认会有一个初始round
        return getCurRoundId() <= 1;
    }

    public static class Round {
        private final int round;
        private int aliveCount;
        private final List<IRoundEvent> eventList;

        public Round(int round) {
            this.round = round;
            this.eventList = Lists.newArrayList();
        }

        public int getAliveCount() {
            return aliveCount;
        }

        public void setAliveCount(int aliveCount) {
            this.aliveCount = aliveCount;
        }

        public int getRound() {
            return round;
        }

        public Round addEvent(IRoundEvent event) {
            eventList.add(event);
            return this;
        }

        public boolean isFirstRound() {
            return round == 1;
        }

        public List<IRoundEvent> getEventList() {
            return eventList;
        }
    }

    public static class RoundTroopEvent extends IRoundEvent {
        private final CommonEnum.BattleLogTroopChangeType type;
        private final String name;
        private final String clanName;
        private final Struct.PlayerCardHead cardHead;
        private final int soldierNum;

        public RoundTroopEvent(CommonEnum.BattleLogTroopChangeType type, String name, String clanName, Struct.PlayerCardHead cardHead, int soldierNum) {
            this.type = type;
            this.name = name;
            this.clanName = clanName;
            this.cardHead = cardHead;
            this.soldierNum = soldierNum;
        }

        public BattleRecordTroopEventProp convert2Prop(boolean omitCardHead) {
            BattleRecordTroopEventProp prop = new BattleRecordTroopEventProp()
                    .setType(type)
                    .setClanName(clanName)
                    .setSoldierNum(soldierNum);
            if (!omitCardHead) {
                prop.getCardHead()
                        .setName(name)
                        .setPic(cardHead.getPic())
                        .setPicFrame(cardHead.getPicFrame());
            }
            return prop;
        }
    }

    public static abstract class IRoundEvent {
    }

    public Round drawRoundPoint() {
        int roundId = getCurRoundId();
        return roundMap.computeIfAbsent(getCurRoundId(), v -> new Round(roundId));
    }

    public Round getCurRoundOrNull() {
        return roundMap.get(getCurRoundId());
    }

    public void removeRelation(long enemyRoleId, BattleRelationContext context) {
        relationContextList.remove(context);
        myRecordInRelation.remove(enemyRoleId);
    }

    public void addRelation(long enemyRoleId, BattleRelationContext relationContext, boolean isInit) {
        if (relationContextList.isEmpty() && isInit) {
            startBattleRoundId = role.getGround().getGroundRound();
            int curAliveCount = role.aliveCount();
            totalAlive = curAliveCount;
            leftAlive = curAliveCount;
            // 第一回合要记录开战前的兵力
            drawRoundPoint().setAliveCount(curAliveCount);
        } else {
            drawRoundPoint();
        }
        BattleRecord.RoleRecord roleRecord = relationContext.getRoleRecord(role.getRoleId());
        relationContextList.add(relationContext);
        myRecordInRelation.put(enemyRoleId, roleRecord);
        // 填充BattleRole成员信息
        role.getAdapter().fillRoleMember(roleRecord);
    }

    /**
     * 吐出所有战报内容，并清理
     */
    public BattleRecordAllProp flushRecord() {
        BattleRecordAllProp recordAllProp = new BattleRecordAllProp();
        // Npc部队、gvg以外副本、模拟不要战报
        if (role.isNpcTroop() || !role.needBattleRecord() || role.isInSimulator()) {
            clear();
            return recordAllProp;
        }

        try {
            List<BattleRecord.RecordOne> recordOnes = preFlush(recordAllProp);
            flush(recordAllProp, recordOnes);
        } catch (Exception e) {
            WechatLog.error("BattleErrLog flushRecord fail", e);
        } finally {
            clear();
        }
        return recordAllProp;
    }

    private List<BattleRecord.RecordOne> preFlush(BattleRecordAllProp recordAllProp) {
        long recordId = IdFactory.nextId("battle_record");
        recordAllProp.setRecordId(recordId);
        // 最后一个回合记录结束兵力
        drawRoundPoint().setAliveCount(leftAlive);
        // 画战报折线图
        genRoundPoint(recordAllProp);
        // 填充概要中兵力
        recordAllProp.getSelfSummary().setTotal(totalAlive);
        recordAllProp.getSelfSummary().setLeftAlive(leftAlive);

        // 构建待flush的RecordOne
        List<BattleRecord.RecordOne> recordOnes = Lists.newArrayList();
        for (BattleRelationContext relationContext : relationContextList) {
            if (!relationContext.needFlush()) {
                continue;
            }
            recordOnes.add(relationContext.getRecordOne());
        }
        recordOnes.addAll(settledSingleRecordOneList);

        // 依据开始时间排序
        recordOnes.sort(Comparator.comparingLong(BattleRecord.RecordOne::getStartMs));
        return recordOnes;
    }

    private void flush(BattleRecordAllProp recordAllProp, List<BattleRecord.RecordOne> recordOnes) {
        if (recordOnes.isEmpty()) {
            LOGGER.error("BattleErrLog role={} flush record failed, recordOnes is empty", role);
            return;
        }
        // 合并战斗记录
        recordOnes = mergeRecordOneList(recordOnes);

        for (BattleRecord.RecordOne recordOne : recordOnes) {
            BattleRecordOneProp recordOneProp = recordOne.convert2Prop(role);
            recordAllProp.addSingleRecordList(recordOneProp);
            // 设置战报总体开始结束时间
            if (recordAllProp.getStartMillis() <= 0 || recordOneProp.getStartMillis() < recordAllProp.getStartMillis()) {
                recordAllProp.setStartMillis(recordOneProp.getStartMillis());
            }
            if (recordAllProp.getEndMillis() <= 0 || recordOneProp.getEndMillis() > recordAllProp.getEndMillis()) {
                recordAllProp.setEndMillis(recordOneProp.getEndMillis());
            }
        }
        // 填充在战报概要
        role.getAdapter().fillRoleSummary(recordAllProp);
        // 存战斗详情
        saveRecordDetail(recordAllProp.getRecordId(), recordOnes);
    }

    public void buildRecordSnapshot(boolean isMeAlive, boolean isAnyEnemyAlive, Set<Long> needSendRecordPlayerIds) {
        BattleRecordAllProp recordAllProp = new BattleRecordAllProp();
        try {
            List<BattleRecord.RecordOne> recordOnes = preFlush(recordAllProp);
            BattleRecordMailSnapshot snapshot = new BattleRecordMailSnapshot(isAnyEnemyAlive, isMeAlive, recordAllProp, recordOnes, Sets.newHashSet(pendingPlunderRelationIds), needSendRecordPlayerIds);
            mailSnapshotMap.put(snapshot.recordAllProp.getRecordId(), snapshot);
            if (ServerContext.isTestEnv() || ServerContext.isDevEnv()) {
                LOGGER.info("BattleLog plunder process recordId:{} waiting for plunder:{}", snapshot.recordAllProp.getRecordId(), pendingPlunderRelationIds);
            }
        } catch (Exception e) {
            WechatLog.error("BattleErrLog buildRecordSnapshot fail", e);
        } finally {
            clear();
        }
    }

    public BattleRecordMailSnapshot flushSnapshot(long recordId) {
        if (ServerContext.isTestEnv() || ServerContext.isDevEnv()) {
            LOGGER.info("BattleLog plunder process {} flushSnapshot recordId:{}", role, recordId);
        }
        BattleRecordMailSnapshot ctx = mailSnapshotMap.remove(recordId);
        try {
            flush(ctx.recordAllProp, ctx.recordOnes);
        } catch (Exception e) {
            WechatLog.error("BattleErrLog flushSnapshot fail recordId={} ", recordId, e);
        }
        return ctx;
    }

    /**
     * 合并战斗记录
     * <p>
     * 对于同一个enemyRole合并战斗记录
     */
    private List<BattleRecord.RecordOne> mergeRecordOneList(List<BattleRecord.RecordOne> settledSingleRecordOneList) {
        List<BattleRecord.RecordOne> ret = Lists.newArrayList();
        Map<Long, List<BattleRecord.RecordOne>> recordOneToMerge = Maps.newHashMap();
        // 找出需要合并的
        for (BattleRecord.RecordOne recordOne : settledSingleRecordOneList) {
            if (!recordOne.isNeedMerge()) {
                ret.add(recordOne);
                continue;
            }
            long enemyRoleId = 0;
            if (recordOne.getOneRecord().getRoleId() == role.getRoleId()) {
                enemyRoleId = recordOne.getOtherRecord().getRoleId();
            } else if (recordOne.getOtherRecord().getRoleId() == role.getRoleId()) {
                enemyRoleId = recordOne.getOneRecord().getRoleId();
            }

            if (enemyRoleId == 0) {
                LOGGER.error("BattleErrLog mergeRecordOneList failed, role:{}, one:{}, other:{}", role, recordOne.getOneRecord().getRoleId(), recordOne.getOtherRecord().getRoleId());
                continue;
            }

            recordOneToMerge.computeIfAbsent(enemyRoleId, v -> Lists.newArrayList()).add(recordOne);
        }

        boolean mergeHappens = false;
        for (Map.Entry<Long, List<BattleRecord.RecordOne>> entry : recordOneToMerge.entrySet()) {
            BattleRecord.RecordOne mergedRecord = entry.getValue().get(0);
            if (entry.getValue().size() > 1) {
                mergeHappens = true;
                // 合并
                for (int i = 1; i < entry.getValue().size(); i++) {
                    mergedRecord.merge(entry.getValue().get(i));
                }
            }

            ret.add(mergedRecord);
        }
        if (mergeHappens) {
            LOGGER.debug("BattleLog role:{} mergeHappens, old:{}, new:{}", role, settledSingleRecordOneList, ret);
        }
        return ret;
    }

    private void saveRecordDetail(long recordId, List<BattleRecord.RecordOne> recordOneList) {
        for (BattleRecord.RecordOne recordOne : recordOneList) {
            PlayerMail.BattleRecordDetail detail = recordOne.genDetailProto(role);
            if (detail != null) {
                TcaplusDb.BattleRecordDetailTable.Builder req = TcaplusDb.BattleRecordDetailTable.newBuilder()
                        .setRecordId(recordId)
                        .setBattleId(recordOne.getBattleId())
                        .setDetail(ByteString.copyFrom(detail.toByteArray()));

                InsertAsk<TcaplusDb.BattleRecordDetailTable.Builder> ask = new InsertAsk<>(req);
                role.getGround().getAdapter().tellGameDb(ask);
            }
        }
    }

    private void genRoundPoint(BattleRecordAllProp recordAllProp) {
        // 画兵力折线图
        drawSoldierNumPoint(recordAllProp);
        // 画回合事件
        drawRoundEventPoint(recordAllProp);
    }

    private void drawSoldierNumPoint(BattleRecordAllProp recordAllProp) {
        // 每隔X回合记录一次兵力
        int gapRound = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getBattleReportPointGapNum();
        // 期望折线图被分为X段
        int perSegment = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getBattleReportPointSegmentNum();
        // 战斗持续总回合数
        int totalRound = getCurRoundId();
        // S: 是折线图被分为perSegment段
        int S = MathUtils.roundInt((double) totalRound / perSegment / gapRound);

        if (S <= 1) {
            // <=1 全取
            for (Round round : roundMap.values()) {
                addRoundPoint(recordAllProp, round);
            }
        } else {
            // > 1 分段取
            // 逻辑有点麻烦，我也不会表述了，看案子把
            int segment = totalRound / gapRound - 1;
            // 头尾必须有
            Round roundStart = roundMap.get(1);
            if (roundStart != null) {
                addRoundPoint(recordAllProp, roundStart);
            } else {
                LOGGER.warn("BattleLog drawSoldierNumPoint failed, roundStart is null curRound={} round={} startRound={} rounds={}", getCurRoundId(), role.getGround().getGroundRound(), startBattleRoundId, roundMap.keySet());
            }
            Round roundEnd = roundMap.get(getCurRoundId());
            if (roundEnd != null) {
                addRoundPoint(recordAllProp, roundEnd);
            } else {
                LOGGER.warn("BattleLog drawSoldierNumPoint failed, roundEnd is null curRound={} round={} startRound={} rounds={}", getCurRoundId(), role.getGround().getGroundRound(), startBattleRoundId, roundMap.keySet());
            }
            for (int i = 0; i < segment; i++) {
                int roundId = (S + i) * gapRound;
                if (roundId != 1 && roundId != getCurRoundId()) {
                    Round round = roundMap.get(roundId);
                    if (round == null) {
                        LOGGER.warn("BattleLog drawSoldierNumPoint failed, roundId:{}, curRoundId:{}, allRounds:{}", roundId, getCurRoundId(), roundMap.keySet());
                        continue;
                    }
                    addRoundPoint(recordAllProp, round);
                }
            }
        }
    }

    private void addRoundPoint(BattleRecordAllProp recordAllProp, Round round) {
        BattleRecordRoundAliveProp roundAliveProp = new BattleRecordRoundAliveProp()
                .setRound(round.getRound())
                .setAlive(round.getAliveCount());
        recordAllProp.addRoundAliveList(roundAliveProp);
    }

    private void drawRoundEventPoint(BattleRecordAllProp recordAllProp) {
        boolean omitCardHead = false;
        int sumEventCount = 0;
        for (Round round : roundMap.values()) {
            sumEventCount += round.getEventList().size();
            // 回合事件总数超过500即省略玩家头像信息
            if (sumEventCount >= BattleConstants.BATTLE_REPORT_CARD_OMIT_FACTOR) {
                omitCardHead = true;
                break;
            }
        }
        for (Round round : roundMap.values()) {
            BattleRecordRoundEventProp roundEventProp = null;

            for (IRoundEvent iRoundEvent : round.getEventList()) {
                if (iRoundEvent instanceof RoundTroopEvent) {
                    RoundTroopEvent event = (RoundTroopEvent) iRoundEvent;
                    if (roundEventProp == null) {
                        roundEventProp = new BattleRecordRoundEventProp().setRound(round.getRound());
                    }
                    roundEventProp.addTroopEventList(event.convert2Prop(omitCardHead));
                }
            }

            if (roundEventProp != null) {
                recordAllProp.addRoundEventList(roundEventProp);
            }
        }
    }

    private void settleRoundPoint() {
        Round curRound;
        int gapRound = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getBattleReportPointGapNum();
        // 每固定X回合记一回合
        if (getCurRoundId() % gapRound == 0) {
            curRound = drawRoundPoint();
        } else {
            curRound = getCurRoundOrNull();
        }
        if (curRound != null && !curRound.isFirstRound()) {
            // 记录本回合画点的兵力
            curRound.setAliveCount(leftAlive);
        }
    }

    public void settleRound(DamageResult damageResult) {
        leftAlive = role.aliveCount();
        settleRoundPoint();

        for (SoldierLossDTO lossDTO : damageResult.getSoldierLossList()) {
            final int soldierId = lossDTO.getSoldierId();
            // 记录治疗
            if (lossDTO.getLossData().getTreatment() > 0) {
                for (BattleRecord.RoleRecord roleRecord : myRecordInRelation.values()) {
                    roleRecord.addTreatment(lossDTO.getLossData().getTreatment());
                    // 战报中成员治疗数据
                    for (Map.Entry<Long, SoldierLossData> entry : lossDTO.getChildLossMap().entrySet()) {
                        roleRecord.addMbrTreatment(entry.getKey(), soldierId, entry.getValue().getTreatment());
                    }
                }
            }

            // 记录死伤
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(soldierId);
            lossDTO.getLossSourceMap().forEach((attackerId, lossData) -> {
                final BattleRecord.RoleRecord roleRecord = myRecordInRelation.get(attackerId);
                if (roleRecord != null) {
                    // 战报中损失战力统计
                    int lossPower = (lossData.getSevere() + lossData.getDead()) * soldierTemplate.getPower();
                    roleRecord.addLostPower(lossPower);
                    // 战报中伤兵数据
                    roleRecord.addSlightWound(lossData.getSlight());
                    roleRecord.addSevereWound(lossData.getSevere());
                    roleRecord.addDead(lossData.getDead());
                    // 战报中成员伤兵数据
                    for (Map.Entry<Long, SoldierLossData> entry : lossDTO.getChildLossSourceMap(attackerId).entrySet()) {
                        roleRecord.addMbrSlightWound(entry.getKey(), soldierId, entry.getValue().getSlight());
                        roleRecord.addMbrSevereWound(entry.getKey(), soldierId, entry.getValue().getSevere());
                        roleRecord.addMbrDead(entry.getKey(), soldierId, entry.getValue().getDead());
                        int memberLossPower = (entry.getValue().getSevere() + entry.getValue().getDead()) * soldierTemplate.getPower();
                        roleRecord.addMemberLostPower(entry.getKey(), memberLossPower);
                    }
                } else {
                    // DOT伤害确实会没有record，只要warn就可以了
                    LOGGER.warn("BattleLog self:{} attackerId:{} get roleRecord null, myRecordInRelation:{}, lossMap:{}", this.role.getRoleId(), attackerId, myRecordInRelation, lossDTO.getLossSourceMap());
                }
            });
        }
        // 防御塔香瓜
        if (damageResult.getGuardTowerLoss() != null) {
            damageResult.getGuardTowerLoss().getLossSourceMap().forEach((attackerId, lossData) -> {
                final BattleRecord.RoleRecord roleRecord = myRecordInRelation.get(attackerId);
                if (roleRecord != null) {
                    roleRecord.addGuardTowerDead(lossData.getDead());
                }
            });
        }
    }

    public void addSettledRecordOne(BattleRecord.RecordOne recordOne) {
        settledSingleRecordOneList.add(recordOne);
    }

    public boolean isNewBoy() {
        return newBoy;
    }

    public void setNewBoy(boolean newBoy) {
        this.newBoy = newBoy;
    }

    public void addTask(DelayTask task) {
        delayTaskList.add(task);
    }

    public int getCurRoundId() {
        return role.getGround().getGroundRound() - startBattleRoundId + 1;
    }

    public void onEndSingleRelation() {
        drawRoundPoint().setAliveCount(leftAlive);
    }

    public Map<Long, List<DamageContext>> getDamageCtxMap() {
        return damageCtxMap;
    }

    public void addDamageCtx(DamageContext dmgCtx) {
        damageCtxMap.computeIfAbsent(dmgCtx.getAttackerId(), v -> Lists.newArrayList())
                .add(dmgCtx);
    }

    public List<TreatmentContext> getTreatmentCtxLists() {
        return treatmentCtxMap;
    }

    public void addTreatmentCtx(TreatmentContext dmgCtx) {
        treatmentCtxMap.add(dmgCtx);
    }

    public List<BattleRelationContext> getRelationContextList() {
        return relationContextList;
    }

    public boolean isWaitingForPlunder() {
        return !pendingPlunderRelationIds.isEmpty();
    }
}

package com.yorha.cnc.battle.buf;

import com.yorha.cnc.battle.core.BattleRole;

import com.yorha.proto.CommonEnum;

/**
 * 加成类buff
 *
 * <AUTHOR>
 * 2021年10月29日 16:07:00
 */
public class AdditionBuff extends Buff {
    public AdditionBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public void execute(BattleRole owner) {
        CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(getTemplate().getType());
        if (buffEffectType != null) {
            if (owner.getAdapter().getAdditionAdapter() != null) {
                owner.getAdapter().getAdditionAdapter().dispatchAdditionChange(buffEffectType);
            }
        }
    }

    @Override
    public void destroy(BattleRole owner) {
        CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(getTemplate().getType());
        if (buffEffectType != null) {
            if (owner.getAdapter().getAdditionAdapter() != null) {
                owner.getAdapter().getAdditionAdapter().dispatchAdditionChange(buffEffectType);
            }
        }
    }

    @Override
    public void add(BattleRole owner) {

    }
}

package com.yorha.cnc.battle.snapshot.unit;

import com.yorha.cnc.battle.core.BattleAddCalc;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.soldier.GuardTower;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.DefenseTowerTemplateService;
import com.yorha.proto.CommonEnum;
import res.template.DefenseTowerStatusTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class GuardTowerSnapshot extends AbstractBattleSoldierSnapshot {
    private final long maxHp;
    private final double guardRatio;

    public GuardTowerSnapshot(BattleRole role, GuardTower tower, Map<CommonEnum.BuffEffectType, Long> additionMap) {
        super(role, tower, additionMap);
        this.maxHp = tower.getMaxHp();
        this.guardRatio = tower.getGuardRatio();
    }

    @Override
    public double calcAttack(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return BattleAddCalc.getGuardTowerAtk(role, getTemplate(), roleAdditionSnapshot);
    }

    @Override
    public double calcDefence(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return BattleAddCalc.getGuardTowerDef(role, getTemplate(), roleAdditionSnapshot);
    }

    @Override
    public double calcHp(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return getTemplate().getHp();
    }

    @Override
    public double calcBaseHp(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot){
        return getHp();
    }
    @Override
    public int getMaxCanTreat() {
        return 0;
    }

    public long getMaxHp() {
        return maxHp;
    }

    public int healthRatio() {
        return (int) Math.ceil((double) aliveCount() / maxHp) * 100;
    }

    public float getAtkRate() {
        DefenseTowerTemplateService templateService = ResHolder.getResService(DefenseTowerTemplateService.class);
        DefenseTowerStatusTemplate statusTemplate = templateService.getDefenseTowerStatusTemplate(healthRatio());
        if (statusTemplate == null) {
            return 0;
        }
        return statusTemplate.getAtkRate();
    }

    public double getGuardRatio() {
        return guardRatio;
    }

    @Override
    public String toString() {
        return "GuardTowerSnapshot{" +
                "maxHp=" + maxHp +
                "guardRatio=" + guardRatio +
                '}';
    }
}

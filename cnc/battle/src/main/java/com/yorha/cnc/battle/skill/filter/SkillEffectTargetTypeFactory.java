package com.yorha.cnc.battle.skill.filter;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.skill.filter.impl.AllEnemyFilter;
import com.yorha.cnc.battle.skill.filter.impl.AlliesFilter;
import com.yorha.cnc.battle.skill.filter.impl.EnemyFilter;
import com.yorha.proto.CommonEnum;
import res.template.SkillEffectTemplate;

import javax.annotation.Nullable;
import java.util.Map;

/**
 * 技能效果的目标选择逻辑
 *
 * <AUTHOR>
 */
public class SkillEffectTargetTypeFactory {

    private static final Map<CommonEnum.EnemyRelation, ISkillEffectRelationFilter> TARGET_TYPE_MAP = Maps.newEnumMap(CommonEnum.EnemyRelation.class);

    static {
        // 敌方
        TARGET_TYPE_MAP.put(CommonEnum.EnemyRelation.ERL_ENEMY, new EnemyFilter());
        // 友方
        TARGET_TYPE_MAP.put(CommonEnum.EnemyRelation.ERL_FRIENDLY, new AlliesFilter());
        // 非友方
        TARGET_TYPE_MAP.put(CommonEnum.EnemyRelation.ERL_ALL_ENEMY, new AllEnemyFilter());
    }

    @Nullable
    public static ISkillEffectRelationFilter of(SkillEffectTemplate template) {
        return TARGET_TYPE_MAP.get(template.getEnemyRelation());
    }


}

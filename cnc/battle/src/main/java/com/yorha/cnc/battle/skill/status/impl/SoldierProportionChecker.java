package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.common.utils.MathUtils;

/**
 * 兵力占比检测
 * 存活兵力小于X万分比
 *
 * <AUTHOR>
 */
public class SoldierProportionChecker extends StatusChecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        double soldierMax = role.getSoldierMax();
        if (soldierMax <= 0){
            // 策划需求，无兵力时按true处理
            return true;
        }
        double soldier = role.soldierAliveCount();
        double rate = soldier / soldierMax;
        return rate <= MathUtils.tenThousandPoint(Integer.parseInt(params[0]));
    }
}

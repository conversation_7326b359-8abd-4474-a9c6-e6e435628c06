package com.yorha.cnc.battle.skill;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.proto.CommonEnum;
import res.template.SkillConfigTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Skill extends SkillFacade {

    private List<Integer> array = new ArrayList<>();

    public Skill(int skillId, int heroId, BattleRole role) {
        super(skillId, heroId, role);
    }

    public SkillConfigTemplate getSkillTemplate() {
        return ResHolder.getResService(SkillDataTemplateService.class).getSkillTemplate(id);
    }

    public int getSingTime() {
        return 1;
    }

    @Override
    public List<Integer> getEffectIdList() {
        if (array == null) {
            array = new ArrayList<>();
            array.add(getSkillTemplate().getBuffId0());
            int extra = getSkillTemplate().getBuffId1();
            if (extra > 0) {
                array.add(extra);
            }
        }
        return array;
    }

    @Override
    protected CommonEnum.BattleLogSkillType getBattleLogSkillType() {
        return CommonEnum.BattleLogSkillType.BLST_SKILL;
    }
}

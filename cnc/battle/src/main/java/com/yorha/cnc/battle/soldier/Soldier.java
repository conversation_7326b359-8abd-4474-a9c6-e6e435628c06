package com.yorha.cnc.battle.soldier;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.game.gen.prop.SoldierProp;
import com.yorha.common.wechatlog.WechatLog;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 同一soldierId的兵团，由一个或多个SoldierUnit组成
 *
 * <AUTHOR>
 */
public class Soldier extends BattleSoldier {
    private final int soldierId;
    /**
     * 组成同一个Soldier的所有单元，这里对于所有单unit的Soldier来说有一个List的开销
     */
    private final List<SoldierUnit> soldierUnitList;

    public Soldier(BattleRole battleRole, int soldierId) {
        super(battleRole);
        this.soldierId = soldierId;
        this.soldierUnitList = Lists.newArrayListWithExpectedSize(1);
    }

    private SoldierUnit findUnit(long childRoleId) {
        for (SoldierUnit soldierUnit : soldierUnitList) {
            if (soldierUnit.getAdapterId() == childRoleId) {
                return soldierUnit;
            }
        }
        return null;
    }

    public void add4ExistUnit(BattleRole role, int addNum) {
        SoldierUnit existUnit = findUnit(role.getRoleId());
        if (existUnit == null) {
            WechatLog.error("BattleErrLog add4ExistUnit unit not found! {} data={}", role, existUnit);
            return;
        }
        existUnit.addSoldier(addNum);
    }

    public void addUnit(SoldierUnit.OwnerInfo ownerInfo, SoldierData data) {
        SoldierUnit existUnit = findUnit(ownerInfo.getAdapterId());
        if (existUnit != null) {
            WechatLog.error("BattleErrLog addUnit but unit exist! unit={}, toAddData={}, role={}", existUnit, data.getSoldierId(), ownerInfo.getAdapterId());
        }
        SoldierUnit unit = new SoldierUnit(ownerInfo, data);
        soldierUnitList.add(unit);
    }

    public void dropUnit(long roleId, boolean careRemoved) {
        boolean removed = soldierUnitList.removeIf(child -> child.getAdapterId() == roleId);
        if (careRemoved && !removed) {
            WechatLog.error("BattleErrLog dropUnit failed: role={} soldierId={}", roleId, soldierId);
        }
    }

    /**
     * 全员满血
     */
    public void recover() {
        for (SoldierUnit unit : soldierUnitList) {
            unit.getData().recover();
        }
    }

    /**
     * 健康士兵数量
     */
    @Override
    public int aliveCount() {
        int sum = 0;
        for (SoldierUnit unit : this.soldierUnitList) {
            sum += unit.getData().aliveCount();
        }
        return sum;
    }

    public int aliveAndSlight() {
        int sum = 0;
        for (SoldierUnit unit : this.soldierUnitList) {
            sum += unit.getData().aliveAndSlight();
        }
        return sum;
    }

    public int tempAliveCount() {
        int sum = 0;
        for (SoldierUnit unit : this.soldierUnitList) {
            sum += unit.getData().tempAliveCount();
        }
        return sum;
    }

    /**
     * 获取所有轻伤兵数量
     */
    public int getSlight() {
        int sum = 0;
        for (SoldierUnit unit : this.soldierUnitList) {
            sum += unit.getData().getSlight();
        }
        return sum;
    }

    @Override
    public int getId() {
        return soldierId;
    }

    public Map<Long, Integer> aliveCountByMember() {
        Map<Long, Integer> ret = Maps.newHashMap();
        for (SoldierUnit unit : this.soldierUnitList) {
            ret.put(unit.getAdapterId(), unit.getData().aliveCount());
        }
        return ret;
    }

    public static int aliveCountOf(SoldierProp soldierProp) {
        return Math.max(0, soldierProp.getNum() - soldierProp.getSlightWoundNum() - soldierProp.getSevereWoundNum() - soldierProp.getDeadNum());
    }

    public static int aliveAndSlight(SoldierProp prop) {
        return Math.max(0, prop.getNum() - prop.getSevereWoundNum() - prop.getDeadNum());
    }

    @Override
    public int getMax() {
        int max = 0;
        for (SoldierUnit unit : soldierUnitList) {
            max += unit.getData().getNum();
        }
        return max;
    }

    @Override
    public String toString() {
        return "Soldier{" +
                "soldierId=" + soldierId +
                ", soldierUnitList=" + soldierUnitList +
                ", battleRole=" + battleRole +
                '}';
    }

    public static void plusMergeProp(SoldierProp origin, SoldierProp toAdd) {
        if (origin.getSoldierId() != toAdd.getSoldierId()) {
            WechatLog.error("BattleErrLog soldierId {} != {}", origin.getSoldierId(), toAdd.getSoldierId());
            return;
        }
        origin.setNum(origin.getNum() + toAdd.getNum());
        origin.setSlightWoundNum(origin.getSlightWoundNum() + toAdd.getSlightWoundNum());
        origin.setSevereWoundNum(origin.getSevereWoundNum() + toAdd.getSevereWoundNum());
        origin.setDeadNum(origin.getDeadNum() + toAdd.getDeadNum());
    }

    public static void copyProp(SoldierProp from, SoldierProp to) {
        to.setNum(from.getNum());
        to.setSlightWoundNum(from.getSlightWoundNum());
        to.setSevereWoundNum(from.getSevereWoundNum());
        to.setDeadNum(from.getDeadNum());
    }

    public void forEachUnit(Consumer<SoldierUnit> consumer) {
        soldierUnitList.forEach(consumer);
    }

    public List<SoldierUnit> getSoldierUnitList() {
        return soldierUnitList;
    }

    public int getMaxCanTreat() {
        return soldierUnitList.stream().mapToInt(SoldierUnit::maxCanTreat).sum();
    }
}
package com.yorha.cnc.battle.core;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.common.ActionType;
import com.yorha.cnc.battle.common.Amend;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.DamageContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.context.TreatmentContext;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.cnc.battle.snapshot.group.SoldierGroupSnapshot;
import com.yorha.cnc.battle.snapshot.unit.GuardTowerSnapshot;
import com.yorha.cnc.battle.snapshot.unit.SoldierSnapshot;
import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.constant.Constants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstBattleTemplate;
import res.template.MonsterTemplate;
import res.template.SkillConfigTemplate;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

import static com.yorha.common.constant.Constants.N_100;

/**
 * 战斗公式
 *
 * <AUTHOR>
 */
public class BattleFormula {
    private static final Logger LOGGER = LogManager.getLogger(BattleFormula.class);

    /**
     * 百分比治疗
     */
    public static TreatmentContext stdApplyPercentageTreatment(BattleRoleSnapshot attacker,
                                                               BattleRoleSnapshot target,
                                                               ActionContext actionCtx,
                                                               EffectContext effectContext,
                                                               Amend treatmentAmend) {
        TreatmentContext treatmentCtx = new TreatmentContext(actionCtx, effectContext);
        // 部队最大可治疗士兵数
        int maxRoleCanTreat = target.getRallyCapacity() - target.soldierAliveCount();
        // 百分比治疗系数 * (1 + [受击方]受到治疗效果提升)
        calcSkillPercentageTreatmentSoldierAmend(attacker, target, treatmentAmend);
        for (SoldierGroupSnapshot group : target.getGroupSnapshotMap().values()) {
            for (SoldierSnapshot soldier : group.getSoldiers()) {
                if (maxRoleCanTreat <= 0) {
                    break;
                }
                if (soldier.aliveCount() <= 0) {
                    continue;
                }

                // 最大可治疗士兵数, min(轻伤数 - 本回合已被治疗数, 部队剩余容量)
                int maxCanTreat = Math.min(soldier.getMaxCanTreat() - soldier.getCacheTreat(), maxRoleCanTreat);
                if (maxCanTreat <= 0) {
                    continue;
                }

                // 兵力回复 = 百分比治疗系数 * (1 + [受击方]受到治疗效果提升) * 兵种初始兵力
                int treatCount = (int) Math.floor(treatmentAmend.getRatio() * soldier.getMax());
                if (treatCount > maxCanTreat) {
                    // 治疗溢出了，用最大可治疗士兵修正真实治疗量
                    treatCount = maxCanTreat;
                }
                treatmentCtx.addTreatValue(soldier.getId(), treatCount);
                soldier.addCacheTreat(treatCount);

                // 扣除剩余治疗数容量， 扣除剩余治疗量
                maxRoleCanTreat -= treatCount;
            }
        }
        return treatmentCtx;
    }

    /**
     * 治疗
     */
    public static TreatmentContext stdApplyTreatment(BattleRoleSnapshot attacker,
                                                     BattleRoleSnapshot target,
                                                     ActionContext actionCtx,
                                                     EffectContext effectContext,
                                                     Amend treatmentAmend) {
        TreatmentContext treatmentCtx = new TreatmentContext(actionCtx, effectContext);
        List<SoldierSnapshot> sortedSoldierList = target.getSoldierListWithSort();
        // 算出本回合已经被治疗了多少
        int totalCacheTreat = 0;
        for (SoldierSnapshot soldier : sortedSoldierList) {
            totalCacheTreat += soldier.getCacheTreat();
        }
        // 部队最大可治疗士兵数=集结容量上限-存活-本回合已经被治疗的数量
        int maxRoleCanTreat = target.getRallyCapacity() - target.soldierAliveCount() - totalCacheTreat;
        if (maxRoleCanTreat <= 0) {
            return treatmentCtx;
        }
        // 治疗系数加成
        calcSkillTreatmentSoldierAmend(attacker, target, treatmentAmend);
        //治疗量 = 治疗修正系数 * 治疗系数 * 兵力系数
        double totalTreat = treatmentAmend.getRatio() * attacker.soldierCountRatio();
        // 按兵种优先级进行治疗
        for (SoldierSnapshot soldier : sortedSoldierList) {
            if (totalTreat <= 0 || maxRoleCanTreat <= 0) {
                break;
            }
            if (soldier.aliveCount() <= 0) {
                continue;
            }

            // 最大可治疗士兵数, min(轻伤数 - 本回合已被治疗数, 部队剩余容量)
            int maxCanTreat = Math.min(soldier.getMaxCanTreat() - soldier.getCacheTreat(), maxRoleCanTreat);
            if (maxCanTreat <= 0) {
                continue;
            }

            double hp = soldier.getBaseHp();
            if (hp <= 0) {
                LOGGER.error("BattleErrLog stdApplyTreatment failed, soldier={}, hp <= 0, attacker={}, target={}", soldier.getId(), attacker, target);
                continue;
            }

            // 真实治疗量
            double realTreatCost = totalTreat;
            // 兵力回复 = 治疗量 / 兵种生命值
            int treatCount = (int) Math.floor(totalTreat / hp);

            if (treatCount > maxCanTreat) {
                // 治疗溢出了，用最大可治疗士兵修正真实治疗量
                treatCount = maxCanTreat;
                realTreatCost = treatCount * hp;
            }
            treatmentCtx.addTreatValue(soldier.getId(), treatCount);
            soldier.addCacheTreat(treatCount);

            // 扣除剩余治疗数容量， 扣除剩余治疗量
            maxRoleCanTreat -= treatCount;
            totalTreat -= realTreatCost;
        }
        return treatmentCtx;
    }

    /**
     * 伤害
     */
    public static DamageContext stdApplyDamage(BattleRoleSnapshot attacker,
                                               BattleRoleSnapshot defender,
                                               ActionContext actionCtx,
                                               EffectContext effectContext,
                                               Amend damageAmend,
                                               double defenceTotalShield) {
        double battleScale = BattleFormula.battleScale(attacker.aliveCount(), defender.aliveCount());
        DamageContext dmgCtx = new DamageContext(actionCtx, effectContext, battleScale);
        if (defender.isInvincible()) {
            // 无敌
            return dmgCtx;
        }
        if (actionCtx.getType() == ActionType.SKILL) {
            // 技能伤害计算
            skillAtk(attacker, defender, actionCtx, dmgCtx, damageAmend, defenceTotalShield);
        } else {
            // 普攻伤害计算
            oriAtk(attacker, defender, actionCtx, dmgCtx, defenceTotalShield);
        }
        // 伤害转兵损
        flushDamage(attacker, defender, dmgCtx);
        return dmgCtx;
    }

    /**
     * 护盾值
     */
    public static double stdCalcShieldValue(BattleRoleSnapshot snapshot, float amend) {
        ConstBattleTemplate constBattleTemplate = ResHolder.getResService(ConstBattleKVResService.class).getTemplate();
        // 护盾量 = 护盾修正系数 * 英雄技能修正系数 * 护盾系数 * 兵力系数
        return constBattleTemplate.getShieldBaseRatio() * 1 * amend * snapshot.soldierCountRatio();
    }

    private static void skillAtk(BattleRoleSnapshot attacker,
                                 BattleRoleSnapshot defender,
                                 ActionContext actionCtx,
                                 DamageContext dmgCtx,
                                 Amend damageAmend,
                                 double defenceTotalShield) {
        if (attacker.soldierAliveCount() <= 0) {
            return;
        }
        // 技能系数加成
        calcSkillAttackSoldierAmend(attacker, defender, actionCtx, dmgCtx.getEffectContext(), damageAmend);
        // 基础攻击力=[攻击方]部队平均攻击力 * [攻击方]兵力系数 * 战斗规模系数 * [攻击方]野怪攻击修正系数
        double baseAttack = attacker.getSoldierAvgAtk()
                * attacker.soldierCountRatio()
                * dmgCtx.getBattleScale()
                * monsterAtkAmendFactor(attacker);
        // 技能攻击力 = 技能伤害修正系数 * 英雄修正系数（只修自己的系数） * 技能系数 * 部队平均攻击力 * 兵力系数
        //              * (1 + [攻击方]∑技能伤害buff + [攻击方]∑伤害buff)
        //              * (1 + [受击方]∑受到技能伤害buff + [受击方]∑受到伤害buff + 受到夹击伤害加成)
        double atk = baseAttack * damageAmend.getRatio();
        // 日志-打印攻击力基础
        BattleGround.getBattleLog().printfRelationSkillAtkBase(attacker, defender, baseAttack, atk, dmgCtx.getEffectContext());
        List<SoldierSnapshot> list = defender.getSoldierListWithSort();
        for (SoldierSnapshot e : list) {
            if (atk <= 0) {
                break;
            }
            if (e.aliveCount() <= 0) {
                continue;
            }
            double oneAtk = skillDamage(attacker, defender, e, atk, dmgCtx, defenceTotalShield);
            if (oneAtk > 0) {
                atk -= oneAtk;
            } else {
                break;
            }
        }
    }

    private static void oriAtk(BattleRoleSnapshot attacker,
                               BattleRoleSnapshot defender,
                               ActionContext actionCtx,
                               DamageContext dmgCtx,
                               double defenceTotalShield) {
        if (defender.aliveCount() <= 0) {
            return;
        }
        // 修正系数
        calcAttackSoldierAmend(attacker, defender, actionCtx);
        for (SoldierGroupSnapshot aGroup : attacker.getGroupSnapshotMap().values()) {
            // 士兵打士兵
            soldierAtkSoldier(attacker, defender, aGroup, actionCtx, dmgCtx, defenceTotalShield);
        }
        // 防御塔打士兵
        guardTowerAtkSoldier(attacker, defender, actionCtx, dmgCtx, defenceTotalShield);
    }

    private static void soldierAtkSoldier(BattleRoleSnapshot attacker,
                                          BattleRoleSnapshot defender,
                                          SoldierGroupSnapshot atkGroup,
                                          ActionContext actionCtx,
                                          DamageContext dmgCtx,
                                          double defenceTotalShield) {
        double aGroupAlive = atkGroup.aliveCount();
        if (aGroupAlive <= 0) {
            return;
        }

        int attackerAlive = attacker.soldierAliveCount();
        int defenderAlive = defender.soldierAliveCount();
        Amend damageAmend = actionCtx.getDamageAmend(defender.getRoleId());
        // 防御塔挡刀，保护部队比例
        double guardRation = defender.getGuardRatio();
        // 基础攻击力 = [攻击方]兵组攻击力 * [攻击方]兵力系数 * 战斗规模系数 * [攻击方]野怪攻击修正系数
        double baseAttack = atkGroup.getAvgAtk()
                * attacker.soldierCountRatio()
                * dmgCtx.getBattleScale()
                * monsterAtkAmendFactor(attacker);

        // 打士兵
        for (SoldierGroupSnapshot dGroup : defender.getGroupSnapshotMap().values()) {
            double dGroupAlive = dGroup.aliveCount();
            if (dGroupAlive > 0) {
                // 兵种克制提升率相关
                double restrainRatio = getRestrainRatio(attacker, defender, atkGroup.getType(), dGroup.getType());
                Amend restrainAmend = new Amend().multiRatio(restrainRatio);
                Amend amend = damageAmend.plus(restrainAmend);
                // 普通攻击攻击力 = 基础攻击力 * [攻击方]兵组占比 * [受击方]兵组占比
                //              * 普通攻击系数
                //              * (1 + [攻击方]∑普通攻击伤害buff + [攻击方]∑伤害buff)
                //              * (1 + [受击方]∑受到普通攻击伤害buff + [受击方]∑受到伤害buff + 受到夹击伤害加成)
                //              * (兵种克制提升率)
                //              * (1 - [受击方]防御塔部队保护比例)
                double atk = baseAttack
                        * (aGroupAlive / attackerAlive)
                        * (dGroupAlive / defenderAlive)
                        * amend.getRatio()
                        * (1 - guardRation);
                atkDamage(attacker, defender, atkGroup, dGroup, atk, actionCtx.getType(), dmgCtx, null, defenceTotalShield);
            }
        }

        // 打防御塔
        if (defender.getGuardTowerAliveCount() > 0) {
            if (defenderAlive <= 0) {
                // 空城时该比例为1
                guardRation = 1;
            }
            // 对防御塔造成的伤害
            long extraRatio = getAtkGuardTowerDamageAmend(attacker, defender);
            Amend extraAmend = new Amend().multiRatio(1 + extraRatio / MathUtils.TEN_THOUSAND);
            Amend amend = damageAmend.plus(extraAmend);
            // 对防御塔普通攻击攻击力 = [攻击方]基础攻击力
            //                      * 系数
            //                      * (1 + [攻击方]∑对防御塔造成的伤害buff)
            //                      * [攻击方]步兵组占比
            //                      * [受击方]防御塔部队保护比例
            double atk = baseAttack
                    * amend.getRatio()
                    * (aGroupAlive / attackerAlive)
                    * guardRation;
            double damage = atk / defender.getGuardTower().getDefence();
            dmgCtx.addCacheDamage(defender.getGuardTower().getId(), damage);
            // 日志-打印伤害信息
            Map<Integer, Double> damageMap = Maps.newHashMap();
            damageMap.put(defender.getGuardTower().getId(), damage);
            BattleGround.getBattleLog().printfRelationGuardTowerDamageInfo(attacker, defender, atkGroup, defender.getGuardTower(), atk, damage, 0, actionCtx.getType(), damageMap);
        }
    }

    private static void guardTowerAtkSoldier(BattleRoleSnapshot attacker,
                                             BattleRoleSnapshot defender,
                                             ActionContext actionCtx,
                                             DamageContext dmgCtx,
                                             double defenceTotalShield) {
        if (attacker.getGuardTowerAliveCount() <= 0) {
            return;
        }
        int defenderAlive = defender.soldierAliveCount();
        float guardTowerMaxHpFactor = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getGuardTowerMaxHpFactor();
        // 防御塔基础攻击力 = 防御塔兵种攻击力 * 防御塔当前状态对应攻击力比例 * 防御塔最大耐久度^(0.5) * 战斗规模系数
        double baseAttack = attacker.getGuardTower().getAtk()
                * attacker.getGuardTower().getAtkRate()
                * Math.pow(attacker.getGuardTower().getMaxHp(), guardTowerMaxHpFactor)
                * dmgCtx.getBattleScale();
        // 受到防御塔伤害相关
        long extraRatio = getRecvGuardTowerDamageAmend(attacker, defender);
        Amend extraAmend = new Amend().multiRatio(1 + extraRatio / MathUtils.TEN_THOUSAND);
        Amend amend = actionCtx.getDamageAmend(defender.getRoleId()).plus(extraAmend);
        for (SoldierGroupSnapshot dGroup : defender.getGroupSnapshotMap().values()) {
            double dGroupAlive = dGroup.aliveCount();
            if (dGroupAlive > 0) {
                // 防御塔普通攻击攻击力 = 防御塔基础攻击力
                //                   * 攻击方兵种占比（即防御塔兵种占比，为1）
                //                   * 部队A步兵兵种占比
                //                   * 系数
                //                   * (1 + [受击方]∑受到防御塔伤害buff)
                double atk = baseAttack
                        * amend.getRatio()
                        * (dGroupAlive / defenderAlive);
                atkDamage(attacker, defender, null, dGroup, atk, actionCtx.getType(), dmgCtx, attacker.getGuardTower(), defenceTotalShield);
            }
        }
    }

    private static void atkDamage(BattleRoleSnapshot attacker,
                                  BattleRoleSnapshot defender,
                                  @Nullable SoldierGroupSnapshot aGroup,
                                  SoldierGroupSnapshot dGroup,
                                  double atk,
                                  ActionType actionType,
                                  DamageContext dmgCtx,
                                  @Nullable GuardTowerSnapshot tower,
                                  double defenceTotalShield) {
        double dGroupAlive = dGroup.aliveCount();

        double defenceShield = Math.max(0, defenceTotalShield - dmgCtx.getDecreaseDamage());

        // 防守方拥有的护盾值
        Map<Integer, Double> damages = Maps.newHashMap();
        // 总伤害
        double damageSum = 0;
        for (SoldierSnapshot dSoldier : dGroup.getSoldiers()) {
            double defence = dSoldier.getDefence();
            if (defence <= 0) {
                LOGGER.error("BattleErrLog atk_damage attacker={},defender={},actionType={},soldier={},defence={} <= 0",
                        attacker, defender, actionType, dSoldier.getId(), defence);
                continue;
            }
            // 对单个兵的伤害 = 攻击力 * 防守兵种占比 / 防守方防御力
            double damage = atk * (dSoldier.aliveCount() / dGroupAlive) / defence;
            damageSum += damage;
            damages.put(dSoldier.getId(), damage);
        }

        double shieldSum = 0;
        if (damageSum > 0) {
            for (SoldierSnapshot dSoldier : dGroup.getSoldiers()) {
                double damage2Soldier = damages.getOrDefault(dSoldier.getId(), 0.0);
                // 分配护盾值 = min{ 受到伤害 / ∑部队该回合受到伤害 * 当前部队剩余护盾量 , 受到伤害 }
                double shield4Soldier = Math.min(defenceShield * (damage2Soldier / damageSum), damage2Soldier);
                // 实际受到的伤害 = 受到伤害 - 分配护盾值
                double damage = damage2Soldier - shield4Soldier;
                shieldSum += shield4Soldier;
                dmgCtx.addCacheDamage(dSoldier.getId(), damage);
                dmgCtx.addCacheDecreaseDamage(dSoldier.getId(), shield4Soldier);
                if (BattleGround.isLogEnable) {
                    int type = aGroup == null ? CommonEnum.SoldierType.ST_GuardTower_VALUE : aGroup.getType();
                    LOGGER.debug("BattleLog atk_damage attacker={},defender={},actionType={},atkSoldierType={} soldier={},damage={}, atk={}, defence={},shield={}",
                            attacker, defender, actionType, type, dSoldier.getId(), damage, atk, dSoldier.getDefence(), shield4Soldier);
                }
            }
        }
        // 日志-打印伤害信息
        BattleGround.getBattleLog().printfRelationDamageInfo(attacker, defender, aGroup, dGroup, atk, damageSum, shieldSum, actionType, damages, tower);
    }

    private static double skillDamage(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, SoldierSnapshot soldier, double atk, DamageContext dmgCtx, double defenceTotalShield) {
        // 防守方拥有的护盾值
        double totalShield = Math.max(0, defenceTotalShield - dmgCtx.getDecreaseDamage());
        double defence = soldier.getDefence();
        // 总伤害
        double ret = 0;
        if (defence <= 0) {
            return ret;
        }
        // T1步兵受到的技能伤害=技能攻击力/T1步兵防御力
        double damage = atk / defence;
        double realDamage = 0;
        if (damage >= totalShield) {
            //真实伤害
            realDamage = Math.min(damage - totalShield, soldier.aliveCount() * soldier.getHp());
            ret = realDamage * defence;
            dmgCtx.addCacheDamage(soldier.getId(), realDamage);
        } else {
            ret = 0;
        }
        double shieldDec = Math.min((long) damage, totalShield);
        totalShield -= shieldDec;
        dmgCtx.addCacheDecreaseDamage(soldier.getId(), shieldDec);
        // 日志-打印技能攻击结果
        BattleGround.getBattleLog().printfRelationSkillAtkResult(attacker, defender, soldier, atk, defence, totalShield, ret, dmgCtx.getEffectContext());
        if (BattleGround.isLogEnable) {
            LOGGER.debug("BattleLog skill_damage,defender={},soldier={},Priority={}, realDamage={}, dead={}, damage={},atk={},defence={},totalShield={}", defender, soldier.getId(), soldier.getTemplate().getBattleCalcPriority(), realDamage, soldier.calcLoss(realDamage), damage, atk, defence, totalShield);
        }
        return ret;
    }

    private static void flushDamage(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, DamageContext dmgCtx) {
        // 士兵flush伤害
        for (SoldierGroupSnapshot dGroup : defender.getGroupSnapshotMap().values()) {
            for (SoldierSnapshot dSoldier : dGroup.getSoldiers()) {
                int loss = dSoldier.calcLoss(dmgCtx.getDamageCache(dSoldier.getId()));
                if (loss > 0) {
                    dmgCtx.putLoss(dSoldier.getId(), loss);
                }
                int decreaseLoss = dSoldier.calcLoss(dmgCtx.getDecreaseDamageCache(dSoldier.getId()));
                if (decreaseLoss > 0) {
                    dmgCtx.putDecreaseLoss(decreaseLoss);
                }
            }
        }
        // 防御塔flush伤害
        if (defender.getGuardTowerAliveCount() > 0) {
            int loss = defender.getGuardTower().calcLoss(dmgCtx.getDamageCache(defender.getGuardTower().getId()));
            if (loss > 0) {
                dmgCtx.putLoss(defender.getGuardTower().getId(), loss);
            }
        }
    }

    // 战斗规模系数 = min(config3, max(1, config2 + (attack + defence) / config1))
    public static double battleScale(double attackerAlive, double defenderAlive) {
        ConstBattleTemplate template = ResHolder.getResService(ConstBattleKVResService.class).getTemplate();
        double param1 = (attackerAlive + defenderAlive) / template.getBattleScaleFactor();
        double param2 = Math.max(param1 + template.getBattleScaleFactor2(), 1);
        return Math.min(param2, template.getBattleScaleFactor3());
    }

    private static float monsterAtkAmendFactor(BattleRoleSnapshot attacker) {
        if (attacker.isMonster()) {
            return ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, (int) attacker.getRoleTypeId()).getAtkParam();
        }
        return 1;
    }

    /**
     * 兵力系数 = 部队总数 ^ 兵力修正系数
     */
    public static double soldierCountRatio(BattleRole role) {
        // 兵力修正系数：目前仅区分出野怪和非野怪
        float troopAmendFactor;
        if (role.isMonsterObj()) {
            // 野怪兵力修正系数
            MonsterTemplate monsterTemplate = ResHolder.getInstance().getValueFromMap(MonsterTemplate.class, (int) role.getAdapter().getRoleTypeId());
            troopAmendFactor = monsterTemplate.getSoldierCountParam();
        } else {
            troopAmendFactor = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getSoldierCountParam();
        }
        return Math.pow(role.aliveCount(), troopAmendFactor);
    }

    /**
     * 部队的平均战斗力
     */
    public static double calcAveragePower(BattleRoleSnapshot attackerSnapShot, BattleRoleSnapshot defenderSnapShot) {
        ConstBattleTemplate constBattleTemplate = ResHolder.getResService(ConstBattleKVResService.class).getTemplate();
        if (attackerSnapShot.aliveCount() <= 0 || defenderSnapShot.aliveCount() <= 0) {
            return 0;
        }

        long allDamageAmend = getAllDamageAmend(attackerSnapShot, defenderSnapShot, null);
        long allRecvDamageAmend = getAllRecvDamageAmend(attackerSnapShot, defenderSnapShot);
        // 攻击方平均战斗力 = Max(1, (攻击方兵力 / 受击方兵力))^重伤修正系数1 *(攻击方平均攻击力 * 攻击方平均防御力) * (1 + 伤害提升BUFF) / (1 + 受到伤害降低BUFF)
        return Math.pow(Math.max((double) attackerSnapShot.aliveCount() / defenderSnapShot.aliveCount(), 1.0), constBattleTemplate.getWoundFactor())
                * attackerSnapShot.calcAverageAttack()
                * attackerSnapShot.calcAverageDefence()
                * (1 + allDamageAmend / MathUtils.TEN_THOUSAND)
                / (1 + allRecvDamageAmend / MathUtils.TEN_THOUSAND);
    }

    public static double getServerRatio(BattleRoleSnapshot snapshot, double baseSevereRatio) {
        double slightPercent = snapshot.getFinalBuffValue(CommonEnum.BuffEffectType.ET_SLIGHT_INJURED_PERCENT);
        double server2SlightPercent = snapshot.getFinalBuffValue(CommonEnum.BuffEffectType.ET_SERVER_2_SLIGHT_INJURED_PERCENT);
        // （配置重伤比例-轻伤单位比例248）*（1-重伤转轻伤比例261）
        return (baseSevereRatio - slightPercent / Constants.CONVERT_TEN_THOUSAND_POINTS) * (1 - server2SlightPercent / Constants.CONVERT_TEN_THOUSAND_POINTS);
    }

    public static double getDeadRatio(BattleRoleSnapshot snapshot, double baseDeadRatio) {
        double server2SlightPercent = snapshot.getFinalBuffValue(CommonEnum.BuffEffectType.ET_DEAD_2_SERVER_PERCENT);
        // 死亡率 * （1 - 死亡转重伤比例）
        return baseDeadRatio * (1 - server2SlightPercent / Constants.CONVERT_TEN_THOUSAND_POINTS);
    }

    /**
     * 英雄怒气
     */
    public static double getHeroPower(BattleRoleSnapshot snapshot, int value) {
        long angerBuff = snapshot.getFinalBuffValue(CommonEnum.BuffEffectType.ET_ANGER_INC);
        return value * (1 + angerBuff / MathUtils.TEN_THOUSAND);
    }

    // region buff
    private static void calcAttackSoldierAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, ActionContext actionCtx) {
        ConstBattleTemplate constBattleTemplate = ResHolder.getResService(ConstBattleKVResService.class).getTemplate();
        Amend damageAmend = actionCtx.getDamageAmend(defender.getRoleId());
        switch (actionCtx.getType()) {
            case ATTACK_BACK: {
                // 反击修正系数
                damageAmend.multiRatio(constBattleTemplate.getAntiAtkFactor());
                break;
            }
            case ORDINARY_ATTACK: {
                // 普攻修正系数
                damageAmend.multiRatio(constBattleTemplate.getBaseAtkFactor());
                break;
            }
        }

        // 普攻伤害buff
        // 1 + [攻击方]∑普通攻击伤害buff + [攻击方]∑伤害buff
        long damageRatio = calcAtkDamageAmend(attacker, defender, actionCtx.getType());
        damageAmend.multiRatio(1 + damageRatio / MathUtils.TEN_THOUSAND);

        // 受到普攻伤害buff
        // 1 + [受击方]∑受到普通攻击伤害buff + [受击方]∑受到伤害buff  + 受到夹击伤害加成
        long recvDamageRatio = calcRecvDamageAmend(attacker, defender, actionCtx.getType());
        damageAmend.multiRatio(1 + recvDamageRatio / MathUtils.TEN_THOUSAND);
        // 日志-士兵伤害修正
        BattleGround.getBattleLog().printfRelationAtkAmend(attacker, defender, damageRatio, recvDamageRatio, damageAmend, actionCtx.getType());
    }

    private static void calcSkillAttackSoldierAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, ActionContext actionCtx, EffectContext effectContext, Amend damageAmend) {
        //技能伤害修正系数
        float skillRatio = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getSkillRatio();
        damageAmend.multiRatio(skillRatio);
        // 技能伤害buff
        //  1 + [攻击方]∑技能伤害buff + [攻击方]∑伤害buff
        long damageRatio = calcSkillAtkDamageAmend(attacker, defender, effectContext);
        damageAmend.multiRatio(1 + damageRatio / MathUtils.TEN_THOUSAND);

        // 受到技能伤害buff
        //1 + [受击方]∑受到技能伤害buff + [受击方]∑受到伤害buff + 受到夹击伤害加成
        long recvDamageRatio = calcRecvSkillDamageAmend(attacker, defender);
        damageAmend.multiRatio(1 + recvDamageRatio / MathUtils.TEN_THOUSAND);
        // 日志-士兵伤害修正
        BattleGround.getBattleLog().printfRelationAtkAmend(attacker, defender, damageRatio, recvDamageRatio, damageAmend, actionCtx.getType());
    }

    private static void calcSkillTreatmentSoldierAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, Amend treatmentAmend) {
        //技能治疗基础修正系数
        float treatmentBaseRatio = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getTreatmentBaseRatio();
        treatmentAmend.multiRatio(treatmentBaseRatio);
        // 技能治疗buff
        //  1 + [攻击方]∑技能治疗buff + [攻击方]∑治疗buff
        long treatmentBuffRatio = calcSkillTreatmentAmend(attacker, defender);
        treatmentAmend.multiRatio(1 + treatmentBuffRatio / MathUtils.TEN_THOUSAND);

        // 受到技能治疗buff
        //  1 + [受击方]∑受到技能治疗buff + [受击方]∑受到治疗buff
        long recvTreatmentBuffRatio = calcRecvSkillTreatmentAmend(attacker, defender);
        treatmentAmend.multiRatio(1 + recvTreatmentBuffRatio / MathUtils.TEN_THOUSAND);
        // 日志-士兵治疗修正
        BattleGround.getBattleLog().printfRelationTreatAmend(attacker, defender, treatmentBuffRatio, recvTreatmentBuffRatio, treatmentAmend);
    }

    private static long calcSkillAtkDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, EffectContext effectContext) {
        long ret = 0;
        ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_SKILL_DMG_INC);
        // Npc部队
        if (defender.isBelongToNpc() && !defender.isInDungeon()) {
            ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_SKILL_DMG_2_MONSTER_PERCENT);
        }
        if (effectContext.isSkill()) {
            SkillConfigTemplate skillTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillTemplate(effectContext.getId());
            if (skillTemplate.getSkillType() == CommonEnum.SkillType.ST_HERO_ACTIVE) {
                // 主动技能伤害
                ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_HERO_ACTIVE_SKILL_DMG_PERCENT);

                // 副将主动技能伤害
                if (effectContext.getRole().getDeputyHero() != null && effectContext.getHeroId() == effectContext.getRole().getDeputyHero().getId()) {
                    ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_DEPUTY_HERO_ACTIVE_SKILL_DMG_PERCENT);
                }
            }
        }
        ret += getAllDamageAmend(attacker, defender, ActionType.SKILL);
        return ret;
    }

    private static long calcAtkDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, ActionType actionType) {
        long ret = 0;
        ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_ORDINARY_ATK_DMG_INC);
        // 反击
        if (actionType == ActionType.ATTACK_BACK) {
            ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_ATK_BACK_DMG_INC);
        }
        // Npc部队
        if (defender.isBelongToNpc() && !defender.isInDungeon()) {
            ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_ATK_DMG_2_MONSTER_PERCENT);
        }
        ret += getAllDamageAmend(attacker, defender, actionType);
        return ret;
    }

    private static long calcSkillTreatmentAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        // 治疗增加
        long ret = 0;
        ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_TREATMENT_EFFECT_INC_PERCENT);
        return ret;
    }

    private static long calcRecvSkillTreatmentAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        long ret = 0;
        ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_TREATMENT_EFFECT_INC_PERCENT);
        return ret;
    }

    private static long calcRecvSkillDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        long ret = 0;
        ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_SKILL_DMG_INC);
        ret += getAllRecvDamageAmend(attacker, defender);
        // 夹击
        ret += getPincerAmend(attacker, defender);
        return ret;
    }

    private static long calcRecvDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, ActionType actionType) {
        long ret = 0;
        ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_ORDINARY_ATK_DMG_INC);
        if (actionType == ActionType.ATTACK_BACK) {
            ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_ATK_BACK_DMG_INC);
        }
        ret += getAllRecvDamageAmend(attacker, defender);
        // 夹击
        ret += getPincerAmend(attacker, defender);
        return ret;
    }

    private static long getAllDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, ActionType actionType) {
        long ret = 0;
        // 造成伤害
        ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_DMG_INC);
        if (defender.isInDungeon()) {
            // 副本
            ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_DUNGEON_DMG_PERCENT);
        } else {
            // Npc部队
            if (defender.isBelongToNpc()) {
                ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_DMG_2_MONSTER_INC);
                // 集结野怪
                if (attacker.isRally()) {
                    ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RALLY_DMG_2_MONSTER_PERCENT);
                }
            }
        }
        if (attacker.isRally()){
            // 集结伤害增加
            ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RALLY_ATTACK_DAMAGE_INC_PERCENT);
            if (actionType != null && actionType == ActionType.ATTACK_BACK){
                // 集结反击伤害增加
                ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RALLY_BACK_DAMAGE_INC_PERCENT);
            }
        }
        return ret;
    }

    private static long getAllRecvDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        long ret = 0;
        // 受到伤害
        ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_DMG_INC);
        if (attacker.isInDungeon()) {
            // 副本
            ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_DUNGEON_RECV_DMG_PERCENT);
        } else {
            // Npc部队
            if (attacker.isBelongToNpc()) {
                ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_MONSTER_DMG_PERCENT);
            }
        }
        return ret;
    }

    private static long getPincerAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        // 夹击伤害加成
        long ret = 0;
        if (defender.getPincerAttackerNum() > 1) {
            ret = (long) ResHolder.getResService(SkillDataTemplateService.class).getPincerRatio(defender.getPincerAttackerNum()) * N_100;
        }
        return ret;
    }

    /**
     * 兵种克制提升率 =(基础兵种克制 + [攻击方]额外兵种克制buff/debuf + [受击方]额外兵种克制buff/debuf)
     */
    private static double getRestrainRatio(BattleRoleSnapshot attack, BattleRoleSnapshot defence, int atkType, int defType) {
        // 基础兵种克制
        double restrainRatio = ResHolder.getResService(SoldierResService.class).getRestrainRatio(atkType, defType);

        // [攻击方]额外兵种克制buff/debuf
        CommonEnum.BuffEffectType restrainEffectType = AdditionConstants.SOLDIER_RESTRAIN_MAP.get(Pair.of(CommonEnum.SoldierType.forNumber(atkType), CommonEnum.SoldierType.forNumber(defType)));
        if (restrainEffectType != null) {
            restrainRatio += attack.getFinalBuffValue(restrainEffectType) / Constants.CONVERT_TEN_THOUSAND_POINTS;
        }

        // [受击方]额外兵种克制buff/debuf
        CommonEnum.BuffEffectType beRestrainEffectType = AdditionConstants.SOLDIER_BE_RESTRAIN_MAP.get(Pair.of(CommonEnum.SoldierType.forNumber(defType), CommonEnum.SoldierType.forNumber(atkType)));
        if (beRestrainEffectType != null) {
            restrainRatio += defence.getFinalBuffValue(beRestrainEffectType) / Constants.CONVERT_TEN_THOUSAND_POINTS;
        }
        return restrainRatio;
    }

    private static void calcSkillPercentageTreatmentSoldierAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender, Amend treatmentAmend) {
        // 受到技能治疗buff
        //  1 + [受击方]∑受到技能治疗buff + [受击方]∑受到治疗buff
        long recvTreatmentBuffRatio = calcRecvSkillTreatmentAmend(attacker, defender);
        treatmentAmend.multiRatio(1 + recvTreatmentBuffRatio / MathUtils.TEN_THOUSAND);
        // 日志-士兵治疗修正
        BattleGround.getBattleLog().printfRelationTreatAmend(attacker, defender, 0, recvTreatmentBuffRatio, treatmentAmend);
    }

    private static long getRecvGuardTowerDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        long ret = 0;
        // 受到防御塔伤害
        ret += defender.getFinalBuffValue(CommonEnum.BuffEffectType.ET_RECV_GUARD_TOWER_DMG_DEC_BUFF);
        return ret;
    }

    private static long getAtkGuardTowerDamageAmend(BattleRoleSnapshot attacker, BattleRoleSnapshot defender) {
        long ret = 0;
        //  对防御塔造成的伤害
        ret += attacker.getFinalBuffValue(CommonEnum.BuffEffectType.ET_ATK_GUARD_TOWER_DMG_INC_BUFF);
        return ret;
    }
    // endregion
}

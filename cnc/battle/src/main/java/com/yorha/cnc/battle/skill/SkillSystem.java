package com.yorha.cnc.battle.skill;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.buf.Buff;
import com.yorha.cnc.battle.common.ActionType;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.battle.skill.task.SkillDelayTask;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.vector.Vector2f;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SkillConfigTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class SkillSystem {
    private static final Logger LOGGER = LogManager.getLogger(SkillSystem.class);

    private static final List<SkillEffectGetter> SKILL_EFFECT_GETTER = Lists.newArrayList();
    /**
     * 注：buff比较特殊，需要传入buff的施加者为技能效果的施法者，所以单独搞了一个这个List。用法见trigger()
     */
    private static final List<SkillEffectGetter> SKILL_EFFECT_GETTER_WITHOUT_BUFF = Lists.newArrayList();

    static {
        registerFunc();
        registerAllFunc();
    }

    private static void registerFunc() {
        SKILL_EFFECT_GETTER_WITHOUT_BUFF.add(SkillSystem::getHeroEffects);
        // 在这里注册其他模块会提供的技能效果。需要自行实现SkillEffectGetter::get


    }

    private static void registerAllFunc() {
        SKILL_EFFECT_GETTER.addAll(SKILL_EFFECT_GETTER_WITHOUT_BUFF);
        SKILL_EFFECT_GETTER.add(SkillSystem::getBuffEffects);
    }

    /**
     * 释放技能
     *
     * @param attacker 释放者
     * @param event    事件
     * @return 技能释放结果
     */
    public static SkillResult fireByEvent(BattleRole attacker, FireSkillEvent event) {
        return fireBySkillId(attacker, event, null);
    }

    /**
     * 释放技能
     *
     * @param attacker      释放者
     * @param effectContext 用于战斗日志的技能释放信息，默认为null
     * @return 技能释放结果
     */
    public static SkillResult fireBySkillId(BattleRole attacker,
                                            FireSkillEvent event,
                                            EffectContext effectContext) {
        long targetId = event.getTargetId();
        int heroId = event.getHeroId();
        int skillId = event.getSkillId();
        LOGGER.debug("BattleLog {} fireBySkillId, heroId={} skillId={}, effectContext={}, targetId={}", attacker, heroId, skillId, effectContext, targetId);

        SkillConfigTemplate skillTemplate = ResHolder.getInstance().findValueFromMap(SkillConfigTemplate.class, skillId);
        if (skillTemplate == null) {
            LOGGER.error("BattleErrLog {} try to fire illegal skill={}", attacker, skillId);
            return null;
        }

        BattleRole target = attacker.getTargetRole();
        if (targetId != 0) {
            BattleRole role = attacker.getGround().getAdapter().getBattleRoleById(targetId);
            if (role == null) {
                LOGGER.error("BattleErrLog {} try to fire to illegal target={}", attacker, targetId);
                return null;
            }
            target = role;
        }

        // 先预警一下
        SkillResult skillResult = new SkillResult()
                .setFireType(CommonEnum.SkillFireType.SFT_PREPARE)
                .setAttackerId(attacker.getRoleId())
                .setSkillId(skillId)
                .setHeroId(heroId);
        attacker.getAdapter().broadcastSkillResult(skillResult, BattleConstants.BattleBroadCastNtfReason.PREPARE);
        // 放技能
        Skill skill = SkillFacade.init(skillTemplate, heroId, attacker);
        return fire(attacker, target, heroId, skill, false, effectContext, event);
    }

    /**
     * 释放技能
     *
     * @param attacker      释放者
     * @param prepareTarget 技能预警时的普攻目标
     * @param heroId        英雄id
     * @param skill         技能实例
     * @param isDelayFire   是否是延时技能，默认为false
     * @param effectContext 用于战斗日志的技能释放信息，默认为null
     * @param event         标识由事件触发，可能Null
     * @return 技能释放结果
     */
    public static SkillResult fire(BattleRole attacker, BattleRole prepareTarget, int heroId, Skill skill, boolean isDelayFire,
                                   EffectContext effectContext, FireSkillEvent event) {
        // 标志由指定目标群释放的技能
        Set<Long> canNullSkillMarkTarget = event == null ? null : event.getCanNullTargetList();
        // 由触发器事件触发的需要屏蔽通知，触发器负责通知
        boolean needNtf = event == null || !event.isShieldNtf();
        boolean isStartSing = !isDelayFire && skill.getSingTime() > 0;
        SkillResult ret = new SkillResult()
                .setFireType(isStartSing ? CommonEnum.SkillFireType.SFT_START_SING : CommonEnum.SkillFireType.SFT_FIRE)
                .setAttackerId(attacker.getRoleId())
                .setSkillId(skill.getId())
                .setHeroId(heroId)
                .setTargetId(attacker.getSkillHandler().getSkillTargetId());
        if (attacker.getSkillHandler().getYaw() != null) {
            Vector2f yaw = attacker.getSkillHandler().getYaw();
            ret.setYaw(StructPB.PointPB.newBuilder().setX((int) yaw.getX()).setY((int) yaw.getY()).build());
        }

        // 日志-技能释放
        BattleGround.getBattleLog().printfRelationSkillStart(attacker, prepareTarget, skill.getEffectIdList(), ret);

        BattleTickContext tickCtx = attacker.getGround().getTickCtx();
        // 吟唱技能在第一次fire中不做实际效果
        if (isStartSing) {
            attacker.getContext().addTask(new SkillDelayTask(tickCtx, skill.getSingTime(), skill, heroId, attacker, prepareTarget, effectContext));
        } else {
            ActionContext actionCtx = new ActionContext(attacker, ActionType.SKILL);
            for (SkillEffect effect : skill.getEffects(CommonEnum.TriggerType.TT_NONE)) {
                EffectResult castRes = effect.cast(tickCtx, actionCtx, attacker, prepareTarget, effectContext, canNullSkillMarkTarget);
                ret.addEffectRes(castRes);
            }
            afterFireSkill(attacker, ret, skill);
        }
        // 不需要ntf的直接返回
        if (isStartSing || needNtf) {
            // 通知客户端表现
            attacker.getAdapter().broadcastSkillResult(ret, BattleConstants.BattleBroadCastNtfReason.FIRE);
        }
        return ret;
    }

    private static void afterFireSkill(BattleRole attacker, SkillResult ret, Skill skill) {
        if (skill.getSkillTemplate().getSkillType() == CommonEnum.SkillType.ST_HERO_ACTIVE) {
            HashSet<Long> targetRoleIds = Sets.newHashSet();
            for (EffectResult effectResult : ret.getEffectResults()) {
                boolean isDamageEffect = effectResult.getDto().stream().anyMatch(it -> it.getType() == CommonEnum.SkillEffectType.SET_DAMAGE);
                for (BattleRole target : effectResult.getTargetRoleList()) {
                    // 记录技能命中数
                    targetRoleIds.add(target.getRoleId());
                    // 记录被主动技能伤害打中
                    if (isDamageEffect) {
                        target.getSkillBlackboard().setDamagedByActiveSkill(true);
                    }
                }
            }
            // 记录技能命中数
            attacker.getSkillBlackboard().getSkillHitCountMap().put(skill.id, targetRoleIds.size());
        }
    }

    private static boolean isNoRallyTrigger(CommonEnum.TriggerType triggerType) {
        return triggerType == CommonEnum.TriggerType.TT_END_BATTLE_NOT_DEFEAT ||
                triggerType == CommonEnum.TriggerType.TT_BEAT_PLAYER ||
                triggerType == CommonEnum.TriggerType.TT_BEAT_MONSTER;
    }

    /**
     * 触发效果
     */
    public static void trigger(BattleRole role, CommonEnum.TriggerType triggerType) {
        if (role.getGround().getTickCtx() == null) {
            LOGGER.info("BattleLog SkillSystem trigger, BattleGround not init");
            return;
        }
        if (role.isDead()) {
            // role死了不触发
            return;
        }
        if (role.getAdapter().isDestroy()) {
            // role被销毁了不触发
            return;
        }
        // 如果是集结，且是脱战相关的技能则不触发，因为集结解散了，RallyEntity会找不到
        if (role.getAdapter().isRally() && isNoRallyTrigger(triggerType)) {
            return;
        }
        for (SkillEffectGetter func : SKILL_EFFECT_GETTER_WITHOUT_BUFF) {
            for (SkillEffect effect : func.get(role, triggerType)) {
                castEffect(effect, role, role.getTargetRole(), BattleConstants.BattleBroadCastNtfReason.TRIGGER);
            }
        }
        // buff的效果的施法者和受击者有点特殊，施法者=buff的施加者，受击方=buff的持有者
        for (Buff buff : role.getBuffHandler().getAllBuff()) {
            for (SkillEffect effect : buff.getEffects(triggerType)) {
                castEffect(effect, buff.getGiver(), role, BattleConstants.BattleBroadCastNtfReason.BUFF);
            }
        }
    }

    private static void castEffect(SkillEffect effect, BattleRole attacker, BattleRole originTarget, BattleConstants.BattleBroadCastNtfReason reason) {
        try {
            BattleTickContext tickCtx = attacker.getGround().getTickCtx();
            ActionContext actionCtx = new ActionContext(attacker, ActionType.SKILL);
            SkillResult ret = new SkillResult()
                    .setFireType(CommonEnum.SkillFireType.SFT_FIRE)
                    .setAttackerId(attacker.getRoleId());
            if (effect.getExecutorInfo().isSkill()) {
                // 技能效果才传下面两个参数，天赋效果不需要
                ret.setSkillId(effect.getExecutorInfo().getId()).setHeroId(effect.getExecutorInfo().getHeroId());
            }
            EffectResult castRes = effect.cast(tickCtx, actionCtx, attacker, originTarget, null, null);
            ret.addEffectRes(castRes);
            // 通知客户端表现
            castRes.getBroadcastRole().getAdapter().broadcastSkillResult(ret, reason);
        } catch (Exception e) {
            LOGGER.error("BattleErrLog castEffect effectId={} attacker={}, target={}", effect.getTemplate().getId(), attacker, originTarget, e);
        }
    }

    public static List<SkillEffect> getSkillEffects(BattleRole role, CommonEnum.TriggerType type) {
        List<SkillEffect> ret = Lists.newArrayList();
        for (SkillEffectGetter func : SKILL_EFFECT_GETTER) {
            ret.addAll(func.get(role, type));
        }
        return ret;
    }

    private static List<SkillEffect> getHeroEffects(BattleRole role, CommonEnum.TriggerType triggerType) {
        List<SkillEffect> ret = Lists.newArrayList();
        if (role.getMainHero() != null) {
            if (role.getMainHero().getActiveSkill() != null) {
                ret.addAll(role.getMainHero().getActiveSkill().getEffects(triggerType));
            }
            for (SkillEffectContainer skill : role.getMainHero().getPassiveSkillList()) {
                ret.addAll(skill.getEffects(triggerType));
            }
            for (SkillEffectContainer talent : role.getMainHero().getTalentList()) {
                ret.addAll(talent.getEffects(triggerType));
            }
        }
        if (role.getDeputyHero() != null) {
            if (role.getDeputyHero().getActiveSkill() != null) {
                ret.addAll(role.getDeputyHero().getActiveSkill().getEffects(triggerType));
            }
            for (SkillEffectContainer skill : role.getDeputyHero().getPassiveSkillList()) {
                ret.addAll(skill.getEffects(triggerType));
            }
        }
        return ret;
    }

    private static List<SkillEffect> getBuffEffects(BattleRole role, CommonEnum.TriggerType triggerType) {
        List<SkillEffect> ret = Lists.newArrayList();
        for (SkillEffectContainer buff : role.getBuffHandler().getAllBuff()) {
            ret.addAll(buff.getEffects(triggerType));
        }
        return ret;
    }
}

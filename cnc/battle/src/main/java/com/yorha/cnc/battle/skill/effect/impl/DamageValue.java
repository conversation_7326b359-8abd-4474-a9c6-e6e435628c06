package com.yorha.cnc.battle.skill.effect.impl;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.common.Amend;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.DamageContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleFormula;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.cnc.battle.snapshot.BattleRoleSnapshot;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SkillEffectTemplate;

import java.util.List;

/**
 * 造成伤害
 *
 * <AUTHOR> Jiang
 */
public class DamageValue extends AbstractSkillEffectValue {
    private static final Logger LOGGER = LogManager.getLogger(DamageValue.class);

    public DamageValue() {
        super(SkillEffectType.SET_DAMAGE);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext,
                                              ActionContext actionCtx,
                                              SkillEffectTemplate template,
                                              BattleRole attacker,
                                              BattleRole target,
                                              EffectContext effectContext) {
        PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder()
                .setTargetId(target.getRoleId())
                .setType(this.getType())
                .setEffectId(template.getId());

        BattleRoleSnapshot attackerSnapshot;
        if (effectContext.isDot()) {
            attackerSnapshot = effectContext.getDotSnapshot();
        } else {
            attackerSnapshot = attacker.getSnapShot("AtkDamageValue");
        }

        boolean isOk = checkAndTryBuildRelation(target, actionCtx, effectContext);
        if (!isOk) {
            return Lists.newArrayList(builder.build());
        }

        Amend baseAmend = getBaseAmend(actionCtx.getDamageAmend(target.getRoleId()), template.getValue1());

        // 防守方总护盾值
        long oldDefenceShield = target.getBuffHandler().getBuffValue(CommonEnum.BuffEffectType.ET_SHIELD_BUF);

        DamageContext damageCtx = BattleFormula.stdApplyDamage(attackerSnapshot, target.getSnapShot("DefDamageValue"), actionCtx, effectContext, baseAmend, oldDefenceShield);

        // 结算护盾
        target.decShieldValue(damageCtx.getDecreaseDamage(), oldDefenceShield);

        target.getContext().addDamageCtx(damageCtx);
        builder.setValue(damageCtx.getTotalLoss());
        builder.setSpecValue(damageCtx.getDecreaseLoss());
        BattleGround.getBattleLog().printfRelationDamageContext(attacker, target, damageCtx);

        return Lists.newArrayList(builder.build());
    }

    /**
     * 检查并创建战斗关系
     */
    private boolean checkAndTryBuildRelation(BattleRole target, ActionContext actionCtx, EffectContext effectContext) {
        BattleRole attacker = effectContext.getRole();
        if (effectContext.isDot()) {
            // attacker.isDead() 为了判断主堡已经脱战了，目标还在吃主堡产生的DOT
            // DOT的施法者已经不在了，就不用建立战斗关系了
            if (attacker.getAdapter().isDestroy() || attacker.isDead()) {
                return true;
            }
        }

        Pair<BattleRelation, ErrorCode> ret = attacker.getGround().tryStartBattleByDamage(attacker, target, effectContext.isDot());
        BattleRelation relation = ret.getFirst();
        ErrorCode code = ret.getSecond();
        if (relation == null) {
            LOGGER.error("BattleErrLog DamageValue handle failed, attacker:{}, defender:{}, actionType:{} EffectContext:{}, relation not found. code={}", attacker, target, actionCtx.getType(), effectContext, code);
            return false;
        }
        relation.setHasDamage(true);
        SkillEffectTemplate template = ResHolder.findTemplate(SkillEffectTemplate.class, effectContext.getEffectId());
        // 重置脱战回合数。为了保证区域技能在产生伤害过程中不脱战
        if (template != null && template.getKeepBattle() == 1) {
            relation.resetHangupRound();
        }
        return true;
    }
}

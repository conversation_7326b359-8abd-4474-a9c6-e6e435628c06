package com.yorha.cnc.battle.adapter;

import com.yorha.cnc.battle.adapter.interfaces.IBattleGroundAdapter;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.common.constant.BattleConstants;
import com.yorha.proto.CommonEnum;

public abstract class AbstractBattleGroundAdapter implements IBattleGroundAdapter {

    private final BattleGround battleGround;

    public AbstractBattleGroundAdapter(BattleConstants.BattleGroundType simulate) {
        battleGround = new BattleGround(this, simulate, null, 0, () -> CommonEnum.MapType.MAT_NONE);
    }

    public BattleGround getBattleGround() {
        return battleGround;
    }

}

package com.yorha.cnc.battle.skill.effect.impl;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.FireSkillEvent;
import com.yorha.cnc.battle.skill.SkillResult;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.List;

/**
 * 释放技能
 *
 * <AUTHOR>
 */
public class FireSkillValue extends AbstractSkillEffectValue {

    public FireSkillValue() {
        super(CommonEnum.SkillEffectType.SET_FIRE_SKILL);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext,
                                              ActionContext actionCtx,
                                              SkillEffectTemplate template,
                                              BattleRole attacker,
                                              BattleRole target,
                                              EffectContext effectContext) {
        FireSkillEvent event = FireSkillEvent.newBuilder()
                .setSkillId(template.getValue1())
                .setHeroId(effectContext.getHeroId())
                .setTargetId(0)
                .setShieldNtf(true)  // 由效果触发的技能释放需要屏蔽通知，交由上层来通知
                .build();
        SkillResult skillResult = SkillSystem.fireBySkillId(attacker, event, effectContext);
        if (skillResult != null) {
            return skillResult.getEffectList();
        }
        return Lists.newArrayList();
    }
}

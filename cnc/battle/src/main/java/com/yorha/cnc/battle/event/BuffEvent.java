package com.yorha.cnc.battle.event;

import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class BuffEvent extends BattleRoundEvent {
    public enum OperationType {
        ADD, // 添加
        REMOVE, // 移除
        TAKE_EFFECT, // 生效
    }

    private final int buffId;
    private final OperationType opType;
    private final CommonEnum.BattleLogBuffType type;

    public BuffEvent(CommonEnum.BattleLogBuffType type, int buffId, OperationType opType) {
        this.buffId = buffId;
        this.opType = opType;
        this.type = type;
    }

    public int getBuffId() {
        return buffId;
    }

    public OperationType getOpType() {
        return opType;
    }

    public CommonEnum.BattleLogBuffType getType() {
        return type;
    }
}

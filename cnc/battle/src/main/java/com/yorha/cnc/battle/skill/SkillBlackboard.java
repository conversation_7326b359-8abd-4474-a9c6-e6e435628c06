package com.yorha.cnc.battle.skill;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class SkillBlackboard {
    /**
     * 本回合生效的buff
     */
    private final List<Integer> curRoundValidBuffs;
    /**
     * 本回合是否收到主动技能伤害
     */
    private boolean isDamagedByActiveSkill;
    /**
     * 技能命中数记录
     */
    private final Map<Integer, Integer> skillHitCountMap;
    /**
     * 主动技能记录
     */
    private Skill activeSkill;

    public SkillBlackboard() {
        this.curRoundValidBuffs = Lists.newArrayList();
        this.skillHitCountMap = Maps.newHashMap();
    }

    public List<Integer> getCurRoundValidBuffs() {
        return curRoundValidBuffs;
    }

    public boolean isDamagedByActiveSkill() {
        return isDamagedByActiveSkill;
    }

    public void setDamagedByActiveSkill(boolean damagedByActiveSkill) {
        isDamagedByActiveSkill = damagedByActiveSkill;
    }

    public Map<Integer, Integer> getSkillHitCountMap() {
        return skillHitCountMap;
    }

    public void setActiveSkill(Skill activeSkill) {
        this.activeSkill = activeSkill;
    }

    public Skill getActiveSkill() {
        return activeSkill;
    }
}

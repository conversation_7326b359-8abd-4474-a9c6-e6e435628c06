package com.yorha.cnc.battle.skill;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.TalentTemplate;

import java.util.List;

/**
 * <AUTHOR>
 */
public class Talent extends SkillFacade {
    public Talent(int talentId, int heroId, BattleRole role) {
        super(talentId, heroId, role);
    }

    public TalentTemplate getTalentTemplate() {
        return ResHolder.getInstance().findValueFromMap(TalentTemplate.class, id);
    }

    @Override
    public List<Integer> getEffectIdList() {
        return getTalentTemplate().getEffectList();
    }

    @Override
    protected CommonEnum.BattleLogSkillType getBattleLogSkillType() {
        return CommonEnum.BattleLogSkillType.BLST_TALENT;
    }
}

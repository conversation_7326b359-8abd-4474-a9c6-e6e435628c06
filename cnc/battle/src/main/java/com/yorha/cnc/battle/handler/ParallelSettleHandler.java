package com.yorha.cnc.battle.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.common.DamageResult;
import com.yorha.cnc.battle.common.SevereDeadRatio;
import com.yorha.cnc.battle.context.BattleRelationContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.DamageContext;
import com.yorha.cnc.battle.context.TreatmentContext;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRoleSettleOnly;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.cnc.battle.soldier.*;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.soldier.SoldierResService;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import org.apache.commons.collections4.IterableUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SoldierTypeTemplate;

import java.util.*;

/**
 * 结算并行处理
 * <p>
 * TODO awei: 数据与BattleRole解耦
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public class ParallelSettleHandler extends BattleHandlerAbs<BattleRoleSettleOnly> {
    private static final Logger LOGGER = LogManager.getLogger(ParallelSettleHandler.class);
    /**
     * 本回合击杀数据，用于分配击杀
     */
    public final Map<Long, Map<Integer, Integer>> killResultList = Maps.newHashMap();

    public ParallelSettleHandler(BattleRoleSettleOnly role) {
        super(role);
    }

    public static class SettleDTO {
        private final Map<Integer, SoldierLossDTO> soldierLossMap;
        private final int damageValue;
        private final int shieldValue;

        public static SettleDTO of(Map<Integer, SoldierLossDTO> soldierLossMap, int damageValue, int shieldValue) {
            return new SettleDTO(soldierLossMap, damageValue, shieldValue);
        }

        private SettleDTO(Map<Integer, SoldierLossDTO> soldierLossMap, int damageValue, int shieldValue) {
            this.soldierLossMap = soldierLossMap;
            this.damageValue = damageValue;
            this.shieldValue = shieldValue;
        }

        public void mergeTreatmentList(List<SoldierLossDTO> treatmentList) {
            for (SoldierLossDTO dto : treatmentList) {
                SoldierLossDTO soldierLossDTO = soldierLossMap.computeIfAbsent(dto.getSoldierId(), v -> new SoldierLossDTO(dto.getSoldierId()));
                soldierLossDTO.plusLoss(dto.getLossData());
                for (Map.Entry<Long, SoldierLossData> childLoss : dto.getChildLossMap().entrySet()) {
                    soldierLossDTO.plusChildLoss(childLoss.getKey(), childLoss.getValue());
                }
            }
        }

        public Map<Integer, SoldierLossDTO> getSoldierLossMap() {
            return soldierLossMap;
        }

        public int getDamageValue() {
            return damageValue;
        }

        public int getShieldValue() {
            return shieldValue;
        }
    }

    /**
     * 处理每回合结束
     */
    public void settleRound(BattleTickContext tickContext) {
        // 减员结算
        SettleDTO settleDTO = settleDamage(tickContext);

        // 治疗结算
        if (role.hasTreatmentRole(role.getRoleId())) {
            settleDTO.mergeTreatmentList(settleTreatment(tickContext));
            role.removeTreatmentRole(role.getRoleId());
        }

        DamageResult damageResult = new DamageResult(settleDTO);
        role.getSettleHandler().setDamageResult(damageResult);
        // 把兵损存起来，用来下一个阶段分发给敌方结算击杀
        setLossResult(damageResult.getSoldierLossList());
        if (BattleGround.isLogEnable) {
            LOGGER.debug("BattleLog settle process round={} role:{}, damageResult:{}, current round final soldier data:{}", tickContext.getGroundRound(), role, damageResult.toLogString(), role.getGroupMap());
        }
    }

    private void setLossResult(Collection<SoldierLossDTO> lossCollection) {
        boolean monsterObj = role.isMonsterObj();

        for (SoldierLossDTO lossDTO : lossCollection) {
            // 防御塔不算击杀
            SoldierTypeTemplate soldierTemplate = ResHolder.getResService(SoldierResService.class).findSoldierTemplate(lossDTO.getSoldierId());
            if (soldierTemplate.getSoldierType() == CommonEnum.SoldierType.ST_GuardTower.getNumber()) {
                continue;
            }

            for (Map.Entry<Long, SoldierLossData> entry : lossDTO.getLossSourceMap().entrySet()) {
                Map<Integer, Integer> lossMap = role.getSettleHandler().lossResultMap.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap());
                // 打的是野怪，日落峡谷用totalLoss，打别的用totalKill
                int totalKill = monsterObj || role.isInSimulator() ? entry.getValue().totalLoss() : entry.getValue().totalKill();
                lossMap.put(lossDTO.getSoldierId(), lossMap.getOrDefault(lossDTO.getSoldierId(), 0) + totalKill);
            }
        }
    }

    // region damage

    /**
     * 结算每个enemy对我产生的伤害
     */
    private SettleDTO settleDamage(BattleTickContext tickContext) {
        Map<Integer, SoldierLossDTO> lossMap = Maps.newHashMap();
        int ordinaryAttackNum = 0;
        int shieldNum = 0;
        for (Map.Entry<Long, List<DamageContext>> entry : role.getContext().getDamageCtxMap().entrySet()) {
            try {
                // 结算所有Damage，获得兵损详情 <roleId, <soldierId, SoldierLossData>>
                Map<Long, Map<Integer, SoldierLossData>> lossDetailForEach = settleAllDamage(tickContext, entry.getKey(), entry.getValue());
                // 护盾抵扣值
                for (DamageContext damageContext : entry.getValue()) {
                    if (damageContext.isOrdinaryAttackOrBack()) {
                        ordinaryAttackNum += damageContext.getTotalLoss();
                        if (damageContext.getDecreaseLoss() > 0) {
                            shieldNum += damageContext.getDecreaseLoss();
                        }
                    }
                }
                if (lossDetailForEach.isEmpty()) {
                    continue;
                }

                // 兵损进医院, 获得最终的兵损详情。会修改SoldierLossDTO中的数据, 医院会把部分重伤变成死亡
                //TODO awei: 拿出来放在串行函数中处理，因为里面修改了属性系统
                role.getAdapter().handleSoldierLoss(lossDetailForEach);
                // 扣兵
                //TODO awei: 拿出来放在串行函数中处理，因为里面修改了属性系统
                applyLoss(entry.getKey(), lossDetailForEach, lossMap);
            } catch (Exception e) {
                LOGGER.error("BattleErrLog role:{} settleDamage failed, enemyRoleId:{}", role, entry.getKey(), e);
            }
        }

        role.getContext().getDamageCtxMap().clear();
        return SettleDTO.of(lossMap, ordinaryAttackNum, shieldNum);
    }

    private void applyLoss(long enemyId, Map<Long, Map<Integer, SoldierLossData>> lossDetail, Map<Integer, SoldierLossDTO> lossMap) {
        for (SoldierGroup dGroup : role.getGroupMap().values()) {
            for (Soldier soldier : dGroup.getSoldiers()) {
                SoldierLossDTO lossDTO = lossMap.computeIfAbsent(soldier.getId(), v -> new SoldierLossDTO(soldier.getId()));
                SoldierLossData fatherLossData = new SoldierLossData();

                for (SoldierUnit unit : soldier.getSoldierUnitList()) {
                    Map<Integer, SoldierLossData> roleLossData = lossDetail.get(unit.getAdapterId());
                    if (roleLossData != null) {
                        SoldierLossData soldierLossData = roleLossData.get(soldier.getId());
                        if (soldierLossData != null) {
                            unit.applyLoss(soldierLossData);

                            fatherLossData.merge(soldierLossData);
                            // 填充成员的lossData
                            lossDTO.plusChildLoss(unit.getAdapterId(), soldierLossData);
                            lossDTO.plusSourceCauseChildLoss(enemyId, unit.getAdapterId(), soldierLossData);
                        }
                    }
                }

                // 填充全体的lossData
                lossDTO.plusLoss(fatherLossData);
                lossDTO.plusSourceCauseLoss(enemyId, fatherLossData);
            }
        }
        GuardTower guardTower = role.getGuardTower();
        if (guardTower != null) {
            Map<Integer, SoldierLossData> roleLossData = lossDetail.get(role.getRoleId());
            if (roleLossData != null) {
                SoldierLossData guardTowerLossData = roleLossData.get(guardTower.getId());
                if (guardTowerLossData != null) {
                    guardTower.getTowerUnit().applyLoss(guardTowerLossData);

                    // 填充成员的lossData
                    SoldierLossDTO lossDTO = lossMap.computeIfAbsent(guardTower.getId(), v -> new SoldierLossDTO(guardTower.getId()));
                    lossDTO.plusLoss(guardTowerLossData);
                    lossDTO.plusSourceCauseLoss(enemyId, guardTowerLossData);
                }
            }
        }
    }

    private Map<Long, Map<Integer, SoldierLossData>> settleAllDamage(BattleTickContext tickContext,
                                                                     long enemyRoleId,
                                                                     List<DamageContext> dmgList) {
        Map<Long, Map<Integer, SoldierLossData>> lossDetailForEach = Maps.newHashMap();
        // 一次结算中不同进攻方和自己的重伤比Map(cache)
        Map<CommonEnum.SceneObjType, SevereDeadRatio> severDeadRatioMap = null;
        for (DamageContext dmgCtx : dmgList) {
            try {
                // 获取重伤率, DOT的用快照的，普通伤害用relation的
                Map<CommonEnum.SceneObjType, SevereDeadRatio> ratioMap;
                if (dmgCtx.isDOT()) {
                    ratioMap = dmgCtx.getEffectContext().getDotDeadRatio();
                    if (ratioMap.isEmpty()) {
                        // dot的重伤比快照是空的。看看buildDeadRatioMap这里是不是算错了！
                        // 但是别慌，下面的会有空的保底逻辑
                        LOGGER.error("BattleErrLog settleAllDamage one:{} other={} deadRatio size 0. effectId={}", dmgCtx.getEffectContext().getRole(), role.getRoleId(), dmgCtx.getEffectContext().getEffectId());
                    }
                } else {
                    ratioMap = severDeadRatioMap;
                    if (ratioMap == null) {
                        ratioMap = role.getSettleHandler().buildDeadRatioMap(enemyRoleId);
                        severDeadRatioMap = ratioMap;
                    }
                }
                Map<Long, Map<Integer, SoldierLossData>> lossMap = settleSingleDamage(tickContext, role, !role.getSnapShot("SettleAllDamage").isRally(), dmgCtx.getLossMap(), ratioMap, dmgCtx.isDOT());
                // 合并多个伤害产生的兵损
                mergeLossDetailForEach(lossMap, lossDetailForEach);
                // 记录战斗日志
                logDamage(role, dmgCtx, lossMap);
            } catch (Exception e) {
                LOGGER.error("BattleErrLog role:{} settleSingleDamage failed attacker:{}", role, dmgCtx.getAttackerId(), e);
            }
        }
        return lossDetailForEach;
    }

    /**
     * 结算单个伤害的兵损
     */
    private static Map<Long, Map<Integer, SoldierLossData>> settleSingleDamage(BattleTickContext tickContext,
                                                                               BattleRoleSettleOnly role,
                                                                               boolean isSingleTeam,
                                                                               Map<Integer, Integer> totalLossMap,
                                                                               Map<CommonEnum.SceneObjType, SevereDeadRatio> ratioMap,
                                                                               boolean isDotDamage) {
        Map<Long, Map<Integer, Integer>> lossForEach = Maps.newHashMap();
        Map<Long, Map<Integer, SoldierLossData>> lossDetailForEach = Maps.newHashMap();
        if (!totalLossMap.isEmpty()) {
            // 1. 每个成员的兵损
            lossForEach = calcLossForEach(tickContext, role, totalLossMap);
            // 2. 每个成员的重伤，死亡，轻伤 <roleId, <soldierId, SoldierLossData>>
            lossDetailForEach = calcLossDetailForEach(tickContext, role, totalLossMap, lossForEach, ratioMap, isSingleTeam, isDotDamage);
        }
        if (BattleGround.isLogEnable) {
            LOGGER.debug("BattleLog settle process round:{} role:{}, lossForEach:{}, lossDetailForEach:{}", tickContext.getGroundRound(), role, lossForEach, lossDetailForEach);
        }
        return lossDetailForEach;
    }

    /**
     * 根据总兵损计算出每个成员兵损
     */
    private static Map<Long, Map<Integer, Integer>> calcLossForEach(BattleTickContext tickContext, BattleRoleSettleOnly role, Map<Integer, Integer> totalLossMap) {
        Map<Long, Map<Integer, Integer>> lossForEach = Maps.newHashMap();

        // 计算士兵兵损
        for (SoldierGroup dGroup : role.getGroupMap().values()) {
            for (Soldier soldier : dGroup.getSoldiers()) {
                int totalAlive = soldier.tempAliveCount();
                if (totalAlive <= 0) {
                    continue;
                }
                Map<Long, Integer> soldierLossMap = calcSoldierLossForEach(tickContext, role, totalLossMap, soldier);
                for (Map.Entry<Long, Integer> entry : soldierLossMap.entrySet()) {
                    lossForEach.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap()).put(soldier.getId(), entry.getValue());
                }
            }
        }

        // 计算防御塔兵损
        if (role.getGuardTower() != null) {
            int towerId = role.getGuardTower().getId();
            if (totalLossMap.containsKey(towerId)) {
                lossForEach.computeIfAbsent(role.getRoleId(), v -> Maps.newHashMap()).put(towerId, totalLossMap.getOrDefault(towerId, 0));
            }
        }
        return lossForEach;
    }

    /**
     * 根据每个成员的兵损计算出每个成员的轻，重，死数量
     */
    private static Map<Long, Map<Integer, SoldierLossData>> calcLossDetailForEach(BattleTickContext tickContext,
                                                                                  BattleRoleSettleOnly role,
                                                                                  Map<Integer, Integer> totalLossMap,
                                                                                  Map<Long, Map<Integer, Integer>> lossForEach,
                                                                                  Map<CommonEnum.SceneObjType, SevereDeadRatio> ratioMap,
                                                                                  boolean isSingleTeam,
                                                                                  boolean isDotDamage) {
        Map<Long, Map<Integer, SoldierLossData>> lossDetailForEach = Maps.newHashMap();
        if (lossForEach.isEmpty()) {
            return lossDetailForEach;
        }

        // 计算预重伤数---只针对单人战斗单位
        Map<Integer, Integer> severeMap = null;
        // 如果队伍里只有一个人，算法是不一样的哦！！
        if (isSingleTeam) {
            // 计算每个soldier的预重伤数，需要对取整多出来的部分，做一个随机再分配
            severeMap = calcSevereMap(tickContext, role, totalLossMap, ratioMap);
            if (BattleGround.isLogEnable) {
                LOGGER.debug("BattleLog settle process round:{} role:{}, severeMap:{}, lossMap:{}", tickContext.getGroundRound(), role, severeMap, totalLossMap);
            }
        }

        // 计算士兵兵损详情
        for (SoldierGroup dGroup : role.getGroupMap().values()) {
            for (Soldier soldier : dGroup.getSoldiers()) {
                for (SoldierUnit unit : soldier.getSoldierUnitList()) {
                    int loss = lossForEach.getOrDefault(unit.getAdapterId(), Maps.newHashMap()).getOrDefault(soldier.getId(), 0);
                    loss = Math.min(loss, unit.getData().tempAliveCount());
                    if (loss <= 0) {
                        continue;
                    }

                    SevereDeadRatio ratio = ratioMap.get(unit.getObjType4DamageCalc());
                    if (ratio == null) {
                        ratio = SevereDeadRatio.empty();
                        // 如果是dot伤害，可以接受用默认重伤比
                        // 因为快照机制有漏洞：计算快照时BattleRole内没有增援部队，dot生效期间增援部队进来了，导致找不到增援部队的死伤比
                        if (!isDotDamage) {
                            LOGGER.error("BattleErrLog role:{} get severe wound ratio failed.ratioMap:{}, objType:{}", role, ratioMap, unit.getObjType4DamageCalc());
                        }
                    }

                    int preSevere;
                    if (severeMap != null) {
                        preSevere = severeMap.getOrDefault(soldier.getId(), 0);
                        // 重伤比兵损还多，前面肯定算错了！！
                        if (preSevere > loss) {
                            LOGGER.error("BattleErrLog role:{} adapterId={} calcLossDetailForEach fail preSevere > loss, pls check out, soldierId:{}, alive:{}, loss:{}, preSevere:{} ratio:{}", role,unit.getAdapterId(), soldier.getId(), unit.getData().tempAliveCount(), loss, preSevere, ratio);
                            preSevere = loss;
                        }
                    } else {
                        preSevere = (int) Math.min(loss, Math.ceil(loss * ratio.getSevereRatio()));
                    }

                    // 计算重伤，死亡，轻伤
                    SoldierLossData soldierLossData = calcLoss(loss, preSevere, ratio.getDeadRatio());
                    // 临时损失加上
                    int totalLoss = soldierLossData.totalLoss();
                    if (totalLoss != 0) {
                        unit.getData().applyTempLoss(totalLoss);
                        lossDetailForEach.computeIfAbsent(unit.getAdapterId(), v -> Maps.newHashMap()).put(soldier.getId(), soldierLossData);
                    }
                }
            }
        }

        // 计算防御塔兵损详情
        if (role.getGuardTower() != null) {
            // 防御塔只有死亡
            int guardTowerLoss = lossForEach.getOrDefault(role.getRoleId(), Maps.newHashMap()).getOrDefault(role.getGuardTower().getId(), 0);
            guardTowerLoss = Math.min(guardTowerLoss, role.getGuardTower().getTowerUnit().getData().tempAliveCount());
            if (guardTowerLoss > 0) {
                SoldierLossData guardTowerLossData = new SoldierLossData()
                        .setSevere(0)
                        .setDead(guardTowerLoss)
                        .setSlight(0);
                role.getGuardTower().getTowerUnit().getData().applyTempLoss(guardTowerLossData.totalLoss());
                lossDetailForEach.computeIfAbsent(role.getRoleId(), v -> Maps.newHashMap()).put(role.getGuardTower().getId(), guardTowerLossData);
            }
        }
        return lossDetailForEach;
    }

    /**
     * 计算预重伤
     */
    private static Map<Integer, Integer> calcSevereMap(BattleTickContext tickContext,
                                                       BattleRoleSettleOnly role,
                                                       Map<Integer, Integer> totalLossMap,
                                                       Map<CommonEnum.SceneObjType, SevereDeadRatio> ratioMap) {
        // 实际重伤 = 单次伤害T1步兵总损失 * 重伤率 + T1步兵随机分配重伤数
        // 随机分配重伤数 = 部队单次伤害造成的总重伤数 - ∑单次伤害各兵种预重伤数（总兵损是向上取整的，单兵的向下取整的，所以必然会出现误差重伤，所以要分配出去）
        Map<Integer, Integer> severeMap = Maps.newHashMap();

        SevereDeadRatio ratio = ratioMap.getOrDefault(role.getType(), SevereDeadRatio.empty());
        int totalLoss = 0;
        int totalSevere = 0;
        double severeRatio = ratio.getSevereRatio();

        for (SoldierGroup dGroup : role.getGroupMap().values()) {
            for (Soldier soldier : dGroup.getSoldiers()) {
                int loss = totalLossMap.getOrDefault(soldier.getId(), 0);
                loss = Math.min(loss, soldier.tempAliveCount());
                totalLoss += loss;
                if (loss > 0) {
                    // 单次伤害T1步兵预重伤数 = 单次伤害T1步兵总损失 * 重伤率 结果向下取整
                    int severe = (int) Math.floor(loss * severeRatio);
                    totalSevere += severe;
                    severeMap.put(soldier.getId(), severe);
                }
            }
        }

        // 单次伤害部队总体重伤数 = 该次伤害导致部队总损失 * 重伤率 结果向上取整
        int realTotalLoss = Math.min(totalLoss, role.tempAliveCount());
        int realTotalSeverNum = (int) Math.ceil(realTotalLoss * severeRatio);
        // 剩余的需要随机的重伤数，因为取整不同产生的误差
        int diff = realTotalSeverNum - totalSevere;
        if (diff < 0) {
            LOGGER.error("BattleErrLog role:{} calcSevereMap failed, diff < 0, realTotalSeverNum:{}, totalSevere:{}", role, realTotalSeverNum, totalSevere);
        } else if (diff > 0) {
            if (BattleGround.isLogEnable) {
                LOGGER.debug("BattleLog settle process round:{} role:{} calcSevereMap diff:{} to dispatch.", tickContext.getGroundRound(), role, diff);
            }
            // build一个随机池
            Map<Integer, List<Integer>> randomMap = Maps.newHashMap();
            for (SoldierGroup group : role.getGroupMap().values()) {
                for (int i = 0; i < group.getSoldiers().size(); i++) {
                    if (group.getSoldiers().get(i).tempAliveCount() > 0) {
                        randomMap.computeIfAbsent(group.getType(), v -> Lists.newArrayList()).add(i);
                    }
                }
            }

            // 随机分配取整产生的误差重伤数
            int i = 0;
            while (i < diff) {
                if (randomMap.keySet().isEmpty()) {
                    break;
                }

                // 随一个兵类型出来
                int randomSoldierType = RandomUtils.randomList(Lists.newArrayList(randomMap.keySet()));
                List<Integer> indexs = randomMap.get(randomSoldierType);
                if (IterableUtils.isEmpty(indexs)) {
                    randomMap.remove(randomSoldierType);
                    continue;
                }

                // 随一个兵种出来
                Integer randomSoldierIndex = RandomUtils.randomList(indexs);
                Soldier randomSoldier = role.getGroupMap().get(randomSoldierType).getSoldiers().get(randomSoldierIndex);
                int newSevere = severeMap.getOrDefault(randomSoldier.getId(), 0) + 1;
                if (newSevere <= totalLossMap.getOrDefault(randomSoldier.getId(), 0) && newSevere <= randomSoldier.tempAliveCount()) {
                    severeMap.put(randomSoldier.getId(), newSevere);
                    i++;
                } else {
                    // 条件不满足，移出随机池
                    indexs.remove(randomSoldierIndex);
                }
            }

            if (i != diff) {
                LOGGER.error("BattleErrLog role:{} calcSevereMap dispatch diff no finish, diff:{}, i:{}", role, diff, i);
            }
        }

        return severeMap;
    }

    /**
     * 分配兵损
     * 特别：
     * 1. 兵损分配取整方式为四舍五入，且最小值=1，因此会出现分配后兵损总数，大于总兵损数
     * 2. 对于四舍五入产生的误差，重新分配出去
     */
    private static Map<Long, Integer> calcSoldierLossForEach(BattleTickContext tickContext,
                                                             BattleRoleSettleOnly role,
                                                             Map<Integer, Integer> totalLossMap,
                                                             Soldier soldier) {
        // 每个成员的兵损map
        Map<Long, Integer> lossMapForEach = Maps.newHashMap();
        // 总兵损
        int realSumLoss = totalLossMap.getOrDefault(soldier.getId(), 0);
        int totalAlive = soldier.tempAliveCount();
        // 取兵损和存活士兵的最小值
        realSumLoss = Math.min(realSumLoss, totalAlive);
        if (realSumLoss <= 0) {
            return lossMapForEach;
        }

        int sumLoss = 0;
        // 计算每个成员的兵损
        for (SoldierUnit unit : soldier.getSoldierUnitList()) {
            int unitAlive = unit.getData().tempAliveCount();
            if (unitAlive <= 0) {
                continue;
            }
            // 最小兵损为1. 此处会出现实际兵损总和大于需要分配兵损的情况
            int childLoss = Math.max(1, MathUtils.roundInt((double) realSumLoss * unitAlive / totalAlive));
            lossMapForEach.put(unit.getAdapterId(), childLoss);
            sumLoss += childLoss;
        }

        // 因为上面四舍五入，产生的误差, 把多余的战损分出去
        int diff = realSumLoss - sumLoss;
        if (diff > 0) {
            if (BattleGround.isLogEnable) {
                LOGGER.debug("BattleLog settle process round:{} role:{} calcLossMap diff:{}, realSumLoss:{}, sumLoss:{}", tickContext.getGroundRound(), role, diff, realSumLoss, sumLoss);
            }
            int totalRemainAlive = 0;
            List<Pair<SoldierUnit, Integer>> ableUnit = Lists.newArrayList();
            for (SoldierUnit unit : soldier.getSoldierUnitList()) {
                int remainAlive = unit.getData().tempAliveCount() - lossMapForEach.getOrDefault(unit.getAdapterId(), 0);
                // 分给还有兵能扣的成员
                if (remainAlive > 0) {
                    ableUnit.add(Pair.of(unit, remainAlive));
                    totalRemainAlive += remainAlive;
                }
            }

            if (diff > totalRemainAlive) {
                LOGGER.error("BattleErrLog settle process round:{} role:{} diff:{}, totalRemainAlive:{}, soldiers:{}", tickContext.getGroundRound(), role, diff, totalRemainAlive, soldier.getSoldierUnitList());
            } else {
                // 根据剩余可扣兵力，倒序
                ableUnit.sort(Comparator.comparing(it -> -it.getSecond()));

                if (!ableUnit.isEmpty()) {
                    // 如果有余数，每个unit最终分配的数量 = 在商的基础上+1
                    int remainDiff = diff % ableUnit.size();
                    int base = diff / ableUnit.size();
                    for (Pair<SoldierUnit, Integer> unit : ableUnit) {
                        int toAdd = remainDiff > 0 ? base + 1 : base;
                        if (toAdd > 0) {
                            lossMapForEach.put(unit.getFirst().getAdapterId(), lossMapForEach.getOrDefault(unit.getFirst().getAdapterId(), 0) + toAdd);
                            remainDiff--;
                        }
                    }
                }
            }
        }
        return lossMapForEach;
    }

    private static SoldierLossData calcLoss(int loss, int severe, double deadRatio) {
        int dead = (int) Math.max(0, Math.ceil(severe * deadRatio));
        severe = Math.max(0, severe - dead);
        int slight = Math.max(0, loss - severe - dead);
        return new SoldierLossData()
                .setSevere(severe)
                .setDead(dead)
                .setSlight(slight);
    }

    private static void mergeLossDetailForEach(Map<Long, Map<Integer, SoldierLossData>> lossMap, Map<Long, Map<Integer, SoldierLossData>> lossDetailForEach) {
        for (Map.Entry<Long, Map<Integer, SoldierLossData>> entry : lossMap.entrySet()) {
            for (Map.Entry<Integer, SoldierLossData> entry1 : entry.getValue().entrySet()) {
                lossDetailForEach.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap())
                        .computeIfAbsent(entry1.getKey(), v -> new SoldierLossData())
                        .merge(entry1.getValue());
            }
        }
    }

    private static void logDamage(BattleRoleSettleOnly role, DamageContext dmgCtx, Map<Long, Map<Integer, SoldierLossData>> lossDetailForEach) {
        if (lossDetailForEach.isEmpty()) {
            BattleLogUtil.logUnitDamage(role, dmgCtx, 0, 0, null);
        } else {
            for (Map.Entry<Long, Map<Integer, SoldierLossData>> entry : lossDetailForEach.entrySet()) {
                long memberRoleId = entry.getKey();
                for (Map.Entry<Integer, SoldierLossData> entry1 : entry.getValue().entrySet()) {
                    int soldierId = entry1.getKey();
                    SoldierLossData lossData = entry1.getValue();
                    BattleLogUtil.logUnitDamage(role, dmgCtx, soldierId, memberRoleId, lossData);
                }
            }
        }
    }
    // endregion

    // region treatment
    public List<SoldierLossDTO> settleTreatment(BattleTickContext tickContext) {
        List<SoldierLossDTO> treatmentList = settleTreatment(tickContext, role);
        if (BattleGround.isLogEnable) {
            LOGGER.debug("BattleLog settle process round:{} role:{}, settle treatment:{}", tickContext.getGroundRound(), role, treatmentList);
        }
        if (!treatmentList.isEmpty()) {
            role.getAdapter().handleSoldierTreat(treatmentList);
        }
        return treatmentList;
    }

    private static List<SoldierLossDTO> settleTreatment(BattleTickContext tickContext, BattleRoleSettleOnly role) {
        Map<Integer, SoldierLossDTO> treatmentMap = Maps.newHashMap();
        try {
            // 治疗数量不能超过集结容量上限
            int maxRoleCanTreat = role.getRallyCapacity() - role.getSnapShot("settleTreatment").soldierAliveCount();
            if (maxRoleCanTreat <= 0) {
                return new ArrayList<>(treatmentMap.values());
            }
            // 遍历结算所有治疗效果
            for (TreatmentContext treatmentCtx : role.getContext().getTreatmentCtxLists()) {
                if (maxRoleCanTreat <= 0) {
                    LOGGER.error("BattleErrLog MaxRoleCanTreat={} <= 0, role={} RallyCapacity={} SoldierAlive={}", maxRoleCanTreat, role, role.getRallyCapacity(), role.getSnapShot("settleTreatment").soldierAliveCount());
                    break;
                }
                // 结算每个兵种的治疗量
                for (Map.Entry<Integer, Integer> entry : treatmentCtx.getTreatMap().entrySet()) {
                    int soldierId = entry.getKey();
                    int treatValue = entry.getValue();

                    SoldierTypeTemplate template = ResHolder.findTemplate(SoldierTypeTemplate.class, soldierId);
                    if (template == null) {
                        LOGGER.error("BattleErrLog ParallelSettleHandler settleTreatment role={} soldierId={} template not found", role, soldierId);
                        continue;
                    }
                    SoldierGroup soldierGroup = role.getGroupMap().get(template.getSoldierType());
                    if (soldierGroup == null) {
                        LOGGER.error("BattleErrLog ParallelSettleHandler settleTreatment role={} soldierId={} soldierGroup not found", role, soldierId);
                        continue;
                    }

                    for (Soldier soldier : soldierGroup.getSoldiers()) {
                        if (soldier.getId() != soldierId) {
                            continue;
                        }
                        // 做一次治疗量最大值的保底，本次治疗的治疗数量 = MIN(剩余集结容量上限，士兵轻伤数，技能治疗量)
                        int realTreatValue = Math.min(Math.min(maxRoleCanTreat, soldier.getMaxCanTreat()), treatValue);
                        if (realTreatValue <= 0) {
                            break;
                        }

                        // 分配治疗量给每个成员
                        Map<Long, Integer> treatMapForEach = Maps.newHashMap();
                        int sumTreat = 0;
                        for (SoldierUnit soldierUnit : soldier.getSoldierUnitList()) {
                            if (soldierUnit.maxCanTreat() <= 0) {
                                continue;
                            }
                            int childTreat = MathUtils.roundInt((double) realTreatValue * soldierUnit.maxCanTreat() / soldier.getMaxCanTreat());
                            sumTreat += childTreat;
                            treatMapForEach.put(soldierUnit.getAdapterId(), childTreat);
                        }

                        // 因为上面四舍五入，产生的误差, 把多余的治疗分出去
                        int diff = Math.min(realTreatValue - sumTreat, maxRoleCanTreat);
                        if (diff > 0) {
                            if (BattleGround.isLogEnable) {
                                LOGGER.debug("BattleLog settle process round:{} role:{} settleTreatment diff:{}, treatSoldier:{}, totalTreat:{} maxRoleCanTreat:{}", tickContext.getGroundRound(), role, diff, treatValue, realTreatValue, maxRoleCanTreat);
                            }
                            int totalRemainTreat = 0;
                            List<Pair<SoldierUnit, Integer>> ableUnit = Lists.newArrayList();
                            for (SoldierUnit unit : soldier.getSoldierUnitList()) {
                                int remainCanTreat = unit.maxCanTreat() - treatMapForEach.getOrDefault(unit.getAdapterId(), 0);
                                // 分给还有兵能治疗的成员
                                if (remainCanTreat > 0) {
                                    ableUnit.add(Pair.of(unit, remainCanTreat));
                                    totalRemainTreat += remainCanTreat;
                                }
                            }

                            if (diff > totalRemainTreat) {
                                LOGGER.error("BattleErrLog settle process round:{} role:{} diff:{}, totalRemainTreat:{}, soldiers:{}", tickContext.getGroundRound(), role, diff, totalRemainTreat, soldier.getSoldierUnitList());
                            } else {
                                // 根据剩余可治疗兵力，倒序
                                ableUnit.sort(Comparator.comparing(it -> -it.getSecond()));

                                if (!ableUnit.isEmpty()) {
                                    // 如果有余数，每个unit最终分配的数量 = 在商的基础上+1
                                    int remainDiff = diff % ableUnit.size();
                                    int base = diff / ableUnit.size();
                                    for (Pair<SoldierUnit, Integer> unit : ableUnit) {
                                        int toAdd = remainDiff > 0 ? base + 1 : base;
                                        if (toAdd > 0) {
                                            treatMapForEach.put(unit.getFirst().getAdapterId(), treatMapForEach.getOrDefault(unit.getFirst().getAdapterId(), 0) + toAdd);
                                            remainDiff--;
                                        }
                                    }
                                }
                            }
                        }

                        SoldierLossDTO treatmentDTO = treatmentMap.computeIfAbsent(soldier.getId(), v -> new SoldierLossDTO(soldier.getId()));
                        applyTreat(role, treatmentCtx, treatmentDTO, soldier, treatMapForEach);
                        break;
                    }
                    maxRoleCanTreat -= treatValue;
                }
            }
        } catch (Exception e) {
            LOGGER.error("BattleErrLog role={}", role, e);
        } finally {
            role.getContext().getTreatmentCtxLists().clear();
        }
        return new ArrayList<>(treatmentMap.values());
    }

    private static void applyTreat(BattleRoleSettleOnly role,
                                   TreatmentContext treatmentCtx,
                                   SoldierLossDTO treatmentDTO,
                                   Soldier soldier,
                                   Map<Long, Integer> treatMapForEach) {
        SoldierLossData treatmentData = new SoldierLossData();
        for (SoldierUnit soldierUnit : soldier.getSoldierUnitList()) {
            int realTreat = treatMapForEach.getOrDefault(soldierUnit.getAdapterId(), 0);
            if (realTreat <= 0) {
                continue;
            }

            realTreat = soldierUnit.applyTreat(realTreat);

            SoldierLossData childTreatmentData = new SoldierLossData().setTreatment(realTreat);
            treatmentData.merge(childTreatmentData);
            treatmentDTO.plusChildLoss(soldierUnit.getAdapterId(), childTreatmentData);

            BattleLogUtil.logUnitTreatment(role, treatmentCtx, soldier.getId(), soldierUnit.getAdapterId(), childTreatmentData);
        }
        treatmentDTO.plusLoss(treatmentData);
    }
    // endregion

    // region kill
    public void settleKill(BattleTickContext tickContext) {
        try {
            for (Map.Entry<Long, Map<Integer, Integer>> entry : killResultList.entrySet()) {
                settleSingleKill(tickContext, role, entry.getKey(), entry.getValue());
            }
        } finally {
            killResultList.clear();
        }
    }

    /**
     * 结算自己的击杀
     */
    private static void settleSingleKill(BattleTickContext tickContext, BattleRoleSettleOnly role, long otherRoleId, Map<Integer, Integer> totalKillMap) {
        for (Map.Entry<Integer, Integer> killEntry : totalKillMap.entrySet()) {
            int soldierId = killEntry.getKey();
            int totalKill = killEntry.getValue();

            if (totalKill <= 0) {
                continue;
            }

            BattleRelationContext relationContext = role.getRelationContext(otherRoleId);
            if (relationContext == null) {
                // 战斗关系没了，不记录了。 如：挂了一个DOT后，施法者没了
                continue;
            }
            BattleRecord.RoleRecord selfRecord = relationContext.getRoleRecord(role.getRoleId());
            // 打的是野怪，日落峡谷用totalLoss，打别的用totalKill
            Map<Long, Map<Integer, Integer>> killForEach = Maps.newHashMap();
            int realTotalKill = 0;

            // 把击杀分配给每个成员
            for (Map.Entry<Long, Double> entry : role.getSnapShot("SettleKill").getAtkRatioMap().entrySet()) {
                // 击杀数 = 攻击力占比 * 总击杀 结果四舍五入
                int kill = MathUtils.roundInt(entry.getValue() * totalKill);
                if (kill <= 0) {
                    continue;
                }
                selfRecord.addKill(entry.getKey(), soldierId, kill);
                realTotalKill += kill;

                Map<Integer, Integer> killMap = killForEach.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap());
                killMap.put(soldierId, killMap.getOrDefault(soldierId, 0) + kill);
            }

            // 分配剩余的给车头
            int diff = totalKill - realTotalKill;
            if (diff != 0) {
                long leaderRoleId = role.getAdapter().getLeaderRoleId();
                Map<Integer, Long> leaderKillMap = selfRecord.getMemberKill(leaderRoleId);
                // 策划说的，全部分给车头，如果车头不够分，就丢弃
                if (diff > 0) {
                    selfRecord.addKill(leaderRoleId, soldierId, diff);

                    Map<Integer, Integer> killMap = killForEach.computeIfAbsent(leaderRoleId, v -> Maps.newHashMap());
                    killMap.put(soldierId, killMap.getOrDefault(soldierId, 0) + diff);
                } else {
                    LOGGER.warn("BattleLog settle process round:{} role:{}, give diff:{} soldierId:{} to leader role:{}, leaderKillMap:{}", tickContext.getGroundRound(), role, diff, soldierId, leaderRoleId, leaderKillMap);
                }
                realTotalKill += diff;
                if (BattleGround.isLogEnable) {
                    LOGGER.debug("BattleLog settle process round:{} role:{}, give diff:{} to leader role:{}, totalKill:{}, realTotalKill:{}", tickContext.getGroundRound(), role, diff, leaderRoleId, totalKill, realTotalKill);
                }
            }

            if (realTotalKill != totalKill) {
                LOGGER.error("BattleErrLog settle process round:{} role:{}, calc kill fail totalKill:{}, realTotalKill:{}, atkRatioMap:{}", tickContext.getGroundRound(), role, totalKill, realTotalKill, role.getSnapShot("ErrorLog").getAtkRatioMap());
            }

            if (BattleGround.isLogEnable) {
                LOGGER.debug("BattleLog settle process round:{} role:{}, totalKill:{}, killForEach:{}", tickContext.getGroundRound(), role, totalKill, killForEach);
            }
        }
    }
    // endregion
}

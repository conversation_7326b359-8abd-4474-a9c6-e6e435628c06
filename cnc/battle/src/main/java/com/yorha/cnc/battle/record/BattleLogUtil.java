package com.yorha.cnc.battle.record;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.buf.Buff;
import com.yorha.cnc.battle.buf.PendingBuff;
import com.yorha.cnc.battle.buf.ShieldBuff;
import com.yorha.cnc.battle.common.ActionType;
import com.yorha.cnc.battle.context.*;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.core.BattleRoleSettleOnly;
import com.yorha.cnc.battle.record.action.*;
import com.yorha.cnc.battle.skill.SkillFacade;
import com.yorha.cnc.battle.soldier.SoldierLossData;
import com.yorha.cnc.battle.unit.BattleHero;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import com.yorha.proto.StructPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.BattleBuffTemplate;
import res.template.SkillEffectTemplate;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class BattleLogUtil {
    private static final Logger LOGGER = LogManager.getLogger(BattleLogUtil.class);

    private static void tryCatch(Consumer<?> consumer) {
        try {
            consumer.accept(null);
        } catch (Exception e) {
            LOGGER.error("BattleErrLog", e);
        }
    }

    /**
     * 记录部队变化
     * <p>
     * 广播给所有relation
     */
    public static void logTroop(@NotNull BattleRole role, int soldierNum, SoldierNumChangeReason logBuffType, long memberId) {
        tryCatch(it -> {
            TroopAction troopAction = new TroopAction()
                    .setSoldierNum(soldierNum)
                    .setReason(logBuffType)
                    .setNewMemberId(memberId);

            role.forEachValidRelation(relation -> {
                RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(role.getRoleId());
                roleAction.addTroopList(troopAction);
            });
        });
    }

    /**
     * 记录普攻
     */
    public static void logAtk(@NotNull BattleRole attacker, @NotNull BattleRole defender, boolean isAtkBack) {
        tryCatch(it -> {
            BattleRelation relation = attacker.relationWith(defender.getRoleId());
            if (isAtkBack) {
                relation.getContext().curRoundCtx().getRoleAction(attacker.getRoleId()).setAtkBackAction();
            } else {
                relation.getContext().curRoundCtx().getRoleAction(attacker.getRoleId()).setAtkAction();
            }
        });
    }

    /**
     * 记录受到夹击
     * <p>
     * 广播给所有relation
     */
    public static void logBePincerAtk(@NotNull BattleRole role, int pincerNum) {
        tryCatch(it -> {
            BuffAction buffAction = new BuffAction()
                    .setLogBuffType(CommonEnum.BattleLogBuffType.BLBT_PINCER)
                    .setOpType(CommonEnum.BattleBuffOperateType.BBOT_ADD)
                    .setBuffId(pincerNum);

            role.forEachValidRelation(relation -> {
                RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(role.getRoleId());
                roleAction.addBuffList(buffAction);
            });
        });
    }

    /**
     * 记录技能效果
     */
    public static void logSkillEffect(@NotNull List<PlayerScene.EffectDTO> effects,
                                      @NotNull BattleRole attacker,
                                      @NotNull SkillEffectTemplate template,
                                      EffectContext effectContext) {
        tryCatch(it -> {
            BattleRole realAttacker = attacker;
            if (attacker.getMasterRole() != null) {
                realAttacker = attacker.getMasterRole();
            }
            for (PlayerScene.EffectDTO effect : effects) {
                for (BattleRelation relation : getNeedLogRelations(realAttacker, effect.getTargetId())) {
                    if (relation.getContext().curRoundCtx().isEnd()) {
                        continue;
                    }
                    // 技能释放log, DOT/HOT不需要发SkillAction
                    if (!effectContext.hasAttachBuff()) {
                        logSkillFire(realAttacker, relation, effectContext);
                    }

                    // buff效果走buffAction
                    if (template.getType() == CommonEnum.SkillEffectType.SET_ADD_BUFF) {
                        continue;
                    }
                    if (template.getType() == CommonEnum.SkillEffectType.SET_ADD_BATCH_BUFF) {
                        continue;
                    }
                    // 技能效果Action
                    RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(effect.getTargetId());
                    EffectAction effectAction = new EffectAction(effectContext, template.getType(), effect.getValue());
                    roleAction.putEffectList(effectAction);
                }
            }
        });
    }

    /**
     * 记录伤害
     */
    public static void logUnitDamage(@NotNull BattleRoleSettleOnly role,
                                     @NotNull DamageContext dmgCtx,
                                     int unitId,
                                     long memberRoleId,
                                     SoldierLossData lossData) {
        tryCatch(it -> {
            BattleRelationContext relationCtx = role.getRelationContext(dmgCtx.getAttackerId());
            if (relationCtx == null) {
                // TODO awei 这里dot的伤害先不处理，后面做战报优化的时候做
                if (!dmgCtx.isDOT()) {
                    LOGGER.error("BattleErrLog logUnitDamage failed, roleId={}, attackerRole={} relation not found.", role, dmgCtx.getAttackerId());
                }
                return;
            }

            if (dmgCtx.isOrdinaryAttackOrBack()) {
                // 记录普攻的伤害日志
                RoundRoleAction roleAction = relationCtx.curRoundCtx().getRoleAction(dmgCtx.getAttackerId());
                AtkAction oriOrBackAtkAction = dmgCtx.getActionCtx().getType() == ActionType.ORDINARY_ATTACK ? roleAction.getAtkAction() : roleAction.getAtkBackAction();
                if (oriOrBackAtkAction != null) {
                    if (dmgCtx.getDecreaseDamage() > 0) {
                        oriOrBackAtkAction.setShieldDamage((long) dmgCtx.getDecreaseDamage());
                    }
                    if (lossData != null) {
                        oriOrBackAtkAction.plusUnitDataChange(memberRoleId, unitId, lossData);
                    }
                }
            } else if (dmgCtx.isSkill()) {
                // 记录技能的伤害日志
                RoundRoleAction roleAction = relationCtx.curRoundCtx().getRoleAction(role.getRoleId());
                EffectAction effectAction = roleAction.getEffectAction(dmgCtx.getEffectContext());
                if (effectAction != null) {
                    if (dmgCtx.getDecreaseDamage() > 0) {
                        effectAction.setShieldDamage((long) dmgCtx.getDecreaseDamage());
                    }
                    if (lossData != null) {
                        effectAction.plusUnitDataChange(memberRoleId, unitId, lossData);
                    }
                }
            }
        });
    }

    /**
     * 记录治疗
     */
    public static void logUnitTreatment(@NotNull BattleRoleSettleOnly role,
                                        @NotNull TreatmentContext treatmentCtx,
                                        int unitId,
                                        long memberRoleId,
                                        SoldierLossData childTreatment) {
        tryCatch(it -> {
            BattleRelationContext relationCtx = role.getRelationContext(role.getTargetId());
            if (relationCtx == null) {
                return;
            }

            if (relationCtx.curRoundCtx().isEnd()) {
                return;
            }
            RoundRoleAction roleAction = relationCtx.curRoundCtx().getRoleAction(role.getRoleId());
            EffectAction effectAction = roleAction.getEffectAction(treatmentCtx.getExecutorInfo());
            if (effectAction != null) {
                effectAction.plusUnitDataChange(memberRoleId, unitId, childTreatment);
            }
        });
    }

    /**
     * 记录初始化的buff
     */
    public static void logInitBuff(RoundRoleAction roleAction, @NotNull Map<Integer, Buff> buffStore) {
        tryCatch(it -> {
            for (Buff buff : buffStore.values()) {
                if (buff.isWorking()) {
                    BuffAction buffAction = new BuffAction()
                            .setBuffId(buff.getTemplateId())
                            .setLogBuffType(CommonEnum.BattleLogBuffType.BLBT_SKILL)
                            .setOpType(CommonEnum.BattleBuffOperateType.BBOT_INIT)
                            .setLifeCycleValue(buff.getLifeCycle().getValue())
                            .setExecutorInfo(buff.getExecutorInfo());
                    if (buff instanceof ShieldBuff) {
                        buffAction.setValue(buff.getValue());
                    }
                    roleAction.addBuffList(buffAction);
                }
            }
        });
    }

    /**
     * 记录添加buff
     * <p>
     * 广播给所有relation
     */
    public static void logAddBuff(@NotNull BattleRole targetRole, int buffId, CommonEnum.BattleLogBuffType type) {
        tryCatch(it -> {
            Buff buff = targetRole.getBuffHandler().getBuff(buffId);
            BuffAction buffAction = new BuffAction()
                    .setBuffId(buffId)
                    .setLogBuffType(type)
                    .setOpType(CommonEnum.BattleBuffOperateType.BBOT_ADD);
            if (buff != null) {
                buffAction.setLifeCycleValue(buff.getLifeCycle().getValue())
                        .setExecutorInfo(buff.getExecutorInfo())
                        .setLayer(buff.getLayer())
                        .setValue(buff.getValue());
            }

            targetRole.forEachValidRelation(relation -> {
                if (!relation.getContext().curRoundCtx().isEnd()) {
                    RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(targetRole.getRoleId());
                    roleAction.addBuffList(buffAction);
                }
            });
        });
    }

    /**
     * 记录添加预释放buff
     * <p>
     * 广播给所有relation
     */
    public static void logAddPendingBuff(@NotNull BattleRole targetRole, PendingBuff pendingBuff, CommonEnum.BattleLogBuffType type) {
        tryCatch(it -> {
            BuffAction buffAction = new BuffAction()
                    .setBuffId(pendingBuff.getBuffId())
                    .setLogBuffType(type)
                    .setOpType(CommonEnum.BattleBuffOperateType.BBOT_ADD)
                    .setLifeCycleValue(pendingBuff.getLifeCycle())
                    .setExecutorInfo(pendingBuff.getExecutorInfo())
                    .setLayer(pendingBuff.getLayer())
                    .setValue(pendingBuff.getValue());

            targetRole.forEachValidRelation(relation -> {
                if (!relation.getContext().curRoundCtx().isEnd()) {
                    RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(targetRole.getRoleId());
                    roleAction.addBuffList(buffAction);
                }
            });
        });
    }

    /**
     * 记录移除buff
     * <p>
     * 广播给所有relation
     */
    public static void logRemoveBuff(@NotNull BattleRole targetRole, int buffId) {
        tryCatch(it -> {
            BuffAction buffAction = new BuffAction()
                    .setBuffId(buffId)
                    .setOpType(CommonEnum.BattleBuffOperateType.BBOT_REMOVE);

            targetRole.forEachValidRelation(relation -> {
                RoundContext roundContext = relation.getContext().curRoundCtx();
                if (!roundContext.isEnd()) {
                    RoundRoleAction roleAction = roundContext.getRoleAction(targetRole.getRoleId());
                    roleAction.addBuffList(buffAction);
                } else {
                    LOGGER.warn("BattleLog BattleLogUtil logTroop roleAction is null, relation={} role={} buffId={}", relation, targetRole, buffId);
                }
            });
            if (targetRole.getAllValidRelation().size() <= 0){
                // endAllRelation的时候额外打一下，不然丢了
                BattleGround.getBattleLog().printfRemoveBuffWithRole(targetRole, buffAction);
            }
        });
    }

    /**
     * 记录免疫buff
     * <p>
     * 广播给所有relation
     */
    public static void logImmunityBuff(BattleRole executor, @NotNull BattleRole targetRole, int buffId, EffectContext effectContext) {
        tryCatch(it -> {
            BuffAction buffAction = new BuffAction()
                    .setBuffId(buffId)
                    .setOpType(CommonEnum.BattleBuffOperateType.BBOT_IMMUNITY)
                    .setLogBuffType(CommonEnum.BattleLogBuffType.BLBT_SKILL)
                    .setExecutorInfo(effectContext);
            for (BattleRelation relation : getNeedLogRelations(executor, targetRole.getRoleId())) {
                RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(targetRole.getRoleId());
                roleAction.addBuffList(buffAction);
            }
        });
    }

    /**
     * 记录技能释放
     */
    public static void logSkillFire(@NotNull BattleRole attacker,
                                    @NotNull BattleRelation relation,
                                    @NotNull EffectContext effectContext) {
        tryCatch(it -> {
            RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(attacker.getRoleId());
            SkillAction skillAction = new SkillAction(effectContext, false);
            roleAction.addSkillAction(skillAction);
        });
    }

    /**
     * 记录技能预备释放
     * <p>
     * 广播给所有relation
     */
    public static void logSkillPrepareFire(@NotNull BattleRole attacker, @NotNull SkillFacade skill, BattleHero hero) {
        tryCatch(it -> {
            EffectContext effectContext = EffectContext.newBuilder()
                    .setCastRole(attacker)
                    .setSkillId(skill.getId())
                    .setType(CommonEnum.BattleLogSkillType.BLST_SKILL)
                    .setHeroId(hero.getId())
                    .setEffectId(0)
                    .setDotContext(null)
                    .setAttachBuffId(0)
                    .setLeaderRoleId(attacker.getAdapter().getLeaderRoleId())
                    .build(true);
            SkillAction skillAction = new SkillAction(effectContext, true);
            // 预备释放技能需要广播
            attacker.forEachValidRelation(relation -> {
                RoundRoleAction roleAction = relation.getContext().curRoundCtx().getRoleAction(attacker.getRoleId());
                roleAction.addPrepareSkillAction(skillAction);
            });
        });
    }

    /**
     * 获取需要记录的所有relation
     */
    private static List<BattleRelation> getNeedLogRelations(@NotNull BattleRole attacker, Long targetId) {
        List<BattleRelation> relations = Lists.newArrayList();
        if (targetId == attacker.getRoleId()) {
            // 目标是自己，要广播给所有relation
            attacker.forEachValidRelation(relations::add);
        } else {
            // 目标是敌人，广播给对应的relation
            BattleRelation relation = attacker.relationWith(targetId);
            if (relation != null && !relation.isStopped()) {
                relations.add(relation);
            }
        }
        return relations;
    }
}

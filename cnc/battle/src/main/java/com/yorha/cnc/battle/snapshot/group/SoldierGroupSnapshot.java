package com.yorha.cnc.battle.snapshot.group;


import com.yorha.cnc.battle.snapshot.unit.SoldierSnapshot;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SoldierGroupSnapshot {
    private final List<SoldierSnapshot> soldiers;
    private final int type;
    private final int aliveCount;
    private final double totalAtk;
    private final double totalDefence;
    private final double avgAtk;
    private final double avgDefence;

    public SoldierGroupSnapshot(int type, List<SoldierSnapshot> soldiers) {
        this.soldiers = soldiers;
        this.type = type;
        this.aliveCount = calcAliveCount();
        this.totalAtk = calcTotalAtk();
        this.totalDefence = calcTotalDefence();
        this.avgAtk = calcAvgAtk();
        this.avgDefence = calcAvgDefence();
    }

    public List<SoldierSnapshot> getSoldiers() {
        return soldiers;
    }

    public int aliveCount() {
        return this.aliveCount;
    }

    public double getTotalAtk() {
        return this.totalAtk;
    }

    public double getTotalDefence() {
        return this.totalDefence;
    }

    public double getAvgAtk() {
        return this.avgAtk;
    }

    public double getAvgDefence() {
        return this.avgDefence;
    }

    public int getType() {
        return type;
    }

    public int calcAliveCount() {
        return this.soldiers.stream().mapToInt(SoldierSnapshot::aliveCount).sum();
    }

    public double calcTotalAtk() {
        return this.soldiers.stream().mapToDouble(SoldierSnapshot::calcTotalAttack).sum();
    }

    public double calcTotalDefence() {
        return this.soldiers.stream().mapToDouble(SoldierSnapshot::calcTotalDefence).sum();
    }

    public double calcAvgAtk() {
        return getTotalAtk() / aliveCount();
    }

    public double calcAvgDefence() {
        return getTotalDefence() / aliveCount();
    }

    @Override
    public String toString() {
        return "士兵组快照{" +
                "soldiers=" + soldiers +
                ", type=" + type +
                ", aliveCount=" + aliveCount +
                ", totalAtk=" + totalAtk +
                ", totalDefence=" + totalDefence +
                ", avgAtk=" + avgAtk +
                ", avgDefence=" + avgDefence +
                '}';
    }
}

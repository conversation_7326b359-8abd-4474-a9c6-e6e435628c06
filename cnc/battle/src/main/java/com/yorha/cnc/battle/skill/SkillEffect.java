package com.yorha.cnc.battle.skill;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.cnc.battle.skill.effect.ISkillEffectValue;
import com.yorha.cnc.battle.skill.status.BattleTargetStatusFactory;
import com.yorha.cnc.battle.skill.status.IBattleTargetStatusChecker;
import com.yorha.cnc.battle.skill.status.impl.AlwaysTrueCheck;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SkillEffectTemplate;
import res.template.SkillRangeTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class SkillEffect {
    private static final Logger LOGGER = LogManager.getLogger(SkillEffect.class);

    private final int effectId;
    private final IBattleTargetStatusChecker selfChecker;
    private final IBattleTargetStatusChecker selfTwoChecker;
    private int lastTriggerRound;
    private final EffectContext effectContext;
    private boolean isSubEffect = false;

    private SkillEffect(int effectId, EffectContext effectContext) {
        this.effectId = effectId;
        this.selfChecker = BattleTargetStatusFactory.getSelfChecker(getTemplate().getSelfTroopsState1());
        this.selfTwoChecker = BattleTargetStatusFactory.getSelfChecker(getTemplate().getSelfTroopsState2());
        this.effectContext = effectContext;
    }

    public static SkillEffect create(int effectId, EffectContext effectContext) {
        SkillEffectTemplate skillEffectTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillEffectTemplate(effectId);
        if (skillEffectTemplate.getType() != CommonEnum.SkillEffectType.SET_ADD_ADDITION) {
            return new SkillEffect(effectId, effectContext);
        }
        return null;
    }

    public static SkillEffect createSubEffect(int effectId, EffectContext info) {
        SkillEffect skillEffect = create(effectId, info);
        if (skillEffect == null) {
            return null;
        }
        skillEffect.setSubEffect();
        return skillEffect;
    }

    public void clear() {
        lastTriggerRound = 0;
        selfChecker.clear();
        selfTwoChecker.clear();
    }

    public EffectContext getExecutorInfo() {
        return effectContext;
    }

    public SkillEffectTemplate getTemplate() {
        return ResHolder.getResService(SkillDataTemplateService.class).getSkillEffectTemplate(effectId);
    }

    /**
     * @param tickContext
     * @param actionCtx
     * @param attacker    攻击者
     * @param skillTarget 技能目标
     * @param info
     * @return
     */
    public EffectResult cast(BattleTickContext tickContext,
                             ActionContext actionCtx,
                             BattleRole attacker,
                             BattleRole skillTarget,
                             EffectContext info,
                             Set<Long> canNullSkillMarkTarget) {
        EffectResult res = new EffectResult(isDot() ? skillTarget : attacker);
        try {
            // 触发器判断
            if (!canTrigger(attacker, skillTarget)) {
                return res;
            }

            // cd
            if (isInCd(tickContext)) {
                return res;
            }

            recordCd(tickContext);

            ISkillEffectValue effectValue = SkillHelper.getValue(getTemplate());
            if (effectValue == null) {
                return res;
            }

            // 索敌
            List<BattleRole> targetList = searchTarget(this, attacker, skillTarget, canNullSkillMarkTarget);
            if (targetList == null) {
                return res;
            }
            res.getTargetRoleList().addAll(targetList);

            // 衰减系数
            double reduceRatio = calcReduce(targetList);

            // 触发技能效果
            EffectContext effectContext = info == null ? this.effectContext : info;

            // 日志-技能效果释放
            BattleGround.getBattleLog().printfSkillEffectCast(attacker, skillTarget, effectContext, targetList);

            for (BattleRole target : targetList) {
                actionCtx.getDamageAmend(target.getRoleId()).multiRatio(reduceRatio);
                res.getDto().addAll(effectValue.handle(tickContext, actionCtx, getTemplate(), attacker, target, effectContext));
            }
            if (getTemplate().getType() != CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT
                    && getTemplate().getType() != CommonEnum.SkillEffectType.SET_FIRE_SKILL) {
                BattleLogUtil.logSkillEffect(res.getDto(), attacker, getTemplate(), effectContext);
            }
        } catch (Exception e) {
            LOGGER.error("BattleErrLog SkillEffect cast effectId={} attacker={}, skillTarget={}", getTemplate().getId(), attacker, skillTarget, e);
        }
        return res;
    }

    public boolean canTrigger(BattleRole attacker, BattleRole originTarget) {
        Pair<String[], String[]> selfStatusPair = ResHolder.getResService(SkillDataTemplateService.class).getEffectSelfStatusValue(effectId);
        // 自身条件1判断
        if (!selfChecker.check(attacker, this, selfStatusPair.getFirst())) {
            return false;
        }
        // 自身条件2判断
        if (!selfTwoChecker.check(attacker, this, selfStatusPair.getSecond())) {
            return false;
        }
        selfChecker.mark();
        selfTwoChecker.mark();

        // 目标条件判断
        IBattleTargetStatusChecker targetChecker = SkillHelper.getTargetChecker(getTemplate());
        if (!(targetChecker instanceof AlwaysTrueCheck) && originTarget == null) {
            // 没有目标但是要判断目标，直接失败
            return false;
        }
        String[] targetParams = ResHolder.getResService(SkillDataTemplateService.class).getEffectTargetStatusValue(effectId);
        if (!targetChecker.check(originTarget, this, targetParams)) {
            return false;
        }

        // 触发概率判断
        if (getTemplate().getTriggerValue() <= 0) {
            return true;
        }
        return RandomUtils.trigger(getTemplate().getTriggerValue(), 10000);
    }

    private boolean isInCd(BattleTickContext tickContext) {
        int cd = getTemplate().getCd();
        if (cd <= 0 || lastTriggerRound <= 0) {
            return false;
        }

        return lastTriggerRound + cd > tickContext.getGroundRound();
    }

    private void recordCd(BattleTickContext tickContext) {
        lastTriggerRound = tickContext.getGroundRound();
    }

    /**
     * @param effect
     * @param castObj 施法者
     * @param target 普攻目标
     * @return
     */
    private static List<BattleRole> searchTarget(SkillEffect effect, BattleRole castObj, BattleRole target, Set<Long> canNullSkillMarkTarget) {
        SkillEffectTemplate effectTemplate = effect.getTemplate();

        if (effect.isSubEffect) {
            // 子效果不索敌，直接返回目标
            if (target == null) {
                return null;
            }
            ArrayList<BattleRole> ret = new ArrayList<>();
            ret.add(target);
            return ret;
        }

        // 1、确定施法者、普攻目标、施法目标
        // 施法目标（防御技能选中施法者，攻击技能选中预释放阶段的普通目标）
        BattleRole castTargetObj = SkillHelper.getReleaseTargetObj(effectTemplate, castObj, target);

        // 2、几何索敌
        Set<BattleRole> targetList = SkillHelper.getTargetListByRange(effectTemplate, castObj, castTargetObj, canNullSkillMarkTarget);

        // 3、过滤
        targetList = SkillHelper.filterTarget(effect, castObj, target, castTargetObj, targetList);

        // 4、修饰结果返回
        return SkillHelper.buildSearchResult(targetList);
    }

//    private static ErrorCode checkSelectTargetBySkill(FireSkillEvent event, BattleRole attack){
//        BattleRole selectTargetObj = attack.getTargetRole();
//        Skill skill = new Skill(event.getSkillId(), event.getHeroId(), attack);
//        SkillTemplate skillTemplate = ResHolder.getInstance().getValueFromMap(SkillTemplate.class, event.getSkillId());
//        for (Integer effectId : skillTemplate.getGroupSkillList()) {
//            SkillEffectTemplate effectTemplate = ResHolder.getInstance().getValueFromMap(SkillEffectTemplate.class, effectId);
//            BattleRole castTargetObj = SkillHelper.getReleaseTargetObj(effectTemplate, attack, selectTargetObj);
//            Set<BattleRole> targetList = SkillHelper.getTargetListByRange(effectTemplate, attack, castTargetObj, event.getTargetList());
//            targetList = SkillHelper.filterTarget(skill.getSkillTemplate(), attack, selectTargetObj, castTargetObj, targetList);
//            if (targetList.size() != skillMarkTarget.size()) {
//                targetList.stream().filter(it -> !skillMarkTarget.contains(it.getRoleId()))
//            }
//        }
//
//    }

    private double calcReduce(List<BattleRole> targetList) {
        int reduce = 100;
        if (getTemplate().getRange() > 0) {
            SkillRangeTemplate rangeTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillRangeTemplateOrNull(getTemplate().getRange());
            if (rangeTemplate != null) {
                reduce = rangeTemplate.getReduce();
            }
        }
        return Math.pow(reduce / 100f, Math.max(0, targetList.size() - 1));
    }

    public void setSubEffect() {
        isSubEffect = true;
    }

    public boolean isSubEffect() {
        return isSubEffect;
    }

    public boolean isDot() {
        return effectContext != null && effectContext.isDot();
    }
}

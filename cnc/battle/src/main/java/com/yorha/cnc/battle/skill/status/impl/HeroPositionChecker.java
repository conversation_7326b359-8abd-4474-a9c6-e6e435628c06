package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;

import static com.yorha.common.constant.BattleConstants.DEPUTY_POSITION;
import static com.yorha.common.constant.BattleConstants.MAIN_POSITION;

/**
 * 英雄位置
 *
 * <AUTHOR>
 */
public class Hero<PERSON>osition<PERSON><PERSON><PERSON> extends StatusChecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        int position = 0;
        if (role.getMainHero() != null && role.getMainHero().getId() == effect.getExecutorInfo().getHeroId()) {
            position = MAIN_POSITION;
        } else if (role.getDeputyHero() != null && role.getDeputyHero().getId() == effect.getExecutorInfo().getHeroId()) {
            position = DEPUTY_POSITION;
        }
        return position == Integer.parseInt(params[0]);
    }
}

package com.yorha.cnc.battle.unit;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.shape.Point;

public class BattleHelper {

    /**
     * 获取距离目标的战斗射程距离   用于判断是否能战斗
     */
    public static int getBattleDistance(BattleRole one, BattleRole other) {
        return one.getAdapter().getModelRadius() + other.getAdapter().getModelRadius() + ResHolder.getResService(ConstKVResService.class).getTemplate().getStandardRange() + 500;
    }

    public static boolean isBattleDistanceOk(BattleRole one, BattleRole other) {
        int battleDistance = getBattleDistance(one, other);
        return Point.calDisBetweenTwoPoint(one.getAdapter().getCurPoint(), other.getAdapter().getCurPoint()) <= battleDistance;
    }

    public static String keyOf(long one, long other) {
        if (one < other) {
            return one + ":" + other;
        }
        return other + ":" + one;
    }
}

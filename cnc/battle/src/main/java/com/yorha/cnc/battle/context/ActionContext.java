package com.yorha.cnc.battle.context;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.common.ActionType;
import com.yorha.cnc.battle.common.Amend;
import com.yorha.cnc.battle.common.AmendGroup;

import java.util.HashMap;
import java.util.Map;

/**
 * 战斗角色一次出手上下文
 * <p>
 * 普攻为一次出手，施放技能也是一次出手
 *
 * <AUTHOR>
 */
public class ActionContext {
    private final long attackerId;
    /**
     * 出手类型
     */
    private final ActionType actionType;
    /**
     * 对不同目标的不同修正，一次出手可能会溅射到多个目标
     * <p>
     * key:目标roleId
     */
    private final Map<Long, AmendGroup> amendGroupMap;

    public ActionContext(BattleRole attacker, ActionType actionType) {
        if (attacker.getMasterRole() != null) {
            this.attackerId = attacker.getMasterRole().getRoleId();
        } else {
            this.attackerId = attacker.getRoleId();
        }
        this.actionType = actionType;
        this.amendGroupMap = new HashMap<>();
    }

    public long getAttackerId() {
        return attackerId;
    }

    public Amend getDamageAmend(long targetRoleId) {
        return amendGroupMap.computeIfAbsent(targetRoleId, v -> new AmendGroup()).getDamageAmend();
    }

    public Amend getTreatmentAmend(long targetRoleId) {
        return amendGroupMap.computeIfAbsent(targetRoleId, v -> new AmendGroup()).getTreatmentAmend();
    }

    public Amend getShieldAmend(long targetRoleId) {
        return amendGroupMap.computeIfAbsent(targetRoleId, v -> new AmendGroup()).getShieldAmend();
    }

    public ActionType getType() {
        return actionType;
    }


    @Override
    public String toString() {
        return "ActionContext{" +
                "actionType=" + actionType +
                ", amendGroupMap=" + amendGroupMap +
                '}';
    }
}

package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 区域技能
 *
 * <AUTHOR>
 * @date 2023/4/14
 */
public class AreaSkillValue extends AbstractSkillEffectValue {

    public AreaSkillValue() {
        super(CommonEnum.SkillEffectType.SET_AREA_SKILL);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        Point curPoint = target.getAdapter().getCurPoint();
        BattleRole areaSkill = attacker.getGround().createAreaSkill(attacker, template.getValue1(), curPoint, null);
        if (areaSkill != null) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(getType());
            builder.setEffectId(template.getId());
            ret.add(builder.build());
        }
        return ret;
    }
}

package com.yorha.cnc.battle.skill.filter.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.filter.ISkillEffectRelationFilter;
import com.yorha.cnc.battle.skill.filter.SkillFilterHelper;

import java.util.Set;

/**
 * 非友方
 *
 * <AUTHOR>
 * @date 2023/4/14
 */
public class AllEnemyFilter implements ISkillEffectRelationFilter {
    @Override
    public Set<BattleRole> filter(SkillEffect effect, BattleRole castObj, Set<BattleRole> target, BattleRole castTargetObj) {
        // 可战斗筛选
        return SkillFilterHelper.filterByCanBattle(castObj, target);
    }
}

package com.yorha.cnc.battle.record.action;

import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class TroopAction {
    private long newMemberId;
    private SoldierNumChangeReason reason;
    private int soldierNum;

    public TroopAction() {
    }

    public TroopAction setNewMemberId(long newMemberId) {
        this.newMemberId = newMemberId;
        return this;
    }

    public TroopAction setReason(SoldierNumChangeReason reason) {
        this.reason = reason;
        return this;
    }

    public TroopAction setSoldierNum(int soldierNum) {
        this.soldierNum = soldierNum;
        return this;
    }

    public CommonBattle.BattleEvent convert2Event() {
        return CommonBattle.BattleEvent.newBuilder()
                .setEvent(CommonEnum.BattleEventEnum.BET_TROOP)
                .setTroopEvent(convert2TroopEvent())
                .build();
    }

    private CommonBattle.TroopEvent convert2TroopEvent() {
        CommonBattle.TroopEvent.Builder builder = CommonBattle.TroopEvent.newBuilder()
                .setRoleId(newMemberId)
                .setSoldierNum(soldierNum)
                .setChangeType(transSoldierReason2BattleLogType(reason));
        return builder.build();
    }

    public long getNewMemberId() {
        return newMemberId;
    }

    public SoldierNumChangeReason getReason() {
        return reason;
    }

    public int getSoldierNum() {
        return soldierNum;
    }

    public static CommonEnum.BattleLogTroopChangeType transSoldierReason2BattleLogType(SoldierNumChangeReason reason) {
        switch (reason) {
            case army_cut_in:
            case army_return:
                return CommonEnum.BattleLogTroopChangeType.BLTCT_JOIN_RALLY;
            case army_cut_out:
            case army_out:
                return CommonEnum.BattleLogTroopChangeType.BLTCT_QUIT_RALLY;
            case train_army:
            case update_army:
                return CommonEnum.BattleLogTroopChangeType.BLTCT_TRAIN;
            case serious_recovered:
                return CommonEnum.BattleLogTroopChangeType.BLTCT_HOSPITAL;
            default:
                return CommonEnum.BattleLogTroopChangeType.BLTCT_NONE;
        }
    }

    public int getNumToDisplay() {
        switch (reason) {
            case army_cut_out:
            case army_out:
                return -soldierNum;
            case army_cut_in:
            case army_return:
            case train_army:
            case update_army:
            case serious_recovered:
                return soldierNum;
            default:
                return 0;
        }
    }
}

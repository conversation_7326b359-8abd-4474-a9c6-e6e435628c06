package com.yorha.cnc.battle.event;

import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class TriggerSkillEvent extends BattleRoundEvent {
    private final CommonEnum.TriggerType triggerType;

    public TriggerSkillEvent(CommonEnum.TriggerType triggerType) {
        this.triggerType = triggerType;
    }

    public CommonEnum.TriggerType getTriggerType() {
        return triggerType;
    }

    @Override
    public boolean needActivateRole() {
        return true;
    }
}

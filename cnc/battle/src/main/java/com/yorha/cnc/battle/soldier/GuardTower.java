package com.yorha.cnc.battle.soldier;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.DefenseTowerTemplateService;
import res.template.DefenseTowerStatusTemplate;

/**
 * <AUTHOR>
 */
public class GuardTower extends BattleSoldier {
    private final SoldierUnit towerUnit;
    private final long maxHp;
    private final int lv;

    public GuardTower(BattleRole battleRole, SoldierUnit unit, long maxHp, int lv) {
        super(battleRole);
        this.towerUnit = unit;
        this.maxHp = maxHp;
        this.lv = lv;
    }

    public int getLv() {
        return lv;
    }

    public SoldierUnit getTowerUnit() {
        return towerUnit;
    }

    /**
     * 耐久度百分比
     */
    public int healthRatio() {
        return (int) Math.ceil((double) towerUnit.getData().aliveCount() / maxHp) * 100;
    }

    @Override
    public int aliveCount() {
        return towerUnit.getData().aliveCount();
    }

    @Override
    public int getId() {
        return towerUnit.getData().getSoldierId();
    }

    @Override
    public int getMaxCanTreat() {
        return 0;
    }

    @Override
    public int getMax() {
        return towerUnit.getData().getNum();
    }

    /**
     * 保护部队比例
     */
    public double getGuardRatio() {
        DefenseTowerTemplateService templateService = ResHolder.getResService(DefenseTowerTemplateService.class);
        DefenseTowerStatusTemplate statusTemplate = templateService.getDefenseTowerStatusTemplate(healthRatio());
        if (statusTemplate == null) {
            return 0;
        }
        return statusTemplate.getProtectRate();
    }

    public long getMaxHp() {
        return maxHp;
    }

    @Override
    public String toString() {
        return "GuardTower{" +
                "towerUnit=" + towerUnit +
                ", maxHp=" + maxHp +
                '}';
    }
}

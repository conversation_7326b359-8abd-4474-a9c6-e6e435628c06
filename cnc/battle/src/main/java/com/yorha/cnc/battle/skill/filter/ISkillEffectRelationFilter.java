package com.yorha.cnc.battle.skill.filter;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ISkillEffectRelationFilter {


    /**
     * 选择目标逻辑
     *
     * @param effect
     * @param castObj       释放者
     * @param target        上文中根据范围选中的目标
     * @param castTargetObj 施法目标 可能为null
     * @return 返回列表
     */
    Set<BattleRole> filter(SkillEffect effect, BattleRole castObj, Set<BattleRole> target, BattleRole castTargetObj);
}

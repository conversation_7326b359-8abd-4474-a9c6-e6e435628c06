package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.gemini.utils.StringUtils;


/**
 * 间隔X回合
 *
 * <AUTHOR>
 */
public class RoundIntervalCount<PERSON>hecker extends <PERSON><PERSON><PERSON>cker {
    /**
     * 是否已经触发过了
     * 用于处理首次触发逻辑
     */
    private boolean triggered;

    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        // 首次是否需要触发
        if (!triggered && StringUtils.equals(params[1], "1")) {
            count++;
            triggered = true;

            return true;
        }
        return super.check(role, effect, params);
    }

    @Override
    public void clear() {
        triggered = false;
        super.clear();
    }
}

package com.yorha.cnc.battle.snapshot.unit;

import com.yorha.cnc.battle.core.BattleAddCalc;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.soldier.Soldier;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class SoldierSnapshot extends AbstractBattleSoldierSnapshot {
    private final Map<Long, Integer> aliveCountByMember;

    public SoldierSnapshot(BattleRole role, Soldier soldier, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        super(role, soldier, roleAdditionSnapshot);
        this.aliveCountByMember = soldier.aliveCountByMember();
    }

    @Override
    public double calcAttack(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return BattleAddCalc.getSoldierAtk(getTemplate(), role, roleAdditionSnapshot);
    }

    @Override
    public double calcDefence(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return BattleAddCalc.getSoldierDef(getTemplate(), role, roleAdditionSnapshot);
    }

    @Override
    public double calcHp(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return BattleAddCalc.getSoldierHp(getTemplate(), role, roleAdditionSnapshot);
    }

    @Override
    public double calcBaseHp(BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return getTemplate().getHp();
    }

    public double calcTotalAttackByRoleId(BattleRole role, long roleId, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        int aliveCount = aliveCountByMember.getOrDefault(roleId, 0);
        return aliveCount * calcAttack(role, roleAdditionSnapshot);
    }

    @Override
    public String toString() {
        return getId() + "={aliveCountByMember=" + aliveCountByMember + '}';
    }
}

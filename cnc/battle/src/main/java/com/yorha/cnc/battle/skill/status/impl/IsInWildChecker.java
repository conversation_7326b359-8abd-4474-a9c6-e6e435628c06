package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.proto.CommonEnum;

/**
 * 在野外
 *
 * <AUTHOR>
 */
public class IsInWildChecker extends StatusC<PERSON>cker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        if (!role.isTroopObj()) {
            return false;
        }
        // 不在建筑中，不在采集中，就算野外
        return !(role.getAdapter().getArmyDetailState() == CommonEnum.ArmyDetailState.ADS_ASSIST
                || role.getAdapter().getArmyDetailState() == CommonEnum.ArmyDetailState.ADS_COLLECT);
    }
}

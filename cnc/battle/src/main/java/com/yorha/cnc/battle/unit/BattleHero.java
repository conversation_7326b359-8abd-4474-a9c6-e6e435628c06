package com.yorha.cnc.battle.unit;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.cnc.battle.adapter.interfaces.IBattleAdditionAdapter;
import com.yorha.cnc.battle.common.ActionType;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.Skill;
import com.yorha.cnc.battle.skill.SkillFacade;
import com.yorha.cnc.battle.skill.Talent;
import com.yorha.common.helper.HeroHelper;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.resource.resservice.constant.ConstBattleKVResService;
import com.yorha.game.gen.prop.HeroProp;
import com.yorha.game.gen.prop.SimpleSkillProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.SkillType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstBattleTemplate;
import res.template.SkillConfigTemplate;
import res.template.TalentTemplate;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Jiang
 */
public class BattleHero {
    private static final Logger LOGGER = LogManager.getLogger(BattleHero.class);

    private final BattleRole role;

    private final HeroProp heroProp;
    /**
     * 主动技能
     */
    private Skill activeSkill;
    /**
     * 被动技能
     */
    private final List<Skill> passiveSkillList = new ArrayList<>();

    /**
     * 天赋
     */
    private final List<Talent> talentList = Lists.newArrayList();

    /**
     * 怒气缓存
     */
    private double anger;
    /**
     * 副将可以释放技能的回合数, 0表示可以触发，初始值-1
     */
    private int deputyHeroTriggerRound;
    /**
     * 是否能释放技能
     * <p>
     * 会在怒气满的回合把这个值赋值成true，用于判断下个回合是否释放技能
     */
    private boolean canFireActiveSkill;
    /**
     * 增益
     */
    private final Map<CommonEnum.BuffEffectType, Long> additionMap = Maps.newHashMap();
    /**
     * 是否副将
     */
    private final boolean isDeputyHero;

    public BattleHero(HeroProp heroProp, BattleRole role, boolean isDeputyHero) {
        this.heroProp = heroProp;
        this.deputyHeroTriggerRound = -1;
        this.role = role;
        this.isDeputyHero = isDeputyHero;
        refresh(true);
    }

    public void refreshHeroByProp() {
        refresh(false);
    }

    private void refresh(boolean isInit) {
        activeSkill = null;
        passiveSkillList.clear();
        talentList.clear();
        buildSkill(heroProp);
        buildTalent(heroProp);
        buildAddition(isInit);
    }

    public void clear() {
        activeSkill = null;
        passiveSkillList.clear();
        talentList.clear();
        buildAddition(false);
    }

    private void buildSkill(HeroProp heroProp) {
        for ( SimpleSkillProp e : heroProp.getSkills()) {
            SkillDataTemplateService skillDataTemplateService = ResHolder.getResService(SkillDataTemplateService.class);
        SkillConfigTemplate skillTemplate = skillDataTemplateService.getSkillTemplate(e.getSkillGroupId(),e.getLevel());
            SkillType skillType = skillTemplate.getSkillType();
            if (skillType != null) {

                    switch (skillType) {
                        case ST_HERO_ACTIVE: {
                            activeSkill = SkillFacade.init(skillTemplate, getId(), role);
                            break;
                        }
                        case ST_HERO_PASSIVE: {
                            Skill passiveSkill = SkillFacade.init(skillTemplate, getId(), role);
                            passiveSkillList.add(passiveSkill);
                            break;
                        }
                        default: {
                            break;
                        }
                    }

            }
        }
//        // 觉醒技能替换已有技能
//        for (SkillTemplate skillTemplate : intensiveSkillReplaceData) {
//            int toReplaceSkillId = skillTemplate.getReplaceSkill();
//            if (activeSkill == null || activeSkill.getId() == toReplaceSkillId) {
//                activeSkill = SkillFacade.init(skillTemplate, getId(), role);
//            } else {
//                SkillFacade toReplaceSkill = IterableUtils.find(passiveSkillList, s -> s.getId() == toReplaceSkillId);
//                if (toReplaceSkill != null) {
//                    passiveSkillList.remove(toReplaceSkill);
//                }
//                passiveSkillList.add(SkillFacade.init(skillTemplate, getId(), role));
//            }
//        }
    }


    private void buildTalent(HeroProp heroProp) {
        if (isDeputyHero) {
            return;
        }
        for (Integer talentId : heroProp.getTalentIds()) {
            TalentTemplate talentTemplate = ResHolder.getInstance().findValueFromMap(TalentTemplate.class, talentId);
            Talent init = SkillFacade.init(talentTemplate, getId(), role);
            talentList.add(init);
        }
    }

    public int getLevel() {
        return heroProp.getLevel();
    }

    public int getId() {
        return heroProp.getHeroId();
    }

    @Nullable
    public Skill getActiveSkill() {
        return activeSkill;
    }

    public HeroProp getHeroProp() {
        return heroProp;
    }

    /**
     * 回合期间怒气缓存增加
     */
    public void increaseCacheAnger(double value, ActionType type) {
        LOGGER.debug("BattleLog anger cache increase, value={} cache={}, type={}", value, anger, type);
        anger += value;
        BattleGround.getBattleLog().printfRelationAnger(role, this, value, true, type);
    }

    /**
     * 回合期间怒气缓存减少
     */
    public void decreaseCacheAnger(float value, ActionType type) {
        LOGGER.debug("BattleLog anger cache decrease, value={} cache={}, type={}", value, anger, type);
        anger -= value;
        BattleGround.getBattleLog().printfRelationAnger(role, this, value, false, type);
    }

    public void clearCacheAnger() {
        LOGGER.debug("BattleLog anger cache reset");
        anger = 0;
    }

    /**
     * 怒气缓存结算
     */
    public void settleAnger() {
        if (anger == 0) {
            return;
        }
        if (activeSkill != null) {
            int finAnger = 0;
            if (anger > 0) {
                ConstBattleTemplate constBattleTemplate = ResHolder.getResService(ConstBattleKVResService.class).getTemplate();
                finAnger = heroProp.getAnger() + (int) Math.min(constBattleTemplate.getRageRecoverLimit(), anger);
            }
            if (anger < 0) {
                finAnger = (int) Math.max(0, heroProp.getAnger() + anger);
            }
            heroProp.setAnger(finAnger);
        }
        clearCacheAnger();
        BattleGround.getBattleLog().printfRelationFinalAnger(role, this);
    }

    /**
     * 技能释放怒气扣减
     */
    public void decreaseAngerAfterSkillFire() {
        if (activeSkill != null) {
            // 释放技能时直接把身上所有怒气扣掉（因为可能一直移动一直在攒怒气），本回合获得的怒气在缓存中，还没实际加到英雄身上
            int decreaseAnger = getHeroProp().getAnger();
            decreaseCacheAnger(decreaseAnger, ActionType.SKILL);
            LOGGER.debug("BattleLog decrease anger, hero id={},value={},total={}", this.getHeroProp().getHeroId(), decreaseAnger, heroProp.getAnger());
        }
    }

    /**
     * 判断怒气足够
     */
    public boolean isAngerEnough() {
        if (activeSkill != null) {
            SkillConfigTemplate template = activeSkill.getSkillTemplate();
            return heroProp.getAnger() >= template.getCost();
        } else {
            return false;
        }
    }

    public int getAnger() {
        return heroProp.getAnger();
    }

    public void clearSkill() {
        canFireActiveSkill = false;
        resetDeputyHeroTriggerRound();
        heroProp.setAnger(0);
        if (activeSkill != null) {
            activeSkill.clear();
        }
        for (Skill s : passiveSkillList) {
            s.clear();
        }
        for (Talent s : talentList) {
            s.clear();
        }
    }

    public void resetDeputyHeroTriggerRound() {
        deputyHeroTriggerRound = -1;
    }

    /**
     * 副将释放技能机制--主将释放技能后第三个回合释放副将技能
     * （沉默效果会阻塞释放直到沉默结束然后立即释放）
     */
    public boolean canTriggerDeputyHeroSkill() {
        return deputyHeroTriggerRound == 0;
    }

    /**
     * 每回合结算一次
     */
    public void executeDeputyHeroSkillTrigger() {
        if (canTriggerDeputyHeroSkill()) {
            return;
        }
        deputyHeroTriggerRound = Math.max(-1, deputyHeroTriggerRound - 1);
    }

     /**
     * 主将释放技能后
     */
    public void startDeputyHeroTrigger() {
        deputyHeroTriggerRound = ResHolder.getResService(ConstBattleKVResService.class).getTemplate().getTriggerRound();
    }

    public boolean canFireActiveSkill() {
        return canFireActiveSkill;
    }

    public void setCanFireActiveSkill(boolean canFireActiveSkill) {
        if (canFireActiveSkill != this.canFireActiveSkill) {
            LOGGER.debug("BattleLog hero id={} canFireActiveSkill: {} -> {}", getId(), this.canFireActiveSkill, canFireActiveSkill);
            this.canFireActiveSkill = canFireActiveSkill;
        }
    }

    private void buildAddition(boolean isInit) {
        additionMap.replaceAll((k, v) -> 0L);

        // 被动技能
        for (Skill skill : passiveSkillList) {
            for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : HeroHelper.getEffectAddition(skill.getEffectIdList()).entrySet()) {
                additionMap.put(entry.getKey(), additionMap.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
        }

        // 天赋
        for (Talent talent : talentList) {
            for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : HeroHelper.getEffectAddition(talent.getEffectIdList()).entrySet()) {
                additionMap.put(entry.getKey(), additionMap.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
        }

        if (!isInit) {
            IBattleAdditionAdapter additionHandler = role.getAdapter().getAdditionAdapter();
            if (additionHandler != null) {
                for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : additionMap.entrySet()) {
                    additionHandler.dispatchAdditionChange(entry.getKey());
                }
            }
        }
    }

    public Map<CommonEnum.BuffEffectType, Long> getAllAddition() {
        return additionMap;
    }

    public boolean canTriggerMainHeroSkill() {
        return isAngerEnough();
    }

    public List<Skill> getPassiveSkillList() {
        return passiveSkillList;
    }

    public List<Talent> getTalentList() {
        return talentList;
    }

    public double getCacheAnger() {
        return anger;
    }
}

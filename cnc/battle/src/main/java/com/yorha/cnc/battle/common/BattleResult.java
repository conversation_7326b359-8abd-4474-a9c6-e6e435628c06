package com.yorha.cnc.battle.common;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.record.BattleRecord;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
public class BattleResult {
    private static final Logger LOGGER = LogManager.getLogger(BattleResult.class);

    public final CommonEnum.BattleOverType type;
    public final boolean alive;
    public final boolean anyEnemyAlive;
    public final List<BattleRecord.RecordOne> allRecords;

    public BattleResult(Builder builder) {
        this.type = builder.type;
        this.alive = builder.alive;
        this.anyEnemyAlive = builder.anyEnemyAlive;
        this.allRecords = builder.allRecords;
    }

    /**
     * 所有player的战损
     */
    public Map<Long, Long> getAllPlayerLossPower(long roleId) {
        Map<Long, Long> lossPowerByPlayerId = Maps.newHashMap();
        List<Pair<Long, Long>> relations = new ArrayList<>(allRecords.size());
        for (BattleRecord.RecordOne recordOne : allRecords) {
            relations.add(Pair.of(recordOne.getRelationId(), recordOne.getBattleId()));
            BattleRecord.RoleRecord roleRecord = recordOne.getRecordByRoleId(roleId);
            BattleRecord.RoleRecord otherRoleRecord = recordOne.getRecordByOtherRoleId(roleId);
            if (isPvpBattle(roleRecord.getRoleType(), otherRoleRecord.getRoleType())) {
                for (Map.Entry<Long, Long> entry : roleRecord.getMemberPowerLossByPlayerId().entrySet()) {
                    lossPowerByPlayerId.put(entry.getKey(), lossPowerByPlayerId.getOrDefault(entry.getKey(), 0L) + entry.getValue());
                }
            }
        }
        if (lossPowerByPlayerId.size() > 0){
            LOGGER.info("BattleResult getAllPlayerLossPower, roleId={} result={} relationList={}", roleId, lossPowerByPlayerId, relations);
        }
        return lossPowerByPlayerId;
    }

    private static boolean isPvpBattle(CommonEnum.SceneObjType oneType, CommonEnum.SceneObjType otherType) {
        return !isEnvironmentType(oneType) && !isEnvironmentType(otherType);
    }

    private static boolean isEnvironmentType(CommonEnum.SceneObjType type) {
        return type == CommonEnum.SceneObjType.SOT_MONSTER || type == CommonEnum.SceneObjType.SOT_STRONG_POINT_ARMY;
    }

    public static class Builder {
        private CommonEnum.BattleOverType type;
        private boolean alive;
        private boolean anyEnemyAlive;
        private List<BattleRecord.RecordOne> allRecords;

        public Builder setType(CommonEnum.BattleOverType type) {
            this.type = type;
            return this;
        }

        public Builder setAlive(boolean alive) {
            this.alive = alive;
            return this;
        }

        public Builder setAnyEnemyAlive(boolean anyEnemyAlive) {
            this.anyEnemyAlive = anyEnemyAlive;
            return this;
        }

        public Builder setAllRecords(List<BattleRecord.RecordOne> allRecords) {
            this.allRecords = allRecords;
            return this;
        }

        public BattleResult build() {
            return new BattleResult(this);
        }
    }
}

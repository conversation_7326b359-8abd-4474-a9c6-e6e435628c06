package com.yorha.cnc.battle.event;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;

import java.util.Set;

/**
 * 放技能
 *
 * <AUTHOR>
 */
public class FireSkillEvent extends BattleRoundEvent {
    private final int heroId;
    private final int skillId;
    private final long targetId;
    private Set<Long> canNullTargetList;
    private boolean shieldNtf;

    private FireSkillEvent(Builder builder) {
        this.heroId = builder.heroId;
        this.skillId = builder.skillId;
        this.targetId = builder.targetId;
        this.canNullTargetList = builder.targetList;
        this.shieldNtf = builder.shieldNtf;
    }

    public static Builder newBuilder(){
        return new Builder();
    }

    @Override
    public boolean needActivateRole() {
        return true;
    }

    public int getHeroId() {
        return heroId;
    }

    public int getSkillId() {
        return skillId;
    }

    public long getTargetId() {
        return targetId;
    }

    public boolean isShieldNtf() {
        return shieldNtf;
    }

    public Set<Long> getCanNullTargetList() {
        return canNullTargetList;
    }

    public static class Builder {
        private int heroId;
        private int skillId;
        private long targetId;
        private Set<Long> targetList;
        private boolean shieldNtf;

        public Builder setHeroId(int heroId) {
            this.heroId = heroId;
            return this;
        }

        public Builder setSkillId(int skillId) {
            this.skillId = skillId;
            return this;
        }

        public Builder setTargetId(long targetId) {
            this.targetId = targetId;
            return this;
        }

        public Builder setSelectTargets(Set<Long> targets){
            if (targets == null){
                throw new GeminiException(ErrorCode.FAILED, "forbid null");
            }
            this.targetList = targets;
            return this;
        }

        public Builder setShieldNtf(boolean shieldNtf) {
            this.shieldNtf = shieldNtf;
            return this;
        }

        public FireSkillEvent build() {
            return new FireSkillEvent(this);
        }
    }
}

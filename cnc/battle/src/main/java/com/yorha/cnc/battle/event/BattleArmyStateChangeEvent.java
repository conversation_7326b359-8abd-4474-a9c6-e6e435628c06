package com.yorha.cnc.battle.event;

import com.yorha.proto.CommonEnum;

/**
 * 行军状态变更
 *
 * <AUTHOR>
 */
public class BattleArmyStateChangeEvent extends BattleRoundEvent {
    private CommonEnum.ArmyDetailState oldState;
    private CommonEnum.ArmyDetailState newState;

    public CommonEnum.ArmyDetailState getOldState() {
        return oldState;
    }

    public CommonEnum.ArmyDetailState getNewState() {
        return newState;
    }

    public BattleArmyStateChangeEvent(CommonEnum.ArmyDetailState oldState, CommonEnum.ArmyDetailState newState) {
        this.oldState = oldState;
        this.newState = newState;
    }

    @Override
    public boolean needActivateRole() {
        return true;
    }

}

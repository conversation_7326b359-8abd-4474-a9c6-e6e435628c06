package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.common.utils.shape.Point;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 检测-联盟领土外
 *
 * <AUTHOR>
 */
public class OutsideUnionTerritory extends StatusChecker {
    private static final Logger LOGGER = LogManager.getLogger(OutsideUnionTerritory.class);

    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        long clanId = role.getAdapter().getClanId();
        Point curPoint = role.getAdapter().getCurPoint();
        if (curPoint == null) {
            LOGGER.error("BattleErrLog move point is null, roleId:{}", role);
            return false;
        }
        if (clanId != 0 && role.getGround().isInBigScene() && role.getGround().getAdapter().getBuildingOwnerClanId(curPoint) == clanId) {
            return false;
        }
        return true;
    }
}

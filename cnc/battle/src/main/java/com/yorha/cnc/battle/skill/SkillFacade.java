package com.yorha.cnc.battle.skill;

import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.proto.CommonEnum;
import res.template.SkillConfigTemplate;
import res.template.TalentTemplate;

import java.util.List;

/**
 * 技能，or天赋，其实是一样的东西
 *
 * <AUTHOR>
 */
public abstract class SkillFacade extends SkillEffectContainer {
    protected int id;
    protected final int heroId;
    protected final BattleRole role;

    protected SkillFacade(int id, int heroId, BattleRole role) {
        this.id = id;
        this.heroId = heroId;
        this.role = role;
        initEffects();
    }

    @Override
    public void initEffects() {
        for (Integer effectId : getEffectIdList()) {
            EffectContext effectContext = EffectContext.newBuilder()
                    .setCastRole(role)
                    .setSkillId(id)
                    .setType(getBattleLogSkillType())
                    .setHeroId(heroId)
                    .setEffectId(effectId)
                    .setLeaderRoleId(role.getAdapter().getLeaderRoleId())
                    .setAttachBuffId(0)
                    .setDotContext(null)
                    .build(true);
            addEffect(effectId, effectContext);
        }
    }

    public static Skill init(SkillConfigTemplate template, int heroId, BattleRole role) {
        return new Skill(template.getId(), heroId, role);
    }

    public static Talent init(TalentTemplate template, int heroId, BattleRole role) {
        return new Talent(template.getId(), heroId, role);
    }

    public int getId() {
        return id;
    }

    public abstract List<Integer> getEffectIdList();

    protected abstract CommonEnum.BattleLogSkillType getBattleLogSkillType();
}

package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.handler.SummoningHandler;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 召唤召唤物
 *
 * <AUTHOR>
 */
public class SummoningValue extends AbstractSkillEffectValue {
    private static final Logger LOGGER = LogManager.getLogger(SummoningValue.class);


    public SummoningValue() {
        super(CommonEnum.SkillEffectType.SET_SUMMONING);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        Point curPoint = target.getAdapter().getCurPoint();

        int summonsTemplateId = template.getValue1();
        int summonsTemplateId2 = template.getValue2();
        int summonsTemplateId3 = template.getValue3();
        List<BattleRole> summonsList = new ArrayList<>();
        if (summonsTemplateId > 0 && attacker.getSummoningHandler().isCanSummoning()) {
            BattleRole summons = attacker.getSummoningHandler().createSummons(SummoningHandler.SummonsType.MONSTER, summonsTemplateId, curPoint);
            summonsList.add(summons);
        }
        if (summonsTemplateId2 > 0 && attacker.getSummoningHandler().isCanSummoning()) {
            BattleRole summons = attacker.getSummoningHandler().createSummons(SummoningHandler.SummonsType.MONSTER, summonsTemplateId2, curPoint);
            summonsList.add(summons);
        }
        if (summonsTemplateId3 > 0 && attacker.getSummoningHandler().isCanSummoning()) {
            BattleRole summons = attacker.getSummoningHandler().createSummons(SummoningHandler.SummonsType.MONSTER, summonsTemplateId3, curPoint);
            summonsList.add(summons);
        }
        LOGGER.info("SummoningValue handle success, param1={} param2={} param3={} result={} summonSizeCheck={}", summonsTemplateId, summonsTemplateId2, summonsTemplateId3, summonsList, attacker.getSummoningHandler().isCanSummoning());
        if (summonsList.size() > 0) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(getType());
            builder.setEffectId(template.getId());
            ret.add(builder.build());
        }
        return ret;
    }
}

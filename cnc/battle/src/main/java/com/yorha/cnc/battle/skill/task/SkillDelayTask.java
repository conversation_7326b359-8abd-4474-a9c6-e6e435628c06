package com.yorha.cnc.battle.skill.task;

import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.Skill;
import com.yorha.cnc.battle.skill.SkillSystem;

/**
 * 吟唱技能
 *
 * <AUTHOR>
 */
public class SkillDelayTask extends DelayTask {
    private final Skill skill;
    private final int heroId;
    private final BattleRole attacker;
    private final BattleRole target;
    private final EffectContext effectContext;

    public SkillDelayTask(BattleTickContext tickContext,
                          long delayMillis,
                          Skill skill,
                          int heroId,
                          BattleRole attacker,
                          BattleRole target,
                          EffectContext effectContext) {
        super(tickContext, delayMillis);
        this.skill = skill;
        this.heroId = heroId;
        this.attacker = attacker;
        this.target = target;
        this.effectContext = effectContext;
    }

    @Override
    public void run(BattleTickContext tickContext) {
        SkillSystem.fire(attacker, target, heroId, skill, true, effectContext, null);
    }
}

package com.yorha.cnc.battle.skill.effect;

import com.google.common.collect.Maps;
import com.yorha.cnc.battle.skill.effect.impl.*;
import com.yorha.proto.CommonEnum.SkillEffectType;
import res.template.SkillEffectTemplate;

import javax.annotation.Nullable;
import java.util.Map;

/**
 * 技能效果值工厂
 *
 * <AUTHOR>
 */
public class SkillEffectValueFactory {

    private static final Map<SkillEffectType, ISkillEffectValue> SKILL_EFFECT_VALUE_MAP = Maps.newEnumMap(SkillEffectType.class);

    static {
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_DAMAGE, new DamageValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_TREATMENT, new TreatmentValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_INCR_ANGER, new IncAngerValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_DECR_ANGER, new DecAngerValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_DISPERSE, new DisperseValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_ADD_BUFF, new BuffValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_MULTIPLE_EFFECT, new MultipleEffectValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_FIRE_SKILL, new FireSkillValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_AREA_SKILL, new AreaSkillValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_PERCENTAGE_TREATMENT, new PercentageTreatmentValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_REDUCE_DURABILITY, new ReduceDurability());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_SUMMONING, new SummoningValue());
        SKILL_EFFECT_VALUE_MAP.put(SkillEffectType.SET_ADD_BATCH_BUFF, new BatchLayerBuffValue());
    }

    @Nullable
    public static ISkillEffectValue of(SkillEffectTemplate template) {
        return SKILL_EFFECT_VALUE_MAP.get(template.getType());
    }

}

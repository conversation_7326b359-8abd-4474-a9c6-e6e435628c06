package com.yorha.cnc.battle.common;

/**
 * 一组修正，包括 伤害/治疗/护盾值
 *
 * <AUTHOR>
 */
public class AmendGroup {
    private final Amend damageAmend;
    private final Amend treatmentAmend;
    private final Amend shieldAmend;

    public AmendGroup() {
        this.damageAmend = new Amend();
        this.treatmentAmend = new Amend();
        this.shieldAmend = new Amend();
    }

    public Amend getDamageAmend() {
        return damageAmend;
    }

    public Amend getTreatmentAmend() {
        return treatmentAmend;
    }

    public Amend getShieldAmend() {
        return shieldAmend;
    }
}

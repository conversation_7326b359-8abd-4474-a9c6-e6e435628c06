package com.yorha.cnc.battle.buf.lifecycle.impl;


import com.yorha.cnc.battle.buf.lifecycle.ILifeCycle;
import com.yorha.proto.CommonEnum.LifeCycleType;

import java.text.MessageFormat;

/**
 * 攻击次数
 *
 * <AUTHOR>
 */
public class AttackTimesLifeCycle implements ILifeCycle {
    private int times;
    private int max;
    private LifeCycleType type;

    public AttackTimesLifeCycle(LifeCycleType type, long times) {
        this(type, (int) times);
    }

    public AttackTimesLifeCycle(LifeCycleType type, int times) {
        this.times = times;
        this.type = type;
        this.max = times;
    }

    public int getTimes() {
        return times;
    }

    @Override
    public LifeCycleType getType() {
        return type;
    }

    @Override
    public long getValue() {
        return times;
    }

    @Override
    public boolean checkValid() {
        return times > 0;
    }

    @Override
    public void execute() {
        times--;
    }


    @Override
    public void reset() {
        this.times = this.max;
    }


    @Override
    public String getMessage() {
        if (times > 0) {
            if (max == times) {
                return MessageFormat.format("本回合开始生效,共{0}次攻击次数", this.times);
            } else {
                return MessageFormat.format("剩余{0}次攻击次数", this.times);
            }
        } else {
            return "下回合失效";
        }
    }

}

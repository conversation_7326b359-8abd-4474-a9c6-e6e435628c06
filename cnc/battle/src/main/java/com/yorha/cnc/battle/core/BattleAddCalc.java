package com.yorha.cnc.battle.core;

import com.yorha.common.constant.AdditionConstants;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MathUtils;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.SoldierBuffTemplate;
import res.template.SoldierTypeTemplate;

import java.util.Map;

/**
 * 加成公式计算器
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public class BattleAddCalc {
    private static final Logger LOGGER = LogManager.getLogger(BattleAddCalc.class);

    /**
     * 兵种攻击
     */
    public static double getSoldierAtk(SoldierTypeTemplate template, BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        CommonEnum.SoldierType soldierType = CommonEnum.SoldierType.forNumber(template.getSoldierType());
        if (soldierType == null) {
            LOGGER.error("BattleErrLog role={}, getSoldierAtk failed, soldierType is null.type={}", role, template.getSoldierType());
            return 0;
        }
        long upFixed = getFinalBuffValueWithSnapshot(AdditionConstants.SOLDIER_TYPE_INC_FIXED_MAP.get(soldierType).getLeft(), roleAdditionSnapshot);
        long allUpPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_INC_ATTACK_BUF, roleAdditionSnapshot);
        long upPercent = 0;
        SoldierBuffTemplate soldierBuffTemplate = ResHolder.getInstance().getMap(SoldierBuffTemplate.class).get(soldierType.getNumber());
        if (soldierBuffTemplate != null) {
            CommonEnum.BuffEffectType atkType = CommonEnum.BuffEffectType.forNumber(soldierBuffTemplate.getAtkUp());
            if (atkType != null) {
                upPercent = getFinalBuffValueWithSnapshot(atkType, roleAdditionSnapshot);
            } else {
                LOGGER.warn("BattleLog role={}, getSoldierAtk upPercent failed, SoldierBuffTemplate not found, type={}", role, soldierType);
            }
        }
        long rallyPercent = role.getAdapter().isRally() ? getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_RALLY_ATTACK_PERCENT, roleAdditionSnapshot) : 0;
        long inClanTerritoryPercent = 0;
        if (role.getAdapter().isInClanTerritory()) {
            inClanTerritoryPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_ATK_INC_IN_CLAN_TERRITORY_PERCENT, roleAdditionSnapshot);
        }

        long garrisonPercent = 0L;
        // 判断是否是驻防军队
        if (role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_OTHER ||
                role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF ||
                role.getType() == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY) {
            garrisonPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_ATK_INC_WHEN_GARRISON_PERCENT, roleAdditionSnapshot);
        }

        // 单兵种攻击力 = （单兵种配表攻击力 + ∑单兵种固定攻击力buff） * （ 1 + ∑单兵种百分比攻击力buff + ∑全兵种百分比攻击力buff）
        return (template.getAtk() + upFixed) * Math.max((1 + (upPercent + allUpPercent + rallyPercent + inClanTerritoryPercent + garrisonPercent) / MathUtils.TEN_THOUSAND), 0);
    }

    /**
     * 兵种防御
     * 最小值 = 1
     */
    public static double getSoldierDef(SoldierTypeTemplate template, BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        CommonEnum.SoldierType soldierType = CommonEnum.SoldierType.forNumber(template.getSoldierType());
        if (soldierType == null) {
            LOGGER.error("BattleErrLog role={}, getSoldierDef failed, soldierType is null.type={}", role, template.getSoldierType());
            return 0;
        }
        long upFixed = getFinalBuffValueWithSnapshot(AdditionConstants.SOLDIER_TYPE_INC_FIXED_MAP.get(soldierType).getMiddle(), roleAdditionSnapshot);
        long allUpPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_INC_DEFENCE_BUF, roleAdditionSnapshot);
        long upPercent = 0;
        SoldierBuffTemplate soldierBuffTemplate = ResHolder.getInstance().getMap(SoldierBuffTemplate.class).get(template.getSoldierType());
        if (soldierBuffTemplate != null) {
            CommonEnum.BuffEffectType atkType = CommonEnum.BuffEffectType.forNumber(soldierBuffTemplate.getDefUp());
            if (atkType != null) {
                upPercent = getFinalBuffValueWithSnapshot(atkType, roleAdditionSnapshot);
            } else {
                LOGGER.warn("BattleLog role={}, getSoldierDef upPercent failed, SoldierBuffTemplate not found, type={}", role, soldierType);
            }
        }
        long garrisonPercent = 0L;
        // 判断是否是驻防军队
        if (role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_OTHER ||
                role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF ||
                role.getType() == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY) {
            garrisonPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_DEF_INC_WHEN_GARRISON_PERCENT, roleAdditionSnapshot);
        }
        // 单兵种防御力 = （单兵种配表防御力 + ∑单兵种固定防御力buff） * （ 1 + ∑单兵种百分比防御力buff + ∑全兵种百分比防御力buff）
        return Math.max((template.getDef() + upFixed) * (1 + (upPercent + allUpPercent + garrisonPercent) / MathUtils.TEN_THOUSAND), 1);
    }

    /**
     * 兵种生命
     * 最小值 = 1
     */
    public static double getSoldierHp(SoldierTypeTemplate template, BattleRole role, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        CommonEnum.SoldierType soldierType = CommonEnum.SoldierType.forNumber(template.getSoldierType());
        if (soldierType == null) {
            LOGGER.error("BattleErrLog role={}, getSoldierHp failed, soldierType is null.type={}", role, template.getSoldierType());
            return 0;
        }
        long upFixed = getFinalBuffValueWithSnapshot(AdditionConstants.SOLDIER_TYPE_INC_FIXED_MAP.get(soldierType).getRight(), roleAdditionSnapshot);
        long allUpPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_INC_HP_BUF, roleAdditionSnapshot);
        long upPercent = 0;
        SoldierBuffTemplate soldierBuffTemplate = ResHolder.getInstance().getMap(SoldierBuffTemplate.class).get(template.getSoldierType());
        if (soldierBuffTemplate != null) {
            CommonEnum.BuffEffectType atkType = CommonEnum.BuffEffectType.forNumber(soldierBuffTemplate.getHpUp());
            if (atkType != null) {
                upPercent = getFinalBuffValueWithSnapshot(atkType, roleAdditionSnapshot);
            } else {
                LOGGER.warn("BattleLog role={}, getSoldierHp upPercent failed, SoldierBuffTemplate not found, type={}", role, soldierType);
            }
        }
        long garrisonPercent = 0L;
        // 判断是否是驻防军队
        if (role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_OTHER ||
                role.getType() == CommonEnum.SceneObjType.SOT_CITY_ARMY_SELF ||
                role.getType() == CommonEnum.SceneObjType.SOT_CLAN_BUILDING_ARMY) {
            garrisonPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_HP_INC_WHEN_GARRISON_PERCENT, roleAdditionSnapshot);
        }
        // 单兵种生命值 = （单兵种配表生命值 + ∑单兵种固定生命值buff） * （ 1 + ∑单兵种百分比生命值buff + ∑全兵种百分比生命值buff）
        return Math.max((template.getHp() + upFixed) * (1 + (upPercent + allUpPercent + garrisonPercent) / MathUtils.TEN_THOUSAND), 1);
    }

    /**
     * 防御塔攻击力
     */
    public static double getGuardTowerAtk(BattleRole role, SoldierTypeTemplate template, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        long upPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_GUARD_TOWER_ATK_INC_BUFF, roleAdditionSnapshot);
        return template.getAtk() * (1 + upPercent / MathUtils.TEN_THOUSAND);
    }

    /**
     * 防御塔防御力
     */
    public static double getGuardTowerDef(BattleRole role, SoldierTypeTemplate template, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        long upPercent = getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType.ET_GUARD_TOWER_DEF_INC_BUFF, roleAdditionSnapshot);
        return Math.max(1, template.getDef() * (1 + upPercent / MathUtils.TEN_THOUSAND));
    }

    public static long getFinalBuffValueWithSnapshot(CommonEnum.BuffEffectType buff, Map<CommonEnum.BuffEffectType, Long> roleAdditionSnapshot) {
        return roleAdditionSnapshot.getOrDefault(buff, 0L);
    }
}

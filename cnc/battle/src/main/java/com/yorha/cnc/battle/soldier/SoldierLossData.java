package com.yorha.cnc.battle.soldier;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 士兵损失数据
 *
 * <AUTHOR>
 */
public class SoldierLossData {

    private int slight;
    private int severe;
    private int dead;
    /**
     * 治疗量仅标识有多少被治疗的，计算总loss不需要减掉这里的数值
     */
    private int treatment;


    public SoldierLossData() {
    }

    public int getSlight() {
        return slight;
    }

    public SoldierLossData setSlight(int slight) {
        this.slight = slight;
        return this;
    }

    public int getSevere() {
        return severe;
    }

    public SoldierLossData setSevere(int severe) {
        this.severe = severe;
        return this;
    }

    public int getDead() {
        return dead;
    }

    public SoldierLossData setDead(int dead) {
        this.dead = dead;
        return this;
    }

    public SoldierLossData increaseDead(int value) {
        dead += value;
        return this;
    }

    public int totalLoss() {
        return slight + severe + dead;
    }

    public int getTreatment() {
        return treatment;
    }

    public SoldierLossData setTreatment(int treatment) {
        this.treatment = treatment;
        return this;
    }

    public void clear() {
        this.slight = 0;
        this.severe = 0;
        this.dead = 0;
        this.treatment = 0;
    }

    public void merge(SoldierLossData another) {
        slight += another.slight;
        severe += another.severe;
        dead += another.dead;
        treatment += another.treatment;
    }

    public int totalCount() {
        return slight + severe + dead + treatment;
    }

    public int sum() {
        return treatment - slight - severe - dead;
    }

    public int totalKill() {
        return severe + dead;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

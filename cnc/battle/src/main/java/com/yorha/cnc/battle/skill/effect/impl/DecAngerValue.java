package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.proto.CommonEnum.SkillEffectType;
import com.yorha.proto.PlayerScene;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 降低怒气
 *
 * <AUTHOR>
 */
public class DecAngerValue extends AbstractSkillEffectValue {


    public DecAngerValue() {
        super(SkillEffectType.SET_DECR_ANGER);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        if (target.getMainHero() == null || target.getMainHero().getId() == 0) {
            return ret;
        }
        int decValues = template.getValue1();
        target.getMainHero().decreaseCacheAnger(decValues, actionCtx.getType());
        if (decValues > 0) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(this.getType());
            builder.setValue(decValues);
            builder.setEffectId(template.getId());
            ret.add(builder.build());
        }

        return ret;
    }
}
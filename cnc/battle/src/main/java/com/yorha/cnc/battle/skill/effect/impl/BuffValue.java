package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import res.template.BattleBuffTemplate;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 加buff
 *
 * <AUTHOR>
 */
public class BuffValue extends AbstractSkillEffectValue {

    public BuffValue() {
        super(CommonEnum.SkillEffectType.SET_ADD_BUFF);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        List<BattleBuffTemplate> buffList = ResHolder.getResService(SkillDataTemplateService.class).getRandomBuffTemplateList(template);
        for (BattleBuffTemplate buffTemplate : buffList) {
            Integer buffId = target.getBuffHandler().addBuff(attacker, buffTemplate.getId(), 1, effectContext);
            if (buffId != null) {
                PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
                builder.setTargetId(target.getRoleId());
                builder.setType(getType());
                builder.setValue(buffId);
                builder.setEffectId(template.getId());
                ret.add(builder.build());
            }
        }
        return ret;
    }
}

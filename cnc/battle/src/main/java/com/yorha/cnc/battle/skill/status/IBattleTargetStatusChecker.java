package com.yorha.cnc.battle.skill.status;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;

import java.util.Objects;

/**
 * 对指定目标的状态校验
 *
 * <AUTHOR>
 */
public interface IBattleTargetStatusChecker {


    /**
     * 验证是否在某种状态下
     *
     * @return true 通过
     */
    boolean check(BattleRole role, SkillEffect effect, String[] params);

    void mark();

    void clear();

    static boolean numCompare(int num1, int num2, String operation) {
        if (Objects.equals(operation, "=")) {
            return num1 == num2;
        } else if (Objects.equals(operation, ">")) {
            return num1 > num2;
        } else if (Objects.equals(operation, "<")) {
            return num1 < num2;
        } else if (Objects.equals(operation, ">=")) {
            return num1 >= num2;
        } else if (Objects.equals(operation, "<=")) {
            return num1 <= num2;
        }
        return false;
    }
}

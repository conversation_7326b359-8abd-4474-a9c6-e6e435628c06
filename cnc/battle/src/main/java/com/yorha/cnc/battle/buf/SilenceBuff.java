package com.yorha.cnc.battle.buf;

import com.yorha.common.constant.BattleConstants;
import com.yorha.cnc.battle.core.BattleRole;


/**
 * 沉默buff
 *
 * <AUTHOR>
 */
public class SilenceBuff extends StateBuff {
    public SilenceBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);
    }

    @Override
    public BattleConstants.BattleRoleState getState() {
        return BattleConstants.BattleRoleState.SILENCE;
    }
}

package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import org.apache.logging.log4j.LogManager;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.yorha.cnc.battle.skill.SkillHelper.isBattleSceneBuild;

/**
 * 扣减建筑耐久度
 */
public class ReduceDurability extends AbstractSkillEffectValue {
    private static final org.apache.logging.log4j.Logger LOGGER = LogManager.getLogger(FireSkillValue.class);

    public ReduceDurability() {
        super(CommonEnum.SkillEffectType.SET_REDUCE_DURABILITY);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> result = new ArrayList<>();
        int decNum = target.getAdapter().reduceDurability(template.getValue1());
        if (decNum > 0) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId())
                    .setType(getType())
                    .setValue(template.getId())
                    .setEffectId(template.getId())
                    .setValue(decNum);
            result.add(builder.build());
        }
        if (result.isEmpty() && !isBattleSceneBuild(target.getAdapter().getEntityType())) {
            LOGGER.error("BattleErrLog ReduceDurability skill exec fail, entityType={}, objType={}, templateId={}", target.getAdapter().getEntityType(), target.getType(), template.getId());
        }
        return result;
    }
}
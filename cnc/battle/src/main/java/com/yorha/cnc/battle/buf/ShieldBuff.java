package com.yorha.cnc.battle.buf;

import com.yorha.cnc.battle.core.BattleFormula;
import com.yorha.cnc.battle.core.BattleRole;

import res.template.BattleBuffTemplate;

/**
 * 护盾buff
 *
 * <AUTHOR>
 */
public class ShieldBuff extends Buff {
    public ShieldBuff(BattleRole owner, PendingBuff builder) {
        super(owner, builder);

        // 初始化护盾值
        getData().getSpecData().getShieldData().setShieldValue(getInitValue(owner, getTemplate()));
    }

    @Override
    public long getValue() {
        return getData().getSpecData().getShieldData().getShieldValue();
    }

    @Override
    public boolean isValid() {
        if (getValue() <= 0) {
            return false;
        }
        return super.isValid();
    }

    @Override
    public void execute(BattleRole owner) {

    }

    @Override
    public void destroy(BattleRole owner) {

    }

    @Override
    public void add(BattleRole owner) {

    }

    public static long getInitValue(BattleRole executor, BattleBuffTemplate template) {
        // 这里无脑补快照刷新防无快照，内部根据回合数拦截
        executor.refreshNewRoundSnapShot();
        return (long) BattleFormula.stdCalcShieldValue(executor.getSnapShot("ShieldBuff"), template.getValue());
    }
}

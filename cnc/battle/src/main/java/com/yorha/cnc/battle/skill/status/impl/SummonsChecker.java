package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;

/**
 * 召唤物数量
 *
 * <AUTHOR>
 */
public class <PERSON>mmons<PERSON><PERSON><PERSON> extends StatusChecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        int summonId = Integer.parseInt(params[0]);
        int rule = Integer.parseInt(params[1]);
        int configNum = Integer.parseInt(params[2]);

        // 获取召唤物数量
        int summonsSize = role.getSummoningHandler().getSummonsSize(summonId);
        return AbstractSkillEffectValue.matchNumByConfig(summonsSize, configNum, rule);
    }
}

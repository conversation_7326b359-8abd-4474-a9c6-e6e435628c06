package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.proto.CommonEnum;

/**
 * 是否是采集中
 *
 * <AUTHOR>
 */
public class IsCollectingStatusChecker extends <PERSON><PERSON>he<PERSON> {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        return role.getAdapter().getArmyDetailState() == CommonEnum.ArmyDetailState.ADS_COLLECT;
    }
}

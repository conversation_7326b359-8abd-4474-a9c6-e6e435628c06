package com.yorha.cnc.battle.skill.effect.impl;

import com.yorha.cnc.battle.context.ActionContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.PlayerScene;
import res.template.BattleBuffTemplate;
import res.template.SkillEffectTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 加buff
 *
 * <AUTHOR>
 */
public class BatchLayerBuffValue extends AbstractSkillEffectValue {

    public BatchLayerBuffValue() {
        super(CommonEnum.SkillEffectType.SET_ADD_BATCH_BUFF);
    }

    @Override
    public List<PlayerScene.EffectDTO> handle(BattleTickContext tickContext, ActionContext actionCtx, SkillEffectTemplate template,
                                              BattleRole attacker, BattleRole target, EffectContext effectContext) {
        List<PlayerScene.EffectDTO> ret = new ArrayList<>();
        int buffId = template.getValue1();
        int num = template.getValue2();
        BattleBuffTemplate buffTemplate = ResHolder.getTemplate(BattleBuffTemplate.class, buffId);
        int realNum = num;

        // 配置了限制层数时不能超过最大层数
        if (buffTemplate.getRuleValue() > 1) {
            realNum = Math.min(num, buffTemplate.getRuleValue());
        }

        Integer resultBuff = target.getBuffHandler().addBuff(attacker, buffId, realNum, effectContext);
        if (resultBuff != null) {
            PlayerScene.EffectDTO.Builder builder = PlayerScene.EffectDTO.newBuilder();
            builder.setTargetId(target.getRoleId());
            builder.setType(getType());
            builder.setValue(buffId);
            builder.setEffectId(template.getId());
            ret.add(builder.build());
        }
        return ret;
    }
}

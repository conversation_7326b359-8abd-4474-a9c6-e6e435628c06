package com.yorha.cnc.battle.record.action;

import com.yorha.cnc.battle.context.EffectContext;
import com.yorha.proto.CommonBattle;
import com.yorha.proto.CommonEnum;
import res.template.BattleBuffTemplate;
import res.template.BuffTemplate;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * <AUTHOR>
 */
public class BuffAction {
    private int buffId;
    private CommonEnum.BattleLogBuffType logBuffType;
    private long lifeCycleValue;
    private EffectContext effectContext;
    private long value;
    private int layer;
    private CommonEnum.BattleBuffOperateType opType;

    public BuffAction() {
    }

    public int getBuffId() {
        return buffId;
    }

    public CommonEnum.BattleLogBuffType getLogBuffType() {
        return logBuffType;
    }

    public BuffAction setBuffId(int buffId) {
        this.buffId = buffId;
        return this;
    }

    public BuffAction setLogBuffType(CommonEnum.BattleLogBuffType logBuffType) {
        this.logBuffType = logBuffType;
        return this;
    }

    public BuffAction setLifeCycleValue(long lifeCycleValue) {
        this.lifeCycleValue = lifeCycleValue;
        return this;
    }

    public BuffAction setExecutorInfo(EffectContext effectContext) {
        this.effectContext = effectContext;
        return this;
    }

    public BuffAction setValue(long value) {
        this.value = value;
        return this;
    }

    public BuffAction setOpType(CommonEnum.BattleBuffOperateType opType) {
        this.opType = opType;
        return this;
    }

    public CommonEnum.BattleBuffOperateType getOpType() {
        return opType;
    }

    public CommonBattle.BattleEvent convert2Event() {
        return CommonBattle.BattleEvent.newBuilder()
                .setEvent(CommonEnum.BattleEventEnum.BET_BUFF)
                .setBuffEvent(convert2BuffEvent())
                .build();
    }

    private CommonBattle.BuffEvent convert2BuffEvent() {
        CommonBattle.BuffEvent.Builder builder = CommonBattle.BuffEvent.newBuilder()
                .setBuffId(buffId)
                .setLifeCycleValue(lifeCycleValue)
                .setValue(value);
        if (logBuffType != null) {
            builder.setBuffType(logBuffType);
        }
        if (effectContext != null) {
            builder.setExecutor(effectContext.convert2Pb());
        }
        builder.setOpType(opType);
        return builder.build();
    }

    public long getLifeCycleValue() {
        return lifeCycleValue;
    }

    public EffectContext getExecutorInfo() {
        return effectContext;
    }

    public long getValue() {
        return value;
    }

    public int getLayer() {
        return layer;
    }

    public BuffAction setLayer(int layer) {
        this.layer = layer;
        return this;
    }

    public boolean needLog() {
        if (logBuffType == CommonEnum.BattleLogBuffType.BLBT_ITEM) {
            BuffTemplate buffTemplate = getResHolder().findValueFromMap(BuffTemplate.class, buffId);
            if (buffTemplate == null) {
                return false;
            }
            return buffTemplate.getWarReport();
        } else {
            BattleBuffTemplate buffTemplate = getResHolder().findValueFromMap(BattleBuffTemplate.class, buffId);
            if (buffTemplate == null) {
                return false;
            }
            return buffTemplate.getWarReport();
        }
    }
}

package com.yorha.cnc.battle.handler;

import com.google.common.collect.Lists;
import com.yorha.cnc.battle.context.BattleRoleContext;
import com.yorha.cnc.battle.context.BattleTickContext;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.event.*;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.cnc.battle.record.action.TroopAction;
import com.yorha.cnc.battle.skill.SkillSystem;
import com.yorha.game.gen.prop.BattleRecordAllProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class EventHandler extends BattleHandlerAbs<BattleRole> {
    private static final Logger LOGGER = LogManager.getLogger(EventHandler.class);

    /**
     * 战斗tick外发生的事件，会在下一次tick时消费
     */
    private final List<BattleRoundEvent> roundEventList = Lists.newArrayList();
    /**
     * AI技能事件，需要在TryAction时集中消费
     */
    private final List<BattleRoundEvent> aiSkillEventList = Lists.newArrayList();

    public EventHandler(BattleRole role) {
        super(role);
    }

    public void addBattleRoundEvent(BattleRoundEvent event) {
        // 不在战斗中且不需要激活Role的事件直接忽略
        if (role.getGround().getRoleOrNull(role.getRoleId()) == null && !event.needActivateRole()) {
            return;
        }
        if (event instanceof FireSkillEvent) {
            aiSkillEventList.add(event);
        } else {
            roundEventList.add(event);
        }
        if (event.needActivateRole()) {
            role.getGround().tryActivateRole(role, "BattleRoundEvent");
        }
    }

    public void handleBattleRoundEvent(BattleTickContext tickContext) {
        role.getSkillHandler().resetBlackboard();
        boolean hasGarrisonChangeEvent = false;

        for (BattleRoundEvent iEvent : roundEventList) {
            if (iEvent instanceof BuffEvent) {
                handleBuffEvent((BuffEvent) iEvent);
            }
            if (iEvent instanceof TroopEvent) {
                handleTroopEvent((TroopEvent) iEvent);
            }
            if (iEvent instanceof GarrisonChangeEvent) {
                hasGarrisonChangeEvent = true;
            }
            if (iEvent instanceof FireSkillEvent) {
                LOGGER.error("BattleErrLog FireSkillEvent should be handled in TryAction.");
            }
            if (iEvent instanceof TriggerSkillEvent) {
                TriggerSkillEvent event = (TriggerSkillEvent) iEvent;
                SkillSystem.trigger(role, event.getTriggerType());
            }
            if (iEvent instanceof BattleArmyStateChangeEvent) {
                BattleArmyStateChangeEvent event = (BattleArmyStateChangeEvent) iEvent;
                // 检测并清理隐身状态
                role.getBuffHandler().checkOrClearStealth(event);
            }
        }

        if (hasGarrisonChangeEvent) {
            // 战报拆分前需要先把其他event给处理掉，所以这个event放最后
            LOGGER.debug("BattleLog {} GarrisonReplaced.", role);
            handleGarrisonReplaced(tickContext);
        }
        roundEventList.clear();
    }

    public void handleAiSkillEvent(BattleTickContext tickContext) {
        for (BattleRoundEvent iEvent : aiSkillEventList) {
            FireSkillEvent event = (FireSkillEvent) iEvent;
            SkillSystem.fireByEvent(role, event);
        }
        aiSkillEventList.clear();
    }

    public void clear() {
        roundEventList.clear();
        aiSkillEventList.clear();
    }

    private void handleBuffEvent(BuffEvent event) {
        switch (event.getOpType()) {
            case ADD: {
//                BattleLogUtil.logAddBuff(role, event.getBuffId(), CommonEnum.BattleLogBuffType.BLBT_ITEM);
                break;
            }
            case REMOVE: {
                BattleLogUtil.logRemoveBuff(role, event.getBuffId());
                break;
            }
            case TAKE_EFFECT: {
                role.getSkillBlackboard().getCurRoundValidBuffs().add(event.getBuffId());
                break;
            }
        }
    }

    private void handleTroopEvent(TroopEvent event) {
        BattleRoleContext.RoundTroopEvent roundTroopEvent = new BattleRoleContext.RoundTroopEvent(TroopAction.transSoldierReason2BattleLogType(event.getReason()), event.getName(), event.getClanName(), event.getCardHead(), event.getSoldierNum());
        role.getContext().drawRoundPoint().addEvent(roundTroopEvent);
        BattleLogUtil.logTroop(role, event.getSoldierNum(), event.getReason(), event.getMemberId());
    }

    private void handleGarrisonReplaced(BattleTickContext tickContext) {
        role.buildHero();

        // 当守城将发生变化需要切割战报，但是不中断relation
        if (!role.getContext().nothingHappened()) {
            // 结束所有relation context
            for (BattleRelation relation : role.getRelations()) {
                // 切割的战报不需要合并
                relation.getContext().getRecordOne().setNeedMerge(false);
                relation.getContext().end(tickContext);
            }
            // 先把自己的战报打出来
            BattleRecordAllProp recordAllProp = role.flushRecord();
            role.getAdapter().afterGarrisonReplaced(recordAllProp);

            // 将自己关联的每个relation，都切割一下单战报，送给每个enemyRole，然后重置整个relationContext
            for (Map.Entry<Long, ? extends BattleRelation> entry : role.getMyBattleRelation().entrySet()) {
                long enemyRoleId = entry.getKey();
                BattleRelation relation = entry.getValue();
                // 切割战报，吐给enemy
                BattleRoleContext enemyRoleCtx = relation.getRole(enemyRoleId).getContext();
                enemyRoleCtx.addSettledRecordOne(relation.getContext().getRecordOne());
                // relation context
                relation.getContext().addHistoryRecords();
                relation.getContext().clear();
                enemyRoleCtx.removeRelation(role.getRoleId(), relation.getContext());
                // 双方重新加入relation context
                enemyRoleCtx.addRelation(role.getRoleId(), relation.getContext(), false);
                role.getContext().addRelation(enemyRoleId, relation.getContext(), true);
                // 初始化relation context
                relation.getContext().prepareRound(tickContext);
                relation.getContext().initRelationRecord();
            }
        }
    }
}

package com.yorha.cnc.battle.skill.status.impl;

import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.skill.SkillEffect;
import com.yorha.cnc.battle.skill.effect.AbstractSkillEffectValue;

/**
 * 组召唤物数量
 *
 * <AUTHOR>
 */
public class SummonsGroupChecker extends StatusChecker {
    @Override
    public boolean check(BattleRole role, SkillEffect effect, String[] params) {
        int groupId = Integer.parseInt(params[0]);
        int rule = Integer.parseInt(params[1]);
        int configNum = Integer.parseInt(params[2]);
        // 获取召唤物数量
        int groupSummonsSize = role.getSummoningHandler().getSummonsGroupSize(groupId);
        return AbstractSkillEffectValue.matchNumByConfig(groupSummonsSize, configNum, rule);
    }
}

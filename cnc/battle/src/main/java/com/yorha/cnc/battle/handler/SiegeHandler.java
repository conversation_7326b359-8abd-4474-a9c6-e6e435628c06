package com.yorha.cnc.battle.handler;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.cnc.battle.core.BattleGround;
import com.yorha.cnc.battle.core.BattleRelation;
import com.yorha.cnc.battle.core.BattleRole;
import com.yorha.cnc.battle.record.BattleLogUtil;
import com.yorha.common.server.ServerContext;
import com.yorha.game.gen.prop.SiegeInfoProp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Set;

/**
 * 合围，夹击
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public class SiegeHandler extends BattleHandlerAbs<BattleRole> {
    private static final Logger LOGGER = LogManager.getLogger(SiegeHandler.class);

    /**
     * 合围中的战斗关系
     * key: clanId, value:roleIds
     */
    private final Map<Long, Set<Long>> siegeMap;
    /**
     * 上一回合夹击数量
     */
    private int lastRoundPincerNum;

    public SiegeHandler(BattleRole role) {
        super(role);
        this.siegeMap = Maps.newHashMap();
    }

    public Set<Long> getSiegeRoles(long clanId) {
        return siegeMap.getOrDefault(clanId, Sets.newHashSet());
    }

    public void addSiege(BattleRole other, BattleRelation relation, boolean checkHangup) {
        if (!role.getAdapter().needCheckSiege()) {
            return;
        }
        if (relation == null) {
            LOGGER.error("BattleErrLog {} addSiege failed, relation is null={}", role, other);
            return;
        }
        if (checkHangup && relation.isHangupInLastTick()) {
            LOGGER.error("BattleErrLog {} addSiege failed, relation is hang up, other:{}, relation:{}", role, other, relation.getKey());
            return;
        }
        long clanId = other.getAdapter().getClanId();
        if (clanId <= 0) {
            return;
        }
        SiegeInfoProp siegeInfoProp = role.getAdapter().getBattleProp().getSiegeInfo().get(clanId);
        if (siegeInfoProp == null) {
            siegeInfoProp = new SiegeInfoProp().setClanId(clanId);
            role.getAdapter().getBattleProp().getSiegeInfo().put(clanId, siegeInfoProp);
        }
        boolean add = siegeMap.computeIfAbsent(clanId, v -> Sets.newHashSet()).add(other.getRoleId());
        if (add) {
            siegeInfoProp.setCount(siegeMap.get(clanId).size());
            if (ServerContext.isTestEnv() || ServerContext.isDevEnv()) {
                LOGGER.info("BattleLog {} siegeChange add clanId:{}, role:{}, relationId:{}, siegeMap:{}", role, clanId, other, relation.getId(), siegeMap);
            }
        }
    }

    public void removeSiege(BattleRole other) {
        if (!role.getAdapter().needCheckSiege()) {
            return;
        }
        BattleRelation relation = role.relationWith(other.getRoleId());
        if (relation == null) {
            LOGGER.error("BattleErrLog {} removeSiege failed, relation is null:{}", role, other);
            return;
        }
        long clanId = other.getAdapter().getClanId();
        if (clanId <= 0) {
            return;
        }
        Set<Long> roles = siegeMap.get(clanId);
        if (roles != null) {
            boolean remove = roles.remove(other.getRoleId());
            if (remove) {
                role.getAdapter().getBattleProp().getSiegeInfo().get(clanId).setCount(roles.size());
            }

            if (roles.isEmpty()) {
                siegeMap.remove(clanId);
                role.getAdapter().getBattleProp().getSiegeInfo().remove(clanId);
            }
            if (remove) {
                if (ServerContext.isTestEnv() || ServerContext.isDevEnv()) {
                    LOGGER.info("BattleLog {} siegeChange remove clanId:{}, role:{}, relationId:{}, siegeMap:{}", role, clanId, other, relation.getKey(), siegeMap);
                }
            }
        }
    }

    public void clear() {
        role.getAdapter().getBattleProp().getSiegeInfo().clear();
        siegeMap.clear();
    }

    public void refreshSiege(long oldTargetId, long newTargetId) {
        // 退出对老目标的合围
        if (oldTargetId > 0) {
            BattleRole oldTargetRole = role.getGround().getRoleOrNull(oldTargetId);
            if (oldTargetRole != null) {
                BattleRelation relation = role.relationWith(oldTargetRole.getRoleId());
                if (relation != null) {
                    oldTargetRole.getSiegeHandler().removeSiege(role);
                } else {
                    LOGGER.warn("BattleLog {} updateSiege oldRelation null:{}", role, oldTargetId);
                }
            } else {
                LOGGER.debug("BattleLog {} updateSiege oldTargetRole null or unsupported:{}", role, oldTargetId);
            }
        }

        // 加入对新目标的合围
        if (newTargetId > 0) {
            BattleRole targetRole = role.getTargetRole();
            if (targetRole != null) {
                BattleRelation relation = role.relationWith(targetRole.getRoleId());
                if (relation != null) {
                    targetRole.getSiegeHandler().addSiege(role, relation, true);
                } else {
                    LOGGER.warn("BattleLog {} updateSiege newRelation null:{}", this, newTargetId);
                }
            } else {
                LOGGER.debug("BattleLog {} updateSiege targetRole null or unsupported:{}", this, newTargetId);
            }
        }
    }

    /**
     * 刷新个人受夹击的数量
     */
    public void refreshPincerNum() {
        int num = 0;
        for (BattleRelation r : role.getMyBattleRelation().values()) {
            // 只有当前回合不挂起且对方的攻击目标是我，才能算在夹击数量里面
            BattleRole enemyRole = r.getEnemyRole(role.getRoleId());
            if (!r.shouldHangup() && enemyRole.getTargetId() == role.getRoleId()) {
                num++;
            }
        }

        if (num > 0 && num < lastRoundPincerNum) {
            // 夹击数量变少，移除原来的夹击buff
            BattleLogUtil.logBePincerAtk(role, lastRoundPincerNum);
        }
        if (num > 1 && num > lastRoundPincerNum) {
            // 受到夹击
            BattleLogUtil.logBePincerAtk(role, num);
        }
        lastRoundPincerNum = num;
        if (role.getAdapter().getBattleProp().getMany() != num) {
            role.getAdapter().getBattleProp().setMany(num);
            // 日志-被夹击数量
            BattleGround.getBattleLog().printfRelationPincerNum(role, num);
        }
    }
}

classDiagram
    class PlayerActor {
        -long playerId
        -int curZoneId
        -PlayerEntity playerEntity
        -FriendPlayerEntity friendPlayerEntity
        -ChatPlayerEntity chatPlayerEntity
        +PlayerActor(ActorSystem, IActorRef)
        +getPlayerId() long
        +getEntity() PlayerEntity
        +loadPlayerEntity() PlayerEntity
        +createPlayerEntity(String, int) PlayerEntity
        +callGameDb(SelectUniqueAsk) GetResult
        +callGameDb(UpsertAsk) UpsertResult
        +callGameDb(UpdateAsk) UpdateResult
        +callGameDb(InsertAsk) InsertResult
    }

    class PlayerEntity {
        -PlayerActor actor
        -PlayerProp prop
        -boolean isRegister
        -boolean isZoneMigrate
        -PlayerDbComponent dbComponent
        -PlayerPropComponent playerPropComponent
        +PlayerEntity(PlayerActor, PlayerProp, boolean, Player.PlayerEntity, boolean)
        +getProp() PlayerProp
        +getDbComponent() PlayerDbComponent
        +getPlayerPropComponent() PlayerPropComponent
        +ownerActor() PlayerActor
        +afterDestroy()
        +isZoneMigrate() boolean
    }

    class PlayerProp {
        -long markBits0
        -long markBits1
        -PropertyChangeListener listener
        -long id
        -String openId
        -PlayerSceneProp scene
        -ScenePlayerProp scenePlayer
        -long createTime
        -PlayerBasicInfoProp basicInfo
        -PlayerAvatarModelProp avatarModel
        -PlayerZoneModelProp zoneModel
        +PlayerProp()
        +PlayerProp(AbstractPropNode, int)
        +getId() long
        +setId(long) PlayerProp
        +getOpenId() String
        +setOpenId(String) PlayerProp
        +setListener(PropertyChangeListener)
        +getListener() PropertyChangeListener
        +hasAnyMark() boolean
        +unMarkAll()
        +mark(int)
        +getCopyDbBuilder() Player.PlayerEntity.Builder
        +mergeFromDb(Player.PlayerEntity)
        +mergeChangeFromDb(Player.PlayerEntity)
        +static of(Player.PlayerEntity, Player.PlayerEntity) PlayerProp
    }

    class TcaplusDbPlayerTable {
        <<protobuf>>
        -long playerId
        -int curZoneId
        -Player.PlayerEntity fullAttr
        -Player.PlayerEntity changedAttr
        -int stage
        -int migrateTarget
        -CommonMsg.PlayerOfflineView offlineView
        +Builder newBuilder()
        +setPlayerId(long) Builder
        +setCurZoneId(int) Builder
        +setFullAttr(Player.PlayerEntity) Builder
        +setChangedAttr(Player.PlayerEntity) Builder
        +setStage(int) Builder
        +setMigrateTarget(int) Builder
        +setOfflineView(CommonMsg.PlayerOfflineView) Builder
        +getFullAttr() Player.PlayerEntity
        +getChangedAttr() Player.PlayerEntity
        +getStage() int
        +getMigrateTarget() int
    }

    class PlayerPlayerEntity {
        <<protobuf>>
        -long id
        -string openId
        -PlayerScene scene
        -ScenePlayer scenePlayer
        -long createTime
        -ClanInfo clan
        -HistoryFragments fragments
        -map~int64, Item~ items
        -map~int32, Currency~ purse
        -PlayerBasicInfo basicInfo
        -PlayerAvatarModel avatarModel
        -PlayerZoneModel zoneModel
        +Builder newBuilder()
        +setId(long) Builder
        +setOpenId(string) Builder
        +getSerializedSize() int
        +build() Player.PlayerEntity
    }

    class PlayerDbComponent {
        -PlayerEntity owner
        -DbTaskProxy dbTaskProxy
        +PlayerDbComponent(PlayerEntity)
        +onCreate(Player.PlayerEntity)
        +insertPlayerTable()
        +saveDirty()
        +saveOnDestroy()
        +saveOnMigrate(boolean)
        +saveOfflineView(CommonMsg.PlayerOfflineView)
        -buildFullAttrSaveData(AbstractActor) Player.PlayerEntity
        -newDbSaveRequest(AbstractActor, Player.PlayerEntity, Player.PlayerEntity) TcaplusDb.PlayerTable.Builder
    }

    class PlayerPropComponent {
        -PlayerEntity owner
        -PlayerProp playerProp
        -PlayerPB.PlayerEntityPB.Builder changedCsBuilder
        -boolean isHandleCsMsg
        -boolean isFullPropNotified
        +PlayerPropComponent(PlayerEntity)
        +setPropertyChangeListener()
        +onPropChangeListener()
        +collectCsDirty()
        +sendCsDirty()
        +ntfFullPlayerPropAtLogin()
        +stopUpdatePlayerCard()
    }

    class PropertyChangeListener {
        <<interface>>
        +onPropertyChange()
        +isInTaskQueue() boolean
    }

    class DbTaskProxy {
        -String name
        -String entityId
        -AbstractActor owner
        -PlayerChangeAttrStrategy strategy
        +insert()
        +update()
        +saveDbSync()
        +stop()
        +isProxyStop() boolean
    }

    class PlayerChangeAttrStrategy {
        -Player.PlayerEntity changedAttr
        +PlayerChangeAttrStrategy(Player.PlayerEntity)
        +buildFullAttrSaveData(AbstractActor) Player.PlayerEntity
        +buildFullAttrSaveData(UpdateResult) Player.PlayerEntity
        +newDbSaveRequest(AbstractActor, Player.PlayerEntity, Player.PlayerEntity) Message.Builder
    }

    %% 继承关系
    PlayerProp --|> AbstractPropNode
    PlayerEntity --|> AbstractEntity
    PlayerActor --|> GameActorWithCall
    PlayerDbComponent --|> PlayerComponent
    PlayerPropComponent --|> PlayerComponent

    %% 组合关系
    PlayerActor *-- PlayerEntity : "1"
    PlayerEntity *-- PlayerProp : "1"
    PlayerEntity *-- PlayerDbComponent : "1"
    PlayerEntity *-- PlayerPropComponent : "1"
    PlayerDbComponent *-- DbTaskProxy : "1"
    DbTaskProxy *-- PlayerChangeAttrStrategy : "1"

    %% 关联关系
    PlayerActor --> TcaplusDbPlayerTable : "DB操作"
    PlayerDbComponent --> TcaplusDbPlayerTable : "构建DB请求"
    PlayerProp --> PlayerPlayerEntity : "序列化/反序列化"
    PlayerProp --> PropertyChangeListener : "属性变更监听"
    PlayerPropComponent --> PropertyChangeListener : "设置监听器"

    %% 依赖关系
    PlayerActor ..> PlayerProp : "创建/加载"
    TcaplusDbPlayerTable ..> PlayerPlayerEntity : "包含fullAttr/changedAttr"
    PlayerChangeAttrStrategy ..> PlayerPlayerEntity : "构建保存数据"
    PlayerProp ..> PlayerPlayerEntity : "getCopyDbBuilder()"

    %% 数据流
    PlayerActor -.-> TcaplusDbPlayerTable : "1. 查询DB"
    TcaplusDbPlayerTable -.-> PlayerPlayerEntity : "2. 获取fullAttr/changedAttr"
    PlayerPlayerEntity -.-> PlayerProp : "3. PlayerProp.of()"
    PlayerProp -.-> PlayerEntity : "4. 创建PlayerEntity"
    PlayerEntity -.-> PlayerActor : "5. 设置到Actor"

    PlayerProp -.-> PlayerPropComponent : "6. 属性变更"
    PlayerPropComponent -.-> PlayerDbComponent : "7. 触发保存"
    PlayerDbComponent -.-> PlayerPlayerEntity : "8. 构建保存数据"
    PlayerPlayerEntity -.-> TcaplusDbPlayerTable : "9. 保存到DB"

    note right of TcaplusDbPlayerTable
        数据库表结构:
        - playerId: 主键
        - curZoneId: 当前区域ID
        - fullAttr: 完整属性数据
        - changedAttr: 变更属性数据
        - stage: 迁移阶段
        - migrateTarget: 迁移目标
        - offlineView: 离线视图
    end note

    note right of PlayerProp
        内存数据结构:
        - 69个业务字段
        - 属性变更标记机制
        - 序列化/反序列化
        - 属性变更监听
    end note

    note right of PlayerPlayerEntity
        Protobuf消息:
        - 与PlayerProp字段对应
        - 用于网络传输和DB存储
        - 支持增量更新
    end note
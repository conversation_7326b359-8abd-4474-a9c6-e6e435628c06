# ActorInternalIdGenerator - Actor内部ID生成器

## 概述

`ActorInternalIdGenerator` 是一个专门为Actor模型设计的轻量级ID生成器，由时间戳和自增ID两部分组成。它非线程安全，专门用于Actor内部的单线程环境。

## 特性

- **轻量级设计**：专为Actor单线程环境优化
- **时间戳精确到秒**：减少时钟回拨影响
- **高性能**：每秒可生成42亿个ID
- **长期支持**：支持约68年的时间范围
- **保留位设计**：第一位固定为0，确保ID始终为正数
- **全局唯一**：在同一Actor内保证ID唯一性
- **趋势递增**：ID按时间趋势递增

## ID结构

```
+--------------------------------------------------------------------------+
| 1位保留位 | 31位时间戳(秒级) | 32位自增序列号 |
| (固定为0) | (支持68年)      | (每秒42亿个ID) |
+--------------------------------------------------------------------------+
```

- **最高1位**：保留位（固定为0，确保ID为正数）
- **中间31位**：相对时间戳（秒级，相对于2025-01-01 00:00:00 UTC）
- **低32位**：自增序列号（每秒从1开始递增）

## 使用方法

### 基本使用

```java
// 创建ID生成器
ActorInternalIdGenerator generator = new ActorInternalIdGenerator("player-12345");

// 生成ID
long id = generator.nextId();

// 解析ID
ActorInternalIdGenerator.IdInfo info = ActorInternalIdGenerator.parseId(id);
System.out.println("时间戳: " + info.getFormattedTimestamp());
System.out.println("序列号: " + info.sequence());

// 验证ID
boolean isValid = ActorInternalIdGenerator.isValidId(id);
```

### 在Actor中集成

```java
public class PlayerActor extends AbstractActor {
    private final ActorInternalIdGenerator idGenerator;
    
    public PlayerActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.idGenerator = new ActorInternalIdGenerator(getId());
    }
    
    // 生成消息ID
    public long generateMessageId() {
        return idGenerator.nextId();
    }
    
    // 生成事件ID
    public long generateEventId() {
        return idGenerator.nextId();
    }
}
```

### 批量生成示例

```java
ActorInternalIdGenerator generator = new ActorInternalIdGenerator("batch-actor");

// 批量生成ID
List<Long> ids = new ArrayList<>();
for (int i = 0; i < 1000; i++) {
    ids.add(generator.nextId());
}

// 所有ID都是唯一的
Set<Long> uniqueIds = new HashSet<>(ids);
assert uniqueIds.size() == ids.size();
```

## API参考

### 构造方法

```java
// 指定Actor ID
ActorInternalIdGenerator(String actorId)

// 使用默认Actor ID
ActorInternalIdGenerator()
```

### 核心方法

```java
// 生成下一个ID
long nextId()

// 解析ID信息
static IdInfo parseId(long id)

// 验证ID有效性
static boolean isValidId(long id)

// 获取当前序列号
long getCurrentSequence()

// 获取上次时间戳
long getLastTimestamp()

// 获取Actor ID
String getActorId()
```

### IdInfo类

```java
public record IdInfo(long timestamp, long sequence, long relativeTimestamp) {
    // 获取格式化的时间字符串
    String getFormattedTimestamp()
}
```

## 使用场景

1. **消息序列号生成**：为Actor内部消息生成唯一序列号
2. **事件ID生成**：为Actor产生的事件生成唯一标识
3. **临时ID生成**：为临时对象生成唯一标识
4. **请求ID生成**：为Actor处理的请求生成唯一标识

## 性能特性

- **生成速度**：单线程环境下可达到数万ID/秒
- **内存占用**：极低，只维护少量状态变量
- **CPU开销**：最小化，主要是简单的位运算

## 注意事项

### 线程安全

⚠️ **重要**：此类非线程安全，只能在Actor内部使用！

```java
// ✅ 正确用法 - 在Actor内部使用
public class MyActor extends AbstractActor {
    private final ActorInternalIdGenerator idGenerator = 
        new ActorInternalIdGenerator(getId());
}

// ❌ 错误用法 - 多线程共享
public class SharedService {
    private static final ActorInternalIdGenerator generator = 
        new ActorInternalIdGenerator(); // 危险！
}
```

### 时钟回拨

生成器会检测时钟回拨并抛出异常：

```java
try {
    long id = generator.nextId();
} catch (RuntimeException e) {
    // 处理时钟回拨异常
    logger.error("Clock moved backwards", e);
}
```

### 序列号溢出

当单秒内序列号用完时，生成器会等待下一秒：

```java
// 自动处理序列号溢出
long id = generator.nextId(); // 可能会等待下一秒
```

## 与其他ID生成器的比较

| 特性 | ActorInternalIdGenerator | SnowflakeIdGenerator | IdFactory |
|------|-------------------------|---------------------|-----------|
| 线程安全 | ❌ | ❌ | ✅ |
| 使用场景 | Actor内部 | Zone级别 | 全局持久化 |
| 时间精度 | 秒级 | 毫秒级 | - |
| 序列号位数 | 32位 | 10位 | 变长 |
| 性能 | 极高 | 高 | 中等 |
| 复杂度 | 低 | 中等 | 高 |

## 最佳实践

1. **每个Actor一个实例**：为每个Actor创建独立的ID生成器实例
2. **合理命名**：使用有意义的actorId来便于调试
3. **异常处理**：妥善处理时钟回拨等异常情况
4. **性能监控**：在高频使用场景下监控生成性能
5. **ID验证**：在需要时使用`isValidId()`验证ID有效性

## 示例代码

完整的使用示例请参考：
- `ActorInternalIdGeneratorExample.java` - 基本使用示例
- `ActorInternalIdGeneratorTest.java` - 单元测试示例

## 常见问题

### Q: 为什么选择秒级时间戳而不是毫秒级？

A: 秒级时间戳可以减少时钟回拨的影响，同时32位序列号提供了足够的容量（每秒42亿个ID）。

### Q: 多个Actor可能生成相同的ID吗？

A: 是的，不同Actor的生成器可能在相同时间戳和序列号下生成相同ID。如果需要全局唯一性，请使用其他ID生成器。

### Q: 如何处理Actor重启后的ID连续性？

A: 此生成器不保证重启后的连续性。如果需要持久化的连续ID，请使用`IdFactory`。

### Q: 生成器的时间范围是多少？

A: 支持约68年（2^31秒），从2025年开始计算，足够长期使用。

### Q: 为什么要保留第一位？

A: 保留第一位（固定为0）确保生成的ID始终为正数，避免负数ID可能带来的问题，同时与Java中long类型的符号位保持一致。

## 更新日志

- **v1.0.0** (2025-07-04): 初始版本发布
  - 基本ID生成功能
  - ID解析和验证
  - 完整的单元测试
  - 使用示例和文档

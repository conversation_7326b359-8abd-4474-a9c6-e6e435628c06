package com.yorha.cnc.clancard;

import com.yorha.common.actor.ClanCardService;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.cache.BatchAsyncLoadCache;
import com.yorha.common.cache.CacheMgr;
import com.yorha.common.cache.action.BatchAsyncAddAction;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.BatchSelectAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.CacheUsageEnum;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonMsg.ClanCardInfo;
import com.yorha.proto.CommonMsg.ClanSimpleInfo;
import com.yorha.proto.SsClanCard.*;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ClanCardServiceImpl implements ClanCardService, BatchAsyncAddAction<Long, ClanCardInfo> {
    private static final Logger LOGGER = LogManager.getLogger(ClanCardServiceImpl.class);
    /**
     * actor
     */
    private final ClanCardActor actor;
    /**
     * 联盟数据缓存
     */
    private final BatchAsyncLoadCache<Long, ClanCardInfo> cache;

    public static final String CARD_TYPE = "ClanCard";

    public ClanCardServiceImpl(ClanCardActor actor) {
        this.actor = actor;
        this.cache = CacheMgr.buildBatchAsyncLoadCache(100_000, TimeUnit.MINUTES.toMillis(10), this, CacheUsageEnum.CLAN);
    }

    @Override
    public void loadOne(Long key) {
        TcaplusDb.ClanCardTable.Builder req = TcaplusDb.ClanCardTable.newBuilder();
        req.setClanId(key);
        actor.askGameDb(new SelectUniqueAsk<>(req)).onComplete(
                (ans, t) -> {
                    if (t != null || ans == null || !DbUtil.isOk(ans.getCode()) || ans.value == null) {
                        cache.onCacheLoaded(key);
                        int code = 0;
                        if (ans != null) {
                            code = ans.getCode();
                        }
                        LOGGER.warn("clanCard loadOne fail {} code: {}", key, code, t);
                        return;
                    }
                    cache.putData(key, ans.value.getInfo());
                    cache.onCacheLoaded(key);
                }
        );
    }

    @Override
    public void loadBatch(Collection<Long> keys) {
        List<TcaplusDb.ClanCardTable.Builder> listReq = new ArrayList<>();
        for (Long ket : keys) {
            listReq.add(TcaplusDb.ClanCardTable.newBuilder().setClanId(ket));
        }
        actor.askGameDb(new BatchSelectAsk<>(listReq, BatchGetOption.newBuilder().build())).onComplete(
                (ans, t) -> {
                    // 先把数据都放进去
                    if (ans != null) {
                        for (ValueWithVersion<TcaplusDb.ClanCardTable.Builder> value : ans.values) {
                            if (value.value == null) {
                                continue;
                            }
                            cache.putData(value.value.getClanId(), value.value.getInfo());
                        }
                        if (ans.values.size() != listReq.size()) {
                            LOGGER.error("clanCard loadBatch less {}/{} {}, requestId={}", ans.values.size(), listReq.size(), ans.getCode(), ans.requestId);
                        } else {
                            LOGGER.info("clanCard loadBatch end {} {}, requestId={}", ans.values.size(), ans.getCode(), ans.requestId);
                        }
                    } else {
                        LOGGER.error("clanCard loadBatch fail {} ", listReq.size(), t);
                    }
                    // 不管有没有结果 发出去过的都要调callBack
                    for (TcaplusDb.ClanCardTable.Builder builder : listReq) {
                        cache.onCacheLoaded(builder.getClanId());
                    }
                }
        );
    }

    private ClanSimpleInfo transToSimpleInfo(ClanCardInfo data) {
        ClanSimpleInfo.Builder builder = ClanSimpleInfo.newBuilder();
        StructClanPB.ClanBaseInfoPB base = data.getBase();
        builder.setClanId(data.getId()).setZoneId(data.getZoneId())
                .setOwnerId(data.getOwner().getId())
                .setOwnerName(data.getOwner().getCardHead().getName())
                .setName(base.getName())
                .setSimpleName(base.getSname())
                .setFlagColor(base.getFlagColor())
                .setTerritoryColor(base.getTerritoryColor())
                .setNationFlagId(base.getNationFlagId())
                .setFlagShading(base.getFlagShading())
                .setFlagSign(base.getFlagSign());
        return builder.build();
    }

    @Override
    public void handleQueryClanNameAsk(QueryClanNameAsk ask) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        MonitorUnit.CARD_REQUEST_TOTAL.labels(ServerContext.getBusId(), CARD_TYPE).inc();
        cache.getOrLoadData(
                ask.getClanId(),
                (d) -> {
                    if (d != null) {
                        QueryClanNameAns.Builder builder = QueryClanNameAns.newBuilder();
                        builder.setClanId(d.getId()).setClanName(d.getBase().getName()).setClanSimpleName(d.getBase().getSname());
                        actor.answerWithContext(context, builder.build());
                    } else {
                        LOGGER.warn("handleQueryClanNameAsk request fail, query ClanId={}", ask.getClanId());
                        actor.answerWithContext(context, QueryClanNameAns.getDefaultInstance());
                    }
                });
    }

    @Override
    public void handleQueryClanSimpleAsk(QueryClanSimpleAsk ask) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        MonitorUnit.CARD_REQUEST_TOTAL.labels(ServerContext.getBusId(), CARD_TYPE).inc();
        cache.getOrLoadData(
                ask.getClanId(),
                (d) -> {
                    if (d != null) {
                        QueryClanSimpleAns.Builder builder = QueryClanSimpleAns.newBuilder();
                        builder.setInfo(transToSimpleInfo(d));
                        actor.answerWithContext(context, builder.build());
                    } else {
                        LOGGER.warn("handleQueryClanSimpleAsk request fail, query ClanId={}", ask.getClanId());
                        actor.answerWithContext(context, QueryClanSimpleAns.getDefaultInstance());
                    }
                });
    }

    @Override
    public void handleQueryClanCardAsk(QueryClanCardAsk ask) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        MonitorUnit.CARD_REQUEST_TOTAL.labels(ServerContext.getBusId(), CARD_TYPE).inc();
        cache.getOrLoadData(
                ask.getClanId(),
                (d) -> {
                    if (d != null) {
                        actor.answerWithContext(context, QueryClanCardAns.newBuilder().setInfo(d).build());
                    } else {
                        LOGGER.warn("handleQueryClanCardAsk card request fail, query ClanId={}", ask.getClanId());
                        actor.answerWithContext(context, QueryClanCardAns.getDefaultInstance());
                    }
                });
    }

    @Override
    public void handleBatchQueryClanNameAsk(BatchQueryClanNameAsk ask) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        BatchQueryClanNameAns.Builder builder = BatchQueryClanNameAns.newBuilder();
        MonitorUnit.CARD_REQUEST_TOTAL.labels(ServerContext.getBusId(), CARD_TYPE).inc(ask.getClansCount());
        cache.batchGetOrLoadData(
                ask.getClansList(),
                (d) -> {
                    if (d == null) {
                        return;
                    }
                    builder.putInfo(d.getId(), ClanAllName.newBuilder().setClanName(d.getBase().getName()).setClanSimpleName(d.getBase().getSname()).build());
                },
                () -> {
                    actor.answerWithContext(context, builder.build());
                    if (builder.getInfoCount() != ask.getClansCount()) {
                        LOGGER.warn("handleBatchQueryClanNameAsk batch ask card less {}/{}", builder.getInfoCount(), ask.getClansCount());
                    }
                });
    }

    @Override
    public void handleBatchQueryClanSimpleAsk(BatchQueryClanSimpleAsk ask) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        BatchQueryClanSimpleAns.Builder builder = BatchQueryClanSimpleAns.newBuilder();
        MonitorUnit.CARD_REQUEST_TOTAL.labels(ServerContext.getBusId(), CARD_TYPE).inc(ask.getClansCount());
        cache.batchGetOrLoadData(
                ask.getClansList(),
                (d) -> {
                    if (d == null) {
                        return;
                    }
                    builder.putInfo(d.getId(), transToSimpleInfo(d));
                },
                () -> {
                    actor.answerWithContext(context, builder.build());
                    if (builder.getInfoCount() != ask.getClansCount()) {
                        LOGGER.warn("handleBatchQueryClanSimpleAsk batch ask card less {}/{}", builder.getInfoCount(), ask.getClansCount());
                    }
                });
    }

    @Override
    public void handleBatchQueryClanCardAsk(BatchQueryClanCardAsk ask) {
        final ActorMsgEnvelope context = actor.getCurrentEnvelope();
        BatchQueryClanCardAns.Builder builder = BatchQueryClanCardAns.newBuilder();
        MonitorUnit.CARD_REQUEST_TOTAL.labels(ServerContext.getBusId(), CARD_TYPE).inc(ask.getClansCount());
        cache.batchGetOrLoadData(
                ask.getClansList(),
                (d) -> {
                    if (d == null) {
                        return;
                    }
                    builder.putInfo(d.getId(), d);
                },
                () -> {
                    actor.answerWithContext(context, builder.build());
                    if (builder.getInfoCount() != ask.getClansCount()) {
                        LOGGER.warn("handleBatchQueryClanCardAsk batch ask card less {}/{}", builder.getInfoCount(), ask.getClansCount());
                    }
                });
    }

    @Override
    public void handleUpdateClanCardCmd(UpdateClanCardCmd ask) {
        cache.putData(ask.getCardInfo().getId(), ask.getCardInfo());
    }
}

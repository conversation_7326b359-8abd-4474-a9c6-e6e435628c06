package com.yorha.cnc.clancard;

import com.yorha.common.actor.ClanCardService;
import com.yorha.common.actor.ClanCardServices;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.actor.IActorRef;
import com.yorha.gemini.actor.msg.TypedMsg;

/**
 * <AUTHOR>
 */
public class ClanCardActor extends BaseGameActor implements ClanCardServices {
    private final ClanCardService service;

    public ClanCardActor(ActorSystem system, IActorRef self) {
        super(system, self);
        this.service = new ClanCardServiceImpl(this);
    }

    @Override
    public ClanCardService getClanCardService() {
        return service;
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }
}

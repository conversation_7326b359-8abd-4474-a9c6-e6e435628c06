package com.yorha.cnc.auth.agent;

import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.yorha.cnc.auth.AuthActor;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.auth.server.AuthIntlUtils;
import com.yorha.common.constant.AuthConstant;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.PlatformAsyncClient;
import com.yorha.common.utils.UrlUtils;
import com.yorha.common.utils.time.SystemClock;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.pool.PoolStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

public class AuthIntl implements IAuthAgent {
    private static final Logger LOGGER = LogManager.getLogger(AuthIntl.class);
    // 鉴权后缀
    private static final String VERIFY_LOGIN_URL = "/v2/auth/verify_login";

    private final PlatformAsyncClient httpClient;
    private ActorTimer clearTimer;
    private ActorTimer logTimer;

    public AuthIntl() {
        this.httpClient = new PlatformAsyncClient(AuthConstant.INTL_AUTH_TIMEOUT_MS, 2);
        LOGGER.info("auth intl init!");
    }

    @Override
    public void start(AuthActor actor) {
        String timerPrefix = actor.getId() + AuthConstant.AuthChannelType.INTL;
        this.clearTimer = actor.addRepeatTimer(timerPrefix, TimerReasonType.AUTH_CLEAR_CONNECTION, () -> {
            clearConnection(15);
        }, 15, 15, TimeUnit.SECONDS);

        this.logTimer = actor.addRepeatTimer(timerPrefix, TimerReasonType.AUTH_CONNECTION_LOG, () -> {
            LOGGER.info("AuthActor {} poolStats={}", timerPrefix, getPoolStats());
        }, 60, 60, TimeUnit.SECONDS);
    }

    @Override
    public void destroy() {
        httpClient.destroy();
        if (clearTimer != null) {
            clearTimer.cancel();
            clearTimer = null;
        }
        if (logTimer != null) {
            logTimer.cancel();
            logTimer = null;
        }
    }

    public void auth(String openId, String token, int platformId, int channelId, Consumer<JsonObject> jsonConsumer, Consumer<Exception> exceptionConsumer) {
        this.httpClient.execute2json(formPost(openId, token, platformId, channelId), jsonConsumer, exceptionConsumer);
    }

    HttpPost formPost(String openId, String token, int platformId, int channelId) {
        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");

        Map<String, Object> body = Maps.newHashMap();
        body.put("openid", openId);
        body.put("token", token);


        Map<String, Object> params = Maps.newHashMap();
        params.put("os", AuthIntlUtils.transPlatformId2Os(platformId));
        params.put("gameid", ClusterConfigUtils.getWorldConfig().getIntItem("auth_intl_game_id"));
        params.put("channelid", channelId);
        params.put("ts", SystemClock.nowOfSeconds());
        params.put("sdk_version", "");
        params.put("seq", "");
        params.put("source", 1);
        params.put("sig", AuthIntlUtils.makeSig(VERIFY_LOGIN_URL, params, body, ClusterConfigUtils.getWorldConfig().getStringItem("auth_intl_server_key")));

        String url = UrlUtils.buildUrlWithEmptyValue(VERIFY_LOGIN_URL, params);
        String authHost = ClusterConfigUtils.getWorldConfig().getStringItem("auth_intl_host");
        return this.httpClient.formPostRequest(authHost + url, body, header);
    }

    public void clearConnection(final int idleSecondLimit) {
        this.httpClient.clearConnection(idleSecondLimit);
    }

    public PoolStats getPoolStats() {
        return this.httpClient.getPoolStats();
    }
}

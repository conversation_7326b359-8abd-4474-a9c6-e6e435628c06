<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>
    <!-- 排除生成的代码 -->
    <Match>
        <Source name="~.*[\\/]gen[\\/].*\.java"/>
    </Match>
    <!-- 特别排除 common 模块的 gen 目录 -->
    <Match>
        <Source name="~.*[\\/]common[\\/]src[\\/]main[\\/]gen[\\/].*\.java"/>
    </Match>
    <Match>
        <Source name="~.*[\\/]common[\\/]src[\\/]main[\\/]gen[\\/].*\.java"/>
        <Source name="~.*[\\/][\\/]src[\\/]main[\\/]java[\\/]com[\\/]tencent[\\/].*\.java"/>
    </Match>
    <!-- 排除robot模块 -->
    <Match>
        <Package name="~com\.yorha\.robot\..*"/>
    </Match>

    <!-- 排除测试代码中的一些常见误报 -->
    <Match>
        <Source name="~.*[\\/]test[\\/].*\.java"/>
        <Bug pattern="DM_EXIT"/>
    </Match>
    <Match>
        <Source name="~.*[\\/]test[\\/].*\.java"/>
        <Bug pattern="DM_RUNFINALIZATION_ON_EXIT"/>
    </Match>

    <!-- 排除序列化相关的警告，如果项目不需要序列化 -->
    <Match>
        <Bug pattern="SE_BAD_FIELD"/>
    </Match>
    <Match>
        <Bug pattern="SE_NO_SERIALVERSIONID"/>
    </Match>

    <!-- 排除一些可能的误报 -->
    <Match>
        <Bug pattern="EI_EXPOSE_REP"/>
    </Match>
    <Match>
        <Bug pattern="EI_EXPOSE_REP2"/>
    </Match>

    <!-- 排除 switch 语句的 fallthrough 警告，如果是有意为之 -->
    <Match>
        <Bug pattern="SF_SWITCH_FALLTHROUGH"/>
    </Match>

    <!-- 排除空的 catch 块警告，如果是有意为之 -->
    <Match>
        <Bug pattern="DE_MIGHT_IGNORE"/>
    </Match>

    <!-- 排除未使用的字段警告，可能是框架使用 -->
    <Match>
        <Bug pattern="URF_UNREAD_FIELD"/>
    </Match>

    <!-- 排除可能的 null 指针警告，如果已经有其他检查 -->
    <Match>
        <Bug pattern="NP_NULL_ON_SOME_PATH"/>
    </Match>

    <!-- 排除资源泄漏警告，如果使用了 try-with-resources -->
    <Match>
        <Bug pattern="OBL_UNSATISFIED_OBLIGATION"/>
    </Match>

    <!-- 排除同步相关的警告，如果确认线程安全 -->
    <Match>
        <Bug pattern="IS2_INCONSISTENT_SYNC"/>
    </Match>

    <!-- 排除性能相关的警告，如果不是性能关键代码 -->
    <Match>
        <Bug pattern="DM_STRING_CTOR"/>
    </Match>
    <Match>
        <Bug pattern="DM_STRING_TOSTRING"/>
    </Match>

    <!-- 排除 equals 和 hashCode 相关的警告，如果使用了 Lombok 或其他工具 -->
    <Match>
        <Bug pattern="EQ_DOESNT_OVERRIDE_EQUALS"/>
    </Match>
    <Match>
        <Bug pattern="HE_EQUALS_USE_HASHCODE"/>
    </Match>

    <!-- 排除 finalize 相关的警告 -->
    <Match>
        <Bug pattern="FI_PUBLIC_SHOULD_BE_PROTECTED"/>
    </Match>

    <!-- 排除一些可能的安全警告，如果已经有其他安全措施 -->
    <Match>
        <Bug pattern="HARD_CODE_PASSWORD"/>
    </Match>

    <!-- 排除一些国际化相关的警告 -->
    <Match>
        <Bug pattern="DM_DEFAULT_ENCODING"/>
    </Match>

    <!-- 排除一些可能的内存泄漏警告 -->
    <Match>
        <Bug pattern="ML_SYNC_ON_UPDATED_FIELD"/>
    </Match>

    <!-- 排除一些可能的并发问题警告 -->
    <Match>
        <Bug pattern="VO_VOLATILE_REFERENCE_TO_ARRAY"/>
    </Match>

    <!-- 排除一些可能的设计问题警告 -->
    <Match>
        <Bug pattern="CN_IDIOM_NO_SUPER_CALL"/>
    </Match>

    <!-- 排除一些可能的命名约定警告 -->
    <Match>
        <Bug pattern="NM_FIELD_NAMING_CONVENTION"/>
    </Match>
    <Match>
        <Bug pattern="NM_METHOD_NAMING_CONVENTION"/>
    </Match>

    <!-- 排除一些可能的异常处理警告 -->
    <Match>
        <Bug pattern="REC_CATCH_EXCEPTION"/>
    </Match>

    <!-- 排除一些可能的类型转换警告 -->
    <Match>
        <Bug pattern="BC_UNCONFIRMED_CAST"/>
    </Match>

    <!-- 排除一些可能的数组相关警告 -->
    <Match>
        <Bug pattern="PZLA_PREFER_ZERO_LENGTH_ARRAYS"/>
    </Match>

    <!-- 排除一些可能的集合相关警告 -->
    <Match>
        <Bug pattern="WMI_WRONG_MAP_ITERATOR"/>
    </Match>

    <!-- 排除一些可能的字符串相关警告 -->
    <Match>
        <Bug pattern="VA_FORMAT_STRING_USES_NEWLINE"/>
    </Match>

    <!-- 排除一些可能的数值计算警告 -->
    <Match>
        <Bug pattern="FE_FLOATING_POINT_EQUALITY"/>
    </Match>

    <!-- 排除一些可能的 I/O 相关警告 -->
    <Match>
        <Bug pattern="OS_OPEN_STREAM"/>
    </Match>

    <!-- 排除一些可能的反射相关警告 -->
    <Match>
        <Bug pattern="DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED"/>
    </Match>

</FindBugsFilter>

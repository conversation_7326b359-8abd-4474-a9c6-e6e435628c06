<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    <property name="fileExtensions" value="java"/>
    <property name="localeLanguage" value="en"/>
    <property name="localeCountry" value="US"/>

    <!-- 排除 idip 模块 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]idip[\\/]src[\\/]main[\\/]java[\\/].*$"/>
    </module>
    <!-- 排除 battle 模块-->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]battle[\\/]src[\\/]main[\\/]java[\\/].*$"/>
    </module>
    <!-- 排除 scene 模块 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]scene[\\/]src[\\/]main[\\/]java[\\/].*$"/>
    </module>
    <!-- 排除 codereview 模块 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]codereview[\\/]src[\\/]main[\\/]java[\\/].*$"/>
    </module>
    <!-- 排除 common 模块的 gen 目录 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]common[\\/]src[\\/]main[\\/]gen[\\/].*$"/>
    </module>
    <!-- 排除 yorha-directory 模块的 com.tencent 目录 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]yorha-directory[\\/]src[\\/]main[\\/]java[\\/]com[\\/]tencent[\\/].*$"/>
    </module>
    <!-- 排除 text-filter 模块的 tsssdk.jni 目录 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]text-filter[\\/]src[\\/]main[\\/]java[\\/]tsssdk[\\/]jni[\\/].*$"/>
    </module>
    <!-- 排除 common 模块的 Easy3dNav 目录 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]com[\\/]yorha[\\/]common[\\/]Easy3dNav[\\/].*$"/>
    </module>
    <!-- 排除 common 模块的 qlog 目录 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]com[\\/]yorha[\\/]common[\\/]qlog[\\/].*$"/>
    </module>
    <!-- 排除 game 模块的 tdr 目录 -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*[\\/]com[\\/]tencent[\\/]tdr[\\/].*$"/>
    </module>
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*PlayerCampaignComponent.java"/>
    </module>
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*PlayerMailComponent.java"/>
    </module>
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value=".*RegionalAreaSettingTemplate.java"/>
    </module>

    <!-- 文件级别检查 -->
    <module name="LineLength">
        <property name="fileExtensions" value="java"/>
        <property name="max" value="280"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://|^.*//.*$|^\s*\/\*.*$|^\s*\*.*$|^\s*\/\/.*$"/>
    </module>

    <module name="TreeWalker">
        <!-- 基本代码块检查 -->
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>

        <!-- 基本编码检查 -->
        <module name="OneTopLevelClass"/>
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="DefaultComesLast"/>
        <module name="MissingSwitchDefault"/>
        <module name="MultipleVariableDeclarations"/>
        <module name="OneStatementPerLine"/>
        <module name="PackageDeclaration"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="StringLiteralEquality"/>

        <!-- 导入检查 -->
        <!--module name="AvoidStarImport"/-->
        <module name="IllegalImport"/>
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>

        <!-- 基本修饰符检查 -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>

        <!-- 阿里巴巴命名约定 -->
        <module name="ConstantName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <!-- 基本空白检查 -->
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
<!--        <module name="NoWhitespaceBefore"/>-->
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>
    </module>
</module>

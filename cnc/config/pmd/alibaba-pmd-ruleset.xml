<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Alibaba Java Coding Guidelines PMD Rules"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

    <description>阿里巴巴Java开发手册PMD规则集</description>

    <!-- 阿里巴巴 P3C 规则 - 暂时禁用以解决兼容性问题 -->
<!--     <rule ref="rulesets/java/ali-comment.xml" />-->
     <rule ref="rulesets/java/ali-concurrent.xml" />
<!--     <rule ref="rulesets/java/ali-constant.xml" />-->
     <rule ref="rulesets/java/ali-exception.xml" />
     <rule ref="rulesets/java/ali-flowcontrol.xml" />
<!--     <rule ref="rulesets/java/ali-naming.xml" />-->
     <rule ref="rulesets/java/ali-oop.xml" />
     <rule ref="rulesets/java/ali-orm.xml" />
     <rule ref="rulesets/java/ali-other.xml" />
<!--     <rule ref="rulesets/java/ali-set.xml" />-->

    <!-- 基础PMD规则 - 只包含最重要的 -->
    <rule ref="category/java/bestpractices.xml/AvoidUsingHardCodedIP" />
    <rule ref="category/java/bestpractices.xml/CheckResultSet" />
<!--    <rule ref="category/java/bestpractices.xml/UnusedImports" />-->
    <rule ref="category/java/bestpractices.xml/UnusedLocalVariable" />
    <rule ref="category/java/bestpractices.xml/UnusedPrivateField" />
    <rule ref="category/java/bestpractices.xml/UnusedPrivateMethod" />

    <rule ref="category/java/errorprone.xml/AvoidBranchingStatementAsLastInLoop" />
    <rule ref="category/java/errorprone.xml/AvoidDecimalLiteralsInBigDecimalConstructor" />
    <rule ref="category/java/errorprone.xml/AvoidMultipleUnaryOperators" />
    <rule ref="category/java/errorprone.xml/AvoidUsingOctalValues" />
    <rule ref="category/java/errorprone.xml/BrokenNullCheck" />
    <rule ref="category/java/errorprone.xml/CheckSkipResult" />
    <rule ref="category/java/errorprone.xml/ClassCastExceptionWithToArray" />
    <rule ref="category/java/errorprone.xml/DontUseFloatTypeForLoopIndices" />
    <rule ref="category/java/errorprone.xml/EmptyCatchBlock" />
    <rule ref="category/java/errorprone.xml/EmptyFinallyBlock" />
    <rule ref="category/java/errorprone.xml/EmptyIfStmt" />
    <rule ref="category/java/errorprone.xml/EmptyInitializer" />
    <rule ref="category/java/errorprone.xml/EmptyStatementBlock" />
    <rule ref="category/java/errorprone.xml/EmptyStatementNotInLoop" />
    <rule ref="category/java/errorprone.xml/EmptySwitchStatements" />
    <rule ref="category/java/errorprone.xml/EmptySynchronizedBlock" />
    <rule ref="category/java/errorprone.xml/EmptyTryBlock" />
    <rule ref="category/java/errorprone.xml/EmptyWhileStmt" />

    <rule ref="category/java/multithreading.xml/AvoidThreadGroup" />
    <rule ref="category/java/multithreading.xml/DontCallThreadRun" />
    <rule ref="category/java/multithreading.xml/DoubleCheckedLocking" />

    <rule ref="category/java/performance.xml/BigIntegerInstantiation" />
    <rule ref="category/java/performance.xml/BooleanInstantiation" />

    <rule ref="category/java/security.xml/HardCodedCryptoKey" />
    <rule ref="category/java/security.xml/InsecureCryptoIv" />

</ruleset>

actor:
  - name: IdIpSession
    clazz: com.yorha.cnc.idip.IdIpSessionActor
    isNeedCreateMsg: true
    mailboxQueueSize: 8
    mailboxMaxCount: 1500
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: IdIpSession
  - name: MidasCallbackSession
    clazz: com.yorha.cnc.midasCallbackSession.MidasCallbackSessionActor
    isNeedCreateMsg: false
    mailboxQueueSize: 256
    mailboxMaxCount: 1500
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: MidasCallbackSession
  - name: IMurSession
    clazz: com.yorha.cnc.idip.IMurSessionActor
    isNeedCreateMsg: true
    mailboxQueueSize: 256
    mailboxMaxCount: 1500
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: IMurSession


dispatchers:
  - name: IdIpSession
    type: OneDriveFiberDispatcher
    thoughPut: 1
    keepAliveSec: 0
    parallelism: 2
  - name: MidasCallbackSession
    type: OneDriveFiberDispatcher
    thoughPut: 1
    keepAliveSec: 0
    parallelism: 2
  - name: IMurSession
    type: OneDriveFiberDispatcher
    thoughPut: 1
    keepAliveSec: 0
    parallelism: 2
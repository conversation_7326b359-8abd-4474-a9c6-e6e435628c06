actor:
  - name: Translator
    clazz: com.yorha.cnc.translator.TranslatorActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1000
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Translator

  - name: GroupChat
    clazz: com.yorha.cnc.groupchat.GroupChatActor
    isNeedCreateMsg: false
    mailboxQueueSize: 256
    mailboxMaxCount: 6000
    mail: com.yorha.common.actor.mailbox.ActorRWLockMailboxImpl$LimitLockFactory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: GroupChat

  - name: PlayerCard
    clazz: com.yorha.cnc.playercard.PlayerCardActor
    isNeedCreateMsg: false
    mailboxQueueSize: 50000
    mailboxMaxCount: 10
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: PlayerCard

  - name: Clan<PERSON>ard
    clazz: com.yorha.cnc.clancard.ClanCardActor
    isNeedCreateMsg: false
    mailboxQueueSize: 50000
    mailboxMaxCount: 10
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: ClanCard

  - name: ZoneCard
    clazz: com.yorha.cnc.zoneCard.ZoneCardActor
    isNeedCreateMsg: true
    mailboxQueueSize: 2000
    mailboxMaxCount: 150
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: ZoneCard

dispatchers:
  - name: Translator
    type: OneDriveFiberDispatcher
    thoughPut: 100
    keepAliveSec: 10
    parallelism: 1
  - name: GroupChat
    type: OneDriveFiberDispatcher
    thoughPut: 10
    keepAliveSec: 10
    parallelism: 4
  - name: PlayerCard
    type: OneDriveFiberDispatcher
    thoughPut: 100
    keepAliveSec: 10
    parallelism: 2
  - name: ClanCard
    type: OneDriveFiberDispatcher
    thoughPut: 100
    keepAliveSec: 10
    parallelism: 2
  - name: ZoneCard
    type: OneDriveFiberDispatcher
    thoughPut: 100
    keepAliveSec: 10
    parallelism: 1
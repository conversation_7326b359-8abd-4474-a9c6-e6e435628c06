actor:
  - name: Gate
    clazz: com.yorha.cnc.gate.GateActor
    isNeedCreateMsg: true
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Gate

  - name: GateSession
    clazz: com.yorha.cnc.gate.session.GateSessionActor
    isNeedCreateMsg: true
    mailboxQueueSize: 1024
    mailboxMaxCount: 16000
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: GateSession

  - name: ZoneChat
    clazz: com.yorha.cnc.zonechat.ZoneChatActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: ZoneChat

  - name: Player
    clazz: com.yorha.cnc.player.PlayerActor
    isNeedCreateMsg: false
    mailboxQueueSize: 256
    mailboxMaxCount: 30000
    mail: com.yorha.common.actor.mailbox.ActorRWLockMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Player

  - name: Clan
    clazz: com.yorha.cnc.clan.ClanActor
    isNeedCreateMsg: false
    mailboxQueueSize: 5120
    mailboxMaxCount: 10000
    mail: com.yorha.common.actor.mailbox.ActorRWLockMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Clan

  - name: Scene
    clazz: com.yorha.cnc.scene.SceneActor
    isNeedCreateMsg: false
    mailboxQueueSize: 40000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.cnc.scene.SceneActorMiddlewareImpl
    dispatcher: Scene


  - name: Rank
    clazz: com.yorha.cnc.rank.RankActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Rank

  - name: Name
    clazz: com.yorha.cnc.name.NameActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Name

  - name: AoiView
    clazz: com.yorha.common.aoiView.AoiViewActor
    isNeedCreateMsg: true
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: AoiView

  - name: PathFinding
    clazz: com.yorha.cnc.scene.pathfinding.PathFindingActor
    isNeedCreateMsg: true
    mailboxQueueSize: 20000
    mailboxMaxCount: 100
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: PathFinding

  - name: MidasAgent
    clazz: com.yorha.cnc.midasAgent.MidasAgentActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: MidasAgent

  - name: TextFilter
    clazz: com.yorha.cnc.textFilter.TextFilterActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: TextFilter

  - name: Auth
    clazz: com.yorha.cnc.auth.AuthActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: Auth

  - name: PushNotification
    clazz: com.yorha.cnc.pushNotification.PushNotificationActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: PushNotification

  - name: AntiAddiction
    clazz: com.yorha.cnc.antiaddiction.AntiAddictionActor
    isNeedCreateMsg: false
    mailboxQueueSize: 20000
    mailboxMaxCount: 1
    mail: com.yorha.common.actor.mailbox.ActorMailboxImpl$Factory
    middleware: com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl
    dispatcher: AntiAddiction

dispatchers:
  - name: Gate
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 1
  - name: GateSession
    type: OneDriveFiberDispatcher
    thoughPut: 10
    keepAliveSec: 60
    parallelism: 2
  - name: ZoneChat
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 1
  - name: Player
    type: OneDriveFiberDispatcher
    thoughPut: 10
    keepAliveSec: 10
    parallelism: 2
  - name: Clan
    type: OneDriveFiberDispatcher
    thoughPut: 10
    keepAliveSec: 10
    parallelism: 2
  - name: Rank
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 1
  - name: Name
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 1
  - name: Scene
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 1
  - name: PathFinding
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 2
  - name: AoiView
    type: OneDriveFiberDispatcher
    thoughPut: 0
    keepAliveSec: 600
    parallelism: 1
  - name: MidasAgent
    type: ThreadPoolDispatcher
    thoughPut: 0
    keepAliveSec: 600 # ThreadPoolDispatcher keepAliveSec配置不生效
    parallelism: 1
  - name: TextFilter
    type: ThreadPoolDispatcher
    thoughPut: 0
    keepAliveSec: 600 # ThreadPoolDispatcher keepAliveSec配置不生效
    parallelism: 1
  - name: Auth
    type: ThreadPoolDispatcher
    thoughPut: 0
    keepAliveSec: 600 # ThreadPoolDispatcher keepAliveSec配置不生效
    parallelism: 1
  - name: PushNotification
    type: ThreadPoolDispatcher
    thoughPut: 0
    keepAliveSec: 600 # ThreadPoolDispatcher keepAliveSec配置不生效
    parallelism: 1
  - name: AntiAddiction
    type: ThreadPoolDispatcher
    thoughPut: 0
    keepAliveSec: 600 # ThreadPoolDispatcher keepAliveSec配置不生效
    parallelism: 1
<?xml version="1.0" encoding="UTF-8"?>
<!-- test环境、线上环境 -->
<configuration status="warn" monitorInterval="0" shutdownHook="disable">
    <properties>
        <!-- windows环境下需要此变量 -->
        <property name="LOG_HOME">${sys:LOG_ROOT}</property>
    </properties>
    <appenders>
        <!-- 定义控制台输出 -->
        <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
            <PatternLayout
                    pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%X{actorId}][%file:%line] - %msg%n"/>
            <!-- 过滤掉带有 NO_CONSOLE 标记的日志 -->
            <MarkerFilter marker="NO_CONSOLE" onMatch="DENY" onMismatch="ACCEPT"/>
        </Console>
        <!-- 游戏日志 -->
        <RollingRandomAccessFile name="FileAppender" fileName="${LOG_HOME}/gamesvr"
                                 filePattern="${LOG_HOME}/gamesvr.%d{yyyy-MM-dd-HH}.%i.log"
                                 immediateFlush="true">
            <PatternLayout
                    pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%X{actorId}][%file:%line] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
        <!-- 性能统计日志 -->
        <RollingRandomAccessFile name="PerfStatAppender" fileName="${LOG_HOME}/perf_stat_log"
                                 filePattern="${LOG_HOME}/perf_stat_log.%d{yyyy-MM-dd-HH}.%i.log"
                                 immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
        <!-- 经分流水日志 -->
        <RollingRandomAccessFile name="QlogFlowAppender" fileName="${LOG_HOME}/qlog_flow_log"
                                 filePattern="${LOG_HOME}/qlog_flow_log.%d{yyyy-MM-dd-HH}.%i.log"
                                 immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
        <!-- 业务模块-ai模块日志 -->
        <RollingRandomAccessFile name="AiAppender" fileName="${LOG_HOME}/ai.log"
                                 filePattern="${LOG_HOME}/ai.%d{yyyy-MM-dd-HH}.%i.log" immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="20"/>
        </RollingRandomAccessFile>
        <!-- 战斗日志 -->
        <RollingRandomAccessFile name="BattleLogAppender" fileName="${LOG_HOME}/battle_log"
                                 filePattern="${LOG_HOME}/battle_log.%d{yyyy-MM-dd-HH}.%i.log" immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <!-- 控制一下容量 -->
            <DefaultRolloverStrategy max="20"/>
        </RollingRandomAccessFile>
    </appenders>
    <loggers>
        <!-- 3party Loggers -->
        <Logger name="org.springframework" level="warn">
        </Logger>
        <Logger name="io.netty" level="warn">
        </Logger>
        <Logger name="org.apache.http" level="warn">
        </Logger>
        <Logger name="org.apache.commons" level="warn">
        </Logger>
        <Logger name="com.mchange.v2" level="warn">
        </Logger>
        <Logger name="org.reflections" level="warn">
        </Logger>
        <Logger name="io.etcd" level="warn">
        </Logger>
        <Logger name="java.sql" level="warn">
        </Logger>
        <Logger name="io.jaegertracing" level="warn">
        </Logger>
        <Logger name="com.tencent.tcaplus" level="warn">
        </Logger>
        <Logger name="io.grpc" level="warn">
        </Logger>
        <Logger name="org.mongodb" level="warn">
        </Logger>
        <Logger name="PerfStatLog" level="info" additivity="false">
            <appender-ref ref="PerfStatAppender"/>
        </Logger>
        <Logger name="QlogFlowLog" additivity="false">
            <appender-ref ref="QlogFlowAppender"/>
        </Logger>
        <Logger name="AiLog" level="info" additivity="false">
            <appender-ref ref="AiAppender"/>
        </Logger>
        <Logger name="BattleLog" level="info" additivity="false">
            <appender-ref ref="BattleLogAppender"/>
        </Logger>
        <!-- Root Logger -->
        <Root level="info" includeLocation="true">
            <appender-ref ref="FileAppender"/>
            <appender-ref ref="ConsoleAppender"/>
        </Root>
    </loggers>
    <Filters>
        <DynamicThresholdFilter key="debugLog" onMatch="ACCEPT" onMismatch="NEUTRAL">
            <KeyValuePair key="true" value="DEBUG"/>
        </DynamicThresholdFilter>
    </Filters>
</configuration>
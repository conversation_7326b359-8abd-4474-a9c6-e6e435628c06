//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.tencent.tcaplus.message.impl;

import com.tencent.tcaplus.common.BaseCfg;
import com.tencent.tcaplus.message.MessageBuffer;
import com.tencent.tcaplus.util.ObjectPool;
import com.tencent.tcaplus.util.TCaplusUtil;
import com.tencent.tdr.tcapdir_protocol_cs.TCapdirCSPkg;
import com.tencent.tdr.tcaplus_protocol_cs.TCaplusPkg;
import com.tencent.tdr.tsf4g.TdrException;
import com.yorha.common.server.ServerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.BufferOverflowException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

public class MessageBufferImpl implements MessageBuffer {
    private boolean isOutBuf;
    private ByteBuffer[] packets = new ByteBuffer[1];
    private final List<ByteBuffer> drainPackets = new ArrayList();
    private short magic;
    private short version;
    private int seq;
    private int cmd;
    private int limit;
    private int position;
    private int capacity;
    private int packetCount;
    ByteBufferPool byteBufferPool;
    ObjectPool<byte[]> byteArrayPool;
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageBufferImpl.class);

    public MessageBufferImpl(ByteBufferPool byteBufferPool, ObjectPool<byte[]> byteArrayPool) {
        this.byteBufferPool = byteBufferPool;
        this.byteArrayPool = byteArrayPool;
        this.reset();
    }

    public void resize(int size) {
        this.resetByteBuffer();
        this.isOutBuf = false;
        if (size > 0) {

            // yorha-fix
            if (!ServerContext.isProdSvr()) {
                this.isOutBuf = true;
                int blockSize = BaseCfg.memBlkSize;
                this.packetCount = (size + blockSize - 1) / blockSize;
                this.packets = new ByteBuffer[this.packetCount];
                for (int i = 0; i < this.packetCount; i++) {
                    this.packets[i] = ByteBuffer.allocate(blockSize);
                }
                this.capacity = this.packetCount * blockSize;
                this.limit = size;
                if (this.limit != this.capacity) {
                    this.packets[this.packetCount - 1].limit(this.limit % blockSize);
                }
                return;
            }

            int blockSize = this.byteBufferPool.getBlockSize();
            this.packetCount = (size + blockSize - 1) / blockSize;
            if (this.packetCount == 1) {
                this.packets[0] = (ByteBuffer) this.byteBufferPool.take();
            } else {
                this.byteBufferPool.drainTo(this.drainPackets, this.packetCount);
                if (this.packets.length < this.packetCount) {
                    this.packets = new ByteBuffer[this.packetCount];
                }

                this.drainPackets.toArray(this.packets);
                this.drainPackets.clear();
            }

            this.capacity = this.packetCount * blockSize;
            this.limit = size;
            if (this.limit != this.capacity) {
                this.packets[this.packetCount - 1].limit(this.limit % blockSize);
            }

        }
    }

    public void resize(byte[] buf) {
        this.resetByteBuffer();
        this.isOutBuf = true;
        this.packetCount = 1;
        this.packets[0] = ByteBuffer.wrap(buf);
        this.capacity = this.packets[0].capacity();
        this.limit = this.capacity;
    }

    public void packet(TCaplusPkg pkg, int pkgVersion) throws TdrException {
        byte[] buf;

        // yorha-fix
        if (!ServerContext.isProdSvr()) {
            // 2MB
            buf = new byte[1024 * 1024 * 2];
            this.packet(pkg, pkgVersion, buf);
            return;
        }

        if (TCaplusUtil.isInnerCmd(pkg.Head.Cmd)) {
            buf = new byte[512];
            this.packet(pkg, pkgVersion, buf);
        } else {
            buf = (byte[]) this.byteArrayPool.take();

            try {
                this.packet(pkg, pkgVersion, buf);
            } finally {
                this.byteArrayPool.offer(buf);
            }
        }

    }

    public void packet(TCaplusPkg pkg, int pkgVersion, byte[] packetBuf) throws TdrException {
        this.setCmd(pkg.Head.Cmd);
        this.setSeq(pkg.Head.Seq);
        if (pkgVersion == 0 || pkgVersion > 109) {
            pkgVersion = 109;
        }

        pkg.Head.Version = (short) pkgVersion;
        this.setVersion(pkg.Head.Version);
        this.setMagic(pkg.Head.Magic);
        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace("packet TCaplusPkg, pkg=" + pkg.visualize(0, '\n'));
        }

        this.packet(packetBuf, 0, pkg.pack(packetBuf, packetBuf.length, 0), TCaplusUtil.isInnerCmd(pkg.Head.Cmd));
    }

    public void packet(TCapdirCSPkg pkg, int pkgVersion) throws TdrException {
        this.setCmd(pkg.Head.Cmd);
        if (pkgVersion == 0 || pkgVersion > 107) {
            pkgVersion = 107;
        }

        pkg.Head.Version = (short) pkgVersion;
        this.setVersion(pkg.Head.Version);
        this.setMagic(pkg.Head.Magic);
        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace("packet TCapdirCSPkg, pkg=" + pkg.visualize(0, '\n'));
        }

        byte[] buf = new byte[512];
        this.packet(buf, 0, pkg.pack(buf, buf.length, 0), true);
    }

    public void unpack(TCaplusPkg pkg) throws TdrException {
        byte[] buf = null;

        try {
            if (this.getPacketsCount() == 1) {
                pkg.unpack(this.getPackets()[0].array(), this.limit(), this.getVersion());
            } else {
                // yorha-fix
                if (ServerContext.isProdSvr()) {
                    buf = (byte[]) this.byteArrayPool.take();
                    if (this.limit() > buf.length) {
                        buf = new byte[this.limit()];
                    }
                } else {
                    buf = new byte[this.limit()];
                }

                this.get(buf);
                pkg.unpack(buf, this.position(), this.getVersion());
            }

            if (LOGGER.isTraceEnabled()) {
                LOGGER.trace("unpacked TCaplusPkg, pkg={}", pkg.visualize(0, '\n'));
            }
        } finally {
            // yorha-fix
            if (buf != null && ServerContext.isProdSvr()) {
                this.byteArrayPool.offer(buf);
            }

        }

    }

    public void unpack(TCapdirCSPkg pkg) throws TdrException {
        // yorha-fix
        byte[] buf = null;
        if (ServerContext.isProdSvr()) {
            buf = (byte[]) this.byteArrayPool.take();
        } else {
            buf = new byte[1024 * 1024];
        }

        try {
            this.get(buf);
            pkg.unpack(buf, this.position(), this.getVersion());
            if (LOGGER.isTraceEnabled()) {
                LOGGER.trace("unpacket TCapdirCSPkg, pkg=" + pkg.visualize(0, '\n'));
            }
        } finally {
            if (ServerContext.isProdSvr()) {
                this.byteArrayPool.offer(buf);
            }
        }

    }

    public ByteBuffer[] getPackets() {
        return this.packets;
    }

    public int getPacketsCount() {
        return this.packetCount;
    }

    public int limit() {
        return this.limit;
    }

    public int position() {
        return this.position;
    }

    public void position(int position) {
        this.position = position;
    }

    public boolean hasRemaining() {
        return this.position != this.limit;
    }

    public void flip() {
        for (int i = 0; i < this.packetCount; ++i) {
            this.packets[i].flip();
        }

        this.limit = this.position;
        this.position = 0;
    }

    public void setMagic(short magic) {
        this.magic = magic;
    }

    public short getMagic() {
        return this.magic;
    }

    public void setCmd(int cmd) {
        this.cmd = cmd;
    }

    public int getCmd() {
        return this.cmd;
    }

    public void setVersion(short version) {
        this.version = version;
    }

    public short getVersion() {
        return this.version;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public int getSeq() {
        return this.seq;
    }

    public void packet(byte[] buffer, int offset, int length, boolean needAlloc) {
        if (needAlloc) {
            this.resize(new byte[length]);
        } else {
            this.resize(length);
        }

        this.put(buffer, offset, length);
    }

    public MessageBuffer put(byte[] src) {
        return this.put(src, 0, src.length);
    }

    public MessageBuffer put(byte[] src, int offset, int length) {
        if (this.limit() - this.position() < length) {
            throw new BufferOverflowException();
        } else {
            for (int i = 0; i < this.packetCount && length > 0; ++i) {
                ByteBuffer buffer = this.packets[i];
                if (buffer.hasRemaining()) {
                    int writeCount = Math.min(buffer.remaining(), length);
                    buffer.put(src, offset, writeCount);
                    offset += writeCount;
                    length -= writeCount;
                    this.position += writeCount;
                }
            }

            return this;
        }
    }

    public void reset() {
        this.resetByteBuffer();
        this.setMagic((short) -1);
        this.setVersion((short) -1);
        this.setCmd(-1);
        this.setSeq(-1);
    }

    private void resetByteBuffer() {
        for (int i = 0; i < this.packetCount; ++i) {
            if (!this.isOutBuf) {
                this.byteBufferPool.offer(this.packets[i]);
            }

            this.packets[i] = null;
        }

        this.packetCount = 0;
        this.position = 0;
        this.capacity = 0;
        this.limit = 0;
    }

    public MessageBuffer get(byte[] byteArray) {
        if (this.limit() > byteArray.length) {
            throw new BufferOverflowException();
        } else {
            int offset = 0;

            for (int i = 0; i < this.packetCount; ++i) {
                ByteBuffer buffer = this.packets[i];
                buffer.get(byteArray, offset, buffer.limit());
                offset += buffer.limit();
            }

            this.position = this.limit();
            return this;
        }
    }

    public int capacity() {
        return this.capacity;
    }

    public String toString() {
        return String.format("[magic=%d version=%d cmd=%d seq=%d position=%d limit=%d capacity=%d]", this.getMagic(), this.getVersion(), this.getCmd(), this.getSeq(), this.position(), this.limit(), this.capacity());
    }

    public boolean decodeHead(ByteBuffer headBuffer) {
        this.magic = headBuffer.getShort(0);
        int headLen;
        int bodyLen;
        if (this.magic == 30019) {
            this.version = headBuffer.getShort(2);
            headLen = headBuffer.getInt(4);
            bodyLen = headBuffer.getInt(8);
            headBuffer.getLong(12);
            this.seq = headBuffer.getInt(20);
            this.cmd = headBuffer.getInt(24);
        } else {
            if (this.magic != -27275) {
                LOGGER.error("receive invalid magic, magic=" + this.magic);
                return false;
            }

            this.cmd = headBuffer.getShort(2);
            this.version = headBuffer.getShort(2);
            headLen = headBuffer.getShort(6);
            bodyLen = headBuffer.getInt(8);
        }

        this.resize(headLen + bodyLen);
        return true;
    }
}

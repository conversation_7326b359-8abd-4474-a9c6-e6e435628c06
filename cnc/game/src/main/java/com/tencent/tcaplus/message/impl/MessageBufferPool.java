//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.tencent.tcaplus.message.impl;

import com.tencent.tcaplus.common.BaseCfg;
import com.tencent.tcaplus.message.MessageBuffer;
import com.tencent.tcaplus.util.ObjectPool;
import com.tencent.tcaplus.util.TCaplusUtil;
import com.tencent.tdr.tcapdir_protocol_cs.TCapdirCSPkg;
import com.tencent.tdr.tcaplus_protocol_cs.TCaplusPkg;
import com.tencent.tdr.tsf4g.TdrException;
import com.yorha.common.server.ServerContext;

public class MessageBufferPool extends ObjectPool<MessageBuffer> {
    private ByteBufferPool byteBufferPool;
    public ObjectPool<byte[]> byteArrayPool;

    public MessageBufferPool(int capaticy, int memSize, final int packBufferSize) {
        super(capaticy);
        // yorha-fix
        if (ServerContext.isProdSvr()) {
            this.byteBufferPool = new ByteBufferPool((memSize + BaseCfg.memBlkSize - 1) / BaseCfg.memBlkSize, BaseCfg.memBlkSize);
            this.byteArrayPool = new ObjectPool<byte[]>(BaseCfg.tdrPackBufferCnt) {
                protected byte[] allocate() {
                    return new byte[packBufferSize];
                }
            };
        }
    }

    protected MessageBufferImpl allocate() {
        return new MessageBufferImpl(this.byteBufferPool, this.byteArrayPool);
    }

    public boolean offer(MessageBuffer messageBuffer) {
        messageBuffer.reset();
        return super.offer(messageBuffer);
    }

    public ByteBufferPool getByteBufferPool() {
        return this.byteBufferPool;
    }

    public MessageBuffer packMsgBuffer(TCaplusPkg pkg) throws TdrException {
        Object msg;
        if (!ServerContext.isProdSvr()) {
            // yorha-fix
            msg = this.allocate();
        } else {
            if (TCaplusUtil.isInnerCmd(pkg.Head.Cmd)) {
                msg = this.allocate();
            } else {
                msg = (MessageBuffer) this.take();
            }
        }


        try {
            ((MessageBuffer) msg).packet(pkg, 0);
            ((MessageBuffer) msg).flip();
            return (MessageBuffer) msg;
        } catch (TdrException var4) {
            if (!TCaplusUtil.isInnerCmd(pkg.Head.Cmd)) {
                this.offer((MessageBuffer) msg);
            }

            throw var4;
        }
    }

    public MessageBuffer packMsgBuffer(TCapdirCSPkg pkg) throws TdrException {
        MessageBuffer msg = this.allocate();
        msg.packet(pkg, 0);
        msg.flip();
        return msg;
    }

    public static MessageBufferPool create() {
        return new MessageBufferPool(BaseCfg.msgBufferCnt, BaseCfg.memBlkSize * BaseCfg.memBlkCnt, BaseCfg.tdrPackBufferSize);
    }
}

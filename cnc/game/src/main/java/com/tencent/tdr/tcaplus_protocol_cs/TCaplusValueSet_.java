//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.tencent.tdr.tcaplus_protocol_cs;

import com.tencent.tdr.tsf4g.*;

public class TCaplusValueSet_ implements TdrCompositeType {
    public int Version_;
    public int FieldNum_;
    public TCaplusValueField_[] Fields_;
    public int EncodeType;
    public CompactValueSet CompactValueSet;
    public static final int BASEVERSION = 1;
    public static final int CURRVERSION = 109;
    public static final int VERSION_EncodeType = 109;
    public static final int VERSION_CompactValueSet = 109;
    // yorha-fix
    public static final int VALUE_FIELD_NUM = 8;

    public TCaplusValueSet_() {
        // yorha-fix
        Fields_ = new TCaplusValueField_[VALUE_FIELD_NUM];
        for (int Fields__i = 0; Fields__i < VALUE_FIELD_NUM; ++Fields__i) {
            this.Fields_[Fields__i] = new TCaplusValueField_();
        }

        this.CompactValueSet = new CompactValueSet();
    }

    @Override
    public void construct() throws TdrException {
    }

    @Override
    public int pack(byte[] buffer, int size, int cutVer) throws TdrException {
        if (null != buffer && 0 != buffer.length && size <= buffer.length) {
            TdrWriteBuf destBuf = new TdrWriteBuf(buffer, size);
            this.pack(destBuf, cutVer);
            return destBuf.getUsedSize();
        } else {
            throw new TdrException(TdrError.TDR_ERR_INVALID_BUFFER_PARAMETER);
        }
    }

    @Override
    public void pack(TdrWriteBuf destBuf, int cutVer) throws TdrException {
        if (0 == cutVer || 109 < cutVer) {
            cutVer = 109;
        }

        if (1 > cutVer) {
            throw new TdrException(TdrError.TDR_ERR_CUTVER_TOO_SMALL);
        } else {
            destBuf.writeInt32(this.Version_);
            destBuf.writeInt32(this.FieldNum_);
            if (VALUE_FIELD_NUM < this.FieldNum_) {
                throw new TdrException(TdrError.TDR_ERR_REFER_SURPASS_COUNT);
            } else if (this.Fields_.length < this.FieldNum_) {
                throw new TdrException(TdrError.TDR_ERR_VAR_ARRAY_CONFLICT);
            } else {
                for (int Fields__i = 0; Fields__i < this.FieldNum_; ++Fields__i) {
                    this.Fields_[Fields__i].pack(destBuf, cutVer);
                }

                if (109 <= cutVer) {
                    destBuf.writeInt32(this.EncodeType);
                }

                if (109 <= cutVer) {
                    this.CompactValueSet.pack(destBuf, cutVer);
                }

            }
        }
    }

    @Override
    public long getMaxNetSize() throws TdrException {
        return 43762772L;
    }

    @Override
    public int unpack(byte[] buffer, int size, int cutVer) throws TdrException {
        if (null != buffer && 0 != buffer.length && size <= buffer.length) {
            TdrReadBuf srcBuf = new TdrReadBuf(buffer, size);
            this.unpack(srcBuf, cutVer);
            return srcBuf.getUsedSize();
        } else {
            throw new TdrException(TdrError.TDR_ERR_INVALID_BUFFER_PARAMETER);
        }
    }

    @Override
    public void unpack(TdrReadBuf srcBuf, int cutVer) throws TdrException {
        if (0 == cutVer || 109 < cutVer) {
            cutVer = 109;
        }

        if (1 > cutVer) {
            throw new TdrException(TdrError.TDR_ERR_CUTVER_TOO_SMALL);
        } else {
            this.Version_ = srcBuf.readInt32();
            this.FieldNum_ = srcBuf.readUInt32();
            if (VALUE_FIELD_NUM < this.FieldNum_) {
                throw new TdrException(TdrError.TDR_ERR_REFER_SURPASS_COUNT);
            } else {
                int Fields__i;
                if (this.Fields_.length < this.FieldNum_) {
                    this.Fields_ = new TCaplusValueField_[this.FieldNum_];

                    for (Fields__i = 0; Fields__i < this.FieldNum_; ++Fields__i) {
                        this.Fields_[Fields__i] = new TCaplusValueField_();
                    }
                }

                for (Fields__i = 0; Fields__i < this.FieldNum_; ++Fields__i) {
                    this.Fields_[Fields__i].unpack(srcBuf, cutVer);
                }

                if (109 <= cutVer) {
                    this.EncodeType = srcBuf.readInt32();
                } else {
                    this.EncodeType = 0;
                }

                if (109 <= cutVer) {
                    this.CompactValueSet.unpack(srcBuf, cutVer);
                } else {
                    this.CompactValueSet.construct();
                }

            }
        }
    }

    @Override
    public String visualize(int indent, char separator) throws TdrException {
        TdrVisualBuf destBuf = new TdrVisualBuf();
        this.visualize(destBuf, indent, separator);
        return destBuf.getVisualBuf();
    }

    @Override
    public void visualize(TdrVisualBuf destBuf, int indent, char separator) throws TdrException {
        TdrBufUtil.printVariable(destBuf, indent, separator, "[Version_]", "%d", new Object[]{this.Version_});
        TdrBufUtil.printVariable(destBuf, indent, separator, "[FieldNum_]", "%d", new Object[]{this.FieldNum_});
        if (VALUE_FIELD_NUM < this.FieldNum_) {
            throw new TdrException(TdrError.TDR_ERR_REFER_SURPASS_COUNT);
        } else {
            for (int Fields__i = 0; Fields__i < this.FieldNum_; ++Fields__i) {
                if (null != this.Fields_[Fields__i]) {
                    TdrBufUtil.printVariable(destBuf, indent, separator, "[Fields_]", Fields__i, true);
                    if (0 > indent) {
                        this.Fields_[Fields__i].visualize(destBuf, indent, separator);
                    } else {
                        this.Fields_[Fields__i].visualize(destBuf, indent + 1, separator);
                    }
                }
            }

            TdrBufUtil.printVariable(destBuf, indent, separator, "[EncodeType]", "%d", new Object[]{this.EncodeType});
            TdrBufUtil.printVariable(destBuf, indent, separator, "[CompactValueSet]", true);
            if (0 > indent) {
                this.CompactValueSet.visualize(destBuf, indent, separator);
            } else {
                this.CompactValueSet.visualize(destBuf, indent + 1, separator);
            }

        }
    }

    @Override
    public int getSizeInfo(byte[] buffer, int size) throws TdrException {
        if (0 != buffer.length && size <= buffer.length) {
            TdrReadBuf srcBuf = new TdrReadBuf(buffer, size);
            return this.getSizeInfo(srcBuf);
        } else {
            throw new TdrException(TdrError.TDR_ERR_INVALID_BUFFER_PARAMETER);
        }
    }

    public int getSizeInfo(TdrReadBuf srcBuf) throws TdrException {
        throw new TdrException(TdrError.TDR_ERR_HAVE_NOT_SET_SIZEINFO);
    }
}

package com.yorha.game.groovy

import com.yorha.cnc.player.PlayerActor
import com.yorha.cnc.player.PlayerEntity
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.utils.Pair
import com.yorha.common.utils.script.GroovyScript
import com.yorha.game.GroovyScriptTranslator
import com.yorha.game.gen.prop.InnerBuildProp
import com.yorha.game.gen.prop.QueueTaskProp
import com.yorha.proto.CommonEnum
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 *
 */
class RepairPlayerInnerBuildEntity implements GroovyScript {
    private static final Logger LOGGER = LogManager.getLogger(GroovyScriptTranslator.class)

    @Override
    String execute() {
        int zoneId = 1
        ArrayList<Pair<Long, Long>> player = new ArrayList<>()
        player.add(new Pair(3817211114, 3817211142))
        player.add(new Pair(3814838872, 3814981209))
        player.add(new Pair(3799457248, 3813875718))
        player.add(new Pair(3799076954, 3799076982))
        player.add(new Pair(3798200082, 3798202751))

        LOGGER.info("RepairPlayerInnerBuildEntity start player:{} zone:{}", player, zoneId)
        CountDownLatch latch = new CountDownLatch(1)
        String ret = "OK"
        for (i in 0..<player.size()) {
            Pair<Long, Long> playerPair = player.get(i)
            long playerId = playerPair.getFirst()
            long buildId = playerPair.getSecond()
            ActorSendMsgUtils.send(RefFactory.ofPlayer(zoneId, playerId), new ActorRunnable<PlayerActor>("groovy", { playerActor ->
                PlayerEntity entity = playerActor.getOrLoadEntity()
                InnerBuildProp buildProp = entity.getProp().getInnerBuildV(buildId)
                QueueTaskProp queueProp = entity.getPlayerQueueTaskComponent().getQueueTaskProp(CommonEnum.QueueTaskType.CITY_BUILD, buildId)
                if (queueProp == null && buildProp != null && buildProp.getBuildState() == CommonEnum.BuildState.BUILD_UPGRADE) {
                    buildProp.setBuildState(CommonEnum.BuildState.BUILDED)
                    LOGGER.info("RepairPlayerInnerBuildEntity OK")
                }
                latch.countDown()
            }))
        }
        latch.await(50, TimeUnit.SECONDS)
        LOGGER.info("RepairPlayerInnerBuildEntity end")
        return ret
    }
}
package com.yorha.game.shutdowntask;

import com.yorha.common.actor.msg.ActorStoppingMsg;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.server.ServerContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SendStoppingMsg extends AbstractShutdownTask {
    private static final Logger LOGGER = LogManager.getLogger(SendStoppingMsg.class);

    @Override
    public void run(CountDownLatch downLatch) {
        final ActorStoppingMsg msg = new ActorStoppingMsg();
        // 除了集群相关的所有Actor
        final Set<String> set = ServerContext.getActorSystem().getRegistryValue().getLocalActorRoleList()
                .stream()
                .filter(r -> !r.equals(ActorRole.Node.name()) && !r.equals(ActorRole.Leader.name()))
                .collect(Collectors.toSet());
        Map<String, Integer> sendNum = ServerContext.getActorSystem().getRegistryValue().broadcastLocalWithReturn(set, msg);
        downLatch.countDown();
        LOGGER.info("gemini_system shutdown broadcast local actor ActorStoppingMsg {}", sendNum);
    }
}

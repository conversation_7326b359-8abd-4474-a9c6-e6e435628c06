package com.yorha.game.shutdowntask;

import com.yorha.common.server.ServerContext;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class ShutdownPerfLog extends AbstractShutdownTask {
    @Override
    public void run(CountDownLatch downLatch) {
        ServerContext.getProcessPerfLogger().stop();
        ServerContext.getActorPerfLogger().stop();
        downLatch.countDown();
    }
}

package com.yorha.game;

import com.google.gson.JsonSyntaxException;
import com.yorha.common.exception.GeminiException;
import groovy.json.StringEscapeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.io.InputStream;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Scanner;

/**
 * 发送groovy到指定进程
 *
 * <AUTHOR>
 */
public class GroovyScriptTranslator {
    private static final Logger LOGGER = LogManager.getLogger(GroovyScriptTranslator.class);
    private final String ip;
    private final String port;
    private final String scriptAddress;
    private String url;


    /**
     * 构造函数
     *
     * @param ip            服务器ip地址
     * @param port          服务器端口号
     * @param scriptAddress groovy脚本代码地址
     */
    GroovyScriptTranslator(String ip, String port, String scriptAddress) {
        this.ip = ip;
        this.port = port;
        this.scriptAddress = scriptAddress;
    }

    /**
     * 将groovy脚本转换为url
     *
     * @throws IOException URLEncoder.encode失败
     */
    public void init() throws IOException {
        String urlBody = StringEscapeUtils.escapeJava(Files.readString(Paths.get(scriptAddress)));
        url = "http://" + ip + ":" + port + "/cmd?funName=execScriptContent&param={\"script\":\"" + URLEncoder.encode(urlBody, StandardCharsets.UTF_8) + "\"}";
    }

    /**
     * 拿到url对应网页中的内容
     *
     * @param myUrl   网页的url
     * @param charset 网页的字符集
     * @return 网页中的内容
     */
    public static String getContentFromUrl(String myUrl, String charset) throws IOException {
        StringBuilder sb = new StringBuilder();
        URL url;
        url = new URL(myUrl);
        URLConnection conn = url.openConnection();
        InputStream is = conn.getInputStream();
        Scanner sc = new Scanner(is, charset);
        while (sc.hasNextLine()) {
            sb.append(sc.nextLine()).append("\r\n");
        }
        sc.close();
        is.close();
        // 返回信息包含”unable to resolve“,表明服务器未拉取到拥有执行groovy工具类的版本
        if (sb.toString().contains("unable to resolve")) {
            return "error";
        }
        return sb.toString();
    }

    /**
     * 单服务器信息查询
     *
     * @param ip            服务器ip
     * @param port          服务器端口号
     * @param scriptAddress 脚本文件路径
     * @throws IOException g.init()初始化失败
     */
    public static String query(String ip, String port, String scriptAddress) throws IOException, GeminiException {
        GroovyScriptTranslator g = new GroovyScriptTranslator(ip, port, scriptAddress);
        g.init();
        try {
            return getContentFromUrl(g.url, "utf-8");
        } catch (JsonSyntaxException e) {
            return "服务器ip:" + ip + "端口:" + port + "\r\n" + "获取服务器信息失败(服务器无法执行groovy脚本)";
        } catch (ConnectException e) {
            return "服务器ip:" + ip + "端口:" + port + "\r\n" + "获取服务器信息失败(服务器拒绝连接)";
        } catch (SocketTimeoutException e) {
            return "服务器ip:" + ip + "端口:" + port + "\r\n" + "获取服务器信息失败(服务器连接超时)";
        }
    }

    public static void main(String[] args) throws IOException, InterruptedException {
        // 默认值
        String ip = args[0];
        String port = args[1];
        String groovyName = "GetServerCurTime";
        String groovyAddress = System.getProperty("user.dir") + "/cnc/proj/game/src/main/java/com/yorha/game/groovy/" + groovyName + ".groovy";

        if (args.length >= 3) {
            groovyAddress = args[2];
        }

        StringBuilder sb = new StringBuilder();
        sb.append("ip:").append(ip).append("\nport:").append(port).append("\ngroovy:").append(groovyAddress).append("\n");
        LOGGER.info(sb);

        String data = query(ip, port, groovyAddress);
        System.out.print(data);
    }

}

package com.yorha.game.groovy

import com.yorha.cnc.scene.SceneActor
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity
import com.yorha.cnc.scene.city.CityEntity
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.utils.script.GroovyScript

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 获取某个大世界实体
 * <AUTHOR>
 */
class GetBigSceneZoneInfo implements GroovyScript {

    static String getZoneInfoProp(BigSceneEntity bigSceneEntity) {
        return bigSceneEntity.getZoneEntity().getProp().toString()
    }

    static String getCityNum(BigSceneEntity bigSceneEntity) {
        return bigSceneEntity.getObjMgrComponent().getObjsNumByType(CityEntity.class)
    }

    @Override
    String execute() {
        int zoneId = 1

        CountDownLatch latch = new CountDownLatch(1)
        String ret = "wrong"

        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), new ActorRunnable<SceneActor>("groovy", { sceneActor ->
            BigSceneEntity scene = sceneActor.getBigScene()
            ret = getCityNum(scene)
            latch.countDown()
        }))

        latch.await(10, TimeUnit.SECONDS)
        return ret
    }
}
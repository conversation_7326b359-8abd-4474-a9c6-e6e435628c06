package com.yorha.game.shutdowntask;

import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.server.ServerContext;
import com.yorha.common.actor.msg.ActorDestroyMsg;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class WaitSceneDestroy extends AbstractShutdownTask {
    private static final Logger LOGGER = LogManager.getLogger(WaitGateDestroy.class);
    private final long waitMs;

    public WaitSceneDestroy(long waitMs) {
        this.waitMs = waitMs;
    }

    @Override
    public void run(CountDownLatch downLatch) {
        boolean isSceneDestroy = ServerContext.getActorSystem().getRegistryValue().isAllActorDestroy(ActorRole.Scene.name());
        if (isSceneDestroy) {
            downLatch.countDown();
            LOGGER.info("gemini_system shutdown scene destroy ok");
            return;
        }
        final ActorDestroyMsg msg = new ActorDestroyMsg("server shutdown");
        long start = SystemClock.nowNative();
        while (true) {
            LOGGER.info("gemini_system shutdown scene destroy waiting...");
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {

            }
            isSceneDestroy = ServerContext.getActorSystem().getRegistryValue().isAllActorDestroy(ActorRole.Scene.name());
            if (isSceneDestroy) {
                LOGGER.info("gemini_system shutdown scene destroy ok");
                break;
            }
            // 重新再发销毁消息
            Set<String> set = Stream.of(ActorRole.Scene.name()).collect(Collectors.toSet());
            ServerContext.getActorSystem().getRegistryValue().broadcastLocal(set, msg);

            long cur = SystemClock.nowNative();
            if (cur - start > waitMs) {
                // 如果还没断联干净，那就强硬处理
                LOGGER.error("gemini_system shutdown scene destroy fail can not wait any more.");
                break;
            }
        }
        downLatch.countDown();
    }
}
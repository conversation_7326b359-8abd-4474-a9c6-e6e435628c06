package com.yorha.game.groovy

import com.yorha.cnc.scene.SceneActor
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.utils.JolUtils
import com.yorha.common.utils.script.GroovyScript

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 获取某个场景玩家实体
 * <AUTHOR>
 */
class GetScenePlayerEntity implements GroovyScript {

    private static final long playerId = 40505668

    static String getProp(ScenePlayerEntity scenePlayer) {
        return scenePlayer.getProp().toString()
    }


    static String getMemorySize(ScenePlayerEntity scenePlayer) {
        return JolUtils.getObjMSize(scenePlayer, scenePlayer.JOL_EXCLUDE) + "KB"
    }

    static String getMemoryDetail(ScenePlayerEntity scenePlayer) {
        return JolUtils.getObjMTreeInfo(scenePlayer, scenePlayer.JOL_EXCLUDE, 2, 10)
    }

    @Override
    String execute() {
        int zoneId = 1
        CountDownLatch latch = new CountDownLatch(1)
        String ret = "wrong"
        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), new ActorRunnable<SceneActor>("groovy", { sceneActor ->
            BigSceneEntity scene = sceneActor.getBigScene()
            ScenePlayerEntity scenePlayer = scene.getPlayerMgrComponent().getScenePlayer(playerId)
            ret = getProp(scenePlayer)
            latch.countDown()
        }))
        latch.await(10, TimeUnit.SECONDS)
        return ret
    }
}
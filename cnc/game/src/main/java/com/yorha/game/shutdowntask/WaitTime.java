package com.yorha.game.shutdowntask;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class WaitTime extends AbstractShutdownTask {
    private static final Logger LOGGER = LogManager.getLogger(WaitTime.class);
    private final long waitMs;

    public WaitTime(long waitMs) {
        this.waitMs = waitMs;
    }

    @Override
    public void run(CountDownLatch downLatch) {
        try {
            Thread.sleep(waitMs);
        } catch (InterruptedException e) {
            LOGGER.error("gemini_system shutdown execute task={} ", this, e);
        }
        downLatch.countDown();
    }
}

package com.yorha.game;

import com.yorha.common.actorservice.PatchInterface;

import java.util.Map;

/**
 * 热更预留函数
 */
public class GamePatchImpl implements PatchInterface {

    @Override
    public Map<Object, Object> patchMethod(String str, Map<Object, Object> map) {
        if (str == null) {
            return null;
        }
        if ("参考".equals(str)) {
            return null;
        }
        return null;
    }

}

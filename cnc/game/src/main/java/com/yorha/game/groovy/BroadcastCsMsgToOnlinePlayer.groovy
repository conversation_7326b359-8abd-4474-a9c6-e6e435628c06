package com.yorha.game.groovy

import com.yorha.common.helper.BroadcastHelper
import com.yorha.common.helper.MsgHelper
import com.yorha.common.io.MsgType
import com.yorha.common.utils.script.GroovyScript
import com.yorha.proto.PlayerScene
import com.yorha.proto.StructPB

/**
 * 给确定小服的所有在线玩家发送消息。
 *
 * <AUTHOR>
 */
class BroadcastCsMsgToOnlinePlayer implements GroovyScript {
    @Override
    String execute() {
        PlayerScene.Player_MarqueeMessage_NTF msg = MsgHelper.buildMarqueeMsg(1, StructPB.DisplayDataPB.newBuilder().build());
        BroadcastHelper.toCsOnlinePlayerInZone(1, MsgType.PLAYER_MARQUEEMESSAGE_NTF, msg);
        return "ok";
    }
}

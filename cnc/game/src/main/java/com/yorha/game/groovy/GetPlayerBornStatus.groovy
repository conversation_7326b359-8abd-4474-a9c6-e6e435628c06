package com.yorha.game.groovy

import com.yorha.cnc.scene.SceneActor
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.utils.script.GroovyScript

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 */
class GetPlayerBornStatus implements GroovyScript {

    @Override
    String execute() {
        int zoneId = 1
        CountDownLatch latch = new CountDownLatch(1)
        String ret = "wrong"
        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), new ActorRunnable<SceneActor>("groovy", { sceneActor ->
            BigSceneEntity scene = sceneActor.getBigScene()
            ret = scene.getZoneEntity().getProp().getLanguageLimit().toString()
            latch.countDown()
        }))
        latch.await(2, TimeUnit.SECONDS)
        return ret
    }
}

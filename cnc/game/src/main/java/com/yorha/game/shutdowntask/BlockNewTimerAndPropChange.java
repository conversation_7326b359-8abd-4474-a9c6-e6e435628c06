package com.yorha.game.shutdowntask;

import com.yorha.common.server.ServerContext;
import com.yorha.gemini.props.PropertyChangeListener;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class BlockNewTimerAndPropChange extends AbstractShutdownTask {
    @Override
    public void run(CountDownLatch downLatch) {
        ServerContext.setServerStopStep(2);
        PropertyChangeListener.isStop = true;
        downLatch.countDown();
    }
}

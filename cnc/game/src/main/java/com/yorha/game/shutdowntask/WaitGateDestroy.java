package com.yorha.game.shutdowntask;

import com.yorha.cnc.gate.GeminiSessionManager;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorRegistryValue;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.SsGate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class WaitGateDestroy extends AbstractShutdownTask {
    private static final Logger LOGGER = LogManager.getLogger(WaitGateDestroy.class);
    private final long waitMs;

    public WaitGateDestroy(long waitMs) {
        this.waitMs = waitMs;
    }

    @Override
    public void run(CountDownLatch downLatch) {
        if (!ServerContext.isZoneServer()) {
            downLatch.countDown();
            return;
        }
        final ActorRegistryValue registryValue = ServerContext.getActorSystem().getRegistryValue();
        final IActorRef gateRef = RefFactory.ofGate(ServerContext.getZoneId());
        if (!registryValue.isAllActorDestroy(ActorRole.Gate.name())) {
            ActorSendMsgUtils.send(gateRef, SsGate.ShutdownCmd.getDefaultInstance());
            LOGGER.info("gemini_system shutdown handleEventStopNode Shutdown Gate! ref {}", gateRef);
        }
        long start = SystemClock.nowNative();
        while (true) {
            // 自旋等待所有连接断开，判断session数量为0后继续向下
            boolean allDisconnect = GeminiSessionManager.getInstance().isAllDisconnect();
            if (allDisconnect) {
                LOGGER.info("gemini_system shutdown GeminiSessionManager isAllDisconnect ok");
                break;
            }
            LOGGER.info("gemini_system shutdown GeminiSessionManager isAllDisconnect wait...");
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {

            }
            long cur = SystemClock.nowNative();
            if (cur - start > waitMs) {
                // 如果还没断联干净，那就强硬处理
                LOGGER.error("gemini_system shutdown GeminiSessionManager isAllDisconnect fail, can not wait any more.");
                break;
            }
        }
        downLatch.countDown();
    }
}

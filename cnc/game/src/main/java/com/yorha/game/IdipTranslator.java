package com.yorha.game;

import com.yorha.common.utils.PlatformClient;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;

/**
 * 发送idip请求到指定服务器
 *
 * <AUTHOR>
 */
public class IdipTranslator {
    private static volatile IdipTranslator instance = null;
    private static final Logger LOGGER = LogManager.getLogger(IdipTranslator.class);
    private final String requestParam;
    private final String url;
    private static PlatformClient platformClient;

    public static IdipTranslator getInstance(String ip, String port, String md5Key) throws IOException, URISyntaxException {
        if (instance == null) {
            synchronized (IdipTranslator.class) {
                if (instance == null) {
                    instance = new IdipTranslator(ip, port, md5Key);
                }
            }
        }
        return instance;
    }

    private IdipTranslator(String ip, String port, String md5Key) throws IOException, URISyntaxException {
        this.requestParam = initRequestParam();
        String md5 = genMd5(requestParam, md5Key);
        this.url = genUrl(md5);
        platformClient = new IdipPlatformClient("http://" + ip + ":" + port, 10000);
    }

    private static class IdipPlatformClient extends PlatformClient {
        private final String hostName;

        IdipPlatformClient(String hostName, int timeout) {
            super(timeout);
            this.hostName = hostName;
        }

        @Override
        public String getHostName() {
            return hostName;
        }
    }

    private String initRequestParam() throws IOException, URISyntaxException {
        String requestParam;
        try {
            List<String> lines = Files.readAllLines(Paths.get(Objects.requireNonNull(IdipTranslator.class.getResource("/idipRequestConfig.json")).toURI()));
            requestParam = String.join("", lines);
        } catch (IOException | URISyntaxException e) {
            LOGGER.error("Failed to initialize request parameters", e);
            throw e;
        }
        return requestParam;
    }

    private String genMd5(String requestParam, String md5Key) {
        String param = requestParam + md5Key;
        return DigestUtils.md5Hex(param.getBytes(StandardCharsets.UTF_8));
    }

    private String genUrl(String md5) {
        return "/cnc/idip_sign?idip_sign=" + md5;
    }

    private static void sendMsg(String ip, String port, String md5Key) throws IOException, URISyntaxException {
        IdipTranslator n = getInstance(ip, port, md5Key);
        platformClient.post4https(n.url, n.requestParam);
    }

    public static void main(String[] args) throws IOException, URISyntaxException {
        String ip = args[0]; //"************";
        String port = args[1]; //"12031";
        String md5Key = "a82294215a845274578997691a688a23";

        if (args.length >= 3) {
            md5Key = args[2];
        }

        sendMsg(ip, port, md5Key);
    }
}

package com.yorha.game.groovy


import com.yorha.common.utils.script.GroovyScript

/**
 * 获取当前Player的Hash分布。
 * <AUTHOR>
 */
class GetShardPlayerCnt implements GroovyScript {

    @Override
    public String execute() {
//        Map<Integer, List<IActorRef>> refListMap = new HashMap<>();
//        ActorMsgSystem.getDispatcher(ActorRole.Player).forEachActor({ ref ->
//            def region = ServerContext.getActorSystem().getRegistryValue();
//            def metaData = ServerContext.getActorSystem().getActorMetaDataMgr().getActorMetaData(ActorRole.Player.toString());
//            def hashId = region.extractShardId(ref, metaData);
//            refListMap.computeIfAbsent(hashId, { k -> new ArrayList<>() }).add(ref);
//        });
//        StringBuilder sb = new StringBuilder();
//        sb.append(ActorRole.Player.toString() + "####\n");
//        for (def set : refListMap.entrySet()) {
//            sb.append(set.getKey());
//            sb.append(" : ")
//            sb.append(set.getValue().size());
//            sb.append("\n")
//        }
        return sb.toString();
    }
}
package com.yorha.game.shutdowntask;

import com.yorha.common.qlog.QlogManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class ShutdownQLog extends AbstractShutdownTask {
    private static final Logger LOGGER = LogManager.getLogger(ShutdownQLog.class);

    @Override
    public void run(CountDownLatch downLatch) {
        try {
            QlogManager.getInstance().shutdown();
        } catch (InterruptedException e) {
            LOGGER.error("gemini_system shutdown ShutdownQLog failed ", e);
        }
        downLatch.countDown();
    }
}

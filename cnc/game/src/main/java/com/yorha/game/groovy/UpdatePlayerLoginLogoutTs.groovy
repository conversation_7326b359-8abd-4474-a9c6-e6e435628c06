package com.yorha.game.groovy

import com.yorha.cnc.player.PlayerActor
import com.yorha.cnc.player.PlayerEntity
import com.yorha.common.actor.ref.RefFactory
import com.yorha.game.gen.prop.PlayerProp
import com.yorha.common.actor.IActorRef
import com.yorha.common.actor.msg.FutureActorRunnable
import com.yorha.common.utils.script.GroovyScript
import com.yorha.common.utils.time.SystemClock
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

import java.util.concurrent.TimeUnit

/**
 * 更新指定玩家的上次登录与上次登出时间为当前时间
 */
class UpdatePlayerLoginLogoutTs implements GroovyScript {

    private static final Logger LOGGER = LogManager.getLogger(UpdatePlayerLoginLogoutTs.class);

    @Override
    String execute() {
        final int zoneId = 2;
        final long playerId = 916872045;
        final IActorRef player = RefFactory.ofPlayer(zoneId, playerId);

        final FutureActorRunnable<PlayerActor, String> updateLogoutTs = new FutureActorRunnable<>("updateLogoutTs", { actor ->
            updateLogoutTs(actor);
            return "ok";
        }) as FutureActorRunnable<PlayerActor, String>;
        player.tellAndCreate(updateLogoutTs);

        try {
            return updateLogoutTs.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            return e.toString();
        }

    }

    static void updateLogoutTs(PlayerActor actor) {
        final long now = SystemClock.now();
        LOGGER.info("updateLogoutTs update zone={}, p={}, curTsMs={}", actor.getZoneId(), actor.getPlayerId(), now);
        PlayerEntity playerEntity = actor.getOrLoadEntity();
        PlayerProp prop = playerEntity.getProp();
        prop.getBasicInfo().setLastLoginTsMs(now).setLastLogoutTsMs(now);
        playerEntity.getDbComponent().updateSimplePlayerFull();
        LOGGER.info("updateLogoutTs basicInfo={}", prop.getBasicInfo());
    }
}

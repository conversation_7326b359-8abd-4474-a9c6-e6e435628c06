package com.yorha.game.groovy

import com.yorha.cnc.playercard.PlayerCardActor
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.utils.script.GroovyScript
import com.yorha.proto.StructPB

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.function.Consumer

/**
 * <AUTHOR>
 */
class QueryCardCallback implements GroovyScript {

    @Override
    String execute() {
        CountDownLatch latch = new CountDownLatch(1)
        String ret = "error"
        ActorSendMsgUtils.send(RefFactory.ofPlayerCardActor(0), new ActorRunnable<PlayerCardActor>("groovy", { playerCardActor ->
            List<Consumer<StructPB.PlayerCardInfoPB>> list = playerCardActor.getPlayerCardService().cache.cbList.get(611459226L)
            if (list == null) {
                ret = "0"
            } else {
                ret = "" + list.size()
            }
            for (Map.Entry<Long, List<Consumer<StructPB.PlayerCardInfoPB>>> entry : playerCardActor.getPlayerCardService().cache.cbList.entrySet()) {
                if (entry.getValue().size() > 10) {
                    ret += "\n" + entry.getKey()
                }
            }
            latch.countDown()
        }))
        latch.await(5, TimeUnit.SECONDS)
        return ret
    }
}
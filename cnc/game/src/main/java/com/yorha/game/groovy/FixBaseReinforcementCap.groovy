package com.yorha.game.groovy

import com.yorha.cnc.mainScene.bigScene.BigSceneEntity
import com.yorha.cnc.scene.SceneActor
import com.yorha.cnc.scene.abstractsceneplayer.addition.AbstractScenePlayerAdditionMgr
import com.yorha.cnc.scene.sceneplayer.ScenePlayerEntity
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.server.ServerContext
import com.yorha.common.utils.Pair
import com.yorha.common.utils.script.GroovyScript
import com.yorha.proto.CommonEnum.BuffEffectType

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 修复BigScene的基地援军容量上限
 * <AUTHOR>
 */
class FixBaseReinforcementCap implements GroovyScript {

    @Override
    String execute() {
        // 获取大地图zoneId
        int zoneId = ServerContext.getZoneId();
        BuffEffectType fixBuffEffectType = BuffEffectType.ET_BASE_REINFORCEMENT_CAP_FIXED;

        CountDownLatch latch = new CountDownLatch(1)
        String ret = "wrong"

        Map<Long, Pair<Long, Long>> updatePlayers = new HashMap<>();

        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), new ActorRunnable<SceneActor>("groovy", { sceneActor ->
            BigSceneEntity scene = sceneActor.getBigScene()
            for (ScenePlayerEntity scenePlayer : scene.getPlayerMgrComponent().getAllScenePlayer()) {
                // 获取基地援军容量上限
                long curValue = scenePlayer.getProp().getRally().getBeAidedMaxCap()
                long value = scenePlayer.getAdditionComponent().getAddition(fixBuffEffectType)
                // 属性相同则跳过
                if (curValue == value) {
                    continue
                }
                Map<Integer, Long> newAdditions = new HashMap<>()
                newAdditions.put(fixBuffEffectType.getNumber(), value)
                AbstractScenePlayerAdditionMgr.dispatchAdditionChange(newAdditions, scenePlayer)
                // 记录被修改的数据
                long newValue = scenePlayer.getProp().getRally().getBeAidedMaxCap()
                updatePlayers.put(scenePlayer.getPlayerId(), new Pair(curValue, newValue))
            }

            latch.countDown()
        }))
        latch.await(10, TimeUnit.SECONDS)
        ret = updatePlayers.toString()
        return ret
    }
}
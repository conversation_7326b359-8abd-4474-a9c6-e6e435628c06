package com.yorha.game.groovy

import com.yorha.cnc.scene.SceneActor
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.server.ServerContext
import com.yorha.common.utils.script.GroovyScript

class RefreshMonsterMileStone implements GroovyScript {
    int zoneId = ServerContext.getZoneId()
    int count = 1

    @Override
    String execute() {
        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), new ActorRunnable<SceneActor>("groovy", { sceneActor ->
            for (int i = 0; i < count; i++) {
                sceneActor.getBigScene().getZoneEntity().getProp().setCurMonsterMilestone(0)
            }
        }))
        return "ok"
    }
}
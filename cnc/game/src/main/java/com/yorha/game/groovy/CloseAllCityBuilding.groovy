package com.yorha.game.groovy

import com.yorha.cnc.scene.SceneActor
import com.yorha.cnc.mainScene.bigScene.BigSceneEntity
import com.yorha.cnc.scene.mapBuilding.MapBuildingEntity
import com.yorha.cnc.scene.mapBuilding.component.stagenode.normal.CloseStageNode
import com.yorha.common.actor.msg.ActorRunnable
import com.yorha.common.actor.ref.ActorSendMsgUtils
import com.yorha.common.actor.ref.RefFactory
import com.yorha.common.utils.script.GroovyScript
import com.yorha.proto.CommonEnum

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 关闭所有城市  2023/8/23
 *
 * <AUTHOR>
 */
class CloseAllCityBuilding implements GroovyScript {

    @Override
    String execute() {
        int zoneId = 1
        CountDownLatch latch = new CountDownLatch(1)
        String ret = "error"
        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), new ActorRunnable<SceneActor>("groovy", { sceneActor ->
            BigSceneEntity scene = sceneActor.getBigScene()
            Collection<List<MapBuildingEntity>> list = scene.getBuildingMgrComponent().cityBuilding.values()
            for (List<MapBuildingEntity> v : list) {
                for (MapBuildingEntity b : v) {
                    if (b.getOccupyState() != CommonEnum.OccupyState.TOS_NEUTRAL) {
                        continue
                    }
                    b.getBattleComponent().forceEndAllBattle()
                    b.getStageMgrComponent().getOccupyInfoProp().setState(CommonEnum.OccupyState.TOS_CLOSE).setStateEndTsMs(0)
                    b.getStageMgrComponent().transNewNode(new CloseStageNode(b))
                }
            }
            ret = "OK"
            latch.countDown()
        }))
        latch.await(5, TimeUnit.SECONDS)
        return ret
    }
}
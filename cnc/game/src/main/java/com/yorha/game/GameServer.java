package com.yorha.game;

import com.yorha.cnc.clan.gm.ClanGmCommandMgr;
import com.yorha.cnc.dungeon.gm.DungeonGmCommandMgr;
import com.yorha.cnc.idip.http.IMurReqHandler;
import com.yorha.cnc.idip.http.IdIpReqHandler;
import com.yorha.cnc.midasCallbackSession.MidasCallbackHandler;
import com.yorha.cnc.monitor.MonitorActor;
import com.yorha.cnc.player.activity.ActivityManager;
import com.yorha.cnc.player.controller.PlayerController;
import com.yorha.cnc.player.gm.PlayerGmCommandMgr;
import com.yorha.cnc.scene.gm.SceneGmCommandMgr;
import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.cluster.node.NodeActor;
import com.yorha.common.actor.dispatcher.ElasticFiberPoolActorDispatcherImpl;
import com.yorha.common.actor.dispatcher.IMailboxDispatcher;
import com.yorha.common.actor.dispatcher.OneDriveFiberActorDispatcherImpl;
import com.yorha.common.actor.dispatcher.ThreadPoolActorDispatcherImpl;
import com.yorha.common.actor.mailbox.ActorMailboxImpl;
import com.yorha.common.actor.mailbox.IMailboxFactory;
import com.yorha.common.actor.mailbox.IMailboxMetaData;
import com.yorha.common.actor.mailbox.middleware.DefaultMailboxMonitorMiddlewareImpl;
import com.yorha.common.actor.mailbox.middleware.IMailboxMiddleware;
import com.yorha.common.actor.msg.FutureActorRunnable;
import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.actor.node.ZoneConfigHandler;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorMetaData;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.msg.CommonActorSerializer;
import com.yorha.common.concurrent.NamedRunnable;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.dbactor.DbMgrService;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.http.CmdReqHandler;
import com.yorha.common.http.HttpServerHandler;
import com.yorha.common.io.CommandMgr;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.qlog.QlogManager;
import com.yorha.common.resource.ResLoader;
import com.yorha.common.server.AbstractServer;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.ProcessUtil;
import com.yorha.common.utils.YamlUtils;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.id.ZoneSnowflakeFactory;
import com.yorha.common.utils.ip.IpSeekerUtils;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.utils.time.schedule.ScheduleMgr;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.game.shutdowntask.*;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * game服
 *
 * <AUTHOR>
 */
public class GameServer extends AbstractServer {
    private static final Logger LOGGER = LogManager.getLogger(GameServer.class);

    public GameServer(String[] args) {
        super(args);
    }

    @Override
    public String getServerName() {
        return "gamesvr" + ServerContext.getServerInfo().getBusId();
    }

    protected void initActorSystem() {
        GeminiStopWatch stopWatch = new GeminiStopWatch("scanActorAnnotation");
        this.initActorConfigWithYaml();
        stopWatch.mark("end");
        LOGGER.info("gemini_perf initActorConfig cost: {} ms", stopWatch.getTotalCost());
        ServerContext.getActorSystem().start();
    }

    @Override
    protected void loadConfig() {
        super.loadConfig();
        if (ServerContext.isZoneServer()) {
            if (!ServerContext.getEtcdClient().watch(ActorClusterUrlUtils.etcdZoneConfigUrl(ServerContext.getZoneId()), new ZoneConfigHandler())) {
                throw new GeminiException("zone watch {} fail!", ActorClusterUrlUtils.etcdZoneConfigUrl(ServerContext.getZoneId()));
            }
        }
    }

    private void initActorConfigWithYaml() {
        String serverType = null;
        final ActorSystem.Builder actorSystemBuilder = ActorSystem.newBuilder();

        for (ActorRole actorRole : ActorRole.values()) {
            actorSystemBuilder.addActorMetaBuilder(actorRole.name(), 0);
        }
        if (ServerContext.isZoneServer()) {
            serverType = "zone";
        }

        if (ServerContext.isDungeonServer()) {
            serverType = "dungeon";
        }

        if (ServerContext.isGlobalServer()) {
            serverType = "global";
        }

        if (ServerContext.isIdipServer()) {
            serverType = "idip";
        }

        if (serverType != null) {
            // 打包后配置文件的路径，跟jar包同层级
            String ownConfig = "config/" + serverType + ".yaml";
            final File yamlFile = new File(ownConfig);
            Yaml yaml = new Yaml();
            Map<String, ArrayList<LinkedHashMap<String, Object>>> obj;
            if (yamlFile.exists()) {
                LOGGER.info("gemini_system: server deployment!");
                final StringBuilder stringBuilder = new StringBuilder();
                try {
                    BufferedReader fileReader = new BufferedReader(new FileReader(yamlFile));
                    String temp;
                    while ((temp = fileReader.readLine()) != null) {
                        stringBuilder.append(temp).append("\n");
                    }
                } catch (IOException e) {
                    LOGGER.error("gemini_system : load {} fail!", ownConfig, e);
                    throw new RuntimeException(e.getMessage());
                }
                final String result = stringBuilder.toString();
                obj = yaml.load(result);
            } else {
                LOGGER.info("gemini_system: local deployment");
                //getClassLoader.getResourceAsStream的路径不能以"/"开头
                InputStream inputStream = this.getClass()
                        .getClassLoader()
                        .getResourceAsStream(ownConfig);
                if (inputStream == null) {
                    throw new GeminiException("GameServer initActorConfigWithYaml fail, config yaml not exist, configYaml={}", ownConfig);
                }
                try {
                    obj = yaml.load(inputStream);
                } catch (Exception e) {
                    throw new GeminiException("GameServer initActorConfigWithYaml fail, inputStream not open, configYaml={}", ownConfig);
                }
            }
            LOGGER.info("gemini_system: load {}, map={}!", ownConfig, YamlUtils.dumpToYaml(obj));

            initActorMeta(actorSystemBuilder, obj);
        }

        actorSystemBuilder.addOneDriveFiberDispatcher(ActorRole.Node.name(), 0,
                1, 600);

        actorSystemBuilder.getActorMetaBuilder(ActorRole.Node.name())
                .clazz(NodeActor.class)
                .dispatcher(ActorRole.Node.name())
                .isNeedCreateMsg(true)
                .mailboxQueueSize(256)
                .localMailboxFactory(ActorMailboxImpl.Factory::new)
                .middlewareFactory(DefaultMailboxMonitorMiddlewareImpl::new);

        actorSystemBuilder.addOneDriveFiberDispatcher(ActorRole.Monitor.name(), 0,
                1, 600);
        actorSystemBuilder.getActorMetaBuilder(ActorRole.Monitor.name())
                .clazz(MonitorActor.class)
                .dispatcher(ActorRole.Monitor.name())
                .isNeedCreateMsg(true)
                .mailboxQueueSize(40000)
                .localMailboxFactory(ActorMailboxImpl.Factory::new)
                .middlewareFactory(DefaultMailboxMonitorMiddlewareImpl::new);
        final String busId = ServerContext.getServerInfo().getBusId();
        final String natsAddr = ClusterConfigUtils.getWorldConfig().getStringItem("nats_server_addr"); //ServerContext.getServerCfg().getNatsAddr();
        actorSystemBuilder.name("cnc-" + busId)
                // 网络服务器
                .busId(busId)
                .natsServer(natsAddr, new CommonActorSerializer());
        ServerContext.setActorSystem(actorSystemBuilder.build());
    }

    private Map<String, Supplier<IMailboxDispatcher>> checkAndProcessDispatcherConfig(ArrayList<LinkedHashMap<String, Object>> dispatcherConfig) {
        Map<String, Supplier<IMailboxDispatcher>> dispatcherMap = new HashMap<>(dispatcherConfig.size());
        for (LinkedHashMap<String, Object> dispatcher : dispatcherConfig) {
            // 枚举类valueOf的值不存在时会抛异常IllegalArgumentException
            String name = ActorRole.valueOf((String) dispatcher.get("name")).name();
            String type = (String) dispatcher.get("type");
            if (type == null) {
                throw new GeminiException("Dispatchers config wrong, type is null, dispatcher name={}", name);
            }
            // 注意不要使用Integer，null值强转为Integer不会报错，转为int则会抛异常
            int parallelism = (int) dispatcher.get("parallelism");
            int thoughtPut = (int) dispatcher.get("thoughPut");

            Supplier<IMailboxDispatcher> supplier;
            switch (type) {
                case "OneDriveFiberDispatcher": {
                    int keepAliveSec = (int) dispatcher.get("keepAliveSec");
                    supplier = () -> new OneDriveFiberActorDispatcherImpl(name, thoughtPut, parallelism, keepAliveSec);
                    break;
                }
                case "ThreadPoolDispatcher":
                    supplier = () -> new ThreadPoolActorDispatcherImpl(name, thoughtPut, parallelism);
                    break;
                case "ElasticFiberPoolDispatcher": {
                    int keepAliveSec = (int) dispatcher.get("keepAliveSec");
                    int poolFiberCnt = (int) dispatcher.get("poolFiberCnt");
                    int elasticFiberCnt = (int) dispatcher.get("elasticFiberCnt");
                    supplier = () -> new ElasticFiberPoolActorDispatcherImpl(name, thoughtPut, parallelism, keepAliveSec, poolFiberCnt, elasticFiberCnt);
                    break;
                }
                default:
                    throw new GeminiException("Load GameServer dispatcher fail! unKnown dispatcher type");
            }
            dispatcherMap.put(name, supplier);
        }
        return dispatcherMap;
    }

    private Map<String, ActorMetaData.Builder> checkAndProcessActorConfig(ArrayList<LinkedHashMap<String, Object>> actorConfig) {
        Map<String, ActorMetaData.Builder> actorMap = new HashMap<>(actorConfig.size());
        for (LinkedHashMap<String, Object> actor : actorConfig) {
            String name = ActorRole.valueOf((String) actor.get("name")).name();
            ActorMetaData.Builder builder = ActorMetaData.newBuilder(name, 0);
            Class<?> clazz;
            try {
                clazz = Class.forName((String) actor.get("clazz"));
            } catch (ClassNotFoundException e) {
                throw new GeminiException("Actor config is wrong, clazz not found! actor name = " + name, e);
            }
            String dispatcherName = ActorRole.valueOf((String) actor.get("dispatcher")).name();
            boolean isNeedCreateMsg = (boolean) actor.get("isNeedCreateMsg");
            int mailboxQueueSize = (int) actor.get("mailboxQueueSize");
            int mailboxMaxCount = (int) actor.get("mailboxMaxCount");
            Class<?> mailbox;
            Function<ActorMetaData, IMailboxFactory> mailboxFactory;
            try {
                mailbox = Class.forName((String) actor.get("mail"));
                try {
                    Constructor<?> mailboxConstructor = mailbox.getConstructor(IMailboxMetaData.class);
                    mailboxFactory = (ActorMetaData data) -> {
                        try {
                            return (IMailboxFactory) mailboxConstructor.newInstance(data);
                        } catch (Exception e) {
                            throw new GeminiException("MailBox Constructor illegal");
                        }
                    };
                } catch (NoSuchMethodException e) {
                    throw new GeminiException("Mailbox factory need implement a public constructor with param ActorMetaData!");
                }
            } catch (ClassNotFoundException e) {
                throw new GeminiException("Actor config is wrong, mailbox factory clazz not found! actor name = {}", name);
            }
            Class<?> middleware;
            Function<ActorMetaData, IMailboxMiddleware> middlewareFactory;
            try {
                middleware = Class.forName((String) actor.get("middleware"));
                try {
                    Constructor<?> middlewareConstructor = middleware.getConstructor(ActorMetaData.class);
                    middlewareFactory = (ActorMetaData data) -> {
                        try {
                            return (IMailboxMiddleware) middlewareConstructor.newInstance(data);
                        } catch (Exception e) {
                            throw new GeminiException("Middleware Constructor illegal");
                        }
                    };
                } catch (NoSuchMethodException e) {
                    throw new GeminiException("Middleware need implement a public constructor with param ActorMetaData!");
                }
            } catch (ClassNotFoundException e) {
                throw new GeminiException("Actor config is wrong, middleware clazz not found! actor name = {}", name);
            }
            builder.clazz(clazz)
                    .dispatcher(dispatcherName)
                    .isNeedCreateMsg(isNeedCreateMsg)
                    .mailboxQueueSize(mailboxQueueSize)
                    .mailboxMaxCount(mailboxMaxCount)
                    .localMailboxFactory(mailboxFactory)
                    .middlewareFactory(middlewareFactory);
            actorMap.put(name, builder);
        }
        return actorMap;
    }

    private void initActorMeta(ActorSystem.Builder builder, Map<String, ArrayList<LinkedHashMap<String, Object>>> obj) {
        ArrayList<LinkedHashMap<String, Object>> dispatcherConfig = obj.get("dispatchers");
        if (dispatcherConfig == null) {
            throw new GeminiException("Load GameServer dispatchers config fail, dispatchers is null");
        }
        ArrayList<LinkedHashMap<String, Object>> actorConfig = obj.get("actor");
        if (actorConfig == null) {
            throw new GeminiException("Load GameServer actor config fail, actor is null");
        }
        Map<String, Supplier<IMailboxDispatcher>> dispatcherMap = this.checkAndProcessDispatcherConfig(dispatcherConfig);
        LOGGER.info("Load dispatcher config = {}", dispatcherMap);
        Map<String, ActorMetaData.Builder> actorMap = this.checkAndProcessActorConfig(actorConfig);
        LOGGER.info("Load actor config = {}", actorMap);
        for (Map.Entry<String, Supplier<IMailboxDispatcher>> entry : dispatcherMap.entrySet()) {
            builder.addDispatcher(entry.getKey(), entry.getValue());
        }
        for (Map.Entry<String, ActorMetaData.Builder> entry : actorMap.entrySet()) {
            builder.updateActorMetaBuilder(entry.getKey(), entry.getValue());
        }
        LOGGER.info("After init actor metadata, builder={}", builder);
    }

    private ScheduledFuture<?> perfTimer;

    @Override
    protected void onStart() throws GeminiException {
        LOGGER.info("gemini_system onStart");
        // 连接数据库
        DbMgrService.getInstance().start();
        // 加载策划数据
        final String localPath = ServerContext.getGameDataLocalPath();
        ResLoader.load(localPath);
        // 加载大世界数据
        boolean isZoneServer = ServerContext.isZoneServer();
        if (isZoneServer) {
            LOGGER.info("gemini_system try load map grid data and fog map!");
            // 加载大世界地缘数据
            MapGridDataManager.getInstance().load(BigSceneConstants.BIG_SCENE_MAP_ID);
            // 通过ETCD获取zoneId对应的雪花算法生成ID的唯一WorkerId
            var id = ZoneSnowflakeFactory.getInstance().nextId();
            LOGGER.info("gemini_system try generate id = {}", id);
        }
        // 初始化定时器
        ScheduleMgr.getInstance().start(ServerContext.getBusId());
        int idBufferSize = 2;
        if (isZoneServer) {
            idBufferSize = 3;
        }
        // 初始化id生成器
        IdFactory.run(ClusterConfigUtils.getWorldConfig().getStringItem("cluster_id_factory_key"), idBufferSize);
        // 初始化ActorSystem
        this.initActorSystem();
    }

    private void runShutDownTask(AbstractShutdownTask task, long maxWaitMs, GeminiStopWatch watch) {
        LOGGER.info("gemini_system shutdown taskEnter {} maxWaitMs={}", task, maxWaitMs);
        final CountDownLatch taskDownLatch = new CountDownLatch(1);
        ConcurrentHelper.newFiber(task.toString(), () -> task.run(taskDownLatch)).start();
        try {
            final boolean isOk = taskDownLatch.await(maxWaitMs, TimeUnit.MILLISECONDS);
            LOGGER.info("gemini_system shutdown taskFinish {} isTimeout={}", task, !isOk);
        } catch (InterruptedException e) {
            LOGGER.error("gemini_system shutdown taskFinish {} interrupt!", task, e);
        }
        watch.mark(task.toString());
    }

    @Override
    protected void onStop() {
        LOGGER.info("gemini_system handleEventStopNode Node {} Stop Server!", this);
        this.stopHttpServer();
        GeminiStopWatch watch = new GeminiStopWatch("GameStop");
        // 设置标志位  拦截新的session
        runShutDownTask(new BlockNewSession(), 1_000, watch);
        // 关闭Gate 踢人。
        runShutDownTask(new WaitGateDestroy(20_000), 21_000, watch);
        // IdIp限制流量   要等旧的完成 2倍rpc
        runShutDownTask(new HttpSwitchOff(), ServerContext.getRpcTimeout() * 2L, watch);
        // 设置标志位 拦截Actor加timer的操作 拦截所有propChange的消息
        runShutDownTask(new BlockNewTimerAndPropChange(), 1_000, watch);
        // 发送停服消息
        runShutDownTask(new SendStoppingMsg(), 3_000, watch);
        // 关闭业务定时器
        runShutDownTask(new ShutdownSchedule(), 3_000, watch);
        // 等待2倍rpc时间
        runShutDownTask(new WaitTime(ServerContext.getRpcTimeout() * 2L), ServerContext.getRpcTimeout() * 2L + 1_000, watch);
        // 关闭nats
        runShutDownTask(new ShutdownNats(), 3_000, watch);
        // 发送销毁消息
        runShutDownTask(new SendDestroyMsg(), 3_000, watch);
        // 等scene销毁
        runShutDownTask(new WaitSceneDestroy(10_000), 11_000, watch);
        // 等待一个rpc时间
        runShutDownTask(new WaitTime(ServerContext.getRpcTimeout()), ServerContext.getRpcTimeout() + 1_000, watch);
        // 发送销毁2消息，区别1
        runShutDownTask(new SendDestroy2Msg(), 3_000, watch);
        // 关闭Node
        runShutDownTask(new ShutdownNode(), 5_000, watch);
        // shutdown 系统、qlog、db
        runShutDownTask(new ShutdownActorSystem(), 20_000, watch);
        runShutDownTask(new ShutdownQLog(), 5_000, watch);
        runShutDownTask(new ShutdownDB(), DbUtil.CALL_TIMEOUT_TIME_MS, watch);
        // id factory置空
        IdFactory.shutdown();
        if (perfTimer != null) {
            perfTimer.cancel(false);
            perfTimer = null;
        }
        if (ServerContext.getEtcdClient() != null) {
            ServerContext.getEtcdClient().shutDown();
            ServerContext.setEtcdClient(null);
        }
        // 关perf log
        runShutDownTask(new ShutdownPerfLog(), 7_000, watch);
        LOGGER.info("gemini_system shutdown finish {}", watch.stat());
    }

    @Override
    protected void addShutDownHook() {
        Runtime.getRuntime().addShutdownHook(ConcurrentHelper.newThread("ShutdownHook", false, () -> {
            LOGGER.info("gemini_system ShutDownHook Trigger! GameServer!");
            if (this.isStarting()) {
                System.out.println("ShutDownHook Down! isStarting!");
                LOGGER.info("gemini_system ShutDownHook Down!");
                return;
            }
            // MESA
            final long shutdownFinalTsMs = SystemClock.nowNative() + TimeUnit.MINUTES.toMillis(10);
            // 注意在线程中跑，注意并发问题，不允许直接操作内存。
            ConcurrentHelper.newThread("Node-Shutdown-Server", false, () -> ServerContext.getServer().stop()).start();
            while (!this.isTerminated()) {
                if (SystemClock.nowNative() >= shutdownFinalTsMs) {
                    LOGGER.warn("gemini_system ShutDownHook Wait TimeOut!");
                    dumpThreadInfo("over time");
                    break;
                }
                LockSupport.parkNanos(Thread.currentThread(), TimeUnit.MILLISECONDS.toNanos(10));
            }
            LOGGER.info("gemini_system ShutDownHook Down");
        }));
    }

    @Override
    protected void postStart() {
        super.postStart();
        LOGGER.info("gemini_system begin start {}", ServerContext.getActorSystem().getRegistryValue().getLocalNode());
        final GeminiStopWatch stopWatch = new GeminiStopWatch("GameServer#postStart");
        if (!ServerContext.getEtcdClient().watchPrefix(ActorClusterUrlUtils.etcdWorldKvkFoundPrefix(), DefaultZoneGateItemHandler.getInstance())) {
            throw new GeminiException("watchPrefix {} fail!", ActorClusterUrlUtils.etcdWorldKvkFoundPrefix());
        }
        stopWatch.mark("Proc Game Watch KvkFound!");
        // 初始化ZoneServer
        if (ServerContext.isZoneServer()) {
            // init CommandMgr
            CommandMgr.getInstance().init(PlayerController.class.getPackage().getName());
            stopWatch.mark("Proc Zone Init CommandMgr!");
            ActivityManager.getInstance().init();
            stopWatch.mark("Proc Zone Init ActivityManager!");
            //init qlog manager
            QlogManager.getInstance().init();
            stopWatch.mark("Proc Zone Init QlogManger!");

            // GM命令初始化
            PlayerGmCommandMgr.getInstance().init();
            SceneGmCommandMgr.getInstance().init();
            ClanGmCommandMgr.getInstance().init();
            DungeonGmCommandMgr.getInstance().init();
            stopWatch.mark("Proc Zone Init GM!");

            // Ip Seeker
            IpSeekerUtils.init();
            stopWatch.mark("Proc Zone Init IpSeekerUtils!");
        } else if (ServerContext.isDungeonServer()) { // 初始化DungeonServer
            //init qlog manager
            QlogManager.getInstance().init();
            stopWatch.mark("Proc Dungeon Init QlogManger!");
            // GM命令初始化
            SceneGmCommandMgr.getInstance().init();
            DungeonGmCommandMgr.getInstance().init();
            stopWatch.mark("Proc Dungeon Init GM!");
        } else if (ServerContext.isGlobalServer()) { // 初始化GlobalServer
            if (!ServerContext.getEtcdClient().watchPrefix(ActorClusterUrlUtils.etcdWorldZoneFoundPrefix(), DefaultZoneGateItemHandler.getInstance())) {
                throw new GeminiException("watchPrefix {} fail!", ActorClusterUrlUtils.etcdWorldZoneFoundPrefix());
            }
            stopWatch.mark("Proc Global Watch ZoneFound!");
            if (!ServerContext.getEtcdClient().watchPrefix(ActorClusterUrlUtils.etcdZoneConfigPrefix(), DefaultZoneGateItemHandler.getInstance())) {
                throw new GeminiException("watchPrefix {} fail!", ActorClusterUrlUtils.etcdZoneConfigPrefix());
            }
            stopWatch.mark("Proc Global Watch ZoneConfig!");
        } else if (ServerContext.isIdipServer()) { // 初始化IdIp Server
            // 注册idIp处理器
            HttpServerHandler.getInstance().registerUrlHandler(new IdIpReqHandler());
            // 起midas回调server,现在就简单的起在global上的
            HttpServerHandler.getInstance().registerUrlHandler(new MidasCallbackHandler());
            // 注册imur处理器
            // 注册问卷系统imur处理器
            HttpServerHandler.getInstance().registerUrlHandler(new IMurReqHandler());
            stopWatch.mark("Proc IdIp Register Http Handler!");
        } else {
            LOGGER.info("server {} no need to init process data", ServerContext.getBusId());
        }

        // 注册http服务，Groovy、Ping、资源热更新
        HttpServerHandler.getInstance().registerUrlHandler(new CmdReqHandler(new GameCmdProcessorImpl()));
        stopWatch.mark("GameServer Register Http Handler!");

        // 性能timer
        this.perfTimer = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(
                new NamedRunnable("show_process_perf", new Runnable() {
                    private long lastFlushUTCClockTsMs = 0;

                    @Override
                    public void run() {
                        ServerContext.getProcessPerfLogger().flush();
                        // 在控制台按小时刷新时间戳
                        this.flushClockOnHour();
                    }

                    private void flushClockOnHour() {
                        final long elderLastFlushUTCClockTsMs = this.lastFlushUTCClockTsMs;
                        this.lastFlushUTCClockTsMs = SystemClock.nowNative();
                        if (elderLastFlushUTCClockTsMs == 0) {
                            System.out.println("flushClockOnHour(UTC):" + TimeUtils.nowNative2String());
                            return;
                        }
                        final int lastHour = TimeUtils.getHourOfDay(elderLastFlushUTCClockTsMs);
                        final int nowHour = TimeUtils.getHourOfDay(lastFlushUTCClockTsMs);
                        if (nowHour != lastHour) {
                            System.out.println("flushClockOnHour(UTC):" + TimeUtils.nowNative2String());
                        }
                    }
                }),
                1,
                1,
                TimeUnit.MINUTES
        );
        stopWatch.mark("GameServer Init ProcPerf!");


        // 拉起本地重要Actor
        try {
            // start node
            final FutureActorRunnable<NodeActor, Boolean> initNode = new FutureActorRunnable<>("init_node", NodeActor::initAndStartNode);
            ActorSendMsgUtils.sendAndCreate(RefFactory.ofLocalNode(), initNode);
            final boolean isInitOk = initNode.get(180, TimeUnit.SECONDS);
            if (!isInitOk) {
                throw new GeminiException("init node fail");
            }
            stopWatch.mark("GameServer Actor init end!");
        } catch (Exception e) {
            throw new GeminiException("start actor fail", e);
        }

        // 启动后台HTTP服务
        final int httpThreadNum;
        if (ServerContext.isIdipServer()) {
            httpThreadNum = 4;
        } else {
            httpThreadNum = 1;
        }
        this.initHttpServer(httpThreadNum);
        stopWatch.mark("GameServer Init HttpServer!");

        // 存储PID
        ProcessUtil.savePid(ServerContext.getBusId());
        stopWatch.mark("GameServer Save Pid " + ServerContext.getBusId());

        // 开启HTTP流量
        HttpServerHandler.getInstance().switchOn();
        stopWatch.mark("GameServer SwitchOn HttpServer!");

        final String stat = StringUtils.format("gemini_perf handleEventStartNode cost: {}", stopWatch.stat());
        LOGGER.info(MARKER_NO_CONSOLE, stat);
        // k8s, 打印在控制台
        System.out.println(stat);
    }
}

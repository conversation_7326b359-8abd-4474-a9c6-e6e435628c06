package com.yorha.game.shutdowntask;

import com.yorha.common.actor.cluster.node.NodeActor;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class ShutdownNode extends AbstractShutdownTask {
    @Override
    public void run(CountDownLatch downLatch) {
        ActorSendMsgUtils.send(RefFactory.ofLocalNode(), new ActorRunnable<NodeActor>("ShutdownSystem", (node) -> {
            try {
                node.forceDestroy("ShutdownSystem");
            } finally {
                downLatch.countDown();
            }
        }));
    }
}

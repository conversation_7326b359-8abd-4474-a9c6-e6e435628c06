<?xml version="1.0" encoding="UTF-8"?>
<!-- test环境、线上环境 -->
<configuration status="warn" monitorInterval="0" shutdownHook="disable">
    <properties>
        <!-- windows环境下需要此变量 -->
        <property name="LOG_HOME">${sys:LOG_ROOT}</property>
    </properties>
    <appenders>
        <!-- 定义控制台输出-->
        <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
            <PatternLayout
                    pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%X{actorId}] - %msg%n"/>
        </Console>
    </appenders>
    <loggers>
        <!-- 3party Loggers -->
        <Logger name="org.springframework" level="warn">
        </Logger>
        <Logger name="io.netty" level="warn">
        </Logger>
        <Logger name="org.apache.http" level="warn">
        </Logger>
        <Logger name="org.apache.commons" level="warn">
        </Logger>
        <Logger name="com.mchange.v2" level="warn">
        </Logger>
        <Logger name="org.reflections" level="warn">
        </Logger>
        <Logger name="io.etcd" level="warn">
        </Logger>
        <Logger name="java.sql" level="warn">
        </Logger>
        <Logger name="io.jaegertracing" level="warn">
        </Logger>
        <Logger name="com.tencent.tcaplus" level="warn">
        </Logger>
        <Logger name="io.grpc" level="warn">
        </Logger>
        <!-- Root Logger -->
        <Root level="error" includeLocation="false">
            <appender-ref ref="ConsoleAppender"/>
        </Root>
    </loggers>
    <Filters>
        <DynamicThresholdFilter key="debugLog" onMatch="ACCEPT" onMismatch="NEUTRAL">
            <KeyValuePair key="true" value="DEBUG"/>
        </DynamicThresholdFilter>
    </Filters>
</configuration>
import com.github.spotbugs.snom.SpotBugsTask

// 外部依赖
dependencies {
    api 'com.lmax:disruptor:3.4.4'
    api 'org.apache.logging.log4j:log4j-api:2.24.3'
    api 'org.apache.logging.log4j:log4j-core:2.24.3'
    api 'org.apache.logging.log4j:log4j-web:2.24.3'
    // 桥接：告诉commons logging使用Log4j2
    api 'org.apache.logging.log4j:log4j-jcl:2.24.3'
    // 桥接：告诉slf4j使用Log4j2
    api 'org.apache.logging.log4j:log4j-slf4j2-impl:2.24.3'
    api 'org.slf4j:slf4j-api:2.0.17'
    // netty
    api 'io.netty:netty-all:4.1.63.Final'
    // executor queue
    api 'org.jctools:jctools-core:3.3.0'
    // utils io
    api 'commons-io:commons-io:2.8.0'
    // utils xml
    api 'org.dom4j:dom4j:2.1.3'
    // utils lang
    api 'org.apache.commons:commons-lang3:3.11'
    // utils collection
    api 'org.apache.commons:commons-collections4:4.4'
    // utils yaml
    api 'org.yaml:snakeyaml:1.26'
    // utils json
    api 'com.google.code.gson:gson:2.8.1'
    // utils template
    api 'org.freemarker:freemarker:2.3.31'
    // cache
    api 'com.github.ben-manes.caffeine:caffeine:2.8.5'
    // 压缩
    api 'com.github.luben:zstd-jni:1.5.5-3'
    // http
    api 'org.apache.httpcomponents:httpclient:4.5.13'
    api 'org.apache.httpcomponents:fluent-hc:4.5.13'
    api 'org.apache.httpcomponents:httpasyncclient:4.1.4'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.2'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    // protobuf
    api 'com.google.protobuf:protobuf-java:3.12.0'
    // 静态ip地域
    api "com.github.jarod:qqwry-java:0.9.+"
    // prometheus monitor
    api group: 'io.prometheus', name: 'simpleclient', version: '0.12.0'
    api group: 'io.prometheus', name: 'simpleclient_hotspot', version: '0.12.0'
    api group: 'io.prometheus', name: 'simpleclient_httpserver', version: '0.12.0'
    api group: 'io.prometheus', name: 'simpleclient_pushgateway', version: '0.12.0'
    // reflect
    api 'org.reflections:reflections:0.9.11'
    // ASM
    api 'com.esotericsoftware:reflectasm:1.11.9'
    api 'org.ow2.asm:asm:8.0.1'
    // groovy 脚本引擎
    api 'org.apache-extras.beanshell:bsh:2.0b6'
    api 'org.codehaus.groovy:groovy-all:2.4.21'
    // jetcd
    api('io.etcd:jetcd-core:0.7.3') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }
    // jnats
    api 'io.nats:jnats:2.16.0'
    api group: 'com.auth0', name: 'java-jwt', version: '3.18.2'
    api group: 'com.auth0', name: 'jwks-rsa', version: '0.20.0'
    api group: 'io.gamioo', name: 'gamioo-redis', version: '1.0.18'

    api 'it.unimi.dsi:fastutil:8.5.8'
    api('org.mockito:mockito-core:4.6.1')
    // mongo
    api 'org.mongodb:mongodb-driver-reactivestreams:5.5.1'
    api 'org.mongodb:bson:5.5.1'
    api 'io.projectreactor:reactor-core:3.8.0-M4'
    //    api 'org.mongodb:mongodb-driver-sync:5.5.1'
    api 'org.openjdk.jol:jol-core:0.17'
    // 引入 lib 目录下的所有 JAR 包
    api fileTree(dir: 'libs', include: ['*.*'])
}


/**
 * common包有许多自动生成的
 */
sourceSets {
    main {
        java {
            srcDirs 'src/main/gen'
        }
    }
    test
    jmh
}

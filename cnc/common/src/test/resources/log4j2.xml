<?xml version="1.0" encoding="UTF-8"?>
<!-- status=debug 可 以查看log4j的装配过程级别有8个  ALL,TRACE, DEBUG, INFO, WARN, ERROR ,FATAL,OFF,如果monitorInterval设置为0或负数,不会对配置变更进行监测-->
<configuration status="warn" monitorInterval="1800" shutdownHook="disable">
    <properties>
        <property name="SERVER_ID">1</property>
        <property name="SERVER_NAME">gamesvr</property>
        <property name="LOG_HOME">../log/${SERVER_NAME}/${SERVER_ID}</property>
        <property name="YORHA_HOME">../log/stat/yorha/${SERVER_NAME}/${SERVER_ID}</property>
    </properties>
    <appenders>
        <!-- 定义控制台输出-->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
        </Console>
        <!-- 程序员调试日志 -->
        <RollingRandomAccessFile name="DevLog" fileName="${LOG_HOME}/${SERVER_NAME}"
                                 filePattern="${LOG_HOME}/${SERVER_NAME}.%d{yyyy-MM-dd-HH}.log" immediateFlush="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
        <!-- 游戏产品数据分析日志 -->
        <RollingRandomAccessFile name="YORHA"
                                 fileName="${YORHA_HOME}/${SERVER_NAME}"
                                 filePattern="${YORHA_HOME}/${SERVER_NAME}.%d{yyyy-MM-dd-HH-mm}.log">
            <PatternLayout pattern="%msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="5" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
    </appenders>
    <loggers>
        <!-- 3party Loggers -->
        <Logger name="org.springframework" level="warn">
        </Logger>
        <Logger name="io.netty" level="warn">
        </Logger>
        <Logger name="org.apache.http" level="warn">
        </Logger>
        <Logger name="org.apache.commons" level="warn">
        </Logger>
        <Logger name="com.mchange.v2" level="warn">
        </Logger>
        <Logger name="org.reflections" level="warn">
        </Logger>
        <Logger name="io.etcd" level="warn">
        </Logger>
        <Logger name="java.sql" level="warn">
        </Logger>
        <Logger name="org.mongodb" level="warn">
        </Logger>
        <Logger name="com.tencent.tcaplus" level="warn">
        </Logger>
        <!-- Game Stat  logger -->
        <Logger name="YORHA" level="info" additivity="false">
            <appender-ref ref="YORHA"/>
        </Logger>
        <!-- Root Logger -->
        <Root level="DEBUG" includeLocation="true">
            <appender-ref ref="DevLog"/>
            <appender-ref ref="Console"/>
        </Root>
    </loggers>
</configuration>
package com.yorha.common.helper;

import res.template.ActivityRecycleTemplate;

import java.lang.reflect.Field;
import java.util.Map;

public class ReflectionHelper {

    /**
     * 使用反射将给定的属性值映射填充到指定的ActivityRecycleTemplate实例中。
     * 本方法直接通过反射设置字段值，不依赖setter方法。
     *
     * @param template  需要填充属性值的ActivityRecycleTemplate实例
     * @param valueMap  属性名与对应值的映射表
     */
    public static void fillActivityRecycleTemplate(ActivityRecycleTemplate template, Map<String, Object> valueMap) throws Exception {
        Class<?> templateClass = template.getClass();

        for (Map.Entry<String, Object> entry : valueMap.entrySet()) {
            String propertyName = entry.getKey();
            Object propertyValue = entry.getValue();

            Field field = getField(templateClass, propertyName);
            if (field != null) {
                field.setAccessible(true);
                field.set(template, propertyValue);
                continue;
            }

            throw new IllegalArgumentException("No matching field found for property: " + propertyName);
        }
    }

    private static Field getField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return null;
        }
    }

}
//package com.yorha.common.message;
//
//import com.yorha.gemini.concurrent.NameableThreadFactory;
//import com.yorha.gemini.concurrent.NamedRunnable;
//import com.yorha.gemini.utils.NamedCallable;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.junit.jupiter.api.BeforeAll;
//import org.junit.jupiter.api.Test;
//
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.ScheduledExecutorService;
//import java.util.concurrent.ScheduledThreadPoolExecutor;
//import java.util.concurrent.ThreadFactory;
//
//class ExecutorTest {
//    private static final Logger logger = LogManager.getLogger(ExecutorTest.class);
//    private static Executor threadExecutor;
//    private static FibberExecutor fibberExecutor;
//
//    @BeforeAll
//
//    public static void beforeAll() throws Exception {
//        ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1, new NameableThreadFactory("gameScheduler"));
//        threadExecutor = new Executor(ExecutorType.THREAD, 1, "game-world", 8, 5000, new NameableThreadFactory("game-world"), scheduledExecutorService, null);
//        Executor executor = new Executor(ExecutorType.THREAD, 2, "game-bus_cache", 8, 5000, new NameableThreadFactory("game-bus_cache"), scheduledExecutorService, null);
//        String name = executor.createWorkerName();
//        ThreadFactory factory = Thread.builder().virtual().daemon(true).name(name).factory();
//        fibberExecutor = new FibberExecutor(executor.getId(), executor.getName(), "6868686", 8, 5000, factory, scheduledExecutorService, null);
//    }
//
//
//    @Test
//    void submit() throws ExecutionException, InterruptedException {
//        String result = threadExecutor.submit(new NamedCallable<>("testCallHelloThread", () -> {
//            String value = "hello thread";
//            logger.debug(value);
//            return value;
//        })).get();
//        logger.debug(result);
//
//        result = fibberExecutor.submit(new NamedCallable<>("testCallHelloFibber", () -> {
//            String value = "hello fibber";
//            logger.debug(value);
//            return value;
//        })).get();
//        logger.debug(result);
//    }
//
//    @Test
//    void execute() {
//        threadExecutor.execute(new NamedRunnable("testHelloWorld", () -> {
//            logger.debug("hello thread");
//        }));
//        fibberExecutor.execute(new NamedRunnable("testHelloFibber", () -> {
//            logger.debug("hello fibber");
//        }));
//    }
//
//}
//

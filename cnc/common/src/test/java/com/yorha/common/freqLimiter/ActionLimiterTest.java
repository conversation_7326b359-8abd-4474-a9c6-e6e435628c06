package com.yorha.common.freqLimiter;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.freqLimitCaller.ActionLimiter;
import com.yorha.common.utils.time.TimeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;

public class ActionLimiterTest {

    @Test
    public void normalTest() throws InterruptedException {
        final long actionGapMs = TimeUnit.SECONDS.toMillis(5);
        ActionLimiter actionLimiter = new ActionLimiter();
        LongAdder l = new LongAdder();
        for (int i = 0; i < 3; i++) {
            actionLimiter.tryAction(actionGapMs, 3, l::increment);
        }
        // 期望只执行一次
        Assertions.assertEquals(1L, l.longValue());
        for (int i = 0; i < 3; i++) {
            actionLimiter.tryAction(actionGapMs, 3, l::increment);
        }
        // 期望只执行一次
        Assertions.assertEquals(1L, l.longValue());

        Thread.sleep(actionGapMs);
        for (int i = 0; i < 3; i++) {
            actionLimiter.tryAction(actionGapMs, 3, l::increment);
        }
        Assertions.assertEquals(2L, l.longValue());
    }

    @Test
    public void failTest() throws InterruptedException {
        final long actionGapMs = TimeUnit.SECONDS.toMillis(5);
        ActionLimiter actionLimiter = new ActionLimiter();
        LongAdder l = new LongAdder();
        for (int i = 0; i < 2; i++) {
            actionLimiter.tryAction(actionGapMs, 3, l::increment);
        }
        // 期望没执行
        Assertions.assertEquals(0L, l.longValue());
        Thread.sleep(actionGapMs);
        for (int i = 0; i < 2; i++) {
            actionLimiter.tryAction(actionGapMs, 3, l::increment);
        }
        // 期望没执行
        Assertions.assertEquals(0L, l.longValue());
    }

    /**
     * 测试并发模型
     *
     * @throws InterruptedException
     */
    @Test
    public void concurrentTest() throws InterruptedException {
        final long actionGapMs = TimeUnit.SECONDS.toMillis(2);
        ActionLimiter actionLimiter = new ActionLimiter();
        LongAdder l = new LongAdder();

        final int taskNum = 1000;
        CountDownLatch latch = new CountDownLatch(taskNum);
        GeminiThreadPoolExecutor executor = ConcurrentHelper.newSingleThreadExecutor("test", 1, true, new ThreadPoolExecutor.AbortPolicy());
        System.out.println(TimeUtils.nowNative2String());
        int num = 0;
        for (int i = 0; i < taskNum; i++) {
            try {
                executor.submit(() -> {
                    actionLimiter.tryAction(actionGapMs, 3, l::increment);
                    latch.countDown();
                });
            } catch (RejectedExecutionException e) {
                num++;
                latch.countDown();
            }

        }

        latch.await();
        System.out.println("reject " + num);
        System.out.println(TimeUtils.nowNative2String());
        Assertions.assertTrue(l.sum() > 0);
        System.out.println(l.sum());
        Assertions.assertEquals(1, l.sum());
    }
}

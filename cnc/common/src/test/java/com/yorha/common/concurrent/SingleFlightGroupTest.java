package com.yorha.common.concurrent;

import com.google.common.util.concurrent.UncheckedExecutionException;
import com.yorha.common.concurrent.singleflight.SingleFlightGroup;
import org.apache.commons.lang3.tuple.Pair;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;
import org.junit.jupiter.api.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Vector;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class SingleFlightGroupTest {
    public static class CacheKey {
        private final String key1;
        private final String key2;

        public CacheKey(String key1, String key2) {
            this.key1 = key1;
            this.key2 = key2;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            CacheKey cacheKey = (CacheKey) o;

            if (!Objects.equals(key1, cacheKey.key1)) return false;
            return Objects.equals(key2, cacheKey.key2);
        }

        @Override
        public int hashCode() {
            int result = key1 != null ? key1.hashCode() : 0;
            result = 31 * result + (key2 != null ? key2.hashCode() : 0);
            return result;
        }
    }

    public static class CacheItem {
        private final String uin;
        private final String subAccountUin;
        private final long status;
        private final String clusterTag;
        private final long expireMsEpoch;


        public CacheItem(String uin, String subAccountUin, long status, String clusterTag, long expireMsEpoch) {
            this.uin = uin;
            this.subAccountUin = subAccountUin;
            this.status = status;
            this.clusterTag = clusterTag;
            this.expireMsEpoch = expireMsEpoch;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            CacheItem cacheItem = (CacheItem) o;

            if (status != cacheItem.status) return false;
            if (expireMsEpoch != cacheItem.expireMsEpoch) return false;
            if (!Objects.equals(uin, cacheItem.uin)) return false;
            if (!Objects.equals(subAccountUin, cacheItem.subAccountUin))
                return false;
            return Objects.equals(clusterTag, cacheItem.clusterTag);
        }

        @Override
        public int hashCode() {
            int result = uin != null ? uin.hashCode() : 0;
            result = 31 * result + (subAccountUin != null ? subAccountUin.hashCode() : 0);
            result = 31 * result + (int) (status ^ (status >>> 32));
            result = 31 * result + (clusterTag != null ? clusterTag.hashCode() : 0);
            result = 31 * result + (int) (expireMsEpoch ^ (expireMsEpoch >>> 32));
            return result;
        }
    }


    private static final String KEY_1 = "uin";
    private static final String KEY_2 = "sub_uin";
    private static final int DEFAULT_CONCURRENT_NUMBER = 20;
    private static final CacheKey CACHE_KEY = new CacheKey(KEY_1, KEY_2);
    private static final CacheItem CACHE_ITEM = new CacheItem(KEY_1, KEY_2, 1, "test",
            System.currentTimeMillis() + 1000000); // offset 1000s

    private static ThreadPoolExecutor SYSTEM_THREAD_POOL;

    @BeforeAll
    public static void before() {
        SYSTEM_THREAD_POOL = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS,
                new MpscBlockingConsumerArrayQueue<>(1000),
                Thread.ofPlatform().daemon().name("system").factory());
    }

    @AfterAll
    public static void after() {
        SYSTEM_THREAD_POOL.shutdown();
    }


    @Test
    @DisplayName("异步并发性测试")
    public void invokeAsyncMultiply() throws InterruptedException {
        // 测试并发
        final int totalKey = 3;
        AtomicInteger atomicNumber = new AtomicInteger();
        AtomicInteger atomicInteger = new AtomicInteger();
        AtomicInteger atomicScore = new AtomicInteger();
        CountDownLatch latch = new CountDownLatch(DEFAULT_CONCURRENT_NUMBER);
        SingleFlightGroup<String, CacheItem> sf = new SingleFlightGroup<>();
        List<Pair<CacheItem, Throwable>> cbResult = new Vector<>();
        List<CacheItem> results = this.invokeTaskConcurrent(DEFAULT_CONCURRENT_NUMBER, () -> {
            final int number = atomicNumber.incrementAndGet();
            sf.invokeAsync(KEY_1 + (number % totalKey), () -> {
                atomicScore.addAndGet(sf.size());
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Assertions.assertNull(e);
                }
                atomicInteger.incrementAndGet();
                return CACHE_ITEM;
            }, (v, e) -> {
                cbResult.add(Pair.of(v, e));
                latch.countDown();
            });
            return null;
        });
        results.forEach(Assertions::assertNull);
        Assertions.assertEquals(results.size(), DEFAULT_CONCURRENT_NUMBER, "not same result size");
        latch.await();
        // 测似乎返回值
        cbResult.forEach(ve -> {
            Assertions.assertEquals(CACHE_ITEM, ve.getLeft(), "not recv same result");
            Assertions.assertSame(CACHE_ITEM, ve.getLeft(), "not recv same instance");
        });
        // 测试singleflight效果
        Assertions.assertEquals(atomicNumber.get(), results.size(), "not right drive number");
        Assertions.assertEquals(atomicInteger.get(), totalKey, "not right drive number");
        Assertions.assertTrue(((1 + totalKey) * totalKey / 2) <= atomicScore.get(), "check score");
    }

    @Test
    @DisplayName("异步异常测试")
    public void invokeAsyncException() throws InterruptedException {
        final AtomicInteger counter = new AtomicInteger();
        SingleFlightGroup<CacheKey, CacheItem> sf = new SingleFlightGroup<>();
        Assertions.assertNull(sf.get(CACHE_KEY), "cache key not right");
        List<Pair<CacheItem, Throwable>> cbResults = new Vector<>();
        final Exception e = new Exception("test");
        final CountDownLatch latch = new CountDownLatch(DEFAULT_CONCURRENT_NUMBER);
        List<CacheItem> results = this.invokeTaskConcurrent(DEFAULT_CONCURRENT_NUMBER, () -> {
            sf.invokeAsync(CACHE_KEY, () -> {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e1) {
                    Assertions.assertNull(e1);
                }
                counter.incrementAndGet();
                throw new UncheckedExecutionException(e);
            }, (v, err) -> {
                latch.countDown();
                cbResults.add(Pair.of(v, err));
            });
            return null;
        });
        latch.await();
        Assertions.assertNull(sf.get(CACHE_KEY), "cache key not right");
        Assertions.assertEquals(DEFAULT_CONCURRENT_NUMBER, results.size(), "not one drive");
        results.forEach(Assertions::assertNull);
        Assertions.assertEquals(cbResults.size(), DEFAULT_CONCURRENT_NUMBER, "cb results number not right!");
        Assertions.assertEquals(1, counter.get(), "not one drive");
        cbResults.forEach(ve -> {
            Assertions.assertNull(ve.getLeft());
            Assertions.assertSame(ve.getRight().getCause(), e);
        });
    }

    @Test
    @DisplayName("异步测试")
    public void invokeAsync() throws InterruptedException {
        SingleFlightGroup<CacheKey, CacheItem> sf = new SingleFlightGroup<>();
        Assertions.assertNull(sf.get(CACHE_KEY));
        AtomicInteger counter = new AtomicInteger();
        CountDownLatch latch = new CountDownLatch(DEFAULT_CONCURRENT_NUMBER);
        List<Pair<CacheItem, Throwable>> cbResults = new Vector<>();
        List<CacheItem> results = this.invokeTaskConcurrent(DEFAULT_CONCURRENT_NUMBER, () -> {
            sf.invokeAsync(CACHE_KEY, () -> {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Assertions.assertNull(e);
                }
                counter.incrementAndGet();
                return CACHE_ITEM;
            }, (v, t) -> {
                cbResults.add(Pair.of(v, t));
                latch.countDown();
            });
            return null;
        });
        Assertions.assertEquals(results.size(), DEFAULT_CONCURRENT_NUMBER, "not right concurrent number");
        results.forEach((v) -> Assertions.assertNull(null));
        latch.await();
        Assertions.assertNull(sf.get(CACHE_KEY), "cache key not right");
        Assertions.assertEquals(1, counter.get(), "not one drive");
        Assertions.assertEquals(cbResults.size(), results.size(), "cb size not right");
        cbResults.forEach(ve -> {
            Assertions.assertEquals(Pair.of(CACHE_ITEM, null), ve, "cb result not right");
            Assertions.assertSame(ve.getLeft(), CACHE_ITEM, "value not same instance");
        });
    }

    @Test
    @DisplayName("single flight invoke函数测试")
    public void invoke() {
        final SingleFlightGroup<CacheKey, CacheItem> sf = new SingleFlightGroup<>();
        AtomicInteger counter = new AtomicInteger();
        Assertions.assertNull(sf.get(CACHE_KEY), "call not null");
        List<CacheItem> results = invokeTaskConcurrent(DEFAULT_CONCURRENT_NUMBER, () -> {
            try {
                return sf.invoke(CACHE_KEY, () -> {
                    Assertions.assertNotNull(sf.get(CACHE_KEY), "call is null");
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Assertions.assertNull(e);
                    }
                    counter.incrementAndGet();
                    return CACHE_ITEM;
                });
            } catch (Throwable throwable) {
                Assertions.assertNull(throwable);
            }
            return null;
        });
        Assertions.assertEquals(results.size(), DEFAULT_CONCURRENT_NUMBER, "result number not right");
        results.forEach(result -> {
            Assertions.assertEquals(CACHE_ITEM, result, "not recv same result");
            Assertions.assertSame(CACHE_ITEM, result, "not recv same result instance");
        });
        Assertions.assertEquals(1, counter.get(), "not one drive");
        Assertions.assertNull(sf.get(CACHE_KEY), "call is not null");
    }

    @Test
    @DisplayName("SingleFlight invoke 异常测试")
    public void invokeWhenException() {
        AtomicInteger counter = new AtomicInteger();
        SingleFlightGroup<CacheKey, CacheItem> sf = new SingleFlightGroup<>();
        Assertions.assertNull(sf.get(CACHE_KEY), "call not null");
        List<Throwable> exceptions = new Vector<>();
        final Exception e = new Exception("test");
        List<CacheItem> results = this.invokeTaskConcurrent(DEFAULT_CONCURRENT_NUMBER, () -> {
            try {
                return sf.invoke(CACHE_KEY, () -> {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ie) {
                        Assertions.assertNull(ie);
                    }
                    counter.incrementAndGet();
                    throw new UncheckedExecutionException(e);
                });
            } catch (Throwable ex) {
                exceptions.add(ex);
            }
            return null;
        });
        Assertions.assertNull(sf.get(CACHE_KEY), "check null call");
        Assertions.assertEquals(results.size(), DEFAULT_CONCURRENT_NUMBER, "check result counts");
        results.forEach(result -> Assertions.assertNull(result, "result not null"));
        Assertions.assertEquals(DEFAULT_CONCURRENT_NUMBER, exceptions.size(), "exception count not right");
        Assertions.assertEquals(counter.get(), 1, "not one drive");
        exceptions.forEach(exception -> {
            Assertions.assertTrue(exception instanceof UncheckedExecutionException, "exception not right");
            Assertions.assertSame(exception.getCause(), e, "exception cause not right");
        });
    }

    @Test
    @DisplayName("并发测试并发多个SingleFlight key")
    public void invokeMultiply() {
        // 测试并发
        final int totalKey = 3;
        AtomicInteger atomicNumber = new AtomicInteger();
        AtomicInteger atomicInteger = new AtomicInteger();
        AtomicInteger atomicScore = new AtomicInteger();
        SingleFlightGroup<String, CacheItem> sf = new SingleFlightGroup<>();
        List<CacheItem> results = this.invokeTaskConcurrent(DEFAULT_CONCURRENT_NUMBER, () -> {
            final int number = atomicNumber.incrementAndGet();
            try {
                return sf.invoke(KEY_1 + (number % totalKey), () -> {
                    atomicScore.addAndGet(sf.size());
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Assertions.assertNull(e);
                    }
                    atomicInteger.incrementAndGet();
                    return CACHE_ITEM;
                });
            } catch (Throwable throwable) {
                Assertions.assertNull(throwable);
            }
            return null;
        });
        // 测似乎返回值
        results.forEach(result -> {
            Assertions.assertEquals(CACHE_ITEM, result, "not recv same result");
            Assertions.assertSame(CACHE_ITEM, result, "not recv same instance");
        });
        // 测试singleflight效果
        Assertions.assertEquals(results.size(), DEFAULT_CONCURRENT_NUMBER, "not same result size");
        Assertions.assertEquals(atomicNumber.get(), results.size(), "not right drive number");
        Assertions.assertEquals(atomicInteger.get(), totalKey, "not right drive number");
        Assertions.assertTrue(((1 + totalKey) * totalKey / 2) <= atomicScore.get(), "check score");
    }


    private <T> List<T> invokeTaskConcurrent(final int cnt, Callable<T> task) {
        final ExecutorService es = Executors.newFixedThreadPool(cnt, Thread.ofVirtual()
              //  .scheduler(SYSTEM_THREAD_POOL)
                .factory());
        List<Future<T>> futures = new ArrayList<>(cnt);
        List<T> results = new ArrayList<>(cnt);
        try {
            // 提交并发任务
            for (int i = 0; i < cnt; i++) {
                Future<T> f = es.submit(task);
                futures.add(f);
            }
            // 等待任务结束
            Assertions.assertEquals(futures.size(), cnt, "future task not right");
            futures.forEach(f -> {
                try {
                    T result = f.get();
                    results.add(result);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            Assertions.assertNull(e, "executor error");
        } finally {
            es.shutdown();
        }
        return results;
    }
}

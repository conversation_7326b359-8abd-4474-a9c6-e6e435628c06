package com.yorha.common.utils.jol;

import com.google.common.collect.Lists;
import com.yorha.common.utils.TextTable;
import org.openjdk.jol.info.GraphLayout;

import java.util.List;

/**
 * 内存分析工具测试，没有做成单元测试，就main函数跑跑啦
 */
public class JolParserTest {

    private static class TestObject {
        int i;
        Long l;
        String str;
        boolean[] bArray;
        TestObject next;
        List<String> strArray;
        List<TestObject> childs;

        public TestObject(int i, Long l, String str, boolean[] bArray, TestObject next, List<String> strArray, List<TestObject> childs) {
            this.i = i;
            this.l = l;
            this.str = str;
            this.bArray = bArray;
            this.next = next;
            this.strArray = strArray;
            this.childs = childs;
        }
    }

    public static void main(String[] args) {
        TestObject obj1 = new TestObject(
                1,
                2L,
                "乌拉乌拉",
                new boolean[]{true},
                null,
                Lists.newArrayList("a", "bb", "ccccc"),
                Lists.newArrayList());
        TestObject obj2 = new TestObject(
                1,
                2L,
                "乌拉乌拉",
                new boolean[]{true, false},
                obj1,
                Lists.newArrayList("a", "bb", "ccccc"),
                Lists.newArrayList(obj1));
        TestObject obj3 = new TestObject(
                1,
                2L,
                "乌拉乌拉",
                new boolean[]{true, false, true},
                obj2,
                Lists.newArrayList("a", "bb", "ccccasdasdac"),
                Lists.newArrayList(obj1, obj2)
        );
        parseAndPrint(obj1);
        parseAndPrint(obj2);
        parseAndPrint(obj3);

    }

    private static void parseAndPrint(TestObject obj1) {
        System.out.println("##################################################################");
        GraphLayout graphLayout = GraphLayout.parseInstance(obj1);
        System.out.println(graphLayout.totalSize());
        System.out.println(graphLayout.totalCount());
        System.out.println(graphLayout.toPrintable());
        System.out.println(graphLayout.toFootprint());

        TreeLayout treeLayout = TreeLayout.parse(graphLayout);
        TextTable textTable = JolParser.toDisplay(treeLayout, 20, 2);
        System.out.println(textTable.newStringBuilder().toString());
    }
}

package com.yorha.common.utils.shape.nonUnitTest;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Rectangle;
import com.yorha.common.utils.vector.Vector2f;

import javax.swing.*;
import java.awt.*;
import java.text.MessageFormat;

class RectangleTest extends JFrame {

    public static void main(String[] args) {
        new RectangleTest();
    }

    public RectangleTest() {
        setTitle("绘制几何图形");
        setBounds(0, 0, 2000, 1500);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setVisible(true);

        Container c = getContentPane();
        MyCanvas canvas = new MyCanvas();
        c.add(canvas);
    }

    private class MyCanvas extends Canvas {
        @Override
        public void paint(Graphics g) {
            Point a = Point.valueOf(800, 400);
            Point b = Point.valueOf(600, 700);

            Graphics2D g2 = (Graphics2D) g;//新绘图类
            Font font = new Font("微软雅黑", Font.PLAIN, 16);
            g2.setBackground(Color.WHITE);

            g2.setFont(font);

            //消除文字锯齿
            g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            //消除画图锯齿
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            BasicStroke stroke = new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND, 1, new float[]{16, 4}, 1);
            g2.setStroke(stroke);
//            this.drawPoint(true, g2, b, Color.RED);
//            this.drawPoint(true, g2, a, Color.BLACK);

            //g2.drawLine(a.getX(), a.getY(), b.getX(), b.getY());
            /*BasicStroke.CAP_ROUND : 圆角末端
             *BasicStroke.CAP_BUTT :无修饰末端
             *BasicStroke.CAP_SQUARE :正方形末端
             *
             *BasicStroke.JOIN_BEVEL :平角
             *BasicStroke.JOIN_MITER :尖角（默认）
             *BasicStroke.JOIN_ROUND :圆角
             * */
            Rectangle rectangle = Rectangle.valueOf(Point.valueOf(800, 800), Vector2f.valueOf(700f, -900f), 500, 700);
            TestUtils.drawRect(g2, rectangle, Color.BLACK);
            this.drawPoint(true, g2, rectangle.getCenter(), Color.RED);
            this.drawPoint(true, g2, rectangle.getPointA(), Color.RED);
            this.drawPoint(true, g2, rectangle.getPointB(), Color.RED);
            this.drawPoint(true, g2, rectangle.getPointC(), Color.RED);
            this.drawPoint(true, g2, rectangle.getPointD(), Color.RED);

//            rectangle.rotate(b);
//            TestUtils.drawRect(g2, rectangle, Color.RED);
//            rect.rotate(-30);
//            this.drawRect(g2, rect, Color.RED);

            for (int i = 0; i < 5000; i++) {
                Point rand = Point.valueOf(RandomUtils.randomBetween(0, 1400), RandomUtils.randomBetween(0, 1400));
//                Point test = Point.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000));
                if (rectangle.containsPoint(rand.getX(), rand.getY())) {
                    System.out.println("true:" + rand.getX() + "," + rand.getY());
                    this.drawPoint(false, g2, rand, Color.GREEN);
                } else {
                    System.out.println("false:" + rand.getX() + "," + rand.getY());
                    this.drawPoint(false, g2, rand, Color.RED);
                }
            }
//            System.out.println(rectangle.toString());
//            boolean flag = rect.isPointInsideSelf(test);
//            System.out.println(flag);


            //   g2.drawPolyline();

//			g2.setStroke(stroke);//加载画笔


//            stroke = new BasicStroke(1, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND);
//            g2.setStroke(stroke);//加载画笔


//			g2.setColor(c);
//            g2.setColor(Color.BLUE);
//            g2.drawOval(60, 20, 30, 30);// 画了一个圆
//
//            java.awt.Shape shape1 = new Rectangle2D.Double(110, 5, 100, 100);// 矩形圆形对象
//            g2.fill(shape1);// 画这个图形
//
//            java.awt.Shape shape2 = new Rectangle2D.Double(220, 15, 80, 80);// 矩形圆形对象
//            g2.fill(shape2);// 画这个图形
//
//            g2.drawArc(320, 25, 100, 50, 270, 200);// 弧形
//
//            g2.drawLine(5, 120, 100, 120);//横线
//            g2.drawLine(50, 120, 50, 200);//垂直线
//
//            g2.drawRoundRect(120, 120, 100, 50, 10, 10);// 圆角矩形
//
//            int x[] = {250, 300, 250, 300};
//            int y[] = {130, 130, 200, 200};
////			g2.drawPolygon(x, y, 4);// 多边形
//
//            g2.drawPolyline(x, y, 4);// 多边线


        }

        public void drawPoint(boolean show, Graphics g2, Point point, Color c) {
            g2.setColor(c);
            if (show) {
                g2.drawString(MessageFormat.format("({0},{1})", point.getX(), point.getY()), point.getX(), point.getY());
            }

            g2.fillOval(point.getX(), point.getY(), 6, 6);
        }


    }


}









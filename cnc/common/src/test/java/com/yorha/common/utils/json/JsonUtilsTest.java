package com.yorha.common.utils.json;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class JsonUtilsTest {
    private static final Logger LOGGER = LogManager.getLogger(JsonUtilsTest.class);

    @Test
    public void testParseObject() throws InterruptedException {

        // 正常解析idip发来的groovy脚本
        String obj = "{\"head\":{\"Cmdid\":\"1\",\"Seqid\":\"1\",\"ServiceName\":\"execScript\"},\"body\":{\"script\":\"package com.yorha.gemini.utils.script\\n\\nimport com.yorha.common.resource.ResHolder\\nimport res.template.ConstTemplate\\n\\nclass GetConstReturnTime implements GroovyScript {\\n\\n    @Override\\n    String execute() {\\n        int time = ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getReturnTime()\\n        return time;\\n    }\\n}\\n\\n              \"}}";
        Map dataMap = new HashMap<>();
        dataMap = JsonUtils.parseObject(obj, dataMap.getClass());

        // 带""的会出现问题
//        String obj2 = "{\"test\": \"return \"ok\"\"}";
//        dataMap = JsonUtils.parseObject(obj2, dataMap.getClass());

        // 带""的会出现问题 加上\\解决
        String obj3 = "{\"test\": \"return \\\"ok\\\"\"}";
        dataMap = JsonUtils.parseObject(obj3, dataMap.getClass());
    }

}

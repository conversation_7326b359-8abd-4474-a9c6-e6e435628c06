package com.yorha.common.helper;


import com.yorha.common.asset.AssetPackage;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.proto.Struct;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import res.template.ActivityRecycleTemplate;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActivityHelperTest {

    /**
     * 测试回收或补偿的流程
     */
    @Disabled
    @Test
    public void testBuildRecycleOrRecoup() throws Exception {
        ActivityRecycleTemplate activityRecycleTemplate = new ActivityRecycleTemplate();
        ArrayList<IntPairType> cost = new ArrayList<>();
        cost.add(IntPairType.makePair(1, 30));
        cost.add(IntPairType.makePair(2, 10));
        cost.add(IntPairType.makePair(3, 50));

        ArrayList<IntPairType> giver = new ArrayList<>();
        giver.add(IntPairType.makePair(1, 100));

        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("id", 123);
        valueMap.put("activityId", 123);
        valueMap.put("recycleItemPairList", cost);
        valueMap.put("recycleMail", 123);
        valueMap.put("compensateItemPairList", giver);
        
        ReflectionHelper.fillActivityRecycleTemplate(activityRecycleTemplate, valueMap); // 使用getter/setter方法填充

        final Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);

        Pair<AssetPackage, List<Struct.ItemPair>> assetPackageListPair = ActivityHelper.buildRecycleOrRecoup((id) -> 100, activityRecycleTemplate);
        AssetPackage first = assetPackageListPair.getFirst();
        List<Struct.ItemPair> second = assetPackageListPair.getSecond();

        Assertions.assertEquals(3, first.getImmutableAssets().size());
        first.forEachItems(itemDesc -> {
            if (itemDesc.getId() == 1){
                Assertions.assertEquals(100, itemDesc.getAmount());
            }
            if (itemDesc.getId() == 2){
                Assertions.assertEquals(100, itemDesc.getAmount());
            }
            if (itemDesc.getId() == 3){
                Assertions.assertEquals(100, itemDesc.getAmount());
            }
        });
        Assertions.assertEquals(1, second.size());
        for (Struct.ItemPair itemPair : second) {
            Assertions.assertEquals(200, itemPair.getCount());
        }
    }
}

package com.yorha.common;

import com.yorha.common.utils.eventdispatcher.EventDispatcher;
import com.yorha.common.utils.eventdispatcher.EventListener;
import com.yorha.common.utils.eventdispatcher.IEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.atomic.AtomicLong;

public class EventDispatcherTest {
    private static final Logger LOGGER = LogManager.getLogger(EventDispatcherTest.class);
    private final EventDispatcher eventDispatcher = new EventDispatcher();
    private EventListener eventListener1;
    private EventListener eventListener2;

    /**
     * 测试用 模拟死亡事件
     */
    private class DieEvent extends IEvent {
        private int templateId;

        public DieEvent(int templateId) {
            this.templateId = templateId;
        }

        public int getTemplateId() {
            return templateId;
        }
    }

    /**
     * 测试用 模拟移动事件
     */
    private class MoveEvent extends IEvent {
        private int x;
        private int y;

        public MoveEvent(int x, int y) {
            this.x = x;
            this.y = y;
        }

        public int getX() {
            return x;
        }

        public int getY() {
            return y;
        }
    }


    /**
     * 测试添加并触发单个事件 回调中取消
     */
    @Test
    public void testAddAndTriggerEvent() {
        eventListener1 = eventDispatcher.addEventListenerRepeat(this::onDie, DieEvent.class);
        eventDispatcher.dispatch(new DieEvent(1001));
        eventDispatcher.dispatch(new DieEvent(1002));
    }

    private void onDie(DieEvent event) {
        LOGGER.info("entity Id:{}", event.getTemplateId());
        eventListener1.cancel();
    }

    /**
     * 测试添加并触发单个事件  回调中再次添加
     */
    @Test
    public void testAddTriggerRepeatEvent() {
        eventDispatcher.addEventListener(this::onDieAndAdd, DieEvent.class);
        eventDispatcher.dispatch(new DieEvent(1001));
        eventDispatcher.dispatch(new DieEvent(1002));
    }

    private void onDieAndAdd(DieEvent event) {
        LOGGER.info("entity Id:{}", event.getTemplateId());
        eventDispatcher.addEventListener(this::onDieAndAdd, DieEvent.class);
    }

    /**
     * 测试添加并触发系列事件
     */
    @Test
    public void testAddMultiOnceTriggerEvent() {
        EventDispatcher eventDispatcher = new EventDispatcher();
        eventListener2 = eventDispatcher.addMultiEventListener(this::onTrigger, DieEvent.class, MoveEvent.class);
        eventDispatcher.dispatch(new MoveEvent(0, 0));
        eventDispatcher.dispatch(new DieEvent(1));
    }

    /**
     * 测试添加并触发系列事件
     */
    @Test
    public void testAddMultiTriggerEvent() {
        EventDispatcher eventDispatcher = new EventDispatcher();
        // player1
        eventListener1 = eventDispatcher.addEventListenerRepeat(this::onDie, DieEvent.class);

        // player2
        eventListener2 = eventDispatcher.addMultiEventListenerRepeat(this::onTrigger, DieEvent.class, MoveEvent.class);
        eventDispatcher.dispatch(new MoveEvent(0, 0));
        eventDispatcher.dispatch(new DieEvent(10001));
    }

    private void onTrigger(IEvent event) {
        if (event.equals(MoveEvent.class)) {
            LOGGER.info("moveEvent:{}", event.getClass().getName());
        }
        LOGGER.info("event:{}", event.getClass().getName());
    }


    private void onMove(IEvent event) {
        MoveEvent e = (MoveEvent) event;
        LOGGER.info("x:{} y:{}", e.getX(), e.getY());
    }

    /**
     * 测试移除事件
     */
    @Test
    public void testRemoveEvent() {
        AtomicLong ret = new AtomicLong(0);
        EventDispatcher eventDispatcher = new EventDispatcher();
        //监听死亡事件，并对ret赋值
        eventListener1 = eventDispatcher.addEventListenerRepeat(this::onDie, DieEvent.class);
        //移除监听
        eventListener1.cancel();
        //触发事件，此刻应无回调
        eventDispatcher.dispatch(new DieEvent(10001));
        //ret保持为0
        Assertions.assertEquals(ret.get(), 0);
    }

}

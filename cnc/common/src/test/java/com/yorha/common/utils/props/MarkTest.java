package com.yorha.common.utils.props;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractMapNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.AbstractSetNode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class MarkTest {
    @Test
    @DisplayName("掩码正确性测试")
    public void mark() {
        Assertions.assertEquals(0x7FFF + 1, AbstractPropNode.FIELD_DESCRIBE_MAX_FIELD_COUNT);
        Assertions.assertEquals(0x7FFF, AbstractPropNode.FIELD_DESCRIBE_FIELD_INDEX_MASK);
        Assertions.assertEquals(0x3FFF8000, AbstractPropNode.FIELD_DESCRIBE_FIELD_COUNT_MASK);
        Assertions.assertEquals(0x7FFF, AbstractListNode.FIELD_DESCRIBE_INDEX_MASK);
        Assertions.assertEquals(0x8000, AbstractListNode.FIELD_DESCRIBE_DIRTY_MASK);
        Assertions.assertEquals(0x3FF, AbstractMapNode.FILED_DESCRIBE_MOD_MASK);
        Assertions.assertEquals(0x400, AbstractMapNode.FIELD_DESCRIBE_DIRTY_MASK);
        Assertions.assertEquals(0x800, AbstractMapNode.FIELD_DESCRIBE_CLEAR_MASK);
        Assertions.assertEquals(0x7FFF000, AbstractMapNode.FIELD_DESCRIBE_INDEX_MASK);
        Assertions.assertEquals(0x3FF, AbstractSetNode.FILED_DESCRIBE_MOD_MASK);
        Assertions.assertEquals(0x400, AbstractSetNode.FIELD_DESCRIBE_DIRTY_MASK);
        Assertions.assertEquals(0x800, AbstractSetNode.FIELD_DESCRIBE_CLEAR_MASK);
        Assertions.assertEquals(0x7FFF000, AbstractSetNode.FIELD_DESCRIBE_INDEX_MASK);
    }
}

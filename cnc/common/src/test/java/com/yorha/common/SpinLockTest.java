package com.yorha.common;

import com.google.common.collect.Maps;
import com.yorha.common.lock.SpinLock;
import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
public class SpinLockTest {

    @Test
    public void test() throws InterruptedException {
        Map<Integer, Integer> map = Maps.newHashMap();
        SpinLock lock = new SpinLock();

        // 一个线程写
        new Thread(() -> {
            lock.lock();
            for (int i = 0; i < 1000; i++) {
                map.put(i, i);
            }
            lock.unlock();
        }).start();

        // 一个线程读
        new Thread(() -> {
            lock.lock();
            for (Map.Entry<Integer, Integer> entry : map.entrySet()) {

            }
            lock.unlock();
        }).start();
    }

}

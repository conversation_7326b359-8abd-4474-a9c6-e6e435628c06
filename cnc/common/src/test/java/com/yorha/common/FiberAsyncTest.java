package com.yorha.common;

import com.yorha.common.concurrent.FiberAsync;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

public class FiberAsyncTest {
    private static final Logger LOGGER = LogManager.getLogger(FiberAsyncTest.class);

    private static class TestFiberAsync extends FiberAsync<Object, Exception> {

        private final CountDownLatch c;

        public TestFiberAsync(CountDownLatch countDownLatch) {
            this.c = countDownLatch;
        }

        @Override
        protected void requestAsync() {
            LOGGER.debug("try do sth");
            this.c.countDown();
        }

        public void ok() {
            LOGGER.debug("ok");
            asyncCompleted("1");
        }
    }

    @Test
    public void fiberTest() throws InterruptedException {
        ThreadFactory factory = Thread.ofVirtual().name("dummy", 1).factory();

        int num = 1000;

        CountDownLatch latch = new CountDownLatch(num);

        for (int i = 0; i < num; ++i) {
            long startTime = System.currentTimeMillis();

            Thread thread = factory.newThread(() -> {
                Object a = new String("xxxx");
                LockSupport.parkNanos(a, 1000000 * 10000L);
                long delta = System.currentTimeMillis() - startTime;
                if (delta > 1) {
                    System.out.println(Thread.currentThread().getName() + " cost " + delta + " ms");
                }
                latch.countDown();
            });
            thread.start();
            // Thread.sleep(10);
//            System.out.println(thread.getName()+" " +thread.getState());
            LockSupport.unpark(thread);
        }

        boolean await = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(await);
    }

}

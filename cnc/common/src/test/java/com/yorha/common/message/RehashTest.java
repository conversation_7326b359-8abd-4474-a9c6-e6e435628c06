package com.yorha.common.message;


import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@DisplayName("Rehash测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class RehashTest {
    private static final Logger logger = LogManager.getLogger(RehashTest.class);
    private static List<Long> list;

    public static int DELAY = 10_000;

    @BeforeAll
    public static void beforeAll() throws Exception {
        list = new ArrayList<>();
        for (long i = 0; i < 2000; i++) {
            long value = RandomUtils.nextLong(1000000, 9999999999L);
            list.add(value);
        }


    }

    @BeforeEach
    public void beforeEach() throws Exception {

    }

    @AfterEach
    public void afterEach() throws Exception {

    }


    @DisplayName("取模运算")
    @Test
    public void modulus() throws Exception {
        Map<Integer, DeviationDTO> store = new HashMap<>();
        for (long id : list) {
            int index = (int) (id & 7);
            DeviationDTO dto = store.get(index);
            if (dto == null) {
                dto = new DeviationDTO();
                dto.setValue(1);
                store.put(index, dto);
            } else {
                dto.increase();
            }
        }

        for (DeviationDTO e : store.values()) {
            e.setRatio(String.format("%.4f", Math.abs(0.125f - e.getValue() / 2000f) * 100) + "%");
        }

        logger.debug("modulus store={}", store);

    }

    @DisplayName("rehash8取模运算")
    @Test
    public void rehash8() throws Exception {
        Map<Integer, DeviationDTO> store = new HashMap<>();
        for (Long id : list) {
            int h = id.hashCode();
            h ^= h >>> 16;
            int index = (int) (h & 7);
            DeviationDTO dto = store.get(index);
            if (dto == null) {
                dto = new DeviationDTO();
                dto.setValue(1);
                store.put(index, dto);
            } else {
                dto.increase();
            }

        }
        for (DeviationDTO e : store.values()) {
            e.setRatio(String.format("%.4f", Math.abs(0.125f - e.getValue() / 2000f) * 100) + "%");
        }

        logger.debug("rehash8 store={}", store);
    }

    @DisplayName("rehash7取模运算")
    @Test
    public void rehash7() throws Exception {
        Map<Integer, DeviationDTO> store = new HashMap<>();
        for (Long id : list) {
            int h = 0;
            h ^= id.hashCode();
            h ^= (h >>> 20) ^ (h >>> 12);
            h = h ^ (h >>> 7) ^ (h >>> 4);
            int index = h & 7;
            DeviationDTO dto = store.get(index);
            if (dto == null) {
                dto = new DeviationDTO();
                dto.setValue(1);
                store.put(index, dto);
            } else {
                dto.increase();
            }
        }

        for (DeviationDTO e : store.values()) {

            e.setRatio(String.format("%.4f", Math.abs(0.125f - e.getValue() / 2000f) * 100) + "%");
        }

        logger.debug("rehash7 store={}", store);
    }

}

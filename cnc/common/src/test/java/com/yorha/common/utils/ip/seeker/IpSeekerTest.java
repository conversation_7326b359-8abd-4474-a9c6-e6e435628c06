package com.yorha.common.utils.ip.seeker;

import com.github.jarod.qqwry.IPZone;
import com.yorha.common.utils.ip.IpSeekerUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * 2021年12月03日 10:58:00
 */
public class IpSeekerTest {


    @Test
    public void testIpSeeker() {
        IpSeekerUtils.init();
        IPZone HKIPZone = IpSeekerUtils.getIpZone("**************");
        IPZone ShangHaiZone = IpSeekerUtils.getIpZone("*************");
        IPZone USAZone = IpSeekerUtils.getIpZone("************");
        IPZone AfricaZone = IpSeekerUtils.getIpZone("*************");
        System.out.println(HKIPZone.toString());
        System.out.println(ShangHaiZone.toString());
        System.out.println(USAZone.toString());
        System.out.println(AfricaZone.toString());
        Assertions.assertTrue(HKIPZone.toString().contains("香港"));
        Assertions.assertTrue(ShangHaiZone.toString().contains("上海"));
        Assertions.assertTrue(USAZone.toString().contains("美国"));
        Assertions.assertTrue(AfricaZone.toString().contains("非洲"));
    }
}

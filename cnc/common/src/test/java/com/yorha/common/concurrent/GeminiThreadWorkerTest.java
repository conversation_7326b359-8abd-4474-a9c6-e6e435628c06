package com.yorha.common.concurrent;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadWorker;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class GeminiThreadWorkerTest {

    @Test
    public void start() throws InterruptedException {
        final GeminiThreadWorker test = GeminiThreadWorker.newBuilder()
                .daemon(true)
                .taskSize(100)
                .name("test")
                .build();
        Assertions.assertFalse(test.isWorking());
        Assertions.assertFalse(test.isTakingTask());
        // 启动工作线程
        test.start();
        Assertions.assertTrue(test.isWorking());
        final CountDownLatch latch = new CountDownLatch(1);
        test.execute(latch::countDown);
        // 关闭工作线程
        Assertions.assertTrue(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertTrue(test.isWorking());
        Assertions.assertTrue(test.isTakingTask());
        Assertions.assertTrue(test.shutdown(1000));
        Assertions.assertFalse(test.isWorking());
        Assertions.assertFalse(test.isTakingTask());
        Assertions.assertEquals(0, test.getTaskCount());
    }

    @Test
    public void fullfilled() throws InterruptedException {
        final GeminiThreadWorker test = GeminiThreadWorker.newBuilder()
                .daemon(true)
                .taskSize(4)
                .name("test")
                .build();
        test.start();
        Assertions.assertTrue(test.isWorking());
        // 计数器
        final AtomicInteger cnt = new AtomicInteger(0);
        final CountDownLatch endLatch = new CountDownLatch(1);
        // 执行首个长耗时任务
        test.execute(() -> {

            // 连续丢进去10任务
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            cnt.incrementAndGet();
            endLatch.countDown();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ignored) {
            }
        });
        Assertions.assertTrue(test.isWorking());
        Assertions.assertTrue(endLatch.await(5, TimeUnit.SECONDS));
        // mpsc会对齐容量
        Assertions.assertEquals(5, test.getTaskCount());
        Assertions.assertTrue(test.isTakingTask());
        // 等待结束
        Assertions.assertTrue(test.shutdown(20000));
        Assertions.assertFalse(test.isWorking());
        Assertions.assertFalse(test.isTakingTask());
        Assertions.assertEquals(5, cnt.get());
    }

    @Test
    public void shutdown() throws InterruptedException {
        final GeminiThreadWorker test = GeminiThreadWorker.newBuilder()
                .daemon(true)
                .taskSize(100)
                .name("test")
                .build();
        test.start();

        // 计数器
        final AtomicInteger cnt = new AtomicInteger(0);
        // 执行首个长耗时任务
        test.execute(() -> {
            cnt.incrementAndGet();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ignored) {
            }
        });
        // 控制latch
        final CountDownLatch endLatch = new CountDownLatch(1);
        final CountDownLatch postLatch = new CountDownLatch(1);
        // 大量投递
        ConcurrentHelper.newThread("test1", false, () -> {
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(cnt::incrementAndGet);
            test.execute(() -> {
                cnt.incrementAndGet();
                endLatch.countDown();
            });
            postLatch.countDown();
        }).start();
        // 投递完成
        Assertions.assertTrue(postLatch.await(5, TimeUnit.SECONDS));
        // 积压任务数量
        Assertions.assertEquals(10, test.getTaskCount());
        Assertions.assertTrue(test.isWorking());
        Assertions.assertTrue(test.isTakingTask());
        // 不等待结束，瞬间关闭
        Assertions.assertFalse(test.shutdown(0));
        Assertions.assertFalse(test.isWorking());
        Assertions.assertTrue(test.isTakingTask());
        // 积压任务数量
        Assertions.assertEquals(10, test.getTaskCount());
        // 关闭后投递，被拒绝
        test.execute(cnt::incrementAndGet);
        test.execute(cnt::incrementAndGet);
        test.execute(cnt::incrementAndGet);
        test.execute(cnt::incrementAndGet);
        // 最终check
        Assertions.assertEquals(10, test.getTaskCount());
        Assertions.assertFalse(test.isWorking());
        Assertions.assertTrue(test.isTakingTask());
        Assertions.assertTrue(endLatch.await(5, TimeUnit.SECONDS));
        Assertions.assertEquals(10, cnt.get());
        Assertions.assertEquals(0, test.getTaskCount());
        Assertions.assertFalse(test.isWorking());
        Assertions.assertFalse(test.isTakingTask());
    }
}

package com.yorha.common.concurrent;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ComputeTest {

    @Test
    @DisplayName("computeIfAbsent测试")
    public void computeIfAbsentTest() throws InterruptedException {
        ConcurrentHashMap<Integer, Integer> map = new ConcurrentHashMap<>();
        map.computeIfAbsent(1, (k) -> {
            return null;
        });
        map.computeIfAbsent(1, (k) -> {
            return 2;
        });
        Assertions.assertEquals(map.get(1), 2);
    }

    @Test
    @DisplayName("forTest测试")
    public void forTest() throws InterruptedException {
        // 用普通的hashMap会报异常
        // Map<Integer, Integer> hashMap = new HashMap<>();
        Map<Integer, Integer> hashMap = new ConcurrentHashMap<>();


        // 1个线程写
        ConcurrentHelper.newThread("write", false, () -> {
            for (int i = 0; i < 1000; i++) {
                hashMap.put(i, i);
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();


        // 100个线程for循环
        for (int j = 0; j < 100; j++) {
            ConcurrentHelper.newThread("for-" + j, false, () -> {
                for (Integer v : hashMap.values()) {
                    // do sth
                    try {
                        Thread.sleep(1);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }


    }

}

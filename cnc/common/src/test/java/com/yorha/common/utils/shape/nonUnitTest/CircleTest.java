package com.yorha.common.utils.shape.nonUnitTest;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Line;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Rectangle;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR>
 * @Description 圆形图形相关单元测试
 * @createTime 2021年11月02日 16:11:00
 */
public class CircleTest extends JFrame {

    Circle circle = Circle.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 300));

    public static void main(String[] args) {
        new CircleTest();
    }

    public CircleTest() {
        setTitle("绘制几何图形");
        setBounds(0, 0, 2000, 1500);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setVisible(true);

        Container c = getContentPane();
        MyCanvas canvas = new MyCanvas();
        c.add(canvas);
    }

    private class MyCanvas extends Canvas {

        @Override
        public void paint(Graphics g) {
            Graphics2D g2 = (Graphics2D) g;
            TestUtils.initGraphics(g2);
            g2.setColor(Color.BLACK);
            g2.drawOval(circle.getX() - circle.getR(), circle.getY() - circle.getR(), circle.getR() * 2, circle.getR() * 2);
            while (true) {
                try {
                    Thread.sleep(100);
                    // 测试插件
                    testOnLine(g2);
//                    testOnCircle(g2);
//                    testOnPoint(g2);
//                    testOnRectangle(g2);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 测试是否与圆相交
     */
    private void testOnCircle(Graphics2D g2) {
        Circle circleB = Circle.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 300));
        g2.setColor(Color.RED);
        if (ShapeUtils.isContact(circleB, circle)) {
            g2.setColor(Color.GREEN);
        }
        g2.drawOval(circleB.getX() - circleB.getR(), circleB.getY() - circleB.getR(), circleB.getR() * 2, circleB.getR() * 2);

    }

    /**
     * 测试是否和点相交
     */
    private void testOnPoint(Graphics2D g2) {
        Point point = Point.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000));
        g2.setColor(Color.RED);
        if (ShapeUtils.isContact(circle, point)) {
            g2.setColor(Color.GREEN);
        }
        g2.drawString("+", point.getX(), point.getY());
    }

    /**
     * 测试是否和线段相交
     */
    private void testOnLine(Graphics2D g2) {
        Line line = Line.valueOf(Point.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000)), Point.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000)));
        g2.setColor(Color.RED);
        if (ShapeUtils.isContact(circle, line)) {
            g2.setColor(Color.GREEN);
        }
        g2.drawLine(line.getSrcPoint().getX(), line.getSrcPoint().getY(), line.getEndPoint().getX(), line.getEndPoint().getY());
    }

    /**
     * 测试是否和矩形相交
     */
    private void testOnRectangle(Graphics2D g2) {
        Point point = Point.valueOf(RandomUtils.nextInt(200, 1000), RandomUtils.nextInt(200, 1000));
        Rectangle rectangle = Rectangle.valueOf(point, RandomUtils.nextInt(0, 200), RandomUtils.nextInt(0, 200));
        Color color = Color.RED;
        if (ShapeUtils.isContact(circle, rectangle)) {
            color = Color.GREEN;
        }
        TestUtils.drawRect(g2, rectangle, color);
    }


}

package com.yorha.common.resource;

import com.yorha.common.resource.resservice.whiteList.WhiteListResService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class ClientVersionTest {
    WhiteListResService.ClientVersion w1 = WhiteListResService.ClientVersion.of("1.0.0.3");
    WhiteListResService.ClientVersion w2 = WhiteListResService.ClientVersion.of("2.1.2.4");

    WhiteListResService.ClientVersion case1 = WhiteListResService.ClientVersion.of("1.0.0.2");
    WhiteListResService.ClientVersion case2 = WhiteListResService.ClientVersion.of("1.0.0.3");
    WhiteListResService.ClientVersion case3 = WhiteListResService.ClientVersion.of("1.0.0.4");


    WhiteListResService.ClientVersion case4 = WhiteListResService.ClientVersion.of("2.1.2.3");
    WhiteListResService.ClientVersion case5 = WhiteListResService.ClientVersion.of("2.1.2.4");
    WhiteListResService.ClientVersion case6 = WhiteListResService.ClientVersion.of("2.1.2.5");
    WhiteListResService.ClientVersion case7 = WhiteListResService.ClientVersion.of("1.0.111111111.2");


    @DisplayName("测试客户端版本号比较接口")
    @Test
    public void testGetAllResTemplate() {
        Assertions.assertTrue(checkIsValid(case1));
        Assertions.assertTrue(checkIsValid(case2));
        Assertions.assertTrue(checkIsValid(case3));

        Assertions.assertTrue(checkIsValid(case4));
        Assertions.assertTrue(checkIsValid(case5));
        Assertions.assertTrue(checkIsValid(case6));

        Assertions.assertFalse(checkIsValid(case7));
    }

    private Boolean checkIsValid(WhiteListResService.ClientVersion one) {
        if (one.compareTo(w1) >= 0) {
            return true;
        }
        if (one.compareTo(w2) >= 0) {
            return true;
        }
        return false;
    }
}

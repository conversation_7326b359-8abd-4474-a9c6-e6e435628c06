package com.yorha.common.concurrent;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ReentrantReadWriteLockTest {


    @Test
    @DisplayName("ReentrantReadWriteLock测试")
    public void reentrantReadWriteLockTest() throws InterruptedException {

        Thread.ofVirtual().name("ReentrantReadWriteLockTest");
        
        ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
        // 写锁
        lock.writeLock().lock();
        System.out.println(1);
        // 读锁
        lock.readLock().lock();
        System.out.println(2);
        // 读再锁
        lock.readLock().lock();
        System.out.println(3);
        // 解开读锁
        lock.readLock().unlock();
        System.out.println(4);
        // 解开 写锁
        lock.writeLock().unlock();
        System.out.println(5);
    }

}

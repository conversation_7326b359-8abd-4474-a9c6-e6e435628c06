package com.yorha.common.resource;

import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.*;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class ResLoaderTest {
    private static final Logger LOGGER = LogManager.getLogger(ResLoaderTest.class);

    @BeforeAll
    public static void init() {
        // 初始化wechat
        ServerContext.initServerInfo("999.1.4.1");
        ClusterConfigUtils.refreshCluster("unit-test",
                "wechat_log_enable: true\n" +
                        "wechat_log_host_name: https://qyapi.weixin.qq.com\n" +
                        "wechat_server_err_key: 141a8f09-5654-477e-904b-c39c9986a2ca\n" +
                        "wechat_designer_err_key: d402cc66-335b-4a87-aa07-d2ab6a670c0d\n" +
                        "wechat_log_sync: true\n" +
                        "rpc_timeout: 1000\n" +
                        "developer: test\n" +
                        "check_change_attr: false\n" +
                        "env_flag: dev\n" +
                        "client_data_local_path: ../../../../out/cnc/game_data/client\n" +
                        "game_data_local_path: ../../../game_data/server");
        WechatLog.getInstance().init(ServerContext::getServerInfo);
    }

    @DisplayName("获取所有策划数据模板")
    @Test
    public void testGetAllResTemplate() {
        Set<Class<? extends IResTemplate>> allResTemplate = ResHolder.getResTemplateClazzSet(null);
        Assertions.assertFalse(allResTemplate.isEmpty());
    }

    @DisplayName("获取所有ResService")
    @Test
    public void testGetAllResService() {
        Set<Class<? extends AbstractResService>> allResServiceClazz = ResHolder.getResServiceClazzSet(null);
        Assertions.assertFalse(allResServiceClazz.isEmpty());
    }

    @DisplayName("加载测试")
    @Test
    public void testLoad() {
        // 当前执行路径
        System.out.println(System.getProperty("user.dir"));
        ResLoader.load(ClusterConfigUtils.getWorldConfig().getStringItem("game_data_local_path"));
        //ResLoader.load(ServerContext.getGameDataLocalPath());
    }

    @AfterAll
    public static void afterAll() throws Exception {
        LOGGER.info("try stop");
        WechatLog.getInstance().stop();
        LOGGER.info("stop ok");
    }

}

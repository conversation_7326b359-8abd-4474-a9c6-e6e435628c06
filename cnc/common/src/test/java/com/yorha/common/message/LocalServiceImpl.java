package com.yorha.common.message;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class LocalServiceImpl implements LocalService {
    private static final Logger logger = LogManager.getLogger(LocalServiceImpl.class);

    @Override
    public String helloWorld(String value) {
        String result = "Hello world " + value;
        logger.debug("result={}", result);
        return result;
    }

    @Override
    public String helloBus(String value) {
        String result = "Hello bus " + value;
        logger.debug("result={}", result);
        return result;
    }
}

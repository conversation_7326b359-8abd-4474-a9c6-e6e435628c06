package com.yorha.common.utils;

import com.yorha.common.utils.boolmap.BoolMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * 位置工具包测试用例
 *
 * <AUTHOR>
 * 2021年11月11日 14:44:00
 */
public class PositionBoolMapTest {

    @Test
    public void positionTest() {
        BoolMap boolMap = BoolMap.getInstance(90, 90);
        boolMap.setGridStatus(20, 50, 1, true);
        boolMap.setGridStatus(20, 51, 1, true);
        boolMap.setGridStatus(20, 52, 1, true);
        boolMap.setGridStatus(20, 53, 1, true);
        boolMap.setGridStatus(20, 54, 1, true);

        Assertions.assertTrue(boolMap.checkPosStatus(20, 50, true));
        boolMap.setGridStatus(20, 50, 1, false);
        Assertions.assertTrue(boolMap.checkPosStatus(20, 50, false));
        Assertions.assertFalse(boolMap.checkPosStatus(99, 50, false));
        Assertions.assertFalse(boolMap.checkPosStatus(-1, 50, false));
    }

    @Test
    public void bfsPathTest() {
        BoolMap boolMap = BoolMap.getInstance(10, 10);
        boolMap.setGridStatusTwoWay(1, 2, true);
        boolMap.setGridStatusTwoWay(2, 7, true);
        boolMap.setGridStatusTwoWay(7, 9, true);
        boolMap.setGridStatusTwoWay(3, 4, true);
//        System.out.println(boolMap.findPath(1, 2));
//        System.out.println(boolMap.findPath(9, 1));
//        System.out.println(boolMap.findPath(3, 5));
    }
}

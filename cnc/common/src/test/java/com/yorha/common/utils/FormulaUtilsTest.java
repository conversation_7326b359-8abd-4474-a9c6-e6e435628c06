package com.yorha.common.utils;

import org.junit.jupiter.api.Test;

public class FormulaUtilsTest {
    @Test
    public void f1() {
        FormulaUtils.f1(1, 1, 1, 1);
    }

    @Test
    public void f2() {
        FormulaUtils.f2(1, 1, 1, 1);
    }

    @Test
    public void f3() {
        FormulaUtils.f3(1, 1, 1, 1);
    }

    @Test
    public void f4() {
        FormulaUtils.f4(1, 1, 1, 1);
    }

    @Test
    public void f5() {
        FormulaUtils.f5(1, 1, 1, 1);
    }

    @Test
    public void f6() {
        FormulaUtils.f6(1, 1, 1, 1);
    }
}

package com.yorha.common.utils.shape;

import com.yorha.common.utils.vector.Vector2f;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class PointTest {

    @Test
    public void testGetPointWithVectorAndDis() {
        Point point = Point.valueOf(10, 20);
        Vector2f vector = Vector2f.valueOf(33, 44);
        Point finalPoint = point.getPointWithVectorAndDis(vector, 10);
        Assertions.assertEquals(finalPoint, Point.valueOf(10 + 7, 20 + 6));
    }

}

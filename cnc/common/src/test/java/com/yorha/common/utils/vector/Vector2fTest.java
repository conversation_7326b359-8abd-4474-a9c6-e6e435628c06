package com.yorha.common.utils.vector;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class Vector2fTest {

    @Test
    public void testIntersectionAngle() {
        Vector2f a = Vector2f.valueOf(2, 0);
        Vector2f b = Vector2f.valueOf(4, 4);
        Assertions.assertTrue(Math.abs(Vector2f.getIntersectionAngle(a, b) - 4) < 0.1f);
        Assertions.assertTrue(Math.abs(Vector2f.getIntersectionAngle2(a, b) - 356) < 0.1f);
        Vector2f c = Vector2f.valueOf(4, -4);
        Assertions.assertTrue(Math.abs(Vector2f.getIntersectionAngle(a, c) - 4) < 0.1f);
        Assertions.assertTrue(Math.abs(Vector2f.getIntersectionAngle2(a, c) - 4) < 0.1f);
        Vector2f d = Vector2f.valueOf(-4, -4);
        Assertions.assertTrue(Math.abs(Vector2f.getIntersectionAngle(a, d) - 176) < 0.1f);
        Assertions.assertTrue(Math.abs(Vector2f.getIntersectionAngle2(a, d) - 184) < 0.1f);
    }

    @Test
    public void testRotateVector() {
        Vector2f a = Vector2f.valueOf(2, 0);
        Vector2f rotate = a.rotate(45);
        Vector2f rotate1 = a.rotate(135);
        Vector2f rotate2 = a.rotate(315);
        int x = 1;
    }

}

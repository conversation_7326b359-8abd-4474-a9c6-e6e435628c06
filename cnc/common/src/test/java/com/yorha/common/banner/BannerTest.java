package com.yorha.common.banner;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.*;
@DisplayName("Banner测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BannerTest {
    private static final Logger logger = LogManager.getLogger(BannerTest.class);
    @Test
    @Order(1)
    @DisplayName("打印水印")
    public void print() {
        BannerPrinter.print("222.1.4.1","game",BannerTest.class);
    }
}

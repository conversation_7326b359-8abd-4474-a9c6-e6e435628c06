package com.yorha.common.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.yaml.snakeyaml.error.YAMLException;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class YamlUtilsTest {
    public static class YamlTestCase<T> {
        private final String content;
        private final boolean isException;
        private final T result;

        public YamlTestCase(String content, T result, boolean isException) {
            this.content = content;
            this.result = result;
            this.isException = isException;
        }

        public String getContent() {
            return this.content;
        }

        public T getResult() {
            return this.result;
        }

        public boolean getIsException() {
            return this.isException;
        }
    }

    public static class YamlResult {
        private int appId;
        private int zoneId;
        private String password;
        private List<String> addressList;

        public YamlResult() {

        }

        public YamlResult(int appId, int zoneId, String password, List<String> addressList) {
            this.appId = appId;
            this.zoneId = zoneId;
            this.password = password;
            this.addressList = addressList;
        }

        public void setAddressList(List<String> addressList) {
            this.addressList = addressList;
        }

        public void setAppId(int appId) {
            this.appId = appId;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public void setZoneId(int zoneId) {
            this.zoneId = zoneId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            YamlResult that = (YamlResult) o;
            return appId == that.appId && zoneId == that.zoneId && Objects.equals(password, that.password) && Objects.equals(addressList, that.addressList);
        }

        @Override
        public int hashCode() {
            return Objects.hash(appId, zoneId, password, addressList);
        }
    }

    @Test
    public void testNewInstanceWithClazz() {
        Stream.of(
                new YamlTestCase<YamlUtilsTest.YamlResult>("tcaplus:\n" +
                        "    appId: 2\n" +
                        "   zoneId: 3\n" +
                        "  password: B433BB5525731A54\n" +
                        "  addressList:", null, true),
                new YamlTestCase<>(
                        "appId: 2\n" +
                                "zoneId: 3\n" +
                                "password: B433BB5525731A54\n" +
                                "addressList:\n" +
                                "- tcp://***********:9999",
                        new YamlResult(2, 3, "B433BB5525731A54",
                                Stream.of("tcp://***********:9999").collect(Collectors.toList())), false)
        ).forEach(testCase -> {
            try {
                YamlUtilsTest.YamlResult result = YamlUtils.newInstance(testCase.getContent(), YamlUtilsTest.YamlResult.class);
                Assertions.assertFalse(testCase.getIsException());
                Assertions.assertEquals(result, testCase.getResult());
            } catch (YAMLException e) {
                Assertions.assertTrue(testCase.getIsException(), e.getMessage());
            }
        });
    }

    @Test
    public void testNewInstance() {
        Stream.of(
                new YamlTestCase<YamlUtilsTest.YamlResult>("tcaplus:\n" +
                        "    appId: 2\n" +
                        "   zoneId: 3\n" +
                        "  password: B433BB5525731A54\n" +
                        "  addressList:", null, true),
                new YamlTestCase<>(
                        "appId: 2\n" +
                                "zoneId: 3\n" +
                                "password: B433BB5525731A54\n" +
                                "addressList:\n" +
                                "- tcp://***********:9999",
                        new YamlResult(2, 3, "B433BB5525731A54",
                                Stream.of("tcp://***********:9999").collect(Collectors.toList())), false)
        ).forEach(testCase -> {
            try {
                Map<String, Object> result = YamlUtils.newInstance(testCase.getContent());
                Assertions.assertTrue(!testCase.getIsException());
                Assertions.assertTrue(result.get("appId").equals(testCase.getResult().appId));
                Assertions.assertTrue(result.get("zoneId").equals(testCase.getResult().zoneId));
                Assertions.assertTrue(result.get("password").equals(testCase.getResult().password));
                Assertions.assertTrue(result.get("addressList").equals(testCase.getResult().addressList));
            } catch (YAMLException e) {
                Assertions.assertTrue(testCase.getIsException(), e.getMessage());
            }
        });
    }
}

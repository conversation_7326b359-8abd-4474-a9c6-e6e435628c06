package com.yorha.common.utils.shape.nonUnitTest;

import com.yorha.common.utils.shape.Rectangle;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Point;

import java.awt.*;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @Description 测试用便携工具包
 * @createTime 2021年11月03日 14:42:00
 */
public class TestUtils {

    /**
     * 画一个矩阵
     */
    public static void drawRect(Graphics g2, Rectangle rect, Color c) {
        {
            g2.setColor(c);
            Point leftBottom = rect.getPointC();
            Point rightBottom = rect.getPointD();
            Point leftTop = rect.getPointA();
            Point rightTop = rect.getPointB();
            g2.drawString(MessageFormat.format(" leftBottom ({0},{1})", leftBottom.getX(), leftBottom.getY()), leftBottom.getX(), leftBottom.getY());
            g2.drawString(MessageFormat.format(" rightBottom ({0},{1})", rightBottom.getX(), rightBottom.getY()), rightBottom.getX(), rightBottom.getY());
            g2.drawString(MessageFormat.format(" leftTop ({0},{1})", leftTop.getX(), leftTop.getY()), leftTop.getX(), leftTop.getY());
            g2.drawString(MessageFormat.format(" rightTop ({0},{1})", rightTop.getX(), rightTop.getY()), rightTop.getX(), rightTop.getY());
            g2.drawLine(leftBottom.getX(), leftBottom.getY(), rightBottom.getX(), rightBottom.getY());
            g2.drawLine(leftTop.getX(), leftTop.getY(), rightTop.getX(), rightTop.getY());
            g2.drawLine(leftTop.getX(), leftTop.getY(), leftBottom.getX(), leftBottom.getY());
            g2.drawLine(rightTop.getX(), rightTop.getY(), rightBottom.getX(), rightBottom.getY());
        }

        {
            AABB aabb = rect.getAABB();
            Point leftBottom = Point.valueOf(aabb.getLeft(), aabb.getBottom());
            Point rightBottom = Point.valueOf(aabb.getRight(), aabb.getBottom());
            Point leftTop = Point.valueOf(aabb.getLeft(), aabb.getTop());
            Point rightTop = Point.valueOf(aabb.getRight(), aabb.getTop());
            g2.drawString(MessageFormat.format(" ({0},{1})", leftBottom.getX(), leftBottom.getY()), leftBottom.getX(), leftBottom.getY());
            g2.drawString(MessageFormat.format(" ({0},{1})", rightBottom.getX(), rightBottom.getY()), rightBottom.getX(), rightBottom.getY());
            g2.drawString(MessageFormat.format(" ({0},{1})", leftTop.getX(), leftTop.getY()), leftTop.getX(), leftTop.getY());
            g2.drawString(MessageFormat.format(" ({0},{1})", rightTop.getX(), rightTop.getY()), rightTop.getX(), rightTop.getY());
            g2.drawLine(leftBottom.getX(), leftBottom.getY(), rightBottom.getX(), rightBottom.getY());
            g2.drawLine(leftTop.getX(), leftTop.getY(), rightTop.getX(), rightTop.getY());
            g2.drawLine(leftTop.getX(), leftTop.getY(), leftBottom.getX(), leftBottom.getY());
            g2.drawLine(rightTop.getX(), rightTop.getY(), rightBottom.getX(), rightBottom.getY());
        }
    }

    /**
     * 初始化一个画板
     */
    public static Graphics2D initGraphics(Graphics2D g) {
        Graphics2D g2 = g;//新绘图类
        Font font = new Font("微软雅黑", Font.PLAIN, 16);
        g2.setBackground(Color.WHITE);
        g2.setFont(font);

        //消除文字锯齿
        g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        //消除画图锯齿
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);


        BasicStroke stroke = new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND, 1, new float[]{16, 4}, 1);
        g2.setStroke(stroke);
        return g2;
    }
}

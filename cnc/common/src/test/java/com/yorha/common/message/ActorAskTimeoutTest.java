package com.yorha.common.message;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.cluster.net.IActorClusterNetServer;
import com.yorha.common.actor.mailbox.ActorMailboxImpl;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.FixedRef;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actorservice.msg.GeminiCompletionStage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.SsNode;
import org.apache.commons.lang3.NotImplementedException;
import org.junit.jupiter.api.*;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@DisplayName("Actor ask超时测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ActorAskTimeoutTest {
    private static final String BUS_ID = "10086.0.0.1";
    private static final String DISPATCHER_NAME = "TestDispatcher";
    private static final String ACTOR_ROLE = "Test";


    public static class TestActor extends AbstractActor {

        public TestActor(ActorSystem system, IActorRef self) {
            super(system, self);
        }

        @Override
        protected void handleTypedMsg(TypedMsg typedMsg) {

        }

    }

    public static class SwallowNetServer implements IActorClusterNetServer {

        @Override
        public void start() {

        }

        @Override
        public void shutdown() {

        }

        @Override
        public void sendMsg(String busId, ActorMsgEnvelope envelope) {
            throw new NotImplementedException();
        }
    }

    @BeforeAll
    public static void init() {
        final ActorSystem.Builder builder = ActorSystem.newBuilder();
        builder.busId(BUS_ID).name("TestHHH").server(new SwallowNetServer());
        builder.addOneDriveFiberDispatcher(DISPATCHER_NAME, 0, 1, 100);
        builder.addActorMetaBuilder(ACTOR_ROLE, 0);
        builder.getActorMetaBuilder(ACTOR_ROLE)
                .clazz(TestActor.class)
                .dispatcher(DISPATCHER_NAME)
                .mailboxQueueSize(10)
                .isNeedCreateMsg(false)
                .localMailboxFactory(ActorMailboxImpl.Factory::new);
        ServerContext.setActorSystem(builder.build());
        ServerContext.initServerInfo(BUS_ID);
        ClusterConfigUtils.refreshCluster("unit-test",
                "wechat_log_enable: false\n" +
                        "wechat_log_host_name: https://qyapi.weixin.qq.com\n" +
                        "wechat_server_err_key: 0cef16ef-8f11-454a-84f7-41d8aa58ab78\n" +
                        "wechat_designer_err_key: d402cc66-335b-4a87-aa07-d2ab6a670c0d\n" +
                        "wechat_log_sync: true\n" +
                        "rpc_timeout: 1000\n" +
                        "developer: test\n" +
                        "check_change_attr: false\n" +
                        "env_flag: dev\n");
        IdFactory.fakeWorkerRun();
        ServerContext.getActorSystem().start();
        // tips: 强制引入SsMsgTypes.getTypeFromMsg，以免跑的时候卡住主流程。
        final TypedMsg typedMsg = new TypedMsg(SsMsgTypes.getTypeFromMsg(SsNode.NodeTaskCmd.getDefaultInstance()), SsNode.NodeTaskCmd.getDefaultInstance());
    }

    @AfterAll
    public static void destroy() {
        ServerContext.getActorSystem().shutdown();
        ServerContext.setActorSystem(null);
        ServerContext.setEtcdClient(null);
        IdFactory.shutdown();
    }

    @DisplayName("正常ask请求")
    @Test
    @Disabled
    public void askNormal() throws Exception {
        final FixedRef ref = new FixedRef(BUS_ID, ACTOR_ROLE, "1");
        final FixedRef other = new FixedRef(BUS_ID, ACTOR_ROLE, "2");
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<Object> finalRes = new AtomicReference<>();
        final AtomicReference<Throwable> finalError = new AtomicReference<>();
        final ActorRunnable<TestActor> runnable = new ActorRunnable<>("runnable", testActor1 -> {
            testActor1.ask(other, new ActorRunnable<TestActor>("anonymous", testActor2 -> {
                testActor2.answer(SsNode.NodeTaskCmd.getDefaultInstance());
            })).onComplete((res, err) -> {
                finalRes.set(res);
                finalError.set(err);
                latch.countDown();
            });
        });
        ActorSendMsgUtils.send(ref, runnable);
        Assertions.assertTrue(latch.await(5, TimeUnit.SECONDS));
        Assertions.assertSame(SsNode.NodeTaskCmd.getDefaultInstance(), finalRes.get());
        Assertions.assertNull(finalError.get());
    }

    @DisplayName("超时ask请求")
    @Test
    public void askTimeOut() throws Exception {
        final FixedRef ref = new FixedRef(BUS_ID, ACTOR_ROLE, "1");
        final FixedRef other = new FixedRef(BUS_ID, ACTOR_ROLE, "2");
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<Object> finalRes = new AtomicReference<>();
        final AtomicReference<Throwable> finalError = new AtomicReference<>();
        final ActorRunnable<TestActor> runnable = new ActorRunnable<>("runnable", testActor -> {
            testActor.ask(other, new ActorRunnable<TestActor>("anonymous", testActor1 -> {
            })).onComplete((res, err) -> {
                finalRes.set(res);
                finalError.set(err);
                latch.countDown();
            });
        });
        ActorSendMsgUtils.send(ref, runnable);
        Assertions.assertTrue(latch.await(5, TimeUnit.SECONDS));
        Assertions.assertNull(finalRes.get());
        Assertions.assertTrue(finalError.get() instanceof GeminiCompletionStage.GeminiStageTimeoutException);
    }

    @Disabled
    @DisplayName("异常ask请求")
    @Test
    public void askException() throws Exception {
        final FixedRef ref = new FixedRef(BUS_ID, ACTOR_ROLE, "1");
        final FixedRef other = new FixedRef(BUS_ID, ACTOR_ROLE, "2");
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<Object> finalRes = new AtomicReference<>();
        final AtomicReference<Throwable> finalError = new AtomicReference<>();
        final ActorRunnable<TestActor> runnable = new ActorRunnable<>("runnable", testActor1 -> {
            testActor1.ask(other, new ActorRunnable<TestActor>("anonymous", testActor2 -> {
                throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION, "");
            })).onComplete((res, err) -> {
                finalRes.set(res);
                finalError.set(err);
                latch.countDown();
            });
        });
        ActorSendMsgUtils.send(ref, runnable);
        Assertions.assertTrue(latch.await(5, TimeUnit.SECONDS));
        Assertions.assertNull(finalRes.get());
        Assertions.assertTrue(finalError.get() instanceof GeminiException);
        Assertions.assertEquals(ErrorCode.PARAM_PARAMETER_EXCEPTION.getCodeId(), ((GeminiException) finalError.get()).getCodeId());
    }


    @DisplayName("包含多个onComplete的ask请求(不建议业务使用)")
    @Test
    public void askMultipleOnComplete() throws InterruptedException {
        final FixedRef ref = new FixedRef(BUS_ID, ACTOR_ROLE, "1");
        final FixedRef other = new FixedRef(BUS_ID, ACTOR_ROLE, "2");
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<Object> finalRes = new AtomicReference<>();
        final AtomicReference<Throwable> finalError = new AtomicReference<>();
        final ActorRunnable<TestActor> runnable = new ActorRunnable<>("runnable", testActor -> {
            final GeminiCompletionStage<Object> ask = testActor.ask(other, new ActorRunnable<TestActor>("anonymous", testActor1 -> {
            }));

            ask.onComplete((res, err) -> {
                finalRes.set(res);
                finalError.set(err);
                latch.countDown();
            });
        });
        ActorSendMsgUtils.send(ref, runnable);
        Assertions.assertTrue(latch.await(5, TimeUnit.SECONDS));
        Assertions.assertNull(finalRes.get());
        Assertions.assertTrue(finalError.get() instanceof GeminiCompletionStage.GeminiStageTimeoutException);

    }


}

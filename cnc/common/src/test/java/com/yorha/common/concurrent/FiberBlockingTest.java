package com.yorha.common.concurrent;

import com.google.common.collect.Maps;
import com.yorha.common.concurrent.dispatcher.GeminiDispatcherFactory;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

public class FiberBlockingTest {

    /**
     * 测试1： 阻塞测试，单线程fiber间死锁。
     *
     * @throws InterruptedException
     */
    public void testA() throws InterruptedException {
        Map<String, String> m = Maps.newConcurrentMap();
        IGeminiDispatcher geminiDispatcher = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 100);
        CountDownLatch latch = new CountDownLatch(2);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            System.out.println("test1");
            m.computeIfAbsent("1", (k) -> {
                LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
                return "";
            });
            System.out.println("test2");
            latch.countDown();
        }));
        Thread.sleep(1);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            System.out.println("test2");
            m.computeIfAbsent("1", (k) -> {
                return "";
            });
            latch.countDown();
        }));

        latch.await(10000, TimeUnit.SECONDS);
    }

    // ThreadPoolExecutor pin问题复现
    public void testA1() throws InterruptedException {
        Map<String, String> m = Maps.newConcurrentMap();
        ThreadPoolExecutor schedulerExecutor = new ThreadPoolExecutor(
                1,
                2,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1),
                Thread.ofPlatform().name("scheduler-", 0).daemon(false).factory(),
                (t, e) -> System.out.println(e)
        );
        // 以ForkJoinPool作为调度器，线程池中的线程120秒未活动，则被淘汰。
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                100,        // 理论上协程占用堆外34MB，实际上占用10-20MB（由于操作系统的内存管理是惰性的，对于已申请的内存虽然会分配地址空间，但并不会直接占用物理内存）
                5000,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(64), // 超过capacity会额外新建worker（上限maximumPoolSize）
                Thread.ofVirtual()
                 //       .scheduler(schedulerExecutor)
                        .name("worker-", 0).factory(),
                (r, e) -> {
                    System.out.println(e);
                }
        );
        CountDownLatch latch = new CountDownLatch(2);
        // 1 进入synchronized字段，pin住线程，
        executor.execute(NamedRunnableWithId.valueOf(1, "1", () -> {

            System.out.println("test1 " + Thread.currentThread());
            m.computeIfAbsent("1", (k) -> {
                LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
                return "";
            });
            System.out.println("test1 after " + Thread.currentThread());
            latch.countDown();
        }));
        Thread.sleep(1);
        executor.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            System.out.println("test2 " + Thread.currentThread());
            m.computeIfAbsent("1", (k) -> {
                return "";
            });
            System.out.println("test2 end " +Thread.currentThread());
            latch.countDown();
        }));

        latch.await();
        executor.shutdown();
        executor.awaitTermination(1000, TimeUnit.SECONDS);
        schedulerExecutor.shutdown();
        schedulerExecutor.awaitTermination(1000, TimeUnit.SECONDS);
    }

    // 和testA1对比，扩容线程，避免pin死循环。
    public void testA2() throws InterruptedException {
        Map<String, String> m = Maps.newConcurrentMap();
        ThreadPoolExecutor schedulerExecutor = new ThreadPoolExecutor(
                1,
                2,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1),
                Thread.ofPlatform().name("scheduler-", 0).daemon(false).factory(),
                (t, e) -> System.out.println(e)
        );

        // 以ForkJoinPool作为调度器，线程池中的线程120秒未活动，则被淘汰。
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                100,        // 理论上协程占用堆外34MB，实际上占用10-20MB（由于操作系统的内存管理是惰性的，对于已申请的内存虽然会分配地址空间，但并不会直接占用物理内存）
                5000,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(64), // 超过capacity会额外新建worker（上限maximumPoolSize）
                Thread.ofVirtual()
                //        .scheduler(schedulerExecutor)
                        .name("worker-", 0).factory(),
                (r, e) -> {
                    System.out.println(e);
                }
        );
        CountDownLatch latch = new CountDownLatch(2);
        // 1 进入synchronized字段，pin住线程，
        executor.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            System.out.println("test1 " + Thread.currentThread());
            m.computeIfAbsent("1", (k) -> {
                LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
                return "";
            });
            System.out.println("test1 after " + Thread.currentThread());
            latch.countDown();
        }));
        Thread.sleep(1);
        executor.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            System.out.println("test2 " + Thread.currentThread());
            m.computeIfAbsent("1", (k) -> {
                return "";
            });
            System.out.println("test2 end " +Thread.currentThread());
            latch.countDown();
        }));

        (new Thread(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            System.out.println("test3");
            executor.execute(() -> {
                System.out.println("test3 end " + Thread.currentThread());
            });
        })).start();

        latch.await();
        executor.shutdown();
        executor.awaitTermination(1000, TimeUnit.SECONDS);
        schedulerExecutor.shutdown();
        schedulerExecutor.awaitTermination(1000, TimeUnit.SECONDS);
        System.out.println("end");
    }

    /**
     * 测试2，使用多个线程，fiber间死锁接触
     *
     * @throws InterruptedException
     */
    public void testB() throws InterruptedException {
        Map<String, String> m = Maps.newConcurrentMap();
        IGeminiDispatcher geminiDispatcher = GeminiDispatcherFactory.buildFiberDispatcher("timi", 2, 100);
        CountDownLatch latch = new CountDownLatch(2);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            m.computeIfAbsent("1", (k) -> {
                LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
                return "";
            });
            latch.countDown();
        }));
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            m.computeIfAbsent("1", (k) -> {
                return "";
            });
            latch.countDown();
        }));

        latch.await(10000, TimeUnit.SECONDS);
    }

    /**
     * computeIfAbsent bug样例，导致spin跑满cpu。
     *
     * @throws InterruptedException
     */
    public void testC() throws InterruptedException {
        Map<String, String> m = Maps.newConcurrentMap();
        IGeminiDispatcher geminiDispatcher = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 100);
        CountDownLatch latch = new CountDownLatch(2);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            m.computeIfAbsent("1", (k) -> {
                m.put("1", "2");
                return "";
            });
            latch.countDown();
        }));

        latch.await(10000, TimeUnit.SECONDS);
    }

    /**
     * ReentrantLock 的park测试。
     *
     * @throws InterruptedException
     */
    public void testD() throws InterruptedException {
        Object syncLock = new Object();
        ReentrantLock lock = new ReentrantLock();
        IGeminiDispatcher geminiDispatcher = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 100);
        CountDownLatch latch = new CountDownLatch(2);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            System.out.println("hello-0");
            lock.lock();
        }));
        Thread.sleep(1);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            synchronized (syncLock) {
                try {
                    System.out.println("hello1");
                    lock.lock();
                    System.out.println("hello1-2");
                } finally {
                    lock.unlock();
                }
            }
            latch.countDown();
        }));
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            LockSupport.parkNanos(100);
            System.out.println("hello3");
            synchronized (syncLock) {
                System.out.println("hello2");
                lock.unlock();
            }
            latch.countDown();
        }));

        latch.await(10000, TimeUnit.SECONDS);
    }

    /**
     * ReentrantLock的调度测试。
     *
     * @throws InterruptedException
     */
    public void testF() throws InterruptedException {
        ReentrantLock lock = new ReentrantLock();
        IGeminiDispatcher geminiDispatcher = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 100);
        CountDownLatch latch = new CountDownLatch(2);
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            lock.lock();
        }));
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            try {
                System.out.println("hello1");
                lock.lock();
            } finally {
                lock.unlock();
            }
            latch.countDown();
        }));
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            System.out.println("hello2");
            lock.unlock();
            latch.countDown();
        }));

        latch.await(10000, TimeUnit.SECONDS);
    }

    /**
     * synchronized park测试。
     *
     * @throws InterruptedException
     */
    public void testG() throws InterruptedException {
        Object syncLock = new Object();
        IGeminiDispatcher geminiDispatcher = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 100);
        CountDownLatch latch = new CountDownLatch(2);

        geminiDispatcher.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
            synchronized (syncLock) {
                System.out.println("hello1");
                LockSupport.parkNanos(1);
            }
            latch.countDown();
        }));
        geminiDispatcher.execute(NamedRunnableWithId.valueOf(2, "2", () -> {
            System.out.println("hello3");
            synchronized (syncLock) {
                System.out.println("hello2");
            }
            latch.countDown();
        }));

        latch.await(10000, TimeUnit.SECONDS);
    }

    /**
     * synchronized park测试。
     *
     * @throws InterruptedException
     */
    public void testH() throws InterruptedException {
        Object syncLock = new Object();
        CountDownLatch latch = new CountDownLatch(3);

        System.setProperty("jdk.defaultScheduler.parallelism", String.valueOf(2));
        Thread.startVirtualThread(() -> {
            synchronized (syncLock) {
                System.out.println("hello1");
                LockSupport.parkNanos(1);
            }
            latch.countDown();
        });

        Thread.startVirtualThread(() -> {
            System.out.println("hello3");
            synchronized (syncLock) {
                System.out.println("hello2");
            }
            latch.countDown();
        });

        Thread.startVirtualThread(() -> {
            System.out.println("hello5");
            synchronized (syncLock) {
                System.out.println("hello4");
            }
            latch.countDown();
        });

        latch.await(10000, TimeUnit.SECONDS);
    }
}

package com.yorha.common.utils.shape.nonUnitTest;


import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Sector;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.text.MessageFormat;

class SectorTest extends JFrame {

    public static void main(String[] args) {
        new SectorTest();
    }

    public SectorTest() {
        setTitle("绘制几何图形");
        setBounds(0, 0, 2000, 1500);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setVisible(true);

        Container c = getContentPane();
        MyCanvas canvas = new MyCanvas();
        c.add(canvas);
    }


    private class MyCanvas extends Canvas implements MouseListener {

        private Sector sector;

        @Override
        public void paint(Graphics g) {
            // 碰撞包含判断测试
//            collisionContainsJudgment((Graphics2D) g);

            // 扇形和其他图形的碰撞测试
            crashTest((Graphics2D) g);
        }

        /**
         * 扇形和其他图形的碰撞测试
         */
        private void crashTest(Graphics2D g) {
            Point a = Point.valueOf(1000, 1000);
            Point b = Point.valueOf(100, 0);
            sector = Sector.valueOf(a, b, 100, 90);
            Graphics2D g2 = TestUtils.initGraphics(g);

            g2.setColor(Color.BLACK);
            Point center = sector.getCenter();

            g2.drawOval(center.getX() - sector.getR(), center.getY() - sector.getR(), sector.getR() * 2, sector.getR() * 2);
            g2.drawString(MessageFormat.format(" A ({0},{1})", a.getX(), a.getY()), a.getX(), a.getY());
            g2.drawString(MessageFormat.format(" B ({0},{1})", b.getX(), b.getY()), b.getX(), b.getY());
            g2.drawLine(center.getX(), center.getY(), sector.getPointA().getX(), sector.getPointA().getY());
            g2.drawLine(center.getX(), center.getY(), sector.getPointB().getX(), sector.getPointB().getY());
            int i = 0;
            while (true) {
                i++;
                Circle circle = Circle.valueOf(RandomUtils.nextInt(500, 1500), RandomUtils.nextInt(500, 1500), RandomUtils.nextInt(200, 300));
                g2.setColor(Color.RED);
                g2.drawString(MessageFormat.format(" {0}: ({1},{2})", i, circle.getX(), circle.getY()), circle.getX(), circle.getY());
                boolean contact = ShapeUtils.isContact(circle, sector);
                if (contact) {
                    g2.setColor(Color.GREEN);
                }
                g2.drawOval(circle.getX() - circle.getR(), circle.getY() - circle.getR(), circle.getR() * 2, circle.getR() * 2);
                System.out.println("" + i + ":" + contact);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        /**
         * 碰撞包含判断测试
         */
        private void collisionContainsJudgment(Graphics2D g) {

            Point a = Point.valueOf(1000, 800);
            Point b = Point.valueOf(900, 1038);
            sector = Sector.valueOf(a, b, 350, 90);
            this.addMouseListener(this);
            Graphics2D g2 = TestUtils.initGraphics(g);
            this.drawPoint(true, g2, b, Color.RED);
            this.drawPoint(true, g2, a, Color.BLACK);

            g2.drawLine(a.getX(), a.getY(), b.getX(), b.getY());


            this.drawArc(g2, sector, Color.BLACK);
            for (int i = 0; i < 1000; i++) {
                Point test = sector.getRandomPoint();
                //  System.out.println(test.getX() + ":" + test.getY());
                this.drawPoint(false, g2, test, Color.DARK_GRAY);
            }
        }

        public void drawPoint(boolean show, Graphics g2, Point point, Color c) {
            g2.setColor(c);
            if (show) {
                g2.drawString(MessageFormat.format("({0},{1})", point.getX(), point.getY()), point.getX(), point.getY());
            }

            g2.fillOval(point.getX(), point.getY(), 4, 4);
        }


        public void drawArc(Graphics g2, Sector sector, Color c) {
            g2.setColor(c);
            // g2.drawArc();
            Point center = sector.getCenter();
            g2.drawOval(center.getX() - sector.getR(), center.getY() - sector.getR(), sector.getR() * 2, sector.getR() * 2);
            Point a = sector.getPointA();
            g2.drawString(MessageFormat.format(" A ({0},{1})", a.getX(), a.getY()), a.getX(), a.getY());
            Point b = sector.getPointB();
            g2.drawString(MessageFormat.format(" B ({0},{1})", b.getX(), b.getY()), b.getX(), b.getY());
            g2.drawLine(center.getX(), center.getY(), a.getX(), a.getY());
            g2.drawLine(center.getX(), center.getY(), b.getX(), b.getY());
            // g2.drawOval();//椭圆
            // g2.drawPolygon();多边形
            g2.setColor(Color.RED);
            AABB aabb = sector.getAABB();
            Point leftBottom = Point.valueOf(aabb.getLeft(), aabb.getBottom());
            Point rightBottom = Point.valueOf(aabb.getRight(), aabb.getBottom());
            Point leftTop = Point.valueOf(aabb.getLeft(), aabb.getTop());
            Point rightTop = Point.valueOf(aabb.getRight(), aabb.getTop());
            g2.drawString(MessageFormat.format(" ({0},{1})", leftBottom.getX(), leftBottom.getY()), leftBottom.getX(), leftBottom.getY());
            g2.drawString(MessageFormat.format(" ({0},{1})", rightBottom.getX(), rightBottom.getY()), rightBottom.getX(), rightBottom.getY());
            g2.drawString(MessageFormat.format(" ({0},{1})", leftTop.getX(), leftTop.getY()), leftTop.getX(), leftTop.getY());
            g2.drawString(MessageFormat.format(" ({0},{1})", rightTop.getX(), rightTop.getY()), rightTop.getX(), rightTop.getY());
            g2.drawLine(leftBottom.getX(), leftBottom.getY(), rightBottom.getX(), rightBottom.getY());
            g2.drawLine(leftTop.getX(), leftTop.getY(), rightTop.getX(), rightTop.getY());
            g2.drawLine(leftTop.getX(), leftTop.getY(), leftBottom.getX(), leftBottom.getY());
            g2.drawLine(rightTop.getX(), rightTop.getY(), rightBottom.getX(), rightBottom.getY());
        }


        @Override
        public void mouseClicked(MouseEvent e) {

        }

        @Override
        public void mousePressed(MouseEvent e) {


        }

        @Override
        public void mouseReleased(MouseEvent e) {
            int x = (int) e.getPoint().getX();
            int y = (int) e.getPoint().getY();
            if (sector.containsPoint(x, y)) {
                System.out.println("in");
            } else {
                System.out.println("out");
            }
        }

        @Override
        public void mouseEntered(MouseEvent e) {

        }

        @Override
        public void mouseExited(MouseEvent e) {

        }


    }


}

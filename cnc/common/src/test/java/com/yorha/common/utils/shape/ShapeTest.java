package com.yorha.common.utils.shape;

import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.vector.Vector2f;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * 图形单元测试
 *
 * <AUTHOR>
 * 2021年11月03日 17:10:00
 */
public class ShapeTest {
    @Test
    public void contactTest() {
        Point point = Point.valueOf(100, 100);
        Line line = Line.valueOf(point, Point.valueOf(100, 200));
        Circle circle = Circle.valueOf(point.getX(), point.getY(), 100);
        Sector sector = Sector.valueOf(Point.valueOf(50, 100), point, 100, 100);
        Rectangle rectangle = Rectangle.valueOf(Point.valueOf(100, 100), Vector2f.valueOf(100, 100), 100, 100);

        Assertions.assertTrue(ShapeUtils.isContact(point, circle));
        Assertions.assertTrue(ShapeUtils.isContact(point, sector));
        Assertions.assertTrue(ShapeUtils.isContact(point, rectangle));
        Assertions.assertTrue(ShapeUtils.isContact(line, circle));
        Assertions.assertTrue(ShapeUtils.isContact(circle, sector));
        Assertions.assertTrue(ShapeUtils.isContact(circle, rectangle));
    }

    @Test
    public void testSectorContainsPoint() {
        // 这里的点位置数据来源于一次快乐的debug
        // 已受理，择日干掉
        Point point = Point.valueOf(24375, 16160);
        Point center = Point.valueOf(22839, 17471);
        Sector sector = Sector.valueOf(center, point, 3000, 120);
        Assertions.assertTrue(sector.containsPoint(point));
    }

    @Test
    public void testCircleLineContact() {
        // 一次绕城BUG
        Circle circle = Circle.valueOf(39744, 14783, 500);
        Line line = Line.valueOf(Point.valueOf(37780, 16084), Point.valueOf(42156, 13383));
        Assertions.assertTrue(ShapeUtils.isContact(circle, line));

        // 额外补充一些
        circle = Circle.valueOf(100, 100, 50);
        line = Line.valueOf(Point.valueOf(0, 0), Point.valueOf(200, 50));
        Assertions.assertFalse(ShapeUtils.isContact(circle, line));
        line = Line.valueOf(Point.valueOf(0, 0), Point.valueOf(50, 200));
        Assertions.assertFalse(ShapeUtils.isContact(circle, line));
        line = Line.valueOf(Point.valueOf(0, 0), Point.valueOf(100, 50));
        Assertions.assertTrue(ShapeUtils.isContact(circle, line));
        line = Line.valueOf(Point.valueOf(0, 150), Point.valueOf(150, 150));
        Assertions.assertTrue(ShapeUtils.isContact(circle, line));
        line = Line.valueOf(Point.valueOf(0, 200), Point.valueOf(200, 100));
        Assertions.assertTrue(ShapeUtils.isContact(circle, line));
    }

    @Test
    public void testCircleLineContactNew() {
        // 一次新的绕城BUG
        Circle circle = Circle.valueOf(12750, 18000, 749);
        Line line = Line.valueOf(Point.valueOf(60000, 18000), Point.valueOf(12750, 18000));
        ImmutablePair<Point, Point> crossPointByLine = ShapeUtils.getShapeIntersection(circle, line);
        Assertions.assertNotNull(crossPointByLine.getLeft());
        Assertions.assertNull(crossPointByLine.getRight());

    }

    @Test
    public void testCircleLineContactNewNew() {
        // 一次新的新绕城BUG
        Circle circle = Circle.valueOf(318000, 273000, 700);
        Line line = Line.valueOf(Point.valueOf(306966, 226350), Point.valueOf(318378, 274824));
        ImmutablePair<Point, Point> crossPointByLine = ShapeUtils.getShapeIntersection(circle, line);
        Assertions.assertNotNull(crossPointByLine.getLeft());
        Assertions.assertNotNull(crossPointByLine.getRight());
    }

    /**
     * Point{x=24593, y=36593}, Point{x=26030, y=38030}
     */
    @Test
    public void testCircleLineContactNewNewNew() {
        // 目标的阻挡圈过滤掉
        Circle circle = Circle.valueOf(86000, 276000, 150);
        Line line = Line.valueOf(Point.valueOf(86226, 276226), Point.valueOf(85394, 275394));
        ImmutablePair<Point, Point> crossPoints = ShapeUtils.getShapeIntersection(circle, line);

        Point leftPoint = crossPoints.getLeft();
        Point rightPoint = crossPoints.getRight();

        Vector2f oneV = Vector2f.getVectorFromPointToPoint(circle.getCircleCenter(), leftPoint).unitization();
        Vector2f otherV = Vector2f.getVectorFromPointToPoint(circle.getCircleCenter(), rightPoint).unitization();


        Vector2f midV = Vector2f.getMidVector(oneV, otherV);

        // 扩大一圈
        Circle largerCircle = circle.newCircleWithNewRadius((int) (circle.getR() * 1.2));
        Point tempPoint = largerCircle.getPointFromVector(oneV);
        Assertions.assertTrue(tempPoint.getX() > 0 && tempPoint.getY() > 0);
        tempPoint = largerCircle.getPointFromVector(otherV);
        Assertions.assertTrue(tempPoint.getX() > 0 && tempPoint.getY() > 0);
        tempPoint = largerCircle.getPointFromVector(midV);
        Assertions.assertTrue(tempPoint.getX() > 0 && tempPoint.getY() > 0);


        Point.calDisBetweenTwoPoint(leftPoint, rightPoint);
        // 再多加入一些点
        Vector2f l1V = Vector2f.add(oneV, midV).unitization();
        Point point1 = largerCircle.getPointFromVector(l1V);
        Vector2f l2V = Vector2f.add(l1V, midV).unitization();
        Point point2 = largerCircle.getPointFromVector(l2V);
        Vector2f r1V = Vector2f.add(otherV, midV).unitization();
        Point point3 = largerCircle.getPointFromVector(r1V);
        Vector2f r2V = Vector2f.add(r1V, midV).unitization();
        Point point4 = largerCircle.getPointFromVector(r2V);

        Assertions.assertTrue(point1.getX() > 0 && point1.getY() > 0);
        Assertions.assertTrue(point2.getX() > 0 && point2.getY() > 0);
        Assertions.assertTrue(point3.getX() > 0 && point3.getY() > 0);
        Assertions.assertTrue(point4.getX() > 0 && point4.getY() > 0);
    }

    @Test
    public void testShapeCircle() {
        Assertions.assertTrue(ShapeUtils.includedAngle(-141, 158, 278));
        Assertions.assertTrue(ShapeUtils.includedAngle(-40, -100, 20));
        Assertions.assertFalse(ShapeUtils.includedAngle(-110, -100, 20));
        Assertions.assertFalse(ShapeUtils.includedAngle(60, -100, 20));
        Assertions.assertTrue(ShapeUtils.includedAngle(400, 30, 60));
        Assertions.assertFalse(ShapeUtils.includedAngle(-120, 10, 60));
    }


}

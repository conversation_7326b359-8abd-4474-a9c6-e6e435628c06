package com.yorha.common.concurrent;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;
import org.junit.jupiter.api.*;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@DisplayName("新版协程测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class VirtualThreadTest {
    private static final Logger logger = LogManager.getLogger(VirtualThreadTest.class);

    private int num=0;
    private int i;
    @Test
    @Order(1)
    @DisplayName("打印CarrierThread默认的线程数量")
    public void printParallelism() throws InterruptedException {

        logger.info("cpu num: {}",Runtime.getRuntime().availableProcessors());
        ExecutorService  executorService = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS,
                new MpscBlockingConsumerArrayQueue<>(1000),
                Thread.ofVirtual().name("virtual").factory(),
                (r, executor) -> logger.error("executor {} reject  {}", executor, r)
        );
     //   AtomicInteger num = new AtomicInteger();


        for  (int i=0;i<1000;i++) {
            executorService.execute(() -> {
              //  logger.info("CarrierThread: {},num: {}",VirtualThreadUtils.currentCarrierThread(),num.getAndIncrement());
                logger.info("CarrierThread: {},num: {}",Thread.currentThread(),++num);
                Thread.yield();
//                LockSupport.parkNanos(Duration.ofMillis(1).toNanos());
            });
        }

//
//        logger.info("Parallelism: {}", ((ForkJoinPool) executorService).getParallelism());

//        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
//        executor.execute(() -> {
//            logger.info("CarrierThread: {}", VirtualThreadUtils.currentCarrierThread());
//
//        });

        Thread.sleep(1000);
//        System.out.println("Parallelism: " + ((ForkJoinPool) executor).getParallelism());
        executorService.shutdown();
    }


    @Test
    @Order(2)
    @DisplayName("协程执行案例2")
    public void printFiber4execute() throws InterruptedException {
        GeminiThreadPoolExecutor schedulerExecutor = ConcurrentHelper.newFixedThreadExecutor("game", 1, 0, false);
        ThreadFactory factory=ConcurrentHelper.newFiberFactory("game",schedulerExecutor);
        for  (int i=0;i<100;i++) {
            schedulerExecutor.execute(() -> {
                logger.info("CarrierThread: {},num: {}",Thread.currentThread(),++num);
                Thread.yield();
            });
        }
        Thread.sleep(1000);
        schedulerExecutor.shutdown();
    }


    @Test
    @Order(3)
    @DisplayName("协程执行案例3")
    public void printFiber4newThread() throws InterruptedException {
        GeminiThreadPoolExecutor schedulerExecutor = ConcurrentHelper.newFixedThreadExecutor("game", 1, 0, false);
        ThreadFactory factory=ConcurrentHelper.newFiberFactory("game",schedulerExecutor);
        for  (int i=0;i<100;i++) {
            factory.newThread(() -> {
                logger.info("CarrierThread: {},num: {}",Thread.currentThread(),++num);
            }).start();
        }
        Thread.sleep(1000);
        schedulerExecutor.shutdown();
    }


}

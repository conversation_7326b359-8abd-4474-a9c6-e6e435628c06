package com.yorha.common;


import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import sun.misc.Unsafe;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.StampedLock;

import static com.mongodb.internal.Locks.withLock;
import static java.lang.Thread.sleep;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Disabled
public class FiberPinnedTest {
    private static final Logger LOGGER = LogManager.getLogger(FiberPinnedTest.class);

    private static final Unsafe UNSAFE;

    public static Unsafe getUnsafe() throws NoSuchFieldException, IllegalAccessException {
        Field f = Unsafe.class.getDeclaredField("theUnsafe");
        f.setAccessible(true);
        return (Unsafe) f.get(null);
    }


    static {
        try {
            UNSAFE = getUnsafe();


        } catch (Exception ex) {
            throw new Error(ex);
        }
    }

    @BeforeAll
    public static void setup() {
        System.setProperty("jdk.virtualThreadScheduler.parallelism", "1");
        System.setProperty("jdk.virtualThreadScheduler.maxPoolSize", "1");
        System.setProperty("jdk.virtualThreadScheduler.minRunnable", "1");
    }



    @Test
    @DisplayName("协程共享平台线程 + StampedLock 导致整体卡死")
    public void testFiberStampedLockWithSharedCarrierThread() throws InterruptedException {
        // 使用固定大小的虚拟线程池，强制复用少量平台线程（例如 1 个）
        ThreadFactory factory = Thread.ofVirtual().name("vt-", 1).factory();
        ExecutorService executor = Executors.newFixedThreadPool(1, factory);
        // 创建写锁
        Lock lock = new StampedLock().asWriteLock();
        // 用于同步确认任务是否完成
        CountDownLatch latch = new CountDownLatch(3);
        // 第一个任务：获取锁并休眠一段时间，模拟长时间占用
        factory.newThread(() -> {
            lock.lock();
            try {
                LOGGER.info("{},Task 1: 获取锁，开始 sleep...", Thread.currentThread());
                try {
                    sleep(5000); // 模拟长时间操作
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                LOGGER.info("{},Task 1: 完成", Thread.currentThread());
            } finally {
                lock.unlock();
                latch.countDown();
            }
        }).start();
        // 第二个任务：尝试获取锁，但会被阻塞
        factory.newThread(() -> {
            LOGGER.info("{},Task 2: 等待获取锁...", Thread.currentThread());
            lock.lock();
            try {
                LOGGER.info("{},Task 2: 成功获取锁并执行", Thread.currentThread());
            } finally {
                lock.unlock();
                latch.countDown();
            }

        }).start();

        // 第三个任务：不涉及锁，但与前两个共享平台线程
        factory.newThread(() -> {
            LOGGER.info("{},Task 3: 开始执行", Thread.currentThread());
            latch.countDown();
            LOGGER.info("{},Task 3: 完成执行", Thread.currentThread());
        }).start();

        // 设置超时等待，验证是否所有任务都能完成
        boolean completed = latch.await(6, TimeUnit.SECONDS);
        executor.shutdown();

        // 如果 Task 3 没有执行，说明平台线程被卡住
        assertTrue(completed, () -> {
            String message = "平台线程可能被阻塞，Task 3 未按时执行";
            LOGGER.info(message);
            return message;
        });
    }





    @Test
    @DisplayName("协程共享平台线程 + ReentrantLock 整体运行正常")
    public void testFiberReentrantLockWithSharedCarrierThread() throws InterruptedException {
        // 使用固定大小的虚拟线程池，强制复用少量平台线程（例如 1 个）
        ThreadFactory factory = Thread.ofVirtual().name("vt-", 1).factory();


        ExecutorService executor = Executors.newFixedThreadPool(1, factory);
        // 创建可重入的写锁
        Lock lock = new ReentrantLock();

        // 用于同步确认任务是否完成
        CountDownLatch latch = new CountDownLatch(3);
        factory.newThread(() -> {
        });
        // 第一个任务：获取锁并休眠一段时间，模拟长时间占用
        factory.newThread(() -> {
            lock.lock();
            try {
                LOGGER.info("{},Task 1: 获取锁，开始 sleep...", Thread.currentThread());
                try {
                    sleep(5000); // 模拟长时间操作
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                LOGGER.info("{},Task 1: 完成", Thread.currentThread());
            } finally {
                lock.unlock();
                latch.countDown();
            }
        }).start();
        // 第二个任务：尝试获取锁，但会被阻塞
        factory.newThread(() -> {
            LOGGER.info("{},Task 2: 等待获取锁...", Thread.currentThread());
            lock.lock();
            try {
                LOGGER.info("{},Task 2: 成功获取锁并执行", Thread.currentThread());
            } finally {
                lock.unlock();
                latch.countDown();
            }

        }).start();

        // 第三个任务：不涉及锁，但与前两个共享平台线程
        factory.newThread(() -> {
            LOGGER.info("{},Task 3: 开始执行", Thread.currentThread());
            latch.countDown();
            LOGGER.info("{},Task 3: 完成执行", Thread.currentThread());
        }).start();

        // 设置超时等待，验证是否所有任务都能完成
        boolean completed = latch.await(6, TimeUnit.SECONDS);
        executor.shutdown();

        // 如果 Task 3 没有执行，说明平台线程被卡住
        assertTrue(completed, () -> {
            String message = "平台线程可能被阻塞，Task 3 未按时执行";
            LOGGER.info(message);
            return message;
        });
    }


    @Test
    @DisplayName("协程用park")
    public void testFiberPark() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(1);
        Thread vtThread = Thread.ofVirtual().name("park vt thread")
                // .scheduler(singleThreadSchedulerExecutor)
                .start(() -> {
                    UNSAFE.park(false, 0);
                    latch.countDown();
                });
        sleep(1000);
        UNSAFE.unpark(vtThread);
        final boolean result = latch.await(2, TimeUnit.SECONDS);
        assertFalse(result);
    }

    @Test
    @DisplayName("协程用lockSupport")
    public void testFiberLockSupport() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(1);
        Thread vtThread = Thread.ofVirtual().name("park vt thread")
                //.scheduler(singleThreadSchedulerExecutor)
                .start(() -> {
                    LockSupport.park();
                    latch.countDown();
                });
        sleep(1000);
        LockSupport.unpark(vtThread);

        final boolean result = latch.await(2, TimeUnit.SECONDS);
        assertTrue(result);
    }

    @Test
    @DisplayName("线程程用park")
    public void testThreadPark() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(1);
        Thread thread = Thread.ofPlatform().name("park thread").start(() -> {
            UNSAFE.park(false, 0);
            latch.countDown();
        });
        sleep(1000);
        UNSAFE.unpark(thread);

        final boolean result = latch.await(2, TimeUnit.SECONDS);
        assertTrue(result);
    }

    @Test
    @DisplayName("线程用lockSupport")
    public void testThreadLockSupport() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(1);
        Thread thread = Thread.ofPlatform().name("park thread").start(() -> {
            LockSupport.park();
            latch.countDown();
        });
        sleep(1000);
        LockSupport.unpark(thread);

        final boolean result = latch.await(2, TimeUnit.SECONDS);
        assertTrue(result);
    }
}

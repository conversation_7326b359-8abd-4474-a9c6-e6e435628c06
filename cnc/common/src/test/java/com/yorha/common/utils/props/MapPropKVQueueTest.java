package com.yorha.common.utils.props;

import com.yorha.gemini.props.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MapPropKVQueueTest {
    private static final class TestIntegerElement extends AbstractContainerElementNode<Integer> {
        private Integer privateKey;
        /**
         * 是否标脏
         */
        public boolean isDirty;

        public TestIntegerElement(ITreeMarkContainer parent, int fieldIndex, int fieldCount) {
            super(parent, fieldIndex, fieldCount);
        }

        @Override
        public Integer getPrivateKey() {
            return this.privateKey;
        }


        @Override
        public void mark(int index) {
            if (index < 0 || index >= this.getFieldCount()) {
                throw new IndexOutOfBoundsException(Integer.toString(index));
            }
            this.isDirty = true;
        }

        @Override
        public void unMark(int index) {
            if (index < 0 || index >= this.getFieldCount()) {
                throw new IndexOutOfBoundsException(Integer.toString(index));
            }
            this.isDirty = false;
        }

        @Override
        public boolean hasMark(final int index) {
            if (index < 0 || index >= this.getFieldCount()) {
                throw new IndexOutOfBoundsException(Integer.toString(index));
            }
            return isDirty;
        }

        @Override
        public boolean hasAnyMark() {
            return isDirty;
        }

        @Override
        public boolean compareDataTo(IMarkContainer node) {
            return false;
        }

        @Override
        public void markAll() {
            this.isDirty = true;
        }

        @Override
        public void unMarkAll() {
            this.isDirty = false;
        }
    }

    private static final class TestIntegerMap extends AbstractMapNode<Integer, TestIntegerElement> {

        /**
         * Creates a DataMarker of the specified size, initialized to zeros.
         *
         * @param parent     父节点
         * @param fieldIndex 子节点但位于父节点的属性索引
         */
        public TestIntegerMap(AbstractPropNode parent, int fieldIndex) {
            super(parent, fieldIndex);
        }

        @Override
        public TestIntegerElement addEmptyValue(Integer integer) {
            TestIntegerElement element = new TestIntegerElement(this, 0, 1);
            element.privateKey = integer;
            this.put(integer, element);
            return element;
        }
    }

    @Test
    public void offer() {
        final TestIntegerMap map = new TestIntegerMap(null, 0);
        MapPropKVQueue<Integer, TestIntegerElement> propQueueProxy = new MapPropKVQueue<>(2, map, (k1, k2) -> k1.getPrivateKey().compareTo(k2.privateKey));
        Assertions.assertTrue(propQueueProxy.isEmpty());
        Assertions.assertNull(propQueueProxy.peekFirst());
        Assertions.assertNull(propQueueProxy.peekLast());
        Assertions.assertNull(propQueueProxy.pollFirst());
        TestIntegerElement element = propQueueProxy.offerLast(0);
        Assertions.assertNotNull(element);
        Assertions.assertEquals(element.getPrivateKey(), 0);
        Assertions.assertFalse(propQueueProxy.isEmpty());
        Assertions.assertSame(propQueueProxy.peekFirst(), propQueueProxy.peekLast());
        Assertions.assertSame(propQueueProxy.peekFirst(), element);
        Assertions.assertSame(propQueueProxy.pollFirst(), element);
        Assertions.assertTrue(propQueueProxy.isEmpty());
        TestIntegerElement elementPair0 = propQueueProxy.offerLast(0);
        TestIntegerElement elementPair1 = propQueueProxy.offerLast(1);
        Assertions.assertFalse(propQueueProxy.isEmpty());
        Assertions.assertSame(elementPair0, propQueueProxy.peekFirst());
        Assertions.assertSame(elementPair1, propQueueProxy.peekLast());
        Assertions.assertSame(propQueueProxy.pollFirst(), elementPair0);
        Assertions.assertSame(propQueueProxy.pollFirst(), elementPair1);
        Assertions.assertTrue(propQueueProxy.isEmpty());
    }

    @Test
    public void sort() {
        final TestIntegerMap map = new TestIntegerMap(null, 0);
        map.addEmptyValue(5);
        map.addEmptyValue(2);
        map.addEmptyValue(1);
        map.addEmptyValue(4);
        map.addEmptyValue(3);
        MapPropKVQueue<Integer, TestIntegerElement> propQueueProxy = new MapPropKVQueue<>(map.size(), map, (k1, k2) -> k1.getPrivateKey().compareTo(k2.privateKey));
        Assertions.assertEquals(Stream.of(1, 2, 3, 4, 5).collect(Collectors.toList()), new ArrayList<>(propQueueProxy.keys()));
    }

    @Test
    public void values() {
        final TestIntegerMap map = new TestIntegerMap(null, 0);
        MapPropKVQueue<Integer, TestIntegerElement> propQueueProxy = new MapPropKVQueue<>(2, map, (k1, k2) -> k1.getPrivateKey().compareTo(k2.privateKey));
        propQueueProxy.offerLast(2);
        propQueueProxy.offerLast(1);
        Assertions.assertEquals(propQueueProxy.values().stream().map(TestIntegerElement::getPrivateKey).collect(Collectors.toList()), Stream.of(2, 1).collect(Collectors.toList()));
    }

    @Test
    public void removeAll() {
        final TestIntegerMap map = new TestIntegerMap(null, 0);
        final List<Integer> removeKeys = new ArrayList<>();
        MapPropKVQueue<Integer, TestIntegerElement> propQueueProxy = new MapPropKVQueue<>(10, map, (k1, k2) -> k1.getPrivateKey().compareTo(k2.privateKey));
        propQueueProxy.offerLast(0);
        propQueueProxy.offerLast(1);
        propQueueProxy.offerLast(2);
        propQueueProxy.offerLast(3);
        final TestIntegerElement element3 = propQueueProxy.peekLast();
        propQueueProxy.offerLast(4);
        final TestIntegerElement element4 = propQueueProxy.peekLast();
        propQueueProxy.offerLast(5);
        final TestIntegerElement element5 = propQueueProxy.peekLast();
        propQueueProxy.offerLast(6);
        propQueueProxy.offerLast(7);
        propQueueProxy.offerLast(8);
        propQueueProxy.offerLast(9);
        Assertions.assertThrows(RuntimeException.class, () -> propQueueProxy.offerLast(10));
        removeKeys.add(propQueueProxy.pollFirst().getPrivateKey());
        propQueueProxy.offerLast(10);
        Assertions.assertThrows(RuntimeException.class, () -> propQueueProxy.offerLast(11));
        removeKeys.add(propQueueProxy.pollFirst().getPrivateKey());
        propQueueProxy.offerLast(11);
        Assertions.assertEquals(Stream.of(0, 1).collect(Collectors.toList()), removeKeys);
        removeKeys.clear();
        removeKeys.addAll(propQueueProxy.removeTargetValues((v) -> v.getPrivateKey() < 8).stream().map(TestIntegerElement::getPrivateKey).collect(Collectors.toList()));
        Assertions.assertEquals(Stream.of(8, 9, 10, 11).collect(Collectors.toList()), removeKeys);
        propQueueProxy.pollFirst();
        Assertions.assertSame(element3, propQueueProxy.pollFirst());
        Assertions.assertSame(element4, propQueueProxy.pollFirst());
        Assertions.assertSame(element5, propQueueProxy.pollFirst());
    }

    @Test
    public void removeN() {
        final TestIntegerMap map = new TestIntegerMap(null, 0);
        MapPropKVQueue<Integer, TestIntegerElement> propQueueProxy = new MapPropKVQueue<>(10, map, (k1, k2) -> k1.getPrivateKey().compareTo(k2.privateKey));
        final TestIntegerElement testIntegerElement0 = propQueueProxy.offerLast(0);
        final TestIntegerElement testIntegerElement1 = propQueueProxy.offerLast(1);
        final TestIntegerElement testIntegerElement2 = propQueueProxy.offerLast(2);
        final TestIntegerElement testIntegerElement3 = propQueueProxy.offerLast(3);
        final List<TestIntegerElement> removeN = propQueueProxy.removeN(Stream.of(1, 3).collect(Collectors.toSet()));
        Assertions.assertSame(testIntegerElement1, removeN.get(0));
        Assertions.assertSame(testIntegerElement3, removeN.get(1));
        Assertions.assertEquals(propQueueProxy.keys(), Stream.of(0, 2).collect(Collectors.toList()));
        Assertions.assertSame(propQueueProxy.peekFirst(), testIntegerElement0);
        Assertions.assertSame(propQueueProxy.peekLast(), testIntegerElement2);
        final TestIntegerElement testIntegerElement4 = propQueueProxy.offerLast(4);
        final TestIntegerElement testIntegerElement5 = propQueueProxy.offerLast(5);
        final TestIntegerElement remove = propQueueProxy.remove(2);
        Assertions.assertSame(remove, testIntegerElement2);
        Assertions.assertEquals(propQueueProxy.keys(), Stream.of(0, 4, 5).collect(Collectors.toList()));
        Assertions.assertSame(propQueueProxy.peekLast(), testIntegerElement5);
        Assertions.assertSame(propQueueProxy.remove(4), testIntegerElement4);
        Assertions.assertEquals(propQueueProxy.getSize(), 2);
    }
}

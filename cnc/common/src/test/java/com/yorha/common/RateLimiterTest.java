package com.yorha.common;

import com.google.common.util.concurrent.RateLimiter;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;

public class RateLimiterTest {


    @DisplayName("RateLimiterTest")
    @Test
    public void rateLimiterTest() throws InterruptedException {
        RateLimiter limiter = RateLimiter.create(10, Duration.ZERO);
        int i = 0;
//        Thread.sleep(1000);
        while (i < 10) {
            boolean ret = limiter.tryAcquire();
            System.out.println(i);
            System.out.println(ret);
            i++;
        }
    }

}

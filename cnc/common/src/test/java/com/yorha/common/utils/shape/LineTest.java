package com.yorha.common.utils.shape;

import com.yorha.common.utils.ShapeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * 2021年12月03日 10:58:00
 */
public class LineTest {

    @Test
    public void testContainPoint() {
        Line line = Line.valueOf(Point.valueOf(0, 0), Point.valueOf(0, 10));
        Point containPoint = Point.valueOf(0, 5);
        Point notContainPoint = Point.valueOf(1, 5);
        Assertions.assertTrue(line.containsPoint(containPoint.getX(), containPoint.getY()));
        Assertions.assertFalse(line.containsPoint(notContainPoint.getX(), notContainPoint.getY()));
    }

    @Test
    public void testContactCircle() {
        Line line = Line.valueOf(Point.valueOf(0, 0), Point.valueOf(0, 10));
        Circle circleContact = Circle.valueOf(0, 15, 5);
        Circle circleNotContact = Circle.valueOf(-5, -1, 5);
        Assertions.assertTrue(ShapeUtils.isContact(line, circleContact));
        Assertions.assertFalse(ShapeUtils.isContact(line, circleNotContact));
    }

    @Test
    public void testContactCircle2() {
        Line line = Line.valueOf(Point.valueOf(306966, 226350), Point.valueOf(318432, 274385));
        Circle circleContact = Circle.valueOf(318000, 273000, 700);
        Assertions.assertTrue(ShapeUtils.isContact(line, circleContact));
    }
}

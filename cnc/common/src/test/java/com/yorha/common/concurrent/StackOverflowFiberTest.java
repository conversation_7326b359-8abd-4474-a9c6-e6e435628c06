package com.yorha.common.concurrent;

import com.yorha.common.wechatlog.WechatLog;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;

public class StackOverflowFiberTest {
    @BeforeAll
    public static void before() {
        WechatLog.getInstance().openScreenByGm();
    }

    @AfterAll
    public static void after() {
        WechatLog.getInstance().closeScreenByGm();
    }



}

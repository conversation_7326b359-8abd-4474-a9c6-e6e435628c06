package com.yorha.common.message;

import com.yorha.common.concurrent.DaemonThreadFactory;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.*;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.*;

@Disabled
@DisplayName("Fibber测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class FiberTest {
    private static final Logger logger = LogManager.getLogger(FiberTest.class);

    @BeforeAll
    public static void beforeAll() throws Exception {


    }

    @BeforeEach
    public void beforeEach() throws Exception {

    }

    @AfterEach
    public void afterEach() throws Exception {

    }

    @DisplayName("默认KONA切换测试")
    @RepeatedTest(10)
    public void fiberDefaultYieldWithMonitor() throws InterruptedException {
        final Object lock = new Object();
        final AtomicInteger no = new AtomicInteger();
        final AtomicInteger no1 = new AtomicInteger();
        final AtomicInteger no2 = new AtomicInteger();
        final CountDownLatch latch = new CountDownLatch(2);
        Thread.ofVirtual()
              //  .scheduler(scheduler)
                .name("no-1").start(() -> {
            synchronized (lock) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ignored) {
                }
                Thread.yield();
                no1.set(no.incrementAndGet());
                latch.countDown();
            }
        });
        Thread.ofVirtual()
              //  .scheduler(scheduler)
                .name("no-2").start(() -> {
            no2.set(no.incrementAndGet());
            latch.countDown();
        });
        Assertions.assertTrue(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertEquals(2, no1.get());
        Assertions.assertEquals(1, no2.get());
    }

    @DisplayName("KONA死锁测试")
    @Test
    public void fiberDeadLock() throws InterruptedException {
        final Object lock = new Object();
        final AtomicInteger no = new AtomicInteger();
        final AtomicInteger no1 = new AtomicInteger();
        final AtomicInteger no2 = new AtomicInteger();
        final CountDownLatch latch = new CountDownLatch(2);
        Thread.ofVirtual()
                //.scheduler(scheduler)
                .name("no-1").start(() -> {
            synchronized (lock) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ignored) {
                }
                Thread.yield();
                no1.set(no.incrementAndGet());
                latch.countDown();
            }
        });
        Thread.ofVirtual()
                //.scheduler(scheduler)
                .name("no-2").start(() -> {
            synchronized (lock) {
                no2.set(no.incrementAndGet());
                latch.countDown();
            }
        });
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertEquals(0, no1.get());
        Assertions.assertEquals(0, no2.get());
    }

    @DisplayName("默认KONA切换测试")
//    @RepeatedTest(10) 必需加上-XX:-YieldWithMonitor，关闭yield选项。
    public void fiberShutdownYieldWithMonitor() throws InterruptedException {
       final Object lock = new Object();
        final AtomicInteger no = new AtomicInteger();
        final AtomicInteger no1 = new AtomicInteger();
        final AtomicInteger no2 = new AtomicInteger();
        final CountDownLatch latch = new CountDownLatch(2);
        Thread.ofVirtual()
                //.scheduler(scheduler)
                .name("no-1").start(() -> {
            synchronized (lock) {
                Thread.yield();
                no1.set(no.incrementAndGet());
                latch.countDown();
            }
        });
        Thread.ofVirtual()
               // .scheduler(scheduler)
                .name("no-2").start(() -> {
            no2.set(no.incrementAndGet());
            latch.countDown();
        });
        Assertions.assertTrue(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertEquals(1, no1.get());
        Assertions.assertEquals(2, no2.get());
    }

    @DisplayName("KONA Pin")
    @Test
    public void fiberPin() throws InterruptedException {
       final Object lock = new Object();
        final AtomicInteger no = new AtomicInteger(0);
        final AtomicInteger no1 = new AtomicInteger(0);
        final AtomicInteger no2 = new AtomicInteger(0);
        final CountDownLatch latch = new CountDownLatch(2);
        Thread.ofVirtual()
                //.scheduler(scheduler)
                .name("no-1").start(() -> {
            synchronized (lock) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ignored) {
                }
                Thread.yield();
                no1.set(no.incrementAndGet());
                latch.countDown();
            }
        });
        Thread.ofVirtual()
               // .scheduler(scheduler)
                .name("no-2").start(() -> {
            synchronized (lock) {
                no2.set(no.incrementAndGet());
                latch.countDown();
            }
        });
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertEquals(0, no1.get());
        Assertions.assertEquals(0, no2.get());
    }

    @DisplayName("KONA 协程饥饿")
//    @Test 必需加上-XX:-YieldWithMonitor，关闭yield选项。
    public void fiberHungry() throws InterruptedException {
        /**
         * 1. 如果未加上-XX:-YieldWithMonitor，本单测会失败，即所有线程协程协调后各自推出。
         * 2. 加上-XX:-YieldWithMonitor，本单测成功。
         *
         * 结论：由于CLH的优先机制，以及KONA的线程锁，导致的饥饿现象是存在的。
         */
        final ExecutorService scheduler = Executors.newFixedThreadPool(2, new DaemonThreadFactory("fiber"));
        // 先把线程加载出来
        scheduler.execute(() -> {
        });
        scheduler.execute(() -> {
        });
        final ReentrantLock smallLock = new ReentrantLock();
        final Condition smallLockCondition = smallLock.newCondition();
        final Object bigLock = new Object();
        final CountDownLatch latch = new CountDownLatch(4);
        // 线程1 先拿到锁，并进入Condition状态
        Thread.ofPlatform().name("thread1").daemon(false).start(() -> {
            try {
                smallLock.lock();
                long nanos = TimeUnit.MILLISECONDS.toNanos(1000);
                Thread.sleep(1000);
                System.out.println("lock Free");
                nanos = smallLockCondition.awaitNanos(nanos);
                if (nanos <= 0) {
                    throw new TimeoutException("timedout");
                }
                for (int i = 0; i < 1000; i++) {
                    System.out.println("hello world " + i);
                }
            } catch (InterruptedException | TimeoutException e) {
                e.printStackTrace();
            } finally {
                smallLock.unlock();
                latch.countDown();
                System.out.println(Thread.currentThread().getName() + " Done");
            }
        });
        Thread.sleep(10);
        // VT1阻塞于小锁，占据小锁获取锁的顺位第一位
        Thread.ofVirtual().name("vt1")
               // .scheduler(scheduler)
                .start(() -> {
            try {
                smallLock.lock();
            } finally {
                smallLock.unlock();
                latch.countDown();
                System.out.println(Thread.currentThread().getName() + " Done");
            }
        });
        Thread.sleep(10);
        // vt2排序第二位，占据大锁且阻塞于小锁
        Thread.ofVirtual().name("vt2")
                //.scheduler(scheduler)
                .start(() -> {
            synchronized (bigLock) {
                try {
                    smallLock.lock();
                } finally {
                    smallLock.unlock();
                    latch.countDown();
                    System.out.println(Thread.currentThread().getName() + " Done");
                }
            }
        });
        Thread.sleep(10);
        // vt3排序第三位，阻塞于大锁
        Thread.ofVirtual().name("vt3")
              //  .scheduler(scheduler)
                .start(() -> {
            synchronized (bigLock) {

            }
            latch.countDown();
            System.out.println(Thread.currentThread().getName() + " Done");
        });
        Assertions.assertFalse(latch.await(5, TimeUnit.SECONDS));
    }


    @DisplayName("协程测试")
    @Test
    public void fibberTest1() throws Exception {
        ExecutorService scheduler = Executors.newFixedThreadPool(8, new DaemonThreadFactory("game"));
        ThreadFactory factory = Thread.ofVirtual()
                //.scheduler(scheduler)
                .name("test-fiber-runner").factory();
        List<Thread> threadList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Thread thread = factory.newThread(() -> {
                logger.debug("hello world start");
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                logger.debug("hello world end");
            });
            thread.start();
            threadList.add(thread);
        }
        for (Thread thread : threadList) {
            thread.join(5000);
        }

        scheduler.shutdown();
        scheduler.awaitTermination(60000, TimeUnit.MILLISECONDS);
    }

    @DisplayName("协程测试2")
    @Test
    public void fibberTest2() throws Exception {
        int NTASKS = 10;
        List<Future<String>> futureList = new LinkedList<>();
        ThreadFactory factory = Thread.ofVirtual().name("test-fiber-runner").factory();
        ExecutorService exec = Executors.newFixedThreadPool(NTASKS, factory);
        for (int i = 1; i <= NTASKS; i++) {
            String taskname = "task-" + i;
            Callable<String> callable = () -> {
                logger.debug("{} start", taskname);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                logger.debug("{} end", taskname);
                return taskname;
            };
            Future<String> future = exec.submit(callable);
            futureList.add(future);
        }

        futureList.forEach(future -> {
            try {
                String result = future.get();
                logger.debug("{}", result);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }


        });

        exec.shutdown();
        exec.awaitTermination(60000, TimeUnit.MILLISECONDS);


    }

    @DisplayName("协程Park/Unpark测试, unpark被吞掉")
    public void fibberParkUnPark() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(2);
        final ReentrantLock lock = new ReentrantLock();
        Thread a1 = Thread.ofVirtual().name("a1").start(() -> {
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            long tsMs = System.currentTimeMillis();
            lock.lock();
            System.out.println(StringUtils.format("{} {}", Thread.currentThread(), System.currentTimeMillis() - tsMs));
            LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
            latch.countDown();
        });

        Thread.ofVirtual().name("a2").start(() -> {
            lock.lock();
            long tsMs = System.currentTimeMillis();
            System.out.println(StringUtils.format("{} {}", Thread.currentThread(), System.currentTimeMillis() - tsMs));
            try {
                LockSupport.unpark(a1);
                Thread.sleep(100);
                System.out.println(StringUtils.format("{} {}", Thread.currentThread(), System.currentTimeMillis() - tsMs));
            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
                latch.countDown();
            }
        });
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertTrue(a1.isVirtual());
        Assertions.assertSame(a1.getState(), Thread.State.WAITING);
    }

    @DisplayName("线程Park/UnPark测试，unpark被吞掉")
    @Test
    public void threadParkUnPark() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(2);
        final ReentrantLock lock = new ReentrantLock();
        final Thread a1 = Thread.ofPlatform().name("a1").start(() -> {
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            lock.lock();
            System.out.println(Thread.currentThread());
            LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
            latch.countDown();
        });
        Thread.ofPlatform().name("a2").start(() -> {
            lock.lock();
            System.out.println(Thread.currentThread());
            try {
                LockSupport.unpark(a1);
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
                latch.countDown();
            }
        });
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertSame(a1.getState(), Thread.State.TIMED_WAITING);
    }

    @DisplayName("线程Park/UnPark测试，unpark被吞掉")
    public void threadParkUnPark2() throws InterruptedException {
        final CountDownLatch latch = new CountDownLatch(2);
        final Thread a1 = Thread.ofPlatform().name("a1").start(() -> {
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            System.out.println(StringUtils.format("{} start", Thread.currentThread()));
            LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
            System.out.println(StringUtils.format("{} unpark", Thread.currentThread()));
            LockSupport.parkNanos(Thread.currentThread(), TimeUnit.SECONDS.toNanos(2));
            latch.countDown();
        });
        Thread.ofPlatform().name("a2").start(() -> {
            System.out.println(Thread.currentThread());
            LockSupport.unpark(a1);
            LockSupport.unpark(a1);
            latch.countDown();
        });
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertSame(a1.getState(), Thread.State.TIMED_WAITING);
    }

    @DisplayName("测试下多协程使用stampedlock")
    @Test
    public void fiberWithStampedLock() throws InterruptedException {

        GeminiThreadPoolExecutor tttt = ConcurrentHelper.newFixedThreadExecutor("tttt", 2, 0, false);
        ThreadFactory tttf = ConcurrentHelper.newFiberFactory("tttf", tttt);

        final CountDownLatch latch = new CountDownLatch(3);
        Lock lock = new StampedLock().asWriteLock();

        Thread f1 = tttf.newThread(() -> {
            lock.lock();
            int a = 1;
            // 让渡一下
            Thread.yield();
            lock.unlock();
            lock.lock();
            a += 1;
            lock.unlock();
            latch.countDown();
        });

        Thread f2 = tttf.newThread(() -> {
            lock.lock();
            int a = 1;
            lock.unlock();
            lock.lock();
            a += 1;
            lock.unlock();
            latch.countDown();
        });

        Thread f3 = tttf.newThread(() -> {
            lock.lock();
            int a = 1;
            lock.unlock();
            lock.lock();
            a += 1;
            lock.unlock();
            latch.countDown();
        });

        f1.start();
        f2.start();
        f3.start();

        Assertions.assertTrue(latch.await(2, TimeUnit.SECONDS));
    }
}

//package com.yorha.common.message;
//
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.junit.jupiter.api.BeforeAll;
//import org.junit.jupiter.api.Test;
//
//class RouteLoadingCacheTest {
//    private static final Logger logger = LogManager.getLogger(RouteLoadingCacheTest.class);
//    private static ExecutorLoadingCache cache;
//
//    @BeforeAll
//    public static void beforeAll() throws Exception {
//        BalanceBusinessExecutorService service = new BalanceBusinessExecutorService(Group.BusCache, 5000);
//        cache = new ExecutorLoadingCache(service.getGroup(), service.getExecutorList(), null);
//    }
//
//
//    @Test
//    void load() {
//        FibberExecutor executor = cache.load("6666666666L");
//        executor.execute("testHelloWorld", () -> {
//            logger.debug("hello world");
//        });
//    }
//
//}
//

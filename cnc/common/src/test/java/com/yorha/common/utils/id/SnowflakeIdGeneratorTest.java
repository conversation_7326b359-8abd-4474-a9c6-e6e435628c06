package com.yorha.common.utils.id;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;

/**
 * ModifiedSnowflakeIdGenerator 测试类
 * <p>
 * 测试内容：
 * 1. 基本功能测试
 * 2. 并发安全测试
 * 3. ID唯一性测试
 * 4. ID解析测试
 * 5. 边界条件测试
 * 6. 性能测试
 *
 * <AUTHOR>
 */
public class SnowflakeIdGeneratorTest {

    private SnowflakeIdGenerator generator;
    private static final long TEST_WORKER_ID = 1024L;

    @BeforeEach
    void setUp() {
        generator = new SnowflakeIdGenerator(TEST_WORKER_ID);
    }

    @Test
    @DisplayName("测试基本ID生成功能")
    void testBasicIdGeneration() {
        // 生成ID
        long id1 = generator.nextId();
        long id2 = generator.nextId();

        // 验证ID为正数
        Assertions.assertTrue(id1 > 0, "ID应该为正数");
        Assertions.assertTrue(id2 > 0, "ID应该为正数");

        // 验证ID不相等
        Assertions.assertNotEquals(id1, id2, "连续生成的ID应该不相等");

        // 验证ID趋势递增（在短时间内）
        Assertions.assertTrue(id2 > id1, "ID应该趋势递增");
    }

    @Test
    @DisplayName("测试机器ID边界值")
    void testMachineIdBoundary() {
        // 测试最小机器ID
        SnowflakeIdGenerator generator0 = new SnowflakeIdGenerator(0);
        long id0 = generator0.nextId();
        Assertions.assertTrue(id0 > 0, "机器ID为0时应该能正常生成ID");

        // 测试最大机器ID
        SnowflakeIdGenerator generatorMax = new SnowflakeIdGenerator(8191);
        long idMax = generatorMax.nextId();
        Assertions.assertTrue(idMax > 0, "机器ID为最大值时应该能正常生成ID");

        // 测试超出范围的机器ID
        Assertions.assertThrows(IllegalArgumentException.class, () -> new SnowflakeIdGenerator(8192), "超出范围的机器ID应该抛出异常");

        Assertions.assertThrows(IllegalArgumentException.class, () -> new SnowflakeIdGenerator(-1), "负数机器ID应该抛出异常");
    }

    @Test
    @DisplayName("测试序列号耗尽处理")
    void testSequenceExhaustion() {
        // 在同一毫秒内生成大量ID，测试序列号耗尽处理
        Set<Long> ids = new HashSet<>();

        // 生成超过序列号上限的ID数量
        for (int i = 0; i < 1100; i++) { // 超过1024的序列号上限
            long id = generator.nextId();
            Assertions.assertTrue(ids.add(id), "所有ID应该唯一");
        }

        Assertions.assertEquals(1100, ids.size(), "应该生成1100个唯一ID");
    }

    @Test
    @DisplayName("测试性能基准")
    void testPerformanceBenchmark() {
        final int testCount = 1000000;

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < testCount; i++) {
            generator.nextId();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double idsPerMs = (double) testCount / duration;

        System.out.printf("性能测试结果: 生成 %d 个ID 耗时 %d ms, 平均 %.2f IDs/豪秒%n",
                testCount, duration, idsPerMs);

        // 验证性能应该达到一定标准（这里设置为每毫秒至少900个ID）
        Assertions.assertTrue(idsPerMs > 900,
                String.format("性能不达标，期望 > 1000 IDs/豪秒，实际 %.2f IDs/豪秒", idsPerMs));

        // 每毫秒不应该超过1024个ID
        Assertions.assertTrue(idsPerMs <= 1024,
                String.format("每毫秒生成ID数量过多，期望 <= 1024 IDs/豪秒，实际 %.2f IDs/豪秒", idsPerMs));
    }

}

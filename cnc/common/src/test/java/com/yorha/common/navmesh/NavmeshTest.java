package com.yorha.common.navmesh;

import com.google.common.collect.ImmutableList;

import java.util.List;

/**
 * <AUTHOR>
 */
public class NavmeshTest {

    static List<List<Float>> point = ImmutableList.of(
            ImmutableList.<Float>of(31.1f, 16.0f, 24.6f, 30.0f),
            ImmutableList.<Float>of(47.2f, 29.1f, 32.6f, 22.4f),
            ImmutableList.<Float>of(27.5f, 48.3f, 21.9f, 23.5f),
            ImmutableList.<Float>of(18.1f, 33.9f, 47.9f, 48.1f),
            ImmutableList.<Float>of(7.4f, 37.0f, 47.2f, 35.1f),
            ImmutableList.<Float>of(26.2f, 32.1f, 8.6f, 46.9f));

//
//    @Test
//    public void testNav() throws InterruptedException {
//        IGeminiDispatcher dispatcher1 = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 1);
//        IGeminiDispatcher dispatcher2 = GeminiDispatcherFactory.buildFiberDispatcher("timi", 1, 1);
//        GeminiNav nav1 = new GeminiNav();
//        GeminiNav nav2 = new GeminiNav();
//        try {
//            nav1.init(1, "F:\\game_data/server/navmesh/map_2001_0.bin");
//            nav2.init(2, "F:\\game_data/server/navmesh/map_2001_0.bin");
//        } catch (Exception e) {
//            System.out.println(e);
//            return;
//        }
//        CountDownLatch latch = new CountDownLatch(2);
//        int n = 100;
//        dispatcher1.execute(NamedRunnableWithId.valueOf(1, "1", () -> {
//            for (int i = 0; i < n; i++) {
//                List<Float> p = point.get(i % 6);
//                nav1.find(1, p.get(0), p.get(1), p.get(2), p.get(3));
//            }
//            System.out.println("1 end");
//            latch.countDown();
//        }));
//
//        dispatcher2.execute(NamedRunnableWithId.valueOf(1, "2", () -> {
//            for (int i = 0; i < n; i++) {
//                List<Float> p = point.get(i % 6);
//                nav2.find(2, p.get(0), p.get(1), p.get(2), p.get(3));
//            }
//            System.out.println("2 end");
//            latch.countDown();
//        }));
//        latch.await();
//    }
}

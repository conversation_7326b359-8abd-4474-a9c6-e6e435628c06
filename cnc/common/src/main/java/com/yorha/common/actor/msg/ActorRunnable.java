package com.yorha.common.actor.msg;

import com.yorha.common.actor.IActor;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class ActorRunnable<T extends IActor> implements IActorRunnable<T> {
    private final String name;
    private final Consumer<T> consumer;

    public ActorRunnable(String name, Consumer<T> consumer) {
        this.name = name;
        this.consumer = consumer;
    }

    @Override
    public void run(T actor) {
        consumer.accept(actor);
    }

    @Override
    public String profName() {
        return name;
    }

    @Override
    public String toString() {
        return profName();
    }
}

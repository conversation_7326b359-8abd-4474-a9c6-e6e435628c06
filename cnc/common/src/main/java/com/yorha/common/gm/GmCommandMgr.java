package com.yorha.common.gm;

import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public abstract class GmCommandMgr<T extends BaseGameActor> {
    private static final Logger LOGGER = LogManager.getLogger(GmCommandMgr.class);

    public static final Map<String, GmCommand> COMMAND_STORE = new ConcurrentHashMap<>();
    protected static final String HELP = "help";
    protected static final String QUESTION_MARK = "?";

    /**
     * gm列表扫描路径
     */
    protected abstract String getCommandDir();

    /**
     * 初始化
     */
    public void init() {
        // 反射加载所有的gm命令
        final JavaClassScanner scanner = new JavaClassScanner();
        Set<Class<? extends GmCommand>> subTypesSet = scanner.getSubTypesOf(getCommandDir(), GmCommand.class);
        LOGGER.info("DebugCommand num:{}", subTypesSet.size());
        for (Class<? extends GmCommand> commandClazz : subTypesSet) {
            if (commandClazz.isInterface()) {
                continue;
            }
            try {
                Constructor<? extends GmCommand> constructor = commandClazz.getConstructor();
                GmCommand debugCommand = constructor.newInstance();
                COMMAND_STORE.put(commandClazz.getSimpleName().toUpperCase(Locale.ROOT), debugCommand);
            } catch (NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException e) {
                LOGGER.error("gm:{} constructor error", commandClazz.getSimpleName());
            }
        }
        LOGGER.info("gemini_system init gm ok, num:{}", COMMAND_STORE.size());
    }

    public String handle(T actor, long playerId, String command) {
        LOGGER.info("{} try execute gm:{}", actor, command);
        String commandName = StringUtils.substringBefore(command, " ").trim().toUpperCase(Locale.ROOT);
        GmCommand gmCommand = COMMAND_STORE.get(commandName);
        if (gmCommand == null) {
            throw new GeminiException(ErrorCode.GM_COMMAND_CANNOT_FIND);
        }
        if (StringUtils.contains(command, HELP) || StringUtils.contains(command, QUESTION_MARK)) {
            return gmCommand.showHelp();
        }
        String args = StringUtils.substringAfter(command, " ").trim();
        if (!checkValid(args)) {
            return "the command [" + command + "] is illegal";
        }
        // 执行命令逻辑
        gmCommand.execute(actor, playerId, buildArgs(args));
        return "OK";
    }

    /**
     * 获取所有gm命令
     *
     * @param value groupId gm组
     * @return 该组的所有gm
     */
    public Collection<String> getDebugCommandList(int value) {
        List<String> ret = new ArrayList<>();
        CommonEnum.DebugGroup group = CommonEnum.DebugGroup.forNumber(value);
        for (GmCommand e : COMMAND_STORE.values()) {
            if (group == CommonEnum.DebugGroup.DG_NONE || e.getGroup() == group) {
                ret.add(e.showHelp());
            }
        }
        return ret;
    }

    protected Map<String, String> buildArgs(String value) {
        String[] args = StringUtils.split(value, " ");
        Map<String, String> ret = new HashMap<>();
        for (String e : args) {
            String[] array = StringUtils.split(e, "=");
            ret.put(array[0], array[1]);
        }
        return ret;
    }

    protected boolean checkValid(String args) {
        String regExp = "^[0-9A-Za-z=, _-]*$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(args);
        return m.matches();
    }
}

package com.yorha.common.db.tcaplus;

/**
 * <AUTHOR>
 */
public final class TcaplusConstant {
    /**
     * 下面是各个支持的命令字在设置不同的result_flag下执行成功后返回给API端的数据详细情况:
     * <p>
     * 1. TCAPLUS_API_INSERT_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回本次insert操作后的数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回空数据
     * <p>
     * 2. TCAPLUS_API_REPLACE_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回本次replace操作后的数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端操作前的数据, 如果tcapsvr端没有数据,即返回为空
     * <p>
     * 3. TCAPLUS_API_UPDATE_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回本次update操作后的数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端操作前的数据
     * <p>
     * 4. TCAPLUS_API_INCREASE_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回本次increase操作后的数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端操作前的数据, 如果tcapsvr端没有数据,即返回为空
     * <p>
     * 5. TCAPLUS_API_DELETE_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回空数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端操作前的数据
     * <p>
     * 6. TCAPLUS_API_LIST_DELETE_BATCH_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据, 暂时没有实现
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端操作前的数据, 凡是本次成功删除的index对应的数据都会返回
     * <p>
     * 7. TCAPLUS_API_LIST_ADDAFTER_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据, 暂时没有实现
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后, 返回本次插入的记录和本次淘汰的数据记录
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后不返回数据
     * <p>
     * 8. TCAPLUS_API_LIST_DELETE_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据, 暂时没有实现
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回空数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端listdelete前的数据
     * <p>
     * 9. TCAPLUS_API_LIST_REPLACE_REQ
     * 如果设置的是TCaplusValueFlag_NOVALUE, 则操作成功后不返回数据
     * 如果设置的是TCaplusValueFlag_SAMEWITHREQUEST, 则操作成功后返回和请求一致的数据, 暂时没有实现
     * 如果设置的是TCaplusValueFlag_ALLVALUE, 则操作成功后返回tcapsvr端listreplace后的数据
     * 如果设置的是TCaplusValueFlag_ALLOLDVALUE, 则操作成功后返回tcapsvr端listreplace前的数据
     * <p>
     * 对于batch_get请求，该字段设置为大于0时，某个key查询记录不存在或svr端产生的其它错误时会返回对应的key，
     * 从而知道是哪个key对应的记录失败了
     */
    public enum ResultFlag {
        /**
         * 只需返回操作执行成功与否
         */
        RESULT_FLAG_RESULT_ONLY(0),
        /**
         * 返回与请求字段一致
         */
        RESULT_FLAG_FIELDS_REQUEST(1),
        /**
         * 须返回变更记录的所有字段最新数据
         */
        RESULT_FLAG_FIELDS_ALL(2),
        /**
         * 须返回变更记录的所有字段旧数据
         */
        RESULT_FLAG_FIELDS_ALL_OLD(3);

        private final int value;

        ResultFlag(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * CHECKDATAVERSION_AUTOINCREASE: 表示检测记录版本号。
     * 如果class TcaplusServiceRecord的void SetVersion(IN int32_t iVersion)函数传入的参数iVersion的值<=0,则仍然表示不关心版本号不关注版本号；
     * 如果class TcaplusServiceRecord的void SetVersion(IN int32_t iVersion)函数传入的参数iVersion的值>0，
     * 那么只有当该版本号与服务器端的版本号相同时，Replace, Update, Increase, ListAddAfter, ListDelete, ListReplace, ListDeleteBatch操作才会成功同时在服务器端该版本号会自增1。
     * <p>
     * NOCHECKDATAVERSION_OVERWRITE: 表示不检测记录版本号。
     * 如果class TcaplusServiceRecord的void SetVersion(IN int32_t iVersion)函数传入的参数iVersion的值<=0,则会把版本号1写入服务端的数据记录版本号(服务器端成功写入的数据记录的版本号最少为1)；
     * 如果class TcaplusServiceRecord的void SetVersion(IN int32_t iVersion)函数传入的参数iVersion的值>0，那么会把该版本号写入服务端的数据记录版本号。
     * <p>
     * NOCHECKDATAVERSION_AUTOINCREASE: 表示不检测记录版本号，将服务器端的数据记录版本号自增1，若服务器端新写入数据记录则新写入的数据记录的版本号为1。
     * <p>
     * 默认类型为CHECKDATAVERSION_AUTOINCREASE。
     * 当SetCheckDataVersionPolicy设置为CHECKDATAVERSION_AUTOINCREASE，而SetResultFlag设置为2返回所有字段值时，
     * insert主键冲突，响应消息会返回最新记录；increase replace update list_delete list_replace list_batch_delete在
     * 版本号不正确出错时，响应消息会返回最新记录。
     * SetCheckDataVersionPolicy只适合Replace, Update, Increase, ListAddAfter, ListDelete, ListReplace, ListDeleteBatch操作
     */

    public enum CheckDataVersionType {
        /**
         * 表示检测记录版本号
         */
        CHECK_DATA_VERSION_AUTO_INCREASE(1),
        /**
         * 表示不检测记录版本号
         */
        NO_CHECK_DATA_VERSION_OVERWRITE(2),
        /**
         * 表示不检测记录版本号，将服务器端的数据记录版本号自增1，若服务器端新写入数据记录则新写入的数据记录的版本号为1。
         */
        NO_CHECK_DATA_VERSION_AUTO_INCREASE(3);

        CheckDataVersionType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        private final int value;
    }

    public enum CommonFlag {
        /**
         * 置位时，若存储端数据与指定版本相比发生了变化，才返回数据
         */
        TCAPLUS_FLAG_FETCH_ONLY_IF_MODIFIED(1),
        /**
         * 置位时，若存储端数据在最近指定的时间窗口内发生了变化，才返回数据
         */
        TCAPLUS_FLAG_FETCH_ONLY_IF_EXPIRED(2),
        /**
         * 外部读请求仅发给Slave
         */
        TCAPLUS_FLAG_ONLY_READ_FROM_SLAVE(4),
        /**
         * 置位时，List表删除最后一个元素时需要保留index和version
         */
        TCAPLUS_FLAG_LIST_RESERVE_INDEX_HAVING_NO_ELEMENTS(8);

        CommonFlag(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        private final int value;
    }
}

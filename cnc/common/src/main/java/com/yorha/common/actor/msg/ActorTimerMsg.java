package com.yorha.common.actor.msg;

import com.yorha.common.wechatlog.WechatLog;

/**
 * actor定时消息
 *
 * <AUTHOR>
 */
public class ActorTimerMsg implements IActorMsg {
    private final int id;
    private final String name;

    @Override
    public String toString() {
        return "ActorTimerMsg{" + name + '}';
    }

    private ActorTimerMsg(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static ActorTimerMsg valueOf(int id, String name) {
        return new ActorTimerMsg(id, name);
    }

    public int getId() {
        return id;
    }

    public String getName() {
        // 完整的类似 entityId + "@" + name
        return name;
    }

    @Override
    public boolean canRemote() {
        return false;
    }

    @Override
    public String profName() {
        // 去掉timer的name中的id前缀
        String[] split = name.split("@");
        if (split.length != 2) {
            WechatLog.error("timer name is invalid {}", name);
            return name;
        }
        return split[1];
    }
}

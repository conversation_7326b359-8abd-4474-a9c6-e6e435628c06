package com.yorha.common.db.tcaplus.result;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;
import org.checkerframework.checker.nullness.qual.NonNull;

import java.util.List;

public class BatchGetResult<T extends Message.Builder> implements GameDbResp {
    public TcaplusErrorCode code = TcaplusErrorCode.GEN_ERR_SUC;

    /**
     * values，不为null
     */
    @NonNull
    public List<ValueWithVersion<T>> values;
    /**
     * 返回无法获取的返回值的Key，不为null
     */
    public List<T> errorKeys;
    /**
     * 请求requestId。
     */
    public long requestId;

    @Override
    public int getCode() {
        return code.getValue();
    }
}

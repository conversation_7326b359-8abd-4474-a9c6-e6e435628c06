package com.yorha.common.utils.id;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.LinkedList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 唯一id生成器，依赖于中间件做编码段划分，编码规则：
 * +-----------------------------------+
 * |--保留字-|--编码段编号--|---序列号---|
 * |--4bits-|---40bits---|---20bits---|
 * +-----------------------------------+
 * 算法使用缓存段来提高性能。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class BufferSegmentIdFactory implements IIdFactory {
    public static long encode(long reserve, long segmentId, long seqId) {
        return (reserve << ID_ENCODE_RESERVED_LEFT_MOVE_SIZE) + (segmentId << ID_ENCODE_SEGMENT_LEFT_MOVE_SIZE) + (seqId << ID_ENCODE_SEQUENCE_LEFT_MOVE_SIZE);
    }

    private static final Logger LOGGER = LogManager.getLogger(BufferSegmentIdFactory.class);

    /**
     * 编码段占用bit位
     */
    private static final long ID_ENCODE_SEGMENT_BIT_SIZE = 40;
    /**
     * 序号段占bit位
     */
    private static final long ID_ENCODE_SEQUENCE_BIT_SIZE = 20;
    /**
     * 序号段最大值（不允许超过）
     */
    private static final long ID_ENCODE_MAX_SEQUENCE = 1L << ID_ENCODE_SEQUENCE_BIT_SIZE;
    /**
     * 编码段最大值（不允许超过）
     */
    private static final long ID_ENCODE_MAX_SEGMENT = 1L << ID_ENCODE_SEGMENT_BIT_SIZE;
    private static final long SEGMENT_WARNING_THRESHOLD = (long) (ID_ENCODE_MAX_SEGMENT * 0.8);
    /**
     * 保留位左移位数
     */
    private static final long ID_ENCODE_RESERVED_LEFT_MOVE_SIZE = ID_ENCODE_SEGMENT_BIT_SIZE + ID_ENCODE_SEQUENCE_BIT_SIZE;
    /**
     * 编码段左移位数
     */
    private static final long ID_ENCODE_SEGMENT_LEFT_MOVE_SIZE = ID_ENCODE_SEQUENCE_BIT_SIZE;
    /**
     * 序号段左移位数
     */
    private static final long ID_ENCODE_SEQUENCE_LEFT_MOVE_SIZE = 0;

    /**
     * 段结构体。包含：1.序号生成器 2. 段号
     */
    protected static class Segment {
        private final AtomicLong sequence;
        private final long segment;

        private Segment(long segment, long initValue) {
            this.segment = segment;
            this.sequence = new AtomicLong(initValue);
        }

        @Override
        public String toString() {
            return "Segment{" +
                    "sequence=" + sequence.get() +
                    ", segment=" + segment +
                    '}';
        }
    }

    private final LinkedList<Segment> segmentsCache;
    private final IIdFactory segmentIdFactory;
    private volatile Segment segment;
    private final ReentrantLock lock;
    private final long reserve;

    public BufferSegmentIdFactory(long reserve, int bufferSize, IIdFactory uf, long initValue) {
        if (bufferSize <= 0) {
            throw new RuntimeException("bufferSize <= 0");
        }
        if (initValue < 0) {
            throw new RuntimeException("initValue < 0");
        }
        this.reserve = reserve;
        this.segmentsCache = new LinkedList<>();
        this.segmentIdFactory = uf;
        this.segment = null;
        for (int i = 0; i < bufferSize; i++) {
            this.addSegment(initValue);
        }
        this.segment = this.popSegment();
        this.lock = new ReentrantLock();
    }

    /**
     * 获取一个唯一id， reserve设置0L
     *
     * @param reason 唯一id获取的理由
     * @return 唯一id
     */
    @Override
    public final long nextId(String reason) {
        return this.nextId(reserve, reason);
    }

    /**
     * 获取一个唯一id
     *
     * @param reserve 保留字
     * @param reason  唯一id获取的理由
     * @return 唯一id
     */
    public final long nextId(final long reserve, final String reason) {
        final Segment s = this.segment;
        final long sequence = s.sequence.getAndAdd(1L);
        if (sequence >= BufferSegmentIdFactory.ID_ENCODE_MAX_SEQUENCE) {
            this.switchNextSegment(s);
            return this.nextId(reserve, reason);
        }
        final long nextId = BufferSegmentIdFactory.encode(reserve, s.segment, sequence);
        LOGGER.trace("nextId {} {} {} {}", nextId, reason, s.segment, sequence);
        return nextId;
    }

    private void switchNextSegment(final Segment cs) {
        // 仅当当前buffer即目标buffer时候，进行buffer更新
        this.lock.lock();
        try {
            if (this.segment == cs) {
                // 无可用序号段，则同步更新下一个段作为序号段
                Segment ns = this.popSegment();
                if (ns == null) {
                    // 同步获取id
                    LOGGER.error("switch a over used buffer!try synchronized update segment! curSegment={}", cs);
                    this.addSegment(0L);
                    ns = this.popSegment();
                    LOGGER.info("synchronized update segment successful! segment={} will be updated", this.segment);
                } else {
                    // 当前序号段序列耗尽，有缓存其他序号段，则异步缓存新的序号段
                    ConcurrentHelper.newFiber("SwitchSegment#" + cs.segment, () -> {
                        this.lock.lock();
                        try {
                            this.addSegment(0L);
                            LOGGER.info("asynchronous update segment successful!");
                        } finally {
                            this.lock.unlock();
                        }
                    }).start();
                }
                this.segment = ns;
            }
        } finally {
            this.lock.unlock();
        }
    }

    private void addSegment(long initValue) {
        final Segment ns = new Segment(this.segmentIdFactory.nextId(this.getClass().getName()), initValue);
        if (ns.segment >= ID_ENCODE_MAX_SEGMENT) {
            throw new RuntimeException("segment is overflow");
        }
        if (ns.segment >= SEGMENT_WARNING_THRESHOLD) {
            LOGGER.error("segment {} is above 80%! segmentFactory {}!", ns, this.segmentIdFactory);
        }
        LOGGER.info("add segment={}", ns);
        this.onSegmentAdd(ns);
        this.segmentsCache.add(ns);
    }

    private Segment popSegment() {
        if (this.segmentsCache.isEmpty()) {
            return null;
        }
        final Segment s = this.segmentsCache.removeFirst();
        LOGGER.info("remove segment={}", segment);
        this.onSegmentSwitch(s);
        return s;
    }

    /**
     * 不允许抛出异常，重载这个函数要小心。
     *
     * @param segment 段号。
     */
    protected void onSegmentSwitch(final Segment segment) {
    }

    protected void onSegmentAdd(final Segment segment) {
    }
}

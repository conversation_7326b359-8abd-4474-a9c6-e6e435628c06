package com.yorha.common.db;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.*;
import com.yorha.common.db.tcaplus.result.*;

import java.util.Collection;

/**
 * db操作工厂
 *
 * <AUTHOR>
 */
public interface IDbOperationFactory {


    void checkTable();

    <T extends Message.Builder> IDbOperation<T, InsertOption, InsertResult<T>> insertOperation(T dbData, InsertOption option);

    <T extends Message.Builder> IDbOperation<T, GetOption, GetResult<T>> getOperation(T dbData, GetOption option);

    <T extends Message.Builder> IDbOperation<Collection<T>, BatchGetOption, BatchGetResult<T>> batchGetOperation(Collection<T> dbData, BatchGetOption option);

    <T extends Message.Builder> IDbOperation<T, GetByPartKeyOption, GetByPartKeyResult<T>> getByPartKeyOperation(T dbData, GetByPartKeyOption option);

    <T extends Message.Builder> IDbOperation<T, UpdateOption, UpdateResult<T>> updateOperation(T dbData, UpdateOption option);

    <T extends Message.Builder> IDbOperation<T, UpsertOption, UpsertResult<T>> upsertOperation(T dbData, UpsertOption option);

    <T extends Message.Builder> IDbOperation<T, IncreaseOption, IncreaseResult<T>> increaseOperation(T dbData, IncreaseOption option);

    <T extends Message.Builder> IDbOperation<T, DeleteOption, DeleteResult> deleteOperation(T dbData, DeleteOption option);

    <T extends Message.Builder> IDbOperation<T, DeleteByPartKeyOption, DeleteByPartKeyResult> deleteByPartKeyOperation(T dbData, DeleteByPartKeyOption option);

    <T extends Message.Builder> IDbOperation<T, GetMetaOption, GetMetaResult> getMetaOperation(T dbData, GetMetaOption option);

    <T extends Message.Builder> IDbOperation<T, TraverseOption<T>, TraverseResult> traverseOperation(T dbData, TraverseOption<T> option);

    void close();
}

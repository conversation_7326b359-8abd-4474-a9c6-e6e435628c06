package com.yorha.common.constant;

import com.google.common.collect.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum.*;
import res.template.ConstTemplate;

import java.util.*;

import static com.yorha.proto.CommonEnum.ArmyActionType.AAT_PICK_UP;

/**
 * 游戏逻辑常量
 *
 * <AUTHOR>
 */
public interface GameLogicConstants {
    /**
     * 寻路相关
     */
    int STATIC_END_CORRECT = 0x1;  // 静态终点修正  目标点是静态阻挡  偏移到最近的可达点
    int DYNAMIC_END_CORRECT = 0x2; // 动态终点修正  目标点在动态阻挡圆内，要修正
    int DYNAMIC_PATH_CORRECT = 0x4; // 动态阻挡绕过修正
    int IGNORE_CROSSING_OWNER = 0x8; // 无视关卡占有 使用全联通图
    int IGNORE_STATIC = 0x10; // 无视静态阻挡
    int CROSS_TYPE_INSERT_POINT = 0x20; // 过关方式, 二段式   插点   正常应该是三段式插线段
    int IGNORE_ALL = 0x40; // 无视所有 不用在关卡走过 直接连线
    int BESIEGE_TAG = 0X80; // 围攻标签

    int NORMAL_MOVE_POINT = STATIC_END_CORRECT | DYNAMIC_END_CORRECT | DYNAMIC_PATH_CORRECT; //普通行军 全部
    int NORMAL_MOVE_TARGET = DYNAMIC_PATH_CORRECT; // 普通移动到目标 不需要静态动态终点修正
    int BESIEGE_MOVE = NORMAL_MOVE_TARGET | BESIEGE_TAG; // 围攻
    int DIED_ARMY_MOVE = DYNAMIC_PATH_CORRECT | IGNORE_CROSSING_OWNER; // 溃败行军   绕城修正+无视关卡归属
    int AIRPORT_MOVE = IGNORE_CROSSING_OWNER | IGNORE_STATIC | CROSS_TYPE_INSERT_POINT; // 侦察机  无视关卡占有+无视静态阻挡
    int TRANSPORT_MOVE = IGNORE_STATIC; // 运输机  无视静态阻挡
    int TRANSPORT_MOVE_RETURN = IGNORE_CROSSING_OWNER | IGNORE_STATIC; // 运输机回城 无视关卡占有

    /**
     * 普通层视野同步包entity最大个数
     */
    int AOI_SYNC_PACKAGE_ENTITY_NUM = 10;
    /**
     * 场景跨服旁观者人数上限
     */
    int SCENE_BYSTANDER_MAX = 500;
    /**
     * 保底公告
     */
    int DEFAULT_ANNOUNCEMENT_ID = -1;
    /**
     * 逻辑最小距离单位 到 navmesh坐标体系的 转化比例
     */
    int LOGIC_TO_NAVMESH_COORDINATE_RATIO = 1000;
    int LTNCR = LOGIC_TO_NAVMESH_COORDINATE_RATIO;
    /**
     * 坐标精度误差大小  用于围攻点判定
     */
    int ACCURACY_ERROR_SIZE = 10;
    /**
     * 标精度误差大小  用于判断recast是否遇到阻挡
     */
    int REY_CAST_POINT_EQUAL = 10;
    /**
     * 并行追击的触发角度
     */
    int PARALLEL_CHASE_ANGLE = 90;
    /**
     * 集结出发最小行军数
     */
    int RALLY_ARMY_MIN = 2;
    /**
     * 城池升天判定未上线时间
     */
    long CITY_ASCEND_TIME = 48 * 60 * 60L;
    /**
     * 玩家城池在地图建筑表的配置id
     */
    int CITY_CONFIG_TEMPLATE_ID = 1000;
    /**
     * 围攻上限
     */
    int BESIEGE_MAX_SIZE = 8;
    /**
     * 万分之一
     */
    int IPPM = 10000;
    /**
     * 开放迷雾间隔时间
     */
    int OPEN_FOG_INTERNAL_TIME = 2;
    /**
     * 开放迷雾
     */
    int OPEN_FOG_MAX_NUM = 100000;
    /**
     * 联盟数据初始版本号
     */
    long CLAN_VERSION_INIT = 0L;
    /**
     * 医院（维修站）治疗队列的任务id，治疗只有一个队列，都用这一个
     */
    int HOSPITAL_OCCUPY_QUEUE_TASK_ID = 1;
    /**
     * 新手期间服务器自动落城尝试最大次数
     */
    int CITY_FALL_MAX_TIMES_WHEN_NEWBIE = 5;
    /**
     * 占领表现id
     */
    int CITY_OCCUPY_SUCCEED_SHOW_ID = 6666;
    /**
     * 离线踢线时长
     */
    int OFFLINE_KICK_OFF_TIME_MS = 60 * 1000;
    /**
     * s2c协议警告大小 1MB
     */
    long S2C_MSG_WARING_SIZE = 1024 * 1024;
    /**
     * 单州活动野怪数量上限
     */
    int ACT_MONSTER_REGION_LIMIT = 2000;
    /**
     * 战斗时模型圈半径刷新间隔时长 毫秒
     */
    int MODEL_RADIUS_UPDATE_INTERVAL = 5000;
    /**
     * 名字搜索需要查询的zone个数
     */
    int NAME_SEARCH_ZONE_NUM = 20;

    /**
     * 兵种类型对应的训练队列类型
     */
    Map<SoldierType, QueueTaskType> SOLDIER_TYPE_QUEUE_TASK_TYPE_MAP = ImmutableMap.of(
            SoldierType.ST_Foot, QueueTaskType.SOLDIER_FOOT_TRAINING,
            SoldierType.ST_Tank, QueueTaskType.SOLDIER_TANK_TRAINING,
            SoldierType.ST_Artillery, QueueTaskType.SOLDIER_ARTILLERY_TRAINING,
            SoldierType.ST_ArmoredCar, QueueTaskType.SOLDIER_ARMORED_CAR_TRAINING
    );
    /**
     * 无需提醒标识
     */
    int MAX_RETRY_TIMES = 100;
    int BORN_RANDOM_LOOP_MAX = 10_000;
    /**
     * 模糊单位值
     */
    int PERCENT_CONVERSION_UNITS = 100;
    /**
     * 迷雾探索一次递归最大深度
     */
    int FOG_EXPLORE_STACK_DEPTH = 5;
    /**
     * 每秒可回收最大数量
     */
    int RECYCLE_MAX_PER_SECOND = 400;
    /**
     * 每秒可创建最大数量
     */
    int CREATE_MAX_PER_SECOND = 400;
    /**
     * 资源田每秒可创建最大数量
     */
    int RES_BUILDING_CREATE_MAX_PER_SECOND = 400;


    /**虚拟编队*/
    int FORMATION_MAX_SLOT = 5;


    /**
     * qlog gameAppId
     */
    String GAME_APPID = "FB_29185WX_29185";
    String GAME_QQ_APPID = "1112273554";
    String GAME_WX_APPID = "wxf3e86619d7eb8783";
    /**
     * battle_pass 周任务组数最大值
     */
    int BP_WEEK_MISSION_MAX = 5;
    /**
     * 一个scene内同时最大发起ai巡逻数量
     */
    int AI_PATROL_LIMIT_PERSCENE = 5000;
    /**
     * 行军空运状态
     */
    List<ArmyState> TRANSPORT_STATE = Arrays.asList(ArmyState.AS_LiftOff, ArmyState.AS_AirTransport, ArmyState.AS_Airborne, ArmyState.AS_TransportReturn);
    /**
     * 地图id对应aoi格子数
     */
    HashMap<Integer, Integer> MAP_ID_2_AOI_NUM = new HashMap<>() {
        {
            put(BigSceneConstants.BIG_SCENE_MAP_ID, 180);
            put(1100, 180);
        }
    };

    /**
     * 地图id对应的碰撞世界collisionWorld 格子数  没有的默认用非格子的方式
     */
    HashMap<Integer, Integer> MAP_ID_2_COLLISION_WORLD_NUM = new HashMap<>() {
        {
            put(BigSceneConstants.BIG_SCENE_MAP_ID, 10000);
            put(1100, 10000);
        }
    };

    int BIG_MAP_BRIEF_AOI_NUM = 20;
    /**
     * prepareItem过期时间（s）
     */
    int PREPARE_ITEM_EXPIRE_TIME = 10;
    /**
     * 迁服请求重试次数
     */
    int MIGRATE_RETRY_NUM = 3;
    /**
     * 视野能看到的最大个数
     */
    int VIEW_OBJ_NUM_MAX = 50;
    /**
     * 移民战力配表数值单位，万
     */
    long MIGRATE_POWER_MEASUREMENT = 10000L;
    /**
     * 仇恨默认过期时间60s
     */
    int DEFAULT_HATE_EXPIRE = 60;
    Set<Integer> VALID_SOLDIER_TYPE = ImmutableSet.of(
            SoldierType.ST_Foot_VALUE,
            SoldierType.ST_Tank_VALUE,
            SoldierType.ST_Artillery_VALUE,
            SoldierType.ST_ArmoredCar_VALUE
    );

    static boolean staticEndCorrect(int searchTag) {
        return (searchTag & GameLogicConstants.STATIC_END_CORRECT) > 0;
    }

    static boolean dynamicEndCorrect(int searchTag) {
        return (searchTag & GameLogicConstants.DYNAMIC_END_CORRECT) > 0;
    }

    static boolean dynamicPathCorrect(int searchTag) {
        return (searchTag & GameLogicConstants.DYNAMIC_PATH_CORRECT) > 0;
    }

    static boolean ignoreCrossOwner(int searchTag) {
        return (searchTag & GameLogicConstants.IGNORE_CROSSING_OWNER) > 0;
    }

    static boolean ignoreStatic(int searchTag) {
        return (searchTag & GameLogicConstants.IGNORE_STATIC) > 0;
    }

    static boolean ignoreAll(int searchTag) {
        return (searchTag & GameLogicConstants.IGNORE_ALL) > 0;
    }

    static boolean isBesiegeMove(int searchTag) {
        return (searchTag & GameLogicConstants.BESIEGE_TAG) > 0;
    }

    /**
     * 判断是否是据点
     */
    static boolean isMapStronghold(MapAreaType type) {
        return type == MapAreaType.TERRITORY;
    }

    /**
     * 城池的区域类型  1-4级城、关隘
     */
    static boolean isMapCity(MapAreaType type) {
        return type == MapAreaType.CITY_ONE
                || type == MapAreaType.CITY_TWO
                || type == MapAreaType.CITY_THREE
                || type == MapAreaType.CITY_FOUR
                || type == MapAreaType.CROSSING;
    }

    /**
     * 是不是王城
     */
    static boolean isKingCity(MapBuildingType type) {
        return type == MapBuildingType.MBT_ROYAL_CITY;
    }

    /**
     * 是否是改建建筑
     */
    static boolean isRebuildBuilding(MapBuildingType type) {
        return type == MapBuildingType.MBT_MAIN_BASE || type == MapBuildingType.MBT_COMMAND_CENTER || type == MapBuildingType.MBT_CLAN_FORTRESS;
    }

    /**
     * 目前做了的地缘建筑 区域类型
     */
    static boolean isMapStrongholdOrCity(MapAreaType type) {
        return isMapStronghold(type) || isMapCity(type);
    }

    static QueueTaskType getQueueTaskTypeBySoldierType(SoldierType type) {
        return SOLDIER_TYPE_QUEUE_TASK_TYPE_MAP.get(type);
    }

    static boolean isSoldierTrainingQueue(QueueTaskType type) {
        return SOLDIER_TYPE_QUEUE_TASK_TYPE_MAP.containsValue(type);
    }

    static boolean isRallyActionType(ArmyActionType type) {
        return type == ArmyActionType.AAT_CreateRally || type == ArmyActionType.AAT_JoinRally;
    }

    static boolean needCheckEnergyActionType(ArmyActionType type) {
        return type == ArmyActionType.AAT_Battle || type == ArmyActionType.AAT_CreateRally || type == ArmyActionType.AAT_JoinRally || type == AAT_PICK_UP;
    }

    static boolean isTransportState(ArmyState armyState) {
        return GameLogicConstants.TRANSPORT_STATE.contains(armyState);
    }

    static int getCurrencyBurdenRadio(CurrencyType type) {
        return switch (type) {
            case DIAMOND -> ResHolder.getConsts(ConstTemplate.class).getLoadVsGold();
            case OIL -> ResHolder.getConsts(ConstTemplate.class).getLoadVsOil();
            case STEEL -> ResHolder.getConsts(ConstTemplate.class).getLoadVsIron();
            case TIBERIUM -> ResHolder.getConsts(ConstTemplate.class).getLoadVsTiberium();
            case RARE_EARTH -> ResHolder.getConsts(ConstTemplate.class).getLoadVsRare();
            default -> throw new GeminiException("res not has burden radio", type);
        };
    }
}

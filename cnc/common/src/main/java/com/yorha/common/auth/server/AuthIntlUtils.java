package com.yorha.common.auth.server;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.json.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.Map;

import static com.yorha.common.constant.AuthConstant.INTL_AUTH_OS_ANDROID;
import static com.yorha.common.constant.AuthConstant.INTL_AUTH_OS_IOS;

/**
 * Intl鉴权渠道工具类
 *
 * <AUTHOR>
 */
public class AuthIntlUtils {
    private static final Logger LOGGER = LogManager.getLogger(AuthIntlUtils.class);

    public static int transPlatformId2Os(int platformId) {
        return switch (platformId) {
            case 0 -> INTL_AUTH_OS_IOS;
            default -> INTL_AUTH_OS_ANDROID;
        };
    }

    /**
     * 生成签名
     * <p>
     * sig = md5 (path + "?" + params + body + sigkey)
     * <p>
     * ex. sourceStr = /v2/auth/verify_login?channelid=1&gameid=11&os=5&sdk_version=2.0&seq=&source=0&ts=1644943802{"openid":"11219380013689673060","token":"B8D116F42A6A8116398C40AED587195C"}sigkey
     * <p>
     * 详情见：https://docs.playernetwork.intlgame.com/docs/zh/API/Backend/Sig/SigKey
     */
    public static String makeSig(String path, Map<String, Object> params, Map<String, Object> body, String sigKey) {
        Object[] keys = params.keySet().toArray();
        Arrays.sort(keys);
        StringBuilder paramBuffer = new StringBuilder(128);
        for (int i = 0; i < keys.length; i++) {
            paramBuffer.append(keys[i]).append("=").append(params.get(String.valueOf(keys[i])));
            if (i != keys.length - 1) {
                paramBuffer.append("&");
            }
        }
        String buffer = path + "?" + paramBuffer + JsonUtils.toJsonString(body) + sigKey;
        LOGGER.debug("md5 str:{}", buffer);
        String sig = StringUtils.md5(buffer);
        if (sig == null) {
            sig = StringUtils.EMPTY;
        }
        return sig.toLowerCase();
    }
}
package com.yorha.common.resource.resservice.errorcode;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import res.template.ErrorcodeTemplate;

import java.util.ArrayList;

/**
 * 错误码配置表校验
 *
 * <AUTHOR>
 */
public class ErrorCodeTemplateService extends AbstractResService {

    public ErrorCodeTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

    }

    @Override
    public void checkValid() throws ResourceException {
        // 仅zone承载错误码检测
        if (!ServerContext.isZoneServer()) {
            return;
        }
        ArrayList<String> errorStr = new ArrayList<>();
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCodeId() < 10000) {
                continue;
            }
            ErrorcodeTemplate template = getResHolder().findValueFromMap(ErrorcodeTemplate.class, errorCode.getCodeId());
            if (template == null) {

                String format = StringUtils.format("错误码缺失 {} -> {}\n", errorCode.getCodeId(), errorCode.getDesc());
                if (errorStr.size() > 20) {
                    errorStr.add(format + "......\n");
                    break;
                }
                errorStr.add(format);
            }

        }
//        if (errorStr.size() > 0){
//            WechatLog.error(new ResourceException(errorStr.toString()));
//        }
    }
}

package com.yorha.common.db.tcaplus.option;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GetByPartKeyOption {
    private List<String> fieldNames;
    /**
     * 获得所有的字段
     * 默认：true
     */
    private boolean getAllFields = true;

    private int resultOffset;

    private int resultLimit;

    private long timeoutTimeMs;

    private GetByPartKeyOption() {

    }

    public List<String> getFieldNames() {
        return fieldNames;
    }

    public boolean isGetAllFields() {
        return getAllFields;
    }

    public int getResultOffset() {
        return resultOffset;
    }

    public int getResultLimit() {
        return resultLimit;
    }

    public long getTimeoutTimeMs() {
        return timeoutTimeMs;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static final class Builder {
        private List<String> fieldNames;
        private boolean getAllFields = true;
        private int resultOffset;
        private int resultLimit;
        private long timeout;

        private Builder() {
        }

        public Builder withFieldNames(List<String> fieldNumbers) {
            this.fieldNames = fieldNumbers;
            return this;
        }

        public Builder withFieldNames(String... fieldNumbers) {
            this.fieldNames = Arrays.asList(fieldNumbers);
            return this;
        }

        public Builder withGetAllFields(boolean getAllFields) {
            this.getAllFields = getAllFields;
            return this;
        }

        public Builder withResultOffset(int resultOffset) {
            this.resultOffset = resultOffset;
            return this;
        }

        public Builder withResultLimit(int resultLimit) {
            this.resultLimit = resultLimit;
            return this;
        }

        public Builder withTimeout(long timeout) {
            this.timeout = timeout;
            return this;
        }

        public GetByPartKeyOption build() {
            GetByPartKeyOption getByPartKeyOption = new GetByPartKeyOption();
            getByPartKeyOption.resultLimit = this.resultLimit;
            getByPartKeyOption.resultOffset = this.resultOffset;
            getByPartKeyOption.getAllFields = this.getAllFields;
            getByPartKeyOption.fieldNames = this.fieldNames;
            getByPartKeyOption.timeoutTimeMs = this.timeout;
            return getByPartKeyOption;
        }
    }
}

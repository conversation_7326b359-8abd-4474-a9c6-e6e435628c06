package com.yorha.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class UrlUtils {
    private static final Logger LOGGER = LogManager.getLogger(UrlUtils.class);

    public static String buildUrl(String url, Map<String, Object> params) {
        return url + "?" + buildParams(params);
    }

    public static String buildParams(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (Entry<String, Object> entry : params.entrySet()) {
            if (i > 0) {
                sb.append("&");
            }
            i++;
            try {
                Object value = entry.getValue();
                if (value != null && StringUtils.isNotEmpty(String.valueOf(value))) {
                    sb.append(entry.getKey()).append("=").append(URLEncoder.encode(String.valueOf(value), "UTF-8"));
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        return sb.toString();
    }

    public static String buildUrlWithEmptyValue(String url, Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        sb.append(url);
        for (Entry<String, Object> entry : params.entrySet()) {
            if (i == 0) {
                sb.append("?");
            } else {
                sb.append("&");
            }
            i++;
            try {
                Object value = entry.getValue();
                if (value == null || StringUtils.isEmpty(String.valueOf(value))) {
                    value = "";
                }
                sb.append(entry.getKey()).append("=").append(URLEncoder.encode(String.valueOf(value), "UTF-8"));
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        return sb.toString();
    }

    public static String buildParams(List<Pair<String, Object>> params) {
        return params.stream()
                .map(pair -> {
                    try {
                        return pair.getFirst() + "=" + URLEncoder.encode(String.valueOf(pair.getSecond()), "UTF-8");
                    } catch (Exception e) {
                        LOGGER.error(e.getMessage(), e);
                    }
                    return null;
                }).collect(Collectors.joining("&"));
    }
}

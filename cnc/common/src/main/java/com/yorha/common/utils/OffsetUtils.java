package com.yorha.common.utils;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.UpsertResult;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.TableCommon;
import com.yorha.proto.TcaplusDb;

import java.util.concurrent.TimeUnit;

public class OffsetUtils {

    /**
     * 从db拉取offset和版本号
     */
    public static Pair<Long, Integer> getOffsetFromDb(GameActorWithCall actor) {
        TcaplusDb.KVStoreTable.Builder builder = TcaplusDb.KVStoreTable.newBuilder();
        builder.setZoneId(0).setKey("offset");

        GetOption getOption = GetOption.newBuilder().withGetAllFields(true).build();

        GetResult<TcaplusDb.KVStoreTable.Builder> getResult = null;
        try {
            getResult = actor.callGameDb(new SelectUniqueAsk<>(builder, getOption));
        } catch (Exception e) {
            throw new GeminiException("getOffsetFromDb fail", e);
        }
        if (getResult.isNotExist()) {
            // 数据库没有offset
            return Pair.of(0L, 0);
        }
        if (!getResult.isOk()) {
            throw new GeminiException("getOffsetFromDb fail not ok {}", getResult);
        }
        long offsetFromDb = getResult.value.getValue().getLongValue();
        int version = getResult.getVersion();
        return Pair.of(offsetFromDb, version);
    }

    /**
     * 用版本号和新offset插入db
     */
    public static long changeOffsetToDb(Pair<Long, Integer> offsetFromDbWithVersion, int addSeconds, GameActorWithCall actor) {
        long offsetFromDb = offsetFromDbWithVersion.getFirst();
        int version = offsetFromDbWithVersion.getSecond();

        long newOffset = offsetFromDb + TimeUnit.SECONDS.toMillis(addSeconds);

        TcaplusDb.KVStoreTable.Builder builder = TcaplusDb.KVStoreTable.newBuilder();
        builder.setZoneId(0).setKey("offset").setValue(TableCommon.KVStoreValue.newBuilder().setLongValue(newOffset).build());
        UpsertResult<TcaplusDb.KVStoreTable.Builder> result;
        try {
            result = actor.callGameDb(new UpsertAsk<>(builder, UpsertOption.newBuilder().setVersion(version).build()));
        } catch (Exception e) {
            throw new GeminiException("changeOffsetToDb fail", e);
        }
        if (!result.isOk()) {
            throw new GeminiException("changeOffsetToDb fail not ok {}", result);
        }

        return newOffset;
    }

}

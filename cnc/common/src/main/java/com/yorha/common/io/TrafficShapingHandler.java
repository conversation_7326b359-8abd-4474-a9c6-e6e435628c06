package com.yorha.common.io;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufHolder;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;

/**
 * 协议性能分析handler
 *
 * <AUTHOR>
 */
@Sharable
public class TrafficShapingHandler extends ChannelDuplexHandler {

    /**
     * 流量性能分析
     */
    protected final TrafficCounter trafficCounter;

    public TrafficShapingHandler(String name) {
        trafficCounter = new TrafficCounter(name);
        trafficCounter.start();
    }
    
    public final void release() {
        trafficCounter.stop();
    }

    /**
     * 清空tick并展示
     */
    public void clearTickAndShow() {
        trafficCounter.resetTickCapture();
    }

    @Override
    public void channelRead(final ChannelHandlerContext ctx, final Object msg) {
        long size = calculateSize(msg);
        if (size > -1) {
            trafficCounter.onReadBytes(size);
        }
        ctx.fireChannelRead(msg);
    }

    @Override
    public void write(final ChannelHandlerContext ctx, final Object msg, final ChannelPromise promise) throws Exception {
        long size = calculateSize(msg);
        if (size > -1) {
            trafficCounter.onWriteBytes(size);
        }
        ctx.write(msg, promise);
    }

    private long calculateSize(Object msg) {
        if (msg instanceof ByteBuf) {
            return ((ByteBuf) msg).readableBytes();
        }
        if (msg instanceof ByteBufHolder) {
            return ((ByteBufHolder) msg).content().readableBytes();
        }
        return -1;
    }
}

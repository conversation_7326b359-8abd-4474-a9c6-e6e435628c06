package com.yorha.common.utils.id;

import com.yorha.common.etcd.EtcdClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * 以ETCD为中间件的远端id生成器。
 *
 * <AUTHOR>
 */
public class EtcdIdFactory implements IIdFactory {
    private static final Logger LOGGER = LogManager.getLogger(EtcdIdFactory.class);
    private final EtcdClient client;
    private final String etcdKey;
    /**
     * 发号器初始编码段
     */
    private static final String START_ID = "10";

    public EtcdIdFactory(EtcdClient client, String etcdKey) {
        this.client = client;
        this.etcdKey = etcdKey;
        // 初始编码段从10开始  保留0-419w的id, 用于mapBuilding的唯一id(0-11w)
        this.client.setNX(this.etcdKey, START_ID);
    }

    @Override
    public long nextId(final String reason) {
        for (int i = 0; i < 15; i++) {
            final String rawOldValue = this.client.getSingle(this.etcdKey);
            final long oldValue = Long.parseLong(rawOldValue);
            if (this.client.cas(this.etcdKey, rawOldValue, String.valueOf(oldValue + 1))) {
                LOGGER.info("nextId id={}, reason={}", oldValue, reason);
                return oldValue;
            }
            LockSupport.parkNanos(this, TimeUnit.MILLISECONDS.toNanos(100 + (new Random()).nextInt(100)));
        }
        throw new RuntimeException("etcd id factory not available");
    }

    @Override
    public String toString() {
        return "EtcdIdFactory{" +
                "etcdKey='" + etcdKey + '\'' +
                '}';
    }
}

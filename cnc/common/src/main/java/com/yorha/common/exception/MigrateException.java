package com.yorha.common.exception;

import com.yorha.gemini.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class MigrateException extends GeminiException {
    private final int zoneId;
    private static final int ERROR_ID_MIGRATE = 2;

    public MigrateException(int zoneId, String msg) {
        super(msg, null, ERROR_ID_MIGRATE);
        this.zoneId = zoneId;
    }

    public MigrateException(int zoneId, String msgPattern, Object... arguments) {
        super(StringUtils.format(msgPattern, arguments), null, ERROR_ID_MIGRATE);
        this.zoneId = zoneId;
    }

    public int getZoneId() {
        return this.zoneId;
    }

    @Override
    public String toString() {
        return "MigrateException{" +
                "zoneId=" + zoneId +
                '}';
    }
}

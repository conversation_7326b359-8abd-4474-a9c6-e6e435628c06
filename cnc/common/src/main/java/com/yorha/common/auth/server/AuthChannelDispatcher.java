package com.yorha.common.auth.server;

import com.google.common.collect.Maps;
import com.yorha.common.constant.AuthConstant;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CsAccount;
import com.yorha.proto.User;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 鉴权渠道分发器
 *
 * <AUTHOR>
 */
public class AuthChannelDispatcher {
    public static final Logger LOGGER = LogManager.getLogger(AuthChannelDispatcher.class);

    private static final Map<AuthConstant.AuthChannelType, Cube> AUTH_CHANNEL_MAP = Maps.newEnumMap(AuthConstant.AuthChannelType.class);

    static {
        AuthChannelDispatcher.register(new AuthChannelGemini());
        AuthChannelDispatcher.register(new AuthChannelIntl());
        AuthChannelDispatcher.register(new AuthChannelMsdk());
    }

    static class Cube {
        private final AuthChannel channel;
        volatile boolean initialized = false;
        final ReentrantLock lock = new ReentrantLock();

        Cube(AuthChannel channel) {
            this.channel = channel;
        }

        AuthChannel initAndGet() {
            if (initialized) {
                return channel;
            }
            lock.lock();
            try {
                if (initialized) {
                    return channel;
                }
                LOGGER.info("start init auth channel: {}", channel.getClass().getName());
                channel.init();
                initialized = true;
            } finally {
                lock.unlock();
            }
            return channel;
        }
    }

    public static void register(AuthChannel channel) {
        LOGGER.info("auth channel register {}", channel.authChannelType());
        AUTH_CHANNEL_MAP.put(channel.authChannelType(), new Cube(channel));
    }

    private static AuthChannel initAndGet(AuthConstant.AuthChannelType type) {
        Cube cube = AUTH_CHANNEL_MAP.get(type);
        if (cube == null) {
            throw new GeminiException("dispatch auth channel handler null, type={}", type);
        }
        return cube.initAndGet();
    }

    /**
     * 可能会get出来是 ANY 这个type，需要注意处理哦
     */
    public static AuthConstant.AuthChannelType getAuthChannelType() {
        return AuthConstant.AuthChannelType.forNumber(ClusterConfigUtils.getWorldConfig().getIntItem("auth_channel"));
    }

    public static AuthChannel dispatchChannelBy(User.GetRoleList_C2S_Msg msg) {
        AuthConstant.AuthChannelType serverChannelType = getAuthChannelType();
        if (serverChannelType == AuthConstant.AuthChannelType.ANY) {
            for (Cube cube : AUTH_CHANNEL_MAP.values()) {
                // 先就简单的
                AuthChannel channel = cube.initAndGet();
                if (!channel.getOpenId(msg).isEmpty()) {
                    return channel;
                }
            }
        } else {
            AuthConstant.AuthChannelType msgChannelType = getAuthChannelTypeFromMsg(msg);
            if (msgChannelType != serverChannelType) {
                var expMsg = StringUtils.format("auth channel type not match, msgChannelType={}, channelType={}", msgChannelType, serverChannelType);
                throw new GeminiException(ErrorCode.AUTH_CHANNEL_NOT_MATCH, expMsg);
            }
            return initAndGet(serverChannelType);
        }
        return null;
    }

    public static AuthChannel dispatchChannelBy(CsAccount.DirGetZone_C2S_Msg msg) {
        AuthConstant.AuthChannelType serverChannelType = getAuthChannelType();
        if (serverChannelType == AuthConstant.AuthChannelType.ANY) {
            for (Cube cube : AUTH_CHANNEL_MAP.values()) {
                AuthChannel channel = cube.initAndGet();
                if (!channel.getOpenId(msg).isEmpty()) {
                    return channel;
                }
            }
        } else {
            AuthConstant.AuthChannelType msgChannelType = getAuthChannelTypeFromMsg(msg);
            if (msgChannelType != serverChannelType) {
                var expMsg = StringUtils.format("auth channel type not match, msgChannelType={}, channelType={}", msgChannelType, serverChannelType);
                throw new GeminiException(ErrorCode.AUTH_CHANNEL_NOT_MATCH, expMsg);
            }
            return initAndGet(serverChannelType);
        }
        return null;
    }

    /**
     * @return gemini渠道可否登录
     */
    public static boolean isGeminiChannelAllow() {
        AuthConstant.AuthChannelType channelType = getAuthChannelType();
        return channelType == AuthConstant.AuthChannelType.ANY || channelType == AuthConstant.AuthChannelType.GEMINI;
    }

    /**
     * 从消息中获取渠道类型
     *
     * @param msg DirGetZone_C2S_Msg
     * @return 仅会返回AuthChannelType.INTL或AuthChannelType.GEMINI任意一种
     * @throws GeminiException 当两个渠道相关项都填充了，或者两个渠道相关项都没填充时抛出
     */
    private static AuthConstant.AuthChannelType getAuthChannelTypeFromMsg(CsAccount.DirGetZone_C2S_Msg msg) throws GeminiException {
        if (!msg.hasOpenId() && !msg.hasAccountToken()) {
            // 渠道相关均未填充，返回错误
            var expMsg = StringUtils.format("getAuthChannelTypeFromMsg, auth channel type not found", msg);
            throw new GeminiException(ErrorCode.AUTH_CHANNEL_NOT_MATCH, expMsg);
        }
        if (msg.hasOpenId() && msg.hasAccountToken()) {
            // 渠道相关两项都填了，默认INTL
            LOGGER.warn("getAuthChannelTypeFromMsg DirGetZone_C2S_Msg has both channel={}", msg);
        }
        // 不是INTL就是GEMINI，NOTE(furson): 如果之后引入第三渠道这里的逻辑需要更改
        if (msg.hasOpenId()) {
            AuthConstant.AuthChannelType authChannelType = getAuthChannelType();
            if (authChannelType == AuthConstant.AuthChannelType.MSDK || authChannelType == AuthConstant.AuthChannelType.INTL) {
                return authChannelType;
            }
        }
        return AuthConstant.AuthChannelType.GEMINI;
    }

    /**
     * 从消息中获取渠道类型
     *
     * @param msg GetRoleList_C2S_Msg
     * @return 仅会返回AuthChannelType.INTL或AuthChannelType.GEMINI任意一种
     * @throws GeminiException 当两个渠道相关项都填充了，或者两个渠道相关项都没填充时抛出
     */
    private static AuthConstant.AuthChannelType getAuthChannelTypeFromMsg(User.GetRoleList_C2S_Msg msg) throws GeminiException {
        if (!msg.hasIntl() && !msg.hasAccountToken() && !msg.hasMsdkAccount()) {
            // 渠道相关均未填充，返回错误
            throw new GeminiException(ErrorCode.AUTH_CHANNEL_NOT_MATCH, StringUtils.format("getAuthChannelTypeFromMsg, auth channel type not found", msg));
        }
        if (msg.hasIntl() && msg.hasAccountToken()) {
            // 渠道相关两项都填了，默认INTL
            LOGGER.warn("getAuthChannelTypeFromMsg GetRoleList_C2S_Msg has both channel={}", msg);
        }
        // 不是INTL就是GEMINI，NOTE(furson): 如果之后引入第三渠道这里的逻辑需要更改
        if (msg.hasIntl()) {
            return AuthConstant.AuthChannelType.INTL;
        } else if (msg.hasMsdkAccount()) {
            return AuthConstant.AuthChannelType.MSDK;
        }
        return AuthConstant.AuthChannelType.GEMINI;
    }
}

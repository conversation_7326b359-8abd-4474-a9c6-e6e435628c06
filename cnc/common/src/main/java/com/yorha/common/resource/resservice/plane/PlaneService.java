package com.yorha.common.resource.resservice.plane;

import com.yorha.common.constant.Constants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.BattlePlaneTemplate;
import res.template.BattlePlaneUpgradeTemplate;
import res.template.ConpensationComputorTemplate;
import res.template.SpyCostTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 飞机相关的配置数据
 *
 * <AUTHOR>
 */
public class PlaneService extends AbstractResService {
    private final Map<Long, Integer> spyCostMap = new HashMap<>();
    // 补偿配置
    private final Map<Integer, Map<Integer, ConpensationComputorTemplate>> makeUpConfig = new HashMap<>();

    public PlaneService(ResHolder resHolder) {
        super(resHolder);
    }


    @Override
    public void load() throws ResourceException {
        Collection<SpyCostTemplate> spyCostTemplates = getResHolder().getListFromMap(SpyCostTemplate.class);
        for (SpyCostTemplate spyCostTemplate : spyCostTemplates) {
            spyCostMap.put(getSpyId(spyCostTemplate.getType(), spyCostTemplate.getLevel()), spyCostTemplate.getCurrencyNum());
        }
        Collection<ConpensationComputorTemplate> conpensationComputorTemplates = getResHolder().getListFromMap(ConpensationComputorTemplate.class);
        for (ConpensationComputorTemplate conpensationComputorTemplate : conpensationComputorTemplates) {
            makeUpConfig.computeIfAbsent(conpensationComputorTemplate.getCityLevel(), key -> new HashMap<>());
            makeUpConfig.get(conpensationComputorTemplate.getCityLevel()).put(conpensationComputorTemplate.getSoildierLevel(), conpensationComputorTemplate);
        }
    }

    public ConpensationComputorTemplate getMakeUpConfig(int cityLevel, int soldierLevel) {
        return makeUpConfig.get(cityLevel).get(soldierLevel);
    }

    private long getSpyId(CommonEnum.SpyType spyType, int level) {
        if (spyType.getNumber() <= 0) {
            throw new GeminiException("PlaneResService getSpyId spyType:{}", spyType);
        }
        return spyType.getNumber() * 10000L + level;
    }

    public Integer getCostNum(CommonEnum.SpyType spyType, int level) {
        return spyCostMap.getOrDefault(getSpyId(spyType, level), null);
    }


    @Override
    public void checkValid() throws ResourceException {
        for (BattlePlaneTemplate battlePlaneTemplate : getResHolder().getListFromMap(BattlePlaneTemplate.class)) {
            if (getResHolder().findValueFromMap(BattlePlaneUpgradeTemplate.class, battlePlaneTemplate.getInitModel()) == null) {
                throw new ResourceException(StringUtils.format("空军战斗机表初始等级异常. model:{}", battlePlaneTemplate.getInitModel()));
            }
            if (StringUtils.isNotEmpty(battlePlaneTemplate.getCost())) {
                String[] cost = battlePlaneTemplate.getCost().split(Constants.XIA_HUA_XIAN);
                if (cost.length != 2) {
                    throw new ResourceException(StringUtils.format("空军战斗机道具消耗配置异常. model:{}, cost:{}",
                            battlePlaneTemplate.getInitModel(), battlePlaneTemplate.getCost()));
                }
                for (String s : cost) {
                    try {
                        Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        throw new ResourceException(StringUtils.format("空军战斗机道具消耗配置异常. 无法解析的配置类型 model:{}, cost:{}",
                                battlePlaneTemplate.getInitModel(), battlePlaneTemplate.getCost()));
                    }
                }
            }
        }
    }

    public Pair<Integer, Integer> getBattlePlaneCost(String cost) {
        if (StringUtils.isEmpty(cost)) {
            return null;
        }
        String[] split = cost.split(Constants.XIA_HUA_XIAN);
        return Pair.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
    }
}

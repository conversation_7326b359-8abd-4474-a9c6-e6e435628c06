package com.yorha.common.db.mongo.listener;

import com.mongodb.connection.ConnectionId;
import com.mongodb.event.CommandFailedEvent;
import com.mongodb.event.CommandListener;
import com.mongodb.event.CommandStartedEvent;
import com.mongodb.event.CommandSucceededEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.BsonDocument;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class MongoCommandListener implements CommandListener {
    private static final Logger LOGGER = LogManager.getLogger(MongoCommandListener.class);
    // 超时阈值（毫秒）
    private static final long SLOW_THRESHOLD_MS = 10;

    @Override
    public void commandStarted(CommandStartedEvent event) {
        ConnectionId connectionId = event.getConnectionDescription().getConnectionId();
        BsonDocument command = event.getCommand();
        String databaseName = event.getDatabaseName();
        String commandName = event.getCommandName();
        int requestId = event.getRequestId();
        LOGGER.debug("CMD_START |{}| reqId: {} | db: {} | cmd: {} | detail: {}",
                connectionId, requestId, databaseName, commandName, command.toJson());
    }

    @Override
    public void commandSucceeded(CommandSucceededEvent event) {
        ConnectionId connectionId = event.getConnectionDescription().getConnectionId();
        int requestId = event.getRequestId();

        long durationMs = event.getElapsedTime(TimeUnit.MILLISECONDS);

        String commandName = event.getCommandName();
        String databaseName = event.getDatabaseName();

        if (durationMs > SLOW_THRESHOLD_MS) {
            LOGGER.warn("SLOW_CMD_SUCCESS |{}| reqId: {} | db: {} | cmd: {} | time: {} ms",
                    connectionId, requestId, databaseName, commandName, durationMs);
        } else {
            LOGGER.debug("CMD_SUCCESS |{}| reqId: {} | db: {} | cmd: {} | time: {} ms",
                    connectionId, requestId, databaseName, commandName, durationMs);
        }
    }

    @Override
    public void commandFailed(CommandFailedEvent event) {
        ConnectionId connectionId = event.getConnectionDescription().getConnectionId();
        int requestId = event.getRequestId();
        long durationMs = event.getElapsedTime(TimeUnit.MILLISECONDS);
        String commandName = event.getCommandName();
        String databaseName = event.getDatabaseName();
        Throwable throwable = event.getThrowable();
        LOGGER.warn("CMD_FAILED |{}| reqId: {} | db: {} | cmd: {} | time: {} ms| error: {}",
                connectionId, requestId, databaseName, commandName, durationMs, throwable.getMessage(), throwable);
    }
}

package com.yorha.common.server;

import com.yorha.common.exception.GeminiException;

/**
 * 环境标签
 */
public enum WorldEnvFlag {
    DEV("dev"),
    TEST("test"),
    PROD("prod"),
    ;

    private final String name;

    WorldEnvFlag(String name) {
        this.name = name;
    }

    public static WorldEnvFlag forName(String name) {
        for (WorldEnvFlag flag : WorldEnvFlag.values()) {
            if (flag.name.equals(name)) {
                return flag;
            }
        }
        throw new GeminiException("WorldEnvFlag not recognized! {}", name);
    }
}

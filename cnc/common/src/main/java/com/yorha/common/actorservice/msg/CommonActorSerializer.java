package com.yorha.common.actorservice.msg;

import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.common.actor.msg.ActorSerializer;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.System;

/**
 * 从设计上来说，应该是可以支持各种序列化方式的，现在仅仅使用proto序列化TypedMsg
 *
 * <AUTHOR>
 */
public class CommonActorSerializer implements ActorSerializer {

    public CommonActorSerializer() {
    }

    @Override
    public byte[] serialize(Object obj) {
        if (obj instanceof ActorMsgEnvelope) {
            ActorMsgEnvelope msg = (ActorMsgEnvelope) obj;
            return msg.toProto().toByteArray();
        } else {
            // 也可以有其他对象类型
            WechatLog.error("only ActorMsgEnvelope can be serialized.! obj={}", obj);
            return null;
        }
    }

    @Override
    public Object deserialize(byte[] bytes) {
        try {
            System.YoActorMsgEnvelope yoEnvelope = System.YoActorMsgEnvelope.parseFrom(bytes);
            return ActorMsgEnvelope.createFromProto(yoEnvelope);
        } catch (InvalidProtocolBufferException e) {
            WechatLog.error("now only support ActorMsgEnvelope!!");
        }
        return null;
    }

}

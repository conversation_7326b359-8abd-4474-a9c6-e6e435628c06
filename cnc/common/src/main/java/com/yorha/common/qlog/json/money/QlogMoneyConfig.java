package com.yorha.common.qlog.json.money;

import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.json.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Qlog 用到的货币结构
 */
public class QlogMoneyConfig {
    private static final Logger LOGGER = LogManager.getLogger(QlogMoneyConfig.class);
    /**
     * 0: None (正常不应填0）
     * 1 - 4: 资源（石油、钢铁、稀土、泰矿）
     * 5: 金条
     * 6: 远征币
     * 较大数值可能是道具id
     */
    private int moneyType;
    private int moneyCount;

    public QlogMoneyConfig(int moneyType, int moneyCount) {
        this.moneyType = moneyType;
        this.moneyCount = moneyCount;
    }

    public int getMoneyType() {
        return moneyType;
    }

    public int getMoneyCount() {
        return moneyCount;
    }

    public void setMoneyCount(int moneyCount) {
        this.moneyCount = moneyCount;
    }

    public void setMoneyType(int moneyType) {
        this.moneyType = moneyType;
    }

    public static String getQlogMoneyConfigStr(int moneyType, int moneyCount) {
        return JsonUtils.toJsonString(new QlogMoneyConfig(moneyType, moneyCount));
    }

    public static String getQlogMoneyConfigStr(List<Integer> moneyTypes, List<Integer> moneyCounts) {
        if (moneyTypes == null || moneyCounts == null
                || moneyTypes.size() != moneyCounts.size()
                || moneyTypes.size() == 0) {
            LOGGER.error("moneyTypes or moneyCounts is null or size not equal or size is 0.");
            return "";
        }
        int size = moneyTypes.size();
        // 只有一个不需要构建list
        if (size == 1) {
            LOGGER.warn("moneyTypes size is 1, use getQlogMoneyConfigStr(int moneyType, int moneyCount) instead.");
            return getQlogMoneyConfigStr(moneyTypes.get(0), moneyCounts.get(0));
        }
        // 构建list返回list的字符串
        List<QlogMoneyConfig> qlogMoneyConfigs = new ArrayList<>();
        for (int i = 0; i < moneyTypes.size(); i++) {
            qlogMoneyConfigs.add(new QlogMoneyConfig(moneyTypes.get(i), moneyCounts.get(i)));
        }
        return JsonUtils.toJsonString(qlogMoneyConfigs);
    }

    public static String getQlogMoneyConfigStr(List<IntPairType> item) {
        if (item == null || item.size() == 0) {
            LOGGER.error("item is null or size is 0.");
            return "";
        }
        int size = item.size();
        // 只有一个不需要构建list
        if (size == 1) {
            return getQlogMoneyConfigStr(item.get(0).getKey(), item.get(0).getValue());
        }
        // 构建list返回list的字符串
        List<QlogMoneyConfig> qlogMoneyConfigs = new ArrayList<>();
        for (int i = 0; i < item.size(); i++) {
            qlogMoneyConfigs.add(new QlogMoneyConfig(item.get(i).getKey(), item.get(i).getValue()));
        }
        return JsonUtils.toJsonString(qlogMoneyConfigs);
    }
}

package com.yorha.common.db.tcaplus.msg;

import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.utils.ClassNameCacheUtils;

/**
 * @param <RESP> 回包类型，用于防止错误强转
 */
public interface GameDbReq<RESP extends GameDbResp> extends IActorMsg {

    @Override
    default boolean canRemote() {
        return false;
    }

    @Override
    default String profName() {
        return ClassNameCacheUtils.getSimpleName(getClass());
    }
}

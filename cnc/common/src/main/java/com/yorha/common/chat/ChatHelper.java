package com.yorha.common.chat;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.constant.ChatConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.BatchSelectAsk;
import com.yorha.common.db.tcaplus.msg.IncreaseAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CommonMsg.ChatDescriptionInfo;
import com.yorha.proto.CommonMsg.ChatMessage;
import com.yorha.proto.PlayerChat;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.ConstChatTemplate;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ChatHelper {
    private static final Logger LOGGER = LogManager.getLogger(ChatHelper.class);
    private static final String PRIVATE_PLAYER_AND_HAO = "&";
    private static final int PRIVATE_PLAYER_NUM = 2;

    /**
     * 构造一个私聊的id。
     *
     * @param playerId1 私聊玩家1。
     * @param playerId2 私聊玩家2。
     * @return 私聊玩家id。
     */
    public static String getPrivateChatIdFromPlayerIds(final long playerId1, final long playerId2) {
        if (playerId1 < playerId2) {
            return playerId1 + PRIVATE_PLAYER_AND_HAO + playerId2;
        }
        return playerId2 + PRIVATE_PLAYER_AND_HAO + playerId1;
    }

    /**
     * 从一个私聊id重拆解出聊天双方的id。
     * 长度为2，代表聊天双方。
     *
     * @param chatId 私聊id。
     * @return 聊天双方的玩家id。
     */
    public static long[] getPrivateChatRelatedPlayerIds(final String chatId) {
        final String[] split = StringUtils.split(chatId, PRIVATE_PLAYER_AND_HAO);
        if (split.length != PRIVATE_PLAYER_NUM) {
            throw new GeminiException(ErrorCode.CHAT_CHAT_CHANNEL_ID_NOT_CORRECT);
        }
        return new long[]{
                Long.parseLong(split[0]),
                Long.parseLong(split[1])
        };
    }

    /**
     * @return 单次用户查询聊天信息最多遍历的聊天记录数量。
     */
    public static int getMaxChatMsgCount() {
        return ResHolder.getInstance().getConstTemplate(ConstChatTemplate.class).getChatReqMessageNumber() * ChatConstants.CACHE_PAGE_SIZE;
    }

    /**
     * @return 单次DB查询聊天记录表最多查找的记录数量。
     */
    public static int getChatReqMessageNumber() {
        return ResHolder.getInstance().getConstTemplate(ConstChatTemplate.class).getChatReqMessageNumber();
    }

    /**
     * 初始化时加载聊天的描述信息
     */
    public static long getChatMaxIndex(GameActorWithCall actor, int channelType, String channelId) {
        GetOption option = GetOption.newBuilder().withGetAllFields(true).build();
        TcaplusDb.ChatDescriptionTable.Builder req = TcaplusDb.ChatDescriptionTable.newBuilder();
        req.setChannelType(channelType).setChannelId(channelId);
        SelectUniqueAsk<TcaplusDb.ChatDescriptionTable.Builder> selectAsk = new SelectUniqueAsk<>(req, option);
        GetResult<TcaplusDb.ChatDescriptionTable.Builder> res;
        try {
            res = actor.callGameDb(selectAsk);
            if (res.isRecordNotExist()) {
                LOGGER.warn("ChatHelper getChatMaxIndex but not exist {} {}", channelType, channelId);
                return 0;
            }
            if (!res.isOk()) {
                LOGGER.error("ChatHelper getChatMaxIndex failed {} {} {}", channelType, channelId, res.getCode());
                return 0;
            }
            return res.value.getMaxMsgIndex();
        } catch (Exception e) {
            LOGGER.error("ChatHelper getChatMaxIndex failed {} {} {}", channelType, channelId, e);
            return 0;
        }
    }

    /**
     * 从缓存中获取聊天消息
     *
     * @param fromId          [toId, fromId]
     * @param toId            [toId, fromId]
     * @param shieldSenderIds 屏蔽列表
     * @param cache           消息缓存
     * @return 缓存中的聊天消息
     */
    public static List<ChatMessage> queryChatMsgFromCache(long fromId, long toId,
                                                          @NotNull final Collection<Long> shieldSenderIds,
                                                          @NotNull final ChatCache cache) {
        if (toId < 0) {
            throw new IllegalArgumentException("toId < 0");
        }

        // 过滤无效游标
        if (fromId < toId) {
            return Collections.emptyList();
        }

        if (cache.isEmpty()) {
            return Collections.emptyList();
        }
        // id目标
        toId = Long.max(toId, fromId - ChatHelper.getMaxChatMsgCount() + 1);

        // 读取数据，直到满足拉取数量，或者达到可以读取的数据上限
        final int size = (int) Math.min(ChatHelper.getChatReqMessageNumber(), fromId - toId + 1);
        final List<CommonMsg.ChatMessage> totalChatMessages = new ArrayList<>(size);
        while (fromId >= toId) {
            // 计算拉取的数据上限
            final long finalToId = Long.max(toId, fromId - ChatHelper.getChatReqMessageNumber() + 1);
            // 提取缓存
            for (long lastTmpId = fromId; lastTmpId >= finalToId; lastTmpId--) {
                // cache中不可能包含任意更小id的消息
                if (lastTmpId < cache.getMinChatMessageId()) {
                    break;
                }
                final ChatMessage chatMessage = cache.getDataOrNull(lastTmpId);
                // 内存里无
                if (chatMessage == null) {
                    continue;
                }
                // 被屏蔽
                if (shieldSenderIds.contains(chatMessage.getChatMessageSender().getSenderId())) {
                    continue;
                }
                totalChatMessages.add(chatMessage);
                // 已经达到上限
                if (totalChatMessages.size() >= size) {
                    return totalChatMessages;
                }
            }

            fromId = finalToId - 1;
        }
        return totalChatMessages;
    }

    /**
     * 根据对方actor返回的消息，有必要则再去db拉取
     *
     * @param actor        playerActor
     * @param cacheMsgList zone或clan返回的最新消息(可能为空)
     * @param chatSession  聊天频道&频道号
     * @param fromId       [toId, fromId]
     * @param toId         [toId, fromId]
     * @param shieldList   屏蔽列表
     * @return 最终的聊天消息查询结果
     */
    public static List<ChatMessage> playerQueryChatMsgOnCacheMsg(
            GameActorWithCall actor,
            @Nullable final List<CommonMsg.ChatMessage> cacheMsgList,
            CommonMsg.ChatSession chatSession,
            final long fromId,
            final long toId,
            Collection<Long> shieldList,
            final long minId
    ) {
        LOGGER.info("ChatHelper queryChatMsgWithCache {}, cacheMsgListSize={}, fromId={}, toId={}",
                actor, cacheMsgList != null ? cacheMsgList.size() : cacheMsgList, fromId, toId);

        // 全拉到了，不需要再去db拉
        if (cacheMsgList != null && cacheMsgList.size() >= fromId - toId) {
            return cacheMsgList;
        }

        final List<CommonMsg.ChatMessage> msgList = cacheMsgList != null ? new ArrayList<>(cacheMsgList) : new ArrayList<>();
        final int channelType = chatSession.getChannelType().getNumber();
        final String channelId = chatSession.getChatChannelId();
        msgList.addAll(getNotCachedMsgFromDb(actor, cacheMsgList, channelType, channelId, fromId, toId, minId, shieldList));
        return msgList;

    }


    private static List<CommonMsg.ChatMessage> getNotCachedMsgFromDb(
            final GameActorWithCall actor,
            @Nullable final List<CommonMsg.ChatMessage> cachedMsgList,
            final int channelType,
            final String channelId,
            final long c2sFromId,
            final long c2sToId,
            final long minId,
            Collection<Long> shieldList
    ) {
        long curFromId;
        // 没去别的actor拉过，直接player拉
        if (cachedMsgList == null) {
            curFromId = c2sFromId;
        } else if (cachedMsgList.isEmpty()) {
            // 之前拉的消息都被屏蔽了，往前再拉拉
            curFromId = c2sToId - 1;
        } else {
            final long minCachedMsgId = cachedMsgList.getLast().getMessageId();
            curFromId = minCachedMsgId - 1;
        }
        if (curFromId < 0) {
            LOGGER.info("ChatHelper getNotCachedMsgFromDb {} curFromId={}, < 0 skip", actor, curFromId);
            return Collections.emptyList();
        }
        // 从db拉的数量=cs需要数量-缓存拉到的数量
        final int requiredSize = (int) (c2sFromId - c2sToId) - (cachedMsgList != null ? cachedMsgList.size() : 0);

        final List<CommonMsg.ChatMessage> msgList = new ArrayList<>(requiredSize);

        while (msgList.size() < requiredSize) {
            final long curToId = Math.max(curFromId - requiredSize, 0);
            LOGGER.info("ChatHelper getNotCachedMsgFromDb {}, msgListSize={}, requiredSize={}, curFromId={}, curToId={}, minId={}",
                    actor, msgList.size(), requiredSize, curFromId, curToId, minId);
            // 到了最小id
            if (curToId < minId) {
                return msgList;
            } else if (curFromId < minId) {
                curFromId = minId;
            }
            if (curFromId < 0) {
                return msgList;
            }
            List<CommonMsg.ChatMessage> dbMsgList = ChatHelper.queryChatMsgSync(actor, channelType, channelId, curFromId, curToId, shieldList);
            msgList.addAll(dbMsgList);
            curFromId -= (requiredSize + 1);
            if (curFromId < 0) {
                return msgList;
            }
        }
        msgList.sort(Comparator.comparing(CommonMsg.ChatMessage::getMessageId).reversed());
        return msgList.subList(0, requiredSize);
    }

    /**
     * 同步查询聊天消息。
     * 剔除shieldIdList的闭区间[toId, fromId]。
     *
     * @param actor         发起查询的actor。
     * @param channelType   频道类型。
     * @param channelId     频道id。
     * @param fromId        起始消息的索引id。
     * @param toId          终止消息的索引id。
     * @param shieldChatIds 频闭的聊天id列表，闭区间中剔除的id。
     * @return 查询到的消息列表。
     */
    public static List<ChatMessage> queryChatMsgSync(
            final GameActorWithCall actor,
            final int channelType, final String channelId,
            final long fromId, final long toId,
            final Collection<Long> shieldChatIds) {
        // 批量请求
        final List<TcaplusDb.ChatTable.Builder> listReq = new ArrayList<>(Math.max(0, (int) (fromId - toId)));
        for (long id = fromId; id >= toId; id--) {
            if (shieldChatIds.contains(id)) {
                continue;
            }
            TcaplusDb.ChatTable.Builder req = TcaplusDb.ChatTable.newBuilder();
            req.setChannelType(channelType).setChannelId(channelId).setMsgIndex(id);
            listReq.add(req);
        }

        if (listReq.isEmpty()) {
            return Collections.emptyList();
        }

        // 构造请求
        final BatchGetOption batchOption = BatchGetOption.newBuilder().withGetAllFields(true).build();
        final BatchSelectAsk<TcaplusDb.ChatTable.Builder> batchSelectAsk = new BatchSelectAsk<>(listReq, batchOption);
        final BatchGetResult<TcaplusDb.ChatTable.Builder> result;
        try {
            result = actor.callGameDb(batchSelectAsk);
        } catch (Exception e) {
            LOGGER.error("ChatHelper getChatMsg, totalMsgNum={}, absentMsgNum={}, ", fromId - toId, listReq.size(), e);
            throw e;
        }

        final List<CommonMsg.ChatMessage> allChatMsg;
        // 过滤无效的数据
        if (CollectionUtils.isNotEmpty(result.values)) {
            allChatMsg = new ArrayList<>(result.values.size());
            for (final ValueWithVersion<TcaplusDb.ChatTable.Builder> vv : result.values) {
                final CommonMsg.ChatMessage.Builder message = vv.value.getMsgBuilder();
                ChatHelper.clearSharedMailContent(message);
                allChatMsg.add(message.build());
            }
        } else {
            allChatMsg = Collections.emptyList();
        }

        LOGGER.info("ChatHelper queryChatMsgSync, totalMsgNum={}, absentMsgNum={}, allChatMsgSize={}, requestId={}, code={}",
                listReq.size(), listReq.size() - result.values.size(), allChatMsg.size(), result.requestId, result.getCode());
        return allChatMsg;

    }

    /**
     * 插入频道描述表 成功或已存在正常  其他抛异常
     */
    public static void insertChatDescription(GameActorWithCall actor, int channelType, String channelId) {
        TcaplusDb.ChatDescriptionTable.Builder req = TcaplusDb.ChatDescriptionTable.newBuilder();
        req.setChannelType(channelType).setChannelId(channelId).setMaxMsgIndex(0);
        req.setDescription(ChatDescriptionInfo.newBuilder().setCreateTsMs(SystemClock.now()));
        InsertAsk<TcaplusDb.ChatDescriptionTable.Builder> insertAsk = new InsertAsk<>(req);
        InsertResult<TcaplusDb.ChatDescriptionTable.Builder> insertResult = actor.callGameDb(insertAsk);
        // 刚好有人同时插入了 OK的
        if (DbUtil.isRecordAlreadyExist(insertResult.getCode())) {
            return;
        }
        if (!insertResult.isOk()) {
            throw new GeminiException("insert chat description failed. code:{}", insertResult.getCode());
        }
    }

    /**
     * 插入一条消息 对外统一接口
     * retryIfNotExistDes  如果increase索引时不存在是否尝试插入并重试
     */
    public static Pair<ChatMessage, Long> onChatMsgRequest(GameActorWithCall actor, String channelId, int channelType, ChatMessage chatMessage) {
        GeminiStopWatch watch = new GeminiStopWatch("chat request");
        // ChatDescriptionTable自增maxIndex请求
        TcaplusDb.ChatDescriptionTable.Builder increaseReq = TcaplusDb.ChatDescriptionTable.newBuilder();
        increaseReq.setChannelType(channelType)
                .setChannelId(channelId)
                .setMaxMsgIndex(1);
        IncreaseOption.Builder increaseOption = IncreaseOption.newBuilder();
        increaseOption.setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL);
        IncreaseAsk<TcaplusDb.ChatDescriptionTable.Builder> increaseAsk = new IncreaseAsk<>(increaseReq, increaseOption.build());
        IncreaseResult<TcaplusDb.ChatDescriptionTable.Builder> result = actor.callGameDb(increaseAsk);
        watch.mark("try increase");
        // 记录不存在 尝试插入
        if (result.isRecordNotExist()) {
            // 保底插入
            insertChatDescription(actor, channelType, channelId);
            watch.mark("insert description");
            // 再次尝试插入
            result = actor.callGameDb(increaseAsk);
            watch.mark("try increase2");
            if (!result.isOk()) {
                // 其他db错误
                LOGGER.error("ChatHelper onChatMsgRequest insert description fail, channelType={}, channelId={} code={}",
                        channelType, channelId, result.getCode());
                throw new GeminiException(ErrorCode.FAILED);
            }
        } else if (!result.isOk()) {
            // 其他db错误
            LOGGER.error("ChatHelper onChatMsgRequest insert description fail, channelType={}, channelId={} code={}",
                    channelType, channelId, result.getCode());
            throw new GeminiException(ErrorCode.FAILED);
        }
        long msgId = result.value.getMaxMsgIndex();
        long retCreateTsMs = result.value.getDescription().getCreateTsMs();
        ChatMessage finalChatMsg = chatMessage.toBuilder().setMessageId(msgId).build();
        // 消息插入db的请求
        TcaplusDb.ChatTable.Builder insertReq = TcaplusDb.ChatTable.newBuilder();
        insertReq.setChannelType(channelType).setChannelId(channelId).setMsgIndex(msgId).setMsg(finalChatMsg);
        InsertAsk<TcaplusDb.ChatTable.Builder> insertAsk = new InsertAsk<>(insertReq, InsertOption.newBuilder().withResponseHasRecord(true).build());
        InsertResult<TcaplusDb.ChatTable.Builder> result1 = actor.callGameDb(insertAsk);
        if (!result1.isOk()) {
            LOGGER.error("onChatMsgRequest insert new message failed channelType={}, channelId={}, code={}", channelType, channelId, result1.getCode());
            throw new GeminiException(ErrorCode.FAILED);
        }
        watch.mark("chat request end");
        LOGGER.info("ChatHelper onChatMsgRequest totalCost={}ms askStat={}", watch.getTotalCost(), watch.stat());
        return Pair.of(finalChatMsg, retCreateTsMs);
    }

    public static PlayerChat.Player_BoradcastChatMessage_NTF formBroadCastChatNtf(
            final CommonMsg.ChatMessage message,
            final String channelId,
            final CommonEnum.ChatChannel channel
    ) {
        PlayerChat.Player_BoradcastChatMessage_NTF.Builder builder = PlayerChat.Player_BoradcastChatMessage_NTF.newBuilder();
        builder.getChatSessionBuilder().setChannelType(channel).setChatChannelId(channelId);
        builder.setChatMessage(message);
        clearSharedMailContent(builder.getChatMessageBuilder());
        return builder.build();
    }

    public static void clearSharedMailContent(final CommonMsg.ChatMessage.Builder msgBuilder) {
        if (msgBuilder.getType() != CommonEnum.MessageType.MT_MAIL_SHARE) {
            msgBuilder.getMessageDataBuilder().getSharedMailBuilder().clearContent();
        }
    }
}

package com.yorha.common.resource.resservice.troop;

import com.google.common.collect.Maps;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 部队配置数据管理
 */
public class TroopResService extends AbstractResService {

    public TroopResService(ResHolder resHolder) {
        super(resHolder);
    }

    private Map<Integer, Map<CommonEnum.BuffEffectType, Long>> troopAdditionMap = new HashMap<>();
    //队伍+槽位->建筑id+等级
    private Map<IntPairType, IntPairType> troopSlotUnlock = new HashMap<>();
    //控制兵种等级上限的建筑
    private int buildTypeForUnitLevel = 0;
    private int buildTypeForHeroLevel = 0;
    //兵种ID+等级->UnitsLevelTemplate
    private Map<IntPairType, Integer> unitsLevelForTemplate = new HashMap<>();

    @Override
    public void load() throws ResourceException {
        for (TroopTemplate troopTemplate : getResHolder().getListFromMap(TroopTemplate.class)) {
            int troopId = troopTemplate.getId();
            Map<CommonEnum.BuffEffectType, Long> additionMap = this.troopAdditionMap.getOrDefault(troopId, new HashMap<>());
            for (IntPairType intPairType : troopTemplate.getAdditionGroupPairList()) {
                CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(intPairType.getKey());
                additionMap.put(buffEffectType, (long) intPairType.getValue());
            }
            this.troopAdditionMap.put(troopId, additionMap);
        }

        for (BuildUpgradeRhTemplate upgradeTemplate : getResHolder().getListFromMap(BuildUpgradeRhTemplate.class)) {
            for (IntPairType pair : upgradeTemplate.getSquadUnlockPairList()) {
                var curInfo = troopSlotUnlock.get(pair);
                //还没有设置
                if (curInfo == null) {
                    troopSlotUnlock.put(pair, IntPairType.makePair(upgradeTemplate.getBuildId(), upgradeTemplate.getBuildLevel()));
                } else {
                    //建筑等级更低的优先级越高
                    if (curInfo.getKey() == upgradeTemplate.getBuildId() && upgradeTemplate.getBuildLevel() < curInfo.getValue()) {
                        troopSlotUnlock.put(pair, IntPairType.makePair(upgradeTemplate.getBuildId(), upgradeTemplate.getBuildLevel()));
                    }
                }
            }

            if (buildTypeForUnitLevel == 0 && upgradeTemplate.getSoldierLevelMax() > 0) {
                buildTypeForUnitLevel = upgradeTemplate.getBuildId();
            }
            if (buildTypeForHeroLevel == 0 && upgradeTemplate.getHeroLevelMax() > 0) {
                buildTypeForHeroLevel = upgradeTemplate.getBuildId();
            }
        }

        for (UnitLevelRhTemplate e : getResHolder().getListFromMap(UnitLevelRhTemplate.class)) {
            unitsLevelForTemplate.put(IntPairType.makePair(e.getUnitId(), e.getLevel()), e.getId());
        }

    }

    public Map<CommonEnum.BuffEffectType, Long> getTroopAdditions(int troopId) {
        return troopAdditionMap.getOrDefault(troopId, Maps.newHashMap());
    }

    public long getTroopAddition(int troopId, CommonEnum.BuffEffectType additionType) {
        Map<CommonEnum.BuffEffectType, Long> buffEffectTypeLongMap = troopAdditionMap.get(troopId);
        if (buffEffectTypeLongMap == null || buffEffectTypeLongMap.size() <= 0) {
            return 0L;
        }
        return buffEffectTypeLongMap.getOrDefault(additionType, 0L);
    }

    @Override
    public void checkValid() throws ResourceException {
        Collection<TroopTemplate> list = getResHolder().getListFromMap(TroopTemplate.class);
        for (TroopTemplate template : list) {
            // 英雄存在性检测
            if (template.getMainHeroId() != 0) {
                getResHolder().checkValueFromMap(HeroRhTemplate.class, template.getMainHeroId(), () -> StringUtils.format("TroopTemplate MainHeroId:{} not existed", template.getMainHeroId()));
            }
            if (template.getDeputyHeroId() != 0) {
                getResHolder().checkValueFromMap(HeroRhTemplate.class, template.getDeputyHeroId(), () -> StringUtils.format("TroopTemplate DeputyHeroId:{} not existed", template.getDeputyHeroId()));
            }
            // 士兵兵种存在性检测
            List<IntPairType> soldierPairList = template.getSoldierPairList();
            if (soldierPairList.isEmpty()) {
                throw new ResourceException(StringUtils.format("id:{} 没有配置士兵数据", template.getId()));
            }
            for (IntPairType soldier : soldierPairList) {
                SoldierTypeTemplate valueFromMap = getResHolder().findValueFromMap(SoldierTypeTemplate.class, soldier.getKey());
                if (valueFromMap == null) {
                    throw new ResourceException("TroopTemplate部队配置表错误, 士兵类型不存在:{}", soldier.getKey());
                }

            }
            for (IntPairType intPairType : template.getAdditionGroupPairList()) {
                CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(intPairType.getKey());
                if (buffEffectType == null) {
                    throw new ResourceException(StringUtils.format("id:{} 配置了没实现的buffEffectType", template.getId(), intPairType.getKey()));
                }
            }
        }
    }

    public IntPairType getTroopSlotRelateBuilding(int troopId, int slot) {
        return troopSlotUnlock.get(IntPairType.makePair(troopId, slot));
    }

    public int getBuildTypeForUnitLevel() {
        return buildTypeForUnitLevel;
    }

    public int getBuildTypeForHeroLevel() {
        return buildTypeForHeroLevel;
    }

    public UnitLevelRhTemplate getUnitsLevelTemplate(int unitId, int level) {
        int id = unitsLevelForTemplate.getOrDefault(IntPairType.makePair(unitId, level), 0);
        if (id == 0) {
            return null;
        }
        return ResHolder.getTemplate(UnitLevelRhTemplate.class, id);
    }
}

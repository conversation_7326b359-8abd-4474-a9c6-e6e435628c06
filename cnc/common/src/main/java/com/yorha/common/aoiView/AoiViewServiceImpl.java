package com.yorha.common.aoiView;

import com.yorha.common.actor.AoiViewService;
import com.yorha.common.aoiView.manager.AoiNodeManager;
import com.yorha.proto.SsAoiView.*;

/**
 * <AUTHOR>
 */
public class AoiViewServiceImpl implements AoiViewService {
    private final AoiViewActor owner;

    public AoiViewServiceImpl(AoiViewActor owner) {
        this.owner = owner;
    }

    public AoiNodeManager getManager() {
        return owner.getManager();
    }

    @Override
    public void handleUpdateViewAsk(UpdateViewAsk ask) {
        boolean isOk = getManager().updateBriefView(ask);
        owner.answer(UpdateViewAns.newBuilder().setIsOk(isOk).build());
    }

    @Override
    public void handleClearViewAsk(ClearViewAsk ask) {
        getManager().clearPlayerView(ask.getPlayerId());
        owner.answer(ClearViewAns.getDefaultInstance());
    }

    @Override
    public void handleAddSceneObjCmd(AddSceneObjCmd ask) {
        getManager().addIntoAoi(ask);
    }

    @Override
    public void handleChangeSceneObjCmd(ChangeSceneObjCmd ask) {
        getManager().sceneObjOnChange(ask.getObjId(), ask.getLayer(), ask.getNewLayer());
    }
}

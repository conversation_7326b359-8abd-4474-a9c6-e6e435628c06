package com.yorha.common.utils.shape;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.vector.Vector2i;

/**
 * <AUTHOR>
 */
public class Triangle implements Shape {

    private final Point pointA;
    private final Point pointB;
    private final Point pointC;

    private Triangle(Point pointA, Point pointB, Point pointC) {
        this.pointA = pointA;
        this.pointB = pointB;
        this.pointC = pointC;
    }

    public static Triangle valueOf(Point pointA, Point pointB, Point pointC) {
        return new Triangle(pointA, pointB, pointC);
    }

    @Override
    public boolean containsPoint(long x, long y) {
        Point p = Point.valueOf(x, y);
        Vector2i pA = Vector2i.valueOfPoint(pointA, p);
        Vector2i pB = Vector2i.valueOfPoint(pointB, p);
        Vector2i pC = Vector2i.valueOfPoint(pointA, p);
        double t1 = Vector2i.crossProduct(pA, pB);
        double t2 = Vector2i.crossProduct(pB, pC);
        double t3 = Vector2i.crossProduct(pC, pA);
        return t1 * t2 >= 0 && t1 * t3 >= 0;
    }

    /**
     * 获取三角形内随机点
     *
     * @return 点
     */
    @Override
    public Point getRandomPoint() {
        double p1 = Math.sqrt(RandomUtils.nextDouble());
        double p2 = RandomUtils.nextDouble();
        double a = 1 - p1;
        double b = (1 - p2) * p1;
        double c = p2 * p1;
        double x = pointA.getX() * a + pointB.getX() * b + pointC.getX() * c;
        double y = pointA.getY() * a + pointB.getY() * b + pointC.getY() * c;
        return Point.valueOf((int) x, (int) y);
    }

    private AABB aabb = null;

    @Override
    public AABB getAABB() {
        if (aabb == null) {
            int left = Math.min(pointA.getX(), pointB.getX());
            left = Math.min(pointC.getX(), left);

            int right = Math.max(pointA.getX(), pointB.getX());
            right = Math.max(pointC.getX(), right);

            int bottom = Math.max(pointA.getY(), pointB.getY());
            bottom = Math.max(pointC.getY(), bottom);

            int top = Math.min(pointA.getY(), pointB.getY());
            top = Math.min(pointC.getY(), top);

            aabb = new AABB(left, top, right, bottom);
        }
        return aabb;
    }

    @Override
    public String toString() {
        return "Triangle{" + pointA.toString() + pointB.toString() + pointC.toString() + "}";
    }
}

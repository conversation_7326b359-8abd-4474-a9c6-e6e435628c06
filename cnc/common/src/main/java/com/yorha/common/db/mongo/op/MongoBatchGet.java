package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.Projections;
import com.mongodb.reactivestreams.client.FindPublisher;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.MultiGetIBasicSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.mongo.utils.MongoUtils;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.result.BatchGetResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import org.bson.Document;
import org.reactivestreams.Publisher;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MongoBatchGet<T extends Message.Builder> extends MongoOperation<Collection<T>, BatchGetOption, Document, List<Document>, BatchGetResult<T>> {
    public MongoBatchGet(MongoDatabase database, Collection<T> t, BatchGetOption batchGetOption) {
        super(database, PbFieldMetaCaches.getMetaData(t.iterator().next()), t, batchGetOption);
    }

    @Override
    protected MultiGetIBasicSubscriber getSubscriber() {
        return new MultiGetIBasicSubscriber();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        var req = DocumentHelper.formBatchGet((Collection<Message.Builder>) this.getReq());
        FindPublisher<Document> result = this.database.getCollection(this.getTableName()).find(req).batchSize(MongoUtils.MAX_RECORD_NUM_PER_GET);
        if (this.getOption().isGetAllFields()) {
            return result.projection(Projections.excludeId());
        }
        return result.projection(this.buildProjection(this.getOption().getFieldNames()));
    }

    @Override
    protected BatchGetResult<T> buildResult(List<Document> documents) {
        BatchGetResult<T> result = new BatchGetResult<>();
        result.requestId = this.getRequestId();
        result.values = new ArrayList<>(documents.size());
        result.errorKeys = new ArrayList<>(this.getReq());
        if (documents.isEmpty()) {
            result.code = TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
            return result;
        }

        for (final Document document : documents) {
            T proto = MongoOperation.buildDefaultValue(this.getReq().iterator().next());
            this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
            ValueWithVersion<T> valueWithVersion = new ValueWithVersion<>();
            valueWithVersion.value = proto;
            result.values.add(valueWithVersion);
            //TODO(JOSEFREN): 粗糙实现，后面找更优解
            result.errorKeys.removeIf((element) -> PbHelper.keyEqual(this.getFieldMetaData(), proto, element));
        }
        return result;
    }

    @Override
    protected BatchGetResult<T> onMongoError() {
        BatchGetResult<T> result = new BatchGetResult<>();
        result.requestId = this.getRequestId();
        result.values = Collections.emptyList();
        result.errorKeys = new ArrayList<>(this.getReq());
        result.code = DEFAULT_ERROR_CODE;
        return result;
    }
}
package com.yorha.common.constant;

import com.google.common.collect.ImmutableMap;
import com.yorha.common.utils.Pair;
import com.yorha.proto.CommonEnum.*;
import org.apache.commons.lang3.tuple.Triple;

import java.util.Map;

/**
 * 加成常量
 *
 * <AUTHOR>
 */
public interface AdditionConstants {
    /**
     * 联盟资源加成的buff枚举对应资源
     */
    Map<Integer, ClanResourceType> CLAN_RESOURCES_EFFECT = ImmutableMap.of(
            BuffEffectType.ET_CLAN_OIL_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_OIL,
            BuffEffectType.ET_CLAN_STEEL_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_STEEL,
            BuffEffectType.ET_CLAN_RARE_EARTH_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_RARE_EARTH,
            BuffEffectType.ET_CLAN_TITANIUM_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_TIBERIUM,
            BuffEffectType.ET_CLAN_CRYSTAL_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_CRYSTAL
    );

    /**
     * 玩家资源加成的buff枚举对应资源
     */
    Map<Integer, ClanResourceType> PLAYER_RESOURCES_EFFECT = ImmutableMap.of(
            BuffEffectType.ET_PLAYER_OIL_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_OIL,
            BuffEffectType.ET_PLAYER_STEEL_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_STEEL,
            BuffEffectType.ET_PLAYER_RARE_EARTH_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_RARE_EARTH,
            BuffEffectType.ET_PLAYER_TITANIUM_PRODUCE_BUFF_VALUE, ClanResourceType.CNRT_TIBERIUM
    );

    /**
     * 资源对应采集速度加成
     */
    Map<CurrencyType, BuffEffectType> RESOURCE_COLLECT_INT_MAP = ImmutableMap.of(
            CurrencyType.OIL, BuffEffectType.ET_COLLECT_OIL_SPEED_UP,
            CurrencyType.STEEL, BuffEffectType.ET_COLLECT_STEEL_SPEED_UP,
            CurrencyType.RARE_EARTH, BuffEffectType.ET_COLLECT_RARE_SPEED_UP,
            CurrencyType.TIBERIUM, BuffEffectType.ET_COLLECT_TITANIUM_SPEED_UP,
            CurrencyType.DIAMOND, BuffEffectType.ET_COLLECT_DIAMOND_SPEED_UP
    );

    Map<CurrencyType, BuffEffectType> RESOURCE_COLLECT_CLAN_INT_MAP = ImmutableMap.of(
            CurrencyType.OIL, BuffEffectType.ET_COLLECT_CLAN_OIL_SPEED_UP,
            CurrencyType.STEEL, BuffEffectType.ET_COLLECT_CLAN_STEEL_SPEED_UP,
            CurrencyType.RARE_EARTH, BuffEffectType.ET_COLLECT_CLAN_RARE_SPEED_UP,
            CurrencyType.TIBERIUM, BuffEffectType.ET_COLLECT_CLAN_TITANIUM_SPEED_UP,
            CurrencyType.DIAMOND, BuffEffectType.ET_COLLECT_CLAN_DIAMOND_SPEED_UP
    );

    /**
     * 兵种类型对应的移速增加万分比/固定值
     */
    Map<SoldierType, Pair<BuffEffectType, BuffEffectType>> SOLDIER_TYPE_SPEED_INC_MAP = ImmutableMap.of(
            SoldierType.ST_Foot, Pair.of(BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE1_PERCENT, BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE1_FIXED),
            SoldierType.ST_Tank, Pair.of(BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE2_PERCENT, BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE2_FIXED),
            SoldierType.ST_Artillery, Pair.of(BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE3_PERCENT, BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE3_FIXED),
            SoldierType.ST_ArmoredCar, Pair.of(BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE4_PERCENT, BuffEffectType.ET_ARMY_MOVE_SPEED_ADD_TYPE4_FIXED)
    );

    /**
     * 兵种类型对应的攻防血增加固定值
     */
    Map<SoldierType, Triple<BuffEffectType, BuffEffectType, BuffEffectType>> SOLDIER_TYPE_INC_FIXED_MAP = ImmutableMap.of(
            SoldierType.ST_Foot, Triple.of(BuffEffectType.ET_INFANTRY_ATK_INC_FIXED, BuffEffectType.ET_INFANTRY_DEF_INC_FIXED, BuffEffectType.ET_INFANTRY_HP_INC_FIXED),
            SoldierType.ST_Tank, Triple.of(BuffEffectType.ET_TANK_ATK_INC_FIXED, BuffEffectType.ET_TANK_DEF_INC_FIXED, BuffEffectType.ET_TANK_HP_INC_FIXED),
            SoldierType.ST_Artillery, Triple.of(BuffEffectType.ET_ARTILLERY_ATK_INC_FIXED, BuffEffectType.ET_ARTILLERY_DEF_INC_FIXED, BuffEffectType.ET_ARTILLERY_HP_INC_FIXED),
            SoldierType.ST_ArmoredCar, Triple.of(BuffEffectType.ET_ARMORED_ATK_INC_FIXED, BuffEffectType.ET_ARMORED_DEF_INC_FIXED, BuffEffectType.ET_ARMORED_HP_INC_FIXED)
    );

    /**
     * 兵种类型对应的固定训练上限
     */
    Map<SoldierType, BuffEffectType> SOLDIER_TYPE_TRAIN_EFFECT_LIMIT_TYPE_MAP = ImmutableMap.of(
            SoldierType.ST_Foot, BuffEffectType.ET_INFANTRY_TRAIN_NUM_LIMIT,
            SoldierType.ST_Tank, BuffEffectType.ET_TANK_TRAIN_NUM_LIMIT,
            SoldierType.ST_Artillery, BuffEffectType.ET_ARTILLERY_TRAIN_NUM_LIMIT,
            SoldierType.ST_ArmoredCar, BuffEffectType.ET_ARMORED_TRAIN_NUM_LIMIT
    );

    /**
     * 兵种类型对应的负重增加百分比
     */
    Map<SoldierType, BuffEffectType> SOLDIER_TYPE_BURDEN_EFFECT_PER_TYPE_MAP = ImmutableMap.of(
            SoldierType.ST_Foot, BuffEffectType.ET_INFANTRY_BURDEN_PERCENT,
            SoldierType.ST_Tank, BuffEffectType.ET_TANK_BURDEN_PERCENT,
            SoldierType.ST_Artillery, BuffEffectType.ET_ARTILLERY_BURDEN_PERCENT,
            SoldierType.ST_ArmoredCar, BuffEffectType.ET_ARMORED_BURDEN_PERCENT
    );

    /**
     * 兵种类型对应的负重增加绝对值
     */
    Map<SoldierType, BuffEffectType> SOLDIER_TYPE_BURDEN_EFFECT_TYPE_MAP = ImmutableMap.of(
            SoldierType.ST_Foot, BuffEffectType.ET_INFANTRY_BURDEN,
            SoldierType.ST_Tank, BuffEffectType.ET_TANK_BURDEN,
            SoldierType.ST_Artillery, BuffEffectType.ET_ARTILLERY_BURDEN,
            SoldierType.ST_ArmoredCar, BuffEffectType.ET_ARMORED_BURDEN
    );

    /**
     * 内城建筑类型对应的任务队列类型
     */
    Map<InnerCityBuildType, QueueTaskType> INNER_CITY_BUILD_TYPE_QUEUE_TASK_TYPE_MAP = ImmutableMap.of(
            InnerCityBuildType.BARRACKS, QueueTaskType.SOLDIER_FOOT_TRAINING,
            InnerCityBuildType.TANK_FACTORY, QueueTaskType.SOLDIER_TANK_TRAINING,
            InnerCityBuildType.ARTILLERY_FACTORY, QueueTaskType.SOLDIER_ARTILLERY_TRAINING,
            InnerCityBuildType.VEHICLE_FACTORY, QueueTaskType.SOLDIER_ARMORED_CAR_TRAINING,
            InnerCityBuildType.SERVICE_STATION, QueueTaskType.MEDICAL_TREATMENT,
            InnerCityBuildType.TECHNOLOGY_CENTER, QueueTaskType.RESEARCH
    );

    /**
     * 需要检测的建筑类型:步兵营、火炮营、坦克营、装甲车营、科技中心、医院
     */
    static QueueTaskType getQueueTaskTypeByInnerCityBuildType(InnerCityBuildType innerCityBuildType) {
        return INNER_CITY_BUILD_TYPE_QUEUE_TASK_TYPE_MAP.get(innerCityBuildType);
    }

    /**
     * 兵种克制类型
     * ET_INFANTRY_2_TANK_DMG_PERCENT = 46 [(addNeedSync) = true]; // 步兵单位对坦克单位的伤害（万分比）
     * ET_TANK_2_ARTILLERY_DMG_PERCENT = 47 [(addNeedSync) = true]; // 坦克单位对火炮单位的伤害（万分比）
     * ET_ARTILLERY_2_INFANTRY_DMG_PERCENT = 48 [(addNeedSync) = true]; // 火炮单位对步兵单位的伤害（万分比）
     */
    Map<Pair<SoldierType, SoldierType>, BuffEffectType> SOLDIER_RESTRAIN_MAP = ImmutableMap.of(
            Pair.of(SoldierType.ST_Foot, SoldierType.ST_Tank), BuffEffectType.ET_INFANTRY_2_TANK_DMG_PERCENT,
            Pair.of(SoldierType.ST_Tank, SoldierType.ST_Artillery), BuffEffectType.ET_TANK_2_ARTILLERY_DMG_PERCENT,
            Pair.of(SoldierType.ST_Artillery, SoldierType.ST_Foot), BuffEffectType.ET_ARTILLERY_2_INFANTRY_DMG_PERCENT
    );

    /**
     * 兵种被克制类型
     * ET_TANK_4_INFANTRY_DMG_INC_PERCENT = 49 [(addNeedSync) = true]; // 坦克单位受到步兵伤害增加（万分比）
     * ET_ARTILLERY_4_TANK_DMG_INC_PERCENT = 50 [(addNeedSync) = true]; // 火炮单位受到坦克伤害增加（万分比）
     * ET_INFANTRY_4_ARTILLERY_DMG_INC_PERCENT = 51 [(addNeedSync) = true]; // 步兵单位受到火炮伤害增加（万分比）
     */
    Map<Pair<SoldierType, SoldierType>, BuffEffectType> SOLDIER_BE_RESTRAIN_MAP = ImmutableMap.of(
            Pair.of(SoldierType.ST_Tank, SoldierType.ST_Foot), BuffEffectType.ET_TANK_4_INFANTRY_DMG_INC_PERCENT,
            Pair.of(SoldierType.ST_Artillery, SoldierType.ST_Tank), BuffEffectType.ET_ARTILLERY_4_TANK_DMG_INC_PERCENT,
            Pair.of(SoldierType.ST_Foot, SoldierType.ST_Artillery), BuffEffectType.ET_INFANTRY_4_ARTILLERY_DMG_INC_PERCENT
    );

    Map<MapBuildingType, BuffEffectType> CLAN_BUILDING_BUILD_EFFECT_TYPE_MAP = ImmutableMap.of(
            MapBuildingType.MBT_CLAN_FORTRESS, BuffEffectType.ET_UNLOCK_CLAN_FORTRESS
    );

    /**
     * 建筑类型对应的数量上限加成
     */
    Map<MapBuildingType, BuffEffectType> CLAN_BUILDING_UPPER_LIMIT_EFFECT_MAP = ImmutableMap.of(
            MapBuildingType.MBT_MAIN_BASE, BuffEffectType.ET_BUILD_MAIN_BASE_NUM_FIXED,
            MapBuildingType.MBT_CLAN_FORTRESS, BuffEffectType.ET_BUILD_CLAN_FORTRESS_NUM_FIXED,
            MapBuildingType.MBT_COMMAND_CENTER, BuffEffectType.ET_BUILD_COMMAND_CENTER_NUM_FIXED
    );

    /**
     * 建筑类型对应的建造速度加成
     */
    Map<MapBuildingType, BuffEffectType> CLAN_BUILDING_BUILD_SPEEDUP_EFFECT_TYPE_MAP = ImmutableMap.of(
            MapBuildingType.MBT_MAIN_BASE, BuffEffectType.ET_BUILD_MAIN_BASE_SPEED_PERCENT,
            MapBuildingType.MBT_CLAN_FORTRESS, BuffEffectType.ET_BUILD_CLAN_FORTRESS_SPEED_PERCENT,
            MapBuildingType.MBT_COMMAND_CENTER, BuffEffectType.ET_BUILD_COMMAND_CENTER_SPEED_PERCENT
    );

    /**
     * 建筑类型对应的耐久度加成
     */
    Map<MapBuildingType, BuffEffectType> CLAN_BUILDING_MAX_HP_EFFECT_TYPE_MAP = ImmutableMap.of(
            MapBuildingType.MBT_CLAN_FORTRESS, BuffEffectType.ET_CLAN_FORTRESS_HP_MAX_PERCENT
    );

    /**
     * 建造速度加成的int值对应的建造类型
     */
    Map<Integer, MapBuildingType> BUILDING_SPEED_EFFECT_TYPE_INT_CLAN_BUILDING_TYPE_MAP = ImmutableMap.of(
            BuffEffectType.ET_BUILD_MAIN_BASE_SPEED_PERCENT_VALUE, MapBuildingType.MBT_MAIN_BASE,
            BuffEffectType.ET_BUILD_CLAN_FORTRESS_SPEED_PERCENT_VALUE, MapBuildingType.MBT_CLAN_FORTRESS,
            BuffEffectType.ET_BUILD_COMMAND_CENTER_SPEED_PERCENT_VALUE, MapBuildingType.MBT_COMMAND_CENTER
    );

    /**
     * 耐久度加成的int值对应的建造类型
     */
    Map<Integer, MapBuildingType> MAX_HP_EFFECT_TYPE_INT_CLAN_BUILDING_TYPE_MAP = ImmutableMap.of(
            BuffEffectType.ET_CLAN_FORTRESS_HP_MAX_PERCENT_VALUE, MapBuildingType.MBT_CLAN_FORTRESS
    );
}

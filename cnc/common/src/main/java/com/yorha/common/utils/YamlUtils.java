package com.yorha.common.utils;

import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.error.YAMLException;

import java.util.Map;

/**
 * 操作yaml文件的通用工具类
 * <p>
 * 测试文件：test目录的YamlUtilsTest
 *
 * <AUTHOR>
 */
public class YamlUtils {

    /**
     * 根据content和classz得到yaml对应的数据
     * 注意，clazz必须包含无参构造函数 & 相应的参数必须提供set方法
     *
     * @param content 输入的信息内容
     * @param clazz   反序列化的类
     * @return 反序列化的对象
     * @throws YAMLException content内容不符合规范
     */
    public static <T> T newInstance(String content, Class<T> clazz) throws YAMLException {
        LoaderOptions loaderOptions = new LoaderOptions();
        loaderOptions.setWrappedToRootException(true);
        Yaml yaml = new Yaml(loaderOptions);
        return yaml.loadAs(content, clazz);
    }

    /**
     * 序列化
     */
    public static String dumpToYaml(Object object) {
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setDefaultScalarStyle(DumperOptions.ScalarStyle.PLAIN);
        return new Yaml(options).dump(object);
    }

    /**
     * 根据content得到yaml对应的数据
     *
     * @param content 输入的yaml字符流
     * @return map容器
     * @throws YAMLException content内容不符合规范
     */
    public static Map<String, Object> newInstance(String content) throws YAMLException {
        LoaderOptions loaderOptions = new LoaderOptions();
        loaderOptions.setWrappedToRootException(true);
        Yaml yaml = new Yaml(loaderOptions);
        return yaml.load(content);
    }

    /**
     * 提供一个无脑将任何类型转为string的方法
     */
    public static String getOrDefault(Object obj, String defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        return obj.toString();
    }
}

package com.yorha.common.mapgrid;

import com.google.common.collect.ImmutableMap;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.MapAreaType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapBuildingTemplate;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>  进程共享数据  并发读 不能修改!!!!
 * <p>
 * 大世界地缘数据
 */
public class MapGridData {
    private static final Logger LOGGER = LogManager.getLogger(MapGridData.class);
    /**
     * 格子数据
     */
    private final int[][] grids = new int[BigSceneConstants.GRID_NUM][BigSceneConstants.GRID_NUM];
    /**
     * 片Id -> 格子索引List
     */
    private Map<Integer, List<Integer>> partGridList;
    /**
     * 州Id-> AreaType -> 格子数
     */
    private Map<Integer, Map<MapAreaType, Integer>> regionAreaGridSize;
    /**
     * 关隘围攻位置角度
     */
    private Map<Integer, Map<Integer, List<Integer>>> crossBesiegeAngle;

    private static String getMapDataFilePath(int mapId) {
        return ResHolder.getInstance().getResPath() + "//navmesh//map" + mapId + ".txt";
    }

//    public static void main(String[] args) {
//        File file = new File("F:\\dev65\\game_data\\server\\navmesh\\map1004_out.txt");
//        File out_file = new File("F:\\dev65\\game_data\\server\\navmesh\\map1004.txt");
//        try {
//            BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
//            FileOutputStream fileOutputStream = new FileOutputStream(out_file);
//            String s;
//            while ((s = br.readLine()) != null) {
//                String[] data = s.split(":");
//                String[] pos = data[0].split(",");
//                String[] tags = data[1].split(",");
//                int i = Integer.parseInt(pos[0]);
//                int j = Integer.parseInt(pos[1]);
//                long partId = Integer.parseInt(tags[0]);
//                long regionId = Integer.parseInt(tags[1]);
//                long tag = (partId << BigSceneConstants.LANDFORMS_TYPE_DIGIT_NUM) + (regionId << BigSceneConstants.BEFORE_REGION_DIGIT_NUM);
//                String out = String.format("%d,%d:%d\n", i, j, tag);
//                fileOutputStream.write(out.getBytes(StandardCharsets.UTF_8));
//            }
//            br.close();
//        } catch (Exception e) {
//
//        }
//    }

//    public static void main(String[] args) {
//        File file = new File("F:\\dev65\\game_data\\server\\navmesh\\big_map.txt");
//        File out_file = new File("F:\\dev65\\game_data\\server\\navmesh\\map1004_out.txt");
//        try {
//            BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
//            FileOutputStream fileOutputStream = new FileOutputStream(out_file);
//            String s;
//            while ((s = br.readLine()) != null) {
//                String[] data = s.split(":");
//                String[] pos = data[0].split(",");
//                int i = Integer.parseInt(pos[0]);
//                int j = Integer.parseInt(pos[1]);
//                int tag = Integer.parseInt(data[1]);
//
//                int partId = getPartTag(tag);
//                int regionId = getRegionTag(tag);
//                String out = String.format("%d,%d:%d,%d\n", i, j, partId, regionId);
//                fileOutputStream.write(out.getBytes(StandardCharsets.UTF_8));
//            }
//            br.close();
//        } catch (Exception e) {
//
//        }
//    }

    public void load(int mapId) throws ResourceException {
        File file = new File(getMapDataFilePath(mapId));
        int count = 0;
        long startTs = SystemClock.now();
        partGridList = new HashMap<>();
        regionAreaGridSize = new HashMap<>();
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
            String s;
            while ((s = br.readLine()) != null) {
                String[] data = s.split(":");
                String[] pos = data[0].split(",");
                int i = Integer.parseInt(pos[0]);
                int j = Integer.parseInt(pos[1]);
                int tag = Integer.parseInt(data[1]);
                if (grids[i][j] != 0) {
                    // 重复格子
                    throw new ResourceException(StringUtils.format("地缘地图格子(x:{}, y:{}) 已存在 (x:{} y:{} tag:{})", i, j, i, j, grids[i][j]));
                }
                grids[i][j] = tag;
                int gridId = (i << BigSceneConstants.MAP_GRID_Y_DIGIT_NUM) + j;
                int partId = getPartTag(tag);
                partGridList.computeIfAbsent(partId, k -> new ArrayList<>()).add(gridId);
                count++;
            }
            br.close();
        } catch (Exception e) {
            throw new ResourceException("MapGridData load fail, message:{}", e.getMessage(), e);
        }
        if (count != BigSceneConstants.GRID_NUM * BigSceneConstants.GRID_NUM) {
            throw new ResourceException(StringUtils.format("MapGridData load  count: {} failed", count));
        }
        LOGGER.debug("BigWorldMapGrid load end. count: {}  cost: {}ms", count, SystemClock.now() - startTs);
        // 检测地图格子的片id是否都有配置
        MapTemplateDataItem mapTemplateDataItem = ResHolder.getResService(MapSubdivisionDataService.class).getMapTemplateDataItem(mapId);
        for (Integer partId : partGridList.keySet()) {
            if (mapTemplateDataItem.findValueFromMap(RegionalAreaSettingTemplate.class, partId) == null) {
                throw new ResourceException(StringUtils.format("MapGridData part:{} not has template", partId));
            }
        }
        // 设置为不可变数据
        partGridList = ImmutableMap.copyOf(partGridList);
        // 检查地缘区域表 并 构建关隘围攻角度数据
        checkAndLoadAreaSettingTemplate(mapTemplateDataItem);
        LOGGER.debug("BigWorldMapGrid calculate regionAreaGridSize end. {}", regionAreaGridSize);
    }

    private void checkAndLoadAreaSettingTemplate(MapTemplateDataItem mapTemplateDataItem) throws ResourceException {
        List<String> errorPart = new ArrayList<>();
        Map<Integer, MapBuildingTemplate> map = ResHolder.getInstance().getMap(MapBuildingTemplate.class);
        crossBesiegeAngle = new HashMap<>();
        for (RegionalAreaSettingTemplate template : mapTemplateDataItem.getMap(RegionalAreaSettingTemplate.class).values()) {
            // 检测表里的每一个片是否都有格子
            if (CollectionUtils.isEmpty(getGridList(template.getId()))) {
                throw new ResourceException(StringUtils.format("MapGridData part:{} has not grid", template.getId()));
            }
            // 统计每个州每种类型的格子数目
            Map<MapAreaType, Integer> areaTypeNumMap = regionAreaGridSize.computeIfAbsent(template.getRegionId(), k -> new HashMap<>());
            areaTypeNumMap.put(template.getAreaType(), areaTypeNumMap.getOrDefault(template.getAreaType(), 0) + getGridList(template.getId()).size());
            if (template.getAreaType() != MapAreaType.CROSSING) {
                continue;
            }
            // 构建围攻角度数据
            Map<Integer, List<Integer>> integerListMap = crossBesiegeAngle.computeIfAbsent(template.getId(), k -> new HashMap<>());
            integerListMap.put(template.getLinkRegionList().get(0), new ArrayList<>());
            integerListMap.put(template.getLinkRegionList().get(1), new ArrayList<>());
            List<Integer> besiegeAngleList = map.get(template.getBuildingId()).getBesiegeAngleList();
            Point selfPoint = Point.valueOf(template.getPosX(), template.getPosY());
            int angle = besiegeAngleList.get(0) + template.getRotation() / 1000;
            Point point = selfPoint.getPointWithAngleAndDis(Math.toRadians(angle), 2000);
            int regionId = getRegionTag(getGridTag(point.getX(), point.getY()));
            if (!template.getLinkRegionList().contains(regionId)) {
                throw new ResourceException(StringUtils.format("{} 关隘围攻角度计算坐标错误  配置联通州id: {} {}  实际计算出围攻角度州id {}",
                        template.getId(), template.getLinkRegionList().get(0), template.getLinkRegionList().get(1), regionId));
            }
            int size = besiegeAngleList.size() / 2;
            for (int i = 0; i < size; i++) {
                integerListMap.get(regionId).add(besiegeAngleList.get(i));
            }
            int otherRegionId = template.getLinkRegionList().get(0) == regionId ? template.getLinkRegionList().get(1) : template.getLinkRegionList().get(0);
            for (int i = size; i < besiegeAngleList.size(); i++) {
                integerListMap.get(otherRegionId).add(besiegeAngleList.get(i));
            }
            // check 联通州和城门线
            int targetRegion = getRegionTag(getGridTag(template.getLink1List().get(0), template.getLink1List().get(1)));
            if (targetRegion != template.getLinkRegionList().get(0)) {
                String content = template.getId() +
                        " " +
                        "配置联通州id :" +
                        template.getLinkRegionList().get(0) +
                        "实际城门线州id: " +
                        targetRegion;
                errorPart.add(content);
            }
        }
        if (!errorPart.isEmpty()) {
            WechatLog.error("地缘地缘配置表 城门线顺序错误\n{}", String.join("\n", errorPart));
        }
    }

    public Map<Integer, Map<Integer, List<Integer>>> getCrossBesiegeAngle() {
        return Collections.unmodifiableMap(crossBesiegeAngle);
    }

    /**
     * 获取某州某类型的可出生的格子数目
     */
    public int getRegionBornAreaNum(int regionId, MapAreaType areaType) {
        if (!regionAreaGridSize.containsKey(regionId)) {
            return 0;
        }
        return regionAreaGridSize.get(regionId).getOrDefault(areaType, 0);
    }

    /**
     * 获取格子tag
     */
    public int getGridTag(int gridId) {
        int i = (gridId & BigSceneConstants.MAP_GRID_X) >> BigSceneConstants.MAP_GRID_Y_DIGIT_NUM;
        int j = gridId & BigSceneConstants.MAP_GRID_Y;
        if (i >= BigSceneConstants.GRID_NUM || j >= BigSceneConstants.GRID_NUM) {
            LOGGER.warn("point outside i:{} j:{}", i, j);
            throw new GeminiException(ErrorCode.MAP_MOVE_CITY_INVAILD_POINT, "point outside " + i + " " + j);
        }
        return grids[i][j];
    }

    public int getGridTag(int x, int y) {
        int i = x / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        int j = y / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        if (i >= BigSceneConstants.GRID_NUM || j >= BigSceneConstants.GRID_NUM || i < 0 || j < 0) {
            LOGGER.warn("point outside x:{} y:{}", x, y);
            throw new GeminiException(ErrorCode.MAP_MOVE_CITY_INVAILD_POINT, "point outside " + x + " " + y);
        }
        return grids[i][j];
    }

    public int getGridId(int x, int y) {
        int i = x / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        int j = y / BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        if (i >= BigSceneConstants.GRID_NUM || j >= BigSceneConstants.GRID_NUM) {
            LOGGER.warn("point outside x:{} y:{}", x, y);
            throw new GeminiException(ErrorCode.MAP_MOVE_CITY_INVAILD_POINT, "point outside " + x + " " + y);
        }
        return (i << BigSceneConstants.MAP_GRID_Y_DIGIT_NUM) + j;
    }

    private static int getPartTag(int tag) {
        return (tag & BigSceneConstants.PART_TAG) >> BigSceneConstants.LANDFORMS_TYPE_DIGIT_NUM;
    }

    private static int getRegionTag(int tag) {
        return (tag & BigSceneConstants.REGION_ID_TAG) >> BigSceneConstants.BEFORE_REGION_DIGIT_NUM;
    }

    /**
     * 获取片的格子List
     */
    public List<Integer> getGridList(int partId) {
        return partGridList.get(partId);
    }

}

package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.result.IncreaseResult;

/**
 * <AUTHOR>
 */
public class IncreaseAsk<T extends Message.Builder> implements GameDbReq<IncreaseResult<T>> {
    private final T req;
    private final IncreaseOption option;

    public IncreaseAsk(T req, IncreaseOption option) {
        this.req = req;
        this.option = option;
    }

    public IncreaseOption getOption() {
        return option;
    }

    public T getReq() {
        return req;
    }
}

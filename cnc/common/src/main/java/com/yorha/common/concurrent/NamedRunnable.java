package com.yorha.common.concurrent;

/**
 * 带名字的runnable，便于任务统计
 *
 * <AUTHOR>
 */
public class NamedRunnable implements Runnable, Named {

    protected final String name;
    protected final Runnable runnable;

    public NamedRunnable(String name, Runnable runnable) {
        this.name = name;
        this.runnable = runnable;
    }

    @Override
    public void run() {
        if (runnable != null) {
            runnable.run();
        }
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "NamedRunnable{" + name + '}';
    }
}

package com.yorha.common.enums.qlog.mail;

import com.yorha.proto.CommonEnum;

/**
 * 邮件页签enum
 *
 * <AUTHOR>
 */

public enum MailTabType {
    /**
     * 个人邮件
     */
    PERSONAL_MAIL("personal_mail", CommonEnum.MailTabsType.MAIL_TABS_TYPE_PERSONAL),
    /**
     * 联盟邮件
     */
    CLAN_MAIL("guild_mail", CommonEnum.MailTabsType.MAIL_TABS_TYPE_CLAN),
    /**
     * 战报邮件
     */
    BATTLE_REPORT_MAIL("broadcast_mail", CommonEnum.MailTabsType.MAIL_TABS_TYPE_REPORT),
    /**
     * 系统邮件
     */
    SYSTEM_MAIL("system_mail", CommonEnum.MailTabsType.MAIL_TABS_TYPE_SYSTEM),
    /**
     * 已发送邮件
     */
    SEND_MAIL("mail_write_by_myself", CommonEnum.MailTabsType.MAIL_TABS_TYPE_SEND);

    private String type;
    private CommonEnum.MailTabsType tabType;

    MailTabType(String type, CommonEnum.MailTabsType tabType) {
        this.type = type;
        this.tabType = tabType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public CommonEnum.MailTabsType getTabType() {
        return tabType;
    }

    public void setTabType(CommonEnum.MailTabsType tabType) {
        this.tabType = tabType;
    }

    public static String getTypeByMailTabsType(CommonEnum.MailTabsType tabType) {
        for (MailTabType type : values()) {
            if (type.getTabType() == tabType) {
                return type.getType();
            }
        }
        return "";
    }
}

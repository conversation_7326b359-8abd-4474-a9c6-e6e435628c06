package com.yorha.common.resource.resservice.player;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.item.ItemReward;
import com.yorha.common.resource.resservice.item.ItemRewardBox;
import com.yorha.common.resource.resservice.item.ItemRewardCondition;
import com.yorha.common.resource.resservice.item.ItemRewardPool;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import res.template.ItemTemplate;
import res.template.RecruitRewardBoxTemplate;
import res.template.RecruitRewardPoolTemplate;
import res.template.RecruitTemplate;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 招募配置
 *
 * <AUTHOR>
 */
public class RecruitService extends AbstractResService {
    public RecruitService(ResHolder resHolder) {
        super(resHolder);
    }

    private final Map<Integer, List<RecruitRewardBoxTemplate>> rewardBoxTemplateMap = new HashMap<>();
    private final Map<Integer, List<RecruitRewardPoolTemplate>> rewardPoolTemplateMap = new HashMap<>();
    private final Map<CommonEnum.RecruitPoolType, ItemRewardPool> recruitPool = new HashMap<>();
    private final Map<CommonEnum.RecruitPoolType, RecruitTemplate> recruitConfigMap = new HashMap<>();
    private final Map<CommonEnum.RecruitPoolType, Map<Integer, ItemRewardPool>> recruitGuaranteePool = new HashMap<>();

    @Override
    public void load() throws ResourceException {
        loadRecruitBox();
        loadRecruitPool();
        for (RecruitTemplate recruitTemplate : getResHolder().getListFromMap(RecruitTemplate.class)) {
            refreshRecruitPool(recruitTemplate);
        }
    }

    private void refreshRecruitPool(RecruitTemplate recruitTemplate) throws ResourceException {
        if (!rewardPoolTemplateMap.containsKey(recruitTemplate.getPoolId())) {
            throw new ResourceException(StringUtils.format("招募表:recruit RecruitPoolType:{} unsuport pool {}",
                    recruitTemplate.getRecruitPoolType(), recruitTemplate.getPoolId()));
        }
        for (IntPairType pairType : recruitTemplate.getRecruitGuaranteePairList()) {
            if (!rewardPoolTemplateMap.containsKey(pairType.getValue())) {
                throw new ResourceException(StringUtils.format("招募表:recruit RecruitPoolType:{} 保底奖池 unsuport pool {}",
                        recruitTemplate.getRecruitPoolType(), pairType.getValue()));
            }
        }
        final ItemRewardPool itemRewardPool = new ItemRewardPool(recruitTemplate.getRandomNum());
        // 必出项
        if (!recruitTemplate.getFirstRecruitList().isEmpty()) {
            for (int configId : recruitTemplate.getFirstRecruitList()) {
                RecruitRewardBoxTemplate template = getResHolder().getMap(RecruitRewardBoxTemplate.class).get(configId);
                var itemReward = ItemReward.newBuilder()
                        .setConfigId(template.getId())
                        .setItemTemplate(template.getItemId())
                        .setCount(template.getNum())
                        .build();
                itemRewardPool.addMustOutReward(itemReward);
            }
        }
        // 构建抽奖池对象
        buildItemRewardPool(itemRewardPool, recruitTemplate.getPoolId());
        recruitConfigMap.put(recruitTemplate.getRecruitPoolType(), recruitTemplate);
        recruitPool.put(recruitTemplate.getRecruitPoolType(), itemRewardPool);
        // 保底奖池
        for (IntPairType pairType : recruitTemplate.getRecruitGuaranteePairList()) {
            //构建保底奖池对象
            final ItemRewardPool guaranteeItemRewardPool = new ItemRewardPool(1);
            buildItemRewardPool(guaranteeItemRewardPool, pairType.getValue());
            recruitGuaranteePool.computeIfAbsent(recruitTemplate.getRecruitPoolType(), key -> new HashMap<>())
                    .put(pairType.getKey(), guaranteeItemRewardPool);
        }
    }

    private void buildItemRewardPool(ItemRewardPool itemRewardPool, int poolId) {
        for (RecruitRewardPoolTemplate poolTemplate : rewardPoolTemplateMap.get(poolId)) {
            ItemRewardBox box = new ItemRewardBox(poolTemplate.getWeight());
            for (RecruitRewardBoxTemplate boxTemplate : rewardBoxTemplateMap.get(poolTemplate.getBoxId())) {
                ItemReward.Builder builder = ItemReward.newBuilder();
                if (boxTemplate.getCondition() != null) {
                    builder.setCondition(new ItemRewardCondition(boxTemplate.getCondition(), boxTemplate.getParam()));
                }
                builder.setConfigId(boxTemplate.getId()).setItemTemplate(boxTemplate.getItemId()).setCount(boxTemplate.getNum()).setWeight(boxTemplate.getWeight());
                box.addReward(builder.build());
            }
            itemRewardPool.addRewardBox(box);
        }
    }

    /**
     * 获取抽奖池（白银宝箱，黄金宝箱）
     */
    public ItemRewardPool getRecruitPool(CommonEnum.RecruitPoolType recruitPoolType) {
        return recruitPool.get(recruitPoolType);
    }

    /**
     * 获取保底抽奖池
     */
    @Nullable
    public ItemRewardPool getGuaranteePool(CommonEnum.RecruitPoolType recruitPoolType, int times) {
        final Map<Integer, ItemRewardPool> times2Pool = recruitGuaranteePool.get(recruitPoolType);
        if (times2Pool == null) {
            return null;
        }
        return times2Pool.get(times);
    }

    /**
     * 加载奖励箱
     */
    public void loadRecruitBox() {
        Map<Integer, RecruitRewardBoxTemplate> recruitRewardBoxTemplateMap = getResHolder().getMap(RecruitRewardBoxTemplate.class);
        for (Map.Entry<Integer, RecruitRewardBoxTemplate> entry : recruitRewardBoxTemplateMap.entrySet()) {
            List<RecruitRewardBoxTemplate> list = rewardBoxTemplateMap.getOrDefault(entry.getValue().getBoxId(), null);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(entry.getValue());
            rewardBoxTemplateMap.put(entry.getValue().getBoxId(), list);
        }
    }

    /**
     * 加载奖池
     */
    public void loadRecruitPool() {
        Map<Integer, RecruitRewardPoolTemplate> recruitRewardPoolTemplateMap = getResHolder().getMap(RecruitRewardPoolTemplate.class);
        for (Map.Entry<Integer, RecruitRewardPoolTemplate> entry : recruitRewardPoolTemplateMap.entrySet()) {
            List<RecruitRewardPoolTemplate> list = rewardPoolTemplateMap.getOrDefault(entry.getValue().getPoolId(), null);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(entry.getValue());
            rewardPoolTemplateMap.put(entry.getValue().getPoolId(), list);
        }
    }

    public Struct.Item getCostItem(CommonEnum.RecruitPoolType recruitPoolType) {
        RecruitTemplate template = recruitConfigMap.get(recruitPoolType);
        return Struct.Item.newBuilder().setTemplateId(template.getCostItemId()).setNum(template.getCostItemNum()).build();
    }

    public int getDailyLimit(CommonEnum.RecruitPoolType recruitPoolType) {
        return recruitConfigMap.get(recruitPoolType).getDailyRecruitLimit();
    }

    public int getFreeDailyLimit(CommonEnum.RecruitPoolType recruitPoolType) {
        return recruitConfigMap.get(recruitPoolType).getDailyFreeRecruit();
    }

    public int getFreeRefreshCdS(CommonEnum.RecruitPoolType recruitPoolType) {
        return recruitConfigMap.get(recruitPoolType).getFreeRefreshCd();
    }

    @Override
    public void checkValid() throws ResourceException {
        Map<Integer, RecruitTemplate> recruitTemplateMap = getResHolder().getMap(RecruitTemplate.class);
        for (Map.Entry<Integer, RecruitTemplate> entry : recruitTemplateMap.entrySet()) {
            if (entry.getValue().getFirstRecruitList().size() > entry.getValue().getRandomNum()) {
                throw new ResourceException(StringUtils.format("招募表:recruit id:{} 必出项超过一次抽取上限 {}",
                        entry.getKey(), entry.getValue().getFirstRecruitList().size()));
            }
        }
        Map<Integer, RecruitRewardPoolTemplate> recruitRewardPoolTemplateMap = getResHolder().getMap(RecruitRewardPoolTemplate.class);
        for (Map.Entry<Integer, RecruitRewardPoolTemplate> entry : recruitRewardPoolTemplateMap.entrySet()) {
            if (entry.getValue().getPoolId() <= 0) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_pool id:{} unsuport pool {}",
                        entry.getKey(), entry.getValue().getPoolId()));
            }
            if (!rewardBoxTemplateMap.containsKey(entry.getValue().getBoxId())) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_pool id:{} unsuport box {}",
                        entry.getKey(), entry.getValue().getBoxId()));
            }
            if (entry.getValue().getWeight() <= 0) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_pool id:{} unsuport weight {}",
                        entry.getKey(), entry.getValue().getWeight()));
            }
        }

        Map<Integer, RecruitRewardBoxTemplate> recruitRewardBoxTemplateMap = getResHolder().getMap(RecruitRewardBoxTemplate.class);
        Map<Integer, ItemTemplate> itemTemplateMap = getResHolder().getMap(ItemTemplate.class);
        for (Map.Entry<Integer, RecruitRewardBoxTemplate> entry : recruitRewardBoxTemplateMap.entrySet()) {
            if (entry.getValue().getBoxId() <= 0) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_box id:{} unsuport box {}",
                        entry.getKey(), entry.getValue().getBoxId()));
            }
            int itemId = entry.getValue().getItemId();
            if (!itemTemplateMap.containsKey(itemId)) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_box id:{} unsuport item {}",
                        entry.getKey(), itemId));
            }

            if (entry.getValue().getNum() <= 0) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_box id:{} unsuport num {}",
                        entry.getKey(), entry.getValue().getNum()));
            }

            if (entry.getValue().getWeight() <= 0) {
                throw new ResourceException(StringUtils.format("招募表:recruit_reward_box id:{} unsuport weight {}",
                        entry.getKey(), entry.getValue().getWeight()));
            }
        }
        for (List<RecruitRewardBoxTemplate> box : rewardBoxTemplateMap.values()) {
            if (box.size() == 1 && box.getFirst().getWeight() < ItemRewardBox.WEIGHT_BASE) {
                throw new ResourceException("招募表:recruit_reward_box id:{} 所属boxId只有单个奖励但权重不是100000，这不对哦",
                        box.getFirst().getId());
            }
        }
    }
}

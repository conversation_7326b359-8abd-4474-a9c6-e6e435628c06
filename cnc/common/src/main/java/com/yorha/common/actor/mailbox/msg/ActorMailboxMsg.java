package com.yorha.common.actor.mailbox.msg;

import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.utils.time.SystemClock;

import javax.annotation.concurrent.ThreadSafe;

/**
 * ActorMailbox接收的消息。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class ActorMailboxMsg {
    public final ActorMsgEnvelope msg;
    public final String name;
    public final long offerTsNs;

    public ActorMailboxMsg(ActorMsgEnvelope msg, String name) {
        this.msg = msg;
        this.name = name;
        this.offerTsNs = SystemClock.nanoTimeNative();
    }

    public ActorMsgEnvelope getMsg() {
        return this.msg;
    }

    public String getName() {
        return this.name;
    }

    public long getOfferTsNs() {
        return this.offerTsNs;
    }

    @Override
    public String toString() {
        return this.msg.toString();
    }
}

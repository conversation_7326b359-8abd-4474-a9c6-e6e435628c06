package com.yorha.common.resource.resservice.city;

import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import res.template.CityAscendTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class AscendService extends AbstractResService {
    public Map<Integer, Long> ascendTime = new HashMap<>();

    public AscendService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        Collection<CityAscendTemplate> configs = getResHolder().getListFromMap(CityAscendTemplate.class);
        for (CityAscendTemplate config : configs) {
            ascendTime.put(config.getId(), TimeUnit.HOURS.toMillis(config.getHours()));
        }
    }

    public long getLevel2AscendTime(int level) {
        return ascendTime.getOrDefault(level, GameLogicConstants.CITY_ASCEND_TIME);
    }

    @Override
    public void checkValid() throws ResourceException {

    }
}


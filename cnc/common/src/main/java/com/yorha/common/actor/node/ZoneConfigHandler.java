package com.yorha.common.actor.node;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.etcd.IWatchHandler;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * zone的配置更新Handler。
 *
 * <AUTHOR>
 */
public class ZoneConfigHandler implements IWatchHandler {
    private static final Logger LOGGER = LogManager.getLogger(ZoneConfigHandler.class);

    @Override
    public void onDelete(@NotNull String key) {
        LOGGER.info("gemini_system ZoneConfigHandler delete key={}", key);
    }

    @Override
    public void onUpdate(@NotNull String key, @NotNull String value) {
        LOGGER.info("gemini_system ZoneConfigHandler update key={}", key);
        // 更新小服配置
        if (!key.startsWith(ActorClusterUrlUtils.etcdZoneConfigPrefix())) {
            throw new GeminiException("ZoneConfigHandler update useless, key={}", key);
        }
        final String sZoneId = key.replaceFirst(ActorClusterUrlUtils.etcdZoneConfigPrefix(), "");
        final int zoneId = Integer.parseInt(sZoneId);
        ClusterConfigUtils.refreshZone("refresh_zone_config " + zoneId, zoneId, value);
        String status = ClusterConfigUtils.getZoneConfig(zoneId).getStringItem("cur_server_status");
        this.tryReloadServerStatusSetting(status);
        this.tryBroadcastOpenZoneMsg(zoneId);
        this.tryUpdateDisableMsg(zoneId);
        this.updateGlobalFeatureLock(zoneId);
        this.updateZoneRegisterLimitMonitor(zoneId);
    }

    /**
     * 服务器状态 reload检查
     */
    private void tryReloadServerStatusSetting(final String status) {
        try {
            // 初始化服务器状态
            final ServerOpenStatus newStatus = ServerOpenStatus.valueOf(status);
            final ServerOpenStatus oldStatus = ServerContext.getServerSetting().getOpenStatus();
            if (oldStatus == newStatus) {
                LOGGER.info("ZoneConfigHandler tryReloadServerStatusSetting skip! {}->{}", oldStatus, newStatus);
                return;
            }
            ServerContext.initServerSetting(newStatus);
            LOGGER.info("gemini_system ZoneConfigHandler initServerSetting {}->{}!", oldStatus, newStatus);
            if (ServerContext.getActorSystem() == null) {
                LOGGER.warn("ZoneConfigHandler tryBroadcastServerStatus with no ActorSystem!");
                return;
            }
            // 通知scene
            final ActorRunnable<ISceneActor> msg = new ActorRunnable<>("handleChangeServerStatus", ISceneActor::handleChangeServerStatus);
            Set<String> set = Stream.of(ActorRole.Scene.name()).collect(Collectors.toSet());
            ServerContext.getActorSystem().getRegistryValue().broadcastLocal(set, msg);
            // gate 踢人
            if (newStatus != ServerOpenStatus.OPEN) {
                SessionHelper.kickOffAllSession(CommonEnum.SessionCloseReason.SCR_SERVER_ALREADY_CLOSE);
            }
        } catch (Exception e) {
            WechatLog.error("ZoneConfigHandler checkServerStatus load status error", e);
        }
    }

    private void tryBroadcastOpenZoneMsg(final int zoneId) {
        if (!ServerContext.isZoneServer()) {
            LOGGER.info("ZoneConfigHandler skip tryBroadcastOpenZoneMsg! server is not ZoneServer!");
            return;
        }
        if (zoneId != ServerContext.getZoneId()) {
            WechatLog.error("ZoneConfigHandler skip tryBroadcastOpenZoneMsg! zoneId {} not same to server zoneId {}!", zoneId, ServerContext.getZoneId());
            return;
        }
        final ActorSystem actorSystem = ServerContext.getActorSystem();
        if (actorSystem == null) {
            LOGGER.info("ZoneConfigHandler tryBroadcastOpenZoneMsg!server is not ready actorSystem is null");
            return;
        }
        // 设置新的开服时间
        final List<IActorMailbox> mailboxes = actorSystem.getRegistryValue().batchGetMailboxByActorRole(ActorRole.Scene.name());
        for (final IActorMailbox mailbox : mailboxes) {
            final ActorRunnable<ISceneActor> msg = new ActorRunnable<>("handleChangeOpenZoneTime", (ISceneActor::handleChangeOpenZoneTime));
            ActorSendMsgUtils.send(mailbox.ref(), msg);
            LOGGER.info("gemini_system ZoneConfigHandler tell mailbox={} reloadOpenZoneTime! zoneId={}!", mailbox, zoneId);
        }
    }

    private void tryUpdateDisableMsg(final int zoneId) {
        if (!ServerContext.isZoneServer()) {
            LOGGER.info("ZoneConfigHandler skip tryUpdateDisableMsg! server is not ZoneServer!");
            return;
        }
        if (zoneId != ServerContext.getZoneId()) {
            WechatLog.error("ZoneConfigHandler skip tryUpdateDisableMsg! zoneId {} not same to server zoneId {}!", zoneId, ServerContext.getZoneId());
            return;
        }
        final ActorSystem actorSystem = ServerContext.getActorSystem();
        if (actorSystem == null) {
            LOGGER.info("ZoneConfigHandler tryUpdateDisableMsg! server is not ready actorSystem is null");
            return;
        }
        IActorRef gateRef = RefFactory.ofLocalGate();
        ActorSendMsgUtils.send(
                gateRef,
                new ActorRunnable<>("refresh_node_config", IGateActor::checkDisableModule)
        );
        LOGGER.info("ZoneConfigHandler tryUpdateDisableMsg! zoneId={}!", zoneId);
    }

    private void updateGlobalFeatureLock(final int zoneId) {
        if (!ServerContext.isZoneServer()) {
            LOGGER.info("skip updateGlobalFeatureLock! server is not ZoneServer!");
            return;
        }
        if (zoneId != ServerContext.getZoneId()) {
            WechatLog.error("skip updateGlobalFeatureLock! zoneId {} not same to server zoneId {}!", zoneId, ServerContext.getZoneId());
            return;
        }
        List<String> zoneLockedFeatures = ClusterConfigUtils.getZoneConfig(zoneId).getListStrItemMayEmpty("feature_lock");
        List<CommonEnum.ModuleEnum> newLockedFeatures = new ArrayList<>();
        try {
            if (zoneLockedFeatures == null) {
                ServerContext.setGlobalFeatureLock(newLockedFeatures);
                LOGGER.info("updateGlobalFeatureLock finish config null");
                return;
            }
            for (String feature : zoneLockedFeatures) {
                try {
                    int featureId = Integer.parseInt(feature);
                    CommonEnum.ModuleEnum moduleEnum = CommonEnum.ModuleEnum.forNumber(featureId);
                    if (moduleEnum == null) {
                        WechatLog.error("updateGlobalFeatureLock with failed config {} ", feature);
                        continue;
                    }
                    newLockedFeatures.add(moduleEnum);
                } catch (Exception e) {
                    WechatLog.error("updateGlobalFeatureLock failed feature {} ", feature, e);
                }
            }
        } catch (Exception e) {
            WechatLog.error("updateGlobalFeatureLock failed ", e);
        } finally {
            ServerContext.setGlobalFeatureLock(newLockedFeatures);
            LOGGER.info("updateGlobalFeatureLock finish {}", newLockedFeatures);
        }
    }

    private void updateZoneRegisterLimitMonitor(final int zoneId) {
        if (!ServerContext.isZoneServer()) {
            LOGGER.info("updateZoneRegisterLimitMonitor! server is not ZoneServer!");
            return;
        }
        if (zoneId != ServerContext.getZoneId()) {
            WechatLog.error("updateZoneRegisterLimitMonitor! zoneId {} not same to server zoneId {}!", zoneId, ServerContext.getZoneId());
            return;
        }
        try {
            final int registerLimit = ClusterConfigUtils.getZoneConfig(zoneId).getIntItem("register_limit");
            LOGGER.info("updateZoneRegisterLimitMonitor,  zoneId:{}, limit:{}", zoneId, registerLimit);
            MonitorUnit.GAME_ZONE_STATUS_PLAYER_REGISTER_LIMIT.labels(
                            ServerContext.getBusId(),
                            ServerContext.getWorldIdStr(),
                            String.valueOf(zoneId))
                    .set(registerLimit);
        } catch (Exception e) {
            LOGGER.error("updateZoneRegisterLimitMonitor failed ", e);
        }
    }
}


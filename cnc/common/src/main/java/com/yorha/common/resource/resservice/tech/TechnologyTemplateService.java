package com.yorha.common.resource.resservice.tech;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.TechIdTemplate;
import res.template.TechSubidTemplate;

import java.util.*;

/**
 * 科技配置处理service
 *
 * <AUTHOR>
 */
public class TechnologyTemplateService extends AbstractResService {

    private final HashMap<Integer, Set<Integer>> unLockSoldierMap = Maps.newHashMap();
    private final HashMap<Integer, Set<Integer>> unLockResourceMap = Maps.newHashMap();
    private final HashMap<Integer, Integer> spyDataLevel = Maps.newHashMap();

    public TechnologyTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        Collection<TechSubidTemplate> techSubTemplates = getResHolder().getListFromMap(TechSubidTemplate.class);
        for (TechSubidTemplate techSubTemplate : techSubTemplates) {
            List<IntPairType> paramPairList = techSubTemplate.getParamPairList();
            for (IntPairType intPairType : paramPairList) {
                CommonEnum.TechAttrType techAttrType = CommonEnum.TechAttrType.forNumber(intPairType.getKey());
                if (techAttrType == null) {
                    throw new ResourceException(StringUtils.format("科技子id表配置了不存在的属性类型. techSubId:{}, param:{}", techSubTemplate.getId(), techAttrType));
                }
                switch (techAttrType) {
                    case UNLOCK_SOLDIER: {
                        unLockSoldierMap.computeIfAbsent(techSubTemplate.getId(), (key) -> {
                            HashSet<Integer> integers = Sets.newHashSet();
                            integers.add(key);
                            return integers;
                        });
                        break;
                    }
                    case SPY_DATA_LEVEL_UP: {
                        spyDataLevel.put(techSubTemplate.getId(), intPairType.getValue());
                        break;
                    }
                    case UNLOCK_RESOURCE_TYPE: {
                        unLockResourceMap.computeIfAbsent(techSubTemplate.getId(), (key) -> {
                            HashSet<Integer> integers = Sets.newHashSet();
                            integers.add(key);
                            return integers;
                        });
                        break;
                    }
                    default: {
                        throw new ResourceException(StringUtils.format("科技子id表配置了不存在的属性类型. techSubId:{}, param:{}", techSubTemplate.getId(), techAttrType));
                    }
                }
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        Collection<TechSubidTemplate> techSubTemplates = getResHolder().getListFromMap(TechSubidTemplate.class);
        for (TechSubidTemplate techSubTemplate : techSubTemplates) {
            if (techSubTemplate.getParentId() != 0) {
                getResHolder().checkValueFromMap(TechIdTemplate.class, techSubTemplate.getParentId(),
                        () -> StringUtils.format("科技子id对应科技id不存在. 科技id：{}， 科技子id：{}", techSubTemplate.getId(), techSubTemplate.getParentId()));
            }
            for (IntPairType intPairType : techSubTemplate.getParamPairList()) {
                CommonEnum.TechAttrType techAttrType = CommonEnum.TechAttrType.forNumber(intPairType.getKey());
                if (techAttrType == null) {
                    throw new ResourceException(StringUtils.format("科技子id表配置了不存在的属性类型. techSubId:{}, param:{}", techSubTemplate.getId(), techAttrType));
                }

                switch (techAttrType) {
                    case UNLOCK_SOLDIER:
                    case SPY_DATA_LEVEL_UP:
                    case UNLOCK_RESOURCE_TYPE: {
                        break;
                    }
                    default: {
                        throw new ResourceException(StringUtils.format("科技子id表配置了不存在的属性类型. techSubId:{}, param:{}", techSubTemplate.getId(), techAttrType));
                    }
                }
            }
        }


    }

    public Set<Integer> getUnLockSoldierIds(TechSubidTemplate template) {
        return unLockSoldierMap.get(template.getId());
    }

    public Set<Integer> getUnLockResourceIds(TechSubidTemplate template) {
        return unLockResourceMap.get(template.getId());
    }

    public Integer getSpyDataLevel(TechSubidTemplate template) {
        return spyDataLevel.get(template.getId());
    }

}

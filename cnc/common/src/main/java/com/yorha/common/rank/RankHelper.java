package com.yorha.common.rank;

import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsRank;
import com.yorha.proto.StructMsg;
import com.yorha.proto.StructPB;
import res.template.ArrowDetermineTemplate;

public class RankHelper {
    public static SsRank.MemberDto buildMember(long member, long score, int zoneId) {
        return SsRank.MemberDto.newBuilder().setMemberId(member).setScore(score).setZoneId(zoneId).build();
    }

    public static SsRank.MemberRankDto buildRankMember(long memberId, int rank) {
        return SsRank.MemberRankDto.newBuilder().setMemberId(memberId).setRank(rank).build();
    }

    public static CommonMsg.MemberAllDto buildRankMemberAll(long memberId, int rank, long score, int zoneId) {
        return CommonMsg.MemberAllDto.newBuilder().setZoneId(zoneId).setMemberId(memberId).setRank(rank).setScore(score).build();
    }

    /**
     * 构建联盟排行数据
     */
    public static StructMsg.RankInfoDTO buildClanRankInfoDTO(int rankId, int rank, long score, int zoneId, CommonMsg.ClanSimpleInfo simpleClan) {
        StructMsg.RankInfoDTO.Builder dto = StructMsg.RankInfoDTO.newBuilder();
        dto.setRankId(rankId).setRank(rank).setZoneId(zoneId);
        if (zoneId != 0) {
            dto.setZoneId(zoneId);
        }
        dto.setClanId(simpleClan.getClanId());
        dto.setClanName(simpleClan.getName());
        dto.setClansimpleName(simpleClan.getSimpleName());
        dto.setFlagSign(simpleClan.getFlagSign());
        dto.setFlagColor(simpleClan.getFlagColor());
        dto.setFlagShading(simpleClan.getFlagShading());
        dto.setTerritoryColor(simpleClan.getTerritoryColor());
        dto.setPlayerId(simpleClan.getOwnerId());
        dto.getCardHeadBuilder().setName(simpleClan.getOwnerName());
        dto.setNationFlagId(simpleClan.getNationFlagId());
        dto.setValue(score);
        return dto.build();
    }

    /**
     * 构建玩家排行数据
     */
    public static StructMsg.RankInfoDTO buildPlayerRankInfoDTO(
            int rankId,
            int rank,
            long score,
            int zoneId,
            StructPB.PlayerCardInfoPB player,
            CommonMsg.ClanSimpleInfo simpleClan
    ) {
        StructMsg.RankInfoDTO.Builder dto = StructMsg.RankInfoDTO.newBuilder();
        dto.setRank(rank).setPlayerId(player.getPlayerId()).setCardHead(player.getCardHead()).setValue(score).setRankId(rankId);
        if (zoneId != 0) {
            dto.setZoneId(zoneId);
        }
        if (simpleClan != null) {
            dto.setClanId(simpleClan.getClanId())
                    .setClanName(simpleClan.getName())
                    .setClansimpleName(simpleClan.getSimpleName())
                    .setFlagColor(simpleClan.getFlagColor())
                    .setFlagShading(simpleClan.getFlagShading())
                    .setFlagSign(simpleClan.getFlagSign())
                    .setTerritoryColor(simpleClan.getTerritoryColor())
                    .setNationFlagId(simpleClan.getNationFlagId());
        }
        return dto.build();
    }

    /**
     * 构建玩家排行数据
     */
    public static StructMsg.RankInfoDTO buildPlayerRankInfoDTO(
            int rankId,
            int rank,
            long score,
            int zoneId,
            int lastRank,
            StructPB.PlayerCardInfoPB player,
            CommonMsg.ClanSimpleInfo simpleClan
    ) {
        StructMsg.RankInfoDTO.Builder dto = StructMsg.RankInfoDTO.newBuilder();
        dto.setRank(rank).setPlayerId(player.getPlayerId()).setCardHead(player.getCardHead()).setValue(score).setRankId(rankId);
        if (zoneId != 0) {
            dto.setZoneId(zoneId);
        }
        for (ArrowDetermineTemplate template : ResHolder.getInstance().getListFromMap(ArrowDetermineTemplate.class)) {
            if (rank > template.getRangePair().getKey() && rank <= template.getRangePair().getValue()) {
                if (lastRank == 0) {
                    lastRank = 999999;
                }
                if (rank - lastRank >= template.getDownNum()) {
                    dto.setArrow(-1);
                }
                if (lastRank - rank >= template.getUpNum()) {
                    dto.setArrow(1);
                }
                break;
            }
        }
        if (simpleClan != null) {
            dto.setClanId(simpleClan.getClanId())
                    .setClanName(simpleClan.getName())
                    .setClansimpleName(simpleClan.getSimpleName())
                    .setFlagColor(simpleClan.getFlagColor())
                    .setFlagShading(simpleClan.getFlagShading())
                    .setFlagSign(simpleClan.getFlagSign())
                    .setTerritoryColor(simpleClan.getTerritoryColor())
                    .setNationFlagId(simpleClan.getNationFlagId());
        }
        return dto.build();
    }

    /**
     * 构建服务器排行数据
     */
    public static StructMsg.RankInfoDTO buildZoneRankInfoDTO(int rankId, int rank, long score, int zoneId) {
        StructMsg.RankInfoDTO.Builder dto = StructMsg.RankInfoDTO.newBuilder();
        dto.setRank(rank).setValue(score).setRankId(rankId).setZoneId(zoneId);
        return dto.build();
    }

    public static CommonMsg.Range buildRange(int start, int end) {
        return CommonMsg.Range.newBuilder().setRangeStart(start).setRangeEnd(end).build();
    }
}

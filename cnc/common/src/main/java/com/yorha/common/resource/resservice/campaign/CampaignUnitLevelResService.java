package com.yorha.common.resource.resservice.campaign;

import com.google.common.collect.Maps;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.gemini.utils.StringUtils;
import res.template.CampaignUnitLevelTemplate;

import java.util.HashMap;

public class CampaignUnitLevelResService extends AbstractResService {

    /**
     * 战役关卡配置 战役难度等级->关卡等级->template
     */
    private final HashMap<Integer, HashMap<Integer, CampaignUnitLevelTemplate>> unitLevelMap = new HashMap<>();
    /**
     * 战役关卡配置 战役难度等级->关卡最大等级
     */
    private final HashMap<Integer, Integer> unitMaxLevelMap = new HashMap<>();

    public CampaignUnitLevelResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 刷新地图
        refreshCampaignUnitLevelTemplate();
    }

    @Override
    public void checkValid() throws ResourceException {
//        for (CampaignUnitLevelTemplate template : getResHolder().getListFromMap(CampaignUnitLevelTemplate.class)) {
//            if (getResHolder().findValueFromMap(UnitRhTemplate.class, template.getUnitId()) == null) {
//                throw new ResourceException(StringUtils.format("Z_战役配置表【RH】.xlsx 单位升级配置错误(campaign_unit_level). id:{} 配置的 unitId:{} 在（unit_rh）不存在", template.getId(), template.getUnitId()));
//            }
//        }
    }

    private void refreshCampaignUnitLevelTemplate() {
        for (CampaignUnitLevelTemplate template : getResHolder().getListFromMap(CampaignUnitLevelTemplate.class)) {
            HashMap<Integer, CampaignUnitLevelTemplate> levelMap = unitLevelMap.computeIfAbsent(template.getUnitType(), (key) -> Maps.newHashMap());
            levelMap.put(template.getUnitLevel(), template);

            if (unitMaxLevelMap.getOrDefault(template.getUnitType(), 0) < template.getUnitLevel()) {
                unitMaxLevelMap.put(template.getUnitType(), template.getUnitLevel());
            }
        }
    }

    public void checkUnitLevel(int unitType, int unitLevel) throws GeminiException {
        HashMap<Integer, CampaignUnitLevelTemplate> levelMap = unitLevelMap.get(unitType);
        if (levelMap == null) {
            throw new GeminiException(StringUtils.format("战役单位等级配置错误. unitType:{}, unitLevel: {} 不存在", unitType, unitLevel));
        }
        CampaignUnitLevelTemplate template = levelMap.get(unitLevel);
        if (template == null) {
            throw new GeminiException(StringUtils.format("战役单位等级配置错误. unitType:{}, unitLevel: {} 不存在", unitType, unitLevel));
        }
    }

    public int getUnitId(int unitType, int unitLevel) throws GeminiException {
        HashMap<Integer, CampaignUnitLevelTemplate> levelMap = unitLevelMap.get(unitType);
        if (levelMap == null) {
            throw new GeminiException(StringUtils.format("战役单位等级配置错误. unitType:{}, unitLevel: {} 不存在", unitType, unitLevel));
        }
        CampaignUnitLevelTemplate template = levelMap.get(unitLevel);
        if (template == null) {
            throw new GeminiException(StringUtils.format("战役单位等级配置错误. unitType:{}, unitLevel: {} 不存在", unitType, unitLevel));
        }
        return template.getUnitId();
    }

}
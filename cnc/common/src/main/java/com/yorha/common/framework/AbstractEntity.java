package com.yorha.common.framework;

import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum.EntityState;
import com.yorha.proto.EntityAttrOuterClass;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public abstract class AbstractEntity {

    private final long entityId;
    private EntityState entityState = EntityState.ES_NONE;

    protected AbstractEntity(long entityId) {
        this.entityId = entityId;
    }

    /**
     * 获取entity id
     *
     * @return entity id
     */
    public long getEntityId() {
        return entityId;
    }

    /**
     * 获取
     *
     * @return entity 类型
     */
    public abstract EntityAttrOuterClass.EntityType getEntityType();

    /**
     * @return 本entity归属的actor
     */
    public abstract AbstractActor ownerActor();

    protected void setEntityState(EntityState entityState) {
        this.entityState = entityState;
    }

    public boolean isInitOk() {
        return entityState == EntityState.ES_InitOK;
    }

    public boolean isDestroy() {
        return entityState == EntityState.ES_Destory;
    }

    /**
     * entity所有的组件
     * 使用ArrayList替代LinkedList以减少内存分配开销
     * 预分配80个元素容量，基于PlayerEntity有73个Component的实际情况
     */
    protected final ArrayList<AbstractComponent<?>> ownedComponents = new ArrayList<>(80);

    /**
     * 注册组件 key可以通过父类获取
     */
    public void registerComponent(AbstractComponent<?> component) {
        ownedComponents.add(component);
    }

    /**
     * 二阶段初始化所有的组件
     */
    protected void initAllComponents() {
        ownedComponents.forEach(AbstractComponent::init);
        setEntityState(EntityState.ES_InitOK);
        try {
            ownedComponents.forEach(AbstractComponent::postInit);
        } catch (Exception e) {
            WechatLog.error("initAllComponents {} postInit fail", this, e);
            onPostInitFailed();
            throw e;
        }
    }

    protected abstract void onPostInitFailed();

    @SuppressWarnings("unchecked")
    protected <T extends AbstractComponent<?>> Collection<T> getAllComponents() {
        return (Collection<T>) ownedComponents;
    }

    public void deleteObj() {
        if (!isInitOk()) {
            WechatLog.error("deleteObj {} with wrong state={}", this, entityState);
        }
        try {
            ownedComponents.forEach(AbstractComponent::onDestroy);
        } catch (Exception e) {
            WechatLog.error("deleteObj {} onDestroy fail", this, e);
        }
        setEntityState(EntityState.ES_Destory);
        try {
            afterDestroy();
        } catch (Exception e) {
            WechatLog.error("deleteObj {} afterDestroy fail", this, e);
        }
    }

    /**
     * 这里各entity的实现中，需要新增任何逻辑需要找 timi check
     */
    protected void afterDestroy() {

    }

    // ------------------------- 容器重写 -------------------------

    @Override

    public int hashCode() {
        return Objects.hash(getEntityId());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        AbstractEntity entity = (AbstractEntity) obj;
        return getEntityId() == entity.getEntityId();
    }

    @Override
    public String toString() {
        return "[" + getClass().getSimpleName() + "-" + getEntityId() + "]";
    }
}

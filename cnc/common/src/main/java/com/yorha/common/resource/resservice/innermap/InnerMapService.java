package com.yorha.common.resource.resservice.innermap;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.FileUtils;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapConfigRhTemplate;
import res.template.PveMissionRhTemplate;

import java.util.*;

public class InnerMapService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(InnerMapService.class);

    public static class InnerMapBlockInfo {
        public IntPairType size;
        public Set<IntPairType> blocks = new HashSet<>();
    }

    private final Map<Integer, InnerMapBlockInfo> innerMapBlock = new HashMap<>();
    //关卡的后置关卡列表
    //pve关卡->关卡
    private final Map<Integer, List<Integer>> pveMissionPost = new HashMap<>();
    //
    private int firstMainMission = 0;

    public InnerMapService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (PveMissionRhTemplate missionTemplate : getResHolder().getListFromMap(PveMissionRhTemplate.class)) {
            for (Integer preId : missionTemplate.getPrevMissionList()) {
                pveMissionPost.computeIfAbsent(preId, k -> new ArrayList<>()).add(missionTemplate.getId());
            }

            if (firstMainMission == 0 && missionTemplate.getMainMission() > 0 && missionTemplate.getPrevMissionList().isEmpty()) {
                firstMainMission = missionTemplate.getId();
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        final String gameDataDirPath;
        try {
            gameDataDirPath = ServerContext.getGameDataLocalPath();
        } catch (Exception e) {
            LOGGER.warn("HotFixTemplateService load no etcd config");
            return;
        }
        for (MapConfigRhTemplate config : getResHolder().getListFromMap(MapConfigRhTemplate.class)) {
            String fileName = StringUtils.format("{}/maps/{}.block", gameDataDirPath, config.getMapName());
            try {
                String fileText = FileUtils.readConfigFile(fileName);
                InnerMapBlockInfo block = new InnerMapBlockInfo();
                String[] lines = StringUtils.split(fileText, "\n");
                //第一行 width,height
                //此后每行 x,y
                int lineNum = 0;
                for (String line : lines) {
                    int[] array = Arrays.stream(line.split(",")).mapToInt(Integer::parseInt).toArray();
                    if (array.length < 2) {
                        continue;
                    }
                    if (lineNum == 0) {
                        block.size = IntPairType.makePair(array[0], array[1]);
                    } else {
                        block.blocks.add(IntPairType.makePair(array[0], array[1]));
                    }
                    lineNum++;
                }
                innerMapBlock.put(config.getId(), block);
            } catch (Exception e) {
                LOGGER.error("load inner map block failed", e);
            }

        }
    }

    public InnerMapBlockInfo getInnerMapBlock(int mapId) {
        return innerMapBlock.get(mapId);
    }

    //获取首个PVE关卡
    public int getFirstMainMissionId() {
        return firstMainMission;
    }

    //获取关卡的后置关卡
    public List<Integer> getPostMissions(int missionId) {
        return pveMissionPost.getOrDefault(missionId, Collections.emptyList());
    }
}

package com.yorha.common.resource.mapdata;

import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.proto.CommonEnum;

import java.util.List;


public class RegionalAreaSettingTemplate extends MapSubdivisionTemplate {
    /**
     * 片id
     * int id
     */
    @ResAttribute("id")
    private int id;

    /**
     * 所属州
     * int regionId
     */
    @ResAttribute("regionId")
    private int regionId;

    /**
     * 区域类型
     * CommonEnum.MapAreaType AreaType
     */
    @ResAttribute("AreaType")
    private CommonEnum.MapAreaType AreaType;

    /**
     * 建筑配置id
     * int buildingId
     */
    @ResAttribute("buildingId")
    private int buildingId;

    /**
     * 建筑X坐标(以10m为单位)
     * int posX
     */
    @ResAttribute("posX")
    private int posX;

    /**
     * 建筑Y坐标(以10m为单位)
     * int posY
     */
    @ResAttribute("posY")
    private int posY;

    /**
     * 相邻的片
     * intarray adjoinAreaId
     */
    @ResAttribute("adjoinAreaId")
    private List<Integer> adjoinAreaIdList;

    /**
     * 联通州id
     * intarray linkRegion
     */
    @ResAttribute("linkRegion")
    private List<Integer> linkRegionList;

    /**
     * 城门线1
     * intarray link1
     */
    @ResAttribute("link1")
    private List<Integer> link1List;

    /**
     * 城门线2
     * intarray link2
     */
    @ResAttribute("link2")
    private List<Integer> link2List;

    /**
     * 旋转角度
     * int rotation
     */
    @ResAttribute("rotation")
    private int rotation;

    /**
     * 是否有迷雾奖励
     * int ifHaveFogReward
     */
    @ResAttribute("ifHaveFogReward")
    private int ifHaveFogReward;

    /**
     * 等级
     * int level
     */
    @ResAttribute("level")
    private int level;

    /**
     * 块x
     * int chunkX
     */
    @ResAttribute("chunkX")
    private int chunkX;

    /**
     * 块y
     * int chunkY
     */
    @ResAttribute("chunkY")
    private int chunkY;

    /**
     * 特殊类型
     * int specialType
     */
    @ResAttribute("specialType")
    private int specialType;

    /**
     * 关隘围攻角度1
     * intarray besiegeAngle1
     */
    @ResAttribute("besiegeAngle1")
    private List<Integer> besiegeAngle1List;

    /**
     * 关隘围攻角度2
     * intarray besiegeAngle2
     */
    @ResAttribute("besiegeAngle2")
    private List<Integer> besiegeAngle2List;


    /**
     * 片id
     * int id
     */
    public int getId() {
        return id;
    }

    /**
     * 所属州
     * int regionId
     */
    public int getRegionId() {
        return regionId;
    }


    /**
     * 区域类型
     * CommonEnum.MapAreaType AreaType
     */
    public CommonEnum.MapAreaType getAreaType() {
        return AreaType;
    }

    /**
     * 建筑配置id
     * int buildingId
     */
    public int getBuildingId() {
        return buildingId;
    }

    /**
     * 建筑X坐标(以10m为单位)
     * int posX
     */
    public int getPosX() {
        return posX;
    }

    /**
     * 建筑Y坐标(以10m为单位)
     * int posY
     */
    public int getPosY() {
        return posY;
    }


    /**
     * 相邻的片
     * intarray adjoinAreaId
     */
    public List<Integer> getAdjoinAreaIdList() {
        return adjoinAreaIdList;
    }


    /**
     * 联通州id
     * intarray linkRegion
     */
    public List<Integer> getLinkRegionList() {
        return linkRegionList;
    }


    /**
     * 城门线1
     * intarray link1
     */
    public List<Integer> getLink1List() {
        return link1List;
    }


    /**
     * 城门线2
     * intarray link2
     */
    public List<Integer> getLink2List() {
        return link2List;
    }

    /**
     * 旋转角度
     * int rotation
     */
    public int getRotation() {
        return rotation;
    }

    /**
     * 是否有迷雾奖励
     * int ifHaveFogReward
     */
    public int getIfHaveFogReward() {
        return ifHaveFogReward;
    }

    /**
     * 等级
     * int level
     */
    public int getLevel() {
        return level;
    }

    /**
     * 块x
     * int chunkX
     */
    public int getChunkX() {
        return chunkX;
    }

    /**
     * 块y
     * int chunkY
     */
    public int getChunkY() {
        return chunkY;
    }

    /**
     * 特殊类型
     * int specialType
     */
    public int getSpecialType() {
        return specialType;
    }


    /**
     * 关隘围攻角度1
     * intarray besiegeAngle1
     */
    public List<Integer> getBesiegeAngle1List() {
        return besiegeAngle1List;
    }


    /**
     * 关隘围攻角度2
     * intarray besiegeAngle2
     */
    public List<Integer> getBesiegeAngle2List() {
        return besiegeAngle2List;
    }


}
package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.result.BatchGetResult;

import java.util.List;

public class BatchSelectAsk<T extends Message.Builder> implements GameDbReq<BatchGetResult<T>> {
    private final List<T> reqList;
    private final BatchGetOption option;

    public BatchSelectAsk(List<T> reqList) {
        this(reqList, BatchGetOption.newBuilder().build());
    }

    public BatchSelectAsk(List<T> reqList, BatchGetOption option) {
        this.reqList = reqList;
        this.option = option;
    }

    public List<T> getReqList() {
        return reqList;
    }

    public BatchGetOption getOption() {
        return option;
    }
}

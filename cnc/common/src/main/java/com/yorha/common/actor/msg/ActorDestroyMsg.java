package com.yorha.common.actor.msg;

/**
 * 销毁Actor的消息。
 *
 * <AUTHOR>
 */
public class ActorDestroyMsg implements IActorMsg {
    /**
     * 销毁Actor的理由。
     */
    private final String reason;

    public ActorDestroyMsg(String reason) {
        this.reason = reason;
    }
    
    public String getReason() {
        return reason;
    }

    @Override
    public boolean canRemote() {
        return false;
    }

    @Override
    public String toString() {
        return "ActorDestroyMsg{" +
                "reason='" + reason + '\'' +
                '}';
    }
}

package com.yorha.common.constant;

import org.apache.commons.collections4.IteratorUtils;

import java.util.Arrays;

/**
 * 鉴权常量
 *
 * <AUTHOR>
 */
public class AuthConstant {
    public static final String FACEBOOK_INPUT_TOKEN = "input_token";
    public static final String FACEBOOK_ACCESS_TOKEN = "access_token";
    public static final int FACEBOOK_AUTH_TIMEOUT_MS = 10_000;
    public static final int INTL_AUTH_TIMEOUT_MS = 3100;

    /**
     * 鉴权渠道
     */
    public enum AuthChannelType {
        GEMINI(0),
        INTL(1),
        MSDK(2),

        // 服务器端不限制渠道，用于dev/test环境，客户端既可以在unity下用gemini登录，也可以在打出来的包中用某指定渠道登录
        ANY(100),

        UNKNOWN(999),
        ;

        private final int value;

        AuthChannelType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static AuthChannelType forNumber(int value) {
            AuthChannelType type = IteratorUtils.find(Arrays.stream(AuthChannelType.values()).iterator(), it -> it.value == value);
            if (type == null) {
                return UNKNOWN;
            } else {
                return type;
            }
        }
    }

    public static final int INTL_AUTH_OS_ANDROID = 1;
    public static final int INTL_AUTH_OS_IOS = 2;

}

package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tcaplus.client.impl.ResponseLite;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.GetMetaOption;
import com.yorha.common.db.tcaplus.result.GetMetaResult;

public class TcaplusGetMeta<T extends Message.Builder> extends TcaplusOperation<T, GetMetaOption, GetMetaResult> {
    public TcaplusGetMeta(Client client, T t, GetMetaOption getMetaOption) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, getMetaOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_METADATA_GET_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
    }

    @Override
    protected GetMetaResult buildResult(Response response) {
        ResponseLite rspLite = (ResponseLite) response;

        GetMetaResult result = new GetMetaResult();
        result.code = TcaplusErrorCode.forNumber(response.getResult());
        result.value = rspLite.getMetadata();
        return result;
    }
}

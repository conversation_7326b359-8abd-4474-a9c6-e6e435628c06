package com.yorha.common.qlog;


/**
 * 以联盟为单位的派发流水
 *
 * <AUTHOR>
 */
public abstract class AbstractClanQlogFlow extends AbstractQlogFlow {
    /**
     * 服务器类型
     */
    private String serverType;
    /**
     * 联盟所处的服务器id
     */
    private String iZoneAreaID;
    /**
     * 联盟首次创建时间
     */
    private String guildCreateTime;
    /**
     * 联盟id
     */
    private String guildId;
    /**
     * 联盟名字
     */
    private String guildName;
    /**
     * 联盟简称
     */
    private String guildShortName;
    /**
     * 联盟等级
     */
    private int guildLevel;
    /**
     * 联盟人数
     */
    private int guildPopulation;
    /**
     * 联盟在线人数
     */
    private int guildOnline;
    /**
     * 联盟礼物等级
     */
    private int guildGiftLevel;
    /**
     * 联盟总战力
     */
    private long guildPower;
    /**
     * 联盟总势力
     */
    private long guildInfluence;
    /**
     * 联盟盟主id
     */
    private String guildLeaderId;
    /**
     * accountId
     */
    private String accountId;

    protected AbstractClanQlogFlow() {
        serverType = "";
        iZoneAreaID = "";
        guildCreateTime = "";
        guildId = "";
        guildName = "";
        guildShortName = "";
        guildLeaderId = "";
        accountId = "";
    }

    public void setServerType(String serverType) {
        this.serverType = serverType;
    }

    public void setIZoneAreaID(String iZoneAreaID) {
        this.iZoneAreaID = iZoneAreaID;
    }

    public void setGuildCreateTime(String guildCreateTime) {
        this.guildCreateTime = guildCreateTime;
    }

    public void setGuildId(String guildId) {
        this.guildId = guildId;
    }

    public void setGuildName(String guildName) {
        this.guildName = guildName;
    }

    public void setGuildShortName(String guildShortName) {
        this.guildShortName = guildShortName;
    }

    public void setGuildLevel(int guildLevel) {
        this.guildLevel = guildLevel;
    }

    public void setGuildPopulation(int guildPopulation) {
        this.guildPopulation = guildPopulation;
    }

    public void setGuildOnline(int guildOnline) {
        this.guildOnline = guildOnline;
    }

    public void setGuildGiftLevel(int guildGiftLevel) {
        this.guildGiftLevel = guildGiftLevel;
    }

    public void setGuildPower(long guildPower) {
        this.guildPower = guildPower;
    }

    public void setGuildInfluence(long guildInfluence) {
        this.guildInfluence = guildInfluence;
    }

    public void setGuildLeaderId(String guildLeaderId) {
        this.guildLeaderId = guildLeaderId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    private void fillFullHead(QlogClanFlowInterface clanFlow) {
        setServerType(clanFlow.getServer_type());
        setIZoneAreaID(clanFlow.getIZoneAreaID());
        setGuildCreateTime(clanFlow.getGuildCreate_time());
        setGuildId(clanFlow.getGuild_id());
        setGuildName(clanFlow.getGuild_name());
        setGuildShortName(clanFlow.getGuildShortName());
        setGuildLevel(clanFlow.getGuild_level());
        setGuildPopulation(clanFlow.getGuild_population());
        setGuildOnline(clanFlow.getGuild_online());
        setGuildGiftLevel(clanFlow.getGuildGiftLevel());
        setGuildPower(clanFlow.getGuild_power());
        setGuildInfluence(clanFlow.getGuild_influence());
        setGuildLeaderId(clanFlow.getGuildLeader_id());
        setAccountId(clanFlow.getAccountId());
    }

    private void fillSimpleHead(QlogClanFlowInterface clanFlow) {
        setServerType(clanFlow.getServer_type());
        setIZoneAreaID(clanFlow.getIZoneAreaID());
        setGuildId(clanFlow.getGuild_id());
        setAccountId(clanFlow.getAccountId());
        setGuildCreateTime("");
        setGuildName("");
        setGuildShortName("");
        setGuildLevel(0);
        setGuildPopulation(0);
        setGuildOnline(0);
        setGuildGiftLevel(0);
        setGuildPower(0);
        setGuildInfluence(0);
        setGuildLeaderId("");
    }

    public void fillHead(QlogClanFlowInterface clanFlow) {
        if (needFullHead()) {
            fillFullHead(clanFlow);
            return;
        }
        fillSimpleHead(clanFlow);
    }

    protected boolean needFullHead() {
        return true;
    }

    @Override
    protected void addFlowHeadContent(StringBuilder builder) {
        builder.append("|").append(serverType)
                .append("|").append(iZoneAreaID)
                .append("|").append(guildCreateTime)
                .append("|").append(guildId)
                .append("|").append(guildName)
                .append("|").append(guildShortName)
                .append("|").append(guildLevel)
                .append("|").append(guildPopulation)
                .append("|").append(guildOnline)
                .append("|").append(guildGiftLevel)
                .append("|").append(guildPower)
                .append("|").append(guildInfluence)
                .append("|").append(guildLeaderId)
                .append("|").append(accountId);
    }
}

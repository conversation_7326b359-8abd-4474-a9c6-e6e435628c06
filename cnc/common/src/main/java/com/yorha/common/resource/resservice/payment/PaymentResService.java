package com.yorha.common.resource.resservice.payment;

import com.google.common.collect.ArrayListMultimap;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.List;
import java.util.Map;

/**
 * 买买买
 */
public class PaymentResService extends AbstractResService {
    private final ArrayListMultimap<Integer, TriggerTypeTemplate> triggerTypeMap = ArrayListMultimap.create();
    // triggerId -> 可以触发的礼包配置
    private final ArrayListMultimap<Integer, TriggerGoodsTemplate> triggerBundleMap = ArrayListMultimap.create();
    // 活动id -> 呼脸礼包的回归个数配置
    private final ArrayListMultimap<Integer, ActivityResellTriggerGoodsTemplate> resellTriggerGoodsMap = ArrayListMultimap.create();

    private int growthFundGoodsId;

    public PaymentResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (TriggerTypeTemplate triggerTypeTemplate : getResHolder().getListFromMap(TriggerTypeTemplate.class)) {
            int triggerType = triggerTypeTemplate.getTriggerType();
            triggerTypeMap.put(triggerType, triggerTypeTemplate);
        }
        for (TriggerGoodsTemplate triggerGoodsTemplate : getResHolder().getListFromMap(TriggerGoodsTemplate.class)) {
            int triggerId = triggerGoodsTemplate.getTriggerId();
            if (triggerId > 0) {
                triggerBundleMap.put(triggerId, triggerGoodsTemplate);
                if (!getResHolder().hasTemplate(TriggerTypeTemplate.class, triggerId)) {
                    throw new ResourceException("糊脸礼包的触发器id没有对应配置，礼包:{} 触发器:{}", triggerGoodsTemplate.getId(), triggerId);
                }
            }
        }

        for (ActivityResellTriggerGoodsTemplate template : getResHolder().getListFromMap(ActivityResellTriggerGoodsTemplate.class)) {
            resellTriggerGoodsMap.put(template.getActivityId(), template);
        }
        for (ChargeGoodsTemplate goodsTemplate : getResHolder().getListFromMap(ChargeGoodsTemplate.class)) {
            if (goodsTemplate.getGoodsType() == CommonEnum.GoodsType.GT_GROWTH_FUND) {
                if (this.growthFundGoodsId > 0) {
                    throw new ResourceException("成长基金礼包只能配置一个，不然有问题的,现在有多个了 {} {}", this.growthFundGoodsId, goodsTemplate.getId());
                }
                this.growthFundGoodsId = goodsTemplate.getId();
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        checkChargeSdkTemplates();
        checkChargeGoodsTemplates();
        checkChargeBaseTemplates();
        checkChargeChainTemplates();
        checkTriggerGoods();
        checkVipGoods();
        checkBattlePassGoods();
        checkSelectGoods();
        checkRandomGoods();
    }

    private void checkSelectGoods() throws ResourceException {
        Map<Integer, SelecteRewardTemplate> selecteRewardTemplateMap = getResHolder().getMap(SelecteRewardTemplate.class);
        for (ChargeGoodsTemplate goodsTemplate : getResHolder().getListFromMap(ChargeGoodsTemplate.class)) {
            if (goodsTemplate.getGoodsType() != CommonEnum.GoodsType.GT_SELECT) {
                continue;
            }
            if (goodsTemplate.getSelectRewardsList().isEmpty()) {
                throw new ResourceException("Z_支付 charge_goods 自选礼包 selectRewards 不可为空 {}", goodsTemplate.getId());
            }
            for (int selectReward : goodsTemplate.getSelectRewardsList()) {
                if (!selecteRewardTemplateMap.containsKey(selectReward)) {
                    throw new ResourceException("Z_支付 charge_goods 自选礼包 selectRewards 配置了不存在的宝箱id {} {}", goodsTemplate.getId(), selectReward);
                }
                SelecteRewardTemplate selecteRewardTemplate = selecteRewardTemplateMap.get(selectReward);
                if (selecteRewardTemplate.getType() != CommonEnum.SelectorType.ST_PICK_UP) {
                    throw new ResourceException("Z_支付 charge_goods 自选礼包 selectRewards 只能配自选宝箱 {} {}", goodsTemplate.getId(), selectReward);
                }
                if (selecteRewardTemplate.getRewards().isEmpty()) {
                    throw new ResourceException("J_奖励 select_reward 自选宝箱的 rewards不可为空 {}", selectReward);
                }
            }
        }
    }

    private void checkRandomGoods() throws ResourceException {
        Map<Integer, SelecteRewardTemplate> selecteRewardTemplateMap = getResHolder().getMap(SelecteRewardTemplate.class);
        for (ChargeGoodsTemplate goodsTemplate : getResHolder().getListFromMap(ChargeGoodsTemplate.class)) {
            if (goodsTemplate.getGoodsType() != CommonEnum.GoodsType.GT_RANDOM) {
                continue;
            }
            if (goodsTemplate.getSelectRewardsList().isEmpty()) {
                throw new ResourceException("Z_支付 charge_goods 随机礼包 selectRewards 不可为空 {}", goodsTemplate.getId());
            }
            for (int selectReward : goodsTemplate.getSelectRewardsList()) {
                if (!selecteRewardTemplateMap.containsKey(selectReward)) {
                    throw new ResourceException("Z_支付 charge_goods 随机礼包 selectRewards 配置了不存在的宝箱id {} {}", goodsTemplate.getId(), selectReward);
                }
                SelecteRewardTemplate selecteRewardTemplate = selecteRewardTemplateMap.get(selectReward);
                if (selecteRewardTemplate.getType() != CommonEnum.SelectorType.ST_COMPOUND) {
                    throw new ResourceException("Z_支付 charge_goods 随机礼包 selectRewards 只能配权重宝箱 {} {}", goodsTemplate.getId(), selectReward);
                }
                if (selecteRewardTemplate.getRewards().isEmpty()) {
                    throw new ResourceException("J_奖励 select_reward 权重宝箱的 rewards不可为空 {}", selectReward);
                }
            }
        }
    }

    private void checkBattlePassGoods() throws ResourceException {
        ConstBpTemplate constBp = getResHolder().getConstTemplate(ConstBpTemplate.class);
        ensureGoodsIsType("通行证礼包", constBp.getBPSmallGiftID(), CommonEnum.GoodsType.GT_BATTLEPASS);
        ensureGoodsIsType("通行证礼包", constBp.getBPmidGiftID(), CommonEnum.GoodsType.GT_BATTLEPASS);
        ensureGoodsIsType("通行证礼包", constBp.getBPBigGiftID(), CommonEnum.GoodsType.GT_BATTLEPASS);
        ensureGoodsIsType("通行证礼包", constBp.getSeasonBPSmallGiftID(), CommonEnum.GoodsType.GT_SEASON_BATTLEPASS);
        ensureGoodsIsType("通行证礼包", constBp.getSeasonBPmidGiftID(), CommonEnum.GoodsType.GT_SEASON_BATTLEPASS);
        ensureGoodsIsType("通行证礼包", constBp.getSeasonBPBigGiftID(), CommonEnum.GoodsType.GT_SEASON_BATTLEPASS);
    }

    private void checkVipGoods() throws ResourceException {
        for (VipCommonTemplate template : getResHolder().getListFromMap(VipCommonTemplate.class)) {
            ensureGoodsIsType("vip尊享礼包", template.getExclusiveBoxId(), CommonEnum.GoodsType.GT_VIP);
        }
    }

    private void checkTriggerGoods() throws ResourceException {
        for (TriggerGoodsTemplate template : getResHolder().getListFromMap(TriggerGoodsTemplate.class)) {
            ensureGoodsIsType("糊脸礼包", template.getGoodsId(), CommonEnum.GoodsType.GT_TRIGGER_BUNDLE);
        }
    }

    private void checkChargeChainTemplates() throws ResourceException {
        for (ChargeChainTemplate chargeChainTemplate : getResHolder().getListFromMap(ChargeChainTemplate.class)) {
            int id = chargeChainTemplate.getId();
            if (chargeChainTemplate.getChargeChainGoodsIdList().isEmpty()) {
                throw new ResourceException("支付表 charge_chain id={} 空子礼包链", id);
            }

            for (Integer chargeChainGoodsId : chargeChainTemplate.getChargeChainGoodsIdList()) {
                if (getResHolder().findValueFromMap(ChargeGoodsTemplate.class, chargeChainGoodsId) == null) {
                    throw new ResourceException("支付表 charge_chain id={} 礼包链中含未知子礼包 goodsId={}", id, chargeChainGoodsId);
                }
            }
        }
    }

    private void checkChargeSdkTemplates() throws ResourceException {
        for (ChargeSdkTemplate chargeSdkTemplate : getResHolder().getListFromMap(ChargeSdkTemplate.class)) {
            final int honourVoucherItemId = chargeSdkTemplate.getHonourVoucherItemId();
            if (honourVoucherItemId == 0) {
                continue;
            }
            final ItemTemplate itemTemplate = getResHolder().findValueFromMap(ItemTemplate.class, honourVoucherItemId);
            if (itemTemplate == null) {
                throw new ResourceException("支付表 charge_sdk id={} 配置的荣耀金券道具Id={}, 不存在于道具表", chargeSdkTemplate.getId(), honourVoucherItemId);
            }
            if (itemTemplate.getEffectType() != CommonEnum.ItemUseType.HONOUR_VOUCHER_VALUE) {
                throw new ResourceException("支付表 charge_sdk id={} 配置的荣耀金券道具Id={}, 道具类型不是荣耀金券", chargeSdkTemplate.getId(), honourVoucherItemId);
            }
        }

    }

    private void checkChargeBaseTemplates() throws ResourceException {
        for (ChargeBaseTemplate chargeBaseTemplate : getResHolder().getListFromMap(ChargeBaseTemplate.class)) {
            int id = chargeBaseTemplate.getId();
            int chargeSdkId = chargeBaseTemplate.getChargeSdkId();
            if (getResHolder().findValueFromMap(ChargeSdkTemplate.class, chargeSdkId) == null) {
                throw new ResourceException("支付表 charge_base id={} 未知 支付的产品id={}", id, chargeSdkId);
            }
        }
    }

    private void checkChargeGoodsTemplates() throws ResourceException {
        for (ChargeGoodsTemplate chargeGoodsTemplate : getResHolder().getListFromMap(ChargeGoodsTemplate.class)) {
            int id = chargeGoodsTemplate.getId();
            int chargeSdkId = chargeGoodsTemplate.getChargeSdkId();

            if (getResHolder().findValueFromMap(ChargeSdkTemplate.class, chargeSdkId) == null) {
                throw new ResourceException("支付表 charge_goods id={} 未知 支付的产品id={}", id, chargeSdkId);
            }

            for (IntPairType goodsReward : chargeGoodsTemplate.getGoodsRewardPairList()) {
                int itemId = goodsReward.getKey();
                int count = goodsReward.getValue();
                if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                    throw new ResourceException("支付表 charge_goods id={} 赠送奖励中含未知道具 itemId={}", id, itemId);
                }
                if (count <= 0) {
                    throw new ResourceException("支付表 charge_goods id={} 赠送奖励中道具数量错误 itemId={}, count={}", id, itemId, count);
                }
            }

            for (IntPairType subGoods : chargeGoodsTemplate.getSubGoodsIdPairList()) {
                int goodsId = subGoods.getKey();
                int count = subGoods.getValue();
                if (getResHolder().findValueFromMap(ChargeGoodsTemplate.class, goodsId) == null) {
                    throw new ResourceException("支付表 charge_goods id={} 子礼包内容中含未知礼包 goodsId={}", id, goodsId);
                }
                if (count <= 0) {
                    throw new ResourceException("支付表 charge_goods id={} 子礼包内容中礼包数量错误 itemId={}, count={}", id, goodsId, count);
                }
            }
        }
    }

    public List<TriggerTypeTemplate> getTriggerTemplates(int triggerType) {
        return triggerTypeMap.get(triggerType);
    }

    public List<TriggerGoodsTemplate> getTriggerBundleList(int triggerId) {
        return triggerBundleMap.get(triggerId);
    }

    public List<ActivityResellTriggerGoodsTemplate> getResellTriggerGoodsList(int activityId) {
        return resellTriggerGoodsMap.get(activityId);
    }

    public void ensureGoodsIsType(String signature, int goodsId, CommonEnum.GoodsType goodsType) throws ResourceException {
        ChargeGoodsTemplate goodsTemplate = getResHolder().findValueFromMap(ChargeGoodsTemplate.class, goodsId);
        if (goodsTemplate == null) {
            throw new ResourceException("{} 找不到支付礼包配置 礼包Id={} ", signature, goodsId);
        }
        if (goodsTemplate.getGoodsType() != goodsType) {
            throw new ResourceException("{} 支付礼包类型不匹配 礼包Id={} 应为={} 实为={}", signature, goodsId, goodsType, goodsTemplate.getGoodsType());
        }
    }

    public int getGrowthFundGoodsId() {
        return growthFundGoodsId;
    }
}

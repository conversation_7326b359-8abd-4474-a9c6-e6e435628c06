package com.yorha.common.utils;

import com.yorha.common.server.config.ServerInfo;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;

public class BusIdUtils {

    public static String formBusId(int worldId, int zoneId, int serverTypeId, int instanceId) {
        return StringUtils.format("{}.{}.{}.{}", worldId, zoneId, serverTypeId, instanceId);
    }

    public static int getWorldIdFromBusId(String busId) {
        final String[] busIds = getSplitId(busId);
        return Integer.parseInt(busIds[0]);
    }

    public static int getZoneIdFromBusId(String busId) {
        final String[] busIds = getSplitId(busId);
        return Integer.parseInt(busIds[1]);
    }

    public static int getServerTypeFromBusId(String busId) {
        final String[] busIds = getSplitId(busId);
        return Integer.parseInt(busIds[2]);
    }

    public static int getInstanceIdFromBusId(String busId) {
        final String[] busIds = getSplitId(busId);
        return Integer.parseInt(busIds[3]);
    }

    static String[] getSplitId(String busId) {
        final String[] busIds = busId.split("\\.");
        if (busIds.length != 4) {
            throw new GeminiException("wrong busId:{}", busId);
        }
        return busIds;
    }

    public static int transFormBusIdToInt(final ServerInfo serverInfo) {
        final int worldId = serverInfo.getWorldId();
        final int zoneId = serverInfo.getZoneId();
        final int typeId = serverInfo.getServerTypeId();
        final int instanceId = serverInfo.getInstanceId();
        // 6 位 worldId, 13 位 zoneId, 5 位 typeId, 8 位 instanceId
        return ((worldId & 0x3F) << 26) | ((zoneId & 0x1FFF) << 13) | ((typeId & 0x1F) << 8) | (instanceId & 0xFF);
    }
}

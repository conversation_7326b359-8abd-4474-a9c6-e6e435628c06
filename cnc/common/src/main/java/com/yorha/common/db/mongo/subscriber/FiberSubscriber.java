package com.yorha.common.db.mongo.subscriber;

import com.yorha.common.concurrent.FiberAsync;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 带有fiber能力的subscriber
 *
 * <AUTHOR>
 */
public class FiberSubscriber<Response, ConsumeResponse> extends FiberAsync<ConsumeResponse, Exception> {
    private final IBasicSubscriber<Response, ConsumeResponse> subscriber;
    private static final Logger LOGGER = LogManager.getLogger(FiberSubscriber.class);

    public FiberSubscriber(IBasicSubscriber<Response, ConsumeResponse> subscriber) {
        this.subscriber = subscriber;
        this.subscriber.afterConsume((response, throwable) -> {
            if (throwable != null) {
                LOGGER.error("FiberSubscriber run fail, e=", throwable);
                this.asyncFailed(throwable);
            } else {
                this.asyncCompleted(response);
            }

        });
    }

    @Override
    protected void requestAsync() {
        this.subscriber.start();
    }
}



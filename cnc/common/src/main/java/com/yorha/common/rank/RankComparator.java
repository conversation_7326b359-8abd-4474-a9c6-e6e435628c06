package com.yorha.common.rank;

import io.gamioo.redis.zset.generic.ScoreHandler;

/**
 * 排行榜比较器
 *
 * <AUTHOR>
 */
public class RankComparator implements ScoreHandler<RankMember> {

    @Override
    public int compare(RankMember o1, RankMember o2) {
        final int result = Long.compare(o2.getScore(), o1.getScore());
        if (result != 0) {
            return result;
        }
        // 时间戳升序(时间戳小的排前面)
        return Long.compare(o1.getAddTime(), o2.getAddTime());
    }

    @Override
    public RankMember sum(RankMember oldScore, RankMember increment) {
        throw new UnsupportedOperationException();
    }
}



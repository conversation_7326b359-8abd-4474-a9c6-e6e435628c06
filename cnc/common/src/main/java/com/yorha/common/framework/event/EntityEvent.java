package com.yorha.common.framework.event;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;

/**
 * 静态事件，Player、Clan使用
 *
 * <AUTHOR>
 */
public abstract class EntityEvent {
    private static final Logger LOGGER = LogManager.getLogger(EntityEvent.class);

    @SuppressWarnings("unchecked")
    public void dispatch() {
        Collection<EntityEventHandler<? extends EntityEvent>> handlers = EntityEventHandlerHolder.getHandlers(this);
        if (handlers == null) {
            return;
        }
        for (EntityEventHandler handler : handlers) {
            try {
                handler.handle(this);
            } catch (Exception e) {
                if (GeminiException.isLogicException(e)) {
                    LOGGER.error("exception_perf logic EntityEvent {} dispatch fail! ", this, e);
                } else {
                    WechatLog.error("exception_perf EntityEvent {} dispatch fail! ", this, e);
                }
            }
        }
    }

}

package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.UpdateOption;
import com.yorha.common.db.tcaplus.result.UpdateResult;

public class TcaplusUpdate<T extends Message.Builder> extends TcaplusOperation<T, UpdateOption, UpdateResult<T>> {
    public TcaplusUpdate(Client client, T t, UpdateOption option) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, option);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_UPDATE_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        request.setResultFlag(this.getOption().getResultFlag());
        Record record = request.addRecord();
        configRecordProperty(record);
        setRequestKeys(getReq(), record);
        setRequestValues(getReq(), record);
    }

    private void configRecordProperty(Record record) {
        // 设置版本
        if (getOption().getVersion() >= 0) {
            record.setVersion(getOption().getVersion());
        }
    }

    @Override
    protected UpdateResult<T> buildResult(Response response) {
        UpdateResult<T> result = new UpdateResult<>();
        result.code = TcaplusErrorCode.forNumber(response.getResult());
        result.value = buildDefaultValue(getReq());
        Record record;
        // Tcaplus SDK 所有proxy宕机保护
        try {
            record = response.fetchRecord();
        } catch (NullPointerException e) {
            record = null;
        }
        if (record != null) {
            result.version = record.getVersion();
            readFromResponseValues(record, result.value);
        }
        return result;
    }
}

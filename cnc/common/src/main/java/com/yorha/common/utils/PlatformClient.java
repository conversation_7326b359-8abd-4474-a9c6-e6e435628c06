package com.yorha.common.utils;

import com.google.gson.JsonObject;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.ThirdPartyErrLogLimiter;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.HttpResponseException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.fluent.Request;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR> Jiang
 */
public abstract class PlatformClient {
    private static final Logger LOGGER = LogManager.getLogger(PlatformClient.class);

    // 一条连接建立后15s还空闲的话，就回收释放掉
    private static final int DEFAULT_CONNECTION_EVICT_MS = 15_000;
    private static final int DEFAULT_MAX_CONN_COUNT = 1000;
    private static final int DEFAULT_MAX_CONN_COUNT_PER_ROUTE = 500;

    private final int timeout;
    private final int connectionEvictMs;
    private final PoolingHttpClientConnectionManager manager;
    private final CloseableHttpClient defaultClient;

    /**
     * @param timeout 超时时间
     */
    public PlatformClient(int timeout) {
        this(timeout, DEFAULT_CONNECTION_EVICT_MS, DEFAULT_MAX_CONN_COUNT, DEFAULT_MAX_CONN_COUNT_PER_ROUTE);
    }

    public PlatformClient(int timeout, int connectionEvictMs,
                          int maxConnCount, int maxConnCountPerRoute) {
        this.timeout = timeout;
        this.connectionEvictMs = connectionEvictMs;
        this.manager = new PoolingHttpClientConnectionManager();
        manager.setMaxTotal(maxConnCount);
        manager.setDefaultMaxPerRoute(maxConnCountPerRoute);
        this.defaultClient = createSslInsecureClient();
    }

    public PoolingHttpClientConnectionManager getManager() {
        return manager;
    }

    public abstract String getHostName();

    public HttpGet buildGetReq(String path, Map<String, Object> params, Map<String, String> headers) {
        String url = UrlUtils.buildUrl(path, params);
        url = getHostName() + url;
        HttpGet req = new HttpGet(url);
        req.setConfig(commonConfig());
        headers.forEach(req::addHeader);
        LOGGER.info("http req get: {}", url);
        return req;
    }

    @Nullable
    public JsonObject execute2json(HttpRequestBase request) {
        return execute(request, JsonUtils::parseObject);
    }

    @Nullable
    public String execute(HttpRequestBase request) {
        return execute(request, str -> str);
    }

    @Nullable
    public <T> T execute(HttpRequestBase request, Function<String, T> respTransfer) {
        try {
            LOGGER.info("http exec req={} headers={}", request, request.getAllHeaders());
            ResponseHandler<String> responseHandler = response -> {
                int status = response.getStatusLine().getStatusCode();
                if (status >= 200 && status < 300) {
                    HttpEntity entity = response.getEntity();
                    return entity != null ? EntityUtils.toString(entity, Consts.UTF_8) : null;
                } else {
                    ThirdPartyErrLogLimiter.HTTP_FAIL.tryError("http query failed,url:{},Unexpected response status:{} ", request.getURI(), status);
                    throw new HttpResponseException(status, "Unexpected response");
                }
            };
            long start = System.nanoTime();
            String ret = this.defaultClient.execute(request, responseHandler);
            LOGGER.info("http recv delay={} ms, entity={}", (System.nanoTime() - start) / 1000000f, ret);
            return ret == null ? null : respTransfer.apply(ret);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.releaseConnection();
//            try {
//                client.close();
//            } catch (IOException e) {
//                LOGGER.error(e.getMessage(), e);
//            }
        }
        return null;
    }

    public RequestConfig commonConfig() {
        // 设置建立连接超时时间
        // 设置读数据超时时间
        return RequestConfig.custom()
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout)
                .setConnectTimeout(timeout)
                .build();
    }

    public JsonObject get4https(String url) {
        url = getHostName() + (url == null ? "" : url);
        LOGGER.info("url={}", url);
        HttpGet httpGet = new HttpGet(url);
        httpGet.setConfig(commonConfig());
        return execute2json(httpGet);
    }


    public JsonObject post4https(String url, Map<String, Object> content) {
        return post4https(url, content, Collections.emptyMap());
    }

    public JsonObject post4https(String url, Map<String, Object> content, Map<String, String> headers) {
        return post4https(url, JsonUtils.toJsonString(content), headers);
    }

    public JsonObject post4https(String url, String content, Map<String, String> headers) {
        if (url == null) {
            url = "";
        }
        url = getHostName() + url;
        LOGGER.info("url={},content={} header={}", url, content, headers);
        HttpPost httpPost = new HttpPost(url);
        StringEntity postEntity = new StringEntity(content, "UTF-8");

        headers.forEach(httpPost::addHeader);
        httpPost.setEntity(postEntity);
        httpPost.setConfig(commonConfig());

        return execute2json(httpPost);
    }


    public String post4https(String url, String content) {
        if (url == null) {
            url = "";
        }
        url = getHostName() + url;
        HttpPost httpPost = new HttpPost(url);
        StringEntity postEntity = new StringEntity(content, "UTF-8");
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(postEntity);
        httpPost.setConfig(commonConfig());
        return execute(httpPost);
    }

    private CloseableHttpClient createSslInsecureClient() {
        try {
            //	String[] sslClientProtocols = {"TLSv1","TLSv1.1","TLSv1.2"};

            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext,
                    new NoopHostnameVerifier());
            // 用连接池
            return HttpClients.custom().setSSLSocketFactory(sslsf)
                    .setConnectionManager(manager)
                    .evictExpiredConnections()
                    .evictIdleConnections(connectionEvictMs, TimeUnit.MILLISECONDS)
                    .build();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return HttpClients.createDefault();
    }

    public JsonObject get4Json(String url, Map<String, Object> params) {
        url = UrlUtils.buildUrl(url, params);
        return this.get4ReturnJson(url);
    }

    public String put4String(String url, Map<String, Object> params) {
        url = UrlUtils.buildUrl(url, params);
        return this.put4ReturnString(url);
    }


    public String post4String(String url, Map<String, Object> params) {
        url = UrlUtils.buildUrl(url, params);
        return this.post4ReturnString(url);
    }

    public String get4String(String url, Map<String, Object> params) {
        url = UrlUtils.buildUrl(url, params);
        return this.get4ReturnString(url);
    }

    public String delete4String(String url, Map<String, Object> params) {
        url = UrlUtils.buildUrl(url, params);
        return this.delete4ReturnString(url);
    }

    public <T> T get(String url, Map<String, Object> params, Class<T> clazz) {
        url = UrlUtils.buildUrl(url, params);
        return this.get(url, clazz);
    }


    public JsonObject get4https(String url, Map<String, Object> params) {
        url = UrlUtils.buildUrl(url, params);
        return this.get4https(url);
    }

    /**
     * A {@link PoolingHttpClientConnectionManager} with maximum 100 connections
     * per route and a total maximum of 200 connections is used internally.
     */
    public <T> T get(String url, Class<T> clazz) {
        url = getHostName() + url;
        LOGGER.debug("url={}", url);
        Request request = Request.Get(url);
        long start = System.nanoTime();
        try {
            String ret = request.connectTimeout(timeout).socketTimeout(timeout).execute().returnContent().asString();
            LOGGER.debug("recv delay={} ms", (System.nanoTime() - start) / 1000000f);
            if (ret != null) {
                T object = JsonUtils.parseObject(ret, clazz);
                return object;
            }

        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * A {@link PoolingHttpClientConnectionManager} with maximum 100 connections
     * per route and a total maximum of 200 connections is used internally.
     */
    public JsonObject get4ReturnJson(String url) {
        url = getHostName() + url;
        LOGGER.info("url={}", url);
        Request request = Request.Get(url);
        long start = System.nanoTime();
        try {
            String ret = request.connectTimeout(timeout).socketTimeout(timeout).execute().returnContent().asString();
            LOGGER.info("recv delay={} ms", (System.nanoTime() - start) / 1000000f);
            if (ret != null) {
                JsonObject object = JsonUtils.parseObject(ret);
                return object;
            }

        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    public String get4ReturnString(String url) {
        LOGGER.info("url={}", url);
        Request request = Request.Get(url);
        long start = System.nanoTime();
        try {
            String ret = request.connectTimeout(timeout).socketTimeout(timeout).execute().returnContent().asString();
            LOGGER.info("recv delay={} ms", (System.nanoTime() - start) / 1000000f);
            return ret;

        } catch (Exception e) {
            if (e.getMessage().indexOf("500") < 0) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        return null;
    }

    public String put4ReturnString(String url) {
        Request request = Request.Put(url);
        long start = System.nanoTime();
        try {
            String ret = request.connectTimeout(timeout).socketTimeout(timeout).execute().returnContent().asString();
            LOGGER.info("recv delay={} ms", (System.nanoTime() - start) / 1000000f);
            return ret;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    public String post4ReturnString(String url) {
        Request request = Request.Post(url);
        long start = System.nanoTime();
        try {
            String ret = request.connectTimeout(timeout).socketTimeout(timeout).execute().returnContent().asString();
            LOGGER.info("recv delay={} ms", (System.nanoTime() - start) / 1000000f);
            return ret;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    public String delete4ReturnString(String url) {
        LOGGER.debug("url={}", url);
        Request request = Request.Delete(url);
        long start = System.nanoTime();
        try {
            String ret = request.connectTimeout(timeout).socketTimeout(timeout).execute().returnContent().asString();
            LOGGER.info("recv delay={} ms", (System.nanoTime() - start) / 1000000f);
            return ret;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

}

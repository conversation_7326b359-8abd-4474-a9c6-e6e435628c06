package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.UpsertResult;

/**
 * <AUTHOR>
 */
public class UpsertAsk<T extends Message.Builder> implements GameDbReq<UpsertResult<T>> {
    private final T req;
    private final UpsertOption option;

    public UpsertAsk(T req, UpsertOption option) {
        this.req = req;
        this.option = option;
    }

    public UpsertOption getOption() {
        return option;
    }

    public T getReq() {
        return req;
    }
}

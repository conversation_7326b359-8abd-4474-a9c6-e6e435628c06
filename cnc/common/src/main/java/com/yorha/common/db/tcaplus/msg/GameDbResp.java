package com.yorha.common.db.tcaplus.msg;

import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.actor.msg.IActorMsg;

public interface GameDbResp extends IActorMsg {

    int getCode();

    default boolean isRecordNotExist() {
        return getCode() == TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST.getValue();
    }

    default boolean isRecordAlreadyExist() {
        return getCode() == TcaplusErrorCode.SVR_ERR_FAIL_RECORD_EXIST.getValue();
    }

    default boolean isOk() {
        return getCode() == TcaplusErrorCode.GEN_ERR_SUC.getValue();
    }

    default boolean isInvalidVersion() {
        return getCode() == TcaplusErrorCode.SVR_ERR_FAIL_INVALID_VERSION.getValue();
    }

    @Override
    default boolean canRemote() {
        return false;
    }
}

package com.yorha.common.resource.resservice.map;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.FogBuildingTemplate;
import com.yorha.common.resource.mapdata.MapSubdivisionTemplate;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import res.template.InitializationWildBuildingTemplate;
import res.template.MapBuildingTemplate;
import res.template.OpenRegionTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 地缘区域数据管理
 */
public class MapSubdivisionDataService extends AbstractResService {
    /**
     * 地图分表数据
     */
    private final Map<Integer, MapTemplateDataItem> mapTemplateDataItemMap = new HashMap<>();
    /**
     * 州Id->开放配置
     */
    private final Map<Integer, OpenRegionTemplate> openRegionConfigMap = new HashMap<>();
    /**
     * 附属建筑  主建筑配置id -> 附属建筑列表
     */
    private final Map<Integer, List<InitializationWildBuildingTemplate>> outbuilding = new HashMap<>();

    public MapSubdivisionDataService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 扫描所有的template, 构建出多map为key的数据
        Map<String, Map<Integer, IResTemplate>> templateData = getResHolder().getResTemplateDataStore();
        for (Map.Entry<String, Map<Integer, IResTemplate>> entry : templateData.entrySet()) {
            if (checkAndAddData(entry.getKey(), "RegionalSettingMap", entry.getValue(), RegionalAreaSettingTemplate.class)) {
                continue;
            }
            if (checkAndAddData(entry.getKey(), "FogBuildingMap", entry.getValue(), FogBuildingTemplate.class)) {
                continue;
            }
        }
        // 所有的都构建好后的check和关联性数据构建
        for (MapTemplateDataItem item : mapTemplateDataItemMap.values()) {
            item.afterBuildTemplateCheck(getResHolder());
        }
        // 州开放设定
        getResHolder().getListFromMap(OpenRegionTemplate.class).forEach(data -> openRegionConfigMap.put(data.getRegionId(), data));
        // 附属建筑
        getResHolder().getListFromMap(InitializationWildBuildingTemplate.class).forEach(data -> {
            getResHolder().getValueFromMap(MapBuildingTemplate.class, data.getTemplateId());
            outbuilding.computeIfAbsent(data.getBuildingID(), (k) -> new ArrayList<>()).add(data);
        });
    }

    private <T extends MapSubdivisionTemplate> boolean checkAndAddData(String key, String prefix, Map<Integer, IResTemplate> source, Class<T> clazz) throws ResourceException {
        if (!key.startsWith(prefix)) {
            return false;
        }
        String[] strings1 = key.split(prefix);
        String[] strings2 = strings1[1].split("Template");
        int dataKey = Integer.parseInt(strings2[0]);
        MapTemplateDataItem mapDataItem = mapTemplateDataItemMap.computeIfAbsent(dataKey, MapTemplateDataItem::new);
        mapDataItem.addTemplateData(getResHolder(), source, clazz);
        return true;
    }

    public MapTemplateDataItem getMapTemplateDataItem(int mapId) {
        return mapTemplateDataItemMap.get(mapId);
    }

    /**
     * 获取某个模板的一条记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @param id    索引
     * @return 返回模板的一条记录
     */
    @SuppressWarnings("unchecked")
    public <T extends MapSubdivisionTemplate> T getValueFromMap(Class<T> clazz, int mapId, int id) {
        if (id == 0) {
            String format = StringUtils.format("配置id不存在. detail->> mapId={} id:0 {}不存在", mapId, ClassNameCacheUtils.getSimpleName(clazz));
            WechatLog.error(format);
            throw new GeminiException(format);
        }
        MapTemplateDataItem mapDataItem = mapTemplateDataItemMap.get(mapId);
        if (mapDataItem == null) {
            String format = StringUtils.format("配置id不存在. detail->> mapId={} id:{} {}不存在", id, mapId, ClassNameCacheUtils.getSimpleName(clazz));
            WechatLog.error(format);
            throw new GeminiException(format);
        }
        return mapDataItem.getValueFromMap(clazz, id);
    }

    public List<Integer> getCrossBesiegeAngle(int mapId, int partId, int regionId) {
        Map<Integer, Map<Integer, List<Integer>>> crossBesiegeAngle = MapGridDataManager.getCrossBesiegeAngle(mapId);
        if (!crossBesiegeAngle.containsKey(partId)) {
            return Collections.emptyList();
        }
        Map<Integer, List<Integer>> map = crossBesiegeAngle.get(partId);
        if (!map.containsKey(regionId)) {
            return Collections.emptyList();
        }
        return Collections.unmodifiableList(map.get(regionId));
    }

    public boolean checkRegionOpen(int regionId) {
        if (!openRegionConfigMap.containsKey(regionId)) {
            return false;
        }
        return openRegionConfigMap.get(regionId).getIsOpen();
    }

    public List<InitializationWildBuildingTemplate> getOutBuildingTemplate(int buildTemplateId) {
        return outbuilding.get(buildTemplateId);
    }

    /**
     * 外圈州判断
     */
    public boolean isOutCircleRegion(int storyId, int regionId) {
        if (storyId == 0) {
            return regionId < 6;
        }
        return false;
    }

    public boolean isOutRegion2MiddleRegion(int storyId, RegionalAreaSettingTemplate template) {
        if (isOutCircleRegion(storyId, template.getLinkRegionList().get(0))) {
            return true;
        }
        return isOutCircleRegion(storyId, template.getLinkRegionList().get(1));
    }

    @Override

    public void checkValid() throws ResourceException {

    }
}

package com.yorha.common.utils.id;


import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class IdFactory {
    private static final Logger LOGGER = LogManager.getLogger(IdFactory.class);
    /**
     * 全区全服发号器   保留位使用0L
     * |--保留字-|--编码段编号--|---序列号---|
     * |--4bits-|---40bits---|---20bits---|
     */
    private static IIdFactory worker = null;
    /**
     * zone发号器  不会与全区全服发号器重复  保留位使用1L
     * |--保留字-|--Zone保留位--|---ZoneId位---|---序列号---|
     * |--4bits-|---4bits---|---13bits---|---43bits---|
     * 自增型  zone保留位使用0L
     */
    private static IIdFactory zoneWorker = null;
    /**
     * actor msg发号器 高32位与busId相关, 低32位atomic自增
     */
    private static IIdFactory actorMsgWorkder = null;
    /**
     * zone 自增循环易失发号器
     * [0 -> 2 ^ 63 - 1]
     */
    private static IIdFactory recycleIncreaseWorker = null;
    private static final String ZONE_WORKER_KEY = "id_factory_zone";

    private static class CNC extends BufferSegmentIdFactory {

        CNC(long reserve, int bufferSize, IIdFactory uf, long initValue) {
            super(reserve, bufferSize, uf, initValue);
        }

        @Override
        protected void onSegmentSwitch(BufferSegmentIdFactory.Segment segment) {
            super.onSegmentSwitch(segment);
            MonitorUnit.ID_FACTORY_REMOVE_SEGMENT_TOTAL.labels(ServerContext.getBusId()).inc();
        }

        @Override
        protected void onSegmentAdd(Segment segment) {
            super.onSegmentAdd(segment);
        }
    }

    public static synchronized void run(final String dbKey, final int buffSize) {
        if (IdFactory.actorMsgWorkder != null) {
            throw new GeminiException("don't run IdFactory again");
        }

        // dir不需要全局唯一id
        if (!ServerContext.isDirServer()) {
            final IIdFactory segmentFactory = getSegmentFactory(dbKey);
            final BufferSegmentIdFactory bufferSegmentIdFactory = new CNC(0L, buffSize, segmentFactory, 0L);
            bufferSegmentIdFactory.nextId("start_up_db_test_generate");
            IdFactory.worker = bufferSegmentIdFactory;
        }

        if (ServerContext.isZoneServer()) {
            int zoneId = ServerContext.getZoneId();
            IdFactory.zoneWorker = getZoneIncreaseIdFactory(zoneId);
        }

        IdFactory.actorMsgWorkder = new ActorMsgIdFactory(ServerContext.getServerInfo(), RandomUtils.nextInt(Integer.MIN_VALUE, Integer.MAX_VALUE), SystemClock.nowNative());
        IdFactory.recycleIncreaseWorker = new RecycleIncreaseIdFactory();
    }

    private static IIdFactory getZoneIncreaseIdFactory(final int zoneId) {
        return new ZoneIncreaseIdFactory(1L, 0L, zoneId, ZONE_WORKER_KEY);
    }


    private static IIdFactory getSegmentFactory(final String key) {
        return new DbIdFactory(key);
    }

    public static synchronized void shutdown() {
        if (IdFactory.actorMsgWorkder == null) {
            LOGGER.error("IdFactory not run!");
            return;
        }
        IdFactory.worker = null;
        IdFactory.zoneWorker = null;
        IdFactory.actorMsgWorkder = null;
        IdFactory.recycleIncreaseWorker = null;
    }

    /**
     * 用于持久化对象的ID生成
     */
    public static long nextId(String reason) {
        if (worker == null) {
            throw new GeminiException("don't run global IdFactory");
        }
        final long nextId = worker.nextId(reason);
        MonitorUnit.ID_FACTORY_NEXT_ID_REASON_TOTAL.labels(ServerContext.getBusId(), "nextId", reason).inc();
        return nextId;
    }

    /**
     * 只能zone进程使用!!!!
     */
    public static long nextIdInZone(String reason) {
        if (zoneWorker == null) {
            throw new GeminiException("don't run zone IdFactory");
        }
        final long zoneId = zoneWorker.nextId(reason);
        MonitorUnit.ID_FACTORY_NEXT_ID_REASON_TOTAL.labels(ServerContext.getBusId(), "nextIdInZone", reason).inc();
        return zoneId;
    }

    /**
     * 只能ActorMsgId用!
     */
    public static long nextIdForActorMsg(String reason) {
        if (actorMsgWorkder == null) {
            throw new GeminiException("don't run actor msg IdFactory");
        }
        MonitorUnit.ID_FACTORY_NEXT_ID_REASON_TOTAL.labels(ServerContext.getBusId(), "nextIdForActorMsg", reason).inc();
        return actorMsgWorkder.nextId(reason);
    }

    public static long nextIdForRecycleIncrease(String reason) {
        if (recycleIncreaseWorker == null) {
            throw new GeminiException("don't run recycle increase IdFactory");
        }
        MonitorUnit.ID_FACTORY_NEXT_ID_REASON_TOTAL.labels(ServerContext.getBusId(), "nextIdForRecycleIncrease", reason).inc();
        return recycleIncreaseWorker.nextId(reason);
    }

    public static synchronized void fakeWorkerRun() {
        if (IdFactory.worker != null) {
            throw new GeminiException("don't run IdFactory again");
        }
        IdFactory.worker = new FakeIdFactory();
        IdFactory.actorMsgWorkder = new ActorMsgIdFactory(ServerContext.getServerInfo(), RandomUtils.nextInt(Integer.MIN_VALUE, Integer.MAX_VALUE), SystemClock.nowNative());
    }
}

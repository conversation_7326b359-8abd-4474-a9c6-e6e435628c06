package com.yorha.common.concurrent;

import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadFactory;

/**
 * 守护的线程工厂
 *
 * <AUTHOR>
 */
public class DaemonThreadFactory implements ThreadFactory {
    private final String name;
    final ThreadGroup group;

    @Override
    public Thread newThread(@NotNull Runnable runnable) {
        Thread thread = new Thread(group, runnable, name);
        thread.setDaemon(true);
        return thread;
    }


    public DaemonThreadFactory(String name) {
        this.name = name;
        this.group = Thread.currentThread().getThreadGroup();
    }
}

package com.yorha.common.resource.datatype;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class IntPairArray extends ArrayList<IntPairType> {
    private static final String COMMA = ",";

    public IntPairArray(List collect) {
        super(collect);
    }

    public IntPairArray() {

    }

    public static IntPairArray valueOf(String s) {
        IntPairArray ret = new IntPairArray();
        Arrays.stream(s.split(COMMA)).map(e -> ret.add(IntPairType.valueOf(e))).toList();
        return ret;
    }
}

package com.yorha.common.actor;

import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

/**
 * 用处
 * 1、解耦FreqLimitCaller
 * 2、解决部分ownerActor明确不是SceneActor的情况下，强转后加定时器
 */
public interface IActorWithTimer {

    @Nullable
    ActorTimer addTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit);

    @Nullable
    ActorTimer addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit);
}

package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.result.IncreaseResult;

/**
 * 基于Tcaplus的发号器逻辑。
 *
 * <AUTHOR>
 */
public class TcaplusIncrease<T extends Message.Builder> extends TcaplusOperation<T, IncreaseOption, IncreaseResult<T>> {
    public TcaplusIncrease(Client client, T t, IncreaseOption increaseOption) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, increaseOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_INCREASE_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        // 设置配置
        request.setResultFlag(this.getOption().getResultFlag());

        final Record record = request.addRecord();
        this.setRequestKeys(getReq(), record);
        this.setRequestValues(getReq(), record);
        // 设置版本号
        record.setVersion(this.getOption().getVersion());

    }

    @Override
    protected IncreaseResult<T> buildResult(Response response) {
        final IncreaseResult<T> result = new IncreaseResult<>();
        result.code = TcaplusErrorCode.forNumber(response.getResult());

        Record record;
        // Tcaplus SDK 所有proxy宕机保护
        try {
            record = response.fetchRecord();
        } catch (NullPointerException e) {
            record = null;
        }
        if (record == null) {
            return result;
        }
        result.value = buildDefaultValue(this.getReq());
        result.version = record.getVersion();
        this.readFromResponseValues(record, result.value);
        return result;
    }
}

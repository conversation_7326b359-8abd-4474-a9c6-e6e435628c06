package com.yorha.common.actor;

/**
 * 解耦actor和其他模块（定时器、属性系统等）
 *
 * <AUTHOR>
 */
public interface IActorRef {
    /**
     * 引用对应Actor的角色。
     *
     * @return Actor Role。
     */
    String getActorRole();

    /**
     * 引用对应Actor的id。
     *
     * @return id。
     */
    String getActorId();

    /**
     * ref对应的bus id。
     * 1. null表示ref无确定地址，需要通过服务发现获取。
     * 2. ref对应actor的bus id值。
     *
     * @return null or 具体值。
     */
    String getBusId();

    /**
     * 区服id
     *
     * @return zone id
     */
    int getZoneId();

    /**
     * 空的Actor 引用。
     */
    IActorRef NOBODY = new IActorRef() {
        @Override
        public String getActorRole() {
            return "NoBody";
        }

        @Override
        public String getActorId() {
            return "NoBody";
        }

        @Override
        public String getBusId() {
            return "NoBody";
        }

        @Override
        public int getZoneId() {
            return -1;
        }

        @Override
        public String toString() {
            return "NoBody";
        }
    };
}

package com.yorha.common.resource.resservice.scene;

import com.google.common.collect.Maps;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.SceneObjectEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.WorldLayerTemplate;
import res.template.WorldObjectTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 无极缩放相关配置管理
 *
 * <AUTHOR>
 */
public class WorldLayerService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(WorldLayerService.class);
    private final Map<SceneObjectEnum, Map<Integer, Integer>> objectRegionLayer = Maps.newEnumMap(SceneObjectEnum.class);

    public WorldLayerService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (WorldObjectTemplate template : getResHolder().getListFromMap(WorldObjectTemplate.class)) {
            if (template.getObjType().getNumber() != template.getId()) {
                throw new ResourceException(StringUtils.format("无极缩放物体层级表id与枚举对不上 {}", template.getId()));
            }
            if (template.getRegionLayerPairList() != null && template.getRegionLayerPairList().size() != 0) {
                objectRegionLayer.put(template.getObjType(), new HashMap<>());
                for (IntPairType pair : template.getRegionLayerPairList()) {
                    objectRegionLayer.get(template.getObjType()).put(pair.getKey(), pair.getValue());
                }
            }
        }
        LOGGER.info("objectRegionLayer {}", objectRegionLayer);
    }

    /**
     * 拿无极缩放最高层  非分州版 如果是分州读取的 那返回0
     */
    public int getObjectLayer(SceneObjectEnum type) {
        if (objectRegionLayer.containsKey(type)) {
            return 0;
        }
        return getResHolder().getValueFromMap(WorldObjectTemplate.class, type.getNumber()).getLayer();
    }

    public int getObjectLayer(SceneObjectEnum type, int regionId) {
        Map<Integer, Integer> map = objectRegionLayer.get(type);
        if (map != null && map.containsKey(regionId)) {
            return map.get(regionId);
        }
        return getResHolder().getValueFromMap(WorldObjectTemplate.class, type.getNumber()).getLayer();
    }

    @Override
    public void checkValid() throws ResourceException {
        for (WorldObjectTemplate d : getResHolder().getListFromMap(WorldObjectTemplate.class)) {
            getResHolder().checkValueFromMap(WorldLayerTemplate.class, d.getLayer(), null);
        }
    }
}

package com.yorha.common.actor.msg;

/**
 * 用于传递GeminiException
 */
public class ActorExceptionMsg implements IActorMsg {

    private final Exception err;

    public ActorExceptionMsg(Exception exception) {
        this.err = exception;
    }

    @Override
    public boolean canRemote() {
        return true;
    }

    public Exception getErr() {
        return err;
    }

    @Override
    public String profName() {
        return "ActorExceptionMsg";
    }

    @Override
    public String toString() {
        return "ActorExceptionMsg{" +
                "err=" + this.err +
                '}';
    }
}

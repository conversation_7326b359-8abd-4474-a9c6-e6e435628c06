package com.yorha.common.prop;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.Entity;
import com.yorha.proto.EntityAttrOuterClass;

/**
 * prop 和 PBMsg 的互相转化，代码理论上都可以通过代码生成自动完成
 *
 * @param <P>         prop类型
 * @param <PBBuilder> P对应的PB.Builder类型
 */
public abstract class PropNtfTransfer<P extends AbstractPropNode, PBBuilder extends GeneratedMessageV3.Builder<?>> {

    /**
     * 填充 entityAttr，其实这里可以通过泛型 P 自动做掉
     */
    public abstract void fillEntityAttr(EntityAttrOuterClass.EntityAttr.Builder entityAttr, PBBuilder pbBuilder);

    /**
     * 手动实现一下 prop.copyChangeTo
     */
    public abstract int copyChange(P prop, PBBuilder changeCollector);

    /**
     * 手动实现一下 prop.copyTo
     */
    public abstract void copyFull(P prop, PBBuilder changeCollector);

    protected void fillModEntityNtf(Entity.EntityNtfMsg.Builder builder, EntityAttrOuterClass.EntityAttr.Builder entityAttr) {
        builder.addModEntities(entityAttr);
    }

    protected void fillNewEntityNtf(Entity.EntityNtfMsg.Builder builder, EntityAttrOuterClass.EntityAttr.Builder entityAttr) {
        builder.addNewEntities(entityAttr);
    }

    public Entity.EntityNtfMsg buildNewEntityNtf(PBBuilder pbBuilder) {
        final EntityAttrOuterClass.EntityAttr.Builder entityAttr = EntityAttrOuterClass.EntityAttr.newBuilder();
        fillEntityAttr(entityAttr, pbBuilder);
        Entity.EntityNtfMsg.Builder ntf = Entity.EntityNtfMsg.newBuilder();
        fillNewEntityNtf(ntf, entityAttr);
        return ntf.build();
    }

    public Entity.EntityNtfMsg buildModEntityNtf(PBBuilder pbBuilder) {
        final EntityAttrOuterClass.EntityAttr.Builder entityAttr = EntityAttrOuterClass.EntityAttr.newBuilder();
        fillEntityAttr(entityAttr, pbBuilder);
        Entity.EntityNtfMsg.Builder ntf = Entity.EntityNtfMsg.newBuilder();
        fillModEntityNtf(ntf, entityAttr);
        return ntf.build();
    }

}
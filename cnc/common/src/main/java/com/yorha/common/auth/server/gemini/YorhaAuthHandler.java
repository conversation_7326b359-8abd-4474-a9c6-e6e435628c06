package com.yorha.common.auth.server.gemini;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.yorha.common.constant.LogKeyConstants;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.PlatformClient;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * yorha账号鉴权
 *
 * <AUTHOR>
 */
public class YorhaAuthHandler {

    //毫秒
    public static final int TIMEOUT = 16000;
    private static final Logger LOGGER = LogManager.getLogger(YorhaAuthHandler.class);
    private static PlatformClient client;

    private static volatile YorhaAuthHandler instance;

    public static YorhaAuthHandler getInstance() {
        if (instance == null) {
            synchronized (YorhaAuthHandler.class) {
                if (instance == null) {
                    instance = new YorhaAuthHandler();
                }
            }
        }
        return instance;
    }

    private YorhaAuthHandler() {
        client = new YorhaPlatformClient(TIMEOUT);
        final String domain = client.getHostName();
        LOGGER.info("init yorha auth with domain:{}", domain);
    }

    private static class YorhaPlatformClient extends PlatformClient {

        YorhaPlatformClient(int timeout) {
            super(timeout);
        }

        @Override
        public String getHostName() {
            return ClusterConfigUtils.getWorldConfig().getStringItem("auth_yorha_domain");
        }
    }

    /**
     * 通过code换取授权access_token和openid
     */
    public Pair<String, String> getAccessToken(String code) {
        Map<String, Object> params = new HashMap<>();
        final String appId = ClusterConfigUtils.getWorldConfig().getStringItem("auth_yorha_app_id");
        final String secret = ClusterConfigUtils.getWorldConfig().getStringItem("auth_yorha_app_secret");
        params.put("appid", appId);
        params.put("secret", secret);
        params.put("code", code);
        params.put("grant_type", "authorization_code");
        JsonObject result = client.get4https("/oauth2/access_token.do", params);

        String openId = null;
        String accessToken = null;

        JsonElement openIdJson = result.get("openid");
        if (openIdJson != null) {
            openId = openIdJson.getAsString();
        }

        JsonElement accessTokenJson = result.get("access_token");
        if (accessTokenJson != null) {
            accessToken = accessTokenJson.getAsString();
        }

        if (openId == null || accessToken == null) {
            LOGGER.warn("getAccessToken code={} but openId={} accessToken={}", code, openId, accessToken);
            return null;
        }

        return Pair.of(openId, accessToken);
    }

    /**
     * 拉取用户信息
     * 能够拉到user的真正工作姓名
     * access_token会失效，目前先不使用了
     */
    public String getUserInfo(String accessToken, String openId) {
        Map<String, Object> params = new HashMap<>();
        params.put("access_token", accessToken);
        params.put("openid", openId);
        JsonObject result = client.get4https("/oauth2/userinfo.do", params);
        JsonElement element = result.get("nickname");
        if (element != null) {
            return element.getAsString();
        } else {
            WechatLog.error("get userinfo failed accessToken: {},openId={}，result={}", accessToken, openId, result);
            return null;
        }
    }

    /**
     * 用户同意授权，获取code（内部接口,临时使用）
     * 通过timi账号名字获取一个code，理论上还需要带上密码才好
     * 更理论上，这个操作应该在客户端，后台直接拿到code去问gemini鉴权服才对
     */
    public String getCode(String accountName) {
        Map<String, Object> params = new HashMap<>();
        final String appId = ClusterConfigUtils.getWorldConfig().getStringItem("auth_yorha_app_id");
        final String secret = ClusterConfigUtils.getWorldConfig().getStringItem("auth_yorha_app_secret");
        params.put("appid", appId);
        params.put("secret", secret);
        params.put("account_name", accountName);
        params.put("grant_type", "authorization_code");
        JsonObject result = client.get4https("/oauth2/inner/authorize.do", params);
        if (result != null) {
            JsonElement element = result.get("code");
            if (element != null) {
                return element.getAsString();
            } else {
                LOGGER.error("{} atPlayer yorha. exchange yorha code failed accountName={},result={}", LogKeyConstants.GAME_PLAYER_LOGIN, accountName, result);
            }
        }
        return null;
    }
}

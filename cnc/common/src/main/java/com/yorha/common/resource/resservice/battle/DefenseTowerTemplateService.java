package com.yorha.common.resource.resservice.battle;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.Pair;
import res.template.DefenseTowerStatusTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 防御塔相关
 *
 * <AUTHOR>
 */
public class DefenseTowerTemplateService extends AbstractResService {
    private final Map<Pair<Integer, Integer>, DefenseTowerStatusTemplate> defenseTowerStatusMap = new HashMap<>();


    public DefenseTowerTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        Collection<DefenseTowerStatusTemplate> listFromMap = getResHolder().getListFromMap(DefenseTowerStatusTemplate.class);
        for (DefenseTowerStatusTemplate t : listFromMap) {
            IntPairType pair = t.getDurabilityRangePair();
            defenseTowerStatusMap.put(Pair.of(pair.getKey(), pair.getValue()), t);
        }
    }

    @Override
    public void checkValid() throws ResourceException {

    }

    public DefenseTowerStatusTemplate getDefenseTowerStatusTemplate(int durabilityRatio) {
        for (Map.Entry<Pair<Integer, Integer>, DefenseTowerStatusTemplate> entry : defenseTowerStatusMap.entrySet()) {
            if (entry.getKey().getFirst() >= durabilityRatio && durabilityRatio > entry.getKey().getSecond()) {
                return entry.getValue();
            }
        }
        return defenseTowerStatusMap.get(Pair.of(0, 0));
    }

}

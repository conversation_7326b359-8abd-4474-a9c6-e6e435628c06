package com.yorha.common.enums;


/**
 * 登陆结果
 *
 * <AUTHOR>
 */

public enum LoginResult {
    /**
     *  登陆成功
     */
    SUCCESS("登陆成功"),
    /**
     *  参数错误
     */
    PARAM_INVALID("参数错误"),
    /**
     *  服务器已关闭
     */
    SERVER_IS_CLOSED("服务器已关闭"),
    /**
     *  设备已封禁
     */
    DEVICE_IS_BLOCKED("设备已封禁"),
    /**
     *  IP已封禁
     */
    IP_IS_BLOCKED("IP已封禁"),
    /**
     *  客户端版本错误
     */
    CLIENT_VERSION_INVALID("客户端版本错误"),
    /**
     *  注册已达上限
     */
    REGISTER_LIMIT("注册已达上限"),
    /**
     *  在线已达上限
     */
    ONLINE_LIMIT("在线已达上限");

    private final String result;

    LoginResult(String result) {
        this.result = result;
    }

    public String getResult() {
        return result;
    }
}

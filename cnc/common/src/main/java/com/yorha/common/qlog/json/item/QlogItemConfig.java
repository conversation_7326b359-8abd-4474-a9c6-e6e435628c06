package com.yorha.common.qlog.json.item;

import com.yorha.common.asset.AssetPackage;
import com.yorha.common.utils.json.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Qlog 用到的道具结构
 */
public class QlogItemConfig {
    private static final Logger LOGGER = LogManager.getLogger(QlogItemConfig.class);

    private int itemId;
    private int itemCount;

    public QlogItemConfig(int itemId, int itemCount) {
        this.itemId = itemId;
        this.itemCount = itemCount;
    }

    public int getItemId() {
        return itemId;
    }

    public int getItemCount() {
        return itemCount;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public void setItemCount(int itemCount) {
        this.itemCount = itemCount;
    }

    /**
     * 获取qlog道具字符串
     *
     * @param itemId    道具id
     * @param itemCount 道具数量
     * @return qlog道具字符串
     */
    public static String getQlogItemConfigStr(int itemId, int itemCount) {
        return JsonUtils.toJsonString(new QlogItemConfig(itemId, itemCount));
    }

    public static String fromAssetPackage(AssetPackage assetPackage) {
        List<QlogItemConfig> qlogItemConfigs = new ArrayList<>();
        assetPackage.forEachItems(itemDesc -> qlogItemConfigs.add(new QlogItemConfig(itemDesc.getId(), (int) itemDesc.getAmount())));
        return JsonUtils.toJsonString(qlogItemConfigs);
    }

    /**
     * 获取qlog道具字符串
     *
     * @param itemIds    道具id列表
     * @param itemCounts 道具数量列表
     * @return qlog道具字符串
     */
    public static String getQlogItemConfigStr(List<Integer> itemIds, List<Integer> itemCounts) {
        if (itemIds == null || itemCounts == null
                || itemIds.size() != itemCounts.size()
                || itemIds.size() == 0) {
            LOGGER.error("itemIds or itemCounts is null or size not equal or size is 0.");
            return "";
        }

        int size = itemIds.size();
        // 只有一个不需要构建list
        if (size == 1) {
            LOGGER.warn("itemIds size is 1, use getQlogItemConfigStr(int itemId, int itemCount) instead.");
            return getQlogItemConfigStr(itemIds.get(0), itemCounts.get(0));
        }
        // 构建list返回list的字符串
        List<QlogItemConfig> qlogItemConfigs = new ArrayList<>(size);
        for (int i = 0; i < itemIds.size(); ++i) {
            qlogItemConfigs.add(new QlogItemConfig(itemIds.get(i), itemCounts.get(i)));
        }
        return JsonUtils.toJsonString(qlogItemConfigs);
    }
}

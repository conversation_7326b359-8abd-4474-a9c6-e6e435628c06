package com.yorha.common.actorservice;

import com.google.common.collect.Maps;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actor.msg.ActorExceptionMsg;
import com.yorha.common.actor.msg.FastFailActorRunnable;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actorservice.msg.ActorMsgUtil;
import com.yorha.common.dbactor.DbMgrService;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * actor消息投递处，进程单例
 *
 * <AUTHOR>
 */
public class ActorMsgSystem {
    private static final Logger LOGGER = LogManager.getLogger(ActorMsgSystem.class);

    private static final Map<Long, ActorCallContext> ACTOR_CALL_CONTEXT_MAP = Maps.newConcurrentMap();

    /**
     * 投递消息到目标Actor的引用上。
     *
     * @param envelope 消息包。
     */
    public static void dispatchMsg(ActorMsgEnvelope envelope) {
        final IActorRef sender = envelope.getSender();
        ActorMsgUtil.debugLogSendMsg(sender, LOGGER, envelope, envelope.getReceiver());
        final IActorRef target = envelope.getReceiver();
        if (target == null) {
            LOGGER.error("actor fast fail! null target, envelope={}", envelope);
            answerFailFast(envelope, ErrorCode.ACTOR_NOT_EXISTS);
            return;
        }
        // 获取目标bus id
        final String busId = target.getBusId();
        if (busId == null) {
            LOGGER.error("actor fast fail! null bus! target {} envelope {}", target, envelope);
            answerFailFast(envelope, ErrorCode.ACTOR_NOT_EXISTS);
            return;
        }
        if (ServerContext.isSameNode(busId)) {
            // 本进程通信
            dispatchMsgLocal(envelope, target);
            return;
        }
        // 远程通信
        dispatchMsgRemote(envelope, busId);
    }

    /**
     * 投递到本进程的actor消息里
     */
    private static void dispatchMsgLocal(ActorMsgEnvelope envelope, IActorRef actorRef) {
        final String actorRole = actorRef.getActorRole();
        // call的消息不能直接给到对端线程执行complete，就地complete
        if (envelope.isCallAnswer()) {
            // 通过消息唯一id，找到对应的ActorCallContext
            final long msgSeqId = envelope.getMsgSeqId();
            final ActorCallContext actorCallContext = ActorMsgSystem.getActorCallContext(msgSeqId);
            if (actorCallContext == null) {
                // 可能发起方actor已经超时timeout去除了callContext
                LOGGER.error("actor answer context not found! callContext ref={} envelop={}!", actorRef, envelope);
                return;
            }
            ActorMsgUtil.debugLogRecvMsg(actorCallContext.getSender(), LOGGER, envelope);
            // 唤醒
            final IActorMsg payload = envelope.getPayload();
            if (payload instanceof ActorExceptionMsg) {
                actorCallContext.tryCompleteWithException(((ActorExceptionMsg) payload).getErr());
            } else {
                actorCallContext.tryCompleteWithMsg(payload);
            }
            return;
        }
        if (envelope.getReceiver() instanceof DbMgrService) {
            ((DbMgrService) envelope.getReceiver()).dispatchProtoMsg(envelope);
            return;
        }

        final ActorSystem actorSystem = ServerContext.getActorSystem();
        final ActorMetaData metaData = actorSystem.getActorMetaDataMgr().getActorMetaData(actorRole);
        // 不具备拉取能力的消息，直接只获取。
        IActorMailbox mb = actorSystem.getRegistryValue().getMailbox(actorRef);
        if (mb == null && envelope.isTimer()) {
            // 定时器消息不能拉起任何actor
            LOGGER.warn("mailBox is null, so discard timer msg, {} to {}", envelope, actorRef);
            return;
        }
        var shouldCreateMb = envelope.isCreate() || !metaData.needCreateMsg();
        if (mb == null && shouldCreateMb) {
            // 具备拉起能力的消息，add一个mailbox
            mb = actorSystem.getRegistryValue().computeMailboxIfNeeded(actorRef);
            if (mb == null) {
                final int mbCnt = actorSystem.getRegistryValue().getMailboxSizeByActorRole(actorRole);
                LOGGER.error("actor create mailbox fail! ref {}! mbCnt={}! isShutdown={}!", actorRef, mbCnt, actorSystem.isShutdown());
            }
        }
        if (mb == null) {
            LOGGER.warn("actor fast fail! mailbox null! ref={} envelop={}!", actorRef, envelope);
            answerFailFast(envelope, ErrorCode.ACTOR_NOT_EXISTS);
            return;
        }
        final boolean success = mb.sendMsg(envelope);
        if (!success) {
            IActorMsg payLoad = envelope.getPayload();
            if (payLoad instanceof FastFailActorRunnable) {
                WechatLog.error("actor fast fail! queue is full! ref={} envelop={}!", actorRef, envelope);
                throw new GeminiException("fail to send Msg");
            }
        }
    }

    /**
     * 跨进程通信
     */
    private static void dispatchMsgRemote(ActorMsgEnvelope envelope, final String busId) {
        final IActorMsg payload = envelope.getPayload();
        // 跨进程通信
        if (!payload.canRemote()) {
            // 非跨进程信息，无法序列化
            LOGGER.warn("send {} to {}! msg cannot support remote!", envelope, busId);
            answerFailFast(envelope, ErrorCode.MSG_CAN_NOT_SEND_REMOTE);
            return;
        }
        ServerContext.getActorSystem().getNetServer().sendMsg(busId, envelope);
    }

    /**
     * 如果actor不存在 or 节点不存在，但这又是个ask、call需要返回的消息，则立刻返回一个对象不存在的异常消息
     *
     * @param envelope 需要回应的envelop。
     */
    private static void answerFailFast(final ActorMsgEnvelope envelope, ErrorCode code) {
        if (envelope.isAnswer()) {
            return;
        }
        if (envelope.isByTell()) {
            return;
        }
        final ActorMsgEnvelope answerEnvelope =
                ActorMsgEnvelope.createFromAnswerException(envelope, new GeminiException(code.getDesc(), null, code.getCodeId()));
        final IActorRef sender = envelope.getSender();
        answerEnvelope.setReceiver(sender);
        ActorMsgSystem.dispatchMsg(answerEnvelope);
    }

    public static void dispatchBroadcastMsg(Map<String, List<IActorRef>> broadcastBook, IActorRef sender, GeneratedMessageV3 msg) {
        // 避免for循环内不停计算
        final int typeFromMsg = SsMsgTypes.getTypeFromMsg(msg);
        for (Map.Entry<String, List<IActorRef>> entry : broadcastBook.entrySet()) {
            String busId = entry.getKey();
            List<IActorRef> receivers = entry.getValue();
            if (ServerContext.isSameNode(busId)) {
                final TypedMsg typedMsg = new TypedMsg(typeFromMsg, msg);
                for (final IActorRef ref : receivers) {
                    ActorSendMsgUtils.send(ref, sender, typedMsg);
                }
                continue;
            }
            final ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromTell(sender, msg);
            envelope.setReceivers(receivers);
            ActorMsgSystem.dispatchMsgRemote(envelope, busId);
        }
    }

    public static void putActorCallContext(ActorCallContext callContext) {
        if (ACTOR_CALL_CONTEXT_MAP.containsKey(callContext.getMsgSeqId())) {
            throw new GeminiException("repeat putActorCallContext {}", callContext);
        }
        ACTOR_CALL_CONTEXT_MAP.put(callContext.getMsgSeqId(), callContext);
    }

    @Nullable
    public static ActorCallContext removeActorCallContext(long uniqueId) {
        return ACTOR_CALL_CONTEXT_MAP.remove(uniqueId);
    }

    public static ActorCallContext getActorCallContext(long uniqueId) {
        return ACTOR_CALL_CONTEXT_MAP.get(uniqueId);
    }
}

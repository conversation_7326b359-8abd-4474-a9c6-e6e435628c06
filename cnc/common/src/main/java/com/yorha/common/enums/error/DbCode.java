package com.yorha.common.enums.error;

import com.yorha.common.exception.IErrorCode;
import com.yorha.proto.Core;

public enum DbCode implements IErrorCode {
    /**
     * 框架层
     */
    DB_OK(0, "成功"),
    DB_NEED_KEY(1, "缺少主键"),
    DB_NOT_INDEX(2, "缺少索引"),
    DB_UNKNOWN_FIELD(3, "未知字段");
    /**
     * 饿汉模式，提前构造，避免多次构造
     */
    private final Core.Code code;
    /**
     * 描述
     */
    private final String desc;

    DbCode(int id, String desc) {
        Core.Code.Builder builder = Core.Code.newBuilder();
        builder.setId(id);
        this.desc = desc;
        this.code = builder.build();
    }

    @Override
    public int getCodeId() {
        return 0;
    }

    /**
     * 获取错误码对象
     *
     * @return 错误码对象
     */
    public Core.Code getCode() {
        return this.code;
    }

    /**
     * 获取描述
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 判断是否为正确情况
     *
     * @param code 错误码
     * @return true or  false
     */
    public static boolean isOK(Core.Code code) {
        return code.getId() == 0;
    }

    public static boolean isOK(int codeId) {
        return codeId == 0;
    }
}

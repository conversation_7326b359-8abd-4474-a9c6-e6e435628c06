package com.yorha.common.resource.resservice.kingdom;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 王国配置数据管理服务
 *
 * <AUTHOR>
 */
public class KingdomTemplateResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(KingdomTemplateResService.class);

    private static final Integer KING_RANK = 1;

    private int kingOfficeId;

    public KingdomTemplateResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        loadKingdomTitle();
    }

    @Override
    public void checkValid() throws ResourceException {
        checkKingdomBuff();
        checkKingdomSkill();
        checkKingdomGift();
    }

    private void loadKingdomTitle() {
        for (KingdomTitleTemplate template : getResHolder().getListFromMap(KingdomTitleTemplate.class)) {
            if (template.getRank() == KING_RANK) {
                kingOfficeId = template.getId();
                break;
            }
        }
    }

    // ----------------------------- check functions -------------------------------------- //

    private void checkKingdomBuff() {

    }

    private void checkKingdomSkill() throws ResourceException {
        for (KingdomSkillTemplate template : getResHolder().getListFromMap(KingdomSkillTemplate.class)) {
            int effectTime = template.getEffectTime();
            if (effectTime < 0) {
                throw new ResourceException("王国管理表 kingdom_skill id={} 技能生效时间为负数:{}", template.getId(), effectTime);
            }
            int cd = template.getCd();
            if (cd < 0) {
                throw new ResourceException("王国管理表 kingdom_skill id={} 技能CD时间为负数:{}", template.getId(), cd);
            }
            for (Integer buffId : template.getBuffList()) {
                if (getResHolder().findValueFromMap(BuffTemplate.class, buffId) == null) {
                    throw new ResourceException("王国管理表 kingdom_skill id={} 内城增益buff不存在={}", template.getId(), buffId);
                }
            }
            for (Integer buffId : template.getBattlebuffList()) {
                if (getResHolder().findValueFromMap(BattleBuffTemplate.class, buffId) == null) {
                    throw new ResourceException("王国管理表 kingdom_skill id={} 战斗增益buff不存在={}", template.getId(), buffId);
                }
            }
        }
    }

    private void checkKingdomGift() throws ResourceException {
        for (KingdomGiftTemplate template : getResHolder().getListFromMap(KingdomGiftTemplate.class)) {
            int mailId = template.getMail();
            if (getResHolder().findValueFromMap(MailTemplate.class, mailId) == null) {
                throw new ResourceException("王国管理表 kingdom_gift id={} 未知邮件ID:{}", template.getId(), mailId);
            }
            int buffTime = template.getBuffTime();
            if (buffTime <= 0) {
                throw new ResourceException("王国管理表 kingdom_gift id={} 王国恩赐发放次数上限为非正数:{}", template.getId(), buffTime);
            }

            for (IntPairType itemPair : template.getItemPairList()) {
                int itemId = itemPair.getKey();
                int itemNum = itemPair.getValue();
                if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                    throw new ResourceException("王国管理表 kingdom_gift id={} 王国恩赐道具奖励存在未知ID:{}", template.getId(), itemId);
                }
                if (itemNum <= 0) {
                    throw new ResourceException("王国管理表 kingdom_gift id={} 王国恩赐道具奖励存在非正数数量:{}", template.getId(), itemNum);
                }
            }
        }
    }
    // ------------------------------ interface ----------------------------------------- //

    public List<Integer> getBuffByOfficeId(int officeId) {
        KingdomTitleTemplate template = getResHolder().findValueFromMap(KingdomTitleTemplate.class, officeId);
        List<Integer> retList = new ArrayList<>();
        if (null == template) {
            LOGGER.error("can not find kingdom title template by officeId {}", officeId);
            return retList;
        }
        return template.getBuffIDList();
    }

    public int getTotalCanGiveNum(int giftId) {
        KingdomGiftTemplate template = getResHolder().findValueFromMap(KingdomGiftTemplate.class, giftId);
        if (null == template) {
            LOGGER.error("can not find kingdom title template by giftId {}", giftId);
            return 0;
        }
        return template.getBuffTime();
    }

    public KingdomGiftTemplate getGiftMailId(int giftId) {
        KingdomGiftTemplate template = getResHolder().findValueFromMap(KingdomGiftTemplate.class, giftId);
        if (null == template) {
            LOGGER.error("can not find kingdom title template by giftId {}", giftId);
            return null;
        }
        return template;
    }

    public KingdomSkillTemplate getKingdomSkillById(int skillId) {
        KingdomSkillTemplate template = getResHolder().findValueFromMap(KingdomSkillTemplate.class, skillId);
        if (null == template) {
            LOGGER.error("can not find kingdom skill template by skillId {}", skillId);
        }
        return template;
    }

    public KingdomTitleTemplate getTitleTemplate(int officeId) throws GeminiException {
        KingdomTitleTemplate template = getResHolder().findValueFromMap(KingdomTitleTemplate.class, officeId);
        if (null == template) {
            LOGGER.error("can not find kingdom title template by officeId {}", officeId);
            throw new GeminiException(ErrorCode.KINGDOM_CONFIG_NOT_EXIST);
        }
        return template;
    }

    public KingdomBuffTemplate getBuffTemplate(int buffId) throws GeminiException {
        KingdomBuffTemplate template = getResHolder().findValueFromMap(KingdomBuffTemplate.class, buffId);
        if (null == template) {
            LOGGER.error("can not find kingdom buff template by buffId {}", buffId);
            throw new GeminiException(ErrorCode.KINGDOM_CONFIG_NOT_EXIST);
        }
        return template;
    }

    public KingdomSkillTemplate getSkillTemplate(int skillId) throws GeminiException {
        KingdomSkillTemplate template = getResHolder().findValueFromMap(KingdomSkillTemplate.class, skillId);
        if (null == template) {
            LOGGER.error("can not find kingdom buff template by buffId {}", skillId);
            throw new GeminiException(ErrorCode.KINGDOM_CONFIG_NOT_EXIST);
        }
        return template;
    }

    public List<Integer> getAllGiftIds() {
        List<Integer> giftIds = new ArrayList<>();
        for (KingdomGiftTemplate giftTemplate : getResHolder().getListFromMap(KingdomGiftTemplate.class)) {
            giftIds.add(giftTemplate.getId());
        }
        return giftIds;
    }

    public ConstKingdomTemplate getConstTemplate() {
        return getResHolder().getConstTemplate(ConstKingdomTemplate.class);
    }

    public int getGainTaxDecRate() {
        return getConstTemplate().getKingdomSkillTaxPara2();
    }

    public long getMaxTaxNum() {
        return getConstTemplate().getKingdomSkillTaxPara3();
    }

    public int getKingOfficeId() {
        return kingOfficeId;
    }

    public boolean isKing(int officeId) throws GeminiException {
        if (officeId <= 0) {
            return false;
        }
        return getTitleTemplate(officeId).getRank() == KING_RANK;
    }

    public boolean isCanAppointRank(int operatorOffice, int targetOffice) throws GeminiException {
        int operatorRankId = getTitleTemplate(operatorOffice).getRank();
        if (!getConstTemplate().getKingdomTitleAppoint().contains(operatorRankId)) {
            return false;
        }
        int targetRankId = getTitleTemplate(targetOffice).getRank();
        // NOTE(furson): 隐含的规则，较小的头衔级别可以任命较大的头衔级别
        return operatorRankId < targetRankId;
    }
}

package com.yorha.common.db.mongo.subscriber;

import org.bson.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * 用于返回多查询结果的subscriber
 *
 * <AUTHOR>
 */
public class MultiGetIBasicSubscriber extends IBasicSubscriber<Document, List<Document>> {


    public MultiGetIBasicSubscriber() {
        this.received = new ArrayList<>();
    }


    @Override
    public void onNext(Document response) {
        // 接收结果
        received.add(response);
    }


    @Override
    public void onError(final Throwable t) {
        error = t;
        onComplete();
    }

}

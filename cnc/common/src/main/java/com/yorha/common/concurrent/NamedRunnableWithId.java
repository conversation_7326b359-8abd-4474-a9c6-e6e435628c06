package com.yorha.common.concurrent;


/**
 * 带识别id、名字的函数封装
 *
 * <AUTHOR>
 */
public class NamedRunnableWithId extends NamedRunnable {

    private final Object id;

    private NamedRunnableWithId(Object id, String name, Runnable runnable) {
        super(name, runnable);
        this.id = id;
    }

    public static NamedRunnableWithId valueOf(Object id, String name, Runnable runnable) {
        return new NamedRunnableWithId(id, name, runnable);
    }

    public Object getId() {
        return id;
    }

    @Override
    public String toString() {
        return "NamedRunnableWithId{" +
                "name='" + name + '\'' +
                ", id=" + id +
                '}';
    }
}

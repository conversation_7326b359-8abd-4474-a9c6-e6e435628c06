package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.DeleteByPartKeyOption;
import com.yorha.common.db.tcaplus.result.DeleteByPartKeyResult;

public class DeleteByPartKeyAsk<T extends Message.Builder> implements GameDbReq<DeleteByPartKeyResult> {
    private final T req;
    private final DeleteByPartKeyOption option;

    public DeleteByPartKeyAsk(T req) {
        this(req, DeleteByPartKeyOption.newBuilder().build());
    }

    public DeleteByPartKeyAsk(T req, DeleteByPartKeyOption option) {
        this.req = req;
        this.option = option;
    }

    public T getReq() {
        return req;
    }

    public DeleteByPartKeyOption getOption() {
        return option;
    }
}

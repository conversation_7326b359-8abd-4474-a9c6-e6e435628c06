package com.yorha.common.actor.cluster.toplogy;

import com.yorha.common.utils.BusIdUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class Node {
    private final String busId;
    private final long startTsMs;

    private Node(String busId, long startTsMs) {
        this.busId = busId;
        this.startTsMs = startTsMs;
    }

    public String getBusId() {
        return this.busId;
    }

    public long getStartTsMs() {
        return this.startTsMs;
    }

    public int getServerType() {
        return BusIdUtils.getServerTypeFromBusId(this.busId);
    }

    public int getInstanceId() {
        return BusIdUtils.getInstanceIdFromBusId(this.busId);
    }

    public int getZoneId() {
        return BusIdUtils.getZoneIdFromBusId(this.busId);
    }
    
    public Map<String, Object> toMap() {
        return Collections.emptyMap();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final Node node = (Node) o;
        return Objects.equals(busId, node.busId);
    }

    @Override
    public int hashCode() {
        return busId != null ? busId.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Node{" +
                "busId='" + busId + '\'' +
                ", startTsMs=" + startTsMs +
                '}';
    }

    public static class Builder {
        private String busId;
        private long startTsMs;

        public Builder busId(final String busId) {
            this.busId = busId;
            return this;
        }

        public Builder startTsMs(final long startTsMs) {
            this.startTsMs = startTsMs;
            return this;
        }

        public Builder map(final Map<String, Object> map) {
            return this;
        }

        public Node build() {
            return new Node(this.busId, this.startTsMs);
        }
    }

    public static Builder newBuilder() {
        return new Builder();
    }
}

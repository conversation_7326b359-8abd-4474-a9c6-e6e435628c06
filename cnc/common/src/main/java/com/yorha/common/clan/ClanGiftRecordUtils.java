package com.yorha.common.clan;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;

import java.util.Optional;

/**
 * 军团礼物来源工具
 */
public class ClanGiftRecordUtils {

    private ClanGiftRecordUtils() {
    }

    public static Struct.ClanGiftRecord formTogetherKillMonsterRecord(long playerId, int monsterId) {
        Struct.ClanGiftRecord.Builder record = Struct.ClanGiftRecord.newBuilder();
        record.setPlayerId(playerId)
                .setType(CommonEnum.ClanGiftRecordType.CGRT_TOGETHER_KILL_MONSTER)
                .setDisplayData(Struct.DisplayData.newBuilder()
                        .setParams(Struct.DisplayParamList.newBuilder()
                                .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_MONSTER_ID, monsterId))
                                .build())
                        .build()
                );
        return record.build();
    }

    public static Struct.ClanGiftRecord formMemberKillMonsterRecord(long playerId, String playerName, int monsterId) {
        Struct.ClanGiftRecord.Builder record = Struct.ClanGiftRecord.newBuilder();
        record.setPlayerId(playerId)
                .setType(CommonEnum.ClanGiftRecordType.CGRT_MEMBER_KILL_MONSTER)
                .setDisplayData(Struct.DisplayData.newBuilder()
                        .setParams(Struct.DisplayParamList.newBuilder()
                                .addDatas(MsgHelper.buildDisPlayText(playerName))
                                .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_MONSTER_ID, monsterId))
                                .build())
                        .build()
                );
        return record.build();
    }

    public static Struct.ClanGiftRecord formBuyGoodsRecord(boolean hideInfo, String playerName, long playerId, long goodsId) {

        Struct.ClanGiftRecord.Builder record = Struct.ClanGiftRecord.newBuilder();
        record.setPlayerId(playerId)
                .setType(CommonEnum.ClanGiftRecordType.CGRT_BUY_GOODS)
                .setDisplayData(Struct.DisplayData.newBuilder()
                        .setParams(Struct.DisplayParamList.newBuilder()
                                .addDatas(MsgHelper.buildDisPlayText(playerName))
                                .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_CHARGE_GOODS_ID, goodsId))
                                .build())
                        .build()
                ).setIsHidden(hideInfo);
        return record.build();
    }

    public static Struct.ClanGiftRecord formHiddenBuyGoodsRecord(long goodsId) {

        Struct.ClanGiftRecord.Builder record = Struct.ClanGiftRecord.newBuilder();
        record.setType(CommonEnum.ClanGiftRecordType.CGRT_BUY_GOODS)
                .setDisplayData(Struct.DisplayData.newBuilder()
                        .setParams(Struct.DisplayParamList.newBuilder()
                                .addDatas(MsgHelper.buildDisPlayId(CommonEnum.DisplayParamType.DPT_CHARGE_GOODS_ID, goodsId))
                                .build())
                        .build()
                );
        return record.build();
    }

    public static long getMonsterId(Struct.ClanGiftRecord record) {
        Optional<Struct.DisplayParam> target = record.getDisplayData().getParams().getDatasList().stream().filter(
                displayParam -> displayParam.getType() == CommonEnum.DisplayParamType.DPT_MONSTER_ID
        ).findFirst();
        if (target.isEmpty()) {
            throw new GeminiException("find no monster Id in ClanGiftRecord={}", record);
        }
        return target.get().getNumber();
    }

    public static long getGoodsId(Struct.ClanGiftRecord record) {
        Optional<Struct.DisplayParam> target = record.getDisplayData().getParams().getDatasList().stream().filter(
                displayParam -> displayParam.getType() == CommonEnum.DisplayParamType.DPT_CHARGE_GOODS_ID
        ).findFirst();
        if (target.isEmpty()) {
            throw new GeminiException("find no goods Id in ClanGiftRecord={}", record);
        }
        return target.get().getNumber();
    }
}

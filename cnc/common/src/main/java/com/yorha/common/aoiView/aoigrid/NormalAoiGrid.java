package com.yorha.common.aoiView.aoigrid;

import com.yorha.common.aoiView.manager.AoiObserver;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class NormalAoiGrid extends AoiGrid {
    /**
     * 普通层存全量的
     */
    private final Set<AoiObserver> observers = new ObjectOpenHashSet<>();
    private final Set<Long> objs = new LongOpenHashSet();

    public NormalAoiGrid(int x, int y) {
        super(x, y);
    }

    @Override
    public void addObserver(AoiObserver observer, int layer) {
        super.addObserver(observer, layer);
        observers.add(observer);
    }

    @Override
    public void removeObserver(AoiObserver observer, int layer) {
        super.removeObserver(observer, layer);
        observers.remove(observer);
    }

    @Override
    public Set<AoiObserver> getObserverIds() {
        return observers;
    }

    @Override
    public void addSceneObj(long objId, int layer) {
        objs.add(objId);
    }

    @Override
    public void removeSceneObj(long objId, int layer) {
        objs.remove(objId);
    }

    @Override
    public Set<Long> getSceneObjIds() {
        return objs;
    }

    @Override
    public Set<Long> getSceneObjIds(int layer) {
        return objs;
    }
}

package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.DeleteOption;
import com.yorha.common.db.tcaplus.result.DeleteResult;

public class DeleteAsk<T extends Message.Builder> implements GameDbReq<DeleteResult> {
    private final T req;
    private final DeleteOption option;

    public DeleteAsk(T req) {
        this(req, DeleteOption.newBuilder().build());
    }

    public DeleteAsk(T req, DeleteOption option) {
        this.req = req;
        this.option = option;
    }

    public T getReq() {
        return req;
    }

    public DeleteOption getOption() {
        return option;
    }
}

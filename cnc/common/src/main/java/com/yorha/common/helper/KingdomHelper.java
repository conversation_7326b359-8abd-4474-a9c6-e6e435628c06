package com.yorha.common.helper;

import com.google.common.collect.Lists;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.db.tcaplus.msg.BatchSelectAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.kingdom.KingdomTemplateResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.SsSceneKingdom;
import com.yorha.proto.StructMsg;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 王国管理公共借口
 *
 * <AUTHOR>
 */
public class KingdomHelper {
    private static final Logger LOGGER = LogManager.getLogger(KingdomHelper.class);
    /**
     * 国王历史缓存最大页数
     */
    private static final Integer HISTORY_KING_CACHE_PAGE_SIZE = 5;
    /**
     * 国王历史缓存
     */
    private static final Map<Integer, StructMsg.SimpleKingInfo> KING_HISTORY = new ConcurrentHashMap<>();

    /**
     * 根据任期获取单条记录
     */
    public static StructMsg.SimpleKingInfo getKindInfo(int kingNum) {
        return KING_HISTORY.get(kingNum);
    }

    /**
     * 初始化load
     */
    public static void initHistoryKingCache(BaseGameActor actor, int toId) {
        int fromId = Integer.max(toId - getMaxCacheHistoryKingCount() + 1, 1);
        if (fromId > toId) {
            LOGGER.info("initHistoryKingCache fromId {} > toId {}", fromId, toId);
            return;
        }
        long startTsMs = SystemClock.now();
        BatchSelectAsk<TcaplusDb.HistoryKingTable.Builder> batchSelectAsk = getBatchSelectAsk(actor.getZoneId(), fromId, toId);
        actor.askGameDb(batchSelectAsk)
                .onComplete((res, e) -> {
                    if (e != null) {
                        LOGGER.error("initHistoryKingCache get chat msg by count from db failed, ", e);
                        return;
                    }
                    if (!res.isOk() && !res.isRecordNotExist()) {
                        LOGGER.error("initHistoryKingCache get chat msg by count from db failed. requestId={}, code={}", res.requestId, res.getCode());
                        return;
                    }
                    if (CollectionUtils.isNotEmpty(res.values)) {
                        for (ValueWithVersion<TcaplusDb.HistoryKingTable.Builder> value : res.values) {
                            StructMsg.SimpleKingInfo kingInfo = value.value.getKingInfo();
                            KING_HISTORY.put(kingInfo.getNumber(), kingInfo);
                        }
                        LOGGER.info("initHistoryKingCache: load cache cost time {}, from={} to={} historyKingCache={}",
                                SystemClock.now() - startTsMs, fromId, toId, KING_HISTORY.keySet());
                    }
                });
    }

    public static void loadHistoryKingFromDb(
            BaseGameActor actor,
            ActorMsgEnvelope envelope,
            SsSceneKingdom.FetchHistoryKingAns.Builder retBuilder,
            int fromId,
            int toId
    ) {
        long startTsMs = SystemClock.now();
        BatchSelectAsk<TcaplusDb.HistoryKingTable.Builder> batchSelectAsk = getBatchSelectAsk(actor.getZoneId(), fromId, toId);
        actor.askGameDb(batchSelectAsk)
                .onComplete((res, e) -> {
                    // 出错则answer默认数据
                    if (e != null) {
                        LOGGER.error("loadHistoryKingFromDb get chat msg by count from db failed, ", e);
                        actor.answerWithContext(envelope, retBuilder.build());
                        return;
                    }
                    if (!res.isOk() && !res.isRecordNotExist()) {
                        LOGGER.error("loadHistoryKingFromDb get chat msg by count from db failed. requestId={}, code={}", res.requestId, res.getCode());
                        actor.answerWithContext(envelope, retBuilder.build());
                        return;
                    }
                    if (CollectionUtils.isNotEmpty(res.values)) {
                        List<StructMsg.SimpleKingInfo> msgList = Lists.newArrayList(res.values).stream()
                                .map(it -> it.value.getKingInfo())
                                .sorted(Comparator.comparingInt(StructMsg.SimpleKingInfo::getNumber).reversed())
                                .collect(Collectors.toList());
                        retBuilder.addAllKingInfos(msgList);
                    }
                    actor.answerWithContext(envelope, retBuilder.build());
                    LOGGER.info("loadHistoryKingFromDb cost time={}, fromId={} toId={}", SystemClock.now() - startTsMs, fromId, toId);
                });
    }

    private static BatchSelectAsk<TcaplusDb.HistoryKingTable.Builder> getBatchSelectAsk(int zoneId, int fromId, int toId) {
        if (fromId > toId) {
            LOGGER.error("getBatchSelectAsk: fromId > toId, fromId={}, toId={}", fromId, toId);
            return null;
        }
        BatchGetOption.Builder batchOption = BatchGetOption.newBuilder().withGetAllFields(true);
        List<TcaplusDb.HistoryKingTable.Builder> listReq = new ArrayList<>();
        for (int id = fromId; id <= toId; id++) {
            TcaplusDb.HistoryKingTable.Builder req = TcaplusDb.HistoryKingTable.newBuilder();
            req.setZoneId(zoneId).setNumber(id);
            listReq.add(req);
        }
        if (listReq.isEmpty()) {
            LOGGER.info("getBatchSelectAsk: listReq is empty, fromId={}, toId={}", fromId, toId);
            return null;
        }

        return new BatchSelectAsk<>(listReq, batchOption.build());
    }

    public static void addKingInfo(GameActorWithCall actor, int zoneId, int thisKingNumber, StructMsg.SimpleKingInfo kingInfo) {
        // 缓存
        KING_HISTORY.put(thisKingNumber, kingInfo);
        KING_HISTORY.remove(thisKingNumber - getMaxCacheHistoryKingCount());
        LOGGER.info("addKingInfo add={} remove={} playerId={}", thisKingNumber, thisKingNumber - getMaxCacheHistoryKingCount(), kingInfo.getPlayerId());
        // db
        TcaplusDb.HistoryKingTable.Builder req = TcaplusDb.HistoryKingTable.newBuilder();
        req.setZoneId(zoneId)
                .setNumber(thisKingNumber)
                .setKingInfo(kingInfo);

        InsertAsk<TcaplusDb.HistoryKingTable.Builder> ask = new InsertAsk<>(req);
        actor.tellGameDb(ask);
    }

    /**
     * 获取最大缓存条数
     */
    public static int getMaxCacheHistoryKingCount() {
        KingdomTemplateResService service = ResHolder.getResService(KingdomTemplateResService.class);
        int perPageNum = service.getConstTemplate().getKingHistoryPerPageNum();
        return perPageNum * HISTORY_KING_CACHE_PAGE_SIZE;
    }
}

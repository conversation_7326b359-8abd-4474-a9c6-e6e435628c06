package com.yorha.common.auth.server;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.auth.server.result.AuthResult;
import com.yorha.common.constant.AuthConstant;
import com.yorha.common.io.ISession;
import com.yorha.proto.CsAccount;
import com.yorha.proto.User;

/**
 * 鉴权渠道
 *
 * <AUTHOR>
 */
public interface AuthChannel {
    AuthConstant.AuthChannelType authChannelType();

    void init();

    /**
     * token鉴权
     */
    AuthResult<?> auth(ISession session, GameActorWithCall actor, String openId, User.GetRoleList_C2S_Msg msg);

    /**
     * 获取OpenId
     */
    String getOpenId(User.GetRoleList_C2S_Msg msg);

    String getOpenId(CsAccount.DirGetServerList_C2S_Msg msg);

    String getOpenId(CsAccount.DirGetZone_C2S_Msg msg);
}

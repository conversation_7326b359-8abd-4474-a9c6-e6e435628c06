package com.yorha.common.aoiView.manager;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.utils.shape.AABB;
import com.yorha.proto.CommonEnum.SceneObjectNtfReason;
import com.yorha.proto.Entity;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;

import java.util.Set;

/**
 * 观察者对象
 *
 * <AUTHOR>
 */
public class AoiObserverItem implements AoiObserver {
    private final long playerId;
    /**
     * session地址
     */
    private final IActorRef sessionRef;
    /**
     * zoneId  玩家所在主世界场景zoneId
     */
    private final int sceneZoneId;
    /**
     * 更新视野新设的 包围盒和对应层级
     */
    private AABB aabb;
    private int newLayer;
    private int entityNumMax;
    /**
     * 当前看到的obj列表
     */
    private Set<Long> curViewObj = new LongOpenHashSet();
    /**
     * 已经算好的aoi格子的对应的层级
     */
    private Set<AoiGrid> curAoiGrids = new ObjectOpenHashSet<>();
    private int curLayer;

    public AoiObserverItem(IActorRef sessionRef, int sceneZoneId, long playerId) {
        this.sessionRef = sessionRef;
        this.sceneZoneId = sceneZoneId;
        this.playerId = playerId;
    }

    public void clearView() {
        for (AoiGrid aoiGrid : curAoiGrids) {
            aoiGrid.removeObserver(this, curLayer);
        }
        curAoiGrids.clear();
        curViewObj.clear();
        aabb = null;
    }

    public void clearView(AbstractActor objOwnerActor, int objOwnerZoneId, boolean isChangeZone) {
        // 缩略层 或  切服务器 不需要发delete
        if (isChangeZone || aabb == null) {
            clearView();
            return;
        }
        aabb = null;
        for (AoiGrid aoiGrid : curAoiGrids) {
            aoiGrid.removeObserver(this, curLayer);
        }
        curAoiGrids.clear();
        if (curViewObj.isEmpty()) {
            return;
        }
        Entity.EntityNtfMsg.Builder msgBuilder = Entity.EntityNtfMsg.newBuilder().setReason(SceneObjectNtfReason.SONR_AOI);
        msgBuilder.addAllDelEntities(curViewObj);
        SessionHelper.sendMsgToSession(sessionRef, objOwnerActor, MsgType.ENTITYNTFMSG, msgBuilder.setZoneId(objOwnerZoneId).build());
        curViewObj.clear();
    }

    @Override
    public int getSceneZoneId() {
        return sceneZoneId;
    }

    @Override
    public long getId() {
        return playerId;
    }

    @Override
    public IActorRef getSessionRef() {
        return sessionRef;
    }

    @Override
    public void setNewView(Set<AoiGrid> curAoiGrids, int layer) {
        this.curLayer = layer;
        this.curAoiGrids = curAoiGrids;
    }

    @Override
    public Set<AoiGrid> getOldAoiGrid() {
        return curAoiGrids;
    }

    @Override
    public int getOldLayer() {
        return curLayer;
    }

    @Override
    public void setUpdateView(AABB aabb, int layer, int entityNumMax) {
        this.aabb = aabb;
        this.newLayer = layer;
        this.entityNumMax = entityNumMax;
    }

    @Override
    public int getEntityNumMax() {
        return entityNumMax;
    }

    @Override
    public AABB getNewAABB() {
        return aabb;
    }

    @Override
    public int getNewLayer() {
        return newLayer;
    }

    @Override
    public Set<Long> getCurViewObj() {
        return curViewObj;
    }

    @Override
    public void setCurViewObj(Set<Long> obj) {
        this.curViewObj = obj;
    }

    @Override
    public void addViewObj(long objId) {
        curViewObj.add(objId);
    }

    @Override
    public void removeViewObj(long objId) {
        curViewObj.remove(objId);
    }

}

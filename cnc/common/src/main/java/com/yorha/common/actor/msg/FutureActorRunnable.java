package com.yorha.common.actor.msg;

import com.yorha.common.actor.IActor;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;

/**
 * 一种可以等待的Runnable
 *
 * <AUTHOR>
 */
public class FutureActorRunnable<T extends IActor, R> implements IActorRunnable<T> {
    private final String name;
    private final Function<T, R> function;
    private final Thread thread;
    private volatile boolean isCompleted;
    private Throwable throwable;
    private R result;

    public FutureActorRunnable(final String name, final Function<T, R> function) {
        this.name = name;
        this.function = function;
        this.thread = Thread.currentThread();
        this.isCompleted = false;
    }

    @Override
    public void run(T actor) {
        if (this.isCompleted) {
            throw new GeminiException("{} already run!", this);
        }
        try {
            this.result = this.function.apply(actor);
            this.isCompleted = true;
        } catch (Throwable throwable) {
            this.throwable = throwable;
            this.isCompleted = true;
        } finally {
            LockSupport.unpark(this.thread);
        }
    }

    /**
     * 等待结果。
     *
     * @param timeout 超时时间。
     * @param unit    时间单位。
     * @return true成功；false超时。
     */
    public boolean await(final long timeout, final TimeUnit unit) {
        // MESA模型
        final long nanos = unit.toNanos(timeout) + SystemClock.nanoTimeNative();
        while (!this.isCompleted) {
            final long remainingTimeNs = nanos - SystemClock.nanoTimeNative();
            if (remainingTimeNs <= 0) {
                return false;
            }
            LockSupport.parkNanos(this, remainingTimeNs);
        }
        return true;
    }

    /**
     * 获取返回值。
     *
     * @param timeout 超时时间。
     * @param unit    超时时间单位。
     * @return 结果
     * @throws TimeoutException 执行超时。
     */
    public R get(final long timeout, final TimeUnit unit) throws TimeoutException {
        final boolean isOk = this.await(timeout, unit);
        if (!isOk) {
            throw new TimeoutException(this.profName());
        }
        if (this.throwable != null) {
            throw new GeminiException("execute " + this.profName() + " fail!", this.throwable);
        }
        return this.result;
    }

    @Override
    public String profName() {
        return name;
    }

    @Override
    public String toString() {
        return profName();
    }
}

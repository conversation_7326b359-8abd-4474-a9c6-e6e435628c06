package com.yorha.common.activity;

import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.utils.time.SystemClock;

import java.time.Instant;
import java.util.function.Supplier;

public class TickLikeTimer {
    /**
     * 不要用nextReloadTimer的执行时间来替代这个字段，那个带偏移的
     */
    private Instant nextTickTime = null;
    /**
     * 下面2选1
     */
    private ActorTimer nextTickTimer = null;
    private Runnable cancelRunnable = null;

    public TickLikeTimer() {
    }

    public void tryAddTickTimer(Instant next, Supplier<ActorTimer> tickTimer) {
        if (this.nextTickTime != null && nextTickTime.isAfter(SystemClock.nowInstant()) && next.isAfter(nextTickTime)) {
            return;
        }
        cancel();
        this.nextTickTime = next;
        this.nextTickTimer = tickTimer.get();
    }

    public void tryAddTickTimer(Instant next, Runnable addRunnable, Runnable cancelRunnable) {
        if (this.nextTickTime != null && nextTickTime.isAfter(SystemClock.nowInstant()) && next.isAfter(nextTickTime)) {
            return;
        }
        cancel();
        this.nextTickTime = next;
        addRunnable.run();
        this.cancelRunnable = cancelRunnable;
    }

    public void cancel() {
        this.nextTickTime = null;
        if (this.nextTickTimer != null) {
            this.nextTickTimer.cancel();
            this.nextTickTimer = null;
        }
        if (cancelRunnable != null) {
            cancelRunnable.run();
            cancelRunnable = null;
        }
    }
}

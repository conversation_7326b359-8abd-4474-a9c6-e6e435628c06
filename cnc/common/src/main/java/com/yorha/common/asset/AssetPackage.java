package com.yorha.common.asset;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.game.gen.prop.YoAssetDescProp;
import com.yorha.game.gen.prop.YoAssetPackageProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 玩家财产包，用于统一操作、传递钱币资源、道具、英雄等玩家财产
 * AssetPackage的assets没有深拷贝，所以就算b = builderA.build()后，如果继续修改builderA，也会影响到b
 * 此类对象不可变,可使用[Builder]构建
 */
public class AssetPackage {
    private static final Logger LOGGER = LogManager.getLogger(AssetPackage.class);

    private final List<AssetDesc> assets;

    public static final AssetPackage EMPTY = new AssetPackage(Collections.emptyList());

    private AssetPackage(List<AssetDesc> assets) {
        // 没有深拷贝
        this.assets = assets;
    }

    public List<AssetDesc> getImmutableAssets() {
        return ImmutableList.copyOf(assets);
    }

    public static Builder builder() {
        return new Builder();
    }

    public void forEach(AssetConsumer consumer) {
        for (AssetDesc asset : assets) {
            if (asset instanceof ItemDesc) {
                consumer.acceptItem((ItemDesc) asset);
            } else if (asset instanceof CurrencyDesc) {
                consumer.acceptCurrency((CurrencyDesc) asset);
            } else {
                throw new GeminiException("unrecognized asset: {}", asset);
            }
        }
    }

    public void forEachItems(Consumer<ItemDesc> consumer) {
        for (AssetDesc asset : assets) {
            if (asset instanceof ItemDesc) {
                consumer.accept((ItemDesc) asset);
            }
        }
    }

    public void forEachCurrency(Consumer<CurrencyDesc> consumer) {
        for (AssetDesc asset : assets) {
            if (asset instanceof CurrencyDesc) {
                consumer.accept((CurrencyDesc) asset);
            }
        }
    }

    public boolean hasStandaloneAsset() {
        Multimap<AssetType, Integer> type2ids = HashMultimap.create();
        for (AssetDesc asset : assets) {
            if (type2ids.containsEntry(asset.getType(), asset.getId())) {
                return true;
            }
            type2ids.put(asset.getType(), asset.getId());
        }
        return false;
    }

    public boolean isEmpty() {
        return assets.isEmpty();
    }

    public YoAssetPackageProp toProp() {
        YoAssetPackageProp prop = new YoAssetPackageProp();
        for (AssetDesc asset : assets) {
            prop.addAssets(asset.toProp());
        }
        return prop;
    }

    public Struct.YoAssetPackage toProto() {
        Struct.YoAssetPackage.Builder builder = Struct.YoAssetPackage.newBuilder();
        for (AssetDesc asset : assets) {
            builder.getAssetsBuilder().addDatas(asset.toProto());
        }
        return builder.build();
    }

    public StructPB.YoAssetPackagePB toPb() {
        StructPB.YoAssetPackagePB.Builder builder = StructPB.YoAssetPackagePB.newBuilder();
        for (AssetDesc asset : assets) {
            builder.getAssetsBuilder().addDatas(asset.toPb());
        }
        return builder.build();
    }

    @Override
    public String toString() {
        return "AssetPackage{" +
                "assets=" + assets +
                '}';
    }

    public Struct.ItemPairList toItemReward() {
        Struct.ItemPairList.Builder builder = Struct.ItemPairList.newBuilder();
        for (AssetDesc asset : assets) {
            if (asset instanceof ItemDesc) {
                builder.addDatas(Struct.ItemPair.newBuilder()
                        .setItemTemplateId(asset.getId())
                        .setCount((int) asset.getAmount())
                        .build()
                );
            } else {
                LOGGER.error("AssetPackage toItemReward currencyDesc appeared!. {}", this);
            }
        }
        return builder.build();
    }

    public static class Builder {
        private final ArrayList<AssetDesc> buildingList;

        Builder() {
            this.buildingList = Lists.newArrayList();
        }

        public AssetPackage build() {
            return new AssetPackage(buildingList);
        }

        /**
         * 清理数量为0的Asset
         */
        public Builder tidy() {
            buildingList.removeIf(desc -> desc.getAmount() <= 0);
            return this;
        }

        public boolean isEmpty() {
            return buildingList.isEmpty();
        }

        /**
         * 与已有的合并,只修改amount
         */
        public Builder plus(AssetPackage pack) {
            for (AssetDesc asset : pack.assets) {
                plus(asset);
            }
            return this;
        }

        public Builder plus(AssetDesc assetDesc) {
            for (int i = 0; i < buildingList.size(); i++) {
                AssetDesc exist = buildingList.get(i);
                if (exist.similar(assetDesc)) {
                    buildingList.set(i, exist.plus(assetDesc));
                    return this;
                }
            }
            buildingList.add(assetDesc);
            return this;
        }

        public Builder plus(AssetPackage.Builder another) {
            for (AssetDesc asset : another.buildingList) {
                plus(asset);
            }
            return this;
        }


        public Builder plusItem(IntPairType intPairType) {
            plusItem(intPairType.getKey(), intPairType.getValue());
            return this;
        }


        public Builder plusItem(int itemId, long num) {
            plus(new ItemDesc(itemId, num));
            return this;
        }

        public Builder plusItems(List<IntPairType> itemPairList) {
            for (IntPairType pair : itemPairList) {
                plusItem(pair.getKey(), pair.getValue());
            }
            return this;
        }

        public Builder plusCurrency(CommonEnum.CurrencyType type, long num) {
            plus(new CurrencyDesc(type, num));
            return this;
        }

        public Builder plusCurrency(int type, long value) {
            CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(type);
            if (currencyType == null) {
                throw new GeminiException("CurrencyType unrecognized.{}", type);
            }
            return plusCurrency(currencyType, value);
        }

        public Builder plusCurrency(IntPairType pair) {
            return plusCurrency(pair.getKey(), pair.getValue());
        }

        public Builder plusCurrency(List<IntPairType> costList) {
            for (IntPairType pair : costList) {
                plusCurrency(pair);
            }
            return this;
        }

        public Builder plusCurrency(Map<CommonEnum.CurrencyType, Integer> map) {
            map.forEach(this::plusCurrency);
            return this;
        }

        public Builder plusCurrencyLong(Map<CommonEnum.CurrencyType, Long> map) {
            map.forEach(this::plusCurrency);
            return this;
        }

        /**
         * 直接追加一个[AssetDesc],不与已有的合并,用于需要单独显示图标等特殊需求
         * <p>
         * 只能用于组装奖励，不可以用来组装consume用途的
         */
        public Builder plusStandalone(AssetPackage pack) {
            for (AssetDesc asset : pack.assets) {
                plusStandalone(asset);
            }
            return this;
        }

        public Builder plusStandalone(AssetDesc assetDesc) {
            buildingList.add(assetDesc);
            return this;
        }

        public Builder from(YoAssetPackageProp prop) {
            for (YoAssetDescProp assetProp : prop.getAssets()) {
                AssetType type = AssetType.fromIndex(assetProp.getType());
                if (type == null) {
                    throw new GeminiException("AssetType not recognized.{}", assetProp);
                }
                AssetDesc assetDesc = type.fromProp(assetProp);
                plusStandalone(assetDesc);
            }
            return this;
        }

        public Builder from(Struct.YoAssetPackage proto) {
            for (Struct.YoAssetDesc yoAssetDesc : proto.getAssets().getDatasList()) {
                AssetType type = AssetType.fromIndex(yoAssetDesc.getType());
                if (type == null) {
                    throw new GeminiException("AssetType not recognized.{}", yoAssetDesc);
                }
                AssetDesc assetDesc = type.fromProto(yoAssetDesc);
                plusStandalone(assetDesc);
            }
            return this;
        }

        public Builder plusItemList(List<IntPairType> pairList) {
            if (CollectionUtils.isEmpty(pairList)) {
                return this;
            }
            for (IntPairType pair : pairList) {
                plusItem(pair.getKey(), pair.getValue());
            }
            return this;
        }
    }

}


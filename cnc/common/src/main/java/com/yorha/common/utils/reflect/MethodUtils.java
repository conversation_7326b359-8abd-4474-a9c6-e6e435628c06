/*
 * Copyright 2015-2020 yorha Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yorha.common.utils.reflect;


import java.lang.reflect.Method;
import java.util.*;

/**
 * 方法工具类
 */
public class MethodUtils {

    /**
     * 获取指定类的所有方法，包含父类的方法.
     *
     * @param klass 指定类
     * @return 指定类的方法集合.
     */
    public static List<Method> getMethodList(final Class<?> klass) {
        Set<Method> result = new HashSet<>();
        for (Class<?> target = klass; target != Object.class; target = target.getSuperclass()) {
            Collections.addAll(result, target.getDeclaredMethods());
        }
        return new ArrayList<>(result);
    }
}
package com.yorha.common.actorservice;

import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import org.jetbrains.annotations.NotNull;

/**
 * Actor处理消息的上下文。
 *
 * <AUTHOR>
 */
public interface IMsgContext {
    /**
     * 当前的信封。
     *
     * @return 信封。
     */
    @NotNull
    ActorMsgEnvelope getEnvelope();

    /**
     * 当前的mailbox。
     *
     * @return mailbox。
     */
    @NotNull
    IActorMailbox getMailbox();

    /**
     * 记录上下文中执行的耗时信封。（一般是Call）
     *
     * @param startTsNs 启动时间戳。
     * @param endTsNs   销毁时间戳。
     * @param envelope  信封。
     */
    void addCostSendEnvelop(long startTsNs, long endTsNs, ActorMsgEnvelope envelope);
}

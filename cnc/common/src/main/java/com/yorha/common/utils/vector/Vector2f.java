package com.yorha.common.utils.vector;

import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;

/**
 * <AUTHOR>
 */
public class Vector2f {
    private final float x;
    private final float y;

    public Vector2f(float x, float y) {
        this.x = x;
        this.y = y;
    }

    public float getX() {
        return x;
    }

    public float getY() {
        return y;
    }

    public double getRadians() {
        return Math.atan2(y, x);
    }

    public Point toPoint() {
        return Point.valueOf(x, y);
    }

    public double getAngle() {
        return Math.toDegrees(this.getRadians());
    }

    public double getLength() {
        return Math.sqrt(x * x + y * y);
    }


    public static Vector2f valueOf(int length, int angle) {
        double radians = Math.toRadians(angle);
        float y = (float) Math.sin(radians) * length;
        float x = (float) Math.cos(radians) * length;
        return new Vector2f(x, y);
    }


    public static Vector2f valueOf(float x, float y) {
        return new Vector2f(x, y);
    }

    public static Vector2f add(Vector2f a, Vector2f b) {
        return new Vector2f(a.x + b.x, a.y + b.y);
    }

    public Vector2f add(Vector2f src) {
        return new Vector2f(x + src.x, y + src.y);
    }

    public Point add(Point srcPoint) {
        return Point.valueOf(x + srcPoint.getX(), y + srcPoint.getY());
    }


    /**
     * 单位化
     */
    public static Vector2f unitization(Vector2f a) {
        float len = (float) a.getLength();
        return new Vector2f(a.getX() / len, a.getY() / len);
    }

    public Vector2f unitization() {
        return unitization(this);
    }

    /**
     * 调整长度
     */
    public Vector2f resizeLength(float newLength) {
        float len = (float) getLength();
        return Vector2f.valueOf(x / len * newLength, y / len * newLength);
    }

    /**
     * 旋转
     *
     * @param angle 顺时针 旋转角度 [0 - 360]
     * @return 旋转后的向量
     */
    public Vector2f rotate(int angle) {
        double radians = Math.toRadians(angle);
        double sin = Math.sin(radians);
        double cos = Math.cos(radians);
        return Vector2f.valueOf((float) (sin * y + cos * x), (float) (cos * y - sin * x));
    }


    public Vector2f rotate(double radians) {
        double sin = Math.sin(radians);
        double cos = Math.cos(radians);
        return Vector2f.valueOf((float) (sin * y + cos * x), (float) (cos * y - sin * x));
    }

    /**
     * 绕着point 旋转 radian弧度后得到的点
     */
    public Vector2f rotate(Vector2f point, double radians) {
        double sin = Math.sin(radians);
        double cos = Math.cos(radians);
        int x = (int) ((this.x - point.getX()) * cos - (this.y - point.getY()) * sin + point.getX());
        int y = (int) ((this.x - point.getX()) * sin + (this.y - point.getY()) * cos + point.getY());
        return Vector2f.valueOf(x, y);
    }

    public static Vector2f getVectorFromPointToPoint(Point srcPoint, Point endPoint) {
        return new Vector2f(endPoint.getX() - srcPoint.getX(), endPoint.getY() - srcPoint.getY());
    }

    /**
     * 向量点乘
     */
    public static float dotProduct(Vector2f a, Vector2f b) {
        return a.getX() * b.getX() + a.getY() * b.getY();
    }

    /**
     * 向量叉乘
     */
    public static float crossProduct(Vector2f a, Vector2f b) {
        return a.getX() * b.getY() - a.getY() * b.getX();
    }

    /**
     * 获取两个向量的夹角
     *
     * @return 返回值 [0 - 180]
     */
    public static float getIntersectionAngle(Vector2f a, Vector2f b) {
        float v = dotProduct(a, b);
        double value = v / (a.getLength() * b.getLength());
        double angle = Math.toDegrees(Math.acos(value));
        return (float) angle;
    }

    /**
     * 获取两个向量的夹角
     * 以a为基准，顺时针计算到b的夹角
     *
     * @return 返回值 [0 - 360]
     */
    public static float getIntersectionAngle2(Vector2f a, Vector2f b) {
        double v = Math.atan2(a.getY(), a.getX()) - Math.atan2(b.getY(), b.getX());
        double angle = Math.toDegrees(v);
        if (angle > 360) {
            angle -= 360;
        } else if (angle < 0) {
            angle += 360;
        }

        return (float) angle;
    }

    /**
     * 获取角平分线
     *
     * @param oneV:归一化向量
     * @param otherV:归一化向量
     */
    public static Vector2f getMidVector(Vector2f oneV, Vector2f otherV) {
        float collinearV = isCollinear(oneV, otherV);
        Vector2f midV;
        if (collinearV < 0) {
            // 逆向共线角平分线特殊计算
            midV = Vector2f.valueOf(-oneV.getY(), oneV.getX()).unitization();
        } else {
            midV = Vector2f.add(oneV, otherV).unitization();
        }
        return midV;
    }

    /**
     * 判断向量是否共线
     *
     * @return 0:不共线  >0:正向共线  <0:逆向共线
     */
    public static float isCollinear(Vector2f oneV, Vector2f otherV) {
        float x2 = otherV.getX();
        float y2 = otherV.getY();
        if (oneV.getX() * y2 != x2 * oneV.getY()) {
            return 0;
        }
        if (otherV.getX() != 0) {
            return oneV.getX() / otherV.getX();
        }
        if (otherV.getY() != 0) {
            return oneV.getY() / otherV.getY();
        }
        WechatLog.error("Why do the parameters need to pass a zero vector. otherV:{}", otherV);
        return 0;
    }
}

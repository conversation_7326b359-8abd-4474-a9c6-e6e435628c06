package com.yorha.common.server;

import com.yorha.common.utils.BusIdUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务器角色列表.
 *
 * <AUTHOR>
 */

public enum NodeRole {
    // 目录服务器
    Dir(1),
    // kvk服务器
    // KVK(2),
    // IdIp服务器
    IdIp(3),
    // Zone服务器
    Zone(4),
    // 全局服务器
    Global(5),
    // 副本服务器
    Dungeon(6),
    // 战斗服务器
    Battle(7),
    ;


    private static final Map<Integer, NodeRole> ROLE_MAP = new HashMap<>();
    private final int typeId;

    static {
        for (NodeRole role : NodeRole.values()) {
            ROLE_MAP.put(role.getTypeId(), role);
        }
    }

    NodeRole(int id) {
        this.typeId = id;
    }

    public int getTypeId() {
        return typeId;
    }

    public static NodeRole forNumber(int id) {
        return ROLE_MAP.get(id);
    }

    public static boolean isSameType(NodeRole role, int id) {
        return role.getTypeId() == id;
    }

    public static boolean isSameType(NodeRole role, String busId) {
        return isSameType(role, BusIdUtils.getServerTypeFromBusId(busId));
    }
}

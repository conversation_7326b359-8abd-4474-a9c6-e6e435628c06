package com.yorha.common.dbactor;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.common.actor.IActorWithTimer;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.option.DeleteOption;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.option.UpdateOption;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.freqLimitCaller.FreqLimitCaller;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.concurrent.TimeUnit;

/**
 * Db的任务队列，功能如下：
 * 1. 保证无并发的Db任务。
 * 2. 提供db操作的框架。
 * 3. 提供重试、覆盖等操作。
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class DbTaskProxy {
    private static final Logger LOGGER = LogManager.getLogger(DbTaskProxy.class);

    /**
     * 0x00_00_00 ~ 0x00_00_FF当前状态
     * 0x00_01_00 ~ 0x00_FF_00下一次状态
     * 0x01_00_00标识不再接收Update操作。
     * 0x02_00_00标识proxy已经关闭
     */
    public static final int TASK_QUEUE_STATUS_NONE = 0;
    public static final int TASK_QUEUE_STATUS_INSERT = 0x01;
    public static final int TASK_QUEUE_STATUS_UPDATE = 0x01 << 1;
    public static final int TASK_QUEUE_STATUS_DELETE = 0x01 << 2;
    private static final int TASK_QUEUE_STATUS_NOT_UPDATE_ANY_MORE = 0x10000;
    private static final int TASK_QUEUE_STATUS_NOT_INSERT_ANY_MORE = 0x40000;
    private static final int TASK_QUEUE_STATUS_STOP = 0x20000;
    private static final int TASK_QUEUE_STATUS_MARK = 0xFFFFFF00;
    private static final int TASK_QUEUE_NEXT_STATUS_MARK = 0xFFFF00FF;


    /**
     * 增、删、改配置。
     */
    private final IDbChangeUpsertStrategy upsertStrategy;
    private final IDbChangeDeleteStrategy deleteStrategy;
    /**
     * db操作。
     */
    private final IDbOperationStrategy dbOperation;
    /**
     * 限制频率。
     */
    private final long timeoutTimeMs;
    /**
     * DB限频器。
     */
    private final FreqLimitCaller updateSpeedController;
    /**
     * queue状态。
     */
    private int mark;
    /**
     * 记录上次的超时时间。
     */
    private long lastTaskTsMs;
    /**
     * 当前的taskId。
     */
    private int taskId;
    /**
     * proxy的owner。
     */
    private final AbstractActor owner;
    /**
     * proxy的名称。
     */
    private final String name;

    private DbTaskProxy(Builder builder) {
        this.dbOperation = builder.operationStrategy;
        this.upsertStrategy = builder.upsertStrategy;
        this.deleteStrategy = builder.deleteStrategy;
        this.timeoutTimeMs = builder.timeoutTimeMs;
        if (builder.owner == null) {
            throw new GeminiException("DbTaskProxy name={} no owner", builder.name);
        }
        this.owner = builder.owner;
        this.name = builder.name;
        this.mark = 0;
        if (builder.intervalMs > 0) {
            if (builder.actorTimerOwner == null) {
                throw new GeminiException("DbTaskProxy name={} no actorTimerOwner", builder.name);
            }
            final String extraName = this.owner.getRole() + (this.name != null ? this.name : "");
            this.updateSpeedController = new FreqLimitCaller(TimerReasonType.DB_TASK_PROXY_UPDATE_LIMIT, extraName,
                    builder.intervalMs, 1, this::doUpdate, builder.actorTimerOwner, builder.entityId);
        } else {
            this.updateSpeedController = null;
        }
    }

    /**
     * [异步]插入数据(插入失败则将后来触发的update、saveDb改为insert)
     * 互斥操作，不允许作为后置任务。
     * 1. NONE -> INSERT: 触发INSERT。
     * 2. INSERT -> INSERT: 上一次超时，触发INSERT。
     * 3. DELETE -> INSERT: 禁止INSERT
     * <p>
     * 会重新开始接收更新请求。
     *
     * @throws GeminiException 当前任务执行非INSERT任务即报错。
     */
    public void insert() {
        LOGGER.info("DbTaskProxy insert {} {}", this.owner, this.name);
        if (this.isProxyStop()) {
            WechatLog.error("DbTaskProxy insert isProxyStop, actor={} proxy={}", this.owner, this);
            return;
        }
        final int status = this.getStatus();
        // 确保当前状态是 NONE or INSERT
        if (status != TASK_QUEUE_STATUS_NONE && status != TASK_QUEUE_STATUS_INSERT) {
            throw new GeminiException("DbTaskProxy insert insert when status={}, proxy={} actor={}", getStringStatus(this.getStatus()), this, this.owner);
        }
        // 禁止插入
        if (this.isForbidInsertAnymore()) {
            LOGGER.error("DbTaskProxy insert isForbidInsertAnymore, actor={} proxy={}", this.owner, this);
            return;
        }
        final long nowTsMs = SystemClock.nowNative();
        // 未超时，则跳过。
        if (this.lastTaskTsMs > nowTsMs) {
            LOGGER.warn("DbTaskProxy insert insert when not timeout, taskId={} proxy={} actor={}", this.taskId, this, this.owner);
            return;
        }
        // 触发一次INSERT。
        this.taskId += 1;
        // 状态标记
        this.setStatus(TASK_QUEUE_STATUS_INSERT);
        this.lastTaskTsMs = nowTsMs + this.timeoutTimeMs;
        final int taskId = this.taskId;
        Message.Builder request = this.upsertStrategy.buildInsertRequest(this.owner);
        final InsertAsk<Message.Builder> ask = new InsertAsk<>(request);
        // 若插入失败，则将后续触发的update、saveDb改为insert
        this.dbOperation.insertAsync(this.owner, ask, (result, err) -> {
            if (err != null) {
                LOGGER.error("DbTaskProxy insert insert fail taskId={} result is null proxy={} actor={}", taskId, this, this.owner, err);
                return;
            }
            // 错误码不为Ok，打印错误日志
            if (!DbUtil.isOk(result.getCode())) {
                // 记录已经存在，则走插入成功流程
                if (!DbUtil.isRecordAlreadyExist(result.getCode())) {
                    LOGGER.error("DbTaskProxy insert insert fail taskId={}, code is={} proxy={} actor={}", taskId, result.getCode(), this, this.owner);
                    return;
                }
                LOGGER.warn("DbTaskProxy insert insert refused taskId={} already insert proxy={} actor={}", taskId, this, this.owner);
            }
            this.finishTask(taskId);
        });
    }


    /**
     * [异步]更新数据库。(不重试，失败则由下次update、saveDb将数据补偿更新；此前若没insert成功，会insert)
     * 注意：会进行收脏和落库。（不要unmarkAll之后调用，可能会丢失数据）
     */
    public void update() {
        LOGGER.info("DbTaskProxy update {} {}", this.owner, this.name);
        if (this.isProxyStop()) {
            WechatLog.error("DbTaskProxy update isProxyStop, actor={} proxy={}", this.owner, this);
            return;
        }
        final int dirtyId = this.upsertStrategy.collectDirtyForSave(this.owner);
        if (dirtyId < 0) {
            LOGGER.info("DbTaskProxy update collectDirty skip, owner={}", this.owner);
            return;
        }
        if (this.updateSpeedController != null) {
            this.updateSpeedController.run();
            return;
        }
        this.doUpdate();
    }

    /**
     * [异步]落库（含重试，此前若没insert成功，会insert）
     * 仅Scene在停服时可用
     */
    public void saveDbAsync() {
        LOGGER.info("DbTaskProxy saveDbAsync {} {}", this.owner, this.name);
        if (this.isProxyStop()) {
            WechatLog.error("DbTaskProxy saveDbAsync isProxyStop, actor={} proxy={}", this.owner, this);
            return;
        }
        if (this.updateSpeedController != null) {
            this.updateSpeedController.stopTimer();
        }
        // 有前置insert操作，重试至成功
        if (this.getStatus() == TASK_QUEUE_STATUS_INSERT) {
            Message.Builder request = this.upsertStrategy.buildInsertRequest(this.owner);

            LOGGER.info("DbTaskProxy saveDbAsync insertAsync actor={} proxy={}", this.owner, this);
            // 带有重试
            final InsertAsk<Message.Builder> ask = new InsertAsk<>(request, InsertOption.newBuilder().withRetry().build());
            this.dbOperation.insertAsync(this.owner, ask, null);
            return;
        }
        final int dirtyId = this.upsertStrategy.collectDirtyForSave(this.owner);
        if (dirtyId < 0) {
            LOGGER.info("DbTaskProxy saveDbAsync collectDirty skip, proxy={} actor={}", this, this.owner);
            return;
        }
        if (this.isForbidUpdateAnymore()) {
            LOGGER.error("DbTaskProxy saveDbAsync isForbidUpdateAnymore actor={} proxy={}", this.owner, this);
            return;
        }
        final long nowTsMs = SystemClock.nowNative();
        if (this.getStatus() != TASK_QUEUE_STATUS_NONE && this.getStatus() != TASK_QUEUE_STATUS_UPDATE) {
            WechatLog.error("DbTaskProxy saveDbAsync Running actor={} proxy={}", this.owner, this);
            return;
        }
        if (this.getNextStatus() != TASK_QUEUE_STATUS_NONE && this.getStatus() != TASK_QUEUE_STATUS_UPDATE) {
            WechatLog.error("DbTaskProxy saveDbAsync Running actor={} proxy={}", this.owner, this);
            return;
        }
        // 设置update状态为空
        if (this.getStatus() == TASK_QUEUE_STATUS_UPDATE) {
            this.setStatus(TASK_QUEUE_STATUS_NONE);
            LOGGER.warn("DbTaskProxy saveDbAsync skip wait taskId={} actor={} proxy={}", this.taskId, this.owner, this);
        }
        this.setNextStatus(TASK_QUEUE_STATUS_NONE);
        // taskId递增
        this.taskId += 1;
        // 状态标记
        this.setStatus(TASK_QUEUE_STATUS_UPDATE);
        this.lastTaskTsMs = nowTsMs + this.timeoutTimeMs;
        // 构造请求
        final IDbChangeUpsertStrategy.Request request = this.upsertStrategy.buildUpdateRequest(this.owner);

        final Message.Builder req = request.req;
        final GeneratedMessageV3 checkAttr = request.checkAttr;

        final UpdateOption.Builder option = UpdateOption.newBuilder().withRetry();
        if (checkAttr != null) {
            option.withResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL);
        }

        LOGGER.info("DbTaskProxy saveDbAsync updateAsync actor {} proxy {}", this.owner, this);
        final UpdateAsk<Message.Builder> ask = new UpdateAsk<>(req, option.build());
        this.dbOperation.updateAsync(this.owner, ask, null);
    }

    /**
     * [同步]落库（含重试，此前若没insert成功，会insert）
     * Entity销毁时(停服或玩家下线)or玩家移民时调用
     */
    public void saveDbSync() {
        LOGGER.info("DbTaskProxy saveDbSync {} {}", this.owner, this.name);
        if (this.isProxyStop()) {
            WechatLog.error("DbTaskProxy saveDbSync isProxyStop! actor {} proxy {}!", this.owner, this);
            return;
        }
        if (this.updateSpeedController != null) {
            this.updateSpeedController.stopTimer();
        }
        // 有前置insert操作，重试至成功
        if (this.getStatus() == TASK_QUEUE_STATUS_INSERT) {
            Message.Builder request = this.upsertStrategy.buildInsertRequest(this.owner);
            // 带有重试
            final InsertAsk<Message.Builder> ask = new InsertAsk<>(request, InsertOption.newBuilder().withRetry().build());
            final InsertResult<Message.Builder> result = this.dbOperation.insertSync(this.owner, ask);
            if (result == null) {
                LOGGER.info("DbTaskProxy saveDbSync insert fail taskId={}, result is null, proxy={} actor={}", taskId, this, this.owner);
                return;
            }
            // 错误码不为Ok，打印错误日志
            if (!DbUtil.isOk(result.getCode())) {
                // 非记录已存在错误码
                if (!DbUtil.isRecordAlreadyExist(result.getCode())) {
                    WechatLog.error("DbTaskProxy saveDbSync insert fail, taskId={}, code={} proxy={} actor={}", taskId, result.getCode(), this, this.owner);
                    return;
                }
                LOGGER.info("DbTaskProxy saveDbSync insert duplicate, taskId={}, already insert, proxy={} actor={}", taskId, this, this.owner);
            }
            this.setStatus(TASK_QUEUE_STATUS_NONE);

            LOGGER.info("DbTaskProxy saveDbSync insert taskId={}, insert success, proxy={} actor={}", taskId, this, this.owner);
            return;
        }
        final int dirtyId = this.upsertStrategy.collectDirtyForSave(this.owner);
        if (dirtyId < 0) {
            LOGGER.info("DbTaskProxy saveDbSync collectDirty skip, proxy={} actor={}", this, this.owner);
            return;
        }
        if (this.isForbidUpdateAnymore()) {
            LOGGER.error("DbTaskProxy saveDbSync isForbidUpdateAnymore actor={} proxy={}", this.owner, this);
            return;
        }
        final long nowTsMs = SystemClock.nowNative();
        if (this.getStatus() != TASK_QUEUE_STATUS_NONE && this.getStatus() != TASK_QUEUE_STATUS_UPDATE) {
            WechatLog.error("DbTaskProxy saveDbSync Running actor={} proxy={}", this.owner, this);
            return;
        }
        if (this.getNextStatus() != TASK_QUEUE_STATUS_NONE && this.getStatus() != TASK_QUEUE_STATUS_UPDATE) {
            WechatLog.error("DbTaskProxy saveDbSync Running actor={} proxy={}", this.owner, this);
            return;
        }
        // 设置update状态为空
        if (this.getStatus() == TASK_QUEUE_STATUS_UPDATE) {
            this.setStatus(TASK_QUEUE_STATUS_NONE);
            LOGGER.warn("DbTaskProxy saveDbSync skip wait taskId={} actor={} proxy={}", this.taskId, this.owner, this);
        }
        this.setNextStatus(TASK_QUEUE_STATUS_NONE);
        // taskId递增
        this.taskId += 1;
        final int taskId = this.taskId;
        // 状态标记
        this.setStatus(TASK_QUEUE_STATUS_UPDATE);
        this.lastTaskTsMs = nowTsMs + this.timeoutTimeMs;
        // 构造请求
        final IDbChangeUpsertStrategy.Request request = this.upsertStrategy.buildUpdateRequest(this.owner);

        final Message.Builder req = request.req;
        final boolean isFull = request.isFull;
        final boolean isChange = request.isChanged;
        final GeneratedMessageV3 checkAttr = request.checkAttr;


        final UpdateOption.Builder option = UpdateOption.newBuilder().withRetry();
        if (checkAttr != null) {
            option.withResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL);
        }

        final UpdateAsk<Message.Builder> ask = new UpdateAsk<>(req, option.build());
        final UpdateResult<Message.Builder> res;

        try {
            res = this.dbOperation.updateSync(this.owner, ask);
            if (res == null) {
                LOGGER.error("DbTaskProxy saveDbSync proxy={} res is null, actor={}", this, this.owner);
                return;
            }
            this.onUpdateCallback(taskId, isFull, isChange, res, checkAttr, dirtyId);
            if (DbUtil.isOk(res.getCode())) {
                LOGGER.info("DbTaskProxy saveDbSync proxy={} taskId={} actor={}, success", this, taskId, this.owner);
                this.finishTask(taskId);
                return;
            }
            if (DbUtil.isRecordNotExist(res.getCode())) {
                WechatLog.error("DbTaskProxy saveDbSync proxy={} taskId={} actor={}, not exists", this, taskId, this.owner);
                this.finishTask(taskId);
                return;
            }
            LOGGER.info("DbTaskProxy saveDbSync proxy={} actor={} errorCode={}", this, this.owner, res.getCode());
        } catch (Exception e) {
            LOGGER.error("DbTaskProxy saveDbSync proxy={} actor={}, ", this, this.owner, e);
        }
    }

    /**
     * 更新操作。
     * 非互斥操作：
     * 1. INSERT -> UPDATE，标记下一个任务为UPDATE，直接调用INSERT。
     * 2. DELETE -> UPDATE，直接调用DELETE。
     * 3. UPDATE -> DELETE，超时情况下，触发delete操作。
     * 4. NONE -> UPDATE，UPDATE没被禁止，触发更新。
     * 5. UPDATE -> UPDATE，UPDATE没被禁止
     * <p>
     * 5.1. UPDATE未被禁止，超时情况下触发更新。
     * 5.2. UPDATE未被禁止，未超时情况下，标记下一阶段为更新操作。
     * 5.3. UPDATE被禁止，跳过。
     *
     * @throws GeminiException 前置状态不合法。
     */
    private void doUpdate() {
        LOGGER.info("DbTaskProxy doUpdate {} {}", this.owner, this.name);
        final int status = this.getStatus();
        // INSERT -> UPDATE，标记接下来UPDATE，触发INSERT。
        if (status == TASK_QUEUE_STATUS_INSERT) {
            this.setNextStatus(TASK_QUEUE_STATUS_UPDATE);
            this.insert();
            return;
        }
        // DELETE -> UPDATE，触发DELETE。
        if (status == TASK_QUEUE_STATUS_DELETE) {
            this.delete();
            return;
        }
        final long nowTsMs = SystemClock.nowNative();
        // 当前更新任务超时
        if (status == TASK_QUEUE_STATUS_UPDATE && this.lastTaskTsMs <= nowTsMs) {
            this.finishTask(this.taskId);
            // 合并update
            if (this.getStatus() == TASK_QUEUE_STATUS_UPDATE) {
                return;
            }
            // 重新执行update
            this.doUpdate();
            return;
        }
        // 状态不符合预期。
        if (status != TASK_QUEUE_STATUS_NONE && status != TASK_QUEUE_STATUS_UPDATE) {
            throw new GeminiException("DbTaskProxy doUpdate update when {}", getStringStatus(status));
        }
        // 禁止更新
        if (this.isForbidUpdateAnymore()) {
            LOGGER.error("DbTaskProxy doUpdate isForbidUpdateAnymore, actor={} proxy={}", this.owner, this);
            return;
        }
        // UPDATE -> UPDATE, 未超时
        if (status == TASK_QUEUE_STATUS_UPDATE && this.lastTaskTsMs > nowTsMs) {
            this.setNextStatus(TASK_QUEUE_STATUS_UPDATE);
            return;
        }
        // NONE/UPDATE -> UPDATE 超时
        this.taskId += 1;
        // 状态标记
        this.setStatus(TASK_QUEUE_STATUS_UPDATE);
        this.lastTaskTsMs = nowTsMs + this.timeoutTimeMs;
        // 发出db请求
        final IDbChangeUpsertStrategy.Request request = this.upsertStrategy.buildUpdateRequest(this.owner);

        final GeneratedMessageV3 checkAttr = request.checkAttr;
        final boolean isFull = request.isFull;
        final boolean isChange = request.isChanged;
        final UpdateOption.Builder option = UpdateOption.newBuilder();
        if (checkAttr != null) {
            option.withResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL);
        }
        final int dirtyId = this.upsertStrategy.getDirtyId();
        final UpdateAsk<Message.Builder> ask = new UpdateAsk<>(request.req, option.build());
        this.dbOperation.updateAsync(this.owner, ask, (result, err) -> {
            this.finishTask(taskId);
            if (err != null) {
                LOGGER.error("DbTaskProxy doUpdate update fail, taskId={}, actor={}, proxy={}, full={}, change={}",
                        taskId, this.owner, this, isFull, isChange, err);
                return;
            }
            this.onUpdateCallback(taskId, isFull, isChange, result, checkAttr, dirtyId);
        });
    }

    private void onUpdateCallback(
            final int taskId,
            final boolean isFull,
            final boolean isChange,
            final UpdateResult<Message.Builder> res,
            final GeneratedMessageV3 checkAttr,
            final int dirtyId
    ) {
        if (!DbUtil.isOk(res.getCode())) {
            // 出现异常 || Tcaplus code不对
            LOGGER.error("DbTaskProxy onUpdateCallback fail, taskId={}, code={}, actor={}, proxy={}, full={}, change={}",
                    taskId, res.getCode(), this.owner, this, isFull, isChange);
            return;
        }
        final boolean isOk = this.upsertStrategy.checkDbResultWhenNeeded(this.owner, res, checkAttr);
        if (!isOk) {
            WechatLog.error("DbTaskProxy onUpdateCallback assertUpdateRight taskId={}, actor={}, proxy={}, isFull={}, isChange={}",
                    taskId, this.owner, this, isFull, isChange);
        }
        // 清空lastFullAttr
        if (this.taskId == taskId) {
            this.upsertStrategy.clearDirtyWhenSaveOk(this.owner, dirtyId);
        } else {
            LOGGER.warn("DbTaskProxy onUpdateCallback timeout, taskId={}, code={}, actor={}, proxy={}, full={}, change={}",
                    taskId, res.getCode(), this.owner, this, isFull, isChange);
        }
        LOGGER.info("DbTaskProxy onUpdateCallback taskId={}, actor={}, proxy={}, isFull={}, isChange={}, code={}",
                taskId, this.owner, this, isChange, isChange, res.getCode());
    }

    /**
     * [异步]删除（含重试）。
     */
    public void delete() {
        LOGGER.info("DbTaskProxy delete {} {}", this.owner, this.name);
        if (this.isProxyStop()) {
            WechatLog.error("DbTaskProxy delete isProxyStop, actor={} proxy={}", this.owner, this);
            return;
        }
        if (this.updateSpeedController != null) {
            this.updateSpeedController.stopTimer();
        }
        this.doDelete();
    }

    /**
     * 删除操作，删除操作和其他操作是互斥的，必须保证其他的完成。
     * 删除操作执行后，会禁止后续的UPDATE发生。
     * <p>
     * 1. NONE -> DELETE，执行删除操作。
     * 2. UPDATE -> DELETE，超时直接执行删除操作，否则标记下一个行为为DELETE。
     * 3. DELETE -> DELETE，超时则再次执行，否则跳过。
     * 4. INSERT -> DELETE，立即完成INSERT，并立即执行DELETE。
     */
    private void doDelete() {
        LOGGER.info("DbTaskProxy doDelete {} {}", this.owner, this.name);
        final int status = this.getStatus();
        // 禁止更新
        this.forbidUpdateAnymore();
        // 禁止插入
        this.forbidInsertAnymore();
        final long nowTsMs = SystemClock.nowNative();
        // UPDATE -> DELETE
        if (status == TASK_QUEUE_STATUS_UPDATE && this.lastTaskTsMs > nowTsMs) {
            this.setNextStatus(TASK_QUEUE_STATUS_DELETE);
            return;
        }
        // DELETE -> DELETE 未超时，跳过。
        if (status == TASK_QUEUE_STATUS_DELETE && this.lastTaskTsMs > nowTsMs) {
            LOGGER.info("DbTaskProxy doDelete when not timeout");
            return;
        }
        // INSERT -> DELETE 立即完成INSERT，并立即执行DELETE。
        if (status == TASK_QUEUE_STATUS_INSERT) {
            this.setNextStatus(TASK_QUEUE_STATUS_NONE);
        }
        // 当前任务超时
        if (status != TASK_QUEUE_STATUS_NONE) {
            this.finishTask(this.taskId);
            // 当前任务是DELETE，跳过
            if (this.getStatus() == TASK_QUEUE_STATUS_DELETE) {
                return;
            }
            // 执行delete
            this.doDelete();
            return;
        }
        // NONE/DELETE/UPDATE -> DELETE
        this.taskId += 1;
        // 状态标记
        this.setStatus(TASK_QUEUE_STATUS_DELETE);
        this.setNextStatus(TASK_QUEUE_STATUS_NONE);
        this.lastTaskTsMs = nowTsMs + this.timeoutTimeMs;
        if (this.updateSpeedController != null) {
            this.updateSpeedController.stopTimer();
        }

        Message.Builder request = this.deleteStrategy.buildDeleteRequest(this.owner);
        final DeleteAsk<Message.Builder> ask = new DeleteAsk<>(request, DeleteOption.newBuilder().withRetry().build());
        final int taskId = this.taskId;
        this.dbOperation.deleteAsync(this.owner, ask, (result, err) -> {
            if (err != null) {
                LOGGER.error("DbTaskProxy doDelete delete async fail, taskId={} actor={} proxy={}, ", taskId, this.owner, this, err);
                return;
            }
            if (DbUtil.isOk(result.getCode())) {
                this.finishTask(taskId);
                return;
            }
            // 兜底：重复删除
            if (DbUtil.isRecordNotExist(result.getCode())) {
                this.finishTask(taskId);
                WechatLog.error("DbTaskProxy doDelete delete fail, record is not exist, taskId={} actor={} proxy={}", taskId, this.owner, this);
                return;
            }
            LOGGER.error("DbTaskProxy doDelete delete fail, taskId={}, code={}, actor={} proxy={}", taskId, result.getCode(), this.owner, this);
        });
    }


    /**
     * 完成Task的操作。
     * (调用要注意)
     *
     * @param taskId 标识taskId。
     */
    private void finishTask(final int taskId) {
        LOGGER.info("DbTaskProxy finishTask {}  {} taskId={}", this.owner, this.name, taskId);
        if (this.isProxyStop()) {
            LOGGER.warn("DbTaskProxy finishTask isProxyStop proxy={} taskId={}", this, taskId);
            return;
        }
        final int status = this.getStatus();
        if (status == TASK_QUEUE_STATUS_NONE) {
            LOGGER.warn("DbTaskProxy finishTask timeout, proxy={} want={}", this, taskId);
            return;
        }
        if (this.taskId != taskId) {
            LOGGER.warn("DbTaskProxy finishTask timeout, proxy={} want={}", this, taskId);
            return;
        }
        // 状态标记。
        this.lastTaskTsMs = 0;
        this.setStatus(TASK_QUEUE_STATUS_NONE);
        this.fireNextTask();
    }

    /**
     * 测试finishTask，仅测试用例可调用
     *
     * @param taskId 标识taskId
     */
    public void testFinishTask(final int taskId) {
        this.finishTask(taskId);
    }

    /**
     * 当前的TaskId，仅测试用例可用
     *
     * @return task id。
     */
    public int getCurrentTaskId() {
        return this.taskId;
    }

    /**
     * 获取当前状态。
     *
     * @return 状态。
     */
    public int getStatus() {
        return this.mark & 0xFF;
    }

    /**
     * proxy是否关闭。
     *
     * @return true 关闭; false 未关闭。
     */
    public boolean isProxyStop() {
        return (this.mark & TASK_QUEUE_STATUS_STOP) != 0;
    }

    /**
     * 获取下一个状态。
     *
     * @return 下一个状态。
     */
    public int getNextStatus() {
        return (this.mark & 0xFF00) >> 8;
    }

    /**
     * 结束运行
     */
    public void stop() {
        LOGGER.info("DbTaskProxy stop {} {}", this.owner, this.name);
        if (this.isProxyStop()) {
            WechatLog.error("DbTaskProxy stop isProxyStop, actor={} proxy={}", this.owner, this);
            return;
        }
        // 如果存在限频器，关闭运行
        if (this.updateSpeedController != null) {
            this.updateSpeedController.stopTimer();
        }
        this.stopProxy();
        this.forbidUpdateAnymore();
        this.setStatus(TASK_QUEUE_STATUS_NONE);
        this.setNextStatus(TASK_QUEUE_STATUS_NONE);
    }

    /**
     * @return true or false。
     */
    public boolean isForbidUpdateAnymore() {
        return (this.mark & TASK_QUEUE_STATUS_NOT_UPDATE_ANY_MORE) != 0;
    }

    private void stopProxy() {
        this.mark = TASK_QUEUE_STATUS_STOP | this.mark;
    }

    /**
     * 禁止更新。
     */
    private void forbidUpdateAnymore() {
        this.mark = TASK_QUEUE_STATUS_NOT_UPDATE_ANY_MORE | this.mark;
    }


    /**
     * 已禁止插入。
     */
    public boolean isForbidInsertAnymore() {
        return (this.mark & TASK_QUEUE_STATUS_NOT_INSERT_ANY_MORE) != 0;
    }

    /**
     * 禁止插入。
     */
    private void forbidInsertAnymore() {
        this.mark = TASK_QUEUE_STATUS_NOT_INSERT_ANY_MORE | this.mark;
    }

    /**
     * 触发下一阶段任务。
     */
    private void fireNextTask() {
        final int status = this.getStatus();
        if (status != TASK_QUEUE_STATUS_NONE) {
            throw new GeminiException("DbTaskProxy fireNextTask last status={} not right", getStringStatus(status));
        }
        final int nextStatus = this.getNextStatus();
        if (nextStatus == TASK_QUEUE_STATUS_NONE) {
            return;
        }
        this.setNextStatus(TASK_QUEUE_STATUS_NONE);
        switch (nextStatus) {
            case TASK_QUEUE_STATUS_UPDATE:
                this.update();
                break;
            case TASK_QUEUE_STATUS_DELETE:
                this.delete();
                break;
            default:
                throw new GeminiException("DbTaskProxy fireNextTask next status={} not right", getStringStatus(nextStatus));
        }
    }

    /**
     * @param status 状态。
     * @return 字符串描述。
     */
    private static String getStringStatus(int status) {
        return switch (status) {
            case TASK_QUEUE_STATUS_INSERT -> "Insert";
            case TASK_QUEUE_STATUS_NONE -> "None";
            case TASK_QUEUE_STATUS_DELETE -> "Delete";
            case TASK_QUEUE_STATUS_UPDATE -> "Update";
            default ->
                    throw new GeminiException("DbTaskProxy fireNextTask getStringStatus status={} not implement", status);
        };
    }

    /**
     * 设置当前状态。
     *
     * @param status 状态。
     */
    private void setStatus(final int status) {
        this.mark = (this.mark & TASK_QUEUE_STATUS_MARK) | status;
    }

    /**
     * 设置下一个状态。
     *
     * @param status 状态。
     */
    private void setNextStatus(final int status) {
        this.mark = (this.mark & TASK_QUEUE_NEXT_STATUS_MARK) | (status << 8);
    }

    @Override
    public String toString() {
        return "DbTaskProxy{" +
                "status=" + getStringStatus(this.getStatus()) +
                ", nextStatus=" + getStringStatus(this.getNextStatus()) +
                ", lastTaskTsMs=" + lastTaskTsMs +
                ", taskId=" + taskId +
                ", isForbidUpdate=" + this.isForbidUpdateAnymore() +
                ", name=" + this.name +
                '}';
    }

    public static class Builder {
        private String name = null;
        private IDbChangeDeleteStrategy deleteStrategy = null;
        private IDbChangeUpsertStrategy upsertStrategy = null;
        private IDbOperationStrategy operationStrategy = null;
        /**
         * 超时时间。
         */
        private long timeoutTimeMs = TimeUnit.SECONDS.toMillis(5);
        /**
         * 拥有者。
         */
        private AbstractActor owner;
        /**
         * 限流
         */
        private int intervalMs = 0;

        private String entityId = "0";
        /**
         * Timer的拥有者。
         */
        private IActorWithTimer actorTimerOwner;

        private Builder() {
        }

        /**
         * 别名。
         *
         * @param name 别名。
         * @return Builder。
         */
        public Builder name(final String name) {
            if (name == null) {
                throw new NullPointerException("DbTaskProxy Builder name is null");
            }
            this.name = name;
            return this;
        }

        /**
         * 操作DB的策略。
         *
         * @param operation 策略。
         * @return Builder。
         */
        public Builder operation(IDbOperationStrategy operation) {
            if (operation == null) {
                throw new NullPointerException("DbTaskProxy Builder operation is null");
            }
            this.operationStrategy = operation;
            return this;
        }


        /**
         * 更新插入操作策略。
         *
         * @param updateStrategy 策略。
         * @return Builder。
         */
        public Builder upsert(IDbChangeUpsertStrategy updateStrategy) {
            if (updateStrategy == null) {
                throw new NullPointerException("DbTaskProxy Builder upsertStrategy is null");
            }
            this.upsertStrategy = updateStrategy;
            return this;
        }

        /**
         * 删除策略。
         *
         * @param deleteStrategy 策略。
         * @return Builder。
         */
        public Builder delete(IDbChangeDeleteStrategy deleteStrategy) {
            if (deleteStrategy == null) {
                throw new NullPointerException("DbTaskProxy Builder deleteStrategy is null");
            }
            this.deleteStrategy = deleteStrategy;
            return this;
        }

        /**
         * 超时时间。（ms）
         *
         * @param timeoutTimeMs 超时时间。
         * @return Builder。
         */
        public Builder retryTimeoutMs(final long timeoutTimeMs) {
            if (timeoutTimeMs < 0) {
                throw new IllegalArgumentException("DbTaskProxy Builder timeoutTimeMs " + timeoutTimeMs);
            }
            this.timeoutTimeMs = timeoutTimeMs;
            return this;
        }

        /**
         * proxy的拥有者。
         *
         * @param actor 拥有者。
         * @return Builder。
         */
        public Builder owner(final AbstractActor actor) {
            this.owner = actor;
            return this;
        }

        /**
         * proxy的限流
         *
         * @param intervalMs 间隔时间ms
         * @return Builder。
         */
        public Builder intervalMs(final int intervalMs) {
            this.intervalMs = intervalMs;
            return this;
        }

        /**
         * entityId
         *
         * @param entityId entityId
         * @return Builder。
         */
        public Builder entityId(final String entityId) {
            this.entityId = entityId;
            return this;
        }


        /**
         * Timer的拥有者。
         *
         * @param actorTimerOwner Timer拥有者。
         * @return Builder。
         */
        public Builder limitTimerOwner(final IActorWithTimer actorTimerOwner) {
            this.actorTimerOwner = actorTimerOwner;
            return this;
        }

        /**
         * 构造。
         *
         * @return task queue对象。
         */
        public DbTaskProxy build() {
            return new DbTaskProxy(this);
        }
    }

    public static Builder newBuilder() {
        return new Builder();
    }
}

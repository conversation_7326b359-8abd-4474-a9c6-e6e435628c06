package com.yorha.common.utils;

import com.yorha.common.actor.dispatcher.IMailboxDispatcher;
import com.yorha.common.actorservice.ActorMetaData;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;

/**
 * <AUTHOR>
 */
public class ActorConfigUtils {

    public static ActorMetaData getActorMetaData(final String actorRole) {
        ActorSystem actorSystem = ServerContext.getActorSystem();
        if (actorSystem == null) {
            throw new GeminiException("ActorConfigUtils getActorMetaData failed, actorSystem is null. actorRole={}", actorRole);
        }
        return actorSystem.getActorMetaDataMgr().getActorMetaData(actorRole);
    }

    public static IMailboxDispatcher getDispatcher(final String actorRole) {
        ActorSystem actorSystem = ServerContext.getActorSystem();
        if (actorSystem == null) {
            throw new GeminiException("ActorConfigUtils getDispatcher failed, actorSystem is null. actorRole={}", actorRole);
        }
        return actorSystem.getDispatcher(actorRole);
    }

    public static int getActorThreadNum(String actorRole) {
        IMailboxDispatcher dispatcher = getDispatcher(actorRole);
        return dispatcher.getParallelism();
    }


}

package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.DeleteOption;
import org.reactivestreams.Publisher;

/**
 * <AUTHOR>
 */
public class MongoDelete<T extends Message.Builder> extends MongoOperation<T, DeleteOption, DeleteResult, DeleteResult, com.yorha.common.db.tcaplus.result.DeleteResult> {
    public MongoDelete(MongoDatabase database, T t, DeleteOption deleteOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, deleteOption);
    }

    @Override
    protected NormalSubscriber<DeleteResult> getSubscriber() {
        return new NormalSubscriber<>();
    }

    @Override
    protected Publisher<DeleteResult> getPublisher() {
        return this.database.getCollection(this.getTableName()).deleteOne(DocumentHelper.formKey(this.getReq()));

    }

    @Override
    protected com.yorha.common.db.tcaplus.result.DeleteResult buildResult(DeleteResult deleteResult) {
        if (!deleteResult.wasAcknowledged()) {
            //TODO(JOSEFREN): dbPerf
            return new com.yorha.common.db.tcaplus.result.DeleteResult(TcaplusErrorCode.SVR_ERR_FAIL_DELETE_RECORD.getValue());
        }
        if (deleteResult.getDeletedCount() == 0) {
            return new com.yorha.common.db.tcaplus.result.DeleteResult(TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST.getValue());
        }
        return new com.yorha.common.db.tcaplus.result.DeleteResult(TcaplusErrorCode.GEN_ERR_SUC.getValue());
    }

    @Override
    protected com.yorha.common.db.tcaplus.result.DeleteResult onMongoError() {
        return new com.yorha.common.db.tcaplus.result.DeleteResult(DEFAULT_ERROR_CODE.getValue());
    }
}

package com.yorha.common.server.config;

import com.yorha.common.actor.node.TempWhiteListConfig;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.json.JsonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 临时白名单配置
 * zeo
 */
public class WhiteListConfig {
    private static final Logger LOGGER = LogManager.getLogger(WhiteListConfig.class);
    private final Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteAccount;
    private final Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteDevice;
    private final Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteIp;

    public WhiteListConfig(
            Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteAccount,
            Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteDevice,
            Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteIp
    ) {
        this.etcdWhiteAccount = Collections.unmodifiableMap(etcdWhiteAccount);
        this.etcdWhiteDevice = Collections.unmodifiableMap(etcdWhiteDevice);
        this.etcdWhiteIp = Collections.unmodifiableMap(etcdWhiteIp);
    }

    public Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> getEtcdWhiteAccount() {
        return etcdWhiteAccount;
    }

    public Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> getEtcdWhiteDevice() {
        return etcdWhiteDevice;
    }

    public Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> getEtcdWhiteIp() {
        return etcdWhiteIp;
    }

    public static WhiteListConfig valueOf(List<TempWhiteListConfig.WhiteConfig> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("ServerContext  loadWhiteConfig").append(" : ").append("\n");
        Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteAccount = new HashMap<>();
        Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteDevice = new HashMap<>();
        Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> etcdWhiteIp = new HashMap<>();
        for (TempWhiteListConfig.WhiteConfig template : list) {
            if (template.getWorldId() != ServerContext.getWorldId()) {
                continue;
            }
            if (!StringUtils.isEmpty(template.getDeviceId())) {
                int templateZoneId = template.getZoneId();
                Map<String, TempWhiteListConfig.WhiteConfig> map = etcdWhiteDevice.computeIfAbsent(templateZoneId, k -> new HashMap<>());
                map.put(template.getDeviceId(), template);
            }
            if (!StringUtils.isEmpty(template.getIp())) {
                int templateZoneId = template.getZoneId();
                Map<String, TempWhiteListConfig.WhiteConfig> map = etcdWhiteIp.computeIfAbsent(templateZoneId, k -> new HashMap<>());
                map.put(template.getIp(), template);
            }
            if (!StringUtils.isEmpty(template.getOpenId())) {
                int templateZoneId = template.getZoneId();
                Map<String, TempWhiteListConfig.WhiteConfig> map = etcdWhiteAccount.computeIfAbsent(templateZoneId, k -> new HashMap<>());
                map.put(template.getOpenId(), template);
            }
            sb.append(JsonUtils.toJsonString(template)).append("\n");
        }
        LOGGER.info(sb);
        return new WhiteListConfig(etcdWhiteAccount, etcdWhiteDevice, etcdWhiteIp);
    }

    @Override
    public String toString() {
        return "WhiteListConfig{" +
                "etcdWhiteAccount=" + etcdWhiteAccount +
                ", etcdWhiteDevice=" + etcdWhiteDevice +
                ", etcdWhiteIp=" + etcdWhiteIp +
                '}';
    }
}

package com.yorha.common.resource;

import com.google.common.collect.Sets;
import com.yorha.common.actor.msg.ActorReLoadResMsg;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;

/**
 * 资源加载器（保证原子替换ResHolder）
 * 工具类，不可实例化
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public final class ResLoader {
    private static final Logger LOGGER = LogManager.getLogger(ResLoader.class);

    private static String resPath;

    /**
     * 禁止实例化
     */
    private ResLoader() {
    }

    /**
     * 实际的资源持有器
     */
    private static volatile ResHolder resHolder = null;

    public static ResHolder getResHolder() {
        return resHolder;
    }

    /**
     * 加载之前
     */
    private static void beforeLoad(String resPath) {
        LOGGER.info("try load res path:{}", resPath);
        ResLoader.resPath = resPath;
    }

    /**
     * 加载资源
     *
     * @param resPath 资源目录
     */
    public static synchronized void load(String resPath) {
        long startTime = SystemClock.now();
        beforeLoad(resPath);
        ResHolder newResHolder = new ResHolder();
        try {
            newResHolder.loadWithConfig(resPath);
        } catch (ResourceException resourceException) {
            String format = StringUtils.format("\t配置表加载失败\n{}", resourceException.getMessage());
            WechatLog.error(format, resourceException);
            throw new GeminiException("load resource fail");
        }
        afterLoad(startTime, newResHolder);
        LOGGER.info("load success, interval: {} ms", SystemClock.now() - startTime);
    }

    /**
     * 加载之后执行
     */
    private static void afterLoad(long startTime, ResHolder newResHolder) {
        final ResHolder oldHolder = resHolder;
        final boolean isInitial = resHolder == null;
        resHolder = newResHolder;
        if (ServerContext.isDirServer()) {
            return;
        }
        if (!isInitial) {
            Set<Class<? extends IResTemplate>> updatedTemplates = diff(oldHolder, newResHolder);
            if (updatedTemplates.isEmpty()) {
                LOGGER.warn("ResLoader afterLoad but no changed");
                return;
            }
            BroadcastHelper.toLocalAllGameActor(new ActorReLoadResMsg(updatedTemplates));
        }
    }

    private static Set<Class<? extends IResTemplate>> diff(ResHolder old, ResHolder new0) {
        Set<Class<? extends IResTemplate>> ret = Sets.newHashSet();
        old.xmlMd5Map.forEach((clazz, oldMd5) -> {
            String newMd5 = new0.xmlMd5Map.get(clazz);
            if (newMd5 == null || !newMd5.equals(oldMd5)) {
                ret.add(clazz);
            }
        });
        return ret;
    }

    /**
     * 重新加载，使用原先的资源目录
     */
    public static void reload() {
        LOGGER.info("config reload start");
        ResLoader.load(ResLoader.resPath);
        LOGGER.info("config reload end");
    }

}

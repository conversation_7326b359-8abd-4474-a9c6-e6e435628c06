package com.yorha.common.db.tcaplus;

import com.yorha.gemini.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class TcaplusDBException extends RuntimeException {
    public TcaplusDBException(String message) {
        super(message);
    }

    public TcaplusDBException(TcaplusErrorCode errorCode) {
        this(StringUtils.format("{} error: {}", errorCode, errorCode.getValue()));
    }

    public TcaplusDBException(String message, Throwable cause) {
        super(message, cause);
    }

    public TcaplusDBException(Throwable cause) {
        super(cause);
    }
}

package com.yorha.common.server;

import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.PatchInterface;
import com.yorha.common.etcd.EtcdClient;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.LoginHelper;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.perf.ActorPerfLogger;
import com.yorha.common.perf.ProcessPerfLogger;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.config.ServerInfo;
import com.yorha.common.server.config.WhiteListConfig;
import com.yorha.common.server.serversetting.ServerSetting;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 服务器context
 * 静态工具类，所有方法和变量都是static，不使用单例为了减少一层函数调用，更高效
 *
 * <AUTHOR>
 */
public class ServerContext {
    /**
     * 进程启动时间
     */
    public static final long PROC_START_TIME_MS = SystemClock.nowNative();
    private static final Logger LOGGER = LogManager.getLogger(ServerContext.class);
    /**
     * 调式开关，单进程
     */
    private static final ServerDebugOption SERVER_DEBUG_OPTION = new ServerDebugOption();
    private static final ProcessPerfLogger PROCESS_PERF_LOGGER = new ProcessPerfLogger();
    private static final ActorPerfLogger ACTOR_PERF_LOGGER = new ActorPerfLogger();
    private static EtcdClient etcdClient;
    /**
     * 进程服务器实例
     */
    private static AbstractServer server;
    /**
     * 版本号
     */
    private static String logicTag;
    private static String gitBranch = "";
    /**
     * 服务器信息（不可变）一旦初始化后，不再修改
     */
    private static ServerInfo serverInfo;
    /**
     * 服务器配置
     */
    //private static volatile ServerCfg serverCfg;
    /**
     * 服务器信息（可变）
     */
    private static volatile ServerSetting serverSetting = new ServerSetting(ServerOpenStatus.CLOSE);
    /**
     * 临时白名单 （可变）
     */
    private static volatile WhiteListConfig whiteListConfig;
    /**
     * worldId的string版本，避免每次new
     */
    private static volatile String worldIdStr = null;
    /**
     * actorSystem
     */
    private static ActorSystem actorSystem;
    private static LoginHelper loginHelper;
    /**
     * 停服步骤
     */
    private static volatile int serverStopStep = 0;
    private static volatile Set<CommonEnum.ModuleEnum> globalLockedFeatures;

    private static volatile PatchInterface patchInterface;

    private ServerContext() {
    }

    public static void setPatch(PatchInterface patch) {
        ServerContext.patchInterface = patch;
    }

    public static PatchInterface getPatchInterface() {
        return patchInterface;
    }

    public static int getWorldId() {
        return getServerInfo().getWorldId();
    }

    public static String getWorldIdStr() {
        if (worldIdStr == null) {
            worldIdStr = String.valueOf(getWorldId());
        }
        return worldIdStr;
    }

    public static String getBusId() {
        return getServerInfo().getBusId();
    }

    public static int getInstanceId() {
        return getServerInfo().getInstanceId();
    }

    public static int getZoneId() {
        if (isZoneServer()) {
            return getServerInfo().getZoneId();
        } else {
            throw new GeminiException("ServerContext getZoneId fail, cur server is not ZoneServer");
        }
    }

    public static int getServerStopStep() {
        return serverStopStep;
    }

    public static void setServerStopStep(int serverStopStep) {
        ServerContext.serverStopStep = serverStopStep;
    }

    /**
     * 判断是否同一个进程节点
     */
    public static boolean isSameNode(String busId) {
        return busId != null && busId.equals(getServerInfo().getBusId());
    }

    public static WorldEnvFlag getEnv() {
        return WorldEnvFlag.forName(ClusterConfigUtils.getWorldConfig().getStringItem("env_flag"));
    }

    public static boolean isDevEnv() {
        return getEnv() == WorldEnvFlag.DEV;
    }

    public static boolean isTestEnv() {
        return getEnv() == WorldEnvFlag.TEST;
    }

    public static boolean isProdSvr() {
        return getEnv() == WorldEnvFlag.PROD;
    }

    public static boolean isPressEnv() {
        if (isZoneServer()) {
            return getZoneId() == 46;
        }
        return false;
    }

    public static ProcessPerfLogger getProcessPerfLogger() {
        return PROCESS_PERF_LOGGER;
    }

    public static ActorPerfLogger getActorPerfLogger() {
        return ACTOR_PERF_LOGGER;
    }

    public static AbstractServer getServer() {
        if (server == null) {
            throw new GeminiException("server is null");
        }
        return server;
    }

    /**
     * 设置唯一的server实例
     */
    public static void setServer(AbstractServer abstractServer) {
        if (server != null) {
            throw new GeminiException(StringUtils.format("server already exist {} {}", server, abstractServer));
        }
        server = abstractServer;
    }

    public static String getLogicTag() {
        return ServerContext.logicTag;
    }

    /**
     * 设置版本号
     */
    public static void setLogicTag(String logicTag) {
        ServerContext.logicTag = logicTag;
        serverInfo.setServerVersion(logicTag);
        for (String p : logicTag.trim().split("\n")) {
            if (p.trim().startsWith("code_git_branch")) {
                String[] kv = p.trim().split(":");
                gitBranch = kv[1].trim();
                MonitorUnit.GIT_BRANCH.labels(ServerContext.getBusId(), gitBranch).set(SystemClock.nowNative());
                LOGGER.info("ServerContext setLogicTag set gitBranch: {}", gitBranch);
            }
            if (p.trim().startsWith("docker_tag")) {
                String[] kv = p.trim().split(":");
                final String version = kv[1].trim();
                MonitorUnit.VERSION.labels(ServerContext.getBusId(), version).set(SystemClock.nowNative());
                LOGGER.info("ServerContext setLogicTag set version: {}", version);
            }
        }
        LOGGER.info("set logicTag:{}", logicTag);
    }

    public static String getGitBranch() {
        return gitBranch;
    }

    /**
     * 初始化ServerInfo，不再变化
     */
    public static void initServerInfo(String busId) {
        if (serverInfo != null) {
            LOGGER.error("already has serverInfo:{}", serverInfo);
        }
        final String[] busids = busId.split("\\.");
        final int worldId = Integer.parseInt(busids[0]);
        final int zoneId = Integer.parseInt(busids[1]);
        final int serverTypeId = Integer.parseInt(busids[2]);
        final int instanceId = Integer.parseInt(busids[3]);
        if (instanceId <= 0) {
            throw new GeminiException("ServerContext instanceId not ok {}", instanceId);
        }
        serverInfo = new ServerInfo(worldId, zoneId, serverTypeId, instanceId);
        LOGGER.info("new {}", serverInfo);
    }

    public static void loadServerCfg() {
        // FIXME: 错误处理，cfg路径可配
        //serverCfg = ServerCfg.load("cfg/server.yaml");
        //LOGGER.info("load server cfg {} ok", "cfg/server.yaml");
    }

    public static void initServerSetting(ServerOpenStatus status) {
        serverSetting = new ServerSetting(status);
    }

    public static EtcdClient getEtcdClient() {
        return etcdClient;
    }

    public static void setEtcdClient(EtcdClient etcdClient) {
        ServerContext.etcdClient = etcdClient;
    }

    public static ServerInfo getServerInfo() {
        return serverInfo;
    }

    //public static ServerCfg getServerCfg() {
    //    return serverCfg;
    //}


    public static ServerSetting getServerSetting() {
        return serverSetting;
    }

    public static ActorSystem getActorSystem() {
        return ServerContext.actorSystem;
    }

    public static void setActorSystem(ActorSystem actorSystem) {
        ServerContext.actorSystem = actorSystem;
    }

    public static int getCloseAnnouncementId() {
        // 客户端不再依赖，仅需要知道是close，就会去拉intl公告，后台不再拥有此变量
        return 0;
//        return ClusterConfigUtils.getWorldConfig().getIntItem("server_close_announcement_id");
    }

    public static long getOpenAnnouncementTsMs() {
        // 客户端不再依赖，仅需要知道是close，就会去拉intl公告，后台不再拥有此变量
        return 0L;
//        return ClusterConfigUtils.getWorldConfig().getLongItem("server_open_announcement_ts_ms");
    }

    public static WhiteListConfig getWhiteListConfig() {
        return whiteListConfig;
    }

    public static void setWhiteListConfig(WhiteListConfig config) {
        LOGGER.info("gemini_system ServerContext setWhiteListConfig");
        whiteListConfig = config;
    }

    public static void setGlobalFeatureLock(List<CommonEnum.ModuleEnum> lockedFeatures) {
        LOGGER.info("gemini_system ServerContext setGlobalFeatureLock :{}", lockedFeatures);
        globalLockedFeatures = Collections.unmodifiableSet(new HashSet<>(lockedFeatures));

    }

    public static Set<CommonEnum.ModuleEnum> getGlobalLockedFeatures() {
        return globalLockedFeatures;
    }

    public static void setLoginHelper() {
        loginHelper = new LoginHelper();
    }

    public static LoginHelper getLoginHelper() {
        return loginHelper;
    }

    public static ServerDebugOption getServerDebugOption() {
        return SERVER_DEBUG_OPTION;
    }

    private static boolean isPortRuleForType() {
        return ClusterConfigUtils.getWorldConfig().getIntItem("port_rule") == 1;
    }

    public static int getInnerPort() {
        int start = ClusterConfigUtils.getWorldConfig().getIntItem("server_inner_port_start");
        if (isPortRuleForType()) {
            return start + getServerInfo().getServerTypeId() * 10;
        }
        return start + getServerInfo().getZoneId() % 10 + getServerInfo().getServerTypeId() * 10 + getServerInfo().getInstanceId() % 10;
    }

    public static int getWebPort() {
        int start = ClusterConfigUtils.getWorldConfig().getIntItem("server_web_port_start");
        if (isPortRuleForType()) {
            return start + getServerInfo().getServerTypeId() * 10;
        }
        return start + getServerInfo().getZoneId() % 10 + getServerInfo().getServerTypeId() * 10 + getServerInfo().getInstanceId() % 10;
    }

    public static int getMonitorPort() {
        int start = ClusterConfigUtils.getWorldConfig().getIntItem("server_monitor_port_start");
        if (isPortRuleForType()) {
            return start + getServerInfo().getServerTypeId() * 10;
        }
        return start + getServerInfo().getZoneId() % 10 + getServerInfo().getServerTypeId() * 10 + getServerInfo().getInstanceId() % 10;
    }

    public static int getRpcTimeout() {
        return ClusterConfigUtils.getWorldConfig().getIntItem("rpc_timeout");
    }

    public static boolean checkChangeAttr() {
        return ClusterConfigUtils.getWorldConfig().getBooleanItem("check_change_attr");
    }

    public static String getGameDataLocalPath() {
        //return getServerCfg().getGameDataPath();
        return ClusterConfigUtils.getWorldConfig().getStringItem("game_data_local_path");
    }

    public static String getScriptLocalPath() {
        //return getServerCfg().getGroovyPath();
        return ClusterConfigUtils.getWorldConfig().getStringItem("script_local_path");
    }

    public static boolean isDirServer() {
        return NodeRole.isSameType(NodeRole.Dir, ServerContext.getServerInfo().getServerTypeId());
    }

    public static boolean isZoneServer() {
        return NodeRole.isSameType(NodeRole.Zone, ServerContext.getServerInfo().getServerTypeId());
    }

    public static boolean isDungeonServer() {
        return NodeRole.isSameType(NodeRole.Dungeon, ServerContext.getServerInfo().getServerTypeId());
    }

    public static boolean isGlobalServer() {
        return NodeRole.isSameType(NodeRole.Global, ServerContext.getServerInfo().getServerTypeId());
    }

    public static boolean isIdipServer() {
        return NodeRole.isSameType(NodeRole.IdIp, ServerContext.getServerInfo().getServerTypeId());
    }

    public static boolean isIosExamine() {
        try {
            return ClusterConfigUtils.getWorldConfig().getBooleanItem("ios_examine");
        } catch (Exception e) {
            LOGGER.warn("isIosExamine no config use false");
        }
        return false;
    }

    public static boolean getMonitorOpen() {
        try {
            return ClusterConfigUtils.getWorldConfig().getBooleanItem("monitor_open");
        } catch (Exception e) {
            LOGGER.warn("monitor_open no config use false");
        }
        return false;
    }

    public static boolean isBanShuSvr() {
        try {
            return ClusterConfigUtils.getWorldConfig().getBooleanItem("is_ban_shu");
        } catch (Exception e) {
            LOGGER.warn("is_ban_shu no config use false");
        }
        return false;
    }

    public static boolean isChinaVersion() {
        try {
            return ClusterConfigUtils.getWorldConfig().getBooleanItem("is_china_version");
        } catch (Exception e) {
            LOGGER.warn("is_china_version no config use false");
        }
        return false;
    }

    public static String getLogLevel() {
        Object object = ClusterConfigUtils.getWorldConfig().getObjectItem("log_level");
        if (object == null) {
            LOGGER.warn("log_level no config use default log level");
        } else if (object instanceof String) {
            return (String) object;
        }
        return "";
    }
}

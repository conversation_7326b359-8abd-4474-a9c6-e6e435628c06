package com.yorha.common.resource;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.CircleType;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.resource.resservice.whiteList.WhiteListResService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.FileUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import res.template.WhiteListTemplate;
import res.template.WhiteModelTemplate;
import res.template.WhiteVersionTemplate;

import java.io.FileInputStream;
import java.lang.reflect.*;
import java.text.ParseException;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 所有策划数据的真正拥有者
 * 包含
 * 1、原始策划数据
 * 2、二次加工后的resService数据
 *
 * <AUTHOR>
 */
public class ResHolder {
    private static final Logger LOGGER = LogManager.getLogger(ResHolder.class);

    private static final String RES_TEMPLATE_DIR = "res.template";
    private static final String RES_SERVICE_DIR = "com.yorha.common.resource.resservice";

    /**
     * const template 默认 ID
     */
    public static final int CONST_TEMPLATE_RETAIN_ID = 1;
    /**
     * 资源目录
     */
    private String resPath = null;
    /**
     * 策划表格原始数据集合
     */
    private final Map<String, Map<Integer, IResTemplate>> resTemplateDataStore = new HashMap<>();

    /**
     * xml文件的md5，用于热更配置的时候比较出哪些template发生了变更（直接
     */
    final Map<Class<? extends IResTemplate>, String> xmlMd5Map = Maps.newHashMap();
    /**
     * 策划表格二次加工后的数据集合
     */
    private final Map<Class<? extends AbstractResService>, AbstractResService> resServiceStore = new HashMap<>();

    public static ResHolder getInstance() {
        return ResLoader.getResHolder();
    }

    /**
     * 根据服务器按需加载资源
     */
    public void loadWithConfig(String resPath) throws ResourceException {
        LOGGER.info("resHolder start load path:{}", resPath);
        if (ServerContext.isZoneServer()
                || ServerContext.isDungeonServer()
                || ServerContext.isIdipServer()
                || ServerContext.isGlobalServer()) {
            // 全加载
            loadTemplate(resPath, getResTemplateClazzSet(null));
            loadService(getResServiceClazzSet(null));
        }
        if (ServerContext.isDirServer()) {
            // dir只加载白名单相关
            List<String> excelConfigTemplate = new ArrayList<>();
            excelConfigTemplate.add(WhiteListTemplate.class.getSimpleName());
            excelConfigTemplate.add(WhiteModelTemplate.class.getSimpleName());
            excelConfigTemplate.add(WhiteVersionTemplate.class.getSimpleName());
            loadTemplate(resPath, getResTemplateClazzSet(excelConfigTemplate));

            List<String> excelConfigService = new ArrayList<>();
            excelConfigService.add(WhiteListResService.class.getSimpleName());
            loadService(getResServiceClazzSet(excelConfigService));
        }
        LOGGER.info("resHolder load res end");
    }

    /**
     * 载入配置
     */
    private void loadTemplate(String resPath, Set<Class<? extends IResTemplate>> templateClazzSet) throws ResourceException {
        LOGGER.info("load template data start");
        this.resPath = resPath;

        // 加载所有的template原生数据
        for (Class<? extends IResTemplate> clazz : templateClazzSet) {
            LOGGER.info("load Template {} start", clazz.getSimpleName());
            if (Modifier.isAbstract(clazz.getModifiers())) {
                continue;
            }
            String simpleName = clazz.getSimpleName();
            if (resTemplateDataStore.containsKey(simpleName)) {
                throw new GeminiException(StringUtils.format("same template name:{}", simpleName));
            }
            Map<Integer, IResTemplate> array = loadTemplate(clazz);
            resTemplateDataStore.put(simpleName, array);
            String md5 = parseXmlMd5(clazz);
            xmlMd5Map.put(clazz, md5);
            LOGGER.info("load Template {} end", clazz.getSimpleName());
        }
        LOGGER.info("load  template data end.num:{}", templateClazzSet.size());
    }


    /**
     * 载入配置service
     */
    private void loadService(Set<Class<? extends AbstractResService>> serviceClazzSet) throws ResourceException {
        List<ResourceException> resourceExceptions = Lists.newArrayList();
        LOGGER.info("load resService data start, service info:{}", serviceClazzSet);
        // 使用ResService二次加工所有的策划数据
        for (Class<? extends AbstractResService> resServiceClazz : serviceClazzSet) {
            try {
                Constructor<? extends AbstractResService> constructor = resServiceClazz.getConstructor(ResHolder.class);
                AbstractResService resServiceInstance = constructor.newInstance(this);
                resServiceInstance.load();
                resServiceStore.put(resServiceClazz, resServiceInstance);
            } catch (InstantiationException | IllegalAccessException | NoSuchMethodException |
                    InvocationTargetException e) {
                throw new GeminiException(StringUtils.format("{} find no construct function", resServiceClazz.getSimpleName()));
            } catch (ResourceException e) {
                resourceExceptions.add(e);
            }
        }
        if (CollectionUtils.isNotEmpty(resourceExceptions)) {
            List<String> msgs = resourceExceptions.stream().map(Throwable::getMessage).collect(Collectors.toList());
            throw new ResourceException(String.join("\n", msgs));
        }
        // 二阶段checkValid
        for (AbstractResService resService : resServiceStore.values()) {
            try {
                resService.checkValid();
            } catch (ResourceException e) {
                resourceExceptions.add(e);
            }
        }
        if (CollectionUtils.isNotEmpty(resourceExceptions)) {
            List<String> msgs = resourceExceptions.stream().map(Throwable::getMessage).collect(Collectors.toList());
            throw new ResourceException(String.join("\n", msgs));
        }
        LOGGER.info("load resService data end, load size:{}", serviceClazzSet.size());
    }

    /**
     * 获取配置TemplateSet
     *
     * @param configs 需要读取的配置项 null or size = 0 都标识全量读取
     * @return 所有的ResTemplate
     */
    public static Set<Class<? extends IResTemplate>> getResTemplateClazzSet(Collection<String> configs) {
        final JavaClassScanner scanner = new JavaClassScanner();
        Set<Class<? extends IResTemplate>> subTypesSet = scanner.getSubTypesOf(RES_TEMPLATE_DIR, IResTemplate.class);
        if (configs != null) {
            Set<Class<? extends IResTemplate>> result = new HashSet<>();
            for (String name : configs) {
                for (Class<? extends IResTemplate> clazz : subTypesSet) {
                    if (clazz.getSimpleName().equalsIgnoreCase(name)) {
                        result.add(clazz);
                        break;
                    }
                }
            }
            LOGGER.info("res template num:{}", subTypesSet.size());
            return result;
        }
        LOGGER.info("res template num:{}", subTypesSet.size());
        return subTypesSet;
    }

    public <T extends AbstractResService> T innerGetResService(Class<T> clazz) {
        //noinspection unchecked
        return (T) this.resServiceStore.get(clazz);
    }

    /**
     * 获取配置Service
     *
     * @param services 指定的service类名称集合 null or size = 0 均标识全量数据
     * @return 所有的ResService
     */
    public static Set<Class<? extends AbstractResService>> getResServiceClazzSet(Collection<String> services) {
        final JavaClassScanner scanner = new JavaClassScanner();
        Set<Class<? extends AbstractResService>> subTypesSet = scanner.getSubTypesOf(RES_SERVICE_DIR, AbstractResService.class);
        if (services != null) {
            Set<Class<? extends AbstractResService>> result = new HashSet<>();
            for (String name : services) {
                for (Class<? extends AbstractResService> clazz : subTypesSet) {
                    if (clazz.getSimpleName().equalsIgnoreCase(name)) {
                        result.add(clazz);
                        break;
                    }
                }
            }
            LOGGER.info("res service num:{}", subTypesSet.size());
            return result;
        }
        LOGGER.info("res service num:{}", subTypesSet.size());
        return subTypesSet;
    }

    /**
     * 加载指定template的策划数据
     *
     * @param clazz 策划表格template
     * @return 表格实际数据map
     * @throws GeminiException 加载异常
     */
    private Map<Integer, IResTemplate> loadTemplate(Class<? extends IResTemplate> clazz) throws ResourceException {
        Map<Integer, IResTemplate> ret = new LinkedHashMap<>();
        ResXml xml = clazz.getAnnotation(ResXml.class);
        if (xml == null) {
            throw new ResourceException(StringUtils.format("配置表Template注解不存在:{}\n", clazz.getSimpleName()));
        }
        try {
            FileInputStream steam = new FileInputStream(this.resPath + "/" + xml.node());
            SAXReader reader = new SAXReader();
            Document doc = reader.read(steam);
            Element root = doc.getRootElement();
            if (!clazz.getAnnotation(ResXml.class).isConst()) {
                for (Element dataE : root.elements("entry")) {
                    IResTemplate template = this.make(clazz, dataE);
                    ret.put(template.getId(), template);
                }
            } else {
                IResTemplate template = this.makeConst(clazz, root.elements("entry"));
                ret.put(ResHolder.CONST_TEMPLATE_RETAIN_ID, template);
            }
        } catch (Exception ex) {
            String message = StringUtils.format("\t配置表:{}->{} 加载异常\n{}", xml.excel(), xml.node().replace(".xml", ""), ex.getMessage());
            throw new ResourceException(message, ex);
        }
        return ret;
    }

    private String parseXmlMd5(Class<? extends IResTemplate> clazz) throws ResourceException {
        ResXml xml = clazz.getAnnotation(ResXml.class);
        if (xml == null) {
            throw new ResourceException(StringUtils.format("配置表Template注解不存在:{}\n", clazz.getSimpleName()));
        }
        try {
            String fileText = FileUtils.readFileText(this.resPath + "/" + xml.node());
            return StringUtils.md5(fileText);
        } catch (Exception ex) {
            String message = StringUtils.format("\t配置表:{}->{} 加载异常\n{}", xml.excel(), xml.node().replace(".xml", ""), ex.getMessage());
            throw new ResourceException(message, ex);
        }
    }

    /**
     * @param elements const 类型表格的kv
     * @return 资源模板
     * @throws InstantiationException   初始化异常
     * @throws IllegalAccessException   不合法访问
     * @throws IllegalArgumentException 非法参数
     * @throws ParseException           解析异常
     */
    private IResTemplate makeConst(Class<? extends IResTemplate> clazz, List<Element> elements) throws Exception {
        IResTemplate template = clazz.getDeclaredConstructor().newInstance();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            for (Element e : elements) {
                if (e.attributeValue("name").equals(f.getName())) {
                    String value = e.attributeValue("value");
                    if (value == null) {
                        continue;
                    }
                    this.setTemplateFieldValue(template, f, value);
                    break;
                }
            }
        }
        return template;
    }

    private IResTemplate make(Class<? extends IResTemplate> clazz, Element element) throws Exception {
        IResTemplate template = clazz.getDeclaredConstructor().newInstance();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            // 没有配置的属性直接跳过
            ResAttribute a = f.getAnnotation(ResAttribute.class);
            if (a == null) {
                continue;
            }
            String value = element.attributeValue(a.value());
            if (value == null) {
                continue;
            }
            this.setTemplateFieldValue(template, f, value);
        }
        return template;
    }

    public static <T extends AbstractResService> T getResService(Class<T> clazz) {
        return ResLoader.getResHolder().innerGetResService(clazz);
    }

    /**
     * 获取某个模板的所有记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @return 返回顺序列表
     */
    @SuppressWarnings("unchecked")
    public <T extends IResTemplate> Collection<T> getListFromMap(Class<T> clazz) {
        String clazzName = ClassNameCacheUtils.getSimpleName(clazz);
        Map<Integer, T> array = (Map<Integer, T>) resTemplateDataStore.get(clazzName);
        if (array == null) {
            throw new GeminiException(StringUtils.format("配置表={}不存在. {}", clazzName, getFilename(clazz)));
        }
        return array.values();
    }

    /**
     * 获取某个模板的一条记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @param id    索引
     * @return 返回模板的一条记录
     */
    @SuppressWarnings("unchecked")
    public <T extends IResTemplate> T getValueFromMap(Class<T> clazz, int id) {
        if (id == 0) {
            String format = StringUtils.format("配置id不存在. detail->> id:0 {}不存在", getFilename(clazz));
            WechatLog.error(format);
            throw new GeminiException(format);
        }
        String clazzName = ClassNameCacheUtils.getSimpleName(clazz);
        Map<Integer, T> array = (Map<Integer, T>) resTemplateDataStore.get(clazzName);
        T result = array.get(id);
        if (result == null) {
            String format = StringUtils.format("配置id不存在. detail->> id:{} {}不存在", id, getFilename(clazz));
            WechatLog.error(format);
            throw new GeminiException(format);
        }
        return result;
    }

    public static <T extends IResTemplate> T getTemplate(Class<T> clazz, int id) throws GeminiException {
        return getInstance().getValueFromMap(clazz, id);
    }

    /**
     * 检测模板记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @param id    索引
     * @return 返回模板的一条记录
     */
    @SuppressWarnings("unchecked")
    public <T extends IResTemplate> T checkValueFromMap(Class<T> clazz, int id, Supplier<String> msg) throws ResourceException {
        String clazzName = ClassNameCacheUtils.getSimpleName(clazz);
        if (id == 0) {
            throw new ResourceException(msg == null ? StringUtils.format("id:0 {}不存在", getFilename(clazz)) : msg.get());
        }
        Map<Integer, T> array = (Map<Integer, T>) resTemplateDataStore.get(clazzName);
        T result = array.get(id);
        if (result == null) {
            throw new ResourceException(msg == null ? StringUtils.format("id:{} {}不存在", id, getFilename(clazz)) : msg.get());
        }
        return result;
    }

    /**
     * 获取 res const类型的记录,const 类型的表只有一条数据
     *
     * @param clazz 表名
     * @param <T>   模板类型
     * @return 返回const 模板
     */
    public <T extends IResConstTemplate> T getConstTemplate(Class<T> clazz) {
        return this.getValueFromMap(clazz, ResHolder.CONST_TEMPLATE_RETAIN_ID);
    }

    /**
     * 获取某个模板的一条记录，找不到也不会抛异常，而是返回null
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @param id    索引
     * @return 返回模板的一条记录
     */
    @SuppressWarnings("unchecked")
    public <T extends IResTemplate> T findValueFromMap(Class<T> clazz, int id) {
        String clazzName = ClassNameCacheUtils.getSimpleName(clazz);
        Map<Integer, T> map = (Map<Integer, T>) resTemplateDataStore.get(clazzName);
        if (map == null) {
            return null;
        }
        return map.get(id);
    }

    public <T extends IResTemplate> boolean hasTemplate(Class<T> clazz, int id) {
        return findValueFromMap(clazz, id) != null;
    }

    public static <T extends IResTemplate> T findTemplate(Class<T> clazz, int id) {
        return getInstance().findValueFromMap(clazz, id);
    }

    public static <T extends IResConstTemplate> T getConsts(Class<T> clazz) {
        return getInstance().getValueFromMap(clazz, ResHolder.CONST_TEMPLATE_RETAIN_ID);
    }

    /**
     * 获取某个模板的所有记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @return 返回顺序列表
     */
    @SuppressWarnings("unchecked")

    public <T extends IResTemplate> Map<Integer, T> getMap(Class<T> clazz) throws GeminiException {
        String clazzName = ClassNameCacheUtils.getSimpleName(clazz);
        Map<Integer, T> ret = (Map<Integer, T>) resTemplateDataStore.get(clazzName);
        if (ret == null) {
            throw new GeminiException(StringUtils.format("{}没有配置数据. size=0", getFilename(clazz)));
        }
        return ret;
    }

    /**
     * 获取某个模板的所有记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @return 返回顺序列表
     */
    @SuppressWarnings("unchecked")
    public <T extends IResTemplate> Map<Integer, T> checkMap(Class<T> clazz, Supplier<String> msg) throws ResourceException {
        String clazzName = ClassNameCacheUtils.getSimpleName(clazz);
        Map<Integer, T> ret = (Map<Integer, T>) resTemplateDataStore.get(clazzName);
        if (ret == null) {
            throw new ResourceException(msg == null ? StringUtils.format("{}没有配置数据. size=0", getFilename(clazz)) : msg.get());
        }
        return ret;
    }


    public static String getFilename(Class<?> clazz) {
        ResXml xml = clazz.getAnnotation(ResXml.class);
        if (xml != null) {
            return StringUtils.format("在配置表={} sheet={}中", xml.excel(), xml.node());
        } else {
            return "unknown xml file";
        }
    }

    public String getResPath() {
        return resPath;
    }

    public Map<String, Map<Integer, IResTemplate>> getResTemplateDataStore() {
        return Collections.unmodifiableMap(resTemplateDataStore);
    }

    /**
     * 公用函数，用于给 template 和 const template 赋值
     *
     * @param template 模板
     * @param f        属性
     * @param value    值
     */
    private void setTemplateFieldValue(IResTemplate template, Field f, String value) throws Exception {
        if ("null".equals(value) || StringUtils.isBlank(value)) {
            if (StringUtils.equals(f.getType().getSimpleName(), "List")) {
                f.setAccessible(true);
                f.set(template, new ArrayList<>());
            }
            return;
        }
        value = value.trim();
        f.setAccessible(true);
        parseField(template, f, value);
        f.setAccessible(false);
    }

    private void parseField(IResTemplate template, Field f, String value) throws Exception {
        try {
            switch (f.getType().getSimpleName()) {
                case "int":
                    // 不这样写的原因是：有可能导出的数据时科学级数法格式
                    f.set(template,  Double.valueOf(value).intValue());
                    break;
                case "long":
                    f.setLong(template,  Double.valueOf(value).longValue());
                    break;
                case "float":
                    f.set(template, Float.parseFloat(value));
                    break;
                case "double":
                    f.set(template, Double.parseDouble(value));
                    break;
                case "Date":
                    f.set(template, new Date(TimeUtils.string2TimeStampMs(value)));
                    break;
                case "boolean":
                    f.setBoolean(template, Boolean.parseBoolean(value));
                    break;
                case "IntPairType":
                    f.set(template, IntPairType.parseFromString(value));
                    break;
                case "IntTripleType":
                    f.set(template, IntTripleType.parseFromString(value));
                    break;
                case "CircleType":
                    f.set(template, CircleType.parseFromString(value));
                    break;
                case "List":
                    parseListField(template, f, value);
                    break;
                default:
                    if (f.getType().isEnum()) {
                        f.set(template, f.getType().getMethod("valueOf", String.class).invoke(null, value));
                    } else {
                        f.set(template, value);
                    }
            }
        } catch (Exception ex) {
            String message = StringUtils.format("\t字段:{} 应该为:{}类型 但实际:{}不是\n{}", f.getName(), f.getType().getSimpleName(), value, ex.getMessage());
            throw new ResourceException(message, ex);
        }
    }

    private static void parseListField(IResTemplate template, Field f, String value) throws ResourceException {
        Class<?> param = (Class<?>) ((ParameterizedType) f.getGenericType()).getActualTypeArguments()[0];
        String[] array = StringUtils.split(value, ",");
        try {
            switch (param.getSimpleName()) {
                case "Integer":
                    List<Integer> intList = new ArrayList<>();
                    for (String e : array) {
                        intList.add(Integer.parseInt(e));
                    }
                    f.set(template, intList);
                    break;
                case "Float":
                    List<Float> floatList = new ArrayList<>();
                    for (String e : array) {
                        floatList.add(Float.parseFloat(e));
                    }
                    f.set(template, floatList);
                    break;
                case "String":
                    f.set(template, Arrays.asList(array));
                    break;
                case "IntPairType":
                    f.set(template, IntPairType.parseListFromString(value));
                    break;
                case "IntTripleType":
                    f.set(template, IntTripleType.parseListFromString(value));
                    break;
                case "CircleType":
                    f.set(template, CircleType.parseListFromString(value));
                    break;
                default:

            }
        } catch (Exception ex) {
            String message = StringUtils.format("\t泛型错误，字段应该为:{}类型的集合 但实际:{}不是", param.getSimpleName(), value);
            throw new ResourceException(message, ex);
        }
    }
}

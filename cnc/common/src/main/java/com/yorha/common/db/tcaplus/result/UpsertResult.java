package com.yorha.common.db.tcaplus.result;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;


/**
 * <AUTHOR>
 */
public class UpsertResult<T extends Message.Builder> implements GameDbResp {
    public TcaplusErrorCode code = TcaplusErrorCode.GEN_ERR_SUC;
    public int version = -1;
    public T value;

    @Override
    public int getCode() {
        return code.getValue();
    }

    @Override
    public boolean isOk() {
        return code == TcaplusErrorCode.GEN_ERR_SUC;
    }

    @Override
    public String toString() {
        return "UpsertResult{" +
                "code=" + code +
                ", version=" + version +
                '}';
    }
}

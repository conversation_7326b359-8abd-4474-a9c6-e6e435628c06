package com.yorha.common.utils.shape;

import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.vector.Vector2f;
import org.apache.commons.lang3.RandomUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 扇形
 *
 * <AUTHOR>
 */
public class Sector implements Shape {
    private Point center;
    private Point pointA;
    private Point pointB;
    private final int r;
    private final int startAngle;
    private final int endAngle;

    private AABB aabb = null;

    /**
     * 扇形构造函数
     *
     * @param one    原点
     * @param other  目标点
     * @param radius 半径
     * @param angle  扇形全角
     */
    public static Sector valueOf(Point one, Point other, int radius, int angle) {
        return new Sector(one, other, radius, angle);
    }

    /**
     * 扇形构造函数
     *
     * @param one    原点
     * @param yaw    朝向
     * @param radius 半径
     * @param angle  扇形全角
     */
    public static Sector valueOf(Point one, Vector2f yaw, int radius, int angle) {
        return new Sector(one, yaw, radius, angle);
    }

    private Sector(Point one, Point other, int radius, int angle) {
        this(one, Vector2f.getVectorFromPointToPoint(one, other), radius, angle);
    }

    private Sector(Point one, Vector2f yaw, int radius, int angle) {
        this.center = Point.valueOf(one.getX(), one.getY());
        this.r = radius;
        Vector2f left = yaw.rotate(angle / 2).resizeLength(radius);
        this.pointA = Point.valueOf(left.getX() + this.center.getX(), left.getY() + this.center.getY());
        Vector2f right = left.rotate(-angle);
        this.pointB = Point.valueOf(right.getX() + this.center.getX(), right.getY() + this.center.getY());
        this.startAngle = (int) left.getAngle();
        this.endAngle = this.startAngle + angle;
    }

    /**
     * 判断点是否在扇形夹角中
     */
    public boolean angleContainPoint(long x, long y) {
        double angle = Math.atan2(y - getCenter().getY(), x - getCenter().getX());
        angle = Math.toDegrees(angle);
        return ShapeUtils.includedAngle(angle, startAngle, endAngle);
    }

    @Override
    public boolean containsPoint(long x, long y) {
        return pointWithinRadius(x, y) && angleContainPoint(x, y);
    }

    /**
     * 忽略角度判断某点是否在扇形半径内
     */
    public boolean pointWithinRadius(long x, long y) {
        long distance = (x - this.center.getX()) * (x - this.center.getX()) + (y - this.center.getY()) * (y - this.center.getY());
        return distance <= (long) r * r;
    }

    public boolean containsPoint(Point point) {
        return this.containsPoint(point.getX(), point.getY());
    }


    @Override
    public AABB getAABB() {
        if (aabb == null) {
            List<Integer> listX = new ArrayList<>();
            List<Integer> listY = new ArrayList<>();
            Point center = this.center;
            listX.add(center.getX());
            listY.add(center.getY());

            Point pointA = this.pointA;
            listX.add(pointA.getX());
            listY.add(pointA.getY());

            Point pointB = this.pointB;
            listX.add(pointB.getX());
            listY.add(pointB.getY());

            Point pointC = Point.valueOf(this.center.getX() - r, this.center.getY());
            if (this.containsPoint(pointC)) {
                listX.add(pointC.getX());
                listY.add(pointC.getY());
            }

            Point pointD = Point.valueOf(this.center.getX() + r, this.center.getY());
            if (this.containsPoint(pointD)) {
                listX.add(pointD.getX());
                listY.add(pointD.getY());
            }

            Point pointE = Point.valueOf(this.center.getX(), this.center.getY() + r);
            if (this.containsPoint(pointE)) {
                listX.add(pointE.getX());
                listY.add(pointE.getY());
            }

            Point pointF = Point.valueOf(this.center.getX(), this.center.getY() - r);
            if (this.containsPoint(pointF)) {
                listX.add(pointF.getX());
                listY.add(pointF.getY());
            }

            int left = MathUtils.min(listX);
            int right = MathUtils.max(listX);
            int top = MathUtils.min(listY);
            int bottom = MathUtils.max(listY);
            aabb = new AABB(left, top, right, bottom);
        }
        return aabb;

    }

    @Override
    public Point getRandomPoint() {
        int length = RandomUtils.nextInt(1, r);
        int angle = getRandomAngle();
        Vector2f result = Vector2f.valueOf(length, angle);
        return Point.valueOf(result.getX() + this.center.getX(), result.getY() + this.center.getY());
    }

    public int getRandomAngle() {
        if (this.startAngle < 0) {
            return (int) RandomUtils.nextDouble(this.startAngle + 1 + 360, this.endAngle + 360);
        } else {
            return (int) RandomUtils.nextDouble(this.startAngle + 1, this.endAngle);
        }
    }


    public Point getCenter() {
        return center;
    }

    public void setCenter(Point center) {
        this.center = center;
    }

    public Point getPointA() {
        return pointA;
    }

    public void setPointA(Point pointA) {
        this.pointA = pointA;
    }

    public Point getPointB() {
        return pointB;
    }

    public void setPointB(Point pointB) {
        this.pointB = pointB;
    }

    public int getR() {
        return r;
    }

    public double getStartAngle() {
        return startAngle;
    }

    public double getEndAngle() {
        return endAngle;
    }


}

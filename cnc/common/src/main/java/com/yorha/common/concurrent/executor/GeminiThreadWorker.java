package com.yorha.common.concurrent.executor;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.exception.GeminiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 单线程的worker。
 *
 * <AUTHOR>
 */
public class GeminiThreadWorker {
    private static final Logger LOGGER = LogManager.getLogger(GeminiThreadWorker.class);
    private static final int THREAD_WORKER_WORKING = 0x01;
    private static final int THREAD_WORKER_THREAD_RUNNING = 0x02;

    /**
     * 状态是否处于工作中。
     *
     * @param s 状态。
     * @return true or false。
     */
    private static boolean isStatusWorking(int s) {
        return (s & THREAD_WORKER_WORKING) != 0;
    }

    /**
     * 状态是否处于消费任务中。
     *
     * @param s 状态。
     * @return true or false。
     */
    private static boolean isStatusTakingTask(int s) {
        return (s & THREAD_WORKER_THREAD_RUNNING) != 0;
    }

    /**
     * cas 尝试状态设置位非工作状态。
     *
     * @param status 状态。
     * @return true 成功，false 失败。
     */
    private static boolean casNotWorking(final AtomicInteger status) {
        while (true) {
            final int s0 = status.get();
            if (!isStatusWorking(s0)) {
                return false;
            }
            final int s = s0 & (~THREAD_WORKER_WORKING);
            final boolean isCasOk = status.compareAndSet(s0, s);
            if (isCasOk) {
                return true;
            }
        }
    }

    private static boolean casWorking(final AtomicInteger status) {
        while (true) {
            final int s0 = status.get();
            if (isStatusWorking(s0)) {
                return false;
            }
            final int s = s0 | THREAD_WORKER_WORKING;
            final boolean isCasOk = status.compareAndSet(s0, s);
            if (isCasOk) {
                return true;
            }
        }
    }

    /**
     * cas 尝试设置状态为处理任务状态。
     *
     * @param status 状态。
     * @return true 成功，false 失败。
     */
    private static boolean casTakingTask(final AtomicInteger status) {
        while (true) {
            final int s0 = status.get();
            if (isStatusTakingTask(s0)) {
                return false;
            }
            final int s = s0 | THREAD_WORKER_THREAD_RUNNING;
            final boolean isCasOk = status.compareAndSet(s0, s);
            if (isCasOk) {
                return true;
            }
        }
    }

    /**
     * cas 尝试设置状态为不处理任务。
     *
     * @param status 状态。
     * @return true 成功，false 失败。
     */
    private static boolean casNotTakingTask(final AtomicInteger status) {
        while (true) {
            final int s0 = status.get();
            if (!isStatusTakingTask(s0)) {
                return false;
            }
            final int s = s0 & (~THREAD_WORKER_THREAD_RUNNING);
            final boolean isCasOk = status.compareAndSet(s0, s);
            if (isCasOk) {
                return true;
            }
        }
    }


    /**
     * 任务堆积数量。
     */
    private final AtomicInteger taskCount;
    /**
     * 阻塞队列。注意，可能是mpsc，因此别瞎调用接口，以免死循环。
     */
    private final BlockingQueue<Runnable> blockingQueue;
    /**
     * 名称。
     */
    private final String name;
    /**
     * 是否守护线程。
     */
    private final boolean isDaemon;
    /**
     * 状态位。
     */
    private final AtomicInteger status;
    /**
     * 工作线程。
     */
    private Thread thread;

    private final ReentrantLock lock;


    private GeminiThreadWorker(Builder builder) {
        this.name = builder.name;
        this.isDaemon = builder.isDaemon;
        this.taskCount = new AtomicInteger(0);
        this.status = new AtomicInteger(0);
        this.blockingQueue = builder.newBlockingQueue();
        this.lock = new ReentrantLock(true);
        this.thread = null;
    }


    /**
     * @return 是否处于工作状态。
     */
    public boolean isWorking() {
        return isStatusWorking(this.status.get());
    }

    /**
     * @return 是否正在消费任务。
     */
    public boolean isTakingTask() {
        return isStatusTakingTask(this.status.get());
    }

    public void start() {
        this.lock.lock();
        try {
            if (!casWorking(this.status)) {
                throw new GeminiException("{} can't work", this);
            }
            this.thread = ConcurrentHelper.newThread(this.name, this.isDaemon, this::workerLoop);
            this.thread.start();
        } finally {
            this.lock.unlock();
        }
    }

    /**
     * 执行任务。
     *
     * @param command 任务。
     */
    public void execute(final Runnable command) {
        // 不再工作
        if (!this.isWorking()) {
            LOGGER.error("{} is not working, refuse {}", this, command);
            return;
        }
        // 队列满了
        if (!this.blockingQueue.offer(command)) {
            LOGGER.error("{} is full, refuse {}", this, command);
            return;
        }
        // 递增taskCount
        this.taskCount.getAndIncrement();
    }

    private void workerLoop() {
        LOGGER.info("{} begin take task", this);
        try {
            casTakingTask(this.status);
            while (this.isWorking() || !this.blockingQueue.isEmpty()) {
                final Runnable cmd;
                try {
                    cmd = this.blockingQueue.take();
                } catch (InterruptedException e) {
                    continue;
                }
                try {
                    cmd.run();
                } catch (Throwable e) {
                    LOGGER.error("{} take {}, but ", this, cmd, e);
                } finally {
                    this.taskCount.decrementAndGet();
                }
            }
        } finally {
            casNotTakingTask(this.status);
            LOGGER.info("{} end take task", this);
        }

    }

    /**
     * 优雅退出。
     *
     * @param waitTimeOutMs 等待时间，最小的等待时间。
     * @return 是否再规定时间内完成优雅退出。
     */
    public boolean shutdown(final int waitTimeOutMs) {
        this.lock.lock();
        try {
            if (!casNotWorking(this.status)) {
                throw new IllegalStateException(StringUtils.format("{} is not working!", this));
            }
            final long startTsMs = SystemClock.nowNative();
            while ((SystemClock.nowNative() - startTsMs) < waitTimeOutMs && this.isTakingTask()) {
                this.thread.interrupt();
                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(10));
            }
            return !this.isTakingTask();
        } finally {
            this.lock.unlock();
        }
    }

    /**
     * 获取当前任务数量。
     *
     * @return 任务数量。
     */
    public int getTaskCount() {
        return this.taskCount.get();
    }


    @Override
    public String toString() {
        return "GeminiThreadWorker{" +
                "name='" + name + '\'' +
                ", isDaemon=" + isDaemon +
                ", isRunning=" + this.isTakingTask() +
                ", isWorking=" + this.isWorking() +
                ", taskCount=" + this.getTaskCount() +
                ", thread=" + this.thread +
                '}';
    }

    public static class Builder {
        private String name = null;
        private int taskSize = 0;
        private boolean isDaemon = false;

        private Builder() {
        }

        /**
         * 设置线程池名称。
         *
         * @param name 名称。
         * @return Builder。
         */
        public Builder name(final String name) {
            if (StringUtils.isEmpty(name)) {
                throw new NullPointerException("name is empty");
            }
            this.name = name;
            return this;
        }


        /**
         * 线程池可缓存的任务数量上限。
         *
         * @param taskSize taskSize == 0 无上限队列；> 0 可缓存的任务上限。
         * @return Builder。
         */
        public Builder taskSize(final int taskSize) {
            if (taskSize < 0) {
                throw new IllegalArgumentException("taskSize < 0");
            }
            this.taskSize = taskSize;
            return this;
        }

        /**
         * 是否守护线程的线程池。
         *
         * @param isDaemon true 守护线程；false 非守护线程。
         * @return Builder。
         */
        public Builder daemon(final boolean isDaemon) {
            this.isDaemon = isDaemon;
            return this;
        }

        private BlockingQueue<Runnable> newBlockingQueue() {
            // 准备queue
            final BlockingQueue<Runnable> queue;
            if (this.taskSize == 0) {
                queue = new LinkedBlockingQueue<>();
            } else {
                queue = new MpscBlockingConsumerArrayQueue<>(this.taskSize);
            }
            return queue;
        }

        public GeminiThreadWorker build() {
            return new GeminiThreadWorker(this);
        }
    }

    public static Builder newBuilder() {
        return new Builder();
    }
}

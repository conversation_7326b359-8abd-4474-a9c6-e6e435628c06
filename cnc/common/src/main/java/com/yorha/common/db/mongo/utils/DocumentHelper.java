package com.yorha.common.db.mongo.utils;

import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Descriptors.FieldDescriptor.JavaType;
import com.google.protobuf.Message;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.FieldMetaData;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.enums.error.DbCode;
import com.yorha.common.exception.DataBaseException;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 副总document与pb的转换
 *
 * <AUTHOR>
 */
public class DocumentHelper {

    /**
     * 检查pb中已设置的数据是否能与index匹配
     *
     * @param req pb数据
     * @return true==可用index
     */
    private static boolean isIndex(final Message.Builder req) {
        final Set<String> setFields = PbHelper.getPbValidFieldNames(req);
        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
        return fieldMetaData.indexes.contains(setFields);
    }

    /**
     * 向document添加数据的入口（避免直接调用document.append）
     *
     * @param field    字段描述
     * @param value    待设置的值
     * @param document Document
     */
    private static void addValToDocument(final Descriptors.FieldDescriptor field, final Object value, final Document document) {
        if (field.getJavaType() == JavaType.MESSAGE) {
            byte[] bytes = ((Message) value).toByteArray();
            document.append(field.getName(), bytes);
        } else if (field.getJavaType() == JavaType.BYTE_STRING) {
            document.append(field.getName(), ((ByteString) value).toByteArray());
        } else {
            document.append(field.getName(), value);
        }
    }

    /**
     * 将数据转为更新用的bson
     *
     * @param field 字段描述
     * @param value 待设置的值
     */
    private static Bson formSingleUpdate(final Descriptors.FieldDescriptor field, final Object value) {
        if (field.getJavaType() == JavaType.MESSAGE) {
            byte[] bytes = ((Message) value).toByteArray();
            return Updates.set(field.getName(), bytes);
        } else if (field.getJavaType() == JavaType.BYTE_STRING) {
            return Updates.set(field.getName(), ((ByteString) value).toByteArray());
        }
        return Updates.set(field.getName(), value);
    }

    /**
     * 将数据转为更新用的bson
     *
     * @param field 字段描述
     * @param value 待设置的值
     */
    private static Bson formSingleIncrease(final Descriptors.FieldDescriptor field, final Object value) {
        final JavaType javaType = field.getJavaType();
        if (javaType != JavaType.INT && javaType != JavaType.LONG) {
            throw new DataBaseException(TcaplusErrorCode.API_ERR_INVALID_INCREASE_FIELD.getValue());
        }
        return Updates.inc(field.getName(), (Number) value);
    }

    /**
     * 构建索引查询数据
     *
     * @param req pb数据
     * @return Document
     */
    public static Document formIndex(final Message.Builder req) {
        if (!isIndex(req)) {
            throw new DataBaseException(DbCode.DB_NOT_INDEX, "need index");
        }
        final Document document = new Document();
        for (final Map.Entry<Descriptors.FieldDescriptor, Object> fieldWithValue : req.getAllFields().entrySet()) {
            addValToDocument(fieldWithValue.getKey(), fieldWithValue.getValue(), document);
        }
        return document;
    }

    /**
     * 构建主键查询数据
     *
     * @param req pb数据
     * @return Doucment
     */
    public static Document formKey(final Message.Builder req) {
        final Document document = new Document();
        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
        // mongodb id
        if (fieldMetaData.documentIdField != null && req.hasField(fieldMetaData.documentIdField)) {
            document.append("_id", req.getField(fieldMetaData.documentIdField));
            return document;
        }
        // 主键
        for (final Descriptors.FieldDescriptor keyField : fieldMetaData.keyFieldsList) {
            if (!req.hasField(keyField)) {
                throw new DataBaseException(DbCode.DB_NEED_KEY, "need key");
            }
            addValToDocument(keyField, req.getField(keyField), document);
        }
        return document;
    }

//    /**
//     * 将主键以外数据转为document
//     *
//     * @param req pb数据
//     * @return Doucment
//     */
//    public static Document formVal(final Message.Builder req) {
//        final Document document = new Document();
//        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
//        // 值
//        for (final Descriptors.FieldDescriptor valField : fieldMetaData.valueFieldsList) {
//            addValToDocument(valField, req.getField(valField), document);
//        }
//        return document;
//    }

    /**
     * 将pb数据转为doucment
     *
     * @param req pb数据
     * @return Doucment
     */
    public static Document formKeyAndVal(final Message.Builder req) {
        final Document document = new Document();
        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
        // 主键
        for (final Descriptors.FieldDescriptor keyField : fieldMetaData.keyFieldsList) {
            if (!req.hasField(keyField)) {
                throw new DataBaseException(DbCode.DB_NEED_KEY, "need key");
            }
            addValToDocument(keyField, req.getField(keyField), document);
        }
        // 值
        for (final Descriptors.FieldDescriptor valField : fieldMetaData.valueFieldsList) {
            if (!req.hasField(valField)) {
                continue;
            }
            addValToDocument(valField, req.getField(valField), document);

        }
        return document;
    }

    /**
     * 构建update bson语句
     *
     * @param req pb数据
     * @return
     */
    public static Bson formUpdate(final Message.Builder req) {
        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
        final List<Bson> updates = Lists.newArrayListWithExpectedSize(fieldMetaData.valueFieldsList.size());
        // 值
        for (final Descriptors.FieldDescriptor valField : fieldMetaData.valueFieldsList) {
            if (!req.hasField(valField)) {
                continue;
            }
            updates.add(formSingleUpdate(valField, req.getField(valField)));
        }
        return Updates.combine(updates);
    }

    /**
     * 构建increase用的数据
     *
     * @param req pb数据
     * @return Bson
     */
    public static Bson formIncrease(final Message.Builder req) {
        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
        final List<Bson> updates = Lists.newArrayListWithExpectedSize(fieldMetaData.valueFieldsList.size());
        // 值
        for (final Descriptors.FieldDescriptor valField : fieldMetaData.valueFieldsList) {
            if (!req.hasField(valField)) {
                continue;
            }
            final JavaType javaType = valField.getJavaType();
            if (javaType != JavaType.INT && javaType != JavaType.LONG) {
                throw new DataBaseException(TcaplusErrorCode.API_ERR_INVALID_INCREASE_FIELD.getValue());
            }
            updates.add(formSingleIncrease(valField, req.getField(valField)));
        }
        return Updates.combine(updates);
    }

    /**
     * 构建批量查询用的数据
     *
     * @param reqs pb数据集合
     * @return Bson
     */
    public static Bson formBatchGet(final Collection<Message.Builder> reqs) {
        final List<Bson> batchGet = Lists.newArrayListWithExpectedSize(reqs.size());
        for (final Message.Builder get : reqs) {
            batchGet.add(formKey(get));
        }
        return Filters.or(batchGet);
    }
}

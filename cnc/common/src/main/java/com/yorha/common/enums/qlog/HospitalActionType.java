package com.yorha.common.enums.qlog;

/**
 * 医院Action enum
 *
 * <AUTHOR>
 */
public enum HospitalActionType {
    /**
     * 战斗重伤进入医院
     */
    INJURED_ENTER_HOSPITAL("injured_enter_hospital"),
    /**
     * 开始治疗
     */
    START_RECOVERING("start_recovering"),
    /**
     * 开始一键治疗
     */
    START_FAST_RECOVERING("start_recovering"),
    /**
     * 结束治疗
     */
    COMPLETE_RECOVERING("complete_recovering"),
    /**
     * 治疗完成出院
     */
    RECOVERED_LEAVE_HOSPITAL("recovered_leave_hospital"),
    ;

    private String type;

    HospitalActionType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}

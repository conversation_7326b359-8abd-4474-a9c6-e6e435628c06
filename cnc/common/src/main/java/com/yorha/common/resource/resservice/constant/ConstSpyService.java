package com.yorha.common.resource.resservice.constant;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import res.template.ConstSpyTemplate;

/**
 * <AUTHOR>
 * <p>
 * 侦察常量管理
 */
public class ConstSpyService extends AbstractResService {

    public ConstSpyService(ResHolder resHolder) {
        super(resHolder);
    }

    public ConstSpyTemplate getTemplate() {
        return getResHolder().getConstTemplate(ConstSpyTemplate.class);
    }

    @Override
    public void load() throws ResourceException {
    }

    @Override
    public void checkValid() throws ResourceException {
    }
}

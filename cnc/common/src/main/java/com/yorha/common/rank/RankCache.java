package com.yorha.common.rank;

import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMsg.RankInfoDTO;
import com.yorha.proto.StructPB;
import io.gamioo.redis.zset.long2object.Long2ObjectEntry;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 */
public class RankCache {
    private static final Logger LOGGER = LogManager.getLogger(RankCache.class);

    private final Map<Integer, RankInfoDTO> rankCache;
    private final Map<Long, RankInfoDTO> idCache;
    private final long expireTsMs;
    private static final int TIME_OFFSET_MS = 120000;
    private static final int TIME_BASE_MS = 240000;

    public boolean isExpire() {
        return expireTsMs < SystemClock.nowNative();
    }

    public RankCache(AbstractRankEntity rankEntity, List<Long2ObjectEntry<RankMember>> full, Map<Long, CommonMsg.ClanSimpleInfo> clanMap) {
        Random rand = new Random();
        this.expireTsMs = SystemClock.nowNative() + rand.nextInt(TIME_OFFSET_MS) + TIME_BASE_MS;
        this.rankCache = new HashMap<>(full.size());
        this.idCache = new HashMap<>(full.size());
        int rankId = rankEntity.getRankId();
        int del = 0;
        for (int i = 0; i < full.size(); i++) {
            Long2ObjectEntry<RankMember> member = full.get(i);
            RankMember rankMember = member.getScore();
            CommonMsg.ClanSimpleInfo simpleClan = clanMap.get(rankMember.getId());
            if (simpleClan != null) {
                RankInfoDTO rankInfoDTO = RankHelper.buildClanRankInfoDTO(
                        rankId, i + 1 - del,
                        rankMember.getScore(),
                        rankMember.getZoneId(),
                        simpleClan);
                rankCache.put((i + 1 - del), rankInfoDTO);
                idCache.put(rankMember.getId(), rankInfoDTO);
            } else {
                // 为了使排名跳过这个。让客户端显示正常
                del++;
                LOGGER.warn("simple clan is not exist,rankId={},member={}", rankId, rankMember);
                //应蝶影的要求，做的最后防御，那边没法保证，删除军团后，还有更新排行榜的操作
                rankEntity.deleteRanking(rankMember.getId());
            }
        }
    }

    public RankCache(
            AbstractRankEntity rankEntity,
            List<Long2ObjectEntry<RankMember>> full,
            Map<Long, StructPB.PlayerCardInfoPB> playerMap,
            Map<Long, CommonMsg.ClanSimpleInfo> clanMap
    ) {
        Random rand = new Random();
        this.expireTsMs = SystemClock.nowNative() + rand.nextInt(TIME_OFFSET_MS) + TIME_BASE_MS;
        this.rankCache = new HashMap<>(full.size());
        this.idCache = new HashMap<>(full.size());
        int rankId = rankEntity.getRankId();
        int del = 0;
        boolean setArrow = rankEntity.getLastStatisticsTsMs() != 0;
        for (int i = 0; i < full.size(); i++) {
            Long2ObjectEntry<RankMember> member = full.get(i);
            RankMember rankMember = member.getScore();
            StructPB.PlayerCardInfoPB pb = playerMap.get(rankMember.getId());
            CommonMsg.ClanSimpleInfo simpleClan = null;
            if (pb != null) {
                if (pb.getClanId() > 0) {
                    simpleClan = clanMap.get(pb.getClanId());
                }
            } else {
                // 为了使排名跳过这个。让客户端显示正常
                del++;
                WechatLog.error("simple player is not exist,rankId={},member={}", rankId, rankMember);
                continue;
            }
            RankInfoDTO dto;
            if (setArrow) {
                dto = RankHelper.buildPlayerRankInfoDTO(
                        rankId, i + 1 - del,
                        rankMember.getScore(),
                        rankMember.getZoneId(),
                        rankMember.getLastRank(),
                        pb, simpleClan);
            } else {
                dto = RankHelper.buildPlayerRankInfoDTO(
                        rankId, i + 1 - del,
                        rankMember.getScore(),
                        rankMember.getZoneId(),
                        pb, simpleClan);
            }
            rankCache.put((i + 1 - del), dto);
            idCache.put(rankMember.getId(), dto);
        }
    }

    public RankInfoDTO getDtoById(long id) {
        return idCache.get(id);
    }

    public RankInfoDTO getDtoByRank(int rank) {
        return rankCache.get(rank);
    }

    public int size() {
        return rankCache.size();
    }
}

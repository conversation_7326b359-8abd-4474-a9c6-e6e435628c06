package com.yorha.common.utils.boolmap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * bool占位工具包
 *
 * <AUTHOR>
 * @modify YuHy 2022年4月21日 一维数组修改为二维数组，增加内存占用量，减少计算复杂度。目的是为了能快速定位问题
 * 此前使用bitMask，变二维为一维，逻辑实在恶心
 */
public class BoolMap {
    private final int xMax;
    private final int yMax;
    /**
     * 二维标记 true表明格子被占
     */
    private final boolean[][] grid;
    private List<Integer> path;
    private List<List<Integer>> bestPath;


    private BoolMap(int xMax, int yMax) {
        this.xMax = xMax;
        this.yMax = yMax;
        grid = new boolean[xMax][yMax];
    }

    public static BoolMap getInstance(int xMax, int yMax) {
        return new BoolMap(xMax, yMax);
    }


    public void setGridStatus(int x, int y, boolean status) {
        if (paramIsValid(x, y)) {
            grid[x][y] = status;
        }
    }

    public void setGridStatus(int x, int y, int size, boolean status) {
        for (int i = 0; i < size; i++) {
            int xPoint = x + i;
            for (int j = size - 1; j >= 0; j--) {
                int yPoint = y + j;
                setGridStatus(xPoint, yPoint, status);
            }
        }
    }

    public void setRHGridStatus(int x, int y, int width, int height, boolean status) {
        for (int i = 0; i < width; i++) {
            int xPoint = x + i;
            for (int j = 0; j < height; j++) {
                int yPoint = y + j;
                setGridStatus(xPoint, yPoint, status);
            }
        }
    }

    public void setGridStatusTwoWay(int x, int y, boolean status) {
        if (paramIsValid(x, y)) {
            grid[x][y] = status;
            grid[y][x] = status;
        }
    }

    /**
     * check格子状态
     *
     * @param status 期望状态
     * @return false:不满足或参数非法
     */
    public boolean checkPosStatus(int x, int y, boolean status) {
        if (paramIsValid(x, y)) {
            return grid[x][y] == status;
        }
        return false;
    }

    /**
     * 参数校验
     *
     * @return false:不符合规范
     */
    private boolean paramIsValid(int x, int y) {
        if (x < 0 || y < 0) {
            return false;
        }
        return x < xMax && y < yMax;
    }

    /**
     * 搜索所有可行路径
     */
    public List<List<Integer>> findAllPath(int x, int y) {
        if (!paramIsValid(x, y)) {
            return Collections.emptyList();
        }
        path = new ArrayList<>();
        bestPath = new ArrayList<>();
        path.add(x);
        dfsAll(y);
        return bestPath;
    }

    /**
     * 搜索最短路径
     */
    public List<Integer> findShortestPath(int x, int y) {
        if (!paramIsValid(x, y)) {
            return Collections.emptyList();
        }
        path = new ArrayList<>();
        bestPath = new ArrayList<>();
        path.add(x);
        // 直接过
        if (grid[x][y]) {
            path.add(y);
            return path;
        }
        dfsShortest(y);
        if (bestPath.isEmpty()) {
            return Collections.emptyList();
        }
        return bestPath.get(0);
    }

    public boolean checkPath(int x, int y) {
        if (!paramIsValid(x, y)) {
            return false;
        }
        path = new ArrayList<>();
        path.add(x);
        return defCheck(y);
    }

    private boolean defCheck(int target) {
        int src = path.get(path.size() - 1);
        if (grid[src][target]) {
            return true;
        }
        for (int i = 0; i < xMax; i++) {
            if (grid[src][i] && !path.contains(i)) {
                path.add(i);
                if (defCheck(target)) {
                    return true;
                }
                path.remove(path.size() - 1);
            }
        }
        return false;
    }

    private void dfsShortest(int target) {
        int src = path.get(path.size() - 1);
        if (src == target) {
            if (bestPath.isEmpty() || bestPath.get(0).size() > path.size()) {
                bestPath.clear();
                bestPath.add(new ArrayList<>(path));
            }
            return;
        }
        // 剪枝 如果当前已经比最好的长了 没必要算了
        if (!bestPath.isEmpty() && bestPath.get(0).size() <= path.size() + 1) {
            return;
        }
        for (int i = 0; i < xMax; i++) {
            if (grid[src][i] && !path.contains(i)) {
                path.add(i);
                dfsShortest(target);
                path.remove(path.size() - 1);
            }
        }
    }

    private void dfsAll(int target) {
        int src = path.get(path.size() - 1);
        if (src == target) {
            bestPath.add(new ArrayList<>(path));
            return;
        }
        for (int i = 0; i < xMax; i++) {
            if (grid[src][i] && !path.contains(i)) {
                path.add(i);
                dfsAll(target);
                path.remove(path.size() - 1);
            }
        }
    }

    @Override
    public String toString() {
        return "PositionBitMask{" +
                "xMax=" + xMax +
                ", yMax=" + yMax +
                ", position=" + Arrays.toString(grid) +
                '}';
    }


}

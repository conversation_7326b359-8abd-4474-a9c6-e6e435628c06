package com.yorha.common.actor.ref;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.utils.BusIdUtils;
import com.yorha.gemini.utils.StringUtils;

/**
 * 固定busId的ref
 * 用于信封中的sender
 *
 * <AUTHOR>
 */
public class FixedRef implements IActorRef {

    private final String busId;
    private final String actorId;
    private final String actorRole;

    public FixedRef(String busId, String actorRole, String actorId) {
        this.busId = busId;
        this.actorRole = actorRole;
        this.actorId = actorId;
    }

    @Override
    public String getActorRole() {
        return this.actorRole;
    }

    @Override
    public String getActorId() {
        return this.actorId;
    }

    @Override
    public String getBusId() {
        return this.busId;
    }

    @Override
    public int getZoneId() {
        if (this.busId == null) {
            return -1;
        }
        return BusIdUtils.getZoneIdFromBusId(this.busId);
    }


    @Override
    public String toString() {
        return "FixedRef{" +
                "busId='" + busId + '\'' +
                ", actorRole=" + actorRole +
                ", actorId='" + actorId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final FixedRef other = (FixedRef) o;
        if (!StringUtils.equals(this.getBusId(), other.getBusId())) {
            return false;
        }
        if (!StringUtils.equals(this.getActorRole(), other.getActorRole())) {
            return false;
        }
        return StringUtils.equals(this.getActorId(), other.getActorId());
    }

    @Override
    public int hashCode() {
        int result = (busId != null ? busId.hashCode() : 0);
        result = 31 * result + (actorRole != null ? actorRole.hashCode() : 0);
        result = 31 * result + (actorId != null ? actorId.hashCode() : 0);
        return result;
    }
}

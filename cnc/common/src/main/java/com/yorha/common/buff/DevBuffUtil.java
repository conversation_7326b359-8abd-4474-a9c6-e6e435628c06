package com.yorha.common.buff;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.yorha.common.addition.AdditionUtil;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.AdditionSourceType;
import com.yorha.proto.CommonEnum.DevBuffSourceType;
import res.template.BuffTemplate;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * <AUTHOR>
 */
public class DevBuffUtil {
    /**
     * buff来源和加成来源的映射关系
     */
    public static final Map<DevBuffSourceType, AdditionSourceType> DEV_BUFF_2_ADDITION_SOURCE = Maps.newEnumMap(DevBuffSourceType.class);

    static {
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_ITEM, CommonEnum.AdditionSourceType.AST_ITEM);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_WAR_FRENZY, CommonEnum.AdditionSourceType.AST_WAR_FRENZY);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_SYS, CommonEnum.AdditionSourceType.AST_SYS);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_GM, CommonEnum.AdditionSourceType.AST_GM);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_DUNGEON, CommonEnum.AdditionSourceType.AST_DUNGEON);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_KINGDOM, CommonEnum.AdditionSourceType.AST_KINGDOM);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_CLAN_STAFF, CommonEnum.AdditionSourceType.AST_CLAN_STAFF);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_KINGDOM_STAFF, CommonEnum.AdditionSourceType.AST_KINGDOM_STAFF);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_MONSTER, CommonEnum.AdditionSourceType.AST_MONSTER);

        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_CLAN_BUILDING, CommonEnum.AdditionSourceType.AST_CLAN_BUILDING);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_CLAN_MAIN_BASE, CommonEnum.AdditionSourceType.AST_CLAN_MAIN_BASE);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_CLAN_COMMAND_CENTER, CommonEnum.AdditionSourceType.AST_CLAN_COMMAND_CENTER);
        DEV_BUFF_2_ADDITION_SOURCE.put(CommonEnum.DevBuffSourceType.DBST_CLAN_OWN_CITY, CommonEnum.AdditionSourceType.AST_CLAN_OWN_CITY);
    }

    /**
     * 获取buff来源对应的加成来源
     */
    public static CommonEnum.AdditionSourceType getAdditionSourceType(CommonEnum.DevBuffSourceType type) {
        if (type == CommonEnum.DevBuffSourceType.DBST_NONE) {
            throw new GeminiException(ErrorCode.FAILED, "DevBuffSourceType is none!");
        }
        return DEV_BUFF_2_ADDITION_SOURCE.get(type);
    }

    /**
     * 是否是联盟自己的buff
     */
    public static boolean isClanSelfBuff(int buffId) {
        return AdditionUtil.isClanAddition(DevBuffMgrBase.getBuffTemplate(buffId).getType());
    }

    public static boolean isPeaceShieldBuff(int buffId) {
        BuffTemplate temp = getResHolder().findValueFromMap(BuffTemplate.class, buffId);
        if (temp == null) {
            return false;
        }
        return temp.getType() == CommonEnum.BuffEffectType.ET_PEACE_SHIELD_VALUE;
    }

    public static boolean isSceneBuff(int buffId) {
        BuffTemplate buffTemplate = getResHolder().findValueFromMap(BuffTemplate.class, buffId);
        if (buffTemplate != null) {
            return AdditionUtil.isSceneAddition(buffTemplate.getType());
        }
        return false;
    }

    public static int effectLayer(DevBuffProp buffProp, BuffTemplate template) {
        return template.getRuleValue() > 0 ? Math.min(template.getRuleValue(), buffProp.getLayer()) : buffProp.getLayer();
    }

    public static final Set<CommonEnum.DevBuffSourceType> CLAN_BUILDING_SOURCE_TYPE = ImmutableSet.of(
            CommonEnum.DevBuffSourceType.DBST_CLAN_MAIN_BASE,
            CommonEnum.DevBuffSourceType.DBST_CLAN_COMMAND_CENTER,
            CommonEnum.DevBuffSourceType.DBST_CLAN_OWN_CITY,
            CommonEnum.DevBuffSourceType.DBST_CLAN_BUILDING
    );

    /**
     * 领土模块提供的buff来源
     */
    public static final List<CommonEnum.DevBuffSourceType> TERRITORY_BUFF_SOURCE_TYPE = ImmutableList.of(
            CommonEnum.DevBuffSourceType.DBST_CLAN_BUILDING, // 据点
            CommonEnum.DevBuffSourceType.DBST_CLAN_MAIN_BASE, // 主基地
            CommonEnum.DevBuffSourceType.DBST_CLAN_COMMAND_CENTER, // 指挥中心
            CommonEnum.DevBuffSourceType.DBST_CLAN_OWN_CITY// 城市
    );

    public static boolean isNeedAddToCity(int buffId) {
        BuffTemplate temp = getResHolder().findValueFromMap(BuffTemplate.class, buffId);
        if (temp == null) {
            return false;
        }
        return !StringUtils.isEmpty(temp.getShow());
    }
}

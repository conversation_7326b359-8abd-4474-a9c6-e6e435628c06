/*
 * Copyright 2015-2020 yorha Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yorha.common.utils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.ToIntFunction;

/**
 * 随机数相关操作工具类.
 * 本工具类中统一以{@link ThreadLocalRandom}为基础的封装
 *
 * <AUTHOR> Jiang
 */
public class RandomUtils {

    private RandomUtils() {
    }

    /**
     * 返回一个随机Boolean值.
     *
     * @return 随机Boolean值
     */
    public static boolean nextBoolean() {
        return ThreadLocalRandom.current().nextBoolean();
    }

    /**
     * 返回一个0到指定区间的随机数字.
     * <p>
     * 0 &lt;= random &lt; bound
     *
     * @param bound 最大值（不包含）
     * @return 返回一个0到指定区间的随机数字
     */
    public static int nextInt(int bound) {
        return ThreadLocalRandom.current().nextInt(bound);
    }

    /**
     * 返回一个指定区间的随机数字.
     * <p>
     * origin &lt;= random &lt; bound
     *
     * @param origin 最小值（包含）
     * @param bound  最大值（不包含）
     * @return 返回一个指定区间的随机数字
     */
    public static int nextInt(int origin, int bound) {
        return ThreadLocalRandom.current().nextInt(origin, bound);
    }

    /**
     * 返回一个0到指定区间的随机数字.
     * <p>
     * 0 &lt;= random &lt; bound
     *
     * @param bound 最大值（不包含）
     * @return 返回一个0到指定区间的随机数字
     */
    public static long nextLong(long bound) {
        return ThreadLocalRandom.current().nextLong(bound);
    }

    /**
     * 返回一个指定区间的随机数字.
     * <p>
     * origin &lt;= random &lt; bound
     *
     * @param origin 最小值（包含）
     * @param bound  最大值（不包含）
     * @return 返回一个指定区间的随机数字
     */
    public static long nextLong(long origin, long bound) {
        return ThreadLocalRandom.current().nextLong(origin, bound);
    }

    /**
     * 返回一个随机Double值.
     *
     * @return 随机Double值
     */
    public static double nextDouble() {
        return ThreadLocalRandom.current().nextDouble();
    }

    /**
     * 判定一次随机事件是否成功.
     *
     * <pre>
     * 如果rate&gt;=1,则百分百返回true.<br>
     * 如果rate&lt;=0,则百分百返回false.
     * </pre>
     *
     * @param rate 成功率
     * @return 如果成功返回true, 否则返回false.
     */
    public static boolean isSuccess(float rate) {
        return ThreadLocalRandom.current().nextFloat() < rate;
    }

    /**
     * 判定一次随机事件是否成功.
     *
     * <pre>
     * 如果rate&gt;=1,则百分百返回true.<br>
     * 如果rate&lt;=0,则百分百返回false.
     * </pre>
     *
     * @param rate 成功率
     * @return 如果成功返回true, 否则返回false.
     */
    public static boolean isSuccess(double rate) {
        return ThreadLocalRandom.current().nextDouble() < rate;
    }

    /**
     * 判定一次百分比的随机事件是否成功.
     * <p>
     * 参数自动转化为百分比单位，就是除100
     *
     * <pre>
     * RandomUtils.isSuccessByPercentage(rate) = RandomUtils.isSuccess(rate / 100D)
     * </pre>
     *
     * @param rate 成功率/100D
     * @return 如果成功返回true, 否则返回false.
     */
    public static boolean isSuccessByPercentage(long rate) {
        return isSuccess(rate / MathUtils.HUNDRED);
    }

    /**
     * 判定一次千分比的随机事件是否成功.
     * <p>
     * 参数自动转化为千分比单位，就是除1000
     *
     * <pre>
     * RandomUtils.isSuccessByPermillage(rate) = RandomUtils.isSuccess(rate / 1000D)
     * </pre>
     *
     * @param rate 成功率/1000D
     * @return 如果成功返回true, 否则返回false.
     */
    public static boolean isSuccessByPermillage(long rate) {
        return isSuccess(rate / MathUtils.THOUSAND);
    }

    /**
     * 在指定集合中随机出一个元素.
     * <p>
     * 所以元素无权重的随机.
     *
     * @param <T> 要随机集合里的元素类型
     * @param set 指定集合
     * @return 随机返回集合中的一个元素.
     */
    public static <T> T randomSet(Set<T> set) {
        if (set == null || set.isEmpty()) {
            return null;
        }
        int rn = nextInt(set.size());
        int i = 0;
        for (T t : set) {
            if (i++ == rn) {
                return t;
            }
        }
        return null;
    }

    /**
     * 在指定集合中随机出一个元素.
     * <p>
     * 所以元素无权重的随机.
     *
     * @param <T>  要随机集合里的元素类型
     * @param list 指定集合
     * @return 随机返回集合中的一个元素.
     */
    public static <T> T randomList(List<T> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(nextInt(list.size()));
    }

    /**
     * 从一个List集合中随机出指定数量的元素.
     * <p>
     * <code>
     * source = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]<br>
     * random(source, 5) = [5, 3, 6, 7, 2]
     * </code>
     *
     * @param <T>    要随机集合里的元素类型
     * @param source List集合
     * @param num    指定数量
     * @return 如果源为空或指定数量小于1，则返回空集合，否则随机抽取元素组装新集合并返回
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> randomList(final List<T> source, int num) {
        // 没有源或要取的数小于1个就直接返回空列表
        if (source == null || num < 1 || source.isEmpty()) {
            return Collections.emptyList();
        }

        // 数量刚刚好
        if (source.size() <= num) {
            List<T> result = new ArrayList<>(source);
            Collections.shuffle(source);
            return result;
        }

        // 随机位，最后一个元素向前移动的方式
        Object[] rs = source.toArray();
        List<T> result = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            int index = nextInt(rs.length - i);
            result.add((T) rs[index]);
            rs[index] = rs[rs.length - 1 - i];
        }
        return result;
    }

    /**
     * 在指定集合中按权重随机出一个元素.
     * <p>
     * K为元素，如果是自定义对象记得重写HashCode和equals.<br>
     * V为权重，机率为V/(sum(All))
     *
     * @param <K>  要随机的元素类型，也是Map的Key
     * @param data 随机集合
     * @return 按权重随机返回集合中的一个元素.
     */
    public static <K> K randomByWeight(Map<K, Integer> data) {
        final int sum = data.values().stream().reduce(0, Integer::sum);
        if (sum <= 0) {
            return randomList(new ArrayList<>(data.keySet()));
        }

        final int random = nextInt(sum);
        int step = 0;
        for (Map.Entry<K, Integer> e : data.entrySet()) {
            step += e.getValue();
            if (step > random) {
                return e.getKey();
            }
        }
        throw new RuntimeException("randomByWeight的实现有Bug：" + random);
    }

    /**
     * 在指定列表中按指定权重随机出一个元素.
     * <p>
     * T为元素，如果是自定义对象记得重写HashCode和equals.<br>
     *
     * @param <T>     要随机的元素类型
     * @param data    要随机的元素集合
     * @param weights 要随机的权重集合
     * @return 按权重随机返回集合中的一个元素.
     */
    public static <T> T randomByWeight(List<T> data, List<Integer> weights) throws RuntimeException {
        if (data.size() != weights.size()) {
            throw new RuntimeException("data's size not equals with weight's size");
        }
        int size = data.size();
        final int sum = weights.stream().reduce(0, Integer::sum);
        if (sum <= 0) {
            return randomList(data);
        }
        final int random = nextInt(sum);
        int step = 0;
        for (int i = 0; i < size; ++i) {
            step += weights.get(i);
            if (step > random) {
                return data.get(i);
            }
        }
        throw new RuntimeException("randomWeight failed, check weights first");
    }

    /**
     * 在指定列表中按指定权重随机出指定个数的元素.
     * <p>
     * T为元素，如果是自定义对象记得重写HashCode和equals.<br>
     *
     * @param <T>         要随机的元素类型
     * @param data        要随机的元素集合
     * @param weights     要随机的权重集合
     * @param expectedNum 指定个数，需要保证大于0
     * @return 按权重随机返回集合中的指定个数的元素.
     */
    public static <T> List<T> randomByWeightWithExpectedNum(List<T> data, List<Integer> weights, int expectedNum) throws RuntimeException {
        if (data.size() != weights.size()) {
            throw new RuntimeException("data's size not equals with weight's size");
        }
        if (expectedNum <= 0 || data.size() < expectedNum) {
            throw new RuntimeException("num less or equals with 0 or data‘s size less than expectedNum");
        }
        // 元素集合数量和指定返回的个数相同，重排后返回即可
        if (data.size() == expectedNum) {
            List<T> result = new ArrayList<>(data);
            Collections.shuffle(data);
            return result;
        }

        List<T> result = new ArrayList<>();
        // 构建临时元素集合
        List<T> tempData = new ArrayList<>(data);
        List<Integer> tempWeights = new ArrayList<>(weights);

        // NOTE(furson): 可以根据data list的size和expectedNum的大小，（expectedNum是否大于size的二分之一）选择是正向随机还是反向随机，但收益很小
        // 进行指定个数次的随机
        int weightSum = tempWeights.stream().reduce(0, Integer::sum);
        for (int times = 0; times < expectedNum; ++times) {
            if (weightSum <= 0) {
                throw new RuntimeException("weightSum bigger than INT_MAX, use little weight");
            }
            final int random = nextInt(weightSum);
            // 根据权重选出随机下标
            int step = 0;
            int randomLoc = 0;
            while (randomLoc < tempWeights.size()) {
                step += tempWeights.get(randomLoc);
                if (step > random) {
                    break;
                }
                ++randomLoc;
            }
            // 随机下标有误
            if (randomLoc == tempWeights.size()) {
                throw new RuntimeException("randomWeight failed, check weights first");
            }
            // 添加单次随机结果到返回结果中
            result.add(tempData.get(randomLoc));
            // 减去随机到的权重
            weightSum -= tempWeights.get(randomLoc);
            // 将随机下标对应的数据移至临时集合的末尾
            Collections.swap(tempData, randomLoc, tempData.size() - 1);
            Collections.swap(tempWeights, randomLoc, tempWeights.size() - 1);
            // 删除临时集合的最后一个元素
            tempData.remove(tempData.size() - 1);
            tempWeights.remove(tempWeights.size() - 1);
        }
        return result;
    }

    /**
     * 在指定集合中按权重随机出一个元素.
     * <p>
     * 权重，机率为V/(sum(All))
     *
     * @param <T>            要随机的元素类型
     * @param data           随机集合
     * @param weightFunction 元素中权重方法
     * @return 按权重随机返回集合中的一个元素
     */
    public static <T> T randomByWeight(List<T> data, ToIntFunction<? super T> weightFunction) {
        final int sum = data.stream().mapToInt(weightFunction).reduce(0, Integer::sum);
        if (sum <= 0) {
            return randomList(data);
        }

        final int random = nextInt(sum);
        int step = 0;
        for (T e : data) {
            step += weightFunction.applyAsInt(e);
            if (step > random) {
                return e;
            }
        }
        throw new RuntimeException("randomByWeight的实现有Bug：" + random);
    }

    /**
     * 在指定集合中按权重随机出n个元素.
     * <p>
     * 权重，机率为V/(sum(All))
     *
     * @param <T>            要随机的元素类型
     * @param data           随机集合
     * @param weightFunction 元素中权重方法
     * @return 按权重随机返回集合中的一个元素
     */
    public static <T> List<T> randomByWeightBatch(List<T> data, ToIntFunction<? super T> weightFunction, int n) {
        final int sum = data.stream().mapToInt(weightFunction).reduce(0, Integer::sum);
        if (sum <= 0) {
            return randomList(data, n);
        }
        List<T> ret = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            final int random = nextInt(sum);
            int step = 0;
            for (T e : data) {
                step += weightFunction.applyAsInt(e);
                if (step > random) {
                    ret.add(e);
                    break;
                }
            }
        }
        return ret;
    }

    /**
     * 在指定列表中按指定权重随机出指定个数的元素.
     * <p>
     * T为元素，如果是自定义对象记得重写HashCode和equals.<br>
     *
     * @param <T>         要随机的元素类型
     * @param data        要随机的元素集合
     * @param expectedNum 指定个数，需要保证大于0
     * @return 按权重随机返回集合中的指定个数的元素.
     */
    public static <T> List<T> randomByWeightNoBack(List<T> data, ToIntFunction<? super T> weightFunction, int expectedNum) throws RuntimeException {
        if (expectedNum <= 0 || data.size() < expectedNum) {
            throw new RuntimeException("num less or equals with 0 or data‘s size less than expectedNum");
        }
        // 元素集合数量和指定返回的个数相同，重排后返回即可
        if (data.size() == expectedNum) {
            List<T> result = new ArrayList<>(data);
            Collections.shuffle(data);
            return result;
        }

        List<T> result = new ArrayList<>();
        // 构建临时元素集合
        List<T> tempData = new ArrayList<>(data);

        // NOTE(furson): 可以根据data list的size和expectedNum的大小，（expectedNum是否大于size的二分之一）选择是正向随机还是反向随机，但收益很小
        // 进行指定个数次的随机
        int weightSum = data.stream().mapToInt(weightFunction).reduce(0, Integer::sum);
        for (int times = 0; times < expectedNum; ++times) {
            if (weightSum <= 0) {
                throw new RuntimeException("weightSum bigger than INT_MAX, use little weight");
            }
            final int random = nextInt(weightSum);
            // 根据权重选出
            int step = 0;
            T selected = null;
            for (T e : tempData) {
                step += weightFunction.applyAsInt(e);
                if (step > random) {
                    selected = e;
                    break;
                }
            }
            if (selected == null) {
                throw new RuntimeException("randomWeight failed, check weights first");
            }
            tempData.remove(selected);
            result.add(selected);
            // 减去随机到的权重
            weightSum -= weightFunction.applyAsInt(selected);
        }
        return result;
    }

    /**
     * 区间随机
     */
    public static int randomBetween(int min, int max) {
        return ThreadLocalRandom.current().nextInt(max - min + 1) + min;
    }

    /**
     * 以 numerator/denominator的概率随机触发
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return 是否触发
     * @throws IllegalArgumentException 分母为0时抛出异常，无法判断此时期望的return
     */
    public static boolean trigger(int numerator, int denominator) {
        if (denominator <= 0) {
            throw new IllegalArgumentException("denominator=" + denominator);
        }
        if (numerator <= 0) {
            return false;
        }
        if (numerator > denominator) {
            return true;
        }
        return nextInt(0, denominator) < numerator;
    }
}
package com.yorha.common.resource.resservice.points;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.constant.Constants;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.ActivityPointsWay;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.CommonEnum.MonsterCategory;
import res.template.ActivityPointsTemplate;
import res.template.MonsterTemplate;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动积分
 */
public class PointsResService extends AbstractResService {

    private final ArrayListMultimap<MonsterCategory, PointsByKillMonsterConf> killMonsterMap = ArrayListMultimap.create();
    private final HashMap<Integer, ArrayListMultimap<Integer, ActivityPointsTemplate>> trainSoldierLvMap = Maps.newHashMap();
    private final ArrayListMultimap<CurrencyType, ActivityPointsTemplate> resCollectMap = ArrayListMultimap.create();
    private final ArrayListMultimap<CommonEnum.PowerType, PointsByIncPowerConf> powerIncMap = ArrayListMultimap.create();
    private final ArrayListMultimap<Integer, ActivityPointsTemplate> killPvpSoldierMap = ArrayListMultimap.create();
    private final ArrayListMultimap<Integer, PointsByUseSpeedUpConf> useSpeedUpItemMap = ArrayListMultimap.create();

    private final Map<ActivityPointsWay, List<ActivityPointsTemplate>> way2points = Maps.newEnumMap(ActivityPointsWay.class);

    public PointsResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (ActivityPointsTemplate pointsTemplate : getResHolder().getListFromMap(ActivityPointsTemplate.class)) {
            parsePointsTemplate(pointsTemplate);
            way2points.computeIfAbsent(pointsTemplate.getWay(), k -> Lists.newArrayList())
                    .add(pointsTemplate);
        }
    }

    private void parsePointsTemplate(ActivityPointsTemplate template) throws ResourceException {
        ActivityPointsWay way = template.getWay();
        switch (way) {
            case APW_KILL_MONSTER:
                PointsByKillMonsterConf pbkmc = new PointsByKillMonsterConf(template);
                killMonsterMap.put(pbkmc.getCategory(), pbkmc);
                break;
            case APW_TRAIN_SOLDIER_LV:
                int targetSoldierLv = Integer.parseInt(template.getParam1());
                int fromSoldierLv = 0;
                if (StringUtils.isNotEmpty(template.getParam2())) {
                    fromSoldierLv = Integer.parseInt(template.getParam2());
                }
                if (targetSoldierLv <= fromSoldierLv) {
                    throw new ResourceException("活动积分训练部队类型，参数1应该大于参数2的，现在是：{} {}", targetSoldierLv, fromSoldierLv);
                }
                trainSoldierLvMap.computeIfAbsent(targetSoldierLv, k -> ArrayListMultimap.create())
                        .put(fromSoldierLv, template);
                break;
            case APW_COLLECT_RES:
                int type = Integer.parseInt(template.getParam1());
                if (type == 0) {
                    for (CurrencyType currencyType : CurrencyType.values()) {
                        if (currencyType != CurrencyType.CT_None) {
                            resCollectMap.put(currencyType, template);
                        }
                    }
                } else {
                    resCollectMap.put(CurrencyType.forNumber(type), template);
                }
                break;
            case APW_SPEED_UP_ANY:
                break;
            case APW_INC_POWER:
                int powerTypeNumber = Integer.parseInt(template.getParam1());
                // 配成0默认任意战力都会增加积分的
                if (powerTypeNumber == 0) {
                    for (CommonEnum.PowerType pt : CommonEnum.PowerType.values()) {
                        if (pt != CommonEnum.PowerType.PT_POWER_NONE) {
                            powerIncMap.put(pt, new PointsByIncPowerConf(template));
                        }
                    }
                } else {
                    powerIncMap.put(CommonEnum.PowerType.forNumber(powerTypeNumber), new PointsByIncPowerConf(template));
                }
                break;
            case APW_KILL_PVP_SOLDIER:
                killPvpSoldierMap.put(Integer.parseInt(template.getParam2()), template);
                int p1 = Integer.parseInt(template.getParam1());
                if (p1 <= 0) {
                    throw new ResourceException("积分获取途径，类型：击杀玩家兵力 的参数1必须>0 配置id:{}", template.getId());
                }
                break;
            case APW_PLAYER_USE_SPEED_UP:
                for (String queueType : template.getParam1().split(Constants.DOU_HAO)) {
                    // 配的多就爆炸
                    final int queueTypeInt = Integer.parseInt(queueType);
                    useSpeedUpItemMap.put(queueTypeInt, new PointsByUseSpeedUpConf(template, queueTypeInt));
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        checkPointsTemplate();
    }

    private void checkPointsTemplate() throws ResourceException {
        for (ActivityPointsTemplate template : getResHolder().getListFromMap(ActivityPointsTemplate.class)) {
            final ActivityPointsWay way = template.getWay();
            switch (way) {
                case APW_PLAYER_USE_SPEED_UP:
                    checkPlayerUseSpeedUpTemplate(template);
                    break;
                case APW_INC_POWER:
                    checkIncPowerTemplate(template);
                    break;
                default:
                    break;
            }
        }
    }

    private void checkPlayerUseSpeedUpTemplate(ActivityPointsTemplate template) throws ResourceException {
        final String param1 = template.getParam1();
        final String param2 = template.getParam2();
        if (StringUtils.isEmpty(param1) || StringUtils.isEmpty(param2)) {
            throw new ResourceException("积分获取途径，类型：玩家主动使用加速道具 参数不能为空 id:{}", template.getId());
        }
        try {
            final int minutesInt = Integer.parseInt(param2);
            if (minutesInt <= 0) {
                throw new ResourceException("积分获取途径，类型：玩家主动使用加速道具 参数2配置错误 必须大于0 id:{}", template.getId());
            }
        } catch (NumberFormatException nfe) {
            throw new ResourceException("积分获取途径，类型：玩家主动使用加速道具 参数2配置错误 不是数字 id:{}", template.getId());
        }
        String[] queueTypeList = param1.split(Constants.DOU_HAO);
        for (String queueType : queueTypeList) {
            if (StringUtils.isEmpty(queueType)) {
                throw new ResourceException("积分获取途径，类型：玩家主动使用加速道具 参数1配置错误 id:{}", template.getId());
            }
            try {
                final int queueTypeInt = Integer.parseInt(queueType);
                if (CommonEnum.QueueTaskType.forNumber(queueTypeInt) == null) {
                    throw new ResourceException("积分获取途径，类型：玩家主动使用加速道具 参数1配置错误 不是有效的队列类型 id:{}", template.getId());
                }
            } catch (NumberFormatException nfe) {
                throw new ResourceException("积分获取途径，类型：玩家主动使用加速道具 参数1配置错误 不是数字 id:{}", template.getId());
            }
        }
    }

    private void checkIncPowerTemplate(ActivityPointsTemplate template) throws ResourceException {
        final String param1 = template.getParam1();
        if (StringUtils.isEmpty(param1)) {
            throw new ResourceException("积分获取途径，类型：提升指定战力 参数1 不能为空 id:{}", template.getId());
        }
        try {
            final int powerTypeInt = Integer.parseInt(param1);
            if (CommonEnum.PowerType.forNumber(powerTypeInt) == null) {
                throw new ResourceException("积分获取途径，类型：提升指定战力 参数1配置错误 不是有效的战力类型 id:{}", template.getId());
            }
        } catch (NumberFormatException nfe) {
            throw new ResourceException("积分获取途径，类型：提升指定战力 参数1配置错误 不是数字 id:{}", template.getId());
        }
        final String param2 = template.getParam2();
        if (StringUtils.isEmpty(param2)) {
            // 允许为空
            return;
        }
        try {
            for (String reason : param2.split(Constants.DOU_HAO)) {
                final int reasonInt = Integer.parseInt(reason);
                if (SoldierNumChangeReason.valueOf(reasonInt) == null) {
                    throw new ResourceException("积分获取途径，类型：提升指定战力 参数2配置错误 不是有效的战力变更原因 id:{}", template.getId());
                }
            }
        } catch (NumberFormatException nfe) {
            throw new ResourceException("积分获取途径，类型：提升指定战力 参数2配置错误 不是数字 id:{}", template.getId());
        }
    }

    public List<ActivityPointsTemplate> getPointsTemplates(ActivityPointsWay way) {
        return way2points.getOrDefault(way, Collections.emptyList());
    }

    public List<PointsByKillMonsterConf> getKillMonsterConfs(MonsterTemplate monsterTemplate) {
        return killMonsterMap.get(monsterTemplate.getCategory());
    }

    public List<ActivityPointsTemplate> getTrainSoldierLvConfs(int soldierLv, int fromSoldierLv) {
        ArrayListMultimap<Integer, ActivityPointsTemplate> m1 = trainSoldierLvMap.get(soldierLv);
        if (m1 == null) {
            return Collections.emptyList();
        }
        return m1.get(fromSoldierLv);
    }

    public List<ActivityPointsTemplate> getResCollectConfs(int currencyType) {
        return resCollectMap.get(CurrencyType.forNumber(currencyType));
    }

    public List<PointsByIncPowerConf> getPowerIncConfs(CommonEnum.PowerType powerType) {
        return powerIncMap.get(powerType);
    }

    public List<ActivityPointsTemplate> getKillPvpSoldierConfs(int soldierLv) {
        return killPvpSoldierMap.get(soldierLv);
    }

    public List<PointsByUseSpeedUpConf> getUseSpeedUpItemConfs(CommonEnum.QueueTaskType queueType) {
        return useSpeedUpItemMap.get(queueType.getNumber());
    }
}

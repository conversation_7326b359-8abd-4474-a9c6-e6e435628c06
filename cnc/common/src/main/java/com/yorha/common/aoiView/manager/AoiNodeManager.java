package com.yorha.common.aoiView.manager;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.aoiView.AoiViewHelper;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.aoiView.aoigrid.BriefAoiGrid;
import com.yorha.common.aoiView.aoigrid.NormalAoiGrid;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.SessionHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Shape;
import com.yorha.proto.Entity;
import com.yorha.proto.SsAoiView;
import com.yorha.proto.SsSceneInfoMgr;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class AoiNodeManager {
    private static final Logger LOGGER = LogManager.getLogger(AoiNodeManager.class);
    private final AbstractActor owner;
    private final IActorForAoiTimer actorForAoiTimer;
    private final int aoiSceneZoneId;
    /**
     * 单格横纵大小
     */
    private final int singleWidth;
    private final int singleHeight;
    /**
     * 横纵格子数
     */
    private final int xNum;
    private final int yNum;
    /**
     * 视野格子类型
     */
    private final AoiGridType type;
    /**
     * 视野格子数据
     */
    private final AoiGrid[][] aoiGrids;
    /**
     * 全部的观察者  如果是bigscene 那是空的
     */
    private final Map<Long, AoiObserverItem> observerItemMap = new Long2ObjectOpenHashMap<>();
    /**
     * 跨服的旁观者
     */
    private final Map<Long, AoiObserverItem> bystanderMap = new Long2ObjectOpenHashMap<>();
    /**
     * 跨服旁观者的检活定时器
     */
    private ActorTimer bystanderCheckTimer;
    private final Map<Long, Set<AoiGrid>> objAoiGrids = new Long2ObjectOpenHashMap<>();

    public static AoiGrid girdValueOf(AoiGridType type, int x, int y) {
        if (type == AoiGridType.AGT_BRIEF) {
            return new BriefAoiGrid(x, y);

        }
        if (type == AoiGridType.AGT_NORMAL) {
            return new NormalAoiGrid(x, y);
        }
        return null;
    }

    public AoiNodeManager(AbstractActor owner, IActorForAoiTimer actor, int aoiSceneZoneId, AoiGridType type, int mapWidth, int mapHeight, int xNum, int yNum) {
        LOGGER.debug("init aoi grids {} num: {} {}", type, xNum, yNum);
        this.owner = owner;
        this.actorForAoiTimer = actor;
        this.aoiSceneZoneId = aoiSceneZoneId;
        this.type = type;
        this.xNum = xNum;
        this.yNum = yNum;
        this.singleWidth = mapWidth / xNum;
        this.singleHeight = mapHeight / yNum;
        this.aoiGrids = new AoiGrid[xNum][yNum];
        for (int i = 0; i < xNum; i++) {
            for (int j = 0; j < yNum; j++) {
                aoiGrids[i][j] = girdValueOf(type, i, j);
            }
        }
    }

    public int getAoiSceneZoneId() {
        return aoiSceneZoneId;
    }

    /**
     * 视野切换临时变量，避免每次new
     */
    private final LongOpenHashSet oldAoiSceneObj = new LongOpenHashSet();
    private final LongOpenHashSet newAoiSceneObj = new LongOpenHashSet();

    /**
     * 5分钟旁观者检活
     */
    private void bystanderCheck() {
        if (bystanderMap.isEmpty()) {
            return;
        }
        Map<Integer, SsSceneInfoMgr.CheckPlayerOnlineAsk.Builder> msg = new HashMap<>();
        for (AoiObserverItem observer : bystanderMap.values()) {
            msg.computeIfAbsent(observer.getSceneZoneId(), k -> SsSceneInfoMgr.CheckPlayerOnlineAsk.newBuilder()).addPlayer(observer.getId());
        }
        Set<Long> playerSet = new HashSet<>(bystanderMap.keySet());
        owner.<SsSceneInfoMgr.CheckPlayerOnlineAns>batchAsk(msg,
                RefFactory::ofBigScene,
                (res, t) -> {
                    if (t != null) {
                        return;
                    }
                    playerSet.removeAll(res.getOnlinePlayerList());
                },
                () -> {
                    if (playerSet.isEmpty()) {
                        return;
                    }
                    for (Long playerId : playerSet) {
                        clearPlayerView(playerId);
                        LOGGER.warn("bystanderCheck remove player={}", playerId);
                    }
                });
    }

    /**
     * 获取旁观者
     */
    public AoiObserverItem getBystander(long playerId) {
        return bystanderMap.get(playerId);
    }

    public Map<Long, AoiObserverItem> getBystanderMap() {
        return bystanderMap;
    }

    /**
     * 检测并加入旁观者
     */
    public AoiObserverItem checkAddBystander(long player, int zoneId, IActorRef sessionRef) {
        if (bystanderMap.containsKey(player)) {
            return bystanderMap.get(player);
        }
        // 跨服查看人数达到上限
        if (bystanderMap.size() >= GameLogicConstants.SCENE_BYSTANDER_MAX) {
            LOGGER.info("checkAddBystander but byStander num more than {} refuse {}", bystanderMap.size(), player);
            return null;
        }
        AoiObserverItem observerItem = new AoiObserverItem(sessionRef, zoneId, player);
        bystanderMap.put(player, observerItem);
        LOGGER.info("checkAddBystander bystander increase curNum={} playerId={}", bystanderMap.size(), player);
        if (bystanderCheckTimer == null) {
            // zeo : 走过特殊申请了,这里owner可能是scene也可能是aoiView, 且在common包中
            bystanderCheckTimer = actorForAoiTimer.addAoiTimer(String.valueOf(owner.getId()), TimerReasonType.BYSTANDER_CHECK_TIMER,
                    this::bystanderCheck, 5, 5, TimeUnit.MINUTES, false);
        }
        return observerItem;
    }

    /**
     * 清理玩家视野
     */
    public void clearPlayerView(long playerId) {
        AoiObserverItem item = observerItemMap.remove(playerId);
        if (item == null) {
            LOGGER.warn("clearPlayerView but player not has view {}", playerId);
            return;
        }
        item.clearView();
        bystanderMap.remove(playerId);
    }

    /**
     * 清理旁观者视野
     */
    public void clearBystanderView(long playerId, boolean isChangeZone) {
        AoiObserverItem remove = bystanderMap.remove(playerId);
        if (remove == null) {
            LOGGER.warn("clearBystanderView but player not has view {}", playerId);
            return;
        }
        remove.clearView(owner, getAoiSceneZoneId(), isChangeZone);
        LOGGER.info("clearBystanderView bystander decrease curNum={} playerId={}", bystanderMap.size(), playerId);
        if (bystanderMap.isEmpty() && bystanderCheckTimer != null) {
            bystanderCheckTimer.cancel();
            bystanderCheckTimer = null;
        }
        observerItemMap.remove(playerId);
    }

    public boolean updateBriefView(SsAoiView.UpdateViewAsk ask) {
        // 兜底
        if (BigSceneConstants.isNormalLayer(ask.getLayerId())) {
            clearPlayerView(ask.getPlayerId());
            return true;
        }
        AoiObserverItem item = observerItemMap.get(ask.getPlayerId());
        if (item == null) {
            final IActorRef sessionRef = RefFactory.fromPb(ask.getSessionRef());
            boolean isBystander = ask.getSceneZoneId() != getAoiSceneZoneId();
            if (isBystander) {
                item = checkAddBystander(ask.getPlayerId(), ask.getSceneZoneId(), sessionRef);
                if (item == null) {
                    return false;
                }
            } else {
                item = new AoiObserverItem(sessionRef, ask.getSceneZoneId(), ask.getPlayerId());
            }
            observerItemMap.put(ask.getPlayerId(), item);
        }
        AABB aabb = AoiViewHelper.getAABBFromPoints(ask.getP1(), ask.getP2(), ask.getP3(), ask.getP4());
        updateBriefView(item, aabb, ask.getLayerId());
        return true;
    }

    private void updateBriefView(AoiObserverItem observer, AABB aabb, int layer) {
        Set<AoiGrid> oldAoiGrids = observer.getOldAoiGrid();
        int oldLayer = observer.getOldLayer();
        // 内部切换
        Set<AoiGrid> newAoiGrids = getAffectAoiGrids(aabb);
        // 如果没有改变层级 那就判一下是不是一样先 一样就不做操作了
        if (layer == oldLayer && !AoiViewHelper.isAoiChanged(oldAoiGrids, newAoiGrids)) {
            return;
        }
        oldAoiSceneObj.clear();
        newAoiSceneObj.clear();
        for (AoiGrid aoiGrid : oldAoiGrids) {
            // 新的没了 remove
            if (!newAoiGrids.contains(aoiGrid)) {
                aoiGrid.removeObserver(observer, oldLayer);
            }
            oldAoiSceneObj.addAll(aoiGrid.getSceneObjIds(oldLayer));
        }
        for (AoiGrid aoiGrid : newAoiGrids) {
            // 旧的没有 add
            if (!oldAoiGrids.contains(aoiGrid)) {
                aoiGrid.addObserver(observer, layer);
            } else if (layer != oldLayer) {
                // 旧的有 change layer
                aoiGrid.changeObserverLayer(observer, oldLayer, layer);
            }
            newAoiSceneObj.addAll(aoiGrid.getSceneObjIds(layer));
        }
        // 设置新的视野
        observer.setNewView(newAoiGrids, layer);
        // 求新老视野的被观察物体的差集
        LongOpenHashSet leaveAoiSceneObj = new LongOpenHashSet(oldAoiSceneObj);
        leaveAoiSceneObj.removeAll(newAoiSceneObj);
        LongOpenHashSet enterAoiSceneObj = new LongOpenHashSet(newAoiSceneObj);
        enterAoiSceneObj.removeAll(oldAoiSceneObj);
        // 真正发送数据
        if (leaveAoiSceneObj.isEmpty() && enterAoiSceneObj.isEmpty()) {
            return;
        }
        sendBriefNtfMsg(observer.getSessionRef(), leaveAoiSceneObj, enterAoiSceneObj);
    }

    /**
     * 发送简要层数据
     */
    private void sendBriefNtfMsg(IActorRef sessionRef, Set<Long> leaveAoiSceneObj, Set<Long> enterAoiSceneObj) {
        Entity.SceneObjBriefNtfMsg.Builder msgBuilder = Entity.SceneObjBriefNtfMsg.newBuilder().setZoneId(getAoiSceneZoneId());
        for (long leaveEntityId : leaveAoiSceneObj) {
            msgBuilder.addDelEntities(leaveEntityId);
        }
        for (long objId : enterAoiSceneObj) {
            Entity.SceneObjBriefAttr briefAttr = AoiViewHelper.getBriefAttr(objId);
            if (briefAttr == null) {
                continue;
            }
            msgBuilder.addNewEntities(briefAttr);
        }
        SessionHelper.sendMsgToSession(sessionRef, owner, MsgType.SCENEOBJBRIEFNTFMSG, msgBuilder.build());
    }

    public void addIntoAoi(SsAoiView.AddSceneObjCmd cmd) {
        long objId = cmd.getObjId();
        int layer = cmd.getLayer();
        Set<AoiGrid> newAoiGrids = getAffectAoiGrids(cmd.getAabb());
        // 统计新视野的玩家
        Set<AoiObserver> newAreaNormalPlayerSet = new ObjectOpenHashSet<>();
        for (AoiGrid newAoiGrid : newAoiGrids) {
            // 新的格子需要加入自己
            newAoiGrid.addSceneObj(objId, layer);
            newAreaNormalPlayerSet.addAll(newAoiGrid.getObserverIds(layer));
        }
        objAoiGrids.put(objId, newAoiGrids);
        // 发送给观察者 ntf
        sendEntityNtfPlayer(objId, newAreaNormalPlayerSet, true);
    }

    public void sceneObjOnChange(long objId, int oldLayer, int newLayer) {
        Set<AoiGrid> aoiGrids = objAoiGrids.get(objId);
        if (aoiGrids == null) {
            return;
        }
        // 是否是层级变更
        boolean isLayerChange = oldLayer != newLayer && newLayer != 0;
        Set<AoiObserver> oldObserver = new ObjectOpenHashSet<>();
        Set<AoiObserver> newObserver = new ObjectOpenHashSet<>();
        for (AoiGrid grid : aoiGrids) {
            oldObserver.addAll(grid.getBriefObserverIds(oldLayer));
            if (isLayerChange || newLayer == 0) {
                grid.changeSceneObjLayer(objId, oldLayer, newLayer);
            }
            if (isLayerChange) {
                newObserver.addAll(grid.getBriefObserverIds(newLayer));
            }
        }
        // 单纯属性更新
        if (oldLayer == newLayer) {
            sendEntityNtfPlayer(objId, oldObserver, true);
            return;
        }
        // 移除
        if (newLayer == 0) {
            sendEntityNtfPlayer(objId, oldObserver, false);
            objAoiGrids.remove(objId);
            return;
        }
        // 层级变更
        // 老的层级高 给少的人发delete
        if (oldLayer > newLayer) {
            oldObserver.removeAll(newObserver);
            sendEntityNtfPlayer(objId, oldObserver, false);
        } else {
            // 老的层级低  给新的人发new
            newObserver.removeAll(oldObserver);
            sendEntityNtfPlayer(objId, newObserver, true);
        }
    }

    private void sendEntityNtfPlayer(long objId, Set<AoiObserver> players, boolean isNew) {
        Set<IActorRef> sessionSet = new ObjectOpenHashSet<>();
        for (AoiObserver observer : players) {
            sessionSet.add(observer.getSessionRef());
        }
        sendEntityNtfSession(objId, sessionSet, isNew);
    }

    private void sendEntityNtfSession(long objId, Set<IActorRef> players, boolean isNew) {
        if (players.isEmpty()) {
            return;
        }
        Entity.SceneObjBriefNtfMsg.Builder builder = Entity.SceneObjBriefNtfMsg.newBuilder().setZoneId(getAoiSceneZoneId());
        if (isNew) {
            Entity.SceneObjBriefAttr briefAttr = AoiViewHelper.getBriefAttr(objId);
            if (briefAttr == null) {
                return;
            }
            builder.addNewEntities(briefAttr);
        } else {
            builder.addDelEntities(objId);
        }
        SessionHelper.broadcastMsgToSessions(players, getSender(), MsgType.SCENEOBJBRIEFNTFMSG, builder.build());
    }

    public Set<AoiGrid> getAffectAoiGrids(SsAoiView.AABB aabb) {
        int left = Math.max(toGridX(aabb.getLeft()), 0);
        int top = Math.max(toGridY(aabb.getTop()), 0);
        int right = Math.min(toGridX(aabb.getRight()), xNum - 1);
        int bottom = Math.min(toGridY(aabb.getBottom()), yNum - 1);

        Set<AoiGrid> ret = new ObjectOpenHashSet<>();
        for (int x = left; x <= right; ++x) {
            for (int y = top; y <= bottom; ++y) {
                ret.add(aoiGrids[x][y]);
            }
        }
        return ret;
    }

    public Set<AoiGrid> getAffectAoiGrids(Shape shape) {
        AABB aabb = shape.getAABB();
        int left = Math.max(toGridX(aabb.getLeft()), 0);
        int top = Math.max(toGridY(aabb.getTop()), 0);
        int right = Math.min(toGridX(aabb.getRight()), xNum - 1);
        int bottom = Math.min(toGridY(aabb.getBottom()), yNum - 1);

        Set<AoiGrid> ret = new ObjectOpenHashSet<>();
        for (int x = left; x <= right; ++x) {
            for (int y = top; y <= bottom; ++y) {
                ret.add(aoiGrids[x][y]);
            }
        }
        return ret;
    }

    public void consumerAffectAoiGrids(Shape shape, Consumer<AoiGrid> consumer) {
        AABB aabb = shape.getAABB();
        int left = Math.max(toGridX(aabb.getLeft()), 0);
        int top = Math.max(toGridY(aabb.getTop()), 0);
        int right = Math.min(toGridX(aabb.getRight()), xNum - 1);
        int bottom = Math.min(toGridY(aabb.getBottom()), yNum - 1);

        for (int x = left; x <= right; ++x) {
            for (int y = top; y <= bottom; ++y) {
                consumer.accept(aoiGrids[x][y]);
            }
        }
    }

    /**
     * 获取指定范围内 指定层级及以下 所有的ObserverId(mapPlayer)
     */
    public Set<AoiObserver> getAoiNormalObserverList(Shape shape, int layer) {
        AABB aabb = shape.getAABB();
        Set<AoiGrid> affectAoiGrids = getAffectAoiGrids(aabb);
        if (affectAoiGrids == null || affectAoiGrids.isEmpty()) {
            return Collections.emptySet();
        }
        Set<AoiObserver> obPlayerList = new ObjectOpenHashSet<>();
        for (AoiGrid aoiGrid : affectAoiGrids) {
            obPlayerList.addAll(aoiGrid.getObserverIds(layer));
        }
        return obPlayerList;
    }

    private int toGridX(int x) {
        return x / singleWidth;
    }

    private int toGridY(int y) {
        return y / singleHeight;
    }

    public AoiGridType getType() {
        return type;
    }

    private IActorRef getSender() {
        return owner.self();
    }

    /**
     * 获取所有Aoi格子
     * 只允许gm偶尔使用，严禁任何功能逻辑使用
     */
    public Set<AoiGrid> getAllAoiGrid() {
        if (ServerContext.isProdSvr()) {
            throw new GeminiException("very danger getAllAoiGrid");
        }

        Set<AoiGrid> s = new HashSet<>();
        for (AoiGrid[] a : aoiGrids) {
            for (AoiGrid b : a) {
                s.add(b);
            }
        }
        return s;
    }
}

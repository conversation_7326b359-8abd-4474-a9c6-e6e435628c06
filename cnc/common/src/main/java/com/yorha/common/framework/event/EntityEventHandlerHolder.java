package com.yorha.common.framework.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class EntityEventHandlerHolder {
    private static final Logger LOGGER = LogManager.getLogger(EntityEventHandlerHolder.class);

    private static final Map<String, List<EntityEventHandler<? extends EntityEvent>>> HANDLERS = new ConcurrentHashMap<>();

    public static <T extends EntityEvent> void register(Class<T> event, EntityEventHandler<T> handler) {
        HANDLERS.compute(event.getName(), (k, v) -> {
            if (v == null) {
                v = new LinkedList<>();
                v.add(handler);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("handler {} listening event {} success(new)", handler.getClass().getSimpleName(), event.getSimpleName());
                }
                return v;
            }
            for (EntityEventHandler<?> val : v) {
                if (val.getClass().equals(handler.getClass())) {
                    return v;
                }
            }
            v.add(handler);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("handler {} listening event {} success", handler.getClass().getSimpleName(), event.getSimpleName());
            }
            return v;
        });
    }

    static Collection<EntityEventHandler<? extends EntityEvent>> getHandlers(EntityEvent event) {
        return HANDLERS.getOrDefault(event.getClass().getName(), null);
    }
}

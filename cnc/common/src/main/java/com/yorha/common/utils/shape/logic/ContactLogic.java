package com.yorha.common.utils.shape.logic;

import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.shape.*;


/**
 * <AUTHOR>
 * @Description 相交逻辑
 */
public class ContactLogic {
    public static boolean intersects(Point point1, Point point2) {
        return point1.getX() == point2.getX() && point1.getY() == point2.getY();
    }

    public static boolean intersects(Circle circle1, Circle circle2) {
        int diffX = circle1.getX() - circle2.getX();
        int diffY = circle1.getY() - circle2.getY();
        int radiusSum = circle1.getR() + circle2.getR();
        return diffX * diffX + diffY * diffY <= radiusSum * radiusSum;
    }

    public static boolean intersects(Circle circle, Point point) {
        return circle.isPointInsideSelf(point);
    }

    public static boolean intersects(Rectangle rect, Point point) {
        return rect.containsPoint(point.getX(), point.getY());
    }

    /**
     * 圆和线段相交判定
     * 目前仅用于动态城阻挡
     * 线段任意一点在圆内都认为与圆相交
     * 包括线段两点都在圆内的情况都认为返回true
     */
    public static boolean intersects(Circle circle, Line line) {
        int r = circle.getR();
        Point circlePoint = circle.getCircleCenter();
        double distance = ShapeUtils.getPointRecentDistance(circlePoint, line);
        return distance <= r;
    }

    /**
     * 扇形和点相交判定
     */
    public static boolean intersects(Sector sector, Point point) {
        return sector.containsPoint(point);
    }

    /**
     * 扇形和圆相交判定
     */
    public static boolean intersects(Sector sector, Circle circle) {
        // 1、圆在扇形外部 中心距离大于两图形半径和
        int x = circle.getX() - sector.getCenter().getX();
        int y = circle.getY() - sector.getCenter().getY();
        double dotProduct = Math.sqrt(x * x + y * y);

        int distance = sector.getR() + circle.getR();
        if (dotProduct <= distance) {

            // 2、圆心在扇形夹角内
            if (sector.angleContainPoint(circle.getCircleCenter().getX(), circle.getCircleCenter().getY())) {
                return true;
            }

            // 3、圆与扇形两边相交
            return intersects(circle, Line.valueOf(sector.getCenter(), sector.getPointA()))
                    || intersects(circle, Line.valueOf(sector.getCenter(), sector.getPointB()));
        }
        return false;
    }

    /**
     * 圆和矩阵相交判定
     */
    public static boolean intersects(Circle circle, Rectangle rectangle) {
        int x = Math.abs(circle.getX() - rectangle.getCenter().getX());
        int y = Math.abs(circle.getY() - rectangle.getCenter().getY());

        if (x > (rectangle.getWidth() * 0.5 + circle.getR())) {
            return false;
        }
        if (y > (rectangle.getHeight() * 0.5 + circle.getR())) {
            return false;
        }

        if (x <= (rectangle.getWidth() * 0.5)) {
            return true;
        }
        if (y <= (rectangle.getHeight() * 0.5)) {
            return true;
        }

        double v = x - rectangle.getWidth() * 0.5;
        double v1 = y - rectangle.getHeight() * 0.5;
        double sq = v * v + v1 * v1;

        return (sq <= (circle.getR() ^ 2));
    }

    /**
     * 环和点相交判定
     */
    public static boolean intersects(Ring ring, Point point) {
        return ring.containsPoint(point.getX(), point.getY());
    }

    /**
     * 环和圆相交判定
     */
    public static boolean intersects(Ring ring, Circle circle) {
        // 与外圆相交且不在内圆里面
        return intersects(ring.getOuterCircle(), circle) && !ring.getInnerCircle().containsCircle(circle);
    }

    public static boolean intersects(AABB aabb, Point point) {
        return aabb.containsPoint(point);
    }

    public static boolean intersects(AABB aabb1, AABB aabb2) {
        int left = Math.max(aabb1.getLeft(), aabb2.getLeft());
        int right = Math.min(aabb1.getRight(), aabb2.getRight());
        if (left > right) {
            return false;
        }
        int top = Math.max(aabb1.getTop(), aabb2.getTop());
        int bottom = Math.min(aabb1.getBottom(), aabb2.getBottom());
        return top <= bottom;
    }
}


package com.yorha.common.resource.resservice.fog;

import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.UnitConvertUtils;
import res.template.MapConfigTemplate;

/**
 * <AUTHOR>
 */
public class FogMapResService extends AbstractResService {

    public FogMapResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

    }

    @Override
    public void checkValid() throws ResourceException {

    }
    
    public static MapConfigTemplate getBigSceneMapConfig() {
        // 优先读取配置中心
        final int bigSceneMapId = BigSceneConstants.BIG_SCENE_MAP_ID;
        return ResHolder.getInstance().getValueFromMap(MapConfigTemplate.class, bigSceneMapId);
    }

    public int getWidth() {
        return UnitConvertUtils.meterToCm(getBigSceneMapConfig().getLength());
    }

    public int getHeight() {
        return UnitConvertUtils.meterToCm(getBigSceneMapConfig().getWidth());
    }

}

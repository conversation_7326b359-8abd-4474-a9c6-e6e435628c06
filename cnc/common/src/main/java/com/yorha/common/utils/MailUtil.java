package com.yorha.common.utils;

import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.mail.MailIdType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ItemTemplate;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MailUtil {
    private static final Logger LOGGER = LogManager.getLogger(MailUtil.class);

    public static long genMailId(final MailIdType mailIdType) {
        return IdFactory.nextId("genMailId_" + mailIdType.name());
    }

    /**
     * tell发送邮件给个人(若可获取playerEntity，应优先调用PlayerMailComponent::sendPersonalMail)
     *
     * @param receiver       收件者
     * @param mailSendParams 邮件参数
     * @return mailId
     */
    public static long sendMailToPlayer(final CommonMsg.MailReceiver receiver, final StructMail.MailSendParams mailSendParams) {
        final long mailId = genMailId(MailIdType.PLAYER);
        LOGGER.info("MailUtil sendMailToPlayer receiver={}, mailTemplateId={}, mailId={}", receiver, mailSendParams.getMailTemplateId(), mailId);
        // 添加邮件content
        final StructMail.NewMailCache.Builder builder = MailUtil.buildNewMailCachePb(mailSendParams, CommonEnum.MailType.MAIL_TYPE_PERSONAL, 0, mailId, 0);

        final SsPlayerMisc.OnReceiveMailCmd.Builder cmd = SsPlayerMisc.OnReceiveMailCmd.newBuilder()
                .setNewMailCache(builder.build());
        ActorSendMsgUtils.sendAndCreate(RefFactory.ofPlayer(receiver.getZoneId(), receiver.getPlayerId()), cmd.build());
        return mailId;
    }

    /**
     * tell发送邮件给多人
     *
     * @param receivers      收件者们
     * @param mailSendParams 邮件参数
     * @return mailIds
     */
    public static long sendMailToPlayers(final List<CommonMsg.MailReceiver> receivers, final StructMail.MailSendParams mailSendParams) {
        final long mailId = genMailId(MailIdType.PLAYER);
        LOGGER.info("MailUtil sendMailToPlayers receivers={}, mailTemplateId={}, mailId={}", receivers, mailSendParams.getMailTemplateId(), mailId);
        // 添加邮件content
        final StructMail.NewMailCache.Builder builder = MailUtil.buildNewMailCachePb(mailSendParams, CommonEnum.MailType.MAIL_TYPE_PERSONAL, 0, mailId, 0);

        final SsPlayerMisc.OnReceiveMailCmd.Builder cmd = SsPlayerMisc.OnReceiveMailCmd.newBuilder()
                .setNewMailCache(builder.build());
        for (CommonMsg.MailReceiver receiver : receivers) {
            ActorSendMsgUtils.sendAndCreate(RefFactory.ofPlayer(receiver.getZoneId(), receiver.getPlayerId()), cmd.build());
        }
        return mailId;
    }

    /**
     * tell发送idip邮件给玩家
     *
     * @param receiver       收件者
     * @param mailSendParams 邮件参数
     * @param idIpMailData   idip参数
     * @return mailId
     */
    public static long sendIdIpMailToPlayer(
            final CommonMsg.MailReceiver receiver,
            final StructMail.MailSendParams mailSendParams,
            final @Nullable StructMsg.IdIpMailData idIpMailData
    ) {
        final long mailId = genMailId(MailIdType.IDIP);
        LOGGER.info("MailUtil sendIdIpMailToPlayer receiver={}, mailTemplateId={}, mailId={}", receiver, mailSendParams.getMailTemplateId(), mailId);

        // 添加邮件content
        final StructMail.NewMailCache.Builder builder = MailUtil.buildNewMailCachePb(mailSendParams, CommonEnum.MailType.MAIL_TYPE_PERSONAL, 0, mailId, 0);

        final SsPlayerMisc.OnReceiveMailCmd.Builder cmd = SsPlayerMisc.OnReceiveMailCmd.newBuilder()
                .setNewMailCache(builder.build());
        if (idIpMailData != null) {
            cmd.setIdIpMailData(idIpMailData);
        }
        ActorSendMsgUtils.sendAndCreate(RefFactory.ofPlayer(receiver.getZoneId(), receiver.getPlayerId()), cmd.build());
        return mailId;

    }


    /**
     * tell发送军团邮件
     *
     * @param zoneId         军团所在zoneId
     * @param clanId         军团id
     * @param mailSendParams 邮件参数
     * @return mailId
     */
    public static long sendClanMail(final int zoneId, final long clanId, final StructMail.MailSendParams mailSendParams) {
        final long mailId = genMailId(MailIdType.CLAN);
        LOGGER.info("MailUtil sendClanMail zoneId={}, clanId={}, mailTemplateId={}, mailId={}", zoneId, clanId, mailSendParams.getMailTemplateId(), mailId);
        // 构建cmd
        SsClanBase.SendClanMailAndSaveAsk cmd = SsClanBase.SendClanMailAndSaveAsk.newBuilder()
                .setMailSendParams(mailSendParams)
                .setMailId(mailId)
                .build();
        ActorSendMsgUtils.send(RefFactory.ofClan(zoneId, clanId), cmd);
        return mailId;
    }

    /**
     * tell发送全服邮件
     *
     * @param zoneId         目标zoneId
     * @param mailSendParams 邮件参数
     */
    public static void sendZoneMail(final int zoneId, StructMail.MailSendParams mailSendParams) {
        final long mailId = genMailId(MailIdType.ZONE);
        SsSceneMail.SendZoneMailCmd cmd = SsSceneMail.SendZoneMailCmd.newBuilder()
                .setMailId(mailId)
                .setMailSendParams(mailSendParams)
                .build();
        ActorSendMsgUtils.send(RefFactory.ofBigScene(zoneId), cmd);
    }


    /**
     * 邮件参数pb转无pb字段
     */
    public static StructMail.MailSendParams mailParamPb2proto(StructMailPB.MailSendParamsPB.Builder pb) {
        try {
            return StructMail.MailSendParams.newBuilder().mergeFrom(pb.build().toByteArray()).build();
        } catch (InvalidProtocolBufferException e) {
            WechatLog.error("MailUtil mailParamPb2proto deserialize proto failed {} {}", pb, e);
        }
        return null;
    }

    /**
     * 构建发送给player的邮件Builder
     */
    public static StructMail.NewMailCache.Builder buildNewMailCachePb(
            StructMail.MailSendParams mailSendParams,
            CommonEnum.MailType mailType,
            long mailTypeId,
            long mailId,
            int nextMailIndex
    ) {
        StructMail.NewMailCache.Builder builder = StructMail.NewMailCache.newBuilder();
        builder.setMailId(mailId);
        builder.setMailIndex(nextMailIndex);
        builder.setMailType(mailType);
        builder.setMailTypeId(mailTypeId);
        builder.setMailTemplateId(mailSendParams.getMailTemplateId());
        builder.setSender(mailSendParams.getSender());
        builder.setContent(mailSendParams.getContent());
        builder.setCreateTimestamp(SystemClock.now());
        builder.setTitle(mailSendParams.getTitle());
        builder.setItemReward(safeMailItemReward(mailSendParams.getMailTemplateId(), mailSendParams.getItemReward()));
        builder.setExpireTimestamp(mailSendParams.getExpireTimestamp());
        builder.setIsOnlyForShow(mailSendParams.getIsOnlyForShow());
        builder.setHasInvitedStatus(mailSendParams.getHasInvitedStatus());
        builder.getAddItemByMailBuilder().mergeFrom(mailSendParams.getAddItemByMail());
        return builder;
    }

    /**
     * 检查可变的邮件道具奖励（非读表）是否合法，不合法的会wechat告警
     *
     * @param mailTemplateId 邮件配置id
     * @param rawItemPair    邮件奖励
     * @return 合法的邮件道具奖励
     */
    private static Struct.ItemPairList safeMailItemReward(final long mailTemplateId, final Struct.ItemPairList rawItemPair) {
        Struct.ItemPairList.Builder checkedItemPair = Struct.ItemPairList.newBuilder();
        for (Struct.ItemPair rawItem : rawItemPair.getDatasList()) {
            int count = rawItem.getCount();
            int itemTemplateId = rawItem.getItemTemplateId();
            if (count <= 0) {
                WechatLog.error("MailUtil safeMailItemReward reward count invalid, mailTemplateId={}, itemTemplateId={}, count={}",
                        mailTemplateId, itemTemplateId, count);
                continue;
            }
            if (ResHolder.getInstance().findValueFromMap(ItemTemplate.class, itemTemplateId) == null) {
                WechatLog.error("MailUtil safeMailItemReward reward itemTemplateId no found, mailTemplateId={}, itemTemplateId={}, count={}",
                        mailTemplateId, itemTemplateId, count);
                continue;
            }
            checkedItemPair.addDatas(rawItem);
        }
        return checkedItemPair.build();
    }

    /**
     * 邮件下标检查
     *
     * @param cacheIndex  缓存的下标
     * @param playerIndex 玩家拥有的下标
     * @return true==通过检查
     */
    public static boolean mailIndexChecker(int cacheIndex, int playerIndex) {
        return cacheIndex > playerIndex;
    }

    /**
     * 邮件创建时间检查
     *
     * @param mailCreateTime 邮件创建时间
     * @param time           时间戳
     * @return true==创建时间晚于指定时间戳
     */
    public static boolean mailCreateTimeChecker(long mailCreateTime, long time) {
        return mailCreateTime >= time;
    }

    /**
     * 邮件过期时间检查
     *
     * @param mailExpireTime 邮件过期时间
     * @return true==邮件未过期
     */
    public static boolean mailExpireTimeChecker(long mailExpireTime) {
        if (mailExpireTime == 0) {
            return true;
        } else {
            return mailExpireTime > SystemClock.now();
        }
    }

    public static StructMail.MailSendParams.Builder genMailParamsOnlyForShow(int mailTemplateId, List<IntPairType> reward) {
        StructMail.MailSendParams.Builder params = StructMail.MailSendParams.newBuilder()
                .setMailTemplateId(mailTemplateId)
                .setIsOnlyForShow(true);
        for (IntPairType itemReward : reward) {
            Struct.ItemPair.Builder pairBuilder = Struct.ItemPair.newBuilder()
                    .setItemTemplateId(itemReward.getKey()).setCount(itemReward.getValue());
            params.getItemRewardBuilder().addDatas(pairBuilder.build());
        }
        return params;
    }
}


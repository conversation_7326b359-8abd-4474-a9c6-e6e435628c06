package com.yorha.common.utils.time.schedule;

import com.github.benmanes.caffeine.cache.Scheduler;
import com.yorha.common.concurrent.NameableThreadFactory;
import com.yorha.common.concurrent.NamedRunnable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 系统定时器管理器
 * 受系统时间调度，不受业务时间调整影响
 *
 * <AUTHOR>
 */
public class SystemScheduleMgr {
    private static final Logger LOGGER = LogManager.getLogger(SystemScheduleMgr.class);

    public static SystemScheduleMgr getInstance() {
        return SystemScheduleMgr.LazyHolder.INSTANCE;
    }

    private static class LazyHolder {
        private static final SystemScheduleMgr INSTANCE = new SystemScheduleMgr();
    }

    /**
     * 定时任务单线程队列线程池
     */
    private final ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;
    private final Scheduler scheduler;

    private SystemScheduleMgr() {
        this.scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(1, new NameableThreadFactory("SystemScheduleMgr", false));
        this.scheduler = Scheduler.forScheduledExecutorService(scheduledThreadPoolExecutor);
    }

    /**
     * 对外可见
     *
     * @return 调度器接口
     */
    public Scheduler getScheduler() {
        return scheduler;
    }

    public ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit) {
        return this.scheduledThreadPoolExecutor.schedule(() -> {
            try {
                command.run();
            } catch (Exception e) {
                LOGGER.error("SystemScheduleMgr schedule catch:", e);
            }
        }, delay, unit);
    }

    public ScheduledFuture<?> scheduleWithFixedDelay(NamedRunnable runnable, long initialDelay, long period, TimeUnit unit) {
        return this.scheduledThreadPoolExecutor.scheduleWithFixedDelay(() -> {
            try {
                runnable.run();
            } catch (Exception e) {
                LOGGER.error("SystemScheduleMgr scheduleWithFixedDelay catch:", e);
            }
        }, initialDelay, period, unit);
    }

    public void shutdown() {
        this.scheduledThreadPoolExecutor.shutdown();
        try {
            this.scheduledThreadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}

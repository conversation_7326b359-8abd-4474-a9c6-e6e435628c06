package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.TcaplusUtils;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class TcaplusGetByPartKey<T extends Message.Builder> extends TcaplusOperation<T, GetByPartKeyOption, GetByPartKeyResult<T>> {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusGetByPartKey.class);
    private static final int MAX_RECORD_NUM_PER_GET = 50;

    /**
     * 开始时间戳。
     */
    private long startTsMs;
    /**
     * 超时时间戳，用于计算超时。
     */
    private long timeoutTsMs;
    /**
     * 剩余需要捞取的数量。
     */
    private int remainingCnt;
    /**
     * 游标偏移。
     */
    private int offset;
    /**
     * 是否继续获取。
     */
    private boolean needFetch;
    /**
     * 结果。
     */
    private GetByPartKeyResult<T> result;
    /**
     * 请求对象。
     */
    private Request request;
    /**
     * 秒表。
     */
    private final GeminiStopWatch watch;
    /**
     * 回包数量。
     */
    private int responsePackageCnt = 0;

    public TcaplusGetByPartKey(Client client, T t, GetByPartKeyOption option) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, option);
        this.watch = new GeminiStopWatch("GetByPartKey#" + this.getTableName());
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_GET_BY_PARTKEY_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        // 通过addFieldName设置查询的字段
        if (getOption().isGetAllFields()) {
            for (Descriptors.FieldDescriptor e : getFieldMetaData().valueFieldsList) {
                request.addFieldName(e.getName());
                this.addRequestBytes(e.getName().length());
            }
        } else {
            if (getOption().getFieldNames() != null) {
                this.addRequestedFields(getOption().getFieldNames(), this.getReq().getDescriptorForType(), request);
            }
        }
        final Record record = request.addRecord();
        this.setRequestKeys(getReq(), record);
        // https://tcaplusdb.tencent.com/UserGuide/05TcaplusDB_SDK_and_API/01TDR%E8%A1%A8SDK_and_API/01C++_SDK/02%E5%93%8D%E5%BA%94%E5%88%86%E5%8C%85%E9%97%AE%E9%A2%98%E8%AF%B4%E6%98%8E.html
        // getByPartKey必须要分包，否则超过256KB还是会分包。
        request.setMultiResponseFlag(1);
    }

    @Override
    protected GetByPartKeyResult<T> buildResult(Response response) {
        throw new NotImplementedException();
    }


    @Override
    public GetByPartKeyResult<T> run() {
        this.watch.mark("run");
        this.prepare();
        this.watch.mark("prepare");
        this.onDbRequest();
        this.doRunSync();
        final long costMs = this.onDbResponse(result.getCode());
        this.watch.mark("end all");
        if (!this.result.isOk()) {
            LOGGER.info("TcaplusGetByPartKey sync fail, requestId={}, tableName={}, code={}, offset={}, limit={}, costMs={}, recordNum={}, responsePkgCnt={}",
                    getRequestId(), getTableName(), result.getCode(), getOption().getResultOffset(),
                    getOption().getResultLimit(), costMs, result.getValues().size(), responsePackageCnt);
            return this.result;
        }
        if (costMs > MonitorConstant.WARNING_GET_BY_PART_KEY_COST_TIME_MS) {
            LOGGER.warn("TcaplusGetByPartKey sync slow, requestId={}, tableName={}, code={}, offset={}, limit={}, recordNum={}, responsePkgCnt={}, costMs={}, stat={}",
                    getRequestId(), getTableName(), result.getCode(),
                    getOption().getResultOffset(), getOption().getResultLimit(), result.getValues().size(),
                    responsePackageCnt, costMs, watch.stat());
        } else {
            LOGGER.info("TcaplusGetByPartKey sync successful, requestId={}, tableName={}, code={}, offset={}, limit={}, recordNum={}, costMs={}, responsePkgCnt={}",
                    getRequestId(), getTableName(), result.getCode(),
                    getOption().getResultOffset(), getOption().getResultLimit(), result.getValues().size(), costMs, responsePackageCnt);
        }
        return this.result;
    }

    private void prepare() {
        this.startTsMs = SystemClock.nowNative();
        this.request = this.client.acquireRequest();
        this.request.setCmd(getType());
        this.request.setTableName(this.getFieldMetaData().tableName);
        this.configRequestProperty(this.request);
        final long timeout = this.getOption().getTimeoutTimeMs() != 0 ? Math.max(this.getOption().getTimeoutTimeMs(), 1) : 5000;
        this.timeoutTsMs = this.startTsMs + timeout;
        this.remainingCnt = this.getOption().getResultLimit();
        this.offset = this.getOption().getResultOffset();
        this.needFetch = remainingCnt > 0 || this.isGetAll();
        this.result = new GetByPartKeyResult<>();
        this.result.values = new LinkedList<>();
        this.result.code = TcaplusErrorCode.GEN_ERR_SUC;
        this.result.requestId = this.getRequestId();
    }

    private boolean isGetAll() {
        return this.getOption().getResultLimit() == 0;
    }

    private void doRunSync() {
        while (this.needFetch) {
            final long nowTsMs = SystemClock.nowNative();
            // 剩余变量
            final int fc = remainingCnt > 0 ? Math.min(remainingCnt, MAX_RECORD_NUM_PER_GET) : MAX_RECORD_NUM_PER_GET;
            final long timeoutDurationMs = this.timeoutTsMs - nowTsMs;
            // 超时
            if (timeoutDurationMs < 0) {
                this.result.code = TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT;
                this.result.values = Collections.emptyList();
                this.needFetch = false;
                break;
            }
            // 设置游标和需要获取的数量
            this.request.setResultLimit(fc, offset);
            // 获取当前请求
            final List<Response> responseList = TcaplusUtils.requestMultipleResponse(this.client, this.request, timeoutDurationMs);
            this.watch.mark(StringUtils.format("end db fc={}, offset={}", fc, offset));
            // 回调请求
            this.onResponse(responseList);
            this.watch.mark(StringUtils.format("end all fc={}, offset={}", fc, offset));
        }
    }

    @Override
    public void runAsync(Consumer<GetByPartKeyResult<T>> cb) {
        this.watch.mark("runAsync");
        this.prepare();
        this.watch.mark("prepare");
        this.onDbRequest();
        this.doRunAsync(cb);
    }

    private void doRunAsync(Consumer<GetByPartKeyResult<T>> cb) {
        final long nowTsMs = SystemClock.nowNative();
        final int fc = remainingCnt > 0 ? Math.min(remainingCnt, MAX_RECORD_NUM_PER_GET) : MAX_RECORD_NUM_PER_GET;
        final long timeoutDurationMs = this.timeoutTsMs - nowTsMs;
        if (timeoutDurationMs < 0) {
            this.result.code = TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT;
            this.needFetch = false;
            cb.accept(this.result);
            return;
        }
        this.request.setResultLimit(fc, this.offset);
        TcaplusUtils.requestMultipleResponseAsync(this.client, this.request, (responseList -> {
            this.watch.mark(StringUtils.format("end db fc={}, offset={}", fc, this.offset));
            this.onResponse(responseList);
            this.watch.mark(StringUtils.format("end all fc={}, offset={}", fc, this.offset));
            if (this.needFetch) {
                this.doRunAsync(cb);
                return;
            }
            final long costMs = this.onDbResponse(result.getCode());
            this.watch.mark("end all");
            if (!this.result.isOk()) {
                var offset = getOption().getResultOffset();
                var limit = getOption().getResultLimit();
                LOGGER.info("TcaplusGetByPartKey async fail, reqId={}, table={}, code={}, offset={}, limit={}, recordNum={}, costMs={}, responsePkgCnt={}",
                        getRequestId(), getTableName(), result.getCode(), offset, limit, result.values.size(), costMs, responsePackageCnt);
                cb.accept(this.result);
                return;
            }
            if (costMs > MonitorConstant.WARNING_GET_BY_PART_KEY_COST_TIME_MS) {
                LOGGER.warn("TcaplusGetByPartKey async slow, reqId={}, table={}, code={}, offset={}, limit={}, recordNum={}, responsePkgCnt={}, costMs={}, stat={}",
                        getRequestId(), getTableName(), result.getCode(), getOption().getResultOffset(),
                        getOption().getResultLimit(), result.values.size(), responsePackageCnt, costMs, watch.stat());
            } else {
                LOGGER.info("TcaplusGetByPartKey async successful, reqId={}, table={}, code={}, offset={}, limit={}, recordNum={}, costMs={}, responsePkgCnt={}",
                        getRequestId(), getTableName(), result.getCode(),
                        getOption().getResultOffset(), getOption().getResultLimit(), result.values.size(), costMs, responsePackageCnt);
            }
            cb.accept(this.result);
        }));
    }

    private void onResponse(List<Response> responseList) {
        this.responsePackageCnt += responseList.size();
        final GetByPartKeyResult<T> resultCurFetch = this.buildResult(responseList);
        this.result.values.addAll(resultCurFetch.values);
        // 校准游标
        final int totalNum = responseList.isEmpty() ? 0 : responseList.getFirst().getTotalNum();
        this.result.code = resultCurFetch.code;
        assert resultCurFetch.values != null;
        if (this.remainingCnt > 0) {
            this.remainingCnt = remainingCnt - resultCurFetch.values.size();
        }
        // 首次offset是负数，将根据首次返回的totalNum，标定实际的数据
        if (this.offset < 0) {
            this.offset = totalNum + this.offset;
            // 1. < 0 代表record数量不足，表示偏移从0 开始。2. 偏移从实际偏移开始
            this.offset = Math.max(offset, 0);
        }
        this.offset = this.offset + resultCurFetch.values.size();
        // DB问题：1. 返回values为null 2. 请求出问题。  3. totalNum <= 0代表无数据。4. 偏移已经到最后的数量，发生则退出循环
        this.needFetch = !resultCurFetch.values.isEmpty() && resultCurFetch.isOk() && totalNum > 0 && this.offset < totalNum;
        // 业务问题：不拉取所有数据且已经取完所有数据，则退出循环
        this.needFetch = this.needFetch && (this.remainingCnt > 0 || this.isGetAll());
    }

    protected GetByPartKeyResult<T> buildResult(List<Response> responseList) {
        final GetByPartKeyResult<T> result = new GetByPartKeyResult<>();
        result.values = new LinkedList<>();
        for (int i = 0; i < responseList.size(); i++) {
            final Response response = responseList.get(i);
            final List<Record> recordList = response.getRecordList() == null ? Collections.emptyList() : response.getRecordList();
            final List<Record> errorRecordList = response.getErrorRecords() == null ? Collections.emptyList() : response.getErrorRecords();
            LOGGER.info("TcaplusGetByPartKey buildResult, requestId={}, tableName={}, index={}, code={}, hasMore={}, recordNum={}, failNum={}",
                    this.getRequestId(), response.getTableName(), i, response.getResult(), response.hasMore(), recordList.size(), errorRecordList.size());
            result.code = TcaplusErrorCode.forNumber(response.getResult());
            if (response.getResult() != TcaplusErrorCode.GEN_ERR_SUC.getValue()) {
                result.values = Collections.emptyList();
                return result;
            }
            for (final Record record : recordList) {
                final ValueWithVersion<T> value = new ValueWithVersion<>();
                value.version = record.getVersion();
                value.value = buildDefaultValue(this.getReq());
                result.values.add(value);
                this.readFromResponseValues(record, value.value);
            }
        }
        return result;
    }
}

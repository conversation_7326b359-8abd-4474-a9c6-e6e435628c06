package com.yorha.common.resource.resservice.multiServer;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import res.template.ConstServerTemplate;
import res.template.ServerAttributeTemplate;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class MultiServerService extends AbstractResService {

    public MultiServerService(ResHolder resHolder) {
        super(resHolder);
    }

    /**
     * 爆满注册人数
     */
    private int fullRegisterPlayerNum;
    /**
     * 爆满在线人数
     */
    private int fullOnlinePlayerNum;
    /**
     * 拥挤在线人数
     */
    private int busyOnlinePlayerNum;
    /**
     * 分组编号下的服务器id
     */
    private final Map<Integer, Set<Integer>> zonesUnderGroupId = new HashMap<>();

    @Override
    public void load() {
        loadConstTemplate();
        loadServerAttributeTemplate();
    }


    @Override
    public void checkValid() throws ResourceException {
        checkConstTemplate();
        checkServerAttributeTemplate();
    }

    private void loadServerAttributeTemplate() {
        for (final ServerAttributeTemplate template : getResHolder().getListFromMap(ServerAttributeTemplate.class)) {
            final int groupId = template.getGroupid();
            final int zoneId = template.getId();
            zonesUnderGroupId.computeIfAbsent(groupId, (k) -> new HashSet<>()).add(zoneId);
        }
    }

    private void checkServerAttributeTemplate() throws ResourceException {
        if (ServerContext.isZoneServer()) {
            int zoneId = ServerContext.getZoneId();
            ServerAttributeTemplate valueFromMap = getResHolder().findValueFromMap(ServerAttributeTemplate.class, zoneId);
            if (valueFromMap == null) {
                throw new ResourceException("多服表对应当前zone无配置。zoneId:{}", zoneId);
            }
        }
    }

    private void checkConstTemplate() throws ResourceException {
        final ConstServerTemplate constServerTemplate = getResHolder().getConstTemplate(ConstServerTemplate.class);
        if (constServerTemplate.getServerFullDay() <= 0) {
            throw new ResourceException("服务器多服和移民表 const_server serverFullDay应为正数");
        }

        if (constServerTemplate.getServerMaxRegisterPlayerNum() <= 0) {
            throw new ResourceException("服务器多服和移民表 const_server serverMaxRegisterPlayerNum应为正数");
        }

        if (constServerTemplate.getServerOnlinePlayerPercent() <= 0 || constServerTemplate.getServerOnlinePlayerPercent() > 100) {
            throw new ResourceException("服务器多服和移民表 const_server serverOnlinePlayerPercent应在(0,100]区间内");
        }

        if (constServerTemplate.getServerFullRegisterPercent() <= 0 || constServerTemplate.getServerFullRegisterPercent() > 100) {
            throw new ResourceException("服务器多服和移民表 const_server serverFullRegisterPercent应在(0,100]区间内");
        }

        if (constServerTemplate.getServerFullOnlinePlayerPercent() <= 0 || constServerTemplate.getServerFullOnlinePlayerPercent() > 100) {
            throw new ResourceException("服务器多服和移民表 const_server serverFullOnlinePlayerPercent应在(0,100]区间内");
        }

        if (constServerTemplate.getServerBusyOnlinePlayerPercent() <= 0 || constServerTemplate.getServerBusyOnlinePlayerPercent() > 100) {
            throw new ResourceException("服务器多服和移民表 const_server serverBusyOnlinePlayerPercent应在(0,100]区间内");
        }
    }

    private void loadConstTemplate() {
        final ConstServerTemplate constServerTemplate = getResHolder().getConstTemplate(ConstServerTemplate.class);
        final int maxOnlinePlayerNum = constServerTemplate.getServerMaxRegisterPlayerNum() * constServerTemplate.getServerOnlinePlayerPercent() / 100;
        fullRegisterPlayerNum = constServerTemplate.getServerMaxRegisterPlayerNum() * constServerTemplate.getServerFullRegisterPercent() / 100;
        fullOnlinePlayerNum = maxOnlinePlayerNum * constServerTemplate.getServerFullOnlinePlayerPercent() / 100;
        busyOnlinePlayerNum = maxOnlinePlayerNum * constServerTemplate.getServerBusyOnlinePlayerPercent() / 100;
    }

    @Nullable
    public Set<Integer> getZonesUnderGroup(final int groupId) {
        return zonesUnderGroupId.get(groupId);
    }

    public int getBusyOnlinePlayerNum() {
        return busyOnlinePlayerNum;
    }

    public int getFullOnlinePlayerNum() {
        return fullOnlinePlayerNum;
    }

    public int getFullRegisterPlayerNum() {
        return fullRegisterPlayerNum;
    }

}
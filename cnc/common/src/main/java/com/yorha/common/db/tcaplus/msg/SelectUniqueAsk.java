package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetResult;

public class SelectUniqueAsk<T extends Message.Builder> implements GameDbReq<GetResult<T>> {
    private final T req;
    private final GetOption option;

    public SelectUniqueAsk(T req) {
        this(req, GetOption.newBuilder().build());
    }

    public SelectUniqueAsk(T req, GetOption option) {
        this.req = req;
        this.option = option;
    }

    public T getReq() {
        return req;
    }

    public GetOption getOption() {
        return option;
    }
}

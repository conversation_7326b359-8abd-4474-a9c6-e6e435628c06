package com.yorha.common.utils;

import com.google.common.base.Charsets;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.FileOutputStream;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;

/**
 * 进程工具类
 *
 * <AUTHOR>
 */
public class ProcessUtil {
    private static final Logger LOGGER = LogManager.getLogger(ProcessUtil.class);

    public static String getPid() {
        RuntimeMXBean bean = ManagementFactory.getRuntimeMXBean();
        return bean.getName().split("@")[0];
    }

    public static void savePid(String fileName) {
        String pid = getPid();
        try (FileOutputStream out = new FileOutputStream(fileName + ".pid")) {
            out.write(pid.getBytes(Charsets.UTF_8));
            LOGGER.info("saveProcessID success {} {}", fileName, pid);
        } catch (Exception e) {
            LOGGER.error("saveProcessID error {}", fileName, e);
        }
    }
}

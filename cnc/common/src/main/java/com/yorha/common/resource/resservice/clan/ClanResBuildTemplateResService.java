package com.yorha.common.resource.resservice.clan;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum.MapBuildingType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapBuildingTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 军团资源建筑配表获取service
 *
 * <AUTHOR>
 */
public class ClanResBuildTemplateResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(ClanResBuildTemplateResService.class);

    public ClanResBuildTemplateResService(ResHolder resHolder) {
        super(resHolder);
    }

    private Map<MapBuildingType, Integer> typeToTemplateId = new HashMap<>();

    @Override
    public void load() throws ResourceException {
        typeToTemplateId = parseTypeToTemplateId();
    }

    @Override
    public void checkValid() throws ResourceException {
    }

    // ---------------------------- load functions ---------------------------- //

    public Map<MapBuildingType, Integer> parseTypeToTemplateId() {
        Map<MapBuildingType, Integer> typeToTemplateId = new HashMap<>(4);
        for (MapBuildingTemplate template : getResHolder().getListFromMap(MapBuildingTemplate.class)) {
            if (isClanResBuildType(template.getType())) {
                typeToTemplateId.put(template.getType(), template.getId());
            }
        }
        return typeToTemplateId;
    }

    // ---------------------------- interface ---------------------------- //

    public int getResBuildTemplateIdByType(MapBuildingType type) {
        if (!typeToTemplateId.containsKey(type)) {
            LOGGER.warn("type isn't res build type, type: {}", type);
            return -1;
        }
        return typeToTemplateId.get(type);
    }

    // ---------------------------- static method ---------------------------- //

    public static boolean isClanResBuildType(MapBuildingType type) {
        return type == MapBuildingType.MBT_CLAN_RES_OIL ||
                type == MapBuildingType.MBT_CLAN_RES_STEEL ||
                type == MapBuildingType.MBT_CLAN_RES_RARE_EARTH ||
                type == MapBuildingType.MBT_CLAN_RES_TIBERIUM;
    }
}

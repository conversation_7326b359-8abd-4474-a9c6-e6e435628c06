package com.yorha.common.db.tcaplus.option;

import com.google.protobuf.Message;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.FastFailActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.TcaplusUtils;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

/**
 * 遍历数据库表格所使用的Option，注意整个遍历操作本身很耗，慎用！！！
 *
 * <AUTHOR>
 */
public class TraverseOption<T extends Message.Builder> {
    private List<String> fieldNames;
    private boolean isNeedAllFields = true;
    private int totalLimit;
    private long timeoutMs;
    private Consumer<List<ValueWithVersion<T>>> handler;
    private boolean isSync;

    /**
     * @return 请求是否同步。
     */
    public boolean isSync() {
        return isSync;
    }

    /**
     * @return 回调方法，遍历结果会分包，每个分包会组织成一次回调，调用handler。
     */
    public Consumer<List<ValueWithVersion<T>>> getHandler() {
        return handler;
    }

    /**
     * @return 设置一批最多返回的数据条数，由于扫描返回的数据量通常较多，需要分批返回。
     */
    public int getTotalLimit() {
        return this.totalLimit;
    }

    /**
     * @return 是否获得所有的字段。
     */
    public boolean isNeedAllFields() {
        return this.isNeedAllFields;
    }

    /**
     * @return 遍历返回记录的属性字段列表。
     */
    public List<String> getFieldNames() {
        return this.fieldNames;
    }

    public long getTimeoutMs() {
        return this.timeoutMs;
    }

    @Override
    public String toString() {
        return "TraverseOption{" +
                "fieldNames=" + fieldNames +
                ", isNeedAllFields=" + isNeedAllFields +
                ", totalLimit=" + totalLimit +
                ", timeoutMs=" + timeoutMs +
                '}';
    }

    public static <T extends Message.Builder> Builder<T> newBuilder() {
        return new Builder<>();
    }

    public static class Builder<T extends Message.Builder> {
        private static final int MIN_TIMEOUT_MS = TcaplusUtils.TCAPLUS_TIMEOUT_MS + 1_000;

        private List<String> fieldNames = Collections.emptyList();
        private boolean getAllFields = true;
        private int totalLimit = -1;
        private long timeoutMs = MIN_TIMEOUT_MS;
        private Consumer<List<ValueWithVersion<T>>> handler = (v) -> {
        };
        private boolean isSync = true;

        private Builder() {
        }

        /**
         * 遍历过程中，拉取服务器所需的字段列表，和withAllFields互斥。
         * 默认: 空。
         *
         * @param fieldNames 字段列表。
         * @return Builder。
         */
        public Builder<T> fieldNames(String... fieldNames) {
            this.fieldNames = Arrays.asList(fieldNames);
            this.getAllFields = false;
            return this;
        }

        /**
         * 遍历过程，获取所有字段，和fieldNames互斥。
         * 默认：true
         *
         * @return Builder。
         */
        public Builder<T> withAllFields() {
            this.fieldNames = null;
            this.getAllFields = true;
            return this;
        }

        /**
         * 设置总共最多返回的数据条数。
         * 默认值：-1，代表全部。
         *
         * @param totalLimit 设置的数量。
         * @return Builder。
         */
        public Builder<T> totalLimit(final int totalLimit) {
            if (totalLimit < 0) {
                throw new IllegalArgumentException("totalLimit < 0");
            }
            this.totalLimit = totalLimit;
            return this;
        }

        /**
         * 设置整个请求的超时时间（单位：ms）。
         * 默认值：5000ms=(TcaplusUtils.TCAPLUS_TIMEOUT_MS + 1_000)
         *
         * @param timeoutMs 超时时间。
         * @return Builder。
         */
        public Builder<T> timeoutMs(final long timeoutMs) {
            if (timeoutMs <= MIN_TIMEOUT_MS) {
                throw new IllegalArgumentException("timeoutMs <= " + MIN_TIMEOUT_MS);
            }
            this.timeoutMs = timeoutMs;
            return this;
        }

        /**
         * 设置每个遍历回调函数的信息。
         * 注意：异步，会将handler发送到actor上跑。默认同步方式。
         *
         * @param receiver 接收者。
         * @param handler  回调函数。
         * @return Builder。
         */
        public <Actor extends AbstractActor> Builder<T> handlerForEachResponse(final Actor receiver, final Consumer<List<ValueWithVersion<T>>> handler) {
            if (receiver == null) {
                throw new IllegalArgumentException("receiver is null");
            }
            if (handler == null) {
                throw new IllegalArgumentException("handler is null");
            }
            final IActorRef ref = receiver.self();
            this.handler = (vv) -> ActorSendMsgUtils.send(ref, new FastFailActorRunnable<>("TraverseCallback", (actor) -> handler.accept(vv)));
            this.isSync = false;
            return this;
        }

        /**
         * 设置每个遍历回调函数的信息。
         * 注意：同步。默认同步方式。
         *
         * @param handler 回调函数。
         * @return Builder。
         */
        public Builder<T> handlerForEachResponse(final Consumer<List<ValueWithVersion<T>>> handler) {
            if (handler == null) {
                throw new IllegalArgumentException("handler is null");
            }
            this.handler = handler;
            this.isSync = true;
            return this;
        }

        public TraverseOption<T> build() {
            final TraverseOption<T> option = new TraverseOption<>();
            option.totalLimit = this.totalLimit;
            option.fieldNames = this.fieldNames;
            option.isNeedAllFields = this.getAllFields;
            option.timeoutMs = this.timeoutMs;
            option.handler = this.handler;
            option.isSync = this.isSync;
            return option;
        }
    }
}

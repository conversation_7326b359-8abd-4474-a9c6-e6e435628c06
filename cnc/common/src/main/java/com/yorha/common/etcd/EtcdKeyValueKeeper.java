package com.yorha.common.etcd;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.common.exception.ErrorCode;
import io.etcd.jetcd.common.exception.EtcdException;
import io.etcd.jetcd.kv.PutResponse;
import io.etcd.jetcd.lease.LeaseGrantResponse;
import io.etcd.jetcd.lease.LeaseKeepAliveResponse;
import io.etcd.jetcd.options.PutOption;
import io.etcd.jetcd.support.CloseableClient;
import io.grpc.stub.StreamObserver;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 用于保证Etcd Key Value对在重启后依然生效。
 *
 * <AUTHOR>
 */
@ThreadSafe
class EtcdKeyValueKeeper {
    private static final Logger LOGGER = LogManager.getLogger(EtcdKeyValueKeeper.class);

    /**
     * Etcd SDK 当前线程的处理限制非常多，因此自己定义了Handler进行封装。
     * 注意：
     * 1. lease本地也有个过期逻辑，如果本地发现过期，将会调用onCompleted。（需要重新续约）
     * 2. lease出错逻辑，有可能因为租约不存在。（需要重新续约）
     * 3. Etcd 客户端会以1/3超时时间进行续约。
     */
    private static class KeyValueItem implements StreamObserver<LeaseKeepAliveResponse> {
        // 租约
        private final long leaseId;
        // 需要续租的键值对
        private final Map<String, String> map;
        // 客户端
        private CloseableClient closeableClient;
        // 超时时间，单位：秒
        private final long ttl;
        // item的父亲容器。
        private final EtcdKeyValueKeeper keeper;

        private KeyValueItem(final long leaseId, final Map<String, String> map, final long ttl, final EtcdKeyValueKeeper keeper) {
            this.leaseId = leaseId;
            this.map = map;
            this.ttl = ttl;
            this.keeper = keeper;
        }

        /**
         * 清理匹配成功的租约。
         *
         * @param key      键。
         * @param isPrefix 是否是前缀。
         */
        public void clearLeaseMap(final String key, final boolean isPrefix) {
            this.map.remove(key);
            if (!isPrefix) {
                return;
            }
            this.map.keySet().removeIf(s -> s.startsWith(key));
        }

        /**
         * 当前维持的KV对。
         *
         * @return 键值对容器。
         */
        public Map<String, String> getMap() {
            return Collections.unmodifiableMap(map);
        }

        /**
         * 过期时间。（单位：秒）
         *
         * @return 过期时间。
         */
        public long getTtl() {
            return ttl;
        }

        /**
         * 租约id。
         *
         * @return 租约id。
         */
        public long getLeaseId() {
            return leaseId;
        }

        /**
         * 关闭租约。
         */
        public void close() {
            try {
                LOGGER.info("KeyValueItem close item={} start", this);
                this.closeableClient.close();
                this.keeper.client.getLeaseClient()
                        .revoke(this.leaseId)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                LOGGER.info("KeyValueItem close item={} end", this);
            } catch (Exception e) {
                LOGGER.error("KeyValueItem close item={}, e=", this, e);
            }
        }

        /**
         * 设置键值对。
         *
         * @param key   键。
         * @param value 值。
         */
        public void put(final String key, final String value) {
            try {
                final PutOption putOption = PutOption.newBuilder()
                        .withLeaseId(leaseId)
                        .withPrevKV()
                        .build();
                final PutResponse putResponse = this.keeper.client.getKVClient()
                        .put(EtcdUtils.bytesOf(key), EtcdUtils.bytesOf(value), putOption)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                this.map.put(key, value);
                if (putResponse.hasPrevKv()) {
                    LOGGER.debug("KeyValueItem put key={}, value={}, prevValue={}, item={}, leaseHex={}",
                            key, value, EtcdUtils.toString(putResponse.getPrevKv().getValue()), this, Long.toHexString(this.leaseId));
                    return;
                }
                LOGGER.debug("KeyValueItem put key={}, value={}, item={}, leaseHex={}", key, value, this, Long.toHexString(this.leaseId));
            } catch (Exception e) {
                throw new GeminiException("KeyValueItem put key=" + key + ", value=" + value, e);
            }
        }

        /**
         * Etcd触发的回调函数。
         *
         * @param response 返回值。
         */
        @Override
        public void onNext(LeaseKeepAliveResponse response) {
            LOGGER.debug("KeyValueItem alive, item={}, hex={}, response={}", this, Long.toHexString(this.leaseId), response);
        }

        /**
         * Etcd出错触发的回调。如果是租约不见了，需要二次注册。
         *
         * @param t 异常。
         */
        @Override
        public void onError(Throwable t) {
            WechatLog.error("KeyValueItem onError item={}, hex={}, t=", this, Long.toHexString(this.leaseId), t);
            if (!(t instanceof EtcdException)) {
                return;
            }
            final EtcdException exception = (EtcdException) t;
            // 如果发现租约没了，则重新续期。
            if (exception.getErrorCode() != ErrorCode.NOT_FOUND) {
                return;
            }
            // jetcd的任务不能在触发线程，否则会导致死锁，并在超时后放开
            this.keeper.retryLease(this.leaseId);
        }

        @Override
        public void onCompleted() {
            // 本地过期，需要续约
            WechatLog.error("KeyValueItem onCompleted, lease timeout, item={}, hex={}", this, Long.toHexString(leaseId));
            // jetcd的任务不能在触发线程，否则会导致死锁，并在超时后放开
            this.keeper.retryLease(this.leaseId);
        }

        @Override
        public String toString() {
            return "KVItem{" +
                    "leaseId=" + leaseId +
                    ", map=" + map +
                    ", ttl=" + ttl +
                    '}';
        }

        private static class Builder {
            private Map<String, String> map;
            private long ttl;
            private final EtcdKeyValueKeeper keeper;

            private Builder(EtcdKeyValueKeeper keeper) {
                this.keeper = keeper;
            }

            /**
             * @param ttl 时间/秒
             * @return Builder。
             */
            public Builder lease(final long ttl) {
                this.ttl = ttl;
                return this;
            }

            /**
             * 设置KV。
             *
             * @param kv kv。
             * @return builder。
             */
            public Builder kv(final Map<String, String> kv) {
                this.map = new HashMap<>(kv);
                return this;
            }

            /**
             * 创建KeyValueItem。
             *
             * @return KeyValueItem。
             */
            public KeyValueItem build() {
                final LeaseGrantResponse leaseGrantResponse;
                try {
                    leaseGrantResponse = this.keeper.client.getLeaseClient()
                            .grant(ttl)
                            .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                } catch (Exception e) {
                    throw new GeminiException("grant lease fail", e);
                }
                // 参考github etcd源码，以下代码来自于server/etcdserver/v2_server.go，是lease id发号器实现，lease永远是一个正数。
                // forr r.ID == int64(lease.NoLease) { // NoLease = 0
                // r.ID = int64(s.reqIDGen.Next() & ((1 << 63) - 1))
                final long leaseId = leaseGrantResponse.getID();
                final KeyValueItem kvItem = new KeyValueItem(leaseId, this.map, this.ttl, this.keeper);
                kvItem.closeableClient = this.keeper.client.getLeaseClient().keepAlive(leaseId, kvItem);
                return kvItem;
            }
        }
    }

    private final ReentrantLock mutex = new ReentrantLock(true);
    private final Map<Long, KeyValueItem> leaseMap = new HashMap<>();
    private final Client client;
    private final Executor executor;
    private volatile boolean isClose = false;

    EtcdKeyValueKeeper(final Client client, Executor executor) {
        this.client = client;
        this.executor = executor;
    }

    /**
     * @param key   键。
     * @param value 值。
     * @param ttl   过期时间，单位秒。
     * @return leaseId，是一个永远正数的值。
     */
    public long keepKeyValueWithTTL(final String key, final String value, final long ttl) {
        if (ttl <= 0) {
            throw new GeminiException("EtcdKeyValueKeeper keepKeyValueWithTTL, key={}, value={}, leaseTimeSec not illegal!", key, value);
        }
        try {
            this.mutex.lock();
            LOGGER.debug("EtcdKeyValueKeeper keepKeyValueWithTTL, key={}, value={}, ttl={}s, begin", key, value, ttl);
            // 准备item
            final KeyValueItem item = (new KeyValueItem.Builder(this))
                    .lease(ttl)
                    .kv(new HashMap<>(1))
                    .build();
            // 设置item
            item.put(key, value);
            // 清理过期的kv
            this.clearLeaseMapExcept(key, false, null);
            // 设置kv
            this.leaseMap.put(item.leaseId, item);
            LOGGER.debug("EtcdKeyValueKeeper keepKeyValueWithTTL, newItem={}, leaseMap={}, end", item, this.leaseMap);
            return item.leaseId;
        } finally {
            this.mutex.unlock();
        }
    }

    /**
     * @param key     键。
     * @param value   值。
     * @param leaseId 租约id。
     */
    public void keepKeyValueWithLease(final String key, final String value, final long leaseId) {
        try {
            this.mutex.lock();
            LOGGER.info("EtcdKeyValueKeeper keepKeyValueWithLease, key={}, value={}, leaseId={}, hex={}, begin",
                    key, value, leaseId, Long.toHexString(leaseId));
            final KeyValueItem item = this.leaseMap.get(leaseId);
            if (item == null) {
                throw new GeminiException("leaseId={} useless", leaseId);
            }
            // 补充kv
            item.put(key, value);
            // 清理过期kv
            this.clearLeaseMapExcept(key, false, item);
            LOGGER.info("EtcdKeyValueKeeper keepKeyValueWithLease, key={}, value={}, hex={}, item={}, leaseMap={}, end",
                    key, value, Long.toHexString(leaseId), item, this.leaseMap);
        } finally {
            this.mutex.unlock();
        }
    }

    /**
     * 清除Key保持的value。
     *
     * @param key key。
     */
    public void revokeKeyValueKeepAlive(final String key) {
        try {
            this.mutex.lock();
            LOGGER.info("EtcdKeyValueKeeper cancelKeyValueKeeperWithKey, key={}, start", key);
            final Iterator<KeyValueItem> iterator = this.leaseMap.values().iterator();
            while (iterator.hasNext()) {
                final KeyValueItem kvItem = iterator.next();
                if (!kvItem.getMap().containsKey(key)) {
                    continue;
                }
                kvItem.clearLeaseMap(key, false);
                if (!kvItem.getMap().isEmpty()) {
                    continue;
                }
                iterator.remove();
                kvItem.close();
            }
            LOGGER.info("EtcdKeyValueKeeper cancelKeyValueKeeperWithKey, key={}, leaseMap={}, end", key, this.leaseMap);
        } finally {
            this.mutex.unlock();
        }
    }

    /**
     * 清理需要保持key value的map。
     *
     * @param key      key。
     * @param isPrefix 是否是前缀。
     */
    public void clearLeaseMap(final String key, boolean isPrefix) {
        this.clearLeaseMapExcept(key, isPrefix, null);
    }

    private void clearLeaseMapExcept(final String key, final boolean isPrefix, final KeyValueItem item) {
        try {
            this.mutex.lock();
            final Iterator<Map.Entry<Long, KeyValueItem>> iterator = this.leaseMap.entrySet().iterator();
            while (iterator.hasNext()) {
                final Map.Entry<Long, KeyValueItem> next = iterator.next();
                // 相同则跳过。
                if (next.getValue() == item) {
                    continue;
                }
                // 清理lease map
                next.getValue().clearLeaseMap(key, isPrefix);
                // lease map不为空，跳过
                if (!next.getValue().getMap().isEmpty()) {
                    continue;
                }
                // 关闭item
                next.getValue().close();
                // 删除item
                iterator.remove();
            }
        } finally {
            this.mutex.unlock();
        }
    }

    /**
     * 关闭整个Keeper。
     */
    public void close() {
        try {
            this.mutex.lock();
            LOGGER.info("EtcdKeyValueKeeper close start, leaseMap={}", this.leaseMap);
            for (final KeyValueItem kvItem : this.leaseMap.values()) {
                kvItem.close();
            }
            this.leaseMap.clear();
            this.isClose = true;
            LOGGER.info("EtcdKeyValueKeeper close end");
        } finally {
            this.mutex.unlock();
        }
    }

    /**
     * 给当前的KV对重新申请一个lease进行续约。
     *
     * @param leaseId 老的leaseId。
     */
    private void retryLease(final long leaseId) {
        this.executor.execute(() -> {
            try {
                this.mutex.lock();
                final KeyValueItem elderItem = this.leaseMap.get(leaseId);
                LOGGER.info("EtcdKeyValueKeeper retryLease, elderItem={}, isClose={}", elderItem, this.isClose);
                if (elderItem == null || this.isClose) {
                    return;
                }
                KeyValueItem curItem = elderItem;
                do {
                    // 关闭上次循环出错的
                    curItem.close();
                    // 清理老的leaseId
                    this.leaseMap.remove(curItem.getLeaseId());
                    try {
                        // 新建item
                        curItem = (new KeyValueItem.Builder(this))
                                .kv(new HashMap<>(elderItem.getMap().size()))
                                .lease(elderItem.getTtl())
                                .build();
                        // 刷新item的容器
                        this.leaseMap.put(curItem.getLeaseId(), curItem);
                        // 保证kv的租约
                        for (final Map.Entry<String, String> kv : elderItem.getMap().entrySet()) {
                            this.keepKeyValueWithLease(kv.getKey(), kv.getValue(), curItem.getLeaseId());
                        }
                    } catch (Exception e) {
                        LOGGER.error("EtcdKeyValueKeeper retryLease try new fail, so park, elderItem={}, e=", elderItem, e);
                        LockSupport.parkNanos(this, TimeUnit.MILLISECONDS.toNanos(10));
                        continue;
                    }
                    break;
                } while (!this.isClose);
                LOGGER.info("EtcdKeyValueKeeper retryLease, isClose={}, elderItem={}, newItem={}, newItemHex={}, map={}",
                        this.isClose, elderItem, curItem, Long.toHexString(curItem.getLeaseId()), this.leaseMap);
            } finally {
                this.mutex.unlock();
            }
        });
    }
}

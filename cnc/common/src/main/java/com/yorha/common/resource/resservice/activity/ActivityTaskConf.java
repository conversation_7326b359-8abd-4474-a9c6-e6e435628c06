package com.yorha.common.resource.resservice.activity;

import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;

import javax.annotation.Nullable;
import java.util.List;

public class ActivityTaskConf {

    private final int configId;
    private final int taskId;
    private final List<IntPairType> reward;

    @Nullable
    private final Pair<String, Integer> rewardScore;

    /**
     * 领奖时触发的邮件id，不含内容，纯通知，先这样吧
     */
    private final int rewardTriggerMailId;

    public ActivityTaskConf(int configId,
                            int taskId,
                            List<IntPairType> reward,
                            Pair<String, Integer> rewardScore,
                            int rewardTriggerMailId) {
        this.configId = configId;
        this.taskId = taskId;
        this.reward = reward;
        this.rewardScore = rewardScore;
        this.rewardTriggerMailId = rewardTriggerMailId;
    }

    public int getConfigId() {
        return configId;
    }

    public int getTaskId() {
        return taskId;
    }

    public List<IntPairType> getReward() {
        return reward;
    }

    @Nullable
    public Pair<String, Integer> getRewardScore() {
        return rewardScore;
    }

    public int getRewardTriggerMailId() {
        return rewardTriggerMailId;
    }
}

package com.yorha.common.perf;

import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CsPerfLogger extends AbstractPerfLogger {
    private static final Logger LOGGER = LogManager.getLogger(CsPerfLogger.class);
    private final Map<String, C2SRecord> c2sRecordMap = new HashMap<>();
    private final Map<String, S2CRecord> s2cRecordMap = new HashMap<>();
    private final Map<String, Long> errorCodeMap = new HashMap<>();
    private final MsgSizeCounter msgSizeCounter = new MsgSizeCounter(MonitorConstant.LARGE_MSG_SIZE, MonitorUnit.SC_NTF_MAX_SIZE);

    private static class C2SRecord {
        public long opNum = 0;
        public long totalSize = 0;
        public long maxSize = 0;
        public long lastQpsTsMs = 0;
        public int curSecQps = 0;
        public int maxSecQps = 0;

        public void trigger(long size) {
            totalSize += size;
            opNum++;
            maxSize = Math.max(maxSize, size);
            if (lastQpsTsMs == 0 && curSecQps == 0) {
                // 第一次触发
                this.curSecQps++;
                this.maxSecQps = this.curSecQps;
                this.lastQpsTsMs = SystemClock.nowNative();
                return;
            }
            if (SystemClock.nowNative() - this.lastQpsTsMs > TimeUnit.SECONDS.toMillis(1)) {
                this.lastQpsTsMs = SystemClock.nowNative();
                this.maxSecQps = Math.max(this.curSecQps, this.maxSecQps);
                this.curSecQps = 0;
            }
            this.curSecQps++;
        }
    }

    private static class S2CRecord {
        public long opNum = 0;
        public long totalSize = 0;
        public long maxSize = 0;
        public long totalCost = 0;
        public long maxCost = 0;

        public void trigger(long size, long cost) {
            totalSize += size;
            totalCost += cost;
            opNum++;
            maxSize = Math.max(maxSize, size);
            maxCost = Math.max(maxCost, cost);
        }
    }

    public CsPerfLogger() {
        super("cs-perf");
    }

    public void logC2C(String msgType, long size) {
        run(() -> {
            C2SRecord record = c2sRecordMap.computeIfAbsent(msgType, k -> new C2SRecord());
            record.trigger(size);
        });
    }

    public void logS2C(String msgType, long size, long costMs) {
        run(() -> {
            S2CRecord record = s2cRecordMap.computeIfAbsent(msgType, k -> new S2CRecord());
            record.trigger(size, costMs);
        });
    }

    /**
     * 所有ntf或者s2c都会统计
     */
    public void logNtf(String msgTypeName, long size) {
        run(() -> msgSizeCounter.log(msgTypeName, size));
    }


    public void logErrorCode(final String msgType, final int codeId) {
        final String codeName = ErrorCode.getCodeName(codeId);
        LOGGER.warn("cs_handle_msg fail msgType={} errorCode={}", msgType, codeName);
        run(() -> {
            String key = msgType + "#" + codeName;
            long old = errorCodeMap.computeIfAbsent(key, k -> 0L);
            errorCodeMap.put(key, old + 1);
        });
    }

    private void showC2S() {
        MonitorUnit.CS_MAX_QPS.clear();
        StringBuilder sb = new StringBuilder();
        sb.append("c2s_msg_perf");
        for (Map.Entry<String, C2SRecord> entry : c2sRecordMap.entrySet()) {
            C2SRecord value = entry.getValue();
            long num = value.opNum;
            if (num > 0) {
                long avgSize = value.totalSize / value.opNum;
                long maxSize = value.maxSize;
                long maxQps = value.maxSecQps;
                sb.append("\n|").append(entry.getKey())
                        .append("|").append("count:").append(num)
                        .append("|").append("avgSize:").append(avgSize)
                        .append("|").append("maxSize:").append(maxSize)
                        .append("|").append("maxQps:").append(maxQps)
                        .append("|");
                if (maxQps > 5) {
                    // 小于5的没必要上报了
                    MonitorUnit.CS_MAX_QPS.labels(ServerContext.getBusId(), entry.getKey()).set(maxQps);
                }
            }
        }
        c2sRecordMap.clear();
        getLoggerPerf().info(sb);
    }

    private void showS2C() {
        MonitorUnit.SC_AVG_COST.clear();
        StringBuilder sb = new StringBuilder();
        sb.append("s2c_msg_perf");
        for (Map.Entry<String, S2CRecord> entry : s2cRecordMap.entrySet()) {
            S2CRecord value = entry.getValue();
            long num = value.opNum;
            if (num > 0) {
                long avgSize = value.totalSize / num;
                long maxSize = value.maxSize;
                long avgCost = value.totalCost / num;
                long maxCost = value.maxCost;
                sb.append("\n|").append(entry.getKey())
                        .append("|").append("count:").append(num)
                        .append("|").append("avgSize:").append(avgSize)
                        .append("|").append("maxSize:").append(maxSize)
                        .append("|").append("avgCost:").append(avgCost)
                        .append("|").append("maxCost:").append(maxCost)
                        .append("|");
                if (avgCost > 5) {
                    MonitorUnit.SC_AVG_COST.labels(ServerContext.getBusId(), entry.getKey()).set(avgCost);
                }
            }
        }
        s2cRecordMap.clear();
        getLoggerPerf().info(sb);
    }

    private void showErrorCode() {
        StringBuilder sb = new StringBuilder();
        sb.append("errorcode_perf");
        for (Map.Entry<String, Long> entry : errorCodeMap.entrySet()) {
            sb.append("\n|").append(entry.getKey())
                    .append("|").append("count:").append(entry.getValue())
                    .append("|");
        }
        getLoggerPerf().info(sb);
        errorCodeMap.clear();
    }

    public void flush() {
        run(() -> {
            showC2S();
            showS2C();
            showErrorCode();
            msgSizeCounter.clear();
        });
    }
}

package com.yorha.common.db.tcaplus.op;

import com.google.common.collect.Lists;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.db.tcaplus.*;
import com.yorha.common.db.tcaplus.option.BatchGetOption;
import com.yorha.common.db.tcaplus.result.BatchGetResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class TcaplusBatchGet<T extends Message.Builder> extends TcaplusOperation<Collection<T>, BatchGetOption, BatchGetResult<T>> {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusBatchGet.class);

    private static final int MAX_RECORD_NUM_PER_GET = 50;
    private int responseCnt = 0;
    private BatchGetResult<T> result;
    private long startTsMs;
    private GeminiStopWatch stopWatch;

    public TcaplusBatchGet(Client client, Collection<T> ts, BatchGetOption batchGetOption) {
        super(client, PbFieldMetaCaches.getMetaData(ts.iterator().next()), ts, batchGetOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_BATCH_GET_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        throw new GeminiException("TcaplusBatchGet configRequestProperty illegal invoke.");
    }

    private void configRequestProperty0(Request request, Collection<T> reqList) {
        if (reqList.isEmpty()) {
            throw new TcaplusDBException("batchGet failed: invalid arguments");
        }
        // https://tcaplusdb.tencent.com/UserGuide/05TcaplusDB_SDK_and_API/01TDR%E8%A1%A8SDK_and_API/01C++_SDK/02%E5%93%8D%E5%BA%94%E5%88%86%E5%8C%85%E9%97%AE%E9%A2%98%E8%AF%B4%E6%98%8E.html
        // 如果不设置为true，超过256KB就会只返回第一个响应包，进而丢失部分数据。
        request.setMultiResponseFlag(1);
        if (getOption().isFetchOnlyIfExpired()) {
            if (getOption().getExpireSeconds() > 0) {
                request.setExpireTime(getOption().getExpireSeconds());
            }
            request.setFlags(TcaplusConstant.CommonFlag.TCAPLUS_FLAG_FETCH_ONLY_IF_MODIFIED.getValue());
        }
        // 通过addFieldName设置查询的字段
        if (getOption().isGetAllFields()) {
            for (Descriptors.FieldDescriptor e : getFieldMetaData().valueFieldsList) {
                request.addFieldName(e.getName());
                this.addRequestBytes(e.getName().length());
            }
        } else {
            if (getOption().getFieldNames() != null) {
                this.addRequestedFields(getOption().getFieldNames(), reqList.iterator().next().getDescriptorForType(), request);
            }
        }
        for (T r : reqList) {
            Record record = request.addRecord();
            setRequestKeys(r, record);
        }
    }

    @Override
    protected BatchGetResult<T> buildResult(Response response) {
        throw new NotImplementedException();
    }

    /**
     * 从Response的ErrorRecord中读取Key数据
     *
     * @param record 数据库中读取到的记录
     * @param value  待返回的结果
     */
    protected void readFromResponseErrorKey(Record record, Message.Builder value) {
        for (Descriptors.FieldDescriptor desc : getFieldMetaData().keyFieldsList) {
            if (record.getKeyMap().containsKey(desc.getName())) {
                this.addResponseBytes(PbDynamicHandler.getKeyField(record, desc, value));
            }
        }
    }

    protected void buildResult(final List<Response> responseList, final int batchNumIndex) {
        this.responseCnt += responseList.size();
        for (int i = 0; i < responseList.size(); i++) {
            final Response response = responseList.get(i);
            final List<Record> recordList = response.getRecordList() == null ? Collections.emptyList() : response.getRecordList();
            final List<Record> errorRecordList = response.getErrorRecords() == null ? Collections.emptyList() : response.getErrorRecords();
            LOGGER.info("TcaplusBatchGet buildResult, requestId={}, tableName={}, batchNumIndex={}, index={}, code={}, hasMore={}, recordNum={}, failNum={}",
                    this.getRequestId(), response.getTableName(), batchNumIndex, i,
                    response.getResult(), response.hasMore(), recordList.size(), errorRecordList.size());
            this.result.code = TcaplusErrorCode.forNumber(response.getResult());
            if (response.getResult() != TcaplusErrorCode.GEN_ERR_SUC.getValue()) {
                this.result.values = Collections.emptyList();
                this.result.errorKeys = Collections.emptyList();
                return;
            }
            final T next = this.getReq().iterator().next();
            for (final Record record : recordList) {
                ValueWithVersion<T> value = new ValueWithVersion<>();
                value.version = record.getVersion();
                value.value = buildDefaultValue(next);
                result.values.add(value);
                this.readFromResponseValues(record, value.value);
            }
            for (final Record errorRecord : errorRecordList) {
                final T value = buildDefaultValue(next);
                this.readFromResponseErrorKey(errorRecord, value);
                result.errorKeys.add(value);
            }
        }
    }

    @Override
    public void runAsync(Consumer<BatchGetResult<T>> cb) {
        if (this.getReq().isEmpty()) {
            this.result = new BatchGetResult<>();
            this.result.requestId = this.getRequestId();
            this.result.values = Collections.emptyList();
            this.result.errorKeys = Collections.emptyList();
            this.result.code = TcaplusErrorCode.GEN_ERR_SUC;
            LOGGER.info("TcaplusBatchGet async empty request! requestId={}, tableName={}", this.getRequestId(), this.getTableName());
            cb.accept(this.result);
            return;
        }

        final LinkedList<Collection<T>> splitReqList = TcaplusBatchGet.splitReq(this.getReq());

        this.stopWatch = new GeminiStopWatch("TcaplusBatchGet#" + this.getTableName());
        this.startTsMs = SystemClock.nowNative();
        this.result = new BatchGetResult<>();
        this.result.requestId = this.getRequestId();
        this.result.values = new LinkedList<>();
        this.result.errorKeys = new LinkedList<>();
        this.result.code = TcaplusErrorCode.GEN_ERR_SUC;

        this.responseCnt = 0;
        this.stopWatch.mark("runAsync");
        this.onDbRequest();

        this.async0(cb, stopWatch, splitReqList, 0);
    }

    private void async0(final Consumer<BatchGetResult<T>> cb, final GeminiStopWatch stopWatch, final LinkedList<Collection<T>> splitReqList, final int batchNumIndex) {
        final long curTimeTsMs = SystemClock.nowNative();
        final long endTimeTsMs = this.startTsMs + this.getTimeoutTimeMs();
        if (this.result.isOk() && endTimeTsMs <= curTimeTsMs && !splitReqList.isEmpty()) {
            this.result.code = TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT;
            this.result.errorKeys = Collections.emptyList();
            this.result.values = Collections.emptyList();
        }
        if (!this.result.isOk() || splitReqList.isEmpty()) {
            final long costMs = this.onDbResponse(this.result.getCode());
            if (!this.result.isOk()) {
                LOGGER.info("TcaplusBatchGet async fail, reqId={}, table={}, code={}, batchNumIndex={}, recordNum={}, wantRecordNum={}, responseCnt={}, costMs={}",
                        this.getRequestId(), this.getTableName(), this.result.getCode(), batchNumIndex, this.result.values.size(), this.getReq().size(), this.responseCnt, costMs);
                cb.accept(this.result);
                return;
            }
            if (costMs > MonitorConstant.WARNING_BATCH_GET_COST_TIME_MS) {
                LOGGER.warn("TcaplusBatchGet async slow, reqId={}, table={}, code={}, batchNum={}, recordNum={}, wantRecordNum={}, responseCnt={}, failNum={}, costMs={}, stat={}",
                        this.getRequestId(), this.getTableName(), this.result.getCode(), batchNumIndex,
                        this.result.values.size(), this.getReq().size(), this.responseCnt, this.result.errorKeys.size(), costMs, stopWatch.stat());

            } else {
                LOGGER.info("TcaplusBatchGet async successful, reqId={}, table={}, code={}, batchNum={}, recordNum={}, wantRecordNum={}, failNum={}, responseCnt={}, costMs={}",
                        this.getRequestId(), this.getTableName(), this.result.getCode(), batchNumIndex,
                        this.result.values.size(), this.getReq().size(), this.result.errorKeys.size(), this.responseCnt, costMs);
            }
            cb.accept(this.result);
            return;
        }
        final Collection<T> first = splitReqList.removeFirst();
        final Request request = this.client.acquireRequest();
        request.setCmd(getType());
        request.setTableName(getFieldMetaData().tableName);
        configRequestProperty0(request, first);
        TcaplusUtils.requestMultipleResponseAsync(this.client, request, (responseList -> {
            stopWatch.mark(StringUtils.format("batchIndex={} end db", batchNumIndex));
            this.buildResult(responseList, batchNumIndex);
            stopWatch.mark(StringUtils.format("batchIndex={} end all", batchNumIndex));
            this.async0(cb, stopWatch, splitReqList, batchNumIndex + 1);
        }));
    }

    private long getTimeoutTimeMs() {
        return this.getOption().getTimeoutTimeMs() != 0 ? Math.max(this.getOption().getTimeoutTimeMs(), 1) : 5000;
    }

    @Override
    public BatchGetResult<T> run() {
        if (this.getReq().isEmpty()) {
            this.result = new BatchGetResult<>();
            this.result.values = Collections.emptyList();
            this.result.errorKeys = Collections.emptyList();
            this.result.requestId = this.getRequestId();
            this.result.code = TcaplusErrorCode.GEN_ERR_SUC;
            LOGGER.info("TcaplusBatchGet sync empty request! requestId={}, tableName={}", this.getRequestId(), this.getTableName());
            return this.result;
        }

        final LinkedList<Collection<T>> splitReqList = TcaplusBatchGet.splitReq(this.getReq());

        this.stopWatch = new GeminiStopWatch("TcaplusBatchGet#" + this.getTableName());
        this.startTsMs = SystemClock.nowNative();

        this.result = new BatchGetResult<>();
        this.result.requestId = this.getRequestId();
        this.result.values = new LinkedList<>();
        this.result.errorKeys = new LinkedList<>();
        this.result.code = TcaplusErrorCode.GEN_ERR_SUC;

        this.stopWatch.mark("run");
        this.onDbRequest();

        int batchNumIndex = 0;
        Collection<T> singleReq;
        this.responseCnt = 0;

        while ((singleReq = splitReqList.pollFirst()) != null) {
            batchNumIndex += 1;

            final long deadlineMs = this.startTsMs + this.getTimeoutTimeMs();
            final long timeoutTimeMs = deadlineMs - SystemClock.nowNative();

            if (timeoutTimeMs <= 0) {
                this.result.values = Collections.emptyList();
                this.result.errorKeys = Collections.emptyList();
                this.result.code = TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT;
                break;
            }

            final Request request = this.client.acquireRequest();
            request.setCmd(getType());
            request.setTableName(getFieldMetaData().tableName);
            this.configRequestProperty0(request, singleReq);
            this.stopWatch.mark(StringUtils.format("batchIndex={} end db", batchNumIndex));
            final List<Response> responseList = TcaplusUtils.requestMultipleResponse(this.client, request, timeoutTimeMs);
            this.stopWatch.mark(StringUtils.format("batchIndex={} end all", batchNumIndex));
            this.buildResult(responseList, batchNumIndex);
            if (!this.result.isOk()) {
                break;
            }
        }

        final long costMs = this.onDbResponse(result.getCode());
        if (!result.isOk()) {
            LOGGER.info("TcaplusBatchGet sync fail, requestId={}, tableName={}, code={}, batchSize={}, wantRecordNum={}, responseCnt={}, costMs={}",
                    getRequestId(), getTableName(), result.getCode(), splitReqList.size(), getReq().size(), this.responseCnt, costMs);
            return result;
        }
        var failNum = result.errorKeys.size();
        if (costMs > MonitorConstant.WARNING_BATCH_GET_COST_TIME_MS) {
            LOGGER.warn("TcaplusBatchGet sync slow, requestId={}, tableName={}, code={}, batchSize={}, recordNum={}, wantRecordNum={}, failNum={}, responseCnt={}, stat={}",
                    getRequestId(), getTableName(), result.getCode(), splitReqList.size(), result.values.size(), getReq().size(), failNum, responseCnt, stopWatch.stat());
        } else {
            LOGGER.info("TcaplusBatchGet sync successful, requestId={}, tableName={}, code={}, batchSize={}, recordNum={}, wantRecordNum={}, failNum={}, responseCnt={}, costMs={}",
                    getRequestId(), getTableName(), result.getCode(), splitReqList.size(), result.values.size(), getReq().size(), failNum, responseCnt, costMs);
        }
        return result;
    }

    public static <T extends Message.Builder> LinkedList<Collection<T>> splitReq(final Collection<T> req) {
        LinkedList<Collection<T>> ret = Lists.newLinkedList();
        final int reqSize = req.size();
        if (reqSize == 0) {
            return ret;
        }
        if (reqSize <= MAX_RECORD_NUM_PER_GET) {
            ret.add(req);
        } else {
            ArrayList<T> reqList = Lists.newArrayList(req);
            int index = 0;
            while (index < reqSize) {
                ret.add(reqList.subList(index, Math.min(index + MAX_RECORD_NUM_PER_GET, reqSize)));
                index += MAX_RECORD_NUM_PER_GET;
            }
        }
        return ret;
    }
}

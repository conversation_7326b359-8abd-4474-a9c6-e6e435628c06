package com.yorha.common.utils.jol;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.openjdk.jol.info.GraphLayout;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

public class TreeLayout {

    private Node root;

    TreeLayout() {
    }

    public Node getRoot() {
        return root;
    }

    public static class Node {
        private final GraphPathRecord record;
        private final List<Node> children;
        private final Node father;
        private long size;
        private final int layer;
        private final String name;

        public Node(GraphPathRecord record, int layer, Node father) {
            this.record = record;
            this.children = Lists.newArrayList();
            this.size = record.size();
            this.layer = layer;
            this.name = record.obj().getClass().getSimpleName() + " " + record.path();
            this.father = father;
        }

        Node addChild(GraphPathRecord childRecord) {
            Node child = new Node(childRecord, layer + 1, this);
            children.add(child);
            addSize(childRecord.size());
            return child;
        }

        void addSize(long size) {
            this.size += size;
            if (father != null) {
                father.addSize(size);
            }
        }

        public long getSize() {
            return size;
        }

        public String getName() {
            return name;
        }

        public int getLayer() {
            return layer;
        }

        public GraphPathRecord getRecord() {
            return record;
        }

        public List<Node> getChildren() {
            return children;
        }
    }

    public static TreeLayout parse(GraphLayout layout) {
        List<GraphPathRecord> list = null;
        try {
            Field gprsField = GraphLayout.class.getDeclaredField("gprs");
            gprsField.setAccessible(true);  // 允许访问私有字段
            list = (List<GraphPathRecord>) gprsField.get(layout);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return parse(list);
    }

    public static TreeLayout parse(List<GraphPathRecord> gprs) {
        TreeLayout layout = new TreeLayout();
        final Map<GraphPathRecord, Node> gprMapping = Maps.newIdentityHashMap();
        for (GraphPathRecord gpr : gprs) {
            putNode(layout, gprMapping, gpr);
        }
        return layout;
    }

    private static Node putNode(TreeLayout layout, Map<GraphPathRecord, Node> gprMapping, GraphPathRecord gpr) {
        if (gpr.parent == null) {
            // 理应只有一个root
            if (layout.root != null) {
                throw new IllegalArgumentException("Multi root! " + gpr.path() + " exist:" + layout.root.record.path());
            }
            Node root = new Node(gpr, 1, null);
            gprMapping.put(gpr, root);
            layout.root = root;
            return root;
        }
        Node parentNode = gprMapping.get(gpr.parent);
        if (parentNode == null) {
            parentNode = putNode(layout, gprMapping, gpr.parent);
        }
        Node node = parentNode.addChild(gpr);
        gprMapping.put(gpr, node);
        return node;
    }

}

package com.yorha.common.db.tcaplus.option;

import com.yorha.proto.CommonEnum;

/**
 * insert无法指定版本号，版本号为1
 *
 * <AUTHOR>
 */
public class InsertOption {

    public static final InsertOption DEFAULT_VALUE = InsertOption.newBuilder().build();

    private CommonEnum.TcaplusResultFlag resultFlag;
    /**
     * 是否需要重试。如果重试，DBActor会不停得重试直到超时。
     */
    private boolean isRetry;

    private InsertOption() {

    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public int getResultFlag() {
        return resultFlag.getNumber();
    }

    public boolean isRetry() {
        return isRetry;
    }

    public static final class Builder {
        private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
        private boolean isRetry = false;

        private Builder() {
        }

        /**
         * 是否在响应包里携带数据（主要用于处理insert冲突）
         */
        public Builder withResponseHasRecord(boolean flag) {
            if (flag) {
                this.resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_REQUEST;
            } else {
                this.resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
            }
            return this;
        }

        public Builder withRetry() {
            this.isRetry = true;
            return this;
        }

        public InsertOption build() {
            final InsertOption insertOption = new InsertOption();
            insertOption.resultFlag = this.resultFlag;
            insertOption.isRetry = this.isRetry;
            return insertOption;
        }
    }


}

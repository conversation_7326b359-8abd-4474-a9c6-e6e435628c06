package com.yorha.common.asset;

import com.yorha.common.resource.ResHolder;
import res.template.ItemTemplate;

public class ItemDesc extends AssetDesc {

    private final int itemId;
    private final long num;

    public ItemDesc(int itemId, long num) {
        super(AssetType.ITEM);
        this.itemId = itemId;
        this.num = num;
    }

    @Override
    public int getId() {
        return itemId;
    }

    @Override
    public long getAmount() {
        return num;
    }

    @Override
    public ItemDesc plusImpl(AssetDesc another) {
        return new ItemDesc(this.itemId, Math.addExact(this.num, another.getAmount()));
    }

    @Override
    public ItemDesc copyWithAmount(long amount) {
        return new ItemDesc(this.itemId, amount);
    }

    public ItemTemplate getTemplate() {
        return ResHolder.getTemplate(ItemTemplate.class, this.itemId);
    }

    @Override
    public String toString() {
        return "ItemDesc{" +
                "itemId=" + itemId +
                ", num=" + num +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ItemDesc)) {
            return false;
        }

        ItemDesc itemDesc = (ItemDesc) o;
        if (itemId != itemDesc.itemId) {
            return false;
        }
        return num == itemDesc.num;
    }

    @Override
    public int hashCode() {
        int result = itemId;
        result = 31 * result + (int) (num ^ (num >>> 32));
        return result;
    }
}

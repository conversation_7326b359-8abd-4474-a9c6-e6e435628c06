package com.yorha.common.db.tcaplus;

import com.google.protobuf.Descriptors;
import com.tencent.tcaplus.util.TdrType;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.yorha.common.db.tcaplus.TcaplusErrorCode.*;

public class DbUtil {
    private static final Map<Descriptors.FieldDescriptor.Type, Integer> PROTO_TYPE_TO_TDR_TYPE_MAP = new HashMap<>();
    private static final Map<Integer, String> TDR_TYPE_TO_STRING_MAP = new HashMap<>();
    private static final String UNKNOWN_TDR_TYPE = "unknown";
    /**
     * db重试上限时间。
     */
    public static final long DB_RETRY_TIMEOUT_MS = TimeUnit.MINUTES.toMillis(20);

    /**
     * call db最长超时时间(option设置withRetry时)
     */
    public static final long CALL_TIMEOUT_TIME_MS = DB_RETRY_TIMEOUT_MS + TimeUnit.SECONDS.toMillis(5);

    /**
     * DB操作 Id发号器。
     */
    private static final AtomicLong DB_REQUEST_ID = new AtomicLong(0L);

    public static long nextRequestId() {
        return DB_REQUEST_ID.incrementAndGet();
    }

    static {
        {   //protoTypeToTdrTypeMap.put(Descriptors.FieldDescriptor.Type., TdrType.TCAPLUS_RECORD_TYPE_INT8);
            //protoTypeToTdrTypeMap.put(Descriptors.FieldDescriptor.Type., TdrType.TCAPLUS_RECORD_TYPE_UINT8);
            //protoTypeToTdrTypeMap.put(Descriptors.FieldDescriptor.Type., TdrType.TCAPLUS_RECORD_TYPE_INT16);
            //protoTypeToTdrTypeMap.put(Descriptors.FieldDescriptor.Type., TdrType.TCAPLUS_RECORD_TYPE_UINT16);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.INT32, TdrType.TCAPLUS_RECORD_TYPE_INT32);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.FIXED32, TdrType.TCAPLUS_RECORD_TYPE_INT32);
            //protoTypeToTdrTypeMap.put(Descriptors.FieldDescriptor.Type., TdrType.TCAPLUS_RECORD_TYPE_UINT32);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.INT64, TdrType.TCAPLUS_RECORD_TYPE_INT64);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.FIXED64, TdrType.TCAPLUS_RECORD_TYPE_INT32);
            //protoTypeToTdrTypeMap.put(Descriptors.FieldDescriptor.Type., TdrType.TCAPLUS_RECORD_TYPE_UINT64);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.FLOAT, TdrType.TCAPLUS_RECORD_TYPE_FLOAT);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.DOUBLE, TdrType.TCAPLUS_RECORD_TYPE_DOUBLE);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.STRING, TdrType.TCAPLUS_RECORD_TYPE_STRING);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.BYTES, TdrType.TCAPLUS_RECORD_TYPE_BINARY);
            PROTO_TYPE_TO_TDR_TYPE_MAP.put(Descriptors.FieldDescriptor.Type.MESSAGE, TdrType.TCAPLUS_RECORD_TYPE_BINARY);
        }


        {
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_INT8, "int8");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_INT16, "int16");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_INT32, "int32");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_UINT32, "uint32");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_INT64, "int64");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_UINT64, "uint64");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_FLOAT, "float");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_DOUBLE, "double");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_STRING, "string");
            TDR_TYPE_TO_STRING_MAP.put(TdrType.TCAPLUS_RECORD_TYPE_BINARY, "bytes");

        }
    }

    /**
     * 获取proto类型映射的Tdr类型
     *
     * @param protoType proto类型
     * @return Integer：Tdr类型（<0为转换失败）
     */
    public static Integer protoTypeToTdrType(Descriptors.FieldDescriptor.Type protoType) {
        return PROTO_TYPE_TO_TDR_TYPE_MAP.getOrDefault(protoType, -1);
    }

    public static String tdrTypeToString(int tdrType) {
        return TDR_TYPE_TO_STRING_MAP.getOrDefault(tdrType, UNKNOWN_TDR_TYPE);
    }

    public static boolean isOk(int code) {
        return code == TcaplusErrorCode.GEN_ERR_SUC.getValue();
    }

    public static boolean isRecordAlreadyExist(int code) {
        return code == TcaplusErrorCode.SVR_ERR_FAIL_RECORD_EXIST.getValue();
    }

    public static boolean isRecordNotExist(int code) {
        return code == TXHDB_ERR_RECORD_NOT_EXIST.getValue();
    }

    public static boolean isInvalidVersion(int code) {
        return code == SVR_ERR_FAIL_INVALID_VERSION.getValue();
    }

    /**
     * Tcaplus响应超时,
     *
     * @param code 错误码。
     * @return 是否是Tcaplus DB响应超时。
     */
    public static boolean isTcaplusTimeout(int code) {
        return code == API_ERR_WAIT_RSP_TIMEOUT.getValue();
    }

    public static String idFactoryTableKeyZoneChat = "ZoneChat";

    public static String idFactoryTableKeyClanChat = "ClanChat";
}

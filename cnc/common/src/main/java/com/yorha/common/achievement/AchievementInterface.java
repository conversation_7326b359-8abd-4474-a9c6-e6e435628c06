package com.yorha.common.achievement;

import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;

/**
 * 成就系统接口
 *
 * <AUTHOR>
 */
public interface AchievementInterface {

    /**
     * 统计项产生变更
     *
     * @param checkerType 校验类型（例如：某个建筑升级达到多少级）
     * @param id          一维统计项id（一维统计项id的意义是StatisticEnum，二维统计项下的一维统计项，他的id意义是样例中的某个建筑）
     * @param oldValue    变更前的值
     * @param newValue    变更后的值
     */
    void onTrigger(CommonEnum.AchievementStatisticCheckerType checkerType, int id, long oldValue, long newValue);

    /**
     * 判断成就是否已完成
     *
     * @param achievementId 成就id
     * @return 成就是否已完成
     */
    boolean isAchievementComplete(int achievementId);

    /**
     * 校验本成就的所有需求达成
     *
     * @param achievementId 成就id
     * @return 成就是否已完成
     */
    boolean checkAchievementAllDemandComplete(int achievementId);

    /**
     * 完成成就
     *
     * @param achievementId 成就id
     */
    void onAchievementComplete(int achievementId);

    /**
     * 领取成就奖励
     *
     * @param achievementId 成就id
     * @return 奖励内容
     */
    Struct.YoAssetPackage takeReward(int achievementId);
}

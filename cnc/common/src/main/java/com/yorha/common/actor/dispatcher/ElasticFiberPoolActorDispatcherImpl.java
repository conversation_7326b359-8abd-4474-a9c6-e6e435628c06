package com.yorha.common.actor.dispatcher;

import com.yorha.common.actor.mailbox.IRunnableActorMailbox;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiFiberPoolExecutor;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 多功能的协程池分配器。
 * 1. 协程需求量较小( <= poolFiberCnt)，采用池化协程。
 * 2. 协程需求量较大( poolFiberCnt < , <= poolFiberCnt + elasticFiberCnt) ，会采用池化 + 弹性伸缩协程。
 * 3. 协程需求量过大( > poolFiberCnt + elasticFiberCnt)，阻塞在池化协程队列中。
 * 4. 协程需求量巨大( > 3 * poolFiberCnt + elasticFiberCnt)，丢弃调度任务。
 * 5. 协程最大数量，poolFiberCnt + elasticFiberCnt。
 *
 * <AUTHOR>
 */
public class ElasticFiberPoolActorDispatcherImpl implements IMailboxDispatcher {
    private static final Logger LOGGER = LogManager.getLogger(ElasticFiberPoolActorDispatcherImpl.class);
    private final int throughPut;
    private final GeminiThreadPoolExecutor schedulerExecutor;
    private final int keepAliveSec;
    private final Map<String, AtomicInteger> fiberCntMap;
    private final GeminiFiberPoolExecutor fiberPoolExecutor;
    private final int maxElasticFiberCnt;
    private final int maxPoolFiberCnt;
    private final AtomicInteger poolFiberCnt;
    private final AtomicInteger elasticFiberCnt;

    public ElasticFiberPoolActorDispatcherImpl(
            final String name,
            final int throughPut,
            final int parallelism,
            final int keepAliveSec,
            final int poolFiberCnt,
            final int elasticFiberCnt
    ) {
        this.poolFiberCnt = new AtomicInteger(0);
        this.elasticFiberCnt = new AtomicInteger(0);
        this.maxElasticFiberCnt = elasticFiberCnt;
        this.maxPoolFiberCnt = poolFiberCnt;
        this.throughPut = throughPut;
        this.schedulerExecutor = ConcurrentHelper.newFixedThreadExecutor(name, parallelism, 0, false);
        this.keepAliveSec = keepAliveSec;
        this.fiberCntMap = new ConcurrentHashMap<>();
        this.fiberPoolExecutor = ConcurrentHelper.newFixedFiberExecutor(name, this.schedulerExecutor, poolFiberCnt, poolFiberCnt * 2);
        this.fiberPoolExecutor.setKeepAliveTime(1000, TimeUnit.SECONDS);
        this.fiberPoolExecutor.allowCoreThreadTimeOut(true);
        this.fiberPoolExecutor.setRejectedExecutionHandler((r, executor) -> WechatLog.error("dispatcher {} overload! refuse {}!", this, r));
    }


    @Override
    public String getName() {
        return this.fiberPoolExecutor.getName();
    }

    @Override
    public int getThroughput() {
        return this.throughPut;
    }

    @Override
    public void schedule(IRunnableActorMailbox mb) {
        // 当前等待调度小于池化数量
        if (this.poolFiberCnt.incrementAndGet() <= this.maxPoolFiberCnt) {
            this.scheduleAtPool(mb);
            return;
        }
        // 需要调度的协程大于池化但是小于弹性伸缩的池子
        if (this.elasticFiberCnt.incrementAndGet() <= this.maxElasticFiberCnt) {
            // 池子数量减1
            this.poolFiberCnt.decrementAndGet();
            this.scheduleAtOneDrive(mb);
            return;
        }
        // 弹性伸缩
        this.elasticFiberCnt.decrementAndGet();
        this.scheduleAtPool(mb);
    }

    private void scheduleAtPool(final IRunnableActorMailbox mb) {
        this.fiberPoolExecutor.execute(() -> {
            final AtomicInteger mbCnt = this.fiberCntMap.computeIfAbsent(mb.ref().getActorRole(), (k) -> new AtomicInteger(0));
            try {
                mbCnt.incrementAndGet();
                mb.run();
            } finally {
                mbCnt.decrementAndGet();
                this.poolFiberCnt.decrementAndGet();
            }
        });
    }

    private void scheduleAtOneDrive(final IRunnableActorMailbox mb) {
        this.fiberPoolExecutor.getThreadFactory().newThread(() -> {
            final AtomicInteger mbCnt = this.fiberCntMap.computeIfAbsent(mb.ref().getActorRole(), (k) -> new AtomicInteger(0));
            try {
                mbCnt.incrementAndGet();
                mb.run();
            } finally {
                mbCnt.decrementAndGet();
                this.elasticFiberCnt.decrementAndGet();
            }
        });
    }

    @Override
    public void setParallelism(int parallelism) {
        this.schedulerExecutor.setCorePoolSize(parallelism);
    }

    @Override
    public int getParallelism() {
        return this.schedulerExecutor.getCorePoolSize();
    }

    @Override
    public int getFiberCnt(String actorRole) {
        return this.fiberPoolExecutor.getPoolSize() + this.elasticFiberCnt.get();
    }

    @Override
    public int getKeepAliveSec() {
        return this.keepAliveSec;
    }

    @Override
    public void shutdown() {
        if (this.schedulerExecutor.isShutdown()) {
            throw new GeminiException("{} Already Shutdown!", this);
        }
        this.schedulerExecutor.shutdown();
        try {
            final boolean isFinished = this.schedulerExecutor.awaitTermination(10, TimeUnit.SECONDS);
            if (!isFinished) {
                LOGGER.warn("{} Shutdown! NotFinished After 10s!", this);
            }
        } catch (InterruptedException e) {
            LOGGER.warn("{} Shutdown! But Interrupted!", this);
        }
    }
}

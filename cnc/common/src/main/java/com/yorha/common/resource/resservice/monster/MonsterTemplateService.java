package com.yorha.common.resource.resservice.monster;

import com.google.common.collect.Maps;
import com.yorha.common.constant.Constants;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.vector.Vector2i;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.BigSceneSpawnBuildingTemplate;
import res.template.MonsterTemplate;
import res.template.TroopTemplate;

import java.util.Map;

/**
 * 野怪配置表Service
 *
 * <AUTHOR>
 * 2021年10月26日 14:55:00
 */
public class MonsterTemplateService extends AbstractResService {

    private final Map<Integer, Vector2i> yawMap = Maps.newHashMap();

    public MonsterTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getListFromMap(MonsterTemplate.class).forEach(monsterTemplate -> {
            if (monsterTemplate.getYaw() > 0) {
                int angle = monsterTemplate.getYaw() % Constants.ANGLE_MAX_VALUE;
                double radians = Math.toRadians(angle);
                yawMap.put(monsterTemplate.getId(), new Vector2i(
                        (int) (Math.cos(radians) * Constants.ACCURACY_CONVERSION_SAFETY_VALUE),
                        (int) (Math.sin(radians) * Constants.ACCURACY_CONVERSION_SAFETY_VALUE))
                );
            }
        });
    }

    @Override
    public void checkValid() throws ResourceException {
        for (BigSceneSpawnBuildingTemplate template : getResHolder().getListFromMap(BigSceneSpawnBuildingTemplate.class)) {
            if (template.getId() != 0) {
                for (IntPairType refreshConfig : template.getRefreshConfigPairList()) {
                    if (getResHolder().findValueFromMap(MonsterTemplate.class, refreshConfig.getKey()) == null) {
                        throw new ResourceException(StringUtils.format("大世界野怪投放表中refreshConfig的野怪id={} 在野怪表中不存在", refreshConfig.getKey()));
                    }
                }
            }
        }
        for (MonsterTemplate template : getResHolder().getListFromMap(MonsterTemplate.class)) {
            if (template.getBeAttackModel() != CommonEnum.BeAttackModel.BAM_ONE_OR_RALLY) {
                continue;
            }
            if (template.getRallyEnergy() <= 0) {
                throw new ResourceException(" 野怪表 id={}，野怪类型为 单人进攻或集结 ，集结体力消耗配置错误，必须大于0", template.getId());
            }
        }
        // 检查部队配置表是否存在
        for (MonsterTemplate template : getResHolder().getListFromMap(MonsterTemplate.class)) {
            if (template.getTroopIndex() == 0) {
                throw new ResourceException(" 野怪表 id={} 部队表索引为0", template.getId());
            }
            if (getResHolder().findValueFromMap(TroopTemplate.class, template.getTroopIndex()) == null) {
                throw new ResourceException(" 野怪表 id={} troopIndex={} 在部队表中不存在", template.getId(), template.getTroopIndex());
            }
        }
    }

    public Vector2i getBronYaw(int monsterId) {
        if (yawMap.containsKey(monsterId)) {
            return yawMap.get(monsterId);
        }
        return new Vector2i(
                RandomUtils.nextInt(-Constants.ACCURACY_CONVERSION_SAFETY_VALUE, Constants.ACCURACY_CONVERSION_SAFETY_VALUE),
                RandomUtils.nextInt(-Constants.ACCURACY_CONVERSION_SAFETY_VALUE, Constants.ACCURACY_CONVERSION_SAFETY_VALUE)
        );
    }
}

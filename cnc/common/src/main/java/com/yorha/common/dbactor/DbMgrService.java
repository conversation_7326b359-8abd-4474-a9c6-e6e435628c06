package com.yorha.common.dbactor;

import com.google.protobuf.Message;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.concurrent.NamedRunnable;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.db.DbManager;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.TcaplusUtils;
import com.yorha.common.db.tcaplus.msg.*;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.perf.DbPerfLogger;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedList;
import java.util.Random;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;

public class DbMgrService implements IActorRef {
    private static class LazyHolder {
        private static final DbMgrService INSTANCE = new DbMgrService();
    }


    public static DbMgrService getInstance() {
        return DbMgrService.LazyHolder.INSTANCE;
    }

    private static final Logger LOGGER = LogManager.getLogger(DbMgrService.class);
    protected static final int RETRY_BUCKET = 100;
    protected static final int RETRY_BUCKET_DELAY_MS = 100;
    protected static final int RETRY_BUCKET_DELAY_INDEX_OFFSET = 5_000 / RETRY_BUCKET_DELAY_MS;
    /**
     * 最大数据监控timer。
     */
    protected ScheduledFuture<?> maxSizeTimer;
    /**
     * 数据监控timer。
     */
    protected ScheduledFuture<?> taskTimer;
    /**
     * DB重试timer。
     */
    protected ScheduledFuture<?> retryTimer;
    /**
     * db打印数据日志。
     */
    protected DbPerfLogger dbPerfLogger = new DbPerfLogger();
    /**
     * 请求Queue的桶id。
     */
    protected LongAdder retryQueueIndex;
    /**
     * 阻塞队列的Queue。
     */
    protected LinkedBlockingQueue<Runnable>[] retryQueueArray;
    /**
     * 重试的DbExecutor。
     */
    protected ThreadPoolExecutor retryDbExecutor;

    private DbMgrService() {
    }

    @Override
    public String getActorRole() {
        return "DbMgr";
    }

    @Override
    public final String getActorId() {
        return "Local";
    }

    @Override
    public final String getBusId() {
        return ServerContext.getServerInfo().getBusId();
    }

    @Override
    public final int getZoneId() {
        return 0;
    }


    public DbPerfLogger getDbPerfLogger() {
        return dbPerfLogger;
    }


    /**
     * 达到重试超时时间
     *
     * @param startTsMs 开始重试时间
     * @return true==已超时
     */
    protected boolean isRetryTimeout(final long startTsMs) {
        final long timeMs = SystemClock.nowNative() - startTsMs;
        return timeMs > DbUtil.DB_RETRY_TIMEOUT_MS;
    }


    private void handleDbReq(ActorMsgEnvelope envelope, GameDbReq<?> ask) {
        if (ask instanceof InsertAsk) {
            handleInsertAsk(envelope, (InsertAsk<?>) ask);
        } else if (ask instanceof DeleteAsk) {
            handleDeleteAsk(envelope, (DeleteAsk<?>) ask);
        } else if (ask instanceof DeleteByPartKeyAsk) {
            handleDeleteByPartKeyAsk(envelope, (DeleteByPartKeyAsk<?>) ask);
        } else if (ask instanceof UpdateAsk) {
            handleUpdateAsk(envelope, (UpdateAsk<?>) ask);
        } else if (ask instanceof SelectUniqueAsk) {
            handleSelectUniqueAsk(envelope, (SelectUniqueAsk<?>) ask);
        } else if (ask instanceof SelectAsk) {
            handleSelectAsk(envelope, (SelectAsk<?>) ask);
        } else if (ask instanceof BatchSelectAsk) {
            handleBatchSelectAsk(envelope, (BatchSelectAsk<?>) ask);
        } else if (ask instanceof IncreaseAsk) {
            handleIncreaseAsk(envelope, (IncreaseAsk<?>) ask);
        } else if (ask instanceof UpsertAsk) {
            handleUpsertAsk(envelope, (UpsertAsk<?>) ask);
        } else if (ask instanceof TraverseAsk) {
            handleTraverseAsk(envelope, (TraverseAsk<?>) ask);
        } else {
            throw new GeminiException("not implemented.");
        }
    }

    /**
     * 处理插入或更新请求。
     *
     * @param envelope 信封。
     */
    private void handleUpsertAsk(final ActorMsgEnvelope envelope, UpsertAsk<?> ask) {
        new UpsertTask(ask, envelope).run();
    }

    /**
     * 处理自增请求。
     *
     * @param envelope 信封。
     */
    private void handleIncreaseAsk(final ActorMsgEnvelope envelope, IncreaseAsk<?> ask) {
        new IncreaseTask(ask, envelope).run();
    }

    /**
     * 处理更新请求。
     *
     * @param envelope 信封。
     */
    private void handleUpdateAsk(final ActorMsgEnvelope envelope, UpdateAsk<?> ask) {
        new UpdateTask(ask, envelope).run();
    }

    /**
     * 处理多条数据删除请求。
     *
     * @param envelope 信封。
     */
    private void handleDeleteByPartKeyAsk(final ActorMsgEnvelope envelope, DeleteByPartKeyAsk<?> ask) {
        new DeleteByPartKeyTask(ask, envelope).run();
    }

    /**
     * 处理删除请求。
     *
     * @param envelope 信封。
     */
    private void handleDeleteAsk(final ActorMsgEnvelope envelope, DeleteAsk<?> ask) {
        new DeleteTask(ask, envelope).run();
    }

    /**
     * 处理插入请求。
     *
     * @param envelope 信封。
     */
    private void handleInsertAsk(final ActorMsgEnvelope envelope, InsertAsk<?> ask) {
        new InsertTask(ask, envelope).run();
    }

    /**
     * 处理查找请求。
     *
     * @param envelope 信封。
     */
    private <T extends Message.Builder> void handleSelectUniqueAsk(final ActorMsgEnvelope envelope, SelectUniqueAsk<T> ask) {
        Consumer<GetResult<T>> cb = (result) -> this.answer0(envelope, result);
        DbManager.getInstance().getAsync(ask.getReq(), ask.getOption(), cb);
    }

    /**
     * 处理多条数据查询请求。
     *
     * @param envelope 信封。
     */
    private <T extends Message.Builder> void handleSelectAsk(final ActorMsgEnvelope envelope, SelectAsk<T> ask) {
        Consumer<GetByPartKeyResult<T>> cb = (result) -> this.answer0(envelope, result);
        DbManager.getInstance().getByPartKeyAsync(ask.getReq(), ask.getOption(), cb);
    }

    /**
     * 处理批量查询请求。
     *
     * @param envelope 信封。
     */
    private <T extends Message.Builder> void handleBatchSelectAsk(ActorMsgEnvelope envelope, BatchSelectAsk<T> ask) {
        Consumer<BatchGetResult<T>> cb = result -> this.answer0(envelope, result);
        DbManager.getInstance().batchGetAsync(ask.getReqList(), ask.getOption(), cb);
    }

    private <T extends Message.Builder> void handleTraverseAsk(ActorMsgEnvelope envelope, TraverseAsk<T> ask) {
        Consumer<TraverseResult> cb = result -> this.answer0(envelope, result);
        DbManager.getInstance().traverseAsync(ask.getReq(), ask.getOption(), cb);
    }


    public final void dispatchProtoMsg(ActorMsgEnvelope envelope) {
        final IActorMsg payload = envelope.getPayload();
        if (payload instanceof GameDbReq) {
            this.handleDbReq(envelope, (GameDbReq<?>) payload);
            return;
        }
        throw new GeminiException("handle {} fail!", envelope);
    }

    public final synchronized void start() {
        DbManager.getInstance().initFromServerContext();
        this.init();
    }

    private void init() {
        // 初始化重试队列
        this.retryQueueIndex = new LongAdder();
        this.retryQueueArray = new LinkedBlockingQueue[RETRY_BUCKET];
        for (int i = 0; i < RETRY_BUCKET; i++) {
            this.retryQueueArray[i] = new LinkedBlockingQueue<>();
        }
        // 执行器
        this.retryDbExecutor = ConcurrentHelper.newSingleThreadExecutor("TcaplusServiceMgr#retrydb", RETRY_BUCKET * 2, false);
        // 周期性上报数据
        this.taskTimer = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(
                new NamedRunnable("show_db_traffic", () -> this.dbPerfLogger.flush()),
                1,
                1,
                TimeUnit.MINUTES
        );
        // DB重试timer
        this.retryTimer = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(
                new NamedRunnable("retry_db", this::retryDbOperation),
                RETRY_BUCKET_DELAY_MS,
                RETRY_BUCKET_DELAY_MS,
                TimeUnit.MILLISECONDS
        );
        // 每小时重置最大dbsize
        this.maxSizeTimer = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(
                new NamedRunnable("reset_max_db_size", () -> this.dbPerfLogger.resetMaxDbSize()),
                1,
                1,
                TimeUnit.HOURS
        );
    }

    public final synchronized void stop() {
        // 等待任务处理完
        DbManager.getInstance().destroy();
        this.retryDbExecutor.shutdown();
        try {
            this.retryDbExecutor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.error("disconnect retryDbExecutor", e);
        }
        // 取消任务定时器
        if (this.taskTimer != null) {
            this.taskTimer.cancel(false);
            this.taskTimer = null;
        }
        // 取消重试定时器
        if (this.retryTimer != null) {
            this.retryTimer.cancel(false);
            this.retryTimer = null;
        }
        // 重试db定时器
        if (this.dbPerfLogger != null) {
            this.dbPerfLogger.stop();
            this.dbPerfLogger = null;
        }
        // 取消max size定时器
        if (this.maxSizeTimer != null) {
            this.maxSizeTimer.cancel(false);
            this.maxSizeTimer = null;
        }
        this.retryQueueIndex = null;

    }

    protected void addToRetryQueueArray(Runnable runnable) {
        final Random random = new Random();
        final int randomOffset = random.nextInt(10);
        final int index = (int) ((this.retryQueueIndex.longValue() + RETRY_BUCKET_DELAY_INDEX_OFFSET + randomOffset) % RETRY_BUCKET);
        this.retryQueueArray[index].add(runnable);
    }

    private void retryDbOperation() {
        final int index = (int) (this.retryQueueIndex.longValue() % RETRY_BUCKET);
        this.retryQueueIndex.increment();
        if (this.retryQueueArray[index].isEmpty()) {
            return;
        }
        this.retryDbExecutor.execute(() -> {
            int total = 0;
            for (LinkedBlockingQueue<Runnable> linkedBlockingQueue : this.retryQueueArray) {
                total += linkedBlockingQueue.size();
            }
            LinkedList<Runnable> runnableLinkedList = new LinkedList<>();
            this.retryQueueArray[index].drainTo(runnableLinkedList);
            LOGGER.info("DbMgrService retryDbOperation total={}, try tick at {}, len={}", total, index, runnableLinkedList.size());
            Runnable r;
            while ((r = runnableLinkedList.poll()) != null) {
                r.run();
            }
        });
    }

    private void answer0(ActorMsgEnvelope context, GameDbResp resp) {
        if (!context.isByCall() && !context.isByAsk()) {
            return;
        }
        IActorRef sender = context.getSender();
        ActorMsgEnvelope answerEnvelope = ActorMsgEnvelope.createFromAnswer(context, resp);
        answerEnvelope.setReceiver(sender);
        ActorMsgSystem.dispatchMsg(answerEnvelope);
    }


    private abstract static class DbRunnable implements Runnable {
        /**
         * 用于answer
         */
        protected final ActorMsgEnvelope envelope;
        /**
         * 运行次数
         */
        private int runCnt;

        protected DbRunnable(ActorMsgEnvelope envelope) {
            this.envelope = envelope;
            this.runCnt = 0;
        }

        @Override
        public final void run() {
            this.runCnt++;
            this.doTask();
        }

        abstract void doTask();

        protected boolean hasRetried() {
            return this.runCnt > 1;
        }

        protected int getRunCnt() {
            return this.runCnt;
        }

        protected <T extends Message.Builder> String getTableName(T req) {
            return TcaplusUtils.getTableName(req);
        }


    }

    protected class DeleteByPartKeyTask extends DbRunnable {
        protected final DeleteByPartKeyAsk<?> ask;


        public DeleteByPartKeyTask(DeleteByPartKeyAsk<?> ask, ActorMsgEnvelope envelope) {
            super(envelope);
            this.ask = ask;
        }


        protected void onResult(DeleteByPartKeyResult result) {
            final int code = result.getCode();
            if (this.hasRetried()) {
                LOGGER.info("DeleteByPartKeyTask db_retry_total retry={}, code={}, req={}, option={}, envelop={}",
                        this.getRunCnt(), code, this.ask.getReq(), this.ask.getOption(), this.envelope);
            }
            if (DbUtil.isOk(code)) {
                LOGGER.info("DeleteByPartKeyTask db_retry_success tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), code);
                DbMgrService.this.answer0(this.envelope, new DeleteByPartKeyResult(code));
                return;
            }
            if (DbUtil.isRecordNotExist(code)) {
                LOGGER.info("DeleteByPartKeyTask db_retry_success record not exists, tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), code);
                DbMgrService.this.answer0(this.envelope, new DeleteByPartKeyResult(code));
                return;
            }
            if (!this.ask.getOption().isRetry()) {
                DbMgrService.this.answer0(this.envelope, new DeleteByPartKeyResult(code));
                return;
            }
            if (DbMgrService.this.isRetryTimeout(this.envelope.getSendTsMs())) {
                LOGGER.error("DeleteByPartKeyTask db_retry_fail_total retryTimeout, code={}, req={}, option={}, envelop={}",
                        code, ask.getReq(), ask.getOption(), this.envelope);
                DbMgrService.this.answer0(this.envelope, new DeleteByPartKeyResult(code));
                return;
            }

            DbMgrService.this.addToRetryQueueArray(this);
        }

        @Override
        void doTask() {
            DbManager.getInstance().deleteByPartKeyAsync(this.ask.getReq(), this.ask.getOption(), this::onResult);
        }
    }

    protected class InsertTask extends DbRunnable {
        protected final InsertAsk<?> ask;

        public InsertTask(InsertAsk<?> ask, ActorMsgEnvelope envelope) {
            super(envelope);
            this.ask = ask;

        }


        protected <Pb extends Message.Builder> void onResult(InsertResult<Pb> result) {
            if (this.hasRetried()) {
                LOGGER.info("InsertTask db_retry_total retry={}, code={}, option={}, envelop={}",
                        this.getRunCnt(), result.code, this.ask.getOption(), this.envelope);
            }
            if (result.isOk()) {
                LOGGER.info("InsertTask db_retry_success tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(envelope, result);
                return;
            }
            if (DbUtil.isRecordAlreadyExist(result.getCode())) {
                LOGGER.info("InsertTask db_retry_success record already exists, tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(envelope, result);
                return;
            }
            if (!this.ask.getOption().isRetry()) {
                DbMgrService.this.answer0(envelope, result);
                return;
            }
            if (DbMgrService.this.isRetryTimeout(this.envelope.getSendTsMs())) {
                LOGGER.error("InsertTask db_retry_fail_total retryTimeout, code={}, req={}, option={}, envelop={}",
                        result.code, ask.getReq(), ask.getOption(), this.envelope);
                DbMgrService.this.answer0(envelope, result);
                return;
            }
            DbMgrService.this.addToRetryQueueArray(this);
        }

        @Override
        void doTask() {
            DbManager.getInstance().insertAsync(this.ask.getReq(), this.ask.getOption(), this::onResult);
        }
    }

    protected class DeleteTask extends DbRunnable {
        protected final DeleteAsk<?> ask;


        public DeleteTask(DeleteAsk<?> ask, ActorMsgEnvelope envelope) {
            super(envelope);
            this.ask = ask;
        }


        protected void onResult(final DeleteResult result) {
            final int code = result.getCode();
            if (this.hasRetried()) {
                LOGGER.info("DeleteTask db_retry_total retry={}, code={}, req={}, option={}, envelop={}",
                        this.getRunCnt(), code, this.ask.getReq(), this.ask.getOption(), this.envelope);
            }
            if (DbUtil.isOk(code)) {
                LOGGER.info("DeleteTask db_retry_success tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), code);
                DbMgrService.this.answer0(this.envelope, new DeleteResult(code));
                return;
            }
            if (DbUtil.isRecordNotExist(code)) {
                LOGGER.info("DeleteTask db_retry_success record not exists, tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), code);
                DbMgrService.this.answer0(this.envelope, new DeleteResult(code));
                return;
            }
            if (!this.ask.getOption().isRetry()) {
                DbMgrService.this.answer0(this.envelope, new DeleteResult(code));
                return;
            }
            if (DbMgrService.this.isRetryTimeout(this.envelope.getSendTsMs())) {
                LOGGER.error("DeleteTask db_retry_fail_total retryTimeout, code={}, req={}, option={}, envelop={}",
                        code, ask.getReq(), ask.getOption(), this.envelope);
                DbMgrService.this.answer0(this.envelope, new DeleteResult(code));
                return;
            }
            DbMgrService.this.addToRetryQueueArray(this);
        }


        @Override
        void doTask() {
            DbManager.getInstance().deleteAsync(this.ask.getReq(), this.ask.getOption(), this::onResult);
        }
    }

    protected class UpdateTask extends DbRunnable {
        protected final UpdateAsk<?> ask;

        public UpdateTask(UpdateAsk<?> ask, ActorMsgEnvelope envelope) {
            super(envelope);
            this.ask = ask;
        }


        protected <Pb extends Message.Builder> void onResult(final UpdateResult<Pb> result) {
            if (this.hasRetried()) {
                LOGGER.info("UpdateTask db_retry_total retry={}, code={}, option={}, envelop={}",
                        this.getRunCnt(), result.code, this.ask.getOption(), this.envelope);
            }
            if (result.isOk()) {
                LOGGER.info("UpdateTask db_retry_success tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (result.isRecordNotExist()) {
                LOGGER.info("UpdateTask db_retry_success record not exists, tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (!this.ask.getOption().isRetry()) {
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (DbMgrService.this.isRetryTimeout(this.envelope.getSendTsMs())) {
                LOGGER.error("UpdateTask db_retry_fail_total retryTimeout, code={}, req={}, option={}, envelop={}",
                        result.getCode(), ask.getReq(), ask.getOption(), this.envelope);
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            DbMgrService.this.addToRetryQueueArray(this);
        }

        @Override
        void doTask() {
            DbManager.getInstance().updateAsync(this.ask.getReq(), this.ask.getOption(), this::onResult);
        }
    }

    protected class UpsertTask extends DbRunnable {
        protected final UpsertAsk<?> ask;

        public UpsertTask(UpsertAsk<?> ask, ActorMsgEnvelope envelope) {
            super(envelope);
            this.ask = ask;
        }

        protected <Pb extends Message.Builder> void onResult(UpsertResult<Pb> result) {
            if (this.hasRetried()) {
                LOGGER.info("UpsertTask db_retry_total retry={}, code={}, option={}, envelop={}",
                        this.getRunCnt(), result.code, this.ask.getOption(), this.envelope);
            }
            if (result.isOk()) {
                LOGGER.info("UpsertTask db_retry_success tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (!this.ask.getOption().isRetry()) {
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (DbMgrService.this.isRetryTimeout(this.envelope.getSendTsMs())) {
                LOGGER.error("UpsertTask db_retry_fail_total retryTimeout, code={}, req={}, option={}, envelop={}",
                        result.getCode(), ask.getReq(), ask.getOption(), this.envelope);
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            DbMgrService.this.addToRetryQueueArray(this);
        }

        @Override
        void doTask() {
            DbManager.getInstance().upsertAsync(this.ask.getReq(), this.ask.getOption(), this::onResult);
        }
    }

    protected class IncreaseTask extends DbRunnable {
        protected final IncreaseAsk<?> ask;

        public IncreaseTask(IncreaseAsk<?> ask, ActorMsgEnvelope envelope) {
            super(envelope);
            this.ask = ask;
        }

        protected <Pb extends Message.Builder> void onResult(IncreaseResult<Pb> result) {
            if (this.hasRetried()) {
                LOGGER.info("IncreaseTask db_retry_total retry={}, code={}, req={}, option={}, envelop={}",
                        this.getRunCnt(), result.code, this.ask.getReq(), this.ask.getOption(), this.envelope);
            }
            if (result.isOk()) {
                LOGGER.info("IncreaseTask db_retry_success tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (result.isRecordNotExist()) {
                LOGGER.info("IncreaseTask db_retry_success record not exists, tableName={}, keys={}, code={}",
                        TcaplusUtils.getTableName(this.ask.getReq()), TcaplusUtils.getKeys(this.ask.getReq()), result.getCode());
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (!this.ask.getOption().isRetry()) {
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            if (DbMgrService.this.isRetryTimeout(this.envelope.getSendTsMs())) {
                LOGGER.error("IncreaseTask db_retry_fail_total retryTimeout, code={}, req={}, option={}, envelop={}",
                        result.getCode(), ask.getReq(), ask.getOption(), this.envelope);
                DbMgrService.this.answer0(this.envelope, result);
                return;
            }
            DbMgrService.this.addToRetryQueueArray(this);
        }

        @Override
        void doTask() {
            DbManager.getInstance().increaseAsync(this.ask.getReq(), this.ask.getOption(), this::onResult);
        }
    }
}

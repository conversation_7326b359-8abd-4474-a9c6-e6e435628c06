package com.yorha.common.dbactor;

import com.google.protobuf.Message;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.result.DeleteResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.exception.GeminiException;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.function.BiConsumer;

public class DefaultDbOperationStrategyImpl implements IDbOperationStrategy {
    private final IActorRef dbRef;

    public DefaultDbOperationStrategyImpl(final IActorRef dbRef) {
        this.dbRef = dbRef;
    }

    @Override
    public void updateAsync(AbstractActor actor, UpdateAsk<Message.Builder> ask, @Nullable BiConsumer<UpdateResult<Message.Builder>, Throwable> callback) {
        if (callback == null) {
            actor.tell(this.dbRef, ask);
            return;
        }
        if (ask.getOption().isRetry()) {
            actor.<UpdateResult<Message.Builder>>ask(this.dbRef, ask, DbUtil.CALL_TIMEOUT_TIME_MS).onComplete(callback);
        } else {
            actor.<UpdateResult<Message.Builder>>ask(this.dbRef, ask).onComplete(callback);
        }
    }

    @Override
    public UpdateResult<Message.Builder> updateSync(AbstractActor actor, UpdateAsk<Message.Builder> ask) {
        try {
            if (ask.getOption().isRetry()) {
                return actor.call(this.dbRef, ask, DbUtil.CALL_TIMEOUT_TIME_MS);
            }
            return actor.call(this.dbRef, ask);
        } catch (GeminiException e) {
            throw new GeminiException(actor.toString(), e.getCause());
        }
    }

    @Override
    public void insertAsync(AbstractActor actor, InsertAsk<Message.Builder> ask, @Nullable BiConsumer<InsertResult<Message.Builder>, Throwable> callback) {
        if (callback == null) {
            actor.tell(this.dbRef, ask);
            return;
        }
        if (ask.getOption().isRetry()) {
            actor.<InsertResult<Message.Builder>>ask(this.dbRef, ask, DbUtil.CALL_TIMEOUT_TIME_MS).onComplete(callback);
        } else {
            actor.<InsertResult<Message.Builder>>ask(this.dbRef, ask).onComplete(callback);
        }
    }

    @Override
    public InsertResult<Message.Builder> insertSync(AbstractActor actor, InsertAsk<Message.Builder> ask) {
        try {
            if (ask.getOption().isRetry()) {
                return actor.call(this.dbRef, ask, DbUtil.CALL_TIMEOUT_TIME_MS);
            }
            return actor.call(this.dbRef, ask);
        } catch (GeminiException e) {
            throw new GeminiException(actor.toString(), e.getCause());
        }
    }

    @Override
    public void deleteAsync(AbstractActor actor, DeleteAsk<Message.Builder> ask, BiConsumer<DeleteResult, Throwable> callback) {
        actor.<DeleteResult>ask(this.dbRef, ask).onComplete(callback);
    }

}

package com.yorha.common.resource.resservice.item;

import com.google.common.collect.*;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.constant.Constants;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructPB;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.Nullable;
import res.template.*;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ItemResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(ItemResService.class);

    private Map<Integer, List<ItemRewardBox>> selectRewardMap;

    private final Map<Integer, RandomRewardMeta> randomRewardPools = Maps.newHashMap();

    /**
     * 道具id -> 道具回收配置
     */
    private final Map<Integer, ItemWithdrawConf.List> itemWithdrawConfMap = Maps.newHashMap();

    /**
     * 主堡等级 -> 到达等级后需要回收的道具id
     */
    private final Multimap<Integer, Long> mainCityLevel2withdrawItemIds = HashMultimap.create();

    public ItemResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        Map<Integer, List<ItemRewardBox>> selectRewards = new HashMap<>();
        for (SelecteRewardTemplate rewardTemplate : getResHolder().getListFromMap(SelecteRewardTemplate.class)) {
            CommonEnum.SelectorType selectorType = rewardTemplate.getType();
            if (selectorType == null) {
                throw new ResourceException(StringUtils.format("{} select reward type not exist:{}", rewardTemplate.getId(), selectorType));
            }
            List<ItemRewardBox> rewardBoxList = parse(selectorType, rewardTemplate.getRewards());
            // 检查奖池中是否有机甲配件道具，如果有，记录下来
            selectRewards.put(rewardTemplate.getId(), rewardBoxList);
        }
        this.selectRewardMap = selectRewards;

        for (ItemTemplate itemTemplate : getResHolder().getListFromMap(ItemTemplate.class)) {
            if (StringUtils.isNotEmpty(itemTemplate.getWithdrawParam())) {
                ItemWithdrawConf.List withdrawConfList = ItemWithdrawConf.List.parse(itemTemplate.getWithdrawParam());
                itemWithdrawConfMap.put(itemTemplate.getId(), withdrawConfList);
                for (ItemWithdrawConf conf : withdrawConfList.getConfList()) {
                    if (conf instanceof ItemWithdrawConf.CityLevel) {
                        mainCityLevel2withdrawItemIds.put(((ItemWithdrawConf.CityLevel) conf).getCityLevel(), (long) itemTemplate.getId());
                    }
                }
            }
        }

        loadRandomRewardPools();
    }

    /**
     * 加载随机道具池
     */
    private void loadRandomRewardPools() {
        for (RandomRewardTemplate randomRewardTemplate : getResHolder().getListFromMap(RandomRewardTemplate.class)) {
            int randomRewardPoolId = randomRewardTemplate.getPoolId();
            ItemRewardBox randomRewardPool = randomRewardPools.computeIfAbsent(randomRewardPoolId,
                    (k) -> new RandomRewardMeta(new ItemRewardBox(), getResHolder().getValueFromMap(RandomRewardPoolTemplate.class, k))).getItemRewardBox();

            ItemReward.Builder itemReward = ItemReward.newBuilder()
                    .setItemTemplate(randomRewardTemplate.getItemId())
                    .setCount(randomRewardTemplate.getNum())
                    .setWeight(randomRewardTemplate.getWeight());
            randomRewardPool.addReward(itemReward.build());
        }
    }

    /**
     * 从随机池中获取奖励
     *
     * @param randomRewardPoolId 随机道具池id
     * @return Pair<道具奖励 ， 道具奖励是否在清空保底条件中
     */
    public Pair<ItemReward, Boolean> getRewardFromRandomPool(int randomRewardPoolId) {
        Pair<ItemReward, Boolean> res = new Pair<>(null, false);

        RandomRewardMeta randomRewardMeta = this.randomRewardPools.getOrDefault(randomRewardPoolId, null);
        if (randomRewardMeta == null) {
            return res;
        }

        ItemReward itemReward = randomRewardMeta.getItemRewardBox().reward();
        if (itemReward == null) {
            return res;
        }

        res.setFirst(itemReward);
        res.setSecond(randomRewardMeta.getTrigger().contains(itemReward.getItemTemplateId()));
        return res;
    }

    /**
     * 获取保底次数，小于0为不存在
     *
     * @param randomRewardPoolId 随机道具池id
     * @return 保底次数
     */
    public int getTriggerTime(int randomRewardPoolId) {
        if (randomRewardPools.get(randomRewardPoolId) == null) {
            return -1024;
        }
        return randomRewardPools.get(randomRewardPoolId).getTriggerTime();
    }

    /**
     * 获取保底奖励（固定+随机），null为不存在
     *
     * @param randomRewardPoolId 随机道具池id
     * @return 保底奖励
     */
    @Nullable
    public List<ItemReward> getGuaranteeReward(int randomRewardPoolId) {
        if (randomRewardPools.get(randomRewardPoolId) == null) {
            return null;
        }
        List<ItemReward> fixedReward = randomRewardPools.get(randomRewardPoolId).getFixedGuaranteeReward();
        List<ItemReward> randomReward = randomRewardPools.get(randomRewardPoolId).getRandomGuaranteeReward();
        List<ItemReward> reward = new ArrayList<>(fixedReward.size() + randomReward.size());
        reward.addAll(fixedReward);
        reward.addAll(randomReward);
        return reward;
    }

    private List<ItemRewardBox> parse(CommonEnum.SelectorType type, String rewardStr) throws ResourceException {
        List<ItemRewardBox> rewardBox = new ArrayList<>();
        if (type == CommonEnum.SelectorType.ST_COMPOUND) {
            String[] split = org.apache.commons.lang3.StringUtils.split(rewardStr, Constants.FEN_HAO);
            for (String s : split) {
                ItemRewardBox box = new ItemRewardBox();
                String[] rewards = org.apache.commons.lang3.StringUtils.split(s, Constants.DOU_HAO);
                for (String reward : rewards) {
                    String[] strings = org.apache.commons.lang3.StringUtils.split(reward, Constants.XIA_HUA_XIAN);
                    if (strings.length != 3) {
                        throw new ResourceException("select reward format error:" + rewardStr);
                    }
                    ItemReward itemReward = ItemReward.newBuilder()
                            .setItemTemplate(Integer.parseInt(strings[0]))
                            .setCount(Integer.parseInt(strings[1]))
                            .setWeight(Integer.parseInt(strings[2]))
                            .build();
                    box.addReward(itemReward);
                }
                rewardBox.add(box);
            }
            return rewardBox;
        } else if (type == CommonEnum.SelectorType.ST_PICK_UP) {
            ItemRewardBox box = new ItemRewardBox();
            String[] rewards = org.apache.commons.lang3.StringUtils.split(rewardStr, Constants.DOU_HAO);
            for (String reward : rewards) {
                String[] strings = org.apache.commons.lang3.StringUtils.split(reward, Constants.XIA_HUA_XIAN);
                if (strings.length != 2) {
                    throw new ResourceException("select reward format error:" + rewardStr);
                }
                ItemReward itemReward = ItemReward.newBuilder()
                        .setItemTemplate(Integer.parseInt(strings[0]))
                        .setCount(Integer.parseInt(strings[1]))
                        .build();
                box.addReward(itemReward);
            }
            rewardBox.add(box);

            return rewardBox;
        } else {
            throw new ResourceException("select reward format error type: " + type);
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        for (ItemTemplate itemTemplate : getResHolder().getListFromMap(ItemTemplate.class)) {
            checkItemTemplate(itemTemplate);
        }
        checkRandomRewardAndPool();
    }

    private void checkRandomRewardAndPool() throws ResourceException {
        Set<Integer> usedRandomRewardPoll = Sets.newHashSet();
        for (RandomRewardTemplate randomRewardTemplate : getResHolder().getListFromMap(RandomRewardTemplate.class)) {
            int randomRewardPoolId = randomRewardTemplate.getPoolId();
            usedRandomRewardPoll.add(randomRewardPoolId);
            checkRandomReward(randomRewardTemplate);
        }

        for (RandomRewardPoolTemplate randomRewardPoolTemplate : getResHolder().getListFromMap(RandomRewardPoolTemplate.class)) {
            if (!usedRandomRewardPoll.contains(randomRewardPoolTemplate.getId())) {
                throw new ResourceException("奖励表 random_reward_pool中 id={} 为空池子", randomRewardPoolTemplate.getId());
            }
            checkRandomRewardPool(randomRewardPoolTemplate);
        }
    }

    private void checkRandomRewardPool(RandomRewardPoolTemplate randomRewardPoolTemplate) throws ResourceException {
        //检查触发保底次数
        int triggerTime = randomRewardPoolTemplate.getTriggerTime();
        if (triggerTime <= 0) {
            throw new ResourceException("奖励表 random_reward_pool中 id={} 触发保底次数非正数", randomRewardPoolTemplate.getId());
        }

        // 检查清空保底条件
        {
            List<Integer> guaranteeTriggerList = randomRewardPoolTemplate.getGuaranteeTriggerList();
            if (guaranteeTriggerList.isEmpty()) {
                throw new ResourceException("奖励表 random_reward_pool中 id={} 清空保底条件为空", randomRewardPoolTemplate.getId());
            }

            for (Integer itemId : guaranteeTriggerList) {
                if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                    throw new ResourceException("奖励表 random_reward_pool中 id={} 清空保底条件中存在未知道具 itemId={}", randomRewardPoolTemplate.getId(), itemId);
                }
            }
        }

        // 检查保底奖励
        {
            List<IntPairType> fixedGuaranteeReward = randomRewardPoolTemplate.getGuaranteeRewardPairList();
            List<IntTripleType> randomGuaranteeReward = randomRewardPoolTemplate.getRandomGuaranteeRewardTripleList();
            if (fixedGuaranteeReward.isEmpty() && randomGuaranteeReward.isEmpty()) {
                throw new ResourceException("奖励表 random_reward_pool中 id={} 保底奖励(固定与随机)为空", randomRewardPoolTemplate.getId());
            }

            for (IntPairType reward : fixedGuaranteeReward) {
                if (getResHolder().findValueFromMap(ItemTemplate.class, reward.getKey()) == null) {
                    throw new ResourceException("奖励表 random_reward_pool中 id={} 固定保底奖励中存在未知道具 itemId={}", randomRewardPoolTemplate.getId(), reward.getKey());
                }

                if (reward.getValue() <= 0) {
                    throw new ResourceException("奖励表 random_reward_pool中 id={} 固定保底奖励中存在道具数量错误 itemId={}", randomRewardPoolTemplate.getId(), reward.getKey());
                }
            }

            for (IntTripleType reward : randomGuaranteeReward) {
                if (getResHolder().findValueFromMap(ItemTemplate.class, reward.getKey()) == null) {
                    throw new ResourceException("奖励表 random_reward_pool中 id={} 随机保底奖励中存在未知道具 itemId={}", randomRewardPoolTemplate.getId(), reward.getKey());
                }

                if (reward.getValue1() <= 0) {
                    throw new ResourceException("奖励表 random_reward_pool中 id={} 随机保底奖励中存在道具数量错误 itemId={}", randomRewardPoolTemplate.getId(), reward.getKey());
                }

                if (reward.getValue2() <= 0) {
                    throw new ResourceException("奖励表 random_reward_pool中 id={} 随机保底奖励中存在权重错误 itemId={}", randomRewardPoolTemplate.getId(), reward.getKey());
                }
            }
        }
    }

    private void checkRandomReward(RandomRewardTemplate randomRewardTemplate) throws ResourceException {
        int randomRewardPoolId = randomRewardTemplate.getPoolId();
        // 校验poolId是否存在
        if (getResHolder().findValueFromMap(RandomRewardPoolTemplate.class, randomRewardPoolId) == null) {
            throw new ResourceException("奖励表 random_reward中 id={} 无对应随机池id：{}", randomRewardTemplate.getId(), randomRewardPoolId);
        }
        // 校验itemId是否存在
        if (getResHolder().getValueFromMap(ItemTemplate.class, randomRewardTemplate.getItemId()) == null) {
            throw new ResourceException("奖励表 random_reward中 id={} 无对应道具id：{}", randomRewardTemplate.getId(), randomRewardTemplate.getItemId());
        }
        // 检验数量
        if (randomRewardTemplate.getNum() <= 0) {
            throw new ResourceException("奖励表 random_reward中 id={} 数量有误：{}", randomRewardTemplate.getId(), randomRewardTemplate.getNum());
        }
        // 检验权重
        if (randomRewardTemplate.getWeight() <= 0) {
            throw new ResourceException("奖励表 random_reward中 id={} 权重有误：{}", randomRewardTemplate.getId(), randomRewardTemplate.getWeight());
        }

    }

    private void checkItemTemplate(ItemTemplate itemTemplate) throws ResourceException {
        if (itemTemplate.getCdSeconds() > 0) {
            throw new ResourceException("道具不允许设置CD item={}", itemTemplate.getId());
        }

        CommonEnum.ItemUseType itemUseType = CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType());
        if (itemUseType != null) {
            switch (itemUseType) {
                case ADD_SOLDIER:
                    final int soldierId = itemTemplate.getEffectId();
                    final int soldierNum = itemTemplate.getEffectValue();
                    getResHolder().getValueFromMap(SoldierTypeTemplate.class, soldierId);
                    if (soldierNum <= 0) {
                        throw new ResourceException("加士兵道具EffectValue为加士兵数，需要>0, 现在=" + soldierNum);
                    }
                    break;
                case RECRUIT_HERO:
                    int heroTatterId = itemTemplate.getEffectId();
                    if (!getResHolder().getMap(ItemTemplate.class).containsKey(heroTatterId)) {
                        throw new ResourceException("道具表：英雄道具配置的英雄碎片道具id不合法 id :" + itemTemplate.getId());
                    }
                    if (itemTemplate.getEffectValue() <= 0) {
                        throw new ResourceException("道具表：英雄道具配置的英雄碎片数量(effectValue)不合法 id :" + itemTemplate.getId());
                    }
                    int heroId = itemTemplate.getEffectValue2();
                    if (!getResHolder().getMap(HeroRhTemplate.class).containsKey(heroId)) {
                        throw new ResourceException("道具表：英雄道具配置的英雄id(effectValue2)不合法 id :" + itemTemplate.getId());
                    }
                    break;
                case CITY_DRESS:
                    checkCityDress(itemTemplate);
                    break;
                case RANDOM_WITH_GUARANTEE:
                    checkRandomWithGurarantee(itemTemplate);
                    break;
                default:
                    // 需要补充各种校验啦!!
            }
        }
    }

    /**
     * 校验带保底的随机道具
     *
     * @param itemTemplate 道具模板
     * @throws ResourceException 配表错误
     */
    private void checkRandomWithGurarantee(ItemTemplate itemTemplate) throws ResourceException {
        int randomPoolId = itemTemplate.getEffectId();
        if (getResHolder().findValueFromMap(RandomRewardPoolTemplate.class, randomPoolId) == null) {
            throw new ResourceException("道具表：带保底的随机道具配置的道具效果id有误（无对应随机池id） id :{}, 随机池id : {}", itemTemplate.getId(), randomPoolId);
        }
    }

    /**
     * 校验主堡皮肤解锁道具
     *
     * @param itemTemplate 道具模板
     * @throws ResourceException 配表错误
     */
    private void checkCityDress(ItemTemplate itemTemplate) throws ResourceException {
        int dressSubTemplateId = itemTemplate.getEffectValue();
        if (getResHolder().findValueFromMap(DressTemplate.class, dressSubTemplateId) == null) {
            throw new ResourceException("道具表：基地皮肤道具配置的道具效果value有误（无对应基地外观子ID） id :{}, 基地外观子ID : {}", itemTemplate.getId(), dressSubTemplateId);
        }
        for (IntPairType itemPair : itemTemplate.getExchangeItemIdPairList()) {
            int exchangeItemId = itemPair.getKey();
            if (!getResHolder().getMap(ItemTemplate.class).containsKey(exchangeItemId)) {
                throw new ResourceException("道具表：基地皮肤道具配置的补偿兑换道具有误（无对应道具ID） id :{}, exchangeItems : {}", itemTemplate.getId(), itemTemplate.getExchangeItemIdPairList());
            }
            int exchangeItemNum = itemPair.getValue();
            if (exchangeItemNum <= 0) {
                throw new ResourceException("道具表：基地皮肤道具配置的补偿兑换道具有误（数量为非正数） id :{}, exchangeItems : {}", itemTemplate.getId(), itemTemplate.getExchangeItemIdPairList());
            }
        }
    }

    @Override
    public String getResName() {
        return "item";
    }

    public ItemTemplate getItemTemplate(int id) {
        return getResHolder().getValueFromMap(ItemTemplate.class, id);
    }

    // 战役背包消耗
    public int getItemCost(int id) {
        ItemTemplate template = getItemTemplate(id);
        return template.getItemCost() > 0 ? template.getItemCost() : 1;
    }

    public List<ItemRewardBox> getSelectReward(int rewardId) {
        return selectRewardMap.get(rewardId);
    }

    public List<ItemRewardBox> getSelectRewardOrNull(int rewardId) {
        return selectRewardMap.getOrDefault(rewardId, null);
    }

    public List<ItemReward> randomReward(int rewardId) {
        if (rewardId == 0) {
            // 填0代表没有奖励，如果不希望0被处理，那需要在ResService中主动判断拦截
            return Collections.emptyList();
        }
        SelecteRewardTemplate template = ResHolder.findTemplate(SelecteRewardTemplate.class, rewardId);
        if (template == null) {
            LOGGER.error("rewardId {} not exist.", rewardId);
            return Collections.emptyList();
        }
        SelecteRewardTemplate rewardTemplate = ResHolder.getInstance().getValueFromMap(SelecteRewardTemplate.class, rewardId);
        if (rewardTemplate == null || rewardTemplate.getType() != CommonEnum.SelectorType.ST_COMPOUND) {
            return Collections.emptyList();
        }

        List<ItemReward> rewards = new ArrayList<>();
        List<ItemRewardBox> selectReward = getSelectReward(rewardId);
        if (CollectionUtils.isNotEmpty(selectReward)) {
            // 没配置的随机1次
            final int times = template.getRandTimes() > 0 ? template.getRandTimes() : 1;
            for (int i = 0; i < times; i++) {
                selectReward.forEach(rewardBox -> {
                    ItemReward reward = rewardBox.reward();
                    if (reward != null) {
                        rewards.add(reward);
                    }
                });
            }
        }
        return rewards;
    }

    public List<ItemReward> randomRewardList(List<Integer> rewardIdList) {
        if (rewardIdList.isEmpty()) {
            return Collections.emptyList();
        }
        List<ItemReward> rewards = new ArrayList<>();
        for (Integer rewardId : rewardIdList) {
            if (rewardId == 0) {
                continue;
            }
            rewards.addAll(randomReward(rewardId));
        }
        return rewards;
    }


    public AssetPackage randomReward2Pack(int rewardId) {
        List<ItemReward> itemRewards = randomReward(rewardId);
        if (itemRewards.isEmpty()) {
            return AssetPackage.EMPTY;
        }
        AssetPackage.Builder builder = AssetPackage.builder();
        for (ItemReward item : itemRewards) {
            builder.plusItem(item.getItemTemplateId(), item.getCount());
        }
        return builder.build();
    }

    public static StructPB.ItemListPB.Builder makeItemListPB(List<ItemReward> rewards) {
        StructPB.ItemListPB.Builder items = StructPB.ItemListPB.newBuilder();
        if (CollectionUtils.isNotEmpty(rewards)) {
            Map<Integer, Integer> map = new HashMap<>();
            rewards.forEach(reward -> map.compute(reward.getItemTemplateId(), (k, v) -> v == null ? reward.getCount() : reward.getCount() + v));
            map.forEach((k, v) -> {
                StructPB.ItemPB.Builder builder = StructPB.ItemPB.newBuilder();
                builder.setTemplateId(k);
                builder.setNum(v);
                items.addDatas(builder);
            });
        }

        return items;
    }

    public boolean isItemExists(int id) {
        return getResHolder().getMap(ItemTemplate.class).containsKey(id);
    }

    public ItemWithdrawConf.List findWithdrawConf(int itemTemplateId) {
        return itemWithdrawConfMap.get(itemTemplateId);
    }

    static class RandomRewardMeta {
        private final ItemRewardBox itemRewardBox;
        private final Set<Integer> trigger;
        private final int triggerTime;
        private final List<ItemReward> fixedGuaranteeReward = new LinkedList<>();
        private final ItemRewardBox randomGuaranteeReward;

        RandomRewardMeta(ItemRewardBox randomRewardBox, RandomRewardPoolTemplate randomRewardPoolTemplate) {
            this.itemRewardBox = randomRewardBox;
            this.triggerTime = randomRewardPoolTemplate.getTriggerTime();
            this.trigger = new HashSet<>(randomRewardPoolTemplate.getGuaranteeTriggerList());

            for (IntPairType guaranteeReward : randomRewardPoolTemplate.getGuaranteeRewardPairList()) {
                ItemReward.Builder itemReward = ItemReward.newBuilder()
                        .setItemTemplate(guaranteeReward.getKey())
                        .setCount(guaranteeReward.getValue());
                this.fixedGuaranteeReward.add(itemReward.build());
            }

            // 加载随机保底奖励
            this.randomGuaranteeReward = new ItemRewardBox();
            for (IntTripleType randomGuarantee : randomRewardPoolTemplate.getRandomGuaranteeRewardTripleList()) {
                int itemId = randomGuarantee.getKey();
                int count = randomGuarantee.getValue1();
                int weight = randomGuarantee.getValue2();
                ItemReward.Builder itemReward = ItemReward.newBuilder()
                        .setItemTemplate(itemId)
                        .setCount(count)
                        .setWeight(weight);
                this.randomGuaranteeReward.addReward(itemReward.build());
            }

        }

        public ItemRewardBox getItemRewardBox() {
            return itemRewardBox;
        }

        public int getTriggerTime() {
            return triggerTime;
        }

        public List<ItemReward> getFixedGuaranteeReward() {
            return fixedGuaranteeReward;
        }

        public List<ItemReward> getRandomGuaranteeReward() {
            ItemReward itemReward = this.randomGuaranteeReward.reward();
            if (itemReward == null) {
                return Lists.newArrayList();
            }
            List<ItemReward> res = new LinkedList<>();
            res.add(itemReward);
            return res;
        }

        public Set<Integer> getTrigger() {
            return trigger;
        }
    }
}

package com.yorha.common.constant;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface TextFilterConstant {

    enum TextFilterType {
        HTTP(0),
        TSSSDK(1),
        UNKNOWN(999);
        private final int value;

        TextFilterType(int value) {
            this.value = value;
        }

        public static TextFilterType forNumber(int value) {
            switch (value) {
                case 0: return HTTP;
                case 1: return TSSSDK;
                default: return UNKNOWN;
            }
        }
    }

}

package com.yorha.common.perf;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.constant.LogConstant;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class AbstractPerfLogger {
    private static final Logger LOGGER = LogManager.getLogger(AbstractPerfLogger.class);

    private static final Logger LOGGER_PERF = LogManager.getLogger(LogConstant.LOG_TYPE_PERF_STAT);
    private final ExecutorService executor;

    public AbstractPerfLogger(String name) {
        this.executor = ConcurrentHelper.newSingleThreadExecutor(name, 100_000, true);
    }

    protected void run(Runnable runnable) {
        this.executor.execute(runnable);
    }

    protected Logger getLoggerPerf() {
        return LOGGER_PERF;
    }

    public void stop() {
        stopWithSpecificTime(3);
    }

    /**
     * 指定时间版本
     */
    public void stopWithSpecificTime(long waitTime) {
        this.executor.shutdown();
        try {
            boolean isOk = this.executor.awaitTermination(waitTime, TimeUnit.SECONDS);
            LOGGER.warn("AbstractPerfLogger stopWithSpecificTime {}", isOk);
        } catch (InterruptedException e) {
            LOGGER.error("AbstractPerfLogger stopWithSpecificTime ", e);
        }
    }
}

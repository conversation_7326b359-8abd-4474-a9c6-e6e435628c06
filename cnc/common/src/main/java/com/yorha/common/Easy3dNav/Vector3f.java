package com.yorha.common.Easy3dNav;

/**
 * float  向量
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2019/12/26.
 */
public class Vector3f implements Cloneable {
    private float x;
    private float y;
    private float z;


    public Vector3f() {
    }

    public Vector3f(float x, float y, float z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public float getZ() {
        return z;
    }

    public void setZ(float z) {
        this.z = z;
    }
}

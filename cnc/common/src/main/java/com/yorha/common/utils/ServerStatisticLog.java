package com.yorha.common.utils;


import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.WorldEnvFlag;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.WechatConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Supplier;

/**
 * 用来发送统计日志的工具
 *
 * <AUTHOR>
 */
public class ServerStatisticLog {
    private static final Logger LOGGER = LogManager.getLogger(ServerStatisticLog.class);

    private static class LazyHolder {
        private static final ServerStatisticLog INSTANCE = new ServerStatisticLog();
    }

    public static ServerStatisticLog getInstance() {
        return LazyHolder.INSTANCE;
    }


    private Supplier<WechatConfig> serverConfigSupplier;

    /**
     * 发送客户端及发送线程
     */
    private ThreadPoolExecutor executorService;
    PlatformClient platformClient;

    public void init(Supplier<WechatConfig> serverConfigSupplier) {
        if (ServerContext.getEnv() == WorldEnvFlag.PROD || ServerContext.isPressEnv()) {
            return;
        }
        if (this.serverConfigSupplier != null) {
            LOGGER.error("WechatLog serverConfigSupplier is already exist");
            return;
        }
        this.serverConfigSupplier = serverConfigSupplier;
        this.platformClient = new StatisticPlatformClient(5000);
        this.executorService = ConcurrentHelper.newSingleThreadExecutor("statistic-log", 0, true);
    }

    private static class StatisticPlatformClient extends PlatformClient {

        StatisticPlatformClient(int timeout) {
            super(timeout);
        }

        @Override
        public String getHostName() {
            return "http://***********:10001";
        }

    }

    public void record2StatisticServer(ServerStatisticType key) {
        record2StatisticServer(key, 1, 0);
    }

    public void record2StatisticServer(ServerStatisticType key, int value, int value2) {
        try {
            if (ServerContext.getEnv() == WorldEnvFlag.PROD) {
                return;
            }
            if (executorService == null) {
                LOGGER.info("ServerStatisticLog record2StatisticServer key={} value={} value2={}", key, value, value2);
                return;
            }
            ServerStatisticData data = newDataInstant(key, value, value2);

            executorService.execute(() -> {
                try {
                    platformClient.post4https("/statistics", data.toJson());
                } catch (Throwable e) {
                    LOGGER.error(e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    static class ServerStatisticData {
        private final ServerStatisticType key;
        private final int value1;
        private final int value2;
        private final String busId;

        private ServerStatisticData(ServerStatisticType key, int value1, int value2, String busId) {
            this.key = key;
            this.value1 = value1;
            this.value2 = value2;
            this.busId = busId;
        }

        public String toJson() {
            String s = JsonUtils.toJsonString(this);
            return s;
        }

        private ServerStatisticType getKey() {
            return key;
        }

        private int getValue1() {
            return value1;
        }

        private int getValue2() {
            return value2;
        }

        private String getBusId() {
            return busId;
        }
    }


    private ServerStatisticData newDataInstant(ServerStatisticType key, int value, int value2) {
        String busId = "0.0.0.0";
        if (ServerStatisticLog.getInstance().serverConfigSupplier != null) {
            WechatConfig config = ServerStatisticLog.getInstance().serverConfigSupplier.get();
            busId = config.getBusId();
        }
        return new ServerStatisticData(key, value, value2, busId);
    }

    public enum ServerStatisticType {
        // 登录，一维数据
        LOGIN,
        // 登出，一维数据
        LOGOUT,
        DAY_TICK,
    }
}

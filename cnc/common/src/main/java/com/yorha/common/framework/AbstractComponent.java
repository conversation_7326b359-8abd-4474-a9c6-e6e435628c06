package com.yorha.common.framework;

import com.yorha.common.actorservice.AbstractActor;
import com.yorha.proto.EntityAttrOuterClass.EntityType;

/**
 * constructor 和 init() 组成一个二阶段的初始化处理
 * <p>
 * constructor 中可以初始化一些独立的内部容器、打印日志
 * <p>
 * init()中可以初始化一些依赖其他component、依赖owner构造函数的逻辑（如：需要注入 ownerActor 的对象初始化）
 *
 * <AUTHOR>
 */
public abstract class AbstractComponent<T extends AbstractEntity> {

    private final T owner;

    public AbstractComponent(T owner) {
        this.owner = owner;
        if (this.owner != null) {
            owner.registerComponent(this);
        }
    }

    /**
     * entity的所有component构造完毕后
     * 异常将会终止其他模块的init
     * 同步调用
     */
    public void init() {
    }

    /**
     * entity的所有component init完毕后
     * 异常将会终止其他模块的postInit
     * 同步调用
     */
    public void postInit() {
    }

    /**
     * entity delete时
     * 异常将会终止其他模块的onDestroy
     * 同步调用
     */
    public void onDestroy() {

    }

    public T getOwner() {
        return this.owner;
    }

    // --------------------------- 快捷方式 ---------------------------

    public long getEntityId() {
        return getOwner().getEntityId();
    }

    public EntityType getEntityType() {
        return getOwner().getEntityType();
    }

    public AbstractActor ownerActor() {
        return getOwner().ownerActor();
    }
}

package com.yorha.common.notification;

import com.google.common.collect.Lists;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.SsPushNotification;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.PushCdTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 推送（业务调用处）
 *
 * <AUTHOR>
 */
public class PushNotificationManager {
    private static final Logger LOGGER = LogManager.getLogger(PushNotificationManager.class);

    private final Int2ObjectOpenHashMap<FreqLimitItem> freqLimitMap;

    protected static class FreqLimitItem {
        private final int coldId;
        private long lastMsgTsMs;
        private long count;

        private FreqLimitItem(int coldId, long lastMsgTsMs, long count) {
            this.coldId = coldId;
            this.lastMsgTsMs = lastMsgTsMs;
            this.count = count;
        }

        public void increment() {
            this.count += 1;
        }

        public void refresh() {
            this.lastMsgTsMs = SystemClock.nowNative();
            this.count = 0;
        }

        public long getLastMsgTsMs() {
            return this.lastMsgTsMs;
        }

        public int getColdId() {
            return this.coldId;
        }

        public long getCount() {
            return this.count;
        }

        public void setLastMsgTsMs(long lastMsgTsMs) {
            this.lastMsgTsMs = lastMsgTsMs;
        }

        public void setCount(int count) {
            this.count = count;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            FreqLimitItem that = (FreqLimitItem) o;
            return coldId == that.coldId && lastMsgTsMs == that.lastMsgTsMs && count == that.count;
        }

        @Override
        public int hashCode() {
            return coldId;
        }
    }

    public PushNotificationManager() {
        this.freqLimitMap = new Int2ObjectOpenHashMap<>();
    }


    /**
     * 多推。
     *
     * @param item            推送item。
     * @param targetPlayerIds 推送目标
     */
    public final void pushMultipleNotification(final INotificationBuilder item, @NotNull final List<Long> targetPlayerIds, @NotNull final List<IActorRef> ntfActors) {
        if (targetPlayerIds.isEmpty()) {
            return;
        }
        if (item.isTopic()) {
            WechatLog.error("PushNotificationManager pushMultipleNotification item={} isTopic, not support", item);
            return;
        }
        final Notification notification = item.getNotification();
        if (notification == null) {
            WechatLog.error("PushNotificationManager pushMultipleNotification, notification not config in PushContent sheet, item={}, playerIds={}", item, targetPlayerIds);
            return;
        }
        // 检查是否处于CD状态
        if (!this.freshLimitItemAndCheckFreqLimitOk(notification.getColdId())) {
            return;
        }

        final int modelId = notification.getPushNtfModelId();

        final SsPushNotification.PushMultipleNotificationCmd msg = this.buildMultiNotification(notification, targetPlayerIds, modelId);

        // 推送数据
        for (IActorRef ntfActor : ntfActors) {
            this.sendMsg(msg, ntfActor);
        }

    }

    /**
     * 单推
     *
     * @param item      推送item
     * @param playerId  推送目标
     * @param ntfActors 推送actor
     */
    public final void pushSingleNotification(final INotificationBuilder item, final long playerId, @NotNull final List<IActorRef> ntfActors) {
        if (item.isTopic()) {
            WechatLog.error("PushNotificationManager pushSingleNotification item={} isTopic, not support", item);
            return;
        }
        final Notification notification = item.getNotification();
        if (notification == null) {
            WechatLog.error("PushNotificationManager pushNonTopicNotification, notification not config in PushContent sheet, item={}, playerId={}", item, playerId);
            return;
        }
        // 检查是否处于CD状态
        if (!this.freshLimitItemAndCheckFreqLimitOk(notification.getColdId())) {
            return;
        }
        final int modelId = notification.getPushNtfModelId();
        final GeneratedMessageV3 msg = this.buildSingleNotification(notification, playerId, modelId);
        for (IActorRef ntfActor : ntfActors) {
            this.sendMsg(msg, ntfActor);
        }

    }

    /**
     * 快捷单推(scene上可直接调用，推送actor与sceneActor在同进程上)。
     *
     * @param item     推送item。
     * @param playerId 推送目标
     */
    public final void pushLocalSingleNotification(final INotificationBuilder item, final long playerId) {
        this.pushSingleNotification(item, playerId, Lists.newArrayList(RefFactory.ofLocalPushNotification()));
    }

    /**
     * 基于topic的推送。
     *
     * @param item  推送item。
     * @param topic 主题。
     */
    public final void pushTopicNotification(INotificationBuilder item, @NotNull String topic, @NotNull final List<IActorRef> ntfActors) {
        // 主题推
        if (topic.isEmpty()) {
            LOGGER.warn("PushNotificationManager pushTopicNotification topic={}", topic);
            return;
        }
        if (!item.isTopic()) {
            WechatLog.error("PushNotificationManager pushTopicNotification item={} isNotTopic, not support", item);
            return;
        }
        final Notification notification = item.getNotification();
        if (notification == null) {
            WechatLog.error("PushNotificationManager pushTopicNotification, notification not config in PushContent sheet, item={}, topic={}", item, topic);
            return;
        }
        // 检查是否处于CD状态
        if (!this.freshLimitItemAndCheckFreqLimitOk(notification.getColdId())) {
            return;
        }
        final GeneratedMessageV3 msg = this.buildTopicNotification(notification, topic);
        for (IActorRef ntfActor : ntfActors) {
            this.sendMsg(msg, ntfActor);
        }


    }

    /**
     * 限制频率。
     *
     * @param coldId cd id。
     * @return true 表示可以发送，false表示不可发送。
     */
    protected boolean freshLimitItemAndCheckFreqLimitOk(final int coldId) {
        if (coldId <= 0) {
            return true;
        }
        if (!this.freqLimitMap.containsKey(coldId)) {
            this.freqLimitMap.put(coldId, new FreqLimitItem(coldId, SystemClock.nowNative(), 0));
        }
        final FreqLimitItem item = this.freqLimitMap.get(coldId);
        item.increment();
        final PushCdTemplate template = ResHolder.getTemplate(PushCdTemplate.class, item.getColdId());
        final long coldTsMs = item.getLastMsgTsMs() + TimeUnit.SECONDS.toMillis(template.getColdTime());
        final long now = SystemClock.nowNative();
        if (coldTsMs <= now) {
            LOGGER.debug("PushNotificationManager freshLimitItemAndCheckFreqLimitOk, refresh cold cd, coldId={}, nowTime={}", item.getColdId(), now);
            item.setLastMsgTsMs(now);
            item.setCount(1);
            return true;
        }
        if (item.getCount() > template.getColdNum()) {
            LOGGER.debug("PushNotificationManager freshLimitItemAndCheckFreqLimitOk, exceed push times, coldId={}, try push time={}, limit times={}",
                    item.getColdId(), item.getCount(), template.getColdNum());
            return false;
        }
        LOGGER.debug("PushNotificationManager freshLimitItemAndCheckFreqLimitOk, coldId={}, cur push time={}, limit times={}",
                item.getColdId(), item.getCount(), template.getColdNum());
        return true;
    }

    protected void sendMsg(final GeneratedMessageV3 msg, IActorRef pushNotificationRef) {
        ActorSendMsgUtils.send(pushNotificationRef, msg);
    }

    private SsPushNotification.PushSingleNotificationCmd buildSingleNotification(final Notification notification, final long playerId, final int modelId) {
        final int titleId = notification.getTitleId();
        final int bodyId = notification.getBodyId();
        final SsPushNotification.PushSingleNotificationCmd.Builder builder = SsPushNotification.PushSingleNotificationCmd.newBuilder();
        builder.setTitle(titleId)
                .setBody(bodyId)
                .setModelId(modelId)
                .setPlayerId(playerId);
        return builder.build();
    }

    private SsPushNotification.PushMultipleNotificationCmd buildMultiNotification(final Notification notification, final List<Long> playerIds, final int modelId) {
        final int titleId = notification.getTitleId();
        final int bodyId = notification.getBodyId();
        final SsPushNotification.PushMultipleNotificationCmd.Builder builder = SsPushNotification.PushMultipleNotificationCmd.newBuilder();
        builder.setTitle(titleId)
                .setBody(bodyId)
                .setModelId(modelId)
                .addAllPlayerIdList(playerIds);
        return builder.build();
    }

    private GeneratedMessageV3 buildTopicNotification(final Notification notification, final String topic) {
        final int titleId = notification.getTitleId();
        final int bodyId = notification.getBodyId();

        final SsPushNotification.PushTopicNotificationCmd.Builder builder = SsPushNotification.PushTopicNotificationCmd.newBuilder();
        builder.setTitle(titleId)
                .setBody(bodyId)
                .setTopic(topic);
        return builder.build();
    }
}

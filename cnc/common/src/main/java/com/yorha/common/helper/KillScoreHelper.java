package com.yorha.common.helper;

import com.yorha.common.resource.ResHolder;
import com.yorha.proto.StructPB;
import res.template.SoldierTypeTemplate;

import java.util.Map;
import java.util.stream.Collectors;

import static com.yorha.common.utils.FormulaUtils.assertNotLongOverFlow;

/**
 * <AUTHOR>
 */
public class KillScoreHelper {

    /**
     * 计算击杀分(通过统计项)
     *
     * @param killSoldierMap 玩家身上统计项:SOLDIER_KILL_TOTAL
     * @return long 击杀分
     */
    public static long getKillScoreByStatistic(final Map<Integer, StructPB.SingleStatisticPB> killSoldierMap) {
        final Map<Integer, Long> killMap = killSoldierMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getValue()));
        return getKillScoreImpl(killMap);
    }

    /**
     * 计算击杀分
     *
     * @param killMap key=soldierId, val=killNum
     */
    private static long getKillScoreImpl(final Map<Integer, Long> killMap) {
        float sum = 0;
        for (Map.Entry<Integer, Long> entry : killMap.entrySet()) {
            double score = ResHolder.getInstance().getValueFromMap(SoldierTypeTemplate.class, entry.getKey()).getKillScore() * entry.getValue();
            assertNotLongOverFlow(score);
            sum += score;
            assertNotLongOverFlow(sum);
        }

        return (long) sum;
    }
}

package com.yorha.common.midas;

public class MidasUtils {
    public static boolean isMidasBuy(final String channelId) {
        return channelId.contains("midasweb");
    }

    public enum ExtraMsg {
        SAME_REQUEST("midas same request"),
        SAME_APP_META("midas same appMeta"),
        ORDER_NOT_FOUND("midas order not found"),
        ILLEGAL_PRODUCT_ID("midas productId illegal"),
        WEB_FAIL("midas buy fail"),
        WEB_PARSE_FAIL("midas buy payItem parse fail"),
        CHECK_BEFORE_DELIVER_FAIL("midas check before deliver fail");

        ExtraMsg(final String msg) {
            this.msg = msg;
        }

        private final String msg;

        public String getMsg() {
            return msg;
        }
    }
}

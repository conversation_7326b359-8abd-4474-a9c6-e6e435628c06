package com.yorha.common.resource;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;

/**
 * 对策划配置数据二次加工
 * 命名规范： XxxResService
 * <AUTHOR>
 */
public abstract class AbstractResService {

    private final ResHolder resHolder;

    public AbstractResService(ResHolder resHolder) {
        this.resHolder = resHolder;
    }

    protected ResHolder getResHolder() {
        return this.resHolder;
    }

    /**
     * 数据加载
     *
     * @throws GeminiException 框架异常
     */
    public abstract void load() throws ResourceException;

    /**
     * 数据的合法性检测
     *
     * @throws GeminiException 框架异常
     */
    public abstract void checkValid() throws ResourceException;

    /**
     * 资源名
     *
     * @return 资源名
     */
    public String getResName() {
        return getClass().getSimpleName();
    }

}

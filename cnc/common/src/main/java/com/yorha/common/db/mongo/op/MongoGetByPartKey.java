package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.Projections;
import com.mongodb.reactivestreams.client.FindPublisher;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.MultiGetIBasicSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.mongo.utils.MongoUtils;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import org.bson.Document;
import org.reactivestreams.Publisher;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 用于根据部分主键（partition key）获取多条记录
 * */
public class MongoGetByPartKey<T extends Message.Builder> extends MongoOperation<T, GetByPartKeyOption, Document, List<Document>, GetByPartKeyResult<T>> {
    public MongoGetByPartKey(MongoDatabase database, T t, GetByPartKeyOption getOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, getOption);
    }

    @Override
    protected MultiGetIBasicSubscriber getSubscriber() {
        return new MultiGetIBasicSubscriber();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        var req = DocumentHelper.formIndex(this.getReq());
        FindPublisher<Document> result = this.database.getCollection(getTableName()).find(req).batchSize(MongoUtils.MAX_RECORD_NUM_PER_GET);
        if (this.getOption().isGetAllFields()) {
            return result.projection(Projections.excludeId());
        }
        this.checkFieldNames(this.getOption().getFieldNames(), this.getReq().getDescriptorForType());
        return result.projection(this.buildProjection(this.getOption().getFieldNames()));
    }

    @Override
    protected GetByPartKeyResult<T> buildResult(List<Document> documents) {
        final GetByPartKeyResult<T> result = new GetByPartKeyResult<>();
        result.values = new ArrayList<>(documents.size());
        if (documents.isEmpty()) {
            result.code = TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
            return result;
        }
        result.code = TcaplusErrorCode.GEN_ERR_SUC;
        for (final Document document : documents) {
            T proto = MongoOperation.buildDefaultValue(this.getReq());
            this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
            ValueWithVersion<T> valueWithVersion = new ValueWithVersion<>();
            valueWithVersion.value = proto;
            result.values.add(valueWithVersion);
        }
        return result;
    }

    @Override
    protected GetByPartKeyResult<T> onMongoError() {
        final GetByPartKeyResult<T> result = new GetByPartKeyResult<>();
        result.values = Collections.emptyList();
        result.code = DEFAULT_ERROR_CODE;
        return result;
    }
}

package com.yorha.common.wechatlog;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.freqLimitCaller.ActionLimiter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 三方服务告警限频
 * (三方服务容易抖动，避免抖动造成无效告警)
 *
 * <AUTHOR>
 */
public enum ThirdPartyErrLogLimiter {
    /**
     * HTTP请求返回错误码
     */
    HTTP_FAIL,
    /**
     * midas超时
     */
    MIDAS_OUT_OF_TIME,
    /**
     * 屏蔽字超时
     */
    TEXT_FILTER_OUT_OF_TIME,
    /**
     * Intl鉴权超时
     */
    INTL_AUTH_OUT_OF_TIME;

    private static final Logger LOGGER = LogManager.getLogger(ThirdPartyErrLogLimiter.class);
    /**
     * ActionLimiter线程不安全，通过单工作线程池串行化
     */
    private static final GeminiThreadPoolExecutor EXECUTOR = ConcurrentHelper.newSingleThreadExecutor("thirdPartyErrLog", 20000, true, new ThreadPoolExecutor.AbortPolicy());
    /**
     * 限频器
     */
    private final ActionLimiter actionLimiter;


    ThirdPartyErrLogLimiter() {
        this.actionLimiter = new ActionLimiter();
    }

    /**
     * 尝试发送wechat错误日志(触发频率不达时打warning)
     *
     * @param messagePattern 格式化字符串
     * @param arguments      格式化参数
     */
    public void tryError(String messagePattern, Object... arguments) {
        try {
            EXECUTOR.submit(() -> {
                var gapMs = ThirdPartyErrLogConfig.getGapMs(this);
                var threshold = ThirdPartyErrLogConfig.getThreshold(this);
                if (!this.actionLimiter.tryAction(gapMs, threshold, () -> WechatLog.error(messagePattern, arguments))) {
                    LOGGER.warn(messagePattern, arguments);
                }
            });
        } catch (Throwable e) {
            // 一般异常为RejectedExecutionException(超出工作队列上限)，直接企业微信告警
            WechatLog.error(messagePattern, arguments);
            LOGGER.warn("ThirdPartyErrLogLimiter tryError e=", e);
        }
    }
}

package com.yorha.common.qlog;


import com.yorha.common.constant.GameLogicConstants;

/**
 * <AUTHOR>
 */
public class QlogUtil {
    static final int LOGIN_CHANNEL_WX = 1;
    static final int LOGIN_CHANNEL_QQ = 2;

    /**
     * 这里不可以返回不存在的appId,默认appId设定qq大区
     */
    public static String getAppId(int loginChannel) {
        if (loginChannel == LOGIN_CHANNEL_QQ) {
            return GameLogicConstants.GAME_QQ_APPID;
        }

        if (loginChannel == LOGIN_CHANNEL_WX) {
            return GameLogicConstants.GAME_WX_APPID;
        }
        return GameLogicConstants.GAME_QQ_APPID;
    }
}

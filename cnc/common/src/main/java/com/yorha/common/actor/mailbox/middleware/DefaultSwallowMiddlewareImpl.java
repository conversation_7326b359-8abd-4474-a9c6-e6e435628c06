package com.yorha.common.actor.mailbox.middleware;

import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actor.mailbox.MsgContext;
import com.yorha.common.actor.mailbox.msg.ActorMailboxMsg;

/**
 * 默认的监控中间件，吞掉任意的无效信息。
 *
 * <AUTHOR>
 */
public class DefaultSwallowMiddlewareImpl implements IMailboxMiddleware {
    @Override
    public void messageOffer(IActorMailbox mb, ActorMailboxMsg msg) {

    }

    @Override
    public void messageRefused(IActorMailbox mb, ActorMailboxMsg msg, String reason) {

    }

    @Override
    public void messageStart(MsgContext context, long startTsNs) {

    }

    @Override
    public void messageDone(MsgContext context, long startTsNs) {

    }

    @Override
    public void messageError(MsgContext context, long startTsNs) {

    }

    @Override
    public void perfReport() {
    }
}

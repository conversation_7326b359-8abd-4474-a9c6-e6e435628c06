package com.yorha.common.concurrent.executor;

import com.yorha.common.concurrent.IGeminiExecutor;
import com.yorha.common.concurrent.NamedRunnable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 协程版的任务消费者
 *
 * <AUTHOR>
 */
@Deprecated
public class GeminiFiberExecutor implements IGeminiExecutor {
    private static final Logger LOGGER = LogManager.getLogger(GeminiFiberExecutor.class);

    /**
     * 实际的运行器
     */
    private final ThreadPoolExecutor executor;
    private final String name;

    public GeminiFiberExecutor(Object key, IGeminiExecutor fatherExecutor, int taskSize) {
        LOGGER.debug("create GeminiFiberExecutor key:{} from {}", key, fatherExecutor);
        this.name = fatherExecutor.getName() + "-" + key;
        this.executor = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS,
                new MpscBlockingConsumerArrayQueue<>(taskSize),
                Thread.ofVirtual().name(name).factory(),
                (r, executor) -> LOGGER.error("executor {} reject  {}", executor, r)
        );
        this.execute(new NamedRunnable("init_fiber", () -> {
            LOGGER.info("init_fiber new fiber :{} ", Thread.currentThread());
        }));
    }

    @Override
    public String toString() {
        return "GeminiFiberExecutor{" +
                "name=" + name +
                '}';
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void shutdown() {
        this.executor.shutdown();
    }

    @Override
    public void execute(@NotNull Runnable runnable) {
        executor.execute(runnable);
    }
}

package com.yorha.common.resource.resservice.clan;

import com.google.common.collect.Sets;

import java.util.Set;

public enum ClanPrivilegeRole {
    /**
     * 最高权限，盟主
     */
    CLAN_ROOT_ROLE(5),
    /**
     * 高权限，特权阶级
     */
    CLAN_HIGH_PRIVILEGE_ROLE(4),
    /**
     * 低权限者
     */
    CLAN_LOW_PRIVILEGE_THREE_ROLE(3),
    CLAN_LOW_PRIVILEGE_TWO_ROLE(2),
    CLAN_LOW_PRIVILEGE_ONE_ROLE(1);

    /**
     * 低权限集合
     */
    static final Set<Integer> LEVEL_ROLE_LOW_PRIVILEGE = Sets.newHashSet(
            CLAN_LOW_PRIVILEGE_ONE_ROLE.roleId,
            CLAN_LOW_PRIVILEGE_TWO_ROLE.roleId,
            CLAN_LOW_PRIVILEGE_THREE_ROLE.roleId
    );
    /**
     * 中权限集合
     */
    static final Set<Integer> LEVEL_ROLE_MIDDLE_PRIVILEGE = Sets.newHashSet(CLAN_HIGH_PRIVILEGE_ROLE.roleId);
    /**
     * 高权限集合
     */
    static final Set<Integer> LEVEL_ROLE_HIGH_PRIVILEGE = Sets.newHashSet(CLAN_ROOT_ROLE.roleId, CLAN_HIGH_PRIVILEGE_ROLE.roleId);

    final int roleId;

    ClanPrivilegeRole(int roleId) {
        this.roleId = roleId;
    }

    /**
     * 是否是特权阶级角色
     *
     * @param roleId 角色id
     * @return 是 or 否
     */
    public static boolean isHighRole(int roleId) {
        return LEVEL_ROLE_HIGH_PRIVILEGE.contains(roleId);
    }

    public static boolean isMiddleRole(int roleId) {
        return LEVEL_ROLE_MIDDLE_PRIVILEGE.contains(roleId);
    }

    public static boolean isLowRole(int roleId) {
        return LEVEL_ROLE_LOW_PRIVILEGE.contains(roleId);
    }

    /**
     * 返回角色对应的等级
     *
     * @return 等级
     */
    public int getLevel() {
        return this.roleId;
    }

    /**
     * 是否是联盟所有者的角色
     *
     * @param role 角色id
     * @return 是 or 否
     */
    public static boolean isClanOwner(int role) {
        return role == CLAN_ROOT_ROLE.roleId;
    }

    /**
     * 是否是等级角色
     *
     * @param role 角色id
     * @return 是 or 否
     */
    public static boolean isClanRole(int role) {
        return LEVEL_ROLE_LOW_PRIVILEGE.contains(role) || LEVEL_ROLE_HIGH_PRIVILEGE.contains(role);
    }

    /**
     * 角色的数量是否靠谱
     *
     * @param role 角色id
     * @param cnt  角色对应数量
     * @return 是 or 否
     */
    static boolean isCntSuitable(int role, int cnt) {
        return isClanRole(role) && cnt >= 0;
    }
}

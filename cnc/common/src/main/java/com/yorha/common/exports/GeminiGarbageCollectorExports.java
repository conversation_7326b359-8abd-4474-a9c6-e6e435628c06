package com.yorha.common.exports;

import com.google.common.collect.Lists;
import io.prometheus.client.Collector;
import io.prometheus.client.Predicate;
import io.prometheus.client.SummaryMetricFamily;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.List;

/**
 * 从io.prometheus.client.hotspot.GarbageCollectorExports改造。
 *
 * <AUTHOR>
 */
public class GeminiGarbageCollectorExports extends Collector {

    private static final String JVM_GC_COLLECTION_SECONDS = "jvm_gc_collection_seconds";

    private final List<GarbageCollectorMXBean> garbageCollectors;
    private final String busId;

    public GeminiGarbageCollectorExports(final String busId) {
        this(busId, ManagementFactory.getGarbageCollectorMXBeans());
    }

    GeminiGarbageCollectorExports(final String busId, List<GarbageCollectorMXBean> garbageCollectors) {
        this.busId = busId;
        this.garbageCollectors = garbageCollectors;
    }

    @Override
    public List<MetricFamilySamples> collect() {
        return collect(null);
    }

    @Override
    public List<MetricFamilySamples> collect(Predicate<String> nameFilter) {
        List<MetricFamilySamples> mfs = new ArrayList<>();
        if (nameFilter == null || nameFilter.test(JVM_GC_COLLECTION_SECONDS)) {
            final SummaryMetricFamily gcCollection = new SummaryMetricFamily(
                    JVM_GC_COLLECTION_SECONDS,
                    "Time spent in a given JVM garbage collector in seconds.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC, GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID)
            );

            for (final GarbageCollectorMXBean gc : garbageCollectors) {
                gcCollection.addMetric(
                        Lists.newArrayList(gc.getName(), busId),
                        gc.getCollectionCount(),
                        gc.getCollectionTime() / MILLISECONDS_PER_SECOND
                );
            }
            mfs.add(gcCollection);
        }
        return mfs;
    }
}

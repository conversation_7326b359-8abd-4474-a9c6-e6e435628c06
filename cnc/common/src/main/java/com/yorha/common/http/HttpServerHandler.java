/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package com.yorha.common.http;

import com.google.common.collect.Maps;
import com.yorha.common.utils.ChannelHelper;
import com.yorha.common.wechatlog.WechatLog;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import static io.netty.handler.codec.http.HttpResponseStatus.OK;
import static io.netty.handler.codec.http.HttpVersion.HTTP_1_1;

/**
 * <AUTHOR> Jiang
 */
@ChannelHandler.Sharable
public class HttpServerHandler extends SimpleChannelInboundHandler<HttpObject> {
    private static final Logger LOGGER = LogManager.getLogger(HttpServerHandler.class);
    /**
     * 是否提供服务。
     */
    private boolean isSwitchOn = false;
    /**
     * http url分发映射。
     */
    private final Map<String, ReqHandler> urlHandler = Maps.newHashMap();
    /**
     * 读写锁。
     */
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();

    public static HttpServerHandler getInstance() {
        return HttpServerHandler.LazyHolder.INSTANCE;
    }

    private static class LazyHolder {
        private static final HttpServerHandler INSTANCE = new HttpServerHandler();
    }

    public HttpServerHandler() {
        LOGGER.info("create HttpServerHandler");
    }

    public <T extends ReqHandler> void registerUrlHandler(T handler) {
        ReentrantReadWriteLock.WriteLock writeLock = this.rwLock.writeLock();
        writeLock.lock();
        try {
            urlHandler.put(handler.getUrl(), handler);
            if ("/cmd".equals(handler.getUrl())) {
                urlHandler.put("/getdata", handler);
            }
        } finally {
            writeLock.unlock();
        }
        LOGGER.info("http register. {}: {}", handler.getUrl(), handler.getClass());
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        LOGGER.debug("{} channelActive", ctx.channel());
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        LOGGER.debug("{} channelInactive  ", ctx.channel());
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, HttpObject msg) {
        Channel ch = ctx.channel();
        if (msg instanceof HttpRequest) {
            FullHttpRequest req = (FullHttpRequest) msg;
            if (HttpUtil.is100ContinueExpected(req)) {
                send100Continue(ctx);
            }
            FullHttpResponse resp = new DefaultFullHttpResponse(HTTP_1_1, OK);
            try {
                handleQeq(ch, req, resp);
            } catch (Throwable e) {
                WechatLog.error("channelRead0 exception.", e);
            }
        }
    }

    protected void handleQeq(Channel channel, FullHttpRequest req, FullHttpResponse resp) {
        LOGGER.debug("http req uri={}", req.uri());
        final String key = req.uri().split("[?]")[0];
        final ReqHandler handler;
        final ReentrantReadWriteLock.ReadLock readLock = this.rwLock.readLock();
        readLock.lock();
        try {
            if (!this.isSwitchOn) {
                resp.content().writeBytes("idip is switched off".getBytes(StandardCharsets.UTF_8));
                return;
            }
            handler = urlHandler.get(key);
        } finally {
            readLock.unlock();
        }
        if (handler == null) {
            resp.content().writeBytes("cant find processor".getBytes());
            return;
        }
        handler.processReqAndSendRes(channel, req, resp);
    }

    protected static void send100Continue(ChannelHandlerContext ctx) {
        FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.CONTINUE);
        ctx.write(response);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable err) {
        try {
            if (err instanceof IOException) {
                LOGGER.info("{} io change: {}", ctx.channel(), err.getMessage());
            } else {
                WechatLog.error("{} exceptionCaught: {}", ctx.channel(), err);
            }
        } finally {
            if (ctx.channel().isActive()) {
                ChannelHelper.closeChannel(ctx.channel(), "HttpServerHandler exception caught");
            }
        }
    }

    public void switchOn() {
        ReentrantReadWriteLock.WriteLock writeLock = this.rwLock.writeLock();
        writeLock.lock();
        try {
            this.isSwitchOn = true;
        } finally {
            writeLock.unlock();
        }
    }

    public void switchOff() {
        ReentrantReadWriteLock.WriteLock writeLock = this.rwLock.writeLock();
        writeLock.lock();
        try {
            this.isSwitchOn = false;
        } finally {
            writeLock.unlock();
        }
    }
}

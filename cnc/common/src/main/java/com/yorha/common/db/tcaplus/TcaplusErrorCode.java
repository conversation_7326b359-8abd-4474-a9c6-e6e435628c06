package com.yorha.common.db.tcaplus;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum TcaplusErrorCode {
    MAX_MODULE_NUM(0x1a),
    MAX_POSITIVE_ERR_CODE_NUM_PER_MODULE(0x10),
    MAX_NEGATIVE_ERR_CODE_NUM_PER_MODULE(0x100),
    GEN_ERR_SUC(0x00000000),
    GEN_ERR_ERR(-0x00000100),
    GEN_ERR_INVALID_ARGUMENTS(-0x00000101),
    GEN_ERR_UNSUPPORT_OPERATION(-0x00000102),
    GEN_ERR_NOT_ENOUGH_MEMORY(-0x00000103),
    GEN_ERR_ECMGR_INVALID_MODULE_ID(-0x00000200),
    GEN_ERR_ECMGR_INVALID_ERROR_CODE(-0x00000300),
    GEN_ERR_ECMGR_NULL_ERROR_STRING(-0x00000400),
    GEN_ERR_ECMGR_DUPLICATED_ERROR_CODE(-0x00000500),
    GEN_ERR_TXLOG_NULL_POINTER_FROM_TSD(-0x00000600),
    GEN_ERR_TABLE_READONLY(-0x00000700),
    GEN_ERR_TABLE_READ_DELETE(-0x00000800),
    GEN_ERR_ACCESS_DENIED(-0x00000900),
    LOC_ERR__0x00000102(-0x00000102),
    LOC_ERR__0x00000202(-0x00000202),
    LOC_ERR__0x00000302(-0x00000302),
    LOC_ERR__0x00000402(-0x00000402),
    LOC_ERR__0x00000502(-0x00000502),
    LOC_ERR__0x00000602(-0x00000602),
    LOC_ERR__0x00000702(-0x00000702),
    LOC_ERR__0x00000802(-0x00000802),
    LOC_ERR__0x00000902(-0x00000902),
    LOC_ERR__0x00000A02(-0x00000A02),
    LOC_ERR__0x00000B02(-0x00000B02),
    LOC_ERR__0x00000C02(-0x00000C02),
    LOC_ERR__0x00000D02(-0x00000D02),
    LOC_ERR__0x00000E02(-0x00000E02),
    LOC_ERR__0x00000F02(-0x00000F02),
    LOC_ERR__0x00001002(-0x00001002),
    LOC_ERR__0x00001102(-0x00001102),
    LOC_ERR__0x00001202(-0x00001202),
    LOC_ERR__0x00001302(-0x00001302),
    LOC_ERR__0x00001402(-0x00001402),
    LOC_ERR__0x00001502(-0x00001502),
    LOC_ERR__0x00001602(-0x00001602),
    LOC_ERR__0x00001702(-0x00001702),
    LOC_ERR__0x00001802(-0x00001802),
    LOC_ERR__0x00001902(-0x00001902),
    LOC_ERR__0x00001A02(-0x00001A02),
    LOC_ERR__0x00001B02(-0x00001B02),
    LOC_ERR__0x00001C02(-0x00001C02),
    LOC_ERR__0x00001D02(-0x00001D02),
    LOC_ERR__0x00001E02(-0x00001E02),
    LOC_ERR__0x00001F02(-0x00001F02),
    LOC_ERR__0x00002002(-0x00002002),
    LOC_ERR__0x00002802(-0x00002802),
    LOC_ERR__0x00003002(-0x00003002),
    LOC_ERR__0x00003802(-0x00003802),
    LOC_ERR__0x00004002(-0x00004002),
    LOC_ERR__0x00004802(-0x00004802),
    LOC_ERR__0x00005002(-0x00005002),
    LOC_ERR__0x00005802(-0x00005802),
    LOC_ERR__0x00006002(-0x00006002),
    LOC_ERR__0x00006802(-0x00006802),
    LOC_ERR__0x00007002(-0x00007002),
    LOC_ERR__0x00007802(-0x00007802),
    LOC_ERR__0x00008002(-0x00008002),
    LOC_ERR__0x00008802(-0x00008802),
    LOC_ERR__0x00009002(-0x00009002),
    LOC_ERR__0x00009802(-0x00009802),
    LOC_ERR__0x0000A002(-0x0000A002),
    LOC_ERR__0x0000A802(-0x0000A802),
    LOC_ERR__0x0000B002(-0x0000B002),
    LOC_ERR__0x0000B802(-0x0000B802),
    LOC_ERR__0x0000C002(-0x0000C002),
    LOC_ERR__0x0000C802(-0x0000C802),
    LOC_ERR__0x0000FF02(-0x0000FF02),
    TXHDB_ERR_RECORD_NOT_EXIST(0x00000105),
    TXHDB_ERR_ITERATION_NO_MORE_RECORDS(0x00000205),
    TXHDB_ERR_MUTEX_TRYLOCK_BUSY(0x00000305),
    TXHDB_ERR_MUTEX_TIMEDLOCK_TIMEOUT(0x00000405),
    TXHDB_ERR_RWLOCK_TRYWRLOCK_BUSY(0x00000505),
    TXHDB_ERR_RWLOCK_TRYRDLOCK_BUSY(0x00000605),
    TXHDB_ERR_SPIN_TRYLOCK_BUSY(0x00000705),
    TXHDB_ERR_ITERATION_EXCEED_MAX_ALLOWED_TIME_OF_ONE_ITER(0x00000805),
    TXHDB_ERR_INVALID_ARGUMENTS(-0x00000105),
    TXHDB_ERR_INVALID_MEMBER_VARIABLE_VALUE(-0x00000205),
    TXHDB_ERR_ALREADY_OPEN(-0x00000305),
    TXHDB_ERR_MUTEX_LOCK_FAIL(-0x00000405),
    TXHDB_ERR_MUTEX_TRYLOCK_FAIL(-0x00000505),
    TXHDB_ERR_MUTEX_TIMEDLOCK_FAIL(-0x00000605),
    TXHDB_ERR_MUTEX_UNLOCK_FAIL(-0x00000705),
    TXHDB_ERR_RWLOCK_WRLOCK_FAIL(-0x00000805),
    TXHDB_ERR_RWLOCK_TRYWRLOCK_FAIL(-0x00000905),
    TXHDB_ERR_RWLOCK_RDLOCK_FAIL(-0x00000a05),
    TXHDB_ERR_RWLOCK_TRYRDLOCK_FAIL(-0x00000b05),
    TXHDB_ERR_RWLOCK_UNLOCK_FAIL(-0x00000c05),
    TXHDB_ERR_SPIN_LOCK_FAIL(-0x00000d05),
    TXHDB_ERR_SPIN_UNLOCK_FAIL(-0x00000e05),
    TXHDB_ERR_FILE_EXISTS_BUT_STATUS_ERROR(-0x00000f05),
    TXHDB_ERR_FILE_OPEN_FAIL(-0x00001005),
    TXHDB_ERR_FILE_READ_SIZE_INVALID(-0x00001105),
    TXHDB_ERR_FILE_INVALID_FILE_PATH(-0x00001205),
    TXHDB_ERR_FILE_LOCK_FILE_FAIL(-0x00001305),
    TXHDB_ERR_FILE_NOT_A_REGULAR_FILE(-0x00001405),
    TXHDB_ERR_FILE_MMAP_FAIL(-0x00001505),
    TXHDB_ERR_FILE_MUNMAP_FAIL(-0x00001605),
    TXHDB_ERR_FILE_CLOSE_FAIL(-0x00001705),
    TXHDB_ERR_FILE_SPACE_NOT_ENOUGH_IN_HEAD(-0x00001805),
    TXHDB_ERR_FILE_FTRUNCATE_FAIL(-0x00001905),
    TXHDB_ERR_FILE_INCONSISTANT_FILE_SIZE(-0x00001a05),
    TXHDB_ERR_FILE_MSIZ_LESSER_THAN_TXHDB_WHOLE_REC_OFFSET(-0x00001b05),
    TXHDB_ERR_FILE_MSIZ_CHANGE_NOT_PERMIT(-0x00001c05),
    TXHDB_ERR_FILE_FSTAT_FAIL(-0x00001d05),
    TXHDB_ERR_FILE_MSYNC_FAIL(-0x00001e05),
    TXHDB_ERR_FILE_FSYNC_FAIL(-0x00001f05),
    TXHDB_ERR_FILE_FCNTL_LOCK_FILE_FAIL(-0x00002005),
    TXHDB_ERR_FILE_FCNTL_UNLOCK_FILE_FAIL(-0x00002105),
    TXHDB_ERR_FILE_PREAD_FAIL_WITH_SPECIFIED_ERRNO(-0x00002205),
    TXHDB_ERR_FILE_PREAD_FAIL_WITH_UNSPECIFIED_ERRNO(-0x00002305),
    TXHDB_ERR_FILE_PWRITE_FAIL_WITH_SPECIFIED_ERRNO(-0x00002405),
    TXHDB_ERR_FILE_PWRITE_FAIL_WITH_UNSPECIFIED_ERRNO(-0x00002505),
    TXHDB_ERR_FILE_READ_EXCEED_FILE_BOUNDARY(-0x00002605),
    TXHDB_ERR_FILE_READ_FAIL_DURING_COPY(-0x00002705),
    TXHDB_ERR_FILE_WRITE_FAIL_DURING_COPY(-0x00002805),
    TXHDB_ERR_FILE_INVALID_FREE_BLOCK_POOL_METADATA(-0x00002905),
    TXHDB_ERR_FILE_INVALID_MAGIC(-0x00002a05),
    TXHDB_ERR_FILE_INVALID_LIBRARY_VERSION(-0x00002b05),
    TXHDB_ERR_FILE_INVALID_LIBRARY_REVISION(-0x00002c05),
    TXHDB_ERR_FILE_INVALID_FORMAT_VERSION(-0x00002d05),
    TXHDB_ERR_FILE_INVALID_EXTDATA_FORMAT_VERSION(-0x00002e05),
    TXHDB_ERR_FILE_INVALID_DBTYPE(-0x00002f05),
    TXHDB_ERR_FILE_HEAD_CRC_UNMATCH(-0x00003005),
    TXHDB_ERR_FILE_INVALID_METADATA(-0x00003105),
    TXHDB_ERR_FILE_INVALID_HEADLEN(-0x00003205),
    TXHDB_ERR_FILE_DESERIAL_HEAD_SPACE_NOT_ENOUGH(-0x00003305),
    TXHDB_ERR_FILE_SERIAL_HEAD_SPACE_NOT_ENOUGH(-0x00003405),
    TXHDB_ERR_FILE_DESERIAL_STAT_SPACE_NOT_ENOUGH(-0x00003505),
    TXHDB_ERR_FILE_SERIAL_STAT_SPACE_NOT_ENOUGH(-0x00003605),
    TXHDB_ERR_FILE_SERIAL_FREE_BLOCK_LIST_INFO_WRONG_BUFFLEN(-0x00003705),
    TXHDB_ERR_FILE_IN_EXCEPTIONAL_STATUS(-0x00003805),
    TXHDB_ERR_DB_NOT_OPENED(-0x00003905),
    TXHDB_ERR_DB_WRITE_NOT_PERMIT(-0x00003a05),
    TXHDB_ERR_INVALID_OFFSET_FROM_BUCKET(-0x00003b05),
    TXHDB_ERR_READ_EXTDATA_EXCEED_BUFF_LENGTH(-0x00003c05),
    TXHDB_ERR_WRITE_EXTDATA_EXCEED_BUFF_LENGTH(-0x00003d05),
    TXHDB_ERR_FREE_BLOCK_IS_READ_WHEN_GETTING_RECORD(-0x00003e05),
    TXHDB_ERR_INVALID_KEY_DATABLOCK_NUM(-0x00003f05),
    TXHDB_ERR_INVALID_VALUE_DATABLOCK_NUM(-0x00004005),
    TXHDB_ERR_GET_RECORD_EXCEED_BUFF_LENGTH(-0x00004105),
    TXHDB_ERR_COMPRESSION_FAIL(-0x00004205),
    TXHDB_ERR_DECOMPRESSION_FAIL(-0x00004305),
    TXHDB_ERR_INVALID_OFFSETINEXTDATA_AND_SIZE_WHEN_UPDATING_EXTDATA(-0x00004405),
    TXHDB_ERR_UNEXPECTED_FREEBLOCK(-0x00004505),
    TXHDB_ERR_VALUE_APOW_LESSER_THAN_KEY_APOW(-0x00004605),
    TXHDB_ERR_DUPLICATED_FILE_PATH(-0x00004705),
    TXHDB_ERR_INVALID_KEY_HEAD_SIZE_IN_TXHDB_META(-0x00004805),
    TXHDB_ERR_INVALID_FILE_SIZE(-0x00004905),
    TXHDB_ERR_INVALID_FREE_BLOCK_SIZE(-0x00004a05),
    TXHDB_ERR_MMAP_MEMSIZE_CHANGE_NOT_PERMITTED(-0x00004b05),
    TXHDB_ERR_NEW_FILE_OBJ_FAIL(-0x00004c05),
    TXHDB_ERR_RECORD_KEY_OFFSET_LESSER_THAN_TXHDB_WHOLE_REC_OFFSET(-0x00004d05),
    TXHDB_ERR_RECORD_VALUE_OFFSET_LESSER_THAN_TXHDB_WHOLE_REC_OFFSET(-0x00004e05),
    TXHDB_ERR_RECORD_OFFSET_LESSER_THAN_TXHDB_WHOLE_REC_OFFSET(-0x00004f05),
    TXHDB_ERR_KEY_BUFFSIZE_LESSER_THAN_KEY_HEADSIZE(-0x00005005),
    TXHDB_ERR_VALUE_BUFFSIZE_LESSER_THAN_VALUE_HEADSIZE(-0x00005105),
    TXHDB_ERR_RECORD_SIZE_LESSER_THAN_KEY_HEADSIZE(-0x00005205),
    TXHDB_ERR_INVALID_BLOCK_MAGIC(-0x00005305),
    TXHDB_ERR_INVALID_FREE_BLOCK_MAGIC(-0x00005405),
    TXHDB_ERR_INVALID_KEYMAGIC(-0x00005505),
    TXHDB_ERR_INVALID_KEYSPLMAGIC(-0x00005605),
    TXHDB_ERR_INVALID_VALMAGIC(-0x00005705),
    TXHDB_ERR_INVALID_VALSPLMAGIC(-0x00005805),
    TXHDB_ERR_UNSUPPORTED_KEY_FORMAT_VERSION(-0x00005905),
    TXHDB_ERR_UNSUPPORTED_KEY_SPLBLOCK_FORMAT_VERSION(-0x00005a05),
    TXHDB_ERR_UNSUPPORTED_VALUE_FORMAT_VERSION(-0x00005b05),
    TXHDB_ERR_UNSUPPORTED_VALUE_SPLBLOCK_FORMAT_VERSION(-0x00005c05),
    TXHDB_ERR_UNSUPPORTED_FREE_BLOCK_FORMAT_VERSION(-0x00005d05),
    TXHDB_ERR_KEY_HEAD_CRC_UNMATCH(-0x00005e05),
    TXHDB_ERR_KEY_SPLBLOCK_HEAD_CRC_UNMATCH(-0x00005f05),
    TXHDB_ERR_VALUE_HEAD_CRC_UNMATCH(-0x00006005),
    TXHDB_ERR_VALUE_SPLBLOCK_HEAD_CRC_UNMATCH(-0x00006105),
    TXHDB_ERR_FREE_BLOCK_HEAD_CRC_UNMATCH(-0x00006205),
    TXHDB_ERR_FREE_BLOCK_LIST_INFO_CRC_UNMATCH(-0x00006305),
    TXHDB_ERR_GET_KEY_READ_BUFFER_FAIL(-0x00006405),
    TXHDB_ERR_GET_VALUE_READ_BUFFER_FAIL(-0x00006505),
    TXHDB_ERR_GET_LRU_VALUE_BUFFER_FAIL(-0x00006605),
    TXHDB_ERR_GET_EXTDATA_READ_BUFFER_FAIL(-0x00006705),
    TXHDB_ERR_KEY_BLOCK_BODYSIZE_GREATER_THAN_KEY_BODYSIZE(-0x00006805),
    TXHDB_ERR_VALUE_BLOCK_BODYSIZE_GREATER_THAN_VALUE_BODYSIZE(-0x00006905),
    TXHDB_ERR_NULL_RECORD_POINTER(-0x00006a05),
    TXHDB_ERR_NULL_RECORD_WRITE_BUFF(-0x00006b05),
    TXHDB_ERR_SERIALIZE_RECORD_KEY_HEAD(-0x00006c05),
    TXHDB_ERR_INVALID_IDX_IN_STAT_NUMS_ARRAY(-0x00006d05),
    TXHDB_ERR_INVALID_ELEMNUM_OF_STAT_KEYNUMS(-0x00006e05),
    TXHDB_ERR_INVALID_ELEMNUM_OF_STAT_VALNUMS(-0x00006f05),
    TXHDB_ERR_PRINT_SPACE_NOT_ENOUGH(-0x00007005),
    TXHDB_ERR_LRU_SHIFTIN_NOT_ENOUGH_MEMORY(-0x00007105),
    TXHDB_ERR_LRU_SHIFTIN_NO_MORE_LRU_NODE(-0x00007205),
    TXHDB_ERR_LRU_ADJUST_NO_MORE_LRU_NODE(-0x00007305),
    TXHDB_ERR_LRU_SHIFTOUT_RECORD_ALREADY_OUTSIDE_OF_MEMORY(-0x00007405),
    TXHDB_ERR_FILE_EXTDATA_LENGTH_CRC_UNMATCH(-0x00007505),
    TXHDB_ERR_FILE_EXTDATA_INVALID_LENGTH(-0x00007605),
    TXHDB_ERR_INVALID_VALUE_HEAD_SIZE_IN_TXHDB_META(-0x00007705),
    TXHDB_ERR_INVALID_SPLITDATABLOCK_HEAD_SIZE_IN_TXHDB_META(-0x00007805),
    TXHDB_ERR_KEY_BUCKETIDX_UNMATCH(-0x00007905),
    TXHDB_ERR_FILE_WRITE_SIZE_INVALID(-0x00007a05),
    TXHDB_ERR_MODIFY_STAT_UNSUPPORTED_OPERATION_TYPE(-0x00007b05),
    TXHDB_ERR_INVALID_EXTDATAMAGIC(-0x00007c05),
    TXHDB_ERR_INVALID_INTERNAL_LIST_TAIL_DURING_POP_LRU_NODELIST(-0x00007d05),
    TXHDB_ERR_GET_LRUNODE_FAIL(-0x00007e05),
    TXHDB_ERR_LRUNODE_INVALID_FLAG(-0x00007f05),
    TXHDB_ERR_INVALID_FREE_BLOCK_NUM_TOO_MANY_FREE_BLOCKS(-0x00008005),
    TXHDB_ERR_INVALID_ELEMNUM_OF_STAT_NOPADDING_SIZE_KEYNUMS(-0x00008105),
    TXHDB_ERR_INVALID_ELEMNUM_OF_STAT_NOPADDING_SIZE_VALNUMS(-0x00008205),
    TXHDB_ERR_ADD_LSIZE_EXCEEDS_MAX_TSD_VALUE_BUFF_SIZE(-0x00008305),
    TXHDB_ERR_INTERNAL_CONSTANTS_ILLEGAL(-0x00008405),
    TXHDB_ERR_TOO_BIG_KEY_BIZ_SIZE(-0x00008505),
    TXHDB_ERR_TOO_BIG_VALUE_BIZ_SIZE(-0x00008605),
    TXHDB_ERR_INDEX_NO_EXIST(-0x00008705),
    TXHDB_ERR_INVALID_FREE_BLOCK_BASESIZE(-0x00008805),
    TXHDB_ERR_CANNOT_CREATE_MMAPSHM_BECAUSE_SHM_ALREADY_EXISTED(-0x00008905),
    TXHDB_ERR_INVALID_GENSHM_KEY(-0x00008a05),
    TXHDB_ERR_GENSHM_GET_FAIL(-0x00008b05),
    TXHDB_ERR_GENSHM_CREATE_FAIL(-0x00008c05),
    TXHDB_ERR_GENSHM_STAT_FAIL(-0x00008d05),
    TXHDB_ERR_GENSHM_DOES_NOT_EXIST(-0x00008e05),
    TXHDB_ERR_GENSHM_ATTACH_FAIL_BECAUSE_IT_IS_ALREADY_ATTACHED_BY_OTHER_PROCESSES(-0x00008f05),
    TXHDB_ERR_GENSHM_ATTACH_FAIL(-0x00009005),
    TXHDB_ERR_FILE_INCONSISTANT_MSIZE(-0x00009105),
    TXHDB_ERR_INVALID_TCAP_GENSHM_MAGIC(-0x00009205),
    TXHDB_ERR_GENSHM_FIXED_HEAD_BUFFLEN_UNMATCH(-0x00009305),
    TXHDB_ERR_GENSHM_INVALID_HEADLEN(-0x00009405),
    TXHDB_ERR_GENSHM_HEAD_CRC_UNMATCH(-0x00009505),
    TXHDB_ERR_GENSHM_HEAD_INVALID_VERSION(-0x00009605),
    TXHDB_ERR_GENSHM_INVALID_FILETYPE(-0x00009705),
    TXHDB_ERR_GET_IPV4ADDR_FAIL(-0x00009805),
    TXHDB_ERR_NO_VALID_IPV4ADDR_EXISTS(-0x00009905),
    TXHDB_ERR_TRANSFER_IPV4ADDR_FAIL(-0x00009a05),
    TXHDB_ERR_FILE_EXCEEDS_LSIZE_LIMIT(-0x00009b05),
    TXHDB_ERR_GENSHM_DETACH_FAIL(-0x00009c05),
    TXHDB_ERR_TXHDB_HEAD_PARAMETERS_ERROR(-0x00009d05),
    TXHDB_ERR_TXHDB_HEAD_OLD_VERSION(-0x00009e05),
    TXHDB_ERR_TXHDB_SHM_COREINFO_UNMATCH(-0x00009f05),
    TXHDB_ERR_TXHDB_SHM_EXTDATA_UNMATCH(-0x0000a005),
    TXHDB_ERR_TXHDB_EXTDATA_CHECK_ERROR(-0x0000a105),
    TXHDB_ERR_CHUNK_BUFFS_CANNOT_BE_ALLOCED_IF_THEY_ARE_NOT_RELEASED(-0x0000a205),
    TXHDB_ERR_ALLOCATE_MEMORY_FAIL(-0x0000a305),
    TXHDB_ERR_INVALID_CHUNK_RW_MANNER(-0x0000a405),
    TXHDB_ERR_FILE_PREAD_NOT_COMPLETE(-0x0000a505),
    TXHDB_ERR_FILE_PWRITE_NOT_COMPLETE(-0x0000a605),
    TXHDB_ERR_KEY_ONEBLOCK_BUT_NEXT_NOTNULL(-0x0000a705),
    TXHDB_ERR_VALUE_ONEBLOCK_BUT_NEXT_NOTNULL(-0x0000a805),
    TXHDB_ERR_VARINT_FORMAT_ERROR(-0x0000a905),
    TXHDB_ERR_TXSTAT_ERROR(-0x0000aa05),
    ENG_ERR_INVALID_ARGUMENTS(-0x00000107),
    ENG_ERR_INVALID_MEMBER_VARIABLE_VALUE(-0x00000207),
    ENG_ERR_NEW_TXHCURSOR_FAILED(-0x00000307),
    ENG_ERR_TXHCURSOR_KEY_BUFFER_LEGHTH_NOT_ENOUGH(-0x00000407),
    ENG_ERR_TXHCURSOR_VALUE_BUFFER_LEGHTH_NOT_ENOUGH(-0x00000507),
    ENG_ERR_TXHDB_FILEPATH_NULL(-0x00000607),
    ENG_ERR_TCHDB_RELATED_ERROR(-0x00000707),
    ENG_ERR_NULL_CACHE(-0x00000807),
    ENG_ERR_ITER_FAIL_SYSTEM_RECORD(-0x00000907),
    ENG_ERR_SYSTEM_ERROR(-0x00000a07),
    ENG_ERR_ENGINE_ERROR(-0x00000b07),
    ENG_ERR_DATA_ERROR(-0x00000c07),
    ENG_ERR_VERSION_ERROR(-0x00000d07),
    ENG_ERR_SYSTEM_ERROR_BUFF_OVERFLOW(-0x00000e07),
    ENG_ERR_METADATA_ERROR(-0x00000f07),
    ENG_ERR_ADD_KEYMETA_FAILED(-0x00001007),
    ENG_ERR_ADD_VALUEMETA_FAILED(-0x00001107),
    ENG_ERR_RESERVED_FIELDNAME(-0x00001207),
    ENG_ERR_KEYNAME_REPEAT(-0x00001307),
    ENG_ERR_VALUENAME_REPEAT(-0x00001407),
    ENG_ERR_MISS_KEYMETA(-0x00001507),
    ENG_ERR_DELETE_KEYFIELD(-0x00001607),
    ENG_ERR_CHANGE_KEYCOUNT(-0x00001707),
    ENG_ERR_CHANGE_KEYTYPE(-0x00001807),
    ENG_ERR_CHANGE_KEYLENGTH(-0x00001907),
    ENG_ERR_CHANGE_VALUETYPE(-0x00001a07),
    ENG_ERR_CHANGE_VALUELENGTH(-0x00001b07),
    ENG_ERR_CHANGE_DEFAULTVALUE(-0x00001c07),
    ENG_ERR_EMPTY_FIELDNAME(-0x00001d07),
    ENG_ERR_INVALID_TARGET_KEYFIELD(-0x00001e07),
    ENG_ERR_INVALID_TARGET_VALUEFIELD(-0x00001f07),
    ENG_ERR_INVALID_TABLE_TYPE(-0x00002007),
    ENG_ERR_CHANGE_TABLE_TYPE(-0x00002107),
    ENG_ERR_MISS_VALUEMETA(-0x00002207),
    ENG_ERR_NOT_ENOUGH_BUFF_FOR_FILEPATH(-0x00002307),
    ENG_ERR_ENGINE_FILE_NOT_FOUND(-0x00002407),
    ULOG_ERR_INVALID_PARAMS(-0x00000109),
    SYNCDB_ERR_INVALID_PARAMS(-0x0000010b),
    SVR_ERR_FAIL_ROUTE(-0x0000010d),
    SVR_ERR_FAIL_TIMEOUT(-0x0000020d),
    SVR_ERR_FAIL_SHORT_BUFF(-0x0000030d),
    SVR_ERR_FAIL_SYSTEM_BUSY(-0x0000040d),
    SVR_ERR_FAIL_RECORD_EXIST(-0x0000050d),
    SVR_ERR_FAIL_INVALID_FIELD_NAME(-0x0000060d),
    SVR_ERR_FAIL_VALUE_OVER_MAX_LEN(-0x0000070d),
    SVR_ERR_FAIL_INVALID_FIELD_TYPE(-0x0000080d),
    SVR_ERR_FAIL_SYNC_WRITE(-0x0000090d),
    SVR_ERR_FAIL_WRITE_RECORD(-0x00000a0d),
    SVR_ERR_FAIL_DELETE_RECORD(-0x00000b0d),
    SVR_ERR_FAIL_DATA_ENGINE(-0x00000c0d),
    SVR_ERR_FAIL_RESULT_OVERFLOW(-0x00000d0d),
    SVR_ERR_FAIL_INVALID_OPERATION(-0x00000e0d),
    SVR_ERR_FAIL_INVALID_SUBSCRIPT(-0x00000f0d),
    SVR_ERR_FAIL_INVALID_INDEX(-0x0000100d),
    SVR_ERR_FAIL_OVER_MAXE_FIELD_NUM(-0x0000110d),
    SVR_ERR_FAIL_MISS_KEY_FIELD(-0x0000120d),
    SVR_ERR_FAIL_NEED_SIGNUP(-0x0000130d),
    SVR_ERR_FAIL_CROSS_AUTH(-0x0000140d),
    SVR_ERR_FAIL_SIGNUP_FAIL(-0x0000150d),
    SVR_ERR_FAIL_SIGNUP_INVALID(-0x0000160d),
    SVR_ERR_FAIL_SIGNUP_INIT(-0x0000170d),
    SVR_ERR_FAIL_LIST_FULL(-0x0000180d),
    SVR_ERR_FAIL_LOW_VERSION(-0x0000190d),
    SVR_ERR_FAIL_HIGH_VERSION(-0x00001a0d),
    SVR_ERR_FAIL_INVALID_RESULT_FLAG(-0x00001b0d),
    SVR_ERR_FAIL_PROXY_STOPPING(-0x00001c0d),
    SVR_ERR_FAIL_SVR_READONLY(-0x00001d0d),
    SVR_ERR_FAIL_SVR_READONLY_BECAUSE_IN_SLAVE_MODE(-0x00001e0d),
    SVR_ERR_FAIL_INVALID_VERSION(-0x00001f0d),
    SVR_ERR_FAIL_SYSTEM_ERROR(-0x0000200d),
    SVR_ERR_FAIL_OVERLOAD(-0x0000210d),
    SVR_ERR_FAIL_NOT_ENOUGH_DADADISK_SPACE(-0x0000220d),
    SVR_ERR_FAIL_NOT_ENOUGH_ULOGDISK_SPACE(-0x0000230d),
    SVR_ERR_FAIL_UNSUPPORTED_PROTOCOL_MAGIC(-0x0000240d),
    SVR_ERR_FAIL_UNSUPPORTED_PROTOCOL_CMD(-0x0000250d),
    SVR_ERR_FAIL_HIGH_TABLE_META_VERSION(-0x0000260d),
    SVR_ERR_FAIL_MERGE_VALUE_FIELD(-0x0000270d),
    SVR_ERR_FAIL_CUT_VALUE_FIELD(-0x0000280d),
    SVR_ERR_FAIL_PACK_FIELD(-0x0000290d),
    SVR_ERR_FAIL_UNPACK_FIELD(-0x00002a0d),
    SVR_ERR_FAIL_LOW_API_VERSION(-0x00002b0d),
    SVR_ERR_COMMAND_AND_TABLE_TYPE_IS_MISMATCH(-0x00002c0d),
    SVR_ERR_FAIL_TO_FIND_CACHE(-0x00002d0d),
    SVR_ERR_FAIL_TO_FIND_META(-0x00002e0d),
    SVR_ERR_FAIL_TO_GET_CURSOR(-0x00002f0d),
    SVR_ERR_FAIL_OUT_OF_USER_DEF_RANGE(-0x0000300d),
    SVR_ERR_INVALID_ARGUMENTS(-0x0000310d),
    SVR_ERR_SLAVE_READ_INVALID(-0x0000320d),
    SVR_ERR_NULL_CACHE(-0x0000330d),
    SVR_ERR_NULL_CURSOR(-0x0000340d),
    SVR_ERR_METALIB_VERSION_LESS_THAN_ENTRY_VERSION(-0x0000350d),
    SVR_ERR_INVALID_SELECT_ID_FOR_UNION(-0x0000360d),
    SVR_ERR_CAN_NOT_FIND_SELECT_ENTRY_FOR_UNION(-0x0000370d),
    SVR_ERR_FAIL_DOCUMENT_PACK_VERSION(-0x0000380d),
    SVR_ERR_TCAPSVR_PROCESS_NOT_NORMAL(-0x0000390d),
    SVR_ERR_TBUSD_PROCESS_NOT_NORMAL(-0x00003a0d),
    SVR_ERR_INVALID_ARRAY_COUNT(-0x00003b0d),
    SVR_ERR_REJECT_REQUEST_BECAUSE_ROUTE_IN_REJECT_STATUS(-0x00003c0d),
    SVR_ERR_FAIL_GET_ROUTE_HASH_CODE(-0x00003d0d),
    SVR_ERR_FAIL_INVALID_FIELD_VALUE(-0x00003e0d),
    SVR_ERR_FAIL_PROTOBUF_FIELD_GET(-0x00003f0d),
    SVR_ERR_FAIL_PROTOBUF_VALUE_BUFF_EXCEED(-0x0000400d),
    SVR_ERR_FAIL_PROTOBUF_FIELD_UPDATE(-0x0000410d),
    SVR_ERR_FAIL_PROTOBUF_FIELD_INCREASE(-0x0000420d),
    SVR_ERR_FAIL_PROTOBUF_FIELD_TAG_MISMATCH(-0x0000430d),
    TCAPDB_ERR_INVALID_PARAMS(-0x0000010f),
    PROXY_ERR_INVALID_PARAMS(-0x00000111),
    PROXY_ERR_NO_NEED_ROUTE_BATCHGET_ACTION_MSG_WHEN_NODE_IS_IN_SYNC_STATUS(-0x00000211),
    PROXY_ERR_NO_NEED_ROUTE_WHEN_NODE_IS_IN_REJECT_STATUS(-0x00000311),
    PROXY_ERR_PROBE_TIMEOUT(-0x00000411),
    PROXY_ERR_SYSTEM_ERROR(-0x00000511),
    PROXY_ERR_CONFIG_ERROR(-0x00000611),
    PROXY_ERR_OVER_MAX_NODE(-0x00000711),
    PROXY_ERR_INVALID_SPLIT_SIZE(-0x00000811),
    PROXY_ERR_INVALID_ROUTE_INDEX(-0x00000911),
    PROXY_ERR_CONNECT_SERVER(-0x00000a11),
    PROXY_ERR_COMPOSE_MSG(-0x00000b11),
    PROXY_ERR_ROUTE_MSG(-0x00000c11),
    PROXY_ERR_SHORT_BUFFER(-0x00000d11),
    PROXY_ERR_OVER_MAX_RECORD(-0x00000e11),
    PROXY_ERR_INVALID_SERVICE_TABLE(-0x00000f11),
    PROXY_ERR_REGISTER_FAILED(-0x00001011),
    PROXY_ERR_CREATE_SESSION_HASH(-0x00001111),
    PROXY_ERR_WRONG_STATUS(-0x00001211),
    PROXY_ERR_UNPACK_MSG(-0x00001311),
    PROXY_ERR_PACK_MSG(-0x00001411),
    PROXY_ERR_SEND_MSG(-0x00001511),
    PROXY_ERR_ALLOCATE_MEMORY(-0x00001611),
    PROXY_ERR_PARSE_MSG(-0x00001711),
    PROXY_ERR_INVALID_MSG(-0x00001811),
    PROXY_ERR_FAILED_PROC_REQUEST_BECAUSE_NODE_IS_IN_SYNC_STASUS(-0x00001911),
    PROXY_ERR_KEY_FIELD_NUM_IS_ZERO(-0x00001a11),
    PROXY_ERR_LACK_OF_SOME_KEY_FIELDS(-0x00001b11),
    PROXY_ERR_FAILED_TO_FIND_NODE(-0x00001c11),
    PROXY_ERR_INVALID_COMPRESS_TYPE(-0x00001d11),
    PROXY_ERR_REQUEST_OVERSPEED(-0x00001e11),
    PROXY_ERR_SWIFT_TIMEOUT(-0x00001f11),
    PROXY_ERR_SWIFT_ERROR(-0x00002011),
    PROXY_ERR_DIRECT_RESPONSE(-0x00002111),
    PROXY_ERR_INIT_TLOG(-0x00002211),
    PROXY_ERR_ASSISTANT_THREAD_NOT_RUN(-0x00002311),
    PROXY_ERR_REQUEST_ACCESS_CTRL_REJECT(-0x00002411),
    PROXY_ERR_NOT_ALL_NODES_ARE_IN_NORMAL_OR_WAIT_STATUS(-0x00002511),
    API_ERR_OVER_MAX_KEY_FIELD_NUM(-0x00000113),
    API_ERR_OVER_MAX_VALUE_FIELD_NUM(-0x00000213),
    API_ERR_OVER_MAX_FIELD_NAME_LEN(-0x00000313),
    API_ERR_OVER_MAX_FIELD_VALUE_LEN(-0x00000413),
    API_ERR_FIELD_NOT_EXSIST(-0x00000513),
    API_ERR_FIELD_TYPE_NOT_MATCH(-0x00000613),
    API_ERR_PARAMETER_INVALID(-0x00000713),
    API_ERR_OPERATION_TYPE_NOT_MATCH(-0x00000813),
    API_ERR_PACK_MESSAGE(-0x00000913),
    API_ERR_UNPACK_MESSAGE(-0x00000a13),
    API_ERR_PACKAGE_NOT_UNPACKED(-0x00000b13),
    API_ERR_OVER_MAX_RECORD_NUM(-0x00000c13),
    API_ERR_INVALID_COMMAND(-0x00000d13),
    API_ERR_NO_MORE_RECORD(-0x00000e13),
    API_ERR_OVER_KEY_FIELD_NUM(-0x00000f13),
    API_ERR_OVER_VALUE_FIELD_NUM(-0x00001013),
    API_ERR_OBJ_NEED_INIT(-0x00001113),
    API_ERR_INVALID_DATA_SIZE(-0x00001213),
    API_ERR_INVALID_ARRAY_COUNT(-0x00001313),
    API_ERR_INVALID_UNION_SELECT(-0x00001413),
    API_ERR_MISS_PRIMARY_KEY(-0x00001513),
    API_ERR_UNSUPPORT_FIELD_TYPE(-0x00001613),
    API_ERR_ARRAY_BUFFER_IS_SMALL(-0x00001713),
    API_ERR_IS_NOT_WHOLE_PACKAGE(-0x00001813),
    API_ERR_MISS_PAIR_FIELD(-0x00001913),
    API_ERR_GET_META_ENTRY(-0x00001a13),
    API_ERR_GET_ARRAY_META(-0x00001b13),
    API_ERR_GET_ENTRY_META(-0x00001c13),
    API_ERR_INCOMPATIBLE_META(-0x00001d13),
    API_ERR_PACK_ARRAY_DATA(-0x00001e13),
    API_ERR_PACK_UNION_DATA(-0x00001f13),
    API_ERR_PACK_STRUCT_DATA(-0x00002013),
    API_ERR_UNPACK_ARRAY_DATA(-0x00002113),
    API_ERR_UNPACK_UNION_DATA(-0x00002213),
    API_ERR_UNPACK_STRUCT_DATA(-0x00002313),
    API_ERR_INVALID_INDEX_NAME(-0x00002413),
    API_ERR_MISS_PARTKEY_FIELD(-0x00002513),
    API_ERR_ALLOCATE_MEMORY(-0x00002613),
    API_ERR_GET_META_SIZE(-0x00002713),
    API_ERR_MISS_BINARY_VERSION(-0x00002813),
    API_ERR_INVALID_INCREASE_FIELD(-0x00002913),
    API_ERR_INVALID_RESULT_FLAG(-0x00002a13),
    API_ERR_OVER_MAX_LIST_INDEX_NUM(-0x00002b13),
    API_ERR_INVALID_OBJ_STATUE(-0x00002c13),
    API_ERR_INVALID_REQUEST(-0x00002d13),
    API_ERR_INVALID_SHARD_LIST(-0x00002e13),
    API_ERR_TABLE_NAME_MISSING(-0x00002f13),
    API_ERR_SOCKET_SEND_BUFF_IS_FULL(-0x00003013),
    API_ERR_INVALID_MAGIC(-0x00003113),
    API_ERR_TABLE_IS_NOT_EXIST(-0x00003213),
    API_ERR_SHORT_BUFF(-0x00003313),
    API_ERR_FLOW_CONTROL(-0x00003413),
    API_ERR_COMPRESS_SWITCH_NOT_SUPPORTED_REGARDING_THIS_CMD(-0x00003513),
    API_ERR_FAILED_TO_FIND_ROUTE(-0x00003613),
    API_ERR_OVER_MAX_PKG_SIZE(-0x00003713),
    API_ERR_INVALID_VERSION_FOR_TLV(-0x00003813),
    API_ERR_BSON_SERIALIZE(-0x00003913),
    API_ERR_BSON_DESERIALIZE(-0x00003a13),
    API_ERR_ADD_RECORD(-0x00003b13),
    API_ERR_ZONE_IS_NOT_EXIST(-0x00003c13),
    API_ERR_TRAVERSER_IS_NOT_EXIST(-0x00003d13),
    API_ERR_CONNECTOR_IS_ABNORMAL(-0x00004013),
    API_ERR_WAIT_RSP_TIMEOUT(-0x00004113),
    CENTER_ERR_INVALID_PARAMS(-0x00000115),
    CENTER_ERR_TABLE_ALREADY_EXIST(-0x00000215),
    CENTER_ERR_TABLE_NOT_EXIST(-0x00000315),
    DIR_ERR_SIGN_FAIL(-0x00000117),
    DIR_ERR_LOW_VERSION(-0x00000217),
    DIR_ERR_HIGH_VERSION(-0x00000317),
    DIR_ERR_GET_DIR_SERVER_LIST(-0x00000417),
    DIR_ERR_APP_IS_NOT_FOUNT(-0x00000517),
    DIR_ERR_NOT_CONNECT_TCAPCENTER(-0x00000617),
    DIR_ERR_ZONE_IS_NOT_FOUNT(-0x00000717),
    DIR_ERR_HASH_TABLE_FAILED(-0x00000817),
    BSON_ERR_TYPE_IS_NOT_MATCH(-0x00000118),
    BSON_ERR_INVALID_DATA_TYPE(-0x00000218),
    BSON_ERR_INVALID_VALUE(-0x00000318),
    BSON_ERR_BSON_TYPE_UNMATCH_TDR_TYPE(-0x00000418),
    BSON_ERR_BSON_TYPE_IS_NOT_SUPPORT_BY_TCAPLUS(-0x00000518),
    BSON_ERR_BSON_ARRAY_COUNT_IS_INVALID(-0x00000618),
    BSON_ERR_FAILED_TO_PARSE(-0x00000718),
    BSON_ERR_INVALID_FIELD_NAME_LENGTH(-0x00000818),
    BSON_ERR_INDEX_FIELD_NAME_NOT_EXIST_WITH_ARRAY_TYPE(-0x00000918),
    BSON_ERR_INVALID_ARRAY_INDEX(-0x00000a18),
    BSON_ERR_TDR_META_LIB_IS_NULL(-0x00000b18),
    BSON_ERR_MATCHED_COUNT_GREATER_THAN_ONE(-0x00000c18),
    BSON_ERR_NO_MATCHED(-0x00000d18),
    BSON_ERR_GREATER_THAN_ARRAY_MAX_COUNT(-0x00000f18),
    BSON_ERR_BSON_EXCEPTION(-0x00001018),
    BSON_ERR_STD_EXCEPTION(-0x00001118),
    BSON_ERR_INVALID_KEY(-0x00001218),
    BSON_ERR_TDR_META_LIB_IS_INVALID(-0x00001318),
    COMMON_ERR_INVALID_ARGUMENTS(-0x00000119),
    COMMON_ERR_INVALID_MEMBER_VARIABLE_VALUE(-0x00000219),
    COMMON_ERR_SPINLOCK_INIT_FAIL(-0x00000319),
    COMMON_ERR_SPINLOCK_DESTROY_FAIL(-0x00000419),
    COMMON_ERR_COMPRESS_BUF_NOT_ENOUGH(-0x00000519),
    COMMON_ERR_DECOMPRESS_BUF_NOT_ENOUGH(-0x00000619),
    COMMON_ERR_DECOMPRESS_INVALID_INPUT(-0x00000719),
    COMMON_ERR_CANNOT_FIND_COMPRESS_ALGORITHM(-0x00000819),
    COMMON_ERR_CANNOT_FIND_DECOMPRESS_ALGORITHM(-0x00000919),
    COMMON_ERR_COMPRESS_FAIL(-0x00000a19),
    COMMON_ERR_DECOMPRESS_FAIL(-0x00000b19),
    COMMON_ERR_INVALID_SWITCH_VALUE(-0x00000c19),
    COMMON_ERR_LINUX_SYSTEM_CALL_FAIL(-0x00000d19),
    COMMON_ERR_NOT_FIND_STAT_CACHE_VALUE(-0x00000e19),
    COMMON_ERR_LZO_CHECK_FAIL(-0x00000f19),
    COMMON_INFO_DATA_NOT_MODIFIED(0x00000120);

    private static final Map<Integer, TcaplusErrorCode> NUMBERS;

    static {
        NUMBERS = new HashMap<>();
        for (TcaplusErrorCode error : TcaplusErrorCode.values()) {
            NUMBERS.put(error.value, error);
        }
    }

    private final int value;

    TcaplusErrorCode(int value) {
        this.value = value;
    }

    public static TcaplusErrorCode forNumber(int value) {
        return NUMBERS.getOrDefault(value, null);
    }

    public int getValue() {
        return value;
    }

    public boolean ok() {
        return value == GEN_ERR_SUC.value;
    }

    public boolean recordNotExist() {
        return value == TXHDB_ERR_RECORD_NOT_EXIST.value;
    }

    public boolean recordExist() {
        return value == SVR_ERR_FAIL_RECORD_EXIST.value;
    }

    public boolean invalidVersion() {
        return value == SVR_ERR_FAIL_INVALID_VERSION.value;
    }
}

package com.yorha.common.etcd;

import io.etcd.jetcd.ByteSequence;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public final class EtcdUtils {
    private EtcdUtils() {
    }

    /**
     * etcd 超时时间 MS
     */
    public static final int ETCD_TIME_OUT_TIME_MS = 5000;

    /**
     * 将字符串转为客户端所需的ByteSequence实例
     */
    public static ByteSequence bytesOf(String val) {
        return ByteSequence.from(val, StandardCharsets.UTF_8);
    }

    public static String toString(ByteSequence val) {
        return val.toString(StandardCharsets.UTF_8);
    }
}

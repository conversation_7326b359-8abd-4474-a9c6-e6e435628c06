package com.yorha.common.concurrent;

import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.Executor;

/**
 * 任务队列消费者
 *
 * <AUTHOR>
 */
public interface IGeminiExecutor extends Executor {
    /**
     * 执行器名称
     *
     * @return 名称
     */
    String getName();

    /**
     * 关闭线程池。
     */
    void shutdown();

    /**
     * runnable增加数值统计的上下文。类似于python装饰器
     *
     * @param runnable       可执行任务
     * @param offerNanos     任务提交时间 ns
     * @param stat           任务统计对象
     * @param logger         logger
     * @param dispatcherName 名称
     * @return 带上下文信息的执行体。
     */
    @NotNull
    static Runnable wrapWithStats(NamedRunnable runnable,
                                  long offerNanos,
                                  TaskStats stat,
                                  Logger logger,
                                  String dispatcherName) {
//        String runnableName = runnable.getName();
        return () -> {
            try {
                long start = SystemClock.nanoTimeNative();
                stat.onStart(runnable.getName(), start - offerNanos);
                // MonitorMgr.getInstance().getCounter(CounterEnum.TASK_IN_QUEUE_WAIT_TIME).labels(dispatcherName, runnableName).inc(start - offerNanos);

                runnable.run();
                // 统计任务的执行耗时
                long runnableCostTime = SystemClock.nanoTimeNative() - start;
                stat.onDone(runnable.getName(), runnableCostTime);
                // MonitorUnit.TASK_IN_QUEUE_DONE_COUNTER.labels(ServerContext)
                // MonitorMgr.getInstance().getCounter(CounterEnum.TASK_IN_QUEUE_DONE_COUNT).labels(dispatcherName, runnableName).inc();
                // MonitorMgr.getInstance().getCounter(CounterEnum.TASK_IN_QUEUE_COST_TIME).labels(dispatcherName, runnableName).inc(runnableCostTime);
            } catch (Exception e) {
                logger.error("IGeminiExecutor::wrapWithStats catch ", e);
                // MonitorMgr.getInstance().getCounter(CounterEnum.TASK_IN_QUEUE_FAIL_COUNT).labels(dispatcherName, runnableName).inc();
            } catch (Error e) {
                WechatLog.error("IGeminiExecutor::wrapWithStats catch!! ", e);
            }
        };
    }
}

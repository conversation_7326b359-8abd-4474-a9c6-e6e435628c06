package com.yorha.common.db.mongo.utils;

import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.TcaplusDBException;
import com.yorha.common.db.tcaplus.op.FieldMetaData;
import com.yorha.gemini.utils.StringUtils;
import org.bson.Document;
import org.bson.types.Binary;

import javax.annotation.Nonnull;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class PbHelper {

    private static long getBlob(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        long size;
        try {
            byte[] bytes = record.get(key, Binary.class).getData();
            size = bytes.length;
            DynamicMessage dynamicMessage = DynamicMessage.parseFrom(desc.getMessageType(), bytes);
            builder.setField(desc, dynamicMessage);
        } catch (Exception e) {
            throw new TcaplusDBException("getBlob failed", e);
        }
        return size;
    }


    private static long getByteString(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        long size;
        try {
            byte[] bytes = record.get(key, Binary.class).getData();
            size = bytes.length;
            builder.setField(desc, ByteString.copyFrom(bytes));
        } catch (Exception e) {
            throw new TcaplusDBException(StringUtils.format("getByteString failed, descName={}", desc.getFullName()), e);
        }
        return size;
    }


    private static long getDouble(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        builder.setField(desc, record.getDouble(key));
        return Double.BYTES;
    }


    private static long getFloat(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        builder.setField(desc, record.get(key, float.class));
        return Float.BYTES;
    }


    private static long getInt(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        if (desc.getJavaType() == Descriptors.FieldDescriptor.JavaType.ENUM) {
            builder.setField(desc, desc.getEnumType().getValues().get(record.getInteger(key)));
        } else {
            builder.setField(desc, record.getInteger(key));
        }
        return Integer.BYTES;
    }


    private static long getLong(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        builder.setField(desc, record.getLong(key));
        return Long.BYTES;
    }


    private static long getString(Document record, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        String str = record.getString(key);
        builder.setField(desc, str);
        return str.length();
    }

    /**
     * 通过document设置builder
     *
     * @param document 数据记录
     * @param desc     数据字段的描述
     * @param builder  数据字段的容器
     * @return 从record读取的byte数量
     */
    private static long getField(Document document, String key, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        long size;
        switch (desc.getJavaType()) {
            case INT:
            case ENUM:
                size = getInt(document, key, desc, builder);
                break;
            case LONG:
                size = getLong(document, key, desc, builder);
                break;
            case FLOAT:
                size = getFloat(document, key, desc, builder);
                break;
            case DOUBLE:
                size = getDouble(document, key, desc, builder);
                break;
            case STRING:
                size = getString(document, key, desc, builder);
                break;
            case MESSAGE:
                size = getBlob(document, key, desc, builder);
                break;
            case BYTE_STRING:
                size = getByteString(document, key, desc, builder);
                break;
            case BOOLEAN:
            default:
                throw new TcaplusDBException(StringUtils.format("Invalid tcaplus table value type {}", desc.getType().getJavaType()));
        }
        return size;
    }

    /**
     * 根据fieldMetaData解析document，将数据存入value
     *
     * @param document      mongodb数据行
     * @param fieldMetaData tcaplus pb信息
     * @param value         pb数据
     * @return docuemnt数据大小
     */
    public static int document2Pb(@Nonnull final Document document, @Nonnull final FieldMetaData fieldMetaData, @Nonnull final Message.Builder value) {
        int responseBytesSize = 0;
        if (fieldMetaData.documentIdField != null && document.containsKey("_id")) {
            responseBytesSize += PbHelper.getField(document, "_id", fieldMetaData.documentIdField, value);
        }

        for (Descriptors.FieldDescriptor keyDesc : fieldMetaData.keyFieldsList) {
            if (document.containsKey(keyDesc.getName())) {
                responseBytesSize += PbHelper.getField(document, keyDesc.getName(), keyDesc, value);
            }
        }
        for (Descriptors.FieldDescriptor valDesc : fieldMetaData.valueFieldsList) {
            if (document.containsKey(valDesc.getName())) {
                responseBytesSize += PbHelper.getField(document, valDesc.getName(), valDesc, value);
            }
        }
        return responseBytesSize;
    }

    /**
     * 获得pb中所有设置过的字段的名字
     *
     * @param value pb数据
     * @return Set<String>
     */
    public static Set<String> getPbValidFieldNames(final Message.Builder value) {
        return value.getAllFields().keySet().stream().map(Descriptors.FieldDescriptor::getName).collect(Collectors.toSet());
    }

    /**
     * @param fieldMetaData
     * @param a
     * @param b
     * @return 两个message的主键是否相等
     */
    public static boolean keyEqual(@Nonnull final FieldMetaData fieldMetaData, @Nonnull final Message.Builder a, @Nonnull final Message.Builder b) {
        for (Descriptors.FieldDescriptor keyDesc : fieldMetaData.keyFieldsList) {
            if (!a.getField(keyDesc).equals(b.getField(keyDesc))) {
                return false;
            }
        }
        return true;
    }
}

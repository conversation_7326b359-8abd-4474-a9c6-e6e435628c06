package com.yorha.common.resource.resservice.dailyDiscount;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class DailyDiscountService extends AbstractResService {

    public DailyDiscountService(ResHolder resHolder) {
        super(resHolder);
    }

    static final Map<CommonEnum.ActivityOpenType, OpenTimeSupporter> OPENTIME_SUPPORTER_FACTORY = ImmutableMap.of(
            CommonEnum.ActivityOpenType.AOT_FROM_ZONE_OPEN, new ServerOpenTimeSupporter()
    );

    /**
     * key = goodsId
     */
    private final Map<Integer, DailyPackInfo> packInfos = Maps.newHashMap();
    /**
     * key = goodsId
     */
    private final Map<Integer, SuperDailyPackInfo> superPackInfos = Maps.newHashMap();

    /**
     * key = heroId, val = 开服第val天
     */
    private final Map<Integer, Integer> heroBuyableDay = Maps.newHashMap();

    @Override
    public void checkValid() throws ResourceException {
        checkConstDailyPackTemplate();
        checkDailyPackHeroTemplate();
        checkDailyPackTemplate();
    }


    private void checkDailyPackTemplate() throws ResourceException {
        ConstDailyPackTemplate constDailyPackTemplate = getResHolder().getConstTemplate(ConstDailyPackTemplate.class);
        for (DailyPackTemplate dailyPackTemplate : getResHolder().getListFromMap(DailyPackTemplate.class)) {

            if (dailyPackTemplate.getGoodsIdsList().size() != constDailyPackTemplate.getCollectionPackLevel()) {
                throw new ResourceException("每日特惠表 daily_pack id={} 未知礼包档位:{}", dailyPackTemplate.getId(), dailyPackTemplate.getGoodsIdsList().size());
            }

            int heroId = dailyPackTemplate.getId();
            if (getResHolder().findValueFromMap(HeroRhTemplate.class, heroId) == null) {
                throw new ResourceException("每日特惠表 daily_pack id={} 未知heroId:{}", dailyPackTemplate.getId(), heroId);
            }
            // 校验每日特惠礼包
            for (int i = 0; i < dailyPackTemplate.getGoodsIdsList().size(); i++) {
                int goodsId = dailyPackTemplate.getGoodsIdsList().get(i);
                ChargeGoodsTemplate goodsTemplate = getResHolder().findValueFromMap(ChargeGoodsTemplate.class, goodsId);
                if (goodsTemplate == null) {
                    throw new ResourceException("每日特惠表 daily_pack id={} 未知goodsId:{}", dailyPackTemplate.getId(), goodsId);
                }
                if (goodsTemplate.getGoodsType() != CommonEnum.GoodsType.GT_DAILY_ACCOUNT) {
                    throw new ResourceException("每日特惠表 daily_pack id={} goodsId:{} 在支付表中配置的礼包类型非每日特惠礼包", dailyPackTemplate.getId(), goodsId);
                }

            }
            // 校验超值每日特惠礼包
            for (int i = 0; i < dailyPackTemplate.getSupergoodsIdsList().size(); i++) {
                int superGoodsId = dailyPackTemplate.getSupergoodsIdsList().get(i);
                ChargeGoodsTemplate goodsTemplate = getResHolder().findValueFromMap(ChargeGoodsTemplate.class, superGoodsId);
                if (goodsTemplate == null) {
                    throw new ResourceException("每日特惠表 daily_pack id={} 未知superGoodsId:{}", dailyPackTemplate.getId(), superGoodsId);
                }

                if (goodsTemplate.getGoodsType() != CommonEnum.GoodsType.GT_SUPER_DAILY_ACCOUNT) {
                    throw new ResourceException("每日特惠表 daily_pack id={} goodsId:{} 在支付表中配置的礼包类型非超级每日特惠礼包", dailyPackTemplate.getId(), superGoodsId);
                }
            }

        }
    }

    private void checkDailyPackHeroTemplate() throws ResourceException {
        for (DailyPackHeroTemplate dailyPackHeroTemplate : getResHolder().getListFromMap(DailyPackHeroTemplate.class)) {
            int heroId = dailyPackHeroTemplate.getHeroId();
            if (getResHolder().findValueFromMap(HeroRhTemplate.class, heroId) == null) {
                throw new ResourceException("每日特惠表 daily_pack_hero id={} 未知heroId:{}", dailyPackHeroTemplate.getId(), heroId);
            }
            OpenTimeSupporter openTimeSupporter = OPENTIME_SUPPORTER_FACTORY.getOrDefault(dailyPackHeroTemplate.getOpenType(), null);
            if (openTimeSupporter == null) {
                throw new ResourceException("每日特惠表 daily_pack_hero id={} 上架时间类型未支持:{}", dailyPackHeroTemplate.getId(), dailyPackHeroTemplate.getOpenType());
            }
            openTimeSupporter.checkOpenTime(dailyPackHeroTemplate);
        }
    }

    private void checkConstDailyPackTemplate() throws ResourceException {
        ConstDailyPackTemplate constDailyPackTemplate = getResHolder().getConstTemplate(ConstDailyPackTemplate.class);
        if (constDailyPackTemplate.getHeroShowDay() <= 0) {
            throw new ResourceException("每日特惠常量表 const_daily_pack heroShowDay 应为正数");
        }
        {
            int collectionPackLevel = constDailyPackTemplate.getCollectionPackLevel();
            Set<Integer> packLevels = Sets.newHashSet(constDailyPackTemplate.getPackLevels());
            if (packLevels.size() != constDailyPackTemplate.getPackLevels().size()) {
                throw new ResourceException("每日特惠常量表 const_daily_pack packLevels 存在重复礼包等级");
            }
            if (packLevels.contains(collectionPackLevel)) {
                throw new ResourceException("每日特惠常量表 const_daily_pack collectionPackLevel 不应配置在packLevels中");
            }
        }


        {
            int collectionSuperPackLevel = constDailyPackTemplate.getCollectionsuperPackLevel();
            Set<Integer> superPackLevels = Sets.newHashSet(constDailyPackTemplate.getSuperpackLevels());
            if (superPackLevels.size() != constDailyPackTemplate.getPackLevels().size()) {
                throw new ResourceException("每日特惠常量表 const_daily_pack superPackLevels 存在重复超值每日特惠礼包等级");
            }
            if (superPackLevels.contains(collectionSuperPackLevel)) {
                throw new ResourceException("每日特惠常量表 const_daily_pack collectionSuperPackLevel 不应配置在superPackLevels中");
            }
        }

        List<IntPairType> daliyFreeReward = constDailyPackTemplate.getDailyFreeReward();
        for (IntPairType freeReward : daliyFreeReward) {
            int itemId = freeReward.getKey();
            int count = freeReward.getValue();
            if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                throw new ResourceException("每日特惠常量表 const_daily_pack dailyFreeReward 道具id：{} 道具不存在", itemId);
            }
            if (count <= 0) {
                throw new ResourceException("每日特惠常量表 const_daily_pack dailyFreeReward 道具id：{} 数量为非正数", itemId);
            }
        }

        int originHeroId = constDailyPackTemplate.getPrimaryHeroId();
        if (getResHolder().findValueFromMap(HeroRhTemplate.class, originHeroId) == null) {
            throw new ResourceException("每日特惠表 const_daily_pack primaryHeroId 未知heroId: {}", originHeroId);
        }
    }

    @Override
    public void load() throws ResourceException {
        this.loadPackInfo();
        this.loadHeroOpenTime();
    }

    private void loadHeroOpenTime() {
        for (DailyPackHeroTemplate dailyPackHeroTemplate : getResHolder().getListFromMap(DailyPackHeroTemplate.class)) {
            int openDay;
            OpenTimeSupporter openTimeSupporter = OPENTIME_SUPPORTER_FACTORY.getOrDefault(dailyPackHeroTemplate.getOpenType(), null);
            if (openTimeSupporter == null) {
                openDay = -1;
            } else {
                openDay = openTimeSupporter.getOpenTime(dailyPackHeroTemplate);
            }
            this.heroBuyableDay.put(dailyPackHeroTemplate.getHeroId(), openDay);
        }

    }

    private void loadPackInfo() {
        for (DailyPackTemplate dailyPackTemplate : getResHolder().getListFromMap(DailyPackTemplate.class)) {
            int heroId = dailyPackTemplate.getId();
            // 普通每日特惠礼包
            for (int i = 0; i < dailyPackTemplate.getGoodsIdsList().size(); i++) {
                int goodsId = dailyPackTemplate.getGoodsIdsList().get(i);
                this.packInfos.put(goodsId, new DailyPackInfo(heroId, i + 1));
            }
            // 超值每日特惠礼包
            for (int i = 0; i < dailyPackTemplate.getSupergoodsIdsList().size(); i++) {
                int goodsId = dailyPackTemplate.getSupergoodsIdsList().get(i);
                this.superPackInfos.put(goodsId, new SuperDailyPackInfo(heroId, i + 1));
            }
        }
    }

    /**
     * 尝试获取每日特惠商品等级，失败返回负数
     *
     * @param goodsId 商品id
     * @return int <0 失败
     */
    public int tryGetGoodsLevel(int goodsId) {
        DailyPackInfo dailyPackInfo = this.packInfos.getOrDefault(goodsId, null);
        return dailyPackInfo == null ? -1024 : dailyPackInfo.packLevel();
    }

    /**
     * 尝试获取超值每日特惠商品等级，失败返回负数
     *
     * @param goodsId 商品id
     * @return int <0 失败
     */
    public int tryGetSuperGoodsLevel(int goodsId) {
        SuperDailyPackInfo superDailyPackInfo = this.superPackInfos.getOrDefault(goodsId, null);
        return superDailyPackInfo == null ? -1024 : superDailyPackInfo.packLevel();
    }

    /**
     * 尝试获取商品对应英雄id，失败返回负数
     *
     * @param goodsId 商品id
     * @return int <0 失败
     */
    public int tryGetHeroId(int goodsId) {
        DailyPackInfo dailyPackInfo = this.packInfos.getOrDefault(goodsId, null);
        return dailyPackInfo == null ? -1024 : dailyPackInfo.heroId();
    }

    /**
     * 尝试获取商品对应英雄id，失败返回负数
     *
     * @param goodsId 商品id
     * @return int <0 失败
     */
    public int tryGetSuperHeroId(int goodsId) {
        SuperDailyPackInfo superDailyPackInfo = this.superPackInfos.getOrDefault(goodsId, null);
        return superDailyPackInfo == null ? -1024 : superDailyPackInfo.heroId();
    }

    /**
     * 获取每日特惠默认英雄id
     *
     * @return int
     */
    public int getPrimaryHeroId() {
        return getResHolder().getConstTemplate(ConstDailyPackTemplate.class).getPrimaryHeroId();
    }

    /**
     * 是否是可购买英雄
     *
     * @param heroId         英雄id
     * @param serverOpenTsMs 起服时间
     * @return bool
     */
    public boolean isBuyAbleHero(int heroId, long serverOpenTsMs) {
        int buyableDay = this.heroBuyableDay.getOrDefault(heroId, -1024);
        if (buyableDay < 0) {
            return false;
        }
        //策划配置第一天时 服务器上线即可买
        return TimeUtils.getAbsNatureDaysBetween(serverOpenTsMs, SystemClock.now()) >= buyableDay - 1;
    }

    /**
     * 获取礼包合集等级
     *
     * @return int
     */
    public int getCollectionLevel() {
        return getResHolder().getConstTemplate(ConstDailyPackTemplate.class).getCollectionPackLevel();
    }

    /**
     * 获取超级礼包合集等级
     *
     * @return int
     */
    public int getSuperCollectionLevel() {
        return getResHolder().getConstTemplate(ConstDailyPackTemplate.class).getCollectionsuperPackLevel();
    }

    /**
     * 获得普通每日特惠档位数量
     */
    public int getPackLevelNum() {
        return getResHolder().getConstTemplate(ConstDailyPackTemplate.class).getPackLevels().size();
    }


    /**
     * 获取每日免费奖励
     *
     * @return List<IntPairType>
     */
    public List<IntPairType> getDailyFreeReward() {
        return getResHolder().getConstTemplate(ConstDailyPackTemplate.class).getDailyFreeReward();
    }

    /**
     * 每日特惠礼包数据整合
     */
    record DailyPackInfo(int heroId, int packLevel) {
    }

    /**
     * 超值每日特惠礼包数据整合
     */
    record SuperDailyPackInfo(int heroId, int packLevel) {
    }

}
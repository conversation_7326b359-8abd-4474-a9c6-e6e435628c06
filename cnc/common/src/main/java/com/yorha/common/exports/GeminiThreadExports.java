package com.yorha.common.exports;

import com.google.common.collect.Lists;
import com.yorha.common.utils.GeminiThreadUtils;
import io.prometheus.client.Collector;
import io.prometheus.client.CounterMetricFamily;
import io.prometheus.client.GaugeMetricFamily;
import io.prometheus.client.Predicate;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

import static io.prometheus.client.SampleNameFilter.ALLOW_ALL;

/**
 * 基于io.prometheus.client.hotspot.ThreadExports做了扩展，额外的gemini所需扩展。
 * example:
 * jvm_threads_current{} 300
 * jvm_threads_daemon{} 200
 * jvm_threads_peak{} 410
 * jvm_threads_started_total{} 1200
 * jvm_threads_usage{threadName="xxxx"} 0.0 （此值0-1，扩展）
 *
 * <AUTHOR>
 */
public class GeminiThreadExports extends Collector {
    private static final String JVM_THREADS_USAGE = "jvm_threads_usage";
    private static final String JVM_THREADS_CURRENT = "jvm_threads_current";
    private static final String JVM_THREADS_DAEMON = "jvm_threads_daemon";
    private static final String JVM_THREADS_PEAK = "jvm_threads_peak";
    private static final String JVM_THREADS_STARTED_TOTAL = "jvm_threads_started_total";
    private static final String JVM_THREADS_DEADLOCKED = "jvm_threads_deadlocked";
    private static final String JVM_THREADS_DEADLOCKED_MONITOR = "jvm_threads_deadlocked_monitor";
    private static final String JVM_THREADS_STATE = "jvm_threads_state";

    private final String busId;
    private final ThreadMXBean tmxb;
    private final ReentrantLock lock;
    private Map<Long, GeminiThreadUtils.ThreadCpuStatInfo> threadCpuStatInfoMap;
    private Map<String, GeminiThreadUtils.ThreadCpuStatInfo> systemCpuStatInfoMap;

    public GeminiThreadExports(final String busId) {
        this(busId, ManagementFactory.getThreadMXBean());
    }

    @SuppressWarnings("unchecked")
    private GeminiThreadExports(final String busId, ThreadMXBean tmxBean) {
        this.busId = busId;
        this.tmxb = tmxBean;
        this.threadCpuStatInfoMap = Collections.EMPTY_MAP;
        this.systemCpuStatInfoMap = Collections.EMPTY_MAP;
        // 公平锁，FIFO
        this.lock = new ReentrantLock(true);
    }

    @Override
    public List<MetricFamilySamples> collect() {
        return collect(null);
    }

    @Override
    public List<MetricFamilySamples> collect(Predicate<String> nameFilter) {
        final List<MetricFamilySamples> mfs = new ArrayList<>(10);
        this.addThreadMetrics(mfs, nameFilter == null ? ALLOW_ALL : nameFilter);
        if (nameFilter == null || nameFilter.test(JVM_THREADS_USAGE)) {
            mfs.add(this.collectCpuThreadCpuUsage());
        }
        return mfs;
    }

    private MetricFamilySamples collectCpuThreadCpuUsage() {
        final GaugeMetricFamily threadCpuUsageMetricFamily = new GaugeMetricFamily(JVM_THREADS_USAGE, "cpu usage",
                Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_THREAD_NAME));
        // 加锁是因为p8s默认服务器多线程，默认为5
        this.lock.lock();
        try {
            // 清理掉上次已经清理的线程数据
            final var newThreadCpuStatInfoMap = GeminiThreadUtils.collectUserThreadCpuUsage(this.tmxb, this.threadCpuStatInfoMap);
            final var newSystemThreadCpuStatInfoMap = GeminiThreadUtils.collectSystemThreadCpuUsage(this.tmxb, this.systemCpuStatInfoMap);
            // 存活线程添加label
            for (final GeminiThreadUtils.ThreadCpuStatInfo v : newThreadCpuStatInfoMap.values()) {
                threadCpuUsageMetricFamily.addMetric(Lists.newArrayList(busId, v.getThreadName()), v.getThreadCpuUsage());
            }
            for (final GeminiThreadUtils.ThreadCpuStatInfo v : newSystemThreadCpuStatInfoMap.values()) {
                threadCpuUsageMetricFamily.addMetric(Lists.newArrayList(busId, v.getThreadName()), v.getThreadCpuUsage());
            }
            // 刚刚失去的线程设置为0.0
            for (final Map.Entry<Long, GeminiThreadUtils.ThreadCpuStatInfo> kv : this.threadCpuStatInfoMap.entrySet()) {
                if (newThreadCpuStatInfoMap.containsKey(kv.getKey())) {
                    continue;
                }
                final GeminiThreadUtils.ThreadCpuStatInfo tInfo = kv.getValue();
                threadCpuUsageMetricFamily.addMetric(Lists.newArrayList(busId, tInfo.getThreadName()), 0.0);
            }
            for (final Map.Entry<String, GeminiThreadUtils.ThreadCpuStatInfo> kv : this.systemCpuStatInfoMap.entrySet()) {
                if (newSystemThreadCpuStatInfoMap.containsKey(kv.getKey())) {
                    continue;
                }
                final GeminiThreadUtils.ThreadCpuStatInfo tInfo = kv.getValue();
                threadCpuUsageMetricFamily.addMetric(Lists.newArrayList(busId, tInfo.getThreadName()), 0.0);
            }
            // 更新上一次collect的快照
            this.threadCpuStatInfoMap = newThreadCpuStatInfoMap;
            this.systemCpuStatInfoMap = newSystemThreadCpuStatInfoMap;
        } finally {
            this.lock.unlock();
        }
        return threadCpuUsageMetricFamily;
    }

    private void addThreadMetrics(List<MetricFamilySamples> sampleFamilies, Predicate<String> nameFilter) {
        if (nameFilter.test(JVM_THREADS_CURRENT)) {
            final GaugeMetricFamily metricFamily = new GaugeMetricFamily(
                    JVM_THREADS_CURRENT,
                    "Current thread count of a JVM",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            metricFamily.addMetric(Collections.singletonList(this.busId), this.tmxb.getThreadCount());
            sampleFamilies.add(metricFamily);
        }

        if (nameFilter.test(JVM_THREADS_DAEMON)) {
            final GaugeMetricFamily metricFamily = new GaugeMetricFamily(
                    JVM_THREADS_DAEMON,
                    "Daemon thread count of a JVM",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            metricFamily.addMetric(Collections.singletonList(this.busId), this.tmxb.getDaemonThreadCount());
            sampleFamilies.add(metricFamily);
        }

        if (nameFilter.test(JVM_THREADS_PEAK)) {
            final GaugeMetricFamily metricFamily = new GaugeMetricFamily(
                    JVM_THREADS_PEAK,
                    "Peak thread count of a JVM",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            metricFamily.addMetric(Collections.singletonList(this.busId), this.tmxb.getPeakThreadCount());
            sampleFamilies.add(metricFamily);
        }

        if (nameFilter.test(JVM_THREADS_STARTED_TOTAL)) {
            final CounterMetricFamily metricFamily = new CounterMetricFamily(
                    JVM_THREADS_STARTED_TOTAL,
                    "Started thread count of a JVM",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            metricFamily.addMetric(Collections.singletonList(this.busId), this.tmxb.getTotalStartedThreadCount());
            sampleFamilies.add(metricFamily);
        }

        if (nameFilter.test(JVM_THREADS_DEADLOCKED)) {
            final GaugeMetricFamily metricFamily = new GaugeMetricFamily(
                    JVM_THREADS_DEADLOCKED,
                    "Cycles of JVM-threads that are in deadlock waiting to acquire object monitors or ownable synchronizers",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            metricFamily.addMetric(Collections.singletonList(this.busId), nullSafeArrayLength(this.tmxb.findDeadlockedThreads()));
            sampleFamilies.add(metricFamily);
        }

        if (nameFilter.test(JVM_THREADS_DEADLOCKED_MONITOR)) {
            final GaugeMetricFamily metricFamily = new GaugeMetricFamily(
                    JVM_THREADS_DEADLOCKED_MONITOR,
                    "Cycles of JVM-threads that are in deadlock waiting to acquire object monitors",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            metricFamily.addMetric(Collections.singletonList(this.busId), nullSafeArrayLength(this.tmxb.findMonitorDeadlockedThreads()));
            sampleFamilies.add(metricFamily);
        }

        if (nameFilter.test(JVM_THREADS_STATE)) {
            final GaugeMetricFamily threadStateFamily = new GaugeMetricFamily(
                    JVM_THREADS_STATE,
                    "Current count of threads by state",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_THREAD_STATE));

            Map<Thread.State, Integer> threadStateCounts = getThreadStateCountMap();
            for (Map.Entry<Thread.State, Integer> entry : threadStateCounts.entrySet()) {
                threadStateFamily.addMetric(
                        Lists.newArrayList(this.busId, entry.getKey().toString()),
                        entry.getValue()
                );
            }
            sampleFamilies.add(threadStateFamily);
        }
    }

    private Map<Thread.State, Integer> getThreadStateCountMap() {
        // Get thread information without computing any stack traces
        ThreadInfo[] allThreads = this.tmxb.getThreadInfo(this.tmxb.getAllThreadIds(), 0);

        // Initialize the map with all thread states
        HashMap<Thread.State, Integer> threadCounts = new HashMap<Thread.State, Integer>();
        for (Thread.State state : Thread.State.values()) {
            threadCounts.put(state, 0);
        }

        // Collect the actual thread counts
        for (ThreadInfo curThread : allThreads) {
            if (curThread != null) {
                Thread.State threadState = curThread.getThreadState();
                threadCounts.put(threadState, threadCounts.get(threadState) + 1);
            }
        }

        return threadCounts;
    }

    private static double nullSafeArrayLength(long[] array) {
        return null == array ? 0 : array.length;
    }

}

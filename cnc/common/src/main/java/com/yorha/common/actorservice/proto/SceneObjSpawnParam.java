package com.yorha.common.actorservice.proto;

import com.yorha.common.utils.shape.Point;
import com.yorha.proto.CommonEnum.MonsterCreateType;

/**
 * <AUTHOR>
 */
public class SceneObjSpawnParam {

    /**
     * 生命周期
     */
    private long lifeTimeTsMs;

    /**
     * 技能召唤物(仅受生命周期管理的强制回收)
     */
    private boolean isBattleSummons = false;

    /**
     * 野怪创建类型
     */
    private MonsterCreateType createType;
    /**
     * 出生朝向
     */
    private Point yaw;
    /**
     * 野怪召唤者id
     */
    private long summonPlayerId;

    public long getLifeTime() {
        return lifeTimeTsMs;
    }

    public void setLifeTime(long lifeTimeTsMs) {
        this.lifeTimeTsMs = lifeTimeTsMs;
    }

    public void setCreateType(MonsterCreateType createType) {
        this.createType = createType;
    }

    public MonsterCreateType getCreateType() {
        return createType;
    }

    public Point getYaw() {
        return yaw;
    }

    public boolean isBattleSummons() {
        return isBattleSummons;
    }

    public void setBattleSummons(boolean battleSummons) {
        isBattleSummons = battleSummons;
    }

    public void setYaw(Point yaw) {
        this.yaw = yaw;
    }

    public void setSummonPlayerId(long summonPlayerId) {
        this.summonPlayerId = summonPlayerId;
    }

    public long getSummonPlayerId() {
        return summonPlayerId;
    }
}

package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.UpdateOption;
import com.yorha.common.db.tcaplus.result.UpdateResult;

public class UpdateAsk<T extends Message.Builder> implements GameDbReq<UpdateResult<T>> {
    private final T req;
    private final UpdateOption option;

    public UpdateAsk(T req) {
        this(req, UpdateOption.DEFAULT_VALUE);
    }

    public UpdateAsk(T req, UpdateOption option) {
        this.req = req;
        this.option = option;
    }

    public T getReq() {
        return req;
    }

    public UpdateOption getOption() {
        return option;
    }
}

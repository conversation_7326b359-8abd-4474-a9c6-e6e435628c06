package com.yorha.common.monitor;

import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;

/**
 * counter类型必须以_total结尾
 */
public class MonitorUnit {

    /**
     * 全局
     */
    public static final Counter IDIP_REQUEST_TOTAL = GeminiMonitor.newCounter("idip_request_total", "cmdId");

    /**
     * node监控
     */
    // idFactory切换段的次数
    public static final Counter ID_FACTORY_REMOVE_SEGMENT_TOTAL = GeminiMonitor.newCounter("id_factory_remove_segment_total");
    // IdFactor调用next id的次数
    public static final Counter ID_FACTORY_NEXT_ID_REASON_TOTAL = GeminiMonitor.newCounter("id_factory_next_id_total", "type", "reason");
    // Actor数量
    public static final Gauge ACTOR_NUM_GAUGE = GeminiMonitor.newGauge("actor_num", "actorRole");
    // actor淘汰次数
    public static final Counter ACTOR_DESTROY_COUNTER = GeminiMonitor.newCounter("actor_destroy_total", "actorRole");
    // PlayerActor相关的Entity数量
    public static final Gauge PLAYER_ACTOR_ENTITY_TOTAL = GeminiMonitor.newGauge("player_actor_entity_num", "entityType");
    // 协程数量
    public static final Gauge FIBER_NUM_GAUGE = GeminiMonitor.newGauge("fiber_num", "fiberName");
    // qlog数量
    public static final Counter QLOG_TOTAL = GeminiMonitor.newCounter("qlog_total", "qlogName");
    // 目录服响应次数
    public static final Counter DIR_RESPONSE_TOTAL = GeminiMonitor.newCounter("dir_response_total", "msgType");
    // 压缩次数
    public static final Gauge COMPRESS_NUM = GeminiMonitor.newGauge("compress_num", "reason");
    // 解压次数
    public static final Gauge UNCOMPRESS_NUM = GeminiMonitor.newGauge("uncompress_num", "reason");


    /**
     * cs监控
     */
    // session当前数量
    public static final Gauge SESSION_CUR_NUM_GAUGE = GeminiMonitor.newGauge("session_cur_num");
    // session新增
    public static final Counter SESSION_ADD_NUM_COUNTER = GeminiMonitor.newCounter("session_add_num_total");
    // session移除
    public static final Counter SESSION_REMOVE_NUM_COUNTER = GeminiMonitor.newCounter("session_remove_num_total");
    // 上行消息个数
    public static final Counter READ_MSG_COUNTER = GeminiMonitor.newCounter("read_msg_total");
    // 上行消息流量 byte
    public static final Counter READ_MSG_BYTE_COUNTER = GeminiMonitor.newCounter("read_msg_byte_total");
    // 下行消息个数
    public static final Counter WRITE_MSG_COUNTER = GeminiMonitor.newCounter("write_msg_total");
    // 下行消息流量 byte
    public static final Counter WRITE_MSG_BYTE_COUNTER = GeminiMonitor.newCounter("write_msg_byte_total");
    // 大消息触发压缩的次数
    public static final Counter BIG_MSG_COMPRESS_TOTAL = GeminiMonitor.newCounter("big_msg_compress_total", "msgType");
    // cs的maxQps
    public static final Gauge CS_MAX_QPS = GeminiMonitor.newGauge("cs_max_qps", "msgType");
    // 消息处理耗时
    public static final Gauge SC_AVG_COST = GeminiMonitor.newGauge("sc_avg_cost", "msgType");
    // sc和ntf的maxSize
    public static final Gauge SC_NTF_MAX_SIZE = GeminiMonitor.newGauge("sc_ntf_max_size", "msgType");


    /**
     * db监控
     */
    // db的maxQps
    public static final Gauge DB_OUT_MAX_QPS = GeminiMonitor.newGauge("db_out_max_qps", "desc");
    // db的平均耗时
    public static final Gauge DB_IN_AVG_COST = GeminiMonitor.newGauge("db_in_avg_cost", "desc");
    // db上流量
    public static final Counter DB_OP_ASK_BYTES_TOTAL = GeminiMonitor.newCounter("db_op_ask_bytes_total", "tableName", "op");
    // db下流量
    public static final Counter DB_OP_ANS_BYTES_TOTAL = GeminiMonitor.newCounter("db_op_ans_bytes_total", "tableName", "op");
    // 玩家最大落库size
    public static final Gauge MAX_DB_PLAYER_SIZE = GeminiMonitor.newGauge("max_db_player_size");
    // clan最大落库size
    public static final Gauge MAX_DB_CLAN_SIZE = GeminiMonitor.newGauge("max_db_clan_size");
    // 每张表的落库最大size
    public static final Gauge DB_TABLE_REQ_MAX_SIZE = GeminiMonitor.newGauge("db_table_req_max_size", "desc");
    // 玩家增量落库次数
    public static final Counter DB_PLAYER_CHANGE_ATTR_SAVE_TOTAL = GeminiMonitor.newCounter("db_player_change_attr_save_total", "entityType");
    // 玩家全量落库次数
    public static final Counter DB_PLAYER_FULL_ATTR_SAVE_TOTAL = GeminiMonitor.newCounter("db_player_full_attr_save_total", "entityType");
    // 军团增量落库次数
    public static final Counter DB_CLAN_CHANGE_ATTR_SAVE_TOTAL = GeminiMonitor.newCounter("db_clan_change_attr_save_total");
    // 军团全量落库次数
    public static final Counter DB_CLAN_FULL_ATTR_SAVE_TOTAL = GeminiMonitor.newCounter("db_clan_full_attr_save_total");


    /**
     * 业务监控
     */
    // 总联盟数量
    public static final Gauge CLAN_ALIVE_NUM_GAUGE = GeminiMonitor.newGauge("clan_alive_num");
    // 查询名片数量
    public static final Counter CARD_REQUEST_TOTAL = GeminiMonitor.newCounter("card_request_total", "cardType");
    // 名字服务请求数量
    public static final Counter NAME_REQUEST_TOTAL = GeminiMonitor.newCounter("name_request_total", "type");
    // 寻路次数
    public static final Counter SEARCH_PATH_COUNTER = GeminiMonitor.newCounter("search_path_total");
    // 战斗关系数量
    public static final Gauge BATTLE_RELATION_NUM_GAUGE = GeminiMonitor.newGauge("battle_relation_num");
    // 战斗对象数量
    public static final Gauge BATTLE_ROLE_NUM_GAUGE = GeminiMonitor.newGauge("battle_role_num");
    // 战斗tick平均耗时
    public static final Gauge BATTLE_TICK_AVG_COST_TIME = GeminiMonitor.newGauge("battle_tick_avg_cost_time_ms");
    // 大世界tick耗时
    public static final Gauge BIG_SCENE_TICK_COST = GeminiMonitor.newGauge("big_scene_tick_cost");
    // 全地图落堡选点失败次数
    public static final Counter BIG_SCENE_BORN_FAILED_COUNTER = GeminiMonitor.newCounter("big_scene_born_failed_total");
    // SceneObj数量
    public static final Gauge SCENE_OBJ_NUM_GAUGE = GeminiMonitor.newGauge("scene_obj_num", "entityType");
    // 缓存命中次数
    public static final Counter CACHE_HIT_TOTAL = GeminiMonitor.newCounter("cache_hit_total", "usage");
    // 缓存未命中次数
    public static final Counter CACHE_NOT_HIT_TOTAL = GeminiMonitor.newCounter("cache_not_hit_total", "usage");


    /**
     * 登录监控
     */
    // 网关侧在线玩家监控
    public static final Gauge GATE_PLAYER_ONLINE_NUM_GAUGE = GeminiMonitor.newGauge("gate_player_online_num");
    // 大世界侧在线玩家监控
    public static final Gauge SCENE_PLAYER_ONLINE_NUM_GAUGE = GeminiMonitor.newGauge("scene_player_online_num");
    // 被踢
    public static final Counter KICK_OFF_COUNTER = GeminiMonitor.newCounter("kick_off_total", "reason");
    // 账号登录失败
    public static final Counter ACCOUNT_LOGIN_FAIL_COUNTER = GeminiMonitor.newCounter("account_login_fail_total", "reason");
    // 角色登陆成功
    public static final Counter PLAYER_LOGIN_SUCCESS_COUNTER = GeminiMonitor.newCounter("player_login_success_total");
    // 角色登录失败
    public static final Counter PLAYER_LOGIN_FAIL_COUNTER = GeminiMonitor.newCounter("player_login_fail_total", "reason");
    // 注册失败次数
    public static final Counter ACCOUNT_REGISTER_FAIL_TOTAL = GeminiMonitor.newCounter("account_register_fail_total", "reason");


    /**
     * 第三方监控
     */
    // nats推送次数
    public static final Counter NATS_PUSH_COUNTER = GeminiMonitor.newCounter("nats_push_total");
    // nats接收次数
    public static final Counter NATS_RECEIVE_COUNTER = GeminiMonitor.newCounter("nats_receive_total");
    // nats大包次数
    public static final Counter NATS_BIG_MSG_PAYLOAD_COUNTER = GeminiMonitor.newCounter("nats_big_msg_payload_total");
    // 屏蔽字次数
    public static final Counter TEXT_FILTER_COUNTER = GeminiMonitor.newCounter("text_filter_total");
    // 屏蔽字高耗时次数
    public static final Counter TEXT_FILTER_COST_OVER_TIME_COUNTER = GeminiMonitor.newCounter("text_filter_cost_over_time_total");
    // 屏蔽字失败次数
    public static final Counter TEXT_FILTER_FAIL_TOTAL = GeminiMonitor.newCounter("text_filter_fail_total");
    // 鉴权次数
    public static final Counter AUTH_REQUEST_TOTAL = GeminiMonitor.newCounter("auth_request_total");
    public static final Counter AUTH_REQUEST_FAIL_TOTAL = GeminiMonitor.newCounter("auth_request_fail_total");
    public static final Counter AUTH_REQUEST_OVER_TIME_TOTAL = GeminiMonitor.newCounter("auth_request_over_time_total");
    // midas调用次数
    public static final Counter MIDAS_REQUEST_TOTAL = GeminiMonitor.newCounter("midas_request_total", "method");
    // midas高耗时调用次数
    public static final Counter MIDAS_REQUEST_OVER_TIME_TOTAL = GeminiMonitor.newCounter("midas_request_over_time_total", "method");
    // midas调用异常次数
    public static final Counter MIDAS_REQUEST_FAIL_TOTAL = GeminiMonitor.newCounter("midas_request_fail_total");
    // Player调用midas次数
    public static final Counter PLAYER_MIDAS_REQUEST_TOTAL = GeminiMonitor.newCounter("player_midas_request_total", "method");
    // Player调用midas失败次数
    public static final Counter PLAYER_MIDAS_REQUEST_FAIL_TOTAL = GeminiMonitor.newCounter("player_midas_request_fail_total", "reason");
    // midas回调次数
    public static final Counter MIDAS_CALLBACK_TOTAL = GeminiMonitor.newCounter("midas_callback_total");
    // midas回调处理包失败
    public static final Counter MIDAS_CALLBACK_FAIL_TOTAL = GeminiMonitor.newCounter("midas_callback_fail_total");
    // Player接收到midas回调的次数
    public static final Counter PLAYER_MIDAS_CALLBACK_TOTAL = GeminiMonitor.newCounter("player_midas_callback_total");
    // Player接收到midas回调发货失败
    public static final Counter PLAYER_MIDAS_CALLBACK_FAIL_TOTAL = GeminiMonitor.newCounter("player_midas_callback_fail_total");
    // midas三方支付次数
    public static final Counter MIDAS_WEB_BUY_TOTAL = GeminiMonitor.newCounter("midas_web_buy_total");
    // midas三方支付失败次数
    public static final Counter MIDAS_WEB_BUY_FAIL_TOTAL = GeminiMonitor.newCounter("midas_web_buy_fail_total");


    /**
     * tx运维侧自动开服工具
     */
    // 小服是否推荐
    public static final Gauge GAME_ZONE_STATUS_IS_RECOMMEND = GeminiMonitor.newGauge("game_zone_status_is_recommend", "worldId", "zoneId");
    // 小服开服时间
    public static final Gauge GAME_ZONE_STATUS_OPEN_TIME = GeminiMonitor.newGauge("game_zone_status_open_time", "worldId", "zoneId");
    // 小服在线人数
    public static final Gauge GAME_ZONE_STATUS_PLAYER_ONLINE_NUM = GeminiMonitor.newGauge("game_zone_status_player_online_num", "worldId", "zoneId");
    // 小服注册人数
    public static final Gauge GAME_ZONE_STATUS_PLAYER_REGISTER_NUM = GeminiMonitor.newGauge("game_zone_status_player_register_num", "worldId", "zoneId");
    // 小服注册上限
    public static final Gauge GAME_ZONE_STATUS_PLAYER_REGISTER_LIMIT = GeminiMonitor.newGauge("game_zone_status_player_register_limit", "worldId", "zoneId");
    // 小服是否对内
    public static final Gauge GAME_ZONE_STATUS_IS_INTERNAL = GeminiMonitor.newGauge("game_zone_status_is_internal", "worldId", "zoneId");
    /**
     * tx运维侧自动开服工具 end
     */


    public static final Gauge SS_OUT_MAX_QPS = GeminiMonitor.newGauge("ss_out_max_qps", "msgType");
    public static final Gauge SS_IN_AVG_COST = GeminiMonitor.newGauge("ss_in_avg_cost", "msgType");
    public static final Gauge SS_OUT_MAX_SIZE = GeminiMonitor.newGauge("ss_out_max_size", "msgType");

    public static final Gauge ACTOR_MSG_MAX_QPS = GeminiMonitor.newGauge("actor_msg_max_qps", "actorRole", "msgType");
    public static final Gauge ACTOR_MSG_AVG_COST = GeminiMonitor.newGauge("actor_msg_avg_cost", "actorRole", "msgType");


    /**
     * 启动参数
     */
    public static final Gauge OLD_MEM_SIZE = GeminiMonitor.newGauge("old_mem_size");
    public static final Gauge METASPACE_MAX_SIZE = GeminiMonitor.newGauge("metaspace_max_size");
    public static final Gauge FIBER_MAX_NUM = GeminiMonitor.newGauge("fiber_max_num");


    /**
     * 版本管理 start
     */
    public static final Gauge VERSION = GeminiMonitor.newGauge("version", "pkg_version");
    public static final Gauge GIT_BRANCH = GeminiMonitor.newGauge("git_branch", "branch_name");

}

package com.yorha.common.helper;

import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.node.IGateActor;
import com.yorha.common.actor.node.ISessionActor;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Core;
import com.yorha.proto.SsGateSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Map;

/**
 * session单传
 *
 * <AUTHOR>
 */
public class SessionHelper {
    public static final Logger LOGGER = LogManager.getLogger(SessionHelper.class);

    /**
     * 广播消息给Session。
     */
    public static void broadcastMsgToSessions(Collection<IActorRef> targetRefList, IActorRef sender, int msgType, GeneratedMessageV3 msg) {
        LinkedList<IActorRef> remoteRef = new LinkedList<>();
        for (final IActorRef ref : targetRefList) {
            // 在当前进程，则分发消息
            if (ServerContext.isSameNode(ref.getBusId())) {
                ActorSendMsgUtils.send(ref,
                        sender,
                        new ActorRunnable<ISessionActor>("Local#broadcastMsgToSessions", sessionActor -> sessionActor.getSession().answerMsg(msgType, 0, msg)));
                continue;
            }
            remoteRef.add(ref);
        }
        if (remoteRef.isEmpty()) {
            return;
        }
        //  存在非本地session，分发。
        broadcastMsgToSessions(remoteRef, sender, msgType, msg.toByteString());
    }

    /**
     * 广播消息给Session，直接序列化好的数据
     */
    public static void broadcastMsgToSessions(Collection<IActorRef> targetRefList, IActorRef sender, int msgType, ByteString msgBytes) {
        if (targetRefList.isEmpty()) {
            LOGGER.warn("msgType={} empty target! skip! {}", msgType, sender);
            return;
        }
        final SsGateSession.SendMsgToSessionCmd cmd = SsGateSession.SendMsgToSessionCmd.newBuilder()
                .setMsgType(msgType).setSeqId(0)
                .setMsgBytes(msgBytes)
                .build();
        BroadcastHelper.toAllTargets(targetRefList, sender, cmd);
    }

    /**
     * 广播多语言消息给Session。
     */
    public static void broadcastMsgToSessionsWithMultiLanguage(Collection<IActorRef> targetRefList, IActorRef sender, int msgType, Map<CommonEnum.Language, ByteString> msgMap) {
        if (targetRefList.isEmpty()) {
            LOGGER.warn("msg {} msgType={} empty target! skip!", msgMap, msgType);
            return;
        }
        SsGateSession.SendMsgToSessionWithLanguageCmd.Builder cmdBuilder = SsGateSession.SendMsgToSessionWithLanguageCmd.newBuilder();
        cmdBuilder.setMsgType(msgType).setSeqId(0);
        for (Map.Entry<CommonEnum.Language, ByteString> languageEntry : msgMap.entrySet()) {
            var languageId = languageEntry.getKey().getNumber();
            SsGateSession.LanguageMsgBytes languageBytes = SsGateSession.LanguageMsgBytes.newBuilder().setLanguageId(languageId).setMsgBytes(languageEntry.getValue()).build();
            cmdBuilder.putLanguageMap(languageEntry.getKey().getNumber(), languageBytes);
        }
        final SsGateSession.SendMsgToSessionWithLanguageCmd cmd = cmdBuilder.build();
        BroadcastHelper.toAllTargets(targetRefList, sender, cmd);
    }

    /**
     * 发送消息给session。(直传模式)
     */
    public static void sendMsgToSession(IActorRef receiver, AbstractActor sender, int msgType, GeneratedMessageV3 msg) {
        sendMsgToSessionWithSeqId(receiver, sender, msgType, 0, msg);
    }

    /**
     * 发送消息给session。(直传模式，指定seqId)
     */
    public static void sendMsgToSessionWithSeqId(IActorRef receiver, AbstractActor sender, int msgType, int seqId, GeneratedMessageV3 msg) {
        if (receiver == null) {
            LOGGER.error("sendMsgToSessionWithSeqId but receiver is null, {} {} {}", sender, msgType, seqId);
            return;
        }
        if (ServerContext.isSameNode(receiver.getBusId())) {
            sender.tell(receiver, new ActorRunnable<ISessionActor>("SessionHelper#sendMsgToSessionWithSeqId",
                    (sessionActor) -> sessionActor.getSession().answerMsg(msgType, seqId, msg)));
            return;
        }
        SsGateSession.SendMsgToSessionCmd.Builder cmdBuilder = SsGateSession.SendMsgToSessionCmd.newBuilder();
        cmdBuilder.setMsgType(msgType).setSeqId(seqId);
        cmdBuilder.setMsgBytes(msg.toByteString());
        sender.tell(receiver, cmdBuilder.build());
    }

    /**
     * 发送错误码给session。(直传模式)
     */
    public static void sendErrorCodeToSession(IActorRef receiver, AbstractActor sender, int msgType, int seqId, Core.Code code) {
        if (receiver == null) {
            LOGGER.error("sendErrorCodeToSession but receiver is null, {} {} {} {}", sender, msgType, seqId, code);
            return;
        }
        if (ServerContext.isSameNode(receiver.getBusId())) {
            sender.tell(receiver, new ActorRunnable<ISessionActor>("SessionHelper#sendErrorCodeToSession",
                    (sessionActor) -> sessionActor.getSession().answerErrorCode(msgType, seqId, code)));
            return;
        }
        SsGateSession.SendMsgToSessionCmd.Builder cmdBuilder = SsGateSession.SendMsgToSessionCmd.newBuilder();
        cmdBuilder.setMsgType(msgType).setSeqId(seqId).setCode(code);
        SsGateSession.SendMsgToSessionCmd cmd = cmdBuilder.build();
        sender.tell(receiver, new TypedMsg(SsMsgTypes.getTypeFromMsg(cmd), cmd));
    }

    /**
     * 关闭所有连接
     */
    public static void kickOffAllSession(CommonEnum.SessionCloseReason reason) {
        LOGGER.info("kickOffAllSession reason={}", reason);
        if (!ServerContext.isZoneServer()) {
            WechatLog.error("kickOffAllSession must be in zone server");
            return;
        }

        IActorRef ref = RefFactory.ofLocalGate();
        ActorSendMsgUtils.send(ref, new ActorRunnable<IGateActor>("kickOffAllSession", iGateActor -> {
            iGateActor.kickOffAllSession(reason);
        }));
    }
}

package com.yorha.common.resource.resservice.points;

import com.yorha.common.constant.Constants;
import res.template.ActivityPointsTemplate;
import res.template.MonsterTemplate;

import static com.yorha.proto.CommonEnum.MonsterCategory;

public class PointsByKillMonsterConf extends PointsConf {
    private final MonsterCategory category;
    private final int minLv;
    private final int maxLv;

    public PointsByKillMonsterConf(ActivityPointsTemplate template) {
        super(template);
        this.category = MonsterCategory.valueOf(template.getParam1());
        String[] lvPair = template.getParam2().split(Constants.MAO_HAO);
        this.minLv = Integer.parseInt(lvPair[0]);
        this.maxLv = Integer.parseInt(lvPair[1]);
    }

    public boolean matched(MonsterTemplate monsterTemplate) {
        if (monsterTemplate.getCategory() != this.category) {
            return false;
        }

        if (monsterTemplate.getLevel() < minLv) {
            return false;
        }

        if (monsterTemplate.getLevel() > maxLv) {
            return false;
        }
        
        return true;
    }

    public MonsterCategory getCategory() {
        return category;
    }
}

package com.yorha.common.dbactor;

import com.google.protobuf.Message;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.UpdateAsk;
import com.yorha.common.db.tcaplus.result.DeleteResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.UpdateResult;

import javax.annotation.Nullable;
import java.util.function.BiConsumer;

/**
 * DB操作的策略。
 *
 * <AUTHOR>
 */
public interface IDbOperationStrategy {
    /**
     * 异步更新Db。
     *
     * @param actor    actor。
     * @param ask      请求。
     * @param callback 回调(为null则忽略)
     */
    void updateAsync(AbstractActor actor, UpdateAsk<Message.Builder> ask, @Nullable BiConsumer<UpdateResult<Message.Builder>, Throwable> callback);

    /**
     * 同步更新数据库。
     *
     * @param actor actor。
     * @param ask   请求。
     * @return 返回结果。
     */
    UpdateResult<Message.Builder> updateSync(AbstractActor actor, UpdateAsk<Message.Builder> ask);

    /**
     * 异步插入数据库。
     *
     * @param actor    actor。
     * @param ask      请求。
     * @param callback 返回结果(为null则忽略)
     */
    void insertAsync(AbstractActor actor, InsertAsk<Message.Builder> ask, @Nullable BiConsumer<InsertResult<Message.Builder>, Throwable> callback);

    /**
     * 同步插入。
     *
     * @param actor actor。
     * @param ask   请求。
     * @return 返回结果。
     */
    InsertResult<Message.Builder> insertSync(AbstractActor actor, InsertAsk<Message.Builder> ask);

    /**
     * 异步删除。
     *
     * @param actor    actor。
     * @param ask      请求。
     * @param callback 回调。
     */
    void deleteAsync(AbstractActor actor, DeleteAsk<Message.Builder> ask, BiConsumer<DeleteResult, Throwable> callback);

}


package com.yorha.common.resource.datatype;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class IntTripleArray extends ArrayList<IntTripleType> {
    private static final String COMMA = ",";

    public IntTripleArray(List collect) {
        super(collect);
    }

    public IntTripleArray() {

    }

    public static IntTripleArray valueOf(String s) {
        IntTripleArray ret = new IntTripleArray();
        Arrays.stream(s.split(COMMA)).map(e -> ret.add(IntTripleType.valueOf(e))).toList();
        return ret;
    }
}

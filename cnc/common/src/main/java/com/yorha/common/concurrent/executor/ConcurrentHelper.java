package com.yorha.common.concurrent.executor;

import com.yorha.common.concurrent.NameableThreadFactory;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.*;

/**
 * 并发工具包。
 *
 * <AUTHOR>
 */
public final class ConcurrentHelper {
    private static final Logger LOGGER = LogManager.getLogger(ConcurrentHelper.class);

    public static final Thread.UncaughtExceptionHandler UNCAUGHT_EXCEPTION_HANDLER = (thread, err) -> {
        if (thread.isVirtual()) {
            LOGGER.error("fiber quit for exception! fiber={}!", thread, err);
        } else {
            LOGGER.error("thread quit for exception! thread={}! ", thread, err);
        }
    };

    private ConcurrentHelper() {
    }

    /**
     * 新建fiber的构造工厂。
     *
     * @param name      名称。
     * @param scheduler 调度器。
     * @return ThreadFactory。
     */
    public static ThreadFactory newFiberFactory(final String name, final GeminiThreadPoolExecutor scheduler) {
        return newFiberFactory(name, scheduler, UNCAUGHT_EXCEPTION_HANDLER);
    }

    /**
     * 新建fiber工厂。
     *
     * @param name      名称。
     * @param scheduler 调度器。
     * @param handler   错误回调。
     * @return ThreadFactory。
     */
    public static ThreadFactory newFiberFactory(final String name, final GeminiThreadPoolExecutor scheduler, Thread.UncaughtExceptionHandler handler) {
        if (scheduler == null) {
            throw new NullPointerException("scheduler is null!");
        }
        ThreadFactory factory = Thread.ofVirtual().name(name + "-fiber-", 0)
                // .scheduler(scheduler)
                // Sets whether the thread inherits the initial values of inheritable-thread-local variables. The default is to inherit.
                .inheritInheritableThreadLocals(true)
                // Sets whether the thread is allowed to set values for its copy of thread-local variables.
                // .allowSetThreadLocals(true)
                .uncaughtExceptionHandler(handler)
                .factory();
        // 在 JDK 21 中，虚拟线程不再支持直接绑定 scheduler，而是通过设置 ThreadFactory 到线程池来实现相同效果
        scheduler.setThreadFactory(factory);
        return factory;
    }

    /**
     * 新建线程工厂。
     *
     * @param name                     名称。
     * @param isDaemon                 是否守护线程。
     * @param uncaughtExceptionHandler 异常捕捉器。
     * @return ThreadFactory。
     */
    public static ThreadFactory newThreadFactory(final String name, final boolean isDaemon, final Thread.UncaughtExceptionHandler uncaughtExceptionHandler) {
        return Thread.ofPlatform()
                .name(name + "-", 0)
                .daemon(isDaemon)
                .uncaughtExceptionHandler(uncaughtExceptionHandler)
                .factory();
    }

    /**
     * 开启一个协程。
     * 默认将使用当前协程的调度器调度此协程。
     * 如果不存在当前协程，则使用默认的全局ForkJoinPool来调度协程。
     *
     * @param name     协程名。
     * @param runnable 协程任务。
     * @return 协程。
     */
    public static Thread newFiber(final String name, final Runnable runnable) {
        return Thread.ofVirtual()
                .name(name)
                // Sets whether the thread inherits the initial values of inheritable-thread-local variables. The default is to inherit.
                .inheritInheritableThreadLocals(true)
                .uncaughtExceptionHandler(UNCAUGHT_EXCEPTION_HANDLER)
                .unstarted(runnable);
    }

    /**
     * 开启线程。
     *
     * @param name     线程名称。
     * @param isDaemon 是否守护线程。
     * @param runnable 可执行任务。
     * @return 线程。
     */
    public static Thread newThread(final String name, final boolean isDaemon, final Runnable runnable) {
//        return newThreadFactory(name, isDaemon, UNCAUGHT_EXCEPTION_HANDLER).newThread(runnable);
        return Thread.ofPlatform()
                .name(name)
                .daemon(isDaemon)
                .uncaughtExceptionHandler(UNCAUGHT_EXCEPTION_HANDLER)
                .unstarted(runnable);
    }

    /**
     * 创建一个单消费线程的任务队列。
     *
     * @param name         线程池名称。
     * @param taskCapacity 任务队列上限。
     * @param isDaemon     是否守护线程。
     * @return 线程池。
     */
    public static GeminiThreadPoolExecutor newSingleThreadExecutor(final String name, final int taskCapacity, final boolean isDaemon) {
        return GeminiThreadPoolExecutor.newBuilder(name, 1)
                .taskSize(taskCapacity)
                .daemon(isDaemon)
                .uncaughtExecutionHandler(UNCAUGHT_EXCEPTION_HANDLER)
                .build();
    }

    /**
     * 创建一个单消费线程的任务队列。
     * 自定义rejectedExecutionHandler
     *
     * @param name         线程池名称。
     * @param taskCapacity 任务队列上限。
     * @param isDaemon     是否守护线程。
     * @return 线程池。
     */
    public static GeminiThreadPoolExecutor newSingleThreadExecutor(
            final String name,
            final int taskCapacity,
            final boolean isDaemon,
            @NotNull RejectedExecutionHandler handler
    ) {
        return GeminiThreadPoolExecutor.newBuilder(name, 1)
                .taskSize(taskCapacity)
                .daemon(isDaemon)
                .rejectExecuteHandler(handler)
                .uncaughtExecutionHandler(UNCAUGHT_EXCEPTION_HANDLER)
                .build();
    }

    /**
     * 创建固定数量的线程池。
     *
     * @param name         线程池名称。
     * @param threadSize   线程数量。
     * @param taskCapacity 任务队列上限。
     * @param isDaemon     是否守护线程。
     * @return 线程池。
     */
    public static GeminiThreadPoolExecutor newFixedThreadExecutor(final String name, final int threadSize, final int taskCapacity, final boolean isDaemon) {
        return GeminiThreadPoolExecutor.newBuilder(name, threadSize)
                .threadSize(threadSize)
                .taskSize(taskCapacity)
                .daemon(isDaemon)
                .uncaughtExecutionHandler(UNCAUGHT_EXCEPTION_HANDLER)
                .build();
    }

    /**
     * 创建固定协程数量的协程池。
     *
     * @param name      协程池名称。
     * @param scheduler 调度器。
     * @param fiberSize 最大fiber上限。
     * @param taskSize  消息上限。
     * @return 协程池。
     */
    public static GeminiFiberPoolExecutor newFixedFiberExecutor(
            final String name,
            final GeminiThreadPoolExecutor scheduler,
            final int fiberSize,
            final int taskSize
    ) {
        return GeminiFiberPoolExecutor.newBuilder(name, scheduler, fiberSize)
                .taskSize(taskSize)
                .uncaughtExecutionHandler(UNCAUGHT_EXCEPTION_HANDLER)
                .build();
    }

    public static ForkJoinPool newForkJoinPool(final String name, int parallelism) {
        final ForkJoinPool.ForkJoinWorkerThreadFactory forkJoinPoolFactory = pool -> {
            final ForkJoinWorkerThread worker = ForkJoinPool.defaultForkJoinWorkerThreadFactory.newThread(pool);
            worker.setName(name + worker.getPoolIndex());
            worker.setDaemon(false);
            return worker;
        };
        return new ForkJoinPool(
                parallelism,
                forkJoinPoolFactory,
                (t, e) -> WechatLog.error("{} refuse for", name, t.getName(), e),
                true
        );
    }

    public static ScheduledThreadPoolExecutor newSingleThreadScheduledExecutor(final String name) {
        return new ScheduledThreadPoolExecutor(1, new NameableThreadFactory(name, true));
    }
}

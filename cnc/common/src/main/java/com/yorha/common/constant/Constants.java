package com.yorha.common.constant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface Constants {

    String SEPARATOR_WINDOWS = "\\";
    String SEPARATOR_LINUX = "/";
    char DELIM_LEFT = '{';
    char DELIM_RIGHT = '}';

    String FEN_HAO = ";|；";
    String DOU_HAO = ",|，";
    String SHU_XIAN = "\\|";
    String XIA_HUA_XIAN = "_";
    String MAO_HAO = ":|：";
    String BAN_JIAO_DOU_HAO = ",";
    String BAN_JIAO_FEN_HAO = ";";

    /**
     * 角度最大值
     */
    int ANGLE_MAX_VALUE = 360;

    /**
     * 精度转换安全值
     */
    int ACCURACY_CONVERSION_SAFETY_VALUE = 100;

    /**
     * 百分比转换
     */
    double CONVERT_HUNDRED_POINTS = 100.00;

    /**
     * 万分比转换
     */
    double CONVERT_TEN_THOUSAND_POINTS = 10000.00;

    int N_100 = 100;
    int N_1000 = 1000;
    int N_10_000 = 10000;
    long N_100L = 100L;
    long N_1000L = 1000L;
    long N_10_000L = 10000L;


    /**
     * 初始int值
     */
    int INT_INIT_VALUE = 0;

    String SPLIT_FLAG = "?";
    String VERTICAL_BAR_SPLIT_FLAG = "|";

    /**
     * 战斗日志的重置时间
     */
    int BATTLE_LOG_RESET_TIME_SEC = 900;

    /**
     * 里程碑发奖分流
     */
    int MILESTONE_REWARD_BASE = 100;
    

    int SIMULATOR_OUT_TIME_SEC = 600;


    /**
     * 约束角度 到[0,360)
     */
    static int curbAngle(int angle) {
        angle -= angle / 360 * 360;
        if (angle < 0) {
            return angle + 360;
        }
        return angle;
    }

    int INNER_GRID_X_OFFSET = 1000;

    /**
     * SceneObj属性比率日志打印时长ms
     */
    int PRINT_RATIO_MS = 10000;

    /**
     * 里程碑没有奖励标识
     */
    int MILESTONE_NOT_REWARD_TAG = -1;

    int IDIP_MARQUEE_ID = 10000001;
}

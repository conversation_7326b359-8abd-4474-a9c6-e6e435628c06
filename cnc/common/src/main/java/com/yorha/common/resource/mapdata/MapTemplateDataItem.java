package com.yorha.common.resource.mapdata;

import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.boolmap.BoolMap;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.shape.Line;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.MapBuildingTemplate;
import res.template.MapConfigTemplate;
import res.template.TerritoryBuildingTemplate;

import java.util.*;

/**
 * 根据地图id分表的数据中心
 *
 * <AUTHOR>
 */
public class MapTemplateDataItem {
    /**
     * 地图id
     */
    private final int mapId;
    /**
     * 数据源
     */
    private final Map<Class<? extends MapSubdivisionTemplate>, Map<Integer, MapSubdivisionTemplate>> resTemplateDataStore = new HashMap<>();
    /**
     * 州Id -> 可出生的片List
     */
    private final Map<Integer, Map<CommonEnum.MapAreaType, List<Integer>>> entityCanBornPart = new HashMap<>();
    /**
     * 州id1，州id2 -> 关卡的片id
     */
    private final Map<Pair<Integer, Integer>, List<Integer>> crossingPart = new HashMap<>();
    /**
     * 有效的片个数 (排除阻挡、不可出生区域（河桥）)  州id->个数  上面那个的统计
     */
    private final Map<Integer, Integer> bornPartNum = new HashMap<>();


    // 村庄奖励 fogGridId->rewardId
    private final Map<Integer, Integer> fogGridReward = new HashMap<>();
    // 探索邮件 fogGridId->mailId
    private final Map<Integer, Integer> fogGridMail = new HashMap<>();
    // 调查邮件 fogGridId->mailId
    private final Map<Integer, Integer> fogGridRewardMail = new HashMap<>();
    // 建筑解锁迷雾 fogGridId->openView
    private final Map<Integer, Integer> fogGridOpenView = new HashMap<>();
    // pair partId_config (旧配置id_新配置id) 需求要兼容两张表真烦
    private final Map<Integer, List<Pair<Integer, Integer>>> fogGridToConfig = new HashMap<>();

    public MapTemplateDataItem(int mapId) {
        this.mapId = mapId;
    }

    public List<Integer> getEntityCanBornPartList(int regionId, CommonEnum.MapAreaType areaType) {
        if (!entityCanBornPart.containsKey(regionId)) {
            return new ArrayList<>();
        }
        return entityCanBornPart.get(regionId).get(areaType);
    }

    /**
     * 获取两个州相连的关隘
     */
    public Set<Pair<Integer, Integer>> getCrossingRegion() {
        return crossingPart.keySet();
    }

    /**
     * 获取全通的跨州关卡列表
     */
    public List<Integer> getRegionPartIdList(int region1, int region2) {
        int firstRegion = Math.min(region1, region2);
        int secondRegion = Math.max(region1, region2);
        Pair<Integer, Integer> key = Pair.of(firstRegion, secondRegion);
        return crossingPart.get(key);
    }

    /**
     * 获取可出生的片列表
     */
    public Map<CommonEnum.MapAreaType, List<Integer>> getRegionBornPartList(int regionId) {
        return entityCanBornPart.get(regionId);
    }

    public int randomEffectivePart(int regionId) {
        Map<CommonEnum.MapAreaType, List<Integer>> mapAreaTypeListMap = entityCanBornPart.get(regionId);
        int index = RandomUtils.nextInt(bornPartNum.get(regionId));
        for (Map.Entry<CommonEnum.MapAreaType, List<Integer>> entry : mapAreaTypeListMap.entrySet()) {
            int size = entry.getValue().size();
            if (index < size) {
                return entry.getValue().get(index);
            }
            index -= size;
        }
        return -1;
    }

    /**
     * 找能连通到这个点的 这个关卡的城门线交点
     */
    public Point getPointWithSrcAndAllCross(BoolMap regionMap, Point p, int partId, int regionId) {
        RegionalAreaSettingTemplate template = getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        List<Integer> path1 = regionMap.findShortestPath(regionId, template.getLinkRegionList().get(0));
        List<Integer> path2 = regionMap.findShortestPath(regionId, template.getLinkRegionList().get(1));
        if (path1.size() > path2.size()) {
            return getCrossPointWithLine(p, partId, template.getLink2List());
        }
        return getCrossPointWithLine(p, partId, template.getLink1List());
    }

    public Point getCrossPointWithLine(Point p, int partId, List<Integer> line) {
        RegionalAreaSettingTemplate template = getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        Point end = Point.valueOf(template.getPosX(), template.getPosY());
        Point p1 = Point.valueOf(line.get(0), line.get(1));
        Point p2 = Point.valueOf(line.get(2), line.get(3));
        Line cross = Line.valueOf(p1, p2);
        Point crossPoint = cross.getCrossWithLine(Line.valueOf(p, end));
        if (crossPoint != null) {
            return crossPoint;
        }
        double d1 = Point.calDisBetweenTwoPoint(p, p1);
        double d2 = Point.calDisBetweenTwoPoint(p, p2);
        if (d1 <= d2) {
            return p1;
        }
        return p2;
    }

    private List<Integer> getCrossRegionLine(int partId, int regionId) {
        RegionalAreaSettingTemplate template1 = getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        if (template1.getLinkRegionList().get(0) == regionId) {
            return template1.getLink1List();
        }
        if (template1.getLinkRegionList().get(1) == regionId) {
            return template1.getLink2List();
        }
        return null;
    }

    /**
     * 获取两个关卡之间的移动起始点
     */
    public Line getSrcAndEndBetweenCross(int regionId, int partId1, int partId2) {
        List<Integer> lineList1 = getCrossRegionLine(partId1, regionId);
        List<Integer> lineList2 = getCrossRegionLine(partId2, regionId);
        if (lineList1 == null || lineList2 == null) {
            return null;
        }
        Point ret1;
        Point ret2;
        Point p1 = Point.valueOf(lineList1.get(0), lineList1.get(1));
        Point p2 = Point.valueOf(lineList1.get(2), lineList1.get(3));
        Point p3 = Point.valueOf(lineList2.get(0), lineList2.get(1));
        Point p4 = Point.valueOf(lineList2.get(2), lineList2.get(3));
        double d1 = Point.calDisBetweenTwoPoint(p1, p3);
        double d2 = Point.calDisBetweenTwoPoint(p2, p3);
        double d3;
        if (d1 <= d2) {
            ret1 = p1;
            d3 = d1;
        } else {
            ret1 = p2;
            d3 = d2;
        }
        double d4 = Point.calDisBetweenTwoPoint(ret1, p4);
        if (d3 <= d4) {
            ret2 = p3;
        } else {
            ret2 = p4;
        }
        return Line.valueOf(ret1, ret2);
    }

    /**
     * 获取起点和关卡的移动交点
     */
    public Point getPointWithSrcAndCross(Point p, int partId, int regionId) {
        List<Integer> line = getCrossRegionLine(partId, regionId);
        if (line == null) {
            return null;
        }
        return getCrossPointWithLine(p, partId, line);
    }

    /**
     * 获取迷雾配置
     */
    public List<Pair<Integer, Integer>> getFogGridToConfig(int fogGrid) {
        return fogGridToConfig.getOrDefault(fogGrid, new ArrayList<>());
    }

    public int getFogRewardMailId(int fogGridId) {
        return fogGridRewardMail.getOrDefault(fogGridId, 0);
    }

    /**
     * 获取探索邮件id，因为新老配置要兼容，返回值为 configId_mailId
     */
    public int getFogMailId(int fogGridId) {
        return fogGridMail.getOrDefault(fogGridId, 0);
    }

    public int getFogOpenViewId(int fogGridId) {
        return fogGridOpenView.getOrDefault(fogGridId, 0);
    }

    public int getFogRewardId(int fogGridId) {
        return fogGridReward.getOrDefault(fogGridId, 0);
    }

    /**
     * 获取某个模板的一条记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @param id    索引
     * @return 返回模板的一条记录
     */
    @SuppressWarnings("unchecked")
    public <T extends MapSubdivisionTemplate> T getValueFromMap(Class<T> clazz, int id) {
        Map<Integer, T> array = (Map<Integer, T>) resTemplateDataStore.get(clazz);
        T result = array.get(id);
        if (result == null) {
            String format = StringUtils.format("配置id不存在. detail->> mapId={} id:{} {}不存在", mapId, id, ClassNameCacheUtils.getSimpleName(clazz));
            WechatLog.error(format);
            throw new GeminiException(format);
        }
        return result;
    }

    /**
     * 获取某个模板的所有记录
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @return 返回顺序列表
     */
    @SuppressWarnings("unchecked")

    public <T extends MapSubdivisionTemplate> Map<Integer, T> getMap(Class<T> clazz) throws GeminiException {
        Map<Integer, T> ret = (Map<Integer, T>) resTemplateDataStore.get(clazz);
        if (ret == null) {
            throw new GeminiException(StringUtils.format("{}没有配置数据. size=0", ClassNameCacheUtils.getSimpleName(clazz)));
        }
        return ret;
    }

    /**
     * 获取某个模板的一条记录，找不到也不会抛异常，而是返回null
     *
     * @param <T>   模板类型
     * @param clazz 表名
     * @param id    索引
     * @return 返回模板的一条记录
     */
    @SuppressWarnings("unchecked")
    public <T extends MapSubdivisionTemplate> T findValueFromMap(Class<T> clazz, int id) {
        Map<Integer, T> array = (Map<Integer, T>) resTemplateDataStore.get(clazz);
        return array.get(id);
    }

    public <T extends MapSubdivisionTemplate> void addTemplateData(
            ResHolder resHolder,
            Map<Integer, IResTemplate> source,
            Class<T> clazz
    ) throws ResourceException {
        Map<Integer, MapSubdivisionTemplate> map = resTemplateDataStore.computeIfAbsent(clazz, k -> new HashMap<>());
        for (Map.Entry<Integer, IResTemplate> entry : source.entrySet()) {
            String s = JsonUtils.toJsonString(entry.getValue());
            T data = JsonUtils.parseObject(s, clazz);
            map.put(entry.getKey(), data);
            if (data instanceof RegionalAreaSettingTemplate) {
                checkRegionalAreaSettingTemplate(resHolder, (RegionalAreaSettingTemplate) data);
            }
        }

    }

    public void afterBuildTemplateCheck(ResHolder resHolder) throws ResourceException {
        entityCanBornPart.forEach((k, v) -> {
            int num = 0;
            for (Map.Entry<CommonEnum.MapAreaType, List<Integer>> entry : v.entrySet()) {
                num += entry.getValue().size();
            }
            bornPartNum.put(k, num);
        });
    }

    private void checkRegionalAreaSettingTemplate(ResHolder resHolder, RegionalAreaSettingTemplate template) throws ResourceException {
        // 处理实体出生部分
        processEntityBornPart(template);

        // 处理关隘列表构建
        processCrossingPart(template);

        // 建筑合法性检测
        if (template.getBuildingId() == 0) {
            return;
        }

        MapBuildingTemplate mapBuildingTemplate = validateBuildingConfiguration(resHolder, template);
        validatePositionCoordinates(resHolder, template);
        validateAreaTypeAndBuildingType(template, mapBuildingTemplate);
    }

    /**
     * 处理实体出生部分 - 关隘、卡巴尔营地、阻挡不能作为出生
     */
    private void processEntityBornPart(RegionalAreaSettingTemplate template) {
        if (!BigSceneConstants.isBanEntityBornPart(template.getAreaType())) {
            entityCanBornPart.computeIfAbsent(template.getRegionId(),
                    k -> new HashMap<>()).computeIfAbsent(template.getAreaType(), k -> new ArrayList<>()).add(template.getId());
        }
    }

    /**
     * 处理关隘列表构建 - 构建州联通的关隘列表
     */
    private void processCrossingPart(RegionalAreaSettingTemplate template) {
        if (template.getAreaType() == CommonEnum.MapAreaType.CROSSING) {
            int firstRegion = Math.min(template.getLinkRegionList().get(0), template.getLinkRegionList().get(1));
            int secondRegion = Math.max(template.getLinkRegionList().get(0), template.getLinkRegionList().get(1));
            Pair<Integer, Integer> key = Pair.of(firstRegion, secondRegion);
            if (!crossingPart.containsKey(key)) {
                crossingPart.put(key, new ArrayList<>());
            }
            crossingPart.get(key).add(template.getId());
        }
    }

    /**
     * 验证建筑配置合法性
     */
    private MapBuildingTemplate validateBuildingConfiguration(ResHolder resHolder, RegionalAreaSettingTemplate template) throws ResourceException {
        MapBuildingTemplate mapBuildingTemplate = resHolder.getValueFromMap(MapBuildingTemplate.class, template.getBuildingId());
        // 检查配置的建筑id是否存在
        if (mapBuildingTemplate == null) {
            throw new ResourceException(StringUtils.format("配置的建筑id不存在 MapBuildingTemplate. buildId:{}", template.getBuildingId()));
        }
        // 联盟据点还得检测联盟领地表
        if (GameLogicConstants.isMapStrongholdOrCity(template.getAreaType())) {
            resHolder.checkValueFromMap(TerritoryBuildingTemplate.class, template.getBuildingId(),
                    () -> StringUtils.format("联盟领地表联盟据点不存在 TerritoryBuildingTemplate. buildId:{}", template.getBuildingId()));
        }
        return mapBuildingTemplate;
    }

    /**
     * 验证坐标合法性
     */
    private void validatePositionCoordinates(ResHolder resHolder, RegionalAreaSettingTemplate template) throws ResourceException {
        // 没办法 没地方拿大地图的id 只能显式写1004了
        int width = UnitConvertUtils.meterToCm(resHolder.checkValueFromMap(MapConfigTemplate.class, 1004,
                        () -> StringUtils.format("检测大地图宽度,地图id不存在 MapConfigTemplate. buildId:{}", 100))
                .getWidth());
        int height = UnitConvertUtils.meterToCm(resHolder.checkValueFromMap(MapConfigTemplate.class, 1004,
                        () -> StringUtils.format("检测大地图长度,地图id不存在 MapConfigTemplate. buildId:{}", 1004))
                .getLength());
        // 检查下坐标合法性
        if (template.getPosX() < 0 || template.getPosX() >= width) {
            throw new ResourceException(StringUtils.format("region part: {}, building pos error.", template.getId()));
        }
        if (template.getPosY() < 0 || template.getPosY() >= height) {
            throw new ResourceException(StringUtils.format("region part: {}, building pos error.", template.getId()));
        }
        if (template.getPosX() == 0 && template.getPosY() == 0) {
            throw new ResourceException(StringUtils.format("region part: {}, building pos error.", template.getId()));
        }
    }

    /**
     * 验证区域类型和建筑类型匹配
     */
    private void validateAreaTypeAndBuildingType(RegionalAreaSettingTemplate template, MapBuildingTemplate mapBuildingTemplate) throws ResourceException {
        CommonEnum.MapBuildingType type = mapBuildingTemplate.getType();
        switch (template.getAreaType()) {
            case CROSSING:
                validateCrossingAreaType(template, type);
                break;
            case TERRITORY:
                validateTerritoryAreaType(template, type);
                break;
            case CITY_ONE:
                validateCityOneAreaType(template, type);
                break;
            case CITY_TWO:
                validateCityTwoAreaType(template, type);
                break;
            case CITY_THREE:
                validateCityThreeAreaType(template, type);
                break;
            case CITY_FOUR:
                validateCityFourAreaType(template, type);
                break;
            default:
                break;
        }
    }

    /**
     * 验证关隘区域类型
     */
    private void validateCrossingAreaType(RegionalAreaSettingTemplate template, CommonEnum.MapBuildingType type) throws ResourceException {
        // 配置建筑类型检验
        if (type != CommonEnum.MapBuildingType.MBT_PASS) {
            throw new ResourceException(StringUtils.format("地缘建筑表报错 partId: {}, 配置的建筑id对应类型不是关隘.", template.getId()));
        }
        // 联通州校验
        if (template.getLinkRegionList() == null || template.getLinkRegionList().size() != 2) {
            throw new ResourceException(StringUtils.format("region part: {}, LinkRegionList error.", template.getId()));
        }
        if (template.getLink1List() == null || template.getLink1List().size() != 4) {
            throw new ResourceException(StringUtils.format("region part: {}, getLink1List error.", template.getId()));
        }
        if (template.getLink2List() == null || template.getLink2List().size() != 4) {
            throw new ResourceException(StringUtils.format("region part: {}, getLink2List error.", template.getId()));
        }
    }

    /**
     * 验证据点区域类型
     */
    private void validateTerritoryAreaType(RegionalAreaSettingTemplate template, CommonEnum.MapBuildingType type) throws ResourceException {
        // 配置建筑类型检验
        if (type != CommonEnum.MapBuildingType.MBT_TERRITORY_CITY) {
            throw new ResourceException(StringUtils.format("地缘建筑表报错 partId: {}, 配置的建筑id对应类型不是据点.", template.getId()));
        }
    }

    /**
     * 验证一级城区域类型
     */
    private void validateCityOneAreaType(RegionalAreaSettingTemplate template, CommonEnum.MapBuildingType type) throws ResourceException {
        // 配置建筑类型检验
        if (type != CommonEnum.MapBuildingType.MBT_CITY_ONT) {
            throw new ResourceException(StringUtils.format("地缘建筑表报错 partId: {}, 配置的建筑id对应类型不是一级城.", template.getId()));
        }
    }

    /**
     * 验证二级城区域类型
     */
    private void validateCityTwoAreaType(RegionalAreaSettingTemplate template, CommonEnum.MapBuildingType type) throws ResourceException {
        // 配置建筑类型检验
        if (type != CommonEnum.MapBuildingType.MBT_CITY_TWO) {
            throw new ResourceException(StringUtils.format("地缘建筑表报错 partId: {}, 配置的建筑id对应类型不是二级城.", template.getId()));
        }
    }

    /**
     * 验证三级城区域类型
     */
    private void validateCityThreeAreaType(RegionalAreaSettingTemplate template, CommonEnum.MapBuildingType type) throws ResourceException {
        // 配置建筑类型检验
        if (type != CommonEnum.MapBuildingType.MBT_CITY_THREE) {
            throw new ResourceException(StringUtils.format("地缘建筑表报错 partId: {}, 配置的建筑id对应类型不是三级城.", template.getId()));
        }
    }

    /**
     * 验证四级城区域类型
     */
    private void validateCityFourAreaType(RegionalAreaSettingTemplate template, CommonEnum.MapBuildingType type) throws ResourceException {
        // 配置建筑类型检验
        if (type != CommonEnum.MapBuildingType.MBT_CITY_FOUR && type != CommonEnum.MapBuildingType.MBT_ROYAL_CITY) {
            throw new ResourceException(StringUtils.format("地缘建筑表报错 partId: {}, 配置的建筑id对应类型不是四级城市.", template.getId()));
        }
    }
}

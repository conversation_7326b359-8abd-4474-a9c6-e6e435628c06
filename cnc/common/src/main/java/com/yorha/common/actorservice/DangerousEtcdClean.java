package com.yorha.common.actorservice;

import com.yorha.common.etcd.EtcdClient;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DangerousEtcdClean {
    private static final Logger LOGGER = LogManager.getLogger(DangerousEtcdClean.class);

    private static final int WORLD_ID = 101;

    public static void main(String[] args) {
        LOGGER.info("begin clean");

        String[] etcdAddressArray = new String[3];
        etcdAddressArray[0] = "http://test-etcd.internal.com:2379";
        etcdAddressArray[1] = "http://test-etcd.internal.com:2379";
        etcdAddressArray[2] = "http://test-etcd.internal.com:2379";
        EtcdClient etcdClient = new EtcdClient(etcdAddressArray, null, null);

        delete(etcdClient, StringUtils.format("demo/{}/node", WORLD_ID));
        delete(etcdClient, StringUtils.format("demo/{}/leader", WORLD_ID));
        delete(etcdClient, StringUtils.format("demo/{}/cluster", WORLD_ID));
        delete(etcdClient, StringUtils.format("demo/{}/shard", WORLD_ID));

        LOGGER.info("clean ok");
    }

    private static void delete(EtcdClient etcdClient, String key) {
        etcdClient.deletePrefix(key);
    }
}

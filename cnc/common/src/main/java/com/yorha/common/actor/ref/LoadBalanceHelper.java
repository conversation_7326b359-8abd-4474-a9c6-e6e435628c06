package com.yorha.common.actor.ref;

import com.yorha.common.actor.cluster.toplogy.Node;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.utils.RandomUtils;

import java.util.List;

/**
 * 负载均衡策略池。
 *
 * <AUTHOR>
 */
public final class LoadBalanceHelper {
    private LoadBalanceHelper() {
    }

    /**
     * hash负载均衡，获取整个ActorSystem中符合预期的节点busId。
     *
     * @param actorSystem 系统。
     * @param serverType  服务器类型。
     * @param actorId     actor id。
     * @return 无符合要求的节点 null; 符合要求的busId。
     */
    public static String getHashBusId(final ActorSystem actorSystem, final int serverType, String actorId) {
        // 拿到这个ActorRole所有的busId，根据actorId的hashCode取模选择一个。
        final List<Node> nodeList = actorSystem.getRegistryValue().getServerTypeNodeList(serverType);
        if (nodeList.isEmpty()) {
            return null;
        }
        // hashCode可能是负数，所以需要用绝对值取模。
        return nodeList.get(Math.abs(actorId.hashCode()) % nodeList.size()).getBusId();
    }

    /**
     * hash负载均衡，获取整个ActorSystem中符合预期的节点busId。
     *
     * @param actorSystem 系统。
     * @param serverType  服务器类型。
     * @param actorId     actor id。
     * @return 无符合要求的节点 null; 符合要求的busId。
     */
    public static String getHashBusId(final ActorSystem actorSystem, final int serverType, long actorId) {
        // 拿到这个ActorRole所有的busId，根据actorId取模选择一个。
        final List<Node> nodeList = actorSystem.getRegistryValue().getServerTypeNodeList(serverType);
        if (nodeList.isEmpty()) {
            return null;
        }
        // actorId可能是负数，所以需要用绝对值取模。
        return nodeList.get((int) (Math.abs(actorId) % nodeList.size())).getBusId();
    }


    /**
     * hash负载均衡，获取整个ActorSystem中符合预期的节点busId。
     *
     * @param actorSystem 系统。
     * @param serverType  服务器类型。
     * @return 无符合要求的节点 null; 符合要求的busId。
     */
    public static String getRandomBusId(final ActorSystem actorSystem, final int serverType) {
        // 拿到这个ActorRole所有的busId，随机挑一个
        final List<Node> nodeList = actorSystem.getRegistryValue().getServerTypeNodeList(serverType);
        if (nodeList.isEmpty()) {
            return null;
        }
        final Node node = RandomUtils.randomList(nodeList);
        return node.getBusId();
    }
}

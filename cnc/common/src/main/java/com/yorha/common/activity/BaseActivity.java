package com.yorha.common.activity;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityTemplate;

import javax.annotation.Nullable;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.yorha.proto.CommonEnum.ActivityStatus;

/**
 * reload 可重入
 * <p>
 * 加载流程：
 * loadUnits --> 变更状态 --> loadUnits --> reloadChildActivities
 * 过期流程：
 * 变更状态 -> expire子活动 --> expire unit
 * <p>
 * loadUnits 首次开启和每次reload时调用, entity在内存中，只调用一次
 * reloadUnits 玩家重登时调用
 * <p>
 * 活动在player上和zone上都有加载，但是有些东西是统一的，比如父子活动，unit等，它们的生命周期也是雷同的
 */
public abstract class BaseActivity<
        Act extends BaseActivity<Act, Unit>,
        Unit extends BaseActivityUnit> {
    private static final Logger LOGGER = LogManager.getLogger(BaseActivity.class);

    protected final Act father;
    protected final int actScheduleId;
    protected final int activityId;
    protected final LinkedHashMap<Integer, Act> children;
    /**
     * 本活动自身是否已经执行过一次reload，这个字段可以考虑省去
     */
    private boolean selfLoaded;
    /**
     * zone上和player上的unitId是不一样的
     */
    protected final Map<Integer, Unit> unitMap;

    public BaseActivity(@Nullable Act father, int actScheduleId, ActivityTemplate template) {
        this.father = father;
        this.actScheduleId = actScheduleId;
        this.activityId = template.getId();
        this.children = Maps.newLinkedHashMapWithExpectedSize(0);
        this.selfLoaded = false;
        this.unitMap = Maps.newHashMapWithExpectedSize(1);
        LOGGER.info("new activity activityId={} actScheduleId={}", activityId, actScheduleId);
    }

    protected abstract ActivityPropBase baseProp();

    /**
     * 用于日志输出
     */
    protected abstract Object owner();

    protected abstract void tryAddReloadTimer(Instant reloadTime);

    protected void enter(ActivityStatus status, Instant now) {
        baseProp().setStatus(status)
                .setStatusEnterTsSec((int) now.getEpochSecond());
    }

    /**
     * 幂等加载活动逻辑(活动初始化或者从数据库中恢复或刷新子活动状态)
     * <p>
     * 当前可能正在活动中，也可能是已经过期了，过期的话本接口会触发expire
     */
    public void reload() throws Exception {
        final ActivityPropBase baseProp = baseProp();
        if (baseProp.getStatus() == ActivityStatus.ACS_EXPIRED) {
            // 活动已经触发过过期了，不再加载
            LOGGER.debug("{} reload expired activity {}, won't do anything.", owner(), activityId);
            return;
        }

        final Instant now = SystemClock.nowInstant();
        final Instant startTime = Instant.ofEpochSecond(baseProp.getStartTsSec());
        Instant expireTime = Instant.ofEpochSecond(baseProp.getEndTsSec());

        // 支持活动结束时间可热更，不支持结束时间提前，不支持子活动
        final Instant configExpireTime = startTime.plus(getTemplate().getLastingHours(), ChronoUnit.HOURS);
        if (father == null && configExpireTime.isAfter(expireTime)) {
            LOGGER.info("reload activity endTsSec activityId={} endTsSec={} -> {}", activityId, expireTime.getEpochSecond(), configExpireTime.getEpochSecond());
            // 当活动结束时间延后时，修改prop的EndTsSec为最新
            baseProp.setEndTsSec((int) configExpireTime.getEpochSecond());
            expireTime = configExpireTime;
        }

        if (startTime.isAfter(now)) {
            WechatLog.error("{} activity not started, invoking reload {}", owner(), activityId);
            return;
        }

        final ActivityTemplate activityTemplate = ResHolder.getResService(ActivityResService.class).getActivityTemplate(activityId);
        boolean initial = false;
        if (baseProp.getStatus() == ActivityStatus.ACS_NONE) {
            // 初始化所有unit
            loadUnits(activityTemplate, true);
            enter(ActivityStatus.ACS_ACTIVE, now);
            initial = true;
        }

        if (baseProp.getStatus() == ActivityStatus.ACS_ACTIVE) {
            if (!selfLoaded) {
                // 首次加载内存
                if (!initial) {
                    // load所有unit，如果上面init走过了，这里就别重复走了
                    loadUnits(activityTemplate, false);
                }
                selfLoaded = true;
            } else {
                // 在内存中reload
                reloadUnits();
            }
            // 加载子活动
            try {
                reloadChildActivities(activityTemplate);
            } catch (Exception e) {
                LOGGER.error("{} reloadChildActivities failed when reload activityId={} scheduleId={}", owner(), activityId, actScheduleId);
            }

            var isActivityFinished = activityTemplate.getAutoExpire() && allFinished();
            if (now.isAfter(expireTime) || isActivityFinished) {
                // 活动已经过期啦
                expire();
            } else {
                // 活动还没过期，定时过期
                tryAddReloadTimer(expireTime);
            }
        } else {
            LOGGER.error("{} activity reload status illegal! activityId={} status={}", owner(), activityId, baseProp.getStatus());
        }
    }

    protected abstract void loadUnits(ActivityTemplate activityTemplate, boolean isInitial);

    protected abstract void reloadUnits();

    protected void registerUnit(int unitId, Unit unit) {
        LOGGER.debug("{} register activity unit {}", this, unit);
        unitMap.put(unitId, unit);
    }

    protected void reloadChildActivities(ActivityTemplate activityTemplate) {
        final ActivityResService ars = ResHolder.getResService(ActivityResService.class);
        final List<Integer> childActivityIds = activityTemplate.getChildActivityIdListList();
        final Instant fatherStart = Instant.ofEpochSecond(baseProp().getStartTsSec());
        final Instant now = SystemClock.nowInstant();

        for (int childActivityId : childActivityIds) {
            try {
                Act existChildAct = children.get(childActivityId);
                if (existChildAct != null) {
                    existChildAct.reload();
                } else {
                    final ActivityTemplate childTemplate = ars.getActivityTemplate(childActivityId);
                    Instant childStartTime = fatherStart.plus(childTemplate.getOpenOffsetHours(), ChronoUnit.HOURS);
                    Instant childExpireTime = childStartTime.plus(childTemplate.getLastingHours(), ChronoUnit.HOURS);

                    if (now.isBefore(childStartTime)) {
                        // 子活动还没开始
                        tryAddReloadTimer(childStartTime);
                    } else {
                        // 子活动正当周期内或者已经过期，已经过期也需要一个完整的init-load-expire的过程，否则可能会漏掉邮件什么的内容
                        Act childAct = tryLoadChildAct(childTemplate, childStartTime, childExpireTime);
                        if (childAct != null) {
                            children.put(childAct.getActivityId(), childAct);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("{} reloadChildActivities failed activityId={} scheduleId={} childActivityId={}, ",
                        owner(), activityId, actScheduleId, childActivityId, e);
            }
        }
    }

    @Nullable
    protected abstract Act tryLoadChildAct(ActivityTemplate childTemplate, Instant childStartTime, Instant childExpireTime) throws Exception;

    public void expire() {
        try {
            if (baseProp().getStatus() == ActivityStatus.ACS_EXPIRED) {
                LOGGER.debug("{} activity duplicated expire! {}", owner(), baseProp());
                return;
            }
            LOGGER.info("expire activity activityId={} scheduleId={}", activityId, actScheduleId);
            final Instant now = SystemClock.nowInstant();
            enter(ActivityStatus.ACS_EXPIRED, now);

            for (Act childAct : ImmutableList.copyOf(children.values())) {
                try {
                    childAct.expire();
                } catch (Exception e) {
                    LOGGER.error("{} expire child activity failed, childActivityId={} activityId={} scheduleId={},",
                            owner(), childAct.activityId, activityId, actScheduleId, e);
                }
            }
            for (Unit unit : unitMap.values()) {
                try {
                    unit.expire();
                } catch (Exception e) {
                    WechatLog.error(e);
                }
            }
            expireImpl();
            if (father != null) {
                father.children.remove(this.activityId);
                father.afterChildExpired(this.activityId);
            }
        } catch (Exception e) {
            LOGGER.error("{} expire activity fail activityId={} scheduleId={}, ", owner(), activityId, actScheduleId, e);
        }
    }

    protected void afterChildExpired(int activityId) {
    }

    protected abstract void expireImpl();

    public int getActivityId() {
        return activityId;
    }

    public int getActScheduleId() {
        return actScheduleId;
    }

    public ActivityTemplate getTemplate() {
        return ResHolder.getResService(ActivityResService.class).getActivityTemplate(activityId);
    }

    public Unit findUnit(int unitId) {
        return unitMap.get(unitId);
    }

    /**
     * 危险接口，请勿随意使用
     */
    public boolean onlyOneUnit() {
        return unitMap != null && unitMap.size() == 1;
    }

    /**
     * 危险接口，请勿随意使用
     */
    @Nullable
    public Unit getOnlyOneUnit() {
        if (onlyOneUnit()) {
            return unitMap.get(unitMap.keySet().iterator().next());
        }
        return null;
    }

    /**
     * 活动逻辑已经消费完毕，会触发活动的expire逻辑，zone上不该触发这个接口
     */
    protected abstract boolean allFinished();

    public Act getFather() {
        return father;
    }

    public <T extends Unit> T findFirstUnitInTopFatherActOf(Class<T> clazz) {
        BaseActivity<Act, Unit> topFatherAct = this;
        while (topFatherAct.getFather() != null) {
            topFatherAct = topFatherAct.getFather();
        }
        return topFatherAct.findFirstUnitOf(clazz);
    }

    protected <T extends Unit> T findFirstUnitOf(Class<T> clazz) {
        for (Unit unit : unitMap.values()) {
            if (unit != null && clazz.isAssignableFrom(unit.getClass())) {
                //noinspection unchecked
                return (T) unit;
            }
        }
        for (Act child : children.values()) {
            T childFound = child.findFirstUnitOf(clazz);
            if (childFound != null) {
                return childFound;
            }
        }
        return null;
    }

    /**
     * 强制下架
     */
    public void forceOff() {
        LOGGER.info("forceOff {} {}", this.actScheduleId, this.activityId);
        // 这里暂时不用try-catch，让异常阻断掉reload流程，等待手动修复
        for (Act child : Lists.newLinkedList(this.children.values())) {
            child.forceOff();
        }
        for (Unit unit : this.unitMap.values()) {
            unit.forceOff();
        }
        this.forceOffImpl();
    }

    /**
     * 不处理业务相关的东西，只管卸载容器什么的就可以了
     */
    public abstract void forceOffImpl();

    public boolean isActive() {
        return baseProp().getStatus() == ActivityStatus.ACS_ACTIVE;
    }

}

package com.yorha.common.actor.mailbox;

import com.yorha.common.actor.mailbox.msg.ActorMailboxMsg;
import com.yorha.common.actor.ref.FixedRef;
import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actor.IActorRef;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 不丢消息的邮箱。
 * 通过一把读写锁，保证投递和销毁是互斥的。
 * 注意：适合频繁淘汰拉起的有状态Actor。
 *
 * <AUTHOR>
 */
public final class ActorRWLockMailboxImpl extends ActorMailboxImpl {
    private static final Logger LOGGER = LogManager.getLogger(ActorRWLockMailboxImpl.class);

    private final ReentrantReadWriteLock rwLock;

    private ActorRWLockMailboxImpl(final IActorRef ref, final IMailboxMetaData metaData, final ReentrantReadWriteLock rwLock) {
        super(ref, metaData);
        this.rwLock = rwLock;
    }

    @Override
    public boolean sendMsg(Object object) {
        this.rwLock.readLock().lock();
        // 碰巧，这个actor在销毁，mb无用了，再次投递
        if (this.status.get() == ActorMailboxImpl.MAILBOX_DESTROYED) {
            // 这个锁不是mb独占，可能同时两个id的mb都在用，这个锁是外部传入，所以必须解锁
            this.rwLock.readLock().unlock();
            // 再来一遍吧，正好投递的时候，这个mb销毁中，我把消息给新mb吧
            ActorMsgSystem.dispatchMsg((ActorMsgEnvelope) object);
            return true;
        }
        try {
            return super.sendMsg(object);
        } finally {
            this.rwLock.readLock().unlock();
        }
    }

    @Override
    protected void onDestroy() {
        try {
            this.rwLock.writeLock().lock();
            this.status.set(ActorMailboxImpl.MAILBOX_DESTROYED);
            if (!this.metaData.getActorSystem().getRegistryValue().removeMailbox(this.ref())) {
                LOGGER.warn("removeRegistryValue {} of {} fail!", this.ref(), this);
            }
            this.msgCount.set(0);
            ActorMailboxMsg msg;
            while ((msg = this.queue.poll()) != null) {
                // 因为先调用了removeMailbox，所以dispatchMsg会一路走下去创建新的mb，等于把destroy后的消息给了下一个新mb
                // 两个mb是同一个id，本mb等待gc掉，没地方能引用本mb了，新mb虽然创建了，但不用担心消息乱序，因为有读写锁，写锁被这个协程占有
                ActorMsgSystem.dispatchMsg(msg.getMsg());
            }
        } finally {
            this.rwLock.writeLock().unlock();
        }
    }

    /**
     * 提供了带读写锁的Mailbox构建方案，能够防止由Actor销毁导致的消息丢失。
     *
     * <AUTHOR>
     */
    public static class Factory implements IMailboxFactory {
        private final Map<String, ReentrantReadWriteLock> lockMap;
        private final IMailboxMetaData metaData;

        public Factory(final IMailboxMetaData metaData) {
            this.metaData = metaData;
            this.lockMap = new ConcurrentHashMap<>();
        }

        @Override
        public IActorMailbox newMailbox(IActorRef ref) {
            // 禁止写日志！！！此处处于一个ConcurrentHashMap的computeIfAbsent中，有synchronized锁
            if (!(ref instanceof FixedRef)) {
                ref = new FixedRef(this.metaData.getActorSystem().getRegistryValue().getLocalNode().getBusId(), ref.getActorRole(), ref.getActorId());
            }
            final ReentrantReadWriteLock readWriteLock = this.lockMap.computeIfAbsent(ref.getActorId(), k -> new ReentrantReadWriteLock());
            return new ActorRWLockMailboxImpl(ref, this.metaData, readWriteLock);
        }
    }

    public static class LimitLockFactory implements IMailboxFactory {
        private static final int LOCK_LIMIT = 5000;
        private final Map<Integer, ReentrantReadWriteLock> lockMap;
        private final IMailboxMetaData metaData;

        public LimitLockFactory(final IMailboxMetaData metaData) {
            this.metaData = metaData;
            this.lockMap = new ConcurrentHashMap<>();
        }

        @Override
        public IActorMailbox newMailbox(IActorRef ref) {
            // 禁止写日志！！！此处处于一个ConcurrentHashMap的computeIfAbsent中，有synchronized锁
            if (!(ref instanceof FixedRef)) {
                ref = new FixedRef(this.metaData.getActorSystem().getRegistryValue().getLocalNode().getBusId(), ref.getActorRole(), ref.getActorId());
            }
            var key = Math.abs(ref.getActorId().hashCode()) % LOCK_LIMIT;
            final ReentrantReadWriteLock readWriteLock = this.lockMap.computeIfAbsent(key, k -> new ReentrantReadWriteLock());
            return new ActorRWLockMailboxImpl(ref, this.metaData, readWriteLock);
        }
    }
}

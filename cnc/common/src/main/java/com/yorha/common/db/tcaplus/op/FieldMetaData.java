package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Descriptors;

import java.util.*;

/**
 * <AUTHOR>
 */
public class FieldMetaData {
    public String tableName = "";
    public Descriptors.FieldDescriptor documentIdField = null;
    public HashMap<String, Descriptors.FieldDescriptor> keyFieldsMap = new HashMap<>();
    public HashMap<String, Descriptors.FieldDescriptor> valueFieldsMap = new HashMap<>();
    public List<String> keyFieldNames = new ArrayList<>();
    public List<Descriptors.FieldDescriptor> keyFieldsList = new ArrayList<>(16);
    public List<Descriptors.FieldDescriptor> valueFieldsList = new ArrayList<>(16);
    public Set<Set<String>> indexes = new HashSet<>(3);
}

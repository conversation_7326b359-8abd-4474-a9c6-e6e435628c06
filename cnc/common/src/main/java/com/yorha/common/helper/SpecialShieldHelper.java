package com.yorha.common.helper;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.yorha.proto.CommonEnum.SafeGuardFunctionType;
import com.yorha.proto.CommonEnum.SafeGuardReason;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

public class SpecialShieldHelper {
    private static final Logger LOGGER = LogManager.getLogger(SpecialShieldHelper.class);

    /**
     * NOTE(furson): 这里的定义是有顺序的！遍历也期望是有顺序的！不能随便更改！
     */
    private static final Map<Integer, Set<Integer>> REASON_TO_FUNCTION_MAP = ImmutableMap.of(
            // 领土活动提供的护罩，【不能侦察】
            SafeGuardReason.SGR_CLAN_TERRITORY_ACTIVITY_VALUE, ImmutableSet.of(
                    SafeGuardFunctionType.SGFT_CANNOT_BE_RALLY_VALUE,
                    SafeGuardFunctionType.SGFT_CANNOT_BE_ATTACK_VALUE
            ),
            // 军团堡垒提供的护罩
            SafeGuardReason.SGR_CLAN_FORTRESS_VALUE, ImmutableSet.of(
                    SafeGuardFunctionType.SGFT_CANNOT_BE_SPY_VALUE,
                    SafeGuardFunctionType.SGFT_CANNOT_BE_RALLY_VALUE,
                    SafeGuardFunctionType.SGFT_CANNOT_BE_ATTACK_VALUE
            )
    );

    /**
     * 检查特殊防护罩是否有特定功能
     *
     * @param isOpen       是否开启
     * @param reasons      护罩原因
     * @param functionType 护罩功能类型
     * @return 是否有特殊护罩功能
     */
    public static SafeGuardReason checkHasFunction(boolean isOpen, final Collection<Integer> reasons,
                                                   SafeGuardFunctionType functionType) {
        if (!isOpen) {
            LOGGER.error("SpecialShieldHelper checkHasFunction: isOpen is false");
            return SafeGuardReason.SGR_NONE;
        }
        if (reasons == null || reasons.isEmpty()) {
            LOGGER.info("SpecialShieldHelper checkHasFunction: reasons is empty or null");
            return SafeGuardReason.SGR_NONE;
        }
        int functionTypeValue = functionType.getNumber();
        for (Integer reason : reasons) {
            if (!REASON_TO_FUNCTION_MAP.containsKey(reason)) {
                LOGGER.error("SpecialShieldHelper checkHasFunction: reason {} not exist in reasonToFunctionMap", reason);
                continue;
            }
            if (REASON_TO_FUNCTION_MAP.get(reason).contains(functionTypeValue)) {
                return SafeGuardReason.forNumber(reason);
            }
        }
        return SafeGuardReason.SGR_NONE;
    }
}

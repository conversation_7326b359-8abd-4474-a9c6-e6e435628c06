package com.yorha.common.enums.reason;

/**
 * <AUTHOR>
 * 军团个人积分动作枚举，与运营流水中的行为一一对应，通过ClanScoreCategory映射得到
 */
public enum GuildPersonalScoreActionType {
    /**
     * 帮助行为
     */
    GUILD_HELP,
    /**
     * 建设行为
     */
    PARTICIPATE_BUILD,
    /**
     * 占领行为
     */
    OCCUPY,
    /**
     * 科技捐献
     */
    TECH_DONATE,
    /**
     * 军团商店回收
     */
    RECYCLE,
    /**
     * 军团商店购买
     */
    GUILD_SHOP,
    /**
     * 军团礼物
     */
    GUILD_GIFT,
    /**
     * 使用道具
     */
    USE_ITEM,
    /**
     * gm增减
     */
    GM,
}

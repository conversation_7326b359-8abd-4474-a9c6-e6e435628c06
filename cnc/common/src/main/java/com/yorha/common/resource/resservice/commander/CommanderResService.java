package com.yorha.common.resource.resservice.commander;

import com.google.common.collect.Maps;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import res.template.ConstTemplate;
import res.template.EnergyBuyTemplate;
import res.template.ItemTemplate;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * 指挥官相关的东西，目前体力放在这里
 */
public class CommanderResService extends AbstractResService {

    /**
     * 购买次数 -> 消耗钻石
     */
    private final TreeMap<Integer, Integer> energyBuyCostMap = Maps.newTreeMap();

    public CommanderResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        Collection<EnergyBuyTemplate> energyBuyTemplates = getResHolder().getListFromMap(EnergyBuyTemplate.class);
        for (EnergyBuyTemplate template : energyBuyTemplates) {
            energyBuyCostMap.put(template.getBuyTimes(), template.getCostDiamond());
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        // 确保这个道具是合法的
        int energyBuyItemId = getResHolder().getConstTemplate(ConstTemplate.class).getEnergyBuyItemId();
        if (getResHolder().findValueFromMap(ItemTemplate.class, energyBuyItemId) == null) {
            throw new ResourceException("指挥官体力界面快速购买的体力道具ID={}错误，道具不存在", energyBuyItemId);
        }
    }

    public int getEnergyBuyCostDiamondByTimes(int times) {
        Map.Entry<Integer, Integer> entry = energyBuyCostMap.floorEntry(times);
        if (entry == null) {
            throw new GeminiException("energy buy cost template not found {}", times);
        }
        return entry.getValue();
    }

}

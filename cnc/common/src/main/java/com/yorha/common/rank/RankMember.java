package com.yorha.common.rank;

import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonMsg;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * 排行成员
 *
 * <AUTHOR>
 */
public class RankMember {
    private long id;
    private long score;
    private long addTime;
    private int zoneId;
    private int lastRank;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public long getAddTime() {
        return addTime;
    }

    public void setAddTime(long addTime) {
        this.addTime = addTime;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    public int getZoneId() {
        return zoneId;
    }

    public void setLastRank(int lastRank) {
        this.lastRank = lastRank;
    }

    public int getLastRank() {
        return lastRank;
    }

    public CommonMsg.RankRecordDetail buildDb() {
        return CommonMsg.RankRecordDetail.newBuilder()
                .setMemberId(id)
                .setScore(score)
                .setLastRank(lastRank)
                .setAddTime(addTime)
                .setZoneId(zoneId).build();
    }

    public static RankMember build(long memberId, long score, int zoneId, int lastRank, long addTsMs) {
        RankMember ret = new RankMember();
        ret.setId(memberId);
        ret.setScore(score);
        ret.setZoneId(zoneId);
        ret.setLastRank(lastRank);
        ret.setAddTime(addTsMs);
        return ret;
    }

    public static RankMember build(long memberId, long score, int zoneId, int lastRank) {
        RankMember ret = new RankMember();
        ret.setId(memberId);
        ret.setScore(score);
        ret.setZoneId(zoneId);
        ret.setLastRank(lastRank);
        ret.setAddTime(SystemClock.now());
        return ret;
    }

    public static RankMember build(long memberId, long score, int zoneId, long addTsMs) {
        RankMember ret = new RankMember();
        ret.setId(memberId);
        ret.setScore(score);
        ret.setZoneId(zoneId);
        ret.setAddTime(addTsMs);
        return ret;
    }

    public static RankMember build(CommonMsg.RankRecordDetail detail) {
        RankMember ret = new RankMember();
        ret.setId(detail.getMemberId());
        ret.setScore(detail.getScore());
        ret.setZoneId(detail.getZoneId());
        ret.setLastRank(detail.getLastRank());
        ret.setAddTime(detail.getAddTime());
        return ret;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

}

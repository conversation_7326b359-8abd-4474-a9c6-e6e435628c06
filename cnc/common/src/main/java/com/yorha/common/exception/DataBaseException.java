package com.yorha.common.exception;

import com.yorha.gemini.utils.StringUtils;

public class DataBaseException extends RuntimeException {
    private final int code;

    public DataBaseException(Throwable cause) {
        super(cause);
        this.code = -4;
    }

    public DataBaseException(int code) {
        super();
        this.code = code;
    }

    public DataBaseException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public DataBaseException(IErrorCode errorCode) {
        super(StringUtils.format("errorCode={}", errorCode));
        this.code = errorCode.getCodeId();
    }

    public DataBaseException(IErrorCode errorCode, String msg) {
        super(msg);
        this.code = errorCode.getCodeId();
    }

    public int getCodeId() {
        return code;
    }
}

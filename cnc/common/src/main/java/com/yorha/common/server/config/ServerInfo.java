package com.yorha.common.server.config;

import com.yorha.common.utils.BusIdUtils;
import com.yorha.common.wechatlog.WechatConfig;

/**
 * 服务器信息，不可变，java bean
 *
 * <AUTHOR>
 */
public class ServerInfo implements WechatConfig {

    private final int worldId;
    private final int zoneId;
    private final int serverTypeId;
    private final int instanceId;
    private final String busId;
    private String serverVersion;

    public ServerInfo(int worldId, int zoneId, int serverTypeId, int instanceId) {
        this.worldId = worldId;
        this.zoneId = zoneId;
        this.serverTypeId = serverTypeId;
        this.instanceId = instanceId;
        this.busId = BusIdUtils.formBusId(worldId, zoneId, serverTypeId, instanceId);
    }

    @Override
    public int getWorldId() {
        return worldId;
    }

    public int getServerTypeId() {
        return serverTypeId;
    }

    public int getInstanceId() {
        return instanceId;
    }

    public int getZoneId() {
        return zoneId;
    }

    @Override
    public String getBusId() {
        return busId;
    }

    @Override
    public String toString() {
        return "ServerInfo{" + busId + '}';
    }

    @Override
    public String getServerVersion() {
        return serverVersion;
    }

    /**
     * 版本号是在ServerInfo构造之后初始化的，这里赋值一次
     */
    public void setServerVersion(String serverVersion) {
        this.serverVersion = serverVersion;
    }

    @Override
    public boolean getWechatLogEnable() {
        return ClusterConfigUtils.getWorldConfig().getBooleanItem("wechat_log_enable");
    }

    @Override
    public boolean getWechatLogSync() {
        return ClusterConfigUtils.getWorldConfig().getBooleanItem("wechat_log_sync");
    }

    @Override
    public String getWeChatKey(String name) {
        return ClusterConfigUtils.getWorldConfig().getStringItem(name);
    }
}

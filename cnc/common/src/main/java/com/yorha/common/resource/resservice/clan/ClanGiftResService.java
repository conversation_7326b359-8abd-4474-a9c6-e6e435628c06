package com.yorha.common.resource.resservice.clan;

import com.google.common.collect.Lists;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.*;

/**
 * 军团礼物
 */
public class ClanGiftResService extends AbstractResService {

    //Map<GiftId, GiftInfo>
    private final Map<Integer, GiftInfo> giftInfoMap = new HashMap<>();

    public ClanGiftResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        final int maxGiftLevel = this.getMaxGiftLevel();

        for (ClanGiftLvTemplate giftLvTemplate : getResHolder().getListFromMap(ClanGiftLvTemplate.class)) {
            if (giftLvTemplate.getId() > maxGiftLevel) {
                throw new ResourceException("军团礼物等级表 等级上限常量配置为:{} 出现超等级上限配置", maxGiftLevel);
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        checkClanGiftLv();
        checkTreasureGift();
        checkClanGift();
        checkConst();
        // 有序，不可前置于checkClanGift
        checkClanGiftSendTables();
    }

    private void checkConst() throws ResourceException {
        if (getResHolder().getConstTemplate(ConstClanTemplate.class).getGiftRareStorageLimit() >= 400) {
            throw new ResourceException("军团常量表 军团礼物稀有礼物存储上限过大");
        }
        if (getResHolder().getConstTemplate(ConstClanTemplate.class).getGiftStorageLimit() >= 400) {
            throw new ResourceException("军团常量表 军团礼物普通礼物存储上限过大");
        }
    }

    private void checkClanGiftSendTables() throws ResourceException {
        for (MonsterTemplate monsterTemplate : getResHolder().getListFromMap(MonsterTemplate.class)) {
            checkClanGiftId(monsterTemplate.getId(), monsterTemplate.getClanGiftId(), "monster 野怪表");
        }

        for (ChargeGoodsTemplate chargeGoodsTemplate : getResHolder().getListFromMap(ChargeGoodsTemplate.class)) {
            checkClanGiftId(chargeGoodsTemplate.getId(), chargeGoodsTemplate.getClanGiftId(), "charge_goods 礼包表");
        }
    }

    private void checkClanGiftId(int id, int itemId, final String tableName) throws ResourceException {
        // 未配置则跳过
        if (itemId == 0) {
            return;
        }

        ItemTemplate itemTemplate = getResHolder().findValueFromMap(ItemTemplate.class, itemId);
        if (itemTemplate == null) {
            throw new ResourceException("{} id={} 军团礼物id={}配置错误，道具表中不存在", tableName, id, itemId);
        }
        if (CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType()) != CommonEnum.ItemUseType.CLAN_GIFT) {
            throw new ResourceException("{} id={} 军团礼物id={}配置错误，道具表中类型不为军团礼物，为{}", tableName, id, CommonEnum.ItemUseType.forNumber(itemTemplate.getEffectType()));
        }
        int giftId = itemTemplate.getEffectId();
        if (!this.isValidGiftId(giftId)) {
            throw new ResourceException("{} id={} 军团礼物id={}配置错误，道具表中giftId={}不存在与军团礼物表中", tableName, id, giftId);
        }

    }


    private void checkClanGift() throws ResourceException {
        for (ClanGiftTemplate clanGiftTemplate : getResHolder().getListFromMap(ClanGiftTemplate.class)) {
            this.giftInfoMap.computeIfAbsent(clanGiftTemplate.getGiftID(), (k) -> new GiftInfo(this.getResHolder())).checkTemplate(clanGiftTemplate);
        }
        for (GiftInfo giftInfo : this.giftInfoMap.values()) {
            giftInfo.finishCheck();
        }
    }

    private void checkTreasureGift() throws ResourceException {
        // 珍藏礼物配置
        for (ClanTreasureTemplate treasureTemplate : getResHolder().getListFromMap(ClanTreasureTemplate.class)) {
            if (getResHolder().findValueFromMap(SelecteRewardTemplate.class, treasureTemplate.getRewardId()) == null) {
                throw new ResourceException("军团珍藏礼物 id={} 不存在对应奖励id={}", treasureTemplate.getId(), treasureTemplate.getRewardId());
            }
        }
    }

    private void checkClanGiftLv() throws ResourceException {
        // 礼物等级配置
        final int maxGiftLevel = this.getMaxGiftLevel();
        if (maxGiftLevel <= 0) {
            throw new ResourceException("军团礼物等级 礼物等级没有配置");
        }
        for (int i = 1; i <= maxGiftLevel; i++) {
            if (getResHolder().findValueFromMap(ClanGiftLvTemplate.class, i) == null) {
                throw new ResourceException("军团礼物等级 level={}未配置", i);
            }
        }
        Collection<ClanGiftLvTemplate> giftLvTemplates = getResHolder().getListFromMap(ClanGiftLvTemplate.class);

        for (ClanGiftLvTemplate giftLvTemplate : giftLvTemplates) {
            int treasureId = giftLvTemplate.getTreasureId();
            if (getResHolder().findValueFromMap(ClanTreasureTemplate.class, treasureId) == null) {
                throw new ResourceException("军团礼物等级 level={} 不存在对应珍藏礼物id{}", giftLvTemplate.getId(), treasureId);
            }
        }
    }


    /**
     * 获取军团礼物配表
     *
     * @param giftLevel 军团礼物等级
     * @param giftId    军团礼物id
     * @return 军团礼物配表
     */
    public ClanGiftTemplate getClanGiftTemplate(int giftLevel, int giftId) {
        if (!this.giftInfoMap.containsKey(giftId)) {
            throw new GeminiException(ErrorCode.CLAN_GIFT_INVALID_GIFT_ID);
        }
        GiftInfo giftInfo = this.giftInfoMap.get(giftId);
        return giftInfo.getClanGiftTemplate(giftLevel);
    }

    /**
     * 获取最大军团礼物等级
     *
     * @return 最大军团礼物等级
     */
    public int getMaxGiftLevel() {
        return getResHolder().getConstTemplate(ConstClanTemplate.class).getGiftLevelLimit();
    }

    /**
     * 礼物id是否合法
     *
     * @param giftId 礼物id
     * @return true==合法
     */
    public boolean isValidGiftId(int giftId) {
        return this.giftInfoMap.containsKey(giftId);
    }

    static class GiftInfo {
        private final ResHolder resHolder;
        private final List<LevelIntervalInfo> levelIntervalInfos = Lists.newArrayList();

        private CommonEnum.ClanGiftRarityType rarity = CommonEnum.ClanGiftRarityType.CLANGRT_NONE;

        GiftInfo(ResHolder resHolder) {
            this.resHolder = resHolder;
        }

        /**
         * 边加载边初始校验
         */
        public void checkTemplate(ClanGiftTemplate clanGiftTemplate) throws ResourceException {
            this.checkRarityType(clanGiftTemplate);
            this.checkReward(clanGiftTemplate);
            this.addLevel(clanGiftTemplate);
        }

        /**
         * 完成最终校验，并处理数据
         */
        public void finishCheck() throws ResourceException {
            this.levelIntervalInfos.sort(Comparator.comparingInt(LevelIntervalInfo::getLevelMax));
            for (int i = 0; i < this.levelIntervalInfos.size() - 1; i++) {
                LevelIntervalInfo curLevelIntervalInfo = this.levelIntervalInfos.get(i);
                LevelIntervalInfo nextLevelIntervalInfo = this.levelIntervalInfos.get(i + 1);
                if (curLevelIntervalInfo.getLevelMax() > nextLevelIntervalInfo.getLevelMin()) {
                    throw new ResourceException("军团礼物 id={}与{} 存在等级区间重合",
                            curLevelIntervalInfo.getClanGiftTemplate().getId(), nextLevelIntervalInfo.getClanGiftTemplate().getId());
                }
                if (curLevelIntervalInfo.getLevelMax() + 1 < nextLevelIntervalInfo.getLevelMin()) {
                    throw new ResourceException("军团礼物 id={}与{} 存在等级区间间隙",
                            curLevelIntervalInfo.getClanGiftTemplate().getId(), nextLevelIntervalInfo.getClanGiftTemplate().getId());
                }
            }
            final int giftMaxLevel = this.levelIntervalInfos.getLast().getLevelMax();
            final int giftMaxLevelLimit = this.resHolder.getConstTemplate(ConstClanTemplate.class).getGiftLevelLimit();
            if (giftMaxLevel != giftMaxLevelLimit) {
                throw new ResourceException("军团礼物 礼物id={} 最大已配置等级为{}, 常量配置礼物等级上限为{}",
                        this.levelIntervalInfos.getFirst().getClanGiftTemplate().getGiftID(), giftMaxLevel, giftMaxLevelLimit);
            }
        }

        private void checkReward(ClanGiftTemplate clanGiftTemplate) throws ResourceException {
            if (this.resHolder.findValueFromMap(SelecteRewardTemplate.class, clanGiftTemplate.getRewardId()) == null) {
                throw new ResourceException("军团礼物 id={} 未配置有效奖励id={}", clanGiftTemplate.getId(), clanGiftTemplate.getRewardId());
            }
            if (clanGiftTemplate.getKey() <= 0) {
                throw new ResourceException("军团礼物 id={} 未配置有效钥匙数={}", clanGiftTemplate.getId(), clanGiftTemplate.getKey());
            }
            if (clanGiftTemplate.getPoint() <= 0) {
                throw new ResourceException("军团礼物 id={} 未配置有效礼物点数数量={}", clanGiftTemplate.getId(), clanGiftTemplate.getPoint());
            }
        }

        private void addLevel(ClanGiftTemplate clanGiftTemplate) {
            this.levelIntervalInfos.add(new LevelIntervalInfo(clanGiftTemplate.getGiftlevelmin(), clanGiftTemplate.getGiftlevelmax(), clanGiftTemplate));
        }

        private void checkRarityType(ClanGiftTemplate clanGiftTemplate) throws ResourceException {
            CommonEnum.ClanGiftRarityType rarityType = clanGiftTemplate.getType();
            if (rarityType == CommonEnum.ClanGiftRarityType.CLANGRT_NONE) {
                throw new ResourceException("军团礼物 id={} 未配置礼物类型", clanGiftTemplate.getId());
            }

            if (this.rarity == CommonEnum.ClanGiftRarityType.CLANGRT_NONE) {
                this.rarity = rarityType;
            }

            if (this.rarity != rarityType) {
                throw new ResourceException("军团礼物 id={}, giftId={} 多种礼物类型: {}  {}", clanGiftTemplate.getId(), clanGiftTemplate.getGiftID(), this.rarity, rarityType);
            }
        }

        public CommonEnum.ClanGiftRarityType getRarity() {
            return this.rarity;
        }

        public ClanGiftTemplate getClanGiftTemplate(int level) {
            for (LevelIntervalInfo intervalInfo : this.levelIntervalInfos) {
                if (level >= intervalInfo.getLevelMin() && level <= intervalInfo.getLevelMax()) {
                    return intervalInfo.getClanGiftTemplate();
                }
            }
            return null;
        }
    }

    static class LevelIntervalInfo {
        private final int levelMin;
        private final int levelMax;
        private final ClanGiftTemplate clanGiftTemplate;

        LevelIntervalInfo(int minLevel, int maxLevel, ClanGiftTemplate template) {
            this.levelMin = minLevel;
            this.levelMax = maxLevel;
            this.clanGiftTemplate = template;
        }

        public int getLevelMin() {
            return levelMin;
        }

        public int getLevelMax() {
            return levelMax;
        }

        public ClanGiftTemplate getClanGiftTemplate() {
            return clanGiftTemplate;
        }
    }
}
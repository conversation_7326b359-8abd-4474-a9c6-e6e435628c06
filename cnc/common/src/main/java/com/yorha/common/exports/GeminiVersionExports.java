package com.yorha.common.exports;

import io.prometheus.client.Collector;
import io.prometheus.client.Info;

import java.util.List;

/**
 * 从io.prometheus.client.hotspot.VersionExports改造而来。
 *
 * <AUTHOR>
 */
public class GeminiVersionExports extends Collector {
    private final String busId;

    public GeminiVersionExports(String busId) {
        this.busId = busId;
    }

    @Override
    public List<MetricFamilySamples> collect() {
        Info i = Info.build().name("jvm").help("VM version info").create();
        i.info(
                "busId", this.busId,
                "version", System.getProperty("java.runtime.version", "unknown"),
                "vendor", System.getProperty("java.vm.vendor", "unknown"),
                "runtime", System.getProperty("java.runtime.name", "unknown")
        );
        return i.collect();
    }
}
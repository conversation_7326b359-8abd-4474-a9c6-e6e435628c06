package com.yorha.common.qlog;

import java.util.List;

/**
 * qlog流水抽象基类
 *
 * <AUTHOR>
 */
public abstract class AbstractQlogFlow {
    /**
     * 获取流水内容
     *
     * @return
     */
    public String getContent() {
        StringBuilder builder = new StringBuilder();
        builder.append(getMetaName());
        addFlowHeadContent(builder);
        addUsrDefContent(builder);
        builder.append("\n");
        return builder.toString();
    }

    public void sendToQlog() {
        QlogManager.getInstance().addLogFlow(this);
    }

    /**
     * 只打本地qlog日志，不发送到远端qlog服
     * 理论上只有pingsvr用
     */
    public void sendLocalQlog() {
        QlogManager.getInstance().logLocal(this);
    }
    protected abstract void addFlowHeadContent(StringBuilder builder);

    protected abstract int getCurrentFieldCnt();

    protected abstract String getMetaName();

    protected abstract void addUsrDefContent(StringBuilder builder);

    public abstract boolean checkCompletion();

    public abstract List<String> getIncompleteFields();
}

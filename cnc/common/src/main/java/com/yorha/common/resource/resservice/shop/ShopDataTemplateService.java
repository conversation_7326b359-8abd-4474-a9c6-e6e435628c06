package com.yorha.common.resource.resservice.shop;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.asset.AssetPackage;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.ResLoader;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import javax.annotation.Nullable;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.Map.Entry;

import static com.yorha.proto.CommonEnum.CurrencyType;
import static com.yorha.proto.CommonEnum.ExchangeType;

public class ShopDataTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(ShopDataTemplateService.class);

    public ShopDataTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    private final Map<ExchangeType, List<ConversionGoldTemplate>> exchangeTypeMap = Maps.newEnumMap(ExchangeType.class);

    private final Map<Integer, ShopTemplate> itemPriceMap = Maps.newHashMap();

    /**
     * 限制 <组id，Map<主城等级， Template>>
     */
    private final Map<Integer, Map<Integer, ShopLimitTemplate>> shopLimitMap = Maps.newHashMap();

    @Override
    public void load() throws ResourceException {
        // 加载兑换表
        for (ConversionGoldTemplate conversionGoldTemplate : getResHolder().getListFromMap(ConversionGoldTemplate.class)) {
            ExchangeType exchangeType = ExchangeType.forNumber(conversionGoldTemplate.getType());
            List<ConversionGoldTemplate> conversionList = exchangeTypeMap.computeIfAbsent(exchangeType, (key) -> Lists.newArrayList());
            conversionList.add(conversionGoldTemplate);
        }
        // 按照价值倒序排列，确保最贵的在最前面
        for (List<ConversionGoldTemplate> list : exchangeTypeMap.values()) {
            list.sort(Comparator.comparingInt(ConversionGoldTemplate::getValue).reversed());
        }
        // 加载商城表，构建item—>price映射
        for (ShopTemplate shopTemplate : getResHolder().getListFromMap(ShopTemplate.class)) {
            if (itemPriceMap.containsKey(shopTemplate.getItemID())) {
                throw new ResourceException("商城表shop页签里每个道具id应该是唯一的，四哥答应过的");
            }
            itemPriceMap.put(shopTemplate.getItemID(), shopTemplate);
        }
        for (ShopLimitTemplate shopLimitTemplate : getResHolder().getListFromMap(ShopLimitTemplate.class)) {
            ErrorcodeTemplate valueFromMap = getResHolder().findValueFromMap(ErrorcodeTemplate.class, shopLimitTemplate.getErrorcodeId());
            if (valueFromMap == null) {
                throw new ResourceException("商城限购表shop_limit页签里错误码id不存在, id={} errorId={}", shopLimitTemplate.getId(), shopLimitTemplate.getErrorcodeId());
            }

            Map<Integer, ShopLimitTemplate> map = shopLimitMap.computeIfAbsent(shopLimitTemplate.getNumLimitGroupId(), (key) -> Maps.newHashMap());
            if (map.containsKey(shopLimitTemplate.getLevel())) {
                throw new ResourceException("商城限购表shop_limit页签里每个主城等级应该唯一, id={}", shopLimitTemplate.getId());
            }
            map.put(shopLimitTemplate.getLevel(), shopLimitTemplate);

        }
    }

    /**
     * 获取刷新后的列表
     */
    public List<DiscountShopTemplate> getRandomDiscountShopTemplateList(int mainCityLevel) {
        Map<Integer, List<DiscountShopTemplate>> store = this.buildFilterList(mainCityLevel, SystemClock.now());
        List<DiscountShopTemplate> ret = new ArrayList<>();
        List<Integer> array = ResHolder.getInstance().getConstTemplate(ConstDiscountShopTemplate.class).getRefreshNum();
        Set<Entry<Integer, List<DiscountShopTemplate>>> list = store.entrySet();
        for (Entry<Integer, List<DiscountShopTemplate>> entry : list) {
            List<DiscountShopTemplate> elements = entry.getValue();
            int size = Math.min(array.get(entry.getKey() - 1), elements.size());
            for (int i = 0; i < size; i++) {
                DiscountShopTemplate template = RandomUtils.randomByWeight(elements, DiscountShopTemplate::getRefreshWeight);
                entry.getValue().remove(template);
                ret.add(template);
            }

        }
        return ret;
    }

    private Map<Integer, List<DiscountShopTemplate>> buildFilterList(int mainCityLevel, long now) {
        Map<Integer, List<DiscountShopTemplate>> ret = new HashMap<>();
        Collection<DiscountShopTemplate> list = getResHolder().getListFromMap(DiscountShopTemplate.class);
        for (DiscountShopTemplate e : list) {
            IntPairType city = e.getCityLevelPair();
            if (city == null || (mainCityLevel >= city.getKey() && mainCityLevel <= city.getValue())) {
                if (hasNullDate(e) || withInTime(now, e)) {
                    ret.computeIfAbsent(e.getGroup(), key -> new ArrayList<>()).add(e);
                }
            }
        }
        return ret;

    }

    private boolean hasNullDate(DiscountShopTemplate e) {
        return e.getStartTimeDt() == null || e.getOffTimeDt() == null;
    }

    private boolean withInTime(long now, DiscountShopTemplate e) {
        return now >= e.getStartTimeDt().getTime() && now <= e.getOffTimeDt().getTime();
    }

    private int getMaxGroup() {
        int ret = 0;
        Collection<DiscountShopTemplate> list = getResHolder().getListFromMap(DiscountShopTemplate.class);
        for (DiscountShopTemplate e : list) {
            if (ret < e.getGroup()) {
                ret = e.getGroup();
            }
        }
        return ret;
    }

    public Pair<AssetPackage, AssetPackage> conversionGoldBatch(List<IntPairType> exchange) {
        AssetPackage.Builder costAssetBuild = AssetPackage.builder();
        AssetPackage.Builder remainAssetBuild = null;

        for (IntPairType intPairType : exchange) {
            var assetPackageAssetPackagePair = conversionGold(getExchangeByCurrencyType(CurrencyType.forNumber(intPairType.getKey())), intPairType.getValue());
            costAssetBuild.plus(assetPackageAssetPackagePair.getFirst());
            if (assetPackageAssetPackagePair.getSecond() != null) {
                if (remainAssetBuild == null) {
                    remainAssetBuild = AssetPackage.builder();
                }
                remainAssetBuild.plus(assetPackageAssetPackagePair.getSecond());
            }
        }
        if (remainAssetBuild == null) {
            return Pair.of(costAssetBuild.build(), null);
        }
        return Pair.of(costAssetBuild.build(), remainAssetBuild.build());
    }

    /**
     * 兑换黄金
     *
     * @param exchangeType 兑换类型
     * @param value        需要兑换的数值
     * @return Pair<兑换所需资源, 兑换多余资源 ( or null )>
     */
    public Pair<AssetPackage, AssetPackage> conversionGold(ExchangeType exchangeType, long value) {

        // 计算出最佳的购买方案
        Map<ConversionGoldTemplate, Integer> best = calcBest(exchangeType, value, false);
        AssetPackage.Builder cost = AssetPackage.builder();
        AssetPackage.Builder remain = AssetPackage.builder();

        long gainValue = 0;
        for (Entry<ConversionGoldTemplate, Integer> e : best.entrySet()) {
            ConversionGoldTemplate template = e.getKey();
            int num = e.getValue();
            CurrencyType priceType = getCurrencyByExchangeType(ExchangeType.forNumber(template.getPriceType()));
            if (priceType != CurrencyType.DIAMOND) {
                throw new GeminiException("no allow exchange type.{}", template);
            }
            cost.plusCurrency(priceType, (long) template.getPriceValue() * num);

            gainValue += (long) template.getValue() * num;
        }
        // 加速不会返还
        CurrencyType valueType = getCurrencyByExchangeType(exchangeType);
        long addCurrency = gainValue - value;
        if (valueType != null && addCurrency > 0) {
            remain.plusCurrency(valueType, addCurrency);
        }
        LOGGER.info("exchange data. cost={}. exchange={} value={}. remainAsset={}", cost, exchangeType, value, remain);
        return Pair.of(cost.build(), remain.build());
    }

    public CurrencyType getCurrencyByExchangeType(ExchangeType type) {
        if (type == null) {
            return null;
        }
        switch (type) {
            case ET_OIL:
                return CurrencyType.OIL;
            case ET_STEEL:
                return CurrencyType.STEEL;
            case ET_RARE_EARTH:
                return CurrencyType.RARE_EARTH;
            case ET_TIBERIUM:
                return CurrencyType.TIBERIUM;
            case ET_DIAMOND:
                return CurrencyType.DIAMOND;
            default: {
                return null;
            }
        }
    }

    public ExchangeType getExchangeByCurrencyType(CurrencyType type) {
        if (type == null) {
            return null;
        }
        switch (type) {
            case OIL:
                return ExchangeType.ET_OIL;
            case STEEL:
                return ExchangeType.ET_STEEL;
            case RARE_EARTH:
                return ExchangeType.ET_RARE_EARTH;
            case TIBERIUM:
                return ExchangeType.ET_TIBERIUM;
            case DIAMOND:
                return ExchangeType.ET_DIAMOND;
            default: {
                return null;
            }
        }
    }

    @Nullable
    public ShopTemplate findItemShopConf(int itemId) {
        return itemPriceMap.get(itemId);
    }

    /**
     * 数据的合法性检测
     *
     * @throws GeminiException 框架异常
     */
    @Override
    public void checkValid() throws ResourceException {
        int maxGroup = this.getMaxGroup();
        List<Integer> array = getResHolder().getConstTemplate(ConstDiscountShopTemplate.class).getRefreshNum();
        if (maxGroup > array.size()) {
            throw new ResourceException(StringUtils.format("DiscountShopTemplate config group out of array range，maxGroup:{},array={}", maxGroup, array));
        }
        testBestAlgorithm();

        for (ShopTemplate shopTemplate : getResHolder().getListFromMap(ShopTemplate.class)) {
            if (shopTemplate.getNumLimitGroupId() > 0) {
                Map<Integer, ShopLimitTemplate> shopLimitTemplateMap = shopLimitMap.get(shopTemplate.getNumLimitGroupId());
                if (shopLimitTemplateMap == null) {
                    throw new ResourceException("商城限购表组id不存在 shopId={} groupId={}", shopTemplate.getId(), shopTemplate.getNumLimitGroupId());
                }
            }
        }
    }

    public static void main(String[] args) {
        ResLoader.load("D:\\workspace\\cnc\\cnc_server\\external\\game_data\\server");
        ShopDataTemplateService service = ResHolder.getResService(ShopDataTemplateService.class);
        ((org.apache.logging.log4j.core.Logger) LOGGER).setLevel(Level.INFO);
        service.testBestAlgorithm();
    }

    private void testBestAlgorithm() {
        // 一些有趣的case，结果需要人肉观测一下
        ExchangeType type = ExchangeType.ET_TIME_SEC;
        List<Long> funnyTestCases = Lists.newArrayList(
                Duration.ofHours(30).getSeconds(),
                Duration.ofDays(1).plusHours(23).plusMinutes(12).plusSeconds(35).getSeconds(),
                Duration.ofHours(75).getSeconds(),
                Duration.ofHours(30).plusMinutes(5).plusSeconds(35).getSeconds(),
                Duration.ofHours(31).plusMinutes(59).getSeconds(),
                Duration.ofHours(54).getSeconds(),
                Duration.ofHours(54).plusMinutes(1).getSeconds(),
                Duration.ofHours(55).getSeconds(),
                Duration.ofDays(4).plusHours(15).plusMinutes(6).plusSeconds(40).getSeconds(), // 6921
                Duration.ofDays(3).plusHours(20).plusMinutes(43).plusSeconds(20).getSeconds(), // 5900
                Duration.ofDays(77).plusHours(20).plusMinutes(43).plusSeconds(20).getSeconds() // 5900
        );

        for (Long testCase : funnyTestCases) {
            calcBest(type, testCase, true);
        }
        // 批量样例
        List<ConversionGoldTemplate> conf = exchangeTypeMap.get(type);
        long gcd = 0;
        for (int i = 0; i < conf.size() - 1; i++) {
            gcd = gcd(conf.get(i).getValue(), conf.get(i + 1).getValue());
        }
        if (gcd > 0) {
            long prePrice = 0;
            Instant t1 = Instant.now();
            int loopTimes = 0;
            for (long i = gcd; i < conf.get(0).getValue(); i += gcd) {
                loopTimes++;
                Map<ConversionGoldTemplate, Integer> best = calcBest(type, i, false);
                long price = best.entrySet().stream().mapToLong(e -> (long) e.getKey().getPriceValue() * e.getValue()).sum();
                if (price < prePrice) {
                    LOGGER.error("algorithm incorrect!: {} {}->{} {}>{}", type, i - gcd, i, prePrice, price);
                    break;
                }
            }
            LOGGER.info("testBestAlgorithm cost {} ms, loopTimes={}", Duration.between(t1, Instant.now()).toMillis(), loopTimes);
        }
    }


    /**
     * 计算出最便宜的购买方案
     * <p>
     * 修改此算法需要打开checkValid中的校验步骤{@link #testBestAlgorithm()}，还是有一点点复杂滴
     */
    private Map<ConversionGoldTemplate, Integer> calcBest(ExchangeType type, long require, boolean printProgress) {
        final List<ConversionGoldTemplate> conf = exchangeTypeMap.get(type);

        class Node {
            final ConversionGoldTemplate template;
            final int num;
            final long sumPrice;
            final long sumValue;
            final Node father;

            Node(ConversionGoldTemplate template, int num, long sumPrice, long sumValue, Node father) {
                this.template = template;
                this.num = num;
                this.sumPrice = sumPrice;
                this.sumValue = sumValue;
                this.father = father;
            }

            @Override
            public String toString() {
                StringBuilder builder = new StringBuilder()
                        .append("[");
                List<Node> list = Lists.newArrayList();
                Node f = this;
                while (f != null && f.template != null) {
                    list.add(f);
                    f = f.father;
                }
                if (!list.isEmpty()) {
                    for (int i = list.size() - 1; i >= 0; i--) {
                        builder.append(template.getId()).append("=").append(list.get(i).num).append(", ");
                    }
                }
                return builder
                        .append("]")
                        .toString();
            }
        }
        Node bestNode = null;
        List<List<Node>> tree = Lists.newArrayList();
        tree.add(Lists.newArrayList(new Node(null, 0, 0, 0, null)));

        int loopTimes = 0;
        for (int lay = 0; lay < conf.size(); lay++) {
            ConversionGoldTemplate cur = conf.get(lay);
            List<Node> upLayerNodes = tree.get(lay);
            if (upLayerNodes.isEmpty()) {
                break;
            }
            List<Node> curLayerNodes = Lists.newLinkedList();
            for (Node upLayerNode : upLayerNodes) {
                long req = require - upLayerNode.sumValue;
                if (req > 0) {
                    loopTimes++;
                    long times = req / cur.getValue();
                    if (req % cur.getValue() > 0) {
                        times++;
                    }

                    Node layerLeaf = new Node(cur,
                            (int) times,
                            upLayerNode.sumPrice + (cur.getPriceValue() * times),
                            upLayerNode.sumValue + (cur.getValue() * times),
                            upLayerNode
                    );
                    curLayerNodes.add(layerLeaf);
                    if (bestNode == null || layerLeaf.sumPrice < bestNode.sumPrice) {
                        bestNode = layerLeaf;
                    }
                    if (lay < conf.size() - 1) {
                        ConversionGoldTemplate next = conf.get(lay + 1);
                        for (long n = times - 1; n >= 0; n--) {
                            loopTimes++;
                            long sumP = upLayerNode.sumPrice + (cur.getPriceValue() * n);
                            if (sumP > bestNode.sumPrice) {
                                break;
                            }
                            long sumV = upLayerNode.sumValue + (cur.getValue() * n);
                            long reqValue4nextLayer = require - sumV;
                            // 找到在这一层之下理论上能达到的最便宜价格，如果还是高过当前最佳，则无视掉
                            long minPrice4NextLayer = reqValue4nextLayer * next.getPriceValue() / next.getValue();
                            if (sumP + minPrice4NextLayer >= bestNode.sumPrice) {
                                break;
                            }
                            curLayerNodes.add(new Node(cur, (int) n, sumP, sumV, upLayerNode));
                        }
                    }
                }
            }
            tree.add(curLayerNodes);
        }


        Map<ConversionGoldTemplate, Integer> best = Maps.newIdentityHashMap();
        while (bestNode != null && bestNode.template != null) {
            if (bestNode.num > 0) {
                best.put(bestNode.template, bestNode.num);
            }
            bestNode = bestNode.father;
        }

        if (printProgress) {
            long price = best.entrySet().stream().mapToLong(e -> (long) e.getKey().getPriceValue() * e.getValue()).sum();
            StringBuilder s = new StringBuilder();
            for (Entry<ConversionGoldTemplate, Integer> e : best.entrySet()) {
                s.append(e.getKey().getId()).append("=").append(e.getValue()).append(",");
            }
            var d = DurationFormatUtils.formatDuration(Duration.ofSeconds(require).toMillis(), "d.H.m.s");
            LOGGER.info("printCalcProgress [{}-{}-{}]: {}    {} treeSize:{} loopTimes:{}",
                    type, require, d, price, s, tree.stream().mapToInt(List::size).sum(), loopTimes);
        }

        return best;
    }

    // 最大公约数
    private static long gcd(long m, long n) {
        while (n > 0) {
            long temp = m % n;
            m = n;
            n = temp;
        }
        return m;
    }

    /**
     * 在正常逻辑上添加的天limit限制，所以保守处理
     */
    public ShopLimitTemplate getDayLimitWithServer(int limitGroupId, int mainCityLevel) {
        if (limitGroupId <= 0) {
            return null;
        }
        if (!shopLimitMap.containsKey(limitGroupId)) {
            return null;
        }
        Map<Integer, ShopLimitTemplate> shopLimitTemplates = shopLimitMap.get(limitGroupId);
        ShopLimitTemplate limitTemplate = shopLimitTemplates.get(mainCityLevel);
        if (limitTemplate == null) {
            return null;
        }
        return limitTemplate;
    }
}
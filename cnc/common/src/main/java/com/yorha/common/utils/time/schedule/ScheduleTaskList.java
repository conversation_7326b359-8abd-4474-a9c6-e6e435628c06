package com.yorha.common.utils.time.schedule;

import com.yorha.common.exception.GeminiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 定时任务双向队列
 * 非线程安全
 *
 * <AUTHOR>
 */
public final class ScheduleTaskList {
    private static final Logger LOGGER = LogManager.getLogger(ScheduleTaskList.class);
    private int size = 0;
    private ScheduleTask first;
    private ScheduleTask last;

    /**
     * 尾插, 需要小心，task重复插入问题
     *
     * @param task 要添加的任务
     */
    public boolean add(ScheduleTask task) {
        if (task.getPre() != null) {
            throw new GeminiException("add error schedule task: {} pre: {}", task, task.getPre());
        }
        if (task.getNext() != null) {
            throw new GeminiException("add error schedule task: {} next: {}", task, task.getNext());
        }
        if (first != null && first.equals(task)) {
            LOGGER.error("add error schedule repeated task:{} first: {} last: {}", task, first, last);
            return false;
        }
        if ((size > 0)) {
            if ((first == null) || (last == null)) {
                throw new GeminiException("add schedule failed task: {} size: {} first: {} last: {}", task, first, last);
            }
        }
        linkLast(task);
        return true;
    }

    /**
     * poll
     *
     * @return 头节点
     */
    public ScheduleTask poll() {
        final ScheduleTask f = first;
        return (f == null) ? null : unlinkFirst(f);
    }

    /**
     * 删除
     *
     * @param task 删除的任务，需要小心，task的重复删除问题 与 删除已消费的task
     * @return 删除结果
     */
    public boolean remove(ScheduleTask task) {
        if (task == null) {
            return false;
        }
        if ((task.getPre() == null) && (task.getNext() == null)) {
            if (first == null || !first.equals(task)) {
                return false;
            }
        }
        unlink(task);
        return true;
    }


    private void unlink(ScheduleTask task) {
        if (task == null) {
            return;
        }
        final ScheduleTask next = task.getNext();
        final ScheduleTask prev = task.getPre();
        if (prev == null) {
            first = next;
        } else {
            prev.setNext(next);
            task.setPre(null);
        }

        if (next == null) {
            last = prev;
        } else {
            next.setPre(prev);
            task.setNext(null);
        }
        size--;
    }

    private void linkLast(ScheduleTask task) {
        if (task == null) {
            return;
        }
        if (size <= 0) {
            first = task;
        } else {
            last.setNext(task);
            task.setPre(last);
        }
        last = task;
        size++;
    }

    private ScheduleTask unlinkFirst(ScheduleTask f) {
        final ScheduleTask next = f.getNext();
        first = next;
        if (next == null) {
            last = null;
        } else {
            next.setPre(null);
        }
        f.setPre(null);
        f.setNext(null);
        size--;
        return f;
    }

    public int size() {
        if ((first == null) && (size > 0)) {
            LOGGER.error("ScheduleTaskList size: {} last: {}", size, last);
            return 0;
        }
        return size;
    }
}

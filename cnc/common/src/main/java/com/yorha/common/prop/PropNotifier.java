package com.yorha.common.prop;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.Entity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 属性下发客户端的管理器（只管理下发客户端的部分，作为 prop change listener 中可选的一个部分）
 *
 * <AUTHOR>
 */
public abstract class PropNotifier<P extends AbstractPropNode, PBBuilder extends GeneratedMessageV3.Builder<?>>
        extends PropNtfTransfer<P, PBBuilder> {
    private static final Logger LOGGER = LogManager.getLogger(PropNotifier.class);

    /**
     * newEntity 是否已经下发给客户端，为严格控制在new之前不可以下发mod
     */
    private boolean fullPropNotified;

    private boolean isDirty;
    protected PBBuilder changeCollector;

    public PropNotifier(PBBuilder changeCollector) {
        this.fullPropNotified = false;
        this.isDirty = false;
        this.changeCollector = changeCollector;
    }

    /**
     * 是否在线等前置条件
     */
    public abstract boolean canNtfToClient();

    /**
     * 需要实现推送给客户端的方法
     *
     * @return true:下发成功
     */
    public abstract boolean ntfToClient(Entity.EntityNtfMsg notify);

    /**
     * 下线，断线重连等操作需要reset一下，这样
     */
    public void reset() {
        this.fullPropNotified = false;
    }

    public void setFullPropNotified(boolean fullPropNotified) {
        this.fullPropNotified = fullPropNotified;
    }

    public void markChange(P prop) {
        if (!fullPropNotified) {
            // 全量prop没有下发的时候不用收集脏的，反正也用不上
            return;
        }
        if (prop.hasAnyMark() && copyChange(prop, changeCollector) > 0) {
            this.isDirty = true;
        }
    }

    private void tryNotify(P prop) {
        if (!canNtfToClient()) {
            return;
        }
        if (!fullPropNotified) {
            notifyFullProp(prop);
            return;
        }

        if (!isDirty) {
            return;
        }

        Entity.EntityNtfMsg ntf = buildModEntityNtf(changeCollector);
        if (ntfToClient(ntf)) {
            // 没有真正下发的话，不会把收集的脏数据清理掉
            changeCollector.clear();
            this.isDirty = false;
        } else {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("tryNtfChange, but notify failed. {}:{}", ClassNameCacheUtils.getSimpleName(ntf.getClass()), ntf);
            }
        }
    }

    /**
     * 如果没有下发过newEntity，会下发全量的
     */
    public final void tryMarkChangeAndNtf(P prop) {
        markChange(prop);
        tryNotify(prop);
    }

    protected final void notifyFullProp(P prop) {
        changeCollector.clear();
        this.isDirty = false;
        copyFull(prop, changeCollector);
        Entity.EntityNtfMsg ntf = buildNewEntityNtf(changeCollector);
        if (ntfToClient(ntf)) {
            changeCollector.clear();
            this.isDirty = false;
            this.fullPropNotified = true;
        } else {
            WechatLog.error("notifyFullProp failed! {}:{}", ClassNameCacheUtils.getSimpleName(prop.getClass()), prop);
        }
    }

}

package com.yorha.common.helper;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.NodeRole;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.SsTranslator;

/**
 * 翻译专用工具类。
 *
 * <AUTHOR>
 */
public final class TranslateHelper {
    private static final long SENTENCE_MAX_RUNE_LENGTH = 2048;


    /**
     * 翻译文本，注意，这是一个基于actor call的语义。
     *
     * @param actor    call承担者。
     * @param text     字符串。
     * @param language 目标语言。
     * @return 结果字符串。
     */
    public static String translate(final GameActorWithCall actor, final String text, CommonEnum.Language language) {
        if (StringUtils.isEmpty(text)) {
            throw new GeminiException(ErrorCode.TRANSLATE_EMPTY_TEXT);
        }

        if (text.length() > SENTENCE_MAX_RUNE_LENGTH) {
            throw new GeminiException(ErrorCode.TRANSLATE_TOO_LONG);
        }

        String targetLanguage;
        // 转换枚举为ISO-639-1标准代码。https://cloud.google.com/translate/docs/languages?hl=zh-cn
        switch (language) {
            case zh_tw:
                targetLanguage = "zh-TW";
                break;
            case ide:
                targetLanguage = "id";
                break;
            case ja:
                targetLanguage = "ja";
                break;
            default:
                targetLanguage = language.name();
                break;
        }
        final SsTranslator.TranslateTextAsk ask = SsTranslator.TranslateTextAsk.newBuilder()
                .setText(text)
                .setTargetLanguage(targetLanguage)
                .build();

        final int actorNum = Math.max(actor.system().getRegistryValue().getServerTypeNodeList(NodeRole.Global.getTypeId()).size(), 1);
        final long translatorId = StringUtils.getStringHashId(text, actorNum);
        IActorRef target = RefFactory.ofTranslator(translatorId);
        final SsTranslator.TranslateTextAns ans = actor.call(target, ask);
        if (!ans.hasText()) {
            throw new GeminiException("translate fail!");
        }
        return ans.getText();
    }
}

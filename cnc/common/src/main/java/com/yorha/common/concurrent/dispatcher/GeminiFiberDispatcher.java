package com.yorha.common.concurrent.dispatcher;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.yorha.common.concurrent.IGeminiExecutor;
import com.yorha.common.concurrent.executor.GeminiFiberExecutor;
import com.yorha.common.concurrent.executor.GeminiThreadExecutor;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 支持协程的任务派发器
 *
 */
@Deprecated
public class GeminiFiberDispatcher extends GeminiDispatcher {
    private static final Logger LOGGER = LogManager.getLogger(GeminiFiberDispatcher.class);

    /**
     * 协程池，由caffeine做淘汰策略
     */
    private final LoadingCache<Object, GeminiFiberExecutor> fiberLoadingCache;
    /**
     * 标记是否关闭
     */
    private volatile boolean isShutdown = false;

    /**
     * GeminiFiberDispatcher 构造函数
     *
     * @param poolName      池名称，用于标识该派发器的名称
     * @param threadNum     线程消费者数量，控制并行处理任务的线程数
     * @param fiberTaskSize 协程队列大小，每个协程执行器的任务队列容量
     */
    GeminiFiberDispatcher(final String poolName, final int threadNum, final int fiberTaskSize) {
        super(poolName, threadNum, 16384);
        this.fiberLoadingCache = Caffeine.newBuilder()
                .scheduler(SystemScheduleMgr.getInstance().getScheduler())
//                .expireAfterAccess(30, TimeUnit.SECONDS)
                .removalListener(((key, value, cause) -> {
                    LOGGER.info("GeminiFiberDispatcher removalListener {} {} {}", key, value, cause);
                    GeminiFiberExecutor executor = (GeminiFiberExecutor) value;
                    if (executor == null) {
                        return;
                    }
                    executor.shutdown();
                }))
                .build(key -> {
                    if (this.isShutdown) {
                        return null;
                    }
                    final GeminiThreadExecutor executor = (GeminiThreadExecutor) super.getExecutor(key);
                    return new GeminiFiberExecutor(key, executor, fiberTaskSize);
                });
    }


    @Override
    public IGeminiExecutor getExecutor(Object id) {
        return fiberLoadingCache.get(id);
    }

    @Override
    public synchronized void shutdown() {
        if (this.isShutdown) {
            return;
        }
        this.isShutdown = true;
        this.fiberLoadingCache.invalidateAll();
        super.shutdown();
    }
}

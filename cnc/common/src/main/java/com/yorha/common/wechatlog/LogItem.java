package com.yorha.common.wechatlog;


import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.message.Message;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class LogItem {

    private static final Logger LOGGER = LogManager.getLogger(LogItem.class);

    // error event
    public static final int ERROR = 1;
    // An event for informational purposes.
    public static final int INFO = 3;

    /**
     * 日志等级
     */
    private int level;

    /**
     * 线程名称
     */
    private final String threadName;

    /**
     * 类名
     */
    private String className;

    /**
     * busId
     */
    private final String busId;

    /**
     * 限频等级
     * 用于限制企微告警频率
     */
    private final AtomicInteger limitLevel = new AtomicInteger(0);

    /**
     * 过期时间
     */
    private final LongAdder cacheExpireTsMs = new LongAdder();

    /**
     * 缓存key
     */
    private final String cacheKey;

    /**
     * IP地址
     */
    private final String ipAddress;

    /**
     * 分支
     */
    private final String branch;

    /**
     * 过期时间 ms 1小时
     */
    private static final long EXPIRE_MS = 3600_000;

    private final AtomicInteger triggerTimes = new AtomicInteger(0);


    public LogItem(int level, WechatConfig config, String cacheKey, String ipAddress, String branch) {
        this.level = level;
        this.cacheKey = cacheKey;
        this.threadName = buildThreadInfo();
        this.busId = config.getBusId();
        this.ipAddress = ipAddress;
        this.branch = branch;
    }

    private String buildThreadInfo() {
        StringBuilder threadNameBuilder = new StringBuilder();
        if (Thread.currentThread().isVirtual()) {
            threadNameBuilder.append("[").append(Thread.currentThread().getName()).append("]");
        } else {
            threadNameBuilder.append(Thread.currentThread().getName());
        }
        return threadNameBuilder.toString();
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getBusId() {
        return busId;
    }

    public int getLimitLevel() {
        return limitLevel.get();
    }

    public long getCacheExpireTsMs() {
        return cacheExpireTsMs.longValue();
    }

    public void upCacheExpire() {
        cacheExpireTsMs.reset();
        cacheExpireTsMs.add(SystemClock.now() + EXPIRE_MS);
    }

    public void upLimitLevel() {
        limitLevel.incrementAndGet();
    }

    public void addTriggerTimes() {
        triggerTimes.incrementAndGet();
    }

    public int getTriggerTimes() {
        return triggerTimes.get();
    }

    private static StringBuilder printStackTrace(StackTraceElement[] stackTrace) {
        StringBuilder sb = new StringBuilder(2048);
        for (StackTraceElement e : stackTrace) {
            if (sb.length() > 2000) {
                return sb.append("\tat ").append("......");
            }
            sb.append("\tat ").append(e).append("\n");
        }
        return sb;
    }

    @Override
    public String toString() {
        return "LogItem{" +
                "level=" + level +
                ", threadName='" + threadName + '\'' +
                ", className='" + className + '\'' +
                ", busId='" + busId + '\'' +
                ", limitLevel=" + limitLevel +
                ", cacheExpireTsMs=" + cacheExpireTsMs +
                ", cacheKey='" + cacheKey + '\'' +
                ", triggerTimes=" + triggerTimes +
                '}';
    }

    private String toInfo(Message message) {
        StringBuilder sb = new StringBuilder();
        sb.append("{\n" +
                "\"msgtype\": \"markdown\",\n" +
                "\"markdown\": {\n" +
                "\"content\":\n" + "\"");

        sb.append("**busId**: ").append(busId).append("\n");
        sb.append("**msg**: ").append(message.getFormattedMessage()).append("\n");

        return sb.append("\"\n" +
                "}\n" +
                "}").toString();
    }

    public String toMessage(Message message) {
        if (getLevel() == LogItem.INFO) {
            return toInfo(message);
        }

        StringBuilder sb = new StringBuilder();
        sb.append("{\n" +
                "\"msgtype\": \"markdown\",\n" +
                "\"markdown\": {\n" +
                "\"content\":\n" + "\"");

        switch (level) {
            case 1: {
                sb.append("# <font color='warning'>\uD83D\uDC1E Exception Monitor</font>\n");
                break;
            }
            case 2: {
                sb.append("# <font color='warning'>⚠ Warning Monitor</font>\n");
                break;
            }
            case 3: {
                sb.append("# <font color='info'>\uD83C\uDFAE Info Monitor</font>\n");
                break;
            }
            default: {
                sb.append("# <font color='comment'>\uD83D\uDCBB Monitor</font>\n");
            }
        }
        sb.append("    **triggerTimes**:").append(triggerTimes).append("\n");

        if (StringUtils.isNotEmpty(busId)) {
            sb.append("    **BusId**:<font color='comment'>").append(busId).append(" </font>\n");
        }
        if (StringUtils.isNotEmpty(ipAddress)) {
            sb.append("    **ip**:<font color='comment'>").append(ipAddress).append(" </font>\n");
        }
        if (StringUtils.isNotEmpty(branch)) {
            sb.append("    **branch**:<font color='comment'>").append(branch).append(" </font>\n");
        }

        appendMessageToSb(message, sb);

        return sb.append("\"\n" +
                "}\n" +
                "}").toString();
    }

    private static void appendMessageToSb(Message message, StringBuilder sb) {
        sb.append("    **curTime**:").append(SystemClock.nowInstant().atZone(TimeUtils.getZoneId()).format(TimeUtils.FORMATTER)).append("\n");
        sb.append("    **nativeTime**:").append(TimeUtils.nowNative2String()).append("\n");
        if (message == null) {
            return;
        }
        Throwable throwable = message.getThrowable();
        String detail;
        if (throwable == null) {
            String format = StringUtils.format("LogItem buildOrSetMessage fail, throwable is null param={} sb={}", message, sb);
            LOGGER.error(format);
            detail = format;
        } else {
            int i = 20;
            while (throwable.getCause() != null) {
                if (i > 0) {
                    i--;
                } else {
                    LOGGER.error("throwable toooo long message:{}", message);
                    break;
                }
                throwable = throwable.getCause();
            }

            if (throwable.getStackTrace().length > 0) {
                detail = "\n\t" + throwable + "\n" + printStackTrace(throwable.getStackTrace());
            } else {
                detail = "\n\t" + throwable;
            }
        }

        String msg = message.getFormattedMessage();
        if (StringUtils.isNotEmpty(msg)) {
            msg = msg.replace("\"", "'");
            sb.append("    **message**:").append(msg).append("\n");
        }
        if (StringUtils.isNotEmpty(detail)) {
            detail = detail.replace("\"", "'");
            sb.append("    > detail :<font color='comment'>").append(detail).append("</font>\n");
        }
    }
}


package com.yorha.common.actorservice;

/**
 * 系统中的actor类型
 *
 * <AUTHOR>
 */
public enum ActorRole {
    /**
     * 集群leader：无分片，自定义创建
     */
    Leader,

    /**
     * 游戏服节点Actor：每个进程一个实例：无分片、本地创建，每个节点一个实例
     */
    Node,

    /**
     * 游戏翻译角色：分片，预分配。
     */
    Translator,

    /**
     * 联盟actor：分片，通过Leader分配。
     */
    Clan,

    /**
     * 玩家Actor：分片，通过Leader分配。
     */
    Player,

    /**
     * zone：分片，通过Leader分配。
     */
    ZoneChat,

    /**
     * 场景actor：无分片，通过Leader分配。
     */
    Scene,

    /**
     * 邮件actor：无分片，本地创建。
     */
    Mail,

    /**
     * 排行榜：无分片，本地创建。
     */
    Rank,

    /**
     * zone级别单例名称服务Actor：无分片，本地创建。
     */
    Name,

    /**
     * 商店单例actor：无分片，本地创建。
     */
    Store,

    /**
     * IdIp进程
     */
    IdIp,

    /**
     * idip 处理actor: 无分片，本地创建。
     */
    IdIpSession,
    /**
     * 游戏服务器的网关服务: 分片，本地创建。
     */
    Gate,
    /**
     * gate 处理actor: 无分片，本地创建。
     */
    GateSession,
    /**
     * 副本
     */
    Dungeon,

    /**
     * 敏感词文本过滤：分片，预分配。
     */
    TextFilter,

    /**
     * 寻路Actor：无分片，本地创建.
     */
    PathFinding,

    /**
     * IMur问卷回调处理actor: 无分片，本地创建
     */
    IMurSession,

    /**
     * 米大师
     */
    MidasAgent,
    /**
     * 战斗模拟器
     */
    BattleSimulator,

    /**
     * midas回调处理actor: 无分片，本地创建。
     */
    MidasCallbackSession,

    /**
     * AoiViewActor：无分片，本地创建.
     */
    AoiView,

    /**
     * 聊天Actor：无分片，本地创建
     */
    GroupChat,

    /**
     * SceneActor上的存库序列化处理actor
     */
    Serialize,

    /**
     * 名片缓存
     */
    PlayerCard,
    /**
     * 名片缓存
     */
    ClanCard,
    /**
     * 服务器信息
     */
    ZoneCard,
    /**
     * 鉴权
     */
    Auth,
    /**
     * 推送
     */
    PushNotification,
    /**
     * 监控节点，每个节点一个实例
     */
    Monitor,
    /**
     * 防沉迷
     */
    AntiAddiction,
    /**
     * 战斗
     */
    Battle
}

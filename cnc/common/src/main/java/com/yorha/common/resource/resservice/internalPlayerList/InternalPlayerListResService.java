package com.yorha.common.resource.resservice.internalPlayerList;

import com.google.common.collect.Sets;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import res.template.InternalDeviceTemplate;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class InternalPlayerListResService extends AbstractResService {
    private final Set<String> internalDeviceList = Sets.newTreeSet();

    public InternalPlayerListResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getListFromMap(InternalDeviceTemplate.class).forEach(t -> {
            internalDeviceList.add(t.getDeviceId());
        });
    }

    public boolean isInternalDevice(String device) {
        return internalDeviceList.contains(device);
    }

    @Override
    public void checkValid() throws ResourceException {

    }
}

package com.yorha.common.qlog;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;

/**
 * qlog.xml加载
 *
 * <AUTHOR>
 */
public class MetalibLoader {
    private static final Logger LOGGER = LogManager.getLogger(MetalibLoader.class);

    Metalib loadRes;

    public Metalib getMetaLib() {
        return loadRes;
    }

    public void load(String xmlPath) {
        loadRes = new Metalib();
        InputStream xml = Thread.currentThread().getContextClassLoader().getResourceAsStream(xmlPath);
        SAXReader reader = new SAXReader();
        try {
            Document doc = reader.read(xml);
            Element metaLib = doc.getRootElement();
            //获取各个struct
            for (Iterator i = metaLib.elementIterator(); i.hasNext(); ) {
                Element struct = (Element) i.next();
                MetaStruct metaStruct = new MetaStruct();
                for (Iterator<?> k = struct.attributeIterator(); k.hasNext(); ) {
                    Attribute tmpAttr = (Attribute) k.next();
                    metaStruct.setAttr(tmpAttr.getName(), tmpAttr.getValue());
                }
                loadRes.addStruct(metaStruct);
                //获取entry
                int idx = 1;
                for (Iterator j = struct.elementIterator(); j.hasNext(); ++idx) {
                    Element entry = (Element) j.next();
                    MetaEntry metaEntry = new MetaEntry(idx);
                    //获取attribute
                    for (Iterator<?> k = entry.attributeIterator(); k.hasNext(); ) {
                        Attribute tmpAttr = (Attribute) k.next();
                        metaEntry.setAttr(tmpAttr.getName(), tmpAttr.getValue());
                    }
                    metaStruct.addEntry(metaEntry);
                }
            }
        } catch (DocumentException e) {
            LOGGER.error("loading xml is error", e);
            throw new RuntimeException(String.format("load qlog %s error!", xmlPath));
        }
    }

    public static class Meta {
        HashMap<String, String> attrMap = new HashMap<>();

        public void setAttr(String key, String val) {
            attrMap.put(key, val);
        }

        public String getAttr(String key) {
            if (attrMap.containsKey(key)) {
                return attrMap.get(key);
            } else {
                return "";
            }
        }

    }

    public static class MetaStruct extends Meta {

        HashMap<String, MetaEntry> entries = new HashMap<>();

        public boolean hasEntry(String field) {
            return entries.containsKey(field);
        }

        public void addEntry(MetaEntry item) {
            entries.put(item.getAttr("name"), item);
        }

        public int fieldCnt() {
            return entries.size();
        }

        public MetaEntry getEntry(String key) {
            return entries.get(key);
        }

    }

    public static class MetaEntry extends Meta {
        int idx;

        MetaEntry(int idx) {
            this.idx = idx;
        }

        public int getIdx() {
            return idx;
        }
    }

    public static class Metalib extends Meta {
        HashMap<String, MetaStruct> structs = new HashMap<>();

        public void addStruct(MetaStruct struct) {
            structs.put(struct.getAttr("name"), struct);
        }

        public boolean hasStruct(String name) {
            return structs.containsKey(name);
        }

        public MetaStruct getStruct(String name) {
            return structs.get(name);
        }
    }


}

package com.yorha.common.resource.datatype;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class CircleType {
    private static final String UNDERLINE = "_";
    private static final String COMMA = ",";
    private static final int SIZE = 3;
    private final int x;
    private final int y;
    private final int r;

    private CircleType(int x, int y, int r) {
        this.x = x;
        this.y = y;
        this.r = r;
    }

    public static CircleType valueOf(String s) {
        int[] tripleInt = Arrays.stream(s.split(UNDERLINE)).mapToInt(num -> Integer.parseInt(num)).toArray();
        if (tripleInt.length == SIZE) {
            return new CircleType(tripleInt[0], tripleInt[1], tripleInt[2]);
        }
        return new CircleType(tripleInt[0], tripleInt[1], tripleInt[2]);
    }

    public static CircleType parseFromString(String s) {
        int[] tripleInt = Arrays.stream(s.split(UNDERLINE)).mapToInt(num -> Integer.parseInt(num)).toArray();
        if (tripleInt.length == SIZE) {
            return new CircleType(tripleInt[0], tripleInt[1], tripleInt[2]);
        }
        return new CircleType(tripleInt[0], tripleInt[1], tripleInt[2]);
    }

    public static List<CircleType> parseListFromString(String s) {
        List<CircleType> retList = new ArrayList<>();
        Arrays.stream(s.split(COMMA)).map(iStr -> retList.add(CircleType.parseFromString(iStr))).collect(Collectors.toList());
        return retList;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public int getR() {
        return r;
    }
}

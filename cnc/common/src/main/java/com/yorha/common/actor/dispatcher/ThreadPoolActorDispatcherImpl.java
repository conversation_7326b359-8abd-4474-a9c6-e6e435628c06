package com.yorha.common.actor.dispatcher;

import com.yorha.common.actor.mailbox.IRunnableActorMailbox;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.exception.GeminiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * 线程级别的Dispatcher。
 *
 * <AUTHOR>
 */
public class ThreadPoolActorDispatcherImpl implements IMailboxDispatcher {
    private static final Logger LOGGER = LogManager.getLogger(ThreadPoolActorDispatcherImpl.class);
    private final int throughPut;
    private final GeminiThreadPoolExecutor threadPoolExecutor;

    public ThreadPoolActorDispatcherImpl(final String name, final int throughput, final int parallelism) {
        LOGGER.info("create thread pool actor dispatcher, name:{} throughput:{} parallelism:{}", name, throughput, parallelism);
        this.throughPut = throughput;
        this.threadPoolExecutor = ConcurrentHelper.newFixedThreadExecutor(name, parallelism, 0, false);
    }

    @Override
    public String getName() {
        return this.threadPoolExecutor.getName();
    }

    @Override
    public int getThroughput() {
        return this.throughPut;
    }

    @Override
    public void schedule(IRunnableActorMailbox mb) {
        this.threadPoolExecutor.execute(mb);
    }

    @Override
    public void setParallelism(int parallelism) {
        this.threadPoolExecutor.setCorePoolSize(parallelism);
    }

    @Override
    public int getParallelism() {
        return this.threadPoolExecutor.getCorePoolSize();
    }

    @Override
    public int getFiberCnt(String actorRole) {
        return 0;
    }

    @Override
    public int getKeepAliveSec() {
        return 0;
    }

    @Override
    public void shutdown() {
        if (this.threadPoolExecutor.isShutdown()) {
            throw new GeminiException("{} Already Shutdown!", this);
        }
        this.threadPoolExecutor.shutdown();
        try {
            final boolean isFinished = this.threadPoolExecutor.awaitTermination(10, TimeUnit.SECONDS);
            if (!isFinished) {
                LOGGER.warn("{} Shutdown! NotFinished After 10s!", this);
            }
        } catch (InterruptedException e) {
            LOGGER.warn("{} Shutdown! But Interrupted!", this);
        }
    }

    @Override
    public String toString() {
        return "ThreadActorDispatcher{" +
                "name='" + this.getName() + '\'' +
                ", throughPut=" + throughPut +
                '}';
    }
}

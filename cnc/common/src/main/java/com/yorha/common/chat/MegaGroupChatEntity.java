package com.yorha.common.chat;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.db.tcaplus.msg.IncreaseAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsPlayerChat;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * 大范围的聊天基类，主要是联盟聊天、全服（小服）聊天。
 * 1. 包含一个FIFO的缓存，只存储最近的ChatHelper.getChatReqMessageNumber() * ChatConstants.CACHE_PAGE_SIZE（200）条。
 * 2. 采用IdFactory发号下一个消息id的方式，发送下一个Id，插入id对应的聊天到ChatTable。
 * 3. 直接读取db的聊天能力。
 *
 * <AUTHOR>
 */

public abstract class MegaGroupChatEntity {
    private static final Logger LOGGER = LogManager.getLogger(MegaGroupChatEntity.class);
    /**
     * 通信actor
     */
    protected final GameActorWithCall actor;
    /**
     * 聊天缓存
     * Entity初始化时加载，用call的方式，如果使用ask，在缓存未加载完毕时可能大量请求击穿到DB
     * 只缓存200条（20条/页 * 10页）最新聊天
     */
    protected final ChatCache chatMsgCache;
    /**
     * 当前最大消息id
     */
    private long lastMsgId;


    public MegaGroupChatEntity(GameActorWithCall actor) {
        this.actor = actor;
        // 初始化消息id
        this.lastMsgId = this.loadChatMaxIndexByIdFactory(this.getIdFactoryKey(), this.getChannelId());
        // 初始化消息缓存
        this.chatMsgCache = new ChatCache(ChatHelper.getMaxChatMsgCount(), this.lastMsgId);
    }

    /**
     * 获取聊天消息请求
     *
     * @param fromId     聊天信息起始id，为0代表从最大的id搜索。
     * @param toId       聊天信息终止id。
     * @param shieldList 屏蔽的发送者列表。
     * @param context    上下文。
     */
    public abstract void queryChatMsgs(long fromId, long toId, List<Long> shieldList, ActorMsgEnvelope context);

    /**
     * 广播聊天消息。
     *
     * @param finalChatMsg 聊天消息。
     */
    protected abstract void broadcastMessage(CommonMsg.ChatMessage finalChatMsg);

    /**
     * 回应player -> chat的回包。
     *
     * @param context   聊天上下文。
     * @param message   消息。
     * @param throwable 错误。
     */
    protected abstract void answerChatRequest(@Nullable ActorMsgEnvelope context, CommonMsg.ChatMessage message, Throwable throwable);

    /**
     * 当前entity对应的聊天频道。
     *
     * @return 聊天频道
     */
    protected abstract CommonEnum.ChatChannel chatChannel();

    /**
     * 当前entity对应的聊天id。
     *
     * @return channelId。
     */
    protected abstract String getChannelId();

    /**
     * 聊天消息id发号器。
     *
     * @return IdFactoryTable的发号器key，生成聊天消息的MsgId。
     */
    public abstract String getIdFactoryKey();

    /**
     * 请求聊天。
     *
     * @param chatMessage 聊天消息。
     * @param context     聊天上下文，可以为null。当入参为null，表示此消息不需要做有效性校验。
     */
    public final void chatRequest(@NotNull CommonMsg.ChatMessage chatMessage, @Nullable final ActorMsgEnvelope context) {
        this.insertChatMsg(chatMessage, context);
    }

    /**
     * @return 最近消息id。
     */
    public final long getLastMsgId() {
        return this.lastMsgId;
    }

    /**
     * 处理被@的消息，回拉起被@的人。
     */
    protected final void handleAtMsg(CommonMsg.ChatMessage chatMessage) {
        final Map<Long, Integer> playerZonesMap = chatMessage.getMessageData().getAtInfo().getPlayerZonesMap();
        SsPlayerChat.HandleNewMessageAsk.Builder builder = SsPlayerChat.HandleNewMessageAsk.newBuilder();
        builder.setNewMessage(chatMessage)
                .getChatSessionBuilder()
                .setChannelType(chatChannel())
                .setChatChannelId(this.getChannelId());

        final SsPlayerChat.HandleNewMessageAsk msg = builder.build();
        for (final Map.Entry<Long, Integer> entry : playerZonesMap.entrySet()) {
            // 发送消息给对应player
            sendMsgToPlayer(entry.getValue(), entry.getKey(), msg);
        }
    }

    /**
     * 捞频道最大消息index
     */
    private long loadChatMaxIndexByIdFactory(final String key, final String channelId) {
        try {
            // 获取当前数据库里所存的最大id
            {
                final GetOption option = GetOption.newBuilder().withGetAllFields(true).build();
                final TcaplusDb.IdFactoryTable.Builder req = TcaplusDb.IdFactoryTable.newBuilder().setZoneId(0).setKey(key);
                final SelectUniqueAsk<TcaplusDb.IdFactoryTable.Builder> selectAsk = new SelectUniqueAsk<>(req, option);
                final GetResult<TcaplusDb.IdFactoryTable.Builder> res = actor.callGameDb(selectAsk);

                // 存在数据，则分会当前id
                if (res.isOk()) {
                    return res.value.getNextId();
                }

                // 非记录不存在，则重新构造
                if (!res.isRecordNotExist()) {
                    throw new GeminiException("get chat id by count from db failed. {} code:{}", channelId, res.getCode());
                }
            }

            // 当前不存在id，则需要重建数据
            {
                final TcaplusDb.IdFactoryTable.Builder insertRequest = TcaplusDb.IdFactoryTable.newBuilder().setZoneId(0).setNextId(0).setKey(key);
                final InsertAsk<TcaplusDb.IdFactoryTable.Builder> insertAsk = new InsertAsk<>(insertRequest);
                final InsertResult<TcaplusDb.IdFactoryTable.Builder> insertResult = actor.callGameDb(insertAsk);

                // 插入失败
                if (!insertResult.isOk()) {
                    throw new GeminiException("insert chat msgIndex failed. code={}", insertResult.getCode());
                }

                return insertResult.value.getNextId();
            }
        } catch (Exception e) {
            LOGGER.error("MegaGroupChatEntity loadChatMaxIndexByIdFactory error: channelType={} , channelId={}, e=",
                    chatChannel().toString(), channelId, e);
            throw e;
        }
    }

    /**
     * 添加聊天记录
     * 先自增ChatDescriptionTable中的maxIndex
     * 根据自增后的maxIndex设置消息id并插入chatTable
     * 消息存缓存、更新内存中的lastMsgId、广播通知玩家
     * <p>
     * insertChatMsg很可能错序，因为异步执行顺序不确定，很可能后一条消息比前一条消息先插入DB，玩家收到消息的顺序也会不保序，后续的处理逻辑一定要兼容错序
     */
    private void insertChatMsg(final CommonMsg.ChatMessage chatMessage, @Nullable final ActorMsgEnvelope context) {
        final GeminiStopWatch watch = new GeminiStopWatch("chat request");
        // 自增maxIndex请求
        TcaplusDb.IdFactoryTable.Builder req = TcaplusDb.IdFactoryTable.newBuilder();
        req.setZoneId(0).setKey(this.getIdFactoryKey()).setNextId(1);
        IncreaseOption.Builder increaseOption = IncreaseOption.newBuilder();
        increaseOption.setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_REQUEST);
        IncreaseAsk<TcaplusDb.IdFactoryTable.Builder> increaseAsk = new IncreaseAsk<>(req, increaseOption.build());
        actor.askGameDb(increaseAsk)
                .onComplete((increaseResult, increaseException) -> {
                    if (increaseException != null) {
                        LOGGER.error("MegaGroupChatEntity insertChatMsg, increaseAsk fail, channelType={}, channelId={}, t=",
                                CommonEnum.ChatChannel.CC_GROUP, this.getChannelId(), increaseException);
                        this.answerChatRequest(context, null, increaseException);
                        return;
                    }
                    if (!increaseResult.isOk()) {
                        final Exception exception = new GeminiException("MegaGroupChatEntity insertChatMsg, increase maxIndex failed, channelType={}, channelId={}, code={}",
                                chatChannel(), this.getChannelId(), increaseResult.getCode());
                        LOGGER.error("MegaGroupChatEntity insertChatMsg, increase fail, channelType={}, channelId={}", chatChannel(), this.getChannelId(), exception);
                        this.answerChatRequest(context, null, exception);
                        return;
                    }
                    long msgId = increaseResult.value.getNextId();
                    CommonMsg.ChatMessage finalChatMsg = chatMessage.toBuilder().setMessageId(msgId).build();
                    // 消息插入db的请求
                    TcaplusDb.ChatTable.Builder insertReq = TcaplusDb.ChatTable.newBuilder();
                    insertReq.setChannelType(chatChannel().getNumber())
                            .setChannelId(this.getChannelId())
                            .setMsgIndex(msgId)
                            .setMsg(finalChatMsg);
                    InsertAsk<TcaplusDb.ChatTable.Builder> insertAsk = new InsertAsk<>(insertReq, InsertOption.newBuilder().withResponseHasRecord(true).build());
                    watch.mark("chat message insert DB end");
                    actor.askGameDb(insertAsk, 5000)
                            .onComplete((insertResult, insertException) -> {
                                if (insertException != null) {
                                    LOGGER.error("insertAsk fail, channelType={}, channelId={}, t=",
                                            CommonEnum.ChatChannel.CC_GROUP, this.getChannelId(), insertException);
                                    this.answerChatRequest(context, null, insertException);
                                    return;
                                }
                                if (!insertResult.isOk()) {
                                    var exception = new GeminiException("insert new message failed, channelType={}, channelId={}, code={}",
                                            chatChannel(), this.getChannelId(), insertResult.getCode());
                                    LOGGER.error("insert fail, channelType={}, channelId={}", chatChannel(), this.getChannelId(), exception);
                                    this.answerChatRequest(context, null, exception);
                                    return;
                                }
                                // 消息存cache，按msgId FIFO淘汰
                                final CommonMsg.ChatMessage message = insertResult.value.getMsg();
                                this.chatMsgCache.putData(message.getMessageId(), message);
                                // 更新内存中的lastMsgId,不在自增maxIndex之后更新的原因是：写消息并不依赖内存中的lastMsgId，读消息确保只读已存盘的消息
                                this.lastMsgId = Math.max(message.getMessageId(), this.lastMsgId);
                                watch.mark("chat message update Cache end");
                                this.broadcastMessage(message);

                                this.answerChatRequest(context, message, null);

                                watch.mark("chat request end");
                                LOGGER.info("MegaGroupChatEntity insertChatMsg, totalCost={}ms, askStat={}", watch.getTotalCost(), watch.stat());
                            });

                });


    }

    private void sendMsgToPlayer(final int zoneId, final long playerId, final GeneratedMessageV3 msg) {
        final IActorRef playerRef = RefFactory.ofPlayer(zoneId, playerId);
        this.actor.ask(playerRef, msg).onComplete(
                (res, t) -> {
                    // 成功则返回
                    if (t == null) {
                        return;
                    }

                    // 非移民异常，直接打印错误
                    if (!(t instanceof MigrateException)) {
                        LOGGER.error("MegaGroupChatEntity sendMsgToPlayer {} to player={} failed, error=",
                                msg.getClass().getSimpleName(), playerRef.getActorId(), t);
                        return;
                    }

                    // 更新玩家zoneId
                    final int newZoneId = ((MigrateException) t).getZoneId();

                    // 非发送消息，不重试
                    if (!(msg instanceof SsPlayerChat.HandleNewMessageAsk)) {
                        LOGGER.error("MegaGroupChatEntity sendMsgToPlayer {} to player {} failed, error=",
                                msg.getClass().getSimpleName(), playerRef.getActorId(), t);
                        return;
                    }

                    // 重试
                    final IActorRef newRef = RefFactory.ofPlayer(newZoneId, Long.parseLong(playerRef.getActorId()));
                    actor.ask(newRef, msg).onComplete((nextRes, nextT) -> {
                        if (nextT == null) {
                            return;
                        }
                        LOGGER.error("MegaGroupChatEntity sendMsgToPlayer {} to player {} after migrate failed, error=",
                                msg.getClass().getSimpleName(), newRef.getActorId(), nextT);
                    });
                }
        );
    }
}

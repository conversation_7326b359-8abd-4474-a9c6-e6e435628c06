package com.yorha.common.cache;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.cache.action.CacheAddAction;
import com.yorha.common.server.ServerContext;
import com.yorha.common.concurrent.singleflight.SingleFlightGroup;
import com.yorha.common.monitor.MonitorUnit;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class AutoLoadSyncCache<K, V> {
    private final BaseSyncCache<K, Optional<V>> cache;
    private final CacheAddAction<K, V> cacheAdder;
    private final SingleFlightGroup<K, Optional<V>> singleFlightGroup = new SingleFlightGroup<>();

    /**
     * 同步异步：同步
     * 缓存类型：任意数据
     * 插入缓存：缓存未命中时使用cacheAddAction尝试插入
     *
     * @param entryLimit     缓存条目上限
     * @param timeLimit      缓存被访问后生存时间ms（==0 永不过期）
     * @param cacheAddAction 缓存自动写入类（缓存未命中时工作）
     */
    AutoLoadSyncCache(int entryLimit, long timeLimit, CacheAddAction<K, V> cacheAddAction, String name) {
        this.cache = new BaseSyncCache<>(entryLimit, timeLimit, name);
        this.cacheAdder = cacheAddAction;
    }

    /**
     * 读取缓存内数据，若无则尝试从使用构造传入的CacheAddiction读取，并置入缓存，若CacheAddiction无法获取数据则返回null
     *
     * @param key   key
     * @param actor actor对象
     * @return 缓存数据 或 null
     */
    @Nullable
    public V getDataOrNull(K key, GameActorWithCall actor) {
        Optional<V> val = cache.getDataOrNull(key);
        // 缓存未命中则通过valGetter获取DB结果
        if (val.isEmpty()) {
            MonitorUnit.CACHE_NOT_HIT_TOTAL.labels(ServerContext.getBusId(), cache.getName()).inc();
            try {
                val = loadAndPutValue(key, actor);
            } catch (Throwable throwable) {
                return null;
            }
        } else {
            MonitorUnit.CACHE_HIT_TOTAL.labels(ServerContext.getBusId(), cache.getName()).inc();
        }
        return val.orElse(null);
    }

    /**
     * 读取缓存内数据，若无则尝试从使用构造传入的CacheAddiction读取，并置入缓存，若CacheAddiction无法获取数据则抛出NullPointerException
     *
     * @param key   key
     * @param actor actor对象
     * @return 缓存数据
     * @throws NullPointerException key不存在
     * @throws Throwable            cacheAddAction内置操作错误
     */
    public V getData(K key, GameActorWithCall actor) throws Throwable {
        Optional<V> val = cache.getDataOrNull(key);
        // 缓存未命中则通过valGetter获取DB结果
        if (val.isEmpty()) {
            MonitorUnit.CACHE_NOT_HIT_TOTAL.labels(ServerContext.getBusId(), cache.getName()).inc();
            val = loadAndPutValue(key, actor);
        } else {
            MonitorUnit.CACHE_HIT_TOTAL.labels(ServerContext.getBusId(), cache.getName()).inc();
        }

        if (val == null || val.isEmpty()) {
            throw new NullPointerException();
        }
        return val.get();
    }

    /**
     * 手动删除缓存
     *
     * @param key key
     */
    public void removeData(K key) {
        this.cache.removeData(key);
    }


    /**
     * 加载KV并写入缓存
     *
     * @param key   key
     * @param actor actor
     * @return Optional<V>
     * @throws Throwable cacheAddAction内置操作错误
     */
    private Optional<V> loadAndPutValue(K key, GameActorWithCall actor) throws Throwable {
        // 使用singleFlightGroup避免缓存击穿
        return this.singleFlightGroup.invoke(key, () -> {
            V value = this.cacheAdder.execute(key, actor);
            cache.putData(key, Optional.ofNullable(value));
            // 加载失败则写null，避免缓存穿透
            return Optional.ofNullable(value);
        });
    }
}

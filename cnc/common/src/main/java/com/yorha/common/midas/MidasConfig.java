package com.yorha.common.midas;

import com.yorha.common.server.config.ClusterConfigUtils;

public class MidasConfig {
    private MidasConfig() {
    }

    public static final String CALLBACK_URL = "/cnc/midas/callbackapi";
    public static String clusterAppId() {
        return ClusterConfigUtils.getWorldConfig().getStringItem("midas_app_id");
    }

    public static String clusterAppKey() {
        return ClusterConfigUtils.getWorldConfig().getStringItem("midas_app_key");
    }

    public static String clusterDomain() {
        return ClusterConfigUtils.getWorldConfig().getStringItem("midas_domain");
    }

    public static String clusterRsaPriKey() {
        return ClusterConfigUtils.getWorldConfig().getStringItem("midas_rsa_private_key");
    }

    public static boolean fakeMidas() {
        return ClusterConfigUtils.getWorldConfig().getBooleanItem("fake_midas");
    }

}

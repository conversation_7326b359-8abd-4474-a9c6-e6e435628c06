package com.yorha.common.enums.reason;

/**
 * 队列加速类型
 */
public enum QueueSpeedReason {
    /**
     * 联盟帮助
     */
    GUILD_HELP,
    /**
     * 使用加速卡
     */
    ACCELERATE_CARD,
    /**
     * 直接使用充值货币加速
     */
    STRAIGHTLY_ACCELERATE,
    /**
     * 使用VIP特权加速
     */
    VIP_ACCELERATE,
    /**
     * 使用加速券（可以直接完成一条队列）
     */
    STRAIGHTLY_CARD,
    /**
     * 免费加速（小于一定时间时可以免费加速）
     */
    FREE_ACCELERATE,
    ;

    /**
     * 是否是免费加速
     * 除了道具和货币都免费
     */
    public static boolean isFreeSpeedUp(QueueSpeedReason type) {
        if (type == ACCELERATE_CARD || type == STRAIGHTLY_ACCELERATE) {
            return false;
        }
        return true;
    }
}

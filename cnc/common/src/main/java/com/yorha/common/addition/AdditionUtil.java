package com.yorha.common.addition;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.AdditionItemProp;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Option;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BuffEffectTemplate;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class AdditionUtil {
    private static final Logger LOGGER = LogManager.getLogger(AdditionUtil.class);

    /**
     * 联盟加成
     */
    public static final Set<CommonEnum.AdditionSourceType> CLAN_ADDITION_SOURCES =
            Sets.immutableEnumSet(
                    CommonEnum.AdditionSourceType.AST_CLAN_ACADEMY,
                    CommonEnum.AdditionSourceType.AST_CLAN_BUILDING
            );

    /**
     * 是否是联盟加成
     */
    public static boolean isClanAddition(int additionId) {
        BuffEffectTemplate temp = ResHolder.getInstance().getValueFromMap(BuffEffectTemplate.class, additionId);
        return temp.getEffectType() == CommonEnum.EffectType.ETT_CLAN;
    }

    public static boolean isSceneAddition(int additionId) {
        CommonEnum.BuffEffectType type = CommonEnum.BuffEffectType.forNumber(additionId);
        if (type == null) {
            LOGGER.error("addition id not exists. additionId:{}", additionId);
            return false;
        }
        // 需要同步
        return type.getValueDescriptor().getOptions().getExtension(Option.addNeedSync);
    }

    public static AdditionSysProp loadSceneClanAdditionFromDb(AdditionSysProp additionSysDb) {
        AdditionSysProp clanAdditionSys = new AdditionSysProp();
        for (Map.Entry<Integer, AdditionProp> entry : additionSysDb.getAddition().entrySet()) {
            if (isClanAddition(entry.getKey()) && isSceneAddition(entry.getKey())) {
                AdditionProp additionProp = new AdditionProp();
                additionProp.mergeFromDb(entry.getValue().getCopyDbBuilder().build());
                clanAdditionSys.putAdditionV(additionProp);
            }
        }
        return clanAdditionSys;
    }

    /**
     * 合并两个AdditionSys
     *
     * @param oriSys  源
     * @param toMerge 需要被合并的
     */
    public static void mergeAdditionSys(AdditionSysProp oriSys, AdditionSysProp toMerge) {
        for (Map.Entry<Integer, AdditionProp> entry : toMerge.getAddition().entrySet()) {
            AdditionProp additionV = oriSys.getAdditionV(entry.getKey());
            if (additionV == null) {
                additionV = new AdditionProp().setAdditionId(entry.getKey());
                oriSys.putAdditionV(additionV);
            }
            long totalMergeValue = 0;
            for (Map.Entry<Integer, AdditionItemProp> itemEntry : entry.getValue().getAdditionItems().entrySet()) {
                AdditionItemProp additionItemV = additionV.getAdditionItemsV(itemEntry.getKey());
                if (additionItemV == null) {
                    additionItemV = new AdditionItemProp().setSourceId(itemEntry.getKey());
                    additionV.putAdditionItemsV(additionItemV);
                }
                // item--加值
                additionItemV.setValue(additionItemV.getValue() + itemEntry.getValue().getValue());
                totalMergeValue += itemEntry.getValue().getValue();
            }
            // total--更新总值
            additionV.setTotalValue(additionV.getTotalValue() + totalMergeValue);
        }
    }

    /**
     * 更新AdditionSys
     * ATTENTION!! 数值是覆盖式的
     *
     * @param oriSys     需要被更新的AdditionSys
     * @param sourceType 来源
     * @param additions  sourceType来源下需要被更新的additionMap
     */
    public static void updateAdditionSys(AdditionSysProp oriSys, CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            AdditionProp additionV = oriSys.getAdditionV(entry.getKey());
            // 值没变就continue
            if (additionV != null) {
                AdditionItemProp additionItemV = additionV.getAdditionItemsV(sourceType.getNumber());
                // item存在，但是value相同，跳出；item不存在，但是新的value==0，跳出
                var sameValue = additionItemV != null && additionItemV.getValue() == entry.getValue();
                var noChange = additionItemV == null && entry.getValue() == 0;
                if (sameValue || noChange) {
                    continue;
                }
            } else if (entry.getValue() == 0) {
                // 原值为0，最新值也是0，跳出
                continue;
            }

            if (additionV == null) {
                additionV = new AdditionProp().setAdditionId(entry.getKey());
                oriSys.putAdditionV(additionV);
            }
            AdditionItemProp additionItemV = additionV.getAdditionItemsV(sourceType.getNumber());
            if (additionItemV == null) {
                additionItemV = new AdditionItemProp().setSourceId(sourceType.getNumber());
                additionV.putAdditionItemsV(additionItemV);
            }
            long changeValue = entry.getValue() - additionItemV.getValue();
            // item--覆盖旧值
            additionItemV.setValue(entry.getValue());
            // total--更新总值
            additionV.setTotalValue(additionV.getTotalValue() + changeValue);
        }
    }

    /**
     * AdditionSys to SourceMap
     */
    public static Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> additionSysToSourceMap(AdditionSysProp additionSys) {
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> additionMapBySource = Maps.newHashMap();
        for (Map.Entry<Integer, AdditionProp> entry : additionSys.getAddition().entrySet()) {
            for (Map.Entry<Integer, AdditionItemProp> entry1 : entry.getValue().getAdditionItems().entrySet()) {
                CommonEnum.AdditionSourceType type = CommonEnum.AdditionSourceType.forNumber(entry1.getKey());
                additionMapBySource.computeIfAbsent(type, v -> Maps.newHashMap()).put(entry.getKey(), entry1.getValue().getValue());
            }
        }
        return additionMapBySource;
    }

    /**
     * 获取两个AdditionSys的差异
     */
    public static Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> getChangedAdditionMapBySource(
            AdditionSysProp oldAdditionSys,
            Struct.AdditionSys newAdditionSys
    ) {
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> sourceMap = Maps.newHashMap();
        for (Map.Entry<Integer, AdditionProp> entry : oldAdditionSys.getAddition().entrySet()) {
            Struct.Addition addition = newAdditionSys.getAddition().getDatasMap().get(entry.getKey());
            if (addition != null) {
                if (addition.getTotalValue() == entry.getValue().getTotalValue()) {
                    continue;
                }
                // 值发生变化的
                for (Map.Entry<Integer, Struct.AdditionItem> itemEntry : addition.getAdditionItems().getDatasMap().entrySet()) {
                    var key = CommonEnum.AdditionSourceType.forNumber(itemEntry.getKey());
                    sourceMap.computeIfAbsent(key, v -> Maps.newHashMap()).put(entry.getKey(), itemEntry.getValue().getValue());
                }
            } else {
                // 移除的
                for (Map.Entry<Integer, AdditionItemProp> itemEntry : entry.getValue().getAdditionItems().entrySet()) {
                    var key = CommonEnum.AdditionSourceType.forNumber(itemEntry.getKey());
                    sourceMap.computeIfAbsent(key, v -> Maps.newHashMap()).put(entry.getKey(), 0L);
                }
            }
        }
        for (Map.Entry<Integer, Struct.Addition> entry : newAdditionSys.getAddition().getDatasMap().entrySet()) {
            AdditionProp addition = oldAdditionSys.getAdditionV(entry.getKey());
            if (addition == null) {
                // 新增的
                for (Map.Entry<Integer, Struct.AdditionItem> itemEntry : entry.getValue().getAdditionItems().getDatasMap().entrySet()) {
                    var key = CommonEnum.AdditionSourceType.forNumber(itemEntry.getKey());
                    sourceMap.computeIfAbsent(key, v -> Maps.newHashMap()).put(entry.getKey(), itemEntry.getValue().getValue());
                }
            }
        }
        return sourceMap;
    }

    public static AdditionSysProp convert2AdditionSysProp(Map<CommonEnum.BuffEffectType, Long> values) {
        AdditionSysProp prop = new AdditionSysProp();
        for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : values.entrySet()) {
            AdditionProp additionProp = new AdditionProp().setAdditionId(entry.getKey().getNumber()).setTotalValue(entry.getValue());
            prop.getAddition().put(entry.getKey().getNumber(), additionProp);
        }
        return prop;
    }

    public static void mergeAddition(Map<CommonEnum.BuffEffectType, Long> map, Map<CommonEnum.BuffEffectType, Long> additions) {
        for (Map.Entry<CommonEnum.BuffEffectType, Long> entry : additions.entrySet()) {
            map.put(entry.getKey(), map.getOrDefault(entry.getKey(), 0L) + entry.getValue());
        }
    }
}

package com.yorha.common.actorservice;

import com.yorha.common.exception.GeminiException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Actor元信息管理
 *
 * <AUTHOR>
 */
public class ActorMetaDataMgr {
    private final Map<String, ActorMetaData> metaDataMap;

    public ActorMetaDataMgr(List<ActorMetaData> metaDataList) {
        this.metaDataMap = new HashMap<>(metaDataList.size());
        for (final ActorMetaData metaData : metaDataList) {
            if (this.metaDataMap.containsKey(metaData.getActorRole())) {
                throw new GeminiException("ActorRole {} MetaData Repeat!", metaData);
            }
            this.metaDataMap.put(metaData.getActorRole(), metaData);
        }
    }

    public ActorMetaData getActorMetaData(String actorRole) {
        return metaDataMap.get(actorRole);
    }
}

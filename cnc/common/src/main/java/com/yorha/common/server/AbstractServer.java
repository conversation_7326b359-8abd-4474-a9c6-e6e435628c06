package com.yorha.common.server;

import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.node.ClusterConfigHandler;
import com.yorha.common.actor.node.WhiteListConfigHandler;
import com.yorha.common.banner.BannerPrinter;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.etcd.EtcdClient;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exports.GeminiExports;
import com.yorha.common.http.HttpServerHandler;
import com.yorha.common.monitor.MemoryService;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.monitors.LoggerService;
import com.yorha.common.utils.FileUtils;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpContentCompressor;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpRequestDecoder;
import io.netty.handler.codec.http.HttpResponseEncoder;
import io.prometheus.client.exporter.HTTPServer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.Marker;
import org.apache.logging.log4j.MarkerManager;
import org.apache.logging.log4j.core.LoggerContext;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

/**
 * 服务器基类
 */
public abstract class AbstractServer {
    private static final Logger LOGGER = LogManager.getLogger(AbstractServer.class);
    // 定义为静态常量
    protected static final Marker MARKER_NO_CONSOLE = MarkerManager.getMarker("NO_CONSOLE");
    /**
     * 进程启动参数
     */
    protected final List<String> processStartUpArgs;
    /**
     * 进程状态
     */
    private volatile ServerProcessStatus serverProcessStatus = ServerProcessStatus.STATUS_NEW;
    /**
     * http
     */
    private NioEventLoopGroup httpIoGroup;
    private NioEventLoopGroup httpWorkerGroup;

    /**
     * 服务器名
     */
    public abstract String getServerName();

    public boolean isRunning() {
        return serverProcessStatus == ServerProcessStatus.STATUS_RUNNING;
    }

    public boolean isTerminated() {
        return this.serverProcessStatus == ServerProcessStatus.STATUS_TERMINATED;
    }

    public boolean isStarting() {
        return this.serverProcessStatus == ServerProcessStatus.STATUS_STARTING;
    }

    public AbstractServer(String[] args) {
        this.processStartUpArgs = Collections.unmodifiableList(Arrays.asList(args));
        ServerContext.setServer(this);
        ServerContext.initServerInfo(this.processStartUpArgs.getFirst());
        //ServerContext.loadServerCfg();
    }

    /**
     * 服务器启动
     */
    public void start() {
        LOGGER.info("gemini_system start");
        if (serverProcessStatus != ServerProcessStatus.STATUS_NEW) {
            LOGGER.error("gemini_system invalid status:{}", serverProcessStatus);
            return;
        }
        final GeminiStopWatch stopWatch = new GeminiStopWatch(getServerName() + "_start");
        serverProcessStatus = ServerProcessStatus.STATUS_STARTING;
        try {
            preStart();
            stopWatch.mark("preStart");
            onStart();
            stopWatch.mark("onStart");
            postStart();
            stopWatch.mark("postStart");
            serverProcessStatus = ServerProcessStatus.STATUS_RUNNING;
            // 增加信号hook
            addShutDownHook();
            final String stat = StringUtils.format("gemini_perf server start {} is running! {}", this, stopWatch.stat());
            LOGGER.info(MARKER_NO_CONSOLE, stat);
            //K8S 控制台能打印
            System.out.println(stat);
            // 获取版本
            NodeRole role = NodeRole.forNumber(ServerContext.getServerInfo().getServerTypeId());
            BannerPrinter.print(ServerContext.getBusId(), role.toString(), this.getClass());
        } catch (Throwable e) {
            // 打印到控制台，为了k8s能读取到内容
            System.out.println("gemini_system server start error" + e.getMessage());
            // wechat log输出
            WechatLog.error("gemini_system server start error.", e);
            // 为了log4j2能在进程停止前全部输出到文件
            WechatLog.getInstance().stop();
            stopAsyncLogger();
            System.exit(1);
        }
    }

    /**
     * 确保异步日志完整输出
     * https://stackoverflow.com/questions/30336669/how-to-flush-asynchronous-loggers-in-log4j2-with-disruptor
     */
    void stopAsyncLogger() {
        // you might need different arguments to getContext() to find the right one
        LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
        ctx.stop();
    }

    protected void preStart() {
        LOGGER.info("gemini_system pre start, args={}", this.processStartUpArgs);
        if (this.processStartUpArgs.size() < 2) {
            throw new IllegalArgumentException("gemini_system start up args wrong");
        }
        // 加载服务器config配置
        loadConfig();
        // 加载服务器版本号
        initLogicTag();
        // 初始化wechatLog的服务器配置
        WechatLog.getInstance().init(ServerContext::getServerInfo);
        // 初始化统计日志(禁用生产)
        // FIXME: 服务不可用 ServerStatisticLog.getInstance().init(ServerContext::getServerInfo);

        // 错误码检测
        ErrorCode.checkValid();
    }

    /**
     * 子类继承实现
     */
    protected abstract void onStart();

    protected void postStart() {
        LOGGER.info("gemini_system post start");
        // Prometheus监控
        int monitorPort = ServerContext.getMonitorPort();
        this.initMonitorServer(monitorPort);
    }

    public void stop() {
        LOGGER.info("gemini_system {} begin stop", this);
        final GeminiStopWatch stopWatch = new GeminiStopWatch(getServerName() + "_stop");
        serverProcessStatus = ServerProcessStatus.STATUS_STOPPING;
        try {
            onStop();
            stopWatch.mark("onStop");
            postStop();
            stopWatch.mark("postStop");
            final String stat = StringUtils.format("gemini_perf stop server {} finish stop! {}", this, stopWatch.stat());
            LOGGER.info(MARKER_NO_CONSOLE, stat);
            // k8s
            System.out.println(stat);
        } catch (Throwable e) {
            LOGGER.error("exception_perf failed to stop server: {}", this, e);
        } finally {
            dumpThreadInfo("normal");
            LogManager.shutdown();
            this.serverProcessStatus = ServerProcessStatus.STATUS_TERMINATED;
        }
    }

    protected void dumpThreadInfo(String reason) {
        final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
        LOGGER.info("gemini_system dumpThreadInfo sum reason {}, daemon thread {}, all {} ",
                reason, threadMXBean.getDaemonThreadCount(), threadMXBean.getThreadCount());
    }

    protected abstract void onStop() throws Exception;

    private void postStop() {
        SystemScheduleMgr.getInstance().shutdown();
        WechatLog.getInstance().stop();
    }

    protected void initHttpServer(int httpWorkerNum) {
        LOGGER.info("gemini_system init HTTP server to start");
        ServerBootstrap bootstrap = new ServerBootstrap();
        this.httpIoGroup = new NioEventLoopGroup(1);
        this.httpWorkerGroup = new NioEventLoopGroup(httpWorkerNum);
        bootstrap.group(this.httpIoGroup, this.httpWorkerGroup);
        bootstrap.channel(NioServerSocketChannel.class);
        bootstrap.option(ChannelOption.SO_REUSEADDR, true);
        bootstrap.childOption(ChannelOption.TCP_NODELAY, true);
        bootstrap.childOption(ChannelOption.SO_KEEPALIVE, false);
        bootstrap.childHandler(new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) {
                ChannelPipeline pipeline = ch.pipeline();
                pipeline.addLast("decoder", new HttpRequestDecoder(16 * 1024 * 1024, 8192, 8192));
                //他负责把一个http请求消息的多个部分合并成一条完整的HTTP消息
                pipeline.addLast("aggregator", new HttpObjectAggregator(65536));
                pipeline.addLast("encoder", new HttpResponseEncoder());
                pipeline.addLast("deflate", new HttpContentCompressor());
                pipeline.addLast("handler", HttpServerHandler.getInstance());
            }
        });
        int webPort = ServerContext.getWebPort();
        try {
            LOGGER.info("gemini_port gemini_system http server start on {}", webPort);
            bootstrap.bind(new InetSocketAddress(webPort)).sync();
            LOGGER.info("gemini_system init HTTP server to end");
        } catch (Exception e) {
            throw new GeminiException(StringUtils.format("init HTTP server to fail {}", webPort), e);
        }
    }

    protected void stopHttpServer() {
        if (this.httpIoGroup != null) {
            this.httpWorkerGroup.shutdownGracefully();
            this.httpIoGroup.shutdownGracefully();
        }
    }

    /**
     * SIGINT	2	中断（同 Ctrl + C）会触发触发shutdownhook
     * SIGKILL	9	终止进程，强制杀死进程
     * SIGTERM	15	终止进程，会先释放自己的资源，触发shutdownhook然后终止进程
     * 此处会hook 2 15的信号
     * 若此函数死循环导致进程无法退出，需要kill -9 pid
     */
    protected void addShutDownHook() {
        Runtime.getRuntime().addShutdownHook(ConcurrentHelper.newThread("ShutdownHook", false, () -> {
            LOGGER.info("gemini_system ShutDownHook Trigger! Basic!");
            if (!this.isRunning()) {
                return;
            }
            this.stop();
        }));
    }

    private void initLogicTag() {
        if (ServerContext.isDevEnv()) {
            // dev环境不加载
            return;
        }
        // 加载版本号
        try {
            LOGGER.info("gemini_system initLogicTag start");
            String logicTag = FileUtils.readConfigFile("../game_data/server/LOGIC_TAG");
            ServerContext.setLogicTag(logicTag);
            LOGGER.info("gemini_system initLogicTag over, {}", logicTag);
        } catch (Exception e) {
            LOGGER.error("gemini_system initLogicTag fail", e);
        }
    }

    private void initMonitorServer(int monitorPort) {
        if (monitorPort <= 0) {
            LOGGER.warn("init monitor server skip! port {} <= 0", monitorPort);
            return;
        }
        try {
            LOGGER.info("gemini_port gemini_system init monitor server start! port on {}", monitorPort);
            GeminiExports.initialize(ServerContext.getBusId());
            final InetSocketAddress socket = new InetSocketAddress(monitorPort);
            new HTTPServer(socket, GeminiExports.getCollectorRegistry(), true);


            String oldMemSizeProp = System.getProperty("OLD_MEM_SIZE");
            long oldMemSize = 0;
            if (!StringUtils.isEmpty(oldMemSizeProp)) {
                // 传入的单位是MiB，改成byte[iec]
                oldMemSize = Long.parseLong(oldMemSizeProp) * 1024 * 1024;
            }

            String metaspaceMaxSizeProp = System.getProperty("METASPACE_MAX_SIZE");
            long metaspaceMaxSize = 0;
            if (!StringUtils.isEmpty(metaspaceMaxSizeProp)) {
                // 传入的单位是MiB，改成byte[iec]
                metaspaceMaxSize = Long.parseLong(metaspaceMaxSizeProp) * 1024 * 1024;
            }

            String fiberMaxNumProp = System.getProperty("FIBER_MAX_NUM");
            long fiberMaxNum = 0;
            if (!StringUtils.isEmpty(fiberMaxNumProp)) {
                fiberMaxNum = Long.parseLong(fiberMaxNumProp);
            }
            LOGGER.info("gemini_system initMonitorServer {} {} {}", oldMemSize, metaspaceMaxSize, fiberMaxNum);

            MonitorUnit.OLD_MEM_SIZE.labels(ServerContext.getBusId()).set(oldMemSize);
            MonitorUnit.METASPACE_MAX_SIZE.labels(ServerContext.getBusId()).set(metaspaceMaxSize);
            MonitorUnit.FIBER_MAX_NUM.labels(ServerContext.getBusId()).set(fiberMaxNum);
            LOGGER.info("gemini_system init monitor server end! port on {}", monitorPort);
            //TODO 临时监控
            MemoryService.getInstance().init();
            LoggerService.getInstance().init();

        } catch (Exception e) {
            throw new GeminiException("init monitor server failed", e);
        }
    }

    @Override
    public String toString() {
        return "AbstractServer{" +
                "name=" + getServerName() +
                ", processStartArgs=" + processStartUpArgs +
                ", serverStatus=" + serverProcessStatus +
                ", openStatus=" + ServerContext.getServerSetting().getOpenStatus() +
                '}';
    }

    /**
     * 载入配置，可重入
     */
    protected void loadConfig() {
        // etcd初始化
        if (ServerContext.getEtcdClient() == null) {
            List<String> addresses = Stream.of(this.processStartUpArgs.get(1)).toList();
            if (addresses.isEmpty()) {
                throw new GeminiException("gemini_system etcd config is null");
            }
            final String[] etcdAddressArray = addresses.toArray(new String[0]);
            // 连接etcd
            final EtcdClient etcdClient = new EtcdClient(etcdAddressArray, null, null);
            ServerContext.setEtcdClient(etcdClient);
        }
        // 获取集群配置
        if (!ServerContext.getEtcdClient().watch(ActorClusterUrlUtils.etcdClusterConfigUrl(), new ClusterConfigHandler())) {
            throw new GeminiException("watch {} fail!", ActorClusterUrlUtils.etcdClusterConfigUrl());
        }
        // 加载临时/动态白名单
        if (!ServerContext.getEtcdClient().watch(ActorClusterUrlUtils.etcdWhiteListUrl(), new WhiteListConfigHandler())) {
            throw new GeminiException("watch {} fail!", ActorClusterUrlUtils.etcdWhiteListUrl());
        }
        LOGGER.info("gemini_system load config from etcd!");
    }
}
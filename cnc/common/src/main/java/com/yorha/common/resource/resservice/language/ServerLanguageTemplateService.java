package com.yorha.common.resource.resservice.language;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.ConstTemplate;
import res.template.ServerLanguageTemplate;

/**
 * 服务器语言配置Service
 *
 * <AUTHOR>
 */
public class ServerLanguageTemplateService extends AbstractResService {

    public ServerLanguageTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
    }


    @Override
    public void checkValid() throws ResourceException {
        int playerNamePrefix = getResHolder().getConstTemplate(ConstTemplate.class).getPlayerNamePrefix();
        if (!getResHolder().getMap(ServerLanguageTemplate.class).containsKey(playerNamePrefix)) {
            throw new ResourceException("ServerLanguageTemplate not has playerNamePrefix: " + playerNamePrefix);
        }
        for (ServerLanguageTemplate template : getResHolder().getListFromMap(ServerLanguageTemplate.class)) {

            for (CommonEnum.Language value : CommonEnum.Language.values()) {
                try {
                    if (value == CommonEnum.Language.L_NONE) {
                        continue;
                    }
                    getServerLanguage(value, template.getId());
                } catch (GeminiException e) {
                    throw new ResourceException(e.getMessage());
                }
            }
        }

    }

    public String getServerLanguage(CommonEnum.Language language, int id) {
        ServerLanguageTemplate valueFromMap = getResHolder().getValueFromMap(ServerLanguageTemplate.class, id);
        String str = null;
        switch (language) {
            case zh:
                str = valueFromMap.getZh();
                break;
            case en:
                str = valueFromMap.getEn();
                break;
            case zh_tw:
                str = valueFromMap.getZhTw();
                break;
            case de:
                str = valueFromMap.getDe();
                break;
            case ru:
                str = valueFromMap.getRu();
                break;
            case it:
                str = valueFromMap.getIt();
                break;
            case es:
                str = valueFromMap.getEs();
                break;
            case pt:
                str = valueFromMap.getPt();
                break;
            case tr:
                str = valueFromMap.getTr();
                break;
            case ide:
                str = valueFromMap.getIde();
                break;
            case th:
                str = valueFromMap.getTh();
                break;
            case vi:
                str = valueFromMap.getVi();
                break;
            case ja:
                str = valueFromMap.getJa();
                break;
            case ko:
                str = valueFromMap.getKo();
                break;
            case ar:
                str = valueFromMap.getAr();
                break;
            case fr:
                str = valueFromMap.getFr();
                break;
            case pl:
                str = valueFromMap.getPl();
                break;
            case nl:
                str = valueFromMap.getNl();
                break;
            case se:
                str = valueFromMap.getSe();
                break;
            case hu:
                str = valueFromMap.getHu();
                break;
            case ms:
                str = valueFromMap.getMs();
                break;
            case as_in:
                str = valueFromMap.getAsIn();
                break;
            case other:
                // 其他语言默认使用英文
                str = valueFromMap.getEn();
                break;
            default:
                WechatLog.error("策划语言表配置未找到对应语言. language:{}, template:{}", language.name(), valueFromMap);
                str = valueFromMap.getEn();
                break;
        }
        if (StringUtils.isEmpty(str)) {
            return valueFromMap.getEn();
        }
        return str;
    }
}
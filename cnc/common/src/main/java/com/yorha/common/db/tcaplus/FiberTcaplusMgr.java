package com.yorha.common.db.tcaplus;

import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;

import java.util.concurrent.TimeUnit;

/**
 * 协程版本的封装
 *
 * <AUTHOR>
 */
public class FiberTcaplusMgr {
    private final Client client;

    public FiberTcaplusMgr(final Client client) {
        this.client = client;
    }

    public Response send(Request request) {
        return this.send(request, TimeUnit.SECONDS.toMillis(5));
    }

    public Response send(Request request, long timeoutDurationMs) {
        return TcaplusUtils.requestResponse(this.client, request, timeoutDurationMs);
    }

    public Client getClient() {
        return client;
    }
}

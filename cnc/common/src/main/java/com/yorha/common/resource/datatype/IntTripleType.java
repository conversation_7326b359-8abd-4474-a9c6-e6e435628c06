package com.yorha.common.resource.datatype;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class IntTripleType {
    private static final String UNDERLINE = "_";
    private static final String COMMA = ",";
    private static final int SIZE = 3;
    private final int key;
    private final int value1;
    private final int value2;


    private IntTripleType(int key, int value1, int value2) {
        this.key = key;
        this.value1 = value1;
        this.value2 = value2;
    }

    public int getKey() {
        return key;
    }

    public int getValue1() {
        return value1;
    }

    public int getValue2() {
        return value2;
    }

    public static IntTripleType makeTriple(int key, int value1, int value2) {
        return new IntTripleType(key, value1, value2);
    }

    public static IntTripleType valueOf(String s) {
        int[] tripleInt = Arrays.stream(s.split(UNDERLINE)).mapToInt(num -> Integer.parseInt(num)).toArray();
        if (tripleInt.length == SIZE) {
            return IntTripleType.makeTriple(tripleInt[0], tripleInt[1], tripleInt[2]);
        }
        return IntTripleType.makeTriple(0, 0, 0);
    }

    public static IntTripleType parseFromString(String s) {
        int[] tripleInt = Arrays.stream(s.split(UNDERLINE)).mapToInt(num -> Integer.parseInt(num)).toArray();
        if (tripleInt.length == SIZE) {
            return IntTripleType.makeTriple(tripleInt[0], tripleInt[1], tripleInt[2]);
        }
        return IntTripleType.makeTriple(0, 0, 0);
    }

    public static List<IntTripleType> parseListFromString(String s) {
        List<IntTripleType> retList = new ArrayList<>();
        Arrays.stream(s.split(COMMA)).map(iStr -> retList.add(IntTripleType.parseFromString(iStr))).collect(Collectors.toList());
        return retList;
    }
}

package com.yorha.common.concurrent.dispatcher;

/**
 * <AUTHOR>
 */
public final class GeminiDispatcherFactory {

    /**
     * 静态工厂类
     */
    private GeminiDispatcherFactory() {
    }

    /**
     * 任务派发器
     * 线程版
     *
     * @param name           派发器名
     * @param threadNum      线程数
     * @param threadTaskSize 线程队列大小
     * @return IGeminiDispatcher
     */
    public static GeminiDispatcher buildDispatcher(String name, int threadNum, int threadTaskSize) {
        return new GeminiDispatcher(name, threadNum, threadTaskSize);
    }

    /**
     * 任务派发器
     * 协程版
     *
     * @param name          派发器名
     * @param threadNum     线程数
     * @param fiberTaskSize 协程队列大小
     * @return IGeminiDispatcher
     */
    public static GeminiFiberDispatcher buildFiberDispatcher(String name, int threadNum, int fiberTaskSize) {
        return new GeminiFiberDispatcher(name, threadNum, fiberTaskSize);
    }
}

package com.yorha.common.utils.shape;

import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.common.utils.vector.Vector2f;
import org.apache.commons.lang3.RandomUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 一段圆环。。
 *
 * <AUTHOR>
 */
public class SnippetRing implements Shape {
    /**
     * 外扇形
     */
    private final Sector outerSector;
    /**
     * 内扇形
     */
    private final Sector innerSector;

    private AABB aabb;

    public SnippetRing(Point one, Vector2f yaw, int angle, int minR, int maxR) {
        this.outerSector = Sector.valueOf(one, yaw, maxR, angle);
        this.innerSector = Sector.valueOf(one, yaw, minR, angle);
    }

    @Override
    public boolean containsPoint(long x, long y) {
        boolean ret = outerSector.angleContainPoint(x, y);
        if (!ret) {
            return false;
        }
        Point center = outerSector.getCenter();
        long xDis = x - center.getX();
        long yDis = y - center.getY();
        double distance = Math.sqrt(xDis * xDis + yDis * yDis);
        return innerSector.getR() <= distance && distance <= outerSector.getR();
    }

    @Override
    public AABB getAABB() {
        if (aabb != null) {
            return aabb;
        }
        List<Integer> listX = new ArrayList<>();
        List<Integer> listY = new ArrayList<>();
        Point center = this.outerSector.getCenter();
        listX.add(center.getX());
        listY.add(center.getY());
        Point pointA = this.outerSector.getPointA();
        listX.add(pointA.getX());
        listY.add(pointA.getY());
        Point pointB = this.outerSector.getPointB();
        listX.add(pointB.getX());
        listY.add(pointB.getY());
        pointA = this.innerSector.getPointA();
        listX.add(pointA.getX());
        listY.add(pointA.getY());
        pointB = this.innerSector.getPointB();
        listX.add(pointB.getX());
        listY.add(pointB.getY());

        double startAngle = innerSector.getStartAngle();
        double endAngle = innerSector.getEndAngle();
        if (ShapeUtils.includedAngle(0, startAngle, endAngle)) {
            listX.add(center.getX() + outerSector.getR());
        }
        if (ShapeUtils.includedAngle(90, startAngle, endAngle)) {
            listY.add(center.getY() + outerSector.getR());
        }
        if (ShapeUtils.includedAngle(180, startAngle, endAngle)) {
            listX.add(center.getX() - outerSector.getR());
        }
        if (ShapeUtils.includedAngle(270, startAngle, endAngle)) {
            listY.add(center.getY() - outerSector.getR());
        }
        int left = MathUtils.min(listX);
        int right = MathUtils.max(listX);
        int top = MathUtils.min(listY);
        int bottom = MathUtils.max(listY);
        aabb = new AABB(left, top, right, bottom);
        return aabb;
    }

    @Override
    public Point getRandomPoint() {
        double r = RandomUtils.nextDouble(innerSector.getR(), outerSector.getR());
        int angle = innerSector.getRandomAngle();
        double radians = Math.toRadians(angle);
        int x = (int) (Math.sin(radians) * r);
        int y = (int) (Math.cos(radians) * r);
        return Point.valueOf(innerSector.getCenter().getX() + x, innerSector.getCenter().getY() + y);
    }
}
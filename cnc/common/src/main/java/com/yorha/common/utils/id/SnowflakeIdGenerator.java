package com.yorha.common.utils.id;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 修改版雪花算法ID生成器
 * <p>非线程安全！！！适合在Actor内部使用</p>
 * <p>
 * ID结构（64位）：
 * +--------------------------------------------------------------------------+
 * | 1位符号位 | 40位时间戳  | 13位Worker ID  | 10位序列号 |
 * | (固定为0) | (毫秒级)   | (8192个Worker) | (1024个序列) |
 * +--------------------------------------------------------------------------+
 * <p>
 * 特点：
 * - 支持8192个Worker
 * - 每毫秒可生成1024个ID
 * - 可使用约34.8年（2^40毫秒）
 * - 全局唯一、趋势递增、高性能
 * <p>
 * 使用场景：
 * - 大规模分布式系统，需要支持更多机器实例
 * - 对单机并发要求不是特别高的场景
 * - 需要长期稳定运行的系统
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public class SnowflakeIdGenerator {

    private static final Logger LOGGER = LogManager.getLogger(SnowflakeIdGenerator.class);

    // ==================== 位数配置 ====================

    /**
     * 序列号占用位数
     */
    private static final long SEQUENCE_BITS = 10L;

    /**
     * workerId占用位数
     */
    private static final long WORKER_ID_BITS = 13L;

    /**
     * 时间戳占用位数
     */
    private static final long TIMESTAMP_BITS = 40L;

    // ==================== 最大值配置 ====================

    /**
     * 最大workerId：8191 (2^13 - 1)
     */
    public static final long MAX_WORKER_ID = (1L << WORKER_ID_BITS) - 1;

    /**
     * 最大序列号：1023 (2^10 - 1)
     */
    private static final long MAX_SEQUENCE = (1L << SEQUENCE_BITS) - 1;

    /**
     * 最大时间戳：1099511627775 (2^40 - 1)
     */
    public static final long MAX_TIMESTAMP = (1L << TIMESTAMP_BITS) - 1;

    // ==================== 位移配置 ====================

    /**
     * workerId左移位数：10位
     */
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;

    /**
     * 时间戳左移位数：23位 (10 + 13)
     */
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;

    // ==================== 时间配置 ====================

    /**
     * 起始时间戳：2025-06-30 00:00:00 UTC
     */
    public static final long EPOCH = 1751212800000L;

    /**
     * 一年的毫秒数
     */
    public static final long ONE_YEAR_MS = 365L * 24 * 60 * 60 * 1000;

    // ==================== 实例变量 ====================

    private final long workerId;

    /**
     * 当前序列号
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间戳
     */
    private long lastTimestamp;

    // ==================== 构造方法 ====================

    /**
     * 构造函数
     *
     * @param workerId 范围：[0 , 8191)
     * @throws IllegalArgumentException 当workId超出范围时抛出
     */
    public SnowflakeIdGenerator(long workerId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("WorkerId must be between 0 and %d, but got: %d", MAX_WORKER_ID, workerId)
            );
        }
        this.workerId = workerId;
        long now = getCurrentTimestamp();
        this.lastTimestamp = now;

        //增加对最大时间戳检测，保证创建的时候距离最大时间戳至少有一年时间
        long maxTimestamp = EPOCH + MAX_TIMESTAMP;
        if (maxTimestamp - now < ONE_YEAR_MS) {
            throw new IllegalArgumentException(
                    String.format("SnowflakeIdGenerator initialized too late, maxTimestamp: %d, now: %d", maxTimestamp, now)
            );
        }

        LOGGER.info("SnowflakeIdGenerator initialized with machineId: {}, maxMachineId: {}, maxSequence: {}",
                workerId, MAX_WORKER_ID, MAX_SEQUENCE);
    }


    // ==================== 核心方法 ====================

    /**
     * <p>非线程安全！！！适合在Actor内部使用</p>
     * 生成下一个唯一ID
     *
     * @return 64位唯一ID
     * @throws RuntimeException 当时钟回拨或时间戳溢出时抛出
     */
    public long nextId() {
        long timestamp = getCurrentTimestamp();

        // 检查时钟回拨
        if (timestamp < lastTimestamp) {
            long backwardsMs = lastTimestamp - timestamp;

            throw new RuntimeException(
                    String.format("Clock moved backwards by %d ms, refusing to generate ID", backwardsMs)
            );
        }

        // 处理同一毫秒内的序列号
        if (timestamp == lastTimestamp) {
            // 序列号递增
            sequence = (sequence + 1) & MAX_SEQUENCE;

            // 序列号用完，等待下一毫秒
            if (sequence == 0) {
                LOGGER.debug("Sequence exhausted, waiting for next millisecond.");
                timestamp = waitNextMillis(lastTimestamp);
            }
        } else {
            // 新的毫秒，重置序列号
            sequence = 0L;
        }

        // 更新最后时间戳
        lastTimestamp = timestamp;

        // 计算相对时间戳
        long relativeTimestamp = timestamp - EPOCH;

        // 检查时间戳是否溢出
        if (relativeTimestamp > MAX_TIMESTAMP) {
            long overflowYears = (relativeTimestamp * ONE_YEAR_MS) / (1L << TIMESTAMP_BITS);
            throw new RuntimeException(
                    String.format("Timestamp overflow! RelativeTimestamp: %d, MaxTimestamp: %d, OverflowYears: %d",
                            relativeTimestamp, MAX_TIMESTAMP, overflowYears)
            );
        }

        // 组装64位ID
        return assembleId(relativeTimestamp, workerId, sequence);
    }

    /**
     * 组装ID的各个部分
     *
     * @param timestamp 相对时间戳
     * @param workerId  机器ID
     * @param sequence  序列号
     * @return 组装后的64位ID
     */
    private long assembleId(long timestamp, long workerId, long sequence) {
        return (timestamp << TIMESTAMP_SHIFT) |
                (workerId << WORKER_ID_SHIFT) |
                sequence;
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取当前时间戳（毫秒）
     *
     * @return 当前时间戳
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 等待下一毫秒
     *
     * @param lastTimestamp 上次时间戳
     * @return 新的时间戳
     */
    private long waitNextMillis(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }

}
package com.yorha.common.resource.resservice.constant;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.Camp;
import res.template.ConstTemplate;
import res.template.MilestoneChapterTemplate;
import res.template.TaskMainTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ConstKVResService extends AbstractResService {

    public ConstKVResService(ResHolder resHolder) {
        super(resHolder);
    }

    /**
     * TBT1里约副本开启时间（临时）
     */
    private final List<Pair<Integer, Integer>> rioOpenTime = new ArrayList<>();

    /**
     * 英雄升星用的通用碎片
     */
    private final Map<Integer, Integer> heroStarClipStore = new HashMap<>();

    public ConstTemplate getTemplate() {
        return getResHolder().getConstTemplate(ConstTemplate.class);
    }

    @Override
    public void load() throws ResourceException {

        heroStarClipStore.clear();
        List<IntPairType> list = this.getTemplate().getOmniHeroShardItem();
        for (IntPairType e : list) {
            heroStarClipStore.put(e.getKey(), e.getValue());
        }


        List<Integer> initialHero = getResHolder().getConstTemplate(ConstTemplate.class).getRioCrisisTime();
        for (int i = 0; i < (initialHero.size() / 3); i++) {
            rioOpenTime.add(new Pair<>(initialHero.get(i * 3 + 1), initialHero.get(i * 3 + 2)));
        }
    }

    public int getHeroStarClipItemId(int quality) {
        return heroStarClipStore.get(quality);
    }

    public boolean rioIsOpenTime() {
        long now = SystemClock.now();
        for (Pair<Integer, Integer> integerIntegerPair : rioOpenTime) {
            long start = TimeUtils.second2Ms(integerIntegerPair.getFirst());
            long end = TimeUtils.second2Ms(integerIntegerPair.getSecond());
            if (start < now && end >= now) {
                return true;
            }
        }
        return false;
    }

    public Camp getBossFieldArmyCamp() {
        return Camp.forNumber(getTemplate().getBossFieldArmyCamp());
    }

    @Override
    public void checkValid() throws ResourceException {
        int marchingLineLinit = getTemplate().getMarchingLineLimit();
        if (marchingLineLinit > 200) {
            throw new ResourceException(StringUtils.format("常量表:marchingLineLinit cant more than 200. {}", marchingLineLinit));
        }
        // 灭火城墙资源类型有效性检测
        IntPairType cost = getTemplate().getCityWallOutfireCost();
        CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(cost.getKey());
        if (currencyType == null) {
            throw new ResourceException(StringUtils.format("常量表:CityWallOutfireCost enum not exist {}", cost.getKey()));
        }
        for (Integer integer : getTemplate().getTechUnlockRareEarthTiberiumDiamond()) {
            if (CommonEnum.CurrencyType.forNumber(integer) == null) {
                throw new ResourceException(StringUtils.format("常量表:getTechUnlockRareEarthTiberiumDiamond 配置不存在的类型 {}", cost.getKey()));
            }
        }

        int mileStoneStarId = getTemplate().getMileStoneStarId();
        MilestoneChapterTemplate mileStoneTemplate = getResHolder().findValueFromMap(MilestoneChapterTemplate.class, mileStoneStarId);
        if (mileStoneTemplate == null) {
            throw new ResourceException(StringUtils.format("里程碑章节表初始章节配置不存在{}", mileStoneStarId));
        }

        IntPairType unlockHourTime = getTemplate().getMileStoneFuncUnlockTime();
        if (unlockHourTime.getValue() <= unlockHourTime.getKey()) {
            throw new ResourceException(StringUtils.format("常量表中里程碑功能开启时间有问题.后者不能小于前者{}", unlockHourTime));
        }
        TaskMainTemplate valueFromMap = getResHolder().findValueFromMap(TaskMainTemplate.class, getTemplate().getFirstMainTaskId());
        if (valueFromMap == null) {
            throw new ResourceException(StringUtils.format("常量表中主线任务配置有问题.id不存在:{}", getTemplate().getFirstMainTaskId()));
        }

        List<Integer> cityBuildProsperitySection = getTemplate().getCityBuildProsperitySection();
        if (cityBuildProsperitySection.size() != CommonEnum.ProsperityStage.values().length) {
            throw new ResourceException(StringUtils.format("常量表繁荣度阶段配置问题 const:{}", cityBuildProsperitySection));
        }
    }
}

package com.yorha.common.resource.resservice.campaign;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.gemini.utils.StringUtils;
import res.template.*;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

public class CampaignDifficultyLevelResService extends AbstractResService {

    public CampaignDifficultyLevelResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
    }

    @Override
    public void checkValid() throws ResourceException {
        for (CampaignDifficultyLevelTemplate template : getResHolder().getListFromMap(CampaignDifficultyLevelTemplate.class)) {
            if (getResHolder().getListFromMap(CampaignMissionLevelTemplate.class).stream().noneMatch(t -> t.getCampaignDifficultyId() == template.getId())) {
                throw new GeminiException(StringUtils.format("战役难度配置错误（campaign_diffculty_level）. id:{} 的配置在 campaign_mission_level 不存在", template.getId()));
            }
            if (getResHolder().getListFromMap(CampaignMissionRewardTemplate.class).stream().noneMatch(t -> t.getDifficultylevel() == template.getId())) {
                throw new GeminiException(StringUtils.format("战役难度配置错误（campaign_diffculty_level）. id:{} 的配置在 campaign_mission_reward 不存在", template.getId()));
            }
            for (int mapId : template.getMapIDList()) {
                if (getResHolder().findValueFromMap(CampaignMapTemplate.class, mapId) == null) {
                    throw new GeminiException(StringUtils.format("战役难度配置错误（campaign_diffculty_level）. id:{} 配置的 mapID:{} 在 campaign_map 不存在", template.getId(), mapId));
                }
            }
            for (int mapType : template.getMapTypeWeightingPairList().stream().map(IntPairType::getKey).toList()) {
                if (getResHolder().getListFromMap(MapConfigRhTemplate.class).stream().noneMatch(
                        t -> t.getMapType() == mapType && template.getMapLevelList().contains(t.getMapLevel()))
                ) {
                    throw new GeminiException(StringUtils.format("战役难度配置错误（campaign_diffculty_level）. id:{} 配置的 mapTypeWeighting:{}, mapLevel:{} 在 map_config_rh 不存在",
                            template.getId(), template.getMapTypeWeightingPairList(), template.getMapLevelList()));
                }
            }
            for (int mapType : template.getMapTypeGuaranteeList()) {
                if (getResHolder().getListFromMap(MapConfigRhTemplate.class).stream().noneMatch(
                        t -> t.getMapType() == mapType && template.getMapLevelList().contains(t.getMapLevel()))
                ) {
                    throw new GeminiException(StringUtils.format("战役难度配置错误（campaign_diffculty_level）. id:{} 配置的 mapTypeGuarantee:{}, mapLevel:{} 在 map_config_rh 不存在",
                            template.getId(), template.getMapTypeGuaranteeList(), template.getMapLevelList()));
                }
            }
        }
    }

    public CampaignDifficultyLevelTemplate getMaxLevelByConstruction(int constructionValue) {
        int finalConstructionValue = Math.max(constructionValue, 0);
        List<CampaignDifficultyLevelTemplate> unlockTemplates = ResHolder.getInstance().getListFromMap(CampaignDifficultyLevelTemplate.class).stream().filter(
                t -> t.getConstructionValuePerLevel() <= finalConstructionValue).toList();
        Optional<CampaignDifficultyLevelTemplate> optionalTemplate = unlockTemplates.stream().max(Comparator.comparingInt(CampaignDifficultyLevelTemplate::getId));
        if (optionalTemplate.isEmpty()) {
            throw new GeminiException(StringUtils.format("战役难度配置错误（campaign_diffculty_level）. 满足 constructionValuePerLevel >= {} 的配置不存在", constructionValue));
        }
        return optionalTemplate.get();
    }
}
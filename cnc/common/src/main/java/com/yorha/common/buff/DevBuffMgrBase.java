package com.yorha.common.buff;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.game.gen.prop.DevBuffProp;
import com.yorha.game.gen.prop.DevBuffSysProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import org.apache.commons.collections4.IterableUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BuffTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * DevBuff管理类
 *
 * <AUTHOR>
 */
public abstract class DevBuffMgrBase<T extends AbstractEntity> {
    public static final Logger LOGGER = LogManager.getLogger(DevBuffMgrBase.class);
    /**
     * 过期优先级队列
     */
    protected final PriorityQueue<DevBuffProp> buffPriorityQueue = new PriorityQueue<>(Comparator.comparingLong(DevBuffProp::getEndTime));
    /**
     * buff拥有的加成
     */
    private final Map<Integer, Map<CommonEnum.AdditionSourceType, Long>> additionMap = Maps.newHashMap();
    /**
     * 下一个等待过期的任务
     */
    protected int pendingExpireBuffId = 0;

    private final T owner;
    /**
     * init的时候过期要删除的buffList
     */
    private final List<Integer> buffToRemove = Lists.newArrayList();

    public enum LOGLEVEL {
        DEBUG,
        INFO
    }

    protected LOGLEVEL logLevel() {
        return LOGLEVEL.DEBUG;
    }

    private void log(String messagePattern, Object... arguments) {
        if (logLevel() == LOGLEVEL.DEBUG) {
            LOGGER.debug(StringUtils.format(messagePattern, arguments));
        } else {
            LOGGER.info(StringUtils.format(messagePattern, arguments));
        }
    }

    public T getOwner() {
        return owner;
    }

    protected DevBuffMgrBase(T owner) {
        this.owner = owner;
        init();
    }

    protected void init() {
        for (Map.Entry<Integer, DevBuffProp> entry : getDevBuffSys().getDevBuff().entrySet()) {
            BuffTemplate buffTemplate = getBuffTemplate(entry.getKey());
            if (buffTemplate == null) {
                LOGGER.error("owner:{}, load buff failed.template not find.buffId:{}", owner, entry.getKey());
                continue;
            }
            final DevBuffProp buffProp = entry.getValue();
            // 过期了，在postInit里面删掉
            if (isExpired(buffProp)) {
                buffToRemove.add(entry.getKey());
                continue;
            }
            // 加入定时队列
            if (buffProp.getEndTime() > 0) {
                buffPriorityQueue.add(buffProp);
            }
        }
        refreshAdditionCache();
        log("refreshAdditionCache {} additionMap:{}", getOwner(), additionMap);
    }

    public void postInit() {
        for (Integer buffId : buffToRemove) {
            removeDevBuff(buffId, null, true);
        }
        buffToRemove.clear();
        scheduleBuffExpired();
    }

    public void refreshAdditionCache() {
        // 在这里重新组建一下加成map，为了玩家重登的时候能够拿到配置表里最新的加成map
        additionMap.clear();
        for (Map.Entry<Integer, DevBuffProp> entry : getDevBuffSys().getDevBuff().entrySet()) {
            BuffTemplate buffTemplate = getBuffTemplate(entry.getKey());
            if (buffTemplate == null) {
                LOGGER.error("owner:{}, load buff failed.template not find.buffId:{}", owner, entry.getKey());
                continue;
            }
            final DevBuffProp buffProp = entry.getValue();
            if (isExpired(buffProp)) {
                continue;
            }
            final int effectLayer = DevBuffUtil.effectLayer(buffProp, buffTemplate);
            CommonEnum.AdditionSourceType additionSourceType = DevBuffUtil.getAdditionSourceType(buffProp.getSourceType());
            Map<CommonEnum.AdditionSourceType, Long> sourceMap = additionMap.computeIfAbsent(buffTemplate.getType(), v -> Maps.newHashMap());
            sourceMap.put(additionSourceType, sourceMap.getOrDefault(additionSourceType, 0L) + (long) buffTemplate.getValue() * effectLayer);
        }
    }

    private boolean isExpired(DevBuffProp devBuff) {
        return devBuff.getEndTime() > 0 && devBuff.getEndTime() <= SystemClock.now();
    }

    /**
     * 获取buffSysProp
     */
    public abstract DevBuffSysProp getDevBuffSys();

    /**
     * 添加buff后操作
     */
    protected abstract void afterAddDevBuff(DevBuffProp devBuff, int addLayer);

    /**
     * 移除buff后操作
     */
    protected abstract void afterRemoveDevBuff(DevBuffProp devBuffToRemove, boolean isExpired, int decLayer);

    public abstract CommonEnum.DevBuffType getBuffType();

    /**
     * 添加buff
     *
     * @param startTime    buff开始时间，为null则为当前时间
     * @param forceEndTime buff结束时间，为null则为读表时间，否则为自定义的结束时间
     * @param layer        需要叠加的层数，为null则为1层
     */
    public DevBuffProp addDevBuff(int buffId,
                                  Long startTime,
                                  Long forceEndTime,
                                  CommonEnum.DevBuffType buffType,
                                  CommonEnum.DevBuffSourceType sourceType,
                                  Integer layer) {
        if (sourceType == CommonEnum.DevBuffSourceType.DBST_NONE) {
            throw new GeminiException(ErrorCode.BUFF_NO_CONFIG, "sourceType is none");
        }
        final int addLayer = layer == null ? 1 : layer;
        BuffTemplate toAddTemp = getBuffTemplate(buffId);
        if (toAddTemp == null) {
            throw new GeminiException(ErrorCode.BUFF_NO_CONFIG);
        }

        DevBuffProp devBuff = getDevBuffByGroupId(toAddTemp.getEffectGroupId());
        if (devBuff == null) {
            // 同组内无buff
            devBuff = doAdd(buffId, startTime, forceEndTime, buffType, sourceType, addLayer);
        } else {
            BuffTemplate curTemp = getBuffTemplate(devBuff.getDevBuffId());
            if (devBuff.getDevBuffId() == buffId && curTemp.getMerge()) {
                // 叠加buff
                final int preLayer = devBuff.getLayer();
                final int maxLayer = curTemp.getRuleValue();
                // 这里直接将layer加上，方便remove，在使用layer的地方判断上限
                devBuff.setLayer(preLayer + addLayer);
                log("{} add dev buff succ buffId:{} buffType:{} startTime:{} endTime:{} layer:{}->{} maxLayer:{} sourceType:{}",
                        owner, buffId, buffType, devBuff.getStartTime(), devBuff.getEndTime(), preLayer, devBuff.getLayer(), maxLayer, sourceType);
                if (sourceType != devBuff.getSourceType()) {
                    // 这里仅仅告警，一般来说策划不可以复用buff（即同一buff的source必须一致）
                    WechatLog.error("addDevBuff merge buff layer with diff sourceType. {}vs{} {} {}", sourceType, devBuff.getSourceType(), buffId, layer);
                }
                refreshAdditionCache();
            } else {
                if (toAddTemp.getEffectPriority() >= curTemp.getEffectPriority()) {
                    // 同组内，优先级>=时，顶掉原来的buff
                    removeDevBuff(devBuff.getDevBuffId(), null, false);
                    devBuff = doAdd(buffId, startTime, forceEndTime, buffType, sourceType, addLayer);
                } else {
                    // 优先级不够，加不上
                    return null;
                }
            }
        }

        if (devBuff != null) {
            afterAddDevBuff(devBuff, addLayer);
            // 添加子buff
            for (Integer subBuffId : toAddTemp.getSubBuffList()) {
                try {
                    addDevBuff(subBuffId, startTime, forceEndTime, buffType, sourceType, layer);
                } catch (Exception e) {
                    LOGGER.error("{}, add sub buff failed.buffId:{}, subBuffId:{}, cause:{}", getOwner(), buffId, subBuffId, e);
                }
            }
        }

        return devBuff;
    }

    /**
     * 移除buff
     *
     * @param layer     移除的层数。null表示全移除
     * @param isExpired 是否是过期删除
     */
    public DevBuffProp removeDevBuff(int buffId, Integer layer, boolean isExpired) {
        DevBuffProp devBuffV = getDevBuffSys().getDevBuffV(buffId);
        if (devBuffV == null) {
//            scheduleBuffExpired();
            return null;
        } else {
            // layer为null，全移除
            BuffTemplate buffTemplate = getBuffTemplate(buffId);

            final int preLayer = devBuffV.getLayer();
            final int realLayer = layer == null || layer <= 0 ? preLayer : layer;
            final int newLayer = preLayer - realLayer;
            devBuffV.setLayer(newLayer);
            refreshAdditionCache();

            if (newLayer <= 0) {
                log("{} remove dev buff succ buffId:{} sourceType:{}", owner, buffId, devBuffV.getSourceType());
                buffPriorityQueue.remove(devBuffV);
                getDevBuffSys().removeDevBuffV(buffId);
                scheduleBuffExpired();
            } else {
                log("{} remove dev buff succ by layer buffId:{} layer:{} sourceType:{}", owner, buffId, realLayer, devBuffV.getSourceType());
            }
            afterRemoveDevBuff(devBuffV, isExpired, realLayer);
            // 移除子buff
            for (Integer subBuffId : buffTemplate.getSubBuffList()) {
                try {
                    removeDevBuff(subBuffId, layer, isExpired);
                } catch (Exception e) {
                    LOGGER.error("{}, remove sub buff failed.buffId:{}, subBuffId:{}, cause:{}", getOwner(), buffId, subBuffId, e);
                }
            }
        }
        return devBuffV;
    }

    /**
     * 是否能添加buff
     */
    public boolean canAddDevBuff(int buffId) {
        BuffTemplate toAddTemp = getBuffTemplate(buffId);
        if (toAddTemp == null) {
            return false;
        }
        DevBuffProp devBuff = getDevBuffByGroupId(toAddTemp.getEffectGroupId());
        if (devBuff == null) {
            // 同组内无buff
            return true;
        } else {
            BuffTemplate curTemp = getBuffTemplate(devBuff.getDevBuffId());
            if (devBuff.getDevBuffId() == buffId && curTemp.getMerge()) {
                // 叠加buff，没到叠加上限
                return devBuff.getLayer() < curTemp.getRuleValue();
            } else {
                // 同组内，优先级>=时，顶掉原来的buff
                // 优先级不够，加不上
                return toAddTemp.getEffectPriority() >= curTemp.getEffectPriority();
            }
        }
    }

    public static BuffTemplate getBuffTemplate(int buffId) {
        return getResHolder().findValueFromMap(BuffTemplate.class, buffId);
    }

    public DevBuffProp getDevBuffByGroupId(int groupId) {
        return IterableUtils.find(getDevBuffSys().getDevBuff().values(), it -> {
            BuffTemplate temp = getBuffTemplate(it.getDevBuffId());
            if (temp != null) {
                return temp.getEffectGroupId() == groupId;
            } else {
                return false;
            }
        });
    }

    private DevBuffProp doAdd(
            int buffId,
            Long startTime,
            Long forceEndTime,
            CommonEnum.DevBuffType buffType,
            CommonEnum.DevBuffSourceType sourceType,
            int layer
    ) {
        BuffTemplate buffTemplate = getBuffTemplate(buffId);
        if (buffTemplate == null) {
            LOGGER.error("owner:{}, add dev buff:{} failed. buff template not exists.", owner, buffId);
            return null;
        }

        long realStartTime;
        if (startTime != null) {
            realStartTime = startTime;
        } else {
            realStartTime = SystemClock.now();
        }
        long endTime;
        if (forceEndTime != null) {
            endTime = forceEndTime;
        } else {
            endTime = buffTemplate.getLifeCycleValue() > 0 ? SystemClock.now() + TimeUtils.second2Ms(buffTemplate.getLifeCycleValue()) : 0;
        }

        log("{} add dev buff succ buffId:{} buffType:{} startTime:{} endTime:{} sourceType:{} layer:{}",
                owner, buffId, buffType, realStartTime, endTime, sourceType, layer);
        DevBuffProp devBuffProp = new DevBuffProp()
                .setDevBuffId(buffTemplate.getId())
                .setStartTime(realStartTime)
                .setEndTime(endTime)
                .setDevBuffType(buffType)
                .setLayer(layer)
                .setSourceType(sourceType);
        getDevBuffSys().putDevBuffV(devBuffProp);
        refreshAdditionCache();

        if (endTime > 0) {
            buffPriorityQueue.add(devBuffProp);
            scheduleBuffExpired();
        }
        return devBuffProp;
    }

    /**
     * buff定时
     */
    private void scheduleBuffExpired() {
        DevBuffProp peek = buffPriorityQueue.peek();
        if (peek != null) {
            // pending和queue.peek出来的不是同一个，说明有buff插到最前面了，取消pending的task
            if (pendingExpireBuffId != 0) {
                if (peek.getDevBuffId() != pendingExpireBuffId) {
                    cancelPendingTask();
                } else {
                    return;
                }
            }
            if (peek.getEndTime() <= 0) {
                // 永久buff，不定时
                return;
            }
            pendingExpireBuffId = peek.getDevBuffId();
            addPendingTask(peek);
        } else {
            cancelPendingTask();
        }
    }

    protected abstract void addTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable,
                                     long initialDelay, TimeUnit unit);

    protected abstract void cancelTimer(String prefix, TimerReasonType timerReasonType);

    protected void addPendingTask(DevBuffProp devBuff) {
        long delay = devBuff.getEndTime() < SystemClock.now() ? 0 : devBuff.getEndTime() - SystemClock.now();
        if (delay <= 0) {
            removeDevBuff(devBuff.getDevBuffId(), null, true);
            return;
        }
        addTimer(getTimerPrefix(devBuff.getDevBuffId()),
                TimerReasonType.BUFF_EXPIRE_TIMER,
                () -> removeDevBuff(devBuff.getDevBuffId(), null, true),
                delay,
                TimeUnit.MILLISECONDS);
        log("{} add dev buff pendingTask:{} {}ms", owner, devBuff.getDevBuffId(), delay);
    }

    protected void cancelPendingTask() {
        if (pendingExpireBuffId > 0) {
            log("{} cancel dev buff pendingTask:{} buffId:{}", owner, pendingExpireBuffId);
            // 顶掉之前的定时器
            cancelTimer(getTimerPrefix(pendingExpireBuffId), TimerReasonType.BUFF_EXPIRE_TIMER);
            pendingExpireBuffId = 0;
        }
    }

    private String getTimerPrefix(int buffId) {
        return owner.getEntityId() + "-" + buffId;
    }

    public List<DevBuffProp> getDevBuffByEffectType(CommonEnum.BuffEffectType type) {
        List<DevBuffProp> res = new ArrayList<>();
        for (Map.Entry<Integer, DevBuffProp> entry : getDevBuffSys().getDevBuff().entrySet()) {
            BuffTemplate buffTemplate = getBuffTemplate(entry.getKey());
            if (buffTemplate != null) {
                if (buffTemplate.getType() == type.getNumber()) {
                    res.add(entry.getValue());
                }
            }
        }
        return res;
    }

    public List<DevBuffProp> getDevBuffByBuffType(CommonEnum.DevBuffType type) {
        List<DevBuffProp> res = new ArrayList<>();
        for (Map.Entry<Integer, DevBuffProp> entry : getDevBuffSys().getDevBuff().entrySet()) {
            if (entry.getValue().getDevBuffType() == type) {
                res.add(entry.getValue());
            }
        }
        return res;
    }

    /**
     * 获取加成
     */
    public Map<CommonEnum.AdditionSourceType, Long> getAdditionValue(int additionId) {
        Map<CommonEnum.AdditionSourceType, Long> additionCache = additionMap.getOrDefault(additionId, Maps.newEnumMap(CommonEnum.AdditionSourceType.class));
        Map<CommonEnum.AdditionSourceType, Long> res = Maps.newHashMap(additionCache);
        for (CommonEnum.AdditionSourceType type : DevBuffUtil.DEV_BUFF_2_ADDITION_SOURCE.values()) {
            if (res.containsKey(type)) {
                continue;
            }
            res.put(type, 0L);
        }
        return res;
    }

    public boolean hasDevBuff(int buffId) {
        return getDevBuffSys().getDevBuffV(buffId) != null;
    }

    public void onPendingDevBuffExpired(long now) {
        pendingExpireBuffId = 0;
        DevBuffProp peek;
        do {
            peek = buffPriorityQueue.peek();
            if (peek == null || peek.getEndTime() > now) {
                break;
            }
            buffPriorityQueue.poll();
            removeDevBuff(peek.getDevBuffId(), null, true);
        } while (true);
    }
}

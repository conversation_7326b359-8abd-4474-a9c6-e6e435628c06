package com.yorha.common.cache;

import com.yorha.common.cache.action.BatchAsyncAddAction;
import com.yorha.common.cache.action.CacheAddAction;
import com.yorha.common.enums.CacheUsageEnum;

public class CacheMgr {

    /**
     * 建立通用缓存（LFU淘汰）
     *
     * @param entryLimit 缓存数目上限
     * @param timeLimit  缓存时间上限（写入时即固定）
     * @param <K>        任意类型
     * @param <V>        任意类型
     * @return 缓存实例
     */
    public static <K, V> BaseSyncCache<K, V> buildBaseSyncCache(int entryLimit, long timeLimit, CacheUsageEnum name) {
        return new BaseSyncCache<>(entryLimit, timeLimit, name.name());
    }

    /**
     * 建立自加载缓存（LFU淘汰，缓存不存在时根据cacheAddAction添加）
     *
     * @param entryLimit     缓存数目上限
     * @param timeLimit      缓存时间上限（写入时即固定）
     * @param cacheAddAction 添加缓存方法
     * @param <K>            任意类型
     * @param <V>            任意类型
     * @return 缓存实例
     */
    public static <K, V> AutoLoadSyncCache<K, V> buildAutoLoadCache(int entryLimit, long timeLimit, CacheAddAction<K, V> cacheAddAction, CacheUsageEnum name) {
        return new AutoLoadSyncCache<>(entryLimit, timeLimit, cacheAddAction, name.name());
    }

    /**
     * 建立自加载缓存（LFU淘汰，缓存不存在时使用自定义loader器）
     *
     * @param entryLimit 缓存数目上限
     * @param timeLimit  缓存时间上限（写入时即固定）
     * @param loader     缓存加载器
     * @param <K>        任意类型
     * @param <V>        任意类型
     * @return 缓存实例
     */
    public static <K, V> BatchAsyncLoadCache<K, V> buildBatchAsyncLoadCache(
            int entryLimit,
            long timeLimit,
            BatchAsyncAddAction<K, V> loader,
            CacheUsageEnum name
    ) {
        return new BatchAsyncLoadCache<>(entryLimit, timeLimit, loader, name.name());
    }
}

package com.yorha.common.db.tcaplus.option;

import com.yorha.proto.CommonEnum;

/**
 * <AUTHOR>
 */
public class UpdateOption {

    public static final UpdateOption DEFAULT_VALUE = UpdateOption.newBuilder().build();

    /**
     * 如果设置为负数，表示当前数据不启动版本控
     * 制
     */
    private int version = -1;
    private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
    /**
     * 是否需要重试。
     */
    private boolean isRetry;

    private UpdateOption() {
    }

    public int getVersion() {
        return this.version;
    }

    public int getResultFlag() {
        return this.resultFlag.getNumber();
    }

    public boolean isRetry() {
        return this.isRetry;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static final class Builder {
        private int version = -1;
        private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
        private boolean isRetry = false;

        private Builder() {
        }

        public Builder withVersion(int version) {
            this.version = version;
            return this;
        }

        public Builder withResultFlag(CommonEnum.TcaplusResultFlag resultFlag) {
            this.resultFlag = resultFlag;
            return this;
        }

        public Builder withRetry() {
            this.isRetry = true;
            return this;
        }

        public UpdateOption build() {
            UpdateOption updateOption = new UpdateOption();
            updateOption.version = this.version;
            updateOption.resultFlag = this.resultFlag;
            updateOption.isRetry = this.isRetry;
            return updateOption;
        }
    }
}

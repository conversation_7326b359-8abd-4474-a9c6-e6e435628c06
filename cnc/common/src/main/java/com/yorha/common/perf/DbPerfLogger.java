package com.yorha.common.perf;

import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * db性能日志。
 *
 * <AUTHOR>
 */
public class DbPerfLogger extends AbstractPerfLogger {
    private static final Logger LOGGER = LogManager.getLogger(DbPerfLogger.class);

    private final Map<String, DbReqRecord> reqRecordMap = new HashMap<>();
    private final Map<String, DbRespRecord> respRecordMap = new HashMap<>();
    private volatile long maxPlayerDbSize = 0;
    private volatile long maxPlayerId = 0;
    private volatile long maxClanDbSize = 0;
    private volatile long maxClanId = 0;

    private static class DbReqRecord {
        public long opNum = 0;
        public long totalSize = 0;
        public long maxSize = 0;
        public long lastQpsTsMs = 0;
        public int curSecQps = 0;
        public int maxSecQps = 0;

        public void trigger(String desc, long size) {
            totalSize += size;
            opNum++;
            if (maxSize < size) {
                maxSize = size;
                MonitorUnit.DB_TABLE_REQ_MAX_SIZE.labels(ServerContext.getBusId(), desc).set(maxSize);
            }
            if (lastQpsTsMs == 0 && curSecQps == 0) {
                // 第一次触发
                this.curSecQps++;
                this.maxSecQps = this.curSecQps;
                this.lastQpsTsMs = SystemClock.nowNative();
                return;
            }
            if (SystemClock.nowNative() - this.lastQpsTsMs > TimeUnit.SECONDS.toMillis(1)) {
                this.lastQpsTsMs = SystemClock.nowNative();
                if (maxSecQps < curSecQps) {
                    maxSecQps = curSecQps;
                    MonitorUnit.DB_OUT_MAX_QPS.labels(ServerContext.getBusId(), desc).set(maxSecQps);
                }
                this.curSecQps = 0;
            }
            this.curSecQps++;
        }
    }

    private static class DbRespRecord {
        public long opNum = 0;
        public long totalSize = 0;
        public long maxSize = 0;
        public long totalCost = 0;
        public long maxCost = 0;

        public void trigger(long size, long cost) {
            totalSize += size;
            totalCost += cost;
            opNum++;
            maxSize = Math.max(maxSize, size);
            maxCost = Math.max(maxCost, cost);
        }
    }

    public DbPerfLogger() {
        super("db-perf");
    }

    public void onPlayerSaveDb(long playerId, long dbSize) {
        run(() -> {
            if (dbSize > maxPlayerDbSize) {
                maxPlayerDbSize = dbSize;
                maxPlayerId = playerId;
                MonitorUnit.MAX_DB_PLAYER_SIZE.labels(ServerContext.getBusId()).set(dbSize);
                LOGGER.info("DbPerfLogger onPlayerSaveDb playerId={} dbSize={}", maxPlayerId, maxPlayerDbSize);
            }
        });
    }

    public void onClanSaveDb(long clanId, long dbSize) {
        run(() -> {
            if (dbSize > maxClanDbSize) {
                maxClanDbSize = dbSize;
                maxClanId = clanId;
                MonitorUnit.MAX_DB_CLAN_SIZE.labels(ServerContext.getBusId()).set(maxClanDbSize);
                LOGGER.info("DbPerfLogger onClanSaveDb clanId={} {}", maxClanId, maxClanDbSize);
            }
        });
    }

    public void logReq(long requestId, String tableName, String op, long size) {
        if (size > MonitorConstant.TCAPLUS_ROW_WARNING_WECHAT_BYTE_SIZE) {
            WechatLog.error("db_op_big TableName={}, requestId={}, op={}, size={}", tableName, requestId, op, size);
        } else if (size > MonitorConstant.TCAPLUS_ROW_WARNING_LOGGER_BYTE_SIZE) {
            LOGGER.error("db_op_big TableName={}, requestId={}, op={}, size={}", tableName, requestId, op, size);
        }

        MonitorUnit.DB_OP_ASK_BYTES_TOTAL.labels(ServerContext.getBusId(), tableName, op).inc(size);
        run(() -> {
            String desc = tableName + "#" + op;
            DbReqRecord dbRecord = reqRecordMap.computeIfAbsent(desc, k -> new DbReqRecord());
            dbRecord.trigger(desc, size);
        });
    }

    public void logResp(long requestId, String tableName, String op, long size, long costMs, int errorCode) {
        MonitorUnit.DB_OP_ANS_BYTES_TOTAL.labels(ServerContext.getBusId(), tableName, op).inc(size);
        if (!DbUtil.isOk(errorCode)) {
            LOGGER.warn("db_op_code TableName={}, requestId={}, op={}, size={}, code={}", tableName, requestId, op, size,  TcaplusErrorCode.forNumber(errorCode));
        }
        run(() -> {
            DbRespRecord dbRecord = respRecordMap.computeIfAbsent(tableName + "#" + op, k -> new DbRespRecord());
            dbRecord.trigger(size, costMs);
        });
    }

    private void showReq() {
        MonitorUnit.DB_OUT_MAX_QPS.clear();
        MonitorUnit.DB_TABLE_REQ_MAX_SIZE.clear();
        StringBuilder sb = new StringBuilder();
        sb.append("db_req_perf");
        for (Map.Entry<String, DbReqRecord> entry : reqRecordMap.entrySet()) {
            DbReqRecord value = entry.getValue();
            long num = value.opNum;
            if (num > 0) {
                long avgSize = value.totalSize / num;
                long maxSize = value.maxSize;
                long maxQps = value.maxSecQps;
                sb.append("\n|").append(entry.getKey())
                        .append("|").append("count:").append(num)
                        .append("|").append("avgSize:").append(avgSize)
                        .append("|").append("maxSize:").append(maxSize)
                        .append("|").append("maxQps:").append(maxQps)
                        .append("|");
            }
        }
        reqRecordMap.clear();
        getLoggerPerf().info(sb);
    }

    private void showResp() {
        MonitorUnit.DB_IN_AVG_COST.clear();
        StringBuilder sb = new StringBuilder();
        sb.append("db_resp_perf");
        for (Map.Entry<String, DbRespRecord> entry : respRecordMap.entrySet()) {
            DbRespRecord value = entry.getValue();
            long num = value.opNum;
            if (num > 0) {
                long avgSize = value.totalSize / num;
                long maxSize = value.maxSize;
                long avgCost = value.totalCost / num;
                long maxCost = value.maxCost;
                sb.append("\n|").append(entry.getKey())
                        .append("|").append("count:").append(num)
                        .append("|").append("avgSize:").append(avgSize)
                        .append("|").append("maxSize:").append(maxSize)
                        .append("|").append("avgCost:").append(avgCost)
                        .append("|").append("maxCost:").append(maxCost)
                        .append("|");
                MonitorUnit.DB_IN_AVG_COST.labels(ServerContext.getBusId(), entry.getKey()).set(avgCost);
            }
        }
        respRecordMap.clear();
        getLoggerPerf().info(sb);
    }

    public void flush() {
        run(() -> {
            showReq();
            showResp();
        });
    }

    public void resetMaxDbSize() {
        maxPlayerDbSize = 0;
        maxPlayerId = 0;
        maxClanDbSize = 0;
        maxClanId = 0;
    }

    public long getMaxPlayerDbSize() {
        return maxPlayerDbSize;
    }

    public long getMaxPlayerId() {
        return maxPlayerId;
    }

    public long getMaxClanDbSize() {
        return maxClanDbSize;
    }

    public long getMaxClanId() {
        return maxClanId;
    }

}

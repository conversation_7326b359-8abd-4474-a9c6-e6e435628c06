package com.yorha.common.resource.resservice.activity;

import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.time.TimeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityScheduleTemplate;
import res.template.ActivityTemplate;

import javax.annotation.Nullable;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 活动排期的东西越搞越复杂
 * <p>
 * 排期这里有些逻辑是需要在player和zone上一起跑的
 * <p>
 * 不同的ActivityOpenType依赖的动态参数(开服时间、创角时间等)和可支持的配置参数(循环、序列等)都有所不同，甚至对同一个参数的解读也未必一样，应严格明确参数意义和最终效果
 * <p>
 * 考虑到未来的维护，这里不封装OpenType的统一抽象类，有一种新的排期方式需求出现时，也可以考虑新增type，而不是在原type上做加法(比较危险)，保持每一个openType简单明确
 */
public class ActivitySchedule {
    private static final Logger LOGGER = LogManager.getLogger(ActivitySchedule.class);

    public final ActivityScheduleTemplate template;

    public ActivitySchedule(ActivityScheduleTemplate template) {
        this.template = template;
    }

    public static class Cell {
        public final int actId;
        public final ActivityTemplate template;
        public final Instant startTime;
        public final Instant expireTime;
        public final int loopTimes;

        public static Cell of(int actId, Instant startTime, int loopTimes) {
            ActivityTemplate activityTemplate = ResHolder.getTemplate(ActivityTemplate.class, actId);
            Instant expireTime = startTime.plus(activityTemplate.getLastingHours(), ChronoUnit.HOURS);
            return new Cell(actId, activityTemplate, startTime, expireTime, loopTimes);
        }

        public Cell(int actId, ActivityTemplate template, Instant startTime, Instant expireTime, int loopTimes) {
            this.actId = actId;
            this.template = template;
            this.startTime = startTime;
            this.expireTime = expireTime;
            this.loopTimes = loopTimes;
        }
    }

    /**
     * 定期开放
     */
    public static class OnDate {
        public static Cell cell(ActivitySchedule as) {
            final ActivityScheduleTemplate template = as.template;
            Instant confTime = template.getDateForOpenDt().toInstant();
            return Cell.of(template.getActivityId(), confTime, 0);
        }
    }

    /**
     * 创角N天
     */
    public static class FromPlayerCreate {
        public static Cell cell(ActivitySchedule as, long playerCreateTsMs) {
            Instant actStart = TimeUtils.getDayStartInstant(
                    Instant.ofEpochMilli(playerCreateTsMs).plus(as.template.getDaysForOpen(), ChronoUnit.DAYS)
            );
            return Cell.of(as.template.getActivityId(), actStart, 0);
        }
    }

    /**
     * 开服N天
     */
    public static class FromZoneOpen {
        public static Cell cell(ActivitySchedule as, Instant zoneOpenTime) {
            Instant start = TimeUtils.getDayStartInstant(zoneOpenTime.plus(as.template.getDaysForOpen(), ChronoUnit.DAYS));
            return Cell.of(as.template.getActivityId(), start, 0);
        }
    }

    private static int calcActId(ActivityScheduleTemplate template, int loopTimes) {
        int count = 0;
        List<IntPairType> seq = template.getActivitySeqPairList();
        if (seq == null || seq.isEmpty()) {
            return template.getActivityId();
        }
        for (IntPairType act2times : seq) {
            count += act2times.getValue();
            if (count >= loopTimes) {
                return act2times.getKey();
            }
        }
        return 0;
    }

    /**
     * 定期循环
     * <p>
     * 从配置的活动开启时间(dateForOpenDt)开始循环
     */
    public static class OnDateLoop {
        @Nullable
        public static Cell curOrNext(ActivitySchedule as, Instant now) {
            ActivityScheduleTemplate template = as.template;
            ActivityLoop activityLoop = ActivityLoop.of(template.getLoopType());
            // 首次开启时间
            Instant initOpenTime = template.getDateForOpenDt().toInstant();
            // 修正后的开启时间
            initOpenTime = activityLoop.withFixedParam(initOpenTime, template.getLoopParam());
            long intervalMs = activityLoop.intervalMs(template.getLoopCycle());

            return calcCurOrNextInLoop(now, template, initOpenTime, intervalMs);
        }
    }

    /**
     * 例子：
     * initOpenTime = 10.1
     * now = 10.6
     * intervalMs = 2 * 86400  2天一次循环
     * delta = (10.6 - 10.1) = 5 * 86400 当前开了5天了
     * loopTimes = ((5 / 2) + 1) = 3   开启到现在，循环了几次，+1是因为要算算上第一次
     * curLoopOpenTime = 10.1 + (3 - 1) * 2 = 10.5 当前正在执行的循环的开启时间
     * curActExpire 当前这一期的结束时间
     * nextActStart 下一期循环开启时间
     *
     * @return 当前或下一期期数信息
     */
    @Nullable
    private static Cell calcCurOrNextInLoop(Instant now, ActivityScheduleTemplate template, Instant initOpenTime, long intervalMs) {
        long delta = now.toEpochMilli() - initOpenTime.toEpochMilli();
        delta = delta < 0 ? 0 : delta;
        int loopTimes = (int) ((delta / intervalMs) + 1);
        Instant curLoopOpenTime = initOpenTime.plusMillis((loopTimes - 1) * intervalMs);
        int curActId = calcActId(template, loopTimes);
        if (curActId > 0) {
            ActivityTemplate curActTemplate = ResHolder.getTemplate(ActivityTemplate.class, curActId);
            Instant curActExpire = curLoopOpenTime.plus(curActTemplate.getLastingHours(), ChronoUnit.HOURS);
            if (curActExpire.isAfter(now)) {
                // 当前这一期还没有结束，返回当前这一期的cell
                return new Cell(curActId, curActTemplate, curLoopOpenTime, curActExpire, loopTimes);
            } else {
                // 当前这个循环的活动已经过了时间了，返回下一期的cell
                int nextActId = calcActId(template, loopTimes + 1);
                if (nextActId > 0) {
                    ActivityTemplate nextActTemplate = ResHolder.getTemplate(ActivityTemplate.class, nextActId);
                    Instant nextActStart = curLoopOpenTime.plusMillis(intervalMs);
                    Instant nextActExpire = nextActStart.plus(nextActTemplate.getLastingHours(), ChronoUnit.HOURS);
                    return new Cell(nextActId, nextActTemplate, nextActStart, nextActExpire, loopTimes + 1);
                }
            }
        }

        return null;
    }

    /**
     * 定期分服循环
     * <p>
     * 从配置的活动开启时间(dateForOpenDt)之后且当前时间满足开服天数限制(serverOpenLimitPairList)开始循环
     */
    public static final class OnDateZoneLoop {
        @Nullable
        public static Cell curOrNext(ActivitySchedule as, Instant zoneOpenTime, Instant now) {
            ActivityScheduleTemplate template = as.template;
            ActivityLoop activityLoop = ActivityLoop.of(template.getLoopType());
            // 第一期活动的开启时间
            Instant initOpenTime = template.getDateForOpenDt().toInstant();
            // 1. 算出活动开启时间当周的周X
            initOpenTime = activityLoop.withFixedParam(initOpenTime, template.getLoopParam());
            long intervalMs = activityLoop.intervalMs(template.getLoopCycle());

            // 定期分服和定期循环的区别就在于这里了
            // 定期分服的第一期是从满足开服时间后的那一期开始的
            int zoneOpenStartDay = 0;
            List<IntPairType> serverOpenLimitPairList = template.getServerOpenLimitPairList();
            if (serverOpenLimitPairList != null && !serverOpenLimitPairList.isEmpty()) {
                zoneOpenStartDay = serverOpenLimitPairList.get(0).getKey();
            }
            // 2. 算出满足开服天数限制后的时间
            Instant zoneAvailableTime = TimeUtils.getDayStartInstant(zoneOpenTime.plus(zoneOpenStartDay, ChronoUnit.DAYS));
            // 3. 算出满足天数限制和活动开启时间之间已经过了几期了
            long delta = zoneAvailableTime.toEpochMilli() - initOpenTime.toEpochMilli();
            long passOverTimes = delta <= 0 ? 0 : (delta / intervalMs);
            if (delta > 0) {
                if (template.getServerInsertOpen() && template.getBanInsertPeriodPair() != null) {
                    Instant initOpenTime1 = initOpenTime.plusMillis(passOverTimes * intervalMs);
                    Instant forbiddenPeriodStart = initOpenTime1.plus(template.getBanInsertPeriodPair().getKey(), ChronoUnit.HOURS);
                    Instant forbiddenPeriodEnd = initOpenTime1.plus(template.getBanInsertPeriodPair().getValue(), ChronoUnit.HOURS);
                    if (forbiddenPeriodStart.getEpochSecond() <= zoneAvailableTime.getEpochSecond()
                            && zoneAvailableTime.getEpochSecond() <= forbiddenPeriodEnd.getEpochSecond()) {
                        // 满足开服天数限制后的时间，落在禁止开启的窗口期内的时候，往后延一期
                        passOverTimes += 1;
                        LOGGER.info("OnDateZoneLoop curOrNext scheduleId={} zoneAvailableTime={} initOpenTime1={} forbiddenPeriodStart={} forbiddenPeriodEnd={} passOverTime={}",
                                template.getId(), zoneAvailableTime, initOpenTime1, forbiddenPeriodStart, forbiddenPeriodEnd, passOverTimes);
                    }
                } else {
                    // delta % intervalMs > 0 表示上一期正在开或者已经过了，所以要开后面一期。如果允许中途开启，则这一期就是要开的
                    // delta % intervalMs == 0 表示现在这一期就是要开的
                    boolean isOpening = delta % intervalMs > 0;
                    if (isOpening) {
                        // 如果本期活动正在开启中，且不允许中途插入，则往后延一期
                        passOverTimes += 1;
                    }
                }
            }
            // 4. 算出根据开服时间偏移后的第一期活动的开启时间
            initOpenTime = initOpenTime.plusMillis(passOverTimes * intervalMs);
            // 5. 通过第一期开启时间和当前时间，算出当前正真要开的那一期的时间
            return calcCurOrNextInLoop(now, template, initOpenTime, intervalMs);
        }
    }

    /**
     * 分服循环
     * <p>
     * 当前时间满足开服天数限制(serverOpenLimitPairList)之后开始循环
     */
    public static final class ZoneLoop {
        @Nullable
        public static Cell curOrNext(ActivitySchedule as, Instant zoneOpenTime, Instant now) {
            ActivityScheduleTemplate template = as.template;
            ActivityLoop activityLoop = ActivityLoop.of(template.getLoopType());
            long intervalMs = activityLoop.intervalMs(template.getLoopCycle());

            // 分服循环的第一期是从满足开服时间后的第一个周X开始的
            // 和定期分服循环的区别在于，一个双周循环的活动，定期分服循环里面，所有服都是在偶数周循环开启的；而分服循环则是有的服在偶数周循环，有的服在单数周循环
            int zoneOpenStartDay = 0;
            List<IntPairType> serverOpenLimitPairList = template.getServerOpenLimitPairList();
            if (serverOpenLimitPairList != null && !serverOpenLimitPairList.isEmpty()) {
                zoneOpenStartDay = serverOpenLimitPairList.get(0).getKey();
            }
            Instant zoneAvailableTime = TimeUtils.getDayStartInstant(zoneOpenTime.plus(zoneOpenStartDay, ChronoUnit.DAYS));
            Instant initOpenTime = activityLoop.withFixedParam(zoneAvailableTime, template.getLoopParam());

            return calcCurOrNextInLoop(now, template, initOpenTime, intervalMs);
        }
    }
}
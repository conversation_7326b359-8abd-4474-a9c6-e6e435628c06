package com.yorha.common.activity;

import com.yorha.game.gen.prop.ActivityProp;
import com.yorha.game.gen.prop.ZoneActivityProp;
import com.yorha.proto.CommonEnum;

// NOTE:为什么有这个类？因为需要在基类中操作两个不同的prop，所以使用代理模式来实现
public interface ActivityPropBase {
    ActivityPropBase setStatus(CommonEnum.ActivityStatus status);
    CommonEnum.ActivityStatus getStatus();

    ActivityPropBase setEndTsSec(int endTsSec);
    int getEndTsSec();

    ActivityPropBase setStartTsSec(int startTsSec);
    int getStartTsSec();

    ActivityPropBase setStatusEnterTsSec(int statusEnterTsSec);
    int getStatusEnterTsSec();

    class ActivityPropWrapper implements ActivityPropBase {
        private final ActivityProp prop;
        public ActivityPropWrapper(ActivityProp prop) {
            this.prop = prop;
        }

        @Override
        public ActivityPropWrapper setStatus(CommonEnum.ActivityStatus status) {
            prop.setStatus(status);
            return this;
        }
        @Override
        public CommonEnum.ActivityStatus getStatus() {
            return prop.getStatus();
        }
        @Override
        public ActivityPropWrapper setEndTsSec(int endTsSec) {
            prop.setEndTsSec(endTsSec);
            return this;
        }
        @Override
        public int getEndTsSec() {
            return prop.getEndTsSec();
        }
        @Override
        public ActivityPropWrapper setStartTsSec(int startTsSec) {
            prop.setStartTsSec(startTsSec);
            return this;
        }
        @Override
        public int getStartTsSec() {
            return prop.getStartTsSec();
        }
        @Override
        public ActivityPropWrapper setStatusEnterTsSec(int statusEnterTsSec) {
            prop.setStatusEnterTsSec(statusEnterTsSec);
            return this;
        }
        @Override
        public int getStatusEnterTsSec() {
            return prop.getStatusEnterTsSec();
        }
    }

    class ZoneActivityPropWrapper implements ActivityPropBase {
        private final ZoneActivityProp prop;
        public ZoneActivityPropWrapper(ZoneActivityProp prop) {
            this.prop = prop;
        }

        @Override
        public ZoneActivityPropWrapper setStatus(CommonEnum.ActivityStatus status) {
            prop.setStatus(status);
            return this;
        }
        @Override
        public CommonEnum.ActivityStatus getStatus() {
            return prop.getStatus();
        }
        @Override
        public ZoneActivityPropWrapper setEndTsSec(int endTsSec) {
            prop.setEndTsSec(endTsSec);
            return this;
        }
        @Override
        public int getEndTsSec() {
            return prop.getEndTsSec();
        }
        @Override
        public ZoneActivityPropWrapper setStartTsSec(int startTsSec) {
            prop.setStartTsSec(startTsSec);
            return this;
        }
        @Override
        public int getStartTsSec() {
            return prop.getStartTsSec();
        }
        @Override
        public ZoneActivityPropWrapper setStatusEnterTsSec(int statusEnterTsSec) {
            prop.setStatusEnterTsSec(statusEnterTsSec);
            return this;
        }
        @Override
        public int getStatusEnterTsSec() {
            return prop.getStatusEnterTsSec();
        }
    }

}

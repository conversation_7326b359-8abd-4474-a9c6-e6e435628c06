package com.yorha.common.utils;

import com.google.common.collect.Lists;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.URLEncoder;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * http param builder.
 * <p>
 * 用List实现，某些服务提供商依赖param顺序
 */
public class UrlParams {
    private static final Logger LOGGER = LogManager.getLogger(UrlParams.class);

    private final List<Pair<String, String>> params;

    public UrlParams() {
        this.params = Lists.newLinkedList();
    }

    public UrlParams(Map<String, String> map) {
        this.params = Lists.newLinkedList();
        map.forEach((k, v) -> this.params.add(Pair.of(k, v)));
    }

    public UrlParams append(String key, Object value) {
        params.add(new Pair<>(key, String.valueOf(value)));
        return this;
    }

    public UrlParams shuffle() {
        Collections.shuffle(params);
        return this;
    }

    public int size() {
        return params.size();
    }

    public void forEach(BiConsumer<String, String> consumer) {
        for (Pair<String, String> param : params) {
            consumer.accept(param.getFirst(), param.getSecond());
        }
    }

    public void forEachIndexed(TripleConsumer<Integer, String, String> consumer) {
        for (int i = 0; i < params.size(); i++) {
            Pair<String, String> p = this.params.get(i);
            consumer.accept(i, p.getFirst(), p.getSecond());
        }
    }

    /**
     * 按key字典排序
     */
    public UrlParams sorted() {
        params.sort(Comparator.comparing(Pair::getFirst));
        return this;
    }

    public String build() {
        return params.stream()
                .map(pair -> {
                    try {
                        return pair.getFirst() + "=" + URLEncoder.encode(pair.getSecond(), "UTF-8");
                    } catch (Exception e) {
                        LOGGER.error(e.getMessage(), e);
                    }
                    return null;
                }).collect(Collectors.joining("&"));
    }

    public String get(String key) {
        for (Pair<String, String> param : params) {
            if (param.getFirst().equals(key)) {
                return param.getSecond();
            }
        }
        return null;
    }


    public static String append(String paramStr, String key, Object value) {
        String pair = key + "=" + value;
        if (StringUtils.isEmpty(paramStr)) {
            return pair;
        }
        return paramStr + "&" + pair;
    }

}

package com.yorha.common.db.mongo.listener;

import com.mongodb.event.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * MongoDB 连接池监听器，用于监控连接池和连接的生命周期事件。
 */
public class MongoConnectionPoolListener implements ConnectionPoolListener {
    private static final Logger LOGGER = LogManager.getLogger(MongoConnectionPoolListener.class);

    /**
     * 当一个新地连接池被创建时触发。
     * 通常发生在于某个 MongoDB 节点首次建立连接时。
     *
     * @param event 包含服务端节点信息的事件对象
     */
    @Override
    public void connectionPoolCreated(ConnectionPoolCreatedEvent event) {
        LOGGER.debug("Connection pool created: {}", event.getServerId());
    }

    /**
     * 当一个连接池被关闭时触发。
     * 可能由于该节点下线、拓扑变化或客户端关闭等原因。
     *
     * @param event 包含服务端节点信息的事件对象
     */
    @Override
    public void connectionPoolClosed(ConnectionPoolClosedEvent event) {
        LOGGER.debug("Connection pool closed: {}", event.getServerId());
    }

    /**
     * 当从连接池中取出一个连接用于执行数据库操作时触发。
     * 表示该连接开始被使用。
     *
     * @param event 包含连接ID的事件对象
     */
    @Override
    public void connectionCheckedOut(ConnectionCheckedOutEvent event) {
        LOGGER.debug("Connection checked out: {}", event.getConnectionId());
    }

    /**
     * 当一个连接完成使用并被归还到连接池时触发。
     * 表示该连接可以被其他请求复用。
     *
     * @param event 包含连接ID的事件对象
     */
    @Override
    public void connectionCheckedIn(ConnectionCheckedInEvent event) {
        LOGGER.debug("Connection checked in: {}", event.getConnectionId());
    }

    /**
     * 当一个新的底层物理连接成功建立时触发。
     * 表示与 MongoDB 节点之间的 TCP 连接已经建立。
     *
     * @param event 包含连接ID的事件对象
     */
    @Override
    public void connectionCreated(ConnectionCreatedEvent event) {
        LOGGER.debug("Connection created: {}", event.getConnectionId());
    }

    /**
     * 当一个连接被关闭时触发。
     * 可能原因包括空闲超时、异常中断、主动关闭等。
     *
     * @param event 包含连接ID和关闭原因的事件对象
     */
    @Override
    public void connectionClosed(ConnectionClosedEvent event) {
        LOGGER.debug("Connection closed: {}, reason: {}", event.getConnectionId(), event.getReason());
    }
}

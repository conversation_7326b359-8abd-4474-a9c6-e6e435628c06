package com.yorha.common.banner;

import com.yorha.common.utils.FileUtils;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.InputStream;


/**
 * <AUTHOR>
 */
public class BannerPrinter {
    private static final Logger LOGGER = LogManager.getLogger(BannerPrinter.class);

    private static final String DEFAULT_BANNER_LOCATION = "banner.txt";


    public static void print(String id, String type, Class<?> mainClass) {
        try {
            InputStream is = FileUtils.getInputStream(DEFAULT_BANNER_LOCATION);
            if (is != null) {
                String content = StringUtils.readString(is);
                Banner banner = new ResourceBanner(content);
                banner.printBanner(id, type, mainClass);
            } else {
                LOGGER.warn("Banner file not found at location: " + DEFAULT_BANNER_LOCATION);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }
}

package com.yorha.common.io;

import com.yorha.common.constant.LogConstant;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 流量性能分析
 *
 * <AUTHOR>
 */
public class TrafficCounter {
    private static final Logger LOGGER_PERF = LogManager.getLogger(LogConstant.LOG_TYPE_PERF_STAT);

    /**
     * 当前tick次数
     */
    private int curTickTimes = 0;
    /**
     * 每tick10次输出一次total信息
     */
    private static final int TOTAL_TICK_TIMES = 60;

    /**
     * interval秒为单位
     */
    private final AtomicLong tickWriteBytes = new AtomicLong();
    private final AtomicLong tickReadBytes = new AtomicLong();


    /**
     * interval分为单位
     */
    private final AtomicLong totalWriteBytes = new AtomicLong();
    private final AtomicLong totalReadBytes = new AtomicLong();

    /**
     * 写的峰值(秒为单位)
     */
    private final AtomicLong peakWriteBytesInTotal = new AtomicLong();
    /**
     * 读的峰值(秒为单位)
     */
    private final AtomicLong peakReadBytesInTotal = new AtomicLong();

    private final String name;

    /**
     * tick清算时间戳
     */
    private long lastTickCaptureTs;
    /**
     * total清算时间戳
     */
    private long lastTotalCaptureTs;

    public TrafficCounter(String name) {
        this.name = name;
    }

    public synchronized void start() {
        this.lastTickCaptureTs = SystemClock.nowNative();
        this.lastTotalCaptureTs = SystemClock.nowNative();
    }

    public synchronized void stop() {
        resetTickCapture();
        resetTotalCapture();
    }

    /**
     * 收到流量
     */
    void onReadBytes(long recv) {
        tickReadBytes.addAndGet(recv);
        totalReadBytes.addAndGet(recv);
    }

    /**
     * 输出流量
     */
    void onWriteBytes(long write) {
        tickWriteBytes.addAndGet(write);
        totalWriteBytes.addAndGet(write);
    }

    public void resetTickCapture() {
        curTickTimes++;
        long currentTs = SystemClock.nowNative();
        final long interval = currentTs - lastTickCaptureTs;
        if (interval == 0) {
            return;
        }
        // 比较是否成为峰值
        peakWriteBytesInTotal.getAndUpdate(operand -> Math.max(operand, tickWriteBytes.get()));
        peakReadBytesInTotal.getAndUpdate(operand -> Math.max(operand, tickReadBytes.get()));

        this.lastTickCaptureTs = currentTs;
        tickWriteBytes.set(0);
        tickReadBytes.set(0);

        if (curTickTimes % TOTAL_TICK_TIMES == 0) {
            // 触发一次total输出
            resetTotalCapture();
        }
    }

    private static String getUnit(long bms) {
        String tmp;
        if (bms < 1000) {
            tmp = bms + "b/s";
        } else if (bms < 1_000_000) {
            tmp = bms / 1000 + "kb/s";
        } else {
            tmp = bms / 1_000_000 + "mb/s";
        }
        return tmp;
    }

    public void resetTotalCapture() {
        long currentTs = SystemClock.nowNative();
        final long interval = currentTs - lastTotalCaptureTs;
        if (interval == 0) {
            return;
        }

        long lastTotalReadThroughput = totalReadBytes.get() * 1000 / interval;
        long lastTotalWriteThroughput = totalWriteBytes.get() * 1000 / interval;
        if (lastTotalReadThroughput != 0 || lastTotalWriteThroughput != 0) {
            LOGGER_PERF.info("{} last total avg read:{}bytes/s write:{}bytes/s", name, getUnit(lastTotalReadThroughput), getUnit(lastTotalWriteThroughput));
            LOGGER_PERF.info("{} last total peak read:{}bytes/s write:{}bytes/s", name, getUnit(peakReadBytesInTotal.get()), getUnit(peakWriteBytesInTotal.get()));
        }

        lastTotalCaptureTs = SystemClock.nowNative();
        totalReadBytes.set(0);
        totalWriteBytes.set(0);
        peakWriteBytesInTotal.set(0);
        peakReadBytesInTotal.set(0);
    }
}

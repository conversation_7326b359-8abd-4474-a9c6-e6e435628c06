package com.yorha.common.auth.server;

import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.auth.server.result.AuthResponse;
import com.yorha.common.auth.server.result.AuthResult;
import com.yorha.common.constant.AuthConstant;
import com.yorha.common.enums.WhitePermissions;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.ISession;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.whiteList.WhiteListResService;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * Intl鉴权渠道
 *
 * <AUTHOR>
 */
public class AuthChannelIntl implements AuthChannel {
    private static final Logger LOGGER = LogManager.getLogger(AuthChannelIntl.class);


    @Override
    public AuthConstant.AuthChannelType authChannelType() {
        return AuthConstant.AuthChannelType.INTL;
    }

    @Override
    public void init() {
        final String authHost = ClusterConfigUtils.getWorldConfig().getStringItem("auth_intl_host");
        LOGGER.info("init intl auth host:{}", authHost);
    }

    @Override
    public AuthResult<AuthResponse> auth(ISession session, GameActorWithCall actor, String openId, User.GetRoleList_C2S_Msg msg) {
        CommonMsg.AccountIdentityIntl intl = msg.getIntl();
        AuthResponse authResponse = authWithWhiteList(actor, session.getClientIp(), openId, intl.getToken(),
                msg.getClientInfo().getPlatformId(), msg.getClientInfo().getChannelId());
        if (authResponse.isSuccess()) {
            return AuthResult.ofSuccess();
        } else {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_VERIFY_FAILED, authResponse.toString());
        }
    }

    private AuthResponse authWithWhiteList(GameActorWithCall actor, String clientIp, String openId, String token, int platformId, int channelId) {
        // 非空校验
        if (StringUtils.isEmpty(clientIp)) {
            return AuthResponse.fail("clientIp not found.");
        }
        if (StringUtils.isEmpty(openId)) {
            return AuthResponse.fail("openId is empty.");
        }

        // 鉴权白名单， 跳过鉴权
        if (StringUtils.isEmpty(token)) {
            WhiteListResService whiteService = ResHolder.getResService(WhiteListResService.class);
            boolean passAuth = whiteService.hasWhitePermission(0, openId, "", "", WhitePermissions.WP_AUTH);
            if (passAuth) {
                LOGGER.info("ATTENTION!! pass auth by white list, openId:{}, ip:{}", openId, clientIp);
                return AuthResponse.success();
            }
        }
        return postAuth(actor, openId, token, platformId, channelId);
    }

    private static AuthResponse postAuth(GameActorWithCall actor, String openId, String token, int platformId, int channelId) {
        try {
            SsAuth.AuthIntlAsk ask = SsAuth.AuthIntlAsk.newBuilder()
                    .setOpenId(openId)
                    .setToken(token)
                    .setPlatformId(platformId)
                    .setChannelId(channelId)
                    .build();

            SsAuth.AuthIntlAns ans = actor.callAndCreate(RefFactory.ofAuth(), ask);
            return JsonUtils.parseObject(ans.getResult(), AuthResponse.class);
        } catch (Exception e) {
            if (e instanceof GeminiException) {
                LOGGER.warn("postAuth fail ", e);
                return AuthResponse.fail("post auth network fail");
            }
            LOGGER.error("postAuth fail ", e);
            return AuthResponse.fail("unknown");
        }
    }

    @Override
    public String getOpenId(User.GetRoleList_C2S_Msg msg) {
        return msg.getIntl().getOpenId();
    }

    @Override
    public String getOpenId(CsAccount.DirGetServerList_C2S_Msg msg) {
        return msg.getOpenId();
    }

    @Override
    public String getOpenId(CsAccount.DirGetZone_C2S_Msg msg) {
        return msg.getOpenId();
    }

}
package com.yorha.common.server.discovery;

import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;

/**
 * 服务发现中每个服务的信息（信息刷新存在问题，仅可用于服务发现判断zone是否存活）
 *
 * <AUTHOR>
 */
public class ZoneItem {

    private int zoneId;
    /**
     * ServerOpenStatus的String
     */
    private String openStatus;

    /**
     * 开服时间
     */
    private long openTsMs;

    private PojoZoneCard zoneCard;

    public String getOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(final String newOpenStatus) {
        openStatus = newOpenStatus;
    }

    public int getZoneId() {
        return zoneId;
    }

    public long getOpenTsMs() {
        return openTsMs;
    }

    public void setOpenTsMs(final long newOpenTsMs) {
        openTsMs = newOpenTsMs;
    }

    public CommonMsg.ZoneCard getZoneCard() {
        return pojoToPb(this.zoneCard);
    }

    public void setZoneCard(CommonMsg.ZoneCard zoneCard) {
        this.zoneCard = pbToPojo(zoneCard);
    }

    public static ZoneItem newItem(int zoneId, long openTsMs, String openStatus) {
        final ZoneItem item = new ZoneItem();
        item.zoneId = zoneId;
        item.openTsMs = openTsMs;
        item.openStatus = openStatus;
        item.zoneCard = new PojoZoneCard();
        return item;
    }

    @Override
    public String toString() {
        return "ZoneItem{" +
                "zoneId=" + zoneId +
                ", openStatus='" + openStatus + '\'' +
                ", openTsMs=" + openTsMs +
                ", zoneCard=" + zoneCard +
                '}';
    }

    /**
     * 是否对玩家开放
     */
    public boolean isOpenToPlayer() {
        return ServerOpenStatus.valueOf(this.openStatus).equals(ServerOpenStatus.OPEN);
    }

    /**
     * 是否已开服
     */
    public boolean isAfterOpenTs() {
        return this.openTsMs < SystemClock.now();
    }


    static PojoZoneCard pbToPojo(CommonMsg.ZoneCard zoneCard) {
        PojoZoneCard ret = new PojoZoneCard();
        ret.zoneSeason = zoneCard.getZoneSeason();
        ret.serverJamStatus = zoneCard.getServerJamStatus();
        ret.serverPower = zoneCard.getServerPower();
        ret.mileStoneId = zoneCard.getMileStoneId();
        return ret;
    }

    static CommonMsg.ZoneCard pojoToPb(PojoZoneCard pojoZoneCard) {
        return CommonMsg.ZoneCard.newBuilder()
                .setServerPower(pojoZoneCard.serverPower)
                .setZoneSeason(pojoZoneCard.zoneSeason)
                .setServerJamStatus(pojoZoneCard.serverJamStatus)
                .setMileStoneId(pojoZoneCard.mileStoneId)
                .build();
    }

    static class PojoZoneCard {
        long serverPower = 0;
        CommonEnum.ZoneSeason zoneSeason = CommonEnum.ZoneSeason.ZS_NONE;
        PojoMigrateInfo migrateInfo = new PojoMigrateInfo();
        CommonEnum.ServerJamStatus serverJamStatus = CommonEnum.ServerJamStatus.SJS_NONE;
        int mileStoneId = 0;
    }

    static class PojoMigrateInfo {
        int newMigrateInNum = 0;
        long normalMigrateMaxPower = 0;
        long specialMigrateMaxPower = 0;
        long kingPlayerId = 0;
    }
}
package com.yorha.common.actor.cluster;

import com.yorha.common.actor.cluster.toplogy.Node;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.server.NodeRole;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.proto.SsNode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

public class ClusterHelper {
    private static final Logger LOGGER = LogManager.getLogger(ClusterHelper.class);

    public static final long CLUSTER_NODE_LEASE_TTL_SEC_PROD = 60;

    public static void broadcastStringToAllNodes(final ActorSystem actorSystem, final String msg, final IActorRef sender) {
        LOGGER.info("gemini_system broadcastStringToAllNodes msg={}, sender={}", msg, sender);
        final SsNode.NodeTaskCmd cmd = SsNode.NodeTaskCmd.newBuilder()
                .setNodeTaskType(SsNode.NodeTaskType.NTT_STRING_MSG)
                .setStringMsg(msg)
                .setTaskId(IdFactory.nextId("broadcast_task"))
                .build();
        for (final NodeRole nodeRole : NodeRole.values()) {
            final List<Node> nodeList = actorSystem.getRegistryValue().getServerTypeNodeList(nodeRole.getTypeId());
            if (nodeList.isEmpty()) {
                continue;
            }
            for (final Node node : nodeList) {
                ActorSendMsgUtils.send(RefFactory.ofNode(node.getBusId()), sender, cmd);
            }
        }
    }
}

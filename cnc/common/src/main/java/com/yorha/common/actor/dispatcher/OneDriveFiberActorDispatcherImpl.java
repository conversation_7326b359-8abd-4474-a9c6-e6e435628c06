package com.yorha.common.actor.dispatcher;

import com.yorha.common.actor.mailbox.IRunnableActorMailbox;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.exception.GeminiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 单次驱动的协程调度器
 *
 * <AUTHOR>
 */
public class OneDriveFiberActorDispatcherImpl implements IMailboxDispatcher {
    private static final Logger LOGGER = LogManager.getLogger(OneDriveFiberActorDispatcherImpl.class);
    private final int throughput;
    private final GeminiThreadPoolExecutor schedulerExecutor;
    private final int keepAliveSec;
    private final Map<String, AtomicInteger> fiberCntMap;
    private final ThreadFactory factory;

    public OneDriveFiberActorDispatcherImpl(final String name, final int throughput, final int parallelism, final int keepAliveSec) {
        LOGGER.info("create onedrive fiber actor dispatcher, name:{} throughput:{} parallelism:{} keepAliveSec:{} seconds", name, throughput, parallelism, keepAliveSec);
        this.throughput = throughput;
        this.schedulerExecutor = ConcurrentHelper.newFixedThreadExecutor(name, parallelism, 0, false);
        this.keepAliveSec = keepAliveSec;
        this.fiberCntMap = new ConcurrentHashMap<>();
        this.factory = ConcurrentHelper.newFiberFactory(name, this.schedulerExecutor);
    }

    @Override
    public String getName() {
        return this.schedulerExecutor.getName();
    }

    @Override
    public int getThroughput() {
        return this.throughput;
    }

    @Override
    public void schedule(IRunnableActorMailbox mb) {
        this.factory.newThread(() -> {
            final AtomicInteger cnt = this.fiberCntMap.computeIfAbsent(mb.ref().getActorRole(), (k) -> new AtomicInteger(0));
            try {
                cnt.incrementAndGet();
                mb.run();
            } finally {
                cnt.decrementAndGet();
            }
        }).start();
    }

    @Override
    public void setParallelism(int parallelism) {
        this.schedulerExecutor.setCorePoolSize(parallelism);
    }

    @Override
    public int getParallelism() {
        return this.schedulerExecutor.getCorePoolSize();
    }

    @Override
    public int getFiberCnt(final String actorRole) {
        final AtomicInteger concurrency = this.fiberCntMap.get(actorRole);
        return concurrency != null ? concurrency.get() : 0;
    }

    @Override
    public int getKeepAliveSec() {
        return this.keepAliveSec;
    }

    @Override
    public void shutdown() {
        if (this.schedulerExecutor.isShutdown()) {
            throw new GeminiException("{} Already Shutdown!", this);
        }
        this.schedulerExecutor.shutdown();
        try {
            final boolean isFinished = this.schedulerExecutor.awaitTermination(10, TimeUnit.SECONDS);
            if (!isFinished) {
                LOGGER.warn("{} Shutdown! NotFinished After 10s!", this);
            }
        } catch (InterruptedException e) {
            LOGGER.warn("{} Shutdown! But Interrupted!", this);
        }
    }

    @Override
    public String toString() {
        return "OneDriveFiberActorDispatcher{" +
                "name='" + this.getName() + '\'' +
                ", throughPut=" + this.getThroughput() +
                ", parallelism=" + this.getParallelism() +
                '}';
    }
}

package com.yorha.common.perf;

import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 进程性能分析
 */
public class ProcessPerfLogger extends AbstractPerfLogger {
    private final Map<String, SsOutRecord> ssOutRecordMap = new HashMap<>();
    private final Map<String, SsInRecord> ssInRecordMap = new HashMap<>();
    private final Map<String, Long> compressMap = new HashMap<>();
    private final Map<String, Long> unCompressMap = new HashMap<>();
    private final MsgSizeCounter msgSizeCounter = new MsgSizeCounter(MonitorConstant.LARGE_MSG_SIZE, MonitorUnit.SS_OUT_MAX_SIZE);

    private static class SsOutRecord {
        public long opNum = 0;
        public long totalSize = 0;
        public long maxSize = 0;
        public long lastQpsTsMs = 0;
        public int curSecQps = 0;
        public int maxSecQps = 0;

        public void trigger(long size) {
            totalSize += size;
            opNum++;
            maxSize = Math.max(maxSize, size);
            if (lastQpsTsMs == 0 && curSecQps == 0) {
                // 第一次触发
                this.curSecQps++;
                this.maxSecQps = this.curSecQps;
                this.lastQpsTsMs = SystemClock.nowNative();
                return;
            }
            if (SystemClock.nowNative() - this.lastQpsTsMs > TimeUnit.SECONDS.toMillis(1)) {
                this.lastQpsTsMs = SystemClock.nowNative();
                this.maxSecQps = Math.max(this.curSecQps, this.maxSecQps);
                this.curSecQps = 0;
            }
            this.curSecQps++;
        }
    }

    private static class SsInRecord {
        public long opNum = 0;
        public long totalSize = 0;
        public long maxSize = 0;
        public long totalCost = 0;
        public long maxCost = 0;

        public void trigger(long size, long cost) {
            totalSize += size;
            totalCost += cost;
            opNum++;
            maxSize = Math.max(maxSize, size);
            maxCost = Math.max(maxCost, cost);
        }
    }

    public ProcessPerfLogger() {
        super("process-perf");
    }

    public void logSsOut(String sender, String msgType, long size) {
        run(() -> {
            String key = sender + "#" + msgType;
            SsOutRecord ssOutRecord = ssOutRecordMap.computeIfAbsent(key, k -> new SsOutRecord());
            ssOutRecord.trigger(size);
            msgSizeCounter.log(msgType, size);
        });
    }

    public void logSsIn(String msgType, long size, long cost) {
        run(() -> {
            SsInRecord ssInRecord = ssInRecordMap.computeIfAbsent(msgType, k -> new SsInRecord());
            ssInRecord.trigger(size, cost);
        });
    }

    public void showSsOut() {
        MonitorUnit.SS_OUT_MAX_QPS.clear();
        StringBuilder sb = new StringBuilder();
        sb.append("ss_out_perf");
        for (Map.Entry<String, SsOutRecord> entry : ssOutRecordMap.entrySet()) {
            SsOutRecord value = entry.getValue();
            long num = value.opNum;
            if (num > 0) {
                long avgSize = value.totalSize / value.opNum;
                long maxSize = value.maxSize;
                long maxQps = value.maxSecQps;
                sb.append("\n|").append(entry.getKey())
                        .append("|").append("count:").append(num)
                        .append("|").append("avgSize:").append(avgSize)
                        .append("|").append("maxSize:").append(maxSize)
                        .append("|").append("maxQps:").append(maxQps)
                        .append("|");
                MonitorUnit.SS_OUT_MAX_QPS.labels(ServerContext.getBusId(), entry.getKey()).set(maxQps);
            }
        }
        ssOutRecordMap.clear();
        getLoggerPerf().info(sb);
    }

    public void showSsIn() {
        MonitorUnit.SS_IN_AVG_COST.clear();
        StringBuilder sb = new StringBuilder();
        sb.append("ss_in_perf");
        for (Map.Entry<String, SsInRecord> entry : ssInRecordMap.entrySet()) {
            SsInRecord value = entry.getValue();
            long num = value.opNum;
            if (num > 0) {
                long avgSize = value.totalSize / value.opNum;
                long maxSize = value.maxSize;
                long avgCost = value.totalCost / value.opNum;
                long maxCost = value.maxCost;
                sb.append("\n|").append(entry.getKey())
                        .append("|").append("count:").append(num)
                        .append("|").append("avgSize:").append(avgSize)
                        .append("|").append("maxSize:").append(maxSize)
                        .append("|").append("avgCost:").append(avgCost)
                        .append("|").append("maxCost:").append(maxCost)
                        .append("|");
                MonitorUnit.SS_IN_AVG_COST.labels(ServerContext.getBusId(), entry.getKey()).set(avgCost);
            }
        }
        ssInRecordMap.clear();
        getLoggerPerf().info(sb);
    }

    public void logCompress(String reason) {
        run(() -> {
            Long v = compressMap.computeIfAbsent(reason, k -> 0L);
            compressMap.put(reason, v + 1);
        });
    }

    public void logUnCompress(String reason) {
        run(() -> {
            Long v = unCompressMap.computeIfAbsent(reason, k -> 0L);
            unCompressMap.put(reason, v + 1);
        });
    }

    public void showCompress() {
        MonitorUnit.COMPRESS_NUM.clear();
        MonitorUnit.UNCOMPRESS_NUM.clear();
        for (Map.Entry<String, Long> entry : compressMap.entrySet()) {
            MonitorUnit.COMPRESS_NUM.labels(ServerContext.getBusId(), entry.getKey()).set(entry.getValue());
        }
        for (Map.Entry<String, Long> entry : unCompressMap.entrySet()) {
            MonitorUnit.UNCOMPRESS_NUM.labels(ServerContext.getBusId(), entry.getKey()).inc(entry.getValue());
        }
        compressMap.clear();
        unCompressMap.clear();
    }

    public void flush() {
        run(() -> {
            showSsOut();
            showSsIn();
            showCompress();
            msgSizeCounter.clear();
        });
    }
}

package com.yorha.common.utils.time.schedule;

import com.yorha.common.enums.TimerReasonType;

import java.util.concurrent.TimeUnit;

/**
 * timerComponent需要实现该接口
 *
 * <AUTHOR>
 */
public interface ActorTimerInterface {

    void addTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit);

    void addTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit);

    void addRepeatTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit);

    void addRepeatTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit);

    void addFixRepeatTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit);

    void addFixRepeatTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit);

    void cancelTimer(TimerReasonType timerReasonType);

    void cancelTimer(TimerReasonType timerReasonType, String prefix);

    boolean containTimer(TimerReasonType timerReasonType, String prefix);

    void onDestroy();

}

package com.yorha.common.actorservice;

import com.yorha.common.actor.dispatcher.DefaultFastFailMailboxDispatcher;
import com.yorha.common.actor.dispatcher.IMailboxDispatcher;
import com.yorha.common.actor.mailbox.IMailboxFactory;
import com.yorha.common.actor.mailbox.IMailboxMetaData;
import com.yorha.common.actor.mailbox.middleware.DefaultSwallowMiddlewareImpl;
import com.yorha.common.actor.mailbox.middleware.IMailboxMiddleware;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.Objects;
import java.util.function.Function;

/**
 * Actor描述数据。
 *
 * <AUTHOR>
 */
public final class ActorMetaData implements IMailboxMetaData {
    private final String actorRole;
    private final Function<IActorRef, AbstractActor> factory;
    private final int actorShardNum;
    private final IMailboxFactory localMailboxFactory;
    private final IMailboxMiddleware mailboxMiddleware;
    private final IMailboxDispatcher dispatcher;
    private final boolean isNeedCreateMsg;
    private final int mailboxQueueSize;
    private final int mailboxMaxCount;
    private final ActorSystem actorSystem;

    private ActorMetaData(final ActorSystem actorSystem, final Builder builder) {
        this.actorRole = builder.actorRole;
        this.actorShardNum = builder.shardNum;
        this.isNeedCreateMsg = builder.isNeedCreateMsg;
        this.mailboxQueueSize = builder.mailboxQueueSize;
        this.actorSystem = actorSystem;
        this.dispatcher = builder.dispatcherFactory.apply(this);
        this.mailboxMiddleware = builder.middlewareFactory.apply(this);
        this.factory = builder.actorFactory.apply(this);
        this.localMailboxFactory = builder.localMailboxFactory.apply(this);
        this.mailboxMaxCount = builder.mailboxMaxCount;
    }

    /**
     * @return 本地邮箱的生成工厂。
     */
    public IMailboxFactory getLocalMailboxFactory() {
        return this.localMailboxFactory;
    }

    /**
     * @return actor的工厂。
     */
    @Override
    public Function<IActorRef, AbstractActor> getActorFactory() {
        return this.factory;
    }

    /**
     * @return actor role。
     */
    public String getActorRole() {
        return actorRole;
    }

    /**
     * @return actor的拉起是否需要一个createMsg。
     */
    public boolean needCreateMsg() {
        return this.isNeedCreateMsg;
    }

    /**
     * Shard是共享地址的分片Actor，运行后shard数量不变。
     * 非Shard Actor独享地址。
     *
     * @return == 0代表式非shard的actor; > 0 表示shard actor。
     */
    public int getActorShardNum() {
        return this.actorShardNum;
    }

    /**
     * 邮箱的最大数量。
     *
     * @return 邮箱最大值。
     */
    public int getMailboxMaxCount() {
        return this.mailboxMaxCount;
    }

    /**
     * 邮箱的队列的长度，投递超过邮箱队列长度的消息将被丢失。
     *
     * @return == 0代表不限长度的邮箱; > 0 则表示邮箱的消息队列长度。
     */
    @Override
    public int getMailboxQueueSize() {
        return this.mailboxQueueSize;
    }

    /**
     * @return 邮箱调度器。
     */
    @Override
    public IMailboxDispatcher getDispatcher() {
        return this.dispatcher;
    }

    /**
     * @return 邮箱中间件。
     */
    @Override
    public IMailboxMiddleware getMailboxMiddleware() {
        return this.mailboxMiddleware;
    }

    /**
     * 所属ActorSystem。
     *
     * @return actor system。
     */
    @Override
    public ActorSystem getActorSystem() {
        return actorSystem;
    }

    @Override
    public String toString() {
        return "ActorMetaData{" +
                "actorRole=" + actorRole +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final ActorMetaData metaData = (ActorMetaData) o;
        return Objects.equals(actorRole, metaData.actorRole);
    }

    @Override
    public int hashCode() {
        return actorRole != null ? actorRole.hashCode() : 0;
    }

    public static class Builder {
        private String actorRole;
        private int shardNum = 0;
        private Function<ActorMetaData, Function<IActorRef, AbstractActor>> actorFactory = (meta) -> {
            return (ref) -> {
                throw new GeminiException("ref {} to not ok meta!", ref);
            };
        };
        private Function<ActorMetaData, IMailboxFactory> localMailboxFactory = meta -> {
            return (ref) -> {
                throw new GeminiException("try create local meta={}, ref={}", meta, ref);
            };
        };
        private Function<ActorMetaData, IMailboxMiddleware> middlewareFactory = metaData -> new DefaultSwallowMiddlewareImpl();
        private Function<ActorMetaData, IMailboxDispatcher> dispatcherFactory = metaData -> new DefaultFastFailMailboxDispatcher();
        private boolean isNeedCreateMsg = false;
        private int mailboxQueueSize = 0;
        private int mailboxMaxCount = Integer.MAX_VALUE;

        Builder() {
        }

        /**
         * meta对应的角色信息。
         *
         * @param actorRole 角色信息。
         * @return builder。
         */
        public Builder actorRole(final String actorRole) {
            if (StringUtils.isEmpty(actorRole)) {
                throw new IllegalArgumentException("actorRole is empty!");
            }
            this.actorRole = actorRole;
            return this;
        }

        /**
         * actor对应的shard数量。
         *
         * @param shardNum > 0 标识shard模式actor; == 0 代表普通Actor。
         * @return builder。
         */
        public Builder shardNum(final int shardNum) {
            if (shardNum < 0) {
                throw new IllegalArgumentException("shardNum < 0!");
            }
            this.shardNum = shardNum;
            return this;
        }

        /**
         * Actor对应的class。
         *
         * @param clazz 目标class。
         * @return builder。
         */
        public Builder clazz(final Class<?> clazz) {
            if (clazz == null) {
                throw new IllegalArgumentException("clazz is null!");
            }
            final Constructor<?> constructor;
            try {
                constructor = clazz.getConstructor(ActorSystem.class, IActorRef.class);
            } catch (NoSuchMethodException e) {
                throw new GeminiException("class {} hasn't right constructor!", clazz, e);
            }
            this.actorFactory = (meta) -> {
                return (ref) -> {
                    try {
                        return (AbstractActor) constructor.newInstance(meta.getActorSystem(), ref);
                    } catch (InstantiationException | IllegalAccessException | InvocationTargetException e) {
                        throw new GeminiException(StringUtils.format("construct actor fail, ref={}", ref), e);
                    }
                };
            };
            return this;
        }

        /**
         * 配置actor对应的dispatcher。
         *
         * @param dispatcherName dispatcher名称，必须是基于ActorSystem预定义好的。
         * @return builder。
         */
        public Builder dispatcher(final String dispatcherName) {
            if (StringUtils.isEmpty(dispatcherName)) {
                throw new IllegalArgumentException("dispatcherName is empty!");
            }
            this.dispatcherFactory = (metaData -> {
                return metaData.getActorSystem().getDispatcher(dispatcherName);
            });
            return this;
        }

        /**
         * 配置actor对应的dispatcher。
         *
         * @param dispatcher dispatcher实例。
         * @return builder。
         */
        public Builder dispatcher(final IMailboxDispatcher dispatcher) {
            if (dispatcher == null) {
                throw new IllegalArgumentException("dispatcher is null;");
            }
            this.dispatcherFactory = (metaData -> dispatcher);
            return this;
        }

        /**
         * 配置Actor对象是否需要标记为create的消息才能拉起。
         *
         * @param isNeedCreateMsg true or false。
         * @return builder。
         */
        public Builder isNeedCreateMsg(final boolean isNeedCreateMsg) {
            this.isNeedCreateMsg = isNeedCreateMsg;
            return this;
        }

        /**
         * 配置Actor的邮箱长度。== 0代表无限长(慎用); > 0代表邮箱长度。
         *
         * @param size 大小。
         * @return builder。
         */
        public Builder mailboxQueueSize(final int size) {
            if (size < 0) {
                throw new IllegalArgumentException("size < 0!");
            }
            this.mailboxQueueSize = size;
            return this;
        }

        /**
         * 配置邮箱最多可以存在的上限数量，如果达到就回熔断新mailbox的创建。
         * 默认值：int类型最大值。
         *
         * @param count 数量。
         * @return builder。
         */
        public Builder mailboxMaxCount(final int count) {
            if (count <= 0) {
                throw new IllegalArgumentException("count <= 0!");
            }
            this.mailboxMaxCount = count;
            return this;
        }

        /**
         * 配置本地mailbox的构造工厂。
         *
         * @param factory meta -> IMailboxFactory 的工厂。
         * @return builder。
         */
        public Builder localMailboxFactory(Function<ActorMetaData, IMailboxFactory> factory) {
            if (factory == null) {
                throw new IllegalArgumentException("factory is null!");
            }
            this.localMailboxFactory = factory;
            return this;
        }

        /**
         * 配置本地mailbox中间件的构造工厂。
         *
         * @param middlewareFactory meta -> IMailboxMiddleware 的工厂。
         * @return builder。
         */
        public Builder middlewareFactory(Function<ActorMetaData, IMailboxMiddleware> middlewareFactory) {
            if (middlewareFactory == null) {
                throw new IllegalArgumentException("middlewareFactory is null!");
            }
            this.middlewareFactory = middlewareFactory;
            return this;
        }

        /**
         * 构造。
         *
         * @param actorSystem 目标的actor system。
         * @return meta data。
         */
        public ActorMetaData build(ActorSystem actorSystem) {
            if (this.actorRole == null) {
                throw new GeminiException("actorRole null!");
            }
            return new ActorMetaData(actorSystem, this);
        }
    }

    public static Builder newBuilder(final String actorRole, final int shardNum) {
        final Builder builder = new Builder();
        return builder.actorRole(actorRole).shardNum(shardNum);
    }
}

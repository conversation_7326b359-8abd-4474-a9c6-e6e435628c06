package com.yorha.common.resource.resservice.activity;

import com.google.common.collect.*;
import com.google.protobuf.Descriptors;
import com.yorha.common.constant.Constants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import javax.annotation.Nullable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yorha.proto.CommonEnum.ActivityUnitType;

/**
 * 活动配置
 */
public class ActivityResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(ActivityResService.class);

    private static final Map<String, ActivityUnitType> UNIT_TYPE_META_MAP;

    private final Map<Integer, ActivitySchedule> scheduleMap = Maps.newHashMap();

    /**
     * 任务池id-> 任务池
     * <p>
     * 如：五天乐->所有五天乐任务配置
     */
    private final Map<String, Map<Integer, ActivityTaskConf>> taskPoolMap = Maps.newHashMap();

    /**
     * 积分活动id -> 所有的宝箱id
     */
    private final Map<String, Map<Integer, ActivityScoreRewardTemplate>> scoreRewardMap = Maps.newHashMap();
    /**
     * 积分活动id -> 积分来源途径id
     */
    private final Map<String, Set<Integer>> scoreRewardPointsIds = Maps.newHashMap();

    private final Map<ActivityUnitType, Map<Integer, ActivityReserveServerTemplate>> zlcbRewardMap = Maps.newHashMap();
    /**
     * 排行活动奖励
     */
    private final Map<Integer, List<EventRankRewardsTemplate>> rankRewardMap = Maps.newHashMap();

    /**
     * key=折扣周卡月卡礼包id, val=周卡月卡礼包id
     */
    private final Map<Integer, Integer> weekMonthGoods = Maps.newHashMap();

    private Set<Integer> forceOffScheduleIds = Collections.emptySet();
    
    /**
     * pooId -> 筹码数量 -> 价格(金条数量)
     */
    private final Map<Integer, Map<Integer, Integer>> fpPriceMap = Maps.newHashMap();

    /**
     * activityTriggerId -> triggerParams
     */
    private Map<Integer, Map<CommonEnum.ActTriggerParamType, Integer>> actTriggerParams = new HashMap<>();
    /**
     * 节日bp actId->等级->所需积分
     */
    private Map<Integer, Map<Integer, Integer>> fbpUpLevelScore = new HashMap<>();

    /**
     * 道具回收配置 活动id -> 配置
     */
    private final Map<Integer, List<ActivityRecycleTemplate>> recycleMap = new HashMap<>();

    private final Map<Integer, TreeMap<Integer, List<IntPairType>>> rankIdRank2Reward = new HashMap<>();

    private final HashSet<Integer> launchItemSet = new HashSet<>();

    static {
        UNIT_TYPE_META_MAP = parseActivityUnitEnumMeta();
    }

    private static final Set<ActivityUnitType> COMMON_UNIT_TYPE = Sets.newHashSet(
            ActivityUnitType.AUT_TASK,
            ActivityUnitType.AUT_SCORE_RANK,
            ActivityUnitType.AUT_CHARGE_GOODS_CHAIN,
            ActivityUnitType.AUT_STORE,
            ActivityUnitType.AUT_TIMER_REWARD,
            ActivityUnitType.AUT_SCORE_REWARD
    );

    public ActivityResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (IntPairType item : getResHolder().getConstTemplate(ConstActivityTemplate.class).getPreActivitySynthesisiItemNumber()) {
            launchItemSet.add(item.getKey());
        }
        try {
            loadTasks();
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            throw new ResourceException("{}", e.getMessage(), e);
        }

        for (ActivityScoreRewardTemplate srt : getResHolder().getListFromMap(ActivityScoreRewardTemplate.class)) {
            scoreRewardMap.computeIfAbsent(srt.getUnitId(), k -> Maps.newHashMap())
                    .put(srt.getId(), srt);
            scoreRewardPointsIds.computeIfAbsent(srt.getUnitId(), i -> Sets.newHashSet())
                    .addAll(srt.getPointsTemplateIdsList());
        }

        for (ActivityReserveServerTemplate template : getResHolder().getListFromMap(ActivityReserveServerTemplate.class)) {
            zlcbRewardMap.computeIfAbsent(template.getUnitType(), k -> Maps.newHashMap());
            Map<Integer, ActivityReserveServerTemplate> tempMap = zlcbRewardMap.get(template.getUnitType());
            IntPairType pair = template.getLevelPairPair();
            if (pair.getKey() > pair.getValue()) {
                throw new ResourceException("活动表 activity_reserve_server id: {} levelPair left: {} > right: {}", template.getId(), pair.getKey(), pair.getValue());
            }
            for (int i = pair.getKey(); i <= pair.getValue(); i++) {
                tempMap.put(i, template);
            }
        }

        for (ActivityScheduleTemplate scheduleTemplate : getResHolder().getListFromMap(ActivityScheduleTemplate.class)) {
            scheduleMap.put(scheduleTemplate.getId(), new ActivitySchedule(scheduleTemplate));
        }

        Collection<ActivityForceOffScheduleTemplate> forceOffSchedules = getResHolder().getListFromMap(ActivityForceOffScheduleTemplate.class);
        for (ActivityForceOffScheduleTemplate forceOffSchedule : forceOffSchedules) {
            LOGGER.info("ActivityForceOffScheduleTemplate id={} reason={}", forceOffSchedule.getId(), forceOffSchedule.getReason());
        }
        this.forceOffScheduleIds = ImmutableSet.copyOf(forceOffSchedules.stream().map(ActivityForceOffScheduleTemplate::getId).collect(Collectors.toSet()));

        for (ActivityWeekmonthCardTemplate activityWeekmonthCardTemplate : getResHolder().getListFromMap(ActivityWeekmonthCardTemplate.class)) {
            final int discountId = activityWeekmonthCardTemplate.getWeekMonthCardCouponID();
            final int originId = activityWeekmonthCardTemplate.getGoodsId();
            // 没配
            if (discountId == 0) {
                continue;
            }
            this.weekMonthGoods.put(discountId, originId);
        }
        loadActTriggerParams();
        loadActRecycle();
        loadFbp();

        for (EventRankRewardsTemplate template : getResHolder().getListFromMap(EventRankRewardsTemplate.class)) {
            int rankId = template.getActivityRankId();
            if (rankId <= 0) {
                for (RankTemplate rankTemplate : getResHolder().getListFromMap(RankTemplate.class)) {
                    if (rankTemplate.getType() == template.getActivityRankType()) {
                        rankId = rankTemplate.getId();
                    }
                }
            }
            rankRewardMap.computeIfAbsent(rankId, k -> Lists.newArrayList())
                    .add(template);

            TreeMap<Integer, List<IntPairType>> treeMap = rankIdRank2Reward.computeIfAbsent(rankId, k -> Maps.newTreeMap());
            treeMap.put(template.getRewardRangePair().getKey(), template.getRewardPairList());
        }

    }

    private void loadActRecycle() throws ResourceException {
        for (ActivityRecycleTemplate activityRecycleTemplate : getResHolder().getListFromMap(ActivityRecycleTemplate.class)) {
            ActivityTemplate valueFromMap = getResHolder().findValueFromMap(ActivityTemplate.class, activityRecycleTemplate.getActivityId());
            if (valueFromMap == null) {
                throw new ResourceException("活动回收表有问题活动不存在 id={} activity={}", activityRecycleTemplate.getId(), activityRecycleTemplate.getActivityId());
            }
            List<ActivityRecycleTemplate> orDefault = recycleMap.getOrDefault(activityRecycleTemplate.getActivityId(), new LinkedList<>());
            if (activityRecycleTemplate.getRecycleItemPairList().isEmpty()) {
                throw new ResourceException("活动回收表有问题 id={} RecycleItemPair={}", activityRecycleTemplate.getId(), activityRecycleTemplate.getRecycleItemPairList());
            }
            orDefault.add(activityRecycleTemplate);
            recycleMap.put(activityRecycleTemplate.getActivityId(), orDefault);
        }
    }

    /**
     * 加载节日活动通行证
     */
    private void loadFbp() {
        for (FestivalBpTemplate bpTemplate : getResHolder().getListFromMap(FestivalBpTemplate.class)) {
            for (int levelScore : bpTemplate.getLevelScoreList()) {
                final FestivalBpLevelTemplate levelTemplate = getResHolder().getValueFromMap(FestivalBpLevelTemplate.class, levelScore);
                fbpUpLevelScore.computeIfAbsent(bpTemplate.getId(), key -> new HashMap<>()).put(levelTemplate.getCurLevel(), levelTemplate.getScore());
            }
        }
    }

    /**
     * 加载活动触发器相关参数
     */
    private void loadActTriggerParams() {
        final Map<Integer, ActTriggerParamTemplate> paramTemplateMap = getResHolder().getMap(ActTriggerParamTemplate.class);
        for (ActivityTriggerPoolTemplate actTriggerTemplate : getResHolder().getListFromMap(ActivityTriggerPoolTemplate.class)) {
            for (IntPairType pair : actTriggerTemplate.getParamPairList()) {
                final Map<CommonEnum.ActTriggerParamType, Integer> paramMap = actTriggerParams.computeIfAbsent(actTriggerTemplate.getId(), k -> new HashMap<>());
                // 加checkVaild
                final ActTriggerParamTemplate actTriggerParamTemplate = paramTemplateMap.get(pair.getKey());
                final CommonEnum.ActTriggerParamType paramType = actTriggerParamTemplate.getTriggerParamType();
                paramMap.put(paramType, pair.getValue());
            }
        }
    }

    public Map<Integer, Integer> getFpPriceMap(int poolId) {
        return this.fpPriceMap.get(poolId);
    }

    public Map<CommonEnum.ActTriggerParamType, Integer> getActTriggerParams(int triggerId) {
        return actTriggerParams.get(triggerId);
    }


    @Nullable
    public int getWeekMonthGoodsId(final int discountGoodsId) {
        return this.weekMonthGoods.getOrDefault(discountGoodsId, 0);
    }

    private void loadTasks() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        final JavaClassScanner scanner = new JavaClassScanner();
        // H_活动任务表里的所有template都加载一下
        List<Class<? extends IResTemplate>> taskTemplateClazzList = scanner.getSubTypesOf(TaskFivedaysTemplate.class.getPackage().getName(), IResTemplate.class)
                .stream()
                .filter(it ->
                        it.getAnnotation(ResXml.class) != null && it.getAnnotation(ResXml.class).excel().contains("活动任务")
                )
                .toList();
        for (Class<? extends IResTemplate> clazz : taskTemplateClazzList) {
            LOGGER.info("load activity task template: {}", clazz.getName());
            ResXml resXml = clazz.getAnnotation(ResXml.class);
            String xmlName = resXml.node();
            String key = xmlName.substring(0, xmlName.length() - 4);
            Collection<? extends IResTemplate> templates = getResHolder().getListFromMap(clazz);

            Method getTaskId = clazz.getMethod("getTaskId");
            Method getRewardPairList = clazz.getMethod("getRewardPairList");
            Method getScoreUnitId = null;
            Method getScoreNum = null;
            Method getRewardTriggerMailId = null;
            Method[] methods = clazz.getMethods();
            for (Method method : methods) {
                switch (method.getName()) {
                    case "getScoreUnitId":
                        getScoreUnitId = method;
                        break;
                    case "getScoreNum":
                        getScoreNum = method;
                        break;
                    case "getRewardTriggerMailId":
                        getRewardTriggerMailId = method;
                        break;
                    default:
                        break;
                }
            }

            for (IResTemplate t : templates) {
                Pair<String, Integer> rewardScore = null;
                if (getScoreUnitId != null && getScoreNum != null) {
                    String scoreUnitId = (String) getScoreUnitId.invoke(t);
                    if (StringUtils.isNotEmpty(scoreUnitId)) {
                        rewardScore = Pair.of(scoreUnitId, (Integer) getScoreNum.invoke(t));
                    }
                }
                int rewardTriggerMailId = 0;
                if (getRewardTriggerMailId != null) {
                    rewardTriggerMailId = (int) getRewardTriggerMailId.invoke(t);
                }
                taskPoolMap.computeIfAbsent(key, k -> Maps.newHashMap())
                        .put(t.getId(), new ActivityTaskConf(
                                t.getId(),
                                (Integer) getTaskId.invoke(t),
                                (List<IntPairType>) getRewardPairList.invoke(t),
                                rewardScore,
                                rewardTriggerMailId
                        ));
            }
        }
    }

    private static Map<String, ActivityUnitType> parseActivityUnitEnumMeta() {
        Map<String, ActivityUnitType> map = Maps.newHashMap();
        for (ActivityUnitType type : ActivityUnitType.values()) {
            Descriptors.EnumValueDescriptor descriptor = type.getValueDescriptor();
            String extName = descriptor.getOptions().getExtension(Option.name);
            // 无视大小写吧
            extName = extName.toLowerCase();
            if (StringUtils.isNotEmpty(extName)) {
                if (map.containsKey(extName)) {
                    throw new GeminiException("ActivityUnitType的name定义重复了，达咩. {} and {}", map.get(extName), type);
                }
                map.put(extName, type);
            }
        }
        return map;
    }

    public Map<Integer, Integer> getFbpLevelScore(int actId) {
        return fbpUpLevelScore.get(actId);
    }

    @Override
    public void checkValid() throws ResourceException {
        Set<Integer> oldActivities = oldActivities();

        for (ActivityTemplate activityTemplate : getResHolder().getListFromMap(ActivityTemplate.class)) {
            validateChildActivities(activityTemplate);
            validateTaskPool(activityTemplate);
            validateScoreReward(activityTemplate);
            validateActivityConfiguration(activityTemplate);
            validateActivityUnitTypes(activityTemplate);
            validateChargeGoodsChain(activityTemplate);
            validateNewActivityRules(oldActivities, activityTemplate);
        }

        // NOTE: 根据不同排期类型作校验
        checkSchedule();
        checkOnlineRewards();
        checkStore();
        checkLottery();
        checkWeekMonthCard();
        checkSecondQueue();
        checkServerLimit();
        checkScoreReward();
        checkFbp();
    }

    /**
     * 验证子活动配置
     */
    private void validateChildActivities(ActivityTemplate activityTemplate) throws ResourceException {
        List<Integer> childActIds = activityTemplate.getChildActivityIdListList();
        if (childActIds != null) {
            for (Integer childActId : childActIds) {
                ActivityTemplate childActivityTemplate = getResHolder().findValueFromMap(ActivityTemplate.class, childActId);
                if (childActivityTemplate == null) {
                    throw new ResourceException("活动 {} 子活动配置不存在 {}", activityTemplate.getId(), childActId);
                }
                if (childActivityTemplate.getLastingHours() + childActivityTemplate.getOpenOffsetHours() > activityTemplate.getLastingHours()) {
                    throw new ResourceException("子活动 {} 持续时间超过父活动持续时间 {}", childActId, activityTemplate.getId());
                }
                if (childActId == activityTemplate.getId()) {
                    throw new ResourceException("子活动 {} id和父活动一样，会死循环的.", childActId);
                }
            }
            if (!childActIds.isEmpty()) {
                // 有子活动，一定是父活动
                if (activityTemplate.getAutoExpire()) {
                    throw new ResourceException("父活动 {}, 不允许配置自动下架", activityTemplate.getId());
                }
            }
        }
    }

    /**
     * 验证任务池配置
     */
    private void validateTaskPool(ActivityTemplate activityTemplate) throws ResourceException {
        if (StringUtils.isNotEmpty(activityTemplate.getTaskPoolName())) {
            Map<Integer, ActivityTaskConf> taskPool = taskPoolMap.get(activityTemplate.getTaskPoolName());
            if (taskPool == null) {
                throw new ResourceException("活动任务池未配置 {}", activityTemplate.getTaskPoolName());
            }
            List<Integer> taskIds = activityTemplate.getActivityTaskIdsList();
            for (Integer taskId : taskIds) {
                ActivityTaskConf taskConf = taskPool.get(taskId);
                if (taskConf == null) {
                    throw new ResourceException("活动任务未配置 活动id:{} 任务id:{}", activityTemplate.getId(), taskId);
                }
                if (taskConf.getRewardTriggerMailId() > 0) {
                    MailTemplate mailTemplate = getResHolder().findValueFromMap(MailTemplate.class, taskConf.getRewardTriggerMailId());
                    if (mailTemplate == null) {
                        throw new ResourceException("活动任务配置的邮件id找不到配置 {} {} {}", activityTemplate.getTaskPoolName(), taskId, taskConf.getRewardTriggerMailId());
                    }
                }
                for (IntPairType item : taskConf.getReward()) {
                    ItemTemplate itemTemplate = getResHolder().findValueFromMap(ItemTemplate.class, item.getKey());
                    if (itemTemplate == null) {
                        throw new ResourceException("{} taskId={} itemId={} 奖励道具不存在", activityTemplate.getTaskPoolName(), taskConf.getTaskId(), item.getKey());
                    }
                    if (item.getValue() <= 0) {
                        throw new ResourceException("{} taskId={} itemId={} 奖励道具数量配置错误", activityTemplate.getTaskPoolName(), taskConf.getTaskId(), item.getKey());
                    }
                }
            }
        }
    }

    /**
     * 验证积分奖励配置
     */
    private void validateScoreReward(ActivityTemplate activityTemplate) throws ResourceException {
        if (StringUtils.isNotEmpty(activityTemplate.getScoreRewardUnitId())) {
            Map<Integer, ActivityScoreRewardTemplate> scoreRewardPool = scoreRewardMap.get(activityTemplate.getScoreRewardUnitId());
            if (scoreRewardPool == null) {
                throw new ResourceException("活动的积分内容未配置 活动id:{} 积分id:{}", activityTemplate.getId(), activityTemplate.getScoreRewardUnitId());
            }
        }
    }

    /**
     * 验证活动配置（怪物、商店、抽奖等）
     */
    private void validateActivityConfiguration(ActivityTemplate activityTemplate) throws ResourceException {
        if (activityTemplate.getSpecUnitType() == ActivityUnitType.AUT_FIGHT_MONSTER) {
            if (!getResHolder().getMap(ActivityMonsterTemplate.class).containsKey(activityTemplate.getId())) {
                throw new ResourceException("打野活动:{} 在activity_monster表里没配置:{}", activityTemplate.getId());
            }
        }
        // 活动的商店id不存在
        if (activityTemplate.getActivityMarketId() != 0) {
            if (!getResHolder().getMap(ActivityStoreTemplate.class).containsKey(activityTemplate.getActivityMarketId())) {
                throw new ResourceException("活动的商店未配置 活动id:{} 商店id:{}", activityTemplate.getId(), activityTemplate.getActivityMarketId());
            }
        }
        if (activityTemplate.getLotteryId() > 0) {
            if (getResHolder().findValueFromMap(ActivityLotteryTemplate.class, activityTemplate.getLotteryId()) == null) {
                throw new ResourceException("抽奖活动配置找不到 活动id:{} 抽奖id:{}", activityTemplate.getId(), activityTemplate.getLotteryId());
            }
        }
    }

    /**
     * 验证活动单元类型配置
     */
    private void validateActivityUnitTypes(ActivityTemplate activityTemplate) throws ResourceException {
        // 配置的unitType未实现
        String unitTypeStr = activityTemplate.getCommonUnitTypes();
        if (StringUtils.isNotEmpty(unitTypeStr)) {
            String[] unitNames = StringUtils.split(unitTypeStr.trim(), Constants.DOU_HAO);
            for (String unitName : unitNames) {
                if (!UNIT_TYPE_META_MAP.containsKey(unitName.toLowerCase())) {
                    throw new ResourceException("活动配置了未实现的 unitType 活动id={} unitName={}", activityTemplate.getId(), unitName);
                }
            }
        }

        List<ActivityUnitType> commonUnitTypes = parseCommonUnitTypes(activityTemplate);
        if (commonUnitTypes.size() > 1) {
            throw new ResourceException("活动id:{} commonUnitTypes字段只能配置一个类型", activityTemplate.getId());
        }
        for (ActivityUnitType commonUnitType : commonUnitTypes) {
            if (!COMMON_UNIT_TYPE.contains(commonUnitType)) {
                Descriptors.EnumValueDescriptor descriptor = commonUnitType.getValueDescriptor();
                String extName = descriptor.getOptions().getExtension(Option.name);
                throw new ResourceException("活动id:{} commonUnitTypes字段不允许配置:{}", activityTemplate.getId(), extName);
            }
            if (commonUnitType == ActivityUnitType.AUT_CHARGE_GOODS_CHAIN && activityTemplate.getSpecUnitType() == ActivityUnitType.AUT_CYCLE_CHARGE_GOODS) {
                throw new ResourceException("ChargeGoodsChain和CycleChargeGoods不允许同时出现 活动id:{}", activityTemplate.getId());
            }
        }
    }

    /**
     * 验证充值商品链配置
     */
    private void validateChargeGoodsChain(ActivityTemplate activityTemplate) throws ResourceException {
        List<ActivityUnitType> commonUnitTypes = parseCommonUnitTypes(activityTemplate);
        boolean isChargeUnit = commonUnitTypes.contains(ActivityUnitType.AUT_CHARGE_GOODS_CHAIN);
        boolean isCycleChargeUnit = activityTemplate.getSpecUnitType() == ActivityUnitType.AUT_CYCLE_CHARGE_GOODS;

        if (isChargeUnit || isCycleChargeUnit) {
            if (activityTemplate.getGoodsChainArrayList().isEmpty()) {
                throw new ResourceException("活动id:{} goodsChainArray字段为空，没有配置礼包链", activityTemplate.getId());
            }
            for (Integer chainId : activityTemplate.getGoodsChainArrayList()) {
                ChargeChainTemplate template = getResHolder().findValueFromMap(ChargeChainTemplate.class, chainId);
                if (template == null) {
                    throw new ResourceException("活动id:{} goodsChainArray={} 配置的礼包链不存在", activityTemplate.getId(), activityTemplate.getGoodsChainArrayList(), chainId);
                }
                for (Integer goodsId : template.getChargeChainGoodsIdList()) {
                    ChargeGoodsTemplate goodsTemplate = getResHolder().findValueFromMap(ChargeGoodsTemplate.class, goodsId);
                    if (goodsTemplate == null) {
                        throw new ResourceException("支付表 charge_chain id={} 礼包链中含未知子礼包 goodsId={}", chainId, goodsId);
                    }
                    if (isChargeUnit && goodsTemplate.getGoodsType() != CommonEnum.GoodsType.GT_ACTIVITY) {
                        throw new ResourceException("支付表 charge_chain id={} goodsId={} goodsType={} 配错了，应该配成活动常规礼包，对应的活动id:{}",
                                chainId, goodsId, goodsTemplate.getGoodsType(), activityTemplate.getId());
                    }
                    if (isCycleChargeUnit && goodsTemplate.getGoodsType() == CommonEnum.GoodsType.GT_ACTIVITY) {
                        throw new ResourceException("支付表 charge_chain id={} goodsId={} goodsType={} 配错了，不应该配成活动常规礼包，对应的活动id:{}",
                                chainId, goodsId, goodsTemplate.getGoodsType(), activityTemplate.getId());
                    }
                }
            }
        }
    }

    /**
     * 验证新活动规则
     */
    private void validateNewActivityRules(Set<Integer> oldActivities, ActivityTemplate activityTemplate) throws ResourceException {
        // 新老做法校验
        if (isNewActivity(oldActivities, activityTemplate.getId())) {
            List<ActivityUnitType> commonUnitTypes = parseCommonUnitTypes(activityTemplate);
            // 所有新活动只能配一个unitType，且需要配置在specUnitType字段
            if (!commonUnitTypes.isEmpty()) {
                if (activityTemplate.getSpecUnitType() != null && activityTemplate.getSpecUnitType() != ActivityUnitType.AUT_NONE) {
                    throw new ResourceException("活动id:{} commonUnitTypes字段和specUnitType字段只允许配一处", activityTemplate.getId());
                }
                ActivityUnitType activityUnitType = commonUnitTypes.get(0);
                // 只有一些老的unit type可以配在commonUnitTypes,其他的都只能配置在specUnitType字段
                if (!commonUnitType.contains(activityUnitType)) {
                    throw new ResourceException("活动id:{} 不允许commonUnitTypes字段，请配到specUnitType字段中", activityTemplate.getId());
                }
            }
            checkFatherAct(activityTemplate);
        }
    }

    /**
     * 下面这些允许配在commonUnitTypes，因为客户端有写死的逻辑
     */
    static Set<ActivityUnitType> commonUnitType = Sets.newHashSet(
            ActivityUnitType.AUT_TASK,
            ActivityUnitType.AUT_SCORE_REWARD,
            ActivityUnitType.AUT_SCORE_RANK,
            ActivityUnitType.AUT_STORE
    );

    /**
     * 父活动，common，spec unit为空，BestCommander除外改不动
     */
    private void checkFatherAct(ActivityTemplate activityTemplate) throws ResourceException {
        if (activityTemplate.getChildActivityIdListList().isEmpty()) {
            return;
        }

        if (activityTemplate.getSpecUnitType() == ActivityUnitType.AUT_NONE) {
            return;
        }
        // todo zeo 赛季版本的最强指挥官上了，老的强制下架了，这里可以去掉
        if (activityTemplate.getSpecUnitType() == ActivityUnitType.AUT_BEST_COMMANDER) {
            return;
        }
        throw new ResourceException("活动id:{} 作为一个父活动 commonUnitTypes，specUnitType 需要为空", activityTemplate.getId());
    }

    private Set<Integer> oldActivities() {
        return Sets.newHashSet(100001, 10000101, 10000102, 10000103, 10000104, 10000105, 200001, 10000302,
                200003, 400001, 400002, 400003, 100004, 10000401, 10000402, 10000403, 10000404, 100007, 10000701,
                10000702, 10000703, 10000704, 10000705, 10000706, 10000707, 10000708, 10000709, 10000710, 10000711,
                10000712, 10000713, 10000714, 10000715, 10000716, 10000717, 10000718, 10000719, 100008, 10000902,
                10000903, 10000904, 10000905, 10000906, 10000907, 10001001, 10001002, 10001003, 10001004, 10001005,
                10001006, 100011, 10001101, 10001102, 10001103, 10001104, 10001105, 10001106, 10001107, 10001108,
                10001109, 10001110, 10001111, 100012, 10001201, 10001202, 10001203, 10001204, 10001205, 10001206,
                10001207, 10001208, 10001209, 10001210, 10001211, 100013, 10001301, 10001302, 10001303, 100014, 100015,
                100016, 10001701, 10001702, 10001703, 10001704, 10001706, 10001707, 10001708, 10001801, 10001802, 100019,
                10001901, 10001902, 10001903, 10001904, 10001905, 10001906, 10001907, 10001908, 10001909, 10001910,
                10001911, 10001912, 10001913, 10001914, 10001915, 10001916, 10001917, 100020, 10002001, 10002002,
                10002003, 10002004, 10002005, 10002006, 10002007, 10002008, 10002009, 10002010, 10002011, 10002012,
                10002013, 10002014, 10002015, 10002016, 100021, 10002202, 10002203, 10002204, 10002205, 10002206,
                400014, 10002302, 10002303, 10002304, 10002305, 10002306, 10002307, 401091, 401092, 401093, 401094,
                401101, 401102, 401103, 401104, 401111, 401112, 401113, 401114, 401121, 401122, 401123, 401124, 50010001,
                50010002, 50010003, 50010004, 50020001, 50020002, 50020003, 50020004, 50020005, 50030001, 50030002,
                50030003, 50030004, 50040001, 50040002, 50040003, 50040004, 50050002, 50050003, 50050004, 50060002,
                50060003, 50060004, 10002401, 10002402, 10002403, 10002501, 10002502, 10002503, 100026, 10002701,
                10002702, 10002703, 100028, 100029, 501300101, 501300102, 501300103, 501300201, 501300202, 501300203,
                501300301, 501300302, 501300303, 50050006, 50050007, 50050008, 10001112, 10001113, 10001114, 10001115,
                50130001, 50130002, 50130003, 50130004, 50130005, 50010005, 50010006, 50010007, 50010008, 50050010,
                50050011, 50050012, 50060006, 50060007, 50060008, 50060010, 50060011, 50060012, 10003301, 10003302,
                10003303, 10003401, 10003402, 10003403, 100030, 10003101, 10003102, 10003103, 100032, 400025, 400026,
                400027, 401125, 401126, 401127, 401128, 401129, 401130, 401131, 401132, 401133, 401134, 401135, 405001,
                400600, 102040, 10204001, 10204002, 10204003, 10204004, 10204005, 406000, 40700001, 40700002, 40700003,
                40900001, 40900002, 100030002, 100030003, 100030004, 100030005, 100030006, 10005, 100050001, 10006,
                100060001, 10007, 10008, 100080001, 10009, 10013, 100130001, 101010002, 101010003, 101010004, 101010005,
                101010006, 101010007, 101020002, 101020003, 101020004, 101020005, 101020006, 101020007, 101030002,
                101030003, 101030004, 101030005, 101030006, 101030007, 10203, 102030001, 102030002, 102030003,
                102030004, 102030005, 10204, 20001, 200010001, 20002, 200020001, 20003, 200030001, 20004, 200040001,
                20005, 200050001, 20101, 20102, 20103, 202010001, 202010002, 202010003, 202020001, 202020002, 202020003,
                202030001, 202030002, 202030003, 202040001, 202040002, 202040003, 20302, 203030001, 203030002, 203030003,
                20304, 203050001, 203050002, 203050003, 203060001, 203060002, 203060003, 20307, 20401, 204010001,
                204010002, 204010003, 204010004, 20402, 204020001, 204020002, 204020003, 204020004, 20403, 204030001,
                204030002, 204030003, 204030004, 20404, 204040001, 204040002, 204040003, 204040004);
    }

    private boolean isNewActivity(Set<Integer> oldActivities, int actId) {
        return !oldActivities.contains(actId);
    }

    private void checkServerLimit() throws ResourceException {
        for (ActivityServerlimitTemplate template : getResHolder().getListFromMap(ActivityServerlimitTemplate.class)) {
            if (getResHolder().findValueFromMap(ActivityScheduleTemplate.class, template.getId()) == null) {
                throw new ResourceException("活动区服限制表 id={} 在活动排期表中不存在", template.getId());
            }
            if (template.getOpenType() == CommonEnum.ActivityZoneOperateType.AZOT_NONE) {
                throw new ResourceException("活动区服限制表 id={} openType不能为空", template.getId());
            }
            if (template.getServerListList().isEmpty()) {
                throw new ResourceException("活动区服限制表 id={} serverListList不能为空", template.getId());
            }
            if (template.getAffecttimeEndDt().before(template.getAffecttimeBeginDt())) {
                throw new ResourceException("活动区服限制表 id={} 结束时间小于开始时间 affectTime_beginDt={} affectTime_endDt={}",
                        template.getId(), template.getAffecttimeBeginDt(), template.getAffecttimeEndDt());
            }
        }
    }

    private void checkSecondQueue() throws ResourceException {
        ConstActivityTemplate constTemplate = getResHolder().getConstTemplate(ConstActivityTemplate.class);
        ensureGoodsIsType("第二队列解锁道具", constTemplate.getSecondQueueGoodsId(), CommonEnum.GoodsType.GT_ACT_SECOND_QUEUE);
    }

    private void checkWeekMonthCard() throws ResourceException {
        for (ActivityWeekmonthCardTemplate template : getResHolder().getListFromMap(ActivityWeekmonthCardTemplate.class)) {
            ensureGoodsIsType("activity_weekmonth_card", template.getGoodsId(), CommonEnum.GoodsType.GT_WEEK_MONTH_CARD);
            if (template.getWeekMonthCardCouponID() != 0) {
                ensureGoodsIsType("activity_weekmonth_card", template.getWeekMonthCardCouponID(), CommonEnum.GoodsType.GT_DISCOUNT_WEEK_MONTH_CARD);
            }

            ensureActivitySpecUnitType(template.getActivityId(), ActivityUnitType.AUT_WEEK_MONTH_CARD);
        }
        ConstActivityTemplate constTemplate = getResHolder().getConstTemplate(ConstActivityTemplate.class);
        if (getResHolder().findValueFromMap(ItemTemplate.class, constTemplate.getWeekMonthCardCouponID()) == null) {
            throw new ResourceException("活动常量表 weekMonthCardCouponID 无对应道具id={}", constTemplate.getWeekMonthCardCouponID());
        }
    }

    private void checkLottery() throws ResourceException {
        for (ActivityLotteryTemplate lotteryTemplate : getResHolder().getListFromMap(ActivityLotteryTemplate.class)) {
            for (Integer drawId : lotteryTemplate.getDrawIdsList()) {
                if (!getResHolder().hasTemplate(LotteryDrawTemplate.class, drawId)) {
                    throw new ResourceException("抽奖活动抽奖配置找不到： {} {}", lotteryTemplate.getId(), drawId);
                }
            }
            if (lotteryTemplate.getStageRewardIdsList() != null) {
                for (Integer stageRewardId : lotteryTemplate.getStageRewardIdsList()) {
                    if (!getResHolder().hasTemplate(LotteryStageRewardTemplate.class, stageRewardId)) {
                        throw new ResourceException("抽奖活动阶段奖励配置找不到： {} {}", lotteryTemplate.getId(), stageRewardId);
                    }
                }
            }
        }
    }

    private void checkStore() throws ResourceException {
        // 活动的商品不存在
        for (ActivityStoreTemplate value : getResHolder().getMap(ActivityStoreTemplate.class).values()) {
            for (Integer goodId : value.getGoodsIdList()) {
                if (!getResHolder().getMap(ActivityStoreGoodsTemplate.class).containsKey(goodId)) {
                    throw new ResourceException("活动商店的商品未配置 商店id:{} 商品id:{}", value.getId(), goodId);
                }
            }
        }
    }

    private void checkSchedule() throws ResourceException {
        Collection<ActivityScheduleTemplate> scheduleTemplates = getResHolder().getListFromMap(ActivityScheduleTemplate.class);
        for (ActivityScheduleTemplate scheduleTemplate : scheduleTemplates) {
            CommonEnum.ActivityOpenType openType = scheduleTemplate.getOpenType();
            long intervalMs = 0;
            if (!isNotLoopSchedule(openType)) {
                ActivityLoop activityLoop = ActivityLoop.of(scheduleTemplate.getLoopType());
                intervalMs = activityLoop.intervalMs(scheduleTemplate.getLoopCycle());
                if (intervalMs <= 0) {
                    throw new ResourceException("活动排期循环类型的循环周期<0s，这不合理. 排期id=", scheduleTemplate.getId());
                }
            } else {
                if (scheduleTemplate.getActivityId() <= 0) {
                    throw new ResourceException("活动排期id={} 活动id={}没配", scheduleTemplate.getId(), scheduleTemplate.getActivityId());
                }
            }
            if (scheduleTemplate.getActivityId() > 0) {
                ActivityTemplate at = ensureActivityTemplateExist(scheduleTemplate.getActivityId());
                ensureActivityLastingDurationLessThanLoopInterval(scheduleTemplate, intervalMs, at);
            } else if (CollectionUtils.isNotEmpty(scheduleTemplate.getActivitySeqPairList())) {
                for (IntPairType actPair : scheduleTemplate.getActivitySeqPairList()) {
                    final int activityId = actPair.getKey();
                    ActivityTemplate at = ensureActivityTemplateExist(activityId);
                    ensureActivityLastingDurationLessThanLoopInterval(scheduleTemplate, intervalMs, at);
                }
            } else {
                throw new ResourceException("活动排期对应的活动配置找不到. 排期id={}", scheduleTemplate.getId());
            }
            switch (openType) {
                case AOT_NONE:
                case AOT_FROM_ZONE_OPEN:
                case AOT_FROM_PLAYER_CREATE:
                    break;
                case AOT_ON_DATE:
                    if (scheduleTemplate.getDateForOpenDt() == null) {
                        throw new ResourceException("特定日期开放的活动排期没有配置指定日期！ 排期id={}", scheduleTemplate.getId());
                    }
                    break;
                case AOT_ON_DATE_LOOP:
                case AOT_ON_DATE_ZONE_LOOP:
                    ensureDateForOpenExist(scheduleTemplate);
                    ensureLoopTypeExist(scheduleTemplate);
                    ensureLoopCycleExist(scheduleTemplate);
                    break;
                case AOT_ZONE_LOOP:
                    ensureLoopTypeExist(scheduleTemplate);
                    ensureLoopCycleExist(scheduleTemplate);
                    break;
                default:
                    break;
            }
            if (scheduleTemplate.getBanInsertPeriodPair() != null) {
                if (scheduleTemplate.getBanInsertPeriodPair().getKey() > scheduleTemplate.getBanInsertPeriodPair().getValue()
                        || scheduleTemplate.getBanInsertPeriodPair().getValue() <= 0) {
                    throw new ResourceException("禁止中途插入时段配置错误 排期id={} banInsertPeriod={}", scheduleTemplate.getId(), scheduleTemplate.getBanInsertPeriodPair());
                }
            }
        }
    }

    /**
     * 校验节日通行证活动
     */
    private void checkFbp() throws ResourceException {
        final Map<Integer, FestivalBpTemplate> bpMap = getResHolder().getMap(FestivalBpTemplate.class);
        final Map<Integer, FestivalBpPoolTemplate> bpPoolMap = getResHolder().getMap(FestivalBpPoolTemplate.class);
        final Map<Integer, FestivalBpLevelTemplate> bpPoolLevelMap = getResHolder().getMap(FestivalBpLevelTemplate.class);
        final Map<Integer, FpRewardPoolTemplate> rewardPoolMap = getResHolder().getMap(FpRewardPoolTemplate.class);
        for (ActivityTemplate act : getResHolder().getListFromMap(ActivityTemplate.class)) {
            if (act.getSpecUnitType() != ActivityUnitType.AUT_FEASTIVAL_BP) {
                continue;
            }
            // 每个节日通行证活动，得配置活动配置
            if (!bpMap.containsKey(act.getId())) {
                throw new ResourceException("活动id={}没有在J_节日活动，festival_bp中没有相关配置哟", act.getId());
            }
            // 积分道具校验
            final FestivalBpTemplate bpTemplate = bpMap.get(act.getId());
            if (getResHolder().findValueFromMap(ItemTemplate.class, bpTemplate.getScoreItem()) == null) {
                throw new ResourceException("活动id={}在J_节日活动中配置的积分道具{}不存在", act.getId(), bpTemplate.getScoreItem());
            }
            // 补发邮件校验
            if (getResHolder().findValueFromMap(MailTemplate.class, bpTemplate.getMailId()) == null) {
                throw new ResourceException("活动id={}在J_节日活动中配置的补发邮件{}不存在", act.getId(), bpTemplate.getMailId());
            }
            // 通行证校验
            int levelCount = -1;
            for (int bpId : bpTemplate.getBpListList()) {
                if (!bpPoolMap.containsKey(bpId)) {
                    throw new ResourceException("活动id={}在J_节日活动中，{}在festival_bp_pool中不存在", act.getId(), bpId);
                }
                // 通行证价格校验
                final FestivalBpPoolTemplate bpPoolTemplate = bpPoolMap.get(bpId);
                if (bpPoolTemplate.getType() == CommonEnum.FestivalBpType.FBPT_GOLD) {
                    if (bpPoolTemplate.getPrice() <= 0) {
                        throw new ResourceException("J_节日活动，bp{}在festival_bp_pool金条价格{}非法", bpPoolTemplate.getId(), bpPoolTemplate.getPrice());
                    }
                }
                if (bpPoolTemplate.getType() == CommonEnum.FestivalBpType.FBPT_GOODS) {
                    if (bpPoolTemplate.getGoodsId() <= 0) {
                        throw new ResourceException("J_节日活动，bp{}在festival_bp_pool礼包id{}非法", bpPoolTemplate.getId(), bpPoolTemplate.getGoodsId());
                    }
                    final ChargeGoodsTemplate goodsTemplate = getResHolder().findValueFromMap(ChargeGoodsTemplate.class, bpPoolTemplate.getGoodsId());
                    if (goodsTemplate == null) {
                        throw new ResourceException("J_节日活动，bp{}在festival_bp_pool礼包{}在礼包表里不存在", bpPoolTemplate.getId(), bpPoolTemplate.getGoodsId());
                    }
                }
                if (levelCount < 0) {
                    levelCount = bpPoolTemplate.getLevelRewardList().size();
                }
                if (levelCount != bpPoolTemplate.getLevelRewardList().size()) {
                    throw new ResourceException("J_节日活动，bp{}在festival_bp_pool等级奖励数量与本活动其他bp不同", bpPoolTemplate.getId());
                }

                for (int levelReward : bpPoolTemplate.getLevelRewardList()) {
                    final FpRewardPoolTemplate rewardPoolTemplate = rewardPoolMap.get(levelReward);
                    if (rewardPoolTemplate == null) {
                        throw new ResourceException("J_节日活动，bp{}在festival_bp_pool等级奖励不存在{}", bpPoolTemplate.getId(), levelReward);
                    }
                    for (IntPairType item : rewardPoolTemplate.getItemsPairList()) {
                        if (getResHolder().findValueFromMap(ItemTemplate.class, item.getKey()) == null) {
                            throw new ResourceException("J_节日活动中fp_reward_pool中{}配置的道具{}不存在", levelReward, item.getKey());
                        }
                    }
                }

            }
            // 通行证积分等级校验
            for (int bpLevelId : bpTemplate.getLevelScoreList()) {
                if (!bpPoolLevelMap.containsKey(bpLevelId)) {
                    throw new ResourceException("活动id={}没有在J_节日活动，level {} 在festival_bp_level中没有相关配置哟", act.getId(), bpLevelId);
                }
                final FestivalBpLevelTemplate bpLevelTemplate = bpPoolLevelMap.get(bpLevelId);
                if (bpLevelTemplate.getCurLevel() < 0) {
                    throw new ResourceException("在J_节日活动，在festival_bp_level中{}等级非法", act.getId(), bpLevelTemplate.getCurLevel());
                }

                if (bpLevelTemplate.getCurLevel() >= levelCount) {
                    throw new ResourceException("在J_节日活动，在festival_bp_level中{}等级非法", act.getId(), bpLevelTemplate.getCurLevel());
                }
            }
        }
    }

    private void checkScoreReward() throws ResourceException {
        Map<String, List<Integer>> checkMap = new HashMap<>();
        for (ActivityScoreRewardTemplate srt : getResHolder().getListFromMap(ActivityScoreRewardTemplate.class)) {
            List<Integer> pointList = checkMap.get(srt.getUnitId());

            if (pointList == null) {
                checkMap.put(srt.getUnitId(), srt.getPointsTemplateIdsList());
                continue;
            }

            if (!pointList.equals(srt.getPointsTemplateIdsList())) {
                throw new ResourceException("H_活动表 activity_score_reward 中相同unitId 需要配置相同的 pointsTemplateIds {} {}", srt.getId(), srt.getUnitId());
            }

        }
    }

    private static void ensureLoopCycleExist(ActivityScheduleTemplate scheduleTemplate) throws ResourceException {
        if (scheduleTemplate.getLoopCycle() <= 0) {
            throw new ResourceException("活动排期的配置缺失，需要配置loopCycle 排期id={}", scheduleTemplate.getId());
        }
    }

    private static void ensureLoopTypeExist(ActivityScheduleTemplate scheduleTemplate) throws ResourceException {
        if (StringUtils.isEmpty(scheduleTemplate.getLoopType()) || ActivityLoop.of(scheduleTemplate.getLoopType()) == null) {
            throw new ResourceException("活动排期的配置缺失，需要配置loopType 排期id={}", scheduleTemplate.getId());
        }
    }

    private static void ensureDateForOpenExist(ActivityScheduleTemplate scheduleTemplate) throws ResourceException {
        if (scheduleTemplate.getDateForOpenDt() == null) {
            throw new ResourceException("活动排期的配置缺失，需要配置dateForOpen 排期id={}", scheduleTemplate.getId());
        }
    }

    private static void ensureActivityLastingDurationLessThanLoopInterval(
            ActivityScheduleTemplate scheduleTemplate,
            long intervalMs,
            ActivityTemplate at
    ) throws ResourceException {
        if (intervalMs > 0 && TimeUnit.HOURS.toMillis(at.getLastingHours()) > intervalMs) {
            throw new ResourceException("活动排期的活动持续时间大于循环周期，这不合理吧。 排期id={} 活动id={} 持续={}h 周期={}h",
                    scheduleTemplate.getId(), at.getId(), at.getLastingHours(), TimeUnit.MILLISECONDS.toHours(intervalMs));
        }
    }

    private ActivityTemplate ensureActivityTemplateExist(int activityId) throws ResourceException {
        ActivityTemplate at = getResHolder().findValueFromMap(ActivityTemplate.class, activityId);
        if (at == null) {
            throw new ResourceException("活动配置不存在 {}", activityId);
        }
        return at;
    }

    private void checkOnlineRewards() throws ResourceException {
        for (ActivityOnlineTemplate template : getResHolder().getListFromMap(ActivityOnlineTemplate.class)) {
            int mailId = template.getMailId();
            if (getResHolder().findValueFromMap(MailTemplate.class, mailId) == null) {
                throw new ResourceException("持续在线奖励 id={}, 未知mailId={}", template.getId(), mailId);
            }

            for (IntPairType awardPair : template.getAwardPairList()) {
                int itemId = awardPair.getKey();
                int itemCnt = awardPair.getValue();
                if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                    throw new ResourceException("持续在线奖励 id={}, 奖励中存在未知道具id={}", template.getId(), itemId);
                }
                if (itemCnt <= 0) {
                    throw new ResourceException("持续在线奖励 id={}, 奖励中存在非正数数量={}", template.getId(), itemCnt);
                }
            }
        }
    }

    public List<ActivitySchedule> allActivitySchedule() {
        return ImmutableList.copyOf(scheduleMap.values());
    }

    public ActivityTemplate getActivityTemplate(int activityId) {
        return getResHolder().getValueFromMap(ActivityTemplate.class, activityId);
    }

    public ActivityScheduleTemplate getScheduleTemplate(int actScheduleId) {
        return getResHolder().getValueFromMap(ActivityScheduleTemplate.class, actScheduleId);
    }

    public ActivityTaskConf getTaskConf(String taskPoolName, int taskId) {
        return taskPoolMap.get(taskPoolName).get(taskId);
    }

    public ActivityScoreRewardTemplate findScoreRewardTemplate(String scoreId, int boxId, int cityLevel, int serverOpenDay) {
        LOGGER.info("ActivityResService findScoreRewardTemplate {} {} {} {}", scoreId, boxId, cityLevel, serverOpenDay);
        Map<Integer, ActivityScoreRewardTemplate> boxGroup = scoreRewardMap.get(scoreId);
        if (boxGroup == null) {
            LOGGER.warn("ActivityResService findScoreRewardTemplate error scoreId {} {} {} {}", scoreId, boxId, cityLevel, serverOpenDay);
            return null;
        }
        ActivityScoreRewardTemplate scoreReward = boxGroup.get(boxId);
        if (scoreReward == null) {
            LOGGER.warn("ActivityResService findScoreRewardTemplate error boxId {} {} {} {}", scoreId, boxId, cityLevel, serverOpenDay);
            return null;
        }
        IntPairType cityLevelPair = scoreReward.getCityLevelPair();
        if ((cityLevel < cityLevelPair.getKey()) || (cityLevel > cityLevelPair.getValue())) {
            LOGGER.warn("ActivityResService findScoreRewardTemplate error cityLevel {} {} {} {}", scoreId, boxId, cityLevel, serverOpenDay);
            return null;
        }
        IntPairType serverOpenDayPair = scoreReward.getServerOpenDayPair();
        if ((serverOpenDay < serverOpenDayPair.getKey()) || (serverOpenDay > serverOpenDayPair.getValue())) {
            LOGGER.warn("ActivityResService findScoreRewardTemplate error serverOpenDay {} {} {} {}", scoreId, boxId, cityLevel, serverOpenDay);
            return null;
        }
        LOGGER.info("ActivityResService findScoreRewardTemplate {} {} {}", scoreReward.getId(), cityLevel, serverOpenDay);
        return scoreReward;
    }

    public ImmutableSet<Integer> findAllBoxIds(String scoreId) {
        return ImmutableSet.copyOf(scoreRewardMap.get(scoreId).keySet());
    }

    public Map<Integer, ActivityScoreRewardTemplate> findAllBox(String scoreId) {
        return Collections.unmodifiableMap(scoreRewardMap.get(scoreId));
    }

    public Set<Integer> getScoreRewardPointsIds(String scoreId) {
        return scoreRewardPointsIds.getOrDefault(scoreId, Collections.emptySet());
    }

    public static ActivityUnitType getUnitTypeByName(String name) {
        ActivityUnitType type = UNIT_TYPE_META_MAP.get(name.toLowerCase());
        if (type == null) {
            throw new GeminiException("activity unit type not recognized. {}", name);
        }
        return type;
    }

    public static List<ActivityUnitType> parseCommonUnitTypes(ActivityTemplate activityTemplate) {
        if (StringUtils.isEmpty(activityTemplate.getCommonUnitTypes())) {
            return Collections.emptyList();
        }
        String[] unitTypeStrArray = activityTemplate.getCommonUnitTypes().split(",");
        return Arrays.stream(unitTypeStrArray).map(ActivityResService::getUnitTypeByName).collect(Collectors.toList());
    }

    public ActivityReserveServerTemplate getZlcbRewardTemplate(ActivityUnitType unitType, int level) {
        return zlcbRewardMap.get(unitType).getOrDefault(level, null);
    }

    public List<EventRankRewardsTemplate> getRankReward(CommonEnum.RankType rankType) {
        return rankRewardMap.getOrDefault(rankType.getNumber(), null);
    }

    public List<EventRankRewardsTemplate> getRankReward(int rankId) {
        return rankRewardMap.getOrDefault(rankId, Collections.emptyList());
    }

    public static boolean isNotLoopSchedule(CommonEnum.ActivityOpenType openType) {
        return openType == CommonEnum.ActivityOpenType.AOT_FROM_ZONE_OPEN
                || openType == CommonEnum.ActivityOpenType.AOT_FROM_PLAYER_CREATE
                || openType == CommonEnum.ActivityOpenType.AOT_ON_DATE;
    }

    public static boolean isServerOpenLimitOk(ActivityScheduleTemplate template, long zoneOpenDays) {
        return isServerOpenLimitOk(template.getServerOpenLimitPairList(), zoneOpenDays);
    }

    public static boolean isServerOpenLimitOk(List<IntPairType> range, long zoneOpenDays) {
        if (range == null || range.isEmpty()) {
            return true;
        }
        for (IntPairType pair : range) {
            if (zoneOpenDays >= pair.getKey() && zoneOpenDays <= pair.getValue()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isPlayerLevelOk(ActivityScheduleTemplate template, int playerLevel) {
        return isPlayerLevelOk(template.getRequiredPlayerLevelPairList(), playerLevel);
    }

    public static boolean isPlayerLevelOk(List<IntPairType> range, int playerLevel) {
        if (range == null || range.isEmpty()) {
            return true;
        }
        for (IntPairType pair : range) {
            if (playerLevel >= pair.getKey() && playerLevel <= pair.getValue()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isZoneIdOk(ActivityScheduleTemplate template, int zoneId, Instant now) {
        ActivityServerlimitTemplate zoneLimitTemplate = ResHolder.findTemplate(ActivityServerlimitTemplate.class, template.getId());
        // 这个排期不受区服限制
        if (zoneLimitTemplate == null) {
            return true;
        }

        Date nowDate = Date.from(now);
        // 只要时间条件不满足，则所有区服都取getOpenType的反逻辑
        boolean isTimeMatch = zoneLimitTemplate.getAffecttimeBeginDt().before(nowDate) && zoneLimitTemplate.getAffecttimeEndDt().after(nowDate);
        if (zoneLimitTemplate.getOpenType() == CommonEnum.ActivityZoneOperateType.AZOT_OPEN) {
            // serverListList是白名单
            if (isTimeMatch) {
                // 时间条件满足，当前zone白名单命中，开启
                return zoneLimitTemplate.getServerListList().contains(zoneId);
            } else {
                // 时间条件不满足，则白名单为空，所有zone都不开启
                return false;
            }
        } else if (zoneLimitTemplate.getOpenType() == CommonEnum.ActivityZoneOperateType.AZOT_CLOSE) {
            // serverListList是黑名单
            if (isTimeMatch) {
                // 时间条件满足，当前zone黑名单命中，不开启
                return !zoneLimitTemplate.getServerListList().contains(zoneId);
            } else {
                // 时间条件不满足，则黑名单为空，所有zone都开启
                return true;
            }
        } else {
            return false;
        }
    }

    public Map<String, Map<Integer, ActivityTaskConf>> getTaskPoolMap() {
        return taskPoolMap;
    }

    private void ensureGoodsIsType(String signature, int goodsId, CommonEnum.GoodsType goodsType) throws ResourceException {
        ChargeGoodsTemplate goodsTemplate = getResHolder().findValueFromMap(ChargeGoodsTemplate.class, goodsId);
        if (goodsTemplate == null) {
            throw new ResourceException("{} 找不到支付礼包配置 礼包Id={} ", signature, goodsId);
        }
        if (goodsTemplate.getGoodsType() != goodsType) {
            throw new ResourceException("{} 支付礼包类型不匹配 礼包Id={} 应为={} 实为={}", signature, goodsId, goodsType, goodsTemplate.getGoodsType());
        }
    }

    private void ensureActivitySpecUnitType(final int activityId, final ActivityUnitType unitType) throws ResourceException {
        final ActivityTemplate template = ensureActivityTemplateExist(activityId);
        if (template.getSpecUnitType() != unitType) {
            throw new ResourceException("活动表 activityId={} 配置的特定逻辑单元 应为={} 实为={}", activityId, unitType, template.getSpecUnitType());
        }
    }

    public Set<Integer> getForceOffScheduleIds() {
        return forceOffScheduleIds;
    }

    public boolean verifyLaunchItem(int itemId) {
        if (ResHolder.getInstance().findValueFromMap(ItemTemplate.class, itemId) == null) {
            return false;
        }
        return launchItemSet.contains(itemId);
    }

    public static class ActivityCalendar {
        public final ActivitySchedule.Cell cell;
        public final int scheduleId;

        public ActivityCalendar(ActivitySchedule.Cell cell, int scheduleId) {
            this.cell = cell;
            this.scheduleId = scheduleId;
        }

        public boolean canShowInCalendar(Instant now) {
            if (!cell.template.getShowCalendar()) {
                return false;
            }
            if (!cell.expireTime.isAfter(now)) {
                final long expiredDays = TimeUtils.getAbsNatureDaysBetween(cell.expireTime.toEpochMilli(), now.toEpochMilli());
                // 过期2天以上的不要
                if (expiredDays > 2) {
                    return false;
                }
            }
            if (!cell.startTime.isBefore(now)) {
                final long comingDays = TimeUtils.getAbsNatureDaysBetween(now.toEpochMilli(), cell.startTime.toEpochMilli());
                // 未开4天以上的不要
                if (comingDays > 4) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public int hashCode() {
            return Objects.hash(cell.actId, cell.startTime);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null || getClass() != obj.getClass()) {
                return false;
            }
            ActivityCalendar c = (ActivityCalendar) obj;
            return cell.actId == c.cell.actId && cell.startTime.equals(c.cell.startTime);
        }

        @Override
        public String toString() {
            return "ActivityCalendar{" +
                    "actId=" + cell.actId +
                    ", scheduleId=" + scheduleId +
                    ", startTime=" + cell.startTime +
                    '}';
        }
    }

    public static Set<ActivityCalendar> getTodayActivityCalendar(long serverOpenTsMs, Instant today, int zoneId, long playerCreateTsMs) {
        Set<ActivityCalendar> ret = Sets.newHashSet();
        ActivityResService resService = ResHolder.getResService(ActivityResService.class);

        for (ActivitySchedule as : resService.scheduleMap.values()) {
            ActivityScheduleTemplate scheduleTemplate = as.template;
            // 强制下架
            if (resService.forceOffScheduleIds.contains(scheduleTemplate.getId())) {
                continue;
            }
            // 找出today的所有活动
            final Set<ActivityCalendar> calendars = calCalendar(serverOpenTsMs, today, as, zoneId, playerCreateTsMs);
            ret.addAll(calendars);
        }
        return ret;
    }

    private static Set<ActivityCalendar> calCalendar(long serverOpenTsMs, Instant today, ActivitySchedule as, int zoneId, long playerCreateTsMs) {
        final Instant zoneOpenTime = Instant.ofEpochMilli(serverOpenTsMs);
        final Instant zoneOpenDayInstant = TimeUtils.getDayStartInstant(zoneOpenTime);
        Set<ActivityCalendar> ret = Sets.newHashSet();
        final ActivityScheduleTemplate template = as.template;
        CommonEnum.ActivityOpenType openType = template.getOpenType();
        ActivitySchedule.Cell curOrNext = null;
        switch (openType) {
            case AOT_FROM_ZONE_OPEN:
                curOrNext = ActivitySchedule.FromZoneOpen.cell(as, zoneOpenTime);
                break;
            case AOT_FROM_PLAYER_CREATE:
                curOrNext = ActivitySchedule.FromPlayerCreate.cell(as, playerCreateTsMs);
                break;
            case AOT_ON_DATE:
                curOrNext = ActivitySchedule.OnDate.cell(as);
                break;
            case AOT_ON_DATE_LOOP:
                curOrNext = ActivitySchedule.OnDateLoop.curOrNext(as, today);
                break;
            case AOT_ON_DATE_ZONE_LOOP:
                curOrNext = ActivitySchedule.OnDateZoneLoop.curOrNext(as, zoneOpenTime, today);
                break;
            case AOT_ZONE_LOOP:
                curOrNext = ActivitySchedule.ZoneLoop.curOrNext(as, zoneOpenTime, today);
                break;
            default:
                break;
        }
        if (curOrNext == null) {
            return ret;
        }
        // 如果开启时间在开服限制天数结束之后，说明这个活动不应该被开启
        if (!isServerLimitOkForGvg(template, zoneOpenDayInstant, curOrNext)) {
            return ret;
        }
        // 区服限制检查
        if (!isZoneIdOk(template, zoneId, curOrNext.startTime)) {
            return ret;
        }

        // 获取父子活动
        Set<ActivityCalendar> oneScheduleCalendar = Sets.newHashSet();
        ActivityCalendar fatherCalendar = new ActivityCalendar(curOrNext, as.template.getId());
        oneScheduleCalendar.add(fatherCalendar);
        oneScheduleCalendar.addAll(getChildCalendar(today, fatherCalendar));

        // 修正父子活动的开启时间，判断今天时候能看到
        for (ActivityCalendar activityCalendar : oneScheduleCalendar) {
            activityCalendar = fixCalendarByServerOpenLimit(activityCalendar, template, zoneOpenDayInstant);
            if (activityCalendar == null) {
                continue;
            }
            if (!activityCalendar.cell.startTime.isBefore(activityCalendar.cell.expireTime)) {
                continue;
            }
            if (!today.isBefore(activityCalendar.cell.startTime) && !today.isAfter(activityCalendar.cell.expireTime)) {
                ret.add(activityCalendar);
            }
        }
        return ret;
    }

    private static boolean isServerLimitOkForGvg(ActivityScheduleTemplate template, Instant zoneOpenDayInstant, ActivitySchedule.Cell cell) {
        if (cell == null) {
            return false;
        }
        if (template.getServerOpenLimitPairList() != null) {
            for (IntPairType pair : template.getServerOpenLimitPairList()) {
                Instant zoneEndTime = zoneOpenDayInstant.plus(pair.getValue(), ChronoUnit.DAYS);
                if (!zoneEndTime.isAfter(cell.startTime)) {
                    return false;
                }
            }
        }
        return true;
    }

    private static ActivityCalendar fixCalendarByServerOpenLimit(ActivityCalendar calendar, ActivityScheduleTemplate template, Instant zoneOpenInstant) {
        if (template.getServerOpenLimitPairList() != null) {
            for (IntPairType pair : template.getServerOpenLimitPairList()) {
                Instant zoneStartTime = zoneOpenInstant.plus(pair.getKey(), ChronoUnit.DAYS);
                // 如果开启时间在开服天数限制前，修正startTime到开服天数限制的那一天
                if (!zoneStartTime.isBefore(calendar.cell.startTime) && !zoneStartTime.isAfter(calendar.cell.expireTime)) {
                    ActivitySchedule.Cell cell = new ActivitySchedule.Cell(
                            calendar.cell.actId,
                            calendar.cell.template,
                            calendar.cell.startTime.plus(zoneStartTime.getEpochSecond() - calendar.cell.startTime.getEpochSecond(), ChronoUnit.SECONDS),
                            calendar.cell.expireTime,
                            calendar.cell.loopTimes);
                    return new ActivityCalendar(cell, calendar.scheduleId);
                } else if (!calendar.cell.expireTime.isAfter(zoneStartTime)) {
                    // 活动结束时间在开服限制开始之前，那这个活动不能开
                    return null;
                }
            }
        }
        return calendar;
    }

    private static Set<ActivityCalendar> getChildCalendar(Instant today, ActivityCalendar father) {
        Set<ActivityCalendar> ret = Sets.newHashSet();
        if (father.cell.template.getChildActivityIdListList().isEmpty()) {
            return ret;
        }
        final ActivityResService ars = ResHolder.getResService(ActivityResService.class);
        for (Integer childActId : father.cell.template.getChildActivityIdListList()) {
            final ActivityTemplate childTemplate = ars.getActivityTemplate(childActId);
            Instant childStartTime = father.cell.startTime.plus(childTemplate.getOpenOffsetHours(), ChronoUnit.HOURS);
            Instant childExpireTime = childStartTime.plus(childTemplate.getLastingHours(), ChronoUnit.HOURS);

            if (!today.isBefore(childStartTime) && !today.isAfter(childExpireTime)) {
                ActivitySchedule.Cell childCell = new ActivitySchedule.Cell(childActId, childTemplate, childStartTime, childExpireTime, father.cell.loopTimes);
                ActivityCalendar childCalendar = new ActivityCalendar(childCell, father.scheduleId);
                ret.add(childCalendar);
                // 算子活动
                ret.addAll(getChildCalendar(today, childCalendar));
            }
        }
        return ret;
    }

    public List<ActivityRecycleTemplate> getRecycleRule(int actId) {
        return recycleMap.get(actId);
    }

    /**
     * 根据活动排行榜id，名次拿到奖励
     *
     * @return 奖励 or null
     */
    public List<IntPairType> getRewardByRankIdRank(int rankId, int rank) {
        TreeMap<Integer, List<IntPairType>> treeMap = rankIdRank2Reward.get(rankId);
        if (treeMap == null) {
            return null;
        }
        Map.Entry<Integer, List<IntPairType>> entry = treeMap.floorEntry(rank);
        if (entry == null) {
            return null;
        }
        return entry.getValue();
    }
}

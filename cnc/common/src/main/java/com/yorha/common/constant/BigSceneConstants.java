package com.yorha.common.constant;

import com.google.common.collect.BiMap;
import com.google.common.collect.ImmutableBiMap;
import com.yorha.proto.CommonEnum.DungeonType;
import com.yorha.proto.CommonEnum.MapAreaType;

/**
 * 大世界相关常量
 *
 * <AUTHOR>
 */
public interface BigSceneConstants {

    /**
     * 地图id对应的州个数
     */
    BiMap<Integer, Integer> MAP_REGION_NUM = ImmutableBiMap.of(
            1004, 10,
            1100, 29
    );
    /**
     * 12k大世界 地图数据id
     */
    int BIG_SCENE_MAP_ID = 1004;
    /**
     * 大世界加载存盘数据超时时间
     */
    int BIG_SCENE_LOAD_TIMEOUT = 60000;

    /**
     * 地缘数据格子数
     */
    int GRID_NUM = 1228;
    /**
     * 地缘格子x占位
     */
    int MAP_GRID_X = 0b111111111111111000000000000000;
    /**
     * 地缘格子y占位
     */
    int MAP_GRID_Y_DIGIT_NUM = 15;
    int MAP_GRID_Y = 0b111111111111111;

    /**
     * 地形类型 占位 长度5 0-31
     */
    int LANDFORMS_TYPE_TAG = 0b11111;
    int LANDFORMS_TYPE_DIGIT_NUM = 5;
    /**
     * 片id 占位 长度19 0-524287
     */
    int PART_TAG = 0b1111111111111111111 << LANDFORMS_TYPE_DIGIT_NUM;
    int PART_TAG_DIGIT_NUM = 19;
    /**
     * 州id  占位  长度6  0-63
     */
    int BEFORE_REGION_DIGIT_NUM = PART_TAG_DIGIT_NUM + LANDFORMS_TYPE_DIGIT_NUM;
    int REGION_ID_TAG = 0b111111 << BEFORE_REGION_DIGIT_NUM;

    /**
     * 格子索引到坐标的 转化比例
     */
    int GRID_INDEX_TO_MAP_COORDINATE_RATIO = 1000;
    /**
     * 地缘特殊区域类型之---河
     */
    int AREA_SPECIAL_TYPE_RIVER = 1;
    /**
     * 普通数据层最低层  普通层
     */
    int WORLD_NORMAL_MIN_LAYER = 1;
    /**
     * 普通数据层最高层  1.5级图标层
     */
    int WORLD_NORMAL_MAX_LAYER = 2;
    /**
     * 视野最大层数
     */
    int WORLD_MAX_LAYER = 8;

    static boolean isNormalLayer(int layer) {
        return layer <= WORLD_NORMAL_MAX_LAYER;
    }

    static boolean isBanEntityBornPart(MapAreaType type) {
        return type == MapAreaType.AREA_NONE || type == MapAreaType.CROSSING;
    }

    /**
     * 大世界存在资源田上限
     */
    int RES_BUILDING_NUM_MAX = 50000;

    /**
     * 大世界存在野怪上限
     */
    int MONSTER_NUM_MAX = 50000;

    /**
     * 大世界在副本时是否需要同步sceneplayer
     */
    static boolean needSyncScenePlayer(DungeonType type) {
        return false;
    }
}

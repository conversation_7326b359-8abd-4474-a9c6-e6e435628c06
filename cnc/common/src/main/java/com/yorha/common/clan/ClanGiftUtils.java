package com.yorha.common.clan;

import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ClanGiftItemProp;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import res.template.ClanGiftTemplate;
import res.template.ConstClanTemplate;

import java.util.concurrent.TimeUnit;

public class ClanGiftUtils {
    private ClanGiftUtils() {
    }

    /**
     * 礼物是否过期
     *
     * @param giftItem 军团礼物数据
     * @return true == 过期
     */
    public static boolean isGiftExpired(ClanGiftItemProp giftItem) {
        long giftCreateTsMs = TimeUtils.second2Ms(giftItem.getCreateTsSec());
        long giftExpireTsMs = giftCreateTsMs + TimeUnit.HOURS.toMillis(ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getGiftExpireHours());
        return SystemClock.now() > giftExpireTsMs;
    }

    /**
     * 礼物是否需要删除
     *
     * @param giftItem 军团礼物prop
     * @return true==礼物需要删除
     */
    public static boolean isGiftNeedDelete(ClanGiftItemProp giftItem) {
        long giftCreateTsMs = TimeUtils.second2Ms(giftItem.getCreateTsSec());
        long giftDeleteTsMs = giftCreateTsMs + TimeUnit.HOURS.toMillis(ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getGiftDeleteHours());
        return SystemClock.now() >= giftDeleteTsMs;
    }


    /**
     * 军团礼物是否已领取
     *
     * @param giftItem 军团礼物prop
     * @return true==礼物可已领取
     */
    public static boolean isGiftTaken(ClanGiftItemProp giftItem) {
        return giftItem.getStatus() != CommonEnum.ClanGiftStatus.CGS_CAN_TAKE;
    }


    /**
     * giftId对应礼物是否是普通礼物
     *
     * @param giftId 礼物id
     * @return true == 是普通礼物
     */
    public static boolean isNormalGift(int giftId) {
        return ResHolder.getTemplate(ClanGiftTemplate.class, giftId).getType() == CommonEnum.ClanGiftRarityType.CLANGRT_NORMAML;
    }
}

package com.yorha.common.utils.vector;

import com.yorha.common.utils.shape.Point;

/**
 * <AUTHOR>
 */
public class Vector2i {
    private final int x;
    private final int y;

    public Vector2i(int x, int y) {
        this.x = x;
        this.y = y;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public static Vector2i valueOfPoint(Point src, Point end) {
        return new Vector2i(end.getX() - src.getX(), end.getY() - src.getY());
    }

    public double calVectorLength() {
        int xDis = x * x;
        int yDis = y * y;
        return Math.sqrt(xDis * xDis + yDis * yDis);
    }

    /**
     * 向量点乘
     */
    public static float dotProduct(Vector2i a, Vector2i b) {
        return a.getX() * b.getX() + a.getY() * b.getY();
    }

    /**
     * 向量叉乘
     */

    public static float crossProduct(Vector2i a, Vector2i b) {
        return a.getX() * b.getY() - a.getY() * b.getX();
    }

    public static Vector2i getVectorFromPointToPoint(Point srcPoint, Point endPoint) {
        return new Vector2i(endPoint.getX() - srcPoint.getX(), endPoint.getY() - endPoint.getX());
    }

    /**
     * 调整长度
     */
    public Vector2i resizeLength(float newLength) {
        float len = (float) getLength();
        return new Vector2i((int) (x / len * newLength), (int) (y / len * newLength));
    }

    public double getLength() {
        return Math.sqrt(x * x + y * y);
    }
}

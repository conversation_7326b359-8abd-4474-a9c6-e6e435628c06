package com.yorha.common.qlog;

import com.google.common.base.Charsets;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.constant.LogConstant;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;

import java.io.IOException;
import java.net.*;
import java.util.concurrent.TimeUnit;

/**
 * 接入qlog，管理经分流水
 *
 * <AUTHOR>
 */
public final class QlogManager {
    private static final Logger LOGGER = LogManager.getLogger(LogConstant.LOG_TYPE_QLOG);
    private DatagramSocket socket;
    private DatagramPacket packet;
    private volatile boolean stop = true;
    private boolean enableLocalLog = true;

    private MpscBlockingConsumerArrayQueue<AbstractQlogFlow> queue;
    private Thread qlogThread;

    public static QlogManager getInstance() {
        return QlogManager.InstanceHolder.INSTANCE;
    }

    /**
     * 控制开关是否打本地日志
     */
    private boolean enableLocalLog() {
        return this.enableLocalLog;
    }

    /**
     * 增加一条经分流水
     */
    public void addLogFlow(AbstractQlogFlow qlogFlow) {
        logLocal(qlogFlow);

        if (!qlogFlow.checkCompletion()) {
            WechatLog.error("{} try log but not Completed content: {}", qlogFlow.getMetaName(), qlogFlow.getIncompleteFields());
            return;
        }
        MonitorUnit.QLOG_TOTAL.labels(ServerContext.getBusId(), qlogFlow.getMetaName()).inc();
        if (stop) {
            return;
        }
        boolean add = queue.add(qlogFlow);
        if (!add) {
            LOGGER.error("addLogFlow fail {}", ClassNameCacheUtils.getSimpleName(qlogFlow.getClass()));
        }
    }

    public void logLocal(AbstractQlogFlow qlogFlow) {
        if (enableLocalLog()) {
            LOGGER.info(qlogFlow.getContent());
        }
    }

    public void initLocal() {
        this.enableLocalLog = true;
    }

    public void init() {
        final String address = ClusterConfigUtils.getWorldConfig().getStringItem("qlog_address");
        final String port = ClusterConfigUtils.getWorldConfig().getStringItem("qlog_port");
        if (StringUtils.isBlank(address) || StringUtils.isBlank(port)) {
            LOGGER.info("init qlog failed address:{}, port:{}", address, port);
            return;
        }
        try {
            queue = new MpscBlockingConsumerArrayQueue<>(40_000);
            qlogThread = ConcurrentHelper.newThread("qlog-", true, this::execute);
            socket = new DatagramSocket();
            packet = new DatagramPacket(new byte[1], 1);
            packet.setAddress(InetAddress.getByName(address));
            packet.setPort(Integer.parseInt(port));
            stop = false;
            qlogThread.start();
            LOGGER.info("init qlog success");
        } catch (SocketException | UnknownHostException e) {
            throw new GeminiException("init qlog net error", e);
        }
    }

    private void execute() {
        do {
            AbstractQlogFlow flow;
            try {
                while ((flow = queue.poll(1, TimeUnit.SECONDS)) != null) {
                    if (isFlowBan(flow)) {
                        continue;
                    }
                    sendToQlogServer(flow.getContent(), socket, packet);
                }
            } catch (InterruptedException e) {
                LOGGER.info("execute qlog interrupt", e);
            } catch (Exception e) {
                LOGGER.error("execute qlog error", e);
            }
        } while (!stop);
    }

    /**
     * 流水是否被禁止发送
     */
    private boolean isFlowBan(AbstractQlogFlow flow) {
        if (!ServerContext.isChinaVersion()) {
            return false;
        }
        // 国内版本暂时先只开放这一批流水
        final String metaName = flow.getMetaName();
        if (("PlayerRegister".equals(metaName))
                || ("PlayerLogin".equals(metaName))
                || ("PlayerLogout".equals(metaName))
                || ("ItemFlow".equals(metaName))
                || ("MoneyFlow".equals(metaName))
                || ("OnlineCnt".equals(metaName))
                || ("CncBundlePurchase".equals(metaName))
                || ("CncVipExp".equals(metaName))
                || ("CncUserDistribution".equals(metaName))
                || ("CncTaskFlow".equals(metaName))
                || ("CncClientInfo".equals(metaName))
                || ("CncBuildCity".equals(metaName))
        ) {
            return false;
        }
        return true;
    }

    public void shutdown() throws InterruptedException {
        LOGGER.info("QlogManager shutdown isStop {}", stop);
        if (stop) {
            return;
        }
        stop = true;
        int leftQlogNum = queue.size();
        if (leftQlogNum > 0) {
            LOGGER.warn("qlog stop server and left :{}", leftQlogNum);
        }
        qlogThread.interrupt();
    }

    private void sendToQlogServer(String text, DatagramSocket socket, DatagramPacket packet) {
        if (socket == null || packet == null) {
            throw new GeminiException("sendToQlogServer fail socket:{] packet:{}", socket, packet);
        }
        byte[] bytes = text.getBytes(Charsets.UTF_8);
        packet.setData(bytes, 0, bytes.length);
        try {
            socket.send(packet);
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private static class InstanceHolder {
        private static final QlogManager INSTANCE = new QlogManager();
    }
}

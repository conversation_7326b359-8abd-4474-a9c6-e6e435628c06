package com.yorha.common.resource.datatype;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class IntArray extends ArrayList<Integer> {
    public IntArray(List collect) {
        super(collect);
    }

    public static IntArray valueOf(String s) {
        return new IntArray(Arrays.stream(Arrays.stream(s.split(",")).mapToInt(num -> Integer.parseInt(num)).toArray()).boxed().collect(Collectors.toList()));
    }
}

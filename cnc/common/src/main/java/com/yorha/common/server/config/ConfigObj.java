package com.yorha.common.server.config;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.YamlUtils;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import javax.annotation.concurrent.ThreadSafe;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
@ThreadSafe
public class ConfigObj {
    private static final Logger LOGGER = LogManager.getLogger(ConfigObj.class);

    private String md5;
    private volatile Map<String, Object> items;
    private final ReentrantLock reentrantLock;

    public ConfigObj() {
        this.md5 = null;
        this.items = null;
        this.reentrantLock = new ReentrantLock(true);
    }

    /**
     * 读取整形配置项。
     *
     * @param key 配置项键。
     * @return 配置项数据。
     */
    public int getIntItem(final String key) {
        final Object o = this.items.get(key);
        if (!(o instanceof Integer)) {
            throw new GeminiException("try get key={} value={}! but not int!", key, o);
        }
        return (int) o;
    }

    /**
     * 读取长整型配置项。
     *
     * @param key 配置项键。
     * @return 配置项数据。
     */
    public long getLongItem(final String key) {
        final Object o = this.items.get(key);
        if (!(o instanceof Integer) && !(o instanceof Long)) {
            throw new GeminiException("try get key={} value={}! but not long or int!", key, o);
        }
        if (o instanceof Integer) {
            return (int) o;
        }
        return (long) o;
    }


    /**
     * 读取bool型配置
     *
     * @param key 配置键。
     * @return 配置项数据。
     */
    public boolean getBooleanItem(final String key) {
        final Object o = this.items.get(key);
        if (!(o instanceof Boolean)) {
            throw new GeminiException("try get key={} value={}! but not bool!", key, o);
        }
        return (boolean) o;
    }

    /**
     * 读取List<String>类型配置。
     *
     * @param key 配置键。
     * @return 配置项。
     */
    @SuppressWarnings("unchecked")
    public List<String> getListStrItem(final String key) {
        final Object o = this.items.get(key);
        if (!(o instanceof List)) {
            throw new GeminiException("try get key={} value={}! but not list!", key, o);
        }
        return Collections.unmodifiableList((List<String>) o);
    }

    /**
     * 读取List<Integer>类型配置。
     *
     * @param key 配置键。
     * @return 配置项。
     */
    @SuppressWarnings("unchecked")
    public List<Integer> getListIntegerItem(final String key) {
        final Object o = this.items.get(key);
        if (!(o instanceof List)) {
            throw new GeminiException("try get key={} value={}! but not list!", key, o);
        }
        return Collections.unmodifiableList((List<Integer>) o);
    }

    /**
     * 读取List<String>类型配置。
     *
     * @return 配置项 （可为null）
     */
    @SuppressWarnings("unchecked")
    public List<String> getListStrItemMayEmpty(final String key) {
        Object o = this.items.get(key);
        if (o == null) {
            return null;
        }
        return Collections.unmodifiableList((List<String>) o);
    }

    /**
     * 读取string类型配置值。
     *
     * @param key 配置键
     * @return 配置项属性
     */
    public String getStringItem(final String key) {
        final Object o = this.items.get(key);
        if (!(o instanceof String)) {
            throw new GeminiException("try get key={} value={}! but not string!", key, o);
        }
        return (String) o;
    }

    public Object getObjectItem(final String key) {
        return this.items.get(key);
    }

    /**
     * 获取String Map(可能为空)
     *
     * @param key 配置建
     * @return 配置键属性
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public Map<String, Object> getStringMapItem(final String key) {
        final Object o = this.items.get(key);
        if (o == null) {
            LOGGER.warn("try get key={}, but value has no config, return null", key);
            return null;
        }
        if (!(o instanceof Map)) {
            throw new GeminiException("try get key={} value={}! but not Map!", key, o);
        }
        return Collections.unmodifiableMap((Map<String, Object>) o);
    }

    /**
     * 获取Int Map(可能为空)
     *
     * @param key 配置建
     * @return 配置键属性
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public Map<Integer, Integer> getIntegerMapItem(final String key) {
        final Object o = this.items.get(key);
        if (o == null) {
            LOGGER.warn("try get key={}, but value has no config, return null", key);
            return null;
        }
        if (!(o instanceof Map)) {
            throw new GeminiException("try get key={} value={}! but not Map!", key, o);
        }
        return Collections.unmodifiableMap((Map<Integer, Integer>) o);
    }

    /**
     * 更新配置对象数据。
     *
     * @param reason     更新缘由
     * @param newContent yaml文件内容。
     */
    void refresh(final String reason, final String newContent) throws IllegalArgumentException {
        this.reentrantLock.lock();
        try {
            if (StringUtils.isEmpty(newContent)) {
                throw new GeminiException("refresh with empty content reason:{}", reason);
            }
            final String newMd5 = StringUtils.md5(newContent);
            LOGGER.info("gemini_system try refresh reason={}", reason);
            if (this.md5 != null && StringUtils.equals(this.md5, newMd5)) {
                LOGGER.warn("refresh pass! md5 is same, reason={}", reason);
                return;
            }
            Map<String, Object> newItems;
            try {
                newItems = YamlUtils.newInstance(newContent);
            } catch (Exception e) {
                throw new GeminiException("refresh with illegal content, YamlUtils.newInstance fail", e);
            }
            if (!this.checkNewItemLegally(null, this.items, newItems)) {
                throw new GeminiException("refresh with illegal content! reason={}", reason);
            }
            this.md5 = newMd5;
            this.items = newItems;
            LOGGER.info("gemini_system refresh successful! reason={}", reason);
        } finally {
            this.reentrantLock.unlock();
        }
    }

    @SuppressWarnings({"rawtypes"})
    private boolean checkNewItemLegally(final String parentKey, Map oldItems, Map newItems) {
        if (oldItems == null) {
            // 就算是第一次设置，也应该check每个值是否合法，不为null
            for (final Object key : newItems.keySet()) {
                final Object newValue = newItems.get(key);
                final String curKey = this.getCurrentKey(parentKey, key);
                // 存在(key: null)，这是一个不合法的设置行为。
                if (newValue == null) {
                    LOGGER.error("checkNewItemLegally key={}! key={} value is null", curKey, key);
                    return false;
                }
                // number必须符合number的要求
                if (newValue instanceof Number && !this.checkValueNumber(newValue)) {
                    return false;
                }
                // value是一个map，需要递归检查
                if (!(newValue instanceof Map)) {
                    continue;
                }
                if (!this.checkNewItemLegally(curKey, null, (Map) newValue)) {
                    return false;
                }
            }
            return true;
        }
        // oldItems不为空，但是新的items为空，表示尝试清空配置，这个不被允许。
        if (newItems == null) {
            LOGGER.error("checkNewItemLegally key={}! old not null! new null!", parentKey);
            return false;
        }
        // 新旧Items都存在，但是类型变了，这个不被允许。
        if (!oldItems.getClass().equals(newItems.getClass())) {
            LOGGER.error("checkNewItemLegally key={}! old class={}! new class={}!", parentKey, oldItems.getClass().getSimpleName(), newItems.getClass().getSimpleName());
            return false;
        }
        // 以下保证旧数值是新数值的父集合，保证各个对应字段类型不变
        for (final Object key : oldItems.keySet()) {
            final String curKey = this.getCurrentKey(parentKey, key);
            final Object oldValue = oldItems.get(key);
            final Object newValue = newItems.get(key);
            // oldItems的值不可能存在null，因此newValue为空表示缺乏字段。
            // 最外层不可少kv，kv下的map可少(最外层的parentKey为null)
            if (newValue == null && parentKey == null) {
                LOGGER.error("checkNewItemLegally key={}! old is {}, new is null", curKey, oldValue.getClass().getSimpleName());
                return false;
            }
            // 旧数值是number
            if (oldValue instanceof Number) {
                if (!this.checkValueNumber(newValue)) {
                    return false;
                }
                continue;
            }
            // 类型判定
            if (!oldValue.getClass().equals(newValue.getClass())) {
                LOGGER.error("checkNewItemLegally key={}! old class={}! new class={}!", curKey, oldValue.getClass().getSimpleName(), newValue.getClass().getSimpleName());
                return false;
            }
            // 递归检查
            if (!(oldValue instanceof Map)) {
                continue;
            }
            if (!this.checkNewItemLegally(curKey, (Map) oldValue, (Map) newValue)) {
                return false;
            }
        }
        return true;
    }

    private String getCurrentKey(final String parentKey, final Object key) {
        return parentKey == null ? key.toString() : (parentKey + "." + key.toString());
    }

    private boolean checkValueNumber(final Object value) {
        return (value instanceof Long || value instanceof Integer);
    }
}

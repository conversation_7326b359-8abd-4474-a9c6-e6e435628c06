package com.yorha.common.resource.resservice.item;

import com.yorha.proto.CommonEnum;

public class ItemRewardCondition {
    private final CommonEnum.OpenConditionType openConditionType;
    private final int param;

    public ItemRewardCondition(CommonEnum.OpenConditionType openConditionType, int param) {
        this.openConditionType = openConditionType;
        this.param = param;
    }

    public boolean isOpen(long serverOpenDays) {
        if (openConditionType == CommonEnum.OpenConditionType.OCT_OPEN_SERVER) {
            if (serverOpenDays < param) {
                return false;
            }
        }
        return true;
    }
}

package com.yorha.common.db.tcaplus.option;

/**
 * 在tcaplus中按part-key删除全片数据
 *
 * <AUTHOR>
 */
public class DeleteByPartKeyOption {
    /**
     * 如果设置为负数，表示当前数据不启动版本控制
     */
    private int version = -1;
    /**
     * 是否重试数据库。如果重试，DBActor会不停得重试直到超时。
     */
    private boolean isRetry = false;

    private DeleteByPartKeyOption() {
    }

    public static DeleteByPartKeyOption.Builder newBuilder() {
        return new DeleteByPartKeyOption.Builder();
    }

    public int getVersion() {
        return this.version;
    }

    public boolean isRetry() {
        return this.isRetry;
    }

    public static final class Builder {
        private int version = -1;
        private boolean isRetry = false;

        private Builder() {
        }

        public DeleteByPartKeyOption.Builder withVersion(int version) {
            this.version = version;
            return this;
        }

        public DeleteByPartKeyOption.Builder withRetry() {
            this.isRetry = true;
            return this;
        }

        public DeleteByPartKeyOption build() {
            DeleteByPartKeyOption option = new DeleteByPartKeyOption();
            option.version = this.version;
            option.isRetry = this.isRetry;
            return option;
        }
    }
}

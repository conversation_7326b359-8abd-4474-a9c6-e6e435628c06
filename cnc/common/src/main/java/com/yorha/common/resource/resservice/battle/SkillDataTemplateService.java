package com.yorha.common.resource.resservice.battle;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.shape.Ring;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Option;
import org.jetbrains.annotations.NotNull;
import res.template.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 技能相关配置
 *
 * <AUTHOR> Jiang
 */
public class SkillDataTemplateService extends AbstractResService {

    private int maxPincer;

    /**
     * 新版技能系统
     */
    private final Map<String, SkillConfigTemplate> skillStore = Maps.newHashMap();


    private final Map<Integer, Pair<String[], String[]>> effectSelfStatusMap = Maps.newHashMap();
    private final Map<Integer, String[]> effectTargetStatusMap = Maps.newHashMap();
    private final Map<Integer, List<Integer>> comboEffects = Maps.newHashMap();

    public SkillDataTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

        Collection<PincerTemplate> list = getResHolder().getListFromMap(PincerTemplate.class);
        for (PincerTemplate e : list) {
            if (e.getId() > maxPincer) {
                maxPincer = e.getId();
            }
        }

        for (SkillConfigTemplate e : getResHolder().getListFromMap(SkillConfigTemplate.class)) {
            skillStore.put(e.getSkillId() + "_" + e.getLevel(), e);
        }


        for (SkillEffectTemplate effectTemplate : getResHolder().getListFromMap(SkillEffectTemplate.class)) {
            String[] selfEffect = null;
            String[] selfEffect2 = null;
            if (effectTemplate.getSelfTroopsStateValue1() != null) {
                selfEffect = effectTemplate.getSelfTroopsStateValue1().replace("&gt;", ">").replace("&lt;", "<").split(",");
            }
            if (effectTemplate.getSelfTroopsStateValue2() != null) {
                selfEffect2 = effectTemplate.getSelfTroopsStateValue2().replace("&gt;", ">").replace("&lt;", "<").split(",");
            }
            effectSelfStatusMap.put(effectTemplate.getId(), Pair.of(selfEffect, selfEffect2));

            if (effectTemplate.getTroopsStateValue() != null) {
                String str = effectTemplate.getTroopsStateValue().replace("&gt;", ">").replace("&lt;", "<");
                effectTargetStatusMap.put(effectTemplate.getId(), str.split(","));
            }

            if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
                List<Integer> effects = Lists.newArrayList(effectTemplate.getValue1(), effectTemplate.getValue2(), effectTemplate.getValue3(), effectTemplate.getValue4())
                        .stream()
                        .filter(it -> it > 0)
                        .collect(Collectors.toList());
                comboEffects.put(effectTemplate.getId(), effects);
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
       // checkEffect();
        checkSkill();
        checkAreaSkill();
//        checkWithEffectType();
        checkSummon();
    }

//    private void checkEffect() throws ResourceException {
//        for (SkillEffectTemplate skillEffectTemplate : getResHolder().getListFromMap(SkillEffectTemplate.class)) {
//            for (IntPairType intPairType : skillEffectTemplate.getAdditionGroupPairList()) {
//                CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(intPairType.getKey());
//                if (buffEffectType == null) {
//                    throw new ResourceException("技能效果id:{}， 配置了没实现的增益id:{}", skillEffectTemplate.getId(), intPairType.getKey());
//                }
//
//                switch (buffEffectType) {
//                    case ET_COLLECT_END_REWARD:
//                    case ET_MONSTER_KILL_REWARD: {
//                        if (getResHolder().findValueFromMap(SelecteRewardTemplate.class, intPairType.getValue()) == null) {
//                            throw new ResourceException("技能效果id:{}， 配置了不存在的奖励id:{}", skillEffectTemplate.getId(), intPairType.getValue());
//                        }
//                    }
//                    default:
//                        break;
//                }
//            }
//            for (IntPairType intPairType : skillEffectTemplate.getGroupBuff1PairList()) {
//                BattleBuffTemplate buffTemplate = getResHolder().findValueFromMap(BattleBuffTemplate.class, intPairType.getKey());
//                if (buffTemplate == null) {
//                    throw new ResourceException("技能效果id:{}， 配置了不存在的buffId:{}", skillEffectTemplate.getId(), intPairType.getKey());
//                }
//            }
//            for (IntPairType intPairType : skillEffectTemplate.getGroupBuff2PairList()) {
//                BattleBuffTemplate buffTemplate = getResHolder().findValueFromMap(BattleBuffTemplate.class, intPairType.getKey());
//                if (buffTemplate == null) {
//                    throw new ResourceException("技能效果id:{}， 配置了不存在的buffId:{}", skillEffectTemplate.getId(), intPairType.getKey());
//                }
//            }
//            if (skillEffectTemplate.getSelfTroopsState1() == CommonEnum.StatusType.ST_COMMON_RATE) {
//                boolean match = getResHolder().getListFromMap(SkillEffectTemplate.class).stream()
//                        .filter(it -> it.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT)
//                        .anyMatch(it -> getComboEffects(it).contains(skillEffectTemplate.getId()));
//                if (!match) {
//                    throw new ResourceException("技能效果id:{}，type:共用概率池，必须和[释放N个技能效果]一起使用", skillEffectTemplate.getId());
//                }
//                String[] effectSelfStatusValue = getEffectSelfStatusValue(skillEffectTemplate.getId()).getFirst();
//                if (effectSelfStatusValue == null || effectSelfStatusValue.length != 2) {
//                    throw new ResourceException("技能效果id:{}，type:共用概率池，参数错误:{}, 要配置一个区间", skillEffectTemplate.getId(), skillEffectTemplate.getSelfTroopsStateValue1());
//                }
//                if (skillEffectTemplate.getSelfTroopsState2() != CommonEnum.StatusType.STT_NONE && skillEffectTemplate.getSelfTroopsState2() != null) {
//                    throw new ResourceException("技能效果id:{}，type:共用概率池，不允许配置自身状态2", skillEffectTemplate.getId());
//                }
//            }
//
//            if (skillEffectTemplate.getSelfTroopsState1() == CommonEnum.StatusType.ST_RELEASE_SKILLS) {
//                selfStateReleaseSkillCheck(skillEffectTemplate.getId(), skillEffectTemplate.getSelfTroopsStateValue1(), true);
//            }
//            if (skillEffectTemplate.getSelfTroopsState2() == CommonEnum.StatusType.ST_RELEASE_SKILLS) {
//                selfStateReleaseSkillCheck(skillEffectTemplate.getId(), skillEffectTemplate.getSelfTroopsStateValue2(), false);
//            }
//
//
//            if (skillEffectTemplate.getSelfTroopsState1() == CommonEnum.StatusType.ST_SUMMONING_NUM) {
//                Pair<String[], String[]> pair = effectSelfStatusMap.get(skillEffectTemplate.getId());
//                if (pair == null || pair.getFirst() == null) {
//                    throw new ResourceException("技能效果id:{}，不包含自身状态1的配置参数", skillEffectTemplate.getId());
//                }
//                checkSummoningNumStatusCheck(skillEffectTemplate.getId(), pair.getFirst(), true);
//            }
//            if (skillEffectTemplate.getSelfTroopsState2() == CommonEnum.StatusType.ST_SUMMONING_NUM) {
//                Pair<String[], String[]> pair = effectSelfStatusMap.get(skillEffectTemplate.getId());
//                if (pair == null || pair.getSecond() == null) {
//                    throw new ResourceException("技能效果id:{}，不包含自身状态2的配置参数", skillEffectTemplate.getId());
//                }
//                checkSummoningNumStatusCheck(skillEffectTemplate.getId(), pair.getSecond(), true);
//            }
//            if (skillEffectTemplate.getTroopsState() == CommonEnum.StatusType.ST_SUMMONING_NUM) {
//                String[] param = effectTargetStatusMap.get(skillEffectTemplate.getId());
//                if (param == null) {
//                    throw new ResourceException("技能效果id:{}，不包含目标状态的配置参数", skillEffectTemplate.getId());
//                }
//                checkSummoningNumStatusCheck(skillEffectTemplate.getId(), param, true);
//            }
//
//
//            if (skillEffectTemplate.getSelfTroopsState1() == CommonEnum.StatusType.ST_SUMMONING_GROUP_NUM) {
//                Pair<String[], String[]> pair = effectSelfStatusMap.get(skillEffectTemplate.getId());
//                if (pair == null || pair.getFirst() == null) {
//                    throw new ResourceException("技能效果id:{}，不包含自身状态1的配置参数", skillEffectTemplate.getId());
//                }
//                checkSummoningNumStatusCheck(skillEffectTemplate.getId(), pair.getFirst(), false);
//            }
//            if (skillEffectTemplate.getSelfTroopsState2() == CommonEnum.StatusType.ST_SUMMONING_GROUP_NUM) {
//                Pair<String[], String[]> pair = effectSelfStatusMap.get(skillEffectTemplate.getId());
//                if (pair == null || pair.getSecond() == null) {
//                    throw new ResourceException("技能效果id:{}，不包含自身状态2的配置参数", skillEffectTemplate.getId());
//                }
//                checkSummoningNumStatusCheck(skillEffectTemplate.getId(), pair.getSecond(), false);
//            }
//            if (skillEffectTemplate.getTroopsState() == CommonEnum.StatusType.ST_SUMMONING_GROUP_NUM) {
//                String[] param = effectTargetStatusMap.get(skillEffectTemplate.getId());
//                if (param == null) {
//                    throw new ResourceException("技能效果id:{}，不包含目标状态的配置参数", skillEffectTemplate.getId());
//                }
//                checkSummoningNumStatusCheck(skillEffectTemplate.getId(), param, false);
//            }
//
//
//            if (skillEffectTemplate.getSelfTroopsState2() == CommonEnum.StatusType.ST_COMMON_RATE) {
//                throw new ResourceException("技能效果id:{}，type:共用概率池，自身状态2不能适用共用概率池", skillEffectTemplate.getId());
//            }
//
//            if (skillEffectTemplate.getTargetType() == CommonEnum.TargetType.TAT_NONE) {
//                throw new ResourceException("技能效果id:{}，配置了不存在的TargetType:{}", skillEffectTemplate.getId(), skillEffectTemplate.getTargetType());
//            }
//
//            if (skillEffectTemplate.getRange() > 0) {
//                SkillRangeTemplate rangeTemplate = getResHolder().findValueFromMap(SkillRangeTemplate.class, skillEffectTemplate.getRange());
//                if (rangeTemplate == null) {
//                    throw new ResourceException("技能效果id:{}，配置了不存在的技能范围range:{}", skillEffectTemplate.getId(), skillEffectTemplate.getRange());
//                }
//                if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_DAMAGE || skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_TREATMENT) {
//                    if (rangeTemplate.getReduce() <= 0) {
//                        throw new ResourceException("技能范围id:{}，配置了错误的reduce:{}", rangeTemplate.getId(), rangeTemplate.getReduce());
//                    }
//                }
//                if (skillEffectTemplate.getMustTypeList().size() > rangeTemplate.getLimit()) {
//                    throw new ResourceException("技能范围id:{} 效果id{}，必中大于范围筛选", rangeTemplate.getId(), skillEffectTemplate.getMustTypeList().size());
//                }
//            }
//
//        }
//    }


    private void checkSkill() throws ResourceException {
        for (SkillConfigTemplate skillTemplate : getResHolder().getListFromMap(SkillConfigTemplate.class)) {
            int buffId0 = skillTemplate.getBuffId0();
            if (buffId0 > 0) {
                getResHolder().checkValueFromMap(BuffConfigTemplate.class, buffId0, () -> StringUtils.format("SkillConfigTemplate id:{} buffId0:{} not existed", skillTemplate.getSkillId(), buffId0));
            }
        }
        // 回合开始和战斗开始触发器，只能配置增删buff的技能效果
        Set<SkillEffectTemplate> needNtfTemplateSet = new HashSet<>();
        for (SkillEffectTemplate skillEffectTemplate : getResHolder().getListFromMap(SkillEffectTemplate.class)) {
            CommonEnum.TriggerType triggerType = skillEffectTemplate.getTrigger();
            if (triggerType != CommonEnum.TriggerType.TT_ROUND_START && triggerType != CommonEnum.TriggerType.TT_BATTLE_START) {
                continue;
            }
            if (isNeedCheckWithBuff(skillEffectTemplate)) {
                needNtfTemplateSet.add(skillEffectTemplate);
            }
            // 回合开始时、开战时不允许配置伤害类BUFF（DOT）
            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_ADD_BUFF) {
                checkDotBuff(skillEffectTemplate);
            }
            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_ADD_BATCH_BUFF) {
                checkDotBuff(skillEffectTemplate.getId(), skillEffectTemplate.getValue1());
            }
        }
        if (!needNtfTemplateSet.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (SkillEffectTemplate skillEffectTemplate : needNtfTemplateSet) {
                boolean needWechatLog = true;
                if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
                    needWechatLog = checkMultipleEffectIdSet(skillEffectTemplate);
                }
                if (needWechatLog) {
                    sb.append(StringUtils.format("技能效果id: {}，触发类型为 {}，战斗开始和回合开始时，只能配置增删buff的技能效果\n", skillEffectTemplate.getId(),
                            skillEffectTemplate.getType().getValueDescriptor().getOptions().getExtension(Option.name)));
                }
            }
            if (!sb.toString().isEmpty()) {
                throw new ResourceException(sb.toString());
            }
        }
        // 结算阶段之后的触发器，不能配置伤害的技能效果
        for (SkillEffectTemplate skillEffectTemplate : getResHolder().getListFromMap(SkillEffectTemplate.class)) {
            CommonEnum.TriggerType triggerType = skillEffectTemplate.getTrigger();
            if (!isTriggerTypeAfterSettlement(triggerType)) {
                continue;
            }
            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_DAMAGE) {
                throw new ResourceException("技能效果id: {}，触发类型为 {}，结算阶段之后的触发器，不能配置造成伤害的技能效果",
                        skillEffectTemplate.getId(), skillEffectTemplate.getType().getValueDescriptor().getOptions().getExtension(Option.name));
            }
        }
    }

//    private void checkWithEffectType() throws ResourceException {
//        for (SkillEffectTemplate skillEffectTemplate : getResHolder().getListFromMap(SkillEffectTemplate.class)) {
//            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
//                List<Integer> comboEffects = getComboEffects(skillEffectTemplate);
//                if (comboEffects.isEmpty()) {
//                    throw new ResourceException("技能效果id:{}，type:释放N个技能效果，没有配置需要释放的技能效果", skillEffectTemplate.getId());
//                } else {
//                    for (Integer comboEffect : comboEffects) {
//                        SkillEffectTemplate effectTemplate = getResHolder().findValueFromMap(SkillEffectTemplate.class, comboEffect);
//                        if (effectTemplate == null) {
//                            throw new ResourceException("技能效果id:{}，type:释放N个技能效果，配置了不存在的技能效果id:{}", skillEffectTemplate.getId(), comboEffect);
//                        }
//                    }
//                }
//            }
//            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_DAMAGE) {
//                if (skillEffectTemplate.getTargetType() == CommonEnum.TargetType.TT_ATTACKER
//                        && skillEffectTemplate.getEnemyRelation() == CommonEnum.EnemyRelation.ERL_NONE) {
//                    throw new ResourceException("技能效果id:{}，配置了错误的targetType：{}", skillEffectTemplate.getId(),
//                            skillEffectTemplate.getTargetType().getValueDescriptor().getOptions().getExtension(Option.name));
//                }
//            }
//            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_DISPERSE) {
//                if (skillEffectTemplate.getValue1() != 1 && skillEffectTemplate.getValue1() != 2 && skillEffectTemplate.getValue1() != 3) {
//                    throw new ResourceException("技能效果id:{}，效果：移除BUFF 配置了错误的value1:{}", skillEffectTemplate.getId(), skillEffectTemplate.getValue1());
//                }
//                if (skillEffectTemplate.getValue2() == 0) {
//                    throw new ResourceException("技能效果id:{}，配置了错误的value2:{}", skillEffectTemplate.getId(), skillEffectTemplate.getValue2());
//                }
//                if (skillEffectTemplate.getValue1() == 3) {
//                    BattleBuffTemplate valueFromMap = getResHolder().findValueFromMap(BattleBuffTemplate.class, skillEffectTemplate.getValue2());
//                    if (valueFromMap == null) {
//                        throw new ResourceException("技能效果id:{}，配置了不存在的value2:{}", skillEffectTemplate.getId(), skillEffectTemplate.getValue2());
//                    }
//                }
//            }
//
//            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_REDUCE_DURABILITY) {
//                if (skillEffectTemplate.getValue1() <= 0 || skillEffectTemplate.getValue1() > MathUtils.TEN_THOUSAND) {
//                    throw new ResourceException("技能效果id:{}，错误的参数1:{}， 应该区间(0, 10000]", skillEffectTemplate.getId(), skillEffectTemplate.getValue2());
//                }
//                if (skillEffectTemplate.getValue2() > 0) {
//                    throw new ResourceException("技能效果id:{}，无效value2，但配了value2:{}", skillEffectTemplate.getId(), skillEffectTemplate.getValue2());
//                }
//            }
//            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_ADD_BATCH_BUFF) {
//                if (skillEffectTemplate.getValue1() <= 0) {
//                    throw new ResourceException("技能效果表错误, id={}, type={}, 添加多层BUFF配置问题, value1 <= 0, value1={}",
//                            skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue1());
//                }
//                if (skillEffectTemplate.getValue2() <= 0) {
//                    throw new ResourceException("技能效果表错误, id={}, type={}, 添加多层BUFF配置问题, value2 <= 0, value2={}",
//                            skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue2());
//                }
//                BattleBuffTemplate buff = getResHolder().findValueFromMap(BattleBuffTemplate.class, skillEffectTemplate.getValue1());
//                if (buff == null) {
//                    throw new ResourceException("技能效果表错误, id={}, type={}, 添加多层BUFF配置问题, buffId在战斗buff表中不存在,  value1={}",
//                            skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue1());
//                }
//            }
//            if (skillEffectTemplate.getType() == CommonEnum.SkillEffectType.SET_SUMMONING) {
//                if (skillEffectTemplate.getValue1() <= 0) {
//                    throw new ResourceException("技能效果表错误, id={}, type={}, 召唤技能未配置召唤id, value1={}",
//                            skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue1());
//                }
//                SummoningMonsterTemplate summoningMonsterTemplate = getResHolder().findValueFromMap(SummoningMonsterTemplate.class, skillEffectTemplate.getValue1());
//                if (summoningMonsterTemplate == null) {
//                    throw new ResourceException("技能效果表错误, id={}, type={}, 召唤id在技能召唤表不存在, value1={}",
//                            skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue1());
//                }
//                if (skillEffectTemplate.getValue2() > 0) {
//                    SummoningMonsterTemplate summoningMonsterTemplate2 = getResHolder().findValueFromMap(SummoningMonsterTemplate.class, skillEffectTemplate.getValue2());
//                    if (summoningMonsterTemplate2 == null) {
//                        throw new ResourceException("技能效果表错误, id={}, type={}, 召唤id在技能召唤表不存在, value2={}",
//                                skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue2());
//                    }
//                }
//                if (skillEffectTemplate.getValue3() > 0) {
//                    SummoningMonsterTemplate summoningMonsterTemplate3 = getResHolder().findValueFromMap(SummoningMonsterTemplate.class, skillEffectTemplate.getValue3());
//                    if (summoningMonsterTemplate3 == null) {
//                        throw new ResourceException("技能效果表错误, id={}, type={}, 召唤id在技能召唤表不存在, value3={}",
//                                skillEffectTemplate.getId(), skillEffectTemplate.getType(), skillEffectTemplate.getValue3());
//                    }
//                }
//            }
//        }
//    }

    private void checkSummon() throws ResourceException {
        for (SummoningMonsterTemplate summoningMonsterTemplate : getResHolder().getListFromMap(SummoningMonsterTemplate.class)) {
            if (summoningMonsterTemplate.getRangePair() == null) {
                throw new ResourceException("技能召唤表错误SummoningMonster, id={} 范围配置错误 RangePair={}",
                        summoningMonsterTemplate.getId(), summoningMonsterTemplate.getRangePair());
            }
            int innerR = summoningMonsterTemplate.getRangePair().getKey();
            int outerR = summoningMonsterTemplate.getRangePair().getValue();
            if (!Ring.checkInnerOuterData(innerR, outerR)) {
                throw new ResourceException("技能召唤表错误SummoningMonster, id={} 范围配置错误, RangePair={}",
                        summoningMonsterTemplate.getId(), summoningMonsterTemplate.getRangePair());
            }
            MonsterTemplate monsterTemplate = getResHolder().findValueFromMap(MonsterTemplate.class, summoningMonsterTemplate.getMonsterID());
            if (monsterTemplate == null) {
                throw new ResourceException("技能召唤表错误, id={}, 野怪id不存在, monsterId={}",
                        summoningMonsterTemplate.getId(), summoningMonsterTemplate.getMonsterID());
            }
        }
    }

    private boolean isNeedCheckWithBuff(SkillEffectTemplate skillEffectTemplate) {
        return skillEffectTemplate.getType() != CommonEnum.SkillEffectType.SET_ADD_BUFF
                && skillEffectTemplate.getType() != CommonEnum.SkillEffectType.SET_DISPERSE
                && skillEffectTemplate.getType() != CommonEnum.SkillEffectType.SET_ADD_BATCH_BUFF;
    }

    private void checkDotBuff(SkillEffectTemplate skillEffectTemplate) throws ResourceException {
        for (IntPairType buffPair : skillEffectTemplate.getGroupBuff1PairList()) {
            checkDotBuff(skillEffectTemplate.getId(), buffPair.getKey());
        }
        for (IntPairType buffPair : skillEffectTemplate.getGroupBuff2PairList()) {
            checkDotBuff(skillEffectTemplate.getId(), buffPair.getKey());
        }
    }

    private void checkDotBuff(int effectId, int buffId) throws ResourceException {
        BattleBuffTemplate buffTemplate = getResHolder().getValueFromMap(BattleBuffTemplate.class, buffId);
        SkillEffectTemplate effectTemplate = checkOrGetDotEffect(effectId, false,
                (checkEffect) -> StringUtils.format("战斗开始和回合开始时，禁止配置伤害类BUFF, 效果id={} buffId={}", checkEffect, buffTemplate.getId()));
        // 如果是多效果的效果，要递归了
        if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
            for (Integer effectSubId : getComboEffects(effectTemplate)) {
                checkOrGetDotEffect(effectSubId, true, (checkEffect) -> StringUtils.format("多重效果中禁止配置多重效果, 效果id={}", checkEffect));
            }
        }
    }

    @NotNull
    private SkillEffectTemplate checkOrGetDotEffect(int effectId, boolean checkMultiple, Function<Integer, String> function) throws ResourceException {
        SkillEffectTemplate effectTemplate = getResHolder().getValueFromMap(SkillEffectTemplate.class, effectId);
        if (checkMultiple && effectTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
            throw new ResourceException(function.apply(effectId));
        }
        if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_DAMAGE) {
            throw new ResourceException(function.apply(effectId));
        }
        return effectTemplate;
    }

    private boolean checkMultipleEffectIdSet(SkillEffectTemplate skillEffectTemplate) {
        Set<Integer> effectIdSet = getMultipleEffectIdSet(skillEffectTemplate);
        boolean needLog = false;
        for (int effectId : effectIdSet) {
            SkillEffectTemplate tmpTemplate = getResHolder().findValueFromMap(SkillEffectTemplate.class, effectId);
            if (tmpTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
                needLog |= checkMultipleEffectIdSet(tmpTemplate);
            } else if (isNeedCheckWithBuff(tmpTemplate)) {
                needLog = true;
                break;
            }
        }
        return needLog;
    }

    private Set<Integer> getMultipleEffectIdSet(SkillEffectTemplate template) {
        Set<Integer> needCheckSkillEffectIdSet = new HashSet<>();
        if (template.getValue1() != 0) {
            needCheckSkillEffectIdSet.add(template.getValue1());
        }
        if (template.getValue2() != 0) {
            needCheckSkillEffectIdSet.add(template.getValue2());
        }
        if (template.getValue3() != 0) {
            needCheckSkillEffectIdSet.add(template.getValue3());
        }
        if (template.getValue4() != 0) {
            needCheckSkillEffectIdSet.add(template.getValue4());
        }
        return needCheckSkillEffectIdSet;
    }

    private boolean isTriggerTypeAfterSettlement(CommonEnum.TriggerType triggerType) {
        return switch (triggerType) {
            case TT_END_BATTLE_NOT_DEFEAT, TT_LEAVE_BUILDING, TT_BEAT_PLAYER, TT_BEAT_MONSTER, TT_BE_TREATED -> true;
            default -> false;
        };
    }



    private void checkAreaSkill() throws ResourceException {
        String filename = ResHolder.getFilename(RangeSkillTemplate.class);
        for (RangeSkillTemplate template : getResHolder().getListFromMap(RangeSkillTemplate.class)) {
            if (template.getLifetime() <= 0) {
                throw new ResourceException("{} id:{}， 回收时间配置错误：{}", filename, template.getId(), template.getLifetime());
            }
            if (template.getCreateRange() == CommonEnum.AreaSkillBornPointType.ASBPT_NONE) {
                throw new ResourceException("{} id:{}， 出生点规则配置不可为空", filename, template.getId());
            }
            if (template.getCreateRange() == CommonEnum.AreaSkillBornPointType.ASBPT_RING_RANDOM) {
                if (template.getRangeList().size() != 2) {
                    throw new ResourceException("{} id:{}， 范围配置错误： 参数不为2. Range:{}", filename, template.getId(), template.getRangeList());
                }
                Integer innerR = template.getRangeList().get(0);
                Integer outerR = template.getRangeList().get(1);
                if (!Ring.checkInnerOuterData(innerR, outerR)) {
                    throw new ResourceException("{} id:{}， 范围配置错误：， Range:{}", filename, template.getId(), template.getRangeList());
                }
            }
            if (template.getMasterRemove() > 1 || template.getMasterRemove() < 0) {
                throw new ResourceException("{} id:{}， 回收配置错误：{}， msterRemove:{}", filename, template.getId(), template.getMasterRemove());
            }
            if (template.getMasterDieRemove() > 1 || template.getMasterDieRemove() < 0) {
                throw new ResourceException("{} id:{}， 回收配置错误：{}， masterDieRemove:{}", filename, template.getId(), template.getMasterDieRemove());
            }
            TroopTemplate valueFromMap = getResHolder().findValueFromMap(TroopTemplate.class, template.getTroopIndex());
            if (valueFromMap == null) {
                throw new ResourceException("{} id:{}， 配置了不存在的部队索引:{}", filename, template.getId(), template.getTroopIndex());
            }
        }
    }

//    private void recursionCheckEffectDepth(List<Pair<Integer, String>> id2Types, int depth) throws ResourceException {
//        Pair<Integer, String> check = id2Types.getLast();
//        Integer id = check.getFirst();
//        String type = check.getSecond();
//        Set<Pair<Integer, String>> collect = new HashSet<>(id2Types);
//        if (id2Types.size() > collect.size()) {

    /// /            throw new ResourceException("有重复效果={}", id2Types));
//        }
//        switch (type) {
//            case "skill": {
//                SkillTemplate skillTemplate = getResHolder().getValueFromMap(SkillTemplate.class, id);
//                for (Integer effectId : skillTemplate.getGroupSkillList()) {
//                    id2Types.add(Pair.of(effectId, "effect"));
//                    recursionCheckEffectDepth(id2Types, depth);
//                }
//                break;
//            }
//            case "buff": {
//                BattleBuffTemplate buffEffectTemplate = getResHolder().getValueFromMap(BattleBuffTemplate.class, id);
//                for (Integer effectId : buffEffectTemplate.getAddSkillEffectList()) {
//                    id2Types.add(Pair.of(effectId, "effect"));
//                    recursionCheckEffectDepth(id2Types, depth);
//                }
//                break;
//            }
//            case "effect": {
//                depth++;
//                if (depth > DEPTH) {
//                    throw new ResourceException("效果={} 引用链过深={}", id, id2Types);
//                }
//                SkillEffectTemplate effectTemplate = getResHolder().getValueFromMap(SkillEffectTemplate.class, id);
//                if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_MULTIPLE_EFFECT) {
//                    if (effectTemplate.getValue1() > 0) {
//                        id2Types.add(Pair.of(effectTemplate.getValue1(), "effect"));
//                        recursionCheckEffectDepth(id2Types, depth);
//                    }
//                    if (effectTemplate.getValue2() > 0) {
//                        id2Types.add(Pair.of(effectTemplate.getValue2(), "effect"));
//                        recursionCheckEffectDepth(id2Types, depth);
//                    }
//                    if (effectTemplate.getValue3() > 0) {
//                        id2Types.add(Pair.of(effectTemplate.getValue3(), "effect"));
//                        recursionCheckEffectDepth(id2Types, depth);
//                    }
//                }
//                if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_FIRE_SKILL) {
//                    id2Types.add(Pair.of(effectTemplate.getValue1(), "skill"));
//                    recursionCheckEffectDepth(id2Types, depth);
//                }
//                if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_ADD_BUFF) {
//                    for (IntPairType buffPair : effectTemplate.getGroupBuff1PairList()) {
//                        id2Types.add(Pair.of(buffPair.getKey(), "buff"));
//                        recursionCheckEffectDepth(id2Types, depth);
//                    }
//                    for (IntPairType buffPair : effectTemplate.getGroupBuff2PairList()) {
//                        id2Types.add(Pair.of(buffPair.getKey(), "buff"));
//                        recursionCheckEffectDepth(id2Types, depth);
//                    }
//                }
//                break;
//            }
//            default: {
//                break;
//            }
//        }
//    }
    @Override
    public String getResName() {
        return "skill";
    }

    /**
     * 获取技能模板
     */
    public SkillConfigTemplate getSkillTemplate(int skillId) {
        return getResHolder().getValueFromMap(SkillConfigTemplate.class, skillId);
    }


    public SkillConfigTemplate getSkillTemplate(int groupId, int level) {
        String key = groupId + "_" + level;
        return skillStore.get(key);
    }


    public SkillRangeTemplate getSkillRangeTemplateOrNull(int templateId) {
        return getResHolder().findValueFromMap(SkillRangeTemplate.class, templateId);
    }

    /**
     * 夹击表
     */
    public double getPincerRatio(int num) {
        if (num > maxPincer) {
            num = maxPincer;
        }
        return getResHolder().getValueFromMap(PincerTemplate.class, num).getBonus();
    }

    /**
     * 获取技能效果模板
     */
    public SkillEffectTemplate getSkillEffectTemplate(int effectId) {
        return getResHolder().getValueFromMap(SkillEffectTemplate.class, effectId);
    }

    public List<BattleBuffTemplate> getRandomBuffTemplateList(SkillEffectTemplate template) {
        List<BattleBuffTemplate> ret = new ArrayList<>();
        //独立概率组
        for (IntPairType e : template.getGroupBuff1PairList()) {
            if (RandomUtils.trigger(e.getValue(), 10000)) {
                BattleBuffTemplate buffTemplate = this.getBattleBuffTemplate(e.getKey());
                ret.add(buffTemplate);
            }
        }
        //共同概率组
        List<IntPairType> groupBuff2PairList = template.getGroupBuff2PairList();
        if (groupBuff2PairList != null && !groupBuff2PairList.isEmpty()) {
            int total = 0;
            for (IntPairType e : groupBuff2PairList) {
                total += e.getValue();
            }
            int index = RandomUtils.nextInt(total);
            for (IntPairType e : groupBuff2PairList) {
                if (index <= e.getValue()) {
                    BattleBuffTemplate buffTemplate = this.getBattleBuffTemplate(e.getKey());
                    ret.add(buffTemplate);
                } else {
                    index -= e.getValue();
                }
            }
        }
        return ret;
    }

    /**
     * 获取buff 模板
     */
    public BattleBuffTemplate getBattleBuffTemplate(int buffId) {
        return getResHolder().getValueFromMap(BattleBuffTemplate.class, buffId);
    }

    public List<Integer> getComboEffects(SkillEffectTemplate template) {
        return comboEffects.getOrDefault(template.getId(), Lists.newArrayList());
    }


    public Pair<String[], String[]> getEffectSelfStatusValue(int effectId) {
        return effectSelfStatusMap.getOrDefault(effectId, new Pair<>(null, null));
    }

    public String[] getEffectTargetStatusValue(int effectId) {
        return effectTargetStatusMap.getOrDefault(effectId, new String[]{});
    }
}
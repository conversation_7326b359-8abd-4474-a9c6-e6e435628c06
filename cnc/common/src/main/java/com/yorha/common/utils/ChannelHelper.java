package com.yorha.common.utils;

import io.netty.channel.Channel;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ChannelHelper {
    private static final Logger LOGGER = LogManager.getLogger(ChannelHelper.class);

    private ChannelHelper() {

    }

    /**
     * 关闭channel统一入口
     *
     * @param channel
     * @param reason
     */
    public static void closeChannel(Channel channel, String reason) {
        if (!channel.isActive()) {
            return;
        }
        LOGGER.info("closeChannel channel={}, reason={}", channel, reason);
        try {
            channel.close();
        } catch (Exception e) {
            LOGGER.error("closeChannel channel={} fail!", channel, e);
        }
    }
}

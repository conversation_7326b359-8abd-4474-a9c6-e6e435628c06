package com.yorha.common.actorservice.msg;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorExceptionMsg;
import com.yorha.common.actor.msg.ActorTimerMsg;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.MsgHelper;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.utils.id.IdFactory;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.CommonEnum.CommunicationWay;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.System;

import java.util.ArrayList;
import java.util.List;

/**
 * ActorContext中真实持有的 msg 信封（mailbox中取出的对象），immutable
 *
 * <AUTHOR>
 */
public class ActorMsgEnvelope {
    /**
     * 发送方的ActorRef
     */
    private final IActorRef sender;

    private final IActorMsg payload;
    /**
     * 接收方的ActorRef
     */
    private IActorRef receiver;
    /**
     * 接收方的ActorRef列表。
     */
    private List<IActorRef> receivers;
    /**
     * 发送方生成的消息id
     */
    private final long msgSeqId;
    /**
     * 位运算，携带一些标记位
     */
    private int tag;
    /**
     * 对应Ask的stage id。
     */
    private Long askStageId = null;

    private static final int TAG_ANSWER = 1 << 1; // 是否是回包
    private static final int TAG_TELL = 1 << 2; // tell
    private static final int TAG_ASK = 1 << 3; // ask
    private static final int TAG_CALL = 1 << 4; // call
    private static final int TAG_CREATE = 1 << 5; // 是否创建actor
    private static final int TAG_DEBUG_LOG = 1 << 6; // 是否开启白名单

    /**
     * 发包时间戳
     */
    private long sendTsMs;

    private ActorMsgEnvelope(IActorRef sender, CommunicationWay way, boolean answer, IActorMsg msg) {
        this.sender = sender;
        this.sendTsMs = SystemClock.nowNative();
        setTagAnswer(answer);
        setTagWay(way);
        this.payload = msg;
        if (msg instanceof ActorTimerMsg) {
            // 定时器就暂时不使用唯一id了
            this.msgSeqId = 0;
        } else {
            this.msgSeqId = IdFactory.nextIdForActorMsg("actor msg");
        }
    }

    private ActorMsgEnvelope(IActorRef sender, CommunicationWay way, boolean answer, IActorMsg msg, long msgSeqId, Long askStageId) {
        this.sender = sender;
        this.sendTsMs = SystemClock.nowNative();
        setTagAnswer(answer);
        setTagWay(way);
        this.payload = msg;
        this.msgSeqId = msgSeqId;
        this.askStageId = askStageId;
    }

    private ActorMsgEnvelope(IActorRef sender, CommunicationWay way, boolean answer, GeneratedMessageV3 messageV3) {
        this(sender, way, answer, new TypedMsg(SsMsgTypes.getTypeFromMsg(messageV3), messageV3));
    }

    private ActorMsgEnvelope(IActorRef sender, int tag, IActorMsg msg, long msgSeqId) {
        this.sendTsMs = SystemClock.nowNative();
        this.sender = sender;
        this.tag = tag;
        this.payload = msg;
        this.msgSeqId = msgSeqId;
    }

    private void setTagAnswer(boolean answer) {
        if (answer) {
            tag |= TAG_ANSWER;
        }
    }

    private void setTagWay(CommunicationWay way) {
        if (way == CommunicationWay.TELL) {
            tag |= TAG_TELL;
        } else if (way == CommunicationWay.ASK) {
            tag |= TAG_ASK;
        } else if (way == CommunicationWay.CALL) {
            tag |= TAG_CALL;
        } else {
            throw new RuntimeException();
        }
    }

    public void setTagCreate(boolean create) {
        if (create) {
            tag |= TAG_CREATE;
        }
    }

    public void setTagDebugLog(boolean isDebugLog) {
        if (isDebugLog) {
            tag |= TAG_DEBUG_LOG;
        }
    }

    public static ActorMsgEnvelope createFromProto(System.YoActorMsgEnvelope yoEnvelope) {
        IActorRef sender = RefFactory.fromPb(yoEnvelope.getSender());
        IActorMsg payload = MsgUtils.parsePayload(yoEnvelope.getPayload());
        ActorMsgEnvelope envelope;
        if (payload != null) {
            envelope = new ActorMsgEnvelope(sender, yoEnvelope.getTag(), payload, yoEnvelope.getMsgSeqId());
        } else {
            throw new GeminiException("unknown payload null");
        }
        if (yoEnvelope.hasReceiver()) {
            envelope.setReceiver(RefFactory.fromPb(yoEnvelope.getReceiver()));
        } else {
            final List<IActorRef> refList = new ArrayList<>(yoEnvelope.getReceiverListCount());
            for (CommonMsg.ActorRefData refData : yoEnvelope.getReceiverListList()) {
                refList.add(RefFactory.fromPb(refData));
            }
            envelope.setReceivers(refList);
        }
        envelope.sendTsMs = yoEnvelope.getSendTsMs();
        if (yoEnvelope.hasAskStageId()) {
            envelope.setAskStageId(yoEnvelope.getAskStageId());
        }
        return envelope;
    }

    public System.YoActorMsgEnvelope toProto() {
        final System.YoActorMsgEnvelope.Builder builder = System.YoActorMsgEnvelope.newBuilder();
        builder.setSender(MsgHelper.buildRefMsg(this.getSender()))
                .setMsgSeqId(getMsgSeqId())
                .setTag(tag);
        if (getReceiver() != null) {
            final IActorRef receiver = getReceiver();
            builder.setReceiver(MsgHelper.buildRefMsg(receiver));
        } else {
            for (IActorRef receiver : getReceivers()) {
                builder.addReceiverList(MsgHelper.buildRefMsg(receiver));
            }
        }
        final IActorMsg payload = getPayload();
        builder.setPayload(MsgUtils.payloadToProto(payload));
        builder.setSendTsMs(this.sendTsMs);
        if (this.getAskStageId() != null) {
            builder.setAskStageId(this.getAskStageId());
        }
        return builder.build();
    }

    public static ActorMsgEnvelope createFromTell(IActorRef sender, GeneratedMessageV3 msg) {
        return new ActorMsgEnvelope(sender, CommunicationWay.TELL, false, msg);
    }

    public static ActorMsgEnvelope createFromTell(IActorRef sender, IActorMsg msg) {
        return new ActorMsgEnvelope(sender, CommunicationWay.TELL, false, msg);
    }

    public static ActorMsgEnvelope createFromCall(IActorRef sender, GeneratedMessageV3 msg) {
        return new ActorMsgEnvelope(sender, CommunicationWay.CALL, false, msg);
    }

    public static ActorMsgEnvelope createFromCall(IActorRef sender, IActorMsg msg) {
        return new ActorMsgEnvelope(sender, CommunicationWay.CALL, false, msg);
    }

    public static ActorMsgEnvelope createFromAsk(IActorRef sender, GeneratedMessageV3 msg) {
        return new ActorMsgEnvelope(sender, CommunicationWay.ASK, false, msg);
    }

    public static ActorMsgEnvelope createFromAsk(IActorRef sender, IActorMsg msg) {
        return new ActorMsgEnvelope(sender, CommunicationWay.ASK, false, msg);
    }

    public static ActorMsgEnvelope createFromAnswer(ActorMsgEnvelope askEnvelope, GeneratedMessageV3 msg) {
        return new ActorMsgEnvelope(askEnvelope.receiver, askEnvelope.getWay(), true,
                new TypedMsg(SsMsgTypes.getTypeFromMsg(msg), msg), askEnvelope.getMsgSeqId(), askEnvelope.getAskStageId());
    }

    public static ActorMsgEnvelope createFromAnswer(ActorMsgEnvelope askEnvelope, IActorMsg msg) {
        return new ActorMsgEnvelope(askEnvelope.receiver, askEnvelope.getWay(), true, msg, askEnvelope.getMsgSeqId(), askEnvelope.getAskStageId());
    }

    public static ActorMsgEnvelope createFromAnswerException(ActorMsgEnvelope askEnvelope, Exception e) {
        return new ActorMsgEnvelope(askEnvelope.receiver, askEnvelope.getWay(), true,
                new ActorExceptionMsg(e), askEnvelope.getMsgSeqId(), askEnvelope.getAskStageId());
    }

    public static ActorMsgEnvelope createFromEnvelopeAndResult(ActorMsgEnvelope envelope, GeneratedMessageV3 msg) {
        final ActorMsgEnvelope msgEnvelope = new ActorMsgEnvelope(envelope.sender, envelope.getWay(), envelope.isAnswer(),
                new TypedMsg(SsMsgTypes.getTypeFromMsg(msg), msg), envelope.getMsgSeqId(), envelope.getAskStageId());
        if (envelope.getReceiver() != null) {
            msgEnvelope.setReceiver(envelope.getReceiver());
        } else {
            msgEnvelope.setReceivers(envelope.getReceivers());
        }
        return msgEnvelope;
    }

    public static ActorMsgEnvelope createFromEnvelopeAndResult(ActorMsgEnvelope envelope, IActorMsg msg) {
        final ActorMsgEnvelope msgEnvelope =
                new ActorMsgEnvelope(envelope.sender, envelope.getWay(), envelope.isAnswer(), msg, envelope.getMsgSeqId(), envelope.getAskStageId());
        if (envelope.getReceiver() != null) {
            msgEnvelope.setReceiver(envelope.getReceiver());
        } else {
            msgEnvelope.setReceivers(envelope.getReceivers());
        }
        return msgEnvelope;
    }

    public static ActorMsgEnvelope createFromAnswerException(final IActorRef sender, final Exception e, final long msgSeqId, final Long askStageId) {
        return new ActorMsgEnvelope(sender, CommunicationWay.ASK, true, new ActorExceptionMsg(e), msgSeqId, askStageId);
    }

    public static ActorMsgEnvelope createFromTransfer(ActorMsgEnvelope envelope, IActorRef receiver) {
        final ActorMsgEnvelope msgEnvelope = new ActorMsgEnvelope(envelope.sender, envelope.getWay(),
                envelope.isAnswer(), envelope.getPayload(), envelope.getMsgSeqId(), envelope.getAskStageId());
        msgEnvelope.setReceiver(receiver);
        return msgEnvelope;
    }

    public void setReceiver(IActorRef receiver) {
        this.receivers = null;
        this.receiver = receiver;
    }

    public void setReceivers(List<IActorRef> receivers) {
        this.receiver = null;
        this.receivers = receivers;
    }

    /**
     * 仅供框架层使用！！！
     *
     * @param askStageId 设置目标GeminiCompletionStage的id。
     */
    public void setAskStageId(Long askStageId) {
        this.askStageId = askStageId;
    }

    public Long getAskStageId() {
        return this.askStageId;
    }

    public long getSendTsMs() {
        return sendTsMs;
    }

    public long getMsgSeqId() {
        return msgSeqId;
    }

    public IActorRef getSender() {
        return sender;
    }

    public IActorRef getReceiver() {
        return receiver;
    }

    public List<IActorRef> getReceivers() {
        return receivers;
    }

    public IActorMsg getPayload() {
        return payload;
    }

    public boolean isAnswer() {
        return (tag & TAG_ANSWER) != 0;
    }

    public boolean isByAsk() {
        return (tag & TAG_ASK) != 0;
    }

    public boolean isAskAnswer() {
        return isByAsk() && isAnswer();
    }

    public boolean isByCall() {
        return (tag & TAG_CALL) != 0;
    }

    public boolean isCallAnswer() {
        return isByCall() && isAnswer();
    }

    public boolean isByTell() {
        return (tag & TAG_TELL) != 0;
    }

    public CommunicationWay getWay() {
        if (isByTell()) {
            return CommunicationWay.TELL;
        } else if (isByCall()) {
            return CommunicationWay.CALL;
        } else if (isByAsk()) {
            return CommunicationWay.ASK;
        } else {
            return CommunicationWay.CW_NONE;
        }
    }

    public boolean isTimer() {
        return payload instanceof ActorTimerMsg;
    }

    public boolean isCreate() {
        return (tag & TAG_CREATE) != 0;
    }

    public boolean isDebugLog() {
        return (tag & TAG_DEBUG_LOG) != 0;
    }

    public String getSimpleName() {
        if (payload instanceof TypedMsg) {
            return MsgUtils.getMsgNameFromSsMsgType(((TypedMsg) payload).getMsgType());
        }
        return ActorMsgUtil.profNameOf(payload);
    }

    @Override
    public String toString() {
        return "Envelope{" +
                "sender=" + sender +
                ", receiver=" + receiver +
                ", seq=" + msgSeqId +
                ", tag=" + tag +
                ", payload=" + payload +
                ", stage=" + askStageId +
                '}';
    }
}

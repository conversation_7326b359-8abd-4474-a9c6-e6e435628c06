package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.TcaplusUtils;
import com.yorha.common.db.tcaplus.option.DeleteByPartKeyOption;
import com.yorha.common.db.tcaplus.result.DeleteByPartKeyResult;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

/**
 * 在tcaplus中按part-key删除全片数据
 *
 * <AUTHOR>
 */
public class TcaplusDeleteByPartKey<T extends Message.Builder> extends TcaplusOperation<T, DeleteByPartKeyOption, DeleteByPartKeyResult> {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusDeleteByPartKey.class);

    public TcaplusDeleteByPartKey(Client fiberTcaplusMgr, T t, DeleteByPartKeyOption option) {
        super(fiberTcaplusMgr, PbFieldMetaCaches.getMetaData(t), t, option);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_DELETE_BY_PARTKEY_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        Record record = request.addRecord();
        configRecordProperty(record);
        setRequestKeys(getReq(), record);
        // https://tcaplusdb.tencent.com/UserGuide/05TcaplusDB_SDK_and_API/01TDR%E8%A1%A8SDK_and_API/01C++_SDK/02%E5%93%8D%E5%BA%94%E5%88%86%E5%8C%85%E9%97%AE%E9%A2%98%E8%AF%B4%E6%98%8E.html
        // DeleteByPartKey 全局索引查询，必然分包，设置无效
        request.setMultiResponseFlag(1);
    }

    private void configRecordProperty(Record record) {
        // 设置版本
        if (getOption().getVersion() >= 0) {
            record.setVersion(getOption().getVersion());
        }
    }

    @Override
    protected DeleteByPartKeyResult buildResult(Response response) {
        throw new NotImplementedException();
    }

    protected DeleteByPartKeyResult buildResult(List<Response> responseList) {
        int code = TcaplusErrorCode.GEN_ERR_ERR.getValue();
        for (final Response value : responseList) {
            code = TcaplusErrorCode.GEN_ERR_SUC.getValue();
            final List<Record> recordList = value.getRecordList() == null ? Collections.emptyList() : value.getRecordList();
            final List<Record> errorRecordList = value.getErrorRecords() == null ? Collections.emptyList() : value.getErrorRecords();
            LOGGER.info("TcaplusDeleteByPartKey, requestId={}, tableName={}, recordSize={}, errorSize={}, code={}",
                    this.getRequestId(), this.getTableName(), recordList.size(), errorRecordList.size(), value.getResult());
            if (value.getResult() != TcaplusErrorCode.GEN_ERR_SUC.getValue()) {
                code = value.getResult();
                break;
            }
        }
        return new DeleteByPartKeyResult(code);
    }

    @Override
    public DeleteByPartKeyResult run() {
        final Request request = this.client.acquireRequest();
        request.setCmd(getType());
        request.setTableName(getFieldMetaData().tableName);
        configRequestProperty(request);
        this.onDbRequest();
        // 处理响应
        List<Response> responseList = TcaplusUtils.requestMultipleResponse(this.client, request, TcaplusUtils.TCAPLUS_TIMEOUT_MS + 1_000);
        final DeleteByPartKeyResult result = this.buildResult(responseList);
        this.onDbResponse(result.getCode());
        return result;
    }

    @Override
    public void runAsync(Consumer<DeleteByPartKeyResult> cb) {
        final Request request = this.client.acquireRequest();
        request.setCmd(getType());
        request.setTableName(getFieldMetaData().tableName);
        configRequestProperty(request);
        this.onDbRequest();

        TcaplusUtils.requestMultipleResponseAsync(this.client, request, (responseList -> {
            final DeleteByPartKeyResult result = this.buildResult(responseList);
            this.onDbResponse(result.getCode());
            cb.accept(result);
        }));
    }
}

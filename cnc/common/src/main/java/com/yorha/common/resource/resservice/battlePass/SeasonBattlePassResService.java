package com.yorha.common.resource.resservice.battlePass;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.server.ServerContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.BpTaskTemplate;
import res.template.SeasonBpScheduleTemplate;
import res.template.TaskPoolTemplate;

import java.util.*;

/**
 * 赛季版通行证配置
 *
 * <AUTHOR>
 */
public class SeasonBattlePassResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(SeasonBattlePassResService.class);

    private final Map<Integer, Integer> battlePassTaskMap = new HashMap<>();

    /**
     * bpId -> open time
     */
    private final Map<Integer, Integer> bpId2OpenTime = new HashMap<>();

    public SeasonBattlePassResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        if (!ServerContext.isZoneServer()) {
            return;
        }
        for (SeasonBpScheduleTemplate template : getResHolder().getListFromMap(SeasonBpScheduleTemplate.class)) {
            int openTime = template.getOpenTime();
            for (IntPairType pair : template.getOpenDelayPairList()) {
                if (pair.getKey() != ServerContext.getZoneId()) {
                    continue;
                }
                openTime += pair.getValue();
            }
            bpId2OpenTime.put(template.getId(), openTime);
        }
        LOGGER.info("BattlePassResService load bpId2OpenTime {}", bpId2OpenTime);
    }

    public int getOpenTime(int bpId) {
        return bpId2OpenTime.get(bpId);
    }

    @Override
    public void checkValid() throws ResourceException {
        if (!ServerContext.isZoneServer()) {
            return;
        }
        // 获取所有task_pool中的任务id
        Set<Integer> taskPool = new HashSet<>();
        for (TaskPoolTemplate template : getResHolder().getListFromMap(TaskPoolTemplate.class)) {
            taskPool.add(template.getId());
        }
        // 检查bpTaskId的任务在不在taskPool中
        for (BpTaskTemplate template : getResHolder().getListFromMap(BpTaskTemplate.class)) {
            if (!taskPool.contains(template.getTaskId())) {
                throw new ResourceException("T_通行证.xlsx bp_task 中的任务不在任务池中 id {}", template.getId());
            }
            battlePassTaskMap.put(template.getId(), template.getTaskId());
        }
        Set<Integer> bpTasks = battlePassTaskMap.keySet();
        for (SeasonBpScheduleTemplate template : getResHolder().getListFromMap(SeasonBpScheduleTemplate.class)) {
            // 检查赛季任务和周任务是不是在bpTask表中
            if (!bpTasks.containsAll(template.getSessionMissionList())) {
                throw new GeminiException("T_通行证.xlsx season_bp_schedule 配置了不存在的赛季任务 id {}", template.getId());
            }
            if (!bpTasks.containsAll(template.getWeeklyMission1List())) {
                throw new GeminiException("T_通行证.xlsx season_bp_schedule 配置了不存在的第一周任务 id {}", template.getId());
            }
            if (!bpTasks.containsAll(template.getWeeklyMission2List())) {
                throw new GeminiException("T_通行证.xlsx season_bp_schedule 配置了不存在的第二周任务 id {}", template.getId());
            }
            if (!bpTasks.containsAll(template.getWeeklyMission3List())) {
                throw new GeminiException("T_通行证.xlsx season_bp_schedule 配置了不存在的第三周任务 id {}", template.getId());
            }
            if (!bpTasks.containsAll(template.getWeeklyMission4List())) {
                throw new GeminiException("T_通行证.xlsx season_bp_schedule 配置了不存在的第四周任务 id {}", template.getId());
            }
            if (!bpTasks.containsAll(template.getWeeklyMission5List())) {
                throw new GeminiException("T_通行证.xlsx season_bp_schedule 配置了不存在的第五周任务 id {}", template.getId());
            }
        }
    }

    public List<Integer> getMissionGroup(int bpId, int groupId) {
        List<Integer> bpTasks;
        SeasonBpScheduleTemplate template = ResHolder.getTemplate(SeasonBpScheduleTemplate.class, bpId);
        switch (groupId) {
            case 0:
                // 赛季任务
                bpTasks = template.getSessionMissionList();
                break;
            case 1:
                // 第一周周任务
                bpTasks = template.getWeeklyMission1List();
                break;
            case 2:
                // 第二周周任务
                bpTasks = template.getWeeklyMission2List();
                break;
            case 3:
                // 第三周周任务
                bpTasks = template.getWeeklyMission3List();
                break;
            case 4:
                // 第四周周任务
                bpTasks = template.getWeeklyMission4List();
                break;
            case 5:
                // 第五周周任务
                bpTasks = template.getWeeklyMission5List();
                break;
            default:
                bpTasks = Collections.emptyList();
                LOGGER.error("BattlePassResService getMissionGroup, illegal groupId, groupId={}", groupId);
        }
        return bpTasks;
    }
}

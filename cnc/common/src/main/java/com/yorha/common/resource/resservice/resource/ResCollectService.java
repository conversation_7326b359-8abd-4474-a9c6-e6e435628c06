package com.yorha.common.resource.resservice.resource;

import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum.CurrencyType;
import res.template.RegionResourceTemplate;
import res.template.ResourceRefreshTemplate;
import res.template.ResourceTemplate;

import java.util.*;

/**
 * 采集相关
 *
 * <AUTHOR>
 */
public class ResCollectService extends AbstractResService {

    public ResCollectService(ResHolder resHolder) {
        super(resHolder);
    }

    private final Map<Integer, StoryData> dataMap = new HashMap<>();

    private static class StoryData {
        /**
         * 资源类型 ->资源等级 ->  templateId
         */
        private final Map<CurrencyType, Map<Integer, Integer>> resTemplate = new HashMap<>();
        /**
         * 州id -> 普通矿补充刷新数量线  黄金矿刷新配置
         */
        private final Map<Integer, RegionResourceTemplate> regionRefreshConfig = new HashMap<>();
        /**
         * 资源带
         */
        private final Map<Integer, List<Integer>> resourceBand = new HashMap<>();
        /**
         * 片等级 -> 资源带等级
         */
        private final Map<Integer, Map<Integer, Integer>> partLevelToBand = new HashMap<>();
        /**
         * 州id -> 黄金资源带
         */
        private final Map<Integer, List<Integer>> goldBand = new HashMap<>();

        private void initGoldBand(RegionResourceTemplate template) throws ResourceException {
            goldBand.computeIfAbsent(template.getRegionId(), (key) -> new ArrayList<>());
            List<Integer> list = goldBand.get(template.getRegionId());
            for (IntPairType levelPair : template.getGoldLevelWeightPairList()) {
                int num = levelPair.getValue();
                if (num > GameLogicConstants.IPPM) {
                    throw new ResourceException("region_gold too big weight config:{}", template.getId());
                }
                for (int i = 0; i < num; i++) {
                    int resourceId = resTemplate.getOrDefault(CurrencyType.DIAMOND, Collections.emptyMap()).getOrDefault(levelPair.getKey(), 0);
                    if (resourceId <= 0) {
                        throw new ResourceException("region_gold has unknown level config:{}", template.getId());
                    }
                    list.add(resourceId);
                }
            }
            if (list.size() > GameLogicConstants.IPPM) {
                throw new GeminiException("warn zeo config too big:{}", template.getId());
            }
        }

        private void initResourceBand(ResourceRefreshTemplate template) throws ResourceException {
            resourceBand.computeIfAbsent(template.getId(), (key) -> new ArrayList<>());
            List<Integer> list = resourceBand.get(template.getId());
            for (IntPairType typePair : template.getTypeWeightPairList()) {
                for (IntPairType levelPair : template.getLevelWeightPairList()) {
                    int num = MathUtils.multiplyExact(typePair.getValue(), levelPair.getValue());
                    if (num > GameLogicConstants.IPPM) {
                        throw new ResourceException("resource_refresh too big weight config:{}", template.getId());
                    }
                    for (int i = 0; i < num; i++) {
                        int resourceId = resTemplate.get(CurrencyType.forNumber(typePair.getKey())).getOrDefault(levelPair.getKey(), 0);
                        if (resourceId <= 0) {
                            throw new ResourceException("resource_refresh has unknown level config:{}", template.getId());
                        }
                        list.add(resourceId);
                    }
                }
            }
            if (list.size() > GameLogicConstants.IPPM) {
                throw new GeminiException("warn zeo config too big:{}", template.getId());
            }
        }
    }

    @Override
    public void load() throws ResourceException {
        // 单个资源田配置
        getResHolder().getListFromMap(ResourceTemplate.class).forEach(it -> {
            Map<CurrencyType, Map<Integer, Integer>> resTemplate = dataMap.computeIfAbsent(it.getStoryId(), k -> new StoryData()).resTemplate;
            if (!resTemplate.containsKey(it.getResType())) {
                resTemplate.put(it.getResType(), new HashMap<>());
            }
            resTemplate.get(it.getResType()).put(it.getResLevel(), it.getId());
        });
        // 单州资源刷新配置  黄金矿刷新配置
        for (RegionResourceTemplate template : getResHolder().getListFromMap(RegionResourceTemplate.class)) {
            StoryData storyData = dataMap.computeIfAbsent(template.getStoryId(), k -> new StoryData());
            storyData.regionRefreshConfig.put(template.getRegionId(), template);
            storyData.initGoldBand(template);
        }
        // 刷新配比
        for (ResourceRefreshTemplate template : getResHolder().getListFromMap(ResourceRefreshTemplate.class)) {
            StoryData storyData = dataMap.computeIfAbsent(template.getStoryId(), k -> new StoryData());
            for (int i = template.getLevelMin(); i <= template.getLevelMax(); i++) {
                storyData.partLevelToBand.computeIfAbsent(template.getRegion(), key -> new HashMap<>()).put(i, template.getId());
            }
            storyData.initResourceBand(template);
        }
    }

    public int getResBuildingTemplate(int storyId, CurrencyType type, int level) {
        StoryData storyData = dataMap.get(storyId);
        if (storyData == null) {
            return 0;
        }
        return storyData.resTemplate.get(type).get(level);
    }

    public int getRandomResourceId(int storyId, int mapId, int partId) {
        if (partId <= 0) {
            return 0;
        }
        StoryData storyData = dataMap.get(storyId);
        if (storyData == null) {
            return 0;
        }
        MapTemplateDataItem mapTemplateDataItem = ResHolder.getResService(MapSubdivisionDataService.class).getMapTemplateDataItem(mapId);
        if (mapTemplateDataItem == null) {
            return 0;
        }
        RegionalAreaSettingTemplate template = mapTemplateDataItem.getValueFromMap(RegionalAreaSettingTemplate.class, partId);
        int regionId = template.getRegionId();
        int partLevel = template.getLevel();
        if (!storyData.partLevelToBand.containsKey(regionId)) {
            return 0;
        }
        int bandLevel = storyData.partLevelToBand.get(regionId).getOrDefault(partLevel, 0);
        if (bandLevel <= 0) {
            return 0;
        }
        List<Integer> list = storyData.resourceBand.get(bandLevel);
        return list.get(RandomUtils.nextInt(list.size()));
    }

    public int getRandomGoldId(int storyId, int regionId) {
        if (regionId < 0) {
            return 0;
        }
        StoryData storyData = dataMap.get(storyId);
        if (storyData == null) {
            return 0;
        }
        List<Integer> list = storyData.goldBand.get(regionId);
        return list.get(RandomUtils.nextInt(list.size()));
    }

    public Map<Integer, RegionResourceTemplate> getRegionResourceTemplateMap(int storyId) {
        StoryData storyData = dataMap.get(storyId);
        if (storyData == null) {
            return null;
        }
        return storyData.regionRefreshConfig;
    }

    public RegionResourceTemplate getRegionResourceTemplate(int storyId, int region) {
        StoryData storyData = dataMap.get(storyId);
        if (storyData == null) {
            return null;
        }
        return storyData.regionRefreshConfig.get(region);
    }

    @Override
    public void checkValid() throws ResourceException {
        // 资源类型检查
        for (ResourceRefreshTemplate template : getResHolder().getListFromMap(ResourceRefreshTemplate.class)) {
            for (IntPairType typePair : template.getTypeWeightPairList()) {
                if (CurrencyType.forNumber(typePair.getKey()) == null) {
                    throw new ResourceException("resource_refresh has unknown CurrencyType config:{}", template.getId());
                }
            }
        }
    }

    public boolean isCollectFromClanRes(int templateId) {
        Map<Integer, ResourceTemplate> resourceMap = ResHolder.getInstance().getMap(ResourceTemplate.class);
        return !resourceMap.containsKey(templateId);
    }
}

package com.yorha.common.utils;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yorha.common.qlog.json.reward.RewardConfig;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */

public class QlogUtils {
    private QlogUtils() {
    }

    /**
     * 奖励转换成json
     */
    public static String transReward2String(List<IntPairType> rewards) {
        List<RewardConfig> list = new ArrayList<>();
        for (IntPairType reward : rewards) {
            RewardConfig rewardConfig = new RewardConfig();
            rewardConfig.setIGoodsId(reward.getKey());
            rewardConfig.setiCount(reward.getValue());
            list.add(rewardConfig);
        }
        return JsonUtils.toJsonString(list);
    }

    public static <T> String transCollection2ArrayString(Collection<T> list) {
        Collection<String> ret = Lists.newArrayList();
        for (T t : list) {
            ret.add(String.valueOf(t));
        }
        return transCollectionString2ArrayString(ret);
    }

    public static String transCollectionString2ArrayString(Collection<String> list) {
        return String.join(",", list);
    }

    public static String pointToString(Struct.Point point) {
        return point.getX() + "," + point.getY();
    }

    public static String driveTrafficTypeToString(CommonEnum.DriveTrafficType driveTrafficType) {

        switch (driveTrafficType) {
            case DTAT_NORMAL:
                return "normally_distributed";
            case DTAT_SERVER_UNFULLFILLED:
                return "unnormal_server_unfullfilled";
            case DTAT_SERVER_FULLFILLED:
                return "unnormal_server_fullfilled";
            case DTAT_MANUAL_SELECT:
                return "manually_select";
            default:
                return "distribution_error";
        }
    }

    public static String hardwareLevelToJsonString(final int hardwareLevel) {
        ImmutableMap<String, Object> json = ImmutableMap.of(
                "device level", DriveTrafficUtils.getTrafficLevelString(hardwareLevel)
        );
        return JsonUtils.toJsonString(json);
    }
}

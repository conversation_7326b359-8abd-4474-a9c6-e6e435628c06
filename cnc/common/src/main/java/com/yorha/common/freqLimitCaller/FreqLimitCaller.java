package com.yorha.common.freqLimitCaller;

import com.yorha.common.actor.IActorWithTimer;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.util.Strings;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;


/**
 * 限频操作（指定intervalMs时间内仅可执行maxCount的操作）
 *
 * <AUTHOR>
 */
public class FreqLimitCaller {
    private static final Logger LOGGER = LogManager.getLogger(FreqLimitCaller.class);

    private final String extraName;
    private final long intervalMs;
    private final int maxCount;
    private final IActorWithTimer owner;
    private final TimerReasonType reasonType;
    private final Runnable runnable;
    private long lastRunTimeMs;
    private long currentCount;
    private ActorTimer runFuture = null;
    private final String entityId;
    private boolean needNextRun = false;
    private boolean isRunning = false;
    private int taskId = 0;

    /**
     * @param reasonType 任务名
     * @param intervalMs 间隔ms
     * @param maxCount   间隔ms内最多执行次数
     * @param runnable   任务
     * @param owner      IActorTimerOwner
     * @param entityId   entityId
     */
    public FreqLimitCaller(TimerReasonType reasonType, long intervalMs, int maxCount, Runnable runnable, final IActorWithTimer owner, final String entityId) {
        this(reasonType, null, intervalMs, maxCount, runnable, owner, entityId);
    }

    /**
     * 当同entityId相等，reasonType相等，需要引入第三个变量（jobName）来额外标识一个timer
     *
     * @param reasonType 任务名
     * @param extraName  额外的任务名
     * @param intervalMs 间隔ms
     * @param maxCount   间隔ms内最多执行次数
     * @param runnable   任务
     * @param owner      IActorTimerOwner
     * @param entityId   entityId
     */
    public FreqLimitCaller(
            TimerReasonType reasonType,
            @Nullable String extraName,
            long intervalMs,
            int maxCount,
            Runnable runnable,
            final IActorWithTimer owner,
            final String entityId
    ) {
        this.extraName = extraName != null ? extraName : Strings.EMPTY;
        this.reasonType = reasonType;
        this.intervalMs = intervalMs;
        this.maxCount = maxCount;
        this.runnable = runnable;
        this.owner = owner;
        this.entityId = entityId;
        // 为了削峰，防止同一时间的大量落库。设置上次执行时间为当前，且达到最大次数，保证下次落库一定走延迟落库。
        this.lastRunTimeMs = SystemClock.nowNative();
        this.currentCount = this.maxCount;
    }

    /**
     * 正常执行
     */
    public void run() {
        if (this.runFuture != null) {
            long lastRunDurationMs = SystemClock.nowNative() - this.lastRunTimeMs;
            if (lastRunDurationMs <= this.intervalMs * 2) {
                return;
            }
            // 超出时间没执行，可能丢消息了
            LOGGER.warn("FreqLimitCaller run runFuture seems not running? intervalMs={}, lastRunDurationMs={}", this.intervalMs, lastRunDurationMs);
        }

        if (canImmediateRun()) {
            runImmediate(this.taskId);
            return;
        }
        int curTaskId = this.taskId;
        if (this.runFuture != null) {
            LOGGER.warn("FreqLimitCaller in case repeat add timer, should consider why, lastRunTimeMs={}, currentCount={}, isRunning={}, intervalMs={}",
                    this.lastRunTimeMs, this.currentCount, this.isRunning, this.intervalMs);
            this.cancelTimer();
        }
        final long curIntervalMs;
        // 首次任务，随机偏移
        if (this.taskId == 0) {
            curIntervalMs = RandomUtils.nextLong(this.intervalMs) + 1;
        } else {
            curIntervalMs = this.intervalMs;
        }
        this.runFuture = this.owner.addTimer(this.entityId + this.extraName, this.reasonType,
                () -> this.runImmediate(curTaskId), curIntervalMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 立即执行
     */
    private void runImmediate(int taskId) {
        if (taskId != this.taskId) {
            LOGGER.info("FreqLimitCaller runImmediate is canceled, skip taskId={}", taskId);
            return;
        }
        this.cancelTimer();
        //正在运行中，排队
        if (this.isRunning) {
            this.needNextRun = true;
            return;
        }
        try {
            this.isRunning = true;
            if (this.runnable == null) {
                LOGGER.error("FreqLimitCaller runImmediate runnable null");
            } else {
                this.runnable.run();
            }
        } finally {
            this.taskId++;
            this.lastRunTimeMs = SystemClock.nowNative();
            this.isRunning = false;
            if (this.needNextRun) {
                this.needNextRun = false;
                runImmediate(this.taskId);
            }
        }
    }

    /**
     * 停止已设置的计时器运行
     */
    public void stopTimer() {
        this.taskId++;
        this.cancelTimer();
    }

    /**
     * 取消定时器
     */
    private void cancelTimer() {
        if (this.runFuture != null) {
            if (!this.runFuture.isCanceled()) {
                this.runFuture.cancel();
            }
            this.runFuture = null;
        }
    }

    /**
     * 是否有待执行任务
     */
    public boolean isRunFuture() {
        return this.runFuture != null;
    }

    /**
     * 是否可立即执行
     *
     * @return true 可立即执行
     */
    private boolean canImmediateRun() {
        if ((SystemClock.nowNative() - this.lastRunTimeMs) >= this.intervalMs) {
            this.currentCount = 0;
        }
        if (this.currentCount >= this.maxCount) {
            return false;
        }
        ++this.currentCount;
        return true;
    }
}
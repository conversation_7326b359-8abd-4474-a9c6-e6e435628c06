package com.yorha.common.concurrent.executor;

import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.*;

/**
 * 通用线程池。
 *
 * <AUTHOR>
 */
public class GeminiThreadPoolExecutor extends ThreadPoolExecutor {
    private static final Logger LOGGER = LogManager.getLogger(GeminiThreadPoolExecutor.class);
    private static final RejectedExecutionHandler REJECTED_EXECUTION_HANDLER =
            (r, executor) -> LOGGER.error("thread executor {} refuse {}, executor is_shutdown={} or queue is full",
                    ((GeminiThreadPoolExecutor) executor).getName(), r, executor.isShutdown());

    private final String name;

    private GeminiThreadPoolExecutor(Builder builder) {
        super(builder.threadSize, builder.threadSize, 0, TimeUnit.SECONDS, builder.newBlocking<PERSON><PERSON><PERSON>(),
                builder.newThreadFactory(), builder.rejectedExecutionHandler);
        this.name = builder.name;
    }


    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        // 每个任务执行前
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        // 每个任务执行后
    }

    /**
     * 线程池终止时候的回调。
     */
    @Override
    protected void terminated() {
        LOGGER.info("executor {} terminated!", this);
    }

    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "GeminiThreadPoolExecutor{" +
                "name='" + name + '\'' +
                ", threadPool=" + super.toString() +
                '}';
    }

    public static Builder newBuilder(final String name, final int threadSize) {
        final Builder builder = new Builder();
        return builder.name(name).threadSize(threadSize);
    }

    public static class Builder {
        private String name = null;
        private int threadSize = 0;
        private int taskSize = 0;
        private boolean isDaemon = false;
        private RejectedExecutionHandler rejectedExecutionHandler = REJECTED_EXECUTION_HANDLER;
        private Thread.UncaughtExceptionHandler uncaughtExceptionHandler = ConcurrentHelper.UNCAUGHT_EXCEPTION_HANDLER;

        private Builder() {
        }

        /**
         * 设置线程池名称。
         *
         * @param name 名称。
         * @return Builder。
         */
        public Builder name(final String name) {
            if (StringUtils.isEmpty(name)) {
                throw new NullPointerException("name is empty");
            }
            this.name = name;
            return this;
        }

        /**
         * 设置线程池的大小。
         *
         * @param threadSize 线程数量
         * @return Builder。
         */
        public Builder threadSize(final int threadSize) {
            if (threadSize <= 0) {
                throw new IllegalArgumentException("threadSize <= 0");
            }
            this.threadSize = threadSize;
            return this;
        }

        /**
         * 线程池过载的保护，导致丢弃消息时候的回调。
         *
         * @param handler 回调。
         * @return Builder。
         */
        public Builder rejectExecuteHandler(RejectedExecutionHandler handler) {
            if (handler == null) {
                throw new NullPointerException("handler is null!");
            }
            this.rejectedExecutionHandler = handler;
            return this;
        }

        /**
         * 线程异常状态未处理时候的回调。
         *
         * @param handler 回调。
         * @return Builder。
         */
        public Builder uncaughtExecutionHandler(Thread.UncaughtExceptionHandler handler) {
            if (handler == null) {
                throw new NullPointerException("handler is null!");
            }
            this.uncaughtExceptionHandler = handler;
            return this;
        }

        /**
         * 线程池可缓存的任务数量上限。
         *
         * @param taskSize taskSize == 0 无上限队列；> 0 可缓存的任务上限。
         * @return Builder。
         */
        public Builder taskSize(final int taskSize) {
            if (taskSize < 0) {
                throw new IllegalArgumentException("taskSize < 0");
            }
            this.taskSize = taskSize;
            return this;
        }

        /**
         * 是否守护线程的线程池。
         *
         * @param isDaemon true 守护线程；false 非守护线程。
         * @return Builder。
         */
        public Builder daemon(final boolean isDaemon) {
            this.isDaemon = isDaemon;
            return this;
        }

        private BlockingQueue<Runnable> newBlockingQueue() {
            // 准备queue
            final BlockingQueue<Runnable> queue;
            if (this.taskSize == 0) {
                queue = new LinkedBlockingQueue<>();
            } else {
                queue = new LinkedBlockingQueue<>(this.taskSize);
            }
            return queue;
        }

        private ThreadFactory newThreadFactory() {
            return ConcurrentHelper.newThreadFactory(this.name, this.isDaemon, this.uncaughtExceptionHandler);
        }

        public GeminiThreadPoolExecutor build() {
            return new GeminiThreadPoolExecutor(this);
        }
    }
}

package com.yorha.common.utils.shape;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.vector.Vector2f;

/**
 * <AUTHOR>
 */
public class Line implements Shape {

    private final Point srcPoint;
    private final Point endPoint;

    private Line(Point srcPoint, Point endPoint) {
        this.srcPoint = srcPoint;
        this.endPoint = endPoint;
    }

    private Line(int srcX, int srcY, int endX, int endY) {
        this.srcPoint = Point.valueOf(srcX, srcY);
        this.endPoint = Point.valueOf(endX, endY);
    }

    public static Line valueOf(Point srcPoint, Point endPoint) {
        return new Line(srcPoint, endPoint);
    }

    public static Line valueOf(int srcX, int srcY, int endX, int endY) {
        return new Line(srcX, srcY, endX, endY);
    }

    @Override
    public boolean containsPoint(long x, long y) {
        return (x - srcPoint.getX()) * (srcPoint.getY() - endPoint.getY()) == (srcPoint.getX() - endPoint.getX()) * (y - srcPoint.getY());
    }

    public long getLength() {
        double a = Math.pow(endPoint.getX() - srcPoint.getX(), 2) + Math.pow((endPoint.getY() - srcPoint.getY()), 2);
        return (long) Math.sqrt(Math.abs(a));
    }

    public Point getSrcPoint() {
        return srcPoint;
    }

    public Point getEndPoint() {
        return endPoint;
    }

    private Vector2f selfVector2f = null;

    public Vector2f getSelfVector2f() {
        if (selfVector2f != null) {
            return selfVector2f;
        }
        selfVector2f = Vector2f.getVectorFromPointToPoint(srcPoint, endPoint);
        return selfVector2f;
    }

    /**
     * 获取从起点向终点x长度处的点
     */
    public Point getPointByDisToSrcPoint(float t) {
        if (t < 0 || t > getLength()) {
            return null;
        }
        Vector2f v = getSelfVector2f().unitization();
        return Point.valueOf((int) (srcPoint.getX() + t * v.getX()), (int) (srcPoint.getY() + t * v.getY()));
    }

    public Point getPointByDisFromSrc(float t) {
        if (t < 0) {
            return null;
        }
        Vector2f v = getSelfVector2f().unitization();
        return Point.valueOf((int) (srcPoint.getX() + t * v.getX()), (int) (srcPoint.getY() + t * v.getY()));
    }

    private AABB aabb = null;

    @Override
    public AABB getAABB() {
        if (aabb == null) {
            int left = srcPoint.getX();
            int right = srcPoint.getX();
            int top = srcPoint.getY();
            int bottom = srcPoint.getY();
            if (endPoint.getX() < left) {
                left = endPoint.getX();
            } else if (endPoint.getX() > right) {
                right = endPoint.getX();
            }
            if (endPoint.getY() < top) {
                top = endPoint.getY();
            } else if (endPoint.getY() > bottom) {
                bottom = endPoint.getY();
            }
            aabb = new AABB(left, top, right, bottom);
        }
        return aabb;
    }

    @Override
    public Point getRandomPoint() {
        int a = RandomUtils.nextInt(100);
        long sx = srcPoint.getX();
        long es = endPoint.getX();
        long sy = srcPoint.getY();
        long ey = endPoint.getY();
        long x = (sx * a + es * (100 - a)) / 100;
        long y = (sy * a + ey * (100 - a)) / 100;
        return Point.valueOf(x, y);
    }

    /**
     * 点在线段上或线段延长线上
     */
    public boolean isOnRayWithPoint(Point point) {
        int x1 = srcPoint.getX();
        int x2 = endPoint.getX();
        int x3 = point.getX();
        int y1 = srcPoint.getY();
        int y2 = endPoint.getY();
        int y3 = point.getY();
        return (x2 - x1) * (y3 - y2) == (x3 - x2) * (y2 - y1);
    }

    public Point getCrossWithLine(Line line) {
        double x1 = srcPoint.getX();
        double y1 = srcPoint.getY();
        double x2 = endPoint.getX();
        double y2 = endPoint.getY();
        double x3 = line.getSrcPoint().getX();
        double y3 = line.getSrcPoint().getY();
        double x4 = line.getEndPoint().getX();
        double y4 = line.getEndPoint().getY();
        // 两个坐标轴线段会有漏洞!!!
        double d = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
        if (d == 0) {
            return null;
        }
        double xi = ((x3 - x4) * (x1 * y2 - y1 * x2) - (x1 - x2) * (x3 * y4 - y3 * x4)) / d;
        double yi = ((y3 - y4) * (x1 * y2 - y1 * x2) - (y1 - y2) * (x3 * y4 - y3 * x4)) / d;
        if (xi < Math.min(x1, x2) || xi > Math.max(x1, x2)) {
            return null;
        }
        if (xi < Math.min(x3, x4) || xi > Math.max(x3, x4)) {
            return null;
        }
        return Point.valueOf((int) xi, (int) yi);
    }
}

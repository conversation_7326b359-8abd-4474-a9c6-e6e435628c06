package com.yorha.common.asset;

import com.yorha.game.gen.prop.YoAssetDescProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB;

/**
 * 单个财产unit
 */
public abstract class AssetDesc {

    private final AssetType type;

    protected AssetDesc(AssetType type) {
        this.type = type;
    }

    public abstract int getId();

    public abstract long getAmount();

    public AssetType getType() {
        return type;
    }

    protected final boolean similar(AssetDesc another) {
        return this.getType() == another.getType() && this.getId() == another.getId();
    }

    public AssetDesc plus(AssetDesc another) {
        if (!similar(another)) {
            throw new GeminiException("{} cannot plus {}", this, another);
        }
        return plusImpl(another);
    }

    public abstract AssetDesc plusImpl(AssetDesc another);

    /**
     * 复制一个相同的财产unit,数量指定为[amount]
     */
    public abstract AssetDesc copyWithAmount(long amount);

    public YoAssetDescProp toProp() {
        return new YoAssetDescProp()
                .setType(this.type.getIndex())
                .setId(getId())
                .setAmount(getAmount());
    }

    public Struct.YoAssetDesc toProto() {
        return Struct.YoAssetDesc.newBuilder()
                .setType(this.type.getIndex())
                .setId(getId())
                .setAmount(getAmount())
                .build();
    }

    public StructPB.YoAssetDescPB toPb() {
        return StructPB.YoAssetDescPB.newBuilder()
                .setType(this.type.getIndex())
                .setId(getId())
                .setAmount(getAmount())
                .build();
    }


}

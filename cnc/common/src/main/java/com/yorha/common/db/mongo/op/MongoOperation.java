package com.yorha.common.db.mongo.op;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.mongodb.MongoCommandException;
import com.mongodb.client.model.Projections;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.tencent.tcaplus.client.Request;
import com.yorha.common.db.IDbOperation;
import com.yorha.common.db.mongo.subscriber.FiberSubscriber;
import com.yorha.common.db.mongo.subscriber.IBasicSubscriber;
import com.yorha.common.db.mongo.utils.MongoUtils;
import com.yorha.common.db.tcaplus.TcaplusDBException;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;
import com.yorha.common.db.tcaplus.op.FieldMetaData;
import com.yorha.common.enums.error.DbCode;
import com.yorha.common.exception.DataBaseException;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.conversions.Bson;
import org.reactivestreams.Publisher;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * mongo操作的基类，所有mongo操作的封装都源自于此
 *
 * <AUTHOR>
 */
public abstract class MongoOperation<Req, Option, Response, AfterConsumeResponse, Result extends GameDbResp> extends IDbOperation<Req, Option, Result> {
    private static final Logger LOGGER = LogManager.getLogger(MongoOperation.class);
    protected static final TcaplusErrorCode DEFAULT_ERROR_CODE = TcaplusErrorCode.SVR_ERR_FAIL_PROXY_STOPPING;
    /**
     * mongoDatabase
     */
    protected final MongoDatabase database;


    public MongoOperation(MongoDatabase database, FieldMetaData fieldMetaData, Req req, Option option) {
        super(req, option, fieldMetaData);
        this.database = database;
    }

    /**
     * 获得subscriber
     */
    protected abstract IBasicSubscriber<Response, AfterConsumeResponse> getSubscriber();

    /**
     * mongo操作
     */
    protected abstract Publisher<Response> getPublisher();

    /**
     * 从Db的Response构建一个Result结果
     *
     * @param response Db返回包
     * @return Result类型对象
     */
    protected abstract Result buildResult(AfterConsumeResponse response);

    /**
     * mongo出错时构建Result
     *
     * @return Result类型对象
     */
    protected abstract Result onMongoError();

    /**
     * 驱动Operation异步执行的的主体执行流函数.
     *
     * @param cb 回调.
     */
    @Override
    public void runAsync(Consumer<Result> cb) {
        final IBasicSubscriber<Response, AfterConsumeResponse> subscriber = this.getSubscriber();
        this.getPublisher().subscribe(subscriber);
        this.onDbRequest();
        subscriber.afterConsume((response, throwable) -> {
            final Result result;
            if (throwable != null) {
                if (this.isUpsertDuplicateError(throwable)) {
                    this.runAsync(cb);
                    this.onDbResponse(DEFAULT_ERROR_CODE.getValue());
                    return;
                }
                result = this.onMongoError();
                LOGGER.error("runAsync fail, throwable=", throwable);
            } else {
                result = buildResult(response);
            }
            this.onDbResponse(result.getCode());
            cb.accept(result);
        });
        subscriber.start();
    }

    private boolean isUpsertDuplicateError(Throwable e) {
        if (!(this instanceof MongoUpsert)) {
            return false;
        }
        if (!(e instanceof MongoCommandException)) {
            return false;
        }
        return ((MongoCommandException) e).getErrorCode() == 11000;

    }


    /**
     * 驱动Operation进行执行的的主体执行流函数
     *
     * @return 执行结果
     */
    @Override
    public Result run() {
        this.onDbRequest();
        final IBasicSubscriber<Response, AfterConsumeResponse> subscriber = this.getSubscriber();
        this.getPublisher().subscribe(subscriber);

        final AfterConsumeResponse response;

        try {
            response = new FiberSubscriber<>(subscriber).run(MongoUtils.MONGO_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            if (this.isUpsertDuplicateError(e)) {
                this.onDbResponse(DEFAULT_ERROR_CODE.getValue());
                return this.run();
            }
            LOGGER.error("MongoOperation run fail, e=", e);
            throw new DataBaseException(e);
        }
        Result result = buildResult(response);
        this.onDbResponse(result.getCode());
        return result;
    }

    /**
     * 设置默认值的工具方法
     *
     * @param req 请求
     * @param <T> 待返回类型
     * @return T类型返回对象
     */
    @SuppressWarnings("unchecked")
    protected static <T extends Message.Builder> T buildDefaultValue(T req) {
        return (T) req.getDefaultInstanceForType().newBuilderForType();
    }

    protected void checkFieldNames(@Nullable final List<String> fieldNames, @Nonnull final Descriptors.Descriptor requestDescriptor) {
        if (fieldNames == null) {
            return;
        }
        for (final String fieldName : fieldNames) {
            if (requestDescriptor.findFieldByName(fieldName) == null) {
                throw new DataBaseException(DbCode.DB_UNKNOWN_FIELD, StringUtils.format("checkFieldNames tableName={} unknown fieldName={}", this.getTableName(), fieldName));
            }
        }
    }

    protected void addRequestedFields(@Nonnull final List<String> fieldNames, @Nonnull final Descriptors.Descriptor requestDescriptor, @Nonnull final Request request) {
        for (final String fieldName : fieldNames) {
            if (requestDescriptor.findFieldByName(fieldName) == null) {
                throw new TcaplusDBException(StringUtils.format("addRequestedFields tableName={} unknown fieldName={}", this.getTableName(), fieldName));
            }
            request.addFieldName(fieldName);
            this.addRequestBytes(fieldName.length());
        }
    }

    protected Bson buildProjection(@Nullable final List<String> fieldNames) {
        final List<String> keyFieldNames = this.getFieldMetaData().keyFieldNames;
        if (fieldNames == null) {
            return Projections.fields(Projections.include(keyFieldNames), Projections.excludeId());
        }
        return Projections.fields(Projections.include(keyFieldNames), Projections.include(fieldNames), Projections.excludeId());
    }


}

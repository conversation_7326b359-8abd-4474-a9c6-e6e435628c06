package com.yorha.common.actor.dispatcher;

import com.yorha.common.actor.mailbox.IRunnableActorMailbox;

/**
 * 调度Actor调度器的接口集合。
 *
 * <AUTHOR>
 */
public interface IMailboxDispatcher {
    /**
     * 保证在整个ActorSystem中唯一。
     *
     * @return 调度器名称。
     */
    String getName();

    /**
     * 单次调度的连续执行次数。
     * <= 0 代表无限处理消息，独占式! > 0 代表一次调度最多处理的消息数量。
     *
     * @return 调度器吞吐量。
     */
    int getThroughput();

    /**
     * 调度一个系统任务。
     *
     * @param mb 任务。
     */
    void schedule(IRunnableActorMailbox mb);

    /**
     * 用于控制系统计算资源数量，并行度。
     *
     * @param parallelism 并行度。
     */
    void setParallelism(int parallelism);

    /**
     * 并行度，实际使用的物理线程数。
     *
     * @return 并行度。
     */
    int getParallelism();

    /**
     * 当前协程数量。
     *
     * @param actorRole actor角色。
     * @return 返回当前协程数量。
     */
    int getFiberCnt(String actorRole);

    /**
     * 单次调度存活时间，单位秒。<= 0 代表不等待，> 0 代表等待时长（单位秒）。
     *
     * @return 存活时间。
     */
    int getKeepAliveSec();

    /**
     * 关闭调度器。
     */
    void shutdown();
}

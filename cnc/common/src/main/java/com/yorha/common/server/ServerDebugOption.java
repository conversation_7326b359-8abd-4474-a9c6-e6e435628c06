package com.yorha.common.server;

import com.yorha.common.server.config.ClusterConfigUtils;

/**
 * 进程调试开关，用于平时gm、压测等
 */
public class ServerDebugOption {

    /**
     * 是否开启连地检查，默认开启
     */
    private volatile boolean isOpenAdjoinCheck = true;
    /**
     * 建造军团建筑时是否只需要检查资源
     */
    private volatile boolean isOnlyCheckResourceWhenBuild = false;
    /**
     * 是否用于战斗压测、客户端千人同屏测试
     */
    private volatile boolean isBattleTestServer = false;
    /**
     * 聊天无cd
     */
    private volatile boolean chatNoCd = false;
    /**
     * 行军无视关卡归属
     */
    private volatile boolean isIgnoreCrossingOwner = false;
    /**
     * 是否战斗无伤
     */
    private volatile boolean isOpenNoDead = false;
    /**
     * 是否敏感词检测
     */
    private volatile boolean isTextFilter = true;
    /**
     * 是否是移民测试
     */
    private volatile boolean isMigrateTest = false;
    /**
     * gvg报名不校验军团中心数量
     */
    private volatile boolean isNoCheckGvgCenter = false;
    /**
     * 是否不限制英灵殿接口的调用时机
     */
    private volatile boolean debugOpenValhalla = false;
    /**
     * 非国王也可发送全服邮件
     */
    private volatile boolean freeSendZoneMail = false;
    /**
     * 跳过联盟权限校验
     */
    private volatile boolean skipClanPermission = false;
    /**
     * 邮件收藏无上限
     */
    private volatile boolean mailStoreFree = false;

    /**
     * gm是否打开
     */
    public boolean isGmSwitch() {
        return ClusterConfigUtils.getWorldConfig().getBooleanItem("gm_switch");
    }

    public boolean isBattleTestServer() {
        if (ServerContext.getWorldId() == 44) {
            return true;
        }
        return isBattleTestServer;
    }

    public void setBattleTestServer(boolean battleTestServer) {
        this.isBattleTestServer = battleTestServer;
    }

    public boolean isOpenAdjoinCheck() {
        return isOpenAdjoinCheck;
    }

    public void setIsOpenAdjoinCheck(boolean isOpenAdjoinCheck) {
        this.isOpenAdjoinCheck = isOpenAdjoinCheck;
    }

    public boolean isOnlyCheckResourceWhenBuild() {
        return isOnlyCheckResourceWhenBuild;
    }

    public void setIsOnlyCheckResourceWhenBuild(boolean isOnlyCheckResourceWhenBuild) {
        this.isOnlyCheckResourceWhenBuild = isOnlyCheckResourceWhenBuild;
    }

    public boolean isChatNoCd() {
        return chatNoCd;
    }

    public void setChatNoCd(boolean chatNoCd) {
        this.chatNoCd = chatNoCd;
    }

    public void setIgnoreCrossingOwner(boolean ignoreCrossingOwner) {
        this.isIgnoreCrossingOwner = ignoreCrossingOwner;
    }

    public void setGvgNoCheckClanCenter(boolean noCheck) {
        this.isNoCheckGvgCenter = noCheck;
    }

    public boolean isIgnoreCrossingOwner() {
        return isIgnoreCrossingOwner;
    }

    public boolean isOpenNoDead() {
        return isOpenNoDead;
    }

    public void setIsOpenNoDead(boolean isOpenNoDead) {
        this.isOpenNoDead = isOpenNoDead;
    }

    public boolean isTextFilter() {
        return isTextFilter;
    }

    public void setIsTextFilter(boolean isTextFilter) {
        this.isTextFilter = isTextFilter;
    }

    public boolean isMigrateTest() {
        return isMigrateTest;
    }

    public void setIsMigrateTest(boolean isMigrateTest) {
        this.isMigrateTest = isMigrateTest;
    }

    public boolean isNoCheckGvgCenter() {
        return isNoCheckGvgCenter;
    }

    public void setDebugOpenValhalla(boolean open) {
        debugOpenValhalla = open;
    }

    public boolean isDebugOpenValhalla() {
        return debugOpenValhalla;
    }

    public void setFreeSendZoneMail(final boolean freeSendZoneMail) {
        this.freeSendZoneMail = freeSendZoneMail;
    }

    public boolean isFreeSendZoneMail() {
        return freeSendZoneMail;
    }

    public boolean isSkipClanPermission() {
        return skipClanPermission;
    }

    public void setSkipClanPermission(boolean skipClanPermission) {
        this.skipClanPermission = skipClanPermission;
    }

    public boolean isMailStoreFree() {
        return mailStoreFree;
    }

    public void setMailStoreFree(boolean mailStoreFree) {
        this.mailStoreFree = mailStoreFree;
    }
}

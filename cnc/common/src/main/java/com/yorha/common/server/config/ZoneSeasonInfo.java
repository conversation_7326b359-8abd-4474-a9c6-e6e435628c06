package com.yorha.common.server.config;

import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2024/6/19
 */
public class ZoneSeasonInfo {
    private static final Logger LOGGER = LogManager.getLogger(ZoneSeasonInfo.class);

    private volatile CommonEnum.ZoneSeason season;
    private volatile CommonEnum.ZoneSeasonStage seasonStage;
    private volatile long stageEnterTsMs;

    public ZoneSeasonInfo() {
        this.season = CommonEnum.ZoneSeason.ZS_NONE;
        this.seasonStage = CommonEnum.ZoneSeasonStage.ZSS_NONE;
        this.stageEnterTsMs = 0;
    }

    public void setSeason(CommonEnum.ZoneSeason season) {
        LOGGER.info("ZoneSeasonInfo setSeason old={} new={}", this.season, season);
        this.season = season;
    }

    public void setSeasonStage(CommonEnum.ZoneSeasonStage seasonStage, long stageEnterTsMs) {
        LOGGER.info("ZoneSeasonInfo setSeasonStage oldSeasonStage={} oldStageEnterTsMs={} newSeasonStage={} newStageEnterTsMs={}",
                this.seasonStage, this.stageEnterTsMs, seasonStage, stageEnterTsMs);
        this.seasonStage = seasonStage;
        this.stageEnterTsMs = stageEnterTsMs;
    }

    public CommonEnum.ZoneSeason getSeason() {
        return season;
    }

    public CommonEnum.ZoneSeasonStage getSeasonStage() {
        return seasonStage;
    }

    public long getStageEnterTsMs() {
        return stageEnterTsMs;
    }

    @Override
    public String toString() {
        return "ZoneSeasonInfo{" +
                "season=" + season +
                ", seasonStage=" + seasonStage +
                ", stageEnterTsMs=" + stageEnterTsMs +
                '}';
    }
}

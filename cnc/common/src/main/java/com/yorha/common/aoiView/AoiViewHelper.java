package com.yorha.common.aoiView;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.ActorConfigUtils;
import com.yorha.common.utils.shape.AABB;
import com.yorha.proto.Entity;
import com.yorha.proto.SsAoiView;
import com.yorha.proto.StructPB.PointPB;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.WorldLayerTemplate;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 主世界视野接口
 * 目前原则上 一个scene对应一个缩略层视野actor
 *
 * <AUTHOR>
 */
public class AoiViewHelper {
    private static final Logger LOGGER = LogManager.getLogger(AoiViewHelper.class);
    /**
     * actor的个数
     */
    public static final int AOI_VIEW_ACTOR_NUM = 1;
    /**
     * 多线程共用简略数据
     */
    private static final Map<Long, Entity.SceneObjBriefAttr> BRIEF_ATTR_MAP = new ConcurrentHashMap<>();

    public static void initAoiViewActor(int zoneId, int mapWidth, int mapHeight, int xNum, int yNum) {
        int threadNum = ActorConfigUtils.getActorThreadNum(ActorRole.AoiView.name());
        if (AOI_VIEW_ACTOR_NUM != threadNum) {
            throw new GeminiException("AoiViewActor configThreadNum:{} not equal AOI_VIEW_THREAD_NUM:{}", threadNum, AOI_VIEW_ACTOR_NUM);
        }
        for (int i = 0; i < AOI_VIEW_ACTOR_NUM; i++) {
            ActorSendMsgUtils.sendAndCreate(genLocalActorRef(zoneId, i), new ActorRunnable<>("init aoiView actor",
                    (actor) -> ((AoiViewActor) actor).init(zoneId, mapWidth, mapHeight, xNum, yNum)));
        }
    }

    private static IActorRef genLocalActorRef(int zoneId, long id) {
        return RefFactory.ofAoiView(ServerContext.getBusId(), zoneId + String.valueOf(id % AOI_VIEW_ACTOR_NUM));
    }

    /**
     * 广播给所有本地的AoiActor
     */
    public static void broadcastMsg(int zoneId, AbstractActor actor, GeneratedMessageV3 msg) {
        for (int i = 0; i < AOI_VIEW_ACTOR_NUM; i++) {
            actor.tell(genLocalActorRef(zoneId, i), msg);
        }
    }

    public static Entity.SceneObjBriefAttr getBriefAttr(long objId) {
        return BRIEF_ATTR_MAP.get(objId);
    }

    public static void sendBriefSceneObjAdd(AbstractActor actor, int zoneId, long objId, Entity.SceneObjBriefAttr attr, AABB aabb, int layer) {
        BRIEF_ATTR_MAP.put(objId, attr);
        SsAoiView.AddSceneObjCmd.Builder builder = SsAoiView.AddSceneObjCmd.newBuilder();
        builder.setAabb(buildAABBMsg(aabb)).setLayer(layer);
        AoiViewHelper.broadcastMsg(zoneId, actor, builder.setObjId(objId).build());
    }

    public static void sendBriefSceneObjUpdate(AbstractActor actor, int zoneId, long objId, Entity.SceneObjBriefAttr attr, int oldLayer, int newLayer) {
        if (newLayer == 0) {
            BRIEF_ATTR_MAP.remove(objId);
        } else {
            BRIEF_ATTR_MAP.put(objId, attr);
        }
        SsAoiView.ChangeSceneObjCmd.Builder builder = SsAoiView.ChangeSceneObjCmd.newBuilder();
        builder.setLayer(oldLayer).setNewLayer(newLayer).setObjId(objId);
        broadcastMsg(zoneId, actor, builder.build());
    }

    private static SsAoiView.AABB buildAABBMsg(AABB aabb) {
        return SsAoiView.AABB.newBuilder().setLeft(aabb.getLeft())
                .setBottom(aabb.getBottom())
                .setTop(aabb.getTop())
                .setRight(aabb.getRight())
                .build();
    }

    public static boolean isAoiChanged(Set<AoiGrid> oldAoiGrids, Set<AoiGrid> newAoiGrids) {
        return !oldAoiGrids.equals(newAoiGrids);
    }

    public static AABB getAABBFromPoints(PointPB p1, PointPB p2, PointPB p3, PointPB p4) {
        int left = p1.getX();
        int right = p1.getX();
        int top = p1.getY();
        int bottom = p1.getY();

        if (p2.getX() < left) {
            left = p2.getX();
        } else if (p2.getX() > right) {
            right = p2.getX();
        }
        if (p2.getY() > bottom) {
            bottom = p2.getY();
        } else if (p2.getY() < top) {
            top = p2.getY();
        }
        if (p3.getX() < left) {
            left = p3.getX();
        } else if (p3.getX() > right) {
            right = p3.getX();
        }
        if (p3.getY() > bottom) {
            bottom = p3.getY();
        } else if (p3.getY() < top) {
            top = p3.getY();
        }
        if (p4.getX() < left) {
            left = p4.getX();
        } else if (p4.getX() > right) {
            right = p4.getX();
        }
        if (p4.getY() > bottom) {
            bottom = p4.getY();
        } else if (p4.getY() < top) {
            top = p4.getY();
        }
        return new AABB(left, top, right, bottom);
    }

    /**
     * 检测视野参数的合法性
     */
    public static boolean checkLayerValid(long playerId, int newLayer, PointPB p1, PointPB p2, PointPB p3, PointPB p4) {
        if (newLayer == 0) {
            LOGGER.warn("{} onPlayerUpdateView layer is 0", playerId);
            return false;
        }
        if (ServerContext.getServerDebugOption().isBattleTestServer()) {
            return true;
        }
        AABB aabb = getAABBFromPoints(p1, p2, p3, p4);
        int width = aabb.getWidth();
        int height = aabb.getHeight();
        Map<Integer, WorldLayerTemplate> templateMap = ResHolder.getInstance().getMap(WorldLayerTemplate.class);
        if (!templateMap.containsKey(newLayer)) {
            LOGGER.warn("{} checkLayerValid layer warn layer: {}", playerId, newLayer);
            return false;
        }
        WorldLayerTemplate template = templateMap.get(newLayer);
        if (width > template.getAoiWidthPair().getValue() || height > template.getAoiHeightPair().getValue()) {
            LOGGER.warn("{} checkLayerValid warn. layer: {} {}", playerId, newLayer, aabb);
            return false;
        }
        return true;
    }
}

package com.yorha.common.enums;

/**
 * <AUTHOR>
 * 方向枚举
 * <p>
 * 坐标2相对于坐标1的角度 7：↖，6：←，5：↙，4：↓，3：↘，2：→，1：↗，0：↑
 */
public enum DirectionEnum {

    U(0, -1),
    RU(1, -1),
    R(1, 0),
    RD(1, 1),
    D(0, 1),
    LD(-1, 1),
    L(-1, 0),
    LU(-1, -1);
    public static final DirectionEnum[][] DIRECTIONS = {{LD, L, LU}, {D, null, U}, {RD, R, RU}};

    private final int addX;
    private final int addY;

    DirectionEnum(int addX, int addY) {
        this.addX = addX;
        this.addY = addY;
    }

    public static DirectionEnum indexOf(int ordinal) {
        return values()[ordinal];
    }

    public int getAddX() {
        return addX;
    }

    public int getAddY() {
        return addY;
    }
}

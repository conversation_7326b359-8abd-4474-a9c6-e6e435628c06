package com.yorha.common.resource.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 策划配置template的注解
 * 标注策划中文表格名和其生成的xml名
 *
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ResXml {

    /**
     * @return 中文策划表格名
     */
    String excel();

    String node();

    boolean isConst() default false;
}

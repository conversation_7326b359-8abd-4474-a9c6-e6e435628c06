package com.yorha.common.enums.reason;

/**
 * 英雄解锁Reason
 * <AUTHOR>
 */
public enum HeroUnlockReason {
    /**
     * 碎片合成
     */
    HERO_FRAGMENT_COMPOSE("碎片合成"),

    /**
     * 开启宝箱获得
     */
    OPEN_CHEST("开启宝箱获得"),

    /**
     * 初始化解锁（获得）
     */
    INITIALIZE_UNLOCK("初始化解锁"),

    /**
     * GM指令获得
     */
    GM_UNLOCK("GM指令获得"),

    /**
     * 英雄道具卡转换获得
     */
    USE_HERO_ITEM("英雄道具卡转换获得"),

    /**
     * 新手免费领取英雄
     */
    NEWBIE_FREE_GET("新手免费领取英雄");

    private final String description;

    HeroUnlockReason(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

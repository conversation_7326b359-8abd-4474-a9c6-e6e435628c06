package com.yorha.common.rank;

import com.google.protobuf.ByteString;
import com.yorha.common.actor.IActorWithTimer;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.msg.UpsertAsk;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.DeleteResult;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.UpsertResult;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.freqLimitCaller.FreqLimitCaller;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.CompressUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CommonMsg.RankRecord;
import com.yorha.proto.CommonMsg.RankRecordDetail;
import com.yorha.proto.StructMsg.RankInfoDTO;
import com.yorha.proto.TcaplusDb;
import io.gamioo.redis.zset.long2object.Long2ObjectEntry;
import io.gamioo.redis.zset.long2object.Long2ObjectZSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.RankTemplate;

import java.io.IOException;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 排行榜相关
 *
 * <AUTHOR> Jiang
 */
public abstract class AbstractRankEntity {
    private static final Logger LOGGER = LogManager.getLogger(AbstractRankEntity.class);
    protected final GameActorWithCall actor;
    protected final int rankId;
    protected final String rankKey;
    private final int zoneId;
    /**
     * 内存排名
     */
    protected final Long2ObjectZSet<RankMember> list = Long2ObjectZSet.newZSet(new RankComparator());
    /**
     * db内存版
     */
    protected Map<Long, RankRecordDetail> map = new HashMap<>();
    /**
     * pb内存版
     */
    protected RankCache cache;
    /**
     * 延时同步至联盟和全服排行榜的task
     */
    private final FreqLimitCaller updateDbSchedule;
    /**
     * 上次统计的时间戳
     */
    private long lastStatisticsTsMs;

    public AbstractRankEntity(GameActorWithCall actor, int zoneId, int rankId, String rankKey, boolean isCreate) {
        this.actor = actor;
        this.zoneId = zoneId;
        this.rankId = rankId;
        this.rankKey = rankKey;
        if (isCreate) {
            create();
        } else {
            load();
        }
        GameActorWithCall gameActorWithCall = ownerActor();
        if (gameActorWithCall instanceof IActorWithTimer) {
            this.updateDbSchedule = new FreqLimitCaller(TimerReasonType.RANK_UPDATE_TIMER,
                    300000,
                    1,
                    this::updateCacheToDb,
                    (IActorWithTimer) gameActorWithCall,
                    rankKey);
        } else {
            throw new GeminiException("ownerActor not impl IActorWithTimer");
        }

    }

    @Override
    public String toString() {
        return "RankEntity{rankId=" + rankId + '}';
    }

    public int getZoneId() {
        return zoneId;
    }

    public long getLastStatisticsTsMs() {
        return lastStatisticsTsMs;
    }

    /**
     * 第一次创建排行榜  创建失败不阻断
     */
    private void create() {
        LOGGER.info("RankEntity insert rank start id={}", rankId);
        TcaplusDb.RankingTable.Builder req = buildRankTable();
        try {
            InsertResult<TcaplusDb.RankingTable.Builder> ans = actor.callGameDb(new InsertAsk<>(req));
            // 已经创建过了
            if (DbUtil.isRecordAlreadyExist(ans.getCode())) {
                LOGGER.error("RankEntity insert rank db but exist {}", rankId);
                return;
            }
            // 失败了
            if (!DbUtil.isOk(ans.getCode())) {
                WechatLog.error("RankEntity insert rank db fail {} {}", rankId, ans.getCode());
                return;
            }
            LOGGER.info("RankEntity insert rank db end {}", rankId);
        } catch (Exception e) {
            WechatLog.error("RankEntity insert rank db fail {}", rankId, e);
        }
    }

    /**
     * 从db里load排行榜  失败阻断流程
     */
    private void load() {
        LOGGER.info("RankEntity load rank from db start {}", rankId);
        TcaplusDb.RankingTable.Builder req = TcaplusDb.RankingTable.newBuilder();
        req.setZoneId(this.getZoneId());
        req.setRankKey(getRankKey());
        try {
            GetResult<TcaplusDb.RankingTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
            if (DbUtil.isRecordNotExist(ans.getCode())) {
                LOGGER.warn("RankEntity load rank from db but not exist {}", rankId);
                // 保底吧  +  为了兼容之前的懒插入
                create();
                return;
            }
            if (!DbUtil.isOk(ans.getCode())) {
                LOGGER.error("RankEntity load rank from db fail {} {}", rankId, ans);
                throw new GeminiException("rank db code {}", ans.getCode());
            }
            if (ans.value != null && ans.value.hasDetail() && !ans.value.getDetail().isEmpty()) {
                byte[] bytes = CompressUtil.zstdUncompress("RankRecordDetail", ans.value.getDetail().toByteArray());
                RankRecord record = RankRecord.parseFrom(bytes);
                this.lastStatisticsTsMs = record.getLastStatisticsTsMs();
                for (long memberId : record.getRecordMap().keySet()) {
                    RankRecordDetail rankRecordDetail = record.getRecordMap().get(memberId);
                    RankMember rankMember = RankMember.build(rankRecordDetail);
                    list.zadd(rankMember, rankMember.getId());
                    map.put(memberId, rankRecordDetail);
                    LOGGER.debug("RankEntity rank load id={},rankMember={}", rankId, rankMember);
                }
            }
            LOGGER.info("RankEntity load rank from db end {}", rankId);
        } catch (IOException e) {
            WechatLog.error("RankEntity load rank from db fail {} ", rankId, e);
        } catch (Exception e) {
            WechatLog.error("RankEntity load rank from db fail {} ", rankId, e);
            throw e;
        }
    }

    /**
     * 重置排行榜 把每个人的分数改成 0
     */
    public void reset() {
        LOGGER.info("RankEntity reset rank start {}", rankId);
        List<Long2ObjectEntry<RankMember>> array = list.zrangeByRank(0, -1);
        for (Long2ObjectEntry<RankMember> entry : array) {
            long memberId = entry.getLongMember();
            this.updateRanking(memberId, 0, entry.getScore().getZoneId());
        }
        // 更新db 比较重要 选择立刻存盘
        updateToDbImmediately("reset");
        LOGGER.info("RankEntity reset rank end {}", rankId);
    }

    /**
     * 清理排行榜 清理内存  不删db
     */
    public void clear() {
        LOGGER.info("RankEntity clear rank start {}", rankId);
        // 清cache
        cache = null;
        list.zlimit(0);
        map.clear();
        // 更新db 比较重要 选择立刻存盘
        updateToDbImmediately("clear");
        LOGGER.info("RankEntity clear rank end {}", rankId);
    }

    /**
     * 销毁对象 强制存盘一次
     */
    public void destroy() {
        // 停服销毁存盘保底
        LOGGER.info("RankEntity destroy rank start {}", rankId);
        updateToDbImmediately("destroy");
        LOGGER.info("RankEntity destroy rank end {}", rankId);
    }

    /**
     * 删除排行榜  清理cache 删db
     */
    public void delete() {
        LOGGER.info("RankEntity delete rank start id={}", rankId);
        // 停timer
        this.updateDbSchedule.stopTimer();
        // 清cache
        cache = null;
        list.zlimit(0);
        // 删db
        TcaplusDb.RankingTable.Builder req = TcaplusDb.RankingTable.newBuilder();
        req.setZoneId(this.getZoneId());
        req.setRankKey(getRankKey());
        try {
            DeleteResult ans = actor.callGameDb(new DeleteAsk<>(req));
            if (!DbUtil.isOk(ans.getCode())) {
                LOGGER.error("RankEntity delete rank fail {} {}", rankId, ans.getCode());
                return;
            }
        } catch (Exception e) {
            LOGGER.error("RankEntity delete rank fail {} ", rankId, e);
            throw e;
        }
        LOGGER.info("RankEntity delete rank end {}", rankId);
    }

    /**
     * 立刻存盘一次
     */
    public void updateToDbImmediately(String reason) {
        if (updateDbSchedule != null) {
            updateDbSchedule.stopTimer();
        }
        TcaplusDb.RankingTable.Builder req = buildRankTable();
        try {
            UpsertResult<TcaplusDb.RankingTable.Builder> res = actor.callGameDb(new UpsertAsk<>(req, UpsertOption.getDefaultInstance()));
            if (DbUtil.isRecordNotExist(res.getCode())) {
                LOGGER.info("RankEntity updateToDbImmediately but not exist {} {}", rankId, reason);
                return;
            }
            if (!DbUtil.isOk(res.getCode())) {
                LOGGER.error("RankEntity updateToDbImmediately fail {} {} {}", rankId, reason, res.getCode());
                return;
            }
        } catch (Exception e) {
            LOGGER.error("RankEntity updateToDbImmediately fail(Exception), rankId={} {}", rankId, reason);
            return;
        }
        LOGGER.info("RankEntity updateToDbImmediately end {} {}", rankId, reason);
    }

    /**
     * 更新当前内存cache到db
     * 使用限频器 禁止外部私自调用
     */
    private void updateCacheToDb() {
        LOGGER.info("start update rankId={}", rankId);
        TcaplusDb.RankingTable.Builder req = buildRankTable();
        actor.tellGameDb(new UpsertAsk<>(req, UpsertOption.getDefaultInstance()));
    }

    /**
     * 构建存盘数据
     */
    private TcaplusDb.RankingTable.Builder buildRankTable() {
        TcaplusDb.RankingTable.Builder req = TcaplusDb.RankingTable.newBuilder();
        req.setZoneId(this.getZoneId());
        req.setRankKey(getRankKey());
        if (lastStatisticsTsMs == 0 && map.isEmpty()) {
            req.setDetail(ByteString.EMPTY);
            return req;
        }
        RankRecord b = RankRecord.newBuilder().setLastStatisticsTsMs(lastStatisticsTsMs).putAllRecord(map).build();
        try {
            ByteString bytes = ByteString.copyFrom(CompressUtil.zstdCompress("RankRecordDetail", b.toByteArray()));
            req.setDetail(bytes);
        } catch (IOException e) {
            LOGGER.error("RankEntity buildRankTable Compress failed ", e);
        }
        return req;
    }

    /**
     * 判断对应分数是否能进当前排行榜
     *
     * @param score 要加入到排行榜的分数
     * @return 可以加入到排行榜返回true，否则返回false
     */
    private boolean isNeedToAdd(long score) {
        // 排行榜不限制人数
        if (getMaxRank() <= 0) {
            return true;
        }
        // 榜内成员不超过配置的最大人数(1.5倍缓冲）
        if (list.zcard() < getRankCacheMaxSize()) {
            return true;
        }
        // 此处榜内length一定大于等于maxRank，maxRank - 1为榜内成员的末位分数，判断该分数是否小于要加入的成员分数
        return list.zmemberByRank(getRankCacheMaxSize() - 1).getScore().getScore() < score;
    }

    public void updateRanking(boolean increase, long memberId, long score, int zoneId) {
        if (increase) {
            RankMember rankMember = list.zscore(memberId);
            if (rankMember != null) {
                // 有数据说明不是第一次，在之前的基础上加此次的值
                score += rankMember.getScore();
            }
        }
        updateRanking(memberId, score, zoneId);
    }

    public void updateRanking(long memberId, long score, int zoneId, long addTsMs) {
        LOGGER.debug("updateRanking id={}, memberId={}, score={}", rankId, memberId, score);
        // 如果在榜上 不用检查去尾
        RankMember oldMember = list.zscore(memberId);
        if (oldMember == null && !isNeedToAdd(score)) {
            return;
        }
        RankMember rankMember = RankMember.build(memberId, score, zoneId, oldMember == null ? 0 : oldMember.getLastRank(), addTsMs);
        // 更内存
        list.zadd(rankMember, memberId);
        map.put(memberId, rankMember.buildDb());
        // 更db
        this.updateDbSchedule.run();
        // insert后，有限制人数的排行榜，可能需要删除排名末尾的人
        if (oldMember == null && list.zcard() > getRankCacheMaxSize()) {
            List<Long2ObjectEntry<RankMember>> array = list.zrangeByRank(getRankCacheMaxSize(), list.zcard());
            // 理论上在策划不更改配置的情况下，应该只会删除一个最末尾的人
            for (Long2ObjectEntry<RankMember> entry : array) {
                this.deleteRanking(entry.getLongMember());
            }
        }
    }

    public void updateRanking(long memberId, long score, int zoneId) {
        updateRanking(memberId, score, zoneId, SystemClock.now());
    }

    public void deleteRanking(long memberId) {
        RankMember rankMember = list.zscore(memberId);
        if (rankMember == null) {
            LOGGER.warn("try deleteRanking but not in {} {}", rankId, memberId);
            return;
        }
        list.zrem(memberId);
        map.remove(rankMember.getId());
        updateDbSchedule.run();
    }

    public void tryDeleteRanking(long memberId) {
        RankMember rankMember = list.zscore(memberId);
        if (rankMember == null) {
            return;
        }
        list.zrem(memberId);
        map.remove(rankMember.getId());
        updateDbSchedule.run();
    }

    /**
     * 刷新上次统计的得分
     */
    public void refreshLastStatisticsScore() {
        LOGGER.info("refreshLastStatisticsScore rankId={}", rankId);
        lastStatisticsTsMs = SystemClock.now();
        List<Long2ObjectEntry<RankMember>> array = list.zrangeByRank(0, -1);
        for (int i = 0; i < array.size(); i++) {
            RankMember member = array.get(i).getScore();
            member.setLastRank(i + 1);
            map.put(array.get(i).getLongMember(), member.buildDb());
        }
        updateToDbImmediately("refreshLastStatisticsScore");
    }

    /**
     * 当前排行榜最大排名数  对外 不管缓存多大 不能超过配置
     */
    public int getTotalSize() {
        return Math.min(list.zcard(), getMaxRank());
    }

    /**
     * 排行榜配置最大名次
     */
    public int getMaxRank() {
        return getTemplate().getMaxRank();
    }

    public int getRankShow() {
        return getTemplate().getShow();
    }

    /**
     * 排行榜缓存最大容量   对内使用的 只作为能不能进榜用  对外请使用getTotalSize/getMaxRank
     */
    protected int getRankCacheMaxSize() {
        return (int) (getMaxRank() * 1.5);
    }

    public int getRankId() {
        return rankId;
    }

    protected String getRankKey() {
        return rankKey;
    }

    public RankTemplate getTemplate() {
        return ResHolder.getTemplate(RankTemplate.class, rankId);
    }

    public int getMemberType() {
        return getTemplate().getMember();
    }

    /**
     * 注: 入参：排名从0开始, 结果：排名从1开始
     * 用最新的 根据排名范围查询
     */
    public List<CommonMsg.MemberAllDto> getRangeRank(int start, int end) {
        List<CommonMsg.MemberAllDto> result = new ArrayList<>();
        List<Long2ObjectEntry<RankMember>> member = list.zrangeByRank(start, end);
        for (int i = 0; i < member.size(); i++) {
            Long2ObjectEntry<RankMember> long2ObjectEntry = member.get(i);
            RankMember rankMember = long2ObjectEntry.getScore();
            result.add(RankHelper.buildRankMemberAll(
                    long2ObjectEntry.getLongMember(), start + i + 1,
                    rankMember.getScore(), rankMember.getZoneId()));
        }
        return result;
    }

    /**
     * 获取榜首
     */
    public abstract void getTopRankInfo(Consumer<RankInfoDTO> onComplete);

    /**
     * 获取排行榜记录
     */
    public abstract void getRankPageInfo(long memberId, int page, BiConsumer<Integer, RankPageDTO> onComplete);

    /**
     * 根据成员id查排行数据
     */
    public abstract void getRankByPlayerList(Collection<Long> ranksList, Consumer<Map<Long, RankInfoDTO>> onComplete);

    /**
     * 根据排行查排行数据
     */
    public abstract void getRankByRankList(Set<Integer> ranksList, Consumer<Map<Long, RankInfoDTO>> onComplete);

    /**
     * 根据分数查排行数据
     */
    public abstract int getTargetRanks(long memberId, long score);

    /**
     * @return 本entity归属的actor
     */
    protected GameActorWithCall ownerActor() {
        return this.actor;
    }
}

package com.yorha.common.resource.resservice.activity;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.Map;

/**
 * 连续活动配置校验
 *
 * <AUTHOR>
 */
public class ContinuesActResService extends AbstractResService {

    public ContinuesActResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

    }

    @Override
    public void checkValid() throws ResourceException {
        final Map<Integer, ContinueActConfigTemplate> configTemplateMap = getResHolder().getMap(ContinueActConfigTemplate.class);
        for (ActivityTemplate activityTemplate : getResHolder().getListFromMap(ActivityTemplate.class)) {
            if (activityTemplate.getSpecUnitType() != CommonEnum.ActivityUnitType.AUT_CONTINUE) {
                continue;
            }
            final ContinueActConfigTemplate configTemplate = configTemplateMap.get(activityTemplate.getId());
            if (configTemplate == null) {
                throw new ResourceException("L_连续活动，continue_act_config 缺少配置 活动id {}", activityTemplate.getId());
            }
            if (!getResHolder().getMap(MailTemplate.class).containsKey(configTemplate.getMailId())) {
                throw new ResourceException("L_连续活动，continue_act_config 邮件id不存在 活动id {} 邮件id {}", activityTemplate.getId(), configTemplate.getMailId());
            }
            if (!getResHolder().getMap(ChargeGoodsTemplate.class).containsKey(configTemplate.getPayGoodsId())) {
                throw new ResourceException("L_连续活动，continue_act_config 付费奖池礼包不存在 活动id {} 礼包id {}", activityTemplate.getId(), configTemplate.getPayGoodsId());
            }
            if (configTemplate.getPayPoolList().size() != configTemplate.getFreePoolList().size()) {
                throw new ResourceException("L_连续活动，continue_act_config 免费奖池付费奖池数量不一样 活动id {}", activityTemplate.getId());
            }
            if (configTemplate.getFreePoolList().isEmpty()) {
                throw new ResourceException("L_连续活动，continue_act_config 免费付费奖池没配置 活动id {}", activityTemplate.getId());
            }
            final Map<Integer, ContinueActPoolTemplate> actConfigTemplateMap = getResHolder().getMap(ContinueActPoolTemplate.class);
            final Map<Integer, ItemTemplate> itemTemplateMap = getResHolder().getMap(ItemTemplate.class);
            for (int index = 0; index < configTemplate.getFreePoolList().size(); index++) {
                final int freeTaskId = configTemplate.getFreePoolList().get(index);
                final int payTaskId = configTemplate.getPayPoolList().get(index);
                if (!actConfigTemplateMap.containsKey(freeTaskId)) {
                    throw new ResourceException("L_连续活动，continue_act_config 免费奖励不存在 活动id {} 奖励 {}", activityTemplate.getId(), freeTaskId);
                }
                if (!actConfigTemplateMap.containsKey(payTaskId)) {
                    throw new ResourceException("L_连续活动，continue_act_config 付费奖励不存在 活动id {} 奖励 {}", activityTemplate.getId(), payTaskId);
                }
                final ContinueActPoolTemplate freeActPoolTemplate = actConfigTemplateMap.get(freeTaskId);
                final ContinueActPoolTemplate payActPoolTemplate = actConfigTemplateMap.get(payTaskId);
                if (freeActPoolTemplate.getScore() != payActPoolTemplate.getScore()) {
                    throw new ResourceException("L_连续活动，continue_act_config 免费付费任务需要积分不匹配 {} {}", freeActPoolTemplate.getId(), payActPoolTemplate.getId());
                }
                if (freeActPoolTemplate.getScore() <= 0) {
                    throw new ResourceException("L_连续活动，continue_act_config 免费任务需要积分不大于0 {}", freeActPoolTemplate.getId());
                }
                if (!itemTemplateMap.containsKey(freeActPoolTemplate.getRewardPair().getKey())) {
                    throw new ResourceException("L_连续活动，continue_act_config 免费任务配置了不存在的道具 {} {}", freeActPoolTemplate.getId(), freeActPoolTemplate.getRewardPair());
                }
                if (freeActPoolTemplate.getRewardPair().getValue() <= 0) {
                    throw new ResourceException("L_连续活动，continue_act_config 免费任务道具数量非法 {} {}", freeActPoolTemplate.getId(), freeActPoolTemplate.getRewardPair());
                }
                if (!itemTemplateMap.containsKey(payActPoolTemplate.getRewardPair().getKey())) {
                    throw new ResourceException("L_连续活动，continue_act_config 付费任务配置了不存在的道具 {} {}", payActPoolTemplate.getId(), payActPoolTemplate.getRewardPair());
                }
                if (payActPoolTemplate.getRewardPair().getValue() <= 0) {
                    throw new ResourceException("L_连续活动，continue_act_config 付费任务道具数量非法 {} {}", payActPoolTemplate.getId(), payActPoolTemplate.getRewardPair());
                }
            }

        }
    }
}

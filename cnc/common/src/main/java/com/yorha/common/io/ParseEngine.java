package com.yorha.common.io;

import com.google.protobuf.Empty;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.CompressUtil;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.proto.Core;
import com.yorha.proto.Core.CSHeader;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * 消息编码、压缩、加密
 *
 * <AUTHOR>
 */
public class ParseEngine {
    public static final int FLAG_COMPRESS = 0x01;
    public static final int FLAG_ENCRYPT = 0x02;
    private static final Logger LOGGER = LogManager.getLogger(ParseEngine.class);

    /**
     * 压缩
     */
    public boolean compress;
    /**
     * 压缩阀值
     */
    public int compressThreshold;
    /**
     * 网关数据包加密
     */
    public boolean crypto;

    public static ParseEngine getInstance() {
        return ParseEngine.InstanceHolder.INSTANCE;
    }

    /**
     * 解码消息
     */
    public static byte[] decodeMsg(int flag, byte[] data) {
        if ((flag & FLAG_ENCRYPT) != 0) {
            // 解密
            data = unCrypto(data.length % 2, data);
        }
        if ((flag & FLAG_COMPRESS) != 0) {
            // 解压
            try {
                data = CompressUtil.zstdUncompress("decodeMsg", data);
            } catch (Exception e) {
                throw new GeminiException("decodeMsg fail", e);
            }
        }
        return data;
    }

    /**
     * 加密
     */
    public static byte[] crypto(int index, byte[] inBytes) {
        if (inBytes.length >= 2) {
            byte tmpByte = inBytes[0];
            inBytes[0] = inBytes[inBytes.length - 1];
            inBytes[inBytes.length - 1] = tmpByte;
            for (int i = index; i < inBytes.length; i += 2) {
                tmpByte = inBytes[i];
                inBytes[i] = (byte) ~(tmpByte << 4 & 0x00F0 | tmpByte >> 4 & 0x000F);
            }
        }
        return inBytes;
    }

    /**
     * 解密
     */
    public static byte[] unCrypto(int index, byte[] inBytes) {
        if (inBytes.length >= 2) {
            byte tmpByte = 0;
            for (int i = index; i < inBytes.length; i += 2) {
                tmpByte = (byte) ~inBytes[i];
                inBytes[i] = (byte) (tmpByte << 4 & 0x00F0 | tmpByte >> 4 & 0x000F);
            }
            tmpByte = inBytes[0];
            inBytes[0] = inBytes[inBytes.length - 1];
            inBytes[inBytes.length - 1] = tmpByte;
        }
        return inBytes;
    }

    /**
     * 编码消息
     */
    public ByteBuf encodeMsg(int msgType, int seqId, Core.Code code, GeneratedMessageV3 msg) {
        CSHeader.Builder headerBuilder = CSHeader.newBuilder();
        headerBuilder.setType(msgType);
        if (seqId != 0) {
            headerBuilder.setSeqId(seqId);
        }
        if (code != null) {
            headerBuilder.setCode(code);
        }
        if (msg == null) {
            msg = Empty.getDefaultInstance();
        }

        int flag = 0;
        byte[] bodyData = msg.toByteArray();
        int rawBodyLength = bodyData.length;
        int bodyLength = rawBodyLength;
        if (compress && rawBodyLength >= compressThreshold) {
            // 压缩
            try {
                String msgTypeName = MsgUtils.getMsgNameFromCsMsgType(msgType);
                bodyData = CompressUtil.zstdCompress(msgTypeName, bodyData);
                bodyLength = bodyData.length;
                MonitorUnit.BIG_MSG_COMPRESS_TOTAL.labels(ServerContext.getBusId(), msgTypeName).inc();
            } catch (Exception e) {
                throw new RuntimeException("encodeMsg compress fail", e);
            }
            flag |= FLAG_COMPRESS;
        }
        if (bodyLength > 0 && crypto) {
            // 加密
            flag |= FLAG_ENCRYPT;
            bodyData = crypto(bodyData.length % 2, bodyData);
        }
        headerBuilder.setFlag(flag);
        final byte[] headerData = headerBuilder.build().toByteArray();
        final int headerLength = headerData.length;
        if (headerLength == 0) {
            LOGGER.error("msg header is 0, msgType={}:{}, seqId={}, code={}", msgType, ClassNameCacheUtils.getSimpleName(msg.getClass()), seqId, code);
            throw new GeminiException("msgType {} header is 0!");
        }
        final int totalSize = 2 + headerLength + bodyLength;
        final ByteBuf byteBuf = Unpooled.buffer(totalSize);

        byteBuf.writeShort(headerLength);
        byteBuf.writeBytes(headerData);
        byteBuf.writeBytes(bodyData);
        return byteBuf;
    }

    public void setCompress(boolean compress) {
        this.compress = compress;
    }

    public void setCompressThreshold(int compressThreshold) {
        this.compressThreshold = compressThreshold;
    }

    public void setCrypto(boolean crypto) {
        this.crypto = crypto;
    }

    private static class InstanceHolder {
        private static final ParseEngine INSTANCE = new ParseEngine();
    }
}

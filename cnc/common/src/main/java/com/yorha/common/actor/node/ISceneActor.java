package com.yorha.common.actor.node;

import com.yorha.common.actor.IActor;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.proto.SsScenePlayer;
import com.yorha.proto.TcaplusDb;
import com.yorha.proto.Zone;
import com.yorha.proto.Zoneside;

import javax.annotation.Nullable;
import java.util.List;
import java.util.concurrent.TimeUnit;

public interface ISceneActor extends IActor {
    boolean initBigScene(boolean isFirstCreate, ZoneInfoProp prop, Zone.ZoneInfoEntity changedProp,
                         ZoneSideProp zoneSideProp, Zoneside.ZoneSideEntity zoneSideChangedProp,
                         List<ValueWithVersion<TcaplusDb.ClanTable.Builder>> clans,
                         List<ValueWithVersion<TcaplusDb.ScenePlayerTable.Builder>> players,
                         List<ValueWithVersion<TcaplusDb.SceneObjTable.Builder>> objs,
                         List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> mails);

    void handleChangeOpenZoneTime();

    void handleChangeServerStatus();

    void handleMultiLanguage(SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd ask);

    @Nullable
    ActorTimer controllerAddTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit);

    @Nullable
    ActorTimer controllerAddRepeatTimer(
            String prefix,
            TimerReasonType timerReasonType,
            Runnable runnable,
            long initialDelay,
            long period,
            TimeUnit unit,
            boolean isFix
    );
}

package com.yorha.common.utils.id;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.LockSupport;

/**
 * 非持久化，启动期间短期保证不重复
 * 保证id在[0, maxId]中循环，到达最大值后再次从0开始，其中maxId默认为2 ^ 63 - 1。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class RecycleIncreaseIdFactory implements IIdFactory {
    private static final Logger LOGGER = LogManager.getLogger(RecycleIncreaseIdFactory.class);

    private final AtomicLong id;
    private final long maxId;

    public RecycleIncreaseIdFactory() {
        this(Long.MAX_VALUE);
    }

    protected RecycleIncreaseIdFactory(final long maxId) {
        this.id = new AtomicLong(0);
        this.maxId = maxId;
    }

    @Override
    public long nextId(String reason) {
        long nextId = id.getAndIncrement();
        // nextId 达到最大值，cas循环重置
        if (nextId == this.maxId) {
            int loopTime = 0;
            while (!id.compareAndSet(id.get(), 0L)) {
                loopTime++;
            }
            LOGGER.info("RecycleIncreaseIdFactory recycle for reason={}, loopTime={}", reason, loopTime);
        } else if (nextId < 0 || nextId > this.maxId) { // nextId打爆了，需要等待cas承担者设置好数据
            Thread.yield();
            nextId = id.getAndIncrement();
            while (nextId < 0 || nextId > this.maxId) {
                nextId = id.getAndIncrement();
                LockSupport.parkNanos(this, 1);
            }
        }
        return nextId;
    }
}

package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.DeleteByPartKeyOption;
import com.yorha.common.db.tcaplus.result.DeleteByPartKeyResult;
import org.reactivestreams.Publisher;

public class MongoDeleteByPartKey<T extends Message.Builder> extends MongoOperation<T, DeleteByPartKeyOption, DeleteResult, DeleteResult, DeleteByPartKeyResult> {
    public MongoDeleteByPartKey(MongoDatabase database, T t, DeleteByPartKeyOption deleteByPartKeyOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, deleteByPartKeyOption);
    }

    @Override
    protected NormalSubscriber<DeleteResult> getSubscriber() {
        return new NormalSubscriber<>();
    }

    @Override
    protected Publisher<DeleteResult> getPublisher() {
        return this.database.getCollection(this.getTableName()).deleteMany(DocumentHelper.formIndex(this.getReq()));
    }

    @Override
    protected DeleteByPartKeyResult buildResult(DeleteResult deleteResult) {
        if (!deleteResult.wasAcknowledged()) {
            //TODO(JOSEFREN): dbPerf
            return new DeleteByPartKeyResult(TcaplusErrorCode.SVR_ERR_FAIL_DELETE_RECORD.getValue());
        }
        if (deleteResult.getDeletedCount() == 0) {
            return new DeleteByPartKeyResult(TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST.getValue());
        }
        return new DeleteByPartKeyResult(TcaplusErrorCode.GEN_ERR_SUC.getValue());
    }

    @Override
    protected DeleteByPartKeyResult onMongoError() {
        return new DeleteByPartKeyResult(DEFAULT_ERROR_CODE.getValue());
    }
}


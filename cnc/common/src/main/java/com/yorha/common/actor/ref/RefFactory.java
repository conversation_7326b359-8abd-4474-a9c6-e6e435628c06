package com.yorha.common.actor.ref;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.aoiView.AoiViewHelper;
import com.yorha.common.dbactor.DbMgrService;
import com.yorha.common.server.NodeRole;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.BusIdUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonMsg;

import javax.annotation.Nullable;

public class RefFactory {

    public static IActorRef ofLocalNode() {
        return ofNode(ServerContext.getBusId());
    }

    public static IActorRef ofLocalZoneCard() {
        return new FixedRef(ServerContext.getBusId(), ActorRole.ZoneCard.name(), ServerContext.getBusId());
    }

    public static IActorRef ofNode(final String busId) {
        return new FixedRef(busId, ActorRole.Node.name(), busId);
    }

    public static IActorRef ofTranslator(final long translatorId) {
        final String busId = LoadBalanceHelper.getHashBusId(ServerContext.getActorSystem(), NodeRole.Global.getTypeId(), translatorId);
        return new FixedRef(busId, ActorRole.Translator.name(), String.valueOf(translatorId));
    }

    public static IActorRef ofClan(final int zoneId, final long clanId) {
        if (clanId <= 0) {
            WechatLog.error("RefFactory ofClan fail {} {}", zoneId, clanId);
            return null;
        }
        final String busId = BusIdUtils.formBusId(ServerContext.getWorldId(), zoneId, NodeRole.Zone.getTypeId(), 1);
        return new FixedRef(busId, ActorRole.Clan.name(), String.valueOf(clanId));
    }

    public static IActorRef ofPlayer(final int zoneId, final long playerId) {
        if (playerId <= 0) {
            WechatLog.error("RefFactory ofPlayer fail {} {}", zoneId, playerId);
            return null;
        }
        final String busId = BusIdUtils.formBusId(ServerContext.getWorldId(), zoneId, NodeRole.Zone.getTypeId(), 1);
        return new FixedRef(busId, ActorRole.Player.name(), String.valueOf(playerId));
    }

    public static IActorRef ofZoneChat(final int zoneId) {
        if (zoneId <= 0) {
            WechatLog.error("RefFactory ofZone fail zoneId={}", zoneId);
            return null;
        }
        final String busId = BusIdUtils.formBusId(ServerContext.getWorldId(), zoneId, NodeRole.Zone.getTypeId(), 1);
        return new FixedRef(busId, ActorRole.ZoneChat.name(), String.valueOf(zoneId));
    }

    public static IActorRef ofLocalBigScene(int zoneId) {
        return new FixedRef(ServerContext.getBusId(), ActorRole.Scene.name(), String.valueOf(zoneId));
    }

    /**
     * 大世界
     */
    public static IActorRef ofBigScene(final int zoneId) {
        final String busId = formZoneBusId(zoneId);
        if (busId == null) {
            return null;
        }
        return new FixedRef(busId, ActorRole.Scene.name(), String.valueOf(zoneId));
    }

    public static IActorRef ofGate(int zoneId) {
        final String busId = BusIdUtils.formBusId(ServerContext.getWorldId(), zoneId, NodeRole.Zone.getTypeId(), 1);
        return new FixedRef(busId, ActorRole.Gate.name(), busId);
    }

    public static IActorRef ofLocalGate() {
        return ofGate(ServerContext.getZoneId());
    }

    public static IActorRef ofRank(int zoneId) {
        final String busId = formZoneBusId(zoneId);
        if (busId == null) {
            return null;
        }
        return new FixedRef(busId, ActorRole.Rank.name(), String.valueOf(zoneId));
    }

    public static IActorRef ofName(int zoneId) {
        final String busId = BusIdUtils.formBusId(ServerContext.getWorldId(), zoneId, NodeRole.Zone.getTypeId(), 1);
        return new FixedRef(busId, ActorRole.Name.name(), String.valueOf(zoneId));
    }


    public static IActorRef ofPlayerCardActor(long actorId) {
        final String busId = LoadBalanceHelper.getHashBusId(ServerContext.getActorSystem(), NodeRole.Global.getTypeId(), actorId);
        return new FixedRef(busId, ActorRole.PlayerCard.name(), String.valueOf(actorId));
    }

    public static IActorRef ofClanCardActor(long actorId) {
        final String busId = LoadBalanceHelper.getHashBusId(ServerContext.getActorSystem(), NodeRole.Global.getTypeId(), actorId);
        return new FixedRef(busId, ActorRole.ClanCard.name(), String.valueOf(actorId));
    }

    public static IActorRef dbActorRef() {
        return DbMgrService.getInstance();
    }

    public static IActorRef fromPb(CommonMsg.ActorRefData actorRefData) {
        final String roleName = actorRefData.getActorRole();
        if (roleName.equals(IActorRef.NOBODY.getActorRole())) {
            return IActorRef.NOBODY;
        }
        return new FixedRef(actorRefData.getBusId(), roleName, actorRefData.getActorId());
    }

    public static IActorRef ofLocalSessionRef(String sessionId) {
        return new FixedRef(ServerContext.getBusId(), ActorRole.GateSession.name(), sessionId);
    }

    public static IActorRef ofLocalIdipSessionRef(final String sessionId) {
        return new FixedRef(ServerContext.getBusId(), ActorRole.IdIpSession.name(), sessionId);
    }

    public static IActorRef ofLocalIMurSessionRef(final String sessionId) {
        return new FixedRef(ServerContext.getBusId(), ActorRole.IMurSession.name(), sessionId);
    }

    public static IActorRef ofLocalMidasCallbackSessionRef(final String sessionId) {
        return new FixedRef(ServerContext.getBusId(), ActorRole.MidasCallbackSession.name(), sessionId);
    }

    public static IActorRef ofNewDungeon(long dungeonId) {
        final String busId = LoadBalanceHelper.getRandomBusId(ServerContext.getActorSystem(), NodeRole.Dungeon.getTypeId());
        return new FixedRef(busId, ActorRole.Dungeon.name(), String.valueOf(dungeonId));
    }

    public static IActorRef ofFixedDungeon(String busId, String actorId) {
        return new FixedRef(busId, ActorRole.Dungeon.name(), actorId);
    }

    public static IActorRef ofPathFinding(String busId, String actorId) {
        return new FixedRef(busId, ActorRole.PathFinding.name(), actorId);
    }

    public static IActorRef ofAoiView(int zoneId, long id) {
        final String busId = formZoneBusId(zoneId);
        if (busId == null) {
            return null;
        }
        return ofAoiView(busId, zoneId + String.valueOf(id % AoiViewHelper.AOI_VIEW_ACTOR_NUM));
    }

    public static IActorRef ofAoiView(String busId, String actorId) {
        return new FixedRef(busId, ActorRole.AoiView.name(), actorId);
    }

    public static IActorRef ofNewBattleSimulator(long id) {
        final String busId = LoadBalanceHelper.getRandomBusId(ServerContext.getActorSystem(), NodeRole.Global.getTypeId());
        return new FixedRef(busId, ActorRole.BattleSimulator.name(), String.valueOf(id));
    }

    public static IActorRef ofGroupChat(int chatChannel, String channelId) {
        final String actorId = String.format("%d_%s", chatChannel, channelId);
        final String busId = LoadBalanceHelper.getHashBusId(ServerContext.getActorSystem(), NodeRole.Global.getTypeId(), actorId);
        return new FixedRef(busId, ActorRole.GroupChat.name(), actorId);
    }

    public static IActorRef ofZoneCard() {
        final String busId = LoadBalanceHelper.getRandomBusId(ServerContext.getActorSystem(), NodeRole.Global.getTypeId());
        return new FixedRef(busId, ActorRole.ZoneCard.name(), busId);
    }

    public static IActorRef ofMidasAgent() {
        return new FixedRef(ServerContext.getBusId(), ActorRole.MidasAgent.name(), ServerContext.getBusId());
    }

    public static IActorRef ofTextFilter() {
        return new FixedRef(ServerContext.getBusId(), ActorRole.TextFilter.name(), ServerContext.getBusId());
    }

    public static IActorRef ofAuth() {
        return new FixedRef(ServerContext.getBusId(), ActorRole.Auth.name(), ServerContext.getBusId());
    }

    public static IActorRef ofLocalPushNotification() {
        return ofPushNotification(ServerContext.getBusId());
    }

    public static IActorRef ofPushNotification(final int zoneId) {
        final String busId = formZoneBusId(zoneId);
        if (busId == null) {
            return null;
        }
        return ofPushNotification(busId);

    }

    public static IActorRef ofPushNotification(final String busId) {
        return new FixedRef(busId, ActorRole.PushNotification.name(), busId);
    }

    public static IActorRef ofLocalMonitor() {
        return ofMonitor(ServerContext.getBusId());
    }

    public static IActorRef ofMonitor(final String busId) {
        return new FixedRef(busId, ActorRole.Monitor.name(), busId);
    }

    public static IActorRef ofAntiAddiction() {
        return new FixedRef(ServerContext.getBusId(), ActorRole.AntiAddiction.name(), ServerContext.getBusId());
    }

    public static IActorRef ofBattle() {
        final String busId = LoadBalanceHelper.getRandomBusId(ServerContext.getActorSystem(), NodeRole.Battle.getTypeId());
        if (busId == null) {
            return null;
        }
        return new FixedRef(busId, ActorRole.Battle.name(), busId);
    }

    private static @Nullable
    String formZoneBusId(final int zoneId) {
        return BusIdUtils.formBusId(ServerContext.getWorldId(), zoneId, NodeRole.Zone.getTypeId(), 1);
    }
}

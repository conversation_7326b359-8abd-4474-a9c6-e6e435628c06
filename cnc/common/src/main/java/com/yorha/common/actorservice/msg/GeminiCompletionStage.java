package com.yorha.common.actorservice.msg;

import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;


/**
 * 融合actor的定制版CompletionStage
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class GeminiCompletionStage<T> {
    private static final Logger LOGGER = LogManager.getLogger(GeminiCompletionStage.class);

    /**
     * 内部超时异常，不捕获堆栈。
     */
    public static class GeminiStageTimeoutException extends Exception {
        GeminiStageTimeoutException(final String msg) {
            super(msg, null, true, false);
        }
    }

    /**
     * 替代throwable的结果。
     */
    private static class ThrowableResult {
        private final Throwable throwable;

        private ThrowableResult(Throwable throwable) {
            this.throwable = throwable;
        }
    }

    /**
     * actor消息
     */
    private final ActorMsgEnvelope envelope;
    private Function<Object, Object> resultHandler;
    private Function<ThrowableResult, Object> exceptionHandler;
    /**
     * 结果
     */
    private Object result;
    /**
     * 过期时间
     */
    private final long expireTimeoutTsMs;
    /**
     * 过期时触发的任务
     */
    private final ScheduledFuture<?> expireTask;

    public GeminiCompletionStage(final ActorMsgEnvelope envelope, final long timeoutTimeMs) {
        if (timeoutTimeMs <= 0) {
            throw new GeminiException("{} withTimeout {}!", this, timeoutTimeMs);
        }

        if (envelope.getAskStageId() == null) {
            throw new GeminiException("{} has no stageId!", this);
        }

        this.envelope = envelope;
        // 2倍超时时间
        this.expireTimeoutTsMs = timeoutTimeMs * 2 + SystemClock.nowNative();
        final ActorMsgEnvelope stageEnvelop = this.envelope;

        this.expireTask = SystemScheduleMgr.getInstance().schedule(() -> {
            final ActorMsgEnvelope answerEnvelope = ActorMsgEnvelope.createFromAnswerException(IActorRef.NOBODY,
                    new GeminiStageTimeoutException(stageEnvelop + " timeout!"), stageEnvelop.getMsgSeqId(), stageEnvelop.getAskStageId());
            answerEnvelope.setReceiver(stageEnvelop.getSender());
            ActorMsgSystem.dispatchMsg(answerEnvelope);
        }, timeoutTimeMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 是否完成。
     *
     * @return true or false。
     */
    public boolean isDone() {
        return this.result != null;
    }


    /**
     * 设置任务完成时候的回调函数(不可重入)
     *
     * @param biConsumer 任务完成时候的回调(T与Throwable互斥，其中一个为null)。
     */
    public void onComplete(BiConsumer<T, Throwable> biConsumer) {
        if (this.isDone()) {
            throw new GeminiException("{} already done! do not onComplete {}!", this, biConsumer);
        }
        if (this.resultHandler != null) {
            throw new GeminiException("{} already set onComplete, do not reset {}", this, biConsumer);
        }
        final Function<T, T> resultHandler = (T t) -> {
            biConsumer.accept(t, null);
            return t;
        };
        final Function<ThrowableResult, Object> exceptionHandler = (t) -> {
            biConsumer.accept(null, t.throwable);
            return t;
        };

        this.resultHandler = (Function<Object, Object>) resultHandler;
        this.exceptionHandler = exceptionHandler;
    }


    /**
     * 异常结束(业务勿调用)
     *
     * @param msgSeqId  标识阶段的msgSeqId。
     * @param throwable 异常。
     */
    public void completeExceptionally(final long msgSeqId, final Throwable throwable) {
        if (throwable == null) {
            throw new GeminiException("{} completeExceptionally {} with null!", this, msgSeqId);
        }
        this.setResult(msgSeqId, new ThrowableResult(throwable));
        this.fireStage();
    }

    /**
     * 设置结果，msgSeqId校验。
     *
     * @param msgSeqId envelop的msgSeqId
     * @param value    结果。
     */
    public void complete(final long msgSeqId, final T value) {
        if (value == null) {
            throw new GeminiException("{} complete {} with null!", this, msgSeqId);
        }
        this.setResult(msgSeqId, value);
        this.fireStage();
    }

    /**
     * 检测是否超时
     *
     * @return boolean true==已过期
     */
    public boolean checkExpiration() {
        final long nowTsMs = SystemClock.nowNative();
        // 没设置超时时间 或 没超时
        if (this.expireTimeoutTsMs == 0 || this.expireTimeoutTsMs >= nowTsMs) {
            return false;
        }
        this.completeExceptionally(this.envelope.getMsgSeqId(), new GeminiStageTimeoutException(this.envelope + " timeout!"));
        return true;
    }

    /**
     * 根据msgSeqId设置阶段性结果。
     *
     * @param msgSeqId 消息序列号。
     * @param result   结果。
     */
    private void setResult(final long msgSeqId, Object result) {
        if (this.envelope.getMsgSeqId() != msgSeqId) {
            throw new GeminiException("{} setResult {} with result {}! no stage exits!", this, msgSeqId, result);
        }
        if (this.isDone()) {
            LOGGER.error("{} setResult {} again! skip!", this, msgSeqId);
            return;
        }
        this.result = result;
    }

    /**
     * 触发stage的完成。
     */
    private void fireStage() {
        if (this.result == null) {
            return;
        }
        this.completeStage();
    }

    /**
     * 完成stage，Completion一个接一个处理。
     */
    private void completeStage() {
        // 取消超时任务
        if (this.expireTask != null && !this.expireTask.isDone()) {
            this.expireTask.cancel(false);
        }

        try {
            if (this.result instanceof GeminiCompletionStage.ThrowableResult) {
                this.result = this.exceptionHandler.apply((ThrowableResult) this.result);
            } else {
                this.result = this.resultHandler.apply(this.result);
            }
        } catch (Throwable t) {
            // 最后一个Node的错误无法处理，打印错误日志
            if (GeminiException.isLogicException(t)) {
                LOGGER.error("Last Completion Error, stage={}, t=", this, t);
            } else {
                WechatLog.error("Last Completion Error, stage={}, t=", this, t);
            }
        }

    }

    @Override
    public String toString() {
        return "GeminiCompletionStage{" +
                "envelope=" + envelope +
                ", result=" + result +
                '}';
    }
}

package com.yorha.common.helper;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.id.ZoneIncreaseIdFactory;
import com.yorha.proto.SsPlayerCard.QueryPlayerCardAns;

/**
 * <AUTHOR>
 */
public class ZoneFindHelper {

    /**
     * call查询单个玩家 所属zoneId
     */
    public static int queryPlayerZoneIdSync(GameActorWithCall actor, long playerId) {
        if (playerId <= 0) {
            throw new GeminiException("queryPlayerZoneIdSync invailed playerId {}", playerId);
        }
        QueryPlayerCardAns ans = CardHelper.queryPlayerCardSync(actor, playerId);
        return ans.getCardInfo().getZoneId();
    }

    /**
     * 查询联盟id
     */
    public static int queryClanZoneId(long clanId) {
        long id = ZoneIncreaseIdFactory.decodeZoneId(clanId);
        return (int) id;
    }
}

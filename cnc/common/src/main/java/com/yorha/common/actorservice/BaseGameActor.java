package com.yorha.common.actorservice;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.msg.GeminiCompletionStage;
import com.yorha.common.db.tcaplus.msg.GameDbReq;
import com.yorha.common.db.tcaplus.msg.GameDbResp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 游戏actor基类
 *
 * <AUTHOR>
 */
public abstract class BaseGameActor extends AbstractActor {
    protected static final Logger LOGGER = LogManager.getLogger(BaseGameActor.class);

    public BaseGameActor(ActorSystem system, IActorRef self) {
        super(system, self);
    }

    /**
     * 获取Zone对应的大世界id。
     *
     * @return 大世界id
     */
    public long getBigSceneId() {
        return this.getZoneId();
    }

    /**
     * 通知消息给Player。
     *
     * @param targetPlayerId 目标玩家id。
     * @param msg            消息。
     */
    public void tellPlayer(int zoneId, long targetPlayerId, GeneratedMessageV3 msg) {
        tell(RefFactory.ofPlayer(zoneId, targetPlayerId), msg);
    }

    public <RESP> GeminiCompletionStage<RESP> askPlayer(int zoneId, long targetPlayerId, GeneratedMessageV3 msg) {
        return ask(RefFactory.ofPlayer(zoneId, targetPlayerId), msg);
    }

    public void tellZoneRank(int zoneId, GeneratedMessageV3 msg) {
        tell(RefFactory.ofRank(zoneId), msg);
    }

    public void tellSelfBigScene(GeneratedMessageV3 msg) {
        tell(RefFactory.ofBigScene(getZoneId()), msg);
    }

    public void tellBigScene(long sceneId, GeneratedMessageV3 msg) {
        tell(RefFactory.ofBigScene((int) sceneId), msg);
    }

    public <RESP> GeminiCompletionStage<RESP> askSelfBigScene(long sceneId, GeneratedMessageV3 msg) {
        return ask(RefFactory.ofBigScene((int) sceneId), msg);
    }

    public void tellClan(int zoneId, long clanId, GeneratedMessageV3 msg) {
        tell(RefFactory.ofClan(zoneId, clanId), msg);
    }

    public <RESP> GeminiCompletionStage<RESP> askClan(int zoneId, long clanId, GeneratedMessageV3 msg) {
        return ask(RefFactory.ofClan(zoneId, clanId), msg);
    }

    public <RESP> GeminiCompletionStage<RESP> askZoneRank(int zoneId, GeneratedMessageV3 msg) {
        return ask(RefFactory.ofRank(zoneId), msg);
    }

    public void tellName(GeneratedMessageV3 msg) {
        tell(RefFactory.ofName(getZoneId()), msg);
    }

    public void tellGameDb(GameDbReq<?> msg) {
        tell(RefFactory.dbActorRef(), msg);
    }

    public <RESP extends GameDbResp> GeminiCompletionStage<RESP> askGameDb(GameDbReq<RESP> msg, long timeoutMillis) {
        return ask(RefFactory.dbActorRef(), msg, timeoutMillis);
    }

    public <RESP extends GameDbResp> GeminiCompletionStage<RESP> askGameDb(GameDbReq<RESP> msg) {
        return ask(RefFactory.dbActorRef(), msg);
    }
}

package com.yorha.common.resource.resservice.constant;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import res.template.ConstBattleTemplate;

/**
 * <AUTHOR>
 */
public class ConstBattleKVResService extends AbstractResService {

    public ConstBattleKVResService(ResHolder resHolder) {
        super(resHolder);
    }

    public ConstBattleTemplate getTemplate() {
        return getResHolder().getConstTemplate(ConstBattleTemplate.class);
    }

    @Override
    public void load() throws ResourceException {
    }

    @Override
    public void checkValid() throws ResourceException {

    }
}

package com.yorha.common.db.tcaplus.option;

import com.yorha.proto.CommonEnum;


public class UpsertOption {

    private static final UpsertOption DEFAULT_VALUE = UpsertOption.newBuilder().build();

    private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
    /**
     * 是否需要重试。如果重试，DBActor会不停得重试直到超时。
     */
    private boolean isRetry = false;
    /**
     * 如果设置为负数，表示当前数据不启动版本控
     * 制
     */
    private int version = -1;

    private UpsertOption() {

    }

    public static UpsertOption getDefaultInstance() {
        return DEFAULT_VALUE;
    }

    public static UpsertOption.Builder newBuilder() {
        return new UpsertOption.Builder();
    }

    public int getResultFlag() {
        return resultFlag.getNumber();
    }

    public int getVersion() {
        return version;
    }

    public boolean isRetry() {
        return isRetry;
    }

    public static final class Builder {
        private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
        private boolean isRetry = false;
        private int version = 0;

        private Builder() {
        }

        public UpsertOption.Builder setVersion(final int version) {
            this.version = version;
            return this;
        }

        public UpsertOption.Builder setResultFlag(CommonEnum.TcaplusResultFlag resultFlag) {
            this.resultFlag = resultFlag;
            return this;
        }

        public UpsertOption.Builder withRetry() {
            this.isRetry = true;
            return this;
        }

        public UpsertOption build() {
            final UpsertOption upsertOption = new UpsertOption();
            upsertOption.resultFlag = this.resultFlag;
            upsertOption.isRetry = this.isRetry;
            upsertOption.version = this.version;
            return upsertOption;
        }
    }

}

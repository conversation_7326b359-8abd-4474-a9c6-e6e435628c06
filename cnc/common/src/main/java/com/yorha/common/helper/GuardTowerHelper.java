package com.yorha.common.helper;

import com.yorha.common.resource.ResHolder;
import res.template.TroopTemplate;

/**
 * <AUTHOR>
 */
public class GuardTowerHelper {

    /**
     * 获取防御塔部队Id
     */
    public static int getGuardTowerTroopId(int towerId) {
        if (towerId <= 0) {
            return 0;
        }
        return 0;
    }

    /**
     * 获取防御塔等级
     */
    public static int getGuardTowerLv(int towerId) {
        return 1;
    }

    public static int getGuardTowerHpMax(int towerId) {
        int towerTroopId = GuardTowerHelper.getGuardTowerTroopId(towerId);
        if (towerTroopId == 0) {
            return 0;
        }
        return ResHolder.getInstance().getValueFromMap(TroopTemplate.class, towerTroopId).getSoldierPairList().get(0).getValue();
    }
}

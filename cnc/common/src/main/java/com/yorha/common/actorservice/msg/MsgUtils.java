package com.yorha.common.actorservice.msg;

import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.InvalidProtocolBufferException;
import com.yorha.common.actor.msg.ActorExceptionMsg;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.MigrateException;
import com.yorha.common.io.MsgType;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.System;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MsgUtils {
    private static final Logger LOGGER = LogManager.getLogger(MsgUtils.class);

    private static final HashMap<Integer, String> CS_TYPE_MSG_NAME_MAP = new HashMap<>();
    private static final HashMap<Integer, String> SS_TYPE_MSG_NAME_MAP = new HashMap<>();

    static {
        for (Map.Entry<Integer, GeneratedMessageV3> kv : MsgType.getMsgId2ProtoMsgMap().entrySet()) {
            CS_TYPE_MSG_NAME_MAP.put(kv.getKey(), ClassNameCacheUtils.getSimpleName(kv.getValue().getClass()));
        }
        for (Map.Entry<Integer, GeneratedMessageV3> kv : SsMsgTypes.getMsgId2ProtoMsgMap().entrySet()) {
            SS_TYPE_MSG_NAME_MAP.put(kv.getKey(), ClassNameCacheUtils.getSimpleName(kv.getValue().getClass()));
        }
    }

    /**
     * 通过msgType获取消息名，带缓存
     */
    public static String getMsgNameFromCsMsgType(int msgType) {
        final String name = CS_TYPE_MSG_NAME_MAP.get(msgType);
        if (name == null) {
            return "csMsgType:unknown";
        }
        return name;
    }

    /**
     * ss版本
     */
    public static String getMsgNameFromSsMsgType(int msgType) {
        final String name = SS_TYPE_MSG_NAME_MAP.get(msgType);
        if (name == null) {
            return "ssMsgType:unknown";
        }
        return name;
    }

    /**
     * 指定proto解析byte
     */
    @SuppressWarnings("unchecked")
    public static <T> T parseProto(GeneratedMessageV3 messageV3, ByteString payload) {
        try {
            GeneratedMessageV3 parsed = messageV3.getParserForType().parseFrom(payload);
            return (T) parsed;
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalArgumentException("msgType parse failed:" + messageV3);
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T parseProto(GeneratedMessageV3 messageV3, byte[] payload) {
        try {
            GeneratedMessageV3 parsed = messageV3.getParserForType().parseFrom(payload);
            return (T) parsed;
        } catch (InvalidProtocolBufferException e) {
            LOGGER.warn("MsgUtils parseProto ByteString payload fail, ", e);
            throw new IllegalArgumentException("msgType parse failed:" + messageV3);
        }
    }

    /**
     * 指定msgType 解析 cs proto ByteString
     */
    @SuppressWarnings("unchecked")
    public static <T> T parseCsProto(int msgType, ByteString payload) {
        GeneratedMessageV3 messageV3 = MsgType.getMsgFromType(msgType);
        if (messageV3 == null) {
            throw new IllegalArgumentException("msgType not recognized:" + msgType);
        }
        try {
            GeneratedMessageV3 parsed = messageV3.getParserForType().parseFrom(payload);
            return (T) parsed;
        } catch (InvalidProtocolBufferException e) {
            LOGGER.warn("MsgUtils parseCsProto ByteString payload fail, ", e);
            throw new IllegalArgumentException("msgType parse failed:" + messageV3);
        }
    }

    /**
     * 指定msgType 解析 cs proto byte[]
     */
    @SuppressWarnings("unchecked")
    public static <T> T parseCsProto(int msgType, byte[] payload) {
        GeneratedMessageV3 messageV3 = MsgType.getMsgFromType(msgType);
        if (messageV3 == null) {
            throw new IllegalArgumentException("msgType not recognized:" + msgType);
        }
        try {
            GeneratedMessageV3 parsed = messageV3.getParserForType().parseFrom(payload);
            return (T) parsed;
        } catch (InvalidProtocolBufferException e) {
            LOGGER.warn("MsgUtils parseCsProto byte[] payload fail, ", e);
            throw new IllegalArgumentException("msgType parse failed:" + messageV3);
        }
    }

    /**
     * 指定msgType 解析 ss proto ByteString
     */
    @SuppressWarnings("unchecked")
    public static <T> T parseProtoPayload(int msgType, ByteString payload) {
        GeneratedMessageV3 messageV3 = SsMsgTypes.getMsgFromType(msgType);
        if (messageV3 == null) {
            throw new IllegalArgumentException("msgType not recognized:" + msgType);
        }
        try {
            GeneratedMessageV3 parsed = messageV3.getParserForType().parseFrom(payload);
            return (T) parsed;
        } catch (InvalidProtocolBufferException e) {
            LOGGER.warn("MsgUtils parseProtoPayload ByteString payload fail, ", e);
            throw new IllegalArgumentException("msgType parse failed:" + messageV3);
        }
    }

    public static System.YoActorMsgPayload payloadToProto(IActorMsg payload) {
        System.YoActorMsgPayload.Builder builder = System.YoActorMsgPayload.newBuilder();
        if (payload instanceof TypedMsg) {
            TypedMsg tm = (TypedMsg) payload;
            builder.setTypedMsg(System.TypedMsg.newBuilder()
                    .setMsgType(tm.getMsgType())
                    .setData(tm.getBytes())
            );
        } else if (payload instanceof ActorExceptionMsg) {
            Exception e = ((ActorExceptionMsg) payload).getErr();
            System.YoExceptionSnapshot.Builder exceptionSnapshot = System.YoExceptionSnapshot.newBuilder()
                    .setClazzName(ClassNameCacheUtils.getSimpleName(e.getClass()));
            if (e instanceof GeminiException) {
                exceptionSnapshot
                        .setErrorCode(((GeminiException) e).getCodeId())
                        .setMsg(e.getMessage());
                if (e instanceof MigrateException) {
                    exceptionSnapshot.setZoneId(((MigrateException) e).getZoneId());
                }
            } else {
                exceptionSnapshot.setErrorCode(ErrorCode.FAILED.getCodeId());
                if (StringUtils.isEmpty(e.getMessage())) {
                    exceptionSnapshot.setMsg(ClassNameCacheUtils.getSimpleName(e.getClass()));
                } else {
                    exceptionSnapshot.setMsg(e.getMessage());
                }
            }
            builder.setException(exceptionSnapshot);
        } else {
            WechatLog.error("transfer ActorMsgEnvelope payload failed: {}", payload);
        }
        return builder.build();
    }

    public static IActorMsg parsePayload(final System.YoActorMsgPayload payload) {
        if (payload.hasTypedMsg()) {
            final int msgType = payload.getTypedMsg().getMsgType();
            final ByteString data = payload.getTypedMsg().getData();
            final GeneratedMessageV3 protoMsg = parseProtoPayload(msgType, data);
            return new TypedMsg(msgType, protoMsg);
        } else if (payload.hasException()) {
            final int errorCode = payload.getException().getErrorCode();
            if (errorCode == ErrorCode.MIGRATE.getCodeId()) {
                return new ActorExceptionMsg(new MigrateException(payload.getException().getZoneId(),
                        "remote exception:" + payload.getException().getClazzName() + " msg:" + payload.getException().getMsg()));
            }
            return new ActorExceptionMsg(new GeminiException(
                    "remote exception:" + payload.getException().getClazzName() + " msg:" + payload.getException().getMsg(),
                    null,
                    payload.getException().getErrorCode()
            ));
        }
        return null;
    }
}

package com.yorha.common.resource.datatype;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class FloatArray extends ArrayList<Float> {
    public FloatArray(List collect) {
        super(collect);
    }

    public static FloatArray valueOf(String s) {
        return new FloatArray(Arrays.stream(Arrays.stream(s.split(",")).map(num -> Float.parseFloat(num)).toArray()).collect(Collectors.toList()));
    }
}

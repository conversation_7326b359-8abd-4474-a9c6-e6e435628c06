package com.yorha.common.helper;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.actor.node.ISceneActor;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.proto.SsClanBase;
import com.yorha.proto.SsGate;
import com.yorha.proto.SsScenePlayer;

import java.util.*;

/**
 * 广播
 *
 * <AUTHOR>
 */
public class BroadcastHelper {
    /**
     * 向目标广播！
     * 广播给所有的target。
     */
    public static void toAllTargets(Collection<IActorRef> targetRefList, IActorRef sender, GeneratedMessageV3 msg) {
        if (targetRefList.isEmpty()) {
            return;
        }
        Map<String, List<IActorRef>> broadcastBook = new HashMap<>(10);
        for (IActorRef targetRef : targetRefList) {
            if (targetRef == null) {
                throw new GeminiException("{} send {} to null", sender, msg);
            }
            final String busId = targetRef.getBusId();
            if (busId == null) {
                throw new GeminiException("{} send {} to {}! no bus id!", sender, msg, targetRef);
            }
            broadcastBook.computeIfAbsent(busId, (key) -> new LinkedList<>()).add(targetRef);
        }
        ActorMsgSystem.dispatchBroadcastMsg(broadcastBook, sender, msg);
    }

    /**
     * 小服原住民广播
     * 广播给所有zone上在线玩家（在大世界 or 在kvk or 在副本）
     * 广播消息给所有客户端sessionActor，不包括角色未绑定，必须是完成登录的在线玩家
     * 会打给gate，再广播给对应的sessionActor
     * 用于给客户端发送ntf
     */
    public static void toCsOnlinePlayerInZone(int zoneId, int msgType, GeneratedMessageV3 msg) {
        SsGate.BroadcastAllCmd cmd = SsGate.BroadcastAllCmd.newBuilder()
                .setMsgType(msgType)
                .setMsgBytes(msg.toByteString())
                .build();
        ActorSendMsgUtils.send(RefFactory.ofGate(zoneId), new TypedMsg(SsMsgTypes.getTypeFromMsg(cmd), cmd));
    }

    /**
     * 小服原住民广播
     * 广播给所有zone上在线玩家（在大世界 or 在kvk or 在副本）
     * 用于给playerActor发送ss消息
     */
    public static void toSsOnlinePlayerInZone(int zoneId, GeneratedMessageV3 msg) {
        SsGate.BroadcastOnlinePlayerSsCmd cmd = SsGate.BroadcastOnlinePlayerSsCmd.newBuilder()
                .setMsgType(SsMsgTypes.getTypeFromMsg(msg))
                .setMsgBytes(msg.toByteString())
                .build();
        ActorSendMsgUtils.send(RefFactory.ofGate(zoneId), new TypedMsg(SsMsgTypes.getTypeFromMsg(cmd), cmd));
    }

    /**
     * 场景广播
     * 小服zone级别广播！
     * 会打给BigScene，再广播给对应的sessionActor，session会根据ClientInfo下发匹配的消息
     */
    public static void toCsOnlinePlayerInZoneWithMultiLanguage(int zoneId, SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd cmd) {
        IActorRef actorRef = RefFactory.ofBigScene(zoneId);
        if (ServerContext.isSameNode(actorRef.getBusId())) {
            ActorSendMsgUtils.send(actorRef,
                    new ActorRunnable<ISceneActor>("toCsOnlinePlayerInZoneWithMultiLanguage", (sceneActor) -> sceneActor.handleMultiLanguage(cmd)));
            return;
        }
        ActorSendMsgUtils.send(actorRef, new TypedMsg(SsMsgTypes.getTypeFromMsg(cmd), cmd));
    }

    /**
     * 联盟广播
     * 如果在Clan上广播，请直接调用broadcastCsMsgToAllOnlineMemberInClan
     * 此处适合不在Clan却要广播联盟级别的场景
     * 会打给Clan，再广播给对应的sessionActor
     */
    public static void toCsOnlinePlayerInClan(int zoneId, long clanId, int msgType, GeneratedMessageV3 msg) {
        SsClanBase.BroadcastClanOnlineMemberCsCmd cmd = SsClanBase.BroadcastClanOnlineMemberCsCmd.newBuilder()
                .setMsgType(msgType)
                .setMsgBytes(msg.toByteString())
                .build();
        ActorSendMsgUtils.send(RefFactory.ofClan(zoneId, clanId), new TypedMsg(SsMsgTypes.getTypeFromMsg(cmd), cmd));
    }

    /**
     * 进程内所有Actor广播
     * 广播给本进程里所有的Actor
     * 目前只用于xmlReload通知
     */
    public static void toLocalAllGameActor(IActorMsg msg) {
        // 除了集群相关的所有Actor
        Collection<String> localActorRoleList = ServerContext.getActorSystem().getRegistryValue().getLocalActorRoleList();
        Set<String> safeList = new HashSet<>(localActorRoleList);
        ServerContext.getActorSystem().getRegistryValue().broadcastLocal(safeList, msg);
    }
}

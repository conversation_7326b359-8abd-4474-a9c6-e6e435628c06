package com.yorha.common.resource.resservice.item;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.RandomUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ItemRewardPool {
    private final List<ItemRewardBox> rewardBoxes;
    private int weight;
    private int randomTimes;

    private List<ItemReward> mustOutReward = new ArrayList<>();

    public ItemRewardPool(int randomTimes) {
        this.rewardBoxes = new ArrayList<>();
        this.randomTimes = randomTimes;
    }

    public void addMustOutReward(ItemReward reward) {
        if (randomTimes <= mustOutReward.size()) {
            throw new GeminiException("too many mustOutReward");
        }
        this.mustOutReward.add(reward);
    }

    public void addRewardBox(ItemRewardBox rewardBox) {
        this.rewardBoxes.add(rewardBox);
        this.weight += rewardBox.getWeight();
    }

    private ItemRewardBox randomBox() {
        if (CollectionUtils.isEmpty(rewardBoxes) || weight == 0) {
            return null;
        }
        // 个数为1的奖池，使用十万分比。多个数的奖池，使用概率累加和
        if (rewardBoxes.size() == 1) {
            return rewardBoxes.get(0);
        }
        return RandomUtils.randomByWeight(rewardBoxes, ItemRewardBox::getWeight);
    }

    public List<ItemReward> randomReward(ItemRewardParam param) {
        List<ItemReward> res = new ArrayList<>();

        int times = randomTimes - param.getDecreaseTimes();
        if (times <= 0) {
            return new ArrayList<>();
        }
        if (param.isNeedMustOut()) {
            if ((mustOutReward != null)) {
                times -= mustOutReward.size();
                res.addAll(mustOutReward);
            }
        }
        for (int i = 0; i < times; i++) {
            ItemReward reward = Objects.requireNonNull(randomBox()).reward(param);
            if (reward == null) {
                throw new GeminiException("reward null:{}", rewardBoxes);
            }
            res.add(reward);
        }
        return res;
    }
}

package com.yorha.common.rank;

/**
 * some description
 *
 * <AUTHOR>
 */
public class RankConstant {
    //单位
    /**
     * 军团
     */
    public static final int MEMBER_CLAN = 1;
    /**
     * 玩家
     */
    public static final int MEMBER_PLAYER = 2;
    /**
     * 服务器
     */
    public static final int MEMBER_ZONE = 3;

    //范围 scope
    /**
     * 单区范围
     */
    public static final int SCOPE_ZONE = 1;
    /**
     * 军团范围
     */
    public static final int SCOPE_CLAN = 2;

    // id
    /**
     * 全服 军团战力排行榜
     */
    public static final int ZONE_CLAN_POWER_RANK = 1;
    /**
     * 全服 军团击杀排行榜
     */
    public static final int ZONE_CLAN_KILL_RANK = 2;
    /**
     * 全服 军团旗帜排行榜
     */
    public static final int ZONE_CLAN_FLAG_RANK = 3;
    /**
     * 全服 个人战力排行榜
     */
    public static final int ZONE_PLAYER_POWER_RANK = 4;
    /**
     * 全服 个人击杀排行榜
     */
    public static final int ZONE_PLAYER_KILL_RANK = 5;
    /**
     * 全服 个人城堡等级排行榜
     */
    public static final int ZONE_PLAYER_CITY_LEVEL_RANK = 6;
    /**
     * 全服 个人采集排行榜
     */
    public static final int ZONE_PLAYER_COLLECT_RANK = 7;
    /**
     * 全服 个人远征排行榜
     */
    public static final int ZONE_PLAYER_EXPEDITION_STAR_RANK = 29;
    /**
     * 军团 个人战力排行榜
     */
    public static final int CLAN_PLAYER_POWER_RANK = 8;
    /**
     * 军团 个人击杀排行榜
     */
    public static final int CLAN_PLAYER_KILL_RANK = 9;
    /**
     * 军团 个人科技捐献排行榜
     */
    public static final int CLAN_PLAYER_TECHNOLOGY_DONATION_RANK = 10;
    /**
     * 军团 个人建筑建造排行榜
     */
    public static final int CLAN_PLAYER_BUILD_RANK = 11;
    /**
     * 军团 个人联盟帮助排行榜
     */
    public static final int CLAN_PLAYER_HELP_RANK = 12;
    /**
     * 军团 个人资源援助排行榜
     */
    public static final int CLAN_PLAYER_RESOURCE_ASSIST_RANK = 13;

}

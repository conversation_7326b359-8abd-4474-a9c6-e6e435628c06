package com.yorha.common.concurrent.singleflight;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * SingleFlight执行任务结构体。
 *
 * <AUTHOR>
 */
public class Call<T> {
    public static class SyncCallTask<T> {
        private final CountDownLatch latch;
        private Throwable exception;
        private T result;
        private boolean isCompleted;

        public SyncCallTask() {
            this.latch = new CountDownLatch(1);
        }

        public T get() throws Throwable {
            this.latch.await();
            if (!this.isCompleted) {
                throw new IllegalStateException("Not completed");
            }
            if (this.exception != null) {
                throw this.exception;
            }
            return this.result;
        }

        public void completeExceptionally(Throwable throwable) {
            this.exception = throwable;
            this.isCompleted = true;
            this.latch.countDown();
        }

        public void complete(T result) {
            this.result = result;
            this.isCompleted = true;
            this.latch.countDown();
        }
    }

    /**
     * 同步任务。
     */
    private SyncCallTask<T> syncCallTask;
    /**
     * 异步future。
     */
    private List<CompletableFuture<T>> futures;


    public Call() {
    }


    /**
     * 通知任务结果。
     */
    public void notifyTask(final T result, final Throwable exception) {
        if (this.futures != null) {
            if (exception != null) {
                this.futures.forEach(f -> f.completeExceptionally(exception));
            } else {
                this.futures.forEach(f -> f.complete(result));
            }
        }
        if (this.syncCallTask != null) {
            if (exception != null) {
                this.syncCallTask.completeExceptionally(exception);
            } else {
                this.syncCallTask.complete(result);
            }
        }
    }

    /**
     * 添加异步任务。
     *
     * @return CompletableFuture<T> 用于和客户端通信的结构体
     */
    public CompletableFuture<T> addAsyncTask() {
        if (this.futures == null) {
            this.futures = new ArrayList<>();
        }
        CompletableFuture<T> future = new CompletableFuture<>();
        this.futures.add(future);
        return future;
    }

    /**
     * 添加同步任务。
     *
     * @return SyncCallTask<T> 同步任务实体。
     */
    public SyncCallTask<T> addSyncTask() {
        if (this.syncCallTask == null) {
            this.syncCallTask = new SyncCallTask<>();
        }
        return this.syncCallTask;
    }
}

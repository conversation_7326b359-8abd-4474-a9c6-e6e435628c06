package com.yorha.common.actor.node;

import com.yorha.common.etcd.IWatchHandler;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.WhiteListConfig;
import com.yorha.common.utils.YamlUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 白名单配置的监听handler
 *
 * <AUTHOR>
 */
public class WhiteListConfigHandler implements IWatchHandler {
    private static final Logger LOGGER = LogManager.getLogger(WhiteListConfigHandler.class);


    @Override
    public void onDelete(final @NotNull String key) {
        LOGGER.info("gemini_system WhiteListConfigHandler delete key={}", key);
        ServerContext.setWhiteListConfig(WhiteListConfig.valueOf(Collections.emptyList()));
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onUpdate(final @NotNull String key, final @NotNull String value) {
        LOGGER.warn("gemini_system white_list update key={}, value={}", key, value);
        final WhiteListConfig newTempWhiteListConfig;
        if (value != null) {
            List<Map<String, Object>> list = YamlUtils.newInstance(value, List.class);
            TempWhiteListConfig config = TempWhiteListConfig.valueOf(list);
            newTempWhiteListConfig = WhiteListConfig.valueOf(config.getWhiteConfigList());
        } else {
            newTempWhiteListConfig = WhiteListConfig.valueOf(Collections.emptyList());
        }
        final WhiteListConfig elderTempWhiteListConfig = ServerContext.getWhiteListConfig();
        LOGGER.warn("gemini_system white_list change! [{}] -> [{}]", elderTempWhiteListConfig, newTempWhiteListConfig);
        ServerContext.setWhiteListConfig(newTempWhiteListConfig);
    }
}

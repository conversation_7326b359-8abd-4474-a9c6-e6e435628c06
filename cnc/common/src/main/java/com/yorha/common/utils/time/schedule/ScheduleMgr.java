package com.yorha.common.utils.time.schedule;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.concurrent.NamedRunnable;
import com.yorha.common.constant.LogConstant;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.LogHelper;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jctools.queues.MpscLinkedQueue;

import javax.annotation.Nullable;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 业务定时器管理中心
 * 受业务时间影响
 * 进程单例
 * 被cancel的任务会在下一次tick的时候从队列中删掉
 * 使用时间轮，slot_interval 100ms
 *
 * <AUTHOR>
 */
public class ScheduleMgr {
    private static final Logger LOGGER = LogManager.getLogger(ScheduleMgr.class);

    private String busId = null;

    private ScheduleMgr() {
    }

    private static class InstanceHolder {
        private static final ScheduleMgr INSTANCE = new ScheduleMgr();
    }

    public static ScheduleMgr getInstance() {
        return ScheduleMgr.InstanceHolder.INSTANCE;
    }

    /**
     * slot interval 100ms
     * 跨度最长支持(2^31-1)*SI = 4971day
     */
    private static final int SI = 100;
    private static final int TVN_BITS = 6;
    private static final int TVR_BITS = 8;
    /**
     * 64
     */
    private static final int TVN_SIZE = (1 << TVN_BITS);
    /**
     * 256
     */
    private static final int TVR_SIZE = (1 << TVR_BITS);
    /**
     * 63 111111
     */
    private static final int TVR_MASK = (TVR_SIZE - 1);
    /**
     * 定时器启动时间戳 /ms
     */
    private final long startTime = SystemClock.now();
    /**
     * 当前tick值，非线程安全, 单位 100ms
     */
    private int tick = 0;
    /**
     * 最大时间
     */
    private final long maxTimeTs = startTime + ((long) Integer.MAX_VALUE * SI);
    /**
     * 最大时间间隔
     */
    private static final long MAX_TIME = (long) Integer.MAX_VALUE * SI;
    /**
     * TIMER_WHEELS(非线程安全，操作均须在单线程环境下),里面包含5个时间轮，从外到里依次为 tv5,tv4,tv3,tv2,tv1
     * tv5~tv2大小是64， tv1为256，一共包含512个定时器链表
     */
    private final ScheduleTaskList[] timerWheels = new ScheduleTaskList[512];

    private int offset(int n) {
        return TVR_SIZE + (n * TVN_SIZE);
    }

    private int index(int v, int n) {
        //255 11111111
        int tvnMask = (TVN_SIZE - 1);
        return ((v >> (TVR_BITS + n * TVN_BITS)) & tvnMask);
    }

    private long getCheckBase() {
        return startTime + ((long) tick * SI);
    }

    /**
     * 缓冲申请队列，保证线程安全，每次tick真正加入到定时任务队列
     */
    private final MpscLinkedQueue<ScheduleTask> tmpQueue = new MpscLinkedQueue<>();

    /**
     * 被cancel的task，在tick中做真正的删除
     */
    private final MpscLinkedQueue<ScheduleTask> cancelledTasks = new MpscLinkedQueue<>();
    /**
     * 基于SystemScheduleMgr定时器任务。
     */
    private ScheduledFuture<?> scheduledFuture;

    private final ReentrantLock lock = new ReentrantLock();
    private static final Logger PERF_LOGGER = LogManager.getLogger(LogConstant.LOG_TYPE_PERF_STAT);

    public boolean isRunning() {
        this.lock.lock();
        try {
            return this.scheduledFuture != null;
        } finally {
            this.lock.unlock();
        }
    }

    public void start(String busId) {
        this.lock.lock();
        this.busId = busId;
        try {
            if (this.scheduledFuture != null) {
                throw new GeminiException("repeat start ScheduleMgr");
            }
            // 精度100ms
            this.scheduledFuture = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(
                    new NamedRunnable("logic-ScheduleMgr-tick", this::tick),
                    10, 100, TimeUnit.MILLISECONDS);
        } finally {
            this.lock.unlock();
        }
    }

    /**
     * 关闭ScheduleTask
     */
    public void shutdown() {
        this.lock.lock();
        try {
            if (this.scheduledFuture == null) {
                return;
            }
            this.scheduledFuture.cancel(false);
            this.scheduledFuture = null;
        } finally {
            this.lock.unlock();
        }
    }

    /**
     * 插入时间轮
     *
     * @param task 定时器任务对象
     */
    private void offer(ScheduleTask task) {
        // 定时器到期时间(跨度最长支持2485day左右)
        long interval = task.getNextExecuteTime() - startTime;
        long tempDelay = interval / SI;
        if ((interval % SI) > 0) {
            tempDelay++;
        }
        // 限定最大值为2^31-1
        if (tempDelay > Integer.MAX_VALUE) {
            WechatLog.error("ScheduleMgr offer too long expires task:{} ", task);
            return;
        }
        int delay = (int) tempDelay;
        int vIndex = computeVIndex(delay, tick); // ← 调用提取的方法
        task.setIndex(vIndex);
        ScheduleTaskList taskList = timerWheels[vIndex];
        if (taskList == null) {
            taskList = new ScheduleTaskList();
            timerWheels[vIndex] = taskList;
        }
        taskList.add(task);
    }


    /**
     * 根据 delay 和 tick 计算任务应插入的时间轮索引（vIndex）
     *
     * @param delay 延迟时间（单位：tick）
     * @param tick  当前 tick 数
     * @return 时间轮槽位索引
     */
    private int computeVIndex(int delay, int tick) {
        int idx = delay - tick;

        if (idx < TVR_SIZE) {
            return delay & TVR_MASK;
        } else if (idx < (1 << (TVR_BITS + TVN_BITS))) {
            return offset(0) + index(delay, 0);
        } else if (idx < (1 << (TVR_BITS + 2 * TVN_BITS))) {
            return offset(1) + index(delay, 1);
        } else if (idx < (1 << (TVR_BITS + 3 * TVN_BITS))) {
            return offset(2) + index(delay, 2);
        } else {
            return offset(3) + index(delay, 3);
        }
    }

    /**
     * 删除时间轮任务
     *
     * @param task 需要删除的 定时器任务对象
     */
    private void remove(ScheduleTask task) {
        if (task == null) {
            return;
        }
        if (task.getHasCanceled()) {
            return;
        }
        ScheduleTaskList taskList = timerWheels[(task.getIndex())];
        if (taskList == null || (taskList.size() <= 0)) {
            return;
        }
        if (!taskList.remove(task)) {
            LOGGER.info("ScheduleMgr remove {} is not in taskList", task);
        }
        task.setHasCanceled();
    }


    /**
     * 向下级联
     *
     * @param offset 偏移(时间轮id)
     * @param index  索引(在单轮中的索引)
     * @return slotId
     */
    private int cascade(int offset, int index) {
        ScheduleTaskList taskList = timerWheels[(offset + index)];
        if ((taskList == null) || (taskList.size() <= 0)) {
            return index;
        }
        int size = taskList.size();
        while (size > 0) {
            size--;
            ScheduleTask task = taskList.poll();
            if (task == null) {
                continue;
            }
            // 重新插入时间轮
            offer(task);
        }
        if (taskList.size() > 0) {
            LOGGER.info("cascade can cascade same solt");
        }
        return index;
    }

    private void tick() {
        long oldNative = SystemClock.nowNative();
        long now = SystemClock.now();
        long lastTick = tick;
        while (getCheckBase() <= now) {
            // 扫描tmp队列，将新任务加入到实际的schedule队列中去
            while (true) {
                ScheduleTask task = tmpQueue.poll();
                if (task == null) {
                    break;
                }
                if (task.isCanceled()) {
                    continue;
                }
                task.resetHasCanceled();
                offer(task);
            }
            // 将cancel掉的任务及时从队列中清除出去
            while (true) {
                // 考虑增加单次tick中删除task的数量上限，以此规避瞬间大量cancel对单次tick耗时的影响
                ScheduleTask cancelled = cancelledTasks.poll();
                if (cancelled == null) {
                    break;
                }
                remove(cancelled);
            }
            // 获取check_base 的最低8位
            int index = tick & TVR_MASK;
            // 降级
            if ((tick > 0) &&
                    // 最低8位为0
                    (index == 0) &&
                    // 9~14位为0
                    (cascade(offset(0), index(tick, 0)) == 0) &&
                    // 15~20位为0
                    (cascade(offset(1), index(tick, 1)) == 0) &&
                    // 21~26位为0
                    (cascade(offset(2), index(tick, 2)) == 0)) {
                // 0~26位均为0
                cascade(offset(3), index(tick, 3));
            }
            // 消费当前index对应的slot
            ScheduleTaskList taskList = timerWheels[index];
            if (taskList != null) {
                int size = taskList.size();
                while (size > 0) {
                    size--;
                    ScheduleTask task = taskList.poll();
                    if (task == null) {
                        continue;
                    }
                    if (task.isNotReady(now)) {
                        LogHelper.timerErrorLog("consume schedule task fail now:{} task:{} id:{}{} base:{} tick:{}", now, task, task.getName(), task.getId(), startTime, tick);
                        continue;
                    }
                    boolean needOffer = task.execute();
                    if (needOffer) {
                        tmpQueue.offer(task);
                    }
                    if (!task.getHasCanceled()) {
                        task.setHasCanceled();
                    }
                }
            }
            tick++; // 一个tick 100ms
        }
        long totalCost = SystemClock.nowNative() - oldNative;
        if (totalCost > 5) {
            // 耗时5ms以上
            PERF_LOGGER.info("gemini_perf scheduleMgr cost:{} tickNum:{}", totalCost, tick - lastTick);
        }
    }

    /**
     * 设置单次定时器
     */
    @Nullable
    public ScheduleTask schedule(IActorRef ref, int id, String name, long initialDelay, TimeUnit unit) {
        if (initialDelay < 0) {
            LogHelper.timerErrorLog("schedule delay<0 name:{}", name);
            return null;
        }
        return newTask(ref, id, name, initialDelay, 0, unit, false);
    }

    /**
     * 设置循环定时器
     * FixedRate不受任务执行时间影响
     * FixedDelay会，本项目只提供该接口
     */
    @Nullable
    public ScheduleTask scheduleWithRepeatDelay(IActorRef ref, int id, String name, long initialDelay, long period, TimeUnit unit) {
        if (initialDelay < 0) {
            LogHelper.timerErrorLog("scheduleWithRepeatDelay initialDelay<0 name:{}", name);
            return null;
        }
        if (period < 0) {
            LogHelper.timerErrorLog("scheduleWithRepeatDelay period<0 name:{}", name);
            return null;
        }
        return newTask(ref, id, name, initialDelay, period, unit, false);
    }

    @Nullable
    public ScheduleTask scheduleWithFixDelay(IActorRef ref, int id, String name, long initialDelay, long period, TimeUnit unit) {
        if (initialDelay < 0) {
            LogHelper.timerErrorLog("scheduleWithFixDelay initialDelay<0 name:{}", name);
            return null;
        }
        if (period < 0) {
            LogHelper.timerErrorLog("scheduleWithFixDelay period<0 name:{}", name);
            return null;
        }
        return newTask(ref, id, name, initialDelay, period, unit, true);
    }


    @Nullable
    public ScheduleTask newTask(IActorRef ref, int id, String name, long initialDelay, long period, TimeUnit unit, boolean isFix) {
        if (unit.toMillis(initialDelay + period) >= MAX_TIME) {
            LogHelper.timerErrorLog("ScheduleMgr newTask too long expire schedule task {} {} {} {} {} {}", name, id, initialDelay, period, unit, isFix);
            return null;
        }
        if (isFix && period <= 0) {
            LogHelper.timerErrorLog("ScheduleMgr isFix but param error name={} id={} period={}", name, id, period);
            return null;
        }
        final ScheduleTask scheduleTask = new ScheduleTask(this, ref, id, name, unit.toMillis(initialDelay), unit.toMillis(period), isFix);
        tmpQueue.offer(scheduleTask);
        return scheduleTask;
    }

    public long getMaxTimeTs() {
        return maxTimeTs;
    }

    void markCancel(ScheduleTask task) {
        if (task.scheduleMgr != this) {
            LogHelper.timerErrorLog("cancel task error: {} {}", task, this);
            return;
        }
        this.cancelledTasks.add(task);
    }

    public String getBusId() {
        return busId;
    }

}

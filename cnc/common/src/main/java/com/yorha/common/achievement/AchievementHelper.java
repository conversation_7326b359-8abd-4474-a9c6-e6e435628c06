package com.yorha.common.achievement;

import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.Pair;
import com.yorha.game.gen.prop.Int32SecondStatisticMapProp;
import com.yorha.game.gen.prop.Int32SingleStatisticMapProp;
import com.yorha.game.gen.prop.SecondStatisticProp;
import com.yorha.game.gen.prop.SingleStatisticProp;
import com.yorha.proto.CommonEnum;
import res.template.DemandConfigTemplate;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.yorha.common.enums.statistic.StatisticEnum.*;

/**
 * kvk成就的一些static 计算方法
 *
 * <AUTHOR>
 */
public class AchievementHelper {

    private static final Map<StatisticEnum, CommonEnum.AchievementStatisticCheckerType> MAP = new ConcurrentHashMap<>();

    /**
     * 根据统计项值判断需求是否完成
     * long
     *
     * @param value     统计项值
     * @param needValue 需求值
     * @return 是否已达成
     */
    public static boolean isDemandComplete(long value, long needValue) {
        if (value < needValue) {
            return false;
        }
        return true;
    }

    /**
     * 根据checkType 获取绑定的统计项
     *
     * @param checkerType 触发类型
     * @return 统计项类型
     */
    @Nullable
    public static StatisticEnum checkType2StatisticEnum(CommonEnum.AchievementStatisticCheckerType checkerType) {
        switch (checkerType) {
            case ASCT_JOIN_CLAN_BUILD:
                return JOIN_CLAN_BUILD;
            case ASCT_COLLECT_RESOURCE:
                return RESBUILDING_COLLECT_RESOURCE_NUM;
            case ASCT_KILL_MONSTER_CATEGORY:
                return KILL_CATEGORY_MONSTER;
            case ASCT_COLLECT_ANY_RESOURCE:
                return RESBUILDING_COLLECT_ANY_RESOURCE_NUM;
            case ASCT_KILL_MONSTER_GROUP:
                return KILL_MONSTER_GROUP_NUM;
            case ASCT_SURVEY_PART:
            case ASCT_UNLOCK_SOLDIER:
            case ASCT_UNLOCK_HERO:
                return CANT_RECORD_SECOND;
            default:
                return null;
        }
    }

    /**
     * 统计项新值计算
     *
     * @param calcType 计算类型
     * @param oldValue 老值
     * @param value    变量值
     * @return 新值
     */
    public static long calcStatistic(CommonEnum.DataRecordCalcType calcType, long oldValue, long value) {
        switch (calcType) {
            case DRCT_ADD:
                return oldValue + value;
            case DRCT_MAX:
                return MathUtils.max(oldValue, value);
            case DRCT_SUB:
                return oldValue - value;
            case DRCT_COVER:
                return value;
            default:
                throw new GeminiException("AchievementHelper calcStatistic unsupport type " + calcType);
        }
    }

    /**
     * 本统计项能否记录
     */
    public static boolean canRecord(StatisticEnum statisticEnum) {
        if (statisticEnum == CANT_RECORD_SINGLE) {
            return false;
        }
        if (statisticEnum == CANT_RECORD_SECOND) {
            return false;
        }
        return true;
    }

    @Nullable
    public static Pair<Integer, Long> getNormalDemandProgress(
            StatisticEnum statisticEnum,
            Int32SecondStatisticMapProp secondStatisticMapProp,
            Int32SingleStatisticMapProp singleStatisticMapProp,
            DemandConfigTemplate demandConfigTemplate
    ) {
        // 二维统计项
        if (statisticEnum.isSecondStatistic()) {
            if (secondStatisticMapProp == null) {
                return null;
            }
            final SecondStatisticProp secondStatisticProp = secondStatisticMapProp.get(statisticEnum.ordinal());
            if (secondStatisticProp == null) {
                return null;
            }
            final List<Integer> params = demandConfigTemplate.getParamList();
            final int firstParam = params.get(0);
            final SingleStatisticProp singleStatisticProp = secondStatisticProp.getSingleStatisticMap().get(firstParam);
            if (singleStatisticProp == null) {
                return null;
            }
            // 需求未达成
            return Pair.of(demandConfigTemplate.getId(), singleStatisticProp.getValue());
        }
        // 一维统计项
        if (statisticEnum.isSingleStatistic()) {
            if (singleStatisticMapProp == null) {
                return null;
            }
            final SingleStatisticProp singleStatisticProp = singleStatisticMapProp.get(statisticEnum.ordinal());
            if (singleStatisticProp == null) {
                return null;
            }
            return Pair.of(demandConfigTemplate.getId(), singleStatisticProp.getValue());
        }
        return null;
    }

    public static CommonEnum.AchievementStatisticCheckerType getCheckTypeFromStatistic(int statisticId) {
        StatisticEnum statisticEnum = StatisticEnum.forNumber(statisticId);
        if (statisticEnum == null) {
            return CommonEnum.AchievementStatisticCheckerType.ASCT_NONE;
        }
        return MAP.getOrDefault(statisticEnum, CommonEnum.AchievementStatisticCheckerType.ASCT_NONE);
    }
}

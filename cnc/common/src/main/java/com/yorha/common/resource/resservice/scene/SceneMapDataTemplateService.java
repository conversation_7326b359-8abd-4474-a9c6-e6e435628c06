package com.yorha.common.resource.resservice.scene;

import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.mapgrid.MapGridDataManager;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import res.template.MapConfigTemplate;

import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */

public class SceneMapDataTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(SceneMapDataTemplateService.class);
    private final ConcurrentHashMap<Integer, String> mapVersion = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, String> mapFilename = new ConcurrentHashMap<>();
    private final Map<String, Set<Integer>> dungeonMapFileName2MapId = new HashMap<>();
    private final Map<Integer, Map<Integer, List<Struct.Point>>> mapResPoints = new HashMap<>();

    public SceneMapDataTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        LOGGER.info("load map xml begin");
        try (FileInputStream steam = new FileInputStream(getNavmeshFileDir() + "version.xml")) {
            SAXReader reader = new SAXReader();
            Document doc = reader.read(steam);
            Element root = doc.getRootElement();
            for (Element dataE : root.elements("map")) {
                try {
                    int mapId = Integer.parseInt(dataE.attributeValue("id"));
                    mapVersion.put(mapId, dataE.attributeValue("version"));
                    String fileName = getNavmeshFileDir() + dataE.attributeValue("name") + ".bin";
                    mapFilename.put(mapId, fileName);
                } catch (Exception ex) {
                    throw new ResourceException(StringUtils.format("map version xml item error: {}", ex.toString()));
                }
            }
            LOGGER.info("load map xml end. version:{} fileName:{}", mapVersion, mapFilename);
        } catch (Exception ex) {
            throw new ResourceException(StringUtils.format("map version xml load fail"), ex);
        }
        //地图表
        for (MapConfigTemplate template : getResHolder().getListFromMap(MapConfigTemplate.class)) {
            Map<Integer, List<Struct.Point>> poslist = new HashMap<>();
            for (IntTripleType res : template.getInnerCityGoldTripleList()) {
                Struct.Point.Builder posbuild = Struct.Point.newBuilder();
                posbuild.setMapId(template.getId()).setX(res.getValue1()).setY(res.getValue2());
                poslist.computeIfAbsent(res.getKey(), k -> new ArrayList<>()).add(posbuild.build());
            }
            for (IntTripleType res : template.getInnerCityOilTripleList()) {
                Struct.Point.Builder posbuild = Struct.Point.newBuilder();
                posbuild.setMapId(template.getId()).setX(res.getValue1()).setY(res.getValue2());
                poslist.computeIfAbsent(res.getKey(), k -> new ArrayList<>()).add(posbuild.build());
            }
            mapResPoints.put(template.getId(), poslist);
        }
    }

    public Map<String, Set<Integer>> getDungeonMapFileName2MapId() {
        return dungeonMapFileName2MapId;
    }

    public String getNavmeshFileDir() {
        return getResHolder().getResPath() + "//navmesh//";
    }

    public String getNavmeshFileName(int mapId) {
        return mapFilename.get(mapId);
    }

    public String getNavmeshVersion(int mapId) {
        return mapVersion.getOrDefault(mapId, "");
    }

    public MapConfigTemplate getMapConfig(int mapId) {
        return ResHolder.getInstance().getValueFromMap(MapConfigTemplate.class, mapId);
    }

    public static boolean isPointInsideBigScene(Point point) {
        return isPointInside(point, ResHolder.getTemplate(MapConfigTemplate.class, BigSceneConstants.BIG_SCENE_MAP_ID));
    }

    public static boolean isPointInside(Point point, MapConfigTemplate config) {
        int width = UnitConvertUtils.meterToCm(config.getWidth());
        int length = UnitConvertUtils.meterToCm(config.getLength());
        if (point.getX() <= 0 || point.getX() > width) {
            return false;
        }
        if (point.getY() <= 0 || point.getY() > length) {
            return false;
        }
        return true;
    }

    /**
     * 合法点校验
     */
    public static ErrorCode isLegalPoint(Point point, MapConfigTemplate config) {
        if (!isPointInside(point, config)) {
            return ErrorCode.ARMY_DST_POINT_NOT_WALKABLE_STATIC;
        }
        if (config.getId() == BigSceneConstants.BIG_SCENE_MAP_ID) {
            // 州开放判断
            int regionId = MapGridDataManager.getRegionId(config.getId(), point);
            if (!ResHolder.getResService(MapSubdivisionDataService.class).checkRegionOpen(regionId)) {
                return ErrorCode.MAP_REGION_NOT_OPEN;
            }
        }
        return ErrorCode.OK;
    }

    //
    public Struct.Point randomResPos(int mapId, int resId) {
        Map<Integer, List<Struct.Point>> resList = mapResPoints.get(mapId);
        if (resList == null) {
            return Struct.Point.getDefaultInstance();
        }
        List<Struct.Point> posList = resList.get(resId);
        if (posList == null || posList.isEmpty()) {
            return Struct.Point.getDefaultInstance();
        }
        return RandomUtils.randomList(posList);
    }

    @Override
    public void checkValid() throws ResourceException {

    }
}

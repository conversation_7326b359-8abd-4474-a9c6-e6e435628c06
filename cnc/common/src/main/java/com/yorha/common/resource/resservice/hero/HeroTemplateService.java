package com.yorha.common.resource.resservice.hero;

import com.google.common.collect.Maps;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.gemini.utils.StringUtils;
import res.template.*;

import java.util.*;

/**
 * 英雄相关的配置后处理
 *
 * <AUTHOR>
 */
public class HeroTemplateService extends AbstractResService {

    public HeroTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    /**
     * 英雄等级
     */
    private final Map<String, HeroLevelRhTemplate> heroLevelMap = Maps.newHashMap();


    private final Map<String, HeroSkillLevelRhTemplate> heroSkillLevelStore = Maps.newHashMap();

    private final Map<String, HeroSkillLevelRhTemplate> heroSkillLevelStore4Star = Maps.newHashMap();


    /**
     * 英雄星级
     */
    private final Map<Integer, HeroStarRhTemplate> heroStarMap = Maps.newHashMap();


    /**
     * 英雄天赋等级
     */
    private final Map<Integer, List<TalentTemplate>> talentLevel = Maps.newHashMap();

    /**
     * 天赋树
     */
    private final TreeMap<Integer, TalentNode> treeTalentMap = new TreeMap<>();


    @Override
    public void load() throws ResourceException {
        buildHeroLevelMap();
        buildHeroSkillLevelMap();
        for (HeroStarRhTemplate e : getResHolder().getListFromMap(HeroStarRhTemplate.class)) {
            heroStarMap.put(e.getStar(), e);
        }
        buildTalentLevelMap();
        buildTalentTreeMap();
    }

    private void buildHeroSkillLevelMap() {
        heroSkillLevelStore.clear();
        heroSkillLevelStore4Star.clear();
        Collection<HeroSkillLevelRhTemplate> list = getResHolder().getListFromMap(HeroSkillLevelRhTemplate.class);
        for (HeroSkillLevelRhTemplate e : list) {
            if (e.getLevel() > 0) {
                heroSkillLevelStore.put(e.getSkillId() + "_" + e.getLevel(), e);
            } else {
                heroSkillLevelStore4Star.put(e.getSkillId() + "_" + e.getRequireStar(), e);
            }

        }

    }

    private void buildHeroLevelMap() {
        heroLevelMap.clear();
        Collection<HeroLevelRhTemplate> list = getResHolder().getListFromMap(HeroLevelRhTemplate.class);
        for (HeroLevelRhTemplate heroLevelRhTemplate : list) {
            heroLevelMap.put(heroLevelRhTemplate.getHeroId() + "_" + heroLevelRhTemplate.getLevel(), heroLevelRhTemplate);
        }
    }


    public HeroLevelRhTemplate getHeroLevelTemplate(int heroId, int level) {

        return heroLevelMap.get(heroId + "_" + level);
    }

    public HeroSkillLevelRhTemplate getHeroSkillLevelTemplate(int skillId, int level) {
        return heroSkillLevelStore.get(skillId + "_" + level);
    }

    /**
     * @param skillId  技能组ID
     * @param heroStar 星数
     */
    public HeroSkillLevelRhTemplate getHeroSkillLevelTemplate4Star(int skillId, int heroStar) {
        return heroSkillLevelStore4Star.get(skillId + "_" + heroStar);
    }


    private void buildTalentLevelMap() {
        for (TalentTemplate talentTemplate : getResHolder().getListFromMap(TalentTemplate.class)) {
            List<TalentTemplate> talentTemplates = talentLevel.computeIfAbsent(talentTemplate.getGroup(), (key) -> new ArrayList<>());
            talentTemplates.add(talentTemplate);
        }

    }

    private void buildTalentTreeMap() throws ResourceException {
        for (TalentTemplate talentTemplate : getResHolder().getListFromMap(TalentTemplate.class)) {
            createTalentTree(talentTemplate);
        }
        for (TalentGroupTemplate talentGroupTemplate : getResHolder().getListFromMap(TalentGroupTemplate.class)) {
            createTalentTreeGroup(talentGroupTemplate);
        }
    }

    /**
     * 构建天赋依赖树
     */
    private void createTalentTree(TalentTemplate template) {
        TalentNode talentNode = treeTalentMap.computeIfAbsent(template.getId(), (key) -> new TalentNode(template));
        if (template.getNextlevel() > 0) {
            TalentTemplate nextTalentTemplate = getResHolder().getValueFromMap(TalentTemplate.class, template.getNextlevel());
            TalentNode nextTalentNode = treeTalentMap.computeIfAbsent(nextTalentTemplate.getId(), (key) -> new TalentNode(nextTalentTemplate));
            nextTalentNode.addPreNode(talentNode);
        }
    }

    /**
     * 构建天赋组依赖树
     */
    private void createTalentTreeGroup(TalentGroupTemplate talentGroupTemplate) throws ResourceException {
        for (IntPairType intPairType : talentGroupTemplate.getUnlockPairList()) {
            TalentTemplate talentTemplate = getTalentTemplateByLevel(talentGroupTemplate.getId(), 1);
            TalentNode talentNode = treeTalentMap.computeIfAbsent(talentTemplate.getId(), (key) -> new TalentNode(talentTemplate));

            TalentTemplate preTalentTemplate = getTalentTemplateByLevel(intPairType.getKey(), intPairType.getValue());
            TalentNode preTalentNode = treeTalentMap.computeIfAbsent(preTalentTemplate.getId(), (key) -> new TalentNode(preTalentTemplate));
            talentNode.addPreNode(preTalentNode);
        }
    }

    /**
     * 根据天赋组+等级获取天赋配置
     */
    public TalentTemplate getTalentTemplateByLevel(int groupId, int level) throws ResourceException {
        List<TalentTemplate> talentTemplates = talentLevel.get(groupId);
        if (talentTemplates == null || talentTemplates.isEmpty()) {
            throw new ResourceException(StringUtils.format("英雄天赋组关系配置错误. groupId:{} level:{} 不存在", groupId, level));
        }
        Optional<TalentTemplate> first = talentTemplates.stream().filter(it -> it.getLevel() == level).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        throw new ResourceException(StringUtils.format("英雄天赋组关系配置错误. groupId:{} level:{} 不存在", groupId, level));
    }


    public boolean hasStar(int star) {
        return heroStarMap.containsKey(star);
    }


    public HeroStarRhTemplate getHeroStarTemplate(int star) {
        return heroStarMap.get(star);
    }


    @Override
    public void checkValid() throws ResourceException {
        Collection<HeroRhTemplate> heroTemplateList = getResHolder().getListFromMap(HeroRhTemplate.class);
        for (HeroRhTemplate heroTemplate : heroTemplateList) {
            checkSkillId(heroTemplate.getSkill1Pair());
            checkSkillId(heroTemplate.getSkill2Pair());
            checkSkillId(heroTemplate.getSkill3Pair());
            checkSkillId(heroTemplate.getSkill4Pair());
        }
    }


    private void checkSkillId(IntPairType one) throws ResourceException {
        if (one == null) {
            return;
        }
        // 这里getValueFromMap会自己抛异常的
        SkillDataTemplateService skillDataTemplateService = getResHolder().innerGetResService(SkillDataTemplateService.class);
        SkillConfigTemplate v = skillDataTemplateService.getSkillTemplate(one.getKey(), one.getValue());
        if (v == null) {
            throw new ResourceException(StringUtils.format("英雄配置的技能id在技能表中没有配置：{},{}", one.getKey(), one.getValue()));
        }

    }

    /**
     * 天赋组树结构
     */
    public static class TalentNode {
        private final List<TalentNode> preNodeList = new ArrayList<>();
        private final TalentTemplate talentTemplate;

        public List<TalentNode> getPreNodeList() {
            return preNodeList;
        }

        public void addPreNode(TalentNode preNode) {
            preNodeList.add(preNode);
        }

        public TalentTemplate getTalentTemplate() {
            return talentTemplate;
        }

        public TalentNode(TalentTemplate talentTemplate) {
            this.talentTemplate = talentTemplate;
        }

        public boolean hasPreNode() {
            return !preNodeList.isEmpty();
        }
    }


    public static int getHeroChipItemIdByHeroId(int heroId) {
        HeroRhTemplate heroTemplate = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroId);
        return heroTemplate.getGetShardAmount();
    }

    public static int getHeroQualityByHeroId(int heroId) {
        HeroRhTemplate heroTemplate = ResHolder.getInstance().getValueFromMap(HeroRhTemplate.class, heroId);
        return heroTemplate.getRarity();
    }

}

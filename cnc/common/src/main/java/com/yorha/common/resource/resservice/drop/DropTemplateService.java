package com.yorha.common.resource.resservice.drop;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.gemini.utils.StringUtils;
import res.template.DropGroupTemplate;
import res.template.DropObjectTemplate;
import res.template.MailTemplate;
import res.template.SelecteRewardTemplate;

/**
 * <AUTHOR>
 */
public class DropTemplateService extends AbstractResService {

    public DropTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

    }

    @Override
    public void checkValid() throws ResourceException {
        for (DropGroupTemplate dropGroup : getResHolder().getListFromMap(DropGroupTemplate.class)) {
            for (IntTripleType tripleType : dropGroup.getDropListTripleList()) {

                DropObjectTemplate dropTemplate = getResHolder().checkValueFromMap(DropObjectTemplate.class, tripleType.getKey(),
                        () -> StringUtils.format("DropObjectTemplate 掉落id不存在. dropId:{}", tripleType.getKey()));
                if (dropTemplate == null) {
                    throw new ResourceException(StringUtils.format("掉落组DropObjectTemplate-【{}】配置了不存在的掉落物ID【{}】", dropGroup.getId(), tripleType.getKey()));
                }
                int mailId = dropTemplate.getMailId();
                if (mailId > 0) {
                    getResHolder().checkValueFromMap(MailTemplate.class, mailId,
                            () -> StringUtils.format("掉落物DropObjectTemplate-【{}】邮件id不存在mailId-【{}】", dropTemplate.getId(), dropTemplate.getMailId()));
                }
                switch (dropTemplate.getDropObjectType()) {
                    case ITEM: {
                        int reward = dropTemplate.getReward();
                        getResHolder().checkValueFromMap(SelecteRewardTemplate.class, reward,
                                () -> StringUtils.format("掉落物DropObjectTemplate-【{}】拾取道具奖励不存在SelectRewardTemplate-【{}】", dropTemplate.getId(), reward));
                        break;
                    }
                    default:
                        throw new ResourceException("DropTemplateService unsupport DropObjectType:{}", dropTemplate.getDropObjectType());
                }
            }
        }
    }

    public DropObjectTemplate getDropTemplate(int dropId) throws GeminiException {
        return getResHolder().getValueFromMap(DropObjectTemplate.class, dropId);
    }

    public DropGroupTemplate getDropGroupTemplate(int dropId) throws GeminiException {
        return getResHolder().getValueFromMap(DropGroupTemplate.class, dropId);
    }
}

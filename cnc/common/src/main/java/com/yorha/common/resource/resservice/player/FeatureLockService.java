package com.yorha.common.resource.resservice.player;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import res.template.FeatureUnlockTemplate;
import res.template.InitLockFeatureTemplate;

import java.util.*;

/**
 * 功能解锁配置中心
 *
 * <AUTHOR>
 */
public class FeatureLockService extends AbstractResService {
    public FeatureLockService(ResHolder resHolder) {
        super(resHolder);
    }

    private final Map<Integer, List<FeatureUnlockTemplate>> taskToTemplateMap = new HashMap<>();
    private final Map<Integer, List<FeatureUnlockTemplate>> buildToTemplateMap = new HashMap<>();

    @Override
    public void load() throws ResourceException {
        for (FeatureUnlockTemplate template : getResHolder().getListFromMap(FeatureUnlockTemplate.class)) {
            taskToTemplateMap.computeIfAbsent(template.getId(), key -> new ArrayList<>());
            taskToTemplateMap.get(template.getId()).add(template);
            for (IntPairType pair : template.getBuildlevelPairList()) {
                buildToTemplateMap.computeIfAbsent(pair.getKey(), key -> new ArrayList<>());
                buildToTemplateMap.get(pair.getKey()).add(template);
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        for (FeatureUnlockTemplate template : getResHolder().getListFromMap(FeatureUnlockTemplate.class)) {
            for (IntPairType pair : template.getBuildlevelPairList()) {
                if (CommonEnum.InnerCityBuildType.forNumber(pair.getKey()) != null) {
                    continue;
                }
                throw new ResourceException("G_功能解锁 中 id: {} 使用了非法的建筑id: {}", template.getId(), pair.getKey());
            }
            if (template.getUnLock() == null) {
                WechatLog.error("FeatureLockService checkValid G_功能解锁 FeatureUnlock 中用了非法模块id {} ", template.getId());
            }
        }
        for (InitLockFeatureTemplate template : getResHolder().getListFromMap(InitLockFeatureTemplate.class)) {
            if (template.getLockedFeature() == null) {
                WechatLog.error("FeatureLockService checkValid G_功能解锁 InitLockFeature 中用了非法模块id {}", template.getId());
            }
        }
    }

    public List<FeatureUnlockTemplate> getTaskToTemplateMap(int task) {
        return taskToTemplateMap.getOrDefault(task, Collections.emptyList());
    }

    public List<FeatureUnlockTemplate> getBuildToTemplateMap(int task) {
        return buildToTemplateMap.getOrDefault(task, Collections.emptyList());
    }
}

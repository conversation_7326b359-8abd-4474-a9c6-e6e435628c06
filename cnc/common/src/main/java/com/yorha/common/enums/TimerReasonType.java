package com.yorha.common.enums;

public enum TimerReasonType {
    ACTOR_RECEIVE_TIMEOUT,
    INIT_REFRESH_GU<PERSON><PERSON><PERSON>,
    BIG_SCENE_TIME_SPAWN_MONSTER,
    ACTOR_PERF_STAT,
    NODE_PERF_STAT,
    TOPOLOGY_EVENT_REFRESH,
    BY<PERSON>ANDER_CHECK_TIMER,
    UPDATE_CLAN_CARD_SCHEDULE,
    RANK_UPDATE_TIMER,
    UPDATE_POWER_TO_OTHER,
    UPDATE_PLAYER_CARD_SCHEDULE,
    BUFF_EXPIRE_TIMER,
    REFRESH_HIGH_POWER_ZONE,
    ACTIVITY_RELOAD,
    SCHEDULE_SYSTEM_MESSAGE,
    ZONE_SIDE_DAY_REFRESH,
    ZONE_SIDE_WEEK_REFRESH,
    TEXT_FILTER_CLEAR_CONNECTION,
    TEXT_FILTER_CONNECTION_LOG,
    REMOVE_NOTIFICATION,
    RELEASE_TRANSPORT_PLANE,
    SIM<PERSON>LATE_SPY_FLY,
    PLAYER_CLAN_RECOMMEND,
    PLAYER_CLAN_RECOMMEND_2,
    REFRESH_DISCOUNT_STORE,
    CLOSE_PASSWORD,
    CLOSE_PASSWORD_2,
    POLL_CHECK_FINISH_QUEUE,
    BATTLE_PASS_LOAD_NEXT,
    BATTLE_PASS_OVER,
    BATTLE_PASS_OVER_2,
    BATTLE_PASS_NEXT_MISSION_GROUP,
    DELETE_MAIL,
    OPEN_FOG_BY_FOG_GRID_IDS,
    FOG_MILESTONE,
    FILL_BATTLE_NAIL_GM,
    FILL_SPY_MAIL_GM,
    FILL_COLLECTION_MAIL_GM,
    FILL_EXPLORE_MAIL_GM,
    PROSPERITY_REFRESH_STAGE,
    PLAYER_ACTIVITY,
    PLAYER_ACTIVITY_RELOAD,
    PLAYER_DAY_REFRESH,
    PLAYER_WEEK_REFRESH,
    RUNE_DELETE,
    EVA_EFFECT_END,
    EVA_EFFECT_END2,
    BUNDLE_EXPIRE,
    FRIEND_CLEAR_EXPIRE_APPLY,
    FRIEND_CLEAR_EXPIRE_APPLY_2,
    FRIEND_CLEAR_EXPIRE_WAIT,
    FRIEND_CLEAR_EXPIRE_WAIT_2,
    BEST_COMMANDER_TOTAL_RANK_UPDATE_SCORE,
    PLAYER_HISTORY_HIGHEST_POWER_UPDATE,
    PLAYER_SCORE_RANK_UPDATE,
    PLAYER_ZLCB_RANK_UPDATE,
    MIDAS_CLEAR_CONNECTION,
    MIDAS_CONNECTION_LOG,
    MARQUEE_SCHEDULE_REPEAT,
    SHOW_TCP_TRAFFIC,
    KICK_OFF_SESSION,
    CLAN_DISSOLVE_OR_TRANSFER_OWNER,
    CLAN_STAFF_AUTO_TRANSFER_OWNER,
    CLAN_RANK_COMBAT_CHANGE_UPDATE,
    CLAN_KILL_CHANGE_UPDATE,
    CLAN_TECH_RESEARCH_FINISH,
    CLAN_DAY_REFRESH,
    CLAN_WEEK_REFRESH,
    CLAN_INVITE_EXPIRE,
    SCENE_TICK,
    CLOSE_LOG_SWITCH,
    SCENE_EVA_SKILL,
    BATTLE_LOSE,
    CITY_BURN_STATE,
    CITY_STATE_TASK,
    ARMY_LIFT_OFF,
    ARMY_AIR_BORNE,
    ARMY_AIR_ARRIVE,
    RES_BUILDING_GLOBAL_REFRESH,
    GOLD_GLOBAL_REFRESH,
    BIG_SCENE_DUMP,
    QLOG_GAME_SERVER_STATE,
    QLOG_CNC_ONLINE_COUNT,
    QLOG_PLAYER_SNAP_SHOT,
    QLOG_CLAN_SNAP_SHOT,
    QLOG_MONSTER_COUNT,
    KINGDOM_GAIN_TAX_SKILL_END,
    KINGDOM_GIFT_REFRESH,
    MILESTONE_SWITCH,
    MILESTONE_DAEMON_PROCESS,
    CITY_ASCEND_CHECK,
    RALLY_PREPARE,
    UPDATE_ZONE_GUIDE_RECORD_TABLE,
    ZONE_DAY_REFRESH,
    ZONE_EXPIRE_ACTIVITY_EFFECT,
    ZONE_SEASON_TIMER,
    RES_BUILDING_COLLECT_FINISH,
    NEXT_EVENT_TIMER_FINISH,
    NEXT_EVENT_TIMER_ONE_ARMY_FINISH,
    CLAN_RES_BUILDING_FINISH,
    CLAN_RES_BUILDING_FINISH_2,
    STAGE_NODE_TIMER,
    CABEL_MONSTER_GRAVE_RECYCLE,
    ARENA_SETTLE,
    CABLE_CAMP_ACTIVE,
    CABLE_MONSTER_REFRESH,
    PLANE_TAKE_OFF,
    PLANE_LANDING,
    EXPLORE_FINISH,
    ANIMATION_LANDING,
    ANIMATION_TAKE_OFF,
    DUNGEON_PREPARE,
    DUNGEON_RUNNING,
    DUNGEON_ENDING,
    DUNGEON_DESTROY,
    DUNGEON_DELAY_SETTLE,
    CREATE_TIMER_ACTION,
    GUARD_CITY_TASK,
    SUCCESS_TIME_LIMIT_TASK,
    AUTH_CLEAR_CONNECTION,
    AUTH_CONNECTION_LOG,
    CLAN_RELOAD_RES_TASK,
    OPEN_GLOBAL_PEACE_SHIELD_TASK,
    FIREBASE_DELAY_PUSH_TASK,
    SKYNET_EXPLORE_TASK,
    SKYNET_NORMAL_POOL,
    SKYNET_GUARD_POOL,
    SKYNET_NORMAL_EXPIRE,
    IDIP_ZONE_CHAT,
    CLAN_ACTOR_DESTROY,
    TIME_COMPETITION,
    GM_GIVE_CLAN_MEMBER_POWER,
    ACTIVITY_ESCORT,
    ESCORT_SUMMON_MONSTER,
    UPDATE_DEF_TROOP_TO_ARENA,
    UPDATE_ATK_TROOP,
    RANK_ARROW_UPDATE,
    CLAN_BATTLE_HORN_RANK_UPDATE,
    CLAN_KING_ACT_RANK_UPDATE,
    PLAYER_ITEM_EXPIRE,
    DAILY_PLAYER_NUM_LOG,
    AUT_CLAN_KING_BROADCAST,
    DB_TASK_PROXY_UPDATE_LIMIT,
    DANGEROUS_PATCH,
    ANTI_ADDICTION_TASK,
    ANTI_ADDICTION_FORCE_LOGOUT,
    CLAN_SCORE_RANK_UPDATE,
    NEXT_DEFEND_CAMPAIGN,
}

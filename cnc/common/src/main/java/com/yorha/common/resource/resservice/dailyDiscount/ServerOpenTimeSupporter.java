package com.yorha.common.resource.resservice.dailyDiscount;

import com.yorha.common.exception.ResourceException;
import res.template.DailyPackHeroTemplate;

/**
 * 开服时间OpenTime
 */
class ServerOpenTimeSupporter implements OpenTimeSupporter {

    @Override
    public void checkOpenTime(DailyPackHeroTemplate dailyPackHeroTemplate) throws ResourceException {
        if (dailyPackHeroTemplate.getOpenTime() < 1) {
            throw new ResourceException("每日特惠表 daily_pack_hero id={} 上架天数错误:{}", dailyPackHeroTemplate.getId(), dailyPackHeroTemplate.getOpenTime());
        }
    }

    @Override
    public int getOpenTime(DailyPackHeroTemplate dailyPackHeroTemplate) {
        return dailyPackHeroTemplate.getOpenTime();
    }

}
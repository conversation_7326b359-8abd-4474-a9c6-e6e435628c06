package com.yorha.common.resource.mapdata;

import com.yorha.common.resource.annotation.ResAttribute;


public class FogBuildingTemplate extends MapSubdivisionTemplate {
    /**
     * ID
     * int id
     */
    @ResAttribute("id")
    private int id;

    /**
     * 所属州
     * int regionId
     */
    @ResAttribute("regionId")
    private int regionId;

    /**
     * 建筑配置id
     * int buildingId
     */
    @ResAttribute("buildingId")
    private int buildingId;

    /**
     * 建筑X坐标
     * int posX
     */
    @ResAttribute("posX")
    private int posX;

    /**
     * 建筑Y坐标
     * int posY
     */
    @ResAttribute("posY")
    private int posY;


    /**
     * ID
     * int id
     */
    public int getId() {
        return id;
    }

    /**
     * 所属州
     * int regionId
     */
    public int getRegionId() {
        return regionId;
    }

    /**
     * 建筑配置id
     * int buildingId
     */
    public int getBuildingId() {
        return buildingId;
    }

    /**
     * 建筑X坐标
     * int posX
     */
    public int getPosX() {
        return posX;
    }

    /**
     * 建筑Y坐标
     * int posY
     */
    public int getPosY() {
        return posY;
    }


}
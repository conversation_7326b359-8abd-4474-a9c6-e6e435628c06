package com.yorha.common.db.tcaplus.option;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BatchGetOption {
    private List<String> fieldNames;
    /**
     * 获得所有的字段
     * 默认：true
     */
    private boolean getAllFields = true;
    /**
     * "数据过期才取回"标志位。在发起读操作之前，用户代码通过 SetExpireTime() 设定数据过期时间，
     * 并将此标志置位，那么存储端若检测到记录在指定时间内发生过更新，则将数据返回，
     * 否则不返回实际数据，只是返回 TcapErrCode::COMMON_INFO_DATA_NOT_MODIFIED 的错误码。
     * 在请求中设置了此标志位之后，收到响应后应首先通过 TcaplusServiceResponse::GetFlags() 来获知
     * 发送请求时是否设置了 TCAPLUS_FLAG_FETCH_ONLY_IF_EXPIRED 标志.
     * 只有如下请求支持设置此标志：
     * TCAPLUS_API_BATCH_GET_REQ
     */
    private boolean fetchOnlyIfExpired = false;

    private int expireSeconds = 0;

    private int timeoutTimeMs = 0;

    public boolean isFetchOnlyIfExpired() {
        return fetchOnlyIfExpired;
    }

    public int getExpireSeconds() {
        return expireSeconds;
    }

    public int getTimeoutTimeMs() {
        return timeoutTimeMs;
    }

    public List<String> getFieldNames() {
        return fieldNames;
    }

    public boolean isGetAllFields() {
        return getAllFields;
    }

    private BatchGetOption() {

    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static final class Builder {
        private List<String> fieldNames;
        private boolean getAllFields = true;
        private boolean fetchOnlyIfExpired = false;
        private int expireSeconds = 0;
        private int timeoutTimeMs = 0;

        private Builder() {
        }

        public Builder withFieldNames(List<String> fieldNumbers) {
            this.fieldNames = fieldNumbers;
            return this;
        }

        public Builder withFieldNames(String... fieldNumbers) {
            this.fieldNames = Arrays.asList(fieldNumbers);
            return this;
        }

        public Builder withGetAllFields(boolean getAllFields) {
            this.getAllFields = getAllFields;
            return this;
        }

        public Builder withFetchOnlyIfExpired(boolean fetchOnlyIfExpired) {
            this.fetchOnlyIfExpired = fetchOnlyIfExpired;
            return this;
        }

        public Builder withExpireSeconds(int expireSeconds) {
            this.expireSeconds = expireSeconds;
            return this;
        }

        public Builder withTimeoutTimeMs(int timeoutTimeMs) {
            this.timeoutTimeMs = timeoutTimeMs;
            return this;
        }

        public BatchGetOption build() {
            BatchGetOption batchGetOption = new BatchGetOption();
            batchGetOption.fieldNames = this.fieldNames;
            batchGetOption.expireSeconds = this.expireSeconds;
            batchGetOption.getAllFields = this.getAllFields;
            batchGetOption.fetchOnlyIfExpired = this.fetchOnlyIfExpired;
            batchGetOption.timeoutTimeMs = this.timeoutTimeMs;
            return batchGetOption;
        }
    }
}

package com.yorha.common.utils.shape;

import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.vector.Vector2f;

/**
 * <AUTHOR>
 */
public class Circle implements Shape {
    private final int x;
    private final int y;
    private final int r;
    private AABB aabb;

    private Circle(int x, int y, int r) {
        this.x = x;
        this.y = y;
        this.r = r;
    }

    public static Circle valueOf(Point point, int r) {
        return new Circle(point.getX(), point.getY(), r);
    }

    public static Circle valueOf(int x, int y, int r) {
        return new Circle(x, y, r);
    }

    public Circle newCircleWithNewRadius(int newRadius) {
        return Circle.valueOf(x, y, newRadius);
    }

    @Override
    public boolean containsPoint(long x, long y) {
        long distance2 = (x - this.getX()) * (x - this.getX()) + (y - this.y) * (y - this.y);
        return distance2 <= (long) r * r;
    }

    @Override
    public AABB getAABB() {
        if (aabb == null) {
            aabb = new AABB(x - r, y - r, x + r, y + r);
        }
        return aabb;
    }

    @Override
    public Point getRandomPoint() {
        int angle = RandomUtils.nextInt(360);
        double sin = Math.sin(angle);
        double cos = Math.cos(angle);
        int newR = RandomUtils.nextInt(r + 1);
        return Point.valueOf((int) (x + newR * sin), (int) (y + newR * cos));
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public int getR() {
        return r;
    }

    private Point centerPoint = null;

    public Point getCircleCenter() {
        if (centerPoint != null) {
            return centerPoint;
        }
        centerPoint = Point.valueOf(x, y);
        return centerPoint;
    }

    /**
     * 判断点是否在圆内
     */
    public boolean isPointInsideSelf(Point targetPoint) {
        return containsPoint(targetPoint.getX(), targetPoint.getY());
    }

    /**
     * 通过向量 获取 圆边上的点
     */
    public Point getPointFromVector(Vector2f vector) {
        Vector2f unitV = Vector2f.unitization(vector);
        return Point.valueOf((int) (x + unitV.getX() * r), (int) (y + unitV.getY() * r));
    }

    /**
     * 是否完全包含另一个圆
     */
    public boolean containsCircle(Circle circle) {
        int diffX = x - circle.getX();
        int diffY = y - circle.getY();
        int diffRadius = r - circle.getR();
        return diffX * diffX + diffY * diffY < diffRadius * diffRadius;
    }

    @Override
    public String toString() {
        return "Circle{" +
                "x=" + x +
                ", y=" + y +
                ", r=" + r +
                '}';
    }
}
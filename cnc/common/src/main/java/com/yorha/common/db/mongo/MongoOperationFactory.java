package com.yorha.common.db.mongo;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.connection.ConnectionPoolSettings;
import com.mongodb.connection.NettyTransportSettings;
import com.mongodb.connection.TransportSettings;
import com.mongodb.reactivestreams.client.MongoClient;
import com.mongodb.reactivestreams.client.MongoClients;
import com.mongodb.reactivestreams.client.MongoCollection;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.IDbOperation;
import com.yorha.common.db.IDbOperationFactory;
import com.yorha.common.db.mongo.listener.MongoCommandListener;
import com.yorha.common.db.mongo.listener.MongoConnectionPoolListener;
import com.yorha.common.db.mongo.op.*;
import com.yorha.common.db.tcaplus.option.*;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.proto.TcaplusDb;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.SignalType;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * mongo操作工厂
 *
 * <AUTHOR> Jiang
 * <AUTHOR>
 */
public class MongoOperationFactory implements IDbOperationFactory, AutoCloseable {
    private static final Logger LOGGER = LogManager.getLogger(MongoOperationFactory.class);

    private final MongoDatabase database;
    private final MongoClient client;
    private final EventLoopGroup group;

    public MongoOperationFactory(MongoClientConfig config) {
        LOGGER.info("MongoDB user:{} database:{} password:{}, addressList:{} connect start", config.getUser(), config.getDatabase(), config.getPassword(), config.getAddressList());
        int ioThreads = Math.min(16, Runtime.getRuntime().availableProcessors() * 2);
        group = new NioEventLoopGroup(ioThreads, new ThreadFactoryBuilder().setNameFormat("mongo-io-%d").setDaemon(true).build());

        NettyTransportSettings nettyTransport = TransportSettings.nettyBuilder().eventLoopGroup(group).build();
        String connectionString = config.buildConnectionString();
        LOGGER.info("MongoDB connectionString:{}", connectionString);
        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(connectionString))
                .transportSettings(nettyTransport)
                .applyToConnectionPoolSettings(builder ->
                        // 最大连接数
                        builder.maxSize(config.getMaxPoolSize())
                                // 最小连接数
                                .minSize(config.getMinPoolSize())
                                .maxConnecting(config.getMaxConnecting())
                                .maxConnectionIdleTime(config.getMaxIdleTimeSec(), TimeUnit.SECONDS)
                                .maxWaitTime(config.getMaxWaitTimeMs(), TimeUnit.MILLISECONDS)
                                .addConnectionPoolListener(new MongoConnectionPoolListener())
                )
                // 连接超时时间
                .applyToSocketSettings(builder -> builder.connectTimeout(config.getConnectTimeoutMs(), TimeUnit.MILLISECONDS)
                        .readTimeout(config.getReadTimeoutMs(), TimeUnit.MILLISECONDS))
                .addCommandListener(new MongoCommandListener())
                .build();
        ConnectionPoolSettings connectionPoolSettings = settings.getConnectionPoolSettings();
        LOGGER.debug("MongoDB connection pool :{}", connectionPoolSettings);

        this.client = MongoClients.create(settings);
        //this.client = MongoClients.create(connectionString);
        this.database = this.client.getDatabase(config.getDatabase());


        // 打印 MongoDB 版本
        // 2. 执行buildInfo命令获取版本
        Mono<Document> versionMono = Mono.from(client.getDatabase("admin")
                .runCommand(new Document("buildInfo", 1)));

        // 3. 提取并打印版本号
        versionMono.map(doc -> doc.getString("version"))
                .doOnNext(version ->
                        LOGGER.info("MongoDB user:{} database:{} password:{}, addressList:{} version:{},connect end",
                                config.getUser(), config.getDatabase(), config.getPassword(), config.getAddressList(), version))
                // 使用日志框架
                .doOnError(e ->
                        LOGGER.error("MongoDB get version failed | addressList:{} | cause:{}",
                                config.getAddressList(),
                                e.getMessage(),
                                e) // 打印完整堆栈
                )
                .doFinally(signal -> {
                    if (signal == SignalType.ON_ERROR) {
                        LOGGER.warn("MongoDB connect failed");
                    }
                })
                .subscribe(); // 非阻塞订阅


    }


    @Override
    public <T extends Message.Builder> IDbOperation<T, GetByPartKeyOption, GetByPartKeyResult<T>> getByPartKeyOperation(T dbData, GetByPartKeyOption option) {
        return new MongoGetByPartKey<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, UpdateOption, UpdateResult<T>> updateOperation(T dbData, UpdateOption option) {
        return new MongoUpdate<>(database, dbData, option);
    }


    @Override
    public void checkTable() {
        //为了不阻塞数据库写入操作，影响性能
        IndexOptions options = new IndexOptions().unique(true).background(true);

        List<Descriptors.Descriptor> descriptors = TcaplusDb.getDescriptor().getMessageTypes();
        List<Mono<Void>> taskMonos = new ArrayList<>();

        for (Descriptors.Descriptor descriptor : descriptors) {
            List<String> uniqueKeys = new ArrayList<>();

            // 提取主键字段
            for (Map.Entry<Descriptors.FieldDescriptor, Object> entry : descriptor.getOptions().getAllFields().entrySet()) {
                if ("tcaplus_primarykey".equals(entry.getKey().getName())) {
                    String indexStr = entry.getValue().toString();
                    uniqueKeys.addAll(Arrays.asList(indexStr.split(",")));
                }
            }

            if (uniqueKeys.isEmpty()) {
                continue;
            }

            String collectionName = descriptor.getName();
            MongoCollection<Document> collection = this.database.getCollection(collectionName);
            // 异步获取现有索引列表
            Mono<Void> task = Flux.from(collection.listIndexes())
                    .collectList()
                    .flatMap(existingIndexes -> {
                        String indexName;
                        Bson indexKeys;

                        if (uniqueKeys.size() == 1) {
                            String field = uniqueKeys.getFirst();
                            indexName = field + "_1";
                            indexKeys = new Document(field, 1);
                        } else {
                            List<Bson> fields = new ArrayList<>();
                            StringBuilder nameBuilder = new StringBuilder();
                            for (String key : uniqueKeys) {
                                fields.add(Indexes.ascending(key));
                                nameBuilder.append(key).append("_1_");
                            }
                            indexName = nameBuilder.substring(0, nameBuilder.length() - 1); // 去掉末尾下划线
                            indexKeys = Indexes.compoundIndex(fields);
                        }

                        boolean exists = existingIndexes.stream()
                                .anyMatch(doc -> indexName.equals(doc.getString("name")));

                        if (!exists) {
                            return Mono.from(collection.createIndex(indexKeys, options))
                                    .retry(3)
                                    .map(ignore -> indexName);
                        } else {
                            LOGGER.info("Index {} already exists, skip creation", indexName);
                            return Mono.empty();
                        }
                    })
                    .doOnSuccess(response -> {
                        if (response != null) {
                            LOGGER.info("createIndex ok: {}", response);
                        }
                    })
                    .doOnError(throwable -> LOGGER.error("createIndex fail, throwable=", throwable))
                    .then(); // 转换为 Mono<Void>

            taskMonos.add(task);
        }

        if (!taskMonos.isEmpty()) {
            Mono.when(taskMonos)
                    .doOnSuccess(v -> LOGGER.info("All indexes created successfully"))
                    .doOnError(e -> LOGGER.error("Error during checkTable", e))
                    .subscribe();
        } else {
            LOGGER.info("No indexes need to be created.");
        }
    }


    @Override
    public <T extends Message.Builder> IDbOperation<T, InsertOption, InsertResult<T>> insertOperation(T dbData, InsertOption option) {
        // return new MongoInsert<>(database, dbData, option);
        return new MongoInsert<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, GetOption, GetResult<T>> getOperation(T dbData, GetOption option) {
        return new MongoGet<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, UpsertOption, UpsertResult<T>> upsertOperation(T dbData, UpsertOption option) {
        return new MongoUpsert<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, IncreaseOption, IncreaseResult<T>> increaseOperation(T dbData, IncreaseOption option) {
        return new MongoIncrease<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, DeleteOption, DeleteResult> deleteOperation(T dbData, DeleteOption option) {
        return new MongoDelete<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, DeleteByPartKeyOption, DeleteByPartKeyResult> deleteByPartKeyOperation(T dbData, DeleteByPartKeyOption option) {
        return new MongoDeleteByPartKey<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, GetMetaOption, GetMetaResult> getMetaOperation(T dbData, GetMetaOption option) {
        //josefren: Mongo不校验
        return null;
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, TraverseOption<T>, TraverseResult> traverseOperation(T dbData, TraverseOption<T> option) {
        return new MongoTraverse<>(database, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<Collection<T>, BatchGetOption, BatchGetResult<T>> batchGetOperation(Collection<T> dbData, BatchGetOption option) {
        return new MongoBatchGet<>(database, dbData, option);
    }


    @Override
    public void close() {
        LOGGER.info("Starting MongoDB resources shutdown...");
        if (client != null) {
            try {
                client.close();
                LOGGER.info("MongoDB client closed successfully");
            } catch (Exception e) {
                LOGGER.error("Error closing MongoDB client", e);
            }
        }

        if (group != null) {
            try {
                group.shutdownGracefully();
                if (!group.awaitTermination(5, TimeUnit.SECONDS)) {
                    LOGGER.warn("Netty EventLoopGroup did not terminate within timeout.");
                }
                LOGGER.info("Netty event loop group closed successfully");
            } catch (InterruptedException e) {
                LOGGER.error("Interrupted while waiting for EventLoopGroup to terminate", e);
                Thread.currentThread().interrupt();
            }
        }
    }

}

package com.yorha.common.actor.cluster.node;

import com.yorha.common.actor.NodeService;
import com.yorha.common.helper.BroadcastHelper;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResLoader;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.OffsetUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.PlayerCommon;
import com.yorha.proto.SsNode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class NodeServiceImpl implements NodeService {
    private static final Logger LOGGER = LogManager.getLogger(NodeServiceImpl.class);

    private final NodeActor actor;

    public NodeServiceImpl(NodeActor actor) {
        this.actor = actor;
    }

    /**
     * @return Owner Actor。
     */
    private NodeActor ownerActor() {
        return this.actor;
    }

    @Override
    public void handleNodeTaskCmd(SsNode.NodeTaskCmd ask) {
        switch (ask.getNodeTaskType()) {
            case NTT_STRING_MSG:
                this.handleBroadcastString(ask.getStringMsg());
                break;
            default:
                throw new RuntimeException("not right protocol {}" + ask);
        }
    }

    /**
     * 集群消息：自定义string
     */
    public void handleBroadcastString(String msg) {
        LOGGER.info("handleBroadcastString {} {}", msg, ownerActor());
        if (msg.startsWith("offset:")) {
            // 调整时间偏移量
            Pair<Long, Integer> offsetFromDb = OffsetUtils.getOffsetFromDb(actor);
            Long newOffset = offsetFromDb.getFirst();
            LOGGER.warn("try update offset {} {}", msg, offsetFromDb);
            SystemClock.updateOffset(newOffset, false);

            if (ServerContext.isZoneServer()) {
                PlayerCommon.Player_SystemTime_NTF ntf = PlayerCommon.Player_SystemTime_NTF.newBuilder()
                        .setCurrentTimeMs(SystemClock.nowNative() + newOffset)
                        .build();
                BroadcastHelper.toCsOnlinePlayerInZone(ServerContext.getZoneId(), MsgType.PLAYER_SYSTEMTIMENTF_NTF, ntf);
            }
        } else if (msg.startsWith("reload;")) {
            // xml热更
            ResLoader.reload();
        }

    }

}

package com.yorha.common.exports;

import io.prometheus.client.Collector;
import io.prometheus.client.CounterMetricFamily;
import io.prometheus.client.GaugeMetricFamily;
import io.prometheus.client.Predicate;

import java.lang.management.ClassLoadingMXBean;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static io.prometheus.client.SampleNameFilter.ALLOW_ALL;

/**
 * 从io.prometheus.client.hotspot.ClassLoadingExports改造而来。
 *
 * <AUTHOR>
 */
public class GeminiClassLoadingExports extends Collector {

    private static final String JVM_CLASSES_CURRENTLY_LOADED = "jvm_classes_currently_loaded";
    private static final String JVM_CLASSES_LOADED_TOTAL = "jvm_classes_loaded_total";
    private static final String JVM_CLASSES_UNLOADED_TOTAL = "jvm_classes_unloaded_total";

    private final ClassLoadingMXBean clBean;
    private final String busId;

    public GeminiClassLoadingExports(final String busId) {
        this(busId, ManagementFactory.getClassLoadingMXBean());
    }

    public GeminiClassLoadingExports(final String busId, ClassLoadingMXBean clBean) {
        this.busId = busId;
        this.clBean = clBean;
    }

    void addClassLoadingMetrics(List<MetricFamilySamples> sampleFamilies, Predicate<String> nameFilter) {
        if (nameFilter.test(JVM_CLASSES_CURRENTLY_LOADED)) {
            final GaugeMetricFamily gaugeMetricFamily = new GaugeMetricFamily(
                    JVM_CLASSES_CURRENTLY_LOADED,
                    "The number of classes that are currently loaded in the JVM",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            gaugeMetricFamily.addMetric(Collections.singletonList(this.busId), clBean.getLoadedClassCount());
            sampleFamilies.add(gaugeMetricFamily);
        }
        if (nameFilter.test(JVM_CLASSES_LOADED_TOTAL)) {
            final CounterMetricFamily counterMetricFamily = new CounterMetricFamily(
                    JVM_CLASSES_LOADED_TOTAL,
                    "The total number of classes that have been loaded since the JVM has started execution",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            counterMetricFamily.addMetric(Collections.singletonList(this.busId), clBean.getTotalLoadedClassCount());
            sampleFamilies.add(counterMetricFamily);
        }
        if (nameFilter.test(JVM_CLASSES_UNLOADED_TOTAL)) {
            final CounterMetricFamily counterMetricFamily = new CounterMetricFamily(
                    JVM_CLASSES_UNLOADED_TOTAL,
                    "The total number of classes that have been unloaded since the JVM has started execution",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID));
            counterMetricFamily.addMetric(Collections.singletonList(this.busId), clBean.getUnloadedClassCount());
            sampleFamilies.add(counterMetricFamily);
        }
    }

    @Override
    public List<MetricFamilySamples> collect() {
        return collect(null);
    }

    @Override
    public List<MetricFamilySamples> collect(Predicate<String> nameFilter) {
        List<MetricFamilySamples> mfs = new ArrayList<MetricFamilySamples>();
        addClassLoadingMetrics(mfs, nameFilter == null ? ALLOW_ALL : nameFilter);
        return mfs;
    }
}
package com.yorha.common.utils.shape;


import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.vector.Vector2f;
import org.apache.commons.lang3.RandomUtils;

/**
 * 矩形
 *
 * <AUTHOR>
 */
public class Rectangle implements Shape {
    /**
     * 长
     */
    private final float width;

    /**
     * 高
     */
    private final float height;

    /**
     * 中心点
     */
    private final Point center;

    /**
     * 长的方向向量 长度为width/2
     */
    private final Vector2f yawWidth;
    /**
     * 高的方向向量 长度为height/2
     */
    private final Vector2f yawHeight;

    private final Point pointA;
    private final Point pointB;
    private final Point pointC;
    private final Point pointD;

    private AABB aabb = null;

    private Rectangle(Point center, Vector2f yaw, float width, float height) {
        this.width = width;
        this.height = height;
        this.center = center;
        this.yawWidth = yaw.resizeLength(width / 2);
        this.yawHeight = yaw.rotate(90).resizeLength(height / 2);
        pointA = Point.valueOf(center.getX() + yawWidth.getX() + yawHeight.getX(), center.getY() + yawWidth.getY() + yawHeight.getY());
        pointB = Point.valueOf(center.getX() + yawWidth.getX() - yawHeight.getX(), center.getY() + yawWidth.getY() - yawHeight.getY());
        pointC = Point.valueOf(center.getX() - yawWidth.getX() + yawHeight.getX(), center.getY() - yawWidth.getY() + yawHeight.getY());
        pointD = Point.valueOf(center.getX() - yawWidth.getX() - yawHeight.getX(), center.getY() - yawWidth.getY() - yawHeight.getY());
    }

    /**
     * 中心点、长、宽
     */
    public static Rectangle valueOf(Point center, float width, float height) {
        Vector2f yaw = Vector2f.valueOf(1.0f, 0.0f);
        return new Rectangle(center, yaw, width, height);
    }

    /**
     * 射线起点、长的方向， 长、宽
     */
    public static Rectangle valueOf(Point one, Vector2f yaw, int width, int height) {
        // 计算中心点
        Vector2f vector2f = yaw.resizeLength(width / 2.0f);
        Point center = Point.valueOf(one.getX() + vector2f.getX(), one.getY() + vector2f.getY());
        return new Rectangle(center, yaw, width, height);
    }

    @Override
    public Point getRandomPoint() {
        Vector2f vecWidth = yawWidth.resizeLength(width);
        Vector2f vecHeight = vecWidth.rotate(90).resizeLength(height);
        double a = RandomUtils.nextDouble(1, 99) - 50;
        double b = RandomUtils.nextDouble(1, 99) - 50;
        int x = (int) (center.getX() + (a * vecWidth.getX() + b * vecHeight.getX()) / 100);
        int y = (int) (center.getY() + (a * vecWidth.getY() + b * vecHeight.getY()) / 100);
        return Point.valueOf(x, y);
    }


    /**
     * 是否包含指定的点
     */
    @Override
    public boolean containsPoint(long x, long y) {
        return isInner(x, y);
    }

    /**
     * 根据点到矩阵四角切割的四个三角形面积和与矩阵面积做比较
     * 判断点是否在矩阵内
     */
    private boolean isInner(long x, long y) {
        Vector2f vec = Vector2f.valueOf(x - center.getX(), y - center.getY());
        float a = Vector2f.dotProduct(vec, yawWidth) / (width / 2);
        if (Math.abs(a) > width / 2) {
            return false;
        }
        float b = Vector2f.dotProduct(vec, yawHeight) / (height / 2);
        return !(Math.abs(b) > height / 2);
    }

    /**
     * 获取aabb包围盒
     */
    @Override
    public AABB getAABB() {
        if (aabb == null) {
            int left = MathUtils.min(pointA.getX(), pointC.getX(), pointB.getX(), pointD.getX());
            int right = MathUtils.max(pointA.getX(), pointC.getX(), pointB.getX(), pointD.getX());
            int bottom = MathUtils.max(pointA.getY(), pointC.getY(), pointB.getY(), pointD.getY());
            int top = MathUtils.min(pointA.getY(), pointC.getY(), pointB.getY(), pointD.getY());
            aabb = new AABB(left, top, right, bottom);
        }
        return aabb;
    }

    public Point getPointA() {
        return pointA;
    }

    public Point getPointB() {
        return pointB;
    }

    public Point getPointC() {
        return pointC;
    }

    public Point getPointD() {
        return pointD;
    }

    public float getWidth() {
        return width;
    }

    public float getHeight() {
        return height;
    }

    public Point getCenter() {
        return center;
    }

    @Override
    public String toString() {
        return "Rectangle{" + pointA.toString() + pointB.toString() + pointC.toString() + pointD.toString() + "} width:" + width + " height:" + height + " center:" + center;
    }
}

package com.yorha.common.actor.cluster.toplogy;

/**
 * <AUTHOR>
 */
public class ClusterEvent {
    private final String busId;
    private final String value;
    private final long startTsMs;

    public ClusterEvent(String busId, String value, long startTsMs) {
        this.busId = busId;
        this.value = value;
        this.startTsMs = startTsMs;
    }

    public long getStartTsMs() {
        return startTsMs;
    }

    public String getBusId() {
        return busId;
    }

    public String getValue() {
        return value;
    }

    public boolean isDelete() {
        return this.value == null;
    }

    @Override
    public String toString() {
        return "ClusterEvent{" +
                "busId='" + busId + '\'' +
                ", value='" + value + '\'' +
                ", startTsMs=" + startTsMs +
                '}';
    }
}

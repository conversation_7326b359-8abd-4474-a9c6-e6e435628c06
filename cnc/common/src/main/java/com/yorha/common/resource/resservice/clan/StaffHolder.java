package com.yorha.common.resource.resservice.clan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Collections;
import java.util.List;

/**
 * 官职信息
 *
 * <AUTHOR>
 */
public class StaffHolder {
    /**
     * set of privilege roles
     */
    private final int role;
    /**
     * array of staff buffs
     */
    private final List<Integer> buffs;
    /**
     * 职位的最大数量
     */
    private final int maxCnt;

    public StaffHolder(final int role, final int maxCnt, final List<Integer> buffs) {
        this.role = role;
        this.maxCnt = maxCnt;
        this.buffs = Collections.unmodifiableList(buffs);
    }

    public int getRole() {
        return this.role;
    }

    public int getMaxCnt() {
        return this.maxCnt;
    }

    public List<Integer> getBuffs() {
        return this.buffs;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

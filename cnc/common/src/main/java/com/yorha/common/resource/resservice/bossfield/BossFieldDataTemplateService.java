package com.yorha.common.resource.resservice.bossfield;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BossFieldDataTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(BossFieldDataTemplateService.class);
    /**
     * 战场类型关注事件和对应阶段
     * bossFieldType-> eventList
     */
    private final Map<Integer, List<Integer>> bossFieldEventList = new HashMap<>();

    private final Map<Integer, Map<Integer, Integer>> bossFieldRankReward = Maps.newHashMap();
    /**
     * bossFieldId -> areaList(areaId)
     */
    private final Map<Integer, List<List<Integer>>> monsterAreaList = new HashMap<>();
    /**
     * groupId -> list
     */
    private final Map<Integer, List<MonsterRefreshGroupTemplate>> monsterGroup = new HashMap<>();
    /**
     * 销毁事件id -> monster刷新序号
     */
    private final Map<Integer, List<Integer>> destroyEvent2RefreshId = new HashMap<>();
    /**
     * 事件id -> 跳转阶段id
     */
    private final Map<Integer, Integer> event2Stage = new HashMap<>();
    /**
     * 阶段id -> 对应data
     */
    private final Map<Integer, BossFieldStageTemplate> stageTemplate = new HashMap<>();

    public BossFieldDataTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        parseBossFieldStage();
        parseBossFieldEvent();
        parseMonsterArea();
        parseMonsterGroup();
        parseMonsterRefresh();
    }

    /**
     * 解析战场阶段表
     */
    private void parseBossFieldStage() throws ResourceException {
        Collection<BossFieldStageTemplate> bossStageList = getResHolder().getListFromMap(BossFieldStageTemplate.class);
        for (BossFieldStageTemplate template : bossStageList) {
            stageTemplate.put(template.getStage(), template);
            int enterEventId = template.getEnterEventId();
            if (enterEventId == 0) {
                continue;
            }
            // 事件id已经在关注列表里了，配置重复了
            if (event2Stage.containsKey(enterEventId)) {
                throw new ResourceException(StringUtils.format("bossFieldStage enterEventId  repeat. {}", enterEventId));
            }
            event2Stage.put(enterEventId, template.getStage());
        }
    }

    /**
     * 解析战场类型表 存储 每个战场类型关注的事件和对应的阶段
     */
    private void parseBossFieldEvent() throws ResourceException {
        for (Map.Entry<Integer, BossFieldTypeTemplate> entry : getResHolder().getMap(BossFieldTypeTemplate.class).entrySet()) {
            int k = entry.getKey();
            BossFieldTypeTemplate v = entry.getValue();
            if (v.getEndSuccEvent() == 0 || v.getEndFailEvent() == 0) {
                throw new ResourceException(StringUtils.format("bossFieldType no succ or fail event. type: {}", v.getId()));
            }
            List<Integer> subscribeEvent = Lists.newArrayList(v.getSubscribeEventList());
            bossFieldEventList.put(k, subscribeEvent);
            // 胜负事件特殊处理
            subscribeEvent.add(v.getEndSuccEvent());
            subscribeEvent.add(v.getEndFailEvent());

            Map<Integer, Integer> rankReward = Maps.newHashMap();
            bossFieldRankReward.put(k, rankReward);
            v.getSuccMailPairList().forEach(d -> {
                rankReward.put(d.getKey(), d.getValue());
            });
        }
        LOGGER.debug("load end. bossFieldEventList={}", bossFieldEventList);
    }

    /**
     * 解析战场表和区域表  初始化每个战场的区域数据
     */
    private void parseMonsterArea() {
        Map<Integer, BossFieldTemplate> templateMap = getResHolder().getMap(BossFieldTemplate.class);
        for (BossFieldTemplate template : templateMap.values()) {
            String monsterArea = template.getMonsterAreaList();
            if (monsterArea == null || monsterArea.equals("")) {
                continue;
            }
            List<List<Integer>> areaList = new ArrayList<>();
            Arrays.asList(monsterArea.split(",")).forEach(str -> {
                areaList.add(Arrays.stream(str.split("_")).map(Integer::parseInt).collect(Collectors.toList()));
            });
            monsterAreaList.put(template.getId(), areaList);
        }
        LOGGER.debug("load end.monsterAreaList={}", monsterAreaList);
    }

    /**
     * 解析战场怪物刷新组表  存储 刷新组ID对应的刷新序号List
     */
    private void parseMonsterGroup() {
        getResHolder().getListFromMap(MonsterRefreshGroupTemplate.class).forEach(v -> {
            if (monsterGroup.containsKey(v.getGroupId())) {
                monsterGroup.get(v.getGroupId()).add(v);
            } else {
                List<MonsterRefreshGroupTemplate> list = new ArrayList<>();
                list.add(v);
                monsterGroup.put(v.getGroupId(), list);
            }
        });
        LOGGER.debug("load end.monsterGroup={}", monsterGroup.keySet());
    }

    /**
     * 解析战场怪物表 存储 事件ID对应的销毁的怪物刷新序号
     */
    private void parseMonsterRefresh() {
        getResHolder().getListFromMap(BossFieldMonsterTemplate.class).forEach(v -> {
            if (v.getDestroyEventIdList() != null) {
                v.getDestroyEventIdList().forEach(e -> {
                    if (!destroyEvent2RefreshId.containsKey(e)) {
                        destroyEvent2RefreshId.put(e, new ArrayList<>());
                    }
                    destroyEvent2RefreshId.get(e).add(v.getId());
                });
            }
        });
        LOGGER.debug("load end.event2RefreshId={}", destroyEvent2RefreshId);
    }

    public List<MonsterRefreshGroupTemplate> getMonsterGroup(int groupId) {
        return monsterGroup.get(groupId);
    }

    public List<Integer> getDestroyRefreshId(int eventId) {
        return destroyEvent2RefreshId.get(eventId);
    }

    public boolean hasBossFieldTemplate(int bossFieldId) {
        return monsterAreaList.containsKey(bossFieldId);
    }

    public BossFieldTemplate getBossFieldTemplate(int bossFieldId) {
        return getResHolder().getValueFromMap(BossFieldTemplate.class, bossFieldId);
    }

    public List<Integer> getBossFieldEventMap(int bossFieldId) {
        return bossFieldEventList.get(getBossFieldTemplate(bossFieldId).getType());
    }

    public BossFieldStageTemplate getBossFieldStageTemplate(int stage) {
        return stageTemplate.get(stage);
    }

    public BossFieldTypeTemplate getBossFieldTypeTemplate(int bossFieldId) {
        int type = getBossFieldTemplate(bossFieldId).getType();
        return getResHolder().getValueFromMap(BossFieldTypeTemplate.class, type);
    }

    public BossFieldEventTemplate getBossFieldEventTemplate(int eventId) {
        return getResHolder().getValueFromMap(BossFieldEventTemplate.class, eventId);
    }

    public BossFieldMonsterTemplate getBossFieldMonsterTemplate(int id) {
        return getResHolder().getValueFromMap(BossFieldMonsterTemplate.class, id);
    }

    public List<List<Integer>> getMonsterAreaListByBossFieldId(int bossFieldId) {
        return monsterAreaList.get(bossFieldId);
    }

    public int getStageByEvent(int event) {
        if (event2Stage.containsKey(event)) {
            return event2Stage.get(event);
        }
        // 非受检事件返回初始阶段
        return 0;
    }

    public Map<Integer, Integer> getBossFieldRankReward(int bossFieldType) {
        return bossFieldRankReward.get(bossFieldType);
    }

    @Override
    public void checkValid() throws ResourceException {
        // check 战场类型存在性
        for (BossFieldTemplate d : getResHolder().getListFromMap(BossFieldTemplate.class)) {
            getResHolder().checkValueFromMap(BossFieldTypeTemplate.class, d.getType(),
                    () -> StringUtils.format("BossFieldTypeTemplate 战场类型不存在. BossFieldTemplate->type:{}", d.getType()));
        }
        // check 战场区域存在性
        for (List<List<Integer>> areaList : monsterAreaList.values()) {
            for (List<Integer> areas : areaList) {
                for (int areaId : areas) {
                    getResHolder().checkValueFromMap(BossFieldAreaTemplate.class, areaId,
                            () -> StringUtils.format("BossFieldAreaTemplate 战场区域id不存在. areaId:{}", areaId));
                }
            }
        }
        // check 怪物刷新序号的存在性
        getResHolder().getListFromMap(BossFieldEventTemplate.class).forEach(d -> {
            List<Integer> monsterRefreshIdList = d.getMonsterRefreshIdList();
            if (monsterRefreshIdList != null && !monsterRefreshIdList.isEmpty()) {
                monsterRefreshIdList.forEach(this::getBossFieldMonsterTemplate);
            }
        });
        // 检测boss战场类型订阅事件是否重复  是否存在
        for (List<Integer> eventList : bossFieldEventList.values()) {
            Set<Integer> set = new HashSet<>();
            for (Integer event : eventList) {
                if (set.contains(event)) {
                    throw new ResourceException("bossFieldType subscribeEvent repeat");
                }
                getBossFieldEventTemplate(event);
                set.add(event);
            }
        }

    }

}
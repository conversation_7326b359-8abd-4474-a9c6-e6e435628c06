package com.yorha.common.etcd;

import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.KeyValue;
import io.etcd.jetcd.Watch;
import io.etcd.jetcd.common.exception.CompactedException;
import io.etcd.jetcd.kv.GetResponse;
import io.etcd.jetcd.options.GetOption;
import io.etcd.jetcd.options.WatchOption;
import io.etcd.jetcd.watch.WatchEvent;
import io.etcd.jetcd.watch.WatchResponse;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 用于保证Etcd内值的变化可以被touch到。
 * 注意：
 * 1. revision、modVersion、version区别：https://github.com/etcd-io/etcd/issues/6518
 * 2. jetcd watch遇到网络断联等问题会自动恢复，并监听到最后一次touch的revision值。
 * 3. 监听瞬间key存在，触发(key, value)事件；监听瞬间key被删除，则按照删除处理；监听瞬间key不存在，则无任何事件。
 * 4. 当watch的时候revision已经被压缩，会触发CompactedException以及触发watch完成。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class EtcdKeyValueWatcher {
    private static final Logger LOGGER = LogManager.getLogger(EtcdKeyValueWatcher.class);

    /**
     * 监听操作的实体，接收etcd消息。
     * 注意：监听指定前缀，接口扩展了常见的Etcd监听能力，当前的数据也会以KV的数据触发到handler上，不必额外使用getPrefix。
     *
     * <AUTHOR>
     */
    @ThreadSafe
    public static class EtcdWatcherItem implements Watch.Listener {
        private static final Logger LOGGER = LogManager.getLogger(EtcdWatcherItem.class);

        private final ReentrantLock mutex;
        private final String key;
        private final boolean isPrefix;
        private final Set<String> gottenKeys;
        private final Client client;
        private final Executor executor;
        private final IWatchHandler handler;
        private volatile boolean isWatch;
        private Watch.Watcher watcher;

        private EtcdWatcherItem(Client client, String key, boolean isPrefix, Executor executor, IWatchHandler handler) {
            this.client = client;
            this.key = key;
            this.isPrefix = isPrefix;
            this.executor = executor;
            this.handler = handler;
            this.gottenKeys = new LinkedHashSet<>();
            this.isWatch = false;
            this.mutex = new ReentrantLock(true);
        }

        public void unwatch() {
            try {
                this.mutex.lock();
                LOGGER.info("EtcdWatcherItem unwatch watcher={} start", this);
                // 关闭watch
                if (this.watcher != null) {
                    this.watcher.close();
                    this.watcher = null;
                }
                // 触发delete
                final List<String> keys = new ArrayList<>(this.gottenKeys);
                for (final String key : keys) {
                    this.delete(key);
                }
                this.isWatch = false;
                LOGGER.info("EtcdWatcherItem unwatch watcher={} end", this);
            } finally {
                this.mutex.unlock();
            }
        }

        public void watch() {
            try {
                this.mutex.lock();
                LOGGER.info("EtcdWatcherItem watch watcher={} start", this);
                // 关闭watch
                if (this.watcher != null) {
                    this.watcher.close();
                    this.watcher = null;
                    LOGGER.info("EtcdWatcherItem watch, close old watch, watcher={}", this);
                }
                final GetResponse getResponse;
                // 曾获取的key
                final Set<String> keys = new LinkedHashSet<>(this.gottenKeys);
                long revision = 0;
                try {
                    // 读取的时候坚决不使用序列化模式，使用线性化模式。
                    // 线性化模式返回达成共识后的值。序列化模式在未达成共识的时候也可以返回，吞吐更好。
                    final GetOption option = GetOption.newBuilder()
                            .isPrefix(this.isPrefix)
                            .withSerializable(false)
                            .build();
                    // 拉取前缀匹配的所有keys
                    getResponse = this.client.getKVClient()
                            .get(EtcdUtils.bytesOf(this.key), option)
                            .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                    for (final KeyValue kv : getResponse.getKvs()) {
                        final String k = EtcdUtils.toString(kv.getKey());
                        final String v = EtcdUtils.toString(kv.getValue());
                        this.update(k, v);
                        keys.remove(k);
                    }
                    // 获取版本号
                    revision = getResponse.getHeader().getRevision();
                } catch (StatusRuntimeException exception) {
                    // 如果是INVALID_ARGUMENT，代表无任何数据，则走当前所有watchDelete的操作
                    if (exception.getStatus() != Status.INVALID_ARGUMENT) {
                        throw exception;
                    }
                }
                // 删除所有无效的数据
                for (final String k : keys) {
                    this.delete(k);
                }
                // 带版本号watch
                // 1. etcd 可以watch比当前版本号更高的版本号
                // 2. 必须比当前版本号 + 1，否则key对应的修改正好是当前版本，会直接触发一次watch事件
                final WatchOption option = WatchOption.newBuilder()
                        .isPrefix(this.isPrefix)
                        .withRevision(revision + 1)
                        .build();
                LOGGER.info("EtcdWatcherItem watch watcher={}, curRevision={}, watchRevision={} end", this, revision, option.getRevision());
                this.client.getWatchClient().watch(EtcdUtils.bytesOf(this.key), option, this);
                this.isWatch = true;
            } catch (Exception e) {
                throw new GeminiException("EtcdWatcherItem watch " + this, e);
            } finally {
                this.mutex.unlock();
            }
        }

        private void update(String key, String value) {
            try {
                LOGGER.debug("EtcdWatcherItem try update key={}, value={}", key, value);
                this.handler.onUpdate(key, value);
            } catch (Exception e) {
                LOGGER.error("EtcdWatcherItem update key={}, value={}, e=", key, value, e);
            } finally {
                this.gottenKeys.add(key);
            }
        }

        private void delete(String key) {
            try {
                LOGGER.info("EtcdWatcherItem try delete key={}", key);
                this.handler.onDelete(key);
            } catch (Exception e) {
                LOGGER.error("EtcdWatcherItem delete key={}, e=", key, e);
            } finally {
                this.gottenKeys.remove(key);
            }
        }

        /**
         * 由vert.x驱动，底层跑再不同线程上，所以需要上锁。
         *
         * @param response 回包。
         */
        @Override
        public void onNext(WatchResponse response) {
            LOGGER.debug("EtcdWatcherItem onNext watcher={}, response={}", this, response);
            if (response.getEvents().isEmpty()) {
                return;
            }
            try {
                this.mutex.lock();
                if (!this.isWatch) {
                    LOGGER.debug("EtcdWatcherItem onNext watcher={}, skip when not watch", this);
                    return;
                }
                final List<WatchEvent> events = response.getEvents();
                for (final WatchEvent event : events) {
                    final WatchEvent.EventType eventType = event.getEventType();
                    final KeyValue keyValue = event.getKeyValue();
                    final String key = EtcdUtils.toString(keyValue.getKey());
                    final String value = EtcdUtils.toString(keyValue.getValue());
                    switch (eventType) {
                        case PUT:
                            this.update(key, value);
                            break;
                        case DELETE:
                            this.delete(key);
                            break;
                        default:
                            LOGGER.error("EtcdWatcherItem onNext watcher={}, key={}, value={}, eventType={} unknown etcd", this, key, value, eventType);
                            return;
                    }
                }
            } finally {
                this.mutex.unlock();
            }
        }

        /**
         * 如果出现压缩异常，代表revision太老了，需要更新。
         *
         * @param throwable 错误
         */
        @Override
        public void onError(Throwable throwable) {
            LOGGER.error("EtcdWatcherItem onError watcher={}, onError=", this, throwable);
            if (!(throwable instanceof CompactedException)) {
                return;
            }
            this.executor.execute(() -> {
                while (this.isWatch) {
                    try {
                        this.watch();
                        break;
                    } catch (Exception e) {
                        LOGGER.error("EtcdWatcherItem retryWatch watcher={}, e=", this, e);
                    }
                    LockSupport.parkNanos(this, TimeUnit.MILLISECONDS.toNanos(10));
                }
                LOGGER.info("EtcdWatcherItem retryWatch watcher={} ok", this);
            });
        }

        @Override
        public void onCompleted() {
            LOGGER.warn("EtcdWatcherItem onCompleted watcher={} done", this);
        }

        @Override
        public String toString() {
            return "EtcdWatcherItem{" +
                    "key='" + key + '\'' +
                    ", isPrefix=" + isPrefix +
                    ", gottenKeys=" + gottenKeys +
                    ", isWatch=" + isWatch +
                    '}';
        }

        static class Builder {
            private String key;
            private boolean isPrefix;
            private Client client;
            private Executor executor;
            private IWatchHandler handler;

            private Builder() {
            }

            public Builder key(final String key, final boolean isPrefix) {
                this.key = key;
                this.isPrefix = isPrefix;
                return this;
            }

            public Builder client(final Client client) {
                this.client = client;
                return this;
            }

            public Builder retry(Executor executor) {
                this.executor = executor;
                return this;
            }

            public Builder handler(IWatchHandler handler) {
                this.handler = handler;
                return this;
            }

            public EtcdWatcherItem build() {
                return new EtcdWatcherItem(this.client, this.key, this.isPrefix, this.executor, this.handler);
            }
        }

        static Builder newBuilder() {
            return new Builder();
        }
    }

    private final ReentrantLock mutex = new ReentrantLock(true);
    private final List<EtcdWatcherItem> watcherList;
    private final Executor executor;
    private final Client etcdClient;

    public EtcdKeyValueWatcher(final Client etcdClient, final Executor executor) {
        this.executor = executor;
        this.etcdClient = etcdClient;
        this.watcherList = new LinkedList<>();
    }

    public void watch(final String key, IWatchHandler handler, boolean isPrefix) {
        if (key == null) {
            throw new GeminiException("EtcdKeyValueWatcher watch null");
        }
        if (handler == null) {
            throw new GeminiException("EtcdKeyValueWatcher watch key={} handler null", key);
        }
        this.mutex.lock();
        try {
            LOGGER.info("EtcdKeyValueWatcher watch key={}, isPrefix={}, start", key, isPrefix);
            for (final EtcdWatcherItem watcher : this.watcherList) {
                if (StringUtils.equals(watcher.key, key)) {
                    LOGGER.warn("EtcdKeyValueWatcher watch 1 key={}, isPrefix={} repeat, watcher={}", key, isPrefix, watcher);
                }
                if (watcher.isPrefix && key.startsWith(watcher.key)) {
                    LOGGER.warn("EtcdKeyValueWatcher watch 2 key={}, isPrefix={} repeat, watcher={}", key, isPrefix, watcher);
                }
                if (isPrefix && key.startsWith(watcher.key)) {
                    LOGGER.warn("EtcdKeyValueWatcher watch 3 key={}, isPrefix=true repeat, watcher={}", key, watcher);
                }
            }
            final EtcdWatcherItem newWatcher = EtcdWatcherItem.newBuilder()
                    .key(key, isPrefix)
                    .retry(this.executor)
                    .client(this.etcdClient)
                    .handler(handler)
                    .build();
            newWatcher.watch();
            this.watcherList.add(newWatcher);
            LOGGER.info("EtcdKeyValueWatcher watch key={}, isPrefix={}, newWatcher={}, watcherList={}, end", key, isPrefix, newWatcher, this.watcherList);
        } finally {
            this.mutex.unlock();
        }
    }

    public void unwatch(final String key, final boolean isPrefix) {
        this.mutex.lock();
        try {
            LOGGER.info("EtcdKeyValueWatcher unwatch, key={}, isPrefix={}, start", key, isPrefix);
            final Iterator<EtcdWatcherItem> iterator = this.watcherList.iterator();
            while (iterator.hasNext()) {
                final EtcdWatcherItem watcher = iterator.next();
                if (!StringUtils.equals(watcher.key, key)) {
                    continue;
                }
                if (watcher.isPrefix != isPrefix) {
                    continue;
                }
                watcher.unwatch();
                iterator.remove();
                break;
            }
            LOGGER.info("EtcdKeyValueWatcher unwatch, key={}, isPrefix={}, watcherList={} end", key, isPrefix, this.watcherList);
        } finally {
            this.mutex.unlock();
        }
    }

    public void close() {
        this.mutex.lock();
        try {
            LOGGER.info("EtcdKeyValueWatcher close, watchers={}, start", this.watcherList);
            for (final EtcdWatcherItem watcher : this.watcherList) {
                watcher.unwatch();
            }
            this.watcherList.clear();
            LOGGER.info("EtcdKeyValueWatcher close end");
        } finally {
            this.mutex.unlock();
        }
    }
}

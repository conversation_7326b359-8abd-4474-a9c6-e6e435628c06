package com.yorha.common.enums.reason;

/**
 * <AUTHOR>
 */
public enum RallyDismissReason {
    // 正常的
    /**
     * 战斗结束
     */
    RDR_BATTLE_END,
    /**
     * 挂了
     */
    RDR_RETREAT,
    /**
     * 占领目标
     */
    RDR_OCCUPY_TARGET,

    // 没邮件的
    /**
     * 主动取消
     */
    RDR_CANCEL,


    // 有邮件的
    /**
     * 没人参加
     */
    RDR_NO_JOINER,
    /**
     * 目标没了
     */
    RDR_TARGET_LOSE,

    /**
     * 发起者退出联盟
     */
    RDR_QUIT_CLAN,
    /**
     * 联盟解散
     */
    RDR_CLAN_DISMISS,
    /**
     * 组织者的城升天了
     */
    RDR_ORGANIZER_CITY_ASCEND,
    /**
     * 目标无法攻击了
     */
    RDR_TARGET_CANT_ATTACK,
    /**
     * 找不到行军路径
     */
    RDR_NO_PATH
}

package com.yorha.common.auth.server;

import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.auth.server.result.*;
import com.yorha.common.io.ISession;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.proto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.yorha.common.constant.AuthConstant.AuthChannelType;

/**
 * msdk 鉴权渠道
 *
 * <AUTHOR>
 */
public class AuthChannelMsdk implements AuthChannel {
    private static final Logger LOGGER = LogManager.getLogger(AuthChannelMsdk.class);

    @Override
    public AuthChannelType authChannelType() {
        return AuthChannelType.MSDK;
    }

    @Override
    public void init() {
        final String authHost = ClusterConfigUtils.getWorldConfig().getStringItem("auth_msdk_domain");
        LOGGER.info("init msdk auth host:{}", authHost);
    }

    @Override
    public AuthResult<AuthResponse> auth(ISession session, GameActorWithCall actor, String openId, User.GetRoleList_C2S_Msg msg) {
        if (!msg.hasMsdkAccount()) {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_UNSUPPORTED_AUTH_CHANNEL, "msdkAccount not exists");
        }
        if (StringUtils.isEmpty(session.getClientIp())) {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_VERIFY_FAILED, "clientIp not found");
        }
        if (StringUtils.isEmpty(openId)) {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_VERIFY_FAILED, "openId is empty");
        }
        try {
            SsAuth.AuthMsdkAsk ask = SsAuth.AuthMsdkAsk.newBuilder()
                    .setOpenId(openId)
                    .setToken(msg.getMsdkAccount().getToken())
                    .setPlatformId(msg.getClientInfo().getPlatformId())
                    .setChannelId(msg.getClientInfo().getChannelId())
                    .build();

            SsAuth.AuthMsdkAns ans = actor.callAndCreate(RefFactory.ofAuth(), ask);
            AuthResponse authResponse = JsonUtils.parseObject(ans.getResult(), AuthResponse.class);
            if (authResponse.isSuccess()) {
                return AuthResult.ofSuccess();
            } else {
                return AuthResult.fail(CommonEnum.AuthRetCode.ARC_VERIFY_FAILED, authResponse.toString());
            }
        } catch (Exception e) {
            LOGGER.error("AuthChannelMsdk auth ", e);
            if (e instanceof GeminiException) {
                return AuthResult.fail(CommonEnum.AuthRetCode.ARC_VERIFY_FAILED, "post auth network error");
            }
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_VERIFY_FAILED, "unknown");
        }
    }

    @Override
    public String getOpenId(User.GetRoleList_C2S_Msg msg) {
        return msg.getMsdkAccount().getOpenId();
    }

    @Override
    public String getOpenId(CsAccount.DirGetServerList_C2S_Msg msg) {
        return msg.getOpenId();
    }

    @Override
    public String getOpenId(CsAccount.DirGetZone_C2S_Msg msg) {
        return msg.getOpenId();
    }
}

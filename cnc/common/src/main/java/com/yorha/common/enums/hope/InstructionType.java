package com.yorha.common.enums.hope;

/**
 * 计时决策返回指令
 *
 * <AUTHOR>
 */
public enum InstructionType {
    /**
     * 未定义
     */
    UNDEFINED(0),

    /**
     * 弹提示
     */
    TIPS(1),

    /**
     * 强制下线
     */
    LOGOUT(2),

    /**
     * 打开网页窗口
     */
    OPENURL(3),

    /**
     * 用户自定义
     */
    USER_DEFINED(4),

    /**
     * 停止操作
     */
    STOP(7),

    /**
     * 踢人预提醒指令
     */
    PRELOGOUT(8);

    private final int instructionType;

    InstructionType(int instructionType) {
        this.instructionType = instructionType;
    }

    public int getInstructionType() {
        return this.instructionType;
    }
}

package com.yorha.common.enums.qlog.battle;

/**
 * 战报结果enum
 *
 * <AUTHOR>
 */

public enum BattleRepResult {
    /**
     * 胜利
     */
    WIN,
    /**
     * 失败
     */
    LOSE,
    /**
     * 平局
     */
    DRAW;

    /**
     * 获取战斗结果
     */
    public static BattleRepResult getBattleResult(boolean selfAlive, boolean anyEnemyAlive) {
        if (selfAlive && anyEnemyAlive) {
            return DRAW;
        } else {
            return selfAlive ? WIN : LOSE;
        }
    }
}

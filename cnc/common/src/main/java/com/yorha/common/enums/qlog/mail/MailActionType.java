package com.yorha.common.enums.qlog.mail;

/**
 * 邮件Action enum
 *
 * <AUTHOR>
 */

public enum MailActionType {
    /**
     * 收到邮件
     */
    RECEIVE_MAIL("receive_mail"),
    /**
     * 读邮件
     */
    READ_MAIL("read_mail"),
    /**
     * 领取邮件奖励
     */
    COLLECTION_MAIL_REWARD("collect_mail_reward"),
    /**
     * 删除邮件
     */
    DELETE_MAIL("delete_mail"),
    /**
     * 过了有效期而被删除
     */
    PASSIVE_DELETE_OVERTIME("passive_delete_overtime"),
    /**
     * 过了容量上限而被删除
     */
    PASSIVE_DELETE_OVERLIMIT("passive_delete_overlimit"),
    /**
     * 加入收藏夹
     */
    ADD_TO_FAVORITE("add_to_favorite");

    private String type;

    MailActionType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}

package com.yorha.common.helper;

import com.yorha.common.asset.AssetPackage;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ActivityRecycleTemplate;
import res.template.ActivityTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 活动相关helper
 */
public class ActivityHelper {
    public static final Logger LOGGER = LogManager.getLogger(ActivityHelper.class);

    /**
     * 按配置计算一下需要清多少道具，补多少道具
     * 场景：N种道具兑换N种道具。最小兑换规则，多出的都自然扣掉
     *
     * @param hasItemFunc     提供一个函数判断拥有道具N个
     * @param recycleTemplate 活动道具兑换规则
     * @return Pair-> key:需要扣除的，value:需要补的  都不要返回null哈
     */
    public static Pair<AssetPackage, List<Struct.ItemPair>> buildRecycleOrRecoup(Function<Integer, Integer> hasItemFunc, ActivityRecycleTemplate recycleTemplate) {
        int count = -1;
        AssetPackage.Builder clearItem = AssetPackage.builder();
        for (IntPairType intPairType : recycleTemplate.getRecycleItemPairList()) {
            int hasNum = hasItemFunc.apply(intPairType.getKey());
            if (hasNum > 0) {
                clearItem.plusItem(intPairType.getKey(), hasNum);
            }
            // 够多少次
            int curCount = hasNum / intPairType.getValue();
            if (count == -1) {
                count = curCount;
            } else {
                count = Math.min(count, curCount);
            }
        }
        if (count <= 0) {
            return Pair.of(clearItem.build(), new ArrayList<>());
        }
        ArrayList<Struct.ItemPair> giver = new ArrayList<>();
        for (IntPairType intPairType : recycleTemplate.getCompensateItemPairList()) {
            giver.add(Struct.ItemPair.newBuilder().setItemTemplateId(intPairType.getKey()).setCount(intPairType.getValue() * count).build());
        }
        return Pair.of(clearItem.build(), giver);
    }

    /**
     * 检测并刷新数据（当活动id和开始时间不一致的时候刷新数据）
     */
    public static boolean checkOrRefreshData(long actId, long clanActId) {
        int oldStartTsSec = (int) (clanActId & 0xFFFFFFFFL);
        int newStartTsSec = (int) (actId & 0xFFFFFFFFL);
        int oldActId = (int) (clanActId >> 32);
        int newActId = (int) (actId >> 32);
        // 校验活动
        if (oldActId > 0 && oldActId != newActId) {
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        // 校验开始时间
        if (newStartTsSec <= 0) {
            LOGGER.error("checkOrRefreshData, startTsSec <= 0 startTsSec={}", newStartTsSec);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        // 开始时间不能往回倒
        if (newStartTsSec < oldStartTsSec) {
            LOGGER.error("checkOrRefreshData, startTsSec <= 0 startTsSec={}", newStartTsSec);
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        // 开始时间不能比当前时间还大
        long nowSec = TimeUtils.ms2Second(SystemClock.now());
        if (nowSec < newStartTsSec) {
            LOGGER.error("checkOrRefreshData, now before startTsSec? startTsSec={}", newStartTsSec);
            throw new GeminiException(ErrorCode.ACTIVITY_NOT_EXIST);
        }
        if (oldStartTsSec == newStartTsSec) {
            return false;
        }
        ActivityTemplate activityTemplate = ResHolder.getInstance().getValueFromMap(ActivityTemplate.class, newActId);
        LOGGER.info("checkOrRefreshData oldActId={} oldStartTsSec={} nowSec={} newActId={} newStartTsSec={}",
                oldActId, oldStartTsSec, nowSec, newActId, newStartTsSec);
        if (oldStartTsSec <= 0 || nowSec > TimeUnit.HOURS.toSeconds(activityTemplate.getLastingHours()) + oldStartTsSec) {
            return true;
        }
        return false;
    }

}

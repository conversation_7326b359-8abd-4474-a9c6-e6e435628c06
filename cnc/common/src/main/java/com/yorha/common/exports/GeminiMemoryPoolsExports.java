package com.yorha.common.exports;

import com.google.common.collect.Lists;
import io.prometheus.client.Collector;
import io.prometheus.client.GaugeMetricFamily;
import io.prometheus.client.Predicate;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryPoolMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static io.prometheus.client.SampleNameFilter.ALLOW_ALL;

/**
 * 由io.prometheus.client.hotspot.MemoryPoolsExports改造而来。
 *
 * <AUTHOR>
 */
public class GeminiMemoryPoolsExports extends Collector {

    private static final String JVM_MEMORY_OBJECTS_PENDING_FINALIZATION = "jvm_memory_objects_pending_finalization";
    private static final String JVM_MEMORY_BYTES_USED = "jvm_memory_bytes_used";
    private static final String JVM_MEMORY_BYTES_COMMITTED = "jvm_memory_bytes_committed";
    private static final String JVM_MEMORY_BYTES_MAX = "jvm_memory_bytes_max";
    private static final String JVM_MEMORY_BYTES_INIT = "jvm_memory_bytes_init";

    // Note: The Prometheus naming convention is that units belong at the end of the metric name.
    // For new metrics like jvm_memory_pool_collection_used_bytes we follow that convention.
    // For old metrics like jvm_memory_pool_bytes_used we keep the names as they are to avoid a breaking change.

    private static final String JVM_MEMORY_POOL_BYTES_USED = "jvm_memory_pool_bytes_used";
    private static final String JVM_MEMORY_POOL_BYTES_COMMITTED = "jvm_memory_pool_bytes_committed";
    private static final String JVM_MEMORY_POOL_BYTES_MAX = "jvm_memory_pool_bytes_max";
    private static final String JVM_MEMORY_POOL_BYTES_INIT = "jvm_memory_pool_bytes_init";
    private static final String JVM_MEMORY_POOL_COLLECTION_USED_BYTES = "jvm_memory_pool_collection_used_bytes";
    private static final String JVM_MEMORY_POOL_COLLECTION_COMMITTED_BYTES = "jvm_memory_pool_collection_committed_bytes";
    private static final String JVM_MEMORY_POOL_COLLECTION_MAX_BYTES = "jvm_memory_pool_collection_max_bytes";
    private static final String JVM_MEMORY_POOL_COLLECTION_INIT_BYTES = "jvm_memory_pool_collection_init_bytes";

    private final MemoryMXBean memoryBean;
    private final List<MemoryPoolMXBean> poolBeans;
    private final String busId;

    public GeminiMemoryPoolsExports(final String busId) {
        this(
                busId,
                ManagementFactory.getMemoryMXBean(),
                ManagementFactory.getMemoryPoolMXBeans());
    }

    public GeminiMemoryPoolsExports(final String busId, MemoryMXBean memoryBean,
                                    List<MemoryPoolMXBean> poolBeans) {
        this.busId = busId;
        this.memoryBean = memoryBean;
        this.poolBeans = poolBeans;
    }

    void addMemoryAreaMetrics(List<Collector.MetricFamilySamples> sampleFamilies, Predicate<String> nameFilter) {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();

        if (nameFilter.test(JVM_MEMORY_OBJECTS_PENDING_FINALIZATION)) {
            GaugeMetricFamily finalizer = new GaugeMetricFamily(
                    JVM_MEMORY_OBJECTS_PENDING_FINALIZATION,
                    "The number of objects waiting in the finalizer queue.",
                    Collections.singletonList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID)
            );
            finalizer.addMetric(Collections.singletonList(this.busId), memoryBean.getObjectPendingFinalizationCount());
            sampleFamilies.add(finalizer);
        }

        if (nameFilter.test(JVM_MEMORY_BYTES_USED)) {
            GaugeMetricFamily used = new GaugeMetricFamily(
                    JVM_MEMORY_BYTES_USED,
                    "Used bytes of a given JVM memory area.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA));
            used.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_HEAP), heapUsage.getUsed());
            used.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_NON_HEAP), nonHeapUsage.getUsed());
            sampleFamilies.add(used);
        }

        if (nameFilter.test(JVM_MEMORY_POOL_BYTES_COMMITTED)) {
            GaugeMetricFamily committed = new GaugeMetricFamily(
                    JVM_MEMORY_BYTES_COMMITTED,
                    "Committed (bytes) of a given JVM memory area.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA));
            committed.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_HEAP), heapUsage.getCommitted());
            committed.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_NON_HEAP), nonHeapUsage.getCommitted());
            sampleFamilies.add(committed);
        }

        if (nameFilter.test(JVM_MEMORY_BYTES_MAX)) {
            GaugeMetricFamily max = new GaugeMetricFamily(
                    JVM_MEMORY_BYTES_MAX,
                    "Max (bytes) of a given JVM memory area.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA));
            max.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_HEAP), heapUsage.getMax());
            max.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_NON_HEAP), nonHeapUsage.getMax());
            sampleFamilies.add(max);
        }

        if (nameFilter.test(JVM_MEMORY_BYTES_INIT)) {
            GaugeMetricFamily init = new GaugeMetricFamily(
                    JVM_MEMORY_BYTES_INIT,
                    "Initial bytes of a given JVM memory area.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA));
            init.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_HEAP), heapUsage.getInit());
            init.addMetric(Lists.newArrayList(this.busId, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_AREA_NON_HEAP), nonHeapUsage.getInit());
            sampleFamilies.add(init);
        }
    }

    void addMemoryPoolMetrics(List<Collector.MetricFamilySamples> sampleFamilies, Predicate<String> nameFilter) {

        boolean anyPoolMetricPassesFilter = false;

        GaugeMetricFamily used = null;
        if (nameFilter.test(JVM_MEMORY_POOL_BYTES_USED)) {
            used = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_BYTES_USED,
                    "Used bytes of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(used);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily committed = null;
        if (nameFilter.test(JVM_MEMORY_POOL_BYTES_COMMITTED)) {
            committed = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_BYTES_COMMITTED,
                    "Committed bytes of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(committed);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily max = null;
        if (nameFilter.test(JVM_MEMORY_POOL_BYTES_MAX)) {
            max = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_BYTES_MAX,
                    "Max bytes of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(max);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily init = null;
        if (nameFilter.test(JVM_MEMORY_POOL_BYTES_INIT)) {
            init = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_BYTES_INIT,
                    "Initial bytes of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(init);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily collectionUsed = null;
        if (nameFilter.test(JVM_MEMORY_POOL_COLLECTION_USED_BYTES)) {
            collectionUsed = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_COLLECTION_USED_BYTES,
                    "Used bytes after last collection of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(collectionUsed);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily collectionCommitted = null;
        if (nameFilter.test(JVM_MEMORY_POOL_COLLECTION_COMMITTED_BYTES)) {
            collectionCommitted = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_COLLECTION_COMMITTED_BYTES,
                    "Committed after last collection bytes of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(collectionCommitted);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily collectionMax = null;
        if (nameFilter.test(JVM_MEMORY_POOL_COLLECTION_MAX_BYTES)) {
            collectionMax = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_COLLECTION_MAX_BYTES,
                    "Max bytes after last collection of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(collectionMax);
            anyPoolMetricPassesFilter = true;
        }
        GaugeMetricFamily collectionInit = null;
        if (nameFilter.test(JVM_MEMORY_POOL_COLLECTION_INIT_BYTES)) {
            collectionInit = new GaugeMetricFamily(
                    JVM_MEMORY_POOL_COLLECTION_INIT_BYTES,
                    "Initial after last collection bytes of a given JVM memory pool.",
                    Lists.newArrayList(GeminiExportsConst.GEMINI_EXPORTS_LABEL_BUS_ID, GeminiExportsConst.GEMINI_EXPORTS_LABEL_GC_POOL));
            sampleFamilies.add(collectionInit);
            anyPoolMetricPassesFilter = true;
        }
        if (anyPoolMetricPassesFilter) {
            for (final MemoryPoolMXBean pool : poolBeans) {
                MemoryUsage poolUsage = pool.getUsage();
                if (poolUsage != null) {
                    addPoolMetrics(used, committed, max, init, pool.getName(), poolUsage);
                }
                MemoryUsage collectionPoolUsage = pool.getCollectionUsage();
                if (collectionPoolUsage != null) {
                    addPoolMetrics(collectionUsed, collectionCommitted, collectionMax, collectionInit, pool.getName(), collectionPoolUsage);
                }
            }
        }
    }

    private void addPoolMetrics(GaugeMetricFamily used, GaugeMetricFamily committed, GaugeMetricFamily max, GaugeMetricFamily init, String poolName, MemoryUsage poolUsage) {
        if (used != null) {
            used.addMetric(Lists.newArrayList(this.busId, poolName), poolUsage.getUsed());
        }
        if (committed != null) {
            committed.addMetric(Lists.newArrayList(this.busId, poolName), poolUsage.getCommitted());
        }
        if (max != null) {
            max.addMetric(Lists.newArrayList(this.busId, poolName), poolUsage.getMax());
        }
        if (init != null) {
            init.addMetric(Lists.newArrayList(this.busId, poolName), poolUsage.getInit());
        }
    }

    @Override
    public List<Collector.MetricFamilySamples> collect() {
        return collect(null);
    }

    @Override
    public List<Collector.MetricFamilySamples> collect(Predicate<String> nameFilter) {
        List<Collector.MetricFamilySamples> mfs = new ArrayList<Collector.MetricFamilySamples>();
        addMemoryAreaMetrics(mfs, nameFilter == null ? ALLOW_ALL : nameFilter);
        addMemoryPoolMetrics(mfs, nameFilter == null ? ALLOW_ALL : nameFilter);
        return mfs;
    }
}
package com.yorha.common.actor.ref;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.io.SsMsgTypes;

/**
 * actor 消息发送
 *
 * <AUTHOR>
 */
public class ActorSendMsgUtils {
    private ActorSendMsgUtils() {
    }

    /**
     * 发送消息（发件为nobody）
     *
     * @param receiver 收件actor
     * @param msg      消息
     */
    public static void send(IActorRef receiver, IActorMsg msg) {
        send(receiver, IActorRef.NOBODY, msg);
    }

    /**
     * 发送消息
     *
     * @param receiver 收件actor
     * @param sender   发件actor
     * @param msg      消息
     */
    public static void send(IActorRef receiver, IActorRef sender, IActorMsg msg) {
        ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromTell(sender, msg);
        envelope.setReceiver(receiver);
        ActorMsgSystem.dispatchMsg(envelope);
    }


    /**
     * 发送ss消息（发件为nobody）
     *
     * @param receiver 收件actor
     * @param ssMsg    ss pb消息
     */
    public static void send(IActorRef receiver, GeneratedMessageV3 ssMsg) {
        send(receiver, IActorRef.NOBODY, ssMsg);
    }

    /**
     * 发送ss消息
     *
     * @param receiver 收件actor
     * @param sender   发件actor
     * @param ssMsg    ss pb消息
     */
    public static void send(IActorRef receiver, IActorRef sender, GeneratedMessageV3 ssMsg) {
        send(receiver, sender, new TypedMsg(SsMsgTypes.getTypeFromMsg(ssMsg), ssMsg));
    }

    /**
     * 发送消息且拉起收件actor
     *
     * @param receiver 收件actor
     * @param sender   发件actor
     * @param msg      消息
     */
    public static void sendAndCreate(IActorRef receiver, IActorRef sender, IActorMsg msg) {
        ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromTell(sender, msg);
        envelope.setReceiver(receiver);
        envelope.setTagCreate(true);
        ActorMsgSystem.dispatchMsg(envelope);
    }

    /**
     * 发送消息且拉起收件actor（发件为nobody）
     *
     * @param receiver 收件actor
     * @param msg      消息
     */
    public static void sendAndCreate(IActorRef receiver, IActorMsg msg) {
        sendAndCreate(receiver, IActorRef.NOBODY, msg);
    }

    /**
     * 发送ss消息并拉起收件actor（发件为nobody）
     *
     * @param receiver 收件actor
     * @param pbMsg    ss pb消息
     */
    public static void sendAndCreate(IActorRef receiver, GeneratedMessageV3 pbMsg) {
        sendAndCreate(receiver, IActorRef.NOBODY, pbMsg);
    }

    /**
     * 发送ss消息并拉起收件actor
     *
     * @param receiver 收件actor
     * @param sender   发件actor
     * @param pbMsg    ss pb消息
     */
    public static void sendAndCreate(IActorRef receiver, IActorRef sender, GeneratedMessageV3 pbMsg) {
        sendAndCreate(receiver, sender, new TypedMsg(SsMsgTypes.getTypeFromMsg(pbMsg), pbMsg));
    }

}

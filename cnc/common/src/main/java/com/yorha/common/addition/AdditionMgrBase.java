package com.yorha.common.addition;

import com.google.common.collect.Maps;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.game.gen.prop.AdditionProp;
import com.yorha.game.gen.prop.AdditionSysProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AdditionMgrBase<T extends AbstractEntity> {
    public static final Logger LOGGER = LogManager.getLogger(AdditionMgrBase.class);

    private final T owner;

    private final Map<AdditionProviderType, AdditionProviderInterface> additionProviderMap = Maps.newHashMap();
    /**
     * 根据来源分组加成
     * <p>
     * key：来源 value:各加成
     */
    private Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> additionMapBySource;

    protected AdditionMgrBase(T owner) {
        this.owner = owner;
        init();
    }

    protected void init() {
        additionMapBySource = AdditionUtil.additionSysToSourceMap(getAdditionSys());
    }

    public T getOwner() {
        return owner;
    }

    /**
     * 注册会提供加成的component
     */
    public void register(AdditionProviderInterface provider) {
        additionProviderMap.put(provider.type(), provider);
    }

    public AdditionProviderInterface getProvider(AdditionProviderType providerType) {
        return additionProviderMap.get(providerType);
    }

    public Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> getAdditionFromProvider(
            AdditionProviderType providerType,
            List<Integer> additionIds
    ) throws GeminiException {
        AdditionProviderInterface provider = getProvider(providerType);
        if (provider == null) {
            throw new GeminiException(ErrorCode.ADDITION_PROVIDER_NOT_REGISTERED);
        } else {
            Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> res = Maps.newHashMap();
            for (Integer additionId : additionIds) {
                if (additionId <= 0) {
                    continue;
                }
                // 从provider中获取加成值
                Map<CommonEnum.AdditionSourceType, Long> additionFromProvider = provider.getAdditionFromProvider(additionId);
                for (Map.Entry<CommonEnum.AdditionSourceType, Long> entry : additionFromProvider.entrySet()) {
                    res.computeIfAbsent(entry.getKey(), v -> Maps.newHashMap()).put(additionId, entry.getValue());
                }
            }
            return res;
        }
    }

    public abstract AdditionSysProp getAdditionSys();

    /**
     * 更新加成
     *
     * @param sourceType 来源
     * @param additions  key: additionId value:value
     */
    public void updateAddition(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        AdditionUtil.updateAdditionSys(getAdditionSys(), sourceType, additions);
        // 更新scourMap
        updateSourceMap(sourceType, additions);
    }

    public Set<Integer> getAdditionIdsBySource(CommonEnum.AdditionSourceType sourceType) {
        return getAdditionBySource(sourceType).keySet();
    }

    public Map<Integer, Long> getAdditionBySource(CommonEnum.AdditionSourceType sourceType) {
        return additionMapBySource.getOrDefault(sourceType, Maps.newHashMap());
    }

    private void updateSourceMap(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> additions) {
        for (Map.Entry<Integer, Long> entry : additions.entrySet()) {
            if (entry.getValue() == 0) {
                // value==0 就删掉
                additionMapBySource.getOrDefault(sourceType, Maps.newHashMap()).remove(entry.getKey());
                continue;
            }
            Map<Integer, Long> sourceMap = additionMapBySource.computeIfAbsent(sourceType, v -> Maps.newHashMap());
            sourceMap.put(entry.getKey(), entry.getValue());
        }
    }

    public long getAddition(int additionId) {
        AdditionProp additionV = getAdditionSys().getAdditionV(additionId);
        if (additionV != null) {
            return additionV.getTotalValue();
        } else {
            return 0;
        }
    }

    /**
     * 更新指定模块的加成
     *
     * @param providerType 提供加成的模块类型
     */
    public void updateAdditionByProvider(AdditionProviderType providerType, @NotNull List<Integer> additionIds) {
        if (additionIds.isEmpty()) {
            return;
        }
        // 获取指定来源模块的加成值
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> additions = getAdditionFromProvider(providerType, additionIds);
        if (!additions.isEmpty()) {
            for (Map.Entry<CommonEnum.AdditionSourceType, Map<Integer, Long>> entry : additions.entrySet()) {
                update(entry.getKey(), entry.getValue());
            }
        } else {
            LOGGER.error("{} updateAddition additions from provider is empty. providerType:{}, additionIds:{}", getOwner(), providerType, additionIds);
        }
    }

    /**
     * 更新指定来源的加成
     *
     * @param sourceType 提供加成的来源类型
     */
    public void updateAdditionBySource(CommonEnum.AdditionSourceType sourceType, Map<Integer, Long> addition) {
        if (addition.isEmpty()) {
            return;
        }
        update(sourceType, addition);
    }

    /**
     * 更新指定additionSys
     */
    public void updateAdditionBySys(Struct.AdditionSys additionSys) {
        if (additionSys == null) {
            LOGGER.error("{} updateAddition additionSys is null", getOwner());
            return;
        }

        AdditionSysProp additionSysProp = new AdditionSysProp();
        additionSysProp.mergeFromSs(additionSys);
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> sourceMap = AdditionUtil.additionSysToSourceMap(additionSysProp);

        for (Map.Entry<CommonEnum.AdditionSourceType, Map<Integer, Long>> entry : sourceMap.entrySet()) {
            update(entry.getKey(), entry.getValue());
        }
    }

    protected abstract void update(CommonEnum.AdditionSourceType sourceType, @NotNull Map<Integer, Long> additions);

    public void refreshAllAddition() {
        List<Integer> allAdditionId = Arrays.stream(CommonEnum.BuffEffectType.values()).map(CommonEnum.BuffEffectType::getNumber).collect(Collectors.toList());
        for (AdditionProviderType type : additionProviderMap.keySet()) {
            refreshAdditionByProvider(type, allAdditionId);
        }
    }

    private void refreshAdditionByProvider(AdditionProviderType providerType, @NotNull List<Integer> additionIds) {
        if (additionIds.isEmpty()) {
            return;
        }
        // 获取指定来源模块的加成值
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> additions = getAdditionFromProvider(providerType, additionIds);
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> copyMap = Maps.newHashMap(additions);

        for (Map.Entry<CommonEnum.AdditionSourceType, Map<Integer, Long>> entry : copyMap.entrySet()) {
            // 本来就没有这个source
            if (!additionMapBySource.containsKey(entry.getKey())) {
                continue;
            }
            // 找出需要删除的加成id
            Map<Integer, Long> baseMap = additionMapBySource.get(entry.getKey());
            for (Integer additionId : additionIds) {
                if (!entry.getValue().containsKey(additionId) && baseMap.containsKey(additionId)) {
                    entry.getValue().put(additionId, 0L);
                }
            }
        }
        // 清空所有NONE的来源。从2024.02.28之后不允许有NONE来源,所以做一次清空
        if (additionMapBySource.containsKey(CommonEnum.AdditionSourceType.AST_INNER_NONE)) {
            for (Map.Entry<Integer, Long> entry : additionMapBySource.get(CommonEnum.AdditionSourceType.AST_INNER_NONE).entrySet()) {
                if (entry.getValue() != 0) {
                    copyMap.computeIfAbsent(CommonEnum.AdditionSourceType.AST_INNER_NONE, v -> Maps.newHashMap()).put(entry.getKey(), 0L);
                }
            }
        }
        Map<CommonEnum.AdditionSourceType, Map<Integer, Long>> mapToPrint = Maps.newHashMap();
        for (Map.Entry<CommonEnum.AdditionSourceType, Map<Integer, Long>> entry : copyMap.entrySet()) {
            if (entry.getValue().isEmpty()) {
                continue;
            }
            var v = entry.getValue().entrySet().stream().filter(i -> i.getValue() != 0).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            mapToPrint.put(entry.getKey(), v);
        }
        LOGGER.info("{} refreshAdditionByProvider providerType={} copy={}", owner, providerType, mapToPrint);
        for (Map.Entry<CommonEnum.AdditionSourceType, Map<Integer, Long>> entry : copyMap.entrySet()) {
            update(entry.getKey(), entry.getValue());
        }
    }
}

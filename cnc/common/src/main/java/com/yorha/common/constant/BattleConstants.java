package com.yorha.common.constant;

/**
 * 战斗常量
 *
 * <AUTHOR>
 */
public interface BattleConstants {
    /**
     * 根据标签
     */
    int BUFF_TAG = 1;
    /**
     * 根据buff组id
     */
    int BUFF_GROUP_ID = 2;
    /**
     * 根据buff id
     */
    int BUFF_ID = 3;
    /**
     * 永远
     */
    int BUFF_ALWAYS = 4;
    /**
     * 据点和城市
     */
    int MAPBUILDING_AND_CITY = 0;
    /**
     * 据点
     */
    int MAPBUILDING = 1;
    /**
     * 城市
     */
    int CITY = 2;
    /**
     * 主将
     */
    int MAIN_POSITION = 1;
    /**
     * 副将
     */
    int DEPUTY_POSITION = 2;
    // battle simulator中entityId分段，保证各个scene中的entityId不重复
    int SHIT_ENTITY_ID_PREFIX = 120000;
    /**
     * 延时结束战斗回合数
     */
    int DELAY_END_ROUND = 2;

    enum BattleRoleState {
        /**
         * 不可战斗
         */
        FORBIDDEN,
        /**
         * 定身
         */
        FROZEN,
        /**
         * 缴械
         */
        DISARM,
        /**
         * 无敌
         */
        INVINCIBLE,
        /**
         * 沉默
         */
        SILENCE,
        /**
         * 禁疗
         */
        NO_TREATMENT,
        /**
         * 隐身
         */
        STEALTH
    }

    enum BattleGroundType {
        /**
         * 大世界
         */
        BIG_SCENE,
        /**
         * 副本
         */
        DUNGEON,
        /**
         * 模拟
         */
        SIMULATE
    }

    enum BattleRelationStatus {
        /**
         * 正常战斗中
         */
        Running,
        /**
         * 挂起，不会执行出手
         */
        Suspend,
        /**
         * 停止战斗，关系将被清理掉
         */
        Stop
    }

    enum BattleRelationType {
        /**
         * 主动普攻建立
         */
        Active,
        /**
         * 被伤害溅射，被动建立
         */
        Passive,
        /**
         * 区域技能，战斗关系永不结束，直到其中一方销毁
         */
        AreaSkill
    }

    enum BattleBroadCastNtfReason {
        /**
         * BUFF
         */
        BUFF,
        /**
         * 技能
         */
        FIRE,
        /**
         * 触发器
         */
        TRIGGER,
        /**
         * 主动技能预释放
         */
        PREPARE,
        /**
         * TEST
         */
        TEST
    }

    /**
     * 战报省略头像信息的事件数
     */
    int BATTLE_REPORT_CARD_OMIT_FACTOR = 500;

    /**
     * 伤害类效果的DOT标识
     */
    int BATTLE_DAMAGE_WITH_DOT = 1;
    /**
     * 治疗类效果的HOT标识
     */
    int BATTLE_TREATMENT_WITH_HOT = 1;
}

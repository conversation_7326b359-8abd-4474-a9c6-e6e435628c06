package com.yorha.common.io;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actorservice.msg.MsgUtils;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.reflect.MethodWrapper;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 协议处理类
 *
 * <AUTHOR>
 */
public class Command {
    private static final Logger LOGGER = LogManager.getLogger(Command.class);
    private final CommonEnum.ModuleEnum module;
    private final int msgType;
    private final MethodWrapper wrapper;
    /**
     * protobuf中的DefaultInstance，用于byte[]解析成内存对象
     */
    private final GeneratedMessageV3 message;
    private final String name;
    /**
     * 当功能模块上锁时，本协议是否不受限
     */
    private final boolean isModuleWhite;

    public Command(MethodWrapper wrapper, CommonEnum.ModuleEnum module, boolean isModuleWhite, int msgType) {
        this.wrapper = wrapper;
        this.module = module;
        this.msgType = msgType;
        this.isModuleWhite = isModuleWhite;
        this.message = MsgType.getMsgFromType(msgType);
        this.name = MsgUtils.getMsgNameFromCsMsgType(msgType);
    }

    public int getMsgType() {
        return msgType;
    }

    public boolean isModuleWhite() {
        return isModuleWhite;
    }

    /**
     * 执行命令入口，处理seqId及c2sMsgId
     *
     * @param session  会话
     * @param c2sMsgId c2s消息对应的枚举值
     * @param seqId    c2s包中携带的序列id，服务端需要原样返回
     * @param content  包体中携带的内容信息
     */
    public void execute(ISession session, int c2sMsgId, int seqId, byte[] content) throws GeminiException {
        GeneratedMessageV3 ret = (GeneratedMessageV3) this.wrapper.invoke(session, MsgUtils.parseProto(this.message, content), seqId);
        if (null != ret) {
            int s2cMsgId = MsgType.getRetMsgId(c2sMsgId);
            if (s2cMsgId == 0) {
                LOGGER.error("cannot find s2cMsgId with c2sMsgId {}", c2sMsgId);
            } else {
                session.answerMsg(s2cMsgId, seqId, ret);
            }
        } else {
            if (c2sMsgId == MsgType.GETROLELIST_C2S_MSG || c2sMsgId == MsgType.LOGIN_C2S_MSG) {
                // 这两个协议走特殊下发
                return;
            }
            LOGGER.error("receive c2sMsg {} return null, no msg will be sent to client, {}", c2sMsgId, session);
        }
    }

    public Object executeFromPlayer(Object player, GeneratedMessageV3 msg, int seqId) {
        return this.wrapper.invoke(player, msg, seqId);
    }

    @SuppressWarnings("unchecked")
    public <T extends GeneratedMessageV3> T getGeneratedMessage(byte[] content) {
        T ret = null;
        try {
            ret = (T) message.getParserForType().parseFrom(content);
        } catch (Exception e) {
            WechatLog.error("Command getGeneratedMessage", e);
        }
        return ret;
    }

    public CommonEnum.ModuleEnum getModule() {
        return module;
    }

    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "Command{" +
                ", msgType=" + msgType +
                ", name='" + name +
                '}';
    }
}


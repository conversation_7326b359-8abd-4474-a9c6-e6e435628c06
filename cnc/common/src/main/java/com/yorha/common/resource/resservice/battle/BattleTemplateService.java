package com.yorha.common.resource.resservice.battle;

import com.google.common.collect.Maps;
import com.yorha.common.buff.DevBuffUtil;
import com.yorha.common.constant.BattleConstants;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.BattleType;
import com.yorha.proto.CommonEnum.SceneObjType;
import res.template.*;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 战斗相关的配置
 *
 * <AUTHOR>
 */
public class BattleTemplateService extends AbstractResService {

    private static final int RATIO_TYPE_OFFSET = 8;
    private static final int BATTLE_TYPE_KEY_OFFSET = 16;
    private static final int MAP_TYPE_KEY_OFFSET = 24;

    /**
     * 战损比例配置
     * key:
     * |--mapType--|--battleType--|--DamageRatioTypeEnum--|--sceneObjType--|
     * |--8bits--|--8bits--|--8bits--|--8bits--|
     */
    private final Map<Long, DamageRatioConf> damageMap = new HashMap<>();
    /**
     * 找不到配置时走默认配置，都是轻伤
     */
    private DamageRatioConf defaultDamageRatioConf;
    /**
     * type1->type2->缺省的战斗场景类型枚举，现在type1 vs type2基本都是同一个场景下
     */
    private final Map<SceneObjType, Map<SceneObjType, BattleType>> defaultBattleTypeMap = Maps.newEnumMap(SceneObjType.class);


    public BattleTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        ConstBattleTemplate template = getResHolder().getConstTemplate(ConstBattleTemplate.class);
        Collection<DamageRatioTemplate> list = getResHolder().getListFromMap(DamageRatioTemplate.class);
        for (DamageRatioTemplate data : list) {
            DamageRatioConf left = new DamageRatioConf(data.getLeftSevereWoundRatio(), data.getLeftDeadRatio());
            long leftKey = genLongKey(data.getDamageRatioType(), data.getBattleType(), data.getLeftObjType(), data.getRatioType());
            damageMap.put(leftKey, left);
            DamageRatioConf right = new DamageRatioConf(data.getRightSevereWoundRatio(), data.getRightDeadRatio());
            long rightKey = genLongKey(data.getDamageRatioType(), data.getBattleType(), data.getRightObjType(), data.getRatioType());
            damageMap.put(rightKey, right);

            SceneObjType leftType = data.getLeftObjType();
            SceneObjType rightType = data.getRightObjType();
            BattleType battleType = data.getBattleType();
            defaultBattleTypeMap.computeIfAbsent(leftType, it -> Maps.newEnumMap(SceneObjType.class))
                    .computeIfAbsent(rightType, it -> battleType);
            defaultBattleTypeMap.computeIfAbsent(rightType, it -> Maps.newEnumMap(SceneObjType.class))
                    .computeIfAbsent(leftType, it -> battleType);
        }
        List<Float> array = template.getDefaultWoundParam();
        defaultDamageRatioConf = new DamageRatioConf(array.get(0), array.get(1));
    }

    @Override
    public void checkValid() throws ResourceException {
        for (DamageRatioConf drc : damageMap.values()) {
            if (drc.getSevereWoundRatio() > 1 || drc.getSevereWoundRatio() < 0) {
                throw new ResourceException(StringUtils.format("战损比例不合理，重伤:{}", drc.getSevereWoundRatio()));
            }
            if (drc.getDeadInSevereRatio() > 1 || drc.getDeadInSevereRatio() < 0) {
                throw new ResourceException(StringUtils.format("战损比例不合理，死亡:{}", drc.getDeadInSevereRatio()));
            }
        }
        buffTemplateCheck();
        battleBuffTemplateCheck();
        checkBuffAndAdditionSource();
        checkDotBuff();
    }

    private void checkDotBuff() throws ResourceException {
        Collection<BattleBuffTemplate> list = getResHolder().getListFromMap(BattleBuffTemplate.class);
        Set<Integer> dotEffect = new HashSet<>();
        for (BattleBuffTemplate buffTemplate : list) {
            List<Integer> addSkillEffectList = buffTemplate.getAddSkillEffectList();
            if (addSkillEffectList.size() == 0) {
                continue;
            }
            for (int effectId : addSkillEffectList) {
                SkillEffectTemplate valueFromMap = getResHolder().getValueFromMap(SkillEffectTemplate.class, effectId);
                if (valueFromMap.getType() != CommonEnum.SkillEffectType.SET_DAMAGE) {
                    continue;
                }
                if (valueFromMap.getValue2() != BattleConstants.BATTLE_DAMAGE_WITH_DOT) {
                    continue;
                }
                dotEffect.add(effectId);
            }
        }
        for (SkillEffectTemplate effectTemplate : getResHolder().getListFromMap(SkillEffectTemplate.class)) {
            if (effectTemplate.getType() != CommonEnum.SkillEffectType.SET_DAMAGE) {
                continue;
            }
            if (effectTemplate.getValue2() != BattleConstants.BATTLE_DAMAGE_WITH_DOT) {
                continue;
            }
            if (!dotEffect.contains(effectTemplate.getId())) {
                throw new ResourceException("效果表配置了dot，但是战斗增益表未配置 buffId={} effectId={}", effectTemplate.getId());
            }
        }
    }

    private void checkBuffAndAdditionSource() throws ResourceException {
        for (CommonEnum.DevBuffSourceType value : CommonEnum.DevBuffSourceType.values()) {
            if (value == CommonEnum.DevBuffSourceType.DBST_NONE) {
                continue;
            }
            if (!DevBuffUtil.DEV_BUFF_2_ADDITION_SOURCE.containsKey(value)) {
                throw new ResourceException(StringUtils.format("DEV_BUFF_2_ADDITION_SOURCE buff来源未注册对应的加成来源，DevBuffSourceType:{}", value));
            }
        }
        Map<CommonEnum.AdditionSourceType, Integer> countCheck = Maps.newHashMap();
        for (CommonEnum.AdditionSourceType value : DevBuffUtil.DEV_BUFF_2_ADDITION_SOURCE.values()) {
            countCheck.put(value, countCheck.getOrDefault(value, 0) + 1);
        }
        List<Map.Entry<CommonEnum.AdditionSourceType, Integer>> collect = countCheck.entrySet().stream().filter(it -> it.getValue() > 1).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            throw new ResourceException(StringUtils.format("DEV_BUFF_2_ADDITION_SOURCE 不同buff来源不能对应同一个加成来源 AdditionSourceType:{}", collect));
        }
    }

    private void buffTemplateCheck() throws ResourceException {
        List<BuffTemplate> invalidBuff = new ArrayList<>();
        for (Map.Entry<Integer, BuffTemplate> entry : getResHolder().getMap(BuffTemplate.class).entrySet()) {
            BuffTemplate template = entry.getValue();
            CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(template.getType());
            if (buffEffectType == null) {
                invalidBuff.add(template);
            }
            if (template.getMerge() && template.getLifeCycleValue() > 0) {
                throw new ResourceException("增益表：可叠加的buff目前是不支持配置持续时间的，麻烦确认一下！buffId={}", template.getId());
            }
        }
        if (!invalidBuff.isEmpty()) {
            throw new ResourceException(StringUtils.format("增益表：这些buffEffect没实现过呀，别配。buffId:{}, effectType:{}",
                    invalidBuff.stream().map(BuffTemplate::getId).collect(Collectors.toList()),
                    invalidBuff.stream().map(BuffTemplate::getType).collect(Collectors.toSet())));
        }
    }

    private void battleBuffTemplateCheck() throws ResourceException {
        List<BattleBuffTemplate> invalidBuff = new ArrayList<>();
        for (Map.Entry<Integer, BattleBuffTemplate> entry : getResHolder().getMap(BattleBuffTemplate.class).entrySet()) {
            CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(entry.getValue().getType());
            if (buffEffectType == null) {
                invalidBuff.add(entry.getValue());
            }
        }
        if (!invalidBuff.isEmpty()) {
            throw new ResourceException(StringUtils.format("战斗增益表：这些buffEffect没实现过呀，别配。buffId:{}, effectType:{}",
                    invalidBuff.stream().map(BattleBuffTemplate::getId).collect(Collectors.toList()),
                    invalidBuff.stream().map(BattleBuffTemplate::getType).collect(Collectors.toSet())));
        }

        for (Map.Entry<Integer, BattleBuffTemplate> entry : getResHolder().getMap(BattleBuffTemplate.class).entrySet()) {
            for (Integer effectId : entry.getValue().getAddSkillEffectList()) {
                if (getResHolder().findValueFromMap(SkillEffectTemplate.class, effectId) == null) {
                    throw new ResourceException(StringUtils.format("战斗增益表：配置了不存在的effectId，buffId:{}, effectId:{}",
                            entry.getKey(), effectId));
                }
            }
        }
        for (Map.Entry<Integer, BattleBuffTemplate> entry : getResHolder().getMap(BattleBuffTemplate.class).entrySet()) {
            if (entry.getValue().getRuleValue() <= 0) {
                throw new ResourceException(StringUtils.format("战斗增益表: 叠加层数配置错误 buffId:{}, ruleValue:{}",
                        entry.getKey(), entry.getValue().getRuleValue()));
            }
        }
        for (Map.Entry<Integer, BattleBuffTemplate> entry : getResHolder().getMap(BattleBuffTemplate.class).entrySet()) {
            CommonEnum.LifeCycleType type = CommonEnum.LifeCycleType.forNumber(entry.getValue().getLifeCycleType());
            if (type == null) {
                throw new ResourceException(StringUtils.format("战斗增益表: 生效类型配置错误 buffId:{}, lifeCycleType:{}",
                        entry.getKey(), entry.getValue().getLifeCycleType()));
            }
        }
    }

    public DamageRatioConf getDamageRatioTemplate(
            int mapTemplateId,
            BattleType battleType,
            SceneObjType objType,
            CommonEnum.DamageRatioTypeEnum ratioTypeEnum
    ) {
        int damageRatioType = ResHolder.getTemplate(MapConfigTemplate.class, mapTemplateId).getDamageRatioType();
        long key = genLongKey(damageRatioType, battleType, objType, ratioTypeEnum);
        DamageRatioConf damageRatioConf = damageMap.get(key);
        if (damageRatioConf == null) {
            WechatLog.error("DamageRatioTemplate not found,key:{} mapTemplateId:{} damageRatioType:{} battleType{} objType:{} ratioTypeEnum:{}",
                    key, mapTemplateId, damageRatioType, battleType, objType, ratioTypeEnum);
            return getDefaultDamageRatioConf();
        }
        return damageRatioConf;
    }

    public DamageRatioConf getDefaultDamageRatioConf() {
        return defaultDamageRatioConf;
    }

    public BattleType defaultBattleType(SceneObjType type1, SceneObjType type2) {
        return defaultBattleTypeMap.getOrDefault(type1, Collections.emptyMap())
                .get(type2);
    }

    /**
     * 4个参数生成一个key
     */
    private static long genLongKey(int damageRatioType, BattleType battleType, SceneObjType objType, CommonEnum.DamageRatioTypeEnum ratioTypeEnum) {
        return (long) damageRatioType << MAP_TYPE_KEY_OFFSET
                | (long) battleType.getNumber() << BATTLE_TYPE_KEY_OFFSET
                | (long) objType.getNumber() << RATIO_TYPE_OFFSET
                | ratioTypeEnum.getNumber();
    }

    @Nullable
    public static BattleType defaultBattleTypeWith(SceneObjType myType, SceneObjType targetType) {
        BattleTemplateService battleTemplateService = ResHolder.getResService(BattleTemplateService.class);
        BattleType battleType = battleTemplateService.defaultBattleType(myType, targetType);
        if (battleType == null) {
            WechatLog.error("尝试建立未配置的战斗关系: {}-{}", myType, targetType);
            return null;
        }
        return battleType;
    }
}

package com.yorha.common.actor.cluster.node;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.NodeService;
import com.yorha.common.actor.NodeServices;
import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.cluster.ClusterHelper;
import com.yorha.common.actor.cluster.toplogy.Cluster;
import com.yorha.common.actor.cluster.toplogy.ClusterEvent;
import com.yorha.common.actor.cluster.toplogy.Node;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.msg.FutureActorRunnable;
import com.yorha.common.actor.node.*;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.etcd.IWatchHandler;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.SceneDbHelper;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.ZoneContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.GeminiThreadUtils;
import com.yorha.common.utils.GeminiThreadUtils.ThreadCpuStatInfo;
import com.yorha.common.utils.OffsetUtils;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.YamlUtils;
import com.yorha.common.utils.time.GeminiStopWatch;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.game.gen.prop.ZoneInfoProp;
import com.yorha.game.gen.prop.ZoneSideProp;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.TcaplusDb;
import com.yorha.proto.Zone;
import com.yorha.proto.Zoneside;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 每个进程一个，配合LeaderActor完成集群初始化。
 * Node得状态是纯Leader驱动，无自身状态机。
 * 禁止写业务逻辑
 * 需要的话写在GameServer的onNodeInitOk
 *
 * <AUTHOR>
 */
public class NodeActor extends GameActorWithCall implements NodeServices {
    private static final Logger LOGGER = LogManager.getLogger(NodeActor.class);

    private final NodeServiceImpl service;
    private final ThreadMXBean tmxb = ManagementFactory.getThreadMXBean();
    private Map<Long, GeminiThreadUtils.ThreadCpuStatInfo> threadCpuStatInfoMap;
    private Map<String, GeminiThreadUtils.ThreadCpuStatInfo> systemCpuStatInfoMap;
    private final LinkedBlockingQueue<ClusterEvent> events;

    public NodeActor(ActorSystem system, IActorRef self) {
        // id --> busId
        super(system, self);
        this.service = new NodeServiceImpl(this);
        this.threadCpuStatInfoMap = Collections.emptyMap();
        this.systemCpuStatInfoMap = Collections.emptyMap();
        this.events = new LinkedBlockingQueue<>();
    }

    @Nullable
    public ActorTimer addRepeatTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(getId(), timerReasonType, runnable, initialDelay, period, unit, false);
    }

    /**
     * 初始化进程
     */
    public boolean initAndStartNode() {
        // 获取offset偏移量
        Pair<Long, Integer> offsetFromDb = OffsetUtils.getOffsetFromDb(this);
        SystemClock.updateOffset(offsetFromDb.getFirst(), true);
        // 进入集群
        this.joinCluster();
        // 监听拓扑事件
        this.watchClusterEvents(this.events);
        // 立刻刷新一次保证集群拓扑信息及时更新
        updateClusterTopology();

        // 开始拉起actor
        try {
            // start monitor
            final FutureActorRunnable<IMonitorActor, Boolean> initMonitor = new FutureActorRunnable<>("initMonitor", IMonitorActor::init);
            tellAndCreate(RefFactory.ofLocalMonitor(), initMonitor);
            boolean isInitOk = initMonitor.get(ServerContext.getRpcTimeout() * 2L, TimeUnit.MILLISECONDS);
            if (!isInitOk) {
                throw new GeminiException("initMonitor failed");
            }

            // 起服流程
            if (ServerContext.isZoneServer()) {
                startZone();
            }
            // start global
            if (ServerContext.isGlobalServer()) {
                startGlobal();
            }
        } catch (Exception e) {
            LOGGER.error("initNode failed ", e);
            return false;
        }

        // 开始node分析监控等
        startNodeTimer();
        return true;
    }

    private void startZone() throws TimeoutException {
        if (ClusterConfigUtils.getZoneConfigOrNull(this.getZoneId()) == null) {
            throw new GeminiException("Zone Config Not Exits! ZoneId {}", this.getZoneId());
        }
        boolean isInitOk;
        GeminiStopWatch stopWatch = new GeminiStopWatch("start zone");
        // init name
        final FutureActorRunnable<INameActor, Boolean> initName = new FutureActorRunnable<>("initName", INameActor::init);
        tell(RefFactory.ofName(getZoneId()), initName);
        isInitOk = initName.get(ServerContext.getRpcTimeout(), TimeUnit.MILLISECONDS);
        if (!isInitOk) {
            throw new GeminiException("initName failed");
        }
        stopWatch.mark("initName");
        // init bigScene
        loadBigScene(stopWatch);
        // 本地拉起Gate
        final FutureActorRunnable<IGateActor, Boolean> initGate = new FutureActorRunnable<>("initGate", IGateActor::init);
        tellAndCreate(RefFactory.ofGate(getZoneId()), initGate);
        isInitOk = initGate.get(ServerContext.getRpcTimeout() * 2L, TimeUnit.MILLISECONDS);
        if (!isInitOk) {
            throw new GeminiException("initGate failed");
        }
        stopWatch.mark("initGate");
        final String stat = StringUtils.format("start zone cost: {}", stopWatch.stat());
        LOGGER.info(stat);
        // k8s, 打印在控制台
        System.out.println(stat);
    }

    private void loadBigScene(GeminiStopWatch stopWatch) throws TimeoutException {
        // 必须有初始开服时间，否则各模块有出现未知错误的风险
        long openZoneTime = ClusterConfigUtils.getZoneConfig(getZoneId()).getLongItem("open_zone_time");
        if (openZoneTime <= 0) {
            throw new GeminiException("must have openZoneTime");
        }
        // load zone
        Pair<TcaplusDb.ZoneTable.Builder, Boolean> pair = SceneDbHelper.loadAndCreateZoneFromDb(this, getZoneId(), openZoneTime);
        TcaplusDb.ZoneTable.Builder dbRet = pair.getFirst();
        final Zone.ZoneInfoEntity zoneChanged = dbRet.getChangedAttr();
        final ZoneInfoProp zoneProp = ZoneInfoProp.of(dbRet.getFullAttr(), zoneChanged);
        final boolean isFirstCreate = pair.getSecond();
        if (zoneProp.getIsOpened()) {
            ZoneContext.setServerOpenTsMs(zoneProp.getServerOpenTsMs());
        } else {
            // 没开服可能暗改开服时间
            ZoneContext.setServerOpenTsMs(openZoneTime);
        }
        stopWatch.mark("loadZone");
        // init zoneRank
        final FutureActorRunnable<IRankActor, Boolean> initRank = new FutureActorRunnable<>("initRank", (actor) -> actor.init(isFirstCreate));
        tell(RefFactory.ofRank(getZoneId()), initRank);
        boolean isInitOk = initRank.get(10, TimeUnit.SECONDS);
        if (!isInitOk) {
            throw new GeminiException("initRank failed");
        }
        stopWatch.mark("initRank");

        // init chat
        FutureActorRunnable<IZoneActor, Boolean> initZone = new FutureActorRunnable<>("initZoneChat", IZoneActor::init);
        tellAndCreate(RefFactory.ofZoneChat(getZoneId()), initZone);
        isInitOk = initZone.get(10, TimeUnit.SECONDS);
        if (!isInitOk) {
            throw new GeminiException("initZone fail");
        }
        stopWatch.mark("initZone");

        // init scene
        FutureActorRunnable<ISceneActor, Boolean> initScene;
        if (isFirstCreate) {
            initScene = new FutureActorRunnable<>("initBigScene",
                    (actor) -> actor.initBigScene(true, zoneProp, zoneChanged,
                            new ZoneSideProp(), Zoneside.ZoneSideEntity.getDefaultInstance(),
                            Collections.emptyList(), Collections.emptyList(),
                            Collections.emptyList(), Collections.emptyList()));
        } else {
            final Zoneside.ZoneSideEntity changedSideProp = dbRet.getZoneSideChangedAttr();
            final ZoneSideProp sideProp = ZoneSideProp.of(dbRet.getZoneSideFullAttr(), dbRet.getZoneSideChangedAttr());
            // 捞数据了
            List<ValueWithVersion<TcaplusDb.ClanTable.Builder>> clans = SceneDbHelper.loadClanFromDb(this, getZoneId());
            stopWatch.mark("loadClan");
            List<ValueWithVersion<TcaplusDb.ScenePlayerTable.Builder>> players = SceneDbHelper.loadScenePlayerFromDb(this, getZoneId());
            stopWatch.mark("loadScenePlayer");
            List<ValueWithVersion<TcaplusDb.SceneObjTable.Builder>> objs = SceneDbHelper.loadSceneObjFromDb(this, getZoneId());
            stopWatch.mark("loadObj");
            List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> mails = SceneDbHelper.loadMailsFromDb(this, getZoneId());
            stopWatch.mark("loadMail");
            initScene = new FutureActorRunnable<>("initBigScene",
                    (actor) -> actor.initBigScene(false, zoneProp, zoneChanged, sideProp, changedSideProp, clans, players, objs, mails));
        }
        tell(RefFactory.ofBigScene(getZoneId()), initScene);
        isInitOk = initScene.get(120, TimeUnit.SECONDS);
        if (!isInitOk) {
            throw new GeminiException("initBigScene fail");
        }
        stopWatch.mark("initBigScene");
    }

    private void startGlobal() {
        ActorSendMsgUtils.sendAndCreate(RefFactory.ofLocalZoneCard(), new ActorRunnable<>("zone_card_actor_init", IZoneCardActor::start));
    }

    /**
     * 初始化分析监控
     */
    private void startNodeTimer() {
        // 打印系统性能日志
        this.addRepeatTimer(TimerReasonType.ACTOR_PERF_STAT, () -> this.system().printPerfLog(), 1, 1, TimeUnit.MINUTES);
        // 打印CPU性能分析
        // CPU日志分析
        this.addRepeatTimer(TimerReasonType.NODE_PERF_STAT, this::printCpuLog, 1, 1, TimeUnit.MINUTES);
        // 更新集群拓扑
        this.addRepeatTimer(TimerReasonType.TOPOLOGY_EVENT_REFRESH, this::updateClusterTopology, 1, 1, TimeUnit.SECONDS);
    }

    private void printCpuLog() {
        // 打印cpu
        final var newThreadCpuStatInfoMap = GeminiThreadUtils.collectUserThreadCpuUsage(this.tmxb, this.threadCpuStatInfoMap);
        final var newSystemThreadCpuStatInfoMap = GeminiThreadUtils.collectSystemThreadCpuUsage(this.tmxb, this.systemCpuStatInfoMap);
        // 存活线程添加label
        for (final ThreadCpuStatInfo v : newThreadCpuStatInfoMap.values()) {
            if (v.getThreadCpuUsage() < 0.3) {
                continue;
            }
            LOGGER.info("thread_cpu_perf user thread:{} id:{} cpu:{}", v.getThreadName(), v.getThreadId(), v.getThreadCpuUsage());
        }
        for (final ThreadCpuStatInfo v : newSystemThreadCpuStatInfoMap.values()) {
            if (v.getThreadCpuUsage() < 0.3) {
                continue;
            }
            LOGGER.info("thread_cpu_perf system thread:{} id:{} cpu:{}", v.getThreadName(), v.getThreadId(), v.getThreadCpuUsage());
        }
        // 更新上一次collect的快照
        this.threadCpuStatInfoMap = newThreadCpuStatInfoMap;
        this.systemCpuStatInfoMap = newSystemThreadCpuStatInfoMap;

        int threadNum = threadCpuStatInfoMap.size() + systemCpuStatInfoMap.size();
        if (threadNum > MonitorConstant.THREAD_NUM_OVER_NUM) {
            WechatLog.error("thread num is over {} cur={}", MonitorConstant.THREAD_NUM_OVER_NUM, threadNum);
        }
    }

    @Override
    protected void handleActorDestroyMsg(String reason) {
        super.handleActorDestroyMsg(reason);
        this.unWatchClusterEvents(this.events);
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    public NodeService getNodeService() {
        return this.service;
    }


    public String toYaml() {
        return YamlUtils.dumpToYaml(this.system().getRegistryValue().getLocalNode().toMap());
    }

    protected void joinCluster() {
        // 服务发现url
        final Node localNode = this.system().getRegistryValue().getLocalNode();
        // url以启服时间为后缀，避免同一个busId下多节点互相影响
        final String nodeTopologyUrl = ActorClusterUrlUtils.etcdNodeTopologyUrl(
                localNode.getBusId(),
                String.valueOf(localNode.getStartTsMs())
        );

        // 服务发现
        final long leaseId = ServerContext.getEtcdClient().putWithAliveLease(nodeTopologyUrl, this.toYaml(), ClusterHelper.CLUSTER_NODE_LEASE_TTL_SEC_PROD);
        if (leaseId < 0) {
            throw new GeminiException("gemini_system putWithAliveLease {} fail", nodeTopologyUrl);
        }
    }

    protected void watchClusterEvents(final LinkedBlockingQueue<ClusterEvent> events) {
        // 监听集群的服务发现
        final boolean isOk = ServerContext.getEtcdClient().watchPrefix(ActorClusterUrlUtils.etcdNodeTopologyPrefix(), new IWatchHandler() {
            @Override
            public void onDelete(@NotNull String key) {
                final String busId = ActorClusterUrlUtils.getBusIdFromEtcdNodeTopology(key);
                final long startTsMs = ActorClusterUrlUtils.getStartTsMsFromEtcdNodeTopology(key);
                events.add(new ClusterEvent(busId, null, startTsMs));
            }

            @Override
            public void onUpdate(@NotNull String key, @NotNull String value) {
                final String busId = ActorClusterUrlUtils.getBusIdFromEtcdNodeTopology(key);
                final long startTsMs = ActorClusterUrlUtils.getStartTsMsFromEtcdNodeTopology(key);
                events.add(new ClusterEvent(busId, value, startTsMs));
            }
        });
        if (!isOk) {
            throw new GeminiException("gemini_system watchPrefix " + ActorClusterUrlUtils.etcdNodeTopologyPrefix() + " fail");
        }
    }

    protected void unWatchClusterEvents(final LinkedBlockingQueue<ClusterEvent> events) {
        // 取消监听集群节点服务发现
        ServerContext.getEtcdClient().unwatch(ActorClusterUrlUtils.etcdNodeTopologyPrefix(), true);

        // 删除已经存在的(key, value)对
        final Node localNode = this.system().getRegistryValue().getLocalNode();
        final String nodeTopologyUrl = ActorClusterUrlUtils.etcdNodeTopologyUrl(
                localNode.getBusId(),
                String.valueOf(localNode.getStartTsMs())
        );
        // 回收监听
        ServerContext.getEtcdClient().deleteSingle(nodeTopologyUrl);

        // 清除事件队列
        events.clear();
    }

    private void updateClusterTopology() {
        // 数据不变更
        if (this.events.isEmpty()) {
            return;
        }

        final Cluster curCluster = this.system().getRegistryValue().getCluster();
        final Cluster.Builder builder = curCluster.toBuilder();

        // 捞取事件
        ClusterEvent event;
        while ((event = this.events.poll()) != null) {
            try {
                final Node curNode = curCluster.getNode(event.getBusId());
                // 删除操作，删除节点
                if (event.isDelete()) {
                    // 当前无节点（可能在上一个node 租约过期的时候又拉起了新的节点）
                    if (curNode == null) {
                        LOGGER.error("gemini_system updateClusterTopology, delete event={} to replace null node", event);
                        continue;
                    }
                    // 时间戳不匹配（可能在上一个node 租约过期前又拉起了新的节点）可能是直接进程被杀掉，然后再手动拉起导致时间戳不匹配
                    if (curNode.getStartTsMs() != event.getStartTsMs()) {
                        LOGGER.error("gemini_system updateClusterTopology, delete event={}, node={} not match", event, curNode);
                        continue;
                    }
                    builder.removeNode(event.getBusId());
                    LOGGER.info("gemini_system updateClusterTopology delete event={}, node={}", event, curNode);
                    continue;
                }

                // 更新操作，当前节点的启动时间 > 新节点启动时间，告警，且过滤。同一busId启动了多个进程。
                if (curNode != null && curNode.getStartTsMs() > event.getStartTsMs()) {
                    WechatLog.error("gemini_system updateClusterTopology, update event={} to replace node={}", event, curNode);
                    continue;
                }

                // 构造新的节点
                final Map<String, Object> map = YamlUtils.newInstance(event.getValue());
                final Node node = Node.newBuilder()
                        .busId(event.getBusId())
                        .startTsMs(event.getStartTsMs())
                        .map(map)
                        .build();

                // 修改事件
                builder.addNode(node);
                LOGGER.info("gemini_system updateClusterTopology, update event={}, node={}, curNode={}", event, node, curNode);
            } catch (Exception e) {
                LOGGER.error("gemini_system updateClusterTopology, event={}, e=", event, e);
            }
        }

        // 刷新数据
        this.system().getRegistryValue().refreshCluster(builder.build());
    }


}

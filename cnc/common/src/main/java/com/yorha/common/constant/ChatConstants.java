package com.yorha.common.constant;

/**
 * 聊天相关常量
 *
 * <AUTHOR>
 */
public interface ChatConstants {
    /**
     * 全服聊天缓存页数
     */
    int CACHE_PAGE_SIZE = 10;
    /**
     * 单次从DB拉取聊天的条数
     */
    int BATCH_GET_MSG_SIZE = 50;

    int PAGE_MSG_SIZE = 20;

    /**
     * 系统消息id
     */
    // 加入军团
    int ENTER_CLAN_MSG = 1;
    // 离开军团
    int LEAVE_CLAN_MSG = 2;
    // 陌生人提醒
    int STRANGER_NOTIFY_MSG = 3;
    // 成为好友
    int BECOME_FRIEND_MSG = 4;
    // 加入群聊
    int JOIN_GROUP_MSG = 5;
    // 删除群成员
    int REMOVE_MEMBER_MSG = 6;
    // 离开群聊
    int LEAVE_GROUP_MSG = 7;
    // 转移群主
    int TRANSFER_GROUP_OWNER_MSG = 8;
    // 修改群名
    int MODIFY_GROUP_NAME_MSG = 9;
    // 发送相同内容
    int SEND_SAME_MSG = 10;
    // 被屏蔽
    int BE_SHIELD_MSG = 11;
    // 创建群聊
    int CREATE_GROUP_CHAT = 14;
}

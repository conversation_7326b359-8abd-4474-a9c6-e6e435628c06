package com.yorha.common.dbactor;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.result.UpdateResult;

/**
 * DB更新插入的策略。
 *
 * <AUTHOR>
 */
public interface IDbChangeUpsertStrategy {
    class Request {
        final Message.Builder req;
        final boolean isFull;
        final boolean isChanged;
        final GeneratedMessageV3 checkAttr;

        public Request(final Message.Builder req, final boolean isFull, final boolean isChanged, final GeneratedMessageV3 checkAttr) {
            this.req = req;
            this.isFull = isFull;
            this.isChanged = isChanged;
            this.checkAttr = checkAttr;
        }
    }

    /**
     * 收集脏数据。
     *
     * @param actor 目标actor。
     * @return 是否收集了脏数据(- 1 没有)。
     */
    int collectDirtyForSave(AbstractActor actor);

    /**
     * @return 脏数据id
     */
    int getDirtyId();

    /**
     * 构造Db更新请求。
     *
     * @param actor 目标actor。
     * @return db请求。
     */
    Request buildUpdateRequest(AbstractActor actor);

    /**
     * 构造Db插入请求。（必须为全量数据）
     *
     * @param actor 目标actor。
     * @return db请求。
     */
    Message.Builder buildInsertRequest(AbstractActor actor);

    /**
     * 清理脏数据。
     *
     * @param actor   目标actor。
     * @param dirtyId 脏标id
     */
    void clearDirtyWhenSaveOk(AbstractActor actor, int dirtyId);

    /**
     * 检查返回的数据。
     *
     * @param actor     容器actor。
     * @param result    db返回数据。
     * @param checkAttr 待校验数据。
     * @return 是否相同。
     */
    boolean checkDbResultWhenNeeded(AbstractActor actor, UpdateResult<Message.Builder> result, GeneratedMessageV3 checkAttr);
}

package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.UpdateOption;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.proto.CommonEnum;
import org.bson.Document;
import org.reactivestreams.Publisher;

/**
 * <AUTHOR>
 */
//TODO(JOSEFREN): OPTION
public class MongoUpdate<T extends Message.Builder> extends MongoOperation<T, UpdateOption, Document, Document, UpdateResult<T>> {
    /**
     * 获取更新后数据
     */
    static final FindOneAndUpdateOptions GET_LATEST_DATA = new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER);
    /**
     * 获取更新前数据
     */
    static final FindOneAndUpdateOptions GET_OLD_DATA = new FindOneAndUpdateOptions().returnDocument(ReturnDocument.BEFORE);

    public MongoUpdate(MongoDatabase database, T t, UpdateOption updateOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, updateOption);
    }

    @Override
    protected NormalSubscriber<Document> getSubscriber() {
        return new NormalSubscriber<>();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        var filter = DocumentHelper.formKey(this.getReq());
        var update = DocumentHelper.formUpdate(this.getReq());
        return this.database.getCollection(this.getTableName()).findOneAndUpdate(filter, update, formOption(this.getOption()));

    }

    private static FindOneAndUpdateOptions formOption(final UpdateOption updateOption) {
        final int resultFlag = updateOption.getResultFlag();
        if (resultFlag == CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL_OLD_VALUE) {
            return GET_OLD_DATA;
        }
        return GET_LATEST_DATA;
    }

    @Override
    protected com.yorha.common.db.tcaplus.result.UpdateResult<T> buildResult(Document document) {
        T proto = MongoOperation.buildDefaultValue(this.getReq());
        final UpdateResult<T> result = new UpdateResult<>();
        result.code = TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
        if (document != null) {
            this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
            result.code = TcaplusErrorCode.GEN_ERR_SUC;
        }

        result.value = proto;
        return result;
    }

    @Override
    protected UpdateResult<T> onMongoError() {
        final UpdateResult<T> result = new UpdateResult<>();
        result.code = DEFAULT_ERROR_CODE;
        result.value = MongoOperation.buildDefaultValue(this.getReq());
        return result;
    }
}
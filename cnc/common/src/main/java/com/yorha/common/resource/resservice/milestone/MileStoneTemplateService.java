package com.yorha.common.resource.resservice.milestone;

import com.google.common.collect.Lists;
import com.yorha.common.constant.Constants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import org.apache.commons.lang3.tuple.Triple;
import res.template.*;

import java.util.*;

/**
 * 里程碑
 *
 * <AUTHOR>
 */
public class MileStoneTemplateService extends AbstractResService {

    /**
     * 里程碑id to 奖励列表
     */
    private final Map<Integer, List<MileStoneRewardNode>> mileStoneReward = new HashMap<>();
    /**
     * 任务配置参数
     */
    private final Map<String, String[]> mileStoneTaskParam = new HashMap<>();
    /**
     * 任务配置参数（空间换时间版）
     */
    private final Map<String, MilestoneTasktemplateTemplate> mileStoneTaskParamStr = new HashMap<>();

    /**
     * 玩法配置参数
     */
    private final Map<Integer, String[]> mileStoneGameParam = new HashMap<>();

    /**
     * 日落峡谷玩法解锁章节(目前只有原服)
     */
    private int arenaUnlockMilestoneId;


    /**
     * 开始里程碑id对应迷雾解锁章节
     */
    private final Map<Integer, Integer> allFogUnlockMap = new HashMap<>();

    /**
     * 开始里程碑id对应已开放的章节配置
     */
    private final Map<Integer, List<MilestoneChapterTemplate>> openChapterTemplate = new HashMap<>();

    /**
     * <任务类型 <触发时间, 进度比率值-补充数值的百分比上限-补充数值的百分比下限>>
     */
    private final Map<Integer, TreeMap<Integer, Triple<Integer, Integer, Integer>>> fakeDataMap = new HashMap<>();

    private final List<CommonEnum.MileStoneTaskType> fakeDataEnumList = Lists.newArrayList(
            CommonEnum.MileStoneTaskType.MST_BASE_RATING,
            CommonEnum.MileStoneTaskType.MST_KILL_REBEL_NUM);

    public MileStoneTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    public int getRewardLevelByRank(int mileStoneId, int rankValue) {
        List<MileStoneRewardNode> mileStoneRewardNodes = mileStoneReward.get(mileStoneId);
        for (MileStoneRewardNode mileStoneRewardNode : mileStoneRewardNodes) {
            if (mileStoneRewardNode.isInRange(rankValue)) {
                return mileStoneRewardNode.getLevel();
            }
        }
        throw new GeminiException("rank no config. mileStoneId={} rank={}", mileStoneId, rankValue);
    }

    public CommonEnum.MileStoneTaskType getTaskTypeById(int mileStoneId) {
        int taskId = getResHolder().getValueFromMap(MilestoneChapterTemplate.class, mileStoneId).getTaskId();
        return getResHolder().getValueFromMap(MilestoneTaskTemplate.class, taskId).getMilestoneTaskTypeId();
    }

    /**
     * 根据等级获取奖励数据
     *
     * @param level 从1开始
     */
    public MileStoneRewardNode getRewardByLevel(int mileStoneId, int level) {
        if (level <= 0) {
            return null;
        }
        List<MileStoneRewardNode> mileStoneRewardNodes = mileStoneReward.get(mileStoneId);
        if (mileStoneRewardNodes.size() < level) {
            return null;
        }
        return mileStoneRewardNodes.get(level - 1);
    }

    public int getRewardIdByLevel(int mileStoneId, int level) {
        MileStoneRewardNode rewardByLevel = getRewardByLevel(mileStoneId, level);
        if (rewardByLevel == null) {
            return 0;
        }
        return rewardByLevel.getReward();
    }

    @Override
    public void load() throws ResourceException {
        // load原服的里程碑
        int mileStoneStarId = getResHolder().getConstTemplate(ConstTemplate.class).getMileStoneStarId();
        loadChapter(mileStoneStarId, mileStoneStarId);

        for (MilestoneTasktemplateTemplate taskTemplateTemplate : getResHolder().getListFromMap(MilestoneTasktemplateTemplate.class)) {
            String key = formatTaskId(taskTemplateTemplate.getTaskId(), taskTemplateTemplate.getMilestoneTemplateId());
            String taskConst = taskTemplateTemplate.getTaskConst();
            if (mileStoneTaskParam.containsKey(key)) {
                throw new ResourceException("里程碑任务参数配置重复. taskId={} templateId={}",
                        taskTemplateTemplate.getTaskId(), taskTemplateTemplate.getMilestoneTemplateId());
            }
            mileStoneTaskParam.put(key, taskConst.split(Constants.XIA_HUA_XIAN));
            mileStoneTaskParamStr.put(key, taskTemplateTemplate);
        }
    }

    private void loadChapter(int startId, int chapterId) throws ResourceException {
        MilestoneChapterTemplate milestoneChapterTemplate = getResHolder().getValueFromMap(MilestoneChapterTemplate.class, chapterId);
        List<MileStoneRewardNode> mileStoneRewardNodes = mileStoneReward.computeIfAbsent(milestoneChapterTemplate.getId(), (key) -> new ArrayList<>());
        MileStoneRewardNode preNode = null;
        int level = 0;
        for (IntPairType intPairType : milestoneChapterTemplate.getRewardIdPairList()) {
            level++;
            MileStoneRewardNode node = new MileStoneRewardNode(intPairType.getKey(), intPairType.getValue(), level);
            if (preNode != null) {
                preNode.setLowerLimit(intPairType.getKey());
            }
            preNode = node;
            mileStoneRewardNodes.add(node);
        }

        for (Integer gameId : milestoneChapterTemplate.getUnlockGameplayTypeList()) {
            MilestoneGameplayUnlockTemplate gamePlayerTemplate = getResHolder().findValueFromMap(MilestoneGameplayUnlockTemplate.class, gameId);
            if (gamePlayerTemplate == null) {
                throw new ResourceException("里程碑gameId={}找不到 里程碑开始章节startId={} chapterId={}", gameId, startId, chapterId);
            }
            if (StringUtils.isEmpty(gamePlayerTemplate.getUnlockGameplayConst())) {
                continue;
            }
            if (gamePlayerTemplate.getUnlockGameplayType() == CommonEnum.MileStoneFuncUnlockType.MSF_UNLOCK_SUNSET_CANYON) {
                if (arenaUnlockMilestoneId > 0) {
                    throw new ResourceException("里程碑日落峡谷解锁重复 里程碑开始章节startId={} chapterId={}", startId, chapterId);
                }
                arenaUnlockMilestoneId = milestoneChapterTemplate.getId();
                continue;
            }
            mileStoneGameParam.put(gamePlayerTemplate.getId(), gamePlayerTemplate.getUnlockGameplayConst().split(Constants.XIA_HUA_XIAN));
        }

        // chapter2task
        MilestoneTaskTemplate taskTemplate = getResHolder().findValueFromMap(MilestoneTaskTemplate.class, milestoneChapterTemplate.getTaskId());
        if (taskTemplate == null) {
            throw new ResourceException("里程碑任务不存在 里程碑开始章节startId={} chapterId={} taskId{}", startId, chapterId, milestoneChapterTemplate.getTaskId());
        }

        // startId2allChapter
        List<MilestoneChapterTemplate> start2AllChapter = openChapterTemplate.computeIfAbsent(startId, (key) -> new ArrayList<>());
        start2AllChapter.add(milestoneChapterTemplate);

        if (fakeDataEnumList.contains(taskTemplate.getMilestoneTaskTypeId())) {
            TreeMap<Integer, Triple<Integer, Integer, Integer>> defaultFakeMap = new TreeMap<>();
            try {
                int maxSec = milestoneChapterTemplate.getDuration();
                String[] stage = taskTemplate.getFakeDataConst().split(Constants.BAN_JIAO_FEN_HAO);
                for (String params : stage) {
                    String[] param = params.split(Constants.BAN_JIAO_DOU_HAO);
                    int triggerSec = (int) (Float.parseFloat(param[0]) * maxSec);
                    int processRatio = (int) (Constants.N_100 * Float.parseFloat(param[1]));
                    int preRatio = (int) (Constants.N_100 * Float.parseFloat(param[2]));
                    int postRatio = (int) (Constants.N_100 * Float.parseFloat(param[3]));
                    defaultFakeMap.put(triggerSec, Triple.of(processRatio, preRatio, postRatio));
                }
            } catch (Exception e) {
                throw new ResourceException("里程碑假数据配置错误. milestoneTaskId:{}", milestoneChapterTemplate.getTaskId());
            }
            fakeDataMap.put(milestoneChapterTemplate.getId(), defaultFakeMap);
        }

        // 开始递归了
        if (milestoneChapterTemplate.getNextChapterID() > 0) {
            loadChapter(startId, milestoneChapterTemplate.getNextChapterID());
        }
    }

    private String formatTaskId(int taskId, int milestoneTemplateId) {
        return taskId + "_" + milestoneTemplateId;
    }

    @Override
    public void checkValid() throws ResourceException {
        validateChapterTemplates();
        validateRewardNodes();
        validateGameplayParams();
        validateTaskParams();
        validateFakeDataConfig();
    }

    /**
     * 验证里程碑章节模板配置
     */
    private void validateChapterTemplates() throws ResourceException {
        for (MilestoneChapterTemplate milestoneChapterTemplate : getResHolder().getListFromMap(MilestoneChapterTemplate.class)) {
            if (milestoneChapterTemplate.getMailIdList().size() > 2) {
                throw new ResourceException("里程碑章节表邮件配置有问题。个数超出上限2");
            }
            if (milestoneChapterTemplate.getNextChapterID() > 0 && milestoneChapterTemplate.getNextChapterID() <= milestoneChapterTemplate.getId()) {
                throw new ResourceException("里程碑后置章节禁止小于当前章节, milestone={} nextMilestone={}",
                        milestoneChapterTemplate.getId(), milestoneChapterTemplate.getNextChapterID());
            }
        }
    }

    /**
     * 验证奖励节点配置
     */
    private void validateRewardNodes() throws ResourceException {
        for (Map.Entry<Integer, List<MileStoneRewardNode>> entry : mileStoneReward.entrySet()) {
            MileStoneRewardNode preNode = null;
            if (entry.getValue().size() > 1) {
                for (MileStoneRewardNode mileStoneRewardNode : entry.getValue()) {
                    if (preNode != null) {
                        if (preNode.getUpperLimit() >= preNode.getLowerLimit()) {
                            throw new ResourceException("里程碑章节表奖励配置有问题。配置非阶段递进。chapter={} reward={} lower={} upper={}",
                                    entry.getKey(), preNode.getReward(), preNode.getLowerLimit(), preNode.getUpperLimit());
                        }
                        if (mileStoneRewardNode.getUpperLimit() == preNode.getLowerLimit() + 1) {
                            throw new ResourceException("里程碑章节表奖励配置有问题。配置非阶段递进。前等级区间为{}->{}  后等级区间为{}->{}",
                                    preNode.getUpperLimit(), preNode.getLowerLimit(), mileStoneRewardNode.getUpperLimit(), mileStoneRewardNode.getLowerLimit());
                        }
                    }
                    preNode = mileStoneRewardNode;
                }
            }
        }
    }

    /**
     * 验证玩法参数配置
     */
    private void validateGameplayParams() throws ResourceException {
        for (Map.Entry<Integer, String[]> gameEntry : mileStoneGameParam.entrySet()) {
            String[] paramArray = gameEntry.getValue();
            MilestoneGameplayUnlockTemplate gameTemplate = getResHolder().getValueFromMap(MilestoneGameplayUnlockTemplate.class, gameEntry.getKey());
            int paramSizeLimit = getGameplayParamSizeLimit(gameTemplate, paramArray);

            if (paramSizeLimit > 0 && paramArray.length != paramSizeLimit) {
                throw new ResourceException("里程碑玩法解锁表参数配置有问题。玩法表id:{} 需要参数个数:{} 实际配置参数为:{}",
                        gameEntry.getKey(), paramSizeLimit, paramArray.length);
            }
        }
    }

    /**
     * 获取玩法参数大小限制并验证特殊逻辑
     */
    private int getGameplayParamSizeLimit(MilestoneGameplayUnlockTemplate gameTemplate, String[] paramArray) throws ResourceException {
        switch (gameTemplate.getUnlockGameplayType()) {
            case MSF_UNLOCK_PASS:
                return 2;
            case MSF_UNLOCK_MUTANT_CAMP:
                return 1;
            case MSF_UNLOCK_MUTANT_HOUSE:
                validateMutantHouseParam(paramArray);
                return 1;
            default:
                return 0; // 不需要验证参数个数
        }
    }

    /**
     * 验证变异人小屋参数
     */
    private void validateMutantHouseParam(String[] paramArray) throws ResourceException {
        boolean errorFlag = true;
        int monsterId = Integer.parseInt(paramArray[0]);
        for (BigSceneSpawnRegionTemplate bigSceneSpawnRegionTemplate : getResHolder().getListFromMap(BigSceneSpawnRegionTemplate.class)) {
            if (bigSceneSpawnRegionTemplate.getCategory() == CommonEnum.MonsterCategory.RALLY_MONSTER
                    && bigSceneSpawnRegionTemplate.getMilestone() == monsterId) {
                errorFlag = false;
                break;
            }
        }
        if (errorFlag) {
            throw new ResourceException("里程碑玩法解锁表参数配置有问题。变异人小屋id不存在:{}", monsterId);
        }
    }

    /**
     * 验证任务参数配置
     */
    private void validateTaskParams() throws ResourceException {
        for (Map.Entry<String, String[]> entry : mileStoneTaskParam.entrySet()) {
            int taskId = Integer.parseInt(entry.getKey().split(Constants.XIA_HUA_XIAN)[0]);
            MilestoneTaskTemplate taskTemplate = getResHolder().getValueFromMap(MilestoneTaskTemplate.class, taskId);
            int size = entry.getValue().length;
            int expectedSize = getExpectedTaskParamSize(taskTemplate.getMilestoneTaskTypeId());

            if (size != expectedSize) {
                throw new ResourceException("里程碑任务表参数配置有问题。type:{} 需要参数个数:{} 实际配置参数为:{} mileStoneId:{}",
                        taskTemplate.getMilestoneTaskTypeId(), expectedSize, entry.getValue(), entry.getKey());
            }
        }
    }

    /**
     * 获取任务参数期望大小
     */
    private int getExpectedTaskParamSize(CommonEnum.MileStoneTaskType taskType) throws ResourceException {
        switch (taskType) {
            case MST_PLAYER_KILL_OTHER_SOLDIER:
            case MST_RANKING_ALL_LEGIONS:
                return 1;
            case MST_CONDITIONAL_LEGION_SIZE:
            case MST_PLAYER_BUILD:
            case MST_BASE_RATING:
                return 2;
            case MST_DEFEAT_THE_RALLY_MONSTER:
            case MST_OCCUPY_BUILDING_NUM:
            case MST_OCCUPIED_BUILDINGS_NUM:
            case MST_BUILDINGS_BUILT_NUM:
            case MST_PLAYER_KILL_MONSTER:
            case MST_CLAN_BUILD:
            case MST_ZONE_OCCUPY_BUILD:
            case MST_KILL_REBEL_NUM:
                return 3;
            default:
                throw new ResourceException("里程碑任务表参数配置有问题。未知的任务类型:{} ", taskType);
        }
    }

    /**
     * 验证假数据配置
     */
    private void validateFakeDataConfig() throws ResourceException {
        for (MilestoneTaskTemplate milestoneTaskTemplate : getResHolder().getListFromMap(MilestoneTaskTemplate.class)) {
            if (!fakeDataEnumList.contains(milestoneTaskTemplate.getMilestoneTaskTypeId())) {
                if (StringUtils.isNotEmpty(milestoneTaskTemplate.getFakeDataConst())) {
                    throw new ResourceException("里程碑任务表假数据配置有问题。未开放假数据的任务类型 里程碑任务id:{} ",
                            milestoneTaskTemplate.getMilestoneTaskTypeId());
                }
                continue;
            }

            if (StringUtils.isEmpty(milestoneTaskTemplate.getFakeDataConst())) {
                throw new ResourceException("里程碑任务表假数据配置有问题。开放的假数据无配置内容 里程碑任务id:{} ",
                        milestoneTaskTemplate.getMilestoneTaskTypeId());
            }

            validateFakeDataParams(milestoneTaskTemplate);
        }
    }

    /**
     * 验证假数据参数格式
     */
    private void validateFakeDataParams(MilestoneTaskTemplate milestoneTaskTemplate) throws ResourceException {
        for (String params : milestoneTaskTemplate.getFakeDataConst().split(Constants.BAN_JIAO_FEN_HAO)) {
            String[] split = params.split(Constants.BAN_JIAO_DOU_HAO);
            if (split.length != 4) {
                throw new ResourceException("里程碑任务表假数据配置有问题。开放的假数据配置参数异常 里程碑任务id:{} 参数",
                        milestoneTaskTemplate.getMilestoneTaskTypeId(), milestoneTaskTemplate.getFakeDataConst());
            }
            if (Float.parseFloat(split[0]) > 1 || Float.parseFloat(split[0]) < 0) {
                throw new ResourceException("里程碑任务表假数据配置有问题。开放的假数据配置参数异常 参数1值大于1 taskId:{} param:{}",
                        milestoneTaskTemplate.getMilestoneTaskTypeId(), params);
            }
        }
    }

    public String[] getTaskParamById(int milestoneId, int templateId) {
        MilestoneChapterTemplate chapterTemplate = getResHolder().getValueFromMap(MilestoneChapterTemplate.class, milestoneId);
        MilestoneTaskTemplate taskTemplate = getResHolder().getValueFromMap(MilestoneTaskTemplate.class, chapterTemplate.getTaskId());
        return mileStoneTaskParam.get(formatTaskId(taskTemplate.getId(), templateId));
    }

    public String getTaskParamStrById(int milestoneId, int templateId) {
        MilestoneChapterTemplate chapterTemplate = getResHolder().getValueFromMap(MilestoneChapterTemplate.class, milestoneId);
        MilestoneTaskTemplate taskTemplate = getResHolder().getValueFromMap(MilestoneTaskTemplate.class, chapterTemplate.getTaskId());
        return mileStoneTaskParamStr.get(formatTaskId(taskTemplate.getId(), templateId)).getTaskConst();
    }

    public String[] getGameParamById(int gameId) {
        return mileStoneGameParam.get(gameId);
    }

    public List<MilestoneChapterTemplate> getChapterList(int mileStoneStarId) {
        return openChapterTemplate.getOrDefault(mileStoneStarId, new ArrayList<>());
    }

    public boolean isFakeProcessMilestone(CommonEnum.MileStoneTaskType milestoneTaskTypeId) {
        return fakeDataEnumList.contains(milestoneTaskTypeId);
    }

    public static class MileStoneRewardNode {
        // 例：[0~10)  upper:0 lower:10
        private final int upperLimit;
        private int lowerLimit = Integer.MAX_VALUE;
        // 奖励的配置id
        private final int reward;
        // 奖励等级 从1开始
        private final int level;

        public int getUpperLimit() {
            return upperLimit;
        }

        public int getLowerLimit() {
            return lowerLimit;
        }

        public MileStoneRewardNode(int upperLimit, int reward, int level) {
            this.upperLimit = upperLimit;
            this.reward = reward;
            this.level = level;
        }

        public void setLowerLimit(int lowerLimit) {
            this.lowerLimit = lowerLimit;
        }

        /**
         * 判断排行是否符合
         *
         * @param value rankValue
         */
        public boolean isInRange(int value) {
            return value < lowerLimit && value >= upperLimit;
        }

        public int getReward() {
            return reward;
        }

        public int getLevel() {
            return level;
        }
    }

    public int getAllFogUnlockMilestoneId(int startId) {
        return allFogUnlockMap.getOrDefault(startId, 0);
    }

    /**
     * 根据里程碑任务类型和当前进度比率返回一个配置的随机比率
     *
     * @param passSec 里程碑任务已经开启的时间秒
     * @return 需要补充的百分比 0:无值
     */
    public Integer getPreRandomFakeRatio(int milestoneId, int passSec, int curProcessRatio100) {
        if (passSec < 0) {
            WechatLog.error("getPreRandomFakeRatio param error. passSec:{}", passSec);
            return 0;
        }
        TreeMap<Integer, Triple<Integer, Integer, Integer>> ratioMap = fakeDataMap.get(milestoneId);
        if (ratioMap == null) {
            WechatLog.error("disable fake process. milestoneId:{} passSec:{} fakeDataMapKey:{}", milestoneId, passSec, fakeDataMap.keySet());
            return 0;
        }
        Map.Entry<Integer, Triple<Integer, Integer, Integer>> ratio = ratioMap.floorEntry(passSec);
        // 没有匹配项时代表不需要
        if (ratio == null || ratio.getValue() == null) {
            return 0;
        }
        // 大于配置不需要补哈
        if (curProcessRatio100 >= ratio.getValue().getLeft()) {
            return 0;
        }
        int pre100 = ratio.getValue().getMiddle();
        int post100 = ratio.getValue().getRight();
        int fakeRatio100 = RandomUtils.randomBetween(pre100, post100);

        return (ratio.getValue().getLeft() - curProcessRatio100) * fakeRatio100 / Constants.N_100;
    }

    /**
     * 获取下次触发的秒数
     */
    public Integer getNextScheduleTriggerSec(int milestoneId, int passSec) {
        if (passSec < 0) {
            WechatLog.error("getNextScheduleTriggerSec param error. passSec:{}", passSec);
            return 0;
        }
        TreeMap<Integer, Triple<Integer, Integer, Integer>> ratioMap = fakeDataMap.get(milestoneId);
        if (ratioMap == null) {
            WechatLog.error("disable fake process. milestoneId:{} passSec:{} fakeDataMapKey:{}", milestoneId, passSec, fakeDataMap.keySet());
            return 0;
        }
        // 没有匹配项时代表不需要(通过+1保证一定取到比给定key大的第一个key值)
        Map.Entry<Integer, Triple<Integer, Integer, Integer>> ratio = ratioMap.ceilingEntry(passSec + 1);
        if (ratio == null || ratio.getKey() == null) {
            return 0;
        }
        return ratio.getKey() - passSec;
    }
}

package com.yorha.common.utils.json;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import org.dom4j.DocumentException;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Map;

/**
 * some description
 *
 * <AUTHOR>
 */
public class JsonUtils {
    private static final Gson GSON =  new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();;

    /**
     * 从xml文件中解析JSON对象
     *
     * @param fileName xml文件名称
     * @return json对象
     * @throws DocumentException 异常
     */
    public static JsonObject loadFromXmlFile(String fileName) throws DocumentException {
//        JsonObject ret = new JsonObject();
//        Document document = DocumentUtil.loadResourceDocument(fileName);
//        Element root = document.getRootElement();
//        JsonXmlUtil.xml2Json(root, ret);
//        return ret;
        return null;
    }

    /**
     * 转换object为json字符串
     *
     * @param o 目标对象
     * @return json字符串
     */
    public static String toJsonString(Object o) {
        return GSON.toJson(o);
    }

    /**
     * 解析出特定的对象
     *
     * @param json     json字符串
     * @param classOfT 对象类型的class对象
     * @param <T>      对象类型
     * @return 对象
     * @throws JsonSyntaxException 解析异常
     */
    public static <T> T parseObject(String json, Class<T> classOfT) throws JsonSyntaxException {
        return GSON.fromJson(json, classOfT);
    }

    /**
     * 解析出特定的对象
     *
     * @param map      数据字典
     * @param classOfT 对象类型的class对象
     * @param <T>      对象类型
     * @return 对象
     * @throws JsonSyntaxException 解析异常
     */
    public static <T> T parseObject(Map<String, String> map, Class<T> classOfT) throws JsonSyntaxException {
        return GSON.fromJson(GSON.toJson(map), classOfT);
    }

    /**
     * 解析JSON对象
     *
     * @param jsonString json字符串
     * @return json对象
     * @throws JsonSyntaxException 解析异常
     */
    public static JsonObject parseObject(String jsonString) throws JsonSyntaxException {
        return new JsonParser().parse(jsonString).getAsJsonObject();
    }

    public static <T> T parseObject(JsonElement json, Class<T> classOfT) throws JsonSyntaxException {
        return GSON.fromJson(json, classOfT);
    }

    /**
     * 解析数组
     *
     * @param json  内容
     * @param clazz array对象
     * @param <T>   类型
     * @return T类型数组
     */
    public static <T> ArrayList<T> parseArray(String json, Class<T> clazz) {
        ArrayList<T> ret = new ArrayList<>();
        Type type = new TypeToken<ArrayList<JsonObject>>() {
        }.getType();
        ArrayList<JsonObject> jsonObjects = GSON.fromJson(json, type);
        for (JsonObject jsonObject : jsonObjects) {
            ret.add(GSON.fromJson(jsonObject, clazz));
        }
        return ret;
    }
}
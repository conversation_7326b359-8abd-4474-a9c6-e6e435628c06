package com.yorha.common.notification;

/**
 * <AUTHOR>
 */
public class Notification {
    /**
     * 推送项Id
     */
    private final int notificationId;
    /**
     * 推送类型
     */
    private final int settingId;
    /**
     * 推送类型对应的设置所在mask的bit位
     */
    private final int pushNtfModelId;
    /**
     * 推送标题
     */
    private final int titleId;
    /**
     * 推送内容
     */
    private final int bodyId;
    /**
     * 冷却类型
     */
    private final int coldId;

    public Notification(final int notificationId, final int settingId, final int modelId, final int titleId, final int bodyId, final int coldId) {
        this.notificationId = notificationId;
        this.settingId = settingId;
        this.pushNtfModelId = modelId;
        this.titleId = titleId;
        this.bodyId = bodyId;
        this.coldId = coldId;
    }

    public int getPushNtfModelId() {
        return this.pushNtfModelId;
    }

    public int getTitleId() {
        return this.titleId;
    }

    public int getBodyId() {
        return this.bodyId;
    }

    public int getColdId() {
        return this.coldId;
    }

    @Override
    public String toString() {
        return "Notification{" +
                "notificationId=" + notificationId +
                ", settingId=" + settingId +
                ", pushNtfModelId=" + pushNtfModelId +
                ", titleId=" + titleId +
                ", bodyId=" + bodyId +
                ", coldId=" + coldId +
                '}';
    }
}

package com.yorha.common.actorservice.msg;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.utils.ClassNameCacheUtils;
import org.apache.logging.log4j.Logger;

/**
 * actor通信中的util
 *
 * <AUTHOR>
 */
public class ActorMsgUtil {

    public static void debugLogSendMsg(IActorRef sender, Logger logger, ActorMsgEnvelope envelope, IActorRef target) {
        if (logger.isDebugEnabled()) {
            logger.debug("{} x send {} to {}", sender, envelope, target);
        }
    }

    public static void debugLogRecvMsg(IActorRef actor, Logger logger, ActorMsgEnvelope msg) {
        if (!logger.isDebugEnabled()) {
            return;
        }
        logger.debug("{} x recv {}", actor, ActorMsgUtil.debugLogMsgToString(msg));
    }

    public static String debugLogMsgToString(Object msg) {
        return msg.toString();
    }

    public static String profNameOf(Object msg) {
        if (msg == null) {
            return "null";
        } else if (msg instanceof ActorMsgEnvelope) {
            return ((ActorMsgEnvelope) msg).getSimpleName();
        } else if (msg instanceof IActorMsg) {
            return ((IActorMsg) msg).profName();
        }
        return ClassNameCacheUtils.getSimpleName(msg.getClass());
    }

    /**
     * 基于nats的网络通信的key值。（用于sub）
     *
     * @param worldId world id
     * @param busId   bus id
     * @return key
     */
    public static String concatNatsTopic4sub(int worldId, String busId) {
        return "actor." + worldId + "." + busId;
    }

    /**
     * 基于nats的网络通信key。（用于pub）
     *
     * @param worldId world id
     * @param busId   bus id
     * @return key
     */
    public static String concatNatsTopic4pub(int worldId, String busId) {
        return "actor." + worldId + "." + busId;
    }

}

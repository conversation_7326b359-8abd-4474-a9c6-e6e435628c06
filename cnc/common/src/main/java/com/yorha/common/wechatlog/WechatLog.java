package com.yorha.common.wechatlog;


import com.google.common.collect.Maps;
import com.yorha.common.concurrent.NamedRunnable;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.PlatformClient;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.schedule.SystemScheduleMgr;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.message.Message;
import org.apache.logging.log4j.message.ParameterizedMessage;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 用来发送wechat日志的工具
 *
 * <AUTHOR>
 */
public class WechatLog {
    private static final Logger LOGGER = LogManager.getLogger(WechatLog.class);
    private static final String WECHAT_URL_PREFIX = "/cgi-bin/webhook/send?key=";

    private static final String SERVER_KEY = "wechat_server_err_key";
    private static final String DESIGNER_KEY = "wechat_designer_err_key";

    private static class LazyHolder {
        private static final WechatLog INSTANCE = new WechatLog();
    }

    public static Map<String, LogItem> cache = Maps.newConcurrentMap();

    public static WechatLog getInstance() {
        return LazyHolder.INSTANCE;
    }

    private final int[] limitByLevel = {1, 2, 3, 10, 20, 30, 50, 100, 10000, 1_000_000_000};

    private Supplier<WechatConfig> serverConfigSupplier;

    /**
     * 发送客户端及发送线程
     */
    private ThreadPoolExecutor executorService;
    PlatformClient platformClient;

    private ScheduledFuture<?> scheduledFuture;

    private static long screenByGmTsMs = 0;

    private static String ipAddress;

    private static String branch;

    private WechatLog() {
    }

    public void init(Supplier<WechatConfig> serverConfigSupplier) {
        if (this.serverConfigSupplier != null) {
            LOGGER.error("WechatLog serverConfigSupplier is already exist");
            return;
        }
        this.serverConfigSupplier = serverConfigSupplier;
        this.platformClient = new WechatPlatformClient(5000);
        this.executorService = ConcurrentHelper.newSingleThreadExecutor("wechat-log", 0, true);
        if (scheduledFuture != null) {
            LOGGER.warn("repeat init wechatLog. configBusId:{}", serverConfigSupplier.get().getBusId());
            scheduledFuture.cancel(true);
        }
        scheduledFuture = SystemScheduleMgr.getInstance().scheduleWithFixedDelay(new NamedRunnable("WeChatTick,", WechatLog::onTick), 1, 1, TimeUnit.SECONDS);
        branch = ServerContext.getGitBranch();
        try {
            //从网卡中获取IP
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip;
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = allNetInterfaces.nextElement();
                //用于排除回送接口,非虚拟网卡,未在使用中的网络接口
                if (!netInterface.isLoopback() && !netInterface.isVirtual() && netInterface.isUp()) {
                    //返回和网络接口绑定的所有IP地址
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        if (ip instanceof Inet4Address) {
                            ipAddress = ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("WechatLog get ipAddress error", e);
        }

    }

    private class WechatPlatformClient extends PlatformClient {

        WechatPlatformClient(int timeout) {
            super(timeout);
        }

        @Override
        public String getHostName() {
            return serverConfigSupplier.get().getWeChatKey("wechat_log_host_name");
        }

    }

    // tick删除过期logItem
    public static void onTick() {
        long now = SystemClock.now();
        cache.values().removeIf(next -> next.getCacheExpireTsMs() < now);
    }

    /**
     * 停止WechatLog
     * 失效所有缓存中存在的event，关闭发送线程和定时线程
     */
    public boolean stop() {
        if (this.executorService == null) {
            return true;
        }
        boolean ret = false;
        this.executorService.shutdown();
        try {
            ret = executorService.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return ret;
    }

    private boolean checkExecute(String wechatRobotKey, LogItem item) {
        if (StringUtils.isEmpty(wechatRobotKey)) {
            return false;
        }
        if (item.getLevel() == LogItem.INFO) {
            return true;
        }
        if (limitByLevel.length <= item.getLimitLevel()) {
            LOGGER.error("wechatCache boom. cache key too long. logItem:{}", item);
            cache.remove(formatCacheKey(wechatRobotKey, item.getCacheKey()));
            return false;
        }

        // 次数++
        item.addTriggerTimes();
        // 更新缓存到期时间
        item.upCacheExpire();

        if (item.getTriggerTimes() < limitByLevel[item.getLimitLevel()]) {
            return false;
        }
        item.upLimitLevel();
        return true;
    }

    private static String formatCacheKey(String wechatRobotKey, String cacheKey) {
        return wechatRobotKey + "_" + cacheKey;
    }

    /**
     * 发送错误日志
     */
    private void sendLog(String wechatRobotKey, LogItem logItem, Message message) {
        if (this.checkExecute(wechatRobotKey, logItem)) {
            directExecute(wechatRobotKey, logItem, message);
        }
    }


    /**
     * 根据配置同步或异步的发送消息至机器人所在群
     */
    public void directExecute(String wechatRobotKey, LogItem item, Message message) {
        if (SystemClock.now() < screenByGmTsMs) {
            return;
        }
        final boolean isSync = WechatLog.getInstance().serverConfigSupplier.get().getWechatLogSync();
        if (isSync) {
            this.syncSend(wechatRobotKey, item, message);
            return;
        }
        this.executorService.execute(() -> this.syncSend(wechatRobotKey, item, message));
    }

    /**
     * 同步发送消息至机器人所在群
     */
    private void syncSend(String key, LogItem item, Message message) {
        try {
            // 真正要发的时候再构建堆栈信息
            String msg = item.toMessage(message);
            this.platformClient.post4https(WechatLog.WECHAT_URL_PREFIX + key, msg);
        } catch (Throwable e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public static void error(Throwable e) {
        if (e != null) {
            error(e.getMessage(), e);
        }
    }

    /**
     * 用于起服关服企微提示
     * 禁止使用，除非你有很好的理由
     */
//    public static void info(String format, Object... arguments) {
//        LOGGER.info(format, arguments);
//        String serverRobotKey = getServerRobotKey();
//        if (serverRobotKey == null) {
//            return;
//        }
//        Message message = new ParameterizedMessage(format, arguments);
//        WechatLog instance = WechatLog.getInstance();
//        WechatConfig config = instance.serverConfigSupplier.get();
//        LogItem logItem = new LogItem(LogItem.INFO, config, format);
//        WechatLog.getInstance().sendLog(serverRobotKey, logItem, message);
//    }
    public static void error(String format, Object... arguments) {
        // 构建一个新的参数对象，保证数组最后一位一定是Throwable类型
        if (arguments == null || arguments.length == 0) {
            arguments = new Object[]{new GeminiException("Synthetic exception")};
        }
        if (!(arguments[arguments.length - 1] instanceof Throwable)) {
            Object[] param = new Object[arguments.length + 1];
            for (int i = 0; i < arguments.length; i++) {
                param[i] = arguments[i];
            }
            param[arguments.length] = new GeminiException("Synthetic exception");
            arguments = param;
        }
        LOGGER.error(format, arguments);

        Message message = new ParameterizedMessage(format, arguments);
        String serverRobotKey = getServerRobotKey();
        if (serverRobotKey != null) {
            LogItem logItem = getOrBuildLogItem(formatCacheKey(serverRobotKey, format));
            WechatLog.getInstance().sendLog(serverRobotKey, logItem, message);
        }

        String plannerRobotKey = getPlannerRobotKey();
        if (plannerRobotKey != null && message.getThrowable() instanceof ResourceException) {
            LogItem logItem = getOrBuildLogItem(formatCacheKey(plannerRobotKey, format));
            WechatLog.getInstance().sendLog(plannerRobotKey, logItem, message);
        }
    }

    private static LogItem getOrBuildLogItem(String cacheKey) {
        return cache.computeIfAbsent(cacheKey, (key) -> {
            WechatLog instance = WechatLog.getInstance();
            WechatConfig config = instance.serverConfigSupplier.get();
            return new LogItem(LogItem.ERROR, config, key, ipAddress, branch);
        });

    }

    /**
     * 获取企微key
     */

    private static String getServerRobotKey() {
        WechatLog instance = WechatLog.getInstance();
        if (instance == null || instance.serverConfigSupplier == null) {
            return null;
        }
        WechatConfig wechatConfig = instance.serverConfigSupplier.get();
        if (!wechatConfig.getWechatLogEnable()) {
            return null;
        }
        String weChatKey = wechatConfig.getWeChatKey(SERVER_KEY);
        if (StringUtils.isEmpty(weChatKey)) {
            return null;
        }
        return weChatKey;
    }

    /**
     * 获取企微key
     */
    private static String getPlannerRobotKey() {
        WechatLog instance = WechatLog.getInstance();
        if (instance == null || instance.serverConfigSupplier == null) {
            return null;
        }
        WechatConfig wechatConfig = instance.serverConfigSupplier.get();
        if (!wechatConfig.getWechatLogEnable()) {
            return null;
        }
        String weChatKey = wechatConfig.getWeChatKey(DESIGNER_KEY);
        if (StringUtils.isEmpty(weChatKey)) {
            return null;
        }
        return weChatKey;
    }

    public void openScreenByGm() {
        // 五秒屏蔽
        screenByGmTsMs = SystemClock.now() + 5000;
    }

    public void closeScreenByGm() {
        screenByGmTsMs = 0;
    }
}

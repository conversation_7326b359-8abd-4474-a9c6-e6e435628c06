package com.yorha.common.constant;

/**
 * 监控常量
 */
public class MonitorConstant {
    // 战斗tick高耗时阈值
    public static final long BATTLE_TICK_OVER_TIME = 100;
    // 大世界tick耗时阈值
    public static final long BIG_SCENE_TICK_OVER_TIME = 300;
    // nats推送高耗时阈值
    public static final long NATS_PUSH_OVER_TIME = 20;
    // nats传递高耗时阈值
    public static final long NATS_RECEIVE_OVER_TIME = 20;
    // 寻路高耗时阈值
    public static final long SEARCH_PATH_OVER_TIME = 3;
    // 屏蔽字高耗时阈值
    public static final long TEXT_FILTER_OVER_TIME = 500;
    // 心跳包的超时阈值
    public static final long HEART_BEAT_OVER_TIME_MS = 1000;
    // Actor实际处理消息高耗时阈值
    public static final long ACTOR_HANDLE_MSG_REAL_COST_OVER_TIME_MS = 50;
    // Actor执行Call操作耗时统计。
    public static final long ACTOR_CALL_COST_OVER_TIME_MS = 50;
    // 鉴权高耗时阈值
    public static final long AUTH_REQUEST_OVER_TIME = 500;
    // 消息调度延迟预警上限
    public static final long ACTOR_HANDLE_MAILBOX_SCHEDULING_COST_NS = 1000_000_000L;
    // 目录服响应高耗时次数
    public static final long DIR_RESPONSE_COST_OVER_TIME = 400;
    // GetByPartKey耗时过久的阈值
    public static final int WARNING_GET_BY_PART_KEY_COST_TIME_MS = 500;
    // BatchGet耗时过久的阈值
    public static final int WARNING_BATCH_GET_COST_TIME_MS = 500;
    // TCAPLUS单行落库大小打印警戒日志
    public static final int TCAPLUS_ROW_WARNING_LOGGER_BYTE_SIZE = 7 * 1024 * 1024;
    //  TCAPLUS单行落库大小Wechat告警
    public static final int TCAPLUS_ROW_WARNING_WECHAT_BYTE_SIZE = 8 * 1024 * 1024;
    // 线程数过高预警
    public static final long THREAD_NUM_OVER_NUM = 200;
    // nats大包预警(800kb)
    public static final int NATS_MESSAGE_WARNING_LOGGER_BYTE_SIZE = 800 * 1024;
    // entity监控大于1kb才显示字段
    public static final int ENTITY_MIN_SHOW_BYTE_SIZE = 1024;
    // playerEntity上报监控阈值
    public static final int PLAYER_ENTITY_MIN_REPORT_BYTE_SIZE = 200 * 1024;
    // clanEntity上报监控阈值
    public static final int CLAN_ENTITY_MIN_REPORT_BYTE_SIZE = 200 * 1024;
    // 下发给客户端的大消息尺寸，单位：B
    public static final int LARGE_MSG_SIZE = 10 * 1024;
}

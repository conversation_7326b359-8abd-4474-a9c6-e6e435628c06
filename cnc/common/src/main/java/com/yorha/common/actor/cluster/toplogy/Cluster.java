package com.yorha.common.actor.cluster.toplogy;

import com.yorha.common.utils.BusIdUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class Cluster {
    private final Map<String, Node> busId2NodeMap;
    private final List<Node>[] serverType2NodeArray;

    private Cluster(final Map<String, Node> cluster, final List<Node>[] nodeTypeArray) {
        this.busId2NodeMap = cluster;
        this.serverType2NodeArray = nodeTypeArray;
    }

    public List<Node> getNodeList(final int serverType) {
        if (serverType > this.serverType2NodeArray.length) {
            return Collections.emptyList();
        }
        if (serverType < 0) {
            return Collections.emptyList();
        }
        if (this.serverType2NodeArray[serverType] == null) {
            return Collections.emptyList();
        }
        return this.serverType2NodeArray[serverType];
    }

    public Node getNode(final String busId) {
        return this.busId2NodeMap.get(busId);
    }

    public Builder toBuilder() {
        return (new Builder()).cluster(this);
    }

    @Override
    public String toString() {
        return "Cluster{" +
                "busId2NodeMap=" + busId2NodeMap +
                '}';
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {
        private Map<String, Node> map;
        private boolean isBuild = false;

        private Builder() {
            this.map = new LinkedHashMap<>();
        }

        private Builder cluster(final Cluster cluster) {
            this.renewMap();
            this.map = new LinkedHashMap<>(cluster.busId2NodeMap);
            return this;
        }

        public Builder addNode(Node node) {
            this.renewMap();
            this.map.put(node.getBusId(), node);
            return this;
        }

        public Builder removeNode(final String busId) {
            this.renewMap();
            this.map.remove(busId);
            return this;
        }

        private void renewMap() {
            if (!this.isBuild) {
                return;
            }
            this.map = new HashMap<>(this.map);
            this.isBuild = false;
        }

        @SuppressWarnings({"rawtypes", "unchecked"})
        public Cluster build() {
            this.isBuild = true;
            int maxServerType = -1;
            final Map<Integer, List<Node>> m = new LinkedHashMap<>(10);
            for (final Node node : this.map.values()) {
                final int serverType = node.getServerType();
                m.computeIfAbsent(serverType, (s) -> new ArrayList<>()).add(node);
                maxServerType = Math.max(serverType, maxServerType);
            }
            final List[] array = new List[(maxServerType / 16 + 1) * 16];
            for (Map.Entry<Integer, List<Node>> kv : m.entrySet()) {
                kv.getValue().sort((k1, k2) -> {
                    final int zone1 = BusIdUtils.getZoneIdFromBusId(k1.getBusId());
                    final int zone2 = BusIdUtils.getZoneIdFromBusId(k2.getBusId());
                    if (zone1 != zone2) {
                        return zone1 - zone2;
                    }
                    final int instance1 = BusIdUtils.getInstanceIdFromBusId(k1.getBusId());
                    final int instance2 = BusIdUtils.getInstanceIdFromBusId(k2.getBusId());
                    if (instance1 != instance2) {
                        return instance1 - instance2;
                    }
                    return 0;
                });
                array[kv.getKey()] = Collections.unmodifiableList(kv.getValue());
            }
            return new Cluster(Collections.unmodifiableMap(this.map), array);
        }
    }
}

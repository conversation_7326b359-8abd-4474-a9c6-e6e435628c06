package com.yorha.common.actor.node;

import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.YamlUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * etcd临时白名单配置结构
 */
public class TempWhiteListConfig {
    private final List<TempWhiteListConfig.WhiteConfig> whiteConfigList;

    public TempWhiteListConfig(List<TempWhiteListConfig.WhiteConfig> list) {
        whiteConfigList = list;
    }

    public List<TempWhiteListConfig.WhiteConfig> getWhiteConfigList() {
        return whiteConfigList;
    }

    public static TempWhiteListConfig valueOf(List<Map<String, Object>> list) {
        List<TempWhiteListConfig.WhiteConfig> tempList = new ArrayList<>();
        for (Map<String, Object> data : list) {
            tempList.add(new WhiteConfig(data));
        }
        return new TempWhiteListConfig(tempList);
    }

    public static class WhiteConfig {
        private final int worldId;
        private final int zoneId;
        private final String deviceId;
        private final String openId;
        private final String ip;
        private final boolean debugLog;
        private final boolean registerLimit;
        private final boolean ipLimit;
        private final boolean powerUser;
        private final String desc;
        private final boolean noAuth;

        public WhiteConfig(Map<String, Object> data) {
            this.worldId = ServerContext.getWorldId();
            this.zoneId = (Integer) data.getOrDefault("zoneId", -1);
            this.deviceId = (String) data.getOrDefault("deviceId", "");
            this.openId = YamlUtils.getOrDefault(data.get("openId"), "");
            this.ip = (String) data.getOrDefault("ip", "");
            this.debugLog = (Boolean) data.getOrDefault("debugLog", false);
            this.registerLimit = (Boolean) data.getOrDefault("registerLimit", true);
            this.ipLimit = (Boolean) data.getOrDefault("ipLimit", true);
            this.powerUser = (Boolean) data.getOrDefault("powerUser", true);
            this.desc = (String) data.getOrDefault("desc", "");
            this.noAuth = (Boolean) data.getOrDefault("noAuth", false);
        }

        public int getWorldId() {
            return worldId;
        }

        public int getZoneId() {
            return zoneId;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public String getOpenId() {
            return openId;
        }

        public String getIp() {
            return ip;
        }

        public boolean getDebugLog() {
            return debugLog;
        }

        public boolean getRegisterLimit() {
            return registerLimit;
        }

        public boolean getIpLimit() {
            return ipLimit;
        }

        public boolean getPowerUser() {
            return powerUser;
        }

        public String getDesc() {
            return desc;
        }

        public boolean getNoAuth() {
            return noAuth;
        }

        @Override
        public String toString() {
            return "WhiteConfig{" +
                    "worldId=" + worldId +
                    ", zoneId=" + zoneId +
                    ", deviceId='" + deviceId + '\'' +
                    ", openId='" + openId + '\'' +
                    ", ip='" + ip + '\'' +
                    ", debugLog=" + debugLog +
                    ", registerLimit=" + registerLimit +
                    ", ipLimit=" + ipLimit +
                    ", powerUser=" + powerUser +
                    ", desc='" + desc + '\'' +
                    ", noAuth=" + noAuth +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "TempWhiteListConfig{" +
                "whiteConfigList=" + whiteConfigList +
                '}';
    }
}


package com.yorha.common.resource.resservice.clan;

import com.google.common.collect.*;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import res.template.ClanTechDonateTemplate;
import res.template.ClanTechRankingTemplate;
import res.template.ClanTechSubTemplate;
import res.template.ConstClanTechTemplate;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.yorha.proto.CommonEnum.*;

/**
 * 军团科技
 */
public class ClanTechResService extends AbstractResService {

    /**
     * 捐献配置
     */
    private final ListMultimap<ClanTechDonateType, ClanTechDonateTemplate> donateMap = ArrayListMultimap.create(2, 5);

    /**
     * <科技id, 所有subTech配置>，每个科技下的subTemplate按照level保证排序
     */
    private final ListMultimap<Integer, ClanTechSubTemplate> clanTechMap = ArrayListMultimap.create();

    /**
     * 排行奖励数据配置 <排行类型,<名次, 奖励id>>
     */
    private final Map<ClanContributionRankingType, Map<Integer, Integer>> rankingMap = Maps.newEnumMap(ClanContributionRankingType.class);
    private final Map<ClanContributionRankingType, Integer> rankingMaxNoMap = Maps.newEnumMap(ClanContributionRankingType.class);

    public ClanTechResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getListFromMap(ClanTechDonateTemplate.class).forEach(donateTemplate -> donateMap.put(donateTemplate.getType(), donateTemplate));
        getResHolder().getListFromMap(ClanTechSubTemplate.class)
                .stream().sorted(
                Comparator.comparingInt(ClanTechSubTemplate::getTechId)
                        .thenComparingInt(ClanTechSubTemplate::getTechLevel)
        )
                .forEach(subTemplate -> clanTechMap.put(subTemplate.getTechId(), subTemplate));
        getResHolder().getListFromMap(ClanTechRankingTemplate.class).forEach(rankingTemplate -> {
                    IntPairType rankPair = rankingTemplate.getRankPair();
                    ClanContributionRankingType rankingType = ClanContributionRankingType.forNumber(rankingTemplate.getType());
                    for (int i = rankPair.getKey(); i <= rankPair.getValue(); i++) {
                        rankingMap.computeIfAbsent(rankingType, t -> Maps.newHashMap())
                                .put(i, rankingTemplate.getRewardId());
                        rankingMaxNoMap.merge(rankingType, i, Math::max);
                    }
                }
        );

    }

    @Override
    public void checkValid() throws ResourceException {
        for (ClanTechDonateType type : ClanTechDonateType.values()) {
            if (type != ClanTechDonateType.CTDT_NONE) {
                if (!donateMap.containsKey(type)) {
                    throw new ResourceException("捐献类型 {} 没有倍率配置", type);
                }
            }
        }
        for (Map.Entry<Integer, Collection<ClanTechSubTemplate>> entry : clanTechMap.asMap().entrySet()) {
            final int techId = entry.getKey();
            List<ClanTechSubTemplate> subTechs = Lists.newArrayList(entry.getValue());
            for (int i = 0; i < subTechs.size(); i++) {
                if (subTechs.get(i).getTechLevel() != i + 1) {
                    throw new ResourceException("军团科技子id中等级缺失，科技id：{} 等级：{}", techId, i + 1);
                }
            }
        }
        for (ClanTechSubTemplate subTemplate : getResHolder().getListFromMap(ClanTechSubTemplate.class)) {
            for (Integer frontTechSubId : subTemplate.getFrontTechList()) {
                ClanTechSubTemplate f = getResHolder().findValueFromMap(ClanTechSubTemplate.class, frontTechSubId);
                if (f == null) {
                    throw new ResourceException("联盟科技子id配置的前置条件非法，techSubId:{}, frontTech:{}", subTemplate.getId(), frontTechSubId);
                }
            }
            for (IntPairType costPair : subTemplate.getCostResPairList()) {
                CurrencyType type = CurrencyType.forNumber(costPair.getKey());
                if (type == null || type == CurrencyType.CT_None) {
                    throw new ResourceException("联盟科技研发消耗资源类型非法！techSubId:{} {}_{}", subTemplate.getId(), costPair.getKey(), costPair.getValue());
                }
            }
        }
    }

    public ClanTechDonateTemplate randomDonateMagnification(ClanTechDonateType type) {
        List<ClanTechDonateTemplate> list = donateMap.get(type);
        return RandomUtils.randomByWeight(list, ClanTechDonateTemplate::getProbability);
    }

    public int calcDonateDiamondCost(int dailyDiamondDonateTimes) {
        ConstClanTechTemplate t = getResHolder().getConstTemplate(ConstClanTechTemplate.class);
        return Math.min(t.getDiamondDonateMaxValue(), t.getDiamondDonateInitialValue() + ((dailyDiamondDonateTimes + 1) * t.getDiamondDonateGrowthValue()));
    }

    public List<ClanTechSubTemplate> getSubTechs(int techId) {
        return ImmutableList.copyOf(clanTechMap.get(techId));
    }

    public int findRewardIdByRanking(ClanContributionRankingType type, int rank) {
        Map<Integer, Integer> map = rankingMap.get(type);
        if (map != null) {
            return map.getOrDefault(rank, 0);
        }
        return 0;
    }

    public int maxRankNoOf(ClanContributionRankingType type) {
        return rankingMaxNoMap.getOrDefault(type, 0);
    }
}

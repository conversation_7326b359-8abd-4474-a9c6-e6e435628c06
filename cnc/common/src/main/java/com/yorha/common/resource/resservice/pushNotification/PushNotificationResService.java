package com.yorha.common.resource.resservice.pushNotification;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.notification.Notification;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import res.template.PushCdTemplate;
import res.template.PushContentTemplate;
import res.template.PushSettingTemplate;
import res.template.ServerLanguageTemplate;

import java.util.*;

/**
 * 推送相关
 *
 * <AUTHOR>
 */
public class PushNotificationResService extends AbstractResService {
    private final Map<Integer, Notification> notificationEntryMap = new HashMap<>();

    private final IntArrayList openNtfModeIdlList = new IntArrayList();

    /**
     * 消息推送条目告警条数，防止策划乱配推送消息
     */
    private static final int PUSH_ENTRY_THRESHOLD = 50;

    public PushNotificationResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        final Map<Integer, Integer> setMap = this.getNotificationSettingMap();
        // 读取所有默认开放的推送模块列表
        final Map<Integer, PushSettingTemplate> settingTemplateMap = getResHolder().getMap(PushSettingTemplate.class);
        for (final PushSettingTemplate settingTemplate : settingTemplateMap.values()) {
            if (!settingTemplate.getInitialOpen()) {
                continue;
            }
            if (settingTemplate.getIsClient()) {
                continue;
            }
            Integer modelId = setMap.get(settingTemplate.getId());
            if (modelId == null) {
                throw new GeminiException("PushSettingTemplate id={} no config in NotificationSettingMap", modelId);
            }
            this.openNtfModeIdlList.add((int) setMap.get(settingTemplate.getId()));
        }
        // 构建推送相关数据
        for (final PushContentTemplate template : getResHolder().getListFromMap(PushContentTemplate.class)) {
            final PushSettingTemplate settingTemplate = settingTemplateMap.get(template.getTypeID());
            if (settingTemplate == null) {
                throw new ResourceException("PushContentTemplate id={}, settingId={} is null", template.getId(), template.getTypeID());
            }
            if (settingTemplate.getIsClient()) {
                continue;
            }
            if (!setMap.containsKey(template.getTypeID())) {
                throw new ResourceException("PushContentTemplate id={}, settingId={} is no config in PushNotificationResService", template.getId(), template.getTypeID());
            }
            final int modelId = setMap.get(template.getTypeID());
            final Notification entry = new Notification(template.getId(), template.getTypeID(), modelId,
                    template.getServerPushName(), template.getServerText(), template.getColdID());
            this.notificationEntryMap.put(template.getId(), entry);
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        // 获取配置的推送设置
        final Map<Integer, Integer> setMap = this.getNotificationSettingMap();
        final Set<Integer> settings = new IntOpenHashSet();
        for (final PushSettingTemplate template : getResHolder().getListFromMap(PushSettingTemplate.class)) {
            if (template.getIsClient()) {
                continue;
            }
            final int setId = template.getId();
            if (!setMap.containsKey(setId)) {
                throw new ResourceException("push setting not config in enum NotificationSetting, setId={}", setId);
            }
            settings.add(setId);
        }
        // 获取配置的推送冷却类型
        final Set<Integer> coldTypes = new IntOpenHashSet();
        for (final PushCdTemplate template : getResHolder().getListFromMap(PushCdTemplate.class)) {
            coldTypes.add(template.getId());
        }
        // 获取配置的服务器多语言文本id
        final Set<Integer> textIds = new IntOpenHashSet();
        for (ServerLanguageTemplate template : getResHolder().getListFromMap(ServerLanguageTemplate.class)) {
            textIds.add(template.getId());
        }
        Set<Integer> titleId = new IntOpenHashSet();
        Set<Integer> bodyId = new IntOpenHashSet();
        for (final PushContentTemplate template : getResHolder().getListFromMap(PushContentTemplate.class)) {
            // 检查推送类型在不在配置里面
            if (!settings.contains(template.getTypeID())) {
                continue;
            }
            // 检查冷却类型在不在配置里面
            if (template.getColdID() != 0 && !coldTypes.contains(template.getColdID())) {
                throw new ResourceException("PushContentTemplate config wrong cold type, templateId={}, wrong cold type={}", template.getId(), template.getColdID());
            }
            // 检查多语言文本在不在配置里面
            if (!textIds.contains(template.getServerPushName())) {
                throw new GeminiException("PushContentTemplate config wrong server text id, templateId={}, wrong text id={}", template.getId(), template.getServerPushName());
            }
            if (!textIds.contains(template.getServerText())) {
                throw new GeminiException("PushContentTemplate config wrong server text id, templateId={}, wrong text id={}", template.getId(), template.getServerPushName());
            }
            titleId.add(template.getServerPushName());
            bodyId.add(template.getServerText());
        }
        if (Math.min(titleId.size(), bodyId.size()) > PUSH_ENTRY_THRESHOLD) {
            throw new ResourceException("Push Notification entry exceed warning threshold");
        }
    }

    /**
     * 获取推送相关的数据。
     *
     * @param notificationId 推送id。
     * @return 推送对象。
     */
    public Notification getNotification(final int notificationId) {
        return this.notificationEntryMap.get(notificationId);
    }

    /**
     * 获取默认打开的推送数据。
     *
     * @return 模块序号列表。
     */
    public List<Integer> getDefaultOpenNtfModeIdlList() {
        return Collections.unmodifiableList(openNtfModeIdlList);
    }

    /**
     * 将setting id转换未对应的模块id。
     *
     * @param settingIdList 图送设置id。
     * @return 推送设置对应的模块id。
     */
    public List<Integer> transferSettingIdList2PushNtfModelIdList(final List<Integer> settingIdList) {
        final Map<Integer, Integer> settingMap = this.getNotificationSettingMap();
        List<Integer> modelIdList = new ArrayList<>(settingIdList.size());
        for (final int settingId : settingIdList) {
            final Integer modelId = settingMap.get(settingId);
            if (modelId == null) {
                throw new GeminiException(ErrorCode.INVALID_NOTIFICATION_SETTING_ID, String.format("setting id %d not right", settingId));
            }
            modelIdList.add(modelId);
        }
        return modelIdList;
    }

    /**
     * 获取打开的推送设置列表
     *
     * @param notificationMask 64位推送设置掩码
     * @return 推送设置id列表
     */
    public List<Integer> getOpenPushSettingList(long notificationMask) {
        notificationMask = (notificationMask << 1) >> 1;
        List<Integer> openSettingList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : getNotificationSettingMap().entrySet()) {
            boolean isOpen = ((1L << entry.getValue()) & notificationMask) > 0;
            if (isOpen) {
                openSettingList.add(entry.getKey());
            }
        }
        return openSettingList;
    }

    /**
     * 获取关闭的推送设置列表
     *
     * @param notificationMask 64位推送设置掩码
     * @return 推送设置id列表
     */
    public List<Integer> getClosePushSettingList(long notificationMask) {
        notificationMask = (notificationMask << 1) >> 1;
        List<Integer> closeSettingList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : getNotificationSettingMap().entrySet()) {
            boolean isClose = ((1L << entry.getValue()) & notificationMask) == 0;
            if (isClose) {
                closeSettingList.add(entry.getKey());
            }
        }
        return closeSettingList;
    }

    /**
     * setId -> 推送模块id 的映射
     * 根据消息推送表的push_setting手动设置，为了能热更，使用枚举无法热更
     * <p>
     * 提交后，映射关系不允许修改。
     */
    private Map<Integer, Integer> getNotificationSettingMap() {
        Map<Integer, Integer> setMap = new Int2IntOpenHashMap();
//        setMap.put(1001, 0);
        setMap.put(1002, 1);
        setMap.put(1003, 2);
        setMap.put(1004, 3);
//        setMap.put(1005, 4);
//        setMap.put(1006, 5);
//        setMap.put(1007, 6);
//        setMap.put(1008, 7);
//        setMap.put(1009, 8);
        setMap.put(1010, 9);
        setMap.put(1011, 10);
        setMap.put(1012, 11);
        setMap.put(1013, 12);
//        setMap.put(1014, 13);
        setMap.put(1015, 14);
//        setMap.put(1016, 15);
        return Collections.unmodifiableMap(setMap);
    }
}

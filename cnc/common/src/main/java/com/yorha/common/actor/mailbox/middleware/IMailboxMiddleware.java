package com.yorha.common.actor.mailbox.middleware;

import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actor.mailbox.MsgContext;
import com.yorha.common.actor.mailbox.msg.ActorMailboxMsg;

/**
 * 消息处理中间件，会被mailbox调用，可用于消息处理得各种埋点。
 * 1. 消息统计。
 * 2. 消息触发。
 * 3. 邮箱信息统计。
 * 4. 邮箱埋点触发。
 *
 * <AUTHOR>
 */
public interface IMailboxMiddleware {
    /**
     * 投递消息成功。
     *
     * @param mb  邮箱。
     * @param msg 消息。
     */
    void messageOffer(IActorMailbox mb, ActorMailboxMsg msg);

    /**
     * 消息被拒绝。
     *
     * @param mb     邮箱。
     * @param msg    消息。
     * @param reason 被拒绝的理由。
     */
    void messageRefused(IActorMailbox mb, ActorMailboxMsg msg, String reason);

    /**
     * 投递消息开始调度。
     *
     * @param context   上下文。
     * @param startTsNs 消息启动时间戳。
     */
    void messageStart(MsgContext context, long startTsNs);

    /**
     * 消息执行完毕。
     *
     * @param context   上下文。
     * @param startTsNs 消息启动的时间戳。
     */
    void messageDone(MsgContext context, long startTsNs);

    /**
     * 处理消息失败。
     *
     * @param context   上下文。
     * @param startTsNs 消息启动时间戳。
     */
    void messageError(MsgContext context, long startTsNs);

    /**
     * 反馈信息。
     */
    void perfReport();
}

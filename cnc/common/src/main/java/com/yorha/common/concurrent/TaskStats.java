package com.yorha.common.concurrent;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.utils.TextTable;
import com.yorha.common.constant.LogConstant;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import javax.annotation.concurrent.ThreadSafe;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;

/**
 * task 统计
 *
 * <AUTHOR>
 */
@ThreadSafe
public class TaskStats {
    private static final Logger LOGGER = LogManager.getLogger(LogConstant.LOG_TYPE_PERF_STAT);

    private final String signature;
    private final Map<String, TaskCollector> taskMap;

    private static final Set<String> NOT_STATISTICS_TASK_NAME = new HashSet<>();

    static {
        NOT_STATISTICS_TASK_NAME.add("show_tcp_traffic");
        NOT_STATISTICS_TASK_NAME.add("dispatcher_perf_stat");
        NOT_STATISTICS_TASK_NAME.add("heartbeat");
        NOT_STATISTICS_TASK_NAME.add("monitor_session");
        NOT_STATISTICS_TASK_NAME.add("LeaderTimer");
        NOT_STATISTICS_TASK_NAME.add("show_db_traffic");
        NOT_STATISTICS_TASK_NAME.add("node_perf_stat");
        NOT_STATISTICS_TASK_NAME.add("NodeHeartBeat");
        NOT_STATISTICS_TASK_NAME.add("NodeHeartBeatCmd");
        NOT_STATISTICS_TASK_NAME.add("ScheduleOnce");
        NOT_STATISTICS_TASK_NAME.add("node_heart_beat");
    }

    public static class TaskCollector {
        private final String taskName;
        private final LongAdder offerCount = new LongAdder();
        private final ConcurrentLinkedQueue<Long> deltaEach = new ConcurrentLinkedQueue<>();
        private final ConcurrentLinkedQueue<Long> costEach = new ConcurrentLinkedQueue<>();

        public TaskCollector(String taskName) {
            this.taskName = taskName;
        }

        public void onOffer() {
            offerCount.increment();
        }

        public void onStart(long deltaNanos) {
            deltaEach.add(deltaNanos);
        }

        public void onDone(long costNanos) {
            costEach.add(costNanos);
        }
    }

    public TaskStats(String signature) {
        this.signature = signature;
        this.taskMap = Maps.newConcurrentMap();
    }

    public void onOffer(String taskName) {
        taskMap.computeIfAbsent(taskName, k -> new TaskCollector(taskName)).onOffer();
    }

    public void onStart(String taskName, long deltaNanos) {
        taskMap.computeIfAbsent(taskName, k -> new TaskCollector(taskName)).onStart(deltaNanos);
    }

    public void onDone(String taskName, long costNanos) {
        taskMap.computeIfAbsent(taskName, k -> new TaskCollector(taskName)).onDone(costNanos);
    }

    public void onlyPrint() {
        print(false);
    }

    public void printAndClear() {
        print(true);
    }

    private void print(boolean isClear) {
        TextTable textTable = buildTextTable(isClear);
        textTable.info(LOGGER, false);
    }

    @NotNull
    public TextTable buildTextTable(boolean isClear) {
        TextTable textTable = new TextTable(signature + ".taskStats");
        textTable.addColumn("taskName", "offerCount", "startCount", "maxDeltaMs", "avgDeltaMs",
                "pct90DeltaMs", "doneCount", "maxCostMs", "avgCostMs", "pct90CostMs", "totalCostMs");
        // 提前copy下来，后续单线程处理
        ArrayList<TaskCollector> tasks = Lists.newArrayList(taskMap.values());
        if (isClear) {
            this.taskMap.clear();
        }
        for (TaskCollector task : tasks) {
            boolean needStatistics = true;
            for (String useless : NOT_STATISTICS_TASK_NAME) {
                if (task.taskName.equals(useless)) {
                    needStatistics = false;
                    break;
                }
            }
            // 不统计无用的
            if (!needStatistics) {
                continue;
            }
            // offer
            long offerCount = task.offerCount.longValue();
            // start
            long totalDelta = 0;
            long maxDelta = 0;
            ArrayList<Long> deltaEach = Lists.newArrayList(task.deltaEach);
            Collections.sort(deltaEach);
            long startCount = deltaEach.size();
            for (long delta : deltaEach) {
                totalDelta += delta;
                maxDelta = Math.max(delta, maxDelta);
            }
            // 不是特别精确的pct90
            int deltaPct90Index = (int) (startCount * 0.9);
            long deltaPct90Ms = 0;
            if (startCount > 0) {
                deltaPct90Ms = deltaEach.get(deltaPct90Index);
            }
            // done
            long totalCost = 0;
            long maxCost = 0;
            ArrayList<Long> costEach = Lists.newArrayList(task.costEach);
            Collections.sort(costEach);
            int doneCount = costEach.size();
            for (long cost : costEach) {
                totalCost += cost;
                maxCost = Math.max(cost, maxCost);
            }
            // 不是特别精确的pct90
            int costPct90Index = (int) (doneCount * 0.9);
            long costPct90Ms = 0;
            if (doneCount > 0) {
                costPct90Ms = costEach.get(costPct90Index);
            }
            if (offerCount <= 0 && startCount <= 0 && doneCount <= 0) {
                continue;
            }
            long avgDelta = 0;
            long avgCost = 0;

            if (startCount > 0) {
                avgDelta = totalDelta / startCount;
            }
            if (doneCount > 0) {
                avgCost = totalCost / doneCount;
            }

            textTable.addRow().add(
                    task.taskName,
                    String.valueOf(offerCount),
                    String.valueOf(startCount),
                    String.valueOf(TimeUnit.NANOSECONDS.toMillis(maxDelta)),
                    String.valueOf(TimeUnit.NANOSECONDS.toMillis(avgDelta)),
                    String.valueOf(TimeUnit.NANOSECONDS.toMillis(deltaPct90Ms)),
                    String.valueOf(doneCount),
                    getTimeString(maxCost),
                    getTimeString(avgCost),
                    getTimeString(costPct90Ms),
                    getTimeString(totalCost)
            );
        }
        return textTable;
    }

    private String getTimeString(long nanoSeconds) {
        if (nanoSeconds > 1000000) {
            return String.valueOf(TimeUnit.NANOSECONDS.toMillis(nanoSeconds));
        }
        return nanoSeconds / 1000 + " kns";
    }

    public static void main(String[] args) {
        TaskStats taskStats = new TaskStats("xx");
        for (int i = 0; i < 15; i++) {
            taskStats.onOffer("a");
            taskStats.onStart("a", 213);
            taskStats.onDone("a", 2111);
        }
        for (int i = 0; i < 199; i++) {
            taskStats.onOffer("b");
            taskStats.onStart("b", 123);
            taskStats.onDone("b", 555);
        }

        taskStats.printAndClear();
    }

}

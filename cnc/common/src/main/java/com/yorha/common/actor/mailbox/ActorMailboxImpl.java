package com.yorha.common.actor.mailbox;

import com.yorha.common.actor.mailbox.middleware.IMailboxMiddleware;
import com.yorha.common.actor.mailbox.msg.ActorMailboxMsg;
import com.yorha.common.actor.ref.FixedRef;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actorservice.msg.ActorMsgUtil;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 本地Actor的处理邮箱，采用了多生产者-单消费者的阻塞队列模型。
 * 本地Actor通过邮箱接收消息，接收到消息后，触发调度器将Mailbox调取起来，消费邮箱消息。
 * 注意：适合常驻内存的Actor。
 *
 * <AUTHOR>
 */
public class ActorMailboxImpl implements IRunnableActorMailbox {
    private static final Logger LOGGER = LogManager.getLogger(ActorMailboxImpl.class);
    static final int MAILBOX_IDLE = 1;
    static final int MAILBOX_RUNNING = 2;
    static final int MAILBOX_DESTROYED = 3;

    /**
     * mailbox msg数量。
     * 注意：请不要放到queue后和status相邻。需要借助queue的padding。
     */
    protected final AtomicInteger msgCount;
    /**
     * 消息队列。
     */
    protected final BlockingQueue<ActorMailboxMsg> queue;
    /**
     * 邮箱是否处于处理消息的状态。
     */
    protected final AtomicInteger status;
    /**
     * 元数据。
     */
    protected final IMailboxMetaData metaData;
    /**
     * 对应的ActorRef。
     */
    protected final IActorRef actorRef;
    /**
     * 宿主Actor。
     */
    protected AbstractActor host;

    protected ActorMailboxImpl(final IActorRef actorRef, final IMailboxMetaData metaData) {
        this.queue = metaData.getMailboxQueueSize() > 0 ? new MpscBlockingConsumerArrayQueue<>(metaData.getMailboxQueueSize()) : new LinkedBlockingQueue<>();
        this.status = new AtomicInteger(MAILBOX_IDLE);
        this.msgCount = new AtomicInteger();
        this.actorRef = actorRef;
        this.metaData = metaData;
    }

    @Override
    public IActorRef ref() {
        return this.actorRef;
    }

    @Override
    public boolean sendMsg(Object object) {
        final ActorMsgEnvelope msg = (ActorMsgEnvelope) object;
        final ActorMailboxMsg mailboxMsg = new ActorMailboxMsg(msg, ActorMsgUtil.profNameOf(msg));
        this.metaData.getMailboxMiddleware().messageOffer(this, mailboxMsg);
        if (!this.queue.offer(mailboxMsg)) {
            this.metaData.getMailboxMiddleware().messageRefused(this, mailboxMsg, "full");
            return false;
        }
        this.msgCount.incrementAndGet();
        this.trySchedule();
        return true;
    }

    @Override
    public final void run() {
        try {
            ThreadContext.put("actorId", this.actorRef.getActorRole() + "-" + this.actorRef.getActorId());
            boolean isCreateHost = false;
            if (this.host == null) {
                isCreateHost = true;
                this.createHostActor();
                if (this.host == null) {
                    // double check 保证host必然存在
                    LOGGER.error("try create actor {}! result is null! destroy mailbox!", this.actorRef);
                    this.onDestroy();
                    return;
                }
            }
            this.handleMsg(isCreateHost);
            if (this.host.isDestroy()) {
                // 这时候可能别的msg正在投递，所以这个mailbox会在destroy的场景下丢消息
                this.onDestroy();
                return;
            }
            this.status.compareAndSet(MAILBOX_RUNNING, MAILBOX_IDLE);
            if (this.msgCount.get() <= 0) {
                return;
            }
            this.trySchedule();
        } finally {
            ThreadContext.remove("actorId");
        }
    }

    /**
     * 船舰邮箱对应的Actor。
     */
    private void createHostActor() {
        try {
            this.host = this.metaData.getActorFactory().apply(this.actorRef);
            LOGGER.info("ActorMailboxImpl createHostActor {}", this.host);
        } catch (Throwable e) {
            LOGGER.error("try create actor {}! create fail!", this.actorRef, e);
        }
    }

    /**
     * 销毁Mailbox的回调。
     */
    protected void onDestroy() {
        this.status.set(MAILBOX_DESTROYED);
        if (!this.metaData.getActorSystem().getRegistryValue().removeMailbox(this.ref())) {
            LOGGER.warn("removeRegistryValue {} of {} fail!", this.ref(), this);
        }
        this.queue.clear();
        this.msgCount.set(0);
    }

    /**
     * 调度邮箱。
     */
    private void trySchedule() {
        if (this.status.compareAndSet(MAILBOX_IDLE, MAILBOX_RUNNING)) {
            this.metaData.getDispatcher().schedule(this);
        }
    }

    /**
     * 消息处理函数。
     */
    private void handleMsg(boolean isCreateHost) {
        try {
            int consistentHandleCnt = 0;
            final int queueSize = this.metaData.getMailboxQueueSize();
            final int keepAliveSec = this.metaData.getDispatcher().getKeepAliveSec();
            final int throughput = this.metaData.getDispatcher().getThroughput();
            final IMailboxMiddleware middleware = this.metaData.getMailboxMiddleware();
            final MsgContext context = new MsgContext(this);
            while (!this.host.isDestroy()) {
                if (throughput > 0 && consistentHandleCnt > throughput) {
                    consistentHandleCnt = 0;
                    Thread.yield();
                }
                final ActorMailboxMsg msg = this.poolMsg(keepAliveSec);
                if (msg == null) {
                    break;
                }
                if (isCreateHost) {
                    // 首次创建的消息需要打印
                    LOGGER.info("ActorMailboxImpl handleMsg create host {} {}", this.host, msg);
                    isCreateHost = false;
                }
                context.setMsg(msg);
                final int msgSize = this.queue.size();
                if ((msgSize * 2) > queueSize) {
                    LOGGER.warn("mailbox {} is nearly full! size {}, msgCount {}! msg {}!", this, msgSize, this.msgCount.get(), msg);
                }
                final long startTsNs = SystemClock.nanoTimeNative();
                middleware.messageStart(context, startTsNs);
                consistentHandleCnt++;
                try {
                    this.host.handleMsg(context);
                    middleware.messageDone(context, startTsNs);
                } catch (Throwable e) {
                    middleware.messageError(context, startTsNs);
                    // 不应该有异常渗透到这里
                    WechatLog.error("exception_perf ActorMailboxImpl {} handle {}, catch exception:", this.host, msg, e);
                }
            }
        } catch (InterruptedException e) {
            LOGGER.debug("exception_perf {} handleMsg interrupted!", this.host);
        }
    }

    private ActorMailboxMsg poolMsg(final int keepAliveSec) throws InterruptedException {
        final ActorMailboxMsg msg = this.queue.poll(keepAliveSec, TimeUnit.SECONDS);
        if (msg != null) {
            this.msgCount.decrementAndGet();
        }
        return msg;
    }

    @Override
    public String toString() {
        return "ActorMailboxImpl{" +
                "host=" + host +
                ", actorRef=" + actorRef +
                '}';
    }

    public static class Factory implements IMailboxFactory {
        private final IMailboxMetaData metaData;

        public Factory(IMailboxMetaData metaData) {
            this.metaData = metaData;
        }

        @Override
        public IActorMailbox newMailbox(IActorRef ref) {
            if (!(ref instanceof FixedRef)) {
                ref = new FixedRef(this.metaData.getActorSystem().getRegistryValue().getLocalNode().getBusId(), ref.getActorRole(), ref.getActorId());
            }
            return new ActorMailboxImpl(ref, this.metaData);
        }
    }
}

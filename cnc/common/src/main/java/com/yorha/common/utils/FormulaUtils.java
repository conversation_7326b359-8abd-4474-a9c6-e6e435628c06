package com.yorha.common.utils;

import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 游戏中用到的公式工具类
 *
 * 1. 计算溢出，throw ArithmeticException
 * 2. 结果小于0，throw ArithmeticException
 * 3. 公式无特殊说明默认向上取整
 *
 * f1: (a + b) * (1 + c) * d，c ＞ -1，最终值非负、向上取整
 * f2: (a – b) / (1 + c) * d，c ＞ -1，最终值非负，中间不取整，结果向上取整
 * f3: (a + b) * (1 - c) * d，c ＜ 1，最终值非负，中间不取整，向上取整
 * f4: (a – b) * (1 - c) * d，c ＜ 1，最终值非负，中间不取整，向上取整
 * f5: max{a + b，c * d}，向上取整
 * f6: (a + b / k1) * (1 + c) ，k1 ＞ 0 ，c ＞ -1，最终值非负，向上取整
 *
 * <AUTHOR>
 */
public class FormulaUtils {
    private static final Logger LOGGER = LogManager.getLogger(FormulaUtils.class);
    /**
     * 是否需要打印公式日志
     */
    private static final boolean IS_LOG_ABLE = true;
    public static final long RATIO = (long) MathUtils.TEN_THOUSAND;

    /**
     * (a + b) * (1 + c) * d
     * c: 万分比
     */
    public static long f1(long a, long b, long c, long d) {
        try {
            if (c <= -RATIO) {
                throw new ArithmeticException("result < 0");
            }
            double v = Math.addExact(a, b) * (1 + MathUtils.tenThousandPoint(c)) * d;
            assertNotLongOverFlow(v);
            assertIsPositive(v);
            v = round(v);
            long r = (long) Math.ceil(v);
            log("f1: (a + b) * (1 + c) * d = {}, a:{}, b:{}, c:{}, d:{}", r, a, b, c, d);
            return r;
        } catch (ArithmeticException e) {
            throw new ArithmeticException(StringUtils.format("{}. f1: (a + b) * (1 + c) * d, a:{}, b:{}, c:{}, d:{}", e.getMessage(), a, b, c, d));
        }
    }

    /**
     * (a - b) / (1 + c) * d
     * c: 万分比
     */
    public static long f2(long a, long b, long c, long d) {
        try {
            if (c <= -RATIO) {
                throw new ArithmeticException("/ by zero or result < 0");
            }
            double v = Math.subtractExact(a, b) / (1 + MathUtils.tenThousandPoint(c)) * d;
            assertNotLongOverFlow(v);
            assertIsPositive(v);
            v = round(v);
            long r = (long) Math.ceil(v);
            log("f2: (a - b) / (1 + c) * d = {}, a:{}, b:{}, c:{}, d:{}", r, a, b, c, d);
            return r;
        } catch (ArithmeticException e) {
            throw new ArithmeticException(StringUtils.format("{}. f2: (a - b) / (1 + c) * d, a:{}, b:{}, c:{}, d:{}", e.getMessage(), a, b, c, d));
        }
    }

    /**
     * (a + b) * (1 - c) * d
     * c: 万分比
     */
    public static long f3(long a, long b, long c, long d) {
        try {
            if (c >= RATIO) {
                throw new ArithmeticException("result <= 0");
            }
            double v = Math.addExact(a, b) * (1 - MathUtils.tenThousandPoint(c)) * d;
            assertNotLongOverFlow(v);
            assertIsPositive(v);
            v = round(v);
            long r = (long) Math.ceil(v);
            log("f3: (a + b) * (1 - c) * d = {}, a:{}, b:{}, c:{}, d:{}", r, a, b, c, d);
            return r;
        } catch (ArithmeticException e) {
            throw new ArithmeticException(StringUtils.format("{}. f3: (a + b) * (1 - c) * d, a:{}, b:{}, c:{}, d:{}", e.getMessage(), a, b, c, d));
        }
    }

    /**
     * (a - b) * (1 - c) * d
     * c: 万分比
     */
    public static long f4(long a, long b, long c, long d) {
        try {
            if (c >= RATIO) {
                throw new ArithmeticException("result <= 0");
            }
            double v = Math.subtractExact(a, b) * (1 - MathUtils.tenThousandPoint(c)) * d;
            assertNotLongOverFlow(v);
            assertIsPositive(v);
            v = round(v);
            long r = (long) Math.ceil(v);
            log("f4: (a - b) * (1 - c) * d = {}, a:{}, b:{}, c:{}, d:{}", r, a, b, c, d);
            return r;
        } catch (ArithmeticException e) {
            throw new ArithmeticException(StringUtils.format("{}. f4: (a - b) * (1 - c) * d, a:{}, b:{}, c:{}, d:{}", e.getMessage(), a, b, c, d));
        }
    }

    /**
     * max{ a + b, c * d }
     * d: 万分比
     */
    public static long f5(long a, long b, long c, long d) {
        try {
            double v = c * MathUtils.tenThousandPoint(d);
            assertNotLongOverFlow(v);
            v = round(v);
            long r = Math.max(Math.addExact(a, b), (long) Math.ceil(v));
            assertIsPositive(r);
            log("f5: max{a + b, c * d} = {}, a:{}, b:{}, c:{}, d:{}", r, a, b, c, d);
            return r;
        } catch (ArithmeticException e) {
            throw new ArithmeticException(StringUtils.format("{}. f5: max{a + b, c * d}, a:{}, b:{}, c:{}, d:{}", e.getMessage(), a, b, c, d));
        }
    }

    /**
     * (a + b / k1) * (1 + c)
     * c: 万分比
     */
    public static long f6(long a, long b, long k1, long c) {
        try {
            if (k1 <= 0 || c <= -RATIO) {
                throw new ArithmeticException("/ by zero or result <= 0");
            }
            double v = (a + (double) b / k1) * (1 + MathUtils.tenThousandPoint(c));
            assertNotLongOverFlow(v);
            assertIsPositive(v);
            v = round(v);
            long r = (long) Math.ceil(v);
            log("f6: (a + b / k1) * (1 + c) = {}, a:{}, b:{}, k1:{}, c:{}", r, a, b, k1, c);
            return r;
        } catch (ArithmeticException e) {
            throw new ArithmeticException(StringUtils.format("{}. f6: (a + b / k1) * (1 + c), a:{}, b:{}, k1:{}, c:{}", e.getMessage(), a, b, k1, c));
        }
    }

    public static void assertNotLongOverFlow(double v) {
        if (isDoubleOverFlow(v) || isLongOverFlow(v)) {
            throw new ArithmeticException("result overflow");
        }
    }

    public static void assertIsPositive(double v) {
        if (v < 0) {
            throw new ArithmeticException(StringUtils.format("result = {} < 0", v));
        }
    }

    private static boolean isDoubleOverFlow(double n) {
        return Double.isInfinite(n) || Double.isNaN(n);
    }

    private static boolean isLongOverFlow(double n) {
        return n > Long.MAX_VALUE || n < Long.MIN_VALUE;
    }

    private static void log(String msg, Object... params) {
        if (IS_LOG_ABLE) {
            LOGGER.trace(msg, params);
        }
    }

    /**
     * 保留两位小数
     */
    private static double round(double value) {
        return new BigDecimal(value).setScale(4, RoundingMode.HALF_EVEN).doubleValue();
    }
}




package com.yorha.common.exports;

import io.prometheus.client.CollectorRegistry;

/**
 * Gemini定制化的p8s导出器。
 *
 * <AUTHOR>
 */
public class GeminiExports {
    private static boolean initialized = false;

    /**
     * Register the default Hotspot collectors with the default
     * registry. It is safe to call this method multiple times, as
     * this will only register the collectors once.
     */
    public static synchronized void initialize(final String busId) {
        if (!initialized) {
            register(busId, CollectorRegistry.defaultRegistry);
            initialized = true;
        }
    }

    /**
     * Get CollectorRegistry for GeminiExports. It will throw RuntimeException when not initialized.
     *
     * @return CollectorRegistry instance.
     */
    public static synchronized CollectorRegistry getCollectorRegistry() {
        if (!initialized) {
            throw new RuntimeException("invoke initialize first!");
        }
        return CollectorRegistry.defaultRegistry;
    }

    /**
     * Register the default Hotspot collectors with the given registry.
     */
    public static void register(final String busId, final CollectorRegistry registry) {
        new GeminiStandardExports(busId).register(registry);
        new GeminiMemoryPoolsExports(busId).register(registry);
        new GeminiGarbageCollectorExports(busId).register(registry);
        new GeminiThreadExports(busId).register(registry);
        new GeminiMemoryAllocationExports(busId).register(registry);
        new GeminiBufferPoolsExports(busId).register(registry);
        new GeminiClassLoadingExports(busId).register(registry);
        new GeminiVersionExports(busId).register(registry);
    }
}

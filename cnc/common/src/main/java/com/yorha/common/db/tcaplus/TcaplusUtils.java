package com.yorha.common.db.tcaplus;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tcaplus.client.impl.ResponseLite;
import com.tencent.tcaplus.util.TCaplusException;
import com.yorha.common.concurrent.FiberAsync;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public final class TcaplusUtils {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusUtils.class);

    /**
     * Tcaplus超时时间上限。
     */
    public static final int TCAPLUS_TIMEOUT_MS = 4_000;

    /**
     * Tcaplus Real Cost告警上限。
     */
    private static final long MAX_TCAPLUS_REAL_COST_MS = 100;


    private TcaplusUtils() {
    }

    private static class TcaplusFiberAsync extends FiberAsync<Response, Exception> {
        private final Client client;
        private final Request request;

        TcaplusFiberAsync(Client client, Request request) {
            this.client = client;
            this.request = request;
        }

        @Override
        protected void requestAsync() {
            TcaplusUtils.requestResponseAsync(this.client, this.request, this::asyncCompleted);
        }
    }

    private static class TcaplusBatchFiberAsync extends FiberAsync<List<Response>, Exception> {
        private final Client client;
        private final Request request;

        TcaplusBatchFiberAsync(Client client, Request request) {
            this.client = client;
            this.request = request;
        }

        @Override
        protected void requestAsync() {
            TcaplusUtils.requestMultipleResponseAsync(this.client, this.request, this::asyncCompleted);
        }
    }


    public static Response requestResponse(final Client client, final Request request, final long timeoutDurationsMs) {
        if (timeoutDurationsMs <= 0) {
            throw new RuntimeException("timeoutDurationMs:" + timeoutDurationsMs);
        }
        if (request.getMultiResponseFlag() == 1) {
            throw new RuntimeException("multiple response");
        }
        TcaplusFiberAsync tcaplusFiberAsync = new TcaplusFiberAsync(client, request);
        Response response;
        try {
            response = tcaplusFiberAsync.run(timeoutDurationsMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            throw new TcaplusDBException(e);
        }
        return response;
    }

    public static List<Response> requestMultipleResponse(final Client client, final Request request, final long timeoutDurationsMs) {
        if (timeoutDurationsMs <= 0) {
            throw new RuntimeException("timeoutDurationMs <= 0");
        }
        if (request.getMultiResponseFlag() != 1) {
            throw new RuntimeException("not multiple response");
        }
        TcaplusBatchFiberAsync tcaplusBatchFiberAsync = new TcaplusBatchFiberAsync(client, request);
        List<Response> response;
        try {
            response = tcaplusBatchFiberAsync.run(timeoutDurationsMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            throw new TcaplusDBException(e);
        }
        return response;
    }

    public static void requestResponseAsync(final Client client, final Request request, final Consumer<Response> cb) {
        if (request.getMultiResponseFlag() == 1) {
            throw new RuntimeException("multiple response");
        }
        final long tcaplusRealStartTsMs = SystemClock.nowNative();
        try {
            client.post(request, (r) -> {
                // r = getResultFromMock(r);
                long tcaplusFutureStartTsMs = SystemClock.nowNative();
                // tcaplus发送到回调触发的耗时
                long tcaplusRealCostMs = tcaplusFutureStartTsMs - tcaplusRealStartTsMs;
                if (tcaplusRealCostMs > MAX_TCAPLUS_REAL_COST_MS) {
                    LOGGER.warn("gemini_perf TcaplusUtils requestResponseAsync, cmd={}, tableName={}, realCost={}ms",
                            request.getCmd(), request.getTableName(), tcaplusRealCostMs);
                }
                cb.accept(r);
            });
        } catch (TCaplusException e) {
            final int overTimeErrorCode = TcaplusErrorCode.SVR_ERR_FAIL_PROXY_STOPPING.getValue();
            LOGGER.error("requestResponseAsync fail request={}, use overTimeErrorCode={} e=", request, overTimeErrorCode, e);
            ResponseLite response = new ResponseLite();
            response.setResult(overTimeErrorCode);
            cb.accept(response);
        }

    }

    public static void requestMultipleResponseAsync(final Client client, final Request request, final Consumer<List<Response>> cb) {
        if (request.getMultiResponseFlag() != 1) {
            throw new RuntimeException("not multiple response");
        }
        final long tcaplusRealStartTsMs = SystemClock.nowNative();
        List<Response> responseList = new LinkedList<>();
        try {
            client.post(request, (r) -> {
                // r = getResultFromMock(r);
                responseList.add(r);
                if (r.hasMore()) {
                    return;
                }
                long tcaplusFutureStartTsMs = SystemClock.nowNative();
                // tcaplus发送到回调触发的耗时
                long tcaplusRealCostMs = tcaplusFutureStartTsMs - tcaplusRealStartTsMs;
                if (tcaplusRealCostMs > MAX_TCAPLUS_REAL_COST_MS) {
                    LOGGER.warn("gemini_perf TcaplusUtils requestMultipleResponseAsync, tableName={}, cmd={}, realCost:{}ms",
                            request.getTableName(), request.getCmd(), tcaplusRealCostMs);
                }
                cb.accept(responseList);
            });
        } catch (TCaplusException e) {
            final int overTimeErrorCode = TcaplusErrorCode.SVR_ERR_FAIL_PROXY_STOPPING.getValue();
            LOGGER.error("requestMultipleResponseAsync fail request={}, use overTimeErrorCode={} e=", request, overTimeErrorCode, e);
            ResponseLite response = new ResponseLite();
            response.setResult(overTimeErrorCode);
            responseList.add(response);
            cb.accept(responseList);
        }


    }

//    private static Response getResultFromMock(final Response response) {
//        final int cmd = response.getCmd();
//        switch (cmd) {
//            case TcaplusProtocolCsConstants.TCAPLUS_CMD_INSERT_RES:
//            case TcaplusProtocolCsConstants.TCAPLUS_CMD_UPDATE_RES:
//            case TcaplusProtocolCsConstants.TCAPLUS_CMD_DELETE_RES:
//            case TcaplusProtocolCsConstants.TCAPLUS_CMD_INCREASE_RES:
//            case TcaplusProtocolCsConstants.TCAPLUS_CMD_REPLACE_RES:
//                break;
//            default:
//                return response;
//        }
//        final Random random = new Random();
//        final int i = random.nextInt(10);
//        if (i < 4) {
//            final ResponseLite responseLite = new ResponseLite();
//            responseLite.reset();
//            responseLite.setResult(TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT.getValue());
//            return responseLite;
//        }
//        if (i < 7) {
//            final ResponseLite responseLite = new ResponseLite();
//            responseLite.reset();
//            responseLite.setResult(TcaplusErrorCode.GEN_ERR_ERR.getValue());
//            return responseLite;
//        }
//        return response;
//    }

    /**
     * 获得tcaplus表名
     */
    public static <T extends Message.Builder> String getTableName(T req) {
        return PbFieldMetaCaches.getMetaData(req).tableName;
    }

    /**
     * 格式: {key1}={val1}, {key2}={val2}
     *
     * @return 返回主键名&主键值
     */
    public static String getKeys(Message.Builder req) {
        StringBuilder sb = new StringBuilder();
        List<Descriptors.FieldDescriptor> keyFields = PbFieldMetaCaches.getMetaData(req).keyFieldsList;
        for (Descriptors.FieldDescriptor field : keyFields) {
            final String fieldName = field.getName();
            if (!req.hasField(field)) {
                sb.append(StringUtils.format("{}=None, ", fieldName));
            } else {
                sb.append(StringUtils.format("{}={}, ", fieldName, req.getField(field)));
            }
        }
        sb.delete(sb.length() - 2, sb.length());
        return sb.toString();
    }

}

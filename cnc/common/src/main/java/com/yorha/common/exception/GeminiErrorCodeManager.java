package com.yorha.common.exception;

import java.util.HashMap;
import java.util.Map;

/**
 * IErrorCode 单例管理器
 *
 * <AUTHOR>
 */
public class GeminiErrorCodeManager {
    /**
     * 服务端错误码的映射表
     */
    private static volatile Map<Integer, IErrorCode> errorCodeMap = new HashMap<>();

    /**
     * 载入服务端服务码
     *
     * @param codeMap 待载入的服务端错误码映射表
     */
    public static void init(Map<Integer, IErrorCode> codeMap) {
        if (!errorCodeMap.isEmpty()) {
            return;
        }
        Map<Integer, IErrorCode> map;
        map = new HashMap<>(errorCodeMap);
        map.putAll(codeMap);
        errorCodeMap = Map.copyOf(map);
    }

    public static IErrorCode getGeminiErrorCode(int codeId) {
        return errorCodeMap.get(codeId);
    }
}

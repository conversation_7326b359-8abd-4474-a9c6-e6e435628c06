package com.yorha.common.io;

import com.esotericsoftware.reflectasm.MethodAccess;
import com.yorha.common.reflections.JavaClassScanner;
import com.yorha.common.utils.reflect.MethodUtils;
import com.yorha.common.utils.reflect.MethodWrapper;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * cs消息管理器
 *
 */
public class CommandMgr {
    private static final Logger LOGGER = LogManager.getLogger(CommandMgr.class);

    protected static final HashMap<Integer, Command> COMMAND_STORE = new HashMap<>();

    private CommandMgr() {
    }

    private static class InstanceHolder {
        private static final CommandMgr INSTANCE = new CommandMgr();
    }

    public static CommandMgr getInstance() {
        return CommandMgr.InstanceHolder.INSTANCE;
    }

    public void init(String controllerPackage) {
        LOGGER.info("gemini_system init command start");
        int commandNum = 0;
        // entity指令注入
        final JavaClassScanner scanner = new JavaClassScanner();
        // 获取所有controller类
        Set<Class<?>> classes = scanner.getTypesAnnotatedWith(controllerPackage, Controller.class);
        for (Class<?> controllerClass : classes) {
            Controller controllerAnnotation = controllerClass.getAnnotation(Controller.class);
            CommonEnum.ModuleEnum module = controllerAnnotation.module();
            Object instance;
            try {
                instance = controllerClass.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                LOGGER.error("", e);
                continue;
            }
            // 获取该类上所有command注解的函数
            MethodAccess access = MethodAccess.get(controllerClass);
            List<Method> list = MethodUtils.getMethodList(controllerClass);
            for (Method method : list) {
                Annotation[] array = method.getAnnotations();
                if (array.length == 0) {
                    continue;
                }
                CommandMapping mapping = method.getAnnotation(CommandMapping.class);
                int index = access.getIndex(method.getName(), method.getParameterTypes());
                MethodWrapper wrapper = new MethodWrapper(instance, method, access, index);
                final Command command = new Command(wrapper, module, mapping.isModuleWhite(), mapping.code());
                COMMAND_STORE.put(command.getMsgType(), command);
                commandNum++;
            }
        }
        LOGGER.info("gemini_system init command end, num:{}", commandNum);
    }

    public Command getCommand(int code) {
        return COMMAND_STORE.get(code);
    }
}

package com.yorha.common.lock;

import java.util.concurrent.atomic.AtomicReference;

/**
 * 自旋锁，不可重入
 *
 * <AUTHOR>
 * @date 2024/3/20
 */
public class SpinLock {
    private final AtomicReference<Thread> owner = new AtomicReference<>();

    public void lock() {
        Thread currentThread = Thread.currentThread();
        // 尝试设置当前线程为锁的拥有者，如果当前没有线程拥有锁（即owner为null），则成功获取锁
        while (!owner.compareAndSet(null, currentThread)) {
            // 循环等待，直到当前线程成功获取锁
        }
    }

    public void unlock() {
        Thread currentThread = Thread.currentThread();
        // 只有锁的拥有者才能释放锁，将owner设置为null
        owner.compareAndSet(currentThread, null);
    }
}

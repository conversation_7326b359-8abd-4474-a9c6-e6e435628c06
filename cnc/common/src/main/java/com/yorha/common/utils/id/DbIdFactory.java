package com.yorha.common.utils.id;

import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.db.DbManager;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.IncreaseResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Random;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * db发号器基类
 *
 * <AUTHOR>
 */
public class DbIdFactory implements IIdFactory {
    private static final Logger LOGGER = LogManager.getLogger(DbIdFactory.class);
    /**
     * 发号器对应key。
     */
    protected final String key;


    public DbIdFactory(final String key) {
        this.key = key;
    }


    @Override
    public final long nextId(String reason) {
        final long startTsMs = SystemClock.nowNative();
        while (true) {
            final long l = this.doNextId();
            if (l >= this.getStartId()) {
                LOGGER.info("nextId id={}, reason={}", l, reason);
                return l;
            }
            final long costTimeMs = SystemClock.nowNative() - startTsMs;
            if (costTimeMs > DbUtil.DB_RETRY_TIMEOUT_MS) {
                break;
            }
            WechatLog.error("{} nextId get ID fail, reason={}", this.getClass().getSimpleName(), reason);
            // 如果遇到出错或者是冲突，就本地休息100 ~ 200ms的随机时间
            LockSupport.parkNanos(this, TimeUnit.MILLISECONDS.toNanos(100 + (new Random()).nextInt(100)));
        }
        LOGGER.error("{} nextId fail", this.getClass().getSimpleName());
        throw new RuntimeException(this.getClass().getSimpleName() + " Id Factory Not Available");
    }


    private long getNextIdByFiber() {
        // 使用db进行发号
        final TcaplusDb.IdFactoryTable.Builder increaseReq = TcaplusDb.IdFactoryTable.newBuilder()
                .setZoneId(ActorSystem.GLOBAL_ZONE)
                .setKey(this.key)
                .setNextId(1);
        final IncreaseOption option = IncreaseOption.newBuilder()
                .setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL)
                .build();

        final IncreaseResult<TcaplusDb.IdFactoryTable.Builder> increaseResult = this.runIncrease(increaseReq, option);
        if (increaseResult == null) {
            return -1;
        }
        if (increaseResult.isRecordNotExist()) {
            // 测试环境、开发环境，初始序号放大，帮忙暴露问题
            final long startId;
            if (ServerContext.isDevEnv() || ServerContext.isTestEnv()) {
                startId = 1L << 32;
            } else {
                startId = this.getStartId();
            }

            // 发号器失败，尝试插入发号段
            final TcaplusDb.IdFactoryTable.Builder insertReq = TcaplusDb.IdFactoryTable.newBuilder()
                    .setKey(this.key)
                    .setZoneId(ActorSystem.GLOBAL_ZONE)
                    .setNextId(startId);
            final InsertResult<TcaplusDb.IdFactoryTable.Builder> insertResult = this.runInsert(insertReq, InsertOption.DEFAULT_VALUE);
            if (insertResult.isOk()) {
                LOGGER.info("{} insert {}!", this.getClass().getSimpleName(), insertReq);
                return startId;
            }
            LOGGER.warn("{} insert {} fail! result {}!", this.getClass().getSimpleName(), insertReq, insertResult);
            return -1;
        }

        // 发号失败
        if (!increaseResult.isOk()) {
            LOGGER.error("{} increase {} fail! result {}!", this.getClass().getSimpleName(), increaseReq, increaseResult);
            return -1;
        }

        // 发号成功
        return increaseResult.value.getNextId();
    }

    private long doNextId() {
        if (Thread.currentThread().isVirtual()) {
            return this.getNextIdByFiber();
        }
        FutureTask<Long> futureTask = new FutureTask<>(this::getNextIdByFiber);
        ConcurrentHelper.newFiber("dbIdFactory#nextId", futureTask).start();
        try {
            return futureTask.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("{} this={} nextId fail!", this.getClass().getSimpleName(), e);
        }
        return -1;
    }

    /**
     * 发号器初始编码段
     */
    private static final long START_ID = 10;


    protected long getStartId() {
        return DbIdFactory.START_ID;
    }


    protected IncreaseResult<TcaplusDb.IdFactoryTable.Builder> runIncrease(TcaplusDb.IdFactoryTable.Builder builder, IncreaseOption option) {
        return DbManager.getInstance().increase(builder, option);
    }


    protected InsertResult<TcaplusDb.IdFactoryTable.Builder> runInsert(TcaplusDb.IdFactoryTable.Builder builder, InsertOption option) {
        return DbManager.getInstance().insert(builder, option);
    }


    @Override
    public String toString() {
        return "DbIdFactory{" +
                "key='" + key + '}';
    }

}

package com.yorha.common.db.tcaplus;

import com.google.protobuf.*;
import com.tencent.tcaplus.client.Record;
import com.yorha.gemini.utils.StringUtils;

import java.io.ByteArrayOutputStream;


/**
 * <AUTHOR>
 */
public class PbDynamicHandler {

    private interface FieldInterface {
        long setBlob(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setByteString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setDouble(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setFloat(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setInt(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setLong(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setShort(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);

        long setString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder);
    }

    private static class KeyFieldSetter implements FieldInterface {
        @Override
        public long setBlob(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            byte[] bytes = ((Message) value).toByteArray();
            try {
                record.setKeyBlob(desc.getName(), bytes);
            } catch (Exception e) {
                throw new TcaplusDBException(StringUtils.format("setBlob failed {}", value), e);
            }
            return bytes.length;
        }

        @Override
        public long setByteString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            final byte[] bytes = ((ByteString) value).toByteArray();
            try {
                record.setKeyBlob(desc.getName(), bytes);
            } catch (Exception e) {
                throw new TcaplusDBException(StringUtils.format("setByteString failed {}", value), e);
            }
            return bytes.length;
        }

        @Override
        public long setDouble(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setKeyDouble(desc.getName(), (Double) value);
            return Double.BYTES;
        }

        @Override
        public long setFloat(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setKeyFloat(desc.getName(), (Float) value);
            return Float.BYTES;
        }

        @Override
        public long setInt(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            if (desc.getJavaType() == Descriptors.FieldDescriptor.JavaType.ENUM) {
                record.setKeyInt(desc.getName(), ((ProtocolMessageEnum) value).getNumber());
            } else {
                record.setKeyInt(desc.getName(), (Integer) value);
            }
            return Integer.BYTES;
        }

        @Override
        public long setLong(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setKeyLong(desc.getName(), (Long) value);
            return Long.BYTES;
        }

        @Override
        public long setShort(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setKeyShort(desc.getName(), (Short) value);
            return Short.BYTES;
        }

        @Override
        public long setString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setKeyString(desc.getName(), (String) value);
            return ((String) value).length();
        }
    }

    private static class ValueFieldSetter implements FieldInterface {
        @Override
        public long setBlob(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            byte[] bytes = ((Message) value).toByteArray();
            try {
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                os.write(bytes, 0, bytes.length);
                record.setValueBlob(desc.getName(), os.toByteArray());
                os.reset();
            } catch (Exception e) {
                throw new TcaplusDBException(StringUtils.format("setBlob failed {}", value), e);
            }
            return bytes.length;
        }

        @Override
        public long setByteString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            final byte[] bytes = ((ByteString) value).toByteArray();
            try {
                record.setValueBlob(desc.getName(), bytes);
            } catch (Exception e) {
                throw new TcaplusDBException(StringUtils.format("setByteString failed {}", value), e);
            }
            return bytes.length;
        }

        @Override
        public long setDouble(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setValueDouble(desc.getName(), (Double) value);
            return Double.BYTES;
        }

        @Override
        public long setFloat(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setValueFloat(desc.getName(), (Float) value);
            return Float.BYTES;
        }

        @Override
        public long setInt(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            if (desc.getJavaType() == Descriptors.FieldDescriptor.JavaType.ENUM) {
                record.setValueInt(desc.getName(), ((ProtocolMessageEnum) value).getNumber());
            } else {
                record.setValueInt(desc.getName(), (Integer) value);
            }
            return Integer.BYTES;
        }

        @Override
        public long setLong(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setValueLong(desc.getName(), (Long) value);
            return Long.BYTES;
        }

        @Override
        public long setShort(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setValueShort(desc.getName(), (Short) value);
            return Short.BYTES;
        }

        @Override
        public long setString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            record.setValueString(desc.getName(), (String) value);
            return ((String) value).length();
        }
    }

    private static class KeyFieldGetter implements FieldInterface {
        @Override
        public long setBlob(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            long size;
            try {
                byte[] bytes = record.getKeyBlob(desc.getName());
                size = bytes.length;
                DynamicMessage dynamicMessage = DynamicMessage.parseFrom(desc.getMessageType(), bytes);
                builder.setField(desc, dynamicMessage);
            } catch (Exception e) {
                throw new TcaplusDBException("setBlob failed", e);
            }
            return size;
        }


        @Override
        public long setByteString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            long size;
            try {
                byte[] bytes = record.getKeyBlob(desc.getName());
                size = bytes.length;
                builder.setField(desc, ByteString.copyFrom(bytes));
            } catch (Exception e) {
                throw new TcaplusDBException("setByteString failed", e);
            }
            return size;
        }

        @Override
        public long setDouble(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getKeyDouble(desc.getName()));
            return Double.BYTES;
        }

        @Override
        public long setFloat(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getKeyFloat(desc.getName()));
            return Float.BYTES;
        }

        @Override
        public long setInt(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            if (desc.getJavaType() == Descriptors.FieldDescriptor.JavaType.ENUM) {
                builder.setField(desc, desc.getEnumType().getValues().get(record.getKeyInt(desc.getName())));
            } else {
                builder.setField(desc, record.getKeyInt(desc.getName()));
            }
            return Integer.BYTES;
        }

        @Override
        public long setLong(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getKeyLong(desc.getName()));
            return Long.BYTES;
        }

        @Override
        public long setShort(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getKeyShort(desc.getName()));
            return Short.BYTES;
        }

        @Override
        public long setString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            String str = record.getKeyString(desc.getName());
            builder.setField(desc, str);
            return str.length();
        }
    }

    private static class ValueFieldGetter implements FieldInterface {
        @Override
        public long setBlob(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            long size;
            try {
                byte[] bytes = record.getValueBlob(desc.getName());
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                os.write(bytes, 0, bytes.length);
                size = os.size();
                DynamicMessage dynamicMessage = DynamicMessage.parseFrom(desc.getMessageType(), os.toByteArray());
                builder.setField(desc, dynamicMessage);
            } catch (Exception e) {
                throw new TcaplusDBException("setBlob failed", e);
            }
            return size;
        }


        @Override
        public long setByteString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            long size;
            try {
                byte[] bytes = record.getValueBlob(desc.getName());
                size = bytes.length;
                builder.setField(desc, ByteString.copyFrom(bytes));
            } catch (Exception e) {
                throw new TcaplusDBException("setByteString failed", e);
            }
            return size;
        }

        @Override
        public long setDouble(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getValueDouble(desc.getName()));
            return Double.BYTES;
        }

        @Override
        public long setFloat(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getValueFloat(desc.getName()));
            return Float.BYTES;
        }

        @Override
        public long setInt(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            if (desc.getJavaType() == Descriptors.FieldDescriptor.JavaType.ENUM) {
                builder.setField(desc, desc.getEnumType().getValues().get(record.getValueInt(desc.getName())));
            } else {
                builder.setField(desc, record.getValueInt(desc.getName()));
            }
            return Integer.BYTES;
        }

        @Override
        public long setLong(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getValueLong(desc.getName()));
            return Long.BYTES;
        }

        @Override
        public long setShort(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            builder.setField(desc, record.getValueShort(desc.getName()));
            return Short.BYTES;
        }

        @Override
        public long setString(Record record, Descriptors.FieldDescriptor desc, Object value, Message.Builder builder) {
            String string = record.getValueString(desc.getName());
            builder.setField(desc, string);
            return string.length();
        }
    }

    private static final KeyFieldSetter KEY_FIELD_SETTER = new KeyFieldSetter();
    private static final KeyFieldGetter KEY_FIELD_GETTER = new KeyFieldGetter();
    private static final ValueFieldSetter VALUE_FIELD_SETTER = new ValueFieldSetter();
    private static final ValueFieldGetter VALUE_FIELD_GETTER = new ValueFieldGetter();

    private static long setField(FieldInterface setter, Record record, Descriptors.FieldDescriptor desc, Object fieldData, Message.Builder builder) {
        //LOGGER.debug("set field {}", desc.getName());
        long size;
        switch (desc.getJavaType()) {
            case INT:
            case ENUM:
                size = setter.setInt(record, desc, fieldData, builder);
                break;
            case LONG:
                size = setter.setLong(record, desc, fieldData, builder);
                break;
            case FLOAT:
                size = setter.setFloat(record, desc, fieldData, builder);
                break;
            case DOUBLE:
                size = setter.setDouble(record, desc, fieldData, builder);
                break;
            case STRING:
                size = setter.setString(record, desc, fieldData, builder);
                break;
            case MESSAGE:
                size = setter.setBlob(record, desc, fieldData, builder);
                break;
            case BYTE_STRING:
                size = setter.setByteString(record, desc, fieldData, builder);
                break;
            case BOOLEAN:
            default:
                throw new TcaplusDBException(StringUtils.format("Invalid tcaplus table value type {}", desc.getType().getJavaType()));
        }
        return size;
    }

    /**
     * 通过fieldData设置Request里的Record的Key
     *
     * @param record    数据记录
     * @param desc      属性描述
     * @param fieldData 属性的值
     * @return 设置到Record的数据字节数
     */
    public static long setKeyField(Record record, Descriptors.FieldDescriptor desc, Object fieldData) {
        return setField(KEY_FIELD_SETTER, record, desc, fieldData, null);
    }

    /**
     * 通过fieldData设置Request里的Record的Value
     *
     * @param record    数据记录
     * @param desc      属性描述
     * @param fieldData 属性的值
     * @return 设置到Record的数据字节数
     */
    public static long setValueField(Record record, Descriptors.FieldDescriptor desc, Object fieldData) {
        return setField(VALUE_FIELD_SETTER, record, desc, fieldData, null);
    }

    /**
     * 通过Response里的Record设置builder
     *
     * @param record  数据记录
     * @param desc    数据字段的描述
     * @param builder 数据字段的容器
     * @return 从record读取的byte数量
     */
    public static long getKeyField(Record record, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        return setField(KEY_FIELD_GETTER, record, desc, null, builder);
    }

    /**
     * 通过Response里的Record设置builder
     *
     * @param record  数据记录
     * @param desc    数据字段的描述
     * @param builder 数据字段的容器
     * @return 从record读取的byte数量
     */
    public static long getValueField(Record record, Descriptors.FieldDescriptor desc, Message.Builder builder) {
        return setField(VALUE_FIELD_GETTER, record, desc, null, builder);
    }
}

package com.yorha.common.helper;

import com.google.common.collect.Lists;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.result.DeleteResult;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.language.ServerLanguageTemplateService;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.NameType;
import com.yorha.proto.SsName;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.yorha.common.resource.ResLoader.getResHolder;

/**
 * <AUTHOR>
 */
public class NameHelper {
    private static final Logger LOGGER = LogManager.getLogger(NameHelper.class);

    public static final String NAME_REQUEST_RELEASE = "release";

    public static final String NAME_REQUEST_OCCUPY = "occupy";

    public static String newPlayerName(CommonEnum.Language lang, int zoneId, int index) {
        List<Character> stringSeedPool = StringUtils.STRING_SEED_POOL;
        if (ServerContext.isChinaVersion()) {
            lang = CommonEnum.Language.zh;
            stringSeedPool = StringUtils.CHINA_SEED_POOL;
        }
        int suffixLength = getResHolder().getConstTemplate(ConstTemplate.class).getCommanderRandomNameLen();
        StringBuilder playerName = new StringBuilder(suffixLength);
        // id生成规则 zoneId*10w + index
        int id = zoneId * 100000 + index;
        // 映射到字符串
        int size = stringSeedPool.size();
        while (id > 0 || playerName.length() < suffixLength) {
            int i = id % size;
            playerName.append(stringSeedPool.get(i));
            id = id / size;
        }
        var namePrefixId = getResHolder().getConstTemplate(ConstTemplate.class).getPlayerNamePrefix();
        String namePrefix = ResHolder.getResService(ServerLanguageTemplateService.class).getServerLanguage(lang, namePrefixId);
        return namePrefix + playerName;
    }

    /**
     * 生成军团名字
     */
    public static String newClanName(GameActorWithCall actor, long clanId) {
        String clanNamePrefix = "Alliance_";
        int suffixLength = 5;
        int loopTimes = 0;

        while (true) {
            String clanNameSuffix = StringUtils.randomSuffix(suffixLength);
            String clanName = clanNamePrefix + clanNameSuffix;
            ErrorCode errorCode = occupyName(actor, NameType.CLAN_NAME, clanName, clanId);
            if (errorCode.isOk()) {
                // 成功了
                return clanName;
            } else {
                // 再来一次随机名字
                if (++loopTimes > GameLogicConstants.MAX_RETRY_TIMES) {
                    // 太多次了，打断
                    throw new GeminiException("newClanName failed.clanId:{}", clanId);
                }
            }
        }
    }

    /**
     * 生成军团简称
     */
    public static String newClanSName(GameActorWithCall actor, long clanId) {
        int loopTimes = 0;
        while (true) {
            String clanSName = StringUtils.randomSuffix(RandomUtils.randomBetween(3, 4));
            ErrorCode errorCode = occupyName(actor, NameType.CLAN_SIMPLE_NAME, clanSName, clanId);
            if (errorCode.isOk()) {
                // 成功了
                return clanSName;
            } else {
                // 再来一次随机名字
                if (++loopTimes > GameLogicConstants.MAX_RETRY_TIMES) {
                    // 太多次了，打断
                    throw new GeminiException("newClanSName failed.clanId:{}", clanId);
                }
            }
        }
    }

    public static ErrorCode checkNameRepeat(GameActorWithCall actor, NameType nameType, String name) {
        TcaplusDb.NameTable.Builder req = TcaplusDb.NameTable.newBuilder().setNameType(nameType.getNumber()).setName(name);
        try {
            GetResult<TcaplusDb.NameTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
            if (DbUtil.isRecordNotExist(ans.getCode())) {
                return ErrorCode.OK;
            }
            if (!DbUtil.isOk(ans.getCode())) {
                WechatLog.error("nameType:{} name:{} checkNameRepeat db fail,code: {}", nameType, name, ans.getCode());
                // db出问题了 还是返回重复吧   宁错杀不放过
                return buildErrorCodeWithType(nameType);
            }
        } catch (Exception e) {
            // call db出问题了 还是返回重复吧   宁错杀不放过
            LOGGER.error("nameType:{} name:{} checkNameRepeat db fail ", nameType, name, e);
        }
        return buildErrorCodeWithType(nameType);
    }

    public static long getNameOwner(GameActorWithCall actor, NameType nameType, String name) {
        TcaplusDb.NameTable.Builder req = TcaplusDb.NameTable.newBuilder().setNameType(nameType.getNumber()).setName(name);
        try {
            GetResult<TcaplusDb.NameTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req));
            if (DbUtil.isRecordNotExist(ans.getCode())) {
                return 0;
            }
            if (!DbUtil.isOk(ans.getCode())) {
                LOGGER.error("NameHelper getNameOwner, db fail, code={}", ans.getCode());
                return 0;
            }
            return ans.value.getOwnerId();
        } catch (Exception e) {
            return 0;
        }
    }

    public static ErrorCode occupyName(GameActorWithCall actor, NameType nameType, String name, long ownerId) {
        TcaplusDb.NameTable.Builder req = TcaplusDb.NameTable.newBuilder();
        req.setNameType(nameType.getNumber()).setName(name).setOwnerId(ownerId);
        InsertResult<TcaplusDb.NameTable.Builder> ans = actor.callGameDb(new InsertAsk<>(req));
        MonitorUnit.NAME_REQUEST_TOTAL.labels(ServerContext.getBusId(), NAME_REQUEST_OCCUPY).inc();
        if (!DbUtil.isOk(ans.getCode())) {
            LOGGER.error("occupyName insert nameType: {} name:{} ownerId: {}  NameTable db fail code: {}", nameType.getNumber(), name, ownerId, ans.getCode());
            return buildErrorCodeWithType(nameType);
        }
        LOGGER.info("occupyName name:{} ownerId:{}", name, ownerId);
        // 发送给nameActor 用于搜索。。
        syncToNameActor(actor, actor.getZoneId(), nameType, name, ownerId);
        return ErrorCode.OK;
    }

    public static ErrorCode occupyNames(GameActorWithCall actor, List<Pair<NameType, String>> namePairs, long ownerId) {
        List<Pair<NameType, String>> insertedNameList = new ArrayList<>();
        ArrayList<SsName.NamePair> list = Lists.newArrayList();
        for (Pair<NameType, String> namePair : namePairs) {
            TcaplusDb.NameTable.Builder req = TcaplusDb.NameTable.newBuilder();
            CommonEnum.NameType nameType = namePair.getFirst();
            String name = namePair.getSecond();
            req.setNameType(nameType.getNumber()).setName(name).setOwnerId(ownerId);
            try {
                InsertResult<TcaplusDb.NameTable.Builder> ans = actor.callGameDb(new InsertAsk<>(req));
                MonitorUnit.NAME_REQUEST_TOTAL.labels(ServerContext.getBusId(), NAME_REQUEST_OCCUPY).inc();
                if (DbUtil.isOk(ans.getCode())) {
                    insertedNameList.add(namePair);
                } else {
                    releaseNames(actor, insertedNameList, 0, false);
                    LOGGER.error("occupyNames insert nameType: {} name:{} ownerId: {}  NameTable db fail code: {}", nameType.getNumber(), name, ownerId, ans.getCode());
                    return buildErrorCodeWithType(nameType);
                }
            } catch (Exception e) {
                releaseNames(actor, insertedNameList, 0, false);
                throw e;
            }
            LOGGER.info("occupy name:{} ownerId:{}", name, ownerId);
            list.add(MsgHelper.buildNamePair(namePair.getFirst(), namePair.getSecond()));
        }
        // 发送给nameActor 用于搜索。。
        syncToNameActor(actor, list, ownerId);
        return ErrorCode.OK;
    }

    public static void syncToNameActor(AbstractActor actor, int zoneId, NameType nameType, String name, long ownerId) {
        SsName.OccupyBatchNameCmd.Builder builder = SsName.OccupyBatchNameCmd.newBuilder();
        ArrayList<SsName.NamePair> list = Lists.newArrayList();
        list.add(MsgHelper.buildNamePair(nameType, name));
        builder.setOwnerId(ownerId).addAllNamePair(list).build();

        actor.tell(RefFactory.ofName(zoneId), builder.build());
    }

    public static void syncToNameActor(AbstractActor actor, ArrayList<SsName.NamePair> list, long ownerId) {
        SsName.OccupyBatchNameCmd.Builder builder = SsName.OccupyBatchNameCmd.newBuilder();
        builder.setOwnerId(ownerId).addAllNamePair(list).build();
        actor.tell(RefFactory.ofName(actor.getZoneId()), builder.build());
    }

    /**
     * 批量释放名称
     */
    public static void releaseNames(GameActorWithCall actor, List<Pair<NameType, String>> namePairs, long ownerId, boolean needTellNameActor) {
        for (Pair<NameType, String> namePair : namePairs) {
            releaseName(actor, namePair.getFirst(), namePair.getSecond(), ownerId, needTellNameActor);
        }
    }

    /**
     * 释放名字 不抛异常 打wechatLog告警
     */
    public static void releaseName(GameActorWithCall actor, CommonEnum.NameType nameType, String name) {
        releaseName(actor, nameType, name, 0, false);
    }

    private static void releaseName(GameActorWithCall actor, CommonEnum.NameType nameType, String name, long ownerId, boolean needTellNameActor) {
        MonitorUnit.NAME_REQUEST_TOTAL.labels(ServerContext.getBusId(), NAME_REQUEST_RELEASE).inc();
        TcaplusDb.NameTable.Builder req = TcaplusDb.NameTable.newBuilder();
        req.setNameType(nameType.getNumber()).setName(name);
        try {
            DeleteResult ans = actor.callGameDb(new DeleteAsk<>(req));
            if (DbUtil.isRecordNotExist(ans.getCode())) {
                return;
            }
            if (!DbUtil.isOk(ans.getCode())) {
                WechatLog.error("nameType:{} name:{} delete NameTable db fail,code: {}", nameType, name, ans.getCode());
            } else {
                LOGGER.info("releaseName. name:{}", name);
            }
        } catch (Exception e) {
            WechatLog.error("nameType:{} name:{} delete NameTable db fail ", nameType, name, e);
        }
        if (!needTellNameActor) {
            return;
        }
        // 发送给nameActor删除
        SsName.ReleaseNameCmd.Builder builder = SsName.ReleaseNameCmd.newBuilder();
        actor.tellName(builder.setOwnerId(ownerId).setNameType(nameType).build());
    }

    private static ErrorCode buildErrorCodeWithType(CommonEnum.NameType nameType) {
        switch (nameType) {
            case CLAN_SIMPLE_NAME: {
                return ErrorCode.CLAN_DUPLICATE_SIMPLE_NAME;
            }
            case CLAN_NAME: {
                return ErrorCode.CLAN_DUPLICATE_NAME;
            }
            case PLAYER_NAME: {
                return ErrorCode.SYSTEM_NAME_DUPLICATE;
            }
            default: {
                LOGGER.error(" nameType:{} buildErrorCodeWithType fail. error", nameType.name());
                return ErrorCode.NAME_TYPE_NOT_EXIST;
            }
        }
    }
}

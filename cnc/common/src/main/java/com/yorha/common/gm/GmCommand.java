package com.yorha.common.gm;

import com.yorha.common.actorservice.AbstractActor;
import com.yorha.proto.CommonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface GmCommand<T extends AbstractActor> {
    /**
     * 执行Debug命令逻辑.
     *
     * @param actor 主体
     * @param args  参数指令，参数都是从第3个元素开始的
     */
    void execute(T actor, long playerId, Map<String, String> args);


    /**
     * 显示命令帮助格式.
     *
     * @return 帮助结果
     */
    default String showHelp() {
        return getClass().getSimpleName();
    }

    /**
     * 获取分组
     *
     * @return 获取分组
     */
    default CommonEnum.DebugGroup getGroup() {
        return CommonEnum.DebugGroup.DG_COMMON;
    }

    default boolean isScene() {
        return false;
    }

    default boolean isClan() {
        return false;
    }
}

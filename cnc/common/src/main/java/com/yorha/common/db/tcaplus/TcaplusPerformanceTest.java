package com.yorha.common.db.tcaplus;

import com.google.common.collect.Lists;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.ClientFactory;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.utils.RandomUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * Tcaplus性能测试
 * prod环境的10000\600\100的情况下，4个proxy，运行机器：2c8g，tps：6w，延迟9-11ms，ping延迟：0.2ms
 * prod环境的10000\600\100的情况下，4个proxy，运行机器：4c8g，tps：8-10w，延迟5-6ms，ping延迟：0.2ms
 * test环境的10000\600\100的情况下，2个proxy，运行机器：4c16g，tps：8-10w，延迟5-7ms，ping延迟：0.2ms
 * test环境的10000\600\100的情况下，4个proxy，运行机器：4c16g，tps：9-10w，延迟5-6ms，ping延迟：0.2ms
 *
 * <AUTHOR>
 */
public class TcaplusPerformanceTest {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusPerformanceTest.class);
    private static Client client;
    private static final String TABLE_NAME = "KVStoreTable";
    private static String[] array;

    /**
     * 量小的话，tps会很低，建议大点
     */
    private static int runTimesInOneBatch = 50_000;
    /**
     * 调的越大，延迟越大
     */
    private static int limit = 800;
    /**
     * 多跑一会儿，预热不预热的差距很大
     */
    private static int totalCycleTimes = 200;


    private static int appId = 3;
    private static int zoneId = 1;
    private static String tcaplusAddress = "tcp://************:9999";
    private static String password = "E6973034FAD7DCDC";

    private static final int BYTE_NUM = 200;
    private static final Map<Integer, byte[]> BYTE_MAP = new HashMap<>(BYTE_NUM);

    public TcaplusPerformanceTest() {
    }

    public static void main(String[] args) throws InterruptedException {
        LOGGER.error("gogogo");

        Random r = new Random();
        for (int i = 0; i < BYTE_NUM; i++) {
            byte[] a = new byte[200 * 1024];
            r.nextBytes(a);
            BYTE_MAP.put(i, a);
        }

        if (args.length >= 1) {
            limit = Integer.parseInt(args[0]);
        }
        if (args.length >= 2) {
            appId = Integer.parseInt(args[1]);
        }
        if (args.length >= 3) {
            zoneId = Integer.parseInt(args[2]);
        }
        if (args.length >= 4) {
            tcaplusAddress = args[3].trim();
        }
        if (args.length >= 5) {
            password = args[4].trim();
        }
        LOGGER.error("limit:{}", limit);
        TcaplusPerformanceTest main = new TcaplusPerformanceTest();
        main.init();
        main.run();
        main.stop();
    }

    private void init() {
        client = ClientFactory.createClient(appId, zoneId, password, Lists.newArrayList(tcaplusAddress), 4, 5000);
        array = new String[runTimesInOneBatch];
        for (int i = 0; i < array.length; i++) {
            array[i] = RandomStringUtils.randomAlphanumeric(10);
        }
        LOGGER.error("array init post:{}", runTimesInOneBatch);
    }

    private void stop() {
        if (client != null) {
            ClientFactory.destroyClient(client);
        }
    }

    private void run() throws InterruptedException {
        int i = 0;
        while (i < totalCycleTimes) {
            handleInsertByPost();
            handleGetByPost();
            handleUpdateByPost();
            handleDeleteByPost();
            i++;
        }
    }

    private void handleInsertByPost() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(runTimesInOneBatch);
        AtomicLong total = new AtomicLong(0);
        LongAdder unfinishNum = new LongAdder();
        long postInsertBegin = System.currentTimeMillis();
        for (int i = 0; i < runTimesInOneBatch; i++) {
            // 生成发送请求
            Request request = client.acquireRequest();
            request.setCmd(TcaplusProtocolCsConstants.TCAPLUS_CMD_INSERT_REQ);
            request.setTableName(TABLE_NAME);
            Record record = request.addRecord();
            record.setKeyLong("ZoneId", 1L);
            record.setKeyString("Key", array[i]);
            int i1 = RandomUtils.nextInt(BYTE_NUM);
            record.setValueBlob("Value", BYTE_MAP.get(i1));
            final long startTime = System.currentTimeMillis();
            client.post(request, (response) -> {
                long interval = System.currentTimeMillis() - startTime;
                latch.countDown();
                total.addAndGet(interval);
                unfinishNum.decrement();
            });
            unfinishNum.increment();
            while (unfinishNum.intValue() > limit) {
                // 自旋
            }
        }
        latch.await();
        long phased = System.currentTimeMillis() - postInsertBegin;
        LOGGER.error("insert tps: {}", (long) (runTimesInOneBatch * 1000.0d / phased));
        LOGGER.error(" insert avg: {}", total.get() / runTimesInOneBatch);
    }

    private void handleGetByPost() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(runTimesInOneBatch);
        AtomicLong total = new AtomicLong(0);
        LongAdder unfinishNum = new LongAdder();
        long postInsertBegin = System.currentTimeMillis();
        for (int i = 0; i < runTimesInOneBatch; i++) {
            // 生成发送请求
            Request request = client.acquireRequest();
            request.setCmd(TcaplusProtocolCsConstants.TCAPLUS_CMD_GET_REQ);
            request.setTableName(TABLE_NAME);
            Record record = request.addRecord();
            record.setKeyLong("ZoneId", 1L);
            record.setKeyString("Key", array[i]);
            final long startTime = System.currentTimeMillis();
            client.post(request, (response) -> {
                long interval = System.currentTimeMillis() - startTime;
                latch.countDown();
                total.addAndGet(interval);
                unfinishNum.decrement();
            });
            unfinishNum.increment();
            while (unfinishNum.intValue() > limit) {
                // 自旋
            }
        }
        latch.await();
        long phased = System.currentTimeMillis() - postInsertBegin;
        LOGGER.error("get tps: {}", (long) (runTimesInOneBatch * 1000.0d / phased));
        LOGGER.error(" get avg: {}", total.get() / runTimesInOneBatch);
    }

    private void handleUpdateByPost() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(runTimesInOneBatch);
        AtomicLong total = new AtomicLong(0);
        LongAdder unfinishNum = new LongAdder();
        long postInsertBegin = System.currentTimeMillis();
        for (int i = 0; i < runTimesInOneBatch; i++) {
            // 生成发送请求
            Request request = client.acquireRequest();
            request.setCmd(TcaplusProtocolCsConstants.TCAPLUS_CMD_UPDATE_REQ);
            request.setTableName(TABLE_NAME);
            Record record = request.addRecord();
            record.setKeyLong("ZoneId", 1L);
            record.setKeyString("Key", array[i]);
            int i1 = RandomUtils.nextInt(BYTE_NUM);
            record.setValueBlob("Value", BYTE_MAP.get(i1));
            final long startTime = System.currentTimeMillis();
            client.post(request, (response) -> {
                long interval = System.currentTimeMillis() - startTime;
                latch.countDown();
                total.addAndGet(interval);
                unfinishNum.decrement();
            });
            unfinishNum.increment();
            while (unfinishNum.intValue() > limit) {
                // 自旋
            }
        }
        latch.await();
        long phased = System.currentTimeMillis() - postInsertBegin;
        LOGGER.error("update tps: {}", (long) (runTimesInOneBatch * 1000.0d / phased));
        LOGGER.error(" update avg: {}", total.get() / runTimesInOneBatch);
    }

    private void handleDeleteByPost() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(runTimesInOneBatch);
        AtomicLong total = new AtomicLong(0);
        LongAdder finishNum = new LongAdder();
        LongAdder sendNum = new LongAdder();
        long postInsertBegin = System.currentTimeMillis();
        for (int i = 0; i < runTimesInOneBatch; i++) {
            Request request = client.acquireRequest();
            request.setCmd(TcaplusProtocolCsConstants.TCAPLUS_CMD_DELETE_REQ);
            request.setTableName(TABLE_NAME);
            Record record = request.addRecord();
            record.setKeyLong("WorldZoneKey", 12345678L);
            record.setKeyString("Key", array[i]);
            final long startTime = System.currentTimeMillis();
            client.post(request, (response) -> {
                long endTime = System.currentTimeMillis();
                long interval = endTime - startTime;
                latch.countDown();
                total.addAndGet(interval);
                finishNum.add(1);
            });
            sendNum.add(1);
            while (sendNum.intValue() - finishNum.intValue() > limit) {
                // 自旋
            }
        }
        latch.await();
        long phased = System.currentTimeMillis() - postInsertBegin;
        LOGGER.error("delete tps: {}", (long) (runTimesInOneBatch * 1000.0d / phased));
        LOGGER.error(" delete avg: {}", total.get() / runTimesInOneBatch);
    }
}

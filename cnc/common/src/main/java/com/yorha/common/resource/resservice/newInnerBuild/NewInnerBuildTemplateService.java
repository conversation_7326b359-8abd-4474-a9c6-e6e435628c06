package com.yorha.common.resource.resservice.newInnerBuild;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.ShapeUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import org.jetbrains.annotations.NotNull;
import res.template.*;

import java.util.*;

/**
 * 内城RH
 *
 * <AUTHOR>
 */

public class NewInnerBuildTemplateService extends AbstractResService {

    public NewInnerBuildTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    private final Map<Integer, Integer> configIdGetBuildId = new HashMap<>();

    //建筑id+等级->对应的建筑等级配置
    private final Map<IntPairType, Integer> buildUpgradeTemplate = new HashMap<>();
    //建筑id+等级->解锁的建筑类型+数量
    private final Map<IntPairType, List<IntPairType>> buildLvForBuildUnlock = new HashMap<>();
    //任务id->解锁的建筑类型+数量
    private final Map<Integer, List<IntPairType>> taskForBuildUnlock = new HashMap<>();
    //pve关卡id->解锁的建筑类型+数量
    private final Map<Integer, List<IntPairType>> missionForBuildUnlock = new HashMap<>();
    //主堡等级->事件id
    private final Map<Integer, List<Integer>> buildLvForDefendCamaign = new HashMap<>();
    //建筑类型+数量->对应的不同数量的解锁条件
    private final Map<IntPairType, List<IntPairType>> typeNumForUnlockCondition = new HashMap<>();
    //建筑等级id->对应的升级条件<数量 条件列表>
    private final Map<IntPairType, List<IntPairType>> upgradeIdForUpgradeCondition = new HashMap<>();
    //默认解锁区域id
    private int defaultAreaId = 0;


    @Override
    public void load() throws ResourceException {
        getNewInnerBuildTemplateMap().putAll(getResHolder().getMap(InnerBuildRhTemplate.class));
        for (InnerBuildRhTemplate buildTemplate : getResHolder().getListFromMap(InnerBuildRhTemplate.class)) {
            configIdGetBuildId.put(buildTemplate.getUnitsConfigId(), buildTemplate.getId());
        }

        //查找默认解锁区域id
        for (BaseExpandRhTemplate template : getResHolder().getListFromMap(BaseExpandRhTemplate.class)) {
            if (template.getUnlockMission() == 0) {
                defaultAreaId = template.getId();
                break;
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        validateInnerBuildTemplates();
        processBuildUpgradeTemplates();
        processCampaignTemplates();
        validateBaseExpandTemplates();
    }

    public int getDefaultAreaId() {
        return defaultAreaId;
    }

    /**
     * 验证内城区域范围不重叠，每个区域都是一个矩形，BaseExpandRhTemplate.getZoneCoordinatePair()定义了左下角坐标，BaseExpandRhTemplate.getZoneSizePair()定义了宽高
     */
    private void validateBaseExpandTemplates() throws ResourceException {
        for (BaseExpandRhTemplate template : getResHolder().getListFromMap(BaseExpandRhTemplate.class)) {
            var point1 = template.getZoneCoordinatePair();
            var size1 = template.getZoneSizePair();
            for (BaseExpandRhTemplate otherTemplate : getResHolder().getListFromMap(BaseExpandRhTemplate.class)) {
                if (otherTemplate.getId() == template.getId()) {
                    continue;
                }
                IntPairType point2 = otherTemplate.getZoneCoordinatePair();
                IntPairType size2 = otherTemplate.getZoneSizePair();
                if (ShapeUtils.isRectanglesIntersect(point1, size1, point2, size2)) {
                    throw new ResourceException(StringUtils.format("区域{}与区域{}范围重叠", template.getId(), otherTemplate.getId()));
                }
            }
        }
    }

    /**
     * 验证内城建筑模板的新手解锁数量不超过上限
     */
    private void validateInnerBuildTemplates() throws ResourceException {
        for (Map.Entry<Integer, InnerBuildRhTemplate> entrySet : getNewInnerBuildTemplateMap().entrySet()) {
            Integer key = entrySet.getKey();
            InnerBuildRhTemplate value = entrySet.getValue();
            if (value.getNewbieUnlockNum() > value.getNumMax()) {
                throw new ResourceException(StringUtils.format("城建表:InnerBuildRhTemplate:id {} 新手解锁数量：{} 超出上限：{}",
                        key, value.getNewbieUnlockNum(), value.getNumMax()));
            }
        }
    }

    /**
     * 处理建筑升级模板，包括解锁条件和升级条件
     */
    private void processBuildUpgradeTemplates() throws ResourceException {
        Map<Integer, BuildUpgradeRhTemplate> mapBuildUpgrade = getResHolder().getMap(BuildUpgradeRhTemplate.class);
        for (BuildUpgradeRhTemplate upgradeTemplate : getResHolder().getListFromMap(BuildUpgradeRhTemplate.class)) {
            IntPairType idAndLv = IntPairType.makePair(upgradeTemplate.getBuildId(), upgradeTemplate.getBuildLevel());
            buildUpgradeTemplate.put(idAndLv, upgradeTemplate.getId());

            validateBuildTemplateExists(upgradeTemplate);
            processUnlockConditions(upgradeTemplate, mapBuildUpgrade);
        }
    }

    /**
     * 验证建筑模板是否存在
     */
    private void validateBuildTemplateExists(BuildUpgradeRhTemplate upgradeTemplate) throws ResourceException {
        var buildTemplate = getResHolder().getValueFromMap(InnerBuildRhTemplate.class, upgradeTemplate.getBuildId());
        if (buildTemplate == null) {
            throw new ResourceException(StringUtils.format("updateTemplate({}) 配置的建筑({})不存在",
                    upgradeTemplate.getId(), upgradeTemplate.getBuildId()));
        }
    }

    /**
     * 处理解锁条件
     */
    private void processUnlockConditions(BuildUpgradeRhTemplate upgradeTemplate,
                                         Map<Integer, BuildUpgradeRhTemplate> mapBuildUpgrade) {
        if (upgradeTemplate.getBuildLevel() == 0) {
            processInitialUnlockConditions(upgradeTemplate, mapBuildUpgrade);
        } else {
            processUpgradeConditions(upgradeTemplate);
        }
    }

    /**
     * 处理初始解锁条件（建筑等级为0时）
     */
    private void processInitialUnlockConditions(BuildUpgradeRhTemplate upgradeTemplate,
                                                Map<Integer, BuildUpgradeRhTemplate> mapBuildUpgrade) {
        if (upgradeTemplate.getUnlockCondition() != null) {
            String[] condStrs = upgradeTemplate.getUnlockCondition().split("\\|");
            int num = 1;
            for (String strs : condStrs) {
                List<IntPairType> unlockConds = parseUnlockConditions(strs);
                IntPairType typeNum = IntPairType.makePair(upgradeTemplate.getBuildId(), num);

                for (IntPairType pair : unlockConds) {
                    addUnlockConditionMapping(pair, typeNum, mapBuildUpgrade);
                }

                typeNumForUnlockCondition.put(typeNum, unlockConds);
                num++;
            }
        } else {
            // 没有条件，就创造一个空条件，使建筑能够被建造出来
            typeNumForUnlockCondition.put(IntPairType.makePair(upgradeTemplate.getBuildId(), 1), Collections.emptyList());
        }
    }

    /**
     * 解析解锁条件字符串
     */
    private List<IntPairType> parseUnlockConditions(String conditionStr) {
        List<IntPairType> unlockConds = new ArrayList<>();
        String[] ss = conditionStr.split(",");
        for (String str : ss) {
            if (str.contains("_")) {
                unlockConds.add(IntPairType.parseFromString(str));
            }
        }
        return unlockConds;
    }

    /**
     * 添加解锁条件映射
     */
    private void addUnlockConditionMapping(IntPairType pair, IntPairType typeNum,
                                           Map<Integer, BuildUpgradeRhTemplate> mapBuildUpgrade) {
        if (pair.getKey() == CommonEnum.BuildConditionType.PRE_BUILD_VALUE) {
            BuildUpgradeRhTemplate pre = mapBuildUpgrade.get(pair.getValue());
            if (pre != null) {
                IntPairType preBuildKey = IntPairType.makePair(pre.getBuildId(), pre.getBuildLevel());
                buildLvForBuildUnlock.computeIfAbsent(preBuildKey, k -> new ArrayList<>()).add(typeNum);
            }
        } else if (pair.getKey() == CommonEnum.BuildConditionType.PRE_TASK_VALUE) {
            taskForBuildUnlock.computeIfAbsent(pair.getValue(), k -> new ArrayList<>()).add(typeNum);
        } else if (pair.getKey() == CommonEnum.BuildConditionType.PRE_MISSION_VALUE) {
            missionForBuildUnlock.computeIfAbsent(pair.getValue(), k -> new ArrayList<>()).add(typeNum);
        }
    }

    /**
     * 处理升级条件（建筑等级大于0时）
     */
    private void processUpgradeConditions(BuildUpgradeRhTemplate upgradeTemplate) {
        if (upgradeTemplate.getUnlockCondition() != null) {
            IntPairType idAndLv = IntPairType.makePair(upgradeTemplate.getBuildId(), upgradeTemplate.getBuildLevel());
            String[] condStrs = upgradeTemplate.getUnlockCondition().split(",");
            for (String str : condStrs) {
                upgradeIdForUpgradeCondition.computeIfAbsent(idAndLv, k -> new ArrayList<>())
                        .add(IntPairType.parseFromString(str));
            }
        }
    }

    /**
     * 更新目标建筑的解锁条件
     */
    private void updateTargetBuildUnlockConditions(IntPairType tarBuild, IntPairType unlockCond, IntPairType idAndLv,
                                                   Map<Integer, BuildUpgradeRhTemplate> mapBuildUpgrade) {
        List<IntPairType> conds = typeNumForUnlockCondition.get(tarBuild);
        if (conds == null) {
            typeNumForUnlockCondition.computeIfAbsent(tarBuild, t -> new ArrayList<>()).add(unlockCond);
        } else {
            boolean found = false;
            for (IntPairType cond : conds) {
                if (cond.getKey() == CommonEnum.BuildConditionType.PRE_BUILD_VALUE) {
                    BuildUpgradeRhTemplate otherTemplate = mapBuildUpgrade.get(cond.getValue());
                    if (otherTemplate.getBuildId() == idAndLv.getKey()) {
                        if (otherTemplate.getBuildLevel() > idAndLv.getValue()) {
                            conds.remove(cond);
                            conds.add(unlockCond);
                        }
                        found = true;
                        break;
                    }
                }
            }
            if (!found) {
                conds.add(unlockCond);
            }
        }
    }

    /**
     * 处理战役模板
     */
    private void processCampaignTemplates() {
        for (CampaignTdTemplate campTemplate : getResHolder().getListFromMap(CampaignTdTemplate.class)) {
            buildLvForDefendCamaign.computeIfAbsent(campTemplate.getBaseLevelId(), k -> new ArrayList<>())
                    .add(campTemplate.getId());
        }
    }

    public Collection<InnerBuildRhTemplate> getAllBuildInitTemplate() {
        return getBuildInitTemplateMap().values();
    }

    public Map<Integer, InnerBuildRhTemplate> getBuildInitTemplateMap() {
        return getResHolder().getMap(InnerBuildRhTemplate.class);
    }

    public InnerBuildRhTemplate getBuildTemplate(int innerBuildRhType) {
        return getResHolder().getTemplate(InnerBuildRhTemplate.class, innerBuildRhType);
    }

    public Map<Integer, InnerBuildRhTemplate> getNewInnerBuildTemplateMap() {
        return getResHolder().getMap(InnerBuildRhTemplate.class);
    }

    public ConstBuildRhTemplate getTemplate() {
        return getResHolder().getConstTemplate(ConstBuildRhTemplate.class);
    }

    public int getBuildingId(int unitConfigId) {
        return configIdGetBuildId.get(unitConfigId);
    }

    public BuildUpgradeRhTemplate getBuildUpgradeTempate(int innerBuildRhType, int level) {
        IntPairType idAndLv = IntPairType.makePair(innerBuildRhType, level);
        Integer id = buildUpgradeTemplate.getOrDefault(idAndLv, 0);
        if (id == 0) {
            return null;
        }
        return getResHolder().getTemplate(BuildUpgradeRhTemplate.class, id);
    }

    public List<IntPairType> getBuildUnlockBuildings(int innerBuildRhType, int level) {
        IntPairType idAndLv = IntPairType.makePair(innerBuildRhType, level);
        return buildLvForBuildUnlock.getOrDefault(idAndLv, Collections.emptyList());
    }

    public List<IntPairType> getTaskUnlockBuildings(int taskId) {
        return taskForBuildUnlock.getOrDefault(taskId, Collections.emptyList());
    }

    public List<IntPairType> getMissionUnlockBuildings(int missionId) {
        return missionForBuildUnlock.getOrDefault(missionId, Collections.emptyList());
    }

    @NotNull
    public List<IntPairType> getBuildUpgradeCondition(int innerBuildRhType, int level) {
        IntPairType idAndLv = IntPairType.makePair(innerBuildRhType, level);
        return upgradeIdForUpgradeCondition.getOrDefault(idAndLv, Collections.emptyList());
    }

    public List<IntPairType> getBuildNumUnlockCondition(int innerBuildRhType, int num) {
        IntPairType typeAndNum = IntPairType.makePair(innerBuildRhType, num);
        return typeNumForUnlockCondition.get(typeAndNum);
    }

    public int randomDefendCampaign(int baseLevel) {
        List<Integer> list = buildLvForDefendCamaign.get(baseLevel);
        if (list == null) {
            return 0;
        }
        return RandomUtils.randomList(list);
    }

    /**
     * 根据建设等级计算下一波的时间（以分钟为单位）
     * 此方法通过查询预设的在线时间列表来确定下一波的时间
     * 它根据建设等级找到合适的时间配置，如果找到则返回该时间，
     * 如果没有找到，则返回最后一个匹配的配置时间
     *
     * @param buildLevel 建设等级，用于确定下一波时间的依据
     * @return 下一波的时间（分钟），根据建设等级和配置确定
     */
    public int getNextWaveTimeMinute(int buildLevel) {
        int ret = 0;
        ConstCampaignTemplate constTemplate = ResHolder.getInstance().getConstTemplate(ConstCampaignTemplate.class);
        List<IntPairType> list = constTemplate.getOnlineTime();
        for (IntPairType e : list) {
            if (e.getKey() <= buildLevel) {
                ret = e.getValue();
            } else {
                break;
            }
        }
        return ret;
    }
}

package com.yorha.common.resource.resservice.player;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.RandomUtils;
import com.yorha.proto.CommonEnum;
import res.template.PrivateFlagTemplate;
import res.template.SpasswordCheckTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置
 *
 * <AUTHOR>
 */
public class SettingService extends AbstractResService {
    private Map<CommonEnum.SPassWordCheckType, Boolean> sPassWordCheckMap = new HashMap<>();
    private Map<CommonEnum.SPassWordCheckType, List<Integer>> sPassWordParamMap = new HashMap<>();
    private List<Integer> defaultPlagList = new ArrayList<>();

    public SettingService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (SpasswordCheckTemplate template : getResHolder().getListFromMap(SpasswordCheckTemplate.class)) {
            sPassWordCheckMap.put(template.getType(), template.getNeedCheck());
            sPassWordParamMap.put(template.getType(), template.getParamList());
        }

        for (PrivateFlagTemplate template : getResHolder().getListFromMap(PrivateFlagTemplate.class)) {
            if (template.getType() != CommonEnum.FlagType.FT_DEFAULT) {
                continue;
            }
            defaultPlagList.add(template.getId());
        }
    }

    /**
     * 抽取任意默认个人旗帜
     *
     * @return
     */
    public int getRandomPflag() {
        if (defaultPlagList.size() <= 0) {
            return 0;
        }
        return defaultPlagList.get(RandomUtils.randomBetween(0, defaultPlagList.size() - 1));
    }

    public boolean needCheck(CommonEnum.SPassWordCheckType type) {
        return sPassWordCheckMap.get(type);
    }

    public List<Integer> getParam(CommonEnum.SPassWordCheckType type) {
        return sPassWordParamMap.get(type);
    }

    @Override
    public void checkValid() throws ResourceException {

    }
}

package com.yorha.common.resource.resservice.points;

import org.jetbrains.annotations.NotNull;
import res.template.ActivityPointsTemplate;

public class PointsByUseSpeedUpConf extends PointsConf {
    public PointsByUseSpeedUpConf(@NotNull ActivityPointsTemplate template, int queueTaskType) {
        super(template);
        this.queueTaskType = queueTaskType;
        this.minutesToPoint = Integer.parseInt(template.getParam2());
    }

    private final int queueTaskType;

    private final int minutesToPoint;

    public int getQueueTaskType() {
        return queueTaskType;
    }

    public int getMinutesToPoint() {
        return minutesToPoint;
    }
}

package com.yorha.common.chat;

import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonMsg;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.Set;
import java.util.TreeMap;

/**
 * 聊天常驻缓存
 * 固定容量，类似先进先出，每次淘汰msgId最小的消息，插入最新的消息
 *
 * <AUTHOR>
 */
public class ChatCache {
    private final int max;
    private final TreeMap<Long, CommonMsg.ChatMessage> cache;
    private long lastMsgId;

    public ChatCache(final int maxSize, final long lastMsgId) {
        this.max = maxSize;
        this.cache = new TreeMap<>();
        this.lastMsgId = lastMsgId;
    }

    /**
     * @param chatMessage 聊天消息。
     * @return 聊天消息是否可以被缓存。
     */
    public boolean canBeCached(final CommonMsg.ChatMessage chatMessage) {
        if (!this.isFull()) {
            return true;
        }
        final long lastCanCachedMsgId = this.lastMsgId - this.cacheCapacity() + 1;
        return chatMessage.getMessageId() >= lastCanCachedMsgId;
    }

    /**
     * 向缓存中添加数据。
     *
     * @param key   键
     * @param value 值
     */
    public void putData(@NotNull Long key, @NotNull CommonMsg.ChatMessage value) {
        if (!this.canBeCached(value)) {
            throw new GeminiException("key={}, value={} can't be cached", key, value);
        }
        this.cache.put(key, value);
        if (this.cache.size() > this.max) {
            this.cache.remove(this.cache.firstKey());
        }
        this.lastMsgId = Math.max(key, this.lastMsgId);
    }

    /**
     * 从缓存中读取数据。
     *
     * @param key 键。
     * @return 值，可能是null。
     */
    @Nullable
    public CommonMsg.ChatMessage getDataOrNull(@NotNull Long key) {
        return this.cache.get(key);
    }

    /**
     * @return 缓存中最小的MessageId
     */
    public long getMinChatMessageId() {
        return this.cache.firstKey();
    }

    /**
     * @return 当前的聊天msgId集合。
     */
    public Set<Long> keySet() {
        return Collections.unmodifiableSet(this.cache.keySet());
    }

    /**
     * @return cache是否填满。
     */
    public boolean isFull() {
        return this.cache.size() >= this.max;
    }

    /**
     * @return cache是否为空。
     */
    public boolean isEmpty() {
        return this.cache.size() == 0;
    }

    /**
     * @return 聊天缓存的最大数量。
     */
    public final int cacheCapacity() {
        return this.max;
    }
}

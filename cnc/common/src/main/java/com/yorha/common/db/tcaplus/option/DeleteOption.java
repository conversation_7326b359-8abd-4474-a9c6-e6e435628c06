package com.yorha.common.db.tcaplus.option;

/**
 * <AUTHOR>
 */
public class DeleteOption {
    /**
     * 如果设置为负数，表示当前数据不启动版本控
     * 制
     */
    private int version = -1;
    /**
     * 是否需要重试。如果重试，DBActor会不停得重试直到超时。
     */
    private boolean isRetry = false;

    private DeleteOption() {
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public int getVersion() {
        return version;
    }

    public boolean isRetry() {
        return isRetry;
    }

    public static final class Builder {
        private int version = -1;
        private boolean isRetry = false;

        private Builder() {
        }

        public Builder withVersion(int version) {
            this.version = version;
            return this;
        }

        public Builder withRetry() {
            this.isRetry = true;
            return this;
        }

        public DeleteOption build() {
            final DeleteOption deleteOption = new DeleteOption();
            deleteOption.version = this.version;
            deleteOption.isRetry = this.isRetry;
            return deleteOption;
        }
    }
}

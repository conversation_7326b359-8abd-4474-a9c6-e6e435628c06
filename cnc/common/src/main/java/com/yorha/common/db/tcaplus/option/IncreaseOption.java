package com.yorha.common.db.tcaplus.option;

import com.yorha.proto.CommonEnum;

public class IncreaseOption {

    public static final IncreaseOption DEFAULT_VALUE = IncreaseOption.newBuilder().build();

    private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
    /**
     * 是否需要重试。如果重试，DBActor会不停得重试直到超时。
     */
    private boolean isRetry = false;
    /**
     * 如果设置为负数，表示当前数据不启动版本控
     * 制
     */
    private int version = -1;

    private IncreaseOption() {

    }

    public static IncreaseOption.Builder newBuilder() {
        return new IncreaseOption.Builder();
    }

    public int getResultFlag() {
        return resultFlag.getNumber();
    }

    public int getVersion() {
        return version;
    }

    public boolean isRetry() {
        return isRetry;
    }

    public static final class Builder {
        private CommonEnum.TcaplusResultFlag resultFlag = CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY;
        private boolean isRetry = false;
        private int version = 0;

        private Builder() {
        }

        public IncreaseOption.Builder setVersion(final int version) {
            this.version = version;
            return this;
        }

        public IncreaseOption.Builder setResultFlag(CommonEnum.TcaplusResultFlag resultFlag) {
            this.resultFlag = resultFlag;
            return this;
        }

        public IncreaseOption.Builder withRetry() {
            this.isRetry = true;
            return this;
        }

        public IncreaseOption build() {
            final IncreaseOption increaseOption = new IncreaseOption();
            increaseOption.resultFlag = this.resultFlag;
            increaseOption.isRetry = this.isRetry;
            increaseOption.version = this.version;
            return increaseOption;
        }
    }

}

package com.yorha.common.actor.mailbox.middleware;

import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actor.mailbox.MsgContext;
import com.yorha.common.actor.mailbox.msg.ActorMailboxMsg;
import com.yorha.common.actorservice.ActorMetaData;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.concurrent.TimeUnit;

/**
 * 默认的监控中间件。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class DefaultMailboxMonitorMiddlewareImpl implements IMailboxMiddleware {
    private static final Logger LOGGER = LogManager.getLogger(DefaultMailboxMonitorMiddlewareImpl.class);

    /**
     * 角色标识。
     */
    private final ActorMetaData metaData;

    public DefaultMailboxMonitorMiddlewareImpl(final ActorMetaData metaData) {
        this.metaData = metaData;
    }

    @Override
    public void messageOffer(IActorMailbox mb, ActorMailboxMsg msg) {
        ServerContext.getActorPerfLogger().logOfferActorMsg(metaData.getActorRole(), msg.getName());
    }

    @Override
    public void messageRefused(final IActorMailbox mb, final ActorMailboxMsg msg, final String reason) {
        WechatLog.error("actor mailbox refuse msg! mailbox={} msg={}! queue is {}!", mb, msg, reason);
    }

    @Override
    public void messageStart(final MsgContext context, long startTsNs) {
        final IActorMailbox mb = context.getMailbox();
        final ActorMailboxMsg msg = context.getMsg();
        final long scheduleTimeNs = startTsNs - msg.getOfferTsNs();
        if (scheduleTimeNs >= MonitorConstant.ACTOR_HANDLE_MAILBOX_SCHEDULING_COST_NS) {
            LOGGER.warn("actor schedule msg overtime! Mailbox {} at Thread {} schedule {} delay {}ms",
                    mb,  Thread.currentThread(),  msg,
                    TimeUnit.NANOSECONDS.toMillis(scheduleTimeNs)
            );
        }
        ServerContext.getActorPerfLogger().logStartMsg(metaData.getActorRole(), msg.getName(), scheduleTimeNs);
    }

    @Override
    public void messageDone(final MsgContext context, final long startTsNs) {
        final ActorMailboxMsg msg = context.getMsg();
        final long costNs = SystemClock.nanoTimeNative() - startTsNs;
        ServerContext.getActorPerfLogger().logMsgDone(metaData.getActorRole(), msg.getName(), costNs);
        this.warnRealCostOverTimeMessage(context, startTsNs);
    }

    protected void warnRealCostOverTimeMessage(final MsgContext context, final long startTsNs) {
        MsgContext.SubMsg cur = context.getSubEnvelopHead();
        long totalCallCostNs = 0; //所有 call 子请求耗时之和（纳秒）
        if (cur != null) {
            // 循环遍历子envelop
            while (cur != null) {
                final ActorMsgEnvelope envelope = cur.envelope;
                if (envelope == null) {
                    WechatLog.error("DefaultMailboxMonitorMiddlewareImpl loop subMsg null! context={}!", context.getEnvelope());
                } else if (envelope.isByCall()) {
                    // call 的错误统计
                    final long costTimeNs = cur.endTsNs - cur.startTsNs;
                    final long costTimeMs = TimeUnit.NANOSECONDS.toMillis(costTimeNs);
                    if (costTimeMs > MonitorConstant.ACTOR_CALL_COST_OVER_TIME_MS) {
                        // 大于50ms需要打印出来
                        LOGGER.info("call cost over 50, cost={}ms call={} context={}", costTimeMs, context.getEnvelope(), envelope);
                    } else {
                        LOGGER.debug("finish call, msgSeqId={} interval={}ms", envelope.getMsgSeqId(), costTimeMs);
                    }
                    totalCallCostNs += costTimeNs;
                }
                cur = cur.getNext();
            }
        }
        //Actor 实际执行业务逻辑的时间（毫秒）
        final long costMs = TimeUnit.NANOSECONDS.toMillis(SystemClock.nanoTimeNative() - startTsNs - totalCallCostNs);
        if (costMs > MonitorConstant.ACTOR_HANDLE_MSG_REAL_COST_OVER_TIME_MS) {
            LOGGER.warn("actor real cost overtime, cost={}ms subCallCost={} ms,msg={} ",
                    costMs, TimeUnit.NANOSECONDS.toMillis(totalCallCostNs), context.getEnvelope());
        }
    }

    @Override
    public void messageError(MsgContext context, long startTsNs) {
        this.warnRealCostOverTimeMessage(context, startTsNs);
    }

    @Override
    public void perfReport() {
        // 打印线程/协程日志
        final int threadNum = this.metaData.getDispatcher().getParallelism();
        final int fiberNum = this.metaData.getDispatcher().getFiberCnt(this.metaData.getActorRole());
        final int actorNum = this.metaData.getActorSystem().getRegistryValue().getMailboxSizeByActorRole(this.metaData.getActorRole());
        LOGGER.info("thread_fiber_num_perf {} thread:{} fiber:{} num:{}", metaData.getActorRole(), threadNum, fiberNum, actorNum);
        // grafana上报
        MonitorUnit.FIBER_NUM_GAUGE.labels(ServerContext.getBusId(), this.metaData.getActorRole()).set(fiberNum);
        MonitorUnit.ACTOR_NUM_GAUGE.labels(ServerContext.getBusId(), this.metaData.getActorRole()).set(actorNum);
    }
}

package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.Projections;
import com.mongodb.reactivestreams.client.FindPublisher;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetResult;
import org.bson.Document;
import org.reactivestreams.Publisher;


/**
 * 用于根据完整主键（key）获取单条记录
 * <AUTHOR>
 */
public class MongoGet<T extends Message.Builder> extends MongoOperation<T, GetOption, Document, Document, GetResult<T>> {
    public MongoGet(MongoDatabase database, T t, GetOption getOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, getOption);
    }

    @Override
    protected NormalSubscriber<Document> getSubscriber() {
        return new NormalSubscriber<>();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        //MongoDB 在底层实现上会优化此类查询行为，找到第一个匹配项后就停止扫描，即使没有显式调用
        FindPublisher<Document> result = this.database.getCollection(this.getTableName()).find(DocumentHelper.formKey(this.getReq())).limit(1);
        if (this.getOption().isGetAllFields()) {
            return result.projection(Projections.excludeId());
        }
        this.checkFieldNames(this.getOption().getFieldNames(), this.getReq().getDescriptorForType());
        return result.projection(this.buildProjection(this.getOption().getFieldNames()));
    }

    @Override
    protected GetResult<T> buildResult(Document document) {
        T proto = MongoOperation.buildDefaultValue(this.getReq());
        final GetResult<T> result = new GetResult<T>();
        result.code = TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
        if (document != null) {
            this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
            result.code = TcaplusErrorCode.GEN_ERR_SUC;
        }

        result.value = proto;
        return result;
    }

    @Override
    protected GetResult<T> onMongoError() {
        final GetResult<T> result = new GetResult<T>();
        result.code = DEFAULT_ERROR_CODE;
        result.value = MongoOperation.buildDefaultValue(this.getReq());
        return result;
    }
}

package com.yorha.common.utils.time.schedule;

import com.yorha.common.actor.node.ISceneActor;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.framework.AbstractEntity;
import com.yorha.common.helper.LogHelper;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ActorTimer控制器，封装添加删除actor timer的逻辑代码
 *
 * <AUTHOR>
 */
public class ActorTimerControllerForSceneActor {
    final AbstractEntity owner;
    final Map<TimerReasonType, Map<String, String>> timerMap;
    private final ISceneActor sceneActor;

    public ActorTimerControllerForSceneActor(AbstractEntity owner, ISceneActor sceneActor) {
        this.owner = owner;
        this.timerMap = new HashMap<>();
        this.sceneActor = sceneActor;
    }

    /**
     * 设置单次定时器  默认使用entityId作为前缀
     */
    public ActorTimer addTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        return addTimerWithPrefix(String.valueOf(owner.getEntityId()), timerReasonType, runnable, initialDelay, unit);
    }

    /**
     * 设置单次定时器  线上热更 timerReasonType用DANGEROUS_PATCH
     */
    public ActorTimer addTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        if (!owner.isInitOk()) {
            LogHelper.timerErrorLog("ActorTimerController addTimerWithPrefix not initOk {} reason={} prefix={}", owner, timerReasonType, prefix);
            return null;
        }
        final ActorTimer actorTimer = sceneActor.controllerAddTimer(prefix, timerReasonType, runnable, initialDelay, unit);
        if (actorTimer == null) {
            return null;
        }
        timerMap.computeIfAbsent(timerReasonType, key -> new HashMap<>()).put(prefix, actorTimer.getName());
        return actorTimer;
    }


    public ActorTimer addRepeatTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return addRepeatTimerWithPrefix(String.valueOf(prefix), timerReasonType, runnable, initialDelay, period, unit);
    }

    /**
     * 设置重复定时器（不补帧）  线上热更 timerReasonType用DANGEROUS_PATCH
     */
    public ActorTimer addRepeatTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        if (!owner.isInitOk()) {
            LogHelper.timerErrorLog("ActorTimerController addRepeatTimerWithPrefix not initOk {} reason={} prefix={}", owner, timerReasonType, prefix);
            return null;
        }
        if (containTimer(timerReasonType, prefix)) {
            LogHelper.timerErrorLog("ActorTimerController addRepeatTimerWithPrefix repeat add {} reason={} prefix={}", owner, timerReasonType, prefix);
            return null;
        }
        final ActorTimer actorTimer = sceneActor.controllerAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, false);
        if (actorTimer == null) {
            return null;
        }
        timerMap.computeIfAbsent(timerReasonType, key -> new HashMap<>()).put(prefix, actorTimer.getName());
        return actorTimer;
    }

    public ActorTimer addFixRepeatTimer(long prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return addFixRepeatTimerWithPrefix(String.valueOf(prefix), timerReasonType, runnable, initialDelay, period, unit);
    }

    /**
     * 设置重复定时器（补帧）  线上热更 timerReasonType用DANGEROURS_PATCH
     */
    public ActorTimer addFixRepeatTimerWithPrefix(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        if (!owner.isInitOk()) {
            LogHelper.timerErrorLog("ActorTimerController addFixRepeatTimerWithPrefix not initOk {} reason={} prefix={}", owner, timerReasonType, prefix);
            return null;
        }
        if (containTimer(timerReasonType, prefix)) {
            LogHelper.timerErrorLog("ActorTimerController addFixRepeatTimerWithPrefix repeat add {} reason={} prefix={}", owner, timerReasonType, prefix);
            return null;
        }
        final ActorTimer actorTimer = sceneActor.controllerAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, true);
        if (actorTimer == null) {
            return null;
        }
        timerMap.computeIfAbsent(timerReasonType, key -> new HashMap<>()).put(prefix, actorTimer.getName());
        return actorTimer;
    }

    public void cancelTimer(TimerReasonType timerReasonType) {
        cancelTimer(timerReasonType, String.valueOf(owner.getEntityId()));
    }

    public void cancelTimer(TimerReasonType timerReasonType, String prefix) {
        if (!containTimer(timerReasonType, prefix)) {
            return;
        }
        final Map<String, String> prefix2Timer = timerMap.get(timerReasonType);
        final String name = prefix2Timer.get(prefix);
        final ActorTimer actorTimer = owner.ownerActor().getTimerByName(name);
        if (actorTimer != null) {
            actorTimer.cancel();
        }
        prefix2Timer.remove(prefix);
    }

    public boolean containTimer(TimerReasonType timerReasonType, String prefix) {
        if (!timerMap.containsKey(timerReasonType)) {
            return false;
        }

        if (!timerMap.get(timerReasonType).containsKey(prefix)) {
            return false;
        }
        return true;
    }

    /**
     * 取消自己持有的所有定时器的引用，并清空map
     */
    public void onDestroy() {
        for (Map<String, String> timers : timerMap.values()) {
            timers.values().forEach(timerName -> {
                final ActorTimer timer = owner.ownerActor().getTimerByName(timerName);
                if (timer == null) {
                    return;
                }
                if (timer.isCanceled()) {
                    return;
                }
                timer.cancel();
            });
        }
        timerMap.clear();
    }

    /**
     * 只清空自己保存的actor timer的引用，不会取消定时器
     */
    public void clearTimerMap() {
        timerMap.clear();
    }
}

package com.yorha.common.helper;

import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.Pair;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.TcaplusDb;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SceneDbHelper {
    private static final Logger LOGGER = LogManager.getLogger(SceneDbHelper.class);

    public static Pair<TcaplusDb.ZoneTable.Builder, Boolean> loadAndCreateZoneFromDb(GameActorWithCall actor, int zoneId, long openZoneTime) {
        TcaplusDb.ZoneTable.Builder req = TcaplusDb.ZoneTable.newBuilder().setZoneId(zoneId);
        GetOption build = GetOption.newBuilder().withGetAllFields(true).build();
        GetResult<TcaplusDb.ZoneTable.Builder> ans = actor.callGameDb(new SelectUniqueAsk<>(req, build), 10_000);
        if (DbUtil.isRecordNotExist(ans.getCode())) {
            LOGGER.info("loadAndCreateZoneFromDb set openZoneTime={}", openZoneTime);
            TcaplusDb.ZoneTable.Builder insert = TcaplusDb.ZoneTable.newBuilder().setZoneId(zoneId);
            insert.getFullAttrBuilder().setIsOpened(false).setCurMonsterMilestone(1).setNextMilestoneStamp(0)
                    .setServerOpenTsMs(openZoneTime);
            InsertResult<TcaplusDb.ZoneTable.Builder> insertAns = actor.callGameDb(new InsertAsk<>(insert), 10_000);
            if (!DbUtil.isOk(insertAns.getCode())) {
                throw new GeminiException("loadAndCreateZoneFromDb insert zoneTable fail code={}", zoneId, insertAns.getCode());
            }
            return Pair.of(insert, true);
        }
        if (DbUtil.isOk(ans.getCode())) {
            return Pair.of(ans.value, false);
        }
        throw new GeminiException("loadAndCreateZoneFromDb fail! code={}", ans.getCode());
    }

    /**
     * 从db恢复场景联盟
     */
    public static List<ValueWithVersion<TcaplusDb.ClanTable.Builder>> loadClanFromDb(GameActorWithCall actor, int zoneId) {
        TcaplusDb.ClanTable.Builder req = TcaplusDb.ClanTable.newBuilder();
        req.setZoneId(zoneId);
        GetByPartKeyOption.Builder builder = GetByPartKeyOption.newBuilder().withTimeout(BigSceneConstants.BIG_SCENE_LOAD_TIMEOUT);
        SelectAsk<TcaplusDb.ClanTable.Builder> ask = new SelectAsk<>(req, builder.build());
        GetByPartKeyResult<TcaplusDb.ClanTable.Builder> ans = actor.callGameDb(ask, BigSceneConstants.BIG_SCENE_LOAD_TIMEOUT);
        if (ans.isRecordNotExist()) {
            LOGGER.info("loadClanFromDb db, data empty,  zoneId={}, requestId={}", zoneId, ans.requestId);
            return Collections.emptyList();
        }
        if (!ans.isOk()) {
            WechatLog.error("loadClanFromDb db, zoneId={}, requestId={}, code={}", zoneId, ans.requestId, ans.getCode());
            throw new GeminiException("loadClanFromDb! code={}", ans.getCode());
        }
        LOGGER.info("loadClanFromDb db success, sceneId={}, zoneId={}, num:{} requestId={}", zoneId, zoneId, ans.getValues().size(), ans.requestId);
        return ans.getValues();
    }

    /**
     * 加载本区db中所有ScenePlayer数据
     */
    public static List<ValueWithVersion<TcaplusDb.ScenePlayerTable.Builder>> loadScenePlayerFromDb(GameActorWithCall actor, int zoneId) {
        TcaplusDb.ScenePlayerTable.Builder req = TcaplusDb.ScenePlayerTable.newBuilder();
        req.setZoneId(zoneId);
        GetByPartKeyOption.Builder builder = GetByPartKeyOption.newBuilder().withTimeout(BigSceneConstants.BIG_SCENE_LOAD_TIMEOUT);
        SelectAsk<TcaplusDb.ScenePlayerTable.Builder> ask = new SelectAsk<>(req, builder.build());
        GetByPartKeyResult<TcaplusDb.ScenePlayerTable.Builder> ans = actor.callGameDb(ask, BigSceneConstants.BIG_SCENE_LOAD_TIMEOUT);
        if (ans.isRecordNotExist()) {
            LOGGER.info("loadScenePlayerFromDb db empty zoneId={}, requestId={}", zoneId, ans.requestId);
            return Collections.emptyList();
        }
        if (!ans.isOk()) {
            WechatLog.error("loadScenePlayerFromDb db fail, oneId={}, requestId={}, code={}", zoneId, ans.requestId, ans.getCode());
            throw new GeminiException("loadAllScenePlayerFromDb! code={}", ans.getCode());
        }
        LOGGER.info("loadScenePlayerFromDb num={}, requestId={}", ans.getValues().size(), ans.requestId);
        return ans.getValues();
    }

    /**
     * 从db恢复场景物体
     */
    public static List<ValueWithVersion<TcaplusDb.SceneObjTable.Builder>> loadSceneObjFromDb(GameActorWithCall actor, int zoneId) {
        TcaplusDb.SceneObjTable.Builder req = TcaplusDb.SceneObjTable.newBuilder();
        req.setZoneId(zoneId).setSceneId(zoneId);
        GetByPartKeyOption.Builder builder = GetByPartKeyOption.newBuilder().withTimeout(BigSceneConstants.BIG_SCENE_LOAD_TIMEOUT);
        SelectAsk<TcaplusDb.SceneObjTable.Builder> ask = new SelectAsk<>(req, builder.build());

        GetByPartKeyResult<TcaplusDb.SceneObjTable.Builder> ans = actor.callGameDb(ask, BigSceneConstants.BIG_SCENE_LOAD_TIMEOUT);
        if (ans.isRecordNotExist()) {
            LOGGER.info("loadSceneObjFromDb data empty, zoneId={}, requestId={}", zoneId, ans.requestId);
            return Collections.emptyList();
        }
        if (!ans.isOk()) {
            WechatLog.error("loadSceneObjFromDb db fail, zoneId={}, requestId={}, code={}", zoneId, ans.requestId, ans.getCode());
            throw new GeminiException("loadSceneObjFromDb! code={}", ans.getCode());
        }
        LOGGER.info("loadSceneObjFromDb num={}, requestId={}", ans.getValues().size(), ans.requestId);
        return ans.getValues();
    }

    /**
     * 全服邮件
     */
    public static List<ValueWithVersion<TcaplusDb.MailStorageTable.Builder>> loadMailsFromDb(GameActorWithCall actor, int zoneId) {
        TcaplusDb.MailStorageTable.Builder req = TcaplusDb.MailStorageTable.newBuilder();
        req.setZoneId(zoneId);

        final GetByPartKeyResult<TcaplusDb.MailStorageTable.Builder> ans = actor.callGameDb(new SelectAsk<>(req), 10_000);
        if (ans.isRecordNotExist()) {
            LOGGER.info("loadMailsFromDb data empty, zoneId={}, requestId={}", zoneId, ans.requestId);
            return Collections.emptyList();
        }
        if (!ans.isOk()) {
            WechatLog.error("loadMailsFromDb fail, zoneId={}, requestId={}, code={}", zoneId, ans.requestId, ans.getCode());
            throw new GeminiException("ZoneMailMgr loadMails load from db fail, code={}", ans.getCode());
        }
        LOGGER.info("loadMailsFromDb success, num={} requestId={}", ans.getValues().size(), ans.requestId);
        if (CollectionUtils.isEmpty(ans.values)) {
            return Collections.emptyList();
        }
        return ans.getValues();
    }

    /**
     * 表格配置更改恢复
     */
    public static void deleteSceneObjFromDb(int zoneId, BaseGameActor actor, long entityId) {
        TcaplusDb.SceneObjTable.Builder req = TcaplusDb.SceneObjTable.newBuilder()
                .setZoneId(zoneId)
                .setEntityId(entityId)
                .setSceneId(zoneId);
        DeleteAsk<TcaplusDb.SceneObjTable.Builder> ask = new DeleteAsk<>(req);
        actor.tellGameDb(ask);
    }
}

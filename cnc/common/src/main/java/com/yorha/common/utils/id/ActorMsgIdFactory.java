package com.yorha.common.utils.id;

import com.yorha.common.server.NodeRole;
import com.yorha.common.server.config.ServerInfo;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 注: 任何需要持久化的id都不应使用
 * ActorMsgIdFactory, 高32位与busId相关, 低32位atomic自增
 *
 * <AUTHOR>
 */
public class ActorMsgIdFactory implements IIdFactory {
    private static final Logger LOGGER = LogManager.getLogger(ActorMsgIdFactory.class);

    private final AtomicInteger id;
    private final int first32;
    private final int originId;
    private long lastCycleTsMs;

    public ActorMsgIdFactory(final ServerInfo serverInfo, final int originId, final long startTsMs) {
        this.first32 = calFirst32(serverInfo);
        this.originId = originId;
        this.id = new AtomicInteger(originId);
        this.lastCycleTsMs = startTsMs;
        LOGGER.info("ActorMsgIdFactory busIdToInt={}, last32 begin={}", this.first32, originId);
    }

    @Override
    public long nextId(String reason) {
        final int last32 = id.incrementAndGet();
        if (last32 == originId) {
            final long nowTsMs = SystemClock.nowNative();
            LOGGER.info("ActorMsgIdFactory nextId recycle, lastCycleTsMs={}, curTsMs={}", lastCycleTsMs, nowTsMs);
            // 1min内用完,告警
            if (nowTsMs - lastCycleTsMs <= TimeUnit.MINUTES.toMillis(1)) {
                WechatLog.error("ActorMsgIdFactory nextId recycle too fast, lastCycleTsMs={}, curTsMs={}", lastCycleTsMs, nowTsMs);
            }
            lastCycleTsMs = nowTsMs;
        }
        // java会记录原来数据的符号位
        long ret = 0xffffffffL & last32;
        ret = ((long) first32 << 32) | ret;
        return ret;
    }

    int calFirst32(final ServerInfo serverInfo) {
        final boolean isZoneServer = isZoneServer(serverInfo);
        int ret = 0;
        final int zoneId = serverInfo.getZoneId();
        final int typeId = serverInfo.getServerTypeId();
        final int instanceId = serverInfo.getInstanceId();
        if (isZoneServer) {
            ret = (1 << 13) | (zoneId & 0x1FFF);
        } else {
            ret = ((typeId & 0x1F) << 8) | (instanceId & 0xFF);
        }
        final long now = SystemClock.nowNative();
        final int hour = TimeUtils.getHourOfDay(now);
        final int min = TimeUtils.getMinuteOfDay(now);
        final int sec = TimeUtils.getSecondOfDay(now);
        ret = ret | ((sec & 0x3F) << 14) | ((min & 0x3F) << 20) | ((hour & 0x3F) << 26);
        return ret;
    }

    boolean isZoneServer(final ServerInfo serverInfo) {
        return NodeRole.isSameType(NodeRole.Zone, serverInfo.getServerTypeId());
    }

}

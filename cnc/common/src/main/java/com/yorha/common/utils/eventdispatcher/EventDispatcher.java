package com.yorha.common.utils.eventdispatcher;

import com.yorha.common.wechatlog.WechatLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 事件分发器
 * 支持注册事件回调和触发
 *
 * <AUTHOR>
 */
public class EventDispatcher {

    private final Map<String, Map<Integer, EventListener>> event2eventHandlerMap = new HashMap<>();
    private int eventListenerUniqueId = 0;

    /**
     * 添加事件监听  单个 重复触发
     */
    public <E extends IEvent> EventListener addEventListenerRepeat(Consumer<? extends E> eventHandler, Class<E> clz) {
        return addEventListener(eventHandler, clz, true);
    }


    /**
     * 添加事件监听  单个 一次触发
     */
    public <E extends IEvent> EventListener addEventListener(Consumer<? extends E> eventHandler, Class<E> clz) {
        return addEventListener(eventHandler, clz, false);
    }

    private <E extends IEvent> EventListener addEventListener(Consumer<? extends E> eventHandler, Class<E> clz, boolean isRepeat) {
        String eventName = clz.getSimpleName();
        Map<Integer, EventListener> eventListeners = event2eventHandlerMap.computeIfAbsent(eventName, k -> new HashMap<>());
        eventListenerUniqueId++;
        EventListener eventListener = EventListener.valueOf(this, eventListenerUniqueId, eventName, eventHandler, isRepeat);
        eventListeners.put(eventListenerUniqueId, eventListener);
        return eventListener;
    }

    /**
     * 添加系列事件监听 重复触发
     */
    @SafeVarargs
    public final EventListener addMultiEventListenerRepeat(Consumer<IEvent> eventHandler, Class<? extends IEvent>... clzList) {
        return addMultiEventListener(eventHandler, true, clzList);
    }

    /**
     * 添加系列事件监听  一次触发
     */
    @SafeVarargs
    public final EventListener addMultiEventListener(Consumer<IEvent> eventHandler, Class<? extends IEvent>... clzList) {
        return addMultiEventListener(eventHandler, false, clzList);
    }

    @SafeVarargs
    private final EventListener addMultiEventListener(Consumer<IEvent> eventHandler, boolean isRepeat, Class<? extends IEvent>... clzList) {
        eventListenerUniqueId++;
        List<String> eventNameList = new ArrayList<>();
        for (Class<? extends IEvent> clz : clzList) {
            eventNameList.add(clz.getSimpleName());
        }
        EventListener eventListener = EventListener.valueOf(this, eventListenerUniqueId, eventNameList, eventHandler, isRepeat);
        for (String eventName : eventNameList) {
            Map<Integer, EventListener> eventListeners = event2eventHandlerMap.computeIfAbsent(eventName, k -> new HashMap<>());
            eventListeners.put(eventListenerUniqueId, eventListener);
        }
        return eventListener;
    }

    /**
     * 触发事件回调
     */
    public <E extends IEvent> void dispatch(E event) {
        String eventName = event.getClass().getSimpleName();
        Map<Integer, EventListener> eventListenerMap = event2eventHandlerMap.get(eventName);
        if (eventListenerMap == null) {
            return;
        }
        for (EventListener eventListener : new ArrayList<>(eventListenerMap.values())) {
            // 可能存在两个回调监听同一事件，一个回调取消了另一个回调的监听 所以先判下
            if (eventListener.isCancel()) {
                continue;
            }
            try {
                eventListener.getEventHandler().accept(event);
            } catch (Exception e) {
                WechatLog.error("EventDispatcher dispatch {} error", eventName, e);
            }
            // 1个事件触发了这个监听，监听的逻辑处理里取消过这个监听 判下
            if (eventListener.isCancel()) {
                continue;
            }
            if (!eventListener.isRepeat()) {
                eventListener.cancel();
            }
        }
    }

    /**
     * 移除事件监听
     */
    public void onListenerCancel(EventListener eventListener) {
        for (String eventName : eventListener.getEventNameList()) {
            Map<Integer, EventListener> eventListenerMap = event2eventHandlerMap.get(eventName);
            if (eventListenerMap != null) {
                eventListenerMap.remove(eventListener.getUniqueId());
            }
        }
    }

    public void clear() {
        event2eventHandlerMap.clear();
    }
}

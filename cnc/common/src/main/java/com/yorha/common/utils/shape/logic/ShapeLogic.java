package com.yorha.common.utils.shape.logic;

import com.google.common.collect.Maps;
import com.yorha.common.utils.shape.Shape;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;

/**
 * 图形逻辑处理类
 *
 * <AUTHOR>
 * 2021年11月02日 17:19:00
 */
public class ShapeLogic {
    private static final Logger LOGGER = LogManager.getLogger(ShapeLogic.class);
    private static final Map<Triple<LogicType, Class, Class>, IShapeIntersectsHandle> CACHE = Maps.newHashMap();

    /**
     * 逻辑枚举
     */
    public enum LogicType {
        // 相交
        CONTACT {
            @Override
            public void init() {
                for (Method method : ContactLogic.class.getMethods()) {
                    Parameter[] parameters = method.getParameters();
                    if (isRegister(parameters)) {
                        continue;
                    }
                    CACHE.put(formatKey(LogicType.CONTACT, parameters[0].getType(), parameters[1].getType()), (b, c) -> exceptionHandle(method, b, c));
                    CACHE.put(formatKey(LogicType.CONTACT, parameters[1].getType(), parameters[0].getType()), (b, c) -> exceptionHandle(method, c, b));
                }
            }
        },
        // 包含
        CONTAIN {
            @Override
            public void init() {
                for (Method method : ContainLogic.class.getMethods()) {
                    Parameter[] parameters = method.getParameters();
                    if (isRegister(parameters)) {
                        continue;
                    }
                    CACHE.put(formatKey(LogicType.CONTAIN, parameters[0].getType(), parameters[1].getType()), (b, c) -> exceptionHandle(method, b, c));
                }
            }
        };

        public abstract void init();
    }

    // 相交判定函数准备
    static {
        for (LogicType type : LogicType.values()) {
            type.init();
        }
    }


    /**
     * 需要注册
     */
    private static boolean isRegister(Parameter[] parameters) {
        return parameters == null
                || parameters.length < 2
                || parameters[0].getType().getClassLoader() != parameters[1].getType().getClassLoader()
                || parameters[0].getType().getClassLoader() != Shape.class.getClassLoader();
    }

    /**
     * key 由logic类 + param类2 + param类2组成
     */
    private static Triple<LogicType, Class, Class> formatKey(LogicType a, Class b, Class c) {
        return new MutableTriple<>(a, b, c);
    }

    /**
     * 反射调用，对异常进行处理
     *
     * @return 是否相交
     */
    private static boolean exceptionHandle(Method method, Shape a, Shape b) {
        try {
            return (boolean) method.invoke(null, a, b);
        } catch (Exception e) {
            LOGGER.error("ShapeUtil error.", e);
            return false;
        }
    }


    /**
     * 获取逻辑结果
     */
    public static boolean logicResult(LogicType type, Shape param1, Shape param2) {
        IShapeIntersectsHandle iShapeIntersectsHandle = CACHE.get(formatKey(type, param1.getClass(), param2.getClass()));
        if (iShapeIntersectsHandle == null) {
            LOGGER.error("ShapeUtil method missing: param1:{}, param2:{}", param1.getClass().getName(), param2.getClass().getName());
            return false;
        }
        return iShapeIntersectsHandle.intersects(param1, param2);
    }

}

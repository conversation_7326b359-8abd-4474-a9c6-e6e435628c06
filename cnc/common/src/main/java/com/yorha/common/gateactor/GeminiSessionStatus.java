package com.yorha.common.gateactor;

/**
 * 标记Session所处状态
 *
 * <AUTHOR>
 */
public enum GeminiSessionStatus {
    /**
     * 对象创建好阶段
     */
    SESSION_STATUS_NONE,
    /**
     * 连接创建阶段
     */
    SESSION_STATUS_CONNECTED,
    /**
     * 尝试鉴权阶段
     */
    SESSION_STATUS_TRY_AUTHORIZE,
    /**
     * 授权已完成阶段
     */
    SESSION_STATUS_AUTHORIZED,
    /**
     * 尝试注册或登录
     */
    SESSION_STATUS_TRY_REGISTER_OR_LOGIN,
    /**
     * 角色已绑定阶段
     */
    SESSION_STATUS_ROLE_BOUND,
    /**
     * 网关主动踢人中
     */
    SESSION_STATUS_KICK_OFF,
    /**
     * 连接断开阶段
     */
    SESSION_STATUS_DISCONNECT
}

package com.yorha.common.concurrent.executor;

import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.*;

/**
 * 通用协程池子。
 *
 * <AUTHOR>
 */
public class GeminiFiberPoolExecutor extends ThreadPoolExecutor {
    private static final Logger LOGGER = LogManager.getLogger(GeminiFiberPoolExecutor.class);
    private static final RejectedExecutionHandler REJECTED_EXECUTION_HANDLER =
            (r, executor) -> LOGGER.error("fiber executor {} refuse {}! queue is full!", ((GeminiFiberPoolExecutor) executor).getName(), r);

    private final String name;
    private final GeminiThreadPoolExecutor scheduler;

    private GeminiFiberPoolExecutor(Builder builder) {
        super(builder.fiberSize, builder.fiberSize, 0, TimeUnit.SECONDS, builder.newBlockingQueue(), builder.newThreadFactory(), builder.rejectedExecutionHandler);
        this.name = builder.name;
        this.scheduler = builder.scheduler;
    }

    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        // 每个任务执行前
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        // 每个任务执行后
    }

    /**
     * 线程池终止时候的回调。
     */
    @Override
    protected void terminated() {
        LOGGER.info("executor {} terminated!", this);
    }

    public String getName() {
        return name;
    }

    public GeminiThreadPoolExecutor getScheduler() {
        return scheduler;
    }

    @Override
    public String toString() {
        return "GeminiFiberPoolExecutor{" +
                "name='" + name + '\'' +
                ", fiberPool=" + super.toString() +
                '}';
    }

    public static Builder newBuilder(final String name, final GeminiThreadPoolExecutor scheduler, final int fiberSize) {
        final Builder builder = new Builder();
        return builder.name(name).scheduler(scheduler).fiberSize(fiberSize);
    }

    public static class Builder {
        private String name;
        private int fiberSize = 0;
        private int taskSize = 0;
        private RejectedExecutionHandler rejectedExecutionHandler = REJECTED_EXECUTION_HANDLER;
        private Thread.UncaughtExceptionHandler uncaughtExceptionHandler = ConcurrentHelper.UNCAUGHT_EXCEPTION_HANDLER;
        private GeminiThreadPoolExecutor scheduler = null;

        private Builder() {
        }

        /**
         * 设置协程池名称。
         *
         * @param name 名称。
         * @return Builder。
         */
        public Builder name(final String name) {
            if (StringUtils.isEmpty(name)) {
                throw new NullPointerException("name is empty");
            }
            this.name = name;
            return this;
        }

        /**
         * 设置协程数量大小。
         *
         * @param fiberSize 线程数量
         * @return Builder。
         */
        public Builder fiberSize(final int fiberSize) {
            if (fiberSize <= 0) {
                throw new IllegalArgumentException("fiberSize <= 0");
            }
            this.fiberSize = fiberSize;
            return this;
        }

        /**
         * 线程池过载的保护，导致丢弃消息时候的回调。
         *
         * @param handler 回调。
         * @return Builder。
         */
        public Builder rejectExecuteHandler(RejectedExecutionHandler handler) {
            if (handler == null) {
                throw new NullPointerException("handler is null!");
            }
            this.rejectedExecutionHandler = handler;
            return this;
        }

        /**
         * 线程异常状态未处理时候的回调。
         *
         * @param handler 回调。
         * @return Builder。
         */
        public Builder uncaughtExecutionHandler(Thread.UncaughtExceptionHandler handler) {
            if (handler == null) {
                throw new NullPointerException("handler is null!");
            }
            this.uncaughtExceptionHandler = handler;
            return this;
        }

        /**
         * 当前等待协程的堆积任务上限。
         *
         * @param taskSize 堆积任务上限。
         * @return Builder。
         */
        public Builder taskSize(final int taskSize) {
            if (taskSize < 0) {
                throw new IllegalArgumentException("taskSize < 0");
            }
            this.taskSize = taskSize;
            return this;
        }

        /**
         * 协程调度器。
         *
         * @param scheduler 调度器。
         * @return Builder。
         */
        public Builder scheduler(final GeminiThreadPoolExecutor scheduler) {
            if (scheduler == null) {
                throw new NullPointerException("scheduler is null!");
            }
            this.scheduler = scheduler;
            return this;
        }

        private BlockingQueue<Runnable> newBlockingQueue() {
            if (this.taskSize == 0) {
                return new LinkedBlockingQueue<>();
            }
            return new LinkedBlockingQueue<>(this.taskSize);
        }

        private ThreadFactory newThreadFactory() {
            return ConcurrentHelper.newFiberFactory(this.name, this.scheduler, this.uncaughtExceptionHandler);
        }

        public GeminiFiberPoolExecutor build() {
            return new GeminiFiberPoolExecutor(this);
        }
    }
}

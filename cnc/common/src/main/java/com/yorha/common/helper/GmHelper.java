package com.yorha.common.helper;

import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.MailUtil;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.StructMail;
import res.template.ConstTemplate;

public class GmHelper {

    public static void sendGmNtfMail(int zoneId, long playerId, String key, String s) {
        StructMail.MailSendParams.Builder builder = StructMail.MailSendParams.newBuilder();
        builder.setMailTemplateId(ResHolder.getInstance().getConstTemplate(ConstTemplate.class).getPersonalMailId());
        StructMail.MailContent.Builder contentBuilder = StructMail.MailContent.newBuilder();
        contentBuilder.setContentType(CommonEnum.MailContentType.MAIL_CONTENT_TYPE_CUSTOM_DATA);
        contentBuilder.getDisplayDataBuilder().getParamsBuilder().addDatas(MsgHelper.buildDisPlayText(s));

        StructMail.MailShowTitle.Builder titleBuilder = StructMail.MailShowTitle.newBuilder()
                .setTitle(key)
                .setSubTitle(key);

        builder.setContent(contentBuilder.build());
        builder.setTitle(titleBuilder);


        MailUtil.sendMailToPlayer(CommonMsg.MailReceiver.newBuilder().setPlayerId(playerId).setZoneId(zoneId).build(),
                builder.build());

    }
}

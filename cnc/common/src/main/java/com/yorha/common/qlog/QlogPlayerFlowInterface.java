package com.yorha.common.qlog;

/**
 * 玩家流水接口
 *
 * <AUTHOR>
 */
public interface QlogPlayerFlowInterface {
    String getGameappid();

    String getVOpenID();

    String getAccountCreate_time();

    String getAccountReg_time();

    String getServer_type();

    String getIZoneAreaID();

    String getGameSvrId();

    String getWorldId();
    
    String getNow_coordinate();

    String getVRoleID();

    String getVRoleName();

    int getRole_num();

    String getRole_type();

    String getRoleCreate_time();

    int getILevel();

    int getIVipLevel();

    long getIRoleCE();

    long getHighestRole_power();

    int getPlayerFriendsNum();

    float getRecharge_sum();

    long getIGuildID();

    String getVGuildName();

    String getGuildGrade();

    String getGuildClass();

    int getPlatID();

    String getGame_language();

    String getVClientIP();

    String getVClientIPV6();

    long getKill_num();

    String getClientVersion();

    int getMoney_config();

    int getTransferOrNot();
}

package com.yorha.common.aoiView;

import com.yorha.common.actor.AoiViewService;
import com.yorha.common.actor.AoiViewServices;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.aoiView.manager.AoiGridType;
import com.yorha.common.aoiView.manager.AoiNodeManager;
import com.yorha.common.aoiView.manager.IActorForAoiTimer;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class AoiViewActor extends BaseGameActor implements AoiViewServices, IActorForAoiTimer {
    private final AoiViewService viewService;
    private AoiNodeManager manager;
    private int zoneId;

    public AoiViewActor(ActorSystem system, IActorRef self) {
        super(system, self);
        viewService = new AoiViewServiceImpl(this);
    }

    public AoiNodeManager getManager() {
        return manager;
    }

    public void init(int zoneId, int mapWidth, int mapHeight, int xNum, int yNum) {
        this.zoneId = zoneId;
        manager = new AoiNodeManager(this, this, zoneId, AoiGridType.AGT_BRIEF, mapWidth, mapHeight, xNum, yNum);
    }

    @Override
    public int getZoneId() {
        return zoneId;
    }

    @Override
    public AoiViewService getAoiViewService() {
        return viewService;
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    @Override
    @Nullable
    public ActorTimer addAoiTimer(
            String prefix,
            TimerReasonType timerReasonType,
            Runnable runnable,
            long initialDelay,
            long period,
            TimeUnit unit,
            boolean isFix
    ) {
        return dangerousAddRepeatTimer(prefix, timerReasonType, runnable, initialDelay, period, unit, isFix);
    }
}

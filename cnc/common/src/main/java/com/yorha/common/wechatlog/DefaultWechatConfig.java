package com.yorha.common.wechatlog;

public class DefaultWechatConfig implements WechatConfig {

    @Override
    public String getBusId() {
        return "555.0.4.0";
    }

    @Override
    public String getServerVersion() {
        return null;
    }

    @Override
    public int getWorldId() {
        return 555;
    }

    @Override
    public boolean getWechatLogEnable() {
        return true;
    }

    @Override
    public boolean getWechatLogSync() {
        return true;
    }

    @Override
    public String getWeChatKey(String name) {
        return switch (name) {
            case "wechat_log_host_name" -> "https://qyapi.weixin.qq.com";
            case "wechat_server_err_key" -> "0cef16ef-8f11-454a-84f7-41d8aa58ab78";
            case "wechat_designer_err_key" -> "d402cc66-335b-4a87-aa07-d2ab6a670c0d";
            default -> "";
        };
    }
}

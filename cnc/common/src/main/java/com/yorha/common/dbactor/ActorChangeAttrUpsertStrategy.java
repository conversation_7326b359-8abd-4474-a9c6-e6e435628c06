package com.yorha.common.dbactor;

import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.db.tcaplus.result.UpdateResult;
import com.yorha.common.server.ServerContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

/**
 * 业务只需重载接口并提供实现，不允许单独使用这个类。
 *
 * @param <T> 类型。
 * <AUTHOR>
 */
public abstract class ActorChangeAttrUpsertStrategy<T extends GeneratedMessageV3> implements IDbChangeUpsertStrategy {
    private static final Logger LOGGER = LogManager.getLogger(ActorChangeAttrUpsertStrategy.class);
    /**
     * 上次落库时候的全量数据，缓存是为了防止上次落库丢失导致数据错乱。
     */
    private T lastFullAttrSaveData;
    /**
     * 增量数据的builder。
     */
    private GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder;
    /**
     * 上次落库的校验数据值。
     */
    private T lastDirtyAttrCollectCheckData;
    /**
     * 数据是否为脏。
     */
    private boolean isDirty = false;

    private int dirtyId = 0;

    public ActorChangeAttrUpsertStrategy(GeneratedMessageV3 msg) {
        if (msg == null) {
            throw new NullPointerException("msg is null!");
        }
        this.changeAttrSaveDataBuilder = (GeneratedMessageV3.Builder<?>) msg.toBuilder();
        this.lastDirtyAttrCollectCheckData = null;
        this.lastFullAttrSaveData = null;
    }

    @Override
    public final int getDirtyId() {
        return this.dirtyId;
    }

    @Override
    public final int collectDirtyForSave(final AbstractActor actor) {
        final boolean hasDirty = this.collectDirty4ChangeAttrSave(actor, this.changeAttrSaveDataBuilder);
        if (hasDirty) {
            this.dirtyId++;
        }
        this.isDirty = hasDirty || this.isDirty;
        if (!this.isDirty) {
            return -1;
        }
        if (ServerContext.checkChangeAttr()) {
            this.lastDirtyAttrCollectCheckData = this.buildFullAttrSaveData(actor);
        }
        return this.dirtyId;
    }

    @Override
    @SuppressWarnings("unchecked")
    public final Message.Builder buildInsertRequest(final AbstractActor actor) {
        T fullAttrSaveData = this.buildFullAttrSaveData(actor);
        T changeAttrSaveData = (T) this.changeAttrSaveDataBuilder.getDefaultInstanceForType();
        return this.newDbSaveRequest(actor, fullAttrSaveData, changeAttrSaveData);
    }

    @Override
    @SuppressWarnings("unchecked")
    public final Request buildUpdateRequest(AbstractActor actor) {
        final T fullAttrSaveData;
        T checkAttr = this.lastDirtyAttrCollectCheckData;
        T changeAttrSaveData = (T) this.changeAttrSaveDataBuilder.build();
        final boolean isLastFullAttrSaved;
        final boolean isLastChangeAttrSaved;
        if (changeAttrSaveData.getSerializedSize() > this.getTriggerFullAttrSaveSize()) {
            // 准备全量数据和更新数据
            fullAttrSaveData = this.buildFullAttrSaveData(actor);
            changeAttrSaveData = (T) changeAttrSaveData.getDefaultInstanceForType();
            // 准备校验数据
            checkAttr = checkAttr != null ? fullAttrSaveData : null;
            this.changeAttrSaveDataBuilder = (GeneratedMessageV3.Builder<?>) changeAttrSaveData.toBuilder();
            isLastFullAttrSaved = true;
            isLastChangeAttrSaved = false;
        } else {
            // 可能由于上次的更新超时了，此次更新需要补偿更新。
            fullAttrSaveData = this.lastFullAttrSaveData;
            isLastFullAttrSaved = fullAttrSaveData != null;
            isLastChangeAttrSaved = true;
        }
        // 更新lastFullAttr
        this.lastFullAttrSaveData = fullAttrSaveData;
        // 构建请求
        return new Request(this.newDbSaveRequest(actor, fullAttrSaveData, changeAttrSaveData), isLastFullAttrSaved, isLastChangeAttrSaved, checkAttr);
    }

    @Override
    public final void clearDirtyWhenSaveOk(final AbstractActor actor, final int dirtyId) {
        this.lastFullAttrSaveData = null;
        this.lastDirtyAttrCollectCheckData = null;
        if (this.dirtyId == dirtyId) {
            this.isDirty = false;
        }

    }

    @Override
    public final boolean checkDbResultWhenNeeded(final AbstractActor actor, final UpdateResult<Message.Builder> result, final GeneratedMessageV3 checkAttr) {
        if (checkAttr == null) {
            return true;
        }
        final T o = this.buildFullAttrSaveData(result);
        if (!checkAttr.equals(o)) {
            // 出现异常 || Tcaplus code不对
            LOGGER.error("assertUpdateRight! actor={}, wantAttr=\n{}dbAttr=\n{}", actor, checkAttr, o);
            LOGGER.error("checkAttr fail! actor={}, value={}", actor, result.value);
            return false;
        }
        return true;
    }

    /**
     * 触发全量落库的增量数据阈值。
     *
     * @return 字节数据大小。
     */
    protected abstract int getTriggerFullAttrSaveSize();

    /**
     * 收集脏数据到增量落库的数据容器中。
     *
     * @param actor                     被收集的Actor。
     * @param changeAttrSaveDataBuilder 增量数据容器。
     * @return 是否收集到脏数据。
     */
    protected abstract boolean collectDirty4ChangeAttrSave(AbstractActor actor, GeneratedMessageV3.Builder<?> changeAttrSaveDataBuilder);

    /**
     * 根据actor打出全量落库的数据。
     *
     * @param actor 被收集的Actor。
     * @return 全量数据。
     */
    protected abstract T buildFullAttrSaveData(AbstractActor actor);

    /**
     * 根据db返回结果打出全量落库的数据。
     *
     * @param result db返回结果。
     * @return 全量数据。
     */
    protected abstract T buildFullAttrSaveData(UpdateResult<Message.Builder> result);

    /**
     * 构建落库结果。
     *
     * @param actor      actor。
     * @param fullAttr   全量数据，可能为null，为null本字段不更新。
     * @param changeAttr 增量数据，不可能为null。
     * @return 数据库请求
     */
    protected abstract Message.Builder newDbSaveRequest(AbstractActor actor, T fullAttr, @NotNull T changeAttr);
}

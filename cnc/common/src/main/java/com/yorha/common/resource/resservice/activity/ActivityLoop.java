package com.yorha.common.resource.resservice.activity;

import com.google.common.collect.ImmutableMap;
import com.yorha.common.utils.time.TimeUtils;

import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.Map;

public interface ActivityLoop {
    Map<String, ActivityLoop> LOGIC_MAP = ImmutableMap.of(
            "每周", new EveryWeek(),
            "每天", new EveryDay()
    );

    static ActivityLoop of(String loopType) {
        return LOGIC_MAP.get(loopType);
    }


    Instant withFixedParam(Instant instant, int loopParam);

    long intervalMs(int loopCycle);

    final class EveryWeek implements ActivityLoop {
        static final long ONE_WEEK_MS = Duration.of(7, ChronoUnit.DAYS).toMillis();

        @Override
        public Instant withFixedParam(Instant instant, int loopParam) {
            ZonedDateTime instantZDT = ZonedDateTime.ofInstant(instant, TimeUtils.getZoneId());
            Instant curWeekWithDay = instantZDT.with(ChronoField.DAY_OF_WEEK, loopParam).toInstant();
            if (curWeekWithDay.isBefore(instant)) {
                return curWeekWithDay.plus(7, ChronoUnit.DAYS);
            }
            return curWeekWithDay;
        }

        @Override
        public long intervalMs(int loopCycle) {
            return loopCycle * ONE_WEEK_MS;
        }
    }

    final class EveryDay implements ActivityLoop {
        static final long ONE_DAY_MS = Duration.of(1, ChronoUnit.DAYS).toMillis();

        @Override
        public Instant withFixedParam(Instant instant, int loopParam) {
            return TimeUtils.getDayStartInstant(instant);
        }

        @Override
        public long intervalMs(int loopCycle) {
            return loopCycle * ONE_DAY_MS;
        }
    }

    static void main(String[] args) {
        Instant instant = ZonedDateTime.of(2023, 5, 8, 0, 0, 0, 0, TimeUtils.getZoneId()).toInstant();
        Instant fixed = new EveryWeek().withFixedParam(instant, 1);
        System.out.println(instant);
        System.out.println(fixed);
    }

}

package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.UpsertResult;

/**
 * Update Or Insert接口。
 *
 * <AUTHOR>
 */
public class TcaplusUpsert<T extends Message.Builder> extends TcaplusOperation<T, UpsertOption, UpsertResult<T>> {
    public TcaplusUpsert(Client client, T t, UpsertOption upsertOption) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, upsertOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_REPLACE_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        // 设置flag
        request.setResultFlag(this.getOption().getResultFlag());

        final Record record = request.addRecord();
        this.setRequestKeys(this.getReq(), record);
        this.setRequestValues(this.getReq(), record);
        // 设置版本
        record.setVersion(getOption().getVersion());

    }

    @Override
    protected UpsertResult<T> buildResult(Response response) {
        final UpsertResult<T> result = new UpsertResult<>();
        result.code = TcaplusErrorCode.forNumber(response.getResult());

        Record record;
        // Tcaplus SDK 所有proxy宕机保护
        try {
            record = response.fetchRecord();
        } catch (NullPointerException e) {
            record = null;
        }
        if (record == null) {
            return result;
        }
        result.value = buildDefaultValue(this.getReq());
        result.version = record.getVersion();
        this.readFromResponseValues(record, result.value);
        return result;
    }
}

package com.yorha.common.utils;

import com.google.common.collect.Lists;
import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import com.google.protobuf.Message;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actor.node.IMonitorActor;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.dbactor.DbMgrService;
import com.yorha.common.enums.MonitorMessageType;
import com.yorha.common.perf.DbPerfLogger;
import com.yorha.common.server.ServerContext;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.Clan;
import com.yorha.proto.Player;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/23
 */
public class MonitorUtils {
    private static final Logger LOGGER = LogManager.getLogger(MonitorUtils.class);

    public static void monitorPlayerEntity(AbstractActor actor, Player.PlayerEntity fullAttr) {
        try {
            if (!ServerContext.getMonitorOpen()) {
                return;
            }
            // 线上大于200k，除此之外都是50k，才触发monitor
            int reportSize;
            if (ServerContext.isProdSvr()) {
                reportSize = MonitorConstant.PLAYER_ENTITY_MIN_REPORT_BYTE_SIZE;
            } else {
                reportSize = 50 * 1024;
            }
            int serializedSize = fullAttr.getSerializedSize();
            if (serializedSize < reportSize) {
                return;
            }
            // 同一个人或者没有别人大，就不触发monitor
            DbPerfLogger dbPerfLogger = DbMgrService.getInstance().getDbPerfLogger();
            long maxPlayerDbSize = dbPerfLogger.getMaxPlayerDbSize();
            long maxPlayerId = dbPerfLogger.getMaxPlayerId();
            if (serializedSize <= maxPlayerDbSize || fullAttr.getId() == maxPlayerId) {
                return;
            }
            actor.tell(RefFactory.ofLocalMonitor(), new ActorRunnable<IMonitorActor>(
                    "monitorPlayerEntity",
                    monitorActor -> monitorActor.monitorMessage(MonitorMessageType.PLAYER_ENTITY, fullAttr)
            ));
        } catch (Exception e) {
            LOGGER.error("MonitorUtils monitorPlayerEntity failed ", e);
        }
    }

    public static void monitorClanEntity(AbstractActor actor, Clan.ClanEntity fullAttr) {
        try {
            if (!ServerContext.getMonitorOpen()) {
                return;
            }
            // 线上大于200k，除此之外都是1k，才触发monitor
            int reportSize;
            if (ServerContext.isProdSvr()) {
                reportSize = MonitorConstant.CLAN_ENTITY_MIN_REPORT_BYTE_SIZE;
            } else {
                reportSize = 1024;
            }
            int serializedSize = fullAttr.getSerializedSize();
            if (serializedSize < reportSize) {
                return;
            }
            // 同一个人或者没有别人大，就不触发monitor
            DbPerfLogger dbPerfLogger = DbMgrService.getInstance().getDbPerfLogger();
            long maxClanDbSize = dbPerfLogger.getMaxClanDbSize();
            long maxClanId = dbPerfLogger.getMaxClanId();
            if (serializedSize <= maxClanDbSize || fullAttr.getId() == maxClanId) {
                return;
            }
            actor.tell(RefFactory.ofLocalMonitor(), new ActorRunnable<IMonitorActor>(
                    "monitorClanEntity",
                    monitorActor -> monitorActor.monitorMessage(MonitorMessageType.CLAN_ENTITY, fullAttr)
            ));
        } catch (Exception e) {
            LOGGER.error("MonitorUtils monitorClanEntity failed ", e);
        }
    }

    /**
     * 打印prop落库的内存大小
     *
     * @param minShowSize 小于指定byte的字段不展示
     */
    public static String printPropMemory(Message msg, int minShowSize) {
        List<MemoryCheck> memoryChecks = getMemory(msg, minShowSize);
        StringBuilder sb = new StringBuilder();
        sb.append("\n").append(msg.getDescriptorForType().getName()).append("总大小=").append(printKbOrB(msg.getSerializedSize()));
        for (MemoryCheck memoryCheck : memoryChecks) {
            String print = memoryCheck.print(minShowSize);
            if (StringUtils.isEmpty(print)) {
                continue;
            }
            sb.append("\n");
            sb.append(print);
        }
        return sb.toString();
    }

    public static List<MemoryCheck> getMemory(Message msg, int minShowSize) {
        Descriptors.Descriptor descriptor = msg.getDescriptorForType();
        List<MemoryCheck> ret = Lists.newArrayList();

        for (Descriptors.FieldDescriptor field : descriptor.getFields()) {
            // 去掉map里的clearFlag，deleteKeys字段
            if (Objects.equals(field.getName(), "clearFlag") || Objects.equals(field.getName(), "deleteKeys")) {
                continue;
            }

            // 创建一个仅包含单个字段的动态消息
            DynamicMessage dynamicMessage = DynamicMessage.newBuilder(descriptor)
                    .setField(field, msg.getField(field))
                    .build();

            // 序列化这个动态消息并计算大小
            int size = dynamicMessage.getSerializedSize();
            // 已经很小了，就不要放到容器里面了
            if (size < minShowSize) {
                continue;
            }

            MemoryCheck memoryCheck = new MemoryCheck(field.getName(), size);
            if (field.getType() == Descriptors.FieldDescriptor.Type.MESSAGE && !field.isRepeated()) {
                Message childMessage = (Message) msg.getField(field);
                memoryCheck.attr.addAll(getMemory(childMessage, minShowSize));
            }
            if (field.isRepeated()) {
                memoryCheck.capacity = msg.getRepeatedFieldCount(field);
                for (int i = 0; i < memoryCheck.capacity; i++) {
                    Object repeatedField = msg.getRepeatedField(field, i);
                    if (repeatedField instanceof Message) {
                        Message childMessage = (Message) repeatedField;
                        memoryCheck.attr.addAll(getMemory(childMessage, minShowSize));
                    }
                }
            }

            ret.add(memoryCheck);
        }
        ret.sort(Comparator.comparingInt(MemoryCheck::getSize).reversed());
        return ret;
    }

    private static final BigDecimal ONE_KB = new BigDecimal(1024);

    private static String printKbOrB(int bytes) {
        if (bytes < ONE_KB.intValue()) {
            return bytes + "B";
        } else {
            BigDecimal dividend = new BigDecimal(bytes);
            BigDecimal result = dividend.divide(ONE_KB, 2, RoundingMode.HALF_UP);
            return result + "kB";
        }
    }

    private static class MemoryCheck {
        public String fieldName;
        public int size;
        public List<MemoryCheck> attr = Lists.newArrayList();
        public int capacity;

        MemoryCheck(String fieldName, int size) {
            this.fieldName = fieldName;
            this.size = size;
        }

        public int getSize() {
            return size;
        }

        @Override
        public String toString() {
            return fieldName + "=" + size;
        }

        public String print(int minShowSize) {
            return print(1, minShowSize);
        }

        private String print(int level, int minShowSize) {
            boolean noShow = size < minShowSize;
            if (noShow) {
                return null;
            }

            StringBuilder sb = new StringBuilder();
            sb.append(fieldName).append(": ").append(printKbOrB(size));
            if (capacity > 0) {
                sb.append(" capacity: ").append(capacity);
            }
            if (attr.isEmpty()) {
                return sb.toString();
            }

            // 打印子属性信息
            StringBuilder childSb = new StringBuilder();
            for (MemoryCheck memoryCheck : attr) {
                String print = memoryCheck.print(level + 1, minShowSize);
                if (StringUtils.isEmpty(print)) {
                    continue;
                }
                if (childSb.length() > 0) {
                    childSb.append("\n");
                }
                for (int j = 0; j < level; j++) {
                    childSb.append("----");
                }
                childSb.append(print);
            }

            if (StringUtils.isEmpty(childSb.toString())) {
                return sb.toString();
            }
            sb.append("\n");
            sb.append(childSb);
            return sb.toString();
        }
    }
}

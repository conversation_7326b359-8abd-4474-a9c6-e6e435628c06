package com.yorha.common.actorservice;

import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.schedule.ScheduleMgr;
import com.yorha.common.utils.time.schedule.ScheduleTask;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

/**
 * actor定时器
 * 在actor销毁时会自动取消
 *
 * <AUTHOR>
 */
public class ActorTimer {
    private final AbstractActor owner;
    private final ScheduleTask task;
    private final Runnable runnable;


    private ActorTimer(AbstractActor owner, ScheduleTask task, Runnable runnable) {
        this.owner = owner;
        this.task = task;
        this.runnable = runnable;
    }

    @Nullable
    public static ActorTimer of(AbstractActor owner, String name, Runnable runnable, long initialDelay, TimeUnit unit) {
        ScheduleTask task = ScheduleMgr.getInstance().schedule(owner.self(), owner.genTimerId(), name, initialDelay, unit);
        if (task == null) {
            return null;
        }
        return new ActorTimer(owner, task, runnable);
    }

    @Nullable
    public static ActorTimer ofRepeat(AbstractActor owner, String name, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        ScheduleTask task = ScheduleMgr.getInstance().scheduleWithRepeatDelay(owner.self(), owner.genTimerId(), name, initialDelay, period, unit);
        if (task == null) {
            return null;
        }
        return new ActorTimer(owner, task, runnable);
    }

    @Nullable
    public static ActorTimer ofFixRepeat(AbstractActor owner, String name, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        ScheduleTask task = ScheduleMgr.getInstance().scheduleWithFixDelay(owner.self(), owner.genTimerId(), name, initialDelay, period, unit);
        if (task == null) {
            return null;
        }
        return new ActorTimer(owner, task, runnable);
    }

    public static boolean isOverLimit(long targetTsMs) {
        long maxTime = ScheduleMgr.getInstance().getMaxTimeTs();
        return targetTsMs > maxTime;
    }

    public void cancel() {
        if (ServerContext.getServerStopStep() != 0 && task.isCanceled()) {
            return;
        }
        task.cancel();
        owner.dangerousRemoveTimer(getName());
    }

    /**
     * 只通知定时器取消  遍历取消全部时使用
     */
    public void onlyCancel() {
        if (ServerContext.getServerStopStep() != 0 && task.isCanceled()) {
            return;
        }
        task.cancel();
    }

    public boolean isCanceled() {
        return task.isCanceled();
    }

    public boolean isRepeat() {
        return task.isRepeat();
    }

    public String getName() {
        return task.getName();
    }

    public int getId() {
        return task.getId();
    }

    public long getNextExecuteTime() {
        return task.getNextExecuteTime();
    }

    public void run() {
        runnable.run();
    }

    @Override
    public String toString() {
        return "ActorTimer{" +
                "task=" + task.getName() +
                ", owner=" + owner +
                '}';
    }
}
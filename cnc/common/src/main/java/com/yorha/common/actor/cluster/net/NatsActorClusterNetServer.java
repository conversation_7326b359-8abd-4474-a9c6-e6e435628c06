package com.yorha.common.actor.cluster.net;

import com.yorha.common.actor.msg.ActorSerializer;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.constant.MonitorConstant;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import io.nats.client.*;
import io.nats.client.api.ServerInfo;
import io.nats.client.support.Status;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.io.IOException;
import java.time.Duration;

/**
 * 基于nats的集群通信解决方案。
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class NatsActorClusterNetServer implements IActorClusterNetServer {
    private static final Logger LOGGER = LogManager.getLogger(NatsActorClusterNetServer.class);

    /**
     * nats服务器列表。
     */
    private final String natsServerUrl;
    /**
     * 当前服务器busId。
     */
    private final String busId;
    /**
     * 序列化反序列化工具。
     */
    private final ActorSerializer serializer;
    /**
     * nats连接。
     */
    private Connection natsConnection;
    /**
     * 流入消息分配器。
     */
    private Dispatcher dispatcher;

    private boolean isShutDown = false;

    public NatsActorClusterNetServer(String natsUrl, String busId, ActorSerializer serializer) {
        this.natsServerUrl = natsUrl;
        this.busId = busId;
        this.serializer = serializer;
    }

    @Override
    public void start() {
        if (this.natsConnection != null) {
            throw new GeminiException("start {} again!", this);
        }
        try {
            final Options options = (new Options.Builder()).server(this.natsServerUrl)
                    // 外出的消息数量，使用默认参数5000
                    .maxMessagesInOutgoingQueue(5000 * 2)
                    // 使用默认参数false
                    // .discardMessagesWhenOutgoingQueueFull()
                    // 连接重试次数，使用默认60。每次理论使用4秒时间，默认停止4分钟。目前重试4000分钟。
                    .maxReconnects(60 * 1000)
                    // 连接超时，默认2秒
                    .connectionTimeout(Duration.ofSeconds(2))
                    // 重连的间隔时间, 默认2000ms
                    .reconnectWait(Duration.ofMillis(2000))
                    // 客户端检查包体大小，由服务端告知，检查。使用默认true。
                    .clientSideLimitChecks(true)
                    // 连接名为busId
                    .connectionName(this.busId)
                    .connectionListener((conn, type) -> {
                        // 连接变化打warning，普通连接打info
                        if (type == ConnectionListener.Events.CONNECTED) {
                            LOGGER.info("nats connectionListener type={}, hostPort={}", type, getConnectionHostPort(conn));
                        } else {
                            LOGGER.warn("nats connectionListener type={}, hostPort={}", type, getConnectionHostPort(conn));
                        }

                    })
                    // 不设置errorListener，项目会使用默认的listener，输出打印到控制台
                    .errorListener(new NatsErrorListener())
                    .build();
            this.natsConnection = Nats.connect(options);
            this.dispatcher = this.natsConnection.createDispatcher();
            this.dispatcher.subscribe(this.busId, new NatsMessageHandler(serializer));
        } catch (IOException | InterruptedException e) {
            throw new GeminiException("nats connect failed! url {}!", this.natsServerUrl);
        }
    }

    @Override
    public void shutdown() {
        if (isShutDown) {
            return;
        }
        isShutDown = true;
        if (this.natsConnection == null) {
            throw new GeminiException("shutdown {}! not start!", this);
        }
        this.natsConnection.closeDispatcher(this.dispatcher);
        if (this.natsConnection != null) {
            try {
                this.natsConnection.close();
            } catch (Exception e) {
                LOGGER.error("gemini_system shutdown system! close nats fail!", e);
            }
        }
    }

    @Override
    public void sendMsg(String busId, ActorMsgEnvelope envelope) {
        try {
            long cur = SystemClock.nowNative();
            final byte[] serialize = this.serializer.serialize(envelope);
            // 统计
            ServerContext.getProcessPerfLogger().logSsOut(envelope.getSender().getActorRole(), envelope.getSimpleName(), serialize.length);
            this.natsConnection.publish(busId, serialize);
            long cost = SystemClock.nowNative() - cur;
            if (cost > MonitorConstant.NATS_PUSH_OVER_TIME) {
                LOGGER.warn("nats post cost:{}ms", cost);
            }
            if (serialize.length > MonitorConstant.NATS_MESSAGE_WARNING_LOGGER_BYTE_SIZE) {
                LOGGER.error("nats message near max payload, sender={}, receiver={}, msgType={}, msgPayload={}KB", envelope.getSender(),
                        envelope.getReceiver(), envelope.getSimpleName(), serialize.length / 1024);
                MonitorUnit.NATS_BIG_MSG_PAYLOAD_COUNTER.labels(ServerContext.getBusId()).inc();
            }

            MonitorUnit.NATS_PUSH_COUNTER.labels(ServerContext.getBusId()).inc();
        } catch (Exception e) {
            LOGGER.error("NatsActorClusterNetServer post busId={}, envelop={} fail, ", busId, e);
        }
    }

    @Override
    public String toString() {
        return "NatsNetServer{" +
                "natsUrl='" + natsServerUrl + '\'' +
                ", busId='" + busId + '\'' +
                '}';
    }

    private static String getConnectionHostPort(Connection conn) {
        if (conn == null) {
            return "Null";
        }
        final ServerInfo serverInfo = conn.getServerInfo();
        if (serverInfo == null) {
            return "NullServerInfo";
        }
        return serverInfo.getHost() + ":" + serverInfo.getPort();
    }

    private static class NatsErrorListener implements ErrorListener {


        @Override
        public void errorOccurred(Connection conn, String error) {
            LOGGER.error("nats {} errorOccurred {}", getConnectionHostPort(conn), error);
        }

        @Override
        public void exceptionOccurred(Connection conn, Exception exp) {
            LOGGER.error("nats {} exceptionOccurred", getConnectionHostPort(conn), exp);
        }

        @Override
        public void slowConsumerDetected(Connection conn, Consumer consumer) {
            LOGGER.warn("nats {} slowConsumerDetected {}", getConnectionHostPort(conn), consumer);
        }

        @Override
        public void messageDiscarded(Connection conn, Message msg) {
            LOGGER.error("nats {} messageDiscarded {}", getConnectionHostPort(conn), msg);
        }

        @Override
        public void heartbeatAlarm(Connection conn, JetStreamSubscription sub, long lastStreamSequence, long lastConsumerSequence) {
            LOGGER.warn("nats {} heartbeatAlarm sub {}, lastStreamSequence {}, lastConsumerSequence {}",
                    getConnectionHostPort(conn), sub, lastStreamSequence, lastConsumerSequence);
        }

        @Override
        public void unhandledStatus(Connection conn, JetStreamSubscription sub, Status status) {
            LOGGER.error("nats {} unhandledStatus sub {}, status {}", getConnectionHostPort(conn), sub, status);
        }

        @Override
        public void flowControlProcessed(Connection conn, JetStreamSubscription sub, String subject, FlowControlSource source) {
            LOGGER.warn("nats {} flowControlProcessed sub {}, subject {}, source {}", getConnectionHostPort(conn), sub, subject, source);
        }
    }

    /**
     * Nats处理函数回调。
     */
    private static class NatsMessageHandler implements MessageHandler {
        private final ActorSerializer serializer;

        private NatsMessageHandler(ActorSerializer serializer) {
            this.serializer = serializer;
        }

        @Override
        public void onMessage(Message msg) throws InterruptedException {
            try {
                Object msgObj = this.serializer.deserialize(msg.getData());
                if (msgObj instanceof ActorMsgEnvelope) {
                    ActorMsgEnvelope envelope = (ActorMsgEnvelope) msgObj;
                    final long sendTsMs = envelope.getSendTsMs();
                    final long cost = SystemClock.nowNative() - sendTsMs;
                    if (cost > MonitorConstant.NATS_RECEIVE_OVER_TIME) {
                        LOGGER.warn("gemini_perf recv nats {} ss to {} cost:{}ms", envelope, envelope.getReceiver(), cost);
                    }
                    MonitorUnit.NATS_RECEIVE_COUNTER.labels(ServerContext.getBusId()).inc();
                    // 统计
                    ServerContext.getProcessPerfLogger().logSsIn(envelope.getSimpleName(), msg.getData().length, cost);
                    // 广播
                    if (envelope.getReceivers() != null) {
                        for (IActorRef ref : envelope.getReceivers()) {
                            ActorSendMsgUtils.send(ref, envelope.getSender(), envelope.getPayload());
                        }
                        if (envelope.getReceivers().isEmpty()) {
                            LOGGER.error("recv {} ss, no receivers", envelope);
                        }
                        return;
                    }
                    // 单播
                    ActorMsgSystem.dispatchMsg(envelope);
                } else {
                    WechatLog.error("deserialized remote msg is not ActorMsgEnvelope, not recognized! {}", msgObj);
                }
            } catch (Throwable e) {
                LOGGER.error("Nats IO receive {}, but ", msg.getData(), e);
            }
        }
    }
}

package com.yorha.common.actor.node;

import com.google.protobuf.ByteString;
import com.yorha.common.actor.IActor;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;

/**
 * 同进程部署的时候，让gate到player的传递更简易
 */
public interface IPlayerActor extends IActor {

    void onReceiveClientMsg(int msgType, int seqId, byte[] content, ByteString msgBytes);

    void receivePlayerQlog(AbstractPlayerQlogFlow flow);
}

package com.yorha.common.resource.resservice.activity;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.Map;

/**
 * 连续礼包配置校验
 *
 * <AUTHOR>
 */
public class ContinuesGiftResService extends AbstractResService {

    public ContinuesGiftResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

    }

    @Override
    public void checkValid() throws ResourceException {
        final Map<Integer, ContinuesGiftActTemplate> configTemplateMap = getResHolder().getMap(ContinuesGiftActTemplate.class);
        final Map<Integer, MailTemplate> mailTemplateMap = getResHolder().getMap(MailTemplate.class);
        final Map<Integer, ContinuesGiftTemplate> giftTemplateMap = getResHolder().getMap(ContinuesGiftTemplate.class);
        final Map<Integer, ChargeGoodsTemplate> chargeGoodsTemplateMap = getResHolder().getMap(ChargeGoodsTemplate.class);
        final Map<Integer, ItemTemplate> itemTemplateMap = getResHolder().getMap(ItemTemplate.class);
        for (ActivityTemplate activityTemplate : getResHolder().getListFromMap(ActivityTemplate.class)) {
            if (activityTemplate.getSpecUnitType() != CommonEnum.ActivityUnitType.AUT_CONTINUES_GIFT) {
                continue;
            }
            final ContinuesGiftActTemplate configTemplate = configTemplateMap.get(activityTemplate.getId());
            if (configTemplate == null) {
                throw new ResourceException("连锁礼包，continue_gift_act 缺少配置 活动id {}", activityTemplate.getId());
            }
            if (!mailTemplateMap.containsKey(configTemplate.getMailId())) {
                throw new ResourceException("连锁礼包，continue_gift_act 邮件id不存在 {} {}", activityTemplate.getId(), configTemplate.getMailId());
            }
            if (!mailTemplateMap.containsKey(configTemplate.getExpireMailId())) {
                throw new ResourceException("连锁礼包，continue_gift_act 过期邮件id不存在 {} {}", activityTemplate.getId(), configTemplate.getExpireMailId());
            }
            final int discountItemId = configTemplate.getDiscountItemId();
            if (discountItemId > 0) {
                if (!itemTemplateMap.containsKey(discountItemId)) {
                    throw new ResourceException("连锁礼包，continue_gift_act {} 配置的折扣道具不存在 {}", activityTemplate.getId(), discountItemId);
                }
            }
            for (int gift : configTemplate.getGiftListList()) {
                final ContinuesGiftTemplate giftTemplate = giftTemplateMap.get(gift);
                if (giftTemplate == null) {
                    throw new ResourceException("连锁礼包，continue_gift_act 活动id {} 包含了不存在的gift {}", activityTemplate.getId(), gift);
                }
                if (giftTemplate.getIsFree()) {
                    continue;
                }
                if (giftTemplate.getGoodsId() <= 0) {
                    throw new ResourceException("连锁礼包，continue_gift gift {} without goodsId", gift);
                }
                if (!chargeGoodsTemplateMap.containsKey(giftTemplate.getGoodsId())) {
                    throw new ResourceException("连锁礼包，continue_gift gift {} 配置的goodId不存在", gift);
                }
                if (!chargeGoodsTemplateMap.containsKey(giftTemplate.getDiscountGoodsId())) {
                    throw new ResourceException("连锁礼包，continue_gift gift {} 配置的discount goodId不存在", gift);
                }
                for (IntPairType itemPair : giftTemplate.getItemListPairList()) {
                    if (itemPair.getValue() <= 0) {
                        throw new ResourceException("连锁礼包，continue_gift gift {} 配置的道具数量不合法 {}", gift, itemPair);
                    }
                    if (!itemTemplateMap.containsKey(itemPair.getKey())) {
                        throw new ResourceException("连锁礼包，continue_gift gift {} 配置的道具不存在 {}", gift, itemPair);
                    }
                }
            }
        }
    }
}

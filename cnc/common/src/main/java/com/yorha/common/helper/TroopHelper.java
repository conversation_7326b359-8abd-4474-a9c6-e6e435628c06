package com.yorha.common.helper;

import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import res.template.TroopTemplate;

public class TroopHelper {

    /**
     * 需要使用部队增益功能的entity，属性上需要设置troopId
     *
     * @param troopId
     * @return
     */
    public static StructPlayer.Troop getTroopBuilder(ResHolder resHolder, int troopId) {
        StructPlayer.Troop.Builder builder = StructPlayer.Troop.newBuilder();
        TroopTemplate template = resHolder.getValueFromMap(TroopTemplate.class, troopId);
        // 保底，设置id
        builder.setTroopId(troopId);
        // 设置士兵
        for (IntPairType intPair : template.getSoldierPairList()) {
            builder.getTroopBuilder().putDatas(intPair.getKey(), Struct.Soldier.newBuilder().setSoldierId(intPair.getKey()).setNum(intPair.getValue()).build());
        }
        // 设置主将
        if (template.getMainHeroId() != 0) {
            builder.getMainHeroBuilder().setHeroId(template.getMainHeroId()).setLevel(template.getMainHeroLevel()).setStar(template.getMainHeroStar());
            buildTroopHeroSkillBuilder(resHolder, builder.getMainHeroBuilder());
        }
        // 设置副将
        if (template.getDeputyHeroId() != 0) {
            builder.getDeputyHeroBuilder().setHeroId(template.getDeputyHeroId()).setLevel(template.getDeputyHeroLevel()).setStar(template.getDeputyHeroStar());
            buildTroopHeroSkillBuilder(resHolder, builder.getDeputyHeroBuilder());
        }
        return builder.build();
    }

    /**
     * 构建英雄技能数据
     */
    public static void buildTroopHeroSkillBuilder(ResHolder resHolder, Struct.Hero.Builder heroBuilder) throws GeminiException {
      //  HeroTemplate heroTemplate = resHolder.getValueFromMap(HeroTemplate.class, heroBuilder.getHeroId());
//         Struct.SimpleSkill.Builder skillIdsBuilder = heroBuilder.getSkills();
//        if (heroTemplate != null) {
//            //主动技能
//            int activeSkill = heroTemplate.getActiveSkill();
//            if (activeSkill > 0) {
//                skillIdsBuilder.addDatas(activeSkill);
//            }
//            if (heroTemplate.getPassiveSkillList().size() > 0) {
//                skillIdsBuilder.addAllDatas(heroTemplate.getPassiveSkillList());
//            }
//            //强化技能
//            if (heroTemplate.getIntensiveSkill() > 0) {
//                skillIdsBuilder.addDatas(heroTemplate.getIntensiveSkill());
//            }
//        }
    }

    public static boolean isUpdatePowerByRecover(Integer soldierNumChangeReason) {
        int seriousRecoveredId = SoldierNumChangeReason.serious_recovered.getId();
        int slightRecoveredId = SoldierNumChangeReason.slight_injury_recovered.getId();
        if (soldierNumChangeReason == null) {
            return false;
        }
        if (soldierNumChangeReason == seriousRecoveredId) {
            return true;
        }
        if (soldierNumChangeReason == slightRecoveredId) {
            return true;
        }
        return false;
    }
}

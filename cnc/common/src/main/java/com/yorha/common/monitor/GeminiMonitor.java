package com.yorha.common.monitor;

import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;

public class GeminiMonitor {
    public static Counter newCounter(String counterName, String... labelName) {
        int len = labelName.length;
        if (len == 0) {
            return Counter.build(counterName, "help").labelNames("busId").register();
        } else {
            String[] l = new String[len + 1];
            l[0] = "busId";
            System.arraycopy(labelName, 0, l, 1, len);
            return Counter.build(counterName, "help").labelNames(l).register();
        }
    }

    public static Gauge newGauge(String gaugeName, String... labelName) {
        int len = labelName.length;
        if (len == 0) {
            return Gauge.build(gaugeName, "help").labelNames("busId").register();
        } else {
            String[] l = new String[len + 1];
            l[0] = "busId";
            System.arraycopy(labelName, 0, l, 1, len);
            return Gauge.build(gaugeName, "help").labelNames(l).register();
        }
    }
}

package com.yorha.common.utils;

import com.google.common.collect.Maps;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public class DriveTrafficUtils {
    private static final Logger LOGGER = LogManager.getLogger(DriveTrafficUtils.class);

    private DriveTrafficUtils() {
    }

    /**
     * 读取配置中机型配置0档对应默认导量机型配置(避免未配置，使用非最高档)
     */
    private static volatile int defaultLevel = 2;

    private static volatile Map<Integer, Integer> hardwareLevelDriveTrafficMapping = Maps.newHashMap();

    private static final ReentrantLock LOCK = new ReentrantLock(true);

    /**
     * 根据客户端上报硬件机型，转换为导量档位(设立各兜底保护)
     *
     * @param hardWareLevel 客户端上报硬件机型
     * @return 导量档位
     */
    public static int getTrafficLevel(int hardWareLevel) {
        if (!hardwareLevelDriveTrafficMapping.containsKey(hardWareLevel)) {
            WechatLog.error("atTraffic DriveTrafficHelper getTrafficLevel unknown hardWareLevel={}, useDefault level={}, cur mapping={}",
                    hardWareLevel, defaultLevel, hardwareLevelDriveTrafficMapping);
            return defaultLevel;
        }
        return hardwareLevelDriveTrafficMapping.get(hardWareLevel);
    }

    /**
     * 获取硬件机型对应导量档位字符串（QLOG用）
     *
     * @param hardWareLevel 客户端上报硬件机型
     * @return 导量档位String
     */
    public static String getTrafficLevelString(int hardWareLevel) {
        int trafficLevel = getTrafficLevel(hardWareLevel);
        switch (trafficLevel) {
            case 1:
                return "high";
            case 2:
                return "low";
            default:
                // 与其它区分
                LOGGER.warn("getTrafficLevelString unimplemented String for trafficLevel={}", trafficLevel);
                return "low_";
        }
    }

    /**
     * 刷新etcd数据
     */
    public static void refreshEtcdData() {
        LOCK.lock();
        try {
            final Map<Integer, Integer> newHardwareLevelDriveTrafficMapping = ClusterConfigUtils.getWorldConfig().getIntegerMapItem("hardware_level_drive_traffic_mapping");
            // 无变化，跳过
            if (hardwareLevelDriveTrafficMapping.equals(newHardwareLevelDriveTrafficMapping)) {
                LOGGER.info("DriveTrafficUtils refrechEtcdData new hardwareLevelDriveTrafficMapping={}, equals old one, skip", newHardwareLevelDriveTrafficMapping);
                return;
            }

            LOGGER.info("DriveTrafficUtils refrechEtcdData new hardwareLevelDriveTrafficMapping={}", newHardwareLevelDriveTrafficMapping);
            if (newHardwareLevelDriveTrafficMapping == null) {
                defaultLevel = 2;
                // etcd配置错误(未配置 or 配置非int map格式)，告警
                WechatLog.error("DriveTrafficHelper refreshEtcdData no valid world config for hardware_level_drive_traffic_mapping");
            } else {
                hardwareLevelDriveTrafficMapping = Collections.unmodifiableMap(new HashMap<>(newHardwareLevelDriveTrafficMapping));
                if (!hardwareLevelDriveTrafficMapping.containsKey(0)) {
                    defaultLevel = 2;
                    // etcd未配置默认档位
                    WechatLog.error("DriveTrafficHelper refreshEtcdData no default driveTrafficLevel in hardware_level_drive_traffic_mapping");
                } else {
                    defaultLevel = hardwareLevelDriveTrafficMapping.get(0);
                }
            }
        } finally {
            LOCK.unlock();
        }
    }
}

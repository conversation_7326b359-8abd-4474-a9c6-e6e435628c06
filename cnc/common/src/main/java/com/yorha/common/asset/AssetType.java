package com.yorha.common.asset;

import com.yorha.game.gen.prop.YoAssetDescProp;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;

public enum AssetType {

    /**
     * 道具
     */
    ITEM(1) {
        @Override
        AssetDesc fromProp(YoAssetDescProp prop) {
            return new ItemDesc(prop.getId(), prop.getAmount());
        }

        @Override
        AssetDesc fromProto(Struct.YoAssetDesc proto) {
            return new ItemDesc(proto.getId(), proto.getAmount());
        }
    },

    /**
     * 英文叫货币，其实是资源
     */
    CURRENCY(2) {
        private CurrencyDesc build(int currencyNumber, long amount) {
            CommonEnum.CurrencyType type = CommonEnum.CurrencyType.forNumber(currencyNumber);
            if (type == null) {
                throw new GeminiException("Currency type not recognized. {}", currencyNumber);
            }
            return new CurrencyDesc(type, amount);
        }

        @Override
        AssetDesc fromProp(YoAssetDescProp prop) {
            return build(prop.getId(), prop.getAmount());
        }

        @Override
        AssetDesc fromProto(Struct.YoAssetDesc proto) {
            return build(proto.getId(), proto.getAmount());
        }
    },
    ;

    private final int index;

    AssetType(int index) {
        this.index = index;
    }

    public int getIndex() {
        return index;
    }

    public static AssetType fromIndex(int index) {
        for (AssetType v : values()) {
            if (v.index == index) {
                return v;
            }
        }
        return null;
    }

    abstract AssetDesc fromProp(YoAssetDescProp prop);

    abstract AssetDesc fromProto(Struct.YoAssetDesc proto);
}

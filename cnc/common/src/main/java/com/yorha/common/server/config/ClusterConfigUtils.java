package com.yorha.common.server.config;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 集群配置工具类。
 *
 * <AUTHOR>
 */
public class ClusterConfigUtils {
    private static final ReentrantLock LOCK = new ReentrantLock(true);

    private static final ConfigObj CLUSTER = new ConfigObj();
    private static volatile Map<Integer, ConfigObj> zones = new HashMap<>();

    /**
     * 热更新服务器配置
     *
     * @param reason  热更新原因。
     * @param content 配置内容
     * @throws IllegalArgumentException 错误
     */
    public static void refreshCluster(String reason, String content) throws IllegalArgumentException {
        ClusterConfigUtils.LOCK.lock();
        try {
            ClusterConfigUtils.CLUSTER.refresh(reason, content);
        } finally {
            ClusterConfigUtils.LOCK.unlock();
        }
    }

    /**
     * 热更新Zone配置。
     *
     * @param reason  热更新原因。
     * @param zoneId  热更新zone的配置。
     * @param content 配置内容。
     * @throws IllegalArgumentException 错误。
     */
    public static void refreshZone(String reason, int zoneId, String content) throws IllegalArgumentException {
        ClusterConfigUtils.LOCK.lock();
        try {
            Map<Integer, ConfigObj> tmp = ClusterConfigUtils.zones;
            if (!tmp.containsKey(zoneId)) {
                tmp = new HashMap<>(tmp);
                tmp.put(zoneId, new ConfigObj());
            }
            tmp.get(zoneId).refresh(reason, content);
            ClusterConfigUtils.zones = tmp;
        } finally {
            ClusterConfigUtils.LOCK.unlock();
        }
    }

    /**
     * 获取区服配置
     *
     * @param zoneId 区服id
     * @return 配置访问器
     */
    public static ConfigObj getZoneConfig(final int zoneId) {
        final ConfigObj configObj = ClusterConfigUtils.zones.get(zoneId);
        if (configObj == null) {
            throw new NullPointerException("zoneId " + zoneId + " config not exist!");
        }
        return configObj;
    }


    /**
     * 根据zoneId获得对应zone etcd配置(可能为空)
     *
     * @param zoneId zoneId
     * @return etcd配置
     */
    @Nullable
    public static ConfigObj getZoneConfigOrNull(int zoneId) {
        ConfigObj zoneConfig;
        try {
            zoneConfig = ClusterConfigUtils.getZoneConfig(zoneId);
        } catch (NullPointerException e) {
            return null;
        }
        return zoneConfig;
    }

    public static Collection<ConfigObj> getAllZoneConfig() {
        return ClusterConfigUtils.zones.values();
    }

    /**
     * 获取区服所有的world所有的区服id列表。
     *
     * @return world相关所有区服相关列表。
     */
    public static List<Integer> getZoneIdList() {
        return ClusterConfigUtils.zones.keySet().stream().sorted().collect(Collectors.toList());
    }

    /**
     * @return world下所有区服的配置
     */
    public static Map<Integer, ConfigObj> getZoneConfigs() {
        return Collections.unmodifiableMap(ClusterConfigUtils.zones);
    }

    /**
     * 获取服务器集群的全局配置。
     *
     * @return 全局配置访问器。
     */
    public static ConfigObj getWorldConfig() {
        return ClusterConfigUtils.CLUSTER;
    }
}

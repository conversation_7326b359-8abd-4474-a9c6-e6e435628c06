package com.yorha.common.actor.msg;

import com.yorha.common.actor.IActor;

/**
 * Actor Runnable的接口。
 *
 * <AUTHOR>
 */
public interface IActorRunnable<T extends IActor> extends IActorMsg {
    /**
     * 根据Actor跑起来。
     *
     * @param actor actor。
     */
    void run(T actor);

    /**
     * 默认runnable不能远程。
     *
     * @return false。
     */
    @Override
    default boolean canRemote() {
        return false;
    }
}

package com.yorha.common.utils;

import com.google.gson.JsonObject;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.ThirdPartyErrLogLimiter;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpResponseException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.pool.PoolStats;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * HttpAsyncClient封装（异步http请求）
 *
 * <AUTHOR>
 */
public class PlatformAsyncClient {
    private static final Logger LOGGER = LogManager.getLogger(PlatformAsyncClient.class);
    /**
     * 建立连接的超时时间
     */
    private final int connectTimeout;
    /**
     * socket读数据的超时时间
     */
    private final int socketTimeout;
    /**
     * 从连接池获取连接的超时时间
     */
    private final int connectionRequestTimeout;
    /**
     * 异步httpClient
     */
    private final CloseableHttpAsyncClient defaultClient;

    private PoolingNHttpClientConnectionManager clientConnectionManager;

    public PlatformAsyncClient(final int socketTimeout, final int workerNum) {
        this(socketTimeout, 2000, 1000, workerNum);
    }

    public PlatformAsyncClient(final int socketTimeout, final int connectTimeout, final int connectionRequestTimeout, final int workerNum) {
        this(connectTimeout, socketTimeout, connectionRequestTimeout, workerNum, 10000, 2000);
    }

    public PlatformAsyncClient(
            final int socketTimeout,
            final int connectTimeout,
            final int connectionRequestTimeout,
            final int workerNum,
            final int maxConnection,
            final int maxConnectionPerRoute
    ) {
        checkParam(connectTimeout, socketTimeout, connectionRequestTimeout, workerNum, maxConnection, maxConnectionPerRoute);
        this.connectTimeout = connectTimeout;
        this.socketTimeout = socketTimeout;
        this.connectionRequestTimeout = connectionRequestTimeout;
        this.defaultClient = buildAsyncClient(workerNum, maxConnection, maxConnectionPerRoute);
        this.defaultClient.start();
    }

    /**
     * 参数校验
     */
    void checkParam(
            final int connectTimeout,
            final int socketTimeout,
            final int connectionRequestTimeout,
            final int workerNum,
            final int maxConnection,
            final int maxConnectionPerRoute
    ) {
        if (connectTimeout <= 0) {
            throw new IllegalArgumentException("PlatformAsyncClient connectTimeout should be positive");
        }
        if (socketTimeout <= 0) {
            throw new IllegalArgumentException("PlatformAsyncClient socketTimeout should be positive");
        }
        if (connectionRequestTimeout <= 0) {
            throw new IllegalArgumentException("PlatformAsyncClient connectionRequestTimeout should be positive");
        }
        if (workerNum <= 0) {
            throw new IllegalArgumentException("PlatformAsyncClient workerNum should be positive");
        }
        if (maxConnection <= 0) {
            throw new IllegalArgumentException("PlatformAsyncClient maxConnection should be positive");
        }
        if (maxConnectionPerRoute <= 0) {
            throw new IllegalArgumentException("PlatformAsyncClient maxConnectionPerRoute should be positive");
        }
    }

    /**
     * 构建post请求
     *
     * @param url     url
     * @param content 内容
     * @param headers 头部
     * @return HttpPost
     */
    public HttpPost formPostRequest(String url, String content, Map<String, String> headers) {
        HttpPost httpPost = new HttpPost(url);
        StringEntity postEntity = new StringEntity(content, "UTF-8");

        headers.forEach(httpPost::addHeader);
        httpPost.setEntity(postEntity);
        httpPost.setConfig(commonConfig());

        return httpPost;
    }

    public HttpPost formPostRequest(String url, Map<String, Object> content, Map<String, String> headers) {
        return formPostRequest(url, JsonUtils.toJsonString(content), headers);
    }

    /**
     * 执行http请求 结果转化为json处理
     *
     * @param request           http请求
     * @param jsonConsumer      请求成功consumer
     * @param exceptionConsumer 异常consumer（成功回调中抛异常&http请求失败都会触发）
     */
    public void execute2json(HttpRequestBase request, Consumer<JsonObject> jsonConsumer, @Nullable Consumer<Exception> exceptionConsumer) {
        execute0(request, JsonUtils::parseObject, jsonConsumer, exceptionConsumer);
    }

    /**
     * 执行http请求 结果转化为string处理
     *
     * @param request           http请求
     * @param stringConsumer    请求成功consumer
     * @param exceptionConsumer 异常consumer（成功回调中抛异常&http请求失败都会触发）
     */
    public void execute(HttpRequestBase request, Consumer<String> stringConsumer, @Nullable Consumer<Exception> exceptionConsumer) {
        execute0(request, str -> str, stringConsumer, exceptionConsumer);
    }

    /**
     * 执行http请求 底层实现
     *
     * @param request           http请求
     * @param respTransfer      结果转化
     * @param respHandler       正确结果处理
     * @param exceptionConsumer 异常处理
     */
    private <T> void execute0(HttpRequestBase request, Function<String, T> respTransfer, Consumer<T> respHandler, @Nullable Consumer<Exception> exceptionConsumer) {
        final ResponseHandler<String> responseHandler = response -> {
            int status = response.getStatusLine().getStatusCode();
            // http code校验
            if (status >= 200 && status < 300) {
                HttpEntity entity = response.getEntity();
                return entity != null ? EntityUtils.toString(entity, Consts.UTF_8) : null;
            } else {
                ThirdPartyErrLogLimiter.HTTP_FAIL.tryError("PlatformAsyncClient http query failed, url={}, Unexpected response status={}", request.getURI(), status);
                throw new HttpResponseException(status, "Unexpected response");
            }
        };
        LOGGER.info("PlatformAsyncClient execute req={} headers={}", request, request.getAllHeaders());
        final long start = System.currentTimeMillis();

        this.defaultClient.execute(request, new FutureCallback<HttpResponse>() {
            @Override
            public void completed(HttpResponse response) {
                final String res;
                try {
                    res = responseHandler.handleResponse(response);
                } catch (Exception e) {
                    // 回调出错，打error
                    LOGGER.error(e.getMessage(), e);
                    // 正常处理回包抛异常(ex: http码非2xx时)，确保exceptionConsumer能处理异常
                    this.failed(e);
                    return;
                }
                LOGGER.info("PlatformAsyncClient http receive delay={} ms, entity={}", (System.currentTimeMillis() - start), res);
                try {
                    respHandler.accept(respTransfer.apply(res));
                } catch (Exception e) {
                    // 回调出错，打error
                    LOGGER.error(e.getMessage(), e);
                    this.failed(e);
                }

            }

            @Override
            public void failed(Exception ex) {
                // 通用异常收束接口，可能是SocketTimeoutException之类网络异常
                LOGGER.warn("PlatformAsyncClient request={} failed, exception={}", request, ex);
                if (exceptionConsumer == null) {
                    return;
                }
                exceptionConsumer.accept(ex);
            }

            @Override
            public void cancelled() {
                this.failed(new GeminiException("http request={} cancelled", request));
            }
        });
    }

    /**
     * 生成http请求设置
     *
     * @return RequestConfig
     */
    public RequestConfig commonConfig() {
        // 设置建立连接超时时间
        // 设置读数据超时时间
        return RequestConfig.custom()
                .setConnectionRequestTimeout(connectionRequestTimeout)   // 从连接池获取连接的超时时间
                .setSocketTimeout(socketTimeout)              // socket读数据的超时时间
                .setConnectTimeout(connectTimeout)             // 建立连接的超时时间
                .build();
    }

    /**
     * 清理连接
     *
     * @param idleLimitSec 最大idle时间
     */
    public void clearConnection(final int idleLimitSec) {
        this.clientConnectionManager.closeExpiredConnections();
        this.clientConnectionManager.closeIdleConnections(idleLimitSec, TimeUnit.SECONDS);
        LOGGER.info("PlatformAsyncClient clearConnection idleLimitSec={}", idleLimitSec);
    }

    public void destroy() {
        try {
            this.defaultClient.close();
        } catch (IOException e) {
            LOGGER.error("PlatformAsyncClient destroy, ", e);
        }
    }

    public PoolStats getPoolStats() {
        return this.clientConnectionManager.getTotalStats();
    }

    /**
     * 构建异步client
     *
     * @param workerNum             工作io线程数
     * @param maxConnection         最大连接数
     * @param maxConnectionPerRoute 每个路由的最大连接数
     * @return CloseableHttpAsyncClient
     */
    CloseableHttpAsyncClient buildAsyncClient(final int workerNum, final int maxConnection, final int maxConnectionPerRoute) {
        try {
            IOReactorConfig ioReactorConfig = IOReactorConfig.custom()
                    .setIoThreadCount(workerNum)    // 工作io线程数量
                    .setConnectTimeout(this.connectTimeout)     // 连接超时时间
                    .setSoTimeout(this.socketTimeout)          // socket超时时间
                    .setTcpNoDelay(true)
                    .build();
            ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor(ioReactorConfig);
            PoolingNHttpClientConnectionManager clientConnectionManager = new PoolingNHttpClientConnectionManager(ioReactor);
            clientConnectionManager.setMaxTotal(maxConnection);
            clientConnectionManager.setDefaultMaxPerRoute(maxConnectionPerRoute);
            this.clientConnectionManager = clientConnectionManager;

            return HttpAsyncClients.custom()
                    .setConnectionManager(this.clientConnectionManager)
                    .build();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return HttpAsyncClients.createDefault();
    }


}

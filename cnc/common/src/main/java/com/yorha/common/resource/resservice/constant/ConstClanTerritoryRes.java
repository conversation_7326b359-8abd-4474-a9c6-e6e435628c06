package com.yorha.common.resource.resservice.constant;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import res.template.ConstClanTerritoryTemplate;

/**
 * <AUTHOR>
 * <p>
 * 联盟领地常量管理
 */
public class ConstClanTerritoryRes extends AbstractResService {

    public ConstClanTerritoryRes(ResHolder resHolder) {
        super(resHolder);
    }

    public ConstClanTerritoryTemplate getTemplate() {
        return getResHolder().getConstTemplate(ConstClanTerritoryTemplate.class);
    }

    @Override
    public void load() throws ResourceException {
    }

    @Override
    public void checkValid() throws ResourceException {
    }
}

package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.InsertResult;

public class TcaplusInsert<T extends Message.Builder> extends TcaplusOperation<T, InsertOption, InsertResult<T>> {
    public TcaplusInsert(Client client, T t, InsertOption insertOption) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, insertOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_INSERT_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        request.setResultFlag(this.getOption().getResultFlag());
        Record record = request.addRecord();
        setRequestKeys(getReq(), record);
        setRequestValues(getReq(), record);
    }

    @Override
    protected InsertResult<T> buildResult(Response response) {
        InsertResult<T> result = new InsertResult<>();
        result.code = TcaplusErrorCode.forNumber(response.getResult());
        result.value = buildDefaultValue(getReq());
        Record record;
        // Tcaplus SDK 所有proxy宕机保护
        try {
            record = response.fetchRecord();
        } catch (NullPointerException e) {
            record = null;
        }
        if (record != null) {
            result.version = record.getVersion();
            readFromResponseValues(record, result.value);
        }
        return result;
    }
}

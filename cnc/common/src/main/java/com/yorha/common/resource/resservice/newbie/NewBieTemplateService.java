package com.yorha.common.resource.resservice.newbie;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.proto.CommonEnum;
import res.template.NewbieCheckTemplate;
import res.template.NewbieTemplate;
import res.template.SoldierTypeTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NewBieTemplateService extends AbstractResService {

    private Map<Integer, Map<Integer, NewbieTemplate>> template = new HashMap<>();

    private Map<CommonEnum.NewbieCheckType, List<NewbieCheckTemplate>> checkMap = new HashMap<>();


    public NewBieTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (NewbieTemplate newbieTemplate : getResHolder().getListFromMap(NewbieTemplate.class)) {
            Map<Integer, NewbieTemplate> templateMap = template.computeIfAbsent(newbieTemplate.getGroupId(), (key) -> new HashMap<>());
            templateMap.put(newbieTemplate.getStep(), newbieTemplate);
        }

        for (NewbieCheckTemplate newbieCheckTemplate : getResHolder().getListFromMap(NewbieCheckTemplate.class)) {
            if (newbieCheckTemplate.getNeedCheck() <= 0) {
                continue;
            }
            List<NewbieCheckTemplate> newbieCheckList = checkMap.computeIfAbsent(newbieCheckTemplate.getType(), (key) -> new ArrayList<>());
            newbieCheckList.add(newbieCheckTemplate);
        }
    }

    public List<NewbieCheckTemplate> getNewbieCheckByType(CommonEnum.NewbieCheckType type) {
        return checkMap.getOrDefault(type, new ArrayList<>());
    }

    public List<NewbieCheckTemplate> getAllNewbieCheck() {
        List<NewbieCheckTemplate> result = new ArrayList<>();
        for (List<NewbieCheckTemplate> value : checkMap.values()) {
            result.addAll(value);
        }
        return result;
    }

    @Override
    public void checkValid() throws ResourceException {
        for (NewbieCheckTemplate newbieCheckTemplate : getResHolder().getListFromMap(NewbieCheckTemplate.class)) {
            if (newbieCheckTemplate.getNeedCheck() <= 0) {
                continue;
            }
            switch (newbieCheckTemplate.getType()) {
                case NCT_BUILD: {
                    break;
                }
                case NCT_SOLDIER: {
                    SoldierTypeTemplate valueFromMap = getResHolder().findValueFromMap(SoldierTypeTemplate.class, newbieCheckTemplate.getTypeId());
                    if (valueFromMap == null) {
                        throw new ResourceException("新手校验表配置错误，不存在的兵种类型:{}", newbieCheckTemplate.getTypeId());
                    }
                    if (newbieCheckTemplate.getValue() <= 0) {
                        throw new ResourceException("新手校验表配置错误，士兵数量小于0:{}", newbieCheckTemplate.getValue());
                    }
                    break;
                }
                case NCT_RESOURCE: {
                    CommonEnum.CurrencyType currencyType = CommonEnum.CurrencyType.forNumber(newbieCheckTemplate.getTypeId());
                    if (currencyType == null) {
                        throw new ResourceException("新手校验表配置错误，不存在的资源类型:{}", newbieCheckTemplate.getTypeId());
                    }
                    if (newbieCheckTemplate.getValue() <= 0) {
                        throw new ResourceException("新手校验表配置错误，资源数量小于0:{}", newbieCheckTemplate.getValue());
                    }
                    break;
                }
                default:
                    throw new ResourceException("新手校验表配置错误，不支持的校验类型:{}", newbieCheckTemplate.getType());
            }
        }
    }

    public boolean hasTemplate(int groupId, int stage) {
        if (!template.containsKey(groupId)) {
            return false;
        }
        Map<Integer, NewbieTemplate> templateMap = template.get(groupId);
        return templateMap.containsKey(stage);
    }
}

package com.yorha.common.utils;

import com.github.luben.zstd.Zstd;
import com.yorha.common.server.ServerContext;

import java.io.IOException;

public class CompressUtil {

    public static byte[] zstdCompress(String reason, byte[] inputByteArray) throws IOException {
        if (inputByteArray == null) {
            return null;
        }
        ServerContext.getProcessPerfLogger().logCompress(reason);
        return Zstd.compress(inputByteArray);
    }

    public static byte[] zstdUncompress(String reason, byte[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        ServerContext.getProcessPerfLogger().logUnCompress(reason);
        int afterSize = (int) Zstd.decompressedSize(array);
        byte[] ret = new byte[afterSize];
        Zstd.decompress(ret, array);
        return ret;
    }

}

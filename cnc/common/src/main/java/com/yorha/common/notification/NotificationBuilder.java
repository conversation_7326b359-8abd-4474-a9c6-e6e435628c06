package com.yorha.common.notification;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.pushNotification.PushNotificationResService;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

/**
 * 推送管理
 *
 * <AUTHOR>
 */

public enum NotificationBuilder implements INotificationBuilder {

    /**
     * 基地受到攻击
     */
    BASE_BE_ATTACK(1002001),
    /**
     * 基地受到侦察
     */
    BASE_BE_DETECT(1002002),
    /**
     * 基地受到集结
     */
    BASE_BE_RALLY(1002003),
    /**
     * 收到新私聊消息
     */
    NEW_PRIVATE_MESSAGE(1011001),
    /**
     * 收到新私聊邮件
     */
    NEW_PRIVATE_MAIL(1003001),
    /**
     * 黄金宝箱
     */
    GIVE_GOLD_BOX(1015001),
    /**
     * 联盟集结野怪
     */
    RALLY_MONSTER(1010001),
    /**
     * 联盟集结玩家
     */
    RALLY_PLAYER(1010002),
    /**
     * 联盟邮件
     */
    NEW_CLAN_MAIL(1004001),
    /**
     * 联盟建筑被集结(主基地、指挥中心、堡垒)
     */
    CLAN_BUILDING_BE_RALLIED(1013001),
    /**
     * 联盟城市被集结(1-4级城＋王城)
     */
    CLAN_CITY_BE_RALLIED(1013002),
    /**
     * 联盟关隘被集结
     */
    CLAN_PASS_BE_RALLIED(1013003);


    private static final Map<Integer, NotificationBuilder> NOTIFICATION_BUILDER_MAP = new HashMap<>();

    static {
        for (NotificationBuilder notificationBuilder : NotificationBuilder.values()) {
            Integer id = notificationBuilder.getNotificationId();
            if (NOTIFICATION_BUILDER_MAP.containsKey(id)) {
                throw new GeminiException("NotificationBuilder repeat id={}", id);
            }
            NOTIFICATION_BUILDER_MAP.put(id, notificationBuilder);
        }
    }

    /**
     * 通知项Id
     */
    private final int notificationId;


    NotificationBuilder(int notificationId) {
        this.notificationId = notificationId;
    }

    @Override
    public boolean isTopic() {
        // josefren: topic推送暂未接入，预留
        return false;
    }

    @Override
    @Nullable
    public Notification getNotification() {
        return ResHolder.getResService(PushNotificationResService.class).getNotification(this.notificationId);
    }

    public int getNotificationId() {
        return this.notificationId;
    }

    @Nullable
    public static NotificationBuilder getNotificationBuilder(final int notificationId) {
        return NOTIFICATION_BUILDER_MAP.getOrDefault(notificationId, null);
    }
}

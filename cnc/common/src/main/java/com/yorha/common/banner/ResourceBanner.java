
package com.yorha.common.banner;

import com.yorha.common.banner.ansi.AnsiColor;
import com.yorha.common.banner.ansi.AnsiOutput;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class ResourceBanner implements Banner {
    private static final Logger LOGGER = LogManager.getLogger(ResourceBanner.class);
    private final String template;
    private static final String ANSI_COLOR_CLASS_NAME = AnsiColor.class.getSimpleName();
    /**
     * 用于匹配 ${变量名} 格式占位符的正则表达式
     */
    private static final String PLACEHOLDER_PATTERN = "\\$\\{([^}]*)\\}";
    /**
     * 预编译的占位符匹配模式，避免重复编译提高性能
     */
    private static final Pattern PLACEHOLDER_COMPILED_PATTERN = Pattern.compile(PLACEHOLDER_PATTERN);
    private Map<String, String> store = new HashMap<>();

    public ResourceBanner(String template) {
        this.template = template;
        AnsiOutput.setEnabled(AnsiOutput.Enabled.ALWAYS);
    }

    @Override
    public void printBanner(String id, String type, Class<?> mainClass) {
        store = this.buildTemplate(id, type, mainClass);
        String result = this.resolve();
        LOGGER.info(StringUtils.LF + "{}", result);
    }

    public String resolve() {
        StringBuilder result = new StringBuilder();
        Matcher matcher = PLACEHOLDER_COMPILED_PATTERN.matcher(template);

        while (matcher.find()) {
            String placeholder = matcher.group(1);
            String replacement;

            if (placeholder.startsWith(ANSI_COLOR_CLASS_NAME)) {
                // 使用 handleRender 处理颜色
                replacement = this.handleRender(placeholder);
            } else {
                // 使用 map.get 获取变量值
                replacement = store.getOrDefault(placeholder, "");
            }

            matcher.appendReplacement(result, replacement != null ? replacement : "");
        }
        matcher.appendTail(result);
        return result.toString();
    }


    private Map<String, String> buildTemplate(String id, String type, Class<?> mainClass) {
        Map<String, String> ret = new HashMap<>();
        ret.put("application.id", id);
        ret.put("application.type", type);
        String version = mainClass.getPackage().getImplementationVersion();
        if (StringUtils.isEmpty(version)) {
            version = "1.0.1-05d10052-128-S20250606R1";
        }
        ret.put("application.version", version);
        ret.put("vm.core", String.valueOf(Runtime.getRuntime().availableProcessors()));
        ret.put("vm.vendor", System.getProperty("java.vendor.version"));
        ret.put("vm.version", System.getProperty("java.vm.version"));
        ret.put("vm.name", System.getProperty("java.vm.name"));
        return ret;
    }


    public String handleRender(String name) {
        if (StringUtils.hasLength(name)) {
            if (name.startsWith(ANSI_COLOR_CLASS_NAME)) {
                String postfix = name.substring(ANSI_COLOR_CLASS_NAME.length() + 1);
                AnsiColor color = AnsiColor.valueOf(postfix);
                return AnsiOutput.encode(color);
            }
        }
        return name;
    }
}


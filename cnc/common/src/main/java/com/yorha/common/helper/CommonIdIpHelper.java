package com.yorha.common.helper;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class CommonIdIpHelper {
    protected static final Logger LOGGER = LogManager.getLogger(CommonIdIpHelper.class);

    public static boolean isIdIpMail(StructMsg.IdIpMailData data) {
        if (data == null) {
            return false;
        }
        if (StringUtils.isEmpty(data.getCmdId())) {
            return false;
        }
        if (StringUtils.isEmpty(data.getSerial())) {
            return false;
        }
        if (StringUtils.isEmpty(data.getSource())) {
            return false;
        }
        return true;
    }
}

package com.yorha.common.actor.msg;

import com.yorha.common.resource.IResTemplate;

import java.util.Set;

/**
 * xml更新消息
 *
 * <AUTHOR>
 */
public class ActorReLoadResMsg implements IActorMsg {

    private final Set<Class<? extends IResTemplate>> templateClass;

    public ActorReLoadResMsg(Set<Class<? extends IResTemplate>> templateClass) {
        this.templateClass = templateClass;
    }

    public Set<Class<? extends IResTemplate>> getTemplateClass() {
        return templateClass;
    }

    @Override
    public boolean canRemote() {
        return false;
    }

    @Override
    public String toString() {
        return "ActorReLoadResMsg{" +
                "templateClass=" + templateClass +
                '}';
    }
}

package com.yorha.common.db.tcaplus;

import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.impl.TableMetadata;
import com.tencent.tcaplus.util.TCaplusUtil;
import com.yorha.common.db.DbManager;
import com.yorha.common.db.tcaplus.op.FieldMetaData;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.GetMetaOption;
import com.yorha.common.db.tcaplus.result.GetMetaResult;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * db表校验
 *
 * <AUTHOR>
 */
public final class TcaplusCheckHelper {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusCheckHelper.class);

    private TcaplusCheckHelper() {
    }

    /**
     * 拉取tcaplus中所有数据表结构，与本地比对，仅当本地为远端子集时可通过，比对所有表后结束
     */
    public static Boolean checkTcaplusTable() {
        boolean errorExists = false;
        for (Descriptors.Descriptor descriptor : TcaplusDb.getDescriptor().getMessageTypes()) {
            errorExists |= checkSingleTcaplusTable(descriptor);
        }
        if (errorExists) {
            throw new GeminiException("checkTcaplusTable fail! check Error Log");
        }
        return false;
    }

    /**
     * DB表结构与内存数据结构不匹配
     *
     * @param localDescriptor 描述器。
     * @return true=存在不匹配， false=相匹配
     */
    private static boolean checkSingleTcaplusTable(final Descriptors.Descriptor localDescriptor) {
        boolean errorExists = false;

        Message.Builder req = DynamicMessage.newBuilder(localDescriptor);
        final FieldMetaData fieldMetaData = PbFieldMetaCaches.getMetaData(req);
        LOGGER.info("checkSingleTcaplusTable check table={} start", fieldMetaData.tableName);
        // 拉取tcaplus表元数据
        GetMetaResult ans = DbManager.getInstance().getMeta(req, GetMetaOption.DEFAULT_VALUE);

        if (ans.isTableNotExist()) {
            throw new GeminiException("checkSingleTcaplusTable name={} table not exists", fieldMetaData.tableName);
        } else if (!ans.isOk()) {
            throw new GeminiException("checkSingleTcaplusTable name={} tcaplus code={} != ok", fieldMetaData.tableName, ans.code);
        }

        final TableMetadata metaData = ans.value;
        for (Descriptors.FieldDescriptor keyColumn : fieldMetaData.keyFieldsList) {
            if (!isInKeyColumn(keyColumn, metaData, fieldMetaData.tableName)) {
                errorExists = true;
            }
        }
        for (Descriptors.FieldDescriptor valColumn : fieldMetaData.valueFieldsList) {
            if (!isInValColumn(valColumn, metaData, fieldMetaData.tableName)) {
                errorExists = true;
            }
        }
        LOGGER.info("checkSingleTcaplusTable check table={} end", fieldMetaData.tableName);
        if (errorExists) {
            LOGGER.error("checkSingleTcaplusTable check table={} has error", fieldMetaData.tableName);
        }
        return errorExists;
    }

    /**
     * 字段是否在tcaoplus val中
     *
     * @param column    pb字段
     * @param metadata  tcaplus表元数据
     * @param tableName tcaplus表名
     * @return 是否有效。
     */
    private static boolean isInValColumn(final Descriptors.FieldDescriptor column, final TableMetadata metadata, final String tableName) {
        for (TableMetadata.ValueColumn valColumn : metadata.valueColumnList) {
            if (column.getName().equals(getValidName(valColumn.name))) {
                return isSameType(column, valColumn.type, tableName);
            }
        }
        LOGGER.error("isInValColumn field={} not found in db values tableName={}", column.getName(), tableName);
        return false;
    }

    /**
     * 字段是否在tcaplus key中
     *
     * @param column    pb字段
     * @param metadata  tcaplus表元数据
     * @param tableName tcaplus表名
     * @return true or false。
     */
    private static boolean isInKeyColumn(final Descriptors.FieldDescriptor column, final TableMetadata metadata, final String tableName) {
        for (TableMetadata.KeyColumn keyColumn : metadata.keyColumnList) {
            if (column.getName().equals(getValidName(keyColumn.name))) {
                return isSameType(column, keyColumn.type, tableName);
            }
        }
        LOGGER.error("isInKeyColumn field={} not found in db keys tableName={}", column.getName(), tableName);
        return false;
    }


    /**
     * 获取tcaplus表中字段正确名字（tcaplus sdk返回字符串存在错误，无法直接使用）
     *
     * @param tcaplusColumnName tcaplus返回字段名
     * @return 正确字段名字
     */
    private static String getValidName(String tcaplusColumnName) {
        return TCaplusUtil.bytesToString(tcaplusColumnName.getBytes());
    }

    /**
     * 内存pb类型与tcaplus字段类型是否相同
     *
     * @param field     内存pb类型
     * @param type      tcaplus字段类型
     * @param tableName tcaplus表明（用于输出错误日志）
     * @return true=类型相同，false=类型不同
     */
    private static boolean isSameType(final Descriptors.FieldDescriptor field, final int type, final String tableName) {
        boolean res = DbUtil.protoTypeToTdrType(field.getType()).equals(type);
        if (!res) {
            LOGGER.error("isSameType tableName={} typeName={} local and remote type differ: local={}, db={}",
                    tableName, field.getName(), field.getType().toString(), DbUtil.tdrTypeToString(type));
        }
        return res;
    }
}

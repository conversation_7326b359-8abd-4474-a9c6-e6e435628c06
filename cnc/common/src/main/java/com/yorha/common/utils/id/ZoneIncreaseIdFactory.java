package com.yorha.common.utils.id;

import com.yorha.common.db.DbManager;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.IncreaseResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 唯一id生成器，依赖于中间件做编码段划分，编码规则：
 * * +-----------------------------------+
 * |--保留字-|--Zone保留位--|---ZoneId位---|---序列号---|
 * |--4bits-|---4bits---|---13bits---|---43bits---|
 * * +-----------------------------------+
 * * 算法使用缓存段来提高性能。
 *
 * <AUTHOR>
 */
public class ZoneIncreaseIdFactory implements IIdFactory {
    private static final Logger LOGGER = LogManager.getLogger(ZoneIncreaseIdFactory.class);

    private static long encode(long reserve, long zoneReserve, long zoneId, long seqId) {
        return (reserve << ID_ENCODE_RESERVED_LEFT_MOVE_SIZE) + (zoneReserve << ID_ENCODE_ZONE_RESERVE_LEFT_MOVE_SIZE) + (zoneId << ID_ENCODE_ZONE_ID_LEFT_MOVE_SIZE) + seqId;
    }

    public static long decodeZoneId(long id) {
        return (id >> ID_ENCODE_SEQUENCE_BIT_SIZE) & ((1 << ID_ENCODE_ZONE_ID_BIT_SIZE + 1) - 1);
    }

    /**
     * zone保留位
     */
    private static final long ID_ENCODE_ZONE_RESERVE_BIT_SIZE = 4;
    /**
     * zoneId 占用bit位
     */
    private static final long ID_ENCODE_ZONE_ID_BIT_SIZE = 13;
    /**
     * 自增序号占bit位
     */
    private static final long ID_ENCODE_SEQUENCE_BIT_SIZE = 43;
    /**
     * 保留位左移位数
     */
    private static final long ID_ENCODE_RESERVED_LEFT_MOVE_SIZE = ID_ENCODE_ZONE_RESERVE_BIT_SIZE + ID_ENCODE_ZONE_ID_BIT_SIZE + ID_ENCODE_SEQUENCE_BIT_SIZE;
    /**
     * zone保留位左移位数
     */
    private static final long ID_ENCODE_ZONE_RESERVE_LEFT_MOVE_SIZE = ID_ENCODE_ZONE_ID_BIT_SIZE + ID_ENCODE_SEQUENCE_BIT_SIZE;
    /**
     * zoneId左移位数
     */
    private static final long ID_ENCODE_ZONE_ID_LEFT_MOVE_SIZE = ID_ENCODE_SEQUENCE_BIT_SIZE;
    /**
     * 序号段最大值（不允许超过）
     */
    private static final long ID_ENCODE_MAX_SEQUENCE = 1L << ID_ENCODE_SEQUENCE_BIT_SIZE;
    private static final long ID_WARNING_THRESHOLD = (long) (ID_ENCODE_MAX_SEQUENCE * 0.8);
    /**
     * 全局保留位
     */
    private final long reserve;
    /**
     * 全局保留位
     */
    private final long zoneReserve;
    /**
     * 本个发号器的zoneId
     */
    private final int zoneId;
    /**
     * 发号器对应key。
     */
    private final String key;

    public ZoneIncreaseIdFactory(long reserve, long zoneReserve, int zoneId, String key) {
        this.reserve = reserve;
        this.zoneReserve = zoneReserve;
        this.zoneId = zoneId;
        this.key = key;
    }

    /**
     * 同步返回increase结果
     *
     * @param builder 数据
     * @param option  option
     */
    protected IncreaseResult<TcaplusDb.IdFactoryTable.Builder> runIncrease(final TcaplusDb.IdFactoryTable.Builder builder, final IncreaseOption option) {
        return DbManager.getInstance().increase(builder, option);
    }

    /**
     * 同步返回insert结果
     *
     * @param builder 数据
     * @param option  option
     */
    protected InsertResult<TcaplusDb.IdFactoryTable.Builder> runInsert(final TcaplusDb.IdFactoryTable.Builder builder, final InsertOption option) {
        return DbManager.getInstance().insert(builder, option);
    }

    @Override
    public final long nextId(String reason) {
        long seqId = getNextIdByFiber();
        if (seqId == -1) {

            throw new RuntimeException(this.getClass().getSimpleName() + " seqId generate failed");
        }
        if (seqId >= ID_ENCODE_MAX_SEQUENCE) {
            throw new RuntimeException(this.getClass().getSimpleName() + " seqId is overflow");
        }
        if (seqId >= ID_WARNING_THRESHOLD) {
            LOGGER.error("{} is above 80% {} {}", this.getClass().getSimpleName(), seqId, ID_WARNING_THRESHOLD);
        }
        return encode(reserve, zoneReserve, zoneId, seqId);
    }

    private long getNextIdByFiber() {
        // 使用tcaplus进行发号
        final TcaplusDb.IdFactoryTable.Builder increaseReq = TcaplusDb.IdFactoryTable.newBuilder()
                .setZoneId(this.zoneId)
                .setKey(this.key)
                .setNextId(1);
        final IncreaseOption option = IncreaseOption.newBuilder()
                .setResultFlag(CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL)
                .build();
        IncreaseResult<TcaplusDb.IdFactoryTable.Builder> increaseResult = this.runIncrease(increaseReq, option);
        // 发号成功
        if (increaseResult.isOk()) {
            return increaseResult.value.getNextId();
        }
        // 发号失败
        if (!increaseResult.isRecordNotExist()) {
            LOGGER.error("{} increase {} fail! result {}!", this.getClass().getSimpleName(), increaseReq, increaseResult);
            return -1;
        }

        // 测试环境、开发环境，需要注意初始化的时候用更大的段，暴露问题
        final long startId;
        if (ServerContext.isDevEnv() || ServerContext.isTestEnv()) {
            startId = 1L << 32;
        } else {
            startId = 0L;
        }

        // 发号器不存在，尝试插入发号段
        final TcaplusDb.IdFactoryTable.Builder insertReq = TcaplusDb.IdFactoryTable.newBuilder()
                .setKey(this.key)
                .setZoneId(this.zoneId)
                .setNextId(startId);
        final InsertResult<TcaplusDb.IdFactoryTable.Builder> insertResult = this.runInsert(insertReq, InsertOption.DEFAULT_VALUE);

        // 发号成功
        if (insertResult.isOk()) {
            return startId;
        }
        // 刚好有人同时插入了 OK的
        if (!DbUtil.isRecordAlreadyExist(insertResult.getCode())) {
            LOGGER.error("{} insert {} fail! result {}!", this.getClass().getSimpleName(), insertReq, insertResult);
            return -1;
        }
        increaseResult = this.runIncrease(increaseReq, option);
        // 发号成功
        if (increaseResult.isOk()) {
            return increaseResult.value.getNextId();
        }
        // 发号失败
        LOGGER.error("{} increase {} fail! result {}!", this.getClass().getSimpleName(), increaseReq, increaseResult);
        return -1;
    }

}

package com.yorha.common.monitors;

import com.yorha.common.concurrent.NameableThreadFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.async.AsyncLoggerContext;
import org.apache.logging.log4j.core.jmx.RingBufferAdmin;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class LoggerService {
    private static final Logger LOGGER = LogManager.getLogger(LoggerService.class);
    private RingBufferAdmin admin;
    private static LoggerService instance;
    private static final int TIME_DELAY = 60000;
    private static final int TIME_INTERVAL = 60000;

    private final ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1, new NameableThreadFactory("log4j2 Monitor"));


    public static LoggerService getInstance() {
        if (null == instance) {
            instance = new LoggerService();
        }
        return instance;
    }


    public void init() {
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        if (context instanceof AsyncLoggerContext) {
            AsyncLoggerContext asyncContext = (AsyncLoggerContext) context;
            // 1. 获取 AsyncLogger 的 RingBufferAdmin
            this.admin = asyncContext.createRingBufferAdmin();
        }
        this.init(TIME_DELAY, TIME_INTERVAL);
    }


    public void init(int delayTime, int intervalTime) {
        scheduledExecutorService.scheduleAtFixedRate(() -> {
            try {
                long bufferSize = admin.getBufferSize();
                long remainingCapacity = admin.getRemainingCapacity();
                long used = bufferSize - remainingCapacity;
                double usagePercentage = ((double) used / bufferSize) * 100;
                LOGGER.info("log4j2 ring buffer size: {}, used: {}, remaining: {}, usage: {}%",
                        bufferSize, used, remainingCapacity, String.format("%.2f", usagePercentage));
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }

        }, delayTime, intervalTime, TimeUnit.MILLISECONDS);
        LOGGER.info("log4j2 monitor thread will start in {} seconds,the monitor interval={} seconds", (delayTime / 1000), intervalTime / 1000);
    }

}

package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import com.google.protobuf.MessageOrBuilder;
import com.yorha.proto.TcaplusDb;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class PbFieldMetaCaches {

    private static final Map<Descriptors.Descriptor, FieldMetaData> CACHE_FIELD_META = new ConcurrentHashMap<>();

    static {
        // 主动加载所有数据
        for (Descriptors.Descriptor descriptor : TcaplusDb.getDescriptor().getMessageTypes()) {
            PbFieldMetaCaches.genFieldMetaData(DynamicMessage.newBuilder(descriptor));
        }

    }

    /**
     * 获取PB字段的元数据
     */
    public static FieldMetaData getMetaData(MessageOrBuilder msg) {
        return CACHE_FIELD_META.computeIfAbsent(msg.getDescriptorForType(), k -> genFieldMetaData(msg));
    }

    private static FieldMetaData genFieldMetaData(MessageOrBuilder dataBuilder) {
        FieldMetaData fieldMetaData = new FieldMetaData();
        fieldMetaData.tableName = dataBuilder.getDescriptorForType().getName();
        Descriptors.Descriptor recordDesc = dataBuilder.getDescriptorForType();
        for (Map.Entry<Descriptors.FieldDescriptor, Object> entry : dataBuilder.getDescriptorForType().getOptions().getAllFields().entrySet()) {
            if ("tcaplus_index".equals(entry.getKey().getName())) {
                String indexStr = entry.getValue().toString();
                for (String index : indexStr.split("\\|")) {
                    String indexContent = index.split(":")[1];
                    String[] indexAttributes = indexContent.split(",");
                    fieldMetaData.indexes.add(new HashSet<>(Arrays.asList(indexAttributes)));
                }
            }
            if ("tcaplus_primarykey".equals(entry.getKey().getName())) {
                String pkstr = entry.getValue().toString();
                for (String pkeystr : pkstr.split(",")) {
                    Descriptors.FieldDescriptor desp = recordDesc.findFieldByName(pkeystr);
                    fieldMetaData.keyFieldsMap.put(desp.getName(), desp);
                    fieldMetaData.keyFieldNames.add(desp.getName());
                    fieldMetaData.keyFieldsList.add(desp);
                }
            }
            if ("document_id".equals(entry.getKey().getName())) {
                String documentId = entry.getValue().toString();
                Descriptors.FieldDescriptor field = recordDesc.findFieldByName(documentId);
                fieldMetaData.documentIdField = field;
            }
        }
        List<Descriptors.FieldDescriptor> fields = recordDesc.getFields();
        for (Descriptors.FieldDescriptor entry : fields) {
            if (fieldMetaData.keyFieldsMap.get(entry.getName()) == null) {
                fieldMetaData.valueFieldsMap.put(entry.getName(), entry);
                fieldMetaData.valueFieldsList.add(entry);
            }
        }
        return fieldMetaData;
    }
}

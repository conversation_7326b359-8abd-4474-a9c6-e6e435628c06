package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.InsertSubscriber;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.proto.CommonEnum;
import org.reactivestreams.Publisher;


/**
 * <AUTHOR>
 */
public class MongoInsert<T extends Message.Builder> extends MongoOperation<T, InsertOption, InsertOneResult, InsertOneResult, InsertResult<T>> {

    public MongoInsert(MongoDatabase database, T t, InsertOption insertOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, insertOption);
    }

    @Override
    protected NormalSubscriber<InsertOneResult> getSubscriber() {
        return new InsertSubscriber<>();
    }

    @Override
    protected Publisher<InsertOneResult> getPublisher() {
        return this.database.getCollection(this.getTableName()).insertOne(DocumentHelper.formKeyAndVal(this.getReq()));

    }

    @Override
    protected InsertResult<T> buildResult(InsertOneResult response) {
        final InsertResult<T> result = new InsertResult<>();
        if (response == null) {
            result.value = MongoOperation.buildDefaultValue(this.getReq());
            result.code = TcaplusErrorCode.SVR_ERR_FAIL_RECORD_EXIST;
            return result;
        }
        if (this.getOption().getResultFlag() == CommonEnum.TcaplusResultFlag.RESULT_FLAG_RESULT_ONLY.getNumber()) {
            result.value = MongoOperation.buildDefaultValue(this.getReq());
        } else {
            result.value = this.getReq();
        }
        return result;
    }

    @Override
    protected InsertResult<T> onMongoError() {
        final InsertResult<T> result = new InsertResult<>();
        result.value = MongoOperation.buildDefaultValue(this.getReq());
        result.code = DEFAULT_ERROR_CODE;
        return result;
    }
}

package com.yorha.common.actor.cluster;

import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.BusIdUtils;

/**
 * 获取集群Key的工具方法。
 *
 * <AUTHOR>
 */
public final class ActorClusterUrlUtils {
    private static final String CLUSTER_PREFIX = "/demo/";

    /**
     * etcd:/{prefix}/{worldId}/zones/{zoneId}
     *
     * @param zoneId zoneId。
     * @return zoneId服务发现相关的URL。
     */
    public static String etcdWorldZoneFoundUrl(int zoneId) {
        return etcdWorldZoneFoundPrefix() + zoneId;
    }

    /**
     * etcd:/{prefix}/{worldId}/zones/
     *
     * @return zoneId服务发现相关的URL前缀。
     */
    public static String etcdWorldZoneFoundPrefix() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + String.format("%d/zones/", ServerContext.getWorldId());
    }

    /**
     * etcd:/{prefix}/{worldId}/kvks/
     *
     * @return kid服务发现相关的URL前缀。
     */
    public static String etcdWorldKvkFoundPrefix() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + String.format("%d/kvks/", ServerContext.getWorldId());
    }

    /**
     * etcd:/{prefix}/{worldId}/config/cluster。
     *
     * @return 集群配置url。
     */
    public static String etcdClusterConfigUrl() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + String.format("%d/config/cluster", ServerContext.getServerInfo().getWorldId());
    }

    /**
     * etcd:/{prefix}/{worldId}/config/zone/。
     *
     * @return 小服配置url前缀。
     */
    public static String etcdZoneConfigPrefix() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + String.format("%d/config/zone/", ServerContext.getServerInfo().getWorldId());
    }

    /**
     * etcd:/{prefix}/{worldId}/config/zone/zoneId。
     * 小服的配置url。
     *
     * @param zoneId 小服id。
     * @return etcdZoneConfigUr。
     */
    public static String etcdZoneConfigUrl(final int zoneId) {
        return etcdZoneConfigPrefix() + zoneId;
    }

    /**
     * 从etcdZoneConfigUrl中解析出zoneId。
     *
     * @param url etcdZoneConfigUrl
     * @return zoneId。
     */
    public static int getZoneFromEtcdZoneConfigUrl(String url) {
        final String sZoneId = url.replaceFirst(etcdZoneConfigPrefix(), "");
        return Integer.parseInt(sZoneId);
    }

    /**
     * 从服务发现etcdWorldZoneFound中解析出zoneId
     *
     * @param url etcdWorldZoneFound
     * @return zoneId
     */
    public static int getZoneFromEtcdWorldZoneFoundUrl(final String url) {
        return Integer.parseInt(url.substring(ActorClusterUrlUtils.etcdWorldZoneFoundPrefix().length()));
    }

    /**
     * 从服务发现etcdWorldZoneFound中解析出zoneId
     *
     * @param url etcdWorldZoneFound
     * @return zoneId
     */
    public static int getKvkFromEtcdWorldKvkFoundUrl(final String url) {
        return Integer.parseInt(url.substring(ActorClusterUrlUtils.etcdWorldKvkFoundPrefix().length()));
    }

    /**
     * etcd:/{prefix}/{worldId}/topology/{serverType}/{busId}
     *
     * @param busId busId。
     * @return 集群拓扑Url。
     */
    public static String etcdNodeTopologyUrl(final String busId, final String suffix) {
        final int serverTypeFromBusId = BusIdUtils.getServerTypeFromBusId(busId);
        return etcdNodeTopologyPrefix() + serverTypeFromBusId + "/" + busId + "/" + suffix;
    }

    /**
     * etcd:/{prefix}/{worldId}/topology/
     *
     * @return 集群拓扑Url前缀。
     */
    public static String etcdNodeTopologyPrefix() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + ServerContext.getServerInfo().getWorldId() + "/topology/";
    }

    /**
     * 从etcdNodeTopologyUrl重读取相应的busId。
     *
     * @param url url。
     * @return bus id。
     */
    public static String getBusIdFromEtcdNodeTopology(String url) {
        if (!url.startsWith(etcdNodeTopologyPrefix())) {
            throw new IllegalArgumentException(url);
        }
        final String tail = url.replaceFirst(etcdNodeTopologyPrefix(), "");
        final String[] items = tail.split("/");
        if (items.length != 3) {
            throw new IllegalArgumentException(url);
        }
        return items[1];
    }

    /**
     * 从etcdNodeTopologyUrl重读取相应的启服时间戳。
     *
     * @param url url。
     * @return 启服时间戳（ms）。
     */
    public static long getStartTsMsFromEtcdNodeTopology(String url) {
        if (!url.startsWith(etcdNodeTopologyPrefix())) {
            throw new IllegalArgumentException(url);
        }
        final String tail = url.replaceFirst(etcdNodeTopologyPrefix(), "");
        final String[] items = tail.split("/");
        if (items.length != 3) {
            throw new IllegalArgumentException(url);
        }
        return Long.parseLong(items[2]);
    }

    /**
     * 白名单URL。
     *
     * @return 白名单相关URL。
     */
    public static String etcdWhiteListUrl() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + ServerContext.getServerInfo().getWorldId() + "/whiteList";
    }

    /**
     * etcd:/{prefix}/{worldId}/snow_flake_worker/counter
     *
     * @return 全局雪花算法WorkerId计数器
     */
    public static String etcdGlobalWorkerIdCounterUrl() {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + String.format("%d/snow_flake_worker/counter", ServerContext.getServerInfo().getWorldId());
    }

    /**
     * etcd:/{prefix}/{worldId}/snow_flake_worker/{zoneId}
     *
     * @return 全局雪花算法WorkerId
     */
    public static String etcdGlobalWorkerIdUrl(int zoneId) {
        return ActorClusterUrlUtils.CLUSTER_PREFIX + String.format("%d/snow_flake_worker/%d", ServerContext.getServerInfo().getWorldId(), zoneId);
    }
}

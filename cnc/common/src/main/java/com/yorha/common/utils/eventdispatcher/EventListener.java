package com.yorha.common.utils.eventdispatcher;

import com.google.common.collect.Lists;
import com.yorha.common.wechatlog.WechatLog;

import java.util.List;
import java.util.function.Consumer;

/**
 * dispatcher添加监听的返回值
 * 可以对本listener调用cancel取消
 *
 * <AUTHOR>
 */
public class EventListener {
    /**
     * 参与的事件dispatcher
     */
    private final EventDispatcher eventDispatcher;
    /**
     * 唯一识别id
     */
    private final int uniqueId;
    /**
     * 监听的事件列表
     */
    private final List<String> eventNameList;
    /**
     * event触发的回调
     */
    private final Consumer<IEvent> eventHandler;

    private final boolean isRepeat;
    /**
     * 是否已经cancel，防止重复cancel
     */
    private boolean isCancel = false;


    public static EventListener valueOf(EventDispatcher eventDispatcher, int uniqueId, String eventName, Consumer<? extends IEvent> eventHandler, boolean isRepeat) {
        return new EventListener(eventDispatcher, uniqueId, Lists.newArrayList(eventName), eventHandler, isRepeat);
    }

    public static EventListener valueOf(EventDispatcher eventDispatcher, int uniqueId, List<String> eventNameList, Consumer<IEvent> eventHandler, boolean isRepeat) {
        return new EventListener(eventDispatcher, uniqueId, eventNameList, eventHandler, isRepeat);
    }

    private EventListener(EventDispatcher eventDispatcher, int uniqueId, List<String> nameList, Consumer<? extends IEvent> eventHandler, boolean isRepeat) {
        this.eventDispatcher = eventDispatcher;
        this.uniqueId = uniqueId;
        this.eventNameList = nameList;
        this.eventHandler = (Consumer<IEvent>) eventHandler;
        this.isRepeat = isRepeat;
    }

    public int getUniqueId() {
        return uniqueId;
    }

    public Consumer<IEvent> getEventHandler() {
        return eventHandler;
    }


    public List<String> getEventNameList() {
        return eventNameList;
    }


    /**
     * 是否是无参数触发方式
     */
    public boolean isRepeat() {
        return isRepeat;
    }

    public boolean isCancel() {
        return isCancel;
    }

    /**
     * 取消监听
     */
    public void cancel() {
        // 防止重复cancel
        if (isCancel) {
            WechatLog.error("repeat cancel EventListener, {}", eventNameList);
            return;
        }
        isCancel = true;
        eventDispatcher.onListenerCancel(this);
    }

}

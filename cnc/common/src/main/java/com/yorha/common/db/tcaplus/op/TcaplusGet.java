package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetResult;

import static com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants.TCAPLUS_FLAG_FETCH_ONLY_IF_MODIFIED;

public class TcaplusGet<T extends Message.Builder> extends TcaplusOperation<T, GetOption, GetResult<T>> {
    public TcaplusGet(Client client, T t, GetOption getOption) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, getOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_GET_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        // 通过addFieldName设置查询的字段
        if (getOption().isGetAllFields()) {
            for (Descriptors.FieldDescriptor e : getFieldMetaData().valueFieldsList) {
                request.addFieldName(e.getName());
                this.addRequestBytes(e.getName().length());
            }
        } else {
            if (getOption().getFieldNames() != null) {
                this.addRequestedFields(getOption().getFieldNames(), this.getReq().getDescriptorForType(), request);
            }
        }
        if (getOption().isFetchOnlyIfModified()) {
            request.setFlags(TCAPLUS_FLAG_FETCH_ONLY_IF_MODIFIED);
        }
        Record record = request.addRecord();
        configRecordProperty(record);
        setRequestKeys(getReq(), record);
    }

    private void configRecordProperty(Record record) {
        // 设置版本
        if (getOption().getVersion() >= 0) {
            record.setVersion(getOption().getVersion());
        }
    }

    @Override
    protected GetResult<T> buildResult(Response response) {
        GetResult<T> result = new GetResult<>();
        result.code = TcaplusErrorCode.forNumber(response.getResult());
        // 远端版本与本地一致，数据未变化，返回版本号
        if (getOption().isFetchOnlyIfModified() && result.code == TcaplusErrorCode.COMMON_INFO_DATA_NOT_MODIFIED) {
            result.version = getOption().getVersion();
            return result;
        }
        Record record;
        // Tcaplus SDK 所有proxy宕机保护
        try {
            record = response.fetchRecord();
        } catch (NullPointerException e) {
            record = null;
        }
        if (record != null) {
            result.version = record.getVersion();
            result.value = buildDefaultValue(getReq());
            readFromResponseValues(record, result.value);
        }
        return result;
    }
}

package com.yorha.common.server.config;

import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class ServerCfg {

    private String gameDataPath;
    private String groovyPath;

    private String natsAddr;
    private String mongoDatabase;
    private String mongoUser;
    private String mongoPassword;
    private List<String> mongoAddressList;

    public ServerCfg() {
    }

    @SuppressWarnings("unchecked")
    private void init(Map<String, Object> data) {
        gameDataPath = data.get("gameDataPath").toString();
        groovyPath = data.get("groovyPath").toString();
        natsAddr = data.get("natsAddr").toString();
        Map<String, Object> mongoData = Collections.unmodifiableMap((Map<String, Object>) data.get("mongo"));
        mongoDatabase = mongoData.get("database").toString();
        mongoUser = mongoData.get("user").toString();
        mongoPassword = mongoData.get("password").toString();
        mongoAddressList = Collections.unmodifiableList((List<String>) mongoData.get("addressList"));
    }

    public String getGameDataPath() {
        return gameDataPath;
    }

    public String getGroovyPath() {
        return groovyPath;
    }

    public String getNatsAddr() {
        return natsAddr;
    }

    public String getMongoDatabase() {
        return mongoDatabase;
    }

    public String getMongoUser() {
        return mongoUser;
    }

    public String getMongoPassword() {
        return mongoPassword;
    }

    public List<String> getMongoAddressList() {
        return mongoAddressList;
    }

    public static ServerCfg load(String path) {
        String content = null;
        try {
            content = new String(Files.readAllBytes(Paths.get(path)));
        } catch (IOException e) {
            // LOGGER.error("gemini_system : load {} fail!", path, e);
            throw new RuntimeException(e.getMessage());
        }
        Yaml yaml = new Yaml();
        Map<String, Object> data = yaml.load(content);
        ServerCfg cfg = new ServerCfg();
        cfg.init(data);
        return cfg;
    }
}

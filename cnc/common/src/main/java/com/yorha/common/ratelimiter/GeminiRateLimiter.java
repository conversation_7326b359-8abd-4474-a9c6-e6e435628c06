package com.yorha.common.ratelimiter;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.concurrent.TimeUnit;

/**
 * 单线程的！！！
 * 协议限流器，也用来记录协议信息
 * 支持配置qps，当每秒超过qps后，tryAcquire返回失败
 */
@NotThreadSafe
public class GeminiRateLimiter {
    private static final Logger LOGGER = LogManager.getLogger(GeminiRateLimiter.class);

    private int max = 0;
    private int cur = 0;
    private long lastCheckTsMs = -1;

    public static GeminiRateLimiter of(int maxQps) {
        if (maxQps <= 0) {
            throw new GeminiException("GeminiRateLimiter maxQps <= 0");
        }
        return new GeminiRateLimiter(maxQps);
    }

    public GeminiRateLimiter(int maxQps) {
        this.max = maxQps;
    }

    public boolean tryAcquire(int msgType) {
        if (lastCheckTsMs <= -1) {
            // 首次，直接重置
            reset();
            cur++;
        } else {
            long now = SystemClock.nowNative();
            if (now - lastCheckTsMs >= TimeUnit.SECONDS.toMillis(1)) {
                // 大于1秒了，重置
                reset();
                cur++;
            } else {
                // 还在1秒内，需要判断max
                cur++;
                if (cur >= max) {
                    LOGGER.warn("msgType:{} over limit, {} {}", msgType, cur, max);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 重置计数器，和计数时间戳
     */
    private void reset() {
        lastCheckTsMs = SystemClock.nowNative();
        cur = 0;
    }
}

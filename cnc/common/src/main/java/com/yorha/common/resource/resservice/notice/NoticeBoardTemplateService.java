package com.yorha.common.resource.resservice.notice;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.gemini.utils.StringUtils;
import res.template.NoticeBoardTemplate;
import res.template.ServerLanguageTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 公告板
 *
 * <AUTHOR>
 */
public class NoticeBoardTemplateService extends AbstractResService {

    private final Map<Integer, Pair<Long, Long>> innerCityBuildMap = Maps.newHashMap();

    public NoticeBoardTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

        for (NoticeBoardTemplate template : getResHolder().getListFromMap(NoticeBoardTemplate.class)) {
            innerCityBuildMap.put(
                    template.getId(), new Pair<>(
                            TimeUtils.string2TimeStampMs(template.getStartTime()),
                            TimeUtils.string2TimeStampMs(template.getOffTime()))
            );
        }
    }


    @Override
    public void checkValid() throws ResourceException {
        for (NoticeBoardTemplate template : getResHolder().getListFromMap(NoticeBoardTemplate.class)) {
            int title = template.getTitle();
            ServerLanguageTemplate titleTemplate = getResHolder().findValueFromMap(ServerLanguageTemplate.class, title);
            if (titleTemplate == null) {
                throw new ResourceException(StringUtils.format("公告表标题无服务器多语言配置. noticeBoardTitleId={}", title));
            }
            int tab = template.getTab();
            ServerLanguageTemplate tabTemplate = getResHolder().findValueFromMap(ServerLanguageTemplate.class, tab);
            if (tabTemplate == null) {
                throw new ResourceException(StringUtils.format("公告表标题无服务器多语言配置. noticeBoardTabId={}", tab));
            }
            int des = template.getDes();
            ServerLanguageTemplate desTemplate = getResHolder().findValueFromMap(ServerLanguageTemplate.class, tab);
            if (desTemplate == null) {
                throw new ResourceException(StringUtils.format("公告表标题无服务器多语言配置. noticeBoardDesId={}", des));
            }

            if (StringUtils.isNotEmpty(template.getStartTime())) {
                checkTimeFormation(template.getStartTime());
            }
            if (StringUtils.isNotEmpty(template.getOffTime())) {
                checkTimeFormation(template.getOffTime());
            }
        }
    }

    private void checkTimeFormation(String template) throws ResourceException {
        try {
            TimeUtils.string2TimeStampMs(template);
        } catch (Exception e) {
            throw new ResourceException(StringUtils.format("公告时间格式异常. template:{}", template), e);
        }
    }

    public List<NoticeBoardTemplate> getTodayNotice() throws GeminiException {
        long now = SystemClock.now();
        ArrayList<NoticeBoardTemplate> result = Lists.newArrayList();
        for (Map.Entry<Integer, Pair<Long, Long>> entry : innerCityBuildMap.entrySet()) {
            Pair<Long, Long> key = entry.getValue();
            if (key.getFirst() <= now && now <= key.getSecond()) {
                result.add(getResHolder().getValueFromMap(NoticeBoardTemplate.class, entry.getKey()));
            }
        }
        return result;
    }
}
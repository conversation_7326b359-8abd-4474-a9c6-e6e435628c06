package com.yorha.common.resource.resservice.model;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.AssemblyModelGradientTemplate;
import res.template.SoldierModelGradientTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 士兵阵型模型圈大小配置
 *
 * <AUTHOR>
 * 2021年10月14日 17:10:00
 */
public class ModelGradientTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(ModelGradientTemplateService.class);

    private final Map<String, Integer> soldierTypeNumMap = new HashMap<>();
    private final Map<String, Integer> rallySoldierTypeNumMap = new HashMap<>();

    public ModelGradientTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getMap(SoldierModelGradientTemplate.class).forEach((key, typeConfig) ->
                typeConfig.getTypePairList().forEach(pair -> soldierTypeNumMap.put(formatId(key, pair.getKey()), pair.getValue())));
        getResHolder().getMap(AssemblyModelGradientTemplate.class).forEach((key, typeConfig) ->
                typeConfig.getTypePairList().forEach(pair -> rallySoldierTypeNumMap.put(formatId(key, pair.getKey()), pair.getValue())));
    }

    @Override
    public void checkValid() throws ResourceException {
        SoldierModelGradientTemplate flag = null;
        for (SoldierModelGradientTemplate entry : getResHolder().getMap(SoldierModelGradientTemplate.class).values()) {
            if (flag != null && flag.getArmyGradientPair().getValue() >= entry.getArmyGradientPair().getKey()) {
                LOGGER.error("配置错误: SoldierModelGradientTemplate表数据非升序梯度 error id:{}:{} >= id:{}:{}",
                        flag.getId(), flag.getArmyGradientPair().getValue(), entry.getId(), entry.getArmyGradientPair().getValue());
            }
            flag = entry;
        }
    }

    /**
     * 获取模型配置大小
     *
     * @param num     士兵数量
     * @param typeNum 士兵类型数量
     */
    public int getNormalModelBase(int num, int typeNum) {
        if (num <= 0) {
            return 0;
        }
        for (SoldierModelGradientTemplate template : getResHolder().getMap(SoldierModelGradientTemplate.class).values()) {
            if (num >= template.getArmyGradientPair().getKey() && num <= template.getArmyGradientPair().getValue()) {
                String key = formatId(template.getId(), typeNum);
                Integer modelBase = soldierTypeNumMap.getOrDefault(key, 0);
                if (modelBase == 0) {
                    LOGGER.error("getModelBase SoldierModelGradientTemplate error. num:{} typeNum:{}", num, typeNum);
                }
                return modelBase;
            }
        }
        LOGGER.error("getModelBase SoldierModelGradientTemplate error. num:{} typeNum:{}", num, typeNum);
        return 0;
    }

    /**
     * 获取集结模型配置大小
     *
     * @param num     士兵数量
     * @param typeNum 士兵类型数量
     */
    public int getRallyModelBase(int num, int typeNum) {
        if (num <= 0) {
            return 0;
        }
        for (AssemblyModelGradientTemplate template : getResHolder().getMap(AssemblyModelGradientTemplate.class).values()) {
            if (num >= template.getArmyGradientPair().getKey() && num <= template.getArmyGradientPair().getValue()) {
                String key = formatId(template.getId(), typeNum);
                Integer modelBase = rallySoldierTypeNumMap.getOrDefault(key, 0);
                if (modelBase == 0) {
                    LOGGER.error("getModelBase AssemblyModelGradientTemplate base is 0. num:{} typeNum:{}", num, typeNum);
                }
                return modelBase;
            }
        }
        LOGGER.error("getModelBase AssemblyModelGradientTemplate not find. num:{} typeNum:{}", num, typeNum);
        return 0;
    }

    private String formatId(int id, int typeNum) {
        return id + "_" + typeNum;
    }

    @Override
    public String getResName() {
        return "modelGradient";
    }
}
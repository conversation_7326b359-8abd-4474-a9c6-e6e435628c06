package com.yorha.common.actor.node;

import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.actor.msg.ActorRunnable;
import com.yorha.common.actorservice.ActorRole;
import com.yorha.common.etcd.IWatchHandler;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.DriveTrafficUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.config.Configurator;
import org.jetbrains.annotations.NotNull;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * cluster的配置更新Handler。
 *
 * <AUTHOR>
 */
public class ClusterConfigHandler implements IWatchHandler {
    private static final Logger LOGGER = LogManager.getLogger(ClusterConfigHandler.class);

    @Override
    public void onDelete(@NotNull String key) {
        LOGGER.info("gemini_system ClusterConfigHandler delete key={}", key);
    }

    @Override
    public void onUpdate(@NotNull String key, @NotNull String value) {
        LOGGER.info("gemini_system ClusterConfigHandler update key={}", key);
        // 更新集群配置
        if (!key.equals(ActorClusterUrlUtils.etcdClusterConfigUrl())) {
            throw new GeminiException("ClusterConfigHandler update key={}", key);
        }
        ClusterConfigUtils.refreshCluster("refresh_cluster_config", value);
        this.updateDriveTrafficUtils();
        this.updateLogLevel();
        // 通知监听了集群配置变化的actor
        notifyClusterConfigReload();
    }

    private void updateDriveTrafficUtils() {
        DriveTrafficUtils.refreshEtcdData();
    }

    private void updateLogLevel() {
        String logLevel = ServerContext.getLogLevel();
        if (logLevel.isEmpty()) {
            return;
        }
        try {
            org.apache.logging.log4j.Level oldLv = LogManager.getRootLogger().getLevel();
            org.apache.logging.log4j.Level newLv = org.apache.logging.log4j.Level.getLevel(logLevel.toUpperCase());

            Configurator.setRootLevel(newLv);
            LOGGER.warn("log level is set to {}, default is {}", newLv.toString(), oldLv.toString());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("update log level failed, log level is {}", logLevel, e);
        }
    }

    private void notifyClusterConfigReload() {
        final var msg = new ActorRunnable<>("handleClusterConfigReload", IClusterConfigWatcherActor::handleClusterConfigReload);
        Set<String> set = Stream.of(ActorRole.PushNotification.name()).collect(Collectors.toSet());
        // 起服preStart阶段会监听etcd触发一次更新，此时ActorSystem还未初始化，应跳过
        if (ServerContext.getActorSystem() == null || ServerContext.getActorSystem().getRegistryValue() == null) {
            return;
        }
        ServerContext.getActorSystem().getRegistryValue().broadcastLocal(set, msg);
    }
}


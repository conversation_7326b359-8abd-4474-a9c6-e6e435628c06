package com.yorha.common.db;

import com.google.protobuf.Message;
import com.yorha.common.db.mongo.MongoClientConfig;
import com.yorha.common.db.mongo.MongoOperationFactory;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.option.*;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.dbactor.DbMgrService;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;

/**
 * 封装db侧的能力，屏蔽tcaplus或者mongo的SDK
 * 提供同步和异步接口
 * 同步依赖调用方在协程环境
 */
public class DbManager {
    private static final Logger LOGGER = LogManager.getLogger(DbManager.class);

    private IDbOperationFactory operationFactory;

    private final LongAdder request = new LongAdder();
    private final LongAdder response = new LongAdder();

    public static DbManager getInstance() {
        return DbManager.InstanceHolder.INSTANCE;
    }

    private static class InstanceHolder {
        private static final DbManager INSTANCE = new DbManager();
    }

    private DbManager() {
    }

    /**
     * 根据ServerContext初始化(依赖etcd配置)
     */
    public void initFromServerContext() {
        if (this.operationFactory != null) {
            throw new GeminiException("initFromServerContext repeat");
        }
        /*
        if (ServerContext.getUseMongo()) {
            this.operationFactory = new MongoOperationFactory();
        } else {
            this.operationFactory = new TcaplusOperationFactory();
        }
        */
        // 现在只使用mongo

        String mongoDatabase = ClusterConfigUtils.getWorldConfig().getStringItem("mongo_database") + ServerContext.getWorldIdStr();
        String user = ClusterConfigUtils.getWorldConfig().getStringItem("mongo_user");
        String password = ClusterConfigUtils.getWorldConfig().getStringItem("mongo_password");
        final List<String> addressList = ClusterConfigUtils.getWorldConfig().getListStrItemMayEmpty("mongos_address_list");
        MongoClientConfig mongoClientConfig = new MongoClientConfig();
        mongoClientConfig.setUser(user);
        mongoClientConfig.setPassword(password);
        mongoClientConfig.setDatabase(mongoDatabase);
        mongoClientConfig.setAddressList(addressList);



        this.operationFactory = new MongoOperationFactory(mongoClientConfig);
        this.operationFactory.checkTable();
    }

    /**
     * 关闭
     */
    public void destroy() {
        LOGGER.info("DbManager destroy start");
        if (this.operationFactory != null) {
            final long startTimeMs = SystemClock.nowNative();
            while (this.request.longValue() != this.response.longValue()) {
                try {
                    Thread.sleep(1_000);
                } catch (InterruptedException ignored) {
                }
                if ((SystemClock.nowNative() - startTimeMs) > DbUtil.DB_RETRY_TIMEOUT_MS) {
                    LOGGER.error("DbManager destroy wait request timeout, request={}, response={}", this.request.longValue(), this.response.longValue());
                    break;
                }
            }
            operationFactory.close();
        }
        LOGGER.info("DbManager destroy end");
    }

    /**
     * 向db请求时调用
     */
    public void onRequest(IDbOperation<?, ?, ?> operation) {
        this.request.increment();
        DbMgrService.getInstance().getDbPerfLogger().logReq(operation.getRequestId(), operation.getTableName(), operation.getClass().getSimpleName(), operation.getRequestBytesSize());
    }

    /**
     * 收到db响应时调用
     */
    public void onResponse(IDbOperation<?, ?, ?> operation, final long costMs, final int dbErrorCode) {
        this.response.increment();
        DbMgrService.getInstance().getDbPerfLogger().logResp(operation.getRequestId(), operation.getTableName(), operation.getClass().getSimpleName(), operation.getResponseBytesSize(), costMs, dbErrorCode);
    }

    /**
     * [同步]插入一条新数据
     *
     * @param req    请求
     * @param option 插入选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> InsertResult<T> insert(T req, InsertOption option) {
        return this.operationFactory.insertOperation(req, option).run();
    }

    /**
     * [异步]插入一条新数据
     *
     * @param req    请求
     * @param option 插入选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void insertAsync(T req, InsertOption option, Consumer<InsertResult<T>> cb) {
        this.operationFactory.insertOperation(req, option).runAsync(cb);
    }

    /**
     * [同步]更新指定Key的数据
     *
     * @param req    请求
     * @param option 更新选项
     * @param <T>    DB表
     * @return 消息Builder
     */
    public <T extends Message.Builder> UpdateResult<T> update(T req, UpdateOption option) {
        return this.operationFactory.updateOperation(req, option).run();
    }

    /**
     * [异步]更新指定Key的数据
     *
     * @param req    请求
     * @param option 更新选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void updateAsync(T req, UpdateOption option, Consumer<UpdateResult<T>> cb) {
        this.operationFactory.updateOperation(req, option).runAsync(cb);
    }

    /**
     * [同步]读取指定Key的单条数据
     *
     * @param req    请求
     * @param option 读取选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> GetResult<T> get(T req, GetOption option) {
        return this.operationFactory.getOperation(req, option).run();
    }

    /**
     * [异步]读取指定Key的单条数据
     *
     * @param req    请求
     * @param option 读取选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void getAsync(T req, GetOption option, Consumer<GetResult<T>> cb) {
        this.operationFactory.getOperation(req, option).runAsync(cb);
    }

    /**
     * [同步]删除指定Key的单条数据
     *
     * @param req    请求
     * @param option 删除选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> DeleteResult delete(T req, DeleteOption option) {
        return this.operationFactory.deleteOperation(req, option).run();
    }

    /**
     * [异步]删除指定Key的单条数据
     *
     * @param req    请求
     * @param option 删除选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void deleteAsync(T req, DeleteOption option, Consumer<DeleteResult> cb) {
        this.operationFactory.deleteOperation(req, option).runAsync(cb);
    }

    /**
     * [同步]删除匹配指定PartKey的多条数据
     *
     * @param req    请求
     * @param option 删除选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> DeleteByPartKeyResult deleteByPartKey(T req, DeleteByPartKeyOption option) {
        return this.operationFactory.deleteByPartKeyOperation(req, option).run();
    }

    /**
     * [异步]删除匹配指定PartKey的多条数据
     *
     * @param req    请求
     * @param option 删除选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void deleteByPartKeyAsync(T req, DeleteByPartKeyOption option, Consumer<DeleteByPartKeyResult> cb) {
        this.operationFactory.deleteByPartKeyOperation(req, option).runAsync(cb);
    }

    /**
     * [同步]批量读取多个Key的数据
     *
     * @param reqList 请求列表
     * @param option  查询选项
     * @param <T>     DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> BatchGetResult<T> batchGet(List<T> reqList, BatchGetOption option) {
        return this.operationFactory.batchGetOperation(reqList, option).run();
    }

    /**
     * [异步]批量读取多个Key的数据
     *
     * @param reqList 请求列表
     * @param option  查询选项
     * @param cb      回调函数
     * @param <T>     DB表
     */
    public <T extends Message.Builder> void batchGetAsync(List<T> reqList, BatchGetOption option, Consumer<BatchGetResult<T>> cb) {
        this.operationFactory.batchGetOperation(reqList, option).runAsync(cb);
    }

    /**
     * [同步]查询符合PartKey的多条数据
     *
     * @param req    请求
     * @param option 查询选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> GetByPartKeyResult<T> getByPartKey(T req, GetByPartKeyOption option) {
        return this.operationFactory.getByPartKeyOperation(req, option).run();
    }

    /**
     * [异步]查询符合PartKey的多条数据
     *
     * @param req    请求
     * @param option 查询选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void getByPartKeyAsync(T req, GetByPartKeyOption option, Consumer<GetByPartKeyResult<T>> cb) {
        this.operationFactory.getByPartKeyOperation(req, option).runAsync(cb);
    }

    /**
     * [同步]插入or更新
     *
     * @param req    请求
     * @param option 选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> UpsertResult<T> upsert(T req, UpsertOption option) {
        return this.operationFactory.upsertOperation(req, option).run();
    }

    /**
     * [异步]插入or更新
     *
     * @param req    请求
     * @param option 选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void upsertAsync(T req, UpsertOption option, Consumer<UpsertResult<T>> cb) {
        this.operationFactory.upsertOperation(req, option).runAsync(cb);
    }

    /**
     * [同步] 指定字段自增
     *
     * @param req    请求
     * @param option 自增选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> IncreaseResult<T> increase(T req, IncreaseOption option) {
        return this.operationFactory.increaseOperation(req, option).run();
    }

    /**
     * [异步] 指定字段自增
     *
     * @param req    请求
     * @param option 自增选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void increaseAsync(T req, IncreaseOption option, Consumer<IncreaseResult<T>> cb) {
        this.operationFactory.increaseOperation(req, option).runAsync(cb);
    }

    /**
     * [同步] 获得db表元数据
     *
     * @param req    请求
     * @param option 自增选项
     * @param <T>    DB表
     * @return 消息builder
     */
    public <T extends Message.Builder> GetMetaResult getMeta(T req, GetMetaOption option) {
        return this.operationFactory.getMetaOperation(req, option).run();
    }

    /**
     * [同步] 遍历db表数据
     *
     * @param req    请求
     * @param option 遍历选项
     * @param <T>
     * @return 消息builder
     */
    public <T extends Message.Builder> TraverseResult traverse(T req, TraverseOption<T> option) {
        return this.operationFactory.traverseOperation(req, option).run();
    }

    /**
     * [异步] 遍历db表数据
     *
     * @param req    请求
     * @param option 遍历选项
     * @param cb     回调函数
     * @param <T>    DB表
     */
    public <T extends Message.Builder> void traverseAsync(T req, TraverseOption<T> option, Consumer<TraverseResult> cb) {
        this.operationFactory.traverseOperation(req, option).runAsync(cb);
    }
}

package com.yorha.common.aoiView.aoigrid;

import com.yorha.common.aoiView.manager.AoiObserver;
import com.yorha.common.constant.BigSceneConstants;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
public abstract class AoiGrid {
    private static final Logger LOGGER = LogManager.getLogger(AoiGrid.class);
    private final int index;

    /**
     * 观察者  层级-> 观察者id列表
     */
    protected final Map<Integer, ObjectOpenHashSet<AoiObserver>> layer2Observers = new Int2ObjectOpenHashMap<>();


    public AoiGrid(int x, int y) {
        this.index = getAoiIndex(x, y);
    }

    public static int getAoiIndex(int x, int y) {
        return x * 10000 + y;
    }

    //**************************************观察者部分*************************************

    /**
     * 加入视野观察者
     */
    public void addObserver(AoiObserver observer, int layer) {
        layer2Observers.computeIfAbsent(layer, k -> new ObjectOpenHashSet<>()).add(observer);
    }

    /**
     * 移除视野观察者
     */
    public void removeObserver(AoiObserver observer, int layer) {
        if (layer2Observers.containsKey(layer)) {
            layer2Observers.get(layer).remove(observer);
        } else {
            LOGGER.error("removeObserver layer not add");
        }
    }

    /**
     * 观察者层级变化
     */
    public void changeObserverLayer(AoiObserver observer, int oldLayer, int newLayer) {
        if (layer2Observers.containsKey(oldLayer)) {
            layer2Observers.get(oldLayer).remove(observer);
        } else {
            LOGGER.error("changeObserverLayer oldLayer not add");
        }
        layer2Observers.computeIfAbsent(newLayer, k -> new ObjectOpenHashSet<>()).add(observer);
    }

    public Set<AoiObserver> getObserverIds() {
        Set<AoiObserver> ret = new ObjectOpenHashSet<>();
        for (ObjectOpenHashSet<AoiObserver> value : layer2Observers.values()) {
            ret.addAll(value);
        }
        return ret;
    }

    public Set<AoiObserver> getObserverIds(int layer) {
        Set<AoiObserver> ret = new ObjectOpenHashSet<>();
        for (int i = layer; i > 0; i--) {
            if (!layer2Observers.containsKey(i)) {
                continue;
            }
            ret.addAll(layer2Observers.get(i));
        }
        return ret;
    }

    /**
     * 获取layer层的物体能被看到的观察者们  normalLayer+1 到 layer
     */
    public Set<AoiObserver> getBriefObserverIds(int layer) {
        Set<AoiObserver> ret = new ObjectOpenHashSet<>();
        for (int i = layer; i > BigSceneConstants.WORLD_NORMAL_MAX_LAYER; i--) {
            if (!layer2Observers.containsKey(i)) {
                continue;
            }
            ret.addAll(layer2Observers.get(i));
        }
        return ret;
    }

    //***********************************obj部分*************************************

    public abstract void addSceneObj(long objId, int layer);

    public abstract void removeSceneObj(long objId, int layer);

    public void changeSceneObjLayer(long objId, int oldLayer, int newLayer) {

    }

    public abstract Set<Long> getSceneObjIds();

    /**
     * 获取layer层能看到的物体们  layer 到 maxLayer
     */
    public abstract Set<Long> getSceneObjIds(int layer);

    @Override
    public int hashCode() {
        return Objects.hash(index);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        AoiGrid aoiGrid = (AoiGrid) obj;
        return index == aoiGrid.index;
    }

    public int getIndex() {
        return index;
    }
}

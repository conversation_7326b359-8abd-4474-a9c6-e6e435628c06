package com.yorha.common.utils;

import com.google.common.collect.Sets;
import com.yorha.common.utils.jol.JolParser;
import com.yorha.common.utils.jol.TreeLayout;
import org.openjdk.jol.info.GraphLayout;

import java.util.Set;

/**
 * java对象布局JOL(java object layout)
 * 工具类
 */
public class JolUtils {

    /**
     * 计算对象内存大小
     *
     * @param obj          目标内存
     * @param excludeClazz 不计入统计类型对象
     */
    public static long getObjMSize(Object obj, Set<Class<?>> excludeClazz) {
        GraphLayout graphLayout = getGraphLayout(obj, excludeClazz);
        return graphLayout.totalSize() / 1024;
    }

    /**
     * 获取对象详细描述
     *
     * @param obj          目标内存
     * @param excludeClazz 不计入统计类型对象
     * @return StringBuilder: tree型文本描述
     */
    public static String getObjMTreeInfo(Object obj, Set<Class<?>> excludeClazz, int layer, int maxNodeInLayer) {
        GraphLayout graphLayout = getGraphLayout(obj, excludeClazz);
        StringBuilder display = JolParser.toDisplay(TreeLayout.parse(graphLayout), layer, maxNodeInLayer).newStringBuilder();
        return display.toString();
    }

    /**
     * 内部接口，理论上不暴露（考虑到外围的综合使用，也暴露）
     */
    public static GraphLayout getGraphLayout(Object obj, Set<Class<?>> excludeClazz) {
        if (excludeClazz == null) {
            excludeClazz = Sets.newHashSet();
        }
        JolParser jolParser = new JolParser(excludeClazz);
        //TODO  JOL分析player内存时，最多分析到10m就退出，避免过多cpu消耗 @Author: jiangguilong
        return jolParser.parse(obj);
    }
}

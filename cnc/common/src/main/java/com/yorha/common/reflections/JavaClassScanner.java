package com.yorha.common.reflections;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.net.JarURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * 参考：https://blog.csdn.net/meifannao789456/article/details/118800482
 *
 * <AUTHOR>
 */
public class JavaClassScanner {
    private static final Logger LOGGER = LogManager.getLogger(JavaClassScanner.class);

    /**
     * 从包package中获取所有的Class
     *
     * @param packageName 包名
     * @return 扫描到的class。
     */
    public Set<Class<?>> scanPackage(String packageName) {
        Set<Class<?>> classes = new HashSet<>();
        this.doScanPackage(packageName, classes);
        return classes;
    }

    public void doScanPackage(String packageName, Set<Class<?>> classes) {
        // 是否循环迭代
        boolean recursive = true;
        // 获取包的名字 并进行替换
        String packageDirName = packageName.replace('.', '/');
        // 定义一个枚举的集合 并进行循环来处理这个目录下的things
        Enumeration<URL> dirs;
        try {
            dirs = Thread.currentThread().getContextClassLoader().getResources(packageDirName);
            // 循环迭代下去
            while (dirs.hasMoreElements()) {
                // 获取下一个元素
                URL url = dirs.nextElement();
                // 得到协议的名称
                String protocol = url.getProtocol();
                // 如果是以文件的形式保存在服务器上
                if ("file".equals(protocol)) {
                    // 获取包的物理路径
                    String filePath = URLDecoder.decode(url.getFile(), "UTF-8");
                    // 以文件的方式扫描整个包下的文件 并添加到集合中
                    try {
                        addClass(classes, filePath, packageName);
                        final List<String> subPackageNames = this.findDirs(filePath, packageName);
                        for (final String subPackageName : subPackageNames) {
                            this.doScanPackage(subPackageName, classes);
                        }
                    } catch (ClassNotFoundException e) {
                        LOGGER.error("getClasses {} fail!", filePath, e);
                    }
                } else if ("jar".equals(protocol)) {
                    // 如果是jar包文件
                    // 定义一个JarFile
                    JarFile jar;
                    try {
                        // 获取jar
                        jar = ((JarURLConnection) url.openConnection()).getJarFile();
                        // 从此jar包 得到一个枚举类
                        Enumeration<JarEntry> entries = jar.entries();
                        // 同样的进行循环迭代
                        while (entries.hasMoreElements()) {
                            // 获取jar里的一个实体 可以是目录 和一些jar包里的其他文件 如META-INF等文件
                            JarEntry entry = entries.nextElement();
                            String name = entry.getName();
                            // 如果是以/开头的
                            if (name.charAt(0) == '/') {
                                // 获取后面的字符串
                                name = name.substring(1);
                            }
                            // 如果前半部分和定义的包名相同
                            if (name.startsWith(packageDirName)) {
                                int idx = name.lastIndexOf('/');
                                // 如果以"/"结尾 是一个包
                                if (idx != -1) {
                                    // 获取包名 把"/"替换成"."
                                    packageName = name.substring(0, idx).replace('/', '.');
                                }
                                // 如果可以迭代下去 并且是一个包
                                if ((idx != -1) || recursive) {
                                    // 如果是一个.class文件 而且不是目录
                                    if (name.endsWith(".class") && !entry.isDirectory()) {
                                        // 去掉后面的".class" 获取真正的类名
                                        String className = name.substring(packageName.length() + 1, name.length() - 6);
                                        try {
                                            // 添加到classes
                                            classes.add(Class.forName(packageName + '.' + className));
                                        } catch (ClassNotFoundException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                    } catch (IOException e) {
                        LOGGER.warn("getClasses {} fail!", url, e);
                    }
                }
            }
        } catch (IOException e) {
            LOGGER.warn("getClasses {} fail!", packageName, e);
        }
    }

    private void addClass(Set<Class<?>> classes, String filePath, String packageName) throws ClassNotFoundException {
        File[] files = new File(filePath).listFiles(file -> (file.isFile() && file.getName().endsWith(".class")));
        assert files != null;
        for (File file : files) {
            String fileName = file.getName();
            if (file.isFile()) {
                String classsName = fileName.substring(0, fileName.lastIndexOf("."));
                if (!packageName.isEmpty()) {
                    classsName = packageName + "." + classsName;
                }
                doAddClass(classes, classsName);
            }
        }
    }

    private List<String> findDirs(String filePath, String packageName) {
        File[] dirs = new File(filePath).listFiles(File::isDirectory);
        assert dirs != null;
        List<String> packageNames = new ArrayList<>(dirs.length);
        for (final File dir : dirs) {
            final String name = dir.getName();
            if (name.isEmpty()) {
                continue;
            }
            packageNames.add(packageName + "." + name);
        }
        return packageNames;
    }

    private void doAddClass(Set<Class<?>> classes, final String classsName) throws ClassNotFoundException {
        final Class<?> cls = this.getClass().getClassLoader().loadClass(classsName);
        classes.add(cls);
    }

    public <A extends Annotation> Set<Class<?>> getTypesAnnotatedWith(String packageName, Class<A> annotationClass) {
        //找用了annotationClass注解的类
        Set<Class<?>> controllers = new HashSet<>();
        Set<Class<?>> clsList = scanPackage(packageName);
        if (clsList != null && clsList.size() > 0) {
            for (Class<?> cls : clsList) {
                if (cls.getAnnotation(annotationClass) != null) {
                    controllers.add(cls);
                }
            }
        }
        return controllers;
    }

    public <T> Set<Class<? extends T>> getSubTypesOf(String packageName, Class<T> superClass) {
        //找用了annotationClass注解的类
        Set<Class<? extends T>> controllers = new HashSet<>();
        Set<Class<?>> res = scanPackage(packageName);
        if (res != null && res.size() > 0) {
            for (Class<?> cls : res) {
                if (superClass.isAssignableFrom(cls)) {
                    controllers.add((Class<? extends T>) cls);
                }
            }
        }
        return controllers;
    }
}

package com.yorha.common.freqLimitCaller;

import com.mongodb.annotations.NotThreadSafe;
import com.yorha.common.utils.time.SystemClock;

/**
 * 注意：出于性能考量，未使用滑动窗口，而是记录了首个未执行的请求发起时间，实际执行次数会低于滑动窗口实现
 * ex：设置5s内触发4次则在第4次请求时执行操作，若[1,5]s内触发了3次，第6s时会丢弃所有先前触发次数，[6,10]s内需触发4次才会执行action
 * 注：本类目前用于减少无用监控告警（三方服务网络波动业务不应告警，若长时间不可用则应告警），使用前需考虑场景能否接受极端情况下一直不执行的可能
 * 注注：并发不安全
 *
 * <AUTHOR>
 */
@NotThreadSafe
public final class ActionLimiter {
    /**
     * 请求执行但未执行的action数量
     */
    private int waitingActionCnt = 0;
    /**
     * 首个未执行action的请求时间
     */
    private long firstWaitingActionTsMs = 0;
    /**
     * 上次执行action
     */
    private long lastDoActionTsMs = 0L;

    public ActionLimiter() {

    }

    /**
     * 在actionGapMs内累计触发满threshold后才会执行action
     * 注：过了首次请求actionGapMs后，会清空waitingActionCnt
     * 注注：为了热更
     *
     * @param actionGapMs 与上次执行的间隔
     * @param threshold   触发阈值
     * @param action      要做的行为
     * @return 是否执行了action
     */
    public boolean tryAction(final long actionGapMs, final int threshold, Runnable action) {
        final long curTsMs = SystemClock.nowNative();
        // 这批action已经过期了，不会再触发
        if (curTsMs - this.firstWaitingActionTsMs > actionGapMs) {
            this.waitingActionCnt = 0;
            this.firstWaitingActionTsMs = curTsMs;
        }
        this.waitingActionCnt++;

        final boolean doAction;
        // 过了action保护期 && 短时间内有大量触发
        if (curTsMs - this.lastDoActionTsMs > actionGapMs && this.waitingActionCnt >= threshold) {
            action.run();
            this.lastDoActionTsMs = curTsMs;
            this.waitingActionCnt = 0;
            doAction = true;
        } else {
            doAction = false;
        }
        return doAction;
    }


}


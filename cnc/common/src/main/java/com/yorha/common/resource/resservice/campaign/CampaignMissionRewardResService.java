package com.yorha.common.resource.resservice.campaign;

import com.google.common.collect.Maps;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.CampaignMissionRewardTemplate;
import res.template.SelecteRewardTemplate;

import java.util.HashMap;

public class CampaignMissionRewardResService extends AbstractResService {

    /**
     * 战役关卡奖励 战役难度等级->奖励等级->template
     */
    private final HashMap<Integer, HashMap<Integer, HashMap<Integer, CampaignMissionRewardTemplate>>> missionRewardMap = new HashMap<>();

    public CampaignMissionRewardResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 刷新地图
        refreshCampaignMissionRewardTemplate();
    }

    @Override
    public void checkValid() throws ResourceException {
        for (CampaignMissionRewardTemplate template : getResHolder().getListFromMap(CampaignMissionRewardTemplate.class)) {
            for (IntPairType reward : template.getGuaranteedRewardPairList()) {
                if (CommonEnum.CurrencyType.forNumber(reward.getKey()) == null) {
                    throw new ResourceException(StringUtils.format("战役任务奖励配置错误（campaign_mission_reward）. id:{} 配置了不存在的CurrencyType:{}",
                            template.getId(), reward.getKey()));
                }
            }

            if (getResHolder().findValueFromMap(SelecteRewardTemplate.class, template.getDropPit()) == null) {
                throw new ResourceException(StringUtils.format("战役任务奖励配置错误（campaign_mission_reward）. id:{} 的配置 dropPid:{} 在 selecte_reward 表不存在",
                        template.getId(), template.getDropPit()));
            }
            if (template.getLootDropCount() > 0 && getResHolder().findValueFromMap(SelecteRewardTemplate.class, template.getLootGroupID()) == null) {
                throw new ResourceException(StringUtils.format("战役任务奖励配置错误（campaign_mission_reward）. id:{} 的配置 lootGroupID:{} 在 selecte_reward 表不存在",
                        template.getId(), template.getLootGroupID()));
            }
        }
    }

    private void refreshCampaignMissionRewardTemplate() throws ResourceException {
        for (CampaignMissionRewardTemplate template : getResHolder().getListFromMap(CampaignMissionRewardTemplate.class)) {
            HashMap<Integer, HashMap<Integer, CampaignMissionRewardTemplate>> typeLevelMap =
                    missionRewardMap.computeIfAbsent(template.getDifficultylevel(), (key) -> Maps.newHashMap());
            HashMap<Integer, CampaignMissionRewardTemplate> levelMap = typeLevelMap.computeIfAbsent(template.getTaskType(), (key) -> Maps.newHashMap());
            levelMap.put(template.getRewardLevel(), template);
        }
    }

    public CampaignMissionRewardTemplate getMissionReward(int campaignLevel, int rewardType, int rewardLevel) {
        HashMap<Integer, HashMap<Integer, CampaignMissionRewardTemplate>> typeLevelMap = missionRewardMap.get(campaignLevel);
        if (typeLevelMap == null) {
            throw new GeminiException(StringUtils.format("战役任务奖励配置错误（campaign_mission_reward. difficultylevel:{}, taskType:{}, rewardLevel:{} 不存在",
                    campaignLevel, rewardType, rewardLevel));
        }
        HashMap<Integer, CampaignMissionRewardTemplate> levelMap = typeLevelMap.get(rewardType);
        if (levelMap == null) {
            throw new GeminiException(StringUtils.format("战役任务奖励配置错误（campaign_mission_reward. difficultylevel:{}, taskType:{}, rewardLevel:{} 不存在",
                    campaignLevel, rewardType, rewardLevel));
        }
        CampaignMissionRewardTemplate template = levelMap.get(rewardLevel);
        if (template == null) {
            throw new GeminiException(StringUtils.format("战役任务奖励配置错误（campaign_mission_reward. difficultylevel:{}, taskType:{}, rewardLevel:{} 不存在",
                    campaignLevel, rewardType, rewardLevel));
        }
        return template;
    }

    // 随机是否提升奖励等级
    public boolean randomUpgradeReward(int campaignLevel, int rewardType, int rewardLevel) {
        HashMap<Integer, HashMap<Integer, CampaignMissionRewardTemplate>> typeLevelMap = missionRewardMap.get(campaignLevel);
        if (typeLevelMap == null) {
            return false;
        }
        HashMap<Integer, CampaignMissionRewardTemplate> levelMap = typeLevelMap.get(rewardType);
        if (levelMap == null) {
            return false;
        }
        CampaignMissionRewardTemplate template = levelMap.get(rewardLevel);
        if (template == null) {
            return false;
        }
        return RandomUtils.isSuccessByPercentage(template.getRankUPChance());
    }

    public int getMaxRewardLevel(int campaignLevel, int rewardType) {
        HashMap<Integer, HashMap<Integer, CampaignMissionRewardTemplate>> typeLevelMap = missionRewardMap.get(campaignLevel);
        if (typeLevelMap == null) {
            return 0;
        }
        HashMap<Integer, CampaignMissionRewardTemplate> levelMap = typeLevelMap.get(rewardType);
        if (levelMap == null) {
            return 0;
        }
        return levelMap.values().stream().map(CampaignMissionRewardTemplate::getRewardLevel).max((t1, t2) -> t1 - t2).orElse(0);
    }
}
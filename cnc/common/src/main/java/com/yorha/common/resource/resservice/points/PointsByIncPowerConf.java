package com.yorha.common.resource.resservice.points;

import com.yorha.common.constant.Constants;
import com.yorha.gemini.utils.StringUtils;
import res.template.ActivityPointsTemplate;

import javax.annotation.Nullable;
import java.util.HashSet;
import java.util.Set;

public class PointsByIncPowerConf extends PointsConf {

    @Nullable
    private Set<Integer> changeReason = null;
    public PointsByIncPowerConf(ActivityPointsTemplate template) {
        super(template);
        final String param2 = template.getParam2();
        if (StringUtils.isNotEmpty(param2)) {
            this.changeReason = new HashSet<>();
            for (String reason : param2.split(Constants.DOU_HAO)) {
                this.changeReason.add(Integer.parseInt(reason));
            }
        }
    }

    public boolean hasChangeReasonConfig() {
        return this.changeReason != null;
    }

    public boolean isChangeReasonMatched(@Nullable Integer eventReason) {
        if (eventReason == null) {
            return false;
        }
        if (this.changeReason == null) {
            return false;
        }
        return this.changeReason.contains(eventReason);
    }
}

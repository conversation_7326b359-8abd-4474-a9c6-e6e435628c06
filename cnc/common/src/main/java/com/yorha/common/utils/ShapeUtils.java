package com.yorha.common.utils;

import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.shape.Circle;
import com.yorha.common.utils.shape.Line;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.utils.shape.Shape;
import com.yorha.common.utils.shape.logic.ShapeLogic;
import com.yorha.common.utils.shape.logic.ShapeLogic.LogicType;
import com.yorha.common.utils.vector.Vector2f;
import org.apache.commons.lang3.tuple.ImmutablePair;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.trunc;

/**
 * 图形库对外接口类
 *
 * <AUTHOR>
 * 2021年11月02日 10:55:00
 */
public class ShapeUtils {

    /*********************public**********************/
    /**
     * 判断两个shape是否相交
     */
    public static boolean isContact(Shape param1, <PERSON>hape param2) {
        return ShapeLogic.logicResult(LogicType.CONTACT, param1, param2);
    }

    /**
     * 判断两个shape是否包含
     */
    public static boolean isContain(Shape param1, Shape param2) {
        return ShapeLogic.logicResult(LogicType.CONTAIN, param1, param2);
    }

    /**
     * 获取两点间角度
     */
    public static float getAngleByPoint(Point a, Point b) {
        double x = Math.abs(b.getX() - a.getX());
        double y = Math.abs(b.getY() - a.getY());
        double z = Math.sqrt(x * x + y * y);
        return Math.round((float) (Math.asin(y / z) / Math.PI * 180));
    }


    /**
     * 获取点到线段最近距离
     */
    public static double getPointRecentDistance(Point circlePoint, Line line) {
        Point srcPoint = line.getSrcPoint();
        Point endPoint = line.getEndPoint();
        long csx = circlePoint.getX() - srcPoint.getX();
        long csy = circlePoint.getY() - srcPoint.getY();
        long esx = endPoint.getX() - srcPoint.getX();
        long esy = endPoint.getY() - srcPoint.getY();
        long cex = circlePoint.getX() - endPoint.getX();
        long cey = circlePoint.getY() - endPoint.getY();
        double cross = esx * csx + esy * csy;
        if (cross <= 0) {
            return Math.sqrt((csx * csx) + (csy) * (csy));
        }

        double d = esx * esx + esy * esy;
        if (cross >= d) {
            return Math.sqrt(cex * cex + cey * cey);
        }

        double temp = cross / d;
        double px = srcPoint.getX() + esx * temp;
        double py = srcPoint.getY() + esy * temp;
        double cpx = circlePoint.getX() - px;
        double pcy = py - circlePoint.getY();
        return Math.sqrt(cpx * cpx + pcy * pcy);
    }

    /**
     * 计算线段与圆的两个交点   线段!!!
     */
    public static ImmutablePair<Point, Point> getShapeIntersection(Circle circle, Line line) {
        Vector2f v = Vector2f.getVectorFromPointToPoint(line.getSrcPoint(), line.getEndPoint());
        Vector2f e = Vector2f.getVectorFromPointToPoint(line.getSrcPoint(), circle.getCircleCenter());
        // 向量点乘 求向量投影长度
        float a = Vector2f.dotProduct(v, e) / line.getLength();
        float a2 = a * a;
        float e2 = e.getX() * e.getX() + e.getY() * e.getY();
        float r2 = circle.getR() * circle.getR();
        float f2 = r2 - e2 + a2;
        if (f2 < 0) {
            return ImmutablePair.nullPair();
        }
        float f = (float) Math.sqrt(f2);
        Point onePoint = line.getPointByDisToSrcPoint(a - f);
        Point otherPoint = line.getPointByDisToSrcPoint(a + f);
        return ImmutablePair.of(onePoint, otherPoint);
    }

    /**
     * 计算直线与圆的两个交点   直线!!!
     */
    public static ImmutablePair<Point, Point> getShapeIntersection2(Circle circle, Line line) {
        Vector2f v = Vector2f.getVectorFromPointToPoint(line.getSrcPoint(), line.getEndPoint());
        Vector2f e = Vector2f.getVectorFromPointToPoint(line.getSrcPoint(), circle.getCircleCenter());
        // 向量点乘 求向量投影长度
        float a = Vector2f.dotProduct(v, e) / line.getLength();
        float a2 = a * a;
        float e2 = e.getX() * e.getX() + e.getY() * e.getY();
        float r2 = circle.getR() * circle.getR();
        float f2 = r2 - e2 + a2;
        if (f2 < 0) {
            return ImmutablePair.nullPair();
        }
        float f = (float) Math.sqrt(f2);
        Point onePoint = line.getSrcPoint().getPointWithVectorAndDis(v, a - f);
        Point otherPoint = line.getSrcPoint().getPointWithVectorAndDis(v, a + f);
        return ImmutablePair.of(onePoint, otherPoint);
    }

    /**
     * 计算过线段的终点与线段垂直的直线与圆的两个交点
     */
    public static ImmutablePair<Point, Point> getShapeIntersection3(Circle circle, Line line) {
        Vector2f vOld = Vector2f.getVectorFromPointToPoint(line.getEndPoint(), line.getSrcPoint());
        Vector2f v = vOld.rotate(90);
        Vector2f e = Vector2f.getVectorFromPointToPoint(line.getEndPoint(), circle.getCircleCenter());
        // 向量点乘 求向量投影长度
        float a = Vector2f.dotProduct(v, e) / line.getLength();
        float a2 = a * a;
        float e2 = e.getX() * e.getX() + e.getY() * e.getY();
        float r2 = circle.getR() * circle.getR();
        float f2 = r2 - e2 + a2;
        if (f2 < 0) {
            return ImmutablePair.nullPair();
        }
        float f = (float) Math.sqrt(f2);
        Point onePoint = line.getEndPoint().getPointWithVectorAndDis(v, a - f);
        Point otherPoint = line.getEndPoint().getPointWithVectorAndDis(v, a + f);
        return ImmutablePair.of(onePoint, otherPoint);
    }

    /**
     * 判断角度是否在指定夹角内
     *
     * @param angle 判断角度
     */
    public static boolean includedAngle(double angle, double startAngle, double endAngle) {
        if (startAngle - endAngle == 0) {
            return false;
        }
        // 1、归一化，约束在+-360°内
        angle = normalizationAngle(angle);
        startAngle = normalizationAngle(startAngle);
        endAngle = normalizationAngle(endAngle);

        // 2、endAngle处理
        if (startAngle < endAngle) {
            return startAngle <= angle && angle <= endAngle;
        }
        return startAngle <= angle || angle <= endAngle;
    }

    /**
     * 归一化360°
     */
    private static double normalizationAngle(double angle) {
        angle = (angle % 360) + (angle - trunc(angle));
        if (angle > 0.0) {
            return angle;
        } else {
            return angle + 360.0;
        }
    }

    /**
     * 判断两个长方形是否相交
     *
     * @param rect1Start 第一个长方形的起点坐标
     * @param rect1Size  第一个长方形的尺寸(宽,高)
     * @param rect2Start 第二个长方形的起点坐标
     * @param rect2Size  第二个长方形的尺寸(宽,高)
     * @return 如果两个长方形相交返回true，否则返回false
     */
    public static boolean isRectanglesIntersect(IntPairType rect1Start, IntPairType rect1Size,
                                                IntPairType rect2Start, IntPairType rect2Size) {
        // 计算每个矩形的边界
        int rect1Left = rect1Start.getKey();
        int rect1Right = rect1Start.getKey() + rect1Size.getKey() - 1;
        int rect1Bottom = rect1Start.getValue();
        int rect1Top = rect1Start.getValue() + rect1Size.getValue() - 1;

        int rect2Left = rect2Start.getKey();
        int rect2Right = rect2Start.getKey() + rect2Size.getKey() - 1;
        int rect2Bottom = rect2Start.getValue();
        int rect2Top = rect2Start.getValue() + rect2Size.getValue() - 1;

        // 检查是否有重叠
        // 如果一个矩形在另一个矩形的左侧、右侧、下方或上方，则它们不相交
        return rect1Right >= rect2Left && rect1Left <= rect2Right &&
                rect1Top >= rect2Bottom && rect1Bottom <= rect2Top;
    }

}

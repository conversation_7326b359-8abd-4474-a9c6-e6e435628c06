package com.yorha.common.resource.resservice.spy;

import com.google.common.collect.Lists;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum;
import res.template.SpyTechTemplate;

import java.util.*;

/**
 * 侦查科技相关
 * zeo
 */
public class SpyTechService extends AbstractResService {
    Map<Integer, List<CommonEnum.SpyContentType>> cache = new HashMap<>();

    public SpyTechService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (SpyTechTemplate template : getResHolder().getListFromMap(SpyTechTemplate.class)) {
            for (Integer id : template.getSpyContentTypeList()) {
                cache.computeIfAbsent(template.getId(), key -> Lists.newArrayList()).add(CommonEnum.SpyContentType.forNumber(id));
            }
        }
    }

    public Set<CommonEnum.SpyContentType> getSpyContentList(List<Integer> techList) {
        Set<CommonEnum.SpyContentType> ret = new HashSet<>();
        if (techList.isEmpty()) {
            ret.addAll(cache.getOrDefault(1, new ArrayList<>()));
            return ret;
        }
        for (int techId : techList) {
            ret.addAll(cache.getOrDefault(techId, new ArrayList<>()));
        }
        return ret;
    }

    @Override
    public void checkValid() throws ResourceException {
        for (SpyTechTemplate template : getResHolder().getListFromMap(SpyTechTemplate.class)) {
            for (Integer id : template.getSpyContentTypeList()) {
                if (CommonEnum.SpyContentType.forNumber(id) != null) {
                    continue;
                }
                throw new ResourceException("SpyTechService 非法配置 template:{}", template.getId());
            }
        }
    }
}

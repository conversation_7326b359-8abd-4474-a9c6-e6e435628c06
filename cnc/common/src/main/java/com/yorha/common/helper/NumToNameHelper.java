package com.yorha.common.helper;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.List;

public class NumToNameHelper {
    private static final Logger LOGGER = LogManager.getLogger(NumToNameHelper.class);

    private static final int MAX_LEN = 15;
    private static final long A = 0x5DEECE66DL;
    private static final long B = 0xBL;
    private static final long M = (1L << 63) - 1;
    private static final List<Character> STRING_SEED_POOL = Arrays.asList(
            'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
            'x', 'c', 'v', 'b', 'n', 'm'
    );

    /**
     * 纯随机生成名字
     */
    public static String randomGenName(int num) {
        long transferNum = lcgFun(num);
        long value = transferNum;
        StringBuilder robotName = new StringBuilder();
        List<Character> stringSeedPool = STRING_SEED_POOL;
        int size = stringSeedPool.size();
        while (transferNum > 0 || robotName.length() < MAX_LEN) {
            long i = transferNum % size;
            robotName.append(stringSeedPool.get((int) i));
            transferNum = transferNum / size;
        }
        if (transferNum != 0) {
            LOGGER.info("NumToNameHelper randomGenName, reach len limit but transfer not 0, num={}, origin transferNum={}, robotName={}, now transferNum={}",
                    num, value, robotName, transferNum);
        }
        LOGGER.info("NumToNameHelper randomGenName, before padding,  num={}, origin transferNum={}, robotName={}", num, value, robotName);
        while (robotName.length() < MAX_LEN) {
            robotName.append(stringSeedPool.get(0));
        }
        LOGGER.info("NumToNameHelper randomGenName, after padding, num={}, origin transferNum={}, robotName={}", num, value, robotName);
        return robotName.toString();
    }

    /**
     * 线性同余算法
     */
    private static long lcgFun(int n) {
        return (A * n + B) % M;
    }

}

package com.yorha.common.resource.resservice.soldier;

import com.google.common.collect.Lists;
import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import res.template.SoldierRestraintTemplate;
import res.template.SoldierTypeTemplate;

import java.util.*;

/**
 * 士兵相关的配置数据
 *
 * <AUTHOR>
 */
public class SoldierResService extends AbstractResService {

    private final Map<String, Integer> restrainMap = new HashMap<>();
    private final Map<Integer, List<Integer>> soldierType2Id = new HashMap<>();
    private final Map<Integer, List<Integer>> soldierLevel2Id = new HashMap<>();

    public SoldierResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        Collection<SoldierRestraintTemplate> list = getResHolder().getListFromMap(SoldierRestraintTemplate.class);
        for (SoldierRestraintTemplate e : list) {
            restrainMap.put(e.getAttacker() + ":" + e.getDefencer(), e.getValue());
        }
        Collection<SoldierTypeTemplate> soldierList = getResHolder().getListFromMap(SoldierTypeTemplate.class);
        for (SoldierTypeTemplate template : soldierList) {
            List<Integer> idList = soldierType2Id.computeIfAbsent(template.getSoldierType(), (key) -> new ArrayList<>());
            idList.add(template.getId());
        }
        for (SoldierTypeTemplate template : soldierList) {
            List<Integer> idList = soldierLevel2Id.computeIfAbsent(template.getSoldierLevel(), (key) -> new ArrayList<>());
            idList.add(template.getId());
        }
    }

    /**
     * 兵种克制加成
     */
    public float getRestrainRatio(int attackType, int defenceType) {
        int ret = 100;
        Integer value = restrainMap.get(attackType + ":" + defenceType);
        if (value != null) {
            ret = value;
        }
        return ret / 100f;
    }

    @Override
    public void checkValid() throws ResourceException {
        // 枚举检查一下
        SoldierNumChangeReason.checkValid();
    }

    public SoldierTypeTemplate findSoldierTemplate(int soldierId) {
        return getResHolder().findValueFromMap(SoldierTypeTemplate.class, soldierId);
    }

    public List<Integer> allSoldierIdsPlayerCanGetForGm() {
        // 策划把新手分支的士兵id提交到dev分支了，导致原来的做法会返回200个兵种。所以只能写死了。策划不改！！
        return Lists.newArrayList(
                1001, 1002, 1003, 1004, 1005,
                2001, 2002, 2003, 2004, 2005,
                3001, 3002, 3003, 3004, 3005,
                4001, 4002, 4003, 4004, 4005);
//        return getResHolder().getListFromMap(SoldierTypeTemplate.class).stream()
//                .filter(t -> t.getSoldierBelong() == SoldierBelong.SB_PLAYER)
//                .mapToInt(SoldierTypeTemplate::getId)
//                .boxed()
//                .collect(Collectors.toList());
    }

    public List<Integer> getIdListBySoldierType(int soldierType) {
        return soldierType2Id.get(soldierType);
    }

    public List<Integer> getIdListBySoldierLevel(int soldierLevel) {
        return soldierLevel2Id.get(soldierLevel);
    }

}
